import{m as Ie,z as tr,p as ke,b2 as rr,b3 as nr,b4 as or,j as i,R as yt,Q as gt,b5 as Ae,X as ar,E as sr,b6 as lr,I as ir,aG as cr,d as Re,b7 as ur,n as Ye,o as dr,b8 as fr,a5 as pr,a4 as br,a3 as vr,b as ee,b9 as hr,t as $e,ba as we,bb as at,bc as mr,bd as yr,be as gr,g as $r,c as wr,a as xr,ar as Er,as as Sr,ao as Cr,ap as kr,aq as Pr,an as Tr,P as jr}from"./index-D8VBA_Ei.js";import{t as Nr,a as Mr}from"./chunk-ML27DD5T-DYtZtKiQ.js";import{r as $,R as G}from"./react-router-dom-Bk_r5m4S.js";import{V as ne}from"./react-hot-toast-RI2B8J99.js";import{a as Dr}from"./qq_manager-DT7Xa_Nu.js";import{$ as _r,m as st,u as Fr,b as Lr,c as Ir,l as Rr}from"./chunk-O4OUNAUT-SDb7E44K.js";import{$ as Hr,d as Or}from"./useListState-D50dcKc9.js";import{a as Ar,$ as Kr,d as Vr}from"./usePress-CHz9NoJl.js";import{k as Wr,$ as Ur,b as qr}from"./SelectionManager-C1K6j-bz.js";import{$ as zr,a as Br,s as Gr}from"./chunk-KVDW62ZT-Ck4XsWki.js";import{m as Zr}from"./index-lkyIzG0M.js";import{c as ue}from"./chunk-NAV3ZXLI-BWK57j2s.js";import{b as Jr,g as Qr}from"./index-BgMCe2b5.js";import{s as Yr,S as Xr}from"./switch_card-BVSFwzbh.js";import{G as en}from"./react-icons-MWc89PtZ.js";import{l as tn}from"./index-Ca-jOSo7.js";import{t as rn}from"./primitives-D092zWiZ.js";import{u as $t}from"./use-config-vL25w4Sl.js";import{i as nn}from"./chunk-2QAN2V2R-D3FHY7xI.js";import{u as on,C as an}from"./react-hook-form-DVPsaNoQ.js";import{u as sn}from"./use-dialog-DKWVyT14.js";import"./react-dom-DoC2WAmd.js";import"./monaco-editor-C8Mcix6f.js";import"./index-CT5KLc5E.js";import"./features-animation-BSAHv7_N.js";import"./chunk-SLABUSGS-O-e1xCFE.js";import"./chunk-EN4B57RQ-Bqlu8Ocb.js";import"./useToggle-vJd16WZs.js";var ln=Ie({base:["w-full","p-1","min-w-[200px]"]});Ie({slots:{base:["flex","group","gap-2","items-center","justify-between","relative","px-2","py-1.5","w-full","h-full","box-border","rounded-small","outline-none","cursor-pointer","tap-highlight-transparent","data-[pressed=true]:opacity-70",...tr,"data-[focus-visible=true]:dark:ring-offset-background-content1"],wrapper:"w-full flex flex-col items-start justify-center",title:"flex-1 text-small font-normal truncate",description:["w-full","text-tiny","text-foreground-500","group-hover:text-current"],selectedIcon:["text-inherit","w-3","h-3","flex-shrink-0"],shortcut:["px-1","py-0.5","rounded","font-sans","text-foreground-500","text-tiny","border-small","border-default-300","group-hover:border-current"]},variants:{variant:{solid:{base:""},bordered:{base:"border-medium border-transparent bg-transparent"},light:{base:"bg-transparent"},faded:{base:"border-small border-transparent hover:border-default data-[hover=true]:bg-default-100"},flat:{base:""},shadow:{base:"data-[hover=true]:shadow-lg"}},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},isDisabled:{true:{base:"opacity-disabled pointer-events-none"}},disableAnimation:{true:{},false:{}}},defaultVariants:{variant:"solid",color:"default"},compoundVariants:[{variant:"solid",color:"default",class:{base:"data-[hover=true]:bg-default data-[hover=true]:text-default-foreground"}},{variant:"solid",color:"primary",class:{base:"data-[hover=true]:bg-primary data-[hover=true]:text-primary-foreground"}},{variant:"solid",color:"secondary",class:{base:"data-[hover=true]:bg-secondary data-[hover=true]:text-secondary-foreground"}},{variant:"solid",color:"success",class:{base:"data-[hover=true]:bg-success data-[hover=true]:text-success-foreground"}},{variant:"solid",color:"warning",class:{base:"data-[hover=true]:bg-warning data-[hover=true]:text-warning-foreground"}},{variant:"solid",color:"danger",class:{base:"data-[hover=true]:bg-danger data-[hover=true]:text-danger-foreground"}},{variant:"shadow",color:"default",class:{base:"data-[hover=true]:shadow-default/50 data-[hover=true]:bg-default data-[hover=true]:text-default-foreground"}},{variant:"shadow",color:"primary",class:{base:"data-[hover=true]:shadow-primary/30 data-[hover=true]:bg-primary data-[hover=true]:text-primary-foreground"}},{variant:"shadow",color:"secondary",class:{base:"data-[hover=true]:shadow-secondary/30 data-[hover=true]:bg-secondary data-[hover=true]:text-secondary-foreground"}},{variant:"shadow",color:"success",class:{base:"data-[hover=true]:shadow-success/30 data-[hover=true]:bg-success data-[hover=true]:text-success-foreground"}},{variant:"shadow",color:"warning",class:{base:"data-[hover=true]:shadow-warning/30 data-[hover=true]:bg-warning data-[hover=true]:text-warning-foreground"}},{variant:"shadow",color:"danger",class:{base:"data-[hover=true]:shadow-danger/30 data-[hover=true]:bg-danger data-[hover=true]:text-danger-foreground"}},{variant:"bordered",color:"default",class:{base:"data-[hover=true]:border-default"}},{variant:"bordered",color:"primary",class:{base:"data-[hover=true]:border-primary data-[hover=true]:text-primary"}},{variant:"bordered",color:"secondary",class:{base:"data-[hover=true]:border-secondary data-[hover=true]:text-secondary"}},{variant:"bordered",color:"success",class:{base:"data-[hover=true]:border-success data-[hover=true]:text-success"}},{variant:"bordered",color:"warning",class:{base:"data-[hover=true]:border-warning data-[hover=true]:text-warning"}},{variant:"bordered",color:"danger",class:{base:"data-[hover=true]:border-danger data-[hover=true]:text-danger"}},{variant:"flat",color:"default",class:{base:"data-[hover=true]:bg-default/40 data-[hover=true]:text-default-foreground"}},{variant:"flat",color:"primary",class:{base:"data-[hover=true]:bg-primary/20 data-[hover=true]:text-primary"}},{variant:"flat",color:"secondary",class:{base:"data-[hover=true]:bg-secondary/20 data-[hover=true]:text-secondary"}},{variant:"flat",color:"success",class:{base:"data-[hover=true]:bg-success/20 data-[hover=true]:text-success "}},{variant:"flat",color:"warning",class:{base:"data-[hover=true]:bg-warning/20 data-[hover=true]:text-warning"}},{variant:"flat",color:"danger",class:{base:"data-[hover=true]:bg-danger/20 data-[hover=true]:text-danger"}},{variant:"faded",color:"default",class:{base:"data-[hover=true]:text-default-foreground"}},{variant:"faded",color:"primary",class:{base:"data-[hover=true]:text-primary"}},{variant:"faded",color:"secondary",class:{base:"data-[hover=true]:text-secondary"}},{variant:"faded",color:"success",class:{base:"data-[hover=true]:text-success"}},{variant:"faded",color:"warning",class:{base:"data-[hover=true]:text-warning"}},{variant:"faded",color:"danger",class:{base:"data-[hover=true]:text-danger"}},{variant:"light",color:"default",class:{base:"data-[hover=true]:text-default-500"}},{variant:"light",color:"primary",class:{base:"data-[hover=true]:text-primary"}},{variant:"light",color:"secondary",class:{base:"data-[hover=true]:text-secondary"}},{variant:"light",color:"success",class:{base:"data-[hover=true]:text-success"}},{variant:"light",color:"warning",class:{base:"data-[hover=true]:text-warning"}},{variant:"light",color:"danger",class:{base:"data-[hover=true]:text-danger"}}]});Ie({slots:{base:"relative mb-2",heading:"pl-1 text-tiny text-foreground-500",group:"data-[has-title=true]:pt-1",divider:"mt-2"}});Ie({base:"w-full flex flex-col gap-0.5 p-1"});function wt(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=wt(e[t]))&&(n&&(n+=" "),n+=r);else for(t in e)e[t]&&(n&&(n+=" "),n+=t);return n}function cn(...e){for(var t=0,r,n,o="";t<e.length;)(r=e[t++])&&(n=wt(r))&&(o&&(o+=" "),o+=n);return o}var un=(e,t)=>{if(!e&&!t)return{};const r=new Set([...Object.keys(e||{}),...Object.keys(t||{})]);return Array.from(r).reduce((n,o)=>({...n,[o]:cn(e==null?void 0:e[o],t==null?void 0:t[o])}),{})},xt=ke((e,t)=>{var r;const{as:n,className:o,children:a,...s}=e,l=n||"div",f=rr(t),{slots:u,classNames:d}=nr(),h=or(d==null?void 0:d.footer,o);return i.jsx(l,{ref:f,className:(r=u.footer)==null?void 0:r.call(u,{class:h}),...s,children:a})});xt.displayName="HeroUI.CardFooter";var dn=xt;const Et=new WeakMap;function fn(e,t,r){let{shouldFocusWrap:n=!0,onKeyDown:o,onKeyUp:a,...s}=e;!e["aria-label"]&&!e["aria-labelledby"]&&console.warn("An aria-label or aria-labelledby prop is required for accessibility.");let l=yt(e,{labelable:!0}),{listProps:f}=Hr({...s,ref:r,selectionManager:t.selectionManager,collection:t.collection,disabledKeys:t.disabledKeys,shouldFocusWrap:n,linkBehavior:"override"});return Et.set(t,{onClose:e.onClose,onAction:e.onAction}),{menuProps:gt(l,{onKeyDown:o,onKeyUp:a},{role:"menu",...f,onKeyDown:u=>{var d;u.key!=="Escape"&&((d=f.onKeyDown)===null||d===void 0||d.call(f,u))}})}}function pn(e,t,r){let{id:n,key:o,closeOnSelect:a,isVirtualized:s,"aria-haspopup":l,onPressStart:f,onPressUp:u,onPress:d,onPressChange:h,onPressEnd:b,onHoverStart:p,onHoverChange:g,onHoverEnd:x,onKeyDown:w,onKeyUp:C,onFocus:D,onFocusChange:k,onBlur:M,selectionManager:E=t.selectionManager}=e,_=!!l,O=_&&e["aria-expanded"]==="true";var S;let V=(S=e.isDisabled)!==null&&S!==void 0?S:E.isDisabled(o);var z;let I=(z=e.isSelected)!==null&&z!==void 0?z:E.isSelected(o),Q=Et.get(t),y=t.collection.getItem(o),m=e.onClose||Q.onClose,F=Ar(),N=q=>{var ge;if(!_){if(!(y==null||(ge=y.props)===null||ge===void 0)&&ge.onAction?y.props.onAction():e.onAction&&e.onAction(o),Q.onAction){let He=Q.onAction;He(o)}q.target instanceof HTMLAnchorElement&&y&&F.open(q.target,q,y.props.href,y.props.routerOptions)}},U="menuitem";_||(E.selectionMode==="single"?U="menuitemradio":E.selectionMode==="multiple"&&(U="menuitemcheckbox"));let P=Ae(),Y=Ae(),ie=Ae(),c={id:n,"aria-disabled":V||void 0,role:U,"aria-label":e["aria-label"],"aria-labelledby":P,"aria-describedby":[Y,ie].filter(Boolean).join(" ")||void 0,"aria-controls":e["aria-controls"],"aria-haspopup":l,"aria-expanded":e["aria-expanded"]};E.selectionMode!=="none"&&!_&&(c["aria-checked"]=I),s&&(c["aria-posinset"]=y==null?void 0:y.index,c["aria-setsize"]=_r(t.collection));let v=q=>{q.pointerType==="keyboard"&&N(q),f==null||f(q)},T=q=>{q.pointerType!=="keyboard"&&(N(q),!_&&m&&(a??(E.selectionMode!=="multiple"||E.isLink(o)))&&m()),u==null||u(q)},{itemProps:j,isFocused:L}=Wr({selectionManager:E,key:o,ref:r,shouldSelectOnPressUp:!0,allowsDifferentPressOrigin:!0,linkBehavior:"none"}),{pressProps:A,isPressed:W}=Kr({onPressStart:v,onPress:d,onPressUp:T,onPressChange:h,onPressEnd:b,isDisabled:V}),{hoverProps:K}=ar({isDisabled:V,onHoverStart(q){!sr()&&!(O&&l==="dialog")&&(E.setFocused(!0),E.setFocusedKey(o)),p==null||p(q)},onHoverChange:g,onHoverEnd:x}),{keyboardProps:R}=lr({onKeyDown:q=>{if(q.repeat){q.continuePropagation();return}switch(q.key){case" ":!V&&E.selectionMode==="none"&&!_&&a!==!1&&m&&m();break;case"Enter":!V&&a!==!1&&!_&&m&&m();break;default:_||q.continuePropagation(),w==null||w(q);break}},onKeyUp:C}),{focusProps:Z}=ir({onBlur:M,onFocus:D,onFocusChange:k}),ye=yt(y==null?void 0:y.props);delete ye.id;let ae=Vr(y==null?void 0:y.props);return{menuItemProps:{...c,...gt(ye,ae,_?{onFocus:j.onFocus,"data-key":j["data-key"]}:j,A,K,R,Z),tabIndex:j.tabIndex!=null&&O?-1:j.tabIndex},labelProps:{id:P},descriptionProps:{id:Y},keyboardShortcutProps:{id:ie},isFocused:L,isSelected:I,isPressed:W,isDisabled:V}}function bn(e){let{heading:t,"aria-label":r}=e,n=cr();return{itemProps:{role:"presentation"},headingProps:t?{id:n,role:"presentation"}:{},groupProps:{role:"group","aria-label":r,"aria-labelledby":t?n:void 0}}}function vn(e={}){const{strict:t=!0,errorMessage:r="useContext: `context` is undefined. Seems you forgot to wrap component within the Provider",name:n}=e,o=$.createContext(void 0);o.displayName=n;function a(){var s;const l=$.useContext(o);if(!l&&t){const f=new Error(r);throw f.name="ContextError",(s=Error.captureStackTrace)==null||s.call(Error,f,a),f}return l}return[o.Provider,a,o]}function hn(e){return typeof e=="function"}function St(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=St(e[t]))&&(n&&(n+=" "),n+=r);else for(t in e)e[t]&&(n&&(n+=" "),n+=t);return n}function mn(...e){for(var t=0,r,n,o="";t<e.length;)(r=e[t++])&&(n=St(r))&&(o&&(o+=" "),o+=n);return o}function yn(e,t){if(e!=null){if(hn(e)){e(t);return}try{e.current=t}catch{throw new Error(`Cannot assign value '${t}' to ref '${e}'`)}}}function gn(...e){return t=>{e.forEach(r=>yn(r,t))}}var[$n,Ct]=vn({name:"DropdownContext",errorMessage:"useDropdownContext: `context` is undefined. Seems you forgot to wrap all popover components within `<Dropdown />`"});const oe=typeof document<"u"?G.useLayoutEffect:()=>{};let lt=new Map;function wn(e,t){if(e===t)return e;let r=lt.get(e);if(r)return r.forEach(o=>o(t)),t;let n=lt.get(t);return n?(n.forEach(o=>o(e)),e):t}function xn(...e){return(...t)=>{for(let r of e)typeof r=="function"&&r(...t)}}const X=e=>{var t;return(t=e==null?void 0:e.ownerDocument)!==null&&t!==void 0?t:document},En=e=>e&&"window"in e&&e.window===e?e:X(e).defaultView||window;function Ke(...e){let t={...e[0]};for(let r=1;r<e.length;r++){let n=e[r];for(let o in n){let a=t[o],s=n[o];typeof a=="function"&&typeof s=="function"&&o[0]==="o"&&o[1]==="n"&&o.charCodeAt(2)>=65&&o.charCodeAt(2)<=90?t[o]=xn(a,s):(o==="className"||o==="UNSAFE_className")&&typeof a=="string"&&typeof s=="string"?t[o]=Re(a,s):o==="id"&&a&&s?t.id=wn(a,s):t[o]=s!==void 0?s:a}}return t}function it(e){if(Sn())e.focus({preventScroll:!0});else{let t=Cn(e);e.focus(),kn(t)}}let Me=null;function Sn(){if(Me==null){Me=!1;try{document.createElement("div").focus({get preventScroll(){return Me=!0,!0}})}catch{}}return Me}function Cn(e){let t=e.parentNode,r=[],n=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==n;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&r.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return n instanceof HTMLElement&&r.push({element:n,scrollTop:n.scrollTop,scrollLeft:n.scrollLeft}),r}function kn(e){for(let{element:t,scrollTop:r,scrollLeft:n}of e)t.scrollTop=r,t.scrollLeft=n}let me=new Map,Ue=new Set;function ct(){if(typeof window>"u")return;function e(n){return"propertyName"in n}let t=n=>{if(!e(n)||!n.target)return;let o=me.get(n.target);o||(o=new Set,me.set(n.target,o),n.target.addEventListener("transitioncancel",r,{once:!0})),o.add(n.propertyName)},r=n=>{if(!e(n)||!n.target)return;let o=me.get(n.target);if(o&&(o.delete(n.propertyName),o.size===0&&(n.target.removeEventListener("transitioncancel",r),me.delete(n.target)),me.size===0)){for(let a of Ue)a();Ue.clear()}};document.body.addEventListener("transitionrun",t),document.body.addEventListener("transitionend",r)}typeof document<"u"&&(document.readyState!=="loading"?ct():document.addEventListener("DOMContentLoaded",ct));function Pn(e){requestAnimationFrame(()=>{me.size===0?e():Ue.add(e)})}function Tn(e){const t=X(e);if(ur()==="virtual"){let r=t.activeElement;Pn(()=>{t.activeElement===r&&e.isConnected&&it(e)})}else it(e)}function jn(e){const t=En(e);if(!(e instanceof t.HTMLElement)&&!(e instanceof t.SVGElement))return!1;let{display:r,visibility:n}=e.style,o=r!=="none"&&n!=="hidden"&&n!=="collapse";if(o){const{getComputedStyle:a}=e.ownerDocument.defaultView;let{display:s,visibility:l}=a(e);o=s!=="none"&&l!=="hidden"&&l!=="collapse"}return o}function Nn(e,t){return!e.hasAttribute("hidden")&&!e.hasAttribute("data-react-aria-prevent-focus")&&(e.nodeName==="DETAILS"&&t&&t.nodeName!=="SUMMARY"?e.hasAttribute("open"):!0)}function kt(e,t){return e.nodeName!=="#comment"&&jn(e)&&Nn(e,t)&&(!e.parentElement||kt(e.parentElement,e))}const ut=G.createContext(null),qe="react-aria-focus-scope-restore";let H=null;function Mn(e){let{children:t,contain:r,restoreFocus:n,autoFocus:o}=e,a=$.useRef(null),s=$.useRef(null),l=$.useRef([]),{parentNode:f}=$.useContext(ut)||{},u=$.useMemo(()=>new Be({scopeRef:l}),[l]);oe(()=>{let b=f||B.root;if(B.getTreeNode(b.scopeRef)&&H&&!_e(H,b.scopeRef)){let p=B.getTreeNode(H);p&&(b=p)}b.addChild(u),B.addNode(u)},[u,f]),oe(()=>{let b=B.getTreeNode(l);b&&(b.contain=!!r)},[r]),oe(()=>{var b;let p=(b=a.current)===null||b===void 0?void 0:b.nextSibling,g=[],x=w=>w.stopPropagation();for(;p&&p!==s.current;)g.push(p),p.addEventListener(qe,x),p=p.nextSibling;return l.current=g,()=>{for(let w of g)w.removeEventListener(qe,x)}},[t]),Rn(l,n,r),Ln(l,r),Hn(l,n,r),In(l,o),$.useEffect(()=>{const b=X(l.current?l.current[0]:void 0).activeElement;let p=null;if(re(b,l.current)){for(let g of B.traverse())g.scopeRef&&re(b,g.scopeRef.current)&&(p=g);p===B.getTreeNode(l)&&(H=p.scopeRef)}},[l]),oe(()=>()=>{var b,p,g;let x=(g=(p=B.getTreeNode(l))===null||p===void 0||(b=p.parent)===null||b===void 0?void 0:b.scopeRef)!==null&&g!==void 0?g:null;(l===H||_e(l,H))&&(!x||B.getTreeNode(x))&&(H=x),B.removeTreeNode(l)},[l]);let d=$.useMemo(()=>Dn(l),[]),h=$.useMemo(()=>({focusManager:d,parentNode:u}),[u,d]);return G.createElement(ut.Provider,{value:h},G.createElement("span",{"data-focus-scope-start":!0,hidden:!0,ref:a}),t,G.createElement("span",{"data-focus-scope-end":!0,hidden:!0,ref:s}))}function Dn(e){return{focusNext(t={}){let r=e.current,{from:n,tabbable:o,wrap:a,accept:s}=t,l=n||X(r[0]).activeElement,f=r[0].previousElementSibling,u=fe(r),d=ce(u,{tabbable:o,accept:s},r);d.currentNode=re(l,r)?l:f;let h=d.nextNode();return!h&&a&&(d.currentNode=f,h=d.nextNode()),h&&le(h,!0),h},focusPrevious(t={}){let r=e.current,{from:n,tabbable:o,wrap:a,accept:s}=t,l=n||X(r[0]).activeElement,f=r[r.length-1].nextElementSibling,u=fe(r),d=ce(u,{tabbable:o,accept:s},r);d.currentNode=re(l,r)?l:f;let h=d.previousNode();return!h&&a&&(d.currentNode=f,h=d.previousNode()),h&&le(h,!0),h},focusFirst(t={}){let r=e.current,{tabbable:n,accept:o}=t,a=fe(r),s=ce(a,{tabbable:n,accept:o},r);s.currentNode=r[0].previousElementSibling;let l=s.nextNode();return l&&le(l,!0),l},focusLast(t={}){let r=e.current,{tabbable:n,accept:o}=t,a=fe(r),s=ce(a,{tabbable:n,accept:o},r);s.currentNode=r[r.length-1].nextElementSibling;let l=s.previousNode();return l&&le(l,!0),l}}}const Xe=["input:not([disabled]):not([type=hidden])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]","[contenteditable]"],_n=Xe.join(":not([hidden]),")+",[tabindex]:not([disabled]):not([hidden])";Xe.push('[tabindex]:not([tabindex="-1"]):not([disabled])');const Fn=Xe.join(':not([hidden]):not([tabindex="-1"]),');function fe(e){return e[0].parentElement}function xe(e){let t=B.getTreeNode(H);for(;t&&t.scopeRef!==e;){if(t.contain)return!1;t=t.parent}return!0}function Ln(e,t){let r=$.useRef(void 0),n=$.useRef(void 0);oe(()=>{let o=e.current;if(!t){n.current&&(cancelAnimationFrame(n.current),n.current=void 0);return}const a=X(o?o[0]:void 0);let s=u=>{if(u.key!=="Tab"||u.altKey||u.ctrlKey||u.metaKey||!xe(e)||u.isComposing)return;let d=a.activeElement,h=e.current;if(!h||!re(d,h))return;let b=fe(h),p=ce(b,{tabbable:!0},h);if(!d)return;p.currentNode=d;let g=u.shiftKey?p.previousNode():p.nextNode();g||(p.currentNode=u.shiftKey?h[h.length-1].nextElementSibling:h[0].previousElementSibling,g=u.shiftKey?p.previousNode():p.nextNode()),u.preventDefault(),g&&le(g,!0)},l=u=>{(!H||_e(H,e))&&re(u.target,e.current)?(H=e,r.current=u.target):xe(e)&&!pe(u.target,e)?r.current?r.current.focus():H&&H.current&&ze(H.current):xe(e)&&(r.current=u.target)},f=u=>{n.current&&cancelAnimationFrame(n.current),n.current=requestAnimationFrame(()=>{if(a.activeElement&&xe(e)&&!pe(a.activeElement,e))if(H=e,a.body.contains(u.target)){var d;r.current=u.target,(d=r.current)===null||d===void 0||d.focus()}else H.current&&ze(H.current)})};return a.addEventListener("keydown",s,!1),a.addEventListener("focusin",l,!1),o==null||o.forEach(u=>u.addEventListener("focusin",l,!1)),o==null||o.forEach(u=>u.addEventListener("focusout",f,!1)),()=>{a.removeEventListener("keydown",s,!1),a.removeEventListener("focusin",l,!1),o==null||o.forEach(u=>u.removeEventListener("focusin",l,!1)),o==null||o.forEach(u=>u.removeEventListener("focusout",f,!1))}},[e,t]),oe(()=>()=>{n.current&&cancelAnimationFrame(n.current)},[n])}function Pt(e){return pe(e)}function re(e,t){return!e||!t?!1:t.some(r=>r.contains(e))}function pe(e,t=null){if(e instanceof Element&&e.closest("[data-react-aria-top-layer]"))return!0;for(let{scopeRef:r}of B.traverse(B.getTreeNode(t)))if(r&&re(e,r.current))return!0;return!1}function _e(e,t){var r;let n=(r=B.getTreeNode(t))===null||r===void 0?void 0:r.parent;for(;n;){if(n.scopeRef===e)return!0;n=n.parent}return!1}function le(e,t=!1){if(e!=null&&!t)try{Tn(e)}catch{}else if(e!=null)try{e.focus()}catch{}}function Tt(e,t=!0){let r=e[0].previousElementSibling,n=fe(e),o=ce(n,{tabbable:t},e);o.currentNode=r;let a=o.nextNode();return t&&!a&&(n=fe(e),o=ce(n,{tabbable:!1},e),o.currentNode=r,a=o.nextNode()),a}function ze(e,t=!0){le(Tt(e,t))}function In(e,t){const r=G.useRef(t);$.useEffect(()=>{if(r.current){H=e;const n=X(e.current?e.current[0]:void 0);!re(n.activeElement,H.current)&&e.current&&ze(e.current)}r.current=!1},[e])}function Rn(e,t,r){oe(()=>{if(t||r)return;let n=e.current;const o=X(n?n[0]:void 0);let a=s=>{let l=s.target;re(l,e.current)?H=e:Pt(l)||(H=null)};return o.addEventListener("focusin",a,!1),n==null||n.forEach(s=>s.addEventListener("focusin",a,!1)),()=>{o.removeEventListener("focusin",a,!1),n==null||n.forEach(s=>s.removeEventListener("focusin",a,!1))}},[e,t,r])}function dt(e){let t=B.getTreeNode(H);for(;t&&t.scopeRef!==e;){if(t.nodeToRestore)return!1;t=t.parent}return(t==null?void 0:t.scopeRef)===e}function Hn(e,t,r){const n=$.useRef(typeof document<"u"?X(e.current?e.current[0]:void 0).activeElement:null);oe(()=>{let o=e.current;const a=X(o?o[0]:void 0);if(!t||r)return;let s=()=>{(!H||_e(H,e))&&re(a.activeElement,e.current)&&(H=e)};return a.addEventListener("focusin",s,!1),o==null||o.forEach(l=>l.addEventListener("focusin",s,!1)),()=>{a.removeEventListener("focusin",s,!1),o==null||o.forEach(l=>l.removeEventListener("focusin",s,!1))}},[e,r]),oe(()=>{const o=X(e.current?e.current[0]:void 0);if(!t)return;let a=s=>{if(s.key!=="Tab"||s.altKey||s.ctrlKey||s.metaKey||!xe(e)||s.isComposing)return;let l=o.activeElement;if(!pe(l,e)||!dt(e))return;let f=B.getTreeNode(e);if(!f)return;let u=f.nodeToRestore,d=ce(o.body,{tabbable:!0});d.currentNode=l;let h=s.shiftKey?d.previousNode():d.nextNode();if((!u||!o.body.contains(u)||u===o.body)&&(u=void 0,f.nodeToRestore=void 0),(!h||!pe(h,e))&&u){d.currentNode=u;do h=s.shiftKey?d.previousNode():d.nextNode();while(pe(h,e));s.preventDefault(),s.stopPropagation(),h?le(h,!0):Pt(u)?le(u,!0):l.blur()}};return r||o.addEventListener("keydown",a,!0),()=>{r||o.removeEventListener("keydown",a,!0)}},[e,t,r]),oe(()=>{const o=X(e.current?e.current[0]:void 0);if(!t)return;let a=B.getTreeNode(e);if(a){var s;return a.nodeToRestore=(s=n.current)!==null&&s!==void 0?s:void 0,()=>{let l=B.getTreeNode(e);if(!l)return;let f=l.nodeToRestore;if(t&&f&&(o.activeElement&&pe(o.activeElement,e)||o.activeElement===o.body&&dt(e))){let u=B.clone();requestAnimationFrame(()=>{if(o.activeElement===o.body){let d=u.getTreeNode(e);for(;d;){if(d.nodeToRestore&&d.nodeToRestore.isConnected){ft(d.nodeToRestore);return}d=d.parent}for(d=u.getTreeNode(e);d;){if(d.scopeRef&&d.scopeRef.current&&B.getTreeNode(d.scopeRef)){let h=Tt(d.scopeRef.current,!0);ft(h);return}d=d.parent}}})}}}},[e,t])}function ft(e){e.dispatchEvent(new CustomEvent(qe,{bubbles:!0,cancelable:!0}))&&le(e)}function ce(e,t,r){let n=t!=null&&t.tabbable?Fn:_n,o=X(e).createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode(a){var s;return!(t==null||(s=t.from)===null||s===void 0)&&s.contains(a)?NodeFilter.FILTER_REJECT:a.matches(n)&&kt(a)&&(!r||re(a,r))&&(!(t!=null&&t.accept)||t.accept(a))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});return t!=null&&t.from&&(o.currentNode=t.from),o}class et{get size(){return this.fastMap.size}getTreeNode(t){return this.fastMap.get(t)}addTreeNode(t,r,n){let o=this.fastMap.get(r??null);if(!o)return;let a=new Be({scopeRef:t});o.addChild(a),a.parent=o,this.fastMap.set(t,a),n&&(a.nodeToRestore=n)}addNode(t){this.fastMap.set(t.scopeRef,t)}removeTreeNode(t){if(t===null)return;let r=this.fastMap.get(t);if(!r)return;let n=r.parent;for(let a of this.traverse())a!==r&&r.nodeToRestore&&a.nodeToRestore&&r.scopeRef&&r.scopeRef.current&&re(a.nodeToRestore,r.scopeRef.current)&&(a.nodeToRestore=r.nodeToRestore);let o=r.children;n&&(n.removeChild(r),o.size>0&&o.forEach(a=>n&&n.addChild(a))),this.fastMap.delete(r.scopeRef)}*traverse(t=this.root){if(t.scopeRef!=null&&(yield t),t.children.size>0)for(let r of t.children)yield*this.traverse(r)}clone(){var t;let r=new et;var n;for(let o of this.traverse())r.addTreeNode(o.scopeRef,(n=(t=o.parent)===null||t===void 0?void 0:t.scopeRef)!==null&&n!==void 0?n:null,o.nodeToRestore);return r}constructor(){this.fastMap=new Map,this.root=new Be({scopeRef:null}),this.fastMap.set(null,this.root)}}class Be{addChild(t){this.children.add(t),t.parent=this}removeChild(t){this.children.delete(t),t.parent=void 0}constructor(t){this.children=new Set,this.contain=!1,this.scopeRef=t.scopeRef}}let B=new et;const jt=typeof document<"u"?G.useLayoutEffect:()=>{};function On(e){const t=$.useRef(null);return jt(()=>{t.current=e},[e]),$.useCallback((...r)=>{const n=t.current;return n==null?void 0:n(...r)},[])}let pt=new Map;function An(e,t){if(e===t)return e;let r=pt.get(e);if(r)return r.forEach(o=>o(t)),t;let n=pt.get(t);return n?(n.forEach(o=>o(e)),e):t}function Kn(...e){return(...t)=>{for(let r of e)typeof r=="function"&&r(...t)}}const Pe=e=>{var t;return(t=e==null?void 0:e.ownerDocument)!==null&&t!==void 0?t:document},be=e=>e&&"window"in e&&e.window===e?e:Pe(e).defaultView||window;function Ee(...e){let t={...e[0]};for(let r=1;r<e.length;r++){let n=e[r];for(let o in n){let a=t[o],s=n[o];typeof a=="function"&&typeof s=="function"&&o[0]==="o"&&o[1]==="n"&&o.charCodeAt(2)>=65&&o.charCodeAt(2)<=90?t[o]=Kn(a,s):(o==="className"||o==="UNSAFE_className")&&typeof a=="string"&&typeof s=="string"?t[o]=Re(a,s):o==="id"&&a&&s?t.id=An(a,s):t[o]=s!==void 0?s:a}}return t}function Vn(e){var t;return typeof window>"u"||window.navigator==null?!1:((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.brands.some(r=>e.test(r.brand)))||e.test(window.navigator.userAgent)}function Wn(e){var t;return typeof window<"u"&&window.navigator!=null?e.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function Nt(e){let t=null;return()=>(t==null&&(t=e()),t)}const Un=Nt(function(){return Wn(/^Mac/i)}),qn=Nt(function(){return Vn(/Android/i)});function zn(e){return e.mozInputSource===0&&e.isTrusted?!0:qn()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}function Mt(e){return null}Mt.getCollectionNode=function*(t,r){let{childItems:n,title:o,children:a}=t,s=t.title||t.children,l=t.textValue||(typeof s=="string"?s:"")||t["aria-label"]||"";!l&&!(r!=null&&r.suppressTextValueWarning)&&console.warn("<Item> with non-plain text contents is unsupported by type to select for accessibility. Please add a `textValue` prop."),yield{type:"item",props:t,rendered:s,textValue:l,"aria-label":t["aria-label"],hasChildNodes:Bn(t),*childNodes(){if(n)for(let f of n)yield{type:"item",value:f};else if(o){let f=[];G.Children.forEach(a,u=>{f.push({type:"item",element:u})}),yield*f}}}};function Bn(e){return e.hasChildItems!=null?e.hasChildItems:!!(e.childItems||e.title&&G.Children.count(e.children)>0)}let Gn=Mt;class Zn{build(t,r){return this.context=r,bt(()=>this.iterateCollection(t))}*iterateCollection(t){let{children:r,items:n}=t;if(G.isValidElement(r)&&r.type===G.Fragment)yield*this.iterateCollection({children:r.props.children,items:n});else if(typeof r=="function"){if(!n)throw new Error("props.children was a function but props.items is missing");let o=0;for(let a of n)yield*this.getFullNode({value:a,index:o},{renderer:r}),o++}else{let o=[];G.Children.forEach(r,s=>{s&&o.push(s)});let a=0;for(let s of o){let l=this.getFullNode({element:s,index:a},{});for(let f of l)a++,yield f}}}getKey(t,r,n,o){if(t.key!=null)return t.key;if(r.type==="cell"&&r.key!=null)return`${o}${r.key}`;let a=r.value;if(a!=null){var s;let l=(s=a.key)!==null&&s!==void 0?s:a.id;if(l==null)throw new Error("No key found for item");return l}return o?`${o}.${r.index}`:`$.${r.index}`}getChildState(t,r){return{renderer:r.renderer||t.renderer}}*getFullNode(t,r,n,o){if(G.isValidElement(t.element)&&t.element.type===G.Fragment){let w=[];G.Children.forEach(t.element.props.children,D=>{w.push(D)});var a;let C=(a=t.index)!==null&&a!==void 0?a:0;for(const D of w)yield*this.getFullNode({element:D,index:C++},r,n,o);return}let s=t.element;if(!s&&t.value&&r&&r.renderer){let w=this.cache.get(t.value);if(w&&(!w.shouldInvalidate||!w.shouldInvalidate(this.context))){w.index=t.index,w.parentKey=o?o.key:null,yield w;return}s=r.renderer(t.value)}if(G.isValidElement(s)){let w=s.type;if(typeof w!="function"&&typeof w.getCollectionNode!="function"){let M=s.type;throw new Error(`Unknown element <${M}> in collection.`)}let C=w.getCollectionNode(s.props,this.context);var l;let D=(l=t.index)!==null&&l!==void 0?l:0,k=C.next();for(;!k.done&&k.value;){let M=k.value;t.index=D;var f;let E=(f=M.key)!==null&&f!==void 0?f:null;E==null&&(E=M.element?null:this.getKey(s,t,r,n));let O=[...this.getFullNode({...M,key:E,index:D,wrapper:Jn(t.wrapper,M.wrapper)},this.getChildState(r,M),n?`${n}${s.key}`:s.key,o)];for(let S of O){var u,d;S.value=(d=(u=M.value)!==null&&u!==void 0?u:t.value)!==null&&d!==void 0?d:null,S.value&&this.cache.set(S.value,S);var h;if(t.type&&S.type!==t.type)throw new Error(`Unsupported type <${Ve(S.type)}> in <${Ve((h=o==null?void 0:o.type)!==null&&h!==void 0?h:"unknown parent type")}>. Only <${Ve(t.type)}> is supported.`);D++,yield S}k=C.next(O)}return}if(t.key==null||t.type==null)return;let b=this;var p,g;let x={type:t.type,props:t.props,key:t.key,parentKey:o?o.key:null,value:(p=t.value)!==null&&p!==void 0?p:null,level:o?o.level+1:0,index:t.index,rendered:t.rendered,textValue:(g=t.textValue)!==null&&g!==void 0?g:"","aria-label":t["aria-label"],wrapper:t.wrapper,shouldInvalidate:t.shouldInvalidate,hasChildNodes:t.hasChildNodes||!1,childNodes:bt(function*(){if(!t.hasChildNodes||!t.childNodes)return;let w=0;for(let C of t.childNodes()){C.key!=null&&(C.key=`${x.key}${C.key}`);let D=b.getFullNode({...C,index:w},b.getChildState(r,C),x.key,x);for(let k of D)w++,yield k}})};yield x}constructor(){this.cache=new WeakMap}}function bt(e){let t=[],r=null;return{*[Symbol.iterator](){for(let n of t)yield n;r||(r=e());for(let n of r)t.push(n),yield n}}}function Jn(e,t){if(e&&t)return r=>e(t(r));if(e)return e;if(t)return t}function Ve(e){return e[0].toUpperCase()+e.slice(1)}function Qn(e,t,r){let n=$.useMemo(()=>new Zn,[]),{children:o,items:a,collection:s}=e;return $.useMemo(()=>{if(s)return s;let f=n.build({children:o,items:a},r);return t(f)},[n,o,a,s,r,t])}function Yn(e){const{isSelected:t,disableAnimation:r,...n}=e;return i.jsx("svg",{"aria-hidden":"true","data-selected":t,role:"presentation",viewBox:"0 0 17 18",...n,children:i.jsx("polyline",{fill:"none",points:"1 9 7 14 15 4",stroke:"currentColor",strokeDasharray:22,strokeDashoffset:t?44:66,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,style:r?{}:{transition:"stroke-dashoffset 200ms ease"}})})}class Xn{isDefaultPrevented(){return this.nativeEvent.defaultPrevented}preventDefault(){this.defaultPrevented=!0,this.nativeEvent.preventDefault()}stopPropagation(){this.nativeEvent.stopPropagation(),this.isPropagationStopped=()=>!0}isPropagationStopped(){return!1}persist(){}constructor(t,r){this.nativeEvent=r,this.target=r.target,this.currentTarget=r.currentTarget,this.relatedTarget=r.relatedTarget,this.bubbles=r.bubbles,this.cancelable=r.cancelable,this.defaultPrevented=r.defaultPrevented,this.eventPhase=r.eventPhase,this.isTrusted=r.isTrusted,this.timeStamp=r.timeStamp,this.type=t}}function Dt(e){let t=$.useRef({isFocused:!1,observer:null});jt(()=>{const n=t.current;return()=>{n.observer&&(n.observer.disconnect(),n.observer=null)}},[]);let r=On(n=>{e==null||e(n)});return $.useCallback(n=>{if(n.target instanceof HTMLButtonElement||n.target instanceof HTMLInputElement||n.target instanceof HTMLTextAreaElement||n.target instanceof HTMLSelectElement){t.current.isFocused=!0;let o=n.target,a=s=>{t.current.isFocused=!1,o.disabled&&r(new Xn("blur",s)),t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)};o.addEventListener("focusout",a,{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&o.disabled){var s;(s=t.current.observer)===null||s===void 0||s.disconnect();let l=o===document.activeElement?null:document.activeElement;o.dispatchEvent(new FocusEvent("blur",{relatedTarget:l})),o.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:l}))}}),t.current.observer.observe(o,{attributes:!0,attributeFilter:["disabled"]})}},[r])}function eo(e){let{isDisabled:t,onFocus:r,onBlur:n,onFocusChange:o}=e;const a=$.useCallback(f=>{if(f.target===f.currentTarget)return n&&n(f),o&&o(!1),!0},[n,o]),s=Dt(a),l=$.useCallback(f=>{const u=Pe(f.target);f.target===f.currentTarget&&u.activeElement===f.target&&(r&&r(f),o&&o(!0),s(f))},[o,r,s]);return{focusProps:{onFocus:!t&&(r||o||n)?l:void 0,onBlur:!t&&(n||o)?a:void 0}}}let Te=null,Ge=new Set,Se=new Map,ve=!1,Ze=!1;const to={Tab:!0,Escape:!0};function tt(e,t){for(let r of Ge)r(e,t)}function ro(e){return!(e.metaKey||!Un()&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function Fe(e){ve=!0,ro(e)&&(Te="keyboard",tt("keyboard",e))}function te(e){Te="pointer",(e.type==="mousedown"||e.type==="pointerdown")&&(ve=!0,tt("pointer",e))}function _t(e){zn(e)&&(ve=!0,Te="virtual")}function Ft(e){e.target===window||e.target===document||(!ve&&!Ze&&(Te="virtual",tt("virtual",e)),ve=!1,Ze=!1)}function Lt(){ve=!1,Ze=!0}function Je(e){if(typeof window>"u"||Se.get(be(e)))return;const t=be(e),r=Pe(e);let n=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){ve=!0,n.apply(this,arguments)},r.addEventListener("keydown",Fe,!0),r.addEventListener("keyup",Fe,!0),r.addEventListener("click",_t,!0),t.addEventListener("focus",Ft,!0),t.addEventListener("blur",Lt,!1),typeof PointerEvent<"u"?(r.addEventListener("pointerdown",te,!0),r.addEventListener("pointermove",te,!0),r.addEventListener("pointerup",te,!0)):(r.addEventListener("mousedown",te,!0),r.addEventListener("mousemove",te,!0),r.addEventListener("mouseup",te,!0)),t.addEventListener("beforeunload",()=>{It(e)},{once:!0}),Se.set(t,{focus:n})}const It=(e,t)=>{const r=be(e),n=Pe(e);t&&n.removeEventListener("DOMContentLoaded",t),Se.has(r)&&(r.HTMLElement.prototype.focus=Se.get(r).focus,n.removeEventListener("keydown",Fe,!0),n.removeEventListener("keyup",Fe,!0),n.removeEventListener("click",_t,!0),r.removeEventListener("focus",Ft,!0),r.removeEventListener("blur",Lt,!1),typeof PointerEvent<"u"?(n.removeEventListener("pointerdown",te,!0),n.removeEventListener("pointermove",te,!0),n.removeEventListener("pointerup",te,!0)):(n.removeEventListener("mousedown",te,!0),n.removeEventListener("mousemove",te,!0),n.removeEventListener("mouseup",te,!0)),Se.delete(r))};function no(e){const t=Pe(e);let r;return t.readyState!=="loading"?Je(e):(r=()=>{Je(e)},t.addEventListener("DOMContentLoaded",r)),()=>It(e,r)}typeof document<"u"&&no();function rt(){return Te!=="pointer"}const oo=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function ao(e,t,r){var n;const o=typeof window<"u"?be(r==null?void 0:r.target).HTMLInputElement:HTMLInputElement,a=typeof window<"u"?be(r==null?void 0:r.target).HTMLTextAreaElement:HTMLTextAreaElement,s=typeof window<"u"?be(r==null?void 0:r.target).HTMLElement:HTMLElement,l=typeof window<"u"?be(r==null?void 0:r.target).KeyboardEvent:KeyboardEvent;return e=e||(r==null?void 0:r.target)instanceof o&&!oo.has(r==null||(n=r.target)===null||n===void 0?void 0:n.type)||(r==null?void 0:r.target)instanceof a||(r==null?void 0:r.target)instanceof s&&(r==null?void 0:r.target.isContentEditable),!(e&&t==="keyboard"&&r instanceof l&&!to[r.key])}function so(e,t,r){Je(),$.useEffect(()=>{let n=(o,a)=>{ao(!!(r!=null&&r.isTextInput),o,a)&&e(rt())};return Ge.add(n),()=>{Ge.delete(n)}},t)}function lo(e){let{isDisabled:t,onBlurWithin:r,onFocusWithin:n,onFocusWithinChange:o}=e,a=$.useRef({isFocusWithin:!1}),s=$.useCallback(u=>{a.current.isFocusWithin&&!u.currentTarget.contains(u.relatedTarget)&&(a.current.isFocusWithin=!1,r&&r(u),o&&o(!1))},[r,o,a]),l=Dt(s),f=$.useCallback(u=>{!a.current.isFocusWithin&&document.activeElement===u.target&&(n&&n(u),o&&o(!0),a.current.isFocusWithin=!0,l(u))},[n,o,l]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:f,onBlur:s}}}let Le=!1,We=0;function Qe(){Le=!0,setTimeout(()=>{Le=!1},50)}function vt(e){e.pointerType==="touch"&&Qe()}function io(){if(!(typeof document>"u"))return typeof PointerEvent<"u"?document.addEventListener("pointerup",vt):document.addEventListener("touchend",Qe),We++,()=>{We--,!(We>0)&&(typeof PointerEvent<"u"?document.removeEventListener("pointerup",vt):document.removeEventListener("touchend",Qe))}}function co(e){let{onHoverStart:t,onHoverChange:r,onHoverEnd:n,isDisabled:o}=e,[a,s]=$.useState(!1),l=$.useRef({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;$.useEffect(io,[]);let{hoverProps:f,triggerHoverEnd:u}=$.useMemo(()=>{let d=(p,g)=>{if(l.pointerType=g,o||g==="touch"||l.isHovered||!p.currentTarget.contains(p.target))return;l.isHovered=!0;let x=p.currentTarget;l.target=x,t&&t({type:"hoverstart",target:x,pointerType:g}),r&&r(!0),s(!0)},h=(p,g)=>{if(l.pointerType="",l.target=null,g==="touch"||!l.isHovered)return;l.isHovered=!1;let x=p.currentTarget;n&&n({type:"hoverend",target:x,pointerType:g}),r&&r(!1),s(!1)},b={};return typeof PointerEvent<"u"?(b.onPointerEnter=p=>{Le&&p.pointerType==="mouse"||d(p,p.pointerType)},b.onPointerLeave=p=>{!o&&p.currentTarget.contains(p.target)&&h(p,p.pointerType)}):(b.onTouchStart=()=>{l.ignoreEmulatedMouseEvents=!0},b.onMouseEnter=p=>{!l.ignoreEmulatedMouseEvents&&!Le&&d(p,"mouse"),l.ignoreEmulatedMouseEvents=!1},b.onMouseLeave=p=>{!o&&p.currentTarget.contains(p.target)&&h(p,"mouse")}),{hoverProps:b,triggerHoverEnd:h}},[t,r,n,o,l]);return $.useEffect(()=>{o&&u({currentTarget:l.target},l.pointerType)},[o]),{hoverProps:f,isHovered:a}}function uo(e={}){let{autoFocus:t=!1,isTextInput:r,within:n}=e,o=$.useRef({isFocused:!1,isFocusVisible:t||rt()}),[a,s]=$.useState(!1),[l,f]=$.useState(()=>o.current.isFocused&&o.current.isFocusVisible),u=$.useCallback(()=>f(o.current.isFocused&&o.current.isFocusVisible),[]),d=$.useCallback(p=>{o.current.isFocused=p,s(p),u()},[u]);so(p=>{o.current.isFocusVisible=p,u()},[],{isTextInput:r});let{focusProps:h}=eo({isDisabled:n,onFocusChange:d}),{focusWithinProps:b}=lo({isDisabled:!n,onFocusWithinChange:d});return{isFocused:a,isFocusVisible:l,focusProps:n?b:h}}var se=e=>e?"true":void 0;function Rt(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=Rt(e[t]))&&(n&&(n+=" "),n+=r);else for(t in e)e[t]&&(n&&(n+=" "),n+=t);return n}function Ce(...e){for(var t=0,r,n,o="";t<e.length;)(r=e[t++])&&(n=Rt(r))&&(o&&(o+=" "),o+=n);return o}var ht={};function fo(e,t,...r){const o=`[Next UI]${` [${t}]`}: ${e}`;typeof console>"u"||ht[o]||(ht[o]=!0)}var po=Object.create,Ht=Object.defineProperty,bo=Object.getOwnPropertyDescriptor,Ot=Object.getOwnPropertyNames,vo=Object.getPrototypeOf,ho=Object.prototype.hasOwnProperty,At=(e,t)=>function(){return t||(0,e[Ot(e)[0]])((t={exports:{}}).exports,t),t.exports},mo=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Ot(t))!ho.call(e,o)&&o!==r&&Ht(e,o,{get:()=>t[o],enumerable:!(n=bo(t,o))||n.enumerable});return e},yo=(e,t,r)=>(r=e!=null?po(vo(e)):{},mo(!e||!e.__esModule?Ht(r,"default",{value:e,enumerable:!0}):r,e)),go=At({"../../../node_modules/.pnpm/react@18.2.0/node_modules/react/cjs/react.production.min.js"(e){var t=Symbol.for("react.element"),r=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),f=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),b=Symbol.iterator;function p(c){return c===null||typeof c!="object"?null:(c=b&&c[b]||c["@@iterator"],typeof c=="function"?c:null)}var g={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},x=Object.assign,w={};function C(c,v,T){this.props=c,this.context=v,this.refs=w,this.updater=T||g}C.prototype.isReactComponent={},C.prototype.setState=function(c,v){if(typeof c!="object"&&typeof c!="function"&&c!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,c,v,"setState")},C.prototype.forceUpdate=function(c){this.updater.enqueueForceUpdate(this,c,"forceUpdate")};function D(){}D.prototype=C.prototype;function k(c,v,T){this.props=c,this.context=v,this.refs=w,this.updater=T||g}var M=k.prototype=new D;M.constructor=k,x(M,C.prototype),M.isPureReactComponent=!0;var E=Array.isArray,_=Object.prototype.hasOwnProperty,O={current:null},S={key:!0,ref:!0,__self:!0,__source:!0};function V(c,v,T){var j,L={},A=null,W=null;if(v!=null)for(j in v.ref!==void 0&&(W=v.ref),v.key!==void 0&&(A=""+v.key),v)_.call(v,j)&&!S.hasOwnProperty(j)&&(L[j]=v[j]);var K=arguments.length-2;if(K===1)L.children=T;else if(1<K){for(var R=Array(K),Z=0;Z<K;Z++)R[Z]=arguments[Z+2];L.children=R}if(c&&c.defaultProps)for(j in K=c.defaultProps,K)L[j]===void 0&&(L[j]=K[j]);return{$$typeof:t,type:c,key:A,ref:W,props:L,_owner:O.current}}function z(c,v){return{$$typeof:t,type:c.type,key:v,ref:c.ref,props:c.props,_owner:c._owner}}function I(c){return typeof c=="object"&&c!==null&&c.$$typeof===t}function Q(c){var v={"=":"=0",":":"=2"};return"$"+c.replace(/[=:]/g,function(T){return v[T]})}var y=/\/+/g;function m(c,v){return typeof c=="object"&&c!==null&&c.key!=null?Q(""+c.key):v.toString(36)}function F(c,v,T,j,L){var A=typeof c;(A==="undefined"||A==="boolean")&&(c=null);var W=!1;if(c===null)W=!0;else switch(A){case"string":case"number":W=!0;break;case"object":switch(c.$$typeof){case t:case r:W=!0}}if(W)return W=c,L=L(W),c=j===""?"."+m(W,0):j,E(L)?(T="",c!=null&&(T=c.replace(y,"$&/")+"/"),F(L,v,T,"",function(Z){return Z})):L!=null&&(I(L)&&(L=z(L,T+(!L.key||W&&W.key===L.key?"":(""+L.key).replace(y,"$&/")+"/")+c)),v.push(L)),1;if(W=0,j=j===""?".":j+":",E(c))for(var K=0;K<c.length;K++){A=c[K];var R=j+m(A,K);W+=F(A,v,T,R,L)}else if(R=p(c),typeof R=="function")for(c=R.call(c),K=0;!(A=c.next()).done;)A=A.value,R=j+m(A,K++),W+=F(A,v,T,R,L);else if(A==="object")throw v=String(c),Error("Objects are not valid as a React child (found: "+(v==="[object Object]"?"object with keys {"+Object.keys(c).join(", ")+"}":v)+"). If you meant to render a collection of children, use an array instead.");return W}function N(c,v,T){if(c==null)return c;var j=[],L=0;return F(c,j,"","",function(A){return v.call(T,A,L++)}),j}function U(c){if(c._status===-1){var v=c._result;v=v(),v.then(function(T){(c._status===0||c._status===-1)&&(c._status=1,c._result=T)},function(T){(c._status===0||c._status===-1)&&(c._status=2,c._result=T)}),c._status===-1&&(c._status=0,c._result=v)}if(c._status===1)return c._result.default;throw c._result}var P={current:null},Y={transition:null},ie={ReactCurrentDispatcher:P,ReactCurrentBatchConfig:Y,ReactCurrentOwner:O};e.Children={map:N,forEach:function(c,v,T){N(c,function(){v.apply(this,arguments)},T)},count:function(c){var v=0;return N(c,function(){v++}),v},toArray:function(c){return N(c,function(v){return v})||[]},only:function(c){if(!I(c))throw Error("React.Children.only expected to receive a single React element child.");return c}},e.Component=C,e.Fragment=n,e.Profiler=a,e.PureComponent=k,e.StrictMode=o,e.Suspense=u,e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ie,e.cloneElement=function(c,v,T){if(c==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+c+".");var j=x({},c.props),L=c.key,A=c.ref,W=c._owner;if(v!=null){if(v.ref!==void 0&&(A=v.ref,W=O.current),v.key!==void 0&&(L=""+v.key),c.type&&c.type.defaultProps)var K=c.type.defaultProps;for(R in v)_.call(v,R)&&!S.hasOwnProperty(R)&&(j[R]=v[R]===void 0&&K!==void 0?K[R]:v[R])}var R=arguments.length-2;if(R===1)j.children=T;else if(1<R){K=Array(R);for(var Z=0;Z<R;Z++)K[Z]=arguments[Z+2];j.children=K}return{$$typeof:t,type:c.type,key:L,ref:A,props:j,_owner:W}},e.createContext=function(c){return c={$$typeof:l,_currentValue:c,_currentValue2:c,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},c.Provider={$$typeof:s,_context:c},c.Consumer=c},e.createElement=V,e.createFactory=function(c){var v=V.bind(null,c);return v.type=c,v},e.createRef=function(){return{current:null}},e.forwardRef=function(c){return{$$typeof:f,render:c}},e.isValidElement=I,e.lazy=function(c){return{$$typeof:h,_payload:{_status:-1,_result:c},_init:U}},e.memo=function(c,v){return{$$typeof:d,type:c,compare:v===void 0?null:v}},e.startTransition=function(c){var v=Y.transition;Y.transition={};try{c()}finally{Y.transition=v}},e.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},e.useCallback=function(c,v){return P.current.useCallback(c,v)},e.useContext=function(c){return P.current.useContext(c)},e.useDebugValue=function(){},e.useDeferredValue=function(c){return P.current.useDeferredValue(c)},e.useEffect=function(c,v){return P.current.useEffect(c,v)},e.useId=function(){return P.current.useId()},e.useImperativeHandle=function(c,v,T){return P.current.useImperativeHandle(c,v,T)},e.useInsertionEffect=function(c,v){return P.current.useInsertionEffect(c,v)},e.useLayoutEffect=function(c,v){return P.current.useLayoutEffect(c,v)},e.useMemo=function(c,v){return P.current.useMemo(c,v)},e.useReducer=function(c,v,T){return P.current.useReducer(c,v,T)},e.useRef=function(c){return P.current.useRef(c)},e.useState=function(c){return P.current.useState(c)},e.useSyncExternalStore=function(c,v,T){return P.current.useSyncExternalStore(c,v,T)},e.useTransition=function(){return P.current.useTransition()},e.version="18.2.0"}}),$o=At({"../../../node_modules/.pnpm/react@18.2.0/node_modules/react/index.js"(e,t){t.exports=go()}});yo($o());function wo(e){for(const t in e)t.startsWith("on")&&delete e[t];return e}function xo(e){if(!e||typeof e!="object")return"";try{return JSON.stringify(e)}catch{return""}}/**
 * @license React
 * react.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *//**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function Eo(e){const t=$.useRef(null);return $.useImperativeHandle(e,()=>t.current),t}var So=new Set(["id","type","style","title","role","tabIndex","htmlFor","width","height","abbr","accept","acceptCharset","accessKey","action","allowFullScreen","allowTransparency","alt","async","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","challenge","charset","checked","cite","class","className","cols","colSpan","command","content","contentEditable","contextMenu","controls","coords","crossOrigin","data","dateTime","default","defer","dir","disabled","download","draggable","dropzone","encType","enterKeyHint","for","form","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","headers","hidden","high","href","hrefLang","httpEquiv","icon","inputMode","isMap","itemId","itemProp","itemRef","itemScope","itemType","kind","label","lang","list","loop","manifest","max","maxLength","media","mediaGroup","method","min","minLength","multiple","muted","name","noValidate","open","optimum","pattern","ping","placeholder","poster","preload","radioGroup","referrerPolicy","readOnly","rel","required","rows","rowSpan","sandbox","scope","scoped","scrolling","seamless","selected","shape","size","sizes","slot","sortable","span","spellCheck","src","srcDoc","srcSet","start","step","target","translate","typeMustMatch","useMap","value","wmode","wrap"]),Co=new Set(["onCopy","onCut","onPaste","onLoad","onError","onWheel","onScroll","onCompositionEnd","onCompositionStart","onCompositionUpdate","onKeyDown","onKeyPress","onKeyUp","onFocus","onBlur","onChange","onInput","onSubmit","onClick","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onPointerDown","onPointerEnter","onPointerLeave","onPointerUp","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionEnd"]),mt=/^(data-.*)$/,ko=/^(aria-.*)$/,De=/^(on[A-Z].*)$/;function nt(e,t={}){let{labelable:r=!0,enabled:n=!0,propNames:o,omitPropNames:a,omitEventNames:s,omitDataProps:l,omitEventProps:f}=t,u={};if(!n)return e;for(const d in e)a!=null&&a.has(d)||s!=null&&s.has(d)&&De.test(d)||De.test(d)&&!Co.has(d)||l&&mt.test(d)||f&&De.test(d)||(Object.prototype.hasOwnProperty.call(e,d)&&(So.has(d)||r&&ko.test(d)||o!=null&&o.has(d)||mt.test(d))||De.test(d))&&(u[d]=e[d]);return u}function Po(e){var t,r;const n=Ye(),[o,a]=dr(e,st.variantKeys),{as:s,item:l,state:f,shortcut:u,description:d,startContent:h,endContent:b,isVirtualized:p,selectedIcon:g,className:x,classNames:w,onAction:C,autoFocus:D,onPress:k,onPressStart:M,onPressUp:E,onPressEnd:_,onPressChange:O,onHoverStart:S,onHoverChange:V,onHoverEnd:z,hideSelectedIcon:I=!1,isReadOnly:Q=!1,closeOnSelect:y,onClose:m,onClick:F,...N}=o,U=(r=(t=e.disableAnimation)!=null?t:n==null?void 0:n.disableAnimation)!=null?r:!1,P=$.useRef(null),Y=s||(N!=null&&N.href?"a":"li"),ie=typeof Y=="string",{rendered:c,key:v}=l,T=f.disabledKeys.has(v)||e.isDisabled,j=f.selectionManager.selectionMode!=="none",L=Fr(),{isFocusVisible:A,focusProps:W}=uo({autoFocus:D});F&&typeof F=="function"&&fo("onClick is deprecated, please use onPress instead. See: https://github.com/heroui-inc/heroui/issues/4292","MenuItem");const K=$.useCallback(J=>{F==null||F(J),k==null||k(J)},[F,k]),{isPressed:R,isFocused:Z,isSelected:ye,isDisabled:ae,menuItemProps:q,labelProps:ge,descriptionProps:He,keyboardShortcutProps:Bt}=pn({key:v,onClose:m,isDisabled:T,onPress:K,onPressStart:M,onPressUp:E,onPressEnd:_,onPressChange:O,"aria-label":o["aria-label"],closeOnSelect:y,isVirtualized:p,onAction:C},f,P);let{hoverProps:Gt,isHovered:ot}=co({isDisabled:ae,onHoverStart(J){rt()||(f.selectionManager.setFocused(!0),f.selectionManager.setFocusedKey(v)),S==null||S(J)},onHoverChange:V,onHoverEnd:z}),Oe=q;const de=$.useMemo(()=>st({...a,isDisabled:ae,disableAnimation:U,hasTitleTextChild:typeof c=="string",hasDescriptionTextChild:typeof d=="string"}),[xo(a),ae,U,c,d]),Zt=Ce(w==null?void 0:w.base,x);Q&&(Oe=wo(Oe));const Jt=(J={})=>({ref:P,...Ee(Q?{}:W,nt(N,{enabled:ie}),Oe,Gt,J),"data-focus":se(Z),"data-selectable":se(j),"data-hover":se(L?ot||R:ot),"data-disabled":se(ae),"data-selected":se(ye),"data-pressed":se(R),"data-focus-visible":se(A),className:de.base({class:Ce(Zt,J.className)})}),Qt=(J={})=>({...Ee(ge,J),className:de.title({class:w==null?void 0:w.title})}),Yt=(J={})=>({...Ee(He,J),className:de.description({class:w==null?void 0:w.description})}),Xt=(J={})=>({...Ee(Bt,J),className:de.shortcut({class:w==null?void 0:w.shortcut})}),er=$.useCallback((J={})=>({"aria-hidden":se(!0),"data-disabled":se(ae),className:de.selectedIcon({class:w==null?void 0:w.selectedIcon}),...J}),[ae,de,w]);return{Component:Y,domRef:P,slots:de,classNames:w,isSelectable:j,isSelected:ye,isDisabled:ae,rendered:c,shortcut:u,description:d,startContent:h,endContent:b,selectedIcon:g,disableAnimation:U,getItemProps:Jt,getLabelProps:Qt,hideSelectedIcon:I,getDescriptionProps:Yt,getKeyboardShortcutProps:Xt,getSelectedIconProps:er}}var Kt=e=>{const{Component:t,slots:r,classNames:n,rendered:o,shortcut:a,description:s,isSelectable:l,isSelected:f,isDisabled:u,selectedIcon:d,startContent:h,endContent:b,disableAnimation:p,hideSelectedIcon:g,getItemProps:x,getLabelProps:w,getDescriptionProps:C,getKeyboardShortcutProps:D,getSelectedIconProps:k}=Po(e),M=$.useMemo(()=>{const E=i.jsx(Yn,{disableAnimation:p,isSelected:f});return typeof d=="function"?d({icon:E,isSelected:f,isDisabled:u}):d||E},[d,f,u,p]);return i.jsxs(t,{...x(),children:[h,s?i.jsxs("div",{className:r.wrapper({class:n==null?void 0:n.wrapper}),children:[i.jsx("span",{...w(),children:o}),i.jsx("span",{...C(),children:s})]}):i.jsx("span",{...w(),children:o}),a&&i.jsx("kbd",{...D(),children:a}),l&&!g&&i.jsx("span",{...k(),children:M}),b]})};Kt.displayName="HeroUI.MenuItem";var Vt=Kt;function To(e){let t=nt(e,{enabled:typeof e.elementType=="string"}),r;return e.orientation==="vertical"&&(r="vertical"),e.elementType!=="hr"?{separatorProps:{...t,role:"separator","aria-orientation":r}}:{separatorProps:t}}function jo(e){const{as:t,className:r,orientation:n,...o}=e;let a=t||"hr";a==="hr"&&n==="vertical"&&(a="div");const{separatorProps:s}=To({elementType:typeof a=="string"?a:"hr",orientation:n}),l=$.useMemo(()=>Or({orientation:n,className:r}),[n,r]),f=$.useCallback((u={})=>({className:l,role:"separator","data-orientation":n,...s,...o,...u}),[l,n,s,o]);return{Component:a,getDividerProps:f}}var Wt=ke((e,t)=>{const{Component:r,getDividerProps:n}=jo({...e});return i.jsx(r,{ref:t,...n()})});Wt.displayName="HeroUI.Divider";var No=Wt,Ut=ke(({item:e,state:t,as:r,variant:n,color:o,disableAnimation:a,onAction:s,closeOnSelect:l,className:f,classNames:u,showDivider:d=!1,hideSelectedIcon:h,dividerProps:b={},itemClasses:p,title:g,...x},w)=>{const C=r||"li",D=$.useMemo(()=>Lr(),[]),k=Ce(u==null?void 0:u.base,f),M=Ce(u==null?void 0:u.divider,b==null?void 0:b.className),{itemProps:E,headingProps:_,groupProps:O}=bn({heading:e.rendered,"aria-label":e["aria-label"]});return i.jsxs(C,{"data-slot":"base",...Ee(E,x),className:D.base({class:k}),children:[e.rendered&&i.jsx("span",{..._,className:D.heading({class:u==null?void 0:u.heading}),"data-slot":"heading",children:e.rendered}),i.jsxs("ul",{...O,className:D.group({class:u==null?void 0:u.group}),"data-has-title":!!e.rendered,"data-slot":"group",children:[[...e.childNodes].map(S=>{const{key:V,props:z}=S;let I=i.jsx(Vt,{classNames:p,closeOnSelect:l,color:o,disableAnimation:a,hideSelectedIcon:h,item:S,state:t,variant:n,onAction:s,...z},V);return S.wrapper&&(I=S.wrapper(I)),I}),d&&i.jsx(No,{as:"li",className:D.divider({class:M}),...b})]})]})});Ut.displayName="HeroUI.MenuSection";var Mo=Ut;class Do{*[Symbol.iterator](){yield*this.iterable}get size(){return this.keyMap.size}getKeys(){return this.keyMap.keys()}getKeyBefore(t){let r=this.keyMap.get(t);var n;return r&&(n=r.prevKey)!==null&&n!==void 0?n:null}getKeyAfter(t){let r=this.keyMap.get(t);var n;return r&&(n=r.nextKey)!==null&&n!==void 0?n:null}getFirstKey(){return this.firstKey}getLastKey(){return this.lastKey}getItem(t){var r;return(r=this.keyMap.get(t))!==null&&r!==void 0?r:null}at(t){const r=[...this.getKeys()];return this.getItem(r[t])}constructor(t,{expandedKeys:r}={}){this.keyMap=new Map,this.firstKey=null,this.lastKey=null,this.iterable=t,r=r||new Set;let n=l=>{if(this.keyMap.set(l.key,l),l.childNodes&&(l.type==="section"||r.has(l.key)))for(let f of l.childNodes)n(f)};for(let l of t)n(l);let o=null,a=0;for(let[l,f]of this.keyMap)o?(o.nextKey=l,f.prevKey=o.key):(this.firstKey=l,f.prevKey=void 0),f.type==="item"&&(f.index=a++),o=f,o.nextKey=void 0;var s;this.lastKey=(s=o==null?void 0:o.key)!==null&&s!==void 0?s:null}}function _o(e){let{onExpandedChange:t}=e,[r,n]=fr(e.expandedKeys?new Set(e.expandedKeys):void 0,e.defaultExpandedKeys?new Set(e.defaultExpandedKeys):new Set,t),o=Ur(e),a=$.useMemo(()=>e.disabledKeys?new Set(e.disabledKeys):new Set,[e.disabledKeys]),s=Qn(e,$.useCallback(f=>new Do(f,{expandedKeys:r}),[r]),null);return $.useEffect(()=>{o.focusedKey!=null&&!s.getItem(o.focusedKey)&&o.setFocusedKey(null)},[s,o.focusedKey]),{collection:s,expandedKeys:r,disabledKeys:a,toggleKey:f=>{n(Fo(r,f))},setExpandedKeys:n,selectionManager:new qr(s,o)}}function Fo(e,t){let r=new Set(e);return r.has(t)?r.delete(t):r.add(t),r}function Lo(e){var t;const r=Ye(),{as:n,ref:o,variant:a,color:s,children:l,disableAnimation:f=(t=r==null?void 0:r.disableAnimation)!=null?t:!1,onAction:u,closeOnSelect:d,itemClasses:h,className:b,state:p,topContent:g,bottomContent:x,hideEmptyContent:w=!1,hideSelectedIcon:C=!1,emptyContent:D="No items.",menuProps:k,onClose:M,classNames:E,..._}=e,O=n||"ul",S=Eo(o),V=typeof O=="string",z=_o({..._,...k,children:l}),I=p||z,{menuProps:Q}=fn({..._,...k,onAction:u},I,S),y=$.useMemo(()=>Ir({className:b}),[b]),m=Ce(E==null?void 0:E.base,b);return{Component:O,state:I,variant:a,color:s,disableAnimation:f,onClose:M,topContent:g,bottomContent:x,closeOnSelect:d,className:b,itemClasses:h,getBaseProps:(P={})=>({ref:S,"data-slot":"base",className:y.base({class:m}),...nt(_,{enabled:V}),...P}),getListProps:(P={})=>({"data-slot":"list",className:y.list({class:E==null?void 0:E.list}),...Q,...P}),hideEmptyContent:w,hideSelectedIcon:C,getEmptyContentProps:(P={})=>({children:D,className:y.emptyContent({class:E==null?void 0:E.emptyContent}),...P})}}var Io=ke(function(t,r){const{Component:n,state:o,closeOnSelect:a,color:s,disableAnimation:l,hideSelectedIcon:f,hideEmptyContent:u,variant:d,onClose:h,topContent:b,bottomContent:p,itemClasses:g,getBaseProps:x,getListProps:w,getEmptyContentProps:C}=Lo({...t,ref:r}),D=i.jsxs(n,{...w(),children:[!o.collection.size&&!u&&i.jsx("li",{children:i.jsx("div",{...C()})}),[...o.collection].map(k=>{const M={closeOnSelect:a,color:s,disableAnimation:l,item:k,state:o,variant:d,onClose:h,hideSelectedIcon:f,...k.props},E=un(g,M==null?void 0:M.classNames);if(k.type==="section")return i.jsx(Mo,{...M,itemClasses:E},k.key);let _=i.jsx(Vt,{...M,classNames:E},k.key);return k.wrapper&&(_=k.wrapper(_)),_})]});return i.jsxs("div",{...x(),children:[b,D,p]})}),Ro=Io,Ho=Gn,he=Ho,Oo=ke(function(t,r){const{getMenuProps:n}=Ct();return i.jsx(pr,{children:i.jsx(Mn,{contain:!0,restoreFocus:!0,children:i.jsx(Ro,{...n(t,r)})})})}),Ao=Oo,qt=e=>{const{getMenuTriggerProps:t}=Ct(),{children:r,...n}=e;return i.jsx(br,{...t(n),children:r})};qt.displayName="HeroUI.DropdownTrigger";var Ko=qt,Vo=(e,t,r)=>{const n=t==null?void 0:t.current;if(!n||!n.contains(e)){const o=document.querySelectorAll("body > span[data-focus-scope-start]");let a=[];if(o.forEach(s=>{a.push(s.nextElementSibling)}),a.length===1)return r.close(),!1}return!n||!n.contains(e)},Wo=(e,t)=>{if(e){const r=Array.isArray(e.children)?e.children:[...(e==null?void 0:e.items)||[]];if(r&&r.length)return r.find(o=>{if(o&&o.key===t)return o})||{}}return null},Uo=(e,t,r)=>{const n=r||Wo(e,t);return n&&n.props&&"closeOnSelect"in n.props?n.props.closeOnSelect:e==null?void 0:e.closeOnSelect};function qo(e){var t;const r=Ye(),{as:n,triggerRef:o,isOpen:a,defaultOpen:s,onOpenChange:l,isDisabled:f,type:u="menu",trigger:d="press",placement:h="bottom",closeOnSelect:b=!0,shouldBlockScroll:p=!0,classNames:g,disableAnimation:x=(t=r==null?void 0:r.disableAnimation)!=null?t:!1,onClose:w,className:C,...D}=e,k=n||"div",M=$.useRef(null),E=o||M,_=$.useRef(null),O=$.useRef(null),S=zr({trigger:d,isOpen:a,defaultOpen:s,onOpenChange:N=>{l==null||l(N),N||w==null||w()}}),{menuTriggerProps:V,menuProps:z}=Br({type:u,trigger:d,isDisabled:f},S,E),I=$.useMemo(()=>ln({className:C}),[C]),Q=N=>{N!==void 0&&!N||b&&S.close()},y=(N={})=>{const U=Ke(D,N);return{state:S,placement:h,ref:O,disableAnimation:x,shouldBlockScroll:p,scrollRef:_,triggerRef:E,...U,classNames:{...g,...N.classNames,content:mn(I,g==null?void 0:g.content,N.className)},shouldCloseOnInteractOutside:U!=null&&U.shouldCloseOnInteractOutside?U.shouldCloseOnInteractOutside:P=>Vo(P,M,S)}},m=(N={})=>{const{onPress:U,onPressStart:P,...Y}=V;return Ke(Y,{isDisabled:f},N)},F=(N,U=null)=>({ref:gn(U,_),menuProps:z,closeOnSelect:b,...Ke(N,{onAction:(P,Y)=>{const ie=Uo(N,P,Y);Q(ie)},onClose:S.close})});return{Component:k,menuRef:_,menuProps:z,classNames:I,closeOnSelect:b,onClose:S.close,autoFocus:S.focusStrategy||!0,disableAnimation:x,getPopoverProps:y,getMenuProps:F,getMenuTriggerProps:m}}var zt=e=>{const{children:t,...r}=e,n=qo(r),[o,a]=G.Children.toArray(t);return i.jsx($n,{value:n,children:i.jsxs(vr,{...n.getPopoverProps(),children:[o,a]})})};zt.displayName="HeroUI.Dropdown";var zo=zt;const Bo=e=>{const{onOpen:t}=e;return i.jsxs(zo,{classNames:{content:"bg-opacity-30 backdrop-blur-md"},placement:"right",children:[i.jsx(Ko,{children:i.jsx(ee,{color:"primary",startContent:i.jsx(Zr,{className:"text-2xl"}),children:"新建"})}),i.jsxs(Ao,{"aria-label":"Create Network Config",color:"primary",variant:"flat",onAction:r=>{t(r)},children:[i.jsx(he,{isReadOnly:!0,className:"cursor-default hover:!bg-transparent",textValue:"title",children:i.jsxs("div",{className:"flex items-center gap-2 justify-center",children:[i.jsx("div",{className:"w-5 h-5 -ml-3",children:i.jsx(hr,{})}),i.jsx("div",{className:"text-primary-400",children:"新建网络配置"})]})},"title"),i.jsx(he,{textValue:"httpServers",startContent:i.jsx("div",{className:"w-6 h-6",children:i.jsx(at,{})}),children:i.jsxs("div",{className:"flex gap-1 items-center",children:["HTTP服务器",i.jsx($e,{content:"「由NapCat建立」一个HTTP服务器，你可以「使用框架连接」此服务器或者「自己构造请求发送」至此服务器。NapCat会根据你配置的IP和端口等建立一个地址，你或者你的框架应该连接到这个地址。",showArrow:!0,className:"max-w-64",children:i.jsx(ee,{isIconOnly:!0,radius:"full",size:"sm",variant:"light",className:"w-4 h-4 min-w-0",children:i.jsx(we,{})})})]})},"httpServers"),i.jsx(he,{textValue:"httpSseServers",startContent:i.jsx("div",{className:"w-6 h-6",children:i.jsx(at,{})}),children:i.jsxs("div",{className:"flex gap-1 items-center",children:["HTTP SSE服务器",i.jsx($e,{content:"「由NapCat建立」一个HTTP SSE服务器，你可以「使用框架连接」此服务器或者「自己构造请求发送」至此服务器。NapCat会根据你配置的IP和端口等建立一个地址，你或者你的框架应该连接到这个地址。",showArrow:!0,className:"max-w-64",children:i.jsx(ee,{isIconOnly:!0,radius:"full",size:"sm",variant:"light",className:"w-4 h-4 min-w-0",children:i.jsx(we,{})})})]})},"httpSseServers"),i.jsx(he,{textValue:"httpClients",startContent:i.jsx("div",{className:"w-6 h-6",children:i.jsx(mr,{})}),children:i.jsxs("div",{className:"flex gap-1 items-center",children:["HTTP客户端",i.jsx($e,{content:"「由框架或者你自己建立」的一个用于「接收」NapCat向你发送请求的客户端，通常框架会提供一个HTTP地址。这个地址是你使用的框架提供的，NapCat会主动连接它。",showArrow:!0,className:"max-w-64",children:i.jsx(ee,{isIconOnly:!0,radius:"full",size:"sm",variant:"light",className:"w-4 h-4 min-w-0",children:i.jsx(we,{})})})]})},"httpClients"),i.jsx(he,{textValue:"websocketServers",startContent:i.jsx("div",{className:"w-6 h-6",children:i.jsx(yr,{})}),children:i.jsxs("div",{className:"flex gap-1 items-center",children:["Websocket服务器",i.jsx($e,{content:"「由NapCat建立」一个WebSocket服务器，你的框架应该连接到此服务器。NapCat会根据你配置的IP和端口等建立一个WebSocket地址，你或者你的框架应该连接到这个地址。",showArrow:!0,className:"max-w-64",children:i.jsx(ee,{isIconOnly:!0,radius:"full",size:"sm",variant:"light",className:"w-4 h-4 min-w-0",children:i.jsx(we,{})})})]})},"websocketServers"),i.jsx(he,{textValue:"websocketClients",startContent:i.jsx("div",{className:"w-6 h-6",children:i.jsx(gr,{})}),children:i.jsxs("div",{className:"flex gap-1 items-center",children:["Websocket客户端",i.jsx($e,{content:"「由框架或者你自己建立」的WebSocket，通常框架会「提供」一个ws地址，NapCat会主动连接它。",showArrow:!0,className:"max-w-64",children:i.jsx(ee,{isIconOnly:!0,radius:"full",size:"sm",variant:"light",className:"w-4 h-4 min-w-0",children:i.jsx(we,{})})})]})},"websocketClients")]})]})};function Go(e){return en({attr:{viewBox:"0 0 24 24",fill:"none"},child:[{tag:"path",attr:{d:"M10 11C10 10.4477 10.4477 10 11 10H13C13.5523 10 14 10.4477 14 11C14 11.5523 13.5523 12 13 12H11C10.4477 12 10 11.5523 10 11Z",fill:"currentColor"},child:[]},{tag:"path",attr:{d:"M11 14C10.4477 14 10 14.4477 10 15C10 15.5523 10.4477 16 11 16H13C13.5523 16 14 15.5523 14 15C14 14.4477 13.5523 14 13 14H11Z",fill:"currentColor"},child:[]},{tag:"path",attr:{fillRule:"evenodd",clipRule:"evenodd",d:"M9.09447 4.74918C8.41606 4.03243 8 3.0648 8 2H10C10 3.10457 10.8954 4 12 4C13.1046 4 14 3.10457 14 2H16C16 3.0648 15.5839 4.03243 14.9055 4.74918C16.1782 5.45491 17.1673 6.6099 17.6586 8H19C19.5523 8 20 8.44772 20 9C20 9.55229 19.5523 10 19 10H18V12H19C19.5523 12 20 12.4477 20 13C20 13.5523 19.5523 14 19 14H18V16H19C19.5523 16 20 16.4477 20 17C20 17.5523 19.5523 18 19 18H17.6586C16.8349 20.3304 14.6124 22 12 22C9.38756 22 7.16508 20.3304 6.34141 18H5C4.44772 18 4 17.5523 4 17C4 16.4477 4.44772 16 5 16H6V14H5C4.44772 14 4 13.5523 4 13C4 12.4477 4.44772 12 5 12H6V10H5C4.44772 10 4 9.55229 4 9C4 8.44772 4.44772 8 5 8H6.34141C6.83274 6.6099 7.82181 5.45491 9.09447 4.74918ZM8 16V10C8 7.79086 9.79086 6 12 6C14.2091 6 16 7.79086 16 10V16C16 18.2091 14.2091 20 12 20C9.79086 20 8 18.2091 8 16Z",fill:"currentColor"},child:[]}]})(e)}const Zo=({title:e,action:t,tag:r,enableSwitch:n,children:o})=>i.jsxs($r,{className:"bg-opacity-50 backdrop-blur-sm",children:[i.jsxs(wr,{className:"pb-0 flex items-center",children:[r&&i.jsx("div",{className:"text-center text-default-400 mb-1 absolute top-0 left-1/2 -translate-x-1/2 text-sm pointer-events-none bg-warning-100 dark:bg-warning-50 px-2 rounded-b",children:r}),i.jsx("h2",{className:Re(rn({color:"foreground",size:"xs",shadow:!0}),"truncate"),children:e}),i.jsx("div",{className:"ml-auto",children:n})]}),i.jsx(xr,{className:"text-sm",children:o}),i.jsx(dn,{children:t})]}),je=({data:e,showType:t,typeLabel:r,fields:n,onEdit:o,onEnable:a,onDelete:s,onEnableDebug:l})=>{const{name:f,enable:u,debug:d}=e,[h,b]=$.useState(!1),p=()=>{b(!0),a().finally(()=>b(!1))},g=()=>{b(!0),s().finally(()=>b(!1))},x=()=>{b(!0),l().finally(()=>b(!1))};return i.jsx(Zo,{action:i.jsxs(Jr,{fullWidth:!0,isDisabled:h,radius:"full",size:"sm",variant:"shadow",children:[i.jsx(ee,{color:"warning",startContent:i.jsx(Qr,{}),onPress:o,children:"编辑"}),i.jsx(ee,{color:d?"success":"default",startContent:i.jsx(Go,{}),onPress:x,children:d?"关闭调试":"开启调试"}),i.jsx(ee,{color:"primary",startContent:i.jsx(tn,{}),onPress:g,children:"删除"})]}),enableSwitch:i.jsx(Yr,{isDisabled:h,isSelected:u,onChange:p}),tag:t&&r,title:f,children:i.jsx("div",{className:"grid grid-cols-2 gap-1",children:n.map((w,C)=>i.jsxs("div",{className:`flex items-center gap-2 ${w.label==="URL"?"col-span-2":""}`,children:[i.jsx("span",{className:"text-default-400",children:w.label}),w.render?w.render(w.value):i.jsx("span",{children:w.value})]},C))})})},Jo=e=>{const{data:t,showType:r,onEdit:n,onEnable:o,onDelete:a,onEnableDebug:s}=e,{url:l,reportSelfMessage:f,messagePostFormat:u}=t,d=[{label:"URL",value:l},{label:"消息格式",value:u},{label:"上报自身消息",value:f,render:h=>i.jsx(ue,{color:h?"success":"default",size:"sm",variant:"flat",children:h?"是":"否"})}];return i.jsx(je,{data:t,showType:r,typeLabel:"HTTP客户端",fields:d,onEdit:n,onEnable:o,onDelete:a,onEnableDebug:s})},Qo=e=>{const{data:t,showType:r,onEdit:n,onEnable:o,onDelete:a,onEnableDebug:s}=e,{host:l,port:f,enableCors:u,enableWebsocket:d,messagePostFormat:h}=t,b=[{label:"主机",value:l},{label:"端口",value:f},{label:"消息格式",value:h},{label:"CORS",value:u,render:p=>i.jsx(ue,{color:p?"success":"default",size:"sm",variant:"flat",children:p?"已启用":"未启用"})},{label:"WS",value:d,render:p=>i.jsx(ue,{color:p?"success":"default",size:"sm",variant:"flat",children:p?"已启用":"未启用"})}];return i.jsx(je,{data:t,showType:r,typeLabel:"HTTP服务器",fields:b,onEdit:n,onEnable:o,onDelete:a,onEnableDebug:s})},Yo=e=>{const{data:t,showType:r,onEdit:n,onEnable:o,onDelete:a,onEnableDebug:s}=e,{host:l,port:f,enableCors:u,enableWebsocket:d,messagePostFormat:h}=t,b=[{label:"主机",value:l},{label:"端口",value:f},{label:"消息格式",value:h},{label:"CORS",value:u,render:p=>i.jsx(ue,{color:p?"success":"default",size:"sm",variant:"flat",children:p?"已启用":"未启用"})},{label:"WS",value:d,render:p=>i.jsx(ue,{color:p?"success":"default",size:"sm",variant:"flat",children:p?"已启用":"未启用"})}];return i.jsx(je,{data:t,showType:r,typeLabel:"HTTP服务器",fields:b,onEdit:n,onEnable:o,onDelete:a,onEnableDebug:s})},Xo=e=>{const{data:t,showType:r,onEdit:n,onEnable:o,onDelete:a,onEnableDebug:s}=e,{url:l,heartInterval:f,reconnectInterval:u,messagePostFormat:d,reportSelfMessage:h}=t,b=[{label:"URL",value:l},{label:"重连间隔",value:`${u}ms`},{label:"心跳间隔",value:`${f}ms`},{label:"消息格式",value:d},{label:"上报自身消息",value:h,render:p=>i.jsx(ue,{color:p?"success":"default",size:"sm",variant:"flat",children:p?"是":"否"})}];return i.jsx(je,{data:t,showType:r,typeLabel:"Websocket客户端",fields:b,onEdit:n,onEnable:o,onDelete:a,onEnableDebug:s})},ea=e=>{const{data:t,showType:r,onEdit:n,onEnable:o,onDelete:a,onEnableDebug:s}=e,{host:l,port:f,heartInterval:u,messagePostFormat:d,reportSelfMessage:h,enableForcePushEvent:b}=t,p=[{label:"主机",value:l},{label:"端口",value:f},{label:"心跳间隔",value:`${u}ms`},{label:"消息格式",value:d},{label:"上报自身消息",value:h,render:g=>i.jsx(ue,{color:g?"success":"default",size:"sm",variant:"flat",children:g?"是":"否"})},{label:"强制推送事件",value:b,render:g=>i.jsx(ue,{color:g?"success":"default",size:"sm",variant:"flat",children:g?"是":"否"})}];return i.jsx(je,{data:t,showType:r,typeLabel:"Websocket服务器",fields:p,onEdit:n,onEnable:o,onDelete:a,onEnableDebug:s})},Ne=({data:e,defaultValues:t,onClose:r,onSubmit:n,fields:o})=>{const{control:a,handleSubmit:s,formState:l,setValue:f,reset:u}=on({defaultValues:t}),h=s(async b=>{await n(b),r()},b=>{var p;for(const g in b){ne.error((p=b[g])==null?void 0:p.message);return}});return $.useEffect(()=>{if(e){const b=Object.keys(e);for(const p of b){const g=e[p];f(p,g)}}else u()},[e,u,f]),i.jsxs(i.Fragment,{children:[i.jsx(Er,{children:i.jsx("div",{className:"grid grid-cols-2 gap-y-4 gap-x-2 w-full",children:o.map(b=>i.jsx("div",{className:b.colSpan===1?"col-span-1":"col-span-2",children:i.jsx(an,{control:a,name:b.name,rules:b.isRequired?{required:`请填写${b.label}`}:void 0,render:({field:p})=>{var g;switch(b.type){case"input":return i.jsx(nn,{value:p.value,onChange:p.onChange,onBlur:p.onBlur,ref:p.ref,isRequired:b.isRequired,isDisabled:b.isDisabled,label:b.label,placeholder:b.placeholder});case"select":return i.jsx(Gr,{...p,ref:p.ref,isRequired:b.isRequired,label:b.label,placeholder:b.placeholder,selectedKeys:[p.value],value:p.value.toString(),children:((g=b.options)==null?void 0:g.map(x=>i.jsx(Rr,{value:x.value,children:x.value},x.key)))||i.jsx(i.Fragment,{})});case"switch":return i.jsx(Xr,{...p,value:p.value,description:b.description,label:b.label});default:return i.jsx(i.Fragment,{})}}})},b.name))})}),i.jsxs(Sr,{children:[i.jsx(ee,{color:"primary",isDisabled:l.isSubmitting,variant:"light",onPress:r,children:"关闭"}),i.jsx(ee,{color:"primary",isLoading:l.isSubmitting,onPress:()=>h(),children:"保存"})]})]})},ta=({data:e,onClose:t,onSubmit:r})=>{const n={enable:!1,name:"",url:"http://localhost:8080",reportSelfMessage:!1,messagePostFormat:"array",token:"",debug:!1},o=[{name:"enable",label:"启用",type:"switch",description:"保存后启用此配置",colSpan:1},{name:"debug",label:"开启Debug",type:"switch",description:"是否开启调试模式",colSpan:1},{name:"name",label:"名称",type:"input",placeholder:"请输入名称",isRequired:!0,isDisabled:!!e},{name:"url",label:"URL",type:"input",placeholder:"请输入URL",isRequired:!0},{name:"reportSelfMessage",label:"上报自身消息",type:"switch",description:"是否上报自身消息",colSpan:1},{name:"messagePostFormat",label:"消息格式",type:"select",placeholder:"请选择消息格式",isRequired:!0,options:[{key:"array",value:"Array"},{key:"string",value:"String"}],colSpan:1},{name:"token",label:"Token",type:"input",placeholder:"请输入Token"}];return i.jsx(Ne,{data:e,defaultValues:n,onClose:t,onSubmit:r,fields:o})},ra=({data:e,onClose:t,onSubmit:r})=>{const n={enable:!1,name:"",host:"0.0.0.0",port:3e3,enableCors:!0,enableWebsocket:!0,messagePostFormat:"array",token:"",debug:!1},o=[{name:"enable",label:"启用",type:"switch",description:"保存后启用此配置",colSpan:1},{name:"debug",label:"开启Debug",type:"switch",description:"是否开启调试模式",colSpan:1},{name:"name",label:"名称",type:"input",placeholder:"请输入名称",isRequired:!0,isDisabled:!!e},{name:"host",label:"Host",type:"input",placeholder:"请输入主机地址",isRequired:!0},{name:"port",label:"Port",type:"input",placeholder:"请输入端口",isRequired:!0},{name:"enableCors",label:"启用CORS",type:"switch",description:"是否启用CORS跨域",colSpan:1},{name:"enableWebsocket",label:"启用Websocket",type:"switch",description:"是否启用Websocket",colSpan:1},{name:"messagePostFormat",label:"消息格式",type:"select",placeholder:"请选择消息格式",isRequired:!0,options:[{key:"array",value:"Array"},{key:"string",value:"String"}]},{name:"token",label:"Token",type:"input",placeholder:"请输入Token"}];return i.jsx(Ne,{data:e,defaultValues:n,onClose:t,onSubmit:r,fields:o})},na=({data:e,onClose:t,onSubmit:r})=>{const n={enable:!1,name:"",host:"0.0.0.0",port:3e3,enableCors:!0,enableWebsocket:!0,messagePostFormat:"array",token:"",debug:!1,reportSelfMessage:!1},o=[{name:"enable",label:"启用",type:"switch",description:"保存后启用此配置",colSpan:1},{name:"debug",label:"开启Debug",type:"switch",description:"是否开启调试模式",colSpan:1},{name:"name",label:"名称",type:"input",placeholder:"请输入名称",isRequired:!0,isDisabled:!!e},{name:"host",label:"Host",type:"input",placeholder:"请输入主机地址",isRequired:!0},{name:"port",label:"Port",type:"input",placeholder:"请输入端口",isRequired:!0},{name:"enableCors",label:"启用CORS",type:"switch",description:"是否启用CORS跨域",colSpan:1},{name:"enableWebsocket",label:"启用Websocket",type:"switch",description:"是否启用Websocket",colSpan:1},{name:"messagePostFormat",label:"消息格式",type:"select",placeholder:"请选择消息格式",isRequired:!0,options:[{key:"array",value:"Array"},{key:"string",value:"String"}]},{name:"token",label:"Token",type:"input",placeholder:"请输入Token"},{name:"reportSelfMessage",label:"上报自身消息",type:"switch",description:"是否上报自身消息",colSpan:1}];return i.jsx(Ne,{data:e,defaultValues:n,onClose:t,onSubmit:r,fields:o})},oa=({data:e,onClose:t,onSubmit:r})=>{const n={enable:!1,name:"",url:"ws://localhost:8082",reportSelfMessage:!1,messagePostFormat:"array",token:"",debug:!1,heartInterval:3e4,reconnectInterval:3e4},o=[{name:"enable",label:"启用",type:"switch",description:"保存后启用此配置",colSpan:1},{name:"debug",label:"开启Debug",type:"switch",description:"是否开启调试模式",colSpan:1},{name:"name",label:"名称",type:"input",placeholder:"请输入名称",isRequired:!0,isDisabled:!!e},{name:"url",label:"URL",type:"input",placeholder:"请输入URL",isRequired:!0},{name:"reportSelfMessage",label:"上报自身消息",type:"switch",description:"是否上报自身消息",colSpan:1},{name:"messagePostFormat",label:"消息格式",type:"select",placeholder:"请选择消息格式",isRequired:!0,options:[{key:"array",value:"Array"},{key:"string",value:"String"}],colSpan:1},{name:"token",label:"Token",type:"input",placeholder:"请输入Token"},{name:"heartInterval",label:"心跳间隔",type:"input",placeholder:"请输入心跳间隔",isRequired:!0,colSpan:1},{name:"reconnectInterval",label:"重连间隔",type:"input",placeholder:"请输入重连间隔",isRequired:!0,colSpan:1}];return i.jsx(Ne,{data:e,defaultValues:n,onClose:t,onSubmit:r,fields:o})},aa=({data:e,onClose:t,onSubmit:r})=>{const n={enable:!1,name:"",host:"0.0.0.0",port:3001,reportSelfMessage:!1,enableForcePushEvent:!0,messagePostFormat:"array",token:"",debug:!1,heartInterval:3e4},o=[{name:"enable",label:"启用",type:"switch",description:"保存后启用此配置",colSpan:1},{name:"debug",label:"开启Debug",type:"switch",description:"是否开启调试模式",colSpan:1},{name:"name",label:"名称",type:"input",placeholder:"请输入名称",isRequired:!0,isDisabled:!!e},{name:"host",label:"Host",type:"input",placeholder:"请输入主机地址",isRequired:!0},{name:"port",label:"Port",type:"input",placeholder:"请输入端口",isRequired:!0,colSpan:1},{name:"messagePostFormat",label:"消息格式",type:"select",placeholder:"请选择消息格式",isRequired:!0,options:[{key:"array",value:"Array"},{key:"string",value:"String"}],colSpan:1},{name:"reportSelfMessage",label:"上报自身消息",type:"switch",description:"是否上报自身消息",colSpan:1},{name:"enableForcePushEvent",label:"强制推送事件",type:"switch",description:"是否强制推送事件",colSpan:1},{name:"token",label:"Token",type:"input",placeholder:"请输入Token"},{name:"heartInterval",label:"心跳间隔",type:"input",placeholder:"请输入心跳间隔",isRequired:!0}];return i.jsx(Ne,{data:e,defaultValues:n,onClose:t,onSubmit:r,fields:o})},sa={httpServers:"HTTP Server",httpClients:"HTTP Client",httpSseServers:"HTTP SSE Server",websocketServers:"Websocket Server",websocketClients:"Websocket Client"},la=e=>{const{isOpen:t,onOpenChange:r,field:n,data:o}=e,{createNetworkConfig:a,updateNetworkConfig:s}=$t(),l=!o,f=async d=>{try{l?await a(n,d):await s(n,d),ne.success("保存配置成功")}catch(h){const b=h.message;throw ne.error(`保存配置失败: ${b}`),h}},u=d=>{switch(n){case"httpServers":return i.jsx(ra,{data:o,onClose:d,onSubmit:f});case"httpClients":return i.jsx(ta,{data:o,onClose:d,onSubmit:f});case"websocketServers":return i.jsx(aa,{data:o,onClose:d,onSubmit:f});case"websocketClients":return i.jsx(oa,{data:o,onClose:d,onSubmit:f});case"httpSseServers":return i.jsx(na,{data:o,onClose:d,onSubmit:f});default:return null}};return i.jsx(Cr,{backdrop:"blur",isDismissable:!1,isOpen:t,size:"lg",scrollBehavior:"outside",onOpenChange:r,children:i.jsx(kr,{children:d=>i.jsxs(i.Fragment,{children:[i.jsx(Pr,{className:"flex flex-col gap-1",children:sa[n]}),u(d)]})})})},ia=({isEmpty:e})=>i.jsx("div",{className:Re("text-default-400",{hidden:!e}),children:"暂时还没有配置项哈"});function Ra(){const{config:e,refreshConfig:t,deleteNetworkConfig:r,enableNetworkConfig:n,enableDebugNetworkConfig:o}=$t(),[a,s]=$.useState("httpServers"),[l,f]=$.useState(""),{network:{httpServers:u,httpClients:d,httpSseServers:h,websocketServers:b,websocketClients:p}}=e,[g,x]=$.useState(!1),{isOpen:w,onOpen:C,onOpenChange:D}=Tr(),k=sn(),M=$.useMemo(()=>e.network[a].find(m=>m.name===l),[a,l,e]),E=async()=>{x(!0);try{await t(),x(!1)}catch(y){const m=y.message;ne.error(`获取配置失败: ${m}`)}finally{x(!1)}},_=y=>{s(y),f(""),C()},O=async(y,m)=>new Promise((F,N)=>{k.confirm({title:"删除配置",content:`确定要删除配置「${m}」吗?`,onConfirm:async()=>{try{await r(y,m),ne.success("删除配置成功"),F()}catch(U){const P=U.message;ne.error(`删除配置失败: ${P}`),N(U)}},onCancel:()=>{F()}})}),S=async(y,m)=>{try{await n(y,m),ne.success("更新配置成功")}catch(F){const N=F.message;throw ne.error(`更新配置失败: ${N}`),F}},V=async(y,m)=>{try{await o(y,m),ne.success("更新配置成功")}catch(F){const N=F.message;throw ne.error(`更新配置失败: ${N}`),F}},z=(y,m)=>{s(y),f(m),C()},I=(y,m,F=!1)=>{switch(y){case"httpServers":return i.jsx(Qo,{showType:F,data:m,onDelete:async()=>{await O("httpServers",m.name)},onEdit:()=>{z("httpServers",m.name)},onEnable:async()=>{await S("httpServers",m.name)},onEnableDebug:async()=>{await V("httpServers",m.name)}},m.name);case"httpClients":return i.jsx(Jo,{showType:F,data:m,onDelete:async()=>{await O("httpClients",m.name)},onEdit:()=>{z("httpClients",m.name)},onEnable:async()=>{await S("httpClients",m.name)},onEnableDebug:async()=>{await V("httpClients",m.name)}},m.name);case"httpSseServers":return i.jsx(Yo,{showType:F,data:m,onDelete:async()=>{await O("httpSseServers",m.name)},onEdit:()=>{z("httpSseServers",m.name)},onEnable:async()=>{await S("httpSseServers",m.name)},onEnableDebug:async()=>{await V("httpSseServers",m.name)}},m.name);case"websocketServers":return i.jsx(ea,{showType:F,data:m,onDelete:async()=>{await O("websocketServers",m.name)},onEdit:()=>{z("websocketServers",m.name)},onEnable:async()=>{await S("websocketServers",m.name)},onEnableDebug:async()=>{await V("websocketServers",m.name)}},m.name);case"websocketClients":return i.jsx(Xo,{showType:F,data:m,onDelete:async()=>{await O("websocketClients",m.name)},onEdit:()=>{z("websocketClients",m.name)},onEnable:async()=>{await S("websocketClients",m.name)},onEnableDebug:async()=>{await V("websocketClients",m.name)}},m.name)}},Q=[{key:"all",title:"全部",items:[...u,...d,...b,...p,...h].sort((y,m)=>y.name.localeCompare(m.name)).map(y=>u.find(m=>m.name===y.name)?I("httpServers",y,!0):h.find(m=>m.name===y.name)?I("httpSseServers",y,!0):d.find(m=>m.name===y.name)?I("httpClients",y,!0):b.find(m=>m.name===y.name)?I("websocketServers",y,!0):p.find(m=>m.name===y.name)?I("websocketClients",y,!0):null)},{key:"httpServers",title:"HTTP服务器",items:u.map(y=>I("httpServers",y))},{key:"httpClients",title:"HTTP客户端",items:d.map(y=>I("httpClients",y))},{key:"httpSseServers",title:"HTTP SSE服务器",items:h.map(y=>I("httpSseServers",y))},{key:"websocketServers",title:"Websocket服务器",items:b.map(y=>I("websocketServers",y))},{key:"websocketClients",title:"Websocket客户端",items:p.map(y=>I("websocketClients",y))}];return $.useEffect(()=>{E()},[]),i.jsxs(i.Fragment,{children:[i.jsx("title",{children:"网络配置 - NapCat WebUI"}),i.jsxs("div",{className:"p-2 md:p-4 relative",children:[i.jsx(la,{data:M,field:a,isOpen:w,onOpenChange:D}),i.jsx(jr,{loading:g}),i.jsxs("div",{className:"flex mb-6 items-center gap-4",children:[i.jsx(Bo,{onOpen:_}),i.jsx(ee,{isIconOnly:!0,color:"primary",radius:"full",variant:"flat",onPress:E,children:i.jsx(Dr,{size:24})})]}),i.jsx(Nr,{"aria-label":"Network Configs",className:"max-w-full",items:Q,classNames:{tabList:"bg-opacity-50 backdrop-blur-sm",cursor:"bg-opacity-60 backdrop-blur-sm"},children:y=>i.jsxs(Mr,{title:y.title,children:[i.jsx(ia,{isEmpty:!y.items.length}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 justify-start items-stretch gap-x-2 gap-y-4",children:y.items})]},y.key)})]})]})}export{Ra as default};
