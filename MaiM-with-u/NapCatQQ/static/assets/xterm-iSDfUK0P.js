import{e as Fe,j as Re,d as We}from"./index-D8VBA_Ei.js";import{r as fe}from"./react-router-dom-Bk_r5m4S.js";var be={exports:{}},Me;function $e(){return Me||(Me=1,function(de,ve){(function(ae,Q){de.exports=Q()})(self,()=>(()=>{var ae={903:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BaseRenderLayer=void 0;const _=a(274),d=a(627),n=a(237),c=a(860),f=a(374),m=a(296),o=a(345),e=a(859),r=a(399),t=a(855);class i extends e.Disposable{get canvas(){return this._canvas}get cacheCanvas(){var l;return(l=this._charAtlas)==null?void 0:l.pages[0].canvas}constructor(l,g,u,p,C,b,y,w,k,D){super(),this._terminal=l,this._container=g,this._alpha=C,this._themeService=b,this._bufferService=y,this._optionsService=w,this._decorationService=k,this._coreBrowserService=D,this._deviceCharWidth=0,this._deviceCharHeight=0,this._deviceCellWidth=0,this._deviceCellHeight=0,this._deviceCharLeft=0,this._deviceCharTop=0,this._selectionModel=(0,m.createSelectionRenderModel)(),this._bitmapGenerator=[],this._charAtlasDisposable=this.register(new e.MutableDisposable),this._onAddTextureAtlasCanvas=this.register(new o.EventEmitter),this.onAddTextureAtlasCanvas=this._onAddTextureAtlasCanvas.event,this._cellColorResolver=new _.CellColorResolver(this._terminal,this._optionsService,this._selectionModel,this._decorationService,this._coreBrowserService,this._themeService),this._canvas=this._coreBrowserService.mainDocument.createElement("canvas"),this._canvas.classList.add(`xterm-${u}-layer`),this._canvas.style.zIndex=p.toString(),this._initCanvas(),this._container.appendChild(this._canvas),this._refreshCharAtlas(this._themeService.colors),this.register(this._themeService.onChangeColors(P=>{this._refreshCharAtlas(P),this.reset(),this.handleSelectionChanged(this._selectionModel.selectionStart,this._selectionModel.selectionEnd,this._selectionModel.columnSelectMode)})),this.register((0,e.toDisposable)(()=>{this._canvas.remove()}))}_initCanvas(){this._ctx=(0,f.throwIfFalsy)(this._canvas.getContext("2d",{alpha:this._alpha})),this._alpha||this._clearAll()}handleBlur(){}handleFocus(){}handleCursorMove(){}handleGridChanged(l,g){}handleSelectionChanged(l,g,u=!1){this._selectionModel.update(this._terminal._core,l,g,u)}_setTransparency(l){if(l===this._alpha)return;const g=this._canvas;this._alpha=l,this._canvas=this._canvas.cloneNode(),this._initCanvas(),this._container.replaceChild(this._canvas,g),this._refreshCharAtlas(this._themeService.colors),this.handleGridChanged(0,this._bufferService.rows-1)}_refreshCharAtlas(l){if(!(this._deviceCharWidth<=0&&this._deviceCharHeight<=0)){this._charAtlas=(0,d.acquireTextureAtlas)(this._terminal,this._optionsService.rawOptions,l,this._deviceCellWidth,this._deviceCellHeight,this._deviceCharWidth,this._deviceCharHeight,this._coreBrowserService.dpr),this._charAtlasDisposable.value=(0,o.forwardEvent)(this._charAtlas.onAddTextureAtlasCanvas,this._onAddTextureAtlasCanvas),this._charAtlas.warmUp();for(let g=0;g<this._charAtlas.pages.length;g++)this._bitmapGenerator[g]=new h(this._charAtlas.pages[g].canvas)}}resize(l){this._deviceCellWidth=l.device.cell.width,this._deviceCellHeight=l.device.cell.height,this._deviceCharWidth=l.device.char.width,this._deviceCharHeight=l.device.char.height,this._deviceCharLeft=l.device.char.left,this._deviceCharTop=l.device.char.top,this._canvas.width=l.device.canvas.width,this._canvas.height=l.device.canvas.height,this._canvas.style.width=`${l.css.canvas.width}px`,this._canvas.style.height=`${l.css.canvas.height}px`,this._alpha||this._clearAll(),this._refreshCharAtlas(this._themeService.colors)}clearTextureAtlas(){var l;(l=this._charAtlas)==null||l.clearTexture()}_fillCells(l,g,u,p){this._ctx.fillRect(l*this._deviceCellWidth,g*this._deviceCellHeight,u*this._deviceCellWidth,p*this._deviceCellHeight)}_fillMiddleLineAtCells(l,g,u=1){const p=Math.ceil(.5*this._deviceCellHeight);this._ctx.fillRect(l*this._deviceCellWidth,(g+1)*this._deviceCellHeight-p-this._coreBrowserService.dpr,u*this._deviceCellWidth,this._coreBrowserService.dpr)}_fillBottomLineAtCells(l,g,u=1,p=0){this._ctx.fillRect(l*this._deviceCellWidth,(g+1)*this._deviceCellHeight+p-this._coreBrowserService.dpr-1,u*this._deviceCellWidth,this._coreBrowserService.dpr)}_curlyUnderlineAtCell(l,g,u=1){this._ctx.save(),this._ctx.beginPath(),this._ctx.strokeStyle=this._ctx.fillStyle;const p=this._coreBrowserService.dpr;this._ctx.lineWidth=p;for(let C=0;C<u;C++){const b=(l+C)*this._deviceCellWidth,y=(l+C+.5)*this._deviceCellWidth,w=(l+C+1)*this._deviceCellWidth,k=(g+1)*this._deviceCellHeight-p-1,D=k-p,P=k+p;this._ctx.moveTo(b,k),this._ctx.bezierCurveTo(b,D,y,D,y,k),this._ctx.bezierCurveTo(y,P,w,P,w,k)}this._ctx.stroke(),this._ctx.restore()}_dottedUnderlineAtCell(l,g,u=1){this._ctx.save(),this._ctx.beginPath(),this._ctx.strokeStyle=this._ctx.fillStyle;const p=this._coreBrowserService.dpr;this._ctx.lineWidth=p,this._ctx.setLineDash([2*p,p]);const C=l*this._deviceCellWidth,b=(g+1)*this._deviceCellHeight-p-1;this._ctx.moveTo(C,b);for(let y=0;y<u;y++){const w=(l+u+y)*this._deviceCellWidth;this._ctx.lineTo(w,b)}this._ctx.stroke(),this._ctx.closePath(),this._ctx.restore()}_dashedUnderlineAtCell(l,g,u=1){this._ctx.save(),this._ctx.beginPath(),this._ctx.strokeStyle=this._ctx.fillStyle;const p=this._coreBrowserService.dpr;this._ctx.lineWidth=p,this._ctx.setLineDash([4*p,3*p]);const C=l*this._deviceCellWidth,b=(l+u)*this._deviceCellWidth,y=(g+1)*this._deviceCellHeight-p-1;this._ctx.moveTo(C,y),this._ctx.lineTo(b,y),this._ctx.stroke(),this._ctx.closePath(),this._ctx.restore()}_fillLeftLineAtCell(l,g,u){this._ctx.fillRect(l*this._deviceCellWidth,g*this._deviceCellHeight,this._coreBrowserService.dpr*u,this._deviceCellHeight)}_strokeRectAtCell(l,g,u,p){const C=this._coreBrowserService.dpr;this._ctx.lineWidth=C,this._ctx.strokeRect(l*this._deviceCellWidth+C/2,g*this._deviceCellHeight+C/2,u*this._deviceCellWidth-C,p*this._deviceCellHeight-C)}_clearAll(){this._alpha?this._ctx.clearRect(0,0,this._canvas.width,this._canvas.height):(this._ctx.fillStyle=this._themeService.colors.background.css,this._ctx.fillRect(0,0,this._canvas.width,this._canvas.height))}_clearCells(l,g,u,p){this._alpha?this._ctx.clearRect(l*this._deviceCellWidth,g*this._deviceCellHeight,u*this._deviceCellWidth,p*this._deviceCellHeight):(this._ctx.fillStyle=this._themeService.colors.background.css,this._ctx.fillRect(l*this._deviceCellWidth,g*this._deviceCellHeight,u*this._deviceCellWidth,p*this._deviceCellHeight))}_fillCharTrueColor(l,g,u){this._ctx.font=this._getFont(!1,!1),this._ctx.textBaseline=n.TEXT_BASELINE,this._clipRow(u);let p=!1;this._optionsService.rawOptions.customGlyphs!==!1&&(p=(0,c.tryDrawCustomChar)(this._ctx,l.getChars(),g*this._deviceCellWidth,u*this._deviceCellHeight,this._deviceCellWidth,this._deviceCellHeight,this._optionsService.rawOptions.fontSize,this._coreBrowserService.dpr)),p||this._ctx.fillText(l.getChars(),g*this._deviceCellWidth+this._deviceCharLeft,u*this._deviceCellHeight+this._deviceCharTop+this._deviceCharHeight)}_drawChars(l,g,u){var k,D,P,T;const p=l.getChars(),C=l.getCode(),b=l.getWidth();if(this._cellColorResolver.resolve(l,g,this._bufferService.buffer.ydisp+u,this._deviceCellWidth),!this._charAtlas)return;let y;if(y=p&&p.length>1?this._charAtlas.getRasterizedGlyphCombinedChar(p,this._cellColorResolver.result.bg,this._cellColorResolver.result.fg,this._cellColorResolver.result.ext,!0):this._charAtlas.getRasterizedGlyph(l.getCode()||t.WHITESPACE_CELL_CODE,this._cellColorResolver.result.bg,this._cellColorResolver.result.fg,this._cellColorResolver.result.ext,!0),!y.size.x||!y.size.y)return;this._ctx.save(),this._clipRow(u),this._bitmapGenerator[y.texturePage]&&this._charAtlas.pages[y.texturePage].canvas!==this._bitmapGenerator[y.texturePage].canvas&&((D=(k=this._bitmapGenerator[y.texturePage])==null?void 0:k.bitmap)==null||D.close(),delete this._bitmapGenerator[y.texturePage]),this._charAtlas.pages[y.texturePage].version!==((P=this._bitmapGenerator[y.texturePage])==null?void 0:P.version)&&(this._bitmapGenerator[y.texturePage]||(this._bitmapGenerator[y.texturePage]=new h(this._charAtlas.pages[y.texturePage].canvas)),this._bitmapGenerator[y.texturePage].refresh(),this._bitmapGenerator[y.texturePage].version=this._charAtlas.pages[y.texturePage].version);let w=y.size.x;this._optionsService.rawOptions.rescaleOverlappingGlyphs&&(0,f.allowRescaling)(C,b,y.size.x,this._deviceCellWidth)&&(w=this._deviceCellWidth-1),this._ctx.drawImage(((T=this._bitmapGenerator[y.texturePage])==null?void 0:T.bitmap)||this._charAtlas.pages[y.texturePage].canvas,y.texturePosition.x,y.texturePosition.y,y.size.x,y.size.y,g*this._deviceCellWidth+this._deviceCharLeft-y.offset.x,u*this._deviceCellHeight+this._deviceCharTop-y.offset.y,w,y.size.y),this._ctx.restore()}_clipRow(l){this._ctx.beginPath(),this._ctx.rect(0,l*this._deviceCellHeight,this._bufferService.cols*this._deviceCellWidth,this._deviceCellHeight),this._ctx.clip()}_getFont(l,g){return`${g?"italic":""} ${l?this._optionsService.rawOptions.fontWeightBold:this._optionsService.rawOptions.fontWeight} ${this._optionsService.rawOptions.fontSize*this._coreBrowserService.dpr}px ${this._optionsService.rawOptions.fontFamily}`}}s.BaseRenderLayer=i;class h{get bitmap(){return this._bitmap}constructor(l){this.canvas=l,this._state=0,this._commitTimeout=void 0,this._bitmap=void 0,this.version=-1}refresh(){var l;(l=this._bitmap)==null||l.close(),this._bitmap=void 0,r.isSafari||(this._commitTimeout===void 0&&(this._commitTimeout=window.setTimeout(()=>this._generate(),100)),this._state===1&&(this._state=2))}_generate(){var l;this._state===0&&((l=this._bitmap)==null||l.close(),this._bitmap=void 0,this._state=1,window.createImageBitmap(this.canvas).then(g=>{this._state===2?this.refresh():this._bitmap=g,this._state=0}),this._commitTimeout&&(this._commitTimeout=void 0))}}},949:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CanvasRenderer=void 0;const _=a(627),d=a(56),n=a(374),c=a(345),f=a(859),m=a(873),o=a(43),e=a(630),r=a(744);class t extends f.Disposable{constructor(h,v,l,g,u,p,C,b,y,w,k){super(),this._terminal=h,this._screenElement=v,this._bufferService=g,this._charSizeService=u,this._optionsService=p,this._coreBrowserService=y,this._themeService=k,this._observerDisposable=this.register(new f.MutableDisposable),this._onRequestRedraw=this.register(new c.EventEmitter),this.onRequestRedraw=this._onRequestRedraw.event,this._onChangeTextureAtlas=this.register(new c.EventEmitter),this.onChangeTextureAtlas=this._onChangeTextureAtlas.event,this._onAddTextureAtlasCanvas=this.register(new c.EventEmitter),this.onAddTextureAtlasCanvas=this._onAddTextureAtlasCanvas.event;const D=this._optionsService.rawOptions.allowTransparency;this._renderLayers=[new r.TextRenderLayer(this._terminal,this._screenElement,0,D,this._bufferService,this._optionsService,C,w,this._coreBrowserService,k),new e.SelectionRenderLayer(this._terminal,this._screenElement,1,this._bufferService,this._coreBrowserService,w,this._optionsService,k),new o.LinkRenderLayer(this._terminal,this._screenElement,2,l,this._bufferService,this._optionsService,w,this._coreBrowserService,k),new m.CursorRenderLayer(this._terminal,this._screenElement,3,this._onRequestRedraw,this._bufferService,this._optionsService,b,this._coreBrowserService,w,k)];for(const P of this._renderLayers)(0,c.forwardEvent)(P.onAddTextureAtlasCanvas,this._onAddTextureAtlasCanvas);this.dimensions=(0,n.createRenderDimensions)(),this._devicePixelRatio=this._coreBrowserService.dpr,this._updateDimensions(),this._observerDisposable.value=(0,d.observeDevicePixelDimensions)(this._renderLayers[0].canvas,this._coreBrowserService.window,(P,T)=>this._setCanvasDevicePixelDimensions(P,T)),this.register(this._coreBrowserService.onWindowChange(P=>{this._observerDisposable.value=(0,d.observeDevicePixelDimensions)(this._renderLayers[0].canvas,P,(T,A)=>this._setCanvasDevicePixelDimensions(T,A))})),this.register((0,f.toDisposable)(()=>{for(const P of this._renderLayers)P.dispose();(0,_.removeTerminalFromCache)(this._terminal)}))}get textureAtlas(){return this._renderLayers[0].cacheCanvas}handleDevicePixelRatioChange(){this._devicePixelRatio!==this._coreBrowserService.dpr&&(this._devicePixelRatio=this._coreBrowserService.dpr,this.handleResize(this._bufferService.cols,this._bufferService.rows))}handleResize(h,v){this._updateDimensions();for(const l of this._renderLayers)l.resize(this.dimensions);this._screenElement.style.width=`${this.dimensions.css.canvas.width}px`,this._screenElement.style.height=`${this.dimensions.css.canvas.height}px`}handleCharSizeChanged(){this.handleResize(this._bufferService.cols,this._bufferService.rows)}handleBlur(){this._runOperation(h=>h.handleBlur())}handleFocus(){this._runOperation(h=>h.handleFocus())}handleSelectionChanged(h,v,l=!1){this._runOperation(g=>g.handleSelectionChanged(h,v,l)),this._themeService.colors.selectionForeground&&this._onRequestRedraw.fire({start:0,end:this._bufferService.rows-1})}handleCursorMove(){this._runOperation(h=>h.handleCursorMove())}clear(){this._runOperation(h=>h.reset())}_runOperation(h){for(const v of this._renderLayers)h(v)}renderRows(h,v){for(const l of this._renderLayers)l.handleGridChanged(h,v)}clearTextureAtlas(){for(const h of this._renderLayers)h.clearTextureAtlas()}_updateDimensions(){if(!this._charSizeService.hasValidSize)return;const h=this._coreBrowserService.dpr;this.dimensions.device.char.width=Math.floor(this._charSizeService.width*h),this.dimensions.device.char.height=Math.ceil(this._charSizeService.height*h),this.dimensions.device.cell.height=Math.floor(this.dimensions.device.char.height*this._optionsService.rawOptions.lineHeight),this.dimensions.device.char.top=this._optionsService.rawOptions.lineHeight===1?0:Math.round((this.dimensions.device.cell.height-this.dimensions.device.char.height)/2),this.dimensions.device.cell.width=this.dimensions.device.char.width+Math.round(this._optionsService.rawOptions.letterSpacing),this.dimensions.device.char.left=Math.floor(this._optionsService.rawOptions.letterSpacing/2),this.dimensions.device.canvas.height=this._bufferService.rows*this.dimensions.device.cell.height,this.dimensions.device.canvas.width=this._bufferService.cols*this.dimensions.device.cell.width,this.dimensions.css.canvas.height=Math.round(this.dimensions.device.canvas.height/h),this.dimensions.css.canvas.width=Math.round(this.dimensions.device.canvas.width/h),this.dimensions.css.cell.height=this.dimensions.css.canvas.height/this._bufferService.rows,this.dimensions.css.cell.width=this.dimensions.css.canvas.width/this._bufferService.cols}_setCanvasDevicePixelDimensions(h,v){this.dimensions.device.canvas.height=v,this.dimensions.device.canvas.width=h;for(const l of this._renderLayers)l.resize(this.dimensions);this._requestRedrawViewport()}_requestRedrawViewport(){this._onRequestRedraw.fire({start:0,end:this._bufferService.rows-1})}}s.CanvasRenderer=t},873:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CursorRenderLayer=void 0;const _=a(457),d=a(859),n=a(399),c=a(782),f=a(903);class m extends f.BaseRenderLayer{constructor(e,r,t,i,h,v,l,g,u,p){super(e,r,"cursor",t,!0,p,h,v,u,g),this._onRequestRedraw=i,this._coreService=l,this._cursorBlinkStateManager=this.register(new d.MutableDisposable),this._cell=new c.CellData,this._state={x:0,y:0,isFocused:!1,style:"",width:0},this._cursorRenderers={bar:this._renderBarCursor.bind(this),block:this._renderBlockCursor.bind(this),underline:this._renderUnderlineCursor.bind(this),outline:this._renderOutlineCursor.bind(this)},this.register(v.onOptionChange(()=>this._handleOptionsChanged())),this._handleOptionsChanged()}resize(e){super.resize(e),this._state={x:0,y:0,isFocused:!1,style:"",width:0}}reset(){var e;this._clearCursor(),(e=this._cursorBlinkStateManager.value)==null||e.restartBlinkAnimation(),this._handleOptionsChanged()}handleBlur(){var e;(e=this._cursorBlinkStateManager.value)==null||e.pause(),this._onRequestRedraw.fire({start:this._bufferService.buffer.y,end:this._bufferService.buffer.y})}handleFocus(){var e;(e=this._cursorBlinkStateManager.value)==null||e.resume(),this._onRequestRedraw.fire({start:this._bufferService.buffer.y,end:this._bufferService.buffer.y})}_handleOptionsChanged(){this._optionsService.rawOptions.cursorBlink?this._cursorBlinkStateManager.value||(this._cursorBlinkStateManager.value=new _.CursorBlinkStateManager(()=>this._render(!0),this._coreBrowserService)):this._cursorBlinkStateManager.clear(),this._onRequestRedraw.fire({start:this._bufferService.buffer.y,end:this._bufferService.buffer.y})}handleCursorMove(){var e;(e=this._cursorBlinkStateManager.value)==null||e.restartBlinkAnimation()}handleGridChanged(e,r){!this._cursorBlinkStateManager.value||this._cursorBlinkStateManager.value.isPaused?this._render(!1):this._cursorBlinkStateManager.value.restartBlinkAnimation()}_render(e){if(!this._coreService.isCursorInitialized||this._coreService.isCursorHidden)return void this._clearCursor();const r=this._bufferService.buffer.ybase+this._bufferService.buffer.y,t=r-this._bufferService.buffer.ydisp;if(t<0||t>=this._bufferService.rows)return void this._clearCursor();const i=Math.min(this._bufferService.buffer.x,this._bufferService.cols-1);if(this._bufferService.buffer.lines.get(r).loadCell(i,this._cell),this._cell.content!==void 0){if(!this._coreBrowserService.isFocused){this._clearCursor(),this._ctx.save(),this._ctx.fillStyle=this._themeService.colors.cursor.css;const h=this._optionsService.rawOptions.cursorStyle,v=this._optionsService.rawOptions.cursorInactiveStyle;return v&&v!=="none"&&this._cursorRenderers[v](i,t,this._cell),this._ctx.restore(),this._state.x=i,this._state.y=t,this._state.isFocused=!1,this._state.style=h,void(this._state.width=this._cell.getWidth())}if(!this._cursorBlinkStateManager.value||this._cursorBlinkStateManager.value.isCursorVisible){if(this._state){if(this._state.x===i&&this._state.y===t&&this._state.isFocused===this._coreBrowserService.isFocused&&this._state.style===this._optionsService.rawOptions.cursorStyle&&this._state.width===this._cell.getWidth())return;this._clearCursor()}this._ctx.save(),this._cursorRenderers[this._optionsService.rawOptions.cursorStyle||"block"](i,t,this._cell),this._ctx.restore(),this._state.x=i,this._state.y=t,this._state.isFocused=!1,this._state.style=this._optionsService.rawOptions.cursorStyle,this._state.width=this._cell.getWidth()}else this._clearCursor()}}_clearCursor(){this._state&&(n.isFirefox||this._coreBrowserService.dpr<1?this._clearAll():this._clearCells(this._state.x,this._state.y,this._state.width,1),this._state={x:0,y:0,isFocused:!1,style:"",width:0})}_renderBarCursor(e,r,t){this._ctx.save(),this._ctx.fillStyle=this._themeService.colors.cursor.css,this._fillLeftLineAtCell(e,r,this._optionsService.rawOptions.cursorWidth),this._ctx.restore()}_renderBlockCursor(e,r,t){this._ctx.save(),this._ctx.fillStyle=this._themeService.colors.cursor.css,this._fillCells(e,r,t.getWidth(),1),this._ctx.fillStyle=this._themeService.colors.cursorAccent.css,this._fillCharTrueColor(t,e,r),this._ctx.restore()}_renderUnderlineCursor(e,r,t){this._ctx.save(),this._ctx.fillStyle=this._themeService.colors.cursor.css,this._fillBottomLineAtCells(e,r),this._ctx.restore()}_renderOutlineCursor(e,r,t){this._ctx.save(),this._ctx.strokeStyle=this._themeService.colors.cursor.css,this._strokeRectAtCell(e,r,t.getWidth(),1),this._ctx.restore()}}s.CursorRenderLayer=m},574:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.GridCache=void 0,s.GridCache=class{constructor(){this.cache=[]}resize(a,_){for(let d=0;d<a;d++){this.cache.length<=d&&this.cache.push([]);for(let n=this.cache[d].length;n<_;n++)this.cache[d].push(void 0);this.cache[d].length=_}this.cache.length=a}clear(){for(let a=0;a<this.cache.length;a++)for(let _=0;_<this.cache[a].length;_++)this.cache[a][_]=void 0}}},43:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.LinkRenderLayer=void 0;const _=a(197),d=a(237),n=a(903);class c extends n.BaseRenderLayer{constructor(m,o,e,r,t,i,h,v,l){super(m,o,"link",e,!0,l,t,i,h,v),this.register(r.onShowLinkUnderline(g=>this._handleShowLinkUnderline(g))),this.register(r.onHideLinkUnderline(g=>this._handleHideLinkUnderline(g)))}resize(m){super.resize(m),this._state=void 0}reset(){this._clearCurrentLink()}_clearCurrentLink(){if(this._state){this._clearCells(this._state.x1,this._state.y1,this._state.cols-this._state.x1,1);const m=this._state.y2-this._state.y1-1;m>0&&this._clearCells(0,this._state.y1+1,this._state.cols,m),this._clearCells(0,this._state.y2,this._state.x2,1),this._state=void 0}}_handleShowLinkUnderline(m){if(m.fg===d.INVERTED_DEFAULT_COLOR?this._ctx.fillStyle=this._themeService.colors.background.css:m.fg&&(0,_.is256Color)(m.fg)?this._ctx.fillStyle=this._themeService.colors.ansi[m.fg].css:this._ctx.fillStyle=this._themeService.colors.foreground.css,m.y1===m.y2)this._fillBottomLineAtCells(m.x1,m.y1,m.x2-m.x1);else{this._fillBottomLineAtCells(m.x1,m.y1,m.cols-m.x1);for(let o=m.y1+1;o<m.y2;o++)this._fillBottomLineAtCells(0,o,m.cols);this._fillBottomLineAtCells(0,m.y2,m.x2)}this._state=m}_handleHideLinkUnderline(m){this._clearCurrentLink()}}s.LinkRenderLayer=c},630:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.SelectionRenderLayer=void 0;const _=a(903);class d extends _.BaseRenderLayer{constructor(c,f,m,o,e,r,t,i){super(c,f,"selection",m,!0,i,o,t,r,e),this._clearState()}_clearState(){this._state={start:void 0,end:void 0,columnSelectMode:void 0,ydisp:void 0}}resize(c){super.resize(c),this._selectionModel.selectionStart&&this._selectionModel.selectionEnd&&(this._clearState(),this._redrawSelection(this._selectionModel.selectionStart,this._selectionModel.selectionEnd,this._selectionModel.columnSelectMode))}reset(){this._state.start&&this._state.end&&(this._clearState(),this._clearAll())}handleBlur(){this.reset(),this._redrawSelection(this._selectionModel.selectionStart,this._selectionModel.selectionEnd,this._selectionModel.columnSelectMode)}handleFocus(){this.reset(),this._redrawSelection(this._selectionModel.selectionStart,this._selectionModel.selectionEnd,this._selectionModel.columnSelectMode)}handleSelectionChanged(c,f,m){super.handleSelectionChanged(c,f,m),this._redrawSelection(c,f,m)}_redrawSelection(c,f,m){if(!this._didStateChange(c,f,m,this._bufferService.buffer.ydisp))return;if(this._clearAll(),!c||!f)return void this._clearState();const o=c[1]-this._bufferService.buffer.ydisp,e=f[1]-this._bufferService.buffer.ydisp,r=Math.max(o,0),t=Math.min(e,this._bufferService.rows-1);if(r>=this._bufferService.rows||t<0)this._state.ydisp=this._bufferService.buffer.ydisp;else{if(this._ctx.fillStyle=(this._coreBrowserService.isFocused?this._themeService.colors.selectionBackgroundTransparent:this._themeService.colors.selectionInactiveBackgroundTransparent).css,m){const i=c[0],h=f[0]-i,v=t-r+1;this._fillCells(i,r,h,v)}else{const i=o===r?c[0]:0,h=r===e?f[0]:this._bufferService.cols;this._fillCells(i,r,h-i,1);const v=Math.max(t-r-1,0);if(this._fillCells(0,r+1,this._bufferService.cols,v),r!==t){const l=e===t?f[0]:this._bufferService.cols;this._fillCells(0,t,l,1)}}this._state.start=[c[0],c[1]],this._state.end=[f[0],f[1]],this._state.columnSelectMode=m,this._state.ydisp=this._bufferService.buffer.ydisp}}_didStateChange(c,f,m,o){return!this._areCoordinatesEqual(c,this._state.start)||!this._areCoordinatesEqual(f,this._state.end)||m!==this._state.columnSelectMode||o!==this._state.ydisp}_areCoordinatesEqual(c,f){return!(!c||!f)&&c[0]===f[0]&&c[1]===f[1]}}s.SelectionRenderLayer=d},744:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.TextRenderLayer=void 0;const _=a(577),d=a(147),n=a(782),c=a(855),f=a(903),m=a(574);class o extends f.BaseRenderLayer{constructor(r,t,i,h,v,l,g,u,p,C){super(r,t,"text",i,h,C,v,l,u,p),this._characterJoinerService=g,this._characterWidth=0,this._characterFont="",this._characterOverlapCache={},this._workCell=new n.CellData,this._state=new m.GridCache,this.register(l.onSpecificOptionChange("allowTransparency",b=>this._setTransparency(b)))}resize(r){super.resize(r);const t=this._getFont(!1,!1);this._characterWidth===r.device.char.width&&this._characterFont===t||(this._characterWidth=r.device.char.width,this._characterFont=t,this._characterOverlapCache={}),this._state.clear(),this._state.resize(this._bufferService.cols,this._bufferService.rows)}reset(){this._state.clear(),this._clearAll()}_forEachCell(r,t,i){for(let h=r;h<=t;h++){const v=h+this._bufferService.buffer.ydisp,l=this._bufferService.buffer.lines.get(v),g=this._characterJoinerService.getJoinedCharacters(v);for(let u=0;u<this._bufferService.cols;u++){l.loadCell(u,this._workCell);let p=this._workCell,C=!1,b=u;if(p.getWidth()!==0){if(g.length>0&&u===g[0][0]){C=!0;const y=g.shift();p=new _.JoinedCellData(this._workCell,l.translateToString(!0,y[0],y[1]),y[1]-y[0]),b=y[1]-1}!C&&this._isOverlapping(p)&&b<l.length-1&&l.getCodePoint(b+1)===c.NULL_CELL_CODE&&(p.content&=-12582913,p.content|=8388608),i(p,u,h),u=b}}}}_drawBackground(r,t){const i=this._ctx,h=this._bufferService.cols;let v=0,l=0,g=null;i.save(),this._forEachCell(r,t,(u,p,C)=>{let b=null;u.isInverse()?b=u.isFgDefault()?this._themeService.colors.foreground.css:u.isFgRGB()?`rgb(${d.AttributeData.toColorRGB(u.getFgColor()).join(",")})`:this._themeService.colors.ansi[u.getFgColor()].css:u.isBgRGB()?b=`rgb(${d.AttributeData.toColorRGB(u.getBgColor()).join(",")})`:u.isBgPalette()&&(b=this._themeService.colors.ansi[u.getBgColor()].css);let y=!1;this._decorationService.forEachDecorationAtCell(p,this._bufferService.buffer.ydisp+C,void 0,w=>{w.options.layer!=="top"&&y||(w.backgroundColorRGB&&(b=w.backgroundColorRGB.css),y=w.options.layer==="top")}),g===null&&(v=p,l=C),C!==l?(i.fillStyle=g||"",this._fillCells(v,l,h-v,1),v=p,l=C):g!==b&&(i.fillStyle=g||"",this._fillCells(v,l,p-v,1),v=p,l=C),g=b}),g!==null&&(i.fillStyle=g,this._fillCells(v,l,h-v,1)),i.restore()}_drawForeground(r,t){this._forEachCell(r,t,(i,h,v)=>this._drawChars(i,h,v))}handleGridChanged(r,t){this._state.cache.length!==0&&(this._charAtlas&&this._charAtlas.beginFrame(),this._clearCells(0,r,this._bufferService.cols,t-r+1),this._drawBackground(r,t),this._drawForeground(r,t))}_isOverlapping(r){if(r.getWidth()!==1||r.getCode()<256)return!1;const t=r.getChars();if(this._characterOverlapCache.hasOwnProperty(t))return this._characterOverlapCache[t];this._ctx.save(),this._ctx.font=this._characterFont;const i=Math.floor(this._ctx.measureText(t).width)>this._characterWidth;return this._ctx.restore(),this._characterOverlapCache[t]=i,i}}s.TextRenderLayer=o},274:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CellColorResolver=void 0;const _=a(855),d=a(160),n=a(374);let c,f=0,m=0,o=!1,e=!1,r=!1,t=0;s.CellColorResolver=class{constructor(i,h,v,l,g,u){this._terminal=i,this._optionService=h,this._selectionRenderModel=v,this._decorationService=l,this._coreBrowserService=g,this._themeService=u,this.result={fg:0,bg:0,ext:0}}resolve(i,h,v,l){if(this.result.bg=i.bg,this.result.fg=i.fg,this.result.ext=268435456&i.bg?i.extended.ext:0,m=0,f=0,e=!1,o=!1,r=!1,c=this._themeService.colors,t=0,i.getCode()!==_.NULL_CELL_CODE&&i.extended.underlineStyle===4){const g=Math.max(1,Math.floor(this._optionService.rawOptions.fontSize*this._coreBrowserService.dpr/15));t=h*l%(2*Math.round(g))}if(this._decorationService.forEachDecorationAtCell(h,v,"bottom",g=>{g.backgroundColorRGB&&(m=g.backgroundColorRGB.rgba>>8&16777215,e=!0),g.foregroundColorRGB&&(f=g.foregroundColorRGB.rgba>>8&16777215,o=!0)}),r=this._selectionRenderModel.isCellSelected(this._terminal,h,v),r){if(67108864&this.result.fg||(50331648&this.result.bg)!=0){if(67108864&this.result.fg)switch(50331648&this.result.fg){case 16777216:case 33554432:m=this._themeService.colors.ansi[255&this.result.fg].rgba;break;case 50331648:m=(16777215&this.result.fg)<<8|255;break;default:m=this._themeService.colors.foreground.rgba}else switch(50331648&this.result.bg){case 16777216:case 33554432:m=this._themeService.colors.ansi[255&this.result.bg].rgba;break;case 50331648:m=(16777215&this.result.bg)<<8|255}m=d.rgba.blend(m,4294967040&(this._coreBrowserService.isFocused?c.selectionBackgroundOpaque:c.selectionInactiveBackgroundOpaque).rgba|128)>>8&16777215}else m=(this._coreBrowserService.isFocused?c.selectionBackgroundOpaque:c.selectionInactiveBackgroundOpaque).rgba>>8&16777215;if(e=!0,c.selectionForeground&&(f=c.selectionForeground.rgba>>8&16777215,o=!0),(0,n.treatGlyphAsBackgroundColor)(i.getCode())){if(67108864&this.result.fg&&(50331648&this.result.bg)==0)f=(this._coreBrowserService.isFocused?c.selectionBackgroundOpaque:c.selectionInactiveBackgroundOpaque).rgba>>8&16777215;else{if(67108864&this.result.fg)switch(50331648&this.result.bg){case 16777216:case 33554432:f=this._themeService.colors.ansi[255&this.result.bg].rgba;break;case 50331648:f=(16777215&this.result.bg)<<8|255}else switch(50331648&this.result.fg){case 16777216:case 33554432:f=this._themeService.colors.ansi[255&this.result.fg].rgba;break;case 50331648:f=(16777215&this.result.fg)<<8|255;break;default:f=this._themeService.colors.foreground.rgba}f=d.rgba.blend(f,4294967040&(this._coreBrowserService.isFocused?c.selectionBackgroundOpaque:c.selectionInactiveBackgroundOpaque).rgba|128)>>8&16777215}o=!0}}this._decorationService.forEachDecorationAtCell(h,v,"top",g=>{g.backgroundColorRGB&&(m=g.backgroundColorRGB.rgba>>8&16777215,e=!0),g.foregroundColorRGB&&(f=g.foregroundColorRGB.rgba>>8&16777215,o=!0)}),e&&(m=r?-16777216&i.bg&-134217729|m|50331648:-16777216&i.bg|m|50331648),o&&(f=-16777216&i.fg&-67108865|f|50331648),67108864&this.result.fg&&(e&&!o&&(f=(50331648&this.result.bg)==0?-134217728&this.result.fg|16777215&c.background.rgba>>8|50331648:-134217728&this.result.fg|67108863&this.result.bg,o=!0),!e&&o&&(m=(50331648&this.result.fg)==0?-67108864&this.result.bg|16777215&c.foreground.rgba>>8|50331648:-67108864&this.result.bg|67108863&this.result.fg,e=!0)),c=void 0,this.result.bg=e?m:this.result.bg,this.result.fg=o?f:this.result.fg,this.result.ext&=536870911,this.result.ext|=t<<29&3758096384}}},627:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.removeTerminalFromCache=s.acquireTextureAtlas=void 0;const _=a(509),d=a(197),n=[];s.acquireTextureAtlas=function(c,f,m,o,e,r,t,i){const h=(0,d.generateConfig)(o,e,r,t,f,m,i);for(let g=0;g<n.length;g++){const u=n[g],p=u.ownedBy.indexOf(c);if(p>=0){if((0,d.configEquals)(u.config,h))return u.atlas;u.ownedBy.length===1?(u.atlas.dispose(),n.splice(g,1)):u.ownedBy.splice(p,1);break}}for(let g=0;g<n.length;g++){const u=n[g];if((0,d.configEquals)(u.config,h))return u.ownedBy.push(c),u.atlas}const v=c._core,l={atlas:new _.TextureAtlas(document,h,v.unicodeService),config:h,ownedBy:[c]};return n.push(l),l.atlas},s.removeTerminalFromCache=function(c){for(let f=0;f<n.length;f++){const m=n[f].ownedBy.indexOf(c);if(m!==-1){n[f].ownedBy.length===1?(n[f].atlas.dispose(),n.splice(f,1)):n[f].ownedBy.splice(m,1);break}}}},197:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.is256Color=s.configEquals=s.generateConfig=void 0;const _=a(160);s.generateConfig=function(d,n,c,f,m,o,e){const r={foreground:o.foreground,background:o.background,cursor:_.NULL_COLOR,cursorAccent:_.NULL_COLOR,selectionForeground:_.NULL_COLOR,selectionBackgroundTransparent:_.NULL_COLOR,selectionBackgroundOpaque:_.NULL_COLOR,selectionInactiveBackgroundTransparent:_.NULL_COLOR,selectionInactiveBackgroundOpaque:_.NULL_COLOR,ansi:o.ansi.slice(),contrastCache:o.contrastCache,halfContrastCache:o.halfContrastCache};return{customGlyphs:m.customGlyphs,devicePixelRatio:e,letterSpacing:m.letterSpacing,lineHeight:m.lineHeight,deviceCellWidth:d,deviceCellHeight:n,deviceCharWidth:c,deviceCharHeight:f,fontFamily:m.fontFamily,fontSize:m.fontSize,fontWeight:m.fontWeight,fontWeightBold:m.fontWeightBold,allowTransparency:m.allowTransparency,drawBoldTextInBrightColors:m.drawBoldTextInBrightColors,minimumContrastRatio:m.minimumContrastRatio,colors:r}},s.configEquals=function(d,n){for(let c=0;c<d.colors.ansi.length;c++)if(d.colors.ansi[c].rgba!==n.colors.ansi[c].rgba)return!1;return d.devicePixelRatio===n.devicePixelRatio&&d.customGlyphs===n.customGlyphs&&d.lineHeight===n.lineHeight&&d.letterSpacing===n.letterSpacing&&d.fontFamily===n.fontFamily&&d.fontSize===n.fontSize&&d.fontWeight===n.fontWeight&&d.fontWeightBold===n.fontWeightBold&&d.allowTransparency===n.allowTransparency&&d.deviceCharWidth===n.deviceCharWidth&&d.deviceCharHeight===n.deviceCharHeight&&d.drawBoldTextInBrightColors===n.drawBoldTextInBrightColors&&d.minimumContrastRatio===n.minimumContrastRatio&&d.colors.foreground.rgba===n.colors.foreground.rgba&&d.colors.background.rgba===n.colors.background.rgba},s.is256Color=function(d){return(50331648&d)==16777216||(50331648&d)==33554432}},237:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.TEXT_BASELINE=s.DIM_OPACITY=s.INVERTED_DEFAULT_COLOR=void 0;const _=a(399);s.INVERTED_DEFAULT_COLOR=257,s.DIM_OPACITY=.5,s.TEXT_BASELINE=_.isFirefox||_.isLegacyEdge?"bottom":"ideographic"},457:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CursorBlinkStateManager=void 0,s.CursorBlinkStateManager=class{constructor(a,_){this._renderCallback=a,this._coreBrowserService=_,this.isCursorVisible=!0,this._coreBrowserService.isFocused&&this._restartInterval()}get isPaused(){return!(this._blinkStartTimeout||this._blinkInterval)}dispose(){this._blinkInterval&&(this._coreBrowserService.window.clearInterval(this._blinkInterval),this._blinkInterval=void 0),this._blinkStartTimeout&&(this._coreBrowserService.window.clearTimeout(this._blinkStartTimeout),this._blinkStartTimeout=void 0),this._animationFrame&&(this._coreBrowserService.window.cancelAnimationFrame(this._animationFrame),this._animationFrame=void 0)}restartBlinkAnimation(){this.isPaused||(this._animationTimeRestarted=Date.now(),this.isCursorVisible=!0,this._animationFrame||(this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>{this._renderCallback(),this._animationFrame=void 0})))}_restartInterval(a=600){this._blinkInterval&&(this._coreBrowserService.window.clearInterval(this._blinkInterval),this._blinkInterval=void 0),this._blinkStartTimeout=this._coreBrowserService.window.setTimeout(()=>{if(this._animationTimeRestarted){const _=600-(Date.now()-this._animationTimeRestarted);if(this._animationTimeRestarted=void 0,_>0)return void this._restartInterval(_)}this.isCursorVisible=!1,this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>{this._renderCallback(),this._animationFrame=void 0}),this._blinkInterval=this._coreBrowserService.window.setInterval(()=>{if(this._animationTimeRestarted){const _=600-(Date.now()-this._animationTimeRestarted);return this._animationTimeRestarted=void 0,void this._restartInterval(_)}this.isCursorVisible=!this.isCursorVisible,this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>{this._renderCallback(),this._animationFrame=void 0})},600)},a)}pause(){this.isCursorVisible=!0,this._blinkInterval&&(this._coreBrowserService.window.clearInterval(this._blinkInterval),this._blinkInterval=void 0),this._blinkStartTimeout&&(this._coreBrowserService.window.clearTimeout(this._blinkStartTimeout),this._blinkStartTimeout=void 0),this._animationFrame&&(this._coreBrowserService.window.cancelAnimationFrame(this._animationFrame),this._animationFrame=void 0)}resume(){this.pause(),this._animationTimeRestarted=void 0,this._restartInterval(),this.restartBlinkAnimation()}}},860:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.tryDrawCustomChar=s.powerlineDefinitions=s.boxDrawingDefinitions=s.blockElementDefinitions=void 0;const _=a(374);s.blockElementDefinitions={"▀":[{x:0,y:0,w:8,h:4}],"▁":[{x:0,y:7,w:8,h:1}],"▂":[{x:0,y:6,w:8,h:2}],"▃":[{x:0,y:5,w:8,h:3}],"▄":[{x:0,y:4,w:8,h:4}],"▅":[{x:0,y:3,w:8,h:5}],"▆":[{x:0,y:2,w:8,h:6}],"▇":[{x:0,y:1,w:8,h:7}],"█":[{x:0,y:0,w:8,h:8}],"▉":[{x:0,y:0,w:7,h:8}],"▊":[{x:0,y:0,w:6,h:8}],"▋":[{x:0,y:0,w:5,h:8}],"▌":[{x:0,y:0,w:4,h:8}],"▍":[{x:0,y:0,w:3,h:8}],"▎":[{x:0,y:0,w:2,h:8}],"▏":[{x:0,y:0,w:1,h:8}],"▐":[{x:4,y:0,w:4,h:8}],"▔":[{x:0,y:0,w:8,h:1}],"▕":[{x:7,y:0,w:1,h:8}],"▖":[{x:0,y:4,w:4,h:4}],"▗":[{x:4,y:4,w:4,h:4}],"▘":[{x:0,y:0,w:4,h:4}],"▙":[{x:0,y:0,w:4,h:8},{x:0,y:4,w:8,h:4}],"▚":[{x:0,y:0,w:4,h:4},{x:4,y:4,w:4,h:4}],"▛":[{x:0,y:0,w:4,h:8},{x:4,y:0,w:4,h:4}],"▜":[{x:0,y:0,w:8,h:4},{x:4,y:0,w:4,h:8}],"▝":[{x:4,y:0,w:4,h:4}],"▞":[{x:4,y:0,w:4,h:4},{x:0,y:4,w:4,h:4}],"▟":[{x:4,y:0,w:4,h:8},{x:0,y:4,w:8,h:4}],"🭰":[{x:1,y:0,w:1,h:8}],"🭱":[{x:2,y:0,w:1,h:8}],"🭲":[{x:3,y:0,w:1,h:8}],"🭳":[{x:4,y:0,w:1,h:8}],"🭴":[{x:5,y:0,w:1,h:8}],"🭵":[{x:6,y:0,w:1,h:8}],"🭶":[{x:0,y:1,w:8,h:1}],"🭷":[{x:0,y:2,w:8,h:1}],"🭸":[{x:0,y:3,w:8,h:1}],"🭹":[{x:0,y:4,w:8,h:1}],"🭺":[{x:0,y:5,w:8,h:1}],"🭻":[{x:0,y:6,w:8,h:1}],"🭼":[{x:0,y:0,w:1,h:8},{x:0,y:7,w:8,h:1}],"🭽":[{x:0,y:0,w:1,h:8},{x:0,y:0,w:8,h:1}],"🭾":[{x:7,y:0,w:1,h:8},{x:0,y:0,w:8,h:1}],"🭿":[{x:7,y:0,w:1,h:8},{x:0,y:7,w:8,h:1}],"🮀":[{x:0,y:0,w:8,h:1},{x:0,y:7,w:8,h:1}],"🮁":[{x:0,y:0,w:8,h:1},{x:0,y:2,w:8,h:1},{x:0,y:4,w:8,h:1},{x:0,y:7,w:8,h:1}],"🮂":[{x:0,y:0,w:8,h:2}],"🮃":[{x:0,y:0,w:8,h:3}],"🮄":[{x:0,y:0,w:8,h:5}],"🮅":[{x:0,y:0,w:8,h:6}],"🮆":[{x:0,y:0,w:8,h:7}],"🮇":[{x:6,y:0,w:2,h:8}],"🮈":[{x:5,y:0,w:3,h:8}],"🮉":[{x:3,y:0,w:5,h:8}],"🮊":[{x:2,y:0,w:6,h:8}],"🮋":[{x:1,y:0,w:7,h:8}],"🮕":[{x:0,y:0,w:2,h:2},{x:4,y:0,w:2,h:2},{x:2,y:2,w:2,h:2},{x:6,y:2,w:2,h:2},{x:0,y:4,w:2,h:2},{x:4,y:4,w:2,h:2},{x:2,y:6,w:2,h:2},{x:6,y:6,w:2,h:2}],"🮖":[{x:2,y:0,w:2,h:2},{x:6,y:0,w:2,h:2},{x:0,y:2,w:2,h:2},{x:4,y:2,w:2,h:2},{x:2,y:4,w:2,h:2},{x:6,y:4,w:2,h:2},{x:0,y:6,w:2,h:2},{x:4,y:6,w:2,h:2}],"🮗":[{x:0,y:2,w:8,h:2},{x:0,y:6,w:8,h:2}]};const d={"░":[[1,0,0,0],[0,0,0,0],[0,0,1,0],[0,0,0,0]],"▒":[[1,0],[0,0],[0,1],[0,0]],"▓":[[0,1],[1,1],[1,0],[1,1]]};s.boxDrawingDefinitions={"─":{1:"M0,.5 L1,.5"},"━":{3:"M0,.5 L1,.5"},"│":{1:"M.5,0 L.5,1"},"┃":{3:"M.5,0 L.5,1"},"┌":{1:"M0.5,1 L.5,.5 L1,.5"},"┏":{3:"M0.5,1 L.5,.5 L1,.5"},"┐":{1:"M0,.5 L.5,.5 L.5,1"},"┓":{3:"M0,.5 L.5,.5 L.5,1"},"└":{1:"M.5,0 L.5,.5 L1,.5"},"┗":{3:"M.5,0 L.5,.5 L1,.5"},"┘":{1:"M.5,0 L.5,.5 L0,.5"},"┛":{3:"M.5,0 L.5,.5 L0,.5"},"├":{1:"M.5,0 L.5,1 M.5,.5 L1,.5"},"┣":{3:"M.5,0 L.5,1 M.5,.5 L1,.5"},"┤":{1:"M.5,0 L.5,1 M.5,.5 L0,.5"},"┫":{3:"M.5,0 L.5,1 M.5,.5 L0,.5"},"┬":{1:"M0,.5 L1,.5 M.5,.5 L.5,1"},"┳":{3:"M0,.5 L1,.5 M.5,.5 L.5,1"},"┴":{1:"M0,.5 L1,.5 M.5,.5 L.5,0"},"┻":{3:"M0,.5 L1,.5 M.5,.5 L.5,0"},"┼":{1:"M0,.5 L1,.5 M.5,0 L.5,1"},"╋":{3:"M0,.5 L1,.5 M.5,0 L.5,1"},"╴":{1:"M.5,.5 L0,.5"},"╸":{3:"M.5,.5 L0,.5"},"╵":{1:"M.5,.5 L.5,0"},"╹":{3:"M.5,.5 L.5,0"},"╶":{1:"M.5,.5 L1,.5"},"╺":{3:"M.5,.5 L1,.5"},"╷":{1:"M.5,.5 L.5,1"},"╻":{3:"M.5,.5 L.5,1"},"═":{1:(o,e)=>`M0,${.5-e} L1,${.5-e} M0,${.5+e} L1,${.5+e}`},"║":{1:(o,e)=>`M${.5-o},0 L${.5-o},1 M${.5+o},0 L${.5+o},1`},"╒":{1:(o,e)=>`M.5,1 L.5,${.5-e} L1,${.5-e} M.5,${.5+e} L1,${.5+e}`},"╓":{1:(o,e)=>`M${.5-o},1 L${.5-o},.5 L1,.5 M${.5+o},.5 L${.5+o},1`},"╔":{1:(o,e)=>`M1,${.5-e} L${.5-o},${.5-e} L${.5-o},1 M1,${.5+e} L${.5+o},${.5+e} L${.5+o},1`},"╕":{1:(o,e)=>`M0,${.5-e} L.5,${.5-e} L.5,1 M0,${.5+e} L.5,${.5+e}`},"╖":{1:(o,e)=>`M${.5+o},1 L${.5+o},.5 L0,.5 M${.5-o},.5 L${.5-o},1`},"╗":{1:(o,e)=>`M0,${.5+e} L${.5-o},${.5+e} L${.5-o},1 M0,${.5-e} L${.5+o},${.5-e} L${.5+o},1`},"╘":{1:(o,e)=>`M.5,0 L.5,${.5+e} L1,${.5+e} M.5,${.5-e} L1,${.5-e}`},"╙":{1:(o,e)=>`M1,.5 L${.5-o},.5 L${.5-o},0 M${.5+o},.5 L${.5+o},0`},"╚":{1:(o,e)=>`M1,${.5-e} L${.5+o},${.5-e} L${.5+o},0 M1,${.5+e} L${.5-o},${.5+e} L${.5-o},0`},"╛":{1:(o,e)=>`M0,${.5+e} L.5,${.5+e} L.5,0 M0,${.5-e} L.5,${.5-e}`},"╜":{1:(o,e)=>`M0,.5 L${.5+o},.5 L${.5+o},0 M${.5-o},.5 L${.5-o},0`},"╝":{1:(o,e)=>`M0,${.5-e} L${.5-o},${.5-e} L${.5-o},0 M0,${.5+e} L${.5+o},${.5+e} L${.5+o},0`},"╞":{1:(o,e)=>`M.5,0 L.5,1 M.5,${.5-e} L1,${.5-e} M.5,${.5+e} L1,${.5+e}`},"╟":{1:(o,e)=>`M${.5-o},0 L${.5-o},1 M${.5+o},0 L${.5+o},1 M${.5+o},.5 L1,.5`},"╠":{1:(o,e)=>`M${.5-o},0 L${.5-o},1 M1,${.5+e} L${.5+o},${.5+e} L${.5+o},1 M1,${.5-e} L${.5+o},${.5-e} L${.5+o},0`},"╡":{1:(o,e)=>`M.5,0 L.5,1 M0,${.5-e} L.5,${.5-e} M0,${.5+e} L.5,${.5+e}`},"╢":{1:(o,e)=>`M0,.5 L${.5-o},.5 M${.5-o},0 L${.5-o},1 M${.5+o},0 L${.5+o},1`},"╣":{1:(o,e)=>`M${.5+o},0 L${.5+o},1 M0,${.5+e} L${.5-o},${.5+e} L${.5-o},1 M0,${.5-e} L${.5-o},${.5-e} L${.5-o},0`},"╤":{1:(o,e)=>`M0,${.5-e} L1,${.5-e} M0,${.5+e} L1,${.5+e} M.5,${.5+e} L.5,1`},"╥":{1:(o,e)=>`M0,.5 L1,.5 M${.5-o},.5 L${.5-o},1 M${.5+o},.5 L${.5+o},1`},"╦":{1:(o,e)=>`M0,${.5-e} L1,${.5-e} M0,${.5+e} L${.5-o},${.5+e} L${.5-o},1 M1,${.5+e} L${.5+o},${.5+e} L${.5+o},1`},"╧":{1:(o,e)=>`M.5,0 L.5,${.5-e} M0,${.5-e} L1,${.5-e} M0,${.5+e} L1,${.5+e}`},"╨":{1:(o,e)=>`M0,.5 L1,.5 M${.5-o},.5 L${.5-o},0 M${.5+o},.5 L${.5+o},0`},"╩":{1:(o,e)=>`M0,${.5+e} L1,${.5+e} M0,${.5-e} L${.5-o},${.5-e} L${.5-o},0 M1,${.5-e} L${.5+o},${.5-e} L${.5+o},0`},"╪":{1:(o,e)=>`M.5,0 L.5,1 M0,${.5-e} L1,${.5-e} M0,${.5+e} L1,${.5+e}`},"╫":{1:(o,e)=>`M0,.5 L1,.5 M${.5-o},0 L${.5-o},1 M${.5+o},0 L${.5+o},1`},"╬":{1:(o,e)=>`M0,${.5+e} L${.5-o},${.5+e} L${.5-o},1 M1,${.5+e} L${.5+o},${.5+e} L${.5+o},1 M0,${.5-e} L${.5-o},${.5-e} L${.5-o},0 M1,${.5-e} L${.5+o},${.5-e} L${.5+o},0`},"╱":{1:"M1,0 L0,1"},"╲":{1:"M0,0 L1,1"},"╳":{1:"M1,0 L0,1 M0,0 L1,1"},"╼":{1:"M.5,.5 L0,.5",3:"M.5,.5 L1,.5"},"╽":{1:"M.5,.5 L.5,0",3:"M.5,.5 L.5,1"},"╾":{1:"M.5,.5 L1,.5",3:"M.5,.5 L0,.5"},"╿":{1:"M.5,.5 L.5,1",3:"M.5,.5 L.5,0"},"┍":{1:"M.5,.5 L.5,1",3:"M.5,.5 L1,.5"},"┎":{1:"M.5,.5 L1,.5",3:"M.5,.5 L.5,1"},"┑":{1:"M.5,.5 L.5,1",3:"M.5,.5 L0,.5"},"┒":{1:"M.5,.5 L0,.5",3:"M.5,.5 L.5,1"},"┕":{1:"M.5,.5 L.5,0",3:"M.5,.5 L1,.5"},"┖":{1:"M.5,.5 L1,.5",3:"M.5,.5 L.5,0"},"┙":{1:"M.5,.5 L.5,0",3:"M.5,.5 L0,.5"},"┚":{1:"M.5,.5 L0,.5",3:"M.5,.5 L.5,0"},"┝":{1:"M.5,0 L.5,1",3:"M.5,.5 L1,.5"},"┞":{1:"M0.5,1 L.5,.5 L1,.5",3:"M.5,.5 L.5,0"},"┟":{1:"M.5,0 L.5,.5 L1,.5",3:"M.5,.5 L.5,1"},"┠":{1:"M.5,.5 L1,.5",3:"M.5,0 L.5,1"},"┡":{1:"M.5,.5 L.5,1",3:"M.5,0 L.5,.5 L1,.5"},"┢":{1:"M.5,.5 L.5,0",3:"M0.5,1 L.5,.5 L1,.5"},"┥":{1:"M.5,0 L.5,1",3:"M.5,.5 L0,.5"},"┦":{1:"M0,.5 L.5,.5 L.5,1",3:"M.5,.5 L.5,0"},"┧":{1:"M.5,0 L.5,.5 L0,.5",3:"M.5,.5 L.5,1"},"┨":{1:"M.5,.5 L0,.5",3:"M.5,0 L.5,1"},"┩":{1:"M.5,.5 L.5,1",3:"M.5,0 L.5,.5 L0,.5"},"┪":{1:"M.5,.5 L.5,0",3:"M0,.5 L.5,.5 L.5,1"},"┭":{1:"M0.5,1 L.5,.5 L1,.5",3:"M.5,.5 L0,.5"},"┮":{1:"M0,.5 L.5,.5 L.5,1",3:"M.5,.5 L1,.5"},"┯":{1:"M.5,.5 L.5,1",3:"M0,.5 L1,.5"},"┰":{1:"M0,.5 L1,.5",3:"M.5,.5 L.5,1"},"┱":{1:"M.5,.5 L1,.5",3:"M0,.5 L.5,.5 L.5,1"},"┲":{1:"M.5,.5 L0,.5",3:"M0.5,1 L.5,.5 L1,.5"},"┵":{1:"M.5,0 L.5,.5 L1,.5",3:"M.5,.5 L0,.5"},"┶":{1:"M.5,0 L.5,.5 L0,.5",3:"M.5,.5 L1,.5"},"┷":{1:"M.5,.5 L.5,0",3:"M0,.5 L1,.5"},"┸":{1:"M0,.5 L1,.5",3:"M.5,.5 L.5,0"},"┹":{1:"M.5,.5 L1,.5",3:"M.5,0 L.5,.5 L0,.5"},"┺":{1:"M.5,.5 L0,.5",3:"M.5,0 L.5,.5 L1,.5"},"┽":{1:"M.5,0 L.5,1 M.5,.5 L1,.5",3:"M.5,.5 L0,.5"},"┾":{1:"M.5,0 L.5,1 M.5,.5 L0,.5",3:"M.5,.5 L1,.5"},"┿":{1:"M.5,0 L.5,1",3:"M0,.5 L1,.5"},"╀":{1:"M0,.5 L1,.5 M.5,.5 L.5,1",3:"M.5,.5 L.5,0"},"╁":{1:"M.5,.5 L.5,0 M0,.5 L1,.5",3:"M.5,.5 L.5,1"},"╂":{1:"M0,.5 L1,.5",3:"M.5,0 L.5,1"},"╃":{1:"M0.5,1 L.5,.5 L1,.5",3:"M.5,0 L.5,.5 L0,.5"},"╄":{1:"M0,.5 L.5,.5 L.5,1",3:"M.5,0 L.5,.5 L1,.5"},"╅":{1:"M.5,0 L.5,.5 L1,.5",3:"M0,.5 L.5,.5 L.5,1"},"╆":{1:"M.5,0 L.5,.5 L0,.5",3:"M0.5,1 L.5,.5 L1,.5"},"╇":{1:"M.5,.5 L.5,1",3:"M.5,.5 L.5,0 M0,.5 L1,.5"},"╈":{1:"M.5,.5 L.5,0",3:"M0,.5 L1,.5 M.5,.5 L.5,1"},"╉":{1:"M.5,.5 L1,.5",3:"M.5,0 L.5,1 M.5,.5 L0,.5"},"╊":{1:"M.5,.5 L0,.5",3:"M.5,0 L.5,1 M.5,.5 L1,.5"},"╌":{1:"M.1,.5 L.4,.5 M.6,.5 L.9,.5"},"╍":{3:"M.1,.5 L.4,.5 M.6,.5 L.9,.5"},"┄":{1:"M.0667,.5 L.2667,.5 M.4,.5 L.6,.5 M.7333,.5 L.9333,.5"},"┅":{3:"M.0667,.5 L.2667,.5 M.4,.5 L.6,.5 M.7333,.5 L.9333,.5"},"┈":{1:"M.05,.5 L.2,.5 M.3,.5 L.45,.5 M.55,.5 L.7,.5 M.8,.5 L.95,.5"},"┉":{3:"M.05,.5 L.2,.5 M.3,.5 L.45,.5 M.55,.5 L.7,.5 M.8,.5 L.95,.5"},"╎":{1:"M.5,.1 L.5,.4 M.5,.6 L.5,.9"},"╏":{3:"M.5,.1 L.5,.4 M.5,.6 L.5,.9"},"┆":{1:"M.5,.0667 L.5,.2667 M.5,.4 L.5,.6 M.5,.7333 L.5,.9333"},"┇":{3:"M.5,.0667 L.5,.2667 M.5,.4 L.5,.6 M.5,.7333 L.5,.9333"},"┊":{1:"M.5,.05 L.5,.2 M.5,.3 L.5,.45 L.5,.55 M.5,.7 L.5,.95"},"┋":{3:"M.5,.05 L.5,.2 M.5,.3 L.5,.45 L.5,.55 M.5,.7 L.5,.95"},"╭":{1:(o,e)=>`M.5,1 L.5,${.5+e/.15*.5} C.5,${.5+e/.15*.5},.5,.5,1,.5`},"╮":{1:(o,e)=>`M.5,1 L.5,${.5+e/.15*.5} C.5,${.5+e/.15*.5},.5,.5,0,.5`},"╯":{1:(o,e)=>`M.5,0 L.5,${.5-e/.15*.5} C.5,${.5-e/.15*.5},.5,.5,0,.5`},"╰":{1:(o,e)=>`M.5,0 L.5,${.5-e/.15*.5} C.5,${.5-e/.15*.5},.5,.5,1,.5`}},s.powerlineDefinitions={"":{d:"M0,0 L1,.5 L0,1",type:0,rightPadding:2},"":{d:"M-1,-.5 L1,.5 L-1,1.5",type:1,leftPadding:1,rightPadding:1},"":{d:"M1,0 L0,.5 L1,1",type:0,leftPadding:2},"":{d:"M2,-.5 L0,.5 L2,1.5",type:1,leftPadding:1,rightPadding:1},"":{d:"M0,0 L0,1 C0.552,1,1,0.776,1,.5 C1,0.224,0.552,0,0,0",type:0,rightPadding:1},"":{d:"M.2,1 C.422,1,.8,.826,.78,.5 C.8,.174,0.422,0,.2,0",type:1,rightPadding:1},"":{d:"M1,0 L1,1 C0.448,1,0,0.776,0,.5 C0,0.224,0.448,0,1,0",type:0,leftPadding:1},"":{d:"M.8,1 C0.578,1,0.2,.826,.22,.5 C0.2,0.174,0.578,0,0.8,0",type:1,leftPadding:1},"":{d:"M-.5,-.5 L1.5,1.5 L-.5,1.5",type:0},"":{d:"M-.5,-.5 L1.5,1.5",type:1,leftPadding:1,rightPadding:1},"":{d:"M1.5,-.5 L-.5,1.5 L1.5,1.5",type:0},"":{d:"M1.5,-.5 L-.5,1.5 L-.5,-.5",type:0},"":{d:"M1.5,-.5 L-.5,1.5",type:1,leftPadding:1,rightPadding:1},"":{d:"M-.5,-.5 L1.5,1.5 L1.5,-.5",type:0}},s.powerlineDefinitions[""]=s.powerlineDefinitions[""],s.powerlineDefinitions[""]=s.powerlineDefinitions[""],s.tryDrawCustomChar=function(o,e,r,t,i,h,v,l){const g=s.blockElementDefinitions[e];if(g)return function(b,y,w,k,D,P){for(let T=0;T<y.length;T++){const A=y[T],S=D/8,x=P/8;b.fillRect(w+A.x*S,k+A.y*x,A.w*S,A.h*x)}}(o,g,r,t,i,h),!0;const u=d[e];if(u)return function(b,y,w,k,D,P){let T=n.get(y);T||(T=new Map,n.set(y,T));const A=b.fillStyle;if(typeof A!="string")throw new Error(`Unexpected fillStyle type "${A}"`);let S=T.get(A);if(!S){const x=y[0].length,E=y.length,R=b.canvas.ownerDocument.createElement("canvas");R.width=x,R.height=E;const I=(0,_.throwIfFalsy)(R.getContext("2d")),F=new ImageData(x,E);let N,z,M,L;if(A.startsWith("#"))N=parseInt(A.slice(1,3),16),z=parseInt(A.slice(3,5),16),M=parseInt(A.slice(5,7),16),L=A.length>7&&parseInt(A.slice(7,9),16)||1;else{if(!A.startsWith("rgba"))throw new Error(`Unexpected fillStyle color format "${A}" when drawing pattern glyph`);[N,z,M,L]=A.substring(5,A.length-1).split(",").map(H=>parseFloat(H))}for(let H=0;H<E;H++)for(let O=0;O<x;O++)F.data[4*(H*x+O)]=N,F.data[4*(H*x+O)+1]=z,F.data[4*(H*x+O)+2]=M,F.data[4*(H*x+O)+3]=y[H][O]*(255*L);I.putImageData(F,0,0),S=(0,_.throwIfFalsy)(b.createPattern(R,null)),T.set(A,S)}b.fillStyle=S,b.fillRect(w,k,D,P)}(o,u,r,t,i,h),!0;const p=s.boxDrawingDefinitions[e];if(p)return function(b,y,w,k,D,P,T){b.strokeStyle=b.fillStyle;for(const[A,S]of Object.entries(y)){let x;b.beginPath(),b.lineWidth=T*Number.parseInt(A),x=typeof S=="function"?S(.15,.15/P*D):S;for(const E of x.split(" ")){const R=E[0],I=f[R];if(!I){console.error(`Could not find drawing instructions for "${R}"`);continue}const F=E.substring(1).split(",");F[0]&&F[1]&&I(b,m(F,D,P,w,k,!0,T))}b.stroke(),b.closePath()}}(o,p,r,t,i,h,l),!0;const C=s.powerlineDefinitions[e];return!!C&&(function(b,y,w,k,D,P,T,A){const S=new Path2D;S.rect(w,k,D,P),b.clip(S),b.beginPath();const x=T/12;b.lineWidth=A*x;for(const E of y.d.split(" ")){const R=E[0],I=f[R];if(!I){console.error(`Could not find drawing instructions for "${R}"`);continue}const F=E.substring(1).split(",");F[0]&&F[1]&&I(b,m(F,D,P,w,k,!1,A,(y.leftPadding??0)*(x/2),(y.rightPadding??0)*(x/2)))}y.type===1?(b.strokeStyle=b.fillStyle,b.stroke()):b.fill(),b.closePath()}(o,C,r,t,i,h,v,l),!0)};const n=new Map;function c(o,e,r=0){return Math.max(Math.min(o,e),r)}const f={C:(o,e)=>o.bezierCurveTo(e[0],e[1],e[2],e[3],e[4],e[5]),L:(o,e)=>o.lineTo(e[0],e[1]),M:(o,e)=>o.moveTo(e[0],e[1])};function m(o,e,r,t,i,h,v,l=0,g=0){const u=o.map(p=>parseFloat(p)||parseInt(p));if(u.length<2)throw new Error("Too few arguments for instruction");for(let p=0;p<u.length;p+=2)u[p]*=e-l*v-g*v,h&&u[p]!==0&&(u[p]=c(Math.round(u[p]+.5)-.5,e,0)),u[p]+=t+l*v;for(let p=1;p<u.length;p+=2)u[p]*=r,h&&u[p]!==0&&(u[p]=c(Math.round(u[p]+.5)-.5,r,0)),u[p]+=i;return u}},56:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.observeDevicePixelDimensions=void 0;const _=a(859);s.observeDevicePixelDimensions=function(d,n,c){let f=new n.ResizeObserver(m=>{const o=m.find(t=>t.target===d);if(!o)return;if(!("devicePixelContentBoxSize"in o))return f==null||f.disconnect(),void(f=void 0);const e=o.devicePixelContentBoxSize[0].inlineSize,r=o.devicePixelContentBoxSize[0].blockSize;e>0&&r>0&&c(e,r)});try{f.observe(d,{box:["device-pixel-content-box"]})}catch{f.disconnect(),f=void 0}return(0,_.toDisposable)(()=>f==null?void 0:f.disconnect())}},374:(B,s)=>{function a(d){return 57508<=d&&d<=57558}function _(d){return d>=128512&&d<=128591||d>=127744&&d<=128511||d>=128640&&d<=128767||d>=9728&&d<=9983||d>=9984&&d<=10175||d>=65024&&d<=65039||d>=129280&&d<=129535||d>=127462&&d<=127487}Object.defineProperty(s,"__esModule",{value:!0}),s.computeNextVariantOffset=s.createRenderDimensions=s.treatGlyphAsBackgroundColor=s.allowRescaling=s.isEmoji=s.isRestrictedPowerlineGlyph=s.isPowerlineGlyph=s.throwIfFalsy=void 0,s.throwIfFalsy=function(d){if(!d)throw new Error("value must not be falsy");return d},s.isPowerlineGlyph=a,s.isRestrictedPowerlineGlyph=function(d){return 57520<=d&&d<=57527},s.isEmoji=_,s.allowRescaling=function(d,n,c,f){return n===1&&c>Math.ceil(1.5*f)&&d!==void 0&&d>255&&!_(d)&&!a(d)&&!function(m){return 57344<=m&&m<=63743}(d)},s.treatGlyphAsBackgroundColor=function(d){return a(d)||function(n){return 9472<=n&&n<=9631}(d)},s.createRenderDimensions=function(){return{css:{canvas:{width:0,height:0},cell:{width:0,height:0}},device:{canvas:{width:0,height:0},cell:{width:0,height:0},char:{width:0,height:0,left:0,top:0}}}},s.computeNextVariantOffset=function(d,n,c=0){return(d-(2*Math.round(n)-c))%(2*Math.round(n))}},296:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.createSelectionRenderModel=void 0;class a{constructor(){this.clear()}clear(){this.hasSelection=!1,this.columnSelectMode=!1,this.viewportStartRow=0,this.viewportEndRow=0,this.viewportCappedStartRow=0,this.viewportCappedEndRow=0,this.startCol=0,this.endCol=0,this.selectionStart=void 0,this.selectionEnd=void 0}update(d,n,c,f=!1){if(this.selectionStart=n,this.selectionEnd=c,!n||!c||n[0]===c[0]&&n[1]===c[1])return void this.clear();const m=d.buffers.active.ydisp,o=n[1]-m,e=c[1]-m,r=Math.max(o,0),t=Math.min(e,d.rows-1);r>=d.rows||t<0?this.clear():(this.hasSelection=!0,this.columnSelectMode=f,this.viewportStartRow=o,this.viewportEndRow=e,this.viewportCappedStartRow=r,this.viewportCappedEndRow=t,this.startCol=n[0],this.endCol=c[0])}isCellSelected(d,n,c){return!!this.hasSelection&&(c-=d.buffer.active.viewportY,this.columnSelectMode?this.startCol<=this.endCol?n>=this.startCol&&c>=this.viewportCappedStartRow&&n<this.endCol&&c<=this.viewportCappedEndRow:n<this.startCol&&c>=this.viewportCappedStartRow&&n>=this.endCol&&c<=this.viewportCappedEndRow:c>this.viewportStartRow&&c<this.viewportEndRow||this.viewportStartRow===this.viewportEndRow&&c===this.viewportStartRow&&n>=this.startCol&&n<this.endCol||this.viewportStartRow<this.viewportEndRow&&c===this.viewportEndRow&&n<this.endCol||this.viewportStartRow<this.viewportEndRow&&c===this.viewportStartRow&&n>=this.startCol)}}s.createSelectionRenderModel=function(){return new a}},509:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.TextureAtlas=void 0;const _=a(237),d=a(860),n=a(374),c=a(160),f=a(345),m=a(485),o=a(385),e=a(147),r=a(855),t={texturePage:0,texturePosition:{x:0,y:0},texturePositionClipSpace:{x:0,y:0},offset:{x:0,y:0},size:{x:0,y:0},sizeClipSpace:{x:0,y:0}};let i;class h{get pages(){return this._pages}constructor(p,C,b){this._document=p,this._config=C,this._unicodeService=b,this._didWarmUp=!1,this._cacheMap=new m.FourKeyMap,this._cacheMapCombined=new m.FourKeyMap,this._pages=[],this._activePages=[],this._workBoundingBox={top:0,left:0,bottom:0,right:0},this._workAttributeData=new e.AttributeData,this._textureSize=512,this._onAddTextureAtlasCanvas=new f.EventEmitter,this.onAddTextureAtlasCanvas=this._onAddTextureAtlasCanvas.event,this._onRemoveTextureAtlasCanvas=new f.EventEmitter,this.onRemoveTextureAtlasCanvas=this._onRemoveTextureAtlasCanvas.event,this._requestClearModel=!1,this._createNewPage(),this._tmpCanvas=g(p,4*this._config.deviceCellWidth+4,this._config.deviceCellHeight+4),this._tmpCtx=(0,n.throwIfFalsy)(this._tmpCanvas.getContext("2d",{alpha:this._config.allowTransparency,willReadFrequently:!0}))}dispose(){for(const p of this.pages)p.canvas.remove();this._onAddTextureAtlasCanvas.dispose()}warmUp(){this._didWarmUp||(this._doWarmUp(),this._didWarmUp=!0)}_doWarmUp(){const p=new o.IdleTaskQueue;for(let C=33;C<126;C++)p.enqueue(()=>{if(!this._cacheMap.get(C,r.DEFAULT_COLOR,r.DEFAULT_COLOR,r.DEFAULT_EXT)){const b=this._drawToCache(C,r.DEFAULT_COLOR,r.DEFAULT_COLOR,r.DEFAULT_EXT);this._cacheMap.set(C,r.DEFAULT_COLOR,r.DEFAULT_COLOR,r.DEFAULT_EXT,b)}})}beginFrame(){return this._requestClearModel}clearTexture(){if(this._pages[0].currentRow.x!==0||this._pages[0].currentRow.y!==0){for(const p of this._pages)p.clear();this._cacheMap.clear(),this._cacheMapCombined.clear(),this._didWarmUp=!1}}_createNewPage(){if(h.maxAtlasPages&&this._pages.length>=Math.max(4,h.maxAtlasPages)){const C=this._pages.filter(T=>2*T.canvas.width<=(h.maxTextureSize||4096)).sort((T,A)=>A.canvas.width!==T.canvas.width?A.canvas.width-T.canvas.width:A.percentageUsed-T.percentageUsed);let b=-1,y=0;for(let T=0;T<C.length;T++)if(C[T].canvas.width!==y)b=T,y=C[T].canvas.width;else if(T-b==3)break;const w=C.slice(b,b+4),k=w.map(T=>T.glyphs[0].texturePage).sort((T,A)=>T>A?1:-1),D=this.pages.length-w.length,P=this._mergePages(w,D);P.version++;for(let T=k.length-1;T>=0;T--)this._deletePage(k[T]);this.pages.push(P),this._requestClearModel=!0,this._onAddTextureAtlasCanvas.fire(P.canvas)}const p=new v(this._document,this._textureSize);return this._pages.push(p),this._activePages.push(p),this._onAddTextureAtlasCanvas.fire(p.canvas),p}_mergePages(p,C){const b=2*p[0].canvas.width,y=new v(this._document,b,p);for(const[w,k]of p.entries()){const D=w*k.canvas.width%b,P=Math.floor(w/2)*k.canvas.height;y.ctx.drawImage(k.canvas,D,P);for(const A of k.glyphs)A.texturePage=C,A.sizeClipSpace.x=A.size.x/b,A.sizeClipSpace.y=A.size.y/b,A.texturePosition.x+=D,A.texturePosition.y+=P,A.texturePositionClipSpace.x=A.texturePosition.x/b,A.texturePositionClipSpace.y=A.texturePosition.y/b;this._onRemoveTextureAtlasCanvas.fire(k.canvas);const T=this._activePages.indexOf(k);T!==-1&&this._activePages.splice(T,1)}return y}_deletePage(p){this._pages.splice(p,1);for(let C=p;C<this._pages.length;C++){const b=this._pages[C];for(const y of b.glyphs)y.texturePage--;b.version++}}getRasterizedGlyphCombinedChar(p,C,b,y,w){return this._getFromCacheMap(this._cacheMapCombined,p,C,b,y,w)}getRasterizedGlyph(p,C,b,y,w){return this._getFromCacheMap(this._cacheMap,p,C,b,y,w)}_getFromCacheMap(p,C,b,y,w,k=!1){return i=p.get(C,b,y,w),i||(i=this._drawToCache(C,b,y,w,k),p.set(C,b,y,w,i)),i}_getColorFromAnsiIndex(p){if(p>=this._config.colors.ansi.length)throw new Error("No color found for idx "+p);return this._config.colors.ansi[p]}_getBackgroundColor(p,C,b,y){if(this._config.allowTransparency)return c.NULL_COLOR;let w;switch(p){case 16777216:case 33554432:w=this._getColorFromAnsiIndex(C);break;case 50331648:const k=e.AttributeData.toColorRGB(C);w=c.channels.toColor(k[0],k[1],k[2]);break;default:w=b?c.color.opaque(this._config.colors.foreground):this._config.colors.background}return w}_getForegroundColor(p,C,b,y,w,k,D,P,T,A){const S=this._getMinimumContrastColor(p,C,b,y,w,k,D,T,P,A);if(S)return S;let x;switch(w){case 16777216:case 33554432:this._config.drawBoldTextInBrightColors&&T&&k<8&&(k+=8),x=this._getColorFromAnsiIndex(k);break;case 50331648:const E=e.AttributeData.toColorRGB(k);x=c.channels.toColor(E[0],E[1],E[2]);break;default:x=D?this._config.colors.background:this._config.colors.foreground}return this._config.allowTransparency&&(x=c.color.opaque(x)),P&&(x=c.color.multiplyOpacity(x,_.DIM_OPACITY)),x}_resolveBackgroundRgba(p,C,b){switch(p){case 16777216:case 33554432:return this._getColorFromAnsiIndex(C).rgba;case 50331648:return C<<8;default:return b?this._config.colors.foreground.rgba:this._config.colors.background.rgba}}_resolveForegroundRgba(p,C,b,y){switch(p){case 16777216:case 33554432:return this._config.drawBoldTextInBrightColors&&y&&C<8&&(C+=8),this._getColorFromAnsiIndex(C).rgba;case 50331648:return C<<8;default:return b?this._config.colors.background.rgba:this._config.colors.foreground.rgba}}_getMinimumContrastColor(p,C,b,y,w,k,D,P,T,A){if(this._config.minimumContrastRatio===1||A)return;const S=this._getContrastCache(T),x=S.getColor(p,y);if(x!==void 0)return x||void 0;const E=this._resolveBackgroundRgba(C,b,D),R=this._resolveForegroundRgba(w,k,D,P),I=c.rgba.ensureContrastRatio(E,R,this._config.minimumContrastRatio/(T?2:1));if(!I)return void S.setColor(p,y,null);const F=c.channels.toColor(I>>24&255,I>>16&255,I>>8&255);return S.setColor(p,y,F),F}_getContrastCache(p){return p?this._config.colors.halfContrastCache:this._config.colors.contrastCache}_drawToCache(p,C,b,y,w=!1){const k=typeof p=="number"?String.fromCharCode(p):p,D=Math.min(this._config.deviceCellWidth*Math.max(k.length,2)+4,this._textureSize);this._tmpCanvas.width<D&&(this._tmpCanvas.width=D);const P=Math.min(this._config.deviceCellHeight+8,this._textureSize);if(this._tmpCanvas.height<P&&(this._tmpCanvas.height=P),this._tmpCtx.save(),this._workAttributeData.fg=b,this._workAttributeData.bg=C,this._workAttributeData.extended.ext=y,this._workAttributeData.isInvisible())return t;const T=!!this._workAttributeData.isBold(),A=!!this._workAttributeData.isInverse(),S=!!this._workAttributeData.isDim(),x=!!this._workAttributeData.isItalic(),E=!!this._workAttributeData.isUnderline(),R=!!this._workAttributeData.isStrikethrough(),I=!!this._workAttributeData.isOverline();let F=this._workAttributeData.getFgColor(),N=this._workAttributeData.getFgColorMode(),z=this._workAttributeData.getBgColor(),M=this._workAttributeData.getBgColorMode();if(A){const U=F;F=z,z=U;const J=N;N=M,M=J}const L=this._getBackgroundColor(M,z,A,S);this._tmpCtx.globalCompositeOperation="copy",this._tmpCtx.fillStyle=L.css,this._tmpCtx.fillRect(0,0,this._tmpCanvas.width,this._tmpCanvas.height),this._tmpCtx.globalCompositeOperation="source-over";const H=T?this._config.fontWeightBold:this._config.fontWeight,O=x?"italic":"";this._tmpCtx.font=`${O} ${H} ${this._config.fontSize*this._config.devicePixelRatio}px ${this._config.fontFamily}`,this._tmpCtx.textBaseline=_.TEXT_BASELINE;const $=k.length===1&&(0,n.isPowerlineGlyph)(k.charCodeAt(0)),W=k.length===1&&(0,n.isRestrictedPowerlineGlyph)(k.charCodeAt(0)),K=this._getForegroundColor(C,M,z,b,N,F,A,S,T,(0,n.treatGlyphAsBackgroundColor)(k.charCodeAt(0)));this._tmpCtx.fillStyle=K.css;const q=W?0:4;let ee=!1;this._config.customGlyphs!==!1&&(ee=(0,d.tryDrawCustomChar)(this._tmpCtx,k,q,q,this._config.deviceCellWidth,this._config.deviceCellHeight,this._config.fontSize,this._config.devicePixelRatio));let re,j=!$;if(re=typeof p=="number"?this._unicodeService.wcwidth(p):this._unicodeService.getStringCellWidth(p),E){this._tmpCtx.save();const U=Math.max(1,Math.floor(this._config.fontSize*this._config.devicePixelRatio/15)),J=U%2==1?.5:0;if(this._tmpCtx.lineWidth=U,this._workAttributeData.isUnderlineColorDefault())this._tmpCtx.strokeStyle=this._tmpCtx.fillStyle;else if(this._workAttributeData.isUnderlineColorRGB())j=!1,this._tmpCtx.strokeStyle=`rgb(${e.AttributeData.toColorRGB(this._workAttributeData.getUnderlineColor()).join(",")})`;else{j=!1;let ne=this._workAttributeData.getUnderlineColor();this._config.drawBoldTextInBrightColors&&this._workAttributeData.isBold()&&ne<8&&(ne+=8),this._tmpCtx.strokeStyle=this._getColorFromAnsiIndex(ne).css}this._tmpCtx.beginPath();const se=q,ie=Math.ceil(q+this._config.deviceCharHeight)-J-(w?2*U:0),_e=ie+U,he=ie+2*U;let ce=this._workAttributeData.getUnderlineVariantOffset();for(let ne=0;ne<re;ne++){this._tmpCtx.save();const te=se+ne*this._config.deviceCellWidth,G=se+(ne+1)*this._config.deviceCellWidth,ge=te+this._config.deviceCellWidth/2;switch(this._workAttributeData.extended.underlineStyle){case 2:this._tmpCtx.moveTo(te,ie),this._tmpCtx.lineTo(G,ie),this._tmpCtx.moveTo(te,he),this._tmpCtx.lineTo(G,he);break;case 3:const pe=U<=1?he:Math.ceil(q+this._config.deviceCharHeight-U/2)-J,me=U<=1?ie:Math.ceil(q+this._config.deviceCharHeight+U/2)-J,xe=new Path2D;xe.rect(te,ie,this._config.deviceCellWidth,he-ie),this._tmpCtx.clip(xe),this._tmpCtx.moveTo(te-this._config.deviceCellWidth/2,_e),this._tmpCtx.bezierCurveTo(te-this._config.deviceCellWidth/2,me,te,me,te,_e),this._tmpCtx.bezierCurveTo(te,pe,ge,pe,ge,_e),this._tmpCtx.bezierCurveTo(ge,me,G,me,G,_e),this._tmpCtx.bezierCurveTo(G,pe,G+this._config.deviceCellWidth/2,pe,G+this._config.deviceCellWidth/2,_e);break;case 4:const Se=ce===0?0:ce>=U?2*U-ce:U-ce;ce>=U||Se===0?(this._tmpCtx.setLineDash([Math.round(U),Math.round(U)]),this._tmpCtx.moveTo(te+Se,ie),this._tmpCtx.lineTo(G,ie)):(this._tmpCtx.setLineDash([Math.round(U),Math.round(U)]),this._tmpCtx.moveTo(te,ie),this._tmpCtx.lineTo(te+Se,ie),this._tmpCtx.moveTo(te+Se+U,ie),this._tmpCtx.lineTo(G,ie)),ce=(0,n.computeNextVariantOffset)(G-te,U,ce);break;case 5:const Pe=.6,Ie=.3,Ce=G-te,Ee=Math.floor(Pe*Ce),ke=Math.floor(Ie*Ce),He=Ce-Ee-ke;this._tmpCtx.setLineDash([Ee,ke,He]),this._tmpCtx.moveTo(te,ie),this._tmpCtx.lineTo(G,ie);break;default:this._tmpCtx.moveTo(te,ie),this._tmpCtx.lineTo(G,ie)}this._tmpCtx.stroke(),this._tmpCtx.restore()}if(this._tmpCtx.restore(),!ee&&this._config.fontSize>=12&&!this._config.allowTransparency&&k!==" "){this._tmpCtx.save(),this._tmpCtx.textBaseline="alphabetic";const ne=this._tmpCtx.measureText(k);if(this._tmpCtx.restore(),"actualBoundingBoxDescent"in ne&&ne.actualBoundingBoxDescent>0){this._tmpCtx.save();const te=new Path2D;te.rect(se,ie-Math.ceil(U/2),this._config.deviceCellWidth*re,he-ie+Math.ceil(U/2)),this._tmpCtx.clip(te),this._tmpCtx.lineWidth=3*this._config.devicePixelRatio,this._tmpCtx.strokeStyle=L.css,this._tmpCtx.strokeText(k,q,q+this._config.deviceCharHeight),this._tmpCtx.restore()}}}if(I){const U=Math.max(1,Math.floor(this._config.fontSize*this._config.devicePixelRatio/15)),J=U%2==1?.5:0;this._tmpCtx.lineWidth=U,this._tmpCtx.strokeStyle=this._tmpCtx.fillStyle,this._tmpCtx.beginPath(),this._tmpCtx.moveTo(q,q+J),this._tmpCtx.lineTo(q+this._config.deviceCharWidth*re,q+J),this._tmpCtx.stroke()}if(ee||this._tmpCtx.fillText(k,q,q+this._config.deviceCharHeight),k==="_"&&!this._config.allowTransparency){let U=l(this._tmpCtx.getImageData(q,q,this._config.deviceCellWidth,this._config.deviceCellHeight),L,K,j);if(U)for(let J=1;J<=5&&(this._tmpCtx.save(),this._tmpCtx.fillStyle=L.css,this._tmpCtx.fillRect(0,0,this._tmpCanvas.width,this._tmpCanvas.height),this._tmpCtx.restore(),this._tmpCtx.fillText(k,q,q+this._config.deviceCharHeight-J),U=l(this._tmpCtx.getImageData(q,q,this._config.deviceCellWidth,this._config.deviceCellHeight),L,K,j),U);J++);}if(R){const U=Math.max(1,Math.floor(this._config.fontSize*this._config.devicePixelRatio/10)),J=this._tmpCtx.lineWidth%2==1?.5:0;this._tmpCtx.lineWidth=U,this._tmpCtx.strokeStyle=this._tmpCtx.fillStyle,this._tmpCtx.beginPath(),this._tmpCtx.moveTo(q,q+Math.floor(this._config.deviceCharHeight/2)-J),this._tmpCtx.lineTo(q+this._config.deviceCharWidth*re,q+Math.floor(this._config.deviceCharHeight/2)-J),this._tmpCtx.stroke()}this._tmpCtx.restore();const le=this._tmpCtx.getImageData(0,0,this._tmpCanvas.width,this._tmpCanvas.height);let ue;if(ue=this._config.allowTransparency?function(U){for(let J=0;J<U.data.length;J+=4)if(U.data[J+3]>0)return!1;return!0}(le):l(le,L,K,j),ue)return t;const Z=this._findGlyphBoundingBox(le,this._workBoundingBox,D,W,ee,q);let V,X;for(;;){if(this._activePages.length===0){const U=this._createNewPage();V=U,X=U.currentRow,X.height=Z.size.y;break}V=this._activePages[this._activePages.length-1],X=V.currentRow;for(const U of this._activePages)Z.size.y<=U.currentRow.height&&(V=U,X=U.currentRow);for(let U=this._activePages.length-1;U>=0;U--)for(const J of this._activePages[U].fixedRows)J.height<=X.height&&Z.size.y<=J.height&&(V=this._activePages[U],X=J);if(X.y+Z.size.y>=V.canvas.height||X.height>Z.size.y+2){let U=!1;if(V.currentRow.y+V.currentRow.height+Z.size.y>=V.canvas.height){let J;for(const se of this._activePages)if(se.currentRow.y+se.currentRow.height+Z.size.y<se.canvas.height){J=se;break}if(J)V=J;else if(h.maxAtlasPages&&this._pages.length>=h.maxAtlasPages&&X.y+Z.size.y<=V.canvas.height&&X.height>=Z.size.y&&X.x+Z.size.x<=V.canvas.width)U=!0;else{const se=this._createNewPage();V=se,X=se.currentRow,X.height=Z.size.y,U=!0}}U||(V.currentRow.height>0&&V.fixedRows.push(V.currentRow),X={x:0,y:V.currentRow.y+V.currentRow.height,height:Z.size.y},V.fixedRows.push(X),V.currentRow={x:0,y:X.y+X.height,height:0})}if(X.x+Z.size.x<=V.canvas.width)break;X===V.currentRow?(X.x=0,X.y+=X.height,X.height=0):V.fixedRows.splice(V.fixedRows.indexOf(X),1)}return Z.texturePage=this._pages.indexOf(V),Z.texturePosition.x=X.x,Z.texturePosition.y=X.y,Z.texturePositionClipSpace.x=X.x/V.canvas.width,Z.texturePositionClipSpace.y=X.y/V.canvas.height,Z.sizeClipSpace.x/=V.canvas.width,Z.sizeClipSpace.y/=V.canvas.height,X.height=Math.max(X.height,Z.size.y),X.x+=Z.size.x,V.ctx.putImageData(le,Z.texturePosition.x-this._workBoundingBox.left,Z.texturePosition.y-this._workBoundingBox.top,this._workBoundingBox.left,this._workBoundingBox.top,Z.size.x,Z.size.y),V.addGlyph(Z),V.version++,Z}_findGlyphBoundingBox(p,C,b,y,w,k){C.top=0;const D=y?this._config.deviceCellHeight:this._tmpCanvas.height,P=y?this._config.deviceCellWidth:b;let T=!1;for(let A=0;A<D;A++){for(let S=0;S<P;S++){const x=A*this._tmpCanvas.width*4+4*S+3;if(p.data[x]!==0){C.top=A,T=!0;break}}if(T)break}C.left=0,T=!1;for(let A=0;A<k+P;A++){for(let S=0;S<D;S++){const x=S*this._tmpCanvas.width*4+4*A+3;if(p.data[x]!==0){C.left=A,T=!0;break}}if(T)break}C.right=P,T=!1;for(let A=k+P-1;A>=k;A--){for(let S=0;S<D;S++){const x=S*this._tmpCanvas.width*4+4*A+3;if(p.data[x]!==0){C.right=A,T=!0;break}}if(T)break}C.bottom=D,T=!1;for(let A=D-1;A>=0;A--){for(let S=0;S<P;S++){const x=A*this._tmpCanvas.width*4+4*S+3;if(p.data[x]!==0){C.bottom=A,T=!0;break}}if(T)break}return{texturePage:0,texturePosition:{x:0,y:0},texturePositionClipSpace:{x:0,y:0},size:{x:C.right-C.left+1,y:C.bottom-C.top+1},sizeClipSpace:{x:C.right-C.left+1,y:C.bottom-C.top+1},offset:{x:-C.left+k+(y||w?Math.floor((this._config.deviceCellWidth-this._config.deviceCharWidth)/2):0),y:-C.top+k+(y||w?this._config.lineHeight===1?0:Math.round((this._config.deviceCellHeight-this._config.deviceCharHeight)/2):0)}}}}s.TextureAtlas=h;class v{get percentageUsed(){return this._usedPixels/(this.canvas.width*this.canvas.height)}get glyphs(){return this._glyphs}addGlyph(p){this._glyphs.push(p),this._usedPixels+=p.size.x*p.size.y}constructor(p,C,b){if(this._usedPixels=0,this._glyphs=[],this.version=0,this.currentRow={x:0,y:0,height:0},this.fixedRows=[],b)for(const y of b)this._glyphs.push(...y.glyphs),this._usedPixels+=y._usedPixels;this.canvas=g(p,C,C),this.ctx=(0,n.throwIfFalsy)(this.canvas.getContext("2d",{alpha:!0}))}clear(){this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.currentRow.x=0,this.currentRow.y=0,this.currentRow.height=0,this.fixedRows.length=0,this.version++}}function l(u,p,C,b){const y=p.rgba>>>24,w=p.rgba>>>16&255,k=p.rgba>>>8&255,D=C.rgba>>>24,P=C.rgba>>>16&255,T=C.rgba>>>8&255,A=Math.floor((Math.abs(y-D)+Math.abs(w-P)+Math.abs(k-T))/12);let S=!0;for(let x=0;x<u.data.length;x+=4)u.data[x]===y&&u.data[x+1]===w&&u.data[x+2]===k||b&&Math.abs(u.data[x]-y)+Math.abs(u.data[x+1]-w)+Math.abs(u.data[x+2]-k)<A?u.data[x+3]=0:S=!1;return S}function g(u,p,C){const b=u.createElement("canvas");return b.width=p,b.height=C,b}},577:function(B,s,a){var _=this&&this.__decorate||function(r,t,i,h){var v,l=arguments.length,g=l<3?t:h===null?h=Object.getOwnPropertyDescriptor(t,i):h;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")g=Reflect.decorate(r,t,i,h);else for(var u=r.length-1;u>=0;u--)(v=r[u])&&(g=(l<3?v(g):l>3?v(t,i,g):v(t,i))||g);return l>3&&g&&Object.defineProperty(t,i,g),g},d=this&&this.__param||function(r,t){return function(i,h){t(i,h,r)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CharacterJoinerService=s.JoinedCellData=void 0;const n=a(147),c=a(855),f=a(782),m=a(97);class o extends n.AttributeData{constructor(t,i,h){super(),this.content=0,this.combinedData="",this.fg=t.fg,this.bg=t.bg,this.combinedData=i,this._width=h}isCombined(){return 2097152}getWidth(){return this._width}getChars(){return this.combinedData}getCode(){return 2097151}setFromCharData(t){throw new Error("not implemented")}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}s.JoinedCellData=o;let e=s.CharacterJoinerService=class Te{constructor(t){this._bufferService=t,this._characterJoiners=[],this._nextCharacterJoinerId=0,this._workCell=new f.CellData}register(t){const i={id:this._nextCharacterJoinerId++,handler:t};return this._characterJoiners.push(i),i.id}deregister(t){for(let i=0;i<this._characterJoiners.length;i++)if(this._characterJoiners[i].id===t)return this._characterJoiners.splice(i,1),!0;return!1}getJoinedCharacters(t){if(this._characterJoiners.length===0)return[];const i=this._bufferService.buffer.lines.get(t);if(!i||i.length===0)return[];const h=[],v=i.translateToString(!0);let l=0,g=0,u=0,p=i.getFg(0),C=i.getBg(0);for(let b=0;b<i.getTrimmedLength();b++)if(i.loadCell(b,this._workCell),this._workCell.getWidth()!==0){if(this._workCell.fg!==p||this._workCell.bg!==C){if(b-l>1){const y=this._getJoinedRanges(v,u,g,i,l);for(let w=0;w<y.length;w++)h.push(y[w])}l=b,u=g,p=this._workCell.fg,C=this._workCell.bg}g+=this._workCell.getChars().length||c.WHITESPACE_CELL_CHAR.length}if(this._bufferService.cols-l>1){const b=this._getJoinedRanges(v,u,g,i,l);for(let y=0;y<b.length;y++)h.push(b[y])}return h}_getJoinedRanges(t,i,h,v,l){const g=t.substring(i,h);let u=[];try{u=this._characterJoiners[0].handler(g)}catch(p){console.error(p)}for(let p=1;p<this._characterJoiners.length;p++)try{const C=this._characterJoiners[p].handler(g);for(let b=0;b<C.length;b++)Te._mergeRanges(u,C[b])}catch(C){console.error(C)}return this._stringRangesToCellRanges(u,v,l),u}_stringRangesToCellRanges(t,i,h){let v=0,l=!1,g=0,u=t[v];if(u){for(let p=h;p<this._bufferService.cols;p++){const C=i.getWidth(p),b=i.getString(p).length||c.WHITESPACE_CELL_CHAR.length;if(C!==0){if(!l&&u[0]<=g&&(u[0]=p,l=!0),u[1]<=g){if(u[1]=p,u=t[++v],!u)break;u[0]<=g?(u[0]=p,l=!0):l=!1}g+=b}}u&&(u[1]=this._bufferService.cols)}}static _mergeRanges(t,i){let h=!1;for(let v=0;v<t.length;v++){const l=t[v];if(h){if(i[1]<=l[0])return t[v-1][1]=i[1],t;if(i[1]<=l[1])return t[v-1][1]=Math.max(i[1],l[1]),t.splice(v,1),t;t.splice(v,1),v--}else{if(i[1]<=l[0])return t.splice(v,0,i),t;if(i[1]<=l[1])return l[0]=Math.min(i[0],l[0]),t;i[0]<l[1]&&(l[0]=Math.min(i[0],l[0]),h=!0)}}return h?t[t.length-1][1]=i[1]:t.push(i),t}};s.CharacterJoinerService=e=_([d(0,m.IBufferService)],e)},160:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.contrastRatio=s.toPaddedHex=s.rgba=s.rgb=s.css=s.color=s.channels=s.NULL_COLOR=void 0;let a=0,_=0,d=0,n=0;var c,f,m,o,e;function r(i){const h=i.toString(16);return h.length<2?"0"+h:h}function t(i,h){return i<h?(h+.05)/(i+.05):(i+.05)/(h+.05)}s.NULL_COLOR={css:"#00000000",rgba:0},function(i){i.toCss=function(h,v,l,g){return g!==void 0?`#${r(h)}${r(v)}${r(l)}${r(g)}`:`#${r(h)}${r(v)}${r(l)}`},i.toRgba=function(h,v,l,g=255){return(h<<24|v<<16|l<<8|g)>>>0},i.toColor=function(h,v,l,g){return{css:i.toCss(h,v,l,g),rgba:i.toRgba(h,v,l,g)}}}(c||(s.channels=c={})),function(i){function h(v,l){return n=Math.round(255*l),[a,_,d]=e.toChannels(v.rgba),{css:c.toCss(a,_,d,n),rgba:c.toRgba(a,_,d,n)}}i.blend=function(v,l){if(n=(255&l.rgba)/255,n===1)return{css:l.css,rgba:l.rgba};const g=l.rgba>>24&255,u=l.rgba>>16&255,p=l.rgba>>8&255,C=v.rgba>>24&255,b=v.rgba>>16&255,y=v.rgba>>8&255;return a=C+Math.round((g-C)*n),_=b+Math.round((u-b)*n),d=y+Math.round((p-y)*n),{css:c.toCss(a,_,d),rgba:c.toRgba(a,_,d)}},i.isOpaque=function(v){return(255&v.rgba)==255},i.ensureContrastRatio=function(v,l,g){const u=e.ensureContrastRatio(v.rgba,l.rgba,g);if(u)return c.toColor(u>>24&255,u>>16&255,u>>8&255)},i.opaque=function(v){const l=(255|v.rgba)>>>0;return[a,_,d]=e.toChannels(l),{css:c.toCss(a,_,d),rgba:l}},i.opacity=h,i.multiplyOpacity=function(v,l){return n=255&v.rgba,h(v,n*l/255)},i.toColorRGB=function(v){return[v.rgba>>24&255,v.rgba>>16&255,v.rgba>>8&255]}}(f||(s.color=f={})),function(i){let h,v;try{const l=document.createElement("canvas");l.width=1,l.height=1;const g=l.getContext("2d",{willReadFrequently:!0});g&&(h=g,h.globalCompositeOperation="copy",v=h.createLinearGradient(0,0,1,1))}catch{}i.toColor=function(l){if(l.match(/#[\da-f]{3,8}/i))switch(l.length){case 4:return a=parseInt(l.slice(1,2).repeat(2),16),_=parseInt(l.slice(2,3).repeat(2),16),d=parseInt(l.slice(3,4).repeat(2),16),c.toColor(a,_,d);case 5:return a=parseInt(l.slice(1,2).repeat(2),16),_=parseInt(l.slice(2,3).repeat(2),16),d=parseInt(l.slice(3,4).repeat(2),16),n=parseInt(l.slice(4,5).repeat(2),16),c.toColor(a,_,d,n);case 7:return{css:l,rgba:(parseInt(l.slice(1),16)<<8|255)>>>0};case 9:return{css:l,rgba:parseInt(l.slice(1),16)>>>0}}const g=l.match(/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*(0|1|\d?\.(\d+))\s*)?\)/);if(g)return a=parseInt(g[1]),_=parseInt(g[2]),d=parseInt(g[3]),n=Math.round(255*(g[5]===void 0?1:parseFloat(g[5]))),c.toColor(a,_,d,n);if(!h||!v)throw new Error("css.toColor: Unsupported css format");if(h.fillStyle=v,h.fillStyle=l,typeof h.fillStyle!="string")throw new Error("css.toColor: Unsupported css format");if(h.fillRect(0,0,1,1),[a,_,d,n]=h.getImageData(0,0,1,1).data,n!==255)throw new Error("css.toColor: Unsupported css format");return{rgba:c.toRgba(a,_,d,n),css:l}}}(m||(s.css=m={})),function(i){function h(v,l,g){const u=v/255,p=l/255,C=g/255;return .2126*(u<=.03928?u/12.92:Math.pow((u+.055)/1.055,2.4))+.7152*(p<=.03928?p/12.92:Math.pow((p+.055)/1.055,2.4))+.0722*(C<=.03928?C/12.92:Math.pow((C+.055)/1.055,2.4))}i.relativeLuminance=function(v){return h(v>>16&255,v>>8&255,255&v)},i.relativeLuminance2=h}(o||(s.rgb=o={})),function(i){function h(l,g,u){const p=l>>24&255,C=l>>16&255,b=l>>8&255;let y=g>>24&255,w=g>>16&255,k=g>>8&255,D=t(o.relativeLuminance2(y,w,k),o.relativeLuminance2(p,C,b));for(;D<u&&(y>0||w>0||k>0);)y-=Math.max(0,Math.ceil(.1*y)),w-=Math.max(0,Math.ceil(.1*w)),k-=Math.max(0,Math.ceil(.1*k)),D=t(o.relativeLuminance2(y,w,k),o.relativeLuminance2(p,C,b));return(y<<24|w<<16|k<<8|255)>>>0}function v(l,g,u){const p=l>>24&255,C=l>>16&255,b=l>>8&255;let y=g>>24&255,w=g>>16&255,k=g>>8&255,D=t(o.relativeLuminance2(y,w,k),o.relativeLuminance2(p,C,b));for(;D<u&&(y<255||w<255||k<255);)y=Math.min(255,y+Math.ceil(.1*(255-y))),w=Math.min(255,w+Math.ceil(.1*(255-w))),k=Math.min(255,k+Math.ceil(.1*(255-k))),D=t(o.relativeLuminance2(y,w,k),o.relativeLuminance2(p,C,b));return(y<<24|w<<16|k<<8|255)>>>0}i.blend=function(l,g){if(n=(255&g)/255,n===1)return g;const u=g>>24&255,p=g>>16&255,C=g>>8&255,b=l>>24&255,y=l>>16&255,w=l>>8&255;return a=b+Math.round((u-b)*n),_=y+Math.round((p-y)*n),d=w+Math.round((C-w)*n),c.toRgba(a,_,d)},i.ensureContrastRatio=function(l,g,u){const p=o.relativeLuminance(l>>8),C=o.relativeLuminance(g>>8);if(t(p,C)<u){if(C<p){const w=h(l,g,u),k=t(p,o.relativeLuminance(w>>8));if(k<u){const D=v(l,g,u);return k>t(p,o.relativeLuminance(D>>8))?w:D}return w}const b=v(l,g,u),y=t(p,o.relativeLuminance(b>>8));if(y<u){const w=h(l,g,u);return y>t(p,o.relativeLuminance(w>>8))?b:w}return b}},i.reduceLuminance=h,i.increaseLuminance=v,i.toChannels=function(l){return[l>>24&255,l>>16&255,l>>8&255,255&l]}}(e||(s.rgba=e={})),s.toPaddedHex=r,s.contrastRatio=t},345:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.runAndSubscribe=s.forwardEvent=s.EventEmitter=void 0,s.EventEmitter=class{constructor(){this._listeners=[],this._disposed=!1}get event(){return this._event||(this._event=a=>(this._listeners.push(a),{dispose:()=>{if(!this._disposed){for(let _=0;_<this._listeners.length;_++)if(this._listeners[_]===a)return void this._listeners.splice(_,1)}}})),this._event}fire(a,_){const d=[];for(let n=0;n<this._listeners.length;n++)d.push(this._listeners[n]);for(let n=0;n<d.length;n++)d[n].call(void 0,a,_)}dispose(){this.clearListeners(),this._disposed=!0}clearListeners(){this._listeners&&(this._listeners.length=0)}},s.forwardEvent=function(a,_){return a(d=>_.fire(d))},s.runAndSubscribe=function(a,_){return _(void 0),a(d=>_(d))}},859:(B,s)=>{function a(_){for(const d of _)d.dispose();_.length=0}Object.defineProperty(s,"__esModule",{value:!0}),s.getDisposeArrayDisposable=s.disposeArray=s.toDisposable=s.MutableDisposable=s.Disposable=void 0,s.Disposable=class{constructor(){this._disposables=[],this._isDisposed=!1}dispose(){this._isDisposed=!0;for(const _ of this._disposables)_.dispose();this._disposables.length=0}register(_){return this._disposables.push(_),_}unregister(_){const d=this._disposables.indexOf(_);d!==-1&&this._disposables.splice(d,1)}},s.MutableDisposable=class{constructor(){this._isDisposed=!1}get value(){return this._isDisposed?void 0:this._value}set value(_){var d;this._isDisposed||_===this._value||((d=this._value)==null||d.dispose(),this._value=_)}clear(){this.value=void 0}dispose(){var _;this._isDisposed=!0,(_=this._value)==null||_.dispose(),this._value=void 0}},s.toDisposable=function(_){return{dispose:_}},s.disposeArray=a,s.getDisposeArrayDisposable=function(_){return{dispose:()=>a(_)}}},485:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.FourKeyMap=s.TwoKeyMap=void 0;class a{constructor(){this._data={}}set(d,n,c){this._data[d]||(this._data[d]={}),this._data[d][n]=c}get(d,n){return this._data[d]?this._data[d][n]:void 0}clear(){this._data={}}}s.TwoKeyMap=a,s.FourKeyMap=class{constructor(){this._data=new a}set(_,d,n,c,f){this._data.get(_,d)||this._data.set(_,d,new a),this._data.get(_,d).set(n,c,f)}get(_,d,n,c){var f;return(f=this._data.get(_,d))==null?void 0:f.get(n,c)}clear(){this._data.clear()}}},399:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.isChromeOS=s.isLinux=s.isWindows=s.isIphone=s.isIpad=s.isMac=s.getSafariVersion=s.isSafari=s.isLegacyEdge=s.isFirefox=s.isNode=void 0,s.isNode=typeof process<"u"&&"title"in process;const a=s.isNode?"node":navigator.userAgent,_=s.isNode?"node":navigator.platform;s.isFirefox=a.includes("Firefox"),s.isLegacyEdge=a.includes("Edge"),s.isSafari=/^((?!chrome|android).)*safari/i.test(a),s.getSafariVersion=function(){if(!s.isSafari)return 0;const d=a.match(/Version\/(\d+)/);return d===null||d.length<2?0:parseInt(d[1])},s.isMac=["Macintosh","MacIntel","MacPPC","Mac68K"].includes(_),s.isIpad=_==="iPad",s.isIphone=_==="iPhone",s.isWindows=["Windows","Win16","Win32","WinCE"].includes(_),s.isLinux=_.indexOf("Linux")>=0,s.isChromeOS=/\bCrOS\b/.test(a)},385:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DebouncedIdleTask=s.IdleTaskQueue=s.PriorityTaskQueue=void 0;const _=a(399);class d{constructor(){this._tasks=[],this._i=0}enqueue(f){this._tasks.push(f),this._start()}flush(){for(;this._i<this._tasks.length;)this._tasks[this._i]()||this._i++;this.clear()}clear(){this._idleCallback&&(this._cancelCallback(this._idleCallback),this._idleCallback=void 0),this._i=0,this._tasks.length=0}_start(){this._idleCallback||(this._idleCallback=this._requestCallback(this._process.bind(this)))}_process(f){this._idleCallback=void 0;let m=0,o=0,e=f.timeRemaining(),r=0;for(;this._i<this._tasks.length;){if(m=Date.now(),this._tasks[this._i]()||this._i++,m=Math.max(1,Date.now()-m),o=Math.max(m,o),r=f.timeRemaining(),1.5*o>r)return e-m<-20&&console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(e-m))}ms`),void this._start();e=r}this.clear()}}class n extends d{_requestCallback(f){return setTimeout(()=>f(this._createDeadline(16)))}_cancelCallback(f){clearTimeout(f)}_createDeadline(f){const m=Date.now()+f;return{timeRemaining:()=>Math.max(0,m-Date.now())}}}s.PriorityTaskQueue=n,s.IdleTaskQueue=!_.isNode&&"requestIdleCallback"in window?class extends d{_requestCallback(c){return requestIdleCallback(c)}_cancelCallback(c){cancelIdleCallback(c)}}:n,s.DebouncedIdleTask=class{constructor(){this._queue=new s.IdleTaskQueue}set(c){this._queue.clear(),this._queue.enqueue(c)}flush(){this._queue.flush()}}},147:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ExtendedAttrs=s.AttributeData=void 0;class a{constructor(){this.fg=0,this.bg=0,this.extended=new _}static toColorRGB(n){return[n>>>16&255,n>>>8&255,255&n]}static fromColorRGB(n){return(255&n[0])<<16|(255&n[1])<<8|255&n[2]}clone(){const n=new a;return n.fg=this.fg,n.bg=this.bg,n.extended=this.extended.clone(),n}isInverse(){return 67108864&this.fg}isBold(){return 134217728&this.fg}isUnderline(){return this.hasExtendedAttrs()&&this.extended.underlineStyle!==0?1:268435456&this.fg}isBlink(){return 536870912&this.fg}isInvisible(){return 1073741824&this.fg}isItalic(){return 67108864&this.bg}isDim(){return 134217728&this.bg}isStrikethrough(){return 2147483648&this.fg}isProtected(){return 536870912&this.bg}isOverline(){return 1073741824&this.bg}getFgColorMode(){return 50331648&this.fg}getBgColorMode(){return 50331648&this.bg}isFgRGB(){return(50331648&this.fg)==50331648}isBgRGB(){return(50331648&this.bg)==50331648}isFgPalette(){return(50331648&this.fg)==16777216||(50331648&this.fg)==33554432}isBgPalette(){return(50331648&this.bg)==16777216||(50331648&this.bg)==33554432}isFgDefault(){return(50331648&this.fg)==0}isBgDefault(){return(50331648&this.bg)==0}isAttributeDefault(){return this.fg===0&&this.bg===0}getFgColor(){switch(50331648&this.fg){case 16777216:case 33554432:return 255&this.fg;case 50331648:return 16777215&this.fg;default:return-1}}getBgColor(){switch(50331648&this.bg){case 16777216:case 33554432:return 255&this.bg;case 50331648:return 16777215&this.bg;default:return-1}}hasExtendedAttrs(){return 268435456&this.bg}updateExtended(){this.extended.isEmpty()?this.bg&=-268435457:this.bg|=268435456}getUnderlineColor(){if(268435456&this.bg&&~this.extended.underlineColor)switch(50331648&this.extended.underlineColor){case 16777216:case 33554432:return 255&this.extended.underlineColor;case 50331648:return 16777215&this.extended.underlineColor;default:return this.getFgColor()}return this.getFgColor()}getUnderlineColorMode(){return 268435456&this.bg&&~this.extended.underlineColor?50331648&this.extended.underlineColor:this.getFgColorMode()}isUnderlineColorRGB(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==50331648:this.isFgRGB()}isUnderlineColorPalette(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==16777216||(50331648&this.extended.underlineColor)==33554432:this.isFgPalette()}isUnderlineColorDefault(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==0:this.isFgDefault()}getUnderlineStyle(){return 268435456&this.fg?268435456&this.bg?this.extended.underlineStyle:1:0}getUnderlineVariantOffset(){return this.extended.underlineVariantOffset}}s.AttributeData=a;class _{get ext(){return this._urlId?-469762049&this._ext|this.underlineStyle<<26:this._ext}set ext(n){this._ext=n}get underlineStyle(){return this._urlId?5:(469762048&this._ext)>>26}set underlineStyle(n){this._ext&=-469762049,this._ext|=n<<26&469762048}get underlineColor(){return 67108863&this._ext}set underlineColor(n){this._ext&=-67108864,this._ext|=67108863&n}get urlId(){return this._urlId}set urlId(n){this._urlId=n}get underlineVariantOffset(){const n=(3758096384&this._ext)>>29;return n<0?4294967288^n:n}set underlineVariantOffset(n){this._ext&=536870911,this._ext|=n<<29&3758096384}constructor(n=0,c=0){this._ext=0,this._urlId=0,this._ext=n,this._urlId=c}clone(){return new _(this._ext,this._urlId)}isEmpty(){return this.underlineStyle===0&&this._urlId===0}}s.ExtendedAttrs=_},782:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CellData=void 0;const _=a(133),d=a(855),n=a(147);class c extends n.AttributeData{constructor(){super(...arguments),this.content=0,this.fg=0,this.bg=0,this.extended=new n.ExtendedAttrs,this.combinedData=""}static fromCharData(m){const o=new c;return o.setFromCharData(m),o}isCombined(){return 2097152&this.content}getWidth(){return this.content>>22}getChars(){return 2097152&this.content?this.combinedData:2097151&this.content?(0,_.stringFromCodePoint)(2097151&this.content):""}getCode(){return this.isCombined()?this.combinedData.charCodeAt(this.combinedData.length-1):2097151&this.content}setFromCharData(m){this.fg=m[d.CHAR_DATA_ATTR_INDEX],this.bg=0;let o=!1;if(m[d.CHAR_DATA_CHAR_INDEX].length>2)o=!0;else if(m[d.CHAR_DATA_CHAR_INDEX].length===2){const e=m[d.CHAR_DATA_CHAR_INDEX].charCodeAt(0);if(55296<=e&&e<=56319){const r=m[d.CHAR_DATA_CHAR_INDEX].charCodeAt(1);56320<=r&&r<=57343?this.content=1024*(e-55296)+r-56320+65536|m[d.CHAR_DATA_WIDTH_INDEX]<<22:o=!0}else o=!0}else this.content=m[d.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|m[d.CHAR_DATA_WIDTH_INDEX]<<22;o&&(this.combinedData=m[d.CHAR_DATA_CHAR_INDEX],this.content=2097152|m[d.CHAR_DATA_WIDTH_INDEX]<<22)}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}s.CellData=c},855:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.WHITESPACE_CELL_CODE=s.WHITESPACE_CELL_WIDTH=s.WHITESPACE_CELL_CHAR=s.NULL_CELL_CODE=s.NULL_CELL_WIDTH=s.NULL_CELL_CHAR=s.CHAR_DATA_CODE_INDEX=s.CHAR_DATA_WIDTH_INDEX=s.CHAR_DATA_CHAR_INDEX=s.CHAR_DATA_ATTR_INDEX=s.DEFAULT_EXT=s.DEFAULT_ATTR=s.DEFAULT_COLOR=void 0,s.DEFAULT_COLOR=0,s.DEFAULT_ATTR=256|s.DEFAULT_COLOR<<9,s.DEFAULT_EXT=0,s.CHAR_DATA_ATTR_INDEX=0,s.CHAR_DATA_CHAR_INDEX=1,s.CHAR_DATA_WIDTH_INDEX=2,s.CHAR_DATA_CODE_INDEX=3,s.NULL_CELL_CHAR="",s.NULL_CELL_WIDTH=1,s.NULL_CELL_CODE=0,s.WHITESPACE_CELL_CHAR=" ",s.WHITESPACE_CELL_WIDTH=1,s.WHITESPACE_CELL_CODE=32},133:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Utf8ToUtf32=s.StringToUtf32=s.utf32ToString=s.stringFromCodePoint=void 0,s.stringFromCodePoint=function(a){return a>65535?(a-=65536,String.fromCharCode(55296+(a>>10))+String.fromCharCode(a%1024+56320)):String.fromCharCode(a)},s.utf32ToString=function(a,_=0,d=a.length){let n="";for(let c=_;c<d;++c){let f=a[c];f>65535?(f-=65536,n+=String.fromCharCode(55296+(f>>10))+String.fromCharCode(f%1024+56320)):n+=String.fromCharCode(f)}return n},s.StringToUtf32=class{constructor(){this._interim=0}clear(){this._interim=0}decode(a,_){const d=a.length;if(!d)return 0;let n=0,c=0;if(this._interim){const f=a.charCodeAt(c++);56320<=f&&f<=57343?_[n++]=1024*(this._interim-55296)+f-56320+65536:(_[n++]=this._interim,_[n++]=f),this._interim=0}for(let f=c;f<d;++f){const m=a.charCodeAt(f);if(55296<=m&&m<=56319){if(++f>=d)return this._interim=m,n;const o=a.charCodeAt(f);56320<=o&&o<=57343?_[n++]=1024*(m-55296)+o-56320+65536:(_[n++]=m,_[n++]=o)}else m!==65279&&(_[n++]=m)}return n}},s.Utf8ToUtf32=class{constructor(){this.interim=new Uint8Array(3)}clear(){this.interim.fill(0)}decode(a,_){const d=a.length;if(!d)return 0;let n,c,f,m,o=0,e=0,r=0;if(this.interim[0]){let h=!1,v=this.interim[0];v&=(224&v)==192?31:(240&v)==224?15:7;let l,g=0;for(;(l=63&this.interim[++g])&&g<4;)v<<=6,v|=l;const u=(224&this.interim[0])==192?2:(240&this.interim[0])==224?3:4,p=u-g;for(;r<p;){if(r>=d)return 0;if(l=a[r++],(192&l)!=128){r--,h=!0;break}this.interim[g++]=l,v<<=6,v|=63&l}h||(u===2?v<128?r--:_[o++]=v:u===3?v<2048||v>=55296&&v<=57343||v===65279||(_[o++]=v):v<65536||v>1114111||(_[o++]=v)),this.interim.fill(0)}const t=d-4;let i=r;for(;i<d;){for(;!(!(i<t)||128&(n=a[i])||128&(c=a[i+1])||128&(f=a[i+2])||128&(m=a[i+3]));)_[o++]=n,_[o++]=c,_[o++]=f,_[o++]=m,i+=4;if(n=a[i++],n<128)_[o++]=n;else if((224&n)==192){if(i>=d)return this.interim[0]=n,o;if(c=a[i++],(192&c)!=128){i--;continue}if(e=(31&n)<<6|63&c,e<128){i--;continue}_[o++]=e}else if((240&n)==224){if(i>=d)return this.interim[0]=n,o;if(c=a[i++],(192&c)!=128){i--;continue}if(i>=d)return this.interim[0]=n,this.interim[1]=c,o;if(f=a[i++],(192&f)!=128){i--;continue}if(e=(15&n)<<12|(63&c)<<6|63&f,e<2048||e>=55296&&e<=57343||e===65279)continue;_[o++]=e}else if((248&n)==240){if(i>=d)return this.interim[0]=n,o;if(c=a[i++],(192&c)!=128){i--;continue}if(i>=d)return this.interim[0]=n,this.interim[1]=c,o;if(f=a[i++],(192&f)!=128){i--;continue}if(i>=d)return this.interim[0]=n,this.interim[1]=c,this.interim[2]=f,o;if(m=a[i++],(192&m)!=128){i--;continue}if(e=(7&n)<<18|(63&c)<<12|(63&f)<<6|63&m,e<65536||e>1114111)continue;_[o++]=e}}return o}}},776:function(B,s,a){var _=this&&this.__decorate||function(e,r,t,i){var h,v=arguments.length,l=v<3?r:i===null?i=Object.getOwnPropertyDescriptor(r,t):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")l=Reflect.decorate(e,r,t,i);else for(var g=e.length-1;g>=0;g--)(h=e[g])&&(l=(v<3?h(l):v>3?h(r,t,l):h(r,t))||l);return v>3&&l&&Object.defineProperty(r,t,l),l},d=this&&this.__param||function(e,r){return function(t,i){r(t,i,e)}};Object.defineProperty(s,"__esModule",{value:!0}),s.traceCall=s.setTraceLogger=s.LogService=void 0;const n=a(859),c=a(97),f={trace:c.LogLevelEnum.TRACE,debug:c.LogLevelEnum.DEBUG,info:c.LogLevelEnum.INFO,warn:c.LogLevelEnum.WARN,error:c.LogLevelEnum.ERROR,off:c.LogLevelEnum.OFF};let m,o=s.LogService=class extends n.Disposable{get logLevel(){return this._logLevel}constructor(e){super(),this._optionsService=e,this._logLevel=c.LogLevelEnum.OFF,this._updateLogLevel(),this.register(this._optionsService.onSpecificOptionChange("logLevel",()=>this._updateLogLevel())),m=this}_updateLogLevel(){this._logLevel=f[this._optionsService.rawOptions.logLevel]}_evalLazyOptionalParams(e){for(let r=0;r<e.length;r++)typeof e[r]=="function"&&(e[r]=e[r]())}_log(e,r,t){this._evalLazyOptionalParams(t),e.call(console,(this._optionsService.options.logger?"":"xterm.js: ")+r,...t)}trace(e,...r){var t;this._logLevel<=c.LogLevelEnum.TRACE&&this._log(((t=this._optionsService.options.logger)==null?void 0:t.trace.bind(this._optionsService.options.logger))??console.log,e,r)}debug(e,...r){var t;this._logLevel<=c.LogLevelEnum.DEBUG&&this._log(((t=this._optionsService.options.logger)==null?void 0:t.debug.bind(this._optionsService.options.logger))??console.log,e,r)}info(e,...r){var t;this._logLevel<=c.LogLevelEnum.INFO&&this._log(((t=this._optionsService.options.logger)==null?void 0:t.info.bind(this._optionsService.options.logger))??console.info,e,r)}warn(e,...r){var t;this._logLevel<=c.LogLevelEnum.WARN&&this._log(((t=this._optionsService.options.logger)==null?void 0:t.warn.bind(this._optionsService.options.logger))??console.warn,e,r)}error(e,...r){var t;this._logLevel<=c.LogLevelEnum.ERROR&&this._log(((t=this._optionsService.options.logger)==null?void 0:t.error.bind(this._optionsService.options.logger))??console.error,e,r)}};s.LogService=o=_([d(0,c.IOptionsService)],o),s.setTraceLogger=function(e){m=e},s.traceCall=function(e,r,t){if(typeof t.value!="function")throw new Error("not supported");const i=t.value;t.value=function(...h){if(m.logLevel!==c.LogLevelEnum.TRACE)return i.apply(this,h);m.trace(`GlyphRenderer#${i.name}(${h.map(l=>JSON.stringify(l)).join(", ")})`);const v=i.apply(this,h);return m.trace(`GlyphRenderer#${i.name} return`,v),v}}},726:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.createDecorator=s.getServiceDependencies=s.serviceRegistry=void 0;const a="di$target",_="di$dependencies";s.serviceRegistry=new Map,s.getServiceDependencies=function(d){return d[_]||[]},s.createDecorator=function(d){if(s.serviceRegistry.has(d))return s.serviceRegistry.get(d);const n=function(c,f,m){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");(function(o,e,r){e[a]===e?e[_].push({id:o,index:r}):(e[_]=[{id:o,index:r}],e[a]=e)})(n,c,m)};return n.toString=()=>d,s.serviceRegistry.set(d,n),n}},97:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.IDecorationService=s.IUnicodeService=s.IOscLinkService=s.IOptionsService=s.ILogService=s.LogLevelEnum=s.IInstantiationService=s.ICharsetService=s.ICoreService=s.ICoreMouseService=s.IBufferService=void 0;const _=a(726);var d;s.IBufferService=(0,_.createDecorator)("BufferService"),s.ICoreMouseService=(0,_.createDecorator)("CoreMouseService"),s.ICoreService=(0,_.createDecorator)("CoreService"),s.ICharsetService=(0,_.createDecorator)("CharsetService"),s.IInstantiationService=(0,_.createDecorator)("InstantiationService"),function(n){n[n.TRACE=0]="TRACE",n[n.DEBUG=1]="DEBUG",n[n.INFO=2]="INFO",n[n.WARN=3]="WARN",n[n.ERROR=4]="ERROR",n[n.OFF=5]="OFF"}(d||(s.LogLevelEnum=d={})),s.ILogService=(0,_.createDecorator)("LogService"),s.IOptionsService=(0,_.createDecorator)("OptionsService"),s.IOscLinkService=(0,_.createDecorator)("OscLinkService"),s.IUnicodeService=(0,_.createDecorator)("UnicodeService"),s.IDecorationService=(0,_.createDecorator)("DecorationService")}},Q={};function Y(B){var s=Q[B];if(s!==void 0)return s.exports;var a=Q[B]={exports:{}};return ae[B].call(a.exports,a,a.exports,Y),a.exports}var oe={};return(()=>{var B=oe;Object.defineProperty(B,"__esModule",{value:!0}),B.CanvasAddon=void 0;const s=Y(345),a=Y(859),_=Y(776),d=Y(949);class n extends a.Disposable{constructor(){super(...arguments),this._onChangeTextureAtlas=this.register(new s.EventEmitter),this.onChangeTextureAtlas=this._onChangeTextureAtlas.event,this._onAddTextureAtlasCanvas=this.register(new s.EventEmitter),this.onAddTextureAtlasCanvas=this._onAddTextureAtlasCanvas.event}get textureAtlas(){var f;return(f=this._renderer)==null?void 0:f.textureAtlas}activate(f){const m=f._core;if(!f.element)return void this.register(m.onWillOpen(()=>this.activate(f)));this._terminal=f;const o=m.coreService,e=m.optionsService,r=m.screenElement,t=m.linkifier,i=m,h=i._bufferService,v=i._renderService,l=i._characterJoinerService,g=i._charSizeService,u=i._coreBrowserService,p=i._decorationService,C=i._logService,b=i._themeService;(0,_.setTraceLogger)(C),this._renderer=new d.CanvasRenderer(f,r,t,h,g,e,l,o,u,p,b),this.register((0,s.forwardEvent)(this._renderer.onChangeTextureAtlas,this._onChangeTextureAtlas)),this.register((0,s.forwardEvent)(this._renderer.onAddTextureAtlasCanvas,this._onAddTextureAtlasCanvas)),v.setRenderer(this._renderer),v.handleResize(h.cols,h.rows),this.register((0,a.toDisposable)(()=>{var y;v.setRenderer(this._terminal._core._createRenderer()),v.handleResize(f.cols,f.rows),(y=this._renderer)==null||y.dispose(),this._renderer=void 0}))}clearTextureAtlas(){var f;(f=this._renderer)==null||f.clearTextureAtlas()}}B.CanvasAddon=n})(),oe})())}(be)),be.exports}var Ue=$e(),we={exports:{}},De;function Ne(){return De||(De=1,function(de,ve){(function(ae,Q){de.exports=Q()})(self,()=>(()=>{var ae={};return(()=>{var Q=ae;Object.defineProperty(Q,"__esModule",{value:!0}),Q.FitAddon=void 0,Q.FitAddon=class{activate(Y){this._terminal=Y}dispose(){}fit(){const Y=this.proposeDimensions();if(!Y||!this._terminal||isNaN(Y.cols)||isNaN(Y.rows))return;const oe=this._terminal._core;this._terminal.rows===Y.rows&&this._terminal.cols===Y.cols||(oe._renderService.clear(),this._terminal.resize(Y.cols,Y.rows))}proposeDimensions(){if(!this._terminal||!this._terminal.element||!this._terminal.element.parentElement)return;const Y=this._terminal._core,oe=Y._renderService.dimensions;if(oe.css.cell.width===0||oe.css.cell.height===0)return;const B=this._terminal.options.scrollback===0?0:Y.viewport.scrollBarWidth,s=window.getComputedStyle(this._terminal.element.parentElement),a=parseInt(s.getPropertyValue("height")),_=Math.max(0,parseInt(s.getPropertyValue("width"))),d=window.getComputedStyle(this._terminal.element),n=a-(parseInt(d.getPropertyValue("padding-top"))+parseInt(d.getPropertyValue("padding-bottom"))),c=_-(parseInt(d.getPropertyValue("padding-right"))+parseInt(d.getPropertyValue("padding-left")))-B;return{cols:Math.max(2,Math.floor(c/oe.css.cell.width)),rows:Math.max(1,Math.floor(n/oe.css.cell.height))}}}})(),ae})())}(we)),we.exports}var ze=Ne(),ye={exports:{}},Ae;function je(){return Ae||(Ae=1,function(de,ve){(function(ae,Q){de.exports=Q()})(self,()=>(()=>{var ae={6:(B,s)=>{function a(d){try{const n=new URL(d),c=n.password&&n.username?`${n.protocol}//${n.username}:${n.password}@${n.host}`:n.username?`${n.protocol}//${n.username}@${n.host}`:`${n.protocol}//${n.host}`;return d.toLocaleLowerCase().startsWith(c.toLocaleLowerCase())}catch{return!1}}Object.defineProperty(s,"__esModule",{value:!0}),s.LinkComputer=s.WebLinkProvider=void 0,s.WebLinkProvider=class{constructor(d,n,c,f={}){this._terminal=d,this._regex=n,this._handler=c,this._options=f}provideLinks(d,n){const c=_.computeLink(d,this._regex,this._terminal,this._handler);n(this._addCallbacks(c))}_addCallbacks(d){return d.map(n=>(n.leave=this._options.leave,n.hover=(c,f)=>{if(this._options.hover){const{range:m}=n;this._options.hover(c,f,m)}},n))}};class _{static computeLink(n,c,f,m){const o=new RegExp(c.source,(c.flags||"")+"g"),[e,r]=_._getWindowedLineStrings(n-1,f),t=e.join("");let i;const h=[];for(;i=o.exec(t);){const v=i[0];if(!a(v))continue;const[l,g]=_._mapStrIdx(f,r,0,i.index),[u,p]=_._mapStrIdx(f,l,g,v.length);if(l===-1||g===-1||u===-1||p===-1)continue;const C={start:{x:g+1,y:l+1},end:{x:p,y:u+1}};h.push({range:C,text:v,activate:m})}return h}static _getWindowedLineStrings(n,c){let f,m=n,o=n,e=0,r="";const t=[];if(f=c.buffer.active.getLine(n)){const i=f.translateToString(!0);if(f.isWrapped&&i[0]!==" "){for(e=0;(f=c.buffer.active.getLine(--m))&&e<2048&&(r=f.translateToString(!0),e+=r.length,t.push(r),f.isWrapped&&r.indexOf(" ")===-1););t.reverse()}for(t.push(i),e=0;(f=c.buffer.active.getLine(++o))&&f.isWrapped&&e<2048&&(r=f.translateToString(!0),e+=r.length,t.push(r),r.indexOf(" ")===-1););}return[t,m]}static _mapStrIdx(n,c,f,m){const o=n.buffer.active,e=o.getNullCell();let r=f;for(;m;){const t=o.getLine(c);if(!t)return[-1,-1];for(let i=r;i<t.length;++i){t.getCell(i,e);const h=e.getChars();if(e.getWidth()&&(m-=h.length||1,i===t.length-1&&h==="")){const v=o.getLine(c+1);v&&v.isWrapped&&(v.getCell(0,e),e.getWidth()===2&&(m+=1))}if(m<0)return[c,i]}c++,r=0}return[c,r]}}s.LinkComputer=_}},Q={};function Y(B){var s=Q[B];if(s!==void 0)return s.exports;var a=Q[B]={exports:{}};return ae[B](a,a.exports,Y),a.exports}var oe={};return(()=>{var B=oe;Object.defineProperty(B,"__esModule",{value:!0}),B.WebLinksAddon=void 0;const s=Y(6),a=/(https?|HTTPS?):[/]{2}[^\s"'!*(){}|\\\^<>`]*[^\s"':,.!?{}|\\\^~\[\]`()<>]/;function _(d,n){const c=window.open();if(c){try{c.opener=null}catch{}c.location.href=n}else console.warn("Opening link blocked as opener could not be cleared")}B.WebLinksAddon=class{constructor(d=_,n={}){this._handler=d,this._options=n}activate(d){this._terminal=d;const n=this._options,c=n.urlRegex||a;this._linkProvider=this._terminal.registerLinkProvider(new s.WebLinkProvider(this._terminal,c,this._handler,n))}dispose(){var d;(d=this._linkProvider)==null||d.dispose()}}})(),oe})())}(ye)),ye.exports}var qe=je(),Le={exports:{}},Be;function Ke(){return Be||(Be=1,function(de,ve){(function(ae,Q){de.exports=Q()})(globalThis,()=>(()=>{var ae={4567:function(B,s,a){var _=this&&this.__decorate||function(t,i,h,v){var l,g=arguments.length,u=g<3?i:v===null?v=Object.getOwnPropertyDescriptor(i,h):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")u=Reflect.decorate(t,i,h,v);else for(var p=t.length-1;p>=0;p--)(l=t[p])&&(u=(g<3?l(u):g>3?l(i,h,u):l(i,h))||u);return g>3&&u&&Object.defineProperty(i,h,u),u},d=this&&this.__param||function(t,i){return function(h,v){i(h,v,t)}};Object.defineProperty(s,"__esModule",{value:!0}),s.AccessibilityManager=void 0;const n=a(9042),c=a(9924),f=a(844),m=a(4725),o=a(2585),e=a(3656);let r=s.AccessibilityManager=class extends f.Disposable{constructor(t,i,h,v){super(),this._terminal=t,this._coreBrowserService=h,this._renderService=v,this._rowColumns=new WeakMap,this._liveRegionLineCount=0,this._charsToConsume=[],this._charsToAnnounce="",this._accessibilityContainer=this._coreBrowserService.mainDocument.createElement("div"),this._accessibilityContainer.classList.add("xterm-accessibility"),this._rowContainer=this._coreBrowserService.mainDocument.createElement("div"),this._rowContainer.setAttribute("role","list"),this._rowContainer.classList.add("xterm-accessibility-tree"),this._rowElements=[];for(let l=0;l<this._terminal.rows;l++)this._rowElements[l]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[l]);if(this._topBoundaryFocusListener=l=>this._handleBoundaryFocus(l,0),this._bottomBoundaryFocusListener=l=>this._handleBoundaryFocus(l,1),this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions(),this._accessibilityContainer.appendChild(this._rowContainer),this._liveRegion=this._coreBrowserService.mainDocument.createElement("div"),this._liveRegion.classList.add("live-region"),this._liveRegion.setAttribute("aria-live","assertive"),this._accessibilityContainer.appendChild(this._liveRegion),this._liveRegionDebouncer=this.register(new c.TimeBasedDebouncer(this._renderRows.bind(this))),!this._terminal.element)throw new Error("Cannot enable accessibility before Terminal.open");this._terminal.element.insertAdjacentElement("afterbegin",this._accessibilityContainer),this.register(this._terminal.onResize(l=>this._handleResize(l.rows))),this.register(this._terminal.onRender(l=>this._refreshRows(l.start,l.end))),this.register(this._terminal.onScroll(()=>this._refreshRows())),this.register(this._terminal.onA11yChar(l=>this._handleChar(l))),this.register(this._terminal.onLineFeed(()=>this._handleChar(`
`))),this.register(this._terminal.onA11yTab(l=>this._handleTab(l))),this.register(this._terminal.onKey(l=>this._handleKey(l.key))),this.register(this._terminal.onBlur(()=>this._clearLiveRegion())),this.register(this._renderService.onDimensionsChange(()=>this._refreshRowsDimensions())),this.register((0,e.addDisposableDomListener)(document,"selectionchange",()=>this._handleSelectionChange())),this.register(this._coreBrowserService.onDprChange(()=>this._refreshRowsDimensions())),this._refreshRows(),this.register((0,f.toDisposable)(()=>{this._accessibilityContainer.remove(),this._rowElements.length=0}))}_handleTab(t){for(let i=0;i<t;i++)this._handleChar(" ")}_handleChar(t){this._liveRegionLineCount<21&&(this._charsToConsume.length>0?this._charsToConsume.shift()!==t&&(this._charsToAnnounce+=t):this._charsToAnnounce+=t,t===`
`&&(this._liveRegionLineCount++,this._liveRegionLineCount===21&&(this._liveRegion.textContent+=n.tooMuchOutput)))}_clearLiveRegion(){this._liveRegion.textContent="",this._liveRegionLineCount=0}_handleKey(t){this._clearLiveRegion(),new RegExp("\\p{Control}","u").test(t)||this._charsToConsume.push(t)}_refreshRows(t,i){this._liveRegionDebouncer.refresh(t,i,this._terminal.rows)}_renderRows(t,i){const h=this._terminal.buffer,v=h.lines.length.toString();for(let l=t;l<=i;l++){const g=h.lines.get(h.ydisp+l),u=[],p=(g==null?void 0:g.translateToString(!0,void 0,void 0,u))||"",C=(h.ydisp+l+1).toString(),b=this._rowElements[l];b&&(p.length===0?(b.innerText=" ",this._rowColumns.set(b,[0,1])):(b.textContent=p,this._rowColumns.set(b,u)),b.setAttribute("aria-posinset",C),b.setAttribute("aria-setsize",v))}this._announceCharacters()}_announceCharacters(){this._charsToAnnounce.length!==0&&(this._liveRegion.textContent+=this._charsToAnnounce,this._charsToAnnounce="")}_handleBoundaryFocus(t,i){const h=t.target,v=this._rowElements[i===0?1:this._rowElements.length-2];if(h.getAttribute("aria-posinset")===(i===0?"1":`${this._terminal.buffer.lines.length}`)||t.relatedTarget!==v)return;let l,g;if(i===0?(l=h,g=this._rowElements.pop(),this._rowContainer.removeChild(g)):(l=this._rowElements.shift(),g=h,this._rowContainer.removeChild(l)),l.removeEventListener("focus",this._topBoundaryFocusListener),g.removeEventListener("focus",this._bottomBoundaryFocusListener),i===0){const u=this._createAccessibilityTreeNode();this._rowElements.unshift(u),this._rowContainer.insertAdjacentElement("afterbegin",u)}else{const u=this._createAccessibilityTreeNode();this._rowElements.push(u),this._rowContainer.appendChild(u)}this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._terminal.scrollLines(i===0?-1:1),this._rowElements[i===0?1:this._rowElements.length-2].focus(),t.preventDefault(),t.stopImmediatePropagation()}_handleSelectionChange(){var p;if(this._rowElements.length===0)return;const t=document.getSelection();if(!t)return;if(t.isCollapsed)return void(this._rowContainer.contains(t.anchorNode)&&this._terminal.clearSelection());if(!t.anchorNode||!t.focusNode)return void console.error("anchorNode and/or focusNode are null");let i={node:t.anchorNode,offset:t.anchorOffset},h={node:t.focusNode,offset:t.focusOffset};if((i.node.compareDocumentPosition(h.node)&Node.DOCUMENT_POSITION_PRECEDING||i.node===h.node&&i.offset>h.offset)&&([i,h]=[h,i]),i.node.compareDocumentPosition(this._rowElements[0])&(Node.DOCUMENT_POSITION_CONTAINED_BY|Node.DOCUMENT_POSITION_FOLLOWING)&&(i={node:this._rowElements[0].childNodes[0],offset:0}),!this._rowContainer.contains(i.node))return;const v=this._rowElements.slice(-1)[0];if(h.node.compareDocumentPosition(v)&(Node.DOCUMENT_POSITION_CONTAINED_BY|Node.DOCUMENT_POSITION_PRECEDING)&&(h={node:v,offset:((p=v.textContent)==null?void 0:p.length)??0}),!this._rowContainer.contains(h.node))return;const l=({node:C,offset:b})=>{const y=C instanceof Text?C.parentNode:C;let w=parseInt(y==null?void 0:y.getAttribute("aria-posinset"),10)-1;if(isNaN(w))return console.warn("row is invalid. Race condition?"),null;const k=this._rowColumns.get(y);if(!k)return console.warn("columns is null. Race condition?"),null;let D=b<k.length?k[b]:k.slice(-1)[0]+1;return D>=this._terminal.cols&&(++w,D=0),{row:w,column:D}},g=l(i),u=l(h);if(g&&u){if(g.row>u.row||g.row===u.row&&g.column>=u.column)throw new Error("invalid range");this._terminal.select(g.column,g.row,(u.row-g.row)*this._terminal.cols-g.column+u.column)}}_handleResize(t){this._rowElements[this._rowElements.length-1].removeEventListener("focus",this._bottomBoundaryFocusListener);for(let i=this._rowContainer.children.length;i<this._terminal.rows;i++)this._rowElements[i]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[i]);for(;this._rowElements.length>t;)this._rowContainer.removeChild(this._rowElements.pop());this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions()}_createAccessibilityTreeNode(){const t=this._coreBrowserService.mainDocument.createElement("div");return t.setAttribute("role","listitem"),t.tabIndex=-1,this._refreshRowDimensions(t),t}_refreshRowsDimensions(){if(this._renderService.dimensions.css.cell.height){this._accessibilityContainer.style.width=`${this._renderService.dimensions.css.canvas.width}px`,this._rowElements.length!==this._terminal.rows&&this._handleResize(this._terminal.rows);for(let t=0;t<this._terminal.rows;t++)this._refreshRowDimensions(this._rowElements[t])}}_refreshRowDimensions(t){t.style.height=`${this._renderService.dimensions.css.cell.height}px`}};s.AccessibilityManager=r=_([d(1,o.IInstantiationService),d(2,m.ICoreBrowserService),d(3,m.IRenderService)],r)},3614:(B,s)=>{function a(c){return c.replace(/\r?\n/g,"\r")}function _(c,f){return f?"\x1B[200~"+c+"\x1B[201~":c}function d(c,f,m,o){c=_(c=a(c),m.decPrivateModes.bracketedPasteMode&&o.rawOptions.ignoreBracketedPasteMode!==!0),m.triggerDataEvent(c,!0),f.value=""}function n(c,f,m){const o=m.getBoundingClientRect(),e=c.clientX-o.left-10,r=c.clientY-o.top-10;f.style.width="20px",f.style.height="20px",f.style.left=`${e}px`,f.style.top=`${r}px`,f.style.zIndex="1000",f.focus()}Object.defineProperty(s,"__esModule",{value:!0}),s.rightClickHandler=s.moveTextAreaUnderMouseCursor=s.paste=s.handlePasteEvent=s.copyHandler=s.bracketTextForPaste=s.prepareTextForTerminal=void 0,s.prepareTextForTerminal=a,s.bracketTextForPaste=_,s.copyHandler=function(c,f){c.clipboardData&&c.clipboardData.setData("text/plain",f.selectionText),c.preventDefault()},s.handlePasteEvent=function(c,f,m,o){c.stopPropagation(),c.clipboardData&&d(c.clipboardData.getData("text/plain"),f,m,o)},s.paste=d,s.moveTextAreaUnderMouseCursor=n,s.rightClickHandler=function(c,f,m,o,e){n(c,f,m),e&&o.rightClickSelect(c),f.value=o.selectionText,f.select()}},7239:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ColorContrastCache=void 0;const _=a(1505);s.ColorContrastCache=class{constructor(){this._color=new _.TwoKeyMap,this._css=new _.TwoKeyMap}setCss(d,n,c){this._css.set(d,n,c)}getCss(d,n){return this._css.get(d,n)}setColor(d,n,c){this._color.set(d,n,c)}getColor(d,n){return this._color.get(d,n)}clear(){this._color.clear(),this._css.clear()}}},3656:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.addDisposableDomListener=void 0,s.addDisposableDomListener=function(a,_,d,n){a.addEventListener(_,d,n);let c=!1;return{dispose:()=>{c||(c=!0,a.removeEventListener(_,d,n))}}}},3551:function(B,s,a){var _=this&&this.__decorate||function(r,t,i,h){var v,l=arguments.length,g=l<3?t:h===null?h=Object.getOwnPropertyDescriptor(t,i):h;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")g=Reflect.decorate(r,t,i,h);else for(var u=r.length-1;u>=0;u--)(v=r[u])&&(g=(l<3?v(g):l>3?v(t,i,g):v(t,i))||g);return l>3&&g&&Object.defineProperty(t,i,g),g},d=this&&this.__param||function(r,t){return function(i,h){t(i,h,r)}};Object.defineProperty(s,"__esModule",{value:!0}),s.Linkifier=void 0;const n=a(3656),c=a(8460),f=a(844),m=a(2585),o=a(4725);let e=s.Linkifier=class extends f.Disposable{get currentLink(){return this._currentLink}constructor(r,t,i,h,v){super(),this._element=r,this._mouseService=t,this._renderService=i,this._bufferService=h,this._linkProviderService=v,this._linkCacheDisposables=[],this._isMouseOut=!0,this._wasResized=!1,this._activeLine=-1,this._onShowLinkUnderline=this.register(new c.EventEmitter),this.onShowLinkUnderline=this._onShowLinkUnderline.event,this._onHideLinkUnderline=this.register(new c.EventEmitter),this.onHideLinkUnderline=this._onHideLinkUnderline.event,this.register((0,f.getDisposeArrayDisposable)(this._linkCacheDisposables)),this.register((0,f.toDisposable)(()=>{var l;this._lastMouseEvent=void 0,(l=this._activeProviderReplies)==null||l.clear()})),this.register(this._bufferService.onResize(()=>{this._clearCurrentLink(),this._wasResized=!0})),this.register((0,n.addDisposableDomListener)(this._element,"mouseleave",()=>{this._isMouseOut=!0,this._clearCurrentLink()})),this.register((0,n.addDisposableDomListener)(this._element,"mousemove",this._handleMouseMove.bind(this))),this.register((0,n.addDisposableDomListener)(this._element,"mousedown",this._handleMouseDown.bind(this))),this.register((0,n.addDisposableDomListener)(this._element,"mouseup",this._handleMouseUp.bind(this)))}_handleMouseMove(r){this._lastMouseEvent=r;const t=this._positionFromMouseEvent(r,this._element,this._mouseService);if(!t)return;this._isMouseOut=!1;const i=r.composedPath();for(let h=0;h<i.length;h++){const v=i[h];if(v.classList.contains("xterm"))break;if(v.classList.contains("xterm-hover"))return}this._lastBufferCell&&t.x===this._lastBufferCell.x&&t.y===this._lastBufferCell.y||(this._handleHover(t),this._lastBufferCell=t)}_handleHover(r){if(this._activeLine!==r.y||this._wasResized)return this._clearCurrentLink(),this._askForLink(r,!1),void(this._wasResized=!1);this._currentLink&&this._linkAtPosition(this._currentLink.link,r)||(this._clearCurrentLink(),this._askForLink(r,!0))}_askForLink(r,t){var h,v;this._activeProviderReplies&&t||((h=this._activeProviderReplies)==null||h.forEach(l=>{l==null||l.forEach(g=>{g.link.dispose&&g.link.dispose()})}),this._activeProviderReplies=new Map,this._activeLine=r.y);let i=!1;for(const[l,g]of this._linkProviderService.linkProviders.entries())t?(v=this._activeProviderReplies)!=null&&v.get(l)&&(i=this._checkLinkProviderResult(l,r,i)):g.provideLinks(r.y,u=>{var C,b;if(this._isMouseOut)return;const p=u==null?void 0:u.map(y=>({link:y}));(C=this._activeProviderReplies)==null||C.set(l,p),i=this._checkLinkProviderResult(l,r,i),((b=this._activeProviderReplies)==null?void 0:b.size)===this._linkProviderService.linkProviders.length&&this._removeIntersectingLinks(r.y,this._activeProviderReplies)})}_removeIntersectingLinks(r,t){const i=new Set;for(let h=0;h<t.size;h++){const v=t.get(h);if(v)for(let l=0;l<v.length;l++){const g=v[l],u=g.link.range.start.y<r?0:g.link.range.start.x,p=g.link.range.end.y>r?this._bufferService.cols:g.link.range.end.x;for(let C=u;C<=p;C++){if(i.has(C)){v.splice(l--,1);break}i.add(C)}}}}_checkLinkProviderResult(r,t,i){var l;if(!this._activeProviderReplies)return i;const h=this._activeProviderReplies.get(r);let v=!1;for(let g=0;g<r;g++)this._activeProviderReplies.has(g)&&!this._activeProviderReplies.get(g)||(v=!0);if(!v&&h){const g=h.find(u=>this._linkAtPosition(u.link,t));g&&(i=!0,this._handleNewLink(g))}if(this._activeProviderReplies.size===this._linkProviderService.linkProviders.length&&!i)for(let g=0;g<this._activeProviderReplies.size;g++){const u=(l=this._activeProviderReplies.get(g))==null?void 0:l.find(p=>this._linkAtPosition(p.link,t));if(u){i=!0,this._handleNewLink(u);break}}return i}_handleMouseDown(){this._mouseDownLink=this._currentLink}_handleMouseUp(r){if(!this._currentLink)return;const t=this._positionFromMouseEvent(r,this._element,this._mouseService);t&&this._mouseDownLink===this._currentLink&&this._linkAtPosition(this._currentLink.link,t)&&this._currentLink.link.activate(r,this._currentLink.link.text)}_clearCurrentLink(r,t){this._currentLink&&this._lastMouseEvent&&(!r||!t||this._currentLink.link.range.start.y>=r&&this._currentLink.link.range.end.y<=t)&&(this._linkLeave(this._element,this._currentLink.link,this._lastMouseEvent),this._currentLink=void 0,(0,f.disposeArray)(this._linkCacheDisposables))}_handleNewLink(r){if(!this._lastMouseEvent)return;const t=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);t&&this._linkAtPosition(r.link,t)&&(this._currentLink=r,this._currentLink.state={decorations:{underline:r.link.decorations===void 0||r.link.decorations.underline,pointerCursor:r.link.decorations===void 0||r.link.decorations.pointerCursor},isHovered:!0},this._linkHover(this._element,r.link,this._lastMouseEvent),r.link.decorations={},Object.defineProperties(r.link.decorations,{pointerCursor:{get:()=>{var i,h;return(h=(i=this._currentLink)==null?void 0:i.state)==null?void 0:h.decorations.pointerCursor},set:i=>{var h;(h=this._currentLink)!=null&&h.state&&this._currentLink.state.decorations.pointerCursor!==i&&(this._currentLink.state.decorations.pointerCursor=i,this._currentLink.state.isHovered&&this._element.classList.toggle("xterm-cursor-pointer",i))}},underline:{get:()=>{var i,h;return(h=(i=this._currentLink)==null?void 0:i.state)==null?void 0:h.decorations.underline},set:i=>{var h,v,l;(h=this._currentLink)!=null&&h.state&&((l=(v=this._currentLink)==null?void 0:v.state)==null?void 0:l.decorations.underline)!==i&&(this._currentLink.state.decorations.underline=i,this._currentLink.state.isHovered&&this._fireUnderlineEvent(r.link,i))}}}),this._linkCacheDisposables.push(this._renderService.onRenderedViewportChange(i=>{if(!this._currentLink)return;const h=i.start===0?0:i.start+1+this._bufferService.buffer.ydisp,v=this._bufferService.buffer.ydisp+1+i.end;if(this._currentLink.link.range.start.y>=h&&this._currentLink.link.range.end.y<=v&&(this._clearCurrentLink(h,v),this._lastMouseEvent)){const l=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);l&&this._askForLink(l,!1)}})))}_linkHover(r,t,i){var h;(h=this._currentLink)!=null&&h.state&&(this._currentLink.state.isHovered=!0,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(t,!0),this._currentLink.state.decorations.pointerCursor&&r.classList.add("xterm-cursor-pointer")),t.hover&&t.hover(i,t.text)}_fireUnderlineEvent(r,t){const i=r.range,h=this._bufferService.buffer.ydisp,v=this._createLinkUnderlineEvent(i.start.x-1,i.start.y-h-1,i.end.x,i.end.y-h-1,void 0);(t?this._onShowLinkUnderline:this._onHideLinkUnderline).fire(v)}_linkLeave(r,t,i){var h;(h=this._currentLink)!=null&&h.state&&(this._currentLink.state.isHovered=!1,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(t,!1),this._currentLink.state.decorations.pointerCursor&&r.classList.remove("xterm-cursor-pointer")),t.leave&&t.leave(i,t.text)}_linkAtPosition(r,t){const i=r.range.start.y*this._bufferService.cols+r.range.start.x,h=r.range.end.y*this._bufferService.cols+r.range.end.x,v=t.y*this._bufferService.cols+t.x;return i<=v&&v<=h}_positionFromMouseEvent(r,t,i){const h=i.getCoords(r,t,this._bufferService.cols,this._bufferService.rows);if(h)return{x:h[0],y:h[1]+this._bufferService.buffer.ydisp}}_createLinkUnderlineEvent(r,t,i,h,v){return{x1:r,y1:t,x2:i,y2:h,cols:this._bufferService.cols,fg:v}}};s.Linkifier=e=_([d(1,o.IMouseService),d(2,o.IRenderService),d(3,m.IBufferService),d(4,o.ILinkProviderService)],e)},9042:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.tooMuchOutput=s.promptLabel=void 0,s.promptLabel="Terminal input",s.tooMuchOutput="Too much output to announce, navigate to rows manually to read"},3730:function(B,s,a){var _=this&&this.__decorate||function(o,e,r,t){var i,h=arguments.length,v=h<3?e:t===null?t=Object.getOwnPropertyDescriptor(e,r):t;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(o,e,r,t);else for(var l=o.length-1;l>=0;l--)(i=o[l])&&(v=(h<3?i(v):h>3?i(e,r,v):i(e,r))||v);return h>3&&v&&Object.defineProperty(e,r,v),v},d=this&&this.__param||function(o,e){return function(r,t){e(r,t,o)}};Object.defineProperty(s,"__esModule",{value:!0}),s.OscLinkProvider=void 0;const n=a(511),c=a(2585);let f=s.OscLinkProvider=class{constructor(o,e,r){this._bufferService=o,this._optionsService=e,this._oscLinkService=r}provideLinks(o,e){var p;const r=this._bufferService.buffer.lines.get(o-1);if(!r)return void e(void 0);const t=[],i=this._optionsService.rawOptions.linkHandler,h=new n.CellData,v=r.getTrimmedLength();let l=-1,g=-1,u=!1;for(let C=0;C<v;C++)if(g!==-1||r.hasContent(C)){if(r.loadCell(C,h),h.hasExtendedAttrs()&&h.extended.urlId){if(g===-1){g=C,l=h.extended.urlId;continue}u=h.extended.urlId!==l}else g!==-1&&(u=!0);if(u||g!==-1&&C===v-1){const b=(p=this._oscLinkService.getLinkData(l))==null?void 0:p.uri;if(b){const y={start:{x:g+1,y:o},end:{x:C+(u||C!==v-1?0:1),y:o}};let w=!1;if(!(i!=null&&i.allowNonHttpProtocols))try{const k=new URL(b);["http:","https:"].includes(k.protocol)||(w=!0)}catch{w=!0}w||t.push({text:b,range:y,activate:(k,D)=>i?i.activate(k,D,y):m(0,D),hover:(k,D)=>{var P;return(P=i==null?void 0:i.hover)==null?void 0:P.call(i,k,D,y)},leave:(k,D)=>{var P;return(P=i==null?void 0:i.leave)==null?void 0:P.call(i,k,D,y)}})}u=!1,h.hasExtendedAttrs()&&h.extended.urlId?(g=C,l=h.extended.urlId):(g=-1,l=-1)}}e(t)}};function m(o,e){if(confirm(`Do you want to navigate to ${e}?

WARNING: This link could potentially be dangerous`)){const r=window.open();if(r){try{r.opener=null}catch{}r.location.href=e}else console.warn("Opening link blocked as opener could not be cleared")}}s.OscLinkProvider=f=_([d(0,c.IBufferService),d(1,c.IOptionsService),d(2,c.IOscLinkService)],f)},6193:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.RenderDebouncer=void 0,s.RenderDebouncer=class{constructor(a,_){this._renderCallback=a,this._coreBrowserService=_,this._refreshCallbacks=[]}dispose(){this._animationFrame&&(this._coreBrowserService.window.cancelAnimationFrame(this._animationFrame),this._animationFrame=void 0)}addRefreshCallback(a){return this._refreshCallbacks.push(a),this._animationFrame||(this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh())),this._animationFrame}refresh(a,_,d){this._rowCount=d,a=a!==void 0?a:0,_=_!==void 0?_:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,a):a,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,_):_,this._animationFrame||(this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._animationFrame=void 0,this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return void this._runRefreshCallbacks();const a=Math.max(this._rowStart,0),_=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(a,_),this._runRefreshCallbacks()}_runRefreshCallbacks(){for(const a of this._refreshCallbacks)a(0);this._refreshCallbacks=[]}}},3236:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Terminal=void 0;const _=a(3614),d=a(3656),n=a(3551),c=a(9042),f=a(3730),m=a(1680),o=a(3107),e=a(5744),r=a(2950),t=a(1296),i=a(428),h=a(4269),v=a(5114),l=a(8934),g=a(3230),u=a(9312),p=a(4725),C=a(6731),b=a(8055),y=a(8969),w=a(8460),k=a(844),D=a(6114),P=a(8437),T=a(2584),A=a(7399),S=a(5941),x=a(9074),E=a(2585),R=a(5435),I=a(4567),F=a(779);class N extends y.CoreTerminal{get onFocus(){return this._onFocus.event}get onBlur(){return this._onBlur.event}get onA11yChar(){return this._onA11yCharEmitter.event}get onA11yTab(){return this._onA11yTabEmitter.event}get onWillOpen(){return this._onWillOpen.event}constructor(M={}){super(M),this.browser=D,this._keyDownHandled=!1,this._keyDownSeen=!1,this._keyPressHandled=!1,this._unprocessedDeadKey=!1,this._accessibilityManager=this.register(new k.MutableDisposable),this._onCursorMove=this.register(new w.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onKey=this.register(new w.EventEmitter),this.onKey=this._onKey.event,this._onRender=this.register(new w.EventEmitter),this.onRender=this._onRender.event,this._onSelectionChange=this.register(new w.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onTitleChange=this.register(new w.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onBell=this.register(new w.EventEmitter),this.onBell=this._onBell.event,this._onFocus=this.register(new w.EventEmitter),this._onBlur=this.register(new w.EventEmitter),this._onA11yCharEmitter=this.register(new w.EventEmitter),this._onA11yTabEmitter=this.register(new w.EventEmitter),this._onWillOpen=this.register(new w.EventEmitter),this._setup(),this._decorationService=this._instantiationService.createInstance(x.DecorationService),this._instantiationService.setService(E.IDecorationService,this._decorationService),this._linkProviderService=this._instantiationService.createInstance(F.LinkProviderService),this._instantiationService.setService(p.ILinkProviderService,this._linkProviderService),this._linkProviderService.registerLinkProvider(this._instantiationService.createInstance(f.OscLinkProvider)),this.register(this._inputHandler.onRequestBell(()=>this._onBell.fire())),this.register(this._inputHandler.onRequestRefreshRows((L,H)=>this.refresh(L,H))),this.register(this._inputHandler.onRequestSendFocus(()=>this._reportFocus())),this.register(this._inputHandler.onRequestReset(()=>this.reset())),this.register(this._inputHandler.onRequestWindowsOptionsReport(L=>this._reportWindowsOptions(L))),this.register(this._inputHandler.onColor(L=>this._handleColorEvent(L))),this.register((0,w.forwardEvent)(this._inputHandler.onCursorMove,this._onCursorMove)),this.register((0,w.forwardEvent)(this._inputHandler.onTitleChange,this._onTitleChange)),this.register((0,w.forwardEvent)(this._inputHandler.onA11yChar,this._onA11yCharEmitter)),this.register((0,w.forwardEvent)(this._inputHandler.onA11yTab,this._onA11yTabEmitter)),this.register(this._bufferService.onResize(L=>this._afterResize(L.cols,L.rows))),this.register((0,k.toDisposable)(()=>{var L,H;this._customKeyEventHandler=void 0,(H=(L=this.element)==null?void 0:L.parentNode)==null||H.removeChild(this.element)}))}_handleColorEvent(M){if(this._themeService)for(const L of M){let H,O="";switch(L.index){case 256:H="foreground",O="10";break;case 257:H="background",O="11";break;case 258:H="cursor",O="12";break;default:H="ansi",O="4;"+L.index}switch(L.type){case 0:const $=b.color.toColorRGB(H==="ansi"?this._themeService.colors.ansi[L.index]:this._themeService.colors[H]);this.coreService.triggerDataEvent(`${T.C0.ESC}]${O};${(0,S.toRgbString)($)}${T.C1_ESCAPED.ST}`);break;case 1:if(H==="ansi")this._themeService.modifyColors(W=>W.ansi[L.index]=b.channels.toColor(...L.color));else{const W=H;this._themeService.modifyColors(K=>K[W]=b.channels.toColor(...L.color))}break;case 2:this._themeService.restoreColor(L.index)}}}_setup(){super._setup(),this._customKeyEventHandler=void 0}get buffer(){return this.buffers.active}focus(){this.textarea&&this.textarea.focus({preventScroll:!0})}_handleScreenReaderModeOptionChange(M){M?!this._accessibilityManager.value&&this._renderService&&(this._accessibilityManager.value=this._instantiationService.createInstance(I.AccessibilityManager,this)):this._accessibilityManager.clear()}_handleTextAreaFocus(M){this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(T.C0.ESC+"[I"),this.element.classList.add("focus"),this._showCursor(),this._onFocus.fire()}blur(){var M;return(M=this.textarea)==null?void 0:M.blur()}_handleTextAreaBlur(){this.textarea.value="",this.refresh(this.buffer.y,this.buffer.y),this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(T.C0.ESC+"[O"),this.element.classList.remove("focus"),this._onBlur.fire()}_syncTextArea(){if(!this.textarea||!this.buffer.isCursorInViewport||this._compositionHelper.isComposing||!this._renderService)return;const M=this.buffer.ybase+this.buffer.y,L=this.buffer.lines.get(M);if(!L)return;const H=Math.min(this.buffer.x,this.cols-1),O=this._renderService.dimensions.css.cell.height,$=L.getWidth(H),W=this._renderService.dimensions.css.cell.width*$,K=this.buffer.y*this._renderService.dimensions.css.cell.height,q=H*this._renderService.dimensions.css.cell.width;this.textarea.style.left=q+"px",this.textarea.style.top=K+"px",this.textarea.style.width=W+"px",this.textarea.style.height=O+"px",this.textarea.style.lineHeight=O+"px",this.textarea.style.zIndex="-5"}_initGlobal(){this._bindKeys(),this.register((0,d.addDisposableDomListener)(this.element,"copy",L=>{this.hasSelection()&&(0,_.copyHandler)(L,this._selectionService)}));const M=L=>(0,_.handlePasteEvent)(L,this.textarea,this.coreService,this.optionsService);this.register((0,d.addDisposableDomListener)(this.textarea,"paste",M)),this.register((0,d.addDisposableDomListener)(this.element,"paste",M)),D.isFirefox?this.register((0,d.addDisposableDomListener)(this.element,"mousedown",L=>{L.button===2&&(0,_.rightClickHandler)(L,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})):this.register((0,d.addDisposableDomListener)(this.element,"contextmenu",L=>{(0,_.rightClickHandler)(L,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})),D.isLinux&&this.register((0,d.addDisposableDomListener)(this.element,"auxclick",L=>{L.button===1&&(0,_.moveTextAreaUnderMouseCursor)(L,this.textarea,this.screenElement)}))}_bindKeys(){this.register((0,d.addDisposableDomListener)(this.textarea,"keyup",M=>this._keyUp(M),!0)),this.register((0,d.addDisposableDomListener)(this.textarea,"keydown",M=>this._keyDown(M),!0)),this.register((0,d.addDisposableDomListener)(this.textarea,"keypress",M=>this._keyPress(M),!0)),this.register((0,d.addDisposableDomListener)(this.textarea,"compositionstart",()=>this._compositionHelper.compositionstart())),this.register((0,d.addDisposableDomListener)(this.textarea,"compositionupdate",M=>this._compositionHelper.compositionupdate(M))),this.register((0,d.addDisposableDomListener)(this.textarea,"compositionend",()=>this._compositionHelper.compositionend())),this.register((0,d.addDisposableDomListener)(this.textarea,"input",M=>this._inputEvent(M),!0)),this.register(this.onRender(()=>this._compositionHelper.updateCompositionElements()))}open(M){var H;if(!M)throw new Error("Terminal requires a parent element.");if(M.isConnected||this._logService.debug("Terminal.open was called on an element that was not attached to the DOM"),((H=this.element)==null?void 0:H.ownerDocument.defaultView)&&this._coreBrowserService)return void(this.element.ownerDocument.defaultView!==this._coreBrowserService.window&&(this._coreBrowserService.window=this.element.ownerDocument.defaultView));this._document=M.ownerDocument,this.options.documentOverride&&this.options.documentOverride instanceof Document&&(this._document=this.optionsService.rawOptions.documentOverride),this.element=this._document.createElement("div"),this.element.dir="ltr",this.element.classList.add("terminal"),this.element.classList.add("xterm"),M.appendChild(this.element);const L=this._document.createDocumentFragment();this._viewportElement=this._document.createElement("div"),this._viewportElement.classList.add("xterm-viewport"),L.appendChild(this._viewportElement),this._viewportScrollArea=this._document.createElement("div"),this._viewportScrollArea.classList.add("xterm-scroll-area"),this._viewportElement.appendChild(this._viewportScrollArea),this.screenElement=this._document.createElement("div"),this.screenElement.classList.add("xterm-screen"),this.register((0,d.addDisposableDomListener)(this.screenElement,"mousemove",O=>this.updateCursorStyle(O))),this._helperContainer=this._document.createElement("div"),this._helperContainer.classList.add("xterm-helpers"),this.screenElement.appendChild(this._helperContainer),L.appendChild(this.screenElement),this.textarea=this._document.createElement("textarea"),this.textarea.classList.add("xterm-helper-textarea"),this.textarea.setAttribute("aria-label",c.promptLabel),D.isChromeOS||this.textarea.setAttribute("aria-multiline","false"),this.textarea.setAttribute("autocorrect","off"),this.textarea.setAttribute("autocapitalize","off"),this.textarea.setAttribute("spellcheck","false"),this.textarea.tabIndex=0,this._coreBrowserService=this.register(this._instantiationService.createInstance(v.CoreBrowserService,this.textarea,M.ownerDocument.defaultView??window,this._document??typeof window<"u"?window.document:null)),this._instantiationService.setService(p.ICoreBrowserService,this._coreBrowserService),this.register((0,d.addDisposableDomListener)(this.textarea,"focus",O=>this._handleTextAreaFocus(O))),this.register((0,d.addDisposableDomListener)(this.textarea,"blur",()=>this._handleTextAreaBlur())),this._helperContainer.appendChild(this.textarea),this._charSizeService=this._instantiationService.createInstance(i.CharSizeService,this._document,this._helperContainer),this._instantiationService.setService(p.ICharSizeService,this._charSizeService),this._themeService=this._instantiationService.createInstance(C.ThemeService),this._instantiationService.setService(p.IThemeService,this._themeService),this._characterJoinerService=this._instantiationService.createInstance(h.CharacterJoinerService),this._instantiationService.setService(p.ICharacterJoinerService,this._characterJoinerService),this._renderService=this.register(this._instantiationService.createInstance(g.RenderService,this.rows,this.screenElement)),this._instantiationService.setService(p.IRenderService,this._renderService),this.register(this._renderService.onRenderedViewportChange(O=>this._onRender.fire(O))),this.onResize(O=>this._renderService.resize(O.cols,O.rows)),this._compositionView=this._document.createElement("div"),this._compositionView.classList.add("composition-view"),this._compositionHelper=this._instantiationService.createInstance(r.CompositionHelper,this.textarea,this._compositionView),this._helperContainer.appendChild(this._compositionView),this._mouseService=this._instantiationService.createInstance(l.MouseService),this._instantiationService.setService(p.IMouseService,this._mouseService),this.linkifier=this.register(this._instantiationService.createInstance(n.Linkifier,this.screenElement)),this.element.appendChild(L);try{this._onWillOpen.fire(this.element)}catch{}this._renderService.hasRenderer()||this._renderService.setRenderer(this._createRenderer()),this.viewport=this._instantiationService.createInstance(m.Viewport,this._viewportElement,this._viewportScrollArea),this.viewport.onRequestScrollLines(O=>this.scrollLines(O.amount,O.suppressScrollEvent,1)),this.register(this._inputHandler.onRequestSyncScrollBar(()=>this.viewport.syncScrollArea())),this.register(this.viewport),this.register(this.onCursorMove(()=>{this._renderService.handleCursorMove(),this._syncTextArea()})),this.register(this.onResize(()=>this._renderService.handleResize(this.cols,this.rows))),this.register(this.onBlur(()=>this._renderService.handleBlur())),this.register(this.onFocus(()=>this._renderService.handleFocus())),this.register(this._renderService.onDimensionsChange(()=>this.viewport.syncScrollArea())),this._selectionService=this.register(this._instantiationService.createInstance(u.SelectionService,this.element,this.screenElement,this.linkifier)),this._instantiationService.setService(p.ISelectionService,this._selectionService),this.register(this._selectionService.onRequestScrollLines(O=>this.scrollLines(O.amount,O.suppressScrollEvent))),this.register(this._selectionService.onSelectionChange(()=>this._onSelectionChange.fire())),this.register(this._selectionService.onRequestRedraw(O=>this._renderService.handleSelectionChanged(O.start,O.end,O.columnSelectMode))),this.register(this._selectionService.onLinuxMouseSelection(O=>{this.textarea.value=O,this.textarea.focus(),this.textarea.select()})),this.register(this._onScroll.event(O=>{this.viewport.syncScrollArea(),this._selectionService.refresh()})),this.register((0,d.addDisposableDomListener)(this._viewportElement,"scroll",()=>this._selectionService.refresh())),this.register(this._instantiationService.createInstance(o.BufferDecorationRenderer,this.screenElement)),this.register((0,d.addDisposableDomListener)(this.element,"mousedown",O=>this._selectionService.handleMouseDown(O))),this.coreMouseService.areMouseEventsActive?(this._selectionService.disable(),this.element.classList.add("enable-mouse-events")):this._selectionService.enable(),this.options.screenReaderMode&&(this._accessibilityManager.value=this._instantiationService.createInstance(I.AccessibilityManager,this)),this.register(this.optionsService.onSpecificOptionChange("screenReaderMode",O=>this._handleScreenReaderModeOptionChange(O))),this.options.overviewRulerWidth&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(e.OverviewRulerRenderer,this._viewportElement,this.screenElement))),this.optionsService.onSpecificOptionChange("overviewRulerWidth",O=>{!this._overviewRulerRenderer&&O&&this._viewportElement&&this.screenElement&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(e.OverviewRulerRenderer,this._viewportElement,this.screenElement)))}),this._charSizeService.measure(),this.refresh(0,this.rows-1),this._initGlobal(),this.bindMouse()}_createRenderer(){return this._instantiationService.createInstance(t.DomRenderer,this,this._document,this.element,this.screenElement,this._viewportElement,this._helperContainer,this.linkifier)}bindMouse(){const M=this,L=this.element;function H(W){const K=M._mouseService.getMouseReportCoords(W,M.screenElement);if(!K)return!1;let q,ee;switch(W.overrideType||W.type){case"mousemove":ee=32,W.buttons===void 0?(q=3,W.button!==void 0&&(q=W.button<3?W.button:3)):q=1&W.buttons?0:4&W.buttons?1:2&W.buttons?2:3;break;case"mouseup":ee=0,q=W.button<3?W.button:3;break;case"mousedown":ee=1,q=W.button<3?W.button:3;break;case"wheel":if(M._customWheelEventHandler&&M._customWheelEventHandler(W)===!1||M.viewport.getLinesScrolled(W)===0)return!1;ee=W.deltaY<0?0:1,q=4;break;default:return!1}return!(ee===void 0||q===void 0||q>4)&&M.coreMouseService.triggerMouseEvent({col:K.col,row:K.row,x:K.x,y:K.y,button:q,action:ee,ctrl:W.ctrlKey,alt:W.altKey,shift:W.shiftKey})}const O={mouseup:null,wheel:null,mousedrag:null,mousemove:null},$={mouseup:W=>(H(W),W.buttons||(this._document.removeEventListener("mouseup",O.mouseup),O.mousedrag&&this._document.removeEventListener("mousemove",O.mousedrag)),this.cancel(W)),wheel:W=>(H(W),this.cancel(W,!0)),mousedrag:W=>{W.buttons&&H(W)},mousemove:W=>{W.buttons||H(W)}};this.register(this.coreMouseService.onProtocolChange(W=>{W?(this.optionsService.rawOptions.logLevel==="debug"&&this._logService.debug("Binding to mouse events:",this.coreMouseService.explainEvents(W)),this.element.classList.add("enable-mouse-events"),this._selectionService.disable()):(this._logService.debug("Unbinding from mouse events."),this.element.classList.remove("enable-mouse-events"),this._selectionService.enable()),8&W?O.mousemove||(L.addEventListener("mousemove",$.mousemove),O.mousemove=$.mousemove):(L.removeEventListener("mousemove",O.mousemove),O.mousemove=null),16&W?O.wheel||(L.addEventListener("wheel",$.wheel,{passive:!1}),O.wheel=$.wheel):(L.removeEventListener("wheel",O.wheel),O.wheel=null),2&W?O.mouseup||(O.mouseup=$.mouseup):(this._document.removeEventListener("mouseup",O.mouseup),O.mouseup=null),4&W?O.mousedrag||(O.mousedrag=$.mousedrag):(this._document.removeEventListener("mousemove",O.mousedrag),O.mousedrag=null)})),this.coreMouseService.activeProtocol=this.coreMouseService.activeProtocol,this.register((0,d.addDisposableDomListener)(L,"mousedown",W=>{if(W.preventDefault(),this.focus(),this.coreMouseService.areMouseEventsActive&&!this._selectionService.shouldForceSelection(W))return H(W),O.mouseup&&this._document.addEventListener("mouseup",O.mouseup),O.mousedrag&&this._document.addEventListener("mousemove",O.mousedrag),this.cancel(W)})),this.register((0,d.addDisposableDomListener)(L,"wheel",W=>{if(!O.wheel){if(this._customWheelEventHandler&&this._customWheelEventHandler(W)===!1)return!1;if(!this.buffer.hasScrollback){const K=this.viewport.getLinesScrolled(W);if(K===0)return;const q=T.C0.ESC+(this.coreService.decPrivateModes.applicationCursorKeys?"O":"[")+(W.deltaY<0?"A":"B");let ee="";for(let re=0;re<Math.abs(K);re++)ee+=q;return this.coreService.triggerDataEvent(ee,!0),this.cancel(W,!0)}return this.viewport.handleWheel(W)?this.cancel(W):void 0}},{passive:!1})),this.register((0,d.addDisposableDomListener)(L,"touchstart",W=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchStart(W),this.cancel(W)},{passive:!0})),this.register((0,d.addDisposableDomListener)(L,"touchmove",W=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchMove(W)?void 0:this.cancel(W)},{passive:!1}))}refresh(M,L){var H;(H=this._renderService)==null||H.refreshRows(M,L)}updateCursorStyle(M){var L;(L=this._selectionService)!=null&&L.shouldColumnSelect(M)?this.element.classList.add("column-select"):this.element.classList.remove("column-select")}_showCursor(){this.coreService.isCursorInitialized||(this.coreService.isCursorInitialized=!0,this.refresh(this.buffer.y,this.buffer.y))}scrollLines(M,L,H=0){var O;H===1?(super.scrollLines(M,L,H),this.refresh(0,this.rows-1)):(O=this.viewport)==null||O.scrollLines(M)}paste(M){(0,_.paste)(M,this.textarea,this.coreService,this.optionsService)}attachCustomKeyEventHandler(M){this._customKeyEventHandler=M}attachCustomWheelEventHandler(M){this._customWheelEventHandler=M}registerLinkProvider(M){return this._linkProviderService.registerLinkProvider(M)}registerCharacterJoiner(M){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");const L=this._characterJoinerService.register(M);return this.refresh(0,this.rows-1),L}deregisterCharacterJoiner(M){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");this._characterJoinerService.deregister(M)&&this.refresh(0,this.rows-1)}get markers(){return this.buffer.markers}registerMarker(M){return this.buffer.addMarker(this.buffer.ybase+this.buffer.y+M)}registerDecoration(M){return this._decorationService.registerDecoration(M)}hasSelection(){return!!this._selectionService&&this._selectionService.hasSelection}select(M,L,H){this._selectionService.setSelection(M,L,H)}getSelection(){return this._selectionService?this._selectionService.selectionText:""}getSelectionPosition(){if(this._selectionService&&this._selectionService.hasSelection)return{start:{x:this._selectionService.selectionStart[0],y:this._selectionService.selectionStart[1]},end:{x:this._selectionService.selectionEnd[0],y:this._selectionService.selectionEnd[1]}}}clearSelection(){var M;(M=this._selectionService)==null||M.clearSelection()}selectAll(){var M;(M=this._selectionService)==null||M.selectAll()}selectLines(M,L){var H;(H=this._selectionService)==null||H.selectLines(M,L)}_keyDown(M){if(this._keyDownHandled=!1,this._keyDownSeen=!0,this._customKeyEventHandler&&this._customKeyEventHandler(M)===!1)return!1;const L=this.browser.isMac&&this.options.macOptionIsMeta&&M.altKey;if(!L&&!this._compositionHelper.keydown(M))return this.options.scrollOnUserInput&&this.buffer.ybase!==this.buffer.ydisp&&this.scrollToBottom(),!1;L||M.key!=="Dead"&&M.key!=="AltGraph"||(this._unprocessedDeadKey=!0);const H=(0,A.evaluateKeyboardEvent)(M,this.coreService.decPrivateModes.applicationCursorKeys,this.browser.isMac,this.options.macOptionIsMeta);if(this.updateCursorStyle(M),H.type===3||H.type===2){const O=this.rows-1;return this.scrollLines(H.type===2?-O:O),this.cancel(M,!0)}return H.type===1&&this.selectAll(),!!this._isThirdLevelShift(this.browser,M)||(H.cancel&&this.cancel(M,!0),!H.key||!!(M.key&&!M.ctrlKey&&!M.altKey&&!M.metaKey&&M.key.length===1&&M.key.charCodeAt(0)>=65&&M.key.charCodeAt(0)<=90)||(this._unprocessedDeadKey?(this._unprocessedDeadKey=!1,!0):(H.key!==T.C0.ETX&&H.key!==T.C0.CR||(this.textarea.value=""),this._onKey.fire({key:H.key,domEvent:M}),this._showCursor(),this.coreService.triggerDataEvent(H.key,!0),!this.optionsService.rawOptions.screenReaderMode||M.altKey||M.ctrlKey?this.cancel(M,!0):void(this._keyDownHandled=!0))))}_isThirdLevelShift(M,L){const H=M.isMac&&!this.options.macOptionIsMeta&&L.altKey&&!L.ctrlKey&&!L.metaKey||M.isWindows&&L.altKey&&L.ctrlKey&&!L.metaKey||M.isWindows&&L.getModifierState("AltGraph");return L.type==="keypress"?H:H&&(!L.keyCode||L.keyCode>47)}_keyUp(M){this._keyDownSeen=!1,this._customKeyEventHandler&&this._customKeyEventHandler(M)===!1||(function(L){return L.keyCode===16||L.keyCode===17||L.keyCode===18}(M)||this.focus(),this.updateCursorStyle(M),this._keyPressHandled=!1)}_keyPress(M){let L;if(this._keyPressHandled=!1,this._keyDownHandled||this._customKeyEventHandler&&this._customKeyEventHandler(M)===!1)return!1;if(this.cancel(M),M.charCode)L=M.charCode;else if(M.which===null||M.which===void 0)L=M.keyCode;else{if(M.which===0||M.charCode===0)return!1;L=M.which}return!(!L||(M.altKey||M.ctrlKey||M.metaKey)&&!this._isThirdLevelShift(this.browser,M)||(L=String.fromCharCode(L),this._onKey.fire({key:L,domEvent:M}),this._showCursor(),this.coreService.triggerDataEvent(L,!0),this._keyPressHandled=!0,this._unprocessedDeadKey=!1,0))}_inputEvent(M){if(M.data&&M.inputType==="insertText"&&(!M.composed||!this._keyDownSeen)&&!this.optionsService.rawOptions.screenReaderMode){if(this._keyPressHandled)return!1;this._unprocessedDeadKey=!1;const L=M.data;return this.coreService.triggerDataEvent(L,!0),this.cancel(M),!0}return!1}resize(M,L){M!==this.cols||L!==this.rows?super.resize(M,L):this._charSizeService&&!this._charSizeService.hasValidSize&&this._charSizeService.measure()}_afterResize(M,L){var H,O;(H=this._charSizeService)==null||H.measure(),(O=this.viewport)==null||O.syncScrollArea(!0)}clear(){var M;if(this.buffer.ybase!==0||this.buffer.y!==0){this.buffer.clearAllMarkers(),this.buffer.lines.set(0,this.buffer.lines.get(this.buffer.ybase+this.buffer.y)),this.buffer.lines.length=1,this.buffer.ydisp=0,this.buffer.ybase=0,this.buffer.y=0;for(let L=1;L<this.rows;L++)this.buffer.lines.push(this.buffer.getBlankLine(P.DEFAULT_ATTR_DATA));this._onScroll.fire({position:this.buffer.ydisp,source:0}),(M=this.viewport)==null||M.reset(),this.refresh(0,this.rows-1)}}reset(){var L,H;this.options.rows=this.rows,this.options.cols=this.cols;const M=this._customKeyEventHandler;this._setup(),super.reset(),(L=this._selectionService)==null||L.reset(),this._decorationService.reset(),(H=this.viewport)==null||H.reset(),this._customKeyEventHandler=M,this.refresh(0,this.rows-1)}clearTextureAtlas(){var M;(M=this._renderService)==null||M.clearTextureAtlas()}_reportFocus(){var M;(M=this.element)!=null&&M.classList.contains("focus")?this.coreService.triggerDataEvent(T.C0.ESC+"[I"):this.coreService.triggerDataEvent(T.C0.ESC+"[O")}_reportWindowsOptions(M){if(this._renderService)switch(M){case R.WindowsOptionsReportType.GET_WIN_SIZE_PIXELS:const L=this._renderService.dimensions.css.canvas.width.toFixed(0),H=this._renderService.dimensions.css.canvas.height.toFixed(0);this.coreService.triggerDataEvent(`${T.C0.ESC}[4;${H};${L}t`);break;case R.WindowsOptionsReportType.GET_CELL_SIZE_PIXELS:const O=this._renderService.dimensions.css.cell.width.toFixed(0),$=this._renderService.dimensions.css.cell.height.toFixed(0);this.coreService.triggerDataEvent(`${T.C0.ESC}[6;${$};${O}t`)}}cancel(M,L){if(this.options.cancelEvents||L)return M.preventDefault(),M.stopPropagation(),!1}}s.Terminal=N},9924:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.TimeBasedDebouncer=void 0,s.TimeBasedDebouncer=class{constructor(a,_=1e3){this._renderCallback=a,this._debounceThresholdMS=_,this._lastRefreshMs=0,this._additionalRefreshRequested=!1}dispose(){this._refreshTimeoutID&&clearTimeout(this._refreshTimeoutID)}refresh(a,_,d){this._rowCount=d,a=a!==void 0?a:0,_=_!==void 0?_:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,a):a,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,_):_;const n=Date.now();if(n-this._lastRefreshMs>=this._debounceThresholdMS)this._lastRefreshMs=n,this._innerRefresh();else if(!this._additionalRefreshRequested){const c=n-this._lastRefreshMs,f=this._debounceThresholdMS-c;this._additionalRefreshRequested=!0,this._refreshTimeoutID=window.setTimeout(()=>{this._lastRefreshMs=Date.now(),this._innerRefresh(),this._additionalRefreshRequested=!1,this._refreshTimeoutID=void 0},f)}}_innerRefresh(){if(this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return;const a=Math.max(this._rowStart,0),_=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(a,_)}}},1680:function(B,s,a){var _=this&&this.__decorate||function(r,t,i,h){var v,l=arguments.length,g=l<3?t:h===null?h=Object.getOwnPropertyDescriptor(t,i):h;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")g=Reflect.decorate(r,t,i,h);else for(var u=r.length-1;u>=0;u--)(v=r[u])&&(g=(l<3?v(g):l>3?v(t,i,g):v(t,i))||g);return l>3&&g&&Object.defineProperty(t,i,g),g},d=this&&this.__param||function(r,t){return function(i,h){t(i,h,r)}};Object.defineProperty(s,"__esModule",{value:!0}),s.Viewport=void 0;const n=a(3656),c=a(4725),f=a(8460),m=a(844),o=a(2585);let e=s.Viewport=class extends m.Disposable{constructor(r,t,i,h,v,l,g,u){super(),this._viewportElement=r,this._scrollArea=t,this._bufferService=i,this._optionsService=h,this._charSizeService=v,this._renderService=l,this._coreBrowserService=g,this.scrollBarWidth=0,this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._wheelPartialScroll=0,this._refreshAnimationFrame=null,this._ignoreNextScrollEvent=!1,this._smoothScrollState={startTime:0,origin:-1,target:-1},this._onRequestScrollLines=this.register(new f.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this.scrollBarWidth=this._viewportElement.offsetWidth-this._scrollArea.offsetWidth||15,this.register((0,n.addDisposableDomListener)(this._viewportElement,"scroll",this._handleScroll.bind(this))),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(p=>this._activeBuffer=p.activeBuffer)),this._renderDimensions=this._renderService.dimensions,this.register(this._renderService.onDimensionsChange(p=>this._renderDimensions=p)),this._handleThemeChange(u.colors),this.register(u.onChangeColors(p=>this._handleThemeChange(p))),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.syncScrollArea())),setTimeout(()=>this.syncScrollArea())}_handleThemeChange(r){this._viewportElement.style.backgroundColor=r.background.css}reset(){this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._coreBrowserService.window.requestAnimationFrame(()=>this.syncScrollArea())}_refresh(r){if(r)return this._innerRefresh(),void(this._refreshAnimationFrame!==null&&this._coreBrowserService.window.cancelAnimationFrame(this._refreshAnimationFrame));this._refreshAnimationFrame===null&&(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._charSizeService.height>0){this._currentRowHeight=this._renderDimensions.device.cell.height/this._coreBrowserService.dpr,this._currentDeviceCellHeight=this._renderDimensions.device.cell.height,this._lastRecordedViewportHeight=this._viewportElement.offsetHeight;const t=Math.round(this._currentRowHeight*this._lastRecordedBufferLength)+(this._lastRecordedViewportHeight-this._renderDimensions.css.canvas.height);this._lastRecordedBufferHeight!==t&&(this._lastRecordedBufferHeight=t,this._scrollArea.style.height=this._lastRecordedBufferHeight+"px")}const r=this._bufferService.buffer.ydisp*this._currentRowHeight;this._viewportElement.scrollTop!==r&&(this._ignoreNextScrollEvent=!0,this._viewportElement.scrollTop=r),this._refreshAnimationFrame=null}syncScrollArea(r=!1){if(this._lastRecordedBufferLength!==this._bufferService.buffer.lines.length)return this._lastRecordedBufferLength=this._bufferService.buffer.lines.length,void this._refresh(r);this._lastRecordedViewportHeight===this._renderService.dimensions.css.canvas.height&&this._lastScrollTop===this._activeBuffer.ydisp*this._currentRowHeight&&this._renderDimensions.device.cell.height===this._currentDeviceCellHeight||this._refresh(r)}_handleScroll(r){if(this._lastScrollTop=this._viewportElement.scrollTop,!this._viewportElement.offsetParent)return;if(this._ignoreNextScrollEvent)return this._ignoreNextScrollEvent=!1,void this._onRequestScrollLines.fire({amount:0,suppressScrollEvent:!0});const t=Math.round(this._lastScrollTop/this._currentRowHeight)-this._bufferService.buffer.ydisp;this._onRequestScrollLines.fire({amount:t,suppressScrollEvent:!0})}_smoothScroll(){if(this._isDisposed||this._smoothScrollState.origin===-1||this._smoothScrollState.target===-1)return;const r=this._smoothScrollPercent();this._viewportElement.scrollTop=this._smoothScrollState.origin+Math.round(r*(this._smoothScrollState.target-this._smoothScrollState.origin)),r<1?this._coreBrowserService.window.requestAnimationFrame(()=>this._smoothScroll()):this._clearSmoothScrollState()}_smoothScrollPercent(){return this._optionsService.rawOptions.smoothScrollDuration&&this._smoothScrollState.startTime?Math.max(Math.min((Date.now()-this._smoothScrollState.startTime)/this._optionsService.rawOptions.smoothScrollDuration,1),0):1}_clearSmoothScrollState(){this._smoothScrollState.startTime=0,this._smoothScrollState.origin=-1,this._smoothScrollState.target=-1}_bubbleScroll(r,t){const i=this._viewportElement.scrollTop+this._lastRecordedViewportHeight;return!(t<0&&this._viewportElement.scrollTop!==0||t>0&&i<this._lastRecordedBufferHeight)||(r.cancelable&&r.preventDefault(),!1)}handleWheel(r){const t=this._getPixelsScrolled(r);return t!==0&&(this._optionsService.rawOptions.smoothScrollDuration?(this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target===-1?this._smoothScrollState.target=this._viewportElement.scrollTop+t:this._smoothScrollState.target+=t,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()):this._viewportElement.scrollTop+=t,this._bubbleScroll(r,t))}scrollLines(r){if(r!==0)if(this._optionsService.rawOptions.smoothScrollDuration){const t=r*this._currentRowHeight;this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target=this._smoothScrollState.origin+t,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()}else this._onRequestScrollLines.fire({amount:r,suppressScrollEvent:!1})}_getPixelsScrolled(r){if(r.deltaY===0||r.shiftKey)return 0;let t=this._applyScrollModifier(r.deltaY,r);return r.deltaMode===WheelEvent.DOM_DELTA_LINE?t*=this._currentRowHeight:r.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(t*=this._currentRowHeight*this._bufferService.rows),t}getBufferElements(r,t){var u;let i,h="";const v=[],l=t??this._bufferService.buffer.lines.length,g=this._bufferService.buffer.lines;for(let p=r;p<l;p++){const C=g.get(p);if(!C)continue;const b=(u=g.get(p+1))==null?void 0:u.isWrapped;if(h+=C.translateToString(!b),!b||p===g.length-1){const y=document.createElement("div");y.textContent=h,v.push(y),h.length>0&&(i=y),h=""}}return{bufferElements:v,cursorElement:i}}getLinesScrolled(r){if(r.deltaY===0||r.shiftKey)return 0;let t=this._applyScrollModifier(r.deltaY,r);return r.deltaMode===WheelEvent.DOM_DELTA_PIXEL?(t/=this._currentRowHeight+0,this._wheelPartialScroll+=t,t=Math.floor(Math.abs(this._wheelPartialScroll))*(this._wheelPartialScroll>0?1:-1),this._wheelPartialScroll%=1):r.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(t*=this._bufferService.rows),t}_applyScrollModifier(r,t){const i=this._optionsService.rawOptions.fastScrollModifier;return i==="alt"&&t.altKey||i==="ctrl"&&t.ctrlKey||i==="shift"&&t.shiftKey?r*this._optionsService.rawOptions.fastScrollSensitivity*this._optionsService.rawOptions.scrollSensitivity:r*this._optionsService.rawOptions.scrollSensitivity}handleTouchStart(r){this._lastTouchY=r.touches[0].pageY}handleTouchMove(r){const t=this._lastTouchY-r.touches[0].pageY;return this._lastTouchY=r.touches[0].pageY,t!==0&&(this._viewportElement.scrollTop+=t,this._bubbleScroll(r,t))}};s.Viewport=e=_([d(2,o.IBufferService),d(3,o.IOptionsService),d(4,c.ICharSizeService),d(5,c.IRenderService),d(6,c.ICoreBrowserService),d(7,c.IThemeService)],e)},3107:function(B,s,a){var _=this&&this.__decorate||function(o,e,r,t){var i,h=arguments.length,v=h<3?e:t===null?t=Object.getOwnPropertyDescriptor(e,r):t;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(o,e,r,t);else for(var l=o.length-1;l>=0;l--)(i=o[l])&&(v=(h<3?i(v):h>3?i(e,r,v):i(e,r))||v);return h>3&&v&&Object.defineProperty(e,r,v),v},d=this&&this.__param||function(o,e){return function(r,t){e(r,t,o)}};Object.defineProperty(s,"__esModule",{value:!0}),s.BufferDecorationRenderer=void 0;const n=a(4725),c=a(844),f=a(2585);let m=s.BufferDecorationRenderer=class extends c.Disposable{constructor(o,e,r,t,i){super(),this._screenElement=o,this._bufferService=e,this._coreBrowserService=r,this._decorationService=t,this._renderService=i,this._decorationElements=new Map,this._altBufferIsActive=!1,this._dimensionsChanged=!1,this._container=document.createElement("div"),this._container.classList.add("xterm-decoration-container"),this._screenElement.appendChild(this._container),this.register(this._renderService.onRenderedViewportChange(()=>this._doRefreshDecorations())),this.register(this._renderService.onDimensionsChange(()=>{this._dimensionsChanged=!0,this._queueRefresh()})),this.register(this._coreBrowserService.onDprChange(()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._altBufferIsActive=this._bufferService.buffer===this._bufferService.buffers.alt})),this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh())),this.register(this._decorationService.onDecorationRemoved(h=>this._removeDecoration(h))),this.register((0,c.toDisposable)(()=>{this._container.remove(),this._decorationElements.clear()}))}_queueRefresh(){this._animationFrame===void 0&&(this._animationFrame=this._renderService.addRefreshCallback(()=>{this._doRefreshDecorations(),this._animationFrame=void 0}))}_doRefreshDecorations(){for(const o of this._decorationService.decorations)this._renderDecoration(o);this._dimensionsChanged=!1}_renderDecoration(o){this._refreshStyle(o),this._dimensionsChanged&&this._refreshXPosition(o)}_createElement(o){var t;const e=this._coreBrowserService.mainDocument.createElement("div");e.classList.add("xterm-decoration"),e.classList.toggle("xterm-decoration-top-layer",((t=o==null?void 0:o.options)==null?void 0:t.layer)==="top"),e.style.width=`${Math.round((o.options.width||1)*this._renderService.dimensions.css.cell.width)}px`,e.style.height=(o.options.height||1)*this._renderService.dimensions.css.cell.height+"px",e.style.top=(o.marker.line-this._bufferService.buffers.active.ydisp)*this._renderService.dimensions.css.cell.height+"px",e.style.lineHeight=`${this._renderService.dimensions.css.cell.height}px`;const r=o.options.x??0;return r&&r>this._bufferService.cols&&(e.style.display="none"),this._refreshXPosition(o,e),e}_refreshStyle(o){const e=o.marker.line-this._bufferService.buffers.active.ydisp;if(e<0||e>=this._bufferService.rows)o.element&&(o.element.style.display="none",o.onRenderEmitter.fire(o.element));else{let r=this._decorationElements.get(o);r||(r=this._createElement(o),o.element=r,this._decorationElements.set(o,r),this._container.appendChild(r),o.onDispose(()=>{this._decorationElements.delete(o),r.remove()})),r.style.top=e*this._renderService.dimensions.css.cell.height+"px",r.style.display=this._altBufferIsActive?"none":"block",o.onRenderEmitter.fire(r)}}_refreshXPosition(o,e=o.element){if(!e)return;const r=o.options.x??0;(o.options.anchor||"left")==="right"?e.style.right=r?r*this._renderService.dimensions.css.cell.width+"px":"":e.style.left=r?r*this._renderService.dimensions.css.cell.width+"px":""}_removeDecoration(o){var e;(e=this._decorationElements.get(o))==null||e.remove(),this._decorationElements.delete(o),o.dispose()}};s.BufferDecorationRenderer=m=_([d(1,f.IBufferService),d(2,n.ICoreBrowserService),d(3,f.IDecorationService),d(4,n.IRenderService)],m)},5871:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ColorZoneStore=void 0,s.ColorZoneStore=class{constructor(){this._zones=[],this._zonePool=[],this._zonePoolIndex=0,this._linePadding={full:0,left:0,center:0,right:0}}get zones(){return this._zonePool.length=Math.min(this._zonePool.length,this._zones.length),this._zones}clear(){this._zones.length=0,this._zonePoolIndex=0}addDecoration(a){if(a.options.overviewRulerOptions){for(const _ of this._zones)if(_.color===a.options.overviewRulerOptions.color&&_.position===a.options.overviewRulerOptions.position){if(this._lineIntersectsZone(_,a.marker.line))return;if(this._lineAdjacentToZone(_,a.marker.line,a.options.overviewRulerOptions.position))return void this._addLineToZone(_,a.marker.line)}if(this._zonePoolIndex<this._zonePool.length)return this._zonePool[this._zonePoolIndex].color=a.options.overviewRulerOptions.color,this._zonePool[this._zonePoolIndex].position=a.options.overviewRulerOptions.position,this._zonePool[this._zonePoolIndex].startBufferLine=a.marker.line,this._zonePool[this._zonePoolIndex].endBufferLine=a.marker.line,void this._zones.push(this._zonePool[this._zonePoolIndex++]);this._zones.push({color:a.options.overviewRulerOptions.color,position:a.options.overviewRulerOptions.position,startBufferLine:a.marker.line,endBufferLine:a.marker.line}),this._zonePool.push(this._zones[this._zones.length-1]),this._zonePoolIndex++}}setPadding(a){this._linePadding=a}_lineIntersectsZone(a,_){return _>=a.startBufferLine&&_<=a.endBufferLine}_lineAdjacentToZone(a,_,d){return _>=a.startBufferLine-this._linePadding[d||"full"]&&_<=a.endBufferLine+this._linePadding[d||"full"]}_addLineToZone(a,_){a.startBufferLine=Math.min(a.startBufferLine,_),a.endBufferLine=Math.max(a.endBufferLine,_)}}},5744:function(B,s,a){var _=this&&this.__decorate||function(i,h,v,l){var g,u=arguments.length,p=u<3?h:l===null?l=Object.getOwnPropertyDescriptor(h,v):l;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")p=Reflect.decorate(i,h,v,l);else for(var C=i.length-1;C>=0;C--)(g=i[C])&&(p=(u<3?g(p):u>3?g(h,v,p):g(h,v))||p);return u>3&&p&&Object.defineProperty(h,v,p),p},d=this&&this.__param||function(i,h){return function(v,l){h(v,l,i)}};Object.defineProperty(s,"__esModule",{value:!0}),s.OverviewRulerRenderer=void 0;const n=a(5871),c=a(4725),f=a(844),m=a(2585),o={full:0,left:0,center:0,right:0},e={full:0,left:0,center:0,right:0},r={full:0,left:0,center:0,right:0};let t=s.OverviewRulerRenderer=class extends f.Disposable{get _width(){return this._optionsService.options.overviewRulerWidth||0}constructor(i,h,v,l,g,u,p){var b;super(),this._viewportElement=i,this._screenElement=h,this._bufferService=v,this._decorationService=l,this._renderService=g,this._optionsService=u,this._coreBrowserService=p,this._colorZoneStore=new n.ColorZoneStore,this._shouldUpdateDimensions=!0,this._shouldUpdateAnchor=!0,this._lastKnownBufferLength=0,this._canvas=this._coreBrowserService.mainDocument.createElement("canvas"),this._canvas.classList.add("xterm-decoration-overview-ruler"),this._refreshCanvasDimensions(),(b=this._viewportElement.parentElement)==null||b.insertBefore(this._canvas,this._viewportElement);const C=this._canvas.getContext("2d");if(!C)throw new Error("Ctx cannot be null");this._ctx=C,this._registerDecorationListeners(),this._registerBufferChangeListeners(),this._registerDimensionChangeListeners(),this.register((0,f.toDisposable)(()=>{var y;(y=this._canvas)==null||y.remove()}))}_registerDecorationListeners(){this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh(void 0,!0))),this.register(this._decorationService.onDecorationRemoved(()=>this._queueRefresh(void 0,!0)))}_registerBufferChangeListeners(){this.register(this._renderService.onRenderedViewportChange(()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._canvas.style.display=this._bufferService.buffer===this._bufferService.buffers.alt?"none":"block"})),this.register(this._bufferService.onScroll(()=>{this._lastKnownBufferLength!==this._bufferService.buffers.normal.lines.length&&(this._refreshDrawHeightConstants(),this._refreshColorZonePadding())}))}_registerDimensionChangeListeners(){this.register(this._renderService.onRender(()=>{this._containerHeight&&this._containerHeight===this._screenElement.clientHeight||(this._queueRefresh(!0),this._containerHeight=this._screenElement.clientHeight)})),this.register(this._optionsService.onSpecificOptionChange("overviewRulerWidth",()=>this._queueRefresh(!0))),this.register(this._coreBrowserService.onDprChange(()=>this._queueRefresh(!0))),this._queueRefresh(!0)}_refreshDrawConstants(){const i=Math.floor(this._canvas.width/3),h=Math.ceil(this._canvas.width/3);e.full=this._canvas.width,e.left=i,e.center=h,e.right=i,this._refreshDrawHeightConstants(),r.full=0,r.left=0,r.center=e.left,r.right=e.left+e.center}_refreshDrawHeightConstants(){o.full=Math.round(2*this._coreBrowserService.dpr);const i=this._canvas.height/this._bufferService.buffer.lines.length,h=Math.round(Math.max(Math.min(i,12),6)*this._coreBrowserService.dpr);o.left=h,o.center=h,o.right=h}_refreshColorZonePadding(){this._colorZoneStore.setPadding({full:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*o.full),left:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*o.left),center:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*o.center),right:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*o.right)}),this._lastKnownBufferLength=this._bufferService.buffers.normal.lines.length}_refreshCanvasDimensions(){this._canvas.style.width=`${this._width}px`,this._canvas.width=Math.round(this._width*this._coreBrowserService.dpr),this._canvas.style.height=`${this._screenElement.clientHeight}px`,this._canvas.height=Math.round(this._screenElement.clientHeight*this._coreBrowserService.dpr),this._refreshDrawConstants(),this._refreshColorZonePadding()}_refreshDecorations(){this._shouldUpdateDimensions&&this._refreshCanvasDimensions(),this._ctx.clearRect(0,0,this._canvas.width,this._canvas.height),this._colorZoneStore.clear();for(const h of this._decorationService.decorations)this._colorZoneStore.addDecoration(h);this._ctx.lineWidth=1;const i=this._colorZoneStore.zones;for(const h of i)h.position!=="full"&&this._renderColorZone(h);for(const h of i)h.position==="full"&&this._renderColorZone(h);this._shouldUpdateDimensions=!1,this._shouldUpdateAnchor=!1}_renderColorZone(i){this._ctx.fillStyle=i.color,this._ctx.fillRect(r[i.position||"full"],Math.round((this._canvas.height-1)*(i.startBufferLine/this._bufferService.buffers.active.lines.length)-o[i.position||"full"]/2),e[i.position||"full"],Math.round((this._canvas.height-1)*((i.endBufferLine-i.startBufferLine)/this._bufferService.buffers.active.lines.length)+o[i.position||"full"]))}_queueRefresh(i,h){this._shouldUpdateDimensions=i||this._shouldUpdateDimensions,this._shouldUpdateAnchor=h||this._shouldUpdateAnchor,this._animationFrame===void 0&&(this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>{this._refreshDecorations(),this._animationFrame=void 0}))}};s.OverviewRulerRenderer=t=_([d(2,m.IBufferService),d(3,m.IDecorationService),d(4,c.IRenderService),d(5,m.IOptionsService),d(6,c.ICoreBrowserService)],t)},2950:function(B,s,a){var _=this&&this.__decorate||function(o,e,r,t){var i,h=arguments.length,v=h<3?e:t===null?t=Object.getOwnPropertyDescriptor(e,r):t;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(o,e,r,t);else for(var l=o.length-1;l>=0;l--)(i=o[l])&&(v=(h<3?i(v):h>3?i(e,r,v):i(e,r))||v);return h>3&&v&&Object.defineProperty(e,r,v),v},d=this&&this.__param||function(o,e){return function(r,t){e(r,t,o)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CompositionHelper=void 0;const n=a(4725),c=a(2585),f=a(2584);let m=s.CompositionHelper=class{get isComposing(){return this._isComposing}constructor(o,e,r,t,i,h){this._textarea=o,this._compositionView=e,this._bufferService=r,this._optionsService=t,this._coreService=i,this._renderService=h,this._isComposing=!1,this._isSendingComposition=!1,this._compositionPosition={start:0,end:0},this._dataAlreadySent=""}compositionstart(){this._isComposing=!0,this._compositionPosition.start=this._textarea.value.length,this._compositionView.textContent="",this._dataAlreadySent="",this._compositionView.classList.add("active")}compositionupdate(o){this._compositionView.textContent=o.data,this.updateCompositionElements(),setTimeout(()=>{this._compositionPosition.end=this._textarea.value.length},0)}compositionend(){this._finalizeComposition(!0)}keydown(o){if(this._isComposing||this._isSendingComposition){if(o.keyCode===229||o.keyCode===16||o.keyCode===17||o.keyCode===18)return!1;this._finalizeComposition(!1)}return o.keyCode!==229||(this._handleAnyTextareaChanges(),!1)}_finalizeComposition(o){if(this._compositionView.classList.remove("active"),this._isComposing=!1,o){const e={start:this._compositionPosition.start,end:this._compositionPosition.end};this._isSendingComposition=!0,setTimeout(()=>{if(this._isSendingComposition){let r;this._isSendingComposition=!1,e.start+=this._dataAlreadySent.length,r=this._isComposing?this._textarea.value.substring(e.start,e.end):this._textarea.value.substring(e.start),r.length>0&&this._coreService.triggerDataEvent(r,!0)}},0)}else{this._isSendingComposition=!1;const e=this._textarea.value.substring(this._compositionPosition.start,this._compositionPosition.end);this._coreService.triggerDataEvent(e,!0)}}_handleAnyTextareaChanges(){const o=this._textarea.value;setTimeout(()=>{if(!this._isComposing){const e=this._textarea.value,r=e.replace(o,"");this._dataAlreadySent=r,e.length>o.length?this._coreService.triggerDataEvent(r,!0):e.length<o.length?this._coreService.triggerDataEvent(`${f.C0.DEL}`,!0):e.length===o.length&&e!==o&&this._coreService.triggerDataEvent(e,!0)}},0)}updateCompositionElements(o){if(this._isComposing){if(this._bufferService.buffer.isCursorInViewport){const e=Math.min(this._bufferService.buffer.x,this._bufferService.cols-1),r=this._renderService.dimensions.css.cell.height,t=this._bufferService.buffer.y*this._renderService.dimensions.css.cell.height,i=e*this._renderService.dimensions.css.cell.width;this._compositionView.style.left=i+"px",this._compositionView.style.top=t+"px",this._compositionView.style.height=r+"px",this._compositionView.style.lineHeight=r+"px",this._compositionView.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._compositionView.style.fontSize=this._optionsService.rawOptions.fontSize+"px";const h=this._compositionView.getBoundingClientRect();this._textarea.style.left=i+"px",this._textarea.style.top=t+"px",this._textarea.style.width=Math.max(h.width,1)+"px",this._textarea.style.height=Math.max(h.height,1)+"px",this._textarea.style.lineHeight=h.height+"px"}o||setTimeout(()=>this.updateCompositionElements(!0),0)}}};s.CompositionHelper=m=_([d(2,c.IBufferService),d(3,c.IOptionsService),d(4,c.ICoreService),d(5,n.IRenderService)],m)},9806:(B,s)=>{function a(_,d,n){const c=n.getBoundingClientRect(),f=_.getComputedStyle(n),m=parseInt(f.getPropertyValue("padding-left")),o=parseInt(f.getPropertyValue("padding-top"));return[d.clientX-c.left-m,d.clientY-c.top-o]}Object.defineProperty(s,"__esModule",{value:!0}),s.getCoords=s.getCoordsRelativeToElement=void 0,s.getCoordsRelativeToElement=a,s.getCoords=function(_,d,n,c,f,m,o,e,r){if(!m)return;const t=a(_,d,n);return t?(t[0]=Math.ceil((t[0]+(r?o/2:0))/o),t[1]=Math.ceil(t[1]/e),t[0]=Math.min(Math.max(t[0],1),c+(r?1:0)),t[1]=Math.min(Math.max(t[1],1),f),t):void 0}},9504:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.moveToCellSequence=void 0;const _=a(2584);function d(e,r,t,i){const h=e-n(e,t),v=r-n(r,t),l=Math.abs(h-v)-function(g,u,p){let C=0;const b=g-n(g,p),y=u-n(u,p);for(let w=0;w<Math.abs(b-y);w++){const k=c(g,u)==="A"?-1:1,D=p.buffer.lines.get(b+k*w);D!=null&&D.isWrapped&&C++}return C}(e,r,t);return o(l,m(c(e,r),i))}function n(e,r){let t=0,i=r.buffer.lines.get(e),h=i==null?void 0:i.isWrapped;for(;h&&e>=0&&e<r.rows;)t++,i=r.buffer.lines.get(--e),h=i==null?void 0:i.isWrapped;return t}function c(e,r){return e>r?"A":"B"}function f(e,r,t,i,h,v){let l=e,g=r,u="";for(;l!==t||g!==i;)l+=h?1:-1,h&&l>v.cols-1?(u+=v.buffer.translateBufferLineToString(g,!1,e,l),l=0,e=0,g++):!h&&l<0&&(u+=v.buffer.translateBufferLineToString(g,!1,0,e+1),l=v.cols-1,e=l,g--);return u+v.buffer.translateBufferLineToString(g,!1,e,l)}function m(e,r){const t=r?"O":"[";return _.C0.ESC+t+e}function o(e,r){e=Math.floor(e);let t="";for(let i=0;i<e;i++)t+=r;return t}s.moveToCellSequence=function(e,r,t,i){const h=t.buffer.x,v=t.buffer.y;if(!t.buffer.hasScrollback)return function(u,p,C,b,y,w){return d(p,b,y,w).length===0?"":o(f(u,p,u,p-n(p,y),!1,y).length,m("D",w))}(h,v,0,r,t,i)+d(v,r,t,i)+function(u,p,C,b,y,w){let k;k=d(p,b,y,w).length>0?b-n(b,y):p;const D=b,P=function(T,A,S,x,E,R){let I;return I=d(S,x,E,R).length>0?x-n(x,E):A,T<S&&I<=x||T>=S&&I<x?"C":"D"}(u,p,C,b,y,w);return o(f(u,k,C,D,P==="C",y).length,m(P,w))}(h,v,e,r,t,i);let l;if(v===r)return l=h>e?"D":"C",o(Math.abs(h-e),m(l,i));l=v>r?"D":"C";const g=Math.abs(v-r);return o(function(u,p){return p.cols-u}(v>r?e:h,t)+(g-1)*t.cols+1+((v>r?h:e)-1),m(l,i))}},1296:function(B,s,a){var _=this&&this.__decorate||function(w,k,D,P){var T,A=arguments.length,S=A<3?k:P===null?P=Object.getOwnPropertyDescriptor(k,D):P;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")S=Reflect.decorate(w,k,D,P);else for(var x=w.length-1;x>=0;x--)(T=w[x])&&(S=(A<3?T(S):A>3?T(k,D,S):T(k,D))||S);return A>3&&S&&Object.defineProperty(k,D,S),S},d=this&&this.__param||function(w,k){return function(D,P){k(D,P,w)}};Object.defineProperty(s,"__esModule",{value:!0}),s.DomRenderer=void 0;const n=a(3787),c=a(2550),f=a(2223),m=a(6171),o=a(6052),e=a(4725),r=a(8055),t=a(8460),i=a(844),h=a(2585),v="xterm-dom-renderer-owner-",l="xterm-rows",g="xterm-fg-",u="xterm-bg-",p="xterm-focus",C="xterm-selection";let b=1,y=s.DomRenderer=class extends i.Disposable{constructor(w,k,D,P,T,A,S,x,E,R,I,F,N){super(),this._terminal=w,this._document=k,this._element=D,this._screenElement=P,this._viewportElement=T,this._helperContainer=A,this._linkifier2=S,this._charSizeService=E,this._optionsService=R,this._bufferService=I,this._coreBrowserService=F,this._themeService=N,this._terminalClass=b++,this._rowElements=[],this._selectionRenderModel=(0,o.createSelectionRenderModel)(),this.onRequestRedraw=this.register(new t.EventEmitter).event,this._rowContainer=this._document.createElement("div"),this._rowContainer.classList.add(l),this._rowContainer.style.lineHeight="normal",this._rowContainer.setAttribute("aria-hidden","true"),this._refreshRowElements(this._bufferService.cols,this._bufferService.rows),this._selectionContainer=this._document.createElement("div"),this._selectionContainer.classList.add(C),this._selectionContainer.setAttribute("aria-hidden","true"),this.dimensions=(0,m.createRenderDimensions)(),this._updateDimensions(),this.register(this._optionsService.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._themeService.onChangeColors(z=>this._injectCss(z))),this._injectCss(this._themeService.colors),this._rowFactory=x.createInstance(n.DomRendererRowFactory,document),this._element.classList.add(v+this._terminalClass),this._screenElement.appendChild(this._rowContainer),this._screenElement.appendChild(this._selectionContainer),this.register(this._linkifier2.onShowLinkUnderline(z=>this._handleLinkHover(z))),this.register(this._linkifier2.onHideLinkUnderline(z=>this._handleLinkLeave(z))),this.register((0,i.toDisposable)(()=>{this._element.classList.remove(v+this._terminalClass),this._rowContainer.remove(),this._selectionContainer.remove(),this._widthCache.dispose(),this._themeStyleElement.remove(),this._dimensionsStyleElement.remove()})),this._widthCache=new c.WidthCache(this._document,this._helperContainer),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}_updateDimensions(){const w=this._coreBrowserService.dpr;this.dimensions.device.char.width=this._charSizeService.width*w,this.dimensions.device.char.height=Math.ceil(this._charSizeService.height*w),this.dimensions.device.cell.width=this.dimensions.device.char.width+Math.round(this._optionsService.rawOptions.letterSpacing),this.dimensions.device.cell.height=Math.floor(this.dimensions.device.char.height*this._optionsService.rawOptions.lineHeight),this.dimensions.device.char.left=0,this.dimensions.device.char.top=0,this.dimensions.device.canvas.width=this.dimensions.device.cell.width*this._bufferService.cols,this.dimensions.device.canvas.height=this.dimensions.device.cell.height*this._bufferService.rows,this.dimensions.css.canvas.width=Math.round(this.dimensions.device.canvas.width/w),this.dimensions.css.canvas.height=Math.round(this.dimensions.device.canvas.height/w),this.dimensions.css.cell.width=this.dimensions.css.canvas.width/this._bufferService.cols,this.dimensions.css.cell.height=this.dimensions.css.canvas.height/this._bufferService.rows;for(const D of this._rowElements)D.style.width=`${this.dimensions.css.canvas.width}px`,D.style.height=`${this.dimensions.css.cell.height}px`,D.style.lineHeight=`${this.dimensions.css.cell.height}px`,D.style.overflow="hidden";this._dimensionsStyleElement||(this._dimensionsStyleElement=this._document.createElement("style"),this._screenElement.appendChild(this._dimensionsStyleElement));const k=`${this._terminalSelector} .${l} span { display: inline-block; height: 100%; vertical-align: top;}`;this._dimensionsStyleElement.textContent=k,this._selectionContainer.style.height=this._viewportElement.style.height,this._screenElement.style.width=`${this.dimensions.css.canvas.width}px`,this._screenElement.style.height=`${this.dimensions.css.canvas.height}px`}_injectCss(w){this._themeStyleElement||(this._themeStyleElement=this._document.createElement("style"),this._screenElement.appendChild(this._themeStyleElement));let k=`${this._terminalSelector} .${l} { color: ${w.foreground.css}; font-family: ${this._optionsService.rawOptions.fontFamily}; font-size: ${this._optionsService.rawOptions.fontSize}px; font-kerning: none; white-space: pre}`;k+=`${this._terminalSelector} .${l} .xterm-dim { color: ${r.color.multiplyOpacity(w.foreground,.5).css};}`,k+=`${this._terminalSelector} span:not(.xterm-bold) { font-weight: ${this._optionsService.rawOptions.fontWeight};}${this._terminalSelector} span.xterm-bold { font-weight: ${this._optionsService.rawOptions.fontWeightBold};}${this._terminalSelector} span.xterm-italic { font-style: italic;}`;const D=`blink_underline_${this._terminalClass}`,P=`blink_bar_${this._terminalClass}`,T=`blink_block_${this._terminalClass}`;k+=`@keyframes ${D} { 50% {  border-bottom-style: hidden; }}`,k+=`@keyframes ${P} { 50% {  box-shadow: none; }}`,k+=`@keyframes ${T} { 0% {  background-color: ${w.cursor.css};  color: ${w.cursorAccent.css}; } 50% {  background-color: inherit;  color: ${w.cursor.css}; }}`,k+=`${this._terminalSelector} .${l}.${p} .xterm-cursor.xterm-cursor-blink.xterm-cursor-underline { animation: ${D} 1s step-end infinite;}${this._terminalSelector} .${l}.${p} .xterm-cursor.xterm-cursor-blink.xterm-cursor-bar { animation: ${P} 1s step-end infinite;}${this._terminalSelector} .${l}.${p} .xterm-cursor.xterm-cursor-blink.xterm-cursor-block { animation: ${T} 1s step-end infinite;}${this._terminalSelector} .${l} .xterm-cursor.xterm-cursor-block { background-color: ${w.cursor.css}; color: ${w.cursorAccent.css};}${this._terminalSelector} .${l} .xterm-cursor.xterm-cursor-block:not(.xterm-cursor-blink) { background-color: ${w.cursor.css} !important; color: ${w.cursorAccent.css} !important;}${this._terminalSelector} .${l} .xterm-cursor.xterm-cursor-outline { outline: 1px solid ${w.cursor.css}; outline-offset: -1px;}${this._terminalSelector} .${l} .xterm-cursor.xterm-cursor-bar { box-shadow: ${this._optionsService.rawOptions.cursorWidth}px 0 0 ${w.cursor.css} inset;}${this._terminalSelector} .${l} .xterm-cursor.xterm-cursor-underline { border-bottom: 1px ${w.cursor.css}; border-bottom-style: solid; height: calc(100% - 1px);}`,k+=`${this._terminalSelector} .${C} { position: absolute; top: 0; left: 0; z-index: 1; pointer-events: none;}${this._terminalSelector}.focus .${C} div { position: absolute; background-color: ${w.selectionBackgroundOpaque.css};}${this._terminalSelector} .${C} div { position: absolute; background-color: ${w.selectionInactiveBackgroundOpaque.css};}`;for(const[A,S]of w.ansi.entries())k+=`${this._terminalSelector} .${g}${A} { color: ${S.css}; }${this._terminalSelector} .${g}${A}.xterm-dim { color: ${r.color.multiplyOpacity(S,.5).css}; }${this._terminalSelector} .${u}${A} { background-color: ${S.css}; }`;k+=`${this._terminalSelector} .${g}${f.INVERTED_DEFAULT_COLOR} { color: ${r.color.opaque(w.background).css}; }${this._terminalSelector} .${g}${f.INVERTED_DEFAULT_COLOR}.xterm-dim { color: ${r.color.multiplyOpacity(r.color.opaque(w.background),.5).css}; }${this._terminalSelector} .${u}${f.INVERTED_DEFAULT_COLOR} { background-color: ${w.foreground.css}; }`,this._themeStyleElement.textContent=k}_setDefaultSpacing(){const w=this.dimensions.css.cell.width-this._widthCache.get("W",!1,!1);this._rowContainer.style.letterSpacing=`${w}px`,this._rowFactory.defaultSpacing=w}handleDevicePixelRatioChange(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}_refreshRowElements(w,k){for(let D=this._rowElements.length;D<=k;D++){const P=this._document.createElement("div");this._rowContainer.appendChild(P),this._rowElements.push(P)}for(;this._rowElements.length>k;)this._rowContainer.removeChild(this._rowElements.pop())}handleResize(w,k){this._refreshRowElements(w,k),this._updateDimensions(),this.handleSelectionChanged(this._selectionRenderModel.selectionStart,this._selectionRenderModel.selectionEnd,this._selectionRenderModel.columnSelectMode)}handleCharSizeChanged(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}handleBlur(){this._rowContainer.classList.remove(p),this.renderRows(0,this._bufferService.rows-1)}handleFocus(){this._rowContainer.classList.add(p),this.renderRows(this._bufferService.buffer.y,this._bufferService.buffer.y)}handleSelectionChanged(w,k,D){if(this._selectionContainer.replaceChildren(),this._rowFactory.handleSelectionChanged(w,k,D),this.renderRows(0,this._bufferService.rows-1),!w||!k)return;this._selectionRenderModel.update(this._terminal,w,k,D);const P=this._selectionRenderModel.viewportStartRow,T=this._selectionRenderModel.viewportEndRow,A=this._selectionRenderModel.viewportCappedStartRow,S=this._selectionRenderModel.viewportCappedEndRow;if(A>=this._bufferService.rows||S<0)return;const x=this._document.createDocumentFragment();if(D){const E=w[0]>k[0];x.appendChild(this._createSelectionElement(A,E?k[0]:w[0],E?w[0]:k[0],S-A+1))}else{const E=P===A?w[0]:0,R=A===T?k[0]:this._bufferService.cols;x.appendChild(this._createSelectionElement(A,E,R));const I=S-A-1;if(x.appendChild(this._createSelectionElement(A+1,0,this._bufferService.cols,I)),A!==S){const F=T===S?k[0]:this._bufferService.cols;x.appendChild(this._createSelectionElement(S,0,F))}}this._selectionContainer.appendChild(x)}_createSelectionElement(w,k,D,P=1){const T=this._document.createElement("div"),A=k*this.dimensions.css.cell.width;let S=this.dimensions.css.cell.width*(D-k);return A+S>this.dimensions.css.canvas.width&&(S=this.dimensions.css.canvas.width-A),T.style.height=P*this.dimensions.css.cell.height+"px",T.style.top=w*this.dimensions.css.cell.height+"px",T.style.left=`${A}px`,T.style.width=`${S}px`,T}handleCursorMove(){}_handleOptionsChanged(){this._updateDimensions(),this._injectCss(this._themeService.colors),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}clear(){for(const w of this._rowElements)w.replaceChildren()}renderRows(w,k){const D=this._bufferService.buffer,P=D.ybase+D.y,T=Math.min(D.x,this._bufferService.cols-1),A=this._optionsService.rawOptions.cursorBlink,S=this._optionsService.rawOptions.cursorStyle,x=this._optionsService.rawOptions.cursorInactiveStyle;for(let E=w;E<=k;E++){const R=E+D.ydisp,I=this._rowElements[E],F=D.lines.get(R);if(!I||!F)break;I.replaceChildren(...this._rowFactory.createRow(F,R,R===P,S,x,T,A,this.dimensions.css.cell.width,this._widthCache,-1,-1))}}get _terminalSelector(){return`.${v}${this._terminalClass}`}_handleLinkHover(w){this._setCellUnderline(w.x1,w.x2,w.y1,w.y2,w.cols,!0)}_handleLinkLeave(w){this._setCellUnderline(w.x1,w.x2,w.y1,w.y2,w.cols,!1)}_setCellUnderline(w,k,D,P,T,A){D<0&&(w=0),P<0&&(k=0);const S=this._bufferService.rows-1;D=Math.max(Math.min(D,S),0),P=Math.max(Math.min(P,S),0),T=Math.min(T,this._bufferService.cols);const x=this._bufferService.buffer,E=x.ybase+x.y,R=Math.min(x.x,T-1),I=this._optionsService.rawOptions.cursorBlink,F=this._optionsService.rawOptions.cursorStyle,N=this._optionsService.rawOptions.cursorInactiveStyle;for(let z=D;z<=P;++z){const M=z+x.ydisp,L=this._rowElements[z],H=x.lines.get(M);if(!L||!H)break;L.replaceChildren(...this._rowFactory.createRow(H,M,M===E,F,N,R,I,this.dimensions.css.cell.width,this._widthCache,A?z===D?w:0:-1,A?(z===P?k:T)-1:-1))}}};s.DomRenderer=y=_([d(7,h.IInstantiationService),d(8,e.ICharSizeService),d(9,h.IOptionsService),d(10,h.IBufferService),d(11,e.ICoreBrowserService),d(12,e.IThemeService)],y)},3787:function(B,s,a){var _=this&&this.__decorate||function(l,g,u,p){var C,b=arguments.length,y=b<3?g:p===null?p=Object.getOwnPropertyDescriptor(g,u):p;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")y=Reflect.decorate(l,g,u,p);else for(var w=l.length-1;w>=0;w--)(C=l[w])&&(y=(b<3?C(y):b>3?C(g,u,y):C(g,u))||y);return b>3&&y&&Object.defineProperty(g,u,y),y},d=this&&this.__param||function(l,g){return function(u,p){g(u,p,l)}};Object.defineProperty(s,"__esModule",{value:!0}),s.DomRendererRowFactory=void 0;const n=a(2223),c=a(643),f=a(511),m=a(2585),o=a(8055),e=a(4725),r=a(4269),t=a(6171),i=a(3734);let h=s.DomRendererRowFactory=class{constructor(l,g,u,p,C,b,y){this._document=l,this._characterJoinerService=g,this._optionsService=u,this._coreBrowserService=p,this._coreService=C,this._decorationService=b,this._themeService=y,this._workCell=new f.CellData,this._columnSelectMode=!1,this.defaultSpacing=0}handleSelectionChanged(l,g,u){this._selectionStart=l,this._selectionEnd=g,this._columnSelectMode=u}createRow(l,g,u,p,C,b,y,w,k,D,P){const T=[],A=this._characterJoinerService.getJoinedCharacters(g),S=this._themeService.colors;let x,E=l.getNoBgTrimmedLength();u&&E<b+1&&(E=b+1);let R=0,I="",F=0,N=0,z=0,M=!1,L=0,H=!1,O=0;const $=[],W=D!==-1&&P!==-1;for(let K=0;K<E;K++){l.loadCell(K,this._workCell);let q=this._workCell.getWidth();if(q===0)continue;let ee=!1,re=K,j=this._workCell;if(A.length>0&&K===A[0][0]){ee=!0;const G=A.shift();j=new r.JoinedCellData(this._workCell,l.translateToString(!0,G[0],G[1]),G[1]-G[0]),re=G[1]-1,q=j.getWidth()}const le=this._isCellInSelection(K,g),ue=u&&K===b,Z=W&&K>=D&&K<=P;let V=!1;this._decorationService.forEachDecorationAtCell(K,g,void 0,G=>{V=!0});let X=j.getChars()||c.WHITESPACE_CELL_CHAR;if(X===" "&&(j.isUnderline()||j.isOverline())&&(X=" "),O=q*w-k.get(X,j.isBold(),j.isItalic()),x){if(R&&(le&&H||!le&&!H&&j.bg===F)&&(le&&H&&S.selectionForeground||j.fg===N)&&j.extended.ext===z&&Z===M&&O===L&&!ue&&!ee&&!V){j.isInvisible()?I+=c.WHITESPACE_CELL_CHAR:I+=X,R++;continue}R&&(x.textContent=I),x=this._document.createElement("span"),R=0,I=""}else x=this._document.createElement("span");if(F=j.bg,N=j.fg,z=j.extended.ext,M=Z,L=O,H=le,ee&&b>=K&&b<=re&&(b=K),!this._coreService.isCursorHidden&&ue&&this._coreService.isCursorInitialized){if($.push("xterm-cursor"),this._coreBrowserService.isFocused)y&&$.push("xterm-cursor-blink"),$.push(p==="bar"?"xterm-cursor-bar":p==="underline"?"xterm-cursor-underline":"xterm-cursor-block");else if(C)switch(C){case"outline":$.push("xterm-cursor-outline");break;case"block":$.push("xterm-cursor-block");break;case"bar":$.push("xterm-cursor-bar");break;case"underline":$.push("xterm-cursor-underline")}}if(j.isBold()&&$.push("xterm-bold"),j.isItalic()&&$.push("xterm-italic"),j.isDim()&&$.push("xterm-dim"),I=j.isInvisible()?c.WHITESPACE_CELL_CHAR:j.getChars()||c.WHITESPACE_CELL_CHAR,j.isUnderline()&&($.push(`xterm-underline-${j.extended.underlineStyle}`),I===" "&&(I=" "),!j.isUnderlineColorDefault()))if(j.isUnderlineColorRGB())x.style.textDecorationColor=`rgb(${i.AttributeData.toColorRGB(j.getUnderlineColor()).join(",")})`;else{let G=j.getUnderlineColor();this._optionsService.rawOptions.drawBoldTextInBrightColors&&j.isBold()&&G<8&&(G+=8),x.style.textDecorationColor=S.ansi[G].css}j.isOverline()&&($.push("xterm-overline"),I===" "&&(I=" ")),j.isStrikethrough()&&$.push("xterm-strikethrough"),Z&&(x.style.textDecoration="underline");let U=j.getFgColor(),J=j.getFgColorMode(),se=j.getBgColor(),ie=j.getBgColorMode();const _e=!!j.isInverse();if(_e){const G=U;U=se,se=G;const ge=J;J=ie,ie=ge}let he,ce,ne,te=!1;switch(this._decorationService.forEachDecorationAtCell(K,g,void 0,G=>{G.options.layer!=="top"&&te||(G.backgroundColorRGB&&(ie=50331648,se=G.backgroundColorRGB.rgba>>8&16777215,he=G.backgroundColorRGB),G.foregroundColorRGB&&(J=50331648,U=G.foregroundColorRGB.rgba>>8&16777215,ce=G.foregroundColorRGB),te=G.options.layer==="top")}),!te&&le&&(he=this._coreBrowserService.isFocused?S.selectionBackgroundOpaque:S.selectionInactiveBackgroundOpaque,se=he.rgba>>8&16777215,ie=50331648,te=!0,S.selectionForeground&&(J=50331648,U=S.selectionForeground.rgba>>8&16777215,ce=S.selectionForeground)),te&&$.push("xterm-decoration-top"),ie){case 16777216:case 33554432:ne=S.ansi[se],$.push(`xterm-bg-${se}`);break;case 50331648:ne=o.channels.toColor(se>>16,se>>8&255,255&se),this._addStyle(x,`background-color:#${v((se>>>0).toString(16),"0",6)}`);break;default:_e?(ne=S.foreground,$.push(`xterm-bg-${n.INVERTED_DEFAULT_COLOR}`)):ne=S.background}switch(he||j.isDim()&&(he=o.color.multiplyOpacity(ne,.5)),J){case 16777216:case 33554432:j.isBold()&&U<8&&this._optionsService.rawOptions.drawBoldTextInBrightColors&&(U+=8),this._applyMinimumContrast(x,ne,S.ansi[U],j,he,void 0)||$.push(`xterm-fg-${U}`);break;case 50331648:const G=o.channels.toColor(U>>16&255,U>>8&255,255&U);this._applyMinimumContrast(x,ne,G,j,he,ce)||this._addStyle(x,`color:#${v(U.toString(16),"0",6)}`);break;default:this._applyMinimumContrast(x,ne,S.foreground,j,he,ce)||_e&&$.push(`xterm-fg-${n.INVERTED_DEFAULT_COLOR}`)}$.length&&(x.className=$.join(" "),$.length=0),ue||ee||V?x.textContent=I:R++,O!==this.defaultSpacing&&(x.style.letterSpacing=`${O}px`),T.push(x),K=re}return x&&R&&(x.textContent=I),T}_applyMinimumContrast(l,g,u,p,C,b){if(this._optionsService.rawOptions.minimumContrastRatio===1||(0,t.treatGlyphAsBackgroundColor)(p.getCode()))return!1;const y=this._getContrastCache(p);let w;if(C||b||(w=y.getColor(g.rgba,u.rgba)),w===void 0){const k=this._optionsService.rawOptions.minimumContrastRatio/(p.isDim()?2:1);w=o.color.ensureContrastRatio(C||g,b||u,k),y.setColor((C||g).rgba,(b||u).rgba,w??null)}return!!w&&(this._addStyle(l,`color:${w.css}`),!0)}_getContrastCache(l){return l.isDim()?this._themeService.colors.halfContrastCache:this._themeService.colors.contrastCache}_addStyle(l,g){l.setAttribute("style",`${l.getAttribute("style")||""}${g};`)}_isCellInSelection(l,g){const u=this._selectionStart,p=this._selectionEnd;return!(!u||!p)&&(this._columnSelectMode?u[0]<=p[0]?l>=u[0]&&g>=u[1]&&l<p[0]&&g<=p[1]:l<u[0]&&g>=u[1]&&l>=p[0]&&g<=p[1]:g>u[1]&&g<p[1]||u[1]===p[1]&&g===u[1]&&l>=u[0]&&l<p[0]||u[1]<p[1]&&g===p[1]&&l<p[0]||u[1]<p[1]&&g===u[1]&&l>=u[0])}};function v(l,g,u){for(;l.length<u;)l=g+l;return l}s.DomRendererRowFactory=h=_([d(1,e.ICharacterJoinerService),d(2,m.IOptionsService),d(3,e.ICoreBrowserService),d(4,m.ICoreService),d(5,m.IDecorationService),d(6,e.IThemeService)],h)},2550:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.WidthCache=void 0,s.WidthCache=class{constructor(a,_){this._flat=new Float32Array(256),this._font="",this._fontSize=0,this._weight="normal",this._weightBold="bold",this._measureElements=[],this._container=a.createElement("div"),this._container.classList.add("xterm-width-cache-measure-container"),this._container.setAttribute("aria-hidden","true"),this._container.style.whiteSpace="pre",this._container.style.fontKerning="none";const d=a.createElement("span");d.classList.add("xterm-char-measure-element");const n=a.createElement("span");n.classList.add("xterm-char-measure-element"),n.style.fontWeight="bold";const c=a.createElement("span");c.classList.add("xterm-char-measure-element"),c.style.fontStyle="italic";const f=a.createElement("span");f.classList.add("xterm-char-measure-element"),f.style.fontWeight="bold",f.style.fontStyle="italic",this._measureElements=[d,n,c,f],this._container.appendChild(d),this._container.appendChild(n),this._container.appendChild(c),this._container.appendChild(f),_.appendChild(this._container),this.clear()}dispose(){this._container.remove(),this._measureElements.length=0,this._holey=void 0}clear(){this._flat.fill(-9999),this._holey=new Map}setFont(a,_,d,n){a===this._font&&_===this._fontSize&&d===this._weight&&n===this._weightBold||(this._font=a,this._fontSize=_,this._weight=d,this._weightBold=n,this._container.style.fontFamily=this._font,this._container.style.fontSize=`${this._fontSize}px`,this._measureElements[0].style.fontWeight=`${d}`,this._measureElements[1].style.fontWeight=`${n}`,this._measureElements[2].style.fontWeight=`${d}`,this._measureElements[3].style.fontWeight=`${n}`,this.clear())}get(a,_,d){let n=0;if(!_&&!d&&a.length===1&&(n=a.charCodeAt(0))<256){if(this._flat[n]!==-9999)return this._flat[n];const m=this._measure(a,0);return m>0&&(this._flat[n]=m),m}let c=a;_&&(c+="B"),d&&(c+="I");let f=this._holey.get(c);if(f===void 0){let m=0;_&&(m|=1),d&&(m|=2),f=this._measure(a,m),f>0&&this._holey.set(c,f)}return f}_measure(a,_){const d=this._measureElements[_];return d.textContent=a.repeat(32),d.offsetWidth/32}}},2223:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.TEXT_BASELINE=s.DIM_OPACITY=s.INVERTED_DEFAULT_COLOR=void 0;const _=a(6114);s.INVERTED_DEFAULT_COLOR=257,s.DIM_OPACITY=.5,s.TEXT_BASELINE=_.isFirefox||_.isLegacyEdge?"bottom":"ideographic"},6171:(B,s)=>{function a(d){return 57508<=d&&d<=57558}function _(d){return d>=128512&&d<=128591||d>=127744&&d<=128511||d>=128640&&d<=128767||d>=9728&&d<=9983||d>=9984&&d<=10175||d>=65024&&d<=65039||d>=129280&&d<=129535||d>=127462&&d<=127487}Object.defineProperty(s,"__esModule",{value:!0}),s.computeNextVariantOffset=s.createRenderDimensions=s.treatGlyphAsBackgroundColor=s.allowRescaling=s.isEmoji=s.isRestrictedPowerlineGlyph=s.isPowerlineGlyph=s.throwIfFalsy=void 0,s.throwIfFalsy=function(d){if(!d)throw new Error("value must not be falsy");return d},s.isPowerlineGlyph=a,s.isRestrictedPowerlineGlyph=function(d){return 57520<=d&&d<=57527},s.isEmoji=_,s.allowRescaling=function(d,n,c,f){return n===1&&c>Math.ceil(1.5*f)&&d!==void 0&&d>255&&!_(d)&&!a(d)&&!function(m){return 57344<=m&&m<=63743}(d)},s.treatGlyphAsBackgroundColor=function(d){return a(d)||function(n){return 9472<=n&&n<=9631}(d)},s.createRenderDimensions=function(){return{css:{canvas:{width:0,height:0},cell:{width:0,height:0}},device:{canvas:{width:0,height:0},cell:{width:0,height:0},char:{width:0,height:0,left:0,top:0}}}},s.computeNextVariantOffset=function(d,n,c=0){return(d-(2*Math.round(n)-c))%(2*Math.round(n))}},6052:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.createSelectionRenderModel=void 0;class a{constructor(){this.clear()}clear(){this.hasSelection=!1,this.columnSelectMode=!1,this.viewportStartRow=0,this.viewportEndRow=0,this.viewportCappedStartRow=0,this.viewportCappedEndRow=0,this.startCol=0,this.endCol=0,this.selectionStart=void 0,this.selectionEnd=void 0}update(d,n,c,f=!1){if(this.selectionStart=n,this.selectionEnd=c,!n||!c||n[0]===c[0]&&n[1]===c[1])return void this.clear();const m=d.buffers.active.ydisp,o=n[1]-m,e=c[1]-m,r=Math.max(o,0),t=Math.min(e,d.rows-1);r>=d.rows||t<0?this.clear():(this.hasSelection=!0,this.columnSelectMode=f,this.viewportStartRow=o,this.viewportEndRow=e,this.viewportCappedStartRow=r,this.viewportCappedEndRow=t,this.startCol=n[0],this.endCol=c[0])}isCellSelected(d,n,c){return!!this.hasSelection&&(c-=d.buffer.active.viewportY,this.columnSelectMode?this.startCol<=this.endCol?n>=this.startCol&&c>=this.viewportCappedStartRow&&n<this.endCol&&c<=this.viewportCappedEndRow:n<this.startCol&&c>=this.viewportCappedStartRow&&n>=this.endCol&&c<=this.viewportCappedEndRow:c>this.viewportStartRow&&c<this.viewportEndRow||this.viewportStartRow===this.viewportEndRow&&c===this.viewportStartRow&&n>=this.startCol&&n<this.endCol||this.viewportStartRow<this.viewportEndRow&&c===this.viewportEndRow&&n<this.endCol||this.viewportStartRow<this.viewportEndRow&&c===this.viewportStartRow&&n>=this.startCol)}}s.createSelectionRenderModel=function(){return new a}},456:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.SelectionModel=void 0,s.SelectionModel=class{constructor(a){this._bufferService=a,this.isSelectAllActive=!1,this.selectionStartLength=0}clearSelection(){this.selectionStart=void 0,this.selectionEnd=void 0,this.isSelectAllActive=!1,this.selectionStartLength=0}get finalSelectionStart(){return this.isSelectAllActive?[0,0]:this.selectionEnd&&this.selectionStart&&this.areSelectionValuesReversed()?this.selectionEnd:this.selectionStart}get finalSelectionEnd(){if(this.isSelectAllActive)return[this._bufferService.cols,this._bufferService.buffer.ybase+this._bufferService.rows-1];if(this.selectionStart){if(!this.selectionEnd||this.areSelectionValuesReversed()){const a=this.selectionStart[0]+this.selectionStartLength;return a>this._bufferService.cols?a%this._bufferService.cols==0?[this._bufferService.cols,this.selectionStart[1]+Math.floor(a/this._bufferService.cols)-1]:[a%this._bufferService.cols,this.selectionStart[1]+Math.floor(a/this._bufferService.cols)]:[a,this.selectionStart[1]]}if(this.selectionStartLength&&this.selectionEnd[1]===this.selectionStart[1]){const a=this.selectionStart[0]+this.selectionStartLength;return a>this._bufferService.cols?[a%this._bufferService.cols,this.selectionStart[1]+Math.floor(a/this._bufferService.cols)]:[Math.max(a,this.selectionEnd[0]),this.selectionEnd[1]]}return this.selectionEnd}}areSelectionValuesReversed(){const a=this.selectionStart,_=this.selectionEnd;return!(!a||!_)&&(a[1]>_[1]||a[1]===_[1]&&a[0]>_[0])}handleTrim(a){return this.selectionStart&&(this.selectionStart[1]-=a),this.selectionEnd&&(this.selectionEnd[1]-=a),this.selectionEnd&&this.selectionEnd[1]<0?(this.clearSelection(),!0):(this.selectionStart&&this.selectionStart[1]<0&&(this.selectionStart[1]=0),!1)}}},428:function(B,s,a){var _=this&&this.__decorate||function(t,i,h,v){var l,g=arguments.length,u=g<3?i:v===null?v=Object.getOwnPropertyDescriptor(i,h):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")u=Reflect.decorate(t,i,h,v);else for(var p=t.length-1;p>=0;p--)(l=t[p])&&(u=(g<3?l(u):g>3?l(i,h,u):l(i,h))||u);return g>3&&u&&Object.defineProperty(i,h,u),u},d=this&&this.__param||function(t,i){return function(h,v){i(h,v,t)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CharSizeService=void 0;const n=a(2585),c=a(8460),f=a(844);let m=s.CharSizeService=class extends f.Disposable{get hasValidSize(){return this.width>0&&this.height>0}constructor(t,i,h){super(),this._optionsService=h,this.width=0,this.height=0,this._onCharSizeChange=this.register(new c.EventEmitter),this.onCharSizeChange=this._onCharSizeChange.event;try{this._measureStrategy=this.register(new r(this._optionsService))}catch{this._measureStrategy=this.register(new e(t,i,this._optionsService))}this.register(this._optionsService.onMultipleOptionChange(["fontFamily","fontSize"],()=>this.measure()))}measure(){const t=this._measureStrategy.measure();t.width===this.width&&t.height===this.height||(this.width=t.width,this.height=t.height,this._onCharSizeChange.fire())}};s.CharSizeService=m=_([d(2,n.IOptionsService)],m);class o extends f.Disposable{constructor(){super(...arguments),this._result={width:0,height:0}}_validateAndSet(i,h){i!==void 0&&i>0&&h!==void 0&&h>0&&(this._result.width=i,this._result.height=h)}}class e extends o{constructor(i,h,v){super(),this._document=i,this._parentElement=h,this._optionsService=v,this._measureElement=this._document.createElement("span"),this._measureElement.classList.add("xterm-char-measure-element"),this._measureElement.textContent="W".repeat(32),this._measureElement.setAttribute("aria-hidden","true"),this._measureElement.style.whiteSpace="pre",this._measureElement.style.fontKerning="none",this._parentElement.appendChild(this._measureElement)}measure(){return this._measureElement.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._measureElement.style.fontSize=`${this._optionsService.rawOptions.fontSize}px`,this._validateAndSet(Number(this._measureElement.offsetWidth)/32,Number(this._measureElement.offsetHeight)),this._result}}class r extends o{constructor(i){super(),this._optionsService=i,this._canvas=new OffscreenCanvas(100,100),this._ctx=this._canvas.getContext("2d");const h=this._ctx.measureText("W");if(!("width"in h&&"fontBoundingBoxAscent"in h&&"fontBoundingBoxDescent"in h))throw new Error("Required font metrics not supported")}measure(){this._ctx.font=`${this._optionsService.rawOptions.fontSize}px ${this._optionsService.rawOptions.fontFamily}`;const i=this._ctx.measureText("W");return this._validateAndSet(i.width,i.fontBoundingBoxAscent+i.fontBoundingBoxDescent),this._result}}},4269:function(B,s,a){var _=this&&this.__decorate||function(r,t,i,h){var v,l=arguments.length,g=l<3?t:h===null?h=Object.getOwnPropertyDescriptor(t,i):h;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")g=Reflect.decorate(r,t,i,h);else for(var u=r.length-1;u>=0;u--)(v=r[u])&&(g=(l<3?v(g):l>3?v(t,i,g):v(t,i))||g);return l>3&&g&&Object.defineProperty(t,i,g),g},d=this&&this.__param||function(r,t){return function(i,h){t(i,h,r)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CharacterJoinerService=s.JoinedCellData=void 0;const n=a(3734),c=a(643),f=a(511),m=a(2585);class o extends n.AttributeData{constructor(t,i,h){super(),this.content=0,this.combinedData="",this.fg=t.fg,this.bg=t.bg,this.combinedData=i,this._width=h}isCombined(){return 2097152}getWidth(){return this._width}getChars(){return this.combinedData}getCode(){return 2097151}setFromCharData(t){throw new Error("not implemented")}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}s.JoinedCellData=o;let e=s.CharacterJoinerService=class Oe{constructor(t){this._bufferService=t,this._characterJoiners=[],this._nextCharacterJoinerId=0,this._workCell=new f.CellData}register(t){const i={id:this._nextCharacterJoinerId++,handler:t};return this._characterJoiners.push(i),i.id}deregister(t){for(let i=0;i<this._characterJoiners.length;i++)if(this._characterJoiners[i].id===t)return this._characterJoiners.splice(i,1),!0;return!1}getJoinedCharacters(t){if(this._characterJoiners.length===0)return[];const i=this._bufferService.buffer.lines.get(t);if(!i||i.length===0)return[];const h=[],v=i.translateToString(!0);let l=0,g=0,u=0,p=i.getFg(0),C=i.getBg(0);for(let b=0;b<i.getTrimmedLength();b++)if(i.loadCell(b,this._workCell),this._workCell.getWidth()!==0){if(this._workCell.fg!==p||this._workCell.bg!==C){if(b-l>1){const y=this._getJoinedRanges(v,u,g,i,l);for(let w=0;w<y.length;w++)h.push(y[w])}l=b,u=g,p=this._workCell.fg,C=this._workCell.bg}g+=this._workCell.getChars().length||c.WHITESPACE_CELL_CHAR.length}if(this._bufferService.cols-l>1){const b=this._getJoinedRanges(v,u,g,i,l);for(let y=0;y<b.length;y++)h.push(b[y])}return h}_getJoinedRanges(t,i,h,v,l){const g=t.substring(i,h);let u=[];try{u=this._characterJoiners[0].handler(g)}catch(p){console.error(p)}for(let p=1;p<this._characterJoiners.length;p++)try{const C=this._characterJoiners[p].handler(g);for(let b=0;b<C.length;b++)Oe._mergeRanges(u,C[b])}catch(C){console.error(C)}return this._stringRangesToCellRanges(u,v,l),u}_stringRangesToCellRanges(t,i,h){let v=0,l=!1,g=0,u=t[v];if(u){for(let p=h;p<this._bufferService.cols;p++){const C=i.getWidth(p),b=i.getString(p).length||c.WHITESPACE_CELL_CHAR.length;if(C!==0){if(!l&&u[0]<=g&&(u[0]=p,l=!0),u[1]<=g){if(u[1]=p,u=t[++v],!u)break;u[0]<=g?(u[0]=p,l=!0):l=!1}g+=b}}u&&(u[1]=this._bufferService.cols)}}static _mergeRanges(t,i){let h=!1;for(let v=0;v<t.length;v++){const l=t[v];if(h){if(i[1]<=l[0])return t[v-1][1]=i[1],t;if(i[1]<=l[1])return t[v-1][1]=Math.max(i[1],l[1]),t.splice(v,1),t;t.splice(v,1),v--}else{if(i[1]<=l[0])return t.splice(v,0,i),t;if(i[1]<=l[1])return l[0]=Math.min(i[0],l[0]),t;i[0]<l[1]&&(l[0]=Math.min(i[0],l[0]),h=!0)}}return h?t[t.length-1][1]=i[1]:t.push(i),t}};s.CharacterJoinerService=e=_([d(0,m.IBufferService)],e)},5114:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CoreBrowserService=void 0;const _=a(844),d=a(8460),n=a(3656);class c extends _.Disposable{constructor(o,e,r){super(),this._textarea=o,this._window=e,this.mainDocument=r,this._isFocused=!1,this._cachedIsFocused=void 0,this._screenDprMonitor=new f(this._window),this._onDprChange=this.register(new d.EventEmitter),this.onDprChange=this._onDprChange.event,this._onWindowChange=this.register(new d.EventEmitter),this.onWindowChange=this._onWindowChange.event,this.register(this.onWindowChange(t=>this._screenDprMonitor.setWindow(t))),this.register((0,d.forwardEvent)(this._screenDprMonitor.onDprChange,this._onDprChange)),this._textarea.addEventListener("focus",()=>this._isFocused=!0),this._textarea.addEventListener("blur",()=>this._isFocused=!1)}get window(){return this._window}set window(o){this._window!==o&&(this._window=o,this._onWindowChange.fire(this._window))}get dpr(){return this.window.devicePixelRatio}get isFocused(){return this._cachedIsFocused===void 0&&(this._cachedIsFocused=this._isFocused&&this._textarea.ownerDocument.hasFocus(),queueMicrotask(()=>this._cachedIsFocused=void 0)),this._cachedIsFocused}}s.CoreBrowserService=c;class f extends _.Disposable{constructor(o){super(),this._parentWindow=o,this._windowResizeListener=this.register(new _.MutableDisposable),this._onDprChange=this.register(new d.EventEmitter),this.onDprChange=this._onDprChange.event,this._outerListener=()=>this._setDprAndFireIfDiffers(),this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this._updateDpr(),this._setWindowResizeListener(),this.register((0,_.toDisposable)(()=>this.clearListener()))}setWindow(o){this._parentWindow=o,this._setWindowResizeListener(),this._setDprAndFireIfDiffers()}_setWindowResizeListener(){this._windowResizeListener.value=(0,n.addDisposableDomListener)(this._parentWindow,"resize",()=>this._setDprAndFireIfDiffers())}_setDprAndFireIfDiffers(){this._parentWindow.devicePixelRatio!==this._currentDevicePixelRatio&&this._onDprChange.fire(this._parentWindow.devicePixelRatio),this._updateDpr()}_updateDpr(){var o;this._outerListener&&((o=this._resolutionMediaMatchList)==null||o.removeListener(this._outerListener),this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this._resolutionMediaMatchList=this._parentWindow.matchMedia(`screen and (resolution: ${this._parentWindow.devicePixelRatio}dppx)`),this._resolutionMediaMatchList.addListener(this._outerListener))}clearListener(){this._resolutionMediaMatchList&&this._outerListener&&(this._resolutionMediaMatchList.removeListener(this._outerListener),this._resolutionMediaMatchList=void 0,this._outerListener=void 0)}}},779:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.LinkProviderService=void 0;const _=a(844);class d extends _.Disposable{constructor(){super(),this.linkProviders=[],this.register((0,_.toDisposable)(()=>this.linkProviders.length=0))}registerLinkProvider(c){return this.linkProviders.push(c),{dispose:()=>{const f=this.linkProviders.indexOf(c);f!==-1&&this.linkProviders.splice(f,1)}}}}s.LinkProviderService=d},8934:function(B,s,a){var _=this&&this.__decorate||function(m,o,e,r){var t,i=arguments.length,h=i<3?o:r===null?r=Object.getOwnPropertyDescriptor(o,e):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")h=Reflect.decorate(m,o,e,r);else for(var v=m.length-1;v>=0;v--)(t=m[v])&&(h=(i<3?t(h):i>3?t(o,e,h):t(o,e))||h);return i>3&&h&&Object.defineProperty(o,e,h),h},d=this&&this.__param||function(m,o){return function(e,r){o(e,r,m)}};Object.defineProperty(s,"__esModule",{value:!0}),s.MouseService=void 0;const n=a(4725),c=a(9806);let f=s.MouseService=class{constructor(m,o){this._renderService=m,this._charSizeService=o}getCoords(m,o,e,r,t){return(0,c.getCoords)(window,m,o,e,r,this._charSizeService.hasValidSize,this._renderService.dimensions.css.cell.width,this._renderService.dimensions.css.cell.height,t)}getMouseReportCoords(m,o){const e=(0,c.getCoordsRelativeToElement)(window,m,o);if(this._charSizeService.hasValidSize)return e[0]=Math.min(Math.max(e[0],0),this._renderService.dimensions.css.canvas.width-1),e[1]=Math.min(Math.max(e[1],0),this._renderService.dimensions.css.canvas.height-1),{col:Math.floor(e[0]/this._renderService.dimensions.css.cell.width),row:Math.floor(e[1]/this._renderService.dimensions.css.cell.height),x:Math.floor(e[0]),y:Math.floor(e[1])}}};s.MouseService=f=_([d(0,n.IRenderService),d(1,n.ICharSizeService)],f)},3230:function(B,s,a){var _=this&&this.__decorate||function(t,i,h,v){var l,g=arguments.length,u=g<3?i:v===null?v=Object.getOwnPropertyDescriptor(i,h):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")u=Reflect.decorate(t,i,h,v);else for(var p=t.length-1;p>=0;p--)(l=t[p])&&(u=(g<3?l(u):g>3?l(i,h,u):l(i,h))||u);return g>3&&u&&Object.defineProperty(i,h,u),u},d=this&&this.__param||function(t,i){return function(h,v){i(h,v,t)}};Object.defineProperty(s,"__esModule",{value:!0}),s.RenderService=void 0;const n=a(6193),c=a(4725),f=a(8460),m=a(844),o=a(7226),e=a(2585);let r=s.RenderService=class extends m.Disposable{get dimensions(){return this._renderer.value.dimensions}constructor(t,i,h,v,l,g,u,p){super(),this._rowCount=t,this._charSizeService=v,this._renderer=this.register(new m.MutableDisposable),this._pausedResizeTask=new o.DebouncedIdleTask,this._observerDisposable=this.register(new m.MutableDisposable),this._isPaused=!1,this._needsFullRefresh=!1,this._isNextRenderRedrawOnly=!0,this._needsSelectionRefresh=!1,this._canvasWidth=0,this._canvasHeight=0,this._selectionState={start:void 0,end:void 0,columnSelectMode:!1},this._onDimensionsChange=this.register(new f.EventEmitter),this.onDimensionsChange=this._onDimensionsChange.event,this._onRenderedViewportChange=this.register(new f.EventEmitter),this.onRenderedViewportChange=this._onRenderedViewportChange.event,this._onRender=this.register(new f.EventEmitter),this.onRender=this._onRender.event,this._onRefreshRequest=this.register(new f.EventEmitter),this.onRefreshRequest=this._onRefreshRequest.event,this._renderDebouncer=new n.RenderDebouncer((C,b)=>this._renderRows(C,b),u),this.register(this._renderDebouncer),this.register(u.onDprChange(()=>this.handleDevicePixelRatioChange())),this.register(g.onResize(()=>this._fullRefresh())),this.register(g.buffers.onBufferActivate(()=>{var C;return(C=this._renderer.value)==null?void 0:C.clear()})),this.register(h.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._charSizeService.onCharSizeChange(()=>this.handleCharSizeChanged())),this.register(l.onDecorationRegistered(()=>this._fullRefresh())),this.register(l.onDecorationRemoved(()=>this._fullRefresh())),this.register(h.onMultipleOptionChange(["customGlyphs","drawBoldTextInBrightColors","letterSpacing","lineHeight","fontFamily","fontSize","fontWeight","fontWeightBold","minimumContrastRatio","rescaleOverlappingGlyphs"],()=>{this.clear(),this.handleResize(g.cols,g.rows),this._fullRefresh()})),this.register(h.onMultipleOptionChange(["cursorBlink","cursorStyle"],()=>this.refreshRows(g.buffer.y,g.buffer.y,!0))),this.register(p.onChangeColors(()=>this._fullRefresh())),this._registerIntersectionObserver(u.window,i),this.register(u.onWindowChange(C=>this._registerIntersectionObserver(C,i)))}_registerIntersectionObserver(t,i){if("IntersectionObserver"in t){const h=new t.IntersectionObserver(v=>this._handleIntersectionChange(v[v.length-1]),{threshold:0});h.observe(i),this._observerDisposable.value=(0,m.toDisposable)(()=>h.disconnect())}}_handleIntersectionChange(t){this._isPaused=t.isIntersecting===void 0?t.intersectionRatio===0:!t.isIntersecting,this._isPaused||this._charSizeService.hasValidSize||this._charSizeService.measure(),!this._isPaused&&this._needsFullRefresh&&(this._pausedResizeTask.flush(),this.refreshRows(0,this._rowCount-1),this._needsFullRefresh=!1)}refreshRows(t,i,h=!1){this._isPaused?this._needsFullRefresh=!0:(h||(this._isNextRenderRedrawOnly=!1),this._renderDebouncer.refresh(t,i,this._rowCount))}_renderRows(t,i){this._renderer.value&&(t=Math.min(t,this._rowCount-1),i=Math.min(i,this._rowCount-1),this._renderer.value.renderRows(t,i),this._needsSelectionRefresh&&(this._renderer.value.handleSelectionChanged(this._selectionState.start,this._selectionState.end,this._selectionState.columnSelectMode),this._needsSelectionRefresh=!1),this._isNextRenderRedrawOnly||this._onRenderedViewportChange.fire({start:t,end:i}),this._onRender.fire({start:t,end:i}),this._isNextRenderRedrawOnly=!0)}resize(t,i){this._rowCount=i,this._fireOnCanvasResize()}_handleOptionsChanged(){this._renderer.value&&(this.refreshRows(0,this._rowCount-1),this._fireOnCanvasResize())}_fireOnCanvasResize(){this._renderer.value&&(this._renderer.value.dimensions.css.canvas.width===this._canvasWidth&&this._renderer.value.dimensions.css.canvas.height===this._canvasHeight||this._onDimensionsChange.fire(this._renderer.value.dimensions))}hasRenderer(){return!!this._renderer.value}setRenderer(t){this._renderer.value=t,this._renderer.value&&(this._renderer.value.onRequestRedraw(i=>this.refreshRows(i.start,i.end,!0)),this._needsSelectionRefresh=!0,this._fullRefresh())}addRefreshCallback(t){return this._renderDebouncer.addRefreshCallback(t)}_fullRefresh(){this._isPaused?this._needsFullRefresh=!0:this.refreshRows(0,this._rowCount-1)}clearTextureAtlas(){var t,i;this._renderer.value&&((i=(t=this._renderer.value).clearTextureAtlas)==null||i.call(t),this._fullRefresh())}handleDevicePixelRatioChange(){this._charSizeService.measure(),this._renderer.value&&(this._renderer.value.handleDevicePixelRatioChange(),this.refreshRows(0,this._rowCount-1))}handleResize(t,i){this._renderer.value&&(this._isPaused?this._pausedResizeTask.set(()=>{var h;return(h=this._renderer.value)==null?void 0:h.handleResize(t,i)}):this._renderer.value.handleResize(t,i),this._fullRefresh())}handleCharSizeChanged(){var t;(t=this._renderer.value)==null||t.handleCharSizeChanged()}handleBlur(){var t;(t=this._renderer.value)==null||t.handleBlur()}handleFocus(){var t;(t=this._renderer.value)==null||t.handleFocus()}handleSelectionChanged(t,i,h){var v;this._selectionState.start=t,this._selectionState.end=i,this._selectionState.columnSelectMode=h,(v=this._renderer.value)==null||v.handleSelectionChanged(t,i,h)}handleCursorMove(){var t;(t=this._renderer.value)==null||t.handleCursorMove()}clear(){var t;(t=this._renderer.value)==null||t.clear()}};s.RenderService=r=_([d(2,e.IOptionsService),d(3,c.ICharSizeService),d(4,e.IDecorationService),d(5,e.IBufferService),d(6,c.ICoreBrowserService),d(7,c.IThemeService)],r)},9312:function(B,s,a){var _=this&&this.__decorate||function(u,p,C,b){var y,w=arguments.length,k=w<3?p:b===null?b=Object.getOwnPropertyDescriptor(p,C):b;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")k=Reflect.decorate(u,p,C,b);else for(var D=u.length-1;D>=0;D--)(y=u[D])&&(k=(w<3?y(k):w>3?y(p,C,k):y(p,C))||k);return w>3&&k&&Object.defineProperty(p,C,k),k},d=this&&this.__param||function(u,p){return function(C,b){p(C,b,u)}};Object.defineProperty(s,"__esModule",{value:!0}),s.SelectionService=void 0;const n=a(9806),c=a(9504),f=a(456),m=a(4725),o=a(8460),e=a(844),r=a(6114),t=a(4841),i=a(511),h=a(2585),v=" ",l=new RegExp(v,"g");let g=s.SelectionService=class extends e.Disposable{constructor(u,p,C,b,y,w,k,D,P){super(),this._element=u,this._screenElement=p,this._linkifier=C,this._bufferService=b,this._coreService=y,this._mouseService=w,this._optionsService=k,this._renderService=D,this._coreBrowserService=P,this._dragScrollAmount=0,this._enabled=!0,this._workCell=new i.CellData,this._mouseDownTimeStamp=0,this._oldHasSelection=!1,this._oldSelectionStart=void 0,this._oldSelectionEnd=void 0,this._onLinuxMouseSelection=this.register(new o.EventEmitter),this.onLinuxMouseSelection=this._onLinuxMouseSelection.event,this._onRedrawRequest=this.register(new o.EventEmitter),this.onRequestRedraw=this._onRedrawRequest.event,this._onSelectionChange=this.register(new o.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onRequestScrollLines=this.register(new o.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this._mouseMoveListener=T=>this._handleMouseMove(T),this._mouseUpListener=T=>this._handleMouseUp(T),this._coreService.onUserInput(()=>{this.hasSelection&&this.clearSelection()}),this._trimListener=this._bufferService.buffer.lines.onTrim(T=>this._handleTrim(T)),this.register(this._bufferService.buffers.onBufferActivate(T=>this._handleBufferActivate(T))),this.enable(),this._model=new f.SelectionModel(this._bufferService),this._activeSelectionMode=0,this.register((0,e.toDisposable)(()=>{this._removeMouseDownListeners()}))}reset(){this.clearSelection()}disable(){this.clearSelection(),this._enabled=!1}enable(){this._enabled=!0}get selectionStart(){return this._model.finalSelectionStart}get selectionEnd(){return this._model.finalSelectionEnd}get hasSelection(){const u=this._model.finalSelectionStart,p=this._model.finalSelectionEnd;return!(!u||!p||u[0]===p[0]&&u[1]===p[1])}get selectionText(){const u=this._model.finalSelectionStart,p=this._model.finalSelectionEnd;if(!u||!p)return"";const C=this._bufferService.buffer,b=[];if(this._activeSelectionMode===3){if(u[0]===p[0])return"";const y=u[0]<p[0]?u[0]:p[0],w=u[0]<p[0]?p[0]:u[0];for(let k=u[1];k<=p[1];k++){const D=C.translateBufferLineToString(k,!0,y,w);b.push(D)}}else{const y=u[1]===p[1]?p[0]:void 0;b.push(C.translateBufferLineToString(u[1],!0,u[0],y));for(let w=u[1]+1;w<=p[1]-1;w++){const k=C.lines.get(w),D=C.translateBufferLineToString(w,!0);k!=null&&k.isWrapped?b[b.length-1]+=D:b.push(D)}if(u[1]!==p[1]){const w=C.lines.get(p[1]),k=C.translateBufferLineToString(p[1],!0,0,p[0]);w&&w.isWrapped?b[b.length-1]+=k:b.push(k)}}return b.map(y=>y.replace(l," ")).join(r.isWindows?`\r
`:`
`)}clearSelection(){this._model.clearSelection(),this._removeMouseDownListeners(),this.refresh(),this._onSelectionChange.fire()}refresh(u){this._refreshAnimationFrame||(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._refresh())),r.isLinux&&u&&this.selectionText.length&&this._onLinuxMouseSelection.fire(this.selectionText)}_refresh(){this._refreshAnimationFrame=void 0,this._onRedrawRequest.fire({start:this._model.finalSelectionStart,end:this._model.finalSelectionEnd,columnSelectMode:this._activeSelectionMode===3})}_isClickInSelection(u){const p=this._getMouseBufferCoords(u),C=this._model.finalSelectionStart,b=this._model.finalSelectionEnd;return!!(C&&b&&p)&&this._areCoordsInSelection(p,C,b)}isCellInSelection(u,p){const C=this._model.finalSelectionStart,b=this._model.finalSelectionEnd;return!(!C||!b)&&this._areCoordsInSelection([u,p],C,b)}_areCoordsInSelection(u,p,C){return u[1]>p[1]&&u[1]<C[1]||p[1]===C[1]&&u[1]===p[1]&&u[0]>=p[0]&&u[0]<C[0]||p[1]<C[1]&&u[1]===C[1]&&u[0]<C[0]||p[1]<C[1]&&u[1]===p[1]&&u[0]>=p[0]}_selectWordAtCursor(u,p){var y,w;const C=(w=(y=this._linkifier.currentLink)==null?void 0:y.link)==null?void 0:w.range;if(C)return this._model.selectionStart=[C.start.x-1,C.start.y-1],this._model.selectionStartLength=(0,t.getRangeLength)(C,this._bufferService.cols),this._model.selectionEnd=void 0,!0;const b=this._getMouseBufferCoords(u);return!!b&&(this._selectWordAt(b,p),this._model.selectionEnd=void 0,!0)}selectAll(){this._model.isSelectAllActive=!0,this.refresh(),this._onSelectionChange.fire()}selectLines(u,p){this._model.clearSelection(),u=Math.max(u,0),p=Math.min(p,this._bufferService.buffer.lines.length-1),this._model.selectionStart=[0,u],this._model.selectionEnd=[this._bufferService.cols,p],this.refresh(),this._onSelectionChange.fire()}_handleTrim(u){this._model.handleTrim(u)&&this.refresh()}_getMouseBufferCoords(u){const p=this._mouseService.getCoords(u,this._screenElement,this._bufferService.cols,this._bufferService.rows,!0);if(p)return p[0]--,p[1]--,p[1]+=this._bufferService.buffer.ydisp,p}_getMouseEventScrollAmount(u){let p=(0,n.getCoordsRelativeToElement)(this._coreBrowserService.window,u,this._screenElement)[1];const C=this._renderService.dimensions.css.canvas.height;return p>=0&&p<=C?0:(p>C&&(p-=C),p=Math.min(Math.max(p,-50),50),p/=50,p/Math.abs(p)+Math.round(14*p))}shouldForceSelection(u){return r.isMac?u.altKey&&this._optionsService.rawOptions.macOptionClickForcesSelection:u.shiftKey}handleMouseDown(u){if(this._mouseDownTimeStamp=u.timeStamp,(u.button!==2||!this.hasSelection)&&u.button===0){if(!this._enabled){if(!this.shouldForceSelection(u))return;u.stopPropagation()}u.preventDefault(),this._dragScrollAmount=0,this._enabled&&u.shiftKey?this._handleIncrementalClick(u):u.detail===1?this._handleSingleClick(u):u.detail===2?this._handleDoubleClick(u):u.detail===3&&this._handleTripleClick(u),this._addMouseDownListeners(),this.refresh(!0)}}_addMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.addEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.addEventListener("mouseup",this._mouseUpListener)),this._dragScrollIntervalTimer=this._coreBrowserService.window.setInterval(()=>this._dragScroll(),50)}_removeMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.removeEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.removeEventListener("mouseup",this._mouseUpListener)),this._coreBrowserService.window.clearInterval(this._dragScrollIntervalTimer),this._dragScrollIntervalTimer=void 0}_handleIncrementalClick(u){this._model.selectionStart&&(this._model.selectionEnd=this._getMouseBufferCoords(u))}_handleSingleClick(u){if(this._model.selectionStartLength=0,this._model.isSelectAllActive=!1,this._activeSelectionMode=this.shouldColumnSelect(u)?3:0,this._model.selectionStart=this._getMouseBufferCoords(u),!this._model.selectionStart)return;this._model.selectionEnd=void 0;const p=this._bufferService.buffer.lines.get(this._model.selectionStart[1]);p&&p.length!==this._model.selectionStart[0]&&p.hasWidth(this._model.selectionStart[0])===0&&this._model.selectionStart[0]++}_handleDoubleClick(u){this._selectWordAtCursor(u,!0)&&(this._activeSelectionMode=1)}_handleTripleClick(u){const p=this._getMouseBufferCoords(u);p&&(this._activeSelectionMode=2,this._selectLineAt(p[1]))}shouldColumnSelect(u){return u.altKey&&!(r.isMac&&this._optionsService.rawOptions.macOptionClickForcesSelection)}_handleMouseMove(u){if(u.stopImmediatePropagation(),!this._model.selectionStart)return;const p=this._model.selectionEnd?[this._model.selectionEnd[0],this._model.selectionEnd[1]]:null;if(this._model.selectionEnd=this._getMouseBufferCoords(u),!this._model.selectionEnd)return void this.refresh(!0);this._activeSelectionMode===2?this._model.selectionEnd[1]<this._model.selectionStart[1]?this._model.selectionEnd[0]=0:this._model.selectionEnd[0]=this._bufferService.cols:this._activeSelectionMode===1&&this._selectToWordAt(this._model.selectionEnd),this._dragScrollAmount=this._getMouseEventScrollAmount(u),this._activeSelectionMode!==3&&(this._dragScrollAmount>0?this._model.selectionEnd[0]=this._bufferService.cols:this._dragScrollAmount<0&&(this._model.selectionEnd[0]=0));const C=this._bufferService.buffer;if(this._model.selectionEnd[1]<C.lines.length){const b=C.lines.get(this._model.selectionEnd[1]);b&&b.hasWidth(this._model.selectionEnd[0])===0&&this._model.selectionEnd[0]<this._bufferService.cols&&this._model.selectionEnd[0]++}p&&p[0]===this._model.selectionEnd[0]&&p[1]===this._model.selectionEnd[1]||this.refresh(!0)}_dragScroll(){if(this._model.selectionEnd&&this._model.selectionStart&&this._dragScrollAmount){this._onRequestScrollLines.fire({amount:this._dragScrollAmount,suppressScrollEvent:!1});const u=this._bufferService.buffer;this._dragScrollAmount>0?(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=this._bufferService.cols),this._model.selectionEnd[1]=Math.min(u.ydisp+this._bufferService.rows,u.lines.length-1)):(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=0),this._model.selectionEnd[1]=u.ydisp),this.refresh()}}_handleMouseUp(u){const p=u.timeStamp-this._mouseDownTimeStamp;if(this._removeMouseDownListeners(),this.selectionText.length<=1&&p<500&&u.altKey&&this._optionsService.rawOptions.altClickMovesCursor){if(this._bufferService.buffer.ybase===this._bufferService.buffer.ydisp){const C=this._mouseService.getCoords(u,this._element,this._bufferService.cols,this._bufferService.rows,!1);if(C&&C[0]!==void 0&&C[1]!==void 0){const b=(0,c.moveToCellSequence)(C[0]-1,C[1]-1,this._bufferService,this._coreService.decPrivateModes.applicationCursorKeys);this._coreService.triggerDataEvent(b,!0)}}}else this._fireEventIfSelectionChanged()}_fireEventIfSelectionChanged(){const u=this._model.finalSelectionStart,p=this._model.finalSelectionEnd,C=!(!u||!p||u[0]===p[0]&&u[1]===p[1]);C?u&&p&&(this._oldSelectionStart&&this._oldSelectionEnd&&u[0]===this._oldSelectionStart[0]&&u[1]===this._oldSelectionStart[1]&&p[0]===this._oldSelectionEnd[0]&&p[1]===this._oldSelectionEnd[1]||this._fireOnSelectionChange(u,p,C)):this._oldHasSelection&&this._fireOnSelectionChange(u,p,C)}_fireOnSelectionChange(u,p,C){this._oldSelectionStart=u,this._oldSelectionEnd=p,this._oldHasSelection=C,this._onSelectionChange.fire()}_handleBufferActivate(u){this.clearSelection(),this._trimListener.dispose(),this._trimListener=u.activeBuffer.lines.onTrim(p=>this._handleTrim(p))}_convertViewportColToCharacterIndex(u,p){let C=p;for(let b=0;p>=b;b++){const y=u.loadCell(b,this._workCell).getChars().length;this._workCell.getWidth()===0?C--:y>1&&p!==b&&(C+=y-1)}return C}setSelection(u,p,C){this._model.clearSelection(),this._removeMouseDownListeners(),this._model.selectionStart=[u,p],this._model.selectionStartLength=C,this.refresh(),this._fireEventIfSelectionChanged()}rightClickSelect(u){this._isClickInSelection(u)||(this._selectWordAtCursor(u,!1)&&this.refresh(!0),this._fireEventIfSelectionChanged())}_getWordAt(u,p,C=!0,b=!0){if(u[0]>=this._bufferService.cols)return;const y=this._bufferService.buffer,w=y.lines.get(u[1]);if(!w)return;const k=y.translateBufferLineToString(u[1],!1);let D=this._convertViewportColToCharacterIndex(w,u[0]),P=D;const T=u[0]-D;let A=0,S=0,x=0,E=0;if(k.charAt(D)===" "){for(;D>0&&k.charAt(D-1)===" ";)D--;for(;P<k.length&&k.charAt(P+1)===" ";)P++}else{let F=u[0],N=u[0];w.getWidth(F)===0&&(A++,F--),w.getWidth(N)===2&&(S++,N++);const z=w.getString(N).length;for(z>1&&(E+=z-1,P+=z-1);F>0&&D>0&&!this._isCharWordSeparator(w.loadCell(F-1,this._workCell));){w.loadCell(F-1,this._workCell);const M=this._workCell.getChars().length;this._workCell.getWidth()===0?(A++,F--):M>1&&(x+=M-1,D-=M-1),D--,F--}for(;N<w.length&&P+1<k.length&&!this._isCharWordSeparator(w.loadCell(N+1,this._workCell));){w.loadCell(N+1,this._workCell);const M=this._workCell.getChars().length;this._workCell.getWidth()===2?(S++,N++):M>1&&(E+=M-1,P+=M-1),P++,N++}}P++;let R=D+T-A+x,I=Math.min(this._bufferService.cols,P-D+A+S-x-E);if(p||k.slice(D,P).trim()!==""){if(C&&R===0&&w.getCodePoint(0)!==32){const F=y.lines.get(u[1]-1);if(F&&w.isWrapped&&F.getCodePoint(this._bufferService.cols-1)!==32){const N=this._getWordAt([this._bufferService.cols-1,u[1]-1],!1,!0,!1);if(N){const z=this._bufferService.cols-N.start;R-=z,I+=z}}}if(b&&R+I===this._bufferService.cols&&w.getCodePoint(this._bufferService.cols-1)!==32){const F=y.lines.get(u[1]+1);if(F!=null&&F.isWrapped&&F.getCodePoint(0)!==32){const N=this._getWordAt([0,u[1]+1],!1,!1,!0);N&&(I+=N.length)}}return{start:R,length:I}}}_selectWordAt(u,p){const C=this._getWordAt(u,p);if(C){for(;C.start<0;)C.start+=this._bufferService.cols,u[1]--;this._model.selectionStart=[C.start,u[1]],this._model.selectionStartLength=C.length}}_selectToWordAt(u){const p=this._getWordAt(u,!0);if(p){let C=u[1];for(;p.start<0;)p.start+=this._bufferService.cols,C--;if(!this._model.areSelectionValuesReversed())for(;p.start+p.length>this._bufferService.cols;)p.length-=this._bufferService.cols,C++;this._model.selectionEnd=[this._model.areSelectionValuesReversed()?p.start:p.start+p.length,C]}}_isCharWordSeparator(u){return u.getWidth()!==0&&this._optionsService.rawOptions.wordSeparator.indexOf(u.getChars())>=0}_selectLineAt(u){const p=this._bufferService.buffer.getWrappedRangeForLine(u),C={start:{x:0,y:p.first},end:{x:this._bufferService.cols-1,y:p.last}};this._model.selectionStart=[0,p.first],this._model.selectionEnd=void 0,this._model.selectionStartLength=(0,t.getRangeLength)(C,this._bufferService.cols)}};s.SelectionService=g=_([d(3,h.IBufferService),d(4,h.ICoreService),d(5,m.IMouseService),d(6,h.IOptionsService),d(7,m.IRenderService),d(8,m.ICoreBrowserService)],g)},4725:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ILinkProviderService=s.IThemeService=s.ICharacterJoinerService=s.ISelectionService=s.IRenderService=s.IMouseService=s.ICoreBrowserService=s.ICharSizeService=void 0;const _=a(8343);s.ICharSizeService=(0,_.createDecorator)("CharSizeService"),s.ICoreBrowserService=(0,_.createDecorator)("CoreBrowserService"),s.IMouseService=(0,_.createDecorator)("MouseService"),s.IRenderService=(0,_.createDecorator)("RenderService"),s.ISelectionService=(0,_.createDecorator)("SelectionService"),s.ICharacterJoinerService=(0,_.createDecorator)("CharacterJoinerService"),s.IThemeService=(0,_.createDecorator)("ThemeService"),s.ILinkProviderService=(0,_.createDecorator)("LinkProviderService")},6731:function(B,s,a){var _=this&&this.__decorate||function(g,u,p,C){var b,y=arguments.length,w=y<3?u:C===null?C=Object.getOwnPropertyDescriptor(u,p):C;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")w=Reflect.decorate(g,u,p,C);else for(var k=g.length-1;k>=0;k--)(b=g[k])&&(w=(y<3?b(w):y>3?b(u,p,w):b(u,p))||w);return y>3&&w&&Object.defineProperty(u,p,w),w},d=this&&this.__param||function(g,u){return function(p,C){u(p,C,g)}};Object.defineProperty(s,"__esModule",{value:!0}),s.ThemeService=s.DEFAULT_ANSI_COLORS=void 0;const n=a(7239),c=a(8055),f=a(8460),m=a(844),o=a(2585),e=c.css.toColor("#ffffff"),r=c.css.toColor("#000000"),t=c.css.toColor("#ffffff"),i=c.css.toColor("#000000"),h={css:"rgba(255, 255, 255, 0.3)",rgba:**********};s.DEFAULT_ANSI_COLORS=Object.freeze((()=>{const g=[c.css.toColor("#2e3436"),c.css.toColor("#cc0000"),c.css.toColor("#4e9a06"),c.css.toColor("#c4a000"),c.css.toColor("#3465a4"),c.css.toColor("#75507b"),c.css.toColor("#06989a"),c.css.toColor("#d3d7cf"),c.css.toColor("#555753"),c.css.toColor("#ef2929"),c.css.toColor("#8ae234"),c.css.toColor("#fce94f"),c.css.toColor("#729fcf"),c.css.toColor("#ad7fa8"),c.css.toColor("#34e2e2"),c.css.toColor("#eeeeec")],u=[0,95,135,175,215,255];for(let p=0;p<216;p++){const C=u[p/36%6|0],b=u[p/6%6|0],y=u[p%6];g.push({css:c.channels.toCss(C,b,y),rgba:c.channels.toRgba(C,b,y)})}for(let p=0;p<24;p++){const C=8+10*p;g.push({css:c.channels.toCss(C,C,C),rgba:c.channels.toRgba(C,C,C)})}return g})());let v=s.ThemeService=class extends m.Disposable{get colors(){return this._colors}constructor(g){super(),this._optionsService=g,this._contrastCache=new n.ColorContrastCache,this._halfContrastCache=new n.ColorContrastCache,this._onChangeColors=this.register(new f.EventEmitter),this.onChangeColors=this._onChangeColors.event,this._colors={foreground:e,background:r,cursor:t,cursorAccent:i,selectionForeground:void 0,selectionBackgroundTransparent:h,selectionBackgroundOpaque:c.color.blend(r,h),selectionInactiveBackgroundTransparent:h,selectionInactiveBackgroundOpaque:c.color.blend(r,h),ansi:s.DEFAULT_ANSI_COLORS.slice(),contrastCache:this._contrastCache,halfContrastCache:this._halfContrastCache},this._updateRestoreColors(),this._setTheme(this._optionsService.rawOptions.theme),this.register(this._optionsService.onSpecificOptionChange("minimumContrastRatio",()=>this._contrastCache.clear())),this.register(this._optionsService.onSpecificOptionChange("theme",()=>this._setTheme(this._optionsService.rawOptions.theme)))}_setTheme(g={}){const u=this._colors;if(u.foreground=l(g.foreground,e),u.background=l(g.background,r),u.cursor=l(g.cursor,t),u.cursorAccent=l(g.cursorAccent,i),u.selectionBackgroundTransparent=l(g.selectionBackground,h),u.selectionBackgroundOpaque=c.color.blend(u.background,u.selectionBackgroundTransparent),u.selectionInactiveBackgroundTransparent=l(g.selectionInactiveBackground,u.selectionBackgroundTransparent),u.selectionInactiveBackgroundOpaque=c.color.blend(u.background,u.selectionInactiveBackgroundTransparent),u.selectionForeground=g.selectionForeground?l(g.selectionForeground,c.NULL_COLOR):void 0,u.selectionForeground===c.NULL_COLOR&&(u.selectionForeground=void 0),c.color.isOpaque(u.selectionBackgroundTransparent)&&(u.selectionBackgroundTransparent=c.color.opacity(u.selectionBackgroundTransparent,.3)),c.color.isOpaque(u.selectionInactiveBackgroundTransparent)&&(u.selectionInactiveBackgroundTransparent=c.color.opacity(u.selectionInactiveBackgroundTransparent,.3)),u.ansi=s.DEFAULT_ANSI_COLORS.slice(),u.ansi[0]=l(g.black,s.DEFAULT_ANSI_COLORS[0]),u.ansi[1]=l(g.red,s.DEFAULT_ANSI_COLORS[1]),u.ansi[2]=l(g.green,s.DEFAULT_ANSI_COLORS[2]),u.ansi[3]=l(g.yellow,s.DEFAULT_ANSI_COLORS[3]),u.ansi[4]=l(g.blue,s.DEFAULT_ANSI_COLORS[4]),u.ansi[5]=l(g.magenta,s.DEFAULT_ANSI_COLORS[5]),u.ansi[6]=l(g.cyan,s.DEFAULT_ANSI_COLORS[6]),u.ansi[7]=l(g.white,s.DEFAULT_ANSI_COLORS[7]),u.ansi[8]=l(g.brightBlack,s.DEFAULT_ANSI_COLORS[8]),u.ansi[9]=l(g.brightRed,s.DEFAULT_ANSI_COLORS[9]),u.ansi[10]=l(g.brightGreen,s.DEFAULT_ANSI_COLORS[10]),u.ansi[11]=l(g.brightYellow,s.DEFAULT_ANSI_COLORS[11]),u.ansi[12]=l(g.brightBlue,s.DEFAULT_ANSI_COLORS[12]),u.ansi[13]=l(g.brightMagenta,s.DEFAULT_ANSI_COLORS[13]),u.ansi[14]=l(g.brightCyan,s.DEFAULT_ANSI_COLORS[14]),u.ansi[15]=l(g.brightWhite,s.DEFAULT_ANSI_COLORS[15]),g.extendedAnsi){const p=Math.min(u.ansi.length-16,g.extendedAnsi.length);for(let C=0;C<p;C++)u.ansi[C+16]=l(g.extendedAnsi[C],s.DEFAULT_ANSI_COLORS[C+16])}this._contrastCache.clear(),this._halfContrastCache.clear(),this._updateRestoreColors(),this._onChangeColors.fire(this.colors)}restoreColor(g){this._restoreColor(g),this._onChangeColors.fire(this.colors)}_restoreColor(g){if(g!==void 0)switch(g){case 256:this._colors.foreground=this._restoreColors.foreground;break;case 257:this._colors.background=this._restoreColors.background;break;case 258:this._colors.cursor=this._restoreColors.cursor;break;default:this._colors.ansi[g]=this._restoreColors.ansi[g]}else for(let u=0;u<this._restoreColors.ansi.length;++u)this._colors.ansi[u]=this._restoreColors.ansi[u]}modifyColors(g){g(this._colors),this._onChangeColors.fire(this.colors)}_updateRestoreColors(){this._restoreColors={foreground:this._colors.foreground,background:this._colors.background,cursor:this._colors.cursor,ansi:this._colors.ansi.slice()}}};function l(g,u){if(g!==void 0)try{return c.css.toColor(g)}catch{}return u}s.ThemeService=v=_([d(0,o.IOptionsService)],v)},6349:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CircularList=void 0;const _=a(8460),d=a(844);class n extends d.Disposable{constructor(f){super(),this._maxLength=f,this.onDeleteEmitter=this.register(new _.EventEmitter),this.onDelete=this.onDeleteEmitter.event,this.onInsertEmitter=this.register(new _.EventEmitter),this.onInsert=this.onInsertEmitter.event,this.onTrimEmitter=this.register(new _.EventEmitter),this.onTrim=this.onTrimEmitter.event,this._array=new Array(this._maxLength),this._startIndex=0,this._length=0}get maxLength(){return this._maxLength}set maxLength(f){if(this._maxLength===f)return;const m=new Array(f);for(let o=0;o<Math.min(f,this.length);o++)m[o]=this._array[this._getCyclicIndex(o)];this._array=m,this._maxLength=f,this._startIndex=0}get length(){return this._length}set length(f){if(f>this._length)for(let m=this._length;m<f;m++)this._array[m]=void 0;this._length=f}get(f){return this._array[this._getCyclicIndex(f)]}set(f,m){this._array[this._getCyclicIndex(f)]=m}push(f){this._array[this._getCyclicIndex(this._length)]=f,this._length===this._maxLength?(this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1)):this._length++}recycle(){if(this._length!==this._maxLength)throw new Error("Can only recycle when the buffer is full");return this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1),this._array[this._getCyclicIndex(this._length-1)]}get isFull(){return this._length===this._maxLength}pop(){return this._array[this._getCyclicIndex(this._length---1)]}splice(f,m,...o){if(m){for(let e=f;e<this._length-m;e++)this._array[this._getCyclicIndex(e)]=this._array[this._getCyclicIndex(e+m)];this._length-=m,this.onDeleteEmitter.fire({index:f,amount:m})}for(let e=this._length-1;e>=f;e--)this._array[this._getCyclicIndex(e+o.length)]=this._array[this._getCyclicIndex(e)];for(let e=0;e<o.length;e++)this._array[this._getCyclicIndex(f+e)]=o[e];if(o.length&&this.onInsertEmitter.fire({index:f,amount:o.length}),this._length+o.length>this._maxLength){const e=this._length+o.length-this._maxLength;this._startIndex+=e,this._length=this._maxLength,this.onTrimEmitter.fire(e)}else this._length+=o.length}trimStart(f){f>this._length&&(f=this._length),this._startIndex+=f,this._length-=f,this.onTrimEmitter.fire(f)}shiftElements(f,m,o){if(!(m<=0)){if(f<0||f>=this._length)throw new Error("start argument out of range");if(f+o<0)throw new Error("Cannot shift elements in list beyond index 0");if(o>0){for(let r=m-1;r>=0;r--)this.set(f+r+o,this.get(f+r));const e=f+m+o-this._length;if(e>0)for(this._length+=e;this._length>this._maxLength;)this._length--,this._startIndex++,this.onTrimEmitter.fire(1)}else for(let e=0;e<m;e++)this.set(f+e+o,this.get(f+e))}}_getCyclicIndex(f){return(this._startIndex+f)%this._maxLength}}s.CircularList=n},1439:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.clone=void 0,s.clone=function a(_,d=5){if(typeof _!="object")return _;const n=Array.isArray(_)?[]:{};for(const c in _)n[c]=d<=1?_[c]:_[c]&&a(_[c],d-1);return n}},8055:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.contrastRatio=s.toPaddedHex=s.rgba=s.rgb=s.css=s.color=s.channels=s.NULL_COLOR=void 0;let a=0,_=0,d=0,n=0;var c,f,m,o,e;function r(i){const h=i.toString(16);return h.length<2?"0"+h:h}function t(i,h){return i<h?(h+.05)/(i+.05):(i+.05)/(h+.05)}s.NULL_COLOR={css:"#00000000",rgba:0},function(i){i.toCss=function(h,v,l,g){return g!==void 0?`#${r(h)}${r(v)}${r(l)}${r(g)}`:`#${r(h)}${r(v)}${r(l)}`},i.toRgba=function(h,v,l,g=255){return(h<<24|v<<16|l<<8|g)>>>0},i.toColor=function(h,v,l,g){return{css:i.toCss(h,v,l,g),rgba:i.toRgba(h,v,l,g)}}}(c||(s.channels=c={})),function(i){function h(v,l){return n=Math.round(255*l),[a,_,d]=e.toChannels(v.rgba),{css:c.toCss(a,_,d,n),rgba:c.toRgba(a,_,d,n)}}i.blend=function(v,l){if(n=(255&l.rgba)/255,n===1)return{css:l.css,rgba:l.rgba};const g=l.rgba>>24&255,u=l.rgba>>16&255,p=l.rgba>>8&255,C=v.rgba>>24&255,b=v.rgba>>16&255,y=v.rgba>>8&255;return a=C+Math.round((g-C)*n),_=b+Math.round((u-b)*n),d=y+Math.round((p-y)*n),{css:c.toCss(a,_,d),rgba:c.toRgba(a,_,d)}},i.isOpaque=function(v){return(255&v.rgba)==255},i.ensureContrastRatio=function(v,l,g){const u=e.ensureContrastRatio(v.rgba,l.rgba,g);if(u)return c.toColor(u>>24&255,u>>16&255,u>>8&255)},i.opaque=function(v){const l=(255|v.rgba)>>>0;return[a,_,d]=e.toChannels(l),{css:c.toCss(a,_,d),rgba:l}},i.opacity=h,i.multiplyOpacity=function(v,l){return n=255&v.rgba,h(v,n*l/255)},i.toColorRGB=function(v){return[v.rgba>>24&255,v.rgba>>16&255,v.rgba>>8&255]}}(f||(s.color=f={})),function(i){let h,v;try{const l=document.createElement("canvas");l.width=1,l.height=1;const g=l.getContext("2d",{willReadFrequently:!0});g&&(h=g,h.globalCompositeOperation="copy",v=h.createLinearGradient(0,0,1,1))}catch{}i.toColor=function(l){if(l.match(/#[\da-f]{3,8}/i))switch(l.length){case 4:return a=parseInt(l.slice(1,2).repeat(2),16),_=parseInt(l.slice(2,3).repeat(2),16),d=parseInt(l.slice(3,4).repeat(2),16),c.toColor(a,_,d);case 5:return a=parseInt(l.slice(1,2).repeat(2),16),_=parseInt(l.slice(2,3).repeat(2),16),d=parseInt(l.slice(3,4).repeat(2),16),n=parseInt(l.slice(4,5).repeat(2),16),c.toColor(a,_,d,n);case 7:return{css:l,rgba:(parseInt(l.slice(1),16)<<8|255)>>>0};case 9:return{css:l,rgba:parseInt(l.slice(1),16)>>>0}}const g=l.match(/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*(0|1|\d?\.(\d+))\s*)?\)/);if(g)return a=parseInt(g[1]),_=parseInt(g[2]),d=parseInt(g[3]),n=Math.round(255*(g[5]===void 0?1:parseFloat(g[5]))),c.toColor(a,_,d,n);if(!h||!v)throw new Error("css.toColor: Unsupported css format");if(h.fillStyle=v,h.fillStyle=l,typeof h.fillStyle!="string")throw new Error("css.toColor: Unsupported css format");if(h.fillRect(0,0,1,1),[a,_,d,n]=h.getImageData(0,0,1,1).data,n!==255)throw new Error("css.toColor: Unsupported css format");return{rgba:c.toRgba(a,_,d,n),css:l}}}(m||(s.css=m={})),function(i){function h(v,l,g){const u=v/255,p=l/255,C=g/255;return .2126*(u<=.03928?u/12.92:Math.pow((u+.055)/1.055,2.4))+.7152*(p<=.03928?p/12.92:Math.pow((p+.055)/1.055,2.4))+.0722*(C<=.03928?C/12.92:Math.pow((C+.055)/1.055,2.4))}i.relativeLuminance=function(v){return h(v>>16&255,v>>8&255,255&v)},i.relativeLuminance2=h}(o||(s.rgb=o={})),function(i){function h(l,g,u){const p=l>>24&255,C=l>>16&255,b=l>>8&255;let y=g>>24&255,w=g>>16&255,k=g>>8&255,D=t(o.relativeLuminance2(y,w,k),o.relativeLuminance2(p,C,b));for(;D<u&&(y>0||w>0||k>0);)y-=Math.max(0,Math.ceil(.1*y)),w-=Math.max(0,Math.ceil(.1*w)),k-=Math.max(0,Math.ceil(.1*k)),D=t(o.relativeLuminance2(y,w,k),o.relativeLuminance2(p,C,b));return(y<<24|w<<16|k<<8|255)>>>0}function v(l,g,u){const p=l>>24&255,C=l>>16&255,b=l>>8&255;let y=g>>24&255,w=g>>16&255,k=g>>8&255,D=t(o.relativeLuminance2(y,w,k),o.relativeLuminance2(p,C,b));for(;D<u&&(y<255||w<255||k<255);)y=Math.min(255,y+Math.ceil(.1*(255-y))),w=Math.min(255,w+Math.ceil(.1*(255-w))),k=Math.min(255,k+Math.ceil(.1*(255-k))),D=t(o.relativeLuminance2(y,w,k),o.relativeLuminance2(p,C,b));return(y<<24|w<<16|k<<8|255)>>>0}i.blend=function(l,g){if(n=(255&g)/255,n===1)return g;const u=g>>24&255,p=g>>16&255,C=g>>8&255,b=l>>24&255,y=l>>16&255,w=l>>8&255;return a=b+Math.round((u-b)*n),_=y+Math.round((p-y)*n),d=w+Math.round((C-w)*n),c.toRgba(a,_,d)},i.ensureContrastRatio=function(l,g,u){const p=o.relativeLuminance(l>>8),C=o.relativeLuminance(g>>8);if(t(p,C)<u){if(C<p){const w=h(l,g,u),k=t(p,o.relativeLuminance(w>>8));if(k<u){const D=v(l,g,u);return k>t(p,o.relativeLuminance(D>>8))?w:D}return w}const b=v(l,g,u),y=t(p,o.relativeLuminance(b>>8));if(y<u){const w=h(l,g,u);return y>t(p,o.relativeLuminance(w>>8))?b:w}return b}},i.reduceLuminance=h,i.increaseLuminance=v,i.toChannels=function(l){return[l>>24&255,l>>16&255,l>>8&255,255&l]}}(e||(s.rgba=e={})),s.toPaddedHex=r,s.contrastRatio=t},8969:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CoreTerminal=void 0;const _=a(844),d=a(2585),n=a(4348),c=a(7866),f=a(744),m=a(7302),o=a(6975),e=a(8460),r=a(1753),t=a(1480),i=a(7994),h=a(9282),v=a(5435),l=a(5981),g=a(2660);let u=!1;class p extends _.Disposable{get onScroll(){return this._onScrollApi||(this._onScrollApi=this.register(new e.EventEmitter),this._onScroll.event(b=>{var y;(y=this._onScrollApi)==null||y.fire(b.position)})),this._onScrollApi.event}get cols(){return this._bufferService.cols}get rows(){return this._bufferService.rows}get buffers(){return this._bufferService.buffers}get options(){return this.optionsService.options}set options(b){for(const y in b)this.optionsService.options[y]=b[y]}constructor(b){super(),this._windowsWrappingHeuristics=this.register(new _.MutableDisposable),this._onBinary=this.register(new e.EventEmitter),this.onBinary=this._onBinary.event,this._onData=this.register(new e.EventEmitter),this.onData=this._onData.event,this._onLineFeed=this.register(new e.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onResize=this.register(new e.EventEmitter),this.onResize=this._onResize.event,this._onWriteParsed=this.register(new e.EventEmitter),this.onWriteParsed=this._onWriteParsed.event,this._onScroll=this.register(new e.EventEmitter),this._instantiationService=new n.InstantiationService,this.optionsService=this.register(new m.OptionsService(b)),this._instantiationService.setService(d.IOptionsService,this.optionsService),this._bufferService=this.register(this._instantiationService.createInstance(f.BufferService)),this._instantiationService.setService(d.IBufferService,this._bufferService),this._logService=this.register(this._instantiationService.createInstance(c.LogService)),this._instantiationService.setService(d.ILogService,this._logService),this.coreService=this.register(this._instantiationService.createInstance(o.CoreService)),this._instantiationService.setService(d.ICoreService,this.coreService),this.coreMouseService=this.register(this._instantiationService.createInstance(r.CoreMouseService)),this._instantiationService.setService(d.ICoreMouseService,this.coreMouseService),this.unicodeService=this.register(this._instantiationService.createInstance(t.UnicodeService)),this._instantiationService.setService(d.IUnicodeService,this.unicodeService),this._charsetService=this._instantiationService.createInstance(i.CharsetService),this._instantiationService.setService(d.ICharsetService,this._charsetService),this._oscLinkService=this._instantiationService.createInstance(g.OscLinkService),this._instantiationService.setService(d.IOscLinkService,this._oscLinkService),this._inputHandler=this.register(new v.InputHandler(this._bufferService,this._charsetService,this.coreService,this._logService,this.optionsService,this._oscLinkService,this.coreMouseService,this.unicodeService)),this.register((0,e.forwardEvent)(this._inputHandler.onLineFeed,this._onLineFeed)),this.register(this._inputHandler),this.register((0,e.forwardEvent)(this._bufferService.onResize,this._onResize)),this.register((0,e.forwardEvent)(this.coreService.onData,this._onData)),this.register((0,e.forwardEvent)(this.coreService.onBinary,this._onBinary)),this.register(this.coreService.onRequestScrollToBottom(()=>this.scrollToBottom())),this.register(this.coreService.onUserInput(()=>this._writeBuffer.handleUserInput())),this.register(this.optionsService.onMultipleOptionChange(["windowsMode","windowsPty"],()=>this._handleWindowsPtyOptionChange())),this.register(this._bufferService.onScroll(y=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this.register(this._inputHandler.onScroll(y=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this._writeBuffer=this.register(new l.WriteBuffer((y,w)=>this._inputHandler.parse(y,w))),this.register((0,e.forwardEvent)(this._writeBuffer.onWriteParsed,this._onWriteParsed))}write(b,y){this._writeBuffer.write(b,y)}writeSync(b,y){this._logService.logLevel<=d.LogLevelEnum.WARN&&!u&&(this._logService.warn("writeSync is unreliable and will be removed soon."),u=!0),this._writeBuffer.writeSync(b,y)}input(b,y=!0){this.coreService.triggerDataEvent(b,y)}resize(b,y){isNaN(b)||isNaN(y)||(b=Math.max(b,f.MINIMUM_COLS),y=Math.max(y,f.MINIMUM_ROWS),this._bufferService.resize(b,y))}scroll(b,y=!1){this._bufferService.scroll(b,y)}scrollLines(b,y,w){this._bufferService.scrollLines(b,y,w)}scrollPages(b){this.scrollLines(b*(this.rows-1))}scrollToTop(){this.scrollLines(-this._bufferService.buffer.ydisp)}scrollToBottom(){this.scrollLines(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp)}scrollToLine(b){const y=b-this._bufferService.buffer.ydisp;y!==0&&this.scrollLines(y)}registerEscHandler(b,y){return this._inputHandler.registerEscHandler(b,y)}registerDcsHandler(b,y){return this._inputHandler.registerDcsHandler(b,y)}registerCsiHandler(b,y){return this._inputHandler.registerCsiHandler(b,y)}registerOscHandler(b,y){return this._inputHandler.registerOscHandler(b,y)}_setup(){this._handleWindowsPtyOptionChange()}reset(){this._inputHandler.reset(),this._bufferService.reset(),this._charsetService.reset(),this.coreService.reset(),this.coreMouseService.reset()}_handleWindowsPtyOptionChange(){let b=!1;const y=this.optionsService.rawOptions.windowsPty;y&&y.buildNumber!==void 0&&y.buildNumber!==void 0?b=y.backend==="conpty"&&y.buildNumber<21376:this.optionsService.rawOptions.windowsMode&&(b=!0),b?this._enableWindowsWrappingHeuristics():this._windowsWrappingHeuristics.clear()}_enableWindowsWrappingHeuristics(){if(!this._windowsWrappingHeuristics.value){const b=[];b.push(this.onLineFeed(h.updateWindowsModeWrappedState.bind(null,this._bufferService))),b.push(this.registerCsiHandler({final:"H"},()=>((0,h.updateWindowsModeWrappedState)(this._bufferService),!1))),this._windowsWrappingHeuristics.value=(0,_.toDisposable)(()=>{for(const y of b)y.dispose()})}}}s.CoreTerminal=p},8460:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.runAndSubscribe=s.forwardEvent=s.EventEmitter=void 0,s.EventEmitter=class{constructor(){this._listeners=[],this._disposed=!1}get event(){return this._event||(this._event=a=>(this._listeners.push(a),{dispose:()=>{if(!this._disposed){for(let _=0;_<this._listeners.length;_++)if(this._listeners[_]===a)return void this._listeners.splice(_,1)}}})),this._event}fire(a,_){const d=[];for(let n=0;n<this._listeners.length;n++)d.push(this._listeners[n]);for(let n=0;n<d.length;n++)d[n].call(void 0,a,_)}dispose(){this.clearListeners(),this._disposed=!0}clearListeners(){this._listeners&&(this._listeners.length=0)}},s.forwardEvent=function(a,_){return a(d=>_.fire(d))},s.runAndSubscribe=function(a,_){return _(void 0),a(d=>_(d))}},5435:function(B,s,a){var _=this&&this.__decorate||function(A,S,x,E){var R,I=arguments.length,F=I<3?S:E===null?E=Object.getOwnPropertyDescriptor(S,x):E;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")F=Reflect.decorate(A,S,x,E);else for(var N=A.length-1;N>=0;N--)(R=A[N])&&(F=(I<3?R(F):I>3?R(S,x,F):R(S,x))||F);return I>3&&F&&Object.defineProperty(S,x,F),F},d=this&&this.__param||function(A,S){return function(x,E){S(x,E,A)}};Object.defineProperty(s,"__esModule",{value:!0}),s.InputHandler=s.WindowsOptionsReportType=void 0;const n=a(2584),c=a(7116),f=a(2015),m=a(844),o=a(482),e=a(8437),r=a(8460),t=a(643),i=a(511),h=a(3734),v=a(2585),l=a(1480),g=a(6242),u=a(6351),p=a(5941),C={"(":0,")":1,"*":2,"+":3,"-":1,".":2},b=131072;function y(A,S){if(A>24)return S.setWinLines||!1;switch(A){case 1:return!!S.restoreWin;case 2:return!!S.minimizeWin;case 3:return!!S.setWinPosition;case 4:return!!S.setWinSizePixels;case 5:return!!S.raiseWin;case 6:return!!S.lowerWin;case 7:return!!S.refreshWin;case 8:return!!S.setWinSizeChars;case 9:return!!S.maximizeWin;case 10:return!!S.fullscreenWin;case 11:return!!S.getWinState;case 13:return!!S.getWinPosition;case 14:return!!S.getWinSizePixels;case 15:return!!S.getScreenSizePixels;case 16:return!!S.getCellSizePixels;case 18:return!!S.getWinSizeChars;case 19:return!!S.getScreenSizeChars;case 20:return!!S.getIconTitle;case 21:return!!S.getWinTitle;case 22:return!!S.pushTitle;case 23:return!!S.popTitle;case 24:return!!S.setWinLines}return!1}var w;(function(A){A[A.GET_WIN_SIZE_PIXELS=0]="GET_WIN_SIZE_PIXELS",A[A.GET_CELL_SIZE_PIXELS=1]="GET_CELL_SIZE_PIXELS"})(w||(s.WindowsOptionsReportType=w={}));let k=0;class D extends m.Disposable{getAttrData(){return this._curAttrData}constructor(S,x,E,R,I,F,N,z,M=new f.EscapeSequenceParser){super(),this._bufferService=S,this._charsetService=x,this._coreService=E,this._logService=R,this._optionsService=I,this._oscLinkService=F,this._coreMouseService=N,this._unicodeService=z,this._parser=M,this._parseBuffer=new Uint32Array(4096),this._stringDecoder=new o.StringToUtf32,this._utf8Decoder=new o.Utf8ToUtf32,this._workCell=new i.CellData,this._windowTitle="",this._iconName="",this._windowTitleStack=[],this._iconNameStack=[],this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=e.DEFAULT_ATTR_DATA.clone(),this._onRequestBell=this.register(new r.EventEmitter),this.onRequestBell=this._onRequestBell.event,this._onRequestRefreshRows=this.register(new r.EventEmitter),this.onRequestRefreshRows=this._onRequestRefreshRows.event,this._onRequestReset=this.register(new r.EventEmitter),this.onRequestReset=this._onRequestReset.event,this._onRequestSendFocus=this.register(new r.EventEmitter),this.onRequestSendFocus=this._onRequestSendFocus.event,this._onRequestSyncScrollBar=this.register(new r.EventEmitter),this.onRequestSyncScrollBar=this._onRequestSyncScrollBar.event,this._onRequestWindowsOptionsReport=this.register(new r.EventEmitter),this.onRequestWindowsOptionsReport=this._onRequestWindowsOptionsReport.event,this._onA11yChar=this.register(new r.EventEmitter),this.onA11yChar=this._onA11yChar.event,this._onA11yTab=this.register(new r.EventEmitter),this.onA11yTab=this._onA11yTab.event,this._onCursorMove=this.register(new r.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onLineFeed=this.register(new r.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onScroll=this.register(new r.EventEmitter),this.onScroll=this._onScroll.event,this._onTitleChange=this.register(new r.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onColor=this.register(new r.EventEmitter),this.onColor=this._onColor.event,this._parseStack={paused:!1,cursorStartX:0,cursorStartY:0,decodedLength:0,position:0},this._specialColors=[256,257,258],this.register(this._parser),this._dirtyRowTracker=new P(this._bufferService),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(L=>this._activeBuffer=L.activeBuffer)),this._parser.setCsiHandlerFallback((L,H)=>{this._logService.debug("Unknown CSI code: ",{identifier:this._parser.identToString(L),params:H.toArray()})}),this._parser.setEscHandlerFallback(L=>{this._logService.debug("Unknown ESC code: ",{identifier:this._parser.identToString(L)})}),this._parser.setExecuteHandlerFallback(L=>{this._logService.debug("Unknown EXECUTE code: ",{code:L})}),this._parser.setOscHandlerFallback((L,H,O)=>{this._logService.debug("Unknown OSC code: ",{identifier:L,action:H,data:O})}),this._parser.setDcsHandlerFallback((L,H,O)=>{H==="HOOK"&&(O=O.toArray()),this._logService.debug("Unknown DCS code: ",{identifier:this._parser.identToString(L),action:H,payload:O})}),this._parser.setPrintHandler((L,H,O)=>this.print(L,H,O)),this._parser.registerCsiHandler({final:"@"},L=>this.insertChars(L)),this._parser.registerCsiHandler({intermediates:" ",final:"@"},L=>this.scrollLeft(L)),this._parser.registerCsiHandler({final:"A"},L=>this.cursorUp(L)),this._parser.registerCsiHandler({intermediates:" ",final:"A"},L=>this.scrollRight(L)),this._parser.registerCsiHandler({final:"B"},L=>this.cursorDown(L)),this._parser.registerCsiHandler({final:"C"},L=>this.cursorForward(L)),this._parser.registerCsiHandler({final:"D"},L=>this.cursorBackward(L)),this._parser.registerCsiHandler({final:"E"},L=>this.cursorNextLine(L)),this._parser.registerCsiHandler({final:"F"},L=>this.cursorPrecedingLine(L)),this._parser.registerCsiHandler({final:"G"},L=>this.cursorCharAbsolute(L)),this._parser.registerCsiHandler({final:"H"},L=>this.cursorPosition(L)),this._parser.registerCsiHandler({final:"I"},L=>this.cursorForwardTab(L)),this._parser.registerCsiHandler({final:"J"},L=>this.eraseInDisplay(L,!1)),this._parser.registerCsiHandler({prefix:"?",final:"J"},L=>this.eraseInDisplay(L,!0)),this._parser.registerCsiHandler({final:"K"},L=>this.eraseInLine(L,!1)),this._parser.registerCsiHandler({prefix:"?",final:"K"},L=>this.eraseInLine(L,!0)),this._parser.registerCsiHandler({final:"L"},L=>this.insertLines(L)),this._parser.registerCsiHandler({final:"M"},L=>this.deleteLines(L)),this._parser.registerCsiHandler({final:"P"},L=>this.deleteChars(L)),this._parser.registerCsiHandler({final:"S"},L=>this.scrollUp(L)),this._parser.registerCsiHandler({final:"T"},L=>this.scrollDown(L)),this._parser.registerCsiHandler({final:"X"},L=>this.eraseChars(L)),this._parser.registerCsiHandler({final:"Z"},L=>this.cursorBackwardTab(L)),this._parser.registerCsiHandler({final:"`"},L=>this.charPosAbsolute(L)),this._parser.registerCsiHandler({final:"a"},L=>this.hPositionRelative(L)),this._parser.registerCsiHandler({final:"b"},L=>this.repeatPrecedingCharacter(L)),this._parser.registerCsiHandler({final:"c"},L=>this.sendDeviceAttributesPrimary(L)),this._parser.registerCsiHandler({prefix:">",final:"c"},L=>this.sendDeviceAttributesSecondary(L)),this._parser.registerCsiHandler({final:"d"},L=>this.linePosAbsolute(L)),this._parser.registerCsiHandler({final:"e"},L=>this.vPositionRelative(L)),this._parser.registerCsiHandler({final:"f"},L=>this.hVPosition(L)),this._parser.registerCsiHandler({final:"g"},L=>this.tabClear(L)),this._parser.registerCsiHandler({final:"h"},L=>this.setMode(L)),this._parser.registerCsiHandler({prefix:"?",final:"h"},L=>this.setModePrivate(L)),this._parser.registerCsiHandler({final:"l"},L=>this.resetMode(L)),this._parser.registerCsiHandler({prefix:"?",final:"l"},L=>this.resetModePrivate(L)),this._parser.registerCsiHandler({final:"m"},L=>this.charAttributes(L)),this._parser.registerCsiHandler({final:"n"},L=>this.deviceStatus(L)),this._parser.registerCsiHandler({prefix:"?",final:"n"},L=>this.deviceStatusPrivate(L)),this._parser.registerCsiHandler({intermediates:"!",final:"p"},L=>this.softReset(L)),this._parser.registerCsiHandler({intermediates:" ",final:"q"},L=>this.setCursorStyle(L)),this._parser.registerCsiHandler({final:"r"},L=>this.setScrollRegion(L)),this._parser.registerCsiHandler({final:"s"},L=>this.saveCursor(L)),this._parser.registerCsiHandler({final:"t"},L=>this.windowOptions(L)),this._parser.registerCsiHandler({final:"u"},L=>this.restoreCursor(L)),this._parser.registerCsiHandler({intermediates:"'",final:"}"},L=>this.insertColumns(L)),this._parser.registerCsiHandler({intermediates:"'",final:"~"},L=>this.deleteColumns(L)),this._parser.registerCsiHandler({intermediates:'"',final:"q"},L=>this.selectProtected(L)),this._parser.registerCsiHandler({intermediates:"$",final:"p"},L=>this.requestMode(L,!0)),this._parser.registerCsiHandler({prefix:"?",intermediates:"$",final:"p"},L=>this.requestMode(L,!1)),this._parser.setExecuteHandler(n.C0.BEL,()=>this.bell()),this._parser.setExecuteHandler(n.C0.LF,()=>this.lineFeed()),this._parser.setExecuteHandler(n.C0.VT,()=>this.lineFeed()),this._parser.setExecuteHandler(n.C0.FF,()=>this.lineFeed()),this._parser.setExecuteHandler(n.C0.CR,()=>this.carriageReturn()),this._parser.setExecuteHandler(n.C0.BS,()=>this.backspace()),this._parser.setExecuteHandler(n.C0.HT,()=>this.tab()),this._parser.setExecuteHandler(n.C0.SO,()=>this.shiftOut()),this._parser.setExecuteHandler(n.C0.SI,()=>this.shiftIn()),this._parser.setExecuteHandler(n.C1.IND,()=>this.index()),this._parser.setExecuteHandler(n.C1.NEL,()=>this.nextLine()),this._parser.setExecuteHandler(n.C1.HTS,()=>this.tabSet()),this._parser.registerOscHandler(0,new g.OscHandler(L=>(this.setTitle(L),this.setIconName(L),!0))),this._parser.registerOscHandler(1,new g.OscHandler(L=>this.setIconName(L))),this._parser.registerOscHandler(2,new g.OscHandler(L=>this.setTitle(L))),this._parser.registerOscHandler(4,new g.OscHandler(L=>this.setOrReportIndexedColor(L))),this._parser.registerOscHandler(8,new g.OscHandler(L=>this.setHyperlink(L))),this._parser.registerOscHandler(10,new g.OscHandler(L=>this.setOrReportFgColor(L))),this._parser.registerOscHandler(11,new g.OscHandler(L=>this.setOrReportBgColor(L))),this._parser.registerOscHandler(12,new g.OscHandler(L=>this.setOrReportCursorColor(L))),this._parser.registerOscHandler(104,new g.OscHandler(L=>this.restoreIndexedColor(L))),this._parser.registerOscHandler(110,new g.OscHandler(L=>this.restoreFgColor(L))),this._parser.registerOscHandler(111,new g.OscHandler(L=>this.restoreBgColor(L))),this._parser.registerOscHandler(112,new g.OscHandler(L=>this.restoreCursorColor(L))),this._parser.registerEscHandler({final:"7"},()=>this.saveCursor()),this._parser.registerEscHandler({final:"8"},()=>this.restoreCursor()),this._parser.registerEscHandler({final:"D"},()=>this.index()),this._parser.registerEscHandler({final:"E"},()=>this.nextLine()),this._parser.registerEscHandler({final:"H"},()=>this.tabSet()),this._parser.registerEscHandler({final:"M"},()=>this.reverseIndex()),this._parser.registerEscHandler({final:"="},()=>this.keypadApplicationMode()),this._parser.registerEscHandler({final:">"},()=>this.keypadNumericMode()),this._parser.registerEscHandler({final:"c"},()=>this.fullReset()),this._parser.registerEscHandler({final:"n"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"o"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"|"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"}"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"~"},()=>this.setgLevel(1)),this._parser.registerEscHandler({intermediates:"%",final:"@"},()=>this.selectDefaultCharset()),this._parser.registerEscHandler({intermediates:"%",final:"G"},()=>this.selectDefaultCharset());for(const L in c.CHARSETS)this._parser.registerEscHandler({intermediates:"(",final:L},()=>this.selectCharset("("+L)),this._parser.registerEscHandler({intermediates:")",final:L},()=>this.selectCharset(")"+L)),this._parser.registerEscHandler({intermediates:"*",final:L},()=>this.selectCharset("*"+L)),this._parser.registerEscHandler({intermediates:"+",final:L},()=>this.selectCharset("+"+L)),this._parser.registerEscHandler({intermediates:"-",final:L},()=>this.selectCharset("-"+L)),this._parser.registerEscHandler({intermediates:".",final:L},()=>this.selectCharset("."+L)),this._parser.registerEscHandler({intermediates:"/",final:L},()=>this.selectCharset("/"+L));this._parser.registerEscHandler({intermediates:"#",final:"8"},()=>this.screenAlignmentPattern()),this._parser.setErrorHandler(L=>(this._logService.error("Parsing error: ",L),L)),this._parser.registerDcsHandler({intermediates:"$",final:"q"},new u.DcsHandler((L,H)=>this.requestStatusString(L,H)))}_preserveStack(S,x,E,R){this._parseStack.paused=!0,this._parseStack.cursorStartX=S,this._parseStack.cursorStartY=x,this._parseStack.decodedLength=E,this._parseStack.position=R}_logSlowResolvingAsync(S){this._logService.logLevel<=v.LogLevelEnum.WARN&&Promise.race([S,new Promise((x,E)=>setTimeout(()=>E("#SLOW_TIMEOUT"),5e3))]).catch(x=>{if(x!=="#SLOW_TIMEOUT")throw x;console.warn("async parser handler taking longer than 5000 ms")})}_getCurrentLinkId(){return this._curAttrData.extended.urlId}parse(S,x){let E,R=this._activeBuffer.x,I=this._activeBuffer.y,F=0;const N=this._parseStack.paused;if(N){if(E=this._parser.parse(this._parseBuffer,this._parseStack.decodedLength,x))return this._logSlowResolvingAsync(E),E;R=this._parseStack.cursorStartX,I=this._parseStack.cursorStartY,this._parseStack.paused=!1,S.length>b&&(F=this._parseStack.position+b)}if(this._logService.logLevel<=v.LogLevelEnum.DEBUG&&this._logService.debug("parsing data"+(typeof S=="string"?` "${S}"`:` "${Array.prototype.map.call(S,L=>String.fromCharCode(L)).join("")}"`),typeof S=="string"?S.split("").map(L=>L.charCodeAt(0)):S),this._parseBuffer.length<S.length&&this._parseBuffer.length<b&&(this._parseBuffer=new Uint32Array(Math.min(S.length,b))),N||this._dirtyRowTracker.clearRange(),S.length>b)for(let L=F;L<S.length;L+=b){const H=L+b<S.length?L+b:S.length,O=typeof S=="string"?this._stringDecoder.decode(S.substring(L,H),this._parseBuffer):this._utf8Decoder.decode(S.subarray(L,H),this._parseBuffer);if(E=this._parser.parse(this._parseBuffer,O))return this._preserveStack(R,I,O,L),this._logSlowResolvingAsync(E),E}else if(!N){const L=typeof S=="string"?this._stringDecoder.decode(S,this._parseBuffer):this._utf8Decoder.decode(S,this._parseBuffer);if(E=this._parser.parse(this._parseBuffer,L))return this._preserveStack(R,I,L,0),this._logSlowResolvingAsync(E),E}this._activeBuffer.x===R&&this._activeBuffer.y===I||this._onCursorMove.fire();const z=this._dirtyRowTracker.end+(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp),M=this._dirtyRowTracker.start+(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp);M<this._bufferService.rows&&this._onRequestRefreshRows.fire(Math.min(M,this._bufferService.rows-1),Math.min(z,this._bufferService.rows-1))}print(S,x,E){let R,I;const F=this._charsetService.charset,N=this._optionsService.rawOptions.screenReaderMode,z=this._bufferService.cols,M=this._coreService.decPrivateModes.wraparound,L=this._coreService.modes.insertMode,H=this._curAttrData;let O=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._activeBuffer.x&&E-x>0&&O.getWidth(this._activeBuffer.x-1)===2&&O.setCellFromCodepoint(this._activeBuffer.x-1,0,1,H);let $=this._parser.precedingJoinState;for(let W=x;W<E;++W){if(R=S[W],R<127&&F){const re=F[String.fromCharCode(R)];re&&(R=re.charCodeAt(0))}const K=this._unicodeService.charProperties(R,$);I=l.UnicodeService.extractWidth(K);const q=l.UnicodeService.extractShouldJoin(K),ee=q?l.UnicodeService.extractWidth($):0;if($=K,N&&this._onA11yChar.fire((0,o.stringFromCodePoint)(R)),this._getCurrentLinkId()&&this._oscLinkService.addLineToLink(this._getCurrentLinkId(),this._activeBuffer.ybase+this._activeBuffer.y),this._activeBuffer.x+I-ee>z){if(M){const re=O;let j=this._activeBuffer.x-ee;for(this._activeBuffer.x=ee,this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData(),!0)):(this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!0),O=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y),ee>0&&O instanceof e.BufferLine&&O.copyCellsFrom(re,j,0,ee,!1);j<z;)re.setCellFromCodepoint(j++,0,1,H)}else if(this._activeBuffer.x=z-1,I===2)continue}if(q&&this._activeBuffer.x){const re=O.getWidth(this._activeBuffer.x-1)?1:2;O.addCodepointToCell(this._activeBuffer.x-re,R,I);for(let j=I-ee;--j>=0;)O.setCellFromCodepoint(this._activeBuffer.x++,0,0,H)}else if(L&&(O.insertCells(this._activeBuffer.x,I-ee,this._activeBuffer.getNullCell(H)),O.getWidth(z-1)===2&&O.setCellFromCodepoint(z-1,t.NULL_CELL_CODE,t.NULL_CELL_WIDTH,H)),O.setCellFromCodepoint(this._activeBuffer.x++,R,I,H),I>0)for(;--I;)O.setCellFromCodepoint(this._activeBuffer.x++,0,0,H)}this._parser.precedingJoinState=$,this._activeBuffer.x<z&&E-x>0&&O.getWidth(this._activeBuffer.x)===0&&!O.hasContent(this._activeBuffer.x)&&O.setCellFromCodepoint(this._activeBuffer.x,0,1,H),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}registerCsiHandler(S,x){return S.final!=="t"||S.prefix||S.intermediates?this._parser.registerCsiHandler(S,x):this._parser.registerCsiHandler(S,E=>!y(E.params[0],this._optionsService.rawOptions.windowOptions)||x(E))}registerDcsHandler(S,x){return this._parser.registerDcsHandler(S,new u.DcsHandler(x))}registerEscHandler(S,x){return this._parser.registerEscHandler(S,x)}registerOscHandler(S,x){return this._parser.registerOscHandler(S,new g.OscHandler(x))}bell(){return this._onRequestBell.fire(),!0}lineFeed(){return this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._optionsService.rawOptions.convertEol&&(this._activeBuffer.x=0),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows?this._activeBuffer.y=this._bufferService.rows-1:this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.x>=this._bufferService.cols&&this._activeBuffer.x--,this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._onLineFeed.fire(),!0}carriageReturn(){return this._activeBuffer.x=0,!0}backspace(){var S;if(!this._coreService.decPrivateModes.reverseWraparound)return this._restrictCursor(),this._activeBuffer.x>0&&this._activeBuffer.x--,!0;if(this._restrictCursor(this._bufferService.cols),this._activeBuffer.x>0)this._activeBuffer.x--;else if(this._activeBuffer.x===0&&this._activeBuffer.y>this._activeBuffer.scrollTop&&this._activeBuffer.y<=this._activeBuffer.scrollBottom&&((S=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y))!=null&&S.isWrapped)){this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.y--,this._activeBuffer.x=this._bufferService.cols-1;const x=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);x.hasWidth(this._activeBuffer.x)&&!x.hasContent(this._activeBuffer.x)&&this._activeBuffer.x--}return this._restrictCursor(),!0}tab(){if(this._activeBuffer.x>=this._bufferService.cols)return!0;const S=this._activeBuffer.x;return this._activeBuffer.x=this._activeBuffer.nextStop(),this._optionsService.rawOptions.screenReaderMode&&this._onA11yTab.fire(this._activeBuffer.x-S),!0}shiftOut(){return this._charsetService.setgLevel(1),!0}shiftIn(){return this._charsetService.setgLevel(0),!0}_restrictCursor(S=this._bufferService.cols-1){this._activeBuffer.x=Math.min(S,Math.max(0,this._activeBuffer.x)),this._activeBuffer.y=this._coreService.decPrivateModes.origin?Math.min(this._activeBuffer.scrollBottom,Math.max(this._activeBuffer.scrollTop,this._activeBuffer.y)):Math.min(this._bufferService.rows-1,Math.max(0,this._activeBuffer.y)),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_setCursor(S,x){this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._coreService.decPrivateModes.origin?(this._activeBuffer.x=S,this._activeBuffer.y=this._activeBuffer.scrollTop+x):(this._activeBuffer.x=S,this._activeBuffer.y=x),this._restrictCursor(),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_moveCursor(S,x){this._restrictCursor(),this._setCursor(this._activeBuffer.x+S,this._activeBuffer.y+x)}cursorUp(S){const x=this._activeBuffer.y-this._activeBuffer.scrollTop;return x>=0?this._moveCursor(0,-Math.min(x,S.params[0]||1)):this._moveCursor(0,-(S.params[0]||1)),!0}cursorDown(S){const x=this._activeBuffer.scrollBottom-this._activeBuffer.y;return x>=0?this._moveCursor(0,Math.min(x,S.params[0]||1)):this._moveCursor(0,S.params[0]||1),!0}cursorForward(S){return this._moveCursor(S.params[0]||1,0),!0}cursorBackward(S){return this._moveCursor(-(S.params[0]||1),0),!0}cursorNextLine(S){return this.cursorDown(S),this._activeBuffer.x=0,!0}cursorPrecedingLine(S){return this.cursorUp(S),this._activeBuffer.x=0,!0}cursorCharAbsolute(S){return this._setCursor((S.params[0]||1)-1,this._activeBuffer.y),!0}cursorPosition(S){return this._setCursor(S.length>=2?(S.params[1]||1)-1:0,(S.params[0]||1)-1),!0}charPosAbsolute(S){return this._setCursor((S.params[0]||1)-1,this._activeBuffer.y),!0}hPositionRelative(S){return this._moveCursor(S.params[0]||1,0),!0}linePosAbsolute(S){return this._setCursor(this._activeBuffer.x,(S.params[0]||1)-1),!0}vPositionRelative(S){return this._moveCursor(0,S.params[0]||1),!0}hVPosition(S){return this.cursorPosition(S),!0}tabClear(S){const x=S.params[0];return x===0?delete this._activeBuffer.tabs[this._activeBuffer.x]:x===3&&(this._activeBuffer.tabs={}),!0}cursorForwardTab(S){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let x=S.params[0]||1;for(;x--;)this._activeBuffer.x=this._activeBuffer.nextStop();return!0}cursorBackwardTab(S){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let x=S.params[0]||1;for(;x--;)this._activeBuffer.x=this._activeBuffer.prevStop();return!0}selectProtected(S){const x=S.params[0];return x===1&&(this._curAttrData.bg|=536870912),x!==2&&x!==0||(this._curAttrData.bg&=-536870913),!0}_eraseInBufferLine(S,x,E,R=!1,I=!1){const F=this._activeBuffer.lines.get(this._activeBuffer.ybase+S);F.replaceCells(x,E,this._activeBuffer.getNullCell(this._eraseAttrData()),I),R&&(F.isWrapped=!1)}_resetBufferLine(S,x=!1){const E=this._activeBuffer.lines.get(this._activeBuffer.ybase+S);E&&(E.fill(this._activeBuffer.getNullCell(this._eraseAttrData()),x),this._bufferService.buffer.clearMarkers(this._activeBuffer.ybase+S),E.isWrapped=!1)}eraseInDisplay(S,x=!1){let E;switch(this._restrictCursor(this._bufferService.cols),S.params[0]){case 0:for(E=this._activeBuffer.y,this._dirtyRowTracker.markDirty(E),this._eraseInBufferLine(E++,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,x);E<this._bufferService.rows;E++)this._resetBufferLine(E,x);this._dirtyRowTracker.markDirty(E);break;case 1:for(E=this._activeBuffer.y,this._dirtyRowTracker.markDirty(E),this._eraseInBufferLine(E,0,this._activeBuffer.x+1,!0,x),this._activeBuffer.x+1>=this._bufferService.cols&&(this._activeBuffer.lines.get(E+1).isWrapped=!1);E--;)this._resetBufferLine(E,x);this._dirtyRowTracker.markDirty(0);break;case 2:for(E=this._bufferService.rows,this._dirtyRowTracker.markDirty(E-1);E--;)this._resetBufferLine(E,x);this._dirtyRowTracker.markDirty(0);break;case 3:const R=this._activeBuffer.lines.length-this._bufferService.rows;R>0&&(this._activeBuffer.lines.trimStart(R),this._activeBuffer.ybase=Math.max(this._activeBuffer.ybase-R,0),this._activeBuffer.ydisp=Math.max(this._activeBuffer.ydisp-R,0),this._onScroll.fire(0))}return!0}eraseInLine(S,x=!1){switch(this._restrictCursor(this._bufferService.cols),S.params[0]){case 0:this._eraseInBufferLine(this._activeBuffer.y,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,x);break;case 1:this._eraseInBufferLine(this._activeBuffer.y,0,this._activeBuffer.x+1,!1,x);break;case 2:this._eraseInBufferLine(this._activeBuffer.y,0,this._bufferService.cols,!0,x)}return this._dirtyRowTracker.markDirty(this._activeBuffer.y),!0}insertLines(S){this._restrictCursor();let x=S.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const E=this._activeBuffer.ybase+this._activeBuffer.y,R=this._bufferService.rows-1-this._activeBuffer.scrollBottom,I=this._bufferService.rows-1+this._activeBuffer.ybase-R+1;for(;x--;)this._activeBuffer.lines.splice(I-1,1),this._activeBuffer.lines.splice(E,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}deleteLines(S){this._restrictCursor();let x=S.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const E=this._activeBuffer.ybase+this._activeBuffer.y;let R;for(R=this._bufferService.rows-1-this._activeBuffer.scrollBottom,R=this._bufferService.rows-1+this._activeBuffer.ybase-R;x--;)this._activeBuffer.lines.splice(E,1),this._activeBuffer.lines.splice(R,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}insertChars(S){this._restrictCursor();const x=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return x&&(x.insertCells(this._activeBuffer.x,S.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData())),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}deleteChars(S){this._restrictCursor();const x=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return x&&(x.deleteCells(this._activeBuffer.x,S.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData())),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}scrollUp(S){let x=S.params[0]||1;for(;x--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollDown(S){let x=S.params[0]||1;for(;x--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,0,this._activeBuffer.getBlankLine(e.DEFAULT_ATTR_DATA));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollLeft(S){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const x=S.params[0]||1;for(let E=this._activeBuffer.scrollTop;E<=this._activeBuffer.scrollBottom;++E){const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+E);R.deleteCells(0,x,this._activeBuffer.getNullCell(this._eraseAttrData())),R.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollRight(S){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const x=S.params[0]||1;for(let E=this._activeBuffer.scrollTop;E<=this._activeBuffer.scrollBottom;++E){const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+E);R.insertCells(0,x,this._activeBuffer.getNullCell(this._eraseAttrData())),R.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}insertColumns(S){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const x=S.params[0]||1;for(let E=this._activeBuffer.scrollTop;E<=this._activeBuffer.scrollBottom;++E){const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+E);R.insertCells(this._activeBuffer.x,x,this._activeBuffer.getNullCell(this._eraseAttrData())),R.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}deleteColumns(S){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const x=S.params[0]||1;for(let E=this._activeBuffer.scrollTop;E<=this._activeBuffer.scrollBottom;++E){const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+E);R.deleteCells(this._activeBuffer.x,x,this._activeBuffer.getNullCell(this._eraseAttrData())),R.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}eraseChars(S){this._restrictCursor();const x=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return x&&(x.replaceCells(this._activeBuffer.x,this._activeBuffer.x+(S.params[0]||1),this._activeBuffer.getNullCell(this._eraseAttrData())),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}repeatPrecedingCharacter(S){const x=this._parser.precedingJoinState;if(!x)return!0;const E=S.params[0]||1,R=l.UnicodeService.extractWidth(x),I=this._activeBuffer.x-R,F=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).getString(I),N=new Uint32Array(F.length*E);let z=0;for(let L=0;L<F.length;){const H=F.codePointAt(L)||0;N[z++]=H,L+=H>65535?2:1}let M=z;for(let L=1;L<E;++L)N.copyWithin(M,0,z),M+=z;return this.print(N,0,M),!0}sendDeviceAttributesPrimary(S){return S.params[0]>0||(this._is("xterm")||this._is("rxvt-unicode")||this._is("screen")?this._coreService.triggerDataEvent(n.C0.ESC+"[?1;2c"):this._is("linux")&&this._coreService.triggerDataEvent(n.C0.ESC+"[?6c")),!0}sendDeviceAttributesSecondary(S){return S.params[0]>0||(this._is("xterm")?this._coreService.triggerDataEvent(n.C0.ESC+"[>0;276;0c"):this._is("rxvt-unicode")?this._coreService.triggerDataEvent(n.C0.ESC+"[>85;95;0c"):this._is("linux")?this._coreService.triggerDataEvent(S.params[0]+"c"):this._is("screen")&&this._coreService.triggerDataEvent(n.C0.ESC+"[>83;40003;0c")),!0}_is(S){return(this._optionsService.rawOptions.termName+"").indexOf(S)===0}setMode(S){for(let x=0;x<S.length;x++)switch(S.params[x]){case 4:this._coreService.modes.insertMode=!0;break;case 20:this._optionsService.options.convertEol=!0}return!0}setModePrivate(S){for(let x=0;x<S.length;x++)switch(S.params[x]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!0;break;case 2:this._charsetService.setgCharset(0,c.DEFAULT_CHARSET),this._charsetService.setgCharset(1,c.DEFAULT_CHARSET),this._charsetService.setgCharset(2,c.DEFAULT_CHARSET),this._charsetService.setgCharset(3,c.DEFAULT_CHARSET);break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(132,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!0,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!0;break;case 12:this._optionsService.options.cursorBlink=!0;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!0;break;case 66:this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire();break;case 9:this._coreMouseService.activeProtocol="X10";break;case 1e3:this._coreMouseService.activeProtocol="VT200";break;case 1002:this._coreMouseService.activeProtocol="DRAG";break;case 1003:this._coreMouseService.activeProtocol="ANY";break;case 1004:this._coreService.decPrivateModes.sendFocus=!0,this._onRequestSendFocus.fire();break;case 1005:this._logService.debug("DECSET 1005 not supported (see #2507)");break;case 1006:this._coreMouseService.activeEncoding="SGR";break;case 1015:this._logService.debug("DECSET 1015 not supported (see #2507)");break;case 1016:this._coreMouseService.activeEncoding="SGR_PIXELS";break;case 25:this._coreService.isCursorHidden=!1;break;case 1048:this.saveCursor();break;case 1049:this.saveCursor();case 47:case 1047:this._bufferService.buffers.activateAltBuffer(this._eraseAttrData()),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!0}return!0}resetMode(S){for(let x=0;x<S.length;x++)switch(S.params[x]){case 4:this._coreService.modes.insertMode=!1;break;case 20:this._optionsService.options.convertEol=!1}return!0}resetModePrivate(S){for(let x=0;x<S.length;x++)switch(S.params[x]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!1;break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(80,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!1,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!1;break;case 12:this._optionsService.options.cursorBlink=!1;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!1;break;case 66:this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire();break;case 9:case 1e3:case 1002:case 1003:this._coreMouseService.activeProtocol="NONE";break;case 1004:this._coreService.decPrivateModes.sendFocus=!1;break;case 1005:this._logService.debug("DECRST 1005 not supported (see #2507)");break;case 1006:case 1016:this._coreMouseService.activeEncoding="DEFAULT";break;case 1015:this._logService.debug("DECRST 1015 not supported (see #2507)");break;case 25:this._coreService.isCursorHidden=!0;break;case 1048:this.restoreCursor();break;case 1049:case 47:case 1047:this._bufferService.buffers.activateNormalBuffer(),S.params[x]===1049&&this.restoreCursor(),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!1}return!0}requestMode(S,x){const E=this._coreService.decPrivateModes,{activeProtocol:R,activeEncoding:I}=this._coreMouseService,F=this._coreService,{buffers:N,cols:z}=this._bufferService,{active:M,alt:L}=N,H=this._optionsService.rawOptions,O=q=>q?1:2,$=S.params[0];return W=$,K=x?$===2?4:$===4?O(F.modes.insertMode):$===12?3:$===20?O(H.convertEol):0:$===1?O(E.applicationCursorKeys):$===3?H.windowOptions.setWinLines?z===80?2:z===132?1:0:0:$===6?O(E.origin):$===7?O(E.wraparound):$===8?3:$===9?O(R==="X10"):$===12?O(H.cursorBlink):$===25?O(!F.isCursorHidden):$===45?O(E.reverseWraparound):$===66?O(E.applicationKeypad):$===67?4:$===1e3?O(R==="VT200"):$===1002?O(R==="DRAG"):$===1003?O(R==="ANY"):$===1004?O(E.sendFocus):$===1005?4:$===1006?O(I==="SGR"):$===1015?4:$===1016?O(I==="SGR_PIXELS"):$===1048?1:$===47||$===1047||$===1049?O(M===L):$===2004?O(E.bracketedPasteMode):0,F.triggerDataEvent(`${n.C0.ESC}[${x?"":"?"}${W};${K}$y`),!0;var W,K}_updateAttrColor(S,x,E,R,I){return x===2?(S|=50331648,S&=-16777216,S|=h.AttributeData.fromColorRGB([E,R,I])):x===5&&(S&=-50331904,S|=33554432|255&E),S}_extractColor(S,x,E){const R=[0,0,-1,0,0,0];let I=0,F=0;do{if(R[F+I]=S.params[x+F],S.hasSubParams(x+F)){const N=S.getSubParams(x+F);let z=0;do R[1]===5&&(I=1),R[F+z+1+I]=N[z];while(++z<N.length&&z+F+1+I<R.length);break}if(R[1]===5&&F+I>=2||R[1]===2&&F+I>=5)break;R[1]&&(I=1)}while(++F+x<S.length&&F+I<R.length);for(let N=2;N<R.length;++N)R[N]===-1&&(R[N]=0);switch(R[0]){case 38:E.fg=this._updateAttrColor(E.fg,R[1],R[3],R[4],R[5]);break;case 48:E.bg=this._updateAttrColor(E.bg,R[1],R[3],R[4],R[5]);break;case 58:E.extended=E.extended.clone(),E.extended.underlineColor=this._updateAttrColor(E.extended.underlineColor,R[1],R[3],R[4],R[5])}return F}_processUnderline(S,x){x.extended=x.extended.clone(),(!~S||S>5)&&(S=1),x.extended.underlineStyle=S,x.fg|=268435456,S===0&&(x.fg&=-268435457),x.updateExtended()}_processSGR0(S){S.fg=e.DEFAULT_ATTR_DATA.fg,S.bg=e.DEFAULT_ATTR_DATA.bg,S.extended=S.extended.clone(),S.extended.underlineStyle=0,S.extended.underlineColor&=-67108864,S.updateExtended()}charAttributes(S){if(S.length===1&&S.params[0]===0)return this._processSGR0(this._curAttrData),!0;const x=S.length;let E;const R=this._curAttrData;for(let I=0;I<x;I++)E=S.params[I],E>=30&&E<=37?(R.fg&=-50331904,R.fg|=16777216|E-30):E>=40&&E<=47?(R.bg&=-50331904,R.bg|=16777216|E-40):E>=90&&E<=97?(R.fg&=-50331904,R.fg|=16777224|E-90):E>=100&&E<=107?(R.bg&=-50331904,R.bg|=16777224|E-100):E===0?this._processSGR0(R):E===1?R.fg|=134217728:E===3?R.bg|=67108864:E===4?(R.fg|=268435456,this._processUnderline(S.hasSubParams(I)?S.getSubParams(I)[0]:1,R)):E===5?R.fg|=536870912:E===7?R.fg|=67108864:E===8?R.fg|=1073741824:E===9?R.fg|=2147483648:E===2?R.bg|=134217728:E===21?this._processUnderline(2,R):E===22?(R.fg&=-134217729,R.bg&=-134217729):E===23?R.bg&=-67108865:E===24?(R.fg&=-268435457,this._processUnderline(0,R)):E===25?R.fg&=-536870913:E===27?R.fg&=-67108865:E===28?R.fg&=-1073741825:E===29?R.fg&=2147483647:E===39?(R.fg&=-67108864,R.fg|=16777215&e.DEFAULT_ATTR_DATA.fg):E===49?(R.bg&=-67108864,R.bg|=16777215&e.DEFAULT_ATTR_DATA.bg):E===38||E===48||E===58?I+=this._extractColor(S,I,R):E===53?R.bg|=1073741824:E===55?R.bg&=-1073741825:E===59?(R.extended=R.extended.clone(),R.extended.underlineColor=-1,R.updateExtended()):E===100?(R.fg&=-67108864,R.fg|=16777215&e.DEFAULT_ATTR_DATA.fg,R.bg&=-67108864,R.bg|=16777215&e.DEFAULT_ATTR_DATA.bg):this._logService.debug("Unknown SGR attribute: %d.",E);return!0}deviceStatus(S){switch(S.params[0]){case 5:this._coreService.triggerDataEvent(`${n.C0.ESC}[0n`);break;case 6:const x=this._activeBuffer.y+1,E=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${n.C0.ESC}[${x};${E}R`)}return!0}deviceStatusPrivate(S){if(S.params[0]===6){const x=this._activeBuffer.y+1,E=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${n.C0.ESC}[?${x};${E}R`)}return!0}softReset(S){return this._coreService.isCursorHidden=!1,this._onRequestSyncScrollBar.fire(),this._activeBuffer.scrollTop=0,this._activeBuffer.scrollBottom=this._bufferService.rows-1,this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._coreService.reset(),this._charsetService.reset(),this._activeBuffer.savedX=0,this._activeBuffer.savedY=this._activeBuffer.ybase,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,this._coreService.decPrivateModes.origin=!1,!0}setCursorStyle(S){const x=S.params[0]||1;switch(x){case 1:case 2:this._optionsService.options.cursorStyle="block";break;case 3:case 4:this._optionsService.options.cursorStyle="underline";break;case 5:case 6:this._optionsService.options.cursorStyle="bar"}const E=x%2==1;return this._optionsService.options.cursorBlink=E,!0}setScrollRegion(S){const x=S.params[0]||1;let E;return(S.length<2||(E=S.params[1])>this._bufferService.rows||E===0)&&(E=this._bufferService.rows),E>x&&(this._activeBuffer.scrollTop=x-1,this._activeBuffer.scrollBottom=E-1,this._setCursor(0,0)),!0}windowOptions(S){if(!y(S.params[0],this._optionsService.rawOptions.windowOptions))return!0;const x=S.length>1?S.params[1]:0;switch(S.params[0]){case 14:x!==2&&this._onRequestWindowsOptionsReport.fire(w.GET_WIN_SIZE_PIXELS);break;case 16:this._onRequestWindowsOptionsReport.fire(w.GET_CELL_SIZE_PIXELS);break;case 18:this._bufferService&&this._coreService.triggerDataEvent(`${n.C0.ESC}[8;${this._bufferService.rows};${this._bufferService.cols}t`);break;case 22:x!==0&&x!==2||(this._windowTitleStack.push(this._windowTitle),this._windowTitleStack.length>10&&this._windowTitleStack.shift()),x!==0&&x!==1||(this._iconNameStack.push(this._iconName),this._iconNameStack.length>10&&this._iconNameStack.shift());break;case 23:x!==0&&x!==2||this._windowTitleStack.length&&this.setTitle(this._windowTitleStack.pop()),x!==0&&x!==1||this._iconNameStack.length&&this.setIconName(this._iconNameStack.pop())}return!0}saveCursor(S){return this._activeBuffer.savedX=this._activeBuffer.x,this._activeBuffer.savedY=this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,!0}restoreCursor(S){return this._activeBuffer.x=this._activeBuffer.savedX||0,this._activeBuffer.y=Math.max(this._activeBuffer.savedY-this._activeBuffer.ybase,0),this._curAttrData.fg=this._activeBuffer.savedCurAttrData.fg,this._curAttrData.bg=this._activeBuffer.savedCurAttrData.bg,this._charsetService.charset=this._savedCharset,this._activeBuffer.savedCharset&&(this._charsetService.charset=this._activeBuffer.savedCharset),this._restrictCursor(),!0}setTitle(S){return this._windowTitle=S,this._onTitleChange.fire(S),!0}setIconName(S){return this._iconName=S,!0}setOrReportIndexedColor(S){const x=[],E=S.split(";");for(;E.length>1;){const R=E.shift(),I=E.shift();if(/^\d+$/.exec(R)){const F=parseInt(R);if(T(F))if(I==="?")x.push({type:0,index:F});else{const N=(0,p.parseColor)(I);N&&x.push({type:1,index:F,color:N})}}}return x.length&&this._onColor.fire(x),!0}setHyperlink(S){const x=S.split(";");return!(x.length<2)&&(x[1]?this._createHyperlink(x[0],x[1]):!x[0]&&this._finishHyperlink())}_createHyperlink(S,x){this._getCurrentLinkId()&&this._finishHyperlink();const E=S.split(":");let R;const I=E.findIndex(F=>F.startsWith("id="));return I!==-1&&(R=E[I].slice(3)||void 0),this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=this._oscLinkService.registerLink({id:R,uri:x}),this._curAttrData.updateExtended(),!0}_finishHyperlink(){return this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=0,this._curAttrData.updateExtended(),!0}_setOrReportSpecialColor(S,x){const E=S.split(";");for(let R=0;R<E.length&&!(x>=this._specialColors.length);++R,++x)if(E[R]==="?")this._onColor.fire([{type:0,index:this._specialColors[x]}]);else{const I=(0,p.parseColor)(E[R]);I&&this._onColor.fire([{type:1,index:this._specialColors[x],color:I}])}return!0}setOrReportFgColor(S){return this._setOrReportSpecialColor(S,0)}setOrReportBgColor(S){return this._setOrReportSpecialColor(S,1)}setOrReportCursorColor(S){return this._setOrReportSpecialColor(S,2)}restoreIndexedColor(S){if(!S)return this._onColor.fire([{type:2}]),!0;const x=[],E=S.split(";");for(let R=0;R<E.length;++R)if(/^\d+$/.exec(E[R])){const I=parseInt(E[R]);T(I)&&x.push({type:2,index:I})}return x.length&&this._onColor.fire(x),!0}restoreFgColor(S){return this._onColor.fire([{type:2,index:256}]),!0}restoreBgColor(S){return this._onColor.fire([{type:2,index:257}]),!0}restoreCursorColor(S){return this._onColor.fire([{type:2,index:258}]),!0}nextLine(){return this._activeBuffer.x=0,this.index(),!0}keypadApplicationMode(){return this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire(),!0}keypadNumericMode(){return this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire(),!0}selectDefaultCharset(){return this._charsetService.setgLevel(0),this._charsetService.setgCharset(0,c.DEFAULT_CHARSET),!0}selectCharset(S){return S.length!==2?(this.selectDefaultCharset(),!0):(S[0]==="/"||this._charsetService.setgCharset(C[S[0]],c.CHARSETS[S[1]]||c.DEFAULT_CHARSET),!0)}index(){return this._restrictCursor(),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._restrictCursor(),!0}tabSet(){return this._activeBuffer.tabs[this._activeBuffer.x]=!0,!0}reverseIndex(){if(this._restrictCursor(),this._activeBuffer.y===this._activeBuffer.scrollTop){const S=this._activeBuffer.scrollBottom-this._activeBuffer.scrollTop;this._activeBuffer.lines.shiftElements(this._activeBuffer.ybase+this._activeBuffer.y,S,1),this._activeBuffer.lines.set(this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.getBlankLine(this._eraseAttrData())),this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom)}else this._activeBuffer.y--,this._restrictCursor();return!0}fullReset(){return this._parser.reset(),this._onRequestReset.fire(),!0}reset(){this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=e.DEFAULT_ATTR_DATA.clone()}_eraseAttrData(){return this._eraseAttrDataInternal.bg&=-67108864,this._eraseAttrDataInternal.bg|=67108863&this._curAttrData.bg,this._eraseAttrDataInternal}setgLevel(S){return this._charsetService.setgLevel(S),!0}screenAlignmentPattern(){const S=new i.CellData;S.content=4194373,S.fg=this._curAttrData.fg,S.bg=this._curAttrData.bg,this._setCursor(0,0);for(let x=0;x<this._bufferService.rows;++x){const E=this._activeBuffer.ybase+this._activeBuffer.y+x,R=this._activeBuffer.lines.get(E);R&&(R.fill(S),R.isWrapped=!1)}return this._dirtyRowTracker.markAllDirty(),this._setCursor(0,0),!0}requestStatusString(S,x){const E=this._bufferService.buffer,R=this._optionsService.rawOptions;return(I=>(this._coreService.triggerDataEvent(`${n.C0.ESC}${I}${n.C0.ESC}\\`),!0))(S==='"q'?`P1$r${this._curAttrData.isProtected()?1:0}"q`:S==='"p'?'P1$r61;1"p':S==="r"?`P1$r${E.scrollTop+1};${E.scrollBottom+1}r`:S==="m"?"P1$r0m":S===" q"?`P1$r${{block:2,underline:4,bar:6}[R.cursorStyle]-(R.cursorBlink?1:0)} q`:"P0$r")}markRangeDirty(S,x){this._dirtyRowTracker.markRangeDirty(S,x)}}s.InputHandler=D;let P=class{constructor(A){this._bufferService=A,this.clearRange()}clearRange(){this.start=this._bufferService.buffer.y,this.end=this._bufferService.buffer.y}markDirty(A){A<this.start?this.start=A:A>this.end&&(this.end=A)}markRangeDirty(A,S){A>S&&(k=A,A=S,S=k),A<this.start&&(this.start=A),S>this.end&&(this.end=S)}markAllDirty(){this.markRangeDirty(0,this._bufferService.rows-1)}};function T(A){return 0<=A&&A<256}P=_([d(0,v.IBufferService)],P)},844:(B,s)=>{function a(_){for(const d of _)d.dispose();_.length=0}Object.defineProperty(s,"__esModule",{value:!0}),s.getDisposeArrayDisposable=s.disposeArray=s.toDisposable=s.MutableDisposable=s.Disposable=void 0,s.Disposable=class{constructor(){this._disposables=[],this._isDisposed=!1}dispose(){this._isDisposed=!0;for(const _ of this._disposables)_.dispose();this._disposables.length=0}register(_){return this._disposables.push(_),_}unregister(_){const d=this._disposables.indexOf(_);d!==-1&&this._disposables.splice(d,1)}},s.MutableDisposable=class{constructor(){this._isDisposed=!1}get value(){return this._isDisposed?void 0:this._value}set value(_){var d;this._isDisposed||_===this._value||((d=this._value)==null||d.dispose(),this._value=_)}clear(){this.value=void 0}dispose(){var _;this._isDisposed=!0,(_=this._value)==null||_.dispose(),this._value=void 0}},s.toDisposable=function(_){return{dispose:_}},s.disposeArray=a,s.getDisposeArrayDisposable=function(_){return{dispose:()=>a(_)}}},1505:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.FourKeyMap=s.TwoKeyMap=void 0;class a{constructor(){this._data={}}set(d,n,c){this._data[d]||(this._data[d]={}),this._data[d][n]=c}get(d,n){return this._data[d]?this._data[d][n]:void 0}clear(){this._data={}}}s.TwoKeyMap=a,s.FourKeyMap=class{constructor(){this._data=new a}set(_,d,n,c,f){this._data.get(_,d)||this._data.set(_,d,new a),this._data.get(_,d).set(n,c,f)}get(_,d,n,c){var f;return(f=this._data.get(_,d))==null?void 0:f.get(n,c)}clear(){this._data.clear()}}},6114:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.isChromeOS=s.isLinux=s.isWindows=s.isIphone=s.isIpad=s.isMac=s.getSafariVersion=s.isSafari=s.isLegacyEdge=s.isFirefox=s.isNode=void 0,s.isNode=typeof process<"u"&&"title"in process;const a=s.isNode?"node":navigator.userAgent,_=s.isNode?"node":navigator.platform;s.isFirefox=a.includes("Firefox"),s.isLegacyEdge=a.includes("Edge"),s.isSafari=/^((?!chrome|android).)*safari/i.test(a),s.getSafariVersion=function(){if(!s.isSafari)return 0;const d=a.match(/Version\/(\d+)/);return d===null||d.length<2?0:parseInt(d[1])},s.isMac=["Macintosh","MacIntel","MacPPC","Mac68K"].includes(_),s.isIpad=_==="iPad",s.isIphone=_==="iPhone",s.isWindows=["Windows","Win16","Win32","WinCE"].includes(_),s.isLinux=_.indexOf("Linux")>=0,s.isChromeOS=/\bCrOS\b/.test(a)},6106:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.SortedList=void 0;let a=0;s.SortedList=class{constructor(_){this._getKey=_,this._array=[]}clear(){this._array.length=0}insert(_){this._array.length!==0?(a=this._search(this._getKey(_)),this._array.splice(a,0,_)):this._array.push(_)}delete(_){if(this._array.length===0)return!1;const d=this._getKey(_);if(d===void 0||(a=this._search(d),a===-1)||this._getKey(this._array[a])!==d)return!1;do if(this._array[a]===_)return this._array.splice(a,1),!0;while(++a<this._array.length&&this._getKey(this._array[a])===d);return!1}*getKeyIterator(_){if(this._array.length!==0&&(a=this._search(_),!(a<0||a>=this._array.length)&&this._getKey(this._array[a])===_))do yield this._array[a];while(++a<this._array.length&&this._getKey(this._array[a])===_)}forEachByKey(_,d){if(this._array.length!==0&&(a=this._search(_),!(a<0||a>=this._array.length)&&this._getKey(this._array[a])===_))do d(this._array[a]);while(++a<this._array.length&&this._getKey(this._array[a])===_)}values(){return[...this._array].values()}_search(_){let d=0,n=this._array.length-1;for(;n>=d;){let c=d+n>>1;const f=this._getKey(this._array[c]);if(f>_)n=c-1;else{if(!(f<_)){for(;c>0&&this._getKey(this._array[c-1])===_;)c--;return c}d=c+1}}return d}}},7226:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DebouncedIdleTask=s.IdleTaskQueue=s.PriorityTaskQueue=void 0;const _=a(6114);class d{constructor(){this._tasks=[],this._i=0}enqueue(f){this._tasks.push(f),this._start()}flush(){for(;this._i<this._tasks.length;)this._tasks[this._i]()||this._i++;this.clear()}clear(){this._idleCallback&&(this._cancelCallback(this._idleCallback),this._idleCallback=void 0),this._i=0,this._tasks.length=0}_start(){this._idleCallback||(this._idleCallback=this._requestCallback(this._process.bind(this)))}_process(f){this._idleCallback=void 0;let m=0,o=0,e=f.timeRemaining(),r=0;for(;this._i<this._tasks.length;){if(m=Date.now(),this._tasks[this._i]()||this._i++,m=Math.max(1,Date.now()-m),o=Math.max(m,o),r=f.timeRemaining(),1.5*o>r)return e-m<-20&&console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(e-m))}ms`),void this._start();e=r}this.clear()}}class n extends d{_requestCallback(f){return setTimeout(()=>f(this._createDeadline(16)))}_cancelCallback(f){clearTimeout(f)}_createDeadline(f){const m=Date.now()+f;return{timeRemaining:()=>Math.max(0,m-Date.now())}}}s.PriorityTaskQueue=n,s.IdleTaskQueue=!_.isNode&&"requestIdleCallback"in window?class extends d{_requestCallback(c){return requestIdleCallback(c)}_cancelCallback(c){cancelIdleCallback(c)}}:n,s.DebouncedIdleTask=class{constructor(){this._queue=new s.IdleTaskQueue}set(c){this._queue.clear(),this._queue.enqueue(c)}flush(){this._queue.flush()}}},9282:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.updateWindowsModeWrappedState=void 0;const _=a(643);s.updateWindowsModeWrappedState=function(d){const n=d.buffer.lines.get(d.buffer.ybase+d.buffer.y-1),c=n==null?void 0:n.get(d.cols-1),f=d.buffer.lines.get(d.buffer.ybase+d.buffer.y);f&&c&&(f.isWrapped=c[_.CHAR_DATA_CODE_INDEX]!==_.NULL_CELL_CODE&&c[_.CHAR_DATA_CODE_INDEX]!==_.WHITESPACE_CELL_CODE)}},3734:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ExtendedAttrs=s.AttributeData=void 0;class a{constructor(){this.fg=0,this.bg=0,this.extended=new _}static toColorRGB(n){return[n>>>16&255,n>>>8&255,255&n]}static fromColorRGB(n){return(255&n[0])<<16|(255&n[1])<<8|255&n[2]}clone(){const n=new a;return n.fg=this.fg,n.bg=this.bg,n.extended=this.extended.clone(),n}isInverse(){return 67108864&this.fg}isBold(){return 134217728&this.fg}isUnderline(){return this.hasExtendedAttrs()&&this.extended.underlineStyle!==0?1:268435456&this.fg}isBlink(){return 536870912&this.fg}isInvisible(){return 1073741824&this.fg}isItalic(){return 67108864&this.bg}isDim(){return 134217728&this.bg}isStrikethrough(){return 2147483648&this.fg}isProtected(){return 536870912&this.bg}isOverline(){return 1073741824&this.bg}getFgColorMode(){return 50331648&this.fg}getBgColorMode(){return 50331648&this.bg}isFgRGB(){return(50331648&this.fg)==50331648}isBgRGB(){return(50331648&this.bg)==50331648}isFgPalette(){return(50331648&this.fg)==16777216||(50331648&this.fg)==33554432}isBgPalette(){return(50331648&this.bg)==16777216||(50331648&this.bg)==33554432}isFgDefault(){return(50331648&this.fg)==0}isBgDefault(){return(50331648&this.bg)==0}isAttributeDefault(){return this.fg===0&&this.bg===0}getFgColor(){switch(50331648&this.fg){case 16777216:case 33554432:return 255&this.fg;case 50331648:return 16777215&this.fg;default:return-1}}getBgColor(){switch(50331648&this.bg){case 16777216:case 33554432:return 255&this.bg;case 50331648:return 16777215&this.bg;default:return-1}}hasExtendedAttrs(){return 268435456&this.bg}updateExtended(){this.extended.isEmpty()?this.bg&=-268435457:this.bg|=268435456}getUnderlineColor(){if(268435456&this.bg&&~this.extended.underlineColor)switch(50331648&this.extended.underlineColor){case 16777216:case 33554432:return 255&this.extended.underlineColor;case 50331648:return 16777215&this.extended.underlineColor;default:return this.getFgColor()}return this.getFgColor()}getUnderlineColorMode(){return 268435456&this.bg&&~this.extended.underlineColor?50331648&this.extended.underlineColor:this.getFgColorMode()}isUnderlineColorRGB(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==50331648:this.isFgRGB()}isUnderlineColorPalette(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==16777216||(50331648&this.extended.underlineColor)==33554432:this.isFgPalette()}isUnderlineColorDefault(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==0:this.isFgDefault()}getUnderlineStyle(){return 268435456&this.fg?268435456&this.bg?this.extended.underlineStyle:1:0}getUnderlineVariantOffset(){return this.extended.underlineVariantOffset}}s.AttributeData=a;class _{get ext(){return this._urlId?-469762049&this._ext|this.underlineStyle<<26:this._ext}set ext(n){this._ext=n}get underlineStyle(){return this._urlId?5:(469762048&this._ext)>>26}set underlineStyle(n){this._ext&=-469762049,this._ext|=n<<26&469762048}get underlineColor(){return 67108863&this._ext}set underlineColor(n){this._ext&=-67108864,this._ext|=67108863&n}get urlId(){return this._urlId}set urlId(n){this._urlId=n}get underlineVariantOffset(){const n=(3758096384&this._ext)>>29;return n<0?4294967288^n:n}set underlineVariantOffset(n){this._ext&=536870911,this._ext|=n<<29&3758096384}constructor(n=0,c=0){this._ext=0,this._urlId=0,this._ext=n,this._urlId=c}clone(){return new _(this._ext,this._urlId)}isEmpty(){return this.underlineStyle===0&&this._urlId===0}}s.ExtendedAttrs=_},9092:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Buffer=s.MAX_BUFFER_SIZE=void 0;const _=a(6349),d=a(7226),n=a(3734),c=a(8437),f=a(4634),m=a(511),o=a(643),e=a(4863),r=a(7116);s.MAX_BUFFER_SIZE=4294967295,s.Buffer=class{constructor(t,i,h){this._hasScrollback=t,this._optionsService=i,this._bufferService=h,this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.tabs={},this.savedY=0,this.savedX=0,this.savedCurAttrData=c.DEFAULT_ATTR_DATA.clone(),this.savedCharset=r.DEFAULT_CHARSET,this.markers=[],this._nullCell=m.CellData.fromCharData([0,o.NULL_CELL_CHAR,o.NULL_CELL_WIDTH,o.NULL_CELL_CODE]),this._whitespaceCell=m.CellData.fromCharData([0,o.WHITESPACE_CELL_CHAR,o.WHITESPACE_CELL_WIDTH,o.WHITESPACE_CELL_CODE]),this._isClearing=!1,this._memoryCleanupQueue=new d.IdleTaskQueue,this._memoryCleanupPosition=0,this._cols=this._bufferService.cols,this._rows=this._bufferService.rows,this.lines=new _.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}getNullCell(t){return t?(this._nullCell.fg=t.fg,this._nullCell.bg=t.bg,this._nullCell.extended=t.extended):(this._nullCell.fg=0,this._nullCell.bg=0,this._nullCell.extended=new n.ExtendedAttrs),this._nullCell}getWhitespaceCell(t){return t?(this._whitespaceCell.fg=t.fg,this._whitespaceCell.bg=t.bg,this._whitespaceCell.extended=t.extended):(this._whitespaceCell.fg=0,this._whitespaceCell.bg=0,this._whitespaceCell.extended=new n.ExtendedAttrs),this._whitespaceCell}getBlankLine(t,i){return new c.BufferLine(this._bufferService.cols,this.getNullCell(t),i)}get hasScrollback(){return this._hasScrollback&&this.lines.maxLength>this._rows}get isCursorInViewport(){const t=this.ybase+this.y-this.ydisp;return t>=0&&t<this._rows}_getCorrectBufferLength(t){if(!this._hasScrollback)return t;const i=t+this._optionsService.rawOptions.scrollback;return i>s.MAX_BUFFER_SIZE?s.MAX_BUFFER_SIZE:i}fillViewportRows(t){if(this.lines.length===0){t===void 0&&(t=c.DEFAULT_ATTR_DATA);let i=this._rows;for(;i--;)this.lines.push(this.getBlankLine(t))}}clear(){this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.lines=new _.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}resize(t,i){const h=this.getNullCell(c.DEFAULT_ATTR_DATA);let v=0;const l=this._getCorrectBufferLength(i);if(l>this.lines.maxLength&&(this.lines.maxLength=l),this.lines.length>0){if(this._cols<t)for(let u=0;u<this.lines.length;u++)v+=+this.lines.get(u).resize(t,h);let g=0;if(this._rows<i)for(let u=this._rows;u<i;u++)this.lines.length<i+this.ybase&&(this._optionsService.rawOptions.windowsMode||this._optionsService.rawOptions.windowsPty.backend!==void 0||this._optionsService.rawOptions.windowsPty.buildNumber!==void 0?this.lines.push(new c.BufferLine(t,h)):this.ybase>0&&this.lines.length<=this.ybase+this.y+g+1?(this.ybase--,g++,this.ydisp>0&&this.ydisp--):this.lines.push(new c.BufferLine(t,h)));else for(let u=this._rows;u>i;u--)this.lines.length>i+this.ybase&&(this.lines.length>this.ybase+this.y+1?this.lines.pop():(this.ybase++,this.ydisp++));if(l<this.lines.maxLength){const u=this.lines.length-l;u>0&&(this.lines.trimStart(u),this.ybase=Math.max(this.ybase-u,0),this.ydisp=Math.max(this.ydisp-u,0),this.savedY=Math.max(this.savedY-u,0)),this.lines.maxLength=l}this.x=Math.min(this.x,t-1),this.y=Math.min(this.y,i-1),g&&(this.y+=g),this.savedX=Math.min(this.savedX,t-1),this.scrollTop=0}if(this.scrollBottom=i-1,this._isReflowEnabled&&(this._reflow(t,i),this._cols>t))for(let g=0;g<this.lines.length;g++)v+=+this.lines.get(g).resize(t,h);this._cols=t,this._rows=i,this._memoryCleanupQueue.clear(),v>.1*this.lines.length&&(this._memoryCleanupPosition=0,this._memoryCleanupQueue.enqueue(()=>this._batchedMemoryCleanup()))}_batchedMemoryCleanup(){let t=!0;this._memoryCleanupPosition>=this.lines.length&&(this._memoryCleanupPosition=0,t=!1);let i=0;for(;this._memoryCleanupPosition<this.lines.length;)if(i+=this.lines.get(this._memoryCleanupPosition++).cleanupMemory(),i>100)return!0;return t}get _isReflowEnabled(){const t=this._optionsService.rawOptions.windowsPty;return t&&t.buildNumber?this._hasScrollback&&t.backend==="conpty"&&t.buildNumber>=21376:this._hasScrollback&&!this._optionsService.rawOptions.windowsMode}_reflow(t,i){this._cols!==t&&(t>this._cols?this._reflowLarger(t,i):this._reflowSmaller(t,i))}_reflowLarger(t,i){const h=(0,f.reflowLargerGetLinesToRemove)(this.lines,this._cols,t,this.ybase+this.y,this.getNullCell(c.DEFAULT_ATTR_DATA));if(h.length>0){const v=(0,f.reflowLargerCreateNewLayout)(this.lines,h);(0,f.reflowLargerApplyNewLayout)(this.lines,v.layout),this._reflowLargerAdjustViewport(t,i,v.countRemoved)}}_reflowLargerAdjustViewport(t,i,h){const v=this.getNullCell(c.DEFAULT_ATTR_DATA);let l=h;for(;l-- >0;)this.ybase===0?(this.y>0&&this.y--,this.lines.length<i&&this.lines.push(new c.BufferLine(t,v))):(this.ydisp===this.ybase&&this.ydisp--,this.ybase--);this.savedY=Math.max(this.savedY-h,0)}_reflowSmaller(t,i){const h=this.getNullCell(c.DEFAULT_ATTR_DATA),v=[];let l=0;for(let g=this.lines.length-1;g>=0;g--){let u=this.lines.get(g);if(!u||!u.isWrapped&&u.getTrimmedLength()<=t)continue;const p=[u];for(;u.isWrapped&&g>0;)u=this.lines.get(--g),p.unshift(u);const C=this.ybase+this.y;if(C>=g&&C<g+p.length)continue;const b=p[p.length-1].getTrimmedLength(),y=(0,f.reflowSmallerGetNewLineLengths)(p,this._cols,t),w=y.length-p.length;let k;k=this.ybase===0&&this.y!==this.lines.length-1?Math.max(0,this.y-this.lines.maxLength+w):Math.max(0,this.lines.length-this.lines.maxLength+w);const D=[];for(let E=0;E<w;E++){const R=this.getBlankLine(c.DEFAULT_ATTR_DATA,!0);D.push(R)}D.length>0&&(v.push({start:g+p.length+l,newLines:D}),l+=D.length),p.push(...D);let P=y.length-1,T=y[P];T===0&&(P--,T=y[P]);let A=p.length-w-1,S=b;for(;A>=0;){const E=Math.min(S,T);if(p[P]===void 0)break;if(p[P].copyCellsFrom(p[A],S-E,T-E,E,!0),T-=E,T===0&&(P--,T=y[P]),S-=E,S===0){A--;const R=Math.max(A,0);S=(0,f.getWrappedLineTrimmedLength)(p,R,this._cols)}}for(let E=0;E<p.length;E++)y[E]<t&&p[E].setCell(y[E],h);let x=w-k;for(;x-- >0;)this.ybase===0?this.y<i-1?(this.y++,this.lines.pop()):(this.ybase++,this.ydisp++):this.ybase<Math.min(this.lines.maxLength,this.lines.length+l)-i&&(this.ybase===this.ydisp&&this.ydisp++,this.ybase++);this.savedY=Math.min(this.savedY+w,this.ybase+i-1)}if(v.length>0){const g=[],u=[];for(let P=0;P<this.lines.length;P++)u.push(this.lines.get(P));const p=this.lines.length;let C=p-1,b=0,y=v[b];this.lines.length=Math.min(this.lines.maxLength,this.lines.length+l);let w=0;for(let P=Math.min(this.lines.maxLength-1,p+l-1);P>=0;P--)if(y&&y.start>C+w){for(let T=y.newLines.length-1;T>=0;T--)this.lines.set(P--,y.newLines[T]);P++,g.push({index:C+1,amount:y.newLines.length}),w+=y.newLines.length,y=v[++b]}else this.lines.set(P,u[C--]);let k=0;for(let P=g.length-1;P>=0;P--)g[P].index+=k,this.lines.onInsertEmitter.fire(g[P]),k+=g[P].amount;const D=Math.max(0,p+l-this.lines.maxLength);D>0&&this.lines.onTrimEmitter.fire(D)}}translateBufferLineToString(t,i,h=0,v){const l=this.lines.get(t);return l?l.translateToString(i,h,v):""}getWrappedRangeForLine(t){let i=t,h=t;for(;i>0&&this.lines.get(i).isWrapped;)i--;for(;h+1<this.lines.length&&this.lines.get(h+1).isWrapped;)h++;return{first:i,last:h}}setupTabStops(t){for(t!=null?this.tabs[t]||(t=this.prevStop(t)):(this.tabs={},t=0);t<this._cols;t+=this._optionsService.rawOptions.tabStopWidth)this.tabs[t]=!0}prevStop(t){for(t==null&&(t=this.x);!this.tabs[--t]&&t>0;);return t>=this._cols?this._cols-1:t<0?0:t}nextStop(t){for(t==null&&(t=this.x);!this.tabs[++t]&&t<this._cols;);return t>=this._cols?this._cols-1:t<0?0:t}clearMarkers(t){this._isClearing=!0;for(let i=0;i<this.markers.length;i++)this.markers[i].line===t&&(this.markers[i].dispose(),this.markers.splice(i--,1));this._isClearing=!1}clearAllMarkers(){this._isClearing=!0;for(let t=0;t<this.markers.length;t++)this.markers[t].dispose(),this.markers.splice(t--,1);this._isClearing=!1}addMarker(t){const i=new e.Marker(t);return this.markers.push(i),i.register(this.lines.onTrim(h=>{i.line-=h,i.line<0&&i.dispose()})),i.register(this.lines.onInsert(h=>{i.line>=h.index&&(i.line+=h.amount)})),i.register(this.lines.onDelete(h=>{i.line>=h.index&&i.line<h.index+h.amount&&i.dispose(),i.line>h.index&&(i.line-=h.amount)})),i.register(i.onDispose(()=>this._removeMarker(i))),i}_removeMarker(t){this._isClearing||this.markers.splice(this.markers.indexOf(t),1)}}},8437:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferLine=s.DEFAULT_ATTR_DATA=void 0;const _=a(3734),d=a(511),n=a(643),c=a(482);s.DEFAULT_ATTR_DATA=Object.freeze(new _.AttributeData);let f=0;class m{constructor(e,r,t=!1){this.isWrapped=t,this._combined={},this._extendedAttrs={},this._data=new Uint32Array(3*e);const i=r||d.CellData.fromCharData([0,n.NULL_CELL_CHAR,n.NULL_CELL_WIDTH,n.NULL_CELL_CODE]);for(let h=0;h<e;++h)this.setCell(h,i);this.length=e}get(e){const r=this._data[3*e+0],t=2097151&r;return[this._data[3*e+1],2097152&r?this._combined[e]:t?(0,c.stringFromCodePoint)(t):"",r>>22,2097152&r?this._combined[e].charCodeAt(this._combined[e].length-1):t]}set(e,r){this._data[3*e+1]=r[n.CHAR_DATA_ATTR_INDEX],r[n.CHAR_DATA_CHAR_INDEX].length>1?(this._combined[e]=r[1],this._data[3*e+0]=2097152|e|r[n.CHAR_DATA_WIDTH_INDEX]<<22):this._data[3*e+0]=r[n.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|r[n.CHAR_DATA_WIDTH_INDEX]<<22}getWidth(e){return this._data[3*e+0]>>22}hasWidth(e){return 12582912&this._data[3*e+0]}getFg(e){return this._data[3*e+1]}getBg(e){return this._data[3*e+2]}hasContent(e){return 4194303&this._data[3*e+0]}getCodePoint(e){const r=this._data[3*e+0];return 2097152&r?this._combined[e].charCodeAt(this._combined[e].length-1):2097151&r}isCombined(e){return 2097152&this._data[3*e+0]}getString(e){const r=this._data[3*e+0];return 2097152&r?this._combined[e]:2097151&r?(0,c.stringFromCodePoint)(2097151&r):""}isProtected(e){return 536870912&this._data[3*e+2]}loadCell(e,r){return f=3*e,r.content=this._data[f+0],r.fg=this._data[f+1],r.bg=this._data[f+2],2097152&r.content&&(r.combinedData=this._combined[e]),268435456&r.bg&&(r.extended=this._extendedAttrs[e]),r}setCell(e,r){2097152&r.content&&(this._combined[e]=r.combinedData),268435456&r.bg&&(this._extendedAttrs[e]=r.extended),this._data[3*e+0]=r.content,this._data[3*e+1]=r.fg,this._data[3*e+2]=r.bg}setCellFromCodepoint(e,r,t,i){268435456&i.bg&&(this._extendedAttrs[e]=i.extended),this._data[3*e+0]=r|t<<22,this._data[3*e+1]=i.fg,this._data[3*e+2]=i.bg}addCodepointToCell(e,r,t){let i=this._data[3*e+0];2097152&i?this._combined[e]+=(0,c.stringFromCodePoint)(r):2097151&i?(this._combined[e]=(0,c.stringFromCodePoint)(2097151&i)+(0,c.stringFromCodePoint)(r),i&=-2097152,i|=2097152):i=r|4194304,t&&(i&=-12582913,i|=t<<22),this._data[3*e+0]=i}insertCells(e,r,t){if((e%=this.length)&&this.getWidth(e-1)===2&&this.setCellFromCodepoint(e-1,0,1,t),r<this.length-e){const i=new d.CellData;for(let h=this.length-e-r-1;h>=0;--h)this.setCell(e+r+h,this.loadCell(e+h,i));for(let h=0;h<r;++h)this.setCell(e+h,t)}else for(let i=e;i<this.length;++i)this.setCell(i,t);this.getWidth(this.length-1)===2&&this.setCellFromCodepoint(this.length-1,0,1,t)}deleteCells(e,r,t){if(e%=this.length,r<this.length-e){const i=new d.CellData;for(let h=0;h<this.length-e-r;++h)this.setCell(e+h,this.loadCell(e+r+h,i));for(let h=this.length-r;h<this.length;++h)this.setCell(h,t)}else for(let i=e;i<this.length;++i)this.setCell(i,t);e&&this.getWidth(e-1)===2&&this.setCellFromCodepoint(e-1,0,1,t),this.getWidth(e)!==0||this.hasContent(e)||this.setCellFromCodepoint(e,0,1,t)}replaceCells(e,r,t,i=!1){if(i)for(e&&this.getWidth(e-1)===2&&!this.isProtected(e-1)&&this.setCellFromCodepoint(e-1,0,1,t),r<this.length&&this.getWidth(r-1)===2&&!this.isProtected(r)&&this.setCellFromCodepoint(r,0,1,t);e<r&&e<this.length;)this.isProtected(e)||this.setCell(e,t),e++;else for(e&&this.getWidth(e-1)===2&&this.setCellFromCodepoint(e-1,0,1,t),r<this.length&&this.getWidth(r-1)===2&&this.setCellFromCodepoint(r,0,1,t);e<r&&e<this.length;)this.setCell(e++,t)}resize(e,r){if(e===this.length)return 4*this._data.length*2<this._data.buffer.byteLength;const t=3*e;if(e>this.length){if(this._data.buffer.byteLength>=4*t)this._data=new Uint32Array(this._data.buffer,0,t);else{const i=new Uint32Array(t);i.set(this._data),this._data=i}for(let i=this.length;i<e;++i)this.setCell(i,r)}else{this._data=this._data.subarray(0,t);const i=Object.keys(this._combined);for(let v=0;v<i.length;v++){const l=parseInt(i[v],10);l>=e&&delete this._combined[l]}const h=Object.keys(this._extendedAttrs);for(let v=0;v<h.length;v++){const l=parseInt(h[v],10);l>=e&&delete this._extendedAttrs[l]}}return this.length=e,4*t*2<this._data.buffer.byteLength}cleanupMemory(){if(4*this._data.length*2<this._data.buffer.byteLength){const e=new Uint32Array(this._data.length);return e.set(this._data),this._data=e,1}return 0}fill(e,r=!1){if(r)for(let t=0;t<this.length;++t)this.isProtected(t)||this.setCell(t,e);else{this._combined={},this._extendedAttrs={};for(let t=0;t<this.length;++t)this.setCell(t,e)}}copyFrom(e){this.length!==e.length?this._data=new Uint32Array(e._data):this._data.set(e._data),this.length=e.length,this._combined={};for(const r in e._combined)this._combined[r]=e._combined[r];this._extendedAttrs={};for(const r in e._extendedAttrs)this._extendedAttrs[r]=e._extendedAttrs[r];this.isWrapped=e.isWrapped}clone(){const e=new m(0);e._data=new Uint32Array(this._data),e.length=this.length;for(const r in this._combined)e._combined[r]=this._combined[r];for(const r in this._extendedAttrs)e._extendedAttrs[r]=this._extendedAttrs[r];return e.isWrapped=this.isWrapped,e}getTrimmedLength(){for(let e=this.length-1;e>=0;--e)if(4194303&this._data[3*e+0])return e+(this._data[3*e+0]>>22);return 0}getNoBgTrimmedLength(){for(let e=this.length-1;e>=0;--e)if(4194303&this._data[3*e+0]||50331648&this._data[3*e+2])return e+(this._data[3*e+0]>>22);return 0}copyCellsFrom(e,r,t,i,h){const v=e._data;if(h)for(let g=i-1;g>=0;g--){for(let u=0;u<3;u++)this._data[3*(t+g)+u]=v[3*(r+g)+u];268435456&v[3*(r+g)+2]&&(this._extendedAttrs[t+g]=e._extendedAttrs[r+g])}else for(let g=0;g<i;g++){for(let u=0;u<3;u++)this._data[3*(t+g)+u]=v[3*(r+g)+u];268435456&v[3*(r+g)+2]&&(this._extendedAttrs[t+g]=e._extendedAttrs[r+g])}const l=Object.keys(e._combined);for(let g=0;g<l.length;g++){const u=parseInt(l[g],10);u>=r&&(this._combined[u-r+t]=e._combined[u])}}translateToString(e,r,t,i){r=r??0,t=t??this.length,e&&(t=Math.min(t,this.getTrimmedLength())),i&&(i.length=0);let h="";for(;r<t;){const v=this._data[3*r+0],l=2097151&v,g=2097152&v?this._combined[r]:l?(0,c.stringFromCodePoint)(l):n.WHITESPACE_CELL_CHAR;if(h+=g,i)for(let u=0;u<g.length;++u)i.push(r);r+=v>>22||1}return i&&i.push(r),h}}s.BufferLine=m},4841:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.getRangeLength=void 0,s.getRangeLength=function(a,_){if(a.start.y>a.end.y)throw new Error(`Buffer range end (${a.end.x}, ${a.end.y}) cannot be before start (${a.start.x}, ${a.start.y})`);return _*(a.end.y-a.start.y)+(a.end.x-a.start.x+1)}},4634:(B,s)=>{function a(_,d,n){if(d===_.length-1)return _[d].getTrimmedLength();const c=!_[d].hasContent(n-1)&&_[d].getWidth(n-1)===1,f=_[d+1].getWidth(0)===2;return c&&f?n-1:n}Object.defineProperty(s,"__esModule",{value:!0}),s.getWrappedLineTrimmedLength=s.reflowSmallerGetNewLineLengths=s.reflowLargerApplyNewLayout=s.reflowLargerCreateNewLayout=s.reflowLargerGetLinesToRemove=void 0,s.reflowLargerGetLinesToRemove=function(_,d,n,c,f){const m=[];for(let o=0;o<_.length-1;o++){let e=o,r=_.get(++e);if(!r.isWrapped)continue;const t=[_.get(o)];for(;e<_.length&&r.isWrapped;)t.push(r),r=_.get(++e);if(c>=o&&c<e){o+=t.length-1;continue}let i=0,h=a(t,i,d),v=1,l=0;for(;v<t.length;){const u=a(t,v,d),p=u-l,C=n-h,b=Math.min(p,C);t[i].copyCellsFrom(t[v],l,h,b,!1),h+=b,h===n&&(i++,h=0),l+=b,l===u&&(v++,l=0),h===0&&i!==0&&t[i-1].getWidth(n-1)===2&&(t[i].copyCellsFrom(t[i-1],n-1,h++,1,!1),t[i-1].setCell(n-1,f))}t[i].replaceCells(h,n,f);let g=0;for(let u=t.length-1;u>0&&(u>i||t[u].getTrimmedLength()===0);u--)g++;g>0&&(m.push(o+t.length-g),m.push(g)),o+=t.length-1}return m},s.reflowLargerCreateNewLayout=function(_,d){const n=[];let c=0,f=d[c],m=0;for(let o=0;o<_.length;o++)if(f===o){const e=d[++c];_.onDeleteEmitter.fire({index:o-m,amount:e}),o+=e-1,m+=e,f=d[++c]}else n.push(o);return{layout:n,countRemoved:m}},s.reflowLargerApplyNewLayout=function(_,d){const n=[];for(let c=0;c<d.length;c++)n.push(_.get(d[c]));for(let c=0;c<n.length;c++)_.set(c,n[c]);_.length=d.length},s.reflowSmallerGetNewLineLengths=function(_,d,n){const c=[],f=_.map((r,t)=>a(_,t,d)).reduce((r,t)=>r+t);let m=0,o=0,e=0;for(;e<f;){if(f-e<n){c.push(f-e);break}m+=n;const r=a(_,o,d);m>r&&(m-=r,o++);const t=_[o].getWidth(m-1)===2;t&&m--;const i=t?n-1:n;c.push(i),e+=i}return c},s.getWrappedLineTrimmedLength=a},5295:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferSet=void 0;const _=a(8460),d=a(844),n=a(9092);class c extends d.Disposable{constructor(m,o){super(),this._optionsService=m,this._bufferService=o,this._onBufferActivate=this.register(new _.EventEmitter),this.onBufferActivate=this._onBufferActivate.event,this.reset(),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.resize(this._bufferService.cols,this._bufferService.rows))),this.register(this._optionsService.onSpecificOptionChange("tabStopWidth",()=>this.setupTabStops()))}reset(){this._normal=new n.Buffer(!0,this._optionsService,this._bufferService),this._normal.fillViewportRows(),this._alt=new n.Buffer(!1,this._optionsService,this._bufferService),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}),this.setupTabStops()}get alt(){return this._alt}get active(){return this._activeBuffer}get normal(){return this._normal}activateNormalBuffer(){this._activeBuffer!==this._normal&&(this._normal.x=this._alt.x,this._normal.y=this._alt.y,this._alt.clearAllMarkers(),this._alt.clear(),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}))}activateAltBuffer(m){this._activeBuffer!==this._alt&&(this._alt.fillViewportRows(m),this._alt.x=this._normal.x,this._alt.y=this._normal.y,this._activeBuffer=this._alt,this._onBufferActivate.fire({activeBuffer:this._alt,inactiveBuffer:this._normal}))}resize(m,o){this._normal.resize(m,o),this._alt.resize(m,o),this.setupTabStops(m)}setupTabStops(m){this._normal.setupTabStops(m),this._alt.setupTabStops(m)}}s.BufferSet=c},511:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CellData=void 0;const _=a(482),d=a(643),n=a(3734);class c extends n.AttributeData{constructor(){super(...arguments),this.content=0,this.fg=0,this.bg=0,this.extended=new n.ExtendedAttrs,this.combinedData=""}static fromCharData(m){const o=new c;return o.setFromCharData(m),o}isCombined(){return 2097152&this.content}getWidth(){return this.content>>22}getChars(){return 2097152&this.content?this.combinedData:2097151&this.content?(0,_.stringFromCodePoint)(2097151&this.content):""}getCode(){return this.isCombined()?this.combinedData.charCodeAt(this.combinedData.length-1):2097151&this.content}setFromCharData(m){this.fg=m[d.CHAR_DATA_ATTR_INDEX],this.bg=0;let o=!1;if(m[d.CHAR_DATA_CHAR_INDEX].length>2)o=!0;else if(m[d.CHAR_DATA_CHAR_INDEX].length===2){const e=m[d.CHAR_DATA_CHAR_INDEX].charCodeAt(0);if(55296<=e&&e<=56319){const r=m[d.CHAR_DATA_CHAR_INDEX].charCodeAt(1);56320<=r&&r<=57343?this.content=1024*(e-55296)+r-56320+65536|m[d.CHAR_DATA_WIDTH_INDEX]<<22:o=!0}else o=!0}else this.content=m[d.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|m[d.CHAR_DATA_WIDTH_INDEX]<<22;o&&(this.combinedData=m[d.CHAR_DATA_CHAR_INDEX],this.content=2097152|m[d.CHAR_DATA_WIDTH_INDEX]<<22)}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}s.CellData=c},643:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.WHITESPACE_CELL_CODE=s.WHITESPACE_CELL_WIDTH=s.WHITESPACE_CELL_CHAR=s.NULL_CELL_CODE=s.NULL_CELL_WIDTH=s.NULL_CELL_CHAR=s.CHAR_DATA_CODE_INDEX=s.CHAR_DATA_WIDTH_INDEX=s.CHAR_DATA_CHAR_INDEX=s.CHAR_DATA_ATTR_INDEX=s.DEFAULT_EXT=s.DEFAULT_ATTR=s.DEFAULT_COLOR=void 0,s.DEFAULT_COLOR=0,s.DEFAULT_ATTR=256|s.DEFAULT_COLOR<<9,s.DEFAULT_EXT=0,s.CHAR_DATA_ATTR_INDEX=0,s.CHAR_DATA_CHAR_INDEX=1,s.CHAR_DATA_WIDTH_INDEX=2,s.CHAR_DATA_CODE_INDEX=3,s.NULL_CELL_CHAR="",s.NULL_CELL_WIDTH=1,s.NULL_CELL_CODE=0,s.WHITESPACE_CELL_CHAR=" ",s.WHITESPACE_CELL_WIDTH=1,s.WHITESPACE_CELL_CODE=32},4863:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Marker=void 0;const _=a(8460),d=a(844);class n{get id(){return this._id}constructor(f){this.line=f,this.isDisposed=!1,this._disposables=[],this._id=n._nextId++,this._onDispose=this.register(new _.EventEmitter),this.onDispose=this._onDispose.event}dispose(){this.isDisposed||(this.isDisposed=!0,this.line=-1,this._onDispose.fire(),(0,d.disposeArray)(this._disposables),this._disposables.length=0)}register(f){return this._disposables.push(f),f}}s.Marker=n,n._nextId=1},7116:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DEFAULT_CHARSET=s.CHARSETS=void 0,s.CHARSETS={},s.DEFAULT_CHARSET=s.CHARSETS.B,s.CHARSETS[0]={"`":"◆",a:"▒",b:"␉",c:"␌",d:"␍",e:"␊",f:"°",g:"±",h:"␤",i:"␋",j:"┘",k:"┐",l:"┌",m:"└",n:"┼",o:"⎺",p:"⎻",q:"─",r:"⎼",s:"⎽",t:"├",u:"┤",v:"┴",w:"┬",x:"│",y:"≤",z:"≥","{":"π","|":"≠","}":"£","~":"·"},s.CHARSETS.A={"#":"£"},s.CHARSETS.B=void 0,s.CHARSETS[4]={"#":"£","@":"¾","[":"ij","\\":"½","]":"|","{":"¨","|":"f","}":"¼","~":"´"},s.CHARSETS.C=s.CHARSETS[5]={"[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},s.CHARSETS.R={"#":"£","@":"à","[":"°","\\":"ç","]":"§","{":"é","|":"ù","}":"è","~":"¨"},s.CHARSETS.Q={"@":"à","[":"â","\\":"ç","]":"ê","^":"î","`":"ô","{":"é","|":"ù","}":"è","~":"û"},s.CHARSETS.K={"@":"§","[":"Ä","\\":"Ö","]":"Ü","{":"ä","|":"ö","}":"ü","~":"ß"},s.CHARSETS.Y={"#":"£","@":"§","[":"°","\\":"ç","]":"é","`":"ù","{":"à","|":"ò","}":"è","~":"ì"},s.CHARSETS.E=s.CHARSETS[6]={"@":"Ä","[":"Æ","\\":"Ø","]":"Å","^":"Ü","`":"ä","{":"æ","|":"ø","}":"å","~":"ü"},s.CHARSETS.Z={"#":"£","@":"§","[":"¡","\\":"Ñ","]":"¿","{":"°","|":"ñ","}":"ç"},s.CHARSETS.H=s.CHARSETS[7]={"@":"É","[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},s.CHARSETS["="]={"#":"ù","@":"à","[":"é","\\":"ç","]":"ê","^":"î",_:"è","`":"ô","{":"ä","|":"ö","}":"ü","~":"û"}},2584:(B,s)=>{var a,_,d;Object.defineProperty(s,"__esModule",{value:!0}),s.C1_ESCAPED=s.C1=s.C0=void 0,function(n){n.NUL="\0",n.SOH="",n.STX="",n.ETX="",n.EOT="",n.ENQ="",n.ACK="",n.BEL="\x07",n.BS="\b",n.HT="	",n.LF=`
`,n.VT="\v",n.FF="\f",n.CR="\r",n.SO="",n.SI="",n.DLE="",n.DC1="",n.DC2="",n.DC3="",n.DC4="",n.NAK="",n.SYN="",n.ETB="",n.CAN="",n.EM="",n.SUB="",n.ESC="\x1B",n.FS="",n.GS="",n.RS="",n.US="",n.SP=" ",n.DEL=""}(a||(s.C0=a={})),function(n){n.PAD="",n.HOP="",n.BPH="",n.NBH="",n.IND="",n.NEL="",n.SSA="",n.ESA="",n.HTS="",n.HTJ="",n.VTS="",n.PLD="",n.PLU="",n.RI="",n.SS2="",n.SS3="",n.DCS="",n.PU1="",n.PU2="",n.STS="",n.CCH="",n.MW="",n.SPA="",n.EPA="",n.SOS="",n.SGCI="",n.SCI="",n.CSI="",n.ST="",n.OSC="",n.PM="",n.APC=""}(_||(s.C1=_={})),function(n){n.ST=`${a.ESC}\\`}(d||(s.C1_ESCAPED=d={}))},7399:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.evaluateKeyboardEvent=void 0;const _=a(2584),d={48:["0",")"],49:["1","!"],50:["2","@"],51:["3","#"],52:["4","$"],53:["5","%"],54:["6","^"],55:["7","&"],56:["8","*"],57:["9","("],186:[";",":"],187:["=","+"],188:[",","<"],189:["-","_"],190:[".",">"],191:["/","?"],192:["`","~"],219:["[","{"],220:["\\","|"],221:["]","}"],222:["'",'"']};s.evaluateKeyboardEvent=function(n,c,f,m){const o={type:0,cancel:!1,key:void 0},e=(n.shiftKey?1:0)|(n.altKey?2:0)|(n.ctrlKey?4:0)|(n.metaKey?8:0);switch(n.keyCode){case 0:n.key==="UIKeyInputUpArrow"?o.key=c?_.C0.ESC+"OA":_.C0.ESC+"[A":n.key==="UIKeyInputLeftArrow"?o.key=c?_.C0.ESC+"OD":_.C0.ESC+"[D":n.key==="UIKeyInputRightArrow"?o.key=c?_.C0.ESC+"OC":_.C0.ESC+"[C":n.key==="UIKeyInputDownArrow"&&(o.key=c?_.C0.ESC+"OB":_.C0.ESC+"[B");break;case 8:o.key=n.ctrlKey?"\b":_.C0.DEL,n.altKey&&(o.key=_.C0.ESC+o.key);break;case 9:if(n.shiftKey){o.key=_.C0.ESC+"[Z";break}o.key=_.C0.HT,o.cancel=!0;break;case 13:o.key=n.altKey?_.C0.ESC+_.C0.CR:_.C0.CR,o.cancel=!0;break;case 27:o.key=_.C0.ESC,n.altKey&&(o.key=_.C0.ESC+_.C0.ESC),o.cancel=!0;break;case 37:if(n.metaKey)break;e?(o.key=_.C0.ESC+"[1;"+(e+1)+"D",o.key===_.C0.ESC+"[1;3D"&&(o.key=_.C0.ESC+(f?"b":"[1;5D"))):o.key=c?_.C0.ESC+"OD":_.C0.ESC+"[D";break;case 39:if(n.metaKey)break;e?(o.key=_.C0.ESC+"[1;"+(e+1)+"C",o.key===_.C0.ESC+"[1;3C"&&(o.key=_.C0.ESC+(f?"f":"[1;5C"))):o.key=c?_.C0.ESC+"OC":_.C0.ESC+"[C";break;case 38:if(n.metaKey)break;e?(o.key=_.C0.ESC+"[1;"+(e+1)+"A",f||o.key!==_.C0.ESC+"[1;3A"||(o.key=_.C0.ESC+"[1;5A")):o.key=c?_.C0.ESC+"OA":_.C0.ESC+"[A";break;case 40:if(n.metaKey)break;e?(o.key=_.C0.ESC+"[1;"+(e+1)+"B",f||o.key!==_.C0.ESC+"[1;3B"||(o.key=_.C0.ESC+"[1;5B")):o.key=c?_.C0.ESC+"OB":_.C0.ESC+"[B";break;case 45:n.shiftKey||n.ctrlKey||(o.key=_.C0.ESC+"[2~");break;case 46:o.key=e?_.C0.ESC+"[3;"+(e+1)+"~":_.C0.ESC+"[3~";break;case 36:o.key=e?_.C0.ESC+"[1;"+(e+1)+"H":c?_.C0.ESC+"OH":_.C0.ESC+"[H";break;case 35:o.key=e?_.C0.ESC+"[1;"+(e+1)+"F":c?_.C0.ESC+"OF":_.C0.ESC+"[F";break;case 33:n.shiftKey?o.type=2:n.ctrlKey?o.key=_.C0.ESC+"[5;"+(e+1)+"~":o.key=_.C0.ESC+"[5~";break;case 34:n.shiftKey?o.type=3:n.ctrlKey?o.key=_.C0.ESC+"[6;"+(e+1)+"~":o.key=_.C0.ESC+"[6~";break;case 112:o.key=e?_.C0.ESC+"[1;"+(e+1)+"P":_.C0.ESC+"OP";break;case 113:o.key=e?_.C0.ESC+"[1;"+(e+1)+"Q":_.C0.ESC+"OQ";break;case 114:o.key=e?_.C0.ESC+"[1;"+(e+1)+"R":_.C0.ESC+"OR";break;case 115:o.key=e?_.C0.ESC+"[1;"+(e+1)+"S":_.C0.ESC+"OS";break;case 116:o.key=e?_.C0.ESC+"[15;"+(e+1)+"~":_.C0.ESC+"[15~";break;case 117:o.key=e?_.C0.ESC+"[17;"+(e+1)+"~":_.C0.ESC+"[17~";break;case 118:o.key=e?_.C0.ESC+"[18;"+(e+1)+"~":_.C0.ESC+"[18~";break;case 119:o.key=e?_.C0.ESC+"[19;"+(e+1)+"~":_.C0.ESC+"[19~";break;case 120:o.key=e?_.C0.ESC+"[20;"+(e+1)+"~":_.C0.ESC+"[20~";break;case 121:o.key=e?_.C0.ESC+"[21;"+(e+1)+"~":_.C0.ESC+"[21~";break;case 122:o.key=e?_.C0.ESC+"[23;"+(e+1)+"~":_.C0.ESC+"[23~";break;case 123:o.key=e?_.C0.ESC+"[24;"+(e+1)+"~":_.C0.ESC+"[24~";break;default:if(!n.ctrlKey||n.shiftKey||n.altKey||n.metaKey)if(f&&!m||!n.altKey||n.metaKey)!f||n.altKey||n.ctrlKey||n.shiftKey||!n.metaKey?n.key&&!n.ctrlKey&&!n.altKey&&!n.metaKey&&n.keyCode>=48&&n.key.length===1?o.key=n.key:n.key&&n.ctrlKey&&(n.key==="_"&&(o.key=_.C0.US),n.key==="@"&&(o.key=_.C0.NUL)):n.keyCode===65&&(o.type=1);else{const r=d[n.keyCode],t=r==null?void 0:r[n.shiftKey?1:0];if(t)o.key=_.C0.ESC+t;else if(n.keyCode>=65&&n.keyCode<=90){const i=n.ctrlKey?n.keyCode-64:n.keyCode+32;let h=String.fromCharCode(i);n.shiftKey&&(h=h.toUpperCase()),o.key=_.C0.ESC+h}else if(n.keyCode===32)o.key=_.C0.ESC+(n.ctrlKey?_.C0.NUL:" ");else if(n.key==="Dead"&&n.code.startsWith("Key")){let i=n.code.slice(3,4);n.shiftKey||(i=i.toLowerCase()),o.key=_.C0.ESC+i,o.cancel=!0}}else n.keyCode>=65&&n.keyCode<=90?o.key=String.fromCharCode(n.keyCode-64):n.keyCode===32?o.key=_.C0.NUL:n.keyCode>=51&&n.keyCode<=55?o.key=String.fromCharCode(n.keyCode-51+27):n.keyCode===56?o.key=_.C0.DEL:n.keyCode===219?o.key=_.C0.ESC:n.keyCode===220?o.key=_.C0.FS:n.keyCode===221&&(o.key=_.C0.GS)}return o}},482:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Utf8ToUtf32=s.StringToUtf32=s.utf32ToString=s.stringFromCodePoint=void 0,s.stringFromCodePoint=function(a){return a>65535?(a-=65536,String.fromCharCode(55296+(a>>10))+String.fromCharCode(a%1024+56320)):String.fromCharCode(a)},s.utf32ToString=function(a,_=0,d=a.length){let n="";for(let c=_;c<d;++c){let f=a[c];f>65535?(f-=65536,n+=String.fromCharCode(55296+(f>>10))+String.fromCharCode(f%1024+56320)):n+=String.fromCharCode(f)}return n},s.StringToUtf32=class{constructor(){this._interim=0}clear(){this._interim=0}decode(a,_){const d=a.length;if(!d)return 0;let n=0,c=0;if(this._interim){const f=a.charCodeAt(c++);56320<=f&&f<=57343?_[n++]=1024*(this._interim-55296)+f-56320+65536:(_[n++]=this._interim,_[n++]=f),this._interim=0}for(let f=c;f<d;++f){const m=a.charCodeAt(f);if(55296<=m&&m<=56319){if(++f>=d)return this._interim=m,n;const o=a.charCodeAt(f);56320<=o&&o<=57343?_[n++]=1024*(m-55296)+o-56320+65536:(_[n++]=m,_[n++]=o)}else m!==65279&&(_[n++]=m)}return n}},s.Utf8ToUtf32=class{constructor(){this.interim=new Uint8Array(3)}clear(){this.interim.fill(0)}decode(a,_){const d=a.length;if(!d)return 0;let n,c,f,m,o=0,e=0,r=0;if(this.interim[0]){let h=!1,v=this.interim[0];v&=(224&v)==192?31:(240&v)==224?15:7;let l,g=0;for(;(l=63&this.interim[++g])&&g<4;)v<<=6,v|=l;const u=(224&this.interim[0])==192?2:(240&this.interim[0])==224?3:4,p=u-g;for(;r<p;){if(r>=d)return 0;if(l=a[r++],(192&l)!=128){r--,h=!0;break}this.interim[g++]=l,v<<=6,v|=63&l}h||(u===2?v<128?r--:_[o++]=v:u===3?v<2048||v>=55296&&v<=57343||v===65279||(_[o++]=v):v<65536||v>1114111||(_[o++]=v)),this.interim.fill(0)}const t=d-4;let i=r;for(;i<d;){for(;!(!(i<t)||128&(n=a[i])||128&(c=a[i+1])||128&(f=a[i+2])||128&(m=a[i+3]));)_[o++]=n,_[o++]=c,_[o++]=f,_[o++]=m,i+=4;if(n=a[i++],n<128)_[o++]=n;else if((224&n)==192){if(i>=d)return this.interim[0]=n,o;if(c=a[i++],(192&c)!=128){i--;continue}if(e=(31&n)<<6|63&c,e<128){i--;continue}_[o++]=e}else if((240&n)==224){if(i>=d)return this.interim[0]=n,o;if(c=a[i++],(192&c)!=128){i--;continue}if(i>=d)return this.interim[0]=n,this.interim[1]=c,o;if(f=a[i++],(192&f)!=128){i--;continue}if(e=(15&n)<<12|(63&c)<<6|63&f,e<2048||e>=55296&&e<=57343||e===65279)continue;_[o++]=e}else if((248&n)==240){if(i>=d)return this.interim[0]=n,o;if(c=a[i++],(192&c)!=128){i--;continue}if(i>=d)return this.interim[0]=n,this.interim[1]=c,o;if(f=a[i++],(192&f)!=128){i--;continue}if(i>=d)return this.interim[0]=n,this.interim[1]=c,this.interim[2]=f,o;if(m=a[i++],(192&m)!=128){i--;continue}if(e=(7&n)<<18|(63&c)<<12|(63&f)<<6|63&m,e<65536||e>1114111)continue;_[o++]=e}}return o}}},225:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.UnicodeV6=void 0;const _=a(1480),d=[[768,879],[1155,1158],[1160,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1539],[1552,1557],[1611,1630],[1648,1648],[1750,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2305,2306],[2364,2364],[2369,2376],[2381,2381],[2385,2388],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2672,2673],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2817,2817],[2876,2876],[2879,2879],[2881,2883],[2893,2893],[2902,2902],[2946,2946],[3008,3008],[3021,3021],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3393,3395],[3405,3405],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3769],[3771,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3984,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4146],[4150,4151],[4153,4153],[4184,4185],[4448,4607],[4959,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6157],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7616,7626],[7678,7679],[8203,8207],[8234,8238],[8288,8291],[8298,8303],[8400,8431],[12330,12335],[12441,12442],[43014,43014],[43019,43019],[43045,43046],[64286,64286],[65024,65039],[65056,65059],[65279,65279],[65529,65531]],n=[[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[917505,917505],[917536,917631],[917760,917999]];let c;s.UnicodeV6=class{constructor(){if(this.version="6",!c){c=new Uint8Array(65536),c.fill(1),c[0]=0,c.fill(0,1,32),c.fill(0,127,160),c.fill(2,4352,4448),c[9001]=2,c[9002]=2,c.fill(2,11904,42192),c[12351]=1,c.fill(2,44032,55204),c.fill(2,63744,64256),c.fill(2,65040,65050),c.fill(2,65072,65136),c.fill(2,65280,65377),c.fill(2,65504,65511);for(let f=0;f<d.length;++f)c.fill(0,d[f][0],d[f][1]+1)}}wcwidth(f){return f<32?0:f<127?1:f<65536?c[f]:function(m,o){let e,r=0,t=o.length-1;if(m<o[0][0]||m>o[t][1])return!1;for(;t>=r;)if(e=r+t>>1,m>o[e][1])r=e+1;else{if(!(m<o[e][0]))return!0;t=e-1}return!1}(f,n)?0:f>=131072&&f<=196605||f>=196608&&f<=262141?2:1}charProperties(f,m){let o=this.wcwidth(f),e=o===0&&m!==0;if(e){const r=_.UnicodeService.extractWidth(m);r===0?e=!1:r>o&&(o=r)}return _.UnicodeService.createPropertyValue(0,o,e)}}},5981:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.WriteBuffer=void 0;const _=a(8460),d=a(844);class n extends d.Disposable{constructor(f){super(),this._action=f,this._writeBuffer=[],this._callbacks=[],this._pendingData=0,this._bufferOffset=0,this._isSyncWriting=!1,this._syncCalls=0,this._didUserInput=!1,this._onWriteParsed=this.register(new _.EventEmitter),this.onWriteParsed=this._onWriteParsed.event}handleUserInput(){this._didUserInput=!0}writeSync(f,m){if(m!==void 0&&this._syncCalls>m)return void(this._syncCalls=0);if(this._pendingData+=f.length,this._writeBuffer.push(f),this._callbacks.push(void 0),this._syncCalls++,this._isSyncWriting)return;let o;for(this._isSyncWriting=!0;o=this._writeBuffer.shift();){this._action(o);const e=this._callbacks.shift();e&&e()}this._pendingData=0,this._bufferOffset=2147483647,this._isSyncWriting=!1,this._syncCalls=0}write(f,m){if(this._pendingData>5e7)throw new Error("write data discarded, use flow control to avoid losing data");if(!this._writeBuffer.length){if(this._bufferOffset=0,this._didUserInput)return this._didUserInput=!1,this._pendingData+=f.length,this._writeBuffer.push(f),this._callbacks.push(m),void this._innerWrite();setTimeout(()=>this._innerWrite())}this._pendingData+=f.length,this._writeBuffer.push(f),this._callbacks.push(m)}_innerWrite(f=0,m=!0){const o=f||Date.now();for(;this._writeBuffer.length>this._bufferOffset;){const e=this._writeBuffer[this._bufferOffset],r=this._action(e,m);if(r){const i=h=>Date.now()-o>=12?setTimeout(()=>this._innerWrite(0,h)):this._innerWrite(o,h);return void r.catch(h=>(queueMicrotask(()=>{throw h}),Promise.resolve(!1))).then(i)}const t=this._callbacks[this._bufferOffset];if(t&&t(),this._bufferOffset++,this._pendingData-=e.length,Date.now()-o>=12)break}this._writeBuffer.length>this._bufferOffset?(this._bufferOffset>50&&(this._writeBuffer=this._writeBuffer.slice(this._bufferOffset),this._callbacks=this._callbacks.slice(this._bufferOffset),this._bufferOffset=0),setTimeout(()=>this._innerWrite())):(this._writeBuffer.length=0,this._callbacks.length=0,this._pendingData=0,this._bufferOffset=0),this._onWriteParsed.fire()}}s.WriteBuffer=n},5941:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.toRgbString=s.parseColor=void 0;const a=/^([\da-f])\/([\da-f])\/([\da-f])$|^([\da-f]{2})\/([\da-f]{2})\/([\da-f]{2})$|^([\da-f]{3})\/([\da-f]{3})\/([\da-f]{3})$|^([\da-f]{4})\/([\da-f]{4})\/([\da-f]{4})$/,_=/^[\da-f]+$/;function d(n,c){const f=n.toString(16),m=f.length<2?"0"+f:f;switch(c){case 4:return f[0];case 8:return m;case 12:return(m+m).slice(0,3);default:return m+m}}s.parseColor=function(n){if(!n)return;let c=n.toLowerCase();if(c.indexOf("rgb:")===0){c=c.slice(4);const f=a.exec(c);if(f){const m=f[1]?15:f[4]?255:f[7]?4095:65535;return[Math.round(parseInt(f[1]||f[4]||f[7]||f[10],16)/m*255),Math.round(parseInt(f[2]||f[5]||f[8]||f[11],16)/m*255),Math.round(parseInt(f[3]||f[6]||f[9]||f[12],16)/m*255)]}}else if(c.indexOf("#")===0&&(c=c.slice(1),_.exec(c)&&[3,6,9,12].includes(c.length))){const f=c.length/3,m=[0,0,0];for(let o=0;o<3;++o){const e=parseInt(c.slice(f*o,f*o+f),16);m[o]=f===1?e<<4:f===2?e:f===3?e>>4:e>>8}return m}},s.toRgbString=function(n,c=16){const[f,m,o]=n;return`rgb:${d(f,c)}/${d(m,c)}/${d(o,c)}`}},5770:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.PAYLOAD_LIMIT=void 0,s.PAYLOAD_LIMIT=1e7},6351:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DcsHandler=s.DcsParser=void 0;const _=a(482),d=a(8742),n=a(5770),c=[];s.DcsParser=class{constructor(){this._handlers=Object.create(null),this._active=c,this._ident=0,this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=c}registerHandler(m,o){this._handlers[m]===void 0&&(this._handlers[m]=[]);const e=this._handlers[m];return e.push(o),{dispose:()=>{const r=e.indexOf(o);r!==-1&&e.splice(r,1)}}}clearHandler(m){this._handlers[m]&&delete this._handlers[m]}setHandlerFallback(m){this._handlerFb=m}reset(){if(this._active.length)for(let m=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;m>=0;--m)this._active[m].unhook(!1);this._stack.paused=!1,this._active=c,this._ident=0}hook(m,o){if(this.reset(),this._ident=m,this._active=this._handlers[m]||c,this._active.length)for(let e=this._active.length-1;e>=0;e--)this._active[e].hook(o);else this._handlerFb(this._ident,"HOOK",o)}put(m,o,e){if(this._active.length)for(let r=this._active.length-1;r>=0;r--)this._active[r].put(m,o,e);else this._handlerFb(this._ident,"PUT",(0,_.utf32ToString)(m,o,e))}unhook(m,o=!0){if(this._active.length){let e=!1,r=this._active.length-1,t=!1;if(this._stack.paused&&(r=this._stack.loopPosition-1,e=o,t=this._stack.fallThrough,this._stack.paused=!1),!t&&e===!1){for(;r>=0&&(e=this._active[r].unhook(m),e!==!0);r--)if(e instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=r,this._stack.fallThrough=!1,e;r--}for(;r>=0;r--)if(e=this._active[r].unhook(!1),e instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=r,this._stack.fallThrough=!0,e}else this._handlerFb(this._ident,"UNHOOK",m);this._active=c,this._ident=0}};const f=new d.Params;f.addParam(0),s.DcsHandler=class{constructor(m){this._handler=m,this._data="",this._params=f,this._hitLimit=!1}hook(m){this._params=m.length>1||m.params[0]?m.clone():f,this._data="",this._hitLimit=!1}put(m,o,e){this._hitLimit||(this._data+=(0,_.utf32ToString)(m,o,e),this._data.length>n.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}unhook(m){let o=!1;if(this._hitLimit)o=!1;else if(m&&(o=this._handler(this._data,this._params),o instanceof Promise))return o.then(e=>(this._params=f,this._data="",this._hitLimit=!1,e));return this._params=f,this._data="",this._hitLimit=!1,o}}},2015:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.EscapeSequenceParser=s.VT500_TRANSITION_TABLE=s.TransitionTable=void 0;const _=a(844),d=a(8742),n=a(6242),c=a(6351);class f{constructor(r){this.table=new Uint8Array(r)}setDefault(r,t){this.table.fill(r<<4|t)}add(r,t,i,h){this.table[t<<8|r]=i<<4|h}addMany(r,t,i,h){for(let v=0;v<r.length;v++)this.table[t<<8|r[v]]=i<<4|h}}s.TransitionTable=f;const m=160;s.VT500_TRANSITION_TABLE=function(){const e=new f(4095),r=Array.apply(null,Array(256)).map((g,u)=>u),t=(g,u)=>r.slice(g,u),i=t(32,127),h=t(0,24);h.push(25),h.push.apply(h,t(28,32));const v=t(0,14);let l;for(l in e.setDefault(1,0),e.addMany(i,0,2,0),v)e.addMany([24,26,153,154],l,3,0),e.addMany(t(128,144),l,3,0),e.addMany(t(144,152),l,3,0),e.add(156,l,0,0),e.add(27,l,11,1),e.add(157,l,4,8),e.addMany([152,158,159],l,0,7),e.add(155,l,11,3),e.add(144,l,11,9);return e.addMany(h,0,3,0),e.addMany(h,1,3,1),e.add(127,1,0,1),e.addMany(h,8,0,8),e.addMany(h,3,3,3),e.add(127,3,0,3),e.addMany(h,4,3,4),e.add(127,4,0,4),e.addMany(h,6,3,6),e.addMany(h,5,3,5),e.add(127,5,0,5),e.addMany(h,2,3,2),e.add(127,2,0,2),e.add(93,1,4,8),e.addMany(i,8,5,8),e.add(127,8,5,8),e.addMany([156,27,24,26,7],8,6,0),e.addMany(t(28,32),8,0,8),e.addMany([88,94,95],1,0,7),e.addMany(i,7,0,7),e.addMany(h,7,0,7),e.add(156,7,0,0),e.add(127,7,0,7),e.add(91,1,11,3),e.addMany(t(64,127),3,7,0),e.addMany(t(48,60),3,8,4),e.addMany([60,61,62,63],3,9,4),e.addMany(t(48,60),4,8,4),e.addMany(t(64,127),4,7,0),e.addMany([60,61,62,63],4,0,6),e.addMany(t(32,64),6,0,6),e.add(127,6,0,6),e.addMany(t(64,127),6,0,0),e.addMany(t(32,48),3,9,5),e.addMany(t(32,48),5,9,5),e.addMany(t(48,64),5,0,6),e.addMany(t(64,127),5,7,0),e.addMany(t(32,48),4,9,5),e.addMany(t(32,48),1,9,2),e.addMany(t(32,48),2,9,2),e.addMany(t(48,127),2,10,0),e.addMany(t(48,80),1,10,0),e.addMany(t(81,88),1,10,0),e.addMany([89,90,92],1,10,0),e.addMany(t(96,127),1,10,0),e.add(80,1,11,9),e.addMany(h,9,0,9),e.add(127,9,0,9),e.addMany(t(28,32),9,0,9),e.addMany(t(32,48),9,9,12),e.addMany(t(48,60),9,8,10),e.addMany([60,61,62,63],9,9,10),e.addMany(h,11,0,11),e.addMany(t(32,128),11,0,11),e.addMany(t(28,32),11,0,11),e.addMany(h,10,0,10),e.add(127,10,0,10),e.addMany(t(28,32),10,0,10),e.addMany(t(48,60),10,8,10),e.addMany([60,61,62,63],10,0,11),e.addMany(t(32,48),10,9,12),e.addMany(h,12,0,12),e.add(127,12,0,12),e.addMany(t(28,32),12,0,12),e.addMany(t(32,48),12,9,12),e.addMany(t(48,64),12,0,11),e.addMany(t(64,127),12,12,13),e.addMany(t(64,127),10,12,13),e.addMany(t(64,127),9,12,13),e.addMany(h,13,13,13),e.addMany(i,13,13,13),e.add(127,13,0,13),e.addMany([27,156,24,26],13,14,0),e.add(m,0,2,0),e.add(m,8,5,8),e.add(m,6,0,6),e.add(m,11,0,11),e.add(m,13,13,13),e}();class o extends _.Disposable{constructor(r=s.VT500_TRANSITION_TABLE){super(),this._transitions=r,this._parseStack={state:0,handlers:[],handlerPos:0,transition:0,chunkPos:0},this.initialState=0,this.currentState=this.initialState,this._params=new d.Params,this._params.addParam(0),this._collect=0,this.precedingJoinState=0,this._printHandlerFb=(t,i,h)=>{},this._executeHandlerFb=t=>{},this._csiHandlerFb=(t,i)=>{},this._escHandlerFb=t=>{},this._errorHandlerFb=t=>t,this._printHandler=this._printHandlerFb,this._executeHandlers=Object.create(null),this._csiHandlers=Object.create(null),this._escHandlers=Object.create(null),this.register((0,_.toDisposable)(()=>{this._csiHandlers=Object.create(null),this._executeHandlers=Object.create(null),this._escHandlers=Object.create(null)})),this._oscParser=this.register(new n.OscParser),this._dcsParser=this.register(new c.DcsParser),this._errorHandler=this._errorHandlerFb,this.registerEscHandler({final:"\\"},()=>!0)}_identifier(r,t=[64,126]){let i=0;if(r.prefix){if(r.prefix.length>1)throw new Error("only one byte as prefix supported");if(i=r.prefix.charCodeAt(0),i&&60>i||i>63)throw new Error("prefix must be in range 0x3c .. 0x3f")}if(r.intermediates){if(r.intermediates.length>2)throw new Error("only two bytes as intermediates are supported");for(let v=0;v<r.intermediates.length;++v){const l=r.intermediates.charCodeAt(v);if(32>l||l>47)throw new Error("intermediate must be in range 0x20 .. 0x2f");i<<=8,i|=l}}if(r.final.length!==1)throw new Error("final must be a single byte");const h=r.final.charCodeAt(0);if(t[0]>h||h>t[1])throw new Error(`final must be in range ${t[0]} .. ${t[1]}`);return i<<=8,i|=h,i}identToString(r){const t=[];for(;r;)t.push(String.fromCharCode(255&r)),r>>=8;return t.reverse().join("")}setPrintHandler(r){this._printHandler=r}clearPrintHandler(){this._printHandler=this._printHandlerFb}registerEscHandler(r,t){const i=this._identifier(r,[48,126]);this._escHandlers[i]===void 0&&(this._escHandlers[i]=[]);const h=this._escHandlers[i];return h.push(t),{dispose:()=>{const v=h.indexOf(t);v!==-1&&h.splice(v,1)}}}clearEscHandler(r){this._escHandlers[this._identifier(r,[48,126])]&&delete this._escHandlers[this._identifier(r,[48,126])]}setEscHandlerFallback(r){this._escHandlerFb=r}setExecuteHandler(r,t){this._executeHandlers[r.charCodeAt(0)]=t}clearExecuteHandler(r){this._executeHandlers[r.charCodeAt(0)]&&delete this._executeHandlers[r.charCodeAt(0)]}setExecuteHandlerFallback(r){this._executeHandlerFb=r}registerCsiHandler(r,t){const i=this._identifier(r);this._csiHandlers[i]===void 0&&(this._csiHandlers[i]=[]);const h=this._csiHandlers[i];return h.push(t),{dispose:()=>{const v=h.indexOf(t);v!==-1&&h.splice(v,1)}}}clearCsiHandler(r){this._csiHandlers[this._identifier(r)]&&delete this._csiHandlers[this._identifier(r)]}setCsiHandlerFallback(r){this._csiHandlerFb=r}registerDcsHandler(r,t){return this._dcsParser.registerHandler(this._identifier(r),t)}clearDcsHandler(r){this._dcsParser.clearHandler(this._identifier(r))}setDcsHandlerFallback(r){this._dcsParser.setHandlerFallback(r)}registerOscHandler(r,t){return this._oscParser.registerHandler(r,t)}clearOscHandler(r){this._oscParser.clearHandler(r)}setOscHandlerFallback(r){this._oscParser.setHandlerFallback(r)}setErrorHandler(r){this._errorHandler=r}clearErrorHandler(){this._errorHandler=this._errorHandlerFb}reset(){this.currentState=this.initialState,this._oscParser.reset(),this._dcsParser.reset(),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingJoinState=0,this._parseStack.state!==0&&(this._parseStack.state=2,this._parseStack.handlers=[])}_preserveStack(r,t,i,h,v){this._parseStack.state=r,this._parseStack.handlers=t,this._parseStack.handlerPos=i,this._parseStack.transition=h,this._parseStack.chunkPos=v}parse(r,t,i){let h,v=0,l=0,g=0;if(this._parseStack.state)if(this._parseStack.state===2)this._parseStack.state=0,g=this._parseStack.chunkPos+1;else{if(i===void 0||this._parseStack.state===1)throw this._parseStack.state=1,new Error("improper continuation due to previous async handler, giving up parsing");const u=this._parseStack.handlers;let p=this._parseStack.handlerPos-1;switch(this._parseStack.state){case 3:if(i===!1&&p>-1){for(;p>=0&&(h=u[p](this._params),h!==!0);p--)if(h instanceof Promise)return this._parseStack.handlerPos=p,h}this._parseStack.handlers=[];break;case 4:if(i===!1&&p>-1){for(;p>=0&&(h=u[p](),h!==!0);p--)if(h instanceof Promise)return this._parseStack.handlerPos=p,h}this._parseStack.handlers=[];break;case 6:if(v=r[this._parseStack.chunkPos],h=this._dcsParser.unhook(v!==24&&v!==26,i),h)return h;v===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0;break;case 5:if(v=r[this._parseStack.chunkPos],h=this._oscParser.end(v!==24&&v!==26,i),h)return h;v===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0}this._parseStack.state=0,g=this._parseStack.chunkPos+1,this.precedingJoinState=0,this.currentState=15&this._parseStack.transition}for(let u=g;u<t;++u){switch(v=r[u],l=this._transitions.table[this.currentState<<8|(v<160?v:m)],l>>4){case 2:for(let w=u+1;;++w){if(w>=t||(v=r[w])<32||v>126&&v<m){this._printHandler(r,u,w),u=w-1;break}if(++w>=t||(v=r[w])<32||v>126&&v<m){this._printHandler(r,u,w),u=w-1;break}if(++w>=t||(v=r[w])<32||v>126&&v<m){this._printHandler(r,u,w),u=w-1;break}if(++w>=t||(v=r[w])<32||v>126&&v<m){this._printHandler(r,u,w),u=w-1;break}}break;case 3:this._executeHandlers[v]?this._executeHandlers[v]():this._executeHandlerFb(v),this.precedingJoinState=0;break;case 0:break;case 1:if(this._errorHandler({position:u,code:v,currentState:this.currentState,collect:this._collect,params:this._params,abort:!1}).abort)return;break;case 7:const p=this._csiHandlers[this._collect<<8|v];let C=p?p.length-1:-1;for(;C>=0&&(h=p[C](this._params),h!==!0);C--)if(h instanceof Promise)return this._preserveStack(3,p,C,l,u),h;C<0&&this._csiHandlerFb(this._collect<<8|v,this._params),this.precedingJoinState=0;break;case 8:do switch(v){case 59:this._params.addParam(0);break;case 58:this._params.addSubParam(-1);break;default:this._params.addDigit(v-48)}while(++u<t&&(v=r[u])>47&&v<60);u--;break;case 9:this._collect<<=8,this._collect|=v;break;case 10:const b=this._escHandlers[this._collect<<8|v];let y=b?b.length-1:-1;for(;y>=0&&(h=b[y](),h!==!0);y--)if(h instanceof Promise)return this._preserveStack(4,b,y,l,u),h;y<0&&this._escHandlerFb(this._collect<<8|v),this.precedingJoinState=0;break;case 11:this._params.reset(),this._params.addParam(0),this._collect=0;break;case 12:this._dcsParser.hook(this._collect<<8|v,this._params);break;case 13:for(let w=u+1;;++w)if(w>=t||(v=r[w])===24||v===26||v===27||v>127&&v<m){this._dcsParser.put(r,u,w),u=w-1;break}break;case 14:if(h=this._dcsParser.unhook(v!==24&&v!==26),h)return this._preserveStack(6,[],0,l,u),h;v===27&&(l|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingJoinState=0;break;case 4:this._oscParser.start();break;case 5:for(let w=u+1;;w++)if(w>=t||(v=r[w])<32||v>127&&v<m){this._oscParser.put(r,u,w),u=w-1;break}break;case 6:if(h=this._oscParser.end(v!==24&&v!==26),h)return this._preserveStack(5,[],0,l,u),h;v===27&&(l|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingJoinState=0}this.currentState=15&l}}}s.EscapeSequenceParser=o},6242:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.OscHandler=s.OscParser=void 0;const _=a(5770),d=a(482),n=[];s.OscParser=class{constructor(){this._state=0,this._active=n,this._id=-1,this._handlers=Object.create(null),this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}registerHandler(c,f){this._handlers[c]===void 0&&(this._handlers[c]=[]);const m=this._handlers[c];return m.push(f),{dispose:()=>{const o=m.indexOf(f);o!==-1&&m.splice(o,1)}}}clearHandler(c){this._handlers[c]&&delete this._handlers[c]}setHandlerFallback(c){this._handlerFb=c}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=n}reset(){if(this._state===2)for(let c=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;c>=0;--c)this._active[c].end(!1);this._stack.paused=!1,this._active=n,this._id=-1,this._state=0}_start(){if(this._active=this._handlers[this._id]||n,this._active.length)for(let c=this._active.length-1;c>=0;c--)this._active[c].start();else this._handlerFb(this._id,"START")}_put(c,f,m){if(this._active.length)for(let o=this._active.length-1;o>=0;o--)this._active[o].put(c,f,m);else this._handlerFb(this._id,"PUT",(0,d.utf32ToString)(c,f,m))}start(){this.reset(),this._state=1}put(c,f,m){if(this._state!==3){if(this._state===1)for(;f<m;){const o=c[f++];if(o===59){this._state=2,this._start();break}if(o<48||57<o)return void(this._state=3);this._id===-1&&(this._id=0),this._id=10*this._id+o-48}this._state===2&&m-f>0&&this._put(c,f,m)}}end(c,f=!0){if(this._state!==0){if(this._state!==3)if(this._state===1&&this._start(),this._active.length){let m=!1,o=this._active.length-1,e=!1;if(this._stack.paused&&(o=this._stack.loopPosition-1,m=f,e=this._stack.fallThrough,this._stack.paused=!1),!e&&m===!1){for(;o>=0&&(m=this._active[o].end(c),m!==!0);o--)if(m instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=o,this._stack.fallThrough=!1,m;o--}for(;o>=0;o--)if(m=this._active[o].end(!1),m instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=o,this._stack.fallThrough=!0,m}else this._handlerFb(this._id,"END",c);this._active=n,this._id=-1,this._state=0}}},s.OscHandler=class{constructor(c){this._handler=c,this._data="",this._hitLimit=!1}start(){this._data="",this._hitLimit=!1}put(c,f,m){this._hitLimit||(this._data+=(0,d.utf32ToString)(c,f,m),this._data.length>_.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}end(c){let f=!1;if(this._hitLimit)f=!1;else if(c&&(f=this._handler(this._data),f instanceof Promise))return f.then(m=>(this._data="",this._hitLimit=!1,m));return this._data="",this._hitLimit=!1,f}}},8742:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Params=void 0;const a=2147483647;class _{static fromArray(n){const c=new _;if(!n.length)return c;for(let f=Array.isArray(n[0])?1:0;f<n.length;++f){const m=n[f];if(Array.isArray(m))for(let o=0;o<m.length;++o)c.addSubParam(m[o]);else c.addParam(m)}return c}constructor(n=32,c=32){if(this.maxLength=n,this.maxSubParamsLength=c,c>256)throw new Error("maxSubParamsLength must not be greater than 256");this.params=new Int32Array(n),this.length=0,this._subParams=new Int32Array(c),this._subParamsLength=0,this._subParamsIdx=new Uint16Array(n),this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}clone(){const n=new _(this.maxLength,this.maxSubParamsLength);return n.params.set(this.params),n.length=this.length,n._subParams.set(this._subParams),n._subParamsLength=this._subParamsLength,n._subParamsIdx.set(this._subParamsIdx),n._rejectDigits=this._rejectDigits,n._rejectSubDigits=this._rejectSubDigits,n._digitIsSub=this._digitIsSub,n}toArray(){const n=[];for(let c=0;c<this.length;++c){n.push(this.params[c]);const f=this._subParamsIdx[c]>>8,m=255&this._subParamsIdx[c];m-f>0&&n.push(Array.prototype.slice.call(this._subParams,f,m))}return n}reset(){this.length=0,this._subParamsLength=0,this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}addParam(n){if(this._digitIsSub=!1,this.length>=this.maxLength)this._rejectDigits=!0;else{if(n<-1)throw new Error("values lesser than -1 are not allowed");this._subParamsIdx[this.length]=this._subParamsLength<<8|this._subParamsLength,this.params[this.length++]=n>a?a:n}}addSubParam(n){if(this._digitIsSub=!0,this.length)if(this._rejectDigits||this._subParamsLength>=this.maxSubParamsLength)this._rejectSubDigits=!0;else{if(n<-1)throw new Error("values lesser than -1 are not allowed");this._subParams[this._subParamsLength++]=n>a?a:n,this._subParamsIdx[this.length-1]++}}hasSubParams(n){return(255&this._subParamsIdx[n])-(this._subParamsIdx[n]>>8)>0}getSubParams(n){const c=this._subParamsIdx[n]>>8,f=255&this._subParamsIdx[n];return f-c>0?this._subParams.subarray(c,f):null}getSubParamsAll(){const n={};for(let c=0;c<this.length;++c){const f=this._subParamsIdx[c]>>8,m=255&this._subParamsIdx[c];m-f>0&&(n[c]=this._subParams.slice(f,m))}return n}addDigit(n){let c;if(this._rejectDigits||!(c=this._digitIsSub?this._subParamsLength:this.length)||this._digitIsSub&&this._rejectSubDigits)return;const f=this._digitIsSub?this._subParams:this.params,m=f[c-1];f[c-1]=~m?Math.min(10*m+n,a):n}}s.Params=_},5741:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.AddonManager=void 0,s.AddonManager=class{constructor(){this._addons=[]}dispose(){for(let a=this._addons.length-1;a>=0;a--)this._addons[a].instance.dispose()}loadAddon(a,_){const d={instance:_,dispose:_.dispose,isDisposed:!1};this._addons.push(d),_.dispose=()=>this._wrappedAddonDispose(d),_.activate(a)}_wrappedAddonDispose(a){if(a.isDisposed)return;let _=-1;for(let d=0;d<this._addons.length;d++)if(this._addons[d]===a){_=d;break}if(_===-1)throw new Error("Could not dispose an addon that has not been loaded");a.isDisposed=!0,a.dispose.apply(a.instance),this._addons.splice(_,1)}}},8771:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferApiView=void 0;const _=a(3785),d=a(511);s.BufferApiView=class{constructor(n,c){this._buffer=n,this.type=c}init(n){return this._buffer=n,this}get cursorY(){return this._buffer.y}get cursorX(){return this._buffer.x}get viewportY(){return this._buffer.ydisp}get baseY(){return this._buffer.ybase}get length(){return this._buffer.lines.length}getLine(n){const c=this._buffer.lines.get(n);if(c)return new _.BufferLineApiView(c)}getNullCell(){return new d.CellData}}},3785:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferLineApiView=void 0;const _=a(511);s.BufferLineApiView=class{constructor(d){this._line=d}get isWrapped(){return this._line.isWrapped}get length(){return this._line.length}getCell(d,n){if(!(d<0||d>=this._line.length))return n?(this._line.loadCell(d,n),n):this._line.loadCell(d,new _.CellData)}translateToString(d,n,c){return this._line.translateToString(d,n,c)}}},8285:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferNamespaceApi=void 0;const _=a(8771),d=a(8460),n=a(844);class c extends n.Disposable{constructor(m){super(),this._core=m,this._onBufferChange=this.register(new d.EventEmitter),this.onBufferChange=this._onBufferChange.event,this._normal=new _.BufferApiView(this._core.buffers.normal,"normal"),this._alternate=new _.BufferApiView(this._core.buffers.alt,"alternate"),this._core.buffers.onBufferActivate(()=>this._onBufferChange.fire(this.active))}get active(){if(this._core.buffers.active===this._core.buffers.normal)return this.normal;if(this._core.buffers.active===this._core.buffers.alt)return this.alternate;throw new Error("Active buffer is neither normal nor alternate")}get normal(){return this._normal.init(this._core.buffers.normal)}get alternate(){return this._alternate.init(this._core.buffers.alt)}}s.BufferNamespaceApi=c},7975:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ParserApi=void 0,s.ParserApi=class{constructor(a){this._core=a}registerCsiHandler(a,_){return this._core.registerCsiHandler(a,d=>_(d.toArray()))}addCsiHandler(a,_){return this.registerCsiHandler(a,_)}registerDcsHandler(a,_){return this._core.registerDcsHandler(a,(d,n)=>_(d,n.toArray()))}addDcsHandler(a,_){return this.registerDcsHandler(a,_)}registerEscHandler(a,_){return this._core.registerEscHandler(a,_)}addEscHandler(a,_){return this.registerEscHandler(a,_)}registerOscHandler(a,_){return this._core.registerOscHandler(a,_)}addOscHandler(a,_){return this.registerOscHandler(a,_)}}},7090:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.UnicodeApi=void 0,s.UnicodeApi=class{constructor(a){this._core=a}register(a){this._core.unicodeService.register(a)}get versions(){return this._core.unicodeService.versions}get activeVersion(){return this._core.unicodeService.activeVersion}set activeVersion(a){this._core.unicodeService.activeVersion=a}}},744:function(B,s,a){var _=this&&this.__decorate||function(e,r,t,i){var h,v=arguments.length,l=v<3?r:i===null?i=Object.getOwnPropertyDescriptor(r,t):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")l=Reflect.decorate(e,r,t,i);else for(var g=e.length-1;g>=0;g--)(h=e[g])&&(l=(v<3?h(l):v>3?h(r,t,l):h(r,t))||l);return v>3&&l&&Object.defineProperty(r,t,l),l},d=this&&this.__param||function(e,r){return function(t,i){r(t,i,e)}};Object.defineProperty(s,"__esModule",{value:!0}),s.BufferService=s.MINIMUM_ROWS=s.MINIMUM_COLS=void 0;const n=a(8460),c=a(844),f=a(5295),m=a(2585);s.MINIMUM_COLS=2,s.MINIMUM_ROWS=1;let o=s.BufferService=class extends c.Disposable{get buffer(){return this.buffers.active}constructor(e){super(),this.isUserScrolling=!1,this._onResize=this.register(new n.EventEmitter),this.onResize=this._onResize.event,this._onScroll=this.register(new n.EventEmitter),this.onScroll=this._onScroll.event,this.cols=Math.max(e.rawOptions.cols||0,s.MINIMUM_COLS),this.rows=Math.max(e.rawOptions.rows||0,s.MINIMUM_ROWS),this.buffers=this.register(new f.BufferSet(e,this))}resize(e,r){this.cols=e,this.rows=r,this.buffers.resize(e,r),this._onResize.fire({cols:e,rows:r})}reset(){this.buffers.reset(),this.isUserScrolling=!1}scroll(e,r=!1){const t=this.buffer;let i;i=this._cachedBlankLine,i&&i.length===this.cols&&i.getFg(0)===e.fg&&i.getBg(0)===e.bg||(i=t.getBlankLine(e,r),this._cachedBlankLine=i),i.isWrapped=r;const h=t.ybase+t.scrollTop,v=t.ybase+t.scrollBottom;if(t.scrollTop===0){const l=t.lines.isFull;v===t.lines.length-1?l?t.lines.recycle().copyFrom(i):t.lines.push(i.clone()):t.lines.splice(v+1,0,i.clone()),l?this.isUserScrolling&&(t.ydisp=Math.max(t.ydisp-1,0)):(t.ybase++,this.isUserScrolling||t.ydisp++)}else{const l=v-h+1;t.lines.shiftElements(h+1,l-1,-1),t.lines.set(v,i.clone())}this.isUserScrolling||(t.ydisp=t.ybase),this._onScroll.fire(t.ydisp)}scrollLines(e,r,t){const i=this.buffer;if(e<0){if(i.ydisp===0)return;this.isUserScrolling=!0}else e+i.ydisp>=i.ybase&&(this.isUserScrolling=!1);const h=i.ydisp;i.ydisp=Math.max(Math.min(i.ydisp+e,i.ybase),0),h!==i.ydisp&&(r||this._onScroll.fire(i.ydisp))}};s.BufferService=o=_([d(0,m.IOptionsService)],o)},7994:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CharsetService=void 0,s.CharsetService=class{constructor(){this.glevel=0,this._charsets=[]}reset(){this.charset=void 0,this._charsets=[],this.glevel=0}setgLevel(a){this.glevel=a,this.charset=this._charsets[a]}setgCharset(a,_){this._charsets[a]=_,this.glevel===a&&(this.charset=_)}}},1753:function(B,s,a){var _=this&&this.__decorate||function(i,h,v,l){var g,u=arguments.length,p=u<3?h:l===null?l=Object.getOwnPropertyDescriptor(h,v):l;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")p=Reflect.decorate(i,h,v,l);else for(var C=i.length-1;C>=0;C--)(g=i[C])&&(p=(u<3?g(p):u>3?g(h,v,p):g(h,v))||p);return u>3&&p&&Object.defineProperty(h,v,p),p},d=this&&this.__param||function(i,h){return function(v,l){h(v,l,i)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CoreMouseService=void 0;const n=a(2585),c=a(8460),f=a(844),m={NONE:{events:0,restrict:()=>!1},X10:{events:1,restrict:i=>i.button!==4&&i.action===1&&(i.ctrl=!1,i.alt=!1,i.shift=!1,!0)},VT200:{events:19,restrict:i=>i.action!==32},DRAG:{events:23,restrict:i=>i.action!==32||i.button!==3},ANY:{events:31,restrict:i=>!0}};function o(i,h){let v=(i.ctrl?16:0)|(i.shift?4:0)|(i.alt?8:0);return i.button===4?(v|=64,v|=i.action):(v|=3&i.button,4&i.button&&(v|=64),8&i.button&&(v|=128),i.action===32?v|=32:i.action!==0||h||(v|=3)),v}const e=String.fromCharCode,r={DEFAULT:i=>{const h=[o(i,!1)+32,i.col+32,i.row+32];return h[0]>255||h[1]>255||h[2]>255?"":`\x1B[M${e(h[0])}${e(h[1])}${e(h[2])}`},SGR:i=>{const h=i.action===0&&i.button!==4?"m":"M";return`\x1B[<${o(i,!0)};${i.col};${i.row}${h}`},SGR_PIXELS:i=>{const h=i.action===0&&i.button!==4?"m":"M";return`\x1B[<${o(i,!0)};${i.x};${i.y}${h}`}};let t=s.CoreMouseService=class extends f.Disposable{constructor(i,h){super(),this._bufferService=i,this._coreService=h,this._protocols={},this._encodings={},this._activeProtocol="",this._activeEncoding="",this._lastEvent=null,this._onProtocolChange=this.register(new c.EventEmitter),this.onProtocolChange=this._onProtocolChange.event;for(const v of Object.keys(m))this.addProtocol(v,m[v]);for(const v of Object.keys(r))this.addEncoding(v,r[v]);this.reset()}addProtocol(i,h){this._protocols[i]=h}addEncoding(i,h){this._encodings[i]=h}get activeProtocol(){return this._activeProtocol}get areMouseEventsActive(){return this._protocols[this._activeProtocol].events!==0}set activeProtocol(i){if(!this._protocols[i])throw new Error(`unknown protocol "${i}"`);this._activeProtocol=i,this._onProtocolChange.fire(this._protocols[i].events)}get activeEncoding(){return this._activeEncoding}set activeEncoding(i){if(!this._encodings[i])throw new Error(`unknown encoding "${i}"`);this._activeEncoding=i}reset(){this.activeProtocol="NONE",this.activeEncoding="DEFAULT",this._lastEvent=null}triggerMouseEvent(i){if(i.col<0||i.col>=this._bufferService.cols||i.row<0||i.row>=this._bufferService.rows||i.button===4&&i.action===32||i.button===3&&i.action!==32||i.button!==4&&(i.action===2||i.action===3)||(i.col++,i.row++,i.action===32&&this._lastEvent&&this._equalEvents(this._lastEvent,i,this._activeEncoding==="SGR_PIXELS"))||!this._protocols[this._activeProtocol].restrict(i))return!1;const h=this._encodings[this._activeEncoding](i);return h&&(this._activeEncoding==="DEFAULT"?this._coreService.triggerBinaryEvent(h):this._coreService.triggerDataEvent(h,!0)),this._lastEvent=i,!0}explainEvents(i){return{down:!!(1&i),up:!!(2&i),drag:!!(4&i),move:!!(8&i),wheel:!!(16&i)}}_equalEvents(i,h,v){if(v){if(i.x!==h.x||i.y!==h.y)return!1}else if(i.col!==h.col||i.row!==h.row)return!1;return i.button===h.button&&i.action===h.action&&i.ctrl===h.ctrl&&i.alt===h.alt&&i.shift===h.shift}};s.CoreMouseService=t=_([d(0,n.IBufferService),d(1,n.ICoreService)],t)},6975:function(B,s,a){var _=this&&this.__decorate||function(t,i,h,v){var l,g=arguments.length,u=g<3?i:v===null?v=Object.getOwnPropertyDescriptor(i,h):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")u=Reflect.decorate(t,i,h,v);else for(var p=t.length-1;p>=0;p--)(l=t[p])&&(u=(g<3?l(u):g>3?l(i,h,u):l(i,h))||u);return g>3&&u&&Object.defineProperty(i,h,u),u},d=this&&this.__param||function(t,i){return function(h,v){i(h,v,t)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CoreService=void 0;const n=a(1439),c=a(8460),f=a(844),m=a(2585),o=Object.freeze({insertMode:!1}),e=Object.freeze({applicationCursorKeys:!1,applicationKeypad:!1,bracketedPasteMode:!1,origin:!1,reverseWraparound:!1,sendFocus:!1,wraparound:!0});let r=s.CoreService=class extends f.Disposable{constructor(t,i,h){super(),this._bufferService=t,this._logService=i,this._optionsService=h,this.isCursorInitialized=!1,this.isCursorHidden=!1,this._onData=this.register(new c.EventEmitter),this.onData=this._onData.event,this._onUserInput=this.register(new c.EventEmitter),this.onUserInput=this._onUserInput.event,this._onBinary=this.register(new c.EventEmitter),this.onBinary=this._onBinary.event,this._onRequestScrollToBottom=this.register(new c.EventEmitter),this.onRequestScrollToBottom=this._onRequestScrollToBottom.event,this.modes=(0,n.clone)(o),this.decPrivateModes=(0,n.clone)(e)}reset(){this.modes=(0,n.clone)(o),this.decPrivateModes=(0,n.clone)(e)}triggerDataEvent(t,i=!1){if(this._optionsService.rawOptions.disableStdin)return;const h=this._bufferService.buffer;i&&this._optionsService.rawOptions.scrollOnUserInput&&h.ybase!==h.ydisp&&this._onRequestScrollToBottom.fire(),i&&this._onUserInput.fire(),this._logService.debug(`sending data "${t}"`,()=>t.split("").map(v=>v.charCodeAt(0))),this._onData.fire(t)}triggerBinaryEvent(t){this._optionsService.rawOptions.disableStdin||(this._logService.debug(`sending binary "${t}"`,()=>t.split("").map(i=>i.charCodeAt(0))),this._onBinary.fire(t))}};s.CoreService=r=_([d(0,m.IBufferService),d(1,m.ILogService),d(2,m.IOptionsService)],r)},9074:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DecorationService=void 0;const _=a(8055),d=a(8460),n=a(844),c=a(6106);let f=0,m=0;class o extends n.Disposable{get decorations(){return this._decorations.values()}constructor(){super(),this._decorations=new c.SortedList(t=>t==null?void 0:t.marker.line),this._onDecorationRegistered=this.register(new d.EventEmitter),this.onDecorationRegistered=this._onDecorationRegistered.event,this._onDecorationRemoved=this.register(new d.EventEmitter),this.onDecorationRemoved=this._onDecorationRemoved.event,this.register((0,n.toDisposable)(()=>this.reset()))}registerDecoration(t){if(t.marker.isDisposed)return;const i=new e(t);if(i){const h=i.marker.onDispose(()=>i.dispose());i.onDispose(()=>{i&&(this._decorations.delete(i)&&this._onDecorationRemoved.fire(i),h.dispose())}),this._decorations.insert(i),this._onDecorationRegistered.fire(i)}return i}reset(){for(const t of this._decorations.values())t.dispose();this._decorations.clear()}*getDecorationsAtCell(t,i,h){let v=0,l=0;for(const g of this._decorations.getKeyIterator(i))v=g.options.x??0,l=v+(g.options.width??1),t>=v&&t<l&&(!h||(g.options.layer??"bottom")===h)&&(yield g)}forEachDecorationAtCell(t,i,h,v){this._decorations.forEachByKey(i,l=>{f=l.options.x??0,m=f+(l.options.width??1),t>=f&&t<m&&(!h||(l.options.layer??"bottom")===h)&&v(l)})}}s.DecorationService=o;class e extends n.Disposable{get isDisposed(){return this._isDisposed}get backgroundColorRGB(){return this._cachedBg===null&&(this.options.backgroundColor?this._cachedBg=_.css.toColor(this.options.backgroundColor):this._cachedBg=void 0),this._cachedBg}get foregroundColorRGB(){return this._cachedFg===null&&(this.options.foregroundColor?this._cachedFg=_.css.toColor(this.options.foregroundColor):this._cachedFg=void 0),this._cachedFg}constructor(t){super(),this.options=t,this.onRenderEmitter=this.register(new d.EventEmitter),this.onRender=this.onRenderEmitter.event,this._onDispose=this.register(new d.EventEmitter),this.onDispose=this._onDispose.event,this._cachedBg=null,this._cachedFg=null,this.marker=t.marker,this.options.overviewRulerOptions&&!this.options.overviewRulerOptions.position&&(this.options.overviewRulerOptions.position="full")}dispose(){this._onDispose.fire(),super.dispose()}}},4348:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.InstantiationService=s.ServiceCollection=void 0;const _=a(2585),d=a(8343);class n{constructor(...f){this._entries=new Map;for(const[m,o]of f)this.set(m,o)}set(f,m){const o=this._entries.get(f);return this._entries.set(f,m),o}forEach(f){for(const[m,o]of this._entries.entries())f(m,o)}has(f){return this._entries.has(f)}get(f){return this._entries.get(f)}}s.ServiceCollection=n,s.InstantiationService=class{constructor(){this._services=new n,this._services.set(_.IInstantiationService,this)}setService(c,f){this._services.set(c,f)}getService(c){return this._services.get(c)}createInstance(c,...f){const m=(0,d.getServiceDependencies)(c).sort((r,t)=>r.index-t.index),o=[];for(const r of m){const t=this._services.get(r.id);if(!t)throw new Error(`[createInstance] ${c.name} depends on UNKNOWN service ${r.id}.`);o.push(t)}const e=m.length>0?m[0].index:f.length;if(f.length!==e)throw new Error(`[createInstance] First service dependency of ${c.name} at position ${e+1} conflicts with ${f.length} static arguments`);return new c(...f,...o)}}},7866:function(B,s,a){var _=this&&this.__decorate||function(e,r,t,i){var h,v=arguments.length,l=v<3?r:i===null?i=Object.getOwnPropertyDescriptor(r,t):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")l=Reflect.decorate(e,r,t,i);else for(var g=e.length-1;g>=0;g--)(h=e[g])&&(l=(v<3?h(l):v>3?h(r,t,l):h(r,t))||l);return v>3&&l&&Object.defineProperty(r,t,l),l},d=this&&this.__param||function(e,r){return function(t,i){r(t,i,e)}};Object.defineProperty(s,"__esModule",{value:!0}),s.traceCall=s.setTraceLogger=s.LogService=void 0;const n=a(844),c=a(2585),f={trace:c.LogLevelEnum.TRACE,debug:c.LogLevelEnum.DEBUG,info:c.LogLevelEnum.INFO,warn:c.LogLevelEnum.WARN,error:c.LogLevelEnum.ERROR,off:c.LogLevelEnum.OFF};let m,o=s.LogService=class extends n.Disposable{get logLevel(){return this._logLevel}constructor(e){super(),this._optionsService=e,this._logLevel=c.LogLevelEnum.OFF,this._updateLogLevel(),this.register(this._optionsService.onSpecificOptionChange("logLevel",()=>this._updateLogLevel())),m=this}_updateLogLevel(){this._logLevel=f[this._optionsService.rawOptions.logLevel]}_evalLazyOptionalParams(e){for(let r=0;r<e.length;r++)typeof e[r]=="function"&&(e[r]=e[r]())}_log(e,r,t){this._evalLazyOptionalParams(t),e.call(console,(this._optionsService.options.logger?"":"xterm.js: ")+r,...t)}trace(e,...r){var t;this._logLevel<=c.LogLevelEnum.TRACE&&this._log(((t=this._optionsService.options.logger)==null?void 0:t.trace.bind(this._optionsService.options.logger))??console.log,e,r)}debug(e,...r){var t;this._logLevel<=c.LogLevelEnum.DEBUG&&this._log(((t=this._optionsService.options.logger)==null?void 0:t.debug.bind(this._optionsService.options.logger))??console.log,e,r)}info(e,...r){var t;this._logLevel<=c.LogLevelEnum.INFO&&this._log(((t=this._optionsService.options.logger)==null?void 0:t.info.bind(this._optionsService.options.logger))??console.info,e,r)}warn(e,...r){var t;this._logLevel<=c.LogLevelEnum.WARN&&this._log(((t=this._optionsService.options.logger)==null?void 0:t.warn.bind(this._optionsService.options.logger))??console.warn,e,r)}error(e,...r){var t;this._logLevel<=c.LogLevelEnum.ERROR&&this._log(((t=this._optionsService.options.logger)==null?void 0:t.error.bind(this._optionsService.options.logger))??console.error,e,r)}};s.LogService=o=_([d(0,c.IOptionsService)],o),s.setTraceLogger=function(e){m=e},s.traceCall=function(e,r,t){if(typeof t.value!="function")throw new Error("not supported");const i=t.value;t.value=function(...h){if(m.logLevel!==c.LogLevelEnum.TRACE)return i.apply(this,h);m.trace(`GlyphRenderer#${i.name}(${h.map(l=>JSON.stringify(l)).join(", ")})`);const v=i.apply(this,h);return m.trace(`GlyphRenderer#${i.name} return`,v),v}}},7302:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.OptionsService=s.DEFAULT_OPTIONS=void 0;const _=a(8460),d=a(844),n=a(6114);s.DEFAULT_OPTIONS={cols:80,rows:24,cursorBlink:!1,cursorStyle:"block",cursorWidth:1,cursorInactiveStyle:"outline",customGlyphs:!0,drawBoldTextInBrightColors:!0,documentOverride:null,fastScrollModifier:"alt",fastScrollSensitivity:5,fontFamily:"courier-new, courier, monospace",fontSize:15,fontWeight:"normal",fontWeightBold:"bold",ignoreBracketedPasteMode:!1,lineHeight:1,letterSpacing:0,linkHandler:null,logLevel:"info",logger:null,scrollback:1e3,scrollOnUserInput:!0,scrollSensitivity:1,screenReaderMode:!1,smoothScrollDuration:0,macOptionIsMeta:!1,macOptionClickForcesSelection:!1,minimumContrastRatio:1,disableStdin:!1,allowProposedApi:!1,allowTransparency:!1,tabStopWidth:8,theme:{},rescaleOverlappingGlyphs:!1,rightClickSelectsWord:n.isMac,windowOptions:{},windowsMode:!1,windowsPty:{},wordSeparator:" ()[]{}',\"`",altClickMovesCursor:!0,convertEol:!1,termName:"xterm",cancelEvents:!1,overviewRulerWidth:0};const c=["normal","bold","100","200","300","400","500","600","700","800","900"];class f extends d.Disposable{constructor(o){super(),this._onOptionChange=this.register(new _.EventEmitter),this.onOptionChange=this._onOptionChange.event;const e={...s.DEFAULT_OPTIONS};for(const r in o)if(r in e)try{const t=o[r];e[r]=this._sanitizeAndValidateOption(r,t)}catch(t){console.error(t)}this.rawOptions=e,this.options={...e},this._setupOptions(),this.register((0,d.toDisposable)(()=>{this.rawOptions.linkHandler=null,this.rawOptions.documentOverride=null}))}onSpecificOptionChange(o,e){return this.onOptionChange(r=>{r===o&&e(this.rawOptions[o])})}onMultipleOptionChange(o,e){return this.onOptionChange(r=>{o.indexOf(r)!==-1&&e()})}_setupOptions(){const o=r=>{if(!(r in s.DEFAULT_OPTIONS))throw new Error(`No option with key "${r}"`);return this.rawOptions[r]},e=(r,t)=>{if(!(r in s.DEFAULT_OPTIONS))throw new Error(`No option with key "${r}"`);t=this._sanitizeAndValidateOption(r,t),this.rawOptions[r]!==t&&(this.rawOptions[r]=t,this._onOptionChange.fire(r))};for(const r in this.rawOptions){const t={get:o.bind(this,r),set:e.bind(this,r)};Object.defineProperty(this.options,r,t)}}_sanitizeAndValidateOption(o,e){switch(o){case"cursorStyle":if(e||(e=s.DEFAULT_OPTIONS[o]),!function(r){return r==="block"||r==="underline"||r==="bar"}(e))throw new Error(`"${e}" is not a valid value for ${o}`);break;case"wordSeparator":e||(e=s.DEFAULT_OPTIONS[o]);break;case"fontWeight":case"fontWeightBold":if(typeof e=="number"&&1<=e&&e<=1e3)break;e=c.includes(e)?e:s.DEFAULT_OPTIONS[o];break;case"cursorWidth":e=Math.floor(e);case"lineHeight":case"tabStopWidth":if(e<1)throw new Error(`${o} cannot be less than 1, value: ${e}`);break;case"minimumContrastRatio":e=Math.max(1,Math.min(21,Math.round(10*e)/10));break;case"scrollback":if((e=Math.min(e,4294967295))<0)throw new Error(`${o} cannot be less than 0, value: ${e}`);break;case"fastScrollSensitivity":case"scrollSensitivity":if(e<=0)throw new Error(`${o} cannot be less than or equal to 0, value: ${e}`);break;case"rows":case"cols":if(!e&&e!==0)throw new Error(`${o} must be numeric, value: ${e}`);break;case"windowsPty":e=e??{}}return e}}s.OptionsService=f},2660:function(B,s,a){var _=this&&this.__decorate||function(f,m,o,e){var r,t=arguments.length,i=t<3?m:e===null?e=Object.getOwnPropertyDescriptor(m,o):e;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(f,m,o,e);else for(var h=f.length-1;h>=0;h--)(r=f[h])&&(i=(t<3?r(i):t>3?r(m,o,i):r(m,o))||i);return t>3&&i&&Object.defineProperty(m,o,i),i},d=this&&this.__param||function(f,m){return function(o,e){m(o,e,f)}};Object.defineProperty(s,"__esModule",{value:!0}),s.OscLinkService=void 0;const n=a(2585);let c=s.OscLinkService=class{constructor(f){this._bufferService=f,this._nextId=1,this._entriesWithId=new Map,this._dataByLinkId=new Map}registerLink(f){const m=this._bufferService.buffer;if(f.id===void 0){const h=m.addMarker(m.ybase+m.y),v={data:f,id:this._nextId++,lines:[h]};return h.onDispose(()=>this._removeMarkerFromLink(v,h)),this._dataByLinkId.set(v.id,v),v.id}const o=f,e=this._getEntryIdKey(o),r=this._entriesWithId.get(e);if(r)return this.addLineToLink(r.id,m.ybase+m.y),r.id;const t=m.addMarker(m.ybase+m.y),i={id:this._nextId++,key:this._getEntryIdKey(o),data:o,lines:[t]};return t.onDispose(()=>this._removeMarkerFromLink(i,t)),this._entriesWithId.set(i.key,i),this._dataByLinkId.set(i.id,i),i.id}addLineToLink(f,m){const o=this._dataByLinkId.get(f);if(o&&o.lines.every(e=>e.line!==m)){const e=this._bufferService.buffer.addMarker(m);o.lines.push(e),e.onDispose(()=>this._removeMarkerFromLink(o,e))}}getLinkData(f){var m;return(m=this._dataByLinkId.get(f))==null?void 0:m.data}_getEntryIdKey(f){return`${f.id};;${f.uri}`}_removeMarkerFromLink(f,m){const o=f.lines.indexOf(m);o!==-1&&(f.lines.splice(o,1),f.lines.length===0&&(f.data.id!==void 0&&this._entriesWithId.delete(f.key),this._dataByLinkId.delete(f.id)))}};s.OscLinkService=c=_([d(0,n.IBufferService)],c)},8343:(B,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.createDecorator=s.getServiceDependencies=s.serviceRegistry=void 0;const a="di$target",_="di$dependencies";s.serviceRegistry=new Map,s.getServiceDependencies=function(d){return d[_]||[]},s.createDecorator=function(d){if(s.serviceRegistry.has(d))return s.serviceRegistry.get(d);const n=function(c,f,m){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");(function(o,e,r){e[a]===e?e[_].push({id:o,index:r}):(e[_]=[{id:o,index:r}],e[a]=e)})(n,c,m)};return n.toString=()=>d,s.serviceRegistry.set(d,n),n}},2585:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.IDecorationService=s.IUnicodeService=s.IOscLinkService=s.IOptionsService=s.ILogService=s.LogLevelEnum=s.IInstantiationService=s.ICharsetService=s.ICoreService=s.ICoreMouseService=s.IBufferService=void 0;const _=a(8343);var d;s.IBufferService=(0,_.createDecorator)("BufferService"),s.ICoreMouseService=(0,_.createDecorator)("CoreMouseService"),s.ICoreService=(0,_.createDecorator)("CoreService"),s.ICharsetService=(0,_.createDecorator)("CharsetService"),s.IInstantiationService=(0,_.createDecorator)("InstantiationService"),function(n){n[n.TRACE=0]="TRACE",n[n.DEBUG=1]="DEBUG",n[n.INFO=2]="INFO",n[n.WARN=3]="WARN",n[n.ERROR=4]="ERROR",n[n.OFF=5]="OFF"}(d||(s.LogLevelEnum=d={})),s.ILogService=(0,_.createDecorator)("LogService"),s.IOptionsService=(0,_.createDecorator)("OptionsService"),s.IOscLinkService=(0,_.createDecorator)("OscLinkService"),s.IUnicodeService=(0,_.createDecorator)("UnicodeService"),s.IDecorationService=(0,_.createDecorator)("DecorationService")},1480:(B,s,a)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.UnicodeService=void 0;const _=a(8460),d=a(225);class n{static extractShouldJoin(f){return(1&f)!=0}static extractWidth(f){return f>>1&3}static extractCharKind(f){return f>>3}static createPropertyValue(f,m,o=!1){return(16777215&f)<<3|(3&m)<<1|(o?1:0)}constructor(){this._providers=Object.create(null),this._active="",this._onChange=new _.EventEmitter,this.onChange=this._onChange.event;const f=new d.UnicodeV6;this.register(f),this._active=f.version,this._activeProvider=f}dispose(){this._onChange.dispose()}get versions(){return Object.keys(this._providers)}get activeVersion(){return this._active}set activeVersion(f){if(!this._providers[f])throw new Error(`unknown Unicode version "${f}"`);this._active=f,this._activeProvider=this._providers[f],this._onChange.fire(f)}register(f){this._providers[f.version]=f}wcwidth(f){return this._activeProvider.wcwidth(f)}getStringCellWidth(f){let m=0,o=0;const e=f.length;for(let r=0;r<e;++r){let t=f.charCodeAt(r);if(55296<=t&&t<=56319){if(++r>=e)return m+this.wcwidth(t);const v=f.charCodeAt(r);56320<=v&&v<=57343?t=1024*(t-55296)+v-56320+65536:m+=this.wcwidth(v)}const i=this.charProperties(t,o);let h=n.extractWidth(i);n.extractShouldJoin(i)&&(h-=n.extractWidth(o)),m+=h,o=i}return m}charProperties(f,m){return this._activeProvider.charProperties(f,m)}}s.UnicodeService=n}},Q={};function Y(B){var s=Q[B];if(s!==void 0)return s.exports;var a=Q[B]={exports:{}};return ae[B].call(a.exports,a,a.exports,Y),a.exports}var oe={};return(()=>{var B=oe;Object.defineProperty(B,"__esModule",{value:!0}),B.Terminal=void 0;const s=Y(9042),a=Y(3236),_=Y(844),d=Y(5741),n=Y(8285),c=Y(7975),f=Y(7090),m=["cols","rows"];class o extends _.Disposable{constructor(r){super(),this._core=this.register(new a.Terminal(r)),this._addonManager=this.register(new d.AddonManager),this._publicOptions={...this._core.options};const t=h=>this._core.options[h],i=(h,v)=>{this._checkReadonlyOptions(h),this._core.options[h]=v};for(const h in this._core.options){const v={get:t.bind(this,h),set:i.bind(this,h)};Object.defineProperty(this._publicOptions,h,v)}}_checkReadonlyOptions(r){if(m.includes(r))throw new Error(`Option "${r}" can only be set in the constructor`)}_checkProposedApi(){if(!this._core.optionsService.rawOptions.allowProposedApi)throw new Error("You must set the allowProposedApi option to true to use proposed API")}get onBell(){return this._core.onBell}get onBinary(){return this._core.onBinary}get onCursorMove(){return this._core.onCursorMove}get onData(){return this._core.onData}get onKey(){return this._core.onKey}get onLineFeed(){return this._core.onLineFeed}get onRender(){return this._core.onRender}get onResize(){return this._core.onResize}get onScroll(){return this._core.onScroll}get onSelectionChange(){return this._core.onSelectionChange}get onTitleChange(){return this._core.onTitleChange}get onWriteParsed(){return this._core.onWriteParsed}get element(){return this._core.element}get parser(){return this._parser||(this._parser=new c.ParserApi(this._core)),this._parser}get unicode(){return this._checkProposedApi(),new f.UnicodeApi(this._core)}get textarea(){return this._core.textarea}get rows(){return this._core.rows}get cols(){return this._core.cols}get buffer(){return this._buffer||(this._buffer=this.register(new n.BufferNamespaceApi(this._core))),this._buffer}get markers(){return this._checkProposedApi(),this._core.markers}get modes(){const r=this._core.coreService.decPrivateModes;let t="none";switch(this._core.coreMouseService.activeProtocol){case"X10":t="x10";break;case"VT200":t="vt200";break;case"DRAG":t="drag";break;case"ANY":t="any"}return{applicationCursorKeysMode:r.applicationCursorKeys,applicationKeypadMode:r.applicationKeypad,bracketedPasteMode:r.bracketedPasteMode,insertMode:this._core.coreService.modes.insertMode,mouseTrackingMode:t,originMode:r.origin,reverseWraparoundMode:r.reverseWraparound,sendFocusMode:r.sendFocus,wraparoundMode:r.wraparound}}get options(){return this._publicOptions}set options(r){for(const t in r)this._publicOptions[t]=r[t]}blur(){this._core.blur()}focus(){this._core.focus()}input(r,t=!0){this._core.input(r,t)}resize(r,t){this._verifyIntegers(r,t),this._core.resize(r,t)}open(r){this._core.open(r)}attachCustomKeyEventHandler(r){this._core.attachCustomKeyEventHandler(r)}attachCustomWheelEventHandler(r){this._core.attachCustomWheelEventHandler(r)}registerLinkProvider(r){return this._core.registerLinkProvider(r)}registerCharacterJoiner(r){return this._checkProposedApi(),this._core.registerCharacterJoiner(r)}deregisterCharacterJoiner(r){this._checkProposedApi(),this._core.deregisterCharacterJoiner(r)}registerMarker(r=0){return this._verifyIntegers(r),this._core.registerMarker(r)}registerDecoration(r){return this._checkProposedApi(),this._verifyPositiveIntegers(r.x??0,r.width??0,r.height??0),this._core.registerDecoration(r)}hasSelection(){return this._core.hasSelection()}select(r,t,i){this._verifyIntegers(r,t,i),this._core.select(r,t,i)}getSelection(){return this._core.getSelection()}getSelectionPosition(){return this._core.getSelectionPosition()}clearSelection(){this._core.clearSelection()}selectAll(){this._core.selectAll()}selectLines(r,t){this._verifyIntegers(r,t),this._core.selectLines(r,t)}dispose(){super.dispose()}scrollLines(r){this._verifyIntegers(r),this._core.scrollLines(r)}scrollPages(r){this._verifyIntegers(r),this._core.scrollPages(r)}scrollToTop(){this._core.scrollToTop()}scrollToBottom(){this._core.scrollToBottom()}scrollToLine(r){this._verifyIntegers(r),this._core.scrollToLine(r)}clear(){this._core.clear()}write(r,t){this._core.write(r,t)}writeln(r,t){this._core.write(r),this._core.write(`\r
`,t)}paste(r){this._core.paste(r)}refresh(r,t){this._verifyIntegers(r,t),this._core.refresh(r,t)}reset(){this._core.reset()}clearTextureAtlas(){this._core.clearTextureAtlas()}loadAddon(r){this._addonManager.loadAddon(this,r)}static get strings(){return s}_verifyIntegers(...r){for(const t of r)if(t===1/0||isNaN(t)||t%1!=0)throw new Error("This API only accepts integers")}_verifyPositiveIntegers(...r){for(const t of r)if(t&&(t===1/0||isNaN(t)||t%1!=0||t<0))throw new Error("This API only accepts positive integers")}}B.Terminal=o})(),oe})())}(Le)),Le.exports}var Ge=Ke();const Je=fe.forwardRef((de,ve)=>{const ae=fe.useRef(null),Q=fe.useRef(null),{className:Y,onInput:oe,onKey:B,onResize:s,...a}=de,{theme:_}=Fe();return fe.useEffect(()=>{const d=new Ge.Terminal({allowTransparency:!0,fontFamily:'"JetBrains Mono", "Aa偷吃可爱长大的", "Noto Serif SC", monospace',cursorInactiveStyle:"outline",drawBoldTextInBrightColors:!1,fontSize:14,lineHeight:1.2});Q.current=d;const n=new ze.FitAddon;d.loadAddon(new qe.WebLinksAddon((f,m)=>{(f.ctrlKey||f.metaKey)&&window.open(m,"_blank")})),d.loadAddon(n),d.open(ae.current),d.loadAddon(new Ue.CanvasAddon),d.onData(f=>{oe&&oe(f)}),d.onKey(f=>{B&&B(f.key,f.domEvent)});const c=new ResizeObserver(()=>{n.fit();const f=d.cols,m=d.rows;s&&s(f,m)});return document.fonts.ready.then(()=>{n.fit(),c.observe(ae.current)}),()=>{c.disconnect(),setTimeout(()=>{d.dispose()},0)}},[]),fe.useEffect(()=>{Q.current&&(_==="dark"?Q.current.options.theme={background:"#00000000",black:"#ffffff",red:"#cd3131",green:"#0dbc79",yellow:"#e5e510",blue:"#2472c8",cyan:"#11a8cd",white:"#e5e5e5",brightBlack:"#666666",brightRed:"#f14c4c",brightGreen:"#23d18b",brightYellow:"#f5f543",brightBlue:"#3b8eea",brightCyan:"#29b8db",brightWhite:"#e5e5e5",foreground:"#cccccc",selectionBackground:"#3a3d41",cursor:"#ffffff"}:Q.current.options.theme={background:"#ffffff00",black:"#000000",red:"#aa3731",green:"#448c27",yellow:"#cb9000",blue:"#325cc0",cyan:"#0083b2",white:"#7f7f7f",brightBlack:"#777777",brightRed:"#f05050",brightGreen:"#60cb00",brightYellow:"#ffbc5d",brightBlue:"#007acc",brightCyan:"#00aacb",brightWhite:"#b0b0b0",foreground:"#000000",selectionBackground:"#bfdbfe",cursor:"#007acc"})},[_]),fe.useImperativeHandle(ve,()=>({write:(...d)=>{var n;return(n=Q.current)==null?void 0:n.write(...d)},writeAsync:async d=>new Promise(n=>{var c;(c=Q.current)==null||c.write(d,n)}),writeln:(...d)=>{var n;return(n=Q.current)==null?void 0:n.writeln(...d)},writelnAsync:async d=>new Promise(n=>{var c;(c=Q.current)==null||c.writeln(d,n)}),clear:()=>{var d;(d=Q.current)==null||d.clear()},terminalRef:Q}),[]),Re.jsx("div",{className:We("p-2 rounded-md shadow-sm border border-default-200 w-full h-full overflow-hidden bg-opacity-50 backdrop-blur-sm",_==="dark"?"bg-black":"bg-white",Y),...a,children:Re.jsx("div",{style:{width:"100%",height:"100%"},ref:ae})})});export{Je as X};
