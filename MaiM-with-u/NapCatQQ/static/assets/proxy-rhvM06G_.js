import{j as ue}from"./index-D8VBA_Ei.js";import{r as P}from"./react-router-dom-Bk_r5m4S.js";const Ps=P.createContext({});function Ji(t){const e=P.useRef(null);return e.current===null&&(e.current=t()),e.current}const De=typeof window<"u",Qi=De?P.useLayoutEffect:P.useEffect,Re=P.createContext(null),bs=P.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});function tr(t=!0){const e=P.useContext(Re);if(e===null)return[!0,null];const{isPresent:n,onExitComplete:s,register:i}=e,o=P.useId();P.useEffect(()=>{if(t)return i(o)},[t]);const r=P.useCallback(()=>t&&s&&s(o),[o,s,t]);return!n&&s?[!1,r]:[!0]}const Bt=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function er(t,e){let n=new Set,s=new Set,i=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function l(u){r.has(u)&&(c.schedule(u),t()),u(a)}const c={schedule:(u,h=!1,f=!1)=>{const m=f&&i?n:s;return h&&r.add(u),m.has(u)||m.add(u),u},cancel:u=>{s.delete(u),r.delete(u)},process:u=>{if(a=u,i){o=!0;return}i=!0,[n,s]=[s,n],n.forEach(l),n.clear(),i=!1,o&&(o=!1,c.process(u))}};return c}const z={},nr=40;function As(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=Bt.reduce((y,A)=>(y[A]=er(o),y),{}),{setup:a,read:l,resolveKeyframes:c,preUpdate:u,update:h,preRender:f,render:d,postRender:m}=r,p=()=>{const y=z.useManualTiming?i.timestamp:performance.now();n=!1,z.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(y-i.timestamp,nr),1)),i.timestamp=y,i.isProcessing=!0,a.process(i),l.process(i),c.process(i),u.process(i),h.process(i),f.process(i),d.process(i),m.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(p))},v=()=>{n=!0,s=!0,i.isProcessing||t(p)};return{schedule:Bt.reduce((y,A)=>{const x=r[A];return y[A]=(w,R=!1,b=!1)=>(n||v(),x.schedule(w,R,b)),y},{}),cancel:y=>{for(let A=0;A<Bt.length;A++)r[Bt[A]].cancel(y)},state:i,steps:r}}const K=t=>t,{schedule:M,cancel:q,state:E,steps:Zt}=As(typeof requestAnimationFrame<"u"?requestAnimationFrame:K,!0),ws=P.createContext({strict:!1}),un={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ct={};for(const t in un)ct[t]={isEnabled:e=>un[t].some(n=>!!e[n])};function sr(t){for(const e in t)ct[e]={...ct[e],...t[e]}}const ir=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Kt(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ir.has(t)}let Vs=t=>!Kt(t);function rr(t){t&&(Vs=e=>e.startsWith("on")?!Kt(e):t(e))}try{rr(require("@emotion/is-prop-valid").default)}catch{}function or(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(Vs(i)||n===!0&&Kt(i)||!e&&!Kt(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function ar(t){if(typeof Proxy>"u")return t;const e=new Map,n=(...s)=>t(...s);return new Proxy(n,{get:(s,i)=>i==="create"?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const _t=P.createContext({});function zt(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function bt(t){return typeof t=="string"||Array.isArray(t)}const Ee=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Le=["initial",...Ee];function Xt(t){return zt(t.animate)||Le.some(e=>bt(t[e]))}function Ms(t){return!!(Xt(t)||t.variants)}function lr(t,e){if(Xt(t)){const{initial:n,animate:s}=t;return{initial:n===!1||bt(n)?n:void 0,animate:bt(s)?s:void 0}}return t.inherit!==!1?e:{}}function ur(t){const{initial:e,animate:n}=lr(t,P.useContext(_t));return P.useMemo(()=>({initial:e,animate:n}),[cn(e),cn(n)])}function cn(t){return Array.isArray(t)?t.join(" "):t}const cr=Symbol.for("motionComponentSymbol");function rt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function hr(t,e,n){return P.useCallback(s=>{s&&t.onMount&&t.onMount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):rt(n)&&(n.current=s))},[e])}const Fe=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),fr="framerAppearId",Cs="data-"+Fe(fr),Ds=P.createContext({}),{schedule:ke}=As(queueMicrotask,!1);function dr(t,e,n,s,i){var p,v;const{visualElement:o}=P.useContext(_t),r=P.useContext(ws),a=P.useContext(Re),l=P.useContext(bs).reducedMotion,c=P.useRef(null);s=s||r.renderer,!c.current&&s&&(c.current=s(t,{visualState:e,parent:o,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));const u=c.current,h=P.useContext(Ds);u&&!u.projection&&i&&(u.type==="html"||u.type==="svg")&&mr(c.current,n,i,h);const f=P.useRef(!1);P.useInsertionEffect(()=>{u&&f.current&&u.update(n,a)});const d=n[Cs],m=P.useRef(!!d&&!((p=window.MotionHandoffIsComplete)!=null&&p.call(window,d))&&((v=window.MotionHasOptimisedAnimation)==null?void 0:v.call(window,d)));return Qi(()=>{u&&(f.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),ke.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),P.useEffect(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{var g;(g=window.MotionHandoffMarkAsComplete)==null||g.call(window,d)}),m.current=!1))}),u}function mr(t,e,n,s){const{layoutId:i,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:c,layoutCrossfade:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Rs(t.parent)),t.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!r||a&&rt(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:c})}function Rs(t){if(t)return t.options.allowProjection!==!1?t.projection:Rs(t.parent)}let Be=()=>{};function pr({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){t&&sr(t);function o(a,l){let c;const u={...P.useContext(bs),...a,layoutId:gr(a)},{isStatic:h}=u,f=ur(a),d=s(a,h);if(!h&&De){yr();const m=vr(u);c=m.MeasureLayout,f.visualElement=dr(i,d,u,e,m.ProjectionNode)}return ue.jsxs(_t.Provider,{value:f,children:[c&&f.visualElement?ue.jsx(c,{visualElement:f.visualElement,...u}):null,n(i,a,hr(d,f.visualElement,l),d,h,f.visualElement)]})}o.displayName=`motion.${typeof i=="string"?i:`create(${i.displayName??i.name??""})`}`;const r=P.forwardRef(o);return r[cr]=i,r}function gr({layoutId:t}){const e=P.useContext(Ps).id;return e&&t!==void 0?e+"-"+t:t}function yr(t,e){P.useContext(ws).strict}function vr(t){const{drag:e,layout:n}=ct;if(!e&&!n)return{};const s={...e,...n};return{MeasureLayout:e!=null&&e.isEnabled(t)||n!=null&&n.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}const Es=t=>e=>typeof e=="string"&&e.startsWith(t),Oe=Es("--"),Tr=Es("var(--"),Ie=t=>Tr(t)?xr.test(t.split("/*")[0].trim()):!1,xr=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,At={};function Sr(t){for(const e in t)At[e]=t[e],Oe(e)&&(At[e].isCSSVariable=!0)}const ft=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],dt=new Set(ft);function Ls(t,{layout:e,layoutId:n}){return dt.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!At[t]||t==="opacity")}const F=t=>!!(t&&t.getVelocity),Fs=(t,e)=>e&&typeof t=="number"?e.transform(t):t,X=(t,e,n)=>n>e?e:n<t?t:n,mt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},wt={...mt,transform:t=>X(0,1,t)},Ot={...mt,default:1},hn={...mt,transform:Math.round},Et=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Y=Et("deg"),$=Et("%"),T=Et("px"),Pr=Et("vh"),br=Et("vw"),fn={...$,parse:t=>$.parse(t)/100,transform:t=>$.transform(t*100)},Ar={rotate:Y,rotateX:Y,rotateY:Y,rotateZ:Y,scale:Ot,scaleX:Ot,scaleY:Ot,scaleZ:Ot,skew:Y,skewX:Y,skewY:Y,distance:T,translateX:T,translateY:T,translateZ:T,x:T,y:T,z:T,perspective:T,transformPerspective:T,opacity:wt,originX:fn,originY:fn,originZ:T},je={borderWidth:T,borderTopWidth:T,borderRightWidth:T,borderBottomWidth:T,borderLeftWidth:T,borderRadius:T,radius:T,borderTopLeftRadius:T,borderTopRightRadius:T,borderBottomRightRadius:T,borderBottomLeftRadius:T,width:T,maxWidth:T,height:T,maxHeight:T,top:T,right:T,bottom:T,left:T,padding:T,paddingTop:T,paddingRight:T,paddingBottom:T,paddingLeft:T,margin:T,marginTop:T,marginRight:T,marginBottom:T,marginLeft:T,backgroundPositionX:T,backgroundPositionY:T,...Ar,zIndex:hn,fillOpacity:wt,strokeOpacity:wt,numOctaves:hn},wr={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Vr=ft.length;function Mr(t,e,n){let s="",i=!0;for(let o=0;o<Vr;o++){const r=ft[o],a=t[r];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(r.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const c=Fs(a,je[r]);if(!l){i=!1;const u=wr[r]||r;s+=`${u}(${c}) `}n&&(e[r]=c)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}function Ne(t,e,n){const{style:s,vars:i,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const c=e[l];if(dt.has(l)){r=!0;continue}else if(Oe(l)){i[l]=c;continue}else{const u=Fs(c,je[l]);l.startsWith("origin")?(a=!0,o[l]=u):s[l]=u}}if(e.transform||(r||n?s.transform=Mr(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=o;s.transformOrigin=`${l} ${c} ${u}`}}const Ue=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ks(t,e,n){for(const s in e)!F(e[s])&&!Ls(s,n)&&(t[s]=e[s])}function Cr({transformTemplate:t},e){return P.useMemo(()=>{const n=Ue();return Ne(n,e,t),Object.assign({},n.vars,n.style)},[e])}function Dr(t,e){const n=t.style||{},s={};return ks(s,n,t),Object.assign(s,Cr(t,e)),s}function Rr(t,e){const n={},s=Dr(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n}const Er=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ke(t){return typeof t!="string"||t.includes("-")?!1:!!(Er.indexOf(t)>-1||/[A-Z]/u.test(t))}const Lr={offset:"stroke-dashoffset",array:"stroke-dasharray"},Fr={offset:"strokeDashoffset",array:"strokeDasharray"};function kr(t,e,n=1,s=0,i=!0){t.pathLength=1;const o=i?Lr:Fr;t[o.offset]=T.transform(-s);const r=T.transform(e),a=T.transform(n);t[o.array]=`${r} ${a}`}function Bs(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:o=1,pathOffset:r=0,...a},l,c){if(Ne(t,a,c),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:u,style:h}=t;u.transform&&(h.transform=u.transform,delete u.transform),(h.transform||u.transformOrigin)&&(h.transformOrigin=u.transformOrigin??"50% 50%",delete u.transformOrigin),h.transform&&(h.transformBox="fill-box",delete u.transformBox),e!==void 0&&(u.x=e),n!==void 0&&(u.y=n),s!==void 0&&(u.scale=s),i!==void 0&&kr(u,i,o,r,!1)}const Os=()=>({...Ue(),attrs:{}}),Is=t=>typeof t=="string"&&t.toLowerCase()==="svg";function Br(t,e,n,s){const i=P.useMemo(()=>{const o=Os();return Bs(o,e,Is(s),t.transformTemplate),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};ks(o,t.style,t),i.style={...o,...i.style}}return i}function Or(t=!1){return(n,s,i,{latestValues:o},r)=>{const l=(Ke(n)?Br:Rr)(s,o,r,n),c=or(s,typeof n=="string",t),u=n!==P.Fragment?{...c,...l,ref:i}:{},{children:h}=s,f=P.useMemo(()=>F(h)?h.get():h,[h]);return P.createElement(n,{...u,children:f})}}function dn(t){const e=[{},{}];return t==null||t.values.forEach((n,s)=>{e[0][s]=n.get(),e[1][s]=n.getVelocity()}),e}function We(t,e,n,s){if(typeof e=="function"){const[i,o]=dn(s);e=e(n!==void 0?n:t.custom,i,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[i,o]=dn(s);e=e(n!==void 0?n:t.custom,i,o)}return e}function jt(t){return F(t)?t.get():t}function Ir({scrapeMotionValuesFromProps:t,createRenderState:e},n,s,i){return{latestValues:jr(n,s,i,t),renderState:e()}}const js=t=>(e,n)=>{const s=P.useContext(_t),i=P.useContext(Re),o=()=>Ir(t,e,s,i);return n?o():Ji(o)};function jr(t,e,n,s){const i={},o=s(t,{});for(const f in o)i[f]=jt(o[f]);let{initial:r,animate:a}=t;const l=Xt(t),c=Ms(t);e&&c&&!l&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||r===!1;const h=u?a:r;if(h&&typeof h!="boolean"&&!zt(h)){const f=Array.isArray(h)?h:[h];for(let d=0;d<f.length;d++){const m=We(t,f[d]);if(m){const{transitionEnd:p,transition:v,...g}=m;for(const S in g){let y=g[S];if(Array.isArray(y)){const A=u?y.length-1:0;y=y[A]}y!==null&&(i[S]=y)}for(const S in p)i[S]=p[S]}}}return i}function $e(t,e,n){var o;const{style:s}=t,i={};for(const r in s)(F(s[r])||e.style&&F(e.style[r])||Ls(r,t)||((o=n==null?void 0:n.getValue(r))==null?void 0:o.liveStyle)!==void 0)&&(i[r]=s[r]);return i}const Nr={useVisualState:js({scrapeMotionValuesFromProps:$e,createRenderState:Ue})};function Ns(t,e,n){const s=$e(t,e,n);for(const i in t)if(F(t[i])||F(e[i])){const o=ft.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[o]=t[i]}return s}const Ur={useVisualState:js({scrapeMotionValuesFromProps:Ns,createRenderState:Os})};function Kr(t,e){return function(s,{forwardMotionProps:i}={forwardMotionProps:!1}){const r={...Ke(s)?Ur:Nr,preloadedFeatures:t,useRender:Or(i),createVisualElement:e,Component:s};return pr(r)}}function Vt(t,e,n){const s=t.getProps();return We(s,e,n!==void 0?n:s.custom,t)}const ce=t=>Array.isArray(t);let Nt;function Wr(){Nt=void 0}const I={now:()=>(Nt===void 0&&I.set(E.isProcessing||z.useManualTiming?E.timestamp:performance.now()),Nt),set:t=>{Nt=t,queueMicrotask(Wr)}};function Ge(t,e){t.indexOf(e)===-1&&t.push(e)}function He(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class _e{constructor(){this.subscriptions=[]}add(e){return Ge(this.subscriptions,e),()=>He(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let o=0;o<i;o++){const r=this.subscriptions[o];r&&r(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Us(t,e){return e?t*(1e3/e):0}const mn=30,$r=t=>!isNaN(parseFloat(t));class Gr{constructor(e,n={}){this.version="12.9.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,i=!0)=>{var r,a;const o=I.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&((r=this.events.change)==null||r.notify(this.current)),i&&((a=this.events.renderRequest)==null||a.notify(this.current))},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=I.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=$r(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new _e);const s=this.events[e].add(n);return e==="change"?()=>{s(),M.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=I.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>mn)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,mn);return Us(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e;(e=this.events.destroy)==null||e.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Mt(t,e){return new Gr(t,e)}function Hr(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Mt(n))}function _r(t){return ce(t)?t[t.length-1]||0:t}function zr(t,e){const n=Vt(t,e);let{transitionEnd:s={},transition:i={},...o}=n||{};o={...o,...s};for(const r in o){const a=_r(o[r]);Hr(t,r,a)}}function Xr(t){return!!(F(t)&&t.add)}function he(t,e){const n=t.getValue("willChange");if(Xr(n))return n.add(e);if(!n&&z.WillChange){const s=new z.WillChange("auto");t.addValue("willChange",s),s.add(e)}}function Ks(t){return t.props[Cs]}const Yr=t=>t!==null;function qr(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(Yr),o=e&&n!=="loop"&&e%2===1?0:i.length-1;return i[o]}const Zr={type:"spring",stiffness:500,damping:25,restSpeed:10},Jr=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Qr={type:"keyframes",duration:.8},to={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},eo=(t,{keyframes:e})=>e.length>2?Qr:dt.has(t)?t.startsWith("scale")?Jr(e[1]):Zr:to;function no({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}function ze(t,e){return(t==null?void 0:t[e])??(t==null?void 0:t.default)??t}const G=t=>t*1e3,H=t=>t/1e3,vt=t=>Math.round(t*1e5)/1e5,Xe=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function so(t){return t==null}const io=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ye=(t,e)=>n=>!!(typeof n=="string"&&io.test(n)&&n.startsWith(t)||e&&!so(n)&&Object.prototype.hasOwnProperty.call(n,e)),Ws=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,o,r,a]=s.match(Xe);return{[t]:parseFloat(i),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},ro=t=>X(0,255,t),Jt={...mt,transform:t=>Math.round(ro(t))},et={test:Ye("rgb","red"),parse:Ws("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+Jt.transform(t)+", "+Jt.transform(e)+", "+Jt.transform(n)+", "+vt(wt.transform(s))+")"};function oo(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const fe={test:Ye("#"),parse:oo,transform:et.transform},ot={test:Ye("hsl","hue"),parse:Ws("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+$.transform(vt(e))+", "+$.transform(vt(n))+", "+vt(wt.transform(s))+")"},L={test:t=>et.test(t)||fe.test(t)||ot.test(t),parse:t=>et.test(t)?et.parse(t):ot.test(t)?ot.parse(t):fe.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?et.transform(t):ot.transform(t)},ao=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function lo(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(Xe))==null?void 0:e.length)||0)+(((n=t.match(ao))==null?void 0:n.length)||0)>0}const $s="number",Gs="color",uo="var",co="var(",pn="${}",ho=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ct(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let o=0;const a=e.replace(ho,l=>(L.test(l)?(s.color.push(o),i.push(Gs),n.push(L.parse(l))):l.startsWith(co)?(s.var.push(o),i.push(uo),n.push(l)):(s.number.push(o),i.push($s),n.push(parseFloat(l))),++o,pn)).split(pn);return{values:n,split:a,indexes:s,types:i}}function Hs(t){return Ct(t).values}function _s(t){const{split:e,types:n}=Ct(t),s=e.length;return i=>{let o="";for(let r=0;r<s;r++)if(o+=e[r],i[r]!==void 0){const a=n[r];a===$s?o+=vt(i[r]):a===Gs?o+=L.transform(i[r]):o+=i[r]}return o}}const fo=t=>typeof t=="number"?0:t;function mo(t){const e=Hs(t);return _s(t)(e.map(fo))}const Z={test:lo,parse:Hs,createTransformer:_s,getAnimatableNone:mo};function Qt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function po({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,o=0,r=0;if(!e)i=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=Qt(l,a,t+1/3),o=Qt(l,a,t),r=Qt(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:s}}function Wt(t,e){return n=>n>0?e:t}const V=(t,e,n)=>t+(e-t)*n,te=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},go=[fe,et,ot],yo=t=>go.find(e=>e.test(t));function gn(t){const e=yo(t);if(!e)return!1;let n=e.parse(t);return e===ot&&(n=po(n)),n}const yn=(t,e)=>{const n=gn(t),s=gn(e);if(!n||!s)return Wt(t,e);const i={...n};return o=>(i.red=te(n.red,s.red,o),i.green=te(n.green,s.green,o),i.blue=te(n.blue,s.blue,o),i.alpha=V(n.alpha,s.alpha,o),et.transform(i))},de=new Set(["none","hidden"]);function vo(t,e){return de.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}const To=(t,e)=>n=>e(t(n)),Lt=(...t)=>t.reduce(To);function xo(t,e){return n=>V(t,e,n)}function qe(t){return typeof t=="number"?xo:typeof t=="string"?Ie(t)?Wt:L.test(t)?yn:bo:Array.isArray(t)?zs:typeof t=="object"?L.test(t)?yn:So:Wt}function zs(t,e){const n=[...t],s=n.length,i=t.map((o,r)=>qe(o)(o,e[r]));return o=>{for(let r=0;r<s;r++)n[r]=i[r](o);return n}}function So(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=qe(t[i])(t[i],e[i]));return i=>{for(const o in s)n[o]=s[o](i);return n}}function Po(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const o=e.types[i],r=t.indexes[o][s[o]],a=t.values[r]??0;n[i]=a,s[o]++}return n}const bo=(t,e)=>{const n=Z.createTransformer(e),s=Ct(t),i=Ct(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?de.has(t)&&!i.values.length||de.has(e)&&!s.values.length?vo(t,e):Lt(zs(Po(s,i),i.values),n):Wt(t,e)};function Xs(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?V(t,e,n):qe(t)(t,e)}const Ao=t=>{const e=({timestamp:n})=>t(n);return{start:()=>M.update(e,!0),stop:()=>q(e),now:()=>E.isProcessing?E.timestamp:I.now()}},Ys=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let o=0;o<i;o++)s+=t(o/(i-1))+", ";return`linear(${s.substring(0,s.length-2)})`},$t=2e4;function Ze(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<$t;)e+=n,s=t.next(e);return e>=$t?1/0:e}function wo(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(Ze(s),$t);return{type:"keyframes",ease:o=>s.next(i*o).value/e,duration:H(i)}}const Vo=5;function qs(t,e,n){const s=Math.max(e-Vo,0);return Us(n-t(s),e-s)}const C={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},vn=.001;function Mo({duration:t=C.duration,bounce:e=C.bounce,velocity:n=C.velocity,mass:s=C.mass}){let i,o,r=1-e;r=X(C.minDamping,C.maxDamping,r),t=X(C.minDuration,C.maxDuration,H(t)),r<1?(i=c=>{const u=c*r,h=u*t,f=u-n,d=me(c,r),m=Math.exp(-h);return vn-f/d*m},o=c=>{const h=c*r*t,f=h*n+n,d=Math.pow(r,2)*Math.pow(c,2)*t,m=Math.exp(-h),p=me(Math.pow(c,2),r);return(-i(c)+vn>0?-1:1)*((f-d)*m)/p}):(i=c=>{const u=Math.exp(-c*t),h=(c-n)*t+1;return-.001+u*h},o=c=>{const u=Math.exp(-c*t),h=(n-c)*(t*t);return u*h});const a=5/t,l=Do(i,o,a);if(t=G(t),isNaN(l))return{stiffness:C.stiffness,damping:C.damping,duration:t};{const c=Math.pow(l,2)*s;return{stiffness:c,damping:r*2*Math.sqrt(s*c),duration:t}}}const Co=12;function Do(t,e,n){let s=n;for(let i=1;i<Co;i++)s=s-t(s)/e(s);return s}function me(t,e){return t*Math.sqrt(1-e*e)}const Ro=["duration","bounce"],Eo=["stiffness","damping","mass"];function Tn(t,e){return e.some(n=>t[n]!==void 0)}function Lo(t){let e={velocity:C.velocity,stiffness:C.stiffness,damping:C.damping,mass:C.mass,isResolvedFromDuration:!1,...t};if(!Tn(t,Eo)&&Tn(t,Ro))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,o=2*X(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:C.mass,stiffness:i,damping:o}}else{const n=Mo(t);e={...e,...n,mass:C.mass},e.isResolvedFromDuration=!0}return e}function Gt(t=C.visualDuration,e=C.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:c,mass:u,duration:h,velocity:f,isResolvedFromDuration:d}=Lo({...n,velocity:-H(n.velocity||0)}),m=f||0,p=c/(2*Math.sqrt(l*u)),v=r-o,g=H(Math.sqrt(l/u)),S=Math.abs(v)<5;s||(s=S?C.restSpeed.granular:C.restSpeed.default),i||(i=S?C.restDelta.granular:C.restDelta.default);let y;if(p<1){const x=me(g,p);y=w=>{const R=Math.exp(-p*g*w);return r-R*((m+p*g*v)/x*Math.sin(x*w)+v*Math.cos(x*w))}}else if(p===1)y=x=>r-Math.exp(-g*x)*(v+(m+g*v)*x);else{const x=g*Math.sqrt(p*p-1);y=w=>{const R=Math.exp(-p*g*w),b=Math.min(x*w,300);return r-R*((m+p*g*v)*Math.sinh(b)+x*v*Math.cosh(b))/x}}const A={calculatedDuration:d&&h||null,next:x=>{const w=y(x);if(d)a.done=x>=h;else{let R=x===0?m:0;p<1&&(R=x===0?G(m):qs(y,x,w));const b=Math.abs(R)<=s,O=Math.abs(r-w)<=i;a.done=b&&O}return a.value=a.done?r:w,a},toString:()=>{const x=Math.min(Ze(A),$t),w=Ys(R=>A.next(x*R).value,x,30);return x+"ms "+w},toTransition:()=>{}};return A}Gt.applyToOptions=t=>{const e=wo(t,100,Gt);return t.ease=e.ease,t.duration=G(e.duration),t.type="keyframes",t};function pe({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],f={done:!1,value:h},d=b=>a!==void 0&&b<a||l!==void 0&&b>l,m=b=>a===void 0?l:l===void 0||Math.abs(a-b)<Math.abs(l-b)?a:l;let p=n*e;const v=h+p,g=r===void 0?v:r(v);g!==v&&(p=g-h);const S=b=>-p*Math.exp(-b/s),y=b=>g+S(b),A=b=>{const O=S(b),j=y(b);f.done=Math.abs(O)<=c,f.value=f.done?g:j};let x,w;const R=b=>{d(f.value)&&(x=b,w=Gt({keyframes:[f.value,m(f.value)],velocity:qs(y,b,f.value),damping:i,stiffness:o,restDelta:c,restSpeed:u}))};return R(0),{calculatedDuration:null,next:b=>{let O=!1;return!w&&x===void 0&&(O=!0,A(b),R(b)),x!==void 0&&b>=x?w.next(b-x):(!O&&A(b),f)}}}const Dt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s};function Fo(t,e,n){const s=[],i=n||z.mix||Xs,o=t.length-1;for(let r=0;r<o;r++){let a=i(t[r],t[r+1]);if(e){const l=Array.isArray(e)?e[r]||K:e;a=Lt(l,a)}s.push(a)}return s}function ko(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const o=t.length;if(Be(o===e.length),o===1)return()=>e[0];if(o===2&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=Fo(e,s,i),l=a.length,c=u=>{if(r&&u<t[0])return e[0];let h=0;if(l>1)for(;h<t.length-2&&!(u<t[h+1]);h++);const f=Dt(t[h],t[h+1],u);return a[h](f)};return n?u=>c(X(t[0],t[o-1],u)):c}function Bo(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=Dt(0,e,s);t.push(V(n,1,i))}}function Oo(t){const e=[0];return Bo(e,t.length-1),e}function Io(t,e){return t.map(n=>n*e)}const Zs=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,jo=1e-7,No=12;function Uo(t,e,n,s,i){let o,r,a=0;do r=e+(n-e)/2,o=Zs(r,s,i)-t,o>0?n=r:e=r;while(Math.abs(o)>jo&&++a<No);return r}function Ft(t,e,n,s){if(t===e&&n===s)return K;const i=o=>Uo(o,0,1,t,n);return o=>o===0||o===1?o:Zs(i(o),e,s)}const Ko=Ft(.42,0,1,1),Wo=Ft(0,0,.58,1),Js=Ft(.42,0,.58,1),$o=t=>Array.isArray(t)&&typeof t[0]!="number",Qs=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,ti=t=>e=>1-t(1-e),ei=Ft(.33,1.53,.69,.99),Je=ti(ei),ni=Qs(Je),si=t=>(t*=2)<1?.5*Je(t):.5*(2-Math.pow(2,-10*(t-1))),Qe=t=>1-Math.sin(Math.acos(t)),ii=ti(Qe),ri=Qs(Qe),oi=t=>Array.isArray(t)&&typeof t[0]=="number",Go={linear:K,easeIn:Ko,easeInOut:Js,easeOut:Wo,circIn:Qe,circInOut:ri,circOut:ii,backIn:Je,backInOut:ni,backOut:ei,anticipate:si},Ho=t=>typeof t=="string",xn=t=>{if(oi(t)){Be(t.length===4);const[e,n,s,i]=t;return Ft(e,n,s,i)}else if(Ho(t))return Go[t];return t};function _o(t,e){return t.map(()=>e||Js).splice(0,t.length-1)}function Tt({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=$o(s)?s.map(xn):xn(s),o={done:!1,value:e[0]},r=Io(n&&n.length===e.length?n:Oo(e),t),a=ko(r,e,{ease:Array.isArray(i)?i:_o(e,i)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const zo=t=>t!==null;function tn(t,{repeat:e,repeatType:n="loop"},s,i=1){const o=t.filter(zo),a=i<0||e&&n!=="loop"&&e%2===1?0:o.length-1;return!a||s===void 0?o[a]:s}const Xo={decay:pe,inertia:pe,tween:Tt,keyframes:Tt,spring:Gt};function ai(t){typeof t.type=="string"&&(t.type=Xo[t.type])}class en{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const Yo=t=>t/100;class li extends en{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:n}=this.options;if(n&&n.updatedAt!==I.now()&&this.tick(I.now()),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:s}=this.options;s&&s()},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;ai(e);const{type:n=Tt,repeat:s=0,repeatDelay:i=0,repeatType:o,velocity:r=0}=e;let{keyframes:a}=e;const l=n||Tt;l!==Tt&&typeof a[0]!="number"&&(this.mixKeyframes=Lt(Yo,Xs(a[0],a[1])),a=[0,100]);const c=l({...e,keyframes:a});o==="mirror"&&(this.mirroredGenerator=l({...e,keyframes:[...a].reverse(),velocity:-r})),c.calculatedDuration===null&&(c.calculatedDuration=Ze(c));const{calculatedDuration:u}=c;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=c}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:s,totalDuration:i,mixKeyframes:o,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:l}=this;if(this.startTime===null)return s.next(0);const{delay:c=0,keyframes:u,repeat:h,repeatType:f,repeatDelay:d,type:m,onUpdate:p,finalKeyframe:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const g=this.currentTime-c*(this.playbackSpeed>=0?1:-1),S=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=i);let y=this.currentTime,A=s;if(h){const b=Math.min(this.currentTime,i)/a;let O=Math.floor(b),j=b%1;!j&&b>=1&&(j=1),j===1&&O--,O=Math.min(O,h+1),!!(O%2)&&(f==="reverse"?(j=1-j,d&&(j-=d/a)):f==="mirror"&&(A=r)),y=X(0,1,j)*a}const x=S?{done:!1,value:u[0]}:A.next(y);o&&(x.value=o(x.value));let{done:w}=x;!S&&l!==null&&(w=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const R=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&w);return R&&m!==pe&&(x.value=tn(u,this.options,v,this.speed)),p&&p(x.value),R&&this.finish(),x}then(e,n){return this.finished.then(e,n)}get duration(){return H(this.calculatedDuration)}get time(){return H(this.currentTime)}set time(e){e=G(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(I.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=H(this.currentTime))}play(){if(this.isStopped)return;const{driver:e=Ao,onPlay:n,startTime:s}=this.options;this.driver||(this.driver=e(o=>this.tick(o))),n&&n();const i=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=i):this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime||(this.startTime=s??i),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(I.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:e}=this.options;e&&e()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),e.observe(this)}}function qo(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const nt=t=>t*180/Math.PI,ge=t=>{const e=nt(Math.atan2(t[1],t[0]));return ye(e)},Zo={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ge,rotateZ:ge,skewX:t=>nt(Math.atan(t[1])),skewY:t=>nt(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ye=t=>(t=t%360,t<0&&(t+=360),t),Sn=ge,Pn=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),bn=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Jo={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Pn,scaleY:bn,scale:t=>(Pn(t)+bn(t))/2,rotateX:t=>ye(nt(Math.atan2(t[6],t[5]))),rotateY:t=>ye(nt(Math.atan2(-t[2],t[0]))),rotateZ:Sn,rotate:Sn,skewX:t=>nt(Math.atan(t[4])),skewY:t=>nt(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function An(t){return t.includes("scale")?1:0}function ve(t,e){if(!t||t==="none")return An(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=Jo,i=n;else{const a=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=Zo,i=a}if(!i)return An(e);const o=s[e],r=i[1].split(",").map(ta);return typeof o=="function"?o(r):r[o]}const Qo=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return ve(n,e)};function ta(t){return parseFloat(t.trim())}const wn=t=>t===mt||t===T,ea=new Set(["x","y","z"]),na=ft.filter(t=>!ea.has(t));function sa(t){const e=[];return na.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const ht={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ve(e,"x"),y:(t,{transform:e})=>ve(e,"y")};ht.translateX=ht.x;ht.translateY=ht.y;const st=new Set;let Te=!1,xe=!1,Se=!1;function ui(){if(xe){const t=Array.from(st).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=sa(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([o,r])=>{var a;(a=s.getValue(o))==null||a.set(r)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}xe=!1,Te=!1,st.forEach(t=>t.complete(Se)),st.clear()}function ci(){st.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(xe=!0)})}function ia(){Se=!0,ci(),ui(),Se=!1}class nn{constructor(e,n,s,i,o,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=o,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(st.add(this),Te||(Te=!0,M.read(ci),M.resolveKeyframes(ui))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;if(e[0]===null){const o=i==null?void 0:i.get(),r=e[e.length-1];if(o!==void 0)e[0]=o;else if(s&&n){const a=s.readValue(n,r);a!=null&&(e[0]=a)}e[0]===void 0&&(e[0]=r),i&&o===void 0&&i.set(e[0])}qo(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),st.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,st.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const ra=t=>t.startsWith("--");function oa(t,e,n){ra(e)?t.style.setProperty(e,n):t.style[e]=n}function sn(t){let e;return()=>(e===void 0&&(e=t()),e)}const aa=sn(()=>window.ScrollTimeline!==void 0),la={};function ua(t,e){const n=sn(t);return()=>la[e]??n()}const hi=ua(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),gt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Vn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:gt([0,.65,.55,1]),circOut:gt([.55,0,1,.45]),backIn:gt([.31,.01,.66,-.59]),backOut:gt([.33,1.53,.69,.99])};function fi(t,e){if(t)return typeof t=="function"?hi()?Ys(t,e):"ease-out":oi(t)?gt(t):Array.isArray(t)?t.map(n=>fi(n,e)||Vn.easeOut):Vn[t]}function ca(t,e,n,{delay:s=0,duration:i=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},c=void 0){const u={[e]:n};l&&(u.offset=l);const h=fi(a,i);Array.isArray(h)&&(u.easing=h);const f={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"};return c&&(f.pseudoElement=c),t.animate(u,f)}function di(t){return typeof t=="function"&&"applyToOptions"in t}function ha({type:t,...e}){return di(t)&&hi()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class fa extends en{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:s,keyframes:i,pseudoElement:o,allowFlatten:r=!1,finalKeyframe:a,onComplete:l}=e;this.isPseudoElement=!!o,this.allowFlatten=r,this.options=e,Be(typeof e.type!="string");const c=ha(e);this.animation=ca(n,s,i,c,o),c.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!o){const u=tn(i,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(u):oa(n,s,u),this.animation.cancel()}l==null||l(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,n;(n=(e=this.animation).finish)==null||n.call(e)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var e,n;this.isPseudoElement||(n=(e=this.animation).commitStyles)==null||n.call(e)}get duration(){var n,s;const e=((s=(n=this.animation.effect)==null?void 0:n.getComputedTiming)==null?void 0:s.call(n).duration)||0;return H(Number(e))}get time(){return H(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=G(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){var s;return this.allowFlatten&&((s=this.animation.effect)==null||s.updateTiming({easing:"linear"})),this.animation.onfinish=null,e&&aa()?(this.animation.timeline=e,K):n(this)}}const mi={anticipate:si,backInOut:ni,circInOut:ri};function da(t){return t in mi}function ma(t){typeof t.ease=="string"&&da(t.ease)&&(t.ease=mi[t.ease])}const Mn=10;class pa extends fa{constructor(e){ma(e),ai(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:s,onComplete:i,element:o,...r}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const a=new li({...r,autoplay:!1}),l=G(this.finishedTime??this.time);n.setWithVelocity(a.sample(l-Mn).value,a.sample(l).value,Mn),a.stop()}}const Cn=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Z.test(t)||t==="0")&&!t.startsWith("url("));function ga(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function ya(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],r=Cn(i,e),a=Cn(o,e);return!r||!a?!1:ga(t)||(n==="spring"||di(n))&&s}const va=new Set(["opacity","clipPath","filter","transform"]),Ta=sn(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function xa(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:o,type:r}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Ta()&&n&&va.has(n)&&(n!=="transform"||!l)&&!a&&!s&&i!=="mirror"&&o!==0&&r!=="inertia"}const Sa=40;class Pa extends en{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:r="loop",keyframes:a,name:l,motionValue:c,element:u,...h}){var m;super(),this.stop=()=>{var p,v;this._animation?(this._animation.stop(),(p=this.stopTimeline)==null||p.call(this)):(v=this.keyframeResolver)==null||v.cancel()},this.createdAt=I.now();const f={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:o,repeatType:r,name:l,motionValue:c,element:u,...h},d=(u==null?void 0:u.KeyframeResolver)||nn;this.keyframeResolver=new d(a,(p,v,g)=>this.onKeyframesResolved(p,v,f,!g),l,c,u),(m=this.keyframeResolver)==null||m.scheduleResolve()}onKeyframesResolved(e,n,s,i){this.keyframeResolver=void 0;const{name:o,type:r,velocity:a,delay:l,isHandoff:c,onUpdate:u}=s;this.resolvedAt=I.now(),ya(e,o,r,a)||((z.instantAnimations||!l)&&(u==null||u(tn(e,s,n))),e[0]=e[e.length-1],s.duration=0,s.repeat=0);const f={startTime:i?this.resolvedAt?this.resolvedAt-this.createdAt>Sa?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:e},d=!c&&xa(f)?new pa({...f,element:f.motionValue.owner.current}):new li(f);d.finished.then(()=>this.notifyFinished()).catch(K),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||ia(),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this.animation.cancel()}}const rn=(t,e,n,s={},i,o)=>r=>{const a=ze(s,t)||{},l=a.delay||s.delay||0;let{elapsed:c=0}=s;c=c-G(l);const u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:f=>{e.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:i};no(a)||Object.assign(u,eo(t,u)),u.duration&&(u.duration=G(u.duration)),u.repeatDelay&&(u.repeatDelay=G(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(h=!0)),(z.instantAnimations||z.skipAnimations)&&(h=!0,u.duration=0,u.delay=0),u.allowFlatten=!a.type&&!a.ease,h&&!o&&e.get()!==void 0){const f=qr(u.keyframes,a);if(f!==void 0){M.update(()=>{u.onUpdate(f),u.onComplete()});return}}return new Pa(u)},pi=new Set(["width","height","top","left","right","bottom",...ft]);function ba({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function gi(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;s&&(o=s);const l=[],c=i&&t.animationState&&t.animationState.getState()[i];for(const u in a){const h=t.getValue(u,t.latestValues[u]??null),f=a[u];if(f===void 0||c&&ba(c,u))continue;const d={delay:n,...ze(o||{},u)},m=h.get();if(m!==void 0&&!h.isAnimating&&!Array.isArray(f)&&f===m&&!d.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){const g=Ks(t);if(g){const S=window.MotionHandoffAnimation(g,u,M);S!==null&&(d.startTime=S,p=!0)}}he(t,u),h.start(rn(u,h,f,t.shouldReduceMotion&&pi.has(u)?{type:!1}:d,t,p));const v=h.animation;v&&l.push(v)}return r&&Promise.all(l).then(()=>{M.update(()=>{r&&zr(t,r)})}),l}function Pe(t,e,n={}){var l;const s=Vt(t,e,n.type==="exit"?(l=t.presenceContext)==null?void 0:l.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const o=s?()=>Promise.all(gi(t,s,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:h,staggerDirection:f}=i;return Aa(t,e,u+c,h,f,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[c,u]=a==="beforeChildren"?[o,r]:[r,o];return c().then(()=>u())}else return Promise.all([o(),r(n.delay)])}function Aa(t,e,n=0,s=0,i=1,o){const r=[],a=(t.variantChildren.size-1)*s,l=i===1?(c=0)=>c*s:(c=0)=>a-c*s;return Array.from(t.variantChildren).sort(wa).forEach((c,u)=>{c.notify("AnimationStart",e),r.push(Pe(c,e,{...o,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(r)}function wa(t,e){return t.sortNodePosition(e)}function Va(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(o=>Pe(t,o,n));s=Promise.all(i)}else if(typeof e=="string")s=Pe(t,e,n);else{const i=typeof e=="function"?Vt(t,e,n.custom):e;s=Promise.all(gi(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}function yi(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const Ma=Le.length;function vi(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?vi(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<Ma;n++){const s=Le[n],i=t.props[s];(bt(i)||i===!1)&&(e[s]=i)}return e}const Ca=[...Ee].reverse(),Da=Ee.length;function Ra(t){return e=>Promise.all(e.map(({animation:n,options:s})=>Va(t,n,s)))}function Ea(t){let e=Ra(t),n=Dn(),s=!0;const i=l=>(c,u)=>{var f;const h=Vt(t,u,l==="exit"?(f=t.presenceContext)==null?void 0:f.custom:void 0);if(h){const{transition:d,transitionEnd:m,...p}=h;c={...c,...p,...m}}return c};function o(l){e=l(t)}function r(l){const{props:c}=t,u=vi(t.parent)||{},h=[],f=new Set;let d={},m=1/0;for(let v=0;v<Da;v++){const g=Ca[v],S=n[g],y=c[g]!==void 0?c[g]:u[g],A=bt(y),x=g===l?S.isActive:null;x===!1&&(m=v);let w=y===u[g]&&y!==c[g]&&A;if(w&&s&&t.manuallyAnimateOnMount&&(w=!1),S.protectedKeys={...d},!S.isActive&&x===null||!y&&!S.prevProp||zt(y)||typeof y=="boolean")continue;const R=La(S.prevProp,y);let b=R||g===l&&S.isActive&&!w&&A||v>m&&A,O=!1;const j=Array.isArray(y)?y:[y];let it=j.reduce(i(g),{});x===!1&&(it={});const{prevResolvedValues:an={}}=S,Zi={...an,...it},ln=k=>{b=!0,f.has(k)&&(O=!0,f.delete(k)),S.needsAnimating[k]=!0;const _=t.getValue(k);_&&(_.liveStyle=!1)};for(const k in Zi){const _=it[k],Yt=an[k];if(d.hasOwnProperty(k))continue;let qt=!1;ce(_)&&ce(Yt)?qt=!yi(_,Yt):qt=_!==Yt,qt?_!=null?ln(k):f.add(k):_!==void 0&&f.has(k)?ln(k):S.protectedKeys[k]=!0}S.prevProp=y,S.prevResolvedValues=it,S.isActive&&(d={...d,...it}),s&&t.blockInitialAnimation&&(b=!1),b&&(!(w&&R)||O)&&h.push(...j.map(k=>({animation:k,options:{type:g}})))}if(f.size){const v={};if(typeof c.initial!="boolean"){const g=Vt(t,Array.isArray(c.initial)?c.initial[0]:c.initial);g&&g.transition&&(v.transition=g.transition)}f.forEach(g=>{const S=t.getBaseTarget(g),y=t.getValue(g);y&&(y.liveStyle=!0),v[g]=S??null}),h.push({animation:v})}let p=!!h.length;return s&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(p=!1),s=!1,p?e(h):Promise.resolve()}function a(l,c){var h;if(n[l].isActive===c)return Promise.resolve();(h=t.variantChildren)==null||h.forEach(f=>{var d;return(d=f.animationState)==null?void 0:d.setActive(l,c)}),n[l].isActive=c;const u=r(l);for(const f in n)n[f].protectedKeys={};return u}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=Dn(),s=!0}}}function La(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!yi(e,t):!1}function Q(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Dn(){return{animate:Q(!0),whileInView:Q(),whileHover:Q(),whileTap:Q(),whileDrag:Q(),whileFocus:Q(),exit:Q()}}class J{constructor(e){this.isMounted=!1,this.node=e}update(){}}class Fa extends J{constructor(e){super(e),e.animationState||(e.animationState=Ea(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();zt(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)==null||e.call(this)}}let ka=0;class Ba extends J{constructor(){super(...arguments),this.id=ka++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const Oa={animation:{Feature:Fa},exit:{Feature:Ba}};function Rt(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}const on=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function kt(t){return{point:{x:t.pageX,y:t.pageY}}}const Ia=t=>e=>on(e)&&t(e,kt(e));function xt(t,e,n,s){return Rt(t,e,Ia(n),s)}function Ti({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function ja({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Na(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}const xi=1e-4,Ua=1-xi,Ka=1+xi,Si=.01,Wa=0-Si,$a=0+Si;function B(t){return t.max-t.min}function Ga(t,e,n){return Math.abs(t-e)<=n}function Rn(t,e,n,s=.5){t.origin=s,t.originPoint=V(e.min,e.max,t.origin),t.scale=B(n)/B(e),t.translate=V(n.min,n.max,t.origin)-t.originPoint,(t.scale>=Ua&&t.scale<=Ka||isNaN(t.scale))&&(t.scale=1),(t.translate>=Wa&&t.translate<=$a||isNaN(t.translate))&&(t.translate=0)}function St(t,e,n,s){Rn(t.x,e.x,n.x,s?s.originX:void 0),Rn(t.y,e.y,n.y,s?s.originY:void 0)}function En(t,e,n){t.min=n.min+e.min,t.max=t.min+B(e)}function Ha(t,e,n){En(t.x,e.x,n.x),En(t.y,e.y,n.y)}function Ln(t,e,n){t.min=e.min-n.min,t.max=t.min+B(e)}function Pt(t,e,n){Ln(t.x,e.x,n.x),Ln(t.y,e.y,n.y)}const Fn=()=>({translate:0,scale:1,origin:0,originPoint:0}),at=()=>({x:Fn(),y:Fn()}),kn=()=>({min:0,max:0}),D=()=>({x:kn(),y:kn()});function U(t){return[t("x"),t("y")]}function ee(t){return t===void 0||t===1}function be({scale:t,scaleX:e,scaleY:n}){return!ee(t)||!ee(e)||!ee(n)}function tt(t){return be(t)||Pi(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Pi(t){return Bn(t.x)||Bn(t.y)}function Bn(t){return t&&t!=="0%"}function Ht(t,e,n){const s=t-n,i=e*s;return n+i}function On(t,e,n,s,i){return i!==void 0&&(t=Ht(t,i,s)),Ht(t,n,s)+e}function Ae(t,e=0,n=1,s,i){t.min=On(t.min,e,n,s,i),t.max=On(t.max,e,n,s,i)}function bi(t,{x:e,y:n}){Ae(t.x,e.translate,e.scale,e.originPoint),Ae(t.y,n.translate,n.scale,n.originPoint)}const In=.999999999999,jn=1.0000000000001;function _a(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let o,r;for(let a=0;a<i;a++){o=n[a],r=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ut(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,bi(t,r)),s&&tt(o.latestValues)&&ut(t,o.latestValues))}e.x<jn&&e.x>In&&(e.x=1),e.y<jn&&e.y>In&&(e.y=1)}function lt(t,e){t.min=t.min+e,t.max=t.max+e}function Nn(t,e,n,s,i=.5){const o=V(t.min,t.max,i);Ae(t,e,n,o,s)}function ut(t,e){Nn(t.x,e.x,e.scaleX,e.scale,e.originX),Nn(t.y,e.y,e.scaleY,e.scale,e.originY)}function Ai(t,e){return Ti(Na(t.getBoundingClientRect(),e))}function za(t,e,n){const s=Ai(t,n),{scroll:i}=e;return i&&(lt(s.x,i.offset.x),lt(s.y,i.offset.y)),s}const wi=({current:t})=>t?t.ownerDocument.defaultView:null,Un=(t,e)=>Math.abs(t-e);function Xa(t,e){const n=Un(t.x,e.x),s=Un(t.y,e.y);return Math.sqrt(n**2+s**2)}class Vi{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=se(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=Xa(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:m}=h,{timestamp:p}=E;this.history.push({...m,timestamp:p});const{onStart:v,onMove:g}=this.handlers;f||(v&&v(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=ne(f,this.transformPagePoint),M.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:m,resumeAnimation:p}=this.handlers;if(this.dragSnapToOrigin&&p&&p(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=se(h.type==="pointercancel"?this.lastMoveEventInfo:ne(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,v),m&&m(h,v)},!on(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const r=kt(e),a=ne(r,this.transformPagePoint),{point:l}=a,{timestamp:c}=E;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=n;u&&u(e,se(a,this.history)),this.removeListeners=Lt(xt(this.contextWindow,"pointermove",this.handlePointerMove),xt(this.contextWindow,"pointerup",this.handlePointerUp),xt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),q(this.updatePoint)}}function ne(t,e){return e?{point:e(t.point)}:t}function Kn(t,e){return{x:t.x-e.x,y:t.y-e.y}}function se({point:t},e){return{point:t,delta:Kn(t,Mi(e)),offset:Kn(t,Ya(e)),velocity:qa(e,.1)}}function Ya(t){return t[0]}function Mi(t){return t[t.length-1]}function qa(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=Mi(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>G(e)));)n--;if(!s)return{x:0,y:0};const o=H(i.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const r={x:(i.x-s.x)/o,y:(i.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Za(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?V(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?V(n,t,s.max):Math.min(t,n)),t}function Wn(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Ja(t,{top:e,left:n,bottom:s,right:i}){return{x:Wn(t.x,n,i),y:Wn(t.y,e,s)}}function $n(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function Qa(t,e){return{x:$n(t.x,e.x),y:$n(t.y,e.y)}}function tl(t,e){let n=.5;const s=B(t),i=B(e);return i>s?n=Dt(e.min,e.max-s,t.min):s>i&&(n=Dt(t.min,t.max-i,e.min)),X(0,1,n)}function el(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const we=.35;function nl(t=we){return t===!1?t=0:t===!0&&(t=we),{x:Gn(t,"left","right"),y:Gn(t,"top","bottom")}}function Gn(t,e,n){return{min:Hn(t,e),max:Hn(t,n)}}function Hn(t,e){return typeof t=="number"?t:t[e]||0}const W={x:!1,y:!1};function Ci(){return W.x||W.y}function sl(t){return t==="x"||t==="y"?W[t]?null:(W[t]=!0,()=>{W[t]=!1}):W.x||W.y?null:(W.x=W.y=!0,()=>{W.x=W.y=!1})}const il=new WeakMap;class rl{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=D(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const i=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(kt(u).point)},o=(u,h)=>{const{drag:f,dragPropagation:d,onDragStart:m}=this.getProps();if(f&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=sl(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),U(v=>{let g=this.getAxisMotionValue(v).get()||0;if($.test(g)){const{projection:S}=this.visualElement;if(S&&S.layout){const y=S.layout.layoutBox[v];y&&(g=B(y)*(parseFloat(g)/100))}}this.originPoint[v]=g}),m&&M.postRender(()=>m(u,h)),he(this.visualElement,"transform");const{animationState:p}=this.visualElement;p&&p.setActive("whileDrag",!0)},r=(u,h)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:m,onDrag:p}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:v}=h;if(d&&this.currentDirection===null){this.currentDirection=ol(v),this.currentDirection!==null&&m&&m(this.currentDirection);return}this.updateAxis("x",h.point,v),this.updateAxis("y",h.point,v),this.visualElement.render(),p&&p(u,h)},a=(u,h)=>this.stop(u,h),l=()=>U(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)==null?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new Vi(e,{onSessionStart:i,onStart:o,onMove:r,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:wi(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&M.postRender(()=>o(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!It(e,i,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(r=Za(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var o;const{dragConstraints:e,dragElastic:n}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(o=this.visualElement.projection)==null?void 0:o.layout,i=this.constraints;e&&rt(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=Ja(s.layoutBox,e):this.constraints=!1,this.elastic=nl(n),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&U(r=>{this.constraints!==!1&&this.getAxisMotionValue(r)&&(this.constraints[r]=el(s.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!rt(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=za(s,i.root,this.visualElement.getTransformPagePoint());let r=Qa(i.layout.layoutBox,o);if(n){const a=n(ja(r));this.hasMutatedConstraints=!!a,a&&(r=Ti(a))}return r}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=U(u=>{if(!It(u,n,this.currentDirection))return;let h=l&&l[u]||{};r&&(h={min:0,max:0});const f=i?200:1e6,d=i?40:1e7,m={type:"inertia",velocity:s?e[u]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(u,m)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return he(this.visualElement,e),s.start(rn(e,s,0,n,this.visualElement,!1))}stopAnimation(){U(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){U(e=>{var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){U(n=>{const{drag:s}=this.getProps();if(!It(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:r,max:a}=i.layout.layoutBox[n];o.set(e[n]-V(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!rt(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};U(r=>{const a=this.getAxisMotionValue(r);if(a&&this.constraints!==!1){const l=a.get();i[r]=tl({min:l,max:l},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),U(r=>{if(!It(r,e,null))return;const a=this.getAxisMotionValue(r),{min:l,max:c}=this.constraints[r];a.set(V(l,c,i[r]))})}addListeners(){if(!this.visualElement.current)return;il.set(this.visualElement,this);const e=this.visualElement.current,n=xt(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();rt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),M.read(s);const r=Rt(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(U(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:r=we,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function It(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function ol(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class al extends J{constructor(e){super(e),this.removeGroupControls=K,this.removeListeners=K,this.controls=new rl(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||K}unmount(){this.removeGroupControls(),this.removeListeners()}}const _n=t=>(e,n)=>{t&&M.postRender(()=>t(e,n))};class ll extends J{constructor(){super(...arguments),this.removePointerDownListener=K}onPointerDown(e){this.session=new Vi(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:wi(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:_n(e),onStart:_n(n),onMove:s,onEnd:(o,r)=>{delete this.session,i&&M.postRender(()=>i(o,r))}}}mount(){this.removePointerDownListener=xt(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ut={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function zn(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const pt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(T.test(t))t=parseFloat(t);else return t;const n=zn(t,e.target.x),s=zn(t,e.target.y);return`${n}% ${s}%`}},ul={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=Z.parse(t);if(i.length>5)return s;const o=Z.createTransformer(t),r=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+r]/=a,i[1+r]/=l;const c=V(a,l,.5);return typeof i[2+r]=="number"&&(i[2+r]/=c),typeof i[3+r]=="number"&&(i[3+r]/=c),o(i)}};class cl extends P.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:o}=e;Sr(hl),o&&(n.group&&n.group.add(o),s&&s.register&&i&&s.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Ut.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:o}=this.props,r=s.projection;return r&&(r.isPresent=o,i||e.layoutDependency!==n||n===void 0||e.isPresent!==o?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||M.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),ke.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Di(t){const[e,n]=tr(),s=P.useContext(Ps);return ue.jsx(cl,{...t,layoutGroup:s,switchLayoutGroup:P.useContext(Ds),isPresent:e,safeToRemove:n})}const hl={borderRadius:{...pt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:pt,borderTopRightRadius:pt,borderBottomLeftRadius:pt,borderBottomRightRadius:pt,boxShadow:ul};function fl(t,e,n){const s=F(t)?t:Mt(t);return s.start(rn("",s,e,n)),s.animation}function dl(t){return t instanceof SVGElement&&t.tagName!=="svg"}const ml=(t,e)=>t.depth-e.depth;class pl{constructor(){this.children=[],this.isDirty=!1}add(e){Ge(this.children,e),this.isDirty=!0}remove(e){He(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(ml),this.isDirty=!1,this.children.forEach(e)}}function gl(t,e){const n=I.now(),s=({timestamp:i})=>{const o=i-n;o>=e&&(q(s),t(o-e))};return M.setup(s,!0),()=>q(s)}const Ri=["TopLeft","TopRight","BottomLeft","BottomRight"],yl=Ri.length,Xn=t=>typeof t=="string"?parseFloat(t):t,Yn=t=>typeof t=="number"||T.test(t);function vl(t,e,n,s,i,o){i?(t.opacity=V(0,n.opacity??1,Tl(s)),t.opacityExit=V(e.opacity??1,0,xl(s))):o&&(t.opacity=V(e.opacity??1,n.opacity??1,s));for(let r=0;r<yl;r++){const a=`border${Ri[r]}Radius`;let l=qn(e,a),c=qn(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||Yn(l)===Yn(c)?(t[a]=Math.max(V(Xn(l),Xn(c),s),0),($.test(c)||$.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=V(e.rotate||0,n.rotate||0,s))}function qn(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const Tl=Ei(0,.5,ii),xl=Ei(.5,.95,K);function Ei(t,e,n){return s=>s<t?0:s>e?1:n(Dt(t,e,s))}function Zn(t,e){t.min=e.min,t.max=e.max}function N(t,e){Zn(t.x,e.x),Zn(t.y,e.y)}function Jn(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Qn(t,e,n,s,i){return t-=e,t=Ht(t,1/n,s),i!==void 0&&(t=Ht(t,1/i,s)),t}function Sl(t,e=0,n=1,s=.5,i,o=t,r=t){if($.test(e)&&(e=parseFloat(e),e=V(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=V(o.min,o.max,s);t===o&&(a-=e),t.min=Qn(t.min,e,n,a,i),t.max=Qn(t.max,e,n,a,i)}function ts(t,e,[n,s,i],o,r){Sl(t,e[n],e[s],e[i],e.scale,o,r)}const Pl=["x","scaleX","originX"],bl=["y","scaleY","originY"];function es(t,e,n,s){ts(t.x,e,Pl,n?n.x:void 0,s?s.x:void 0),ts(t.y,e,bl,n?n.y:void 0,s?s.y:void 0)}function ns(t){return t.translate===0&&t.scale===1}function Li(t){return ns(t.x)&&ns(t.y)}function ss(t,e){return t.min===e.min&&t.max===e.max}function Al(t,e){return ss(t.x,e.x)&&ss(t.y,e.y)}function is(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Fi(t,e){return is(t.x,e.x)&&is(t.y,e.y)}function rs(t){return B(t.x)/B(t.y)}function os(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class wl{constructor(){this.members=[]}add(e){Ge(this.members,e),e.scheduleRender()}remove(e){if(He(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Vl(t,e,n){let s="";const i=t.x.translate/e.x,o=t.y.translate/e.y,r=(n==null?void 0:n.z)||0;if((i||o||r)&&(s=`translate3d(${i}px, ${o}px, ${r}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:c,rotate:u,rotateX:h,rotateY:f,skewX:d,skewY:m}=n;c&&(s=`perspective(${c}px) ${s}`),u&&(s+=`rotate(${u}deg) `),h&&(s+=`rotateX(${h}deg) `),f&&(s+=`rotateY(${f}deg) `),d&&(s+=`skewX(${d}deg) `),m&&(s+=`skewY(${m}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(s+=`scale(${a}, ${l})`),s||"none"}const ie=["","X","Y","Z"],Ml={visibility:"hidden"},as=1e3;let Cl=0;function re(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function ki(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=Ks(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",M,!(i||o))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&ki(s)}function Bi({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(r={},a=e==null?void 0:e()){this.id=Cl++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(El),this.nodes.forEach(Ol),this.nodes.forEach(Il),this.nodes.forEach(Ll)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new pl)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new _e),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const l=this.eventHandlers.get(r);l&&l.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=dl(r),this.instance=r;const{layoutId:l,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||l)&&(this.isLayoutDirty=!0),t){let h;const f=()=>this.root.updateBlockedByResize=!1;t(r,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=gl(f,250),Ut.hasAnimatedSinceResize&&(Ut.hasAnimatedSinceResize=!1,this.nodes.forEach(us))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&u&&(l||c)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeLayoutChanged:d,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const p=this.options.transition||u.getDefaultTransition()||Wl,{onLayoutAnimationStart:v,onLayoutAnimationComplete:g}=u.getProps(),S=!this.targetLayout||!Fi(this.targetLayout,m),y=!f&&d;if(this.options.layoutRoot||this.resumeFrom||y||f&&(S||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,y);const A={...ze(p,"layout"),onPlay:v,onComplete:g};(u.shouldReduceMotion||this.options.layoutRoot)&&(A.delay=0,A.type=!1),this.startAnimation(A)}else f||us(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,q(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(jl),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&ki(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ls);return}this.isUpdating||this.nodes.forEach(kl),this.isUpdating=!1,this.nodes.forEach(Bl),this.nodes.forEach(Dl),this.nodes.forEach(Rl),this.clearAllSnapshots();const a=I.now();E.delta=X(0,1e3/60,a-E.timestamp),E.timestamp=a,E.isProcessing=!0,Zt.update.process(E),Zt.preRender.process(E),Zt.render.process(E),E.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ke.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Fl),this.sharedNodes.forEach(Nl)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,M.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){M.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!B(this.snapshot.measuredBox.x)&&!B(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=D(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a){const l=s(this.instance);this.scroll={animationId:this.root.animationId,phase:r,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const r=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!Li(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;r&&(a||tt(this.latestValues)||u)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return r&&(l=this.removeTransform(l)),$l(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var c;const{visualElement:r}=this.options;if(!r)return D();const a=r.measureViewportBox();if(!(((c=this.scroll)==null?void 0:c.wasRoot)||this.path.some(Gl))){const{scroll:u}=this.root;u&&(lt(a.x,u.offset.x),lt(a.y,u.offset.y))}return a}removeElementScroll(r){var l;const a=D();if(N(a,r),(l=this.scroll)!=null&&l.wasRoot)return a;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:h,options:f}=u;u!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&N(a,r),lt(a.x,h.offset.x),lt(a.y,h.offset.y))}return a}applyTransform(r,a=!1){const l=D();N(l,r);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&ut(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),tt(u.latestValues)&&ut(l,u.latestValues)}return tt(this.latestValues)&&ut(l,this.latestValues),l}removeTransform(r){const a=D();N(a,r);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!tt(c.latestValues))continue;be(c.latestValues)&&c.updateSnapshot();const u=D(),h=c.measurePageBox();N(u,h),es(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return tt(this.latestValues)&&es(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==E.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var f;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==a;if(!(r||l&&this.isSharedProjectionDirty||this.isProjectionDirty||(f=this.parent)!=null&&f.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:u,layoutId:h}=this.options;if(!(!this.layout||!(u||h))){if(this.resolvedRelativeTargetAt=E.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=D(),this.relativeTargetOrigin=D(),Pt(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),N(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=D(),this.targetWithTransforms=D()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Ha(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):N(this.target,this.layout.layoutBox),bi(this.target,this.targetDelta)):N(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=D(),this.relativeTargetOrigin=D(),Pt(this.relativeTargetOrigin,this.target,d.target),N(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||be(this.parent.latestValues)||Pi(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var m;const r=this.getLead(),a=!!this.resumingFrom||this!==r;let l=!0;if((this.isProjectionDirty||(m=this.parent)!=null&&m.isProjectionDirty)&&(l=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===E.timestamp&&(l=!1),l)return;const{layout:c,layoutId:u}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||u))return;N(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,f=this.treeScale.y;_a(this.layoutCorrected,this.treeScale,this.path,a),r.layout&&!r.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(r.target=r.layout.layoutBox,r.targetWithTransforms=D());const{target:d}=r;if(!d){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Jn(this.prevProjectionDelta.x,this.projectionDelta.x),Jn(this.prevProjectionDelta.y,this.projectionDelta.y)),St(this.projectionDelta,this.layoutCorrected,d,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==f||!os(this.projectionDelta.x,this.prevProjectionDelta.x)||!os(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",d))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){var a;if((a=this.options.visualElement)==null||a.scheduleRender(),r){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=at(),this.projectionDelta=at(),this.projectionDeltaWithTransform=at()}setAnimationOrigin(r,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=at();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=D(),d=l?l.source:void 0,m=this.layout?this.layout.source:void 0,p=d!==m,v=this.getStack(),g=!v||v.members.length<=1,S=!!(p&&!g&&this.options.crossfade===!0&&!this.path.some(Kl));this.animationProgress=0;let y;this.mixTargetDelta=A=>{const x=A/1e3;cs(h.x,r.x,x),cs(h.y,r.y,x),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Pt(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Ul(this.relativeTarget,this.relativeTargetOrigin,f,x),y&&Al(this.relativeTarget,y)&&(this.isProjectionDirty=!1),y||(y=D()),N(y,this.relativeTarget)),p&&(this.animationValues=u,vl(u,c,this.latestValues,x,S,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=x},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(q(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=M.update(()=>{Ut.hasAnimatedSinceResize=!0,this.currentAnimation=fl(0,as,{...r,onUpdate:a=>{this.mixTargetDelta(a),r.onUpdate&&r.onUpdate(a)},onStop:()=>{},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(as),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=r;if(!(!a||!l||!c)){if(this!==r&&this.layout&&c&&Oi(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||D();const h=B(this.layout.layoutBox.x);l.x.min=r.target.x.min,l.x.max=l.x.min+h;const f=B(this.layout.layoutBox.y);l.y.min=r.target.y.min,l.y.max=l.y.min+f}N(a,l),ut(a,u),St(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new wl),this.sharedNodes.get(r).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var a;const{layoutId:r}=this.options;return r?((a=this.getStack())==null?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:r}=this.options;return r?(a=this.getStack())==null?void 0:a.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetSkewAndRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:l}=r;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&re("z",r,c,this.animationValues);for(let u=0;u<ie.length;u++)re(`rotate${ie[u]}`,r,c,this.animationValues),re(`skew${ie[u]}`,r,c,this.animationValues);r.render();for(const u in c)r.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);r.scheduleRender()}getProjectionStyles(r){if(!this.instance||this.isSVG)return;if(!this.isVisible)return Ml;const a={visibility:""},l=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,a.opacity="",a.pointerEvents=jt(r==null?void 0:r.pointerEvents)||"",a.transform=l?l(this.latestValues,""):"none",a;const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){const d={};return this.options.layoutId&&(d.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,d.pointerEvents=jt(r==null?void 0:r.pointerEvents)||""),this.hasProjected&&!tt(this.latestValues)&&(d.transform=l?l({},""):"none",this.hasProjected=!1),d}const u=c.animationValues||c.latestValues;this.applyTransformsToTarget(),a.transform=Vl(this.projectionDeltaWithTransform,this.treeScale,u),l&&(a.transform=l(u,a.transform));const{x:h,y:f}=this.projectionDelta;a.transformOrigin=`${h.origin*100}% ${f.origin*100}% 0`,c.animationValues?a.opacity=c===this?u.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:u.opacityExit:a.opacity=c===this?u.opacity!==void 0?u.opacity:"":u.opacityExit!==void 0?u.opacityExit:0;for(const d in At){if(u[d]===void 0)continue;const{correct:m,applyTo:p,isCSSVariable:v}=At[d],g=a.transform==="none"?u[d]:m(u[d],c);if(p){const S=p.length;for(let y=0;y<S;y++)a[p[y]]=g}else v?this.options.visualElement.renderState.vars[d]=g:a[d]=g}return this.options.layoutId&&(a.pointerEvents=c===this?jt(r==null?void 0:r.pointerEvents)||"":"none"),a}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)==null?void 0:a.stop()}),this.root.nodes.forEach(ls),this.root.sharedNodes.clear()}}}function Dl(t){t.updateLayout()}function Rl(t){var n;const e=((n=t.resumeFrom)==null?void 0:n.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:o}=t.options,r=e.source!==t.layout.source;o==="size"?U(h=>{const f=r?e.measuredBox[h]:e.layoutBox[h],d=B(f);f.min=s[h].min,f.max=f.min+d}):Oi(o,e.layoutBox,s)&&U(h=>{const f=r?e.measuredBox[h]:e.layoutBox[h],d=B(s[h]);f.max=f.min+d,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+d)});const a=at();St(a,s,e.layoutBox);const l=at();r?St(l,t.applyTransform(i,!0),e.measuredBox):St(l,s,e.layoutBox);const c=!Li(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const m=D();Pt(m,e.layoutBox,f.layoutBox);const p=D();Pt(p,s,d.layoutBox),Fi(m,p)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=p,t.relativeTargetOrigin=m,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:e,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function El(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Ll(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Fl(t){t.clearSnapshot()}function ls(t){t.clearMeasurements()}function kl(t){t.isLayoutDirty=!1}function Bl(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function us(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Ol(t){t.resolveTargetDelta()}function Il(t){t.calcProjection()}function jl(t){t.resetSkewAndRotation()}function Nl(t){t.removeLeadSnapshot()}function cs(t,e,n){t.translate=V(e.translate,0,n),t.scale=V(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function hs(t,e,n,s){t.min=V(e.min,n.min,s),t.max=V(e.max,n.max,s)}function Ul(t,e,n,s){hs(t.x,e.x,n.x,s),hs(t.y,e.y,n.y,s)}function Kl(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const Wl={duration:.45,ease:[.4,0,.1,1]},fs=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),ds=fs("applewebkit/")&&!fs("chrome/")?Math.round:K;function ms(t){t.min=ds(t.min),t.max=ds(t.max)}function $l(t){ms(t.x),ms(t.y)}function Oi(t,e,n){return t==="position"||t==="preserve-aspect"&&!Ga(rs(e),rs(n),.2)}function Gl(t){var e;return t!==t.root&&((e=t.scroll)==null?void 0:e.wasRoot)}const Hl=Bi({attachResizeListener:(t,e)=>Rt(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),oe={current:void 0},Ii=Bi({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!oe.current){const t=new Hl({});t.mount(window),t.setOptions({layoutScroll:!0}),oe.current=t}return oe.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),_l={pan:{Feature:ll},drag:{Feature:al,ProjectionNode:Ii,MeasureLayout:Di}};function zl(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){let s=document;const i=(n==null?void 0:n[t])??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}function ji(t,e){const n=zl(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function ps(t){return!(t.pointerType==="touch"||Ci())}function Xl(t,e,n={}){const[s,i,o]=ji(t,n),r=a=>{if(!ps(a))return;const{target:l}=a,c=e(l,a);if(typeof c!="function"||!l)return;const u=h=>{ps(h)&&(c(h),l.removeEventListener("pointerleave",u))};l.addEventListener("pointerleave",u,i)};return s.forEach(a=>{a.addEventListener("pointerenter",r,i)}),o}function gs(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,o=s[i];o&&M.postRender(()=>o(e,kt(e)))}class Yl extends J{mount(){const{current:e}=this.node;e&&(this.unmount=Xl(e,(n,s)=>(gs(this.node,s,"Start"),i=>gs(this.node,i,"End"))))}unmount(){}}class ql extends J{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Lt(Rt(this.node.current,"focus",()=>this.onFocus()),Rt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Ni=(t,e)=>e?t===e?!0:Ni(t,e.parentElement):!1,Zl=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Jl(t){return Zl.has(t.tagName)||t.tabIndex!==-1}const yt=new WeakSet;function ys(t){return e=>{e.key==="Enter"&&t(e)}}function ae(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const Ql=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=ys(()=>{if(yt.has(n))return;ae(n,"down");const i=ys(()=>{ae(n,"up")}),o=()=>ae(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",o,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function vs(t){return on(t)&&!Ci()}function tu(t,e,n={}){const[s,i,o]=ji(t,n),r=a=>{const l=a.currentTarget;if(!vs(a)||yt.has(l))return;yt.add(l);const c=e(l,a),u=(d,m)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),!(!vs(d)||!yt.has(l))&&(yt.delete(l),typeof c=="function"&&c(d,{success:m}))},h=d=>{u(d,l===window||l===document||n.useGlobalTarget||Ni(l,d.target))},f=d=>{u(d,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",f,i)};return s.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",r,i),a instanceof HTMLElement&&(a.addEventListener("focus",c=>Ql(c,i)),!Jl(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),o}function Ts(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),o=s[i];o&&M.postRender(()=>o(e,kt(e)))}class eu extends J{mount(){const{current:e}=this.node;e&&(this.unmount=tu(e,(n,s)=>(Ts(this.node,s,"Start"),(i,{success:o})=>Ts(this.node,i,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Ve=new WeakMap,le=new WeakMap,nu=t=>{const e=Ve.get(t.target);e&&e(t)},su=t=>{t.forEach(nu)};function iu({root:t,...e}){const n=t||document;le.has(n)||le.set(n,{});const s=le.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(su,{root:t,...e})),s[i]}function ru(t,e,n){const s=iu(e);return Ve.set(t,n),s.observe(t),()=>{Ve.delete(t),s.unobserve(t)}}const ou={some:0,all:1};class au extends J{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:ou[i]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=c?u:h;f&&f(l)};return ru(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(lu(e,n))&&this.startObserver()}unmount(){}}function lu({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const uu={inView:{Feature:au},tap:{Feature:eu},focus:{Feature:ql},hover:{Feature:Yl}},cu={layout:{ProjectionNode:Ii,MeasureLayout:Di}},Me={current:null},Ui={current:!1};function hu(){if(Ui.current=!0,!!De)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Me.current=t.matches;t.addListener(e),e()}else Me.current=!1}const fu=new WeakMap;function du(t,e,n){for(const s in e){const i=e[s],o=n[s];if(F(i))t.addValue(s,i);else if(F(o))t.addValue(s,Mt(i,{owner:t}));else if(o!==i)if(t.hasValue(s)){const r=t.getValue(s);r.liveStyle===!0?r.jump(i):r.hasAnimated||r.set(i)}else{const r=t.getStaticValue(s);t.addValue(s,Mt(r!==void 0?r:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const Ki=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Wi=t=>/^0[^.\s]+$/u.test(t),mu={test:t=>t==="auto",parse:t=>t},$i=t=>e=>e.test(t),Gi=[mt,T,$,Y,br,Pr,mu],xs=t=>Gi.find($i(t)),pu=[...Gi,L,Z],gu=t=>pu.find($i(t)),yu=new Set(["brightness","contrast","saturate","opacity"]);function vu(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(Xe)||[];if(!s)return t;const i=n.replace(s,"");let o=yu.has(e)?1:0;return s!==n&&(o*=100),e+"("+o+i+")"}const Tu=/\b([a-z-]*)\(.*?\)/gu,Ce={...Z,getAnimatableNone:t=>{const e=t.match(Tu);return e?e.map(vu).join(" "):t}},xu={...je,color:L,backgroundColor:L,outlineColor:L,fill:L,stroke:L,borderColor:L,borderTopColor:L,borderRightColor:L,borderBottomColor:L,borderLeftColor:L,filter:Ce,WebkitFilter:Ce},Hi=t=>xu[t];function _i(t,e){let n=Hi(t);return n!==Ce&&(n=Z),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Ss=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Su{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:o,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=nn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=I.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,M.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=r;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Xt(n),this.isVariantNode=Ms(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in h){const d=h[f];l[f]!==void 0&&F(d)&&d.set(l[f],!1)}}mount(e){this.current=e,fu.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),Ui.current||hu(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Me.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),q(this.notifyUpdate),q(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=dt.has(e);s&&this.onBindTransform&&this.onBindTransform();const i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&M.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),o(),r&&r(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in ct){const n=ct[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):D()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<Ss.length;s++){const i=Ss[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o="on"+i,r=e[o];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=du(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=Mt(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){let s=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return s!=null&&(typeof s=="string"&&(Ki(s)||Wi(s))?s=parseFloat(s):!gu(s)&&Z.test(n)&&(s=_i(e,n)),this.setBaseTarget(e,F(s)?s.get():s)),F(s)?s.get():s}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var o;const{initial:n}=this.props;let s;if(typeof n=="string"||typeof n=="object"){const r=We(this.props,n,(o=this.presenceContext)==null?void 0:o.custom);r&&(s=r[e])}if(n&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,e);return i!==void 0&&!F(i)?i:this.initialValues[e]!==void 0&&s===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new _e),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}const Pu=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function bu(t){const e=Pu.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function zi(t,e,n=1){const[s,i]=bu(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const r=o.trim();return Ki(r)?parseFloat(r):r}return Ie(i)?zi(i,e,n+1):i}function Au(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Wi(t):!0}const wu=new Set(["auto","none","0"]);function Vu(t,e,n){let s=0,i;for(;s<t.length&&!i;){const o=t[s];typeof o=="string"&&!wu.has(o)&&Ct(o).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=_i(n,i)}class Mu extends nn{constructor(e,n,s,i,o){super(e,n,s,i,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let c=e[l];if(typeof c=="string"&&(c=c.trim(),Ie(c))){const u=zi(c,n.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!pi.has(s)||e.length!==2)return;const[i,o]=e,r=xs(i),a=xs(o);if(r!==a)if(wn(r)&&wn(a))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)(e[i]===null||Au(e[i]))&&s.push(i);s.length&&Vu(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ht[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var a;const{element:e,name:n,unresolvedKeyframes:s}=this;if(!e||!e.current)return;const i=e.getValue(n);i&&i.jump(this.measuredOrigin,!1);const o=s.length-1,r=s[o];s[o]=ht[n](e.measureViewportBox(),window.getComputedStyle(e.current)),r!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=r),(a=this.removedTransforms)!=null&&a.length&&this.removedTransforms.forEach(([l,c])=>{e.getValue(l).set(c)}),this.resolveNoneKeyframes()}}class Xi extends Su{constructor(){super(...arguments),this.KeyframeResolver=Mu}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;F(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Yi(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const o in n)t.style.setProperty(o,n[o])}function Cu(t){return window.getComputedStyle(t)}class Du extends Xi{constructor(){super(...arguments),this.type="html",this.renderInstance=Yi}readValueFromInstance(e,n){if(dt.has(n))return Qo(e,n);{const s=Cu(e),i=(Oe(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Ai(e,n)}build(e,n,s){Ne(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return $e(e,n,s)}}const qi=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Ru(t,e,n,s){Yi(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(qi.has(i)?i:Fe(i),e.attrs[i])}class Eu extends Xi{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=D}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(dt.has(n)){const s=Hi(n);return s&&s.default||0}return n=qi.has(n)?n:Fe(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return Ns(e,n,s)}build(e,n,s){Bs(e,n,this.isSVGTag,s.transformTemplate)}renderInstance(e,n,s,i){Ru(e,n,s,i)}mount(e){this.isSVGTag=Is(e.tagName),super.mount(e)}}const Lu=(t,e)=>Ke(t)?new Eu(e):new Du(e,{allowProjection:t!==P.Fragment}),Fu=Kr({...Oa,...uu,..._l,...cu},Lu),Nu=ar(Fu);export{li as J,Ps as L,bs as M,Re as P,Mt as a,Qi as b,tr as c,M as f,F as i,Nu as m,K as n,Ji as u};
