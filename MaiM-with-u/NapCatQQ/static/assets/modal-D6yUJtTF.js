var Zl=Object.defineProperty;var Xl=(n,t,e)=>t in n?Zl(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var j=(n,t,e)=>Xl(n,typeof t!="symbol"?t+"":t,e);import{m as Yl,au as pt,n as Ji,j as m,E as Ql,H as Jl,I as to,J as eo,o as so,p as ro,t as vt,b as ct,a3 as Pt,a4 as Ut,a5 as $t,aw as no,d as io,i as lo,ax as oo,ay as ui,az as ao,an as co,ao as uo,ap as ho,aq as fo,ar as po,as as go}from"./index-D8VBA_Ei.js";import{r as K}from"./react-router-dom-Bk_r5m4S.js";import{V as us}from"./react-hot-toast-RI2B8J99.js";import{c as ve,g as Wn}from"./react-dom-DoC2WAmd.js";import{p as tl,j as hi,q as mo,i as el,s as Zn,t as sl,u as xr,a as bo,v as yo,w as vo,x as xo,S as No,y as Eo,f as wo,z as Ao,g as Co,h as To,m as Ee}from"./_baseIsEqual-C27Ppj04.js";import{u as Lo}from"./use-dialog-DKWVyT14.js";import{i as Gt}from"./chunk-2QAN2V2R-D3FHY7xI.js";import{g as di,h as So,i as qo}from"./index-lkyIzG0M.js";import{e as Er,f as wr,g as ko,h as _o,i as Oo,j as Io}from"./index-Ca-jOSo7.js";import{i as is}from"./url-ByheBKOA.js";import{c as Ro,d as jo}from"./index-D430si04.js";import{r as Mo}from"./qface-Cs62R3Iw.js";import{G as rl}from"./react-icons-MWc89PtZ.js";import{F as Bo}from"./chunk-SLABUSGS-O-e1xCFE.js";import{s as Do}from"./chunk-KVDW62ZT-Ck4XsWki.js";import{l as Po}from"./chunk-O4OUNAUT-SDb7E44K.js";import{t as Uo,a as fi}from"./chunk-ML27DD5T-DYtZtKiQ.js";import{u as $o,C as Ts}from"./react-hook-form-DVPsaNoQ.js";var bn=Yl({slots:{base:"inline-flex items-center justify-between h-fit rounded-large gap-2",pre:"bg-transparent text-inherit font-mono font-normal inline-block whitespace-nowrap",content:"flex flex-col",symbol:"select-none",copyButton:["group","relative","z-10","text-large","text-inherit","data-[hover=true]:bg-transparent"],copyIcon:["absolute text-inherit opacity-100 scale-100 group-data-[copied=true]:opacity-0 group-data-[copied=true]:scale-50"],checkIcon:["absolute text-inherit opacity-0 scale-50 group-data-[copied=true]:opacity-100 group-data-[copied=true]:scale-100"]},variants:{variant:{flat:"",solid:"",bordered:"border-medium bg-transparent",shadow:""},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},size:{sm:{base:"px-1.5 py-0.5 text-tiny rounded-small"},md:{base:"px-3 py-1.5 text-small rounded-medium"},lg:{base:"px-4 py-2 text-medium rounded-large"}},radius:{none:{base:"rounded-none"},sm:{base:"rounded-small"},md:{base:"rounded-medium"},lg:{base:"rounded-large"}},fullWidth:{true:{base:"w-full"}},disableAnimation:{true:{},false:{copyIcon:"transition-transform-opacity",checkIcon:"transition-transform-opacity"}}},defaultVariants:{color:"default",variant:"flat",size:"md",fullWidth:!1},compoundVariants:[{variant:["solid","shadow"],color:"default",class:{copyButton:"data-[focus-visible]:outline-default-foreground"}},{variant:["solid","shadow"],color:"primary",class:{copyButton:"data-[focus-visible]:outline-primary-foreground"}},{variant:["solid","shadow"],color:"secondary",class:{copyButton:"data-[focus-visible]:outline-secondary-foreground"}},{variant:["solid","shadow"],color:"success",class:{copyButton:"data-[focus-visible]:outline-success-foreground"}},{variant:["solid","shadow"],color:"warning",class:{copyButton:"data-[focus-visible]:outline-warning-foreground"}},{variant:["solid","shadow"],color:"danger",class:{copyButton:"data-[focus-visible]:outline-danger-foreground"}},{variant:"flat",color:"default",class:{base:pt.flat.default}},{variant:"flat",color:"primary",class:{base:pt.flat.primary}},{variant:"flat",color:"secondary",class:{base:pt.flat.secondary}},{variant:"flat",color:"success",class:{base:pt.flat.success}},{variant:"flat",color:"warning",class:{base:pt.flat.warning}},{variant:"flat",color:"danger",class:{base:pt.flat.danger}},{variant:"solid",color:"default",class:{base:pt.solid.default}},{variant:"solid",color:"primary",class:{base:pt.solid.primary}},{variant:"solid",color:"secondary",class:{base:pt.solid.secondary}},{variant:"solid",color:"success",class:{base:pt.solid.success}},{variant:"solid",color:"warning",class:{base:pt.solid.warning}},{variant:"solid",color:"danger",class:{base:pt.solid.danger}},{variant:"shadow",color:"default",class:{base:pt.shadow.default}},{variant:"shadow",color:"primary",class:{base:pt.shadow.primary}},{variant:"shadow",color:"secondary",class:{base:pt.shadow.secondary}},{variant:"shadow",color:"success",class:{base:pt.shadow.success}},{variant:"shadow",color:"warning",class:{base:pt.shadow.warning}},{variant:"shadow",color:"danger",class:{base:pt.shadow.danger}},{variant:"bordered",color:"default",class:{base:pt.bordered.default}},{variant:"bordered",color:"primary",class:{base:pt.bordered.primary}},{variant:"bordered",color:"secondary",class:{base:pt.bordered.secondary}},{variant:"bordered",color:"success",class:{base:pt.bordered.success}},{variant:"bordered",color:"warning",class:{base:pt.bordered.warning}},{variant:"bordered",color:"danger",class:{base:pt.bordered.danger}}]}),Fo=K.forwardRef(function(t,e){var s,r;const i=Ji(),o=(r=(s=t.validationBehavior)!=null?s:i==null?void 0:i.validationBehavior)!=null?r:"aria";return m.jsx(Bo,{...t,ref:e,validationBehavior:o})});function Ho(n){const t=K.useRef(null);return K.useImperativeHandle(n,()=>t.current),t}var zo=n=>n?"true":void 0;function nl(n){var t,e,s="";if(typeof n=="string"||typeof n=="number")s+=n;else if(typeof n=="object")if(Array.isArray(n))for(t=0;t<n.length;t++)n[t]&&(e=nl(n[t]))&&(s&&(s+=" "),s+=e);else for(t in n)n[t]&&(s&&(s+=" "),s+=t);return s}function pi(...n){for(var t=0,e,s,r="";t<n.length;)(e=n[t++])&&(s=nl(e))&&(r&&(r+=" "),r+=s);return r}var Vo=Object.create,il=Object.defineProperty,Go=Object.getOwnPropertyDescriptor,ll=Object.getOwnPropertyNames,Ko=Object.getPrototypeOf,Wo=Object.prototype.hasOwnProperty,ol=(n,t)=>function(){return t||(0,n[ll(n)[0]])((t={exports:{}}).exports,t),t.exports},Zo=(n,t,e,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of ll(t))!Wo.call(n,r)&&r!==e&&il(n,r,{get:()=>t[r],enumerable:!(s=Go(t,r))||s.enumerable});return n},Xo=(n,t,e)=>(e=n!=null?Vo(Ko(n)):{},Zo(!n||!n.__esModule?il(e,"default",{value:n,enumerable:!0}):e,n)),Yo=ol({"../../../node_modules/.pnpm/react@18.2.0/node_modules/react/cjs/react.production.min.js"(n){var t=Symbol.for("react.element"),e=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),o=Symbol.for("react.provider"),a=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),b=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),d=Symbol.iterator;function f(y){return y===null||typeof y!="object"?null:(y=d&&y[d]||y["@@iterator"],typeof y=="function"?y:null)}var v={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},E=Object.assign,C={};function S(y,A,D){this.props=y,this.context=A,this.refs=C,this.updater=D||v}S.prototype.isReactComponent={},S.prototype.setState=function(y,A){if(typeof y!="object"&&typeof y!="function"&&y!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,y,A,"setState")},S.prototype.forceUpdate=function(y){this.updater.enqueueForceUpdate(this,y,"forceUpdate")};function M(){}M.prototype=S.prototype;function P(y,A,D){this.props=y,this.context=A,this.refs=C,this.updater=D||v}var tt=P.prototype=new M;tt.constructor=P,E(tt,S.prototype),tt.isPureReactComponent=!0;var U=Array.isArray,Q=Object.prototype.hasOwnProperty,rt={current:null},st={key:!0,ref:!0,__self:!0,__source:!0};function ht(y,A,D){var H,I={},Y=null,J=null;if(A!=null)for(H in A.ref!==void 0&&(J=A.ref),A.key!==void 0&&(Y=""+A.key),A)Q.call(A,H)&&!st.hasOwnProperty(H)&&(I[H]=A[H]);var z=arguments.length-2;if(z===1)I.children=D;else if(1<z){for(var Z=Array(z),X=0;X<z;X++)Z[X]=arguments[X+2];I.children=Z}if(y&&y.defaultProps)for(H in z=y.defaultProps,z)I[H]===void 0&&(I[H]=z[H]);return{$$typeof:t,type:y,key:Y,ref:J,props:I,_owner:rt.current}}function ft(y,A){return{$$typeof:t,type:y.type,key:A,ref:y.ref,props:y.props,_owner:y._owner}}function g(y){return typeof y=="object"&&y!==null&&y.$$typeof===t}function q(y){var A={"=":"=0",":":"=2"};return"$"+y.replace(/[=:]/g,function(D){return A[D]})}var N=/\/+/g;function O(y,A){return typeof y=="object"&&y!==null&&y.key!=null?q(""+y.key):A.toString(36)}function k(y,A,D,H,I){var Y=typeof y;(Y==="undefined"||Y==="boolean")&&(y=null);var J=!1;if(y===null)J=!0;else switch(Y){case"string":case"number":J=!0;break;case"object":switch(y.$$typeof){case t:case e:J=!0}}if(J)return J=y,I=I(J),y=H===""?"."+O(J,0):H,U(I)?(D="",y!=null&&(D=y.replace(N,"$&/")+"/"),k(I,A,D,"",function(X){return X})):I!=null&&(g(I)&&(I=ft(I,D+(!I.key||J&&J.key===I.key?"":(""+I.key).replace(N,"$&/")+"/")+y)),A.push(I)),1;if(J=0,H=H===""?".":H+":",U(y))for(var z=0;z<y.length;z++){Y=y[z];var Z=H+O(Y,z);J+=k(Y,A,D,Z,I)}else if(Z=f(y),typeof Z=="function")for(y=Z.call(y),z=0;!(Y=y.next()).done;)Y=Y.value,Z=H+O(Y,z++),J+=k(Y,A,D,Z,I);else if(Y==="object")throw A=String(y),Error("Objects are not valid as a React child (found: "+(A==="[object Object]"?"object with keys {"+Object.keys(y).join(", ")+"}":A)+"). If you meant to render a collection of children, use an array instead.");return J}function L(y,A,D){if(y==null)return y;var H=[],I=0;return k(y,H,"","",function(Y){return A.call(D,Y,I++)}),H}function _(y){if(y._status===-1){var A=y._result;A=A(),A.then(function(D){(y._status===0||y._status===-1)&&(y._status=1,y._result=D)},function(D){(y._status===0||y._status===-1)&&(y._status=2,y._result=D)}),y._status===-1&&(y._status=0,y._result=A)}if(y._status===1)return y._result.default;throw y._result}var R={current:null},F={transition:null},B={ReactCurrentDispatcher:R,ReactCurrentBatchConfig:F,ReactCurrentOwner:rt};n.Children={map:L,forEach:function(y,A,D){L(y,function(){A.apply(this,arguments)},D)},count:function(y){var A=0;return L(y,function(){A++}),A},toArray:function(y){return L(y,function(A){return A})||[]},only:function(y){if(!g(y))throw Error("React.Children.only expected to receive a single React element child.");return y}},n.Component=S,n.Fragment=s,n.Profiler=i,n.PureComponent=P,n.StrictMode=r,n.Suspense=h,n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=B,n.cloneElement=function(y,A,D){if(y==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+y+".");var H=E({},y.props),I=y.key,Y=y.ref,J=y._owner;if(A!=null){if(A.ref!==void 0&&(Y=A.ref,J=rt.current),A.key!==void 0&&(I=""+A.key),y.type&&y.type.defaultProps)var z=y.type.defaultProps;for(Z in A)Q.call(A,Z)&&!st.hasOwnProperty(Z)&&(H[Z]=A[Z]===void 0&&z!==void 0?z[Z]:A[Z])}var Z=arguments.length-2;if(Z===1)H.children=D;else if(1<Z){z=Array(Z);for(var X=0;X<Z;X++)z[X]=arguments[X+2];H.children=z}return{$$typeof:t,type:y.type,key:I,ref:Y,props:H,_owner:J}},n.createContext=function(y){return y={$$typeof:a,_currentValue:y,_currentValue2:y,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},y.Provider={$$typeof:o,_context:y},y.Consumer=y},n.createElement=ht,n.createFactory=function(y){var A=ht.bind(null,y);return A.type=y,A},n.createRef=function(){return{current:null}},n.forwardRef=function(y){return{$$typeof:u,render:y}},n.isValidElement=g,n.lazy=function(y){return{$$typeof:x,_payload:{_status:-1,_result:y},_init:_}},n.memo=function(y,A){return{$$typeof:b,type:y,compare:A===void 0?null:A}},n.startTransition=function(y){var A=F.transition;F.transition={};try{y()}finally{F.transition=A}},n.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},n.useCallback=function(y,A){return R.current.useCallback(y,A)},n.useContext=function(y){return R.current.useContext(y)},n.useDebugValue=function(){},n.useDeferredValue=function(y){return R.current.useDeferredValue(y)},n.useEffect=function(y,A){return R.current.useEffect(y,A)},n.useId=function(){return R.current.useId()},n.useImperativeHandle=function(y,A,D){return R.current.useImperativeHandle(y,A,D)},n.useInsertionEffect=function(y,A){return R.current.useInsertionEffect(y,A)},n.useLayoutEffect=function(y,A){return R.current.useLayoutEffect(y,A)},n.useMemo=function(y,A){return R.current.useMemo(y,A)},n.useReducer=function(y,A,D){return R.current.useReducer(y,A,D)},n.useRef=function(y){return R.current.useRef(y)},n.useState=function(y){return R.current.useState(y)},n.useSyncExternalStore=function(y,A,D){return R.current.useSyncExternalStore(y,A,D)},n.useTransition=function(){return R.current.useTransition()},n.version="18.2.0"}}),Qo=ol({"../../../node_modules/.pnpm/react@18.2.0/node_modules/react/index.js"(n,t){t.exports=Yo()}});Xo(Qo());function al(n){if(!n||typeof n!="object")return"";try{return JSON.stringify(n)}catch{return""}}/**
 * @license React
 * react.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *//**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jo=new Set(["id","type","style","title","role","tabIndex","htmlFor","width","height","abbr","accept","acceptCharset","accessKey","action","allowFullScreen","allowTransparency","alt","async","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","challenge","charset","checked","cite","class","className","cols","colSpan","command","content","contentEditable","contextMenu","controls","coords","crossOrigin","data","dateTime","default","defer","dir","disabled","download","draggable","dropzone","encType","enterKeyHint","for","form","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","headers","hidden","high","href","hrefLang","httpEquiv","icon","inputMode","isMap","itemId","itemProp","itemRef","itemScope","itemType","kind","label","lang","list","loop","manifest","max","maxLength","media","mediaGroup","method","min","minLength","multiple","muted","name","noValidate","open","optimum","pattern","ping","placeholder","poster","preload","radioGroup","referrerPolicy","readOnly","rel","required","rows","rowSpan","sandbox","scope","scoped","scrolling","seamless","selected","shape","size","sizes","slot","sortable","span","spellCheck","src","srcDoc","srcSet","start","step","target","translate","typeMustMatch","useMap","value","wmode","wrap"]),ta=new Set(["onCopy","onCut","onPaste","onLoad","onError","onWheel","onScroll","onCompositionEnd","onCompositionStart","onCompositionUpdate","onKeyDown","onKeyPress","onKeyUp","onFocus","onBlur","onChange","onInput","onSubmit","onClick","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onPointerDown","onPointerEnter","onPointerLeave","onPointerUp","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionEnd"]),gi=/^(data-.*)$/,ea=/^(aria-.*)$/,or=/^(on[A-Z].*)$/;function sa(n,t={}){let{labelable:e=!0,enabled:s=!0,propNames:r,omitPropNames:i,omitEventNames:o,omitDataProps:a,omitEventProps:u}=t,h={};if(!s)return n;for(const b in n)i!=null&&i.has(b)||o!=null&&o.has(b)&&or.test(b)||or.test(b)&&!ta.has(b)||a&&gi.test(b)||u&&or.test(b)||(Object.prototype.hasOwnProperty.call(n,b)&&(Jo.has(b)||e&&ea.test(b)||r!=null&&r.has(b)||gi.test(b))||or.test(b))&&(h[b]=n[b]);return h}var ra=n=>n.replace(/[\u00A0]/g," ");function na({timeout:n=2e3}={}){const[t,e]=K.useState(null),[s,r]=K.useState(!1),[i,o]=K.useState(null),a=K.useCallback(()=>{i&&clearTimeout(i)},[i]),u=K.useCallback(x=>{a(),o(setTimeout(()=>r(!1),n)),r(x)},[a,n]),h=K.useCallback(x=>{if("clipboard"in navigator){const d=typeof x=="string"?ra(x):x;navigator.clipboard.writeText(d).then(()=>u(!0)).catch(f=>e(f))}else e(new Error("useClipboard: navigator.clipboard is not supported"))},[u]),b=K.useCallback(()=>{r(!1),e(null),a()},[a]);return{copy:h,reset:b,error:t,copied:s}}function ia(n={}){let{autoFocus:t=!1,isTextInput:e,within:s}=n,r=K.useRef({isFocused:!1,isFocusVisible:t||Ql()}),[i,o]=K.useState(!1),[a,u]=K.useState(()=>r.current.isFocused&&r.current.isFocusVisible),h=K.useCallback(()=>u(r.current.isFocused&&r.current.isFocusVisible),[]),b=K.useCallback(f=>{r.current.isFocused=f,o(f),h()},[h]);Jl(f=>{r.current.isFocusVisible=f,h()},[],{isTextInput:e});let{focusProps:x}=to({isDisabled:s,onFocusChange:b}),{focusWithinProps:d}=eo({isDisabled:!s,onFocusWithinChange:b});return{isFocused:i,isFocusVisible:a,focusProps:s?d:x}}function la(n){var t,e,s,r;const i=Ji(),[o,a]=so(n,bn.variantKeys),{ref:u,as:h,children:b,symbol:x="$",classNames:d,timeout:f,copyIcon:v,checkIcon:E,codeString:C,disableCopy:S=!1,disableTooltip:M=!1,hideCopyButton:P=!1,autoFocus:tt=!1,hideSymbol:U=!1,onCopy:Q,tooltipProps:rt={},copyButtonProps:st={},className:ht,...ft}=o,g=h||"div",q=typeof g=="string",N=(e=(t=n==null?void 0:n.disableAnimation)!=null?t:i==null?void 0:i.disableAnimation)!=null?e:!1,O={offset:15,delay:1e3,content:"Copy to clipboard",color:(r=n==null?void 0:n.color)!=null?r:(s=bn.defaultVariants)==null?void 0:s.color,isDisabled:o.disableCopy,...rt},k=Ho(u),L=K.useRef(null),{copy:_,copied:R}=na({timeout:f}),F=b&&Array.isArray(b),{isFocusVisible:B,isFocused:y,focusProps:A}=ia({autoFocus:tt}),D=K.useMemo(()=>bn({...a,disableAnimation:N}),[al(a),N]),H=K.useMemo(()=>{if(!x||typeof x!="string")return x;const X=x.trim();return X?`${X} `:""},[x]),I=pi(d==null?void 0:d.base,ht),Y=K.useCallback(()=>({className:D.base({class:I}),...sa(ft,{enabled:q})}),[D,I,F,ft]),J=K.useCallback(()=>{var X;if(S)return;let nt="";typeof b=="string"?nt=b:Array.isArray(b)&&b.forEach(it=>{var gt,bt;const ge=typeof it=="string"?it:(bt=(gt=it==null?void 0:it.props)==null?void 0:gt.children)==null?void 0:bt.toString();ge&&(nt+=ge+`
`)});const ot=C||nt||((X=L.current)==null?void 0:X.textContent)||"";_(ot),Q==null||Q(ot)},[_,C,S,Q,b]),z={"aria-label":typeof O.content=="string"?O.content:"Copy to clipboard",size:"sm",variant:"light",isDisabled:S,onPress:J,isIconOnly:!0,...st},Z=K.useCallback(()=>({...z,"data-copied":zo(R),className:D.copyButton({class:pi(d==null?void 0:d.copyButton)})}),[D,B,y,S,d==null?void 0:d.copyButton,z,A]);return{Component:g,as:h,domRef:k,preRef:L,children:b,slots:D,classNames:d,copied:R,onCopy:J,copyIcon:v,checkIcon:E,symbolBefore:H,isMultiLine:F,isFocusVisible:B,hideCopyButton:P,disableCopy:S,disableTooltip:M,hideSymbol:U,tooltipProps:O,getSnippetProps:Y,getCopyButtonProps:Z}}var oa=n=>m.jsxs("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",viewBox:"0 0 24 24",width:"1em",...n,children:[m.jsx("path",{d:"M16 17.1c0 3.5-1.4 4.9-4.9 4.9H6.9C3.4 22 2 20.6 2 17.1v-4.2C2 9.4 3.4 8 6.9 8h4.2c3.5 0 4.9 1.4 4.9 4.9Z"}),m.jsx("path",{d:"M8 8V6.9C8 3.4 9.4 2 12.9 2h4.2C20.6 2 22 3.4 22 6.9v4.2c0 3.5-1.4 4.9-4.9 4.9H16"}),m.jsx("path",{d:"M16 12.9C16 9.4 14.6 8 11.1 8"})]}),aa=n=>m.jsx("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,viewBox:"0 0 24 24",width:"1em",...n,children:m.jsx("polyline",{points:"20 6 9 17 4 12"})}),cl=ro((n,t)=>{const{Component:e,domRef:s,preRef:r,children:i,slots:o,classNames:a,copied:u,copyIcon:h=m.jsx(oa,{}),checkIcon:b=m.jsx(aa,{}),symbolBefore:x,disableCopy:d,disableTooltip:f,hideSymbol:v,hideCopyButton:E,tooltipProps:C,isMultiLine:S,onCopy:M,getSnippetProps:P,getCopyButtonProps:tt}=la({...n,ref:t}),U=K.useCallback(({children:st})=>m.jsx(vt,{...C,isDisabled:u||C.isDisabled,children:st}),[al(C)]),Q=K.useMemo(()=>{if(E)return null;const st=b&&K.cloneElement(b,{className:o.checkIcon()}),ht=h&&K.cloneElement(h,{className:o.copyIcon()}),ft=m.jsxs(ct,{...tt(),children:[st,ht]});return f?ft:m.jsx(U,{children:ft})},[o,a==null?void 0:a.copyButton,u,b,h,M,U,d,f,E]),rt=K.useMemo(()=>S&&i&&Array.isArray(i)?m.jsx("div",{className:o.content({class:a==null?void 0:a.content}),children:i.map((st,ht)=>m.jsxs("pre",{className:o.pre({class:a==null?void 0:a.pre}),children:[!v&&m.jsx("span",{className:o.symbol({class:a==null?void 0:a.symbol}),children:x}),st]},`${ht}-${st}`))}):m.jsxs("pre",{ref:r,className:o.pre({class:a==null?void 0:a.pre}),children:[!v&&m.jsx("span",{className:o.symbol({class:a==null?void 0:a.symbol}),children:x}),i]}),[i,v,S,x,a==null?void 0:a.pre,o]);return m.jsxs(e,{ref:s,...P(),children:[rt,Q]})});cl.displayName="HeroUI.Snippet";var ca=cl,ua=Object.prototype,ha=ua.hasOwnProperty;function da(n){var t=n.length,e=new n.constructor(t);return t&&typeof n[0]=="string"&&ha.call(n,"index")&&(e.index=n.index,e.input=n.input),e}function fa(n,t){var e=tl(n.buffer);return new n.constructor(e,n.byteOffset,n.byteLength)}var pa=/\w*$/;function ga(n){var t=new n.constructor(n.source,pa.exec(n));return t.lastIndex=n.lastIndex,t}var mi=hi?hi.prototype:void 0,bi=mi?mi.valueOf:void 0;function ma(n){return bi?Object(bi.call(n)):{}}var ba="[object Boolean]",ya="[object Date]",va="[object Map]",xa="[object Number]",Na="[object RegExp]",Ea="[object Set]",wa="[object String]",Aa="[object Symbol]",Ca="[object ArrayBuffer]",Ta="[object DataView]",La="[object Float32Array]",Sa="[object Float64Array]",qa="[object Int8Array]",ka="[object Int16Array]",_a="[object Int32Array]",Oa="[object Uint8Array]",Ia="[object Uint8ClampedArray]",Ra="[object Uint16Array]",ja="[object Uint32Array]";function Ma(n,t,e){var s=n.constructor;switch(t){case Ca:return tl(n);case ba:case ya:return new s(+n);case Ta:return fa(n);case La:case Sa:case qa:case ka:case _a:case Oa:case Ia:case Ra:case ja:return mo(n,e);case va:return new s;case xa:case wa:return new s(n);case Na:return ga(n);case Ea:return new s;case Aa:return ma(n)}}var Ba="[object Map]";function Da(n){return el(n)&&Zn(n)==Ba}var yi=xr&&xr.isMap,Pa=yi?sl(yi):Da,Ua="[object Set]";function $a(n){return el(n)&&Zn(n)==Ua}var vi=xr&&xr.isSet,Fa=vi?sl(vi):$a,Ha=1,ul="[object Arguments]",za="[object Array]",Va="[object Boolean]",Ga="[object Date]",Ka="[object Error]",hl="[object Function]",Wa="[object GeneratorFunction]",Za="[object Map]",Xa="[object Number]",dl="[object Object]",Ya="[object RegExp]",Qa="[object Set]",Ja="[object String]",tc="[object Symbol]",ec="[object WeakMap]",sc="[object ArrayBuffer]",rc="[object DataView]",nc="[object Float32Array]",ic="[object Float64Array]",lc="[object Int8Array]",oc="[object Int16Array]",ac="[object Int32Array]",cc="[object Uint8Array]",uc="[object Uint8ClampedArray]",hc="[object Uint16Array]",dc="[object Uint32Array]",dt={};dt[ul]=dt[za]=dt[sc]=dt[rc]=dt[Va]=dt[Ga]=dt[nc]=dt[ic]=dt[lc]=dt[oc]=dt[ac]=dt[Za]=dt[Xa]=dt[dl]=dt[Ya]=dt[Qa]=dt[Ja]=dt[tc]=dt[cc]=dt[uc]=dt[hc]=dt[dc]=!0;dt[Ka]=dt[hl]=dt[ec]=!1;function br(n,t,e,s,r,i){var o,a=t&Ha;if(o!==void 0)return o;if(!bo(n))return n;var u=Co(n);if(u)o=da(n);else{var h=Zn(n),b=h==hl||h==Wa;if(yo(n))return vo(n,a);if(h==dl||h==ul||b&&!r)o=b?{}:xo(n);else{if(!dt[h])return r?n:{};o=Ma(n,h,a)}}i||(i=new No);var x=i.get(n);if(x)return x;i.set(n,o),Fa(n)?n.forEach(function(v){o.add(br(v,t,e,v,n,i))}):Pa(n)&&n.forEach(function(v,E){o.set(E,br(v,t,e,E,n,i))});var d=Eo,f=u?void 0:d(n);return wo(f||n,function(v,E){f&&(E=v,v=n[E]),Ao(o,E,br(v,t,e,E,n,i))}),o}var fc=1,pc=4;function ss(n){return br(n,fc|pc)}function Xn(n,t){return To(n,t)}var G=(n=>(n[n.TYPE=3]="TYPE",n[n.LEVEL=12]="LEVEL",n[n.ATTRIBUTE=13]="ATTRIBUTE",n[n.BLOT=14]="BLOT",n[n.INLINE=7]="INLINE",n[n.BLOCK=11]="BLOCK",n[n.BLOCK_BLOT=10]="BLOCK_BLOT",n[n.INLINE_BLOT=6]="INLINE_BLOT",n[n.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",n[n.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",n[n.ANY=15]="ANY",n))(G||{});class ie{constructor(t,e,s={}){this.attrName=t,this.keyName=e;const r=G.TYPE&G.ATTRIBUTE;this.scope=s.scope!=null?s.scope&G.LEVEL|r:G.ATTRIBUTE,s.whitelist!=null&&(this.whitelist=s.whitelist)}static keys(t){return Array.from(t.attributes).map(e=>e.name)}add(t,e){return this.canAdd(t,e)?(t.setAttribute(this.keyName,e),!0):!1}canAdd(t,e){return this.whitelist==null?!0:typeof e=="string"?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1}remove(t){t.removeAttribute(this.keyName)}value(t){const e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""}}class rs extends Error{constructor(t){t="[Parchment] "+t,super(t),this.message=t,this.name=this.constructor.name}}const fl=class qn{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(t,e=!1){if(t==null)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(e){let s=null;try{s=t.parentNode}catch{return null}return this.find(s,e)}return null}create(t,e,s){const r=this.query(e);if(r==null)throw new rs(`Unable to create ${e} blot`);const i=r,o=e instanceof Node||e.nodeType===Node.TEXT_NODE?e:i.create(s),a=new i(t,o,s);return qn.blots.set(a.domNode,a),a}find(t,e=!1){return qn.find(t,e)}query(t,e=G.ANY){let s;return typeof t=="string"?s=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?s=this.types.text:typeof t=="number"?t&G.LEVEL&G.BLOCK?s=this.types.block:t&G.LEVEL&G.INLINE&&(s=this.types.inline):t instanceof Element&&((t.getAttribute("class")||"").split(/\s+/).some(r=>(s=this.classes[r],!!s)),s=s||this.tags[t.tagName]),s==null?null:"scope"in s&&e&G.LEVEL&s.scope&&e&G.TYPE&s.scope?s:null}register(...t){return t.map(e=>{const s="blotName"in e,r="attrName"in e;if(!s&&!r)throw new rs("Invalid definition");if(s&&e.blotName==="abstract")throw new rs("Cannot register abstract class");const i=s?e.blotName:r?e.attrName:void 0;return this.types[i]=e,r?typeof e.keyName=="string"&&(this.attributes[e.keyName]=e):s&&(e.className&&(this.classes[e.className]=e),e.tagName&&(Array.isArray(e.tagName)?e.tagName=e.tagName.map(o=>o.toUpperCase()):e.tagName=e.tagName.toUpperCase(),(Array.isArray(e.tagName)?e.tagName:[e.tagName]).forEach(o=>{(this.tags[o]==null||e.className==null)&&(this.tags[o]=e)}))),e})}};fl.blots=new WeakMap;let ls=fl;function xi(n,t){return(n.getAttribute("class")||"").split(/\s+/).filter(e=>e.indexOf(`${t}-`)===0)}class gc extends ie{static keys(t){return(t.getAttribute("class")||"").split(/\s+/).map(e=>e.split("-").slice(0,-1).join("-"))}add(t,e){return this.canAdd(t,e)?(this.remove(t),t.classList.add(`${this.keyName}-${e}`),!0):!1}remove(t){xi(t,this.keyName).forEach(e=>{t.classList.remove(e)}),t.classList.length===0&&t.removeAttribute("class")}value(t){const e=(xi(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""}}const Xt=gc;function yn(n){const t=n.split("-"),e=t.slice(1).map(s=>s[0].toUpperCase()+s.slice(1)).join("");return t[0]+e}class mc extends ie{static keys(t){return(t.getAttribute("style")||"").split(";").map(e=>e.split(":")[0].trim())}add(t,e){return this.canAdd(t,e)?(t.style[yn(this.keyName)]=e,!0):!1}remove(t){t.style[yn(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")}value(t){const e=t.style[yn(this.keyName)];return this.canAdd(t,e)?e:""}}const we=mc;class bc{constructor(t){this.attributes={},this.domNode=t,this.build()}attribute(t,e){e?t.add(this.domNode,e)&&(t.value(this.domNode)!=null?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])}build(){this.attributes={};const t=ls.find(this.domNode);if(t==null)return;const e=ie.keys(this.domNode),s=Xt.keys(this.domNode),r=we.keys(this.domNode);e.concat(s).concat(r).forEach(i=>{const o=t.scroll.query(i,G.ATTRIBUTE);o instanceof ie&&(this.attributes[o.attrName]=o)})}copy(t){Object.keys(this.attributes).forEach(e=>{const s=this.attributes[e].value(this.domNode);t.format(e,s)})}move(t){this.copy(t),Object.keys(this.attributes).forEach(e=>{this.attributes[e].remove(this.domNode)}),this.attributes={}}values(){return Object.keys(this.attributes).reduce((t,e)=>(t[e]=this.attributes[e].value(this.domNode),t),{})}}const Ar=bc,pl=class{constructor(t,e){this.scroll=t,this.domNode=e,ls.blots.set(e,this),this.prev=null,this.next=null}static create(t){if(this.tagName==null)throw new rs("Blot definition missing tagName");let e,s;return Array.isArray(this.tagName)?(typeof t=="string"?(s=t.toUpperCase(),parseInt(s,10).toString()===s&&(s=parseInt(s,10))):typeof t=="number"&&(s=t),typeof s=="number"?e=document.createElement(this.tagName[s-1]):s&&this.tagName.indexOf(s)>-1?e=document.createElement(s):e=document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e}get statics(){return this.constructor}attach(){}clone(){const t=this.domNode.cloneNode(!1);return this.scroll.create(t)}detach(){this.parent!=null&&this.parent.removeChild(this),ls.blots.delete(this.domNode)}deleteAt(t,e){this.isolate(t,e).remove()}formatAt(t,e,s,r){const i=this.isolate(t,e);if(this.scroll.query(s,G.BLOT)!=null&&r)i.wrap(s,r);else if(this.scroll.query(s,G.ATTRIBUTE)!=null){const o=this.scroll.create(this.statics.scope);i.wrap(o),o.format(s,r)}}insertAt(t,e,s){const r=s==null?this.scroll.create("text",e):this.scroll.create(e,s),i=this.split(t);this.parent.insertBefore(r,i||void 0)}isolate(t,e){const s=this.split(t);if(s==null)throw new Error("Attempt to isolate at end");return s.split(e),s}length(){return 1}offset(t=this.parent){return this.parent==null||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)}optimize(t){this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)&&this.wrap(this.statics.requiredContainer.blotName)}remove(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(t,e){const s=typeof t=="string"?this.scroll.create(t,e):t;return this.parent!=null&&(this.parent.insertBefore(s,this.next||void 0),this.remove()),s}split(t,e){return t===0?this:this.next}update(t,e){}wrap(t,e){const s=typeof t=="string"?this.scroll.create(t,e):t;if(this.parent!=null&&this.parent.insertBefore(s,this.next||void 0),typeof s.appendChild!="function")throw new rs(`Cannot wrap ${t}`);return s.appendChild(this),s}};pl.blotName="abstract";let gl=pl;const ml=class extends gl{static value(t){return!0}index(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1}position(t,e){let s=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(s+=1),[this.parent.domNode,s]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};ml.scope=G.INLINE_BLOT;let yc=ml;const Tt=yc;class vc{constructor(){this.head=null,this.tail=null,this.length=0}append(...t){if(this.insertBefore(t[0],null),t.length>1){const e=t.slice(1);this.append(...e)}}at(t){const e=this.iterator();let s=e();for(;s&&t>0;)t-=1,s=e();return s}contains(t){const e=this.iterator();let s=e();for(;s;){if(s===t)return!0;s=e()}return!1}indexOf(t){const e=this.iterator();let s=e(),r=0;for(;s;){if(s===t)return r;r+=1,s=e()}return-1}insertBefore(t,e){t!=null&&(this.remove(t),t.next=e,e!=null?(t.prev=e.prev,e.prev!=null&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):this.tail!=null?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)}offset(t){let e=0,s=this.head;for(;s!=null;){if(s===t)return e;e+=s.length(),s=s.next}return-1}remove(t){this.contains(t)&&(t.prev!=null&&(t.prev.next=t.next),t.next!=null&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)}iterator(t=this.head){return()=>{const e=t;return t!=null&&(t=t.next),e}}find(t,e=!1){const s=this.iterator();let r=s();for(;r;){const i=r.length();if(t<i||e&&t===i&&(r.next==null||r.next.length()!==0))return[r,t];t-=i,r=s()}return[null,0]}forEach(t){const e=this.iterator();let s=e();for(;s;)t(s),s=e()}forEachAt(t,e,s){if(e<=0)return;const[r,i]=this.find(t);let o=t-i;const a=this.iterator(r);let u=a();for(;u&&o<t+e;){const h=u.length();t>o?s(u,t-o,Math.min(e,o+h-t)):s(u,0,Math.min(h,t+e-o)),o+=h,u=a()}}map(t){return this.reduce((e,s)=>(e.push(t(s)),e),[])}reduce(t,e){const s=this.iterator();let r=s();for(;r;)e=t(e,r),r=s();return e}}function Ni(n,t){const e=t.find(n);if(e)return e;try{return t.create(n)}catch{const s=t.create(G.INLINE);return Array.from(n.childNodes).forEach(r=>{s.domNode.appendChild(r)}),n.parentNode&&n.parentNode.replaceChild(s.domNode,n),s.attach(),s}}const bl=class ye extends gl{constructor(t,e){super(t,e),this.uiNode=null,this.build()}appendChild(t){this.insertBefore(t)}attach(){super.attach(),this.children.forEach(t=>{t.attach()})}attachUI(t){this.uiNode!=null&&this.uiNode.remove(),this.uiNode=t,ye.uiClass&&this.uiNode.classList.add(ye.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new vc,Array.from(this.domNode.childNodes).filter(t=>t!==this.uiNode).reverse().forEach(t=>{try{const e=Ni(t,this.scroll);this.insertBefore(e,this.children.head||void 0)}catch(e){if(e instanceof rs)return;throw e}})}deleteAt(t,e){if(t===0&&e===this.length())return this.remove();this.children.forEachAt(t,e,(s,r,i)=>{s.deleteAt(r,i)})}descendant(t,e=0){const[s,r]=this.children.find(e);return t.blotName==null&&t(s)||t.blotName!=null&&s instanceof t?[s,r]:s instanceof ye?s.descendant(t,r):[null,-1]}descendants(t,e=0,s=Number.MAX_VALUE){let r=[],i=s;return this.children.forEachAt(e,s,(o,a,u)=>{(t.blotName==null&&t(o)||t.blotName!=null&&o instanceof t)&&r.push(o),o instanceof ye&&(r=r.concat(o.descendants(t,a,i))),i-=u}),r}detach(){this.children.forEach(t=>{t.detach()}),super.detach()}enforceAllowedChildren(){let t=!1;this.children.forEach(e=>{t||this.statics.allowedChildren.some(s=>e instanceof s)||(e.statics.scope===G.BLOCK_BLOT?(e.next!=null&&this.splitAfter(e),e.prev!=null&&this.splitAfter(e.prev),e.parent.unwrap(),t=!0):e instanceof ye?e.unwrap():e.remove())})}formatAt(t,e,s,r){this.children.forEachAt(t,e,(i,o,a)=>{i.formatAt(o,a,s,r)})}insertAt(t,e,s){const[r,i]=this.children.find(t);if(r)r.insertAt(i,e,s);else{const o=s==null?this.scroll.create("text",e):this.scroll.create(e,s);this.appendChild(o)}}insertBefore(t,e){t.parent!=null&&t.parent.children.remove(t);let s=null;this.children.insertBefore(t,e||null),t.parent=this,e!=null&&(s=e.domNode),(this.domNode.parentNode!==t.domNode||this.domNode.nextSibling!==s)&&this.domNode.insertBefore(t.domNode,s),t.attach()}length(){return this.children.reduce((t,e)=>t+e.length(),0)}moveChildren(t,e){this.children.forEach(s=>{t.insertBefore(s,e)})}optimize(t){if(super.optimize(t),this.enforceAllowedChildren(),this.uiNode!=null&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),this.children.length===0)if(this.statics.defaultChild!=null){const e=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(e)}else this.remove()}path(t,e=!1){const[s,r]=this.children.find(t,e),i=[[this,t]];return s instanceof ye?i.concat(s.path(r,e)):(s!=null&&i.push([s,r]),i)}removeChild(t){this.children.remove(t)}replaceWith(t,e){const s=typeof t=="string"?this.scroll.create(t,e):t;return s instanceof ye&&this.moveChildren(s),super.replaceWith(s)}split(t,e=!1){if(!e){if(t===0)return this;if(t===this.length())return this.next}const s=this.clone();return this.parent&&this.parent.insertBefore(s,this.next||void 0),this.children.forEachAt(t,this.length(),(r,i,o)=>{const a=r.split(i,e);a!=null&&s.appendChild(a)}),s}splitAfter(t){const e=this.clone();for(;t.next!=null;)e.appendChild(t.next);return this.parent&&this.parent.insertBefore(e,this.next||void 0),e}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(t,e){const s=[],r=[];t.forEach(i=>{i.target===this.domNode&&i.type==="childList"&&(s.push(...i.addedNodes),r.push(...i.removedNodes))}),r.forEach(i=>{if(i.parentNode!=null&&i.tagName!=="IFRAME"&&document.body.compareDocumentPosition(i)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;const o=this.scroll.find(i);o!=null&&(o.domNode.parentNode==null||o.domNode.parentNode===this.domNode)&&o.detach()}),s.filter(i=>i.parentNode===this.domNode&&i!==this.uiNode).sort((i,o)=>i===o?0:i.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1).forEach(i=>{let o=null;i.nextSibling!=null&&(o=this.scroll.find(i.nextSibling));const a=Ni(i,this.scroll);(a.next!==o||a.next==null)&&(a.parent!=null&&a.parent.removeChild(this),this.insertBefore(a,o||void 0))}),this.enforceAllowedChildren()}};bl.uiClass="";let xc=bl;const Wt=xc;function Nc(n,t){if(Object.keys(n).length!==Object.keys(t).length)return!1;for(const e in n)if(n[e]!==t[e])return!1;return!0}const Ke=class We extends Wt{static create(t){return super.create(t)}static formats(t,e){const s=e.query(We.blotName);if(!(s!=null&&t.tagName===s.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new Ar(this.domNode)}format(t,e){if(t===this.statics.blotName&&!e)this.children.forEach(s=>{s instanceof We||(s=s.wrap(We.blotName,!0)),this.attributes.copy(s)}),this.unwrap();else{const s=this.scroll.query(t,G.INLINE);if(s==null)return;s instanceof ie?this.attributes.attribute(s,e):e&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e)}}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return e!=null&&(t[this.statics.blotName]=e),t}formatAt(t,e,s,r){this.formats()[s]!=null||this.scroll.query(s,G.ATTRIBUTE)?this.isolate(t,e).format(s,r):super.formatAt(t,e,s,r)}optimize(t){super.optimize(t);const e=this.formats();if(Object.keys(e).length===0)return this.unwrap();const s=this.next;s instanceof We&&s.prev===this&&Nc(e,s.formats())&&(s.moveChildren(this),s.remove())}replaceWith(t,e){const s=super.replaceWith(t,e);return this.attributes.copy(s),s}update(t,e){super.update(t,e),t.some(s=>s.target===this.domNode&&s.type==="attributes")&&this.attributes.build()}wrap(t,e){const s=super.wrap(t,e);return s instanceof We&&this.attributes.move(s),s}};Ke.allowedChildren=[Ke,Tt],Ke.blotName="inline",Ke.scope=G.INLINE_BLOT,Ke.tagName="SPAN";let Ec=Ke;const Yn=Ec,Ze=class kn extends Wt{static create(t){return super.create(t)}static formats(t,e){const s=e.query(kn.blotName);if(!(s!=null&&t.tagName===s.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new Ar(this.domNode)}format(t,e){const s=this.scroll.query(t,G.BLOCK);s!=null&&(s instanceof ie?this.attributes.attribute(s,e):t===this.statics.blotName&&!e?this.replaceWith(kn.blotName):e&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e))}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return e!=null&&(t[this.statics.blotName]=e),t}formatAt(t,e,s,r){this.scroll.query(s,G.BLOCK)!=null?this.format(s,r):super.formatAt(t,e,s,r)}insertAt(t,e,s){if(s==null||this.scroll.query(e,G.INLINE)!=null)super.insertAt(t,e,s);else{const r=this.split(t);if(r!=null){const i=this.scroll.create(e,s);r.parent.insertBefore(i,r)}else throw new Error("Attempt to insertAt after block boundaries")}}replaceWith(t,e){const s=super.replaceWith(t,e);return this.attributes.copy(s),s}update(t,e){super.update(t,e),t.some(s=>s.target===this.domNode&&s.type==="attributes")&&this.attributes.build()}};Ze.blotName="block",Ze.scope=G.BLOCK_BLOT,Ze.tagName="P",Ze.allowedChildren=[Yn,Ze,Tt];let wc=Ze;const Rs=wc,_n=class extends Wt{checkMerge(){return this.next!==null&&this.next.statics.blotName===this.statics.blotName}deleteAt(t,e){super.deleteAt(t,e),this.enforceAllowedChildren()}formatAt(t,e,s,r){super.formatAt(t,e,s,r),this.enforceAllowedChildren()}insertAt(t,e,s){super.insertAt(t,e,s),this.enforceAllowedChildren()}optimize(t){super.optimize(t),this.children.length>0&&this.next!=null&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};_n.blotName="container",_n.scope=G.BLOCK_BLOT;let Ac=_n;const Cr=Ac;class Cc extends Tt{static formats(t,e){}format(t,e){super.formatAt(0,this.length(),t,e)}formatAt(t,e,s,r){t===0&&e===this.length()?this.format(s,r):super.formatAt(t,e,s,r)}formats(){return this.statics.formats(this.domNode,this.scroll)}}const Ot=Cc,Tc={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},Lc=100,Xe=class extends Wt{constructor(t,e){super(null,e),this.registry=t,this.scroll=this,this.build(),this.observer=new MutationObserver(s=>{this.update(s)}),this.observer.observe(this.domNode,Tc),this.attach()}create(t,e){return this.registry.create(this,t,e)}find(t,e=!1){const s=this.registry.find(t,e);return s?s.scroll===this?s:e?this.find(s.scroll.domNode.parentNode,!0):null:null}query(t,e=G.ANY){return this.registry.query(t,e)}register(...t){return this.registry.register(...t)}build(){this.scroll!=null&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(t,e){this.update(),t===0&&e===this.length()?this.children.forEach(s=>{s.remove()}):super.deleteAt(t,e)}formatAt(t,e,s,r){this.update(),super.formatAt(t,e,s,r)}insertAt(t,e,s){this.update(),super.insertAt(t,e,s)}optimize(t=[],e={}){super.optimize(e);const s=e.mutationsMap||new WeakMap;let r=Array.from(this.observer.takeRecords());for(;r.length>0;)t.push(r.pop());const i=(u,h=!0)=>{u==null||u===this||u.domNode.parentNode!=null&&(s.has(u.domNode)||s.set(u.domNode,[]),h&&i(u.parent))},o=u=>{s.has(u.domNode)&&(u instanceof Wt&&u.children.forEach(o),s.delete(u.domNode),u.optimize(e))};let a=t;for(let u=0;a.length>0;u+=1){if(u>=Lc)throw new Error("[Parchment] Maximum optimize iterations reached");for(a.forEach(h=>{const b=this.find(h.target,!0);b!=null&&(b.domNode===h.target&&(h.type==="childList"?(i(this.find(h.previousSibling,!1)),Array.from(h.addedNodes).forEach(x=>{const d=this.find(x,!1);i(d,!1),d instanceof Wt&&d.children.forEach(f=>{i(f,!1)})})):h.type==="attributes"&&i(b.prev)),i(b))}),this.children.forEach(o),a=Array.from(this.observer.takeRecords()),r=a.slice();r.length>0;)t.push(r.pop())}}update(t,e={}){t=t||this.observer.takeRecords();const s=new WeakMap;t.map(r=>{const i=this.find(r.target,!0);return i==null?null:s.has(i.domNode)?(s.get(i.domNode).push(r),null):(s.set(i.domNode,[r]),i)}).forEach(r=>{r!=null&&r!==this&&s.has(r.domNode)&&r.update(s.get(r.domNode)||[],e)}),e.mutationsMap=s,s.has(this.domNode)&&super.update(s.get(this.domNode),e),this.optimize(t,e)}};Xe.blotName="scroll",Xe.defaultChild=Rs,Xe.allowedChildren=[Rs,Cr],Xe.scope=G.BLOCK_BLOT,Xe.tagName="DIV";let Sc=Xe;const Qn=Sc,On=class yl extends Tt{static create(t){return document.createTextNode(t)}static value(t){return t.data}constructor(t,e){super(t,e),this.text=this.statics.value(this.domNode)}deleteAt(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)}index(t,e){return this.domNode===t?e:-1}insertAt(t,e,s){s==null?(this.text=this.text.slice(0,t)+e+this.text.slice(t),this.domNode.data=this.text):super.insertAt(t,e,s)}length(){return this.text.length}optimize(t){super.optimize(t),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof yl&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(t,e=!1){return[this.domNode,t]}split(t,e=!1){if(!e){if(t===0)return this;if(t===this.length())return this.next}const s=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(s,this.next||void 0),this.text=this.statics.value(this.domNode),s}update(t,e){t.some(s=>s.type==="characterData"&&s.target===this.domNode)&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};On.blotName="text",On.scope=G.INLINE_BLOT;let qc=On;const Nr=qc,kc=Object.freeze(Object.defineProperty({__proto__:null,Attributor:ie,AttributorStore:Ar,BlockBlot:Rs,ClassAttributor:Xt,ContainerBlot:Cr,EmbedBlot:Ot,InlineBlot:Yn,LeafBlot:Tt,ParentBlot:Wt,Registry:ls,Scope:G,ScrollBlot:Qn,StyleAttributor:we,TextBlot:Nr},Symbol.toStringTag,{value:"Module"}));var ar={exports:{}},vn,Ei;function _c(){if(Ei)return vn;Ei=1;var n=-1,t=1,e=0;function s(g,q,N,O,k){if(g===q)return g?[[e,g]]:[];if(N!=null){var L=ht(g,q,N);if(L)return L}var _=a(g,q),R=g.substring(0,_);g=g.substring(_),q=q.substring(_),_=h(g,q);var F=g.substring(g.length-_);g=g.substring(0,g.length-_),q=q.substring(0,q.length-_);var B=r(g,q);return R&&B.unshift([e,R]),F&&B.push([e,F]),M(B,k),O&&x(B),B}function r(g,q){var N;if(!g)return[[t,q]];if(!q)return[[n,g]];var O=g.length>q.length?g:q,k=g.length>q.length?q:g,L=O.indexOf(k);if(L!==-1)return N=[[t,O.substring(0,L)],[e,k],[t,O.substring(L+k.length)]],g.length>q.length&&(N[0][0]=N[2][0]=n),N;if(k.length===1)return[[n,g],[t,q]];var _=b(g,q);if(_){var R=_[0],F=_[1],B=_[2],y=_[3],A=_[4],D=s(R,B),H=s(F,y);return D.concat([[e,A]],H)}return i(g,q)}function i(g,q){for(var N=g.length,O=q.length,k=Math.ceil((N+O)/2),L=k,_=2*k,R=new Array(_),F=new Array(_),B=0;B<_;B++)R[B]=-1,F[B]=-1;R[L+1]=0,F[L+1]=0;for(var y=N-O,A=y%2!==0,D=0,H=0,I=0,Y=0,J=0;J<k;J++){for(var z=-J+D;z<=J-H;z+=2){var Z=L+z,X;z===-J||z!==J&&R[Z-1]<R[Z+1]?X=R[Z+1]:X=R[Z-1]+1;for(var nt=X-z;X<N&&nt<O&&g.charAt(X)===q.charAt(nt);)X++,nt++;if(R[Z]=X,X>N)H+=2;else if(nt>O)D+=2;else if(A){var ot=L+y-z;if(ot>=0&&ot<_&&F[ot]!==-1){var it=N-F[ot];if(X>=it)return o(g,q,X,nt)}}}for(var gt=-J+I;gt<=J-Y;gt+=2){var ot=L+gt,it;gt===-J||gt!==J&&F[ot-1]<F[ot+1]?it=F[ot+1]:it=F[ot-1]+1;for(var bt=it-gt;it<N&&bt<O&&g.charAt(N-it-1)===q.charAt(O-bt-1);)it++,bt++;if(F[ot]=it,it>N)Y+=2;else if(bt>O)I+=2;else if(!A){var Z=L+y-gt;if(Z>=0&&Z<_&&R[Z]!==-1){var X=R[Z],nt=L+X-Z;if(it=N-it,X>=it)return o(g,q,X,nt)}}}}return[[n,g],[t,q]]}function o(g,q,N,O){var k=g.substring(0,N),L=q.substring(0,O),_=g.substring(N),R=q.substring(O),F=s(k,L),B=s(_,R);return F.concat(B)}function a(g,q){if(!g||!q||g.charAt(0)!==q.charAt(0))return 0;for(var N=0,O=Math.min(g.length,q.length),k=O,L=0;N<k;)g.substring(L,k)==q.substring(L,k)?(N=k,L=N):O=k,k=Math.floor((O-N)/2+N);return P(g.charCodeAt(k-1))&&k--,k}function u(g,q){var N=g.length,O=q.length;if(N==0||O==0)return 0;N>O?g=g.substring(N-O):N<O&&(q=q.substring(0,N));var k=Math.min(N,O);if(g==q)return k;for(var L=0,_=1;;){var R=g.substring(k-_),F=q.indexOf(R);if(F==-1)return L;_+=F,(F==0||g.substring(k-_)==q.substring(0,_))&&(L=_,_++)}}function h(g,q){if(!g||!q||g.slice(-1)!==q.slice(-1))return 0;for(var N=0,O=Math.min(g.length,q.length),k=O,L=0;N<k;)g.substring(g.length-k,g.length-L)==q.substring(q.length-k,q.length-L)?(N=k,L=N):O=k,k=Math.floor((O-N)/2+N);return tt(g.charCodeAt(g.length-k))&&k--,k}function b(g,q){var N=g.length>q.length?g:q,O=g.length>q.length?q:g;if(N.length<4||O.length*2<N.length)return null;function k(H,I,Y){for(var J=H.substring(Y,Y+Math.floor(H.length/4)),z=-1,Z="",X,nt,ot,it;(z=I.indexOf(J,z+1))!==-1;){var gt=a(H.substring(Y),I.substring(z)),bt=h(H.substring(0,Y),I.substring(0,z));Z.length<bt+gt&&(Z=I.substring(z-bt,z)+I.substring(z,z+gt),X=H.substring(0,Y-bt),nt=H.substring(Y+gt),ot=I.substring(0,z-bt),it=I.substring(z+gt))}return Z.length*2>=H.length?[X,nt,ot,it,Z]:null}var L=k(N,O,Math.ceil(N.length/4)),_=k(N,O,Math.ceil(N.length/2)),R;if(!L&&!_)return null;_?L?R=L[4].length>_[4].length?L:_:R=_:R=L;var F,B,y,A;g.length>q.length?(F=R[0],B=R[1],y=R[2],A=R[3]):(y=R[0],A=R[1],F=R[2],B=R[3]);var D=R[4];return[F,B,y,A,D]}function x(g){for(var q=!1,N=[],O=0,k=null,L=0,_=0,R=0,F=0,B=0;L<g.length;)g[L][0]==e?(N[O++]=L,_=F,R=B,F=0,B=0,k=g[L][1]):(g[L][0]==t?F+=g[L][1].length:B+=g[L][1].length,k&&k.length<=Math.max(_,R)&&k.length<=Math.max(F,B)&&(g.splice(N[O-1],0,[n,k]),g[N[O-1]+1][0]=t,O--,O--,L=O>0?N[O-1]:-1,_=0,R=0,F=0,B=0,k=null,q=!0)),L++;for(q&&M(g),S(g),L=1;L<g.length;){if(g[L-1][0]==n&&g[L][0]==t){var y=g[L-1][1],A=g[L][1],D=u(y,A),H=u(A,y);D>=H?(D>=y.length/2||D>=A.length/2)&&(g.splice(L,0,[e,A.substring(0,D)]),g[L-1][1]=y.substring(0,y.length-D),g[L+1][1]=A.substring(D),L++):(H>=y.length/2||H>=A.length/2)&&(g.splice(L,0,[e,y.substring(0,H)]),g[L-1][0]=t,g[L-1][1]=A.substring(0,A.length-H),g[L+1][0]=n,g[L+1][1]=y.substring(H),L++),L++}L++}}var d=/[^a-zA-Z0-9]/,f=/\s/,v=/[\r\n]/,E=/\n\r?\n$/,C=/^\r?\n\r?\n/;function S(g){function q(H,I){if(!H||!I)return 6;var Y=H.charAt(H.length-1),J=I.charAt(0),z=Y.match(d),Z=J.match(d),X=z&&Y.match(f),nt=Z&&J.match(f),ot=X&&Y.match(v),it=nt&&J.match(v),gt=ot&&H.match(E),bt=it&&I.match(C);return gt||bt?5:ot||it?4:z&&!X&&nt?3:X||nt?2:z||Z?1:0}for(var N=1;N<g.length-1;){if(g[N-1][0]==e&&g[N+1][0]==e){var O=g[N-1][1],k=g[N][1],L=g[N+1][1],_=h(O,k);if(_){var R=k.substring(k.length-_);O=O.substring(0,O.length-_),k=R+k.substring(0,k.length-_),L=R+L}for(var F=O,B=k,y=L,A=q(O,k)+q(k,L);k.charAt(0)===L.charAt(0);){O+=k.charAt(0),k=k.substring(1)+L.charAt(0),L=L.substring(1);var D=q(O,k)+q(k,L);D>=A&&(A=D,F=O,B=k,y=L)}g[N-1][1]!=F&&(F?g[N-1][1]=F:(g.splice(N-1,1),N--),g[N][1]=B,y?g[N+1][1]=y:(g.splice(N+1,1),N--))}N++}}function M(g,q){g.push([e,""]);for(var N=0,O=0,k=0,L="",_="",R;N<g.length;){if(N<g.length-1&&!g[N][1]){g.splice(N,1);continue}switch(g[N][0]){case t:k++,_+=g[N][1],N++;break;case n:O++,L+=g[N][1],N++;break;case e:var F=N-k-O-1;if(q){if(F>=0&&Q(g[F][1])){var B=g[F][1].slice(-1);if(g[F][1]=g[F][1].slice(0,-1),L=B+L,_=B+_,!g[F][1]){g.splice(F,1),N--;var y=F-1;g[y]&&g[y][0]===t&&(k++,_=g[y][1]+_,y--),g[y]&&g[y][0]===n&&(O++,L=g[y][1]+L,y--),F=y}}if(U(g[N][1])){var B=g[N][1].charAt(0);g[N][1]=g[N][1].slice(1),L+=B,_+=B}}if(N<g.length-1&&!g[N][1]){g.splice(N,1);break}if(L.length>0||_.length>0){L.length>0&&_.length>0&&(R=a(_,L),R!==0&&(F>=0?g[F][1]+=_.substring(0,R):(g.splice(0,0,[e,_.substring(0,R)]),N++),_=_.substring(R),L=L.substring(R)),R=h(_,L),R!==0&&(g[N][1]=_.substring(_.length-R)+g[N][1],_=_.substring(0,_.length-R),L=L.substring(0,L.length-R)));var A=k+O;L.length===0&&_.length===0?(g.splice(N-A,A),N=N-A):L.length===0?(g.splice(N-A,A,[t,_]),N=N-A+1):_.length===0?(g.splice(N-A,A,[n,L]),N=N-A+1):(g.splice(N-A,A,[n,L],[t,_]),N=N-A+2)}N!==0&&g[N-1][0]===e?(g[N-1][1]+=g[N][1],g.splice(N,1)):N++,k=0,O=0,L="",_="";break}}g[g.length-1][1]===""&&g.pop();var D=!1;for(N=1;N<g.length-1;)g[N-1][0]===e&&g[N+1][0]===e&&(g[N][1].substring(g[N][1].length-g[N-1][1].length)===g[N-1][1]?(g[N][1]=g[N-1][1]+g[N][1].substring(0,g[N][1].length-g[N-1][1].length),g[N+1][1]=g[N-1][1]+g[N+1][1],g.splice(N-1,1),D=!0):g[N][1].substring(0,g[N+1][1].length)==g[N+1][1]&&(g[N-1][1]+=g[N+1][1],g[N][1]=g[N][1].substring(g[N+1][1].length)+g[N+1][1],g.splice(N+1,1),D=!0)),N++;D&&M(g,q)}function P(g){return g>=55296&&g<=56319}function tt(g){return g>=56320&&g<=57343}function U(g){return tt(g.charCodeAt(0))}function Q(g){return P(g.charCodeAt(g.length-1))}function rt(g){for(var q=[],N=0;N<g.length;N++)g[N][1].length>0&&q.push(g[N]);return q}function st(g,q,N,O){return Q(g)||U(O)?null:rt([[e,g],[n,q],[t,N],[e,O]])}function ht(g,q,N){var O=typeof N=="number"?{index:N,length:0}:N.oldRange,k=typeof N=="number"?null:N.newRange,L=g.length,_=q.length;if(O.length===0&&(k===null||k.length===0)){var R=O.index,F=g.slice(0,R),B=g.slice(R),y=k?k.index:null;t:{var A=R+_-L;if(y!==null&&y!==A||A<0||A>_)break t;var D=q.slice(0,A),H=q.slice(A);if(H!==B)break t;var I=Math.min(R,A),Y=F.slice(0,I),J=D.slice(0,I);if(Y!==J)break t;var z=F.slice(I),Z=D.slice(I);return st(Y,z,Z,B)}t:{if(y!==null&&y!==R)break t;var X=R,D=q.slice(0,X),H=q.slice(X);if(D!==F)break t;var nt=Math.min(L-X,_-X),ot=B.slice(B.length-nt),it=H.slice(H.length-nt);if(ot!==it)break t;var z=B.slice(0,B.length-nt),Z=H.slice(0,H.length-nt);return st(F,z,Z,ot)}}if(O.length>0&&k&&k.length===0)t:{var Y=g.slice(0,O.index),ot=g.slice(O.index+O.length),I=Y.length,nt=ot.length;if(_<I+nt)break t;var J=q.slice(0,I),it=q.slice(_-nt);if(Y!==J||ot!==it)break t;var z=g.slice(I,L-nt),Z=q.slice(I,_-nt);return st(Y,z,Z,ot)}return null}function ft(g,q,N,O){return s(g,q,N,O,!0)}return ft.INSERT=t,ft.DELETE=n,ft.EQUAL=e,vn=ft,vn}var Ss={exports:{}};Ss.exports;var wi;function vl(){return wi||(wi=1,function(n,t){var e=200,s="__lodash_hash_undefined__",r=9007199254740991,i="[object Arguments]",o="[object Array]",a="[object Boolean]",u="[object Date]",h="[object Error]",b="[object Function]",x="[object GeneratorFunction]",d="[object Map]",f="[object Number]",v="[object Object]",E="[object Promise]",C="[object RegExp]",S="[object Set]",M="[object String]",P="[object Symbol]",tt="[object WeakMap]",U="[object ArrayBuffer]",Q="[object DataView]",rt="[object Float32Array]",st="[object Float64Array]",ht="[object Int8Array]",ft="[object Int16Array]",g="[object Int32Array]",q="[object Uint8Array]",N="[object Uint8ClampedArray]",O="[object Uint16Array]",k="[object Uint32Array]",L=/[\\^$.*+?()[\]{}|]/g,_=/\w*$/,R=/^\[object .+?Constructor\]$/,F=/^(?:0|[1-9]\d*)$/,B={};B[i]=B[o]=B[U]=B[Q]=B[a]=B[u]=B[rt]=B[st]=B[ht]=B[ft]=B[g]=B[d]=B[f]=B[v]=B[C]=B[S]=B[M]=B[P]=B[q]=B[N]=B[O]=B[k]=!0,B[h]=B[b]=B[tt]=!1;var y=typeof ve=="object"&&ve&&ve.Object===Object&&ve,A=typeof self=="object"&&self&&self.Object===Object&&self,D=y||A||Function("return this")(),H=t&&!t.nodeType&&t,I=H&&!0&&n&&!n.nodeType&&n,Y=I&&I.exports===H;function J(l,c){return l.set(c[0],c[1]),l}function z(l,c){return l.add(c),l}function Z(l,c){for(var p=-1,w=l?l.length:0;++p<w&&c(l[p],p,l)!==!1;);return l}function X(l,c){for(var p=-1,w=c.length,et=l.length;++p<w;)l[et+p]=c[p];return l}function nt(l,c,p,w){for(var et=-1,W=l?l.length:0;++et<W;)p=c(p,l[et],et,l);return p}function ot(l,c){for(var p=-1,w=Array(l);++p<l;)w[p]=c(p);return w}function it(l,c){return l==null?void 0:l[c]}function gt(l){var c=!1;if(l!=null&&typeof l.toString!="function")try{c=!!(l+"")}catch{}return c}function bt(l){var c=-1,p=Array(l.size);return l.forEach(function(w,et){p[++c]=[et,w]}),p}function ge(l,c){return function(p){return l(c(p))}}function Fs(l){var c=-1,p=Array(l.size);return l.forEach(function(w){p[++c]=w}),p}var qr=Array.prototype,kr=Function.prototype,Me=Object.prototype,hs=D["__core-js_shared__"],Hs=function(){var l=/[^.]+$/.exec(hs&&hs.keys&&hs.keys.IE_PROTO||"");return l?"Symbol(src)_1."+l:""}(),zs=kr.toString,Jt=Me.hasOwnProperty,Be=Me.toString,_r=RegExp("^"+zs.call(Jt).replace(L,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ae=Y?D.Buffer:void 0,De=D.Symbol,ds=D.Uint8Array,It=ge(Object.getPrototypeOf,Object),Vs=Object.create,Gs=Me.propertyIsEnumerable,Or=qr.splice,fs=Object.getOwnPropertySymbols,Pe=Ae?Ae.isBuffer:void 0,Ks=ge(Object.keys,Object),Ue=Ht(D,"DataView"),Ce=Ht(D,"Map"),Ft=Ht(D,"Promise"),$e=Ht(D,"Set"),ps=Ht(D,"WeakMap"),Te=Ht(Object,"create"),gs=St(Ue),Le=St(Ce),ms=St(Ft),bs=St($e),ys=St(ps),me=De?De.prototype:void 0,Ws=me?me.valueOf:void 0;function oe(l){var c=-1,p=l?l.length:0;for(this.clear();++c<p;){var w=l[c];this.set(w[0],w[1])}}function Ir(){this.__data__=Te?Te(null):{}}function Rr(l){return this.has(l)&&delete this.__data__[l]}function jr(l){var c=this.__data__;if(Te){var p=c[l];return p===s?void 0:p}return Jt.call(c,l)?c[l]:void 0}function Zs(l){var c=this.__data__;return Te?c[l]!==void 0:Jt.call(c,l)}function vs(l,c){var p=this.__data__;return p[l]=Te&&c===void 0?s:c,this}oe.prototype.clear=Ir,oe.prototype.delete=Rr,oe.prototype.get=jr,oe.prototype.has=Zs,oe.prototype.set=vs;function xt(l){var c=-1,p=l?l.length:0;for(this.clear();++c<p;){var w=l[c];this.set(w[0],w[1])}}function Mr(){this.__data__=[]}function Br(l){var c=this.__data__,p=He(c,l);if(p<0)return!1;var w=c.length-1;return p==w?c.pop():Or.call(c,p,1),!0}function Dr(l){var c=this.__data__,p=He(c,l);return p<0?void 0:c[p][1]}function Pr(l){return He(this.__data__,l)>-1}function Ur(l,c){var p=this.__data__,w=He(p,l);return w<0?p.push([l,c]):p[w][1]=c,this}xt.prototype.clear=Mr,xt.prototype.delete=Br,xt.prototype.get=Dr,xt.prototype.has=Pr,xt.prototype.set=Ur;function wt(l){var c=-1,p=l?l.length:0;for(this.clear();++c<p;){var w=l[c];this.set(w[0],w[1])}}function $r(){this.__data__={hash:new oe,map:new(Ce||xt),string:new oe}}function Fr(l){return qe(this,l).delete(l)}function Hr(l){return qe(this,l).get(l)}function zr(l){return qe(this,l).has(l)}function Vr(l,c){return qe(this,l).set(l,c),this}wt.prototype.clear=$r,wt.prototype.delete=Fr,wt.prototype.get=Hr,wt.prototype.has=zr,wt.prototype.set=Vr;function qt(l){this.__data__=new xt(l)}function Gr(){this.__data__=new xt}function Kr(l){return this.__data__.delete(l)}function Wr(l){return this.__data__.get(l)}function Zr(l){return this.__data__.has(l)}function Xr(l,c){var p=this.__data__;if(p instanceof xt){var w=p.__data__;if(!Ce||w.length<e-1)return w.push([l,c]),this;p=this.__data__=new wt(w)}return p.set(l,c),this}qt.prototype.clear=Gr,qt.prototype.delete=Kr,qt.prototype.get=Wr,qt.prototype.has=Zr,qt.prototype.set=Xr;function Fe(l,c){var p=ws(l)||Ve(l)?ot(l.length,String):[],w=p.length,et=!!w;for(var W in l)Jt.call(l,W)&&!(et&&(W=="length"||hn(W,w)))&&p.push(W);return p}function Xs(l,c,p){var w=l[c];(!(Jt.call(l,c)&&er(w,p))||p===void 0&&!(c in l))&&(l[c]=p)}function He(l,c){for(var p=l.length;p--;)if(er(l[p][0],c))return p;return-1}function te(l,c){return l&&Es(c,Cs(c),l)}function xs(l,c,p,w,et,W,at){var lt;if(w&&(lt=W?w(l,et,W,at):w(l)),lt!==void 0)return lt;if(!se(l))return l;var mt=ws(l);if(mt){if(lt=cn(l),!c)return ln(l,lt)}else{var ut=ce(l),At=ut==b||ut==x;if(sr(l))return ze(l,c);if(ut==v||ut==i||At&&!W){if(gt(l))return W?l:{};if(lt=ee(At?{}:l),!c)return on(l,te(lt,l))}else{if(!B[ut])return W?l:{};lt=un(l,ut,xs,c)}}at||(at=new qt);var kt=at.get(l);if(kt)return kt;if(at.set(l,lt),!mt)var yt=p?an(l):Cs(l);return Z(yt||l,function(Ct,Nt){yt&&(Nt=Ct,Ct=l[Nt]),Xs(lt,Nt,xs(Ct,c,p,w,Nt,l,at))}),lt}function Yr(l){return se(l)?Vs(l):{}}function Qr(l,c,p){var w=c(l);return ws(l)?w:X(w,p(l))}function Jr(l){return Be.call(l)}function tn(l){if(!se(l)||fn(l))return!1;var c=As(l)||gt(l)?_r:R;return c.test(St(l))}function en(l){if(!Js(l))return Ks(l);var c=[];for(var p in Object(l))Jt.call(l,p)&&p!="constructor"&&c.push(p);return c}function ze(l,c){if(c)return l.slice();var p=new l.constructor(l.length);return l.copy(p),p}function Ns(l){var c=new l.constructor(l.byteLength);return new ds(c).set(new ds(l)),c}function Se(l,c){var p=c?Ns(l.buffer):l.buffer;return new l.constructor(p,l.byteOffset,l.byteLength)}function Ys(l,c,p){var w=c?p(bt(l),!0):bt(l);return nt(w,J,new l.constructor)}function Qs(l){var c=new l.constructor(l.source,_.exec(l));return c.lastIndex=l.lastIndex,c}function sn(l,c,p){var w=c?p(Fs(l),!0):Fs(l);return nt(w,z,new l.constructor)}function rn(l){return Ws?Object(Ws.call(l)):{}}function nn(l,c){var p=c?Ns(l.buffer):l.buffer;return new l.constructor(p,l.byteOffset,l.length)}function ln(l,c){var p=-1,w=l.length;for(c||(c=Array(w));++p<w;)c[p]=l[p];return c}function Es(l,c,p,w){p||(p={});for(var et=-1,W=c.length;++et<W;){var at=c[et],lt=void 0;Xs(p,at,lt===void 0?l[at]:lt)}return p}function on(l,c){return Es(l,ae(l),c)}function an(l){return Qr(l,Cs,ae)}function qe(l,c){var p=l.__data__;return dn(c)?p[typeof c=="string"?"string":"hash"]:p.map}function Ht(l,c){var p=it(l,c);return tn(p)?p:void 0}var ae=fs?ge(fs,Object):gn,ce=Jr;(Ue&&ce(new Ue(new ArrayBuffer(1)))!=Q||Ce&&ce(new Ce)!=d||Ft&&ce(Ft.resolve())!=E||$e&&ce(new $e)!=S||ps&&ce(new ps)!=tt)&&(ce=function(l){var c=Be.call(l),p=c==v?l.constructor:void 0,w=p?St(p):void 0;if(w)switch(w){case gs:return Q;case Le:return d;case ms:return E;case bs:return S;case ys:return tt}return c});function cn(l){var c=l.length,p=l.constructor(c);return c&&typeof l[0]=="string"&&Jt.call(l,"index")&&(p.index=l.index,p.input=l.input),p}function ee(l){return typeof l.constructor=="function"&&!Js(l)?Yr(It(l)):{}}function un(l,c,p,w){var et=l.constructor;switch(c){case U:return Ns(l);case a:case u:return new et(+l);case Q:return Se(l,w);case rt:case st:case ht:case ft:case g:case q:case N:case O:case k:return nn(l,w);case d:return Ys(l,w,p);case f:case M:return new et(l);case C:return Qs(l);case S:return sn(l,w,p);case P:return rn(l)}}function hn(l,c){return c=c??r,!!c&&(typeof l=="number"||F.test(l))&&l>-1&&l%1==0&&l<c}function dn(l){var c=typeof l;return c=="string"||c=="number"||c=="symbol"||c=="boolean"?l!=="__proto__":l===null}function fn(l){return!!Hs&&Hs in l}function Js(l){var c=l&&l.constructor,p=typeof c=="function"&&c.prototype||Me;return l===p}function St(l){if(l!=null){try{return zs.call(l)}catch{}try{return l+""}catch{}}return""}function tr(l){return xs(l,!0,!0)}function er(l,c){return l===c||l!==l&&c!==c}function Ve(l){return pn(l)&&Jt.call(l,"callee")&&(!Gs.call(l,"callee")||Be.call(l)==i)}var ws=Array.isArray;function Ge(l){return l!=null&&rr(l.length)&&!As(l)}function pn(l){return nr(l)&&Ge(l)}var sr=Pe||mn;function As(l){var c=se(l)?Be.call(l):"";return c==b||c==x}function rr(l){return typeof l=="number"&&l>-1&&l%1==0&&l<=r}function se(l){var c=typeof l;return!!l&&(c=="object"||c=="function")}function nr(l){return!!l&&typeof l=="object"}function Cs(l){return Ge(l)?Fe(l):en(l)}function gn(){return[]}function mn(){return!1}n.exports=tr}(Ss,Ss.exports)),Ss.exports}var qs={exports:{}};qs.exports;var Ai;function xl(){return Ai||(Ai=1,function(n,t){var e=200,s="__lodash_hash_undefined__",r=1,i=2,o=9007199254740991,a="[object Arguments]",u="[object Array]",h="[object AsyncFunction]",b="[object Boolean]",x="[object Date]",d="[object Error]",f="[object Function]",v="[object GeneratorFunction]",E="[object Map]",C="[object Number]",S="[object Null]",M="[object Object]",P="[object Promise]",tt="[object Proxy]",U="[object RegExp]",Q="[object Set]",rt="[object String]",st="[object Symbol]",ht="[object Undefined]",ft="[object WeakMap]",g="[object ArrayBuffer]",q="[object DataView]",N="[object Float32Array]",O="[object Float64Array]",k="[object Int8Array]",L="[object Int16Array]",_="[object Int32Array]",R="[object Uint8Array]",F="[object Uint8ClampedArray]",B="[object Uint16Array]",y="[object Uint32Array]",A=/[\\^$.*+?()[\]{}|]/g,D=/^\[object .+?Constructor\]$/,H=/^(?:0|[1-9]\d*)$/,I={};I[N]=I[O]=I[k]=I[L]=I[_]=I[R]=I[F]=I[B]=I[y]=!0,I[a]=I[u]=I[g]=I[b]=I[q]=I[x]=I[d]=I[f]=I[E]=I[C]=I[M]=I[U]=I[Q]=I[rt]=I[ft]=!1;var Y=typeof ve=="object"&&ve&&ve.Object===Object&&ve,J=typeof self=="object"&&self&&self.Object===Object&&self,z=Y||J||Function("return this")(),Z=t&&!t.nodeType&&t,X=Z&&!0&&n&&!n.nodeType&&n,nt=X&&X.exports===Z,ot=nt&&Y.process,it=function(){try{return ot&&ot.binding&&ot.binding("util")}catch{}}(),gt=it&&it.isTypedArray;function bt(l,c){for(var p=-1,w=l==null?0:l.length,et=0,W=[];++p<w;){var at=l[p];c(at,p,l)&&(W[et++]=at)}return W}function ge(l,c){for(var p=-1,w=c.length,et=l.length;++p<w;)l[et+p]=c[p];return l}function Fs(l,c){for(var p=-1,w=l==null?0:l.length;++p<w;)if(c(l[p],p,l))return!0;return!1}function qr(l,c){for(var p=-1,w=Array(l);++p<l;)w[p]=c(p);return w}function kr(l){return function(c){return l(c)}}function Me(l,c){return l.has(c)}function hs(l,c){return l==null?void 0:l[c]}function Hs(l){var c=-1,p=Array(l.size);return l.forEach(function(w,et){p[++c]=[et,w]}),p}function zs(l,c){return function(p){return l(c(p))}}function Jt(l){var c=-1,p=Array(l.size);return l.forEach(function(w){p[++c]=w}),p}var Be=Array.prototype,_r=Function.prototype,Ae=Object.prototype,De=z["__core-js_shared__"],ds=_r.toString,It=Ae.hasOwnProperty,Vs=function(){var l=/[^.]+$/.exec(De&&De.keys&&De.keys.IE_PROTO||"");return l?"Symbol(src)_1."+l:""}(),Gs=Ae.toString,Or=RegExp("^"+ds.call(It).replace(A,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),fs=nt?z.Buffer:void 0,Pe=z.Symbol,Ks=z.Uint8Array,Ue=Ae.propertyIsEnumerable,Ce=Be.splice,Ft=Pe?Pe.toStringTag:void 0,$e=Object.getOwnPropertySymbols,ps=fs?fs.isBuffer:void 0,Te=zs(Object.keys,Object),gs=ae(z,"DataView"),Le=ae(z,"Map"),ms=ae(z,"Promise"),bs=ae(z,"Set"),ys=ae(z,"WeakMap"),me=ae(Object,"create"),Ws=St(gs),oe=St(Le),Ir=St(ms),Rr=St(bs),jr=St(ys),Zs=Pe?Pe.prototype:void 0,vs=Zs?Zs.valueOf:void 0;function xt(l){var c=-1,p=l==null?0:l.length;for(this.clear();++c<p;){var w=l[c];this.set(w[0],w[1])}}function Mr(){this.__data__=me?me(null):{},this.size=0}function Br(l){var c=this.has(l)&&delete this.__data__[l];return this.size-=c?1:0,c}function Dr(l){var c=this.__data__;if(me){var p=c[l];return p===s?void 0:p}return It.call(c,l)?c[l]:void 0}function Pr(l){var c=this.__data__;return me?c[l]!==void 0:It.call(c,l)}function Ur(l,c){var p=this.__data__;return this.size+=this.has(l)?0:1,p[l]=me&&c===void 0?s:c,this}xt.prototype.clear=Mr,xt.prototype.delete=Br,xt.prototype.get=Dr,xt.prototype.has=Pr,xt.prototype.set=Ur;function wt(l){var c=-1,p=l==null?0:l.length;for(this.clear();++c<p;){var w=l[c];this.set(w[0],w[1])}}function $r(){this.__data__=[],this.size=0}function Fr(l){var c=this.__data__,p=ze(c,l);if(p<0)return!1;var w=c.length-1;return p==w?c.pop():Ce.call(c,p,1),--this.size,!0}function Hr(l){var c=this.__data__,p=ze(c,l);return p<0?void 0:c[p][1]}function zr(l){return ze(this.__data__,l)>-1}function Vr(l,c){var p=this.__data__,w=ze(p,l);return w<0?(++this.size,p.push([l,c])):p[w][1]=c,this}wt.prototype.clear=$r,wt.prototype.delete=Fr,wt.prototype.get=Hr,wt.prototype.has=zr,wt.prototype.set=Vr;function qt(l){var c=-1,p=l==null?0:l.length;for(this.clear();++c<p;){var w=l[c];this.set(w[0],w[1])}}function Gr(){this.size=0,this.__data__={hash:new xt,map:new(Le||wt),string:new xt}}function Kr(l){var c=Ht(this,l).delete(l);return this.size-=c?1:0,c}function Wr(l){return Ht(this,l).get(l)}function Zr(l){return Ht(this,l).has(l)}function Xr(l,c){var p=Ht(this,l),w=p.size;return p.set(l,c),this.size+=p.size==w?0:1,this}qt.prototype.clear=Gr,qt.prototype.delete=Kr,qt.prototype.get=Wr,qt.prototype.has=Zr,qt.prototype.set=Xr;function Fe(l){var c=-1,p=l==null?0:l.length;for(this.__data__=new qt;++c<p;)this.add(l[c])}function Xs(l){return this.__data__.set(l,s),this}function He(l){return this.__data__.has(l)}Fe.prototype.add=Fe.prototype.push=Xs,Fe.prototype.has=He;function te(l){var c=this.__data__=new wt(l);this.size=c.size}function xs(){this.__data__=new wt,this.size=0}function Yr(l){var c=this.__data__,p=c.delete(l);return this.size=c.size,p}function Qr(l){return this.__data__.get(l)}function Jr(l){return this.__data__.has(l)}function tn(l,c){var p=this.__data__;if(p instanceof wt){var w=p.__data__;if(!Le||w.length<e-1)return w.push([l,c]),this.size=++p.size,this;p=this.__data__=new qt(w)}return p.set(l,c),this.size=p.size,this}te.prototype.clear=xs,te.prototype.delete=Yr,te.prototype.get=Qr,te.prototype.has=Jr,te.prototype.set=tn;function en(l,c){var p=Ve(l),w=!p&&er(l),et=!p&&!w&&Ge(l),W=!p&&!w&&!et&&nr(l),at=p||w||et||W,lt=at?qr(l.length,String):[],mt=lt.length;for(var ut in l)It.call(l,ut)&&!(at&&(ut=="length"||et&&(ut=="offset"||ut=="parent")||W&&(ut=="buffer"||ut=="byteLength"||ut=="byteOffset")||un(ut,mt)))&&lt.push(ut);return lt}function ze(l,c){for(var p=l.length;p--;)if(tr(l[p][0],c))return p;return-1}function Ns(l,c,p){var w=c(l);return Ve(l)?w:ge(w,p(l))}function Se(l){return l==null?l===void 0?ht:S:Ft&&Ft in Object(l)?ce(l):Js(l)}function Ys(l){return se(l)&&Se(l)==a}function Qs(l,c,p,w,et){return l===c?!0:l==null||c==null||!se(l)&&!se(c)?l!==l&&c!==c:sn(l,c,p,w,Qs,et)}function sn(l,c,p,w,et,W){var at=Ve(l),lt=Ve(c),mt=at?u:ee(l),ut=lt?u:ee(c);mt=mt==a?M:mt,ut=ut==a?M:ut;var At=mt==M,kt=ut==M,yt=mt==ut;if(yt&&Ge(l)){if(!Ge(c))return!1;at=!0,At=!1}if(yt&&!At)return W||(W=new te),at||nr(l)?Es(l,c,p,w,et,W):on(l,c,mt,p,w,et,W);if(!(p&r)){var Ct=At&&It.call(l,"__wrapped__"),Nt=kt&&It.call(c,"__wrapped__");if(Ct||Nt){var be=Ct?l.value():l,ue=Nt?c.value():c;return W||(W=new te),et(be,ue,p,w,W)}}return yt?(W||(W=new te),an(l,c,p,w,et,W)):!1}function rn(l){if(!rr(l)||dn(l))return!1;var c=sr(l)?Or:D;return c.test(St(l))}function nn(l){return se(l)&&As(l.length)&&!!I[Se(l)]}function ln(l){if(!fn(l))return Te(l);var c=[];for(var p in Object(l))It.call(l,p)&&p!="constructor"&&c.push(p);return c}function Es(l,c,p,w,et,W){var at=p&r,lt=l.length,mt=c.length;if(lt!=mt&&!(at&&mt>lt))return!1;var ut=W.get(l);if(ut&&W.get(c))return ut==c;var At=-1,kt=!0,yt=p&i?new Fe:void 0;for(W.set(l,c),W.set(c,l);++At<lt;){var Ct=l[At],Nt=c[At];if(w)var be=at?w(Nt,Ct,At,c,l,W):w(Ct,Nt,At,l,c,W);if(be!==void 0){if(be)continue;kt=!1;break}if(yt){if(!Fs(c,function(ue,ke){if(!Me(yt,ke)&&(Ct===ue||et(Ct,ue,p,w,W)))return yt.push(ke)})){kt=!1;break}}else if(!(Ct===Nt||et(Ct,Nt,p,w,W))){kt=!1;break}}return W.delete(l),W.delete(c),kt}function on(l,c,p,w,et,W,at){switch(p){case q:if(l.byteLength!=c.byteLength||l.byteOffset!=c.byteOffset)return!1;l=l.buffer,c=c.buffer;case g:return!(l.byteLength!=c.byteLength||!W(new Ks(l),new Ks(c)));case b:case x:case C:return tr(+l,+c);case d:return l.name==c.name&&l.message==c.message;case U:case rt:return l==c+"";case E:var lt=Hs;case Q:var mt=w&r;if(lt||(lt=Jt),l.size!=c.size&&!mt)return!1;var ut=at.get(l);if(ut)return ut==c;w|=i,at.set(l,c);var At=Es(lt(l),lt(c),w,et,W,at);return at.delete(l),At;case st:if(vs)return vs.call(l)==vs.call(c)}return!1}function an(l,c,p,w,et,W){var at=p&r,lt=qe(l),mt=lt.length,ut=qe(c),At=ut.length;if(mt!=At&&!at)return!1;for(var kt=mt;kt--;){var yt=lt[kt];if(!(at?yt in c:It.call(c,yt)))return!1}var Ct=W.get(l);if(Ct&&W.get(c))return Ct==c;var Nt=!0;W.set(l,c),W.set(c,l);for(var be=at;++kt<mt;){yt=lt[kt];var ue=l[yt],ke=c[yt];if(w)var ci=at?w(ke,ue,yt,c,l,W):w(ue,ke,yt,l,c,W);if(!(ci===void 0?ue===ke||et(ue,ke,p,w,W):ci)){Nt=!1;break}be||(be=yt=="constructor")}if(Nt&&!be){var ir=l.constructor,lr=c.constructor;ir!=lr&&"constructor"in l&&"constructor"in c&&!(typeof ir=="function"&&ir instanceof ir&&typeof lr=="function"&&lr instanceof lr)&&(Nt=!1)}return W.delete(l),W.delete(c),Nt}function qe(l){return Ns(l,Cs,cn)}function Ht(l,c){var p=l.__data__;return hn(c)?p[typeof c=="string"?"string":"hash"]:p.map}function ae(l,c){var p=hs(l,c);return rn(p)?p:void 0}function ce(l){var c=It.call(l,Ft),p=l[Ft];try{l[Ft]=void 0;var w=!0}catch{}var et=Gs.call(l);return w&&(c?l[Ft]=p:delete l[Ft]),et}var cn=$e?function(l){return l==null?[]:(l=Object(l),bt($e(l),function(c){return Ue.call(l,c)}))}:gn,ee=Se;(gs&&ee(new gs(new ArrayBuffer(1)))!=q||Le&&ee(new Le)!=E||ms&&ee(ms.resolve())!=P||bs&&ee(new bs)!=Q||ys&&ee(new ys)!=ft)&&(ee=function(l){var c=Se(l),p=c==M?l.constructor:void 0,w=p?St(p):"";if(w)switch(w){case Ws:return q;case oe:return E;case Ir:return P;case Rr:return Q;case jr:return ft}return c});function un(l,c){return c=c??o,!!c&&(typeof l=="number"||H.test(l))&&l>-1&&l%1==0&&l<c}function hn(l){var c=typeof l;return c=="string"||c=="number"||c=="symbol"||c=="boolean"?l!=="__proto__":l===null}function dn(l){return!!Vs&&Vs in l}function fn(l){var c=l&&l.constructor,p=typeof c=="function"&&c.prototype||Ae;return l===p}function Js(l){return Gs.call(l)}function St(l){if(l!=null){try{return ds.call(l)}catch{}try{return l+""}catch{}}return""}function tr(l,c){return l===c||l!==l&&c!==c}var er=Ys(function(){return arguments}())?Ys:function(l){return se(l)&&It.call(l,"callee")&&!Ue.call(l,"callee")},Ve=Array.isArray;function ws(l){return l!=null&&As(l.length)&&!sr(l)}var Ge=ps||mn;function pn(l,c){return Qs(l,c)}function sr(l){if(!rr(l))return!1;var c=Se(l);return c==f||c==v||c==h||c==tt}function As(l){return typeof l=="number"&&l>-1&&l%1==0&&l<=o}function rr(l){var c=typeof l;return l!=null&&(c=="object"||c=="function")}function se(l){return l!=null&&typeof l=="object"}var nr=gt?kr(gt):nn;function Cs(l){return ws(l)?en(l):ln(l)}function gn(){return[]}function mn(){return!1}n.exports=pn}(qs,qs.exports)),qs.exports}var cr={},Ci;function Oc(){if(Ci)return cr;Ci=1,Object.defineProperty(cr,"__esModule",{value:!0});const n=vl(),t=xl();var e;return function(s){function r(u={},h={},b=!1){typeof u!="object"&&(u={}),typeof h!="object"&&(h={});let x=n(h);b||(x=Object.keys(x).reduce((d,f)=>(x[f]!=null&&(d[f]=x[f]),d),{}));for(const d in u)u[d]!==void 0&&h[d]===void 0&&(x[d]=u[d]);return Object.keys(x).length>0?x:void 0}s.compose=r;function i(u={},h={}){typeof u!="object"&&(u={}),typeof h!="object"&&(h={});const b=Object.keys(u).concat(Object.keys(h)).reduce((x,d)=>(t(u[d],h[d])||(x[d]=h[d]===void 0?null:h[d]),x),{});return Object.keys(b).length>0?b:void 0}s.diff=i;function o(u={},h={}){u=u||{};const b=Object.keys(h).reduce((x,d)=>(h[d]!==u[d]&&u[d]!==void 0&&(x[d]=h[d]),x),{});return Object.keys(u).reduce((x,d)=>(u[d]!==h[d]&&h[d]===void 0&&(x[d]=null),x),b)}s.invert=o;function a(u,h,b=!1){if(typeof u!="object")return h;if(typeof h!="object")return;if(!b)return h;const x=Object.keys(h).reduce((d,f)=>(u[f]===void 0&&(d[f]=h[f]),d),{});return Object.keys(x).length>0?x:void 0}s.transform=a}(e||(e={})),cr.default=e,cr}var ur={},Ti;function Nl(){if(Ti)return ur;Ti=1,Object.defineProperty(ur,"__esModule",{value:!0});var n;return function(t){function e(s){return typeof s.delete=="number"?s.delete:typeof s.retain=="number"?s.retain:typeof s.retain=="object"&&s.retain!==null?1:typeof s.insert=="string"?s.insert.length:1}t.length=e}(n||(n={})),ur.default=n,ur}var hr={},Li;function Ic(){if(Li)return hr;Li=1,Object.defineProperty(hr,"__esModule",{value:!0});const n=Nl();class t{constructor(s){this.ops=s,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(s){s||(s=1/0);const r=this.ops[this.index];if(r){const i=this.offset,o=n.default.length(r);if(s>=o-i?(s=o-i,this.index+=1,this.offset=0):this.offset+=s,typeof r.delete=="number")return{delete:s};{const a={};return r.attributes&&(a.attributes=r.attributes),typeof r.retain=="number"?a.retain=s:typeof r.retain=="object"&&r.retain!==null?a.retain=r.retain:typeof r.insert=="string"?a.insert=r.insert.substr(i,s):a.insert=r.insert,a}}else return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?n.default.length(this.ops[this.index])-this.offset:1/0}peekType(){const s=this.ops[this.index];return s?typeof s.delete=="number"?"delete":typeof s.retain=="number"||typeof s.retain=="object"&&s.retain!==null?"retain":"insert":"retain"}rest(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);{const s=this.offset,r=this.index,i=this.next(),o=this.ops.slice(this.index);return this.offset=s,this.index=r,[i].concat(o)}}else return[]}}return hr.default=t,hr}var Si;function Rc(){return Si||(Si=1,function(n,t){Object.defineProperty(t,"__esModule",{value:!0}),t.AttributeMap=t.OpIterator=t.Op=void 0;const e=_c(),s=vl(),r=xl(),i=Oc();t.AttributeMap=i.default;const o=Nl();t.Op=o.default;const a=Ic();t.OpIterator=a.default;const u="\0",h=(x,d)=>{if(typeof x!="object"||x===null)throw new Error(`cannot retain a ${typeof x}`);if(typeof d!="object"||d===null)throw new Error(`cannot retain a ${typeof d}`);const f=Object.keys(x)[0];if(!f||f!==Object.keys(d)[0])throw new Error(`embed types not matched: ${f} != ${Object.keys(d)[0]}`);return[f,x[f],d[f]]};class b{constructor(d){Array.isArray(d)?this.ops=d:d!=null&&Array.isArray(d.ops)?this.ops=d.ops:this.ops=[]}static registerEmbed(d,f){this.handlers[d]=f}static unregisterEmbed(d){delete this.handlers[d]}static getHandler(d){const f=this.handlers[d];if(!f)throw new Error(`no handlers for embed type "${d}"`);return f}insert(d,f){const v={};return typeof d=="string"&&d.length===0?this:(v.insert=d,f!=null&&typeof f=="object"&&Object.keys(f).length>0&&(v.attributes=f),this.push(v))}delete(d){return d<=0?this:this.push({delete:d})}retain(d,f){if(typeof d=="number"&&d<=0)return this;const v={retain:d};return f!=null&&typeof f=="object"&&Object.keys(f).length>0&&(v.attributes=f),this.push(v)}push(d){let f=this.ops.length,v=this.ops[f-1];if(d=s(d),typeof v=="object"){if(typeof d.delete=="number"&&typeof v.delete=="number")return this.ops[f-1]={delete:v.delete+d.delete},this;if(typeof v.delete=="number"&&d.insert!=null&&(f-=1,v=this.ops[f-1],typeof v!="object"))return this.ops.unshift(d),this;if(r(d.attributes,v.attributes)){if(typeof d.insert=="string"&&typeof v.insert=="string")return this.ops[f-1]={insert:v.insert+d.insert},typeof d.attributes=="object"&&(this.ops[f-1].attributes=d.attributes),this;if(typeof d.retain=="number"&&typeof v.retain=="number")return this.ops[f-1]={retain:v.retain+d.retain},typeof d.attributes=="object"&&(this.ops[f-1].attributes=d.attributes),this}}return f===this.ops.length?this.ops.push(d):this.ops.splice(f,0,d),this}chop(){const d=this.ops[this.ops.length-1];return d&&typeof d.retain=="number"&&!d.attributes&&this.ops.pop(),this}filter(d){return this.ops.filter(d)}forEach(d){this.ops.forEach(d)}map(d){return this.ops.map(d)}partition(d){const f=[],v=[];return this.forEach(E=>{(d(E)?f:v).push(E)}),[f,v]}reduce(d,f){return this.ops.reduce(d,f)}changeLength(){return this.reduce((d,f)=>f.insert?d+o.default.length(f):f.delete?d-f.delete:d,0)}length(){return this.reduce((d,f)=>d+o.default.length(f),0)}slice(d=0,f=1/0){const v=[],E=new a.default(this.ops);let C=0;for(;C<f&&E.hasNext();){let S;C<d?S=E.next(d-C):(S=E.next(f-C),v.push(S)),C+=o.default.length(S)}return new b(v)}compose(d){const f=new a.default(this.ops),v=new a.default(d.ops),E=[],C=v.peek();if(C!=null&&typeof C.retain=="number"&&C.attributes==null){let M=C.retain;for(;f.peekType()==="insert"&&f.peekLength()<=M;)M-=f.peekLength(),E.push(f.next());C.retain-M>0&&v.next(C.retain-M)}const S=new b(E);for(;f.hasNext()||v.hasNext();)if(v.peekType()==="insert")S.push(v.next());else if(f.peekType()==="delete")S.push(f.next());else{const M=Math.min(f.peekLength(),v.peekLength()),P=f.next(M),tt=v.next(M);if(tt.retain){const U={};if(typeof P.retain=="number")U.retain=typeof tt.retain=="number"?M:tt.retain;else if(typeof tt.retain=="number")P.retain==null?U.insert=P.insert:U.retain=P.retain;else{const rt=P.retain==null?"insert":"retain",[st,ht,ft]=h(P[rt],tt.retain),g=b.getHandler(st);U[rt]={[st]:g.compose(ht,ft,rt==="retain")}}const Q=i.default.compose(P.attributes,tt.attributes,typeof P.retain=="number");if(Q&&(U.attributes=Q),S.push(U),!v.hasNext()&&r(S.ops[S.ops.length-1],U)){const rt=new b(f.rest());return S.concat(rt).chop()}}else typeof tt.delete=="number"&&(typeof P.retain=="number"||typeof P.retain=="object"&&P.retain!==null)&&S.push(tt)}return S.chop()}concat(d){const f=new b(this.ops.slice());return d.ops.length>0&&(f.push(d.ops[0]),f.ops=f.ops.concat(d.ops.slice(1))),f}diff(d,f){if(this.ops===d.ops)return new b;const v=[this,d].map(P=>P.map(tt=>{if(tt.insert!=null)return typeof tt.insert=="string"?tt.insert:u;const U=P===d?"on":"with";throw new Error("diff() called "+U+" non-document")}).join("")),E=new b,C=e(v[0],v[1],f,!0),S=new a.default(this.ops),M=new a.default(d.ops);return C.forEach(P=>{let tt=P[1].length;for(;tt>0;){let U=0;switch(P[0]){case e.INSERT:U=Math.min(M.peekLength(),tt),E.push(M.next(U));break;case e.DELETE:U=Math.min(tt,S.peekLength()),S.next(U),E.delete(U);break;case e.EQUAL:U=Math.min(S.peekLength(),M.peekLength(),tt);const Q=S.next(U),rt=M.next(U);r(Q.insert,rt.insert)?E.retain(U,i.default.diff(Q.attributes,rt.attributes)):E.push(rt).delete(U);break}tt-=U}}),E.chop()}eachLine(d,f=`
`){const v=new a.default(this.ops);let E=new b,C=0;for(;v.hasNext();){if(v.peekType()!=="insert")return;const S=v.peek(),M=o.default.length(S)-v.peekLength(),P=typeof S.insert=="string"?S.insert.indexOf(f,M)-M:-1;if(P<0)E.push(v.next());else if(P>0)E.push(v.next(P));else{if(d(E,v.next(1).attributes||{},C)===!1)return;C+=1,E=new b}}E.length()>0&&d(E,{},C)}invert(d){const f=new b;return this.reduce((v,E)=>{if(E.insert)f.delete(o.default.length(E));else{if(typeof E.retain=="number"&&E.attributes==null)return f.retain(E.retain),v+E.retain;if(E.delete||typeof E.retain=="number"){const C=E.delete||E.retain;return d.slice(v,v+C).forEach(M=>{E.delete?f.push(M):E.retain&&E.attributes&&f.retain(o.default.length(M),i.default.invert(E.attributes,M.attributes))}),v+C}else if(typeof E.retain=="object"&&E.retain!==null){const C=d.slice(v,v+1),S=new a.default(C.ops).next(),[M,P,tt]=h(E.retain,S.insert),U=b.getHandler(M);return f.retain({[M]:U.invert(P,tt)},i.default.invert(E.attributes,S.attributes)),v+1}}return v},0),f.chop()}transform(d,f=!1){if(f=!!f,typeof d=="number")return this.transformPosition(d,f);const v=d,E=new a.default(this.ops),C=new a.default(v.ops),S=new b;for(;E.hasNext()||C.hasNext();)if(E.peekType()==="insert"&&(f||C.peekType()!=="insert"))S.retain(o.default.length(E.next()));else if(C.peekType()==="insert")S.push(C.next());else{const M=Math.min(E.peekLength(),C.peekLength()),P=E.next(M),tt=C.next(M);if(P.delete)continue;if(tt.delete)S.push(tt);else{const U=P.retain,Q=tt.retain;let rt=typeof Q=="object"&&Q!==null?Q:M;if(typeof U=="object"&&U!==null&&typeof Q=="object"&&Q!==null){const st=Object.keys(U)[0];if(st===Object.keys(Q)[0]){const ht=b.getHandler(st);ht&&(rt={[st]:ht.transform(U[st],Q[st],f)})}}S.retain(rt,i.default.transform(P.attributes,tt.attributes,f))}}return S.chop()}transformPosition(d,f=!1){f=!!f;const v=new a.default(this.ops);let E=0;for(;v.hasNext()&&E<=d;){const C=v.peekLength(),S=v.peekType();if(v.next(),S==="delete"){d-=Math.min(C,d-E);continue}else S==="insert"&&(E<d||!f)&&(d+=C);E+=C}return d}}b.Op=o.default,b.OpIterator=a.default,b.AttributeMap=i.default,b.handlers={},t.default=b,n.exports=b,n.exports.default=b}(ar,ar.exports)),ar.exports}var Dt=Rc();const V=Wn(Dt);class Yt extends Ot{static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return""}}Yt.blotName="break";Yt.tagName="BR";let Zt=class extends Nr{};const jc={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Tr(n){return n.replace(/[&<>"']/g,t=>jc[t])}const re=class re extends Yn{static compare(t,e){const s=re.order.indexOf(t),r=re.order.indexOf(e);return s>=0||r>=0?s-r:t===e?0:t<e?-1:1}formatAt(t,e,s,r){if(re.compare(this.statics.blotName,s)<0&&this.scroll.query(s,G.BLOT)){const i=this.isolate(t,e);r&&i.wrap(s,r)}else super.formatAt(t,e,s,r)}optimize(t){if(super.optimize(t),this.parent instanceof re&&re.compare(this.statics.blotName,this.parent.statics.blotName)>0){const e=this.parent.isolate(this.offset(),this.length());this.moveChildren(e),e.wrap(this)}}};j(re,"allowedChildren",[re,Yt,Ot,Zt]),j(re,"order",["cursor","inline","link","underline","strike","italic","bold","script","code"]);let le=re;const qi=1;class Et extends Rs{constructor(){super(...arguments);j(this,"cache",{})}delta(){return this.cache.delta==null&&(this.cache.delta=El(this)),this.cache.delta}deleteAt(e,s){super.deleteAt(e,s),this.cache={}}formatAt(e,s,r,i){s<=0||(this.scroll.query(r,G.BLOCK)?e+s===this.length()&&this.format(r,i):super.formatAt(e,Math.min(s,this.length()-e-1),r,i),this.cache={})}insertAt(e,s,r){if(r!=null){super.insertAt(e,s,r),this.cache={};return}if(s.length===0)return;const i=s.split(`
`),o=i.shift();o.length>0&&(e<this.length()-1||this.children.tail==null?super.insertAt(Math.min(e,this.length()-1),o):this.children.tail.insertAt(this.children.tail.length(),o),this.cache={});let a=this;i.reduce((u,h)=>(a=a.split(u,!0),a.insertAt(0,h),h.length),e+o.length)}insertBefore(e,s){const{head:r}=this.children;super.insertBefore(e,s),r instanceof Yt&&r.remove(),this.cache={}}length(){return this.cache.length==null&&(this.cache.length=super.length()+qi),this.cache.length}moveChildren(e,s){super.moveChildren(e,s),this.cache={}}optimize(e){super.optimize(e),this.cache={}}path(e){return super.path(e,!0)}removeChild(e){super.removeChild(e),this.cache={}}split(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(s&&(e===0||e>=this.length()-qi)){const i=this.clone();return e===0?(this.parent.insertBefore(i,this),this):(this.parent.insertBefore(i,this.next),i)}const r=super.split(e,s);return this.cache={},r}}Et.blotName="block";Et.tagName="P";Et.defaultChild=Yt;Et.allowedChildren=[Yt,le,Ot,Zt];let Bt=class extends Ot{attach(){super.attach(),this.attributes=new Ar(this.domNode)}delta(){return new V().insert(this.value(),{...this.formats(),...this.attributes.values()})}format(t,e){const s=this.scroll.query(t,G.BLOCK_ATTRIBUTE);s!=null&&this.attributes.attribute(s,e)}formatAt(t,e,s,r){this.format(s,r)}insertAt(t,e,s){if(s!=null){super.insertAt(t,e,s);return}const r=e.split(`
`),i=r.pop(),o=r.map(u=>{const h=this.scroll.create(Et.blotName);return h.insertAt(0,u),h}),a=this.split(t);o.forEach(u=>{this.parent.insertBefore(u,a)}),i&&this.parent.insertBefore(this.scroll.create("text",i),a)}};Bt.scope=G.BLOCK_BLOT;function El(n){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return n.descendants(Tt).reduce((e,s)=>s.length()===0?e:e.insert(s.value(),jt(s,{},t)),new V).insert(`
`,jt(n))}function jt(n){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return n==null||("formats"in n&&typeof n.formats=="function"&&(t={...t,...n.formats()},e&&delete t["code-token"]),n.parent==null||n.parent.statics.blotName==="scroll"||n.parent.statics.scope!==n.statics.scope)?t:jt(n.parent,t,e)}const Rt=class Rt extends Ot{static value(){}constructor(t,e,s){super(t,e),this.selection=s,this.textNode=document.createTextNode(Rt.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){this.parent!=null&&this.parent.removeChild(this)}format(t,e){if(this.savedLength!==0){super.format(t,e);return}let s=this,r=0;for(;s!=null&&s.statics.scope!==G.BLOCK_BLOT;)r+=s.offset(s.parent),s=s.parent;s!=null&&(this.savedLength=Rt.CONTENTS.length,s.optimize(),s.formatAt(r,Rt.CONTENTS.length,t,e),this.savedLength=0)}index(t,e){return t===this.textNode?0:super.index(t,e)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){if(this.selection.composing||this.parent==null)return null;const t=this.selection.getNativeRange();for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);const e=this.prev instanceof Zt?this.prev:null,s=e?e.length():0,r=this.next instanceof Zt?this.next:null,i=r?r.text:"",{textNode:o}=this,a=o.data.split(Rt.CONTENTS).join("");o.data=Rt.CONTENTS;let u;if(e)u=e,(a||r)&&(e.insertAt(e.length(),a+i),r&&r.remove());else if(r)u=r,r.insertAt(0,a);else{const h=document.createTextNode(a);u=this.scroll.create(h),this.parent.insertBefore(u,this)}if(this.remove(),t){const h=(d,f)=>e&&d===e.domNode?f:d===o?s+f-1:r&&d===r.domNode?s+a.length+f:null,b=h(t.start.node,t.start.offset),x=h(t.end.node,t.end.offset);if(b!==null&&x!==null)return{startNode:u.domNode,startOffset:b,endNode:u.domNode,endOffset:x}}return null}update(t,e){if(t.some(s=>s.type==="characterData"&&s.target===this.textNode)){const s=this.restore();s&&(e.range=s)}}optimize(t){super.optimize(t);let{parent:e}=this;for(;e;){if(e.domNode.tagName==="A"){this.savedLength=Rt.CONTENTS.length,e.isolate(this.offset(e),this.length()).unwrap(),this.savedLength=0;break}e=e.parent}}value(){return""}};j(Rt,"blotName","cursor"),j(Rt,"className","ql-cursor"),j(Rt,"tagName","span"),j(Rt,"CONTENTS","\uFEFF");let os=Rt;var xn={exports:{}},ki;function Mc(){return ki||(ki=1,function(n){var t=Object.prototype.hasOwnProperty,e="~";function s(){}Object.create&&(s.prototype=Object.create(null),new s().__proto__||(e=!1));function r(u,h,b){this.fn=u,this.context=h,this.once=b||!1}function i(u,h,b,x,d){if(typeof b!="function")throw new TypeError("The listener must be a function");var f=new r(b,x||u,d),v=e?e+h:h;return u._events[v]?u._events[v].fn?u._events[v]=[u._events[v],f]:u._events[v].push(f):(u._events[v]=f,u._eventsCount++),u}function o(u,h){--u._eventsCount===0?u._events=new s:delete u._events[h]}function a(){this._events=new s,this._eventsCount=0}a.prototype.eventNames=function(){var h=[],b,x;if(this._eventsCount===0)return h;for(x in b=this._events)t.call(b,x)&&h.push(e?x.slice(1):x);return Object.getOwnPropertySymbols?h.concat(Object.getOwnPropertySymbols(b)):h},a.prototype.listeners=function(h){var b=e?e+h:h,x=this._events[b];if(!x)return[];if(x.fn)return[x.fn];for(var d=0,f=x.length,v=new Array(f);d<f;d++)v[d]=x[d].fn;return v},a.prototype.listenerCount=function(h){var b=e?e+h:h,x=this._events[b];return x?x.fn?1:x.length:0},a.prototype.emit=function(h,b,x,d,f,v){var E=e?e+h:h;if(!this._events[E])return!1;var C=this._events[E],S=arguments.length,M,P;if(C.fn){switch(C.once&&this.removeListener(h,C.fn,void 0,!0),S){case 1:return C.fn.call(C.context),!0;case 2:return C.fn.call(C.context,b),!0;case 3:return C.fn.call(C.context,b,x),!0;case 4:return C.fn.call(C.context,b,x,d),!0;case 5:return C.fn.call(C.context,b,x,d,f),!0;case 6:return C.fn.call(C.context,b,x,d,f,v),!0}for(P=1,M=new Array(S-1);P<S;P++)M[P-1]=arguments[P];C.fn.apply(C.context,M)}else{var tt=C.length,U;for(P=0;P<tt;P++)switch(C[P].once&&this.removeListener(h,C[P].fn,void 0,!0),S){case 1:C[P].fn.call(C[P].context);break;case 2:C[P].fn.call(C[P].context,b);break;case 3:C[P].fn.call(C[P].context,b,x);break;case 4:C[P].fn.call(C[P].context,b,x,d);break;default:if(!M)for(U=1,M=new Array(S-1);U<S;U++)M[U-1]=arguments[U];C[P].fn.apply(C[P].context,M)}}return!0},a.prototype.on=function(h,b,x){return i(this,h,b,x,!1)},a.prototype.once=function(h,b,x){return i(this,h,b,x,!0)},a.prototype.removeListener=function(h,b,x,d){var f=e?e+h:h;if(!this._events[f])return this;if(!b)return o(this,f),this;var v=this._events[f];if(v.fn)v.fn===b&&(!d||v.once)&&(!x||v.context===x)&&o(this,f);else{for(var E=0,C=[],S=v.length;E<S;E++)(v[E].fn!==b||d&&!v[E].once||x&&v[E].context!==x)&&C.push(v[E]);C.length?this._events[f]=C.length===1?C[0]:C:o(this,f)}return this},a.prototype.removeAllListeners=function(h){var b;return h?(b=e?e+h:h,this._events[b]&&o(this,b)):(this._events=new s,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=e,a.EventEmitter=a,n.exports=a}(xn)),xn.exports}var Bc=Mc();const Dc=Wn(Bc),In=new WeakMap,Rn=["error","warn","log","info"];let jn="warn";function wl(n){if(jn&&Rn.indexOf(n)<=Rn.indexOf(jn)){for(var t=arguments.length,e=new Array(t>1?t-1:0),s=1;s<t;s++)e[s-1]=arguments[s];console[n](...e)}}function pe(n){return Rn.reduce((t,e)=>(t[e]=wl.bind(console,e,n),t),{})}pe.level=n=>{jn=n};wl.level=pe.level;const Nn=pe("quill:events"),Pc=["selectionchange","mousedown","mouseup","click"];Pc.forEach(n=>{document.addEventListener(n,function(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];Array.from(document.querySelectorAll(".ql-container")).forEach(r=>{const i=In.get(r);i&&i.emitter&&i.emitter.handleDOM(...e)})})});class $ extends Dc{constructor(){super(),this.domListeners={},this.on("error",Nn.error)}emit(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];return Nn.log.call(Nn,...e),super.emit(...e)}handleDOM(t){for(var e=arguments.length,s=new Array(e>1?e-1:0),r=1;r<e;r++)s[r-1]=arguments[r];(this.domListeners[t.type]||[]).forEach(i=>{let{node:o,handler:a}=i;(t.target===o||o.contains(t.target))&&a(t,...s)})}listenDOM(t,e,s){this.domListeners[t]||(this.domListeners[t]=[]),this.domListeners[t].push({node:e,handler:s})}}j($,"events",{EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SCROLL_EMBED_UPDATE:"scroll-embed-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"}),j($,"sources",{API:"api",SILENT:"silent",USER:"user"});const En=pe("quill:selection");class _e{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;this.index=t,this.length=e}}class Uc{constructor(t,e){this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new _e(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,()=>{!this.mouseDown&&!this.composing&&setTimeout(this.update.bind(this,$.sources.USER),1)}),this.emitter.on($.events.SCROLL_BEFORE_UPDATE,()=>{if(!this.hasFocus())return;const s=this.getNativeRange();s!=null&&s.start.node!==this.cursor.textNode&&this.emitter.once($.events.SCROLL_UPDATE,(r,i)=>{try{this.root.contains(s.start.node)&&this.root.contains(s.end.node)&&this.setNativeRange(s.start.node,s.start.offset,s.end.node,s.end.offset);const o=i.some(a=>a.type==="characterData"||a.type==="childList"||a.type==="attributes"&&a.target===this.root);this.update(o?$.sources.SILENT:r)}catch{}})}),this.emitter.on($.events.SCROLL_OPTIMIZE,(s,r)=>{if(r.range){const{startNode:i,startOffset:o,endNode:a,endOffset:u}=r.range;this.setNativeRange(i,o,a,u),this.update($.sources.SILENT)}}),this.update($.sources.SILENT)}handleComposition(){this.emitter.on($.events.COMPOSITION_BEFORE_START,()=>{this.composing=!0}),this.emitter.on($.events.COMPOSITION_END,()=>{if(this.composing=!1,this.cursor.parent){const t=this.cursor.restore();if(!t)return;setTimeout(()=>{this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)},1)}})}handleDragging(){this.emitter.listenDOM("mousedown",document.body,()=>{this.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,()=>{this.mouseDown=!1,this.update($.sources.USER)})}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(t,e){this.scroll.update();const s=this.getNativeRange();if(!(s==null||!s.native.collapsed||this.scroll.query(t,G.BLOCK))){if(s.start.node!==this.cursor.textNode){const r=this.scroll.find(s.start.node,!1);if(r==null)return;if(r instanceof Tt){const i=r.split(s.start.offset);r.parent.insertBefore(this.cursor,i)}else r.insertBefore(this.cursor,s.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;const s=this.scroll.length();t=Math.min(t,s-1),e=Math.min(t+e,s-1)-t;let r,[i,o]=this.scroll.leaf(t);if(i==null)return null;if(e>0&&o===i.length()){const[b]=this.scroll.leaf(t+1);if(b){const[x]=this.scroll.line(t),[d]=this.scroll.line(t+1);x===d&&(i=b,o=0)}}[r,o]=i.position(o,!0);const a=document.createRange();if(e>0)return a.setStart(r,o),[i,o]=this.scroll.leaf(t+e),i==null?null:([r,o]=i.position(o,!0),a.setEnd(r,o),a.getBoundingClientRect());let u="left",h;if(r instanceof Text){if(!r.data.length)return null;o<r.data.length?(a.setStart(r,o),a.setEnd(r,o+1)):(a.setStart(r,o-1),a.setEnd(r,o),u="right"),h=a.getBoundingClientRect()}else{if(!(i.domNode instanceof Element))return null;h=i.domNode.getBoundingClientRect(),o>0&&(u="right")}return{bottom:h.top+h.height,height:h.height,left:h[u],right:h[u],top:h.top,width:0}}getNativeRange(){const t=document.getSelection();if(t==null||t.rangeCount<=0)return null;const e=t.getRangeAt(0);if(e==null)return null;const s=this.normalizeNative(e);return En.info("getNativeRange",s),s}getRange(){const t=this.scroll.domNode;if("isConnected"in t&&!t.isConnected)return[null,null];const e=this.getNativeRange();return e==null?[null,null]:[this.normalizedToRange(e),e]}hasFocus(){return document.activeElement===this.root||document.activeElement!=null&&wn(this.root,document.activeElement)}normalizedToRange(t){const e=[[t.start.node,t.start.offset]];t.native.collapsed||e.push([t.end.node,t.end.offset]);const s=e.map(o=>{const[a,u]=o,h=this.scroll.find(a,!0),b=h.offset(this.scroll);return u===0?b:h instanceof Tt?b+h.index(a,u):b+h.length()}),r=Math.min(Math.max(...s),this.scroll.length()-1),i=Math.min(r,...s);return new _e(i,r-i)}normalizeNative(t){if(!wn(this.root,t.startContainer)||!t.collapsed&&!wn(this.root,t.endContainer))return null;const e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach(s=>{let{node:r,offset:i}=s;for(;!(r instanceof Text)&&r.childNodes.length>0;)if(r.childNodes.length>i)r=r.childNodes[i],i=0;else if(r.childNodes.length===i)r=r.lastChild,r instanceof Text?i=r.data.length:r.childNodes.length>0?i=r.childNodes.length:i=r.childNodes.length+1;else break;s.node=r,s.offset=i}),e}rangeToNative(t){const e=this.scroll.length(),s=(r,i)=>{r=Math.min(e-1,r);const[o,a]=this.scroll.leaf(r);return o?o.position(a,i):[null,-1]};return[...s(t.index,!1),...s(t.index+t.length,!0)]}setNativeRange(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:e,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(En.info("setNativeRange",t,e,s,r),t!=null&&(this.root.parentNode==null||t.parentNode==null||s.parentNode==null))return;const o=document.getSelection();if(o!=null)if(t!=null){this.hasFocus()||this.root.focus({preventScroll:!0});const{native:a}=this.getNativeRange()||{};if(a==null||i||t!==a.startContainer||e!==a.startOffset||s!==a.endContainer||r!==a.endOffset){t instanceof Element&&t.tagName==="BR"&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),s instanceof Element&&s.tagName==="BR"&&(r=Array.from(s.parentNode.childNodes).indexOf(s),s=s.parentNode);const u=document.createRange();u.setStart(t,e),u.setEnd(s,r),o.removeAllRanges(),o.addRange(u)}}else o.removeAllRanges(),this.root.blur()}setRange(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:$.sources.API;if(typeof e=="string"&&(s=e,e=!1),En.info("setRange",t),t!=null){const r=this.rangeToNative(t);this.setNativeRange(...r,e)}else this.setNativeRange(null);this.update(s)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:$.sources.USER;const e=this.lastRange,[s,r]=this.getRange();if(this.lastRange=s,this.lastNative=r,this.lastRange!=null&&(this.savedRange=this.lastRange),!Xn(e,this.lastRange)){if(!this.composing&&r!=null&&r.native.collapsed&&r.start.node!==this.cursor.textNode){const o=this.cursor.restore();o&&this.setNativeRange(o.startNode,o.startOffset,o.endNode,o.endOffset)}const i=[$.events.SELECTION_CHANGE,ss(this.lastRange),ss(e),t];this.emitter.emit($.events.EDITOR_CHANGE,...i),t!==$.sources.SILENT&&this.emitter.emit(...i)}}}function wn(n,t){try{t.parentNode}catch{return!1}return n.contains(t)}const $c=/^[ -~]*$/;class Fc{constructor(t){this.scroll=t,this.delta=this.getDelta()}applyDelta(t){this.scroll.update();let e=this.scroll.length();this.scroll.batchStart();const s=_i(t),r=new V;return zc(s.ops.slice()).reduce((o,a)=>{const u=Dt.Op.length(a);let h=a.attributes||{},b=!1,x=!1;if(a.insert!=null){if(r.retain(u),typeof a.insert=="string"){const v=a.insert;x=!v.endsWith(`
`)&&(e<=o||!!this.scroll.descendant(Bt,o)[0]),this.scroll.insertAt(o,v);const[E,C]=this.scroll.line(o);let S=Ee({},jt(E));if(E instanceof Et){const[M]=E.descendant(Tt,C);M&&(S=Ee(S,jt(M)))}h=Dt.AttributeMap.diff(S,h)||{}}else if(typeof a.insert=="object"){const v=Object.keys(a.insert)[0];if(v==null)return o;const E=this.scroll.query(v,G.INLINE)!=null;if(E)(e<=o||this.scroll.descendant(Bt,o)[0])&&(x=!0);else if(o>0){const[C,S]=this.scroll.descendant(Tt,o-1);C instanceof Zt?C.value()[S]!==`
`&&(b=!0):C instanceof Ot&&C.statics.scope===G.INLINE_BLOT&&(b=!0)}if(this.scroll.insertAt(o,v,a.insert[v]),E){const[C]=this.scroll.descendant(Tt,o);if(C){const S=Ee({},jt(C));h=Dt.AttributeMap.diff(S,h)||{}}}}e+=u}else if(r.push(a),a.retain!==null&&typeof a.retain=="object"){const v=Object.keys(a.retain)[0];if(v==null)return o;this.scroll.updateEmbedAt(o,v,a.retain[v])}Object.keys(h).forEach(v=>{this.scroll.formatAt(o,u,v,h[v])});const d=b?1:0,f=x?1:0;return e+=d+f,r.retain(d),r.delete(f),o+u+d+f},0),r.reduce((o,a)=>typeof a.delete=="number"?(this.scroll.deleteAt(o,a.delete),o):o+Dt.Op.length(a),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(s)}deleteText(t,e){return this.scroll.deleteAt(t,e),this.update(new V().retain(t).delete(e))}formatLine(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.scroll.update(),Object.keys(s).forEach(i=>{this.scroll.lines(t,Math.max(e,1)).forEach(o=>{o.format(i,s[i])})}),this.scroll.optimize();const r=new V().retain(t).retain(e,ss(s));return this.update(r)}formatText(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Object.keys(s).forEach(i=>{this.scroll.formatAt(t,e,i,s[i])});const r=new V().retain(t).retain(e,ss(s));return this.update(r)}getContents(t,e){return this.delta.slice(t,t+e)}getDelta(){return this.scroll.lines().reduce((t,e)=>t.concat(e.delta()),new V)}getFormat(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=[],r=[];e===0?this.scroll.path(t).forEach(a=>{const[u]=a;u instanceof Et?s.push(u):u instanceof Tt&&r.push(u)}):(s=this.scroll.lines(t,e),r=this.scroll.descendants(Tt,t,e));const[i,o]=[s,r].map(a=>{const u=a.shift();if(u==null)return{};let h=jt(u);for(;Object.keys(h).length>0;){const b=a.shift();if(b==null)return h;h=Hc(jt(b),h)}return h});return{...i,...o}}getHTML(t,e){const[s,r]=this.scroll.line(t);if(s){const i=s.length();return s.length()>=r+e&&!(r===0&&e===i)?js(s,r,e,!0):js(this.scroll,t,e,!0)}return""}getText(t,e){return this.getContents(t,e).filter(s=>typeof s.insert=="string").map(s=>s.insert).join("")}insertContents(t,e){const s=_i(e),r=new V().retain(t).concat(s);return this.scroll.insertContents(t,s),this.update(r)}insertEmbed(t,e,s){return this.scroll.insertAt(t,e,s),this.update(new V().retain(t).insert({[e]:s}))}insertText(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return e=e.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(t,e),Object.keys(s).forEach(r=>{this.scroll.formatAt(t,e.length,r,s[r])}),this.update(new V().retain(t).insert(e,ss(s)))}isBlank(){if(this.scroll.children.length===0)return!0;if(this.scroll.children.length>1)return!1;const t=this.scroll.children.head;if((t==null?void 0:t.statics.blotName)!==Et.blotName)return!1;const e=t;return e.children.length>1?!1:e.children.head instanceof Yt}removeFormat(t,e){const s=this.getText(t,e),[r,i]=this.scroll.line(t+e);let o=0,a=new V;r!=null&&(o=r.length()-i,a=r.delta().slice(i,i+o-1).insert(`
`));const h=this.getContents(t,e+o).diff(new V().insert(s).concat(a)),b=new V().retain(t).concat(h);return this.applyDelta(b)}update(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0;const r=this.delta;if(e.length===1&&e[0].type==="characterData"&&e[0].target.data.match($c)&&this.scroll.find(e[0].target)){const i=this.scroll.find(e[0].target),o=jt(i),a=i.offset(this.scroll),u=e[0].oldValue.replace(os.CONTENTS,""),h=new V().insert(u),b=new V().insert(i.value()),x=s&&{oldRange:Oi(s.oldRange,-a),newRange:Oi(s.newRange,-a)};t=new V().retain(a).concat(h.diff(b,x)).reduce((f,v)=>v.insert?f.insert(v.insert,o):f.push(v),new V),this.delta=r.compose(t)}else this.delta=this.getDelta(),(!t||!Xn(r.compose(t),this.delta))&&(t=r.diff(this.delta,s));return t}}function Ye(n,t,e){if(n.length===0){const[f]=An(e.pop());return t<=0?`</li></${f}>`:`</li></${f}>${Ye([],t-1,e)}`}const[{child:s,offset:r,length:i,indent:o,type:a},...u]=n,[h,b]=An(a);if(o>t)return e.push(a),o===t+1?`<${h}><li${b}>${js(s,r,i)}${Ye(u,o,e)}`:`<${h}><li>${Ye(n,t+1,e)}`;const x=e[e.length-1];if(o===t&&a===x)return`</li><li${b}>${js(s,r,i)}${Ye(u,o,e)}`;const[d]=An(e.pop());return`</li></${d}>${Ye(n,t-1,e)}`}function js(n,t,e){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if("html"in n&&typeof n.html=="function")return n.html(t,e);if(n instanceof Zt)return Tr(n.value().slice(t,t+e)).replaceAll(" ","&nbsp;");if(n instanceof Wt){if(n.statics.blotName==="list-container"){const h=[];return n.children.forEachAt(t,e,(b,x,d)=>{const f="formats"in b&&typeof b.formats=="function"?b.formats():{};h.push({child:b,offset:x,length:d,indent:f.indent||0,type:f.list})}),Ye(h,-1,[])}const r=[];if(n.children.forEachAt(t,e,(h,b,x)=>{r.push(js(h,b,x))}),s||n.statics.blotName==="list")return r.join("");const{outerHTML:i,innerHTML:o}=n.domNode,[a,u]=i.split(`>${o}<`);return a==="<table"?`<table style="border: 1px solid #000;">${r.join("")}<${u}`:`${a}>${r.join("")}<${u}`}return n.domNode instanceof Element?n.domNode.outerHTML:""}function Hc(n,t){return Object.keys(t).reduce((e,s)=>{if(n[s]==null)return e;const r=t[s];return r===n[s]?e[s]=r:Array.isArray(r)?r.indexOf(n[s])<0?e[s]=r.concat([n[s]]):e[s]=r:e[s]=[r,n[s]],e},{})}function An(n){const t=n==="ordered"?"ol":"ul";switch(n){case"checked":return[t,' data-list="checked"'];case"unchecked":return[t,' data-list="unchecked"'];default:return[t,""]}}function _i(n){return n.reduce((t,e)=>{if(typeof e.insert=="string"){const s=e.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return t.insert(s,e.attributes)}return t.push(e)},new V)}function Oi(n,t){let{index:e,length:s}=n;return new _e(e+t,s)}function zc(n){const t=[];return n.forEach(e=>{typeof e.insert=="string"?e.insert.split(`
`).forEach((r,i)=>{i&&t.push({insert:`
`,attributes:e.attributes}),r&&t.push({insert:r,attributes:e.attributes})}):t.push(e)}),t}class Qt{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.quill=t,this.options=e}}j(Qt,"DEFAULTS",{});const dr="\uFEFF";let Jn=class extends Ot{constructor(t,e){super(t,e),this.contentNode=document.createElement("span"),this.contentNode.setAttribute("contenteditable","false"),Array.from(this.domNode.childNodes).forEach(s=>{this.contentNode.appendChild(s)}),this.leftGuard=document.createTextNode(dr),this.rightGuard=document.createTextNode(dr),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:super.index(t,e)}restore(t){let e=null,s;const r=t.data.split(dr).join("");if(t===this.leftGuard)if(this.prev instanceof Zt){const i=this.prev.length();this.prev.insertAt(i,r),e={startNode:this.prev.domNode,startOffset:i+r.length}}else s=document.createTextNode(r),this.parent.insertBefore(this.scroll.create(s),this),e={startNode:s,startOffset:r.length};else t===this.rightGuard&&(this.next instanceof Zt?(this.next.insertAt(0,r),e={startNode:this.next.domNode,startOffset:r.length}):(s=document.createTextNode(r),this.parent.insertBefore(this.scroll.create(s),this.next),e={startNode:s,startOffset:r.length}));return t.data=dr,e}update(t,e){t.forEach(s=>{if(s.type==="characterData"&&(s.target===this.leftGuard||s.target===this.rightGuard)){const r=this.restore(s.target);r&&(e.range=r)}})}};class Vc{constructor(t,e){j(this,"isComposing",!1);this.scroll=t,this.emitter=e,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener("compositionstart",t=>{this.isComposing||this.handleCompositionStart(t)}),this.scroll.domNode.addEventListener("compositionend",t=>{this.isComposing&&queueMicrotask(()=>{this.handleCompositionEnd(t)})})}handleCompositionStart(t){const e=t.target instanceof Node?this.scroll.find(t.target,!0):null;e&&!(e instanceof Jn)&&(this.emitter.emit($.events.COMPOSITION_BEFORE_START,t),this.scroll.batchStart(),this.emitter.emit($.events.COMPOSITION_START,t),this.isComposing=!0)}handleCompositionEnd(t){this.emitter.emit($.events.COMPOSITION_BEFORE_END,t),this.scroll.batchEnd(),this.emitter.emit($.events.COMPOSITION_END,t),this.isComposing=!1}}const Os=class Os{constructor(t,e){j(this,"modules",{});this.quill=t,this.options=e}init(){Object.keys(this.options.modules).forEach(t=>{this.modules[t]==null&&this.addModule(t)})}addModule(t){const e=this.quill.constructor.import(`modules/${t}`);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}};j(Os,"DEFAULTS",{modules:{}}),j(Os,"themes",{default:Os});let as=Os;const Gc=n=>n.parentElement||n.getRootNode().host||null,Kc=n=>{const t=n.getBoundingClientRect(),e="offsetWidth"in n&&Math.abs(t.width)/n.offsetWidth||1,s="offsetHeight"in n&&Math.abs(t.height)/n.offsetHeight||1;return{top:t.top,right:t.left+n.clientWidth*e,bottom:t.top+n.clientHeight*s,left:t.left}},fr=n=>{const t=parseInt(n,10);return Number.isNaN(t)?0:t},Ii=(n,t,e,s,r,i)=>n<e&&t>s?0:n<e?-(e-n+r):t>s?t-n>s-e?n+r-e:t-s+i:0,Wc=(n,t)=>{var i,o,a;const e=n.ownerDocument;let s=t,r=n;for(;r;){const u=r===e.body,h=u?{top:0,right:((i=window.visualViewport)==null?void 0:i.width)??e.documentElement.clientWidth,bottom:((o=window.visualViewport)==null?void 0:o.height)??e.documentElement.clientHeight,left:0}:Kc(r),b=getComputedStyle(r),x=Ii(s.left,s.right,h.left,h.right,fr(b.scrollPaddingLeft),fr(b.scrollPaddingRight)),d=Ii(s.top,s.bottom,h.top,h.bottom,fr(b.scrollPaddingTop),fr(b.scrollPaddingBottom));if(x||d)if(u)(a=e.defaultView)==null||a.scrollBy(x,d);else{const{scrollLeft:f,scrollTop:v}=r;d&&(r.scrollTop+=d),x&&(r.scrollLeft+=x);const E=r.scrollLeft-f,C=r.scrollTop-v;s={left:s.left-E,top:s.top-C,right:s.right-E,bottom:s.bottom-C}}r=u||b.position==="fixed"?null:Gc(r)}},Zc=100,Xc=["block","break","cursor","inline","scroll","text"],Yc=(n,t,e)=>{const s=new ls;return Xc.forEach(r=>{const i=t.query(r);i&&s.register(i)}),n.forEach(r=>{let i=t.query(r);i||e.error(`Cannot register "${r}" specified in "formats" config. Are you sure it was registered?`);let o=0;for(;i;)if(s.register(i),i="blotName"in i?i.requiredContainer??null:null,o+=1,o>Zc){e.error(`Cycle detected in registering blot requiredContainer: "${r}"`);break}}),s},ns=pe("quill"),pr=new ls;Wt.uiClass="ql-ui";const Vt=class Vt{static debug(t){t===!0&&(t="log"),pe.level(t)}static find(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return In.get(t)||pr.find(t,e)}static import(t){return this.imports[t]==null&&ns.error(`Cannot import ${t}. Are you sure it was registered?`),this.imports[t]}static register(){if(typeof(arguments.length<=0?void 0:arguments[0])!="string"){const t=arguments.length<=0?void 0:arguments[0],e=!!(!(arguments.length<=1)&&arguments[1]),s="attrName"in t?t.attrName:t.blotName;typeof s=="string"?this.register(`formats/${s}`,t,e):Object.keys(t).forEach(r=>{this.register(r,t[r],e)})}else{const t=arguments.length<=0?void 0:arguments[0],e=arguments.length<=1?void 0:arguments[1],s=!!(!(arguments.length<=2)&&arguments[2]);this.imports[t]!=null&&!s&&ns.warn(`Overwriting ${t} with`,e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&e&&typeof e!="boolean"&&e.blotName!=="abstract"&&pr.register(e),typeof e.register=="function"&&e.register(pr)}}constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.options=Qc(t,e),this.container=this.options.container,this.container==null){ns.error("Invalid Quill container",t);return}this.options.debug&&Vt.debug(this.options.debug);const s=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",In.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.emitter=new $;const r=Qn.blotName,i=this.options.registry.query(r);if(!i||!("blotName"in i))throw new Error(`Cannot initialize Quill without "${r}" blot`);if(this.scroll=new i(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new Fc(this.scroll),this.selection=new Uc(this.scroll,this.emitter),this.composition=new Vc(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.addModule("uiNode"),this.theme.init(),this.emitter.on($.events.EDITOR_CHANGE,o=>{o===$.events.TEXT_CHANGE&&this.root.classList.toggle("ql-blank",this.editor.isBlank())}),this.emitter.on($.events.SCROLL_UPDATE,(o,a)=>{const u=this.selection.lastRange,[h]=this.selection.getRange(),b=u&&h?{oldRange:u,newRange:h}:void 0;zt.call(this,()=>this.editor.update(null,a,b),o)}),this.emitter.on($.events.SCROLL_EMBED_UPDATE,(o,a)=>{const u=this.selection.lastRange,[h]=this.selection.getRange(),b=u&&h?{oldRange:u,newRange:h}:void 0;zt.call(this,()=>{const x=new V().retain(o.offset(this)).retain({[o.statics.blotName]:a});return this.editor.update(x,[],b)},Vt.sources.USER)}),s){const o=this.clipboard.convert({html:`${s}<p><br></p>`,text:`
`});this.setContents(o)}this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof t=="string"){const s=t;t=document.createElement("div"),t.classList.add(s)}return this.container.insertBefore(t,e),t}blur(){this.selection.setRange(null)}deleteText(t,e,s){return[t,e,,s]=he(t,e,s),zt.call(this,()=>this.editor.deleteText(t,e),s,t,-1*e)}disable(){this.enable(!1)}editReadOnly(t){this.allowReadOnlyEdits=!0;const e=t();return this.allowReadOnlyEdits=!1,e}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}focus(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.selection.focus(),t.preventScroll||this.scrollSelectionIntoView()}format(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:$.sources.API;return zt.call(this,()=>{const r=this.getSelection(!0);let i=new V;if(r==null)return i;if(this.scroll.query(t,G.BLOCK))i=this.editor.formatLine(r.index,r.length,{[t]:e});else{if(r.length===0)return this.selection.format(t,e),i;i=this.editor.formatText(r.index,r.length,{[t]:e})}return this.setSelection(r,$.sources.SILENT),i},s)}formatLine(t,e,s,r,i){let o;return[t,e,o,i]=he(t,e,s,r,i),zt.call(this,()=>this.editor.formatLine(t,e,o),i,t,0)}formatText(t,e,s,r,i){let o;return[t,e,o,i]=he(t,e,s,r,i),zt.call(this,()=>this.editor.formatText(t,e,o),i,t,0)}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=null;if(typeof t=="number"?s=this.selection.getBounds(t,e):s=this.selection.getBounds(t.index,t.length),!s)return null;const r=this.container.getBoundingClientRect();return{bottom:s.bottom-r.top,height:s.height,left:s.left-r.left,right:s.right-r.left,top:s.top-r.top,width:s.width}}getContents(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-t;return[t,e]=he(t,e),this.editor.getContents(t,e)}getFormat(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof t=="number"?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}getIndex(t){return t.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(t){return this.scroll.leaf(t)}getLine(t){return this.scroll.line(t)}getLines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof t!="number"?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}getModule(t){return this.theme.modules[t]}getSelection(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e??this.getLength()-t),[t,e]=he(t,e),this.editor.getHTML(t,e)}getText(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e??this.getLength()-t),[t,e]=he(t,e),this.editor.getText(t,e)}hasFocus(){return this.selection.hasFocus()}insertEmbed(t,e,s){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:Vt.sources.API;return zt.call(this,()=>this.editor.insertEmbed(t,e,s),r,t)}insertText(t,e,s,r,i){let o;return[t,,o,i]=he(t,0,s,r,i),zt.call(this,()=>this.editor.insertText(t,e,o),i,t,e.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(t,e,s){return[t,e,,s]=he(t,e,s),zt.call(this,()=>this.editor.removeFormat(t,e),s,t)}scrollRectIntoView(t){Wc(this.root,t)}scrollIntoView(){console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."),this.scrollSelectionIntoView()}scrollSelectionIntoView(){const t=this.selection.lastRange,e=t&&this.selection.getBounds(t.index,t.length);e&&this.scrollRectIntoView(e)}setContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:$.sources.API;return zt.call(this,()=>{t=new V(t);const s=this.getLength(),r=this.editor.deleteText(0,s),i=this.editor.insertContents(0,t),o=this.editor.deleteText(this.getLength()-1,1);return r.compose(i).compose(o)},e)}setSelection(t,e,s){t==null?this.selection.setRange(null,e||Vt.sources.API):([t,e,,s]=he(t,e,s),this.selection.setRange(new _e(Math.max(0,t),e),s),s!==$.sources.SILENT&&this.scrollSelectionIntoView())}setText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:$.sources.API;const s=new V().insert(t);return this.setContents(s,e)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:$.sources.USER;const e=this.scroll.update(t);return this.selection.update(t),e}updateContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:$.sources.API;return zt.call(this,()=>(t=new V(t),this.editor.applyDelta(t)),e,!0)}};j(Vt,"DEFAULTS",{bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:"",readOnly:!1,registry:pr,theme:"default"}),j(Vt,"events",$.events),j(Vt,"sources",$.sources),j(Vt,"version","2.0.3"),j(Vt,"imports",{delta:V,parchment:kc,"core/module":Qt,"core/theme":as});let T=Vt;function Ri(n){return typeof n=="string"?document.querySelector(n):n}function Cn(n){return Object.entries(n??{}).reduce((t,e)=>{let[s,r]=e;return{...t,[s]:r===!0?{}:r}},{})}function ji(n){return Object.fromEntries(Object.entries(n).filter(t=>t[1]!==void 0))}function Qc(n,t){const e=Ri(n);if(!e)throw new Error("Invalid Quill container");const r=!t.theme||t.theme===T.DEFAULTS.theme?as:T.import(`themes/${t.theme}`);if(!r)throw new Error(`Invalid theme ${t.theme}. Did you register it?`);const{modules:i,...o}=T.DEFAULTS,{modules:a,...u}=r.DEFAULTS;let h=Cn(t.modules);h!=null&&h.toolbar&&h.toolbar.constructor!==Object&&(h={...h,toolbar:{container:h.toolbar}});const b=Ee({},Cn(i),Cn(a),h),x={...o,...ji(u),...ji(t)};let d=t.registry;return d?t.formats&&ns.warn('Ignoring "formats" option because "registry" is specified'):d=t.formats?Yc(t.formats,x.registry,ns):x.registry,{...x,registry:d,container:e,theme:r,modules:Object.entries(b).reduce((f,v)=>{let[E,C]=v;if(!C)return f;const S=T.import(`modules/${E}`);return S==null?(ns.error(`Cannot load ${E} module. Are you sure you registered it?`),f):{...f,[E]:Ee({},S.DEFAULTS||{},C)}},{}),bounds:Ri(x.bounds)}}function zt(n,t,e,s){if(!this.isEnabled()&&t===$.sources.USER&&!this.allowReadOnlyEdits)return new V;let r=e==null?null:this.getSelection();const i=this.editor.delta,o=n();if(r!=null&&(e===!0&&(e=r.index),s==null?r=Mi(r,o,t):s!==0&&(r=Mi(r,e,s,t)),this.setSelection(r,$.sources.SILENT)),o.length()>0){const a=[$.events.TEXT_CHANGE,o,i,t];this.emitter.emit($.events.EDITOR_CHANGE,...a),t!==$.sources.SILENT&&this.emitter.emit(...a)}return o}function he(n,t,e,s,r){let i={};return typeof n.index=="number"&&typeof n.length=="number"?typeof t!="number"?(r=s,s=e,e=t,t=n.length,n=n.index):(t=n.length,n=n.index):typeof t!="number"&&(r=s,s=e,e=t,t=0),typeof e=="object"?(i=e,r=s):typeof e=="string"&&(s!=null?i[e]=s:r=e),r=r||$.sources.API,[n,t,i,r]}function Mi(n,t,e,s){const r=typeof e=="number"?e:0;if(n==null)return null;let i,o;return t&&typeof t.transformPosition=="function"?[i,o]=[n.index,n.index+n.length].map(a=>t.transformPosition(a,s!==$.sources.USER)):[i,o]=[n.index,n.index+n.length].map(a=>a<t||a===t&&s===$.sources.USER?a:r>=0?a+r:Math.max(t,a+r)),new _e(i,o-i)}class Oe extends Cr{}function Bi(n){return n instanceof Et||n instanceof Bt}function Di(n){return typeof n.updateContent=="function"}class Qe extends Qn{constructor(t,e,s){let{emitter:r}=s;super(t,e),this.emitter=r,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener("dragstart",i=>this.handleDragStart(i))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;const t=this.batch;this.batch=!1,this.update(t)}emitMount(t){this.emitter.emit($.events.SCROLL_BLOT_MOUNT,t)}emitUnmount(t){this.emitter.emit($.events.SCROLL_BLOT_UNMOUNT,t)}emitEmbedUpdate(t,e){this.emitter.emit($.events.SCROLL_EMBED_UPDATE,t,e)}deleteAt(t,e){const[s,r]=this.line(t),[i]=this.line(t+e);if(super.deleteAt(t,e),i!=null&&s!==i&&r>0){if(s instanceof Bt||i instanceof Bt){this.optimize();return}const o=i.children.head instanceof Yt?null:i.children.head;s.moveChildren(i,o),s.remove()}this.optimize()}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",t?"true":"false")}formatAt(t,e,s,r){super.formatAt(t,e,s,r),this.optimize()}insertAt(t,e,s){if(t>=this.length())if(s==null||this.scroll.query(e,G.BLOCK)==null){const r=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(r),s==null&&e.endsWith(`
`)?r.insertAt(0,e.slice(0,-1),s):r.insertAt(0,e,s)}else{const r=this.scroll.create(e,s);this.appendChild(r)}else super.insertAt(t,e,s);this.optimize()}insertBefore(t,e){if(t.statics.scope===G.INLINE_BLOT){const s=this.scroll.create(this.statics.defaultChild.blotName);s.appendChild(t),super.insertBefore(s,e)}else super.insertBefore(t,e)}insertContents(t,e){const s=this.deltaToRenderBlocks(e.concat(new V().insert(`
`))),r=s.pop();if(r==null)return;this.batchStart();const i=s.shift();if(i){const u=i.type==="block"&&(i.delta.length()===0||!this.descendant(Bt,t)[0]&&t<this.length()),h=i.type==="block"?i.delta:new V().insert({[i.key]:i.value});Tn(this,t,h);const b=i.type==="block"?1:0,x=t+h.length()+b;u&&this.insertAt(x-1,`
`);const d=jt(this.line(t)[0]),f=Dt.AttributeMap.diff(d,i.attributes)||{};Object.keys(f).forEach(v=>{this.formatAt(x-1,1,v,f[v])}),t=x}let[o,a]=this.children.find(t);if(s.length&&(o&&(o=o.split(a),a=0),s.forEach(u=>{if(u.type==="block"){const h=this.createBlock(u.attributes,o||void 0);Tn(h,0,u.delta)}else{const h=this.create(u.key,u.value);this.insertBefore(h,o||void 0),Object.keys(u.attributes).forEach(b=>{h.format(b,u.attributes[b])})}})),r.type==="block"&&r.delta.length()){const u=o?o.offset(o.scroll)+a:this.length();Tn(this,u,r.delta)}this.batchEnd(),this.optimize()}isEnabled(){return this.domNode.getAttribute("contenteditable")==="true"}leaf(t){const e=this.path(t).pop();if(!e)return[null,-1];const[s,r]=e;return s instanceof Tt?[s,r]:[null,-1]}line(t){return t===this.length()?this.line(t-1):this.descendant(Bi,t)}lines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;const s=(r,i,o)=>{let a=[],u=o;return r.children.forEachAt(i,o,(h,b,x)=>{Bi(h)?a.push(h):h instanceof Cr&&(a=a.concat(s(h,b,u))),u-=x}),a};return s(this,t,e)}optimize(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch||(super.optimize(t,e),t.length>0&&this.emitter.emit($.events.SCROLL_OPTIMIZE,t,e))}path(t){return super.path(t).slice(1)}remove(){}update(t){if(this.batch){Array.isArray(t)&&(this.batch=this.batch.concat(t));return}let e=$.sources.USER;typeof t=="string"&&(e=t),Array.isArray(t)||(t=this.observer.takeRecords()),t=t.filter(s=>{let{target:r}=s;const i=this.find(r,!0);return i&&!Di(i)}),t.length>0&&this.emitter.emit($.events.SCROLL_BEFORE_UPDATE,e,t),super.update(t.concat([])),t.length>0&&this.emitter.emit($.events.SCROLL_UPDATE,e,t)}updateEmbedAt(t,e,s){const[r]=this.descendant(i=>i instanceof Bt,t);r&&r.statics.blotName===e&&Di(r)&&r.updateContent(s)}handleDragStart(t){t.preventDefault()}deltaToRenderBlocks(t){const e=[];let s=new V;return t.forEach(r=>{const i=r==null?void 0:r.insert;if(i)if(typeof i=="string"){const o=i.split(`
`);o.slice(0,-1).forEach(u=>{s.insert(u,r.attributes),e.push({type:"block",delta:s,attributes:r.attributes??{}}),s=new V});const a=o[o.length-1];a&&s.insert(a,r.attributes)}else{const o=Object.keys(i)[0];if(!o)return;this.query(o,G.INLINE)?s.push(r):(s.length()&&e.push({type:"block",delta:s,attributes:{}}),s=new V,e.push({type:"blockEmbed",key:o,value:i[o],attributes:r.attributes??{}}))}}),s.length()&&e.push({type:"block",delta:s,attributes:{}}),e}createBlock(t,e){let s;const r={};Object.entries(t).forEach(a=>{let[u,h]=a;this.query(u,G.BLOCK&G.BLOT)!=null?s=u:r[u]=h});const i=this.create(s||this.statics.defaultChild.blotName,s?t[s]:void 0);this.insertBefore(i,e||void 0);const o=i.length();return Object.entries(r).forEach(a=>{let[u,h]=a;i.formatAt(0,o,u,h)}),i}}j(Qe,"blotName","scroll"),j(Qe,"className","ql-editor"),j(Qe,"tagName","DIV"),j(Qe,"defaultChild",Et),j(Qe,"allowedChildren",[Et,Bt,Oe]);function Tn(n,t,e){e.reduce((s,r)=>{const i=Dt.Op.length(r);let o=r.attributes||{};if(r.insert!=null){if(typeof r.insert=="string"){const a=r.insert;n.insertAt(s,a);const[u]=n.descendant(Tt,s),h=jt(u);o=Dt.AttributeMap.diff(h,o)||{}}else if(typeof r.insert=="object"){const a=Object.keys(r.insert)[0];if(a==null)return s;if(n.insertAt(s,a,r.insert[a]),n.scroll.query(a,G.INLINE)!=null){const[h]=n.descendant(Tt,s),b=jt(h);o=Dt.AttributeMap.diff(b,o)||{}}}}return Object.keys(o).forEach(a=>{n.formatAt(s,i,a,o[a])}),s+i},t)}const ti={scope:G.BLOCK,whitelist:["right","center","justify"]},Jc=new ie("align","align",ti),Al=new Xt("align","ql-align",ti),Cl=new we("align","text-align",ti);class Tl extends we{value(t){let e=super.value(t);return e.startsWith("rgb(")?(e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),`#${e.split(",").map(r=>`00${parseInt(r,10).toString(16)}`.slice(-2)).join("")}`):e}}const tu=new Xt("color","ql-color",{scope:G.INLINE}),ei=new Tl("color","color",{scope:G.INLINE}),eu=new Xt("background","ql-bg",{scope:G.INLINE}),si=new Tl("background","background-color",{scope:G.INLINE});class Ie extends Oe{static create(t){const e=super.create(t);return e.setAttribute("spellcheck","false"),e}code(t,e){return this.children.map(s=>s.length()<=1?"":s.domNode.innerText).join(`
`).slice(t,t+e)}html(t,e){return`<pre>
${Tr(this.code(t,e))}
</pre>`}}class Lt extends Et{static register(){T.register(Ie)}}j(Lt,"TAB","  ");class ri extends le{}ri.blotName="code";ri.tagName="CODE";Lt.blotName="code-block";Lt.className="ql-code-block";Lt.tagName="DIV";Ie.blotName="code-block-container";Ie.className="ql-code-block-container";Ie.tagName="DIV";Ie.allowedChildren=[Lt];Lt.allowedChildren=[Zt,Yt,os];Lt.requiredContainer=Ie;const ni={scope:G.BLOCK,whitelist:["rtl"]},Ll=new ie("direction","dir",ni),Sl=new Xt("direction","ql-direction",ni),ql=new we("direction","direction",ni),kl={scope:G.INLINE,whitelist:["serif","monospace"]},_l=new Xt("font","ql-font",kl);class su extends we{value(t){return super.value(t).replace(/["']/g,"")}}const Ol=new su("font","font-family",kl),Il=new Xt("size","ql-size",{scope:G.INLINE,whitelist:["small","large","huge"]}),Rl=new we("size","font-size",{scope:G.INLINE,whitelist:["10px","18px","32px"]}),ru=pe("quill:keyboard"),nu=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey";class Lr extends Qt{static match(t,e){return["altKey","ctrlKey","metaKey","shiftKey"].some(s=>!!e[s]!==t[s]&&e[s]!==null)?!1:e.key===t.key||e.key===t.which}constructor(t,e){super(t,e),this.bindings={},Object.keys(this.options.bindings).forEach(s=>{this.options.bindings[s]&&this.addBinding(this.options.bindings[s])}),this.addBinding({key:"Enter",shiftKey:null},this.handleEnter),this.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},()=>{}),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:"Backspace"},{collapsed:!0},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:"Backspace"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Delete"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const r=lu(t);if(r==null){ru.warn("Attempted to add invalid keyboard binding",r);return}typeof e=="function"&&(e={handler:e}),typeof s=="function"&&(s={handler:s}),(Array.isArray(r.key)?r.key:[r.key]).forEach(o=>{const a={...r,key:o,...e,...s};this.bindings[a.key]=this.bindings[a.key]||[],this.bindings[a.key].push(a)})}listen(){this.quill.root.addEventListener("keydown",t=>{if(t.defaultPrevented||t.isComposing||t.keyCode===229&&(t.key==="Enter"||t.key==="Backspace"))return;const r=(this.bindings[t.key]||[]).concat(this.bindings[t.which]||[]).filter(S=>Lr.match(t,S));if(r.length===0)return;const i=T.find(t.target,!0);if(i&&i.scroll!==this.quill.scroll)return;const o=this.quill.getSelection();if(o==null||!this.quill.hasFocus())return;const[a,u]=this.quill.getLine(o.index),[h,b]=this.quill.getLeaf(o.index),[x,d]=o.length===0?[h,b]:this.quill.getLeaf(o.index+o.length),f=h instanceof Nr?h.value().slice(0,b):"",v=x instanceof Nr?x.value().slice(d):"",E={collapsed:o.length===0,empty:o.length===0&&a.length()<=1,format:this.quill.getFormat(o),line:a,offset:u,prefix:f,suffix:v,event:t};r.some(S=>{if(S.collapsed!=null&&S.collapsed!==E.collapsed||S.empty!=null&&S.empty!==E.empty||S.offset!=null&&S.offset!==E.offset)return!1;if(Array.isArray(S.format)){if(S.format.every(M=>E.format[M]==null))return!1}else if(typeof S.format=="object"&&!Object.keys(S.format).every(M=>S.format[M]===!0?E.format[M]!=null:S.format[M]===!1?E.format[M]==null:Xn(S.format[M],E.format[M])))return!1;return S.prefix!=null&&!S.prefix.test(E.prefix)||S.suffix!=null&&!S.suffix.test(E.suffix)?!1:S.handler.call(this,o,E,S)!==!0})&&t.preventDefault()})}handleBackspace(t,e){const s=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;if(t.index===0||this.quill.getLength()<=1)return;let r={};const[i]=this.quill.getLine(t.index);let o=new V().retain(t.index-s).delete(s);if(e.offset===0){const[a]=this.quill.getLine(t.index-1);if(a&&!(a.statics.blotName==="block"&&a.length()<=1)){const h=i.formats(),b=this.quill.getFormat(t.index-1,1);if(r=Dt.AttributeMap.diff(h,b)||{},Object.keys(r).length>0){const x=new V().retain(t.index+i.length()-2).retain(1,r);o=o.compose(x)}}}this.quill.updateContents(o,T.sources.USER),this.quill.focus()}handleDelete(t,e){const s=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(t.index>=this.quill.getLength()-s)return;let r={};const[i]=this.quill.getLine(t.index);let o=new V().retain(t.index).delete(s);if(e.offset>=i.length()-1){const[a]=this.quill.getLine(t.index+1);if(a){const u=i.formats(),h=this.quill.getFormat(t.index,1);r=Dt.AttributeMap.diff(u,h)||{},Object.keys(r).length>0&&(o=o.retain(a.length()-1).retain(1,r))}}this.quill.updateContents(o,T.sources.USER),this.quill.focus()}handleDeleteRange(t){ii({range:t,quill:this.quill}),this.quill.focus()}handleEnter(t,e){const s=Object.keys(e.format).reduce((i,o)=>(this.quill.scroll.query(o,G.BLOCK)&&!Array.isArray(e.format[o])&&(i[o]=e.format[o]),i),{}),r=new V().retain(t.index).delete(t.length).insert(`
`,s);this.quill.updateContents(r,T.sources.USER),this.quill.setSelection(t.index+1,T.sources.SILENT),this.quill.focus()}}const iu={bindings:{bold:Ln("bold"),italic:Ln("italic"),underline:Ln("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler(n,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","+1",T.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler(n,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","-1",T.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler(n,t){t.format.indent!=null?this.quill.format("indent","-1",T.sources.USER):t.format.list!=null&&this.quill.format("list",!1,T.sources.USER)}},"indent code-block":Pi(!0),"outdent code-block":Pi(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler(n){this.quill.deleteText(n.index-1,1,T.sources.USER)}},tab:{key:"Tab",handler(n,t){if(t.format.table)return!0;this.quill.history.cutoff();const e=new V().retain(n.index).delete(n.length).insert("	");return this.quill.updateContents(e,T.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(n.index+1,T.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler(){this.quill.format("blockquote",!1,T.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler(n,t){const e={list:!1};t.format.indent&&(e.indent=!1),this.quill.formatLine(n.index,n.length,e,T.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler(n){const[t,e]=this.quill.getLine(n.index),s={...t.formats(),list:"checked"},r=new V().retain(n.index).insert(`
`,s).retain(t.length()-e-1).retain(1,{list:"unchecked"});this.quill.updateContents(r,T.sources.USER),this.quill.setSelection(n.index+1,T.sources.SILENT),this.quill.scrollSelectionIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler(n,t){const[e,s]=this.quill.getLine(n.index),r=new V().retain(n.index).insert(`
`,t.format).retain(e.length()-s-1).retain(1,{header:null});this.quill.updateContents(r,T.sources.USER),this.quill.setSelection(n.index+1,T.sources.SILENT),this.quill.scrollSelectionIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler(n){const t=this.quill.getModule("table");if(t){const[e,s,r,i]=t.getTable(n),o=ou(e,s,r,i);if(o==null)return;let a=e.offset();if(o<0){const u=new V().retain(a).insert(`
`);this.quill.updateContents(u,T.sources.USER),this.quill.setSelection(n.index+1,n.length,T.sources.SILENT)}else if(o>0){a+=e.length();const u=new V().retain(a).insert(`
`);this.quill.updateContents(u,T.sources.USER),this.quill.setSelection(a,T.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler(n,t){const{event:e,line:s}=t,r=s.offset(this.quill.scroll);e.shiftKey?this.quill.setSelection(r-1,T.sources.USER):this.quill.setSelection(r+s.length(),T.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler(n,t){if(this.quill.scroll.query("list")==null)return!0;const{length:e}=t.prefix,[s,r]=this.quill.getLine(n.index);if(r>e)return!0;let i;switch(t.prefix.trim()){case"[]":case"[ ]":i="unchecked";break;case"[x]":i="checked";break;case"-":case"*":i="bullet";break;default:i="ordered"}this.quill.insertText(n.index," ",T.sources.USER),this.quill.history.cutoff();const o=new V().retain(n.index-r).delete(e+1).retain(s.length()-2-r).retain(1,{list:i});return this.quill.updateContents(o,T.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(n.index-e,T.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler(n){const[t,e]=this.quill.getLine(n.index);let s=2,r=t;for(;r!=null&&r.length()<=1&&r.formats()["code-block"];)if(r=r.prev,s-=1,s<=0){const i=new V().retain(n.index+t.length()-e-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(i,T.sources.USER),this.quill.setSelection(n.index-1,T.sources.SILENT),!1}return!0}},"embed left":gr("ArrowLeft",!1),"embed left shift":gr("ArrowLeft",!0),"embed right":gr("ArrowRight",!1),"embed right shift":gr("ArrowRight",!0),"table down":Ui(!1),"table up":Ui(!0)}};Lr.DEFAULTS=iu;function Pi(n){return{key:"Tab",shiftKey:!n,format:{"code-block":!0},handler(t,e){let{event:s}=e;const r=this.quill.scroll.query("code-block"),{TAB:i}=r;if(t.length===0&&!s.shiftKey){this.quill.insertText(t.index,i,T.sources.USER),this.quill.setSelection(t.index+i.length,T.sources.SILENT);return}const o=t.length===0?this.quill.getLines(t.index,1):this.quill.getLines(t);let{index:a,length:u}=t;o.forEach((h,b)=>{n?(h.insertAt(0,i),b===0?a+=i.length:u+=i.length):h.domNode.textContent.startsWith(i)&&(h.deleteAt(0,i.length),b===0?a-=i.length:u-=i.length)}),this.quill.update(T.sources.USER),this.quill.setSelection(a,u,T.sources.SILENT)}}}function gr(n,t){return{key:n,shiftKey:t,altKey:null,[n==="ArrowLeft"?"prefix":"suffix"]:/^$/,handler(s){let{index:r}=s;n==="ArrowRight"&&(r+=s.length+1);const[i]=this.quill.getLeaf(r);return i instanceof Ot?(n==="ArrowLeft"?t?this.quill.setSelection(s.index-1,s.length+1,T.sources.USER):this.quill.setSelection(s.index-1,T.sources.USER):t?this.quill.setSelection(s.index,s.length+1,T.sources.USER):this.quill.setSelection(s.index+s.length+1,T.sources.USER),!1):!0}}}function Ln(n){return{key:n[0],shortKey:!0,handler(t,e){this.quill.format(n,!e.format[n],T.sources.USER)}}}function Ui(n){return{key:n?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler(t,e){const s=n?"prev":"next",r=e.line,i=r.parent[s];if(i!=null){if(i.statics.blotName==="table-row"){let o=i.children.head,a=r;for(;a.prev!=null;)a=a.prev,o=o.next;const u=o.offset(this.quill.scroll)+Math.min(e.offset,o.length()-1);this.quill.setSelection(u,0,T.sources.USER)}}else{const o=r.table()[s];o!=null&&(n?this.quill.setSelection(o.offset(this.quill.scroll)+o.length()-1,0,T.sources.USER):this.quill.setSelection(o.offset(this.quill.scroll),0,T.sources.USER))}return!1}}}function lu(n){if(typeof n=="string"||typeof n=="number")n={key:n};else if(typeof n=="object")n=ss(n);else return null;return n.shortKey&&(n[nu]=n.shortKey,delete n.shortKey),n}function ii(n){let{quill:t,range:e}=n;const s=t.getLines(e);let r={};if(s.length>1){const i=s[0].formats(),o=s[s.length-1].formats();r=Dt.AttributeMap.diff(o,i)||{}}t.deleteText(e,T.sources.USER),Object.keys(r).length>0&&t.formatLine(e.index,1,r,T.sources.USER),t.setSelection(e.index,T.sources.SILENT)}function ou(n,t,e,s){return t.prev==null&&t.next==null?e.prev==null&&e.next==null?s===0?-1:1:e.prev==null?-1:1:t.prev==null?-1:t.next==null?1:null}const au=/font-weight:\s*normal/,cu=["P","OL","UL"],$i=n=>n&&cu.includes(n.tagName),uu=n=>{Array.from(n.querySelectorAll("br")).filter(t=>$i(t.previousElementSibling)&&$i(t.nextElementSibling)).forEach(t=>{var e;(e=t.parentNode)==null||e.removeChild(t)})},hu=n=>{Array.from(n.querySelectorAll('b[style*="font-weight"]')).filter(t=>{var e;return(e=t.getAttribute("style"))==null?void 0:e.match(au)}).forEach(t=>{var s;const e=n.createDocumentFragment();e.append(...t.childNodes),(s=t.parentNode)==null||s.replaceChild(e,t)})};function du(n){n.querySelector('[id^="docs-internal-guid-"]')&&(hu(n),uu(n))}const fu=/\bmso-list:[^;]*ignore/i,pu=/\bmso-list:[^;]*\bl(\d+)/i,gu=/\bmso-list:[^;]*\blevel(\d+)/i,mu=(n,t)=>{const e=n.getAttribute("style"),s=e==null?void 0:e.match(pu);if(!s)return null;const r=Number(s[1]),i=e==null?void 0:e.match(gu),o=i?Number(i[1]):1,a=new RegExp(`@list l${r}:level${o}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`,"i"),u=t.match(a),h=u&&u[1]==="bullet"?"bullet":"ordered";return{id:r,indent:o,type:h,element:n}},bu=n=>{var o,a;const t=Array.from(n.querySelectorAll("[style*=mso-list]")),e=[],s=[];t.forEach(u=>{(u.getAttribute("style")||"").match(fu)?e.push(u):s.push(u)}),e.forEach(u=>{var h;return(h=u.parentNode)==null?void 0:h.removeChild(u)});const r=n.documentElement.innerHTML,i=s.map(u=>mu(u,r)).filter(u=>u);for(;i.length;){const u=[];let h=i.shift();for(;h;)u.push(h),h=i.length&&((o=i[0])==null?void 0:o.element)===h.element.nextElementSibling&&i[0].id===h.id?i.shift():null;const b=document.createElement("ul");u.forEach(f=>{const v=document.createElement("li");v.setAttribute("data-list",f.type),f.indent>1&&v.setAttribute("class",`ql-indent-${f.indent-1}`),v.innerHTML=f.element.innerHTML,b.appendChild(v)});const x=(a=u[0])==null?void 0:a.element,{parentNode:d}=x??{};x&&(d==null||d.replaceChild(b,x)),u.slice(1).forEach(f=>{let{element:v}=f;d==null||d.removeChild(v)})}};function yu(n){n.documentElement.getAttribute("xmlns:w")==="urn:schemas-microsoft-com:office:word"&&bu(n)}const vu=[yu,du],xu=n=>{n.documentElement&&vu.forEach(t=>{t(n)})},Nu=pe("quill:clipboard"),Eu=[[Node.TEXT_NODE,Ru],[Node.TEXT_NODE,Hi],["br",Lu],[Node.ELEMENT_NODE,Hi],[Node.ELEMENT_NODE,Tu],[Node.ELEMENT_NODE,Cu],[Node.ELEMENT_NODE,Ou],["li",ku],["ol, ul",_u],["pre",Su],["tr",Iu],["b",Sn("bold")],["i",Sn("italic")],["strike",Sn("strike")],["style",qu]],wu=[Jc,Ll].reduce((n,t)=>(n[t.keyName]=t,n),{}),Fi=[Cl,si,ei,ql,Ol,Rl].reduce((n,t)=>(n[t.keyName]=t,n),{});class jl extends Qt{constructor(t,e){super(t,e),this.quill.root.addEventListener("copy",s=>this.onCaptureCopy(s,!1)),this.quill.root.addEventListener("cut",s=>this.onCaptureCopy(s,!0)),this.quill.root.addEventListener("paste",this.onCapturePaste.bind(this)),this.matchers=[],Eu.concat(this.options.matchers??[]).forEach(s=>{let[r,i]=s;this.addMatcher(r,i)})}addMatcher(t,e){this.matchers.push([t,e])}convert(t){let{html:e,text:s}=t,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(r[Lt.blotName])return new V().insert(s||"",{[Lt.blotName]:r[Lt.blotName]});if(!e)return new V().insert(s||"",r);const i=this.convertHTML(e);return Ds(i,`
`)&&(i.ops[i.ops.length-1].attributes==null||r.table)?i.compose(new V().retain(i.length()-1).delete(1)):i}normalizeHTML(t){xu(t)}convertHTML(t){const e=new DOMParser().parseFromString(t,"text/html");this.normalizeHTML(e);const s=e.body,r=new WeakMap,[i,o]=this.prepareMatching(s,r);return li(this.quill.scroll,s,i,o,r)}dangerouslyPasteHTML(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:T.sources.API;if(typeof t=="string"){const r=this.convert({html:t,text:""});this.quill.setContents(r,e),this.quill.setSelection(0,T.sources.SILENT)}else{const r=this.convert({html:e,text:""});this.quill.updateContents(new V().retain(t).concat(r),s),this.quill.setSelection(t+r.length(),T.sources.SILENT)}}onCaptureCopy(t){var o,a;let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(t.defaultPrevented)return;t.preventDefault();const[s]=this.quill.selection.getRange();if(s==null)return;const{html:r,text:i}=this.onCopy(s,e);(o=t.clipboardData)==null||o.setData("text/plain",i),(a=t.clipboardData)==null||a.setData("text/html",r),e&&ii({range:s,quill:this.quill})}normalizeURIList(t){return t.split(/\r?\n/).filter(e=>e[0]!=="#").join(`
`)}onCapturePaste(t){var o,a,u,h,b;if(t.defaultPrevented||!this.quill.isEnabled())return;t.preventDefault();const e=this.quill.getSelection(!0);if(e==null)return;const s=(o=t.clipboardData)==null?void 0:o.getData("text/html");let r=(a=t.clipboardData)==null?void 0:a.getData("text/plain");if(!s&&!r){const x=(u=t.clipboardData)==null?void 0:u.getData("text/uri-list");x&&(r=this.normalizeURIList(x))}const i=Array.from(((h=t.clipboardData)==null?void 0:h.files)||[]);if(!s&&i.length>0){this.quill.uploader.upload(e,i);return}if(s&&i.length>0){const x=new DOMParser().parseFromString(s,"text/html");if(x.body.childElementCount===1&&((b=x.body.firstElementChild)==null?void 0:b.tagName)==="IMG"){this.quill.uploader.upload(e,i);return}}this.onPaste(e,{html:s,text:r})}onCopy(t){const e=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:e}}onPaste(t,e){let{text:s,html:r}=e;const i=this.quill.getFormat(t.index),o=this.convert({text:s,html:r},i);Nu.log("onPaste",o,{text:s,html:r});const a=new V().retain(t.index).delete(t.length).concat(o);this.quill.updateContents(a,T.sources.USER),this.quill.setSelection(a.length()-t.length,T.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(t,e){const s=[],r=[];return this.matchers.forEach(i=>{const[o,a]=i;switch(o){case Node.TEXT_NODE:r.push(a);break;case Node.ELEMENT_NODE:s.push(a);break;default:Array.from(t.querySelectorAll(o)).forEach(u=>{if(e.has(u)){const h=e.get(u);h==null||h.push(a)}else e.set(u,[a])});break}}),[s,r]}}j(jl,"DEFAULTS",{matchers:[]});function Re(n,t,e,s){return s.query(t)?n.reduce((r,i)=>{if(!i.insert)return r;if(i.attributes&&i.attributes[t])return r.push(i);const o=e?{[t]:e}:{};return r.insert(i.insert,{...o,...i.attributes})},new V):n}function Ds(n,t){let e="";for(let s=n.ops.length-1;s>=0&&e.length<t.length;--s){const r=n.ops[s];if(typeof r.insert!="string")break;e=r.insert+e}return e.slice(-1*t.length)===t}function xe(n,t){if(!(n instanceof Element))return!1;const e=t.query(n);return e&&e.prototype instanceof Ot?!1:["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(n.tagName.toLowerCase())}function Au(n,t){return n.previousElementSibling&&n.nextElementSibling&&!xe(n.previousElementSibling,t)&&!xe(n.nextElementSibling,t)}const mr=new WeakMap;function Ml(n){return n==null?!1:(mr.has(n)||(n.tagName==="PRE"?mr.set(n,!0):mr.set(n,Ml(n.parentNode))),mr.get(n))}function li(n,t,e,s,r){return t.nodeType===t.TEXT_NODE?s.reduce((i,o)=>o(t,i,n),new V):t.nodeType===t.ELEMENT_NODE?Array.from(t.childNodes||[]).reduce((i,o)=>{let a=li(n,o,e,s,r);return o.nodeType===t.ELEMENT_NODE&&(a=e.reduce((u,h)=>h(o,u,n),a),a=(r.get(o)||[]).reduce((u,h)=>h(o,u,n),a)),i.concat(a)},new V):new V}function Sn(n){return(t,e,s)=>Re(e,n,!0,s)}function Cu(n,t,e){const s=ie.keys(n),r=Xt.keys(n),i=we.keys(n),o={};return s.concat(r).concat(i).forEach(a=>{let u=e.query(a,G.ATTRIBUTE);u!=null&&(o[u.attrName]=u.value(n),o[u.attrName])||(u=wu[a],u!=null&&(u.attrName===a||u.keyName===a)&&(o[u.attrName]=u.value(n)||void 0),u=Fi[a],u!=null&&(u.attrName===a||u.keyName===a)&&(u=Fi[a],o[u.attrName]=u.value(n)||void 0))}),Object.entries(o).reduce((a,u)=>{let[h,b]=u;return Re(a,h,b,e)},t)}function Tu(n,t,e){const s=e.query(n);if(s==null)return t;if(s.prototype instanceof Ot){const r={},i=s.value(n);if(i!=null)return r[s.blotName]=i,new V().insert(r,s.formats(n,e))}else if(s.prototype instanceof Rs&&!Ds(t,`
`)&&t.insert(`
`),"blotName"in s&&"formats"in s&&typeof s.formats=="function")return Re(t,s.blotName,s.formats(n,e),e);return t}function Lu(n,t){return Ds(t,`
`)||t.insert(`
`),t}function Su(n,t,e){const s=e.query("code-block"),r=s&&"formats"in s&&typeof s.formats=="function"?s.formats(n,e):!0;return Re(t,"code-block",r,e)}function qu(){return new V}function ku(n,t,e){const s=e.query(n);if(s==null||s.blotName!=="list"||!Ds(t,`
`))return t;let r=-1,i=n.parentNode;for(;i!=null;)["OL","UL"].includes(i.tagName)&&(r+=1),i=i.parentNode;return r<=0?t:t.reduce((o,a)=>a.insert?a.attributes&&typeof a.attributes.indent=="number"?o.push(a):o.insert(a.insert,{indent:r,...a.attributes||{}}):o,new V)}function _u(n,t,e){const s=n;let r=s.tagName==="OL"?"ordered":"bullet";const i=s.getAttribute("data-checked");return i&&(r=i==="true"?"checked":"unchecked"),Re(t,"list",r,e)}function Hi(n,t,e){if(!Ds(t,`
`)){if(xe(n,e)&&(n.childNodes.length>0||n instanceof HTMLParagraphElement))return t.insert(`
`);if(t.length()>0&&n.nextSibling){let s=n.nextSibling;for(;s!=null;){if(xe(s,e))return t.insert(`
`);const r=e.query(s);if(r&&r.prototype instanceof Bt)return t.insert(`
`);s=s.firstChild}}}return t}function Ou(n,t,e){var i;const s={},r=n.style||{};return r.fontStyle==="italic"&&(s.italic=!0),r.textDecoration==="underline"&&(s.underline=!0),r.textDecoration==="line-through"&&(s.strike=!0),((i=r.fontWeight)!=null&&i.startsWith("bold")||parseInt(r.fontWeight,10)>=700)&&(s.bold=!0),t=Object.entries(s).reduce((o,a)=>{let[u,h]=a;return Re(o,u,h,e)},t),parseFloat(r.textIndent||0)>0?new V().insert("	").concat(t):t}function Iu(n,t,e){var r,i;const s=((r=n.parentElement)==null?void 0:r.tagName)==="TABLE"?n.parentElement:(i=n.parentElement)==null?void 0:i.parentElement;if(s!=null){const a=Array.from(s.querySelectorAll("tr")).indexOf(n)+1;return Re(t,"table",a,e)}return t}function Ru(n,t,e){var r;let s=n.data;if(((r=n.parentElement)==null?void 0:r.tagName)==="O:P")return t.insert(s.trim());if(!Ml(n)){if(s.trim().length===0&&s.includes(`
`)&&!Au(n,e))return t;s=s.replace(/[^\S\u00a0]/g," "),s=s.replace(/ {2,}/g," "),(n.previousSibling==null&&n.parentElement!=null&&xe(n.parentElement,e)||n.previousSibling instanceof Element&&xe(n.previousSibling,e))&&(s=s.replace(/^ /,"")),(n.nextSibling==null&&n.parentElement!=null&&xe(n.parentElement,e)||n.nextSibling instanceof Element&&xe(n.nextSibling,e))&&(s=s.replace(/ $/,"")),s=s.replaceAll(" "," ")}return t.insert(s)}class Bl extends Qt{constructor(e,s){super(e,s);j(this,"lastRecorded",0);j(this,"ignoreChange",!1);j(this,"stack",{undo:[],redo:[]});j(this,"currentRange",null);this.quill.on(T.events.EDITOR_CHANGE,(r,i,o,a)=>{r===T.events.SELECTION_CHANGE?i&&a!==T.sources.SILENT&&(this.currentRange=i):r===T.events.TEXT_CHANGE&&(this.ignoreChange||(!this.options.userOnly||a===T.sources.USER?this.record(i,o):this.transform(i)),this.currentRange=Mn(this.currentRange,i))}),this.quill.keyboard.addBinding({key:"z",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:["z","Z"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:"y",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener("beforeinput",r=>{r.inputType==="historyUndo"?(this.undo(),r.preventDefault()):r.inputType==="historyRedo"&&(this.redo(),r.preventDefault())})}change(e,s){if(this.stack[e].length===0)return;const r=this.stack[e].pop();if(!r)return;const i=this.quill.getContents(),o=r.delta.invert(i);this.stack[s].push({delta:o,range:Mn(r.range,o)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(r.delta,T.sources.USER),this.ignoreChange=!1,this.restoreSelection(r)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(e,s){if(e.ops.length===0)return;this.stack.redo=[];let r=e.invert(s),i=this.currentRange;const o=Date.now();if(this.lastRecorded+this.options.delay>o&&this.stack.undo.length>0){const a=this.stack.undo.pop();a&&(r=r.compose(a.delta),i=a.range)}else this.lastRecorded=o;r.length()!==0&&(this.stack.undo.push({delta:r,range:i}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change("redo","undo")}transform(e){zi(this.stack.undo,e),zi(this.stack.redo,e)}undo(){this.change("undo","redo")}restoreSelection(e){if(e.range)this.quill.setSelection(e.range,T.sources.USER);else{const s=Mu(this.quill.scroll,e.delta);this.quill.setSelection(s,T.sources.USER)}}}j(Bl,"DEFAULTS",{delay:1e3,maxStack:100,userOnly:!1});function zi(n,t){let e=t;for(let s=n.length-1;s>=0;s-=1){const r=n[s];n[s]={delta:e.transform(r.delta,!0),range:r.range&&Mn(r.range,e)},e=r.delta.transform(e),n[s].delta.length()===0&&n.splice(s,1)}}function ju(n,t){const e=t.ops[t.ops.length-1];return e==null?!1:e.insert!=null?typeof e.insert=="string"&&e.insert.endsWith(`
`):e.attributes!=null?Object.keys(e.attributes).some(s=>n.query(s,G.BLOCK)!=null):!1}function Mu(n,t){const e=t.reduce((r,i)=>r+(i.delete||0),0);let s=t.length()-e;return ju(n,t)&&(s-=1),s}function Mn(n,t){if(!n)return n;const e=t.transformPosition(n.index),s=t.transformPosition(n.index+n.length);return{index:e,length:s-e}}class Dl extends Qt{constructor(t,e){super(t,e),t.root.addEventListener("drop",s=>{var o;s.preventDefault();let r=null;if(document.caretRangeFromPoint)r=document.caretRangeFromPoint(s.clientX,s.clientY);else if(document.caretPositionFromPoint){const a=document.caretPositionFromPoint(s.clientX,s.clientY);r=document.createRange(),r.setStart(a.offsetNode,a.offset),r.setEnd(a.offsetNode,a.offset)}const i=r&&t.selection.normalizeNative(r);if(i){const a=t.selection.normalizedToRange(i);(o=s.dataTransfer)!=null&&o.files&&this.upload(a,s.dataTransfer.files)}})}upload(t,e){const s=[];Array.from(e).forEach(r=>{var i;r&&((i=this.options.mimetypes)!=null&&i.includes(r.type))&&s.push(r)}),s.length>0&&this.options.handler.call(this,t,s)}}Dl.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler(n,t){if(!this.quill.scroll.query("image"))return;const e=t.map(s=>new Promise(r=>{const i=new FileReader;i.onload=()=>{r(i.result)},i.readAsDataURL(s)}));Promise.all(e).then(s=>{const r=s.reduce((i,o)=>i.insert({image:o}),new V().retain(n.index).delete(n.length));this.quill.updateContents(r,$.sources.USER),this.quill.setSelection(n.index+s.length,$.sources.SILENT)})}};const Bu=["insertText","insertReplacementText"];class Du extends Qt{constructor(t,e){super(t,e),t.root.addEventListener("beforeinput",s=>{this.handleBeforeInput(s)}),/Android/i.test(navigator.userAgent)||t.on(T.events.COMPOSITION_BEFORE_START,()=>{this.handleCompositionStart()})}deleteRange(t){ii({range:t,quill:this.quill})}replaceText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if(t.length===0)return!1;if(e){const s=this.quill.getFormat(t.index,1);this.deleteRange(t),this.quill.updateContents(new V().retain(t.index).insert(e,s),T.sources.USER)}else this.deleteRange(t);return this.quill.setSelection(t.index+e.length,0,T.sources.SILENT),!0}handleBeforeInput(t){if(this.quill.composition.isComposing||t.defaultPrevented||!Bu.includes(t.inputType))return;const e=t.getTargetRanges?t.getTargetRanges()[0]:null;if(!e||e.collapsed===!0)return;const s=Pu(t);if(s==null)return;const r=this.quill.selection.normalizeNative(e),i=r?this.quill.selection.normalizedToRange(r):null;i&&this.replaceText(i,s)&&t.preventDefault()}handleCompositionStart(){const t=this.quill.getSelection();t&&this.replaceText(t)}}function Pu(n){var t;return typeof n.data=="string"?n.data:(t=n.dataTransfer)!=null&&t.types.includes("text/plain")?n.dataTransfer.getData("text/plain"):null}const Uu=/Mac/i.test(navigator.platform),$u=100,Fu=n=>!!(n.key==="ArrowLeft"||n.key==="ArrowRight"||n.key==="ArrowUp"||n.key==="ArrowDown"||n.key==="Home"||Uu&&n.key==="a"&&n.ctrlKey===!0);class Hu extends Qt{constructor(e,s){super(e,s);j(this,"isListening",!1);j(this,"selectionChangeDeadline",0);this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:["ArrowLeft","ArrowRight"],offset:0,shiftKey:null,handler(e,s){let{line:r,event:i}=s;if(!(r instanceof Wt)||!r.uiNode)return!0;const o=getComputedStyle(r.domNode).direction==="rtl";return o&&i.key!=="ArrowRight"||!o&&i.key!=="ArrowLeft"?!0:(this.quill.setSelection(e.index-1,e.length+(i.shiftKey?1:0),T.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener("keydown",e=>{!e.defaultPrevented&&Fu(e)&&this.ensureListeningToSelectionChange()})}ensureListeningToSelectionChange(){if(this.selectionChangeDeadline=Date.now()+$u,this.isListening)return;this.isListening=!0;const e=()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()};document.addEventListener("selectionchange",e,{once:!0})}handleSelectionChange(){const e=document.getSelection();if(!e)return;const s=e.getRangeAt(0);if(s.collapsed!==!0||s.startOffset!==0)return;const r=this.quill.scroll.find(s.startContainer);if(!(r instanceof Wt)||!r.uiNode)return;const i=document.createRange();i.setStartAfter(r.uiNode),i.setEndAfter(r.uiNode),e.removeAllRanges(),e.addRange(i)}}T.register({"blots/block":Et,"blots/block/embed":Bt,"blots/break":Yt,"blots/container":Oe,"blots/cursor":os,"blots/embed":Jn,"blots/inline":le,"blots/scroll":Qe,"blots/text":Zt,"modules/clipboard":jl,"modules/history":Bl,"modules/keyboard":Lr,"modules/uploader":Dl,"modules/input":Du,"modules/uiNode":Hu});class zu extends Xt{add(t,e){let s=0;if(e==="+1"||e==="-1"){const r=this.value(t)||0;s=e==="+1"?r+1:r-1}else typeof e=="number"&&(s=e);return s===0?(this.remove(t),!0):super.add(t,s.toString())}canAdd(t,e){return super.canAdd(t,e)||super.canAdd(t,parseInt(e,10))}value(t){return parseInt(super.value(t),10)||void 0}}const Vu=new zu("indent","ql-indent",{scope:G.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});class Bn extends Et{}j(Bn,"blotName","blockquote"),j(Bn,"tagName","blockquote");class Dn extends Et{static formats(t){return this.tagName.indexOf(t.tagName)+1}}j(Dn,"blotName","header"),j(Dn,"tagName",["H1","H2","H3","H4","H5","H6"]);class Ps extends Oe{}Ps.blotName="list-container";Ps.tagName="OL";class Us extends Et{static create(t){const e=super.create();return e.setAttribute("data-list",t),e}static formats(t){return t.getAttribute("data-list")||void 0}static register(){T.register(Ps)}constructor(t,e){super(t,e);const s=e.ownerDocument.createElement("span"),r=i=>{if(!t.isEnabled())return;const o=this.statics.formats(e,t);o==="checked"?(this.format("list","unchecked"),i.preventDefault()):o==="unchecked"&&(this.format("list","checked"),i.preventDefault())};s.addEventListener("mousedown",r),s.addEventListener("touchstart",r),this.attachUI(s)}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-list",e):super.format(t,e)}}Us.blotName="list";Us.tagName="LI";Ps.allowedChildren=[Us];Us.requiredContainer=Ps;class Ms extends le{static create(){return super.create()}static formats(){return!0}optimize(t){super.optimize(t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}j(Ms,"blotName","bold"),j(Ms,"tagName",["STRONG","B"]);class Pn extends Ms{}j(Pn,"blotName","italic"),j(Pn,"tagName",["EM","I"]);class Ne extends le{static create(t){const e=super.create(t);return e.setAttribute("href",this.sanitize(t)),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e}static formats(t){return t.getAttribute("href")}static sanitize(t){return Pl(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}format(t,e){t!==this.statics.blotName||!e?super.format(t,e):this.domNode.setAttribute("href",this.constructor.sanitize(e))}}j(Ne,"blotName","link"),j(Ne,"tagName","A"),j(Ne,"SANITIZED_URL","about:blank"),j(Ne,"PROTOCOL_WHITELIST",["http","https","mailto","tel","sms"]);function Pl(n,t){const e=document.createElement("a");e.href=n;const s=e.href.slice(0,e.href.indexOf(":"));return t.indexOf(s)>-1}class Un extends le{static create(t){return t==="super"?document.createElement("sup"):t==="sub"?document.createElement("sub"):super.create(t)}static formats(t){if(t.tagName==="SUB")return"sub";if(t.tagName==="SUP")return"super"}}j(Un,"blotName","script"),j(Un,"tagName",["SUB","SUP"]);class $n extends Ms{}j($n,"blotName","strike"),j($n,"tagName",["S","STRIKE"]);class Fn extends le{}j(Fn,"blotName","underline"),j(Fn,"tagName","U");class yr extends Jn{static create(t){if(window.katex==null)throw new Error("Formula module requires KaTeX.");const e=super.create(t);return typeof t=="string"&&(window.katex.render(t,e,{throwOnError:!1,errorColor:"#f00"}),e.setAttribute("data-value",t)),e}static value(t){return t.getAttribute("data-value")}html(){const{formula:t}=this.value();return`<span>${t}</span>`}}j(yr,"blotName","formula"),j(yr,"className","ql-formula"),j(yr,"tagName","SPAN");const Vi=["alt","height","width"];class Hn extends Ot{static create(t){const e=super.create(t);return typeof t=="string"&&e.setAttribute("src",this.sanitize(t)),e}static formats(t){return Vi.reduce((e,s)=>(t.hasAttribute(s)&&(e[s]=t.getAttribute(s)),e),{})}static match(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}static sanitize(t){return Pl(t,["http","https","data"])?t:"//:0"}static value(t){return t.getAttribute("src")}format(t,e){Vi.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}}j(Hn,"blotName","image"),j(Hn,"tagName","IMG");const Gi=["height","width"];class vr extends Bt{static create(t){const e=super.create(t);return e.setAttribute("frameborder","0"),e.setAttribute("allowfullscreen","true"),e.setAttribute("src",this.sanitize(t)),e}static formats(t){return Gi.reduce((e,s)=>(t.hasAttribute(s)&&(e[s]=t.getAttribute(s)),e),{})}static sanitize(t){return Ne.sanitize(t)}static value(t){return t.getAttribute("src")}format(t,e){Gi.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}html(){const{video:t}=this.value();return`<a href="${t}">${t}</a>`}}j(vr,"blotName","video"),j(vr,"className","ql-video"),j(vr,"tagName","IFRAME");const ks=new Xt("code-token","hljs",{scope:G.INLINE});class de extends le{static formats(t,e){for(;t!=null&&t!==e.domNode;){if(t.classList&&t.classList.contains(Lt.className))return super.formats(t,e);t=t.parentNode}}constructor(t,e,s){super(t,e,s),ks.add(this.domNode,s)}format(t,e){t!==de.blotName?super.format(t,e):e?ks.add(this.domNode,e):(ks.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),ks.value(this.domNode)||this.unwrap()}}de.blotName="code-token";de.className="ql-token";class Mt extends Lt{static create(t){const e=super.create(t);return typeof t=="string"&&e.setAttribute("data-language",t),e}static formats(t){return t.getAttribute("data-language")||"plain"}static register(){}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-language",e):super.format(t,e)}replaceWith(t,e){return this.formatAt(0,this.length(),de.blotName,!1),super.replaceWith(t,e)}}class _s extends Ie{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(t,e){t===Mt.blotName&&(this.forceNext=!0,this.children.forEach(s=>{s.format(t,e)}))}formatAt(t,e,s,r){s===Mt.blotName&&(this.forceNext=!0),super.formatAt(t,e,s,r)}highlight(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.children.head==null)return;const r=`${Array.from(this.domNode.childNodes).filter(o=>o!==this.uiNode).map(o=>o.textContent).join(`
`)}
`,i=Mt.formats(this.children.head.domNode);if(e||this.forceNext||this.cachedText!==r){if(r.trim().length>0||this.cachedText==null){const o=this.children.reduce((u,h)=>u.concat(El(h,!1)),new V),a=t(r,i);o.diff(a).reduce((u,h)=>{let{retain:b,attributes:x}=h;return b?(x&&Object.keys(x).forEach(d=>{[Mt.blotName,de.blotName].includes(d)&&this.formatAt(u,b,d,x[d])}),u+b):u},0)}this.cachedText=r,this.forceNext=!1}}html(t,e){const[s]=this.children.find(t);return`<pre data-language="${s?Mt.formats(s.domNode):"plain"}">
${Tr(this.code(t,e))}
</pre>`}optimize(t){if(super.optimize(t),this.parent!=null&&this.children.head!=null&&this.uiNode!=null){const e=Mt.formats(this.children.head.domNode);e!==this.uiNode.value&&(this.uiNode.value=e)}}}_s.allowedChildren=[Mt];Mt.requiredContainer=_s;Mt.allowedChildren=[de,os,Zt,Yt];const Gu=(n,t,e)=>{if(typeof n.versionString=="string"){const s=n.versionString.split(".")[0];if(parseInt(s,10)>=11)return n.highlight(e,{language:t}).value}return n.highlight(t,e).value};class Ul extends Qt{static register(){T.register(de,!0),T.register(Mt,!0),T.register(_s,!0)}constructor(t,e){if(super(t,e),this.options.hljs==null)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");this.languages=this.options.languages.reduce((s,r)=>{let{key:i}=r;return s[i]=!0,s},{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(T.events.SCROLL_BLOT_MOUNT,t=>{if(!(t instanceof _s))return;const e=this.quill.root.ownerDocument.createElement("select");this.options.languages.forEach(s=>{let{key:r,label:i}=s;const o=e.ownerDocument.createElement("option");o.textContent=i,o.setAttribute("value",r),e.appendChild(o)}),e.addEventListener("change",()=>{t.format(Mt.blotName,e.value),this.quill.root.focus(),this.highlight(t,!0)}),t.uiNode==null&&(t.attachUI(e),t.children.head&&(e.value=Mt.formats(t.children.head.domNode)))})}initTimer(){let t=null;this.quill.on(T.events.SCROLL_OPTIMIZE,()=>{t&&clearTimeout(t),t=setTimeout(()=>{this.highlight(),t=null},this.options.interval)})}highlight(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.quill.selection.composing)return;this.quill.update(T.sources.USER);const s=this.quill.getSelection();(t==null?this.quill.scroll.descendants(_s):[t]).forEach(i=>{i.highlight(this.highlightBlot,e)}),this.quill.update(T.sources.SILENT),s!=null&&this.quill.setSelection(s,T.sources.SILENT)}highlightBlot(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"plain";if(e=this.languages[e]?e:"plain",e==="plain")return Tr(t).split(`
`).reduce((r,i,o)=>(o!==0&&r.insert(`
`,{[Lt.blotName]:e}),r.insert(i)),new V);const s=this.quill.root.ownerDocument.createElement("div");return s.classList.add(Lt.className),s.innerHTML=Gu(this.options.hljs,e,t),li(this.quill.scroll,s,[(r,i)=>{const o=ks.value(r);return o?i.compose(new V().retain(i.length(),{[de.blotName]:o})):i}],[(r,i)=>r.data.split(`
`).reduce((o,a,u)=>(u!==0&&o.insert(`
`,{[Lt.blotName]:e}),o.insert(a)),i)],new WeakMap)}}Ul.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"JavaScript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]};const Is=class Is extends Et{static create(t){const e=super.create();return t?e.setAttribute("data-row",t):e.setAttribute("data-row",oi()),e}static formats(t){if(t.hasAttribute("data-row"))return t.getAttribute("data-row")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(t,e){t===Is.blotName&&e?this.domNode.setAttribute("data-row",e):super.format(t,e)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}};j(Is,"blotName","table"),j(Is,"tagName","TD");let Kt=Is;class fe extends Oe{checkMerge(){if(super.checkMerge()&&this.next.children.head!=null){const t=this.children.head.formats(),e=this.children.tail.formats(),s=this.next.children.head.formats(),r=this.next.children.tail.formats();return t.table===e.table&&t.table===s.table&&t.table===r.table}return!1}optimize(t){super.optimize(t),this.children.forEach(e=>{if(e.next==null)return;const s=e.formats(),r=e.next.formats();if(s.table!==r.table){const i=this.splitAfter(e);i&&i.optimize(),this.prev&&this.prev.optimize()}})}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}}j(fe,"blotName","table-row"),j(fe,"tagName","TR");class ne extends Oe{}j(ne,"blotName","table-body"),j(ne,"tagName","TBODY");class cs extends Oe{balanceCells(){const t=this.descendants(fe),e=t.reduce((s,r)=>Math.max(r.children.length,s),0);t.forEach(s=>{new Array(e-s.children.length).fill(0).forEach(()=>{let r;s.children.head!=null&&(r=Kt.formats(s.children.head.domNode));const i=this.scroll.create(Kt.blotName,r);s.appendChild(i),i.optimize()})})}cells(t){return this.rows().map(e=>e.children.at(t))}deleteColumn(t){const[e]=this.descendant(ne);e==null||e.children.head==null||e.children.forEach(s=>{const r=s.children.at(t);r!=null&&r.remove()})}insertColumn(t){const[e]=this.descendant(ne);e==null||e.children.head==null||e.children.forEach(s=>{const r=s.children.at(t),i=Kt.formats(s.children.head.domNode),o=this.scroll.create(Kt.blotName,i);s.insertBefore(o,r)})}insertRow(t){const[e]=this.descendant(ne);if(e==null||e.children.head==null)return;const s=oi(),r=this.scroll.create(fe.blotName);e.children.head.children.forEach(()=>{const o=this.scroll.create(Kt.blotName,s);r.appendChild(o)});const i=e.children.at(t);e.insertBefore(r,i)}rows(){const t=this.children.head;return t==null?[]:t.children.map(e=>e)}}j(cs,"blotName","table-container"),j(cs,"tagName","TABLE");cs.allowedChildren=[ne];ne.requiredContainer=cs;ne.allowedChildren=[fe];fe.requiredContainer=ne;fe.allowedChildren=[Kt];Kt.requiredContainer=fe;function oi(){return`row-${Math.random().toString(36).slice(2,6)}`}class Ku extends Qt{static register(){T.register(Kt),T.register(fe),T.register(ne),T.register(cs)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(cs).forEach(t=>{t.balanceCells()})}deleteColumn(){const[t,,e]=this.getTable();e!=null&&(t.deleteColumn(e.cellOffset()),this.quill.update(T.sources.USER))}deleteRow(){const[,t]=this.getTable();t!=null&&(t.remove(),this.quill.update(T.sources.USER))}deleteTable(){const[t]=this.getTable();if(t==null)return;const e=t.offset();t.remove(),this.quill.update(T.sources.USER),this.quill.setSelection(e,T.sources.SILENT)}getTable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.quill.getSelection();if(t==null)return[null,null,null,-1];const[e,s]=this.quill.getLine(t.index);if(e==null||e.statics.blotName!==Kt.blotName)return[null,null,null,-1];const r=e.parent;return[r.parent.parent,r,e,s]}insertColumn(t){const e=this.quill.getSelection();if(!e)return;const[s,r,i]=this.getTable(e);if(i==null)return;const o=i.cellOffset();s.insertColumn(o+t),this.quill.update(T.sources.USER);let a=r.rowOffset();t===0&&(a+=1),this.quill.setSelection(e.index+a,e.length,T.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(t){const e=this.quill.getSelection();if(!e)return;const[s,r,i]=this.getTable(e);if(i==null)return;const o=r.rowOffset();s.insertRow(o+t),this.quill.update(T.sources.USER),t>0?this.quill.setSelection(e,T.sources.SILENT):this.quill.setSelection(e.index+r.children.length,e.length,T.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(t,e){const s=this.quill.getSelection();if(s==null)return;const r=new Array(t).fill(0).reduce(i=>{const o=new Array(e).fill(`
`).join("");return i.insert(o,{table:oi()})},new V().retain(s.index));this.quill.updateContents(r,T.sources.USER),this.quill.setSelection(s.index,T.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(T.events.SCROLL_OPTIMIZE,t=>{t.some(e=>["TD","TR","TBODY","TABLE"].includes(e.target.tagName)?(this.quill.once(T.events.TEXT_CHANGE,(s,r,i)=>{i===T.sources.USER&&this.balanceTables()}),!0):!1)})}}const Ki=pe("quill:toolbar");class ai extends Qt{constructor(t,e){var s,r;if(super(t,e),Array.isArray(this.options.container)){const i=document.createElement("div");i.setAttribute("role","toolbar"),Wu(i,this.options.container),(r=(s=t.container)==null?void 0:s.parentNode)==null||r.insertBefore(i,t.container),this.container=i}else typeof this.options.container=="string"?this.container=document.querySelector(this.options.container):this.container=this.options.container;if(!(this.container instanceof HTMLElement)){Ki.error("Container required for toolbar",this.options);return}this.container.classList.add("ql-toolbar"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach(i=>{var a;const o=(a=this.options.handlers)==null?void 0:a[i];o&&this.addHandler(i,o)}),Array.from(this.container.querySelectorAll("button, select")).forEach(i=>{this.attach(i)}),this.quill.on(T.events.EDITOR_CHANGE,()=>{const[i]=this.quill.selection.getRange();this.update(i)})}addHandler(t,e){this.handlers[t]=e}attach(t){let e=Array.from(t.classList).find(r=>r.indexOf("ql-")===0);if(!e)return;if(e=e.slice(3),t.tagName==="BUTTON"&&t.setAttribute("type","button"),this.handlers[e]==null&&this.quill.scroll.query(e)==null){Ki.warn("ignoring attaching to nonexistent format",e,t);return}const s=t.tagName==="SELECT"?"change":"click";t.addEventListener(s,r=>{let i;if(t.tagName==="SELECT"){if(t.selectedIndex<0)return;const a=t.options[t.selectedIndex];a.hasAttribute("selected")?i=!1:i=a.value||!1}else t.classList.contains("ql-active")?i=!1:i=t.value||!t.hasAttribute("value"),r.preventDefault();this.quill.focus();const[o]=this.quill.selection.getRange();if(this.handlers[e]!=null)this.handlers[e].call(this,i);else if(this.quill.scroll.query(e).prototype instanceof Ot){if(i=prompt(`Enter ${e}`),!i)return;this.quill.updateContents(new V().retain(o.index).delete(o.length).insert({[e]:i}),T.sources.USER)}else this.quill.format(e,i,T.sources.USER);this.update(o)}),this.controls.push([e,t])}update(t){const e=t==null?{}:this.quill.getFormat(t);this.controls.forEach(s=>{const[r,i]=s;if(i.tagName==="SELECT"){let o=null;if(t==null)o=null;else if(e[r]==null)o=i.querySelector("option[selected]");else if(!Array.isArray(e[r])){let a=e[r];typeof a=="string"&&(a=a.replace(/"/g,'\\"')),o=i.querySelector(`option[value="${a}"]`)}o==null?(i.value="",i.selectedIndex=-1):o.selected=!0}else if(t==null)i.classList.remove("ql-active"),i.setAttribute("aria-pressed","false");else if(i.hasAttribute("value")){const o=e[r],a=o===i.getAttribute("value")||o!=null&&o.toString()===i.getAttribute("value")||o==null&&!i.getAttribute("value");i.classList.toggle("ql-active",a),i.setAttribute("aria-pressed",a.toString())}else{const o=e[r]!=null;i.classList.toggle("ql-active",o),i.setAttribute("aria-pressed",o.toString())}})}}ai.DEFAULTS={};function Wi(n,t,e){const s=document.createElement("button");s.setAttribute("type","button"),s.classList.add(`ql-${t}`),s.setAttribute("aria-pressed","false"),e!=null?(s.value=e,s.setAttribute("aria-label",`${t}: ${e}`)):s.setAttribute("aria-label",t),n.appendChild(s)}function Wu(n,t){Array.isArray(t[0])||(t=[t]),t.forEach(e=>{const s=document.createElement("span");s.classList.add("ql-formats"),e.forEach(r=>{if(typeof r=="string")Wi(s,r);else{const i=Object.keys(r)[0],o=r[i];Array.isArray(o)?Zu(s,i,o):Wi(s,i,o)}}),n.appendChild(s)})}function Zu(n,t,e){const s=document.createElement("select");s.classList.add(`ql-${t}`),e.forEach(r=>{const i=document.createElement("option");r!==!1?i.setAttribute("value",String(r)):i.setAttribute("selected","selected"),s.appendChild(i)}),n.appendChild(s)}ai.DEFAULTS={container:null,handlers:{clean(){const n=this.quill.getSelection();if(n!=null)if(n.length===0){const t=this.quill.getFormat();Object.keys(t).forEach(e=>{this.quill.scroll.query(e,G.INLINE)!=null&&this.quill.format(e,!1,T.sources.USER)})}else this.quill.removeFormat(n.index,n.length,T.sources.USER)},direction(n){const{align:t}=this.quill.getFormat();n==="rtl"&&t==null?this.quill.format("align","right",T.sources.USER):!n&&t==="right"&&this.quill.format("align",!1,T.sources.USER),this.quill.format("direction",n,T.sources.USER)},indent(n){const t=this.quill.getSelection(),e=this.quill.getFormat(t),s=parseInt(e.indent||0,10);if(n==="+1"||n==="-1"){let r=n==="+1"?1:-1;e.direction==="rtl"&&(r*=-1),this.quill.format("indent",s+r,T.sources.USER)}},link(n){n===!0&&(n=prompt("Enter link URL:")),this.quill.format("link",n,T.sources.USER)},list(n){const t=this.quill.getSelection(),e=this.quill.getFormat(t);n==="check"?e.list==="checked"||e.list==="unchecked"?this.quill.format("list",!1,T.sources.USER):this.quill.format("list","unchecked",T.sources.USER):this.quill.format("list",n,T.sources.USER)}}};const Xu='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>',Yu='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>',Qu='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>',Ju='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>',th='<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>',eh='<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>',sh='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>',rh='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>',Zi='<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>',nh='<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>',ih='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>',lh='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>',oh='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>',ah='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>',ch='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',uh='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',hh='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>',dh='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',fh='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>',ph='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>',gh='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>',mh='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>',bh='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>',yh='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>',vh='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>',xh='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>',Nh='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>',Eh='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>',wh='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>',Ah='<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>',Ch='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>',Th='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>',Lh='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>',Bs={align:{"":Xu,center:Yu,right:Qu,justify:Ju},background:th,blockquote:eh,bold:sh,clean:rh,code:Zi,"code-block":Zi,color:nh,direction:{"":ih,rtl:lh},formula:oh,header:{1:ah,2:ch,3:uh,4:hh,5:dh,6:fh},italic:ph,image:gh,indent:{"+1":mh,"-1":bh},link:yh,list:{bullet:vh,check:xh,ordered:Nh},script:{sub:Eh,super:wh},strike:Ah,table:Ch,underline:Th,video:Lh},Sh='<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>';let Xi=0;function Yi(n,t){n.setAttribute(t,`${n.getAttribute(t)!=="true"}`)}class Sr{constructor(t){this.select=t,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",()=>{this.togglePicker()}),this.label.addEventListener("keydown",e=>{switch(e.key){case"Enter":this.togglePicker();break;case"Escape":this.escape(),e.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}togglePicker(){this.container.classList.toggle("ql-expanded"),Yi(this.label,"aria-expanded"),Yi(this.options,"aria-hidden")}buildItem(t){const e=document.createElement("span");e.tabIndex="0",e.setAttribute("role","button"),e.classList.add("ql-picker-item");const s=t.getAttribute("value");return s&&e.setAttribute("data-value",s),t.textContent&&e.setAttribute("data-label",t.textContent),e.addEventListener("click",()=>{this.selectItem(e,!0)}),e.addEventListener("keydown",r=>{switch(r.key){case"Enter":this.selectItem(e,!0),r.preventDefault();break;case"Escape":this.escape(),r.preventDefault();break}}),e}buildLabel(){const t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML=Sh,t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}buildOptions(){const t=document.createElement("span");t.classList.add("ql-picker-options"),t.setAttribute("aria-hidden","true"),t.tabIndex="-1",t.id=`ql-picker-options-${Xi}`,Xi+=1,this.label.setAttribute("aria-controls",t.id),this.options=t,Array.from(this.select.options).forEach(e=>{const s=this.buildItem(e);t.appendChild(s),e.selected===!0&&this.selectItem(s)}),this.container.appendChild(t)}buildPicker(){Array.from(this.select.attributes).forEach(t=>{this.container.setAttribute(t.name,t.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout(()=>this.label.focus(),1)}close(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}selectItem(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const s=this.container.querySelector(".ql-selected");t!==s&&(s!=null&&s.classList.remove("ql-selected"),t!=null&&(t.classList.add("ql-selected"),this.select.selectedIndex=Array.from(t.parentNode.children).indexOf(t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e&&(this.select.dispatchEvent(new Event("change")),this.close())))}update(){let t;if(this.select.selectedIndex>-1){const s=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(s)}else this.selectItem(null);const e=t!=null&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",e)}}class $l extends Sr{constructor(t,e){super(t),this.label.innerHTML=e,this.container.classList.add("ql-color-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0,7).forEach(s=>{s.classList.add("ql-primary")})}buildItem(t){const e=super.buildItem(t);return e.style.backgroundColor=t.getAttribute("value")||"",e}selectItem(t,e){super.selectItem(t,e);const s=this.label.querySelector(".ql-color-label"),r=t&&t.getAttribute("data-value")||"";s&&(s.tagName==="line"?s.style.stroke=r:s.style.fill=r)}}class Fl extends Sr{constructor(t,e){super(t),this.container.classList.add("ql-icon-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach(s=>{s.innerHTML=e[s.getAttribute("data-value")||""]}),this.defaultItem=this.container.querySelector(".ql-selected"),this.selectItem(this.defaultItem)}selectItem(t,e){super.selectItem(t,e);const s=t||this.defaultItem;if(s!=null){if(this.label.innerHTML===s.innerHTML)return;this.label.innerHTML=s.innerHTML}}}const qh=n=>{const{overflowY:t}=getComputedStyle(n,null);return t!=="visible"&&t!=="clip"};class Hl{constructor(t,e){this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,qh(this.quill.root)&&this.quill.root.addEventListener("scroll",()=>{this.root.style.marginTop=`${-1*this.quill.root.scrollTop}px`}),this.hide()}hide(){this.root.classList.add("ql-hidden")}position(t){const e=t.left+t.width/2-this.root.offsetWidth/2,s=t.bottom+this.quill.root.scrollTop;this.root.style.left=`${e}px`,this.root.style.top=`${s}px`,this.root.classList.remove("ql-flip");const r=this.boundsContainer.getBoundingClientRect(),i=this.root.getBoundingClientRect();let o=0;if(i.right>r.right&&(o=r.right-i.right,this.root.style.left=`${e+o}px`),i.left<r.left&&(o=r.left-i.left,this.root.style.left=`${e+o}px`),i.bottom>r.bottom){const a=i.bottom-i.top,u=t.bottom-t.top+a;this.root.style.top=`${s-u}px`,this.root.classList.add("ql-flip")}return o}show(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}const kh=[!1,"center","right","justify"],_h=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],Oh=[!1,"serif","monospace"],Ih=["1","2","3",!1],Rh=["small",!1,"large","huge"];class $s extends as{constructor(t,e){super(t,e);const s=r=>{if(!document.body.contains(t.root)){document.body.removeEventListener("click",s);return}this.tooltip!=null&&!this.tooltip.root.contains(r.target)&&document.activeElement!==this.tooltip.textbox&&!this.quill.hasFocus()&&this.tooltip.hide(),this.pickers!=null&&this.pickers.forEach(i=>{i.container.contains(r.target)||i.close()})};t.emitter.listenDOM("click",document.body,s)}addModule(t){const e=super.addModule(t);return t==="toolbar"&&this.extendToolbar(e),e}buildButtons(t,e){Array.from(t).forEach(s=>{(s.getAttribute("class")||"").split(/\s+/).forEach(i=>{if(i.startsWith("ql-")&&(i=i.slice(3),e[i]!=null))if(i==="direction")s.innerHTML=e[i][""]+e[i].rtl;else if(typeof e[i]=="string")s.innerHTML=e[i];else{const o=s.value||"";o!=null&&e[i][o]&&(s.innerHTML=e[i][o])}})})}buildPickers(t,e){this.pickers=Array.from(t).map(r=>{if(r.classList.contains("ql-align")&&(r.querySelector("option")==null&&Ls(r,kh),typeof e.align=="object"))return new Fl(r,e.align);if(r.classList.contains("ql-background")||r.classList.contains("ql-color")){const i=r.classList.contains("ql-background")?"background":"color";return r.querySelector("option")==null&&Ls(r,_h,i==="background"?"#ffffff":"#000000"),new $l(r,e[i])}return r.querySelector("option")==null&&(r.classList.contains("ql-font")?Ls(r,Oh):r.classList.contains("ql-header")?Ls(r,Ih):r.classList.contains("ql-size")&&Ls(r,Rh)),new Sr(r)});const s=()=>{this.pickers.forEach(r=>{r.update()})};this.quill.on($.events.EDITOR_CHANGE,s)}}$s.DEFAULTS=Ee({},as.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit("formula")},image(){let n=this.container.querySelector("input.ql-image[type=file]");n==null&&(n=document.createElement("input"),n.setAttribute("type","file"),n.setAttribute("accept",this.quill.uploader.options.mimetypes.join(", ")),n.classList.add("ql-image"),n.addEventListener("change",()=>{const t=this.quill.getSelection(!0);this.quill.uploader.upload(t,n.files),n.value=""}),this.container.appendChild(n)),n.click()},video(){this.quill.theme.tooltip.edit("video")}}}}});class zl extends Hl{constructor(t,e){super(t,e),this.textbox=this.root.querySelector('input[type="text"]'),this.listen()}listen(){this.textbox.addEventListener("keydown",t=>{t.key==="Enter"?(this.save(),t.preventDefault()):t.key==="Escape"&&(this.cancel(),t.preventDefault())})}cancel(){this.hide(),this.restoreFocus()}edit(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),this.textbox==null)return;e!=null?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value="");const s=this.quill.getBounds(this.quill.selection.savedRange);s!=null&&this.position(s),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute(`data-${t}`)||""),this.root.setAttribute("data-mode",t)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:t}=this.textbox;switch(this.root.getAttribute("data-mode")){case"link":{const{scrollTop:e}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,"link",t,$.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",t,$.sources.USER)),this.quill.root.scrollTop=e;break}case"video":t=jh(t);case"formula":{if(!t)break;const e=this.quill.getSelection(!0);if(e!=null){const s=e.index+e.length;this.quill.insertEmbed(s,this.root.getAttribute("data-mode"),t,$.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(s+1," ",$.sources.USER),this.quill.setSelection(s+2,$.sources.USER)}break}}this.textbox.value="",this.hide()}}function jh(n){let t=n.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||n.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return t?`${t[1]||"https"}://www.youtube.com/embed/${t[2]}?showinfo=0`:(t=n.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?`${t[1]||"https"}://player.vimeo.com/video/${t[2]}/`:n}function Ls(n,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;t.forEach(s=>{const r=document.createElement("option");s===e?r.setAttribute("selected","selected"):r.setAttribute("value",String(s)),n.appendChild(r)})}const Mh=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]];class Vl extends zl{constructor(t,e){super(t,e),this.quill.on($.events.EDITOR_CHANGE,(s,r,i,o)=>{if(s===$.events.SELECTION_CHANGE)if(r!=null&&r.length>0&&o===$.sources.USER){this.show(),this.root.style.left="0px",this.root.style.width="",this.root.style.width=`${this.root.offsetWidth}px`;const a=this.quill.getLines(r.index,r.length);if(a.length===1){const u=this.quill.getBounds(r);u!=null&&this.position(u)}else{const u=a[a.length-1],h=this.quill.getIndex(u),b=Math.min(u.length()-1,r.index+r.length-h),x=this.quill.getBounds(new _e(h,b));x!=null&&this.position(x)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()})}listen(){super.listen(),this.root.querySelector(".ql-close").addEventListener("click",()=>{this.root.classList.remove("ql-editing")}),this.quill.on($.events.SCROLL_OPTIMIZE,()=>{setTimeout(()=>{if(this.root.classList.contains("ql-hidden"))return;const t=this.quill.getSelection();if(t!=null){const e=this.quill.getBounds(t);e!=null&&this.position(e)}},1)})}cancel(){this.show()}position(t){const e=super.position(t),s=this.root.querySelector(".ql-tooltip-arrow");return s.style.marginLeft="",e!==0&&(s.style.marginLeft=`${-1*e-s.offsetWidth/2}px`),e}}j(Vl,"TEMPLATE",['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""));class Gl extends $s{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=Mh),super(t,e),this.quill.container.classList.add("ql-bubble")}extendToolbar(t){this.tooltip=new Vl(this.quill,this.options.bounds),t.container!=null&&(this.tooltip.root.appendChild(t.container),this.buildButtons(t.container.querySelectorAll("button"),Bs),this.buildPickers(t.container.querySelectorAll("select"),Bs))}}Gl.DEFAULTS=Ee({},$s.DEFAULTS,{modules:{toolbar:{handlers:{link(n){n?this.quill.theme.tooltip.edit():this.quill.format("link",!1,T.sources.USER)}}}}});const Bh=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]];class Kl extends zl{constructor(){super(...arguments);j(this,"preview",this.root.querySelector("a.ql-preview"))}listen(){super.listen(),this.root.querySelector("a.ql-action").addEventListener("click",e=>{this.root.classList.contains("ql-editing")?this.save():this.edit("link",this.preview.textContent),e.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",e=>{if(this.linkRange!=null){const s=this.linkRange;this.restoreFocus(),this.quill.formatText(s,"link",!1,$.sources.USER),delete this.linkRange}e.preventDefault(),this.hide()}),this.quill.on($.events.SELECTION_CHANGE,(e,s,r)=>{if(e!=null){if(e.length===0&&r===$.sources.USER){const[i,o]=this.quill.scroll.descendant(Ne,e.index);if(i!=null){this.linkRange=new _e(e.index-o,i.length());const a=Ne.formats(i.domNode);this.preview.textContent=a,this.preview.setAttribute("href",a),this.show();const u=this.quill.getBounds(this.linkRange);u!=null&&this.position(u);return}}else delete this.linkRange;this.hide()}})}show(){super.show(),this.root.removeAttribute("data-mode")}}j(Kl,"TEMPLATE",['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""));class Wl extends $s{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=Bh),super(t,e),this.quill.container.classList.add("ql-snow")}extendToolbar(t){t.container!=null&&(t.container.classList.add("ql-snow"),this.buildButtons(t.container.querySelectorAll("button"),Bs),this.buildPickers(t.container.querySelectorAll("select"),Bs),this.tooltip=new Kl(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"k",shortKey:!0},(e,s)=>{t.handlers.link.call(t,!s.format.link)}))}}Wl.DEFAULTS=Ee({},$s.DEFAULTS,{modules:{toolbar:{handlers:{link(n){if(n){const t=this.quill.getSelection();if(t==null||t.length===0)return;let e=this.quill.getText(t);/^\S+@\S+\.\S+$/.test(e)&&e.indexOf("mailto:")!==0&&(e=`mailto:${e}`);const{tooltip:s}=this.quill.theme;s.edit("link",e)}else this.quill.format("link",!1,T.sources.USER)}}}}});T.register({"attributors/attribute/direction":Ll,"attributors/class/align":Al,"attributors/class/background":eu,"attributors/class/color":tu,"attributors/class/direction":Sl,"attributors/class/font":_l,"attributors/class/size":Il,"attributors/style/align":Cl,"attributors/style/background":si,"attributors/style/color":ei,"attributors/style/direction":ql,"attributors/style/font":Ol,"attributors/style/size":Rl},!0);T.register({"formats/align":Al,"formats/direction":Sl,"formats/indent":Vu,"formats/background":si,"formats/color":ei,"formats/font":_l,"formats/size":Il,"formats/blockquote":Bn,"formats/code-block":Lt,"formats/header":Dn,"formats/list":Us,"formats/bold":Ms,"formats/code":ri,"formats/italic":Pn,"formats/link":Ne,"formats/script":Un,"formats/strike":$n,"formats/underline":Fn,"formats/formula":yr,"formats/image":Hn,"formats/video":vr,"modules/syntax":Ul,"modules/table":Ku,"modules/toolbar":ai,"themes/bubble":Gl,"themes/snow":Wl,"ui/icons":Bs,"ui/picker":Sr,"ui/icon-picker":Fl,"ui/color-picker":$l,"ui/tooltip":Hl},!0);const Dh=({modules:n,formats:t,placeholder:e})=>{const s=K.useRef(null),[r,i]=K.useState(null);return K.useEffect(()=>{if(s.current){const o=new T(s.current,{modules:n,formats:t,placeholder:e});i(o)}},[]),{quillRef:s,quill:r,Quill:T}},Ph=({messages:n})=>m.jsx(ca,{hideSymbol:!0,tooltipProps:{content:"点击复制"},classNames:{copyButton:"self-start sticky top-0 right-0"},className:"bg-content1 h-96 overflow-y-scroll items-start",children:JSON.stringify(n,null,2).split(`
`).map((t,e)=>m.jsx("span",{className:"whitespace-pre-wrap break-all",children:t},e))}),je=()=>{const n=Lo();return e=>{n.alert({title:"消息内容",size:"3xl",content:K.createElement(Ph,{messages:e})})}};var _t=(n=>(n.GroupUpload="group_upload",n.GroupAdmin="group_admin",n.GroupDecrease="group_decrease",n.GroupIncrease="group_increase",n.GroupBan="group_ban",n.FriendAdd="friend_add",n.GroupRecall="group_recall",n.FriendRecall="friend_recall",n.Notify="notify",n.GroupMsgEmojiLike="group_msg_emoji_like",n.GroupMsgEmojiLikeLagrange="reaction",n.GroupEssence="essence",n.GroupCard="group_card",n))(_t||{});const Sd=n=>{switch(n){case"message":return"消息";case"notice":return"通知";case"request":return"请求";case"meta_event":return"元事件";case"message_sent":return"消息上报";default:return"未知"}},qd=n=>{switch(n){case"enable":return"启用";case"disable":return"停用";case"connect":return"连接";default:return"未知"}},kd=n=>{switch(n){case"enable":return"success";case"disable":return"danger";case"connect":return"success";default:return"default"}},_d=n=>"post_type"in n,Od=n=>"status"in n&&"retcode"in n,Id=n=>{switch(n){case"ok":return"成功";case"failed":return"失败";case"async":return"异步";default:return"未知"}},Rd=n=>{switch(n){case"ok":return"success";case"failed":return"danger";case"async":return"warning";default:return"default"}},jd=n=>{switch(n){case _t.GroupUpload:return"群文件上传";case _t.GroupAdmin:return"群管理员变动";case _t.GroupDecrease:return"群成员减少";case _t.GroupIncrease:return"群成员增加";case _t.GroupBan:return"群禁言";case _t.FriendAdd:return"好友添加";case _t.GroupRecall:return"群消息撤回";case _t.FriendRecall:return"好友消息撤回";case _t.Notify:return"通知";case _t.GroupMsgEmojiLike:return"群消息表情回应";case _t.GroupEssence:return"群消息精华";case _t.GroupCard:return"群名片更新";default:return"未知"}},Md=n=>n.message_type==="group",Uh=n=>{var e,s,r;let t={type:"text",data:{text:n.insert}};return typeof n.insert!="string"&&((e=n.insert)!=null&&e.image?t={type:"image",data:{file:n.insert.image.src}}:(s=n.insert)!=null&&s.emoji?t={type:"face",data:{id:n.insert.emoji.id}}:(r=n.insert)!=null&&r.reply&&(t={type:"reply",data:{id:n.insert.reply.messageId}})),t},$h=()=>{const[n,t]=K.useState(""),e=K.useRef(null),s=je(),r=U=>{s([{type:"record",data:{file:U}}])},[i,o]=K.useState(!1),a=K.useRef(null),u=K.useRef([]),[h,b]=K.useState(null),[x,d]=K.useState(!1),f=K.useRef(null),[v,E]=K.useState(0),C=K.useRef(null);K.useEffect(()=>{var U;i?(navigator.mediaDevices.getUserMedia({audio:!0}).then(Q=>{f.current=Q;const rt=new MediaRecorder(Q);a.current=rt,rt.start(),rt.ondataavailable=st=>{st.data.size>0&&u.current.push(st.data)},rt.onstop=()=>{if(u.current.length>0){const st=new Blob(u.current,{type:"audio/wav"}),ht=new FileReader;ht.readAsDataURL(st),ht.onloadend=()=>{const ft=ht.result;b(ft),d(!0)},u.current=[]}Q.getTracks().forEach(st=>st.stop())}}),C.current=setInterval(()=>{E(Q=>Q+1)},1e3)):((U=a.current)==null||U.stop(),C.current&&(clearInterval(C.current),C.current=null))},[i]);const S=()=>{b(null),d(!1),E(0),o(!0)},M=()=>{o(!1)},P=()=>{h&&r(h)},tt=U=>{const Q=Math.floor(U/60),rt=U%60;return`${Q}:${rt.toString().padStart(2,"0")}`};return m.jsxs(m.Fragment,{children:[m.jsxs(Pt,{children:[m.jsx(vt,{content:"发送音频",children:m.jsx("div",{className:"max-w-fit",children:m.jsx(Ut,{children:m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",children:m.jsx(di,{className:"text-xl"})})})})}),m.jsxs($t,{className:"flex-row gap-2 p-4",children:[m.jsx(vt,{content:"上传音频",children:m.jsx(ct,{className:"text-lg",color:"primary",isIconOnly:!0,variant:"flat",radius:"full",onPress:()=>{var U;(U=e==null?void 0:e.current)==null||U.click()},children:m.jsx(Er,{})})}),m.jsxs(Pt,{children:[m.jsx(vt,{content:"输入音频地址",children:m.jsx("div",{className:"max-w-fit",children:m.jsx(Ut,{tooltip:"输入音频地址",children:m.jsx(ct,{className:"text-lg",color:"primary",isIconOnly:!0,variant:"flat",radius:"full",children:m.jsx(wr,{})})})})}),m.jsxs($t,{className:"flex-row gap-1 p-2",children:[m.jsx(Gt,{value:n,onChange:U=>t(U.target.value),placeholder:"请输入音频地址"}),m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",onPress:()=>{if(!is(n)){us.error("请输入正确的音频地址");return}r(n),t("")},children:m.jsx(no,{})})]})]}),m.jsxs(Pt,{children:[m.jsx(vt,{content:"录制音频",children:m.jsx("div",{className:"max-w-fit",children:m.jsx(Ut,{children:m.jsx(ct,{className:"text-lg",color:"primary",isIconOnly:!0,variant:"flat",radius:"full",children:m.jsx(di,{})})})})}),m.jsxs($t,{className:"flex-col gap-2 p-4",children:[m.jsxs("div",{className:"flex gap-2",children:[m.jsx(ct,{color:"primary",variant:"flat",onPress:i?M:S,children:i?"停止录制":"开始录制"}),x&&h&&m.jsx(ct,{color:"primary",variant:"flat",onPress:P,children:"查看消息"})]}),(i||h)&&m.jsxs("div",{className:"flex gap-1 items-center",children:[m.jsx("span",{className:io("w-4 h-4 rounded-full",i?"animate-pulse bg-primary-400":"bg-success-400")}),m.jsxs("span",{children:["录制时长: ",tt(v)]})]}),x&&h&&m.jsx("audio",{controls:!0,src:h})]})]})]})]}),m.jsx("input",{type:"file",ref:e,hidden:!0,accept:"audio/*",className:"hidden",onChange:U=>{var st;const Q=(st=U.target.files)==null?void 0:st[0];if(!Q)return;const rt=new FileReader;rt.readAsDataURL(Q),rt.onload=ht=>{var g;const ft=(g=ht.target)==null?void 0:g.result;r(ft),U.target.value=""}}})]})},Fh=()=>{const n=je();return m.jsx(vt,{content:"发送骰子",children:m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",onPress:()=>{n([{type:"dice"}])},children:m.jsx(Ro,{className:"text-lg"})})})};var zn=Mo();const Bd=Wn(zn),Qi=zn.data.map(n=>({alt:n.QDes,src:zn.getUrl(n.QSid),id:n.QSid})),Hh=({onInsertEmoji:n,onOpenChange:t})=>{const[e,s]=K.useState([]),[r,i]=K.useState(!1),o=K.useRef(null);K.useEffect(()=>{r&&(s([]),requestAnimationFrame(()=>a()))},[r]);const a=(u=0,h=10)=>{u<Qi.length&&(s(b=>[...b,...Qi.slice(u,u+h)]),requestAnimationFrame(()=>a(u+h,h)))};return m.jsx("div",{ref:o,children:m.jsxs(Pt,{portalContainer:o.current,shouldCloseOnScroll:!1,placement:"right-start",onOpenChange:u=>{t(u),i(u)},children:[m.jsx(vt,{content:"插入表情",children:m.jsx("div",{className:"max-w-fit",children:m.jsx(Ut,{children:m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",children:m.jsx(ko,{className:"text-xl"})})})})}),m.jsx($t,{className:"grid grid-cols-8 gap-1 flex-wrap justify-start items-start overflow-y-auto max-w-full max-h-96 p-2",children:e.map(u=>m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",onPress:()=>n(u),children:m.jsx(lo,{src:u.src,alt:u.alt,className:"w-6 h-6"})},u.id))})]})})};function zh(n){return rl({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4"},child:[]},{tag:"path",attr:{d:"M14 2v4a2 2 0 0 0 2 2h4"},child:[]},{tag:"path",attr:{d:"M3 15h6"},child:[]},{tag:"path",attr:{d:"M6 12v6"},child:[]}]})(n)}const Vh=()=>{const[n,t]=K.useState(""),e=K.useRef(null),s=je(),r=i=>{s([{type:"file",data:{file:i}}])};return m.jsxs(m.Fragment,{children:[m.jsxs(Pt,{children:[m.jsx(vt,{content:"发送文件",children:m.jsx("div",{className:"max-w-fit",children:m.jsx(Ut,{children:m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",children:m.jsx(oo,{className:"text-lg"})})})})}),m.jsxs($t,{className:"flex-row gap-2 p-4",children:[m.jsx(vt,{content:"上传文件",children:m.jsx(ct,{className:"text-lg",color:"primary",isIconOnly:!0,variant:"flat",radius:"full",onPress:()=>{var i;(i=e==null?void 0:e.current)==null||i.click()},children:m.jsx(Er,{})})}),m.jsxs(Pt,{children:[m.jsx(vt,{content:"输入文件地址",children:m.jsx("div",{className:"max-w-fit",children:m.jsx(Ut,{tooltip:"输入文件地址",children:m.jsx(ct,{className:"text-lg",color:"primary",isIconOnly:!0,variant:"flat",radius:"full",children:m.jsx(wr,{})})})})}),m.jsxs($t,{className:"flex-row gap-1 p-2",children:[m.jsx(Gt,{value:n,onChange:i=>t(i.target.value),placeholder:"请输入文件地址"}),m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",onPress:()=>{if(!is(n)){us.error("请输入正确的文件地址");return}r(n),t("")},children:m.jsx(zh,{})})]})]})]})]}),m.jsx("input",{type:"file",ref:e,hidden:!0,className:"hidden",onChange:i=>{var u;const o=(u=i.target.files)==null?void 0:u[0];if(!o)return;const a=new FileReader;a.readAsDataURL(o),a.onload=h=>{var x;const b=(x=h.target)==null?void 0:x.result;r(b),i.target.value=""}}})]})},Gh=({insertImage:n,onOpenChange:t})=>{const[e,s]=K.useState(""),r=K.useRef(null);return m.jsxs(m.Fragment,{children:[m.jsxs(Pt,{onOpenChange:t,children:[m.jsx(vt,{content:"插入图片",children:m.jsx("div",{className:"max-w-fit",children:m.jsx(Ut,{children:m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",children:m.jsx(_o,{className:"text-xl"})})})})}),m.jsxs($t,{className:"flex-row gap-2 p-4",children:[m.jsx(vt,{content:"上传图片",children:m.jsx(ct,{className:"text-lg",color:"primary",isIconOnly:!0,variant:"flat",radius:"full",onPress:()=>{var i;(i=r==null?void 0:r.current)==null||i.click()},children:m.jsx(Er,{})})}),m.jsxs(Pt,{children:[m.jsx(vt,{content:"输入图片地址",children:m.jsx("div",{className:"max-w-fit",children:m.jsx(Ut,{tooltip:"输入图片地址",children:m.jsx(ct,{className:"text-lg",color:"primary",isIconOnly:!0,variant:"flat",radius:"full",children:m.jsx(wr,{})})})})}),m.jsxs($t,{className:"flex-row gap-1 p-2",children:[m.jsx(Gt,{value:e,onChange:i=>s(i.target.value),placeholder:"请输入图片地址"}),m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",onPress:()=>{if(!is(e)){us.error("请输入正确的图片地址");return}n(e),s("")},children:m.jsx(Oo,{})})]})]})]})]}),m.jsx("input",{type:"file",ref:r,hidden:!0,accept:"image/*",className:"hidden",onChange:i=>{var u;const o=(u=i.target.files)==null?void 0:u[0];if(!o)return;const a=new FileReader;a.readAsDataURL(o),a.onload=h=>{var x;const b=(x=h.target)==null?void 0:x.result;n(b),i.target.value=""}}})]})},Kh=()=>{const[n,t]=K.useState(""),[e,s]=K.useState(new Set(["163"])),[r,i]=K.useState("default"),o=K.useRef(null),{control:a,handleSubmit:u,reset:h}=$o({defaultValues:{url:"",audio:"",title:"",image:"",content:""}}),b=je(),x=f=>{const v=[];f.type==="custom"?v.push({type:"music",data:{...f,type:"custom"}}):v.push({type:"music",data:f}),b(v)},d=f=>{x({type:"custom",...f}),h()};return m.jsx("div",{ref:o,className:"overflow-visible",children:m.jsxs(Pt,{placement:"right-start",shouldCloseOnScroll:!1,portalContainer:o.current,children:[m.jsx(vt,{content:"发送音乐",children:m.jsx("div",{className:"max-w-fit",children:m.jsx(Ut,{children:m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",children:m.jsx(So,{className:"text-xl"})})})})}),m.jsx($t,{className:"gap-2 p-4",children:m.jsxs(Uo,{placement:"top",className:"w-96",fullWidth:!0,selectedKey:r,onSelectionChange:i,children:[m.jsxs(fi,{title:"主流平台",className:"flex flex-col gap-2",children:[m.jsx(Do,{onClick:f=>f.stopPropagation(),"aria-label":"音乐平台",selectedKeys:e,label:"音乐平台",placeholder:"请选择音乐平台",items:[{name:"QQ音乐",id:"qq"},{name:"网易云音乐",id:"163"},{name:"虾米音乐",id:"xm"}],onSelectionChange:s,children:f=>m.jsx(Po,{value:f.id,children:f.name},f.id)}),m.jsx(Gt,{value:n,onChange:f=>t(f.target.value),placeholder:"请输入音乐ID",label:"音乐ID"}),m.jsxs(ct,{fullWidth:!0,size:"lg",color:"primary",variant:"flat",radius:"full",onPress:()=>{if(!n){us.error("请输入音乐ID");return}x({type:Array.from(e)[0],id:n}),t("")},startContent:m.jsx(ui,{}),children:["创建",Array.from(e)[0]==="163"?"网易云":"QQ","音乐"]})]},"default"),m.jsx(fi,{title:"自定义音乐",className:"flex flex-col gap-2",children:m.jsxs(Fo,{onSubmit:u(d),className:"flex flex-col gap-2",validationBehavior:"native",children:[m.jsx(Ts,{name:"url",control:a,render:({field:f})=>m.jsx(Gt,{...f,isRequired:!0,validate:v=>is(v)?null:"请输入正确的音乐URL",size:"sm",placeholder:"请输入音乐URL",label:"音乐URL"})}),m.jsx(Ts,{name:"audio",control:a,render:({field:f})=>m.jsx(Gt,{...f,isRequired:!0,validate:v=>is(v)?null:"请输入正确的音频URL",size:"sm",placeholder:"请输入音频URL",label:"音频URL"})}),m.jsx(Ts,{name:"title",control:a,render:({field:f})=>m.jsx(Gt,{...f,isRequired:!0,size:"sm",errorMessage:"请输入音乐标题",placeholder:"请输入音乐标题",label:"音乐标题"})}),m.jsx(Ts,{name:"image",control:a,render:({field:f})=>m.jsx(Gt,{...f,size:"sm",placeholder:"请输入封面图片URL",label:"封面图片URL"})}),m.jsx(Ts,{name:"content",control:a,render:({field:f})=>m.jsx(Gt,{...f,size:"sm",placeholder:"请输入音乐描述",label:"音乐描述"})}),m.jsx(ct,{fullWidth:!0,size:"lg",color:"primary",variant:"flat",radius:"full",type:"submit",startContent:m.jsx(ui,{}),children:"创建自定义音乐"})]})},"custom")]})})]})})},Wh=({insertReply:n})=>{const[t,e]=K.useState("");return m.jsx(m.Fragment,{children:m.jsxs(Pt,{children:[m.jsx(vt,{content:"回复消息",children:m.jsx("div",{className:"max-w-fit",children:m.jsx(Ut,{children:m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",children:m.jsx(jo,{className:"text-lg"})})})})}),m.jsxs($t,{className:"flex-row gap-2 p-4",children:[m.jsx(Gt,{placeholder:"输入消息 ID",value:t,onChange:s=>{const r=s.target.value;/^(?:0|(?:-?[1-9]\d*))$/.test(r)&&e(r)}}),m.jsx(ct,{color:"primary",variant:"flat",radius:"full",isIconOnly:!0,onPress:()=>{n(t),e("")},children:m.jsx(Io,{})})]})]})})};function Zh(n){return rl({attr:{viewBox:"0 0 32 32"},child:[{tag:"path",attr:{d:"M 11.40625 6.96875 C 10.578125 6.953125 9.890625 7.125 9.46875 7.25 C 9.457031 7.25 9.449219 7.25 9.4375 7.25 L 6.9375 8.03125 C 4.003906 8.933594 2 11.652344 2 14.71875 L 2 20 C 2 23.855469 5.144531 27 9 27 L 18.90625 27 C 20.125 27.027344 21.304688 26.3125 21.78125 25.125 C 22.082031 24.371094 22.039063 23.578125 21.75 22.875 C 22.363281 22.550781 22.882813 22.027344 23.15625 21.34375 C 23.46875 20.558594 23.417969 19.722656 23.09375 19 L 27 19 C 28.644531 19 30 17.644531 30 16 C 30 14.355469 28.644531 13 27 13 L 25.46875 13 L 25.875 12.875 C 27.449219 12.398438 28.351563 10.699219 27.875 9.125 C 27.398438 7.550781 25.699219 6.648438 24.125 7.125 L 15.6875 9.71875 C 15.613281 9.53125 15.527344 9.328125 15.40625 9.125 C 14.90625 8.289063 13.894531 7.34375 12.28125 7.0625 C 11.980469 7.011719 11.683594 6.972656 11.40625 6.96875 Z M 25.125 9 C 25.515625 9.042969 25.847656 9.3125 25.96875 9.71875 C 26.132813 10.257813 25.820313 10.804688 25.28125 10.96875 L 18.4375 13.03125 L 18.78125 14.15625 L 18.78125 15 L 27 15 C 27.566406 15 28 15.433594 28 16 C 28 16.566406 27.566406 17 27 17 L 20.40625 17 L 17.78125 15.96875 C 17.402344 15.816406 17.011719 15.742188 16.625 15.75 L 16.09375 11.65625 L 24.71875 9.03125 C 24.855469 8.988281 24.996094 8.984375 25.125 9 Z M 11.375 9.03125 C 11.566406 9.03125 11.765625 9.03125 11.9375 9.0625 C 13.011719 9.25 13.425781 9.71875 13.6875 10.15625 C 13.949219 10.59375 13.96875 10.90625 13.96875 10.90625 C 13.96875 10.925781 13.96875 10.949219 13.96875 10.96875 L 14.8125 17.40625 C 14.820313 17.4375 14.832031 17.46875 14.84375 17.5 C 14.96875 18.027344 14.652344 18.53125 14.125 18.65625 C 13.800781 18.734375 13.636719 18.691406 13.46875 18.59375 C 13.300781 18.496094 13.09375 18.289063 12.9375 17.84375 L 11.6875 13 C 11.609375 12.703125 11.398438 12.460938 11.121094 12.339844 C 10.839844 12.21875 10.519531 12.230469 10.25 12.375 L 8.59375 13.28125 C 8.109375 13.546875 7.933594 14.15625 8.203125 14.640625 C 8.46875 15.125 9.078125 15.300781 9.5625 15.03125 L 10.0625 14.75 L 11.03125 18.4375 C 11.039063 18.46875 11.050781 18.5 11.0625 18.53125 C 11.332031 19.304688 11.792969 19.925781 12.4375 20.3125 C 12.964844 20.628906 13.578125 20.75 14.1875 20.6875 C 13.871094 20.980469 13.609375 21.355469 13.4375 21.78125 C 12.980469 22.925781 13.269531 24.183594 14.09375 25 L 9 25 C 6.226563 25 4 22.773438 4 20 L 4 14.71875 C 4 12.519531 5.429688 10.585938 7.53125 9.9375 L 10.03125 9.1875 C 10.234375 9.125 10.804688 9.03125 11.375 9.03125 Z M 16.8125 17.78125 C 16.886719 17.792969 16.957031 17.78125 17.03125 17.8125 L 20.75 19.3125 C 21.273438 19.523438 21.523438 20.070313 21.3125 20.59375 C 21.101563 21.117188 20.523438 21.367188 20 21.15625 L 16.28125 19.6875 C 16.226563 19.667969 16.203125 19.621094 16.15625 19.59375 C 16.550781 19.085938 16.804688 18.445313 16.8125 17.78125 Z M 16.1875 21.90625 C 16.320313 21.90625 16.460938 21.917969 16.59375 21.96875 L 17.9375 22.5 L 19.25 23.03125 L 19.375 23.0625 C 19.898438 23.273438 20.148438 23.851563 19.9375 24.375 C 19.785156 24.757813 19.445313 24.980469 19.0625 25 C 19.050781 25 19.042969 25 19.03125 25 C 18.898438 25.003906 18.757813 24.988281 18.625 24.9375 L 15.84375 23.8125 C 15.320313 23.601563 15.070313 23.023438 15.28125 22.5 C 15.386719 22.238281 15.578125 22.070313 15.8125 21.96875 C 15.929688 21.917969 16.054688 21.90625 16.1875 21.90625 Z"},child:[]}]})(n)}const Xh=()=>{const n=je();return m.jsx(vt,{content:"发送猜拳",children:m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",onPress:()=>{n([{type:"rps"}])},children:m.jsx(Zh,{className:"text-2xl"})})})},Yh=()=>{const[n,t]=K.useState(""),e=K.useRef(null),s=je(),r=i=>{s([{type:"video",data:{file:i}}])};return m.jsxs(m.Fragment,{children:[m.jsxs(Pt,{children:[m.jsx(vt,{content:"发送视频",children:m.jsx("div",{className:"max-w-fit",children:m.jsx(Ut,{children:m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",children:m.jsx(qo,{className:"text-xl"})})})})}),m.jsxs($t,{className:"flex-row gap-2 p-4",children:[m.jsx(vt,{content:"上传视频",children:m.jsx(ct,{className:"text-lg",color:"primary",isIconOnly:!0,variant:"flat",radius:"full",onPress:()=>{var i;(i=e==null?void 0:e.current)==null||i.click()},children:m.jsx(Er,{})})}),m.jsxs(Pt,{children:[m.jsx(vt,{content:"输入视频地址",children:m.jsx("div",{className:"max-w-fit",children:m.jsx(Ut,{tooltip:"输入视频地址",children:m.jsx(ct,{className:"text-lg",color:"primary",isIconOnly:!0,variant:"flat",radius:"full",children:m.jsx(wr,{})})})})}),m.jsxs($t,{className:"flex-row gap-1 p-2",children:[m.jsx(Gt,{value:n,onChange:i=>t(i.target.value),placeholder:"请输入视频地址"}),m.jsx(ct,{color:"primary",variant:"flat",isIconOnly:!0,radius:"full",onPress:()=>{if(!is(n)){us.error("请输入正确的视频地址");return}r(n),t("")},children:m.jsx(ao,{})})]})]})]})]}),m.jsx("input",{type:"file",ref:e,hidden:!0,accept:"video/*",className:"hidden",onChange:i=>{var u;const o=(u=i.target.files)==null?void 0:u[0];if(!o)return;const a=new FileReader;a.readAsDataURL(o),a.onload=h=>{var x;const b=(x=h.target)==null?void 0:x.result;r(b),i.target.value=""}}})]})},Qh=T.import("blots/embed"),Je=class Je extends Qh{static create(t){const e=super.create(t);return e.setAttribute("alt",t.alt),e.setAttribute("src",t.src),e.setAttribute("data-id",t.id),e.classList.add(...Je.classNames),e}static formats(t){return{alt:t.getAttribute("alt")??"",src:t.getAttribute("src")??"",id:t.getAttribute("data-id")??""}}static value(t){return{alt:t.getAttribute("alt")??"",src:t.getAttribute("src")??"",id:t.getAttribute("data-id")??""}}};j(Je,"blotName","emoji"),j(Je,"tagName","img"),j(Je,"classNames",["w-6","h-6"]);let Vn=Je;const Jh=T.import("blots/embed"),ts=class ts extends Jh{static create(t){let e=super.create();return e.setAttribute("alt",t.alt),e.setAttribute("src",t.src),e.classList.add(...ts.classNames),e}static value(t){return{alt:t.getAttribute("alt")??"",src:t.getAttribute("src")??""}}};j(ts,"blotName","image"),j(ts,"tagName","img"),j(ts,"classNames",["max-w-48","max-h-48","align-bottom"]);let Gn=ts;const td=T.import("blots/block/embed"),es=class es extends td{static create(t){const e=super.create();e.setAttribute("data-message-id",t.messageId),e.setAttribute("contenteditable","false"),e.classList.add(...es.classNames);const s=document.createElement("div");s.classList.add("text-sm","text-default-500","relative");const r=document.createElement("div");r.classList.add("w-3","h-3","absolute","top-0","right-0");const i='<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M15.9082 12.3714H20.5982C20.5182 17.0414 19.5982 17.8114 16.7282 19.5114C16.3982 19.7114 16.2882 20.1314 16.4882 20.4714C16.6882 20.8014 17.1082 20.9114 17.4482 20.7114C20.8282 18.7114 22.0082 17.4914 22.0082 11.6714V6.28141C22.0082 4.57141 20.6182 3.19141 18.9182 3.19141H15.9182C14.1582 3.19141 12.8282 4.52141 12.8282 6.28141V9.28141C12.8182 11.0414 14.1482 12.3714 15.9082 12.3714Z" fill="#292D32"></path> <path d="M5.09 12.3714H9.78C9.7 17.0414 8.78 17.8114 5.91 19.5114C5.58 19.7114 5.47 20.1314 5.67 20.4714C5.87 20.8014 6.29 20.9114 6.63 20.7114C10.01 18.7114 11.19 17.4914 11.19 11.6714V6.28141C11.19 4.57141 9.8 3.19141 8.1 3.19141H5.1C3.33 3.19141 2 4.52141 2 6.28141V9.28141C2 11.0414 3.33 12.3714 5.09 12.3714Z" fill="#292D32"></path> </g></svg>';return r.innerHTML=i,s.innerHTML=`消息ID：${t.messageId}`,s.appendChild(r),e.appendChild(s),e}static value(t){return{messageId:t.getAttribute("data-message-id")||""}}};j(es,"blotName","reply"),j(es,"tagName","div"),j(es,"classNames",["p-2","select-none","bg-default-100","rounded-md","pointer-events-none"]);let Kn=es;const ed=()=>{const n=K.useRef(null),t=je(),e=["image","emoji","reply"],s={toolbar:"#toolbar"},{quillRef:r,quill:i,Quill:o}=Dh({modules:s,formats:e,placeholder:"请输入消息"});o&&!i&&(o.register("formats/emoji",Vn),o.register("formats/image",Gn,!0),o.register("formats/reply",Kn)),i&&(i.on("selection-change",d=>{var f;if(d){const E=i.getContents().ops[0];typeof(E==null?void 0:E.insert)!="string"&&((f=E==null?void 0:E.insert)!=null&&f.reply)&&d.index===0&&d.length!==i.getLength()&&i.setSelection(1,o.sources.SILENT)}}),i.on("text-change",()=>{var v;const f=i.getContents().ops[0];f&&typeof f.insert!="string"&&((v=f.insert)!=null&&v.reply)&&i.getLength()===1&&i.insertText(1,`
`,o.sources.SILENT)}),i.on("editor-change",d=>{var f;if(d==="text-change"){const E=i.getContents().ops[0];E&&typeof E.insert!="string"&&((f=E.insert)!=null&&f.reply)&&i.getLength()===1&&i.insertText(1,`
`,o.sources.SILENT)}}),i.root.addEventListener("compositionstart",()=>{var v;const f=i.getContents().ops[0];f&&typeof f.insert!="string"&&((v=f.insert)!=null&&v.reply)&&i.getLength()===1&&i.insertText(1,`
`,o.sources.SILENT)}));const a=d=>{if(d){const f=i==null?void 0:i.getSelection();f&&(n.current=f)}},u=d=>{const f=n.current||(i==null?void 0:i.getSelection());i==null||i.deleteText((f==null?void 0:f.index)||0,(f==null?void 0:f.length)||0),i==null||i.insertEmbed((f==null?void 0:f.index)||0,"image",{src:d,alt:"图片"}),i==null||i.setSelection(((f==null?void 0:f.index)||0)+1,0)};function h(d){var S;if(!/^(?:0|(?:-?[1-9]\d*))$/.test(d)){us.error("请输入正确的消息ID");return}const v=i==null?void 0:i.getContents(),E=v==null?void 0:v.ops[0],C=i==null?void 0:i.getSelection();if(E&&typeof E.insert!="string"&&((S=E.insert)!=null&&S.reply)){const M=i==null?void 0:i.getContents();M&&(M.ops[0]={insert:{reply:{messageId:d}}},i==null||i.setContents(M,o.sources.USER))}else i==null||i.insertEmbed(0,"reply",{messageId:d},o.sources.USER);i==null||i.setSelection(((C==null?void 0:C.index)||0)+1,0),i==null||i.blur()}const b=d=>{const f=n.current||(i==null?void 0:i.getSelection());i==null||i.deleteText((f==null?void 0:f.index)||0,(f==null?void 0:f.length)||0),i==null||i.insertEmbed((f==null?void 0:f.index)||0,"emoji",{alt:d.alt,src:d.src,id:d.id}),i==null||i.setSelection(((f==null?void 0:f.index)||0)+1,0)},x=()=>{var E;const d=i==null?void 0:i.getContents();return(((E=d==null?void 0:d.ops)==null?void 0:E.filter(C=>C.insert!==`
`))??[]).map(C=>Uh(C))};return m.jsxs("div",{children:[m.jsx("div",{ref:r,className:"border border-default-200 rounded-md !mb-2 !text-base !h-64"}),m.jsxs("div",{id:"toolbar",className:"!border-none flex gap-2",children:[m.jsx(Gh,{insertImage:u,onOpenChange:a}),m.jsx(Hh,{onInsertEmoji:b,onOpenChange:a}),m.jsx(Wh,{insertReply:h}),m.jsx(Vh,{}),m.jsx($h,{}),m.jsx(Yh,{}),m.jsx(Kh,{}),m.jsx(Fh,{}),m.jsx(Xh,{}),m.jsx(ct,{color:"primary",onPress:()=>{const d=x();t(d)},className:"ml-auto",children:"获取JSON格式"})]})]})};function Dd(){const{isOpen:n,onOpen:t,onOpenChange:e}=co();return m.jsxs(m.Fragment,{children:[m.jsx(ct,{onPress:t,color:"primary",radius:"full",variant:"flat",children:"构造聊天消息"}),m.jsx(uo,{size:"4xl",scrollBehavior:"inside",isOpen:n,onOpenChange:e,children:m.jsx(ho,{children:s=>m.jsxs(m.Fragment,{children:[m.jsx(fo,{className:"flex flex-col gap-1",children:"构造消息"}),m.jsx(po,{className:"overflow-y-auto",children:m.jsx("div",{className:"overflow-y-auto",children:m.jsx(ed,{})})}),m.jsx(go,{children:m.jsx(ct,{color:"primary",onPress:s,variant:"flat",children:"关闭"})})]})})})]})}export{Dd as C,_t as O,kd as a,jd as b,Id as c,Rd as d,Sd as e,_d as f,qd as g,Od as h,Md as i,Bd as q,ca as s};
