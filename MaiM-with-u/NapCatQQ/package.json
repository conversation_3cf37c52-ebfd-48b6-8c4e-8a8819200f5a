{"name": "napcat", "private": true, "type": "module", "version": "4.7.45", "scripts": {"build:universal": "npm run build:webui && vite build --mode universal || exit 1", "build:framework": "npm run build:webui && vite build --mode framework || exit 1", "build:shell": "npm run build:webui && vite build --mode shell || exit 1", "build:webui": "cd napcat.webui && npm run build", "dev:universal": "vite build --mode universal", "dev:framework": "vite build --mode framework", "dev:shell": "vite build --mode shell", "dev:webui": "cd napcat.webui && npm run dev", "lint": "eslint --fix src/**/*.{js,ts,vue}", "depend": "cd dist && npm install --omit=dev", "dev:depend": "npm i && cd napcat.webui && npm i"}, "devDependencies": {"@babel/preset-typescript": "^7.24.7", "@eslint/compat": "^1.2.2", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.14.0", "@homebridge/node-pty-prebuilt-multiarch": "^0.12.0-beta.5", "@log4js-node/log4js-api": "^1.0.2", "@napneko/nap-proto-core": "^0.0.4", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-typescript": "^12.1.2", "@sinclair/typebox": "^0.34.9", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/multer": "^1.4.12", "@types/node": "^22.0.1", "@types/on-finished": "^2.3.4", "@types/qrcode-terminal": "^0.12.2", "@types/react-color": "^3.0.13", "@types/type-is": "^1.6.7", "@types/ws": "^8.5.12", "@typescript-eslint/eslint-plugin": "^8.3.0", "@typescript-eslint/parser": "^8.3.0", "ajv": "^8.13.0", "async-mutex": "^0.5.0", "commander": "^13.0.0", "cors": "^2.8.5", "esbuild": "0.25.0", "eslint": "^9.14.0", "eslint-import-resolver-typescript": "^4.0.0", "eslint-plugin-import": "^2.29.1", "express-rate-limit": "^7.5.0", "fast-xml-parser": "^4.3.6", "file-type": "^20.0.0", "globals": "^16.0.0", "image-size": "^1.1.1", "json5": "^2.2.3", "multer": "^1.4.5-lts.1", "typescript": "^5.3.3", "typescript-eslint": "^8.13.0", "vite": "^6.0.1", "vite-plugin-cp": "^6.0.0", "vite-tsconfig-paths": "^5.1.0", "napcat.protobuf": "^1.1.4", "winston": "^3.17.0", "compressing": "^1.10.1"}, "dependencies": {"express": "^5.0.0", "silk-wasm": "^3.6.1", "ws": "^8.18.0"}}