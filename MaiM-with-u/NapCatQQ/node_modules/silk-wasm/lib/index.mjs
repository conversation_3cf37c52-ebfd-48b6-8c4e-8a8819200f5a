var Module=(()=>{var _scriptName=import.meta.url;return async function(moduleArg={}){var moduleRtn,g=moduleArg,aa,q,ba=new Promise((a,b)=>{aa=a,q=b}),ca=typeof window=="object",da=typeof importScripts=="function",ea=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string"&&process.type!="renderer";if(ea){let{createRequire:a}=await import("module"),b=import.meta.url;b.startsWith("data:")&&(b="/");var require2=a(b)}var fa=Object.assign({},g),r=(a,b)=>{throw b},t="",ha,w;if(ea){var fs=require2("fs"),ia=require2("path");import.meta.url.startsWith("data:")||(t=ia.dirname(require2("url").fileURLToPath(import.meta.url))+"/"),w=a=>(a=y(a)?new URL(a):ia.normalize(a),fs.readFileSync(a)),ha=a=>(a=y(a)?new URL(a):ia.normalize(a),new Promise((b,c)=>{fs.readFile(a,void 0,(d,e)=>{d?c(d):b(e.buffer)})})),process.argv.slice(2),r=(a,b)=>{throw process.exitCode=a,b}}else(ca||da)&&(da?t=self.location.href:typeof document<"u"&&document.currentScript&&(t=document.currentScript.src),_scriptName&&(t=_scriptName),t.startsWith("blob:")?t="":t=t.substr(0,t.replace(/[?#].*/,"").lastIndexOf("/")+1),da&&(w=a=>{var b=new XMLHttpRequest;return b.open("GET",a,!1),b.responseType="arraybuffer",b.send(null),new Uint8Array(b.response)}),ha=a=>y(a)?new Promise((b,c)=>{var d=new XMLHttpRequest;d.open("GET",a,!0),d.responseType="arraybuffer",d.onload=()=>{d.status==200||d.status==0&&d.response?b(d.response):c(d.status)},d.onerror=c,d.send(null)}):fetch(a,{credentials:"same-origin"}).then(b=>b.ok?b.arrayBuffer():Promise.reject(Error(b.status+" : "+b.url))));g.print||console.log.bind(console);var z=g.printErr||console.error.bind(console);Object.assign(g,fa),fa=null;var A=g.wasmBinary,B,C=!1,ja,ka,E,F,G,H,I,la,ma;function na(){var a=B.buffer;g.HEAP8=ka=new Int8Array(a),g.HEAP16=F=new Int16Array(a),g.HEAPU8=E=new Uint8Array(a),g.HEAPU16=G=new Uint16Array(a),g.HEAP32=H=new Int32Array(a),g.HEAPU32=I=new Uint32Array(a),g.HEAPF32=la=new Float32Array(a),g.HEAPF64=ma=new Float64Array(a)}var oa=[],pa=[],qa=[];function ra(){var a=g.preRun;a&&(typeof a=="function"&&(a=[a]),a.forEach(sa)),ta(oa)}function sa(a){oa.unshift(a)}function ua(a){qa.unshift(a)}var J=0,wa=null,K=null;function xa(a){throw g.onAbort?.(a),a="Aborted("+a+")",z(a),C=!0,a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info."),q(a),a}var ya=a=>a.startsWith("data:application/octet-stream;base64,"),y=a=>a.startsWith("file://"),za;function Aa(a){if(a==za&&A)return new Uint8Array(A);if(w)return w(a);throw"both async and sync fetching of the wasm failed"}function Ba(a){return A?Promise.resolve().then(()=>Aa(a)):ha(a).then(b=>new Uint8Array(b),()=>Aa(a))}function Ca(a,b,c){return Ba(a).then(d=>WebAssembly.instantiate(d,b)).then(c,d=>{z(`failed to asynchronously prepare wasm: ${d}`),xa(d)})}function Da(a,b){var c=za;return A||typeof WebAssembly.instantiateStreaming!="function"||ya(c)||y(c)||ea||typeof fetch!="function"?Ca(c,a,b):fetch(c,{credentials:"same-origin"}).then(d=>WebAssembly.instantiateStreaming(d,a).then(b,function(e){return z(`wasm streaming compile failed: ${e}`),z("falling back to ArrayBuffer instantiation"),Ca(c,a,b)}))}function Ea(a){this.name="ExitStatus",this.message=`Program terminated with exit(${a})`,this.status=a}var ta=a=>{a.forEach(b=>b(g))},Fa=g.noExitRuntime||!0;class Ga{constructor(a){this.I=a-24}}var Ha=0,Ia=0,Ja,L=a=>{for(var b="";E[a];)b+=Ja[E[a++]];return b},O={},P={},Q={},R,Ka=a=>{throw new R(a)},La,Ma=(a,b)=>{function c(f){if(f=b(f),f.length!==d.length)throw new La("Mismatched type converter count");for(var m=0;m<d.length;++m)S(d[m],f[m])}var d=[];d.forEach(f=>Q[f]=a);var e=Array(a.length),h=[],l=0;a.forEach((f,m)=>{P.hasOwnProperty(f)?e[m]=P[f]:(h.push(f),O.hasOwnProperty(f)||(O[f]=[]),O[f].push(()=>{e[m]=P[f],++l,l===h.length&&c(e)}))}),h.length===0&&c(e)};function Na(a,b,c={}){var d=b.name;if(!a)throw new R(`type "${d}" must have a positive integer typeid pointer`);if(P.hasOwnProperty(a)){if(c.K)return;throw new R(`Cannot register type '${d}' twice`)}P[a]=b,delete Q[a],O.hasOwnProperty(a)&&(b=O[a],delete O[a],b.forEach(e=>e()))}function S(a,b,c={}){return Na(a,b,c)}var Oa=[],T=[],Pa=a=>{9<a&&--T[a+1]===0&&(T[a]=void 0,Oa.push(a))},Qa=a=>{if(!a)throw new R("Cannot use deleted val. handle = "+a);return T[a]},Ra=a=>{switch(a){case void 0:return 2;case null:return 4;case!0:return 6;case!1:return 8;default:let b=Oa.pop()||T.length;return T[b]=a,T[b+1]=1,b}};function Sa(a){return this.fromWireType(I[a>>2])}var Ta={name:"emscripten::val",fromWireType:a=>{var b=Qa(a);return Pa(a),b},toWireType:(a,b)=>Ra(b),H:8,readValueFromPointer:Sa,G:null},Ua=(a,b)=>{switch(b){case 4:return function(c){return this.fromWireType(la[c>>2])};case 8:return function(c){return this.fromWireType(ma[c>>3])};default:throw new TypeError(`invalid float width (${b}): ${a}`)}},U=(a,b)=>Object.defineProperty(b,"name",{value:a}),Va=a=>{for(;a.length;){var b=a.pop();a.pop()(b)}};function Wa(a){for(var b=1;b<a.length;++b)if(a[b]!==null&&a[b].G===void 0)return!0;return!1}function $a(a){var b=Function;if(!(b instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof b} which is not a function`);var c=U(b.name||"unknownFunctionName",function(){});return c.prototype=b.prototype,c=new c,a=b.apply(c,a),a instanceof Object?a:c}for(var ab=(a,b)=>{if(g[a].F===void 0){var c=g[a];g[a]=function(...d){if(!g[a].F.hasOwnProperty(d.length))throw new R(`Function '${b}' called with an invalid number of arguments (${d.length}) - expects one of (${g[a].F})!`);return g[a].F[d.length].apply(this,d)},g[a].F=[],g[a].F[c.J]=c}},bb=(a,b,c)=>{if(g.hasOwnProperty(a)){if(c===void 0||g[a].F!==void 0&&g[a].F[c]!==void 0)throw new R(`Cannot register public name '${a}' twice`);if(ab(a,a),g[a].F.hasOwnProperty(c))throw new R(`Cannot register multiple overloads of a function with the same number of arguments (${c})!`);g[a].F[c]=b}else g[a]=b,g[a].J=c},cb=(a,b)=>{for(var c=[],d=0;d<a;d++)c.push(I[b+4*d>>2]);return c},db,eb=(a,b,c=[])=>(a.includes("j")?(a=a.replace(/p/g,"i"),b=(0,g["dynCall_"+a])(b,...c)):b=db.get(b)(...c),b),fb=(a,b)=>(...c)=>eb(a,b,c),gb=(a,b)=>{a=L(a);var c=a.includes("j")?fb(a,b):db.get(b);if(typeof c!="function")throw new R(`unknown function pointer with signature ${a}: ${b}`);return c},hb,jb=a=>{a=ib(a);var b=L(a);return V(a),b},kb=(a,b)=>{function c(h){e[h]||P[h]||(Q[h]?Q[h].forEach(c):(d.push(h),e[h]=!0))}var d=[],e={};throw b.forEach(c),new hb(`${a}: `+d.map(jb).join([", "]))},lb=a=>{a=a.trim();let b=a.indexOf("(");return b!==-1?a.substr(0,b):a},mb=(a,b,c)=>{switch(b){case 1:return c?d=>ka[d]:d=>E[d];case 2:return c?d=>F[d>>1]:d=>G[d>>1];case 4:return c?d=>H[d>>2]:d=>I[d>>2];default:throw new TypeError(`invalid integer width (${b}): ${a}`)}},nb=typeof TextDecoder<"u"?new TextDecoder:void 0,ob=(a=0,b=NaN)=>{var c=E,d=a+b;for(b=a;c[b]&&!(b>=d);)++b;if(16<b-a&&c.buffer&&nb)return nb.decode(c.subarray(a,b));for(d="";a<b;){var e=c[a++];if(e&128){var h=c[a++]&63;if((e&224)==192)d+=String.fromCharCode((e&31)<<6|h);else{var l=c[a++]&63;e=(e&240)==224?(e&15)<<12|h<<6|l:(e&7)<<18|h<<12|l<<6|c[a++]&63,65536>e?d+=String.fromCharCode(e):(e-=65536,d+=String.fromCharCode(55296|e>>10,56320|e&1023))}}else d+=String.fromCharCode(e)}return d},pb=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0,qb=(a,b)=>{for(var c=a>>1,d=c+b/2;!(c>=d)&&G[c];)++c;if(c<<=1,32<c-a&&pb)return pb.decode(E.subarray(a,c));for(c="",d=0;!(d>=b/2);++d){var e=F[a+2*d>>1];if(e==0)break;c+=String.fromCharCode(e)}return c},rb=(a,b,c)=>{if(c??=2147483647,2>c)return 0;c-=2;var d=b;c=c<2*a.length?c/2:a.length;for(var e=0;e<c;++e)F[b>>1]=a.charCodeAt(e),b+=2;return F[b>>1]=0,b-d},sb=a=>2*a.length,tb=(a,b)=>{for(var c=0,d="";!(c>=b/4);){var e=H[a+4*c>>2];if(e==0)break;++c,65536<=e?(e-=65536,d+=String.fromCharCode(55296|e>>10,56320|e&1023)):d+=String.fromCharCode(e)}return d},ub=(a,b,c)=>{if(c??=2147483647,4>c)return 0;var d=b;c=d+c-4;for(var e=0;e<a.length;++e){var h=a.charCodeAt(e);if(55296<=h&&57343>=h){var l=a.charCodeAt(++e);h=65536+((h&1023)<<10)|l&1023}if(H[b>>2]=h,b+=4,b+4>c)break}return H[b>>2]=0,b-d},vb=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);55296<=d&&57343>=d&&++c,b+=4}return b},wb=[],xb=a=>{var b=wb.length;return wb.push(a),b},yb=(a,b)=>{var c=P[a];if(c===void 0)throw a=`${b} has unknown type ${jb(a)}`,new R(a);return c},zb=(a,b)=>{for(var c=Array(a),d=0;d<a;++d)c[d]=yb(I[b+4*d>>2],"parameter "+d);return c},Ab=(a,b,c)=>{var d=[];return a=a.toWireType(d,c),d.length&&(I[b>>2]=Ra(d)),a},W={},Bb=0,Cb=a=>{ja=a,Fa||0<Bb||(g.onExit?.(a),C=!0),r(a,new Ea(a))},Db=a=>{if(!C)try{if(a(),!(Fa||0<Bb))try{ja=a=ja,Cb(a)}catch(b){b instanceof Ea||b=="unwind"||r(1,b)}}catch(b){b instanceof Ea||b=="unwind"||r(1,b)}},Eb=Array(256),X=0;256>X;++X)Eb[X]=String.fromCharCode(X);Ja=Eb,R=g.BindingError=class extends Error{constructor(a){super(a),this.name="BindingError"}},La=g.InternalError=class extends Error{constructor(a){super(a),this.name="InternalError"}},T.push(0,1,void 0,1,null,1,!0,1,!1,1),g.count_emval_handles=()=>T.length/2-5-Oa.length,hb=g.UnboundTypeError=((a,b)=>{var c=U(b,function(d){this.name=b,this.message=d,d=Error(d).stack,d!==void 0&&(this.stack=this.toString()+`
`+d.replace(/^Error(:[^\n]*)?\n/,""))});return c.prototype=Object.create(a.prototype),c.prototype.constructor=c,c.prototype.toString=function(){return this.message===void 0?this.name:`${this.name}: ${this.message}`},c})(Error,"UnboundTypeError");var Hb={r:(a,b,c)=>{var d=new Ga(a);throw I[d.I+16>>2]=0,I[d.I+4>>2]=b,I[d.I+8>>2]=c,Ha=a,Ia++,Ha},s:()=>{xa("")},n:()=>{},j:(a,b,c,d)=>{b=L(b),S(a,{name:b,fromWireType:function(e){return!!e},toWireType:function(e,h){return h?c:d},H:8,readValueFromPointer:function(e){return this.fromWireType(E[e])},G:null})},i:a=>S(a,Ta),g:(a,b,c)=>{b=L(b),S(a,{name:b,fromWireType:d=>d,toWireType:(d,e)=>e,H:8,readValueFromPointer:Ua(b,c),G:null})},h:(a,b,c,d,e,h,l)=>{var f=cb(b,c);a=L(a),a=lb(a),e=gb(d,e),bb(a,function(){kb(`Cannot call ${a} due to unbound types`,f)},b-1),Ma(f,m=>{var k=[m[0],null].concat(m.slice(1));m=a;var n=a,v=e,p=k.length;if(2>p)throw new R("argTypes array size mismatch! Must at least get return value and 'this' types!");var D=k[1]!==null&&!1,M=Wa(k),Xa=k[0].name!=="void";v=[n,Ka,v,h,Va,k[0],k[1]];for(var x=0;x<p-2;++x)v.push(k[x+2]);if(!M)for(x=D?1:2;x<k.length;++x)k[x].G!==null&&v.push(k[x].G);M=Wa(k),x=k.length-2;var u=[],N=["fn"];for(D&&N.push("thisWired"),p=0;p<x;++p)u.push(`arg${p}`),N.push(`arg${p}Wired`);u=u.join(","),N=N.join(","),u=`return function (${u}) {
`,M&&(u+=`var destructors = [];
`);var Ya=M?"destructors":"null",va="humanName throwBindingError invoker fn runDestructors retType classParam".split(" ");for(D&&(u+=`var thisWired = classParam['toWireType'](${Ya}, this);
`),p=0;p<x;++p)u+=`var arg${p}Wired = argType${p}['toWireType'](${Ya}, arg${p});
`,va.push(`argType${p}`);if(u+=(Xa||l?"var rv = ":"")+`invoker(${N});
`,M)u+=`runDestructors(destructors);
`;else for(p=D?1:2;p<k.length;++p)D=p===1?"thisWired":"arg"+(p-2)+"Wired",k[p].G!==null&&(u+=`${D}_dtor(${D});
`,va.push(`${D}_dtor`));Xa&&(u+=`var ret = retType['fromWireType'](rv);
return ret;
`);let[Za,Jb]=[va,u+`}
`];if(Za.push(Jb),k=$a(Za)(...v),n=U(n,k),k=b-1,!g.hasOwnProperty(m))throw new La("Replacing nonexistent public symbol");return g[m].F!==void 0&&k!==void 0?g[m].F[k]=n:(g[m]=n,g[m].J=k),[]})},b:(a,b,c,d,e)=>{if(b=L(b),e===-1&&(e=4294967295),e=f=>f,d===0){var h=32-8*c;e=f=>f<<h>>>h}var l=b.includes("unsigned")?function(f,m){return m>>>0}:function(f,m){return m};S(a,{name:b,fromWireType:e,toWireType:l,H:8,readValueFromPointer:mb(b,c,d!==0),G:null})},a:(a,b,c)=>{function d(h){return new e(ka.buffer,I[h+4>>2],I[h>>2])}var e=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][b];c=L(c),S(a,{name:c,fromWireType:d,H:8,readValueFromPointer:d},{K:!0})},e:(a,b)=>{b=L(b);var c=b==="std::string";S(a,{name:b,fromWireType:function(d){var e=I[d>>2],h=d+4;if(c)for(var l=h,f=0;f<=e;++f){var m=h+f;if(f==e||E[m]==0){if(l=l?ob(l,m-l):"",k===void 0)var k=l;else k+="\0",k+=l;l=m+1}}else{for(k=Array(e),f=0;f<e;++f)k[f]=String.fromCharCode(E[h+f]);k=k.join("")}return V(d),k},toWireType:function(d,e){e instanceof ArrayBuffer&&(e=new Uint8Array(e));var h,l=typeof e=="string";if(!(l||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int8Array))throw new R("Cannot pass non-string to std::string");var f;if(c&&l)for(h=f=0;h<e.length;++h){var m=e.charCodeAt(h);127>=m?f++:2047>=m?f+=2:55296<=m&&57343>=m?(f+=4,++h):f+=3}else f=e.length;if(h=f,f=Fb(4+h+1),m=f+4,I[f>>2]=h,c&&l){if(l=m,m=h+1,h=E,0<m){m=l+m-1;for(var k=0;k<e.length;++k){var n=e.charCodeAt(k);if(55296<=n&&57343>=n){var v=e.charCodeAt(++k);n=65536+((n&1023)<<10)|v&1023}if(127>=n){if(l>=m)break;h[l++]=n}else{if(2047>=n){if(l+1>=m)break;h[l++]=192|n>>6}else{if(65535>=n){if(l+2>=m)break;h[l++]=224|n>>12}else{if(l+3>=m)break;h[l++]=240|n>>18,h[l++]=128|n>>12&63}h[l++]=128|n>>6&63}h[l++]=128|n&63}}h[l]=0}}else if(l)for(l=0;l<h;++l){if(k=e.charCodeAt(l),255<k)throw V(m),new R("String has UTF-16 code units that do not fit in 8 bits");E[m+l]=k}else for(l=0;l<h;++l)E[m+l]=e[l];return d!==null&&d.push(V,f),f},H:8,readValueFromPointer:Sa,G(d){V(d)}})},d:(a,b,c)=>{if(c=L(c),b===2)var d=qb,e=rb,h=sb,l=f=>G[f>>1];else b===4&&(d=tb,e=ub,h=vb,l=f=>I[f>>2]);S(a,{name:c,fromWireType:f=>{for(var m=I[f>>2],k,n=f+4,v=0;v<=m;++v){var p=f+4+v*b;(v==m||l(p)==0)&&(n=d(n,p-n),k===void 0?k=n:(k+="\0",k+=n),n=p+b)}return V(f),k},toWireType:(f,m)=>{if(typeof m!="string")throw new R(`Cannot pass non-string to C++ string type ${c}`);var k=h(m),n=Fb(4+k+b);return I[n>>2]=k/b,e(m,n+4,k+b),f!==null&&f.push(V,n),n},H:8,readValueFromPointer:Sa,G(f){V(f)}})},l:a=>{S(a,Ta)},k:(a,b)=>{b=L(b),S(a,{L:!0,name:b,H:0,fromWireType:()=>{},toWireType:()=>{}})},u:(a,b,c)=>E.copyWithin(a,b,b+c),p:()=>{Fa=!1,Bb=0},x:(a,b,c,d)=>(a=wb[a],b=Qa(b),a(null,b,c,d)),c:Pa,w:(a,b,c)=>{b=zb(a,b);var d=b.shift();a--;var e=`return function (obj, func, destructorsRef, args) {
`,h=0,l=[];c===0&&l.push("obj");for(var f=["retType"],m=[d],k=0;k<a;++k)l.push("arg"+k),f.push("argType"+k),m.push(b[k]),e+=`  var arg${k} = argType${k}.readValueFromPointer(args${h?"+"+h:""});
`,h+=b[k].H;return e+=`  var rv = ${c===1?"new func":"func.call"}(${l.join(", ")});
`,d.L||(f.push("emval_returnValue"),m.push(Ab),e+=`  return emval_returnValue(retType, destructorsRef, rv);
`),f.push(e+`};
`),a=$a(f)(...m),c=`methodCaller<(${b.map(n=>n.name).join(", ")}) => ${d.name}>`,xb(U(c,a))},m:a=>{9<a&&(T[a+1]+=1)},v:a=>{var b=Qa(a);Va(b),Pa(a)},f:(a,b)=>(a=yb(a,"_emval_take_value"),a=a.readValueFromPointer(b),Ra(a)),q:(a,b)=>{if(W[a]&&(clearTimeout(W[a].id),delete W[a]),!b)return 0;var c=setTimeout(()=>{delete W[a],Db(()=>Gb(a,performance.now()))},b);return W[a]={id:c,M:b},0},t:a=>{var b=E.length;if(a>>>=0,2147483648<a)return!1;for(var c=1;4>=c;c*=2){var d=b*(1+.2/c);d=Math.min(d,a+100663296);a:{d=(Math.min(2147483648,65536*Math.ceil(Math.max(a,d)/65536))-B.buffer.byteLength+65535)/65536|0;try{B.grow(d),na();var e=1;break a}catch{}e=void 0}if(e)return!0}return!1},o:Cb},Y=function(){function a(c){return Y=c.exports,B=Y.y,na(),db=Y.E,pa.unshift(Y.z),J--,g.monitorRunDependencies?.(J),J==0&&(wa!==null&&(clearInterval(wa),wa=null),K&&(c=K,K=null,c())),Y}var b={a:Hb};if(J++,g.monitorRunDependencies?.(J),g.instantiateWasm)try{return g.instantiateWasm(b,a)}catch(c){z(`Module.instantiateWasm callback failed with error: ${c}`),q(c)}return za??=g.locateFile?ya("silk.wasm")?"silk.wasm":g.locateFile?g.locateFile("silk.wasm",t):t+"silk.wasm":new URL("silk.wasm",import.meta.url).href,Da(b,function(c){a(c.instance)}).catch(q),{}}(),ib=a=>(ib=Y.A)(a),Fb=a=>(Fb=Y.B)(a),V=a=>(V=Y.C)(a),Gb=(a,b)=>(Gb=Y.D)(a,b),Z,Ib;K=function Kb(){Z||Lb(),Z||(K=Kb)};function Lb(){function a(){if(!Z&&(Z=1,g.calledRun=1,!C)){ta(pa),aa(g),g.onRuntimeInitialized?.();var b=g.postRun;b&&(typeof b=="function"&&(b=[b]),b.forEach(ua)),ta(qa)}}if(!(0<J)){if(!Ib&&(Ib=1,ra(),0<J))return;g.setStatus?(g.setStatus("Running..."),setTimeout(()=>{setTimeout(()=>g.setStatus(""),1),a()},1)):a()}}if(g.preInit)for(typeof g.preInit=="function"&&(g.preInit=[g.preInit]);0<g.preInit.length;)g.preInit.pop()();return Lb(),moduleRtn=ba,moduleRtn}})(),silk_default=Module;function isWavFile(fileData){try{let chunks=unpackWavFileChunks(fileData),fmt=decodeFormatChunk(chunks.get("fmt")),data=chunks.get("data");return getWavFileType(fmt),verifyDataChunkLength(data,fmt),!0}catch{return!1}}var audioEncodingNames=["int","float"],wavFileTypeAudioEncodings=[0,0,0,1];function decodeWavFile(fileData){let chunks=unpackWavFileChunks(fileData),fmt=decodeFormatChunk(chunks.get("fmt")),data=chunks.get("data"),wavFileType=getWavFileType(fmt),audioEncoding=wavFileTypeAudioEncodings[wavFileType],wavFileTypeName=audioEncodingNames[audioEncoding]+fmt.bitsPerSample;return verifyDataChunkLength(data,fmt),{channelData:decodeDataChunk(data,fmt,wavFileType),sampleRate:fmt.sampleRate,numberOfChannels:fmt.numberOfChannels,audioEncoding,bitsPerSample:fmt.bitsPerSample,wavFileTypeName}}function unpackWavFileChunks(fileData){let dataView;fileData instanceof ArrayBuffer?dataView=new DataView(fileData):dataView=new DataView(fileData.buffer,fileData.byteOffset,fileData.byteLength);let fileLength=dataView.byteLength;if(fileLength<20)throw new Error("WAV file is too short.");if(getString(dataView,0,4)!="RIFF")throw new Error("Not a valid WAV file (no RIFF header).");let mainChunkLength=dataView.getUint32(4,!0);if(8+mainChunkLength!=fileLength)throw new Error(`Main chunk length of WAV file (${8+mainChunkLength}) does not match file size (${fileLength}).`);if(getString(dataView,8,4)!="WAVE")throw new Error("RIFF file is not a WAV file.");let chunks=new Map,fileOffset=12;for(;fileOffset<fileLength;){if(fileOffset+8>fileLength)throw new Error(`Incomplete chunk prefix in WAV file at offset ${fileOffset}.`);let chunkId=getString(dataView,fileOffset,4).trim(),chunkLength=dataView.getUint32(fileOffset+4,!0);if(fileOffset+8+chunkLength>fileLength)throw new Error(`Incomplete chunk data in WAV file at offset ${fileOffset}.`);let chunkData=new DataView(dataView.buffer,dataView.byteOffset+fileOffset+8,chunkLength);chunks.set(chunkId,chunkData);let padLength=chunkLength%2;fileOffset+=8+chunkLength+padLength}return chunks}function getString(dataView,offset,length){let a=new Uint8Array(dataView.buffer,dataView.byteOffset+offset,length);return String.fromCharCode.apply(null,a)}function getInt24(dataView,offset){let b0=dataView.getInt8(offset+2)*65536,b12=dataView.getUint16(offset,!0);return b0+b12}function decodeFormatChunk(dataView){if(!dataView)throw new Error("No format chunk found in WAV file.");if(dataView.byteLength<16)throw new Error("Format chunk of WAV file is too short.");let fmt={};return fmt.formatCode=dataView.getUint16(0,!0),fmt.numberOfChannels=dataView.getUint16(2,!0),fmt.sampleRate=dataView.getUint32(4,!0),fmt.bytesPerSec=dataView.getUint32(8,!0),fmt.bytesPerFrame=dataView.getUint16(12,!0),fmt.bitsPerSample=dataView.getUint16(14,!0),fmt}function getWavFileType(fmt){if(fmt.numberOfChannels<1||fmt.numberOfChannels>999)throw new Error("Invalid number of channels in WAV file.");let bytesPerSample=Math.ceil(fmt.bitsPerSample/8),expectedBytesPerFrame=fmt.numberOfChannels*bytesPerSample;if(fmt.formatCode==1&&fmt.bitsPerSample>=1&&fmt.bitsPerSample<=8&&fmt.bytesPerFrame==expectedBytesPerFrame)return 0;if(fmt.formatCode==1&&fmt.bitsPerSample>=9&&fmt.bitsPerSample<=16&&fmt.bytesPerFrame==expectedBytesPerFrame)return 1;if(fmt.formatCode==1&&fmt.bitsPerSample>=17&&fmt.bitsPerSample<=24&&fmt.bytesPerFrame==expectedBytesPerFrame)return 2;if(fmt.formatCode==3&&fmt.bitsPerSample==32&&fmt.bytesPerFrame==expectedBytesPerFrame)return 3;throw new Error(`Unsupported WAV file type, formatCode=${fmt.formatCode}, bitsPerSample=${fmt.bitsPerSample}, bytesPerFrame=${fmt.bytesPerFrame}, numberOfChannels=${fmt.numberOfChannels}.`)}function decodeDataChunk(data,fmt,wavFileType){switch(wavFileType){case 0:return decodeDataChunk_uint8(data,fmt);case 1:return decodeDataChunk_int16(data,fmt);case 2:return decodeDataChunk_int24(data,fmt);case 3:return decodeDataChunk_float32(data,fmt);default:throw new Error("No decoder.")}}function decodeDataChunk_int16(data,fmt){let channelData=allocateChannelDataArrays(data.byteLength,fmt),numberOfChannels=fmt.numberOfChannels,numberOfFrames=channelData[0].length,offs=0;for(let frameNo=0;frameNo<numberOfFrames;frameNo++)for(let channelNo=0;channelNo<numberOfChannels;channelNo++){let sampleValueFloat=data.getInt16(offs,!0)/32768;channelData[channelNo][frameNo]=sampleValueFloat,offs+=2}return channelData}function decodeDataChunk_uint8(data,fmt){let channelData=allocateChannelDataArrays(data.byteLength,fmt),numberOfChannels=fmt.numberOfChannels,numberOfFrames=channelData[0].length,offs=0;for(let frameNo=0;frameNo<numberOfFrames;frameNo++)for(let channelNo=0;channelNo<numberOfChannels;channelNo++){let sampleValueFloat=(data.getUint8(offs)-128)/128;channelData[channelNo][frameNo]=sampleValueFloat,offs+=1}return channelData}function decodeDataChunk_int24(data,fmt){let channelData=allocateChannelDataArrays(data.byteLength,fmt),numberOfChannels=fmt.numberOfChannels,numberOfFrames=channelData[0].length,offs=0;for(let frameNo=0;frameNo<numberOfFrames;frameNo++)for(let channelNo=0;channelNo<numberOfChannels;channelNo++){let sampleValueFloat=getInt24(data,offs)/8388608;channelData[channelNo][frameNo]=sampleValueFloat,offs+=3}return channelData}function decodeDataChunk_float32(data,fmt){let channelData=allocateChannelDataArrays(data.byteLength,fmt),numberOfChannels=fmt.numberOfChannels,numberOfFrames=channelData[0].length,offs=0;for(let frameNo=0;frameNo<numberOfFrames;frameNo++)for(let channelNo=0;channelNo<numberOfChannels;channelNo++){let sampleValueFloat=data.getFloat32(offs,!0);channelData[channelNo][frameNo]=sampleValueFloat,offs+=4}return channelData}function allocateChannelDataArrays(dataLength,fmt){let numberOfFrames=Math.floor(dataLength/fmt.bytesPerFrame),channelData=new Array(fmt.numberOfChannels);for(let channelNo=0;channelNo<fmt.numberOfChannels;channelNo++)channelData[channelNo]=new Float32Array(numberOfFrames);return channelData}function verifyDataChunkLength(data,fmt){if(!data)throw new Error("No data chunk found in WAV file.");if(data.byteLength%fmt.bytesPerFrame!=0)throw new Error("WAV file data chunk length is not a multiple of frame size.")}function getWavFileInfo(fileData){let chunks=unpackWavFileChunks(fileData),chunkInfo=getChunkInfo(chunks),fmt=decodeFormatChunk(chunks.get("fmt"));return{chunkInfo,fmt}}function getChunkInfo(chunks){let chunkInfo=[];for(let e of chunks){let ci={};ci.chunkId=e[0],ci.dataOffset=e[1].byteOffset,ci.dataLength=e[1].byteLength,chunkInfo.push(ci)}return chunkInfo.sort((e1,e2)=>e1.dataOffset-e2.dataOffset),chunkInfo}function ensureMonoPcm(channelData){let{length:numberOfChannels}=channelData;if(numberOfChannels===1)return channelData[0];let monoData=new Float32Array(channelData[0].length);for(let i=0;i<monoData.length;i++){let sum=0;for(let j=0;j<numberOfChannels;j++)sum+=channelData[j][i];monoData[i]=sum/numberOfChannels}return monoData}function ensureS16lePcm(input){let fileLength=input.length*2,arrayBuffer=new ArrayBuffer(fileLength),int16Array=new Int16Array(arrayBuffer);for(let offset=0;offset<input.length;offset++){let x=~~(input[offset]*32768);int16Array[offset]=x>32767?32767:x}return arrayBuffer}function toUTF8String(input,start=0,end=input.byteLength){return new TextDecoder().decode(input.slice(start,end))}function binaryFromSource(source){return ArrayBuffer.isView(source)?source.buffer.slice(source.byteOffset,source.byteOffset+source.byteLength):source}async function encode(input,sampleRate){let instance=await silk_default(),buffer=binaryFromSource(input);if(!buffer?.byteLength)throw new Error("input data length is 0");if(isWavFile(input)){let{channelData,sampleRate:wavSampleRate}=decodeWavFile(input);sampleRate||=wavSampleRate,buffer=ensureS16lePcm(ensureMonoPcm(channelData))}let data=new Uint8Array,duration=instance.silk_encode(buffer,sampleRate,output=>{data=output.slice()});if(duration===0)throw new Error("silk encoding failure");return{data,duration}}async function decode(input,sampleRate){let instance=await silk_default(),buffer=binaryFromSource(input);if(!buffer?.byteLength)throw new Error("input data length is 0");let data=new Uint8Array,duration=instance.silk_decode(buffer,sampleRate,output=>{output.length>0&&(data=output.slice())});if(duration===0)throw new Error("silk decoding failure");return{data,duration}}function getDuration(data,frameMs=20){let buffer=binaryFromSource(data),view=new DataView(buffer),byteLength=view.byteLength,offset=view.getUint8(0)===2?10:9,blocks=0;for(;offset<byteLength;){let size=view.getUint16(offset,!0);blocks+=1,offset+=size+2}return blocks*frameMs}function isWav(data){return isWavFile(data)}function getWavFileInfo2(data){return getWavFileInfo(data)}function isSilk(data){let buffer=binaryFromSource(data);return buffer.byteLength<7?!1:toUTF8String(buffer,0,7).includes("#!SILK")}export{decode,encode,getDuration,getWavFileInfo2 as getWavFileInfo,isSilk,isWav};
