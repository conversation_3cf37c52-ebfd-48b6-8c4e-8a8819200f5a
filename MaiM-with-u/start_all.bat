@echo off
rem ---------- 设置项目根目录 ----------
set ROOT=%~dp0

rem ---------- 启动 MaiBot ----------
echo 启动 MaiBot...
start "MaiBot" cmd /k ^
    cd /d "%ROOT%MaiBot" ^&^& ^
    call "%ROOT%MaiBotEnv\Scripts\activate.bat" ^&^& ^
    python bot.py

rem ---------- 启动 Napcat-Adapter ----------
echo 启动 Napcat-Adapter...
start "Adapter" cmd /k ^
    cd /d "%ROOT%MaiBot-Napcat-Adapter" ^&^& ^
    call "%ROOT%MaiBotEnv\Scripts\activate.bat" ^&^& ^
    python main.py

echo 全部服务已启动！
pause
