<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>元素火柴人激斗</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #222;
            color: white;
            font-family: sans-serif;
        }
        #gameArea {
            position: relative;
            border: 1px solid #555;
            background-color: #111;
        }
        canvas {
            display: block;
        }
        #ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            padding: 10px;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
        }
        .player-stats {
            width: 45%;
        }
        .bar-container {
            background-color: #333;
            border: 1px solid #666;
        }
        .hp-bar, .mp-bar {
            height: 15px;
            transition: width 0.3s ease;
        }
        .hp-bar {
            background-color: #e74c3c;
        }
        .mp-bar {
            background-color: #3498db;
            height: 10px;
        }
        #p2Stats {
            text-align: right;
        }
        #instructions {
            position: absolute;
            bottom: 10px;
            left: 10px;
            font-size: 12px;
            color: #aaa;
        }
        #gameOverScreen {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 32px;
            color: white;
            display: none;
            text-shadow: 2px 2px 4px black;
        }
    </style>
</head>
<body>
    <div id="gameArea">
        <canvas id="gameCanvas" width="1000" height="600"></canvas>
        <div id="ui">
            <div class="player-stats" id="p1Stats">
                <div>Player 1 (你)</div>
                <div class="bar-container"><div id="p1HP" class="hp-bar" style="width: 100%;"></div></div>
                <div class="bar-container"><div id="p1MP" class="mp-bar" style="width: 100%;"></div></div>
            </div>
            <div class="player-stats" id="p2Stats">
                <div>Player 2 (AI)</div>
                <div class="bar-container"><div id="p2HP" class="hp-bar" style="width: 100%;"></div></div>
                <div class="bar-container"><div id="p2MP" class="mp-bar" style="width: 100%;"></div></div>
            </div>
        </div>
        <div id="instructions">
            A/D: 移动 | W: 二段跳 | S: 格挡 | J: 普攻 | K: 闪避 | 1-5: 元素技能
        </div>
        <div id="gameOverScreen"></div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const CANVAS_WIDTH = canvas.width;
        const CANVAS_HEIGHT = canvas.height;
        const GROUND_Y = CANVAS_HEIGHT - 50;
        const GRAVITY = 0.6;

        let keys = {};
        let projectiles = [];
        let gameover = false;

        // --- 辅助函数 ---
        function clamp(value, min, max) {
            return Math.max(min, Math.min(max, value));
        }

        // --- Fighter 类 (核心逻辑) ---
        class Fighter {
            constructor(x, color, controls, isAI = false) {
                this.x = x;
                this.y = GROUND_Y;
                this.w = 40; // Hitbox width
                this.h = 80; // Hitbox height
                this.vx = 0;
                this.vy = 0;
                this.speed = 5;
                this.jumpForce = -12;
                this.color = color;
                this.controls = controls;
                this.isAI = isAI;

                this.maxHp = 200;
                this.hp = this.maxHp;
                this.maxMp = 100;
                this.mp = this.maxMp;

                this.facing = (x < CANVAS_WIDTH / 2) ? 1 : -1; // 1: right, -1: left

                // 状态
                this.onGround = false;
                this.jumpsRemaining = 2; // 二段跳
                this.isAttacking = false;
                this.isBlocking = false;
                this.isDodging = false;
                this.isStunned = false;
                this.invincible = false;
                this.defenseBoost = 0;

                // 冷却和计时器
                this.attackCooldown = 0;
                this.dodgeCooldown = 0;
                this.stunTimer = 0;
                this.defenseBoostTimer = 0;
                this.actionTimer = 0; // 用于AI决策
            }

            // 绘制火柴人
            draw() {
                ctx.save();
                ctx.translate(this.x, this.y);

                // 状态视觉效果
                if (this.invincible) {
                    ctx.globalAlpha = 0.5;
                }
                if (this.isStunned) {
                    ctx.fillStyle = 'yellow';
                    ctx.fillText('💫', 0, -this.h - 5);
                }
                if (this.defenseBoost > 0) {
                    ctx.strokeStyle = '#8B4513'; // 土元素颜色
                    ctx.lineWidth = 3;
                    ctx.strokeRect(-this.w/2 - 5, -this.h, this.w + 10, this.h);
                }

                ctx.strokeStyle = this.color;
                ctx.lineWidth = 4;

                // Head
                ctx.beginPath();
                ctx.arc(0, -this.h + 15, 15, 0, Math.PI * 2);
                ctx.stroke();

                // Body
                ctx.beginPath();
                ctx.moveTo(0, -this.h + 30);
                ctx.lineTo(0, -this.h + 60);
                ctx.stroke();

                // Arms
                ctx.beginPath();
                if (this.isBlocking) {
                    // 格挡姿势
                    ctx.moveTo(this.facing * 5, -this.h + 40);
                    ctx.lineTo(this.facing * 20, -this.h + 30);
                    ctx.lineTo(this.facing * 20, -this.h + 50);
                } else if (this.isAttacking) {
                    // 攻击姿势
                    ctx.moveTo(0, -this.h + 40);
                    ctx.lineTo(this.facing * 35, -this.h + 40);
                } else {
                    // 默认姿势
                    ctx.moveTo(-15, -this.h + 40);
                    ctx.lineTo(15, -this.h + 40);
                }
                ctx.stroke();

                // Legs
                ctx.beginPath();
                ctx.moveTo(0, -this.h + 60);
                ctx.lineTo(-15, 0);
                ctx.moveTo(0, -this.h + 60);
                ctx.lineTo(15, 0);
                ctx.stroke();

                ctx.restore();
            }

            // 更新物理和状态
            update(opponent) {
                // 冷却和状态恢复
                if (this.attackCooldown > 0) this.attackCooldown--;
                if (this.dodgeCooldown > 0) this.dodgeCooldown--;
                if (this.mp < this.maxMp) this.mp += 0.05; // MP恢复

                if (this.defenseBoostTimer > 0) {
                    this.defenseBoostTimer--;
                    if (this.defenseBoostTimer === 0) this.defenseBoost = 0;
                }

                if (this.stunTimer > 0) {
                    this.stunTimer--;
                    this.isStunned = this.stunTimer > 0;
                }

                // AI逻辑调用
                if (this.isAI && !this.isStunned && !gameover) {
                    this.runAI(opponent);
                }

                // 应用物理
                this.vy += GRAVITY;
                this.x += this.vx;
                this.y += this.vy;

                // 地面碰撞
                if (this.y >= GROUND_Y) {
                    this.y = GROUND_Y;
                    this.vy = 0;
                    this.onGround = true;
                    this.jumpsRemaining = 2; // 重置二段跳
                } else {
                    this.onGround = false;
                }

                // 边界碰撞
                this.x = clamp(this.x, this.w / 2, CANVAS_WIDTH - this.w / 2);

                // 确定朝向 (非闪避状态下)
                if (!this.isDodging) {
                    this.vx = 0; // 默认停止移动，由输入控制
                    if (opponent.x > this.x) {
                        this.facing = 1;
                    } else {
                        this.facing = -1;
                    }
                }
            }

            // 处理输入 (仅玩家)
            handleInput() {
                if (this.isStunned || gameover) return;

                this.isBlocking = false;

                if (this.isDodging) return; // 闪避时锁定输入

                // 移动
                if (keys[this.controls.left]) {
                    this.vx = -this.speed;
                }
                if (keys[this.controls.right]) {
                    this.vx = this.speed;
                }

                // 格挡
                if (keys[this.controls.block]) {
                    this.isBlocking = true;
                    this.vx = 0; // 格挡时不能移动
                }

                // 攻击 (格挡时不能攻击)
                if (keys[this.controls.attack] && !this.isBlocking) {
                    this.attack();
                }

                // 闪避
                if (keys[this.controls.dodge]) {
                    this.dodge();
                }

                // 技能
                if (keys['Digit1']) this.useSkill('fire');
                if (keys['Digit2']) this.useSkill('water');
                if (keys['Digit3']) this.useSkill('earth');
                if (keys['Digit4']) this.useSkill('wind');
                if (keys['Digit5']) this.useSkill('thunder');
            }

            // 跳跃 (需要单独处理，因为是 keydown 事件)
            jump() {
                if (this.isStunned) return;
                if (this.jumpsRemaining > 0) {
                    this.vy = this.jumpForce;
                    this.jumpsRemaining--;
                    this.onGround = false;
                }
            }

            // 普攻
            attack() {
                if (this.attackCooldown === 0 && !this.isAttacking) {
                    this.isAttacking = true;
                    this.attackCooldown = 30; // 攻击冷却
                    setTimeout(() => {
                        this.isAttacking = false;
                    }, 150); // 攻击持续时间

                    // 创建攻击判定框
                    const attackBox = {
                        x: this.x + (this.facing * (this.w / 2)),
                        y: this.y - this.h + 30,
                        w: 40,
                        h: 20
                    };
                    this.checkHit(attackBox, 10, 10);
                }
            }

            // 闪避
            dodge() {
                if (this.dodgeCooldown === 0) {
                    this.isDodging = true;
                    this.invincible = true;
                    this.vx = this.facing * 18;
                    this.dodgeCooldown = 60;

                    setTimeout(() => {
                        this.isDodging = false;
                        this.invincible = false;
                        this.vx = 0;
                    }, 250); // 闪避持续时间
                }
            }

            // 技能系统
            useSkill(element) {
                if (this.isAttacking || this.isBlocking || this.isDodging) return;

                const costs = { fire: 15, water: 20, earth: 15, wind: 25, thunder: 30 };

                if (this.mp < costs[element]) return;

                this.mp -= costs[element];

                switch (element) {
                    case 'fire': // 火球
                        projectiles.push(new Projectile(this.x + this.facing * 20, this.y - this.h / 2, this.facing, '#e74c3c', this));
                        break;
                    case 'water': // 治疗
                        this.hp = Math.min(this.maxHp, this.hp + 30);
                        // 简单的治疗效果视觉
                        ctx.fillStyle = '#3498db';
                        ctx.fillText('+HP', this.x, this.y - this.h - 10);
                        break;
                    case 'earth': // 防御强化
                        this.defenseBoost = 0.5; // 减少50%伤害
                        this.defenseBoostTimer = 300; // 5秒
                        break;
                    case 'wind': // 风切突进
                        this.isDodging = true; // 利用闪避机制实现突进
                        this.vx = this.facing * 15;
                        // 创建突进攻击判定框
                        const windBox = {
                            x: this.x,
                            y: this.y - this.h,
                            w: this.w + 40,
                            h: this.h
                        };
                        this.checkHit(windBox, 15, 5);

                        setTimeout(() => {
                            this.isDodging = false;
                            this.vx = 0;
                        }, 200);
                        break;
                    case 'thunder': // 雷击
                        const opponent = this === player1 ? player2 : player1;
                        // 直接打击对手位置
                        projectiles.push(new Projectile(opponent.x, opponent.y - opponent.h - 100, 0, '#f1c40f', this, true));
                        break;
                }
            }

            // 检查攻击是否命中
            checkHit(attackBox, damage, knockback) {
                const opponent = this === player1 ? player2 : player1;
                const oppRect = {
                    x: opponent.x - opponent.w / 2,
                    y: opponent.y - opponent.h,
                    w: opponent.w,
                    h: opponent.h
                };

                // 简化的碰撞检测
                if (attackBox.x < oppRect.x + oppRect.w &&
                    attackBox.x + attackBox.w > oppRect.x &&
                    attackBox.y < oppRect.y + oppRect.h &&
                    attackBox.y + attackBox.h > oppRect.y) {
                    opponent.takeDamage(damage, this.facing * knockback);
                }
            }

            // 受到伤害
            takeDamage(amount, knockback, stun = 0) {
                if (this.invincible) return;

                let actualDamage = amount;

                if (this.isBlocking) {
                    actualDamage *= 0.2; // 格挡减少80%伤害
                    knockback *= 0.3;    // 减少击退
                }

                if (this.defenseBoost > 0) {
                    actualDamage *= (1 - this.defenseBoost);
                }

                this.hp -= actualDamage;
                this.vx = knockback;
                this.vy = -Math.abs(knockback) * 0.3; // 轻微击飞

                if (stun > 0 && !this.isBlocking) {
                    this.isStunned = true;
                    this.stunTimer = stun;
                }

                // 受伤闪烁效果
                this.invincible = true;
                setTimeout(() => {
                    this.invincible = false;
                }, 100);

                if (this.hp <= 0) {
                    this.hp = 0;
                    this.die();
                }
            }

            die() {
                gameover = true;
                const winner = this === player1 ? "Player 2 (AI)" : "Player 1 (你)";
                document.getElementById('gameOverScreen').innerText = `${winner} 胜利! (刷新页面重开)`;
                document.getElementById('gameOverScreen').style.display = 'block';
            }

            // --- AI 逻辑 ---
            runAI(opponent) {
                if (this.actionTimer > 0) {
                    this.actionTimer--;
                    return;
                }

                const distance = Math.abs(this.x - opponent.x);
                const hpRatio = this.hp / this.maxHp;
                const mpRatio = this.mp / this.maxMp;

                // 1. 反应对手攻击 (防御策略)
                if (opponent.isAttacking && distance < 60) {
                    if (Math.random() < 0.6) {
                        this.isBlocking = true;
                        this.vx = 0;
                        this.actionTimer = 15; // 格挡一小会
                        return;
                    } else if (Math.random() < 0.2) {
                        this.dodge();
                        this.actionTimer = 30;
                        return;
                    }
                }

                this.isBlocking = false;

                // 2. 移动策略
                if (distance > 50) {
                    this.vx = this.facing * this.speed * 0.8; // 靠近
                } else if (distance < 40) {
                    this.vx = -this.facing * this.speed * 0.5; // 稍微拉开距离
                } else {
                    this.vx = 0;
                }

                // 3. 进攻/技能策略
                const rand = Math.random();

                if (hpRatio < 0.3 && mpRatio > 0.5 && rand < 0.5) {
                    // 低血量优先治疗
                    this.useSkill('water');
                    this.actionTimer = 40;
                } else if (distance < 60) {
                    // 近距离
                    if (rand < 0.6) {
                        this.attack();
                        this.actionTimer = 30;
                    } else if (rand < 0.8 && this.mp >= 25) {
                        this.useSkill('wind'); // 突进
                        this.actionTimer = 40;
                    } else if (this.mp >= 15 && this.defenseBoost === 0) {
                        this.useSkill('earth'); // 开盾
                        this.actionTimer = 30;
                    }
                } else if (distance > 100 && distance < 400) {
                    // 中距离
                    if (this.mp >= 30 && rand < 0.3) {
                        this.useSkill('thunder');
                        this.actionTimer = 50;
                    } else if (this.mp >= 15 && rand < 0.6) {
                        this.useSkill('fire');
                        this.actionTimer = 40;
                    }
                }

                // 随机跳跃
                if (this.onGround && Math.random() < 0.01) {
                    this.jump();
                }
            }
        }

        // --- Projectile 类 (用于火球/雷击) ---
        class Projectile {
            constructor(x, y, direction, color, owner, isThunder = false) {
                this.x = x;
                this.y = y;
                this.direction = direction;
                this.color = color;
                this.owner = owner;
                this.isThunder = isThunder;
                this.speed = isThunder ? 15 : 10;
                this.size = isThunder ? 20 : 10;
                this.active = true;
                this.damage = isThunder ? 25 : 12;
                this.stun = isThunder ? 30 : 0; // 雷击带眩晕

                if (isThunder) {
                    this.vx = 0;
                    this.vy = this.speed;
                } else {
                    this.vx = this.direction * this.speed;
                    this.vy = 0;
                }
            }

            update(opponent) {
                this.x += this.vx;
                this.y += this.vy;

                // 边界检查
                if (this.x < 0 || this.x > CANVAS_WIDTH || this.y > GROUND_Y) {
                    this.active = false;
                }

                // 碰撞检测
                const oppRect = {
                    x: opponent.x - opponent.w / 2,
                    y: opponent.y - opponent.h,
                    w: opponent.w,
                    h: opponent.h
                };

                if (this.x > oppRect.x && this.x < oppRect.x + oppRect.w &&
                    this.y > oppRect.y && this.y < oppRect.y + oppRect.h) {
                    opponent.takeDamage(this.damage, this.direction * 5, this.stun);
                    this.active = false;
                }
            }

            draw() {
                ctx.fillStyle = this.color;
                if (this.isThunder) {
                    // 绘制闪电（简化为垂直矩形）
                    ctx.fillRect(this.x - 5, this.y - this.size, 10, this.size * 2);
                } else {
                    // 绘制火球
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
        }

        // --- 初始化游戏 ---
        let player1 = new Fighter(100, '#3498db', {
            left: 'KeyA',
            right: 'KeyD',
            jump: 'KeyW',
            block: 'KeyS',
            attack: 'KeyJ',
            dodge: 'KeyK'
        });

        let player2 = new Fighter(900, '#e74c3c', {}, true); // AI控制

        // --- 绘制场景 ---
        function drawBackground() {
            // 天空
            ctx.fillStyle = '#87CEEB';
            ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

            // 远山
            ctx.fillStyle = '#778899';
            ctx.beginPath();
            ctx.moveTo(0, GROUND_Y);
            ctx.lineTo(200, GROUND_Y - 150);
            ctx.lineTo(400, GROUND_Y);
            ctx.lineTo(600, GROUND_Y - 100);
            ctx.lineTo(800, GROUND_Y);
            ctx.fill();

            // 地面
            ctx.fillStyle = '#2ecc71';
            ctx.fillRect(0, GROUND_Y, CANVAS_WIDTH, CANVAS_HEIGHT - GROUND_Y);
        }

        // --- 更新 UI ---
        function updateUI() {
            document.getElementById('p1HP').style.width = `${(player1.hp / player1.maxHp) * 100}%`;
            document.getElementById('p1MP').style.width = `${(player1.mp / player1.maxMp) * 100}%`;
            document.getElementById('p2HP').style.width = `${(player2.hp / player2.maxHp) * 100}%`;
            document.getElementById('p2MP').style.width = `${(player2.mp / player2.maxMp) * 100}%`;
        }

        // --- 游戏主循环 ---
        function gameLoop() {
            // 1. 处理输入
            player1.handleInput();

            // 2. 更新状态
            player1.update(player2);
            player2.update(player1);

            // 更新投射物
            const opponentForP1 = player2;
            const opponentForP2 = player1;

            projectiles.forEach((p, index) => {
                const target = p.owner === player1 ? opponentForP2 : opponentForP1;
                p.update(target);
                if (!p.active) {
                    projectiles.splice(index, 1);
                }
            });

            // 3. 绘制
            ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
            drawBackground();
            player1.draw();
            player2.draw();
            projectiles.forEach(p => p.draw());

            // 4. 更新 UI
            updateUI();

            requestAnimationFrame(gameLoop);
        }

        // --- 事件监听 ---
        window.addEventListener('keydown', (e) => {
            keys[e.code] = true;
            // 处理跳跃 (需要即时响应)
            if (e.code === player1.controls.jump) {
                player1.jump();
            }
        });

        window.addEventListener('keyup', (e) => {
            keys[e.code] = false;
        });

        // 启动游戏
        gameLoop();
    </script>
</body>
</html>