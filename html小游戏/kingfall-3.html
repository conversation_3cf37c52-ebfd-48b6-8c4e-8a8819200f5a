<!DOCTYPE html>
<html>
<head>
<title>火柴人元素格斗:: 策略版</title>
<meta charset="UTF-8">
<style>
  body {
    background-color: #080810; color: #ccc; font-family: monospace;
    display: flex; flex-direction: column; align-items: center;
    margin: 0; overflow: hidden; padding-top: 5px;
  }
  canvas {
    border: 1px solid #666; background-color: #000;
    box-shadow: 0 0 25px rgba(0, 150, 255, 0.2); image-rendering: auto; 
    display: block; margin: auto;
  }
   #info { margin-top: 5px; text-align: center; font-size: 10px; color: #888; display: flex; gap: 12px;}
   #info div { text-align: left;}
   #info kbd {
        background-color: #333; border: 1px solid #555; border-radius: 3px;
        padding: 1px 4px; margin: 0 1px; font-weight: bold; color: #eee; font-size: 11px;
   }
    h4 { margin: 3px 0; color: #eee;}
    .highlight { color: yellow; font-weight: bold;}
</style>
</head>
<body>

<canvas id="gameCanvas" width="900" height="500"></canvas>
<div id="info">
    <div>
     <h4>移动控制:</h4>
       <kbd>A</kbd><kbd>D</kbd> 移动 | <kbd>SPACE</kbd> 跳 | <kbd>S</kbd> 格挡/下平台 | <kbd>Shift</kbd>+<kbd>A</kbd>/<kbd>D</kbd> 闪避
    </div>
     <div>
     <h4>技能 (按住蓄力):</h4>
       <kbd>Q</kbd>风 | <kbd>W</kbd>雷 | <kbd>E</kbd>火 | <kbd>R</kbd>土 | <kbd>F</kbd>水 
     </div>
      <div>
      <h4>特殊 & 奥义:</h4>
      <kbd>J</kbd> 普攻 | <kbd>V</kbd> 形态(HP<50%) | <kbd>TAB</kbd> 重置<br>
       <span class="highlight">奥义:</span> 形态激活 + 按住<kbd>S</kbd> + 按<kbd>Q</kbd>/<kbd>W</kbd>/<kbd>E</kbd>/<kbd>R</kbd>/<kbd>F</kbd> (清空MP, 技能CD翻倍)
     </div>
      <div>
       [提示: 管理MP! MP空时CD惩罚. 元素可互动(风+火). HP<50%可开形态/用奥义. 节奏变慢, 重视博弈.]
      </div>
</div>

<script>
  // 防止因切换标签导致的卡顿或超长蓄力
   let isActive = true;
    window.onfocus = function () {  isActive = true; keysPressed = {}; }; // Clear keys on refocus
    window.onblur = function () { isActive = false;  keysPressed = {}; if(player1) player1.cancelCharge(); }; // Cancel charge on blur

  const canvas = document.getElementById('gameCanvas');
  const ctx = canvas.getContext('2d');

  const GRAVITY = 0.48; // 降低重力，更飘
  const WORLD_WIDTH = 1500; 
  const GROUND_LEVEL = canvas.height - 30;
  const PLAYER_HEIGHT = 45; PLAYER_WIDTH = 18;
  const MAX_HEALTH = 500; MAX_MANA = 300;
  const MANA_REGEN = 0.6; // 慢回蓝
  const MANA_EMPTY_PENALTY_FACTOR = 1.8; // 空蓝CD惩罚倍数
  const JUMP_FORCE = -10.5; MOVE_SPEED = 3.0; // 慢速
  const DODGE_SPEED = 10; DODGE_DURATION = 20; DODGE_COOLDOWN = 120; // 长CD闪避
  const STUN_BASE = 60; KNOCKBACK_BASE = 7;
  const MAX_CHARGE_TIME = 120; // frames (2s)
  const ULTIMATE_COST_MP_FACTOR = 2.0; // 奥义后CD翻倍
   const HP_THRESHOLD_FORM = MAX_HEALTH * 0.5;
  
  const KEYS = {
      LEFT: 'KeyA', RIGHT: 'KeyD', JUMP: 'Space', DOWN_BLOCK: 'KeyS', ATTACK: 'KeyJ', DODGE_MOD: 'ShiftLeft',
      SKILL_WIND: 'KeyQ', SKILL_THUNDER:'KeyW', SKILL_FIRE: 'KeyE', SKILL_EARTH: 'KeyR', SKILL_WATER: 'KeyF',
      SWITCH_FORM: 'KeyV', RESET: 'Tab'
  };
   const COLORS = {
      fire: '#FF4500', water: '#00BFFF', earth: '#A0522D', wind: '#7CFC00', thunder: '#FFFF00',
      light: '#FFFFFF', dark: '#A020F0', none: '#AAAAAA', block: '#8888FF', hit: '#FF0000',
      mana: '#0066FF', health_high: '#00FF00', health_mid:'#FFFF00', health_low: '#FF4400', penalty: '#FF00FF',
       bg_grad1: '#050210', bg_grad2: '#102040'
  };
  const ELEMENTS = ['fire', 'water', 'earth', 'wind', 'thunder'];
  const ELEMENT_MAP = { KeyQ: 'wind', KeyW: 'thunder', KeyE: 'fire', KeyR: 'earth', KeyF: 'water' };
   const KEY_MAP = { wind: 'Q', thunder: 'W', fire:'E', earth:'R', water:'F'};
  
   const SKILL_STATS = { // CD大幅增加
      attack:   { mp: 0,  cd: 40,  dmg: 10,  range: 40, knockback: KNOCKBACK_BASE * 0.7 },
      dodge:    { mp: 15, cd: DODGE_COOLDOWN },
      switch:   { mp: 30, cd: 300 }, // 切换形态CD长
      block:    { mp_cost: 0.2 }, // 格挡微量耗蓝
      fire:     { mp: 30, cd: 150,  dmg: 15, power: 100, charge_factor: 2 }, // 蓄力增加伤害/大小
      water:    { mp: 60, cd: 350, heal: 50, charge_factor: 2.5}, // 蓄力增加治疗量
      earth:    { mp: 40, cd: 220, dmg: 25, knockback: KNOCKBACK_BASE * 0.8, blockBreak: true, charge_factor: 1.8 }, 
      wind:     { mp: 25, cd: 180,  dmg: 5,  knockback: KNOCKBACK_BASE * 2, power: 50, charge_factor: 3 }, // 蓄力增加龙卷风持续/大小
      thunder:  { mp: 50, cd: 280, dmg: 10,  stun: STUN_BASE, power: 80, charge_factor: 1.5}, // 蓄力增加眩晕
      // Ultimates
       ulti_fire: { name: "豪火灭却", dmg: 5, duration: 100, width: 300 },
       ulti_water: { name: "暴水冲波", dmg: 3, duration: 180, push: 5},
       ulti_earth: { name: "地动山摇", dmg: 80, knockback: KNOCKBACK_BASE*1.5, radius: 250},
       ulti_wind:  { name: "风神之息", dmg: 8, duration: 300, size: 120, speed: 2.5},
       ulti_thunder:{ name: "天罚神雷", dmg: 100, stun: STUN_BASE*2, radius: 80},
  };

  const PLATFORMS = [
     { x: 0, y: GROUND_LEVEL, width: WORLD_WIDTH, height: 30, isGround: true },
     { x: 200, y: GROUND_LEVEL - 100, width: 150, height: 15 },
     { x: WORLD_WIDTH - 350, y: GROUND_LEVEL - 100, width: 150, height: 15 },
     { x: WORLD_WIDTH / 2 - 125, y: GROUND_LEVEL - 160, width: 250, height: 15 },
     { x: WORLD_WIDTH / 2 - 280 , y: GROUND_LEVEL - 260, width: 100, height: 15 },
     { x: WORLD_WIDTH / 2 + 180 , y: GROUND_LEVEL - 260, width: 100, height: 15 },
  ];

  let keysPressed = {}; let projectiles = []; let effects = []; let areaEffects = [];
  let gameState = "PLAYING"; let gameTimer = 0; let cameraX = 0; let screenFlash = {color: '#FFF', alpha: 0};

   function checkCollision(r1, r2) { return r1.x < r2.x + r2.width && r1.x + r1.width > r2.x && r1.y < r2.y + r2.height && r1.y + r1.height > r2.y; }
   function distance(p1, p2) { return Math.hypot(p1.x - p2.x, p1.y - p2.y); }
   function clamp(num, min, max) { return Math.min(Math.max(num, min), max); }
   function getStatMod(base, form) { // 光减暗增
       if (!base) return 0;
       if (form === 'light') return base * 0.8;
       if (form === 'dark') return base * 1.3;
       return base;
    }
   function getCD(baseCD, fighter) {
       let cd = getStatMod(baseCD, fighter.form);
       if(fighter.mana <= 1) cd *= MANA_EMPTY_PENALTY_FACTOR; // 空蓝惩罚
       return cd;
   }

    function handlePlatformCollision(f) {
        f.isOnGround = false; f.currentPlatform = null;
        if (f.vy >= 0 && !f.platformDrop) {
            const feetY = f.y + f.height;
            for (const p of PLATFORMS) {
                 if (f.x < p.x + p.width && f.x + f.width > p.x && feetY >= p.y && feetY < p.y + p.height + f.vy + 2 && f.y + f.height/2 < p.y ) {
                    f.y = p.y - f.height; f.vy = 0; f.jumpCount = 0; f.isOnGround = true; f.currentPlatform = p;
                    if (f.justLanded) { addEffect(f.x+f.width/2, f.y+f.height, COLORS.none, 40, 25, 'ring', 1); f.justLanded = false; }
                    break; 
                }
            }
        }
        if(!f.isOnGround) f.justLanded = true;
    }
    
    // 绘制火柴人 (更细致)
   function drawStickFigure(ctx, f) {
      const x = f.x + f.width/2; const y = f.y; const height = f.height; const dir = f.facingRight ? 1 : -1;
      const time = gameTimer / 15; // 动画放慢
      let color = f.form === 'light' ? COLORS.light : (f.form === 'dark' ? COLORS.dark : f.color);
      if (f.isBlocking) color = COLORS.block;
      if (f.stunTimer > 0) color = COLORS.thunder;
      if (f.hitStun > 0) color = gameTimer % 8 > 4 ? color : COLORS.hit;
       if (f.mana <= 1) color = gameTimer % 10 > 5 ? color : COLORS.penalty; // 空蓝闪烁

      ctx.strokeStyle = color; ctx.lineWidth = f.form === 'dark' ? 3 : 2; ctx.lineCap = 'round';
      
      const headRadius = height / 6.5, headY = y + headRadius, neckY = y + headRadius * 2, shoulderY = neckY + height*0.1, hipY = y + height * 0.6;
      
      let glowColor = color;
       if(f.activeElementEffect) glowColor = COLORS[f.activeElementEffect];
       else if(f.isCharging) glowColor = COLORS[f.chargeElement];
      ctx.shadowBlur = f.isCharging ? clamp(f.chargeTimer/MAX_CHARGE_TIME * 40, 10, 40) : (f.form !== 'none' || f.activeElementEffect ? 18 : 0) ;
      ctx.shadowColor = glowColor;

      let armAngle1 = 0.3*dir, armAngle2 = -0.3*dir, legAngle1 = 0.1*dir, legAngle2 = -0.1*dir, bodyTilt = 0;

      if (f.isCharging) {
         bodyTilt = -0.1*dir; armAngle1 = (1.0 + Math.sin(time*1.5)*0.2)*dir; armAngle2 = (0.8 + Math.cos(time*1.5)*0.2)*dir; 
         legAngle1=0.3*dir; legAngle2=-0.3*dir;
         addEffect(x, y+height/2, glowColor, f.chargeTimer/2, 10, 'spark_in', 1, 0, x, y+height/2);
      } else if(f.isBlocking) {
         bodyTilt = -0.08 * dir; armAngle1 = 1.5*dir; armAngle2 = 1.5 * -dir; legAngle1=0.2*dir; legAngle2=-0.2*dir;
      } else if (f.dodgeTimer > 0) {
           bodyTilt = 0.25*dir; legAngle1 = -0.7*dir; legAngle2 = 0.5*dir; armAngle1 = -0.9*dir; armAngle2 = 0.7*dir;
      } else if (f.hitStun > 0 || f.stunTimer > 0) {
          bodyTilt = -0.2*dir; armAngle1 = -1.2*dir; armAngle2 = 1.2*dir;
      } else if (f.isAttacking > 0 || f.isCasting > 0) {
           armAngle1 = 1.7*dir; bodyTilt = 0.1*dir; legAngle1 = -0.2*dir; legAngle2=0.3*dir;
       } else if (!f.isOnGround) { 
          bodyTilt = f.vy / 30 * dir; armAngle1 = (-0.7 + f.vy/15)*dir; armAngle2 = (0.7 - f.vy/15)*dir;
          legAngle1 = (-0.3 + f.vy/10)*dir; legAngle2 = (0.2 - f.vy/10)*dir;
      } else if (Math.abs(f.vx) > 0.5) { // Run
          const runCycle = Math.sin(time * 1.8); bodyTilt = 0.1*dir;
          armAngle1 = -runCycle*0.7*dir; armAngle2 = runCycle*0.7*dir; legAngle1 = runCycle*0.5*dir; legAngle2 = -runCycle*0.5*dir;
      } else { const idleCycle = Math.sin(time * 0.4); armAngle1 = (0.2 + idleCycle*0.08)*dir; armAngle2 = (-0.2 - idleCycle*0.08)*dir;}
      
      const cosBody = Math.cos(bodyTilt), sinBody = Math.sin(bodyTilt);
      const applyTilt = (px, py) => ({ x: x+(px-x)*cosBody - (py-y)*sinBody, y: y+(px-x)*sinBody + (py-y)*cosBody});
       const neckPt = applyTilt(x, neckY), hipPt = applyTilt(x, hipY), shoulderPt = applyTilt(x, shoulderY);

       ctx.beginPath(); ctx.moveTo(neckPt.x, neckPt.y); ctx.lineTo(hipPt.x, hipPt.y); ctx.stroke(); // Body
       ctx.beginPath(); ctx.arc(x, headY, headRadius, 0, Math.PI * 2); ctx.stroke(); // Head
        if(f.form === 'dark') { ctx.fillStyle = color; ctx.fill();}

       const drawLimb = (sX, sY, len, angle) => { ctx.beginPath(); ctx.moveTo(sX, sY); ctx.lineTo(sX + len * Math.sin(angle), sY + len * Math.cos(angle)); ctx.stroke(); };
       drawLimb(shoulderPt.x, shoulderPt.y, height*0.28, armAngle1); drawLimb(shoulderPt.x, shoulderPt.y, height*0.28, armAngle2);
       drawLimb(hipPt.x, hipPt.y, height*0.4, legAngle1); drawLimb(hipPt.x, hipPt.y, height*0.4, legAngle2);
      ctx.shadowBlur = 0; 
   }

    class Effect {
        constructor(x, y, color, size, lifespan, type, count, vy, targetX, targetY) {
            this.particles = [];
            for(let i=0; i< count; i++){
               this.particles.push({
                 x: x + (Math.random()-0.5)*size/3,  y: y + (Math.random()-0.5)*size/3, 
                 vx: (Math.random()-0.5)*2, vy: (Math.random()-0.5)*1.5 + vy,
                 color: color, size: size * (Math.random()*0.5+0.75), lifespan: lifespan, maxLifespan: lifespan, type: type,
                 tx: targetX, ty: targetY // for 'spark_in'
               });
            }
        }
         update() { 
            this.particles.forEach(p => {
                 if(p.type === 'spark') { p.x += p.vx; p.y += p.vy; p.vy += 0.08; }
                 else if (p.type === 'spark_in' && p.tx !== undefined) { // Flow towards target
                    const angle = Math.atan2(p.ty - p.y, p.tx - p.x);
                    p.x += Math.cos(angle) * 2; p.y += Math.sin(angle) * 2;
                 }
                 p.lifespan--;
             });
             this.particles = this.particles.filter(p => p.lifespan > 0);
             return this.particles.length > 0;
          }
         draw(ctx) {
             this.particles.forEach( p => {
                  const alpha = clamp(p.lifespan / p.maxLifespan, 0, 1);
                  ctx.fillStyle = p.color; ctx.strokeStyle = p.color; ctx.globalAlpha = alpha;
                  ctx.shadowBlur = 8; ctx.shadowColor = p.color;
                  if (p.type.startsWith('spark')) {
                      ctx.beginPath(); ctx.arc(p.x, p.y, p.size/6 * alpha, 0, Math.PI * 2); ctx.fill();
                  } else if (p.type === 'ring') {
                      ctx.lineWidth = p.size/12; ctx.beginPath(); ctx.arc(p.x, p.y, p.size * (1-alpha+0.05) , 0, Math.PI * 2); ctx.stroke();
                  }
                   ctx.shadowBlur = 0;
             });
             ctx.globalAlpha = 1.0;
         }
    }
     function addEffect(x, y, color, size, lifespan, type='spark', count=1, vy=-1, tx, ty) { effects.push(new Effect(x, y, color, size, lifespan, type, count, vy, tx, ty));}
     function setScreenFlash(color, alpha) { screenFlash = {color: color, alpha: alpha}; }

    // 区域/持续效果 (龙卷风, 火墙, 泥潭)
     class AreaEffect {
        constructor(x, y, width, height, color, element, owner, stats, lifespan, vx=0) {
           this.x = x; this.y = y; this.width = width; this.height = height;
           this.color = color; this.element = element; this.owner = owner;
            this.stats = stats; this.lifespan = lifespan; this.maxLifespan = lifespan;
           this.hitTimer = 0; this.vx = vx; this.isUltimate = stats.name !== undefined;
            this.form = owner.form;
        }
        getHitbox() { return {x:this.x, y:this.y, width: this.width, height: this.height}; }
         update(target) {
             this.lifespan--; this.hitTimer --; this.x += this.vx;
             const alpha = clamp(this.lifespan / this.maxLifespan, 0, 1);
             
              // INTERACTION LOGIC
             if(this.element === 'wind' || this.element === 'ulti_wind') {
                 for(const p of projectiles) {
                     if (p.owner === this.owner && p.element === 'fire' && checkCollision(this.getHitbox(), p.getHitbox())) {
                          this.element = 'fire_wind'; this.color = COLORS.fire; p.hit = true; // Consume fire ball
                          this.stats.dmg = (this.stats.dmg || 0) + p.damage; addEffect(this.x, this.y, COLORS.fire, 100, 30, 'ring', 3);
                     }
                 }
                 if (this.element === 'fire_wind') addEffect(this.x + Math.random()*this.width, this.y + Math.random()*this.height, COLORS.fire, 20, 15, 'spark', 2, -1);
             }


             if(this.hitTimer <= 0 && checkCollision(this.getHitbox(), target.getHitbox())) {
                 this.hitTimer = this.element.includes('wind') ? 15 : 25; // wind hits faster
                 const dmg = getStatMod(this.stats.dmg, this.form);
                 const dir = target.getCenter().x > this.owner.getCenter().x ? 1 : -1;
                 target.takeDamage(dmg, dir * (this.stats.knockback || 0) , this.stats.stun || 0, this.element, this.stats.blockBreak);
                  
                  // Tsunami Push
                  if(this.element === 'ulti_water') {
                      target.x += this.stats.push * (this.vx > 0 ? 1 : -1);
                      target.stunTimer = Math.max(target.stunTimer, 10); // micro stun
                  }
                 // Wind Suction/Lift
                  if(this.element.includes('wind')) {
                      target.vx += ( (this.x+this.width/2) - target.getCenter().x ) * 0.05; // Suction
                      if(target.isOnGround) target.vy = -2; // Lift
                  }
             }
             return this.lifespan > 0 && this.x > -100 && this.x < WORLD_WIDTH+100;
         }
         draw(ctx) {
             const alpha = clamp(this.lifespan / this.maxLifespan * 0.6, 0, 0.6) ;
             ctx.fillStyle = this.color; ctx.globalAlpha = alpha; ctx.shadowColor = this.color; ctx.shadowBlur = 15;
             
             if (this.element.includes('wind')) { // Draw Tornado
                ctx.globalAlpha = alpha * 1.2;
                 const step = this.height / 10;
                 for(let i=0; i<10; i++) {
                     const w = this.width * (i/10) * Math.sin(gameTimer/10 + i) ;
                     ctx.beginPath();
                     ctx.ellipse(this.x + this.width/2, this.y + i*step, Math.abs(w/2), step/3, 0, 0, Math.PI*2);
                     ctx.fill();
                 }
             } else if (this.element === 'ulti_water') { // Draw Wave
                 ctx.beginPath();
                 ctx.moveTo(this.x, this.y+this.height);
                 ctx.quadraticCurveTo(this.x+this.width/2, this.y - this.height*0.8, this.x+this.width, this.y+this.height);
                 ctx.fill();
             }
             else {
                ctx.fillRect(this.x, this.y, this.width, this.height);
             }
             ctx.globalAlpha = 1.0; ctx.shadowBlur = 0;
         }
     }

   class Projectile {
      constructor(x, y, vx, vy, size, color, owner, element, stats, lifespan = 200) {
          this.x = x; this.y = y; this.vx = vx; this.vy = vy; this.width = size; this.height = size; 
          this.color = color; this.owner = owner; this.element = element;
          this.stats = stats; this.damage = getStatMod(stats.dmg, owner.form); this.power = stats.power || 0;
          this.lifespan = lifespan; this.hit = false; this.form = owner.form;
      }
      getHitbox() { return { x: this.x - this.width/2, y: this.y - this.height/2, width: this.width, height: this.height };}
      getCenter() { return { x: this.x, y: this.y }; }

      update(target, otherProjectiles) {
          if (this.hit) return false;
          this.x += this.vx; this.y += this.vy; this.lifespan--;
           if (this.element === 'earth') this.vy += GRAVITY * 0.6; 
           addEffect(this.x, this.y, this.color, this.width*0.8, 10, 'spark', 1, 0);

           // 对波 & INTERACTION
           if (this.power > 0) {
              for(const other of otherProjectiles) {
                 if(other !== this && !other.hit && checkCollision(this.getHitbox(), other.getHitbox())) {
                     if( other.owner !== this.owner && other.power > 0) { // Beam Struggle
                           addEffect((this.x+other.x)/2, (this.y+other.y)/2, COLORS.light, 60, 30, 'ring', 2);
                           const powerDiff = this.power - other.power;
                           if (Math.abs(powerDiff) < 20) { this.hit = true; other.hit = true; } 
                           else if (powerDiff > 0) { other.hit = true; this.power -= other.power*0.5; this.vx *= 0.6; } 
                           else { this.hit = true; other.power -= this.power*0.5; other.vx *= 0.6; }
                     }
                    return !this.hit; 
                 }
              }
           }
            // WATER + THUNDER Interaction
            if ((this.element === 'water' && target.activeElementEffect === 'thunder') || 
                (this.element === 'thunder' && target.activeElementEffect === 'water')
                ) {
                 // trigger electrocute on target? Or create steam?
             }


           if (checkCollision(this.getHitbox(), target.getHitbox())) {
                const direction = this.vx > 0 ? 1 : -1;
                target.takeDamage(this.damage, direction * KNOCKBACK_BASE*0.8, this.stats.stun, this.element, this.stats.blockBreak);
                addEffect(this.x, this.y, this.color, this.width * 4, 25, 'spark', 6);
                this.hit = true; return false; 
           }
           const hitGround = this.y > GROUND_LEVEL;
           return this.lifespan > 0 && this.x > -50 && this.x < WORLD_WIDTH+50 && !hitGround;
      }
      draw(ctx) {
          ctx.fillStyle = this.color; ctx.shadowBlur = 20;
          ctx.shadowColor = this.form === 'dark' ? COLORS.dark : (this.form === 'light' ? COLORS.light : this.color);
          ctx.beginPath(); ctx.arc(this.x, this.y, this.width/2 * (this.form === 'dark' ? 1.2:1), 0, Math.PI * 2); ctx.fill();
          ctx.shadowBlur = 0;
      }
   }

  class Fighter {
    constructor(x, y, color, isAI = false, name="Player") {
      this.x = x; this.y = y; this.width = PLAYER_WIDTH; this.height = PLAYER_HEIGHT;
      this.vx = 0; this.vy = 0; this.color = color; this.name = name;
      this.health = MAX_HEALTH; this.mana = MAX_MANA;
      this.isAI = isAI; this.isOnGround = false; this.justLanded = false;
      this.jumpCount = 0; this.facingRight = !isAI; this.form = 'none'; // light, dark, none

      this.timers = { attack:0, dodge:0, dodgeTime:0, stun:0, hitStun:0, blockStun:0, attacking:0, casting:0, invincible:0, effect:0, form:0};
      this.isBlocking = false; this.isCharging = false; this.chargeTimer = 0; this.chargeElement = null;
      this.skillCooldowns = {}; ELEMENTS.forEach(e => this.skillCooldowns[e] = 0); 
      this.activeElementEffect = null; 
      this.platformDrop = false; this.currentPlatform = null; this.moveSpeedMod = 1.0;
       this.ultimateUsed = false; // flag after using ultimate

       this.aiActionTimer = 0; this.aiAggression = Math.random() * 0.5 + 0.4; this.aiChargeTimer = 0; this.aiBlockTimer = 0;
    }
    getHitbox() { return { x: this.x + 2, y: this.y, width: this.width-4, height: this.height }; }
    getCenter() { return { x: this.x + this.width/2, y: this.y + this.height/2 };}
    canAct() { return this.timers.stun <= 0 && this.timers.hitStun <=0 && this.timers.blockStun <=0 && this.timers.attacking <=0 && this.timers.casting <= 0 && this.timers.dodgeTime <=0 && !this.isCharging; }
    canMove() { return this.timers.stun <= 0 && this.timers.hitStun <=0 && this.timers.blockStun <=0 && this.timers.attacking <= 0 && this.timers.casting <= 0 && !this.isBlocking && !this.isCharging; }
    canUseUltimate() { return this.health <= HP_THRESHOLD_FORM && this.form !== 'none' && this.mana > MAX_MANA * 0.5; } // Need some mana to start
    setStateEffect(element, duration=40) { this.activeElementEffect = element; this.timers.effect = duration; }
     cancelCharge() { this.isCharging = false; this.chargeTimer = 0; this.chargeElement = null; }

    update(target) {
       for(let t in this.timers) { this.timers[t] = Math.max(0, this.timers[t] -1); }
       if(this.timers.effect === 0) this.activeElementEffect = null;
       for(let skill in this.skillCooldowns) { this.skillCooldowns[skill] = Math.max(0, this.skillCooldowns[skill] - 1); }
        if(this.ultimateUsed && this.mana > 1) this.ultimateUsed = false; // reset flag when mana regens

       this.mana = clamp(this.mana + MANA_REGEN * (this.form === 'light' ? 1.3 : 1), 0, MAX_MANA);
       if(this.isCharging) this.chargeTimer++;

      if (this.isAI) this.runAI(target);
      if (this.timers.stun > 0) { this.isBlocking = false; this.cancelCharge(); }
      if ((this.isBlocking && this.isOnGround) || this.isCharging) { 
           this.vx *= 0.5; 
           if(this.isBlocking) this.mana = Math.max(0, this.mana - SKILL_STATS.block.mp_cost);
      }

       this.vy += GRAVITY;
       const moveSpeed = getStatMod(MOVE_SPEED, this.form === 'light' ? 'dark' : (this.form === 'dark' ? 'light' : 'none')) * this.moveSpeedMod; 
       if (this.timers.dodgeTime > 0) {
          this.vx = (this.facingRight ? 1 : -1) * DODGE_SPEED; this.vy = Math.min(this.vy, -1); this.timers.invincible = this.timers.dodgeTime;
       } else if (this.canMove()) {
           this.vx = clamp(this.vx, -moveSpeed, moveSpeed);
       } else { this.vx *= 0.8; } // Friction
        this.x += this.vx; this.y += this.vy;

       handlePlatformCollision(this); 
       this.x = clamp(this.x, 0, WORLD_WIDTH - this.width);

       const attackBox = (this.timers.attacking > 0) ? { 
           y: this.y + this.height/4, height: this.height/3, 
           width: SKILL_STATS.attack.range, 
           x: this.facingRight ? this.x + this.width : this.x - SKILL_STATS.attack.range
        } : null;
       if(attackBox && checkCollision(attackBox, target.getHitbox())) {
           const stats = SKILL_STATS.attack;
           target.takeDamage(getStatMod(stats.dmg, this.form), (this.facingRight?1:-1) * stats.knockback, 0, 'none');
           this.timers.attacking = 0; addEffect( (attackBox.x + target.x)/2 , attackBox.y, COLORS.light, 20, 15, 'spark', 3);
       }
       if(this.canAct() && !this.isBlocking) { this.facingRight = this.x < target.x; } 
    }

    draw(ctx) { drawStickFigure(ctx, this); }
    move(direction) { if (this.canMove() && this.timers.dodgeTime <=0) this.vx = direction * getStatMod(MOVE_SPEED, this.form === 'light' ? 'dark' : (this.form === 'dark' ? 'light' : 'none')) * this.moveSpeedMod; }
    jump() {
        if (this.canMove() && this.jumpCount < 2) { this.cancelCharge();
           this.vy = JUMP_FORCE * (this.jumpCount === 1 ? 0.9 : 1) ; this.jumpCount++; this.isOnGround = false; this.platformDrop = false; 
           addEffect(this.x+this.width/2, this.y+this.height, COLORS.none, 40, 20, 'ring', 1);
        }
     }
    attack() {
         const stats = SKILL_STATS.attack;
         if(this.canAct() && this.timers.attack === 0 && this.mana >= stats.mp) { this.cancelCharge();
            this.timers.attacking = 20; this.timers.attack = getCD(stats.cd, this); this.mana -= stats.mp; this.vx *=0.1;
         }
    }
    block(isBlocking) {
        if (isBlocking) this.cancelCharge();
        if((this.canAct() || !isBlocking) && this.isOnGround && this.timers.dodgeTime <=0 ) { this.isBlocking = isBlocking; }
    }
    dodge(direction){
        const stats = SKILL_STATS.dodge;
        if(this.canAct() && this.timers.dodge === 0 && this.mana >= stats.mp) { this.cancelCharge();
            this.timers.dodgeTime = DODGE_DURATION; this.timers.dodge = getCD(stats.cd, this); this.mana -= stats.mp;
            this.facingRight = direction > 0;
            addEffect(this.getCenter().x, this.getCenter().y, COLORS[this.form]||COLORS.light, 50, 20, 'spark', 5);
        }
    }
    switchForm() {
        const stats = SKILL_STATS.switch;
        // HP check added
        if(this.canAct() && this.timers.form === 0 && this.mana >= stats.mp && this.health <= HP_THRESHOLD_FORM) { this.cancelCharge();
            this.timers.form = getCD(stats.cd, this); this.mana -= stats.mp;
            this.form = (this.form === 'none') ? 'light' : (this.form === 'light' ? 'dark' : 'none');
            addEffect(this.getCenter().x, this.getCenter().y, COLORS[this.form]||COLORS.light, 90, 40, 'ring', 3);
            this.timers.casting = 25; setScreenFlash(COLORS[this.form]||COLORS.light, 0.3);
        }
    }
    startCharge(element) {
        const stats = SKILL_STATS[element];
         if(!this.canAct() || this.skillCooldowns[element] > 0 || this.mana < getStatMod(stats.mp, this.form) || this.isBlocking) return;
         this.isCharging = true; this.chargeElement = element; this.chargeTimer = 0;
    }
    releaseCharge(target) {
        if(!this.isCharging) return;
         const element = this.chargeElement;
         const chargeLevel = clamp(this.chargeTimer / MAX_CHARGE_TIME, 0.1, 1.0); // 0.1 to 1.0
         this.cancelCharge();
         this.useSkill(element, target, chargeLevel);
    }
    useSkill(element, target, chargeLevel = 0.1) { // Tap is 0.1 charge
           const stats = SKILL_STATS[element];
           const mpCost = getStatMod(stats.mp, this.form);
           if(this.skillCooldowns[element] > 0 || this.mana < mpCost) return;

           this.mana -= mpCost;
           this.skillCooldowns[element] = getCD(stats.cd, this);
           this.timers.casting = 20; 
           this.setStateEffect(element, 50);

           const startX = this.getCenter().x + (this.facingRight ? 15 : -15);
           const startY = this.y + this.height / 3;
           const dir = this.facingRight ? 1 : -1;
           const charged = 1 + (chargeLevel * (stats.charge_factor-1)); // e.g., factor 2: 1 + (1 * 1) = 2x effect

           switch(element) {
               case 'fire': case 'thunder':
                  const pSize = (element==='fire'?12:8) * charged;
                  const pDmg = (stats.dmg||0) * charged;
                  const pStun = (stats.stun||0) * charged;
                   const finalStats = {...stats, dmg: pDmg, stun: pStun};
                  projectiles.push(new Projectile(startX, startY, dir * (element=='fire'?8:11), 0, pSize, COLORS[element], this, element, finalStats));
                  break;
                case 'earth':
                     if(this.isOnGround) { 
                         const radius = 100 * charged;
                          addEffect(this.getCenter().x, this.y+this.height, COLORS.earth, radius, 30, 'ring',1);
                         if(target.isOnGround && Math.abs(target.x - this.x) < radius) {
                           target.takeDamage(getStatMod(stats.dmg * charged, this.form), dir*stats.knockback, 0, 'earth', stats.blockBreak);
                           target.vy = -6;
                         }
                    } else projectiles.push(new Projectile(startX, startY, dir*5, -1.5, 18*charged, COLORS.earth, this, 'earth', {...stats, dmg: stats.dmg*charged}, 250));
                    break;
               case 'water': 
                   this.health = clamp(this.health + getStatMod(stats.heal * charged, this.form === 'light'? 'dark':'none'), 0, MAX_HEALTH); 
                    addEffect(this.getCenter().x, this.getCenter().y, COLORS.water, 50*charged, 40, 'ring', 3);
                  break;
               case 'wind': // Charged = Tornado
                   const duration = 80 * charged; const size = 30 * charged;
                    const tornadoStats = {...stats, dmg: stats.dmg};
                    areaEffects.push(new AreaEffect(startX - size/2, this.y, size, this.height, COLORS.wind, element, this, tornadoStats, duration, dir*1.5));
                   break;
           }
     }
     
      useUltimate(element, target) {
        if(!this.canUseUltimate() || !this.isBlocking) return;
         const stats = SKILL_STATS['ulti_'+element];
         if(!stats) return;
        
         console.log(`${this.name} ULTIMATE: ${stats.name}`);
         setScreenFlash(COLORS[element], 0.6);
         addEffect(this.getCenter().x, this.getCenter().y, COLORS[element], 200, 50, 'ring', 5);
         this.cancelCharge(); this.isBlocking = false; this.timers.casting = 40; this.ultimateUsed = true;

          // Apply COST
         this.mana = 0;
         for(const skill in this.skillCooldowns) {
             const baseCD = SKILL_STATS[skill] ? SKILL_STATS[skill].cd : 100;
             this.skillCooldowns[skill] = Math.max(this.skillCooldowns[skill], getCD(baseCD, this) * ULTIMATE_COST_MP_FACTOR);
         }
         this.timers.attack = Math.max(this.timers.attack, getCD(SKILL_STATS.attack.cd, this)*ULTIMATE_COST_MP_FACTOR);
         this.timers.dodge = Math.max(this.timers.dodge, getCD(SKILL_STATS.dodge.cd, this)*ULTIMATE_COST_MP_FACTOR);
         this.timers.form = Math.max(this.timers.form, getCD(SKILL_STATS.switch.cd, this)*ULTIMATE_COST_MP_FACTOR);
         
         const dir = this.facingRight ? 1 : -1;
         const center = this.getCenter();
          switch(element) {
              case 'fire':
                 const fireX = this.facingRight ? center.x : center.x - stats.width;
                 areaEffects.push(new AreaEffect(fireX, this.y-this.height*0.5, stats.width, this.height*1.5, COLORS.fire, 'ulti_fire', this, stats, stats.duration, 0));
                 break;
             case 'water': // Tsunami
                 const waveHeight = 80, waveWidth = 60;
                 const waveX = this.facingRight ? center.x : center.x - waveWidth;
                 areaEffects.push(new AreaEffect(waveX, GROUND_LEVEL - waveHeight, waveWidth, waveHeight, COLORS.water, 'ulti_water', this, stats, stats.duration, dir * stats.push));
                 break;
              case 'earth': // Quake
                 addEffect(center.x, GROUND_LEVEL, COLORS.earth, stats.radius*2, 40, 'ring', 3);
                 if(target.isOnGround && Math.abs(target.x - this.x) < stats.radius) {
                    target.takeDamage(getStatMod(stats.dmg, this.form), dir*stats.knockback, 0, 'earth', true); // Always break block
                    target.vy = -12; // Launch
                  }
                 break;
               case 'wind': // Giant Tornado
                  areaEffects.push(new AreaEffect(center.x - stats.size/2, GROUND_LEVEL - stats.size*2, stats.size, stats.size*2, COLORS.wind, 'ulti_wind', this, stats, stats.duration, dir * stats.speed));
                  break;
               case 'thunder': // Sky strike on target
                  const thunderX = target.getCenter().x - stats.radius/2;
                  areaEffects.push(new AreaEffect(thunderX, 0, stats.radius, canvas.height, COLORS.thunder, 'ulti_thunder', this, stats, 20, 0)); // short duration high impact
                  setScreenFlash(COLORS.thunder, 0.8);
                  break;
          }
      }


      takeDamage(amount, knockback, stun = 0, element='none', blockBreak = false) {
         if(this.timers.invincible > 0 || amount <=0 ) return;
         amount = Math.round(amount);
         const isBlockingCorrectly = this.isBlocking && (knockback>0) !== this.facingRight;

         if(isBlockingCorrectly && !blockBreak) {
             amount *= 0.15; knockback *= 0.3; this.timers.blockStun = 20; stun = 0; this.mana = Math.max(0, this.mana - amount*3); // mana cost
             addEffect(this.getCenter().x, this.getCenter().y, COLORS.block, 50, 20, 'ring', 1);
         } else {
            if (isBlockingCorrectly && blockBreak) { 
                this.isBlocking = false; this.timers.blockStun = 40; knockback *= 1.2; stun = Math.max(stun, 25);
                addEffect(this.getCenter().x, this.getCenter().y, COLORS.earth, 70, 30, 'ring', 3);
            }
             this.cancelCharge();
             this.timers.hitStun = 25; this.timers.attacking = 0; this.timers.casting = 0; this.isBlocking = false;
             this.timers.stun = Math.max(this.timers.stun, stun); 
             addEffect(this.getCenter().x, this.getCenter().y, COLORS.hit, 40, 15, 'spark', amount > 30 ? 8: 4);
             if(ELEMENTS.includes(element)) this.setStateEffect(element, 30);
         }
         this.health = clamp(this.health - amount, 0, MAX_HEALTH); this.vx = knockback;
         if (Math.abs(knockback) > 5 && !this.isBlocking) this.vy = Math.min(this.vy, -3); 
         if(this.health <= 0) gameState = "GAMEOVER";
      }

       runAI(target) { // Basic AI for new mechanics
            this.aiActionTimer--; this.aiBlockTimer--;
             this.platformDrop = false;
             if(this.isCharging) { // continue charge
                 if(this.aiChargeTimer-- <= 0 || distance(this.getCenter(), target.getCenter()) < 80) this.releaseCharge(target);
                 return;
             }
             if(this.aiBlockTimer > 0) {this.block(true); return;} else this.block(false);

            if(this.aiActionTimer > 0 || !this.canAct()) return;
            this.aiActionTimer = Math.floor(Math.random() * 40 + 30); // Slower decisions

            const dist = distance(this.getCenter(), target.getCenter());
            const dir = target.x > this.x ? 1 : -1;
            const react = Math.random() < this.aiAggression;
             const canAfford = (el) => this.mana >= getStatMod(SKILL_STATS[el].mp, this.form) && this.skillCooldowns[el]===0;
             const risky = this.health < MAX_HEALTH * 0.3;

             // ULTIMATE
             if (this.canUseUltimate() && react && Math.random() < 0.2 && dist < 300) {
                 const possible = ELEMENTS.filter(e => SKILL_STATS['ulti_'+e]);
                 if (possible.length > 0) {
                    this.isBlocking = true; // fake block press
                    this.useUltimate(possible[Math.floor(Math.random()*possible.length)], target);
                    this.isBlocking = false;
                    return;
                 }
             }
             // Form
             if (this.health <= HP_THRESHOLD_FORM && this.form === 'none' && this.timers.form === 0 && react) { this.switchForm(); return; }

             // Evasion
             let evading = false;
             projectiles.concat(areaEffects).forEach( p => {
                 if(p.owner !== this && distance(p.getHitbox(), this.getCenter()) < 120 && react && !evading) {
                    if(this.timers.dodge === 0 && Math.random() < 0.6) this.dodge(p.x < this.x ? 1 : -1); 
                    else if(this.isOnGround) { this.block(true); this.aiBlockTimer = 30; }
                    evading = true;
                 }
             });
             if(evading) return;
             if((target.timers.attacking > 0 || target.timers.casting>0) && dist < 90 && react) {this.block(true); this.aiBlockTimer = 40; return;}

            // Skills / Attack
            if(react && this.mana > MAX_MANA * 0.2) { // Conserve some mana
                let skillToUse = null;
                if(risky && canAfford('water')) skillToUse = 'water';
                 else if (target.isBlocking && dist < 120 && canAfford('earth')) skillToUse = 'earth';
                 else if (dist > 150 && dist < 400) {
                    if (canAfford('fire') && Math.random()<0.4) skillToUse = 'fire';
                    else if (canAfford('wind') && Math.random()<0.4) skillToUse = 'wind';
                    else if (canAfford('thunder') && Math.random()<0.3) skillToUse = 'thunder';
                 } else if (dist < SKILL_STATS.attack.range*1.1 && Math.random() < 0.7) { this.attack(); return;}

                 if(skillToUse) { // Start Charging a skill
                    this.startCharge(skillToUse);
                    this.aiChargeTimer = Math.random() * MAX_CHARGE_TIME * 0.8 + MAX_CHARGE_TIME*0.2; // Random charge time
                     return;
                 }
            }

            // Movement (simplified platforming)
             const yDiff = this.y - target.y;
             if (yDiff > this.height * 1.8 && this.isOnGround) this.jump();
             else if (yDiff < -this.height * 1.5 && this.currentPlatform && !this.currentPlatform.isGround) this.platformDrop = true;
            const preferredDist = risky ? 250 : 120;
            if (dist > preferredDist) this.move(dir); else if (dist < preferredDist - 40) this.move(-dir);
            if(risky && this.timers.dodge === 0 && react) this.dodge(-dir);
       }
  }

  let player1, player2; 
  function resetGame() {
     player1 = new Fighter(150, GROUND_LEVEL - PLAYER_HEIGHT, COLORS.none, false, "玩家 1");
     player2 = new Fighter(WORLD_WIDTH - 150 - PLAYER_WIDTH, GROUND_LEVEL - PLAYER_HEIGHT, COLORS.none, true, "电脑 AI");
     projectiles = []; effects = []; areaEffects = [];
     gameState = "PLAYING"; gameTimer = 0; cameraX = 0; keysPressed = {}; screenFlash.alpha = 0;
  }

  function updateCamera() {
      const targetX = (player1.x + player2.x) / 2 - canvas.width/2; 
       cameraX = cameraX + (targetX - cameraX) * 0.04; // smooth follow
       cameraX = clamp(cameraX, 0, WORLD_WIDTH - canvas.width);
  }

 function drawGradientBar(ctx, x, y, w, h, val, max, color1, color2, label, penalty, canUse) {
      ctx.fillStyle = '#111'; ctx.fillRect(x-1, y-1, w+2, h+2);
      const grad = ctx.createLinearGradient(x, y, x+w, y);
      grad.addColorStop(0, color1); grad.addColorStop(1, color2);
      ctx.fillStyle = grad;
      const fillW = w * clamp(val / max, 0, 1);
      ctx.fillRect(x, y, fillW, h);
      ctx.strokeStyle= penalty? COLORS.penalty : (canUse? COLORS.light :'#444'); 
       ctx.lineWidth= canUse ? 2 : 1; 
       ctx.strokeRect(x,y,w,h);
       ctx.fillStyle = '#FFF'; ctx.font = '11px monospace'; ctx.shadowColor = '#000'; ctx.shadowBlur = 2;
       ctx.textAlign = 'left';
       ctx.fillText(`${label}: ${Math.round(val)}/${max}`, x + 5, y + h - 3);
       ctx.shadowBlur = 0;
  }
    function drawIcon(ctx, x, y, size, element, cd, canAfford, form, isAI=false) {
        const ready = cd === 0 && canAfford;
        ctx.fillStyle = COLORS[element];
        ctx.globalAlpha = ready ? 1 : 0.3;
        ctx.fillRect(x, y, size, size);
        ctx.strokeStyle = ready ? COLORS.light : '#555'; ctx.strokeRect(x,y,size,size);
         ctx.fillStyle = ready ? '#000' : '#CCC';
         if(!isAI) ctx.fillText(KEY_MAP[element], x+2, y+size-4);
        if (cd > 0) {
           ctx.globalAlpha = 0.8; ctx.fillStyle = '#FFF';
           ctx.fillText(Math.ceil(cd/60), x+size/2-5, y+size+10);
        }
        ctx.globalAlpha = 1;
    }

  function drawHUD() {
       const barW = 250, barH_hp=18, barH_mp=14;
       const hpColor = player1.health > HP_THRESHOLD_FORM ? COLORS.health_high : (player1.health > MAX_HEALTH*0.2 ? COLORS.health_mid: COLORS.health_low);
       const canUlti1 = player1.canUseUltimate();
       drawGradientBar(ctx, 20, 20, barW, barH_hp, player1.health, MAX_HEALTH, hpColor, '#301010', "HP", false, canUlti1);
       drawGradientBar(ctx, 20, 42, barW, barH_mp, player1.mana, MAX_MANA, COLORS.mana, '#101030', "MP", player1.mana<=1, false);
       
       const hpColor2 = player2.health > HP_THRESHOLD_FORM ? COLORS.health_high : (player2.health > MAX_HEALTH*0.2 ? COLORS.health_mid: COLORS.health_low);
        const canUlti2 = player2.canUseUltimate();
        ctx.textAlign = 'right';
        drawGradientBar(ctx, canvas.width-20-barW, 20, barW, barH_hp, player2.health, MAX_HEALTH, hpColor2, '#301010', "HP", false, canUlti2);
        drawGradientBar(ctx, canvas.width-20-barW, 42, barW, barH_mp, player2.mana, MAX_MANA, COLORS.mana, '#101030', "MP", player2.mana<=1, false);
        
       ctx.fillStyle = '#FFF';  ctx.font = '14px monospace';
       ctx.fillText(player2.name, canvas.width-25, 34); ctx.textAlign = 'left'; ctx.fillText(player1.name, 25, 34);

        // Skills P1
        let skillX = 20, iconSize = 20;
        ELEMENTS.forEach( el => {
            const cost = getStatMod(SKILL_STATS[el].mp, player1.form);
            drawIcon(ctx, skillX, 65, iconSize, el, player1.skillCooldowns[el], player1.mana >= cost, player1.form, false);
            skillX += iconSize + 4;
        });
        ctx.fillStyle = COLORS[player1.form]||COLORS.none; ctx.fillText(player1.form.toUpperCase(), skillX+10, 80);
        // Skills P2
         skillX = canvas.width - 20 - (iconSize+4)*5;
         ELEMENTS.forEach( el => {
             const cost = getStatMod(SKILL_STATS[el].mp, player2.form);
            drawIcon(ctx, skillX, 65, iconSize, el, player2.skillCooldowns[el], player2.mana >= cost, player2.form, true);
             skillX += iconSize + 4;
         });
         ctx.fillStyle = COLORS[player2.form]||COLORS.none; ctx.textAlign='right'; ctx.fillText(player2.form.toUpperCase(), canvas.width-20-(iconSize+4)*5 - 10, 80);

        if(player1.mana <= 1) {ctx.fillStyle=COLORS.penalty; ctx.fillText("MP EMPTY! CD PENALTY!", 20, 110); }
         if(player2.mana <= 1) {ctx.fillStyle=COLORS.penalty; ctx.fillText("MP EMPTY! CD PENALTY!", canvas.width-20, 110); }
         ctx.textAlign='left';
  }

  function handlePlayerInput() {
     if(!isActive) return;
     player1.vx = 0; 
     if(!player1.isCharging) player1.block(false); // only allow block if not charging
     player1.platformDrop = false;

     if (keysPressed[KEYS.DOWN_BLOCK] && player1.isOnGround) { // Block or drop
          if(player1.currentPlatform && !player1.currentPlatform.isGround) player1.platformDrop = true;
          else player1.block(true);
       }
      if(player1.canMove()){ // Move
        if (keysPressed[KEYS.LEFT]) player1.move(-1);
        if (keysPressed[KEYS.RIGHT]) player1.move(1);
      }
       if (keysPressed[KEYS.DODGE_MOD] && player1.canAct()) { // Dodge
           if (keysPressed[KEYS.LEFT]) player1.dodge(-1);
           else if (keysPressed[KEYS.RIGHT]) player1.dodge(1);
       }
  }

  function gameLoop() {
    if (!isActive) { requestAnimationFrame(gameLoop); return; } // Pause logic
    gameTimer++;
    ctx.fillStyle = ctx.createLinearGradient(0,0,0,canvas.height);
    ctx.fillStyle.addColorStop(0, COLORS.bg_grad1); ctx.fillStyle.addColorStop(1, COLORS.bg_grad2);
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    updateCamera();

    ctx.save(); ctx.translate(-cameraX, 0);
     PLATFORMS.forEach(p => { ctx.fillStyle = p.isGround ? '#3a2a2a' : '#444'; ctx.fillRect(p.x, p.y, p.width, p.height); }); // draw platforms
    if (gameState === "PLAYING") {
        handlePlayerInput();
        player1.update(player2); player2.update(player1);
        projectiles = projectiles.filter(p => p.update(p.owner === player1 ? player2 : player1, projectiles));
        areaEffects = areaEffects.filter(a => a.update(a.owner === player1 ? player2 : player1));
        effects = effects.filter(e => e.update());
    }
     areaEffects.forEach(a => a.draw(ctx)); player1.draw(ctx); player2.draw(ctx);
     projectiles.forEach(p => p.draw(ctx)); effects.forEach(e => e.draw(ctx));
    ctx.restore(); 

     drawHUD(); 
     if(screenFlash.alpha > 0) { // Flash
        ctx.fillStyle = screenFlash.color; ctx.globalAlpha = screenFlash.alpha;
        ctx.fillRect(0,0,canvas.width, canvas.height);
        screenFlash.alpha -= 0.02; ctx.globalAlpha = 1;
     }

      if(gameState === "GAMEOVER") {
          ctx.fillStyle = 'rgba(0, 0, 0, 0.85)'; ctx.fillRect(0, 0, canvas.width, canvas.height);
          ctx.fillStyle = '#FFF'; ctx.font = '36px monospace'; ctx.textAlign = 'center';
          ctx.fillText("== 战斗结束 ==", canvas.width / 2, canvas.height / 2 - 20);
           ctx.font = '18px monospace';
           ctx.fillText((player1.health <= 0 ? player2.name : player1.name) + " 胜出!", canvas.width / 2, canvas.height / 2 + 15);
           ctx.fillText("按 'TAB' 重新开始", canvas.width / 2, canvas.height / 2 + 50);
      }
    requestAnimationFrame(gameLoop);
  }

   window.addEventListener('keydown', (e) => {
     if (keysPressed[e.code] || !isActive) return; keysPressed[e.code] = true;
     if (e.code === KEYS.RESET) { resetGame(); return; } if (gameState !== "PLAYING") return;
     const element = ELEMENT_MAP[e.code];
      // Ultimate check first
      if (keysPressed[KEYS.DOWN_BLOCK] && element && player1.canUseUltimate()) {
           player1.useUltimate(element, player2);
           return; // priority
      }
     if (e.code === KEYS.JUMP) player1.jump();
     if (e.code === KEYS.ATTACK && player1.canAct()) player1.attack();
     if (e.code === KEYS.SWITCH_FORM) player1.switchForm();
     if (element && !keysPressed[KEYS.DOWN_BLOCK]) player1.startCharge(element); // Start charge if not blocking
   });
    window.addEventListener('keyup', (e) => { 
        keysPressed[e.code] = false; 
        if(!isActive) return;
        const element = ELEMENT_MAP[e.code];
        if(element && player1.isCharging && player1.chargeElement === element) {
            player1.releaseCharge(player2); // Release on key up
        }
         if(e.code === KEYS.DOWN_BLOCK) player1.block(false); // Ensure block stops
    });

   resetGame();
   requestAnimationFrame(gameLoop);
</script>
</body>
</html>