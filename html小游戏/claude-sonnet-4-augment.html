<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>火柴人格斗游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #gameContainer {
            position: relative;
            border: 3px solid #333;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
        }
        
        #gameCanvas {
            display: block;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 70%, #8B4513 100%);
        }
        
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            display: flex;
            justify-content: space-between;
            color: white;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        
        .health-bar {
            width: 200px;
            height: 20px;
            background: rgba(255,255,255,0.3);
            border: 2px solid #333;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .health-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff4444, #ffaa44);
            transition: width 0.3s ease;
        }
        
        .skills {
            display: flex;
            gap: 5px;
            margin-top: 5px;
        }
        
        .skill {
            width: 30px;
            height: 30px;
            border: 2px solid #333;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            position: relative;
        }
        
        .skill.fire { background: #ff4444; }
        .skill.water { background: #4444ff; }
        .skill.earth { background: #8B4513; }
        .skill.wind { background: #87CEEB; }
        .skill.thunder { background: #ffff44; }
        
        .skill.cooldown {
            opacity: 0.5;
            background: #666;
        }
        
        .cooldown-text {
            position: absolute;
            color: white;
            font-size: 10px;
        }
        
        #controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            font-size: 12px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        }
        
        #gameOver {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            display: none;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas" width="1000" height="600"></canvas>
        
        <div id="ui">
            <div>
                <div>玩家血量</div>
                <div class="health-bar">
                    <div class="health-fill" id="playerHealth" style="width: 100%;"></div>
                </div>
                <div class="skills">
                    <div class="skill fire" id="fireSkill">火<div class="cooldown-text" id="fireCooldown"></div></div>
                    <div class="skill water" id="waterSkill">水<div class="cooldown-text" id="waterCooldown"></div></div>
                    <div class="skill earth" id="earthSkill">土<div class="cooldown-text" id="earthCooldown"></div></div>
                    <div class="skill wind" id="windSkill">风<div class="cooldown-text" id="windCooldown"></div></div>
                    <div class="skill thunder" id="thunderSkill">雷<div class="cooldown-text" id="thunderCooldown"></div></div>
                </div>
            </div>
            
            <div>得分: <span id="score">0</span></div>
            
            <div>
                <div>敌人血量</div>
                <div class="health-bar">
                    <div class="health-fill" id="enemyHealth" style="width: 100%;"></div>
                </div>
            </div>
        </div>
        
        <div id="controls">
            控制: WASD移动 | 空格跳跃 | J普攻 | K格挡 | L闪避 | 12345元素技能
        </div>
        
        <div id="gameOver">
            <h2 id="gameOverText">游戏结束</h2>
            <p id="finalScore">最终得分: 0</p>
            <button onclick="restartGame()">重新开始</button>
        </div>
    </div>

    <script>
        // 游戏基础设置
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const GRAVITY = 0.8;
        const GROUND_Y = canvas.height - 100;
        
        // 游戏状态
        let gameRunning = true;
        let score = 0;
        let keys = {};
        
        // 粒子效果数组
        let particles = [];
        let projectiles = [];
        
        // 角色基类
        class Character {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.width = 20;
                this.height = 60;
                this.color = color;
                this.velocityX = 0;
                this.velocityY = 0;
                this.health = 100;
                this.maxHealth = 100;
                this.onGround = false;
                this.facing = 1; // 1 = 右, -1 = 左
                this.isBlocking = false;
                this.isDodging = false;
                this.isAttacking = false;
                this.attackCooldown = 0;
                this.dodgeCooldown = 0;
                this.jumpCount = 0;
                this.maxJumps = 2;
            }
            
            update() {
                // 重力
                if (!this.onGround) {
                    this.velocityY += GRAVITY;
                }
                
                // 位置更新
                this.x += this.velocityX;
                this.y += this.velocityY;
                
                // 地面碰撞
                if (this.y + this.height >= GROUND_Y) {
                    this.y = GROUND_Y - this.height;
                    this.velocityY = 0;
                    this.onGround = true;
                    this.jumpCount = 0;
                } else {
                    this.onGround = false;
                }
                
                // 边界检测
                if (this.x < 0) this.x = 0;
                if (this.x + this.width > canvas.width) this.x = canvas.width - this.width;
                
                // 冷却时间更新
                if (this.attackCooldown > 0) this.attackCooldown--;
                if (this.dodgeCooldown > 0) this.dodgeCooldown--;
                
                // 摩擦力
                this.velocityX *= 0.8;
                
                // 重置状态
                if (this.attackCooldown <= 0) this.isAttacking = false;
                if (this.dodgeCooldown <= 0) this.isDodging = false;
            }
            
            draw() {
                ctx.save();
                
                // 闪避时半透明
                if (this.isDodging) {
                    ctx.globalAlpha = 0.5;
                }
                
                // 格挡时发光效果
                if (this.isBlocking) {
                    ctx.shadowColor = '#00ffff';
                    ctx.shadowBlur = 10;
                }
                
                // 攻击时红色发光
                if (this.isAttacking) {
                    ctx.shadowColor = '#ff0000';
                    ctx.shadowBlur = 15;
                }
                
                ctx.fillStyle = this.color;
                
                // 身体
                ctx.fillRect(this.x + 5, this.y + 15, 10, 30);
                
                // 头部
                ctx.beginPath();
                ctx.arc(this.x + 10, this.y + 10, 8, 0, Math.PI * 2);
                ctx.fill();
                
                // 手臂
                const armY = this.y + 20;
                ctx.fillRect(this.x, armY, 5, 15);
                ctx.fillRect(this.x + 15, armY, 5, 15);
                
                // 腿部
                const legY = this.y + 45;
                ctx.fillRect(this.x + 3, legY, 4, 15);
                ctx.fillRect(this.x + 13, legY, 4, 15);
                
                ctx.restore();
            }
            
            jump() {
                if (this.jumpCount < this.maxJumps) {
                    this.velocityY = -15;
                    this.jumpCount++;
                    this.createJumpParticles();
                }
            }
            
            attack() {
                if (this.attackCooldown <= 0) {
                    this.isAttacking = true;
                    this.attackCooldown = 30;
                    return true;
                }
                return false;
            }
            
            block() {
                this.isBlocking = true;
            }
            
            dodge() {
                if (this.dodgeCooldown <= 0) {
                    this.isDodging = true;
                    this.dodgeCooldown = 60;
                    this.velocityX = this.facing * 8;
                    this.createDodgeParticles();
                }
            }
            
            takeDamage(damage) {
                if (this.isDodging) return false;
                
                if (this.isBlocking) {
                    damage *= 0.3; // 格挡减少70%伤害
                    this.createBlockParticles();
                }
                
                this.health -= damage;
                this.health = Math.max(0, this.health);
                this.createDamageParticles();
                return true;
            }
            
            createJumpParticles() {
                for (let i = 0; i < 5; i++) {
                    particles.push(new Particle(
                        this.x + this.width/2 + (Math.random() - 0.5) * 20,
                        this.y + this.height,
                        (Math.random() - 0.5) * 4,
                        Math.random() * 2,
                        '#ffffff',
                        20
                    ));
                }
            }
            
            createDodgeParticles() {
                for (let i = 0; i < 8; i++) {
                    particles.push(new Particle(
                        this.x + Math.random() * this.width,
                        this.y + Math.random() * this.height,
                        (Math.random() - 0.5) * 6,
                        (Math.random() - 0.5) * 6,
                        '#00ffff',
                        30
                    ));
                }
            }
            
            createBlockParticles() {
                for (let i = 0; i < 6; i++) {
                    particles.push(new Particle(
                        this.x + this.width/2,
                        this.y + this.height/2,
                        (Math.random() - 0.5) * 8,
                        (Math.random() - 0.5) * 8,
                        '#ffff00',
                        25
                    ));
                }
            }
            
            createDamageParticles() {
                for (let i = 0; i < 10; i++) {
                    particles.push(new Particle(
                        this.x + Math.random() * this.width,
                        this.y + Math.random() * this.height,
                        (Math.random() - 0.5) * 8,
                        (Math.random() - 0.5) * 8,
                        '#ff0000',
                        40
                    ));
                }
            }
        }

        // 玩家类
        class Player extends Character {
            constructor(x, y) {
                super(x, y, '#0066ff');
                this.skills = {
                    fire: { cooldown: 0, maxCooldown: 120 },
                    water: { cooldown: 0, maxCooldown: 100 },
                    earth: { cooldown: 0, maxCooldown: 150 },
                    wind: { cooldown: 0, maxCooldown: 80 },
                    thunder: { cooldown: 0, maxCooldown: 200 }
                };
            }

            update() {
                super.update();

                // 更新技能冷却
                for (let skill in this.skills) {
                    if (this.skills[skill].cooldown > 0) {
                        this.skills[skill].cooldown--;
                    }
                }

                this.handleInput();
            }

            handleInput() {
                // 移动
                if (keys['a'] || keys['A']) {
                    this.velocityX = -5;
                    this.facing = -1;
                }
                if (keys['d'] || keys['D']) {
                    this.velocityX = 5;
                    this.facing = 1;
                }

                // 跳跃
                if (keys[' ']) {
                    this.jump();
                    keys[' '] = false; // 防止连续跳跃
                }

                // 攻击
                if (keys['j'] || keys['J']) {
                    if (this.attack()) {
                        this.performAttack();
                    }
                    keys['j'] = keys['J'] = false;
                }

                // 格挡
                this.isBlocking = keys['k'] || keys['K'];

                // 闪避
                if (keys['l'] || keys['L']) {
                    this.dodge();
                    keys['l'] = keys['L'] = false;
                }

                // 技能
                if (keys['1']) this.useSkill('fire');
                if (keys['2']) this.useSkill('water');
                if (keys['3']) this.useSkill('earth');
                if (keys['4']) this.useSkill('wind');
                if (keys['5']) this.useSkill('thunder');
            }

            performAttack() {
                // 检查是否击中敌人
                const attackRange = 40;
                const attackX = this.facing > 0 ? this.x + this.width : this.x - attackRange;

                if (enemy &&
                    enemy.x < attackX + attackRange &&
                    enemy.x + enemy.width > attackX &&
                    Math.abs(enemy.y - this.y) < 50) {

                    if (enemy.takeDamage(15)) {
                        score += 10;
                        updateUI();
                    }
                }

                // 攻击特效
                this.createAttackEffect();
            }

            createAttackEffect() {
                const effectX = this.facing > 0 ? this.x + this.width : this.x - 30;
                for (let i = 0; i < 8; i++) {
                    particles.push(new Particle(
                        effectX + Math.random() * 30,
                        this.y + 20 + Math.random() * 20,
                        this.facing * (2 + Math.random() * 4),
                        (Math.random() - 0.5) * 4,
                        '#ffaa00',
                        20
                    ));
                }
            }

            useSkill(skillType) {
                if (this.skills[skillType].cooldown > 0) return;

                this.skills[skillType].cooldown = this.skills[skillType].maxCooldown;

                switch(skillType) {
                    case 'fire':
                        this.fireSkill();
                        break;
                    case 'water':
                        this.waterSkill();
                        break;
                    case 'earth':
                        this.earthSkill();
                        break;
                    case 'wind':
                        this.windSkill();
                        break;
                    case 'thunder':
                        this.thunderSkill();
                        break;
                }

                updateUI();
            }

            fireSkill() {
                // 火球攻击
                const fireball = new Projectile(
                    this.x + this.width/2,
                    this.y + this.height/2,
                    this.facing * 8,
                    0,
                    '#ff4444',
                    25,
                    'fire'
                );
                projectiles.push(fireball);

                // 火焰粒子效果
                for (let i = 0; i < 15; i++) {
                    particles.push(new Particle(
                        this.x + this.width/2,
                        this.y + this.height/2,
                        (Math.random() - 0.5) * 6,
                        (Math.random() - 0.5) * 6,
                        Math.random() > 0.5 ? '#ff4444' : '#ffaa00',
                        40
                    ));
                }
            }

            waterSkill() {
                // 水波攻击
                for (let i = 0; i < 3; i++) {
                    const waterball = new Projectile(
                        this.x + this.width/2,
                        this.y + this.height/2,
                        this.facing * (6 + i),
                        -2 + i,
                        '#4444ff',
                        20,
                        'water'
                    );
                    projectiles.push(waterball);
                }

                // 水花效果
                for (let i = 0; i < 12; i++) {
                    particles.push(new Particle(
                        this.x + this.width/2,
                        this.y + this.height/2,
                        (Math.random() - 0.5) * 8,
                        (Math.random() - 0.5) * 8,
                        '#4444ff',
                        35
                    ));
                }
            }

            earthSkill() {
                // 地刺攻击
                if (enemy) {
                    const spikes = new EarthSpike(enemy.x, GROUND_Y - 40);
                    projectiles.push(spikes);
                }

                // 土石效果
                for (let i = 0; i < 20; i++) {
                    particles.push(new Particle(
                        this.x + Math.random() * 100 - 50,
                        GROUND_Y,
                        (Math.random() - 0.5) * 4,
                        -Math.random() * 8,
                        '#8B4513',
                        50
                    ));
                }
            }

            windSkill() {
                // 风刃攻击
                for (let i = 0; i < 5; i++) {
                    const windBlade = new Projectile(
                        this.x + this.width/2,
                        this.y + this.height/2 - 20 + i * 10,
                        this.facing * 10,
                        0,
                        '#87CEEB',
                        15,
                        'wind'
                    );
                    projectiles.push(windBlade);
                }

                // 风的效果
                for (let i = 0; i < 25; i++) {
                    particles.push(new Particle(
                        this.x + this.width/2,
                        this.y + Math.random() * this.height,
                        this.facing * (4 + Math.random() * 6),
                        (Math.random() - 0.5) * 2,
                        '#87CEEB',
                        30
                    ));
                }
            }

            thunderSkill() {
                // 雷电攻击
                if (enemy) {
                    const lightning = new Lightning(enemy.x + enemy.width/2, 0, enemy.y + enemy.height/2);
                    projectiles.push(lightning);

                    if (enemy.takeDamage(30)) {
                        score += 20;
                    }
                }

                // 雷电效果
                for (let i = 0; i < 30; i++) {
                    particles.push(new Particle(
                        this.x + this.width/2 + (Math.random() - 0.5) * 100,
                        Math.random() * canvas.height,
                        (Math.random() - 0.5) * 10,
                        (Math.random() - 0.5) * 10,
                        '#ffff44',
                        25
                    ));
                }
            }
        }

        // 敌人AI类
        class Enemy extends Character {
            constructor(x, y) {
                super(x, y, '#ff4444');
                this.aiState = 'idle';
                this.aiTimer = 0;
                this.attackRange = 60;
                this.detectionRange = 200;
                this.lastPlayerX = 0;
                this.aggressiveness = Math.random() * 0.5 + 0.3; // 0.3-0.8的攻击性
            }

            update() {
                super.update();
                this.updateAI();
            }

            updateAI() {
                if (!player) return;

                const distanceToPlayer = Math.abs(this.x - player.x);
                const playerDirection = player.x > this.x ? 1 : -1;
                this.facing = playerDirection;

                this.aiTimer++;

                switch(this.aiState) {
                    case 'idle':
                        if (distanceToPlayer < this.detectionRange) {
                            this.aiState = 'chase';
                            this.aiTimer = 0;
                        }
                        break;

                    case 'chase':
                        if (distanceToPlayer > this.attackRange) {
                            // 追击玩家
                            this.velocityX = playerDirection * 3;

                            // 随机跳跃
                            if (Math.random() < 0.02 && this.onGround) {
                                this.jump();
                            }
                        } else {
                            this.aiState = 'combat';
                            this.aiTimer = 0;
                        }
                        break;

                    case 'combat':
                        if (distanceToPlayer > this.attackRange * 1.5) {
                            this.aiState = 'chase';
                            break;
                        }

                        // 战斗AI逻辑
                        if (this.aiTimer > 30) {
                            const action = Math.random();

                            if (action < this.aggressiveness) {
                                // 攻击
                                if (this.attack()) {
                                    this.performAttack();
                                }
                            } else if (action < this.aggressiveness + 0.2) {
                                // 格挡
                                this.block();
                            } else if (action < this.aggressiveness + 0.3) {
                                // 闪避
                                this.dodge();
                            } else {
                                // 移动
                                if (Math.random() < 0.5) {
                                    this.velocityX = playerDirection * 2;
                                } else {
                                    this.velocityX = -playerDirection * 2;
                                }
                            }

                            this.aiTimer = 0;
                        }

                        // 反应玩家攻击
                        if (player.isAttacking && distanceToPlayer < this.attackRange) {
                            if (Math.random() < 0.4) {
                                this.block();
                            } else if (Math.random() < 0.3) {
                                this.dodge();
                            }
                        }
                        break;
                }
            }

            performAttack() {
                // 检查是否击中玩家
                const attackRange = 40;
                const attackX = this.facing > 0 ? this.x + this.width : this.x - attackRange;

                if (player &&
                    player.x < attackX + attackRange &&
                    player.x + player.width > attackX &&
                    Math.abs(player.y - this.y) < 50) {

                    player.takeDamage(12);
                    updateUI();
                }

                // 攻击特效
                this.createAttackEffect();
            }

            createAttackEffect() {
                const effectX = this.facing > 0 ? this.x + this.width : this.x - 30;
                for (let i = 0; i < 6; i++) {
                    particles.push(new Particle(
                        effectX + Math.random() * 30,
                        this.y + 20 + Math.random() * 20,
                        this.facing * (2 + Math.random() * 4),
                        (Math.random() - 0.5) * 4,
                        '#ff6666',
                        15
                    ));
                }
            }
        }

        // 粒子类
        class Particle {
            constructor(x, y, vx, vy, color, life) {
                this.x = x;
                this.y = y;
                this.vx = vx;
                this.vy = vy;
                this.color = color;
                this.life = life;
                this.maxLife = life;
                this.size = Math.random() * 4 + 2;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.vy += 0.2; // 重力
                this.vx *= 0.98; // 阻力
                this.life--;

                return this.life > 0;
            }

            draw() {
                const alpha = this.life / this.maxLife;
                ctx.save();
                ctx.globalAlpha = alpha;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size * alpha, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 弹射物基类
        class Projectile {
            constructor(x, y, vx, vy, color, damage, type) {
                this.x = x;
                this.y = y;
                this.vx = vx;
                this.vy = vy;
                this.color = color;
                this.damage = damage;
                this.type = type;
                this.life = 120;
                this.size = 8;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life--;

                // 检查碰撞
                if (this.checkCollision()) {
                    return false;
                }

                // 边界检查
                if (this.x < 0 || this.x > canvas.width || this.y > canvas.height) {
                    return false;
                }

                return this.life > 0;
            }

            checkCollision() {
                if (enemy &&
                    this.x < enemy.x + enemy.width &&
                    this.x + this.size > enemy.x &&
                    this.y < enemy.y + enemy.height &&
                    this.y + this.size > enemy.y) {

                    if (enemy.takeDamage(this.damage)) {
                        score += 15;
                        updateUI();
                    }

                    this.createHitEffect();
                    return true;
                }
                return false;
            }

            createHitEffect() {
                for (let i = 0; i < 8; i++) {
                    particles.push(new Particle(
                        this.x,
                        this.y,
                        (Math.random() - 0.5) * 8,
                        (Math.random() - 0.5) * 8,
                        this.color,
                        20
                    ));
                }
            }

            draw() {
                ctx.save();
                ctx.fillStyle = this.color;
                ctx.shadowColor = this.color;
                ctx.shadowBlur = 10;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 地刺类
        class EarthSpike extends Projectile {
            constructor(x, y) {
                super(x, y, 0, 0, '#8B4513', 20, 'earth');
                this.height = 0;
                this.maxHeight = 60;
                this.growing = true;
                this.life = 90;
            }

            update() {
                if (this.growing) {
                    this.height += 4;
                    if (this.height >= this.maxHeight) {
                        this.growing = false;
                    }
                } else {
                    this.life--;
                }

                // 检查碰撞
                if (this.checkCollision()) {
                    return false;
                }

                return this.life > 0;
            }

            checkCollision() {
                if (enemy &&
                    this.x < enemy.x + enemy.width &&
                    this.x + 20 > enemy.x &&
                    this.y - this.height < enemy.y + enemy.height &&
                    this.y > enemy.y) {

                    if (enemy.takeDamage(this.damage)) {
                        score += 15;
                        updateUI();
                    }

                    this.createHitEffect();
                    return true;
                }
                return false;
            }

            draw() {
                ctx.save();
                ctx.fillStyle = this.color;
                ctx.fillRect(this.x, this.y - this.height, 20, this.height);

                // 尖端
                ctx.beginPath();
                ctx.moveTo(this.x, this.y - this.height);
                ctx.lineTo(this.x + 10, this.y - this.height - 10);
                ctx.lineTo(this.x + 20, this.y - this.height);
                ctx.fill();

                ctx.restore();
            }
        }

        // 雷电类
        class Lightning extends Projectile {
            constructor(x, startY, endY) {
                super(x, startY, 0, 0, '#ffff44', 30, 'thunder');
                this.endY = endY;
                this.segments = [];
                this.life = 20;
                this.generateLightning();
            }

            generateLightning() {
                const segmentCount = 10;
                const segmentHeight = (this.endY - this.y) / segmentCount;

                for (let i = 0; i <= segmentCount; i++) {
                    this.segments.push({
                        x: this.x + (Math.random() - 0.5) * 30,
                        y: this.y + i * segmentHeight
                    });
                }
            }

            update() {
                this.life--;

                // 重新生成闪电路径
                if (this.life % 3 === 0) {
                    this.generateLightning();
                }

                return this.life > 0;
            }

            draw() {
                ctx.save();
                ctx.strokeStyle = this.color;
                ctx.lineWidth = 3;
                ctx.shadowColor = this.color;
                ctx.shadowBlur = 15;

                ctx.beginPath();
                ctx.moveTo(this.segments[0].x, this.segments[0].y);

                for (let i = 1; i < this.segments.length; i++) {
                    ctx.lineTo(this.segments[i].x, this.segments[i].y);
                }

                ctx.stroke();
                ctx.restore();
            }
        }

        // 游戏对象
        let player, enemy;

        // 初始化游戏
        function initGame() {
            player = new Player(100, GROUND_Y - 60);
            enemy = new Enemy(canvas.width - 150, GROUND_Y - 60);
            particles = [];
            projectiles = [];
            score = 0;
            gameRunning = true;
            updateUI();
        }

        // 更新UI
        function updateUI() {
            document.getElementById('playerHealth').style.width = (player.health / player.maxHealth * 100) + '%';
            document.getElementById('enemyHealth').style.width = (enemy.health / enemy.maxHealth * 100) + '%';
            document.getElementById('score').textContent = score;

            // 更新技能冷却显示
            for (let skill in player.skills) {
                const skillElement = document.getElementById(skill + 'Skill');
                const cooldownElement = document.getElementById(skill + 'Cooldown');

                if (player.skills[skill].cooldown > 0) {
                    skillElement.classList.add('cooldown');
                    cooldownElement.textContent = Math.ceil(player.skills[skill].cooldown / 60);
                } else {
                    skillElement.classList.remove('cooldown');
                    cooldownElement.textContent = '';
                }
            }
        }

        // 游戏主循环
        function gameLoop() {
            if (!gameRunning) return;

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景
            drawBackground();

            // 更新和绘制角色
            if (player) {
                player.update();
                player.draw();
            }

            if (enemy) {
                enemy.update();
                enemy.draw();
            }

            // 更新和绘制粒子
            particles = particles.filter(particle => {
                const alive = particle.update();
                if (alive) particle.draw();
                return alive;
            });

            // 更新和绘制弹射物
            projectiles = projectiles.filter(projectile => {
                const alive = projectile.update();
                if (alive) projectile.draw();
                return alive;
            });

            // 检查游戏结束
            checkGameOver();

            // 生成新敌人
            if (enemy && enemy.health <= 0) {
                setTimeout(() => {
                    enemy = new Enemy(canvas.width - 150, GROUND_Y - 60);
                    enemy.health = Math.min(100 + score / 10, 200); // 敌人血量随分数增加
                    enemy.maxHealth = enemy.health;
                    enemy.aggressiveness = Math.min(0.8, 0.3 + score / 500); // 攻击性增加
                    updateUI();
                }, 2000);
            }

            requestAnimationFrame(gameLoop);
        }

        // 绘制背景
        function drawBackground() {
            // 地面
            ctx.fillStyle = '#228B22';
            ctx.fillRect(0, GROUND_Y, canvas.width, canvas.height - GROUND_Y);

            // 地面装饰
            ctx.fillStyle = '#32CD32';
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.fillRect(i, GROUND_Y, 30, 10);
            }

            // 云朵
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            for (let i = 0; i < 5; i++) {
                const x = (i * 200 + Date.now() * 0.01) % (canvas.width + 100) - 50;
                const y = 50 + i * 20;
                drawCloud(x, y);
            }
        }

        function drawCloud(x, y) {
            ctx.beginPath();
            ctx.arc(x, y, 20, 0, Math.PI * 2);
            ctx.arc(x + 25, y, 25, 0, Math.PI * 2);
            ctx.arc(x + 50, y, 20, 0, Math.PI * 2);
            ctx.arc(x + 15, y - 15, 15, 0, Math.PI * 2);
            ctx.arc(x + 35, y - 15, 15, 0, Math.PI * 2);
            ctx.fill();
        }

        // 检查游戏结束
        function checkGameOver() {
            if (player && player.health <= 0) {
                gameRunning = false;
                showGameOver(false);
            }
        }

        // 显示游戏结束界面
        function showGameOver(victory) {
            const gameOverDiv = document.getElementById('gameOver');
            const gameOverText = document.getElementById('gameOverText');
            const finalScore = document.getElementById('finalScore');

            gameOverText.textContent = victory ? '胜利！' : '游戏结束';
            finalScore.textContent = '最终得分: ' + score;
            gameOverDiv.style.display = 'block';
        }

        // 重新开始游戏
        function restartGame() {
            document.getElementById('gameOver').style.display = 'none';
            initGame();
            gameLoop();
        }

        // 键盘事件处理
        document.addEventListener('keydown', (e) => {
            keys[e.key] = true;
            e.preventDefault();
        });

        document.addEventListener('keyup', (e) => {
            keys[e.key] = false;
            e.preventDefault();
        });

        // 防止页面滚动
        document.addEventListener('keydown', (e) => {
            if(['Space','ArrowUp','ArrowDown','ArrowLeft','ArrowRight'].indexOf(e.code) > -1) {
                e.preventDefault();
            }
        }, false);

        // 窗口失去焦点时暂停游戏
        window.addEventListener('blur', () => {
            // 清空按键状态
            keys = {};
        });

        // 触摸控制支持（移动设备）
        let touchStartX = 0;
        let touchStartY = 0;

        canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            touchStartX = touch.clientX;
            touchStartY = touch.clientY;
        });

        canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
        });

        canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            const touch = e.changedTouches[0];
            const deltaX = touch.clientX - touchStartX;
            const deltaY = touch.clientY - touchStartY;

            // 根据滑动方向执行动作
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                if (Math.abs(deltaX) > 30) {
                    if (deltaX > 0) {
                        keys['d'] = true;
                        setTimeout(() => keys['d'] = false, 100);
                    } else {
                        keys['a'] = true;
                        setTimeout(() => keys['a'] = false, 100);
                    }
                }
            } else {
                if (Math.abs(deltaY) > 30) {
                    if (deltaY < 0) {
                        keys[' '] = true;
                        setTimeout(() => keys[' '] = false, 100);
                    }
                }
            }

            // 点击攻击
            if (Math.abs(deltaX) < 20 && Math.abs(deltaY) < 20) {
                keys['j'] = true;
                setTimeout(() => keys['j'] = false, 100);
            }
        });

        // 添加移动端虚拟按键
        function createMobileControls() {
            if (window.innerWidth <= 768) {
                const controlsDiv = document.createElement('div');
                controlsDiv.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    left: 20px;
                    right: 20px;
                    display: flex;
                    justify-content: space-between;
                    z-index: 1000;
                `;

                // 移动按钮
                const moveButtons = document.createElement('div');
                moveButtons.innerHTML = `
                    <button ontouchstart="keys['a']=true" ontouchend="keys['a']=false" style="margin-right: 10px;">←</button>
                    <button ontouchstart="keys['d']=true" ontouchend="keys['d']=false">→</button>
                `;

                // 动作按钮
                const actionButtons = document.createElement('div');
                actionButtons.innerHTML = `
                    <button ontouchstart="keys[' ']=true" ontouchend="keys[' ']=false" style="margin-right: 10px;">跳</button>
                    <button ontouchstart="keys['j']=true" ontouchend="keys['j']=false" style="margin-right: 10px;">攻击</button>
                    <button ontouchstart="keys['k']=true" ontouchend="keys['k']=false">格挡</button>
                `;

                controlsDiv.appendChild(moveButtons);
                controlsDiv.appendChild(actionButtons);
                document.body.appendChild(controlsDiv);
            }
        }

        // 游戏启动
        window.addEventListener('load', () => {
            createMobileControls();
            initGame();
            gameLoop();

            // 显示加载完成提示
            setTimeout(() => {
                alert('游戏加载完成！\n\n控制说明：\nWASD - 移动\n空格 - 跳跃\nJ - 普攻\nK - 格挡\nL - 闪避\n1-5 - 元素技能（火水土风雷）\n\n击败敌人获得分数，敌人会越来越强！');
            }, 500);
        });

        // 调整画布大小
        function resizeCanvas() {
            const container = document.getElementById('gameContainer');
            const maxWidth = window.innerWidth - 40;
            const maxHeight = window.innerHeight - 40;

            if (maxWidth < 1000 || maxHeight < 600) {
                const scale = Math.min(maxWidth / 1000, maxHeight / 600);
                container.style.transform = `scale(${scale})`;
                container.style.transformOrigin = 'center center';
            }
        }

        window.addEventListener('resize', resizeCanvas);
        window.addEventListener('load', resizeCanvas);
    </script>
</body>
</html>
