<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>火柴人格斗游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(to bottom, #1a2980, #26d0ce);
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }
        
        #gameContainer {
            position: relative;
            width: 800px;
            height: 500px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            overflow: hidden;
        }
        
        #gameCanvas {
            background: linear-gradient(to bottom, #87CEEB, #E0F7FA);
            display: block;
        }
        
        #uiOverlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            padding: 10px;
            box-sizing: border-box;
            color: white;
            text-shadow: 1px 1px 2px black;
        }
        
        .player-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .health-bar {
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .health-fill {
            height: 100%;
            transition: width 0.3s;
        }
        
        #playerHealthFill {
            background: linear-gradient(to right, #ff0000, #ff7700);
        }
        
        #enemyHealthFill {
            background: linear-gradient(to right, #00ff00, #0077ff);
        }
        
        .controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 10px;
            color: white;
            font-size: 12px;
        }
        
        .skill-bar {
            display: flex;
            gap: 5px;
            margin-top: 5px;
        }
        
        .skill {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .skill:hover {
            transform: scale(1.1);
        }
        
        .fire { background: radial-gradient(circle, #ff5500, #8b0000); }
        .water { background: radial-gradient(circle, #00bfff, #00008b); }
        .earth { background: radial-gradient(circle, #8b4513, #5d2906); }
        .wind { background: radial-gradient(circle, #00ff00, #006400); }
        .thunder { background: radial-gradient(circle, #ffff00, #8b8b00); }
        
        #startScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 10;
        }
        
        #startButton {
            background: linear-gradient(to bottom, #ff8c00, #ff4500);
            border: none;
            color: white;
            padding: 15px 30px;
            font-size: 20px;
            border-radius: 50px;
            cursor: pointer;
            margin-top: 30px;
            box-shadow: 0 0 15px rgba(255, 140, 0, 0.7);
            transition: transform 0.2s;
        }
        
        #startButton:hover {
            transform: scale(1.05);
        }
        
        h1 {
            font-size: 40px;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #ff8c00;
        }
        
        .instructions {
            text-align: center;
            max-width: 80%;
            line-height: 1.6;
        }
        
        #gameOverScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 10;
        }
        
        #restartButton {
            background: linear-gradient(to bottom, #00ff00, #008b00);
            border: none;
            color: white;
            padding: 15px 30px;
            font-size: 20px;
            border-radius: 50px;
            cursor: pointer;
            margin-top: 30px;
            box-shadow: 0 0 15px rgba(0, 255, 0, 0.7);
            transition: transform 0.2s;
        }
        
        #restartButton:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas" width="800" height="500"></canvas>
        
        <div id="uiOverlay">
            <div class="player-info">
                <div>
                    <div>玩家</div>
                    <div class="health-bar">
                        <div id="playerHealthFill" class="health-fill" style="width: 100%;"></div>
                    </div>
                </div>
                <div>
                    <div>敌人</div>
                    <div class="health-bar">
                        <div id="enemyHealthFill" class="health-fill" style="width: 100%;"></div>
                    </div>
                </div>
            </div>
            
            <div class="skill-bar">
                <div class="skill fire" title="火球术 (1)">1</div>
                <div class="skill water" title="水弹 (2)">2</div>
                <div class="skill earth" title="土墙 (3)">3</div>
                <div class="skill wind" title="风刃 (4)">4</div>
                <div class="skill thunder" title="雷电 (5)">5</div>
            </div>
        </div>
        
        <div class="controls">
            <div>移动: A/D 或 ←/→</div>
            <div>跳跃: W 或 ↑ (双击二段跳)</div>
            <div>攻击: J</div>
            <div>格挡: K</div>
            <div>闪避: L</div>
            <div>技能: 1-5</div>
        </div>
        
        <div id="startScreen">
            <h1>🔥 火柴人格斗游戏 🔥</h1>
            <div class="instructions">
                <p>控制你的火柴人战士，使用各种元素技能击败敌人！</p>
                <p>躲避攻击，合理使用技能，成为格斗大师！</p>
            </div>
            <button id="startButton">开始游戏</button>
        </div>
        
        <div id="gameOverScreen">
            <h1 id="gameResult">游戏结束</h1>
            <button id="restartButton">重新开始</button>
        </div>
    </div>

    <script>
        // 游戏变量
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const startScreen = document.getElementById('startScreen');
        const gameOverScreen = document.getElementById('gameOverScreen');
        const startButton = document.getElementById('startButton');
        const restartButton = document.getElementById('restartButton');
        const gameResult = document.getElementById('gameResult');
        const playerHealthFill = document.getElementById('playerHealthFill');
        const enemyHealthFill = document.getElementById('enemyHealthFill');
        
        // 游戏状态
        let gameRunning = false;
        let player;
        let enemy;
        let keys = {};
        let lastJumpTime = 0;
        let jumpCount = 0;
        let projectiles = [];
        let particles = [];
        let platforms = [];
        
        // 初始化游戏
        function initGame() {
            // 创建平台
            platforms = [
                {x: 0, y: 450, width: 800, height: 50}, // 地面
                {x: 200, y: 350, width: 150, height: 20},
                {x: 450, y: 300, width: 150, height: 20},
                {x: 100, y: 250, width: 100, height: 20},
                {x: 600, y: 200, width: 100, height: 20}
            ];
            
            // 创建玩家
            player = {
                x: 100,
                y: 300,
                width: 30,
                height: 60,
                velocityX: 0,
                velocityY: 0,
                speed: 5,
                jumpPower: 15,
                isJumping: false,
                facingRight: true,
                health: 100,
                maxHealth: 100,
                state: 'idle', // idle, running, jumping, attacking, blocking, dodging, hurt
                stateTime: 0,
                invulnerable: false,
                invulnerableTime: 0,
                // 技能冷却时间
                skills: {
                    fire: 0,
                    water: 0,
                    earth: 0,
                    wind: 0,
                    thunder: 0
                },
                skillCooldown: 1000 // 1秒冷却
            };
            
            // 创建敌人
            enemy = {
                x: 600,
                y: 300,
                width: 30,
                height: 60,
                velocityX: 0,
                velocityY: 0,
                speed: 3,
                jumpPower: 12,
                isJumping: false,
                facingRight: false,
                health: 100,
                maxHealth: 100,
                state: 'idle',
                stateTime: 0,
                aiState: 'patrol', // patrol, chase, attack, retreat
                aiStateTime: 0,
                lastAttackTime: 0,
                attackCooldown: 1000
            };
            
            // 重置游戏元素
            projectiles = [];
            particles = [];
            
            // 更新UI
            updateHealthBars();
            
            // 开始游戏循环
            gameRunning = true;
            gameLoop();
        }
        
        // 更新血条
        function updateHealthBars() {
            playerHealthFill.style.width = (player.health / player.maxHealth * 100) + '%';
            enemyHealthFill.style.width = (enemy.health / enemy.maxHealth * 100) + '%';
        }
        
        // 绘制火柴人
        function drawStickman(x, y, width, height, facingRight, color, state) {
            ctx.save();
            ctx.translate(x + width/2, y + height/2);
            
            if (!facingRight) {
                ctx.scale(-1, 1);
            }
            
            // 身体
            ctx.beginPath();
            ctx.moveTo(0, -height/3);
            ctx.lineTo(0, height/3);
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 头部
            ctx.beginPath();
            ctx.arc(0, -height/2, width/2, 0, Math.PI * 2);
            ctx.stroke();
            
            // 手臂
            ctx.beginPath();
            ctx.moveTo(0, -height/6);
            
            if (state === 'attacking') {
                ctx.lineTo(width, -height/4);
            } else if (state === 'blocking') {
                ctx.lineTo(-width/2, -height/4);
            } else {
                ctx.lineTo(width/2, -height/4);
            }
            
            ctx.moveTo(0, -height/6);
            
            if (state === 'attacking') {
                ctx.lineTo(-width, -height/4);
            } else if (state === 'blocking') {
                ctx.lineTo(width/2, -height/4);
            } else {
                ctx.lineTo(-width/2, -height/4);
            }
            
            ctx.stroke();
            
            // 腿部
            ctx.beginPath();
            ctx.moveTo(0, height/3);
            
            if (state === 'running' || state === 'jumping') {
                ctx.lineTo(width/3, height/2);
            } else {
                ctx.lineTo(0, height/2);
            }
            
            ctx.moveTo(0, height/3);
            
            if (state === 'running' || state === 'jumping') {
                ctx.lineTo(-width/3, height/2);
            } else {
                ctx.lineTo(0, height/2);
            }
            
            ctx.stroke();
            
            ctx.restore();
        }
        
        // 绘制平台
        function drawPlatforms() {
            ctx.fillStyle = '#8B4513';
            platforms.forEach(platform => {
                ctx.fillRect(platform.x, platform.y, platform.width, platform.height);
                
                // 平台顶部线条
                ctx.fillStyle = '#A0522D';
                ctx.fillRect(platform.x, platform.y, platform.width, 5);
                ctx.fillStyle = '#8B4513';
            });
        }
        
        // 绘制背景
        function drawBackground() {
            // 天空渐变
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            gradient.addColorStop(0, '#87CEEB');
            gradient.addColorStop(1, '#E0F7FA');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 云朵
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.beginPath();
            ctx.arc(100, 80, 30, 0, Math.PI * 2);
            ctx.arc(140, 70, 40, 0, Math.PI * 2);
            ctx.arc(180, 80, 30, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(600, 120, 30, 0, Math.PI * 2);
            ctx.arc(640, 110, 40, 0, Math.PI * 2);
            ctx.arc(680, 120, 30, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // 绘制技能效果
        function drawProjectiles() {
            projectiles.forEach(projectile => {
                ctx.save();
                
                if (projectile.type === 'fire') {
                    ctx.fillStyle = '#FF4500';
                    ctx.beginPath();
                    ctx.arc(projectile.x, projectile.y, projectile.size, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // 火焰效果
                    ctx.fillStyle = '#FFD700';
                    ctx.beginPath();
                    ctx.arc(projectile.x + Math.random() * 10 - 5, projectile.y + Math.random() * 10 - 5, projectile.size/2, 0, Math.PI * 2);
                    ctx.fill();
                } 
                else if (projectile.type === 'water') {
                    ctx.fillStyle = '#1E90FF';
                    ctx.beginPath();
                    ctx.arc(projectile.x, projectile.y, projectile.size, 0, Math.PI * 2);
                    ctx.fill();
                }
                else if (projectile.type === 'earth') {
                    ctx.fillStyle = '#8B4513';
                    ctx.fillRect(projectile.x - projectile.size/2, projectile.y - projectile.size/2, projectile.size, projectile.size);
                }
                else if (projectile.type === 'wind') {
                    ctx.strokeStyle = '#00FF00';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.arc(projectile.x, projectile.y, projectile.size, 0, Math.PI * 2);
                    ctx.stroke();
                }
                else if (projectile.type === 'thunder') {
                    ctx.strokeStyle = '#FFFF00';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(projectile.x - projectile.size, projectile.y);
                    ctx.lineTo(projectile.x + projectile.size, projectile.y);
                    ctx.moveTo(projectile.x, projectile.y - projectile.size);
                    ctx.lineTo(projectile.x, projectile.y + projectile.size);
                    ctx.stroke();
                }
                
                ctx.restore();
            });
        }
        
        // 绘制粒子效果
        function drawParticles() {
            particles.forEach((particle, index) => {
                ctx.save();
                ctx.globalAlpha = particle.alpha;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
                
                // 更新粒子
                particle.x += particle.velocityX;
                particle.y += particle.velocityY;
                particle.alpha -= 0.02;
                particle.size -= 0.1;
                
                // 移除消失的粒子
                if (particle.alpha <= 0 || particle.size <= 0) {
                    particles.splice(index, 1);
                }
            });
        }
        
        // 创建粒子效果
        function createParticles(x, y, color, count) {
            for (let i = 0; i < count; i++) {
                particles.push({
                    x: x,
                    y: y,
                    size: Math.random() * 5 + 2,
                    velocityX: (Math.random() - 0.5) * 10,
                    velocityY: (Math.random() - 0.5) * 10,
                    color: color,
                    alpha: 1
                });
            }
        }
        
        // 碰撞检测
        function checkCollision(obj1, obj2) {
            return obj1.x < obj2.x + obj2.width &&
                   obj1.x + obj1.width > obj2.x &&
                   obj1.y < obj2.y + obj2.height &&
                   obj1.y + obj1.height > obj2.y;
        }
        
        // 平台碰撞检测
        function checkPlatformCollision(obj) {
            for (let platform of platforms) {
                if (obj.x < platform.x + platform.width &&
                    obj.x + obj.width > platform.x &&
                    obj.y + obj.height <= platform.y + 10 &&
                    obj.y + obj.height + obj.velocityY >= platform.y) {
                    obj.y = platform.y - obj.height;
                    obj.velocityY = 0;
                    obj.isJumping = false;
                    jumpCount = 0;
                    return true;
                }
            }
            return false;
        }
        
        // 更新玩家
        function updatePlayer() {
            // 处理输入
            if (keys['ArrowLeft'] || keys['a']) {
                player.velocityX = -player.speed;
                player.facingRight = false;
                if (player.state !== 'attacking' && player.state !== 'blocking' && player.state !== 'dodging') {
                    player.state = 'running';
                }
            } else if (keys['ArrowRight'] || keys['d']) {
                player.velocityX = player.speed;
                player.facingRight = true;
                if (player.state !== 'attacking' && player.state !== 'blocking' && player.state !== 'dodging') {
                    player.state = 'running';
                }
            } else {
                player.velocityX = 0;
                if (player.state === 'running') {
                    player.state = 'idle';
                }
            }
            
            // 跳跃
            if ((keys['ArrowUp'] || keys['w']) && !player.isJumping && jumpCount < 2) {
                const now = Date.now();
                if (now - lastJumpTime > 200) { // 防止误触
                    player.velocityY = -player.jumpPower;
                    player.isJumping = true;
                    jumpCount++;
                    lastJumpTime = now;
                    player.state = 'jumping';
                }
            }
            
            // 重力
            player.velocityY += 0.8;
            
            // 更新位置
            player.x += player.velocityX;
            player.y += player.velocityY;
            
            // 边界检测
            if (player.x < 0) player.x = 0;
            if (player.x > canvas.width - player.width) player.x = canvas.width - player.width;
            
            // 平台碰撞
            if (checkPlatformCollision(player)) {
                if (player.state === 'jumping') {
                    player.state = 'idle';
                }
            }
            
            // 地面碰撞
            if (player.y > canvas.height - player.height - 50) {
                player.y = canvas.height - player.height - 50;
                player.velocityY = 0;
                player.isJumping = false;
                jumpCount = 0;
                if (player.state === 'jumping') {
                    player.state = 'idle';
                }
            }
            
            // 状态时间更新
            player.stateTime++;
            
            // 恢复默认状态
            if (player.state === 'attacking' && player.stateTime > 20) {
                player.state = 'idle';
            }
            
            if (player.state === 'hurt' && player.stateTime > 30) {
                player.state = 'idle';
            }
            
            // 无敌时间
            if (player.invulnerable) {
                player.invulnerableTime++;
                if (player.invulnerableTime > 60) {
                    player.invulnerable = false;
                    player.invulnerableTime = 0;
                }
            }
            
            // 技能冷却
            for (let skill in player.skills) {
                if (player.skills[skill] > 0) {
                    player.skills[skill]--;
                }
            }
        }
        
        // 更新敌人AI
        function updateEnemy() {
            // AI状态机
            enemy.aiStateTime++;
            
            const distanceToPlayer = Math.abs(enemy.x - player.x);
            
            switch (enemy.aiState) {
                case 'patrol':
                    // 巡逻状态
                    if (distanceToPlayer < 300) {
                        enemy.aiState = 'chase';
                        enemy.aiStateTime = 0;
                    } else {
                        // 简单左右移动
                        if (enemy.aiStateTime % 120 < 60) {
                            enemy.velocityX = -enemy.speed;
                            enemy.facingRight = false;
                        } else {
                            enemy.velocityX = enemy.speed;
                            enemy.facingRight = true;
                        }
                    }
                    break;
                    
                case 'chase':
                    // 追击状态
                    if (distanceToPlayer > 350) {
                        enemy.aiState = 'patrol';
                        enemy.aiStateTime = 0;
                    } else if (distanceToPlayer < 100) {
                        enemy.aiState = 'attack';
                        enemy.aiStateTime = 0;
                    } else {
                        // 向玩家移动
                        if (enemy.x < player.x) {
                            enemy.velocityX = enemy.speed;
                            enemy.facingRight = true;
                        } else {
                            enemy.velocityX = -enemy.speed;
                            enemy.facingRight = false;
                        }
                    }
                    break;
                    
                case 'attack':
                    // 攻击状态
                    enemy.velocityX = 0;
                    
                    if (distanceToPlayer > 120) {
                        enemy.aiState = 'chase';
                        enemy.aiStateTime = 0;
                    } else if (enemy.aiStateTime > 30 && Date.now() - enemy.lastAttackTime > enemy.attackCooldown) {
                        // 执行攻击
                        enemy.state = 'attacking';
                        enemy.stateTime = 0;
                        enemy.lastAttackTime = Date.now();
                        
                        // 有概率使用技能
                        if (Math.random() < 0.3) {
                            const skills = ['fire', 'water', 'earth', 'wind', 'thunder'];
                            const randomSkill = skills[Math.floor(Math.random() * skills.length)];
                            useEnemySkill(randomSkill);
                        }
                        
                        enemy.aiStateTime = 0;
                    }
                    break;
            }
            
            // 跳跃AI
            if (Math.random() < 0.01 && !enemy.isJumping && enemy.aiState === 'chase') {
                enemy.velocityY = -enemy.jumpPower;
                enemy.isJumping = true;
            }
            
            // 重力
            enemy.velocityY += 0.8;
            
            // 更新位置
            enemy.x += enemy.velocityX;
            enemy.y += enemy.velocityY;
            
            // 边界检测
            if (enemy.x < 0) enemy.x = 0;
            if (enemy.x > canvas.width - enemy.width) enemy.x = canvas.width - enemy.width;
            
            // 平台碰撞
            if (checkPlatformCollision(enemy)) {
                if (enemy.state === 'jumping') {
                    enemy.state = 'idle';
                }
            }
            
            // 地面碰撞
            if (enemy.y > canvas.height - enemy.height - 50) {
                enemy.y = canvas.height - enemy.height - 50;
                enemy.velocityY = 0;
                enemy.isJumping = false;
                if (enemy.state === 'jumping') {
                    enemy.state = 'idle';
                }
            }
            
            // 状态时间更新
            enemy.stateTime++;
            
            // 恢复默认状态
            if (enemy.state === 'attacking' && enemy.stateTime > 20) {
                enemy.state = 'idle';
            }
        }
        
        // 敌人使用技能
        function useEnemySkill(skillType) {
            const projectile = {
                x: enemy.x + (enemy.facingRight ? enemy.width : 0),
                y: enemy.y + enemy.height/2,
                velocityX: enemy.facingRight ? 7 : -7,
                velocityY: 0,
                size: 10,
                type: skillType,
                owner: 'enemy'
            };
            
            projectiles.push(projectile);
            createParticles(projectile.x, projectile.y, '#FF0000', 10);
        }
        
        // 更新投射物
        function updateProjectiles() {
            for (let i = projectiles.length - 1; i >= 0; i--) {
                const p = projectiles[i];
                
                // 更新位置
                p.x += p.velocityX;
                p.y += p.velocityY;
                
                // 边界检测
                if (p.x < 0 || p.x > canvas.width || p.y < 0 || p.y > canvas.height) {
                    projectiles.splice(i, 1);
                    continue;
                }
                
                // 碰撞检测
                let hit = false;
                
                if (p.owner === 'player' && checkCollision(p, enemy)) {
                    // 玩家技能击中敌人
                    enemy.health -= 10;
                    hit = true;
                    createParticles(p.x, p.y, '#00FF00', 15);
                } else if (p.owner === 'enemy' && checkCollision(p, player)) {
                    // 敌人技能击中玩家
                    if (!player.invulnerable) {
                        player.health -= 8;
                        player.state = 'hurt';
                        player.stateTime = 0;
                        player.invulnerable = true;
                        player.invulnerableTime = 0;
                        hit = true;
                        createParticles(p.x, p.y, '#FF0000', 15);
                    }
                }
                
                if (hit) {
                    projectiles.splice(i, 1);
                }
            }
        }
        
        // 玩家攻击
        function playerAttack() {
            if (player.state !== 'attacking' && player.state !== 'blocking' && player.state !== 'dodging') {
                player.state = 'attacking';
                player.stateTime = 0;
                
                // 检测是否击中敌人
                const attackRange = {
                    x: player.facingRight ? player.x + player.width : player.x - 30,
                    y: player.y,
                    width: 30,
                    height: player.height
                };
                
                if (checkCollision(attackRange, enemy)) {
                    enemy.health -= 5;
                    createParticles(
                        enemy.x + enemy.width/2, 
                        enemy.y + enemy.height/2, 
                        '#FFFF00', 
                        10
                    );
                }
            }
        }
        
        // 玩家格挡
        function playerBlock() {
            if (player.state !== 'attacking' && player.state !== 'dodging') {
                player.state = 'blocking';
                player.stateTime = 0;
            }
        }
        
        // 玩家闪避
        function playerDodge() {
            if (player.state !== 'attacking' && player.state !== 'blocking') {
                player.state = 'dodging';
                player.stateTime = 0;
                player.invulnerable = true;
                player.invulnerableTime = 0;
                
                // 闪避移动
                player.velocityX = player.facingRight ? 15 : -15;
            }
        }
        
        // 玩家使用技能
        function usePlayerSkill(skillType) {
            if (player.skills[skillType] <= 0) {
                const projectile = {
                    x: player.x + (player.facingRight ? player.width : 0),
                    y: player.y + player.height/2,
                    velocityX: player.facingRight ? 10 : -10,
                    velocityY: 0,
                    size: 12,
                    type: skillType,
                    owner: 'player'
                };
                
                projectiles.push(projectile);
                player.skills[skillType] = player.skillCooldown;
                createParticles(projectile.x, projectile.y, '#00FFFF', 15);
            }
        }
        
        // 游戏主循环
        function gameLoop() {
            if (!gameRunning) return;
            
            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            drawBackground();
            
            // 绘制平台
            drawPlatforms();
            
            // 更新游戏对象
            updatePlayer();
            updateEnemy();
            updateProjectiles();
            
            // 绘制粒子效果
            drawParticles();
            
            // 绘制投射物
            drawProjectiles();
            
            // 绘制玩家
            drawStickman(
                player.x, 
                player.y, 
                player.width, 
                player.height, 
                player.facingRight, 
                player.invulnerable && Math.floor(player.invulnerableTime/5) % 2 ? '#FF0000' : '#0000FF',
                player.state
            );
            
            // 绘制敌人
            drawStickman(
                enemy.x, 
                enemy.y, 
                enemy.width, 
                enemy.height, 
                enemy.facingRight, 
                '#FF0000',
                enemy.state
            );
            
            // 更新UI
            updateHealthBars();
            
            // 检查游戏结束条件
            if (player.health <= 0) {
                gameRunning = false;
                gameResult.textContent = "你被击败了！";
                gameOverScreen.style.display = "flex";
            } else if (enemy.health <= 0) {
                gameRunning = false;
                gameResult.textContent = "恭喜你获胜了！";
                gameOverScreen.style.display = "flex";
            }
            
            // 继续游戏循环
            requestAnimationFrame(gameLoop);
        }
        
        // 键盘事件处理
        window.addEventListener('keydown', function(e) {
            keys[e.key] = true;
            
            // 攻击
            if (e.key === 'j' || e.key === 'J') {
                playerAttack();
            }
            
            // 格挡
            if (e.key === 'k' || e.key === 'K') {
                playerBlock();
            }
            
            // 闪避
            if (e.key === 'l' || e.key === 'L') {
                playerDodge();
            }
            
            // 技能
            if (e.key === '1') {
                usePlayerSkill('fire');
            } else if (e.key === '2') {
                usePlayerSkill('water');
            } else if (e.key === '3') {
                usePlayerSkill('earth');
            } else if (e.key === '4') {
                usePlayerSkill('wind');
            } else if (e.key === '5') {
                usePlayerSkill('thunder');
            }
        });
        
        window.addEventListener('keyup', function(e) {
            keys[e.key] = false;
            
            // 恢复空闲状态
            if ((e.key === 'j' || e.key === 'J') && player.state === 'attacking') {
                player.state = 'idle';
            }
            
            if ((e.key === 'k' || e.key === 'K') && player.state === 'blocking') {
                player.state = 'idle';
            }
            
            if ((e.key === 'l' || e.key === 'L') && player.state === 'dodging') {
                player.state = 'idle';
            }
        });
        
        // 开始游戏按钮
        startButton.addEventListener('click', function() {
            startScreen.style.display = 'none';
            initGame();
        });
        
        // 重新开始按钮
        restartButton.addEventListener('click', function() {
            gameOverScreen.style.display = 'none';
            initGame();
        });
        
        // 初始绘制开始屏幕
        drawBackground();
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = 'white';
        ctx.font = '40px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('火柴人格斗游戏', canvas.width/2, canvas.height/2 - 50);
        ctx.font = '20px Arial';
        ctx.fillText('点击开始按钮开始游戏', canvas.width/2, canvas.height/2 + 20);
    </script>
</body>
</html>
