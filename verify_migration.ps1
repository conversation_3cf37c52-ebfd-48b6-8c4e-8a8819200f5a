# 验证程序迁移结果的脚本

Write-Host "=== 程序迁移验证工具 ===" -ForegroundColor Cyan

# 检查MongoDB迁移状态
function Test-MongoDBMigration {
    Write-Host "`n检查MongoDB迁移状态..." -ForegroundColor Yellow
    
    if (Test-Path "C:\Program Files\MongoDB") {
        $item = Get-Item "C:\Program Files\MongoDB"
        if ($item.Attributes -band [System.IO.FileAttributes]::ReparsePoint) {
            $target = $item.Target
            Write-Host "✓ 符号链接存在" -ForegroundColor Green
            Write-Host "  链接目标: $target" -ForegroundColor Gray
            
            if (Test-Path $target) {
                Write-Host "✓ 目标文件夹存在" -ForegroundColor Green
                
                # 计算目标文件夹大小
                $size = (Get-ChildItem $target -Recurse -ErrorAction SilentlyContinue | 
                        Measure-Object -Property Length -Sum -ErrorAction SilentlyContinue).Sum
                $sizeGB = [math]::Round($size/1GB, 2)
                Write-Host "  文件夹大小: $sizeGB GB" -ForegroundColor Gray
            } else {
                Write-Host "✗ 目标文件夹不存在！" -ForegroundColor Red
            }
        } else {
            Write-Host "⚠ C:\Program Files\MongoDB 存在但不是符号链接" -ForegroundColor Yellow
        }
    } else {
        Write-Host "✗ C:\Program Files\MongoDB 不存在" -ForegroundColor Red
    }
    
    # 检查服务状态
    $service = Get-Service -Name "MongoDB" -ErrorAction SilentlyContinue
    if ($service) {
        $status = $service.Status
        $color = if ($status -eq 'Running') { 'Green' } else { 'Yellow' }
        Write-Host "  服务状态: $status" -ForegroundColor $color
    } else {
        Write-Host "  未找到MongoDB服务" -ForegroundColor Gray
    }
}

# 检查磁盘空间变化
function Test-DiskSpace {
    Write-Host "`n检查磁盘空间..." -ForegroundColor Yellow
    
    $cDisk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
    $dDisk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='D:'"
    
    Write-Host "C盘状态:" -ForegroundColor Cyan
    Write-Host "  总容量: $([math]::Round($cDisk.Size/1GB, 2)) GB" -ForegroundColor Gray
    Write-Host "  可用空间: $([math]::Round($cDisk.FreeSpace/1GB, 2)) GB" -ForegroundColor Gray
    Write-Host "  使用率: $([math]::Round((($cDisk.Size-$cDisk.FreeSpace)/$cDisk.Size)*100, 2))%" -ForegroundColor Gray
    
    if ($dDisk) {
        Write-Host "D盘状态:" -ForegroundColor Cyan
        Write-Host "  总容量: $([math]::Round($dDisk.Size/1GB, 2)) GB" -ForegroundColor Gray
        Write-Host "  可用空间: $([math]::Round($dDisk.FreeSpace/1GB, 2)) GB" -ForegroundColor Gray
        Write-Host "  使用率: $([math]::Round((($dDisk.Size-$dDisk.FreeSpace)/$dDisk.Size)*100, 2))%" -ForegroundColor Gray
    }
}

# 检查其他可迁移程序
function Test-OtherPrograms {
    Write-Host "`n检查其他可迁移程序..." -ForegroundColor Yellow
    
    $programs = @(
        @{Name="JetBrains"; Path="C:\Program Files\JetBrains"},
        @{Name="FeverGames"; Path="C:\Program Files\FeverGames"},
        @{Name="Adobe"; Path="C:\Program Files\Adobe"},
        @{Name="Docker"; Path="C:\Program Files\Docker"}
    )
    
    foreach ($program in $programs) {
        if (Test-Path $program.Path) {
            $size = (Get-ChildItem $program.Path -Recurse -ErrorAction SilentlyContinue | 
                    Measure-Object -Property Length -Sum -ErrorAction SilentlyContinue).Sum
            $sizeGB = [math]::Round($size/1GB, 2)
            Write-Host "  $($program.Name): $sizeGB GB (可迁移)" -ForegroundColor Green
        } else {
            Write-Host "  $($program.Name): 不存在" -ForegroundColor Gray
        }
    }
}

# 生成下一步建议
function Show-NextSteps {
    Write-Host "`n=== 下一步建议 ===" -ForegroundColor Cyan
    
    $cDisk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
    $freeSpaceGB = [math]::Round($cDisk.FreeSpace/1GB, 2)
    $usagePercent = [math]::Round((($cDisk.Size-$cDisk.FreeSpace)/$cDisk.Size)*100, 2)
    
    if ($usagePercent -gt 90) {
        Write-Host "⚠ C盘使用率仍然很高 ($usagePercent%)，建议继续迁移其他程序：" -ForegroundColor Yellow
        Write-Host "  1. JetBrains工具 (约3.14GB)" -ForegroundColor Gray
        Write-Host "  2. FeverGames (约1.33GB)" -ForegroundColor Gray
        Write-Host "  3. Adobe套件 (约4.21GB)" -ForegroundColor Gray
    } elseif ($usagePercent -gt 80) {
        Write-Host "✓ C盘使用率有所改善 ($usagePercent%)，可以考虑迁移更多程序" -ForegroundColor Green
    } else {
        Write-Host "✓ C盘空间充足 ($usagePercent%)，迁移成功！" -ForegroundColor Green
    }
    
    Write-Host "`n建议的维护任务：" -ForegroundColor Cyan
    Write-Host "  • 清空回收站" -ForegroundColor Gray
    Write-Host "  • 清理临时文件" -ForegroundColor Gray
    Write-Host "  • 运行磁盘清理工具" -ForegroundColor Gray
}

# 执行所有检查
Test-MongoDBMigration
Test-DiskSpace
Test-OtherPrograms
Show-NextSteps

Write-Host "`n验证完成！" -ForegroundColor Green
