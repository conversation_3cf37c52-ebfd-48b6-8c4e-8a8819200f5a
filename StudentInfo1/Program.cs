using System;

namespace StudentGrades
{
    class Program
    {
        static void Main(string[] args)
        {
            // 创建学生信息对象
            StudentInfo student = new StudentInfo();

            // 输入学生信息
            Console.Write("请输入学号：");
            student.StudentId = Console.ReadLine();
            Console.Write("请输入姓名：");
            student.Name = Console.ReadLine();
            Console.Write("请输入语文成绩：");
            student.Chinese = float.Parse(Console.ReadLine());
            Console.Write("请输入数学成绩：");
            student.Math = float.Parse(Console.ReadLine());
            Console.Write("请输入英语成绩：");
            student.English = float.Parse(Console.ReadLine());

            // 输出学生信息
            Console.WriteLine($"学号：{student.StudentId}");
            Console.WriteLine($"姓名：{student.Name}");
            Console.WriteLine($"总成绩：{student.CalculateTotalScore()}");
            Console.WriteLine($"平均分：{student.CalculateAverageScore()}");

            Console.ReadKey(); // 等待按键以便查看结果
        }
    }
}
