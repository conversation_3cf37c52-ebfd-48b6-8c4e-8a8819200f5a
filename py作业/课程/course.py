courses = [
    {"id": "LX190020", "name": "概率论与数理统计", "teacher": "丁", "time": "星期三第一二讲"},
    {"id": "LX240170", "name": "Python程序设计及应用SL", "teacher": "徐", "time": "星期一第五讲"},
    {"id": "LX240170", "name": "Python程序设计及应用SL", "teacher": "马", "time": "星期二第二讲"}, # 同一课程编号，不同教师和时间
    {"id": "LX240260", "name": "数学建模SL", "teacher": "杨", "time": "星期五第四讲"}
]

def add_course():
    course_id = input("请输入课程编号: ").strip()
    name = input("请输入课程名称: ").strip()
    teacher = input("请输入教师名称: ").strip()
    time = input("请输入上课时间 (例如: 星期三第一二讲): ").strip()

    # 检查时间冲突：同一教师在同一时间不能有两门课
    # 检查完全重复：同一课程编号、教师、时间不能重复添加
    for course in courses:
        if course["teacher"] == teacher and course["time"] == time:
            print(f"错误：教师 '{teacher}' 在 '{time}' 已有课程安排 (课程: {course['name']})，时间冲突。")
            return
        if course["id"] == course_id and course["teacher"] == teacher and course["time"] == time:
            print(f"错误：课程编号 '{course_id}', 教师 '{teacher}', 时间 '{time}' 的课程已存在。")
            return
    
    # 题目要求检查课程编号是否已存在。如果严格指课程编号唯一，则需修改。
    # 按示例，课程编号可以不唯一，但教师+时间是唯一的。
    # 如果要检查课程编号是否已存在（即使教师或时间不同），可以添加如下逻辑：
    # for course in courses:
    #     if course["id"] == course_id:
    #         print(f"提示：课程编号 '{course_id}' 已存在 (课程名: {course['name']}, 教师: {course['teacher']})。")
    #         # 可以选择在此处返回或继续添加（如果允许同一编号有不同实例）
    #         # break # 如果只提示一次

    courses.append({"id": course_id, "name": name, "teacher": teacher, "time": time})
    print(f"课程 '{name}' 添加成功。")

def query_course_by_id():
    course_id_query = input("请输入要查询的课程编号: ").strip()
    found_courses_list = []
    for course in courses:
        if course["id"] == course_id_query:
            found_courses_list.append(course)
            
    if not found_courses_list:
        print(f"未找到课程编号为 '{course_id_query}' 的课程。")
    else:
        print(f"\n--- 课程编号 '{course_id_query}' 的查询结果 ---")
        print("课程编号\t课程名称\t\t教师\t上课时间")
        for c in found_courses_list:
            print(f"{c['id']}\t{c['name'][:15]}\t\t{c['teacher']}\t{c['time']}")
        print("------------------------------------------")

def delete_course():
    course_id_del = input("请输入要删除的课程编号: ").strip()
    teacher_del = input("请输入要删除课程的任课教师: ").strip()
    
    # 为了安全删除，最好能唯一确定一条记录。如果教师+编号可能对应多条（不同时间），
    # 则还需要上课时间。题目只要求编号和教师。
    # 我们将删除第一个匹配编号和教师的课程。

    original_length = len(courses)
    # 使用列表推导式来创建一个不包含要删除项的新列表
    # 或者找到索引后删除
    
    index_to_delete = -1
    for i, course in enumerate(courses):
        if course["id"] == course_id_del and course["teacher"] == teacher_del:
            index_to_delete = i
            break # 删除第一个匹配项

    if index_to_delete != -1:
        deleted_course_name = courses[index_to_delete]['name']
        del courses[index_to_delete]
        print(f"课程 '{deleted_course_name}' (编号: {course_id_del}, 教师: {teacher_del}) 已被删除。")
    else:
        print(f"未找到课程编号为 '{course_id_del}' 且教师为 '{teacher_del}' 的课程，无法删除。")


def display_all_courses():
    if not courses:
        print("\n当前没有课程安排。")
        return
    print("\n--- 所有课程信息 ---")
    print("课程编号\t课程名称\t\t教师\t上课时间")
    for course in courses:
        # 为了对齐，可能需要更复杂的格式化
        print(f"{course['id']}\t{course['name'][:15]}\t\t{course['teacher']}\t{course['time']}")
    print("--------------------")

def main_menu_courses():
    while True:
        print("\n简易课程管理系统")
        print("1. 添加课程")
        print("2. 按课程编号查询课程")
        print("3. 删除课程 (按课程编号和教师)")
        print("4. 显示所有课程")
        print("5. 退出")
        choice = input("请输入您的选择 (1-5): ")

        if choice == '1':
            add_course()
        elif choice == '2':
            query_course_by_id()
        elif choice == '3':
            delete_course()
        elif choice == '4':
            display_all_courses()
        elif choice == '5':
            print("正在退出课程管理系统。")
            break
        else:
            print("无效的选择，请重新输入。")

if __name__ == "__main__":
    # display_all_courses() # 显示初始数据
    main_menu_courses()