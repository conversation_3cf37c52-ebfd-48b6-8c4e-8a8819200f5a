# 商品列表: (编号, 商品名称, 单价)
products = [
    (1, "苹果", 5.0),
    (2, "香蕉", 3.0),
    (3, "牛奶", 8.0),
    (4, "面包", 6.0),
    (5, "巧克力", 10.0)
]

# 购物车: 字典列表
# 每个字典: {'id': 商品编号, 'name': 名称, 'price': 单价, 'quantity': 数量}
shopping_cart = []

def display_products():
    print("\n--- 可选商品列表 ---")
    print("编号\t商品名称\t单价")
    for p_id, name, price in products:
        print(f"{p_id}\t{name}\t\t{price:.2f}")
    print("--------------------")

def add_to_cart():
    display_products()
    try:
        product_id_input = int(input("请输入要添加到购物车的商品编号: "))
        quantity_input = int(input("请输入数量: "))

        if quantity_input <= 0:
            print("数量必须为正数。")
            return

        selected_product = None
        for p in products:
            if p[0] == product_id_input:
                selected_product = p
                break
        
        if not selected_product:
            print("无效的商品编号。")
            return

        # 检查商品是否已在购物车中
        item_in_cart = None
        for item in shopping_cart:
            if item['id'] == selected_product[0]: # selected_product[0] 是商品ID
                item_in_cart = item
                break
        
        if item_in_cart:
            item_in_cart['quantity'] += quantity_input
            print(f"已更新购物车中 {selected_product[1]} 的数量。")
        else:
            shopping_cart.append({
                'id': selected_product[0],
                'name': selected_product[1],
                'price': selected_product[2],
                'quantity': quantity_input
            })
            print(f"{selected_product[1]} 已添加到购物车。")

    except ValueError:
        print("输入无效，请输入数字。")
    except Exception as e:
        print(f"发生错误: {e}")

def view_cart():
    if not shopping_cart:
        print("\n购物车是空的。")
        return

    print("\n--- 购物车内容 ---")
    grand_total = 0.0
    print("商品名称\t单价\t数量\t小计")
    for item in shopping_cart:
        subtotal = item['price'] * item['quantity']
        print(f"{item['name']}\t\t{item['price']:.2f}\t{item['quantity']}\t{subtotal:.2f}")
        grand_total += subtotal
    print("--------------------")
    print(f"总计金额: {grand_total:.2f}")
    print("--------------------")

def main_menu_shopping():
    while True:
        print("\n简易购物系统")
        print("1. 查看所有商品")
        print("2. 添加商品到购物车")
        print("3. 查看购物车并计算总价")
        print("4. 退出")
        choice = input("请输入您的选择 (1-4): ")

        if choice == '1':
            display_products()
        elif choice == '2':
            add_to_cart()
        elif choice == '3':
            view_cart()
        elif choice == '4':
            print("感谢使用，正在退出系统。")
            break
        else:
            print("无效的选择，请重新输入。")

if __name__ == "__main__":
    main_menu_shopping()