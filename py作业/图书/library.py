library = {
    "9787302663799": {"name": "Python程序设计", "author": "董付国", "year": 2025, "publisher": "清华大学出版社"},
    "9787030673206": {"name": "应用多元统计分析", "author": "朱建平", "year": 2021, "publisher": "科学出版社"},
    "9787040629637": {"name": "数学模型", "author": "姜启源", "year": 2024, "publisher": "高教出版社"},
    "9787111632023": {"name": "机器学习及其应用", "author": "汪荣贵", "year": 2019, "publisher": "机械工业出版社"}
}

def add_book():
    isbn = input("请输入图书编号 (ISBN): ").strip()
    if isbn in library:
        print(f"错误：图书编号 '{isbn}' 已存在。")
        return
    
    name = input("请输入图书名称: ").strip()
    author = input("请输入作者: ").strip()
    while True:
        try:
            year = int(input("请输入出版年份: ").strip())
            break
        except ValueError:
            print("年份格式无效，请输入数字。")
    publisher = input("请输入出版社: ").strip() # 题目要求中未明确要求操作，但表中有

    library[isbn] = {"name": name, "author": author, "year": year, "publisher": publisher}
    print(f"图书 '{name}' 添加成功。")

def query_books_by_author():
    author_query = input("请输入要查询的作者名称: ").strip()
    found_books = []
    for isbn, book_info in library.items():
        if book_info["author"] == author_query:
            found_books.append((isbn, book_info))
            
    if not found_books:
        print(f"未找到作者 '{author_query}' 的图书。")
    else:
        print(f"\n--- 作者 '{author_query}' 的图书查询结果 ---")
        for isbn, book_info in found_books:
            print(f"编号: {isbn}, 名称: {book_info['name']}, 作者: {book_info['author']}, 年份: {book_info['year']}, 出版社: {book_info['publisher']}")
        print("------------------------------------")

def modify_book_info():
    isbn = input("请输入要修改信息的图书编号 (ISBN): ").strip()
    if isbn not in library:
        print(f"错误：未找到图书编号 '{isbn}'。")
        return

    book_info = library[isbn]
    print("\n当前图书信息:")
    print(f"  名称: {book_info['name']}")
    print(f"  作者: {book_info['author']}")
    print(f"  年份: {book_info['year']}")
    print(f"  出版社: {book_info['publisher']}")
    print("请输入新信息，留空则不修改。")

    new_name = input(f"新名称 (当前: {book_info['name']}): ").strip()
    if new_name:
        book_info['name'] = new_name

    new_author = input(f"新作者 (当前: {book_info['author']}): ").strip()
    if new_author:
        book_info['author'] = new_author

    new_year_str = input(f"新出版年份 (当前: {book_info['year']}): ").strip()
    if new_year_str:
        try:
            book_info['year'] = int(new_year_str)
        except ValueError:
            print("年份格式无效，年份未修改。")
            
    new_publisher = input(f"新出版社 (当前: {book_info['publisher']}): ").strip()
    if new_publisher:
        book_info['publisher'] = new_publisher

    print(f"图书 '{isbn}' 信息修改成功。")

def delete_book():
    isbn = input("请输入要删除的图书编号 (ISBN): ").strip()
    if isbn in library:
        book_name = library[isbn]['name']
        del library[isbn]
        print(f"图书 '{book_name}' (编号: {isbn}) 已被删除。")
    else:
        print(f"错误：未找到图书编号 '{isbn}'，无法删除。")

def display_all_books():
    if not library:
        print("\n图书馆中目前没有图书。")
        return
    print("\n--- 图书馆所有图书 ---")
    print("图书编号\t\t图书名称\t\t作者\t出版年份\t出版社")
    for isbn, book_info in library.items():
        # 为了对齐，可能需要更复杂的格式化，这里简单处理
        print(f"{isbn}\t{book_info['name'][:10]}\t\t{book_info['author']}\t{book_info['year']}\t\t{book_info['publisher']}")
    print("----------------------")

def main_menu_library():
    while True:
        print("\n图书管理系统")
        print("1. 添加图书")
        print("2. 按作者查询图书")
        print("3. 修改图书信息")
        print("4. 删除图书")
        print("5. 显示所有图书")
        print("6. 退出")
        choice = input("请输入您的选择 (1-6): ")

        if choice == '1':
            add_book()
        elif choice == '2':
            query_books_by_author()
        elif choice == '3':
            modify_book_info()
        elif choice == '4':
            delete_book()
        elif choice == '5':
            display_all_books()
        elif choice == '6':
            print("正在退出图书管理系统。")
            break
        else:
            print("无效的选择，请重新输入。")

if __name__ == "__main__":
    # display_all_books() # 显示初始数据
    main_menu_library()