students = [] # 全局列表: [['姓名', [成绩1, 成绩2,..., 平均分(可选)], 平均分已计算标记(可选True)]]

def find_student_index(name_to_find):
    """辅助函数：根据姓名查找学生在列表中的索引"""
    for i, student_data in enumerate(students):
        if student_data[0] == name_to_find:
            return i
    return -1

def add_student_name():
    """1. 实现添加学生姓名"""
    try:
        num_students = int(input("请输入要添加的学生数量: "))
        if num_students <= 0:
            print("数量必须为正。")
            return
    except ValueError:
        print("请输入有效的数字。")
        return

    for _ in range(num_students):
        name = input(f"请输入第 {_+1} 位学生的姓名: ").strip()
        if not name:
            print("姓名不能为空。")
            continue
        
        if find_student_index(name) != -1:
            print(f"学生 '{name}' 已存在。")
        else:
            students.append([name, []]) # 初始：[姓名, 空成绩列表]
            print(f"学生 '{name}' 添加成功。")

def record_grade():
    """3. 实现录入成绩"""
    if not students:
        print("系统中尚无学生，请先添加学生。")
        return

    while True:
        target_name = input("请输入要录入成绩的学生姓名 (或输入 'q' 退出): ").strip()
        if target_name.lower() == 'q':
            break

        student_idx = find_student_index(target_name)
        if student_idx != -1:
            student_record = students[student_idx]
            try:
                grade_str = input(f"请输入学生 '{target_name}' 的一项成绩: ")
                grade_value = float(grade_str)
                if not (0 <= grade_value <= 150): # 假设成绩范围0-150
                    print("成绩应在0到150之间。")
                    continue
            except ValueError:
                print("无效的成绩输入，请输入数字。")
                continue
            
            # 如果之前计算过平均分并存储在成绩列表末尾，先移除它
            # 标记是 student_record 长度为3
            if len(student_record) == 3 and student_record[1]:
                student_record[1].pop() # 移除末尾的旧平均分
                student_record.pop()    # 移除平均分已计算的标记

            student_record[1].append(grade_value)
            print(f"成绩 {grade_value} 已为学生 '{target_name}' 录入。")
        else:
            print(f"学生 '{target_name}' 不存在，无法录入成绩。")
        
        # 题目要求：输入y退回录入。这里理解为继续当前录入功能。
        continue_choice = input("是否继续录入成绩? (y/n): ").strip().lower()
        if continue_choice != 'y':
            break

def calculate_average():
    """4. 实现计算平均分 (针对指定学生，结果存于其成绩列表末尾)"""
    if not students:
        print("系统中尚无学生，请先添加学生。")
        return

    target_name = input("请输入要计算平均分的学生姓名: ").strip()
    student_idx = find_student_index(target_name)

    if student_idx != -1:
        student_record = students[student_idx]
        grades_list = student_record[1] # 这可能已包含旧的平均分

        # 清理可能存在的旧平均分（如果之前计算过）
        # 标记是 student_record 长度为3
        actual_grades_for_calc = list(grades_list) # 操作副本
        if len(student_record) == 3 and actual_grades_for_calc:
            actual_grades_for_calc.pop() # 移除副本末尾的旧平均分
            # 同时也要更新原始列表和标记
            student_record[1].pop()
            student_record.pop()


        if actual_grades_for_calc: # 用清理后的成绩列表计算
            average = sum(actual_grades_for_calc) / len(actual_grades_for_calc)
            
            # 将新平均分追加到原始成绩列表
            student_record[1].append(average)
            # 添加标记表示平均分已计算并存储
            if len(student_record) == 2: # 如果之前没有标记
                 student_record.append(True) # True作为标记
            
            print(f"学生 '{target_name}' 的平均分 {average:.2f} 已计算并存储于其成绩列表末尾。")
        else:
            print(f"学生 '{target_name}' 尚无成绩可用于计算平均分。")
            # 如果之前有标记但现在没成绩了，清除标记和可能残留的平均分
            if len(student_record) == 3:
                if student_record[1]: student_record[1].pop() # 清除残留平均分
                student_record.pop() # 清除标记
    else:
        print(f"学生 '{target_name}' 不存在，无法计算平均分。")


def display_student_info():
    """5. 实现输出学生信息"""
    if not students:
        print("系统中没有学生信息。")
        return
    
    print("\n--- 学生信息 ---")
    print(f"{'姓名':<10}\t{'成绩列表 (最后一项可能为平均分)':<40}")
    print("-" * 50)

    for student_record in students:
        name = student_record[0]
        grades_and_avg_list = student_record[1]
        
        display_list_str = ", ".join(map(lambda x: f"{x:.2f}" if isinstance(x, float) else str(x), grades_and_avg_list))
        
        # 检查是否有平均分标记
        avg_info = ""
        if len(student_record) == 3 and grades_and_avg_list: # 标记存在且有内容
            # 最后一个元素是平均分
            avg_info = f" (其中平均分: {grades_and_avg_list[-1]:.2f})"
        elif grades_and_avg_list:
            avg_info = " (平均分未计算)"
        else:
            avg_info = " (无成绩, 平均分 N/A)"
            
        print(f"{name:<10}\t[{display_list_str}]{avg_info}")
    print("-" * 50)


def main_menu():
    """主菜单函数"""
    while True:
        print("\n简易学生成绩管理系统 ")
        print("1. 添加学生姓名")
        print("2. 录入成绩")
        print("3. 计算平均分")
        print("4. 显示学生信息")
        print("5. 退出系统")
        choice = input("请输入您的选择 (1-5): ").strip()

        if choice == '1':
            add_student_name()
        elif choice == '2':
            record_grade()
        elif choice == '3':
            calculate_average()
        elif choice == '4':
            display_student_info()
        elif choice == '5':
            print("感谢使用，系统已退出。")
            break
        else:
            print("无效的选择，请输入1到5之间的数字。")

if __name__ == "__main__":
    main_menu()