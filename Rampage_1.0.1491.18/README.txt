>--- Overview ----<
Rampage is a Trainer / Modification for Red Dead Redemption 2 Story Mode

Please keep in mind that this product is a WIP project that is evolving through time. It is not a finished release product where everything is working perfectly.
This is a mod that depends on research and R* Patches,
That means in general that functions have to be researched and there are certain borders and limits in what is possible and also what <PERSON>* allows us to do.


>--- How to Install ---<
Extract the .zip file and put the Rampage.asi and RampageFiles into your RDR2 Directory.
Make sure you have the latest version of Alexander Blades ScriptHookRDR2 plugin (ScriptHookRDR2.dll & dinput8.dll)
If you have any issues with dinpu8.dll you can also just use version.dll from LMS to load your .asi files.
It's also requiered to use version.dll if you want content from RDO to be usable in Singleplayer

If RDR2 is installed on your main drive (C:) or you get any issues regarding not being able to save file make sure 
that your user account and the user group have the correct permissions for the game folder.


>--- Troubleshooting ---<
If you encounter any issues while loading such as "Settings.json missing" or "Hotkey.json missing", issues with loading of textures
or any other crashes on startup, try to delete the RampageFiles folder, dowload the latest version and restart RDR2.
Also make sure that your user has  permissions to write to the game folder.

If your game is not installed on your C: drive to fix permissions navigate to your game folder
right click on it, select the security tab and click on Advanced.
Now you need to click on Change and then Enable inheritance.

If your game is installed on C: you may need to make sure your user has write permission checked.


>--- Common Questions ---<
Q: How can I disable the Prompt that shows Open F5?
A: Go into Settings->Extended UI->Disable "Open Info"

Q: How can I disable the command prompt that shows when pressing T?
A: Go into Settings->Hotkey Manager->Disable or change CMD Key

Q: How can I disable the Panels in the top right corner?
A: Go into Settings->Extended UI->Disable "Window Activators"

Q: How can I use RDR Online Weapons?
A: To use RDR Online weapons you need to install a LML mod that adds those weapons to the SP Catalogue

Q: Some emotes or animations are not working
A: For some models, emotes and animations it is neccessary to load online content with either using version.dll or online content unlocker or rpfstreamer

Q: How can I use Online Clothes/Horses/Maps
A: Put version.dll from LMS inside your game directory

Q: Can I save custom set hotkeys
A: Limited availability (You can save some but not all) (There are no plans to improve that as it would cause an entire re-write)

Q: I can't save my settings (Error failed to save Settings.json)
A: Make sure that your user has write access to the game folder 

If you experience a FATAL error after startup or Can't find native and you're using an older game version such as 1355 or older you also need to use and older rampage + scripthook version.


>--- Known issues ---<
Options that access RDR`s entity pools like Local Peds & Local Vehicle options, Black hole etc. tend to crash if there are to many entites.
To prevent crashing because of too many spawned objects try to delete old ones first and free the memory by deleting world vehicles / peds.


>--- Important Things ---<
If you want to change a specific hotkey in the trainer you can change it in the Hotkey Manager under settings,
just click on the Option and enter your new hotkey inside the window.

Log files are stored in RampageFiles/Logs if you have any issues you may want to look into these files.

If you make any changes like changing the colors or other settings in order to save them you need to manually save them
under Settings -> Load / Save.


>--- Issues and Bug Reporting ---<
If you encounter any issues or bugs or your game crashes you can submit a bug report.
For a bug report please provide some general information like What options you used, what other mods you used
did you load any savegame?, did you played any missions or just free play? You can always find a Log inside the Rampagefiles folder in the subfolder Logs.
If you encounter any crashes you can enable Advanced Logging on version 1.3.5 and higher (Settings->Debug) you can then submit your log file to make it easier to track down issues.
w

>--- Controls ---<
Rampage supports both Keyboard & Gamepad, Controls are:
Open Trainer = F5
Move Up = Arrow Key up or Numpad8
Move Down Arrow Key down or Numpad2
Move on Numerical Option (Number, Decimal, List) = Right/Left Arrow key or Numpad4 and Numpad6
Move Back = Delete Key or Numpad0
Select = Enter Key or Numpad5
X = Activate cursor mode


>--- Terms & Rules ---<
The Software is provided "As Is", without warranty of any kind.
The Software is completely free and it's forbidden to sell or use it in any commercial way.
You are not allowed to redistribute this software without permission.
You are not allowed to modify or reverse code as well as debugging / patching
You take full responsibility for your actions using this product.
Software support may be stopped at any time without any reason.
This terms may be updated at any time without any reason.

>--- Credits ---<

RDR2 Modding Resources
-> alloc8or Native DB
-> Alexander Blade For ScriptHookRDR2
-> rdr2mods
-> RedM


>--- Startup Arguments ---<
-> -rampage_dbg // Opens the debug console upon startup
-> -rampage_nmh // disables minhook
-> -rampage_docfm // Switches the main folder location to Documens/Rockstar Games/Red Dead Redemption 2

>--- Changelog ---<

Version 1.6.5

-Added Auto Teleport to Waypoint
-Added Opacity Setting for Peds in the Ped Editor
-Added Elevation Safe Teleport For-/Backward
-Added Vehicle Ghostmode Draft Animals
-Added Vehicle Draft Animals Flaming Hooves
-Added Vehicle Alpha Setting for Draft Animals
-Added Drive On Water for Draft Vehicles
-Added Weapon Skill Stat Editing
-Added Experimental Volume Editor
-Added Support for attaching objects to peds in the ped database
-Added Patch to be able to spawn all types of objects
-Animal Spawner list is now updated to be aligned with the the compendium
-Fish Spawner list is now updated to be aligned with the compendium
-Horse Spawner will now give random saddle components when "Spawn on Horse" is enabled
-Keyboard Input now has a higher character limit (90)
-Fixed an issue where disabling "Disco Lantern" would set lantern light to wrong light tone
-Fixed a wrong alligator naming in ped spawner
-Fixed an issue with weather transition when having freeze weather enabled
-Improved Hogtie Nearby Peds
-Improved Color Palette
-Internal Improvements