{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "aerophibian_wings", "model_identifier": "", "visible_box": [5, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 4, 4], "to": [28, 37, 4], "autouv": 0, "color": 0, "origin": [-2, 15, 4], "uv_offset": [56, 37], "faces": {"north": {"uv": [36, 59, 58, 64], "texture": 5}, "east": {"uv": [58, 59, 36, 64], "texture": 5}, "south": {"uv": [26, 0, 0, 31], "texture": 5}, "west": {"uv": [58, 59, 36, 64], "texture": 5}, "up": {"uv": [64, 5, 14, 53], "texture": 5}, "down": {"uv": [64, 5, 14, 53], "texture": 5}}, "type": "cube", "uuid": "881851bf-de61-84fa-23ca-646052ddf32f"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 4, 4], "to": [28, 37, 4], "autouv": 0, "color": 0, "origin": [-2, 15, 4], "uv_offset": [56, 37], "faces": {"north": {"uv": [36, 59, 58, 64], "texture": 5}, "east": {"uv": [58, 59, 36, 64], "texture": 5}, "south": {"uv": [54, 0, 24, 35], "texture": 5}, "west": {"uv": [58, 59, 36, 64], "texture": 5}, "up": {"uv": [64, 5, 14, 53], "texture": 5}, "down": {"uv": [64, 5, 14, 53], "texture": 5}}, "type": "cube", "uuid": "0879640c-bde3-b543-0c94-3fb076ad8a85"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-28, 4, 4], "to": [2, 37, 4], "autouv": 0, "color": 0, "origin": [2, 15, 4], "uv_offset": [56, 37], "faces": {"north": {"uv": [58, 59, 36, 64], "texture": 5}, "east": {"uv": [36, 59, 58, 64], "texture": 5}, "south": {"uv": [0, 0, 26, 31], "texture": 5}, "west": {"uv": [36, 59, 58, 64], "texture": 5}, "up": {"uv": [14, 5, 64, 53], "texture": 5}, "down": {"uv": [14, 5, 64, 53], "texture": 5}}, "type": "cube", "uuid": "2b0da7c0-ab92-5e33-f112-b9ad98127e31"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-28, 4, 4], "to": [2, 37, 4], "autouv": 0, "color": 0, "origin": [2, 15, 4], "uv_offset": [56, 37], "faces": {"north": {"uv": [58, 59, 36, 64], "texture": 5}, "east": {"uv": [36, 59, 58, 64], "texture": 5}, "south": {"uv": [24, 0, 54, 35], "texture": 5}, "west": {"uv": [36, 59, 58, 64], "texture": 5}, "up": {"uv": [14, 5, 64, 53], "texture": 5}, "down": {"uv": [14, 5, 64, 53], "texture": 5}}, "type": "cube", "uuid": "23066e73-c311-0da7-4472-33e286aa3dc2"}], "outliner": [{"name": "armorHead", "origin": [0, 24, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "antenna", "origin": [0, 0, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorBody", "origin": [0, 24, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorRightArm", "origin": [5, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingRight", "origin": [5, 22, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["881851bf-de61-84fa-23ca-646052ddf32f", "0879640c-bde3-b543-0c94-3fb076ad8a85"]}]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [5, 22, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["2b0da7c0-ab92-5e33-f112-b9ad98127e31", "23066e73-c311-0da7-4472-33e286aa3dc2"]}, {"name": "armNested", "origin": [-5, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\transformations\\afaliens\\big_wings.png", "name": "big_wings.png", "folder": "block", "namespace": "", "id": "0", "width": 32, "height": 32, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "relative_path": "../../models/transformations/afaliens/big_wings.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAglJREFUWEftlk1LAlEUhq9KYYOpQdCq/kXbfkD/oEVtpKURQkkUiBCFBbZokYtsYVCbFi2iVZv6HxFEu0RSy6/SiffSkTOT5njniggdEMfx433Oez6uHjHk8AxZX4wGwPRi0iSnWo2KxbTCXcpVEn9+mYRJ1C4OEu+4IYFUQRzThxeiptn8lGLBjbgoHaaExzfWBlCFcAwAgcn5FTO8nZTiFMZqRHj9hqie5pSc6AsACnM3L2bpICXghn95SYojqmc5pXL0DQCx2esnk1wABKJ+caVUDmWAcjotXeCBnui3MZUAZjL3ZiWbFb6JkAWiU3P2mg4lAGpIAFDADQLAPT4hGNVG4VmW6O3h2KLpCoDbjevQ1o4o7u/+KguEv97zegHIBVKbSuwJTAeC9wZ3AosMMLwsyg7wNLEfjEhE1M4vpfUSLhaz7AveoIDI3yakthYAggEIrWaUg/YFvc/dIBe0AlBZeDm6lWRgABCkMaWMA2tR0apVxMdJpj0dAwUABE7S+uujgBsQR5SP0vIZYDSO2ktA4uh4PIKbcQmAMwM9AWGcrAMBsP9/CKxHZcYkbFkQPy+0OQBx/oelWS3KxfSXuLYx5OIQ5mOHa/v65U64doDEkT1twE7z3sl+LQ4QAGVOB1SvU7DtUjcyp/e5A/Y97+Q3tJSA9roTQftnXAOoiGptwn+AkXfgG03aCTCPvWEnAAAAAElFTkSuQmCC"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK (1)\\addonpacks\\MARSHYS_OMNITRIX_ADDON_4.53_PATCHED\\assets\\m_ten\\textures\\models\\ben\\5yl\\ultimates\\necrofriggian_wings.png", "name": "necrofriggian_wings.png", "folder": "", "namespace": "", "id": "2", "width": 32, "height": 32, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "6174779c-3791-d0b8-c6a5-5fa30a85f009", "relative_path": "../../../../../../../../MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK (1)/addonpacks/MARSHYS_OMNITRIX_ADDON_4.53_PATCHED/assets/m_ten/textures/models/ben/5yl/ultimates/necrofriggian_wings.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAANJJREFUWEdj3GMo+58BCbicf8yIzKc1m7FUSRXsAHf+H0TbRU1Hgn0LcwSyC+jlIHhwY3MEoSChhiNR4pscR8AcScgxuKKNag4g5BCiHIArPRCKCnR5bKFBtANo5QiSHADzETXTBFkOoDQ0kKOCbAdQEhpUdQCpCRGkHrmYpzgEsDkAvR7B50iqOgBm8c6PHAQDBhYNFDsA2bfEWIxeMJHtAFJ8C7K0+95tkqpzvIpBluPyLakW4YorklxLMMLJUDDqgNEQGA2B0RAYDYHREBgNAQB5zFghiPRW/AAAAABJRU5ErkJggg=="}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK (1)\\addonpacks\\MARSHYS_OMNITRIX_ADDON_4.53_PATCHED\\assets\\m_ten\\textures\\models\\ben\\5yl\\nemunia_wings.png", "name": "nemunia_wings.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "169c1443-2239-1598-337f-686d5cbd1b1f", "relative_path": "../../../../../../../../MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK (1)/addonpacks/MARSHYS_OMNITRIX_ADDON_4.53_PATCHED/assets/m_ten/textures/models/ben/5yl/nemunia_wings.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK (1)\\addonpacks\\MARSHYS_OMNITRIX_ADDON_4.53_PATCHED\\assets\\m_ten\\textures\\models\\transformations\\af_overlays\\big_wings.png", "name": "big_wings.png", "folder": "", "namespace": "", "id": "4", "width": 32, "height": 32, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "ec28110c-cdec-6f89-08c9-32ece3482260", "relative_path": "../../../../../../../../MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK (1)/addonpacks/MARSHYS_OMNITRIX_ADDON_4.53_PATCHED/assets/m_ten/textures/models/transformations/af_overlays/big_wings.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAWNJREFUWEftlk0KwjAQhdNYXXgNEQRv4cKNG4/qxo0LbyEI4jUU/EkltRPHySSp7RQRdGcafV/eS16TqS9/si/rq98A0NNlEXLK7FbJRejJojD7NTsv+mMnfDnx+oOhGw+BWHF1PStz3H4OgFX1eP50wdyewzp/Pa5AOAgxgFLTQgAAhbDfB0NFIQCgZGdcSOZHvfcgYAI4EoEQAWCdSEDEYvjYgVoAJI5YDM0B8Ib0cqo2KIoj5EIjgKgLTBzl0P2quOPYDoCuHJ8Qekp6fVkA5wKGoADUDftc58ocNm7hjR1gSwoGQyDgCtobIgBuodCWMZCqL8AFUQA2Fu60oBjEAWpBdA1QQthXOH6Lkn0BtdyNA1icEdajWdEJgHd/QOKt7wO0c7z2jVgeErf/IRLBW95MB3QK4MQDNYxbj3OxtQMeACmaVHSyAKTnU+Iie8A6UOdqHoJp7UCdVcbm/AH+DjwA7YTWIb6/O9UAAAAASUVORK5CYII="}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK (1)\\addonpacks\\MARSHYS_OMNITRIX_ADDON_4.53_PATCHED\\assets\\wings\\textures\\entity\\wings\\blue_butterfly.png", "name": "blue_butterfly.png", "folder": "", "namespace": "", "id": "5", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "8dfcc38f-cacc-557f-b957-cef90aca193a", "relative_path": "../../../../../../../../MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK (1)/addonpacks/MARSHYS_OMNITRIX_ADDON_4.53_PATCHED/assets/wings/textures/entity/wings/blue_butterfly.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAA2hJREFUeF7tWTFOHTEQ/b+J6FGKCLo0HCCKxAEiGiouwlm4CBVNxAGQohwgTbqgFBE9SrPRrP6s/L3jnTf2emyUpUIwtuc9v5kdz+x3Cz+nl9fD0v9L//fy9LAv3aN0vegAAz/58FHd//n+jmzifYazm1t17evvn6NNSyJmBBB4BDg5ngDPwCESyJiIaEXCEQEo+D/fvu7+/voh3Xx868O784vd+89XkBpakDARgII/3DoCflIC/YKGhDcJIwEI+AB4eJtaEpslUY0I73BIEhAD/vJ92D1+muE1ESDtERPSFQHkMP1kAJ/lgvAP4b5vgoCIBO3WUwlvZJPB857NCUjFP4dAQgVWEqZcEO8n5QTPMNhLBBD4RMzz7WYTEIcChVdLFYgEBN95S8ZXv/UHg6Mvg1QnNFHA8/3d/uzmdoiyP900O2y99cVccCifJzJIBeyDOwHexYcmFQpLL5/GEGj9IAkJ8fZnygGtX2bhC9Q9BMLXnzcR0tPblQDOzPwpCpPg6eV1tWfqIc4n9Ufnr5VwtXQzNTLEQoWcqnEbXHtwvSGU2xsB6tWtZDBrZfG+LMmaCqCzhLpjJWjYNkkCeHmNPBDHf+Cqm/TRmn6oTIA74FgXqgM1qrIae2KCn1tNlSC6QW6JmjNjyD0LxUJ2oQKgNra1UFphxmDBY7bNGmjQKcjXAWm2ssfKjMEMDF0g5YBVevkoeMOMAcVkskslwbEy1FrYKSWg4DNmDCZwiLGogHihRkQYDgj4zBkDgsdsoxJg7eWneoyhZ5kzBjM4ZMFSHXDUu0N7+UtNVnJohRkDggu20Qohcy9fIyAiQTsfBpJrCCkA7eWTE9JofeUZQy5WcR1EgBS/Ui9fIqDCjMGNgPAgqJcvEVB5xlBMBhqD4Wwg2ctnAhxnDG4EqAd5t7NVh0ADVAGL28UvPY9XHIhPNSsiIO7lcwhYX4yqlxUNsghI9fLjJPgWiDARAPbyp76C14yhRCAwAYZevuuMoQQ8rd0IQBkMa3ywl39UL9A5SBcJ9WctO5MCXp4epHMXmyrhghot9lIicgiA10TOVZkxeBOQC370s6d5ABNXBKiU/R7WbwT0cAstfdgU0JL9Hs7eFNDDLbT0YVNAS/Z7OHtTQA+30NKHTQEt2e/h7E0BPdxCSx/+ewX8Aww8Tl8XWnvqAAAAAElFTkSuQmCC"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\transformations\\afaliens\\jetray_wing.png", "name": "jetray_wing.png", "folder": "", "namespace": "", "id": "6", "width": 32, "height": 32, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "052bdd4b-40f1-1a60-44fd-1433d2499697", "relative_path": "../../models/transformations/afaliens/jetray_wing.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAVVJREFUWEftlUFqAjEYhZthhCIuuimUXqCX6KKLXseDuHHT6/QapfQC1rUtRbCovBmevPxmxiQOuklgcIKT5JvvJf+46eRpN3LVDdv859MdOhe4cQDAOgqBfh/I4v2hGcP2+LrMhm4GqgWAbHZb790tDAFGdbvu5r/lyQE5kBPCmuiCAQQBFCLVigfAKABBEAXAvdogBEBoIRXGy06jGFe1FwMWDwE00LVrLjYL0xfN0ebRKDQOmpitPrwxGsX4tjoyAZgsAD0ZGocFwHOEAIC2v3W7me9fvjtPSfAPWxvURB9AKA5AJAPwaIYshADUAg1wXyCCu+dFmgFOYosUTHQBEIIW9DcbIGTiVKm2RQomzgKwEKcAQiay9oC3naVcxwBYiMEAMPHb71f0h4fHcxAARnFVABtLTB8WkiphzKRDPhOd55CL6lwFoBgoBoqBYqAYuLqBPcK6vCELK7qbAAAAAElFTkSuQmCC"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": true, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}