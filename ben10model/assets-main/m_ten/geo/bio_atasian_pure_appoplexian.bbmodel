{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "bio_atasian_pure_appoplexian", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 28, -4], "to": [3, 34, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 3, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 5}, "east": {"uv": [0, 8, 8, 16], "texture": 5}, "south": {"uv": [24, 8, 32, 16], "texture": 5}, "west": {"uv": [16, 8, 24, 16], "texture": 5}, "up": {"uv": [16, 8, 8, 0], "texture": 5}, "down": {"uv": [24, 0, 16, 8], "texture": 5}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 28, -4], "to": [3, 34, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 3, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 5}, "east": {"uv": [32, 8, 40, 16], "texture": 5}, "south": {"uv": [56, 8, 64, 16], "texture": 5}, "west": {"uv": [48, 8, 56, 16], "texture": 5}, "up": {"uv": [48, 8, 40, 0], "texture": 5}, "down": {"uv": [56, 0, 48, 8], "texture": 5}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 19, -2], "to": [4, 25, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 26], "texture": 5}, "east": {"uv": [16, 20, 20, 26], "texture": 5}, "south": {"uv": [32, 20, 40, 26], "texture": 5}, "west": {"uv": [28, 20, 32, 26], "texture": 5}, "up": {"uv": [28, 20, 20, 16], "texture": 5}, "down": {"uv": [36, 16, 28, 20], "texture": 5}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 19, -2], "to": [4, 25, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 42], "texture": 5}, "east": {"uv": [16, 36, 20, 42], "texture": 5}, "south": {"uv": [32, 36, 40, 42], "texture": 5}, "west": {"uv": [28, 36, 32, 42], "texture": 5}, "up": {"uv": [28, 36, 20, 32], "texture": 5}, "down": {"uv": [36, 32, 28, 36], "texture": 5}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 13, -2], "to": [9, 26, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 5}, "east": {"uv": [40, 20, 44, 32], "texture": 5}, "south": {"uv": [52, 20, 56, 32], "texture": 5}, "west": {"uv": [48, 20, 52, 32], "texture": 5}, "up": {"uv": [48, 20, 44, 16], "texture": 5}, "down": {"uv": [52, 16, 48, 20], "texture": 5}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 13, -2], "to": [9, 26, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 5}, "east": {"uv": [40, 36, 44, 48], "texture": 5}, "south": {"uv": [52, 36, 56, 48], "texture": 5}, "west": {"uv": [48, 36, 52, 48], "texture": 5}, "up": {"uv": [48, 36, 44, 32], "texture": 5}, "down": {"uv": [52, 32, 48, 36], "texture": 5}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9, 13, -2], "to": [-5, 26, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1, 2, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 5}, "east": {"uv": [32, 52, 36, 64], "texture": 5}, "south": {"uv": [44, 52, 48, 64], "texture": 5}, "west": {"uv": [40, 52, 44, 64], "texture": 5}, "up": {"uv": [40, 52, 36, 48], "texture": 5}, "down": {"uv": [44, 48, 40, 52], "texture": 5}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9, 13, -2], "to": [-5, 26, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1, 2, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 5}, "east": {"uv": [48, 52, 52, 64], "texture": 5}, "south": {"uv": [60, 52, 64, 64], "texture": 5}, "west": {"uv": [56, 52, 60, 64], "texture": 5}, "up": {"uv": [56, 52, 52, 48], "texture": 5}, "down": {"uv": [60, 48, 56, 52], "texture": 5}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [1, 16], "faces": {"north": {"uv": [5, 20, 8, 32], "texture": 5}, "east": {"uv": [1, 20, 5, 32], "texture": 5}, "south": {"uv": [12, 20, 15, 32], "texture": 5}, "west": {"uv": [8, 20, 12, 32], "texture": 5}, "up": {"uv": [8, 20, 5, 16], "texture": 5}, "down": {"uv": [11, 16, 8, 20], "texture": 5}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [1, 32], "faces": {"north": {"uv": [5, 36, 8, 48], "texture": 5}, "east": {"uv": [1, 36, 5, 48], "texture": 5}, "south": {"uv": [12, 36, 15, 48], "texture": 5}, "west": {"uv": [8, 36, 12, 48], "texture": 5}, "up": {"uv": [8, 36, 5, 32], "texture": 5}, "down": {"uv": [11, 32, 8, 36], "texture": 5}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -2], "to": [-0.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 23, 64], "texture": 5}, "east": {"uv": [16, 52, 20, 64], "texture": 5}, "south": {"uv": [27, 52, 30, 64], "texture": 5}, "west": {"uv": [23, 52, 27, 64], "texture": 5}, "up": {"uv": [23, 52, 20, 48], "texture": 5}, "down": {"uv": [26, 48, 23, 52], "texture": 5}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -2], "to": [-0.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 7, 64], "texture": 5}, "east": {"uv": [0, 52, 4, 64], "texture": 5}, "south": {"uv": [11, 52, 14, 64], "texture": 5}, "west": {"uv": [7, 52, 11, 64], "texture": 5}, "up": {"uv": [7, 52, 4, 48], "texture": 5}, "down": {"uv": [10, 48, 7, 52], "texture": 5}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 13, -2], "to": [3.5, 18, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.5, 1, 0], "uv_offset": [16, 23], "faces": {"north": {"uv": [20, 27, 28, 32], "texture": 5}, "east": {"uv": [16, 27, 20, 32], "texture": 5}, "south": {"uv": [16, 27, 20, 32], "texture": 5}, "west": {"uv": [28, 27, 29.75, 32], "texture": 5}, "up": {"uv": [38.625, 29, 30.625, 25], "texture": 5}, "down": {"uv": [38.625, 29, 30.625, 25], "texture": 5}}, "type": "cube", "uuid": "8725b865-28c3-0904-ccb2-94caa5409736"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 25, -2.5], "to": [4.5, 27, 2.5], "autouv": 0, "color": 0, "origin": [-0.5, 26, 0], "uv_offset": [14, 15], "faces": {"north": {"uv": [19.5, 19, 28.5, 21], "texture": 5}, "east": {"uv": [16.125, 21.625, 17.625, 23.125], "texture": 5}, "south": {"uv": [16.125, 21.625, 17.625, 23.125], "texture": 5}, "west": {"uv": [16.125, 21.625, 17.625, 23.125], "texture": 5}, "up": {"uv": [16.125, 21.625, 17.625, 23.125], "texture": 5}, "down": {"uv": [16.125, 21.625, 17.625, 23.125], "texture": 5}}, "type": "cube", "uuid": "60286f45-9e2b-a5d4-8ae3-bf88be3b20a1"}], "outliner": [{"name": "armorHead", "origin": [0, 26, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", {"name": "antenna", "origin": [0, 2, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "60286f45-9e2b-a5d4-8ae3-bf88be3b20a1", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "8725b865-28c3-0904-ccb2-94caa5409736"]}, {"name": "armorRightArm", "origin": [6, 23, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", {"name": "wingRight", "origin": [6, 23, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 23, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 23, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-6, 23, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\5yl\\ov_omni_highbreed.png", "name": "ov_omni_highbreed.png", "folder": "", "namespace": "", "id": "1", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "a64d4784-6c60-3de3-d3a2-e0ec3e0d6769", "relative_path": "../textures/models/ben/5yl/ov_omni_highbreed.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\5yl\\omni_highbreed.png", "name": "omni_highbreed.png", "folder": "", "namespace": "", "id": "2", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "2e6b0bed-cce7-da5e-5745-a9099c1e31e7", "relative_path": "../textures/models/ben/5yl/omni_highbreed.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\bio_appoplexian_highbreed.png", "name": "bio_appoplexian_highbreed.png", "folder": "", "namespace": "", "id": "3", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "26e8369e-8378-f857-3a45-6b813f95c74d", "relative_path": "../textures/models/ben/bio/bio_appoplexian_highbreed.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\bio_appoplexian_highbreed_3.png", "name": "bio_appoplexian_highbreed_3.png", "folder": "", "namespace": "", "id": "4", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "e168b9df-12ba-cccd-9d1a-9a5836892592", "relative_path": "../textures/models/ben/bio/bio_appoplexian_highbreed_3.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\bio_appoplexian_highbreed_2.png", "name": "bio_appoplexian_highbreed_2.png", "folder": "", "namespace": "", "id": "5", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "f0195671-de37-1f71-ccde-a2effa3c7252", "relative_path": "../textures/models/ben/bio/bio_appoplexian_highbreed_2.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": true, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}