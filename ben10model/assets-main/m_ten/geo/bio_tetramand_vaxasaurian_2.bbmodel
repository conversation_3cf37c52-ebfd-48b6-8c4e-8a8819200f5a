{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "bio_tetramand_vaxasaurian_2", "model_identifier": "", "visible_box": [4, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "tail", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.625, 14, -9], "to": [0.375, 14, -2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "rotation": [53.47830000000022, -0.05206252861114037, 0.6832148980363318], "origin": [0.875, -2, -1], "uv_offset": [16, 32], "faces": {"north": {"uv": [12, 36, 13.75, 38.5], "texture": 4}, "east": {"uv": [12.25, 36.25, 14, 38.5], "texture": 4}, "south": {"uv": [12.25, 36.25, 14, 38.5], "texture": 4}, "west": {"uv": [12, 36, 14, 38.5], "texture": 4}, "up": {"uv": [14, 38.5, 12, 36], "texture": 4}, "down": {"uv": [14, 36.25, 12, 38.5], "texture": 4}}, "type": "cube", "uuid": "afb4589d-592a-328c-5f8f-c56c75bca4bf"}, {"name": "armorHead", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 28, -5], "to": [3.5, 36, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [-0.5, 0, 0], "faces": {"north": {"uv": [16, 8, 8, 16], "texture": 4}, "east": {"uv": [24, 8, 16, 16], "texture": 4}, "south": {"uv": [32, 8, 24, 16], "texture": 4}, "west": {"uv": [8, 8, 0, 16], "texture": 4}, "up": {"uv": [8, 8, 16, 0], "texture": 4}, "down": {"uv": [16, 0, 24, 8], "texture": 4}}, "type": "cube", "uuid": "c7c3ad6c-3e05-acf8-10d8-20313f6eb901"}, {"name": "armorHead", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 28, -5], "to": [3.5, 36, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [-0.5, 0, 0], "faces": {"north": {"uv": [48, 8, 40, 16], "texture": 4}, "east": {"uv": [56, 8, 48, 16], "texture": 4}, "south": {"uv": [64, 8, 56, 16], "texture": 4}, "west": {"uv": [40, 8, 32, 16], "texture": 4}, "up": {"uv": [40, 8, 48, 0], "texture": 4}, "down": {"uv": [48, 0, 56, 8], "texture": 4}}, "type": "cube", "uuid": "0df28adb-1cb7-8e39-2003-0cdf88517947"}, {"name": "armorRightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 12, -2], "to": [10, 25, 2], "autouv": 0, "color": 6, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 4}, "east": {"uv": [40, 20, 44, 32], "texture": 4}, "south": {"uv": [52, 20, 56, 32], "texture": 4}, "west": {"uv": [48, 20, 52, 32], "texture": 4}, "up": {"uv": [48, 20, 44, 16], "texture": 4}, "down": {"uv": [52, 16, 48, 20], "texture": 4}}, "type": "cube", "uuid": "c55f6b38-5cac-2528-3bc3-341bb7392b9e"}, {"name": "armorRightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 12, -2], "to": [10, 25, 2], "autouv": 0, "color": 6, "visibility": false, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 4}, "east": {"uv": [40, 36, 44, 48], "texture": 4}, "south": {"uv": [52, 36, 56, 48], "texture": 4}, "west": {"uv": [48, 36, 52, 48], "texture": 4}, "up": {"uv": [48, 36, 44, 32], "texture": 4}, "down": {"uv": [52, 32, 48, 36], "texture": 4}}, "type": "cube", "uuid": "85ff5063-fab0-689d-b3c1-aa27665b486b"}, {"name": "armNested", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10, 12, -2], "to": [-6, 25, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 4}, "east": {"uv": [32, 52, 36, 64], "texture": 4}, "south": {"uv": [44, 52, 48, 64], "texture": 4}, "west": {"uv": [40, 52, 44, 64], "texture": 4}, "up": {"uv": [40, 52, 36, 48], "texture": 4}, "down": {"uv": [44, 48, 40, 52], "texture": 4}}, "type": "cube", "uuid": "defa2ffb-be6e-2a1e-6459-5e75b88c565a"}, {"name": "armNested", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10, 12, -2], "to": [-6, 25, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 4}, "east": {"uv": [48, 52, 52, 64], "texture": 4}, "south": {"uv": [60, 52, 64, 64], "texture": 4}, "west": {"uv": [56, 52, 60, 64], "texture": 4}, "up": {"uv": [56, 52, 52, 48], "texture": 4}, "down": {"uv": [60, 48, 56, 52], "texture": 4}}, "type": "cube", "uuid": "a6f4e37a-4248-79f6-3d2c-007780377cf9"}, {"name": "armorRightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 0, -2], "to": [4, 12, 2], "autouv": 0, "color": 1, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 4}, "east": {"uv": [0, 20, 4, 32], "texture": 4}, "south": {"uv": [12, 20, 16, 32], "texture": 4}, "west": {"uv": [8, 20, 12, 32], "texture": 4}, "up": {"uv": [8, 20, 4, 16], "texture": 4}, "down": {"uv": [12, 16, 8, 20], "texture": 4}}, "type": "cube", "uuid": "8cfaf881-aa7f-a98a-cb6f-9768ccf30de0"}, {"name": "armorRightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 0, -2], "to": [4, 12, 2], "autouv": 0, "color": 1, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 4}, "east": {"uv": [0, 36, 4, 48], "texture": 4}, "south": {"uv": [12, 36, 16, 48], "texture": 4}, "west": {"uv": [8, 36, 12, 48], "texture": 4}, "up": {"uv": [8, 36, 4, 32], "texture": 4}, "down": {"uv": [12, 32, 8, 36], "texture": 4}}, "type": "cube", "uuid": "44abbadd-78cb-77e4-e0d3-b8c021302da7"}, {"name": "armorLeftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 0, -2], "to": [0, 12, 2], "autouv": 0, "color": 2, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 4}, "east": {"uv": [16, 52, 20, 64], "texture": 4}, "south": {"uv": [28, 52, 32, 64], "texture": 4}, "west": {"uv": [24, 52, 28, 64], "texture": 4}, "up": {"uv": [24, 52, 20, 48], "texture": 4}, "down": {"uv": [28, 48, 24, 52], "texture": 4}}, "type": "cube", "uuid": "9fdde206-b749-1fbd-8c2b-5aeb298604e7"}, {"name": "armorLeftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 0, -2], "to": [0, 12, 2], "autouv": 0, "color": 2, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 4}, "east": {"uv": [0, 52, 4, 64], "texture": 4}, "south": {"uv": [12, 52, 16, 64], "texture": 4}, "west": {"uv": [8, 52, 12, 64], "texture": 4}, "up": {"uv": [8, 52, 4, 48], "texture": 4}, "down": {"uv": [12, 48, 8, 52], "texture": 4}}, "type": "cube", "uuid": "98c13805-5cc5-7390-86b3-f460efe15a1f"}, {"name": "tail", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.125, 10, -15.5], "to": [1.875, 13, -11.5], "autouv": 0, "color": 3, "inflate": 0.75, "rotation": [53.4783, -0.05206, 0.68321], "origin": [-0.125, -2, -1], "faces": {"north": {"uv": [12, 36, 13.75, 38.5], "texture": 4}, "east": {"uv": [12.25, 36.25, 14, 38.5], "texture": 4}, "south": {"uv": [12.25, 36.25, 14, 38.5], "texture": 4}, "west": {"uv": [12, 36, 14, 38.5], "texture": 4}, "up": {"uv": [14, 38.5, 12, 36], "texture": 4}, "down": {"uv": [14, 36.25, 12, 38.5], "texture": 4}}, "type": "cube", "uuid": "d306cc33-e29a-006e-51e7-1bb7d057cfff"}, {"name": "middle", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.125, 10, -10], "to": [1.875, 13, 1], "autouv": 0, "color": 4, "inflate": 0.75, "rotation": [53.4783, -0.05206, 0.68321], "origin": [-0.125, -2, -1], "faces": {"north": {"uv": [12, 36, 13.75, 38.5], "texture": 4}, "east": {"uv": [12.25, 36.25, 14, 38.5], "texture": 4}, "south": {"uv": [12.25, 36.25, 14, 38.5], "texture": 4}, "west": {"uv": [12, 36, 14, 38.5], "texture": 4}, "up": {"uv": [14, 38.5, 12, 36], "texture": 4}, "down": {"uv": [14, 36.25, 12, 38.5], "texture": 4}}, "type": "cube", "uuid": "21ef5294-51f5-71c2-1ed7-e2dd9603909a"}, {"name": "back", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.125, -4.5, 12], "to": [0.875, -2.5, 20], "autouv": 0, "color": 5, "inflate": 0.75, "rotation": [53.4783, -0.0521, 0.6832], "origin": [-0.125, -18, 17], "faces": {"north": {"uv": [12, 36, 13.75, 38.5], "texture": 4}, "east": {"uv": [12.25, 36.25, 14, 38.5], "texture": 4}, "south": {"uv": [12.25, 36.25, 14, 38.5], "texture": 4}, "west": {"uv": [12, 36, 14, 38.5], "texture": 4}, "up": {"uv": [14, 38.5, 12, 36], "texture": 4}, "down": {"uv": [14, 36.25, 12, 38.5], "texture": 4}}, "type": "cube", "uuid": "8d023654-d419-ebfc-5503-00eae6ae0bb9"}, {"name": "back", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.875, -3.5, 16], "to": [1.875, -3.5, 16], "autouv": 0, "color": 5, "inflate": 0.75, "rotation": [53.4783, -0.0521, 0.6832], "origin": [2.875, -18, 17], "faces": {"north": {"uv": [15, 39, 15.5, 39.5], "texture": 4}, "east": {"uv": [15, 39, 15.5, 39.5], "texture": 4}, "south": {"uv": [15, 39, 15.5, 39.5], "texture": 4}, "west": {"uv": [15, 39, 15.5, 39.5], "texture": 4}, "up": {"uv": [15.5, 39.5, 15, 39], "texture": 4}, "down": {"uv": [15.5, 39, 15, 39.5], "texture": 4}}, "type": "cube", "uuid": "8c598ef6-bd6b-fae1-5b62-9c3b1cca2acb"}, {"name": "back", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.875, -1.5, 15], "to": [1.875, -1.5, 15], "autouv": 0, "color": 5, "inflate": 0.75, "rotation": [53.4783, -0.0521, 0.6832], "origin": [2.875, -16, 16], "faces": {"north": {"uv": [15, 39, 15.25, 39.5], "texture": 4}, "east": {"uv": [15.25, 39.25, 15.5, 39.5], "texture": 4}, "south": {"uv": [15.25, 39.25, 15.5, 39.5], "texture": 4}, "west": {"uv": [15, 39, 15.5, 39.5], "texture": 4}, "up": {"uv": [15.5, 39.5, 15, 39], "texture": 4}, "down": {"uv": [15.5, 39, 15, 39.5], "texture": 4}}, "type": "cube", "uuid": "3997484c-1c07-2821-3502-cd5db561af32"}, {"name": "back", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.125, -1.5, 15], "to": [-2.125, -1.5, 15], "autouv": 0, "color": 5, "inflate": 0.75, "rotation": [53.4783, 0.0521, -0.6832], "origin": [-3.125, -16, 16], "faces": {"north": {"uv": [15.25, 39, 15, 39.5], "texture": 4}, "east": {"uv": [15.5, 39, 15, 39.5], "texture": 4}, "south": {"uv": [15.5, 39.25, 15.25, 39.5], "texture": 4}, "west": {"uv": [15.5, 39.25, 15.25, 39.5], "texture": 4}, "up": {"uv": [15, 39.5, 15.5, 39], "texture": 4}, "down": {"uv": [15, 39, 15.5, 39.5], "texture": 4}}, "type": "cube", "uuid": "67789543-674e-9a57-2e1d-f81445c5cfff"}, {"name": "back", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.125, -3.5, 16], "to": [-2.125, -3.5, 16], "autouv": 0, "color": 5, "inflate": 0.75, "rotation": [53.4783, 0.0521, -0.6832], "origin": [-3.125, -18, 17], "faces": {"north": {"uv": [15.5, 39, 15, 39.5], "texture": 4}, "east": {"uv": [15.5, 39, 15, 39.5], "texture": 4}, "south": {"uv": [15.5, 39, 15, 39.5], "texture": 4}, "west": {"uv": [15.5, 39, 15, 39.5], "texture": 4}, "up": {"uv": [15, 39.5, 15.5, 39], "texture": 4}, "down": {"uv": [15, 39, 15.5, 39.5], "texture": 4}}, "type": "cube", "uuid": "baf800a9-220f-3fba-9d70-4dda6af0c212"}, {"name": "armorBody", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 26, 2], "autouv": 0, "color": 2, "visibility": false, "export": false, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [20, 20, 28, 32], "texture": 4}, "east": {"uv": [16, 20, 20, 32], "texture": 4}, "south": {"uv": [32, 20, 40, 32], "texture": 4}, "west": {"uv": [28, 20, 32, 32], "texture": 4}, "up": {"uv": [28, 20, 20, 16], "texture": 4}, "down": {"uv": [36, 16, 28, 20], "texture": 4}}, "type": "cube", "uuid": "271c055f-7f74-392c-9286-411fc66cc872"}, {"name": "armorBody", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 26, 2], "autouv": 0, "color": 2, "visibility": false, "export": false, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 4}, "east": {"uv": [16, 36, 20, 48], "texture": 4}, "south": {"uv": [32, 36, 40, 48], "texture": 4}, "west": {"uv": [28, 36, 32, 48], "texture": 4}, "up": {"uv": [28, 36, 20, 32], "texture": 4}, "down": {"uv": [36, 32, 28, 36], "texture": 4}}, "type": "cube", "uuid": "b517e297-a25f-9008-4604-a0475118239f"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.625, 18, -2], "to": [4.875, 27, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.125, 2, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 28.5], "texture": 4}, "east": {"uv": [16, 20, 20, 28.75], "texture": 4}, "south": {"uv": [32, 20, 40, 28.75], "texture": 4}, "west": {"uv": [28, 20, 32, 28.75], "texture": 4}, "up": {"uv": [28, 20, 20, 16], "texture": 4}, "down": {"uv": [36, 16, 28, 20], "texture": 4}}, "type": "cube", "uuid": "338068bf-6905-2d9f-7de2-7db2b493256f"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.125, 13, -2], "to": [3.375, 17, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.125, 1, 0], "uv_offset": [16, 26], "faces": {"north": {"uv": [20.75, 28, 27.25, 32], "texture": 4}, "east": {"uv": [16, 28.75, 20, 32], "texture": 4}, "south": {"uv": [32, 28.75, 40, 32], "texture": 4}, "west": {"uv": [28, 28.75, 32, 32], "texture": 4}, "up": {"uv": [34.25, 32, 28, 28.75], "texture": 4}, "down": {"uv": [34.25, 28.75, 28.25, 32], "texture": 4}}, "type": "cube", "uuid": "7c9cdfd6-6842-3abc-f017-59a6bf79570d"}, {"name": "Left Arm Second", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.625, 9, -1], "to": [-4.125, 18, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.125, -9, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 4}, "east": {"uv": [32, 52, 36, 64], "texture": 4}, "south": {"uv": [44, 52, 48, 64], "texture": 4}, "west": {"uv": [40, 52, 44, 64], "texture": 4}, "up": {"uv": [40, 52, 36, 48], "texture": 4}, "down": {"uv": [44, 48, 40, 52], "texture": 4}}, "type": "cube", "uuid": "facb93bd-1138-0406-982d-298d8e896c12"}, {"name": "Ritght Arm Second", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.375, 9, -1], "to": [5.875, 18, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.125, -9, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [40, 52, 36, 64], "texture": 4}, "east": {"uv": [44, 52, 40, 64], "texture": 4}, "south": {"uv": [48, 52, 44, 64], "texture": 4}, "west": {"uv": [36, 52, 32, 64], "texture": 4}, "up": {"uv": [36, 52, 40, 48], "texture": 4}, "down": {"uv": [40, 48, 44, 52], "texture": 4}}, "type": "cube", "uuid": "e0db4762-5873-a4e2-e899-90cf9e9d508a"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.625, 13, -2], "to": [4.375, 27, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.875, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 4}, "east": {"uv": [16, 36, 20, 48], "texture": 4}, "south": {"uv": [32, 36, 40, 48], "texture": 4}, "west": {"uv": [28, 36, 32, 48], "texture": 4}, "up": {"uv": [28, 36, 20, 32], "texture": 4}, "down": {"uv": [0, 0, 0, 0], "texture": null}}, "type": "cube", "uuid": "3a553d02-fb2a-5d71-b520-ae6e838b7b89"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.125, 18.5, -1.5], "to": [-4.125, 21.3, 1.5], "autouv": 0, "color": 0, "origin": [-5.125, 19, 0], "uv_offset": [29, 22], "faces": {"north": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "east": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "south": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "west": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "up": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "down": {"uv": [33.5, 25.75, 35, 27], "texture": 4}}, "type": "cube", "uuid": "5b69c92c-27de-6d5d-e3b8-357a5f208b62"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.125, 20.5, -1.5], "to": [-4.125, 24.299999999999997, 1.5], "autouv": 0, "color": 0, "origin": [-5.125, 22, 0], "uv_offset": [29, 22], "faces": {"north": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "east": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "south": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "west": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "up": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "down": {"uv": [33.5, 25.75, 35, 27], "texture": 4}}, "type": "cube", "uuid": "b5b170df-d622-03d8-82f4-d6cb5b4f44e9"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.875, 18.5, -1.5], "to": [5.875, 21.3, 1.5], "autouv": 0, "color": 0, "origin": [4.875, 19, 0], "uv_offset": [29, 22], "faces": {"north": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "east": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "south": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "west": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "up": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "down": {"uv": [33.5, 25.75, 35, 27], "texture": 4}}, "type": "cube", "uuid": "f1930a36-8339-6a1e-dfb0-c82c3623ed45"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.875, 20.5, -1.5], "to": [5.875, 24.299999999999997, 1.5], "autouv": 0, "color": 0, "origin": [4.875, 22, 0], "uv_offset": [29, 22], "faces": {"north": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "east": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "south": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "west": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "up": {"uv": [33.5, 25.75, 35, 27], "texture": 4}, "down": {"uv": [33.5, 25.75, 35, 27], "texture": 4}}, "type": "cube", "uuid": "a753a134-cdb9-f116-9276-0476d56c6722"}], "outliner": [{"name": "armorBody", "origin": [-0.125, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["271c055f-7f74-392c-9286-411fc66cc872", "b517e297-a25f-9008-4604-a0475118239f", {"name": "tail", "origin": [-0.125, 1, 0], "color": 0, "uuid": "f201d992-ac6e-f7c4-2816-348e617e93d2", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["afb4589d-592a-328c-5f8f-c56c75bca4bf", "d306cc33-e29a-006e-51e7-1bb7d057cfff", {"name": "middle", "origin": [-0.125, 1, 0], "color": 0, "uuid": "5026c7ab-4b06-dba7-1705-68dd462905af", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["21ef5294-51f5-71c2-1ed7-e2dd9603909a", {"name": "back", "origin": [-0.125, -9, 15], "rotation": [-90, 0, 0], "color": 5, "uuid": "4019043c-80c8-3ddc-b48c-35e9a880c126", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["8d023654-d419-ebfc-5503-00eae6ae0bb9", "8c598ef6-bd6b-fae1-5b62-9c3b1cca2acb", "3997484c-1c07-2821-3502-cd5db561af32", "67789543-674e-9a57-2e1d-f81445c5cfff", "baf800a9-220f-3fba-9d70-4dda6af0c212"]}]}]}, "338068bf-6905-2d9f-7de2-7db2b493256f", "7c9cdfd6-6842-3abc-f017-59a6bf79570d", "facb93bd-1138-0406-982d-298d8e896c12", "e0db4762-5873-a4e2-e899-90cf9e9d508a", "3a553d02-fb2a-5d71-b520-ae6e838b7b89", "5b69c92c-27de-6d5d-e3b8-357a5f208b62", "b5b170df-d622-03d8-82f4-d6cb5b4f44e9", "f1930a36-8339-6a1e-dfb0-c82c3623ed45", "a753a134-cdb9-f116-9276-0476d56c6722"]}, {"name": "armorHead", "origin": [-0.5, 27, 0], "color": 0, "uuid": "374cfed6-a18a-ea58-8cbb-f29e183ddb81", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c7c3ad6c-3e05-acf8-10d8-20313f6eb901", "0df28adb-1cb7-8e39-2003-0cdf88517947", {"name": "antenna", "origin": [-0.5, 3, 0], "color": 1, "uuid": "21bac58d-2805-67d9-8963-0d5f833bd5b4", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorRightArm", "origin": [6, 23, 0], "color": 6, "uuid": "4d7059ca-6529-2ec3-79c6-c8ee59f848cd", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c55f6b38-5cac-2528-3bc3-341bb7392b9e", "85ff5063-fab0-689d-b3c1-aa27665b486b", {"name": "wingRight", "origin": [6, 23, 0], "color": 7, "uuid": "e42e1b49-0be5-ee28-2ec8-5b5c32debbf5", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 23, 0], "color": 8, "uuid": "c52b2100-814b-c0a7-8a51-cfb990373d0f", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 23, 0], "color": 9, "uuid": "5f19cebd-8675-1ee5-f06f-9008a801c13a", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-6, 23, 0], "color": 0, "uuid": "61749878-df1e-fa75-c668-65bc631f264e", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["defa2ffb-be6e-2a1e-6459-5e75b88c565a", "a6f4e37a-4248-79f6-3d2c-007780377cf9"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 1, "uuid": "cc8976f6-45f8-7260-a457-97ada33f4d80", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["8cfaf881-aa7f-a98a-cb6f-9768ccf30de0", "44abbadd-78cb-77e4-e0d3-b8c021302da7"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 2, "uuid": "6e5c3db2-9cbb-234d-61d9-a48c27dfe67f", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["9fdde206-b749-1fbd-8c2b-5aeb298604e7", "98c13805-5cc5-7390-86b3-f460efe15a1f"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 3, "uuid": "0bc981b7-97dd-81e9-2dde-0824694e56f2", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 4, "uuid": "f22a2159-8db9-f1ee-40df-7debb91bf9ed", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorBody2", "origin": [0, 25, 0], "color": 0, "uuid": "74a1d2e9-eeb4-329d-6e93-dd9744d7b352", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\fourmungosaur.png", "name": "fourmungosaur.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "b4f4ae19-04ca-750f-396c-446ac5f3c85a", "relative_path": "../textures/models/ben/fourmungosaur.png", "source": "data:image/png;base64,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**************************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"}, {"path": "C:\\Users\\<USER>\\Downloads\\bio_tetrasaurian.png", "name": "bio_tetrasaurian.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "47182f70-3f74-c5cf-ed94-de2b94b5cbdf", "relative_path": "../../../../../../../../../Downloads/bio_tetrasaurian.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\Downloads\\fourmungosaur_2.png", "name": "fourmungosaur_2.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "d8f97005-d970-ca10-51e8-68ce468d14de", "relative_path": "../../../../../../../../../Downloads/fourmungosaur_2.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\Downloads\\fourmungosaur.png", "name": "fourmungosaur.png", "folder": "", "namespace": "", "id": "5", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "e916d5d7-555b-ee6e-8ee0-7bdf2dbad7c3", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}], "relative_path": "../../../../../../../../../Downloads/fourmungosaur.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\Downloads\\bio_tetrasaurian_2.png", "name": "bio_tetrasaurian_2.png", "folder": "", "namespace": "", "id": "6", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "38c6d1f5-6724-8c96-8029-493c7a6a9562", "relative_path": "../../../../../../../../../Downloads/bio_tetrasaurian_2.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\Downloads\\fourmungosaur_3.png", "name": "fourmungosaur_3.png", "folder": "", "namespace": "", "id": "7", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "35da3e15-ce4d-cfbf-aeaa-8958043edeb8", "relative_path": "../../../../../../../../../Downloads/fourmungosaur_3.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAH+BJREFUeF7tXWuMXsV5Pi5e1qyXBXuxzS5OZAPGQBzqpP0RpFoiqMpFQk3aKqCq/dVCG/cWV2mURkBDSqImatoSodZJcapKVRRZUdqmikSAKlRyJf60iQsE4kIxCs6uL9iG9XrxslCq75zzfux5vzNn3rmdOZdn/6zXZ+admWdmnnnmnduaJPLPLydb3oqchajJ/3NyYk3UDCDxXiMQvfGBAEAAve6BkQsPAohcAVAAkSug58mDACI3ABBA5AroefIggMgNAAQQuQJ6njwIIHIDAAFEroCeJ99ZAvhIMplW7XeSxUZXMQig0dXT+cyBACJXMQggcgX0PPnGEoDtCE7xqF6hAHrewlH8SgRAAJEbCBRA5AroefKNJwDTkRwKoOctGsU3QgAEYASX/8BQAP4xhUU5Ao0lAHkRZCFtfQoy6/ahQAD22CGmOwIgAHcMnSyAAJzgQ2RHBBpHANKR2jScqS/BEVdxdBCAGCoEDIAACCAAqCYmQQAmaCGsbwQaSwCqEZt7+XUje9NXBUAAvps07JkgAAIwQStAWBBAAFBhUoxAbwigqTsCQQDitoqAARAAAQQA1cQkCMAELYT1jUDjCEBawKbP7aXlAAFIkUK4EAiAAEKgamATBGAAFoJ6R6C1BOAdiUgGQQCRgEeyKQIggMgNAQQQuQJ6nnxjCUC13m9bX1gFsEUO8bqMAAggcu1CAUSugJ4n3xgC8D3i6+q1KYoABKCrKXwPiQAIICS6AtsgAAFICBIMgegE8A/JNVHfBoytBEAAwdo2DAsQAAFEvjYcBCBopQgSDAEQAAggWOOC4eYjAAIAATS/lSKHwRBYU/fz3HV7+02Ri+0TMM2va3hMQVwRbHd8EACrPxBAuxs0cm+GQG0EYDvyU4c0iW8Th8PWFyKAAjDrMF0L3WgCGHTC1Zd/SkjAJk5ZpYIAutbUUZ4yBBpNAKszvLpjV1WlNJyuOYAAdAjhexcQaA0BDMDWdW7dd5MKAwGYoIWwbUUgOAFIZDuB99vr5gs4/t2FmRFcVZ1c9f8SmzpF0dbKleQbPgAJSt0N0zgCuO+Rnx2ifd8H/zvhJGBCAIPOr7Onq9quKwEQgK4FdPt7owlgAL2EBMpIgXd+lS1d9YIAdAjhe5sRaBQBDICUSvbVHVM1zZDawhQgXBOmjWZ7dm5KEznx6vn095eOH42+C5VK/ekrtw8PpG25bH3634eOnEp/d10hNY4AdJ2xyqfg0wlI+YACcCMHEIAbfqFjt4oABmCY+AB8gAcCsENR1fHPLb1eMPi3Cz+NqgR+d+qqwnH0SycuTvPXFyUQnACotiWrARLJbkoAEptlTbzrHZ/KHEriggDsiLPuWI0jAInXnpNA1RKgxB4IwH+zIwK47sqJ1Dgf+XmKdSsBPvLz/JAS+J/jS532BdRGAKomtloZSD33EgIwseW/+bfHYmgFAAJodltoFAEMoJJIdgkBmNhqdhWFzV1oAtDlfmZqba0+AT7yzy+8ocsiFIAIIcdAEh+BYxKF6H2Z4+swAwGAAKJeysmdhL83Npf+19+szOrartF3bhcEkMEXigB0lcMvoiElENoXQAqAj/yxcNDhFPp7bVOAR8cWjMoSigCkmfjAypQ0aKvDxWr4IIBmNBsQgKIeQAD1NND1Y5cUFOj5ldeC7guoO716ULRPJTgB6Eb+e1Y2p7l/PrmQ/r42WZf+nkmKzqHB/80n2XzN9NvquDydz4+drESv60QQSwEQ6HV3yLrTs++a9cQEAYAAgo64umZcd4esOz1d+WN/D0YAupGfCn7Xyob0n1uSsfT3Z9dlhzBC/XzuQn4oJVlJk3ho7Kwoqa4qASiAsFMOUeOKGAgEAAKAAojYAWMnHZ0AVi4qb3/8IpBD+QMee5LJSsxU4fgGIzIy9qZsFRQKIExTrVuS151eGNT8WQUBgACiKgB/TRmWbBDwTgDSuf9t49NpfmcW/y/9fS55M/2tWgVwVQC0gkCrAJcmF6XpzU/+TPr7u8unRfh1TQnE9gGIQEegYAiAAEAAUADBulfzDUcnAIJocjHzypMCoP+nNX/ayiuFlHYS0shP8UgBLE5mqw70AwUgRRbhuoQACCCvTRBAl5o1yiJFoDEEwDO8ezGbo/tSAIcnMx+D6gcEIG0yCNclBEAAUADwAXSpRxuWxbny+bqqYfrOwUMfHtFlcF8yIdtIoDNk+X3/eOY7ifWzvLzi3IZc8r5vdpsT/tILQWYWz5Rm84FkKWr5XbAbxHXOPAgABODaCF3igwBc0AMBuKGXJIlWAey+2i2Nwy9Uxu+9AtApsNnstKnqBwrArXkmUAAaBQACcGxh1dG1BAwCqAQQUwDH5qlsgHnHf/9Wt6vNHj+WXZGWKJRA3xXA/cnmSh/A6fwMybCaOSHMVd8HoWse8AGwG110gPn+3lgnIAjAd1WX2gMBuMEMBeCG36gPwFPH59lSKQEogGoFwHEcKoJcCcAH4NgB4ANgPgAQgGOLMouuUwAggGo8oQDM2ttIaO4DuH73TWmYma1+bxWeP5bdqvzjw08W8gAFYKYACDxSAvOTG0UtAPsAFDBBARQVAAhA1J+8BTJVACCAIvRQAI5NEQog7k5AWwLgRCBtBlwxHFw85tyHpGmHCOeceSgAKIAQDVNqEwQgRao8HAjADb+RVQCaApBZV18Azf3JHnwAxQpzJYCnJy8WtQDyAUABMLigAMoVAAhA1K+cA4EA3CCEAnDDb6gAZq7ekVq6bOqSgkXfCuDVhddS+/MvPJf+xiqA3SoAVRIUgGMHgALIFAAIwLEhWUaHArAELo+2JnQH3rg1GxlVP2eOZSNZrJ+7HJ3Y1PF5/kkJ+FIANPLzdEgJWOOn2Bs/zd5foJFy1+LraVJ85OT/T39TvvgOvJH85nvyebrW5RJGHDkrQPE0h4iE5rXBHph70VmFaxOpCAACAAEUm4eiI4IAXLqZOm7nCYAXnSuCrioAKretEiDvv2rkJ/veFICm489MZa81T88tVSqA07MT6XfaYy9WAlAAYRhGYzW4AgABZE5B06kACKCe/oApQKTjvKQEWqcALC/4uD6fU+uIYLjnn59jl/YHzQ1CKjPSPfHSbOjCDffW01y7ZgWg7PjwAeiqzs93EEA5jiCA6sdf/bS+JAEBZEjWPgWgCmwdAeQj/zvXX5oWYeJ8dhGN6YjO4xMeS+szZ/BPzp9Lf5sqBh5fdYOQrgOpvP80YtN31SoAP2XHfQCU/siqQE0KgNLdeceHClC8eOasDprC920bNxT+PnLoB0bxKXDvnIAggCKBgADySztBAFYE4hoJCkC6DJgrAOnIzCtGKu292Zf6AhRzcBrhyftP5ZGuAlB4vhpgeyOPtKErVx1yA64jP8+HqRLgNxDFPk0IAgABZG2ajcAgABnlgABkOI2EaqsPwHaEtoRJHG1EYXhWANK78yjDpBx0CmBYQMcpgNap1xAFwCsUPoC2bAV2nAKIe7JlQBDAogi52FMAEECOABSAqL2KA9kSgHT933YVQFcA7mPgUxFdfO3In/s4du55b6kpU++/zgdA36WrAlAAUAC6Ni76DgJQwAQCqGw/cAIaOgFV6/i6/QCiXmwQiN8UZL0PQLoKoHASUpbJ+67y8uu+GxS9MuiIoqC5f0MUAFYBWj4FAAFkO/V0G4ES9vAGCCBr+J0hANdTfa3zAdC4w84C1EUI2hGf8if1/lN4oQIIvgpA+TF9q0/41h93/lFywXwAB79XrlRYflvrAwABZPULAihv58bLgCAAX7MgIzvWPoCuEMBt49NGgM1ce3mh49PefRURcOM6XwEf6Xl8PtendCkf88+/YlQeaeDaVgEURKC6KUi3CqAa+UMrALJ/RKMEoAAirwKAAGQUAAIox4nvBOShQACK9tU1HwCNwLruJD0NKLUzkm4gH4BqqzDlU+fkU31XllO4M3BEAbA59vi7tqdJ8I7qOvfn+VbZX/7R0WJQ8nHk+YQCiKwAjC8FVTgBQQDZZaGmy4AggJZeCtoVH4AtAfC5t+6MgK/TgNwOz4fxPQCaW4H5oSDdaoCp809njysMThhcAfCdjTvf8+40yvK6CzpR5fX7+IV1qb0jP3yqYJfj09rTgCCA7Fy/9AIPEEC+V58RDggg7uOivV8FMFYAxOfshiApEdgOM95H/jwj0rMAfCTWvQsgLadq557O58DtD+8YZB9oFWB+LntRqa6fmdnsMlg4ARWIt9YJCAJIEQABVFMJCEBDta0nAA0RhBppnOf8PGNsJyB95nf/2S4Djnj/KQHdyzu2qwAKBcCLHXoVgNKDAuiqAgABFGqWd3SaWoAAqrcEt24ZUPXWn+n9/l1VANLbgk0VAvkArE/9qRLs2CqAqpi+LwLh6YxcDabaAZhHJIJs3SoACIBVveV14SCADAHfqwAgALOWZbwK4Prar+vyoVnx9KGtVwG4aQ0RPH5sTp+ZVSHev3U26yDHFtLf3kd+Nhc37YiuTkCp95+yydPjYKpWAShcY04DMtw7MwWgcummAiAAGQ+AAIovBIEAZO3GNFTvFcBlv7TRFDOE94jA7U+MFa0Jvf8USXcaEAqgurJAACAAj93Z3BQIoGVnAbrmA5i8c715q62IMX3jlV7tcWOnnzke1D4Z910OVb5v+cdsz7zrj84HoFICrvsBuPf/5YP/kSale3acfC6dWQVoqw8ABFDe9UAAMkoCATCc2uYE9EUAvjuMrvmFVgK+y6PK7699hz0HHsgH0DQFQPnBKkDk+wBAAHEVAAgAPgDdYBf0+1e+9VUv9nfu2Fmw8zvfv9OLXTLytVsPFOwdee6IV/vcGC+Pa2Kq/D72G3e7mk7j87MKKqM0N6d9Ab58ADT3p3ThA1DUQNP2AYAAyisKBCDjJfIB9J4AdHN/DmdTzgL4IoDhHDNXAqEUQOiR37cS0OX3Ex/7eCFJ2pmpugX4i2PnRD1zYnoqDbdw9owofKhAUxuyfSaqfCwvS5+mCpNDb/sAQABZBdHICQLI8AABdJwAeMf/wqYb0op/YuFkgbJuntqc/n33qWfDUBmzStd969L15QSk5H17zzlYob3/PD3X8ujyu3jgfGl74EpAOvKTMSgAWTdzVgAggCLQrh1GV226DqWLb/rdtTy6/IIAWj4F+OTy2tI2pVIAPLBvRcAf+iAFQOny9HwrgFBKQNeRTDu2aXhTIpDmV0UApvnj4VUK4KNjWwpB/2XlhFNS9yxvKMT//PjZwt+d9wGAAMrbj2mH0bVCaYfS2bH9bloeaX5BAB1TAHzk5w1ONyLbNlCKZ6oAcBrQFXG3+K/+axgvvc4HQErAlwLgIz+h0jsFAAJw6xB9iw0CiKwA+tbgQpV3y94r3hrYPrH/5TWh0oBdNQL7Zrel+Jv+TM8tlUbhT5zF3rNvWi5peDRWKVKacCAAT0BamgEB2AEHArDDbSQWCMATkJZm9iUT5QpA+P7ASLIsHhSAZcX0JRoIIG5NgwDs8IcCsMMNCsATbr7M3J9sLlUAI6fyejKyS3EFAUiRgg/AE1JhzIAA7HAFAdjhBgXgCTdfZlQEwO1z7z5WAXzVQM/twAcQtwGAAOzwhwKwww0KwBNuvsxICYDS074n0BNfAQjAUwuEAvAEpKUZEIAdcCAAO9ygADzh5suMKQFQuvcmJ436AF9ufCBZMorvq7y+7LQ6875A8GEHCsAHivY2QAB22IEA7HCDAvCEmy8ztgSgSl91FmCoAHIfQdt3CIIAPLVAKABPQFqaAQHYAQcCsMMNCsATbr7M2BKA7v5+yh+N9JTO6dmJ9BMUgK8abLkdKIC4FQgCsMO/9wqAOq4dfG/HuujkePrHm5uX099dvReAOtrTkxcXINu1+Hr6t8qrTjiHwik0Abi2Dx6/KcoBBJBf5OFawaEatmu+fMcHAfhBFATgB0dnK64KgHd8fnnmM3/wdCdIli7coL3zKgXA58Y3PrircEqPLgsl3G5/YiyrQ8NXgZ0rPjcg9QH4Sq9pSqATjdOlckAAMvRAADKcTEPFVgK9JwBeYcqLJSjg7qsLUb558/H071f+/tX09zVfLr4S/L9/nL3ia/oG3L7dxZFT3LAOv1AZ1Hbn2h2TW43u3Hvqzy/PcPn37J78y2/J7s8nPC7/zcvSv4fPg9esAEzPAojxNwwIAjAELHRwEEA5wiCAMC0PBBAGV2urSgLIR/53rr80tT1xPhsQP/Hsf1WO/DwjOiVAI//1yWQadWZr9sqt6mf+2EL6aWl9JuZ+cj5/PVehBGwVAOFCr/aqfADffDB7A5L/qJTA3umrsqA1KQDft/3aXkZK+IAArLtqmIgggHJcQQAKXCyvIwcBhOm/zlZ1BKAamT/zwbk0bZrj8oxw38AHvl6eVenIz2OTEvhxsph98qQA+AinWgXQzfmp/KQE5r7901SymE4tdBVM+xEonM7L7zoCmyqA+YU3CkU4uHgsqh8uauK6yozxHQRQRB0EUN0KQQAxemnANG0JgLJ0+2OPF3LHR376+LVbDxTCnfn0nzqVKrQC4CMXZZZGfp551Zyf+0CGHcjRB6D16lMGA53iMyUCTAGcmnu4yKEJgHd8XhJbIgAB5FMfXdMAARQQwhSANRhXAiBz5BNYbX515z/08h8VUt5zxV8P/7YhgVAEQHP0mcXsFV9aBSBvv2qkV835ef8cGTkNlYB25K/5bj9TJeDqg9Dxne47CKBmArh1957kc//286X18tlf/M/k+4cPJSCAt4lG14BBADqEqr+DAAwJgO8DoOiq9XpSAoPRf9D56YeTwKDz04+EBGjEpzih9gHwOfqjDxZ3QlL60jk/b46+VwFmptaWtvi6R1qVEsAqgBthBY+tmwL4IoBBQYgEVnf+wf+DAOyrGQRghh0UgFQBUDh2FkBKCKfu/PVk78fuLKSmIoD93zqQbDrwjUJY7YhPoT2fBaCR7NHPZDsTpXN+OuVX98hr1vwRGgQQiQCqpgAgAHTMuhAAATCkdXPSmWuzU2408tPee5USWG1+7X17UxVQ5QQcdP6/eukvhtH+8OvZyKua6/N8zD//SmXbMd15xs/zk3Hl3v7l7Hz//OTG9LdpenU1fKSTIQACqJkAKLmTG75aSHnz2Y8P/wYBoHvWhQAIwNMUYHgKT1Nzu+77VGWI1Z1/EJDOFuy9oXjPAB/5h0Y9+QBo5JfO+fd+eyXNgu/TdnV1hL6mAwKomQCo40596u0Rf5AF3vEpWyCAvnbNesoNArAkAD4C607xjezUy9N99LfKK1p1qvArN/xcGoFO/Y0oAUcFYDrn/+jYlkIBaBlO6v3X+VzIOD/lR//PNwLZ3ndQT3dLElpVof0AsX0kIAAQQAEBEEBYKgABhMU3mnXTm3x4Rk3vE/B927DpnH/PI8Wxg0ZouilIOrIp986zMwH8BiKOH51VaLwCSCayq6QCHUoy7QBQAKaIKcKDALKHQUAA1Q2qaY+LggA8EQCZsb7NNzew/9nsFmH6Ud0nQPfru75ARNei8/cMVN7/e5az232V7wLkNxJJR2KpAlBVU+t8AFAAnntcw8yBALJz+SCA8oYJBdCwDtvU7HBnHL9Pn17Wobv1bMsx+6tXpXNSetOQv29ASoD29qtuBqL0sQogmwI0ZackpgC2PSdwPBBAEeDOLAPmUwAQQOAO1BXz4+Nj6QhNL+nwtwipnKa+AP4kGrdLSoDu8KP1eu7tp/S9rQIobgTCKkCYFg0FEAZXb1ZBABmUIABvTapgCAQQBlfvVlVzdVIGtgnSSM8VBvct2D4OqsuX7zsBpc5HXb5CfYcTMBSyHbcLAiivYCwDujV8KAA3/GqPzacEvjLA5/wqu/cnm1OfhGofwL3JSaM2JT0LoCtna3YC5k+J4SyArkbxvRQBEEB5wwAB2HUYI7a2SwKxQiDAvfiuaUhXEXQjtvQMAOW3dz4A9pio9NSka/2q4oMAQiEb2C4IIAO4dT4AEEDgngHzQRGAD8ANXhwHdsMPsSMjAAJwqwAQgBt+iB0ZAd8+ANUFJLbFpFOSFJ/7NrjPIfQcnE/V+KlL3/c6mOIGH4ApYj0PDwIwawCdJ4Brkh3puvB127M34x4+yq6KYXiZhjeDG6FDIRBqJ+D6sUuyG3I8/2zcuiO1+NLRJwuDXN078d6x/aa0fGeOPVdawvMrr0UdhJ0TN+3QpuE9twuYs0QABGAHHAgACsCu5TQkFn8deDrJXirS3QgkvfPOtwLQjah1KwBejby8uvyGbgZQAKERbrl9EIDfCgQBGPoM/MIPa6YItI0AqHyqkZWWMU/PTqRBQ68CUH5USgcKQOM0NG2wCO8XARCAHzw7SwB+4GmvFd9zWFMkYo8gylt9hQV56NQJYcjyYG0tv055qIiXo2B6+pLHd/YBONVeByKDALY5LeOBAMo7AQigJeTQewKge+5V9ZW/gKP63HkCyO845OXX3VykOiVpakfXjaAAdAhpvoMA8qeuQADlCIAAHHtYw6P3nQDIq66qJn5cl/YHDMMrOshD2WXI2p/G+QBYeWjfhO+5uxYYYQAoACFQqmAggOyKMBBAjgAIwLFHtSw6CKCaAHh1DhUB+Qa6ogAU7xmomrPOew8nYEuIAAQAAkibKgigJT3WczZBAGYEQPCTEpDO9VXVFt0HkK+CqOb6zgqADFg6E3XNHT4AHUKa7yAAEMCgiYAAHDtSW6ODAOwIgOr7i2PnnKo+tgLQrYLoFMDwdGIeULc/wAmskshQAI6IggBAADZNiJyAIAAb9BoUBwTgRgD8XgF64EPqG2i9AqBrwvM5PikArAI0qJNXZQUEAAKwaapDBQACsIGvOXFAACAAm9bICWB6bik1M0IMWAWwgbe+OCAAEIBNawMB2KAWII5rB6bbZ1VZU90GG6AoVibv2rRFFk+xDm26/CVLTB1qZCehq0FpfM/l1+0ElGaLwtney9D7VQAQAAhA1NlAACKYWhfINwE0fcTnFQQFIGyynglAmOpIMN8KCArA8WEKPgUAAdg2bVk83x1Alurbe/15+LZPgUAAngigbR2fGrKrAiA7dXWEphFA28sPAgAByAZBhQRueweQFV6tANpefhBAzwngy7/y4bQNv3jmrLgvDAJu27ihEP7Iwe+lf4dWAr4UwM49703zu/fgPzn1AemOPSNwKwL7Kj8l4VR4X4WKaceXE7CtUwAQAAggZv+LnnbfCeD3b3mfUx2olIBKGvO9/06Jl0SemVpbaZJGfgrkWwHonISxy8/zBwXQ8ykACMCvAgAB+Kb0wPagAMIqAFX12foKXOfAvhUAlY/7AnTNNlb5KV/0MhEUABSArq1WftdNAUAA5QiAAJyanb/IUAB+FQDVzJFDP8j+6Wn50PfI78sHwFui9EUflY9E1bJdy89HfvobCgAKwIlNuQIAAbC3EhtCgCAARTOHAgisABju8wtvOBGOLrJqFYDP/UMpgBGfQEPKr3qNGAoACkDXp4x8ACMKoCEdAATwYmlfBwFEIoCmHCLyvQw4JIB8Z+AIe/DXgjUSWRufB1DY23nHh0qJzHUfgBN7NiAyCAAE4NQMlT4AEIATrnVFBgGAAJzamooAtEogD6BaDtO+Kuw48of2ATiBWmNkEAAIwKm5gQCc4IseGQQAAvDSCDkR0OnC5R8dLdoXPp9NkcTr32SX+RjG37U9NaXK35cee7zXfaDXhR80jFjLgF1xAlJHBQF44dHajYAAoACCNLrxC+tSu0d++FTBPr38Q/+p8gGYnpqj9X++z2Dne96dJrW87kJpOetWAPwpMN/lV633qyoZBAACAAEEQaDcKAigRrAlSfV9CvDJ990igck4zMzsJZkCUCwHzk9urLS5a/H19LuxD4BZpfX/+bnXStP7s0MP1zoIEgGEKr/p68K1Ft64FdUQAQQAAqihmQ2TAAHUibYgrb4TgOtOQJUTkP5fpQB0PgD67ksB8KZAqxRN9QHYlh8KQNDpVwcBAbgdBgIBmDU4qQ8ABGCGq3XophAAFaDuy0VDKQDdyK9TAMarAItnKtsAPwvQdAVgW34oAEMqAAGEUQAggPKGKFUAIADDjmwb3BcBSEfwpr0m7KoAjA8D5UBJveBiKaw5VdiU04CmTkDT8kMBGDIBCMBNAYAAzBocCMAMr+ChQQBxCEDnA+AVr10NaJkCCFV+KABDygABgAAMm4xTcKkPwJYAQQCG1QMC8EsAUuefzgdgWI3J8C5AhRKgPfdX3PELBdN13wgknQLYlh9nAQyRAwGAAAybjFNwEIATfP4jgwDiEID3OTAZbIkCCFV+KABDjgABgAAMm4xT8GA+gDxXIADD6gEB+CEAmvvrnrwib34sHwA1D/IFwAdg2GG6FhwEAAKos03DB1An2oK0fBGA6R5+1Y5AUzuCIkYNQuWUlutPVi4tzS8ph4fG3hKVR5ru+ZXXnI7E87cAuQTfsveKQoanb7xSlH/bQKefOV6IemL/y5Xlcyq8bSabFA8EELY2pB2RcgECcKuP2gngmmRHynDXbb86zfnDRx+pJBXT8G5w6GO7EgBP4Qubbkj/64mFk4VPN09tTv+++9Szhf8PdTnobePTaTqqdPXI+AlhSgCcCExHfoovTdebAshXH/hGnHdsvyntH1IF5Af1JKHyv3T0ybAKwLRDm4b3BYjKDgggLMLSjshzQUoABGBXPyAAIW6uBEAjPk9OpQB4uL8cL76W6zpS0MhP6ZACoL+5AhHCpAzG0/vu8ulSheNaLtN8qoiHTzHuTU46TYOHPgCFAuD5dm1vOhxMFY1T4QeZMR3RTcPrCuz63bVCQADZVIN+QABLlX3Ktb3p2jsIQIcQ++5aIZwA+MjPs8NH5K4oAN7xTefihtWmDa6bepAS8KUApueW0jyp7Lm2M22BWQApEUABOL4LAALIFAAIoJ0E8P8tzUUSKYELbgAAAABJRU5ErkJggg=="}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": true, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}