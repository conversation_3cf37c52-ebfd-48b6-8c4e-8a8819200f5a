{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "chronosapien", "model_identifier": "", "visible_box": [5, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 27, -4], "to": [4, 34, 4], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.51, "origin": [0, 3, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 1}, "east": {"uv": [0, 8, 8, 16], "texture": 1}, "south": {"uv": [24, 8, 32, 16], "texture": 1}, "west": {"uv": [16, 8, 24, 16], "texture": 1}, "up": {"uv": [16, 8, 8, 0], "texture": 1}, "down": {"uv": [24, 0, 16, 8], "texture": 1}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 27, -4], "to": [4, 34, 4], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 1, "origin": [0, 3, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 1}, "east": {"uv": [32, 8, 40, 16], "texture": 1}, "south": {"uv": [56, 8, 64, 16], "texture": 1}, "west": {"uv": [48, 8, 56, 16], "texture": 1}, "up": {"uv": [48, 8, 40, 0], "texture": 1}, "down": {"uv": [56, 0, 48, 8], "texture": 1}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 12, -4], "to": [7, 26, 4], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 32], "texture": 1}, "east": {"uv": [16, 20, 20, 32], "texture": 1}, "south": {"uv": [32, 20, 40, 32], "texture": 1}, "west": {"uv": [28, 20, 32, 32], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 13, -4], "to": [8, 28, 4], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 1}, "east": {"uv": [16, 36, 20, 48], "texture": 1}, "south": {"uv": [32, 36, 40, 48], "texture": 1}, "west": {"uv": [28, 36, 32, 48], "texture": 1}, "up": {"uv": [28, 36, 20, 32], "texture": 1}, "down": {"uv": [36, 32, 28, 36], "texture": 1}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [8, 13, -2], "to": [11, 25, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [4, 1, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 1}, "east": {"uv": [40, 20, 44, 32], "texture": 1}, "south": {"uv": [52, 20, 56, 32], "texture": 1}, "west": {"uv": [48, 20, 52, 32], "texture": 1}, "up": {"uv": [48, 20, 44, 16], "texture": 1}, "down": {"uv": [52, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [8, 13, -2], "to": [11, 25, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [4, 1, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 1}, "east": {"uv": [40, 36, 44, 48], "texture": 1}, "south": {"uv": [52, 36, 56, 48], "texture": 1}, "west": {"uv": [48, 36, 52, 48], "texture": 1}, "up": {"uv": [48, 36, 44, 32], "texture": 1}, "down": {"uv": [52, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-11, 12, -2], "to": [-8, 25, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [-4, 1, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 1}, "east": {"uv": [32, 52, 36, 64], "texture": 1}, "south": {"uv": [44, 52, 48, 64], "texture": 1}, "west": {"uv": [40, 52, 44, 64], "texture": 1}, "up": {"uv": [40, 52, 36, 48], "texture": 1}, "down": {"uv": [44, 48, 40, 52], "texture": 1}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-11, 12, -2], "to": [-8, 25, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-4, 1, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 1}, "east": {"uv": [48, 52, 52, 64], "texture": 1}, "south": {"uv": [60, 52, 64, 64], "texture": 1}, "west": {"uv": [56, 52, 60, 64], "texture": 1}, "up": {"uv": [56, 52, 52, 48], "texture": 1}, "down": {"uv": [60, 48, 56, 52], "texture": 1}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [1.9999999046325683, 0, -2], "to": [5.999999904632569, 12, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [2.0999999046325684, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 1}, "east": {"uv": [0, 20, 4, 32], "texture": 1}, "south": {"uv": [12, 20, 16, 32], "texture": 1}, "west": {"uv": [8, 20, 12, 32], "texture": 1}, "up": {"uv": [8, 20, 4, 16], "texture": 1}, "down": {"uv": [12, 16, 8, 20], "texture": 1}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [1.9999999046325683, 0, -2], "to": [5.999999904632569, 12, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [2.0999999046325684, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 1}, "east": {"uv": [0, 36, 4, 48], "texture": 1}, "south": {"uv": [12, 36, 16, 48], "texture": 1}, "west": {"uv": [8, 36, 12, 48], "texture": 1}, "up": {"uv": [8, 36, 4, 32], "texture": 1}, "down": {"uv": [12, 32, 8, 36], "texture": 1}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.999999904632569, 0, -2], "to": [-1.9999999046325683, 12, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [-2.0999999046325684, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 1}, "east": {"uv": [16, 52, 20, 64], "texture": 1}, "south": {"uv": [28, 52, 32, 64], "texture": 1}, "west": {"uv": [24, 52, 28, 64], "texture": 1}, "up": {"uv": [24, 52, 20, 48], "texture": 1}, "down": {"uv": [28, 48, 24, 52], "texture": 1}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.999999904632569, 0, -2], "to": [-1.9999999046325683, 12, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-2.0999999046325684, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 1}, "east": {"uv": [0, 52, 4, 64], "texture": 1}, "south": {"uv": [12, 52, 16, 64], "texture": 1}, "west": {"uv": [8, 52, 12, 64], "texture": 1}, "up": {"uv": [8, 52, 4, 48], "texture": 1}, "down": {"uv": [12, 48, 8, 52], "texture": 1}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 32, -1], "to": [1, 36, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [0, 36, 0], "uv_offset": [23, 9], "faces": {"north": {"uv": [23, 11, 25, 12.5], "texture": 1}, "east": {"uv": [23, 11, 25, 12.5], "texture": 1}, "south": {"uv": [23, 11, 25, 12.5], "texture": 1}, "west": {"uv": [23, 11, 25, 12.5], "texture": 1}, "up": {"uv": [25, 10.5, 23, 11], "texture": 1}, "down": {"uv": [25, 11, 23, 10.5], "texture": 1}}, "type": "cube", "uuid": "6f8bcb3c-9c11-ae4a-8016-5e09deee803a"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 36, -1], "to": [6, 37, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [0, 39, 0], "uv_offset": [19, 9], "faces": {"north": {"uv": [23, 11, 25.5, 12], "texture": 1}, "east": {"uv": [22.25, 11, 23.25, 12], "texture": 1}, "south": {"uv": [22.25, 11, 25.5, 12], "texture": 1}, "west": {"uv": [22.75, 11, 23.25, 12], "texture": 1}, "up": {"uv": [25, 11, 21, 10], "texture": 1}, "down": {"uv": [25, 10, 21, 11], "texture": 1}}, "type": "cube", "uuid": "be37b7a1-5f62-fc41-11c9-2aa9c7e7e2e7"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 37, -1], "to": [6, 39, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [2, 40, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 23.5, 12.25], "texture": 1}, "east": {"uv": [23, 11, 24.5, 12.25], "texture": 1}, "south": {"uv": [24, 11, 24.5, 12.25], "texture": 1}, "west": {"uv": [24, 11, 25.5, 12.25], "texture": 1}, "up": {"uv": [24, 11, 23, 9], "texture": 1}, "down": {"uv": [25, 9, 24, 11], "texture": 1}}, "type": "cube", "uuid": "23ba3d26-563b-8f71-cd92-c68b3475508c"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [3, 37, -1], "to": [5, 40, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [1, 40, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 23.75, 12.5], "texture": 1}, "east": {"uv": [21, 11, 22.75, 12.5], "texture": 1}, "south": {"uv": [24, 11, 24.75, 12.5], "texture": 1}, "west": {"uv": [24, 11, 25.75, 12.5], "texture": 1}, "up": {"uv": [24, 11, 23, 9], "texture": 1}, "down": {"uv": [25, 9, 24, 11], "texture": 1}}, "type": "cube", "uuid": "80993334-55ba-35f5-bbfb-d564c731fd01"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 37, -1], "to": [-3, 40, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [-1, 40, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24, 11, 23.75, 12.5], "texture": 1}, "east": {"uv": [26, 11, 24.75, 12.5], "texture": 1}, "south": {"uv": [24.75, 11, 24.75, 11], "texture": 1}, "west": {"uv": [23, 11.25, 22.5, 12.5], "texture": 1}, "up": {"uv": [23, 11, 24, 9], "texture": 1}, "down": {"uv": [24, 9, 25, 11], "texture": 1}}, "type": "cube", "uuid": "413d5d04-7aa5-a9f5-1b76-433f3bf64d34"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 37, -1], "to": [-4, 39, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [-2, 40, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24, 11, 23, 12.25], "texture": 1}, "east": {"uv": [25, 11, 23, 12.25], "texture": 1}, "south": {"uv": [25, 11, 24, 11.5], "texture": 1}, "west": {"uv": [25, 11, 23, 11.5], "texture": 1}, "up": {"uv": [23, 11, 24, 10.75], "texture": 1}, "down": {"uv": [24, 10.5, 25, 11], "texture": 1}}, "type": "cube", "uuid": "7909650f-04d6-c175-5c1f-1e6e4f546304"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 37, -1], "to": [1, 40, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [3, 40, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "east": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "south": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "west": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "up": {"uv": [23, 11, 25, 9], "texture": 1}, "down": {"uv": [25, 9, 27, 11], "texture": 1}}, "type": "cube", "uuid": "87ccb374-2c37-5527-0442-cc7bb75fb6eb"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 40, -1], "to": [5, 41, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [1, 43, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 25.5, 12], "texture": 1}, "east": {"uv": [22.25, 11, 23, 12], "texture": 1}, "south": {"uv": [22.5, 11, 25.5, 12], "texture": 1}, "west": {"uv": [24.25, 11, 25, 12], "texture": 1}, "up": {"uv": [25.5, 11, 23, 12], "texture": 1}, "down": {"uv": [25.5, 12, 23, 11], "texture": 1}}, "type": "cube", "uuid": "61b6e333-2dd2-68b7-29ef-64d184876737"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 42, -1], "to": [4, 43, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [1, 43, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 25.5, 12], "texture": 1}, "east": {"uv": [22.25, 11, 23, 12], "texture": 1}, "south": {"uv": [22.5, 11, 25.5, 12], "texture": 1}, "west": {"uv": [24.25, 11, 25, 12], "texture": 1}, "up": {"uv": [25.5, 11, 23, 12], "texture": 1}, "down": {"uv": [25.5, 12, 23, 11], "texture": 1}}, "type": "cube", "uuid": "*************-9234-8e0f-58fbf1a75b11"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 39, -1], "to": [1, 42, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [3, 40, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "east": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "south": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "west": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "up": {"uv": [23, 11, 25, 9], "texture": 1}, "down": {"uv": [25, 9, 27, 11], "texture": 1}}, "type": "cube", "uuid": "8c5e1639-d8ee-b768-d001-8a89fa3aad9b"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 39, -1], "to": [-4, 41, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [-2, 40, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24, 11, 23, 12.25], "texture": 1}, "east": {"uv": [25, 11, 23, 12.25], "texture": 1}, "south": {"uv": [25, 11, 24, 11.5], "texture": 1}, "west": {"uv": [25, 11, 23, 11.5], "texture": 1}, "up": {"uv": [23, 11, 24, 10.75], "texture": 1}, "down": {"uv": [24, 10.5, 25, 11], "texture": 1}}, "type": "cube", "uuid": "91d7b691-35c7-3f7f-183b-d6d848165f03"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 39, -1], "to": [-3, 42, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [-1, 40, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24, 11, 23.75, 12.5], "texture": 1}, "east": {"uv": [26, 11, 24.75, 12.5], "texture": 1}, "south": {"uv": [24.75, 11, 24.75, 11], "texture": 1}, "west": {"uv": [23, 11.25, 22.5, 12.5], "texture": 1}, "up": {"uv": [23, 11, 24, 9], "texture": 1}, "down": {"uv": [24, 9, 25, 11], "texture": 1}}, "type": "cube", "uuid": "359a5af1-05ba-3caa-bd3a-03ad7ec2178c"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [3, 39, -1], "to": [4, 42, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [1, 40, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 23.75, 12.5], "texture": 1}, "east": {"uv": [21, 11, 22.75, 12.5], "texture": 1}, "south": {"uv": [24, 11, 24.75, 12.5], "texture": 1}, "west": {"uv": [24, 11, 25.75, 12.5], "texture": 1}, "up": {"uv": [24, 11, 23, 9], "texture": 1}, "down": {"uv": [25, 9, 24, 11], "texture": 1}}, "type": "cube", "uuid": "b892ad21-7b9b-8cea-4714-ddb536b73073"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 39, -1], "to": [5, 41, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [2, 40, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 23.5, 12.25], "texture": 1}, "east": {"uv": [23, 11, 24.5, 12.25], "texture": 1}, "south": {"uv": [24, 11, 24.5, 12.25], "texture": 1}, "west": {"uv": [24, 11, 25.5, 12.25], "texture": 1}, "up": {"uv": [24, 11, 23, 9], "texture": 1}, "down": {"uv": [25, 9, 24, 11], "texture": 1}}, "type": "cube", "uuid": "b2612fc1-03b6-4ac4-414c-02d200d5d1bd"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 38, -1], "to": [5, 39, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [0, 39, 0], "uv_offset": [19, 9], "faces": {"north": {"uv": [23, 11, 25.5, 12], "texture": 1}, "east": {"uv": [22.25, 11, 23.25, 12], "texture": 1}, "south": {"uv": [22.25, 11, 25.5, 12], "texture": 1}, "west": {"uv": [22.75, 11, 23.25, 12], "texture": 1}, "up": {"uv": [25, 11, 21, 10], "texture": 1}, "down": {"uv": [25, 10, 21, 11], "texture": 1}}, "type": "cube", "uuid": "053c20f0-75a1-2fac-662e-80f55c2de448"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 34, -1], "to": [1, 38, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [0, 36, 0], "uv_offset": [23, 9], "faces": {"north": {"uv": [23, 11, 25, 12.5], "texture": 1}, "east": {"uv": [23, 11, 25, 12.5], "texture": 1}, "south": {"uv": [23, 11, 25, 12.5], "texture": 1}, "west": {"uv": [23, 11, 25, 12.5], "texture": 1}, "up": {"uv": [25, 10.5, 23, 11], "texture": 1}, "down": {"uv": [25, 11, 23, 10.5], "texture": 1}}, "type": "cube", "uuid": "628630d4-2f7e-f656-0e73-7cd99161e7af"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 13, -4], "to": [8, 28, 4], "autouv": 0, "color": 0, "inflate": 0.6, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [17, 19.375, 31, 33.375], "texture": 1}, "east": {"uv": [16, 20, 20, 32], "texture": null}, "south": {"uv": [32, 20, 40, 32], "texture": null}, "west": {"uv": [28, 20, 32, 32], "texture": null}, "up": {"uv": [28, 20, 20, 16], "texture": null}, "down": {"uv": [36, 16, 28, 20], "texture": null}}, "type": "cube", "uuid": "e815ba66-5896-32a9-ba3d-f204d1613385"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 31, -1], "to": [1, 35, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [0, 35, 0], "uv_offset": [23, 9], "faces": {"north": {"uv": [23, 11, 25, 12.5], "texture": 1}, "east": {"uv": [23, 11, 25, 12.5], "texture": 1}, "south": {"uv": [23, 11, 25, 12.5], "texture": 1}, "west": {"uv": [23, 11, 25, 12.5], "texture": 1}, "up": {"uv": [25, 10.5, 23, 11], "texture": 1}, "down": {"uv": [25, 11, 23, 10.5], "texture": 1}}, "type": "cube", "uuid": "2f521d80-628b-bd20-fbd1-efa3ff3d4164"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 35, -1], "to": [6, 36, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [0, 38, 0], "uv_offset": [19, 9], "faces": {"north": {"uv": [23, 11, 25.5, 12], "texture": 1}, "east": {"uv": [22.25, 11, 23.25, 12], "texture": 1}, "south": {"uv": [22.25, 11, 25.5, 12], "texture": 1}, "west": {"uv": [22.75, 11, 23.25, 12], "texture": 1}, "up": {"uv": [25, 11, 21, 10], "texture": 1}, "down": {"uv": [25, 10, 21, 11], "texture": 1}}, "type": "cube", "uuid": "4370ff22-00d6-17f2-2f6c-f0e96145e20e"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 36, -1], "to": [6, 38, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [2, 39, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 23.5, 12.25], "texture": 1}, "east": {"uv": [23, 11, 24.5, 12.25], "texture": 1}, "south": {"uv": [24, 11, 24.5, 12.25], "texture": 1}, "west": {"uv": [24, 11, 25.5, 12.25], "texture": 1}, "up": {"uv": [24, 11, 23, 9], "texture": 1}, "down": {"uv": [25, 9, 24, 11], "texture": 1}}, "type": "cube", "uuid": "4d17c6c0-f8d3-614e-ba81-fa3f8932f279"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [3, 36, -1], "to": [5, 39, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [1, 39, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 23.75, 12.5], "texture": 1}, "east": {"uv": [21, 11, 22.75, 12.5], "texture": 1}, "south": {"uv": [24, 11, 24.75, 12.5], "texture": 1}, "west": {"uv": [24, 11, 25.75, 12.5], "texture": 1}, "up": {"uv": [24, 11, 23, 9], "texture": 1}, "down": {"uv": [25, 9, 24, 11], "texture": 1}}, "type": "cube", "uuid": "1992da10-33e2-c5d8-313a-4621ae2a5b65"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 36, -1], "to": [-3, 39, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [-1, 39, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24, 11, 23.75, 12.5], "texture": 1}, "east": {"uv": [26, 11, 24.75, 12.5], "texture": 1}, "south": {"uv": [24.75, 11, 24.75, 11], "texture": 1}, "west": {"uv": [23, 11.25, 22.5, 12.5], "texture": 1}, "up": {"uv": [23, 11, 24, 9], "texture": 1}, "down": {"uv": [24, 9, 25, 11], "texture": 1}}, "type": "cube", "uuid": "ff121730-478b-8421-d1e6-882e24031166"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 36, -1], "to": [-4, 38, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [-2, 39, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24, 11, 23, 12.25], "texture": 1}, "east": {"uv": [25, 11, 23, 12.25], "texture": 1}, "south": {"uv": [25, 11, 24, 11.5], "texture": 1}, "west": {"uv": [25, 11, 23, 11.5], "texture": 1}, "up": {"uv": [23, 11, 24, 10.75], "texture": 1}, "down": {"uv": [24, 10.5, 25, 11], "texture": 1}}, "type": "cube", "uuid": "be4abef6-f59e-6dd7-9cc4-5dd0bf9da50e"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 36, -1], "to": [1, 39, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [3, 39, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "east": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "south": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "west": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "up": {"uv": [23, 11, 25, 9], "texture": 1}, "down": {"uv": [25, 9, 27, 11], "texture": 1}}, "type": "cube", "uuid": "9cfccb46-3778-aee8-6a8d-6b0deef7d1b8"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 39, -1], "to": [5, 40, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [1, 42, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 25.5, 12], "texture": 1}, "east": {"uv": [22.25, 11, 23, 12], "texture": 1}, "south": {"uv": [22.5, 11, 25.5, 12], "texture": 1}, "west": {"uv": [24.25, 11, 25, 12], "texture": 1}, "up": {"uv": [25.5, 11, 23, 12], "texture": 1}, "down": {"uv": [25.5, 12, 23, 11], "texture": 1}}, "type": "cube", "uuid": "e1e188b0-3cef-631e-d41f-ea63dcffd002"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 28.3, -4], "to": [4, 33.3, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 3, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 1}, "east": {"uv": [0, 8, 8, 16], "texture": 1}, "south": {"uv": [24, 8, 32, 16], "texture": 1}, "west": {"uv": [16, 8, 24, 16], "texture": 1}, "up": {"uv": [16, 8, 8, 0], "texture": 1}, "down": {"uv": [24, 0, 16, 8], "texture": 1}}, "type": "cube", "uuid": "0e6d1597-0d1c-9adc-395c-5a265af159d0"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 28.3, -4], "to": [4, 33.3, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 3, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 1}, "east": {"uv": [32, 8, 40, 16], "texture": 1}, "south": {"uv": [56, 8, 64, 16], "texture": 1}, "west": {"uv": [48, 8, 56, 16], "texture": 1}, "up": {"uv": [48, 8, 40, 0], "texture": 1}, "down": {"uv": [56, 0, 48, 8], "texture": 1}}, "type": "cube", "uuid": "fac153ed-cdc2-10b7-8447-3b7cceb31e8b"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 13, -4], "to": [8, 28, 4], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 31.5, 28, 32], "texture": 1}, "east": {"uv": [16, 20, 20, 32], "texture": 1}, "south": {"uv": [32, 20, 40, 32], "texture": 1}, "west": {"uv": [28, 20, 32, 32], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "3c184c15-200d-895d-805c-356d1caa2cc1"}, {"name": "Right Shoulder Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 25, -2], "to": [13.5, 27.5, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [5, 2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 47, 38], "texture": 1}, "east": {"uv": [40, 36, 44, 38], "texture": 1}, "south": {"uv": [52, 36, 55, 38], "texture": 1}, "west": {"uv": [48, 36, 52, 38], "texture": 1}, "up": {"uv": [47, 36, 44, 32], "texture": 1}, "down": {"uv": [51, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "e92d7739-1ec5-750a-3050-7239987b691a"}, {"name": "Right Shoulder", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 25, -2], "to": [13.5, 27.5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [5, 2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 47, 22], "texture": 1}, "east": {"uv": [40, 20, 44, 22], "texture": 1}, "south": {"uv": [52, 20, 55, 22], "texture": 1}, "west": {"uv": [48, 20, 52, 22], "texture": 1}, "up": {"uv": [47, 20, 44, 16], "texture": 1}, "down": {"uv": [51, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "1b489489-5d14-eb9e-3436-a83e677d7ac8"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 19.7, -2], "to": [12, 23.5, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [5, 2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 38, 47, 41.8], "texture": 1}, "east": {"uv": [40, 38, 44, 41.8], "texture": 1}, "south": {"uv": [52, 38, 55, 41.8], "texture": 1}, "west": {"uv": [48, 38, 52, 41.8], "texture": 1}, "up": {"uv": [47, 36, 44, 32], "texture": 1}, "down": {"uv": [51, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "aa87080c-3191-4e3d-3593-1a974be73f55"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 19.7, -2], "to": [12, 23.5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [5, 2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 22, 47, 25.8], "texture": 1}, "east": {"uv": [40, 22, 44, 25.8], "texture": 1}, "south": {"uv": [52, 22, 55, 25.8], "texture": 1}, "west": {"uv": [48, 22, 52, 25.8], "texture": 1}, "up": {"uv": [47, 20, 44, 16], "texture": 1}, "down": {"uv": [51, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "8906f1f2-2fd4-2263-cf33-82ef25979637"}, {"name": "Right hand Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 10, -3], "to": [14, 19, 3], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [5, -1, -1], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 43.125, 48, 48], "texture": 1}, "east": {"uv": [40, 43.125, 44, 48], "texture": 1}, "south": {"uv": [52, 43.125, 55, 48], "texture": 1}, "west": {"uv": [48, 43.125, 52, 48], "texture": 1}, "up": {"uv": [47, 36, 44, 32], "texture": 1}, "down": {"uv": [51, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "5db6b359-f278-8ae6-34b6-7d0abc839ab8"}, {"name": "Right Hand", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 10, -3], "to": [14, 19, 3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [5, -1, -1], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 27.5, 48, 32], "texture": 1}, "east": {"uv": [40, 27.5, 44, 32], "texture": 1}, "south": {"uv": [52, 27.5, 55, 32], "texture": 1}, "west": {"uv": [48, 27.5, 52, 32], "texture": 1}, "up": {"uv": [47, 20, 44, 16], "texture": 1}, "down": {"uv": [51, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "92a3b62a-492b-16db-e364-5f79a69fbb19"}, {"name": "Left Shoulder Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-14, 24, -2], "to": [-9.5, 27.5, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-6, 14, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 55.5, 54.5], "texture": 1}, "east": {"uv": [48, 52, 52, 54.5], "texture": 1}, "south": {"uv": [60, 52, 63.5, 54.5], "texture": 1}, "west": {"uv": [56, 52, 60, 54.5], "texture": 1}, "up": {"uv": [55.5, 52, 52, 48], "texture": 1}, "down": {"uv": [59.5, 48, 56, 52], "texture": 1}}, "type": "cube", "uuid": "bbd1c71a-fb88-f727-94d6-223a9d9f2a86"}, {"name": "Left Shoulder", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-14, 24, -2], "to": [-9.5, 27.5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-6, 14, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 39.5, 54.5], "texture": 1}, "east": {"uv": [32, 52, 36, 54.5], "texture": 1}, "south": {"uv": [44, 52, 47.5, 54.5], "texture": 1}, "west": {"uv": [40, 52, 44, 54.5], "texture": 1}, "up": {"uv": [39.5, 52, 36, 48], "texture": 1}, "down": {"uv": [43.5, 48, 40, 52], "texture": 1}}, "type": "cube", "uuid": "dc9b23bb-346a-61a1-6d96-12509d64c816"}, {"name": "Left Hand Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-14, 10, -3], "to": [-9, 19, 3], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-5, 0, -1], "uv_offset": [48, 48], "faces": {"north": {"uv": [48, 43.125, 44, 48], "texture": 1}, "east": {"uv": [44, 43.125, 40, 48], "texture": 1}, "south": {"uv": [55, 43.125, 52, 48], "texture": 1}, "west": {"uv": [52, 43.125, 48, 48], "texture": 1}, "up": {"uv": [55, 51.125, 52, 47.125], "texture": 1}, "down": {"uv": [59, 47.125, 56, 51.125], "texture": 1}}, "type": "cube", "uuid": "5c45de32-ddd0-1ecb-a12f-0cd79df24d03"}, {"name": "Left Hand", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-14, 10, -3], "to": [-9, 19, 3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-5, 0, -1], "uv_offset": [32, 48], "faces": {"north": {"uv": [48, 27.5, 44, 32], "texture": 1}, "east": {"uv": [44, 27.5, 40, 32], "texture": 1}, "south": {"uv": [55, 27.5, 52, 32], "texture": 1}, "west": {"uv": [52, 27.5, 48, 32], "texture": 1}, "up": {"uv": [39, 51.125, 36, 47.125], "texture": 1}, "down": {"uv": [43, 47.125, 40, 51.125], "texture": 1}}, "type": "cube", "uuid": "c123720f-16a8-95aa-22e8-0b0ef1d63e88"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-12, 19, -2], "to": [-9, 22.58, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-4, 6, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [47, 22, 44, 25.8], "texture": 1}, "east": {"uv": [44, 22, 40, 25.8], "texture": 1}, "south": {"uv": [55, 22, 52, 25.8], "texture": 1}, "west": {"uv": [52, 22, 48, 25.8], "texture": 1}, "up": {"uv": [54, 52.625, 52, 48.625], "texture": 1}, "down": {"uv": [58, 48.625, 56, 52.625], "texture": 1}}, "type": "cube", "uuid": "e96eb94f-2a35-54e6-6feb-67e1ec77ff77"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-12, 19, -2], "to": [-9, 22.58, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-4, 6, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [47, 22, 44, 25.8], "texture": 1}, "east": {"uv": [44, 22, 40, 25.8], "texture": 1}, "south": {"uv": [55, 22, 52, 25.8], "texture": 1}, "west": {"uv": [52, 22, 48, 25.8], "texture": 1}, "up": {"uv": [38, 52.625, 36, 48.625], "texture": 1}, "down": {"uv": [42, 48.625, 40, 52.625], "texture": 1}}, "type": "cube", "uuid": "52ec88d4-d013-8256-2795-a03c4eb400d1"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 31, -1], "to": [1, 35, 1], "autouv": 0, "color": 0, "origin": [0, 35, 0], "uv_offset": [23, 9], "faces": {"north": {"uv": [23, 11, 25, 12.5], "texture": 1}, "east": {"uv": [23, 11, 25, 12.5], "texture": 1}, "south": {"uv": [23, 11, 25, 12.5], "texture": 1}, "west": {"uv": [23, 11, 25, 12.5], "texture": 1}, "up": {"uv": [25, 10.5, 23, 11], "texture": 1}, "down": {"uv": [25, 11, 23, 10.5], "texture": 1}}, "type": "cube", "uuid": "5a52a630-b47f-d406-94de-71470b9531e8"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 35, -1], "to": [7, 36, 1], "autouv": 0, "color": 0, "origin": [0, 38, 0], "uv_offset": [19, 9], "faces": {"north": {"uv": [23, 11, 25.5, 12], "texture": 1}, "east": {"uv": [22.25, 11, 23.25, 12], "texture": 1}, "south": {"uv": [22.25, 11, 25.5, 12], "texture": 1}, "west": {"uv": [22.75, 11, 23.25, 12], "texture": 1}, "up": {"uv": [25, 11, 21, 10], "texture": 1}, "down": {"uv": [25, 10, 21, 11], "texture": 1}}, "type": "cube", "uuid": "324d078e-e225-c9fc-7f6f-b658e3dec2e8"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 36, -1], "to": [7, 38, 1], "autouv": 0, "color": 0, "origin": [4, 39, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 23.5, 12.25], "texture": 1}, "east": {"uv": [23, 11, 24.5, 12.25], "texture": 1}, "south": {"uv": [24, 11, 24.5, 12.25], "texture": 1}, "west": {"uv": [24, 11, 25.5, 12.25], "texture": 1}, "up": {"uv": [24, 11, 23, 9], "texture": 1}, "down": {"uv": [25, 9, 24, 11], "texture": 1}}, "type": "cube", "uuid": "529a12a9-c11e-7ffa-b4ca-a585e1812589"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 36, -1], "to": [5, 39, 1], "autouv": 0, "color": 0, "origin": [2, 39, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 23.75, 12.5], "texture": 1}, "east": {"uv": [21, 11, 22.75, 12.5], "texture": 1}, "south": {"uv": [24, 11, 24.75, 12.5], "texture": 1}, "west": {"uv": [24, 11, 25.75, 12.5], "texture": 1}, "up": {"uv": [24, 11, 23, 9], "texture": 1}, "down": {"uv": [25, 9, 24, 11], "texture": 1}}, "type": "cube", "uuid": "660d2a59-ac07-1f3d-8db7-e1a0b01b4607"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 36, -1], "to": [-4, 39, 1], "autouv": 0, "color": 0, "mirror_uv": true, "origin": [-2, 39, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24, 11, 23.75, 12.5], "texture": 1}, "east": {"uv": [26, 11, 24.75, 12.5], "texture": 1}, "south": {"uv": [24.75, 11, 24.75, 11], "texture": 1}, "west": {"uv": [23, 11.25, 22.5, 12.5], "texture": 1}, "up": {"uv": [23, 11, 24, 9], "texture": 1}, "down": {"uv": [24, 9, 25, 11], "texture": 1}}, "type": "cube", "uuid": "f907c8fe-fad6-96c6-c1d4-8e470b9d63a1"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 36, -1], "to": [-5, 38, 1], "autouv": 0, "color": 0, "mirror_uv": true, "origin": [-4, 39, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24, 11, 23, 12.25], "texture": 1}, "east": {"uv": [25, 11, 23, 12.25], "texture": 1}, "south": {"uv": [25, 11, 24, 11.5], "texture": 1}, "west": {"uv": [25, 11, 23, 11.5], "texture": 1}, "up": {"uv": [23, 11, 24, 10.75], "texture": 1}, "down": {"uv": [24, 10.5, 25, 11], "texture": 1}}, "type": "cube", "uuid": "c630ba18-c632-764f-66a2-b07817fad542"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 36, -1], "to": [1.5, 39, 1], "autouv": 0, "color": 0, "mirror_uv": true, "origin": [2.5, 39, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "east": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "south": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "west": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "up": {"uv": [23, 11, 25, 9], "texture": 1}, "down": {"uv": [25, 9, 27, 11], "texture": 1}}, "type": "cube", "uuid": "7191e36c-9b6b-8e12-ba62-a12099170fee"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.999999904632569, 10, -2], "to": [-1.9999999046325683, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-2.0999999046325684, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 56], "texture": 1}, "east": {"uv": [16, 52, 20, 56], "texture": 1}, "south": {"uv": [28, 52, 32, 56], "texture": 1}, "west": {"uv": [24, 52, 28, 56], "texture": 1}, "up": {"uv": [24, 52, 20, 48], "texture": 1}, "down": {"uv": [28, 48, 24, 52], "texture": 1}}, "type": "cube", "uuid": "c4622225-73f4-e2e2-fbc1-159682c65c65"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.999999904632569, 10, -2], "to": [-1.9999999046325683, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-2.0999999046325684, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 56], "texture": 1}, "east": {"uv": [0, 52, 4, 56], "texture": 1}, "south": {"uv": [12, 52, 16, 56], "texture": 1}, "west": {"uv": [8, 52, 12, 56], "texture": 1}, "up": {"uv": [8, 52, 4, 48], "texture": 1}, "down": {"uv": [12, 48, 8, 52], "texture": 1}}, "type": "cube", "uuid": "09b0c1a1-48e4-f1a6-507f-a97bb97ebc5d"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 0, -2], "to": [-1.4999999999999996, 9, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-2.0999999046325684, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 57, 25, 64], "texture": 1}, "east": {"uv": [16, 57, 20, 64], "texture": 1}, "south": {"uv": [28, 57, 33, 64], "texture": 1}, "west": {"uv": [24, 57, 28, 64], "texture": 1}, "up": {"uv": [25, 57, 20, 53], "texture": 1}, "down": {"uv": [29, 53, 24, 57], "texture": 1}}, "type": "cube", "uuid": "4dcdb851-f947-adb9-ea04-12c6f212e1e0"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 0, -2], "to": [-1.4999999999999996, 9, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-2.0999999046325684, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 57, 9, 64], "texture": 1}, "east": {"uv": [0, 57, 4, 64], "texture": 1}, "south": {"uv": [12, 57, 17, 64], "texture": 1}, "west": {"uv": [8, 57, 12, 64], "texture": 1}, "up": {"uv": [9, 57, 4, 53], "texture": 1}, "down": {"uv": [13, 53, 8, 57], "texture": 1}}, "type": "cube", "uuid": "9ccd9397-817e-79a5-90e8-4d34b3639ce9"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.9999999046325683, 10, -2], "to": [5.999999904632569, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [2.0999999046325684, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 40], "texture": 1}, "east": {"uv": [0, 36, 4, 40], "texture": 1}, "south": {"uv": [12, 36, 16, 40], "texture": 1}, "west": {"uv": [8, 36, 12, 40], "texture": 1}, "up": {"uv": [8, 36, 4, 32], "texture": 1}, "down": {"uv": [12, 32, 8, 36], "texture": 1}}, "type": "cube", "uuid": "aab36e1c-a15a-8905-faee-e086e7a37624"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.9999999046325683, 10, -2], "to": [5.999999904632569, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2.0999999046325684, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 24], "texture": 1}, "east": {"uv": [0, 20, 4, 24], "texture": 1}, "south": {"uv": [12, 20, 16, 24], "texture": 1}, "west": {"uv": [8, 20, 12, 24], "texture": 1}, "up": {"uv": [8, 20, 4, 16], "texture": 1}, "down": {"uv": [12, 16, 8, 20], "texture": 1}}, "type": "cube", "uuid": "6b1b842a-0880-3443-2dd0-b81a00346c1e"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.5, 0, -2], "to": [6.5, 9, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [2.0999999046325684, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 41, 9, 48], "texture": 1}, "east": {"uv": [0, 41, 4, 48], "texture": 1}, "south": {"uv": [12, 41, 16.125, 48], "texture": 1}, "west": {"uv": [8, 41, 12, 48], "texture": 1}, "up": {"uv": [9, 41, 4, 37], "texture": 1}, "down": {"uv": [13, 37, 8, 41], "texture": 1}}, "type": "cube", "uuid": "ad6697bf-ef58-3e51-4b77-ab1ec0b54165"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.5, 0, -2], "to": [6.5, 9, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2.0999999046325684, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 25, 9, 32], "texture": 1}, "east": {"uv": [0, 25, 4, 32], "texture": 1}, "south": {"uv": [12, 25, 17, 32], "texture": 1}, "west": {"uv": [8, 25, 12, 32], "texture": 1}, "up": {"uv": [9, 25, 4, 21], "texture": 1}, "down": {"uv": [13, 21, 8, 25], "texture": 1}}, "type": "cube", "uuid": "6cc379e5-d2fa-86dc-5ec8-53607d61ddd4"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 39, -1], "to": [-1, 40, 1], "autouv": 0, "color": 0, "origin": [1, 42, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 25.5, 12], "texture": 1}, "east": {"uv": [22.25, 11, 23, 12], "texture": 1}, "south": {"uv": [22.5, 11, 25.5, 12], "texture": 1}, "west": {"uv": [24.25, 11, 25, 12], "texture": 1}, "up": {"uv": [25.5, 11, 23, 12], "texture": 1}, "down": {"uv": [25.5, 12, 23, 11], "texture": 1}}, "type": "cube", "uuid": "1441ad74-de97-8e1b-673f-5b9440cb402d"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1, 39, -1], "to": [5, 40, 1], "autouv": 0, "color": 0, "origin": [1, 42, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [23, 11, 25.5, 12], "texture": 1}, "east": {"uv": [22.25, 11, 23, 12], "texture": 1}, "south": {"uv": [22.5, 11, 25.5, 12], "texture": 1}, "west": {"uv": [24.25, 11, 25, 12], "texture": 1}, "up": {"uv": [25.5, 11, 23, 12], "texture": 1}, "down": {"uv": [25.5, 12, 23, 11], "texture": 1}}, "type": "cube", "uuid": "70a41793-b5d1-6cbc-8bf7-9127fb657898"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 36, -1], "to": [1.5, 39, 1], "autouv": 0, "color": 0, "mirror_uv": true, "origin": [2.5, 39, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "east": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "south": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "west": {"uv": [24.5, 11, 23, 12.25], "texture": 1}, "up": {"uv": [23, 11, 25, 9], "texture": 1}, "down": {"uv": [25, 9, 27, 11], "texture": 1}}, "type": "cube", "uuid": "9573e1ec-93a8-520f-1fd5-9cbfdd5ec48d"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-26, 4, 5.5], "to": [4, 37, 5.5], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [2, 15, 5], "uv_offset": [56, 37], "faces": {"north": {"uv": [56, 57, 40, 64], "texture": null}, "east": {"uv": [38, 59, 60, 64], "texture": null}, "south": {"uv": [14, 5, 64, 54], "texture": 1}, "west": {"uv": [36, 57, 50, 60], "texture": null}, "up": {"uv": [14, 5, 64, 53], "texture": null}, "down": {"uv": [14, 5, 64, 53], "texture": null}}, "type": "cube", "uuid": "9c841cb3-7298-5e2a-786a-215ed65465c2"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 4, 5.5], "to": [26, 37, 5.5], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [-2, 15, 5], "uv_offset": [56, 37], "faces": {"north": {"uv": [36, 59, 58, 64], "texture": null}, "east": {"uv": [58, 59, 36, 64], "texture": null}, "south": {"uv": [64, 5, 14, 54], "texture": 1}, "west": {"uv": [58, 59, 36, 64], "texture": null}, "up": {"uv": [64, 5, 14, 53], "texture": null}, "down": {"uv": [64, 5, 14, 53], "texture": null}}, "type": "cube", "uuid": "f9770956-099a-2365-4e66-364b0d5e381e"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 11.5, 5.5], "to": [5, 28, 5.5], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [-4, 15, 5], "uv_offset": [56, 37], "faces": {"north": {"uv": [36, 59, 58, 64], "texture": null}, "east": {"uv": [58, 59, 36, 64], "texture": null}, "south": {"uv": [64, 23, 56, 42], "texture": 1}, "west": {"uv": [58, 59, 36, 64], "texture": null}, "up": {"uv": [64, 5, 14, 53], "texture": null}, "down": {"uv": [64, 5, 14, 53], "texture": null}}, "type": "cube", "uuid": "08a220b1-fbc3-ff2a-f4d0-c878ba67d769"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 11.5, 5.5], "to": [5, 28, 5.5], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [4, 15, 5], "uv_offset": [56, 37], "faces": {"north": {"uv": [58, 59, 36, 64], "texture": null}, "east": {"uv": [36, 59, 58, 64], "texture": null}, "south": {"uv": [56, 23, 64, 42], "texture": 1}, "west": {"uv": [36, 59, 58, 64], "texture": null}, "up": {"uv": [14, 5, 64, 53], "texture": null}, "down": {"uv": [14, 5, 64, 53], "texture": null}}, "type": "cube", "uuid": "8dd0b33a-2355-377d-6c30-edfead3b6261"}, {"name": "Right Shoulder", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 25, -2], "to": [13.5, 27.5, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [5, 2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 47, 22], "texture": 1}, "east": {"uv": [40, 20, 44, 22], "texture": 1}, "south": {"uv": [52, 20, 55, 22], "texture": 1}, "west": {"uv": [48, 20, 52, 22], "texture": 1}, "up": {"uv": [47, 20, 44, 16], "texture": 1}, "down": {"uv": [51, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "0d58ad5a-0d4b-9619-927c-154d91334077"}, {"name": "Right Shoulder Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 25, -2], "to": [13.5, 27.5, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [5, 2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 47, 38], "texture": 1}, "east": {"uv": [40, 36, 44, 39], "texture": 1}, "south": {"uv": [52, 36, 55, 38], "texture": 1}, "west": {"uv": [48, 36, 52, 38], "texture": 1}, "up": {"uv": [47, 36, 44, 32], "texture": 1}, "down": {"uv": [51, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "1bce5c14-f28a-d443-4609-6c457794176c"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 19.7, -2], "to": [12, 23.5, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [5, 2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 47, 25], "texture": 1}, "east": {"uv": [40, 20, 44, 25], "texture": 1}, "south": {"uv": [52, 20, 55, 25], "texture": 1}, "west": {"uv": [48, 20, 52, 25], "texture": 1}, "up": {"uv": [47, 20, 44, 16], "texture": 1}, "down": {"uv": [51, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "4313ff20-7d55-dc89-5a69-f4b382fc8a1a"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 19.7, -2], "to": [12, 23.5, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [5, 2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 47, 41], "texture": 1}, "east": {"uv": [56, 36, 52, 41], "texture": 1}, "south": {"uv": [52, 36, 55, 41], "texture": 1}, "west": {"uv": [48, 36, 52, 41], "texture": 1}, "up": {"uv": [47, 36, 44, 32], "texture": 1}, "down": {"uv": [51, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "7c198309-680d-921c-8fe6-6612773dd324"}, {"name": "Right Hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 10, -3], "to": [14, 19, 3], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [5, -1, -1], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 47, 23], "texture": 1}, "east": {"uv": [40, 20, 44, 23], "texture": 1}, "south": {"uv": [52, 20, 55, 23], "texture": 1}, "west": {"uv": [48, 20, 52, 23], "texture": 1}, "up": {"uv": [47, 20, 44, 16], "texture": 1}, "down": {"uv": [51, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "cee48b40-31d8-9287-7af7-3496191b8fd7"}, {"name": "Right hand Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 10, -3], "to": [14, 19, 3], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [5, -1, -1], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 47, 39], "texture": 1}, "east": {"uv": [52, 36, 56, 39], "texture": 1}, "south": {"uv": [52, 36, 55, 39], "texture": 1}, "west": {"uv": [48, 36, 52, 39], "texture": 1}, "up": {"uv": [47, 36, 44, 32], "texture": 1}, "down": {"uv": [51, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "1dc80cfb-acd1-69c7-63f6-dd5a48948230"}, {"name": "Left Shoulder", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-13.5, 25, -2], "to": [-9, 27.5, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [-5, 2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [47, 20, 44, 22], "texture": 1}, "east": {"uv": [52, 20, 48, 22], "texture": 1}, "south": {"uv": [55, 20, 52, 22], "texture": 1}, "west": {"uv": [44, 20, 40, 22], "texture": 1}, "up": {"uv": [44, 20, 47, 16], "texture": 1}, "down": {"uv": [48, 16, 51, 20], "texture": 1}}, "type": "cube", "uuid": "8fa6e27a-e6ca-6440-fce2-b90c7a3ab3dc"}, {"name": "Left Shoulder Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-13.5, 25, -2], "to": [-9, 27.5, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-5, 2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [47, 36, 44, 38], "texture": 1}, "east": {"uv": [52, 36, 48, 38], "texture": 1}, "south": {"uv": [55, 36, 52, 38], "texture": 1}, "west": {"uv": [44, 36, 40, 39], "texture": 1}, "up": {"uv": [44, 36, 47, 32], "texture": 1}, "down": {"uv": [48, 32, 51, 36], "texture": 1}}, "type": "cube", "uuid": "71f86bde-0b50-61e7-e48e-dbe73d0f3412"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-12, 19.7, -2], "to": [-9, 23.5, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [-5, 2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [47, 20, 44, 25], "texture": 1}, "east": {"uv": [52, 20, 48, 25], "texture": 1}, "south": {"uv": [55, 20, 52, 25], "texture": 1}, "west": {"uv": [44, 20, 40, 25], "texture": 1}, "up": {"uv": [44, 20, 47, 16], "texture": 1}, "down": {"uv": [48, 16, 51, 20], "texture": 1}}, "type": "cube", "uuid": "a8abc46a-7f8a-f25b-d4c0-46940329e49a"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-12, 19.7, -2], "to": [-9, 23.5, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-5, 2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [47, 36, 44, 41], "texture": 1}, "east": {"uv": [52, 36, 48, 41], "texture": 1}, "south": {"uv": [55, 36, 52, 41], "texture": 1}, "west": {"uv": [52, 36, 56, 41], "texture": 1}, "up": {"uv": [44, 36, 47, 32], "texture": 1}, "down": {"uv": [48, 32, 51, 36], "texture": 1}}, "type": "cube", "uuid": "c2bae35a-64a6-4a76-4d29-8bbff5440e5f"}, {"name": "Left Hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-14, 10, -3], "to": [-9, 19, 3], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [-5, -1, -1], "uv_offset": [40, 16], "faces": {"north": {"uv": [47, 20, 44, 23], "texture": 1}, "east": {"uv": [52, 20, 48, 23], "texture": 1}, "south": {"uv": [55, 20, 52, 23], "texture": 1}, "west": {"uv": [44, 20, 40, 23], "texture": 1}, "up": {"uv": [44, 20, 47, 16], "texture": 1}, "down": {"uv": [48, 16, 51, 20], "texture": 1}}, "type": "cube", "uuid": "e712e9dd-fe6b-07f2-e4f7-0e3c86531b29"}, {"name": "Left hand Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-14, 10, -3], "to": [-9, 19, 3], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-5, -1, -1], "uv_offset": [40, 32], "faces": {"north": {"uv": [47, 36, 44, 39], "texture": 1}, "east": {"uv": [52, 36, 48, 39], "texture": 1}, "south": {"uv": [55, 36, 52, 39], "texture": 1}, "west": {"uv": [56, 36, 52, 39], "texture": 1}, "up": {"uv": [44, 36, 47, 32], "texture": 1}, "down": {"uv": [48, 32, 51, 36], "texture": 1}}, "type": "cube", "uuid": "bdd22596-0f78-bf0a-1431-6ef2221a2ae8"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 13, -4], "to": [8, 28, 4], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 1}, "east": {"uv": [16, 36, 20, 48], "texture": 1}, "south": {"uv": [32, 36, 40, 48], "texture": 1}, "west": {"uv": [28, 36, 32, 48], "texture": 1}, "up": {"uv": [28, 36, 20, 32], "texture": 1}, "down": {"uv": [36, 32, 28, 36], "texture": 1}}, "type": "cube", "uuid": "70e0649a-f02f-8cab-8962-8cd7089c2f6e"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 13, -4], "to": [8, 28, 4], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.6, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [17, 19.375, 31, 33.375], "texture": 1}, "east": {"uv": [16, 20, 20, 32], "texture": null}, "south": {"uv": [32, 20, 40, 32], "texture": null}, "west": {"uv": [28, 20, 32, 32], "texture": null}, "up": {"uv": [28, 20, 20, 16], "texture": null}, "down": {"uv": [36, 16, 28, 20], "texture": null}}, "type": "cube", "uuid": "aa5c28e4-1143-f953-0d9e-472ee3e42a6a"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 13, -4], "to": [8, 28, 4], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 31.5, 28, 32], "texture": 1}, "east": {"uv": [16, 20, 20, 32], "texture": 1}, "south": {"uv": [32, 20, 40, 32], "texture": 1}, "west": {"uv": [28, 20, 32, 32], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "72fea554-6b7c-21e7-c3ef-6935595fb745"}, {"name": "Shoulder pad", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [10.5, 34, -2], "to": [10.5, 40, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "rotation": [0, 0, 22.5], "origin": [10, 33, 0], "uv_offset": [28, 57], "faces": {"north": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "east": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "south": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "west": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "up": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "down": {"uv": [37, 53.75, 32.75, 56], "texture": 1}}, "type": "cube", "uuid": "c30b08a4-3692-db2b-5b13-105a5e9c29f0"}, {"name": "Shoulder pad", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [10.5, 31, -2], "to": [11.5, 33, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "rotation": [0, 0, 15], "origin": [10, 33, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "east": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "south": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "west": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "up": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "down": {"uv": [37, 53.75, 32.75, 56], "texture": 1}}, "type": "cube", "uuid": "c769aff3-b32a-d047-020a-dba63877d367"}, {"name": "Shoulder pad", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [11.5, 28.912327711690228, -2], "to": [13, 30.21232771169023, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [11, 32.912327711690224, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "east": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "south": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "west": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "up": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "down": {"uv": [37, 53.75, 32.75, 56], "texture": 1}}, "type": "cube", "uuid": "6be368fc-7841-36fd-fb42-d88779339cc7"}, {"name": "Shoulder pad", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-11.5, 31, -2], "to": [-10.5, 33, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "rotation": [0, 0, -15], "origin": [-10, 33, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "east": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "south": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "west": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "up": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "down": {"uv": [37, 53.75, 32.75, 56], "texture": 1}}, "type": "cube", "uuid": "b8051d6e-0698-f448-0f04-fd1052f85c1a"}, {"name": "Shoulder pad", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-13, 28.912327711690228, -2], "to": [-11.5, 30.21232771169023, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-11, 32.912327711690224, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "east": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "south": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "west": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "up": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "down": {"uv": [37, 53.75, 32.75, 56], "texture": 1}}, "type": "cube", "uuid": "26c24c1b-0cfd-d602-d5ab-b55b6dc1bd0c"}, {"name": "Shoulder pad", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-10.5, 34, -2], "to": [-10.5, 40, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "rotation": [0, 0, -22.5], "origin": [-10, 33, 0], "uv_offset": [28, 57], "faces": {"north": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "east": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "south": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "west": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "up": {"uv": [37, 53.75, 32.75, 56], "texture": 1}, "down": {"uv": [37, 53.75, 32.75, 56], "texture": 1}}, "type": "cube", "uuid": "f54a5463-04fb-e454-b196-1d2f2a3d73bc"}], "outliner": [{"name": "armorHead", "origin": [0, 24, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", "fac153ed-cdc2-10b7-8447-3b7cceb31e8b", "0e6d1597-0d1c-9adc-395c-5a265af159d0", {"name": "oldantenna", "origin": [0, 0, 0], "color": 0, "uuid": "53c52b16-787f-abd6-4e7f-06b58d5b353b", "export": true, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["*************-9234-8e0f-58fbf1a75b11", "8c5e1639-d8ee-b768-d001-8a89fa3aad9b", "91d7b691-35c7-3f7f-183b-d6d848165f03", "359a5af1-05ba-3caa-bd3a-03ad7ec2178c", "b892ad21-7b9b-8cea-4714-ddb536b73073", "b2612fc1-03b6-4ac4-414c-02d200d5d1bd", "053c20f0-75a1-2fac-662e-80f55c2de448", "628630d4-2f7e-f656-0e73-7cd99161e7af"]}, {"name": "antenna", "origin": [0, 0, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["6f8bcb3c-9c11-ae4a-8016-5e09deee803a", "be37b7a1-5f62-fc41-11c9-2aa9c7e7e2e7", "23ba3d26-563b-8f71-cd92-c68b3475508c", "80993334-55ba-35f5-bbfb-d564c731fd01", "413d5d04-7aa5-a9f5-1b76-433f3bf64d34", "7909650f-04d6-c175-5c1f-1e6e4f546304", "87ccb374-2c37-5527-0442-cc7bb75fb6eb", "61b6e333-2dd2-68b7-29ef-64d184876737"]}, {"name": "antenna_af", "origin": [0, -1, 0], "color": 0, "uuid": "aa984cd2-3c39-28a7-f066-82f121ff8745", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["2f521d80-628b-bd20-fbd1-efa3ff3d4164", "4370ff22-00d6-17f2-2f6c-f0e96145e20e", "4d17c6c0-f8d3-614e-ba81-fa3f8932f279", "1992da10-33e2-c5d8-313a-4621ae2a5b65", "ff121730-478b-8421-d1e6-882e24031166", "be4abef6-f59e-6dd7-9cc4-5dd0bf9da50e", "9cfccb46-3778-aee8-6a8d-6b0deef7d1b8", "e1e188b0-3cef-631e-d41f-ea63dcffd002"]}, {"name": "antenna_ov", "origin": [0, 0, 0], "color": 0, "uuid": "bf13c477-7a17-f6eb-6e7c-6c56c63eac73", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["5a52a630-b47f-d406-94de-71470b9531e8", "324d078e-e225-c9fc-7f6f-b658e3dec2e8", "529a12a9-c11e-7ffa-b4ca-a585e1812589", "660d2a59-ac07-1f3d-8db7-e1a0b01b4607", "f907c8fe-fad6-96c6-c1d4-8e470b9d63a1", "c630ba18-c632-764f-66a2-b07817fad542", "7191e36c-9b6b-8e12-ba62-a12099170fee", "9573e1ec-93a8-520f-1fd5-9cbfdd5ec48d", "1441ad74-de97-8e1b-673f-5b9440cb402d", "70a41793-b5d1-6cbc-8bf7-9127fb657898"]}]}, {"name": "armorBody", "origin": [0, 24, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "3c184c15-200d-895d-805c-356d1caa2cc1", "e815ba66-5896-32a9-ba3d-f204d1613385", "5a4ba021-cf61-d8a1-ec37-9958cffad565", {"name": "prypiatosian_a3", "origin": [0, 1, 0], "color": 0, "uuid": "e656e642-b324-aa69-dd5f-3d85f3df6d7e", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["72fea554-6b7c-21e7-c3ef-6935595fb745", "aa5c28e4-1143-f953-0d9e-472ee3e42a6a", "70e0649a-f02f-8cab-8962-8cd7089c2f6e"]}, {"name": "wings_part", "origin": [4, 15, 4], "color": 0, "uuid": "46480162-457c-de2b-a839-8107100ddd26", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["08a220b1-fbc3-ff2a-f4d0-c878ba67d769", "8dd0b33a-2355-377d-6c30-edfead3b6261"]}, {"name": "wingRight", "origin": [5, 22, 0], "color": 0, "uuid": "91c6f18e-bc6b-6f52-549e-87212bd18edb", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["f9770956-099a-2365-4e66-364b0d5e381e"]}, {"name": "wingLeft", "origin": [-5, 22, 0], "color": 0, "uuid": "5fa0375e-145b-6b57-4847-31db152fff3f", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["9c841cb3-7298-5e2a-786a-215ed65465c2"]}]}, {"name": "armorRightArm", "origin": [9, 23, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "biosovortian", "origin": [10, 33, 0], "color": 0, "uuid": "7e16325b-6e34-0289-7621-61afac881404", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["6be368fc-7841-36fd-fb42-d88779339cc7", "c769aff3-b32a-d047-020a-dba63877d367", "c30b08a4-3692-db2b-5b13-105a5e9c29f0"]}, "59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", {"name": "prypiatosian_a2", "origin": [4, 0, 0], "color": 0, "uuid": "da7fb93f-50e9-9c31-1e87-ab1fc9b8911c", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["1dc80cfb-acd1-69c7-63f6-dd5a48948230", "cee48b40-31d8-9287-7af7-3496191b8fd7", "7c198309-680d-921c-8fe6-6612773dd324", "4313ff20-7d55-dc89-5a69-f4b382fc8a1a", "1bce5c14-f28a-d443-4609-6c457794176c", "0d58ad5a-0d4b-9619-927c-154d91334077"]}, {"name": "clockwork", "origin": [4, 0, 0], "color": 0, "uuid": "2a7e7741-e465-ea66-3127-07c18e9fb155", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["1b489489-5d14-eb9e-3436-a83e677d7ac8", "e92d7739-1ec5-750a-3050-7239987b691a", "8906f1f2-2fd4-2263-cf33-82ef25979637", "aa87080c-3191-4e3d-3593-1a974be73f55", "92a3b62a-492b-16db-e364-5f79a69fbb19", "5db6b359-f278-8ae6-34b6-7d0abc839ab8"]}]}, {"name": "armorLeftArm", "origin": [-9, 23, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "armNested", "origin": [-9, 23, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134", {"name": "clockwork2", "origin": [-5, 12, 0], "color": 0, "uuid": "0f34bc49-4804-f3fb-3919-c0c8340a750c", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["c123720f-16a8-95aa-22e8-0b0ef1d63e88", "5c45de32-ddd0-1ecb-a12f-0cd79df24d03", "52ec88d4-d013-8256-2795-a03c4eb400d1", "e96eb94f-2a35-54e6-6feb-67e1ec77ff77", "dc9b23bb-346a-61a1-6d96-12509d64c816", "bbd1c71a-fb88-f727-94d6-223a9d9f2a86"]}, {"name": "prypiatosian_a", "origin": [-5, 12, 0], "color": 0, "uuid": "2b2cf3df-1150-4f98-0fde-cee2a192ab9c", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["bdd22596-0f78-bf0a-1431-6ef2221a2ae8", "e712e9dd-fe6b-07f2-e4f7-0e3c86531b29", "c2bae35a-64a6-4a76-4d29-8bbff5440e5f", "a8abc46a-7f8a-f25b-d4c0-46940329e49a", "71f86bde-0b50-61e7-e48e-dbe73d0f3412", "8fa6e27a-e6ca-6440-fce2-b90c7a3ab3dc"]}]}, {"name": "biosovortian2", "origin": [-10, 33, 0], "color": 0, "uuid": "0c0cbc7d-dc7d-422c-c9a1-46b67364e44c", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["f54a5463-04fb-e454-b196-1d2f2a3d73bc", "26c24c1b-0cfd-d602-d5ab-b55b6dc1bd0c", "b8051d6e-0698-f448-0f04-fd1052f85c1a"]}]}, {"name": "armorRightLeg", "origin": [3.9000000000000004, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806", "6b1b842a-0880-3443-2dd0-b81a00346c1e", "aab36e1c-a15a-8905-faee-e086e7a37624", "6cc379e5-d2fa-86dc-5ec8-53607d61ddd4", "ad6697bf-ef58-3e51-4b77-ab1ec0b54165"]}, {"name": "armorLeftLeg", "origin": [-3.9000000000000004, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb", "09b0c1a1-48e4-f1a6-507f-a97bb97ebc5d", "c4622225-73f4-e2e2-fbc1-159682c65c65", "9ccd9397-817e-79a5-90e8-4d34b3639ce9", "4dcdb851-f947-adb9-ea04-12c6f212e1e0"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_chronosapien.png", "name": "ov_chronosapien.png", "folder": "", "namespace": "", "id": "1", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "4a52a6c2-8047-171d-59d1-343e7d72e3d8", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/ov_chronosapien.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\chronosapien_prypiatosian_a.png", "name": "chronosapien_prypiatosian_a.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "0c1ece9b-764e-1d98-a628-e3239cd29cbd", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/bio/chronosapien_prypiatosian_a.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_petrosapien_chronosapien.png", "name": "ov_petrosapien_chronosapien.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "83b2da40-84e3-4182-f202-48b674abe9d7", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/bio/ov_petrosapien_chronosapien.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\chronosapien_prypiatosian_a.png", "name": "chronosapien_prypiatosian_a.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "ef8df216-9334-c0a3-6b44-************", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/chronosapien_prypiatosian_a.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio_chronolinopithecus.png", "name": "bio_chronolinopithecus.png", "folder": "", "namespace": "", "id": "5", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "023f7640-5e9e-1f90-14a7-b48c6128dcce", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAGBFJREFUeF7tnV2oXcUVx88tJtfWBuxDSJoQaymllGAN4kORCqUf2CItKAh9KfpQU0os9aORCBYjBAxNrZaalsY+KH0JCJW2SJF+<PERSON>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", "relative_path": "../textures/models/ben/bio_chronolinopithecus.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\bio_necrofriggian_chronosapien.png", "name": "bio_necrofriggian_chronosapien.png", "folder": "", "namespace": "", "id": "6", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "56cdc2e5-84d0-ebb1-2832-1b767a106ec7", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAGKtJREFUeF7tnU2oZVdWx++tynspSE20RATBdqSCaEfIoJtQFm0rQUENSCYNwW61iw4YSwtxKoIjkdLYQkvaTloKehICEUEptG1iEdKDgqQRIToyQoOIpZMKJO/l1ZN7b+3nPevdfffHWvus8/GrSVJ199of/732b6/9cc5ZLpz/XHzm5umuKjz88IFJzQ6evLo3n5P33jEppzaTkzu3lrW22LVT4OIXb+30y5Ov3tzbX7V27VqyP2d35wMAAMDL+feVWzuQa+28NBgcAKxmfiloLBIgAvByvWGXWzuQa+281AAALAG8fG/Q5dYO5Fo7LzEGA4BWM38qEiAC8HK9YZdbO5Br7bzUAABEAF6+N+hyawdyrZ2XGAAAAHj53qDLrR3ItXZeYgAAAODle4Mut3Yg19p5iTFaAMhd/eN372ZpKO3YA8iSbXCJDm/c7pzTH730vKkv1w7kWjsvgU1Fq2lEuAdQugkIAGrUno4NALDpy9EBIAx8OXNf/LGfWiuSigSIAGwcxyuXMPBj/W8dCXi1s69yAQB7AH35mkk5AMBExrNM3AGwvHZ9551r2cwLly53/kkuGVK/B2MiAFsHapVb7Ip4KC/V/7JePHOxu6cAABFAqzGsyhcAqOTLNh4dAFKbhSESiKUjAsj2DdeEtZvDsciRCGAiEQAAcB2XvRUOAPqRenQRQO4akAigHwdqVQpLgFbKdvMFAOwB9ONphaUAgELBKpOPBgByF597AJU9PnIzjgFtOxAAEAHYelTj3ACArcCjA4CMBMLfUzcAY3Y8C2DrUH3l1voqcF/t8C4HABABePtgVfkAoEq2c0ajBUBt87kHUKscdlNUAAAQAUzRr2lTpgIAAABkugrJpqgAAAAAU/Rr2pSpAAAAAJmuQrIpKgAAAMAU/Zo2ZSowGwDwZaBMjyDZrBQAAEQAs3J4GttVYPIA4OvAuDwKxBUAAEQAjI8ZK7BMPXY5Y21m0XTelOPbzfI7Aidfvdl0UpblAQDf/ncvHQD4dgEA8NV/9qUDAF8X6OtLQrFyiAB8+9+9dADg2wWjA0DqpZy+csZLP3j71Z0/Hn/6C6ZVlt8nMM28QWYAoIGoBVnGABCy0O4JpPIvjgCsAdDXwOyrHABQ4P0kXaQG6GwBIH2jdKaODXhtvimfBQAphfh9WwEAEAnNtQMVAOQNNJYAeTq1SjU6AAQhrJYCYaB+9hs/vc766Pho/d+7v/ptU82v/tWn1vkdHhyu//vNz/3T+r+lkUWsUmOb+c/WmHduNT13Nu3ECWYGAB5FAADAx7uJAHx0D6UCgEcACDP053/8ezs98utP/a2qh7527xc69l//l//pRBhEAEQAKgdTGgMAAKB0IZ05EYBOP601AHgEgDBTf+PfHnQ0/dyPXF7/vTQSyM2PCIAIQDuINfYAAABo/EdtSwSgllCVAQB4BIA/e/PqWsjX/v14/d+wWx/U/eUfPF3/729du7tX8JDPX3+3u7kdThee++GDTj5EAEQAqhGsNE4BIGRfeiEoN9/im4ChQtbHgABA6UmV5kQAlcIZmeUO1MkDIOgpQRBm7BAZpO4HhNMEaSdn/lAeEQARgNFYrsoGAIibgACgyo+qjYgAqqUzMRwtAKyWArlXdk3U3pGJNgIY6w3As7UlNwFbuVZWvgAg81mALDUrEgEAlgAVbmNmMnsAbCtZEw08XG52+y+cbk4Jkn9eeKWT5Pjd/acKqfyIAFIK8XuOAikQWG8ChvyqTwGslgC1AAgDP9gDgBw3O5+GPYA63aytAMBisciNAMLgP/7wo3U/HFx6nAig0iMBQKVwxmYAIAMA27N+GPyhHx5/fPOYb+xPWOvLD4WwBGAPwHgsV2UHALZk2xUJbM/6y+VyeXraXfSv/m2VxWOPH643A+SSAADs9ksigKrxam4EACIA2DXr7wPACgyrJYHcGwAAAMB81BpmCAB2iHnx219f/+sq3F8N6hD2r8L9jz7avDko/Nn+Pfx/AMHJpz7//+me3DxzEP6wBGAJYDiO1VlpQVBqP6hTAKneCgDbG33bAFhFBtu/BVCs/ithAADifskSQD1mTTMoHcDnxswXb+09D5fHiaMDQNjxl8eAZzO6iBZW6QEAADAdpT1kVvvBkFK7UQBge02/0j525i+hEJYOAAAA9DBmTYsoHcih8FK7wQEgrPulmtkXfR4ZxiKEC1/6GnsAWwqwBDAdt2aZlQ7kyQKgdODLHjh3YxAAdCQCAGZj1jSj3gCgrfXy2vXMS/i7S9Lepfd24IvP3FS1X6u/t723/nP3P23/qz8KMfcOAAC+x4hz9z8A4Pw8OwAAANpB6GlPBKBUHwAAAKULuZoDAKX8AGBaADi9/36RRzy897p6DBUVaJxYXfm5r8EAAAAwHpO9ZgcAlHIDAACgdCFXcwCglB8AAAClC7maAwCl/ABgWgAodQfvexCl9ZXpAYBSQQAAAJQu5GoOAJTyAwAAoHQhV3MAoJQfAAAApQu5mgMApfwAwBcAhzduq57FOHnvnY4HcA+gcEBwD4CHgQpdxjQ5ANDJSQSg029BBEAEoHQhV3MAoJQfAAAApQu5mqvfCBRq//DDB1UN0b4PoLRcWd7Fb325qt4Y2ShQ+nFW+WEXbS20b4XWln/65svqSVhTBwAAADT+o7YFAM4AODw8UO2i5npArKNbRQC53xnMrT/p2ijQCgDeM3uuWu4RAADI7SrStVAAAMwkAmjhPOQ5fgWsAZA782sjT6l86V5UsJ9NBDB+V6UFLRQAAEQALfxqsHn+4h9/cl233/u5H9hZx6ufvDPYureoWF8AsJ7xU1rkRgREACklJ/Y7AOh2KAAgApjYEO82Jwz4WCNjkUBIP/WIoDUA+p75S/cGiAAmPfwXCwCwv4MBgHMEYHWVNXfNI91BS+jScvu6CRgL9f/o7/+zI0EsAgjpwu9TjwQmPg9Em3d0dMxNQE3nAwCNeth6K+AOgL4uAnkL3Vf5d7/zzLooOYPLmT/UJzcCmMueQF/9NJRyAMBQesKoHgDASMiZZAMAJtbRuQD47c/+eaflhxf+cKcSMnL4m9/9zqQUYxPQeROQJYDteEoBQA58WboEAQDoKpR6HFheBdZuMmu9I7VHxTGgVuGB2KcG/qqa24P/7n//TqfmV7/vT87+HosGtg04FRhIxyurwRJAKeBQzHMB8DNPXl38wT88tbPav/+z9xb/+O7dBQAYSq+2rwcAaK9xLyXICz9hdz+E8KvZfzX4wx8JgdXgD39WEPjTb/7m+q88M9BL97kVAgDcpLctuBQAq9IDBLYH/+rfAYBt3ww5N3cAcBPQ5p2AKQD8/A+9uHjhud/o+GIMAF957S8Xf/cfm3pNPQLgFMD5FAAA+ABg3xIAAMTnbE4BbOMZXgpq9FLQVASwmsn/+V+fW0cB+zYBtwf/dlfLSIBTANuB4JUbS4BLl1Xap85ZZeatHgbKBUCoz399z190qvb9//uls7+H8B8AqFxjFMazA0Drt/Wm1pStABC8LRwHxrxvFQXs+/MTP/pa52eeChzFOK6uJAColm634dABEGotQSAHfkgHAIwdZGDZzQYArWd+2a+53yGw/jKQjADkAC71v6lfBS7VY2rpAUCjHgUAjYQlW1MFZguAMEC1D2uETUAZYXgBQO4F5L4BSHrVXGb+1JJN6sIxoCl/Fr0dA8YGKADY3aEAYLcuAGCgAAjVksdysbV/6wgg1CdVjvUegOye3HcDBru5nfcfHR2bevTy2vWd+WknmtJKxo6nT998WWY1jXcCAoDdLgIA9g8dALAAAKV03U4f2wMYSgQg25Z6bHhqb/xJ9a01AEJ5MhLwjgB2zPxnVU1p1PJ3sz0AIoC8bgIAXZ0AgHME0PcrweSur5bMuXsOecOTVH0rYH0KcPTS8303oag8GZnM7pVgAKDIXyafGAA4Pw7cVwSQey5f6vGx3da+bx6W1pv0GwWsARBeCrpnze0ifex0YjYRAABw8bvBFwoAnCMAqxeCxDYBUx5ovQcQKy9WTut7AKn2z/33VgCQumr9rLSfch9Td48AAIDNG4FKHYT0bZcAACDPw5ofA6aqoSVzLmmJAFI94fN76whg+cH9vQ1bXvlE04an/JMIoKc3AgGApn5enTkAYA+g2nlWhinChswBgErmZsYAAAConAsAqORzNwYAEwOAu0cVVoBTgELBjJN//JO/1Mnx9Ikrnb/LNfxjTz/b+f3jt97Yay+rK/NjD+CZm6fGfTqq7ACAb3cBAOcIYHnt+qwB4Ov+50uPvfBCfvZ6aPWurY+ckYkAapWss1sCgDrhWlkBAJYArXxrV76uLyNYVUh7Eenkzi33NvTZYZTVVUA7gWnvoYzd/9wHDwBgSGsUAAAa9Ra+LyMgAtB1HtaLBQDQeQERgE4/rJ0VAAC6DgAAOv2wdlbAGgCn998vatHDe6+7j6GiCovE7pVnD0DTfdgCAJ0PAACdflg7KwAAdB0AAHT6Ye2sAADQdQAA0OmHtbMC1gAobQ73AEoVE+nZA1AKOHNzAKBzACIAnX5YOysAAHQdAAB0+mHtrAAA0HUAANDph7WzAoc3bqueZj15751OC7gH0HOHsgfQs+ATKw4A6DqUCECnH9bOCgAAXQcAAJ1+WDsrAAB0HeAOAF319dbaJYi+BvPOIfelrkGl2AtTalX0ftOS+3cBaoWbih0A8O1JAOD8TkDf7vcvHQD49kErAHjP7LmqEgHkKtUoHQBoJGxmtgCACCDTVdokqwVAqeNa1T61Bo7NfLnvvgvtkjNTqU7yjny4sCPrUapjbful/rl65PZbaTtCvkQAuQo3Slfq2KEatR2ubUbtAMh1eABQ10O1/gAA6vQ2swIAXSlzASBneKnj0CKAXABaOVYuEACAleKV+QAAAFDpOnvNAEALVRvk2TcADt5+dW8rUh/L9F4CxCIEucYfSgTQ98wvOzcFAiKABoO6JEsAUBYBAIAS70p/vh4AlOlpnro1AOSMf3R0vPf25eHhQefpNhkRTC0CGPsbdcwdsucMuQpc+XXkVGgX+hEAbJSIHQMCgJ5HvCgOAFQCIHUcGAZ+asZPdX+ICEIkUBoB1K6BU6cBEoChnNy1f2g3AEh5QNvfAQAA2OlhAKDtwBtK7gAAABQBoNRxY6E/EUCpkm3SAwAAAADajK1R5AoAjAFgtfaX3nN2OvDCK3sdSz4LULsHkNrjkMdXsZdzpspnD8CXEwAAAOz1wNhpBwDwHbhWpQMAJQDkTEkE0HVNIgCrodomHwAAAIgA2oytUeS6rL0JJ1uXezFG2qVmiJSKpeXK8mrXoDHdLn7ry+sqa8//S/cAjl56vgrmqf5nCZDywHH/DgDu3DIdOACAJcCYkDB7AJR2VixiCDNpXwCIzfhyNz4VYYX2lEYCqU3A3HJL9Se9rQIAoFBPALARDAAUOs5Ak/cOgNaPP6Y+FpmamXL7KayN5R1460ggnP+ffObFddUCgMIHMcK5f3hGoPYeQCoSkHsBtQCQ5dTuweT2E+n2KwAAKj0EAHTfZpu79AAAlQ7XyKw3ALSe+aU+tTfTanWWjm21F5CKAGR9iQBqe3CedgDAqN8BwPXOi0xiSy0iACOHM8rGDQCpp8Ry25d6RZXMx2oPILUZGOpl9UKQ2PP2oX1yTyD8e6q9qbf7hnys9wBCvqk9G9l/fb0PIdf/UulS91T6jozPRcqp459UA2MOIu1im0YpB02VDwA2CgGAjQ61S6CUn9X+PhsA5M4UkvytAJBbTu0udOwtuBKo0gFK3wocu7koB3ztTBLqO5VTgL73fmJgyL1BWQsWKzuzJQAA2NwoBAAbT/DaAwAAZWiYPQDK5Iqnjs2gqRAwVX4qApD2tSHwVCIAGfmFv2sjzVQ/yd9Teyal+bVKDwCMlAUAwzoFyL2XYNT957IZDQBaCRDL17pjtELXboKmTgFiS6JSvXOfXmQTcKNs7VORpf1Sm176f+3eTW350q7qSThN4QCgTD0A0NUrdQwIAMr8qzcAtNqc8dptjX0NN7UJWNY95zfT5LMAIb9ax/c+BSjdI0kBIOyBeM+s52baa90lUvjdu54AoHREPkoPAGyeBQAAXR0r3bHarPkmYKpm2t3ZXAfSlhPb5Y09Ddh6D2AqTwPm9l/QMzcCkP1l3f8pv85tl3sEULsJFhMgt+HBXtsxueVpywEADzoSWD0OnNt/ACCFnLrfiQDqdDtnxTFg3TFgawAsP7i/t4eXVz5h5AG7s0m1jwjg0mVVB6QEtoo0UpUEAABgl4+k/BMAOAOg9lmAFBD4PU+B1k8DEgHs74fZLwEAQN5AbZUKAEzsFKCVo7TKFwC0UjYv3wtP/UrnRSKnT1zpGMoZ/LGnn+38/vFbb3Q3J4W9rIXMjz0Aoy/j5HX38FIBAN8+AQDOEUBpCObrLvale2/C2LdoXDkCAADg6rEAwFX+BQBwBoBv959/gUZpfbxDeG0Epb2gRPvrPu1W6mex9Nr+19ZDO4H19ixArKHam4gMgHEPgLkDEAAoNyEBAADQDiKNPRGARr0d79ArzQ4AAIBSn7FMDwCUarIE0F2FnhoAT++/X+RRD++97rqM1QKgdAmkfQPWuXsRRWo3SAwAAMC2WwGA/YMMAAh9pjYDljJ2au0HAACgaAxMbQAUNX7rc+GldlbprUNgAAAAinwTALAJWOQwxom1ANRWh3sAdxgAWifS2GsHQOkmmKzr2CcAjfYrWwAAAGa1Cw4AugoAAAAAALTTqMJeGwEpil6bAgAA4AqA8HbiWkc+ee+djunYNgGt25/SkWNAjgE7Cnivga0HAADgFCAFwUENAG0IOPZNMABwu/NGoyLnXR3jiggoZU8EQAQwKAACAACQgtagf9deRZZEjn25JnyzLoihnfljoqZeQ23dGakv9ZSWJ3Uqte87/djb77qB1Hdn7SoPAOh6YewDQNf6xWLs7QcAyvcRhBn3nCN85de0vmVqf/zpL5jmV5pZ7kAZWwSQq8NQ2w8AAECuD6vSDXUAqBpVYDzU9s8eAIeHB6pd3AIfGGTSk8+8aFKv3L2H1EDoKwKw3oMZa/sBAAAAAAYKAAADET2yIAKwiQBk38UGhFcEYD3jp3x1LO0nAiACSPly1e9jGQBVjcswGkv7AQAAyHDn+iS59yRCCdZ7AH3P/KlIqO8IKNV+AAAA6kd3hiUAeNBRCQBkOE2fSawvAoW6y44e6k3Ag7df7VPuwZV1dHQ860lw1o1feSMAAACDo1KPFZo9AOZ+CtCjrw2yKCKAQXZLf5UCAP1pPcSSAMAQe6XHOs0dAFY3AWNdNvRNQO8XqvTo6juLYgnAKUBTHwQATeVVZw4AZg4AtQeNPAOWACPvQG31574E0Oo3dnsAMPYeVNYfACgFHLk5ABh5B2qrDwC0Co7bfvYAmPtFmNRd6ZR75z70MdSbgNr2l+oztKuwsz8FAACXUz6893cAsF8+TgFU7tXceAkA5g0AngWY+bMAAAAANJ9mBlzA7PcArPsmfClnqE/DyY8pWm8ChrfvDrX9c5/xpb8DAGMCAIDN67cBgLFjNcoOADQSVn4yynsXPPYZZesI4EzOF17pKOvdfmb+3Y4OAABAm9eCA4BGnmWb7ewBYLUJeG5tfaP70UTvGVDWLyxVtOfgZ18Gkm/WGTgAwl6Ftv2p4Tj0Y8BU/bW/D739ZseAAEC8WQcArMfO0AeAdoCn7Ife/mYACMKEvYAQAbSecUK5QfjY2r95BBAq8ggEXu2Prf1bRwCxC1Knb76cGjOq35fXru+078vvpP/JysT8UdXoLePg1zK/WPsBwCWbewDRTTYA0PFFAPBy00fwAcAjdyMC2LyOeigRQOuBL2c8GQl4RwCtZ/4d7e9sbhMBCIVYAmzuK7QaGHIJAADazvzuAAgV6Jt0pWsnGSJpB8C5TZ6Bv2c/rP23+qtUQlX6wxu399prvwxUAZqmIblsrLwfI383aH9Re8z2AADARoGhX7gBAOf4UzRgVPRbLBaTB8BQQVC6OZLb0dHHgQcWCciB33cEENbkfb0PoCAS6AUAsSvy1hFA6fgzjwBKK5A70LTpAMBmzS//FAwUVRcAgOvrTbkeAZgFtmYAiD0Mo11z53phbGaW9tr6lL4QRFve0Nofq0+uLq1mQOt+ztU9pBtL+wGA0T0A6SBzASAA2K0AAHjyakcZr5twKXJrZ+Tcjp5q+0/vv79X4tMnrnR+7ysEDoVq+zflP2NvPxEAEUDKx/cPcAAwagACAAAAABQKEAF8uLlyOtc1MEsAlgD7+DH0JZA6AlDAE9MJKCBnQOnwsonLD+53/umxp5/t/P3jt97o/F3mJ+1T5fW9B5Cqz9DaDwAmMAg9mwAAuhHQ6AAQuyCjdarYbq/2rrO2Xn3Zj6X92hkSAACArD2AkAgA3O2LQVnlAIAsmaKJxg7A/wPgttYVWCwiPwAAAABJRU5ErkJggg==", "relative_path": "../textures/models/ben/bio/bio_necrofriggian_chronosapien.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio_chronosapien_biosovortian_0.png", "name": "bio_chronosapien_biosovortian_0.png", "folder": "", "namespace": "", "id": "7", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "0e678606-deeb-70e8-7726-1b1c1ac518c4", "relative_path": "../textures/models/ben/bio_chronosapien_biosovortian_0.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "aa984cd2-3c39-28a7-f066-82f121ff8745": {"name": "antenna_af", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "02ddee81-3636-39ee-65b7-ffac08629632", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b69ac39-cee3-b808-ddea-faac6c29c0e9", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "42b68df9-3906-942c-7a93-ad241b3646be", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "e8789e20-e82e-76ba-0511-95f15526d957", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "bf13c477-7a17-f6eb-6e7c-6c56c63eac73": {"name": "antenna_ov", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "d0871530-5721-ef63-f300-bcc01cf74497", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "73acde66-e978-cf9c-dcde-a137b2b6d4b5", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "0132db34-cf0c-86dc-659b-e17f99cef176", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "be174adc-5aee-eaac-3233-4c744e4b25d5", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}