{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "conductoid", "model_identifier": "", "visible_box": [4, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 27.5, -5], "to": [3, 33.5, 3], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 2, -1], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 1}, "east": {"uv": [0, 8, 8, 16], "texture": 1}, "south": {"uv": [24, 7.75, 32, 15.75], "texture": 1}, "west": {"uv": [16, 8, 24, 16], "texture": 1}, "up": {"uv": [16, 8, 8, 0], "texture": 1}, "down": {"uv": [24, 0, 16, 8], "texture": 1}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 27.5, -5], "to": [3, 33.5, 3], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 1, -1], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 1}, "east": {"uv": [32, 8, 40, 16], "texture": 1}, "south": {"uv": [56, 0, 64, 8], "texture": 1}, "west": {"uv": [48, 8, 56, 16], "texture": 1}, "up": {"uv": [48, 8, 40, 0], "texture": 1}, "down": {"uv": [56, 0, 48, 8], "texture": 1}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 20, -2.5], "to": [5, 26, 2.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 1, -0.5], "uv_offset": [16, 16], "faces": {"north": {"uv": [19, 20, 29, 26], "texture": 1}, "east": {"uv": [16, 20, 20, 27], "texture": 1}, "south": {"uv": [32, 20, 40, 27], "texture": 1}, "west": {"uv": [28, 20, 32, 27], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 20, -2.5], "to": [5, 26, 2.5], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 1, -0.5], "uv_offset": [16, 32], "faces": {"north": {"uv": [19, 36, 29, 42], "texture": 1}, "east": {"uv": [16, 36, 20, 43], "texture": 1}, "south": {"uv": [20, 36, 28, 43], "texture": 1}, "west": {"uv": [28, 36, 32, 43], "texture": 1}, "up": {"uv": [28, 36, 20, 32], "texture": 1}, "down": {"uv": [36, 32, 28, 36], "texture": 1}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 16, -2], "to": [8.5, 26, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2, 2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 46.5, 26], "texture": 1}, "east": {"uv": [40, 20, 44, 26], "texture": 1}, "south": {"uv": [52, 20, 54.5, 26], "texture": 1}, "west": {"uv": [48, 20, 52, 26], "texture": 1}, "up": {"uv": [46.5, 20, 44, 16], "texture": 1}, "down": {"uv": [50.5, 16, 48, 20], "texture": null}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 16, -2], "to": [8.5, 26, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [2, 2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 46.5, 42], "texture": 1}, "east": {"uv": [40, 36, 44, 42], "texture": 1}, "south": {"uv": [52, 36, 54.5, 42], "texture": 1}, "west": {"uv": [48, 36, 52, 42], "texture": 1}, "up": {"uv": [46.5, 36, 44, 32], "texture": null}, "down": {"uv": [50.5, 32, 48, 36], "texture": null}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 14, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 1}, "east": {"uv": [0, 20, 4, 32], "texture": 1}, "south": {"uv": [8, 20, 4, 32], "texture": 1}, "west": {"uv": [8, 20, 12, 32], "texture": 1}, "up": {"uv": [8, 20, 4, 16], "texture": 1}, "down": {"uv": [12, 16, 8, 20], "texture": 1}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 14, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 1}, "east": {"uv": [0, 36, 4, 48], "texture": 1}, "south": {"uv": [12, 36, 16, 48], "texture": 1}, "west": {"uv": [8, 36, 12, 48], "texture": 1}, "up": {"uv": [8, 36, 4, 32], "texture": 1}, "down": {"uv": [12, 32, 8, 36], "texture": 1}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -2], "to": [-0.5, 14, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 1}, "east": {"uv": [16, 52, 20, 64], "texture": 1}, "south": {"uv": [28, 52, 32, 64], "texture": 1}, "west": {"uv": [24, 52, 28, 64], "texture": 1}, "up": {"uv": [24, 52, 20, 48], "texture": 1}, "down": {"uv": [28, 48, 24, 52], "texture": 1}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -2], "to": [-0.5, 14, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 1}, "east": {"uv": [0, 52, 4, 64], "texture": 1}, "south": {"uv": [12, 52, 16, 64], "texture": 1}, "west": {"uv": [8, 52, 12, 64], "texture": 1}, "up": {"uv": [8, 52, 4, 48], "texture": 1}, "down": {"uv": [12, 48, 8, 52], "texture": 1}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.4410320421874798, 34.62927082428106, -4.245036023739737], "to": [3.44103204218748, 36.62927082428106, 5.754963976260263], "autouv": 0, "color": 0, "rotation": [-32.5, 0, 0], "origin": [3, 35, 0], "faces": {"north": {"uv": [54, 0, 55, 2.5], "texture": 1}, "east": {"uv": [54, 0, 55, 2.5], "texture": 1}, "south": {"uv": [54, 0, 55, 2.5], "texture": 1}, "west": {"uv": [54, 0, 55, 2.5], "texture": 1}, "up": {"uv": [53.5, 0, 55.5, 2.5], "texture": 1}, "down": {"uv": [54, 0, 55, 2.5], "texture": 1}}, "type": "cube", "uuid": "e215f235-48e7-b606-3e35-c7b06617c248"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.4410320421874798, 36.63640258695515, 2.6985428722267986], "to": [3.44103204218748, 38.63640258695515, 10.698542872226799], "autouv": 0, "color": 0, "rotation": [27.5, 0, 0], "origin": [3, 37, 5], "faces": {"north": {"uv": [57, 8, 58, 16], "texture": 1}, "east": {"uv": [57, 8, 58, 16], "texture": 1}, "south": {"uv": [57, 8, 58, 16], "texture": 1}, "west": {"uv": [57, 8, 58, 16], "texture": 1}, "up": {"uv": [56.5, 8, 58.5, 16], "texture": 1}, "down": {"uv": [57, 8, 58, 16], "texture": 1}}, "type": "cube", "uuid": "6d57defe-6ddd-dc77-7354-63210eb003df"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.4410320421874798, 33.63997799515415, 8.316068615087213], "to": [3.44103204218748, 35.63997799515415, 17.31606861508721], "autouv": 0, "color": 0, "rotation": [55, 0, 0], "origin": [3, 34, 10], "faces": {"north": {"uv": [57, 7.75, 58, 16], "texture": 1}, "east": {"uv": [57, 7.75, 58, 16], "texture": 1}, "south": {"uv": [57, 7.75, 58, 16], "texture": 1}, "west": {"uv": [57, 7.75, 58, 16], "texture": 1}, "up": {"uv": [56.5, 8, 58.5, 16], "texture": 1}, "down": {"uv": [57, 7.75, 58, 16], "texture": 1}}, "type": "cube", "uuid": "26d0d55e-7941-5b28-5a63-8e55b9ded395"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 34.9, 17], "to": [-2, 36.9, 20], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [55, 0, 0], "origin": [-3, 34, 10], "faces": {"north": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "east": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "south": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "west": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "up": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "down": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}}, "type": "cube", "uuid": "8ced54a7-ba6b-dc12-525f-5903b24224c9"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [2, 35, -2], "to": [4, 37, 7], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [-32.5, 0, 0], "origin": [3, 35, 0], "faces": {"north": {"uv": [54, 0, 55, 2.5], "texture": 1}, "east": {"uv": [54, 0, 55, 2.5], "texture": 1}, "south": {"uv": [54, 0, 55, 2.5], "texture": 1}, "west": {"uv": [54, 0, 55, 2.5], "texture": 1}, "up": {"uv": [53.5, 0, 55.5, 2.5], "texture": 1}, "down": {"uv": [54, 0, 55, 2.5], "texture": 1}}, "type": "cube", "uuid": "9a96c008-68af-8dcd-ff01-6c982c62dcca"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [2, 37.9, 3], "to": [4, 39.9, 11], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [27.5, 0, 0], "origin": [3, 37, 5], "faces": {"north": {"uv": [57, 8, 58, 16], "texture": 1}, "east": {"uv": [57, 8, 58, 16], "texture": 1}, "south": {"uv": [57, 8, 58, 16], "texture": 1}, "west": {"uv": [57, 8, 58, 16], "texture": 1}, "up": {"uv": [56.5, 8, 58.5, 16], "texture": 1}, "down": {"uv": [57, 8, 58, 16], "texture": 1}}, "type": "cube", "uuid": "b5d9b3e2-dd2b-3f81-3312-299057e03569"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [2, 34.9, 8], "to": [4, 36.9, 17], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [55, 0, 0], "origin": [3, 34, 10], "faces": {"north": {"uv": [57, 7.75, 58, 16], "texture": 1}, "east": {"uv": [57, 7.75, 58, 16], "texture": 1}, "south": {"uv": [57, 7.75, 58, 16], "texture": 1}, "west": {"uv": [57, 7.75, 58, 16], "texture": 1}, "up": {"uv": [56.5, 8, 58.5, 16], "texture": 1}, "down": {"uv": [57, 7.75, 58, 16], "texture": 1}}, "type": "cube", "uuid": "1cdef952-b6cc-4115-e35c-dac2e58a44f6"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [2, 34.9, 17], "to": [4, 36.9, 20], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [55, 0, 0], "origin": [3, 34, 10], "faces": {"north": {"uv": [38.5, 44.50000000000001, 37, 47.00000000000001], "texture": 1}, "east": {"uv": [38.5, 44.50000000000001, 37, 47.00000000000001], "texture": 1}, "south": {"uv": [38.5, 44.50000000000001, 37, 47.00000000000001], "texture": 1}, "west": {"uv": [38.5, 44.50000000000001, 37, 47.00000000000001], "texture": 1}, "up": {"uv": [38.5, 44.50000000000001, 37, 47.00000000000001], "texture": 1}, "down": {"uv": [38.5, 44.50000000000001, 37, 47.00000000000001], "texture": 1}}, "type": "cube", "uuid": "9ae874f1-6e7b-856c-37ba-f18c5ca10f99"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 35, -2], "to": [-2, 37, 7], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [-32.5, 0, 0], "origin": [-3, 35, 0], "faces": {"north": {"uv": [55, 0, 54, 2.5], "texture": 1}, "east": {"uv": [55, 0, 54, 2.5], "texture": 1}, "south": {"uv": [55, 0, 54, 2.5], "texture": 1}, "west": {"uv": [55, 0, 54, 2.5], "texture": 1}, "up": {"uv": [55.5, 0, 53.5, 2.5], "texture": 1}, "down": {"uv": [55, 0, 54, 2.5], "texture": 1}}, "type": "cube", "uuid": "eaefd640-e3cd-02bc-7834-df76bc187065"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 37.9, 3], "to": [-2, 39.9, 11], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [27.5, 0, 0], "origin": [-3, 37, 5], "faces": {"north": {"uv": [58, 8, 57, 16], "texture": 1}, "east": {"uv": [58, 8, 57, 16], "texture": 1}, "south": {"uv": [58, 8, 57, 16], "texture": 1}, "west": {"uv": [58, 8, 57, 16], "texture": 1}, "up": {"uv": [58.5, 8, 56.5, 16], "texture": 1}, "down": {"uv": [58, 8, 57, 16], "texture": 1}}, "type": "cube", "uuid": "3989a682-cf2d-d026-8459-886414e7c027"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 34.9, 8], "to": [-2, 36.9, 17], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [55, 0, 0], "origin": [-3, 34, 10], "faces": {"north": {"uv": [58, 7.75, 57, 16], "texture": 1}, "east": {"uv": [58, 7.75, 57, 16], "texture": 1}, "south": {"uv": [58, 7.75, 57, 16], "texture": 1}, "west": {"uv": [58, 7.75, 57, 16], "texture": 1}, "up": {"uv": [58.5, 8, 56.5, 16], "texture": 1}, "down": {"uv": [58, 7.75, 57, 16], "texture": 1}}, "type": "cube", "uuid": "d921aa56-f383-11d7-b973-0bd117c09c42"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.4410320421874798, 33.63997799515415, 17.31606861508721], "to": [3.44103204218748, 35.63997799515415, 20.31606861508721], "autouv": 0, "color": 0, "rotation": [55, 0, 0], "origin": [3, 34, 10], "faces": {"north": {"uv": [38.5, 44.50000000000001, 37, 47.00000000000001], "texture": 1}, "east": {"uv": [38.5, 44.50000000000001, 37, 47.00000000000001], "texture": 1}, "south": {"uv": [38.5, 44.50000000000001, 37, 47.00000000000001], "texture": 1}, "west": {"uv": [38.5, 44.50000000000001, 37, 47.00000000000001], "texture": 1}, "up": {"uv": [38.5, 44.50000000000001, 37, 47.00000000000001], "texture": 1}, "down": {"uv": [38.5, 44.50000000000001, 37, 47.00000000000001], "texture": 1}}, "type": "cube", "uuid": "233498ac-a71f-4bfa-2b43-2e2651ef1d77"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 12, -1], "to": [0, 14, 7], "autouv": 0, "color": 0, "rotation": [-32.5, 0, 0], "origin": [-1, 12, 0], "faces": {"north": {"uv": [55, 0, 54, 2.5], "texture": 1}, "east": {"uv": [55, 0, 54, 2.5], "texture": 1}, "south": {"uv": [55, 0, 54, 2.5], "texture": 1}, "west": {"uv": [55, 0, 54, 2.5], "texture": 1}, "up": {"uv": [55.5, 0, 53.5, 2.5], "texture": 1}, "down": {"uv": [55, 0, 54, 2.5], "texture": 1}}, "type": "cube", "uuid": "473222fd-9b3d-f5b0-a36b-67fe1a57aada"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.44103204218748, 33.63997799515415, 17.31606861508721], "to": [-1.4410320421874798, 35.63997799515415, 20.31606861508721], "autouv": 0, "color": 0, "rotation": [55, 0, 0], "origin": [-3, 34, 10], "faces": {"north": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "east": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "south": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "west": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "up": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "down": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}}, "type": "cube", "uuid": "2d07727f-4d16-01c8-aa73-e84173111982"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.44103204218748, 34.62927082428106, -4.245036023739737], "to": [-1.4410320421874798, 36.62927082428106, 5.754963976260263], "autouv": 0, "color": 0, "rotation": [-32.5, 0, 0], "origin": [-3, 35, 0], "faces": {"north": {"uv": [55, 0, 54, 2.5], "texture": 1}, "east": {"uv": [55, 0, 54, 2.5], "texture": 1}, "south": {"uv": [55, 0, 54, 2.5], "texture": 1}, "west": {"uv": [55, 0, 54, 2.5], "texture": 1}, "up": {"uv": [55.5, 0, 53.5, 2.5], "texture": 1}, "down": {"uv": [55, 0, 54, 2.5], "texture": 1}}, "type": "cube", "uuid": "6b3d44cd-de7b-0e96-7c9c-17020f9da23a"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.44103204218748, 36.63640258695515, 2.6985428722267986], "to": [-1.4410320421874798, 38.63640258695515, 10.698542872226799], "autouv": 0, "color": 0, "rotation": [27.5, 0, 0], "origin": [-3, 37, 5], "faces": {"north": {"uv": [58, 8, 57, 16], "texture": 1}, "east": {"uv": [58, 8, 57, 16], "texture": 1}, "south": {"uv": [58, 8, 57, 16], "texture": 1}, "west": {"uv": [58, 8, 57, 16], "texture": 1}, "up": {"uv": [58.5, 8, 56.5, 16], "texture": 1}, "down": {"uv": [58, 8, 57, 16], "texture": 1}}, "type": "cube", "uuid": "6a524067-5c58-1674-9bc2-d12aa311efc9"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.44103204218748, 33.63997799515415, 8.316068615087213], "to": [-1.4410320421874798, 35.63997799515415, 17.31606861508721], "autouv": 0, "color": 0, "rotation": [55, 0, 0], "origin": [-3, 34, 10], "faces": {"north": {"uv": [58, 7.75, 57, 16], "texture": 1}, "east": {"uv": [58, 7.75, 57, 16], "texture": 1}, "south": {"uv": [58, 7.75, 57, 16], "texture": 1}, "west": {"uv": [58, 7.75, 57, 16], "texture": 1}, "up": {"uv": [58.5, 8, 56.5, 16], "texture": 1}, "down": {"uv": [58, 7.75, 57, 16], "texture": 1}}, "type": "cube", "uuid": "dc617b9f-52da-bc58-b116-1db09bc2eb91"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 14.899999999999999, 3], "to": [0, 16.9, 11], "autouv": 0, "color": 0, "rotation": [27.5, 0, 0], "origin": [-1, 14, 5], "faces": {"north": {"uv": [58, 8, 57, 16], "texture": 1}, "east": {"uv": [58, 8, 57, 16], "texture": 1}, "south": {"uv": [58, 8, 57, 16], "texture": 1}, "west": {"uv": [58, 8, 57, 16], "texture": 1}, "up": {"uv": [58.5, 8, 56.5, 16], "texture": 1}, "down": {"uv": [58, 8, 57, 16], "texture": 1}}, "type": "cube", "uuid": "1acfb9c4-21cf-6e16-0554-399f4dfaed1f"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 11.899999999999999, 8], "to": [0, 13.899999999999999, 17], "autouv": 0, "color": 0, "rotation": [55, 0, 0], "origin": [-1, 11, 10], "faces": {"north": {"uv": [58, 7.75, 57, 16], "texture": 1}, "east": {"uv": [58, 7.75, 57, 16], "texture": 1}, "south": {"uv": [58, 7.75, 57, 16], "texture": 1}, "west": {"uv": [58, 7.75, 57, 16], "texture": 1}, "up": {"uv": [58.5, 8, 56.5, 16], "texture": 1}, "down": {"uv": [58, 7.75, 57, 16], "texture": 1}}, "type": "cube", "uuid": "1c2ca142-81c3-da75-1a95-0bf8e0bc9f0f"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 11.899999999999999, 17], "to": [0, 13.899999999999999, 20], "autouv": 0, "color": 0, "rotation": [55, 0, 0], "origin": [-1, 11, 10], "faces": {"north": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "east": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "south": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "west": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "up": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}, "down": {"uv": [37, 44.50000000000001, 38.5, 47.00000000000001], "texture": 1}}, "type": "cube", "uuid": "1e396ce8-bd49-2341-7a02-f876c57ecb3a"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.44103204218748, 34.62927082428106, -5.245036023739737], "to": [-1.4410320421874798, 36.62927082428106, -4.245036023739737], "autouv": 0, "color": 0, "rotation": [-32.5, 0, 0], "origin": [-3, 35, 0], "faces": {"north": {"uv": [55, 0, 54, 2.5], "texture": 1}, "east": {"uv": [55, 0, 54, 2.5], "texture": 1}, "south": {"uv": [55, 0, 54, 2.5], "texture": 1}, "west": {"uv": [55, 0, 54, 2.5], "texture": 1}, "up": {"uv": [55.5, 0, 53.5, 2.5], "texture": 1}, "down": {"uv": [55, 0, 54, 2.5], "texture": 1}}, "type": "cube", "uuid": "2eda0da5-5b97-764d-57b1-9a5823e0d672"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.4410320421874798, 34.62927082428106, -5.245036023739737], "to": [3.44103204218748, 36.62927082428106, -4.245036023739737], "autouv": 0, "color": 0, "rotation": [-32.5, 0, 0], "origin": [3, 35, 0], "faces": {"north": {"uv": [54, 0, 55, 2.5], "texture": 1}, "east": {"uv": [54, 0, 55, 2.5], "texture": 1}, "south": {"uv": [54, 0, 55, 2.5], "texture": 1}, "west": {"uv": [54, 0, 55, 2.5], "texture": 1}, "up": {"uv": [53.5, 0, 55.5, 2.5], "texture": 1}, "down": {"uv": [54, 0, 55, 2.5], "texture": 1}}, "type": "cube", "uuid": "86ab9fbb-207c-9435-2efe-21440a1ac45f"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 15, -2], "to": [4, 19, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 26, 28, 32], "texture": 1}, "east": {"uv": [16, 26, 20, 32], "texture": 1}, "south": {"uv": [32, 26, 40, 32], "texture": 1}, "west": {"uv": [28, 26, 32, 32], "texture": 1}, "up": {"uv": [28, 26, 20, 22], "texture": 1}, "down": {"uv": [36, 22, 28, 26], "texture": 1}}, "type": "cube", "uuid": "8fb2f24f-f493-32f9-960a-66b34e43c063"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 15, -2], "to": [4, 19, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 42, 28, 48], "texture": 1}, "east": {"uv": [16, 42, 20, 48], "texture": 1}, "south": {"uv": [20, 42, 28, 48], "texture": 1}, "west": {"uv": [28, 42, 32, 48], "texture": 1}, "up": {"uv": [28, 42, 20, 38], "texture": 1}, "down": {"uv": [36, 38, 28, 42], "texture": 1}}, "type": "cube", "uuid": "272eaafc-2e50-9155-44ca-28950857a81b"}, {"name": "Right Hand", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 10, -2], "to": [8.8, 15, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2, -2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 27, 46.8, 32], "texture": 1}, "east": {"uv": [40, 27, 44, 32], "texture": 1}, "south": {"uv": [52, 27, 54.8, 32], "texture": 1}, "west": {"uv": [48, 27, 52, 32], "texture": 1}, "up": {"uv": [46.8, 20, 44, 16], "texture": 1}, "down": {"uv": [50.8, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "09a5c3d9-fc15-7d18-c66b-37520a88684f"}, {"name": "Right Hand Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 10, -2], "to": [8.8, 15, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [2, -2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 43, 46.8, 48], "texture": 1}, "east": {"uv": [40, 43, 44, 48], "texture": 1}, "south": {"uv": [52, 43, 54.8, 48], "texture": 1}, "west": {"uv": [48, 43, 52, 48], "texture": 1}, "up": {"uv": [46.8, 36, 44, 32], "texture": 1}, "down": {"uv": [50.8, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "c1026957-ee5f-b9be-48e6-96a36e30cbbc"}, {"name": "conduct", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 21.5, -1.5], "to": [10, 24.5, 1.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [5, 7, 2], "uv_offset": [40, 16], "faces": {"north": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "east": {"uv": [41, 20.5, 43, 23.5], "texture": 1}, "south": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "west": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "up": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "down": {"uv": [43, 21, 43.5, 22.75], "texture": 1}}, "type": "cube", "uuid": "47c2930b-34d7-da1e-73e3-89b9867c5654"}, {"name": "conduct", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 25.5, -1], "to": [9.5, 25.5, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [4, 11, 1.5], "uv_offset": [40, 16], "faces": {"north": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "east": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "south": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "west": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "up": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "down": {"uv": [43, 21, 43.5, 22.75], "texture": 1}}, "type": "cube", "uuid": "90871d3c-0b3c-1ed0-7122-d7dfce902b06"}, {"name": "conduct", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 20.5, -1], "to": [9.5, 20.5, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [4, 6, 1.5], "uv_offset": [40, 16], "faces": {"north": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "east": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "south": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "west": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "up": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "down": {"uv": [43, 21, 43.5, 22.75], "texture": 1}}, "type": "cube", "uuid": "a218d17d-bbe7-894a-0752-9bba99d0bd0f"}, {"name": "conduct", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 15, -1], "to": [9.5, 15, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [4, 1, 1.5], "uv_offset": [40, 16], "faces": {"north": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "east": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "south": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "west": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "up": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "down": {"uv": [43, 21, 43.5, 22.75], "texture": 1}}, "type": "cube", "uuid": "4df05cb3-14c4-46c8-da69-a064aa86cc2b"}, {"name": "conduct", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 19, -1], "to": [9.5, 19, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [4, 5, 1.5], "uv_offset": [40, 16], "faces": {"north": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "east": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "south": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "west": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "up": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "down": {"uv": [43, 21, 43.5, 22.75], "texture": 1}}, "type": "cube", "uuid": "696d35f7-2d6d-2161-e312-3d5c4c2f64d5"}, {"name": "conduct", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 16, -1], "to": [10, 18, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [5, 1, 1], "uv_offset": [40, 16], "faces": {"north": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "east": {"uv": [41, 20.5, 43, 23.5], "texture": 1}, "south": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "west": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "up": {"uv": [43, 21, 43.5, 22.75], "texture": 1}, "down": {"uv": [43, 21, 43.5, 22.75], "texture": 1}}, "type": "cube", "uuid": "450910b2-38fa-ed21-2a22-9aeaf8207630"}, {"name": "armorHead", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.75, 25, -1.5], "to": [1.75, 28, 1.5], "autouv": 0, "color": 0, "inflate": 0.51, "rotation": [-22.5, 0, 0], "origin": [-0.5, 25.5, -0.5], "faces": {"north": {"uv": [31.75, 14.4375, 31.75, 15.9375], "texture": 1}, "east": {"uv": [31.75, 14.4375, 31.75, 15.9375], "texture": 1}, "south": {"uv": [31.75, 14.4375, 31.75, 15.9375], "texture": 1}, "west": {"uv": [31.75, 14.4375, 31.75, 15.9375], "texture": 1}, "up": {"uv": [31.75, 14.4375, 31.75, 15.9375], "texture": 1}, "down": {"uv": [31.75, 14.4375, 31.75, 15.9375], "texture": 1}}, "type": "cube", "uuid": "72760b34-8bc9-f3b6-8f13-fea4db9dd4d5"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3, 0, -4], "to": [3.5, 0, -3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [8.75, 18.25, 10.25, 19], "texture": 1}, "east": {"uv": [8.75, 18.25, 10.25, 19], "texture": 1}, "south": {"uv": [8.75, 18.25, 10.25, 19], "texture": 1}, "west": {"uv": [8.75, 18.25, 10.25, 19], "texture": 1}, "up": {"uv": [8.75, 18.25, 10.25, 19], "texture": 1}, "down": {"uv": [8.75, 18.25, 10.25, 19], "texture": 1}}, "type": "cube", "uuid": "ec61e76a-a615-d50d-9f52-2ab75d847eaa"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1, 0, -4], "to": [1.5, 0, -3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1.9000000953674316, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [8.75, 18.25, 10.25, 19], "texture": 1}, "east": {"uv": [8.75, 18.25, 10.25, 19], "texture": 1}, "south": {"uv": [8.75, 18.25, 10.25, 19], "texture": 1}, "west": {"uv": [8.75, 18.25, 10.25, 19], "texture": 1}, "up": {"uv": [8.75, 18.25, 10.25, 19], "texture": 1}, "down": {"uv": [8.75, 18.25, 10.25, 19], "texture": 1}}, "type": "cube", "uuid": "2b20f1bc-3bf3-5244-b49c-c264311d1e0c"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -4], "to": [-3, 0, -3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [10.25, 18.25, 8.75, 19], "texture": 1}, "east": {"uv": [10.25, 18.25, 8.75, 19], "texture": 1}, "south": {"uv": [10.25, 18.25, 8.75, 19], "texture": 1}, "west": {"uv": [10.25, 18.25, 8.75, 19], "texture": 1}, "up": {"uv": [10.25, 18.25, 8.75, 19], "texture": 1}, "down": {"uv": [10.25, 18.25, 8.75, 19], "texture": 1}}, "type": "cube", "uuid": "aae4d32f-499b-48ab-a89c-c79975758c33"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 0, -4], "to": [-1, 0, -3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1.9000000953674316, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [10.25, 18.25, 8.75, 19], "texture": 1}, "east": {"uv": [10.25, 18.25, 8.75, 19], "texture": 1}, "south": {"uv": [10.25, 18.25, 8.75, 19], "texture": 1}, "west": {"uv": [10.25, 18.25, 8.75, 19], "texture": 1}, "up": {"uv": [10.25, 18.25, 8.75, 19], "texture": 1}, "down": {"uv": [10.25, 18.25, 8.75, 19], "texture": 1}}, "type": "cube", "uuid": "518f1640-cbcd-2a62-7b6d-7e9980814257"}, {"name": "Left Hand", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.8, 10, -2], "to": [-6, 15, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-2, -2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [46.8, 27, 44, 32], "texture": 1}, "east": {"uv": [52, 27, 48, 32], "texture": 1}, "south": {"uv": [54.8, 27, 52, 32], "texture": 1}, "west": {"uv": [44, 27, 40, 32], "texture": 1}, "up": {"uv": [44, 20, 46.8, 16], "texture": 1}, "down": {"uv": [48, 16, 50.8, 20], "texture": 1}}, "type": "cube", "uuid": "68155b09-c6a0-b263-6b88-8cc4af579a52"}, {"name": "Left Hand Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.8, 10, -2], "to": [-6, 15, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-2, -2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [46.8, 43, 44, 48], "texture": 1}, "east": {"uv": [52, 43, 48, 48], "texture": 1}, "south": {"uv": [54.8, 43, 52, 48], "texture": 1}, "west": {"uv": [44, 43, 40, 48], "texture": 1}, "up": {"uv": [44, 36, 46.8, 32], "texture": 1}, "down": {"uv": [48, 32, 50.8, 36], "texture": 1}}, "type": "cube", "uuid": "6aeb62bc-acd4-b508-45f9-7d189c7abfeb"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.5, 16, -2], "to": [-6, 26, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-2, 2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [46.5, 36, 44, 42], "texture": 1}, "east": {"uv": [52, 36, 48, 42], "texture": 1}, "south": {"uv": [54.5, 36, 52, 42], "texture": 1}, "west": {"uv": [44, 36, 40, 42], "texture": 1}, "up": {"uv": [44, 36, 46.5, 32], "texture": null}, "down": {"uv": [48, 32, 50.5, 36], "texture": null}}, "type": "cube", "uuid": "f34323ee-6b4a-e781-e730-30ad9c00230f"}, {"name": "conduct", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.5, 15, -1], "to": [-9, 15, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-4, 1, 1.5], "uv_offset": [40, 16], "faces": {"north": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "east": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "south": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "west": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "up": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "down": {"uv": [43.5, 21, 43, 22.75], "texture": 1}}, "type": "cube", "uuid": "1bccb35f-3a66-bb8e-1566-7fc63399a8b3"}, {"name": "conduct", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.5, 19, -1], "to": [-9, 19, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-4, 5, 1.5], "uv_offset": [40, 16], "faces": {"north": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "east": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "south": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "west": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "up": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "down": {"uv": [43.5, 21, 43, 22.75], "texture": 1}}, "type": "cube", "uuid": "5c00a615-e81c-7166-59c5-cde21b7695e8"}, {"name": "conduct", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10, 16, -1], "to": [-9, 18, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-5, 1, 1], "uv_offset": [40, 16], "faces": {"north": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "east": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "south": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "west": {"uv": [43, 20.5, 41, 23.5], "texture": 1}, "up": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "down": {"uv": [43.5, 21, 43, 22.75], "texture": 1}}, "type": "cube", "uuid": "fb242d98-b84c-5bfe-f30e-916c19d50d7c"}, {"name": "conduct", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.5, 20.5, -1], "to": [-9, 20.5, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-4, 6, 1.5], "uv_offset": [40, 16], "faces": {"north": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "east": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "south": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "west": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "up": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "down": {"uv": [43.5, 21, 43, 22.75], "texture": 1}}, "type": "cube", "uuid": "6dd674e7-9adb-efb6-cc1d-2fd3839cca60"}, {"name": "conduct", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.5, 25.5, -1], "to": [-9, 25.5, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-4, 11, 1.5], "uv_offset": [40, 16], "faces": {"north": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "east": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "south": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "west": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "up": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "down": {"uv": [43.5, 21, 43, 22.75], "texture": 1}}, "type": "cube", "uuid": "42fb9c3f-a880-9b00-5454-8ba639b184a3"}, {"name": "conduct", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10, 21.5, -1.5], "to": [-9, 24.5, 1.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-5, 7, 2], "uv_offset": [40, 16], "faces": {"north": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "east": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "south": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "west": {"uv": [43, 20.5, 41, 23.5], "texture": 1}, "up": {"uv": [43.5, 21, 43, 22.75], "texture": 1}, "down": {"uv": [43.5, 21, 43, 22.75], "texture": 1}}, "type": "cube", "uuid": "2e2b02c7-38f7-a377-8ff5-8f9b3e09f70f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.5, 16, -2], "to": [-6, 26, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-2, 2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [46.5, 20, 44, 26], "texture": 1}, "east": {"uv": [52, 20, 48, 26], "texture": 1}, "south": {"uv": [54.5, 20, 52, 26], "texture": 1}, "west": {"uv": [44, 20, 40, 26], "texture": 1}, "up": {"uv": [44, 20, 46.5, 16], "texture": 1}, "down": {"uv": [48, 16, 50.5, 20], "texture": null}}, "type": "cube", "uuid": "46ce976a-00a2-3719-4b86-5198dcba25a7"}], "outliner": [{"name": "armorHead", "origin": [0, 26, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", "72760b34-8bc9-f3b6-8f13-fea4db9dd4d5", {"name": "cable3", "origin": [0, 0, 0], "rotation": [-8.606518949548925e-16, 22.5, -10.999999999999993], "color": 0, "uuid": "2fc9b95f-5d6b-0f28-e0da-a8b6be4ffc5b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["2d07727f-4d16-01c8-aa73-e84173111982", "6b3d44cd-de7b-0e96-7c9c-17020f9da23a", "2eda0da5-5b97-764d-57b1-9a5823e0d672", "6a524067-5c58-1674-9bc2-d12aa311efc9", "dc617b9f-52da-bc58-b116-1db09bc2eb91"]}, {"name": "cable", "origin": [0, 0, 0], "rotation": [-8.606518949548925e-16, -22.5, 10.999999999999993], "color": 0, "uuid": "a8bdd2c6-60f9-202b-3a11-041e8600fa1d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["233498ac-a71f-4bfa-2b43-2e2651ef1d77", "e215f235-48e7-b606-3e35-c7b06617c248", "86ab9fbb-207c-9435-2efe-21440a1ac45f", "6d57defe-6ddd-dc77-7354-63210eb003df", "26d0d55e-7941-5b28-5a63-8e55b9ded395"]}, {"name": "cable2", "origin": [0, 0, 0], "color": 0, "uuid": "7b83acde-8e59-bf38-4fa9-41d90cdd404d", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["8ced54a7-ba6b-dc12-525f-5903b24224c9", "9a96c008-68af-8dcd-ff01-6c982c62dcca", "b5d9b3e2-dd2b-3f81-3312-299057e03569", "1cdef952-b6cc-4115-e35c-dac2e58a44f6", "9ae874f1-6e7b-856c-37ba-f18c5ca10f99", "eaefd640-e3cd-02bc-7834-df76bc187065", "3989a682-cf2d-d026-8459-886414e7c027", "d921aa56-f383-11d7-b973-0bd117c09c42"]}, {"name": "antenna", "origin": [0, 2, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": false, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": false, "autouv": 0, "children": []}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "272eaafc-2e50-9155-44ca-28950857a81b", "8fb2f24f-f493-32f9-960a-66b34e43c063", {"name": "tail", "origin": [2, -23, 0], "rotation": [0, 0, -1], "color": 0, "uuid": "6436e9d5-4ca1-ace6-adf5-2ae51c080f0f", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["473222fd-9b3d-f5b0-a36b-67fe1a57aada", {"name": "tail_middle", "origin": [2, -23, 0], "color": 0, "uuid": "6d1bcad6-4102-f834-229e-b1d8ec164c6e", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1acfb9c4-21cf-6e16-0554-399f4dfaed1f", "1c2ca142-81c3-da75-1a95-0bf8e0bc9f0f", "1e396ce8-bd49-2341-7a02-f876c57ecb3a"]}]}]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "47c2930b-34d7-da1e-73e3-89b9867c5654", "90871d3c-0b3c-1ed0-7122-d7dfce902b06", "a218d17d-bbe7-894a-0752-9bba99d0bd0f", "450910b2-38fa-ed21-2a22-9aeaf8207630", "696d35f7-2d6d-2161-e312-3d5c4c2f64d5", "4df05cb3-14c4-46c8-da69-a064aa86cc2b", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", "c1026957-ee5f-b9be-48e6-96a36e30cbbc", "09a5c3d9-fc15-7d18-c66b-37520a88684f"]}, {"name": "armorLeftArm", "origin": [-4, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "armNested", "origin": [-5, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, "46ce976a-00a2-3719-4b86-5198dcba25a7", "2e2b02c7-38f7-a377-8ff5-8f9b3e09f70f", "42fb9c3f-a880-9b00-5454-8ba639b184a3", "6dd674e7-9adb-efb6-cc1d-2fd3839cca60", "fb242d98-b84c-5bfe-f30e-916c19d50d7c", "5c00a615-e81c-7166-59c5-cde21b7695e8", "1bccb35f-3a66-bb8e-1566-7fc63399a8b3", "f34323ee-6b4a-e781-e730-30ad9c00230f", "6aeb62bc-acd4-b508-45f9-7d189c7abfeb", "68155b09-c6a0-b263-6b88-8cc4af579a52"]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "ec61e76a-a615-d50d-9f52-2ab75d847eaa", "2b20f1bc-3bf3-5244-b49c-c264311d1e0c", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb", "518f1640-cbcd-2a62-7b6d-7e9980814257", "aae4d32f-499b-48ab-a89c-c79975758c33"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\conductoid.png", "name": "conductoid.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "c8f7c8c8-eee1-a6e7-eebb-bf65e5bb0d22", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAGD1JREFUeF7tnU/optdVx99EwuxCpDDQwRCGoiWlhCS2kCwEKZKoCKEQu8vGBsR2FyriqtOViCUbaUUY3WRXCyEgaoIUwUUKrWkIYqhShhJJYaA0xNUQzMi8vzk/f++d577n3HvO/fM87yebJL/n3HPv+Z5zvs+5f5773rcb/M/DVx+7XTKEm++/e1T88pVHS9TdI5vq9+rTBvPejXfu02RO4bnEQQ7/UpxK9ZXKaz6J1qf1V/t8ePBBABDAneCNTphSfaXyWsJF69P6q30OASTIUQHUhpKvXXTClOorldesjdan9Vf7HAKAAGpjJ7RddMKU6iuV14yP1qf1V/scAoAAamMntF10wpTqK5XXjI/Wp/VX+xwCgABqYye0XXTClOorldeMj9an9Vf7HAKAAGpjJ7RddMKU6iuV14yP1qf1V/scAoAAamMntF10wpTqK5XXjI/Wp/VX+xwCgABqYye0XXTClOorldeMj9an9Vf7HAKAAGpjJ7RddMKU6iuV14yP1qf1V/v85AlAO1lYC6y13a1bHw33gXWsLeWiEyZaX0vbR+oeHnziKCsIWsKWHt3V9FnHVSsHAZwhF52w0fpq/Tt7OwhA+bagtQMhAAigdYwd0w8BQAAj4++87+g3drS+KUBqMAgIAAJoEFblKqMTNlpfuUXraAEBQABTRGp0wkbrmwKkBoMYTgCXLj2wvw9AFu9yX+OVLtZF62uA/V4lawCHyEo8yF+9+ETraxUHo/RCAKOQv9uvN8AHDz+8++iEjdYXbvBghdMQwGAchnUPAQyDno53ux0EMDgMIIDBDjjx7ocTQG6xprVfcmsErftN9UMAvRGnv4sIQADsApARJ4zANAQgPihd7S/1XXpUuHV/2vioADSEeN4SAQiACqBlfKF7cgSmI4BWlUDuIyEqgMkjlOE1RQACoAJoGmAonxsBCAACmDtCGV1TBCAACKBpgKF8bgQgAAhg7ghldE0RgAAggKYBhvK5EZiWAKJ2A7QrwtgFmDtAGV1bBCAAKoC2EYb2qRG4L/1c0jta7Y1bqt/7hp5tPKn92slE7blX33s3+Hny0phsIR99gYlVHwRQ6E0vIXkTVutfI4z0OQRQGACNxK0Ja+3eqm86AtAC3ApATs5bEUSPrzRhtf5L9UEA3oiKaW9NWGtvVn0QgBXRu3JaAhaqO78KLbfoqSW0t6KAAEo91kbemrDW3q36wglABlj6po1OLA2oWcanJbj2HALQPL2O59aEtVpj1QcBGBFtRVBagmvPIQCjAycXsyas1QyrvmYEYK0EWiWWFSitEmg9Pi3BtecQgNXTc8tZE9ZqhVUfBHDl0aOYQgDWkEPOg4A1Ya19WPWtjgC+/sbnjmLwjWd+aMVoL0cFwDmAooBpJGxNWGv3Vn0QABXA8NOg1qDespw1Ya0YWPWthgC0N38KjLUSoAKgArAmVUs5a8Jax2DVBwFQAVABWLOqoZw1Ya1DsOprTgDaboC2yFb65i+tBEbfFait8mvP2QWwpsTcctaEtVph1QcBZCoAjZisjtDktATXnkMAGsLreG5NWKs1Vn0QAATAFMCaVQ3lrAlrHYJVHwQAAUAA1qxqKGdNWOsQrPqGE4AYpJXcpWsB2i7A6NX/3NpIigNTAGvIr1vOmrBWK636IIAMohohWR2hyWkJrj1nDUBDeB3PrQlrtcaqbxoCsFYCVgByctqbv9c4qAC8ntxWe2vCWq226oMAqABYA7BmVUM5a8Jah2DV140Acm+81KDWpbdWAbTu31uya+PTpgxcCWZNob5y1oS1jsqqDwJIENUSzOoAq1xpwmrjK9XHjUBWT7WVsyasdRRWfdMQgAT2P/zTP+5tfP+/f7b/95Vf+eT+3w899JDV9r3cBx98sKjnd3/7d/Z/H30CkDWAInduXtiasFYgrPogACoA1gCsWdVQzpqw1iFY9U1HANev/+3exs989uyijv/493etNi/KpXpefPEPqAAuIMUUwBVeYY2tCWvt0KoPAqACoAKwZlVDOWvCWodg1TcdAYiBshYgc3+pBGRNIAeErB3Im1/WAmTun5t7y9+1RTarA6xypYt22vhK9VEBWD3VVs6asNZRWPVBAFQAVADWrGooZ01Y6xCs+qYhgF5vYM4BHF6CSgVgTam2ctaEtY7Cqg8CoAKgArBmVUM5a8Jah2DVBwFAABCANasaylkT1joEqz4IAAKAAKxZ1VDOmrDWIVj1uZ1/6dIDt62DaiF369ZHbhs84/rMX3520f6ffO3HHrX3tP3UNz+9qM/aT659bpBWvaPxl0BP7dB2S6KcM9p+rx3u5IEAIABvEHraQwAe9HY7CMCH344KYGwFlhJArze/hA0VAFMApgBOEvU0hwA86FEB+NC7880CawDuKtLjBC8BvPnKcwfdP/3Ca0XDoQKgAqACKEqZWGEIwIenm71ZBGQR0BeCvtZeAvj2Vx8/GMBXvvV20YCoAKgAqACKUiZWGALw4UkF4MOPNYDB5zC8BOB0/44KgAqACsCbRY72EIADvB27AD702AUY/gb0EgC7AM4UYBGQRUBnCLmaQwAu+DgJ6IOPcwCj58BeAkh3ASQennjqkf1/aucCRtvvjV8WAZ0IchBo3UeBIQBnAjAFYArgDCFX8+iPgYQQqACMboEAIABjqDQRgwB8sBZfCFJ7p573e3armTK+Xl+FWe3SbuvV7LP2o+mxPi+9P0DTm7tfQIsnTa/2vFcc5Mah2Tf6TkYIQIsg5bk1MSGA5QtStARxumcHARxH8B4CSPdFf/T9nx5ouPbqraMaNcDThLHePGMNhFkqABmv2Ff7W4TamzgavxRnrX/NL9r4Ulxe+/NfO1BZGn/SWItDbdzRz3P+n64CgADKXK8lCARw/Go0COAd905cWcQeSp9XAJL4r79x46i+yw/ev3+eqwRGM6+31K4FUyOC/3n5DLf0nxxemj7tzVprh9YuelziL3nzrz3+NPzS+JymAoAANNcdf64lBgSwjB8EMGkF8OwzVw88JsxMBbAcyBoB+Ojl3tajKoBoO3IEsNb40/BZTQWwVgfMOgXQAqP0OQSwvBg9egqq+XFaApATUDc//HhvgxCArMLK32evAMQBsxPBX3/h+kGs/OH3XtRiZ/98K4mf+unaFy/t/7SV+Ms5EwIwhblfaEYC+K+XbD8W8qsvL/8ICASw263lBbR6AsgaMPkuQDrumc4FCAF8458/twjv13/rh/u/QwD5FwAE4H85XtRwvg2YTgEggDqgjy0GQgD3YioEnU4BthJ/uReS/H2abUAZUO7zSHnuPQlYl1ZH3ghXzn7vPrf4M0sFcGfO/4XHf+PckLQKkLf/HYHvvf2vu9yawFbXAMR/WvzJrb21Jyuj469U37RrABBAqSuX5XMVQEoAd1oLCVxMfgjg8JruFGUIICZORUu3j4Fih/3/2qyLfb0qgRwBvPTwH+/+6PcPV/pzBPBXf3d99/J7f7EI2dYrAGucUAFYkTouBwHE4HiuxUoAx6YAEIDuFAhAx8gisXoC0BZZ0rWBqEogV3n0WgS0Vj6WIBgpU+sPCCDGaxBAJY4QQCVwSTMIYJJvAazuzDGvtJ/lKGYuQWsDLodPCRF4DgLlPiueBW9r/OQqtlI7qABqET9st7kKQMybkQAuQn9nUfDiP7lFP5GBAA4DFwKAAEwIpG/86AogRzj/++TP948e+s1fNo0zJ/TBv/xi/+iX3vrEgUjpG9M1iIaNU39oF9KwDRjrjM1WAGliSsJAALEB5NUGAZzIGkCrxNMCMLoCKF19/8TvXdaGePT5z//+5tHnpeNxDaZB4xwBpDcDyTcAVACxTuhWAUAAdY6DAM5wgwDq4kdrFU4AWoe9564yp0xvl5U3iTbe9Hnp4pP3pqDS+/R741uKXw7P9FsAuRfgfCp39ytUKgAv4oftIYBCPCGAQsAU8bQyzH2VSgUQi7toCyOAdJEtHe6oN9O1Fx5bRO7aK+9UIVpLALKab+1Udg9OtQLI4UQFYI0gmxwEYMPpXAoCKASssgKAAGJxzmkLJ4A+w7b3MnoNQHYBSs8DSMWQWwQsJSI7Yn0laxeH12p/Ou7pLgTR3L824CEAzaNjn0MAGzkHMGqOr4VvrzUAGUeKQ24XIF0TyFUI6RrAWr7F0Pxyvrqf3OjESUArcjFym58CQAAxgdJKCycBN1IB5N6ArQLHqrfXFIAKwOqRQzlOAkIAdZFjbAUBGIEaJAYBQABNQ0+mALLvn/5/aeelc3DWAI4jzEEgCKA0B4vkIYAiuLoLQwAQQPeg83RYWgFIX2kloO0ClJ4AnHUNRsM6RwC5dpwE1BAtex62C7DWACyDa7eDAEoRq5sCQACxOOe0hRPA1okAAogNTA4CbWwKAAG8u5ghTAGWiQMCOBECWPvNNRK+URWA9h6ddQ0gl7DeRC49Sbq2I+m5+NnMtwBpQOd+kGPtFQIEsPxjrBCARulnzzf7MZA1sddeCZwqAVgT3CqXvhHlhZEe3JJV//TvuV+plp8Zf+KpR/ZdPP3Ca7bM7CQFAdz9+MNKGJ38Yu4GAlhe48gltAZs7iSgXOEGAWgI+p6H7QLUzuFK2/nM9bc+NQIordhq5akA/LFZowECKEQNArBVANYKjwpgI7sApYt+pXPFwjxtJg4BQACe4NrsGgAEcBYWuSlN6fXgs2wD1pb0VADLNDEdAXjYjLYgAALrRuC+dQ+f0YMACHgQgAA86NEWBFaOAASwcgcyfBDwIAABeNCj7eoReOs7z9++aMSTX/ruSeXESRm7+mjFgHAEIIBwSFEIAutB4G9e+vxBBfDll39wUi/FkzJ2PWHJSHshAAH0Qpp+QAAEpkOACmA6lzAgEOiHAATQD2t6mhABFgEndApDAoFeCEAAvZCmHxCYEIF0EVCGKDcKbf1cAFOACYOSIfVDAALohzU9gcD0CAghUAFM7yoGCALxCEAA8ZiicQUIPHz1sf2JuNzFLqX310frWwGEqxwiawCrdFv8oKMTNlpfvMVovIPAPQSQbovI9cwC16mdlT6VMIlO2Fp9xF/fiIMA+uI9bW+1CZszqFYfBNA3RM4JQIB//Y0bR0dw+cH798+pBPo6qnVvtQkbRQDEX2sPL+uHAMbgPl2vEMB0LukyoCwBPPvM1YMBSGVABdDFL907mY0AiL8+IQAB9MF5+l4ggOld1GSA5wQgByBufvjxviNhYNkFkL9TATTxw3ClowmA+BsTAhDAGNyn6xUCmM4lXQaUJYBc71QAXfzSvZPZCID46xMCEEAfnKfvBQKY3kVNBnjPQaDc55HSO/v/TfwwXOloAhAAiL++oQAB9MV72t4ggGld03RgfAzUFN71KJ+FANaD2DZGCgFsw49uKyAAN4SrVAABrNJt8YPOEYD0dPnKo/v/1O4FiNITbyEalxCAAIiLPQJRiRulB7f0QQAC6IPz9L1EJW6UnukB28gAIYCNONJrRlTiRunx2kN7GwIQgA2nzUtFJa5XDxeC9A01CKAv3tP25k1cMcyrBwLoGyIQQF+8p+/Nux3obZ+7GYhvUNqEDgTQBtfVavUmsLc9BNA3dCCAvnhP35s3gb3t03sBBDAqgDahAwG0wXW1Wr0J7G0PAfQNHQigL96r6S1N5NqTgNZ2AgxfA/YNEQigL96r6Q0CWI2rXAOFAFzwbbfxKALYLqJzWgYBzOmX4aOCAIa7oMsAIIAuMK+3EyECscD6NaBVPkWGg0B9YwUC6Iv36nqDAFbnsqIBQwBFcJ2e8CgCSH+jknMAbWIPAmiD62a0QgCbceWiIRDAtv1bbV26H//EU4/sdT35pe8ejRmZw8svSskArLdJcxCo2mVVDSGAKti23wgC2L6P71gIAZyGn4ut/LMXf/32UqM/vf5vR2Omtp30xUnAYle5GkAALvi227g2kWvbQQBjYgkCGIP79L2OmgJMD8zGBggBbMyhUeZAAFFIzq0HApjbP8NGV1vK17YbZuiJdwwBnHgA5MyvTeTadrhhDAIQwBjcp+911BQgPUcg5wdyf0+BlHFbzy1M74jGA4QAGgO8VvUQwFo9VzZuCKAMr5ORllJe9v3T/9emDqXtRB8VQN8QgwD64r2a3iCA1bjKNVAIwAUfjaMRoAKIRvS4PgigL970piAAAfQNEQjAifelSw8snpl3qjU3v3Xro6E+tNovtwOnht18/12zrUuCo+13DX6CxkODZwL73UOwJoC7o4yC0QlgtR8CaBUBPr0QgA+/nTUBnN1km6+FAMSAlAioAFpFhk0vBGDDKSsFAZRNgSAAZ8AFN4cAnIBCAH0J4M1XnjvwmHZDkdO90zVPb0322g8BOF0MAUAAzhAqag4BFMHVXhgC6EsA3/7q4wdOtd412D4S+vSQHtH22k8F4PQbBAABOEOoqDkEUARXe2EIoC8BpB4dvQvSPsLa9kAF4MQXAoAAnCE0tDkE4IQfAuhLAOwCPH9w8pRdAGcCe5tDABCAN4ZK2rMLUIJWB1kIoC0ByBs//aUhca13FbxDiLi6yP3SUpT9TAFc7tlxFLjwY6jSk4AQwFnJ34oAIQAIwIWAtQKq/RgoJYCvfOvtg/FufRfAehdirRMhgFrk7razJoCzm2zz0QlgtR8CqIsACKAON3MrawDnFH7qm59efPSTr/148e+5RBBh79dxaafSX7ReK8CavVY9tXKa3aMJtNauqHYnXwFAAFGhtKwHAmiLr1f7dASQu45aDPXue6aARRNA7s0v/WoJce2Llw6GKPfbyx+ffuG1Ip+PrgCsdoucZv9zf/KfJvu1N78oma0C6B3/EEDhKnYafekUAALwVQIQwOcPDvqkL4DoF+A0BCDMd/PDj48y/LPPXN0/jwLCWwGYXkcXhHIVgAS+1X5rJVC67VZqT6l8lP25SsD65p+tAhgV/xCAswIYlQAQwPJUAAIoi8hpCUDe9GLO62/c2P/nqVQAmv1WAkjn4KUJUhZOurS1AtDs33oFoNkfVQFDAJNWAFoAQADbrgA0/2+eAC4/eP/+dSKLIFIByN+jzoDPugag2Z+eiNPfvWcSo3cFrBWAZv+1V28tmlxa4cyyC5CuAWj2R8X/tBVALwAgACt1xMhBAMs4QgAvnW1/aKvgp1IB5NJN7N96BaDZv/UKQLN/8xVALwBmrQA0+yGAbU8BNP9vjgDE4PQkVApElOGidxYCkPGkB2FS+2sTP9Uzai1A+ygovfU3Z7+mxzphmWUNYFT8T7MGMAoACMD345zWREu3I9N2sngHARyeBGz9ApyOAEoDyis/GwGUrmJ77e/dPurNHaVntgqgtz8ggEnOAYjjIQBbCkAANpw0KQgAAtBiJPR5VOJG6aECCHXv+pQxBejrs6jEjdIDAfT1/3S9QQB9XRKVuFF6IIC+/p+uNwigr0uiEjdKDwTQ1//T9QYB9HWJlrjpNmDuRiRNj9Wq2Qjg5G8EsjouSg4CiELSpkdLXAjgRG8EsoVPvFQ0AWgn7LQ7AU99G1AIIP0mRD6Plc+gNSKxRsqsFUDO/qjPgAUftgGDtwEhgOOppyUuBLD8UVz0RTgQwF0EWlUA1jdQKkcF8PgekvQNmH4FqRGJFf+1VADRX8FCABCANUdC5bTEzVUAEMDZBTnRH8MxBQieAki2aIGuPQ/NuomUaXbzMRAfA3UN1+gpAAQQswaQ0yKfQ2tEYg2iWacAufFTAVg9a5RrRQBa91EBrPUz2/Mou6P0zEYAvf3FFKDRFEBzZFQAa/3M9jzK7ig9EMBsEdJ5PFQAfQHXEpeDQBwE6hqREEBXuM+vJU97zd0IxFHgRw6g4iBQcLz2JgBOAj666MGUADgJePgbmRwECk58UQcBNAI2o9Y6BYAAIIAukQkBdIH5vJNaAuAgEAeBmkQqBNAE1qxSCOA43rmfCecocKM4hQAaAeucAuRGxUGgH4Ru3Ycq6xtKMb1BADE4WrVYKwAIYBkBTgJaI80o14sAtNV/Ge6pfw1odJu6nWjVw0EgK1IblYMA+jpWqwCso4nSAwFYEd+oXBQBpAGZvsmpAM4CKCpxo/RAABtNbKtZEIAVqRi5qMSN0gMBxPh1tVq8BKC9+VNgtEpga2sAb77y3B6CH33/p/t/X3t1+We95VeR5eiv3P0n3wakf18LAbz1nedvX7Q/t4gn239ipxz5zf09KuHYBXB+DQgBHA9FCAACiCKrJnqiKoDSN3fUG6wJKIFKIQAIIDCc4lVBAPGYXtQIAUAAbSPMqb0VAWhTAyqAQ8exBnB2DwBrAM6ELm0OAZQiViZPBTB3BfB/vbVUeGvkO3wAAAAASUVORK5CYII=", "relative_path": "../textures/models/ben/conductoid.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\conductiod_prypiatosian_a.png", "name": "conductiod_prypiatosian_a.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "ff874f42-4a69-e8fd-2692-09a0bf0d9bd3", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/conductiod_prypiatosian_a.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_celestialsapien_conductoid.png", "name": "ov_celestialsapien_conductoid.png", "folder": "", "namespace": "", "id": "3", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "7d79d7f2-4478-4d88-ff61-82d06a6b0d62", "source": "data:image/png;base64,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********************************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", "relative_path": "../textures/models/ben/ov_celestialsapien_conductoid.png"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}, {"uuid": "715cd984-93f2-9db0-e952-691ab30bdadc", "name": "animation.model.new", "loop": "loop", "override": false, "length": 4.291666666666667, "snapping": 72.24, "selected": false, "saved": false, "path": "", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"6d1bcad6-4102-f834-229e-b1d8ec164c6e": {"name": "tail_middle", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": -5, "z": "0"}], "uuid": "03ccd74e-efef-a676-840a-f079524ca57f", "time": 0.3045404208194906, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "eba16740-fc19-8ca6-cd61-b343bc6564c4", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "43875a30-9dbe-e1af-2bcd-d5aa7286c54c", "time": 0.6090808416389812, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 5, "z": 0}], "uuid": "fdf5fec1-3427-1113-d8e0-933d43949365", "time": 0.9828349944629015, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "936c663a-749d-85c6-063f-bf47bbc8aa14", "time": 1.4258028792912514, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "eaa8f756-46ef-7b4c-1939-6498570d181c", "name": "animation.model.new", "loop": "once", "override": false, "length": 1.5365, "snapping": 24, "selected": false, "saved": true, "path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\animations\\conductoid.tail.anim.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"6d1bcad6-4102-f834-229e-b1d8ec164c6e": {"name": "tail_middle", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "94b28257-6b5d-b28b-e7a0-e2ff8310a6dc", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": -5, "z": 0}], "uuid": "05930240-c4ac-40d0-8fc3-478ca7256d63", "time": 0.3045, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "a2fbfccb-3e69-a0f4-83ff-14e09c6e7d95", "time": 0.6091, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 5, "z": 0}], "uuid": "6c2b1914-ece8-6b4d-5ce6-59559335ebb7", "time": 0.9828, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "2600ace0-4fa5-687c-246b-7c9bf012690a", "time": 1.4258, "color": -1, "interpolation": "linear"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}