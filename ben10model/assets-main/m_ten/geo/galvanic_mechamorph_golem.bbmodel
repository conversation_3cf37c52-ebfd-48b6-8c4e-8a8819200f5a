{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "galvanic_mechamorph_golem", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 27, -2], "to": [3, 29, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.51, "origin": [0, 2, 0], "faces": {"north": {"uv": [7.75, 15.25, 16, 16], "texture": 5}, "east": {"uv": [0, 8, 8, 16], "texture": 5}, "south": {"uv": [24, 8, 32, 16], "texture": 5}, "west": {"uv": [16, 8, 24, 16], "texture": 5}, "up": {"uv": [16, 8, 8, 0], "texture": 5}, "down": {"uv": [24, 0, 16, 8], "texture": 5}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 27, -2], "to": [3, 29, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 1, "origin": [0, 2, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 13.5, 48, 16], "texture": 5}, "east": {"uv": [32, 8, 40, 16], "texture": 5}, "south": {"uv": [56, 8, 64, 16], "texture": 5}, "west": {"uv": [48, 8, 56, 16], "texture": 5}, "up": {"uv": [48, 8, 40, 0], "texture": 5}, "down": {"uv": [56, 0, 48, 8], "texture": 5}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 12, -2], "to": [4, 26, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 32], "texture": 5}, "east": {"uv": [16, 20, 20, 32], "texture": 5}, "south": {"uv": [32, 20, 40, 32], "texture": 5}, "west": {"uv": [28, 20, 32, 32], "texture": 5}, "up": {"uv": [28, 20, 20, 16], "texture": 5}, "down": {"uv": [36, 16, 28, 20], "texture": 5}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 12, -2], "to": [4, 26, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 5}, "east": {"uv": [16, 36, 20, 48], "texture": 5}, "south": {"uv": [32, 36, 40, 48], "texture": 5}, "west": {"uv": [28, 36, 32, 48], "texture": 5}, "up": {"uv": [28, 36, 20, 32], "texture": 5}, "down": {"uv": [36, 32, 28, 36], "texture": 5}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 9, -2], "to": [8, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [1, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 5}, "east": {"uv": [40, 20, 44, 32], "texture": 5}, "south": {"uv": [52, 20, 56, 32], "texture": 5}, "west": {"uv": [48, 20, 52, 32], "texture": 5}, "up": {"uv": [48, 20, 44, 16], "texture": 5}, "down": {"uv": [52, 16, 48, 20], "texture": 5}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 9, -2], "to": [8, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [1, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 5}, "east": {"uv": [40, 36, 44, 48], "texture": 5}, "south": {"uv": [52, 36, 56, 48], "texture": 5}, "west": {"uv": [48, 36, 52, 48], "texture": 5}, "up": {"uv": [48, 36, 44, 32], "texture": 5}, "down": {"uv": [52, 32, 48, 36], "texture": 5}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 9, -2], "to": [-5, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [-1, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 5}, "east": {"uv": [32, 52, 36, 64], "texture": 5}, "south": {"uv": [44, 52, 48, 64], "texture": 5}, "west": {"uv": [40, 52, 44, 64], "texture": 5}, "up": {"uv": [40, 52, 36, 48], "texture": 5}, "down": {"uv": [44, 48, 40, 52], "texture": 5}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 9, -2], "to": [-5, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-1, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 5}, "east": {"uv": [48, 52, 52, 64], "texture": 5}, "south": {"uv": [60, 52, 64, 64], "texture": 5}, "west": {"uv": [56, 52, 60, 64], "texture": 5}, "up": {"uv": [56, 52, 52, 48], "texture": 5}, "down": {"uv": [60, 48, 56, 52], "texture": 5}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 5}, "east": {"uv": [0, 20, 4, 32], "texture": 5}, "south": {"uv": [12, 20, 16, 32], "texture": 5}, "west": {"uv": [8, 20, 12, 32], "texture": 5}, "up": {"uv": [8, 20, 4, 16], "texture": 5}, "down": {"uv": [12, 16, 8, 20], "texture": 5}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 5}, "east": {"uv": [0, 36, 4, 48], "texture": 5}, "south": {"uv": [12, 36, 16, 48], "texture": 5}, "west": {"uv": [8, 36, 12, 48], "texture": 5}, "up": {"uv": [8, 36, 4, 32], "texture": 5}, "down": {"uv": [12, 32, 8, 36], "texture": 5}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, 0, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 5}, "east": {"uv": [16, 52, 20, 64], "texture": 5}, "south": {"uv": [28, 52, 32, 64], "texture": 5}, "west": {"uv": [24, 52, 28, 64], "texture": 5}, "up": {"uv": [24, 52, 20, 48], "texture": 5}, "down": {"uv": [28, 48, 24, 52], "texture": 5}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, 0, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 5}, "east": {"uv": [0, 52, 4, 64], "texture": 5}, "south": {"uv": [12, 52, 16, 64], "texture": 5}, "west": {"uv": [8, 52, 12, 64], "texture": 5}, "up": {"uv": [8, 52, 4, 48], "texture": 5}, "down": {"uv": [12, 48, 8, 52], "texture": 5}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 30, -3], "to": [3, 36, 3], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 1, "origin": [0, 5, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 5}, "east": {"uv": [32, 8, 40, 16], "texture": 5}, "south": {"uv": [56, 8, 64, 16], "texture": 5}, "west": {"uv": [48, 8, 56, 16], "texture": 5}, "up": {"uv": [48, 8, 40, 0], "texture": 5}, "down": {"uv": [56, 0, 48, 8], "texture": 5}}, "type": "cube", "uuid": "9954a7a6-073d-01aa-8ca1-10da91e67ce6"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 30, -3], "to": [3, 36, 3], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.51, "origin": [0, 5, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 5}, "east": {"uv": [0, 8, 8, 16], "texture": 5}, "south": {"uv": [24, 8, 32, 16], "texture": 5}, "west": {"uv": [16, 8, 24, 16], "texture": 5}, "up": {"uv": [16, 8, 8, 0], "texture": 5}, "down": {"uv": [24, 0, 16, 8], "texture": 5}}, "type": "cube", "uuid": "ac44c541-9bae-657d-3cf8-af029b473a71"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 33, -7.5], "to": [4, 43, 0.5], "autouv": 0, "color": 0, "origin": [0, 0, 0], "faces": {"north": {"uv": [8, 8, 16, 15.75], "texture": 5}, "east": {"uv": [0, 8, 8, 15.75], "texture": 5}, "south": {"uv": [24, 8, 32, 15.75], "texture": 5}, "west": {"uv": [16, 8, 24, 15.75], "texture": 5}, "up": {"uv": [16, 8, 8, 0], "texture": 5}, "down": {"uv": [24, 0, 16, 8], "texture": 5}}, "type": "cube", "uuid": "b5728dd9-ec75-03a8-820f-bfbd3996cca6"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 32, -9.5], "to": [1, 36, -7.5], "autouv": 0, "color": 0, "origin": [0, 0, 0], "uv_offset": [24, 0], "faces": {"north": {"uv": [11.25, 8.5, 13.25, 12.5], "texture": 5}, "east": {"uv": [9.25, 8.5, 11.25, 12.5], "texture": 5}, "south": {"uv": [15.25, 8.5, 17.25, 12.5], "texture": 5}, "west": {"uv": [13.25, 8.5, 15.25, 12.5], "texture": 5}, "up": {"uv": [13.25, 8.5, 11.25, 6.5], "texture": 5}, "down": {"uv": [15.25, 6.5, 13.25, 8.5], "texture": 5}}, "type": "cube", "uuid": "6744f9fe-1801-7f1b-5e09-dbbb09a22f47"}, {"name": "right_arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 3.5, -3], "to": [13, 33.5, 3], "autouv": 0, "color": 1, "origin": [0, 0, 0], "uv_offset": [60, 21], "faces": {"north": {"uv": [42.25, 20.25, 46.25, 32], "texture": 5}, "east": {"uv": [36.25, 20.25, 42.25, 32], "texture": 5}, "south": {"uv": [52.25, 20.25, 56.25, 32], "texture": 5}, "west": {"uv": [46.25, 20.25, 52.25, 32], "texture": 5}, "up": {"uv": [47.5, 15.75, 44, 20], "texture": 5}, "down": {"uv": [52, 16, 47.5, 20], "texture": 5}}, "type": "cube", "uuid": "c6e6e7ee-0e96-e299-754a-622d8cc00d70"}, {"name": "left_leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7.5, 0, -3], "to": [-1.5, 16, 2], "autouv": 0, "color": 2, "mirror_uv": true, "origin": [0, 0, 0], "uv_offset": [60, 0], "faces": {"north": {"uv": [23.25, 51.75, 19.75, 64], "texture": 5}, "east": {"uv": [19, 52.25, 16, 64], "texture": 5}, "south": {"uv": [31.75, 51.25, 28, 64], "texture": 5}, "west": {"uv": [32, 51.25, 27, 64], "texture": 5}, "up": {"uv": [20, 50.75, 23.75, 48], "texture": 5}, "down": {"uv": [23.75, 48, 27.5, 50.75], "texture": 5}}, "type": "cube", "uuid": "17134e6e-78f7-61a7-75a2-c6093f27ac7d"}, {"name": "left_arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-13, 3.5, -3], "to": [-9, 33.5, 3], "autouv": 0, "color": 3, "origin": [0, 0, 0], "uv_offset": [60, 58], "faces": {"north": {"uv": [42.25, 20.25, 46.25, 32], "texture": 5}, "east": {"uv": [36.25, 20.25, 42.25, 32], "texture": 5}, "south": {"uv": [52.25, 20.25, 56.25, 32], "texture": 5}, "west": {"uv": [46.25, 20.25, 52.25, 32], "texture": 5}, "up": {"uv": [47.5, 15.75, 44, 20], "texture": 5}, "down": {"uv": [52, 16, 47.5, 20], "texture": 5}}, "type": "cube", "uuid": "241d7b41-317c-9e12-2ec6-4fdf23048182"}, {"name": "right_leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.5, 0, -3], "to": [7.5, 16, 2], "autouv": 0, "color": 4, "origin": [0, 0, 0], "uv_offset": [37, 0], "faces": {"north": {"uv": [19.75, 51.75, 23.25, 64], "texture": 5}, "east": {"uv": [16, 52.25, 19, 64], "texture": 5}, "south": {"uv": [28, 51.25, 31.75, 64], "texture": 5}, "west": {"uv": [27, 51.25, 32, 64], "texture": 5}, "up": {"uv": [23.75, 50.75, 20, 48], "texture": 5}, "down": {"uv": [27.5, 48, 23.75, 50.75], "texture": 5}}, "type": "cube", "uuid": "db2dcbe1-4762-7ccd-49cc-b0e0b80feae3"}, {"name": "body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9, 21, -6], "to": [9, 33, 5], "autouv": 0, "color": 5, "origin": [0, 0, 0], "uv_offset": [0, 40], "faces": {"north": {"uv": [14.75, 20, 32.75, 32], "texture": 5}, "east": {"uv": [3.75, 20, 14.75, 32], "texture": 5}, "south": {"uv": [31.5, 20, 40.5, 32], "texture": 5}, "west": {"uv": [32.75, 20, 43.75, 32], "texture": 5}, "up": {"uv": [28, 20, 20.25, 16.25], "texture": 5}, "down": {"uv": [35.75, 16, 28, 20], "texture": 5}}, "type": "cube", "uuid": "4773c732-5858-ab2c-3e86-f972f7d84429"}, {"name": "body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 16, -3], "to": [4.5, 21, 3], "autouv": 0, "color": 5, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [0, 70], "faces": {"north": {"uv": [19.25, 26.5, 28.25, 31.5], "texture": 5}, "east": {"uv": [11.75, 26.5, 19.25, 31.75], "texture": 5}, "south": {"uv": [31.75, 26.5, 40.25, 31.5], "texture": 5}, "west": {"uv": [37, 26.25, 43, 31.5], "texture": 5}, "up": {"uv": [27.75, 19.75, 20, 15.75], "texture": 5}, "down": {"uv": [35.6875, 16.0625, 27.9375, 20.0625], "texture": 5}}, "type": "cube", "uuid": "5c6a5892-7cbc-4c1b-f7e4-ebc8b71dea28"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 33, -7.5], "to": [4, 43, 0.5], "autouv": 0, "color": 0, "inflate": 0.3, "origin": [0, 0, 0], "faces": {"north": {"uv": [40, 8, 48, 15.75], "texture": 5}, "east": {"uv": [32, 8, 40, 15.75], "texture": 5}, "south": {"uv": [56, 8, 64, 15.75], "texture": 5}, "west": {"uv": [48, 8, 56, 15.75], "texture": 5}, "up": {"uv": [48, 3.5, 40, 0], "texture": 5}, "down": {"uv": [56, 0, 48, 5.75], "texture": 5}}, "type": "cube", "uuid": "e7e60bcc-6523-31e5-4861-16f7dff3c9ec"}], "outliner": [{"name": "armorHead", "origin": [0, 24, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", "ac44c541-9bae-657d-3cf8-af029b473a71", "9954a7a6-073d-01aa-8ca1-10da91e67ce6", "b5728dd9-ec75-03a8-820f-bfbd3996cca6", "e7e60bcc-6523-31e5-4861-16f7dff3c9ec", "6744f9fe-1801-7f1b-5e09-dbbb09a22f47"]}, {"name": "armorBody", "origin": [0, 24, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "5c6a5892-7cbc-4c1b-f7e4-ebc8b71dea28", "4773c732-5858-ab2c-3e86-f972f7d84429"]}, {"name": "armorRightArm", "origin": [5, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": false, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", "c6e6e7ee-0e96-e299-754a-622d8cc00d70"]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "armNested", "origin": [-5, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134"]}, "241d7b41-317c-9e12-2ec6-4fdf23048182"]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806", "db2dcbe1-4762-7ccd-49cc-b0e0b80feae3"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb", "17134e6e-78f7-61a7-75a2-c6093f27ac7d"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_petrosapien.png", "name": "ov_petrosapien.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "921d203c-2c83-2d82-a23c-e250eb86d483", "source": "data:image/png;base64,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***************************************/2lrYRvmFAIYwQQBRYVOs0GgIQO4o9kSe1BHAe5x2MaQDjiCA2gjjfx0CEAAKAEm8wRyR3fl7976q6xrCxYs/y76HnP6X+3/9LQdy3LhtX3z0gtumhkFv/HM/HqoxsTYQ6fJTf5qQnTwQwJIAIYAa9GL7hABsjNZOAfLMt7YgAAggN4Zy7CGAHPS2tlAAefi9QoAogEwgE80hgETgdswggDz8LiOA1x2/faXH2L3/MgcN7YP4/pn7Fv5ZA1jCnEsAes5/5b59roh45KZbs3PIVWHhwtmNZwownAJAAIUj1HAHAeThDQHk4WcqgNiRP7QarZUACmCIFASQF8AQQB5+EEDnx7AQQF4AQwB5+AUJwDrxyKo2dGIRCqCsAggpL6t/5Dr7ANgINNgHIGsAEEBsCuWVy1UAEEAe/uwD2CFAeQwIAWQGlNMcAnACpoozBcjDjynAxNcAUACFEiDTTbJ5773ooXcBtBJIPdNQngLI3F+AYh/AEolcBcA+gOTUWxqyD2D1VmAIIDOwIs0hgEigAsWYAuThZ24FftvJjy9q8CoAOdtOj/wogGGHQQB5AQwB5OEHAUx8DWDjpwCtJPyBa7dXptqF585lpuA4zEMvA4UUgIzweqegfheglQIItf/D93xpAfC9d77HBbRlJ2sYH33qCZffuRXu/V2GPRBAmZCCAIY4QgBxcbUxBCBwaCUwdwUg962VQEgBWHP/WmsAmsAkgaW+VAUQskcBLJGBAGY+BYAAlghoAoEANpQA4oTR9ErpEXT/zVetvYlf+tUTi+uyBnDh/Om15V96/AeD66X3Aej2a8WSqgBkTeNbD/zFyvYf+8pD0+vsgi3ufZ5AszWAgpiN0hUEsHoNAAJYH64QwCjTOb1Rh+55e7pxhOX5O78ZUSq9yI1fvH+tsR7JdWFRDiEnT/z2hwaXUAB9TxRCAaTnykpLCGC58QkCiAssFEAcTpMpNXUCeNMn/nSB9cHt6weYy7cU9776zNq+uPjT42vtv/vnf4sC2IUABDCZ1I5rKAQAAcRFyrIUBOBBawJlp04AsgYgI75ALorAelpx4NAdC5OQfas1gHe++dDaaPn3/zpfNJpS64MAinZDf2cQAASwKgpDhAMB9M/Zoi2YKgHIyC9vLeqXZPTvWgnIyB9rL0qg9FMAayTWnZ2rBHLrgwCKpl9/ZxDADYtOsAgEAmANoH+2VmxBaSJo9fzfOrdA3lX4v/95eICe7Gw8cfTYWlSFGCAACKBi+vV3DQGs7gMIYLj4yBSgf65WacHUCOC6+z62wCH0JSMZ+WWE/6dHf3+A2+/d8rnF37qcFNLnHDx9+7I+1gDYCVglAXs7hQCGUwEIYBmRetERBdA7UyvVP1UCEDisD5uEFIAe8eVv/Y1DFABrAJVSbxxuIYBvDDoCAkABjCMzC7dCTjjSJxvVJoBQvam3J2sAU1cA0n7r+Xzu83+Nc2p9TAFSI3YkdhDAsiNkEbD3FAAC8CUGrwP78AqW1mcdvvzrLxbyvHRzxX+8fuCv9FmKlgKQymPfBdA332oKAAH4wg4C8OEFARhHl8mWYAhgiADvAhRKtKm4qb0GUAuH0H4Avepv1S9TgtDjP7EvvQ/AatfYrrMGMLYeKdQeCGC5MQgCWB9QEEChhBubm6kSgOAoX+yRBLbm/hp//XbgX19/48ouQgGwE3BsuVukPRDA8lwAebkIAlgdViiAIuk2PidTJwA5G1CQtc4C1D0gZwPK7/osQNYAlghAAOPL3SItggCWZwNCAKwBFEmoqTmZKgGEngLErgWETgaSfQDyDgAKAAUwtZx2tRcCGJ4MBAGwBuBKoKkXnioB6LMBpR9CJwHpfgqdDKQPAkEBoACmnuNr2w8BrD4PoNWx4FMJLhYBp9JTznZaBHD/0c8MPH7osQ+uraH2mYBSuV4DkN/lcZ61I1DvABR7pgBMAZwpNO3iqwjg6TviPux53enLPzAKAUw7HkKtRwF07lf9We/c5uy/+aqgCyGAv/rakZVl/vI3zi5+X0UAYvDS4z/IbeJa+9cdv31xPXQi0L13vmet/Yfv+dLiuj4WXBTA98/cN7A/8ienq97P2J1DAJ17CAIYdgAE0DYgZ0MA+n14C8bS77Nb9YWu3/v54Vw81c/brxvKdj2nvzTnP/pr73rFvVYBMvpfKvDYf359a5X97rZ98+m46UTq/Tz06pcHpu/76RUuV7H2j1z1yy6/uYW//cwzuS7W2r/18GGXfwjABVf5wr0I4NKdCAnsTn4IoHwf7/YIAQzxzT4QxDvy6+7trQRaEcAdBz+y9ce/M1zpDxHAp//lM1unn/2bAVT6qUFtBVA3DX/hHQUw8bcBIYBlMFtTAE0A66YAEEA9+kEBFFYA9bqqjedWCuDS3eQ8BZirAvi7F7/bpqNVLdZ3D6xGhZ5yWHb6+pMnT+3x2pQsnz0FKNmYHr4ggB6o/6JOCAAC6BqBPR4DWjfccx+A1bbS19928uMDl/r0YD1Se6+H2ttLAeh6Hz9+GwqgdFB5/LUkgN3turQmsPufXvQL3UPtjUAe7EqUhQAggBJxhI+JIiBvH0rz5Z0D+VvPtWOvy9uLY1EA8lVlfT8ogIkGLs0ugwAE0FkBlOlGvIBAHwTu/s7Zn+fUfOotR7rOwXPaXsJ2o2++BID46IsABJCHPwSQhx/WnRGAAPI6AALIww/rzghAAHkdAAHk4Yd1ZwRSCUA+1tn7bbzO8G1BAL17gPqzEIAAsuCDAPLgw7o3AhBAXg+gAPLww7ozAhBAXgdAAHn4Yd0ZAQggrwMggDz8sO6MAASQ1wEQQB5+WHdGAALI6wAIIA8/rDsjAAHkdQAEkIcf1p0RSCUAaTbvAnTuQKoHgRwEIIAc9LbYB5AHH9a9EYAA8nqAKUAeflh3RgACyOsACCAPv9lbS4J558qxdrkJHNsBsvf/nW8+NDCR32P96HIhf/r3kH8vrqntDNlBAKURnZm/2ETWtx1rBwH0PZAEAphZwpa6HZ2Y3pEq1r42AcgILx8EkbP5BKfSCkDOIpRvBFpKwItrqf4VPxBAaURn4i82gUO3G2sPAaAAZpIy87qNm888uDhrT07h9Y5UkthyCm7o9NtaBKBH/tCIrBXAj37yk7UdeeW+fYProTUAURyWEvDiWjrKUAClEZ2JPwhgdUdCADMJcG5jNQKS+CF8rHPsvfalFICM5HoEly8JyRd5JIFl5A7ZWfFh+dH1ij9thwKwkOZ6UwS8Cawb57WHAFgDaBrgVBaHgE5ka+S3iCBk/1v/9vnBuf7Wqrk1Z7faKfclI7FWDLH2ekQXP157q3xcb6WXYg0gHbtZW0IAcVMkTSRWQufiWjroIIDSiOLPhYBOCEkovYouTr0jdqgxuYmYa+8CqWJhCKAiuLi2EYAAbIxqloAAaqKLbzcCQgihVXRLYrsr3HADCGDDA2Bstw8BtO0RCKAt3tQGAqNCAAIYVXfQGBBoiwAE0BZvaotEQE8F5C2+3jvnIps/mWIQwGS6arMaCgG06W8IoA3O1BKJgOwMDL2/L25QApGAGsUggDI44qUQAhBAISAj3UAAkUBRrC4Ckviy00/2AegTfHQrSiuB0NSjdL2xR6bVRZ1jwWvji/9IBCCASKAKF0MBFAYUdz4EQokvXiwFIOXk5KHYE4xkBNbnAYjy8NZvKRFdn7z1aNn50PSXhgD8mGFREAEIgPMACoYTrkojkDpXjbWztv5aI7Ie+a37198HEPvQuwdW/VJf6LsDuj1acfR+twEFYEXMhl+PTWQNU6wdBHBb1xzsWvmG59aobz/2VN/QTcQeKvqOB+5enAikz+wTv6Ez/uS62MncPxZUfWpwav3Wqb8ogNgeodyoEIAAlt1hERAEMKqwpTFTQ0Cf0We1XyekPmXXstdzdvGnj/sO+Sld/yM33dpVhXetPLazKDdfBCAACGC+0c2dmQh4CSDk0DpN2JqLxyqA0vWjAMwQocCcEYAAUABzjm/uzUAAAoAASJINRgACgAA2OPy59dYEEPqyUO4agPRk7FqEtIM1AHJgoxGAAFAAG50Am37zoQ+DeHHxjrz6eb7U51UCeh+Btx0oAG9PU35WCEAAKIBZBTQ340MAAoAAfBFD6VkhAAFAALMKaG7Gh4AmgNS5eOwqvD6BJ7d+PYeXl6hCKOinEKwB+OKF0jNDIDcBNRzWIhwEMESMl4FmllBTu50QAdRSArEEEFt/rALQI7/4RwFMLWJpb1EEIADWAIoGFM5AYDcCek5e+xTe1vXl9jZTgFwEsR81Aq0TsnV9ueBDALkIYj9qBFonZOv6csGHAHIRxH7UCLROyNb15YIPAWQiuHfvqxan2vb6d/Hiz7r2ofXc28JFzuW3ysl1ffpv7Tm91a7c++/d/q7BY4E7hesQwNksAoQA+DLQFPI82EYIAALICWAUQA56I7CFACCAnDCEAHLQG4EtBJBHAKEdcqGu1e/x9/62nnzc1NqCHLofCGAESZzTBAgAArgUPxBAThZN2BYCgAAggAkncG7TIQAIAALIzaIJ20MAeQQw9ceArAFMOHlLNB0CgABQACUyaaI+IAAIAAKYaPKWaDYEAAFAACUyaaI+IIA8Apj6PgDeBZho4pZqNgQAAeTEEhuBctAbgS0EAAHkhCEEkIPeCGwhAAggJwwhgBz0CtjmJvCBa7dXtuLCc+cKtK6+i48+9UT9SkZcg3cNQt+K3gIs/lK3BteCKtSujT8PAAKAAHKSDgLIQW8EtqUJYCojv0CPAjifFYWaAB5+7MsLf289fHjxf28lICP/t595ZtGeE0ePDe4XBZB5pJeeAkAAWfnU3Lj0FAACaN6FeRXmKoC82vtbH/vKQ/0bMaIW6PMGdNOu3Ldv8FNoCiAjbi8loEf+UDtQAJkKYESxm9QUCGAIGwSQFEbTNUIBoAAuRa+V+CElIApARlzx88Lzzy9Mrr7mmoGpKIjSawO6fqlUt0PXjwJAAUyXvQu2HAIoCOaUXKEANlsBeM8Y1B8zlRFV/FhnFIbsJWcsZaAXLVPbL+1GAaAApsTXxduamkDSEAigeJe0dYgCKKMAvCNX7V6ObU/syK3bq0dya+QP3a+lCPTTBC9hWfWiAFAARXIxNuGKVBbhJLY9EEAEmHMuggLIUwBWounYyd14Y8Witz29X8axlIVexU9VGiHcUAAoACun1l73JhwEsB5uPSWAALLC0zZGAaAA7CiZbwkUAAogK7pRAFnwdTeGACCArCCEALLgu8xYzhiUl4pkJ2Hpub9UDAFAAFkRDAFkwQcBlIXP7401ANYA/FFT3iJ0urB+vfiRm24tOmgXdVYelvoeIQAIoH6U2TVAADZGVUpAAHkEIJ1iTQVqP/7TwRHbHhlRrQSsNRe3viug1wJk67Fud+p+BhQAawBFiDU24YpUFuEktj0QQASYcy6CAthsBWARhYzAkgOiBPR79dYILCO9fA353ds3RKVVbP361N/Y9qAAUABRgWgVshJprFMAq92xCRibcBCAFUmNr6MAyiiAxt1WrLrQqb56xJe/9dZcGcm93wMIEUGIcEL1y++xJw3pdqIAUADFkmmKjiCAKfZawTajADZbAcj79qERX58CrM/8Sz31VxSAKIrU+kNrElaKiBJAAaAArFiZ9XUIYNbda98cCmCzFYA+YUeP+KEIErvYubf2EzrFt3X9KAAUgM2SMy4BAcy4c2NuDQWw2QogJkZWlSmlAHrXjwJAAaTG4EbbQQAz6X4UAAogJZQhgBTURmgDAUAAKWEJAaSgNkIbCAACSAlLTQDiw9paLOVyt0anEpCulzUA1gBS4n/jbSCAmYQACgAF4Anl0EdEvfsBUhVA6LGlV3mIHxQACsAT/xtfFgKYWQiUUgAHrt12IXPhuXOu8rUKX3ffx4q6lr3xRZ0WdKa3/nrbCwEU7IwxuIIAIABPHEIAHrQmUDaXALwjv4aktxJAARyOitJQ4uvz+q0z/krN/aXR+t0Bay1AH1zCGkDmGgAEMMwfr6SOyr6ChVKnABBAwU4Yk6tcBTCme0lpy41fvH9hFntGndQh77PL3/q99pS2tLTR79GH6g4lvpSPVQClR/6QApDfQ0oABaB6GgKAANYRDwTQkpY71AUBtCUAGXlDXW2dkOO9Hqqn1Tn/1mGhtUNer0mgAFAAAwRaTwEggNopP/QPARh4owDaKIATR48tekKvHeju0WsRunzsdX26rq4HBbBEhKcAmU8B2vJ5+dpaKQAIoHzfxXi0FMD/AxQL/meJVy7cAAAAAElFTkSuQmCC", "relative_path": "../textures/models/ben/ov_petrosapien.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_pyronite.png", "name": "ov_pyronite.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "2fee0005-e682-8e70-f99c-413d31ecb6cf", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAIABJREFUeF7tXX2splVxv3dZEaikuMvH2ha2rhJRkNiWhDSNRYiRNKmkbYoCrakmEKGJDSXVVFJQlwYba5DUP9BIUozKqjT9wCYtxkioqcYGq0FQiLLtQikfyy5rl8IWlt3mPs+d131+7zPvb+bMed/nfe+d+8/uvc/5mDNnzm/mzJkzZ3lp4J/7th9zeEgSHrjtuCG7X7p0597lQQnIztc1BwYXvgSABIB1vQIHHvzgAPDEXecMagFcc+mDg07B7XufHXwOBmVAdj4oBwYXvgSABIBBV8A673xwAHjp8PZBLYA3bP3koCLw0CO7B5+DQRlAOr/7kuWOfPzs6Wf01vjJj1pL7vwvHk5+OiZ0cGYlACQATJLXBADHai4omgCQFkCB2AxX5ZEvndlrMR58bk9D1K67nuwQlxbB5LlKAEgAGG41F/ScAFDAtAlV5hYANix9sOpIDy19tLe99AFUZXM2tmAcSABIC2DBRDbJrcmBuQOA2pofmYWWQFoANcUp21o0DiQApAWwaDKb9FbkQAJAAkBFcZpeU+w4UOIAhIL0/tvmIgEgAcAmKQOXSgCYzgQkACQATEeyptTqzr/e0sQBbDxuc6cHiQPY9p4nBpfpKQ19Ks0OziyMBEwn4FTmec00mgBQdyoTANICqCtRztb+/brXNxp92nt42ULgXYJfvuGHg68BJ8uqFh988GkBrO+7AAkAVdezu7EEgLQA3EJTs4KE9s5qD4+hxKe984HB10BNfnrbGnzwaQGsbwsgAcC7ZOuWTwBIC6CuRM2oNdk6SHeZD6CM8QkACQBlkjNwrQSAOhOQAJAAUEeSZtSKLPwTz9rQ6XG97+VL2Z8AkABQKjuD1EsAqMv2BIAEgLoSla0tFAcSABIAFkpgk9i6HEgASACoK1HZ2kJxIAEgAWChBDaJrcuBBIAEgLoSNePWJMY/7/+XMT4BIAGgTHLmpFYCQGwi5g4AZDi1rwVnVuCYoMxbbVn4Wy88pSFN3gNIS8A3UwkAaQH4JGZOSicA1JmI5aGf5z7zums7I/nya26eOLJ3PHz1xO/e+g/ccGMdTi5oKwdffHVDeWksPV7nZRo4GsKLC//V7360of8/bju1+deaEQjzA+z78eNN/Qt27JuoFLGeN58A1i/le6m4Cf9lvAkACQAJAEtLS+sWAIZ+nvukt13UCKCmub+y74UO2H1uzwcmgt+7Nn+s8/3tJxzdW14sid1fvbMUTNdEvS0X3ttoPEm1hRqUOdlKr/NiPe8e/vDhF5tMQof3fqmZhw2bf9+0nUULQnILWu8SYEoyaz0RFqyPbxoyCyoqdML3/9u7r2lqOQEgASABYGnJupDXHAAM/Ty3IJpYAB8+eNREkPvwxpea75c83H0F9ouvab3B1vrMlxBF2kWpf9Ty9RM1pwi8V0Nbxy+a3LuHP7Tn840FYNX8qIHFd7C8/DKT5SD1hV6tntVHIPQvb3pn07R3/Fb+Yjnk23ICQCkr10a9BIAEgN731md1Di/9WDX4rzz7fLPycG8vvoLvvOJYlwWhxQfMavwasbPqnwFAqYa2wiNqQq9GtvaDGtzrO7D2490izHr8OA7VApiVACYA9IvWrPifAGBzHq4bAKgteGN7kKWPdv7kBQCpLJaA/M40v5RDHwJaALMeP/Jn1v1bAUDoZBqanRpYF9K8lPOOp/R0go3XS4e1/JgFMGsBTADoTv2s+Z8AMHnpWRfStLcYXjqs5RcWABhiat/TAuhaYAwAULCZBTDtU4PSeS+tJ+PZt/OVTRMs8o+dEnjpkMi9E7Y901S1Rjpa5yEBQNmSeCfKWl5zOkr9ebMAEgDax0gTAKwSTsppe245BTj/lm29LZx3xS4XBfd8Zmtv+buv2tn8XeII0gcwOQ7AxfSVyLzVCL1ZnWt76fOWl/E8+uU3NVWtAUPefqQ8xhFI9uNT3/G9pgizwBCw2TzMnQWAAT4yoDO2/pyLpw/u+u/e8gI0CQAte6xbACvzEwCsnOovt24B4Mmv/mrDkVPe9q1ezsh3ZgmI5mftyPe0AOpaAKiB7r7spOZP7JZdbNnUry0L8S072rso04ob0CiXmP1feOsHO0WskY8YX/Dd689u2kEfxtxYAAf3f6Yz0I3HX9HLG2YJaJpfaz8BIAGgT9ASAOqDatMi8wFIt6VbAWb6Y/sJANMBABSfr196QuNMWxRLQLzov3jRx5uhWDXvlJbN0uEnrm34t7zlRtfdBXZnYm4sANmbCwN/93t/3vwXLQGvBSCa/2/e9GeduUkfQMuO2j4AbQEkAMSgYeoAwDLpIPmahmbD1I7BpH+J6ZdYf+xHAEDO8/H2H1oAAizYrnYbsPQYDgGM8aF2/6w/+Y78nxUAaPRZA1as47OW0/oVoDr/9t1NU1avu7XfWuVq8W1kAcwrAIglIIw766wPNf/VAOD++z/S4bFo/gSAli0JAC0fEgBaPizv2Lap9zagF6msFoFmAUhuvgduO67pGhesRg9aAAIMUh7bOfPdzzWfMBehlLdaAF6N7+WnlLfyVWuf0Xnpzr2uPWXpOLR61oi1Wv1iRiBrZF2t/qPtaPSXWgQJADAjCQBREfXVTwDw8WtmAMAy62j38pnG0iwAyc0nkXoSEXjNpQ82HNJy+yGdeEvwph1ntCbfagSgtCu5CJH9DABQo3rzELDpxhyIOG7GX40+6RfbG9oCmHXgkABOaUYgNn/T/o70y/m+966A0KlaAEMDgAj6721uL2GUAsAX9rSXKDDUOAGgFYEEAF9GoGkvcNb+3AAAEqpZBFiO5eITX8Abr9vfVJUIQNHgGoMQsDAi8Ps3HN9U1fb+0i5zhlo1PtKJvgnrONCi0YBQ2kMLAvMkIB1DA4DQLZaAFrHGFgb7Lrfqfmn7fU3RefXus3HIdznf/6+vtbc7S+8oFFsAswIA6Yc5s9hxYAJAy8kEgASAI9duNQCQRlFjIVCgBkOLQHwBGMsvloAVIbX6aPqjxkcNiv1ZMw/VsgA0oNX4wOibVwCQ8UgM/NP3H2r+xO7fM3kQzS+36ko1JetnUb8vDAAIgzGmHxmv3SEQAEkAaNOqy8+8bAESAIaBkDEAYM6/KJlsTysWgeaNx62A0Ku9FyD0yukD0/hMg0bHr/kCps13zSKZNwCI8jfr+zgwtwBwaP+JzUhEo2sx/QgAeIdA6m04/ummvQSAVkAEiBIAfAtmrZUeAcCsNZAwUrMItJd/pJ62V9fuEGhOxKHGjZbArOlIAFhrS7lsPHMPAJrmZwCAlkACQFdAEgDKFsxaqxWOA4/eJZDYfGEsntOjL0CO86T82dcfCI8hMqky/qE0eLRfa3yCxqOhtxC15e/pHx7jEodFyW+gDSq8eGpPQAKATf6025C22j8tlQDQXg6TnwQApwRFAUDLAozHdRIfgOTJ+/ZOsqsVX3QLgMVtMEbdvvfZsBJhfUz6Xlv+9u983EXO6Vc9Nuj4XcT2FA4TX3sChMYEgMlTW8sCSADopqFPAHBCShQA2N0ARs7QGW0W3QJg/GXfH3pkd1iJsD6maQGg/G148oCLHG+OPlfjMygcnrwEgDahStQZ553rWhaAt18snwDgS9IZ5Xft+gkAQY6mBZAWQFCEBq2eABBkfwJAAkBQhAatngAQZH8CwGIDwCXfvLIjAS88tcclES8/+9PhNeTqsHLhMPHpA0gfQGWZdDUXlb8EABe7xwtHJyBPAcomIJ2ALd+i8pcAUCZ/o1rRCUgAKJuABIAEgDLJ6dbKLUCQi+kDWGwfQMYBVFoApc2kBVDGubQA6lgACQBl8pdbgFUOpAWQFkBwCQ1aPbcAQfYnACQABEVo0OrVngZjobB3XN7m+Zefi29t8/RHr6NKe5hXQN4YnBV32fhL6RC+Cb+kndKMQrVzEtaav1L+aPWs8/Hgv13RacIbB/C3v3VHiPSh8ykkAISm76eVrQLn7S4BwMuxtrx1PhIAVl8HZvnwcRqsT3VhPXmx56lv3998wteArdMt/YvmR43PxoP1rf16y6Hm9tYXfp13xa6mKlpSMm6rwGsWxOjvB49ykei9TqzJDZuvWvKH7UQB4D1v+YKLX/P2NuPIApjVBCQAuORlKQGgn1+lCigBoMuB4qSgbO+nvdorEyeZgMQS8C2Ln5Y++dyzml/k7UDr231Cv5aRqJQe0dRSHzW2t11JkSZvJsp4pZ0o/7z01C5fy4LxWkAyL+ytSDZemRerpTdvLzMlANzSzQjDJpx9TwBgHOp+TwDYGz6J83E8aAEwzc/2krJn/NyeDzRFtVx/1kGJ5vdqABlHNBBJ6JQHR9jrvNZxiekvqdGET/I7PnBSOn42X1Z6veVmfYqh+U6sdGtyIvOClhizCOYlLbvbAkgA6BeZBADrUmrLJQC0bzQOfgx43/ZjmpRW7Ied20c1kFcTezWgthe3xgto9OHCx7cFvV5ymQfNySV0sCfO2HzW8sazfrTvpU48FsegzTP6ipAu5gQXepklIO1qvhlcRwkAqxxLAOiKZAJAP3QkAJRCbn89ugXAYzute7bnwXrWrYR1uJoFIhoBvefiO7BqAk0DvGvzx5omar8qrFkO4jtBC0DGIXyInj7UmmetHZl/jOC0zrdWTuYZnbHSn2h6BFhmwcp8aMCM9GinS2gZDP2yVQIAzBx7cxAtlQSAsiWbANDybW4AgAk+PsONCMf2PCMNtbF1flg1r3UPqWkS5rsYs0yUSDimAZgGKVsm47V+sOt9zR/l/LlUUzNTWtoVC1B+l3n2WnzMAvTyT3MioqbHfsVSK3VCaq9Z4/phPo65OwVIAGhFRRPEBICWPwkAz3cwRRb6wgMAE3wZNUM21BwoMGzvb9UEuMfV9n7aXtq7B5RxaUBgpbvUEsDINTl/1uIg2OmD9RQAfR/SL+6xveNCOWBeeGwf6ffGX5RaAJolo0W+Mgt28FMAdp9dM3mY6YzOQ6spbl1ICQB3NlOQANBKYgKAF4Lb8tQJyPZu8l0zgaxeaWtIKMbwozffKghRDRCt750uAVTr6QU7lbD6ANDiESfoG7Z+0juETvko/2rXF+KsCmhUftWnVVpv7i2ABIB+OY8KoHf1JAB0ORblvxUA2TxJOwsPAF4EZBOAWwfUSJp3mTFcvmv3/5nmQ+T2jttan53DW73o0g7mTSj1YWj0Mz7gHQ7vLbjSfq0KiNFvbad0IZf2PzcWQOkAtHoJAN0UaCiACQCg0Y2JSGo5kWtbAKXrZ+4AAM/TNUFlFgAKfKkzUbMIvP0zDYDIzzQ4s1S0UwmpZ21f2rnm0gebqto5dqkAjvGFLET0wXhPA3DeakcCWoE2CgCsvnV+5yYQSBiXANBywjqBGhAkAPRzJgGgy5e5AQBZ+NaMM9otOuseSkNQ63mwNR6BWRCaBkI+aLHlmuZlEWk37TijQ5qmSTUnE9NAzEIprY++AO00wAqgKEel8oP1pn0MrfFPW0cyHzLPcxcJmADQXTIJAP0QkgDQ8mXNAIDkA9ACdSTmH5HLunfT8tmX1h9pXOVOAdMg1qy6LFKNRaIJndqphMT0Szm5VKRpbmynVINr/GN8Q7rYnQR2Kw/bs1p+Uo/xX75jHArL/2Dlg7aVkXFrmZvwNuLgTsAEgONaRAfnVwLA5E1EAkD3Uhta0AsDAPi8NyKx3D9HDcXOoVkeAczQIgswGh/A9r5aXgCmgZgGtmoOoQ8tAG9k3bxYANrdAM0Hwnw3Vt8BzjNamtoxNGvfezwrdGgZh7TI1LnzAchAEgD6ISQBoMsXAbAEgJYvawYAUJOhqccu9eAeR9PsaCJpmVqsefu1SzFoUUi/Is6SWcerwUvjEPBWX63+R3v7woAa6/g1+jULigGn9qYjowfjEdg1ZXadW/hn9U1pvgTvXZTBfQC4BUgAYJuIfi8wE1hpNQGgf+8s/Cm9FJYAYJNbLLXMsgLLyyla/nNtL4Z7MPElYHnUgNZ6WjvaFgY1vpZd17qQNQuAXQ7BfP/elGLTCoW1jrs2/WLh4ctOTJzRl4CnVVp9ZglY+aA5idldFPRBDB4IlABwc0dWSgVA6iUATF66uHASAA7Mx8tA2l4b98z4ko/EB2hef/EZoNebZbVFb7lWXrufrpna0m5tCwDFXgMS5IP3Vl3U+z/yFUAchRX4ap9e4DxY6WCBOMybP6tIVC1xjczDlgvvTQA4cvFoPogEgH7fg9f5lwAwmY/sOFj4x44z0ecjv+Mx9NwAgKYRUbNhWmxcmFq2YPEl4Es6WjwBanDZK2v5+TFLLkZkafW9LwxpC8i6EEXzCB+9vpVZWwCowXAerRp7zEJatUCED9Ysx0xzyx0L5hSUdqKnEOzUQIuEFX7MzSlAAsCqZig8RksAYG677ncEwgQAH/9qlR6dAmjvpGMEIHrzZeK0+viqrRAukW8aEqMGx/JiaeDrudI++i5QwFCTeRewNgFMI3qzCrOIylqCoLWDMf0Y4ce83iqfiA/CGhGqaVi8A6DRgceOLFIQ20GTHr9LHgf8u1iyg1sAcgqQAGCzAKLHcAkA/Xtw7TqvLBzvwzMJADbVsPzEXec0rwNrmhQZj0CBpwKoeZEMbc+LSGrV4LZhLi1pdM7aByD0srz9Ug5vtc3aB6DF9Jdqfs2HIs437e1DPGYVCxD3+shXLe8CO8dHC0Toxlux8nftcVu2tRk8DiABoE4cgHULkQDQbwEkAFhVWd1yyy8d3t5YANqPV8NbyfO26y1vpYMhtLUdrRw7j2btW182knaix1jWmH5Gt/ad7bGFX8xCwtMjzRuvbW2FPrQAEYi0cXhPldCiEEvioUd2DxsHkABwY6ksm+olAHTZlADQ8mNhAMAk5QtcCC2L2kNh59Gl/WkZaUrbw3pR4JL2WCSc3AGQPT1mP9bGo4UUY25FjFx89LNf6jT5P4883NsFO9XSfGMY5yKNY/yKWBC37322sQC+++E3dizxZx56tEPXCa99Vef3n/yozRItP+d/8XCRJUG3ALUEal7bSQDon5kEgGt7GaM5sbVAt3UDABuePNBh2KFTjun8zr4PBRBaiHGUHoz08+bP1zSoRhc7j/aOJ2q5iObX4giEHvSe3/eROzqkoiZ85etO7f1+3u3X9A7x4Pcfmzj0Zx7/Tu9362mWVJby91x2U297SJ+Uu2DHvkZz/+dtZ3YsgBef39dp5+WbTuj8fvC5PZ3ft73niWEtALbA2XevgNYqnwDQz8kEgIsmipjmlE4AWGXbolgALBtvKdDgns976w81qNDB3g8opRc1Mpq61i0B0q3d8xf+nPP+Y5uut1353uZf3KOjJnzZsV1NKN+l/s5PfbrDgtN+7TcnsmTjG38+yrJOfewfG0c6T7/qsSLNXYvoaj4ApuHZ91oD8raTANDlmHZJKQHAJlkLBwC2YemlMKWYt72hY6G99Gb5+eJAyl9sPsLmR05AbAKydowDKX8x/iUAxPiXtQfmQAJAbAISAGL8y9oDcyABIDYBCQAx/mXtgTmQABCbgASAGP+y9sAcSACITUACQIx/WXtgDiQAxCYgASDGv6w9MAcSAGITkAAQ41/WHpgDCQCxCUgAiPEvaw/MgQSA2AQkAMT4l7UH5kACQGwCEgBi/MvaA3MgASA2AQkAMf5l7YE5kAAQm4AEgBj/svbAHEgAiE1AAkCMf1l7YA4kAMQmIAEgxr+sPTAHEgBiE7AcZaB0z97Ew/znrzvtpCYHGntqyzo8zCtQa1zW/qdVTsa1VsYzLT5hu0wepXw0L390XobOh5EAMCuJLOwnAaCMcQkANr6NAMD6oow0iy+zlFoCgqCl/WsLhLU3q9dZL9v0iokvL7Fpkrzx0o78LvWi/MP+Gd8Yvey7JjfefkvlD+mLWgDe+UW658YCmNUEIMOjApwAsKkBmFrz522HLXj8ngBwdIclcwMAVpNppOnhfXe2B8NnsWsPHIGEvV4rvofadIhvo9Ye07vAFq28zFtU/rz1o5of+Yzzrs0D+rxqy593/kdbAC8DmfMO20sA8E7N+iifALA3fBIXkRQ3ALCFz3wBAgS4ly0dRFSAaiGwtpXRLJFb/+krnSFf/htv7/zONBR6n6MA7q1fOl+aBWntX5M/Vl/belrH4ZUTZhFMywK1jkfKJQDsrIPAVgDAhY8TJkCQANAvygkA3iU+ubzZ/GDn9gyBx3wE4EPwIqxXA2oLynqOq9HHfA9HvnN/1Te+NmLDN57+4w5L3nziJ0a/3/Lmty5pzrJSp+e0nHGl4ljqxWcA4AVOoZ85P2udGuE68sp9Kb+1egkA21ovOvupBQAXvOnNSx/52jm93X3orfcuff1731hKANBnIwGASarvO90CMESV7tieh1kAPrLHS2sWiFfza5pA0wByDsxOHVZM/5XFLz8IAiuLX34EBPp4Ir4TzXIRPljnzct37zxr7U97D4x0Sn8yv2iBMAsWndiMb0NrdkaffE8AAE7NCgBWuhUQOHLxr/w9AcAqvnq5BAAbD2kkoCClIKQXubWJQPLYHgzLWyOqmO9izDI5eFQv55gGYBrk/X/18aWrLr6807YGALfccevSX/7Rn/TSYdXsTFMzU1o61/pj7TPxw/4Z/5gFifKpyZNYaqX9H+nTWaFJ5BD7Yz4O7zpi/Cz9ngAAnNMEsTYATNoCJABwcdYWMJr62FICQJcjdAugIR7TnKW3/6yawKqZkH6h27sHlHoaEFjoFitgkhNQW/zMl6EFXmlLyRqSq/k+alsAUQsQ6zOfTKkFoFkipfwf2leQALA6o5YFvFI0CgAiQE+98lMdWTr5mStHv/eZ/wkA3aWHAJYAwK2mvhIUANjeS75reyCrV9oa0Yd7J/SGWwUhqgFK669YAZN+2N5f8/5bxy19W30AWgh3bQvACsAa/dH6o3YVH5A2Z8LH0v7n3gJIAOif+lIAWFlQK+YpAsHKwpdvfT2KBZAA0HKnlP9eAGR6dc0AgBcB2QTg3hv3ZFZvtjYBmuZne79paRDUAGx8fffI+2jHhV96jm0FcuYExTscXkvAankwjat+N2rwKB1ROZq7U4AEgP7jP6sgRgFAA64EgO4MsMtoVlM8AaDl61hKMNyTaAjPLABcOKWnCaUL0Gq6acDHNDhrX75r/PO2jxGHUQEurY+aK2oBTHsPbJXfqALE+t75tcpT7XIJAKsc9Wpw60QkAEzW4AkAVkmaTrkRAHgngsWiM3I1DWQ9D2aRVtb+reNmEY0IICwgxbqX1pxMpRo86gTDfA5RgPPe6rTuvacdh6Lxn8nT3N0GlAlghOOCSgB4qcOSBICuhFhN4ASAOvkomMLTvo+uA2sx8wIMpcilaU4r4NTew2mZYbQFLIxjsd4s9hwnAOlg2WXRSVjbArA6z2Qc1gXOLCeNv0ygWSAQ3mFhx6hRHwC7Lo7jrJVfgPGJfU8AWM0HkADgOwVJAOhagAsLACySDvPSC6KwWHqrgGAEoLUeQzbrd2tacqaBoxo06k2ParDa9Gs+EOa7KZ1/5J8Wh8Lat84DC83WLBu8jGS1hK3y7C03dgqApmwCQMvSBIDJe3x2/15THAhcbIFqAp4A4F36bfkxAGDHYex+PUaqsbsAqIGt9/w1pySj31qPsdMbB6Htna13IJCeoXwAmg/F6gNBulEDWvmhRdJpGpxd5xb+WjVyNCR7YSIBvccpCQCTocPqhPQC0Ky2AAkALafXDAAwQfN+FwTGPZj2DgAivrWepsm1LYyUx2PP2sdQ7HKI5v0vvcMwZhkUxsJbfQC16ccsx6VxIHhapcktswSsfEBLRuhm81i6xfGuQ2t5c1Zga4MJAK13WBOk2gsoAaDlQAKAdYV2yxVHAkozsuBZhh7NdGQCzM5vtYln/Wn59Us1ABuH5gNAPrJpjO79R1sFeJfBOm5tS8jo1vrFebDSwSLxmDd/VpGo86bxcZ4SAOBdgKgAsr04A0q2kBIAWg4lADBJsX0fAwD2Uo7mtZXumBeVef21UwOJlCuNoNLq1/IBsIWPGpDxCaePnb5YgctrAbDzbm+/pXxg45c9uPeORe1TCG3emAVkW671SyUAVLYA2IIoPf5hC4D1O7ZFMW4BEgD6F511HlkotFcR1IYA6gTEGPVar/p67xZ4y1sZ5bUAogkpvElF52UPify3er21eWBxFNZxawuMxZ8IXdOORNXueJRasla5tpZLAHBaAAkA7SlHAoBtiS08ANiGubilvBaAtpez+gCknPZeAXJSe5lpzKQ3nv97fQBaTD8772YSoZ2ja28fikZnEYAsjkTbSiGgMWcti2hk45+X79QCmBdCp0VHAkDLWc2HkADQlTx2bX5acjqtdtc9ALDzYsZ4tleNtm992UjoLI2kY/WjGl/joxZXwCyk0jsjSId2G5b5urynSto4mfww+Yt+TwA47aTDESayCUwAmMzdBIDdg67BQTuPLLxadaMLlAGA0BntZ2zPbzzGq8WnabVTGhqt7cGRz9b5qTU+Lb8Eev3FgmCWRi26tHYSAKZsASQA+CwATH+u1U4AqAMNCQBBAGAhqVEAYBqstmVRR6x4K9ZAGt5SlohwIAEgASAiP8V1EwCKWVe1YgJAEACYl9760hLOKjuHrioFAzSWADAA03u6TABIABhEEhMABmH7WKdhAGC3B9kwh74Mwehj36PjP/Pdz7EuJn4/+/oD4TksISA6bunzlHMPubo/8fUHOuWHGr8QEeXD0PIfFp5FZ4BL+noKR8efAJAAEJXBSP0EgAj3JiSHtDZ7/i3brEV7y2258N7wHJYQEAU+6fOc9x/r6v74ba/qlB9q/GkBrHIgKghDm0Au6ZuCBZAAkAAQlcFI/bD2SADYFAolfsfDV0fmb+mo5evDc1hCQHTepc9Lvnmlq/tDpxzTKT/U+NMCSAug4UB0ISQAJAC4ELBy4bD2iC6A9b4FSABIAKi8pl3NJQC42DVeOAqACQAJAEERDFVPAAixL7cAQfagwMCNAAAKu0lEQVQt/c7fX+xq4uiTN3fKL2+5MSzDLgKgcFQBDG0Bh5m36AyITH76AKLcW0oA2Lk3vAYjsxDuPAEgTwEiApgWQAJAGIQiAhitGwXA9e4DSABIAEgACKDQUOfgUeDLOICWA+kDGHgPFFh7GQcQZd7S0lIGAqUFkBZAYCGlBTBMJKRMWdQSSgsgLYDA8l/KUOCBQqHXDABEEUx7g43lqmNPbIVWxRGV2WvG0X5k/Hdcvr/T1MW3Hj+x6VmNH68bP3DbcR26oteRtXHOanzR+Ytq4HmR81I+jF4HLm0gAeCohnUJAF0JSgBo+THvfBgBgPVFGQQKzGuOr61qwKK9DceACF+EYXSzV1i1PO6MDvn+p1cfbP578rlnNf/efdXOduLJW32zHr9cO37q2/d3hiZ0W8eL5c67YlfI0mHzh43j/ON3a3tMLqz8YBaAtMOAwEq3tFeLDwkAq68DeycgAaDlQAKA7WWpuQUAK9Jp5bS31dhbcux9eKvlwDStZO3VEP++7ceE7vPjntr6bPZQ48cEJCe97aKQCDxww41N/VKfB5s/JE5bSKwdqYc+j1o5BaOWAKO/Fh8w23T4CC4BoOtUSwDox5PShVtL8BMAWk6OAUCt57Gtgq/tiawIWKo50RJQLQyyd9foX9TxR0ORd3/1zoYl6Fvwng6w+S8FEFz46POYVk5BZhFExzMGjERutXcmxk4B2ESstQXgZeRaG38CwHSSqi4cAHidYOiFtAJHbQtg1F6h5pb66338IUfAEZW10waxCKat+TRTH09pao0X5c/6otO0+aDRgRZ/8SlAAsDRHRladACstSASAHb3+tXQIpg7ANACeryCMS3TR7McmAXAXtfFuAUM6PGOf9recK/gsPH8xc0bXUNkcRj4XQMEV6cFhUXjyzHltI/hxIKU8ddaTwVD71SR9SjjRzpHFkAtghMAykKArRZEAoBtSSQAtHyiAIDsZAtY08TsFVwRXBH0Um++tgCsMd3IEDwXZhpcGz9qOmxn2uPXYvrR640a0Qo8Vt8NvpasWQS2ZTxeSiItmeWHGk/KaxF0tfigjb+2gmXrRxu/xOfI97H9SgLAZA2eAPDSRN9HAsDzHf7U3hKgAtOA0AwAWiAPQ2gNSUUTW50eVs3N6NEsGS3mXqPfehqg1RdLAENkNcslehtPxs00vZTDPSDTpMhXpnmwPAME77xieTZfqPFYf1ZLwMqH2uPX5k/jA0bkIt1jcQCMocyUSgBoH/tMAGglpfYCSAB4oWEBc8YKn9wAUIqAAhxyO1CzALT7+aX9aj4FhoheZxrThNLf5/Z8oCn6hq2f7JrKG1vTWbufP/T4kY+aZYJ3H6IWBFomTEOX7uHZKQCOg53nl8oPi0hlCphZNEyOqAXAGhgxalWgcQITABIAJi1ibeEwwWeWJ5PbBICWgyoAMAYyDej1ctbyPTBNrwmjdQ/nrf+DXe9rqsgtOayvRaSVLoBa40eNr+UJkJh/tASs8lOqObUIP2Yx4HfNgin1RdW6S2O1pFhci5UfQvdYHIC1AW0BWQlMAGgTh0RN4AQAq8S25RIAWj7MDACspwGlGsRaz2rBWMVJA0CxAOSWnNUZaB1H1HIZaRrYwqEPAC8Jffk1N/eyxhtHEdXkXstD+sM8CGLJSD4AjAhFRYbfpV0W/6LJk1VRYn3rMSDWo7cBrQLIBAg7SgDopswqNYEZ363AxdoR+hIAujH9CQCrkmONxGO59kovE0U1YO0FKAtKNOGZ113b/Ek0pmaiz8v4BfjROy0AgONg8Q+YF4DtwaOKp9R3gnRhO+LM1uQY+eD1IZT6DrRTBMYHbd7cPoAEgG4kXAJAywHrpR+vCc+2bkzwpb43iWYCgGJTIgBoyHfZplc0ufa03IClmnxaGtyqibB/0fziNZcce+/a/LGGg/M+fsxkhFsAHAeLrNQiIbWth3UBawvZOm/s/B3bka2sJsdMEbKQeo2PbCunxQEwPmgBWWELIAHguWbOEgDa/AgJAO1bfwsDAAxxvN/ZuwBDaXDNCxrNCowLX/bMGiIPNX4tL4B2a1F8AFpE40ijr2Zi0iwHlJ9S3wdzXlrl1Mp/kRdtIWsaXLYOYjlodM0LH8JZgXGACQDtcVkCQLsFSgDoh4C5AQC2YDUE03wB3r0/ahJrf1hO28NJOe381Nr/WH9wji4a07v3t/bPQllx/NpTZcz3gpq81ALAetPS4Gzvq/VrlR+rBYCRsN4tgHUcUT7i/Lt9ABoB4gtIAPA5/xIA2rcVrT8MwGorkHUDAMI4q1eW5QPAiWABQQwBmdfVKkACUNPKB6DdotP22lEAsGYCwtMJTFSBwB71AczKAojyT6vv9QEMbQGU8mFq+QASALocSABo+VGqwZkpX6pA1j0AWDUn0+AMgRBRrd5YnHjt2JHRp9WzngKwBczGL3vy0vz4Wgw9Jr/UFgoG6khAjpYHQCIaRZMzHwRGQuKbgaw+W8CzAoDSUPZSucT1N2s+mE8B2AJjCyABYH/DogSAfpUza8HXFt66BQBc4NbbStZrvXI+arU4WDlrv9IOJiqR8T1x1zlNxCLe2rvnM1s7JGgx7trrwEj/TTvOaP7EYuXZuEvzCWCmIhkf0iPjER+AaHIcp2h8zZLQLA5tfFEAkHa9ORbxdWBZBxoQaLcAUR41iwDL1bYAvHcSRhZAAkD31l4CwNWNbCYAtEsUj5G11HayoBcGADRNipch2MAQyRigaLehSpFX0yDYnvUUQDQm3oPX7pXj+NFngHkCtPz2CDyYx1+7pquNX3wPsqeXOAV2CiLfrZocT4+wvvAnagFpFgSzRKQe8h3lOqrJMWuvVyMzC5B9Z3kNEMjUU4AEgDa3XwJAm+VYSwUmApkA0HJi4QEAEQtjmgXZpRxDHA2x8J41+gjwO/bLkND6HREfvdmiMaU9oQPvy4sG1vbWooGlHbxnLxaH9h37lYxDTKPiXQXNpJfx3HPZTQ0J591+TYeF0p/8UeIKkF6WWQhPFdBykT02WlrsjT+W1wDlAYG9lqZmcq05062RrDIO7VRFy2uA45d1PWYBJAC0t/vQZE4AuLMjQwkA/Spm4QDAqilnXQ5DijXva5QujAPQNKb0g5pc659pTMwazK4TY4y+9Iv9ID24ULH8D277cUfj7/zUpztNbLvyvb1DxHJS6NknN3XKn/2hizu/o+ZlGZPwjoX2IIb8/ZRzD3X60ywZGfcFO/aZj8KjsrZSX/NBaVsoDLFHRSS/47scVlpnOngrUUcyChMg1DLVhJYEgAQAj1xGyyYARDlYub7EAUizTGNiOdGo+3c+3nzSNCaS7bUQvBaIlU2iyV/7239grTKx3K5//ofO929tb/mCPoYnv72hU07zBaCvRetcNP+pv/4zLgvm9KseG1QJaqdhksFH840hH0oV46CDryJxwUYSAFqTPwEgKEiF1RMAChlXq9pLh7c3kYDRnw1PHug0ceiUYzq/s+/MQtAslNp0e9t74ak9E6scffLmzvcf/91nO7+jz+CZhx7tfD//E3/Y/C71Hv2X/+3tTzS/F8iWt9y4rpXguh78iiQlAHSBKwHAy4HFLp8AMKcWwKzECi0Tb7/MAnjkX/9x4hYDfQYvPr+vQwJqdLQgpLBX80u9tAC8M77Gys+rBTArNicArO8twP8DgiC3vF1P9z4AAAAASUVORK5CYII=", "relative_path": "../textures/models/ben/ov_pyronite.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_polymorph.png", "name": "ov_polymorph.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "43751577-e476-8039-6761-a18f3268a897", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/ov_polymorph.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio_galvanic_polymorph.png", "name": "bio_galvanic_polymorph.png", "folder": "", "namespace": "", "id": "5", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "d86e2d89-e16f-4f4d-9748-803b861d0476", "relative_path": "../textures/models/ben/bio_galvanic_polymorph.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_galvanic_mechamorph.png", "name": "ov_galvanic_mechamorph.png", "folder": "", "namespace": "", "id": "6", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "aa6f5d46-163e-2f30-650e-c82f8d8946a2", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/ov_galvanic_mechamorph.png"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}