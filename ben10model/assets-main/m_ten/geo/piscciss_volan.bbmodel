{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "piscciss_volan", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 19, -2], "to": [4.5, 26, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [0.5, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [19.5, 18.75, 28.5, 25.75], "texture": 0}, "east": {"uv": [15.5, 18.75, 19.5, 25.75], "texture": 0}, "south": {"uv": [31.5, 18.75, 40, 25.75], "texture": 0}, "west": {"uv": [27.5, 18.75, 31.5, 25.75], "texture": 0}, "up": {"uv": [28, 18.75, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 18.75], "texture": 0}}, "type": "cube", "uuid": "850d310d-d691-5416-daa7-1a5b1059677e"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 19, -2], "to": [4.5, 26, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [0.5, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [19.5, 34.75, 28.5, 41.75], "texture": 0}, "east": {"uv": [15.5, 34.75, 19.5, 41.75], "texture": 0}, "south": {"uv": [31.5, 34.75, 40.5, 41.75], "texture": 0}, "west": {"uv": [27.5, 34.75, 31.5, 41.75], "texture": 0}, "up": {"uv": [28.5, 34.75, 19.5, 30.75], "texture": 0}, "down": {"uv": [36.5, 30.75, 27.5, 34.75], "texture": 0}}, "type": "cube", "uuid": "557862b0-a0da-be82-e063-6004bf857d65"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 13, -2], "to": [3.5, 18, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [-0.5, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20.5, 27, 27.5, 32], "texture": 0}, "east": {"uv": [16.25, 25.5, 20.25, 30.5], "texture": 0}, "south": {"uv": [32.25, 25.5, 39.25, 30.5], "texture": 0}, "west": {"uv": [28.25, 25.5, 32.25, 30.5], "texture": 0}, "up": {"uv": [27, 4, 20, 0], "texture": 0}, "down": {"uv": [35.25, 14.5, 28.25, 18.5], "texture": 0}}, "type": "cube", "uuid": "6300eda9-b246-dc5d-b0ef-f96509b0b936"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 13, -2], "to": [3.5, 18, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [-0.5, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20.5, 43.25, 27.5, 48.25], "texture": 0}, "east": {"uv": [16.25, 41.5, 20.25, 46.5], "texture": 0}, "south": {"uv": [32.25, 41.5, 39.25, 46.5], "texture": 0}, "west": {"uv": [28.25, 41.5, 32.25, 46.5], "texture": 0}, "up": {"uv": [27, 20, 20, 16], "texture": 0}, "down": {"uv": [35.25, 30.5, 28.25, 34.5], "texture": 0}}, "type": "cube", "uuid": "91b8fcb0-47fc-8bac-ff45-4ed330b00397"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.5, 12, -2], "to": [8, 25, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 0}, "east": {"uv": [40, 20, 44, 32], "texture": 0}, "south": {"uv": [52, 20, 56, 32], "texture": 0}, "west": {"uv": [48, 20, 52, 32], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "a7302fb4-6503-6de2-1971-33518643497b"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.5, 11, -2], "to": [8, 26, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 0}, "east": {"uv": [40, 36, 44, 48], "texture": 0}, "south": {"uv": [52, 36, 56, 48], "texture": 0}, "west": {"uv": [48, 36, 52, 48], "texture": 0}, "up": {"uv": [48, 36, 44, 32], "texture": 0}, "down": {"uv": [52, 32, 48, 36], "texture": 0}}, "type": "cube", "uuid": "4e1e497f-4726-351e-a876-2717f9bb75fa"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 11, -2], "to": [-4.5, 26, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [-2, 1, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 0}, "east": {"uv": [48, 52, 52, 64], "texture": 0}, "south": {"uv": [60, 52, 64, 64], "texture": 0}, "west": {"uv": [56, 52, 60, 64], "texture": 0}, "up": {"uv": [56, 52, 52, 48], "texture": 0}, "down": {"uv": [60, 48, 56, 52], "texture": 0}}, "type": "cube", "uuid": "2a20268f-ac50-b135-de1b-f8b5095e8faa"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 12, -2], "to": [-5.5, 25, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [-2, 1, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 0}, "east": {"uv": [32, 52, 36, 64], "texture": 0}, "south": {"uv": [44, 52, 48, 64], "texture": 0}, "west": {"uv": [40, 52, 44, 64], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "f7e71384-83f7-4c95-3bcc-c4f86725af5d"}, {"name": "Yellow", "box_uv": false, "rescale": false, "locked": false, "render_order": "behind", "allow_mirror_modeling": true, "from": [-0.9999999999999996, 32.85, -4.825], "to": [1.0000000000000004, 36.35, -3.575], "autouv": 0, "color": 1, "visibility": false, "rotation": [22.5, 0, 0], "origin": [4.440892098500626e-16, 32.69623, -3.5776899999999996], "faces": {"north": {"uv": [0.25, 0, 2, 1.5], "texture": 0}, "east": {"uv": [0.25, 0, 2, 1.5], "texture": 0}, "south": {"uv": [0.25, 0, 2, 1.5], "texture": 0}, "west": {"uv": [0.25, 0, 2, 1.5], "texture": 0}, "up": {"uv": [0.25, 0, 2, 1.5], "texture": 0}, "down": {"uv": [0.25, 0, 2, 1.5], "texture": 0}}, "type": "cube", "uuid": "84e794ce-961b-74cb-5899-e101ed3c2126"}, {"name": "Yellow", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.9999999999999996, 31.59017, -4.039569999999999], "to": [1.0000000000000004, 33.64017, -3.0395700000000003], "autouv": 0, "color": 3, "visibility": false, "rotation": [-22.5, 0, 0], "origin": [4.440892098500626e-16, 31.890169999999998, -3.5395700000000003], "faces": {"north": {"uv": [0.25, 0, 2, 1.5], "texture": 0}, "east": {"uv": [0.25, 0, 2, 1.5], "texture": 0}, "south": {"uv": [0.25, 0, 2, 1.5], "texture": 0}, "west": {"uv": [0.25, 0, 2, 1.5], "texture": 0}, "up": {"uv": [0.25, 0, 2, 1.5], "texture": 0}, "down": {"uv": [0.25, 0, 2, 1.5], "texture": 0}}, "type": "cube", "uuid": "0aa1d76b-8125-96bb-feb6-9f982f2d6c1a"}, {"name": "Right", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.2499999999999996, 31.924999999999997, -3.125], "to": [-1.2499999999999996, 34.275, -1.375], "autouv": 0, "color": 5, "visibility": false, "origin": [4.440892098500626e-16, 33.5, -3], "faces": {"north": {"uv": [2, 0, 3.75, 1.75], "texture": 0}, "east": {"uv": [2, 0, 3.75, 1.75], "texture": 0}, "south": {"uv": [2, 0, 3.75, 1.75], "texture": 0}, "west": {"uv": [2, 0, 3.75, 1.75], "texture": 0}, "up": {"uv": [2, 0, 3.75, 1.75], "texture": 0}, "down": {"uv": [2, 0, 3.75, 1.75], "texture": 0}}, "type": "cube", "uuid": "a19a98d9-1d09-79a9-fefe-6e66b782d0f7"}, {"name": "Right", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.0499999999999994, 33.85147, -2.97968], "to": [-1.4499999999999997, 35.67647, -1.52968], "autouv": 0, "color": 8, "visibility": false, "origin": [-2.2499999999999996, 33.43897, -2.7546799999999996], "faces": {"north": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "east": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "south": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "west": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "up": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "down": {"uv": [3.5, 1, 5.5, 3], "texture": 0}}, "type": "cube", "uuid": "df63177f-70cd-af0f-8a1a-5ec31dd1fa75"}, {"name": "Right", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.25, 27.25, -1.5], "to": [-3.0749999999999997, 29.75, 0], "autouv": 0, "color": 6, "visibility": false, "rotation": [0, 0, 22.5], "origin": [-3.7499999999999996, 29, -1], "faces": {"north": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "east": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "south": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "west": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "up": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "down": {"uv": [3.5, 1, 5.5, 3], "texture": 0}}, "type": "cube", "uuid": "3a0df523-c55e-3afc-3881-d58b6a97fe73"}, {"name": "Right", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.046709999999999, 29.5597, -1.2599999999999998], "to": [-4.1917100000000005, 31.0597, -0.2400000000000002], "autouv": 0, "color": 2, "visibility": false, "rotation": [0, 0, 22.5], "origin": [-4.706709999999999, 31.3097, -0.75], "faces": {"north": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "east": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "south": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "west": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "up": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "down": {"uv": [3.5, 1, 5.5, 3], "texture": 0}}, "type": "cube", "uuid": "119ad3a6-a650-cb58-6610-18caf0e25e9e"}, {"name": "Right", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.375, 29.5, -1.25], "to": [-3.1249999999999996, 32.25, 0.25], "autouv": 0, "color": 2, "visibility": false, "rotation": [0, 0, 22.5], "origin": [-3.9999999999999996, 32.25, -0.5], "faces": {"north": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "east": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "south": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "west": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "up": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "down": {"uv": [3.5, 1, 5.5, 3], "texture": 0}}, "type": "cube", "uuid": "4de24bba-8092-ff3d-6770-0aaf12845ca4"}, {"name": "Right", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.01238, 32.14559, -1.085], "to": [-4.037380000000001, 33.89559, 0.08500000000000085], "autouv": 0, "color": 3, "visibility": false, "rotation": [0, 0, 22.5], "origin": [-4.64988, 34.14559, -0.25], "faces": {"north": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "east": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "south": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "west": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "up": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "down": {"uv": [3.5, 1, 5.5, 3], "texture": 0}}, "type": "cube", "uuid": "971af909-2fc2-8603-64fb-470fb250e6b1"}, {"name": "Left", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.4499999999999997, 33.85147, -2.97968], "to": [3.050000000000001, 35.67647, -1.52968], "autouv": 0, "color": 0, "visibility": false, "origin": [2.2500000000000004, 33.43897, -2.7546799999999996], "faces": {"north": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "east": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "south": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "west": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "up": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "down": {"uv": [3.5, 1, 5.5, 3], "texture": 0}}, "type": "cube", "uuid": "85d470d7-e12e-0172-130b-098cc397a8ce"}, {"name": "Left", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.2500000000000004, 31.924999999999997, -3.125], "to": [3.2500000000000004, 34.275, -1.375], "autouv": 0, "color": 8, "visibility": false, "origin": [4.440892098500626e-16, 33.5, -3], "faces": {"north": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "east": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "south": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "west": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "up": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "down": {"uv": [3.5, 1, 5.5, 3], "texture": 0}}, "type": "cube", "uuid": "1a40dbcf-da8b-5956-388f-a794fd8b1752"}, {"name": "Right", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.1250000000000004, 29.5, -1.25], "to": [4.375, 32.25, 0.25], "autouv": 0, "color": 0, "visibility": false, "rotation": [0, 0, -22.5], "origin": [4, 32.25, -0.5], "faces": {"north": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "east": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "south": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "west": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "up": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "down": {"uv": [3.5, 1, 5.5, 3], "texture": 0}}, "type": "cube", "uuid": "cba815f2-a068-3e4c-7241-9f43c4d86f65"}, {"name": "Right", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.037380000000001, 32.14559, -1.085], "to": [5.01238, 33.89559, 0.08500000000000085], "autouv": 0, "color": 3, "visibility": false, "rotation": [0, 0, -22.5], "origin": [4.64988, 34.14559, -0.25], "faces": {"north": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "east": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "south": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "west": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "up": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "down": {"uv": [3.5, 1, 5.5, 3], "texture": 0}}, "type": "cube", "uuid": "d297d7b2-fcae-d7d6-52d5-0a418b4e14e7"}, {"name": "Right", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.0749999999999997, 27.25, -1.5], "to": [4.25, 29.75, 0], "autouv": 0, "color": 1, "visibility": false, "rotation": [0, 0, -22.5], "origin": [3.7500000000000004, 29, -1], "faces": {"north": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "east": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "south": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "west": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "up": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "down": {"uv": [3.5, 1, 5.5, 3], "texture": 0}}, "type": "cube", "uuid": "ba05dd01-b647-2f07-40fb-08310b646205"}, {"name": "Right", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.1917100000000005, 29.5597, -1.2599999999999998], "to": [5.046709999999999, 31.0597, -0.2400000000000002], "autouv": 0, "color": 9, "visibility": false, "rotation": [0, 0, -22.5], "origin": [4.706709999999999, 31.3097, -0.75], "faces": {"north": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "east": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "south": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "west": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "up": {"uv": [3.5, 1, 5.5, 3], "texture": 0}, "down": {"uv": [3.5, 1, 5.5, 3], "texture": 0}}, "type": "cube", "uuid": "5137ad69-cd00-d843-a0fb-670fb83bd43a"}, {"name": "cube", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.9999999999999996, 28, -1], "to": [1.0000000000000004, 30, 1], "autouv": 1, "color": 0, "visibility": false, "origin": [4.440892098500626e-16, 28, 0], "faces": {"north": {"uv": [2, 2, 4, 4], "texture": 0}, "east": {"uv": [0, 2, 2, 4], "texture": 0}, "south": {"uv": [6, 2, 8, 4], "texture": 0}, "west": {"uv": [4, 2, 6, 4], "texture": 0}, "up": {"uv": [4, 2, 2, 0], "texture": 0}, "down": {"uv": [6, 0, 4, 2], "texture": 0}}, "type": "cube", "uuid": "81b2f150-d3b6-e779-6868-7e03cda76338"}, {"name": "Right Shoulder", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 22, -2], "to": [9.5, 25, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [2, 1, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [44, 20, 48, 23], "texture": 0}, "east": {"uv": [40, 20, 44, 23], "texture": 0}, "south": {"uv": [52, 20, 56, 23], "texture": 0}, "west": {"uv": [48, 20, 52, 23], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "73e6114b-9b8a-e056-9fae-0bc5af921b33"}, {"name": "Right Shoulder Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 22, -2], "to": [9.5, 25, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.51, "origin": [2, 1, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [44, 36, 48, 39], "texture": 0}, "east": {"uv": [40, 36, 44, 39], "texture": 0}, "south": {"uv": [52, 36, 56, 39], "texture": 0}, "west": {"uv": [48, 36, 52, 39], "texture": 0}, "up": {"uv": [48, 36, 44, 32], "texture": 0}, "down": {"uv": [52, 32, 48, 36], "texture": 0}}, "type": "cube", "uuid": "c76a7a12-9c4e-ba09-111b-c23c8ca0b2df"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 18, -2], "to": [9, 22, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [2, 1, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 23, 47, 26], "texture": 0}, "east": {"uv": [40, 23, 44, 26], "texture": 0}, "south": {"uv": [52, 23, 55, 26], "texture": 0}, "west": {"uv": [48, 23, 52, 26], "texture": 0}, "up": {"uv": [47, 23, 44, 19], "texture": 0}, "down": {"uv": [51, 19, 48, 23], "texture": 0}}, "type": "cube", "uuid": "cea98f2c-444a-7710-af78-c8aafc6d6bab"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 18, -2], "to": [9, 22, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [2, 1, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 39, 47, 42], "texture": 0}, "east": {"uv": [40, 39, 44, 42], "texture": 0}, "south": {"uv": [52, 39, 55, 42], "texture": 0}, "west": {"uv": [48, 39, 52, 42], "texture": 0}, "up": {"uv": [47, 39, 44, 35], "texture": 0}, "down": {"uv": [51, 35, 48, 39], "texture": 0}}, "type": "cube", "uuid": "1c5f496f-f9ba-bc47-00fe-1bf13dec37e4"}, {"name": "Right Hand", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 12, -2], "to": [10, 19, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [2, 1, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 26, 48, 32], "texture": 0}, "east": {"uv": [40, 26, 44, 32], "texture": 0}, "south": {"uv": [52, 26, 56, 32], "texture": 0}, "west": {"uv": [48, 26, 52, 32], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "b9cc816f-166c-bbb5-ba08-9555eb6758c1"}, {"name": "Right Hand Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 12, -2], "to": [10, 19, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.52, "origin": [2, 1, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 42, 48, 48], "texture": 0}, "east": {"uv": [40, 42, 44, 48], "texture": 0}, "south": {"uv": [52, 42, 56, 48], "texture": 0}, "west": {"uv": [48, 42, 52, 48], "texture": 0}, "up": {"uv": [48, 46, 44, 42], "texture": 0}, "down": {"uv": [52, 38, 48, 42], "texture": 0}}, "type": "cube", "uuid": "41b740cd-10ec-b3ec-3fa3-ca3744f70497"}, {"name": "Left Shoulder", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.5, 22, -2], "to": [-6, 25, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [-2, 1, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 55], "texture": 0}, "east": {"uv": [32, 52, 36, 54], "texture": 0}, "south": {"uv": [44, 52, 48, 54], "texture": 0}, "west": {"uv": [40, 52, 44, 54], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "c8dac093-351f-0f2f-f0a8-835e104cb360"}, {"name": "Left Shoulder Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.5, 22, -2], "to": [-6, 25, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.51, "origin": [-2, 1, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 55], "texture": 0}, "east": {"uv": [48, 52, 52, 54], "texture": 0}, "south": {"uv": [60, 52, 64, 54], "texture": 0}, "west": {"uv": [56, 52, 60, 54], "texture": 0}, "up": {"uv": [56, 52, 52, 48], "texture": 0}, "down": {"uv": [60, 48, 56, 52], "texture": 0}}, "type": "cube", "uuid": "bfa7635e-99f2-95ee-4839-1a26da0f6c14"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9, 18, -2], "to": [-6, 22, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [-2, 1, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [36, 55, 39, 58], "texture": 0}, "east": {"uv": [32, 55, 36, 58], "texture": 0}, "south": {"uv": [44, 55, 47, 58], "texture": 0}, "west": {"uv": [40, 55, 44, 58], "texture": 0}, "up": {"uv": [39, 55, 36, 51], "texture": 0}, "down": {"uv": [43, 51, 40, 55], "texture": 0}}, "type": "cube", "uuid": "5be098a4-8213-38eb-c336-caae61b5c198"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9, 18, -2], "to": [-6, 22, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [-2, 1, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [52, 55, 55, 58], "texture": 0}, "east": {"uv": [48, 55, 52, 58], "texture": 0}, "south": {"uv": [60.75, 55, 63.75, 58], "texture": 0}, "west": {"uv": [56, 55, 60, 58], "texture": 0}, "up": {"uv": [55, 55, 52, 51], "texture": 0}, "down": {"uv": [59, 51, 56, 55], "texture": 0}}, "type": "cube", "uuid": "d29024b9-9896-24d7-a741-ed626e3d307d"}, {"name": "Left Hand", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10, 12, -2], "to": [-6, 19, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [-2, 1, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [36, 59, 40, 64], "texture": 0}, "east": {"uv": [32, 59, 36, 64], "texture": 0}, "south": {"uv": [44, 59, 48, 64], "texture": 0}, "west": {"uv": [40, 59, 44, 64], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "065d74a0-a6a9-b3ab-3dae-47298eedaddb"}, {"name": "Left Hand Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10, 12, -2], "to": [-6, 19, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.52, "origin": [-2, 1, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 42, 48, 48], "texture": 0}, "east": {"uv": [40, 42, 44, 48], "texture": 0}, "south": {"uv": [52, 42, 56, 48], "texture": 0}, "west": {"uv": [48, 42, 52, 48], "texture": 0}, "up": {"uv": [48, 46, 44, 42], "texture": 0}, "down": {"uv": [52, 38, 48, 42], "texture": 0}}, "type": "cube", "uuid": "c471ccd4-2a3c-53c4-5a83-92d9b97efba0"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 19, -2], "to": [5, 26, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [19, 20, 29, 27], "texture": 0}, "east": {"uv": [15, 20, 19, 27], "texture": 0}, "south": {"uv": [32, 20, 40.25, 27], "texture": 0}, "west": {"uv": [27, 20, 31, 27], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [37, 16, 27, 20], "texture": 0}}, "type": "cube", "uuid": "17570ecb-0680-1ea4-a159-8dc272fbd2fc"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 19, -2], "to": [5, 26, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [19, 36, 29, 43], "texture": 0}, "east": {"uv": [15, 36, 19, 43], "texture": 0}, "south": {"uv": [32, 36, 40, 43], "texture": 0}, "west": {"uv": [27, 36, 31, 43], "texture": 0}, "up": {"uv": [29, 36, 19, 32], "texture": 0}, "down": {"uv": [37, 32, 27, 36], "texture": 0}}, "type": "cube", "uuid": "30b87036-38a5-0885-d0f9-55c00f7c4a68"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 13, -2], "to": [4.5, 18, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [-0.5, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [19.5, 27, 28.5, 32], "texture": 0}, "east": {"uv": [15.5, 27, 19.5, 32], "texture": 0}, "south": {"uv": [31.5, 27, 40.5, 32], "texture": 0}, "west": {"uv": [27.5, 27, 31.5, 32], "texture": 0}, "up": {"uv": [28.5, 27, 19.5, 23], "texture": 0}, "down": {"uv": [36.5, 23, 27.5, 27], "texture": 0}}, "type": "cube", "uuid": "f6129da9-db0d-a488-d52c-7c62d00cd9f6"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 13, -2], "to": [4.5, 18, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [-0.5, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [19.5, 43, 28.5, 48], "texture": 0}, "east": {"uv": [15.5, 43, 19.5, 48], "texture": 0}, "south": {"uv": [32, 41, 40, 48], "texture": 0}, "west": {"uv": [27.5, 43, 31.5, 48], "texture": 0}, "up": {"uv": [28, 43, 19, 39], "texture": 0}, "down": {"uv": [36.5, 39, 27.5, 43], "texture": 0}}, "type": "cube", "uuid": "0ea49582-abf8-9b9a-de04-b68065b02974"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, 0, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 0}, "east": {"uv": [0, 52, 4, 64], "texture": 0}, "south": {"uv": [12, 52, 16, 64], "texture": 0}, "west": {"uv": [8, 52, 12, 64], "texture": 0}, "up": {"uv": [8, 52, 4, 48], "texture": 0}, "down": {"uv": [12, 48, 8, 52], "texture": 0}}, "type": "cube", "uuid": "8e8404ba-2ce6-fd7c-e6f8-8e0f3a2ed172"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, 0, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 0}, "east": {"uv": [16, 52, 20, 64], "texture": 0}, "south": {"uv": [28, 52, 32, 64], "texture": 0}, "west": {"uv": [24, 52, 28, 64], "texture": 0}, "up": {"uv": [24, 52, 20, 48], "texture": 0}, "down": {"uv": [28, 48, 24, 52], "texture": 0}}, "type": "cube", "uuid": "831470fe-9617-7f94-f464-02821ff58cd1"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 0}, "east": {"uv": [0, 36, 4, 48], "texture": 0}, "south": {"uv": [12, 36, 16, 48], "texture": 0}, "west": {"uv": [8, 36, 12, 48], "texture": 0}, "up": {"uv": [8, 36, 4, 32], "texture": 0}, "down": {"uv": [12, 32, 8, 36], "texture": 0}}, "type": "cube", "uuid": "ab0b9301-bc8d-e71a-0d0b-7913410f6245"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 0}, "east": {"uv": [0, 20, 4, 32], "texture": 0}, "south": {"uv": [12, 20, 16, 32], "texture": 0}, "west": {"uv": [8, 20, 12, 32], "texture": 0}, "up": {"uv": [8, 20, 4, 16], "texture": 0}, "down": {"uv": [12, 16, 8, 20], "texture": 0}}, "type": "cube", "uuid": "d222714e-943b-2ab4-4205-6d9941ce1121"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 33, -1], "to": [-1, 35, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [0, 0, -12.5], "origin": [-2, 33, 0], "uv_offset": [8, 0], "faces": {"north": {"uv": [10, 2, 12, 6], "texture": 0}, "east": {"uv": [8, 2, 10, 6], "texture": 0}, "south": {"uv": [10, 2, 12, 6], "texture": 0}, "west": {"uv": [12, 2, 14, 6], "texture": 0}, "up": {"uv": [12, 2, 10, 0], "texture": 0}, "down": {"uv": [14, 0, 12, 2], "texture": 0}}, "type": "cube", "uuid": "3412592f-6338-07ca-60a4-d1cc4be32cdd"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1, 33, -1], "to": [3, 34, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [0, 0, 12.5], "origin": [2, 33, 0], "uv_offset": [8, 1], "faces": {"north": {"uv": [8.75, 4, 10.75, 6], "texture": 0}, "east": {"uv": [8.25, 3.25, 10.25, 5.25], "texture": 0}, "south": {"uv": [14, 3, 16, 5], "texture": 0}, "west": {"uv": [12, 3, 14, 5], "texture": 0}, "up": {"uv": [12, 3.25, 10, 1.25], "texture": 0}, "down": {"uv": [14, 1.25, 12, 3.25], "texture": 0}}, "type": "cube", "uuid": "d8f3a281-1a43-6fbc-706f-1d481225d415"}, {"name": "Eyes", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 29, -3.7], "to": [6.5, 37, 4.299999999999999], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.9, "origin": [1.5, 3, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [24, 0, 37.25, 8], "texture": 0}, "east": {"uv": [48, 6.25, 40, 0], "texture": null}, "south": {"uv": [48, 6.25, 40, 0], "texture": null}, "west": {"uv": [48, 6.25, 40, 0], "texture": null}, "up": {"uv": [48, 6.25, 40, 0], "texture": null}, "down": {"uv": [48, 6.25, 40, 0], "texture": null}}, "type": "cube", "uuid": "9cd5a16d-cac8-337c-87d5-4859c0a4165f"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 19, -2], "to": [4, 25, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 26], "texture": 0}, "east": {"uv": [16, 20, 20, 26], "texture": 0}, "south": {"uv": [32, 20, 40, 26], "texture": 0}, "west": {"uv": [28, 20, 32, 26], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "dd0a7062-5498-217a-54f8-3d66340bbd1e"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 19, -2], "to": [4, 25, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 42], "texture": 0}, "east": {"uv": [16, 36, 20, 42], "texture": 0}, "south": {"uv": [32, 36, 40, 42], "texture": 0}, "west": {"uv": [28, 36, 32, 42], "texture": 0}, "up": {"uv": [28, 36, 20, 32], "texture": 0}, "down": {"uv": [36, 32, 28, 36], "texture": 0}}, "type": "cube", "uuid": "961f0ccb-a84d-7e4c-6a5c-b722ef3c9c18"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 13, -2], "to": [3.5, 18, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [-0.5, 1, 0], "uv_offset": [16, 23], "faces": {"north": {"uv": [20.5, 27, 27.5, 32], "texture": 0}, "east": {"uv": [16, 27, 20, 32], "texture": 0}, "south": {"uv": [31, 27, 38, 32], "texture": 0}, "west": {"uv": [27, 27, 31, 32], "texture": 0}, "up": {"uv": [27, 27, 20, 23], "texture": 0}, "down": {"uv": [34, 23, 27, 27], "texture": 0}}, "type": "cube", "uuid": "a08f6fb1-1fed-2df0-5572-953bed22500f"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 13, -2], "to": [3.5, 18, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [-0.5, 1, 0], "uv_offset": [16, 39], "faces": {"north": {"uv": [20, 43, 27, 48], "texture": 0}, "east": {"uv": [16, 43, 20, 48], "texture": 0}, "south": {"uv": [31, 43, 38, 48], "texture": 0}, "west": {"uv": [27, 43, 31, 48], "texture": 0}, "up": {"uv": [27, 43, 20, 39], "texture": 0}, "down": {"uv": [34, 39, 27, 43], "texture": 0}}, "type": "cube", "uuid": "7a55e1a2-9ceb-a7e9-745c-b36b002945ea"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 26, -4], "to": [3.5, 32, 4], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.51, "origin": [0.5, 1, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 0}, "east": {"uv": [0, 8, 8, 16], "texture": 0}, "south": {"uv": [24, 8, 32, 16], "texture": 0}, "west": {"uv": [16, 8, 24, 16], "texture": 0}, "up": {"uv": [16, 8, 8, 0], "texture": 0}, "down": {"uv": [24, 0, 16, 8], "texture": 0}}, "type": "cube", "uuid": "7c1d65f2-1a2b-08d3-4462-e674dcf57a24"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 26, -4], "to": [3.5, 32, 4], "autouv": 0, "color": 0, "visibility": false, "inflate": 1, "origin": [0.5, 1, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 0}, "east": {"uv": [32, 8, 40, 16], "texture": 0}, "south": {"uv": [56, 8, 64, 16], "texture": 0}, "west": {"uv": [48, 8, 56, 16], "texture": 0}, "up": {"uv": [48, 8, 40, 0], "texture": 0}, "down": {"uv": [56, 0, 48, 8], "texture": 0}}, "type": "cube", "uuid": "7042fc4a-54b9-1b56-3c69-9f64c38df94b"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 32, -1], "to": [1, 34, 1], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 24, 0], "uv_offset": [40, 2], "faces": {"north": {"uv": [59.25, 21.25, 61.25, 23.25], "texture": 0}, "east": {"uv": [59.25, 21.25, 61.25, 23.25], "texture": 0}, "south": {"uv": [59.25, 21.25, 61.25, 23.25], "texture": 0}, "west": {"uv": [59.25, 21.25, 61.25, 23.25], "texture": 0}, "up": {"uv": [59.25, 21.25, 61.25, 23.25], "texture": 0}, "down": {"uv": [59.25, 21.25, 61.25, 23.25], "texture": 0}}, "type": "cube", "uuid": "373ee4f5-abcc-6d7c-1908-5b2bcf834aee"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 34, -6], "to": [1, 35, 1], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 25, 0], "uv_offset": [51, 11], "faces": {"north": {"uv": [61.25, 21.5, 59.25, 23], "texture": 0}, "east": {"uv": [61.25, 21.5, 59.25, 23], "texture": 0}, "south": {"uv": [61.25, 21.5, 59.25, 23], "texture": 0}, "west": {"uv": [61.25, 21.5, 59.25, 23], "texture": 0}, "up": {"uv": [61.25, 21.5, 59.25, 23], "texture": 0}, "down": {"uv": [61.25, 21.5, 59.25, 23], "texture": 0}}, "type": "cube", "uuid": "93c955dd-b714-b8e1-fdcc-05616ef218e9"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 34, -7], "to": [1, 35, -6], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 25, -1], "uv_offset": [57, 17], "faces": {"north": {"uv": [60.75, 27.5, 59.75, 28.5], "texture": 0}, "east": {"uv": [60.75, 27.5, 59.75, 28.5], "texture": 0}, "south": {"uv": [60.75, 27.5, 59.75, 28.5], "texture": 0}, "west": {"uv": [60.75, 27.5, 59.75, 28.5], "texture": 0}, "up": {"uv": [60.75, 27.5, 59.75, 28.5], "texture": 0}, "down": {"uv": [60.75, 27.5, 59.75, 28.5], "texture": 0}}, "type": "cube", "uuid": "d9b443db-8c5f-ce24-6c90-850dc0fd8822"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 12, -2], "to": [8, 24, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [1, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 0}, "east": {"uv": [40, 36, 44, 48], "texture": 0}, "south": {"uv": [52, 36, 56, 48], "texture": 0}, "west": {"uv": [48, 36, 52, 48], "texture": 0}, "up": {"uv": [48, 36, 44, 32], "texture": 0}, "down": {"uv": [52, 32, 48, 36], "texture": 0}}, "type": "cube", "uuid": "4954c74c-88cf-9c10-792a-88dc170439f0"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 12, -2], "to": [8, 24, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [1, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 0}, "east": {"uv": [40, 20, 44, 32], "texture": 0}, "south": {"uv": [52, 20, 56, 32], "texture": 0}, "west": {"uv": [48, 20, 52, 32], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "52a39933-b0f1-97d1-9e9d-d799969cada6"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 12, -2], "to": [-5, 24, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [-1, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 0}, "east": {"uv": [48, 52, 52, 64], "texture": 0}, "south": {"uv": [60, 52, 64, 64], "texture": 0}, "west": {"uv": [56, 52, 60, 64], "texture": 0}, "up": {"uv": [56, 52, 52, 48], "texture": 0}, "down": {"uv": [60, 48, 56, 52], "texture": 0}}, "type": "cube", "uuid": "dab536d8-4ad3-b374-0d81-13d675ccb50e"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 12, -2], "to": [-5, 24, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [-1, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 0}, "east": {"uv": [32, 52, 36, 64], "texture": 0}, "south": {"uv": [44, 52, 48, 64], "texture": 0}, "west": {"uv": [40, 52, 44, 64], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "2928b0ec-ebb3-0284-91ff-f38b134e1227"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -2], "to": [-0.5, 12, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 0}, "east": {"uv": [0, 52, 4, 64], "texture": 0}, "south": {"uv": [12, 52, 16, 64], "texture": 0}, "west": {"uv": [8, 52, 12, 64], "texture": 0}, "up": {"uv": [8, 52, 4, 48], "texture": 0}, "down": {"uv": [12, 48, 8, 52], "texture": 0}}, "type": "cube", "uuid": "c0d406c9-5ad4-6979-5424-12cb3b063aa9"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -2], "to": [-0.5, 12, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 0}, "east": {"uv": [16, 52, 20, 64], "texture": 0}, "south": {"uv": [28, 52, 32, 64], "texture": 0}, "west": {"uv": [24, 52, 28, 64], "texture": 0}, "up": {"uv": [24, 52, 20, 48], "texture": 0}, "down": {"uv": [28, 48, 24, 52], "texture": 0}}, "type": "cube", "uuid": "9919b16b-f9ba-72b9-f558-20129c04ce66"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 0}, "east": {"uv": [0, 20, 4, 32], "texture": 0}, "south": {"uv": [12, 20, 16, 32], "texture": 0}, "west": {"uv": [8, 20, 12, 32], "texture": 0}, "up": {"uv": [8, 20, 4, 16], "texture": 0}, "down": {"uv": [12, 16, 8, 20], "texture": 0}}, "type": "cube", "uuid": "2906fb46-937d-536a-3868-8f52f9202c99"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "visibility": false, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 0}, "east": {"uv": [0, 36, 4, 48], "texture": 0}, "south": {"uv": [12, 36, 16, 48], "texture": 0}, "west": {"uv": [8, 36, 12, 48], "texture": 0}, "up": {"uv": [8, 36, 4, 32], "texture": 0}, "down": {"uv": [12, 32, 8, 36], "texture": 0}}, "type": "cube", "uuid": "c84be484-afde-1d83-86f2-02b77f003e40"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 24, -1], "to": [-5, 28, 1], "autouv": 0, "color": 0, "visibility": false, "rotation": [-2.684319482615525, 0.7790973560979717, 32.364564846716696], "origin": [-6, 25, 0], "uv_offset": [0, 9], "faces": {"north": {"uv": [10, 2, 12, 6], "texture": 0}, "east": {"uv": [8, 2, 10, 6], "texture": 0}, "south": {"uv": [10, 1.75, 12, 5.75], "texture": 0}, "west": {"uv": [12, 2, 14, 6], "texture": 0}, "up": {"uv": [12, 3.5, 10, 1.5], "texture": 0}, "down": {"uv": [14, 0, 12, 2], "texture": 0}}, "type": "cube", "uuid": "bb2d287d-b46e-e67c-0819-58d4380ae820"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 24, -1], "to": [7, 28, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "rotation": [-2.684319482615525, -0.7790973560979717, -32.364564846716696], "origin": [6, 25, 0], "uv_offset": [0, 10], "faces": {"north": {"uv": [12, 2, 10, 6], "texture": 0}, "east": {"uv": [14, 2, 12, 6], "texture": 0}, "south": {"uv": [12, 2, 10, 6], "texture": 0}, "west": {"uv": [10, 2, 8, 6], "texture": 0}, "up": {"uv": [10, 3.5, 12, 1.5], "texture": 0}, "down": {"uv": [12, 0, 14, 2], "texture": 0}}, "type": "cube", "uuid": "c8cb4826-182f-3b1b-b062-7c16e844c283"}, {"name": "bone5", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 27, -6.8], "to": [3.5, 28, -0.7999999999999998], "autouv": 0, "color": 3, "inflate": 0.2, "origin": [0, 0, 0], "uv_offset": [36, 14], "faces": {"north": {"uv": [8, 14.5, 16, 15.75], "texture": 0}, "east": {"uv": [0, 15.5, 8, 16], "texture": 0}, "south": {"uv": [24, 15.5, 32, 16], "texture": 0}, "west": {"uv": [16, 15.5, 24, 16], "texture": 0}, "up": {"uv": [16, 8, 8, 0], "texture": 0}, "down": {"uv": [24, 0, 16, 8], "texture": 0}}, "type": "cube", "uuid": "17d774b8-07cd-9296-faa4-502151dbb2a7"}, {"name": "bone5", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2.2, 27.4, -7], "to": [3.2, 28.4, -6], "autouv": 0, "color": 3, "inflate": 0.1, "rotation": [0, 0, 45], "origin": [2.7, 27.9, -6.5], "uv_offset": [81, 87], "faces": {"north": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "east": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "south": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "west": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "up": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "down": {"uv": [11.75, 13, 11.75, 13], "texture": 0}}, "type": "cube", "uuid": "a21fbdd7-137c-e5b5-d2b6-5003926c6e12"}, {"name": "bone5", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.2, 27.4, -7], "to": [-2.2, 28.4, -6], "autouv": 0, "color": 3, "inflate": 0.1, "rotation": [0, 0, 45], "origin": [-2.7, 27.9, -6.5], "uv_offset": [81, 87], "faces": {"north": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "east": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "south": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "west": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "up": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "down": {"uv": [11.75, 13, 11.75, 13], "texture": 0}}, "type": "cube", "uuid": "b0e66a3d-1909-a646-92b8-8e98ce97eba4"}, {"name": "bone5", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.8999999999999999, 27.4, -7], "to": [1.9, 28.4, -6], "autouv": 0, "color": 3, "inflate": 0.1, "rotation": [0, 0, 45], "origin": [1.4, 27.9, -6.5], "uv_offset": [81, 87], "faces": {"north": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "east": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "south": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "west": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "up": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "down": {"uv": [11.75, 13, 11.75, 13], "texture": 0}}, "type": "cube", "uuid": "a7c1d1be-1feb-02ab-8c19-99f1c3515cf1"}, {"name": "bone5", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.9, 27.4, -7], "to": [-0.8999999999999999, 28.4, -6], "autouv": 0, "color": 3, "inflate": 0.1, "rotation": [0, 0, 45], "origin": [-1.4, 27.9, -6.5], "uv_offset": [81, 87], "faces": {"north": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "east": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "south": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "west": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "up": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "down": {"uv": [11.75, 13, 11.75, 13], "texture": 0}}, "type": "cube", "uuid": "ee78656d-becb-1382-3d1d-b3759bd035f6"}, {"name": "bone5", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 27.4, -7], "to": [0.5, 28.4, -6], "autouv": 0, "color": 3, "inflate": 0.1, "rotation": [0, 0, 45], "origin": [0, 27.9, -6.5], "uv_offset": [81, 87], "faces": {"north": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "east": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "south": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "west": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "up": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "down": {"uv": [11.75, 13, 11.75, 13], "texture": 0}}, "type": "cube", "uuid": "f28720f9-2fbf-b254-4b8b-cdd01d0342f9"}, {"name": "bone5", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.4, 27, -4.8], "to": [-3.4, 31, -1.7999999999999998], "autouv": 0, "color": 3, "rotation": [15, 0, 0], "origin": [-3.5, 28, -1.8], "uv_offset": [122, 88], "faces": {"north": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "east": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "south": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "west": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "up": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "down": {"uv": [11.75, 13, 11.75, 13], "texture": 0}}, "type": "cube", "uuid": "a4fd14e1-dc5a-2890-3030-73dad6470695"}, {"name": "bone5", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.4, 27, -4.8], "to": [3.4, 31, -1.7999999999999998], "autouv": 0, "color": 3, "rotation": [15, 0, 0], "origin": [3.5, 28, -1.8], "uv_offset": [84, 102], "faces": {"north": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "east": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "south": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "west": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "up": {"uv": [11.75, 13, 11.75, 13], "texture": 0}, "down": {"uv": [11.75, 13, 11.75, 13], "texture": 0}}, "type": "cube", "uuid": "db911a6e-6bb6-8fb3-51c1-b8344832affc"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 28, -6.5], "to": [3.5, 32, -0.5], "autouv": 0, "color": 2, "origin": [0, 0, 0], "faces": {"north": {"uv": [8, 8, 16, 13], "texture": 0}, "east": {"uv": [0, 8, 8, 14.5], "texture": 0}, "south": {"uv": [24, 8, 32, 16], "texture": 0}, "west": {"uv": [16, 8, 24, 14.5], "texture": 0}, "up": {"uv": [16, 8, 8, 0], "texture": 0}, "down": {"uv": [24, 0, 16, 8], "texture": 0}}, "type": "cube", "uuid": "e4cd28cf-ea01-4e74-0c3f-83f008279a2c"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2.2, 27.8, -6.6], "to": [3.2, 28.8, -5.6], "autouv": 0, "color": 2, "rotation": [0, 0, 45], "origin": [2.7, 28.3, -6.1], "uv_offset": [81, 87], "faces": {"north": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "east": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "south": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "west": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "up": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "down": {"uv": [12.25, 13, 12.25, 13], "texture": 0}}, "type": "cube", "uuid": "ff7dfe23-5677-79d3-c6d4-c2d286b8df28"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2.6, 27.8, -6.2], "to": [3.6, 28.8, -5.2], "autouv": 0, "color": 2, "rotation": [-45, 0, 0], "origin": [2.8, 28.3, -5.7], "uv_offset": [81, 87], "faces": {"north": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "east": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "south": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "west": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "up": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "down": {"uv": [12.25, 13, 12.25, 13], "texture": 0}}, "type": "cube", "uuid": "8fa33568-9f8e-d540-7030-f9c4d61d9edf"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.6, 27.8, -6.2], "to": [-2.6, 28.8, -5.2], "autouv": 0, "color": 2, "rotation": [-45, 0, 0], "origin": [-3.4, 28.3, -5.7], "uv_offset": [81, 87], "faces": {"north": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "east": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "south": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "west": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "up": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "down": {"uv": [12.25, 13, 12.25, 13], "texture": 0}}, "type": "cube", "uuid": "553f1eb4-b245-29fa-1693-7c7cc98b46d5"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.8, 27.8, -6.6], "to": [-0.8, 28.8, -5.6], "autouv": 0, "color": 2, "rotation": [0, 0, 45], "origin": [-1.3, 28.3, -6.1], "uv_offset": [81, 87], "faces": {"north": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "east": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "south": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "west": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "up": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "down": {"uv": [12.25, 13, 12.25, 13], "texture": 0}}, "type": "cube", "uuid": "a48449e6-34d1-998f-f274-6d7d497c18c7"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 27.8, -6.6], "to": [0.5, 28.8, -5.6], "autouv": 0, "color": 2, "rotation": [0, 0, 45], "origin": [0, 28.3, -6.1], "uv_offset": [81, 87], "faces": {"north": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "east": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "south": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "west": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "up": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "down": {"uv": [12.25, 13, 12.25, 13], "texture": 0}}, "type": "cube", "uuid": "a4fa0893-7438-3bda-edef-e1f75de986fa"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.2, 27.8, -6.6], "to": [-2.2, 28.8, -5.6], "autouv": 0, "color": 2, "rotation": [0, 0, 45], "origin": [-2.7, 28.3, -6.1], "uv_offset": [81, 87], "faces": {"north": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "east": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "south": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "west": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "up": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "down": {"uv": [12.25, 13, 12.25, 13], "texture": 0}}, "type": "cube", "uuid": "8d4dc996-e59a-c465-7c6b-7aa943bf439c"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.8, 27.8, -6.6], "to": [1.8, 28.8, -5.6], "autouv": 0, "color": 2, "rotation": [0, 0, 45], "origin": [1.3, 28.3, -6.1], "uv_offset": [81, 87], "faces": {"north": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "east": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "south": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "west": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "up": {"uv": [12.25, 13, 12.25, 13], "texture": 0}, "down": {"uv": [12.25, 13, 12.25, 13], "texture": 0}}, "type": "cube", "uuid": "9704859e-8cd9-a6da-762d-4b609f52d016"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 36.18898, -7.34319], "to": [0.5, 37.18898, -6.34319], "autouv": 0, "color": 2, "rotation": [-82.5, 0, 0], "origin": [0, 34.18898, -6.84319], "uv_offset": [16, 28], "faces": {"north": {"uv": [60.5, 28, 60.5, 28], "texture": 0}, "east": {"uv": [60.5, 28, 60.5, 28], "texture": 0}, "south": {"uv": [60.5, 28, 60.5, 28], "texture": 0}, "west": {"uv": [60.5, 28, 60.5, 28], "texture": 0}, "up": {"uv": [60.5, 28, 60.5, 28], "texture": 0}, "down": {"uv": [60.5, 28, 60.5, 28], "texture": 0}}, "type": "cube", "uuid": "afceed65-4915-9b6d-f238-1e72f793abdd"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 33.9, -7.34319], "to": [0.5, 36.9, -6.34319], "autouv": 0, "color": 2, "inflate": -0.1, "rotation": [-82.5, 0, 0], "origin": [0, 34.18898, -6.84319], "uv_offset": [0, 10], "faces": {"north": {"uv": [60, 22.75, 60, 22.75], "texture": 0}, "east": {"uv": [60, 22.75, 60, 22.75], "texture": 0}, "south": {"uv": [60, 22.75, 60, 22.75], "texture": 0}, "west": {"uv": [60, 22.75, 60, 22.75], "texture": 0}, "up": {"uv": [60, 22.75, 60, 22.75], "texture": 0}, "down": {"uv": [60, 22.75, 60, 22.75], "texture": 0}}, "type": "cube", "uuid": "1897224a-7cb1-0b63-ddb3-e5ca2e54f27c"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 31.1, -5.5], "to": [0.5, 35.1, -4.5], "autouv": 0, "color": 2, "inflate": -0.1, "rotation": [-47.5, 0, 0], "origin": [0, 32.5, -5], "faces": {"north": {"uv": [60, 22.75, 60, 22.75], "texture": 0}, "east": {"uv": [60, 22.75, 60, 22.75], "texture": 0}, "south": {"uv": [60, 22.75, 60, 22.75], "texture": 0}, "west": {"uv": [60, 22.75, 60, 22.75], "texture": 0}, "up": {"uv": [60, 22.75, 60, 22.75], "texture": 0}, "down": {"uv": [60, 22.75, 60, 22.75], "texture": 0}}, "type": "cube", "uuid": "701d66cf-9634-843b-5503-373fba834338"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 31, -5.5], "to": [0, 35.5, 0.5], "autouv": 0, "color": 2, "origin": [0, 0, 0], "uv_offset": [0, 33], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 0}, "east": {"uv": [50.5, 11.5, 45.75, 14.75], "texture": 0}, "south": {"uv": [24, 8, 32, 16], "texture": 0}, "west": {"uv": [45.75, 11.5, 50.5, 14.75], "texture": 0}, "up": {"uv": [16, 8, 8, 0], "texture": 0}, "down": {"uv": [24, 0, 16, 8], "texture": 0}}, "type": "cube", "uuid": "449bf602-c15c-386f-c903-bce3bf871c6d"}, {"name": "body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 15, -2], "to": [3, 26, 2], "autouv": 0, "color": 9, "inflate": -0.2, "origin": [0, 0, 0], "uv_offset": [20, 17], "faces": {"north": {"uv": [20, 25.75, 28, 26.75], "texture": 0}, "east": {"uv": [16, 20, 20, 26], "texture": 0}, "south": {"uv": [32, 20, 40, 26], "texture": 0}, "west": {"uv": [28, 20, 32, 26], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "42a75968-003f-247e-068d-61d5de739341"}, {"name": "body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 15, -2], "to": [3, 18, 2], "autouv": 0, "color": 9, "inflate": -0.1, "origin": [0, 0, 0], "uv_offset": [0, 87], "faces": {"north": {"uv": [20, 25.75, 28, 30.25], "texture": 0}, "east": {"uv": [16, 20, 20, 26], "texture": 0}, "south": {"uv": [32, 20, 40, 26], "texture": 0}, "west": {"uv": [28, 20, 32, 26], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "4dc63ad5-491d-df17-229e-7b022cf6002c"}, {"name": "body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 22, -2], "to": [4.5, 26, 2], "autouv": 0, "color": 9, "inflate": 0.21, "origin": [0, 0, 0], "uv_offset": [22, 6], "faces": {"north": {"uv": [20, 20, 28, 24.25], "texture": 0}, "east": {"uv": [16, 20, 20, 26], "texture": 0}, "south": {"uv": [32, 20, 40, 26], "texture": 0}, "west": {"uv": [28, 20, 32, 26], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "adc4c678-ea18-873e-85a0-0215c1e8de64"}, {"name": "body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 23.25, 1.75], "to": [0, 30.25, 9.75], "autouv": 0, "color": 9, "rotation": [90, 0, 0], "origin": [0, 26.25, 1.75], "uv_offset": [18, 43], "faces": {"north": {"uv": [20, 20, 28, 26], "texture": 0}, "east": {"uv": [45.75, 11.5, 50.5, 14.75], "texture": 0}, "south": {"uv": [32, 20, 40, 26], "texture": 0}, "west": {"uv": [50.5, 11.5, 45.75, 14.75], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "aca4db5f-1770-9ff1-e36d-a1c892ebb74b"}, {"name": "body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 19.5, -2], "to": [4, 26.5, 2], "autouv": 0, "color": 9, "inflate": 0.01, "origin": [0, 0, 0], "uv_offset": [0, 10], "faces": {"north": {"uv": [20, 20.5, 28, 26.5], "texture": 0}, "east": {"uv": [16, 20, 20, 26], "texture": 0}, "south": {"uv": [32, 20, 40, 26], "texture": 0}, "west": {"uv": [28, 20, 32, 26], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "a198947a-6a53-0362-db91-9044d8ef86dd"}, {"name": "body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 26.09105, -2.67257], "to": [3.5, 32.091049999999996, 2.32743], "autouv": 0, "color": 9, "inflate": -0.21, "rotation": [-25, 0, 0], "origin": [0, 26, 2], "uv_offset": [42, 113], "faces": {"north": {"uv": [22, 20, 22.5, 20.5], "texture": 0}, "east": {"uv": [16, 20, 20, 26], "texture": 0}, "south": {"uv": [32, 20, 40, 26], "texture": 0}, "west": {"uv": [28, 20, 32, 26], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "2978f78f-31a0-464b-a2da-60ce9807a62a"}, {"name": "body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 26.09105, -3.4225700000000003], "to": [0, 29.341049999999996, 1.8274299999999997], "autouv": 0, "color": 9, "rotation": [65, 0, 0], "origin": [0, 26, 2], "uv_offset": [0, 65], "faces": {"north": {"uv": [20, 20, 28, 26], "texture": 0}, "east": {"uv": [50.5, 11.5, 45.75, 14.75], "texture": 0}, "south": {"uv": [32, 20, 40, 26], "texture": 0}, "west": {"uv": [45.75, 11.5, 50.5, 14.75], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "728d4682-b685-d814-98f1-493e125a8294"}, {"name": "bone3", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 12.75, -2], "to": [3, 17, 1.75], "autouv": 0, "color": 0, "inflate": 0.1, "origin": [0, 0, 0], "uv_offset": [14, 58], "faces": {"north": {"uv": [20, 26, 28, 32], "texture": 0}, "east": {"uv": [16, 26, 20, 32], "texture": 0}, "south": {"uv": [32, 26, 40, 32], "texture": 0}, "west": {"uv": [28, 26, 32, 32], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "371948df-1c37-20e5-33ae-5862ac68ee80"}, {"name": "rightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.5, 18, -2], "to": [6.5, 26, 2], "autouv": 0, "color": 6, "inflate": -0.2, "origin": [0, 0, 0], "uv_offset": [60, 5], "faces": {"north": {"uv": [44, 23.5, 48, 26.75], "texture": 0}, "east": {"uv": [40, 23.5, 44, 26.75], "texture": 0}, "south": {"uv": [52, 23.5, 56, 26.75], "texture": 0}, "west": {"uv": [48, 23.5, 52, 26.75], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "dd1edc37-9113-f9a1-1b28-16c1ad5c2172"}, {"name": "rightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.5, 22, -2], "to": [6.5, 26, 2], "autouv": 0, "color": 6, "origin": [0, 0, 0], "uv_offset": [66, 40], "faces": {"north": {"uv": [44, 20, 48, 23.25], "texture": 0}, "east": {"uv": [40, 20, 44, 23.25], "texture": 0}, "south": {"uv": [52, 20, 56, 23.25], "texture": 0}, "west": {"uv": [48, 20, 52, 23.25], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "b7ce10a1-8f21-d0b8-47e9-75352de409bf"}, {"name": "rightForearm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3, 11, -2], "to": [7, 19, 2], "autouv": 0, "color": 7, "inflate": -0.2, "origin": [0, 0, 0], "uv_offset": [46, 53], "faces": {"north": {"uv": [44, 27, 48, 32], "texture": 0}, "east": {"uv": [40, 27, 44, 32], "texture": 0}, "south": {"uv": [52, 27, 56, 32], "texture": 0}, "west": {"uv": [48, 27, 52, 32], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "bcbf6796-b840-4884-21fe-e199c829d376"}, {"name": "rightForearm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3, 15, -2], "to": [7, 19, 2], "autouv": 0, "color": 7, "inflate": -0.1, "origin": [0, 0, 0], "uv_offset": [62, 49], "faces": {"north": {"uv": [44, 26, 48, 29.25], "texture": 0}, "east": {"uv": [40, 26, 44, 29.25], "texture": 0}, "south": {"uv": [52, 26, 56, 29.25], "texture": 0}, "west": {"uv": [48, 26, 52, 29.25], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "1798a853-43eb-37f6-f628-2dd6c1d70ebf"}, {"name": "lead_hold", "position": [7, 9, 0], "rotation": [0, 0, 0], "ignore_inherited_scale": false, "visibility": true, "locked": false, "uuid": "4c9be5bb-59ca-14ff-d20c-7f5ef70ff160", "type": "locator"}, {"name": "leftArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 18, -2], "to": [-3.5, 26, 2], "autouv": 0, "color": 4, "inflate": -0.2, "origin": [0, 0, 0], "uv_offset": [58, 61], "faces": {"north": {"uv": [36, 55.25, 40, 58.75], "texture": 0}, "east": {"uv": [32, 55.25, 36, 58.75], "texture": 0}, "south": {"uv": [44, 55.25, 48, 58.75], "texture": 0}, "west": {"uv": [40, 55.25, 44, 58.75], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "ef0a73f9-506f-79db-9ed3-74c04aa7b626"}, {"name": "leftArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 22, -2], "to": [-3.5, 26, 2], "autouv": 0, "color": 4, "origin": [0, 0, 0], "uv_offset": [24, 68], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 0}, "east": {"uv": [32, 52, 36, 64], "texture": 0}, "south": {"uv": [44, 52, 48, 64], "texture": 0}, "west": {"uv": [40, 52, 44, 64], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "5923bb8c-297d-1657-ae66-1b32bfc0f614"}, {"name": "leftArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 23, -2], "to": [-3.5, 26, 2], "autouv": 0, "color": 4, "inflate": 0.1, "origin": [0, 0, 0], "uv_offset": [24, 68], "faces": {"north": {"uv": [36, 52, 40, 55.5], "texture": 0}, "east": {"uv": [32, 52, 36, 55.5], "texture": 0}, "south": {"uv": [44, 52, 48, 55.5], "texture": 0}, "west": {"uv": [40, 52, 44, 55.5], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "719a9240-41bb-1446-67d9-716e391798c0"}, {"name": "leftForearm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 15, -2], "to": [-3, 19, 2], "autouv": 0, "color": 5, "inflate": -0.1, "origin": [0, 0, 0], "uv_offset": [40, 65], "faces": {"north": {"uv": [36, 58, 40, 61.5], "texture": 0}, "east": {"uv": [32, 58, 36, 61.5], "texture": 0}, "south": {"uv": [44, 58, 48, 61.5], "texture": 0}, "west": {"uv": [40, 58, 44, 61.5], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "11b8da7c-f5b2-bc76-11a8-909dd3768e1a"}, {"name": "leftForearm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 11, -2], "to": [-3, 19, 2], "autouv": 0, "color": 5, "inflate": -0.2, "origin": [0, 0, 0], "uv_offset": [50, 41], "faces": {"north": {"uv": [36, 59, 40, 64], "texture": 0}, "east": {"uv": [32, 59, 36, 64], "texture": 0}, "south": {"uv": [44, 59, 48, 64], "texture": 0}, "west": {"uv": [40, 59, 44, 64], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "1a7e9d3d-cd60-af42-c690-64c6e7e462ef"}, {"name": "leftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.2, 0, -2], "to": [-0.20000000000000018, 8, 2], "autouv": 0, "color": 3, "origin": [0, 0, 0], "uv_offset": [56, 28], "faces": {"north": {"uv": [20, 58.5, 24, 64], "texture": 0}, "east": {"uv": [16, 58.5, 20, 64], "texture": 0}, "south": {"uv": [28, 58.5, 32, 64], "texture": 0}, "west": {"uv": [24, 58.5, 28, 64], "texture": 0}, "up": {"uv": [24, 52, 20, 48], "texture": 0}, "down": {"uv": [28, 48, 24, 52], "texture": 0}}, "type": "cube", "uuid": "edaed724-f0bb-853a-d12d-a6bc7fb010a8"}, {"name": "leftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.2, 0.5, -3.5], "to": [-1.2000000000000002, 0.5, -1.5], "autouv": 0, "color": 3, "origin": [0, 0, 0], "uv_offset": [10, 21], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 0}, "east": {"uv": [16, 52, 20, 64], "texture": 0}, "south": {"uv": [28, 52, 32, 64], "texture": 0}, "west": {"uv": [24, 52, 28, 64], "texture": 0}, "up": {"uv": [24, 52, 20, 48], "texture": 0}, "down": {"uv": [28, 48, 24, 52], "texture": 0}}, "type": "cube", "uuid": "13ced60b-8437-2eeb-e5f2-ee062fc1ee7c"}, {"name": "leftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.4, 0, -4], "to": [-0.3999999999999999, 1, -2], "autouv": 0, "color": 3, "inflate": 0.1, "rotation": [0, -5, 0], "origin": [-3.3, 4, 0], "uv_offset": [0, 43], "faces": {"north": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "east": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "south": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "west": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "up": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "down": {"uv": [26.5, 62, 26.5, 62], "texture": 0}}, "type": "cube", "uuid": "0377f666-c1c9-08cd-f4e0-34d5b425bca4"}, {"name": "leftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 0, -4], "to": [-3, 1, -2], "autouv": 0, "color": 3, "inflate": 0.1, "rotation": [0, 5, 0], "origin": [-1.3, 4, 0], "uv_offset": [10, 41], "faces": {"north": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "east": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "south": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "west": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "up": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "down": {"uv": [26.5, 62, 26.5, 62], "texture": 0}}, "type": "cube", "uuid": "64025c46-f808-3409-d26e-93eca10a78b8"}, {"name": "leftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.2, 3, -2], "to": [-0.20000000000000018, 17, 2], "autouv": 0, "color": 3, "inflate": -0.3, "origin": [0, 0, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 52, 24, 57.5], "texture": 0}, "east": {"uv": [16, 52, 20, 57.5], "texture": 0}, "south": {"uv": [28, 52, 32, 57.5], "texture": 0}, "west": {"uv": [24, 52, 28, 57.5], "texture": 0}, "up": {"uv": [24, 52, 20, 48], "texture": 0}, "down": {"uv": [28, 48, 24, 52], "texture": 0}}, "type": "cube", "uuid": "e8e13b28-9320-243d-aa5d-188e96baa50a"}, {"name": "rightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.20000000000000018, 3, -2], "to": [4.2, 17, 2], "autouv": 0, "color": 8, "inflate": -0.3, "origin": [0, 0, 0], "uv_offset": [0, 21], "faces": {"north": {"uv": [4, 20, 8, 25.5], "texture": 0}, "east": {"uv": [0, 20, 4, 25.5], "texture": 0}, "south": {"uv": [12, 20, 16, 25.5], "texture": 0}, "west": {"uv": [8, 20, 12, 25.5], "texture": 0}, "up": {"uv": [8, 20, 4, 16], "texture": 0}, "down": {"uv": [12, 16, 8, 20], "texture": 0}}, "type": "cube", "uuid": "f5296969-1c19-4b3b-5502-e4543d6e1eb6"}, {"name": "rightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.20000000000000018, 0, -2], "to": [4.2, 8, 2], "autouv": 0, "color": 8, "origin": [0, 0, 0], "uv_offset": [28, 56], "faces": {"north": {"uv": [4, 26.5, 8, 32], "texture": 0}, "east": {"uv": [0, 26.5, 4, 32], "texture": 0}, "south": {"uv": [12, 26.5, 16, 32], "texture": 0}, "west": {"uv": [8, 26.5, 12, 32], "texture": 0}, "up": {"uv": [8, 20, 4, 16], "texture": 0}, "down": {"uv": [12, 16, 8, 20], "texture": 0}}, "type": "cube", "uuid": "363b70b0-d9db-3f20-bc02-72196d24c772"}, {"name": "rightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.2000000000000002, 0.5, -3.5], "to": [4.2, 0.5, -1.5], "autouv": 0, "color": 8, "origin": [0, 0, 0], "uv_offset": [10, 23], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 0}, "east": {"uv": [0, 20, 4, 32], "texture": 0}, "south": {"uv": [12, 20, 16, 32], "texture": 0}, "west": {"uv": [8, 20, 12, 32], "texture": 0}, "up": {"uv": [8, 20, 4, 16], "texture": 0}, "down": {"uv": [12, 16, 8, 20], "texture": 0}}, "type": "cube", "uuid": "39b7bd97-6e1d-5470-f96f-da6a01c4e86f"}, {"name": "rightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3, 0, -4], "to": [4, 1, -2], "autouv": 0, "color": 8, "inflate": 0.1, "rotation": [0, -5, 0], "origin": [1.3, 4, 0], "uv_offset": [36, 16], "faces": {"north": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "east": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "south": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "west": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "up": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "down": {"uv": [26.5, 62, 26.5, 62], "texture": 0}}, "type": "cube", "uuid": "184d1eec-7169-564d-59a5-5d8e745130b1"}, {"name": "rightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.3999999999999999, 0, -4], "to": [1.4, 1, -2], "autouv": 0, "color": 8, "inflate": 0.1, "rotation": [0, 5, 0], "origin": [3.3, 4, 0], "uv_offset": [36, 1], "faces": {"north": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "east": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "south": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "west": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "up": {"uv": [26.5, 62, 26.5, 62], "texture": 0}, "down": {"uv": [26.5, 62, 26.5, 62], "texture": 0}}, "type": "cube", "uuid": "4f204e85-0892-beda-131a-3f8bcf2f0dcc"}], "outliner": [{"name": "armorHead", "origin": [0, 26.5, -0.25], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "no_fusion", "origin": [0.5, 2, 0], "color": 0, "uuid": "f23c845c-6a0a-a9fb-2d9c-078e0518e8b5", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["449bf602-c15c-386f-c903-bce3bf871c6d", "e4cd28cf-ea01-4e74-0c3f-83f008279a2c", "ff7dfe23-5677-79d3-c6d4-c2d286b8df28", "8fa33568-9f8e-d540-7030-f9c4d61d9edf", "553f1eb4-b245-29fa-1693-7c7cc98b46d5", "a48449e6-34d1-998f-f274-6d7d497c18c7", "a4fa0893-7438-3bda-edef-e1f75de986fa", "8d4dc996-e59a-c465-7c6b-7aa943bf439c", "9704859e-8cd9-a6da-762d-4b609f52d016", {"name": "bone5", "origin": [0, 28.5, -2], "color": 3, "uuid": "ba01e92d-5294-eee1-3cd0-7c25f7a8530a", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["17d774b8-07cd-9296-faa4-502151dbb2a7", "a21fbdd7-137c-e5b5-d2b6-5003926c6e12", "b0e66a3d-1909-a646-92b8-8e98ce97eba4", "a7c1d1be-1feb-02ab-8c19-99f1c3515cf1", "ee78656d-becb-1382-3d1d-b3759bd035f6", "f28720f9-2fbf-b254-4b8b-cdd01d0342f9", "a4fd14e1-dc5a-2890-3030-73dad6470695", "db911a6e-6bb6-8fb3-51c1-b8344832affc"]}, {"name": "antenna", "origin": [0, 1, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["afceed65-4915-9b6d-f238-1e72f793abdd", "1897224a-7cb1-0b63-ddb3-e5ca2e54f27c", "701d66cf-9634-843b-5503-373fba834338"]}]}, {"name": "opticoid_head", "origin": [0.5, 1, 0], "color": 0, "uuid": "793cbcdd-59e2-f646-e62c-d4ab7f376ea1", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["7c1d65f2-1a2b-08d3-4462-e674dcf57a24", "7042fc4a-54b9-1b56-3c69-9f64c38df94b", {"name": "antenna2", "origin": [0, 0, 0], "color": 0, "uuid": "726b4754-56d1-2c4c-f56b-0e36bc5cd405", "export": false, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": false, "autouv": 0, "children": ["373ee4f5-abcc-6d7c-1908-5b2bcf834aee", "93c955dd-b714-b8e1-fdcc-05616ef218e9", "d9b443db-8c5f-ce24-6c90-850dc0fd8822"]}]}, {"name": "methanosian4", "origin": [4.440892098500626e-16, 28, 0], "color": 0, "uuid": "2ed1d400-ff5e-3796-4c54-6bfcb0749e88", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["84e794ce-961b-74cb-5899-e101ed3c2126", "0aa1d76b-8125-96bb-feb6-9f982f2d6c1a", "a19a98d9-1d09-79a9-fefe-6e66b782d0f7", "df63177f-70cd-af0f-8a1a-5ec31dd1fa75", "3a0df523-c55e-3afc-3881-d58b6a97fe73", "119ad3a6-a650-cb58-6610-18caf0e25e9e", "4de24bba-8092-ff3d-6770-0aaf12845ca4", "971af909-2fc2-8603-64fb-470fb250e6b1", "85d470d7-e12e-0172-130b-098cc397a8ce", "1a40dbcf-da8b-5956-388f-a794fd8b1752", "cba815f2-a068-3e4c-7241-9f43c4d86f65", "d297d7b2-fcae-d7d6-52d5-0a418b4e14e7", "ba05dd01-b647-2f07-40fb-08310b646205", "5137ad69-cd00-d843-a0fb-670fb83bd43a", "81b2f150-d3b6-e779-6868-7e03cda76338"]}, {"name": "polymorph", "origin": [0, 27, 0], "color": 0, "uuid": "b0c4b352-853a-ea95-ebc4-4cbe4939e403", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["3412592f-6338-07ca-60a4-d1cc4be32cdd", "d8f3a281-1a43-6fbc-706f-1d481225d415"]}, {"name": "opticoid", "origin": [0, 27, 0], "color": 0, "uuid": "f59fa78a-4645-091a-5605-84608198fec8", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["9cd5a16d-cac8-337c-87d5-4859c0a4165f"]}]}, {"name": "armorBody", "origin": [0, 21, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "no_fusion2", "origin": [0, 1, 0], "color": 0, "uuid": "e193e2f8-e0a4-2d1e-4966-1d1eb46efc2a", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["42a75968-003f-247e-068d-61d5de739341", "4dc63ad5-491d-df17-229e-7b022cf6002c", "adc4c678-ea18-873e-85a0-0215c1e8de64", "aca4db5f-1770-9ff1-e36d-a1c892ebb74b", "a198947a-6a53-0362-db91-9044d8ef86dd", "2978f78f-31a0-464b-a2da-60ce9807a62a", "728d4682-b685-d814-98f1-493e125a8294", "371948df-1c37-20e5-33ae-5862ac68ee80"]}, {"name": "methanosian", "origin": [-0.5, 1, 0], "color": 0, "uuid": "c012672d-d14c-ec21-eee4-3cd8db77ace8", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["850d310d-d691-5416-daa7-1a5b1059677e", "557862b0-a0da-be82-e063-6004bf857d65", "6300eda9-b246-dc5d-b0ef-f96509b0b936", "91b8fcb0-47fc-8bac-ff45-4ed330b00397"]}, {"name": "prypiatosian_a3", "origin": [0, 25, 0], "color": 0, "uuid": "728503a2-0e53-93f1-e445-67c841397d8c", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["17570ecb-0680-1ea4-a159-8dc272fbd2fc", "30b87036-38a5-0885-d0f9-55c00f7c4a68", "f6129da9-db0d-a488-d52c-7c62d00cd9f6", "0ea49582-abf8-9b9a-de04-b68065b02974"]}, {"name": "opticoid2", "origin": [0, 25, 0], "color": 0, "uuid": "8bd46185-e76b-db3e-069c-c98b469ceb94", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["dd0a7062-5498-217a-54f8-3d66340bbd1e", "961f0ccb-a84d-7e4c-6a5c-b722ef3c9c18", "a08f6fb1-1fed-2df0-5572-953bed22500f", "7a55e1a2-9ceb-a7e9-745c-b36b002945ea"]}]}, {"name": "armorRightArm", "origin": [4, 25.75, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "methanosian2", "origin": [1, 1, 0], "color": 0, "uuid": "4770cef2-9e90-9ad7-a68a-c8c87caeadf8", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["a7302fb4-6503-6de2-1971-33518643497b", "4e1e497f-4726-351e-a876-2717f9bb75fa"]}, {"name": "no_fusion3", "origin": [1, 1, 0], "color": 0, "uuid": "f0c4a39e-d927-5cda-2be8-612c8504fe72", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "rightArm", "origin": [4, 25, 0], "rotation": [-2.5, 12.5, 10], "color": 6, "uuid": "f8b89307-eff4-882b-0ae7-e239129d1aae", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["dd1edc37-9113-f9a1-1b28-16c1ad5c2172", "b7ce10a1-8f21-d0b8-47e9-75352de409bf", {"name": "rightForearm", "origin": [5, 19, 0], "rotation": [12.5, 0, -7.5], "color": 7, "uuid": "945b8647-35e1-a8dd-ac87-3b111db039d2", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["bcbf6796-b840-4884-21fe-e199c829d376", "1798a853-43eb-37f6-f628-2dd6c1d70ebf", {"name": "rightItem", "origin": [7, 9, 0], "color": 1, "uuid": "35f94431-ebfc-7fd1-39af-188154054824", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["4c9be5bb-59ca-14ff-d20c-7f5ef70ff160"]}]}]}]}, {"name": "prypiatosian_a", "origin": [6, 23, 0], "color": 0, "uuid": "88a2120f-89d5-993d-9617-ed3edf233f6d", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["73e6114b-9b8a-e056-9fae-0bc5af921b33", "c76a7a12-9c4e-ba09-111b-c23c8ca0b2df", "cea98f2c-444a-7710-af78-c8aafc6d6bab", "1c5f496f-f9ba-bc47-00fe-1bf13dec37e4", "b9cc816f-166c-bbb5-ba08-9555eb6758c1", "41b740cd-10ec-b3ec-3fa3-ca3744f70497"]}, {"name": "opticoid3", "origin": [1.9, 12, 0], "color": 0, "uuid": "057c5a0b-d20a-5ff8-4b62-281803baf69a", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["4954c74c-88cf-9c10-792a-88dc170439f0", "52a39933-b0f1-97d1-9e9d-d799969cada6"]}, {"name": "polymoprh", "origin": [6, 24, 0], "color": 0, "uuid": "2b6fa759-a8a5-fed8-6533-36e046cf41c3", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["c8cb4826-182f-3b1b-b062-7c16e844c283"]}]}, {"name": "armorLeftArm", "origin": [-4, 25.75, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "no_fusion4", "origin": [-5, 23, 0], "color": 0, "uuid": "a8fb880a-e188-423f-9741-cfaf779532c8", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "leftArm", "origin": [-4, 25, 0], "rotation": [-2.5, -12.5, -10], "color": 4, "uuid": "f7921fab-faf1-4b74-ec43-d9580fdef174", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["ef0a73f9-506f-79db-9ed3-74c04aa7b626", "5923bb8c-297d-1657-ae66-1b32bfc0f614", "719a9240-41bb-1446-67d9-716e391798c0", {"name": "leftForearm", "origin": [-5, 19, 0], "rotation": [10, 0, 7.5], "color": 5, "uuid": "38c2659b-9469-7fa5-6bb7-4973c9fb5007", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["11b8da7c-f5b2-bc76-11a8-909dd3768e1a", "1a7e9d3d-cd60-af42-c690-64c6e7e462ef", {"name": "leftItem", "origin": [-7, 9, 0], "color": 2, "uuid": "1019c337-79a8-2c33-b7fb-3a0bf479b783", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}]}]}]}, {"name": "methanosian3", "origin": [-5, 23, 0], "color": 0, "uuid": "36a41923-cfaa-434b-e600-6548e01c5cda", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["2a20268f-ac50-b135-de1b-f8b5095e8faa", "f7e71384-83f7-4c95-3bcc-c4f86725af5d"]}, {"name": "prypiatosian_a2", "origin": [-5, 23, 0], "color": 0, "uuid": "f77ad5a9-61b1-b334-ab19-433449b56650", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["c8dac093-351f-0f2f-f0a8-835e104cb360", "bfa7635e-99f2-95ee-4839-1a26da0f6c14", "5be098a4-8213-38eb-c336-caae61b5c198", "d29024b9-9896-24d7-a741-ed626e3d307d", "065d74a0-a6a9-b3ab-3dae-47298eedaddb", "c471ccd4-2a3c-53c4-5a83-92d9b97efba0"]}, {"name": "opticoid_arm", "origin": [-5, 23, 0], "color": 0, "uuid": "d0a8f26e-e127-4652-d04d-c3d7fb24f313", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["dab536d8-4ad3-b374-0d81-13d675ccb50e", "2928b0ec-ebb3-0284-91ff-f38b134e1227"]}, {"name": "polymorph2", "origin": [-5, 23, 0], "color": 0, "uuid": "dcd9375b-b7b4-5058-1179-c201e75c7cf2", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["bb2d287d-b46e-e67c-0819-58d4380ae820"]}]}, {"name": "armorRightLeg", "origin": [2.65, 16.75, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "prypiatosian_a4", "origin": [0.09999990463256836, 0, 0], "color": 0, "uuid": "82f5e1a5-d3fb-65d2-2906-5247b2d821c5", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["d222714e-943b-2ab4-4205-6d9941ce1121", "ab0b9301-bc8d-e71a-0d0b-7913410f6245"]}, {"name": "no_fusion6", "origin": [1.9, 12, 0], "color": 0, "uuid": "17029f93-b43b-5b8a-a2c6-a31572ef2baf", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "rightLeg", "origin": [2.2, 16, 0], "color": 8, "uuid": "7a15aed2-f67c-0cd7-2af9-11767e15ee77", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["f5296969-1c19-4b3b-5502-e4543d6e1eb6", "363b70b0-d9db-3f20-bc02-72196d24c772", "39b7bd97-6e1d-5470-f96f-da6a01c4e86f", "184d1eec-7169-564d-59a5-5d8e745130b1", "4f204e85-0892-beda-131a-3f8bcf2f0dcc"]}]}, {"name": "opticoid_leg2", "origin": [0.09999990463256836, 0, 0], "color": 0, "uuid": "0716fb62-fd6e-e562-8f0e-39d95a1946b6", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["c84be484-afde-1d83-86f2-02b77f003e40", "2906fb46-937d-536a-3868-8f52f9202c99"]}]}, {"name": "armorLeftLeg", "origin": [-2.4, 16.549999999999997, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "no_fusion5", "origin": [-0.09999990463256836, 0, 0], "color": 0, "uuid": "0f697604-daf8-ff23-0692-763931c5d96f", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "leftLeg", "origin": [-2.2, 16, 0], "color": 3, "uuid": "b0228974-9e53-c739-a6b7-a92bfe226f72", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["edaed724-f0bb-853a-d12d-a6bc7fb010a8", "13ced60b-8437-2eeb-e5f2-ee062fc1ee7c", "0377f666-c1c9-08cd-f4e0-34d5b425bca4", "64025c46-f808-3409-d26e-93eca10a78b8", "e8e13b28-9320-243d-aa5d-188e96baa50a"]}]}, {"name": "prypiatosian_a5", "origin": [-0.09999990463256836, 0, 0], "color": 0, "uuid": "1065fbaf-88f2-c94b-4d2f-2f7c8392048c", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["8e8404ba-2ce6-fd7c-e6f8-8e0f3a2ed172", "831470fe-9617-7f94-f464-02821ff58cd1"]}, {"name": "opticoid_leg", "origin": [-0.09999990463256836, 0, 0], "color": 0, "uuid": "4383e06e-9100-67b9-3538-8b85a09c3d4d", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["c0d406c9-5ad4-6979-5424-12cb3b063aa9", "9919b16b-f9ba-72b9-f558-20129c04ce66"]}]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_piscciss_volan.png", "name": "ov_piscciss_volan.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "df979881-803b-89ad-bc48-91cb4c26dc2f", "source": "data:image/png;base64,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", "relative_path": "../curseforge/minecraft/Instances/Testeos (2)/addonpacks/MOONS_OMNITRIX_ADDON_1.05/assets/m_ten/textures/models/ben/ov_piscciss_volan.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\5yl\\piscciss_volann.png", "name": "piscciss_volann.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "444e064a-53ca-70a2-594d-316bcf74ca41", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/5yl/piscciss_volann.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\timeout\\piscciss_volan_water_timeout.png", "name": "piscciss_volan_water_timeout.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "3e3ecdc9-d848-9da8-f3df-2e6d228068a8", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/timeout/piscciss_volan_water_timeout.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\timeout\\piscciss_volan_timeout.png", "name": "piscciss_volan_timeout.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "a3b9c581-0e66-4f6f-2940-8e4cb042d058", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/timeout/piscciss_volan_timeout.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\recalibrated\\r_piscciss_volan_water.png", "name": "r_piscciss_volan_water.png", "folder": "", "namespace": "", "id": "5", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "528b10db-bca3-63bc-2af6-9943c4fe7755", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "selection", "offset": [224, 74], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 33, "height": 45, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAtCAYAAAAgJgIUAAAAAXNSR0IArs4c6QAAAMRJREFUWEdjZBgEgHEQuIFh1BGwWBgNidGQQM+RFKUJ/Qy9/8gGXpxxiSzzyNIEs3hAHYFuOXrwkhoiZIXEgDqCkOXkhghJITHqCFy5gVANTGwCHY0OiuoOQgmU2GgYdQRVKrBBER2DogIbdQR6ghrQ9sRodIxGB64qezRhjuYO9LRx880ycA8srKYDLEVqO4Ki9gRM86ByBMxR6iJRJLVZqRoSo46ApYkBDQlCnR9i5clKSMQaTqy6UUdQJYsSG9yE1AEA5BWGLq0+pycAAAAASUVORK5CYII="}], "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/recalibrated/r_piscciss_volan_water.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\recalibrated\\r_piscciss_volan.png", "name": "r_piscciss_volan.png", "folder": "", "namespace": "", "id": "6", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "bb0deacf-92ef-0834-c277-01840b879b9a", "source": "data:image/png;base64,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", "relative_path": "../curseforge/minecraft/Instances/Testeos (2)/addonpacks/MOONS_OMNITRIX_ADDON_1.05/assets/m_ten/textures/models/ben/recalibrated/r_piscciss_volan.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\piscciss_volan_water.png", "name": "piscciss_volan_water.png", "folder": "", "namespace": "", "id": "7", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "8cab9feb-b311-5b09-b375-8f764ede604a", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/piscciss_volan_water.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\piscciss_volan.png", "name": "piscciss_volan.png", "folder": "", "namespace": "", "id": "8", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "a8404154-c53a-ab4d-aff8-7a70ed0a03e9", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/piscciss_volan.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_piscciss_volan_water.png", "name": "ov_piscciss_volan_water.png", "folder": "", "namespace": "", "id": "9", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "9cab0785-da48-ba5a-1f77-2ecf9d93311d", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAHKZJREFUeF7tXX/MV9V5/74uiH+QrTpGGpM3II0YUYlQY7uGBelQSRBDfNfJEmbSZhW6TTvRmRIbW40NiytukyxTY2aykQzX4ohIQu07kZR0baNAsOJ8zaiEpGuY1W7hD5FUl577Pm+5D/f5nnPuc84959zvh3+A7z0/nvM5z/mcz3nOueeODRL/+f7rL3+U0oT9B19pVf3OXbtr+dZPrGtVzgMb7xtrlRGZgEAABJI7HwgABBDAj1FESwRAAC0VwHPP7zCQLxifa/5etnR1qy6AAmgFGzIFQgAEAAII5EoopkQEQAAtCQAxgBLdHTZzBEAAIACMihFGAATQkgAQAxjhUdOjpoMAQAA9cmc0xRcBEICFAA4fO9qI6ZtvHK/9fsWVCxvTLV28ZGifYBfA12WRPiQCIAAQQEh/QlmFIQACaEkArv0MBeCKFNKlQAAEAAJI4XeoMxMEQAAggExcEWakQAAEAAJI4XeoMxMEQAAggExcEWakQAAEAAJI4XeoMxMEQAAggExcEWakQAAEAAJI4XeoMxMEQAAggExcEWakQAAEAAJI4XeoMxMEQAAggExcEWakQAAEAAJI4XeoMxMEQAAggExcEWakQAAEAAJI4XeoMxMEQAAtLwQJ1X+4DyAUkiinDQIgABBAG79Bnp4gAAIAAfTEldGMNgiAAEAAbfwGeXqCAAgABNATV0Yz2iAAAgABtPEb5OkJAiAAEEBPXBnNaIMACAAE0MZvkKcnCIx948lvfpRDW1Yuv65TM/YnHvidNnZIZTiHkEtPpLEDBJAG92xqBQFk0xVJDMmGAKj1sZVAX2b+0+8erDnMnEuWt3IgEEAr2HqTCQRQaFeCAArtuMzMzo4AYimBvsz8hA8IILORVKg5IIBCOw4EUGjHZWZ2cgJ47OuPDoVkz+S/qiBbu+oPh+bf/PX7VeV3nfnQ4X2mykXz59Sqnjpx2vx/2dLVXiYhBuAFV+8SgwBAAMnPgvRuVBXUoGwI4N6dn2iEbdv6/zK/+yoBmvlt5ZamAHbu2j3UvdZPrPNyPygAL7h6lxgEUJgCAAH0bgwmbVA2BEAo8Bk7tAKg8qi+UhQADfxFCy8aGgOYOv6+ee6qBKAAko6/5JWDAApRACCA5GOllwYkJwCOqrQr0DYGwMsvZcbndj/3/A7z0wdn69F/nu7CWdVuwG23bnByWCgAJ5h6mwgEUEjXggAK6ajCzExOAHT2f84v/8dAd/o3fqcRQv7cNz0VyvOVckIQS4DCRlYh5oIACnktGARQyIgqzMzOCWDt7843EO35jxPmb64ACD9SAjRja3+XFACVT/bk2n+5LgHoPgnqx89cdcPQg0VHj37L3D+xZMnnaun27tlufh+f/3HTBfw5z0fp33vvnSr9tVfX8lH6kyd+Zn7n5Up22Pr/+6+/bOwk5Vh6DAUEML30AAHYXL/5OQjgvqJPUkYnAD7j0/+1M3ooZcDLISLgdrcbHuFy5bYE4AP/saf+sdbYb//dP5mBYZu5L754bi0fzei+v1MhlE8qZ83au4xdXEFsuOOh2kDmdv/Bl++o3Zy1+c4vmCpLVwIggGkFQA4EAnAjLRAACGCop/CZng+sUhQAJwa34RE+FcUAblixxhT+4uSBWiU3rVph/v/ygb3m79jnAO599AEzI06subGxse8df838zmfiQ4ffML9ftqDa7aEZm37/xUUXm98/9v575u9lS69sLIcqDVW+pAzIvosXXtPYzl17v2t+33b/N4pcCkRTACCAsCQAAqiCfbalQVuCAQFY/PXwsaMmxdLFS5w8e9QIwBcfJxDPSfTu/1Yz4iW/Vc2QRAgLxqs1NN0DwNPZ6mkbxSYFQOVLSoBmSJrRKT3N9FI+Svfvu6r7ICRl8NIbVZRfKp9+//2J5nshpPJt9lG7yM7eKwBfBwcB2Iae33MQQPPSAATg50c8tbgE4AM+FAFwA7o6B+C6ayDFKmzbhFq8fLuRvxbs+vYfryeUArApAZpppZnYt/2hy3Otn8/8vVUAWoeWFAAIwNXVhqcDAVRLg1CE4torI0sABJCvErABG/s7ALb6pXcBXAlMwiM0TrwdUgzA1t5QCoDK4bEAqX7bWt/X7tjppYHO6y117U/tcF4CgACqo8v8Dwig2g60/QEB2BBK89y6DahdCqRpVne1psIn9RIgNML8YFHo8qm80k/uhcYFBKBEFASgBHA6OwggDI6+pXgTQKylgK/hqdOnkv7U7lxiALH6IRQhYMYf3kMggJYeDAJoCZxjNhCAI1DKZOrzy7Nnz3IKAkl2njlzVm2DEgNVdnJUVSGKzA/fvUWRW581df+NX7ZE5X/PvvB4DQRpV0i6q/Ln/32qaP9VGw8C+KbKAbVDEAQAAtD4EAhAg95gMIACSKvgoAB0DgwC0OEHAki8hAMB6BwYBKDDDwRQOAFcf/M8lQfsemJSPYZUBigzq41HDAAxAKUPqrJrFQAIQAX/YAACAAEoXUiVHQSggm8ABaDDD0sALAHUY0jpgqrsauOhAKAAVB6ozKxVADgHoOwAEAAIQOlCquwgABV8WALo4MM5AJwExElA1Um41A4EAtAhkLr/oAB0/YcYgA4/BAERBFSPIaULqrKrjUcMADEAlQcqM2sVAM4BKDsABAACULqQKjsIQAUfgoA6+BAELD0GMPIKQMugzz73tcYxtPLT67VjK2r+eZdW35w79dPqW3XaP2s3/ZEpgr6cxG87pq/n2r6sRBeN0NdnyS56T52e73niX7Qmq/IvWvGpWv6pAz9sVR71Q6vMATJp+19r/8mfHFUvwzUwjIEAQABtHAgEUKE28gTAnYcYdf8Pdjr5VSqlEFoBUGO5EqDfSRGQEpDA4d+dp3S5zPy3fL76CvGi+XPqCuDEafP/F56pf7XY5gTaAWQrXzvD28q3Pbe1T6MAJjatqm3Bt3kzUa0AQAB1BEAAIIBzPWJkCcDGjKmfx1IA+w9V34vnM70UG+A48LU+PSdlsHLZjUmh49KfG+MbC7ANkLaNTT3zc7uldrZRAHzm53X5KIFoCqBtx3WVDwTQDmkQQDvcRoYACJ7cGJjs4h0Ry05SAlQv3wXguwR85ue7ALnM/IsWXjQ0BjB1/H3z3FUJhFYAsfqz3bA/Pxdvr48C2LJ1nVnzT03HWySbKD6zdctu6w5DcAUAAqgQAAG4bQuCANy3AbMmgFLOA3SlAM5b2wuxAZ4ul7U+t+vqVVeZnz44W4/+83QXzqp2A348+brTpNkVAXBCJuOk7wA4GT8YDKSvW0uKrbcKAAQw3GWk4CAIoDqQFeqPtAQAATQjHH0JYDsP0PU5AM7AdCNM25mAzwC2tbrkiNQ9vvlD2W0bgBvv+apJsn5infn79LsHa1nmXLLc/J++Wvzk3zxiK9I8v/2Wu53S2RLxgf/g41tNFmmGjq0AuL28X8kPex8DAAHUXQEEUMcDBNCTGABnvFyjsZIC0M4IvmtB24w2Y8907ICn72rm57jQDC/ZTwrBNvNS/tAE4Drza/t7RrEtv64Rim3bKwV0712VcppJP32Oo40CoDKkYKBP9J/Kir4EcHX0rtKBAJod1oY/EQ4IoI6URHQggEBv2dkc0/e5jQC0M0MoJRA6aOU6I9vwJMeW3gXgM56tvLZKxlaua3tD1c/rI5zoXATFRHh9D2y8z7pXz9sKBWDr/SHPQQAK8AaDAQigGT8QAMOl1BiApADobTz+Pr/0u7RGDBXl97XHdUa00QPtBkjpXKP/WqVls9O1vaEUALfn0OF95idSABQb4enaKAAqI8u3Ack4EECFhHabUHJQEMBwCgAB2Ciyeq4OAkoHgKTqu973JzukE2f8yzDcbuntPEkJuMGuT2Wr3/XtQ1dL7rr/L0zSBeNzzd9SDODtk++Y59sf/VunomPNwKL/sah96PopSErvTJACoP8vW7q6ZlrxCgAEUPUnzchOXh8gEQigHYjSS1jtSjs/18gRgASceCST3RTUlSLQKgDeTj7g+YB0laCujsdnKlv9VG4oO3hUW7KbZjrX3YDQMzDZJRGy652MvH22fJS+SwKg3QCq2+XtP94u9RIABFAhAAKocAAB7K7hEHMJUCQBuM54odO1VQBkh+1WXz7j8Pf5te2RbhiSlEno+vm7AM89v8NUTTEBWtvm8i6AdI7Cdiej60zPcedRfykGQITwi7f+zxTh8y4ArxME4DGqQAAeYDUkBQEMx683BLDhzoVDW7rjqeNOnpTbNqCWAFx3A6TvATiB1pBI+j6AtNYNHf3nJklHgn3fAaByQ78LcJ69wrsUlC5UDIIUEd2XwBUA3ZNAz7NVACCA5qEKAqhwAQE0+0exBMDXEb4z1drVfzw0S1dRfpvdWgXAZwwpykwzti1mYLOXz/C2LwV1NfPztT/ZSfv/t926wfzkuvsQWwHM9Fvgtyqp3Ifv3mL+yW9M4jN+tgoABPC461iszYAggOpAEAigcALg54n5CS8+OqQbSX/0nVONAymXq8K4AqCPQrruV9vWjPzLP9KMbFtKuN4WLK1dXWdeL9YbDAa2dwB4ea7vBHSlAHzb2zY9XZsuKQC6K1FzHwDZdvajCXNL8IN/9UvzU6tzACCA+oUNto63DTzf67+pPlfCkD4dRuWAAGw9GPd5cQTwyTXX1r4vRvu6Eky01pOenzr2Ye2R7URgVzECYtx5iy+o2ed6Zp2313YyT9qH91UAVK/tHECsgc+VT6kxgLjD/vzSeUyAfychpAKg2meN7fK+W2AMBOD20goI4BUDAQjAjUqKIYBrblpeUwC0dpGaye+Fp/1Onp5iArmcB6Cv2nI7fWMAUizANgPbFIPvUWJenq1+N7e1pyptG9DeojQpslEAIAC/GAAIoFIC/G7AXA8CpRne9lqzIQBaAtDan84qS02gGd81FpCLAiBJxtvFYxb2rhuewrW9k4e+Vyto1bLfc6o69Jd0zsMj8l2Osex3xd0J5A4ShSCAEGbOxABAACHgHAyGOeKH9fioWOEF9ThlLV2sAUSVxB5IseyPbXcY7/h1KdkQAC0B+Nqf7wbwGd81FvDCMwdU2J05c3ZoZHP27Fm1GAavTFr7k9I59Yn/VNlHmX978cet5by1+U2T5qHJ5qu5v7aqkteXP3aFtayfH/uZNU2bBC7taFMu5eF2X7jvUk1xM3lBAO1gHAMBgADOdR0QQLuB5JsrGwVAB4H42p8rAmnGJ2XAn/P8rl+L5UC2VQB8zS/Z96Uv3uPbd43pr7i8PmtvfOlPaume/OzTg89e++t1PlcBNPv/KtNLR743aMp/boFvvlWpidB/eDtCl8/tfuiebUGr4ErA9ik27duAtt0X6ZuA1GjNfQAhgBsDAaQhgHOXAucOfhCAzq1BAH74zQQBX917xOsUEe0e8PefbdXzmIBthreVx2MANPPbTjSS4vnyn37RVoXTc5sC2Dz+l4Mvfa6uCkgFcAL4h289PXjs5F+fpyCgAOxdQQRAM792hrfXODwFKQRSAjwImlwB0EAGAei62pcAhi0BQADt+wIE4Ied16zfVLS0hKC0FAsgguEzdmgFQOVxhcJtp/MMB8eO+CEmpHYJnmEXYDDguwD/9uffDoI/L4RuMea/83v5+XO62ksyqm1+KZ/muwAhgAMBgABqfuRCZBrHAwHU0SueAGzbiLuemKyRTFcKgGAmhSKdY6C72TROfW5eHAQajiRfA2/6yh21DDQD0zsafA1ve05rbl8FYJv5XZWEVE5vFQAIoO4argdS/v7Zp2sZ/+z2eoBQGkaxTtJRfa72tyVMEEDPFEBbRyAlECoG0Lac8cuWDD1J6Nu+rgeQr3229F3bLykAspPPnHyG5c9tCsC1XBtONrtcFUPxSwAbUNJzEEA75EZFAbgOVBBAOz+iXOogoK565AYCQCAlAiCAlOijbiCQGAEQQOIOQPVAICUCIICU6KNuIJAYARBA4g5A9UAgJQIggJToo24gkBgBEEDiDkD1QCAlAiCAlOijbiCQGAEQQOIOQPVAICUCIICU6KNuIJAYARBA4g5A9UAgJQIggJToo24gkBgBEEDiDkD1QCAlAiCAlOijbiCQGAEQQOIOQPVAICUCIICU6KNuIJAYARBA4g4otXr+WfnXXjwIXyqwM9FpBXZaDiaDAHLoBb0NIAA9hiNVAt2yvGj+nFq7p06cNv/nt0CPFDgFNhYEUGCnpTQZBJAS/fB1gwDCY9rrErn0541FLKCs7gcBlNVfya0FASTvgqAGgACCwtnfwmjg0zcVpRgAfXUZSqAMXwABlNFPya0EASTvgigGgACiwNq/Qm1fW6YW869B9w+JfrUIBNCv/ozWGhBANGiTFgwCSAp/OZXTEoDW9lu2rqt9U3Hrlt3Gl3i6clo4mpaCAEaz371bDQLwhqyIDCCAIropHyOxDejWF3RgKveTkSAAt/5EqmkEQABurgACcMMJqQpDAEeB3TqMcMr9XAQUgFt/ItU0AiAAN1cAAbjhhFSFIYAlgFuHgQDccEKqwhAAAbh1GAjADSekKgQBOgi0YHyusVh6F+Dtk++Y56/uPTKSy0t+ZJpiAPQORW67AiPZSYWMuazMBAG4dQcIwA0npCoMAS5pJfNznem6ghsE0BXSqKdTBEAAbnCDANxwQqrCEOBHgfmSgNa2o/4ugEQAub4liRhAYQMxlbkgADfkQQBuOCFV4QjgWvDhHchfn871hiQogMIHYirzQQAggFS+h3oTIsDX/mTKqO//J+wSVdVQACr4Ri8zCKBffQ4C6Fd/RmuN7QgwrzjXNW80gAotGARQaMd1bTYIoGvEu6kPBNANzr2pBUuA3nSlaQgIoF/9Gb01IIDoEHdaAQigU7j7Uxm2AfvRlyCAfvRj560AAXQOeZQKx9peXkj5yKrc33vmL7OM+ltrUbwJhRaHAAjgiUmooOLcFgaHQmCMv7xgu7FEei2UZtRcT4Tx4FXut7WG6mCUAwSGIQACePEgFADGyMgiMLMEoBmRkKAZna/x+XOa8T84O6cGYi5rbEmx5Pp+9sh6IhqeBAEQwIheXpnE21BpdgiM0dqYLKNbXyVFQDM+paeZn2ZU3kIqzxZbCI0MzfzcXm43zqyHRh7llYQACAAxgJL8FbYGRmAmCEjl0kxOMzf9Lq31KT2PAVC+1DOs9BILYgCBPQnFFYkACAAxgCIdF0aHQWBmCcDX/nxNTzM83++n31PP9L5wjPrttb54IX0/EQABIAbQT89Gq5wQmFkCSDM+jw0giu6EKxIBgSIQAAEU0U0wEgjEQQDHYJW4zp496yNNEWfOnC26D8YvW6Jq/6av3OEF38rl19XSf+aqG4rGz6vxERIDPCWoIAAQgNKFkmYHASjhBwGAAJQulDQ7CEAJPwgABKB0oaTZQQBK+EEAOgK4/uZ5qh7o+h0TlbEZZgYBKDsFBAACULpQ0uwgACX8IAAQgNKFkmYHASjhBwGAAJQulDQ7CEAJPwhARwA4B6B0QGV2EIASQBAACEDpQkmzgwCU8IMAQABKF0qaHQSghB8EAAJQulDS7CAAJfwgAB0B4ByA0gGV2UEASgBBACAApQslzQ4CUMIPAgABKF0oaXYQgBJ+EAAIQOlCSbOPPAFoB7Br7+3/wc7GpLff9pBrEUPTnfrpG0HK8S1k3qVX+mbJKv3Jnxwd6TEw0o3/lSeCAHTjEQSgwy91bhCA8kYfaWbnHbvy0+vNT7EGTCoFYHPgWO211UvPbbiUfiOTKw5SOhAACEDrQ0PzgwCiwqsuHASgJADfHog1IGwzna+dodPHardkpyseUAChe7qw8rqKARAssQaCq8On6p5Y7QYB6HoUCqAjBdDVABh1IvBtPxSAjkCKz92VAgABVK4SGwcQgN+QhAKIrABi7//z7vYdAA8+vtXPY1hqfk8/PV657MbGclMRwP5D3220Z9S/KwACAAGAAFQIlJ0ZBKAkANs5gNj7/7kqALJr/8FXzD8fvntLzdTQSoArH2nG53hBAZRNYGrrtTEAEED9U128Q0AAaheNWgAUgFIBuPZO6BlPu/1F+WPFAGxEEBoPUgB85t+2/RFjyr13fXUkYwATm1aZbzdK308AAYAAXDmsMZ0UBAQBqGANlhkEYIFSuwRw7anQM16pCsAVr7bpiJA23lPN+IsWXtSoAGhp8sDG+3o5CV5z03Iz81P7oQAEjwIBxNkGlBRA24Htmg8EUCEFAnD0GBBAvwiAun3nrt01BbBs6epGj4ACcBwofU0GAgAB9NG3oQAcezU0Adi2BblZoW4E4uW6nggMvQtAa2tH+IMnO3R4nylz6vj7NQVA/18/sa5WZ98UAAX9pPa/9uLBWsyjlwEQH68CAYRVACAAH+8LnxYE4ImplgD4jE8n/7gZXe0CUL1aBXD42NFGJJcuXjIU4bb5PLtNTA4FUO37QwE4ehQIoFkBtB3IbfM5dpc1GQgABGB1knMTaAnAtbLSFIDULmmAU/rNd36hMSt/OzA0Hh+7/Dcb1/y0D85nRNoV6FsM4JNrrjUK4IOzcxrxQAyAuScIwC8GAAJwpfw06UAAnrjnRgAb7lw4tAU7njru1EJtDMCpksFgYDsK3NW9AKQALpx12pguzYD0/LZbN5h0fVMA1G+0DUjtfXXvkcaAP3YBMnsXAATgSj31dCCAOh4gAEc/iq0AbOcC9uz7Z0dLm5NJiiC1ApBmfmpFrBgAVwDS/388+boxBXcCqtyv/MwgAL8YAO9x3yvBQAB5jRksAQItAXzv/rv+5nmNnrBofhW9lf5MnajWuPzPj75zqvaTqwJI5Y6hFQC19+pVV5kmUQyAK4CpAz+sNRkKIJUHZFJvKAUAAvDrUBCAH16xUkMBKBUADXzXE4DzFl8wtC8XjM8d+vztk+8MfX7q2Ifm+agqAAJn0YpPmX+SAqA1PwcPCiAWtRRSrlYBgADadXQsBQAC8OsPKAClArDBTY4urfnphBqVQzOWVC6tbWccffrGG57+hWcO2ExL+jw2Abg2DgrAFameptMqABssIIBmhEAANs/p5nlnCkB6TZGaKd1ZJsFA5dFzftbbtbyuCMB17c8VAW8/nW23xQKkNW83bmWvBQRgx6iLFCCAjpYAIIC6O4MAuhje9jo6IwB6SYGi3Hyms91eSk3hSoKvhWlmlM4+2yGpp9AqhFs+v6KxStvan+8G8Bm/9FgACMDXE+OkBwFYcAUBxHE8EEAcXH1LjU4A0oxN0W6bIuBrfGnGl2ZE11iABFxbAqATaVQut8/Wfr4bILWPlIFUPtUvxQRC3wnIcYx9DwCvz/cbgfg2oC9leKYHAVSAgQAqHELP/CAAzwHJkkdTADTwpWg1P6tNdtkUgbQWlvbPqTytEnBVCNKan+enGIDtfW2pXuniByk94caVgFYB2NyPXhYiJdAVAdA3Am2XlPb1PgBbv9BzEIArUkI6vkQAAdSBAgEoHSxy9mgEQHbTxQS8HfwtLXrO18b0u22tyyU25eN3oIXGkxMAnSzjMzSv13XXw2avtMTieNKuCLeXKwC6VFOqV/rCDqWX8ktf57W1z/ZcmuElO7j9UAA2hJXPQQDNr/eCAJSONZ0dBKDDMboCkMzjVxbxtb8UBecn4Wjmjz3Tu8YA+NlyKRYSym6OI9kpxT4kBWCb+Xn7JSVgUwB8wNISYdv2R0wVvFzbc6lfoADciAEE4IaTmEpaAlAGEECFBC0BQABKhwucPRkBUDukGcy2rx1qBtXiSQRQ2ltlZDfFALQKwJZfIgCejysA23NfBUDp+/pdAF9/BgH4IsbSgwAqQEAASkdKlP3/AYjwaloOAtWeAAAAAElFTkSuQmCC", "relative_path": "../curseforge/minecraft/Instances/Testeos (2)/addonpacks/MOONS_OMNITRIX_ADDON_1.05/assets/m_ten/textures/models/ben/ov_piscciss_volan_water.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\5yl\\piscciss_volann_tail.png", "name": "piscciss_volann_tail.png", "folder": "", "namespace": "", "id": "10", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "b10cd114-00ff-f6e8-9537-3864915e12b6", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/5yl/piscciss_volann_tail.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\overlays\\glow\\recalibrated\\r_piscciss_volan_glow.png", "name": "r_piscciss_volan_glow.png", "folder": "", "namespace": "", "id": "11", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "aab93e08-9ad4-7fc6-9bf0-0edde70e9bf2", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAACGJJREFUeF7t2+GJHVQURtHYgyUE7MBibCakGYuxAyEl2IOiENCgYPZ7w8t37/J3zpucde7sjIF8985/BAhcK/DdtZtbnACBdwLgERC4WEAALj6+1QkIgDdA4GIBAbj4+FYnIADeAIGLBQTg4uNbnYAAeAMELhYQgIuPb3UCAuANELhYQAAuPr7VCQiAN0DgYgEBuPj4VicgAN4AgYsFBODi41udgAB4AwQuFhCAi49vdQIC4A0QuFhAAC4+vtUJCIA3QOBiAQG4+PhWJyAA3gCBiwUE4OLjW52AAHgDBC4WEICLj291AgLgDRC4WEAALj6+1QkIgDdA4GIBAbj4+FYnIADeAIGLBQTg4uNbnYAAeAMELhYQgIuPb3UCAuANELhYQAAuPr7VCQiAN0DgYgEBuPj4VicgAN4AgYsFBODi41udwDcTgA+/vP/9387x8cdPD/0e3+pzPR0CJwg89M31TIC3+kZ9q8995u4+i8CrBL6pAHz5p/2f37zP+AngLT73VQfzdQk8U0AAnqnpswiMCXxTAfB3AGOvx293XkAA5k9oAQJd4JsJQF/BJAECVUAAqpw5AgcICMABR7QCgSogAFXOHIEDBATggCNagUAVEIAqZ47AAQICcMARrUCgCghAlTNH4AABATjgiFYgUAUEoMqZI3CAgAAccEQrEKgCAlDlzBE4QEAADjiiFQhUAQGocuYIHCAgAAcc0QoEqoAAVDlzBA4QEIADjmgFAlVAAKqcOQIHCAjAAUe0AoEqIABVzhyBAwQE4IAjWoFAFRCAKmeOwAECAnDAEa1AoAoIQJUzR+AAAQE44IhWIFAFBKDKmSNwgIAAHHBEKxCoAgJQ5cwROEBAAA44ohUIVAEBqHLmCBwgIAAHHNEKBKqAAFQ5cwQOEBCAA45oBQJVQACqnDkCBwgIwAFHtAKBKiAAVc4cgQMEBOCAI1qBQBUQgCpnjsABAgJwwBGtQKAKCECVM0fgAAEBOOCIViBQBQSgypkjcICAABxwRCsQqAICUOXMEThAQAAOOKIVCFQBAahy5ggcICAABxzRCgSqgABUuQfnPvzy/ve/f8THHz+5xYOmxr9ewKP7erOHJ7785v/8gSLwMK0P+EoBAfhKsGf9cj8BPEvS5zwiIACP6D04+zkC/uR/ENJ4FhCATPf4oAA8bugTHhMQgMf88rT/Bch0Bp8oIABPxPy/H+UvAf+vlF/31gIC8NbC//H5fgJ4Ebwv+w8BAfAgCFwsIAAXH9/qBATAGyBwsYAAXHx8qxMQAG+AwMUCAnDx8a1OQAC8AQIXCwjAxce3+rbAr7/9/Nc/Kf/h+5/y93Ee3KbzuyewLyAA+ze0AYEs8DkAnz+g/CTgJ4DMb5DAawUE4LX+vjqBlwoIwEv5fXECrxUQgNf6++oE5gX8HcD8CS1AoAsIQLczSWBeQADmT2gBAl1AALqdSQLzAgIwf0ILEOgCAtDtTBKYFxCA+RNagEAXEIBuZ5LAvIAAzJ/QAgS6gAB0O5ME5gUEYP6EFiDQBQSg25kkMC8gAPMntACBLiAA3c4kgXkBAZg/oQUIdAEB6HYmCcwLCMD8CS1AoAsIQLczSWBeQADmT2gBAl1AALqdSQLzAgIwf0ILEOgCAtDtTBKYFxCA+RNagEAXEIBuZ5LAvIAAzJ/QAgS6gAB0O5ME5gUEYP6EFiDQBQSg25kkMC8gAPMntACBLiAA3c4kgXkBAZg/oQUIdAEB6HYmCcwLCMD8CS1AoAsIQLczSWBeQADmT2gBAl1AALqdSQLzAgIwf0ILEOgCAtDtTBKYFxCA+RNagEAXEIBuZ5LAvIAAzJ/QAgS6gAB0O5ME5gUEYP6EFiDQBQSg25kkMC8gAPMntACBLiAA3c4kgXkBAZg/oQUIdAEB6HYmCcwLCMD8CS1AoAsIQLczSWBeQADmT2gBAl1AALqdSQLzAgIwf0ILEOgCAtDtTBKYFxCA+RNagEAXEIBuZ5LAvIAAzJ/QAgS6gAB0O5ME5gUEYP6EFiDQBQSg25kkMC8gAPMntACBLiAA3c4kgXkBAZg/oQUIdAEB6HYmCcwLCMD8CS1AoAsIQLczSWBeQADmT2gBAl1AALqdSQLzAgIwf0ILEOgCAtDtTBKYFxCA+RNagEAXEIBuZ5LAvIAAzJ/QAgS6gAB0O5ME5gUEYP6EFiDQBQSg25kkMC8gAPMntACBLiAA3c4kgXkBAZg/oQUIdAEB6HYmCcwLCMD8CS1AoAsIQLczSWBeQADmT2gBAl1AALqdSQLzAgIwf0ILEOgCAtDtTBKYFxCA+RNagEAXEIBuZ5LAvIAAzJ/QAgS6gAB0O5ME5gUEYP6EFiDQBQSg25kkMC8gAPMntACBLiAA3c4kgXkBAZg/oQUIdAEB6HYmCcwLCMD8CS1AoAsIQLczSWBeQADmT2gBAl1AALqdSQLzAgIwf0ILEOgCAtDtTBKYFxCA+RNagEAXEIBuZ5LAvIAAzJ/QAgS6gAB0O5ME5gUEYP6EFiDQBQSg25kkMC8gAPMntACBLiAA3c4kgXkBAZg/oQUIdAEB6HYmCcwLCMD8CS1AoAsIQLczSWBeQADmT2gBAl1AALqdSQLzAgIwf0ILEOgCAtDtTBKYFxCA+RNagEAXEIBuZ5LAvIAAzJ/QAgS6gAB0O5ME5gUEYP6EFiDQBQSg25kkMC8gAPMntACBLiAA3c4kgXkBAZg/oQUIdAEB6HYmCcwLCMD8CS1AoAsIQLczSWBeQADmT2gBAl1AALqdSQLzAgIwf0ILEOgCAtDtTBKYFxCA+RNagEAXEIBuZ5LAvIAAzJ/QAgS6gAB0O5ME5gUEYP6EFiDQBQSg25kkMC8gAPMntACBLvAHigl5AXvtLXsAAAAASUVORK5CYII=", "relative_path": "../curseforge/minecraft/Instances/Testeos (2)/addonpacks/MOONS_OMNITRIX_ADDON_1.05/assets/m_ten/textures/overlays/glow/recalibrated/r_piscciss_volan_glow.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\overlays\\glow\\piscciss_volan_glow.png", "name": "piscciss_volan_glow.png", "folder": "", "namespace": "", "id": "12", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "14e4f5d7-44c1-1bcb-ab7d-cf147dd1965a", "source": "data:image/png;base64,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", "relative_path": "../curseforge/minecraft/Instances/Testeos (2)/addonpacks/MOONS_OMNITRIX_ADDON_1.05/assets/m_ten/textures/overlays/glow/piscciss_volan_glow.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\overlays\\glow\\ov_piscciss_volan_glow.png", "name": "ov_piscciss_volan_glow.png", "folder": "", "namespace": "", "id": "13", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "4dc7e82a-f1cf-0e98-120a-3056b93e7c8d", "source": "data:image/png;base64,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", "relative_path": "../curseforge/minecraft/Instances/Testeos (2)/addonpacks/MOONS_OMNITRIX_ADDON_1.05/assets/m_ten/textures/overlays/glow/ov_piscciss_volan_glow.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\overlays\\glow\\ov_piscciss_volan_polymorph_glow.png", "name": "ov_piscciss_volan_polymorph_glow.png", "folder": "", "namespace": "", "id": "14", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "03547fb6-be97-5b3a-d2e3-2747daa08ac6", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/overlays/glow/ov_piscciss_volan_polymorph_glow.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_piscciss_volan_polymorph.png", "name": "ov_piscciss_volan_polymorph.png", "folder": "", "namespace": "", "id": "18", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "cef67637-22c3-feb4-f8bc-d5dd8e15b1ea", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/bio/ov_piscciss_volan_polymorph.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_piscciss_volan_water_polymorph.png", "name": "ov_piscciss_volan_water_polymorph.png", "folder": "", "namespace": "", "id": "19", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "9eb0006e-c3fd-4f5b-4942-210fe7635d93", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/bio/ov_piscciss_volan_water_polymorph.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_methanosian_piscciss_volan.png", "name": "ov_methanosian_piscciss_volan.png", "folder": "", "namespace": "", "id": "20", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "d36d454b-28fe-fe99-f3ad-1b77608c8093", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAIABJREFUeF7tXX2QXlV5vxt2syGBQEISQnATYsiQEORL7FRpq6QoOFhthYy0VCzQGQt+o3aAVoXagqOSIgiMU8GKQwsTmKmtjig0MFq0UyB8KCRMiCG7GkJC+E7MZjek856zz2bPk3vu+b5f7+/9Z/d97/n8nef8nt957rnn9mRZlr3+9OK9nb87d41mWTap8282dYr8u+a0XeJv3mfJjyYraX+5fFh+HxkRf1/p7c0OGe2UmWUjk2Va+pzym009nf+3Pr5I1F3VZ80Z+v4Vtemeg7cql898bY5XF87c8luBAz5AoAoEhPGBANyhBwG4Y4Yc9UPASAD/886d2lYv+b6qANa9d7dI29cjndore/ZkhxxwgPJbWxTAv8yQymZ5/************************************************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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/bio/ov_methanosian_piscciss_volan.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_methanosian_piscciss_volan_water.png", "name": "ov_methanosian_piscciss_volan_water.png", "folder": "", "namespace": "", "id": "21", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "cbbc16b1-d953-1aa1-2a0a-31343d7e1622", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/bio/ov_methanosian_piscciss_volan_water.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_prypiatosian_a_piscciss_volan_water.png", "name": "ov_prypia<PERSON>ian_a_piscciss_volan_water.png", "folder": "", "namespace": "", "id": "23", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "1a6f4884-173a-7bfd-1885-8538b7e15e38", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/bio/ov_prypiatosian_a_piscciss_volan_water.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_prypiatosian_a_piscciss_volan.png", "name": "ov_prypia<PERSON>ian_a_piscciss_volan.png", "folder": "", "namespace": "", "id": "24", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "6b6d17e8-d113-2fb8-fd0f-6022c842bac8", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/bio/ov_prypiatosian_a_piscciss_volan.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_piscciss_volan_water_opticoid.png", "name": "ov_piscciss_volan_water_opticoid.png", "folder": "", "namespace": "", "id": "25", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "aa920516-e37c-ebb0-f5d7-de9e79c5f60a", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}], "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/bio/ov_piscciss_volan_water_opticoid.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_piscciss_volan_opticoid.png", "name": "ov_piscciss_volan_opticoid.png", "folder": "", "namespace": "", "id": "26", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "acb106d8-4634-5054-04a9-77b0ac99d90f", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAHnZJREFUeF7tnXuwHUWdx2+WGxIVgSs3PKRYYqwKr7x4LGRReZhUYE1SBFgruqupiigSILgpYZdAQEEguGCBwBIWwVgoSkqBUEkUUuGpssDyyEvQVJmFRRRC2AsKSshl2To9+WXP/DJ9+vHrnpme8z3/3HvO9PP76/70rx8zM6Sn4s/gSze+V2URXt38hlf2U85bkYu38qqpzuk8tmxJz8kLVg9xjogIUCCQApU3PgAAAAjUlpGMhwIAgKcHMO2CJUrukfv1q783zJnkLD88AGfJECGwAgAAABC4SSG5lBQAADwBULc1gPdeuVWtpQzZ6zQnm/rGS6mRo6x6BZwaSwwhsQYQZg3AtyP7xovRFpBm+QoAAJ4eQN3WAJ46/0LlARw2b5RqRSZPgDr+09dsVOEPv/LyyttC+c0fOVZu9FQ9AAAAnacJCgAAFh7AmnXP7WDr827LRk76XDUrG3n5Z/zYg7TtRLILQCM+T9zXA+DpwCNoQvc21wEAMACgqPO3ZAUAzI0LIeqvAADgCQBb04b2AHQj/xuDg6pIY7/41+rviAPP6mjbV3/9b2rNYN0t/63C79bbW1gleAK2lk4zHAAAAAAAafbdIKUGACoEQMuC+4yfb2UDGvl/9sjDOcMfffTHct8H/vxn9f2Ur01Qf/luAK3+33XJanW97/3vz8V/9NFf5r7/3THHqu/wBIL0t9olYtX4Ypa67rsAujUAW006TQEAAFsVES6WAgBAzT0A3ZyfewIHTTg010ZsPQCK9NzqZwpHft7w4AnE6orVpAsAAACq5QEA1XTAqnMFABIFAK36UwOiuTt5Asecc6C6xHcDaPX/ket+nev4fC0BuwJVd81y8gcAAADV0gCAcjpc3XIBABIDgG7uTy68rwdA8UxTAawB1K0Ly8oDAAAAqgUBALKOlGpsACAxANDcn+/X6zrwhXfPzrXNy09enPuui0dTAr4WAA8g1a5eXG4AAAAo3AUAAJrV0XW1AQBqCgC+/89X/cmgdPKPrwHQdToPQN/pBCB95/H4yUAKB0+gmUAAAACA3BoAANDMjg4PQKOA73sBQjUTuhdg41PXqrvzPviBoSppukuP50Mjvi5/vopvuwagS08HBLrr8E9vbVVRRx3+T5UPJqFs0k3pVG60ut8LELsxAACxFUb6nRQAACyeCBSzCf1l8H2FyZMnQBf7+4bnwvG5vK6MpjUA23ibB97OBaWRn8eHJxCztYRPGwAAAApbFQcHABC+89UhRQCgYgDongdAawK6RkIeAvcMpI2KOrpuhKf0MdJLla5HfAAAAMi1RACgHh2zrFIAADUFgKkB8F0DvlZg+14AnWuPEd5kgWZcBwAAgMLFPQCgGR3cVIshf1izUO0/h/y0nndv+kycPjMXZET/bh2jLFt8synJ3PXps0/vGJ7v/9uUuZUgL7dToWoY2PaZhDUsOooUQAEAYJuIAECA1oQkklMgGgBcR0qTB0DKmjwB08hP6bieACRAuNYrVou47qeP55I+51NHeWUFD8BLtsZEAgAsTQkAWAqFYEkpEAUAPgrYegA6T8B25Pf1AHzqFDMOPICY6nZP2gBAorYGABI1XM2KDQDUzCCm4py96H4VZPT+u+SCbnjhTfX9hjmTTEnkrmMNwEmuxgUGABIzKQCQmMFqXlwAoOYG4sWbct6KjiVeedVUpxrBA3CSq3GBAYDETAoAJGawmhc3WQBIdXU9ByDNTxqfOv7oUdlzAXRrABs2Zvft23oC8ACklkk7PgCQiP0AgEQMlVgxAYBEDDbtguz+ine25lf/efF3HprtBiy/In+vha6a8AASaQCRigkARBI2dLIAQGhFkV5LAQAgkXaAKUAihkqsmABAIgYDABIxVGLFBAASMVhdpwD0PAm6l6N33zM7PmRm7dofq+dPjBv36Vy4FcuuV7/vt//eyiL8Oo9H4QcGNmfhJ4zJxaPwL77wsvqdp6srh6k50GPsaRcp9TUUAMBk8ZpcBwDy4AAAwjTM2gCAquN6V6CrDKnt/1P96jYF4CP/qgcfy5nixM/dpkZ408jd19efi0cjuuvvlAjF06UzdfpcVS4OkM/NuiTnkfBy3/uDWbknZ00+fqLKMnVPAABwJUhF4QGAzNXXgQEA8GuYAICfbqXHoinAccdmZ/1Xrno4V4Ypk49V3x96OLtXIPY5gK/+64VqRPzmP+5bqMV9T7+rfucj8dPPPKd+/8jIEbkOTb+/PrxP/b772wPq72GHHlSYDmUaKn2dZ0DAOeGwnQrr+S+3v6R+/9Y/X175A3Z9GiUA4KNaBXEAgM4eAHVUX8AAAIZGvWZdRu7xYzMix/6EXguIPfePrc/V92Un/M49ITsJSEAYuV82h6bnAPBwJjv5rmKTB0Dp6zwBGiFpRKfwNNLr4lG4hbeuV//qPIMHnstW+XXp0+/zT8t2CfhHl76pfFQvSq/xHkDsBs4NAwDkFQEAiqcGAIAJ8Z2va6cAvMOXDQAqthQEsUd+KmeRPq3fYnlM/LZg27v/eHMI5QGYPAEaaXUjsWszDp2ebf585G+sBwAA2DaJLFzZegEA2dQgFFBsrd21AOg00tmK5xOOPID//OWjTtH/5mNHq/BleABFI31sj0m3BuAkUk9Pj68HQPnwtQBd/qa5vmu5Y4fXdXSeb6pzf6qH9RQAAChucrqODgDk9QIAYiPLL33jNmDZri2vxvO/+ZX6aeQBh6i/pjUBGvF5PD95zLGq0qfqKYBZGbcQ/GShW2z70Kmf3LOvqV1IAMBOJ20oAEAo4LboAEAYHV1TcQZAVVMBytfWA3AVwjV8Va4/lbMuawCuutmGDwUEjPidFQcAbFukJpzurcIDrw0KUy4n+heuWV/LI6wAQDn2Fxt/2LChubukXIv9/BOXGqP87Hu3a8NU3YCpoRorESnAyCMvjpSyXbJbtmwVtyG7nIpDfXfeGFH7m3XumbmEdbtGvcN2LSzAiAPPqrT+Eu1accWFLwMAnSop3caSCggANB8Aus7fajsAQAkeAACgVwAeQHwPAADo0APhASwUuaBSDwQAiA+Alo0wBdC0VAAAAJBCTBJfugaw4i/ZMwh9P3fetEo8jfbNO0Q8ceEBAAAgREP0TQMA8FUuiwcAyPTrwSJg2ouA8ACEHQAeADwAYRMSRYcHIJIPHoBMvh54ADgHIPaipW1QEl9ceHgA8AAkDVAaV+oB4CCQ0AIAAAAgbEKi6ACASD5MAWTyYQqAo8A4Ciw6CGNzL0CnToqjwLgXQAJx7AJI1Ovp6cEUAFMAYRMSRZdOAQAAkfwAAM4B4ByAsAtVGh27AEL5AYC0ATD9y3NUCzjj2ru9WkLyR4GlLvyDj91RKNzxEz/jJWhZkfb8cPaGo02/z954JP3cc+tnVRL0HgD+5CJ6e67pPQH0pCF6+yyVi+5Tp+snnfYjaZFF8Ucfe1Qu/oaHH/dKb9GZB3jFs43Ut0evbdAg4VwfBFP18yyGAAAAgE/LBwCKVes6ANBISnLQiKrzDLhsVXkKoT0Aqhf3BOh38gjIE9B1Ov7eeQpXl5F/2uzsLcSj98/eUUifDS9k7y5cvjj/1mITXGJ5AGWP/LyetiCQeAB8AdQnLbEHAADkTQ8AAACtFpEsAEz78vwBFDoAmMhf9fVYHsBbG7+tqsZHet3aANeBz/XpOnkGHxj1lUql464/L4zrWgA8APeHspq2Pl08gR08AABA1r8AALfFQACgJgAwdXzeLcgT4B4AXwuQdafwscvyWAgEVAO+C8B3CfjIz3cB6jLyjx41vOMawIaNb6vrtp5AUwFAdjdNBVxG67svm6BO3ZrSpPWPkxesNm7zb/cAAICwsAEA7DwBAMDeA4gCgFAHWf7rf0cX9qCqVvl13Zk8gCeWZvv2ZT1UUzc14OWsy1yfl2vM5OzdjO9sza/+83A7D812A9avyt7paPqUBYAZX5pbWJTNA5nH4vvp78s8Iv5Z+p3rcz/pRu3KPQAAoJybaQCA4i4GADTEAzhyRv5kmu15gLI9BD7Vobm47o0wppGBz+VNc3U+NeDpu8YPVW5TPQ+d/UMVZOVVU9Xf636ad/HP+VR2MpDeWvzM4n8wJamu33b1jVbhbAPx/X/dyE/pxfIAKH3yBBrvAQAAmcldOzAAUA8ADG75oy1jcuFML6ulwDrQJTsF0K1iEwhCnbH3skpBJBr5TQYLPaKagGCqXyePIXRZO5WF8qIRXheWPASTzqaOYdJFd93XAwgNgJnfuEsVcclFp6i/yxbfrP5yT8AFAFRn3WKgy+o/pWV8O7BOaAAgr4yuwccCQJmdv1VTAKC4J+jsDgAEusvOdyTg8Ww9AIoXuoO5gkA38ocul6u+1LB19wLQiGebbqs+nd7+bJuO7uy/7RqArwdA5eMgIJ3oXERrTYS8gHZPAB6ArYWF4QCA3YQKZtFjACBIwVgitlMQX6DyMtsAoN2Tovg+j7Sr5RSAj5xl7a/bNh4pAOhuPH4/v+533chg8gT4yK9roK7lse0QJj1pN0AXznb1X+ppmcppW99QAODlOXvR/eon8gBobYSH8wEApRHkbkDfcwA6gUlQACAzk3SbEAAwdfXi6wCAnW5DBl+6UZ0vtiWhq7D8hGBV+/625eay6e7O03kCdrLLQxU9WYi8glbq7dd9695eyqNOz1axR+7Xr/7q1gCef3Gzuv74zadbVdK23VklZhFIt3htEdU6SGunhO6ZIA+Avt8wZ1Iunco9AACgs11TAUB7528HQIjO30oPALDr/7RNCgBsfiMbMY7MH7XlTwqK7RG4zv11HgD/XdfhKFyojkfp8ZHSlH/ocvBVbV13oIZvuxsQywPg+lB5bZ/JyOtnikfhywQALQZS3jZ3//F6RZ8CAAB2I4cpFABgUih/HQCw06t0ANgVK1woqQdAJTE91Zc3OH4/v7RGuicMFaXbKkvo/Pm9ANMuWJJ5eNvWBGhuW/a9ALqTf+8ObimU3PRMRtuRnifOV/11UwBaE5h3yOsqCZ9zAJR3kh6AtCO4xu82AOgeK+6qGw8PAHRWEABg+lS9HRiq4/M5uGnurXsfgG8H1L0foD29otX/0GsQXAd+T4DrPQCUnvRuQNPZf50nwOvjax+KRx4RPS+BewD0nAS6fukx2XMTfObt8AAsrNUtAIi9+s+l1t0TAABkUyIAoCaeQGgAmDwBuk4jtmnNwIJhKojtm4Jsnzpsm6+u4/O5P4Wj/f/lV8xUP9l6IL4egOvZf50nIN2F2HvvPVV9j/hC9gQgAgAf8bvOA+AdpuyTgQCAb1cvjkcdBQDI6wMAWLYzakB0MrDu+/+6aulGDP7mH92IbDpYZPu0YFM5LM1iHcx0DwBPyPaeAF8PgPIzrQGYKhj6iUDjPr9YZanzABb8bb5EkjWAre+dqk7xLr/8typRn7SibQPqXEgAIDsgZTt31z0uHADIWhgAUDIAqGOb5ni6BkpTAToRGNoTiOX664BGv9ueA3D1ACh90zkAkz1MI6Hpet2mANwDMN33r6tfaA+A8uFrAmu/PztXBHpWoM+oTQmRB0Dfhw650/geAK6DswcAAGQKmE7m6Q7iAAD5JhhqCgAA9PR4ASD07cA60pZ1LqAqD8A0ApuAwU+guaZnCm8a4W2v120bkMo9+/x5tlXIhYvlAZgKs/jKa1QQyUnAIB4AAGAyVfF13qFNHRAAKNZR6gEAAP+va1d7ALrn/Zu6t+kJPbr4tu8Y/OjVBxQm8e+fvCX3+5cf+GJhuN+e+5vC33ft+1DP8Pfttf1a3Z7CbNKdruv0sY2ferhn59q/GCRGXYM9FdhUuNhTgFQAwDs+142DAAAwtay0rwMABvu5vrSUkjO55BRu38vCvBpsj4P3ztXktWdfzn1vXW/v/D/fnJ+zfqI/mxO2Pi0IFMVvT5BfD9UNeD1CpUvp8HK/sih7glBZn4OvH7M9q2fnri8r2075OK/chyx07T2ApgHgkxM+0XPJqiMKbfi1yU/2PLD6510FgNjA4UK3d/p2GITsVC5pdY0HQKK4TgWaAoCfnL28p9X56cMh0Or89GlB4O9vmLaDB9FED6BsALh0zjLCAgAGlZsKgFa1CQLtnb/1OwBQRterRx7JAoDko7k2jeymx4XTdVp99+3gJvOVvQZA5aER7fWHBtRP7+6ZPZnm8hO+1TPn0/mVfh0AFv34lp4L7/uqirfTpmHq7+7H9am/seb+XM/QI7Ou3Ou/conJlE7X+/uGZ7pveyKQ9G4/p8wLAlM73Kk3syM/dzDiwLPSXAMAAIqbhi0AOk0BAAD/bgcAuGnnvQjIs7EdcfkaQCwPgANKJ0uoXQCTB9DuBXRaBGzv/N3gAby04FK3FmsZmp5izIPz5/IXJUeP9yq6Zoqvi0vxeoftuj3Z1vsIJe8FsJSiYzAAINA2oC0AKNymvptyhtlz4Izt38n9BwD8m7gvADp1/lZppADgNUoeAGNOzPavX3ny6+qvTnh6TvxeR2Th3vyfbI5ctgfA54S+DyrhJwG3d+xtb0XeY1r2pBhaA6DrrbWATp/2zt8OgNeWb1LRTPn6d5lqYnL7Uwek9sLtZbpOnqgrAEwdn6ujA4HJAwAAuhwAOhDwjk/haBEQAMgUAQDCglo8BSAP4LiTxlmV7KF71qpw6+/1u3vLKhOHQLE8ACqCzhMwFZF3fArfLR4A1ZePtHyE5ddNHoBtuib7mMpl6zE0ZgoAAGQm5zflAACdu5JuCmDbUQEAE6o6X690D1JWdMSGAlBAqgAAIFUQ8aFAwgoAAAkbD0WHAlIFAACpgogPBRJWAABI2HgoOhSQKgAASBVEfCiQsAIAQMLGQ9GhgFQBAECqIOJDgYQVAAASNh6KDgWkCgAAUgURHwokrAAAkLDxUHQoIFUAAJAqiPhQIGEFAICEjYeiQwGpAgCAVEHEhwIJKwAAJGw8FB0KSBUAAKQKIj4USFgBACBh41VZ9LFTPv5ee/7rVv4CbalKg3jmDaN5Ctft0QCAZrQAAKAZdiytFqeeMVmN/KP33yWX54YX3lTf77xpFdpUadaQZwRjyTXsqhQAgGaZGwBolj2j14a7/jxDrAVEN0HQDACAoHI2PzEAoFk2BgCaZc9otaGOP3pU9vZd3RrAho1vq+vwBKKZImjCAEBQOZubGADQTNsCAM20a/BaHT51glr9f2drfvWfZ7Tz0Gw34KkVq9G2glshfIIwUnhNG5kiANBIs/YAAM20a/Ba0RSA5vbzF87InQRcOH+paks8XPCCIMGgCgAAQeVsbmIAQDNtCwA0067RaoVtQDtp6cBU3U9GAgB29kSobQoAAHZNAQCw0wmhElMAR4HtDEY61f1cBDwAO3si1DYFAAC7pgAA2OmEUIkpgCmAncEAADudECoxBQAAO4MBAHY6IVQiCtBBoJH79asS6+4FeP7Fzep6t54E5EemaQ2A7qGo264A1gAS6YBVFxMAsLMAAGCnE0IlpgB3aXXFr+tIV5bcAEBZSiOfUhUAAOzkBgDsdEKoxBTgR4H5lIDmtt1+L4AOAHW9SxJrAIl1xKqKCwDYKQ8A2OmEUIkrgMeCdzYgv326rk9IggeQeEesqvgAAABQVdtDvhUqwOf+VJRu3/+v0CSirOEBiOTrvsgAQLNsDgA0y57RamM6AswzruucN5pAiSYMACRquLKLDQCUrXg5+QEA5ejcmFwwBWiMKVVFAIBm2TN6bQCA6BKXmgEAUKrczckM24DNsCUA0Aw7ll4LAKB0yaNkCABEkRWJQoE0FAAA0rATSgkFoigAAESRFYlCgTQUAADSsBNKCQWiKAAARJEViUKBNBQAANKwE0oJBaIoAABEkRWJQoE0FAAA0rATSgkFoigAAESRFYlCgTQUAADSsBNKCQWiKAAARJEViUKBNBQAANKwE0oJBaIoAABEkRWJQoE0FAAA0rATSgkFoigAAAhlHTZs6HuSJJ5/4lJJ9J59xs+v1IZ3XzZBVP+J02c61X9E/2658L37nllp/Z0KX8PAEE9oFAAAABA2oUqjAwBC+QEAAEDYhCqNDgAI5QcAAABhE6o0OgAglB8AkAHgB7/rF1mA3kosSqSLIwMAQuMDAACAsAlVGh0AEMoPAAAAwiZUaXQAQCg/AAAACJtQpdEBAKH8AIAMADgHIGyAwugAgFBAAAAAEDahSqMDAEL5AQAAQNiEKo0OAAjlBwAAAGETqjQ6ACCUHwCQAQDnAIQNUBgdABAKCAAAAMImVGl0AEAoPwAAAAibUKXRAQCh/AAAACBsQpVG73oASDuwrfUefOyOwqAzT7nENomO4Tb9/jmvdO646BCveLaRBl4btA1aSri+PXpz+Zy8YHVX94GurnyrJQAAAEAp5KlpJgCA8Ik+upGd2/v4iZ9RP+354YOiNAV4AJ1l5SM/hYYHEKU5ppOo1AMAADrbui5TAACg2E7wAIQegCvqTB7AE0s/2zHJI2f8qPA6PAB4AK5tsRUeAAAAfNqNdRx4ANZSVRIQACgJANKRn7eOUJ5A03cBdK4/1gAyBQAAACDqyFO1BwAAdDYvABAZALpFwo/81YagHY88Ate1gNZ7CXqH7arKMrjlj85l4s/ppwSWLb45l1bZIOAdf/rs0wvr1u3vFQAAAAAAwBl7zYkAAAgBYNoG1O3/m1b7fZvYyCMvdooqfTORzgOgQry6+Q3172PLlqi/ZXkCs84900oHeABWMjU3UOxzAAAAAFDn3gMPQOgB2BqXdgFo5OcjJ42UtulROF06tp5AbA+g3RNoeQGxPQA+8s/8xl2qCEsuOqUr1wBOPWOyenej7v0JAAAA4MqcXHjTFAAAEMkrjgwAGCSUTgFsLQQPIJsKxP4QkA6d/UOV1ehRwws9APK4qn67ciw9xk75uBr5qf7wADRKAwCy15O7eACxGnt7ugBApgYAYNnaygIAzbV1HaYb1gAsTRIk2JTzVuQ8gBvmTCpMFx5AELnTTQQAaJYHQC0RAMAUwIpKoQHAzwXQib/QrrIpPfIoTLsBoXcBfD0ZK2NZBDp70f0q1IaNb+c8APq+8qqpuVSa5gHQop+u/utW/iK38I9dgMC7AABAOYt9OhYAANm2HwBgMVq0gkg9AN7h6eAPZW+a+/Ni2o6gsT2ANeuKnzE4fmznJxr5xrM0lzEYAAAAGBtJewAAoHgNwLcjd4pHx4FJf9cXg9oYFgAAAGzayfYwUgCYMkvVA9DVS9fBKfzk4yfuEHWn3mHbf1v6netz10OdDLzmV7sXzvlpH5y7xLQr0LQ1gMOnZo9pf2frLoV6YA2ANU8AwG0XwAUA7R2fZAcATEOG7DoA4KhfUwFAMph2A0LvArTLXwYA6L7/ix/JRrydh76p/upGQLq+/IqZKlzTPADSnw4CUX2fWlH8/gPsAgTeBeD8qWoKAAAUu8AAQL6FAgCRAODa8XmHNTkypl2AqgFQNPq3ysSnAFRO37UAkwfAPQL6/uR356qsh+x1Wlf3ga6ufKsBxJoCdBsAdB2egwwAMKG93OsAQCAPgM4DuJ784+YOdQ7A1gP43ZprRS2uvy+72872E3oRkD/777L/yEpCawDcA1j7/dm5osIDsLVcQ8OF8gAAALsGAgDY6VRWKHgAQg+AOj6dANStqpvm7LYjPzUMU3o6z4LfG1C2B0DlIhD4zv0pHfIAZnwpm9PTZ9znF6t/+Zyf6wIPoCzU1DQfqQcAALhNAQCAenUEeABCD4CbM/biH8/P1hPQnQeoygNYfOU1QXvC7PPneaUHD8BLtuZEknoAAICfBwAA1KMPwQPoMg+Amh2tBXz06gPq0RIrKsWzc9d3dR/o6sq32ly3eQAAQJ40AEBF5K1Ltn9Ys1DdPaX7mJ6ok9oUgJd3n/HzxaY4+Pox29N4du56cXolJ9DVg2BXV77V0LodAJMeuV3c39o7fTsMxAmXkAA8gBJErnMWoQDgu/pP2rieA6B4rrsA3BYhAFBn+5rKBgCYFGr4dQBA7gGk3EQAgJStF6DsJgCYsqA1grc2flsFpZHc9Px/uu478ofyAEz1k16netLNQpsHsqf1xvoU3Zvw8subtNk19XkAtvpiDcCwCGgSEgDorFBVAHh3cEsOyLpSAgCmFt7w66E8ANc1AOnIb+sB+ORDD9YsMr3uDTsUVhdX93Zem+Y18KcdN2r6PpiNXbr66crByw8A2FigwWEAgLxxO3X+VsgqANCp+QEAss6JKUCgKcAuH+pTlnjlya+rv/Reem4eGgn3OiILt/5evzPsOrPztQdbD8DU8Xl+OhDYeADtZaLykl48XdN1nQ7wAOzAAAAAAKqllAUADiRTBzddBwDsOrouFAAgBAAJO+bE7O62404aZ2WRh+5ZG8UDsMq8IJAUAKb45PlwAPB43AMwXXcFAIVv6nsBXO0PAAAAQTwAAMC169Uj/P8BeC0NWjGvTpkAAAAASUVORK5CYII=", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/bio/ov_piscciss_volan_opticoid.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Addon\\addonpacks\\MOONS_OMNITRIX_ADDON_1.06\\assets\\m_ten\\textures\\models\\ben\\thepy.png", "name": "thepy.png", "folder": "", "namespace": "", "id": "27", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "91b27633-b3bd-1d52-d34a-ec6f99c3ab36", "source": "data:image/png;base64,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", "relative_path": "../../aminb_ewzjrhk/curseforge/minecraft/Instances/Addon/addonpacks/MOONS_OMNITRIX_ADDON_1.06/assets/m_ten/textures/models/ben/thepy.png"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"726b4754-56d1-2c4c-f56b-0e36bc5cd405": {"name": "antenna2", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "b10ab2f6-ac02-3d10-bcc0-8cb4dbf9bc2c", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "fefa33f1-38eb-988c-7bb3-83bd099c45c1", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "57ded0e5-b4f7-23a3-a626-ee085092d039", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "d592b7de-be7d-d8fd-863f-ccac648bdc28", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}, {"uuid": "9ed62e08-6e80-aa81-17e3-3cc923092ac4", "name": "swim.test.new", "loop": "hold", "override": false, "length": 2.7916666666666665, "snapping": 24, "selected": false, "saved": true, "path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\animations\\piscciss_volan.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"024bf3ac-a147-9be0-d26c-743541c7c08c": {"name": "armorBody", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 2.3731109978443783, "y": 0.0369983423179292, "z": -0.7847681077519155}], "uuid": "748fb007-675b-b938-cfc7-29f93445018e", "time": 2.5416666666666665, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "22f6f580-3c6a-0681-2f30-5e94b8c855e9", "time": 0, "color": -1, "interpolation": "linear"}]}, "2f2d8f4b-3354-920a-7fb1-7cade178baa4": {"name": "armorRightLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 32.5, "y": "0", "z": "0"}], "uuid": "850ae87c-5ae4-dac4-724f-42eadacfdb96", "time": 2.25, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "55b778cc-a946-2098-d85a-7c2657bd39b2", "time": 0, "color": -1, "interpolation": "linear"}]}, "9114e3fe-4f43-960a-522b-49350c626e56": {"name": "armorLeftLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 32.5, "y": "0", "z": "0"}], "uuid": "2507ac0d-710e-d16f-2b09-f5f6b35c18a7", "time": 2.25, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "dac54719-3a0b-fb38-90cf-58357dc99e65", "time": 0, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "087ed087-7b62-08b2-c441-65f5b3cb0849", "name": "animation.model.new", "loop": "hold", "override": true, "length": 0, "snapping": 24, "selected": false, "saved": true, "path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\animations\\piscciss_volan.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}