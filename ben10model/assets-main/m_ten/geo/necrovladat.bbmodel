{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "necrovladat", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 17, -2], "to": [5, 25, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 28.5], "texture": 1}, "east": {"uv": [16, 20, 20, 28.75], "texture": 1}, "south": {"uv": [32, 20, 40, 28.75], "texture": 1}, "west": {"uv": [28, 20, 32, 28.75], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 26, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 1}, "east": {"uv": [16, 36, 20, 48], "texture": 1}, "south": {"uv": [32, 36, 40, 48], "texture": 1}, "west": {"uv": [28, 36, 32, 48], "texture": 1}, "up": {"uv": [28, 36, 20, 32], "texture": 1}, "down": {"uv": [36, 32, 28, 36], "texture": 1}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 12, -2], "to": [9, 25, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 1}, "east": {"uv": [40, 20, 44, 32], "texture": 1}, "south": {"uv": [52, 20, 56, 32], "texture": 1}, "west": {"uv": [48, 20, 52, 32], "texture": 1}, "up": {"uv": [48, 20, 44, 16], "texture": 1}, "down": {"uv": [52, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 12, -2], "to": [9, 25, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 1}, "east": {"uv": [40, 36, 44, 48], "texture": 1}, "south": {"uv": [52, 36, 56, 48], "texture": 1}, "west": {"uv": [48, 36, 52, 48], "texture": 1}, "up": {"uv": [48, 36, 44, 32], "texture": 1}, "down": {"uv": [52, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7.5, 12, -2], "to": [-5.5, 25, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 1}, "east": {"uv": [32, 52, 36, 64], "texture": 1}, "south": {"uv": [44, 52, 48, 64], "texture": 1}, "west": {"uv": [40, 52, 44, 64], "texture": 1}, "up": {"uv": [40, 52, 36, 48], "texture": 1}, "down": {"uv": [44, 48, 40, 52], "texture": 1}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.5, 12, -2], "to": [-5.5, 25, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-3, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 1}, "east": {"uv": [48, 52, 52, 64], "texture": 1}, "south": {"uv": [60, 52, 64, 64], "texture": 1}, "west": {"uv": [56, 52, 60, 64], "texture": 1}, "up": {"uv": [56, 52, 52, 48], "texture": 1}, "down": {"uv": [60, 48, 56, 52], "texture": 1}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 1}, "east": {"uv": [0, 20, 4, 32], "texture": 1}, "south": {"uv": [12, 20, 16, 32], "texture": 1}, "west": {"uv": [8, 20, 12, 32], "texture": 1}, "up": {"uv": [8, 20, 4, 16], "texture": 1}, "down": {"uv": [12, 16, 8, 20], "texture": 1}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, -1, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 1}, "east": {"uv": [0, 36, 4, 48], "texture": 1}, "south": {"uv": [12, 36, 16, 48], "texture": 1}, "west": {"uv": [8, 36, 12, 48], "texture": 1}, "up": {"uv": [8, 36, 4, 32], "texture": 1}, "down": {"uv": [12, 32, 8, 36], "texture": 1}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.9, 0, -2], "to": [0.10000000000000009, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.9000000953674316, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 1}, "east": {"uv": [16, 52, 20, 64], "texture": 1}, "south": {"uv": [28, 52, 32, 64], "texture": 1}, "west": {"uv": [24, 52, 28, 64], "texture": 1}, "up": {"uv": [24, 52, 20, 48], "texture": 1}, "down": {"uv": [28, 48, 24, 52], "texture": 1}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, -1, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 1}, "east": {"uv": [0, 52, 4, 64], "texture": 1}, "south": {"uv": [12, 52, 16, 64], "texture": 1}, "west": {"uv": [8, 52, 12, 64], "texture": 1}, "up": {"uv": [8, 52, 4, 48], "texture": 1}, "down": {"uv": [12, 48, 8, 52], "texture": 1}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 13, -2], "to": [3.5, 16, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 26], "faces": {"north": {"uv": [20.75, 28.75, 27.25, 31.75], "texture": 1}, "east": {"uv": [16, 28.75, 20, 32], "texture": 1}, "south": {"uv": [32, 28.75, 40, 32], "texture": 1}, "west": {"uv": [28, 28.75, 32, 32], "texture": 1}, "up": {"uv": [34.25, 32, 28, 28.75], "texture": 1}, "down": {"uv": [34.25, 28.75, 28.25, 32], "texture": 1}}, "type": "cube", "uuid": "10f829d2-4186-9798-8c58-a625734e5d98"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 26, -4], "to": [4, 35, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [1, 2, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 1}, "east": {"uv": [0, 8, 8, 16], "texture": 1}, "south": {"uv": [24, 8, 32, 16], "texture": 1}, "west": {"uv": [16, 8, 24, 16], "texture": 1}, "up": {"uv": [16, 8, 8, 0], "texture": 1}, "down": {"uv": [24, 0, 16, 8], "texture": 1}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 26, -4], "to": [4, 35, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [1, 2, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 1}, "east": {"uv": [32, 8, 40, 16], "texture": 1}, "south": {"uv": [56, 8, 64, 16], "texture": 1}, "west": {"uv": [48, 8, 56, 16], "texture": 1}, "up": {"uv": [48, 8, 40, 0], "texture": 1}, "down": {"uv": [56, 0, 48, 8], "texture": 1}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 26, -4], "to": [4, 35, 4], "autouv": 0, "color": 0, "inflate": 0.54, "origin": [1, 2, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 1}, "east": {"uv": [0, 0, 0, 0], "texture": null}, "south": {"uv": [0, 0, 0, 0], "texture": null}, "west": {"uv": [0, 0, 0, 0], "texture": null}, "up": {"uv": [0, 0, 0, 0], "texture": null}, "down": {"uv": [0, 0, 0, 0], "texture": null}}, "type": "cube", "uuid": "2c0ba297-8401-d8d7-78ba-80da1e5a133e"}, {"name": "<PERSON><PERSON><PERSON>", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-12.780000000000001, 37, -4], "to": [-4.780000000000001, 41, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [-1, 13, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [33.75, 0, 40, 4.75], "texture": 1}, "east": {"uv": [56, 8, 48, 16], "texture": null}, "south": {"uv": [64, 8, 56, 16], "texture": null}, "west": {"uv": [40, 8, 32, 16], "texture": null}, "up": {"uv": [40, 8, 48, 0], "texture": null}, "down": {"uv": [48, 0, 56, 8], "texture": null}}, "type": "cube", "uuid": "64a9215b-56e4-40d9-e7c9-610b2d5f3f74"}, {"name": "<PERSON><PERSON><PERSON>", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.780000000000001, 37, -4], "to": [12.780000000000001, 41, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [1, 13, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 0, 33.75, 4.75], "texture": 1}, "east": {"uv": [32, 8, 40, 16], "texture": null}, "south": {"uv": [56, 8, 64, 16], "texture": null}, "west": {"uv": [48, 8, 56, 16], "texture": null}, "up": {"uv": [48, 8, 40, 0], "texture": null}, "down": {"uv": [56, 0, 48, 8], "texture": null}}, "type": "cube", "uuid": "3d46f214-2cbf-788d-4184-ef2c2f2dd4f8"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10.4, 27, -4], "to": [-5.4, 35, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [-3, 3, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [30.75, 0, 24.25, 7.75], "texture": 1}, "east": {"uv": [56, 8, 48, 16], "texture": null}, "south": {"uv": [64, 8, 56, 16], "texture": null}, "west": {"uv": [40, 8, 32, 16], "texture": null}, "up": {"uv": [40, 8, 48, 0], "texture": null}, "down": {"uv": [48, 0, 56, 8], "texture": null}}, "type": "cube", "uuid": "6dda68de-471d-7a06-4556-bd4f7d162cab"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.4, 27, -4], "to": [10.4, 35, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [3, 3, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [24.25, 0, 30.75, 7.75], "texture": 1}, "east": {"uv": [32, 8, 40, 16], "texture": null}, "south": {"uv": [56, 8, 64, 16], "texture": null}, "west": {"uv": [48, 8, 56, 16], "texture": null}, "up": {"uv": [48, 8, 40, 0], "texture": null}, "down": {"uv": [56, 0, 48, 8], "texture": null}}, "type": "cube", "uuid": "491b5973-a998-8626-77c9-27bc20f920f9"}], "outliner": [{"name": "armorHead", "origin": [1, 26, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "2c0ba297-8401-d8d7-78ba-80da1e5a133e", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", {"name": "antenna", "origin": [1, 2, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "bio_necrovladat", "origin": [0, 0, 0], "color": 0, "uuid": "4a9e816c-0c87-b5c4-0341-5f4c20b8e317", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["64a9215b-56e4-40d9-e7c9-610b2d5f3f74", "3d46f214-2cbf-788d-4184-ef2c2f2dd4f8", "6dda68de-471d-7a06-4556-bd4f7d162cab", "491b5973-a998-8626-77c9-27bc20f920f9"]}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "10f829d2-4186-9798-8c58-a625734e5d98", "5a4ba021-cf61-d8a1-ec37-9958cffad565"]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", {"name": "wingRight", "origin": [6, 22, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "armNested", "origin": [-6, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_necrovladat_flying.png", "name": "ov_necrovladat_flying.png", "folder": "", "namespace": "", "id": "6", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "f1c1c375-3cb4-5cb3-5a76-9cd34d93e04a", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [97, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 63, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD8AAAAgCAYAAACl36CRAAAAAXNSR0IArs4c6QAAAPdJREFUaEPtlkEOgzAMBMOhj+JBfVUf1Ef1UNQDUmU1rHcTUBObK8Z4PWsnSxGf23p/M5++no+Fib8iVi4otPidjLcJU5FP8aWUJO/YTlPZ3kt870uKdzjkyhB41LGEa8UPST60ePZIm4r8P4uvudI7YnDmayTZcfAWxCy8kOKtaLWxMnlLCDlBLfCXE0KJP6ux3cijxdhCPsUfbEK1sTJ5RMPWqhb4yYP+peYeQjxz/DGxsng04z3JM4KY2BTPdAvFtt66UP6e75vJey876lLqKdbm6i7+zGLZ3MiFKZ7t6Kjx1glTk0f7KMWPamO27rT9V8dC234DjJ6R7YrgqlQAAAAASUVORK5CYII="}, {"name": "pasted", "offset": [80, 80], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 32, "height": 24, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAYCAYAAACbU/80AAAAAXNSR0IArs4c6QAAAFxJREFUSEvtlsEKACAIQ9v/f7ThrQwcQuJl3SLIx9ugYGa2BhcEIAMyIAMyMG4gPgPdQABwzrw2fiCAcQMdnYi5px34DZAN91lPCQXAvoespEx5vJ9GUI1EAFUDG7sgX+m+oJilAAAAAElFTkSuQmCC"}, {"name": "pasted", "offset": [80, 80], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 32, "height": 34, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAiCAYAAAA+stv/AAAAAXNSR0IArs4c6QAAAORJREFUWEftVjEKhDAQzDaBWAhW6j/8ghY+2TR2PibYGIhlDsvNcSPRgzRrt+wyGWbGZCnGGBX4rLWsO00TofllWRjeOI5oXJEQKK5ArmfneTJPj+Ngddu20PM0UyQEiivgvWf/7bZtzMNhGFjdNA2r00zs+876fd/je0AIFFcgNcg5xzJR1zX0XGsN74UUv+s69pZ8PSxCoLgCqWchBLgvGGNgRuZ5Vuu6/twh4HJxIb8lUFUVPEMI3CqQZuJugSGiLMys4YuMECiuAHzcHzSzM/DgDLyQ/BswF08UEAVEgeIKfADiq+eTi47lxgAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/bio/ov_necrovladat_flying.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_necrovladat.png", "name": "ov_necrovladat.png", "folder": "", "namespace": "", "id": "7", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "2c8db624-1384-52ed-8ac8-64635c1d763e", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [97, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 63, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD8AAAAgCAYAAACl36CRAAAAAXNSR0IArs4c6QAAAPdJREFUaEPtlkEOgzAMBMOhj+JBfVUf1Ef1UNQDUmU1rHcTUBObK8Z4PWsnSxGf23p/M5++no+Fib8iVi4otPidjLcJU5FP8aWUJO/YTlPZ3kt870uKdzjkyhB41LGEa8UPST60ePZIm4r8P4uvudI7YnDmayTZcfAWxCy8kOKtaLWxMnlLCDlBLfCXE0KJP6ux3cijxdhCPsUfbEK1sTJ5RMPWqhb4yYP+peYeQjxz/DGxsng04z3JM4KY2BTPdAvFtt66UP6e75vJey876lLqKdbm6i7+zGLZ3MiFKZ7t6Kjx1glTk0f7KMWPamO27rT9V8dC234DjJ6R7YrgqlQAAAAASUVORK5CYII="}], "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/bio/ov_necrovladat.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\necrovladat_transparent_ov.png", "name": "necrovladat_transparent_ov.png", "folder": "", "namespace": "", "id": "9", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "1cb22ee8-97a6-de68-d2ff-68172d87e395", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [97, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 63, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD8AAAAgCAYAAACl36CRAAAAAXNSR0IArs4c6QAAAPZJREFUaEPtlrEOgzAMRMNvdmTsB3Xs2N8s6oBUWQ3nuwTUxGbFGJ/f2clSxOe23t/Mp6/nY2Hir4iVCwotfifjbcJU5FN8KSXJO7bTVLb3Et/7kuIdDrkyBB51LOFa8UOSDy2ePdKmIv/P4muu9I4YnPkaSXYcvAUxCy+keCtabaxM3hJCTlAL/OWEUOLPamw38mgxtpBP8QebUG2sTB7RsLWqBX7yoH+puYcQzxx/TKwsHs14T/KMICY2xTPdQrGtty6Uv+f7ZvLey466lHqKtbm6iz+zWDY3cmGKZzs6arx1wtTk0T5K8aPamK07bf/VsdC23wDye5T/DJrMcQAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/bio/necrovladat_transparent_ov.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_necrofriggian_flying_fur_1.png", "name": "ov_necrofriggian_flying_fur_1.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "c8e8f93f-238c-ad0c-4476-9003532b46e3", "relative_path": "../textures/models/ben/ov_necrofriggian_flying_fur_1.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\extra_ov_necrofriggian_fur.png", "name": "extra_ov_necrofriggian_fur.png", "folder": "", "namespace": "", "id": "5", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "2560c625-867e-4992-20e0-c2fb05f05d81", "relative_path": "../textures/extra_ov_necrofriggian_fur.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}