{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "ultimate_vaxasaurian", "model_identifier": "", "visible_box": [4, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 27, -5], "to": [4, 36, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 3, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 3}, "east": {"uv": [0, 8, 8, 16], "texture": 3}, "south": {"uv": [24, 8, 32, 16], "texture": 3}, "west": {"uv": [16, 8, 24, 16], "texture": 3}, "up": {"uv": [16, 8, 8, 0], "texture": 3}, "down": {"uv": [24, 0, 16, 8], "texture": 3}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 27, -5], "to": [4, 36, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 3, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 3}, "east": {"uv": [32, 8, 40, 16], "texture": 3}, "south": {"uv": [56, 8, 64, 16], "texture": 3}, "west": {"uv": [48, 8, 56, 16], "texture": 3}, "up": {"uv": [48, 8, 40, 0], "texture": 3}, "down": {"uv": [56, 0, 48, 8], "texture": 3}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 26, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 32], "texture": 3}, "east": {"uv": [16, 20, 20, 32], "texture": 3}, "south": {"uv": [32, 20, 40, 32], "texture": 3}, "west": {"uv": [28, 20, 32, 32], "texture": 3}, "up": {"uv": [28, 20, 20, 16], "texture": 3}, "down": {"uv": [36, 16, 28, 20], "texture": 3}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 26, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 3}, "east": {"uv": [16, 36, 20, 48], "texture": 3}, "south": {"uv": [32, 36, 40, 48], "texture": 3}, "west": {"uv": [28, 36, 32, 48], "texture": 3}, "up": {"uv": [28, 36, 20, 32], "texture": 3}, "down": {"uv": [36, 32, 28, 36], "texture": 3}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 12, -2], "to": [10, 25, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 1, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 3}, "east": {"uv": [40, 20, 44, 32], "texture": 3}, "south": {"uv": [52, 20, 56, 32], "texture": 3}, "west": {"uv": [48, 20, 52, 32], "texture": 3}, "up": {"uv": [48, 20, 44, 16], "texture": 3}, "down": {"uv": [52, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 12, -2], "to": [10, 25, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 1, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 3}, "east": {"uv": [40, 36, 44, 48], "texture": 3}, "south": {"uv": [52, 36, 56, 48], "texture": 3}, "west": {"uv": [48, 36, 52, 48], "texture": 3}, "up": {"uv": [48, 36, 44, 32], "texture": 3}, "down": {"uv": [52, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10, 12, -2], "to": [-5, 25, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1, 1, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 3}, "east": {"uv": [32, 52, 36, 64], "texture": 3}, "south": {"uv": [44, 52, 48, 64], "texture": 3}, "west": {"uv": [40, 52, 44, 64], "texture": 3}, "up": {"uv": [40, 52, 36, 48], "texture": 3}, "down": {"uv": [44, 48, 40, 52], "texture": 3}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10, 12, -2], "to": [-5, 25, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1, 1, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 3}, "east": {"uv": [48, 52, 52, 64], "texture": 3}, "south": {"uv": [60, 52, 64, 64], "texture": 3}, "west": {"uv": [56, 52, 60, 64], "texture": 3}, "up": {"uv": [56, 52, 52, 48], "texture": 3}, "down": {"uv": [60, 48, 56, 52], "texture": 3}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 3}, "east": {"uv": [0, 20, 4, 32], "texture": 3}, "south": {"uv": [12, 20, 16, 32], "texture": 3}, "west": {"uv": [8, 20, 12, 32], "texture": 3}, "up": {"uv": [8, 20, 4, 16], "texture": 3}, "down": {"uv": [12, 16, 8, 20], "texture": 3}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 3}, "east": {"uv": [0, 36, 4, 48], "texture": 3}, "south": {"uv": [12, 36, 16, 48], "texture": 3}, "west": {"uv": [8, 36, 12, 48], "texture": 3}, "up": {"uv": [8, 36, 4, 32], "texture": 3}, "down": {"uv": [12, 32, 8, 36], "texture": 3}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, 0, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 3}, "east": {"uv": [16, 52, 20, 64], "texture": 3}, "south": {"uv": [28, 52, 32, 64], "texture": 3}, "west": {"uv": [24, 52, 28, 64], "texture": 3}, "up": {"uv": [24, 52, 20, 48], "texture": 3}, "down": {"uv": [28, 48, 24, 52], "texture": 3}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, 0, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 3}, "east": {"uv": [0, 52, 4, 64], "texture": 3}, "south": {"uv": [12, 52, 16, 64], "texture": 3}, "west": {"uv": [8, 52, 12, 64], "texture": 3}, "up": {"uv": [8, 52, 4, 48], "texture": 3}, "down": {"uv": [12, 48, 8, 52], "texture": 3}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "tail", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 10, -10], "to": [2, 13, 1], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [53.47830000000022, -0.05206252861114037, 0.6832148980363318], "origin": [0, -2, -1], "uv_offset": [16, 32], "faces": {"north": {"uv": [12, 36.25, 13.75, 38.5], "texture": 3}, "east": {"uv": [12.25, 36.25, 13.5, 38.5], "texture": 3}, "south": {"uv": [12.25, 36.25, 13.75, 38.5], "texture": 3}, "west": {"uv": [12, 36.25, 13.5, 38.5], "texture": 3}, "up": {"uv": [13.5, 38.5, 12, 36.25], "texture": 3}, "down": {"uv": [13.5, 36.25, 12, 38.5], "texture": 3}}, "type": "cube", "uuid": "e9922c7c-70eb-8b77-01ef-f55c86aa4c77"}, {"name": "tail", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 5.5, -3], "to": [1, 7.5, 1], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [53.47830000000022, -0.05209999999988213, 0.6831999999999425], "origin": [0, -8, 2], "uv_offset": [16, 32], "faces": {"north": {"uv": [12, 36.25, 13.75, 38.5], "texture": 3}, "east": {"uv": [12.25, 36.25, 14, 38.5], "texture": 3}, "south": {"uv": [12.25, 36.25, 14, 38.5], "texture": 3}, "west": {"uv": [12, 36.25, 14, 38.5], "texture": 3}, "up": {"uv": [14, 38.5, 12, 36.375], "texture": 3}, "down": {"uv": [14, 36.25, 12, 38.5], "texture": 3}}, "type": "cube", "uuid": "a5e5a983-53ac-b463-a626-7a092b2e73a9"}, {"name": "tail", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 10, -15.5], "to": [2, 13, -11.5], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [53.47830000000022, -0.05206252861114029, 0.6832148980363323], "origin": [0, -2, -1], "uv_offset": [16, 32], "faces": {"north": {"uv": [12, 36.25, 13.75, 38.5], "texture": 3}, "east": {"uv": [12.25, 36.25, 14, 38.5], "texture": 3}, "south": {"uv": [12.25, 36.25, 14, 38.5], "texture": 3}, "west": {"uv": [12, 36.25, 14, 38.5], "texture": 3}, "up": {"uv": [14, 38.5, 12, 36.25], "texture": 3}, "down": {"uv": [14, 36.25, 12, 38.5], "texture": 3}}, "type": "cube", "uuid": "27e028a5-71ba-f39d-37c4-2e1f3059d9e1"}, {"name": "ball", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2, 6.5, 1], "to": [2, 6.5, 1], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [53.47830000000022, -0.05209999999988213, 0.6831999999999425], "origin": [3, -8, 2], "uv_offset": [16, 32], "faces": {"north": {"uv": [15, 39.75, 15.5, 40.25], "texture": 3}, "east": {"uv": [15, 40.25, 15.5, 40.75], "texture": 3}, "south": {"uv": [15, 39.75, 15.5, 40.25], "texture": 3}, "west": {"uv": [15, 39.75, 15.5, 40.25], "texture": 3}, "up": {"uv": [15.5, 40.25, 15, 39.75], "texture": 3}, "down": {"uv": [15.5, 39.75, 15, 40.25], "texture": 3}}, "type": "cube", "uuid": "4c019a96-5157-6ee4-31f6-b408c69f1f3b"}, {"name": "ball", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2, 8.5, 0], "to": [2, 8.5, 0], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [53.47830000000022, -0.05209999999988213, 0.6831999999999425], "origin": [3, -6, 1], "uv_offset": [16, 32], "faces": {"north": {"uv": [15, 39.75, 15.25, 40.25], "texture": 3}, "east": {"uv": [15.25, 39.75, 15.5, 40], "texture": 3}, "south": {"uv": [15.25, 40, 15.5, 40.25], "texture": 3}, "west": {"uv": [15, 39.75, 15.5, 40.25], "texture": 3}, "up": {"uv": [15.5, 40.25, 15, 39.75], "texture": 3}, "down": {"uv": [15.5, 39.75, 15, 40.25], "texture": 3}}, "type": "cube", "uuid": "26fba775-3b9c-e250-66f1-8512d3a83b4e"}, {"name": "ball", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 8.5, 0], "to": [-2, 8.5, 0], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [53.47830000000022, 0.05209999999988213, -0.6831999999999425], "origin": [-3, -6, 1], "uv_offset": [16, 32], "faces": {"north": {"uv": [15.25, 39.75, 15, 40.25], "texture": 3}, "east": {"uv": [15.5, 39.5, 15, 40], "texture": 3}, "south": {"uv": [15.5, 40, 15.25, 40.25], "texture": 3}, "west": {"uv": [15.5, 40, 15.25, 40.25], "texture": 3}, "up": {"uv": [15, 40.25, 15.5, 39.75], "texture": 3}, "down": {"uv": [15, 39.75, 15.5, 40.25], "texture": 3}}, "type": "cube", "uuid": "29ca8947-e724-6485-5830-d305bd2c977d"}, {"name": "ball", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 6.5, 1], "to": [-2, 6.5, 1], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [53.47830000000022, 0.05209999999988213, -0.6831999999999425], "origin": [-3, -8, 2], "uv_offset": [16, 32], "faces": {"north": {"uv": [15.5, 39.75, 15, 40.25], "texture": 3}, "east": {"uv": [15.5, 39.5, 15, 40], "texture": 3}, "south": {"uv": [15.5, 39.75, 15, 40.25], "texture": 3}, "west": {"uv": [15.5, 39.75, 15, 40.25], "texture": 3}, "up": {"uv": [15, 40.25, 15.5, 39.75], "texture": 3}, "down": {"uv": [15, 39.75, 15.5, 40.25], "texture": 3}}, "type": "cube", "uuid": "c463bc6e-fc38-38e8-450a-6b9cb73c225b"}], "outliner": [{"name": "armorHead", "origin": [0, 27, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", {"name": "antenna", "origin": [0, 3, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "a5e5a983-53ac-b463-a626-7a092b2e73a9", "4c019a96-5157-6ee4-31f6-b408c69f1f3b", "26fba775-3b9c-e250-66f1-8512d3a83b4e", "c463bc6e-fc38-38e8-450a-6b9cb73c225b", "29ca8947-e724-6485-5830-d305bd2c977d", "e9922c7c-70eb-8b77-01ef-f55c86aa4c77", "27e028a5-71ba-f39d-37c4-2e1f3059d9e1"]}, {"name": "armorRightArm", "origin": [6, 23, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", {"name": "wingRight", "origin": [6, 23, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 23, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 23, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-6, 23, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_vaxasaurian_giant.png", "name": "ov_vaxasaurian_giant.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "39a588b1-b73c-7956-0928-6d5fdab4c32c", "relative_path": "../textures/models/ben/ov_vaxasaurian_giant.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_vaxasaurian.png", "name": "ov_vaxasaurian.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "005a8e06-37c3-4bb3-2684-c65c97d70592", "relative_path": "../textures/models/ben/ov_vaxasaurian.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\albedo\\ultimate_vaxasaurian_ua_al.png", "name": "ultimate_vaxasaurian_ua_al.png", "folder": "", "namespace": "", "id": "3", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "347a8f85-4da0-eb62-8891-1132976a0522", "relative_path": "../textures/models/albedo/ultimate_vaxasaurian_ua_al.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": true, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}