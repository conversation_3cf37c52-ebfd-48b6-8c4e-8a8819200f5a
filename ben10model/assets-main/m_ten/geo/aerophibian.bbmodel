{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "aerophibian", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.5, 24, -3], "to": [2.5, 29, 3], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [-0.5, -1, 1], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 1}, "east": {"uv": [0, 8, 8, 16], "texture": 1}, "south": {"uv": [24, 8, 32, 16], "texture": 1}, "west": {"uv": [16, 8, 24, 16], "texture": 1}, "up": {"uv": [16, 8, 8, 0], "texture": 1}, "down": {"uv": [24, 0, 16, 8], "texture": 1}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 21, -3], "to": [4.5, 31, 3], "autouv": 0, "color": 0, "inflate": 0.55, "origin": [-0.5, -2, 1], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 1}, "east": {"uv": [32, 8, 40, 16], "texture": 1}, "south": {"uv": [56, 8, 64, 16], "texture": 1}, "west": {"uv": [48, 8, 56, 16], "texture": 1}, "up": {"uv": [48, 8, 40, 0], "texture": 1}, "down": {"uv": [56, 0, 48, 8], "texture": 1}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 17, -1.5], "to": [4.5, 23, 1.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.5, -1, 0.5], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 25], "texture": 1}, "east": {"uv": [16, 20, 20, 25], "texture": 1}, "south": {"uv": [32, 20, 40, 25], "texture": 1}, "west": {"uv": [28, 20, 32, 25], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 17, -1.5], "to": [4.5, 23, 1.5], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.5, -1, 0.5], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 41], "texture": 1}, "east": {"uv": [16, 36, 20, 41], "texture": 1}, "south": {"uv": [32, 36, 40, 41], "texture": 1}, "west": {"uv": [28, 36, 32, 41], "texture": 1}, "up": {"uv": [28, 36, 20, 32], "texture": 1}, "down": {"uv": [36, 32, 28, 36], "texture": 1}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.5, 10, -1], "to": [7.5, 23, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, -1, 1], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 1}, "east": {"uv": [40, 20, 44, 32], "texture": 1}, "south": {"uv": [52, 20, 56, 32], "texture": 1}, "west": {"uv": [48, 20, 52, 32], "texture": 1}, "up": {"uv": [48, 20, 44, 16], "texture": 1}, "down": {"uv": [52, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.5, 10, -1], "to": [7.5, 23, 1], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, -1, 1], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 1}, "east": {"uv": [40, 36, 44, 48], "texture": 1}, "south": {"uv": [52, 36, 56, 48], "texture": 1}, "west": {"uv": [48, 36, 52, 48], "texture": 1}, "up": {"uv": [48, 36, 44, 32], "texture": 1}, "down": {"uv": [52, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7.5, 10, -1], "to": [-5.5, 23, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1, -1, 1], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 1}, "east": {"uv": [32, 52, 36, 64], "texture": 1}, "south": {"uv": [44, 52, 48, 64], "texture": 1}, "west": {"uv": [40, 52, 44, 64], "texture": 1}, "up": {"uv": [40, 52, 36, 48], "texture": 1}, "down": {"uv": [44, 48, 40, 52], "texture": 1}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7.5, 10, -1], "to": [-5.5, 23, 1], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1, -1, 1], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 1}, "east": {"uv": [48, 52, 52, 64], "texture": 1}, "south": {"uv": [60, 52, 64, 64], "texture": 1}, "west": {"uv": [56, 52, 60, 64], "texture": 1}, "up": {"uv": [56, 52, 52, 48], "texture": 1}, "down": {"uv": [60, 48, 56, 52], "texture": 1}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1, 6.69516749021717, -2.387958750013702], "to": [3, 14.69516749021717, -0.38795875001370206], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [22.5, 0, 0], "origin": [-0.8500000000000001, 9.19516749021717, -0.8879587500137025], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 7, 25], "texture": 1}, "east": {"uv": [0, 20, 4, 25], "texture": 1}, "south": {"uv": [12, 20, 15, 25], "texture": 1}, "west": {"uv": [8, 20, 12, 25], "texture": 1}, "up": {"uv": [7, 20, 4, 16], "texture": 1}, "down": {"uv": [11, 16, 8, 20], "texture": 1}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1, 6.69516749021717, -2.387958750013702], "to": [3, 14.69516749021717, -0.38795875001370206], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [22.5, 0, 0], "origin": [-0.8500000000000001, 9.19516749021717, -0.8879587500137025], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 7, 41], "texture": 1}, "east": {"uv": [0, 36, 4, 41], "texture": 1}, "south": {"uv": [12, 36, 15, 41], "texture": 1}, "west": {"uv": [8, 36, 12, 41], "texture": 1}, "up": {"uv": [7, 36, 4, 32], "texture": 1}, "down": {"uv": [11, 32, 8, 36], "texture": 1}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 11.283446448469398, 1.6149727570239998], "to": [1, 13.283446448469398, 7.************], "autouv": 0, "color": 0, "rotation": [-22.5, 0, 0], "origin": [0.5, 11.308241613337916, 3.077149446768347], "uv_offset": [20, 12], "faces": {"north": {"uv": [21.75, 10.25, 24, 12], "texture": 1}, "east": {"uv": [21.75, 10.25, 24, 12], "texture": 1}, "south": {"uv": [21.75, 10.25, 24, 12], "texture": 1}, "west": {"uv": [21.75, 10.25, 24, 12], "texture": 1}, "up": {"uv": [21.75, 10.25, 24, 12], "texture": 1}, "down": {"uv": [21.75, 10.25, 24, 12], "texture": 1}}, "type": "cube", "uuid": "da7518c9-4b37-cc53-8edc-7cecfeef4275"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 3.721660592003248, 5.26072624191184], "to": [1, 14.72166059200325, 7.26072624191184], "autouv": 0, "color": 0, "rotation": [-22.5, 0, 0], "origin": [3, 16.22166059200325, 5.76072624191184], "uv_offset": [20, 12], "faces": {"north": {"uv": [21.75, 10.25, 24, 12], "texture": 1}, "east": {"uv": [21.75, 10.25, 24, 12], "texture": 1}, "south": {"uv": [21.75, 10.25, 24, 12], "texture": 1}, "west": {"uv": [21.75, 10.25, 24, 12], "texture": 1}, "up": {"uv": [21.75, 10.25, 24, 12], "texture": 1}, "down": {"uv": [21.75, 10.25, 24, 12], "texture": 1}}, "type": "cube", "uuid": "1a4603f3-753c-98dc-4838-01577bcff664"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 12, -1.5], "to": [3, 16, 1.5], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1, -1, 0.5], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 41.75, 28, 47.75], "texture": 1}, "east": {"uv": [16, 41.75, 20, 47.75], "texture": 1}, "south": {"uv": [32, 41.75, 40, 47.75], "texture": 1}, "west": {"uv": [28, 41.75, 32, 47.75], "texture": 1}, "up": {"uv": [28, 41.75, 20, 37.75], "texture": 1}, "down": {"uv": [36, 37.75, 28, 41.75], "texture": 1}}, "type": "cube", "uuid": "b5a2ea4b-ceb0-5a5f-d0f7-6e5eec28af5f"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 12, -1.5], "to": [3, 16, 1.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1, -1, 0.5], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 25.75, 28, 31.75], "texture": 1}, "east": {"uv": [16, 25.75, 20, 31.75], "texture": 1}, "south": {"uv": [32, 25.75, 40, 31.75], "texture": 1}, "west": {"uv": [28, 25.75, 32, 31.75], "texture": 1}, "up": {"uv": [28, 25.75, 20, 21.75], "texture": 1}, "down": {"uv": [36, 21.75, 28, 25.75], "texture": 1}}, "type": "cube", "uuid": "002783e1-4a0e-3a89-8109-e782b270a126"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 12, -7], "to": [1.5, 15, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [27.5, 0, 0], "origin": [1, 12, 0], "uv_offset": [20, 12], "faces": {"north": {"uv": [22, 13.25, 24.25, 15], "texture": 1}, "east": {"uv": [22, 13.25, 24.25, 15], "texture": 1}, "south": {"uv": [22, 13.25, 24.25, 15], "texture": 1}, "west": {"uv": [22, 13.25, 24.25, 15], "texture": 1}, "up": {"uv": [22, 13.25, 24.25, 15], "texture": 1}, "down": {"uv": [22, 13.25, 24.25, 15], "texture": 1}}, "type": "cube", "uuid": "5ab201f9-4678-6f72-f5ea-cb45de97935b"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 12, 2], "to": [1, 14, 8], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [22.5, 0, 0], "origin": [1, 12, 0], "uv_offset": [20, 12], "faces": {"north": {"uv": [22, 13.25, 24.25, 15], "texture": 1}, "east": {"uv": [22, 13.25, 24.25, 15], "texture": 1}, "south": {"uv": [22, 13.25, 24.25, 15], "texture": 1}, "west": {"uv": [22, 13.25, 24.25, 15], "texture": 1}, "up": {"uv": [22, 13.25, 24.25, 15], "texture": 1}, "down": {"uv": [22, 13.25, 24.25, 15], "texture": 1}}, "type": "cube", "uuid": "123e73d6-0249-a8c1-ed88-4f47e7dd0b7d"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [0.3, 0, -2], "to": [3.5999999999999996, 10, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 7, 25], "texture": 1}, "east": {"uv": [0, 20, 4, 25], "texture": 1}, "south": {"uv": [12, 20, 15, 25], "texture": 1}, "west": {"uv": [8, 20, 12, 25], "texture": 1}, "up": {"uv": [7, 20, 4, 16], "texture": 1}, "down": {"uv": [11, 16, 8, 20], "texture": 1}}, "type": "cube", "uuid": "d477e96c-4080-a15e-4594-8d92d6a48f83"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [0.3, 0, -2], "to": [3.5999999999999996, 10, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 7, 41], "texture": 1}, "east": {"uv": [0, 36, 4, 41], "texture": 1}, "south": {"uv": [12, 36, 15, 41], "texture": 1}, "west": {"uv": [8, 36, 12, 41], "texture": 1}, "up": {"uv": [7, 36, 4, 32], "texture": 1}, "down": {"uv": [11, 32, 8, 36], "texture": 1}}, "type": "cube", "uuid": "ae38bbd2-8709-0dc8-7f3b-e369f0691827"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.0000000000000004, 0.11413406245546653, -4.5], "to": [3, 8.114134062455467, -2.5], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-22.5, 0, 0], "origin": [-6.35, 8.614134062455467, -1], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 27, 8, 32], "texture": 1}, "east": {"uv": [0, 27, 4, 32], "texture": 1}, "south": {"uv": [12, 27, 15, 32], "texture": 1}, "west": {"uv": [8, 27, 12, 32], "texture": 1}, "up": {"uv": [7, 20, 4, 16], "texture": 1}, "down": {"uv": [11, 16, 8, 20], "texture": 1}}, "type": "cube", "uuid": "13f2ba7c-7f5b-98c2-132a-1dcb7be061c8"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.0000000000000004, 1.1141340624554674, -4], "to": [3, 9.114134062455467, -2], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [-22.5, 0, 0], "origin": [-6.35, 8.614134062455467, -1], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 43, 8, 48], "texture": 1}, "east": {"uv": [0, 43, 4, 48], "texture": 1}, "south": {"uv": [12, 43, 15, 48], "texture": 1}, "west": {"uv": [8, 43, 12, 48], "texture": 1}, "up": {"uv": [7, 36, 4, 32], "texture": 1}, "down": {"uv": [11, 32, 8, 36], "texture": 1}}, "type": "cube", "uuid": "a0a9e532-4542-5b63-d45b-add9bbf5f3ba"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.7190235000000005, 0.25, -2.5341665733939016], "to": [-0.6755195, 0.050000000000000044, -2.7641665733939016], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, -45, 0], "origin": [2.060560499999999, 0.15000000000000002, -1.1707665733939014], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}}, "type": "cube", "uuid": "8d71bbb3-3e64-4c92-85ff-805396a549aa"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.1600035000000002, 0.25, -2.5341665733939016], "to": [-0.11649949999999976, 0.050000000000000044, -2.7641665733939016], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, -22.5, 0], "origin": [2.6195804999999996, 0.15000000000000002, -1.1707665733939014], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}}, "type": "cube", "uuid": "9501f0bf-a932-2d6b-2443-3227b9d3faa6"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.3307164999999994, 0.25, -4.024886573393901], "to": [1.3742204999999994, 0.050000000000000044, -0.5773665733939011], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, 22.5, 0], "origin": [4.110300499999999, 0.15000000000000002, -1.1707665733939014], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}}, "type": "cube", "uuid": "a3a238cf-48e4-063f-00c3-51210d9e0dec"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5122115108612324, 0.75, -2.7050841169945357], "to": [0.5557155108612326, 0.55, 0.742435883005464], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-45, 22.499999999999986, 0], "origin": [0.3835004999999996, 0.65, -2.5683165733939015], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}}, "type": "cube", "uuid": "cd746fdb-5c3d-5d57-016a-3b732ccb51a9"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.8343470493448706, 0.24999999999999145, -2.687353757104728], "to": [3.877851049344871, 0.049999999999991385, 0.760166242895272], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [1.6152119899410566e-14, 22.49999999999998, -179.9999999999999], "origin": [3.616499499999998, 0.15000000000000002, -2.3353915733939017], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}}, "type": "cube", "uuid": "ffa9beef-1d37-0a93-1fda-0ee58056e031"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.8343470493448706, 0.8499999999999912, -2.780523757104728], "to": [3.877851049344871, 0.6499999999999915, 0.6669962428952723], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [49.99999999999999, 22.499999999999986, -179.9999999999999], "origin": [3.616499499999998, 0.75, -2.4285615733939014], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}}, "type": "cube", "uuid": "e308d5e0-c434-ba5e-cbe4-77466222aaf7"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.3991645000000004, 0.25, -2.5341665733939016], "to": [-1.3556605000000008, 0.050000000000000044, -2.7641665733939016], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-1.6152119899410566e-14, -22.49999999999998, -179.9999999999999], "origin": [1.380419499999999, 0.15000000000000002, -1.1707665733939014], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}}, "type": "cube", "uuid": "1e7d7eaa-2210-a36d-e0f8-d53a1720591f"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.840144500000001, 0.25, -2.5341665733939016], "to": [-0.7966405000000005, 0.050000000000000044, -2.7641665733939016], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [4.7077028430832684e-14, -45.00000000000001, 179.9999999999999], "origin": [1.9394394999999989, 0.15000000000000002, -1.1707665733939014], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 1}}, "type": "cube", "uuid": "a5add2f8-d0b8-e581-c83e-15ca63649056"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.6755195, 0.25, -2.5341665733939016], "to": [0.7190235000000005, 0.050000000000000044, -2.7641665733939016], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, 45, 0], "origin": [-2.060560499999999, 0.15000000000000002, -1.1707665733939014], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}}, "type": "cube", "uuid": "ffec56f3-f198-599c-a6fc-a9e70808da09"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.11649949999999976, 0.25, -2.5341665733939016], "to": [0.1600035000000002, 0.050000000000000044, -2.7641665733939016], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, 22.5, 0], "origin": [-2.6195804999999996, 0.15000000000000002, -1.1707665733939014], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}}, "type": "cube", "uuid": "763a1499-af8e-7f3d-13a1-691ce4c263ee"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.3742204999999994, 0.25, -4.024886573393901], "to": [-1.3307164999999994, 0.050000000000000044, -0.5773665733939011], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, -22.5, 0], "origin": [-4.110300499999999, 0.15000000000000002, -1.1707665733939014], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}}, "type": "cube", "uuid": "5d11f5fb-96a3-baf4-0bed-7ba13186268b"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5557155108612326, 0.75, -2.7050841169945357], "to": [-0.5122115108612324, 0.55, 0.742435883005464], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-45, -22.499999999999986, 0], "origin": [-0.3835004999999996, 0.65, -2.5683165733939015], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}}, "type": "cube", "uuid": "12469ea7-d093-8f50-3f89-ddf4ad4c5f3d"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.877851049344871, 0.24999999999999145, -2.687353757104728], "to": [-3.8343470493448706, 0.049999999999991385, 0.760166242895272], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [1.6152119899410566e-14, -22.49999999999998, 179.9999999999999], "origin": [-3.616499499999998, 0.15000000000000002, -2.3353915733939017], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}}, "type": "cube", "uuid": "9a611d22-9bd4-70bc-cee8-533298da518f"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.877851049344871, 0.8499999999999912, -2.780523757104728], "to": [-3.8343470493448706, 0.6499999999999915, 0.6669962428952723], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [49.99999999999999, -22.499999999999986, 179.9999999999999], "origin": [-3.616499499999998, 0.75, -2.4285615733939014], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}}, "type": "cube", "uuid": "87494818-5e49-3bdc-7ee9-8e258c60f9b9"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.3556605000000008, 0.25, -2.5341665733939016], "to": [1.3991645000000004, 0.050000000000000044, -2.7641665733939016], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-1.6152119899410566e-14, 22.49999999999998, 179.9999999999999], "origin": [-1.380419499999999, 0.15000000000000002, -1.1707665733939014], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}}, "type": "cube", "uuid": "4ca24f11-1b48-343d-6478-7ac5c47e40f7"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.7966405000000005, 0.25, -2.5341665733939016], "to": [0.840144500000001, 0.050000000000000044, -2.7641665733939016], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [4.7077028430832684e-14, 45.00000000000001, -179.9999999999999], "origin": [-1.9394394999999989, 0.15000000000000002, -1.1707665733939014], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 1}}, "type": "cube", "uuid": "49b4da66-0c90-2728-ad27-17a8ca94c6cd"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 0.11413406245546653, -4.5], "to": [-1.0000000000000004, 8.114134062455467, -2.5], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-22.5, 0, 0], "origin": [6.35, 8.614134062455467, -1], "uv_offset": [0, 16], "faces": {"north": {"uv": [8, 27, 4, 32], "texture": 1}, "east": {"uv": [12, 27, 8, 32], "texture": 1}, "south": {"uv": [15, 27, 12, 32], "texture": 1}, "west": {"uv": [4, 27, 0, 32], "texture": 1}, "up": {"uv": [4, 20, 7, 16], "texture": 1}, "down": {"uv": [8, 16, 11, 20], "texture": 1}}, "type": "cube", "uuid": "f5d9bc9e-d6c7-175b-6d43-ac4489000ac6"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 1.1141340624554674, -4], "to": [-1.0000000000000004, 9.114134062455467, -2], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [-22.5, 0, 0], "origin": [6.35, 8.614134062455467, -1], "uv_offset": [0, 32], "faces": {"north": {"uv": [8, 43, 4, 48], "texture": 1}, "east": {"uv": [12, 43, 8, 48], "texture": 1}, "south": {"uv": [15, 43, 12, 48], "texture": 1}, "west": {"uv": [4, 43, 0, 48], "texture": 1}, "up": {"uv": [4, 36, 7, 32], "texture": 1}, "down": {"uv": [8, 32, 11, 36], "texture": 1}}, "type": "cube", "uuid": "35d3440b-1140-b743-75ed-57a7e41a7fc8"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 6.69516749021717, -2.387958750013702], "to": [-1, 14.69516749021717, -0.38795875001370206], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [22.5, 0, 0], "origin": [0.8500000000000001, 9.19516749021717, -0.8879587500137025], "uv_offset": [0, 32], "faces": {"north": {"uv": [7, 36, 4, 41], "texture": 1}, "east": {"uv": [12, 36, 8, 41], "texture": 1}, "south": {"uv": [15, 36, 12, 41], "texture": 1}, "west": {"uv": [4, 36, 0, 41], "texture": 1}, "up": {"uv": [4, 36, 7, 32], "texture": 1}, "down": {"uv": [8, 32, 11, 36], "texture": 1}}, "type": "cube", "uuid": "2070f83f-1ff7-6836-4c70-a2a7165e5f0c"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 6.69516749021717, -2.387958750013702], "to": [-1, 14.69516749021717, -0.38795875001370206], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [22.5, 0, 0], "origin": [0.8500000000000001, 9.19516749021717, -0.8879587500137025], "uv_offset": [0, 16], "faces": {"north": {"uv": [7, 20, 4, 25], "texture": 1}, "east": {"uv": [12, 20, 8, 25], "texture": 1}, "south": {"uv": [15, 20, 12, 25], "texture": 1}, "west": {"uv": [4, 20, 0, 25], "texture": 1}, "up": {"uv": [4, 20, 7, 16], "texture": 1}, "down": {"uv": [8, 16, 11, 20], "texture": 1}}, "type": "cube", "uuid": "bfd61ddd-1add-c260-bcb5-af0ae89e5e48"}, {"name": "Wing left", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9, 11.549527376085194, -0.30000000000000004], "to": [-3, 23.549527376085194, -0.050000000000000044], "autouv": 0, "color": 0, "rotation": [0, 0, -22.5], "origin": [-6, 17.549527376085194, -0.17500000000000004], "faces": {"north": {"uv": [50, 23.75, 48.75, 25.25], "texture": 1}, "east": {"uv": [50, 23.75, 48.75, 25.25], "texture": 1}, "south": {"uv": [50, 23.75, 48.75, 25.25], "texture": 1}, "west": {"uv": [50, 23.75, 48.75, 25.25], "texture": 1}, "up": {"uv": [50, 23.75, 48.75, 25.25], "texture": 1}, "down": {"uv": [50, 23.75, 48.75, 25.25], "texture": 1}}, "type": "cube", "uuid": "4139b4a7-e918-e8a0-bce0-b5b313f294c7"}, {"name": "Wing right", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3, 11.549527376085194, -0.30000000000000004], "to": [9, 23.549527376085194, -0.050000000000000044], "autouv": 0, "color": 0, "rotation": [0, 0, 22.5], "origin": [6, 17.549527376085194, -0.17500000000000004], "faces": {"north": {"uv": [48.75, 23.75, 50, 25.25], "texture": 1}, "east": {"uv": [48.75, 23.75, 50, 25.25], "texture": 1}, "south": {"uv": [48.75, 23.75, 50, 25.25], "texture": 1}, "west": {"uv": [48.75, 23.75, 50, 25.25], "texture": 1}, "up": {"uv": [48.75, 23.75, 50, 25.25], "texture": 1}, "down": {"uv": [48.75, 23.75, 50, 25.25], "texture": 1}}, "type": "cube", "uuid": "cb6e527b-eb1f-a9a1-d3e6-304285d9978a"}], "outliner": [{"name": "armorHead", "origin": [0, 25, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", {"name": "antenna", "origin": [0, 1, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "002783e1-4a0e-3a89-8109-e782b270a126", "b5a2ea4b-ceb0-5a5f-d0f7-6e5eec28af5f", {"name": "wings", "origin": [2.8398199999999996, 13.596657376085197, -0.050000000000000044], "color": 0, "uuid": "7be30190-3f39-3084-2e9d-62b22190d913", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["4139b4a7-e918-e8a0-bce0-b5b313f294c7", "cb6e527b-eb1f-a9a1-d3e6-304285d9978a"]}, {"name": "tail", "origin": [0, 1, -3], "color": 0, "uuid": "2181afcc-fb93-926e-4c26-f3ed2dcd37a4", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["da7518c9-4b37-cc53-8edc-7cecfeef4275", "1a4603f3-753c-98dc-4838-01577bcff664"]}, {"name": "tail2", "origin": [0, 1, -3], "rotation": [25, 0, 0], "color": 0, "uuid": "db9bc480-421d-32c8-8f5c-0141ffd61d32", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["5ab201f9-4678-6f72-f5ea-cb45de97935b", "123e73d6-0249-a8c1-ed88-4f47e7dd0b7d"]}]}, {"name": "armorRightArm", "origin": [6, 23, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", {"name": "wingRight", "origin": [6, 23, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 23, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 23, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-6, 23, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806", "a0a9e532-4542-5b63-d45b-add9bbf5f3ba", "13f2ba7c-7f5b-98c2-132a-1dcb7be061c8", "a5add2f8-d0b8-e581-c83e-15ca63649056", "1e7d7eaa-2210-a36d-e0f8-d53a1720591f", "e308d5e0-c434-ba5e-cbe4-77466222aaf7", "ffa9beef-1d37-0a93-1fda-0ee58056e031", "cd746fdb-5c3d-5d57-016a-3b732ccb51a9", "a3a238cf-48e4-063f-00c3-51210d9e0dec", "9501f0bf-a932-2d6b-2443-3227b9d3faa6", "8d71bbb3-3e64-4c92-85ff-805396a549aa", "ae38bbd2-8709-0dc8-7f3b-e369f0691827", "d477e96c-4080-a15e-4594-8d92d6a48f83"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["bfd61ddd-1add-c260-bcb5-af0ae89e5e48", "2070f83f-1ff7-6836-4c70-a2a7165e5f0c", "35d3440b-1140-b743-75ed-57a7e41a7fc8", "f5d9bc9e-d6c7-175b-6d43-ac4489000ac6", "49b4da66-0c90-2728-ad27-17a8ca94c6cd", "4ca24f11-1b48-343d-6478-7ac5c47e40f7", "87494818-5e49-3bdc-7ee9-8e258c60f9b9", "9a611d22-9bd4-70bc-cee8-533298da518f", "12469ea7-d093-8f50-3f89-ddf4ad4c5f3d", "5d11f5fb-96a3-baf4-0bed-7ba13186268b", "763a1499-af8e-7f3d-13a1-691ce4c263ee", "ffec56f3-f198-599c-a6fc-a9e70808da09"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_aerophibian.png", "name": "ov_aerophibian.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "a7b36a79-5229-5dd6-fa59-5e4d1c884d90", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAFkpJREFUeF7tnW/onlUZx38/nZvLkA2Hf4hY4kTRFzlqUpRE1gLf+cY3KeEy0FGBMXtl4ITWmxoJFdPIFqEh+MZ3RpYhKwpXzAhFcWEjwiGTjZXN1nTxPI9n7r52n+c6576u+zn3uX+f3xvd85y/33Odz/mec/95lpcK/z26/pbTJZvw/DtHSla/9Mjx/ctFG0DlK1qB4sEHAADAip6BhTsPAHAAhUOQ6ksqAAAAQMn4o+7CCgAAAFA4BKm+pAIAAACUjD/qLqwAAAAAhUOQ6ksqAAAAQMn4o+7CCgAABQCHTh8zDdHG5XVz83MfgEleMhsVAAAAwBhCZK9ZAQAAAGqOX9puVAAAAABjCJG9ZgUAAACoOX5pu1EBAAAAjCFE9poVAAAAoOb4pe1GBQAAADCGENlrVgAAAICa45e2GxUAAADAGEJkr1kBAAAAao5f2m5UAAAAAGMIkb1mBQAAAKg5fmm7UQEAAACMIUT2mhUAAACg5vil7UYFAAAAMIYQ2WtWAAAkvhBk79sHssZ524Wbk9LzPoAkmUjUkwIAAAD0FFoUW4MCAAAA1BCntLEnBQAAAOgptCi2BgUAAACoIU5pY08KAAAA0FNoUWwNCgAAAFBDnNLGnhQAAACgp9Ci2BoUWC7989zX3/tGQ6cXH7p0rm7PKxP2xvM3zM2fW18Ng2hp411Hny66CPzxwY+dnrT/Ew/8eaHtKFWvZaz6yAsAFOD0IfqQygQAiwXPkMZ+0pZkAGgrr+yYthKH9F/40X8aWf/56r9bHYGsf9uOdxvp9u4+r/HvUL9c8T909Qcb6X711Q+YHEfXfucGQl/6lwbAP37xmakDkOOuOYKwggcdc9OHOPjwF59bqPPIHfe+0wMAAFB0AgCAvqf4/PJVAOSuPLkronQAIb9cEcLnYaX/1sPNlfvb98ychHQGIZ9c+cPnMQfQd79jw2KtVwsn6cyG4gBku8P4x1b2AI6QL7aSB6cQG38cwPpbphasr4DUtgIAoKk8AJjpAQA0lPt8X9wBbH/rmdae/O1nn2p8Lh2BJLr2/VV3/r61nj0XbW393DoRNfDJSq315YZDaF9pByDbLVd26QiDI4g5AFb8vEgAAACg6BkAAMibsN6pBwsA2VHpCDQhYiu+zIcDKHsfgDaO4fuw4mt79tR0qfWOPR0AwAEMygHEJlzqxE5NN/aJndq/wQBg5zNbWtu8c+v+xueaE5Arv1YuDqAOB5Aa0KTLUwAA4ACqcAB5YU3qVAWKAyA0NFwN0FbskF46Aa+VP5RvPZXnKkBqCJKupAIAIKI+ACgZltS9KAUGAwDpBDQBNAcQyx/b88v0AEAbAb4fgwIAAAfAGcAYZnLHPgwGAM/976/TLjxx8nBrV7TTf5kpdh9AeF+/tkfHAXSMKLJVpQAAwAHgAKqasr6NHRwAdv34Kt8eitLCU4NDcQBWp2EVi18msipYd34AUNgBAIC6J1DtrR8MAORjwYeu3DXVdu3a1dP/vv767N2B69ata3weBuDEiZPT/z127Nj0v1dcMXu3YPh842v3N8bK+01AMhA0hxHSA4Dap1Dd7QcAOADOAOqew6bWRwHgvTLFVkS58v9l/TcbHQorfljZU3sby/fRo9+d6wQW1W8cQOpIkq5PBQCAeCcgAOgz3Ch7aAoMBgBhzx8E0vb+4Uwg7PVTzwJCunAmIM8CAMDQQpT29KkAAMABcAbQ5wwbeNmDAUApnXAA+wFAqeAbQL0AAAcAAAYwEUs14RwAeO+BU6+Px+rd+/YBkzbh3n+tHUPrt6nTGZm5EzBDrBEmBQDvDSoAGGF00yVVgeIASJ14uU4gtvLHnEBqO1RFIwli90H0Xa/WXhyAptC4vwcA7/2ceN8TEQCMeyLV2jvzAdBl2zfM/WkxTZhbH79SSzL3+9Ir2N0XbzH139T5AWQurf/q1atM+qc6xZjUpftvDQEAYFQQAJS9jAgAbAEMAGz6LQEAAGAMoaLZAYBRfgAAAIwhVDQ7ADDKDwDGBYDU9ziEsBnaryvnhjMAyFVMpAcAAMAYQkWzAwCj/AAAABhDqGh2AGCUHwAAAGMIFc0OAIzyA4BxASA3HLgPgBuBTDei5Abc0NKXngDcB2CLCByATT/uAziOAzCGUNHsAMAoP1sAAGAMoaLZAYBRfgAwLgBwH0DmhOBhIB4GygwZ1+TeZwAAIHN4AAAAyAwZ1+QAwCYnWwCbfhwCjuwQcMU5AOsKLufPJddd3vjozZcOG6fY/Oza+wTCgD556uC0oAN3HG0UePTR2W8Jev3tuWhroyj5ohHtzUby+XQZkNvfesarqdNyZHtdC08ozLs/sspNu69tbcXBHS9PP7e+D0AbT02CkydPmRdhrY553y8DAABgCSBrXgAwcgDIAPF2BFYHoOXXAjy2AoSVNfVtx9rbi2MTxXsF0xyMpof8vm8HE2tPbOWX6W+6f21ulxrpcQBCPrkFAABHWgNMBg4AMM3DczIDgDQ93bcAoVoNBCGd1RFoK7h2BqDlT5NxaSm2Eniv0KE91nJDOX21W+pmXSlTxyGkWzQAtPGI9X90ZwAAoBmqWmBogd33BO27fA00Wv+7fg8A0pTrzQGkVW9PFVvB5d4zdhXAywFogS5BcMcfftDo/GOf/HrS3tIKlJjiqVuSriMWK9/bGQR9nrr9tWlTY040OE/v8U/VJ/R7tA4gVQhrupoA8NMTLyw9+8K+uV2++Yablr689obethQAYKYAAJjpsLxmzQVVP85655obGjEdu5EjOIDfnvx7I73MbwXSPCfwmwO/O1P8viPfaFR104bvn/n35zZ/+pxm9LXyy4q8nYBWntUBaLoEJyD72ffKf+j07PLyxuV1c0Oq9OPUAEAApG8ATFb4B3/98dZqHvj8n6YOAQCkjwIASNeqLeXoARBb+YMYfTkA6QQmq/9k8oc/CYHJ5A9/Z0NAC3Db8J+bW1uxc+tLLS/3Flztp9xSV+DQn2073s3q2t7d5yWl19qBA0iSMZ5I2wIMFQCTHgUInD35J58DAD0oAICuUUqK5dTn2VMJrQ1MSqO6pNH2/tperEudkzyhXq3fW37+taXtt32lUU0MAHue/MnS/i/9cG6TUuvt2q+wclsdSG45WpxJncMKK/uZOt65K7+sJ9cJyPy//NerZZ8FAABdp8gsX+pElACYtwUAAPExAQC2eJW53R1AqEBbEX278f5EDOUG65+6EnRtj1yx5vU7QGDeIaA2+XPq69qnSb7UvXusjtT8sRU8te23rdrUmlSLP+vKHyp98aFLp/+r1RdzNqV/WQgApEZaJF3OhJwAIPy9sf7hRomXHr3nzL/n2f+c+ixdS53AAAAAdCKwJTjPzltq7x/aEKu/bUWY3Ag0WeHn/U3OCSY3Asm/nHo8tF0UAKxt7QrERTuAWLzgAIwRUBMAzu7q2W5g8nnqoZ+US7OeXeUFAGnKpW4BAECantmpYvf897337+IAzu5c7gRbtAMIbc09xbfmyw2A1ENYWW4pByDjBgeQO+IiPQBof9+AUdYz2QHAfCVzHQAA8IrM98oZCgCCFW871W5zIykOYF5ZXVe+XPkBAACYKqDdoLGovamsBwDgACYxkXsWwhZgNpPMlwHD9XZ5PTY2IN4rl3zjz6L2/gFEcpV+9r+z59DP/mu7my7FAbQ9KXfzmisbZffd35R2epxt5E7grqf/oa0AAADkuuHW9ACgKUsuMLouCADAJXzTHYA8vJDVx5xALJ3XyqU9beUjU7yU3IAPJS06X1cdctuZm74vAGhxsSgHEGtH6Hc1VwEAQPsUyg14ANDUEQA8XcfDQBoAwvfBCcRWHK+Vv+uK5p1PBvBYrwKc2TtfuHmuhLlA7AsAoZFyPHbed7FrCITLgLH4j8X76BwAAJidxgOAA40Jpj1ODABG5gDC6Oee6rpieYGFjf0QUG5ZtAmde99A3wCQoeC19w/lSgeQGvejdQAAYFyXAQHA/NUEAET0SSXhAhfrXqrK3fMO7RAwdQVOXdlT01kv43V9NqK0A5DtHs1VADm7AMB83iwaHLHWAADbupDrAEYPgJUy8WNXRVL73xUA1pVThntqeVp7te9jeuUuHNot6Zr+OICm4sm3AnsNoI23w8udOoFky1MnTNcJm6pUavu19mrfe8UPAEgd2bR0ACBNp2gqbQLt23WiNe/BHS83Po/9mKX8/Xqtvtzu5JYX2+N33ft7O4DUQ2jNCci3/cbSpz4OHAPX6M4ANAuWG6BDT69NIADQHEHrCq7lBwB5MwYHkKfXOaljAJATX67kmmWO5deAk9ud3PL6dgC57dfSxxak2Iquvedf5tMcgAYsHIA2ggP/HgDMBshrC+A93ABgvqJuDqBW65+7AsYO5UL/tZU/5NccQEinOYFc3bUVSSuvNgeQuiXwBo+mc6hvNA5ACxxvgb3KAwBNJbVxBABpkbdiAaB1XAuwNHn1VKkTO/b78XoNzRSXXHd5VpbUqwCxQt986XBWfbHE33liS+tX3uOkxYVLZ1oKKfV0amp/R+cAtI57B1YscABA2pQCAOvShMpMpc2D6rYAsQ6FCS2/l+RNfWdgps7nJNeElwCSP9DRtf5rrr6mNevdzzZ/ETgkSnUAj9zc/ktCr7z6StemNvItb3sCB+CiZLMQLQ4BwDv9vM1WEx4ANAMVAOAATqcAMGapYyu/fBNKeG6+bycAAFJG8/004SfJNIeXV+r7qbVy5fsUZHxoTlL7XrZbpvd+Q5UWf7I91ZwBAID5U6DWLQAA8HUAowOARvDaHYDXVYCAh9SrAalnABI7Xqf/odxbH2/+zkDq4WmqI9DKwwEM/JVgACA11GfpAMD8wzB5BgMACgMgL7xJjQIoMCYFir6TfExC0hcUqFEBAFDjqNFmFHBSAAA4CUkxKFCjAgCgxlGjzSjgpAAAcBKSYlCgRgUAQI2jRptRwEkBAOAkJMWgQI0KAIAaR402o4CTAgDASUiKQYEaFQAANY4abUYBJwUAgJOQFIMCNSoAAGocNdqMAk4KAAAnISkGBWpUAADUOGq0GQWcFAAATkJSDArUqAAAqHHUaDMKOCkAAJyEpBgUqFEBAFDjqNFmFHBSAAA4CUkxKFCjAgCgxlGjzSjgpAAAcBKSYlCgRgUAQI2jRptRwEkBAOAkJMWgQI0KAIAaR402o4CTAgDASUiKQYEaFQAANY4abUYBJwUAgJOQFIMCNSoAAGocNdqMAk4KAAAnISkGBWpUAADUOGq0GQWcFAAATkJSDArUqAAAqHHUaDMKOCkAAJyEpBgUqFEBAFDjqNFmFHBSAAA4CUkxKFCjAgCgxlGjzSjgpAAAcBKSYlCgRgUAQI2jRptRwEkBAOAkJMWgQI0KAIAaR402o4CTAgDASUiKQYEaFQAANY4abUYBJwUAgJOQFIMCNSoAAGocNdqMAk4KAAAnISkGBWpUAADUOGq0GQWcFAAATkJSDArUqAAAqHHUaDMKOCkAAJyEpBgUqFEBAFDjqNFmFHBSAAA4CUkxKFCjAgCgxlGjzSjgpAAAcBKSYlCgRgUAQI2jRptRwEkBAOAkJMWgQI0KAIAaR402o4CTAgDASUiKQYEaFQAANY4abUYBJwUAgJOQFIMCNSoAAGocNdqMAk4KAAAnISkGBWpUAADUOGq0GQWcFAAATkJSDArUqAAAMI7aZds3nLYUcevjV1qyLz1yfH/RMVy9epWp/9su3Fx1/02NH0DmosEzgP6bmwAAAIA5iAoWAACM4gMAAGAMoaLZAYBRfgAAAIwhVDQ7ADDKDwBsANhz0dasEXj+nSON9KXPQLIaP8DEAMA4KAAAABhDqGh2AGCUHwAAAGMIFc0OAIzyAwAAYAyhotkBgFF+AGADAPcBGAPQmB0AGAUEAADAGEJFswMAo/wAAAAYQ6hodgBglB8AAABjCBXNDgCM8gMAGwC4D8AYgMbsAMAoIAAAAMYQKpodABjlBwAAwBhCRbMDAKP8AAAAGEOoaPYVDwDrBNZG75LrLm8kefOlw1oW0/ebH1s/zX/bqk3T/8p7502Ft2Te+/aB6aebdl/bWvTBHS97V+la3smTp1b0HFjRnZ9EEgCwzScAYNOvdG4AYHyjjxxAueLL7xftAEL9fTmBfbtOJMXwUJ0ADiBp+MabyNsBAID2WAEAw5xDOACjA9AmfBj2vld+GV6LOgsYqgPQnjEIWxccwDDBtLBWWR0AABjmFgAApE0hHIDRAaTJvPhU0gH0dRbw1O2vTYuOgTA4n/D247DyeiuiTfhYfSv9jUIAAACY5iIAMMlXPPOKB8CaNReY3mtffASVBnx29UemKfq+LyCAQDZH+90DqyPQVv5Dp49Nm7RxeV2rUjiAoUdwz+0DAD4CAwAfHRddCg4ABzCNuW073s2Kvb27z0tKr63AoZAbz9+QVF7qWUZqvTiALNnHlxgHMBtTADC+2E7p0Yp3AHdfvGWhZwCpK53XnXthJYydAeROfBlUuU5A5pd7c00fqUvon1ZubDLgAFIwMeI0ACDP+gOAcU0GHMCCHIC2ssmwqsUBhHZrTiDW/ydPHTTNqOBscvUL7bnr6NMreg6s6M5PgmZRDgAAtB/yAQAT/8yZAUDlDiB22q3t/UPkWM8AUh1A7LTf6nQkWFPLwwHMRgQAAADzKjIpQNsCAAAXmd0LAQAjAUAsMrQ7ABftACQIUlfsWP/CSp5bDg4ABzBVoPYzgNhlsDBhAEA7OgAAABgVAHJPw8MEuP7eN1xsZeoWAAfgIrdbIWwBRrIFAABHsiYFDgAH4OoAtL2oPK0Ol79yJ24sylNP/WU7SjuA0J+ue3hrfu4DyOLm+BJ7nQEAgLSHg7yuBnS9/CfrBwDjm9NZPfICgFyBY43wXvllPantCPn6dgCx+xS8JnDMAWhPA7IFYAvgugVInXgAYBZ4ACBrneotMYeAzoeAqZflYiOauxfWzgS0yNl538Vakqzvd37veCN97E08fQMgNEKOhzxzYQuQNbzjS+y1BYgFnFQsdujX9TALAMwUiIETAMyfszgAZwcQkzv1YSAvB5CKaq87AUN9qfcDLMoBSB1kvTiA1EgZaTpvBwAA0q4GAIBhTCgcAA7ANRI1BxBzQrnOp2s5OIDmcAMAAAAAXBWoqzAAsCAAhLDQzgJyV0JruC36DMDaf+/8nAFYI6jy/Is6AwAAMwW8J7AMPw2gbAHYAjQUAADtLwWVe3nNKXTd+1sncGz9SQUBDqDyFdzafAAAAKwxVHN+zgA4A2jEr7aSx5xALJ9m+fuePDEnwLMAM+UBAAAAAH1TaMDlA4AFA0A7DNT2rgOOpUbTSq/8qWcL/DJQLRHVUzsXfQYAAHoaSKXYGFgBQJnxGEyt3gDQVr4QiF3vZEsVznqrbWo91qcfU+uxpgMA7QqyBXDeAgCAZqBpTz9aJ3ZqfgAAAFoV8HIAsXf+hUrlROjLCaQCKHXiaOlwAJpCw/4eB+DkAABAe6DjAADAoBWwOoDYxJdvwom9MizmBLpeDSjtALQJL38MVKa3fq8Fm9SVQ0BNsZF/DwBsA6y9cUeWbp3gWn6tNwCgqRBbAOMWoC8HEIYp1wngAOYjAAAAgIYCQ3UAAGCmQO4WAQegKdD8/v/cPD4AonRvoAAAAABJRU5ErkJggg==", "relative_path": "../textures/models/ben/ov_aerophibian.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\aerophibian.png", "name": "aerophibian.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "2ab41916-4c02-62ba-8463-2c2f17928b47", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/aerophibian.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\merlin.png", "name": "merlin.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "683f6eb6-**************-8caccfb74a9a", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAH+RJREFUeF7tnXuMXcV9x3/n7mKCAi15gGpKiPKw19gYIiEloa+klarmD+IgBb+weTgktKmEqgbV4N21vbZ3DUal/aOVqkJ42sZAaJIS1KQSrWjKI01EFJvgenHVKuUlFYcCBmzv7r2nnTl37p4ze+bOzJ0595x75nsJMb53Zs7Md36/z/xmzpw5EZX8uX33o3FeFca2XUVzzVkaoqHMz7unHuJ/3zK+jlqtJg1Fye/i+5vH1hBRrMwnX2v68HSpCty9bywqtQK4eNAKlG58KgDcMr6GWq0WDUfDuQC4eWw1xXHc+b0DgNHVFNP89yKz+B0ACNre0XhJgcoBYHz7RpprzlBEDWrFTeVIvnn0St4UAQjh4JtHV/MIQAYHTxxFtHvyQEYCRADwiZAVqBwAeAgfx53QXu6c+VDfLgJg5bDI4NaphwGAkC0ebc8oUDkAsLn/7NxM7gjeohbdNvVIew1gLTVbzQURAFsbaLbmOgCJKNtEeSqACAAeEbIClQPA6Nb1fPEvHcIzx2d+PNxYRJM79/H+umV8LcWt1oJFwC1bk8VBniEnkgAAQjZ3tF1WoHIAuGVsDbXi+cU/7vxEnZFfNIAtArLRvUEN/pXs2De3FwPZ71HEUiZNBQDgBFBgXoHqAWB8LR/BWQTAHLcZN4ndJ9zTDv1F1VWLgPOASNYSuN/HtCBSEOkwBYA7hKxApQHQaAzR5K79uf2jA4DIlEQUbFGxwaMA2whg+tCzTvYxcvFlXfNjH4CTvMjsqEAFATB//78bANL7ANjqfovd+hs6jaZ2ZoEh0rGpQCNqAACOBoPs9VKgegBIrQHkAYAt8rENQGwBUGz4YWsG7B82wu/Z/a1MD7HbinGc/MZ2DSICqJcBozVuClQOAFvYGkDcSjYARcT/u9Fo0O5dyRZgEfqz0ZzN7dmfDAQMCmzNQL7PLxYDAQA3Q0HueipQKQBs3baBZpozmS2+bGRnDr5nKhnZ5S3AcrekR3hxR0GkYQuLHBYUd+4q6BYBsQZQT8NHqxIFKgUA5rBsJBcP+IhOEnN8tqrP7giwSstpWNpOOop5yM+iB/bhUUK7XPYdB0p7qgAAwBVCVqByAOAO2763nwYAux0oPsz55R1+AgDpdPIegHRHi0gBAAjZ/NH2SgBgbHwdzcVN7tTin166hkcAqVE/DxKiXACgF4WRp24KVAIAYmGPLeLJz/8XJjjfKRzTdRt2dr0E1gAK6wEUXAEFKgGAW8ZW8806uY/wFigSixgAgAIFRtGVV6ASABgbW0NzXR4BLlLFazfsQARQpMAou9IKVAIAo2OrOQD6HQGwngEAKm2fqFzBClQCACwCmE09AVhwmzPFAwD9VBvXqpoClQDAKJ8CLDz/rx9iAQD9UBnXqKoClQAAIoCqmgfqVXcFKgGALWOrqYk1gLrbGtpXQQUqAYAqTwGK7jOcB1C0wii/mwIAgOY2YNHmAwAUrTDKrzwAqjwFKNp8AICiFUb5AECOAuIAEd1OwKLNBwAoWmGUDwDkKMCeGmTzn00bJ0u1EACgVPmDv3iwawAcABHRpg0AQPBeELAAlQBAGc8CsCkAO0zsWgAgYPNH0ysBgNGxtfzgL/kgkCK7h79wJI4xBShSZJRdeQUi1eu5TWv+F7f9MR1/903T5Jl07L0dc1GLWsnJXX39JIeLsdOHiJrsEJGI/Y+9aShhovid/Tf7Xvw9yTWfInlpSfYk4r42xPFim0evLHUQOPzoUi7p8itf7Gs9yrquY3d5z24NgL+84yZ678TxxAXiFs3MnqS55pz3ivWrwOTNQ/NunT5FKP29qI/4PS8P+46dPyheYOqrDeykZIamZjzHT0Jm7z9g709MXn+YnH/IX3/G3qTUavI6sJOR2PfDw+xdCQ8qqwIA9Bc8vmzCVznWAJic/FoHAL4qMcjliENG03HB7bsf9dqkzaPsPYjzkYg45DQNgPQFGQjSgOoGpLIBcHx6MafvS8+fldHMd0QgRnxxkY+sTAaxs0Ze62vk4dUwPBSmBcDte26kEyffaY/4Mc01Z6hVRszuobFFF8GPMOcnFycRhXgpaa8RAXtTcjLCJ+83tP2I9yUMDQ3zrFO7FkYCAAAA0NW0duzYRKdmTtjannX6S04/g85pnLYg3+HZE/Tq3Gzn+1+/5EN05jnvU5b/6qE36J1jJyluJc361B/uUab92Z23JC8Q9fgRB5OyP/l0IbJbI0ifUnzP/vHMOxJcqslgdM3acRpZuSJTTFUAILdNRASukYAY+cWIL18HEcDuRzMewBy+2Z7Tc2NuNbkRFv1hAPjWr31iwWX+5PWX6PvvvdX5/oo/v4yWfv48ZXUe2/Jv9B8/fI3mTiXHiF++90hu2pef+ns6eOcWvo5R5Cdqv7moRWzjUYNunUzecKT6TB8+QnsP7OK6s09yuzJ5BbrrpzE0xKOSXTv2dYoCABABZLx727arkwWmPn9UAGDVuOnYS/TTU+/RK3OztPqvf4uW/f75ytp9+0+foel/eoW+8M0XuraAA+CuLfwdg0V+REQgogHVdGD6hSNszsCrcv/+7d6rJCISHgms30ZLly/jMCgbAHJDxZqA/L1pRIAR3850ouFouPjh3aBO3QDAsn/j2Ev0+LtvGQPgout20+JP/wENLcqfLvQLALqm572f4IEHJwqMuiK6dsMEjSwf4VUDAHQ9VO/fAYCCIwCd+SwEQEz3PTjB1ya6vdhEV26339kxaFUFgKreIjLQzdlN07noV6e8AEBVAJCaAty3fxu3MQBg3tVMHds0XZ2c2KUtAEDVABDHdN+B7fy2X2EAuGo7jay4sJJTABdjRl57BSoHgOT9AAv3ZtR+DaAdAdy7f2tn9C8CAJ0t0FGDpiYPVG4NwN6EkcNFgcoBQNUYnwBgC4DsU8Q+ANvOkNcAxB0AtkU57xXotuXnpecQiCJ+S7Jqi4A+2ocyzBWoDABYlT88NEzPnL8st/a+AMBX/785RnFFnl9QAaCzkci8L41TAgDGUtU+YaUAcEbUoIMXLC8MAGLkP3jXKMXtjTZl93AHAC8c4bt92S3Aoj9iGnDr1MOIAIoWu+LlVwoAQqszGw366UeyIBARwJf2fJY++bnFdPqZp9HQafM75E4dn6XmXIv+YftP6OiTr5G8D4A5/6F7tlFr9lSlu8Tnzj9VQ/lZCEQ0TEM0E88G/TBMpY2hD5UrDADCqho5C3psZ/H8IzMLW8kWAQ9fkN2zLgCw5HfPow9ecBZd8uWP0blLfpWiRnKlg9/5L3r96Ft09MlX6Y1fHKeLr7+tsxGoM/J3Cf1ZKem6pnc/LzwLQN8zcnkiB3sNetk7r0QNhqgBAOi7stYpFgCAOQE33nazmbEORQ1iT/yzfe3az//vNR+iiBaxMuIW3bxzb26W8fHkSbduzvC+qEGHLlhOb7eaNPbLV+gf33u7U9aq2z5DSz5/HjWGEwB8f+I5mn7iZZo9meyhZw8BnXPRb9DrP3+Gnr93OzVnTiqrzkqYmsru0U8/lCMyPvjwlNEOPdaqXTvn99vLFx4fW89PQCr7ww4/QQRQdi+Ue/3otGg486iPmJOKauU5gk2VxY6zvDxbx5kjJLBhIyP7DA3Np2Qgef78FbT1zVfoxyffpZfmZjo/fvQz59LZ572fBJP++7lj9ObL71BrLinnoq/OPwX4wj3zc/5mwofMaL9r8sCC6hXZbnaxbaztMXvYpzwQMPDNxnOYAtgYdM3SdgDARgM2cpmMhDYadANAupyxsfX8+p9akTy7XtTn+cP8qX1+D7zbp2gApAG7d/9EKSAAAIqyssEpV3sgSL8c4dKP/1FGtaNvJ8d1n356ckbAL4/9L//zzLPOzHwvMp06lTzB+M7x5PCSD334A/xP8f2SXxnnfz/2RovOen9EP3/lbysDAFaRffsnFsQCvaw9yI0Sw3vepiL22yksAg6OtxZQ08oA4OtfvjHTvINvJAdtnnEGW00geu21/+F/nn322ZnvRaYTJ5LpwZtvJgeULl58Lv9TfH/JB1dnyv+bv/urSgGgW2V6jRCYg1+jefchXkxSgFcNUJFKALiO/LIGqqmA7Pj/8p93ZbIKhxeObaqtKt/nPv61riDoV7tFJUyvxyIE2/UCtoayYUP3fQUAgKlF1TMdACBFAqYOaWoOujUQ39czrZdIBwDYKlav9JUBgAj5hby60F9MCUSobzoVEOnElECeCvh2SACgXg5Tt9YAAIgAcBuwbl5t0Z7KAMCizl6TIgIYAwC8WtRgFQYAIAIAAAbLZ73WdgEAfM+BTe8GqK679xG3E3KvXrMjVzB5bl61dnvt5S6FYRGwX0pX8zoAQLtfAIBqGihqVawCpQPA1PFsIwHVyK+KSEzr0Wt3qO4GFH1dXX0RAegUqvfvAED7fPyiHREAqLcjDWrrnBeArt84Vd7jbERU9ghWdvvLNryy9b9derWdTo/xiXWZJKaRoqrcstuva6/udwBAp5DmdwCg3NuIAICbAQMAbvoRAAAAOJpQqdkBAEf5AQAAwNGESs0OADjKDwDUCwC6Zzdkcxn09yoAAACAkwJlL4L5XgMAACzNoewRsGwDLLv9lt3lPXnZ+gMAbl2KCMBNPywC7sMUwNGESs0OADjKjwigXgCwNYeyIyDb+srpAQBHBQEAAMDRhErNDgA4yg8ABAyAOKa79487+5CjCTpld6582Q5QdghWdvudet9D5rL1970IqJWEvcCGve6Ov98uprsf3OrsQ9prFpjAufJlO0DZBlh2+wu0DaOiy9bfNwBwG9Co2+cTle0AZRtg2e237C7vycvWHwBw61JEAG764TYgbgM6+5CjCTpld6582SNg2SNQ2e136n0PmcvWHxGAWydGOgO2nRMVfbCGbXNF/R976D6edeTiyzJF2LZvPjNbBCIa37E+U97khP414zZtkOsnP88uX8+m7JDSynYpTphyPQ9ApaHWrtpvw948ttp5EHbpRwCgfSJQTyLGMQDQk3D9z1Q5ADAJ4pgAgIJtQRcBuF5edVahGJldIyJR/+27NlKzObeguq4jmFz/ukQwrrq72oWcPzciAAB8y7ywvLoAQA79RUsBgHwbAgDMfCv4KYCZTPpUqkjAt4P6cnxRTlH1lhXTRUp6hbunqJrD69ozcuFSngRTAJ1Sjr8XHQEU7UhFO2jR5ev08bWIOYgAiOIW/dn4WiwCOvp4bnZ5zqW6C1DEtVmZsmPJkcDiS8+mJ7737c7lV5z3e5mq6PL7rnfR11OVr10t1zR00Bxfbk7Zt1FrOwWoIgAeeHjCyG+vWTuhBYhRQRaJag0AsX/fQo9+JQUAPCutGlH6HQHkhb5xnER7o09/NLfVu3/zF/z7KJp/1YLrGoKtvL5BYFqebSRgOvJPH3qWSyDv/7DVxTa96XUBAFtlNekBADdBTR3W9Cqm5VUPAAzCvU/PAQBTC/GcruzQX9WcF179Z3ruX3/Y+VmOAsTozxJc+tu/Q/KagGeZlMWZOqxpfUzL8w0AUwcU7bjpivtNm8TT3fHda43S6+qBCMBIRvNEgwKA9FQg7fwAgFlf66YAOseTrwIA9Bhay9l0HWPWvfapygr9xXV17ZYjAFcAmF7XXskkh6+98rbl6CIBWWfh6HI7Tef8to4vX8c2EpDzP3Xo8d7nGb12biqf9i6ASKvrGJFO5wge6pxbxKABwHUKAAAkZgAAuHmUdwCUBYKyQn/5ut0AKG4D6u4CsNuAqo/N9VxMw3TurrqGaX6VA5vWfdW663KT6gYi15FfGQkobjmqBqiy3ywEAJhamiKdjUMCAAtFrB0A0k1MwQAAcHQ00xFRpOv3fX9VB6dHIpuNQLqpl26Ec5XbdAR3jQBc62kD4PS1CokA0geGsoNDcz5yfREBOFpAWXN/udomAEjnYYuC6Y/qtp9tuY5ydrIDAHZK3vGda9oZ2vsHAIBpOwV7TF3W3F8eoYsekVXAKfq6tqv4op695rM1g14XQ71HABwAOZuHNCBABGDb41J6AKBY0PbqyL3mszWHygGgw4D2FEC8Q0ACgag3AGDb4xUFgNwM3yNz2VMB22cSvADAYk5tq3dxEUB6JhAlLxHpshYAAAAARgoEC4C0OjnOVJ0I4Op2TZnDJwfGdpy/S70HHgCPPXQvP9xw1fqvZAxZReReO0zlJaK8fq/6i/qI21jifrTtSGTk/alEvvXTXd92MdA2vbI9cav9MM78k5Gdh3OiiHpd/RftLSwCiBpErO78z/ZrxNhFpf0BtZkCPHbgXk48ACDZkAIAbM8wRTd1UAOAOX7b+QUDOtF0RCMrlhkNOCrAeQcAeziofdQ3v6Zi7i/qM3AAkCsuCytGYNXOLJHe90ht+9CHbsSz/V2MeJ9e9oX8uZ70HgLb8lUbZX585Ae8KJ2D2V5PTm87otumzwVAx5FiolZ7NE2vsLMIQAMAnV14B0DnNmBKwSjqbFVWvY9iYKYAAEC+K1USAB5PwLF1aNv0ygiAOz4RtZqpqUB7UY0D4MKuEUApAMiZ66vqUbsIQB7hlXN2xxHRdUTznV90pHxst4gITJ9KU9VLGJAY8UU6X+8d0Olh69C26buuAfBXcLMattrhtVhVj2hk5XKjKYAcQd257UVdk51+v2FnctpvZ8BU2DsA4CRzdTJXCgAeR36hsK1D26bvCoAW837m/Gw5gC0KElGjwf4PAPDkAsYPA+mmAHJ9il4M89R+52JUdwG83AfPeS5fOWfWLDq5NtS0PabpZHvK2ItYA+DhP1sHYJFAe0WdTQsiBoAVRhGA3G7fc3+5fNPzAWobAQhBAIBkNdx1kU52qO6LZuyKzFHYKOn3Y+rYpum6AoA3o+30cZOo2V4PEPfWGwCAr95FBOCopPEioMGutryqaO8CrG6fHdBjBGC6r8DUsU3Tae/jCwCwKECE/1ygiE8D5AjAdOApOwKQ212buwChTgGMASBGNSGUocNqAbBmhxPCqguA9pyf3Q3gMEivAbBFwOwUAADozQy8RwChhP5yCKu8C7Dys9JTYqnHRQ0W7Yq+C6AdidsN1S3u6X6X9VKZa8d+OlOAlPNTTCMXr+xq6Tr7QwSQlQ8A6A2cnVxGdwHY6CXmr2mnVzwokq5SuABoj/idOwDJdsCRlRcBAI42m84OADiKaXQXIL2zTcxj+XRWfyCsPKdesmwJNRoNb1uOTSMAIZNqjt/r3D936pjWS2wD5t/FytDfdgqqiwTk1Xzb9HJ9VA9z1W4NQBeCOfpb5bJrASAW6XjNUw+2iIhAAwHZsT72iY/RotMX1RsAopfbTp+WbuSi7A5A7VRCkcDWoW3TAwCVc9ViKmS8CCg/KGJYnX4tAorq6ABedARgKItxMlV7VA6tu49vm0818osGIAIw7spqJjQGQHpUM7wDwLIAAG79DgB018/bGoBu5HDrxuJy286B5ZosG/kkD+3Hdm3M/FTVZwF0I5KuHwctAjCNbHxbmE7n2kUAOsPxLbCv8lwBMHLhUn6fenznVQCAwc5HU8fw1b8AQJ8jAF0H9wsUpo6tqq+unpl8cUzT/559ysx0VVxn6KpyTNsnytf1i1wPXft19TZdBLMtxza9OH9Clc/1aU1luctHjKpamzUAYTA6Q/NtWKYdoDuizNYBdA4IAGQV1dmFkbf0kAgA8BQB6EZK+XdZePmkoKJAoDM0+bq6drkCpgebNcqiA5DrCOy7f3T9YtToHhIBAABARgEAwMyLAAAznUwHCFW6gZkCqEYc1cgvz63kDTNFLc7oRhrXqUCvEYObOS3MbVoP3+ls26G7vnybU44UdZGk7ne5vnJ632sAOvuT6wMAHPb7ZhtdBwAAiQn6ioR0QAAAuitUeQDoOrAuEYDoJh1AdAbvO3R2nct38rd3Isp3K3Tlu7ZHt1bhEgG0Wi16/JEHMk0wPZW609+ez6i0tZ/6AoA9Biv2ubPbZM//iGte9GKgrgN0Bq3LP5AASG1D5u3v8vyBzmF17bcFigsA2LUKmwIYPKqdp4Wt/ZQOANsORXooAAXqo4D+edT6tBUtgQJQQFIAAIBJQIGAFQAAAu58NB0KAACwASgQsAIAQMCdj6ZDAQAANgAFAlYAAAi489F0KAAAwAagQMAKAAABdz6aDgUAANgAFAhYAQAg4M5H06EAAAAbgAIBKwAABNz5aDoUAABgA1AgYAUAgIA7H02HAgAAbAAKBKwAABBw56PpUAAAgA1AgYAVAAAC7nw0HQoAALABKBCwAgBAwJ2PpkMBAAA2AAUCVgAACLjz0XQoAADABqBAwAoAAAF3PpoOBQAA2AAUCFgBACDgzkfToQAAABuAAgErAAAE3PloOhQAAGADUCBgBQCAgDsfTYcCAABsAAoErAAAEHDno+lQAACADUCBgBUAAALufDQdCgAAsAEoELACAEDAnY+mQwEAADYABQJWAAAIuPPRdCgAAMAGoEDACgAAAXc+mg4FAADYABQIWAEAIODOR9OhAAAAG4ACASsAAATc+Wg6FAAAYANQIGAFAICAOx9NhwIAAGwACgSsAAAQcOej6VAAAIANQIGAFQAAAu58NB0KAACwASgQsAIAQMCdj6ZDAQAANgAFAlYAAAi489F0KAAAwAagQMAKAAABdz6aDgUAANgAFAhYAQAg4M5H06EAAAAbgAIBKwAABNz5aDoUAABgA1AgYAUAgIA7H02HAgCAow1cv3EqdizCKfvd+8ZK7cNFi4Yz7Z+ceMiqPdOHp63Sy4nLbr9T5SuQuVTjqUD7nasAAAAAzkZUYgEAgKP4AAAA4GhCpWYHABzlBwAAAEcTKjU7AOAoPwDgBgBb+eU1A6wB2CqYTQ8AuOlHAAAA4GhCpWYHABzlBwAAAEcTKjU7AOAoPwAAADiaUKnZAQBH+QEANwBgH4CjATpmBwAcBQQAAABHEyo1OwDgKD8AAAA4mlCp2QEAR/kBAADA0YRKzQ4AOMoPALgBwFZ+7AOwVax7egDAUU8AAABwNKFSswMAjvIDAACAowmVmh0AcJQfAAAAHE2o1OzBA6DfDjyyfMSqw23vkx99+ntEUYMu/+o3+HVs81tVjoj2PrKdZ7l6zY5MVtt22l7XOH0cE8UxTR85yrOI+or8MzNzQftA0I1nRlA3ALz4ox9wAHxx040AQIoSAoQAQBadAECfT/SxHRltR/AXf/JEAoBrvp7padtyjEdYw4S27TYsVplM1V4AAADIKFC7COC5J4miiL648QYAIAcPAAAAUGsAiMZN/+wpDoJV67/Sl6mA7YjtOyKwjXAECLAGYNtzNUtftwigA4CDT/P/BADyDRYASHTBGkDN1gDmAfBMGwCbKjUVkN3RNRKwHfnl64d+ohAAUFcAHHqWiGJatQ4A6Ba0AgA1C+ltm1PXKUAnEuAgIFq17rq+rgVMt687cvFlRl1iGwmYjvy6egAARt1T30QAQDF9q3M816kAAOCn3zAFqOkUwDYCuOmK+60s6o7vXmuU3hQEviMA0+siAjDqxvomQgSQ9C0AUF8b79YyRAAVjQBMQ1yd2YqRULUGYOv48vVsI4EFob+0RqCLBGRdRPt05ap0QgSgs6Ca/17VCAAAyDc8AMCvQyIC6FMEoBvZ5G4dFACIeusiAVX7H3voPieLFpGNrX6iPptHrwzaB4JuPDOafkUAAED+Y9AAgBP/nDMDAAMeAahWu3Vzf2E5rmsAphGASCeD0DXS6bU8RABJjwAAAIDzKMIK0E0BAAAvMnsvBACoCQBUlqHbAdjvCEAGga8IwLYcRACIALgCg74GoLoNJhwNAMhHIwAAANQKALar4cIBLl866iWsNJ0CIALwIre3QjAFqMkUAACYtnIKRACIALxGAMKgVHNRebVa3P6ydVyVlZuu+sv1KDsCEO3pdQ7vmh/7AKy4Wb/EvtYAAACzh4N83Q3o9faffH0AoH4+bdUiXwCQR2BVJXyP/PJ1TOsh8hUdAaj2KfhyYFUEoHsaEFMATAG8TgFMHQ8ASAwPALAapwpLjEVAz4uAprfllHP5w3aLWbo1AZ3l3LntRV0Sq99v2Lk0k151IlDRAOhEBu2TicTf5TUXTAGsurd+iX1NAVQGJyumWvTrdTELAEgUUC0iykAGALIWgwjAcwSgckjTh4FsV8NdkexrJ6CoB9sPELffxxc1Gsrq9SsCkCsgXxcRgKsFDXh+3xEAAGB2NwAAqIbjIAJABODVEnU7AlWRkG3k02s5iAAwBcgogAjA7jBQHS0AAJ1C1fodEUCfIgDR7bq1ANuR0NWcilgD6FYn1/b7zo81AFcLGvD8/YoAAIBEAd8OLJufDqCYAmAKUMoUYNAAIIfyukih19Df1YFV448pCBABDPgI7lp9RAD5awAAgKtlDUZ+rAFgDSBjqbqRXBUJqPLpQv6i3UT3dCYigKJ7oOLlIwLIRgAAQMUN1nP1EAH0OQLQrQXo5q6e+7+w4kYuXEoUVce8VLrizUCFmcBgFNzvCCAYACxbkgCA/cu2BpcMAwAg3x+rg+iSeOEbALo5rzDEXneymcrkutXW9Drapx/XtrcGAwCmkvY1HQDgeQoAAGTtdxUDQMnOz2qECAARQK4CviIA1Zl/4qLyY6hFRQKmAOqIwcNz9rfexgKjCAAA6OuobnOx3nrd5goVTxs8AFj/OMzRdQD40pqrKW4MlW4FiAAQARQSAahGfvkkHNWRYapIoNe7AT1FADwA6G0s0B24wUVvtToLgvLLQOXIyOz3uBOx6A5YkTtd1hV3AUpnc7kVcI0ABh4AniOAPIeM4piYyzLImDn4vE0sAMSBe1OzlYgAADf/6Q37btesVO6qAkCIZBsJWEcAYoTucnpPtw4zigDYFMMbAO5JRn8escS0at0mK3tCBJCVCwBwvAtQVARQKwCw8Z9H7T4iAAYAThNeHiIAK/4tSPx/ydeGLbeId0AAAAAASUVORK5CYII=", "relative_path": "../textures/models/ben/merlin.png"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "cf2edd07-eb3f-1d93-d994-e27c3bfc6244": {"name": "armorLeftArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "-22.5"}], "uuid": "f9b39280-5113-1815-e45a-f0dd16603583", "time": 0, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}