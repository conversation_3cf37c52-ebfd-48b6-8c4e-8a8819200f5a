{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "merlinisapien", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 24.18915213854678, -6], "to": [3, 30.189152138546778, 2], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, -1.1108478614532196, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 1}, "east": {"uv": [0, 8, 8, 16], "texture": 1}, "south": {"uv": [24, 8, 32, 16], "texture": 1}, "west": {"uv": [16, 8, 24, 16], "texture": 1}, "up": {"uv": [16, 8, 8, 0], "texture": 1}, "down": {"uv": [24, 0, 16, 8], "texture": 1}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 24.18915213854678, -6], "to": [3, 30.189152138546778, 2], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, -1.1108478614532196, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 1}, "east": {"uv": [32, 8, 40, 16], "texture": 1}, "south": {"uv": [56, 8, 64, 16], "texture": 1}, "west": {"uv": [48, 8, 56, 16], "texture": 1}, "up": {"uv": [48, 8, 40, 0], "texture": 1}, "down": {"uv": [56, 0, 48, 8], "texture": 1}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 23, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 30], "texture": 1}, "east": {"uv": [16, 20, 20, 30], "texture": 1}, "south": {"uv": [32, 20, 40, 30], "texture": 1}, "west": {"uv": [28, 20, 32, 30], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 23, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 46], "texture": 1}, "east": {"uv": [16, 36, 20, 46], "texture": 1}, "south": {"uv": [32, 36, 40, 46], "texture": 1}, "west": {"uv": [28, 36, 32, 46], "texture": 1}, "up": {"uv": [28, 36, 20, 32], "texture": 1}, "down": {"uv": [36, 32, 28, 36], "texture": 1}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 18, -2], "to": [7, 23, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 26], "texture": 1}, "east": {"uv": [40, 20, 44, 26], "texture": 1}, "south": {"uv": [52, 20, 54, 26], "texture": 1}, "west": {"uv": [48, 20, 52, 26], "texture": 1}, "up": {"uv": [48, 20, 44, 16], "texture": 1}, "down": {"uv": [52, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 18, -2], "to": [7, 23, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 46, 42], "texture": 1}, "east": {"uv": [40, 36, 44, 42], "texture": 1}, "south": {"uv": [52, 36, 54, 42], "texture": 1}, "west": {"uv": [48, 36, 52, 42], "texture": 1}, "up": {"uv": [48, 36, 44, 32], "texture": 1}, "down": {"uv": [52, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 18, -2], "to": [-5, 23, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 38, 58], "texture": 1}, "east": {"uv": [32, 52, 36, 58], "texture": 1}, "south": {"uv": [44, 52, 46, 58], "texture": 1}, "west": {"uv": [40, 52, 44, 58], "texture": 1}, "up": {"uv": [40, 52, 36, 48], "texture": 1}, "down": {"uv": [44, 48, 40, 52], "texture": 1}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 18, -2], "to": [-5, 23, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 54, 58], "texture": 1}, "east": {"uv": [48, 52, 52, 58], "texture": 1}, "south": {"uv": [60, 52, 62, 58], "texture": 1}, "west": {"uv": [56, 52, 60, 58], "texture": 1}, "up": {"uv": [56, 52, 52, 48], "texture": 1}, "down": {"uv": [60, 48, 56, 52], "texture": 1}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 6, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 7, 26], "texture": 1}, "east": {"uv": [0, 20, 4, 26], "texture": 1}, "south": {"uv": [12, 20, 15, 26], "texture": 1}, "west": {"uv": [8, 20, 12, 26], "texture": 1}, "up": {"uv": [8, 20, 4, 16], "texture": 1}, "down": {"uv": [12, 16, 8, 20], "texture": 1}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 6, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 7, 42], "texture": 1}, "east": {"uv": [0, 36, 4, 42], "texture": 1}, "south": {"uv": [12, 36, 15, 42], "texture": 1}, "west": {"uv": [8, 36, 12, 42], "texture": 1}, "up": {"uv": [8, 36, 4, 32], "texture": 1}, "down": {"uv": [12, 32, 8, 36], "texture": 1}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 6, -2], "to": [-0.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 23, 58], "texture": 1}, "east": {"uv": [16, 52, 20, 58], "texture": 1}, "south": {"uv": [28, 52, 31, 58], "texture": 1}, "west": {"uv": [24, 52, 28, 58], "texture": 1}, "up": {"uv": [24, 52, 20, 48], "texture": 1}, "down": {"uv": [28, 48, 24, 52], "texture": 1}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 6, -2], "to": [-0.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 7, 58], "texture": 1}, "east": {"uv": [0, 52, 4, 58], "texture": 1}, "south": {"uv": [12, 52, 15, 58], "texture": 1}, "west": {"uv": [8, 52, 12, 58], "texture": 1}, "up": {"uv": [8, 52, 4, 48], "texture": 1}, "down": {"uv": [12, 48, 8, 52], "texture": 1}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 9.947620560996004, 0.7906687144060385], "to": [1.5, 12.947620560996004, 7.7906687144060385], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [35.00000000000003, 0, 0], "origin": [0, 11.447620560996004, 7.540668714406038], "uv_offset": [16, 32], "faces": {"north": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "east": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "south": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "west": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "up": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "down": {"uv": [45.75, 23, 49.75, 26], "texture": 1}}, "type": "cube", "uuid": "d03ce662-7d2c-c6e5-82c3-40c6ace6d96b"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 9.947620560996004, 1.7906687144060387], "to": [1.5, 12.947620560996004, 7.7906687144060385], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [35.00000000000003, 0, 0], "origin": [0, 11.447620560996004, 7.540668714406038], "uv_offset": [16, 16], "faces": {"north": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "east": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "south": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "west": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "up": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "down": {"uv": [45.75, 23, 49.75, 26], "texture": 1}}, "type": "cube", "uuid": "b5380ea1-3faf-9ec9-e822-b89d4d3ceabc"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 11.425153049580063, 7.74889003518961], "to": [1, 12.425153049580063, 12.74889003518961], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [45.00000000000001, 0, 0], "origin": [0, 11.447620560996004, 7.540668714406038], "uv_offset": [16, 16], "faces": {"north": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "east": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "south": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "west": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "up": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "down": {"uv": [45.75, 23, 49.75, 26], "texture": 1}}, "type": "cube", "uuid": "f5d7a43e-1d5e-5af7-f24e-61fdb5c936c8"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 11.425153049580063, 7.74889003518961], "to": [1, 12.425153049580063, 12.74889003518961], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [45.00000000000001, 0, 0], "origin": [0, 11.447620560996004, 7.540668714406038], "uv_offset": [16, 32], "faces": {"north": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "east": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "south": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "west": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "up": {"uv": [45.75, 23, 49.75, 26], "texture": 1}, "down": {"uv": [45.75, 23, 49.75, 26], "texture": 1}}, "type": "cube", "uuid": "3bd8ea16-2f34-8f85-2982-20525583a404"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 29.88915213854678, -2.865675097913294], "to": [0, 31.389152138546777, 0.134324902086706], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [-3, -0.11084786145321956, 1.134324902086706], "faces": {"north": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "east": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "south": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "west": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "up": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "down": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}}, "type": "cube", "uuid": "1a4f03a4-f786-bfe8-f2fd-8f4e40d5d4df"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 29.88915213854678, -2.865675097913294], "to": [0, 31.389152138546777, 0.134324902086706], "autouv": 0, "color": 0, "inflate": 1, "origin": [-3, -0.11084786145321956, 1.134324902086706], "uv_offset": [32, 0], "faces": {"north": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "east": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "south": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "west": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "up": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "down": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}}, "type": "cube", "uuid": "b68a2460-4e3d-cb39-d68c-5e6af9591dae"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 32.38915213854678, -0.865675097913294], "to": [0, 33.38915213854678, 4.134324902086706], "autouv": 0, "color": 0, "inflate": 1, "rotation": [-32.5, 0, 0], "origin": [-0.5, 32.38915213854678, 1.634324902086706], "uv_offset": [32, 0], "faces": {"north": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "east": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "south": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "west": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "up": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "down": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}}, "type": "cube", "uuid": "00a615c8-b428-4c35-73f4-ca13f9036c44"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 32.38915213854678, -0.865675097913294], "to": [0, 33.38915213854678, 4.134324902086706], "autouv": 0, "color": 0, "inflate": 0.51, "rotation": [-32.5, 0, 0], "origin": [-0.5, 32.38915213854678, 1.634324902086706], "faces": {"north": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "east": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "south": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "west": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "up": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}, "down": {"uv": [9.75, 4.5, 12, 7.5], "texture": 1}}, "type": "cube", "uuid": "b1a17c3b-051d-75c0-2a37-2e2de19ae9ae"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7.7, 9, -2], "to": [-5, 17, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 56, 54.7, 64], "texture": 1}, "east": {"uv": [48, 56, 52, 64], "texture": 1}, "south": {"uv": [60, 56, 62.7, 64], "texture": 1}, "west": {"uv": [56, 56, 60, 64], "texture": 1}, "up": {"uv": [56, 52, 52, 48], "texture": 1}, "down": {"uv": [60, 48, 56, 52], "texture": 1}}, "type": "cube", "uuid": "e8cd19b3-fbff-8248-71ee-86520bbd14bd"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7.7, 9, -2], "to": [-5, 17, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 56, 38.7, 64], "texture": 1}, "east": {"uv": [32, 56, 36, 64], "texture": 1}, "south": {"uv": [44, 56, 46.7, 64], "texture": 1}, "west": {"uv": [40, 56, 44, 64], "texture": 1}, "up": {"uv": [40, 52, 36, 48], "texture": 1}, "down": {"uv": [44, 48, 40, 52], "texture": 1}}, "type": "cube", "uuid": "a2e1d59d-7128-fa2d-f155-15d3e2a0683a"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 9, -2], "to": [7.7, 17, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 24, 46.7, 32], "texture": 1}, "east": {"uv": [40, 24, 44, 32], "texture": 1}, "south": {"uv": [52, 24, 54.7, 32], "texture": 1}, "west": {"uv": [48, 24, 52, 32], "texture": 1}, "up": {"uv": [48, 20, 44, 16], "texture": 1}, "down": {"uv": [52, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "b63b6c8b-5afd-cf98-df46-43af188e6a87"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 9, -2], "to": [7.7, 17, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 40, 46.7, 48], "texture": 1}, "east": {"uv": [40, 40, 44, 48], "texture": 1}, "south": {"uv": [52, 40, 54.7, 48], "texture": 1}, "west": {"uv": [48, 40, 52, 48], "texture": 1}, "up": {"uv": [48, 36, 44, 32], "texture": 1}, "down": {"uv": [52, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "8a5c403f-f0f5-87d3-e8be-6d7843a3d3a0"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -2], "to": [-0.5, 5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, -7, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 59, 23, 64], "texture": 1}, "east": {"uv": [16, 59, 20, 64], "texture": 1}, "south": {"uv": [28, 59, 31, 64], "texture": 1}, "west": {"uv": [24, 59, 28, 64], "texture": 1}, "up": {"uv": [24, 52, 20, 48], "texture": 1}, "down": {"uv": [28, 48, 24, 52], "texture": 1}}, "type": "cube", "uuid": "4943a072-88c2-422b-6418-92273e2a38ae"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -2], "to": [-0.5, 5, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, -7, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 59, 7, 64], "texture": 1}, "east": {"uv": [0, 59, 4, 64], "texture": 1}, "south": {"uv": [12, 59, 15, 64], "texture": 1}, "west": {"uv": [8, 59, 12, 64], "texture": 1}, "up": {"uv": [8, 52, 4, 48], "texture": 1}, "down": {"uv": [12, 48, 8, 52], "texture": 1}}, "type": "cube", "uuid": "3dfa7780-bc5e-d35d-9da1-02c3a0e5fada"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, -7, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 27, 7, 32], "texture": 1}, "east": {"uv": [0, 27, 4, 32], "texture": 1}, "south": {"uv": [12, 27, 15, 32], "texture": 1}, "west": {"uv": [8, 27, 12, 32], "texture": 1}, "up": {"uv": [8, 20, 4, 16], "texture": 1}, "down": {"uv": [12, 16, 8, 20], "texture": 1}}, "type": "cube", "uuid": "206b6784-e50e-e883-e7c9-8c6237e35ef2"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 5, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, -7, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 43, 7, 48], "texture": 1}, "east": {"uv": [0, 43, 4, 48], "texture": 1}, "south": {"uv": [12, 43, 15, 48], "texture": 1}, "west": {"uv": [8, 43, 12, 48], "texture": 1}, "up": {"uv": [8, 36, 4, 32], "texture": 1}, "down": {"uv": [12, 32, 8, 36], "texture": 1}}, "type": "cube", "uuid": "651be10f-f46e-6670-00ca-0cd936099bb7"}, {"name": "feet", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 0, -3], "to": [-0.5, 0, -3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, -7, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "east": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "south": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "west": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "up": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "down": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}}, "type": "cube", "uuid": "c0e5f686-b92f-589e-30a2-d98e16dbeda3"}, {"name": "feet", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 0, -3], "to": [-2, 0, -3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-2.0999999046325684, -7, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "east": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "south": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "west": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "up": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "down": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}}, "type": "cube", "uuid": "c4a72891-80f5-4a20-e63f-6a3199e7991a"}, {"name": "feet", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -3], "to": [-3.5, 0, -3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-3.0999999046325684, -7, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "east": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "south": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "west": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "up": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "down": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}}, "type": "cube", "uuid": "31520997-0f52-c261-af42-4f062e638a66"}, {"name": "feet", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.5, 0, -3], "to": [3.5, 0, -3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [3.0999999046325684, -7, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "east": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "south": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "west": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "up": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "down": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}}, "type": "cube", "uuid": "1f166727-e23d-b807-8436-b6aec3f296e2"}, {"name": "feet", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2, 0, -3], "to": [2, 0, -3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2.0999999046325684, -7, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "east": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "south": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "west": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "up": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "down": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}}, "type": "cube", "uuid": "6dca9673-f9ae-c952-b5e5-6a432a23ad55"}, {"name": "feet", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -3], "to": [0.5, 0, -3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, -7, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "east": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "south": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "west": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "up": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}, "down": {"uv": [21.75, 63.75, 21.75, 63.75], "texture": 1}}, "type": "cube", "uuid": "8b14de97-427a-f1c3-9f88-f4c7aef8ede9"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 22.494029367474152, -1.8682446420510233], "to": [3, 24.994029367474152, 1.4317553579489766], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [-10, 0, 0], "origin": [0, 24.744029367474152, 0.03175535794897666], "uv_offset": [16, 16], "faces": {"north": {"uv": [16, 5, 24, 8], "texture": 1}, "east": {"uv": [16, 5, 24, 8], "texture": 1}, "south": {"uv": [16, 5, 24, 8], "texture": 1}, "west": {"uv": [16, 5, 24, 8], "texture": 1}, "up": {"uv": [16, 5, 24, 8], "texture": 1}, "down": {"uv": [16, 5, 24, 8], "texture": 1}}, "type": "cube", "uuid": "b3c996d8-aec3-8314-e62b-359c42cc4ef3"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 26, -2], "to": [3, 26, 1.7999999999999998], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [-10, 0, 0], "origin": [-0.5, 25.5, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [16, 5, 24, 8], "texture": 1}, "east": {"uv": [16, 5, 24, 8], "texture": 1}, "south": {"uv": [16, 5, 24, 8], "texture": 1}, "west": {"uv": [16, 5, 24, 8], "texture": 1}, "up": {"uv": [16, 5, 24, 8], "texture": 1}, "down": {"uv": [16, 5, 24, 8], "texture": 1}}, "type": "cube", "uuid": "9853b33d-c5f2-66a5-d2ea-742cd9943159"}], "outliner": [{"name": "armorHead", "origin": [0, 28, -2], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", "9853b33d-c5f2-66a5-d2ea-742cd9943159", "b3c996d8-aec3-8314-e62b-359c42cc4ef3", {"name": "antenna", "origin": [0, 4, -2], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1a4f03a4-f786-bfe8-f2fd-8f4e40d5d4df", "b68a2460-4e3d-cb39-d68c-5e6af9591dae", "00a615c8-b428-4c35-73f4-ca13f9036c44", "b1a17c3b-051d-75c0-2a37-2e2de19ae9ae"]}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", {"name": "neck", "origin": [-0.5, 1, 0], "color": 0, "uuid": "157b9861-3222-cfa5-5165-a440147d90be", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "tail", "origin": [0, 8.395241121992008, 10.081337428812077], "color": 0, "uuid": "1b5dd3d9-63ab-1cf1-648a-7d677862f58c", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["b5380ea1-3faf-9ec9-e822-b89d4d3ceabc", "d03ce662-7d2c-c6e5-82c3-40c6ace6d96b", "3bd8ea16-2f34-8f85-2982-20525583a404", "f5d7a43e-1d5e-5af7-f24e-61fdb5c936c8"]}]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", "8a5c403f-f0f5-87d3-e8be-6d7843a3d3a0", "b63b6c8b-5afd-cf98-df46-43af188e6a87"]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "armNested", "origin": [-6, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134", "a2e1d59d-7128-fa2d-f155-15d3e2a0683a", "e8cd19b3-fbff-8248-71ee-86520bbd14bd"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806", "651be10f-f46e-6670-00ca-0cd936099bb7", "206b6784-e50e-e883-e7c9-8c6237e35ef2", "8b14de97-427a-f1c3-9f88-f4c7aef8ede9", "6dca9673-f9ae-c952-b5e5-6a432a23ad55", "1f166727-e23d-b807-8436-b6aec3f296e2"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb", "3dfa7780-bc5e-d35d-9da1-02c3a0e5fada", "4943a072-88c2-422b-6418-92273e2a38ae", "c0e5f686-b92f-589e-30a2-d98e16dbeda3", "c4a72891-80f5-4a20-e63f-6a3199e7991a", "31520997-0f52-c261-af42-4f062e638a66"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_merlinisapien.png", "name": "ov_merlinisapien.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "6ea21376-839c-cfb7-1484-89ff451835fa", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/ov_merlinisapien.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\merlinisapien.png", "name": "merlinisapien.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "6d10769d-5aff-a7c3-3eb7-8cf59d0b046b", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/merlinisapien.png"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json", "itemModelPath": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\geo\\gun.item.json"}}