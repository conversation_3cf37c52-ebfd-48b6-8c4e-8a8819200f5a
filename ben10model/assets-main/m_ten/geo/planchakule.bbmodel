{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "<PERSON><PERSON><PERSON><PERSON>", "model_identifier": "", "visible_box": [4, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 26, -4], "to": [4, 34, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 2, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 2}, "east": {"uv": [0, 8, 8, 16], "texture": 2}, "south": {"uv": [24, 8, 32, 16], "texture": 2}, "west": {"uv": [16, 8, 24, 16], "texture": 2}, "up": {"uv": [16, 8, 8, 0], "texture": 2}, "down": {"uv": [24, 0, 16, 8], "texture": 2}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 26, -4], "to": [4, 34, 4], "autouv": 0, "color": 0, "visibility": false, "inflate": 1, "origin": [0, 2, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [0, 0, 0, 0], "texture": null}, "east": {"uv": [0, 0, 0, 0], "texture": null}, "south": {"uv": [56, 8, 64, 16], "texture": 2}, "west": {"uv": [0, 0, 0, 0], "texture": null}, "up": {"uv": [48, 8, 40, 0], "texture": 2}, "down": {"uv": [56, 0, 48, 8], "texture": 2}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 25, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 32], "texture": 2}, "east": {"uv": [16, 20, 20, 32], "texture": 2}, "south": {"uv": [32, 20, 40, 32], "texture": 2}, "west": {"uv": [28, 20, 32, 32], "texture": 2}, "up": {"uv": [28, 20, 20, 16], "texture": 2}, "down": {"uv": [36, 16, 28, 20], "texture": 2}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 25, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 2}, "east": {"uv": [16, 36, 20, 48], "texture": 2}, "south": {"uv": [32, 36, 40, 48], "texture": 2}, "west": {"uv": [28, 36, 32, 48], "texture": 2}, "up": {"uv": [28, 36, 20, 32], "texture": 2}, "down": {"uv": [36, 32, 28, 36], "texture": 2}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 9, -2], "to": [7, 24, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 2}, "east": {"uv": [40, 20, 44, 32], "texture": 2}, "south": {"uv": [52, 20, 56, 32], "texture": 2}, "west": {"uv": [48, 20, 52, 32], "texture": 2}, "up": {"uv": [48, 20, 44, 16], "texture": 2}, "down": {"uv": [52, 16, 48, 20], "texture": 2}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 9, -2], "to": [7, 24, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 2}, "east": {"uv": [40, 36, 44, 48], "texture": 2}, "south": {"uv": [52, 36, 56, 48], "texture": 2}, "west": {"uv": [48, 36, 52, 48], "texture": 2}, "up": {"uv": [48, 36, 44, 32], "texture": 2}, "down": {"uv": [52, 32, 48, 36], "texture": 2}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 9, -2], "to": [-5, 24, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 2}, "east": {"uv": [32, 52, 36, 64], "texture": 2}, "south": {"uv": [44, 52, 48, 64], "texture": 2}, "west": {"uv": [40, 52, 44, 64], "texture": 2}, "up": {"uv": [40, 52, 36, 48], "texture": 2}, "down": {"uv": [44, 48, 40, 52], "texture": 2}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 9, -2], "to": [-5, 24, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 2}, "east": {"uv": [48, 52, 52, 64], "texture": 2}, "south": {"uv": [60, 52, 64, 64], "texture": 2}, "west": {"uv": [56, 52, 60, 64], "texture": 2}, "up": {"uv": [56, 52, 52, 48], "texture": 2}, "down": {"uv": [60, 48, 56, 52], "texture": 2}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 2}, "east": {"uv": [0, 20, 4, 32], "texture": 2}, "south": {"uv": [12, 20, 16, 32], "texture": 2}, "west": {"uv": [8, 20, 12, 32], "texture": 2}, "up": {"uv": [8, 20, 4, 16], "texture": 2}, "down": {"uv": [12, 16, 8, 20], "texture": 2}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 2}, "east": {"uv": [0, 36, 4, 48], "texture": 2}, "south": {"uv": [12, 36, 16, 48], "texture": 2}, "west": {"uv": [8, 36, 12, 48], "texture": 2}, "up": {"uv": [8, 36, 4, 32], "texture": 2}, "down": {"uv": [12, 32, 8, 36], "texture": 2}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, 0, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 2}, "east": {"uv": [16, 52, 20, 64], "texture": 2}, "south": {"uv": [28, 52, 32, 64], "texture": 2}, "west": {"uv": [24, 52, 28, 64], "texture": 2}, "up": {"uv": [24, 52, 20, 48], "texture": 2}, "down": {"uv": [28, 48, 24, 52], "texture": 2}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, 0, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 2}, "east": {"uv": [0, 52, 4, 64], "texture": 2}, "south": {"uv": [12, 52, 16, 64], "texture": 2}, "west": {"uv": [8, 52, 12, 64], "texture": 2}, "up": {"uv": [8, 52, 4, 48], "texture": 2}, "down": {"uv": [12, 48, 8, 52], "texture": 2}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "Ears", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 26, -3], "to": [2, 34, 5], "autouv": 0, "color": 0, "inflate": 1, "rotation": [0, -37.5, 0], "origin": [-2, 2, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [0, 0, 0, 0], "texture": null}, "east": {"uv": [0, 0, 0, 0], "texture": null}, "south": {"uv": [0, 0, 0, 0], "texture": null}, "west": {"uv": [48.5, 8, 56, 16], "texture": 2}, "up": {"uv": [0, 0, 0, 0], "texture": null}, "down": {"uv": [0, 0, 0, 0], "texture": null}}, "type": "cube", "uuid": "8b7cfb00-fc44-f296-7e8a-491eac0f6222"}, {"name": "Ears", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 26, -3], "to": [5, 34, 5], "autouv": 0, "color": 0, "inflate": 1, "rotation": [0, 37.5, 0], "origin": [2, 2, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [0, 0, 0, 0], "texture": null}, "east": {"uv": [56, 8, 48.5, 16], "texture": 2}, "south": {"uv": [0, 0, 0, 0], "texture": null}, "west": {"uv": [0, 0, 0, 0], "texture": null}, "up": {"uv": [0, 0, 0, 0], "texture": null}, "down": {"uv": [0, 0, 0, 0], "texture": null}}, "type": "cube", "uuid": "a7ffe042-6217-ab87-0a07-382c2b8fc9ac"}, {"name": "Nose", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 27, -9], "to": [1, 29, -4], "autouv": 0, "color": 0, "rotation": [-22.5, 0, 0], "origin": [0, 28, -6.5], "uv_offset": [13, 0], "faces": {"north": {"uv": [0, 0, 1, 1], "texture": 2}, "east": {"uv": [0, 0, 1, 1], "texture": 2}, "south": {"uv": [0, 0, 1, 1], "texture": 2}, "west": {"uv": [0, 0, 1, 1], "texture": 2}, "up": {"uv": [0, 0, 1, 1], "texture": 2}, "down": {"uv": [0, 0, 1, 1], "texture": 2}}, "type": "cube", "uuid": "ffadb0d0-95fb-8cc7-4838-39bef7a2cc43"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.25, 15, -3], "to": [1.25, 17.5, 3.25], "autouv": 0, "color": 0, "rotation": [22.5, 0, 0], "origin": [-0.25, 15, 0], "uv_offset": [15, 0], "faces": {"north": {"uv": [18, 2.5, 20, 4.5], "texture": 2}, "east": {"uv": [18, 2.5, 20, 4.5], "texture": 2}, "south": {"uv": [18, 2.5, 20, 4.5], "texture": 2}, "west": {"uv": [18, 2.5, 20, 4.5], "texture": 2}, "up": {"uv": [1.0625, 0, 1.8125, 1], "texture": 2}, "down": {"uv": [18, 2.5, 20, 4.5], "texture": 2}}, "type": "cube", "uuid": "6cfa8f82-ed57-975f-91e3-0737e6f41699"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.25, 14.3, 1.3897850598880135], "to": [1.25, 16.3, 8.389785059888013], "autouv": 0, "color": 0, "origin": [-0.24999999999999994, 15.798155256988416, 3.1309660147854936], "uv_offset": [14, -1], "faces": {"north": {"uv": [18, 2.5, 20, 4.5], "texture": 2}, "east": {"uv": [18, 2.5, 20, 4.5], "texture": 2}, "south": {"uv": [18, 2.5, 20, 4.5], "texture": 2}, "west": {"uv": [18, 2.5, 20, 4.5], "texture": 2}, "up": {"uv": [1.0625, 0, 1.8125, 1], "texture": 2}, "down": {"uv": [18, 2.5, 20, 4.5], "texture": 2}}, "type": "cube", "uuid": "ae905e94-998e-4287-cc44-eabbd0bd73a8"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.25, 15.891355640586635, 6.207129166307475], "to": [1.25, 21.891355640586635, 8.707129166307476], "autouv": 0, "color": 0, "rotation": [22.5, 0, 0], "origin": [0, 16.271718013269236, 5.685830574442491], "uv_offset": [14, -1], "faces": {"north": {"uv": [18, 2.5, 20, 4.5], "texture": 2}, "east": {"uv": [18, 2.5, 20, 4.5], "texture": 2}, "south": {"uv": [18, 2.5, 20, 4.5], "texture": 2}, "west": {"uv": [18, 2.5, 20, 4.5], "texture": 2}, "up": {"uv": [1.0625, 0, 1.8125, 1], "texture": 2}, "down": {"uv": [18, 2.5, 20, 4.5], "texture": 2}}, "type": "cube", "uuid": "a03ea9c0-0593-643a-2d8d-062975bb90af"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 33, -1.5], "to": [-2.75, 35, -0.75], "autouv": 0, "color": 0, "inflate": 0.51, "rotation": [0, 0, 22.5], "origin": [-3.125, 35.5, -1.125], "faces": {"north": {"uv": [40.75, 3.75, 41.25, 6], "texture": 2}, "east": {"uv": [40.75, 3.75, 41.25, 6], "texture": 2}, "south": {"uv": [40.75, 3.75, 41.25, 6], "texture": 2}, "west": {"uv": [40.75, 3.75, 41.25, 6], "texture": 2}, "up": {"uv": [40.75, 3.75, 43.25, 6], "texture": 2}, "down": {"uv": [40.75, 3.75, 41.25, 6], "texture": 2}}, "type": "cube", "uuid": "3ece95d5-e868-1bac-f8e7-13ab907c0a5f"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2.75, 33, -1.5], "to": [3.5, 35, -0.75], "autouv": 0, "color": 0, "inflate": 0.51, "rotation": [0, 0, -22.5], "origin": [3.125, 35.5, -1.125], "faces": {"north": {"uv": [41.25, 3.75, 40.75, 6], "texture": 2}, "east": {"uv": [41.25, 3.75, 40.75, 6], "texture": 2}, "south": {"uv": [41.25, 3.75, 40.75, 6], "texture": 2}, "west": {"uv": [41.25, 3.75, 40.75, 6], "texture": 2}, "up": {"uv": [43.25, 3.75, 40.75, 6], "texture": 2}, "down": {"uv": [41.25, 3.75, 40.75, 6], "texture": 2}}, "type": "cube", "uuid": "9a17ea78-0f7c-4fda-f983-d3bf153e5f2a"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.9999999046325683, -0.5, -3], "to": [3.9999999046325683, 0.5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, -1, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "east": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "south": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "west": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "up": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "down": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}}, "type": "cube", "uuid": "b86ac019-5cf8-84b3-10c1-e07f56a9c01e"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.9999999046325683, -0.5, -3], "to": [1.9999999046325683, 0.5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1.9000000953674316, -1, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "east": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "south": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "west": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "up": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "down": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}}, "type": "cube", "uuid": "3ae737f7-27b4-4225-c08d-85deba4cb640"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, -0.5, -3], "to": [-9.536743172944284e-08, 0.5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-3.9000000953674316, -1, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "east": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "south": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "west": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "up": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}, "down": {"uv": [7.075, 63.5, 7.825000000000001, 64], "texture": 2}}, "type": "cube", "uuid": "84a1cded-feda-6457-7108-eea6b2671713"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9.536743172944284e-08, -0.5, -3], "to": [9.536743172944284e-08, 0.5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [3.9000000953674316, -1, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "east": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "south": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "west": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "up": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "down": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}}, "type": "cube", "uuid": "eda0593c-64a4-174f-6eb4-a99b9d6bc06e"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, -0.5, -3], "to": [-3.9999999046325683, 0.5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, -1, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "east": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "south": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "west": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "up": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "down": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}}, "type": "cube", "uuid": "3b84be7b-a46f-635d-e1c0-1b926d987576"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.9999999046325683, -0.5, -3], "to": [-1.9999999046325683, 0.5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1.9000000953674316, -1, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "east": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "south": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "west": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "up": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}, "down": {"uv": [7.825000000000001, 63.5, 7.075, 64], "texture": 2}}, "type": "cube", "uuid": "d605e4ea-8e6c-0dce-66d3-c46a4aedc7ee"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 21.15177039930093, 11.267828588713023], "to": [0.5, 22.151770399300926, 14.267828588713023], "autouv": 0, "color": 0, "rotation": [-22.5, 0, 0], "origin": [0, 21.651770399300922, 10.767828588713025], "uv_offset": [15, 0], "faces": {"north": {"uv": [13, 54.5, 13.5, 53], "texture": 2}, "east": {"uv": [13, 54.5, 13.5, 53], "texture": 2}, "south": {"uv": [13, 54.5, 13.5, 53], "texture": 2}, "west": {"uv": [13, 54.5, 13.5, 53], "texture": 2}, "up": {"uv": [1.0625, 0, 1.8125, 1], "texture": 2}, "down": {"uv": [13, 54.5, 13.5, 53], "texture": 2}}, "type": "cube", "uuid": "8c9372a6-c98b-755d-9c39-df2741e52b5b"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 21.15177039930093, 8.267828588713027], "to": [1.5, 22.151770399300926, 13.267828588713027], "autouv": 0, "color": 0, "rotation": [-22.5, 0, 0], "origin": [0, 21.651770399300922, 10.767828588713025], "uv_offset": [15, 0], "faces": {"north": {"uv": [13, 54.5, 13.5, 53], "texture": 2}, "east": {"uv": [13, 54.5, 13.5, 53], "texture": 2}, "south": {"uv": [13, 54.5, 13.5, 53], "texture": 2}, "west": {"uv": [13, 54.5, 13.5, 53], "texture": 2}, "up": {"uv": [1.0625, 0, 1.8125, 1], "texture": 2}, "down": {"uv": [13, 54.5, 13.5, 53], "texture": 2}}, "type": "cube", "uuid": "4235ec44-1b55-7413-7ff3-27607e6f9dd6"}], "outliner": [{"name": "armorHead", "origin": [0, 26, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "3ece95d5-e868-1bac-f8e7-13ab907c0a5f", "9a17ea78-0f7c-4fda-f983-d3bf153e5f2a", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", "8b7cfb00-fc44-f296-7e8a-491eac0f6222", "a7ffe042-6217-ab87-0a07-382c2b8fc9ac", {"name": "antenna", "origin": [0, 2, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, "ffadb0d0-95fb-8cc7-4838-39bef7a2cc43"]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", {"name": "tail", "origin": [0, 3, -4], "rotation": [15, 0, 0], "color": 0, "uuid": "2e8cf8b9-6228-40fc-923e-a692adb0603e", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["6cfa8f82-ed57-975f-91e3-0737e6f41699", "ae905e94-998e-4287-cc44-eabbd0bd73a8", "a03ea9c0-0593-643a-2d8d-062975bb90af", "4235ec44-1b55-7413-7ff3-27607e6f9dd6", "8c9372a6-c98b-755d-9c39-df2741e52b5b", {"name": "tail_back", "origin": [0, 7, -1], "rotation": [-7.5, 0, 0], "color": 0, "uuid": "e1245989-aeb0-739c-2224-dfc0a2e8882a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", {"name": "wingRight", "origin": [6, 22, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 22, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-6, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "b86ac019-5cf8-84b3-10c1-e07f56a9c01e", "3ae737f7-27b4-4225-c08d-85deba4cb640", "84a1cded-feda-6457-7108-eea6b2671713", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb", "d605e4ea-8e6c-0dce-66d3-c46a4aedc7ee", "3b84be7b-a46f-635d-e1c0-1b926d987576", "eda0593c-64a4-174f-6eb4-a99b9d6bc06e"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_planchakule.png", "name": "ov_planchakule.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "b3ac011a-2620-71f4-3b48-fddadfb8566b", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAGDpJREFUeF7tnV2oXcd1x891ceXEdu1go3sRGNkUISzVJgRDqwdDECYNbmhTSJs8BALGbmJCSKSoIJoUY5oEQRQrhRonsWoIpBC3haaFmrYYUfCDWjAhxJaMECUWBnElbOJULrUSKpd9jubq7NGZPbNmzcf++N0XW2fPx5r/rPWbNbP32Wft2M597806/g6cO7nWdV17bf3xOzv772r/1y5um1/+v+2Xo804/MKu6LopKubWN4WNtDFeBdYAAAAYr3szMp8CAIAMwOcjXB+xAgAAAIzYvRmaT4Gs+3tf5811zgDynrGEzAFlpqsAACADmK73M/IZAAAAhMGEFQAAAGDC7s/QAQAAIAomrAAAAAATdn+GDgAAAFEwYQUAAACYsPszdAAAAIiCCSsAAADAhN2foQMAANC7KHjp1H+s/Ibog3t/J8hftfV7J0hGg4IEzdj/ykeBv/6731rZ5Vf+9cutz11fBw6t3zTG14Fzzq6sbVfg2q24QKCtL7N2HKV7BwBX8Bq5lyGwCgCS+gCgX06sDWBt/X6pUcaa6gDYtu3GrXTv6eePB4368598dGW5mPpHNh4I6jNXIV4IslD2+I++/97uXbvn///SmwdWyv3gncfmn585e2b26Mc/0/Jdbf1c89v3dgEAAOiFjzYB3BjSQKALAE3wN3+rAKCp3wsRKhgBAABABbe7vksDgObKxQ98Z6VN23/+ua3PXQCIrd8LESoYUR0Ad91z/9YW4Ilj7UM+lx5PHlh9SBhT/+CVmyvIfq1LtgDXtgBGFQBQziUBAAAo520dPZEB1JmG6gCw3wgkOcXnLkAdp8nV6zIEuvqw039TVls/17j63G7vANCIFXofn+cA+uxactu0AaytL7d4+DV6CYBQWfldgFClhlXOFciuld8enbb+sNTSWQsAeBRY50HUHrQCAAAADNqBMV6nAAAAADoPovagFQAAAGDQDozxOgUAAADQeRC1B60AAAAAg3ZgjNcpAAAAgM6DqD1oBdY0v81nRv72c79oibB9x73zf//yo+fV4tyxZ8PZxtv//vP5tds//AFnmbdOb6pt+PV/2TFv4+L511pt3f7Ibeq2NQ2keA7iwjNvVl8ENBpQV6cAAAjQDwAEiESRQSrQOwB0rfi2wiEZgF0nJiOYIgCO7dw3/5bm3o2dcwk/8p8/JFMQhPi//fan5vqd2jw3r9XXb30CgIBJBQAAIMBNWkUmBQCpOMvlJSt+igwgRUagGW/KujnPAA6v3zdfwdZvumVusskEjP1kBO2ZNAFvPjUr/4V335l/dOTCK73MoJJkABqnBgDx6gGAeO1S1wQAQkU1gW+6ijkDcJkZczYgHHLy4iUBYIwnEwhb+U0pMgCH2wMAPQ8AgF5DbQuulR8AeAL/saO3zks8e+hS9BykyABsO4aUCeQEgJkU+yzAlQlM5WzADnh7zz+UwDd2Fj8DMCu/CTxjSAwINABw9Q8A2jwGAN0pPwAQrt8uACzvLb/06VeDWpUC4Ns/+K2tds0prQ0gALBaehcIpnI24Ev1h7by9y4DsA+XjIFdMAgBwHLQL7s2AFioEfooMABYPNjjWvEnA4DQH9M88vDZlUuJnQGYU1LXkr//7r1bl2wYuACwHPQnXj/VmU2Y+9xmC+LKALTjDkpphIVKnAEYk3wAsDOBsT0nYD/Y45qqvp/623aLzwC0gSAFgDHYgKAJ6H/66gLGNgB+/2trs+VyIfEEAMK+DAQA2o/2jh4AoYHuEsLOAFx7/6+e/8m8ia+8+MGtpr7+0LXP/vuPF09S2X8NBJYB0AT/qr/f+NtbZsvtmTLms6/tuNZvc82VCaTWIwROvjI5MwAT8LYNBpg+23zPvtt76tIZg7R/890I37hdGe3gngRM7fCxADCC2yBY3ho0ZexUvwl8O9hXQQYArM4AcgOgmZvlIKwJgJC+AYAPfdb10AzAHP794Y//8bpMoPnArNT/8KE/EFkQ2l7oYWBqIIoG4yicMwMwXYam/raJvgxAO/7S31YMBYAZ11DOApxnAKkd3nf7LzRgQx0ntD0A0H0GAAAWHjcZAGgD3wSoNAMw9Uzgmn9LV34bEL72SgHApUso0FaVK5EBxGYCuTIAE4j2XtucTeTu1zdfQ1n5zTiuywAAwOLRZPt2YC5dfA7VdR0AXFMHAMR50hYAUjm4NAOwSW4f7sUNy13LPiy0T7Vz3QWwLXI9JyEZb0kAhGYCuVdAsyVxHQLnOm33bYVyj1viF5KyAODqCy+MaACg231qBwIAkIS3v+ya9HDD3+SiROgZwAM/OD4v/8+f+NTKprUZgetJwN/7+x/O+3v504+2+i0FAFemFKpvU65GBuDKBEqvgNL7+BJdu8raACw97lTjMO30AgD/e+gvrruPbwzMBYCm3fcd/XMAEPla8NqBAADSoKA6AMxzAE0wNn8NDOb//bMvz/978k8eUY103/eem9d/3ze+tfiv1U/puwC+wUjOBmIyAPusR3pq7npAKNfe26VXk7ka32nmUDoO3zz4rvdFB5+dvuu9AYAJTgMAA4EUADDBv6oPAHBS9LLKPjm+2b6WDv7Gj/qkgy/Iu64XB4AxxryQw/U1YM2gJHUNAOwXkuS6DSixbbnsqsygKwMIvatTI3hiNaBeegUAwNUfbgAA6Z2LFvuvQDYA3PqXn2uNfveu3fN/f/bE4tTdfiVX6UzAlfp/d//irsSZs2da9l/64nf6P5sRFpIBRIg2oioA4OpkmgwAAAzHu82dgJBv8w1nVGUtzQYAMwyTCZgMwHz+8sMHVo40dyZgr/zGiAdeONayx2QAY135zWCHnAEAAD0sAMBVDQGA3plKtwAA9IqLbgGt6m7bthtbL0vUmyRr4fLlX6nHIOuxXTrXk5ShNh3efDm0aJZytfW/6577Vf538MrNKl2GnEE1A1cHDwBY/Ix2rT8AAAA0vgcANOpFvChC2d111QEAAND4FADQqAcAZmwBZE9SKt0teXUAoJSUM4C6ZzCpzwCkd6GGfgsSAAAAlQJjywAAgNAdOATkEFDoMkmLkwHo5CQD0OknflussjsOAS0FAIDOowCATj8AUPk5jNQAkLoDzwHwIBDPAUijJmF5AKATkwxApx8ZABmAOoaULqiqrjaeQ0AOAVUeqKxMBqATEADo9CMDGFkGwG1AYUCQAZABCF0mafHUGQAAEE4PAAAAQpdJWhwA6ORkC6DTjy0AWwB1DCldUFV9TUtQu/eL518TGbR9x72i8qn7e+LY4vcHcv1p3yhkv1sxl5212n3ywOL3GlL/hfphKf9z9fPGz35aFSAAAACkjj1RewBgpACwiecicikCG6+0+8udAYiiYYKFUwMgdOV3+YN0CrT9jTYDAABSV5pmeQAw0gzAdmftSu8KDymBbfKTAdQFTyoAaP0gVgVtv6PNAABArEtNqx4AmEgG4HJrbWYQS+BUe8DQzOTp5xe/OOT6+/wnF7+YlEsnKVa0upZaAEw/Unu1fmePL7b/yWQAuRxbKnwpx7TtAgC6270+gEn9AAAsFJ3cbcDcALAd8cUfv9Tq0v7NQXPRlwHkzlhiV1JfYObWO9ZuAAAA5grkdgQA0EZAar0BgBTB7fJkAMonEX0OaAPAlDeZgPnNxIc+9KBoJlMHkjSFFhm7onAq+2PtTtW/b/59OtV+qSoAAABzH40NJJ+D5zr70QYeAGALkHQL4AogMoDVCEgVgLHgStW/FkRkAMoVONYBUh+qAQBZLpAqAGPnP1X/AMCad+mEaCdC2l/qU2lt4Bt7pGcAqQCm1U8W9teXrjX/2n7tkcTqSAYw8AwAAOgQoA3E2MDT9gsAHPMunRDtREj7q5UB2Kf+xo5P/NXH5v/79nO/iIqk2vpFGb1UqbT92v5845X6IxnARDIAAJDnMFAacACgPQ/JbgNKJ6Ive1itQ4RuAboAcMeejdl/HTrjW1xWXs9lf5QxEZVK26/tzzdEaRyMJgOQDhwALBRotgAAwBdW7utSvwMAZAAtBbQOEeqArucBAIDuS0Kh+qdacHyoktpDBjDSMwDbUQAAZwCrFBgNAMzgbAKaFdZFxlIrsIvcpfq3AWBO/5v0v/njDMC3tq6+Ll1xtfPts1JqDwCYaAYAABahpA1IacBp+wMAPgWuXjcTQwawWrDfPLq7dYEMINCxrGIAIE43UyvZbUDbDADQPTEAgAygUWB0WwAXCGrvwfvWPwBYzMjtj9ymWsLMGUpoI2+d3gwtGlVOas/pL7w6rl8GAgBhfgMAAECjwOgAUHpPJu3PDk/toVBs/wAAAACACqfAAKCtQCzAwvIcfym2ACPZAsQ6Uq0V2Lhmrf7JAMgARpUBAAD/ardcAgAAAAAw4S2A/WTgVN8IxBaALYBs6bRKx2YeNbYAV674h3rDDf4yyyVqbWFkVrpLAwAAoPIlLQBKOuDZg4vv/D/54gMrx/zEQy/PP9/1VPspwS6BtPe1pfetVZO1onJp+7X9+cYv1XM0twFjA7H2CgYAFl9GqvWnDUhpwGn78+kktQcAVP4yUCkAfHf/8dn+D1779R87CzCrf+NgJ37y0uyzJ7p/Ldg4otahpQ7rCwDp9dL2a/vzjU+qJwCYKACWtwLLwQ8AfCHWvi4NOADQ1i/Zl4GmugUwcvoc8eBdfzp7/I/aq7rJAmwAPPN3x2dPvfHNzkhI7cg++2Vh6S+dyn6p3an6dY1Qag8ZwMAzgFgAdG0BAIAfIKG62y0BgEwZgGlWmgkM/RBQ4ogp7wKkdmTpyhUeoqtLprLfZ/djR2/tNPXIw2e1Q2nV99ljdzaaDAAA+E/TAcA19wcACy0AQOUtgDYDkYAv5EGgpr2Qh4FS2S2xP+VSaez/5UfPq5r1rbhkAN3yJjsEjHUkrSNLtxy2HNr+Y8f99PPHW6aYA8KQ4G8qprI71n5V1C7ZDwBG8iRgrCNpHbkvAIgdvzSQtHr5+tPq6Wvftv/glZtbVfZu7Gz9+9TmOdH1Zw9d6jTBzgg4A7jn/vd8kya5LnUgrUNL+8uVAQCAMC8BAG2dOAMYyRkAAAAAjQK+MwlbJQAAAMIi52opbcbk60ybUfnaJwPoWQbgmzCudytw/Effb22hHv34Z7K+5bV0f8z/uBXI6qzjlm4xutIBWbq/KczhlMcIAJSzXzogS/enlIfqPVcAACgnqHRAlu5PKQ/Ve64AAFBOUOmALN2fUh6q91wBAKCcoNIBWbo/pTxU77kCAEA5QaUDsnR/Snmo3nMFAIBygkoHZOn+lPJQvecKAADlBJUOyNL9KeWhes8VAADKCSodkKX7U8pD9Z4rAACUE1Q6IEv3p5SH6j1XAAAoJ6h0QJbuTykP1XuuAABQTlDpgCzdn1IeqvdcAQCgnKDSAVm6P6U8VO+5AgBAOUGlA7J0f0p5qN5zBQCAcoJKB2Tp/pTyUL3nCgweAIfX75t/H//IhVeixmIHVKr5Sv1egFx2usa7e1f4LxSHaHbm7OKXke2/1DqF2EKZawpEBU2fBAQAeWYDAOTRtW+tAgDrjT7aCUodOLY9rpVUa3euTMBnLxlArpkLaxcAAIBOT9ECDQCEBWKtUgAgMQDMRGoDp/bKn2ocAKBWaIf1CwAAABlAWKyMstTkAZBqVs0pfeqV35UJpN4723cZUo/DzgRS259qHqfWDgBINOMAoFtIAJDI0RI3M3gAJNaD5lBgUgoAgElNN4NFgbYCAACPQIEJKwAAJjz5DB0FAAA+gAITVgAATHjyGToKAAB8AAUmrAAAmPDkM3QUAAD4AApMWAEAMOHJZ+goAADwARSYsAIAYMKTz9BRAADgAygwYQUAwIQnn6GjAADAB1BgwgoAgAlPPkNHAQCAD6DAhBUAABOefIaOAgAAH6iqwPrjd85/2Sn27449G6Kqb53eFJWXFpbac/oLr1aNwaqdS8Wl/PgUAAAAYHxezYiCFQAAACDYWSg4PgUAQGUAaCfAuKR072Pqafdksf2a/mvvwcYX0rIRaf1POv9af/ONTmpPbf9b004AAPC5BNe7FND6nzTgAEB7NgBA5VPYqeMBALAFUMWAdAWwO6udgqkGP4LKAAAAqNwYAKjk26p8eP2+1v34Ixde4RZxGml73QpbALYAcwcFAL2O02zGQfls0g6rYQOA/XfvnRt+4vVT8/+SCQxrHqXWAgCpYiMtDwBGOrGeYQGAac77dXt/s/KbC6EZgL11MPXJHIbhWABgGPOUzUp75QcA2aTuZcMAoJfTUs4oFwBObZ6bG3Hh3XdWngXYKz9nB+XmLGVPACClmgNsCwAMcNISmgwAEoo5xKZsANgrv72nd638voxhiNpMwWYAMIVZ7hgjAJi2A4gBcGznvs43uJg94/pNt7SUdX1uy3/g3EmxTdOeQt3oDQDMfNl7fnPdnOr7gMHpv24+StcWBxsAKD1FefsDAHn17XvrTgD4At0MbO/GztYYzf1j131l+3Ozd/QJRWbgUyjuugsA9t7f/Nv1wJCrfJxV1CqlAAAopXRP+wEAPZ2YQmaJtwCF7BpMN9u23ah6q612oJcv/6rqHN51z/2q8R+8crNKAjJDlXyzqs6jM70ftQEAAOiHJ8ZZAQDidNuqBQAAgNKFqlYHAEr5AQAAULpQ1eoAQCk/ANAB4K+37xHNgH3XiDMAkXzXFQYAOv1mAAAAKF2oanUAoJQfAAAApQtVrQ4AlPIDAACgdKGq1QGAUn4AoAMAzwEoHVBZHQAoBQQAAEDpQlWrAwCl/AAAAChdqGp1AKCUHwAAAKULVa0OAJTyAwAdAHgOQOmAyuoAQCkgAAAASheqWh0AKOUHAABA6UJVqwMApfwAAAAoXahq9cEDwH5LrevddkZl+wUYT93wP6oJuHj+NVH97TvuFZWXFi5tT2h/ucdt6xRqV+33KUjnN3V5AAAAVD4VGmgAQCVztsqjAYD91lrfW27N9VwZgO3wJlByB0JoQBqP0tpTuj9fJEjtIQPwKdrz667XVgOAsIkDAHVfqRY2S/lKDT4DsKUJfc11qgzANTWulUgbcNL+fK6jtUe64qbKPGL3/HY9MgCfhwzsOgCQTRgAIAOQeUzPS/tec21+l8C8WSbXGYBLJm3ApVr5Uq/E0kygtg6m/zd+9tPRZcGSEB3d4AGAZPpns1SBCABkuvel9GgBYAQ2e33zC0b2r9j+zft1EgzN8W3HAwBkAH2BURI7XA8GAYDV8gIAAJAk8PrWiA0C2z7z3ID2l23IABbKSnVIdQah7ZczgL5FbiJ7AECYkLUyAAAQNj+5S+k2wLmtK9A+GUCa7yZoV+LYqdb2SwYQq/xI6gEAADASV44aBhmA8tdtpStQqpTbzLa0/77cBWALEBWvySsBAACQxKliQaQForZftgBJpn+4jbAFSLMFiM1ISgPA7g8ADDd2k1gOAABAEkcaaCNsAQpvAVLtfWNX3FxnALH2kAHUJQcAAABJPVC6JwcASeUXNwYAAIDYaboqAICkcmZvDAAAgKROBgCSypm9MQCQCQAmtbUDQpvy2h4hDTjOANqHntwFyM6YfneQ6y4AAAibdy0QpQDkNmB7XsgACmUAWkd3hZM0AMgAyACWfQAAAICwpTqwlBRIWjBq+2MLEDixYy2WawuQ+n5/rgzg9kduSzq1d+zZELX31ulNUXm7cOn+fMaWtkfbHxlApgwAAPhCZXEdANQFIADIDIC+7blte8gAwkAVWkq7Iof2Y8pp+wMAAEDqc53ltQ4pNaZ0fz77Stuj7W9t/fE73/MNquu61ADTljb1iyWgPZZLT92gGX61d+EZo6WHYGQAupTb5yzSeNDGgbY/AAAAfD4tuq51SFFns9msdH8++0rbo+0PAAAAn0+LrmsdUtQZAFADEAAAAGnMcQbQocDQAAgAAMCkAXDhmTcnfRAOAAAAAEiqwLAamzT9mqnK/SSgyx20j8ByF2ChgDblnnwGMCxepbcWAEz7UWAAkD6mBtUiAAAAg3LYxMayBSj8JKCZP7YACyVqPwhDBpCYKENrrnQGkCrw+3IGcPiFXZ1T/uyhS53XAUDdiCEDKJwBAIC2wwMAAFBVATIA3RkAGUBV91V3TgZABqByIgCgkq96ZTEAju3c1/ntwQvvvjMf1PpNt7QG5/rcVuDAuZNimzQqkgGkzQB8e/7Hjt7amq69Gztb/z61eU503def7Rv2loNDQGH0AIC2YNKv4479DMAXkABAGHCZiztXW1+gG7tsgp94/dT80v6797ZMd31uE9813lyZARkAGUDmGOt18wCAMwCVg9pnAGQAKjmLV/5//7MQtmJY8TQAAAAASUVORK5CYII=", "relative_path": "../textures/models/ben/ov_planchakule.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\planchakule.png", "name": "planchakule.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "6c2b1cea-2f11-26ac-7cf0-c8e2f074fea6", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/planchakule.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\uprigg.png", "name": "uprigg.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "cfca5bfb-eaad-3f89-4f4d-0d6237ecde02", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/uprigg.png"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": true, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}