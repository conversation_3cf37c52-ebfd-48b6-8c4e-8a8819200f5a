{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "bio_amperi_orishan", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 26.2, -4], "to": [3, 31.2, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 1, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 3}, "east": {"uv": [0, 8, 8, 16], "texture": 3}, "south": {"uv": [24, 8, 32, 16], "texture": 3}, "west": {"uv": [16, 8, 24, 16], "texture": 3}, "up": {"uv": [16, 8, 8, 0], "texture": 3}, "down": {"uv": [24, 0, 16, 8], "texture": 3}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 26.2, -4], "to": [3, 31.2, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 1, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 3}, "east": {"uv": [32, 8, 40, 16], "texture": 3}, "south": {"uv": [56, 8, 64, 16], "texture": 3}, "west": {"uv": [48, 8, 56, 16], "texture": 3}, "up": {"uv": [48, 8, 40, 0], "texture": 3}, "down": {"uv": [56, 0, 48, 8], "texture": 3}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 12, -2], "to": [4, 18, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 25.5, 28, 31.5], "texture": 3}, "east": {"uv": [16, 25.5, 20, 31.5], "texture": 3}, "south": {"uv": [32, 25.5, 40, 31.5], "texture": 3}, "west": {"uv": [28, 25.5, 32, 31.5], "texture": 3}, "up": {"uv": [28, 25.5, 20, 21.5], "texture": 3}, "down": {"uv": [36, 21.5, 28, 25.5], "texture": 3}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 12, -2], "to": [4, 18, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 41.5, 28, 47.5], "texture": 3}, "east": {"uv": [16, 41.5, 20, 47.5], "texture": 3}, "south": {"uv": [32, 41.5, 40, 47.5], "texture": 3}, "west": {"uv": [28, 41.5, 32, 47.5], "texture": 3}, "up": {"uv": [28, 41.5, 20, 37.5], "texture": 3}, "down": {"uv": [36, 37.5, 28, 41.5], "texture": 3}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8, 11, -2], "to": [9, 16, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 27, 48, 32], "texture": 3}, "east": {"uv": [40, 27, 44, 32], "texture": 3}, "south": {"uv": [52, 27, 53, 32], "texture": 3}, "west": {"uv": [48, 27, 52, 32], "texture": 3}, "up": {"uv": [45, 20, 44, 16], "texture": 3}, "down": {"uv": [49, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8, 11, -2], "to": [9, 16, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 43, 48, 48], "texture": 3}, "east": {"uv": [40, 43, 44, 48], "texture": 3}, "south": {"uv": [52, 43, 53, 48], "texture": 3}, "west": {"uv": [48, 43, 52, 48], "texture": 3}, "up": {"uv": [45, 36, 44, 32], "texture": 3}, "down": {"uv": [49, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 3}, "east": {"uv": [0, 20, 4, 32], "texture": 3}, "south": {"uv": [12, 20, 16, 32], "texture": 3}, "west": {"uv": [8, 20, 12, 32], "texture": 3}, "up": {"uv": [8, 20, 4, 16], "texture": 3}, "down": {"uv": [12, 16, 8, 20], "texture": 3}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 3}, "east": {"uv": [0, 36, 4, 48], "texture": 3}, "south": {"uv": [12, 36, 16, 48], "texture": 3}, "west": {"uv": [8, 36, 12, 48], "texture": 3}, "up": {"uv": [8, 36, 4, 32], "texture": 3}, "down": {"uv": [12, 32, 8, 36], "texture": 3}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, 0, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 3}, "east": {"uv": [16, 52, 20, 64], "texture": 3}, "south": {"uv": [28, 52, 32, 64], "texture": 3}, "west": {"uv": [24, 52, 28, 64], "texture": 3}, "up": {"uv": [24, 52, 20, 48], "texture": 3}, "down": {"uv": [28, 48, 24, 52], "texture": 3}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, 0, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 3}, "east": {"uv": [0, 52, 4, 64], "texture": 3}, "south": {"uv": [12, 52, 16, 64], "texture": 3}, "west": {"uv": [8, 52, 12, 64], "texture": 3}, "up": {"uv": [8, 52, 4, 48], "texture": 3}, "down": {"uv": [12, 48, 8, 52], "texture": 3}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "Second Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.5, 9, -2], "to": [6.5, 16, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-2, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 25, 48, 32], "texture": 3}, "east": {"uv": [40, 25, 44, 32], "texture": 3}, "south": {"uv": [52, 25, 53, 32], "texture": 3}, "west": {"uv": [48, 25, 52, 32], "texture": 3}, "up": {"uv": [45, 20, 44, 16], "texture": 3}, "down": {"uv": [49, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "d5143400-d88e-1197-fb85-a3e184add1c1"}, {"name": "Second Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.5, 9, -2], "to": [6.5, 16, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-2, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 41, 48, 48], "texture": 3}, "east": {"uv": [40, 41, 44, 48], "texture": 3}, "south": {"uv": [52, 41, 53, 48], "texture": 3}, "west": {"uv": [48, 41, 52, 48], "texture": 3}, "up": {"uv": [45, 36, 44, 32], "texture": 3}, "down": {"uv": [49, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "99903b69-5f05-60a1-043b-badb8cc7a3c7"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 12, -2], "to": [9, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [1, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 3}, "east": {"uv": [40, 20, 44, 32], "texture": 3}, "south": {"uv": [52, 20, 56, 32], "texture": 3}, "west": {"uv": [48, 20, 52, 32], "texture": 3}, "up": {"uv": [48, 20, 44, 16], "texture": 3}, "down": {"uv": [52, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "f0fd6cac-c470-cf06-a480-e7813a2bab80"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 12, -2], "to": [9, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [1, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 3}, "east": {"uv": [40, 36, 44, 48], "texture": 3}, "south": {"uv": [52, 36, 56, 48], "texture": 3}, "west": {"uv": [48, 36, 52, 48], "texture": 3}, "up": {"uv": [48, 36, 44, 32], "texture": 3}, "down": {"uv": [52, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "e18cf142-f4a6-bd11-2d9a-956ab3197afd"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 19, -2], "to": [4.5, 25, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.5, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [19.5, 20, 28.5, 26], "texture": 3}, "east": {"uv": [15.5, 20, 19.5, 26], "texture": 3}, "south": {"uv": [31.5, 20, 40.5, 26], "texture": 3}, "west": {"uv": [27.5, 20, 31.5, 26], "texture": 3}, "up": {"uv": [28.5, 20, 19.5, 16], "texture": 3}, "down": {"uv": [36.5, 16, 27.5, 20], "texture": 3}}, "type": "cube", "uuid": "40e10c28-da11-3b03-ec94-845908fb79da"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 19, -2], "to": [4.5, 25, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.5, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [19.5, 36, 28.5, 42], "texture": 3}, "east": {"uv": [15.5, 36, 19.5, 42], "texture": 3}, "south": {"uv": [31.5, 36, 40.5, 42], "texture": 3}, "west": {"uv": [27.5, 36, 31.5, 42], "texture": 3}, "up": {"uv": [28.5, 36, 19.5, 32], "texture": 3}, "down": {"uv": [36.5, 32, 27.5, 36], "texture": 3}}, "type": "cube", "uuid": "5c266146-6a46-04a7-14aa-55bcc9ba10a9"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 25, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 32], "texture": 3}, "east": {"uv": [16, 20, 20, 32], "texture": 3}, "south": {"uv": [32, 20, 40, 32], "texture": 3}, "west": {"uv": [28, 20, 32, 32], "texture": 3}, "up": {"uv": [28, 20, 20, 16], "texture": 3}, "down": {"uv": [36, 16, 28, 20], "texture": 3}}, "type": "cube", "uuid": "a7ed8e98-c940-3352-4af0-0d1d8998b751"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 25, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 3}, "east": {"uv": [16, 36, 20, 48], "texture": 3}, "south": {"uv": [32, 36, 40, 48], "texture": 3}, "west": {"uv": [28, 36, 32, 48], "texture": 3}, "up": {"uv": [28, 36, 20, 32], "texture": 3}, "down": {"uv": [36, 32, 28, 36], "texture": 3}}, "type": "cube", "uuid": "7d4c2d3a-f82f-3efc-0c5c-796bc2cd87bf"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 25, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 32], "texture": 3}, "east": {"uv": [16, 20, 20, 32], "texture": 3}, "south": {"uv": [32, 20, 40, 32], "texture": 3}, "west": {"uv": [28, 20, 32, 32], "texture": 3}, "up": {"uv": [28, 20, 20, 16], "texture": 3}, "down": {"uv": [36, 16, 28, 20], "texture": 3}}, "type": "cube", "uuid": "e4e22ed5-1658-f1d6-c4ff-1f1d125406f1"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 13, -2], "to": [4, 25, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 3}, "east": {"uv": [16, 36, 20, 48], "texture": 3}, "south": {"uv": [32, 36, 40, 48], "texture": 3}, "west": {"uv": [28, 36, 32, 48], "texture": 3}, "up": {"uv": [28, 36, 20, 32], "texture": 3}, "down": {"uv": [36, 32, 28, 36], "texture": 3}}, "type": "cube", "uuid": "ad900b38-00e3-c521-fa92-46eba208e090"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-9, 12, -2], "to": [-5, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-1, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 3}, "east": {"uv": [48, 52, 52, 64], "texture": 3}, "south": {"uv": [60, 52, 64, 64], "texture": 3}, "west": {"uv": [56, 52, 60, 64], "texture": 3}, "up": {"uv": [56, 52, 52, 48], "texture": 3}, "down": {"uv": [60, 48, 56, 52], "texture": 3}}, "type": "cube", "uuid": "e05c41a6-9418-10eb-8998-39fe0afcc265"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-9, 12, -2], "to": [-5, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [-1, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 3}, "east": {"uv": [32, 52, 36, 64], "texture": 3}, "south": {"uv": [44, 52, 48, 64], "texture": 3}, "west": {"uv": [40, 52, 44, 64], "texture": 3}, "up": {"uv": [40, 52, 36, 48], "texture": 3}, "down": {"uv": [44, 48, 40, 52], "texture": 3}}, "type": "cube", "uuid": "29da019e-66f7-60c1-bb55-53f72e32f9d5"}, {"name": "Second Middle  Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 17, -1], "to": [6, 20, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-2, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 23.25, 44, 26.25], "texture": 3}, "east": {"uv": [40, 23.25, 44, 26.25], "texture": 3}, "south": {"uv": [52, 23.25, 52, 26.25], "texture": 3}, "west": {"uv": [48, 23.25, 52, 26.25], "texture": 3}, "up": {"uv": [44, 20, 44, 16], "texture": 3}, "down": {"uv": [48, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "e6589fe6-b122-dd09-d98f-d104eafeed3c"}, {"name": "Second  Middle Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 17, -1], "to": [6, 20, 1], "autouv": 0, "color": 0, "inflate": 0.53, "origin": [-2, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 39.25, 44, 42.25], "texture": 3}, "east": {"uv": [40, 39.25, 44, 42.25], "texture": 3}, "south": {"uv": [52, 39.25, 52, 42.25], "texture": 3}, "west": {"uv": [48, 39.25, 52, 42.25], "texture": 3}, "up": {"uv": [44, 36, 44, 32], "texture": 3}, "down": {"uv": [48, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "2be5effd-4e4b-7a3f-a205-867beb1933b4"}, {"name": "Right Middle  Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8.5, 17, -1], "to": [8.5, 20, 1], "autouv": 0, "color": 0, "inflate": 0.53, "origin": [0, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 39.25, 44, 42.25], "texture": 3}, "east": {"uv": [40, 39.25, 44, 42.25], "texture": 3}, "south": {"uv": [52, 39.25, 52, 42.25], "texture": 3}, "west": {"uv": [48, 39.25, 52, 42.25], "texture": 3}, "up": {"uv": [44, 36, 44, 32], "texture": 3}, "down": {"uv": [48, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "bbd67864-be07-a5c0-dba9-f139a68a0a73"}, {"name": "Right Middle Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8.5, 17, -1], "to": [8.5, 20, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 23.25, 44, 26.25], "texture": 3}, "east": {"uv": [40, 23.25, 44, 26.25], "texture": 3}, "south": {"uv": [52, 23.25, 52, 26.25], "texture": 3}, "west": {"uv": [48, 23.25, 52, 26.25], "texture": 3}, "up": {"uv": [44, 20, 44, 16], "texture": 3}, "down": {"uv": [48, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "380149a9-9736-084e-d4a4-3219828a8bc1"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 11, -2], "to": [-8, 16, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 26.75, 48, 31.75], "texture": 3}, "east": {"uv": [52, 26.75, 48, 31.75], "texture": 3}, "south": {"uv": [52, 26.75, 52, 31.75], "texture": 3}, "west": {"uv": [44, 26.75, 40, 31.75], "texture": 3}, "up": {"uv": [44, 20, 44, 16], "texture": 3}, "down": {"uv": [48, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "8f886b27-d071-f87c-a62c-68ef077832c9"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 11, -2], "to": [-8, 16, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 42.75, 48, 47.75], "texture": 3}, "east": {"uv": [52, 42.75, 48, 47.75], "texture": 3}, "south": {"uv": [52, 42.75, 52, 47.75], "texture": 3}, "west": {"uv": [44, 42.75, 40, 47.75], "texture": 3}, "up": {"uv": [44, 36, 44, 32], "texture": 3}, "down": {"uv": [48, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "70613f45-51d9-b0ca-6ab7-be74effe966c"}, {"name": "Second Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 9, -2], "to": [-5.5, 16, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [2, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [48, 41, 44, 48], "texture": 3}, "east": {"uv": [52, 41, 48, 48], "texture": 3}, "south": {"uv": [53, 41, 52, 48], "texture": 3}, "west": {"uv": [44, 41, 40, 48], "texture": 3}, "up": {"uv": [44, 36, 45, 32], "texture": 3}, "down": {"uv": [48, 32, 49, 36], "texture": 3}}, "type": "cube", "uuid": "d1165a6a-96ce-472c-6df4-493cc0b26a5f"}, {"name": "Second Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 9, -2], "to": [-5.5, 16, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [48, 25, 44, 32], "texture": 3}, "east": {"uv": [52, 25, 48, 32], "texture": 3}, "south": {"uv": [53, 25, 52, 32], "texture": 3}, "west": {"uv": [44, 25, 40, 32], "texture": 3}, "up": {"uv": [44, 20, 45, 16], "texture": 3}, "down": {"uv": [48, 16, 49, 20], "texture": 3}}, "type": "cube", "uuid": "e837c53f-4823-6242-146a-1aed22e4547e"}, {"name": "Left Middle Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7.5, 17, -1], "to": [-7.5, 20, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 23.25, 44, 26.25], "texture": 3}, "east": {"uv": [52, 23.25, 48, 26.25], "texture": 3}, "south": {"uv": [52, 23.25, 52, 26.25], "texture": 3}, "west": {"uv": [44, 23.25, 40, 26.25], "texture": 3}, "up": {"uv": [44, 20, 44, 16], "texture": 3}, "down": {"uv": [48, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "e8b655fb-2f66-4f29-4c42-ed3b4921b341"}, {"name": "Left Middle  Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7.5, 17, -1], "to": [-7.5, 20, 1], "autouv": 0, "color": 0, "inflate": 0.53, "origin": [1, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 39.25, 44, 42.25], "texture": 3}, "east": {"uv": [52, 39.25, 48, 42.25], "texture": 3}, "south": {"uv": [52, 39.25, 52, 42.25], "texture": 3}, "west": {"uv": [44, 39.25, 40, 42.25], "texture": 3}, "up": {"uv": [44, 36, 44, 32], "texture": 3}, "down": {"uv": [48, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "2571bcb2-7c58-9ed7-e5f9-556bf5568ce8"}, {"name": "Second  Middle Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 17, -1], "to": [-6, 20, 1], "autouv": 0, "color": 0, "inflate": 0.53, "origin": [2, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 39.25, 44, 42.25], "texture": 3}, "east": {"uv": [52, 39.25, 48, 42.25], "texture": 3}, "south": {"uv": [52, 39.25, 52, 42.25], "texture": 3}, "west": {"uv": [44, 39.25, 40, 42.25], "texture": 3}, "up": {"uv": [44, 36, 44, 32], "texture": 3}, "down": {"uv": [48, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "587806e2-1c2d-a091-c6c3-68142f1426b9"}, {"name": "Second Middle  Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 17, -1], "to": [-6, 20, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 23.25, 44, 26.25], "texture": 3}, "east": {"uv": [52, 23.25, 48, 26.25], "texture": 3}, "south": {"uv": [52, 23.25, 52, 26.25], "texture": 3}, "west": {"uv": [44, 23.25, 40, 26.25], "texture": 3}, "up": {"uv": [44, 20, 44, 16], "texture": 3}, "down": {"uv": [48, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "9a13037a-b0c6-8a18-fec6-862193b766db"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.5, 6, -2], "to": [3.5, 11, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 6, 41], "texture": 3}, "east": {"uv": [0, 36, 4, 41], "texture": 3}, "south": {"uv": [12, 36, 14, 41], "texture": 3}, "west": {"uv": [8, 36, 12, 41], "texture": 3}, "up": {"uv": [6, 36, 4, 32], "texture": 3}, "down": {"uv": [10, 32, 8, 36], "texture": 3}}, "type": "cube", "uuid": "6c3be3e9-612d-a7bc-e149-421ec948cc47"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.5, 6, -2], "to": [3.5, 11, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 6, 25], "texture": 3}, "east": {"uv": [0, 20, 4, 25], "texture": 3}, "south": {"uv": [12, 20, 14, 25], "texture": 3}, "west": {"uv": [8, 20, 12, 25], "texture": 3}, "up": {"uv": [6, 20, 4, 16], "texture": 3}, "down": {"uv": [10, 16, 8, 20], "texture": 3}}, "type": "cube", "uuid": "e9a21f60-5201-cbd9-0833-b4da34fd0e6d"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 6, -2], "to": [-1.5, 11, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1.0999999046325684, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 22, 57], "texture": 3}, "east": {"uv": [16, 52, 20, 57], "texture": 3}, "south": {"uv": [28, 52, 30, 57], "texture": 3}, "west": {"uv": [24, 52, 28, 57], "texture": 3}, "up": {"uv": [22, 52, 20, 48], "texture": 3}, "down": {"uv": [26, 48, 24, 52], "texture": 3}}, "type": "cube", "uuid": "08de7c4a-6349-f194-959b-275151915b4b"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 6, -2], "to": [-1.5, 11, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1.0999999046325684, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 6, 57], "texture": 3}, "east": {"uv": [0, 52, 4, 57], "texture": 3}, "south": {"uv": [12, 52, 14, 57], "texture": 3}, "west": {"uv": [8, 52, 12, 57], "texture": 3}, "up": {"uv": [6, 52, 4, 48], "texture": 3}, "down": {"uv": [10, 48, 8, 52], "texture": 3}}, "type": "cube", "uuid": "79228683-25b2-761e-35e7-9a6afc586045"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.7, 0, -2], "to": [-1.2000000000000002, 5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1.0999999046325684, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 58.75, 24, 63.75], "texture": 3}, "east": {"uv": [16, 58.75, 20, 63.75], "texture": 3}, "south": {"uv": [28, 58.75, 30.5, 63.75], "texture": 3}, "west": {"uv": [24, 58.75, 28, 63.75], "texture": 3}, "up": {"uv": [22.5, 52, 20, 48], "texture": 3}, "down": {"uv": [26.5, 48, 24, 52], "texture": 3}}, "type": "cube", "uuid": "8647ee01-1a2b-37a6-bc1e-200fa6035799"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.7, 0, -2], "to": [-1.2000000000000002, 5, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1.0999999046325684, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 58.75, 8, 63.75], "texture": 3}, "east": {"uv": [0, 58.75, 4, 63.75], "texture": 3}, "south": {"uv": [12, 58.75, 14.5, 63.75], "texture": 3}, "west": {"uv": [8, 58.75, 12, 63.75], "texture": 3}, "up": {"uv": [6.5, 52, 4, 48], "texture": 3}, "down": {"uv": [10.5, 48, 8, 52], "texture": 3}}, "type": "cube", "uuid": "895d1224-8551-9817-0a54-a2b520c60cc3"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.3, 0, -2], "to": [3.8, 5, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 43, 8, 48], "texture": 3}, "east": {"uv": [0, 43, 4, 48], "texture": 3}, "south": {"uv": [12, 43, 14.5, 48], "texture": 3}, "west": {"uv": [8, 43, 12, 48], "texture": 3}, "up": {"uv": [6.5, 36, 4, 32], "texture": 3}, "down": {"uv": [10.5, 32, 8, 36], "texture": 3}}, "type": "cube", "uuid": "8fe6346d-9d95-870c-4233-1a2e2263c5a7"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.3, 0, -2], "to": [3.8, 5, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 27, 8, 32], "texture": 3}, "east": {"uv": [0, 27, 4, 32], "texture": 3}, "south": {"uv": [12, 27, 14.5, 32], "texture": 3}, "west": {"uv": [8, 27, 12, 32], "texture": 3}, "up": {"uv": [6.5, 20, 4, 16], "texture": 3}, "down": {"uv": [10.5, 16, 8, 20], "texture": 3}}, "type": "cube", "uuid": "e8ca2076-69b0-9e0a-c45d-3a4e5c4925d1"}, {"name": "Right Shoulder Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 28, -2], "to": [-1.5, 30.299999999999997, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.75, "origin": [-10, 7, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [56, 52, 52, 55], "texture": 3}, "east": {"uv": [60, 52, 56, 55], "texture": 3}, "south": {"uv": [64, 52, 60, 55], "texture": 3}, "west": {"uv": [52, 52, 48, 55], "texture": 3}, "up": {"uv": [52, 52, 56, 48], "texture": 3}, "down": {"uv": [56, 48, 60, 52], "texture": null}}, "type": "cube", "uuid": "f77234d6-5314-6a35-90f9-2ed4e28ca05d"}, {"name": "Right  Shoulder Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 28, -2], "to": [-1.5, 30.3, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [-10, 7, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [40, 52, 36, 55], "texture": 3}, "east": {"uv": [44, 52, 40, 55], "texture": 3}, "south": {"uv": [48, 52, 44, 55], "texture": 3}, "west": {"uv": [36, 52, 32, 55], "texture": 3}, "up": {"uv": [36, 52, 40, 48], "texture": 3}, "down": {"uv": [40, 48, 44, 52], "texture": null}}, "type": "cube", "uuid": "ae319f4f-b1a3-4668-7d91-d8772aed8f5d"}, {"name": "Right  Shoulder Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [19.5, 15, -2], "to": [22.5, 17.3, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [16, -6, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [40, 52, 36, 55], "texture": 3}, "east": {"uv": [44, 52, 40, 55], "texture": 3}, "south": {"uv": [48, 52, 44, 55], "texture": 3}, "west": {"uv": [36, 52, 32, 55], "texture": 3}, "up": {"uv": [36, 52, 40, 48], "texture": 3}, "down": {"uv": [40, 48, 44, 52], "texture": null}}, "type": "cube", "uuid": "0b95a464-18ee-c319-203e-e972ac452d50"}, {"name": "Right Shoulder Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [18.5, 15, -2], "to": [22.5, 17.299999999999997, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.7, "origin": [16, -6, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [56, 52, 52, 54.3], "texture": 3}, "east": {"uv": [60, 52, 56, 54.3], "texture": 3}, "south": {"uv": [64, 52, 60, 54.3], "texture": 3}, "west": {"uv": [52, 52, 48, 54.3], "texture": 3}, "up": {"uv": [52, 52, 56, 48], "texture": 3}, "down": {"uv": [56, 48, 60, 52], "texture": null}}, "type": "cube", "uuid": "99c7a60a-8284-bd1b-d5d5-88b5e6aa1fb1"}, {"name": "Left  Shoulder Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-23.5, 15, -2], "to": [-20.5, 17.3, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [-16, -6, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 55], "texture": 3}, "east": {"uv": [32, 52, 36, 55], "texture": 3}, "south": {"uv": [44, 52, 48, 55], "texture": 3}, "west": {"uv": [40, 52, 44, 55], "texture": 3}, "up": {"uv": [40, 52, 36, 48], "texture": 3}, "down": {"uv": [44, 48, 40, 52], "texture": null}}, "type": "cube", "uuid": "9b95bb67-80a5-58b3-bf3f-404121fc54b3"}, {"name": "Left Shoulder Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-23.5, 15, -2], "to": [-19.5, 17.299999999999997, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.7, "origin": [-16, -6, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 55], "texture": 3}, "east": {"uv": [48, 52, 52, 55], "texture": 3}, "south": {"uv": [60, 52, 64, 55], "texture": 3}, "west": {"uv": [56, 52, 60, 55], "texture": 3}, "up": {"uv": [56, 52, 52, 48], "texture": 3}, "down": {"uv": [60, 48, 56, 52], "texture": null}}, "type": "cube", "uuid": "0600c7ab-d1be-ab05-99b9-6ba0a035bcd6"}, {"name": "Left Shoulder Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.5, 28, -2], "to": [6.5, 30.299999999999997, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.75, "origin": [10, 7, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 55], "texture": 3}, "east": {"uv": [48, 52, 52, 55], "texture": 3}, "south": {"uv": [60, 52, 64, 55], "texture": 3}, "west": {"uv": [56, 52, 60, 55], "texture": 3}, "up": {"uv": [56, 52, 52, 48], "texture": 3}, "down": {"uv": [60, 48, 56, 52], "texture": null}}, "type": "cube", "uuid": "f75d0a46-1412-a041-3e8d-4bf6bd72d732"}, {"name": "Left  Shoulder Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.5, 28, -2], "to": [6.5, 30.3, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "origin": [10, 7, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 55], "texture": 3}, "east": {"uv": [32, 52, 36, 55], "texture": 3}, "south": {"uv": [44, 52, 48, 55], "texture": 3}, "west": {"uv": [40, 52, 44, 55], "texture": 3}, "up": {"uv": [40, 52, 36, 48], "texture": 3}, "down": {"uv": [44, 48, 40, 52], "texture": null}}, "type": "cube", "uuid": "2602aabc-76c0-9883-ff60-05e1cd550191"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-11.16769071970922, 35, -2], "to": [-8.16769071970922, 36, 2], "autouv": 0, "color": 0, "inflate": 0.7, "rotation": [0, 0, -67.5], "origin": [-10.16769071970922, 21, -2], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 41.5, 28, 43.5], "texture": 3}, "east": {"uv": [20, 41.5, 28, 43.5], "texture": 3}, "south": {"uv": [20, 41.5, 28, 43.5], "texture": 3}, "west": {"uv": [20, 41.5, 28, 43.5], "texture": 3}, "up": {"uv": [20, 41.5, 28, 43.5], "texture": 3}, "down": {"uv": [20, 41.5, 28, 43.5], "texture": 3}}, "type": "cube", "uuid": "42c4473a-da7f-141c-3ac7-bf0e0fce5be9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8.16769071970922, 35, -2], "to": [11.16769071970922, 36, 2], "autouv": 0, "color": 0, "inflate": 0.7, "rotation": [0, 0, 67.5], "origin": [10.16769071970922, 21, -2], "uv_offset": [16, 32], "faces": {"north": {"uv": [28, 41.5, 20, 43.5], "texture": 3}, "east": {"uv": [28, 41.5, 20, 43.5], "texture": 3}, "south": {"uv": [28, 41.5, 20, 43.5], "texture": 3}, "west": {"uv": [28, 41.5, 20, 43.5], "texture": 3}, "up": {"uv": [28, 41.5, 20, 43.5], "texture": 3}, "down": {"uv": [28, 41.5, 20, 43.5], "texture": 3}}, "type": "cube", "uuid": "1209a83c-269e-942b-db42-653573c93dec"}], "outliner": [{"name": "armorHead", "origin": [0, 26, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "42c4473a-da7f-141c-3ac7-bf0e0fce5be9", "1209a83c-269e-942b-db42-653573c93dec", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", {"name": "antenna", "origin": [0, 2, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "5c266146-6a46-04a7-14aa-55bcc9ba10a9", "40e10c28-da11-3b03-ec94-845908fb79da", "7d4c2d3a-f82f-3efc-0c5c-796bc2cd87bf", "a7ed8e98-c940-3352-4af0-0d1d8998b751", "ad900b38-00e3-c521-fa92-46eba208e090", "e4e22ed5-1658-f1d6-c4ff-1f1d125406f1", {"name": "Shoulder", "origin": [-10, 7, 0], "rotation": [0, 0, -32.5], "color": 0, "uuid": "25b84d89-4175-60ee-6888-81fb3976268e", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["f77234d6-5314-6a35-90f9-2ed4e28ca05d", "ae319f4f-b1a3-4668-7d91-d8772aed8f5d"]}, {"name": "Shoulder2", "origin": [10, 7, 0], "rotation": [0, 0, 32.5], "color": 0, "uuid": "ec3d3c4e-0d46-a011-d685-4caf917d5f08", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["f75d0a46-1412-a041-3e8d-4bf6bd72d732", "2602aabc-76c0-9883-ff60-05e1cd550191"]}, {"name": "Bustthing", "origin": [7, -7, 0], "rotation": [0, 0, 35], "color": 0, "uuid": "1916ac3e-bc0d-d804-818e-6d3357837006", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0b95a464-18ee-c319-203e-e972ac452d50", "99c7a60a-8284-bd1b-d5d5-88b5e6aa1fb1"]}, {"name": "Bustthing2", "origin": [-7, -7, 0], "rotation": [0, 0, -35], "color": 0, "uuid": "404845ec-4fd7-f5e6-de56-b4c26495e21a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["9b95bb67-80a5-58b3-bf3f-404121fc54b3", "0600c7ab-d1be-ab05-99b9-6ba0a035bcd6"]}]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", "99903b69-5f05-60a1-043b-badb8cc7a3c7", "d5143400-d88e-1197-fb85-a3e184add1c1", "380149a9-9736-084e-d4a4-3219828a8bc1", "bbd67864-be07-a5c0-dba9-f139a68a0a73", "2be5effd-4e4b-7a3f-a205-867beb1933b4", "e6589fe6-b122-dd09-d98f-d104eafeed3c", "e18cf142-f4a6-bd11-2d9a-956ab3197afd", "f0fd6cac-c470-cf06-a480-e7813a2bab80"]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "armNested", "origin": [-6, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["8f886b27-d071-f87c-a62c-68ef077832c9", "70613f45-51d9-b0ca-6ab7-be74effe966c", "d1165a6a-96ce-472c-6df4-493cc0b26a5f", "e837c53f-4823-6242-146a-1aed22e4547e", "e8b655fb-2f66-4f29-4c42-ed3b4921b341", "2571bcb2-7c58-9ed7-e5f9-556bf5568ce8", "587806e2-1c2d-a091-c6c3-68142f1426b9", "9a13037a-b0c6-8a18-fec6-862193b766db", "29da019e-66f7-60c1-bb55-53f72e32f9d5", "e05c41a6-9418-10eb-8998-39fe0afcc265"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806", "e9a21f60-5201-cbd9-0833-b4da34fd0e6d", "6c3be3e9-612d-a7bc-e149-421ec948cc47", "e8ca2076-69b0-9e0a-c45d-3a4e5c4925d1", "8fe6346d-9d95-870c-4233-1a2e2263c5a7"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb", "79228683-25b2-761e-35e7-9a6afc586045", "08de7c4a-6349-f194-959b-275151915b4b", "895d1224-8551-9817-0a54-a2b520c60cc3", "8647ee01-1a2b-37a6-bc1e-200fa6035799"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_amperi.png", "name": "ov_amperi.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "eed27f8a-5140-374b-b922-fe5f9d12d369", "relative_path": "../textures/models/ben/ov_amperi.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\bio_chimera_sui_generis_amperi_not_pants.png", "name": "bio_chimera_sui_generis_amperi_not_pants.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "de458d02-5b8e-135d-090d-de6af8937246", "relative_path": "../textures/models/ben/bio/bio_chimera_sui_generis_amperi_not_pants.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_orishan_amperi.png", "name": "ov_orishan_amperi.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "76ac7db4-e88b-ea6e-373d-7be7cf71faee", "relative_path": "../textures/models/ben/bio/ov_orishan_amperi.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}