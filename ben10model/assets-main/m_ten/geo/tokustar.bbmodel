{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "tokustar", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.5, 26, -4], "to": [2.5, 31, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [-0.5, 1, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 4}, "east": {"uv": [0, 8, 8, 16], "texture": 4}, "south": {"uv": [24, 8, 32, 16], "texture": 4}, "west": {"uv": [16, 8, 24, 16], "texture": 4}, "up": {"uv": [16, 8, 8, 0], "texture": 4}, "down": {"uv": [24, 0, 16, 8], "texture": 4}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.5, 26, -4], "to": [2.5, 31, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [-0.5, 1, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 4}, "east": {"uv": [32, 8, 40, 16], "texture": 4}, "south": {"uv": [56, 8, 64, 16], "texture": 4}, "west": {"uv": [48, 8, 56, 16], "texture": 4}, "up": {"uv": [48, 8, 40, 0], "texture": 4}, "down": {"uv": [56, 0, 48, 8], "texture": 4}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 19, -2], "to": [3.5, 25, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.5, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 26], "texture": 4}, "east": {"uv": [16, 20, 20, 26], "texture": 4}, "south": {"uv": [32, 20, 40, 26], "texture": 4}, "west": {"uv": [28, 20, 32, 26], "texture": 4}, "up": {"uv": [28, 20, 20, 16], "texture": 4}, "down": {"uv": [36, 16, 28, 20], "texture": 4}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 19, -2], "to": [3.5, 25, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.5, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 42], "texture": 4}, "east": {"uv": [16, 36, 20, 42], "texture": 4}, "south": {"uv": [32, 36, 40, 42], "texture": 4}, "west": {"uv": [28, 36, 32, 42], "texture": 4}, "up": {"uv": [28, 36, 20, 32], "texture": 4}, "down": {"uv": [36, 32, 28, 36], "texture": 4}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.5, 18, -2], "to": [6.5, 24, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 26], "texture": 4}, "east": {"uv": [40, 20, 44, 26], "texture": 4}, "south": {"uv": [52, 20, 54, 26], "texture": 4}, "west": {"uv": [48, 20, 52, 26], "texture": 4}, "up": {"uv": [48, 20, 44, 16], "texture": 4}, "down": {"uv": [52, 16, 48, 20], "texture": 4}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.5, 18, -2], "to": [6.5, 24, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 42], "texture": 4}, "east": {"uv": [40, 36, 44, 42], "texture": 4}, "south": {"uv": [52, 36, 54, 42], "texture": 4}, "west": {"uv": [48, 36, 52, 42], "texture": 4}, "up": {"uv": [48, 36, 44, 32], "texture": 4}, "down": {"uv": [52, 32, 48, 36], "texture": 4}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 18, -2], "to": [-4.5, 24, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 58], "texture": 4}, "east": {"uv": [32, 52, 36, 58], "texture": 4}, "south": {"uv": [44, 52, 46, 58], "texture": 4}, "west": {"uv": [40, 52, 44, 58], "texture": 4}, "up": {"uv": [40, 52, 36, 48], "texture": 4}, "down": {"uv": [44, 48, 40, 52], "texture": 4}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 18, -2], "to": [-4.5, 24, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 58], "texture": 4}, "east": {"uv": [48, 52, 52, 58], "texture": 4}, "south": {"uv": [60, 52, 62, 58], "texture": 4}, "west": {"uv": [56, 52, 60, 58], "texture": 4}, "up": {"uv": [56, 52, 52, 48], "texture": 4}, "down": {"uv": [60, 48, 56, 52], "texture": 4}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.3, 0, -2], "to": [3, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 4}, "east": {"uv": [0, 20, 4, 32], "texture": 4}, "south": {"uv": [12, 20, 16, 32], "texture": 4}, "west": {"uv": [8, 20, 12, 32], "texture": 4}, "up": {"uv": [8, 20, 4, 16], "texture": 4}, "down": {"uv": [12, 16, 8, 20], "texture": 4}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.3, 0, -2], "to": [3, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 4}, "east": {"uv": [0, 36, 4, 48], "texture": 4}, "south": {"uv": [12, 36, 16, 48], "texture": 4}, "west": {"uv": [8, 36, 12, 48], "texture": 4}, "up": {"uv": [8, 36, 4, 32], "texture": 4}, "down": {"uv": [12, 32, 8, 36], "texture": 4}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 0, -2], "to": [-0.2999999999999998, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 4}, "east": {"uv": [16, 52, 20, 64], "texture": 4}, "south": {"uv": [28, 52, 32, 64], "texture": 4}, "west": {"uv": [24, 52, 28, 64], "texture": 4}, "up": {"uv": [24, 52, 20, 48], "texture": 4}, "down": {"uv": [28, 48, 24, 52], "texture": 4}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 0, -2], "to": [-0.2999999999999998, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 4}, "east": {"uv": [0, 52, 4, 64], "texture": 4}, "south": {"uv": [12, 52, 16, 64], "texture": 4}, "west": {"uv": [8, 52, 12, 64], "texture": 4}, "up": {"uv": [8, 52, 4, 48], "texture": 4}, "down": {"uv": [12, 48, 8, 52], "texture": 4}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 13, -2], "to": [3, 18, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20.5, 43, 27.5, 48], "texture": 4}, "east": {"uv": [16, 43, 20, 48], "texture": 4}, "south": {"uv": [32, 43, 39, 48], "texture": 4}, "west": {"uv": [28, 43, 32, 48], "texture": 4}, "up": {"uv": [28, 36, 20, 32], "texture": 4}, "down": {"uv": [36, 32, 28, 36], "texture": 4}}, "type": "cube", "uuid": "e1ecb9a7-97f1-00d9-bedb-9c869977b27a"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 13, -2], "to": [3, 18, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20.5, 27, 27.5, 32], "texture": 4}, "east": {"uv": [16, 27, 20, 32], "texture": 4}, "south": {"uv": [32, 27, 39, 32], "texture": 4}, "west": {"uv": [28, 27, 32, 32], "texture": 4}, "up": {"uv": [28, 20, 20, 16], "texture": 4}, "down": {"uv": [36, 16, 28, 20], "texture": 4}}, "type": "cube", "uuid": "f5bda051-63f1-1c63-7afb-2d57b975bae8"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.508712280835866, 14.912741627426989, -0.7500000000000002], "to": [-7.5087122808358675, 19.91274162742699, 0.7499999999999998], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [0, 0, 20], "origin": [-8.008712280835866, 19.912741627426986, -1], "uv_offset": [48, 48], "faces": {"north": {"uv": [57, 56.25, 58.25, 57], "texture": 4}, "east": {"uv": [57, 56.25, 58.25, 57], "texture": 4}, "south": {"uv": [57, 56.25, 58.25, 57], "texture": 4}, "west": {"uv": [57, 56.25, 58.25, 57], "texture": 4}, "up": {"uv": [57, 55.75, 58.25, 56.5], "texture": 4}, "down": {"uv": [57, 56.25, 58.25, 57], "texture": 4}}, "type": "cube", "uuid": "23692dc5-af29-b531-060f-65f068231e49"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 27, -0.25000000000000044], "to": [-5.5, 28, 0.24999999999999956], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [0, 0, 10], "origin": [1, 1, 1.7499999999999996], "uv_offset": [48, 48], "faces": {"north": {"uv": [57, 55.5, 58.25, 56.25], "texture": 4}, "east": {"uv": [57, 55.5, 58.25, 56.25], "texture": 4}, "south": {"uv": [57, 55.5, 58.25, 56.25], "texture": 4}, "west": {"uv": [57, 55.5, 58.25, 56.25], "texture": 4}, "up": {"uv": [57, 55.5, 58.25, 56.25], "texture": 4}, "down": {"uv": [57, 55.5, 58.25, 56.25], "texture": 4}}, "type": "cube", "uuid": "473aefe3-0792-8089-805a-cb29ae7f5a2d"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.508712280835866, 20.91274162742699, -0.7500000000000002], "to": [-7.5087122808358675, 24.91274162742699, 0.7499999999999998], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [0, 0, 15], "origin": [-8.008712280835866, 19.912741627426986, -1], "uv_offset": [48, 48], "faces": {"north": {"uv": [57, 55.75, 58.25, 56.5], "texture": 4}, "east": {"uv": [57, 55.75, 58.25, 56.5], "texture": 4}, "south": {"uv": [57, 55.75, 58.25, 56.5], "texture": 4}, "west": {"uv": [57, 55.75, 58.25, 56.5], "texture": 4}, "up": {"uv": [57, 55.75, 58.25, 56.5], "texture": 4}, "down": {"uv": [57, 55.75, 58.25, 56.5], "texture": 4}}, "type": "cube", "uuid": "af0919b2-503c-9be7-9f78-683d7c0d0d48"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 30, -6], "to": [1, 36.5, -4], "autouv": 0, "color": 0, "origin": [0, 30, -5], "uv_offset": [16, 1], "faces": {"north": {"uv": [11, 6.75, 13, 8.75], "texture": 4}, "east": {"uv": [11, 6.75, 13, 8.75], "texture": 4}, "south": {"uv": [11, 6.75, 13, 8.75], "texture": 4}, "west": {"uv": [11, 6.75, 13, 8.75], "texture": 4}, "up": {"uv": [11, 6.75, 13, 8.75], "texture": 4}, "down": {"uv": [11, 6.75, 13, 8.75], "texture": 4}}, "type": "cube", "uuid": "43e742af-402e-51e8-c3e0-292d06446697"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 36, -6], "to": [0.5, 37.5, -4], "autouv": 0, "color": 0, "origin": [-0.5, 30, -5], "uv_offset": [16, 1], "faces": {"north": {"uv": [11, 6.5, 13, 8.5], "texture": 4}, "east": {"uv": [11, 6.5, 13, 8.5], "texture": 4}, "south": {"uv": [11, 6.5, 13, 8.5], "texture": 4}, "west": {"uv": [11, 6.5, 13, 8.5], "texture": 4}, "up": {"uv": [11, 6.5, 13, 8.5], "texture": 4}, "down": {"uv": [11, 6.5, 13, 8.5], "texture": 4}}, "type": "cube", "uuid": "05b54b69-34df-f3a8-fe9e-6d1acd793ef2"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [7.5087122808358675, 14.912741627426989, -0.7500000000000002], "to": [8.508712280835866, 19.91274162742699, 0.7499999999999998], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [0, 0, -20], "origin": [8.008712280835866, 19.912741627426986, -1], "uv_offset": [48, 48], "faces": {"north": {"uv": [58.25, 56.25, 57, 57], "texture": 4}, "east": {"uv": [58.25, 56.25, 57, 57], "texture": 4}, "south": {"uv": [58.25, 56.25, 57, 57], "texture": 4}, "west": {"uv": [58.25, 56.25, 57, 57], "texture": 4}, "up": {"uv": [58.25, 55.75, 57, 56.5], "texture": 4}, "down": {"uv": [58.25, 56.25, 57, 57], "texture": 4}}, "type": "cube", "uuid": "90c4801a-05fb-b834-ff6a-ba2972bcee30"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [7.5087122808358675, 20.91274162742699, -0.7500000000000002], "to": [8.508712280835866, 24.91274162742699, 0.7499999999999998], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [0, 0, -15], "origin": [8.008712280835866, 19.912741627426986, -1], "uv_offset": [48, 48], "faces": {"north": {"uv": [58.25, 55.75, 57, 56.5], "texture": 4}, "east": {"uv": [58.25, 55.75, 57, 56.5], "texture": 4}, "south": {"uv": [58.25, 55.75, 57, 56.5], "texture": 4}, "west": {"uv": [58.25, 55.75, 57, 56.5], "texture": 4}, "up": {"uv": [58.25, 55.75, 57, 56.5], "texture": 4}, "down": {"uv": [58.25, 55.75, 57, 56.5], "texture": 4}}, "type": "cube", "uuid": "1122233b-243f-82c8-9af6-a570560c58e6"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.5, 27, -0.25000000000000044], "to": [5.5, 28, 0.24999999999999956], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [0, 0, -10], "origin": [-1, 1, 1.7499999999999996], "uv_offset": [48, 48], "faces": {"north": {"uv": [58.25, 55.5, 57, 56.25], "texture": 4}, "east": {"uv": [58.25, 55.5, 57, 56.25], "texture": 4}, "south": {"uv": [58.25, 55.5, 57, 56.25], "texture": 4}, "west": {"uv": [58.25, 55.5, 57, 56.25], "texture": 4}, "up": {"uv": [58.25, 55.5, 57, 56.25], "texture": 4}, "down": {"uv": [58.25, 55.5, 57, 56.25], "texture": 4}}, "type": "cube", "uuid": "ff5b1350-b556-ab66-08b3-6a068258f6f2"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.8, 12, -2], "to": [-4.5, 17, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 59, 40, 64], "texture": 4}, "east": {"uv": [32, 59, 36, 64], "texture": 4}, "south": {"uv": [44, 59, 46, 64], "texture": 4}, "west": {"uv": [40, 59, 44, 64], "texture": 4}, "up": {"uv": [40, 52, 36, 48], "texture": 4}, "down": {"uv": [44, 48, 40, 52], "texture": 4}}, "type": "cube", "uuid": "230334c2-e19d-6b84-febc-96ca939bbeb4"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.8, 12, -2], "to": [-4.5, 17, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 59, 56, 64], "texture": 4}, "east": {"uv": [48, 59, 52, 64], "texture": 4}, "south": {"uv": [60, 59, 62, 64], "texture": 4}, "west": {"uv": [56, 59, 60, 64], "texture": 4}, "up": {"uv": [56, 52, 52, 48], "texture": 4}, "down": {"uv": [60, 48, 56, 52], "texture": 4}}, "type": "cube", "uuid": "ba308234-3950-56d1-de07-046251896d24"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.8, 12, -2], "to": [7.1, 17, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 43, 48, 48], "texture": 4}, "east": {"uv": [40, 43, 44, 48], "texture": 4}, "south": {"uv": [52, 43, 54, 48], "texture": 4}, "west": {"uv": [48, 43, 52, 48], "texture": 4}, "up": {"uv": [48, 36, 44, 32], "texture": 4}, "down": {"uv": [52, 32, 48, 36], "texture": 4}}, "type": "cube", "uuid": "4d833d7a-0dae-9811-c850-b4099ea98b30"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.8, 12, -2], "to": [7.1, 17, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 27, 48, 32], "texture": 4}, "east": {"uv": [40, 27, 44, 32], "texture": 4}, "south": {"uv": [52, 27, 54, 32], "texture": 4}, "west": {"uv": [48, 27, 52, 32], "texture": 4}, "up": {"uv": [48, 20, 44, 16], "texture": 4}, "down": {"uv": [52, 16, 48, 20], "texture": 4}}, "type": "cube", "uuid": "77db7dbc-31ae-c13e-fd71-c4c38e116928"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 30.200000000000003, -3], "to": [3, 34.2, 5], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [10, 0, 0], "origin": [0, 33.2, 1], "uv_offset": [32, 0], "faces": {"north": {"uv": [0, 0, 8, 8], "texture": 4}, "east": {"uv": [0, 0, 8, 8], "texture": null}, "south": {"uv": [0, 0, 8, 8], "texture": null}, "west": {"uv": [0, 0, 8, 8], "texture": null}, "up": {"uv": [0, 0, 8, 8], "texture": null}, "down": {"uv": [0, 0, 8, 8], "texture": null}}, "type": "cube", "uuid": "3a6b3bb6-f3be-c61f-9363-97afe1f8aa4b"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 30.200000000000003, -4], "to": [2.5, 34.2, 4], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, 0, 10], "origin": [0, 33.2, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [0, 0, 8, 8], "texture": null}, "east": {"uv": [0, 0, 8, 8], "texture": 4}, "south": {"uv": [0, 0, 8, 8], "texture": null}, "west": {"uv": [0, 0, 8, 8], "texture": null}, "up": {"uv": [0, 0, 8, 8], "texture": null}, "down": {"uv": [0, 0, 8, 8], "texture": null}}, "type": "cube", "uuid": "41edcb55-7aab-9af0-5129-8d6881e578dd"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 30.200000000000003, -5], "to": [3, 34.2, 3], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-10, 0, 0], "origin": [0, 33.2, -1], "uv_offset": [32, 0], "faces": {"north": {"uv": [0, 0, 8, 8], "texture": null}, "east": {"uv": [0, 0, 8, 8], "texture": null}, "south": {"uv": [8, 0, 0, 8], "texture": 4}, "west": {"uv": [0, 0, 8, 8], "texture": null}, "up": {"uv": [0, 0, 8, 8], "texture": null}, "down": {"uv": [0, 0, 8, 8], "texture": null}}, "type": "cube", "uuid": "7be01842-a7e8-eb93-1b06-3245580620b2"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.5, 30.200000000000003, -4], "to": [3.5, 34.2, 4], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, 0, -10], "origin": [1, 33.2, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [0, 0, 8, 8], "texture": null}, "east": {"uv": [0, 0, 8, 8], "texture": null}, "south": {"uv": [0, 0, 8, 8], "texture": null}, "west": {"uv": [0, 0, 8, 8], "texture": 4}, "up": {"uv": [0, 0, 8, 8], "texture": null}, "down": {"uv": [0, 0, 8, 8], "texture": null}}, "type": "cube", "uuid": "66ba6998-9047-338b-12df-7e1d14088e13"}], "outliner": [{"name": "armorHead", "origin": [0, 26, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", {"name": "antenna", "origin": [0, 2, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, "43e742af-402e-51e8-c3e0-292d06446697", {"name": "fire", "origin": [1, 34.2, 0], "color": 0, "uuid": "06fa30b9-7e47-7cbf-b584-86dd4bcb81b8", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3a6b3bb6-f3be-c61f-9363-97afe1f8aa4b", "41edcb55-7aab-9af0-5129-8d6881e578dd", "7be01842-a7e8-eb93-1b06-3245580620b2", "66ba6998-9047-338b-12df-7e1d14088e13"]}, "05b54b69-34df-f3a8-fe9e-6d1acd793ef2"]}, {"name": "armorBody", "origin": [-0.5, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "f5bda051-63f1-1c63-7afb-2d57b975bae8", "e1ecb9a7-97f1-00d9-bedb-9c869977b27a"]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", "77db7dbc-31ae-c13e-fd71-c4c38e116928", "4d833d7a-0dae-9811-c850-b4099ea98b30", {"name": "bone2", "origin": [0, 0, 0], "color": 0, "uuid": "8f7d1681-7da8-8560-dd40-8d12f70e9840", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["90c4801a-05fb-b834-ff6a-ba2972bcee30", "1122233b-243f-82c8-9af6-a570560c58e6", "ff5b1350-b556-ab66-08b3-6a068258f6f2"]}, {"name": "wingRight", "origin": [6, 22, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 22, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-6, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134", "ba308234-3950-56d1-de07-046251896d24", "230334c2-e19d-6b84-febc-96ca939bbeb4", {"name": "bone", "origin": [0, 0, 0], "color": 0, "uuid": "7e482e9d-582c-f6e3-cace-13faaf842241", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["23692dc5-af29-b531-060f-65f068231e49", "af0919b2-503c-9be7-9f78-683d7c0d0d48", "473aefe3-0792-8089-805a-cb29ae7f5a2d"]}]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_tokustar.png", "name": "ov_tokustar.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "680616b4-bd62-d725-f2fe-34d36dc33fc2", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/ov_tokustar.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\tokustar.png", "name": "tokustar.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "01ebc945-6b65-9a8d-a4a2-3e7abd00de0d", "relative_path": "../textures/models/ben/tokustar.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\recalibrated\\r_tokustar.png", "name": "r_tokustar.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "ea7c94e4-c09d-1a60-0f56-b282ad7e378d", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/recalibrated/r_tokustar.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_pyronite_tokustar.png", "name": "ov_pyronite_tokustar.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "1ac4e2a8-5cde-5160-d1c9-4db36d94588b", "relative_path": "../textures/models/ben/ov_pyronite_tokustar.png", "source": "data:image/png;base64,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********************************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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bigchuck.png", "name": "bigchuck.png", "folder": "", "namespace": "", "id": "5", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "02b05514-272b-794f-ee94-39983115f57d", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAGJJJREFUeF7tnV2oZtdZx/ekZM7pzOSkcY5t7EyRxpSOSIYIEXVImVISKWJ6IZ1iqBIjAdvLghcKQgwIeiEWetMKwRi0RDrFi0akmFAYWgbBQNMp4pSmKdJJndYzhsxXz5nYjJz9nnVy9pp37Wc962Ovtff+vTfnvO9en//nWb/1rLW/9jWFP+tfbW4WbkLR6jc+2uwr2oDKKz9xZuEfq2uLhn7tl/v1+sg3F+k3Ly/Snz2Jvn0mLu58AAAH7XNQAJCX0AAgr75i6UQA/RIZAJhU0oyuTS8aaOIJAEBhAwMAAFDSBQFASfWbpgEAhQ0w8+oBQGEHAACFDTDz6gFAYQcAAIUNMPPqAUBhBwAAhQ0w8+pnD4Bv3fdaURc4cuRIcRsUFYDKiypQ3PlKXwcAAIr6H5UXVmD2ADh//+KSsa2fXhnUFCvvuKOtb/3uteI2GLTjVFaVAsWdr3QEAACq8kcaM7ACAIAIYGCXo7qaFAAAAKAmf6QtAysAAHYAYHTPvRdg1v6mPvYABvZ4qusoAAAAAENixgoUB8DGxctVPQ+ACGDGo2GGXQcAltEBwAxHwYy7DAAKG589gMIGcFSfOjKt1c4AoLD/1eoYhWUpXj0AGMgEqYUeqNnJqgEAyaRMWlBqv6zVzkQASd1GX1itjqHvybRyAICB7Jla6IGanawaAJBMyiQF5fbH2uxNBJDEbcILqc0hwnsyjZwAYGA75hZ84O6oqwMAasmyZsjtj7XZmwggqzvJhdfmEHKLp50CAAxs39yCD9wddXUAQC1Z1gy5/bE2exMBZHUnufDaHEJu8bRTAICB7Ztb8IG7o64OAKgly5ohtz/WZm8igKzuJBdem0PILZ52CgAwsH1zCz5wd9TVAQC1ZFkz5PbH2uxNBJDVneTCa3MIucXTTjE7AOTusO0uhw4tnoZb6+fq1WGfDizpYN+ebD9RyM6vTQ+AJAuUPW7ednzgwIG2IS/+yvXeSfuhfz/QPl/j+vXrbXrpbcr7AEDXwACgrMNTe1eByQCg9pnfdrxaQKCd0bXpiQDqRo4BgGmlNKNr0w8WAQCAMEfTDmhtegAQZpehcmkHtDZ9NgCMbcBLBq0lIpDaqT0OALSKTSs9APC0JwDwFIpko1IAAHiaCwB4CkWyUSkAADzNBQA8hSLZqBRIDgB7E2p17f9aQW5eu2upMEd+/nCvYG9cuhEl6J2H9/fmf+2/LvUe126qRTW2QGb2AAqIXlGVAAAAFL8atKLxMLumJAOAmSnNjG8rKUUAT/7rA0vFf+o3Xmp/10YCZuaXypUiALtRpp/SFXlj8SQigLFYKk87AYAQAQCAPI5HqXUoEAwAeyZ8/weOdHq0urra+f6f3/5e7x6Aa6Y2mUwkIM3YZk8htrxfvO8XOu3d3NzsfP/+d19rv+eOCLR7ENr0RAB1DERXK6q9FBgAAIC6h840WlcdAFwvz3StiU1kYCICOxKwzwLErtmlCMBEEsY97IjCzPxmxjczvWspYP+eem9AO6Nr0xMB1A0K7aW92vTqJQAAWDiMVodQN9MOaG16ABBqmWHyaQe0Nr03AIxjHTt2bOla37XGt2UyM6y0J+CS13cPQDKPa+bX9sPeGzh//nxbdepIQOpP6PFaAGBuS/dtj30buzafb/pQXceSDwDsWEoCkwtkACCNqwOANDpqS9lnhwzaAkz6b//W4kk/9u7/B778P71FfuWDl0OrTJLvY99Z6y3nux//2c5xM+Dv++e6nhwUKoZ0f3loua58vg+gcc3QUv7QfKa9c4sMAAAAGPRKQGkASwNRyg8AdMhOBgC7WjsikCIBO3/qyECa6e36zcw/tRnf7metEYDOjdOlJgJIpCUASCRk5mIAQFdgAGA5nBnI5mezxtfO6GZG1eaLjQRCZ35tO+2IwehV+14BAAAA7WOEXR8A4DcFAwA/naQ1vF8p+VIRAXhqG7rrb4q/43i3oivn+ivWRgLSzK+t37RuamcFSkUAn1zpnn354tawZ4Nc9dcGgOyXAoeeBgQACySMfZMQACzsaAAEADwjAGnJ4Nor+KNrZ9qs//ilv2//vvHZpztFuSKBVBGAPfPf+Zkn2vp/5xO/1/79q4MnO+2Z+tmAoQHwT/9ypl1yuuxvBuLN2xZPgrr0vxtRHnn4Z9bb/PveWjwZyp75bfv/9m+eHPS0qNQ57aW92vTJTwNKu/8AQDL5sMcBQHcCAACJ/M9eK//Ns19uSz5/8I87Nbz8zC95RQKxEYBr5r//8f/otOfYtb9sv//hYx/v/K49K5BIxuzFDAUAM/P72v+pb/1J2/cLF/67o8GHH/rcUk1eOf8XnfRHj/7cIoJ4fav9+7kP/3Unn5n5XfavDQS5HCF5BGAaCgBymSxtuQBg+QQAANL6WXP0mUWoZRO3tgjAtOfC4929icRyVFPcUAD4xO8/0a79fe0felbARAJm5v/VX/+zVmvX2t/Vni/93dNV7QXkcphsEYDdYACQy4Rx5QKAbgRgJoDZACDOfZqm9IUdpU/bhJ5GjdXd5NfujaSq15RTWv/Nqzc7F7Id/UZ34r7wYO91bk3sC19K9z/WntFhDgBo+j0s1kJCfgAAAGJcDADEqNc0DRHAWrQPxZiACCBGvaaJNh4RABFAnAvG5QYAcfoBgDj9iADuJgKIdKGi2QFApPwsAQBApAsVzQ4AIuUHAAAg0oWKZgcAkfIDAAAQ6UJFswOASPkBwLgB8K6Di7sEzefCjy51vh99z+He41wHcPFy0fPgpQ0AAABA5BxSNDsRQKT8AAAARLpQ0ewAIFJ+AAAAIl2oaHYAECk/AAAAkS5UNDsAiJQfAACASBcqmh0ARMoPAABApAsVzQ4AIuUHAAAg0oWKZvd+PbhvKw8dWrwl2Hxi77f2rVdKZ9qlbY/dn0tv/LBT1e9eP9L5fvn7i68rK1KLdMe3Fo+2a9be3833Dwde6/xw+M73Vqm/q7e2vjpV5NTS8wG0/mDXGNv+1UP7oidhWQV3CgAgqAcAYtxLzhs7gKQaAEC/QskAIBlSIq2U3+6GVJ5JHxuRSAA49Uo3AljZmYD3Lx5Hv/uR3nxk989+ivGNncfjb3UDkOb0vUQAfS4OAABAVEgMAKQ5Nu64Fvza2gBAJQCQZnCtIwwVAdiRhL0HYI6vvGOx92HeSRgaCUgzv3kE2NZPryy1rNkD8NVHO6BSp9faPbb+3Lpo+zOZPQBtx21DxOZ3OUbsEgAAxA65/vxau8e2BgB0FRxsD2DoCMDlWKEOYMqzIwAz89v9c0UC0l6AiQDsNb/r4Z92JEAE0I+IUPv7gkcLNCKAHWW1wkmGBADLlwi+jjxUOq3dY9sl+U1s+dr+AIBEAPAVPtYB7EjAFQEYR7LfSGN+tyMBe+1v0klvyDERwNhmfntpZb5Lm3baARprb219dnrJLwEAAGgVAAALRwAAscjR5Z/cHoBE3NgZwXdT0TXz68zzdmpXJODbntB6c+ez2w8Acis+8U1AADCOtT9LgIUCLAESLQFc3Ew9Q0rl+c78f/rDf+s0+c/f+2te6LcjAak9XoV6JJLAahfhG2kRAUzkXoBYB4nNPxYA2APfbrcEAgDgQas9SXxBpCvVP7Xk10QAiSIASWhjsliHkGbcvghg7+D/+sZnOl70ofXP7n7vg8DQAPDV1TUkJL2nHgG4ljrmdwAwMwB85P4PNU+9+MDS8fLkQy81X3v56w0AeFse6fXe0lwsAUjKn+q4C6QAYCYA2J79twe/+dgQ2B785tMHASIA3ZCsBQCuSAAAzBQA2902ENg7+Ld/BwDTiwAAgAXu1DcD+a5VY2eE0D2AU2eebT596omOCi4AfP70083pk48tneqIAIgAdAr0p57MhUBjA0DfEgAAEAGkHOR9ZQEApdI2aOy7A//gQPcJQXuLN1FA3yZg3+DfLutvrw/zBCBfoPrK54q8pLMAGx/t1rB5tf9NdKuH+h+xt3Hxsm+Ts6Sz+8sewMj3ALQAMF7147u+0HGwd7/+qd3vrvAfADQNAEjLJSKAQD3tuwLtYsyzAu2bfLajgL6PPfjNXYP2s/9MGbnuAiQCCHQMIRsRwI5AY90EtHdzXY8IcwHA5LdB4Jr1AQBLgDwoWpRKBBCorhQB7A70nacGu273dVUvDXwiAD/DxZ718avFPxURwEwiAADQHRRDbQLaQxEA9MOJCMAf3p2UvhGAFgS+Mz8RgJ/hAAAA6CiQyiEAgN8ANKmIABZKsASY2RLAHiZ91wlsp7XP80vDbOpnAez+SzcHcR2A5DHd4ywBdHrtptZGACYjAOgKLl0IBAACHdQzGwDwFMpOBgB0woUuAaYGANMf4z9cCbijiPbCk9DrCErtARABLH9W4dwiAAAwkT0AY0jXO/pc82PqJYD0XgLdPH3rJpU2v5ReArf9VGBtBGCnTwV8qV/a40QAV7szwtgiAACgdflFegCw0AEAzBQArqWAdvfflEME0A8iIoB+fdgEDJvIdnNplwAAoD/yYwkQ6ZDK7ABAKZidHADoBBx6CWBaV2sksH73Wv8DDHTyqlMDALVk3QwAQCcgAOjqBQB29JjLJiBLgGGXAEQA7AF0FEgdChIBEAHoFCAC8DodJIkqhZKu/KkBYOrRgsC+HkB7FiD17r/ph28k5ntln20HSf9jL6/1mt6+F0B6m7B9/Pz9ZZ8JaHeOJcDIlwAAoOvSEkAAABHAJCOAUBBIkY59PNfM7xsBSANYKkfKTwSg9Yi49JwFiNPvltzapYC2egDQfSw4SwCtB3XTA4A4/W7JLYXAsdVJM2hs+a72h9YrvUlJaq+kJwCQFOw/nhwAj5xbaWt8/vhWp2b799BNPFNoaP5QR/aVWXJY33Jc6Uq1P7ReANBv8clsAppuPvrqevvvc/dsdHru+j12QNSWHwB0LQIAAECrAABIg6rQmdi3dpYAvkqlSVc8AkjTDUpBgXEq8PDZ/UtfNvjCiRtFr9EfSs1ZdHIoMalnfAoAgPHZjBajQDIFTpxplkYAZ082s5gcZ9HJZN5CQZNTAABMzqR0CAVQwFcBIgBfpUiHAhNUoDgANi5eXroG89W69GkU33aSrk4F2AQsbBcAUNgAM68eABR2AABQ2AAzr55NwMIOAAAKG2Dm1QOAwg4AAAobgOpnrQCbgLM2P52fuwIAYO4eMPP+azcBzZLh4O37W+XGfs8AAJj5AJh79wFAYQ9gD6CwAWZevXYTkAggscMAgMSCUpxKAQCgkqu+xDZA7AdarB7aV3yZU59qtAgFFgqMfnAAAFwZBcIVGD0ANq/eXHovgXl0FvcKhDsHOaevAACYvo3pIQo4FZgcAOyHZhIB4P0o4FYAAOAdKDBjBSYDAGb+GXsxXQ9WAAAES0dGFBi/AqMHgDkNyFp//M5ID4ZXAAAMrzk1okA1CoweAPZ1AFz5V41v0ZARKAAARmAkmogCuRQYPQDMHoDrpZZEBLlch3KnoAAAmIIV6QMKBCowegAE9ptsKIACU7gbECuiAAqEK0AEEK4dOVFg9AoAgNGbkA6gQLgCACBcO3KiwOgVAACjNyEdQIFwBQBAuHadnOZ6hE+urDlLPHXm2ebTp55onnrxgaVpnnzopebzp59uTp98zFnGF7cut8e49yGR4WZeDABI5AC+ADDV/fiuL3Rqfvfrn9r9DgASGYViRAUAgCjR8gTS48w/9p1FJHDH8W7+7Sig72MP/ivnFqm/8sHFzO/6EBEEGnLm2QBAoAOEAsBUZ4PANesDgEADkc1LAQDgJdPbiaSBbxfnigSkan0Hvl0OkYCkLMf3KgAAlP4AAJSCkbxqBQCA0jxaAJjifSOB0Jnf1EMEoDTozJMDAKUDAAClYCSvWgEAoDRPKABMNX3XCWynMef5lc3aTU4EEKrcPPMBAKXdAYBSMJJXrQAAUJoHACgFI3nVCgAApXkAgFIwkletAABQmgcAKAUjedUKAACleQCAUjCSV60AAFCaJxYArrMBsbv/plzOAigNOvPkAEDpAABAKRjJq1YAACjNAwCUgpG8agUAgNI8AEApGMmrViAZAMw7+sybeFzv7LPTVa3OksYBgLFZjPb2KQAAlP4BAJSCkbxqBUQA+Dq8eTefbwRw9eqVjjBjfbefrz62F9j3BISeBSi1629HeKZ/tb+L8eGz+29ut/XamzfaJp892YhjoOoRHNk4sfO+Dg4AdJYAADq9UqUGAF0lnQDwHfimuFwAqH1m0eqUypFNOUNHAK6Z3+5XrZEAAAAASccgAFguJwBI6mbZCksWAdgt3I4IzDp/7/+unrj2AGqPAEz7DAikfsRa0mha68xv+ufbzhNnmnZNfvD2/W3WF07caH1S+7uvrkQAmSIAU+yjr663/z5/fKsDgEfOrbS/P3fPxlJbSQOn1hkFACwfegDAF0ll0yWLADZv+0nbk8dfed/uQDe/rb71zsaA4Zl7f9Ae3/5N8xl6xtO0bW9a3zVyaPmlQOgb4dhndyS7aWd6V3pfPYkAMkUAAGAhLADont4FAL5oKpNOPA1YplnjqXVum4C2ZSTgHf1G18UuPNgu+Xc/dsSgtbwEGG15taY3kY/dvtjrGABApMUBwM3uiLb0BACRDraTHQCk0TF5KQAAACR3qiUFmr0L+5A5axLaBiKAUOV28gEAABDpQl7ZAYCXTMMnAgAAYHivS1cjEUCklgAAAES6UNHsxQEQO4BK7wLHtj/W+qX7z1mAWAv65Z/sJmDsACo9AGLb72d+d6rS/QcAsRb0yw8AHDqVHgAAIO0S4Oh7DncsfeFHlzrf7eOb+94sHsX6DeG4VJPdBIwdQABgregASB0BAIDloAAARABLFSgNQAAQN7OXzl109tjuPBFAnAsAgHksAeK8xJ0bAEQqGwuwyOobADBuAPje3cgmIEsAlgBN00xtExAAXLzcezOJNEOWngGJADgLIPlo33FfALAJSARABDDjCGCyAIihpyavPVObR5DF3o8utcF+1NlQ9eWux/Tb7p90+6+k19DHSz1haeh+uuorvgk4lBAAII/SACCPrkOVOhsAGEFda3bpoaS2QaQZVipPyh/qAKUiDtNeIoBQy5XJBwB2dJcGLABY7qBEAGUGbqpaZwcA+8q1ve8u0Ijq+25DV5lDRQBacGk0WJZWC9LY+rT5tU8t1pY/tvQAYOclpVrHBQB+EUFtAwIAdC0CAABA0jGqBWnSyj0KAwAAoHPhEUsAj1GjSAIAFGJVkHQ2EYBr7W9soHXcsS0Bcu05SD6s1dUuL/asgtTv0leSSvrlPg4AEp0F0Dq65Jihhne1I1d9Uju1ugAASdG0x2cLACPjXJYAAGD5wCECSAuUaktzPbgCAOQ1GRFAXn1jS59NBOASyvett3b+se0BSO2PdSRXfgCQS9k05QKAnduRtY4KAPwcUKsrewB+uqZKBQAAQCpfWlpOKQD47nmwB5DV/PUXzhIgr40AQF59Y0snAiACiPWh3vxDA8B35jeNJgLIav76CycCyGsjAJBX39jSiQCIAGJ9iAggq4J5CwcAACCrhxEBZJU3unAAAACinWhvAet3r/WWt3m1/yHQq4e6Lrn+1W5xFx7sz88egM6cAAAA6DxGSA0AksqZvTAAAACSOhkASCpn9sIAAABI6mQAIKmc2QsDAAAgq5NpNwHZA8hqjlsKBwAAIKvHAYCs8kYXDgAAQLQT9RUAALLKG104AAAA0U4EALJKmLVwAAAAkjpY7Cag/QxAu3FcB5DUXA0AAABJPQoAJJUze2EAAAAkdbJSANBeAWg6zd2ASc0/vsK4GzCtzQBAWj1zl0YEQASQ1ce0ZwFC9wCIAMLMCAAAQJjneOYCAJ5CFUoGAABAVtcDAFnljS4cAACAaCdKeR0AS4Cs5rilcAAAALJ4nHbmN43QAiB07c9ZgIUCAAAAAIAsCoyj0GQAMK/eWj20ry3TfhWX/bv5XlomTgPmsQARQB5dU5cKAIgAUvtUWx4AyCJr8kJFAEgv1TQtMgb3jQB81265r9QiAkjrU8de7j4T0L52317jS8ft1p2//3LSBsf618Nn97cPKbz25o22XWdPjmtZDQCIAJIOKAAwEQC4Zn7jLa6XY6aOAHLv1hIBJB3/DQAAAF6bgL5LAACgG6C+a2+t/r6tAAAzBYDtINuOaJxs7/++jmSni12rueolAgi1yPJ8sQCwS80FqlQTS+wewIkzTbuHcPD2/W2TXjhxo51Atb+HWtG5B6BdApgGPPrqevvv88e3OgB45NxK+/tz92wEtXUsADD93O7/3o/9ey7HHnsEAAAqAYCZGX1H6+ZtP2mTPv7K+3YHuvlt9a13NgYMz9z7g/b49m+aT24AaNrSl9b00wad6/dU9dZSDhGAbgmgneld6UPt74wAAECYpAAg7jQgEcCwEcD/A2cL0S1obgooAAAAAElFTkSuQmCC"}, {"name": "pasted", "offset": [224, 208], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 16, "height": 24, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAYCAYAAADzoH0MAAAAAXNSR0IArs4c6QAAAIBJREFUOE9jZKAQMFKon2HUAAbcYWB1kOE/cgAfs8euFmcgDmED0J0OC4e3saJg5s1Hr1G8jREGNDcA5iKYS0h2AU4DcDkdPbOhhwXcBcPAgB07dqGkfUIFTX5aNFgJPAyoZgDMZFwumDhrKYoUhgvINkBdTpSkMIA5A+4Ccg0AAKs2U8+LaSUGAAAAAElFTkSuQmCC"}, {"name": "pasted", "offset": [44, 26], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 8, "height": 9, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAJCAYAAAAPU20uAAAAAXNSR0IArs4c6QAAAB1JREFUKFNjtDrI8J+BgYHh7xN2EIUBGEcVgMMEALfJIvwRk1l7AAAAAElFTkSuQmCC"}], "relative_path": "../textures/models/ben/bigchuck.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": true, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}