{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": false}, "name": "lenopan", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 25, -6], "to": [3, 31, 3], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [-1, 1, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 1}, "east": {"uv": [0, 8, 8, 16], "texture": 1}, "south": {"uv": [24, 8, 32, 16], "texture": 1}, "west": {"uv": [16, 8, 24, 16], "texture": 1}, "up": {"uv": [16, 8, 8, 0], "texture": 1}, "down": {"uv": [24, 0, 16, 8], "texture": 1}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 25, -6], "to": [3, 31, 3], "autouv": 0, "color": 0, "inflate": 1, "origin": [-1, 1, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 1}, "east": {"uv": [32, 8, 40, 16], "texture": 1}, "south": {"uv": [56, 8, 64, 16], "texture": 1}, "west": {"uv": [48, 8, 56, 16], "texture": 1}, "up": {"uv": [48, 8, 40, 0], "texture": 1}, "down": {"uv": [56, 0, 48, 8], "texture": 1}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 16, -3], "to": [5.5, 24, 3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1.5, 1, -1], "uv_offset": [16, 16], "faces": {"north": {"uv": [18.5, 20, 29.5, 28], "texture": 1}, "east": {"uv": [16, 20, 22, 28], "texture": 1}, "south": {"uv": [31, 20, 42, 28], "texture": 1}, "west": {"uv": [28, 20, 34, 28], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 16, -3], "to": [5.5, 24, 3], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1.5, 1, -1], "uv_offset": [16, 32], "faces": {"north": {"uv": [18.5, 36, 29.5, 44], "texture": 1}, "east": {"uv": [16, 36, 22, 44], "texture": 1}, "south": {"uv": [31, 36, 42, 44], "texture": 1}, "west": {"uv": [28, 36, 34, 44], "texture": 1}, "up": {"uv": [28, 36, 20, 32], "texture": 1}, "down": {"uv": [36, 32, 28, 36], "texture": 1}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 7, -1.5], "to": [10, 24, 1.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 0, 0.5], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 1}, "east": {"uv": [40, 20, 44, 32], "texture": 1}, "south": {"uv": [52, 20, 56, 32], "texture": 1}, "west": {"uv": [48, 20, 52, 32], "texture": 1}, "up": {"uv": [48, 20, 44, 16], "texture": 1}, "down": {"uv": [52, 16, 48, 20], "texture": 1}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 7, -1.5], "to": [10, 24, 1.5], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 0, 0.5], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 1}, "east": {"uv": [40, 36, 44, 48], "texture": 1}, "south": {"uv": [52, 36, 56, 48], "texture": 1}, "west": {"uv": [48, 36, 52, 48], "texture": 1}, "up": {"uv": [48, 36, 44, 32], "texture": 1}, "down": {"uv": [52, 32, 48, 36], "texture": 1}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10, 7, -1.5], "to": [-6, 24, 1.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1, 0, 0.5], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 1}, "east": {"uv": [32, 52, 36, 64], "texture": 1}, "south": {"uv": [44, 52, 48, 64], "texture": 1}, "west": {"uv": [40, 52, 44, 64], "texture": 1}, "up": {"uv": [40, 52, 36, 48], "texture": 1}, "down": {"uv": [44, 48, 40, 52], "texture": 1}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10, 7, -1.5], "to": [-6, 24, 1.5], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1, 0, 0.5], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 1}, "east": {"uv": [48, 52, 52, 64], "texture": 1}, "south": {"uv": [60, 52, 64, 64], "texture": 1}, "west": {"uv": [56, 52, 60, 64], "texture": 1}, "up": {"uv": [56, 52, 52, 48], "texture": 1}, "down": {"uv": [60, 48, 56, 52], "texture": 1}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 12, -2.5], "to": [4, 15, 2.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, -0.5], "uv_offset": [16, 16], "faces": {"north": {"uv": [19.75, 28, 27.75, 31], "texture": 1}, "east": {"uv": [15.75, 28, 20.75, 31], "texture": 1}, "south": {"uv": [31.75, 28, 39.75, 31], "texture": 1}, "west": {"uv": [27.75, 28, 32.75, 31], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "386141bc-47b9-bea0-c6e2-25422c84001c"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 12, -2.5], "to": [4, 15, 2.5], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 1, -0.5], "uv_offset": [16, 32], "faces": {"north": {"uv": [19.75, 44, 27.75, 47], "texture": 1}, "east": {"uv": [15.75, 44, 20.75, 47], "texture": 1}, "south": {"uv": [31.75, 44, 39.75, 47], "texture": 1}, "west": {"uv": [27.75, 44, 32.75, 47], "texture": 1}, "up": {"uv": [28, 36, 20, 32], "texture": 1}, "down": {"uv": [36, 32, 28, 36], "texture": 1}}, "type": "cube", "uuid": "e6e694b2-e5b6-a68a-2813-b4030f5ebae0"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.75, 3, -2.5], "to": [3.75, 11, 2.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.75, 1, -0.5], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 24.5, 27.5, 32], "texture": 1}, "east": {"uv": [16, 24.5, 21, 32], "texture": 1}, "south": {"uv": [32, 24.5, 39.5, 32], "texture": 1}, "west": {"uv": [28, 24.5, 33, 32], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "98129b1d-efe7-fed8-dc5c-1e4a9f9c4dcc"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.75, 2, -2.5], "to": [3.75, 11, 2.5], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.75, 1, -0.5], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 40.5, 27.5, 49], "texture": 1}, "east": {"uv": [16, 40.5, 21, 49], "texture": 1}, "south": {"uv": [32, 40.5, 39.5, 49], "texture": 1}, "west": {"uv": [28, 40.5, 33, 49], "texture": 1}, "up": {"uv": [28, 36, 20, 32], "texture": 1}, "down": {"uv": [36, 32, 28, 36], "texture": 1}}, "type": "cube", "uuid": "d5f3c6ae-2d75-8585-01db-1654e072ccc4"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 0, -5], "to": [6.5, 0, 7], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2.5, 1, -1], "uv_offset": [16, 16], "faces": {"north": {"uv": [19.75, 28, 27.75, 31], "texture": 1}, "east": {"uv": [15.75, 28, 20.75, 31], "texture": 1}, "south": {"uv": [31.75, 28, 39.75, 31], "texture": 1}, "west": {"uv": [27.75, 28, 32.75, 31], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "e6c579d2-7889-949a-12d1-857017eefc82"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 1, -4], "to": [6, 1, 6], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2, 1, -2], "uv_offset": [16, 16], "faces": {"north": {"uv": [19.75, 28, 27.75, 31], "texture": 1}, "east": {"uv": [15.75, 28, 20.75, 31], "texture": 1}, "south": {"uv": [31.75, 28, 39.75, 31], "texture": 1}, "west": {"uv": [27.75, 28, 32.75, 31], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "50a2ce97-d2af-7630-2003-9c7550e6d731"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 2, -4], "to": [5.5, 2, 6], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2.5, 1, -2], "uv_offset": [16, 16], "faces": {"north": {"uv": [19.75, 28, 27.75, 31], "texture": 1}, "east": {"uv": [15.75, 28, 20.75, 31], "texture": 1}, "south": {"uv": [31.75, 28, 39.75, 31], "texture": 1}, "west": {"uv": [27.75, 28, 32.75, 31], "texture": 1}, "up": {"uv": [28, 20, 20, 16], "texture": 1}, "down": {"uv": [36, 16, 28, 20], "texture": 1}}, "type": "cube", "uuid": "dc7f1dde-47a8-eed7-6fe2-267561a42971"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.5, 16, -5], "to": [4.5, 25, -4], "autouv": 0, "color": 0, "inflate": 0.51, "rotation": [0, 0, 17.5], "origin": [3.5, 21.5, -4.5], "faces": {"north": {"uv": [18.25, 7.75, 19, 16], "texture": 1}, "east": {"uv": [18.25, 7.75, 19, 16], "texture": 1}, "south": {"uv": [18.25, 7.75, 19, 16], "texture": 1}, "west": {"uv": [18.25, 7.75, 19, 16], "texture": 1}, "up": {"uv": [18.25, 7.75, 19, 16], "texture": 1}, "down": {"uv": [18.25, 7.75, 19, 16], "texture": 1}}, "type": "cube", "uuid": "5c5711e9-957f-e279-6504-787b2f7283bb"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 16, -5], "to": [-4.5, 25, -4], "autouv": 0, "color": 0, "inflate": 0.51, "rotation": [0, 0, -17.5], "origin": [-3.5, 21.5, -4.5], "faces": {"north": {"uv": [18.25, 7.75, 19, 16], "texture": 1}, "east": {"uv": [18.25, 7.75, 19, 16], "texture": 1}, "south": {"uv": [18.25, 7.75, 19, 16], "texture": 1}, "west": {"uv": [18.25, 7.75, 19, 16], "texture": 1}, "up": {"uv": [18.25, 7.75, 19, 16], "texture": 1}, "down": {"uv": [18.25, 7.75, 19, 16], "texture": 1}}, "type": "cube", "uuid": "f0111f91-b1aa-7a80-5612-8fdf452e31f8"}], "outliner": [{"name": "armorHead", "origin": [0, 26, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", "5c5711e9-957f-e279-6504-787b2f7283bb", "f0111f91-b1aa-7a80-5612-8fdf452e31f8", {"name": "antenna", "origin": [0, 2, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "e6e694b2-e5b6-a68a-2813-b4030f5ebae0", "386141bc-47b9-bea0-c6e2-25422c84001c", "d5f3c6ae-2d75-8585-01db-1654e072ccc4", "98129b1d-efe7-fed8-dc5c-1e4a9f9c4dcc", "50a2ce97-d2af-7630-2003-9c7550e6d731", "dc7f1dde-47a8-eed7-6fe2-267561a42971", "e6c579d2-7889-949a-12d1-857017eefc82"]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", {"name": "wingRight", "origin": [6, 22, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 22, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-6, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\5yl\\lenopan_0.png", "name": "lenopan_0.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "17183c8f-ac1e-9821-d1fe-fdb247798122", "relative_path": "../textures/models/ben/5yl/lenopan_0.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": true, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "024bf3ac-a147-9be0-d26c-743541c7c08c": {"name": "armorBody", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "27f4441b-4b1f-3e48-20db-bf08807b7ed1", "time": 0.3333333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "4ca6c853-5e93-ff39-3d86-68c25adc56bb", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "0d557d97-1cc6-100f-fc7c-0d8ca990b1b8", "time": 0.5, "color": -1, "interpolation": "linear"}]}, "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0": {"name": "armorRightArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 17.5, "y": "0", "z": "0"}], "uuid": "beee2f81-b038-d007-42f0-474cd2543b07", "time": 0.3333333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f136277e-8da1-5bd7-c83a-d8cd2ab64344", "time": 0, "color": -1, "interpolation": "linear"}]}, "cf2edd07-eb3f-1d93-d994-e27c3bfc6244": {"name": "armorLeftArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 17.5, "y": "0", "z": "0"}], "uuid": "532b646d-84c0-e4d7-0102-7f2e758f04d9", "time": 0.3333333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "1fca2e36-2550-98c5-19c2-326e273bd99c", "time": 0, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json", "itemModelPath": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\geo\\gun.item.json"}}