{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "talpaedan", "model_identifier": "", "visible_box": [4, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 28, -3], "to": [2, 33, 3], "autouv": 0, "color": 0, "export": false, "inflate": 0.51, "origin": [-1, 3, 1], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 3}, "east": {"uv": [0, 8, 8, 16], "texture": 3}, "south": {"uv": [24, 8, 32, 16], "texture": 3}, "west": {"uv": [16, 8, 24, 16], "texture": 3}, "up": {"uv": [16, 8, 8, 0], "texture": 3}, "down": {"uv": [24, 0, 16, 8], "texture": 3}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 28, -3], "to": [2, 33, 3], "autouv": 0, "color": 0, "export": false, "inflate": 1, "origin": [-1, 3, 1], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 3}, "east": {"uv": [32, 8, 40, 16], "texture": 3}, "south": {"uv": [56, 8, 64, 16], "texture": 3}, "west": {"uv": [48, 8, 56, 16], "texture": 3}, "up": {"uv": [48, 8, 40, 0], "texture": 3}, "down": {"uv": [56, 0, 48, 8], "texture": 3}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 18, -3], "to": [7, 28, 3], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "origin": [1, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [18.75, 20, 29, 28], "texture": 3}, "east": {"uv": [16, 20, 20, 28], "texture": 3}, "south": {"uv": [32, 20, 42, 28], "texture": 3}, "west": {"uv": [28, 20, 32, 28], "texture": 3}, "up": {"uv": [28, 20, 20, 16], "texture": 3}, "down": {"uv": [36, 16, 28, 20], "texture": 3}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 18, -3], "to": [7, 28, 3], "autouv": 0, "color": 0, "export": false, "inflate": 0.75, "origin": [1, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [18.5, 36, 29, 44], "texture": 3}, "east": {"uv": [16, 36, 20, 44], "texture": 3}, "south": {"uv": [32, 36, 40, 44], "texture": 3}, "west": {"uv": [28, 36, 32, 44], "texture": 3}, "up": {"uv": [28, 36, 20, 32], "texture": 3}, "down": {"uv": [36, 32, 28, 36], "texture": 3}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8.5, 23, -2], "to": [12.5, 27, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "origin": [4, 3, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 25], "texture": 3}, "east": {"uv": [40, 20, 44, 25], "texture": 3}, "south": {"uv": [52, 20, 56, 25], "texture": 3}, "west": {"uv": [48, 20, 52, 25], "texture": 3}, "up": {"uv": [48, 20, 44, 16], "texture": 3}, "down": {"uv": [52, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8.5, 23, -2], "to": [12.5, 27, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.75, "origin": [4, 3, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 41], "texture": 3}, "east": {"uv": [40, 36, 44, 41], "texture": 3}, "south": {"uv": [52, 36, 56, 41], "texture": 3}, "west": {"uv": [48, 36, 52, 41], "texture": 3}, "up": {"uv": [48, 36, 44, 32], "texture": 3}, "down": {"uv": [52, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.5, 0, -2], "to": [5.5, 9, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "origin": [1.5999999999999996, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 3}, "east": {"uv": [0, 20, 4, 32], "texture": 3}, "south": {"uv": [12, 20, 16, 32], "texture": 3}, "west": {"uv": [8, 20, 12, 32], "texture": 3}, "up": {"uv": [8, 20, 4, 16], "texture": 3}, "down": {"uv": [12, 16, 8, 20], "texture": 3}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.5, 0, -2], "to": [5.5, 9, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.75, "origin": [1.5999999999999996, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 3}, "east": {"uv": [0, 36, 4, 48], "texture": 3}, "south": {"uv": [12, 36, 16, 48], "texture": 3}, "west": {"uv": [8, 36, 12, 48], "texture": 3}, "up": {"uv": [8, 36, 4, 32], "texture": 3}, "down": {"uv": [12, 32, 8, 36], "texture": 3}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 0, -2], "to": [-1.5, 9, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "origin": [-0.5999999999999996, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 3}, "east": {"uv": [16, 52, 20, 64], "texture": 3}, "south": {"uv": [28, 52, 32, 64], "texture": 3}, "west": {"uv": [24, 52, 28, 64], "texture": 3}, "up": {"uv": [24, 52, 20, 48], "texture": 3}, "down": {"uv": [28, 48, 24, 52], "texture": 3}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 0, -2], "to": [-1.5, 9, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.75, "origin": [-0.5999999999999996, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 3}, "east": {"uv": [0, 52, 4, 64], "texture": 3}, "south": {"uv": [12, 52, 16, 64], "texture": 3}, "west": {"uv": [8, 52, 12, 64], "texture": 3}, "up": {"uv": [8, 52, 4, 48], "texture": 3}, "down": {"uv": [12, 48, 8, 52], "texture": 3}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 10, -2], "to": [5, 17, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "origin": [1, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [19.75, 27, 28, 32], "texture": 3}, "east": {"uv": [16, 27, 20, 32], "texture": 3}, "south": {"uv": [32, 27, 40, 32], "texture": 3}, "west": {"uv": [28, 27, 32, 32], "texture": 3}, "up": {"uv": [28, 27, 20, 23], "texture": 3}, "down": {"uv": [36, 16, 28, 20], "texture": 3}}, "type": "cube", "uuid": "47be0fe0-4398-8741-dca4-e1300713f26f"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 10, -2], "to": [5, 17, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.75, "origin": [1, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 45, 28, 48], "texture": 3}, "east": {"uv": [16, 43, 20, 48], "texture": 3}, "south": {"uv": [32, 43, 40, 48], "texture": 3}, "west": {"uv": [28, 43, 32, 48], "texture": 3}, "up": {"uv": [28, 43, 20, 39], "texture": 3}, "down": {"uv": [36, 32, 28, 36], "texture": 3}}, "type": "cube", "uuid": "a63267a7-e09a-dab3-984d-bb8a02ce6e29"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-12, 23, -2], "to": [-8, 27, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "origin": [-4, 3, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 58], "texture": 3}, "east": {"uv": [32, 52, 36, 58], "texture": 3}, "south": {"uv": [44, 52, 48, 58], "texture": 3}, "west": {"uv": [40, 52, 44, 58], "texture": 3}, "up": {"uv": [40, 52, 36, 48], "texture": 3}, "down": {"uv": [44, 48, 40, 52], "texture": 3}}, "type": "cube", "uuid": "358c9da7-4186-1b74-9631-c5291d9ac803"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-12, 23, -2], "to": [-8, 27, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.75, "origin": [-4, 3, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 58], "texture": 3}, "east": {"uv": [48, 52, 52, 58], "texture": 3}, "south": {"uv": [60, 52, 64, 58], "texture": 3}, "west": {"uv": [56, 52, 60, 58], "texture": 3}, "up": {"uv": [56, 52, 52, 48], "texture": 3}, "down": {"uv": [60, 48, 56, 52], "texture": 3}}, "type": "cube", "uuid": "f7854f42-bf39-ac33-74a8-a7693c379c00"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8, 8, -4], "to": [12, 18, 0], "autouv": 0, "color": 0, "export": false, "inflate": 0.75, "rotation": [19.999999999999996, 0, 0], "origin": [0.25, 14.5, -2], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 42, 48, 46.5], "texture": 3}, "east": {"uv": [40, 42, 44, 46.5], "texture": 3}, "south": {"uv": [52, 42, 56, 46.5], "texture": 3}, "west": {"uv": [48, 42, 52, 46.5], "texture": 3}, "up": {"uv": [48, 36, 44, 32], "texture": 3}, "down": {"uv": [52, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "aa9be1cc-a8f4-b818-0bcb-2f6d63fc273a"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8.5, 8, -4], "to": [12.5, 18, 0], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [0.25, 14.5, -2], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 26, 48, 30.5], "texture": 3}, "east": {"uv": [40, 26, 44, 30.5], "texture": 3}, "south": {"uv": [52, 26, 56, 30.5], "texture": 3}, "west": {"uv": [48, 26, 52, 30.5], "texture": 3}, "up": {"uv": [48, 20, 44, 16], "texture": 3}, "down": {"uv": [52, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "af406a98-c213-2447-b442-5ac60b542fd8"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.5, 28, -5], "to": [3.5, 35, 3], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 1, "rotation": [0, 27.5, 0], "origin": [1, 3, -1], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": null}, "east": {"uv": [24.25, 0, 31.25, 8], "texture": 3}, "south": {"uv": [56, 8, 64, 16], "texture": null}, "west": {"uv": [48, 8, 56, 16], "texture": null}, "up": {"uv": [48, 8, 40, 0], "texture": null}, "down": {"uv": [56, 0, 48, 8], "texture": null}}, "type": "cube", "uuid": "0b990db5-a99b-c686-9a92-f22429e96dc3"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 28, -5], "to": [2.5, 35, 3], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 1, "rotation": [0, -27.5, 0], "origin": [-1, 3, -1], "uv_offset": [32, 0], "faces": {"north": {"uv": [48, 8, 40, 16], "texture": null}, "east": {"uv": [56, 8, 48, 16], "texture": null}, "south": {"uv": [64, 8, 56, 16], "texture": null}, "west": {"uv": [31.25, 0, 24.25, 8], "texture": 3}, "up": {"uv": [40, 8, 48, 0], "texture": null}, "down": {"uv": [48, 0, 56, 8], "texture": null}}, "type": "cube", "uuid": "185f09b7-9243-d21c-cbc1-f644f617a03f"}, {"name": "Thing", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10.5, 22.96602540378444, -9.5], "to": [-9.5, 29.96602540378444, -8.5], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "rotation": [35, 0, 0], "origin": [-4, 6, -8], "uv_offset": [40, 16], "faces": {"north": {"uv": [47, 54, 45, 55.5], "texture": 3}, "east": {"uv": [47, 55.5, 45, 54], "texture": 3}, "south": {"uv": [45, 54, 47, 55.5], "texture": 3}, "west": {"uv": [47, 54, 45, 55.5], "texture": 3}, "up": {"uv": [45, 54, 47, 55.5], "texture": 3}, "down": {"uv": [47, 54, 45, 55.5], "texture": 3}}, "type": "cube", "uuid": "533d46d7-444f-0458-d31a-fd03b82f8521"}, {"name": "Thing", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-11, 30.966025403784442, -10], "to": [-9, 30.966025403784442, -8], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "rotation": [35, 0, 0], "origin": [-5, 6, -8], "uv_offset": [40, 16], "faces": {"north": {"uv": [47, 54, 45, 55.5], "texture": 3}, "east": {"uv": [47, 55.5, 45, 54], "texture": 3}, "south": {"uv": [45, 54, 47, 55.5], "texture": 3}, "west": {"uv": [47, 54, 45, 55.5], "texture": 3}, "up": {"uv": [45, 54, 47, 55.5], "texture": 3}, "down": {"uv": [47, 54, 45, 55.5], "texture": 3}}, "type": "cube", "uuid": "cd829283-adb3-af77-bec5-5103607295c3"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 12, 2], "to": [1.5, 15, 6], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [17.499999999999993, 0, 0], "origin": [0, 13.5, 4], "uv_offset": [9, 36], "faces": {"north": {"uv": [12, 38, 13, 40], "texture": 3}, "east": {"uv": [12, 38, 13, 40], "texture": 3}, "south": {"uv": [12, 38, 13, 40], "texture": 3}, "west": {"uv": [12, 38, 13, 40], "texture": 3}, "up": {"uv": [12, 38, 13, 40], "texture": 3}, "down": {"uv": [12, 38, 13, 40], "texture": 3}}, "type": "cube", "uuid": "647d3829-2274-f92d-d612-44f2683225fc"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 12, 5], "to": [1, 14, 9], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [30.000000000000007, 0, 0], "origin": [0.5, 13.5, 6], "uv_offset": [9, 36], "faces": {"north": {"uv": [12, 38, 13, 40], "texture": 3}, "east": {"uv": [12, 38, 13, 40], "texture": 3}, "south": {"uv": [12, 38, 13, 40], "texture": 3}, "west": {"uv": [12, 38, 13, 40], "texture": 3}, "up": {"uv": [12, 38, 13, 40], "texture": 3}, "down": {"uv": [12, 38, 13, 40], "texture": 3}}, "type": "cube", "uuid": "2383d7b0-4206-c0c2-693c-44dfe2fbe1ab"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 10.81698729810778, 7.281088913245535], "to": [0.5, 11.81698729810778, 10.281088913245535], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [30.000000000000007, 0, 0], "origin": [-2.220446049250313e-16, 11.31698729810778, 8.781088913245537], "uv_offset": [9, 36], "faces": {"north": {"uv": [12, 38, 13, 40], "texture": 3}, "east": {"uv": [12, 38, 13, 40], "texture": 3}, "south": {"uv": [12, 38, 13, 40], "texture": 3}, "west": {"uv": [12, 38, 13, 40], "texture": 3}, "up": {"uv": [12, 38, 13, 40], "texture": 3}, "down": {"uv": [12, 38, 13, 40], "texture": 3}}, "type": "cube", "uuid": "6b62e333-5008-ea3b-dd83-72f32809e4de"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.29704824891479165, 34.221808227731856, -4.039757792211379], "to": [-0.29704824891479165, 36.221808227731856, -4.039757792211379], "autouv": 0, "color": 0, "export": false, "inflate": 0.51, "rotation": [61.81321456798652, -56.77405779671273, -98.************], "origin": [0, 33.57830427709356, -1], "faces": {"north": {"uv": [27.25, 0, 28, 0.75], "texture": 3}, "east": {"uv": [27.25, 0, 28, 0.75], "texture": 3}, "south": {"uv": [27.25, 0, 28, 0.75], "texture": 3}, "west": {"uv": [27.25, 0, 28, 0.75], "texture": 3}, "up": {"uv": [27.25, 0, 28, 0.75], "texture": 3}, "down": {"uv": [27.25, 0, 28, 0.75], "texture": 3}}, "type": "cube", "uuid": "fe7c1fcc-6dd7-7f17-5934-b33e1f1f5635"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.29704824891479165, 34.221808227731856, -4.039757792211379], "to": [0.29704824891479165, 36.221808227731856, -4.039757792211379], "autouv": 0, "color": 0, "export": false, "inflate": 0.51, "rotation": [61.81321456798652, 56.77405779671273, 98.************], "origin": [0, 33.57830427709356, -1], "faces": {"north": {"uv": [28, 0, 27.25, 0.75], "texture": 3}, "east": {"uv": [28, 0, 27.25, 0.75], "texture": 3}, "south": {"uv": [28, 0, 27.25, 0.75], "texture": 3}, "west": {"uv": [28, 0, 27.25, 0.75], "texture": 3}, "up": {"uv": [28, 0, 27.25, 0.75], "texture": 3}, "down": {"uv": [28, 0, 27.25, 0.75], "texture": 3}}, "type": "cube", "uuid": "04a1cbce-e430-b398-b99b-d211fa7bf70f"}, {"name": "Thing", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 30.966025403784442, -10], "to": [11, 30.966025403784442, -8], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "rotation": [35, 0, 0], "origin": [5, 6, -8], "uv_offset": [40, 16], "faces": {"north": {"uv": [45, 54, 47, 55.5], "texture": 3}, "east": {"uv": [45, 54, 47, 55.5], "texture": 3}, "south": {"uv": [47, 54, 45, 55.5], "texture": 3}, "west": {"uv": [45, 55.5, 47, 54], "texture": 3}, "up": {"uv": [47, 54, 45, 55.5], "texture": 3}, "down": {"uv": [45, 54, 47, 55.5], "texture": 3}}, "type": "cube", "uuid": "9cef5bfb-23ac-c9bc-659c-f6c4a538930c"}, {"name": "Thing", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9.5, 22.96602540378444, -9.5], "to": [10.5, 29.96602540378444, -8.5], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "rotation": [35, 0, 0], "origin": [4, 6, -8], "uv_offset": [40, 16], "faces": {"north": {"uv": [45, 54, 47, 55.5], "texture": 3}, "east": {"uv": [45, 54, 47, 55.5], "texture": 3}, "south": {"uv": [47, 54, 45, 55.5], "texture": 3}, "west": {"uv": [45, 55.5, 47, 54], "texture": 3}, "up": {"uv": [47, 54, 45, 55.5], "texture": 3}, "down": {"uv": [45, 54, 47, 55.5], "texture": 3}}, "type": "cube", "uuid": "2b90e3f0-dabc-029b-a9a7-86bc4492b482"}, {"name": "tail", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 11, -9], "to": [0.5, 11, -2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "rotation": [53.47830000000022, -0.05206252861114037, 0.6832148980363318], "origin": [1, -5, -1], "uv_offset": [16, 32], "faces": {"north": {"uv": [12, 38, 13, 40], "texture": 3}, "east": {"uv": [12, 38, 13, 40], "texture": 3}, "south": {"uv": [12, 38, 13, 40], "texture": 3}, "west": {"uv": [12, 38, 13, 40], "texture": 3}, "up": {"uv": [12, 38, 13, 40], "texture": 3}, "down": {"uv": [12, 38, 13, 40], "texture": 3}}, "type": "cube", "uuid": "8eb3e6c5-aa58-c194-0a2d-cead3b7143d8"}, {"name": "tail", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 11.375826949523677, 0.8117498084508306], "to": [1, 13.375826949523677, 8.31174980845083], "autouv": 0, "color": 3, "export": false, "inflate": 0.75, "origin": [1.0000000000000002, 13.375826949523677, 2.811749808450826], "faces": {"north": {"uv": [12, 38, 13, 40], "texture": 3}, "east": {"uv": [12, 38, 13, 40], "texture": 3}, "south": {"uv": [12, 38, 13, 40], "texture": 3}, "west": {"uv": [12, 38, 13, 40], "texture": 3}, "up": {"uv": [12, 38, 13, 40], "texture": 3}, "down": {"uv": [12, 38, 13, 40], "texture": 3}}, "type": "cube", "uuid": "05a5aa5e-79c1-27fe-d2be-5db475da51e4"}, {"name": "middle", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.7870196862835952, 8.402700216124504, 8.27039268500538], "to": [8.712980313716404, 10.402700216124504, 10.27039268500538], "autouv": 0, "color": 4, "export": false, "inflate": 0.75, "rotation": [0, 0, -22.5], "origin": [6.9629803137164075, 9.902700216124504, 13.770392685005376], "faces": {"north": {"uv": [12, 38, 13, 40], "texture": 3}, "east": {"uv": [12, 38, 13, 40], "texture": 3}, "south": {"uv": [12, 38, 13, 40], "texture": 3}, "west": {"uv": [12, 38, 13, 40], "texture": 3}, "up": {"uv": [12, 38, 13, 40], "texture": 3}, "down": {"uv": [12, 38, 13, 40], "texture": 3}}, "type": "cube", "uuid": "91960df9-9b30-63d7-59a5-c1599a5b9ba5"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8.5, 18, -2], "to": [12, 22, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.75, "origin": [4, -2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 41, 48, 45], "texture": 3}, "east": {"uv": [40, 41, 44, 45], "texture": 3}, "south": {"uv": [52, 41, 56, 45], "texture": 3}, "west": {"uv": [48, 41, 52, 45], "texture": 3}, "up": {"uv": [48, 36, 44, 32], "texture": 3}, "down": {"uv": [52, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "ed679282-b01c-2e38-db60-b1d7be2dd3b2"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8.5, 18, -2], "to": [12, 22, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "origin": [4, -2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 25, 48, 29], "texture": 3}, "east": {"uv": [40, 25, 44, 29], "texture": 3}, "south": {"uv": [52, 25, 56, 29], "texture": 3}, "west": {"uv": [48, 25, 52, 29], "texture": 3}, "up": {"uv": [48, 20, 44, 16], "texture": 3}, "down": {"uv": [52, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "441def10-3b0f-4a26-07a6-d97233430dfa"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-12, 18, -2], "to": [-8.5, 22, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "origin": [-4, -2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [48, 25, 44, 29], "texture": 3}, "east": {"uv": [52, 25, 48, 29], "texture": 3}, "south": {"uv": [56, 25, 52, 29], "texture": 3}, "west": {"uv": [44, 25, 40, 29], "texture": 3}, "up": {"uv": [44, 20, 48, 16], "texture": 3}, "down": {"uv": [48, 16, 52, 20], "texture": 3}}, "type": "cube", "uuid": "*************-06d3-249c-4e9a8531c662"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-12, 18, -2], "to": [-8.5, 22, 2], "autouv": 0, "color": 0, "export": false, "inflate": 0.75, "origin": [-4, -2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [48, 41, 44, 45], "texture": 3}, "east": {"uv": [52, 41, 48, 45], "texture": 3}, "south": {"uv": [56, 41, 52, 45], "texture": 3}, "west": {"uv": [44, 41, 40, 45], "texture": 3}, "up": {"uv": [44, 36, 48, 32], "texture": 3}, "down": {"uv": [48, 32, 52, 36], "texture": 3}}, "type": "cube", "uuid": "381ae36e-fb9a-bae5-7b6f-566d67ed106b"}, {"name": "drillo hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [8.849999999999998, 6.482459033712734, -6.736161146605351], "to": [12.349999999999998, 7.482459033712734, -3.236161146605351], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [10.599999999999998, 6.982459033712733, -4.736161146605349], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 31, 45, 32], "texture": 3}, "east": {"uv": [44, 31, 45, 32], "texture": 3}, "south": {"uv": [44, 31, 45, 32], "texture": 3}, "west": {"uv": [44, 31, 45, 32], "texture": 3}, "up": {"uv": [44, 31, 45, 32], "texture": 3}, "down": {"uv": [44, 31, 45, 32], "texture": 3}}, "type": "cube", "uuid": "035d22a6-f7aa-279b-04ab-9481f471cf6c"}, {"name": "drillo hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [9.3, 4.61815063694444, -7.1], "to": [11.8, 4.61815063694444, -4.6], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [10.674999999999997, 4.868150636944442, -5.5057064690881035], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 31, 45, 32], "texture": 3}, "east": {"uv": [44, 31, 45, 32], "texture": 3}, "south": {"uv": [44, 31, 45, 32], "texture": 3}, "west": {"uv": [44, 31, 45, 32], "texture": 3}, "up": {"uv": [44, 31, 45, 32], "texture": 3}, "down": {"uv": [44, 31, 45, 32], "texture": 3}}, "type": "cube", "uuid": "e12906cd-6305-d287-8009-b90be8274a1a"}, {"name": "Right Hand", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8, 4, -4], "to": [12, 7, 0], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [19.999999999999996, 0, 0], "origin": [0.25, 14.5, -2], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 46.5, 48, 48], "texture": 3}, "east": {"uv": [40, 46.5, 44, 48], "texture": 3}, "south": {"uv": [52, 46.5, 56, 48], "texture": 3}, "west": {"uv": [48, 46.5, 52, 48], "texture": 3}, "up": {"uv": [48, 36, 44, 32], "texture": 3}, "down": {"uv": [52, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "c302e070-a3f2-d0c9-a1ab-c22c78ca64a7"}, {"name": "Right Hand", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8.5, 4, -4], "to": [12.5, 7, 0], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [0.25, 14.5, -2], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 30.5, 48, 32], "texture": 3}, "east": {"uv": [40, 30.5, 44, 32], "texture": 3}, "south": {"uv": [52, 30.5, 56, 32], "texture": 3}, "west": {"uv": [48, 30.5, 52, 32], "texture": 3}, "up": {"uv": [48, 20, 44, 16], "texture": 3}, "down": {"uv": [52, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "2d5e9efd-b8fc-b292-3955-342399f2fb8a"}, {"name": "drillo hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [9.5, 3.61815063694444, -6.9], "to": [11.5, 3.61815063694444, -4.9], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [10.674999999999997, 4.868150636944442, -5.5057064690881035], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 31, 45, 32], "texture": 3}, "east": {"uv": [44, 31, 45, 32], "texture": 3}, "south": {"uv": [44, 31, 45, 32], "texture": 3}, "west": {"uv": [44, 31, 45, 32], "texture": 3}, "up": {"uv": [44, 31, 45, 32], "texture": 3}, "down": {"uv": [44, 31, 45, 32], "texture": 3}}, "type": "cube", "uuid": "3aef1e9a-e0f0-7f3c-ab0a-503080ed4a86"}, {"name": "drillo hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [9.8, 2.61815063694444, -6.7], "to": [11.3, 2.61815063694444, -5.2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [10.674999999999997, 4.868150636944442, -5.5057064690881035], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 31, 45, 32], "texture": 3}, "east": {"uv": [44, 31, 45, 32], "texture": 3}, "south": {"uv": [44, 31, 45, 32], "texture": 3}, "west": {"uv": [44, 31, 45, 32], "texture": 3}, "up": {"uv": [44, 31, 45, 32], "texture": 3}, "down": {"uv": [44, 31, 45, 32], "texture": 3}}, "type": "cube", "uuid": "424cc345-31c1-23f8-70ef-b0804dfd9140"}, {"name": "drillo hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [10, 1.6181506369444398, -6.5], "to": [11, 1.6181506369444398, -5.5], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [0.17499999999999716, 4.868150636944442, -5.5057064690881035], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 31, 45, 32], "texture": 3}, "east": {"uv": [44, 31, 45, 32], "texture": 3}, "south": {"uv": [44, 31, 45, 32], "texture": 3}, "west": {"uv": [44, 31, 45, 32], "texture": 3}, "up": {"uv": [44, 31, 45, 32], "texture": 3}, "down": {"uv": [44, 31, 45, 32], "texture": 3}}, "type": "cube", "uuid": "bd3d1a16-05cd-96bc-e150-29d2b9cb7b28"}, {"name": "drillo hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [9, 5.61815063694444, -6.8], "to": [12, 5.61815063694444, -4.3], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [10.674999999999997, 5.868150636944442, -5.5057064690881035], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 31, 45, 32], "texture": 3}, "east": {"uv": [44, 31, 45, 32], "texture": 3}, "south": {"uv": [44, 31, 45, 32], "texture": 3}, "west": {"uv": [44, 31, 45, 32], "texture": 3}, "up": {"uv": [44, 31, 45, 32], "texture": 3}, "down": {"uv": [44, 31, 45, 32], "texture": 3}}, "type": "cube", "uuid": "c205950c-d850-f6f2-435e-0db2eb5ba9cc"}, {"name": "drillo hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-11, 1.6181506369444398, -6.5], "to": [-10, 1.6181506369444398, -5.5], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [-0.17499999999999716, 4.868150636944442, -5.5057064690881035], "uv_offset": [40, 16], "faces": {"north": {"uv": [45, 31, 44, 32], "texture": 3}, "east": {"uv": [45, 31, 44, 32], "texture": 3}, "south": {"uv": [45, 31, 44, 32], "texture": 3}, "west": {"uv": [45, 31, 44, 32], "texture": 3}, "up": {"uv": [45, 31, 44, 32], "texture": 3}, "down": {"uv": [45, 31, 44, 32], "texture": 3}}, "type": "cube", "uuid": "c79feebc-37ec-60be-80d7-90922d8a39ab"}, {"name": "drillo hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-11.3, 2.61815063694444, -6.7], "to": [-9.8, 2.61815063694444, -5.2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [-10.674999999999997, 4.868150636944442, -5.5057064690881035], "uv_offset": [40, 16], "faces": {"north": {"uv": [45, 31, 44, 32], "texture": 3}, "east": {"uv": [45, 31, 44, 32], "texture": 3}, "south": {"uv": [45, 31, 44, 32], "texture": 3}, "west": {"uv": [45, 31, 44, 32], "texture": 3}, "up": {"uv": [45, 31, 44, 32], "texture": 3}, "down": {"uv": [45, 31, 44, 32], "texture": 3}}, "type": "cube", "uuid": "42ea8eb3-f2d2-b18f-0dd2-435c02a44a45"}, {"name": "drillo hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-11.5, 3.61815063694444, -6.9], "to": [-9.5, 3.61815063694444, -4.9], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [-10.674999999999997, 4.868150636944442, -5.5057064690881035], "uv_offset": [40, 16], "faces": {"north": {"uv": [45, 31, 44, 32], "texture": 3}, "east": {"uv": [45, 31, 44, 32], "texture": 3}, "south": {"uv": [45, 31, 44, 32], "texture": 3}, "west": {"uv": [45, 31, 44, 32], "texture": 3}, "up": {"uv": [45, 31, 44, 32], "texture": 3}, "down": {"uv": [45, 31, 44, 32], "texture": 3}}, "type": "cube", "uuid": "8482c02e-5918-6004-36a2-00f91aa76eb3"}, {"name": "drillo hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-12, 5.61815063694444, -6.8], "to": [-9, 5.61815063694444, -4.3], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [-10.674999999999997, 5.868150636944442, -5.5057064690881035], "uv_offset": [40, 16], "faces": {"north": {"uv": [45, 31, 44, 32], "texture": 3}, "east": {"uv": [45, 31, 44, 32], "texture": 3}, "south": {"uv": [45, 31, 44, 32], "texture": 3}, "west": {"uv": [45, 31, 44, 32], "texture": 3}, "up": {"uv": [45, 31, 44, 32], "texture": 3}, "down": {"uv": [45, 31, 44, 32], "texture": 3}}, "type": "cube", "uuid": "ebedf7e0-05ef-cdbf-4f42-a5945b5e9079"}, {"name": "drillo hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-11.8, 4.61815063694444, -7.1], "to": [-9.3, 4.61815063694444, -4.6], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [-10.674999999999997, 4.868150636944442, -5.5057064690881035], "uv_offset": [40, 16], "faces": {"north": {"uv": [45, 31, 44, 32], "texture": 3}, "east": {"uv": [45, 31, 44, 32], "texture": 3}, "south": {"uv": [45, 31, 44, 32], "texture": 3}, "west": {"uv": [45, 31, 44, 32], "texture": 3}, "up": {"uv": [45, 31, 44, 32], "texture": 3}, "down": {"uv": [45, 31, 44, 32], "texture": 3}}, "type": "cube", "uuid": "830d1ff5-975e-94ec-5409-3bfd6fe4feb0"}, {"name": "drillo hand", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-12.349999999999998, 6.482459033712734, -6.736161146605351], "to": [-8.849999999999998, 7.482459033712734, -3.236161146605351], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [-10.599999999999998, 6.982459033712733, -4.736161146605349], "uv_offset": [40, 16], "faces": {"north": {"uv": [45, 31, 44, 32], "texture": 3}, "east": {"uv": [45, 31, 44, 32], "texture": 3}, "south": {"uv": [45, 31, 44, 32], "texture": 3}, "west": {"uv": [45, 31, 44, 32], "texture": 3}, "up": {"uv": [45, 31, 44, 32], "texture": 3}, "down": {"uv": [45, 31, 44, 32], "texture": 3}}, "type": "cube", "uuid": "ea59d4aa-f581-8124-9592-2b3f12cdd701"}, {"name": "Left Hand", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-12, 4, -4], "to": [-8, 7, 0], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [19.999999999999996, 0, 0], "origin": [-0.25, 14.5, -2], "uv_offset": [40, 32], "faces": {"north": {"uv": [48, 46.5, 44, 48], "texture": 3}, "east": {"uv": [52, 46.5, 48, 48], "texture": 3}, "south": {"uv": [56, 46.5, 52, 48], "texture": 3}, "west": {"uv": [44, 46.5, 40, 48], "texture": 3}, "up": {"uv": [44, 36, 48, 32], "texture": 3}, "down": {"uv": [48, 32, 52, 36], "texture": 3}}, "type": "cube", "uuid": "e100607f-20eb-4245-f61c-d5643639e475"}, {"name": "Left Hand", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-12.5, 4, -4], "to": [-8.5, 7, 0], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [-0.25, 14.5, -2], "uv_offset": [40, 16], "faces": {"north": {"uv": [48, 30.5, 44, 32], "texture": 3}, "east": {"uv": [52, 30.5, 48, 32], "texture": 3}, "south": {"uv": [56, 30.5, 52, 32], "texture": 3}, "west": {"uv": [44, 30.5, 40, 32], "texture": 3}, "up": {"uv": [44, 20, 48, 16], "texture": 3}, "down": {"uv": [48, 16, 52, 20], "texture": 3}}, "type": "cube", "uuid": "5a858fa9-f622-8854-fd24-29a29349f66e"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-12, 8, -4], "to": [-8, 18, 0], "autouv": 0, "color": 0, "export": false, "inflate": 0.75, "rotation": [19.999999999999996, 0, 0], "origin": [-0.25, 14.5, -2], "uv_offset": [40, 32], "faces": {"north": {"uv": [48, 42, 44, 46.5], "texture": 3}, "east": {"uv": [52, 42, 48, 46.5], "texture": 3}, "south": {"uv": [56, 42, 52, 46.5], "texture": 3}, "west": {"uv": [44, 42, 40, 46.5], "texture": 3}, "up": {"uv": [44, 36, 48, 32], "texture": 3}, "down": {"uv": [48, 32, 52, 36], "texture": 3}}, "type": "cube", "uuid": "b41cafbc-27fc-5895-ee8e-1e02aede9e4a"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-12.5, 8, -4], "to": [-8.5, 18, 0], "autouv": 0, "color": 0, "export": false, "inflate": 0.5, "rotation": [19.999999999999996, 0, 0], "origin": [-0.25, 14.5, -2], "uv_offset": [40, 16], "faces": {"north": {"uv": [48, 26, 44, 30.5], "texture": 3}, "east": {"uv": [52, 26, 48, 30.5], "texture": 3}, "south": {"uv": [56, 26, 52, 30.5], "texture": 3}, "west": {"uv": [44, 26, 40, 30.5], "texture": 3}, "up": {"uv": [44, 20, 48, 16], "texture": 3}, "down": {"uv": [48, 16, 52, 20], "texture": 3}}, "type": "cube", "uuid": "cafd88c0-4b9f-a65d-a8b0-70b2b3fecc24"}], "outliner": [{"name": "armorHead", "origin": [0, 27, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "fe7c1fcc-6dd7-7f17-5934-b33e1f1f5635", "04a1cbce-e430-b398-b99b-d211fa7bf70f", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", {"name": "ears", "origin": [0, 3, 0], "color": 0, "uuid": "7ccb28ee-7e41-494b-dc98-dd0595991033", "export": false, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": false, "autouv": 0, "children": ["0b990db5-a99b-c686-9a92-f22429e96dc3", "185f09b7-9243-d21c-cbc1-f644f617a03f"]}, {"name": "antenna", "origin": [0, 3, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "a63267a7-e09a-dab3-984d-bb8a02ce6e29", "47be0fe0-4398-8741-dca4-e1300713f26f", {"name": "tail2", "origin": [0, 1, 0], "color": 0, "uuid": "e39af513-000b-7087-8f47-d475fd3877fe", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["8eb3e6c5-aa58-c194-0a2d-cead3b7143d8", "05a5aa5e-79c1-27fe-d2be-5db475da51e4", {"name": "middle", "origin": [0, 1, 0], "color": 0, "uuid": "efb46a94-c500-eb91-6f15-9201ef648263", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["91960df9-9b30-63d7-59a5-c1599a5b9ba5", {"name": "back", "origin": [0, -9, 15], "rotation": [-90, 0, 0], "color": 5, "uuid": "69f0077d-8d4f-b1de-8d18-6c3f89fa35b0", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}]}]}, {"name": "tail", "origin": [0, 1, 0], "color": 0, "uuid": "56c00391-c18e-c2f1-cb3c-8b80296029b8", "export": false, "mirror_uv": false, "isOpen": false, "locked": true, "visibility": false, "autouv": 0, "children": ["647d3829-2274-f92d-d612-44f2683225fc", "2383d7b0-4206-c0c2-693c-44dfe2fbe1ab", "6b62e333-5008-ea3b-dd83-72f32809e4de"]}]}, {"name": "armorRightArm", "origin": [7, 23, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", "441def10-3b0f-4a26-07a6-d97233430dfa", "ed679282-b01c-2e38-db60-b1d7be2dd3b2", "af406a98-c213-2447-b442-5ac60b542fd8", "aa9be1cc-a8f4-b818-0bcb-2f6d63fc273a", "2d5e9efd-b8fc-b292-3955-342399f2fb8a", "c302e070-a3f2-d0c9-a1ab-c22c78ca64a7", "035d22a6-f7aa-279b-04ab-9481f471cf6c", "e12906cd-6305-d287-8009-b90be8274a1a", "c205950c-d850-f6f2-435e-0db2eb5ba9cc", "3aef1e9a-e0f0-7f3c-ab0a-503080ed4a86", "424cc345-31c1-23f8-70ef-b0804dfd9140", "bd3d1a16-05cd-96bc-e150-29d2b9cb7b28", {"name": "drillo_right", "origin": [9, 26.233140496612474, 5.557010203003477], "rotation": [-5, 0, 0], "color": 0, "uuid": "66f9a015-86eb-675e-6d0f-8da4934956bf", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["2b90e3f0-dabc-029b-a9a7-86bc4492b482", "9cef5bfb-23ac-c9bc-659c-f6c4a538930c"]}, {"name": "wingRight", "origin": [7, 23, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 22, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-6, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["f7854f42-bf39-ac33-74a8-a7693c379c00", "358c9da7-4186-1b74-9631-c5291d9ac803", {"name": "drillo_left", "origin": [-9, 26.233140496612474, 5.557010203003477], "rotation": [-5, 0, 0], "color": 0, "uuid": "cdc0b131-ba63-1a7a-337b-49d896174c13", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["533d46d7-444f-0458-d31a-fd03b82f8521", "cd829283-adb3-af77-bec5-5103607295c3"]}]}, "381ae36e-fb9a-bae5-7b6f-566d67ed106b", "*************-06d3-249c-4e9a8531c662", "cafd88c0-4b9f-a65d-a8b0-70b2b3fecc24", "b41cafbc-27fc-5895-ee8e-1e02aede9e4a", "5a858fa9-f622-8854-fd24-29a29349f66e", "e100607f-20eb-4245-f61c-d5643639e475", "ea59d4aa-f581-8124-9592-2b3f12cdd701", "830d1ff5-975e-94ec-5409-3bfd6fe4feb0", "ebedf7e0-05ef-cdbf-4f42-a5945b5e9079", "8482c02e-5918-6004-36a2-00f91aa76eb3", "42ea8eb3-f2d2-b18f-0dd2-435c02a44a45", "c79feebc-37ec-60be-80d7-90922d8a39ab"]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\5yl\\talpaedan_withoutarms.png", "name": "talpaedan_withoutarms.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "1382fdc3-**************-2f4333acdc5a", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/5yl/talpaedan_withoutarms.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\talpaedan.png", "name": "talpaedan.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "1fed39f7-bf9e-18e1-6fbb-417bd754feb9", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/talpaedan.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_talpaedan.png", "name": "ov_talpaedan.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "ef01aa64-da41-4d41-fc78-51d9445cb5c9", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [97, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 51, "height": 37, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADMAAAAlCAYAAADvLtGsAAAAAXNSR0IArs4c6QAAAKlJREFUWEftlDEOQEAURO2lcAK12iX0CoVC78QkEgnlN3nJ5mfUZuy8t1Ga4LPux/mObMtcghXY6+GDpBozjNPHTN92N+kaDIXNpBrzXPgaR4XNpByD/ZKE4t9mhG9iUY/B0IrFNiMCxOI2g6EVi21GBIjFbQZDKxbbjAgQi9sMhlYsthkRIBa3GQytWGwzIkAsbjMYWrHYZkSAWNxmMLRisc2IALF4KjMXp/EYJvpnZAYAAAAASUVORK5CYII="}], "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/ov_talpaedan.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\albedo\\talpaedan_al.png", "name": "talpaedan_al.png", "folder": "", "namespace": "", "id": "5", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "a28c911f-46f2-a278-dc94-876f973214bf", "source": "data:image/png;base64,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*****************************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", "relative_path": "../textures/models/albedo/talpaedan_al.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_necrofriggian_talpaedan_flying.png", "name": "ov_necrofriggian_talpaedan_flying.png", "folder": "", "namespace": "", "id": "6", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "4f5b245a-8433-b413-b1c9-4c9ac474f9cb", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/bio/ov_necrofriggian_talpaedan_flying.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_necrofriggian_talpaedan.png", "name": "ov_necrofriggian_talpaedan.png", "folder": "", "namespace": "", "id": "7", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "07a16ca5-9eb6-b9ba-9b96-f851bd9d7775", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAIABJREFUeF7tXW2sZtVVfi/DzBhipaFhJhlTEY3EkUmdYGWgwrTIhzaFatD+sDFAlDGaSWeaCQ1lIFBGGCQlN2UaonHapPCDmrSS2sGQ8FFw0AIVyUgGMRhFJJ1khpSAVeJ8wJj3nLsu96x71llr7bXPx/u+D3+G9579+ey1nv3stffZZ27U83/r7nvlZM9N6LX6Q1vPmfM24Nbd91Yw27Vzu7sMb51tpV954Zak8T/51uuVJs198MNtNbG23Fz1H39mb69j12vlY2RBACCAFM/N5YApdY/z5KofBAAF4CbhK377s5VZ84LzNxV2PIlKAAoACiBJAqYy99DypSwBQAD5ZuBUe4ACSEWO5cMSwL8EIAingQigAKAAoAASyRQE8D5wCAKmGZF7/ZlWjZwLCiBdAeQeiz7KgwKAAoAC6MPzBlInCAAEAAIYiDP20QwvAby3YX3RzLm/f7TS3JMXXVH8PuXgy510A0HATDBjCYAlgMeUQAAetPS0iAHoGLWaImUbsNUGdVy4VQGQ41PzJAVAz9tWAlAAmQwFCgAKwGJKIAALSv40UAAKZkd2bPCjuiTHmvmDjfmhAJrfBeCOb1UAbSsBKICQW7yfeegKAASQaaCFYrQlAAigXfyhAAR8o47Pi5WUABRAVQFIDs/x1GIAktvkig1AAWQipqEqABBApgFWiuEKAATQDe6LS6luq1te29AIILfja0oACqBUAFbH98YA2lICUACZmAMEgF0AEEAmZ0ooBjEABhoUQIIVBbKs2DKfdBI0NQbAm5oaE4ACCAz60qxQALOtAEAAeBcgaQbIxD/LioECaAvZ+nL7JoB39+7oXQV3i3i1tt47DwUABZDiALmWACCAFPQz5gEBgABSzAkEkILa8jxQAAgC5rGkxFKwBEgELlO23gngvq//VREDuPP/zjN1SVuja2fvqZKbf+KF4n95vVr5pkY2JOLtm/VzACCAqEXF8oMAGPGAAGIG5c0NAvAiljf9YAiAuiUpAa9jSkqAZn6pPm893uGAAqgiBgLwWlDe9CAAKIC8FuUsDQTgBCxz8sETQHRG5jMuFEBmCwoWBwIIAhjMDgKAAgiaUCw7CCCGXzT34AmAOuhVAqkxgNT6tIHAfQD1CIEANMtp9zkIQNh+9BKONkwgABCAZiN9PJ9LZWCtsXsuWKclMT3//NeeqKQ7+cL9jfnmzru28vwr119qqkdLtO3ZQ1qSpOdrN16ZlC+a6fCBh4siUt+G4/V73+ePtj/XScDUduTCLbX+XG8jggCMIwACaAYKBGA0pEzJBkcAuWZ8CR9NCbQ180vtyU0IXSsBKICYJ0IBMPxAADGDAgH48MMS4PUKYKlfRw4vAdp2fG4WXAnw57nW/FZzzKUEQAD1iEdf120rxgUFsDBeIIA8wUEQAAjAOumM0/UeA+ja8QkcUgA86ESM3LUCoHZNihKgtT+1O9dMlisIGJ3xNSfKpQhy4aa1V3oOAlj4TDQ3ZBBAs0mBANIuIeWoggAy7fMTsA+fvrf432PHjxX/Xv3O1lpLTlUAD512X1HeqpWrin+vfHtLKvnW5oMCWB/Cs+2ZnzcuqgRAACCAik2BAEAAIQZ0Zp66JQDN0Nede0YFiv/+x9+r/CYFwKW+9Pef+tVvVvJ/46U3RXXhHINWCIAKzR0U5NJ/aDEAqwKgT4lJMaDjz9iu2YYCKC0geRswdxAQBFClHxBAPR2DAEpcelcA0myZSgw0Uz/4yv9Uiv7sOT9Z/CYlYFUATeVxVeGZ+XNJfU+d47ReQpBmfF5vrrVsdBcACsBnESAA9pIPJwYQQPmyj/YfCEBDqP55LtzSah+wAuAdsiqCFb/0l0XWb/3n8eJfitZTeb/10+UHhD634+eLf6UYwFfn/714/jc/rL7prO0uSAPR14yfahjefFFD7mrmp37lWgJQeZMaCxisAgABeF2w3/QggNi5gCh+qaM/MQQgdVBSBlwJfOZnV1aUwf49v9ioADZv+9fiOc9Hv9/9lz+qbdK0z/TSOEQNGArg5VQfDuUDAQgxABCAz65AAFAAg/g6r9cQpRiA1fzr9pGjs5m17iGl8+LO2x7FzBr9RwygivzEKwBuSF5DzE0AQ3LKLtvixR0EUEUgil/qWE88AfB9bfGk2pFXUzEq8r235uza/Nb6Q5VPQOaoAXetALgSoN/WE4B8SLALsCW2Bkq1casDngICSIXYlA8EELP/KH6mQapJNLEKQDvRtnhXneL4u7b9YQWWW/d8vRFLUgLW+lMHZtLyRQ24LwWQC2cogI4VgNUBtZkfBJDHBUAAUACd7gJoBPDGo1+ttWxyeJrpJQLg6XhhZ17xuUbPsZ6hz+N+/ZcCAgABgACW+CEIwEdKWALgIJDLYqAAXHC1njhVAUQdnzrmPQeQG5BoDIDak4pjan+mNgiIJUCqSaTlSzVcEEAV71Qc00Ztgt4G5B3UFACl50QQjQFoa3+qF0uAZpPM5fjTpgC6VgJTqwBAAKlzQlo+78wFAmjG2Ytn2qjNoAJIBYryQQHUI6gZbG6H562YlhiAZJ8avql2PXMKIBUoEEBsxgIBxCxv8AQQ695odOrqVZ1uI/L2njh6rHr1T7RDzvxaFPm7G+8uSrx6x5uukh+aL29H/vSBG135uk489Blcizld//YjIch27dzeq/2FGj++FThagOYAVP6pD9Qb8o233RNqQt8DoPUfBBAaXjWzhj8IoBlCEIBqYs0JNAMEAQQBVrJr+FN2iQigAILjYx0AKAAsAYKmVpvdan8ggHr0oQCCVqkZIBRAEOBMCkBSAlwBPPuD51wNfvQ7D4Z9yFVh5sThxmsOgBgAgoCZbbZSnNX+QABQAK3YoWaAUACtwL5YqIY/r50vBaAAguNjHQDEABADCJpaKAYABQAF0Ib9jTQChAJoBfbWFIC3tX1vQ3vby9MjBhBEEASwI2xDkSHQ8PcuAbxtAQEYrxTDEgBLAK9zWdKDACwoyWnC7G0dABAACCBmqvW5rfZnjQF42wgFAAXQ+C4EYgBel/KltxKA9SAQzgH48FeDYFQcFAAUgNO0TMlBACaYxERYAsTwUwkQCiAIsJJdIwDvy0BQAM7x0gYACgAnAZ0m5Uqu2R8IoBnOOQ1A12gsScyB/9GOc1OLaszHXyf+2umfrE3f111/k6YA+LjtHV1c4Jl6n8GW0dOV8bCOw+Ftt1Tyrd1zR5L9aARgbY9UebT8vu9TAAEkmZU9EwjATwBj59+z764KyNuuummUQgJRB9VGOlr+zBAAAZlbCUgXinAlEGV6zRD4czKMXDNo2+3XDJn3j/pFf+czvYaX1B9y/rtvv6FSxHicPSRg7U8U12g9IADNUpTnIIAggAvZrYZMtbVBANz5l47tmBA8JGDtDwjAuI9vNTMr8FRedABuPntFbdO6igWQxE+dCa24So6X685A77jlJoCmmZ/6biEBbz+i9hetb+oUQBQQr0OAAPJcGuodtzYJYDzTL10CkBIAAXi9Q08/t3r1StOtvmvWrddLG41G797wbVO6vhQAj0GcuKbcpkv9T4qaS+Vpa2XuWLyc1Ki61r+hEcC4vZwEQADaKPqfgwBAAIXVDJEAOAmMf2txAG8/Zn4JkFsBHDlU/Vzyh+ZfaqSl6ADwJQBf+2u7DrkVgMbB3hleKy+KH5XvdZzcS4BxO5bGAXgAUHN+rf1kB1zJcnvV8Jae82C0dqLwiTM/WhTVewwABJB3CaAZEAigRCj3NiAIQLO8+ufZlwASo0pKIDqDSQpAm/kJjq4VQNowybmi+A1JAVBbUg4CSQTA7aAtBUBt15QAzfyUfuoVAHUUBJDb9ZtnUm9t2gzKy2tjCbC0Du9RYBCAd8TL9J0pgLTm6bk44/ITZFoJfSmAPzj22OjxfQ8tNu+/fufPtabWPp90BbDu8W8U/cq1FtdA7FsBvPzPBytNfP3VF8Nv5Gp9bnoOAsi8C9AE9huft70QdeZXmgOnlZnywMOR8V/M25cCAAGAAEIGPEkKgAhg5z+cVdvn3b/2WvF3EEDIJBozt6UAvEtcIj4ogOBYgwCgADwmBAKoooUlQHAJQHBqEvpn/vpPRv/09P5F9LkKoNl/nOBXLt480mICudb+1vZzJ8sdBORBP82p6dVgwt2665ObAB6aP0NrauPzT209jBhABMG+FYDVgTgBjPMRCSx1fhCAzRpAADactFRQAJkUgEYEuQgg98yvtVsyoFwKwDvz8/Z8ZP+B2iZKiiCqAPiukab8tPGamXMAGhOlPh+KAtAcKdcSQDOoVBw1Q25rCQAC6PfLStkVwF/ccLjRBr13y2kG3TcB0H0A9F5+kyPl2AUgAuD1ajhpz6eNALT+0vMooWq4aeVPnQIAAVwp2h4I4H1oyDHaUgAgABsC2RQAOb42E6bekSd1R7oSzHoiMHoSkNplUQKRg0Btzfza0qWrGMBz+z9QVHXvY5dWqrzg/E2V35tO31383rT5x8W/UgzAZv7yS0nW/FAAC0iBAMq3CjUCJMMaxwSW/mfd9sst/UEAsXMUIACBADiDRm/JHboC4EqAfms3AGkzDY+257oDUBofrT30PPcuABSAFfm86VpbAoAASgRAAPUGy2MAIIC8jm0tbdmHQaSPeEp3Ah667LqiLk2acgVAjmE9wUUd4mv2S994vtJXvmbs+vsA3jsCtYFq6w7AoSoAbZeITt5RDIAfCNLw5M+1KL1W3sQvAfinwUAA2pA3PwcB2PCTFAAIwIZfrlTZPw0mKQH6e+4ZjSsADgwpAlICUcaXgNdmglwDRuXk7oe3/V3HAOiOve2XP1FAAAWQx6JAAHlwdN+qG60WBFBuA2IJELOkiScAPnNteOxLtYgcvLz8e27Hocq8M2hs2PL3w9v+thQAlgBRy/DlBwH48BJTex0oWm1uIvO2HwRQjqCGmzZOvR8F5kHAqGFSfus38zSAtPbQAKSWIwU9pXq1LyR5v4ug9U+71Zbn7+puPa3d1uc/fH5lkVTbBpzUGIAWswEBBO+0AwFUXQ0EYKWeMl3qxOFd+kn1TC0BFOBuu6Vg9qbDMNEB8A338tTaLgLPwc8Z8OfSF2EoBmFtrxTLSK3fWm/X6R780+9UFMC0xQBmVgGAAKquBAKopxYQQM/3AbQVAxhL8xc3byxGvemNrb4VQNsxADJ77RuJ3D2sN9ogBnBHAYEWjJOUTdT+rPXO3BIABFA1ORBAvQsiCAgFkGXZyb8RyNfifO1856vvFvV2FQPI0snRaIQYQHkLLz8J6A3KaWtz63hBAQhIda0AQADlQGifpbYadlfpcsUAQABpI5b9INDSZvSxCyB9aor27+ntRWpnVzGAtOFZnst7DiFXvW2VQ0uA1PLpKDAIIA1BEMADN7qQ69sB+67fBZYhMQhgSmIAXIJrY9/W23nUDikGQGt/at+0xQD2P/W4Bj2eDwiBpx7d1++XgXJtA4IAulmDa0FAEMCAvNvQlIkngItfe7LSzc2fuKz4zQ1R+vvTZ11igMmexBsDsJdsS8mjwt4bj3gtfPswum8t9cIazbahoKeifmg3Omm7Odpz3hJver0nsRS7dm6fbAUAAqgaAAjA5hAggBKnqSGAVaeV97qTRCWmPfZOeXEDKQD+99wKwGZ+7aeKEkFXMz9Hom0lwBVM1wqA+kt2qC2p2rYUEEDmJUDbA2YtHwRQjxQIoIpL7wRgNWikq0dAC6LSvQje78D/7X1rT45r1L4D0PfrpH3bxcoLtxQ4pf53/Jm9va7BU9udK99Mdz4HiCCAHCimlwECSMdunBMEEMNvBAIIAhjMDgKIAQgCiOEHAgjiF80OAoghCAKI4QcCCOIXzQ4CiCEIAojhBwII4hfNDgKIIQgCiOEHAgjiF80OAoghCAKI4QcCCOIXzQ4CiCEIAojhBwII4hfNDgKIIQgCiOEHAgjiF80OAoghCAKI4QcCCOIXzQ4CiCEIAojhBwII4hfNDgKIIQgCiOEHAgjiF83uJYD3NqyvVDnr71KAAIIWiKPAQQCD2UEAMQBBADH8oACC+EWzWwmAz/xULxRAdARmPD8UQL8GAAKI4T9nBZBXIzGqtzlRBr51972V98F33/6FShPmzru2sUnR98E1Aoj2Typ/7oEvFv06cfRYSMXR+J984f5GnDQcKfPJt14v/nfugx/2mkKr6dtSAKn+Q52N2l8UNBBA8EIIEEDVBEEAPpcEAeyNfRiBKwCC/659B00jER0AjQBS15razE/lQgHUD7NVoUYVGhSAyc3kRNEBAAFgCVBnXSAAm2O6lwBWYG3Vj0azQgAcD95vTUnQmp+XAwUABWD1tbp0IICWYwDS4IAAImar57VOVNEJaOaWAFZgpRnr5DV/Vhm96ABMyhJAN9n6FBKOQ40B9L0bYLXP1NgMHyUQgGDZIIBUl6/mAwH4cAQB+PAyLwGswJLBXvL9o5WWPPmx1cVvUgC59rG5ArBG/6lxXe0C+Ibl/dQakUYVFJ/BJvU8gNU+tViMd5xmRgFYAQYBeE2oOT0IwIan1T5BAExhSgwmzQR8DU/FSY5Pz0kB8AGIRrGnVQFojt/WGhYKwEY4lGpqFQAIwGYI2vadrZTlqUAAPuSgAHx4LU7c2hpwzXx5ou7Ijg2VGrgSgAJIGwApV18EQO2JKoGujwSDANLsb1kQkA88CKAZ2GlTACAAnyNNzRJAcnyC49wLfqH4X76W51F9Sk+7ANLan9IhBlBvcFwBSLGX3LsAvDWSEtDeDsylAKz1QwH4iGvZEgAEkAZgVwoABFAdH05AIIA0+507dfWqyvv0JPl5cZoCWGSUhffUrc2BAhi2AtCWBJISiCoAbUKimBTVDwKwehwj0kkngFPX/2ZBYKkXUAz1INBQlgAggGbHmvgYABGANPNbYwBWBRCd8flwTBsBWKP/hEPbMQAvAURnfl6fZJfarpR1PuwKP6k90QnI2k8p3eISAASQBmXuGAAIoBwHWgKAANLs0prLTAAa47b1vrrWESiA2I1KXgmrReWjCkBb+3N7ILuUgqSa/UABLAQBNQUAAqg3pVlRANalAAhAo5zq896XAPxWWE1ySfv+vNu51/oSrJOuALTXfbWZresZjBx89B/fqwwJReNTCcA780uxKemE6iKBZb6PwquguB2DAHyEuSw1CKDjJcDCtd8ggNIUp44AyMOkI8B9zfSTrgC0mZ73T5v5KX1fCoC2XRdn7p/79aJJqduxuRWANZjaNX6DVwAgAJ8kscYAQADNuIIAfHaXK7X6MtDQZnzenugSgMrzrsVI+kkn0LwOL61RtYE+5eDLRZLU9mvlL1Mmwpd/cq39+QSktY8Hpym9diNVXwpqcZwXvsTUVaxMwhEEsIBMqgOBAEoAQQAaVVWfk+KZGALou6G5YgBcavKz7BoR8KAPEUDqjG81GykmQArAqmSiQavFXYCFChdjAYnfBDzz98tvOUozubYrxfHjMz89b+tGqlQ8QQBWy1fSeZcAIIAtlZe/vMMAAqgiNvEEwF8G4gYx1Jmf2qkRQPRmG81BtPJ5fvHtOeXrvMvW4gv72VwBaO3N9ZxL/tQlgFcBSEqBxw7o7dWhKQB+xPnQ1nNCX3eOjuey14FBAD5IQQDlZ8BBADa7mRgCGPrMrykAq2NqN9tow6qdjdfyS8+7an9y+9iavy0C8LaPxww0xRC1c+8SAATgHdHEGEBXDgQCaFcBeM0FBOBDbHH9wWMBUWb0NSM9NY8BaI7P++Vl8PSWpuXU+pNbwXjL8878Wn/SUNJztWXP5DdW3AanABalNLsarC3A9KHypQABXOsDjKXWdkW0wkEA5ZV6E08A2kAP9TkRAH85hdqrEVnbCsB7Vx2P6mszptXwvLEGa7kaAWjt1+xKGz8tf9vPpV00625P3/3rdQsix+CAAPIqABoTEIDNOkEANpxaSyUNgJVZcysA74wvAaOdLLQ6qAa8NkNr9UgKwFouT2cdN61fXT3XztEs3pMgnPPou7+TrwBY7MIq/SkdCOD+Rl8BATRTCQigK6oV6onuXkQJINeMvyi9le8qaA4ZHQ7vtuayE4HCTCe1W3v3ItqfrvJrRMDb0ffMv2hvXQHUVj0ggLzIggDS8AQBpOEWzkXApzJqqgLIPfNrCoC/Ddj2OwBWIpDuCLQGE6dFAVB/rUSQaq9hh2EFTE0MIBVQEEC9SYEA0lwNBJCGW2+5hkIAWtTfeh9AbiC1aL5WnxazmDUFkDpRaTinPp94BZDa8eguQO4lAAggOpLDyK8pABDAMMZpsRVDVwDa7cBtxwL4cFkVgTbzU7nTpgAGZt5qc6AALky7IacrBQACUG0YCQIIzDwBBLArsmqSL1q+lr9vSbnuvlcarxg7fODhxi7cfPYKrYuNz3ft3N6rDacqyKEooF7BC438QDKDAEAAEVPsewkEAoiM3mg0Wr16Ze0MeOKau00lT/oMKCkAbeYncCa9/1AAJjOf3kQggHoFAAKw2TwUgA2nwaYCAVQJwOr4NKCXvvG8a2wvOH9TJT1iAC74liXGEiCGH5YALAgIAvAZFBSAD6/BpYYCgAKIGCUIIILeAPKCAEAAETMEAUTQG0BeEECMALALsLfXZXivlQ/Af8NNAAGAACJGBAUQQW8AeUEAIICIGYIAIugNIC8IAAQQMUMQQAS9AeQFAcQOAuEcAGIAA3Dj9CaAAEAA6dYzGkEBRNAbQF4QQOxlICgAKIABuHF6E0AAIIB065kCBbBiy3z5cUThPvu+L7TgN9jsvO3LlfG6+/YbIuMn5v3Q/EuVZ94jstFGrd14ZW0R17/9SOXvu2//wkgbo6a2SPVQntR+H952S6XatXvuSIJEa9+hreeEtsLJ/nnjur6pKQmcsd+mZqR8IIB6BEEAJS4pBDB2/j377qoAu+2qm0YpJAACaPbw1gjAO6u0zZg3XbWhFoncCoA7fnQmTCVobvh85qdy21YA3v6T8/NxufG2e0YeEtAcn9rVlgKYFEUAAsi8BAAB1FOWRQksdf6xw9N/RAYeEgAB2KaOMAFIV2INRQFcsvZ/CyT4e+QEz52vvmtDSkhlNTTvTJjaKN6eDY99qbH/XSkArf+a81N+jQS84wEFkGppC/lAAPXBNglWy0wYGZJpIoCly4Cx449/gwAi1rE8b3YF4J35214rDU0BrLjnd00jeOTQy6Z0PBFfgkyqAuDOP+4nCCDJJBozgQA6XgKAAOqvCW8K/i2NBUAB5CWB3ghAOjdg/aKMFYa+FcCPdpxbaeqadetNTU9VALzwj3/yM2oMYMWo8Wr/xvby/lC7pWBoUyyAkwAPBDY5v3aikMeASGEcPXo85APSOQAJtLZ3u0zGtSRRqPPjcngMwLoEAAE0D9UsEsAYkdRtQBCA1/XL9J0TAHf8S75/tNLyJz+2umzYedem9Yjl6ksB8JmfmjUrCoD6m7ItGjkIxIlAmvmpfV0rAKp3KEoABNBSDAAEUJp6CgGQEljK5dZTgCAA37zZGQFoMz81mxQA/Y4qAU0B5D4JqME/awpAwyP1uYTj+l+unviEAmhGGASQ+SSgZtAgAA0h23MQgA0nLVWYAKxRUFIAfM3PG/jSs/9W/OnIjiqTpyoBUgBUjzYjaIBFn/dFAFL/xycB29gFiOKk5ec4Hrrsutos0slAWqIhBqAhrTwHAfgABAH48JJSgwDy4BhWANZtQCiAcsBAACUOFByko9H8/X/NvCko+N2Ntq8wS+V9auvhkA9YJ0Be/8ztAoAAQABLnQAEkHbUWyNG7/MQ+40rs74MpO0C0NqfOpArBkDlSbGAad8FoP7TiUAeC+BvA576wI0uG5JOAvJCpO3AFzdvdNXHE2vbg9rbgV29DTgzCoA6yk8EggD6UQAggOa3NUEAIf59P7OkBKTi+W7AtCiAh+bPaET0j+9Za0I811FgLwGYGrckkTbDauXNmgLo6gi8hjs9Dy8BqCAQQIkECMBqemU6EECJQ+o2tw/t5amzEQAv2koIpARy7//z9vAYAD3/u0e+FcWw4vhbRk/XlkfRbopaa0qgbQVAjfzeI99u9VZgDVwigOf2f6BI+tzbOytZnv3Bc5Xf2y9/ovi9afOPi38nLQZACmDN/MFKvyjm1TURgABAACAAjaUannu3AWeGAFIVAeVriwm5EmhLAUhXf/WlAAhXvhsABdDOdwG4/fNtcB7zovSSEuDft8jlH60pABBA/c03IICqZczKEmDmCUBSUVqsIBfTSTGBJ878aOWRdx+c9rf3ji4uyqEYgKYAPn2g3G9f9/g3aqGRYgAnrmk++WaNytNdgX0pAH4CkGIAV+94s1GQU5B1qDEAHuWn7XArAUhKgBQAxQ5yxQw6UwAggBIBUgAggFIh0RFgEEDVQ7iDTy0BULe9SoCvibyxg5NvvV5kOXnRFYEQ0GjEZ1xSAuTgVDh3/FClDZmtCmDpTGO9xq2uWm99VIakAO597NIiCb21OWm7AHym5/dbaOdfrApASue1q94VAAjAO2TN6b0OOZ5pQADpY8B3AUAA6VgWOTUl4C1eiiFAAZRIDo0ApiUGIN11qSkA6R0YvgTgfpAaExiMArAqARDAdCsAEED9RTgzQwBdEUFbCoDaT7EA+s1jAl4iy52elgpDUwC5YgDWpVDul4GkaD+f2fkJWL6m5+PNTw5OrQIAAeR29fryQAAlLiCAbuzNXQuPBZw4esy0XNFiCBQTaFsBjDs8PuQy3t4a2uy/dDDGM9aQgoC5lgBdKQDN3vi+PWEvvQNDdi6VKykBawxg2YlCt2d2lAEE0A3QIIDYUWAQQEt2SsBaZ36pGdIAkRJ4b4PtW31S+U0zDR1z/cj+A6O2PwueOgxDI4BJiwHkXrJye9eUgPXmLPHcTKrhtJ0PBNA2wmX5IICYAgABdGOn4Vqsdxd6KxqKArj57BWVpvMTdPx7CHcufBKtawLgSqito8BdxQCs9qKBc9FBAAABWElEQVQtFagcqwKQ6uXnXqSZn+oxBdasnRxyOhDApsrwgABKOKK7AFabBwFYkWo5nfU7BtZmaAqg610AeruQv01It/fyL+gMRQFY8ebppBuBhqYAeLtTg9xaORqOXGHMjAKQ1mqRLbBxmSCA5lt3uUFKSwDNcKXnIIBVJz3YzTwBcCLIRQDXv/2IZxxGJMFdmQyJKRYgxQB4vWMFcNHHf8NQ8nQmeerRfVMxCWpLDGk3bSo6n2KaBBgIAASQYj9DywMCSBwR76WOvJqLX3uy8qfNn7is+L3/qcdNf3/6rEsSW16fLSUGsPO2Lye3Qdtt4AVr6aPPvR3ZtXP7VE2CEhFAAQiWAQL44ggE4KWN4aYHATjHJhcBrDqtvNee9ttJAfC/0wx37J3yXvvcCsDZ/eIgUA4C4OcMpHZEZ3gtv7f/06YAvP2fKvnj7fw4PQgABJBiN9OS5/8B6TKh1gL+3G0AAAAASUVORK5CYII=", "relative_path": "../textures/models/ben/bio/ov_necrofriggian_talpaedan.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_prypiatosian_a_talpaedan.png", "name": "ov_prypiatosian_a_talpaedan.png", "folder": "", "namespace": "", "id": "8", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "37073393-14ea-75cd-6858-a9205e9dfc76", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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***************************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"}], "source": "data:image/png;base64,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***************************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", "relative_path": "../textures/models/ben/bio/ov_prypiatosian_a_talpaedan.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK (1)\\addonpacks\\MARSHYS_OMNITRIX_ADDON_4.53_PATCHED\\assets\\m_ten\\textures\\models\\ben\\crtalpaedan.png", "name": "crtalpaedan.png", "folder": "", "namespace": "", "id": "9", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "ae8f6238-15c9-612b-0794-c44424e326ca", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}], "relative_path": "../../../../../../MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK (1)/addonpacks/MARSHYS_OMNITRIX_ADDON_4.53_PATCHED/assets/m_ten/textures/models/ben/crtalpaedan.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\crtalpaedan.png", "name": "crtalpaedan.png", "folder": "", "namespace": "", "id": "10", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "d0a732ff-f145-483d-cc4f-06640234186b", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/crtalpaedan.png"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": true, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}