{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "arburian_pelarota", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 24, -4], "to": [4, 32, 4], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.51, "origin": [0, 0, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 5}, "east": {"uv": [0, 8, 8, 16], "texture": 5}, "south": {"uv": [24, 8, 32, 16], "texture": 5}, "west": {"uv": [16, 8, 24, 16], "texture": 5}, "up": {"uv": [16, 8, 8, 0], "texture": 5}, "down": {"uv": [24, 0, 16, 8], "texture": 5}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 24, -4], "to": [4, 32, 4], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 1, "origin": [0, 0, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 5}, "east": {"uv": [32, 8, 40, 16], "texture": 5}, "south": {"uv": [56, 8, 64, 16], "texture": 5}, "west": {"uv": [48, 8, 56, 16], "texture": 5}, "up": {"uv": [48, 8, 40, 0], "texture": 5}, "down": {"uv": [56, 0, 48, 8], "texture": 5}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 20, -3], "to": [6, 29, 3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 26.5], "texture": 5}, "east": {"uv": [16, 20, 20, 32], "texture": 5}, "south": {"uv": [32, 20, 40, 29], "texture": 5}, "west": {"uv": [28, 20, 32, 32], "texture": 5}, "up": {"uv": [28, 20, 20, 16], "texture": 5}, "down": {"uv": [36, 16, 28, 20], "texture": 5}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 12, -3], "to": [6, 29, 3], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 0, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 5}, "east": {"uv": [16, 36, 20, 48], "texture": 5}, "south": {"uv": [32, 36, 40, 48], "texture": 5}, "west": {"uv": [28, 36, 32, 48], "texture": 5}, "up": {"uv": [28, 36, 20, 32], "texture": 5}, "down": {"uv": [36, 32, 28, 36], "texture": 5}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [7, 12, -2], "to": [11, 29, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [3, 5, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 5}, "east": {"uv": [40, 20, 44, 32], "texture": 5}, "south": {"uv": [52, 20, 56, 32], "texture": 5}, "west": {"uv": [48, 20, 52, 32], "texture": 5}, "up": {"uv": [48, 20, 44, 16], "texture": 5}, "down": {"uv": [52, 16, 48, 20], "texture": 5}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [7, 12, -2], "to": [11, 29, 2], "autouv": 0, "color": 0, "inflate": 1, "origin": [3, 5, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 5}, "east": {"uv": [40, 36, 44, 48], "texture": 5}, "south": {"uv": [52, 36, 56, 48], "texture": 5}, "west": {"uv": [48, 36, 52, 48], "texture": 5}, "up": {"uv": [48, 36, 44, 32], "texture": 5}, "down": {"uv": [52, 32, 48, 36], "texture": 5}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-11, 12, -2], "to": [-7, 29, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-3, 5, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 5}, "east": {"uv": [32, 52, 36, 64], "texture": 5}, "south": {"uv": [44, 52, 48, 64], "texture": 5}, "west": {"uv": [40, 52, 44, 64], "texture": 5}, "up": {"uv": [40, 52, 36, 48], "texture": 5}, "down": {"uv": [44, 48, 40, 52], "texture": 5}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-11, 12, -2], "to": [-7, 29, 2], "autouv": 0, "color": 0, "inflate": 1, "origin": [-3, 5, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 5}, "east": {"uv": [48, 52, 52, 64], "texture": 5}, "south": {"uv": [60, 52, 64, 64], "texture": 5}, "west": {"uv": [56, 52, 60, 64], "texture": 5}, "up": {"uv": [56, 52, 52, 48], "texture": 5}, "down": {"uv": [60, 48, 56, 52], "texture": 5}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1, 0, -2.6], "to": [5.5, 11, 2.6], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2.0999999046325684, 0, 0.3999999999999999], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 5}, "east": {"uv": [0, 20, 4, 32], "texture": 5}, "south": {"uv": [12, 20, 16, 32], "texture": 5}, "west": {"uv": [8, 20, 12, 32], "texture": 5}, "up": {"uv": [8, 20, 4, 16], "texture": 5}, "down": {"uv": [12, 16, 8, 20], "texture": 5}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1, 0, -2.6], "to": [5.5, 11, 2.6], "autouv": 0, "color": 0, "inflate": 1, "origin": [2.0999999046325684, 0, 0.3999999999999999], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 5}, "east": {"uv": [0, 36, 4, 48], "texture": 5}, "south": {"uv": [12, 36, 16, 48], "texture": 5}, "west": {"uv": [8, 36, 12, 48], "texture": 5}, "up": {"uv": [8, 36, 4, 32], "texture": 5}, "down": {"uv": [12, 32, 8, 36], "texture": 5}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 0, -2.6], "to": [-1, 11, 2.6], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-2.0999999046325684, 0, 0.3999999999999999], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 5}, "east": {"uv": [16, 52, 20, 64], "texture": 5}, "south": {"uv": [28, 52, 32, 64], "texture": 5}, "west": {"uv": [24, 52, 28, 64], "texture": 5}, "up": {"uv": [24, 52, 20, 48], "texture": 5}, "down": {"uv": [28, 48, 24, 52], "texture": 5}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 0, -2.6], "to": [-1, 11, 2.6], "autouv": 0, "color": 0, "inflate": 1, "origin": [-2.0999999046325684, 0, 0.3999999999999999], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 5}, "east": {"uv": [0, 52, 4, 64], "texture": 5}, "south": {"uv": [12, 52, 16, 64], "texture": 5}, "west": {"uv": [8, 52, 12, 64], "texture": 5}, "up": {"uv": [8, 52, 4, 48], "texture": 5}, "down": {"uv": [12, 48, 8, 52], "texture": 5}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 11, -3], "to": [5.5, 19, 3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.5, 0, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [10.75, 3, 21.75, 11], "texture": 5}, "east": {"uv": [14.25, 26.5, 20.25, 32], "texture": 5}, "south": {"uv": [32, 28.75, 40, 32], "texture": 5}, "west": {"uv": [26.25, 26.5, 32.25, 32], "texture": 5}, "up": {"uv": [32, 22, 20, 16], "texture": 5}, "down": {"uv": [36, 16, 28, 20], "texture": 5}}, "type": "cube", "uuid": "5708b945-6f7e-f194-dafc-3c4a03423174"}], "outliner": [{"name": "armorHead", "origin": [0, 24, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", {"name": "antenna", "origin": [0, 0, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorBody", "origin": [0, 24, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "5708b945-6f7e-f194-dafc-3c4a03423174"]}, {"name": "armorRightArm", "origin": [8, 27, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", {"name": "wingRight", "origin": [8, 27, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "armNested", "origin": [-8, 27, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134"]}]}, {"name": "armorRightLeg", "origin": [3.9000000000000004, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-3.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_arburian_pelarota.png", "name": "ov_arburian_pelarota.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "3cc38c6a-8d5f-a617-06ed-6733e68ca1e5", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/ov_arburian_pelarota.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\timeout\\arburian_pelarota_timeout.png", "name": "arburian_pelarota_timeout.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "444497dc-902d-2481-5bea-eddb84b733a4", "relative_path": "../textures/models/ben/timeout/arburian_pelarota_timeout.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\recalibrated\\r_arburian_pelarota.png", "name": "r_arburian_pelarota.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "4e06f11c-632c-39a0-3fe3-388d70a99815", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}], "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/recalibrated/r_arburian_pelarota.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\arburian_pelarota.png", "name": "arburian_pelarota.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "e5e96a28-b8e0-928d-8031-1b27794ed8a3", "relative_path": "../textures/models/ben/arburian_pelarota.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\5yl\\arburian_pelatora.png", "name": "arburian_pelatora.png", "folder": "", "namespace": "", "id": "6", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "4ea62389-2541-18a7-22b6-e80f202fa8b5", "relative_path": "../textures/models/ben/5yl/arburian_pelatora.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\5yl\\arburian_pelatora_face.png", "name": "arburian_pelatora_face.png", "folder": "", "namespace": "", "id": "7", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "82cbf347-2b5f-7986-742a-3536570a07f7", "relative_path": "../textures/models/ben/5yl/arburian_pelatora_face.png", "source": "data:image/png;base64,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****************************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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}