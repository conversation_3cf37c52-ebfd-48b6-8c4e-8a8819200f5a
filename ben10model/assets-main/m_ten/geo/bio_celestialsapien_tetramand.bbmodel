{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "bio_celestialsapien_tetramand", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 18, -2], "to": [5, 27, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 2, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 28.5], "texture": 7}, "east": {"uv": [16, 20, 20, 28.75], "texture": 7}, "south": {"uv": [32, 20, 40, 28.75], "texture": 7}, "west": {"uv": [28, 20, 32, 28.75], "texture": 7}, "up": {"uv": [28, 20, 20, 16], "texture": 7}, "down": {"uv": [36, 16, 28, 20], "texture": 7}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 13, -2], "to": [4.5, 27, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 7}, "east": {"uv": [16, 36, 20, 48], "texture": 7}, "south": {"uv": [32, 36, 40, 48], "texture": 7}, "west": {"uv": [28, 36, 32, 48], "texture": 7}, "up": {"uv": [28, 36, 20, 32], "texture": 7}, "down": {"uv": [0, 0, 0, 0], "texture": null}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 12, -2], "to": [9, 26, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [2, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 7}, "east": {"uv": [40, 36, 44, 48], "texture": 7}, "south": {"uv": [52, 36, 56, 48], "texture": 7}, "west": {"uv": [48, 36, 52, 48], "texture": 7}, "up": {"uv": [48, 36, 44, 32], "texture": 7}, "down": {"uv": [52, 32, 48, 36], "texture": 7}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.5, 12, -2], "to": [-5.5, 26, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 7}, "east": {"uv": [32, 52, 36, 64], "texture": 7}, "south": {"uv": [44, 52, 48, 64], "texture": 7}, "west": {"uv": [40, 52, 44, 64], "texture": 7}, "up": {"uv": [40, 52, 36, 48], "texture": 7}, "down": {"uv": [44, 48, 40, 52], "texture": 7}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.5, 12, -2], "to": [-5.5, 26, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-2, 1, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 7}, "east": {"uv": [48, 52, 52, 64], "texture": 7}, "south": {"uv": [60, 52, 64, 64], "texture": 7}, "west": {"uv": [56, 52, 60, 64], "texture": 7}, "up": {"uv": [56, 52, 52, 48], "texture": 7}, "down": {"uv": [60, 48, 56, 52], "texture": 7}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 7}, "east": {"uv": [0, 20, 4, 32], "texture": 7}, "south": {"uv": [12, 20, 16, 32], "texture": 7}, "west": {"uv": [8, 20, 12, 32], "texture": 7}, "up": {"uv": [8, 20, 4, 16], "texture": 7}, "down": {"uv": [12, 16, 8, 20], "texture": 7}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, -1, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 7}, "east": {"uv": [0, 36, 4, 48], "texture": 7}, "south": {"uv": [12, 36, 16, 48], "texture": 7}, "west": {"uv": [8, 36, 12, 48], "texture": 7}, "up": {"uv": [8, 36, 4, 32], "texture": 7}, "down": {"uv": [12, 32, 8, 36], "texture": 7}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.9, 0, -2], "to": [0.10000000000000009, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.9000000953674316, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 7}, "east": {"uv": [16, 52, 20, 64], "texture": 7}, "south": {"uv": [28, 52, 32, 64], "texture": 7}, "west": {"uv": [24, 52, 28, 64], "texture": 7}, "up": {"uv": [24, 52, 20, 48], "texture": 7}, "down": {"uv": [28, 48, 24, 52], "texture": 7}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, -1, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 7}, "east": {"uv": [0, 52, 4, 64], "texture": 7}, "south": {"uv": [12, 52, 16, 64], "texture": 7}, "west": {"uv": [8, 52, 12, 64], "texture": 7}, "up": {"uv": [8, 52, 4, 48], "texture": 7}, "down": {"uv": [12, 48, 8, 52], "texture": 7}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 13, -2], "to": [3.5, 17, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 26], "faces": {"north": {"uv": [20.75, 28, 27.25, 32], "texture": 7}, "east": {"uv": [16, 28.75, 20, 32], "texture": 7}, "south": {"uv": [32, 28.75, 40, 32], "texture": 7}, "west": {"uv": [28, 28.75, 32, 32], "texture": 7}, "up": {"uv": [34.25, 32, 28, 28.75], "texture": 7}, "down": {"uv": [34.25, 28.75, 28.25, 32], "texture": 7}}, "type": "cube", "uuid": "10f829d2-4186-9798-8c58-a625734e5d98"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 28, -4], "to": [3, 35, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 3, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 7}, "east": {"uv": [0, 8, 8, 16], "texture": 7}, "south": {"uv": [24, 8, 32, 16], "texture": 7}, "west": {"uv": [16, 8, 24, 16], "texture": 7}, "up": {"uv": [16, 8, 8, 0], "texture": 7}, "down": {"uv": [24, 0, 16, 8], "texture": 7}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 28, -4], "to": [3, 35, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 3, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 7}, "east": {"uv": [32, 8, 40, 16], "texture": 7}, "south": {"uv": [56, 8, 64, 16], "texture": 7}, "west": {"uv": [48, 8, 56, 16], "texture": 7}, "up": {"uv": [48, 8, 40, 0], "texture": 7}, "down": {"uv": [56, 0, 48, 8], "texture": 7}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 9, -1], "to": [-3.5, 17, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-1, -9, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 7}, "east": {"uv": [48, 52, 52, 64], "texture": 7}, "south": {"uv": [60, 52, 64, 64], "texture": 7}, "west": {"uv": [56, 52, 60, 64], "texture": 7}, "up": {"uv": [56, 52, 52, 48], "texture": 7}, "down": {"uv": [60, 48, 56, 52], "texture": 7}}, "type": "cube", "uuid": "4bba3088-cf8c-01ef-d978-387c2c4acae0"}, {"name": "Ritght Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 12, -2], "to": [9, 26, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 1, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [40, 52, 36, 64], "texture": 7}, "east": {"uv": [44, 52, 40, 64], "texture": 7}, "south": {"uv": [48, 52, 44, 64], "texture": 7}, "west": {"uv": [36, 52, 32, 64], "texture": 7}, "up": {"uv": [36, 52, 40, 48], "texture": 7}, "down": {"uv": [40, 48, 44, 52], "texture": 7}}, "type": "cube", "uuid": "eb287c72-7457-c5e5-0de3-8305e32a25d0"}, {"name": "Left Arm Second", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 9, -1], "to": [-4, 18, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, -9, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 7}, "east": {"uv": [32, 52, 36, 64], "texture": 7}, "south": {"uv": [44, 52, 48, 64], "texture": 7}, "west": {"uv": [40, 52, 44, 64], "texture": 7}, "up": {"uv": [40, 52, 36, 48], "texture": 7}, "down": {"uv": [44, 48, 40, 52], "texture": 7}}, "type": "cube", "uuid": "6328a29a-bacb-b764-d13a-09f4154a5e84"}, {"name": "Ritght Arm Second", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.5, 9, -1], "to": [6, 18, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, -9, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [40, 52, 36, 64], "texture": 7}, "east": {"uv": [44, 52, 40, 64], "texture": 7}, "south": {"uv": [48, 52, 44, 64], "texture": 7}, "west": {"uv": [36, 52, 32, 64], "texture": 7}, "up": {"uv": [36, 52, 40, 48], "texture": 7}, "down": {"uv": [40, 48, 44, 52], "texture": 7}}, "type": "cube", "uuid": "b52a5610-4e5e-f644-9112-567a5bf0cb13"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 18.5, -1.5], "to": [-4, 21.3, 1.5], "autouv": 0, "color": 0, "origin": [-5, 19, 0], "uv_offset": [29, 22], "faces": {"north": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "east": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "south": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "west": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "up": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "down": {"uv": [33.5, 25.75, 35, 27], "texture": 7}}, "type": "cube", "uuid": "935f059c-863c-22f0-e550-649a51b81fa8"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 20.5, -1.5], "to": [-4, 24.299999999999997, 1.5], "autouv": 0, "color": 0, "origin": [-5, 22, 0], "uv_offset": [29, 22], "faces": {"north": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "east": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "south": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "west": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "up": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "down": {"uv": [33.5, 25.75, 35, 27], "texture": 7}}, "type": "cube", "uuid": "cf7bbe88-beef-1d0c-f908-8d69d9b2f51a"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 20.5, -1.5], "to": [6, 24.299999999999997, 1.5], "autouv": 0, "color": 0, "origin": [5, 22, 0], "uv_offset": [29, 22], "faces": {"north": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "east": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "south": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "west": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "up": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "down": {"uv": [33.5, 25.75, 35, 27], "texture": 7}}, "type": "cube", "uuid": "90381fbd-8525-cfa3-a79e-0e35c1964c87"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 18.5, -1.5], "to": [6, 21.3, 1.5], "autouv": 0, "color": 0, "origin": [5, 19, 0], "uv_offset": [29, 22], "faces": {"north": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "east": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "south": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "west": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "up": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "down": {"uv": [33.5, 25.75, 35, 27], "texture": 7}}, "type": "cube", "uuid": "6f4c7327-d1a0-04a8-ffd2-0e75dfd02f4e"}, {"name": "Horns", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 34, -5.2], "to": [0.5, 40, -4.2], "autouv": 0, "color": 0, "rotation": [-2.4272711214772933, 0.2866813787678053, -0.5316775701867144], "origin": [-1, 25, -9], "uv_offset": [16, 0], "faces": {"north": {"uv": [18, 2, 20, 8], "texture": 7}, "east": {"uv": [16, 2, 18, 8], "texture": 7}, "south": {"uv": [22, 2, 24, 8], "texture": 7}, "west": {"uv": [20, 2, 22, 8], "texture": 7}, "up": {"uv": [20, 2, 18, 0], "texture": 7}, "down": {"uv": [22, 0, 20, 2], "texture": 7}}, "type": "cube", "uuid": "c3213818-af34-7cad-a494-15438947134c"}, {"name": "Horns", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 34, -5.2], "to": [1, 38, -4.2], "autouv": 0, "color": 0, "rotation": [-2.228905536103415, 1.003130062201052, 16.954881105644265], "origin": [0, 25, -9], "uv_offset": [16, 0], "faces": {"north": {"uv": [18, 2, 20, 6], "texture": 7}, "east": {"uv": [16, 2, 18, 6], "texture": 7}, "south": {"uv": [22, 2, 24, 6], "texture": 7}, "west": {"uv": [20, 2, 22, 6], "texture": 7}, "up": {"uv": [20, 2, 18, 0], "texture": 7}, "down": {"uv": [22, 0, 20, 2], "texture": 7}}, "type": "cube", "uuid": "b3d5899d-5032-be02-ad55-005baa5d8ba8"}, {"name": "Horns", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 34, -5.2], "to": [-0.5, 38, -4.2], "autouv": 0, "color": 0, "mirror_uv": true, "rotation": [-2.228905536103415, -1.003130062201052, -16.954881105644265], "origin": [0, 25, -9], "uv_offset": [16, 0], "faces": {"north": {"uv": [20, 2, 18, 6], "texture": 7}, "east": {"uv": [22, 2, 20, 6], "texture": 7}, "south": {"uv": [24, 2, 22, 6], "texture": 7}, "west": {"uv": [18, 2, 16, 6], "texture": 7}, "up": {"uv": [18, 2, 20, 0], "texture": 7}, "down": {"uv": [20, 0, 22, 2], "texture": 7}}, "type": "cube", "uuid": "fdbb175d-ef5d-a032-0464-1ee7f526df38"}], "outliner": [{"name": "armorHead", "origin": [0, 27, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", {"name": "antenna", "origin": [0, 3, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["fdbb175d-ef5d-a032-0464-1ee7f526df38", "b3d5899d-5032-be02-ad55-005baa5d8ba8", "c3213818-af34-7cad-a494-15438947134c"]}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "10f829d2-4186-9798-8c58-a625734e5d98", "6328a29a-bacb-b764-d13a-09f4154a5e84", "b52a5610-4e5e-f644-9112-567a5bf0cb13", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "935f059c-863c-22f0-e550-649a51b81fa8", "cf7bbe88-beef-1d0c-f908-8d69d9b2f51a", "6f4c7327-d1a0-04a8-ffd2-0e75dfd02f4e", "90381fbd-8525-cfa3-a79e-0e35c1964c87"]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["eb287c72-7457-c5e5-0de3-8305e32a25d0", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", {"name": "wingRight", "origin": [6, 22, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 23, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "armNested", "origin": [-5, 23, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134"]}, {"name": "armNested2", "origin": [-4, 13, 0], "color": 0, "uuid": "86add632-ae45-1f1f-e80a-6bb25715ce49", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["4bba3088-cf8c-01ef-d978-387c2c4acae0"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_tetramand.png", "name": "ov_tetramand.png", "folder": "", "namespace": "", "id": "6", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "834996bb-f7e5-8dda-3799-b5cba197156c", "relative_path": "../textures/models/ben/ov_tetramand.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\tetramand.png", "name": "tetramand.png", "folder": "", "namespace": "", "id": "7", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "7e18d943-4ca4-36bf-1983-28cfbcf53c58", "relative_path": "../textures/models/ben/tetramand.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\r_tetramand.png", "name": "r_tetramand.png", "folder": "", "namespace": "", "id": "8", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "faecebca-c3da-6b09-4f40-95d86fd452af", "relative_path": "../textures/models/ben/r_tetramand.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\MARSHY'S SECRETS OF THE OMNITRIX - BEN 10 ADDON (1)\\addonpacks\\MARSHYS_OMNITRIX_ADDON_4.53\\assets\\m_ten\\textures\\models\\ben\\recalibrated\\r_tetramand.png", "name": "r_tetramand.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "80649f2f-07c9-dab8-621f-aa9843e51d12", "relative_path": "../../../../../../MARSHY'S SECRETS OF THE OMNITRIX - BEN 10 ADDON (1)/addonpacks/MARSHYS_OMNITRIX_ADDON_4.53/assets/m_ten/textures/models/ben/recalibrated/r_tetramand.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\MARSHY'S SECRETS OF THE OMNITRIX - BEN 10 ADDON (1)\\addonpacks\\MARSHYS_OMNITRIX_ADDON_4.53\\assets\\m_ten\\textures\\models\\ben\\ov_tetramand.png", "name": "ov_tetramand.png", "folder": "", "namespace": "", "id": "5", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "6d0bd30a-71f6-8e89-2cd2-225315fc4711", "relative_path": "../../../../../../MARSHY'S SECRETS OF THE OMNITRIX - BEN 10 ADDON (1)/addonpacks/MARSHYS_OMNITRIX_ADDON_4.53/assets/m_ten/textures/models/ben/ov_tetramand.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\MARSHY'S SECRETS OF THE OMNITRIX - BEN 10 ADDON (1)\\addonpacks\\MARSHYS_OMNITRIX_ADDON_4.53\\assets\\m_ten\\textures\\models\\ben\\tetramand.png", "name": "tetramand.png", "folder": "", "namespace": "", "id": "9", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "d3ef2374-2c11-0c50-2992-b0ea2fbd2a0f", "relative_path": "../../../../../../MARSHY'S SECRETS OF THE OMNITRIX - BEN 10 ADDON (1)/addonpacks/MARSHYS_OMNITRIX_ADDON_4.53/assets/m_ten/textures/models/ben/tetramand.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\spicy.png", "name": "spicy.png", "folder": "", "namespace": "", "id": "10", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "ca3aac0f-1e6d-4b24-70ab-1df5f68fee6c", "relative_path": "../textures/models/ben/spicy.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}, "e9748d1d-6892-b67f-bb06-390a01e323a0": {"type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "424a6108-7241-5231-e6cd-a06427b42c15", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "996b8625-fe39-5fdd-6036-56e71d8d8af0", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "d127c75a-3565-1cd8-936a-2a6c9b93f876", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "8cca5482-9ae1-4019-bd25-7eff3dce9747", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "dddfb29f-dafb-7888-946f-f12b21be2d70", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "d558333b-91bb-3bd9-9efd-29a826488c6b", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}, "86add632-ae45-1f1f-e80a-6bb25715ce49": {"name": "armNested2", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "3c7db589-1bda-5211-9512-b73c26799a84", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "e9460072-b53a-0af5-10c2-499b155c66be", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "c3c0f602-75a0-48ae-c846-b17b3ed6b3a7", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "3efecafd-016b-38b6-e927-747f7f6788fa", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "7aa77861-ed59-d31f-4aba-9893db51542f", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "da2c19d6-be5f-621f-2040-4d675cc3571c", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}