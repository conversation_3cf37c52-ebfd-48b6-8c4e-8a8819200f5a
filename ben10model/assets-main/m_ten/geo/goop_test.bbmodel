{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": false}, "name": "goop_test", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 0, -5], "to": [7, 16, 7], "autouv": 0, "color": 0, "origin": [2, -11, 0], "uv_offset": [14, 14], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 3}, "east": {"uv": [2, 8, 7.75, 16], "texture": 3}, "south": {"uv": [22, 8.25, 30, 16.25], "texture": 3}, "west": {"uv": [16, 7.75, 22, 16.25], "texture": 3}, "up": {"uv": [16, 7.75, 8, 1.75], "texture": 3}, "down": {"uv": [30.25, 10.25, 22.25, 16.25], "texture": 3}}, "type": "cube", "uuid": "cf80a5bb-306b-1cc9-18a4-db8adb32a22b"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.548740241714622, 15, -1.82], "to": [-1.9087402417146222, 20, 1.82], "autouv": 0, "color": 0, "rotation": [0, 0, -12.5], "origin": [-3.7287402417146227, 15, 0], "uv_offset": [8, 0], "faces": {"north": {"uv": [10, 2, 12, 6], "texture": 3}, "east": {"uv": [8, 2, 10, 6], "texture": 3}, "south": {"uv": [10, 2, 12, 6], "texture": 3}, "west": {"uv": [12, 2, 14, 6], "texture": 3}, "up": {"uv": [12, 2, 10, 0], "texture": 3}, "down": {"uv": [14, 0, 12, 2], "texture": 3}}, "type": "cube", "uuid": "cdf2ae83-16f4-2a73-2276-f4acf23ef880"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.731259758285378, 15, -1.82], "to": [5.371259758285378, 18, 1.82], "autouv": 0, "color": 0, "rotation": [0, 0, 12.5], "origin": [3.5512597582853775, 15, 0], "uv_offset": [8, 1], "faces": {"north": {"uv": [8.75, 4, 10.75, 6], "texture": 3}, "east": {"uv": [8.25, 3.25, 10.25, 5.25], "texture": 3}, "south": {"uv": [14, 3, 16, 5], "texture": 3}, "west": {"uv": [12, 3, 14, 5], "texture": 3}, "up": {"uv": [12, 3.25, 10, 1.25], "texture": 3}, "down": {"uv": [14, 1.25, 12, 3.25], "texture": 3}}, "type": "cube", "uuid": "92f02f9e-5092-cfa6-8f32-b9e666f6c1cb"}], "outliner": [{"name": "armorHead", "origin": [0, 26, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorBody", "origin": [-1.5, 8, 1], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["cf80a5bb-306b-1cc9-18a4-db8adb32a22b", "92f02f9e-5092-cfa6-8f32-b9e666f6c1cb", "cdf2ae83-16f4-2a73-2276-f4acf23ef880"]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingRight", "origin": [6, 22, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 22, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-6, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "slim_arm", "origin": [0, 0, 0], "color": 0, "uuid": "897b59bf-9152-6b1b-c861-72222e196ec8", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\overlays\\omnitrix\\biomnitrix\\biomnitrix_sel_one_0.png", "name": "biomnitrix_sel_one_0.png", "folder": "", "namespace": "", "id": "1", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "afbae6f5-1112-afed-c3cb-548663779b23", "relative_path": "../textures/overlays/omnitrix/biomnitrix/biomnitrix_sel_one_0.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_tokustar_0.png", "name": "ov_tokustar_0.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "224c6a3d-db50-53b5-fd68-441c1734e1a8", "relative_path": "../textures/models/ben/ov_tokustar_0.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_polymorph_0.png", "name": "ov_polymorph_0.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "b3a1099f-a255-fa69-5419-6dad89c5360c", "relative_path": "../textures/models/ben/ov_polymorph_0.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_necrofriggian_polymorph_0_0.png", "name": "ov_necrofriggian_polymorph_0_0.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "85e2e081-1442-0e94-6846-84e8e016b7f3", "relative_path": "../textures/models/ben/bio/ov_necrofriggian_polymorph_0_0.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0": {"name": "armorRightArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "9231fa49-38fb-a4ab-b42f-578183b2bf27", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "7da43887-54c0-6937-c260-42eb6fd0279e", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}]}, "cf2edd07-eb3f-1d93-d994-e27c3bfc6244": {"name": "armorLeftArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "29ed238c-edc1-b162-706d-3d60755df944", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "5c6a70dd-2a96-85ad-e34a-0828ab13fde2", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}]}, "2f2d8f4b-3354-920a-7fb1-7cade178baa4": {"name": "armorRightLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "fd9d17bb-464b-afc4-20c6-8be1e0f884d5", "time": 0, "color": -1, "interpolation": "linear"}]}, "9114e3fe-4f43-960a-522b-49350c626e56": {"name": "armorLeftLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "c1d70d77-2aed-2b8e-89ee-ab5d8d3f6432", "time": 0, "color": -1, "interpolation": "linear"}]}, "024bf3ac-a147-9be0-d26c-743541c7c08c": {"name": "armorBody", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "d2950762-aeda-3e17-ebb9-64134d9a398d", "time": 0, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear"}]}}}, {"uuid": "37120998-92aa-89d6-88ba-ee0409afe6af", "name": "animation.model.new", "loop": "hold", "override": false, "length": 1.125, "snapping": 12, "selected": false, "saved": true, "path": "C:\\Users\\<USER>\\Downloads\\pyronite_magma_anim.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"21d3e5ff-5eb8-92d2-365f-a769c3597beb": {"name": "armorHead", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "8a60e6a0-b9e6-7600-89fd-e2d54ae43ee8", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 10}], "uuid": "6881681a-ef49-09c4-0d30-1fbcac8fb548", "time": 0.9167, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "31876521-951d-20c9-6cc1-e6300aa86e4e", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 1, "y": -0.5, "z": -1.25}], "uuid": "46b2b0f5-48fd-c6e1-0f8b-1cd07bc3667e", "time": 0.9167, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1, "z": 1}], "uuid": "be009e45-0397-aecc-e66e-a235e6ce7921", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0": {"name": "armorRightArm", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "811a331c-397d-0dcc-b5aa-bb1e5884ecc6", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -76.86387, "y": -17.19764, "z": 79.85664}], "uuid": "64c59604-3124-15ab-07db-87c409280a73", "time": 0.9167, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "29ff30b0-a774-fb98-f4e3-0d22d702ab1a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 2.85, "y": 0, "z": -7.25}], "uuid": "22408fa4-ceca-0875-a20c-2d1ea12bb2b1", "time": 0.9167, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1, "z": 1}], "uuid": "c4052afa-fc37-2ffa-ff30-64b2c4de8a3c", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "e9f4593b-73fb-9e93-f8b3-ede73030bf82", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 32.73241, "y": -32.79775, "z": -49.87926}], "uuid": "4ffec5d1-f4f5-2c10-f223-50fdf45dd0bb", "time": 0.9167, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "d67a5780-38ec-4039-caec-5df25260233b", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -1.6, "y": 0, "z": 4.4}], "uuid": "eb7b80f9-9938-ef09-9cfb-2409078e8f22", "time": 0.9167, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1, "z": 1}], "uuid": "c3bc63d5-d794-b396-43f8-466e2298025e", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "2f2d8f4b-3354-920a-7fb1-7cade178baa4": {"name": "armorRightLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "99ab5d94-e3ee-eada-61bf-8731f239fdd0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": -16.85985, "y": -28.68847, "z": 4.70084}], "uuid": "07fc697e-f7bd-c564-31be-f7aa66110ce8", "time": 0.9167, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "5da06059-d7a5-2c81-02a4-6ab1d9181e93", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0.25, "y": 0, "z": -1.5}], "uuid": "86d96419-694a-b9ae-03b3-5a009d26fcf4", "time": 0.9167, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1, "z": 1}], "uuid": "1befff84-5137-5615-769a-6bb1d6ad76e2", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "9114e3fe-4f43-960a-522b-49350c626e56": {"name": "armorLeftLeg", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "d9f40f09-82fd-137f-ac41-cb3b93fe90e1", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 29.29126, "y": -49.47494, "z": -27.16149}], "uuid": "392a263f-d191-7499-fd64-761f3fd0d114", "time": 0.9167, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "c42af8a8-8ffa-7793-e82c-0d026a422a3b", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -1.25, "y": 0, "z": 2.25}], "uuid": "181adc43-ea06-5d80-4ed9-32fc6f713d05", "time": 0.9167, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1, "z": 1}], "uuid": "7d2725b6-1d43-2dd3-57c6-4780cd4e67b6", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}, "024bf3ac-a147-9be0-d26c-743541c7c08c": {"name": "armorBody", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "c174f27c-9455-0c84-2e0b-9bcb7ca1d801", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 12.5, "y": -45, "z": 0}], "uuid": "33bb6905-7df4-1f3f-84b6-61d4d01289a0", "time": 0.9167, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "4ef8d304-01d9-2051-206d-eb5bcf123649", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 1, "y": 0, "z": -1}], "uuid": "f5e0ca0a-8d5a-3243-887d-88cef5f7e37a", "time": 0.9167, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1, "z": 1}], "uuid": "5664c6ec-ff37-56bf-fbfc-d18940a4be5f", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}