{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "bio_naljian_biosovortian", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Body", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 18, -2], "to": [4, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 21.5, 28, 27.5], "texture": 3}, "east": {"uv": [16, 21.5, 20, 27.5], "texture": 3}, "south": {"uv": [32, 21.5, 40, 27.5], "texture": 3}, "west": {"uv": [28, 21.5, 32, 27.5], "texture": 3}, "up": {"uv": [28, 20, 20, 16], "texture": 3}, "down": {"uv": [36, 16, 28, 20], "texture": 3}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 18, -2], "to": [4, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 37.5, 28, 43.5], "texture": 3}, "east": {"uv": [16, 37.5, 20, 43.5], "texture": 3}, "south": {"uv": [32, 37.5, 40, 43.5], "texture": 3}, "west": {"uv": [28, 37.5, 32, 43.5], "texture": 3}, "up": {"uv": [28, 36, 20, 32], "texture": 3}, "down": {"uv": [36, 32, 28, 36], "texture": 3}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 12, -2], "to": [8, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 3}, "east": {"uv": [40, 20, 44, 32], "texture": 3}, "south": {"uv": [52, 20, 56, 32], "texture": 3}, "west": {"uv": [48, 20, 52, 32], "texture": 3}, "up": {"uv": [48, 20, 44, 16], "texture": 3}, "down": {"uv": [52, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 12, -2], "to": [8, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 3}, "east": {"uv": [40, 36, 44, 48], "texture": 3}, "south": {"uv": [52, 36, 56, 48], "texture": 3}, "west": {"uv": [48, 36, 52, 48], "texture": 3}, "up": {"uv": [48, 36, 44, 32], "texture": 3}, "down": {"uv": [52, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 12, -2], "to": [-5, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 3}, "east": {"uv": [32, 52, 36, 64], "texture": 3}, "south": {"uv": [44, 52, 48, 64], "texture": 3}, "west": {"uv": [40, 52, 44, 64], "texture": 3}, "up": {"uv": [40, 52, 36, 48], "texture": 3}, "down": {"uv": [44, 48, 40, 52], "texture": 3}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 12, -2], "to": [-5, 24, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 3}, "east": {"uv": [48, 52, 52, 64], "texture": 3}, "south": {"uv": [60, 52, 64, 64], "texture": 3}, "west": {"uv": [56, 52, 60, 64], "texture": 3}, "up": {"uv": [56, 52, 52, 48], "texture": 3}, "down": {"uv": [60, 48, 56, 52], "texture": 3}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 3}, "east": {"uv": [0, 20, 4, 32], "texture": 3}, "south": {"uv": [12, 20, 16, 32], "texture": 3}, "west": {"uv": [8, 20, 12, 32], "texture": 3}, "up": {"uv": [8, 20, 4, 16], "texture": 3}, "down": {"uv": [12, 16, 8, 20], "texture": 3}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 3}, "east": {"uv": [0, 36, 4, 48], "texture": 3}, "south": {"uv": [12, 36, 16, 48], "texture": 3}, "west": {"uv": [8, 36, 12, 48], "texture": 3}, "up": {"uv": [8, 36, 4, 32], "texture": 3}, "down": {"uv": [12, 32, 8, 36], "texture": 3}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -2], "to": [-0.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 3}, "east": {"uv": [16, 52, 20, 64], "texture": 3}, "south": {"uv": [28, 52, 32, 64], "texture": 3}, "west": {"uv": [24, 52, 28, 64], "texture": 3}, "up": {"uv": [24, 52, 20, 48], "texture": 3}, "down": {"uv": [28, 48, 24, 52], "texture": 3}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -2], "to": [-0.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 3}, "east": {"uv": [0, 52, 4, 64], "texture": 3}, "south": {"uv": [12, 52, 16, 64], "texture": 3}, "west": {"uv": [8, 52, 12, 64], "texture": 3}, "up": {"uv": [8, 52, 4, 48], "texture": 3}, "down": {"uv": [12, 48, 8, 52], "texture": 3}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 12, -2], "to": [3.5, 17, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [-0.5, 0, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20.5, 27, 27.5, 32], "texture": 3}, "east": {"uv": [16.5, 27, 20.5, 32], "texture": 3}, "south": {"uv": [32.5, 27, 39.5, 32], "texture": 3}, "west": {"uv": [28.5, 27, 32.5, 32], "texture": 3}, "up": {"uv": [27, 20, 20, 16], "texture": 3}, "down": {"uv": [35, 16, 28, 20], "texture": 3}}, "type": "cube", "uuid": "5b553ddc-38ef-4302-ba2b-233246b1d4e5"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 12, -2], "to": [3.5, 17, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-0.5, 0, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20.5, 43, 27.5, 48], "texture": 3}, "east": {"uv": [16.5, 43, 20.5, 48], "texture": 3}, "south": {"uv": [32.5, 43, 39.5, 48], "texture": 3}, "west": {"uv": [28.5, 43, 32.5, 48], "texture": 3}, "up": {"uv": [27, 36, 20, 32], "texture": 3}, "down": {"uv": [35, 32, 28, 36], "texture": 3}}, "type": "cube", "uuid": "3bedcc66-1710-a91a-b043-1eb5d9ae4541"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 31.35868673504768, -3.1862023186506256], "to": [3, 35.65868673504768, 4.813797681349374], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [10, 0, 0], "origin": [0.007596123493895934, 33.2151922469878, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [0, 0, 8, 8], "texture": 3}, "east": {"uv": [0, 0, 8, 8], "texture": null}, "south": {"uv": [0, 0, 8, 8], "texture": null}, "west": {"uv": [0, 0, 8, 8], "texture": null}, "up": {"uv": [0, 0, 8, 8], "texture": null}, "down": {"uv": [0, 0, 8, 8], "texture": null}}, "type": "cube", "uuid": "0410ae68-df76-7ea5-615b-aea05512ad87"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.328874526152897, 31.186357610382796, -4], "to": [2.671125473847103, 35.4863576103828, 4], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [0, 0, 10], "origin": [0.007596123493895934, 33.2151922469878, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [0, 0, 8, 8], "texture": null}, "east": {"uv": [0, 0, 8, 8], "texture": 3}, "south": {"uv": [0, 0, 8, 8], "texture": null}, "west": {"uv": [0, 0, 8, 8], "texture": null}, "up": {"uv": [0, 0, 8, 8], "texture": null}, "down": {"uv": [0, 0, 8, 8], "texture": null}}, "type": "cube", "uuid": "fe41dbb7-267d-b9f0-32a9-46c7401e35c7"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 31.35868673504768, -4.813797681349374], "to": [3, 35.65868673504768, 3.1862023186506256], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [-10, 0, 0], "origin": [0.007596123493895934, 33.2151922469878, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [0, 0, 8, 8], "texture": null}, "east": {"uv": [0, 0, 8, 8], "texture": null}, "south": {"uv": [8, 0, 0, 8], "texture": 3}, "west": {"uv": [0, 0, 8, 8], "texture": null}, "up": {"uv": [0, 0, 8, 8], "texture": null}, "down": {"uv": [0, 0, 8, 8], "texture": null}}, "type": "cube", "uuid": "edc279b3-e9d2-c375-0172-bfe32dd0e7f9"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.6860869164663566, 31.35736768204563, -4], "to": [3.3139130835336434, 35.65736768204563, 4], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "rotation": [0, 0, -10], "origin": [0.007596123493895934, 33.2151922469878, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [0, 0, 8, 8], "texture": null}, "east": {"uv": [0, 0, 8, 8], "texture": null}, "south": {"uv": [0, 0, 8, 8], "texture": null}, "west": {"uv": [0, 0, 8, 8], "texture": 3}, "up": {"uv": [0, 0, 8, 8], "texture": null}, "down": {"uv": [0, 0, 8, 8], "texture": null}}, "type": "cube", "uuid": "0bada835-04f0-864d-e4c2-29ed37c7a05e"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 26.2, -4], "to": [3, 32.199999999999996, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 1, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 3}, "east": {"uv": [0, 8, 8, 16], "texture": 3}, "south": {"uv": [24, 8, 32, 16], "texture": 3}, "west": {"uv": [16, 8, 24, 16], "texture": 3}, "up": {"uv": [16, 8, 8, 0], "texture": 3}, "down": {"uv": [24, 0, 16, 8], "texture": 3}}, "type": "cube", "uuid": "32e4ba1d-e0d8-e6fe-90c8-a01ccff7ad12"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 26.2, -4], "to": [3, 32.199999999999996, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 1, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 3}, "east": {"uv": [32, 8, 40, 16], "texture": 3}, "south": {"uv": [56, 8, 64, 16], "texture": 3}, "west": {"uv": [48, 8, 56, 16], "texture": 3}, "up": {"uv": [48, 8, 40, 0], "texture": 3}, "down": {"uv": [56, 0, 48, 8], "texture": 3}}, "type": "cube", "uuid": "6ef19263-9329-dbd6-ddaa-84aa895edeab"}, {"name": "Shoulder pad", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-11, 24.912327711690228, -2], "to": [-9.5, 26.21232771169023, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-9, 28.912327711690228, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [27.75, 58, 32, 60.25], "texture": 3}, "east": {"uv": [27.75, 58.5, 31.5, 60.25], "texture": 3}, "south": {"uv": [27.75, 58, 32, 60.25], "texture": 3}, "west": {"uv": [27.75, 58, 31.625, 60.1875], "texture": 3}, "up": {"uv": [31.5, 60.5, 27.75, 62.75], "texture": 3}, "down": {"uv": [32, 62.5, 28, 60.25], "texture": 3}}, "type": "cube", "uuid": "64d6fe42-a40b-5ecd-028e-c4de8936901e"}, {"name": "Shoulder pad", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.5, 27, -2], "to": [-8.5, 29, 2], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [0, 0, -15], "origin": [-8, 29, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [32, 57.5, 28, 60], "texture": 3}, "east": {"uv": [32, 57.5, 28, 60], "texture": 3}, "south": {"uv": [32, 57.5, 28, 60], "texture": 3}, "west": {"uv": [32, 57.5, 28, 60], "texture": 3}, "up": {"uv": [28, 62.5, 32, 60], "texture": 3}, "down": {"uv": [36.5, 48, 40.5, 52], "texture": 3}}, "type": "cube", "uuid": "9ff848bc-987c-f830-3179-51664ed3eb3f"}, {"name": "Shoulder pad", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.5, 30, -2], "to": [-8.5, 36, 2], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [0, 0, -22.5], "origin": [-8, 29, 0], "uv_offset": [28, 57], "faces": {"north": {"uv": [28, 60, 31.75, 61.75], "texture": 3}, "east": {"uv": [32, 60, 28, 62], "texture": 3}, "south": {"uv": [36, 61, 36, 62], "texture": 3}, "west": {"uv": [31.5, 60, 28, 62], "texture": 3}, "up": {"uv": [28, 62, 32, 60], "texture": 3}, "down": {"uv": [32, 60, 28, 62], "texture": 3}}, "type": "cube", "uuid": "0237c3e5-f507-74aa-e4d9-2d0bf7602ba6"}, {"name": "Shoulder pad", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8.5, 30, -2], "to": [8.5, 36, 2], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [0, 0, 22.5], "origin": [8, 29, 0], "uv_offset": [28, 57], "faces": {"north": {"uv": [31.75, 60, 28, 61.75], "texture": 3}, "east": {"uv": [28, 60, 31.5, 62], "texture": 3}, "south": {"uv": [36, 61, 36, 62], "texture": 3}, "west": {"uv": [28, 60, 32, 62], "texture": 3}, "up": {"uv": [32, 62, 28, 60], "texture": 3}, "down": {"uv": [28, 60, 32, 62], "texture": 3}}, "type": "cube", "uuid": "abaf24ed-c746-b618-b43e-f75d273a70d0"}, {"name": "Shoulder pad", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8.5, 27, -2], "to": [9.5, 29, 2], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [0, 0, 15], "origin": [8, 29, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [28, 57.5, 32, 60], "texture": 3}, "east": {"uv": [28, 57.5, 32, 60], "texture": 3}, "south": {"uv": [28, 57.5, 32, 60], "texture": 3}, "west": {"uv": [28, 57.5, 32, 60], "texture": 3}, "up": {"uv": [32, 62.5, 28, 60], "texture": 3}, "down": {"uv": [40.5, 48, 36.5, 52], "texture": 3}}, "type": "cube", "uuid": "3f999074-3842-864a-168d-b0f559690df5"}, {"name": "Shoulder pad", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [9.5, 24.912327711690228, -2], "to": [11, 26.21232771169023, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [9, 28.912327711690228, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [32, 58, 27.75, 60.25], "texture": 3}, "east": {"uv": [31.625, 58, 27.75, 60.1875], "texture": 3}, "south": {"uv": [32, 58, 27.75, 60.25], "texture": 3}, "west": {"uv": [31.5, 58.5, 27.75, 60.25], "texture": 3}, "up": {"uv": [27.75, 60.5, 31.5, 62.75], "texture": 3}, "down": {"uv": [28, 62.5, 32, 60.25], "texture": 3}}, "type": "cube", "uuid": "bed94464-2844-e95c-3666-8963cd0157a4"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 18, -2.5], "to": [5.5, 24, 2.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1.5, 0, -0.5], "uv_offset": [16, 16], "faces": {"north": {"uv": [18.5, 21.5, 29.5, 27.5], "texture": 3}, "east": {"uv": [16, 21.5, 20, 27.5], "texture": 3}, "south": {"uv": [32, 21.5, 40, 27.5], "texture": 3}, "west": {"uv": [28, 21.5, 32, 27.5], "texture": 3}, "up": {"uv": [28, 20, 20, 16], "texture": 3}, "down": {"uv": [36, 16, 28, 20], "texture": 3}}, "type": "cube", "uuid": "ee79a2e3-b7af-7302-435b-62cba3dc1443"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 18, -2.5], "to": [5.5, 24, 2.5], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1.5, 0, -0.5], "uv_offset": [16, 32], "faces": {"north": {"uv": [18.5, 36, 29.5, 42], "texture": 3}, "east": {"uv": [16, 36, 20, 42], "texture": 3}, "south": {"uv": [32, 36, 40, 42], "texture": 3}, "west": {"uv": [28, 36, 32, 42], "texture": 3}, "up": {"uv": [28, 36, 20, 32], "texture": 3}, "down": {"uv": [36, 32, 28, 36], "texture": 3}}, "type": "cube", "uuid": "d679a108-8860-824f-ce33-5c472d81d135"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 15, -2], "to": [4.5, 17, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.5, 0, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 43.5, 28, 45.5], "texture": 3}, "east": {"uv": [16, 43.5, 20, 45.5], "texture": 3}, "south": {"uv": [32, 43.5, 40, 45.5], "texture": 3}, "west": {"uv": [28, 43.5, 32, 45.5], "texture": 3}, "up": {"uv": [27, 36, 20, 32], "texture": 3}, "down": {"uv": [35, 32, 28, 36], "texture": 3}}, "type": "cube", "uuid": "973d430b-f392-a563-5ae6-23579026a461"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 15, -2], "to": [4, 17, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 27.5, 28, 29.5], "texture": 3}, "east": {"uv": [16, 27.5, 20, 29.5], "texture": 3}, "south": {"uv": [32, 27.5, 40, 29.5], "texture": 3}, "west": {"uv": [28, 27.5, 32, 29.5], "texture": 3}, "up": {"uv": [27, 20, 20, 16], "texture": 3}, "down": {"uv": [35, 16, 28, 20], "texture": 3}}, "type": "cube", "uuid": "ba9f8406-1c78-9837-dfae-2c94a97db118"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 12, -2], "to": [3.5, 14, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.5, 0, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20.5, 29.5, 27.5, 31.5], "texture": 3}, "east": {"uv": [16.5, 29.5, 20.5, 31.5], "texture": 3}, "south": {"uv": [32.5, 29.5, 39.5, 31.5], "texture": 3}, "west": {"uv": [28.5, 29.5, 32.5, 31.5], "texture": 3}, "up": {"uv": [27, 20, 20, 16], "texture": 3}, "down": {"uv": [35, 16, 28, 20], "texture": 3}}, "type": "cube", "uuid": "df9bb2f1-1587-30ff-0fb4-7158e91a6901"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 11, -3], "to": [3.5, 15, 3], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.5, 0, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20.5, 45.5, 27.5, 47.5], "texture": 3}, "east": {"uv": [16.5, 45.5, 20.5, 47.5], "texture": 3}, "south": {"uv": [32.5, 45.5, 39.5, 47.5], "texture": 3}, "west": {"uv": [28.5, 45.5, 32.5, 47.5], "texture": 3}, "up": {"uv": [27, 36, 20, 32], "texture": 3}, "down": {"uv": [35, 32, 28, 36], "texture": 3}}, "type": "cube", "uuid": "9ed2f745-cbb9-d721-2f41-8915780213af"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6.5, 21, -2], "to": [11, 24, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48.5, 23], "texture": 3}, "east": {"uv": [40, 20, 44, 23], "texture": 3}, "south": {"uv": [52, 20, 56.5, 23], "texture": 3}, "west": {"uv": [48, 20, 52, 23], "texture": 3}, "up": {"uv": [48, 20, 44, 16], "texture": 3}, "down": {"uv": [52, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "fdb83df7-1ee1-15af-4eea-25a23b079d14"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6.5, 21, -2], "to": [11, 24, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48.5, 39], "texture": 3}, "east": {"uv": [40, 36, 44, 39], "texture": 3}, "south": {"uv": [52, 36, 56.5, 39], "texture": 3}, "west": {"uv": [48, 36, 52, 39], "texture": 3}, "up": {"uv": [48, 36, 44, 32], "texture": 3}, "down": {"uv": [52, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "77dac94f-97ff-f6b5-5079-c3c5ddaa68e7"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6.5, 9, -2], "to": [9.5, 20, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 23, 48, 32], "texture": 3}, "east": {"uv": [40, 23, 44, 32], "texture": 3}, "south": {"uv": [52, 23, 55, 32], "texture": 3}, "west": {"uv": [48, 23, 52, 32], "texture": 3}, "up": {"uv": [48, 20, 44, 16], "texture": 3}, "down": {"uv": [52, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "4b30a136-1400-6584-5638-7f642b014ac7"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6.5, 9, -2], "to": [9.5, 20, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 39, 48, 48], "texture": 3}, "east": {"uv": [40, 39, 44, 48], "texture": 3}, "south": {"uv": [52, 39, 55, 48], "texture": 3}, "west": {"uv": [48, 39, 52, 48], "texture": 3}, "up": {"uv": [48, 36, 44, 32], "texture": 3}, "down": {"uv": [52, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "61a79eb3-7fac-7ddb-b2d9-f30c1f945047"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-11.3, 21, -2], "to": [-6.800000000000001, 24, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-2, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40.5, 55], "texture": 3}, "east": {"uv": [32, 52, 36, 55], "texture": 3}, "south": {"uv": [44, 52, 48.5, 55], "texture": 3}, "west": {"uv": [40, 52, 44, 55], "texture": 3}, "up": {"uv": [40, 52, 36, 48], "texture": 3}, "down": {"uv": [44, 48, 40, 52], "texture": 3}}, "type": "cube", "uuid": "0905553d-dda6-f39d-8930-678924f810d8"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-11.3, 21, -2], "to": [-6.800000000000001, 24, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-2, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56.5, 55], "texture": 3}, "east": {"uv": [48, 52, 52, 55], "texture": 3}, "south": {"uv": [59.5, 52, 64, 55], "texture": 3}, "west": {"uv": [56, 52, 60, 55], "texture": 3}, "up": {"uv": [56, 52, 52, 48], "texture": 3}, "down": {"uv": [60, 48, 56, 52], "texture": 3}}, "type": "cube", "uuid": "fa9cf24e-ad5c-cdf5-30f0-8143b184496a"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10, 9, -2], "to": [-7, 20, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-2, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 55, 40, 64], "texture": 3}, "east": {"uv": [32, 55, 36, 64], "texture": 3}, "south": {"uv": [44, 55, 47, 64], "texture": 3}, "west": {"uv": [40, 55, 44, 64], "texture": 3}, "up": {"uv": [40, 52, 36, 48], "texture": 3}, "down": {"uv": [44, 48, 40, 52], "texture": 3}}, "type": "cube", "uuid": "c38a2adb-73bd-d866-11b9-52c3185b561b"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10, 9, -2], "to": [-7, 20, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-2, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 55, 56, 64], "texture": 3}, "east": {"uv": [48, 55, 52, 64], "texture": 3}, "south": {"uv": [60, 55, 63, 64], "texture": 3}, "west": {"uv": [56, 55, 60, 64], "texture": 3}, "up": {"uv": [56, 52, 52, 48], "texture": 3}, "down": {"uv": [60, 48, 56, 52], "texture": 3}}, "type": "cube", "uuid": "300dc3a6-a18b-a53c-16b4-86a603559d4d"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.369999904632568, 0.25, -2.5], "to": [-5.249999904632568, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [4.7077028430832684e-14, -45.00000000000001, 179.9999999999999], "origin": [-2.3499999046325684, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}}, "type": "cube", "uuid": "c3de24f0-ee4b-b3c1-76e5-41df52c7d1e6"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.9699999046325685, 0.25, -2.5], "to": [-5.849999904632568, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-1.6152119899410566e-14, -22.49999999999998, -179.9999999999999], "origin": [-2.9499999046325684, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}}, "type": "cube", "uuid": "52b809dd-0853-94b4-419d-1a4c920e66b8"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.3528360650437829, 0.8499999999999912, -2.764416854900533], "to": [-0.23283606504378368, 0.6499999999999915, 1.8355831450994669], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [49.99999999999999, 22.499999999999986, -179.9999999999999], "origin": [-0.5499999046325685, 0.75, -2.35], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}}, "type": "cube", "uuid": "65207a16-5732-84d0-8080-bbc9c1a529db"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.3528360650437829, 0.24999999999999145, -2.6644168549005327], "to": [-0.23283606504378368, 0.049999999999991385, 1.935583145099467], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [1.6152119899410566e-14, 22.49999999999998, -179.9999999999999], "origin": [-0.5499999046325685, 0.15000000000000002, -2.25], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}}, "type": "cube", "uuid": "a6dbd96a-eee4-4c8b-6258-57881e3be9f5"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.918506923135055, 0.75, -2.6834469717727103], "to": [-3.798506923135055, 0.55, 1.9165530282272893], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-45, 22.499999999999986, 0], "origin": [-4.019999904632568, 0.65, -2.5], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}}, "type": "cube", "uuid": "aa75ae1b-9c18-02b1-c401-8838d7acb8c0"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.0399999046325683, 0.25, -4.1], "to": [-2.919999904632568, 0.050000000000000044, 0.5], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, 22.5, 0], "origin": [-0.01999990463256829, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}}, "type": "cube", "uuid": "4e58f3b3-dbf8-e486-916f-8b927454e9e5"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.6399999046325675, 0.25, -2.5], "to": [-4.519999904632567, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, -22.5, 0], "origin": [-1.619999904632568, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}}, "type": "cube", "uuid": "adafc710-3343-4cb6-6cbe-849a026db247"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.239999904632568, 0.25, -2.5], "to": [-5.119999904632568, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, -45, 0], "origin": [-2.2199999046325685, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 3}}, "type": "cube", "uuid": "86950e55-84a6-120e-ea37-b5c90b3ce419"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.249999904632568, 0.25, -2.5], "to": [5.369999904632568, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [4.7077028430832684e-14, 45.00000000000001, -179.9999999999999], "origin": [2.3499999046325684, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}}, "type": "cube", "uuid": "e659f428-3391-4d9c-ccef-74585b2fb89d"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.119999904632568, 0.25, -2.5], "to": [5.239999904632568, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, 45, 0], "origin": [2.2199999046325685, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}}, "type": "cube", "uuid": "87c04480-29c1-1dfd-d32e-348668ecfa65"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.519999904632567, 0.25, -2.5], "to": [4.6399999046325675, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, 22.5, 0], "origin": [1.619999904632568, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}}, "type": "cube", "uuid": "a3b17843-9ad2-4859-e946-b80f357e02e9"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2.919999904632568, 0.25, -4.1], "to": [3.0399999046325683, 0.050000000000000044, 0.5], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, -22.5, 0], "origin": [0.01999990463256829, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}}, "type": "cube", "uuid": "e4dea276-879e-018c-6232-57df3414ca9a"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.798506923135055, 0.75, -2.6834469717727103], "to": [3.918506923135055, 0.55, 1.9165530282272893], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-45, -22.499999999999986, 0], "origin": [4.019999904632568, 0.65, -2.5], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}}, "type": "cube", "uuid": "a6822f65-50d7-401c-dd53-99b19b174ba6"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.23283606504378368, 0.24999999999999145, -2.6644168549005327], "to": [0.3528360650437829, 0.049999999999991385, 1.935583145099467], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [1.6152119899410566e-14, -22.49999999999998, 179.9999999999999], "origin": [0.5499999046325685, 0.15000000000000002, -2.25], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}}, "type": "cube", "uuid": "81b216bf-0693-2814-5ae8-417c17357740"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.23283606504378368, 0.8499999999999912, -2.764416854900533], "to": [0.3528360650437829, 0.6499999999999915, 1.8355831450994669], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [49.99999999999999, -22.499999999999986, 179.9999999999999], "origin": [0.5499999046325685, 0.75, -2.35], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}}, "type": "cube", "uuid": "47684031-32c8-1ce8-a9a3-a96119492ba9"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.849999904632568, 0.25, -2.5], "to": [5.9699999046325685, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-1.6152119899410566e-14, 22.49999999999998, 179.9999999999999], "origin": [2.9499999046325684, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 3}}, "type": "cube", "uuid": "427034b0-11d8-987f-ad35-833ab67bbb08"}], "outliner": [{"name": "armorHead", "origin": [0, 24, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["6ef19263-9329-dbd6-ddaa-84aa895edeab", "32e4ba1d-e0d8-e6fe-90c8-a01ccff7ad12", {"name": "fire", "origin": [1, 34.2, 0], "color": 0, "uuid": "7de30cc1-cafb-20a1-f9f2-c0cbe9fe2594", "export": false, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": false, "autouv": 0, "children": ["0410ae68-df76-7ea5-615b-aea05512ad87", "fe41dbb7-267d-b9f0-32a9-46c7401e35c7", "edc279b3-e9d2-c375-0172-bfe32dd0e7f9", "0bada835-04f0-864d-e4c2-29ed37c7a05e"]}, {"name": "antenna", "origin": [0, 0, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorBody", "origin": [0, 24, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "3bedcc66-1710-a91a-b043-1eb5d9ae4541", "5b553ddc-38ef-4302-ba2b-233246b1d4e5", "ee79a2e3-b7af-7302-435b-62cba3dc1443", "d679a108-8860-824f-ce33-5c472d81d135", "973d430b-f392-a563-5ae6-23579026a461", "ba9f8406-1c78-9837-dfae-2c94a97db118", "df9bb2f1-1587-30ff-0fb4-7158e91a6901", "9ed2f745-cbb9-d721-2f41-8915780213af"]}, {"name": "armorRightArm", "origin": [4, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", "61a79eb3-7fac-7ddb-b2d9-f30c1f945047", "4b30a136-1400-6584-5638-7f642b014ac7", "77dac94f-97ff-f6b5-5079-c3c5ddaa68e7", "fdb83df7-1ee1-15af-4eea-25a23b079d14", "bed94464-2844-e95c-3666-8963cd0157a4", "3f999074-3842-864a-168d-b0f559690df5", "abaf24ed-c746-b618-b43e-f75d273a70d0", {"name": "wingRight", "origin": [4, 22, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 22, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-4, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134", "300dc3a6-a18b-a53c-16b4-86a603559d4d", "c38a2adb-73bd-d866-11b9-52c3185b561b", "fa9cf24e-ad5c-cdf5-30f0-8143b184496a", "0905553d-dda6-f39d-8930-678924f810d8", "0237c3e5-f507-74aa-e4d9-2d0bf7602ba6", "9ff848bc-987c-f830-3179-51664ed3eb3f", "64d6fe42-a40b-5ecd-028e-c4de8936901e"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806", "427034b0-11d8-987f-ad35-833ab67bbb08", "47684031-32c8-1ce8-a9a3-a96119492ba9", "81b216bf-0693-2814-5ae8-417c17357740", "a6822f65-50d7-401c-dd53-99b19b174ba6", "e4dea276-879e-018c-6232-57df3414ca9a", "a3b17843-9ad2-4859-e946-b80f357e02e9", "87c04480-29c1-1dfd-d32e-348668ecfa65", "e659f428-3391-4d9c-ccef-74585b2fb89d"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb", "c3de24f0-ee4b-b3c1-76e5-41df52c7d1e6", "52b809dd-0853-94b4-419d-1a4c920e66b8", "65207a16-5732-84d0-8080-bbc9c1a529db", "a6dbd96a-eee4-4c8b-6258-57881e3be9f5", "aa75ae1b-9c18-02b1-c401-8838d7acb8c0", "4e58f3b3-dbf8-e486-916f-8b927454e9e5", "adafc710-3343-4cb6-6cbe-849a026db247", "86950e55-84a6-120e-ea37-b5c90b3ce419"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_biosovortian_head.png", "name": "ov_biosovortian_head.png", "folder": "", "namespace": "", "id": "1", "width": 128, "height": 128, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "f2cdcdf2-fad8-79ca-86ed-f9d1f25e0a44", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAAAXNSR0IArs4c6QAABVtJREFUeF7tm7FrFFEYxPcK/wBBwSaxE8HGRiGVFmKRQhAsBME+bQqDR8oQiUWwSx8QLATB4gqx0CqgjY0gdiaNoOAfYHHynX7yZbN793Zn7y5vbrYKuTdvd2Z++/ZlyfUK8Fjf2hvWTfHl08Hoo8tXV8Cz1Mt3N9d6U5t8ASaGwxMAeVMiAPLuD756AQBHmPcEAiDv/uCrFwBwhHlPIADy7g++egEAR5j3BDAAz/YHw8PDb5UpTHoPsLx8sajTpsaq9wCpSVWP6wSAukt48/rF6KPbd+7XXqUAwApE1QIATTBzfWcAVN3J4x4BtvzboRVgvgTBAOhV8HwLRM8uANAEM9cLgMwLRC9fAKAJZq7vjXuGmzffrEWf5Y1b/8qTYvvz4xNRjNsEVmlSztUk70nvIWyuLt4j/Hy1PDx397DRzdRG08R76tiJAEyCwIq0ow0AUdd1+Tb3LACwIu1cbQBoo0stNnVcEgA+WSzJV4FbZ7aL65f+wl+GoK4Ah+bD12Hx9nd/pK2aO9VE3bhZADDYWRq6/1QIHBrzv7px1GjlQDMp6xsBEIuKANjvPYQIQrkAL97GmHk7ygCg7wWiwVkBUPZfB4IXH/1nD4AHbiuBHxGGWIiXHosvry65AeDXbytBE//zLt6vtfEK4KuAF7W7uXZiVRrsLB1bEZz41Y2jE2PXt/b+PwK6LN8mncUKULUkx8eCf34alvuqa+0EgO33x//rt3/joIgQ/DNfVI1jBMCCjhCc1vLtOqcGgBXrG0TbHNpKsUgARAhSN4ddb/BS5oMBsJP4Y8ALthVgHAD2uR1+95cfKykXnjJmXo+AuC+w/RA9AG447geqAPBxsfi4CWTZA2QFwOq9h7Xf7Em5y8Z96yc+Aurm8rs05VzTGDN4uT+1v8N9H3CqV4AHa48aA3Bt5Wbx8eDdqI9fP763/uqXlX/2/IXRPHHOaRRdN+fzvadTA2CWPtqeq9cWADthFQST7mhfMcrlx/nammmjEwAtV4AqAOKmy36OZdvP5c/j3S8A2uCLa6AVwEuzx4AdVqg/EuoA8DE+3pZ+P3xFwW2lz6AVoOEKEAurumtTAIj1TJovvcp2IxcegHaxScWSwELvgFlKRHwIACQ9Aq0AICgRsSAAkPQItAKAoETEggBA0iPQCgCCEhELAgBJj0ArAAhKRCwIACQ9Aq0AICgRsSAAkPQItAKAoETEggBA0iPQCgCCEhELAgBJj0ArAAhKRCwIACQ9Aq0AICgRsSAAkPQItAKAoETEggBA0iPQCgCCEhELAgBJj0ArAAhKRCwIACQ9Aq0AICgRsSAAkPQItAKAoETEggBA0iPQCgCCEhELAgBJj0ArAAhKRCwIACQ9Aq0AICgRsSAAkPQItAKAoETEggBA0iPQCgCCEhELAgBJj0ArAAhKRCwIACQ9Aq0AICgRsSAAkPQItAKAoETEggBA0iPQCgCCEhELAgBJj0ArAAhKRCwIACQ9Aq0AICgRsSAAkPQItAKAoETEggBA0iPQCgCCEhELAgBJj0ArAAhKRCwIACQ9Aq0AICgRsSAAkPQItAKAoETEggBA0iPQCgCCEhELAgBJj0ArAAhKRCwIACQ9Aq0AICgRsSAAkPQItAKAoETEggBA0iPQCgCCEhELAgBJj0ArAAhKRCwIACQ9Aq0AICgRsSAAkPQItAKAoETEggBA0iPQCgCCEhELAgBJj0ArAAhKRCwIACQ9Aq0AICgRsSAAkPQItAKAoETEggBA0iPQCgCCEhELAgBJj0ArAAhKRCwIACQ9Aq0AICgRsSAAkPQItAKAoETEggBA0iPQCgCCEhELfwD+HTmfo/1ylAAAAABJRU5ErkJggg==", "relative_path": "../textures/models/ben/ov_biosovortian_head.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_biosovortian.png", "name": "ov_biosovortian.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "4be4c1cc-3adc-20d8-5162-d426dd47af3f", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/ov_biosovortian.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\misc\\chronosapien.png", "name": "chronosapien.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "e2fe39e7-12d6-323b-e778-93b9182931a0", "source": "data:image/png;base64,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", "relative_path": "../textures/models/misc/chronosapien.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\bio_naljian_biosovortian.png", "name": "bio_naljian_biosovortian.png", "folder": "", "namespace": "", "id": "2", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "9022e337-7be1-a75b-63d9-54c7e9ca4a80", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 512, "height": 512, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAAAXNSR0IArs4c6QAAIABJREFUeF7t3c/qJUeW2PFbUOppYwQqTFsImgFRCKPqnd0LeTDoGdSLQY8xK7+BN175McQsup9gFtrMaCG8MZLcCCHQeGh6mkENMoz+QZnG43bdVPU9N+pEZPz7aFfKjIgT33My43sj83fvg4v/EEAAAQQQQGA7Ag+2m7EJI4AAAggggMCFACgCBBBAAAEENiRAADZMuikjgAACCCBAANQAAggggAACGxIgABsm3ZQRQAABBBAgAGoAAQQQQACBDQkQgA2TbsoIIIAAAggQADWAAAIIIIDAhgQIwIZJN2UEEEAAAQQIgBpAAAEEEEBgQwIEYMOkmzICCCCAAAIEQA0ggAACCCCwIQECsGHSTRkBBBBAAAECoAYQQAABBBDYkAAB2DDppowAAggggAABUAMIIIAAAghsSIAAbJh0U0YAAQQQQIAAqAEEEEAAAQQ2JEAANky6KSOAAAIIIEAA1AACCCCAAAIbEiAAGybdlBFAAAEEECAAagABBBBAAIENCRCADZNuyggggAACCBAANYAAAggggMCGBAjAhkk3ZQQQQAABBAiAGkAAAQQQQGBDAgRgw6SbMgIIIIAAAgRADSCAAAIIILAhAQKwYdJNGQEEEEAAAQKgBhBAAAEEENiQAAHYMOmmjAACCCCAAAFQAwgggAACCGxIgABsmHRTRgABBBBAgACoAQQQQAABBDYkQAA2TLopI4AAAgggQADUAAIIIIAAAhsSIAAbJt2UEUAAAQQQIABqAAEEEEAAgQ0JEIANk27KCCCAAAIIEAA1gAACCCCAwIYECMCGSTdlBBBAAAEECIAaQAABBBBAYEMCBGDDpJsyAggggAACBEANIIAAAgggsCEBArBh0k0ZAQQQQAABAqAGEEAAAQQQ2JAAAdgw6aaMAAIIIIAAAVADCCCAAAIIbEiAAGyYdFNGAAEEEECAAKgBBBBAAAEENiRAADZMuikjgAACCCBAANQAAggggAACGxIgABsm3ZQRQAABBBAgAGoAAQQQQACBDQkQgA2TbsoIIIAAAggQADWAAAIIIIDAhgQIwIZJN2UEEEAAAQQIgBpAAAEEEEBgQwIEYMOkmzICCCCAAAIEQA0ggAACCCCwIQECsGHSTRkBBBBAAAECoAYQQAABBBDYkAAB2DDppowAAggggAABUAMIIIDAvxD41ZdfPn0WxjuPHk19j1xtPgq1LoGpi7suCr0hgMDuBFZbMFebz+71WXv+BKA2Uf0hgMC0BFZbMFebz7SFNWjgBGDQxAgLAQTOJ7DagrnafM6viLVHJABr59fsEEDgBoHjAnk8dbV3AFabn+LOESAAOX5aI4DAxAQIwNwvOU5cekOETgCGSIMgEECgBwECQAB61N0oYxKAUTIhDgQQOJ0AASAApxfdQAMSgIGSIRQEEDiXwG+efn/1d/8f/P6rmwGM/k5AJDRvvfLy1fxee/DQGnBuyQ01muQPlQ7BIIDAmQQIAAE4s95GG4sAjJYR8SCAwGkECAABOK3YBhyIAAyYFCEhgMA5BAgAATin0sYchQCMmRdRIYDACQSOAnAcMnon4Hh+63cEomf8x3iOz/yPx70DcEKRDTwEARg4OUJDAIG2BAiAHYC2FTZ27wRg7PyIDgEEGhIgAASgYXkN3zUBGD5FAkQAgVYECAABaFVbM/RLAGbIkhgRQKALgUgQsu8MlE4qeqbvGX8p0b3PJwB759/sEUDgBgECoDxWJkAAVs6uuSGAQIoAAUjh03hwAgRg8AQJDwEE+hEgAP3YG7k9AQLQnrEREEBgEwKlXywUYfHd/REhxzMECECGnrYIIIDAMwQIgHKYiQABmClbYkUAgaEJEICh0yO4AwECoCQQQACBSgQIQCWQujmFAAE4BbNBEEBgRwKlQuCZ/45V0m/OBKAfeyMjgMDiBAjA4gmefHoEYPIECh8BBMYlQADGzY3ILhcCoAoQQACBRgQIQCOwuq1CgABUwagTBBBAICYQCcE7jx65J8cYnVGJgGKrBFI3CCCAQESAAESEHD+TAAE4k7axEEBgawIEYOv0Dzd5AjBcSgSEAAKrEiAAq2Z2znkRgDnzJmoEEJiQAAGYMGkLh0wAFk6uqSGAwFgECMBY+dg9GgKwewWYPwIInEaAAJyG2kB3ECAAd0ByCgIIIFCDAAGoQVEftQgQgFok9YMAAggEBAiAEhmJAAEYKRtiQQCBpQkQgKXTO93kCMB0KRMwAgjMSoAAzJq5NeMmAGvm1awQQGBAAgRgwKRsHBIB2Dj5po4AAucSIADn8jbabQIEQIUggAACJxEgACeBNsxdBAjAXZichAACCOQJEIA8Qz3UI0AA6rHUEwIIIHCTAAFQICMRIAAjZUMsCCCAAAIInESAAJwE2jAIIIAAAgiMRIAAjJQNsSCAAAIIIHASAQJwEmjDIIAAAgggMBIBAjBSNsSCAAIIIIDASQQIwEmgDYMAAggggMBIBAjASNkQCwIIIIAAAicRIAAngTYMAggggAACIxEgACNlQywIIIAAAgicRIAAnATaMAgggAACCIxEgACMlA2xIIAAAgggcBIBAnASaMMggAACCCAwEgECMFI2xIIAAggggMBJBAjASaANgwACCCCAwEgECMBI2RALAggggAACJxEgACeBNgwCCCCAAAIjESAAI2VDLAgggAACCJxEgACcBNowCCCAAAIIjESAAIyUDbEggAACCCBwEgECcBJowyCAAAIIIDASge4C8NKPHj7tCeSLb77uOfzltQcPu+egJ4AnP321a/57zv0PY3/6j//UO4Su43/37fdb13/r+98b//bf3Mzv3/z9P1wdf/fJG1f/fv+Tz1P5efvN16+u7/c+/vSq/93vf10vvsvlkkpujeBbXwBRjAQgItT2OAEgAG0rbOzeW9//CMDY+e8dHQGwA9C1BgkAAehagJ0HJwB774B2Lj87AHYA+pYgASAAfSuw7+gEgAD0rEA7AHYAetbfhQAQgK4F2Hnw2gIQbfkfp+sdgM4F0Hl4AkAAupYgASAAXQuw8+AEwA5AzxIkAASgZ/3ZAfBXAN3vQT0vAAJAAHrWX/eLr/YFUArTOwClxOqebwfADkDdipqrt9r3P48A5sp/72gJgB2ArjVIAAhA1wLsPHhWAEoX/ON0/8v/+J9X/+u//cW/v/q37wHoXCCNhycABKBxid3ungAQgK4F2HlwAuARQM8SJAAEoGf9eQfAOwDd70E9LwACQAB61l/3iy97AWTheQcgSzDX3g6AHYBcBc3dOnv/8whg7vz3jp4A2AHoWoMEgAB0LcDOgxMAOwA9S5AAEICe9ecRgEcA3e9BPS8AAkAAetZf94svewFk4XkEkCWYa28HwA5AroLmbp29/3kEMHf+e0dPAOwAdK1BAkAAuhZg58EJgB2AniVIAAhAz/rzCMAjgO73oJ4XAAFoKwCvXi7/+tn8/vZy+faQ7+965r/32N0vvuwFkAXoEUCWYK69HQA7ALkKmrt19v7nEcDt/BOA23wIgB2ArndQAkAAuhZg58EJgB2AniVIAAhAz/rzCMAjgO73oJ4XAAEgAD3rr/vFl70AsvA8AsgSzLW3A2AHIFdBc7fO3v92fwTw08vlXz1bAf/rcvnnuSvi3OgJgB2AcyvuMBoBIABdC7Dz4AQgtwNAAHIFTAAIQK6Ckq0JAAFIltDUzQkAAehZwASAAPSsP+8AeAeg+z2o5wVAAAhAz/rrfvFlL4AsPO8AZAnm2tsBsAOQq6C5W9e+/5W+E/A3f/8PVwDfffLG1b/f/+Tz1Brx9puvP322w/c+/vSq/9ce5ARg7uz3jz6V3Brh174ASmMiAKXE6p5PAAhA3Yqaq7fa9z8CMFf+e0dLADwC6FqDBIAAdC3AzoMTADsAPUuQABCAnvXnHQDvAHS/B/W8AAgAAehZf90vvtoXQClMjwBKidU93w6AHYC6FTVXb63vf9EjAe8AzFUvtaMlAHYAatdUUX8EgAAUFcxiJxMAOwA9S5oAEICe9ecRgEcA3e9BPS8AAkAAetZf94uv9QUQwfUIICLU9rgdADsAbSts7N5b3/88Ahg7/72je9C6AHtP0PhzE4huYKPP7tPJP+HjP3qFiS9D4Ltvv+/+ITgTf7YtAcgS1L4pAQtQU7xh5/iHiJwwMQEC8KOHV9/UNHEuhb4gAQtQ36Ti35e/0dsSIAAEoG2F6T1FwAKUwpdujH8aoQ4GJkAACMDA5Sm0iEDvBWr2Z/wR3+g4/hEhx0cmQAAIwMj1KbaAgAWob4ng35e/0XMECAAByFWQ1l0JWIC64r/g35e/0XMECAAByFWQ1l0JWIC64icAffEbPUmAABCAZAlp3pPAX//2d1fD/+WrP7n6d1YQjs/4o/F6sugxdsQD/x5ZMea9BAgAAbi3Vpw3IAELUN+k4N+Xv9FzBAgAAchVkNZdCViAuuK/4N+Xv9FzBAgAAchVkNZdCViAuuInAH3xGz1JgAAQgGQJad6TQCQAx+OlsR7fKYjGK+1/9vMjHvjPnuG14ycABGDtCl98dhagvgnGvy9/o+cIEAACkKsgrbsSsAB1xR8+ArAD0Dc/Rr9NgAAQANfIxAQIQN/k4d+Xv9FzBAgAAchVkNZdCViAuuK3A9AXv9GTBAgAAUiWkOY9CRCAnvQvBKAvfqMnCRAAApAsIc17EiAAPekTgL70jZ4lQAAIQLaGtO9IgAB0hH8hAH3pGz1LgAAQgGwNTd3+548fX8X/4WefNZ1PNF7pgh79nX7tyUTjlR6PeNSOPxoP/3Pr/5jf1tdf7Xqarb9j/f/dJ79+MNscasb74CUCUJPndH1FC0LtCUXjWYDOXYCOCw7+5/InALXvMLf7IwDXfAjAufU33GjRglw74Gg8C9C5CxAB6LsDRgBq32EIQAlRAlBCa8FzowW59pSj8QgAAbhVc6WPWKJ6iuqxdf0TgNqECUAJUQJQQmvBc0e7Af7nv/3ginL2hl87Zdl4/utfvHUzpNbPgI/5PgaD/7kCRgBqX6EEoIQoASihteC5BKAsqQTgd0WCVkY3Pnt1/q0FMCa89hneAbjOLwFYu97D2RGAEFHRghctUHYAyngfz474RsdH508AcvURtSYABCCqka2OE4CydEcLTHR89AXIIwCPAMquiLnOJgAEYK6KbRxtbwEYfcGJ8EcLfnT8KAStPwEe843/9TsZZ/OP6qt1PNH4sx+P3nnxPQC+B2D2Gk/FTwBS+C7RAh8dJwB7849mTwAiQrePE4DbfLwDkKuv6VsTgFwKowU+Ok4A9uYfzZ4ARIQIQIYQAcjQW6AtAcglMVrgo+MEYG/+0ewJQESIAGQIEYAMvQXafvHN11ez+MWTnzWd1S8//uhm/3/+Zz++Oh59kcvxeNPgn9N5tMAfjx95H7vEvyyDWf5H3q0X3GhL+jj71vGU0Z7v7Ii3dwC8AzBfVVeMmADkYGYXIALQlz8ByPEfvTUBuJ0hOwCjV3Dj+AhADjABuP5ioBzN8tZZ/gSgnPlMLQgAAZipXk+PlQDkkGcXIDsAffkTgBz/0VsTAAIweo3ejC/7jPu4QB0Hi55Jt4Y32zP/iEdtIYjGyx7fnX+0QBzfWfng91/dRP7WKy9fHa/9Tod3Am5XfJTPY2vvAHgHIHsPbdqeAPzkim+WR9NkXS7F3wtAwOpmpFTAogWDANTNT+veonwSgGsC3gFoXZHJ/rMLnh2AZAIKm5cuQASgEHBwein/aMEgAHXz07q3KJ8EgAC0rsGq/RMAOwBVC+rQmUcAj2/iJQAtq69+3wSgjOmD3zz9/mlZk7ZnH5+xRZ9g20bTvvfsAp+NsDff7HfTZ+dfu330CfTsL/6J5rc7/+h7KY78St8BiPhHx6Pxel+/Ufytj0f3z+M7Gcd4Xnvw8EHrGEfunwB0zk5UwK3D630D2X0B6v1S1+78CUDrO0zb/qP7JwG4zZ8AtK3PsPeogMMOkicQgCTAQ3M7AHV5lvZWyp8AlBIe6/zo/kkACMBYFXuIJirg1sETgLqESxcgOwB9+ROAuvzP7i26fxKAyQUgKqjeC1gUX3Q8KuCoffb48Zl0tr/S9tFLVtGCWjpe6/OjeFv/nXjp/HbnX8oreiYfLTi1x4v62/3+GOXDOwCDvwSowCMCueMEIMfv2JoA1OVZ2lsp/9L+CUApsdz52Q9IBMAOQK4CG7fOFng2PAKQJXjdvnQBqv1NcaWzsQNQRowAlPHKnp29PxIAApCtwabtswWeDY4AZAkSgLoEc72VCljpaASglFju/Oz9kQAEAvDSZF8FHBXEaM+8om96O34RS+5yKW8dxVfeY90W0RfV1B0t39ux/kbnG814N/6tr8fSeoiEI8pfdLz3/TK6n0fxR8cJAAGIaqTp8eiCb33DiSYXxRe1b318twWoNc/S/nfj3/p6LL3eCEBpxV6fTwAIQK6Ckq2jC771DScKP4ovat/6+G4LUGuepf3vxr/19Vh6vRGA0oolACXEpvsxoGjLqPeW1hF+dMG3vuFExRDFF7VvfXy3Bag1z9L+d+Pf+nosvd4IQGnFEoASYst9E2AkCEc40RZRCcznnRvdUErjzcZz9vxL451twYnmN9s7AbvxP843WqCjBTm6n0T3g6ieouOl95Mo3mi87PGI57H/2h/wvvv2e78FkE1izfbZHwMa7QKILvjSeGuy/kNfvW8Ax/nstgDVzme2v934E4CXsyWTak8AUvjSje0AvNL2AiAAZTW62wJURqf92bvxJwBt739RxRKAiFDb4wSAALStsMLed1uACvE0P303/gSAADS/qAYeYHgBiNjVfiYUjVd6PPtMsXS80vPPfkYd7YhEP09bOr/e5x+/aCn68Z+oXrLzwf+zK4QR74hXlI+o/6j98Xj0iXn0+2HpfI/nR49MSx9p+i0AvwWQrcmb7aMbQHRBNw3ucrkQgLaECUBbvlHvEf/o+iQAEeFzjxOAurztANTl+YPeohsMAbhGZgfg66YVGS1ou/GPrs+IV5SsqP+ovR2AawIEoLRibp9PAOryJAABz+iGutsCdMRVe8E49o+/RwCNb3lNuycAdfEO/0VAUcKzOEqfGZWOF91wS/urff5xwY2eUWfHP4537C/6dbrS8bPPRLP1d6yv6Nf/8C/7xBfVQ8Q/4n0UsmjH7jje2dd/ab22vv9F+Yl4Httnr+djf9t/D8DoPwZUWtBRwR2Pt74Azr4BlM6fANwmlq2/aAE6jh4tSKX5PZ5PwH52hSTiTQCyFXe7PQFoyzfq3Q5A5z8DjBLU+jgBIADPEthtB4YA+DPA1vfYkfsnAASg6BNRtph9Ar3+BGoHoK+AEQACkL2nzdx++JcAaz/zOTtZ0UtdpVtgteMv3aLOjn/8hBn1F/HpXR/RI4LSR0zROwIRr+g4/hGh3JZ1ab5z0Vwu2a9Oz45/dvvj9Zbl7XsABv8egN43+GyBE4Brghag2xVFAMquuNoCFo0eCWl2QYrGPx4nALkdDAJAAEqvuaLzCQABKCkYAlBC63IhAF9dAZv9A1OUfTsAEaGy4x4BlPEqPpsAEICSoiEAJbQIgB0AOwBlV8z12cMLQGZyz2t79hZdFH+0pRi1zx4fjcfsW5y1P6Fk85ttP9sC05p/dL2efT1F8UT5Pzve6PqO4j0ez8bvEcDgjwBKCyI6P1swUf+lx7MXcOl4tS+g7PhRewtQRKjtcfyv+UbX69n3lyieqDrOjpcARBk597gdgHN5/2C07AWcDb/3DSCK3wIUEWp7HH8C0LLCsve/7P3LDoAdgJb1HfadvQDCAYITshdQdvyovQUoItT2OP4EoGWFZe9/2fsXASAALes77Dt7AYQDTC4Ao3+VcsQ/egk0at/7OP4EoGUNZu9/BCCXHY8AcvzSrbMXQDaA7AWUHT9qbwGKCLU9jj8BaFlh2ftf9v5lB8AOQMv6DvvOXgDhAHYAsohS7e0ApPClG9fmH12v2QWpdMJRPFF/Z8d7jKd3/ASAAETXSNPj2QsgG1zvG0AUv0+gEaG2x/G3A9CywrL3v+z9iwAMJgCjGWLL4v9D39kLIBtf9gLKjh+1twBFhNoex39sAcjeL3tf/6X3v9rxEgAC0PYOGvReegHUDrb2BVU7PgtQbaJl/eFPAMoqpuzs0vtf7fsVASAAZRVb+ezSC6Dy8JfaF1Tt+CxAtYmW9Yc/ASirmLKzS+9/te9XBIAAlFVs5bNLL4DKwxOA2kAP/dV+Ca1xuD/ongAQgJY1V3r/IwB1szHcnwFmn2kd29cumLr4vQMQ/fjNh599Vhv5qf39/PHjm+OV/jxy7eDx/+gm0tIFqvX9590nb9yM972PP706HsXf+/5YGl/p/KPrxQ6AHYCoRpoejy6ApoNfLt13ACxAtxeg1vnHnwC0rrFb/Uf3v6OgEIC62bIDUJdncW/RBVDcYWGD3p8ALEAEoLBki07P7sBkr8/a11fpAhjFXzu+ouTc8VdQBKCUaNn5BKCMV/Wzowu0+oCHDnvfAAgAAWhZ4wTgq5t4e1//0f2PALS8Oi6X4QQgKogsjt4FH8V/9vyjBTiKt/Xx1d8B6D2/aIHsHV+2vqL5Hd/BOPv6i+Z3/MT//iefP7jV5u03X3/67PHonYDe98Mj72jBz87/yM47AIO9AzDaBRhdoLWPnz1/AlA7g9f9RQtQ7wV29Piy2YnmRwBeziJOtScAKXzpxnYA0gjrdkAArnn2XiCz2Y0WoN7zGz2+1vwJAAHI1tjM7QnAYNkjAATgzJIkANfvYJx9/UW59gjg+s8ePQKIKqbseHcBiC647DOq1v2X4Y7PjuKNe7h9xpHnaI8Aen8izvKN2h8X3N7zHS2eiF/2+HG+o+0AlC74EY/onYCo/dnHz56/dwA6vwMQLXgEoO4lSADq8iztbbQFd7R4SnmWnk8Arr8oqJRf6/MJQGvC1/3bAXil7zOwY7ojIcqWBwHIEsy1H23BHS2eHN24NQEgAM9WiR0AOwDxXePEMwjA3F/9G5XKaAvuaPFE/LLHCQABIAD/n8BwOwDZLf/oBhH92UnUfrXj3gE4N6OjLbijxdM6G5EA1N6RK72fnb0F3pp3af9nz98OwGA7AKUXTGmBEYBrYgSgtIJy54+24I4WT45u3JoA2AGwA2AH4I8EWgtHfEvqewYBOJf/aAvuaPG0zgYBIAAEgAAQgH8hQABaLznX/Y+24I4WT+tsEAACQACeEYDWF5z+EUAAAQSeT+A3h0ewx0eU7zx6dPO7/3FFIENAcWXoaYsAAggkCBCABDxN0wQIQBqhDhBAAIEXI0AAXoybVnUIEIA6HPWCAAIIFBMgAMXINKhIgABUhKkrBBBAoIQAASih5dzaBAhAbaL6QwABBO4kQADuBOW0JgQIQBOsOkUAAQRiAgQgZuSMdgQIQDu2ekYAAQRuEiAACqQnAQLQk76xEUBgaAJPP/wPL10F+PX//vGz/37wn379VWYCBCBDT9ssAQKQJag9AggsS4AALJtaE7tcLgRAGSCAAAJ/ggABUBorEyAAK2fX3BBAIEWAAKTwaTw4AQIweIKEhwAC6xLwDsC6uZ1hZgRghiyJEQEEliRAAJZM6zSTIgDTpEqgCCCwGgECsFpG55oPAZgrX6JFAIGFCBCAhZI54VQIwIRJEzICCKxBgACskcdZZ0EAZs2cuBFAYHoCBGD6FE49AQIwdfoEjwACMxMgADNnb/7YCcD8OTQDBBCYlAABmDRxi4RNABZJpGkggMB8BAjAfDlbKWICsFI2zQUBBKYiQACmStdywRKA5VJqQgggMAsBAjBLptaMkwCsmVezQgCBCQgQgAmStHCIBGDh5JoaAgiMTYAAjJ2f1aMjAKtn2PwQQGBYAgRg2NRsERgB2CLNJokAAiMSIAAjZmWfmAjAPrk2UwQQGIwAARgsIZuFQwA2S7jpIoDAOAQIwDi52DESArBj1s0ZAQSGIEAAhkjDtkEQgG1Tb+IIINCbAAHonYG9xycAe+ff7BFAoCMBAtARvqEvBEARIIDAtgR+9eWXT3tO/q1XXr4a/oPff3X173cePXKPTiTomN8j79cePNya79aTT9SVpgggsAABArBAEm9MgQDczi8BWLv+zQ4BBAoWiLNh2QFoS5wAEIC2FaZ3BBCYloAdgGlTd1fgBIAA3FUoTkIAgf0IRC/h1SZy/MR/7N87AHWJEwACULei9IYAAssQIADLpPK5EyEABGDtCjc7BBB4YQIE4IXRTdGQABCAKQpVkAggcD4BAnA+8zNHJAAE4Mx6MxYCCExE4D+++e+uvgfglx9/dBX98Zl86dSOz/x/8eRnN7v48LPPbh7/7tvv/eXWDUIv/ejhze91+Pnjx1et/+6TX2/Nc+vJl17MzkcAgbUIEIC18kkAyvJJAMp4ORsBBBYiQAAWSublciEAZfkkAGW8nI0AAgsRIAALJZMAFCeTABQj0wABBFYhcHwJ8PiMPnomH3E4PnM+vmMQtY/eQdj9twKiL3KKvnfBbwFEFeg4AgggsCgBAjB3YglALn92AHL8tEYAgYkJEICJk3e5XAhALn8EIMdPawQQmJgAAZg4eQQgnTwCkEaoAwQQmJXAUQCO84iewUfzjp5BH9uXvoOw+/cClL71f3wHwzsAUQU7jgACCCxKgADMnVgCkMufHYAcP60RQGBiAgRg4uS9wJ/92QG4zjcBmLv+RY8AAgkCBCABb4CmdgBySSAAOX5aI4DAxASOXwR0nErt7wGIUGV/i2D17wWI3vo/8o1+i8FvAUQV6TgCCCCwKAECMFdiCUDdfNkBqMtTbwggMBEBAjBRsu74sz87AGX5JABlvJyNAAILESAAcyXTDkDdfBGAujz1hgACExGY/SXACPXs3xMQveQXzT/6LQbfAxARdBwBBBBYlAABGDuxBKBtfuwAtOWrdwQQGJgAARg4OXf8nX8UvR2A24QIQFRBjiOAwLIECMArjj2dAAATC0lEQVTYqbUD0DY/BKAtX70jgMDABEZ7CfCIKvs9BN4BeHyz+nwPwMAXp9AQQACBlgQIQEu6+b5r7wAcIyIA+RzpAQEEEJiSAAEYO20EoG1+PAJoy1fvCCAwMAECMHByGrwEaAfgmgABGLv+RYcAAggggEATAgSgCVadIoAAAgggMDYBAjB2fkSHAAIIIIBAEwIEoAlWnSKAAAIIIDA2AQIwdn5EhwACCCCAQBMCBKAJVp0igAACCCAwNgECMHZ+RIcAAggggEATAgSgCVadIoAAAgggMDYBAjB2fkSHAAIIIIBAEwIEoAlWnSKAAAIIIDA2AQIwdn5EhwACCCCAQBMCBKAJVp0igAACCCAwNgECMHZ+RIcAAggggEATAgSgCVadIoAAAgggMDYBAjB2fkSHAAIIIIBAEwIEoAlWnSKAAAIIIDA2AQIwdn5EhwACCCCAQBMCBKAJVp0igAACCCAwNgECMHZ+RIcAAggggEATAgSgCVadIoAAAgggMDYBAjB2fkSHAAIIIIBAEwIEoAlWnSKAAAIIIDA2AQIwdn5EhwACCCCAQBMCBKAJVp0igAACCCAwNgECMHZ+RIcAAggggEATAgSgCVadIoAAAgggMDYBAjB2fkSHAAIIIIBAEwIEoAlWnSKAAAIIIDA2AQIwdn5EhwACCCCAQBMCBKAJVp0igAACCCAwNgECMHZ+RIcAAggggEATAgSgCVadIoAAAgggMDYBAjB2fkSHAAIIIIBAEwIEoAlWnSKAAAIIIDA2AQIwdn5EhwACCCCAQBMCBKAJVp0igAACCCAwNgECMHZ+RIcAAggggEATAgSgCVadIoAAAgggMDYBAjB2fkSHAAIIIIBAEwIEoAlWnSKAAAIIIDA2AQIwdn5EhwACCCCAQBMCBKAJVp0igAACCCAwNgECMHZ+RIcAAggggEATAgSgCVadIoAAAgggMDYBAjB2fkSHAAIIIIBAEwIEoAlWnSKAAAIIIDA2AQIwdn5EhwACCCCAQBMCBKAJVp0igAACCCAwNgECMHZ+RIcAAggggEATAgSgCVadIoAAAgggMDYBAjB2fkSHAAIIIIBAEwIEoAlWnSKAAAIIIDA2AQIwdn5EhwACCCCAQBMCBKAJVp0igAACCCAwNgECMHZ+RIcAAggggEATAgSgCVadIoAAAgggMDYBAjB2fkSHAAIIIIBAEwIEoAlWnSKAAAIIIDA2AQIwdn5EhwACCCCAQBMCBKAJVp0igAACCCAwNgECMHZ+RIcAAggggEATAgSgCVadIoAAAgggMDYBAjB2fkSHAAIIIIBAEwIEoAlWnSKAAAIIIDA2AQIwdn6Wj+6lHz182nOSX3zzdc/hL689eLj1Nfjkp6/ezP+n//hPTfNzzP+7T964Gu/9Tz5P5eftN1+/mt97H3961f/u+W+a3Mvl8qsvv7zi/9YrL+P/DIFUcbdOnv7XJ0AACMCtKicA698DWs6QANymSwBaVp++QwIEgAAQgPAyccILEiAABOAFS0ezMwgQAAJAAM640vYcgwAQgD0rf5JZEwACUFKqtR8JeAeghP585xIAAjBf1W4UMQEgACXlTgBKaDmXABAAV8HABAgAASgpTwJQQsu5BIAAuAoGJkAACEBJeRKAElrOJQAEwFUwMAECQAAy5ZkVgrPfAfirv/3vV9N959Ejf4mVKYCgLQEgAA3LS9dZAgSAAGRqiABk6K3flgAQgPWrfOIZEgACkClfApCht35bAkAA1q/yiWdIAAhApnwJQIbe+m0JAAFYv8onniEBIACZ8iUAGXrrtyUABGD9Kp94hgSAAGTKlwBk6K3flgAQgPWrfOIZEgACkClfApCht35bAkAA1q/yiWdIAAhApnwJQIbe+m0JAAFYv8onniEBIACZ8iUAGXrrtyUABGD9Kp94hgSAAGTKlwBk6K3flgAQgPWrfOIZEgACkClfApCht35bAkAA1q/yiWdIAAhApnwJQIbe+m0JAAFYv8onniEBIACZ8iUAGXrrtyUABGD9Kp94hgSAAGTKlwBk6K3flgAQgPWrfOIZEgACkClfApCht35bAkAA1q/yiWdIAAhApnwJQIbe+m0JAAFYv8onniEBIAAl5Ztd8I9jffHN11f/690nb1z9+/1PPn9QEt/x3LfffP3ps//vvY8/vTrltQd75z/D9p62BIAA3FMnzulEgADsvQA8+emrVwtkVIYEICLk+LMECAABcEUMTIAAEICS8iQAJbScSwAIgKtgYAIEgACUlCcBKKHlXAJAAFwFAxMgAATgVnnWXvCPY3kHYOCbQ4XQCAABqFBGumhFgAAQAALQ6urSLwEgAK6CgQkQAAJAAAa+QCcPjQAQgMlLeO3wCQABIABrX+M9Z0cACEDP+ht+7N4L8PCABIjAwgS++/b71PccjI4mEoAPfv9V1ym89crLV+Mf43nn0aOm+WnaeVeyBr+LAAG4C5OTEFiSAAEgAEsWtkndR4AA3MfJWQisSIAAEIAV69qc7iRAAO4E5TQEFiRAAAjAgmVtSvcSIAD3knIeAusR2F0Ajhlt/U7A8Zl/NL53ANa75oaaEQEYKh2CQeBUAgTgGjcBOLX8DNabAAHonQHjI9CPAAEgAP2qz8jdCRCA7ikQAALdCBAAAtCt+AzcnwAB6J8DESDQi8BqAhDdz37++PFN1L/8+KOr49lHAsdn/r948rOb43/42Wc3j9fOl+8B6HXlDTJudMEMEqYwEECgAYHaC0qDEIu6jO5nBOAaJwEoKq/1To4umPVmbEYIIPD/CBCA61qwA+Da2IoAAdgq3SaLwBUBAkAAXBIbEyAAGyff1LcnMLsAHL/r/5jQ6O/uj+cfn9FHz+SjAjo+cjjuMETto3cQst8T4BFAlIHFjxOAxRNsegjcIEAAruEQAJfLVgQIwFbpNlkElnoEYAcg92uBdgA2vyEQgM0LwPS3JmAHwA7A1hfA7pMnALtXgPnvTGB2AYjuX62fwUe10/odhGz+7ABEGVz8eHQBLT5900NgawLZBaQ3vOj+RQBuZ4gA9K7gzuNHF1Dn8AyPAAINCRCA23Cjt/Cj1NgBiAg53pUAAeiK3+AIdCVAAAhA1wI0eF8CBKAvf6OfSyD6Ktjs331Hs+k9/jG+2QQgeuv/OL/S7+I/ts/WQ5Tv43jZbyIs/V4AjwCiK3bx4wRg8QSb3hWB6IacveFHuHuPTwBu/xgPAYgq2PGlCBCApdJpMgGB3gtw7/EJAAF4tgbsAGx+yyQAmxfAZtPvvQD3Hp8AEAACsNlN79Z0CYBi2IlA7wW49/izCUD2/rT6nwFG1270jocdgIjg4sezF9jieExvMQK9F+De4xOAj4oqevQ/A4wmQwAiQpsfJwCbF8Bm0++9APcenwAQAI8ANrvpeQQg4Qj8XwK9F+De4xMAAkAA3A3/SMAOgGJYmUC04B7n3vvPAM+OJ9oi7l0b2ftTaf5r8+89fpRf7wD0rvDO42cvsM7hGx6BmwRKb8AEYKyCyt6fSvNPAMbKv2gaE8heYI3D0z0CKQKlCwABSOGu3jh7fyrNPwGonkIdjkwge4GNPDexIVC6ABCAsWome38qzT8BGCv/omlMIHuBNQ5P9wikCJQuAAQghVvjyQh4B2CyhNUOlwDUJqq/kQgQgNvZiF4SGymXYqlPgADUZzpVjwRgqnQJtpAAASAAhSWz1ekEYKt0/3CyBGDzAlh8+gSAACxe4qnpEYAUvvkbE4Cxc/jXv/1dKsC/fPUnqfazNS5d8KP5lf4+e/b356N4ar+j4BFARHzt4wRg7fyGsyMAIaKuJxCAMvwEoIwXASjjtdrZBGC1jBbOhwAUAjv5dAJQBpwAlPEiAGW8VjubAKyW0cL5EIBCYCefTgDKgBOAMl4EoIzXamcTgNUyWjif1QXguIAen9EW4qp+evbnRrMBzf6OQCRItfMd5evs8bL5IwDZK2ju9gRg7vyloycAaYSpDqIFJdX5HY2zC8gdQzQ9hQDkXvIkAE3Lc/jOCcDwKWobIAFoyzfqnQBEhG4fJwAEIFdBe7cmAHvn/0IA+hYAAcjxJwAEIFdBe7cmAHvnf3oBiBaAY3prP6PNlg8ByH3Cj/jXzneUr7PHi+YfPeLxCCAiuPZxArB2fsPZzb4DQADCFN88IVogcr3nW5fmt7XwEYB8TvUwDgECME4uukRCALpg/+Og0YLSOjoCUEY4ypcdgDKezu5LgAD05d99dALQNwXRgtI6OgJQRjjKFwEo4+nsvgQIQF/+3Uf/zdPvn5YE8ed/9uOS06ufG20Jj76gVQcyeYdRPrPTK12QW9f3F998XTSlSDiKOnvOye88emQNyEKcuL3kT5y8GqETgBoU9fGiBAjAbXIE4EUrS7t7CBCAeygtfA4BWDi5E0yNABCACcp02RAJwLKpvW9iBOA+Ts5qQ4AAEIA2laXXewgQgHsoLXxO6UuApc8wI3SlW5ylz/j9FsDtDJQ+I4/yWXq8df5L44nqO4o34tn6HYNSofIOQGmFrHU+AVgrn8WzIQDFyKo2iBaUqoM9p7NowWo9fun8SwWwNH4CUErM+TMTIAAzZ69C7ASgAsREF6ULYGKo5zYlANdYCEDtCtPfyAQIwMjZOSE2AnAC5BtDEICvihJgB+A2Lo8Aispp+5MJwOYlEL0E2PqZZRZ/6Q2v9yfe43x7C0DrBTWb32P7KN+185ut/2hHoZRPVC+l+fRbAKUZWOt8ArBWPotnQwCKkVVtEN3Qqw72nM5KF4zW8UT9E4DbOyal+SQAUcWtfZwArJ3fcHYEIETU9AQCUIaXABCAsopx9i0CBGDz+iAAfQuAAJTxJwAEoKxinE0A1MCfJBC9BFj7GebZz8BLt0R3K5XW+Y14lgrQ2fk88oniPb6DkH2HIOJ3PB4J0vF83wNQSnit8+0ArJXP4tkQgGJkSzUgALfTSQCWKneTORAgAJuXBAHYuwAIAAHY+wrYe/YEYO/8XwjA3gVAAAjA3lfA3rMnAHvn/zL7S4BR+vwWwG1CZz9Tj/IVHe+dz9J3AKL5ZI8f4ynNpz8DzGZg7vYEYO78paMnAGmEqQ6iBSXV+R2NSxeMO7psegoBuMZLAJqW2/KdE4DlU3x7ggSgbwEQgDL+BIAAlFWMs28RIACb1wcB6FsABKCMPwEgAGUV42wCoAb+JIFIAM5G13pBrP1d8Vk+recbxdebR3b+Z8cfxTtaPFH+fQ9ARGjt43YA1s5vODsCECJqekK0oDQd/HK5nL1gHeeTnf/Z8UfxjhZPVD8EICK09nECsHZ+w9kRgBBR0xOiBaXp4ASgGG+ULwJQjFSDjgQIQEf4IwxNAPpmIVpQWkd39oJlB6BuRrP1Ywegbj5m640AzJaxyvESgMpAC7vL3sALh/vB6QSgjGCUr7N5RvFEsyMAEaG1jxOAtfMbzo4AhIianpC9gWeDO3vBsgOQzdh1+2z9EIC6+ZitNwIwW8Yqx0sAKgMt7C57Ay8czg5AEliUr7OFKoonmi4BiAitfZwArJ3fcHYEIETU9ITsDTwb3NkLlh2AbMbsANQluHdvBGDv/Ie/BdAbT3aB7L3ARfyy84v6j4735lM6/9HjHT2+Yz3YAYiukLWPE4C18xvObrQdgNk/IYbADyeULoCl/Ufnz7ZgjR7v6PERgOiK2Os4Adgr3z+YLQHoWwAE4KuiBIy+wI4eHwEoKrflTyYAy6f49gQJQN8CIAAEoGYFltaTRwA16c/XFwGYL2dVIyYAVXEWd1Z6wy4eIGgw+ifWY3zvPnnj5oze+/jTqoiy+anNt3T+UfwEoGq5TNcZAZguZXUDJgB1eZb2Ft2gS/srPb/2AlU6fjR/AnBNlACUVpjzbxEgAJvXBwHoWwDRAtg6OgJwm3A2P7X5EoDWV8Re/ROAvfL9g9kSgL4FkF1gstHXXqBK44nmbwfADkBpTTn/fgIE4H5WS545mgBEC0I2Cb0XvCj+3eZ/nG+04L//yec371lvv/n602cZl74TMBr/4yf+7PyP8/MOQHRFrn2cAKyd33B2BCBEdOoJoy1ArSdPAG4TJgCtK3Dv/gnA3vkf7psAd1sAj+W32/wJAAHY/BbcdfoEoCv+/oPbAeifg2cjIAAvXyWk9ifgKNuj8a89f48AogrY6zgB2CvfP5htbwGIbrjZZ/at+69dPlG82fGyPLPjR+1LF7yov9J3AnrzP3v+rz14aA2Iimjh45K/cHLvmRoBuP7EeQ+zluf0XoBazu2evs9eAEd7BHP2/AnAPVW57jkEYN3c3jUzAkAA7iqUk046ewEkAHYATirtIYchAEOm5bygCAABOK/a4pEIwPVXHUd/9hcRjR6B2AGICK59nACsnd9wdqMJQOtn1NFb5yEwJzQl0FsAau8IlNbz2fMnAE3LefjOCcDwKWobIAEYawegbbbH7/3sBTAikn0ngwBEhB3vSYAA9KQ/wNgEgAAMUIZ/DIEAeAQwUj2uHgsBWD3DwfwIAAEY6RIgAARgpHpcPZb/A9y1p8MVOM6kAAAAAElFTkSuQmCC"}, {"name": "selection", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": false, "blend_mode": "default", "width": 256, "height": 128, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 128, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [128, 288], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 192, "height": 48, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMAAAAAwCAYAAABHTnUeAAAAAXNSR0IArs4c6QAAAtFJREFUeF7tmjFO3EAUhndzgSgSyiWQtkApaLZDqelSptkiEhUniDgBFRIFDeV2qRHdNilQipVyiQgJcQFAsjIUo7X97Ddjj//9aCg8Hr/3/e/zsCvm6/X6dTahn8ViUXS12+226PpSF1dCHh7mcwRIOxKeMNJWMsxuCDAM5/enlAC8qWUEGHggZrOZhzknQOK8PGEkLmWQ7Up4IXmY750Ajy/PjYNx8OGja3A8YbgePNLNCDAweC9wBEgbmDePFNV4XjryJ0DbwLcF0PVE8ITRVkuJ1xFg4FS6AkeAvAF1zSNHNZ6XjuwJUDf4m8fzxgyWB5c7r1tPAk8YOYYj954IkJtwtL8VOAIME4w1j5zVeF468ifAydGyYv/z7kunDC6+PlTr7/9sqt+cALvxIUCnsfIvtgIPJwAC+Jk37WDNI2cVrhMgZ2Fj7L35+7v636Yw+HENbSdBePPH94WTYHl4PB+jL56Zh4BcmAiQZ1BUd907AUKQ8UlQ9+YP6zkBNBVAgP+5IoDmgLd1JSfAza/b6jPA2bdVY+9dT4Cr9U213+r0uxyztiFRvi4XJgIoj2v63vZOgL7fAnECpB++EnZEgCiFus8CCFDCuKavQU6AgCj+U6jtzR+jDSIw+OmHrqQdEaAmDQQoaUzz1SIvQIzu36frRpqfn37svM63P/mGcMydESCijwBjjuPwz5YVIP4s0Bctb/6+5KZxHwK05IQA0xjkvlXKCxCDCd8O1QFj4PuO0jTvQ4AoNwSY5iD3rXrvBOgLivs0CSCAZq50ZSSAAEZQLNMkgACaudKVkQACGEGxTJMAAmjmSldGAghgBMUyTQIIoJkrXRkJIIARFMs0CSCAZq50ZSSAAEZQLNMkgACaudKVkQACGEGxTJMAAmjmSldGAghgBMUyTQIIoJkrXRkJIIARFMs0CSCAZq50ZSSAAEZQLNMkgACaudKVkcAbDnsX+BhknvAAAAAASUVORK5CYII="}], "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/bio/bio_naljian_biosovortian.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\overlays\\glow\\bio_naljian_biosovortian_glow.png", "name": "bio_naljian_biosovortian_glow.png", "folder": "", "namespace": "", "id": "2", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "f59c6a8e-c43c-55ea-936c-ec148479ab9f", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": false, "blend_mode": "default", "width": 512, "height": 512, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAAAXNSR0IArs4c6QAAIABJREFUeF7t3b+vb9l51/Fz0YxNhBLLCYobN6NRZE2QXIALLBFPQwNu6FzSpEPiT0D8CQiJLg2lOxpDQzNO4RQjCktkNLKupnFjC8dyjBQ8M+KgmfE15+x7zvfZa6/fa71c2d5rPT/ez7PX8/nu777f8+LOfxBAAAEEEEBgOwIvtsv4RML39/f3L168eJLNrWuvTJ9ZcyIMSxIJnOF+Zk2iW8sRQACBKQkQAIOVrfSAKmXvqp2r+54rS2l7g5VfOAgggEAzAgRAM9RfOJplgJ2J8we//NUPv/vVr3znKsIzPiLbKTGU8BfF4zoCCCAwCwEC4GKlzgyTM2vOuH/OzlX7V/edidUaBBBAAIE5CBAAhzrlfv8/StkN+VEqIQ4EEEBgTAIEwJh1EVVFAsRRRbhMI4DANAQIgIKlOjtYrjzSP2u7YDp3Kd+vn/XbOo8aOZzN1ToEEEBgZAIEQKPqtB58jdLiBgEEEEBgUgIEwBOFOzOsz6wp3RM9fJbOIdXe2ZzPrkv1bz0CCCCwKgECYKJ/mne1CUsNxzN2zqy5mseVfU/FM1qMV/KyBwEEEMglQACcJDjy0Bg5tpN4LUMAAQQQaEyAAGgMfER3BMSIVRETAgggUJcAAVCXL+sIIIAAAggMSYAAuFCWnE/MOXsvhPrklhFiKJULOwgggAAC1wgQAE9wOzsgR/g35iVjKGnrWjvW37VDjvUp8oAAAisQIABWqGLlHM4KoodhXNlTOQ3mEUAAAQQeECAAMv8Z4OiD7mF8I8XaKpZWfpwqCCCAwGwECIDZKnaIt9eA6+V38nIJHwEEEBiGAAFwoxQrfF98K4cV8hvmThIIAgggMBkBAmCggvlUHRcDo5iRFQgggMAZAgTAGUrWIIAAAgggsBgBAqBhQX16bQibKwQQQACBmwQIAA2CAAIIIIDAhgQIgJNF9+n9JCjLEEAAAQSmIEAATFGmL4IcUYSMGNNEJRUqAggg0I0AAdANfR3HpQZyKTt1smQVAQQQQCCXAAGQS/Cwf4bBOUOMhcvCHAIIIIDAgQABMOEA18UIIIAAAgjkEiAAcgnaHxLIeeKQszcMzAIEEEBgYwIEwMbFXzn154QDQbFy1eWGAAIpBAiAFFrWIoAAAgggsAgBAmCRQkoDAQQQQACBFAIEQAqtxmtXfVy9al6N24M7BBBAIIsAAZCAb8TBNWJMCUgtRQABBBDoRIAA6AT+ldsZBvgMMXYuI/cIIIDAdAQIgOlKVj7gqwP+6r7yGbCIAAIIIJBKgABIJWY9AggggAACCxAgAB4UMfcTbe7+BfopOQXMkpHZgAACCBQhQAAUwTi2EUN27PqIDgEEEOhBgAAoQN2ALQCRCQQQQACBpgQIgKa4OUMAAQQQQGAMAgTAGHUIo6jxlKGGzTCRQgtmjr0QAmYQQACBLAIEQBa+MTcbjmPWRVQIIIDASAQIgCeqcRyguw/UUvmXsjPSDSQWBBBAYFYCBMCslRM3AggggAACGQQIgAx4u3+i3T3/jNaxFQEEEOhOgADoXgIBIIAAAggg0J4AAdCe+SOPPkV3LgD3CCCAwKYECIBNC79y2kTVytWVGwIIlCJAAJQiyU4SAUM6CZfFCCCAQHECBEBxpAwigAACCCAwPgEC4EGNfvDLX/3wu1/9yndKlq32J93I/vH6rRxr5F+SZQlbO+RYghMbCCCwPgEC4EaNo+G6QnucyfHMmpYsRounZe58IYAAAqUIEAClSF6wY5BdgGYLAggggEARAgRAEYyMIIAAAgggMBcBAuBGvW59XzzLp/ee33n3YNQz37lufdEigMDuBAiA3TvgkP9TQ/u5Qd5jwKeWa4YYU3OyHgEEEChB4MV//Y//4su3DP3Lf/vfflPC0eg2Vh0Uq+Y1ej+JDwEEEBidAAEweoUKx0cQ3N1hULipmEMAgSkJEABTlk3QEQFDPiLkOgII7E6AAHimAwyQ9FvjDLMza155jtY+vB6tTc/GDgQQQGBtAgTAhfqWGja5dnL3X0j90ZbIf3Q913/K/pFiSYnbWgQQQKAWAQKgFtlCdq8Orqv7CoXd3czu+XcvgAAQQGB4Av4Z4G9LVHNg1LRdu8Nmjr02G/YRQACBmQk0EQCth0hrf081QE4MOXtTm7Glr9TYzq5fIYezuV5dh9FVcvYhsC6BJgJgXXwyW5GAYbliVeWEAAJHAqcEgANR43xGYKWf2S3d06Xt6TgEEECgNoFTAqB2EK3sO6RbkX7eT4satPDRn+S5CLA4x8kqBHYksJUA2KXAZw/9s+tW5/YUhxZsSvkoZWf1OssPAQQeE3hNAKQcJilre4K/GufVfT1zLeF717xLsBvdhtqOXiHxIdCOwItXB8KZg2HG74BLxnzWVsQyup5a/of2VvgTxqn5P1wf1SiFfWQrJ85We1fIoRUrfhDYjcAyXwGkHOzHIufs3a1h5LsGAT2/Rh1lgUAOgWUEQCoEB2AqsS/Wt+LWys81CnYhgAAC8xPoKgDOHvJn1/Uqx+jxteZyhseZNSXjfs5f6ziu5jRLnFfzsw8BBNoTeFYAXDlwruxpnzKPDz/Jr1CzmXOYOXZ3EgIIzE2g6xOAudGJfncCowzvUeLYvR/kj8BsBLYQAA7I2dpSvAgggAACtQksJQBSB/1T61Nt5Baotb/ceO1HAAEEEFiDwOcCwBBao5izv+hWuwr6vDZh9hFAYCYC1Z8ArHLo5uSRs/epZsqxl7N3psYWKwIIIIDAbQLVBYACILASgRUE1Ao5rNRTckGgF4FL/wyw9AFS2l4vmPwigAACCCAwC4Fh3gH4vTdf3P/dJ/dTPZEgXNq1eW3Wte23I8UTAgggcI7ANAO39QHd2t+5cpVdlZtj7v6y2Zy3NlLcI8VynqCVCCCwAoFhBUCHg/H+QUGH5ZLZdE1z7FDDTDxjbS/Fr5SdseiIBgEEcgk8Oeg2PTAeDseHXGcXA6vmldv79iOAAAJbE5h9uJUs3nODckgxcEKkRfmofcnuYQsBBBCYjMDSQ+DEkHxYrucejx8H6WvMUvykrL3YS7fibfoVwMX4batMoEEPVs6AeQQQKEFgaQFwC9ATh2A0HEMhUKIgmTZSchim9qMMpFHiyOwB2xFAAIFTBIYZAqeirbDowaEfDc/PvI8qAs7GdSbHCpSZRAABBBAYjUARAVDyk1NJWxdgvxqQEZfRBmlq3I/y68z8Qpme3rJKHleA7Jz7FV72IIDA3V3W99lnAZY+nErbe5BHymBPWXsWVeq6lBhS1qbGMcX6in0zRf6CRAABBB4SiD7p7kYrdUimrk/ieWJgnf3kf/z6Qt0TKnGiDgnWLEUAAQTGIHBqEKx8AN54GfAUm8N7Adm/q5DAOmX4PxQAZ/P6XYcmxDRGV4sCAQQQQCAkkDwMQosDLUgdXL9dn/aE5P4P7u9e/G3anjxG//+pw/0f3N29+NszNaz6pCIvHbsRQAABBHoQ+N3wSB2WpYPt7f9BPunD8v737+9e/DpLBCTk/0V8979/d/fi12eG/8NP/5/990d/ACrBb+mSs5dAoHSdSttLSMVSBBAYhMDZATJIuM3CSBcBX4R2dd+ZxK7avrrvTEzTrtlpAO6U67QNKXAEOhAoJgBmPWRuxJ36HftRAPzu03ahul4d5FfyKBRyOTMz9NcMMZarCEsIIDA7gWICYAQQuQfwYX/uwH2FJJfx2R/5eaoEV3OoUs7c+uQG1dt/bvz2I4AAAiUJ5A6nkrGMaOvqAL267xGDSy8lPqb4eRz39/d3L168UOsRO0xMCCCAQCcCzw6F0p+WSttrxOvap+/7P7y/e/E3D0O8OnwfvPH/h3d3L/7mrJ1n4y5dh9L2GtWVGwQQQGB7Ao/eCN+RxokBdk0EfAEz9/v3q/uLPIHYqR9O9MFOOOSKAAIbEDj7ibIrikEO5ytC4OYgPpFXqgBIHvwnYuha+9Wd4796heWHwLgEphAAtfElHMJHEXDmMX/yUE7454TPxbPkH/up3QfsI4AAAjsRWFoAJAz21JqnCIEaAuDU4E9NatT1Fes4asriQgABBKoTqC4Abh3eIx/sT8X2xP93Swg8VbyzL11m2R2Za/WO3tSBmm9aeGkjkEGgugDIiC3cOtihd2Zon+Vd0lbI0QIEEEAAgf0IvDaQBhuq2RX5wS9/9cPvfvUr38k2dM3AlRcHP/N0dV9ylLfq3Zldci65G0br/Zrx1LSdWwf7EUCgDYGzn0g/+zGZez8m83pRWnJp5auFnxY+nrqFUvymrG1zu/KCAAIIlCNwWgCUczmepV0O+tXzHC2/0eIZ784TEQII9CSwlQBwIPdsNb4RQAABBEYiMLQAMLBHahWxIIAAAgisROB3AsCwvV3WVnxK+ClhY6UmHyEXNRmhCmJAAIGHBKo+AZjtNwBaHtItfZ1p+dR4UteficEaBBBAAIF2BM7+ME27iHiqRqDF0G7hoxqgTQyr0SaFliYCAYGkJwCr/LvwWgdga7tn/J1Z81SPXN3X+4572KO5OazQ7yvk0Lun+EdgVQJJAmB0CLkH/sP8IlvR9dFZfRbfCjnkcn6KAS65VO1HAIEZCBQRAA7MGUo9R4x6aY46iRIBBOYnUEQAlMLg8M8nieFthivyOZPTmTX53ccCAgjMRGAoAVACXMvvPK/6OnsYn1l3NoYztl7xf27tU75S7Jaob4qNkWNLycNaBBBAoAaBagJg1sN39LhHjy8SEaWbuDSPEvZK2CjNiT0EEEDgSGCqvwY4y8E6S5ypt8OqeaVysB4BBBBYgUC1JwDPwWk1RFr5GbEJbuW+M5dbtdqFy8M8d8l5xHtUTAiMQCBLAJz9/nmERJ97NL37IXjMf0Qet2Jq2YMlfNXmWyLGke5XsSCAQD0CL0ocSCVs5KTYyn+Kn5JrU2zlcLQXAQQQQGAfAklPAFoOopa+9im3TGchULL/S9qahZ84EUAgJpAkAB6aG/VQOT4CbRlnS19xafdbUZp/aXstKuIrgBaU+UBgDQKXBUCp9N/80hv3pWxdsfPJx592Z3Al7lX2/OnXv9a1/r05/uTnv+gdQlf/u99/tc+/P/njP7pZ37/+6c8enX/vvvPWo/vxvQ8+yjofS9vr2qwLOs8qbgketW+AKMbdD6CIT+3rBAABULvHRrZf+/wjAEaufv/YCABPALp2IQFAAHRtwM7OCYDOBdjcPQFAAHS9BQgAAqBrA3Z2TgB0LsDm7gkAAqDrLUAAEABdG7Cz89ICIHrkf0zXOwCdG6CzewKAAOjaggQAAdC1ATs7JwA6F2Bz9wQAAdD1FiAACICuDdjZOQHQuQCbuycACICutwABQAB0bcDOzgmAzgXY3D0BQAB0vQUIAAKgawN2dp4rAFK/8/cOQOeCD+aeACAAurYkAUAAdG3Azs4JgM4F2Nw9AUAAdL0FCAACoGsDdnZOAHQuwObuCQACoOstQAAQAF0bsLNzAqBzATZ3TwAQAF1vAQKAAOjagJ2dEwCdC7C5ewKAAOh6CxAABEDXBuzsnADoXIDN3RMABEDXW4AAIAC6NmBn5wRA5wJs7p4AIAC63gIEAAHQtQE7OycAOhdgc/cEAAHQ9RYgAAiArg3Y2TkBULcAX7u7+wcPPfzs7u7jg8dP6kYwtnUCgADo2qEEAAHQtQE7OycA6haAALjNlwAgAOregYF1AoAA6NqAnZ0TAHULQAAQADcJfEIA1L0DCYCbBH7ycwKgawN2dk4A1C0AAUAAEAB177Es654AEABZDTT5ZgIgr4Bfv7v7vYcWfnp393d5Fvfa7SsATwC6djwBQAB0bcDOzgmAvAIQAHn8CAACIK+DMncTAARAZgtNvZ0AyCsfAZDHjwAgAPI6KHM3AUAAZLbQ1NsJgLzyEQB5/AgAAiCvgzJ3EwAEQGYLTb09VwAck/+TP/6jJB5//dOfPZoB777z1v1DA+998FHWjChtLyk5i0MCWcUNrZ9YUPoGOOHy0RL/CiCVWNn1BAABULaj5rJW+vwjAOaqf+9oCQBPALr2IAFAAHRtwM7OCYDOBdjcPQFAAHS9BQgAAqBrA3Z2TgB0LsDm7gkAAqDrLUAAEABdG7Cz89ICIPWdAO8AdG6Azu4JAAKgawsSAARA1wbs7JwA6FyAzd0TAARA11uAACAAujZgZ+cEQOcCbO6eACAAut4CBAAB0LUBOzsnADoXYHP3L2o34OZ8pZ9JIPWfNWW6K7599j/2g3/xlmBwIAK7/zNwAmCgZhTK6wQMoL5dgX9f/rzXJUAAfOmNR7/8VBc36wikETCA0niVXo1/aaLsjUSAACAARupHsRwIGEB9WwL/vvx5r0uAACAA6nYY61UJ9B5Qs3/Hn1sc/HMJ2t+TAAFAAPTsP74zCRhAmQAzt+OfCdD2rgQIAAKgawNynkfAAMrjl7sb/1yC9vckQAAQAD37j+9MAgZQJsDM7fhnArS9KwECgADo2oCc5xE43sDH37XIHVDH7/gjf3nZzLc74oH/fDXdKWICgADYqd+Xy9UA6ltS/Pvy5z2PAAFAAOR1kN1dCRhAXfHf4d+XP+95BAgAAiCvg+zuSsAA6oqfAOiLn/dMAgQAAZDZQrb3JBAJgNwb/PhOQeSvJ4seviMe+PeoCp9nCeT251k/o67ztwBGrYy4ThEwgE5hqrYI/2poGW5AgADwBKBBm3FRi4ABVIvsObv4n+Nk1ZgECAACYMzOFNUpAgbQKUzVFuFfDS3DDQgQAARAgzbjohYBA6gW2XN28T/HyaoxCRAABMCYnSmqUwQMoFOYqi3CvxpahhsQIAAIgAZtxkUtAgZQLbLn7OJ/jpNVYxIgAAiAMTtTVKcIGECnMFVbhH81tAw3IEAAEAAN2mxcF996++1Hwb3/8mXVYCN/qQMl+nf6pZOJ/KVej3iUjj/yh3/b/j/Wt/b9V7qfZrN37P8fffDhi9lyKBmv3wEoSXNCW9FAKJ1S5M8AajuAjgMH/7b8CYDSJ8xtewTAYz4EQNv+G85bNJBLBxz5M4DaDiACoO8TMAKg9AlDAKQQJQBSaC24NhrIpVOO/BEABMCtnkv9iiXqp6gfa/c/AVCaMAGQQpQASKG14NrRDsDjd3K5B37pkuXG8+13vnF/K6ba3wEf632MBf+2AowAKH2HEgApRAmAFFoLriUA0opKAHz66KWpiEca3Xh15C+6ProAqy0AY8Jrr/AOwOP6EgBr93uYHQEQInq0IBow0fXRB5AnAJ4ApN0Rc60mAAiAuTq2crQEQBrgaMBH1wmANN7H1RHf6Pro/D0ByOuPaDcBQABEPbLV9d4CYPRPnFEzRAMnun4cSLUHQHQARvFG1yNepa9H8UTXe/OPeNTuh8j/7NdT33mZPd/U+H0FkEpssfUEQF5BowETXe89gAiwxy9l1h640UA6dmPtePK6f/zdEW8/BOSXAMfv4ooREgB5cKMBH10nAPbmH2VPAESEbl8nAG7z8QQgr7+m300A5JUwGvDRdQJgb/5R9gRARIgAyCFEAOTQW2Dv8YdSopekclOOHrlFAzO6nhtf6v4onuj60R/+aRWI+EbXewuwKFsCICJEAOQQIgBy6C2wlwDIK2I0YKLrBEBf/gRAHv/Rd/sKwFcAo/do1/gIgDz80YCPrhMAffkTAHn8R99NABAAo/do1/gIgDz80YCPrhMAffkTAHn8R99NABAAo/fozfiOAzo1meMAOu7PtZ8az3F9NCCj67n+S++P4o2ul44nshfFE12P7Le+HsV7vF57QJR+p8M7Abc7KqrncXf0TlLr/m3tzzsArYkn+ssd0ARAIvDM5akDKLe+meHezRZvlG9qPtHAyB0QBEBUsbLXo3oSAI8JEABl+6+4tdwBQQAUL8lNg6kDKLe+udnNFm+Ub2o+0cAgACLiY12P6kkAEABjdWwQTe6AIADaljt1AOXWNze72eKN8k3NJxoYBEBEfKzrUT0JgIMAGKt8r0cTDbDR44/iG20ARPGWvp772/Sl48m1Fw2g1i+dRfnszj93wEd8a19f/XyM+PU+P6P4Rr/+6G97jxjs6g3eu4F78919APV+qWt3/gTAiKf++Zh6n5/nIx1zJQHQuS69G5gAKNsAngCU5ZlqLZU/AZBKeKz1vc/PsWikR0MApDMruqN3AxMARcsZvlXvK4CyvI/WCIC6fEez3vv8HI1HajzDC4Aood4DLIovut67gUv/M6Uo3+P16BNYdKCn+qu9PjVe/MtWJJV/We/jWXM+jleTkSIiADpXgwD48GYPznagp8ZLAJS9AVP5l/U+njUCYLyajBQRAdC5GgQAAdCzBXd/AtOTfQvfBEALyvP6IAA6144AIAB6tiAB0JN+fd8EQH3GM3uY7pcAo4E5WsOPHm8UX+/mnu2R7mzxRvWdLZ/ceGufH6Pdb7XzjfprNB5RvKtdJwAqVzRqcDfg7QLkHuiVy/ua+dnijfjMlk9uvLXvx+g8iOpR+nrtfKN4R+MRxbvadQKgckWjBncDEgCVWzDLfO5AzXJ+YXNuvLXvx+g8uJBy1pba+UbBjcYjine16wRA5YpGDe4GJAAqt2CW+dyBmuX8wubceGvfj9F5cCHlrC21842CG41HFO9q14d/CTC1QUdrqCj+0eLt3eC5B3jv+I/+Z8tntnijekf5RNcj+6nXo/Mg1d5x/ernSWl+q/OK+okAiAhlXo8advcGnH1gRu3ResBE8UTXZ4s3N5/W+UbnQZRPdH3186Q0v9V5Rf1CAESEMq9HDbt7AxIAmQ1WeHvrgVg4/NfMRflE10vHF50Huf5WP09K81udV9RPBEBEKPN61LC7NyABkNlghbe3HoiFwycAPv50+DM9p+bReZpqe/fzd/pmKd0QqQ0UrR+9wVof+FG9oj9PG/Ee7XrqH/+p3S/4v3zUImd4R8xu9dwZ+yV7NifWknHUstWaZ608RrFLAFSuxOgNSwDUbQACoC7fyHrEP7o/cwdqZD+KP/V6bryp/lqvb82zdX6t/REAlYmP3rAEQN0GiAbQ0XvtfokGxG5PYCLeEa+oeyL70f7U67nxpvprvb41z9b5tfZHAFQmPnrDEgB1G4AAqMs3sh7xj+7P3IEa2Y/iT72eG2+qv9brW/NsnV9rf8P/ENDsBR/9hjx+4nv/5ePvSEs35NHf0X70x2lS48nlX7r/oj//i//jCtfmH/HO9Z/bf6n9nhtvqr/W60vzXJ1XVB8CICKUeb10w2aG89p2AuA20dIHBAGQ1sG1+RMAafXovbr0eVq6v3rzSfVPAKQSS1xfumET3YfLCQAC4CGB3Z7AEADhETHUgtLnKQHwpTfuh6rwIZjZC1S6YUvXigAgAAiA53sg9/xpff/nxlv6fCltrzTP1XlF/Id/CbB0wSMgpa/P1mDRI+pcPqU/YX4WT88eKV1f/NM6rDT/NO/jre55L7Sgod5lKRMAZXm+Zm22hp1tAPU+8ErXF/+0G7I0/zTv463ufT/UJqLeZQkTAGV5EgABz9JPAHofeKUPJAIg7YYszT/N+*********************************************************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"}, {"name": "selection", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 500, "height": 512, "data_url": "data:image/png;base64,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"}, {"name": "selection", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": false, "blend_mode": "default", "width": 256, "height": 128, "data_url": "data:image/png;base64,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"}], "source": "data:image/png;base64,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", "relative_path": "../textures/overlays/glow/bio_naljian_biosovortian_glow.png"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": true, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "reference_images": [{"name": "Pasted", "scope": "project", "position": [432, 517], "size": [211, 363], "source": "data:image/png;base64,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"}], "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}