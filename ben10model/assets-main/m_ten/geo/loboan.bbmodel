{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "loboan", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "armorHead", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 27, -4], "to": [3, 33, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 0, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 0}, "east": {"uv": [0, 8, 8, 16], "texture": 0}, "south": {"uv": [24, 8, 32, 16], "texture": 0}, "west": {"uv": [16, 8, 24, 16], "texture": 0}, "up": {"uv": [16, 8, 8, 0], "texture": 0}, "down": {"uv": [24, 0, 16, 8], "texture": 0}}, "type": "cube", "uuid": "0ae9a357-2cb2-13fa-bd0e-e15ad0dcaf22"}, {"name": "armorHead", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 27, -4], "to": [3, 33, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 0, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 0}, "east": {"uv": [0, 0, 0, 0], "texture": null}, "south": {"uv": [56, 8, 64, 16], "texture": 0}, "west": {"uv": [0, 0, 0, 0], "texture": null}, "up": {"uv": [48, 8, 40, 0], "texture": 0}, "down": {"uv": [56, 0, 48, 8], "texture": 0}}, "type": "cube", "uuid": "049cc0ba-4802-4fb6-5e06-9b7fffdec50e"}, {"name": "armorHead", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 27, -7], "to": [1, 29.5, -4], "autouv": 0, "color": 0, "origin": [0, 0, 0], "faces": {"north": {"uv": [43, 12.75, 45, 14.75], "texture": 0}, "east": {"uv": [44.5, 13.5, 45, 14.25], "texture": 0}, "south": {"uv": [44.5, 13.5, 45, 14.25], "texture": 0}, "west": {"uv": [44.5, 13.5, 45, 14.25], "texture": 0}, "up": {"uv": [44.5, 13.5, 45, 14.25], "texture": 0}, "down": {"uv": [44.5, 13.5, 45, 14.25], "texture": 0}}, "type": "cube", "uuid": "3974783a-8a4b-c750-9994-28fd1d062701"}, {"name": "ears", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.6881300000000001, 32, -1], "to": [3.68813, 34, 1], "autouv": 0, "color": 1, "rotation": [0, 0, -25], "origin": [2.18813, 33, 0], "faces": {"north": {"uv": [20.5, 9.5, 21.25, 10], "texture": 0}, "east": {"uv": [20.5, 9.5, 21.25, 10], "texture": 0}, "south": {"uv": [20.5, 9.5, 21.25, 10], "texture": 0}, "west": {"uv": [20.5, 9.5, 21.25, 10], "texture": 0}, "up": {"uv": [20.5, 9.5, 21.25, 10], "texture": 0}, "down": {"uv": [20.5, 9.5, 21.25, 10], "texture": 0}}, "type": "cube", "uuid": "5411e368-36a4-4cd5-e0ea-f9e0c27a00a0"}, {"name": "ears", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.6881300000000001, 32, 0], "to": [3.68813, 36, 1], "autouv": 0, "color": 1, "rotation": [0, 0, -25], "origin": [2.18813, 33, 0], "faces": {"north": {"uv": [20.5, 9.5, 21.25, 10], "texture": 0}, "east": {"uv": [20.5, 9.5, 21.25, 10], "texture": 0}, "south": {"uv": [20.5, 9.5, 21.25, 10], "texture": 0}, "west": {"uv": [20.5, 9.5, 21.25, 10], "texture": 0}, "up": {"uv": [20.5, 9.5, 21.25, 10], "texture": 0}, "down": {"uv": [20.5, 9.5, 21.25, 10], "texture": 0}}, "type": "cube", "uuid": "feda7122-deb0-74cc-645c-d4f5d67c9d34"}, {"name": "ears", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1.7999999999999998, 34.5, -1], "to": [3.3, 36.5, 0], "autouv": 0, "color": 1, "rotation": [0, 0, -25], "origin": [0.81057, 33, 0], "faces": {"north": {"uv": [37, 9, 37.25, 9.5], "texture": 0}, "east": {"uv": [37, 9, 37.25, 9.5], "texture": 0}, "south": {"uv": [37, 9, 37.25, 9.5], "texture": 0}, "west": {"uv": [37, 9, 37.25, 9.5], "texture": 0}, "up": {"uv": [37, 9, 37.25, 9.5], "texture": 0}, "down": {"uv": [37, 9, 37.25, 9.5], "texture": 0}}, "type": "cube", "uuid": "b3757673-8e09-9ea5-0954-b572cdf5a9d2"}, {"name": "ears", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.68813, 32, -1], "to": [-1.6881300000000001, 34, 1], "autouv": 0, "color": 1, "rotation": [0, 0, 25], "origin": [-2.18813, 33, 0], "faces": {"north": {"uv": [21.25, 9.5, 20.5, 10], "texture": 0}, "east": {"uv": [21.25, 9.5, 20.5, 10], "texture": 0}, "south": {"uv": [21.25, 9.5, 20.5, 10], "texture": 0}, "west": {"uv": [21.25, 9.5, 20.5, 10], "texture": 0}, "up": {"uv": [21.25, 9.5, 20.5, 10], "texture": 0}, "down": {"uv": [21.25, 9.5, 20.5, 10], "texture": 0}}, "type": "cube", "uuid": "62c06b87-57bb-d614-9f52-f6f491303440"}, {"name": "ears", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.68813, 32, 0], "to": [-1.6881300000000001, 36, 1], "autouv": 0, "color": 1, "rotation": [0, 0, 25], "origin": [-2.18813, 33, 0], "faces": {"north": {"uv": [21.25, 9.5, 20.5, 10], "texture": 0}, "east": {"uv": [21.25, 9.5, 20.5, 10], "texture": 0}, "south": {"uv": [21.25, 9.5, 20.5, 10], "texture": 0}, "west": {"uv": [21.25, 9.5, 20.5, 10], "texture": 0}, "up": {"uv": [21.25, 9.5, 20.5, 10], "texture": 0}, "down": {"uv": [21.25, 9.5, 20.5, 10], "texture": 0}}, "type": "cube", "uuid": "7070baca-4a62-caf7-aa9f-91dc43d6050a"}, {"name": "ears", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.3, 33.5, -1], "to": [-1.7999999999999998, 35.5, 0], "autouv": 0, "color": 1, "rotation": [0, 0, 25], "origin": [-0.81057, 33, 0], "faces": {"north": {"uv": [37.25, 9.75, 36.5, 10.25], "texture": 0}, "east": {"uv": [37.25, 9.75, 36.5, 10.25], "texture": 0}, "south": {"uv": [37.25, 9.75, 36.5, 10.25], "texture": 0}, "west": {"uv": [37.25, 9.75, 36.5, 10.25], "texture": 0}, "up": {"uv": [37.25, 9.75, 36.5, 10.25], "texture": 0}, "down": {"uv": [37.25, 9.75, 36.5, 10.25], "texture": 0}}, "type": "cube", "uuid": "e58ea285-169e-1b55-cfa8-73d2df850e93"}, {"name": "ears", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.3, 34.5, -1], "to": [-1.7999999999999998, 36.5, 0], "autouv": 0, "color": 1, "rotation": [0, 0, 25], "origin": [-0.81057, 33, 0], "faces": {"north": {"uv": [37, 9, 37.25, 9.5], "texture": 0}, "east": {"uv": [37, 9, 37.25, 9.5], "texture": 0}, "south": {"uv": [37, 9, 37.25, 9.5], "texture": 0}, "west": {"uv": [37, 9, 37.25, 9.5], "texture": 0}, "up": {"uv": [37, 9, 37.25, 9.5], "texture": 0}, "down": {"uv": [37, 9, 37.25, 9.5], "texture": 0}}, "type": "cube", "uuid": "49a155d5-0db8-2d19-46d7-9fec27626f5f"}, {"name": "armorBody", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 20, -2], "to": [5, 26, 2], "autouv": 0, "color": 2, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [19, 20, 29, 26], "texture": 0}, "east": {"uv": [16, 20, 20, 26], "texture": 0}, "south": {"uv": [32, 20, 40, 26], "texture": 0}, "west": {"uv": [28, 20, 32, 26], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "e10231a6-c9b2-f757-aa74-bbe9b8f09d03"}, {"name": "armorBody", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 20, -2], "to": [5, 26, 2], "autouv": 0, "color": 2, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [19, 36, 29, 42], "texture": 0}, "east": {"uv": [16, 36, 20, 42], "texture": 0}, "south": {"uv": [32, 36, 40, 42], "texture": 0}, "west": {"uv": [28, 36, 32, 42], "texture": 0}, "up": {"uv": [28, 36, 20, 32], "texture": 0}, "down": {"uv": [36, 32, 28, 36], "texture": 0}}, "type": "cube", "uuid": "63ffef65-83ec-0a94-106b-5ff85e7d1583"}, {"name": "armorBody", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 13, -2], "to": [3.5, 19, 2], "autouv": 0, "color": 2, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [20.5, 26, 27.5, 32], "texture": 0}, "east": {"uv": [16, 26, 20, 32], "texture": 0}, "south": {"uv": [32, 26, 39, 32], "texture": 0}, "west": {"uv": [28, 26, 32, 32], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "774647cf-9d4b-3458-714f-a527a01725df"}, {"name": "armorBody", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 13, -2], "to": [3.5, 17, 2], "autouv": 0, "color": 2, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [20, 43, 28, 48], "texture": 0}, "east": {"uv": [16, 43, 20, 48], "texture": 0}, "south": {"uv": [32, 43, 40, 48], "texture": 0}, "west": {"uv": [28, 43, 32, 48], "texture": 0}, "up": {"uv": [28, 36, 20, 32], "texture": 0}, "down": {"uv": [36, 32, 28, 36], "texture": 0}}, "type": "cube", "uuid": "393b18cb-2228-ce7b-95e8-9826ddc63dfb"}, {"name": "tail", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 15, -1], "to": [0.5, 17, 2], "autouv": 0, "color": 3, "inflate": 0.5, "rotation": [20, 0, 0], "origin": [2.5, 5, -1], "faces": {"north": {"uv": [29, 54, 30.5, 56], "texture": 0}, "east": {"uv": [29, 54, 30.5, 56], "texture": 0}, "south": {"uv": [29, 54, 30.5, 56], "texture": 0}, "west": {"uv": [29, 54, 30.5, 56], "texture": 0}, "up": {"uv": [29, 54, 30.5, 56], "texture": 0}, "down": {"uv": [29, 54, 30.5, 56], "texture": 0}}, "type": "cube", "uuid": "eb796349-1911-a61a-ba0b-e1816caf0ac0"}, {"name": "tail", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 15, 14], "to": [1, 17, 20], "autouv": 0, "color": 3, "inflate": 0.5, "rotation": [-47.5, 0, 0], "origin": [3, 5, 13], "faces": {"north": {"uv": [29, 54, 30.5, 56], "texture": 0}, "east": {"uv": [29, 54, 30.5, 56], "texture": 0}, "south": {"uv": [29, 54, 30.5, 56], "texture": 0}, "west": {"uv": [29, 54, 30.5, 56], "texture": 0}, "up": {"uv": [29, 54, 30.5, 56], "texture": 0}, "down": {"uv": [29, 54, 30.5, 56], "texture": 0}}, "type": "cube", "uuid": "b8a64d10-f869-aa19-9ba1-eba25fb5dcc9"}, {"name": "tail", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 24.8, 4], "to": [1, 26.8, 11], "autouv": 0, "color": 3, "inflate": 0.5, "rotation": [37.5, 0, 0], "origin": [3, 14, -2], "faces": {"north": {"uv": [29, 54, 30.5, 56], "texture": 0}, "east": {"uv": [29, 54, 30.5, 56], "texture": 0}, "south": {"uv": [29, 54, 30.5, 56], "texture": 0}, "west": {"uv": [29, 54, 30.5, 56], "texture": 0}, "up": {"uv": [29, 54, 30.5, 56], "texture": 0}, "down": {"uv": [29, 54, 30.5, 56], "texture": 0}}, "type": "cube", "uuid": "33b0f459-2583-37d1-408b-1094f2c05606"}, {"name": "tail", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 24.8, 4], "to": [1, 26.8, 11], "autouv": 0, "color": 3, "inflate": 0.5, "rotation": [37.5, 0, 0], "origin": [3, 14, -2], "faces": {"north": {"uv": [29, 54, 30.5, 56], "texture": 0}, "east": {"uv": [29, 54, 30.5, 56], "texture": 0}, "south": {"uv": [29, 54, 30.5, 56], "texture": 0}, "west": {"uv": [29, 54, 30.5, 56], "texture": 0}, "up": {"uv": [29, 54, 30.5, 56], "texture": 0}, "down": {"uv": [29, 54, 30.5, 56], "texture": 0}}, "type": "cube", "uuid": "4c3c2e03-6478-4a25-23b3-65cc1c0bc7f7"}, {"name": "armorRightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 19, -2], "to": [8, 25, 2], "autouv": 0, "color": 4, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [44, 20, 46, 26], "texture": 0}, "east": {"uv": [40, 20, 44, 26], "texture": 0}, "south": {"uv": [52, 20, 54, 26], "texture": 0}, "west": {"uv": [48, 20, 52, 26], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "bc37039a-4ca2-2c6a-4aee-7ecb2b544525"}, {"name": "armorRightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 19, -2], "to": [8, 25, 2], "autouv": 0, "color": 4, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [44, 36, 46, 42], "texture": 0}, "east": {"uv": [40, 36, 44, 42], "texture": 0}, "south": {"uv": [52, 36, 54, 42], "texture": 0}, "west": {"uv": [48, 36, 52, 42], "texture": 0}, "up": {"uv": [48, 36, 44, 32], "texture": 0}, "down": {"uv": [52, 32, 48, 36], "texture": 0}}, "type": "cube", "uuid": "d795634c-51ea-763a-abe7-dcfee4adc874"}, {"name": "armorRightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.1499999999999995, 8, -3.5], "to": [8.85, 19, 1.5], "autouv": 0, "color": 4, "inflate": 0.5, "rotation": [12.5, 0, 0], "origin": [0, 13.5, 0], "faces": {"north": {"uv": [44, 25, 47.7, 32], "texture": 0}, "east": {"uv": [40, 25, 44, 32], "texture": 0}, "south": {"uv": [52, 25, 55.7, 32], "texture": 0}, "west": {"uv": [48, 25, 52, 32], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "646ff5aa-b690-7488-394d-5f81a407ddf4"}, {"name": "armorRightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.1499999999999995, 8, -3.5], "to": [8.85, 19, 1.5], "autouv": 0, "color": 4, "inflate": 0.75, "rotation": [12.5, 0, 0], "origin": [0, 13.5, 0], "faces": {"north": {"uv": [44, 41, 47.7, 48], "texture": 0}, "east": {"uv": [40, 41, 44, 48], "texture": 0}, "south": {"uv": [52, 41, 55.7, 48], "texture": 0}, "west": {"uv": [48, 41, 52, 48], "texture": 0}, "up": {"uv": [48, 36, 44, 32], "texture": 0}, "down": {"uv": [52, 32, 48, 36], "texture": 0}}, "type": "cube", "uuid": "d46c2e4f-f4a4-345f-19ad-2a433da33605"}, {"name": "armNested", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 19, -2], "to": [-6, 25, 2], "autouv": 0, "color": 8, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [52, 52, 54, 58], "texture": 0}, "east": {"uv": [48, 52, 52, 58], "texture": 0}, "south": {"uv": [60, 52, 62, 58], "texture": 0}, "west": {"uv": [56, 52, 60, 58], "texture": 0}, "up": {"uv": [56, 52, 52, 48], "texture": 0}, "down": {"uv": [60, 48, 56, 52], "texture": 0}}, "type": "cube", "uuid": "604f28c4-ca0e-367f-84d7-1e2710d53d74"}, {"name": "armNested", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 19, -2], "to": [-6, 25, 2], "autouv": 0, "color": 8, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [36, 52, 38, 58], "texture": 0}, "east": {"uv": [32, 52, 36, 58], "texture": 0}, "south": {"uv": [44, 52, 46, 58], "texture": 0}, "west": {"uv": [40, 52, 44, 58], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "d245ae24-cd59-b16b-8f25-d0728ffe1e03"}, {"name": "armNested", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.3, 8, -3.5], "to": [-5.6000000000000005, 19, 1.5], "autouv": 0, "color": 8, "inflate": 0.75, "rotation": [12.5, 0, 0], "origin": [-7.45, 13.5, -0.5], "faces": {"north": {"uv": [52, 57, 55.7, 64], "texture": 0}, "east": {"uv": [48, 57, 52, 64], "texture": 0}, "south": {"uv": [60, 57, 63.7, 64], "texture": 0}, "west": {"uv": [56, 57, 60, 64], "texture": 0}, "up": {"uv": [56, 52, 52, 48], "texture": 0}, "down": {"uv": [60, 48, 56, 52], "texture": 0}}, "type": "cube", "uuid": "f9736e80-00b6-93dc-25e6-ba186fac1552"}, {"name": "armNested", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.3, 8, -3.5], "to": [-5.6000000000000005, 19, 1.5], "autouv": 0, "color": 8, "inflate": 0.5, "rotation": [12.5, 0, 0], "origin": [-7.45, 13.5, -0.5], "faces": {"north": {"uv": [36, 57, 39.7, 64], "texture": 0}, "east": {"uv": [32, 57, 36, 64], "texture": 0}, "south": {"uv": [44, 57, 47.7, 64], "texture": 0}, "west": {"uv": [40, 57, 44, 64], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "18d62470-06fe-07ff-b87e-2d8528eb1e8f"}, {"name": "armorRightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5499999999999998, 8, -3.05071], "to": [3.75, 14, 0.44928999999999997], "autouv": 0, "color": 9, "inflate": 0.5, "rotation": [17.5, 0, 0], "origin": [2.15, 10, -1.30071], "faces": {"north": {"uv": [4, 20, 7.2, 26], "texture": 0}, "east": {"uv": [0, 20, 3.5, 26], "texture": 0}, "south": {"uv": [12, 20, 15.2, 26], "texture": 0}, "west": {"uv": [8, 20, 11.5, 26], "texture": 0}, "up": {"uv": [8, 20, 4, 16], "texture": 0}, "down": {"uv": [27.45, 57, 24.25, 58], "texture": 0}}, "type": "cube", "uuid": "eb19f949-26b2-388a-8e86-07b23fa4b61c"}, {"name": "armorRightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5499999999999998, 0, -3], "to": [3.05, 7, 1], "autouv": 0, "color": 9, "inflate": 0.5, "rotation": [5, 0, 0], "origin": [1.8, 3.5, -1], "faces": {"north": {"uv": [4, 25, 8, 32], "texture": 0}, "east": {"uv": [0, 25, 4, 32], "texture": 0}, "south": {"uv": [12, 25, 14.5, 32], "texture": 0}, "west": {"uv": [8, 25, 12, 32], "texture": 0}, "up": {"uv": [27.45, 57, 24.25, 58], "texture": 0}, "down": {"uv": [12, 16, 8, 20], "texture": 0}}, "type": "cube", "uuid": "2b36f0ab-09ee-7045-3f09-f4724543162d"}, {"name": "armorRightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5499999999999998, 0, -3], "to": [3.05, 7, 1], "autouv": 0, "color": 9, "inflate": 0.75, "rotation": [5, 0, 0], "origin": [1.8, 3.5, -1], "faces": {"north": {"uv": [4, 41, 8, 48], "texture": 0}, "east": {"uv": [0, 41, 4, 48], "texture": 0}, "south": {"uv": [12, 41, 14.5, 48], "texture": 0}, "west": {"uv": [8, 41, 12, 48], "texture": 0}, "up": {"uv": [8, 36, 4, 32], "texture": 0}, "down": {"uv": [12, 32, 8, 36], "texture": 0}}, "type": "cube", "uuid": "3126e2bf-178d-8407-ce58-c4b7cab0f3e3"}, {"name": "armorRightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1, 0, -6], "to": [3, 2, -4], "autouv": 0, "color": 9, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [6.75, 30.75, 7.25, 32], "texture": 0}, "east": {"uv": [6.75, 30.75, 7.25, 32], "texture": 0}, "south": {"uv": [6.75, 30.75, 7.25, 32], "texture": 0}, "west": {"uv": [6.75, 30.75, 7.25, 32], "texture": 0}, "up": {"uv": [6.75, 30.75, 7.25, 32], "texture": 0}, "down": {"uv": [6.75, 30.75, 7.25, 32], "texture": 0}}, "type": "cube", "uuid": "70db8184-37b1-6992-a33f-5378eba62cf9"}, {"name": "armorRightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2, 0, 0.7878], "to": [2, 2, 2.7878], "autouv": 0, "color": 9, "inflate": 0.5, "origin": [1, 0, 0], "faces": {"north": {"uv": [6.75, 30.75, 7.25, 32], "texture": 0}, "east": {"uv": [6.75, 30.75, 7.25, 32], "texture": 0}, "south": {"uv": [6.75, 30.75, 7.25, 32], "texture": 0}, "west": {"uv": [6.75, 30.75, 7.25, 32], "texture": 0}, "up": {"uv": [6.75, 30.75, 7.25, 32], "texture": 0}, "down": {"uv": [6.75, 30.75, 7.25, 32], "texture": 0}}, "type": "cube", "uuid": "9e47e93f-2ba9-a44c-8831-fd09a7e9d438"}, {"name": "armorLeftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.7, 8, -3.05071], "to": [-0.5, 14, 0.44928999999999997], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [17.5, 0, 0], "origin": [-2.1, 10, -1.30071], "faces": {"north": {"uv": [23.2, 52, 20, 58], "texture": 0}, "east": {"uv": [16, 52, 19.5, 58], "texture": 0}, "south": {"uv": [28, 52, 31.2, 58], "texture": 0}, "west": {"uv": [24, 52, 27.5, 58], "texture": 0}, "up": {"uv": [24, 52, 20, 48], "texture": 0}, "down": {"uv": [27.45, 57, 24.25, 58], "texture": 0}}, "type": "cube", "uuid": "3ae309d4-6e4d-e681-e09b-7deb043f8d3f"}, {"name": "armorLeftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 0, -3], "to": [-0.5, 7, 1], "autouv": 0, "color": 0, "inflate": 0.75, "rotation": [5, 0, 0], "origin": [-1.75, 3.5, -1], "faces": {"north": {"uv": [4, 57, 8, 64], "texture": 0}, "east": {"uv": [0, 57, 4, 64], "texture": 0}, "south": {"uv": [12, 57, 14.5, 64], "texture": 0}, "west": {"uv": [8, 57, 12, 64], "texture": 0}, "up": {"uv": [8, 52, 4, 48], "texture": 0}, "down": {"uv": [12, 48, 8, 52], "texture": 0}}, "type": "cube", "uuid": "5513ea26-78f6-5c16-1702-e6749d9299dd"}, {"name": "armorLeftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 0, -3], "to": [-0.5, 7, 1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [5, 0, 0], "origin": [-1.75, 3.5, -1], "faces": {"north": {"uv": [20, 57, 24, 64], "texture": 0}, "east": {"uv": [16, 57, 20, 64], "texture": 0}, "south": {"uv": [28, 57, 30.5, 64], "texture": 0}, "west": {"uv": [24, 57, 28, 64], "texture": 0}, "up": {"uv": [27.45, 57, 24.25, 58], "texture": 0}, "down": {"uv": [28, 48, 24, 52], "texture": 0}}, "type": "cube", "uuid": "16d80b17-d303-7e72-808f-8e6f7371f2d3"}, {"name": "armorLeftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 0.14025, -6.21218], "to": [-1, 2.14025, -4.21218], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [7.25, 30.75, 6.75, 32], "texture": 0}, "east": {"uv": [7.25, 30.75, 6.75, 32], "texture": 0}, "south": {"uv": [7.25, 30.75, 6.75, 32], "texture": 0}, "west": {"uv": [7.25, 30.75, 6.75, 32], "texture": 0}, "up": {"uv": [7.25, 30.75, 6.75, 32], "texture": 0}, "down": {"uv": [7.25, 30.75, 6.75, 32], "texture": 0}}, "type": "cube", "uuid": "5340034f-5e8a-d2bb-1f5a-2c2b17d0b52c"}, {"name": "armorLeftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 0.14025, 0.78782], "to": [-2, 2.14025, 2.78782], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 0, 0], "faces": {"north": {"uv": [7.25, 30.75, 6.75, 32], "texture": 0}, "east": {"uv": [7.25, 30.75, 6.75, 32], "texture": 0}, "south": {"uv": [7.25, 30.75, 6.75, 32], "texture": 0}, "west": {"uv": [7.25, 30.75, 6.75, 32], "texture": 0}, "up": {"uv": [7.25, 30.75, 6.75, 32], "texture": 0}, "down": {"uv": [7.25, 30.75, 6.75, 32], "texture": 0}}, "type": "cube", "uuid": "5c29dcbb-59a9-fdc7-b30e-cbd04239bea6"}], "outliner": [{"name": "armorHead", "origin": [0, 27, 0], "color": 0, "uuid": "7aa3d552-f8c3-aa27-9c14-c8955585e1ae", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["0ae9a357-2cb2-13fa-bd0e-e15ad0dcaf22", "049cc0ba-4802-4fb6-5e06-9b7fffdec50e", "3974783a-8a4b-c750-9994-28fd1d062701", {"name": "ears", "origin": [0, 3, 0], "color": 1, "uuid": "7c8bc423-4560-b380-d226-ccca4330ad80", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["5411e368-36a4-4cd5-e0ea-f9e0c27a00a0", "feda7122-deb0-74cc-645c-d4f5d67c9d34", "b3757673-8e09-9ea5-0954-b572cdf5a9d2", "62c06b87-57bb-d614-9f52-f6f491303440", "7070baca-4a62-caf7-aa9f-91dc43d6050a", "e58ea285-169e-1b55-cfa8-73d2df850e93", "49a155d5-0db8-2d19-46d7-9fec27626f5f"]}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 2, "uuid": "3e9bf71b-3f57-710a-48c4-7455b49d5c7d", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["e10231a6-c9b2-f757-aa74-bbe9b8f09d03", "63ffef65-83ec-0a94-106b-5ff85e7d1583", "774647cf-9d4b-3458-714f-a527a01725df", "393b18cb-2228-ce7b-95e8-9826ddc63dfb", {"name": "tail", "origin": [2.5, 5, -1], "rotation": [-5, 0, 0], "color": 3, "uuid": "880d60fa-a20b-6f1b-45da-762b3a398a1f", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["eb796349-1911-a61a-ba0b-e1816caf0ac0", "b8a64d10-f869-aa19-9ba1-eba25fb5dcc9", "33b0f459-2583-37d1-408b-1094f2c05606", "4c3c2e03-6478-4a25-23b3-65cc1c0bc7f7"]}]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 4, "uuid": "a9ccbf65-e288-1959-89d7-12d5e9bae307", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["bc37039a-4ca2-2c6a-4aee-7ecb2b544525", "d795634c-51ea-763a-abe7-dcfee4adc874", "646ff5aa-b690-7488-394d-5f81a407ddf4", "d46c2e4f-f4a4-345f-19ad-2a433da33605", {"name": "wingRight", "origin": [6, 22, 0], "color": 5, "uuid": "d745e5f7-a8f1-bc71-2bbd-f31a9e5a0b54", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 6, "uuid": "afa74e38-a795-9309-28c2-ad1077dc2581", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 22, 0], "color": 7, "uuid": "3edabae9-5bb8-1c42-dcb4-e9f8b1d172c1", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-6, 22, 0], "color": 8, "uuid": "a29e0180-e43a-970a-b2ab-a98043e6cb12", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["604f28c4-ca0e-367f-84d7-1e2710d53d74", "d245ae24-cd59-b16b-8f25-d0728ffe1e03", "f9736e80-00b6-93dc-25e6-ba186fac1552", "18d62470-06fe-07ff-b87e-2d8528eb1e8f"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 9, "uuid": "c1d7fba9-eb65-8d49-7128-d34f7ed900e7", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["eb19f949-26b2-388a-8e86-07b23fa4b61c", "2b36f0ab-09ee-7045-3f09-f4724543162d", "3126e2bf-178d-8407-ce58-c4b7cab0f3e3", "70db8184-37b1-6992-a33f-5378eba62cf9", "9e47e93f-2ba9-a44c-8831-fd09a7e9d438"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "a9f6df89-c71a-d6ec-36c8-5b513b9ad723", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3ae309d4-6e4d-e681-e09b-7deb043f8d3f", "5513ea26-78f6-5c16-1702-e6749d9299dd", "16d80b17-d303-7e72-808f-8e6f7371f2d3", "5340034f-5e8a-d2bb-1f5a-2c2b17d0b52c", "5c29dcbb-59a9-fdc7-b30e-cbd04239bea6"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 1, "uuid": "d2c76e4a-1047-2ec1-dcc6-a923a2a178e8", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 2, "uuid": "39950ad5-b8bd-8bc3-299b-d462bf898863", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\recalibrated\\r_loboan_scream.png", "name": "r_loboan_scream.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "9cc058fc-f540-7b35-403f-9fa57ca89c65", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/recalibrated/r_loboan_scream.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\recalibrated\\r_loboan.png", "name": "r_loboan.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "ea167b4e-39df-a661-3c86-c0925ed96019", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/recalibrated/r_loboan.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_loboan_scream.png", "name": "ov_loboan_scream.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "57736c70-43d5-ee2d-b192-1af56db7391e", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/ov_loboan_scream.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_loboan.png", "name": "ov_loboan.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "4c9627be-5eb1-8030-e74b-41634bc582e0", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAHgJJREFUeF7tXW2MHtV13q2DTcFeY7K24wgTUFxbGCuEgtRWpkBCUlIpCmBspYJIlSBx6qppjELktm7BsWRVVlLhCFSr5qNSFaxEJnxUtA1tSKDBqiqRklQUhOsI2o3q2CzYGBNiHETlmT1m5+yc95z7NXdm3uf9s/u+9/u55z7nuWfuzIyOZP7MW3jOOzm7cO4lq2ub/98f7q39neeX8lFhyk/5ePn/+s63RnOOH20PNwLZjQ8EAAIY7iWYd/QgACiAvBaI1rMiAAIAAWQ1QDSeFwEQAAggrwWi9awIgABAAFkNEI3nRQAEAALIa4FoPSsCIAAQQFYDRON5ERh6AtDg/+WxV7UsA9PfM/fsgemvv/zT7HMQNEAU7jQC2Y0v9zkAbfZAABpCSO8yAiAAZfZAAF02b/RdQwAEAALQbATpPUYABAAC6LF5Y2gaAiAAEIBmI0jvMQKdI4A58xcOnI7jr70cdboQA4gKJyprGQIgACiAlpkkutMkAp0hAPL8i5ctH4jPwf37ivRYSgAKoElzRFtNIwACgAJo2ubQXosQaD0BWD0/xzSWEoACaJG1oivREQABQAFENypU2B0EQAAggO5YK3oaHQEQAAggulGhwu4g0FoC8N37x44FIAbQHWNGT90RAAFAAbhbDUr0BoHWEUAszx9bCaSacTwPIBWyqNeCAAjAglLCPCCAhOCiahWB1hBAKs/fdiUAAlBtFBkSIgACSAiupWoQgAUl5EmFQHYCGF92cfFuQO2Mf2wAYp0UDO0XCCAUQZQPQQAEEPn2YdfJAAG4Iob8MRHITgAXfuLTWd8OrL3dNybYdXWBAFIjjPoHIQACEF4D3pTZgACaQhrt1CEw2vRjufkTfVz3/prHPld40Yc0/RQLoPRYzxHoirnFJqDfvW6wovunh9r9OvSu99/V7kAAUw8QAQG4mk59/q4voK7333UWGycA3kFNEZDHH/3lLypF33nP6ZXvUjpXBMPu8Tn+sRQALZzzln+o1gZf2vefxe9dUQBdH4eVCEAAma8CWCcqVT4QQBXZvhCZ1V6yE4CmCLg0J08vKQD6XXp68LDt8TVDCCUAvmBWLC+f2fjCvvLZjF3x/Bynvo6LjxMEoK2QnqeDAAbHMmgr0Bdiaz0BaOvNqgC0epBeIuBLAJKH5LjuunN78dNzP3o6+yVnlzlf+eFLi/Mp67+wqbZY1xUODap1CkCbJBCAhpBbOgigHi8QgJsdeed+6+jBgWVnjy0u0vnCl6L+PJ+1fu8BdLxgKgIgz0/wdFUBUP+5EoACiGT41gUKAogEOKsGBDBYAYAA0tjdqVqJAG7/50trW/rK7zxd/D7njPnFX4ryawrg+M9fK/Jr9ZLCSDzM1lbvSwAkkT9w/geLsVGw7MnHHiq+d83jaxNE473i6uuKrHR1439e/Emnx5s9BgAC0EwvbToIwIbv0BGAdKY+9n30dL3+9Ylnaz02KQDy1JddtrrIt/eJxyszt/rKq4rvTz21t/grEQvVN2/pqiJfrHMB2hONtHsYbGYYP5cvAVBPaGHwnvVVAfRtnKICAAG4LTYQQBUvEICb/eTKPWq9H9/1DL12Vx73iFwJcEDIY6+7fm2R9Pzzz1eyXHDBBcX3Pd9+oPhLikKqh3t+1/7yerV7GqQJzq0MQhVALsNFu3EQAAFM4QgCiGNQqKVbCJzaAtAC4J6eD8e6Z9YkMdU78e/lXl46w0+efNlH1hT5Lr+ovNtMUgD/+uPyrrP933+w+EvKgdqj/tNVhKW/UcYOpI9rzEO6B4Hqp+cf5Pb81B8ogG4t2Ni9BQGAADp1RDf2Ahj2+sQgIPdkVs/PAdWUAHnCz9y0oVKUe/KbNm6ppEsKgDLdt6PMz5UDpX/jvp3Fv6mCnbHwS22gXVMAGzZtK87oS/O/c/tmEJqD0YAAhEeIuUp/ifj41sNhbhrJCgJoBObWNuJ9EIg8nFUZaFHyhbNPK0CiaL6GmKYApPJU7uW3TlSyuF7loMKuOGjjajq9KwRAnp+UHeE0tuDsCmRrf/+Pi+9QAjZLAgFM4QQCsBlMrlwggDTIOxNArL0tjw3w6L6mBFwVAOWneinGMCxSXzKftisAWvj0QA4ax6Y/vKH4lxTA5m13VYZId+tBCQwmDhDA1FOBrVuZru71QQBpPGjXazUfBZbOB/guHAKOovC5FEDo9Xjpuj9/30FoO5qhEY6u7XRVATz8yCMVSK695hooAM1IatJBAIFvBgIBeFidQxFpCwACcABxQNYZBKAdiW2LErDGAKS9v6un1KQ/pWtvOgptV5pLTQFI6W1XADTeKz/1Gad3SD7x99/AeQADR4AADCDVZbF6fl4WBOAHOAjADzetlBoE1E7KSQ34xgb4iUDu6bUB8XR+NYFOALrWYz3j77vgQ88TWBUA9Y+IqCsKgPq94949hRKQtgAbb14Hz+9g3CAAI1ggACNQibOBAOICrBIAb66pGAFXAvw6sDUIRP139fxNSXx+FyYpJ82j07ik8tq8USxncv8znfKYUlAQ1/39iAEEIOAGAvAzqNSlQABxEQ5m/8Ubxp2is7z7a8ZuCRpR7pNe8xaeEzT+oMGPjIycsbb61uTQ+lzLH9w5GWxDrm1Oz0+E4FvH/bvuqBS9+trfG1jV+KIllfTc9uc7bioXPHkgABBAqBGGlAcBhKA3MgICCMNvBAogrwKgoCDFAFyn01UBUP2kBKAAsAXAFsB11UXMDwIIAxMKIAw/KIDMMQAiAD6NvoqA6pk8dKD4lzw9fYcCYEgjBoAYQCCHBhUHAQTBhxhAGHwjUABDogAkO0EMADEAxABCWTSgfFMKAAQgIIAtALYAAes3uCgIIAxCBAHD8MMWAFuA4DUUaIJBxYM7DwUABRBkgYGFUykA6WoA7y5iAIgBIAYQuIhDioMAQtDDScAw9E6+e1C4FyD0/n7qmFbPsN8LEEoA/CTgjeur96bw8wA8/9HDeU9ChhowtgCBCIIA8i4AEECYAYMAwvCboQBivTeBKwD6zp+0BAVQPiGIPq4nAKEAAhcAgoDVICAIINCgHItzBQACcANwNHQBU3PvXfm+4t8jTxwu/p515YLanrzy3M/ceqjkfvtfltbmmPXxCVM7s7/zflM+KdObs2YVSdKTfPgbkKSnKtPThPmbiqQn/lC9v/r220H9H1txQVFeald7iCl/QpSUn+ej9v78T/8sqP+uCz6osQSFc19FAAGAAEAACRa2tcrOEwB5fhowVwCxPT4HNlQBHLnvNetc1eabPba4+J3vxcdfvKr4nTwi9+QPPbKzUt8ffKl8Mo3kiSfPf7yS/+cPnF58f+vowaD+L/vImtp2uXKxenZrPhrnuuvXFu3zZz5aBwUFYEWqPl+wAgAB2Alg3+MPmmZr/gdWzdhSgADqoQMBmExKzDS68s5VUQ+ySDEASQlwAnEdzqGvn1ZbhGIAWv0/ufWFovz2v95tapreSkuZSQEsve2s4ica56xDc4rvby86fqreV+8p1cZXvntpbVu3f+zp4vezPzv/VDqvh8YzsfVIrQLwHQcpgf3fL0lq3tJVxV+KTWieXXrLsrT3pwGSAqDvrkpAIgBrPaEEEtpO9i0ACAAEcHLxgQBM/D8jEwhgChIt+s+RI0+peWhtWrgCWPTFE1qRSnqoAvjuf/ygUt/nv/fZ4jvhQQrggT96dOSjH/7tU3m5CiDvfzLD9370g5G1d32yyEsKgK6q/M1H76m097Fff7fOkwm+CoCDRgqAfudKQPLs/GoIledXP16feLZIumnjlkrT1gVFhbgHt5YP9fyxFEtvFAAIoDQJKwGczEskMH3xgwCWOxE4CMAJrhmZo8UAchFAqJKIrQCuu2ZDAfLsiw4Vf0kBbLv6r0Y2rCvVAX0kAti5556RzY99qaIA3vrxouI7v3qQSgFQH0kJkMemmAe/qkH5Kd+i95fnC/g5A0qn/FwBWD1r6MLnZ/zDlpH7VYy2vMkIBBAYBORbACsBDNoCgAD0BQUCCKWssjwIIBEB1J3Zd7kKQNf5+d2A//jEA5WZT60AJDPjMQLu2UkB0ElJnt6UApD2+rEVgISTFJOAApg6EhwaBGzrFgAEUG4BQAD1MY3WEMCcOac5nQMYW3D2QO1x9PCrcbRJw7XQXlSLItM5AC79qbsUBKTr9NM9+C+O2E7tnX5Webho+mfe5eU0fWvrt2uRISWgXQUgw7tvRzX67go3jw3w8qQA6PdD//d8bRNSDECLBWhbAErnz/PnneDv+nPFwVo/tysQgCvSifM3QQDTh3DH3dsqI/qTW3cU37lyoEwggKoBgADiLIhRKIASSM0TEdzkOTUFQFdFKHrPFzbf20vf6WqCdA6A+kUKwHUccczo3Vq4529KAVA73COvvuyKgUMMPQ+gKca9Tz1Zab9tbxcGAUxNj+vCAQHUrysQQBUXEEBsF9OS+qwEQJ6bxwRoGKQMuAKgewv4+Qp+EpArgNzwuBLAp274fG2XyVNqUXRtvJqHTq0AtPqznwTEFkAzofp0EEAcBQAC2Bz8WD4/Cy5LYQvgiZ4rAfBmuCKQ7mHouwLgWy/p+rwURZemT/P8VE7z0Jp5hLYDBaAh3NJ0EEAcBQAC6JgCaOl6bLxbnADo7j3rsxHJs1PHpWcoSk9YOnl34fQPPxHYOCBTDbrGAKTgK1cC5GmtHjvUM1vxC22ncwrACkzf84EA0igAqhUE0MwKco4BNNOt9rciEQD1nN/Hz0fkqgCmP1noZF3DpgB8LULz0FZF4Rtr0OqHAvCd2czlQADNKgDf6QYBDEZudMOmbU73AvDqCGA68CBdv+Vns+mElsaQ2sS77g2pPirHn/GntScF/ygGwBUAeW7p4alSDGDQswXrFADVkzsWECsGoM2DNR0EAAKoRQAEYF1CbvlAAFW8NAeXfQsQqgC4R+VKgAd16HtqBaBdN6b0S36z/s1CVrPnnl9SALw+ihHQ73yPb62HxwKs/dby+SoJiQB2P7qrtsndf1e+78BXyWnjkOqN1V5o/SCAffu0ORyYrk0AFZbuHgMB1MMLArCZpWZ/Q6cApPuweWxAA8YG/8xcrnu+B4+Wb+SJ/al7L8D0NmIpgNj9pvpiKwtJKa0Zu6WiACTCto5TUn78LsHQ5wDw/kj1a0q0dwoABFCaBgigukRAACUe3PH1hgBoukMlkZXpeT7N8/P8NBFQAL6IxymXWgFIvZRiVa6jIs+vPXdAUjYgAFfEhfwggEhANlwNCCDzvQCxrgJwBSDZUey9v7THsv7+l0+Xz/GP/en6FiA2HlJ9EgFIHlPrl6sj4A/s0Orn6VbPP3QKAAQw8+Wg0zFpexDQdSH45gcBQAH42s7Aclr0lQpf8lvnJGn/lCL6tRVB9b/w3+XLS/v6+eG//bQYmuS5fRWjqxJIja80jt7GAJpSAFI7IIDUJh2nfhBATxSAtuC09Djm9G4t1vagAGIj71YfCAAE4GYxxtwgACNQmbOBAHpGALTX0aLwvns7q73mJoAVgXt/Ps6+xgKOvXLGwCn1tRMtphDrXoBTsZ7lg19r3vsYAAcUBBAW/AMBlAiAAKwuzy9f8PMAeLMP7767+GnTlq9WkrZv+XLx/dobPufXU2MpIh5qj4pRf7hBpYoBQAHYJuwvvnB7rb1QaYkAyM4oH7crSQGktkNNeVB/qf8HJl7K+1jw2AeBQADlFIMAQADTEeBE1jsCoAG9cexYMe6tX9tZsYDbbi1P3J05d25SJcAVAO8Pn4jY9wJoJwAJFOtBIJ5fem6AbbnFyzX+4lVFZQf3l7dzz/r4hFflv/JoaQ9cMWoK4P5d5V2cmj1xj5zaDq0KgPp/9PBkPxQACKA0WRCAGw+AAEAAbhbDcnPGpT0eeX7KTp6Ce5rY9wIMKwGcUiqOSuDNb5YlpfmRpLM0v1osoGkFoPUfCiBo+c88QgoCCATUWJxvAUAAJQLa5WeulDtPAHxAZAg3ri+f9EIf2vNwjxx6VcCqAKhdHpuAAjCu+KlstPCpFMUA2koA1C+yE1IAkp26oTEzNwhgChMQwPGBttTVICAIYDBFdI4AQhkvdvnZY0sq7yk4fdaJShO5JVPs8aI+IJATgayXIOoGDgLIaQ5oe9gQAAEM24xjvEBgGgIgAJgDEBhiBFpHAEM8Fxg6EGgcARBA45CjQSDQHgRAAO2ZC/QECDSOAAigccjRIBBoDwLZCWBswXjlur92nd81f3ugRk+AQPsQAAG0b07QIyDQGAIggMagRkNAoH0IgADaNyfoERBoDAEQQGNQoyEg0D4EQADtmxP0CAg0hgAIoDGo0RAQaB8CIID2zQl6BAQaQyA7AYSOdHzZxZVzBFTf5P5nOj+2UGy6XJ4eV5/77bldxtDS984vEhCAZZq7lwcE0MycgQCawRmtGBHYce+eQtHxp+lCCRgBdMwGAnAEDNnTIgACSIsvrx0E0CzeaE1BAATQrImAAJrFG62BAFplA70jAET/W2Vfzp2BAnCGLKgACCAIPhSOjQAIIDaig+vrPAE0CxdaawoBTgS4CpAGeRBAGlxRayACIIBAAI3FQQBGoJANCPQRARBAH2cVYwICRgRAAEagkA0I9BEBEEAfZxVjAgJGBEAARqCQDQj0EQEQQB9nFWMCAkYEQABGoJANCPQRARBAH2cVYwICRgRAAEagkA0I9BEBEEAfZxVjAgJGBEAARqCQrVkE6CjwxpvXwUYTQg9wE4KLqv0RAAH4Y+dSEgTgghbyNoYAEQA1CCWQBnoQQBpcUWsgAiCAQACNxUEARqCQrRkE6HHgK5YvrzQIBZAGfxBAGlxRqycCIABP4DyLgQA8gUOxNAiAANLgKtUKAmgWb7SmIAACaNZEQADN4o3WjAhwIkAMwAicYzYQgCNgyN4MAiCAZnAGATSDM1pxRAAE4AiYZ3YQgCdwKJYWARBAWnypdhBAMzijFU8E8JpwT+CMxUAARqCQLQ8CIIC0uIMA0uKL2oFAqxEAAbR6etA5IJAWARBAWnxROxBoNQIggFZPDzoHBNIiAAJIiy9qBwKtRgAE0OrpQeeAQFoEohPAkqXnvXOyywcmXqrULf2eanjUHtXP+5OqXdTrhoCvXfjOr297bqPSc7elHyAAfa6QIyECvgsBBBBnUqIRAE3IG8eOFT07eniyUvfYgvFCGZw5d26RntojN91enOnoTi0XfuLTxXwe3L+v6PTk/me8bMmXAHzn17dc7JmhfvB1ErsdrT6vSaurFASgQd2vdBBA2HyCAFiMIAzOd0tzIqKUppRHrHG0vR5OANRfVyXg6pFD59e1vdjzoDnK2O1p9fVOAYQaiAYY0ksEQAB+ltA7ApAWnBQDSO2RieH59EAB+BksL0ULn36nGEDXFAD1t+k9OAhgCvlUCxIEEGehS7WAAMLw7R0BhMExs7S0gHMxtja+xRvKqxu+nzVjt/gWLcrt3L452jbOpyN0u65P2boyk4cODKzqsYe/WUlv2oPHGmdb6slqPHUggADcTAMEUL3c7IYecoMAAm0ACmCblwKiN/+8sG/qHIHi+WmaoAACDZYVBwEE4gkCAAEEmlDW4q0jgKxoeDQOArARAH/XH0EtKYDxRUuKLBQToO98inJvgTxMplVFQACB0wECAAEEmlDW4iCAQPhBAHEIQJoGKIBAA1WKgwAC8QUBgAACTShrcRBAIPwgABBAoAllLQ4CCIQfBAACCDShrMWzEwA/+KOd7HLNnxpdEEAaAtD2/jSvuAoQZuEggDD8RkAAIIBAE8paHAQQCD8IIIwAbrt1Q2UGblxf3hshKYD7d91Rya8pxsDp7X1xEEDgFIMAQACBJpS1eGcJ4M0TswrgThw7mHUMIAAbAZCV8xOBUABZ1/9I1sVzcuiuQT3KDwIoDSd3EMz1dmAQQN4Fz1vPTgCp4OAPrpDamTz/8ahdeO/K99XWd+SJw8XvZ125oPj7ynM/i9our2z8xatM9V9+0YdM+VJlku4RsLZH9xJY8/N8uQlU63fq16ODAEAAmg0mTQcBDIYXBGA0P6vH59XFVgC8flIEXAFQvlhKwOrxef+6qgBCPT/h0FYFwLdWRJQbb14X1WlHrcy4VpNkAwHYJD8IoIoACCDJcmyuUunx1IuXLS86QU+tPf7ay8X3cy9ZXfl90RdPBHVW8uA8FiApgKDGR0ZGRv/hwsp4qD7r+NddvzaoC1YJL3lsa3nqpNXzW+uN7VGDwJxWGArAiCQIAARQZyogANsC6vwWYHzZxcUz6SQPL/1O8MRSAFL0n09DbCVw6OunFU34jj9UAdD4rAuOe3BrOasCcK0PCsBGFK3NBQIAAUw3ThCA21LtvAKYt/CcQgHwvT3BQJ5xzvyFtch0XQFMbD1SG9uwjj9UAfAz+9YF6KoEtL2/tV2uJNoeBEwV/SccQACRgoC5tgAggNKUQQBunr93BMA9PPf80ltrV965yuu59gQgXQXITQDa+LnSoX6HvplIu29fexqwNYbgqwC0cm1VAH7L2b1UbxSAtgBAANXLnSCAcrGAANxJo1UlKAbgSwBz5pwWpABcwRhbcPbAIkcPv+pU5eyxxUV+bfyvTzxbW+9NG7c4tccza+/yo+f5cyVgjQFoHlxSENY3Du3527s67wRDJrDzgwcBgADqYgAgABstdJ4AaJicCKwxgK4rABo/VwJ8/KkUgBZ82/vUkxVLlN7wY40VaGbNFcnqy64YWKSt5wC0ccZKBwF0fAsAAqguBRCAGzX0hgD4sPkBIdoj82BgXxQAH/+8pauKnzQlEBoD0BSA7x6exqNJeUlRSLEBjhMUgBthdCY3CAAEUBcbAAFUEeitArAyVV8VgHX8qRUA9+RSv7SrBNp5A16vpkwoPxSA1VJ6mg8EEHYZ0LrQtK0ACCDPAoMCaDgImGea5VbbogD4np3v/bW9fl8VwI579xTnVFIpFRAACCCIk2IpABBA/TSAAILMUy/c9BZA71GzOVIrAE36a6N13fu7KoFUnlUbl5ZOCz91rAIKAApAs8WB6ZoCAAH4wQsC8MPNuZTriy0kD0Mn3rSz7+TR6IRa6AKhBehaj285Vw/r2i/nCVQKaATVFQVAOMa+eWnoFQAIYF/QmtMWGAjAD16uAEAAfjiqpWITADXIo9axPb8UNOO/03fp7rvQBSopiVgKQ1Ic1n5r/YMCUJdIvzOAAOIogFQEAwIoEYACSMRDoQQgeWJ+UwqPDVg9mOuwc0lybbySErGOTzsopCkfqX+x99TW8Wj5aAuQauFT+4gBbHJ7vbU0cVxqagsCBKAtgWo6CGBzkrWapFK3qc2bO5YCsO7Jcy18jnKsfvDYhjabvu36EoDUH7pq09YnAkEBaJYUKR0EEAYkCCAMP6k0CCANrjNqTUUAUvd9PaC29eB7bMlj8t/5E3tcYdeeuBOqPFyfFKTFQHh/2noVIPVrwREDmEIABFB9ZBcIwBWBNPlBAGlw7Z0CcFUGPL+rx/SdFl/l46oAeCxG629bFYDW71jpCAJGugpgNTzfheA64bGDZq7th0p/DU8NRyuxgQBCZ7bj5WNtAbQFp6XHhtHannWhhPZPW7BWJcNjHa7luhIDCMXbWh4KIJIC0Baclm6dMGs+a3sggHVDvQaGevAnF1NsBUCeTovC+3rE2ARgrU/ztNK4rR5bq19Kl3DUiC31CbtQXJsqDwKIrABAAMtrbdeX8KwLmTdqLdfWo8CdJYAlS88rnmF2YOKlCrlIv6caKLVH9fP+0O+xFADV9/Duu4t/N235amVo27d8ufh+7Q2fSzXkol4yfGqPGqP++C5ErdPSuCUFQPkpXcJFWsh8fLy89epBLAJwtW+rfUq4h5aneqMrABAACKDOaDnxgABKR6k5qM4QAC38N44dK/p89PBkhVzGFowXAz5z7twiXfLImqexplvbi6UAyKBp/Fu/trPS1dtu3VB8p/GnUgLk+Xh7qRSANm5JcfByN66/pXZqNQVAePPyTSkAze61BSytF83Ofdvl9UZTAFqHrAtSG7g13doeCMCKaH0+EEDpyV0XsrZetFkJLR99C6B1yLogtYFr6bwflF9SHl0nAO7paG9MBsnHz2MToTEBVwLg+Xn/tL28Nj6tPB9vaAxAs3tur672Gbt87xWAK8AggLAnAoEA3BSAq322ngCkAUkxAM0jax5eSyelwfOlUgCSR+N70vt33VHpUqxYAFcAtPeXxh9bAfBxUezDuvfvugLg9sbtPvYCDiWQ6AoABFBG/bnkBgHUK4u+bQG6TgD/D08IXYehudb7AAAAAElFTkSuQmCC", "relative_path": "../textures/models/ben/ov_loboan.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\loboan_scream.png", "name": "loboan_scream.png", "folder": "", "namespace": "", "id": "5", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "4b576615-290c-ca8a-a766-6a50fc07531e", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "selection", "offset": [89, 110], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 16, "height": 14, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAOCAYAAAAmL5yKAAAAAXNSR0IArs4c6QAAAHZJREFUOE9jZKAQMOLS//r58//4zBaVlATrBROEFGMzaIgZwMrJyfD7+3cUn+D1AkgDMhAQEACFE/EGiEpKMnz48AGsAaQZGtB0NIBiL6AbAHI73kCEeg6c8mB+R088RBtARNZASf5Y84KivAJKPrj/8AHOPAMANflMDyLA55AAAAAASUVORK5CYII="}, {"name": "pasted", "offset": [90, 113], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 12, "height": 12, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAG1JREFUKFNjZEADDnb2/x8+fAgXvf/wASOyEhjnP7pGHHxGFA0fPnzAqk5AQAAmToYGRXmF//cfPoCbjG4LkukMivIKDIzoGkA6YZqQFYPEqaOBoJOgjgcHK21CCdkGIiIPHg9wtaBQQ9aInjQAXbA6iUugbpoAAAAASUVORK5CYII="}], "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/loboan_scream.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\loboan.png", "name": "loboan.png", "folder": "", "namespace": "", "id": "6", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "accbbc54-acdf-44ce-f98a-25f9226c807c", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "selection", "offset": [90, 113], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 12, "height": 12, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAG1JREFUKFNjZEADDnb2/x8+fAgXvf/wASOyEhjnP7pGHHxGFA0fPnzAqk5AQAAmToYGRXmF//cfPoCbjG4LkukMivIKDIzoGkA6YZqQFYPEqaOBoJOgjgcHK21CCdkGIiIPHg9wtaBQQ9aInjQAXbA6iUugbpoAAAAASUVORK5CYII="}], "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/loboan.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\5yl\\loboan_scream.png", "name": "loboan_scream.png", "folder": "", "namespace": "", "id": "7", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "9be8650a-f74f-8337-6b7e-a294cdda530c", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/5yl/loboan_scream.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\5yl\\loboan.png", "name": "loboan.png", "folder": "", "namespace": "", "id": "8", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "19a93cce-71bc-ed0b-088d-0cc839c966c8", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/5yl/loboan.png"}, {"path": "C:\\Users\\<USER>\\Downloads\\ov_loboan.png", "name": "ov_loboan.png", "folder": "", "namespace": "", "id": "10", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": true, "uuid": "c0b149d6-8696-ba28-6c0f-f5c85d50f3f5", "relative_path": "../../../../../../../../../Downloads/ov_loboan.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio_loborosian.png", "name": "bio_loborosian.png", "folder": "", "namespace": "", "id": "11", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "79c40ed3-337c-8b13-b6cc-382c2def35aa", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/bio_loborosian.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_loboan_oryctini.png", "name": "ov_loboan_oryctini.png", "folder": "", "namespace": "", "id": "12", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "d1e7df74-a250-f02d-eca0-78fdbc849cf4", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/bio/ov_loboan_oryctini.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_lobofriggian_flying.png", "name": "ov_lobofriggian_flying.png", "folder": "", "namespace": "", "id": "13", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "62fd2360-7e37-01f5-7a44-aafa9e00c142", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/bio/ov_lobofriggian_flying.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_lobofriggian.png", "name": "ov_lobofriggian.png", "folder": "", "namespace": "", "id": "14", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "5a469ccb-52d5-ffdd-bd19-f2795f051a45", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/bio/ov_lobofriggian.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_lobofriggian_mouth.png", "name": "ov_lobofriggian_mouth.png", "folder": "", "namespace": "", "id": "15", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": false, "internal": true, "saved": false, "uuid": "31af0c48-f46e-32fe-48bf-b3296373a104", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/bio/ov_lobofriggian_mouth.png"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json", "itemModelPath": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\geo\\gun.item.json"}}