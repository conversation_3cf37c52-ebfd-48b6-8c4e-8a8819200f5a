{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "Celestialchrono", "model_identifier": "Celestialsapien Horns", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 25, -4], "to": [4, 33, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 1, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 0}, "east": {"uv": [0, 8, 8, 16], "texture": 0}, "south": {"uv": [24, 8, 32, 16], "texture": 0}, "west": {"uv": [16, 8, 24, 16], "texture": 0}, "up": {"uv": [16, 8, 8, 0], "texture": 0}, "down": {"uv": [24, 0, 16, 8], "texture": 0}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 25, -4], "to": [4, 33, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 1, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 0}, "east": {"uv": [32, 8, 40, 16], "texture": 0}, "south": {"uv": [56, 8, 64, 16], "texture": 0}, "west": {"uv": [48, 8, 56, 16], "texture": 0}, "up": {"uv": [48, 8, 40, 0], "texture": 0}, "down": {"uv": [56, 0, 48, 8], "texture": 0}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 12, -2], "to": [4, 24, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 32], "texture": 0}, "east": {"uv": [16, 20, 20, 32], "texture": 0}, "south": {"uv": [32, 20, 40, 32], "texture": 0}, "west": {"uv": [28, 20, 32, 32], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 12, -2], "to": [4, 24, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 0}, "east": {"uv": [16, 36, 20, 48], "texture": 0}, "south": {"uv": [32, 36, 40, 48], "texture": 0}, "west": {"uv": [28, 36, 32, 48], "texture": 0}, "up": {"uv": [28, 36, 20, 32], "texture": 0}, "down": {"uv": [36, 32, 28, 36], "texture": 0}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 12, -2], "to": [9, 24, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 0}, "east": {"uv": [40, 20, 44, 32], "texture": 0}, "south": {"uv": [52, 20, 56, 32], "texture": 0}, "west": {"uv": [48, 20, 52, 32], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 12, -2], "to": [9, 24, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 0}, "east": {"uv": [40, 36, 44, 48], "texture": 0}, "south": {"uv": [52, 36, 56, 48], "texture": 0}, "west": {"uv": [48, 36, 52, 48], "texture": 0}, "up": {"uv": [48, 36, 44, 32], "texture": 0}, "down": {"uv": [52, 32, 48, 36], "texture": 0}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9, 12, -2], "to": [-5, 24, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 0}, "east": {"uv": [32, 52, 36, 64], "texture": 0}, "south": {"uv": [44, 52, 48, 64], "texture": 0}, "west": {"uv": [40, 52, 44, 64], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9, 12, -2], "to": [-5, 24, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 0}, "east": {"uv": [48, 52, 52, 64], "texture": 0}, "south": {"uv": [60, 52, 64, 64], "texture": 0}, "west": {"uv": [56, 52, 60, 64], "texture": 0}, "up": {"uv": [56, 52, 52, 48], "texture": 0}, "down": {"uv": [60, 48, 56, 52], "texture": 0}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 0}, "east": {"uv": [0, 20, 4, 32], "texture": 0}, "south": {"uv": [12, 20, 16, 32], "texture": 0}, "west": {"uv": [8, 20, 12, 32], "texture": 0}, "up": {"uv": [8, 20, 4, 16], "texture": 0}, "down": {"uv": [12, 16, 8, 20], "texture": 0}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 0}, "east": {"uv": [0, 36, 4, 48], "texture": 0}, "south": {"uv": [12, 36, 16, 48], "texture": 0}, "west": {"uv": [8, 36, 12, 48], "texture": 0}, "up": {"uv": [8, 36, 4, 32], "texture": 0}, "down": {"uv": [12, 32, 8, 36], "texture": 0}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, 0, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 0}, "east": {"uv": [16, 52, 20, 64], "texture": 0}, "south": {"uv": [28, 52, 32, 64], "texture": 0}, "west": {"uv": [24, 52, 28, 64], "texture": 0}, "up": {"uv": [24, 52, 20, 48], "texture": 0}, "down": {"uv": [28, 48, 24, 52], "texture": 0}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, 0, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 0}, "east": {"uv": [0, 52, 4, 64], "texture": 0}, "south": {"uv": [12, 52, 16, 64], "texture": 0}, "west": {"uv": [8, 52, 12, 64], "texture": 0}, "up": {"uv": [8, 52, 4, 48], "texture": 0}, "down": {"uv": [12, 48, 8, 52], "texture": 0}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "cube", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 34, -1], "to": [1, 42, 1], "autouv": 0, "color": 0, "visibility": false, "origin": [0, 25, 0], "uv_offset": [56, 16], "faces": {"north": {"uv": [58, 18, 60, 26], "texture": 0}, "east": {"uv": [56, 18, 58, 26], "texture": 0}, "south": {"uv": [62, 18, 64, 26], "texture": 0}, "west": {"uv": [60, 18, 62, 26], "texture": 0}, "up": {"uv": [60, 18, 58, 16], "texture": 0}, "down": {"uv": [62, 16, 60, 18], "texture": 0}}, "type": "cube", "uuid": "704386d4-c64d-8236-e5e8-651bc0a273b0"}, {"name": "cube", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [7, 18, 0], "to": [11, 29, 0], "autouv": 0, "color": 0, "origin": [5, 22, 0], "uv_offset": [56, 37], "faces": {"north": {"uv": [56, 37, 60, 48], "texture": 0}, "east": {"uv": [56, 37, 56, 48], "texture": 0}, "south": {"uv": [60, 37, 64, 48], "texture": 0}, "west": {"uv": [60, 37, 60, 48], "texture": 0}, "up": {"uv": [60, 37, 56, 37], "texture": 0}, "down": {"uv": [64, 37, 60, 37], "texture": 0}}, "type": "cube", "uuid": "34898a9a-e57f-9a76-b107-9fbda8aaa900"}, {"name": "cube", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-11, 18, 0], "to": [-7, 29, 0], "autouv": 0, "color": 0, "origin": [5, 22, 0], "uv_offset": [56, 26], "faces": {"north": {"uv": [56, 26, 60, 37], "texture": 0}, "east": {"uv": [56, 26, 56, 37], "texture": 0}, "south": {"uv": [60, 26, 64, 37], "texture": 0}, "west": {"uv": [60, 26, 60, 37], "texture": 0}, "up": {"uv": [60, 26, 56, 26], "texture": 0}, "down": {"uv": [64, 26, 60, 26], "texture": 0}}, "type": "cube", "uuid": "7ae9b37a-11ac-8945-1b50-b47f416fb59b"}, {"name": "Horns", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 32, -5], "to": [1, 38, -3], "autouv": 0, "color": 0, "rotation": [-2.4272711214772933, 0.2866813787678053, -0.5316775701867144], "origin": [0, 23, -4], "uv_offset": [16, 0], "faces": {"north": {"uv": [16.375, 9.875, 17.375, 13.625], "texture": 0}, "east": {"uv": [16.375, 9.875, 17.375, 13.625], "texture": 0}, "south": {"uv": [16.375, 9.875, 17.375, 13.625], "texture": 0}, "west": {"uv": [16.375, 9.875, 17.375, 13.625], "texture": 0}, "up": {"uv": [20.125, 12.75, 18.125, 10.75], "texture": 0}, "down": {"uv": [20.125, 10.75, 18.125, 12.75], "texture": 0}}, "type": "cube", "uuid": "50e303bd-69b3-e491-e9e7-2651827b9e15"}, {"name": "Horns", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 32, -5], "to": [1, 36, -3], "autouv": 0, "color": 0, "rotation": [-2.228905536103415, 1.003130062201052, 16.954881105644265], "origin": [0, 23, -4], "uv_offset": [16, 0], "faces": {"north": {"uv": [18, 2, 20, 6], "texture": 0}, "east": {"uv": [16, 2, 18, 6], "texture": 0}, "south": {"uv": [22, 2, 24, 6], "texture": 0}, "west": {"uv": [20, 2, 22, 6], "texture": 0}, "up": {"uv": [20, 2, 18, 0], "texture": 0}, "down": {"uv": [22, 0, 20, 2], "texture": 0}}, "type": "cube", "uuid": "91029d3d-d7cb-b96d-1b3f-305ad01a69f4"}, {"name": "Horns", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 32, -5], "to": [1, 36, -3], "autouv": 0, "color": 0, "mirror_uv": true, "rotation": [-2.228905536103415, -1.003130062201052, -16.954881105644265], "origin": [0, 23, -4], "uv_offset": [16, 0], "faces": {"north": {"uv": [20, 2, 18, 6], "texture": 0}, "east": {"uv": [22, 2, 20, 6], "texture": 0}, "south": {"uv": [24, 2, 22, 6], "texture": 0}, "west": {"uv": [18, 2, 16, 6], "texture": 0}, "up": {"uv": [18, 2, 20, 0], "texture": 0}, "down": {"uv": [20, 0, 22, 2], "texture": 0}}, "type": "cube", "uuid": "6120ca46-a00f-1ce8-dd86-d8e4e0cc999c"}], "outliner": [{"name": "armorHead", "origin": [0, 25, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", {"name": "antenna", "origin": [0, 1, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["704386d4-c64d-8236-e5e8-651bc0a273b0", "50e303bd-69b3-e491-e9e7-2651827b9e15", "91029d3d-d7cb-b96d-1b3f-305ad01a69f4", "6120ca46-a00f-1ce8-dd86-d8e4e0cc999c"]}]}, {"name": "armorBody", "origin": [0, 24, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565"]}, {"name": "armorRightArm", "origin": [5, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", {"name": "wingRight", "origin": [5, 22, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["34898a9a-e57f-9a76-b107-9fbda8aaa900"]}]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 22, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["7ae9b37a-11ac-8945-1b50-b47f416fb59b"]}, {"name": "armNested", "origin": [-5, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\celestialsapien_chronosapien.png", "name": "celestialsapien_chronosapien.png", "folder": "", "namespace": "", "id": "5", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "4b187eb4-cbc5-6bb3-e35f-99abe43ddd8d", "relative_path": "../textures/models/ben/celestialsapien_chronosapien.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": true, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}