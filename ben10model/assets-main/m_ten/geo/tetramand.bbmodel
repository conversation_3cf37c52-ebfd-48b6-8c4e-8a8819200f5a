{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "tetramand", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 18, -2], "to": [5, 27, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 2, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 28.5], "texture": 7}, "east": {"uv": [16, 20, 20, 28.75], "texture": 7}, "south": {"uv": [32, 20, 40, 28.75], "texture": 7}, "west": {"uv": [28, 20, 32, 28.75], "texture": 7}, "up": {"uv": [28, 20, 20, 16], "texture": 7}, "down": {"uv": [36, 16, 28, 20], "texture": 7}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 13, -2], "to": [4.5, 27, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 7}, "east": {"uv": [16, 36, 20, 48], "texture": 7}, "south": {"uv": [32, 36, 40, 48], "texture": 7}, "west": {"uv": [28, 36, 32, 48], "texture": 7}, "up": {"uv": [28, 36, 20, 32], "texture": 7}, "down": {"uv": [0, 0, 0, 0], "texture": null}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 12, -2], "to": [9, 26, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [2, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 7}, "east": {"uv": [40, 36, 44, 48], "texture": 7}, "south": {"uv": [52, 36, 56, 48], "texture": 7}, "west": {"uv": [48, 36, 52, 48], "texture": 7}, "up": {"uv": [48, 36, 44, 32], "texture": 7}, "down": {"uv": [52, 32, 48, 36], "texture": 7}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.5, 12, -2], "to": [-5.5, 26, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 7}, "east": {"uv": [32, 52, 36, 64], "texture": 7}, "south": {"uv": [44, 52, 48, 64], "texture": 7}, "west": {"uv": [40, 52, 44, 64], "texture": 7}, "up": {"uv": [40, 52, 36, 48], "texture": 7}, "down": {"uv": [44, 48, 40, 52], "texture": 7}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.5, 12, -2], "to": [-5.5, 26, 2], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-2, 1, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 7}, "east": {"uv": [48, 52, 52, 64], "texture": 7}, "south": {"uv": [60, 52, 64, 64], "texture": 7}, "west": {"uv": [56, 52, 60, 64], "texture": 7}, "up": {"uv": [56, 52, 52, 48], "texture": 7}, "down": {"uv": [60, 48, 56, 52], "texture": 7}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 7}, "east": {"uv": [0, 20, 4, 32], "texture": 7}, "south": {"uv": [12, 20, 16, 32], "texture": 7}, "west": {"uv": [8, 20, 12, 32], "texture": 7}, "up": {"uv": [8, 20, 4, 16], "texture": 7}, "down": {"uv": [12, 16, 8, 20], "texture": 7}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, -1, -2], "to": [3.9999999046325683, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 7}, "east": {"uv": [0, 36, 4, 48], "texture": 7}, "south": {"uv": [12, 36, 16, 48], "texture": 7}, "west": {"uv": [8, 36, 12, 48], "texture": 7}, "up": {"uv": [8, 36, 4, 32], "texture": 7}, "down": {"uv": [12, 32, 8, 36], "texture": 7}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.9, 0, -2], "to": [0.10000000000000009, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.9000000953674316, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 7}, "east": {"uv": [16, 52, 20, 64], "texture": 7}, "south": {"uv": [28, 52, 32, 64], "texture": 7}, "west": {"uv": [24, 52, 28, 64], "texture": 7}, "up": {"uv": [24, 52, 20, 48], "texture": 7}, "down": {"uv": [28, 48, 24, 52], "texture": 7}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.9999999046325683, -1, -2], "to": [9.536743172944284e-08, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 7}, "east": {"uv": [0, 52, 4, 64], "texture": 7}, "south": {"uv": [12, 52, 16, 64], "texture": 7}, "west": {"uv": [8, 52, 12, 64], "texture": 7}, "up": {"uv": [8, 52, 4, 48], "texture": 7}, "down": {"uv": [12, 48, 8, 52], "texture": 7}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 13, -2], "to": [3.5, 17, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 26], "faces": {"north": {"uv": [20.75, 28, 27.25, 32], "texture": 7}, "east": {"uv": [16, 28.75, 20, 32], "texture": 7}, "south": {"uv": [32, 28.75, 40, 32], "texture": 7}, "west": {"uv": [28, 28.75, 32, 32], "texture": 7}, "up": {"uv": [34.25, 32, 28, 28.75], "texture": 7}, "down": {"uv": [34.25, 28.75, 28.25, 32], "texture": 7}}, "type": "cube", "uuid": "10f829d2-4186-9798-8c58-a625734e5d98"}, {"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 28, -4], "to": [3, 35, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 3, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 7}, "east": {"uv": [0, 8, 8, 16], "texture": 7}, "south": {"uv": [24, 8, 32, 16], "texture": 7}, "west": {"uv": [16, 8, 24, 16], "texture": 7}, "up": {"uv": [16, 8, 8, 0], "texture": 7}, "down": {"uv": [24, 0, 16, 8], "texture": 7}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 28, -4], "to": [3, 35, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 3, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 7}, "east": {"uv": [32, 8, 40, 16], "texture": 7}, "south": {"uv": [56, 8, 64, 16], "texture": 7}, "west": {"uv": [48, 8, 56, 16], "texture": 7}, "up": {"uv": [48, 8, 40, 0], "texture": 7}, "down": {"uv": [56, 0, 48, 8], "texture": 7}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6.5, 9, -1], "to": [-3.5, 17, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [-1, -9, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 7}, "east": {"uv": [48, 52, 52, 64], "texture": 7}, "south": {"uv": [60, 52, 64, 64], "texture": 7}, "west": {"uv": [56, 52, 60, 64], "texture": 7}, "up": {"uv": [56, 52, 52, 48], "texture": 7}, "down": {"uv": [60, 48, 56, 52], "texture": 7}}, "type": "cube", "uuid": "4bba3088-cf8c-01ef-d978-387c2c4acae0"}, {"name": "Ritght Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6, 12, -2], "to": [9, 26, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 1, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [40, 52, 36, 64], "texture": 7}, "east": {"uv": [44, 52, 40, 64], "texture": 7}, "south": {"uv": [48, 52, 44, 64], "texture": 7}, "west": {"uv": [36, 52, 32, 64], "texture": 7}, "up": {"uv": [36, 52, 40, 48], "texture": 7}, "down": {"uv": [40, 48, 44, 52], "texture": 7}}, "type": "cube", "uuid": "eb287c72-7457-c5e5-0de3-8305e32a25d0"}, {"name": "Left Arm Second", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 9, -1], "to": [-4, 18, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, -9, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 7}, "east": {"uv": [32, 52, 36, 64], "texture": 7}, "south": {"uv": [44, 52, 48, 64], "texture": 7}, "west": {"uv": [40, 52, 44, 64], "texture": 7}, "up": {"uv": [40, 52, 36, 48], "texture": 7}, "down": {"uv": [44, 48, 40, 52], "texture": 7}}, "type": "cube", "uuid": "6328a29a-bacb-b764-d13a-09f4154a5e84"}, {"name": "Ritght Arm Second", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.5, 9, -1], "to": [6, 18, 1], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, -9, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [40, 52, 36, 64], "texture": 7}, "east": {"uv": [44, 52, 40, 64], "texture": 7}, "south": {"uv": [48, 52, 44, 64], "texture": 7}, "west": {"uv": [36, 52, 32, 64], "texture": 7}, "up": {"uv": [36, 52, 40, 48], "texture": 7}, "down": {"uv": [40, 48, 44, 52], "texture": 7}}, "type": "cube", "uuid": "b52a5610-4e5e-f644-9112-567a5bf0cb13"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 18.5, -1.5], "to": [-4, 21.3, 1.5], "autouv": 0, "color": 0, "origin": [-5, 19, 0], "uv_offset": [29, 22], "faces": {"north": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "east": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "south": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "west": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "up": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "down": {"uv": [33.5, 25.75, 35, 27], "texture": 7}}, "type": "cube", "uuid": "935f059c-863c-22f0-e550-649a51b81fa8"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 20.5, -1.5], "to": [-4, 24.299999999999997, 1.5], "autouv": 0, "color": 0, "origin": [-5, 22, 0], "uv_offset": [29, 22], "faces": {"north": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "east": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "south": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "west": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "up": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "down": {"uv": [33.5, 25.75, 35, 27], "texture": 7}}, "type": "cube", "uuid": "cf7bbe88-beef-1d0c-f908-8d69d9b2f51a"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 20.5, -1.5], "to": [6, 24.299999999999997, 1.5], "autouv": 0, "color": 0, "origin": [5, 22, 0], "uv_offset": [29, 22], "faces": {"north": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "east": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "south": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "west": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "up": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "down": {"uv": [33.5, 25.75, 35, 27], "texture": 7}}, "type": "cube", "uuid": "90381fbd-8525-cfa3-a79e-0e35c1964c87"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 18.5, -1.5], "to": [6, 21.3, 1.5], "autouv": 0, "color": 0, "origin": [5, 19, 0], "uv_offset": [29, 22], "faces": {"north": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "east": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "south": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "west": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "up": {"uv": [33.5, 25.75, 35, 27], "texture": 7}, "down": {"uv": [33.5, 25.75, 35, 27], "texture": 7}}, "type": "cube", "uuid": "6f4c7327-d1a0-04a8-ffd2-0e75dfd02f4e"}, {"name": "<PERSON>s Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 34, -4.3], "to": [3.5, 39, 2.7], "autouv": 0, "color": 0, "inflate": 0.55, "origin": [0.5, 9, 2], "uv_offset": [32, 0], "faces": {"north": {"uv": [24.125, 0, 32.125, 5.875], "texture": 7}, "east": {"uv": [32, 8, 40, 16], "texture": null}, "south": {"uv": [56, 8, 64, 16], "texture": null}, "west": {"uv": [48, 8, 56, 16], "texture": null}, "up": {"uv": [48, 8, 40, 0], "texture": null}, "down": {"uv": [56, 0, 48, 8], "texture": null}}, "type": "cube", "uuid": "ad4badae-e641-1485-9596-09e5e400e733"}], "outliner": [{"name": "armorHead", "origin": [0, 27, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", {"name": "antenna", "origin": [0, 3, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "horns_overlay", "origin": [0.5, 6, 2], "color": 0, "uuid": "88a476fc-730f-5b8c-f872-4fc6f92ac3ad", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": false, "autouv": 0, "children": ["ad4badae-e641-1485-9596-09e5e400e733"]}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "10f829d2-4186-9798-8c58-a625734e5d98", "6328a29a-bacb-b764-d13a-09f4154a5e84", "b52a5610-4e5e-f644-9112-567a5bf0cb13", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "935f059c-863c-22f0-e550-649a51b81fa8", "cf7bbe88-beef-1d0c-f908-8d69d9b2f51a", "6f4c7327-d1a0-04a8-ffd2-0e75dfd02f4e", "90381fbd-8525-cfa3-a79e-0e35c1964c87"]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["eb287c72-7457-c5e5-0de3-8305e32a25d0", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", {"name": "wingRight", "origin": [6, 22, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 23, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "armNested", "origin": [-5, 23, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134"]}, {"name": "armNested2", "origin": [-4, 13, 0], "color": 0, "uuid": "86add632-ae45-1f1f-e80a-6bb25715ce49", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["4bba3088-cf8c-01ef-d978-387c2c4acae0"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_tetramand.png", "name": "ov_tetramand.png", "folder": "", "namespace": "", "id": "6", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "834996bb-f7e5-8dda-3799-b5cba197156c", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}], "relative_path": "../textures/models/ben/ov_tetramand.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAFctJREFUeF7tnU+oX8UVx3+harTGqImmIk+kiIhCJUg3RQQRF12Iu+CmS7F1UZpqS5EsShcipa21K1tx15VkJy5aKiKIdFOC2KKISBEfwUYTNUbs04rl/V5Gcyd37jkzZ+bO3Hs/2b3c+fudcz5z5s+9v12ryv/u27PxZeUmVK3+mTObu6o2gMoXrUB14wMAAGDRHli58wCg8gAQAVQegIVXDwAqGwAAqDwAC68eAFQ2AABQeQAWXj0AqGwAAKDyACy8+tkC4Ht7D6yH9u+nTzQ9xACg6eGZfeMAQOUhBgCVB2Dh1TcLgNQZ3OVz40oEsHALp/uDCgCAygZCBFB5ABZeffMAiJ3JiQAWbtF0P0oBABAlV/7ERAD5NaVEvQLNAkDfBV3K1D0FXenpqQBAunbktCsAAOwamkoAACb5yGxUoDkAaGfq2HSxewlGXdXZAYBaKhIWUAAAFBA1pkgAEKMWaXMr0CwAQjO2v8svzeytnwoAgNwmTXkxCgCAGLUKpAUABUSlSLUCiwFAqzcCAYDaVklYQAEAUEDUmCIBQIxapM2tQHMA0Haw9bW9th8AQKsU6UooAABKqBpRJgCIEIuk2RWYLACyK1GpQABQSXiqXSsAACobAgCoPAALr75ZAITO+1PHi1OAVOXIN2cFAEDl0SUCqDwAC6++GQDknvGlcW0lIgAA0kjxvKQCAKCkuoqyAYBCJJIUU6A6AJ649raqvw1YOxIAAMVsm4IVCgCAyp8NBwAKKyVJMQUAAAAoZlwU3L4CAAAAtG+ltLCYArvG/nnusXf7Y5WrvScQ215repYgVgWnnR8AeOMHAKZt0LQ+ToHRAND6zO/LthQQEAHEOczcUgOAwIgCgLmZOv3pUwAAAIDqG8G4Zj0FAAAAAAD1/K96zcUBoF37H3nv9fPEePTqm7MIZCl77ksB9gCymNhkC2kCANsOeuT5g+cD4O5XVlYIWMsGAJO1bRquUKAJAGy30+qofX3NUSYAUFgRSSarQDMAyA2BHM6/3SYAMFnbpuEKBZoCQC4I5HJ+AKCwIJJMWoHmAOAg4Ksauxdg2fg7t24igEnbN40XFCgOAFe/9jSglRGbu+M7nTkFaMXi6rQDAAR0BwB1DJJax1VgNACEuuVHBueG7rFhf6iOoTKX4ughbYgAxnW41mprCgD+5t2jBe4B+GUCgE1uArbmlSO2pzoA/D2CnBAYKmvpjs8ewIhe1nBVzQGg7ygwJRKQQAIAdqySJUDD3jlC00YDwHNbJ5O7k7oX0HcUqG3EPbv3a5NOOh0AmPTwmRvfLABSnV6zEahRDQBoVCLN1BUoDoDUmb82ANzAzh0ERABTd2Fb+wGAoB8AsBkYudtWoBgAWpn5fflT9wXmCgIigLYdtHTrAIBSYQCgFIpkk1KgOgB8x3I3A3974tWsQv7swK3r8vzjP22kAgCyDgeFNaIAAFAeTwKARiyWZmRVIDsArDPqy/99d93B2y++ptPR2LcJ/Zk+VK6rxNrurKMyYmHsAYwodoNVAYCzgwIAGrROmlRcgeYA4Pd46G3BPnX8+wPaK78AoLitUUGDCgAAIgDeBmzQMcdqUrMACK35pXP80A1CKRIgAhjL5KinJQUAABEAEUBLHjlyW8yDf+mFl3w5cps71X3y+afmPljaX7v/lrbnyFtb//v2bJjsTxv5hbSq3X/rGJqdp7YD1B6A2v23GoA1f239AYBtBAGATb+VBADrBSLrDGXsnpgdANSNQMUBEhIAAKOCAKCuAxAB2AwYANj0C0YAoXcctNWlvrOgLT9XuqVEAKHTpcPHj5l9KNdYpJRjbrw0A6Y0KiZPbQMM9R8AxIxietqxIgAAEBgjANB/CuIAEPsOgy+ziwRa3QuoDWAAkA7P7ZxEADb9xCUAADAKLGQHADZ9AYBNPwBQ+R7GWADgHgBLgF4FpD0AIgAjYRuJAAAAAAAAPQosZQ8AAAAAAAAAzlOgNgCt8RV7AEYFWQIs4yIQx4BEAEQAC44AAAAAAAAA4DwFuAnI68C9r6NyEci4tlJmH+sYcLYRQOmbfPs2bhwcylObbyqHuu1k2rf+pGNB6ctFToVWbwbGjpJWt9hyp5K+9leZdwGAPKaiNWQA0NVbq1ueUWqvlNkDwJfcjwjmEgEc3H3ZoHVtrC7qPPdB4M/8m6vPBst7Zevj9qw5oUUAYNN8Epcg+1dZikcAAGBHAQDQb6YAYGEAcGbgIoG5RACuX6HNoqOnNzsecGjvRudv6blLLH0V2TIb1MgLAABADbsrVicAiJMWAACAOIuZSGoJBKEIwP//uc78rl8AAABMxKXjmgkAdHoBAACgs5SJpbr+8RvWLX7gse7uf2w3nnpk5zTg7Yfeis06ifQAAABMwlBjGwkAdIoBAACgs5SJpXIA8JstRQRuxvfzEQFMzACUzV3cRSCny1yPAV3/AIDOA4gAiAB0ljKxVCEApHaDCCBVubbzEQHM5GUg38wAgM7xiACIAHSWMrFUAEA3YAAAAOgsZWKpAIBuwADARAFgfatvqZuAOrc4PxV7AKnKdfP5b1n6L2nF1mItb7J7AABg2FSIAHSuNHYEYHVYv1fW8gDAQjcBP3zxg44tXXHnlYMeQwSgA0puB42tNRYIAAAArG0MAMS6mi59rEPqSg2niq0PAMwUAJffu2/Qlr44sNV5/o0TuwfTf/TsKattNpl/7CWA9CWmWJGs5QEAALC2OQAQ63pp6a0O69dqLQ8AzBQAe+6/dNBC999yTef5ydfeHUx/5ulP0iy+8VxjRQChj7Fqv8IcktFaLgAAAGvbAgBlSWV1VADgKcAx4LDB5joGdGAgArABAgD065f8VWAAAABsLrmTmyUANwFz2FFzZfgRgH/uLzXYPxbkHoCkmO65ddOOTcCzChABxEUAAKBfr7EiAFc7AOiOQ/QSIPRbf7Hf95/7uwD+PQD/3F+ar/xjQe4BSIoNPw/90lLudwFcK7TlTu4UAADoDBEA6HQaKwIAAJk2Aa2/9mtdOujMqn4q/x6Af+4vtdA/FuQUQFJM95wlQKElgCtWWgoAAJ2hAgCdTrGpAAAA6Cgg3dmPNTDSxylwx1/H+XFc7gGwBOhVAADEOWzu1ABgYvcA5rYHIN3ZjzV47Vr/T3c93Sn6hy/cr6pKujKsKkSRSNsPRVHrJKF23/nni7VFmNIRARAB9CowNgB8x/cbJYEAAKRxAAAAgKIA0MyY5zr/S+//tNOeO676/Vd/SxAYmlHT3OP8XJr+xNRVOwJwbWUTkE3AjgK5IgCNw2wD4K6Dd6x+9fx3e33nl3f/Y/XCKy+tAEAMWuLSAgAA0FHgD0f/GGdBgdQ33XjT4JreOb9L5ENg2/ndvz4I+EuHN958I0u7Q4X4/bFWFmrv335wxFp0VH4AAACaAMB2IxwEznX+7f8HAFE+HZUYABQCgHQByB+lVt4FyBUBuP65mdMP4x+67uerBw91d/pDAHjy6NOrx9/5TUcyFwGUnvn9cbJGAlJ7f3LoR50qpavBz22dVDn8N/fvXac7/UHdbynuvXLn25ChdmxtfT7ORYiAaot/GagWAIaWAAAg7OMAQMU/dSIzAPyZ/9Grb15X7n9rzYVeR957Xd04S0I3k0j15toEdG0d2gx0UcDQJmCf85+rw1jHgJr+aMZHam/oHQc/EtA6vmsTEYBmdFYrACB8vFMn49epJAC4lCeu7G4+Hvjg61DYD/8BwGoFAGItUZfeDICHty7orSkUAfiJc0cE/szhb/r49eWOAKSZczsKGPoXcn5pJtUNd3oqzTFnCqhKveUYigB+vf87HRF+cfKf6aKsViupvNnvAQCAfvuRHMYHwdCsv10DAIjzUwCg0yt7BCB9Z12akXXNDqeKjQB4GciquC1/qS8dSXsAbubOFQGEyllcBAAAbA6xtNwAoPIx4NIMLnd/L73wki9zlzml8j75/NOq59j37dkoqn/tb/aVtoWqg1e6c2OUDwAAwBh2VqoOAGBUVgKAdLNNqj72+EsqL/dzIoDcio5bHgAw6g0AiACMJlQ1OwAwyh8CgHQaIVXrb6a2GgnMJQIIfTDk8PFjs/aRWXdOcrIczwHAPCIAAJDDGxZYhgSAkGFppXKRABFAv2K5TgEAgNYiSddRAAAQAUzZJVgCGEcPAMwDACEz4B6A0UHmnh0AAIAp2zgRgHH0AAAAMJpQ1ewAwCg/AAAARhOqmh0AGOUHAPMAAKcARkdYanYAAACmbPtEAMbRAwAAwGhCVbMDAKP8AAAAGE2oavbFA0B6mUc7Otq3/qSbgdIHVVx7Wr0ZqNXLpbO+S5DrJmBsu3Olr33PAABk+qAHAEhzCQCwWdUHq1aeZjJ5c+WKAA7uvmywYRurizrPpZ+o2lx9NljeK1sf5xWiUmkAAABUMr2dagFAVflXAAAAVLXAXABwnXC/jOR36ujpzc5/Hdq70flbeu4S5/4dharir1YA4AwAqGqDAKCq/AAAANQ1wNwA0EYCoQjA//+5zvycAuwowClAXf/Ptgfgd0NaCgCAHcXYA2AJUBUBpSKA6x+/Yd2vBx7r7v7HdvapR3ZOA95+6K3YrJNIDwAAQFVDBQBV5ScCYA+grgGWBoDfOykicDO+n48IoN9OtDcBpRuYua1Qe6OTPYDcykeWBwAiBcucfKwlAADoHzhuAma6CuzL6/YAcvkLEUBcBDC2w0vjHIoIiAAk5Qo/HzsCSO0OAAAAqbYzlI8IgAighF2pyrSG/9uV+HsArXzZ54lrb+v91WI/EiACUJlKuUREAOW0lUoGAFwEkmyk+HMAUFziYAU5AdDKzO93VooEiADq2d+6ZgBQbwAAABFAPes7W3MtAHz44gedvl9x55WDWsxxExAAAAAAcFYBAJBmCm4TkCVAmn6cAhQ6Bbj83n2DI/LFga3O82+c2D2Y/qNnT6WNcMO5ckQAoTW26/bh48eq2njr7asqTgu2WWoJAADk0QUAq1VtQC0eALKZkqJlBVqfYVtvHwBo2bppm6hA6w7WevsAgGhiJGhZgdYdrPX2AYCWrZu2iQq07mCttw8AiCZGgpYV4BjQNjoAwKYfuSsrAABsAwAAbPqRu7ICLsT2f1fBNevlMyeq2vjtew70vhXoPgrLMWBlA6L6aSsAAGzjV5WOtqaTGwW+/h4AV4HTrAEApOlGrkYUYA/ANhAAwKYfuSsrIH0RqPYa2z8G5ItAlQ2G6uelAACwjScRgE0/cldWAADYBgAA2PQjd2UFAIBtAACATT9yV1Yg9MtA7lSglT0AfhegsqFQ/TwVAAC2cSUCsOlH7soKAADbAAAAm37kRoFJKwAAJj18NB4FbAoAAJt+5EaBSSsAACY9fDQeBWwKAACbfuRGgUkrAAAmPXw0HgVsCgAAm37kRoFJKwAAJj18NB4FbAoAAJt+5EaBSSsAAIzDV+qnxbTNyvHzWtq6+tKFbuJpy3xu66Q2aW+6qff/mTObgz4o6Svll8QFAJJCwnMAsNH70UutrAAAAGhtpcl0AAAAWAxTmsGJACzqjpAXAAAAi5kBAIt6DeQFADYAhIZQuzSY+x5A6KfF3PcFJIBILsIegKQQewCDCkghaqq8AGBHOQCQakEj5SMCIAKwmJo0gwMAi7oj5AUAAMBiZgDAol4DeQGADQDaUD801HPfA5CWWBJAJBdhD0BSiD2AonsAAIB7AEYXrJudCIAIwGKB0gxOBGBRd4S8AAAAWMwMAFjUayAvALABgHsAw0sATgEacPKhJgAAAGAxUSkCAAAWdUfICwAAgMXMAIBFvQbyAgAAYDFDAGBRL0NeqwPv27hxsBWnNt/M0MpyRdyze3+5wim5eQUWfw8AAACA5r20YAMBwIWXmD5o4UcArc/4vi21GgG4X/cN/apuQZ9oqujSOgAAANCUwbvGlDb8Jjvd06jSOgCATACY2szvbI0IoG0UAIDC45NrDwAA5B2o0oaft7XlSiutAxEAEUA56y1QcmmHKNBkVZG1+gUAAIDKQFtJVMtRSve/Vr8AAAAobduq8iUHcM9dYXM7HdD2T9JJJfY5iQAAAIi1mSLpJcPWOkiRxo1QqLZ/kk6xTQUAACDWZoqk9w1b+jtXI3zHy1VuaoQi9TsVAKF+AgAAkMvmTeWUMnypUQBAUmjmzzkGbPMqcOpMF2uupQDgtyNXRKDtn7ZfRABEAFqbGjUdANiRO1UHAKA0VyKANiMA5fCZk0mv46ZWIH3II7VcKV/I8Q8fP9Y72RMBEAFINjXr5wBg1sMrd65WBNDKW4TWdwE2V591RN5YXSSLXjGF396Xz5woOgmOFQnEzvxuCIp2vuI4q6sGALYlAAAYNjUAoHbFOgkBgA0A/qi1BgSpPaWWAE4XAFDHr9W1AgAAoDaWhIQAIEG0MbMAABsAtFdYxxzTobr89oZ2x3O1NwQAV37q/QCXXzruk/rHHkClU4C5bAICgLQ9AACQC7HGcogA8kYAuQzbOKznZU/dJbe2gwjAqmDh/AAAAJQ0MQBQUt0MZQMAAJDBjIJFAICS6mYoGwAAgAxmBABKiliybACQFwDWXe2SY71ddiunALE6SS8Fpe5xcArAKYDJ5zgFSDsFAAAms8uXuZUIwPVo7M+L534XwPWjtXcC/BuBrp2l3wW4fc+B3l+eOrR3Y90EHwShmV76/6OnN3udQuofEUAjEQAAyAf1vpIAQL++ACATALQO3NqvCVsjAJYAeZcA0qfRQrWxB5A4geRaAgCAHQVi17aJw5acrfVNQACQPLRpGQFA3lMANwqtgSB1hkyzqq9z+fcArLrEngbwLoAwggAAAFidfCg/ACipboayAQAAyGBGwSJiASDt9ksRVuwSh01ANgFN9h8Kra2hrqlRPZmnsgQAALlHniXAoAKcAvR/LTeXGboIQAvE0KmKtPb32+vSswcAAADAOQrEhshWEAAAq4KF87MHkHcPQDvTFR7WYPGtA8A1PHbGJwJItCgAYANASHarAScO53nZpHaM9VHQWDBK7Zb0YQkgKXT2eS4AxN7hD90IjC1H2c1iya5//IZO2Sdfe7fz90V/uXb9t7Zf0p7Ec1snVX1x+n72/eOd9Ptvuabz92s//lfRjfBvPXhV510Av35VZyIS+fr/58n3B/tXtPMR7a6WFADYpAcAw/oBAJt9Fc9tBYDfwEevvnn9X6G3vI6893onS6mPg7qZ1IWCfr2lhbX2y48EtDO/65e2/k8+/7ToJHjdt29dRwDaCCjXuLj+v/PvV4kAhkQFALlMrluO1gFDtQMA27gAAKV+VgC4Gd+vLhQB+Ol+t/t/nf+yzhS+4/i73qUjAV8P1z9rv5TD+VUy5wAPb13Qyer3v3QE4N8EHFt/7gEIlgMAYl1rOD0A6OoDAPLaV/bScgNAOu7xZ+S5RQBugBwIWokAQjPvWBFA6Zk/tBdFBDByBAAAdgQHADs6uAigVQD8H3BENklNkIZkAAAAAElFTkSuQmCC"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\tetramand.png", "name": "tetramand.png", "folder": "", "namespace": "", "id": "7", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "7e18d943-4ca4-36bf-1983-28cfbcf53c58", "relative_path": "../textures/models/ben/tetramand.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\r_tetramand.png", "name": "r_tetramand.png", "folder": "", "namespace": "", "id": "8", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "faecebca-c3da-6b09-4f40-95d86fd452af", "relative_path": "../textures/models/ben/r_tetramand.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\MARSHY'S SECRETS OF THE OMNITRIX - BEN 10 ADDON (1)\\addonpacks\\MARSHYS_OMNITRIX_ADDON_4.53\\assets\\m_ten\\textures\\models\\ben\\recalibrated\\r_tetramand.png", "name": "r_tetramand.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "80649f2f-07c9-dab8-621f-aa9843e51d12", "relative_path": "../../../../../../MARSHY'S SECRETS OF THE OMNITRIX - BEN 10 ADDON (1)/addonpacks/MARSHYS_OMNITRIX_ADDON_4.53/assets/m_ten/textures/models/ben/recalibrated/r_tetramand.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAGGJJREFUeF7tnTHIZUcVx9+XNX5mF41RgylCshhXkUUQlBQusRCMhWwjBAvRFEKKNJpYKWhQQW3MYpNCEE1hIYE0i00iFpFNEbRcJImRNQSJRNQou8kX3azkvZ2YN9+dd86ZM3Nn7r2/bTTfnTkz858zvzkzd+68vVXjf4/tn7hiqcLp1YWdyc+ujlvMNU9758Gze80rQQUWq0Bz5wMAAGCxo6+DhgOAxp1ABNC4AxZePABo7AAAoHEHLLx4ANDYAQBA4w5YePEAoLEDAIDGHbDw4gFAYwcAAI07YOHFA4DGDgAAGnfAwosHAI0dAAA07oCFFw8AGjsAAGjcAQsvHgA0dgAA0LgDFl48AGjsAACgcQcsvHgA0NgBAEDjDlh48QCgsQMAgMYdsPDiAUBjBwAAjTtg4cUDgMYOAAAad8DCiwcARgcofR8BADB2AMmLKgAAjHICAKNgJO9agW4BcGx1zaBwn179aaegv1l9YPD5xdXrRToCABSRESOdKAAAjB0BAIyCkbxrBboBQGrGj9XLjQBiO7kRAQDo2p+pnFEBAGAUDAAYBSN51wo0B8C5/Q+bbgUuFQGEXrFGAgCga3+mckYFAIBxcxAAGD2M5F0rAAAAQNcOSuXqKgAAAEBdD8N61woAgLkB4K67jqw97pFHLnfteVSuCwUAAADowhGpRBsFAMDcANDGjyh1ogoAAAAwUdel2iUUAAAAoIQfYWOiCgAAADBR16XaJRTYs/48t7ZQ7dl+rb2QrvRJQG351hODWrvWdBxEsipG+l0KAAClfwAApVAkm5QCxQFQa+ZvHQGE8luDgAhgUuOr+8oCAGMXAQCjYCTvWgEAYOweAGAUjORdKwAAjN0DAIyCkbxrBQCAsXsAgFEwknetAAAwdg8AMApG8q4VAADG7mkFAGn3PzTj7Oq4qUX8LoFJrtklBgDGLgUARsFI3rUCxQDgff9/9uxGp/13b3/G/tNPXlj//ZdHbtsSMj4RGP8ewBcuP7dO/5Unt2fEg39uPpc/fdrXL2ODgAjA11/kHlYAAGR6BgDIFI5sXSnQDQDueu/f1sJ8+efvM0UCsZrSzP+zL/11neXRf77f1RFjAUA788eN0e4FsAfgcoPJZwYAmV0IADKFI1tXCnQDgKDK59+9maHjf9YZu5SdVG8BgK78mMpkKgAAMoUDAJnCka0rBboDQFfq7KhMbQCk1v7vuuE961r96x9/3ykVewBT8aS29QQAmfoDgEzhyNaVAt0BID4PcOcdm/f2uf8e++3mXEGp9/+hHgAgt0fI15MCACCzNwBApnBk60qB7gAQ1PFGArVm/lYRQFj7h/KlPYA3dRS+DeAcQFfjcfTKAIBMyceOAABAZkeRbacC1QAQBoj3G4HW/ZdqRysAxDO/9FZAehtABNDaw9qWDwAE/QFAWwel9LoKFANAqGaY8b0RQDjTX6r58deEWrtxO2rP/KFe4RyANMOnnoe//+If79rZVCIArSfMMx0AMEYAAGCeA2GprSoOgFhI6x5AmPlzZ+xUR+baHWvAp+r9xRv+tX6U2vUnAljq0C3TbgCgjADKyG23AgDsmpFDr0B3AJCqPvZvA7aOAOI9gVifVAQg7f4HO+wBSB437+cAoPMIAADMewC2bh0AmAgA4mrGSwPtjB/bIQJoPQTblg8AAMBeWxek9JYKuDt/f//aK54G5M5cvaxhn/nGrev2X3jw7R4ZzHmP3//aOs9HH/yLOW/JDAcH/3H7kKc+j+2fcPmfp+w38k49gnJ3HgAAAN5B5MkPADzqrVYAwKffSooArOcg4uqk3kIQAWyUAgA+BwYAPv0AAEsA9xhyuqAru7vyLAGGlwDemV+KBIgAxo0AUv156uBp9xhyjWBnZnflAQAAcPqgK/tYSwAAkOgmAAAAXCPYmRkA+AQkAvDpl9wDYAngFFaZHQAohUokAwA+/QAAm4DuMeR0QVd2d+VZArAEcHmgM/NYEUCqmhwE4iTg4ElAlgDOka3MDgCUQrEE8AmVyp06CAQA6ugdWwUAPp1ZAvj0Yw9gIXsAvAbkNeCgAkQAy/gYCAAAAAAwoMBSvgYEAAAAAACAQwpM/ihw7iZKiojh67XU79s7l9zu7PH9A+FMvdvwVQOpewGsm4LSV4Cl6hvbqX2vQax37fJq6VTKbuvXiNk3AgGAYRcAALuHBgDY1qd7AHhnrl4iAe/NQ7mRwq9+vLmw5uTl/a2ej3WNZ/zzRw7W6T/31bwXNdqZVWqX1k5q2Ne2X2ombmUHAIykPAAYFrr2AK1tfyT3qVbM7AEQKzd2RGAd+FLEE8/UIf2N97+600lCJKD1JGnmf+nBd6xNpeqTKid3b8EaCTDwdT0NAHQ6ZacCANvSAYBsV6qSsVsASDOhdYaJ08e/IlxFXYNRa3ulXz9ORQRhJg1r/FDFsEeQmjnDjB83SapHbj+l6iFFArn5DF01q6QAYPV6Fx0KALa7IXcg5+brwgkaVGLxAAiat/oNPuvAt/qItDcg2UvN/FI+7XNJd2ktL5UjRQxS/rk/BwBXe1hyxFqOAAB2R2AAoJbnbewCgMYACN1rBYF17W2NBLQzv7Ue1ogr9zxC6vxD3eE0PesAAAAMei0AmN5gzqlxdwCwzoSpXWlJDOkknJS/1nOp/db37qGetSMAbSRjXWoRAdTytE6XANIAkOTQOhgA2K2kNQIAAJJn9vmcCCDqFy1AeulOCZjWmT9ulwSCWnrlzvxx/dkL2O2pAAAA7PQQANAL6uvUAwA0BkB8Is/azeEEXxwJpGZ+6Qy/lC/exffWP9Xe1LcI0nv91GvD1LcQ8VeSVv2t6VMnMK12QnqvPQAAANYKSB8VxZFACP0BgG3oegdsXJrXHgBoDACb+xxOrV0rxzOn5DjSAZzSa2upPGnmj5UpbS+3nySdc+2m8lnLAwAAYK1AHApLAwgA6IaudUDqrKZTWcubHQDiNWpKKulKMW9HlM4vDchUeam17+2Xr9vK8tSRVwZNSPcCpMrVzthSu7R2UvWobd/az6VfP3vtAYCJRACSIwOAYQUk3byAAQBWBbbTH7oUVHqvrS0u9X5asl/rvXbuDKVtb0gnvbaz2ovTe88VSOXXHpCtLgWtFXF67S4uAgAA0hDc/RwA5OnnHai1lrIAIKHsWJGAFKLG1dPOkBLorG6s1cPantTrRWv9tOm9a2ZtOXE6ADCsXHdLgFBNrcPnOkTIZx0wAMCnOADY1m+2EUA8kHNnxLFA4HPrdO7Q7tQufypneEtQu/1jD8ixy5MiAa++3vYAAGHkeTuo1sDW2gUA20p5B4xWd+2a3etf3vY0B8DFh27Z/HRN5r+jt908mPPScy9kWhw327n7tn+xJ5QeOjbXQaQDId7nWpWkdtRaG0sz79hLvdSRae+3CF67AEDryZXSAYBrBpXNBZ925gUAGwWaA+DKwyddEcDqpuuH+/zFlysN2bJmH7/ntUGD1j2L+OOc1MySWhJIa/4w06S+PpRUsZ7LmBsA4siuFIAmvwQAAABgCB4AQELq5jkAIAJYO4IUAcSOEr8ViL8NiAfg2BFArRkyHlalQTP2EsS7h8ISQAfaaqnGWgIAgOEuBADP5v3+e6ERsccSYHgJkFozSrqHmV1aq0sRQDwDhwggjhSk+kgDTNrrkPJL5Uv2P3Zm+CtIya72efyWqvbbKWt5x+59HgBoO7NGulQEAAA2CgAAm9cBgKDXRN4CXLr4b1sPJ1KnzkOE5LVnHm8jWtX/6LF3equ+O3+8R1XbL43l7d19ngigrgfstg4ANvoAgEJeODUALP0kYPEZyOgAhdyunJmp1z9WYuz2GMtrHgEAgMIhqNEByo3cQpamXn8AYHKEQ58D525+WTeLpN3hUA+rXVPrV6vVZ37ydmuWvtacZWu/OnSys/KaudQSLCWDdVPOK6e1vOZvAR7bPzF4FFg7QHMHqtY+APC6pDH/yBEAAGj8GtALgNSv5abec6fSx27qHfjxmfvU2fxTZw6MI2Q4+dS/imxV/9THWEU6ZQJGmp8EBAAAYNdbgNqvLwFA45OAKQBIewFhhk6F8taTbtJ361qYS0du40jg9OqC1jTpKihwdnW8gtW0Se/HO7Flr71uIwAAMKpfLrYwANB5BJAiXioCkO6+S331RgSwTAaMDYB4Yiu115Rrp/sIAAAsc2CO1WoA0DgCGKuj51rOM9+4df0aVXtdeCkdwnXmH/rBn5ueJS/VHqudc/sfzrrJyjpTt56hrbpY0y/Seawi7UoPAEqqqbcFAPRa7UoJAJw6SgDQHnhKVSM1YxEBEAE4XXedHQA4VQQATgEzsxMBZAoXZQMATh1TAPDO/HG14kiACKBsBJDqr1MHT896jMy6cc6xrcoOAFQyFU9UOgIAAMW7aBkGAUCbfgYAZXQnAnDqCACcAmZmBwCZwrEHUEa4YAUAlNVTa600AFLlcg5A2yMLTQcA2nQ8ACijO0sAp44AwClgZnYAkCkcS4AywrEEKKuj1RoAsCo2nJ4IwKkjEYBTwMzspQHAa8DMjlh6NgDQxgMAQBndiQCcOgIAp4CZ2QFApnDsAZQRjj2AsjparQEAq2LsAQwqEGbwMnKm7wWwfhsgfQVYqr6xndr3GoRvGEK5Lz34jlpNGbQr3WXprYzVfutvDRa/BAAA2y4PAHwIAAA+/UbPLd2KHCoUz1xSRd93/dF1koe/e3H9v/FtxNJtsuePbK4rv/vbx9b/+7eXL0lFbj3XDuRUu0L9f/fAf03lxok/8Z237aw/EUDbrw0XHwEAgNcGBzgAyOMeEUCebs1yxQCQ1uqpXza68f5X120IAyduUIgEtA0NM3+cPkQCYebU/tJSsCPtLaTqb40Ewsyfqn+IUCS9tXpJ6aw6SfZSz63lsAeQq3ShfABgI2RYCgAAn2MBAJ9+o+cOALDORFKo95HvXDPYljCTPvHDr61+ffbRN9N899zz6/+fmjn/8MDrg/akemhnqtTaPf67FAmk6p/KZ9Xd6iCpiKdWudbyiACsPVo4/dgAeP+3/rizBX/93gcHnwOAvI63Dsi8Uv6fy1oeAPAq7syfe6BEW+xbI4Ew+L957tbB7N8/9ef1398KgdTA15YvpZPuyU/N6JLd8FyKGErPxFJ7SpdnjbDi8gGA1pMqpQMAw0uLIDcAyHM8bSQAAPL0LZar1hIgruDXT928+v1vn3jzz3EUEGb/NxJ8/I5PrX507gVVG2vtAYTC4/MIqkrtOP8Q58+dkXuZ6bV6hHRxvVvfOMQ5gP0T65+YsjqideDFAHijzACBtw5+AKAbUgBAp5OUCgBcBUAQSgKB9TVPsFsbALn1lxykdgSgrXdqBi0VUUg6lH4e/IgIoLSyRnulzgFIxdZeAmgHkjRzxu0AAFLP5j0HAHm6Fc+lPQqcKliKGGq/BbAOaK2AuQM/tp/6FkJbD226Xm/0SW0yAwBtz1ZOBwCGBQYAZRwPAJTRsZqVM0dvyfqd+VCh8JVfPAOlTgKWOggUZpAwUEsLlPoWQXqvn3ptmPoWIv5KMrcdRAB5yi1+E3BsAIQB9O1Tt2z1mPUoMADYdngAAACyFPAuAcIM/Lmvblg61sc0v/rxJnApNYO2+hgoq9MGMgGAPCUXHwEAgI3jAIC8ASTlYg9AUqjxcy0AvDcCxc28/fJ1W3966sgrg0rM/UYg7c1FKTeR3sK0PmorHTVvXT8igOggUMrRAEAeqaUrwQAAV4LleRa5ZqGANgIjAqjT3YuPAOrIilWtAgCACEDrK6SboQIAAADM0K1pklYBAAAAtL5CuhkqkPoYy/qNA+cA8pyDPYA83chVSAEA8GzTMdi08EI+hJkJK6AFQJjhU/cxpM5R3Hfp+aY+njpqHs6BcA5gws5L1f0KAAD2APxehIXJKpDaBIxn/FQEEBrOHkCeCzQNj/KqTK45KQAA2AOYkz/TFqMC8a3M1t3/uLje7t2PvwXgVmCjg5B83goAACKAeXs4rdupQOp3GVK7/VKEQARgczj2AGx6kbqwAgCACKCwS2FuSgpcfMh3J+PR227eau6l57Z/UenYvW3PAcTti+u799knm07CTQufkqNS1zoKAAAAUMezsDoJBa48fNJ1K/Pqpuu32/niy1v/vXf3+aaT3KH2RfUlApiEm1LJWgoAACKAWr6F3QkoAAAAwATclCrWUgAAAIBavoXdCSjAJiAAmICbUsVaCgAAAFDLt7A7AQWks/JxE6TfAejtrL308/PcBzABJ6WK9RQAANwHUM+7sNy9AtIv5+Q2IEQCdx60PWqbOuoc2kUEkNvD5JuFAgCACGDSjry/f63rJNvZ1XFX+3uZ4VyNcGRu1X5pba9tkhQBSNeme9vf9JikVqSe0wGAEy4AevvWOwByywcAucrNLB8AAABvuLT0diLl9kQAEwcCAAAAAGDig9hTfQDgA0Bq5pRu/gl9NvclQGqTtNRbDvYAPKN/tVoBAABQMwIAAM4BWjs7AAAAAKD2KOvYPgAAAACg4wFau2oAwAcAb//MfQ+AcwBeD6mcHwAAgJoRAACoPIC95gEAAAAA3lE04fwAAAAAgAkPYG/VAYAPAJwD2P0xEK8BvSO0cn4AAABqRgAAoPIA9poHAAAAAHhH0YTzAwAAAAAWPIC1TQ/f/XvXvFJ5p1cXpCRbz733EZgK6zBx7ld8oSnhTL7XzljSxPVd/LcA3hlc23EAQKvUuOm8AxcAjNtfxUurDYB4hrU6nParuFgYbSRABHCNy6cAgEu+9pkBgO9KsvY96KuBFchxaQDAp3/z3KUBIM2oVofLjQCCsFIkINW3eQdVroC1P1IACH/32qvV3NiPQj3ZA3Be6hl3mDSgrA4CAGoNiY1da38AgLr9Mbp16WMLqULBgbQDNdfhtPal+vJ8W4Hc/ghWUv3itVuqn6T6LT4CAAClXG2adrwDVRpgrVWR6gcA9vMOssSOo52hcx1Oa7+1w02t/Nz+kJYC8fNS5Uj6Sn4S1wMAAADJp2b9vNTAtA68WqJa6wEAAEAtX5yE3VIASDXWOiC1opWyCwAAgNbnZpkOAMyyW/WNyt0EHHsPQNp11reYlG9VoDYAtP2mrUepmT/UiwhgIhGA1pEY3jYFtAPPZvVw6lIDt5QdAHBVAWsE4P2az+twkgN4HXVp+WvdKixd5FFa55RfSb89SARgjAAAQGnXbWsPALTVv3npRAC+Ljh/5GDLwMnL+z6DlXPH9b3v0vNVJkEigModWco8APApCQCG9QMAPr8aLTcAKCt1b0CQ6sMSoGz/T84aACjbZdKAK1uabE2qDwCQNZx1irEA4N397/U1YO55iFZOFddX2iXPrWdqCVC6HyW/ktpXZQMkV7QW+QCAT3UAYNsDAAA+fyueuxQApI6VSK1tWG/nALyvRbXt9qbLfU+eWy4RQK5yI+cDAD7BAQARgM+DGufWAkA7g0sXMHibSwSQpyARwLBu7AEoTwICgGEHIgIgAshDcie5pAhAO/Dj5tS6Lrr3CKC3+sX90votgKRPqJ+UTmpXeM5bAAE0AMBHYt4C2CIAaWADAJ8/mnNffOiWK+ZMGRmO3nbzYK5Lz72QYa1clnP3+c7uxwdtQs16+yYgVc9a3wKcOTrsV7dfvm4tkbRXlLrHP/X3p468MugUUvsWvwcAAABAOZz+3xIAqKFqBZtXHj45SgSwuun64dq/+HKFVulNPn7Pa/rEAylZApRdAgRr8VJAWhrkvuVYfAQAAACAi4CJzN6vAQFAjV4ZsAkAygIgSCxtdo3UvW8WkztD5tYzBoCkhzTDa+thfctBBMASQOtbg+k4B6BbAgAAl5vVy7z0CODSxX8XFTd+29H6LYdUn2P3jnMjkBYAqQhKGyEQARjdGQAAAKPLqJLnLgEAgErecomWDoBySl61FL/taPyW49Dbl6g+e3efr7IMjl8vS5FIbj9IdqUIp0rjcxvTIh8AKKw6AFgLCgAK+1Utc0s/CHT02DvLSgsA1noemlhq6SLYlSKcxUcAAAAAlCXgxhoAqKFqBZulPgaSdnnjqlu/MrTa10p16sz2vf7afKl00prUa9+aX6qPtEa2lhfSA4Bc5UbOBwAAQA2XAwA1VK1gUwJAKFJ74CWki7/OSn0Fpo0ErBFA+PpN+vqsgqSTMlnrWnA2ASfiBgBgIh1VqZoAoJKwUzGrBUAcCUg3/qQigFgXyU7uzB/KCRFA+G+rPakf+RpwWKHYr0pFepKduH8lwC3+LQAAkIb47ucAAAD4PKhx7tIASN3MIs3I2rPeklzxzTdjRQClIwupnd7nQW/pzrzccoJfSTN2rn0pX+gPIgBBKQAguZIuAgAA2zpNBQD/AxBTsDwt/6wiAAAAAElFTkSuQmCC"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\MARSHY'S SECRETS OF THE OMNITRIX - BEN 10 ADDON (1)\\addonpacks\\MARSHYS_OMNITRIX_ADDON_4.53\\assets\\m_ten\\textures\\models\\ben\\ov_tetramand.png", "name": "ov_tetramand.png", "folder": "", "namespace": "", "id": "5", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "6d0bd30a-71f6-8e89-2cd2-225315fc4711", "relative_path": "../../../../../../MARSHY'S SECRETS OF THE OMNITRIX - BEN 10 ADDON (1)/addonpacks/MARSHYS_OMNITRIX_ADDON_4.53/assets/m_ten/textures/models/ben/ov_tetramand.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\MARSHY'S SECRETS OF THE OMNITRIX - BEN 10 ADDON (1)\\addonpacks\\MARSHYS_OMNITRIX_ADDON_4.53\\assets\\m_ten\\textures\\models\\ben\\tetramand.png", "name": "tetramand.png", "folder": "", "namespace": "", "id": "9", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "d3ef2374-2c11-0c50-2992-b0ea2fbd2a0f", "relative_path": "../../../../../../MARSHY'S SECRETS OF THE OMNITRIX - BEN 10 ADDON (1)/addonpacks/MARSHYS_OMNITRIX_ADDON_4.53/assets/m_ten/textures/models/ben/tetramand.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\spicy.png", "name": "spicy.png", "folder": "", "namespace": "", "id": "10", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "789fc27e-5232-601e-7e81-f947a37b1e08", "relative_path": "../textures/models/ben/spicy.png", "source": "data:image/png;base64,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*****************************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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}, "e9748d1d-6892-b67f-bb06-390a01e323a0": {"type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "424a6108-7241-5231-e6cd-a06427b42c15", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "996b8625-fe39-5fdd-6036-56e71d8d8af0", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "d127c75a-3565-1cd8-936a-2a6c9b93f876", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "8cca5482-9ae1-4019-bd25-7eff3dce9747", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "dddfb29f-dafb-7888-946f-f12b21be2d70", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "d558333b-91bb-3bd9-9efd-29a826488c6b", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}, "86add632-ae45-1f1f-e80a-6bb25715ce49": {"name": "armNested2", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "3c7db589-1bda-5211-9512-b73c26799a84", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "e9460072-b53a-0af5-10c2-499b155c66be", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "c3c0f602-75a0-48ae-c846-b17b3ed6b3a7", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "3efecafd-016b-38b6-e927-747f7f6788fa", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "7aa77861-ed59-d31f-4aba-9893db51542f", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "da2c19d6-be5f-621f-2040-4d675cc3571c", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}