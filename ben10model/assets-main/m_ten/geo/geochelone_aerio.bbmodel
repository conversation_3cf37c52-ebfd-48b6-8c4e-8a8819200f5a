{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": false}, "name": "geochelone_aerio", "model_identifier": "", "visible_box": [3, 5.5, 2.25], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.5, 24, -6], "to": [2.5, 28, -1], "autouv": 0, "color": 0, "inflate": 0.51, "rotation": [0, 45, 0], "origin": [0, 26, -3.5], "faces": {"north": {"uv": [5, 0, 9.75, 3.75], "texture": 0}, "east": {"uv": [0, 0, 5, 3.75], "texture": 0}, "south": {"uv": [24, 8, 32, 16], "texture": 0}, "west": {"uv": [16, 8, 24, 16], "texture": 0}, "up": {"uv": [9.75, 3.75, 13.5, 6.5], "texture": 0}, "down": {"uv": [4.5, 4.25, 0, 7], "texture": 0}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.5, 24, -6], "to": [2.5, 28, -1], "autouv": 0, "color": 0, "visibility": false, "inflate": 1, "rotation": [0, 45, 0], "origin": [0, 26, -3.5], "uv_offset": [32, 0], "faces": {"north": {"uv": [35.5, 0, 40.5, 4], "texture": 0}, "east": {"uv": [32, 0, 35, 4], "texture": 0}, "south": {"uv": [56, 8, 64, 16], "texture": 0}, "west": {"uv": [48, 8, 56, 16], "texture": 0}, "up": {"uv": [48, 8, 40, 0], "texture": 0}, "down": {"uv": [56, 0, 48, 8], "texture": 0}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 13, -3], "to": [6, 29, 3], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 21, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 21, 28, 32], "texture": 0}, "east": {"uv": [16, 23.75, 20, 32], "texture": 0}, "south": {"uv": [32, 23.75, 40, 32], "texture": 0}, "west": {"uv": [28, 23.75, 32, 32], "texture": 0}, "up": {"uv": [40, 24, 32, 20], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 13, -3], "to": [6, 29, 3], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 21, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 37, 28, 45.25], "texture": 0}, "east": {"uv": [16, 39.75, 20, 48], "texture": 0}, "south": {"uv": [32, 39.75, 40, 48], "texture": 0}, "west": {"uv": [28, 39.75, 32, 48], "texture": 0}, "up": {"uv": [28, 36, 20, 32], "texture": 0}, "down": {"uv": [36, 32, 28, 36], "texture": 0}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [7, 14, -2], "to": [11, 29, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [3, 5, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 29.5], "texture": 0}, "east": {"uv": [40, 20, 44, 29.5], "texture": 0}, "south": {"uv": [52, 20, 56, 29.5], "texture": 0}, "west": {"uv": [48, 20, 52, 29.5], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [7, 14, -2], "to": [11, 29, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [3, 5, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 45.5], "texture": 0}, "east": {"uv": [40, 36, 44, 45.5], "texture": 0}, "south": {"uv": [52, 36, 56, 45.5], "texture": 0}, "west": {"uv": [48, 36, 52, 45.5], "texture": 0}, "up": {"uv": [48, 36, 44, 32], "texture": 0}, "down": {"uv": [52, 32, 48, 36], "texture": 0}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-11, 14, -2], "to": [-7, 29, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-3, 5, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [35.5, 52, 39.5, 61.5], "texture": 0}, "east": {"uv": [32, 52, 36, 61.5], "texture": 0}, "south": {"uv": [44, 52, 48, 61.5], "texture": 0}, "west": {"uv": [40, 52, 44, 61.5], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-11, 14, -2], "to": [-7, 29, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-3, 5, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [51.5, 52, 55.5, 61.5], "texture": 0}, "east": {"uv": [48, 52, 52, 61.5], "texture": 0}, "south": {"uv": [60, 52, 64, 61.5], "texture": 0}, "west": {"uv": [56, 52, 60, 61.5], "texture": 0}, "up": {"uv": [56, 52, 52, 48], "texture": 0}, "down": {"uv": [60, 48, 56, 52], "texture": 0}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [5.499999904632569, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 0}, "east": {"uv": [0, 20, 4, 32], "texture": 0}, "south": {"uv": [12, 20, 16, 32], "texture": 0}, "west": {"uv": [8, 20, 12, 32], "texture": 0}, "up": {"uv": [8, 20, 4, 16], "texture": 0}, "down": {"uv": [12, 16, 8, 20], "texture": 0}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-9.536743172944284e-08, 0, -2], "to": [5.499999904632569, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 0}, "east": {"uv": [0, 36, 4, 48], "texture": 0}, "south": {"uv": [12, 36, 16, 48], "texture": 0}, "west": {"uv": [8, 36, 12, 48], "texture": 0}, "up": {"uv": [8, 36, 4, 32], "texture": 0}, "down": {"uv": [12, 32, 8, 36], "texture": 0}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 0, -2], "to": [0, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 0}, "east": {"uv": [16, 52, 20, 64], "texture": 0}, "south": {"uv": [28, 52, 32, 64], "texture": 0}, "west": {"uv": [24, 52, 28, 64], "texture": 0}, "up": {"uv": [24, 52, 20, 48], "texture": 0}, "down": {"uv": [28, 48, 24, 52], "texture": 0}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 0, -2], "to": [0, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 0}, "east": {"uv": [0, 52, 4, 64], "texture": 0}, "south": {"uv": [12, 52, 16, 64], "texture": 0}, "west": {"uv": [8, 52, 12, 64], "texture": 0}, "up": {"uv": [8, 52, 4, 48], "texture": 0}, "down": {"uv": [12, 48, 8, 52], "texture": 0}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 35, -1], "to": [1, 43, 1], "autouv": 0, "color": 0, "origin": [0, 26, 0], "uv_offset": [56, 16], "faces": {"north": {"uv": [58, 18, 60, 26], "texture": 0}, "east": {"uv": [56, 18, 58, 26], "texture": 0}, "south": {"uv": [62, 18, 64, 26], "texture": 0}, "west": {"uv": [60, 18, 62, 26], "texture": 0}, "up": {"uv": [60, 18, 58, 16], "texture": 0}, "down": {"uv": [62, 16, 60, 18], "texture": 0}}, "type": "cube", "uuid": "704386d4-c64d-8236-e5e8-651bc0a273b0"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 12, 3], "to": [7, 30, 7], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1, 0, 5], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20.5, 28, 32], "texture": 0}, "east": {"uv": [16, 20, 20, 32], "texture": 0}, "south": {"uv": [32, 20, 40, 32], "texture": 0}, "west": {"uv": [28, 20, 32, 32], "texture": 0}, "up": {"uv": [36, 20, 28, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "afd74594-73cd-c2aa-1667-9bbb690fc090"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 12, 3], "to": [7, 30, 7], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1, 0, 5], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 0}, "east": {"uv": [16, 36, 20, 48], "texture": 0}, "south": {"uv": [32, 36, 40, 48], "texture": 0}, "west": {"uv": [28, 36, 32, 48], "texture": 0}, "up": {"uv": [28, 36, 20, 32], "texture": 0}, "down": {"uv": [36, 32, 28, 36], "texture": 0}}, "type": "cube", "uuid": "f9c332b4-45f9-5cc0-5149-a8626c4c2752"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8, 11, -2], "to": [11, 13, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [3, 5, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 46, 47, 48], "texture": 0}, "east": {"uv": [40, 46, 44, 48], "texture": 0}, "south": {"uv": [52, 46, 55, 48], "texture": 0}, "west": {"uv": [48, 46, 52, 48], "texture": 0}, "up": {"uv": [48, 36, 44, 32], "texture": 0}, "down": {"uv": [52, 32, 48, 36], "texture": 0}}, "type": "cube", "uuid": "ede2ee68-73ee-c5c7-a7ed-960997fc8625"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [8, 11, -2], "to": [11, 13, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [3, 5, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 30, 47, 32], "texture": 0}, "east": {"uv": [40, 30, 44, 32], "texture": 0}, "south": {"uv": [52, 30, 55, 32], "texture": 0}, "west": {"uv": [48, 30, 52, 32], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "149d6250-cb67-80b9-55cc-5799aac4b66c"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-11, 11, -2], "to": [-8, 13, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-3, 5, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 62, 39, 64], "texture": 0}, "east": {"uv": [32, 62, 36, 64], "texture": 0}, "south": {"uv": [44, 62, 47, 64], "texture": 0}, "west": {"uv": [40, 62, 44, 64], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "acf60744-2801-2681-80dd-25958671fe5c"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-11, 11, -2], "to": [-8, 13, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-3, 5, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 62, 55, 64], "texture": 0}, "east": {"uv": [48, 62, 52, 64], "texture": 0}, "south": {"uv": [60, 62, 63, 64], "texture": 0}, "west": {"uv": [56, 62, 60, 64], "texture": 0}, "up": {"uv": [56, 52, 52, 48], "texture": 0}, "down": {"uv": [60, 48, 56, 52], "texture": 0}}, "type": "cube", "uuid": "6171e50f-871d-65da-059f-dd8642421c53"}], "outliner": [{"name": "armorHead", "origin": [0, 26, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", {"name": "antenna", "origin": [0, 2, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["704386d4-c64d-8236-e5e8-651bc0a273b0"]}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "f9c332b4-45f9-5cc0-5149-a8626c4c2752", "afd74594-73cd-c2aa-1667-9bbb690fc090"]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", "149d6250-cb67-80b9-55cc-5799aac4b66c", "ede2ee68-73ee-c5c7-a7ed-960997fc8625"]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "armNested", "origin": [-6, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134", "6171e50f-871d-65da-059f-dd8642421c53", "acf60744-2801-2681-80dd-25958671fe5c"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\geochelone_aerio_open_0.png", "name": "geochelone_aerio_open_0.png", "folder": "", "namespace": "", "id": "0", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "80d5a90d-ae59-91c4-3382-fa227d322521", "relative_path": "../textures/models/ben/geochelone_aerio_open_0.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\geochelone_aerio_closed_0.png", "name": "geochelone_aerio_closed_0.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "4fa519fa-2d80-8246-a66f-d7484f2dba5a", "relative_path": "../textures/models/ben/geochelone_aerio_closed_0.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAFhpJREFUeF7tnU2sXkUZx9+rLEzqBsPtx6IaEkyggU1FSVAgxQUbEwIJbICFaxITvlTqphtalY+u3OoC2GACIbohURo+XKjIhqY0kYRoF21p48omLtCauTdT5s6dj2fOPOfMO+f87gp6Zp555j/P8zvPzDnv+26cfPXBqyvv7/Lps9f+5fkXP/Evq/3/M0/dtMvWDbfefO3fXD/UBnUMnTlzafXGm5c2xrCNTRToQYGNHABikygBQyjRY3YBQA9hg49zUWAHAC5dvLI1rxd+eura/J59/+s75nrie/9clSS07WyAEbJlrz/98yNb/7m5bw8VwFyii3msvQJBAIQgYBLfTdYL57ZhceAr/4pO8vx/vrZ1bf/BPbugYu3ZxLdGAMDaxwwOzkiBXVuAUBXgzvcXv/nByrSxAHjlV3/Zuuwmsq0gHn38O9cAYBL7Jz/8/S7p3H5u8puG7772XrXUhw5tRm1wBlAtLwY6VyAKADsvm8wm8d2/jz74bMfd3b1uEt0ktoXEbbfv3dHXgiB19wcAnUcW7nehgKgCcJPbJr6f1KnZhvqEIEAF0EXM4OSMFMgCwNylTWIOSXxfJ9eG2UaY6oItwIyiial0p0B2C2BmZEr5WBnvbw1cBexdPrR9MAeD/h8VQHfxg8OdKzAIACaxbVK7/+0nf6yNqQQkANB4Ecgc9KX+eBGo8wjG/SoFgi8C2ScBIcumbPfv6KHT/VAb/9DPte/f/c01AFC1tnRGgawCQQCYXj4E7NMAeyaQtew1sHt+88+pfb/bDQCUqkx7FChTIAoA10yoIgjdsWNDD+0PAMoWk9YoUKqACAChisAOlAJBbCshhYcGAFKC8CJQabjQfm4KiAFQcneXwEEiJACQqEQbFBiuwMbRl+7b9XHg4ebkPY8/+dbGz5491GRs6yUVgHy9aDlPBQAA3wcwz8hmViIFAAAAEAUKjeapAAAAAPOMbGYlUgAAAABRoNBongoAAAAwz8hmViIFAAAAEAUKjeapAAAAAPOMbGYlUmDxABCpNFIjPok4krCYFSsAAMRS6TcEAPqaYrFMAQBQppdqawCgKifGBiiwtgA49fb5AdPZ3eXIvQeidnJfFqLiQMIIABhbYeznFAAAOYVGvA4ARhQX0yIFmv4uXurDQFQAovWjEQpUKQAAquSr60wFUKcfvesVaAqAO++4vunHgfftv65ewQoLAKBCPLqqKAAAVGQcZgQADNONXnoKNP9SDjOV506caQKiB+7fvJr67UA9meOWWs19irkxxvorAAASPx46xfIBgClUZoyYArsAYJ+N5+6M/il96nl7Tv5WSUAFkFsZrs9dAQBABTD3GGd+CQUAAAAgQRasAAAAAAsOf6YOAAAAWbBgBRYNgNa/S9DyEeiCY56pOwoAgMbh0OoJSONpM/yaKAAAGi8EAGi8AAsfHgA0DgAA0HgBFj48AGgcAACg8QIsfHgA0DgAAEDjBVj48ACgcQAAgMYLsPDhAUDjAAAAjRdg4cOvBQCWvAYAYMmr337uAKDxGgCAxguw8OEBQOMAAACNF2DhwwOAxgEAABovwMKHBwCNAwAANF6AhQ8PABoHAABovAALHx4ANA4AANB4ARY+PABoHAAAoPECLHx4ANA4AABA4wVY+PAAoHEAAIDGC7Dw4QFA4wAAAI0XYOHDA4DGAQAAGi/AwocHAI0DAAA0XoCFDw8AGgcAAGi8AAsfHgA0DgAA0HgBFj58FABWl9hvBGr+NuCS1wAALHn1288dADReAwDQeAEWPvzGa6899GVXg4cf/u1/SzQ5+tJ9V0vaa7c9/uRbG9o2S+y1/nER+2vOJT5rtn3jzUtN9T/56oNV8Xf59FmRHFc3DwbbtY4/kfOJRgCgUsEUALSSM/VT7VpjDJUBALS9AQ1dN9sPAFQq+MD9m9E7UCpxS4ZtneQpXwEAAKgqwUoSIdS2dQnGFoAtQG0Mt+y/cerYsetcB44cO/Z5iUOcARxqCsDW1QEVQOcVAAAowd3utlQAVAB1EdS2NxVApf4pAPjvSgwd6si9B6JdqQB4CjA0rkw/AFCj3mq1uvOO65tuAfbt37GDq5xNeXe2AGwBmiZA60NAAMAWoByb69ODCqByLTgDAACVIdS0OwColD8HgOdf/KRqhGeeuinZnzMAzgBqAgwA1Ki3Wq1iAKhNfN+tGAgAAACoCeHq97h5D2D3ewDayW8XOAQBAAAAAECNApV9QxUAAKgUtaA7HwYqECvQlAqgTr/gFgAAVIpa0B0AFIgFAOrECvXmDICnAPpRNZ3FjdSn2S5eKPpYwA6vU2+vuQ1jn7OWSvDxOx9uNc35Gnthxn5iz+yl/U/v2Tf5pHPxfdaqBHJPAmJaxfwPzVWqt3a7G269Wdtkkb1LF68Utfcbb+7bU9X/iUder67CaxzYGPMQb+PSuaxvWgAIQSD3ltwt9xxeWR/HAEB28iM3AAB5gQHASN/oY8hoxU2BQBMA+eXebmES3/4BAKlq47SjAphpBeCWRrWUTYWe3QJIwtNNfACwKZFs9DYAYAEASEVRLRyke7DYOFQAo+d4cgAAAACqIhAAxOXjDCAfWlPdgGKezPYQsDYx80u33aJ2HCoAqdLjtKMCoAKoiiwAQAVQE0BUACM+BZAszFQLwBnAF6tR+h6A9FegpO3cuKACoAKQcCLahgpgnAog93Vm/stRpe2t1wAAAACAKgX0AJBLYnekUgDE+rYAQG3VGVNcejNy+3MIOOGrmKGF5xBwOxxLkt+0rwGA239KAIyV+D4QSkAAAGYEgFASDf0cgQ2qGpvSx4Clya8BAGtjKgBMlfx23aQQAAAzAUAqiYZCoNamBABDkl8LAMbOQz86MtIGaNvs1IlfWg0AgBkAQJJEpRDQsLl0ALROfkk1MEsASMsfDULXjqVxBiBJ1tBdM3br07IHAOo+6qtVmqRiFAAspAKwwZSrBKTJLwHKWACIzaHEd6vHmFsAKoA8wkb5PoDau3Le7S9a1I41ZQVQkrQSDaQw8du5LwKVJG1uvNTBZWw+YwEgl/wXzm1XB/sP1n2hh9ROLE6pANaoAjAB4X4rkPQbgTSTaAxbbuLabxG285SMJ018P8kltlsCoDb57XwNBHK2ZguAEAFr78qSu5/kgMW3k3sPYCgATD9JsLsVwMbex1d/+N3r11z8/h3nt/671E5KqxDASgEwNPml1UALAEgStiQGTduczdkCwKWg/e/bbt+7S7+PPvjs2r+513OlWm4hamFjtgD+d+v7d0dpEuSS968f/js3na3r3z781WS7Un9s+9A8Sx815uYY8i3VBwDM8FVgWxW4URyCgrneAwDcO7cogwN3cpMYv3zh71vdj/7pG0Ezx7/7j61///HT39xVCUiTPnTn1QBALvH9Cbn+rhMAcndq6fqG2qVsz74CcAWpvSuXLELtWKEKwIxvqgA3cEsTMDSHEgCUaBBq6/se+gUhf46uHWkCp/yU2Ji6AgAAO1dM7SmAf9d37/hu+W+GX6ctQOw7BUPJUQOBP/75wOpv7717TX2/CrB3f9PgW3fdvbJnAkNA4N9xjd+xnxCLfZ26JHlzvklsAIAZbgFq78q5wNKsNlJfKhpKjhQEQolnffUB4G4F3OQPASBl19cqVG6nvh49B7rS0r9kK2C0HOuzAO7W0r3rT1UB+OMscgvg3/n9u7/5/9ZnAFoAyB2mDQFAzmau9LfXcwBIAVcTAKFxpgZAyc2lpu2iABA69Asle0jQ3gBg5jDko7D/23O4aAvwpSvbv3gk3WObdrFkHQqA2uS3vqeqJgDAFiAX58nrtduN0gpgKABMP/sYMPcUIPcYMJZYQwBgbPk/iWbtjw0Acy5x98N3Va1/rLN/Yxmz9Pd9CI21yC2AZGWXUgGsMwDcdQq9CSlZx5JKxT2QnAoAxj/pa7tD55uyDwAiqvYIAL8KkN4ptV4EClUAKR9yv5EYC/jcD65KEyU1/pQAsP66W9bcK7y5OUptAQAAsOPswBwKun/uYz8pUCSP2cwYACCXxuNe5+PACX2XUgGUvkMggQAAiAfW5dNnV7U/PKuFBfOyWeywk08DNv404JBDwNKDwNLklxzClTyJGFoBGD9qtwG5scfaAgAAGb7U3gR0h6s9mZe5vt2qdqyhAJBCYGjypyBQkvySLYCf5G7SagIgNM6YADBzb10F2O+boAKY2RlACAAl4NJsm9supO7CsQTXgIDExhivApu7v/1bFwAYf0IQYAvQ6RYgBYCSV3dTIJDaAQDbKrqJv44AsD65IAAAHQPALqjkMK50KxBLaslYPlSGVAD+1qF0KyC5+5sxNCqAUOK7GrSqAmz5H4O8AQEAmBEAhry7HwsOia3cnd/a1gCAtZUDQWisVB8LtNJXgnNJ3wMAjI/PnTizobkdLLXFIeA78Xfuc8HuVwCSpJUukMTW2ACQHCDm5pPTsLQyyo338iufbjV57NEbm58DuBVAyC8A0OGnAWMBaAJZkrS5ALbXc7akyZ9L4lyC5vqn5iOxrQkAm2TWJwuB1luAmF8AYCEACO3bY4/yJHv8HGy0zgBK7LhtJYnvVk+pBHHtptr519wqYN0A4PrGFmAGZwBmQW3ipk7u/a/pcoM7di1mb8oKQAqCksRPAcAv4c3/pxI8dx0AxOs0zgAUzgD8s4CQ3LlHernrMVhIthRDDwFTtn2bQ5LfgjOX3LkEl1yfGgKh/b+rp92eUAHMpAJwqwA/cUJ3a8nbfLH9ccndP7eHH5q4ACCNXgAguTXN6AwAAHwuXPGdzQzkqAAGSafSiS2A4hYgBgHNCqD07p+rAMz1IVWARgVgbNgvIMkdBOauu9sA9xGgzZKWWwDrQ+hRIFuAGW0BpFWApLSXtJHeAnKfyBsCgVoA2P7uNxC5Se4nceqaRId1AEDITwCwEABIglTSZowKwI5bUgnUAMDtG/s+QokWJW0AQFitpq8hliwgbVEABfQVAAD6mmIRBbpRAAB0s1Q4igL6CgAAfU2xiALdKAAAulkqHEUBfQUAgL6mWESBbhQAAN0sFY6igL4CAEBfUyyiQDcKAIBulgpHUUBfAQCgrykWUaAbBQBAN0uFoyigrwAA0NcUiyjQjQIAoJulwlEU0FcAAOhrikUU6EYBANDNUuEoCugrAAD0NcUiCnSjAADoZqlwFAX0FQAA+ppiEQW6UQAAdLNUOIoC+goAAH1NsYgC3SgAALpZKhxFAX0FAIC+plhEgW4UAADdLBWOooC+AgBAX1MsokA3CgCAbpYKR1FAXwEAoK8pFlGgGwUAQDdLhaMooK8AANDXFIso0I0CAKCbpcJRFNBXAADoa4pFFOhGAQDQzVLhKAroKwAA9DXFIgp0owAA6GapcBQF9BUAAPqaYhEFulEAAHSzVDiKAvoKAAB9TbGIAt0oAAC6WSocRQF9BQCAvqZYRIFuFAAA3SwVjqKAvgIAQF9TLKJANwoAgG6WCkdRQF8BAKCvKRZRoBsFAEA3S4WjKKCvAADQ1xSLKNCNAgCgm6XCURTQVwAA6GuKRRToRgEA0M1S4SgK6CsAAPQ1xSIKdKMAAOhmqXAUBfQVAAD6mmIRBbpRAAB0s1Q4igL6CgAAfU2xiALdKAAAulkqHEUBfQUAgL6mWESBbhQAAN0sFY6igL4CAEBfUyyiQDcKAIBulgpHUUBfAQCgrykWUaAbBQBAN0uFoyigrwAA0NcUiyjQjQIAoJulwlEU0FcAAOhrikUU6EYBANDNUuEoCugrAAD0NcUiCnSjAACoXKqjL913tdJEVffjT77VdA1Pvvpg1fwvXbwSnP/GpXMiXZ47cabp/EVOrnEjxKtcHAAAACpDqGl3AFApPwAAAJUh1LQ7AKiUHwAAgMoQatodAFTKDwAAQGUINe0OACrlBwAAoDKEmnYHAJXyAwAAUBlCTbsDgEr5AQAAqAyhpt0BQKX8AAAAVIZQ0+4AoFJ+AAAAKkOoaXcAUCk/AAAAlSHUtDsAqJQfAACAyhBq2h0AVMoPAABAZQg17Q4AKuUHAACgMoSadgcAlfIDAABQGUJNuwOASvkBAACoDKGm3RcPgNoEln5u/dTb5wcv9L791xX3vXjh82Qfa/OWew4X23Y7bO7bU9W/decnHnl90Tmw6Mmb4JsCAFc3D44a5x+/82HSvg8DFygAAACMGpzrbnxMANjEN3fJ2DffaOqTA0FoLAAAADRjsDtbtQBITdgtj6cAgPWlBAQAAAB0l7SaDtcCQLoHnhIARp+p/JKOo7lmmrY4A9BUs0NbACD8pZzSpQQAUqXWsx2HgJXf6itNACqA9UwAKoD1XJfJvKICoAKYLNjWcCAqACqAqrCUVkBVg4zYmQpgRHF7ME0FQAXQQ5yO5SMVABWAWmz1Ug245zGtf1lJTfyBhgDADAFQkohjHE6WjD8wbgd1C80VAAyScj6d5rgFKEnAMQBQ8h7CVJEUmycAmGoF1nQcAFB3BpBb1hIY5WwNuZ4DHAAYouqM+gCAcQHQshrIJb/xDQDMKJmHTGUqABjfJAE5ZA5+n5K77hQ+lfijMX9rQzI3AKCpeIe2AAAVQIdhq+YyTwEmegow1wrgwrltgOw/mP5ikKmrgNzd3/r965PvLzoHFj15E7hUAPUVgEmmHgFgfGYLoFZM9GkIANQDwKx8DgLrVAG4vgKAPvNWzWsAAADUgqlDQ2wBRjwD+OiDz4IhkSuXa+Oo5G6b2yuX+JKqAkp8Khkz1jY2L99HKgANtTu2oVUBhJL9ttv37lBGM9lSkpckm6ZPAKC/RKACGLEC8MNBM9kAQDrZqABkMAIAIwLAVgW2ElgSAPxqoKQqkYWuHACuL2wBduoGABQA4Jb/ftlv5I6dBUienw9JhpJkGwtK6woAX0/OAIZE2Iz6aJ0BWEn8ZHeBMFay+csBAHa+dp06mwAAM0rmIVPRBkDKh6UAIJRwJVAaso6585YYBACAhtod2wAAOu8BmBBIvRbcGgAx/wBAx8mr4ToAqAOATfrcecY6AMDGi+sznwXQyKKObQCAOgBIl35qABi/JFsuKgDpCs60HQAYHwDmJ9RvuPXmSSPo8umzK8mvMgOASZdl/QYDAABg/aJyOo94D0DhPQDpcklKUqmtVLuScntsn8zd3/y1qADMuLkqgApAI+I6tkEFMG4FYAEwJQRM+W//AEA6OakAqABGxbcLADvQWNWAm/gAQLasAAAAyCJlQKtQ8rtmtEAQSnx3nFQVwBZgwMLOqQtbgPG2ADkA1MAgl/QAQJalVABUALJIGdDKBcDLr3y6ZeGxR28cYEneJTQOFUBcPwAAAOTZVdjSAsAmpe0+FgRi4wAAABBVgC3A+FsAPzH9SkAKiFS71BgAAAAAgIgCY74HEKsAcgAIbRVKIeLaAAAAAABMDIDQ/t91wd0G5JLb9Mu1yV2PQYCnAIX7urk1ZwswzhYAAPSRKRwCcgg4SqQCgFFkVTcKAACAelAZg6F3AFKPAnMHgbnr7jYh9JSBLUB4mQEAAJgMALmB3CT3kzh1LWfXXAcAACCoAGcA458BSBJ07DYAIKzw/wFQQWfxL/QUrgAAAABJRU5ErkJggg=="}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_geochelone_aerio_open_0.png", "name": "ov_geochelone_aerio_open_0.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "7186b21a-bca1-6def-ae1d-1f87e0a01bd4", "relative_path": "../textures/models/ben/ov_geochelone_aerio_open_0.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\overlays\\glow\\ov_geochelone_aerio_glow_0.png", "name": "ov_geochelone_aerio_glow_0.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "88a162e5-9a88-de38-8ae1-f518dbfd04ee", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": false, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [8, 2], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 91, "height": 167, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFsAAACnCAYAAAB+ZiD7AAAAAXNSR0IArs4c6QAAAmZJREFUeF7t1W0KAVEARmGzF2uwPdmeNdgLjVKTj+TjHqnHLzLdl8dpTNv9+rhaPHabw7R8/crzb571yu6/XHuGnZE+Qb7+st8+718wn33Otyt+drD3bwVgh1XAhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KhFPKhh0KDJra7tfH5dG7zWFS9gDsa+jLBOwB2PORyh4E++jYC/h8C5mvUfbAHwD2QNzl0W4jP4L2BzkY/l7ZJ8UpLYDq4rZQAAAAAElFTkSuQmCC"}], "relative_path": "../textures/overlays/glow/ov_geochelone_aerio_glow_0.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json", "itemModelPath": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\geo\\gun.item.json"}}