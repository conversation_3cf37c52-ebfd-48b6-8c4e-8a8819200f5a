{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "highbreed", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 28.5, -4], "to": [3, 34.5, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 3, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 0}, "east": {"uv": [0, 8, 8, 16], "texture": 0}, "south": {"uv": [24, 8, 32, 16], "texture": 0}, "west": {"uv": [16, 8, 24, 16], "texture": 0}, "up": {"uv": [16, 8, 8, 0], "texture": 0}, "down": {"uv": [24, 0, 16, 8], "texture": 0}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 28.5, -4], "to": [3, 34.5, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 3, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 0}, "east": {"uv": [32, 8, 40, 16], "texture": 0}, "south": {"uv": [56, 8, 64, 16], "texture": 0}, "west": {"uv": [48, 8, 56, 16], "texture": 0}, "up": {"uv": [48, 8, 40, 0], "texture": 0}, "down": {"uv": [56, 0, 48, 8], "texture": 0}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 19, -2.5], "to": [5.5, 25, 2.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1.5, 1, -0.5], "uv_offset": [16, 16], "faces": {"north": {"uv": [20, 20, 28, 26], "texture": 0}, "east": {"uv": [16, 20, 20, 26], "texture": 0}, "south": {"uv": [32, 20, 40, 26], "texture": 0}, "west": {"uv": [28, 20, 32, 26], "texture": 0}, "up": {"uv": [28, 20, 20, 16], "texture": 0}, "down": {"uv": [36, 16, 28, 20], "texture": 0}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 19, -2.5], "to": [5.5, 26, 2.5], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1.5, 1, -0.5], "uv_offset": [16, 32], "faces": {"north": {"uv": [20, 36, 28, 42], "texture": 0}, "east": {"uv": [16, 36, 20, 42], "texture": 0}, "south": {"uv": [32, 36, 40, 43.875], "texture": 0}, "west": {"uv": [28, 36, 32, 42], "texture": 0}, "up": {"uv": [28, 36, 20, 32], "texture": 0}, "down": {"uv": [36, 32, 28, 36], "texture": 0}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6.5, 11, -2], "to": [10.5, 26, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [2, 2, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 0}, "east": {"uv": [40, 20, 44, 32], "texture": 0}, "south": {"uv": [52, 20, 56, 32], "texture": 0}, "west": {"uv": [48, 20, 52, 32], "texture": 0}, "up": {"uv": [48, 20, 44, 16], "texture": 0}, "down": {"uv": [52, 16, 48, 20], "texture": 0}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [6.5, 11, -2], "to": [10.5, 26, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [2, 2, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 0}, "east": {"uv": [40, 36, 44, 48], "texture": 0}, "south": {"uv": [52, 36, 56, 48], "texture": 0}, "west": {"uv": [48, 36, 52, 48], "texture": 0}, "up": {"uv": [48, 36, 44, 32], "texture": 0}, "down": {"uv": [52, 32, 48, 36], "texture": 0}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10.5, 11, -2], "to": [-6.5, 26, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-2, 2, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 0}, "east": {"uv": [32, 52, 36, 64], "texture": 0}, "south": {"uv": [44, 52, 48, 64], "texture": 0}, "west": {"uv": [40, 52, 44, 64], "texture": 0}, "up": {"uv": [40, 52, 36, 48], "texture": 0}, "down": {"uv": [44, 48, 40, 52], "texture": 0}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-10.5, 11, -2], "to": [-6.5, 26, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-2, 2, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 0}, "east": {"uv": [48, 52, 52, 64], "texture": 0}, "south": {"uv": [60, 52, 64, 64], "texture": 0}, "west": {"uv": [56, 52, 60, 64], "texture": 0}, "up": {"uv": [56, 52, 52, 48], "texture": 0}, "down": {"uv": [60, 48, 56, 52], "texture": 0}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0.09999990463256836, 0, 0], "uv_offset": [1, 16], "faces": {"north": {"uv": [5, 20, 8, 32], "texture": 0}, "east": {"uv": [1, 20, 5, 32], "texture": 0}, "south": {"uv": [12, 20, 15, 32], "texture": 0}, "west": {"uv": [8, 20, 12, 32], "texture": 0}, "up": {"uv": [8, 20, 5, 16], "texture": 0}, "down": {"uv": [11, 16, 8, 20], "texture": 0}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 0, -2], "to": [3.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0.09999990463256836, 0, 0], "uv_offset": [1, 32], "faces": {"north": {"uv": [5, 36, 8, 48], "texture": 0}, "east": {"uv": [1, 36, 5, 48], "texture": 0}, "south": {"uv": [12, 36, 15, 48], "texture": 0}, "west": {"uv": [8, 36, 12, 48], "texture": 0}, "up": {"uv": [8, 36, 5, 32], "texture": 0}, "down": {"uv": [11, 32, 8, 36], "texture": 0}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -2], "to": [-0.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 23, 64], "texture": 0}, "east": {"uv": [16, 52, 20, 64], "texture": 0}, "south": {"uv": [27, 52, 30, 64], "texture": 0}, "west": {"uv": [23, 52, 27, 64], "texture": 0}, "up": {"uv": [23, 52, 20, 48], "texture": 0}, "down": {"uv": [26, 48, 23, 52], "texture": 0}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 0, -2], "to": [-0.5, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.09999990463256836, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 7, 64], "texture": 0}, "east": {"uv": [0, 52, 4, 64], "texture": 0}, "south": {"uv": [11, 52, 14, 64], "texture": 0}, "west": {"uv": [7, 52, 11, 64], "texture": 0}, "up": {"uv": [7, 52, 4, 48], "texture": 0}, "down": {"uv": [10, 48, 7, 52], "texture": 0}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 16, -2], "to": [4, 18, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 23], "faces": {"north": {"uv": [20, 26, 28, 28], "texture": 0}, "east": {"uv": [16, 26, 20, 28], "texture": 0}, "south": {"uv": [16, 26, 23, 28], "texture": 0}, "west": {"uv": [28, 26, 32, 28], "texture": 0}, "up": {"uv": [38.625, 29, 30.625, 25], "texture": 0}, "down": {"uv": [38.625, 29, 30.625, 25], "texture": 0}}, "type": "cube", "uuid": "8725b865-28c3-0904-ccb2-94caa5409736"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 16, -2], "to": [4, 17.5, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 37], "faces": {"north": {"uv": [20, 40.5, 28, 42], "texture": 0}, "east": {"uv": [16, 40, 20, 42], "texture": 0}, "south": {"uv": [28, 34, 35, 36], "texture": null}, "west": {"uv": [28, 40, 32, 42], "texture": 0}, "up": {"uv": [38.625, 29, 30.625, 25], "texture": null}, "down": {"uv": [41.25, 31.25, 33.25, 27.25], "texture": null}}, "type": "cube", "uuid": "f5c23052-9212-5e2a-ef99-3eedd45f50a6"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.5, 25, -3], "to": [4.5, 27.8, 3], "autouv": 0, "color": 0, "origin": [-0.5, 27, -0.5], "uv_offset": [30, 23], "faces": {"north": {"uv": [33.125, 27.125, 35.125, 29.125], "texture": 0}, "east": {"uv": [31.125, 27.125, 33.125, 29.125], "texture": 0}, "south": {"uv": [37.125, 27.125, 39.125, 29.125], "texture": 0}, "west": {"uv": [35.125, 27.125, 37.125, 29.125], "texture": 0}, "up": {"uv": [35.125, 27.125, 33.125, 25.125], "texture": 0}, "down": {"uv": [37.125, 25.125, 35.125, 27.125], "texture": 0}}, "type": "cube", "uuid": "60286f45-9e2b-a5d4-8ae3-bf88be3b20a1"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 13, -2], "to": [3.5, 15, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.5, 1, 0], "uv_offset": [16, 23], "faces": {"north": {"uv": [20, 30, 28, 32], "texture": 0}, "east": {"uv": [16, 30, 20, 32], "texture": 0}, "south": {"uv": [16, 30, 23, 32], "texture": 0}, "west": {"uv": [28, 30, 32, 32], "texture": 0}, "up": {"uv": [38.625, 29, 30.625, 25], "texture": 0}, "down": {"uv": [38.625, 29, 30.625, 25], "texture": 0}}, "type": "cube", "uuid": "c08da958-8ada-80ea-73a5-b2e54845f0be"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 13, -2], "to": [3.5, 14.5, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.5, 1, 0], "uv_offset": [16, 37], "faces": {"north": {"uv": [20, 44, 28, 46], "texture": 0}, "east": {"uv": [16, 44, 20, 46], "texture": 0}, "south": {"uv": [32, 44, 39, 46], "texture": 0}, "west": {"uv": [28, 44, 32, 46], "texture": 0}, "up": {"uv": [38.625, 29, 30.625, 25], "texture": null}, "down": {"uv": [38.625, 29, 30.625, 25], "texture": 0}}, "type": "cube", "uuid": "15e72d32-4d77-55d7-805c-1c4d4c97e6ac"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.955000000000001, 0.25, -2.5], "to": [-4.835000000000001, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, -45, 0], "origin": [-1.9350000000000014, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}}, "type": "cube", "uuid": "*************-18f6-ad83-b3a62964fb1b"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4.355, 0.25, -2.5], "to": [-4.235, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, -22.5, 0], "origin": [-1.3350000000000009, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}}, "type": "cube", "uuid": "cdd74265-72c9-0472-2e25-e7908af001c9"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.7550000000000012, 0.25, -4.1], "to": [-2.635000000000001, 0.050000000000000044, 0.5], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, 22.5, 0], "origin": [0.2649999999999988, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}}, "type": "cube", "uuid": "0678cbb7-c807-1e27-6f2a-2cda54e9a83f"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.633507018502488, 0.75, -2.6834469717727103], "to": [-3.5135070185024877, 0.55, 1.9165530282272893], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-45, 22.499999999999986, 0], "origin": [-3.735000000000001, 0.65, -2.5], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}}, "type": "cube", "uuid": "a1748af9-7bc4-8ef5-71a7-6ae684968271"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.06783616041121587, 0.24999999999999145, -2.6644168549005327], "to": [0.05216383958878357, 0.049999999999991385, 1.935583145099467], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [1.6152119899410566e-14, 22.49999999999998, -179.9999999999999], "origin": [-0.26500000000000146, 0.15000000000000002, -2.25], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}}, "type": "cube", "uuid": "16341bbd-a156-c374-a2c8-3d5c21945555"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.06783616041121587, 0.8499999999999912, -2.764416854900533], "to": [0.05216383958878357, 0.6499999999999915, 1.8355831450994669], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [49.99999999999999, 22.499999999999986, -179.9999999999999], "origin": [-0.26500000000000146, 0.75, -2.35], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}}, "type": "cube", "uuid": "939b9108-cedd-3a1f-ac4f-b4bbc247c2c0"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.685000000000001, 0.25, -2.5], "to": [-5.565000000000001, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-1.6152119899410566e-14, -22.49999999999998, -179.9999999999999], "origin": [-2.6650000000000014, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}}, "type": "cube", "uuid": "1256323b-ab78-6e81-1cbf-0d42ba02cf3d"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.085000000000001, 0.25, -2.5], "to": [-4.965000000000001, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [4.7077028430832684e-14, -45.00000000000001, 179.9999999999999], "origin": [-2.0650000000000013, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "east": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "south": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "west": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "up": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}, "down": {"uv": [8.625, 18.125, 10.75, 19.75], "texture": 0}}, "type": "cube", "uuid": "2c2c0b44-6ee7-b4bb-ab20-3ca4ed159b84"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.835000000000001, 0.25, -2.5], "to": [4.955000000000001, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, 45, 0], "origin": [1.9350000000000014, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}}, "type": "cube", "uuid": "239abc1b-88c4-d1c4-797c-4d70272cf048"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.235, 0.25, -2.5], "to": [4.355, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, 22.5, 0], "origin": [1.3350000000000009, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}}, "type": "cube", "uuid": "6b0be881-87c7-16ea-1168-fb7616218712"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [2.635000000000001, 0.25, -4.1], "to": [2.7550000000000012, 0.050000000000000044, 0.5], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [0, -22.5, 0], "origin": [-0.2649999999999988, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}}, "type": "cube", "uuid": "a9e16a8b-61cd-45b0-8450-79587211bd55"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [3.5135070185024877, 0.75, -2.6834469717727103], "to": [3.633507018502488, 0.55, 1.9165530282272893], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-45, -22.499999999999986, 0], "origin": [3.735000000000001, 0.65, -2.5], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}}, "type": "cube", "uuid": "caec6b17-23cc-677c-185c-11a81a4342ec"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.05216383958878357, 0.24999999999999145, -2.6644168549005327], "to": [0.06783616041121587, 0.049999999999991385, 1.935583145099467], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [1.6152119899410566e-14, -22.49999999999998, 179.9999999999999], "origin": [0.26500000000000146, 0.15000000000000002, -2.25], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}}, "type": "cube", "uuid": "ad36e9e8-a0a1-9054-d906-2a5088ed040f"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.05216383958878357, 0.8499999999999912, -2.764416854900533], "to": [0.06783616041121587, 0.6499999999999915, 1.8355831450994669], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [49.99999999999999, -22.499999999999986, 179.9999999999999], "origin": [0.26500000000000146, 0.75, -2.35], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}}, "type": "cube", "uuid": "1b5fc88c-5ad5-5428-bce9-5d4d1ba9a204"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.565000000000001, 0.25, -2.5], "to": [5.685000000000001, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [-1.6152119899410566e-14, 22.49999999999998, 179.9999999999999], "origin": [2.6650000000000014, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}}, "type": "cube", "uuid": "cdf237a7-2828-6449-d2ad-950509b74116"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4.965000000000001, 0.25, -2.5], "to": [5.085000000000001, 0.050000000000000044, -2.1], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [4.7077028430832684e-14, 45.00000000000001, -179.9999999999999], "origin": [2.0650000000000013, 0.15000000000000002, -1], "uv_offset": [16, 48], "faces": {"north": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "east": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "south": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "west": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "up": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}, "down": {"uv": [10.75, 18.125, 8.625, 19.75], "texture": 0}}, "type": "cube", "uuid": "1455b9e1-131f-8647-6f7b-94306e7473a6"}, {"name": "appoplexian_claws", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [10.7, 7, -0.9], "to": [11.7, 11, 1.1], "autouv": 0, "color": 3, "visibility": false, "export": false, "origin": [-1, 0, 0], "faces": {"north": {"uv": [42.5, 45.75, 42, 47.75], "texture": 0}, "east": {"uv": [42.5, 45.75, 42, 47.75], "texture": 0}, "south": {"uv": [42.5, 45.75, 42, 48], "texture": 0}, "west": {"uv": [42.5, 45.75, 42, 47.75], "texture": 0}, "up": {"uv": [42, 48, 42.5, 45.75], "texture": 0}, "down": {"uv": [42, 45.75, 42.5, 48], "texture": 0}}, "type": "cube", "uuid": "62f418bd-8c91-630b-9bc7-ef278c5d6c77"}, {"name": "appoplexian_claws", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [10.7, 10, -1.5], "to": [11.7, 16, 1.5], "autouv": 0, "color": 3, "visibility": false, "export": false, "origin": [-1, 0, 0], "faces": {"north": {"uv": [42.5, 45.75, 42, 47.75], "texture": 0}, "east": {"uv": [42.5, 45.75, 42, 47.75], "texture": 0}, "south": {"uv": [42.5, 45.75, 42, 48], "texture": 0}, "west": {"uv": [42.5, 45.75, 42, 47.75], "texture": 0}, "up": {"uv": [42, 48, 42.5, 45.75], "texture": 0}, "down": {"uv": [42, 45.75, 42.5, 48], "texture": 0}}, "type": "cube", "uuid": "7da7bd43-c93f-ca2d-83a1-************"}, {"name": "appoplexian_claws", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-11.7, 10, -1.5], "to": [-10.7, 16, 1.5], "autouv": 0, "color": 3, "visibility": false, "export": false, "origin": [1, 0, 0], "faces": {"north": {"uv": [42, 45.75, 42.5, 47.75], "texture": 0}, "east": {"uv": [42, 45.75, 42.5, 47.75], "texture": 0}, "south": {"uv": [42, 45.75, 42.5, 48], "texture": 0}, "west": {"uv": [42, 45.75, 42.5, 47.75], "texture": 0}, "up": {"uv": [42.5, 48, 42, 45.75], "texture": 0}, "down": {"uv": [42.5, 45.75, 42, 48], "texture": 0}}, "type": "cube", "uuid": "987679a6-c13d-7094-3a24-d9bd91b37445"}, {"name": "appoplexian_claws", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-11.7, 7, -0.9], "to": [-10.7, 11, 1.1], "autouv": 0, "color": 3, "visibility": false, "export": false, "origin": [1, 0, 0], "faces": {"north": {"uv": [42, 45.75, 42.5, 47.75], "texture": 0}, "east": {"uv": [42, 45.75, 42.5, 47.75], "texture": 0}, "south": {"uv": [42, 45.75, 42.5, 48], "texture": 0}, "west": {"uv": [42, 45.75, 42.5, 47.75], "texture": 0}, "up": {"uv": [42.5, 48, 42, 45.75], "texture": 0}, "down": {"uv": [42.5, 45.75, 42, 48], "texture": 0}}, "type": "cube", "uuid": "695bdb76-3206-fd10-4858-b5ea5601b351"}], "outliner": [{"name": "armorHead", "origin": [0, 26, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", "b22fcc6e-4535-efd8-a448-a53d0dda5b74", "60286f45-9e2b-a5d4-8ae3-bf88be3b20a1", {"name": "antenna", "origin": [0, 2, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "f5c23052-9212-5e2a-ef99-3eedd45f50a6", "8725b865-28c3-0904-ccb2-94caa5409736", "15e72d32-4d77-55d7-805c-1c4d4c97e6ac", "c08da958-8ada-80ea-73a5-b2e54845f0be"]}, {"name": "armorRightArm", "origin": [6, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", "7da7bd43-c93f-ca2d-83a1-************", "62f418bd-8c91-630b-9bc7-ef278c5d6c77", {"name": "wingRight", "origin": [6, 22, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-5, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-5, 22, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-6, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134"]}, "695bdb76-3206-fd10-4858-b5ea5601b351", "987679a6-c13d-7094-3a24-d9bd91b37445"]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806", "1455b9e1-131f-8647-6f7b-94306e7473a6", "cdf237a7-2828-6449-d2ad-950509b74116", "1b5fc88c-5ad5-5428-bce9-5d4d1ba9a204", "ad36e9e8-a0a1-9054-d906-2a5088ed040f", "caec6b17-23cc-677c-185c-11a81a4342ec", "a9e16a8b-61cd-45b0-8450-79587211bd55", "6b0be881-87c7-16ea-1168-fb7616218712", "239abc1b-88c4-d1c4-797c-4d70272cf048"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb", "2c2c0b44-6ee7-b4bb-ab20-3ca4ed159b84", "1256323b-ab78-6e81-1cbf-0d42ba02cf3d", "939b9108-cedd-3a1f-ac4f-b4bbc247c2c0", "16341bbd-a156-c374-a2c8-3d5c21945555", "a1748af9-7bc4-8ef5-71a7-6ae684968271", "0678cbb7-c807-1e27-6f2a-2cda54e9a83f", "cdd74265-72c9-0472-2e25-e7908af001c9", "*************-18f6-ad83-b3a62964fb1b"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\5yl\\ov_omni_highbreed.png", "name": "ov_omni_highbreed.png", "folder": "", "namespace": "", "id": "1", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "a64d4784-6c60-3de3-d3a2-e0ec3e0d6769", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/5yl/ov_omni_highbreed.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\5yl\\omni_highbreed.png", "name": "omni_highbreed.png", "folder": "", "namespace": "", "id": "2", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "2e6b0bed-cce7-da5e-5745-a9099c1e31e7", "source": "data:image/png;base64,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", "relative_path": "../textures/models/ben/5yl/omni_highbreed.png"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": true, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1e568959-0d20-9557-8812-37c956b12a69": {"name": "wingLeft", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "f9de9cb8-5cf1-0ded-75eb-83afb28ee1a0", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "28632363-b4fd-708c-56ec-e1d83fb11dcd", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "f1d4109d-9588-6d62-efaa-6fefa1d52b90", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "c194e24a-3f6a-b4fb-86a1-6fd8b6e74fc8", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a0745c1f-b58d-3e09-d45a-1d245c41f62c", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25\n", "z": 0}], "uuid": "2ba9155f-5608-43f2-5d7b-e3d043b5dea7", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "ac639a34-9dce-ac46-364f-6ccc863f9839", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "68717791-2da1-ede0-5ff8-f4f3f3dda103", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "7c3c60ec-0041-4f20-0f6b-abecd6dec4ff", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "cf4418bd-080e-3fb8-4449-5fd8a192b7a1", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": true, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a": {"name": "armNested", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "23e2481b-107d-ba9a-463e-acde1a16150c", "time": 0.6666666666666666, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": 0, "y": -2, "z": 0}], "uuid": "bec207c9-58d6-a671-8263-add0460e967c", "time": 0.3333333333333333, "color": -1, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "position", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "74de0a3b-f4c8-b853-3cd1-2ca0ba6ffd11", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": "1", "y": "1", "z": "1"}], "uuid": "58ef6caf-eb2e-88b5-fd86-8395549f6bdc", "time": 0, "color": -1, "uniform": true, "interpolation": "linear"}, {"channel": "scale", "data_points": [{"x": 1, "y": 1.8000000000000007, "z": 1}], "uuid": "4a3f805e-4d73-c9c9-5adb-242298882a81", "time": 0.3333333333333333, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}, {"channel": "scale", "data_points": [{"x": 1, "y": "1", "z": 1}], "uuid": "0099247f-356d-8138-e408-d8a327befb41", "time": 0.6666666666666666, "color": -1, "uniform": false, "interpolation": "linear", "easing": "easeInExpo"}]}}}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}