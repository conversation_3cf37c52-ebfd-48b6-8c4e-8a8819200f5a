{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "sp<PERSON><PERSON>", "model_identifier": "", "visible_box": [3, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "armorHead", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 24, -4], "to": [4, 32, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 0, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 3}, "east": {"uv": [0, 8, 8, 16], "texture": 3}, "south": {"uv": [24, 8, 32, 16], "texture": 3}, "west": {"uv": [16, 8, 24, 16], "texture": 3}, "up": {"uv": [16, 8, 8, 0], "texture": 3}, "down": {"uv": [24, 0, 16, 8], "texture": 3}}, "type": "cube", "uuid": "246fe58a-3d7e-3eea-5ba9-6f5f8c823a5a"}, {"name": "armorHead", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 24, -4], "to": [4, 32, 4], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 0, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 3}, "east": {"uv": [32, 8, 40, 16], "texture": 3}, "south": {"uv": [56, 8, 64, 16], "texture": 3}, "west": {"uv": [48, 8, 56, 16], "texture": 3}, "up": {"uv": [48, 8, 40, 0], "texture": 3}, "down": {"uv": [56, 0, 48, 8], "texture": 3}}, "type": "cube", "uuid": "30d696f3-4d93-ef5f-3aae-8babcd012279"}, {"name": "antenna", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.75, 32, -2], "to": [0.75, 35, 2], "autouv": 0, "color": 1, "origin": [-0.75, 0, 0], "faces": {"north": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "east": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "south": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "west": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "up": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "down": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}}, "type": "cube", "uuid": "c7a11f93-e299-ce88-37d5-b5dcd56d4670"}, {"name": "antenna", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 35, -2], "to": [0.5, 36, 2], "autouv": 0, "color": 1, "origin": [0, 0, 0], "faces": {"north": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "east": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "south": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "west": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "up": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "down": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}}, "type": "cube", "uuid": "cb5e9c89-c305-db23-560c-f350ed1c0a08"}, {"name": "antenna", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [7, 28.3, -2], "to": [8, 29.3, 2], "autouv": 0, "color": 1, "origin": [0, 0, 0], "faces": {"north": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "east": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "south": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "west": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "up": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "down": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}}, "type": "cube", "uuid": "bf6883d1-92e6-50f1-b5d4-c7830421af26"}, {"name": "antenna", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 27.8, -2], "to": [7, 29.3, 2], "autouv": 0, "color": 1, "origin": [0, 0, 0], "faces": {"north": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "east": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "south": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "west": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "up": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}, "down": {"uv": [36.5, 10.5, 34.5, 11], "texture": 3}}, "type": "cube", "uuid": "97d96f13-65d9-171a-3c2a-6d684c783cfc"}, {"name": "armorBody", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 11, -2], "to": [4, 23, 2], "autouv": 0, "color": 2, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [20, 20, 28, 32], "texture": 3}, "east": {"uv": [16, 20, 20, 32], "texture": 3}, "south": {"uv": [32, 20, 40, 32], "texture": 3}, "west": {"uv": [28, 20, 32, 32], "texture": 3}, "up": {"uv": [28, 20, 20, 16], "texture": 3}, "down": {"uv": [36, 16, 28, 20], "texture": 3}}, "type": "cube", "uuid": "fdba0483-1909-d679-e0c7-975d29f0e1e3"}, {"name": "armorBody", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 11, -2], "to": [4, 23, 2], "autouv": 0, "color": 2, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [20, 36, 28, 48], "texture": 3}, "east": {"uv": [16, 36, 20, 48], "texture": 3}, "south": {"uv": [32, 36, 40, 48], "texture": 3}, "west": {"uv": [28, 36, 32, 48], "texture": 3}, "up": {"uv": [28, 36, 20, 32], "texture": 3}, "down": {"uv": [36, 32, 28, 36], "texture": 3}}, "type": "cube", "uuid": "9826b7eb-d095-59e3-fa2f-99b5232d277f"}, {"name": "armorRightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 17, -2], "to": [8, 23, 2], "autouv": 0, "color": 3, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [44, 20, 47, 26], "texture": 3}, "east": {"uv": [40, 20, 44, 26], "texture": 3}, "south": {"uv": [52, 20, 55, 26], "texture": 3}, "west": {"uv": [48, 20, 52, 26], "texture": 3}, "up": {"uv": [47, 20, 44, 16], "texture": 3}, "down": {"uv": [51, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "43a2c0e1-c73c-3702-a272-f19d52670656"}, {"name": "armorRightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 17, -2], "to": [8, 23, 2], "autouv": 0, "color": 3, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [44, 36, 47, 42], "texture": 3}, "east": {"uv": [40, 36, 44, 42], "texture": 3}, "south": {"uv": [52, 36, 55, 42], "texture": 3}, "west": {"uv": [48, 36, 52, 42], "texture": 3}, "up": {"uv": [47, 36, 44, 32], "texture": 3}, "down": {"uv": [51, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "ac003f88-9de1-36e7-9b9b-c4663c47faf5"}, {"name": "armorRightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.2, 10, -2], "to": [8.4, 16, 2], "autouv": 0, "color": 3, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [44, 26, 47.2, 32], "texture": 3}, "east": {"uv": [40, 26, 44, 32], "texture": 3}, "south": {"uv": [52, 26, 55.2, 32], "texture": 3}, "west": {"uv": [48, 26, 52, 32], "texture": 3}, "up": {"uv": [47.2, 20, 44, 16], "texture": 3}, "down": {"uv": [51.2, 16, 48, 20], "texture": 3}}, "type": "cube", "uuid": "1647eaa9-291b-12c3-9494-7a0d1f2c8aac"}, {"name": "armorRightArm", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5.2, 10, -2], "to": [8.4, 16, 2], "autouv": 0, "color": 3, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [44, 42, 47.2, 48], "texture": 3}, "east": {"uv": [40, 42, 44, 48], "texture": 3}, "south": {"uv": [52, 42, 55.2, 48], "texture": 3}, "west": {"uv": [48, 42, 52, 48], "texture": 3}, "up": {"uv": [47.2, 36, 44, 32], "texture": 3}, "down": {"uv": [51.2, 32, 48, 36], "texture": 3}}, "type": "cube", "uuid": "76d61a18-30fb-fb66-d265-67b87d068014"}, {"name": "armNested", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 17, -2], "to": [-5, 23, 2], "autouv": 0, "color": 7, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [36, 52, 39, 58], "texture": 3}, "east": {"uv": [32, 52, 36, 58], "texture": 3}, "south": {"uv": [44, 52, 47, 58], "texture": 3}, "west": {"uv": [40, 52, 44, 58], "texture": 3}, "up": {"uv": [39, 52, 36, 48], "texture": 3}, "down": {"uv": [43, 48, 40, 52], "texture": 3}}, "type": "cube", "uuid": "64001849-1bab-3bc2-4aed-6336686eabe3"}, {"name": "armNested", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 17, -2], "to": [-5, 23, 2], "autouv": 0, "color": 7, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [52, 52, 55, 58], "texture": 3}, "east": {"uv": [48, 52, 52, 58], "texture": 3}, "south": {"uv": [60, 52, 63, 58], "texture": 3}, "west": {"uv": [56, 52, 60, 58], "texture": 3}, "up": {"uv": [55, 52, 52, 48], "texture": 3}, "down": {"uv": [59, 48, 56, 52], "texture": 3}}, "type": "cube", "uuid": "6d1f1db1-39bd-bf02-4ac4-d999492d6206"}, {"name": "armNested", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.2, 10, -2], "to": [-4.999999999999999, 16, 2], "autouv": 0, "color": 7, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [36, 59, 39.2, 64], "texture": 3}, "east": {"uv": [32, 59, 36, 64], "texture": 3}, "south": {"uv": [44, 59, 47.2, 64], "texture": 3}, "west": {"uv": [40, 59, 44, 64], "texture": 3}, "up": {"uv": [39.2, 52, 36, 48], "texture": 3}, "down": {"uv": [43.2, 48, 40, 52], "texture": 3}}, "type": "cube", "uuid": "2aec1a23-c172-8a94-8e83-5e0f9182bc47"}, {"name": "armNested", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8.2, 10, -2], "to": [-4.999999999999999, 16, 2], "autouv": 0, "color": 7, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [52, 59, 55.2, 64], "texture": 3}, "east": {"uv": [48, 59, 52, 64], "texture": 3}, "south": {"uv": [60, 59, 63.2, 64], "texture": 3}, "west": {"uv": [56, 59, 60, 64], "texture": 3}, "up": {"uv": [55.2, 52, 52, 48], "texture": 3}, "down": {"uv": [59.2, 48, 56, 52], "texture": 3}}, "type": "cube", "uuid": "5e9d229f-6e37-c4b1-8b3c-5913df7126ab"}, {"name": "armorRightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 0, -2], "to": [4, 10, 2], "autouv": 0, "color": 8, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 3}, "east": {"uv": [0, 20, 4, 32], "texture": 3}, "south": {"uv": [12, 20, 16, 32], "texture": 3}, "west": {"uv": [8, 20, 12, 32], "texture": 3}, "up": {"uv": [8, 20, 4, 16], "texture": 3}, "down": {"uv": [12, 16, 8, 20], "texture": 3}}, "type": "cube", "uuid": "6386488d-edbb-88d7-8673-d8e5954915a6"}, {"name": "armorRightLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0, 0, -2], "to": [4, 10, 2], "autouv": 0, "color": 8, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 3}, "east": {"uv": [0, 36, 4, 48], "texture": 3}, "south": {"uv": [12, 36, 16, 48], "texture": 3}, "west": {"uv": [8, 36, 12, 48], "texture": 3}, "up": {"uv": [8, 36, 4, 32], "texture": 3}, "down": {"uv": [12, 32, 8, 36], "texture": 3}}, "type": "cube", "uuid": "4d27dc08-9141-d34c-150d-1238dd962ae2"}, {"name": "armorLeftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 0, -2], "to": [0, 10, 2], "autouv": 0, "color": 9, "inflate": 0.5, "origin": [0, 0, 0], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 3}, "east": {"uv": [16, 52, 20, 64], "texture": 3}, "south": {"uv": [28, 52, 32, 64], "texture": 3}, "west": {"uv": [24, 52, 28, 64], "texture": 3}, "up": {"uv": [24, 52, 20, 48], "texture": 3}, "down": {"uv": [28, 48, 24, 52], "texture": 3}}, "type": "cube", "uuid": "86c7e477-717c-70c4-ea14-cfb08c1abb3b"}, {"name": "armorLeftLeg", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-4, 0, -2], "to": [0, 10, 2], "autouv": 0, "color": 9, "inflate": 0.75, "origin": [0, 0, 0], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 3}, "east": {"uv": [0, 52, 4, 64], "texture": 3}, "south": {"uv": [12, 52, 16, 64], "texture": 3}, "west": {"uv": [8, 52, 12, 64], "texture": 3}, "up": {"uv": [8, 52, 4, 48], "texture": 3}, "down": {"uv": [12, 48, 8, 52], "texture": 3}}, "type": "cube", "uuid": "958ce6c0-dc69-c14e-643d-0754189c8e8e"}, {"name": "antenna", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-7, 27.8, -2], "to": [-4, 29.3, 2], "autouv": 0, "color": 1, "origin": [-6.5, 28.675, 0], "faces": {"north": {"uv": [34.5, 10.5, 36.5, 11], "texture": 3}, "east": {"uv": [34.5, 10.5, 36.5, 11], "texture": 3}, "south": {"uv": [34.5, 10.5, 36.5, 11], "texture": 3}, "west": {"uv": [34.5, 10.5, 36.5, 11], "texture": 3}, "up": {"uv": [34.5, 10.5, 36.5, 11], "texture": 3}, "down": {"uv": [34.5, 10.5, 36.5, 11], "texture": 3}}, "type": "cube", "uuid": "2507bffa-3f34-2550-ec88-e87508ae10c4"}, {"name": "antenna", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-8, 28.3, -2], "to": [-7, 29.3, 2], "autouv": 0, "color": 1, "origin": [-6.5, 28.675, 0], "faces": {"north": {"uv": [34.5, 10.5, 36.5, 11], "texture": 3}, "east": {"uv": [34.5, 10.5, 36.5, 11], "texture": 3}, "south": {"uv": [34.5, 10.5, 36.5, 11], "texture": 3}, "west": {"uv": [34.5, 10.5, 36.5, 11], "texture": 3}, "up": {"uv": [34.5, 10.5, 36.5, 11], "texture": 3}, "down": {"uv": [34.5, 10.5, 36.5, 11], "texture": 3}}, "type": "cube", "uuid": "6acc1877-10fe-37b8-14ce-bcf3498aa683"}], "outliner": [{"name": "armorHead", "origin": [0, 26, 0], "color": 0, "uuid": "0d90a993-faa1-95e2-942f-6a6f8a499c63", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["246fe58a-3d7e-3eea-5ba9-6f5f8c823a5a", "30d696f3-4d93-ef5f-3aae-8babcd012279", {"name": "antenna2", "origin": [0, 2, 0], "color": 1, "uuid": "62df6cbb-0f34-ae66-2a6f-3cc2e3e76110", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["6acc1877-10fe-37b8-14ce-bcf3498aa683", "2507bffa-3f34-2550-ec88-e87508ae10c4", "c7a11f93-e299-ce88-37d5-b5dcd56d4670", "cb5e9c89-c305-db23-560c-f350ed1c0a08", "bf6883d1-92e6-50f1-b5d4-c7830421af26", "97d96f13-65d9-171a-3c2a-6d684c783cfc"]}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 2, "uuid": "5f86f544-8a2f-6068-c8b2-9ce2d21fa2cd", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["fdba0483-1909-d679-e0c7-975d29f0e1e3", "9826b7eb-d095-59e3-fa2f-99b5232d277f"]}, {"name": "armorRightArm", "origin": [6, 23, 0], "color": 3, "uuid": "a7ca861f-2a03-a3c9-255a-9e31587ea0bd", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["43a2c0e1-c73c-3702-a272-f19d52670656", "ac003f88-9de1-36e7-9b9b-c4663c47faf5", "1647eaa9-291b-12c3-9494-7a0d1f2c8aac", "76d61a18-30fb-fb66-d265-67b87d068014"]}, {"name": "armorLeftArm", "origin": [-5, 23, 0], "color": 5, "uuid": "8d0b1316-25a2-655e-f98c-b06576ffad35", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "armNested", "origin": [-6, 23, 0], "color": 7, "uuid": "08264fa1-6341-6fba-e783-8742cb9925cd", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["64001849-1bab-3bc2-4aed-6336686eabe3", "6d1f1db1-39bd-bf02-4ac4-d999492d6206", "2aec1a23-c172-8a94-8e83-5e0f9182bc47", "5e9d229f-6e37-c4b1-8b3c-5913df7126ab"]}]}, {"name": "armorRightLeg", "origin": [1.9, 12, 0], "color": 8, "uuid": "92bdf3e5-ac5d-3a7a-0f2a-adf1020ab520", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["6386488d-edbb-88d7-8673-d8e5954915a6", "4d27dc08-9141-d34c-150d-1238dd962ae2"]}, {"name": "armorLeftLeg", "origin": [-1.9, 12, 0], "color": 9, "uuid": "9ec36b05-5abf-d1d2-ff6f-a4190bed7811", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["86c7e477-717c-70c4-ea14-cfb08c1abb3b", "958ce6c0-dc69-c14e-643d-0754189c8e8e"]}, {"name": "armorRightBoot2", "origin": [0, 0, 0], "color": 0, "uuid": "eda6b07c-2efc-3cba-0321-dfdbcf672a80", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot2", "origin": [0, 0, 0], "color": 1, "uuid": "ec9e9c5c-94c4-6406-a141-dd998e37f9b9", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\splixson.png", "name": "splixson.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "d1a62e8f-cb52-ab20-3d70-78f13d056c52", "relative_path": "../textures/models/ben/splixson.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\recalibrated\\r_splixson.png", "name": "r_splixson.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "a427c052-b7d8-6b53-a7ef-c47ed815d7aa", "relative_path": "../textures/models/ben/recalibrated/r_splixson.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_splixson.png", "name": "ov_splixson.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "6964372d-9859-3fd8-8d88-dfa44d0579fc", "relative_path": "../textures/models/ben/ov_splixson.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAFP9JREFUeF7tnV+IX8UVx3eltZToi5GaikFEwkJigw/VBmQfKqL7YGlLzUuwhEpQAw2SxPoiEoL4YmuCpDRGFiFUQiGWttRCFLEPQbDaB4l2YQkiEtFUGl80SGOJZe/uhNzZ3/3Nn3Nm5t75fXxpN7+ZM2e+c+YzZ+be3/ympwr/t/6mzV+HuHBh7uOm+NqN61rVzi2cbf6+8sT1IeZWld13cK+ovqvy/t3PtIqc+eDUtKsOn69WYOOhW1pxs7DrPXSMCJTiogEAABARt1MAIEa11XUAgKUJGYBOYKW2AgB0FAYAAEAnkjJbAQA6gk88AD776HRLyWePP6ejbIeVR7Y+3Prk/FdfFh+DpB1OZBwA6AhbPPhKnwEAAJ1Aym0FAOgoDgDIAHQiKbMVAKAjOAAAADqRlNkKANARHAAAAJ1IymwFAOgIDgAAgE4kZbYCAHQEBwAAQCeSMlsBADqCAwAAoBNJma0AAB3BAQAA0ImkzFYAgI7gAAAA6ERSZisAQEdwAAAAdCIpsxUAoCM4AAAAOpGU2QoA0BEcAAAAnUjKbAUA6AgOAACATiRltgIAdAQHAABAJ5IyWwEAOoIDAACgE0mZrQAAHcEBAADQiaTMVgCAjuAAAADoRFJmKwBAR3AAAAB0IimzFQCgIzgAAAA6kZTZCgDQERwAAACdSMpsBQDoCA4AAIBOJGW2AgB0BAcAAEAnkjJbAQA6ggMAAKATSZmtAAAdwSceALaM/DKQTmCltgIAdBQGAJaOAEAnsFJbAQA6CgMAAKATSZmtAAAdwQEAANCJpMxWAICO4NOhP82l06zbiv2TXe4a40tcc8MGqYle1L8w93Hjx9qN61r+nFs42/x95Ynrg/w888Ewf5580gGg1X8AEDRdyhcGAMtjoDUByo9onAda/V8FAO2V1+6eayX2bf/EO39vmZ679YdeSmq179XYiEJa7V+1Y03L+hfz571cstsfagbg1VkKORUYFAAuTP3P2aEmDZ76Rmc5rQno5QgAiJWJepkUuAQA35VXyy97Ivq0bwCw/7Xvj3Rj313/XN4HjwGAqRjTvlbfl+z0pX0yAM1RHZ4tALAyZj4A0hxeAKCpJrZiFRgMAJb2/HfeOnupn3YWYFb/pQKvv3NyynUm0JcJaDpUCkBkALFTp456gwXAkvwGApdPfgDgF5gGgADAT69aSw0GAM8ef25q59YdrXHoAsDh4/NTj2x9eOyYkQEsvxcBAGqd2n79GiwAxm0BAIB78MkA3BpNQolp+4WC3J3+/MAVTZM+e+AUTwGu3nMxd5dHtvfhnvez+nHjgZuztmc3trDrveKvoV/uU6l5UFoHAAAAioCgdODbnZ5YAFy389qvi0TASqPmnXafFVDjRSDTV7MCmnfoS2qw1HaIDhJf7X6bdnNNyK6Jlqt9o11pP0q1b7c7PSQAXB74S4eCl//nOvSzJw0AWP7yEABoR0YuEPUOAE/d84xkYfGu+/gre0eWNYF4/+arR37+1F3veLexVPDx124dWf7FU583/9618vdFB5+MKESQLuD9+/B/erUXD+kTZeUKXMoA+hL4AGD5a74AQB7cWHArMD3/56NFzgBmNsw03tlv7HWt3FoZgG3HfKtw8fSiW60EJUK3LtounP/qSzIAbVEHZA8ArHytGAAMKGpxVU2BbAAwK36X5yYTcGUA9j0Atj0tO7mB4JsJ+PbfFSHmEHXHT7aTAbjEqvhzANAxuACg4qina5cU6A0AjEdd3+JzrXxdmYD97752cgPA+NmVCfj67dLRfnxKBjDZNAAAPckAAMBkT8RSvVcHgL3XNyup6wyga+UKXflcmYCvvS6/U2cGdgbg66/rbMV8njoDOPmvN1tPlWY3bRn0GUNt/bHjBAA4MoAuoKUiNgBIpWycXQAQp9uqWq4MQGvv78oAzOeulTX1Sm/7qbX39+0/TwGUAnvgZtQzgC49AMD4SAEAA59JA3VfDQCuVLkLANvufbAlnX0vgGulduluZxb2TUDHXn5+pAk7A3D1z+VH1+f7d7e/g5G7/1o3AtWeKtfaPwAAAFQO6WqdIAbctfZPDQC+K6C9ktoZgLHTtTL/fv7o2KZ+vmP7yM9928m997czAOO8/TPlri2U3Wm7H13taGUAvuNPuX4pAACsDAAA9CtA8SatAtkBYHfHXpnMyhe64rlkMhO7qz1X/VSf5/bHbo8MINXIDsMuADg4+oKSXMMHAHIpTTujFBAfAEkvU5RefFH6++ylr1QrHdalbxRaf9Nm0X0WXWdNvroO/U1HAOA70h3lAEDZK8UAgCyAAYBMvykAAACEIVS0OgAQyg8AAIAwhIpWBwBC+QFAXQAIfQw89PsUAAAAEClQ2yEgAAgMB54ClP1lpcDhUi8OAIZ9pyIZgHBKsAVgCyAMoaLVAYBQfgBQFwBCw4H3AA7dInoRgxeBQkOuX+Vr2wKEqgsAAIAIgKEB17fyAGDYdx6yBRDOKLYAbAGEIVS0OgAQyg8A6gIAjwEDJwSPAXkMGBgyqsW1vwsAAAKHBwAAgMCQUS0OAGRysgWQ6ceXgQ6zBRCGUNHq09I97NqN67w6cG7hrFc57UIu/6SPIa/asUbb5SB7X8yfDypvFy7t/5Unrhf5L61s38Icas++ZTq0fukbmQDAnvdDx6xVvvQEAgCi4ZsCADtle1jXCmsPT+pMINQfaQZg+tcFglB/fPWSTnzfTCC1/9IV1O6HdELLcOKubfd3YjIAIw0AcAfJ5SW69AIAo3UEAGHxlW0L0OWWFAjSFUorA7AzAalfXZmA9sTvygRy+a+VAfR94ts6m35PXAbgm+L6ckwaqACgrbTZykh19QUYADglfhLnO1dGlSMDEB4Cdol/44GbJeOyqq42qFzO5fJf6xA1dWbk0iv0c9Pv0t+lAAAAYGTsAoDQKR1WHgCs6FXbGYAdBtKJlHvlz+2/Vgbgmn65MwTffpEBCF8Qku5VU08wADD+PQvfieKa4K7PAcBohdgCJNoCGLkBQFkA2AtEbuC7MlwyADKAsYtX6oB1rZypAZY6AwAA40eYDIAMYGyElALA/E//0vj1yafL3yF54o2HXKxqfe7aGmqD1aVTVyZABkAGQAYwQgEAEMS76MLiDMC07CJul4euPZKrZ9J2Ux8OuVYGV/+0VypXe6WfAtgT3/jzxsl/NP/3xBUveHXBNy5yxV+1GQAAGB+PACDsEBAAePFNrRAZgPD79F0jIZ34tt3cmUAu/+1DwLmLD4wNbjIAtbnfGAIAAGBkRAEAv4km3WoM/hCQLUA7ULQnTu5MILf/XY8B7UxAe+U3uuY6A+hqDwAUegpgBl77EDD3BPJbp/xL5fYfAJS9U5EtgNIWIPXESZ0JlPJf+0Ug35ScDGBZAQAAAJpAAAD+WdLlJaXAqWYLEHsWUHoPJt0C5J442plAaf+l42/rIZ2QoRiQtgcAMp8B2AEHAHQvLgmdQACgkjOAoWYAoQQPDXDKj1cAAAAA0RwJncB2wIXWFzlL5VUKAAAAIJoWoRMYAIjkVq+sBYDQOCj1FMBulzOAwmcAsYGjPhMm1CAAKJwBSH9d1Y7bvt/Pbl9DffWeixM69frRbQAAALJGIgDIKrezMQAAAJxBollA64counxKnQHV5v+FuY9HShl6IUjsVk4KIGm7xc8AJn0LoAmXJVsAYLyiNsAAABmA9hwcay/1Cvq7I4eb9s1ddrt/cb+of3/422ste08+sV9kz1U5N8BsAMReCCJdiV26dH0ubZcMIFb5yHoAYLxwACAssACApVfqAAobntWlUwHArPzvfuvRVqOLx2abv1964VCQ62bl77L3+qsnguz5Fk49fq4tQOyNQNKJ6KuPXU7aLhlArPKR9QBAvzMAABAZ2JHVpjkEjFTOqnbn3XPNv8xsO+mVAdh7e/us4L4Hdo21V2sGYMQLvRFIuhLHRoG0XTKAWOUj66XKAACA34C4tgAAwE9HrVJkAFpKrtgxIDBm7b2/a29vlzeZgLGXauU39kufAcQOh3QlLtUuGUCs8pH1UmUAxh0AMH5gfDOA0OEFAKGKLZcnA4jTrbOWawV17e1dTwtSA8zlv1Su3ADYs/5XY11+/JW9oi5JwUMGIJI/vHLpCQQANrQGretNwNCR7ZqIAGC8kmQAoZHmKO+7gtp7e9fKb5otDTCpXKkygJd++fJI1xZPL5IBjFEAAEgj2qoPAMqcAQCAuEAWA2DfweU91MyGmeZ/337zreZ/b9tye8sj+98NmR/Z+nCc55G1altBI2WIPsOQtmfrb+LH2DVxZP62V/DQz21/XRlBaP9sf0Lrz27aMh1aR7M8ANBUk28DOtUEAG2JAEDhDIAbgZxzNmkB+5AudIV3ZQhkAOOHb+IzAACQdH47jQOAwlsA5whRAAVQoFoFih5AVKsqHUOBgSgAAAYyULiJAikUAAApVMUmCgxEAQAwkIHCTRRIoQAASKEqNlFgIAoAgIEMFG6iQAoFAEAKVbGJAgNRAAAMZKBwEwVSKAAAUqiKTRQYiAIAYCADhZsokEIBAJBCVWyiwEAUAAADGSjcRIEUCgCAFKpiEwUGogAAGMhA4SYKpFAAAKRQFZsoMBAFAMBABgo3USCFAgAgharYRIGBKAAABjJQuIkCKRQAAClUxSYKDEQBADCQgcJNFEihAABIoSo2UWAgCgCAgQwUbqJACgUAQApVsYkCA1EAAAxkoHATBVIoAABSqIpNFBiIAgBgIAOFmyiQQgEAkEJVbKLAQBQAAAMZqElz86+vvvH1Up8/+fRs0/UH7/8ZsZogCBA1gaiYlCsAAOQa+lgAAD4qUSabAmbiv/utR1ttLh6bbf4+euRpYlZxNBBTUUxMyRUAAHINQywAgBC1KJtcge0PPdbs/We2nSQDSK721BQAyCAyTfgrAAD8tdIoCQA0VMSGugIGBMYwe391iRuDACCNrlgVKgAAhAJ6VgcAnkJRDAVqVAAA1Diq9AkFPBUAAJ5CUQwFalQAANQ4qvQJBTwVAACeQlEMBWpUAADUOKr0CQU8FQAAnkJRDAVqVAAA1Diq9AkFPBUAAJ5CUQwFalQAANQ4qvQJBTwVAACeQlEsrwJcCJJHbwCQR2daCVQAAAQKFlkcAEQKR7U0CnAhSBpdu6wCgLx605pDAQCQN0QAQF69ac2hABeC5A0RAJBXb1oDAL2KAQDQq+HAGaMAF4LkiQUAkEdnWglUAAAEChZZHABECkc1FKhBAQBQwyjSBxSIVAAARApHNRSoQQEAUMMo0gcUiFQAAEQKRzUUqEEBAFDDKNIHFIhUAABECkc1FKhBAQBQwyjSBxSIVAAARApHNRSoQQEAUMMo0gcUiFQAAEQKZ6ptPHRL83v2sf99uOf92KpNvfNffVl0DNfftFnU/2MvPy/q/+ymLUX7L3K+B5URTzgIAAAACEOoaHUAIJQfAAAAYQgVrQ4AhPIDAAAgDKGi1QGAUH4AIAPAvoN7g0ZgZsNMqzxnAEHyrSoMAGT6TQEAACAMoaLVAYBQfgAAAIQhVLQ6ABDKDwAAgDCEilYHAEL5AYAMALwHIAxAYXUAIBQQAAAAYQgVrQ4AhPIDAAAgDKGi1QGAUH4AAACEIVS0OgAQyg8AZADgPQBhAAqrAwChgAAAAAhDqGh1ACCUHwAAAGEIFa0OAITyAwAAIAyhotUnHgDX7bxW9H32rtFbu3Gd18CeWzjrVS62HZf9L+bPi9q/5oYNovqlK5/54NREz4GJ7vxS8AEAAFAaQiXbBwDKGYDvym8G3bVCu4JD2p40A3D5Z3+eOmP47KPTQS6VvlEpyNkEhQEAAEgQVt0mAUBWuZ2NAQAhAEJXYHtEcmcAdvvSOwmdEdZRQBsEoSu/cYsMIHYEK6knPQMAAHGBAADidNOuRQZABqAdU172rtqxxqucb6HYswwyAF+FKy1HBiC7ljw2LABArHK69cgAyAB0I2rFmvYElzrZlSGQAUiVHXh9MoA0GQAAGMbEIAMgA1CN1BsP3NyyJ33KIXXOPqS1n3qQAUgVHnh9MgDdDAAADGtCkAGQAahErD3xpe87PHnHkcbEd7+z/J2KHX/6cZCfrsezJhPoawbw/It/bL6jYvr/o7vvSDJXkxgNGqnChckAdDIAAKAbyABAV89Oa6UAoL03dq14XQJI/fBt17eduYsPNK7eMfuDlsuhmYCvXwu73uvVIrj9ocealX9m28lW/7/33980f2tnAr3qfKY532oGAKT9OrIRGwD4RTcA8NNJrRQA6CcAugb4xBUveI19bRnA4rHZpt9HjzytumirGvMamZ4VAgAAoE8h2ZUBAIBEowQA8gAgditg6mmv/MZu384AjF8GBOZv7ZXf2CUDKPQY0HdP7Ms935TXtif1I7Rd3/bMYSAAWFYAAPjOhMByZAD9zAACh/FS8VAg9TUDiO1/aD0yADKA0JhplQ+dcL4ZQKxTof4AgFilK6mXOwPo2wSQ+hM64aTtucIu1B8A4FK08s8BAFuAykN8bPfYAmTeAvRtBZT6E7riSttzTdZQf8gAXIpW/jkZABlA5SFOBjBOAQCQFwBmLLQzgdCV3/hBBjDJ+FP4ZaDQwNMOfHv4cvsT2h4A6NeEm/gzgDXf/HaS3wbsGmbt67DtdkLvx5de3QUA+jWhQ70BAAAgNGZE7wGQAYjkVq8MAACAKKhSZQChNwLF+tHXMwAuBBGFpX9ltgCyH+iInXiusxAAwJVg/rNYUBIA9AsAsTcCxYKobxkAF4IIJnNMVQAAAGLiJlUdAJBK2Q67AKCfAOgKg657AWrPALgQJBEYAAAASBRaUWa5EShKtvhKAKBfADAjGXohSC0ZgOk/NwLFz+mgmgAAAAQFTKbCACCT0ACgnwAIHf7aMoDQ/seW50UgXgSKjZ2mXuzEc70HEOpUrB99ewwY2m9peQAAAEQx9NQ9z4ytf+DMr0d+DgBEsqtVBgAAQBRMAEAkX/HKAAAAiILQBYCZDTMj7d/323tF7dqV2QLEyQkAAEBc5KzUAgAi+YpXBgBCADx7/LlmEM1K9/abbzV/37bl9tbg2v++eHqx+Xz/7vF76NAICb0PwPgf2k5X+a4V35Q3/TZ/2+Wln4f2Y3bTlomeAxPd+aVgkT4GBADtKQcAQhFUtjwAIANQjUAAoCpncmMAAACoBhkAUJUzubH/AwgVn7LwNbdTAAAAAElFTkSuQmCC"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": true, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}