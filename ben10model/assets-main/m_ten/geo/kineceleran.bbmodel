{"meta": {"format_version": "4.10", "model_format": "animated_entity_model", "box_uv": true}, "name": "kineceleran", "model_identifier": "", "visible_box": [4, 4.5, 1.75], "variable_placeholders": " ", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 64, "height": 64}, "elements": [{"name": "Head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 26.2, -4], "to": [2, 31.2, 4], "autouv": 0, "color": 0, "inflate": 0.51, "origin": [0, 1, 0], "faces": {"north": {"uv": [8, 8, 16, 16], "texture": 13}, "east": {"uv": [0, 8, 8, 16], "texture": 13}, "south": {"uv": [24, 8, 32, 16], "texture": 13}, "west": {"uv": [16, 8, 24, 16], "texture": 13}, "up": {"uv": [16, 8, 8, 0], "texture": 13}, "down": {"uv": [24, 0, 16, 8], "texture": 13}}, "type": "cube", "uuid": "0ff37dbe-715a-ff46-a6b8-d6f08b227341"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 26, -4], "to": [3, 33, 4], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 1, "origin": [0, 2, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 13}, "east": {"uv": [32, 8, 40, 16], "texture": 13}, "south": {"uv": [56, 8, 64, 16], "texture": 13}, "west": {"uv": [48, 8, 56, 16], "texture": 13}, "up": {"uv": [48, 8, 40, 0], "texture": 13}, "down": {"uv": [56, 0, 48, 8], "texture": 13}}, "type": "cube", "uuid": "b22fcc6e-4535-efd8-a448-a53d0dda5b74"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 19, -2], "to": [3, 25, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [21, 20, 27, 26], "texture": 13}, "east": {"uv": [17, 20, 21, 26], "texture": 13}, "south": {"uv": [33, 20, 39, 26], "texture": 13}, "west": {"uv": [29, 20, 33, 26], "texture": 13}, "up": {"uv": [26, 20, 20, 16], "texture": 13}, "down": {"uv": [34, 16, 28, 20], "texture": 13}}, "type": "cube", "uuid": "3dd5702d-f04c-77c4-6502-d9edfdcea3a9"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 19, -2], "to": [3, 25, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [21, 36, 27, 42], "texture": 13}, "east": {"uv": [17, 36, 21, 42], "texture": 13}, "south": {"uv": [33, 36, 39, 42], "texture": 13}, "west": {"uv": [29, 36, 33, 42], "texture": 13}, "up": {"uv": [26, 36, 20, 32], "texture": 13}, "down": {"uv": [34, 32, 28, 36], "texture": 13}}, "type": "cube", "uuid": "5a4ba021-cf61-d8a1-ec37-9958cffad565"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 12, -1.5], "to": [6, 24, 1.5], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 13}, "east": {"uv": [40, 36, 44, 48], "texture": 13}, "south": {"uv": [52, 36, 56, 48], "texture": 13}, "west": {"uv": [48, 36, 52, 48], "texture": 13}, "up": {"uv": [48, 36, 44, 32], "texture": 13}, "down": {"uv": [52, 32, 48, 36], "texture": 13}}, "type": "cube", "uuid": "e79d16be-caa1-b00d-20ff-1e7ef52d330f"}, {"name": "Right Leg", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [0.3, 0, -2], "to": [2.3, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [1.0999999046325684, 0, 0], "uv_offset": [0, 16], "faces": {"north": {"uv": [4, 20, 8, 32], "texture": 13}, "east": {"uv": [0, 20, 4, 32], "texture": 13}, "south": {"uv": [12, 20, 16, 32], "texture": 13}, "west": {"uv": [8, 20, 12, 32], "texture": 13}, "up": {"uv": [8, 20, 4, 16], "texture": 13}, "down": {"uv": [12, 16, 8, 20], "texture": 13}}, "type": "cube", "uuid": "c8ef8996-e7df-a4dd-ce7d-610836b644f0"}, {"name": "Right Leg <PERSON>er", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [0.3, 0, -2], "to": [2.3, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [1.0999999046325684, 0, 0], "uv_offset": [0, 32], "faces": {"north": {"uv": [4, 36, 8, 48], "texture": 13}, "east": {"uv": [0, 36, 4, 48], "texture": 13}, "south": {"uv": [12, 36, 16, 48], "texture": 13}, "west": {"uv": [8, 36, 12, 48], "texture": 13}, "up": {"uv": [8, 36, 4, 32], "texture": 13}, "down": {"uv": [12, 32, 8, 36], "texture": 13}}, "type": "cube", "uuid": "fd521ba9-55a4-ad60-2ae8-a8edf602d806"}, {"name": "Left Leg", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.3, 0, -2], "to": [-0.2999999999999998, 12, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-1.0999999046325684, 0, 0], "uv_offset": [16, 48], "faces": {"north": {"uv": [20, 52, 24, 64], "texture": 13}, "east": {"uv": [16, 52, 20, 64], "texture": 13}, "south": {"uv": [28, 52, 32, 64], "texture": 13}, "west": {"uv": [24, 52, 28, 64], "texture": 13}, "up": {"uv": [24, 52, 20, 48], "texture": 13}, "down": {"uv": [28, 48, 24, 52], "texture": 13}}, "type": "cube", "uuid": "5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab"}, {"name": "Left Leg Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.3, 0, -2], "to": [-0.2999999999999998, 12, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-1.0999999046325684, 0, 0], "uv_offset": [0, 48], "faces": {"north": {"uv": [4, 52, 8, 64], "texture": 13}, "east": {"uv": [0, 52, 4, 64], "texture": 13}, "south": {"uv": [12, 52, 16, 64], "texture": 13}, "west": {"uv": [8, 52, 12, 64], "texture": 13}, "up": {"uv": [8, 52, 4, 48], "texture": 13}, "down": {"uv": [12, 48, 8, 52], "texture": 13}}, "type": "cube", "uuid": "137788b1-be61-0eeb-2680-8e1ff17980eb"}, {"name": "<PERSON> Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2, 26.2, -4], "to": [2, 31.2, 6], "autouv": 0, "color": 0, "inflate": 1, "origin": [0, 1, 0], "uv_offset": [32, 0], "faces": {"north": {"uv": [40, 8, 48, 16], "texture": 13}, "east": {"uv": [32, 8, 40, 16], "texture": 13}, "south": {"uv": [56, 8, 64, 16], "texture": 13}, "west": {"uv": [48, 8, 56, 16], "texture": 13}, "up": {"uv": [48, 8, 40, 0], "texture": 13}, "down": {"uv": [56, 0, 48, 8], "texture": 13}}, "type": "cube", "uuid": "ab7da99d-91d4-7dc6-a369-98ac7635e3f5"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 17, -3], "to": [1.5, 21, -0.7000000000000002], "autouv": 0, "color": 0, "origin": [0, 19, -2], "uv_offset": [15, 0], "faces": {"north": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "east": {"uv": [24.5, 5.749999999999993, 27, 3.999999999999993], "texture": 13}, "south": {"uv": [29.25, 1.249999999999993, 31.25, 0.2499999999999929], "texture": 13}, "west": {"uv": [27, 5.749999999999993, 24.5, 3.999999999999993], "texture": 13}, "up": {"uv": [28.75, 2.25, 32.25, 0], "texture": 13}, "down": {"uv": [28.75, 2.25, 32.25, 0], "texture": 13}}, "type": "cube", "uuid": "65bb1603-018e-18f4-abac-8981fea57b05"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 17.2, -1], "to": [1.5, 21.2, 2.3], "autouv": 0, "color": 0, "rotation": [7.5, 0, 0], "origin": [0, 19, -2], "uv_offset": [14, -1], "faces": {"north": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "east": {"uv": [24.5, 5.749999999999993, 27, 3.999999999999993], "texture": 13}, "south": {"uv": [29.25, 1.249999999999993, 31.25, 0.2499999999999929], "texture": 13}, "west": {"uv": [27, 5.749999999999993, 24.5, 3.999999999999993], "texture": 13}, "up": {"uv": [28.75, 2.25, 32.25, 0], "texture": 13}, "down": {"uv": [28.75, 0, 32.25, 2.25], "texture": 13}}, "type": "cube", "uuid": "4aaabe6e-98df-779b-910c-45f9526c458e"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 18.2, 2], "to": [1.5, 22.199999999999996, 5], "autouv": 0, "color": 0, "rotation": [20, 0, 0], "origin": [0, 20, -2], "uv_offset": [14, -1], "faces": {"north": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "east": {"uv": [24.5, 5.749999999999993, 27, 3.999999999999993], "texture": 13}, "south": {"uv": [29.25, 1.249999999999993, 31.25, 0.2499999999999929], "texture": 13}, "west": {"uv": [27, 5.749999999999993, 24.5, 3.999999999999993], "texture": 13}, "up": {"uv": [28.75, 2.25, 32.25, 0], "texture": 13}, "down": {"uv": [28.75, 0, 32.25, 2.25], "texture": 13}}, "type": "cube", "uuid": "5fdcc0b5-240c-1814-c03b-f20ab8cd222f"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 19, 5.5], "to": [1.5, 22.999999999999996, 8], "autouv": 0, "color": 0, "rotation": [20, 0, 0], "origin": [0, 23, -4], "uv_offset": [15, 0], "faces": {"north": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "east": {"uv": [24.5, 5.749999999999993, 27, 3.999999999999993], "texture": 13}, "south": {"uv": [29.25, 1.249999999999993, 31.25, 0.2499999999999929], "texture": 13}, "west": {"uv": [27, 5.749999999999993, 24.5, 3.999999999999993], "texture": 13}, "up": {"uv": [28.75, 2.25, 32.25, 0], "texture": 13}, "down": {"uv": [28.75, 0, 32.25, 2.25], "texture": 13}}, "type": "cube", "uuid": "697ec490-a97c-7d36-2f43-ec56ca576705"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 19, 8], "to": [1.5, 22.999999999999996, 11.300000000000011], "autouv": 0, "color": 0, "rotation": [20, 0, 0], "origin": [-1, 23, -4], "uv_offset": [15, 0], "faces": {"north": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "east": {"uv": [24.5, 5.749999999999993, 27, 3.999999999999993], "texture": 13}, "south": {"uv": [29.25, 1.249999999999993, 31.25, 0.2499999999999929], "texture": 13}, "west": {"uv": [27, 5.749999999999993, 24.5, 3.999999999999993], "texture": 13}, "up": {"uv": [28.75, 2.25, 32.25, 0], "texture": 13}, "down": {"uv": [28.75, 0, 32.25, 2.25], "texture": 13}}, "type": "cube", "uuid": "f7305be1-4982-fcaa-c417-bb022c05470a"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 13.5, 8.5], "to": [1.5, 16.499999999999996, 11.5], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [-25, 0, 0], "origin": [0, 17, -6], "uv_offset": [15, 0], "faces": {"north": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "east": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "south": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "west": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "up": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "down": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}}, "type": "cube", "uuid": "95338aa4-5fe8-f0ce-70a7-5dff3053b0d9"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 13.5, 11.5], "to": [0.5, 16.499999999999996, 12.5], "autouv": 0, "color": 0, "visibility": false, "export": false, "rotation": [-25, 0, 0], "origin": [0, 17, -6], "uv_offset": [15, 0], "faces": {"north": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "east": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "south": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "west": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "up": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "down": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}}, "type": "cube", "uuid": "f049923d-0db6-39de-8242-cd4a113a073c"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 20, 10.5], "to": [1, 22.999999999999996, 14.5], "autouv": 0, "color": 0, "rotation": [22.5, 0, 0], "origin": [-1, 24, -4], "uv_offset": [15, 0], "faces": {"north": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "east": {"uv": [27, 5.749999999999993, 24.5, 3.999999999999993], "texture": 13}, "south": {"uv": [29.25, 1.249999999999993, 31.25, 0.2499999999999929], "texture": 13}, "west": {"uv": [24.5, 5.749999999999993, 27, 3.999999999999993], "texture": 13}, "up": {"uv": [28.75, 0, 32.25, 2.25], "texture": 13}, "down": {"uv": [28.75, 2.25, 32.25, 0], "texture": 13}}, "type": "cube", "uuid": "9bb6f37c-f129-45a4-a7cd-a41546020946"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-0.5, 20.5, 14.5], "to": [0.5, 22.499999999999996, 17.5], "autouv": 0, "color": 0, "rotation": [22.5, 0, 0], "origin": [0, 24, -4], "uv_offset": [15, 0], "faces": {"north": {"uv": [19, 3.999999999999993, 21, 2.999999999999993], "texture": 13}, "east": {"uv": [24.5, 5.749999999999993, 27, 3.999999999999993], "texture": 13}, "south": {"uv": [29.25, 1.249999999999993, 31.25, 0.2499999999999929], "texture": 13}, "west": {"uv": [27, 5.749999999999993, 24.5, 3.999999999999993], "texture": 13}, "up": {"uv": [28.75, 2.25, 32.25, 0], "texture": 13}, "down": {"uv": [28.75, 0, 32.25, 2.25], "texture": 13}}, "type": "cube", "uuid": "453d8b04-16e9-d941-6a0e-2448124f0619"}, {"name": "Left Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 12, -1.5], "to": [-4, 24, 1.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [32, 48], "faces": {"north": {"uv": [36, 52, 40, 64], "texture": 13}, "east": {"uv": [32, 52, 36, 64], "texture": 13}, "south": {"uv": [44, 52, 48, 64], "texture": 13}, "west": {"uv": [40, 52, 44, 64], "texture": 13}, "up": {"uv": [40, 52, 36, 48], "texture": 13}, "down": {"uv": [44, 48, 40, 52], "texture": 13}}, "type": "cube", "uuid": "1c9f344a-321a-164b-d595-d0d9a3594ef2"}, {"name": "Left Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 12, -1.5], "to": [-4, 24, 1.5], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [48, 48], "faces": {"north": {"uv": [52, 52, 56, 64], "texture": 13}, "east": {"uv": [48, 52, 52, 64], "texture": 13}, "south": {"uv": [60, 52, 64, 64], "texture": 13}, "west": {"uv": [56, 52, 60, 64], "texture": 13}, "up": {"uv": [56, 52, 52, 48], "texture": 13}, "down": {"uv": [60, 48, 56, 52], "texture": 13}}, "type": "cube", "uuid": "702db8ca-f5da-2614-8d00-3ed719ea2134"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 12, -1.5], "to": [6, 24, 1.5], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [0, 0, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 32], "texture": 13}, "east": {"uv": [40, 20, 44, 32], "texture": 13}, "south": {"uv": [52, 20, 56, 32], "texture": 13}, "west": {"uv": [48, 20, 52, 32], "texture": 13}, "up": {"uv": [48, 20, 44, 16], "texture": 13}, "down": {"uv": [52, 16, 48, 20], "texture": 13}}, "type": "cube", "uuid": "59beb952-80e8-8f60-8c9c-776d34f25845"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 18, -1.5], "to": [6, 24, 0.5], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0, 6, 0], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 20, 48, 25], "texture": 13}, "east": {"uv": [40, 20, 44, 25], "texture": 13}, "south": {"uv": [52, 20, 56, 25], "texture": 13}, "west": {"uv": [48, 20, 52, 25], "texture": 13}, "up": {"uv": [48, 20, 44, 16], "texture": 13}, "down": {"uv": [52, 16, 48, 20], "texture": 13}}, "type": "cube", "uuid": "c78681eb-4f26-e8ce-6215-21c495d94574"}, {"name": "Right Arm Layer", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 12, -1.5], "to": [6, 24, 0.5], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.75, "origin": [0, 0, 0], "uv_offset": [40, 32], "faces": {"north": {"uv": [44, 36, 48, 48], "texture": 13}, "east": {"uv": [40, 36, 44, 48], "texture": 13}, "south": {"uv": [52, 36, 56, 48], "texture": 13}, "west": {"uv": [48, 36, 52, 48], "texture": 13}, "up": {"uv": [48, 36, 44, 32], "texture": 13}, "down": {"uv": [52, 32, 48, 36], "texture": 13}}, "type": "cube", "uuid": "7126c21f-ec1b-3baa-2570-7af6489777a7"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 4.9, 4.5], "to": [6, 13.100000000000001, 6.5], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0, -5, 5], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 25, 48, 32], "texture": 13}, "east": {"uv": [40, 25, 44, 32], "texture": 13}, "south": {"uv": [52, 25, 56, 32], "texture": 13}, "west": {"uv": [48, 25, 52, 32], "texture": 13}, "up": {"uv": [52, 29, 48, 25], "texture": 13}, "down": {"uv": [52, 20, 48, 16], "texture": 13}}, "type": "cube", "uuid": "3253cba9-0127-e6f5-b15c-426c830ce762"}, {"name": "Right Arm", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [4.5, 14.1, 5], "to": [5.5, 20.1, 6], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.5, "origin": [0, -4, 6], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 25, 48, 28], "texture": 13}, "east": {"uv": [40, 25, 44, 28], "texture": 13}, "south": {"uv": [52, 25, 56, 28], "texture": 13}, "west": {"uv": [48, 25, 52, 28], "texture": 13}, "up": {"uv": [52, 29, 48, 25], "texture": 13}, "down": {"uv": [52, 20, 48, 16], "texture": 13}}, "type": "cube", "uuid": "b0d5d065-359b-0aae-2c85-19ae29a953bc"}, {"name": "Thing", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [4.5, 19.1, -9], "to": [5.5, 26.1, -8], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [35, 0, 0], "origin": [0, 3, -8], "uv_offset": [40, 16], "faces": {"north": {"uv": [44, 25, 48, 28], "texture": 13}, "east": {"uv": [40, 25, 44, 28], "texture": 13}, "south": {"uv": [52, 25, 56, 28], "texture": 13}, "west": {"uv": [48, 25, 52, 28], "texture": 13}, "up": {"uv": [52, 29, 48, 25], "texture": 13}, "down": {"uv": [52, 20, 48, 16], "texture": 13}}, "type": "cube", "uuid": "786cb740-da14-1323-2fdf-4a2051fc7106"}, {"name": "Thing", "box_uv": false, "rescale": false, "locked": true, "render_order": "default", "allow_mirror_modeling": true, "from": [-5.5, 19.1, -9], "to": [-4.5, 26.1, -8], "autouv": 0, "color": 0, "inflate": 0.5, "rotation": [35, 0, 0], "origin": [0, 3, -8], "uv_offset": [40, 16], "faces": {"north": {"uv": [48, 25, 44, 28], "texture": 13}, "east": {"uv": [52, 25, 48, 28], "texture": 13}, "south": {"uv": [56, 25, 52, 28], "texture": 13}, "west": {"uv": [44, 25, 40, 28], "texture": 13}, "up": {"uv": [48, 29, 52, 25], "texture": 13}, "down": {"uv": [48, 20, 52, 16], "texture": 13}}, "type": "cube", "uuid": "acd0cfc8-b34b-6709-5b13-21f3a1b2ba1a"}, {"name": "Body Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.5, 13, -2], "to": [2.5, 18, 2], "autouv": 0, "color": 0, "inflate": 0.75, "origin": [-0.5, 1, 0], "uv_offset": [16, 32], "faces": {"north": {"uv": [21, 41.5, 27, 46.5], "texture": 13}, "east": {"uv": [17, 43, 21, 48], "texture": 13}, "south": {"uv": [33, 43, 39, 48], "texture": 13}, "west": {"uv": [29, 43, 33, 48], "texture": 13}, "up": {"uv": [26, 36, 20, 32], "texture": 13}, "down": {"uv": [34, 32, 28, 36], "texture": 13}}, "type": "cube", "uuid": "d619c6b4-e4a5-67de-7ff5-3a3332224bf8"}, {"name": "Body", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.5, 13, -2], "to": [2.5, 18, 2], "autouv": 0, "color": 0, "inflate": 0.5, "origin": [-0.5, 1, 0], "uv_offset": [16, 16], "faces": {"north": {"uv": [21, 27, 27, 32], "texture": 13}, "east": {"uv": [17, 27, 21, 32], "texture": 13}, "south": {"uv": [33, 27, 39, 32], "texture": 13}, "west": {"uv": [29, 27, 33, 32], "texture": 13}, "up": {"uv": [26, 20, 20, 16], "texture": 13}, "down": {"uv": [34, 16, 28, 20], "texture": 13}}, "type": "cube", "uuid": "1cf316b0-1442-40c1-9cb5-f2d3c5ebd6fa"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 29, -1], "to": [1, 33, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [0, 33.75, 0], "uv_offset": [23, 9], "faces": {"north": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "east": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "south": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "west": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "up": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "down": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}}, "type": "cube", "uuid": "79bd0007-50bf-ef7b-4f10-d06a614509cb"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 33, -1], "to": [6, 34, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [0, 33.75, 0], "uv_offset": [19, 9], "faces": {"north": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "east": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "south": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "west": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "up": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "down": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}}, "type": "cube", "uuid": "24596191-f673-8417-20d0-2ba6decbe9bf"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [5, 34, -1], "to": [6, 35, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [0, 33.75, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "east": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "south": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "west": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "up": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "down": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}}, "type": "cube", "uuid": "ffd55af5-923e-5249-33d4-e3ba05b3ad12"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [4, 34, -1], "to": [5, 36, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [0, 33.75, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "east": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "south": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "west": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "up": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "down": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}}, "type": "cube", "uuid": "8050a963-315e-5e7b-fd2a-eef1d474ab67"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 34, -1], "to": [-4, 36, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [0, 33.75, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "east": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "south": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "west": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "up": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "down": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}}, "type": "cube", "uuid": "09c0ac29-9767-adf3-e6ff-c70605c9adea"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-6, 34, -1], "to": [-5, 35, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [0, 33.75, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "east": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "south": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "west": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "up": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "down": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}}, "type": "cube", "uuid": "1042fa47-662b-fcea-a1f6-55ffb2b4c703"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 34, -1], "to": [1.5, 36, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [0, 33.75, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "east": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "south": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "west": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "up": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "down": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}}, "type": "cube", "uuid": "cd0801c5-d886-06c5-2a53-1aa4fa73429c"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1.5, 34, -1], "to": [1.5, 36, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "mirror_uv": true, "origin": [0, 33.75, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "east": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "south": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "west": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "up": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "down": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}}, "type": "cube", "uuid": "e52057c6-ac3f-31fb-4a70-eaae19fea906"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-5, 36, -1], "to": [-1, 37, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [0, 33.75, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "east": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "south": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "west": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "up": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "down": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}}, "type": "cube", "uuid": "e312c6a8-3a17-9d46-bf29-37331b4038a2"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [1, 36, -1], "to": [5, 37, 1], "autouv": 0, "color": 0, "visibility": false, "export": false, "origin": [0, 33.75, 0], "uv_offset": [21, 9], "faces": {"north": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "east": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "south": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "west": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "up": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}, "down": {"uv": [24.75, 11.75, 26.75, 13.25], "texture": 13}}, "type": "cube", "uuid": "bd8f3bdb-05ce-96de-ee2c-8cc0b020aa38"}, {"name": "<PERSON>s Layer", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 30, -4.3], "to": [3.5, 35, 2.7], "autouv": 0, "color": 0, "visibility": false, "export": false, "inflate": 0.7, "origin": [0.5, 5, 2], "uv_offset": [32, 0], "faces": {"north": {"uv": [0, 0, 8, 5.875], "texture": 13}, "east": {"uv": [32, 8, 40, 16], "texture": null}, "south": {"uv": [56, 8, 64, 16], "texture": null}, "west": {"uv": [48, 8, 56, 16], "texture": null}, "up": {"uv": [48, 8, 40, 0], "texture": null}, "down": {"uv": [56, 0, 48, 8], "texture": null}}, "type": "cube", "uuid": "5ac16f91-b28a-1492-8049-f2d56f8b6952"}], "outliner": [{"name": "armorHead", "origin": [0, 26, 0], "color": 0, "uuid": "21d3e5ff-5eb8-92d2-365f-a769c3597beb", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["0ff37dbe-715a-ff46-a6b8-d6f08b227341", {"name": "horns_overlay", "origin": [0.5, 6, 2], "color": 0, "uuid": "f0986be7-47f7-95b9-ab50-b9f78a231064", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["5ac16f91-b28a-1492-8049-f2d56f8b6952"]}, "b22fcc6e-4535-efd8-a448-a53d0dda5b74", "ab7da99d-91d4-7dc6-a369-98ac7635e3f5", {"name": "chronoantenna_ov", "origin": [0, -3, 0], "color": 0, "uuid": "bd66edab-e9f3-885a-1faa-ec914c6c8233", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["79bd0007-50bf-ef7b-4f10-d06a614509cb", "24596191-f673-8417-20d0-2ba6decbe9bf", "ffd55af5-923e-5249-33d4-e3ba05b3ad12", "8050a963-315e-5e7b-fd2a-eef1d474ab67", "09c0ac29-9767-adf3-e6ff-c70605c9adea", "1042fa47-662b-fcea-a1f6-55ffb2b4c703", "cd0801c5-d886-06c5-2a53-1aa4fa73429c", "e52057c6-ac3f-31fb-4a70-eaae19fea906", "e312c6a8-3a17-9d46-bf29-37331b4038a2", "bd8f3bdb-05ce-96de-ee2c-8cc0b020aa38"]}, {"name": "antenna", "origin": [0, 2, 0], "color": 0, "uuid": "484084a5-45a8-f775-cd36-395e0d28c46b", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorBody", "origin": [0, 25, 0], "color": 0, "uuid": "024bf3ac-a147-9be0-d26c-743541c7c08c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["3dd5702d-f04c-77c4-6502-d9edfdcea3a9", "5a4ba021-cf61-d8a1-ec37-9958cffad565", "1cf316b0-1442-40c1-9cb5-f2d3c5ebd6fa", "d619c6b4-e4a5-67de-7ff5-3a3332224bf8", {"name": "tail", "origin": [0, 7, -6], "rotation": [22.5, 0, 0], "color": 0, "uuid": "f5b1733c-f94b-f51a-a123-1b9f562fe777", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["65bb1603-018e-18f4-abac-8981fea57b05", "4aaabe6e-98df-779b-910c-45f9526c458e", "5fdcc0b5-240c-1814-c03b-f20ab8cd222f", "697ec490-a97c-7d36-2f43-ec56ca576705", "f7305be1-4982-fcaa-c417-bb022c05470a", "9bb6f37c-f129-45a4-a7cd-a41546020946", "453d8b04-16e9-d941-6a0e-2448124f0619", {"name": "tail_back", "origin": [0, 16, -6], "rotation": [20, 0, 0], "color": 0, "uuid": "a3ccb209-9488-a202-bc61-143fed07fd61", "export": false, "mirror_uv": false, "isOpen": true, "locked": true, "visibility": false, "autouv": 0, "children": ["95338aa4-5fe8-f0ce-70a7-5dff3053b0d9", "f049923d-0db6-39de-8242-cd4a113a073c"]}]}]}, {"name": "armorRightArm2", "origin": [5, 22, 0], "rotation": [-35, 0, 0], "color": 0, "uuid": "3b44bc15-d5ad-514d-1b24-c75bf2835c59", "export": false, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": false, "autouv": 0, "children": ["c78681eb-4f26-e8ce-6215-21c495d94574", "7126c21f-ec1b-3baa-2570-7af6489777a7", {"name": "wingRight2", "origin": [5, 17, 5], "rotation": [85, 0, 0], "color": 0, "uuid": "bb35dcc9-bf01-0275-8d3a-83ac57542fd4", "export": false, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": false, "autouv": 0, "children": ["3253cba9-0127-e6f5-b15c-426c830ce762", "b0d5d065-359b-0aae-2c85-19ae29a953bc"]}]}, {"name": "armorRightArm", "origin": [5, 22, 0], "color": 0, "uuid": "0bcfa89b-8b50-66f8-9d51-e48b0a9783d0", "export": true, "mirror_uv": false, "isOpen": false, "locked": false, "visibility": true, "autouv": 0, "children": ["59beb952-80e8-8f60-8c9c-776d34f25845", "e79d16be-caa1-b00d-20ff-1e7ef52d330f", "786cb740-da14-1323-2fdf-4a2051fc7106", {"name": "wingRight", "origin": [5, 22, 0], "color": 0, "uuid": "1da212f2-8d24-2c65-4b89-6ce3078f0c58", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}]}, {"name": "armorLeftArm", "origin": [-4, 22, 0], "color": 0, "uuid": "cf2edd07-eb3f-1d93-d994-e27c3bfc6244", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "wingLeft", "origin": [-4, 22, 0], "color": 0, "uuid": "1e568959-0d20-9557-8812-37c956b12a69", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "wingLeft2", "origin": [-4, 22, 0], "color": 0, "uuid": "3cc15674-482e-2a45-3390-c564ca29e96e", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armNested", "origin": [-5, 22, 0], "color": 0, "uuid": "1937f6ef-a3f0-49d6-f0d1-d2f7253e5f6a", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["1c9f344a-321a-164b-d595-d0d9a3594ef2", "702db8ca-f5da-2614-8d00-3ed719ea2134", "acd0cfc8-b34b-6709-5b13-21f3a1b2ba1a"]}]}, {"name": "armorRightLeg", "origin": [2.9, 12, 0], "color": 0, "uuid": "2f2d8f4b-3354-920a-7fb1-7cade178baa4", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["c8ef8996-e7df-a4dd-ce7d-610836b644f0", "fd521ba9-55a4-ad60-2ae8-a8edf602d806"]}, {"name": "armorLeftLeg", "origin": [-2.9, 12, 0], "color": 0, "uuid": "9114e3fe-4f43-960a-522b-49350c626e56", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["5c8a6707-7ecf-4ef7-ff4d-60b0a9e0e9ab", "137788b1-be61-0eeb-2680-8e1ff17980eb"]}, {"name": "armorRightBoot", "origin": [0, 0, 0], "color": 0, "uuid": "155f6af1-51be-092b-18d8-c85c9db7816d", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}, {"name": "armorLeftBoot", "origin": [0, 0, 0], "color": 0, "uuid": "c1dd3af4-c308-2790-d1a1-f33558d17b64", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": []}], "textures": [{"path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\textures\\models\\gecko_model_test.png", "name": "gecko_model_test.png", "folder": "block", "namespace": "", "id": "0", "width": 64, "height": 64, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": false, "uuid": "edee7abf-5220-99d3-48c5-5b27c1e4fc54", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAArZJREFUeF7tW71OwzAQdiQkkMpMIxUhEEgMiImlO2w8AANiYOIJeAooA0xMnRhhYkI8QMVKGZCQ+BGVCmKkA1ORqzpyr4kvcXJNGl9X52zny3ef785XTyC/2/Z73/RIs3FhnOG6eepha+Q5jm5OAlBfWwzdY+vlU0gAalU/dLzz1RUMADMgnQtsnDwaXfDpeBNlscnFUOO8XcB5AKgFsvAMKAQApT4Gt7sNo8j47SPjR7h8ODeOz+z9GMfnVs8CFip/txU2G3tPAlCvLoSf81/fQgJwtTMf6ir79799CUBlZSnUvvf6ISQAs7X10PG/zrNgAJgB7ALoSUR5EhRKAyhfNGpuBqBIx2AuDICLwvwf5vtYektdP7DdX1SMMCZAevID8/04+T118mS7P2cBwLLJUAbobjHtLoACAEUQxv4w1oex/W6lNiIjSXMHLFfA5sf2t3VzaNTWkWOwNYz9l/3KwOit2xN6rA9jexnLyw2qXALaw5Wx+eDz2PzYfNKeAWAGsAuwBhirwnpBpIwiqBdcJBAwICr9KQABgCA4CYAOgrMAKBCcBUBpwQAAXSXLFgpHaYAqvedaj8ujAKKozwAMb5WZAXnRMK91pfjpV2/OMcB5ABTzgmOQkoo2t7WU+4EvL12BzAV0qkHaTeIlo9YYS4aoNlNkACYmgkV0gYkxgIpZWc2rGEqmAVluNM1cUe02AQDYxQG2uG0/DzYv9fjUMIAKCOcBCAoiVAhPw7ySBZmLoO39vQIsrX1S4EkAUP8vSNtfYGPPACREgIQB+h7S9hcktU/4/iI1ANRFVez+P6zomQSETACI6g+Ic39v6i+IY88AaL3GSb68epYZYIOabkN5u8wuEKNHiTXAdQ3AusAwF/fS1gP8g7vINrk4PszHoKHPMA6ArAFF0ADdz7LuL6AOhf8B+Fonk3Vzd/wAAAAASUVORK5CYII=", "relative_path": "F:/Development/Palladium/run/addonpacks/Test Pack/assets/test/textures/models/gecko_model_test.png"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_kineceleran.png", "name": "ov_kineceleran.png", "folder": "", "namespace": "", "id": "1", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "328c9bca-1a62-8377-aded-7a49c91f49a1", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [97, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 63, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD8AAAAgCAYAAACl36CRAAAAAXNSR0IArs4c6QAAAahJREFUaENjZKASEJaQ/w8zio2DC6epv358Q5FbnyIF53NxMOHU9+3HPxQ5u5bjjJQ6nWIDYA4Y9Tw0JNBjHpn/5cMblAjbmSOD4DNy447M/19R5ExqjlIccRQbQEzMj3oeGkqjMY+UgIdFsg9evh1e2pNSClfebRn6eX7U86REOVTtsIj5My3W8GTPxcmLu7Hy/TOqHFL1Rkojx6R6F8U1FcUGwHwy6nloSIzGPI6E/204JvtDNZaIPE9CBwUlnw/V5u2o52F5fijFPHJ3FD2rOvTPgAuh1MkMDAzofWyYQlKqLGT70PW1K9fApQ8UZuCsPmuVyGhgMDAwgKu6Uc/jCLzRmMfWFB0uyZ5XQBRnj8xt+iJEnr/Xj5o+0EZX4JIkVFkoBqLpa1cqhEvvyozDmbGbtXCPGeIrDcB5ftTzOIJo2Mc8cqeEAS3piQiwwIMFvWojtqpDrsLefPiDMyWiV3V49SFluSPbnpBV14GT/ajnYWE3GvOIVDTskz2xnRL08oCBxlUdTvPR2hjn9j0jP8+Pep6IHtmwjHmy0sww0ES1AcyhGBYAblhj4KUIxbsAAAAASUVORK5CYII="}], "relative_path": "../textures/models/ben/ov_kineceleran.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_kineceleran_maska.png", "name": "ov_kineceleran_maska.png", "folder": "", "namespace": "", "id": "2", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "4f6dbf51-3807-4570-e72e-0ca814c09211", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [96, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 64, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAgCAYAAACinX6EAAAAAXNSR0IArs4c6QAAAZVJREFUaENjZKAiEJaQ/w8zjo2DC6fJv358Q5FbnyIF53NxMOHU9+3HPxQ5u5bjjJQ6n2IDkB0wGgB4UgByivjy4Q1KxO3MkUHwGblxR+r/ryhyJjVHKY5Aig0gNgWMBgBSmTCaAoZrFghevh1eC5BSOlfebRkeZcBoAIz0FHCmxRqeBbg4eXE3aL5/RpVDqvpIaQiZVO+iuBaj2ABkn4wGwGgKGOFZ4FCNJaIMIKFTg5Lvh3JTeDQAhnIKQO7KotdfDv0z4EIorTYGBgb0PjpMISnVGbJ96PralWvg0gcKM3BWrbVKpLQ9kRqeMOZoACD15UdTAFoIjIgswCsgirMn5zZ9EaIMuNePGjxoozRwSRKqMxQD0fS1KxXCpXdlxuHM6M1auMcg8ZUO8KbwaACM9BSA3JFhQEuGIgIs8FSEXu0RWw0iV29vPvzBmSrRq0G8+pCy35FtT8iqB+FZYDQAkHpyoylgJGYBYjsy6KmDgcbVIE7z0Zrh5/Y9o6wMGA0AIntywzYFkJV+hoEmqg6KDsXwAABrN2PgEINvLAAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/ov_kineceleran_maska.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAGdxJREFUeF7tXWGsHcdVvs+loYRWduTXpAFD5CaojR+FYqU/UBVoDEIKAoSwkJoUVShpVVdtImyKRF1XCDUQJAJGMVGDwBZCUCOhtCigWlTgtKRVf7QqbeE6RU1qNQRCwrNilxBKCn7o7fV5vnuyszNnz5k9O7uf/1y/e+fMnPnOmW++md2dXZk5/1tZWdlwdsG1+Y2NjRUrB3a+5rotLK94xZXBal/85gu13z72ju/a+vvKV2wL2r3wzYu1337kns+qffeOvyX+VnHssx51ALXOeieA1n+tvWUCggDk0bDEX966vwUIwDkGlgnYRgDLiuD58+u1Xv/Ne3dd/nvlO8OIbPxX7bebjnxGnT/eE4Al/s6p1Kl5dQA7tbpk5J0AWv+19pYJCAKQR8MSf3nr/hYgAOcYWCYgCEAeTEv85a37W4AAnGNgmYAgAHkwLfGXt+5vAQJwjoFlAu4/earTFZX3P3EP9gCc88CreRCAF/KX2gUB+F4GtsTfOZU6NQ8C6ASbnZFlAkIByONiib+8dX8LEIBzDCwT8PP3vHlrCXDld7wq2LMX/vs/678tXfqT3Ah00wc+oc4f76tAlvg7p1Kn5tUB7NTqkpF3Amj919pbJiAIQB4NS/zlrftbgACcY2CZgCAAeTAt8Ze37m8BAnCOgWUCggDkwbTEX966vwUIwDkGlgn490d++PIegOChntq6H7cCO2dEv82DAPrF+yWtgQB8LwOurCyGwMWLF3sdC9u2bavIuu92eQL22ummsYZNQLvHgaEA5GwOApBjZmoBAlgQwPJtvBzgtxx9cOur2l17s9mMP6NPBSWX85bb43b3Xn9k6+dPHjwQjP0HX9stLQ5+9sluhkZWRABUXe4ZmWb+vtqLwQQFEEMo8++0BAABZAY6UD0IwAf3rVahAKAAPFMQBOCJ/mw2AwGAADxTEATgiT4IYEZLgFfteHXwSb6f+PCfXN4D+NrResTYKT2XpVX6yT61CtllwHtfe3Dr50+8++3BbPnQnvAZhG0phj2Afq8+8FhgD8CZAEEA2AT0TEEQgCf6sxkUAK4CuI5B18Y3xx72ABZ7AMu38c6YDF/d8W1bNMUv+6VeBly+vLd+/n+DtMcvA7baLS0/Pv3xpzpRKZYAWAJ0OsWmU7YN0IiWACAAn+DwTcBc1+f59f9c7UhRhAKQImZcHgQwrD2AXAMTBBC+EQMKAEsAY1pNry6kAKyIIDTwrepP72lzSSgALYJKe1IAqffx8/2BWebLgMH62W3IXzj9b52QGNoeAO+E9tZgEEAkLbAJuNgEBAF04o/ejfbs2VNr88yZMyIfSHFsbCyEr+XToCJHLhWGAuiCmqENFIDvHoA0lCAAKWJQAK0IeM8AxuEUV1eaAtQSAAfIO/5QAOKUtTXwTgDb3shrAwHYnQchR7+6D8f3RJYuTsMGCIwVgdhVCW2/ae+B6gEBaBGFPRAwRAAEYAgmqgICpSEAAigtYvAXCBgi0DsBrK2ttd6JJ73OGcNi9ZqrqyLrzzxbfd5y//Gayfmzj9f+/oej98aqbP39hw6+v/b7jt031P5+5O47q7+5X6pGl4z5rjGv1xpfqd/e/sUSnq9Zpf3LXX/Mnxi+8/ncdSN+BQQAAmhL4twElXuA5q6/eAKgqwAxpuo6c9HMSvbfc/tiwNFM/Jn3vatW9Zvv+4NWRTB/4Ler3198sf5I6xVXLB6ZXXvPr7TO+KH2SHn8y0fqioSUijbQXfGLtav9nS5DxiYCK/9jAzK1Pw272ammreW6Kg7p+PGe+QmErasA0g6kzgwggOZ8S8XPJKtbKgEB1MGZLAHwHNESAg18mvGpfpr5r1q9tvrqr3/xZ6rPX330uurzt27+evX5U3/8cPX53PrT1SefuflMT8qA2iElIW2H70GQIuBKQItP7oGdWn/oRiQrRZA64//YQ39Zc/nv9v9saheSynWtP0QI0vgPZcbnYAXvA5B2kM9oIIA61EOZ8XkCgADa+WOyBKBVBM+eW6+q4Gt+mpGpfq4A6HuuBKgc/R5TAKQgUuuneklx8D2Bq3euJs00VGioAz6VAHg5qSJ47LHHRHjRDP2atbWa3b/P59XfUkVgXd+NN94o6s9QZ/xkBQACWFyOpCUACKD9cjHPFxCA7+W9VLYS3wqcujTQKgCuBOhvvjdA3/O1Pn1PewscEK4wrBRAKTM/9Vf6MFKqEpASwNu+8tXWnE1VAqGZn1f+Z6//vtQxUpVLVQClzPzUeRDApc1GEEDaeAABtOM0egKg7kuVwBs+sLijL7QHQPUe/KvFa2a/fcf/1ZAOzdg8HKE1//+cf1lV9OhPf61mQoqCE8A//sbiDsJU6V/azN9VAZCdNRHEFEBsBtfah4b1WGf+zgoABNCcKiCAZlxSlwLaAay1BwGkKcCtUnR9N8aQtBcQUgBUId/l50qgqwJInfm7KgBK8K43kAhhNy8u3QMgB+iwy1j8UwmA6pUOZCkg1mt/6p/28FBpP6zKi/cAlqRD9d9YAoAArEKVpx4QQDuusfyeLAHkIoKQIqDv+Zqdh48riVQ7uv6fuvYvfebX7gHkVgI8rlJlIJ3peXtjH/jqPQAQwOJGl1KlPwhg2jO/GQGkEkHqUiCP0I3XmqoAxjLzWxFAqhKQ7gWEIhZTAtqZn9oNKYDSJT/HtfMeQENFrXsCIIA4CXmU6LoHwH2NbQqCADyiG2/TjAC4Eggx6dCIIDbz88QtXfLzlLAiAK4EQvHXEkFuBcBnfu5vqbv9ISoAAVx63Di0+QcCiM8iyyX4u/BiA0pW+2wGApAi1l7enABCSwP+/c6rX119Fbs/gOxCu/uh7sWuFvCZ/9yz/9FY1dhm/NwKILQ00KYtH/j0bADVy58itNoLGNuMn20PoEViNP4EAtAOCRt76yUACMAmLn3Vkl0BhPYG6PsYEUhnfg5c7F7/qc78hFNuAgjtDcQSnJ7qu/iDb6yKbvvSF6vP2LkAXe24P2Of+ZfGZT+vBgsdDQUCiA2FvL+DAJrxBQEY512IAGiNTUeI0Z4APwMwdA4AdzN0LgCdEUibfXTGX8wvYxgGV503AVD8eRxCZ/jxTUX+EBbV0/UMQAoQCMA4VWMDDQRgDHhidSAAKIDWNwMl5pG4GBEC32Wn8/2pwth7AnjD/M0/XEnw9wmE/BB3qFCDvghgac1Z5VsI99gpwqkKQBoOysO+8ZD6aV2+t01A7jgIwDqU3errO+HpRTQggG7xsrayeC+Zi4JYAsKiD51xpYTuXEHhhn0TSMNE0ph/qSdWlXaEl3W6WAweEIB1VAqqDwRQULAaXAUBKOMHBbBhkUOdo5B6NmGoASiAztBvGUIB6DEstgZvBQAC0KWOBXuDAAQxuPrAztbSzz54TlCbf1EQgH8MNB6AADToLS5niQgQBKAEnJlDAejwBAHo8EsmgNjA526UogSgAJQJ5GwOAlAGIFUBgACUQAfMoQB0uIIAdPhFFYB04JemBKAAlAnkbA4CUAYgpgBAAEqAI+ZQADp8QQA6/KAANnAfgDKFXM1BAEr4oQBAAMoUcjUHASjhBwGAAJQp5GoOAlDCDwIAAShTyNUcBKCEHwTgSwAh/PE0YFpigwDScAqWAgGAAJQp5Gq+SQCiW1ldvW1ofO+hw9W3Tx5/oPpcv3BB5GLsBBpRZQ2FX/f716uq+Of3PqGyL8U4dcam/vCzAEvpJ/fT+z4KEMCKhQgKpx8IIG1oggDScLIuVTwBaAHJpQDoBqCrvn+HysXn/ul8ZV/KswFdOwsC6Iqczg4EkEkBgABkiQkCkOFlVRoEYEwAoVt/pUqAZn4e6LEqARCA1ZCW1QMCAAHIMiZTaRBAJmAj1YIAjAkgtOnHZ3SuCGK/UxzHflUgRgRWu/+33H88y4h75O47RfXiKoAILvvC1puAIABdjEAAOvyk1sUrgNXt22t9vmbXrurv+XyehEVfBMCdSZ3xuR0UwJmkuPJCfMand0h2qqzF6Ln1p2u/xhQBFIAyAiAAJYADM8+lAEAAzYEuXgHsO3ai1jNi9k8deGv1fezOQC8F8Idv+dOa3+/85C8kDUUoAJkC6Gvgv0ThJSoBKICktA8XKokAnvilryf19vrfuy5YDgQAAkhKosRCxSsA/iwA7zftCfDvaY+gTwVABPDrf3tTY3h+7cc/X30PAghnb+pVAJr5c631E8fXVjHaG+B7AlAAUiRZeRCAEsCBmVvtAYAA0gJbvALgm4DUbT7zh64K9KUANtf8+95481ZUuAqg2X+zwOkvPjoL7QlgCZC2BAABgABqCAyNADadIxJYHvwggD2tmYslQNrATi1VvAIIdZRvDp6+647Gon0pgPd99wdn7/75d9R8CBHAh//ij2b3/euHGv0dmwLwXgOnDpSxlgMB9HQrMCeAtiUACGCsw214/RodAdDMz3d/aReWK4G+FMBm6HEV4KUDAArAlxRAAD0pABBAc6KDAJwJYN+xE7UzAUNrZV83L7fO1/Yhv0IKgJeP3ast7XfbEWC4EQgKQJpPucuvgABkj2/GApJ6BuDmnsDyv9CmH28Pm4CxCOB3CQIr+0+eqikA/jQTVda3MgjN9KE7u7jfMQVAv3/09p+U4BUty08Ekp4ExBvgTw2O7UQgLAGiKZW1AAgABJA1wWKVgwBiCOX9/SUEEGoutzIIPdST2v2hKgDuf0wRhM4CpHqgAFIzAuVSEAABZFYAIID2NIQCSBmm+cokE4DUhZBioHqG8pRW7j0AKW6x8lAAMYTwuwQBEEDPCkASnKayIAAtgrBfRqB4AiClIVUUoeezrdIj9H6ArvWPbeATDlgCdM0IGzsQgPAY51TYQQBpSIEA0nDKVcqcALruxnft4FAVAPVHSwRjnfmhALpmvK0dCCCTAgABpCUqFEAaTrlKZSOA2Jq868zNgYgpjlh562cBeHtQAO2pCwLINbTT6gUBQAGkZUqmUiCATMAmVmtOAIntmhWDAjCD0qUiEIAL7FuNggCgAFwzEATgCv8seiDI2tqaysPUd/SpGlEYW58IhD0AWTBAADK8rEuDAIxPBAIByFIUBCDDy7o0CAAEYJ1TovpAACK4zAuDAEAA5kklqRAEIEHLvmyUAKhJ6V7A0Nf+1K/cewDUjvR+gLHfAUi4gADsB7WkRhBAZgUAAmhPRxCAZLjal00mgFQlUMrM37cCSCWCqcz8UAD2g7lLjSCAnhQACKA5PaEAugxbOxsQQM8EYBe6cdQEAvCNIwgABOCagSAAV/jjdwKmrv15N0rZC+jrKoBvmIfbOgjANzbJCgCXAX0DNdbWQQC+kd0kAO2/2puFOlRm4UOHZmECBICAxeADASCPgEChCIAACg0c3AYCFgiAACxQRB1AoFAEQACFBg5uAwELBEAAFiiiDiBQKAIggEIDB7eBgAUCIAALFFEHECgUARBAoYGD20DAAgEQgAWKqAMIFIoACKDQwMFtIGCBAAjAAkXUAQQKRQAEUGjg4DYQsEAABGCBIuoAAoUiAAIoNHBwGwhYIAACsEARdQCBQhFIPhBkqP3be+hw5dqTxx+oPtcvXJC6akGC0jZRHggMAgEQwKw6Fg3/gMAkEVATAD8qrJSzAJeiDQKYZOqj05sIgACgADASJowACAAEMOH0R9dBACAAjIIJI9CZAELHhGMPYMLZhK4XhwAIAAqguKSFw3YImBMAudaXEljdvr2GxjW7dlV/C9rHVQC7fEJNhSEAAoACKCxl4a4lAsUTwL5jJ2p4XLV6bfX3pw68tfpMuDMQCsAyo1BXUQiAAKAAikpYOGuLQNvs1/rKr9jLQgVr8NQeNfq699Dhyk96FoBXRnsC/Pv5fI6ZPxV5lBstAiCA0YYWHQMCcQTEs+Da2lrSy0AVCkDk0+r27Y3+8JkfM348GVBiegiIBtsmPCCA6SUJejxeBMQEQFBkIILOvow3POgZEMiLQOdBBwLIGxjUDgT6QKAzAaQqgYS9ALUPfQCFNoDAGBFQD76YEgABjDFt0KexIKAmgLEAgX4AgSkiAAKYYtTRZyBwCQEQAFIBCEwYARDAhIOPrgMBEAByAAhMGAEQwISDj64DARAAcgAITBgBEMCEg4+uAwEQAHIACEwYARDAhIOPrgMBEAByAAhMGAEQwISDj64DgZX9J0+1nvDz3PrTjSjR6bshCM9+7tHaT7vfdHMr2qF2Xrlzccqv1b+XM8p76LZbQYIKcOlhsGeeeqqqZf3CBeCpwLNvUxAACECVcyAAFXzuxmICiM38NJOfP/t4rXM7dt9Q/Z1qT8ZSBfD8uYVi4Xb0PW8fCqBbDvKBz2uBEuiGa99WIAAogE45BwLoBNvgjMTrtdQ9g9N33VGre9+xE9VeA5+Bv8V2IB6+Xbcmp3ZCSHO/BheRQhyKEQB1A0pg2AEFAQw7PoP1DgQw2NCIHDMjAFr7x2ZYrgSgAETxci8cU4D0TkYoAPdQJTkAAkiCCYUIARDAuHJBTADj6j56k4pA6PBXuv5P9fzAPUer/375yMHqE3sAqQj7lAMB+OBeXKsggOJCluQwCCAJJhTi0p+v9TlCpARie0JA1hcBEIAv/sW0DgIoJlQiR0EAIrimUzgk+V9/5L4KBLrqQ2t9jsz33vme6qsv/O5vIscGnDYIzoCD4+kaCMAT/f7aBgH0h3VRLcUkP63xqVPY9S8qvFvOggDKjFt2r0EA2SEeRAMWBNB6nkBCLy18SGgmWxFt/7WOueIXezksde7MmTON/dzY2HD1Xwt+6fYW4GsHgIUPnnHQ9l/ruyt+IABt+HztLZJHOwAsfPBEUdt/re+u+KUSQKiT8/nc1X8t+KXbW4CvHQAWPnjGQdt/re+u+IEAtOHztbdIHu0AsPDBE0Vt/7W+u+IHAtCGz9feInm0A8DCB08Utf3X+u6KHwhAGz5fe4vk0Q4ACx88UdT2X+u7K34gAG34fO0tkkc7ACx88ERR23+t7674gQC04fO1t0ge7QCw8METRW3/tb674gcC0IbP194iebQDwMIHTxS1/df67oofCEAbPl97i+TRDgALHzxR1PZf67srfiAAbfh87S2SRzsALHzwRFHbf63vrviBALTh87W3SB7tALDwwRNFbf+1vrviBwLQhs/X3iJ5tAPAwgdPFLX91/ruih8IQBs+X3uL5NEOAAsfPFHU9l/ruyt+qQSApwG1Yc5jb5E82gFg4UMedNJq1fY/rZVwKVf8QADa8PnabyaPdwJnQWD/yVNVvbGz69YvXMjSvnelqf0/941veLtatb9nzx6RHyFFIapkM/knfh4BCAAEIB0zWcqDALLAGq20dwJYW1urOTWfz6NOSgrw+sn2mgO/XP33/NnHq88njz9QfY5NAUj7DwUw7ROJQAAjUwAgAMl0gSXA6Ahg37ETVQY88+Dv1DKBv8NubDM/dVba/5UV1z3ErRhhCSAjLqvSIAArJAdSDwhAFghsAvZ8FSAkUa32AmgA0Fqf0oHW/Ft7Art2Vf+1aleWdvlKS/tPeyNWHj1y952qqmJKQLv7f8v9x2v+Tf3dhe4KgKJhNRClA8CqXVXWGxpL+w8CuGMYayDDHJBU1RsBhGZ+awKg+la3b6/hwN9kc9XqtdXvD912qwSvYsqm9t+6Q3TfBdUrVQTWCoDP+BR38u+h224FAVgnQVN9IIA+UL7cBghggQUIoD3vRqsAeLf3HjpcfcX3AsZ6NSC1/z/64J9nYaauSsBKAcQGPhTAAgEQwMjuAwiN5hABggCwBAjljOkzAgZLANVabe+hw1V/SAEsvb8+yww4tEo5AVD/d7/p5l5cJUUQ2xPQKgCa+flaP9RJ7AGEww8C6GVo9NMICKAZZxCAPP86EUNMAZAbDu+K69QfOWz9WtDlQGr19F13NDpATw3m9q4vBfBzH/m4qCsgABFcVeFOAwYEIAdaYwECSEMPBJCGU1OpJCIY8MCnPiX1oztMPpapBHBpI9jHSbTqjoBmYy1p4IAAfGIMAvDBvbRWNQSQNIPGCMBhzc9jlERkpQSWBj7fBac1eMNegEUOlAIP/GQIWAS/dQCBAPrNORBAv3iX3poFARSNwb5jJ2oEFtotH0onubQP+RVSALz81J+GG0pcvfwAAYAAJp8DXoNvCO1OPvj7T56qKQB+DzsFqW9lEJrpQ3e4cb9jCmDpacjJ58AQBqKXD5MPPghg2vfCew28obQLAmAKIBSYXMpAOtNbJ87Ub4SxxrO0+kAAIIDJ50Bpg9bS38kHny8BtOCGlALVm/qUmtaPVHsogFSkxlkOBJCoAFLDDwJIRQrlhoAACKBwAiDCkSqLpTsDJ58DQxiIXj5MPvilLwFAAF5DZxztggCMFEDX6/DaNAIBaBGctj0IAAQw+RyYMgWsxCRwaFMrtuY8+7lHa7jGzp4LtfPKnYvz+63+vTxTuqfOxKnlUvsbUx68Hl4ezwKkIj3OciAAo7imDuzUcqlugQBSkUK5JgTEBBCb+Skh+bv5duy+oWo/1Z6clSqA5889XZlyO/o+1n5paQICKC1iw/IXBDCseIi9AQGIIYPBEgLiFXHqngFfW9Jz93wG/hY7TuTh23UPp/Dn+3m0G9a8qgNNYtlUwMtHxTkQ6zN+LwcBcfBBALLgggBkeKF0vwiYEUDqnWVcCUAB9BvwhtbEOeDuMRwwQ0Ac/JACAAE0xwQKwCxXUVEGBMQEkMEH7yqTTgWOHW7KO1HAwCeXkQPeGejYPoKf+KYjEIBjlqLpbAiAABIJgCIQI4KCZn4ogGzDqpyKQQAgAORAOePV3FMEHwSAHDAfVuVUiOCDAJAD5YxXc08R/EQCiK39cRXAPDdRYQ8IgABAAMiBHgbaUJv4f3LobPkiVoS8AAAAAElFTkSuQmCC"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_kineceleran_mask.png", "name": "ov_kineceleran_mask.png", "folder": "", "namespace": "", "id": "3", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "9ea6f1f7-00d9-aba6-b651-a45677d6eca4", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [96, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 64, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAgCAYAAACinX6EAAAAAXNSR0IArs4c6QAAAZVJREFUaENjZKAiEJaQ/w8zjo2DC6fJv358Q5FbnyIF53NxMOHU9+3HPxQ5u5bjjJQ6n2IDkB0wGgB4UgByivjy4Q1KxO3MkUHwGblxR+r/ryhyJjVHKY5Aig0gNgWMBgBSmTCaAoZrFghevh1eC5BSOlfebRkeZcBoAIz0FHCmxRqeBbg4eXE3aL5/RpVDqvpIaQiZVO+iuBaj2ABkn4wGwGgKGOFZ4FCNJaIMIKFTg5Lvh3JTeDQAhnIKQO7KotdfDv0z4EIorTYGBgb0PjpMISnVGbJ96PralWvg0gcKM3BWrbVKpLQ9kRqeMOZoACD15UdTAFoIjIgswCsgirMn5zZ9EaIMuNePGjxoozRwSRKqMxQD0fS1KxXCpXdlxuHM6M1auMcg8ZUO8KbwaACM9BSA3JFhQEuGIgIs8FSEXu0RWw0iV29vPvzBmSrRq0G8+pCy35FtT8iqB+FZYDQAkHpyoylgJGYBYjsy6KmDgcbVIE7z0Zrh5/Y9o6wMGA0AIntywzYFkJV+hoEmqg6KDsXwAABrN2PgEINvLAAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/ov_kineceleran_mask.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\recalibrated\\r_kineceleran.png", "name": "r_kineceleran.png", "folder": "", "namespace": "", "id": "4", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "0e3de805-c0dc-6f69-b3dd-f38056587441", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [96, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 64, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAgCAYAAACinX6EAAAAAXNSR0IArs4c6QAAAZVJREFUaENjZKAiEJaQ/w8zjo2DC6fJv358Q5FbnyIF53NxMOHU9+3HPxQ5u5bjjJQ6n2IDkB0wGgB4UgByivjy4Q1KxO3MkUHwGblxR+r/ryhyJjVHKY5Aig0gNgWMBgBSmTCaAoZrFghevh1eC5BSOlfebRkeZcBoAIz0FHCmxRqeBbg4eXE3aL5/RpVDqvpIaQiZVO+iuBaj2ABkn4wGwGgKGOFZ4FCNJaIMIKFTg5Lvh3JTeDQAhnIKQO7KotdfDv0z4EIorTYGBgb0PjpMISnVGbJ96PralWvg0gcKM3BWrbVKpLQ9kRqeMOZoACD15UdTAFoIjIgswCsgirMn5zZ9EaIMuNePGjxoozRwSRKqMxQD0fS1KxXCpXdlxuHM6M1auMcg8ZUO8KbwaACM9BSA3JFhQEuGIgIs8FSEXu0RWw0iV29vPvzBmSrRq0G8+pCy35FtT8iqB+FZYDQAkHpyoylgJGYBYjsy6KmDgcbVIE7z0Zrh5/Y9o6wMGA0AIntywzYFkJV+hoEmqg6KDsXwAABrN2PgEINvLAAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/recalibrated/r_kineceleran.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\recalibrated\\r_kineceleran_mask.png", "name": "r_kineceleran_mask.png", "folder": "", "namespace": "", "id": "5", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "16694038-356d-1451-c966-cf4cf3e4ff08", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAGYFJREFUeF7tXU+sHld1/54hZoPkRHnECTIgV6lE/AS0RiwQCiiGTRCtKrxJ0k2VLLAUJVKcVmpd2BWoVEJQrKCwsNVNEzZGKKmSTUiURqgL1D+AvpdKjbAIiMTpsxKjbOqU96o3n495czx37jn3nJl778zPm+f3vvvnnN8953d+9858M2uLzP/W1tZ2MpuQdfqdnZ21rAZknjz3+s8e/8zrv8gdALn9n30AZi4As8c/dwKAAKAAcsYgCCAn+osFFAC2AFm3gCAAEEBWBGYfgNgCZI2/7AdQ2AJgC5AzA2ZPwDnB350bBAACyBmDIICc6IMAFrMPQGwBsmYgtgBZ4V+AAEAAWSMQBJAVfhBA7i3g7BVY5vjHGQAuA+IyYMYkhALICP7u1LOvQNgCZI1AEEBW+EEA2ALkDUAQQF78oQCgALJGIAggK/xQALkVwNraKgW2t7dHzYV9+/Y1Zx9jz8vDfVSnu3ItdwBkzn8ogMwKAASQOQNAALgTMGcIEgGQDUNXZKr8Y80XwxYKIIbQwJ/jKkDeB8KAAAYO8NjwUABQALEYGfJzEMCQ6ArGBgGAAARhMlgTEMBg0MoGBgGAAGSRMkwrEMAwuIpHBQGAAMTBMkBDEMAAoGqGBAGAADTx4t0WBOCNqHI8EAAIQBkyrs1BAK5w6gcDAYAA9FHj14MTwFDX5/n1/6Hm0SKD+wC0iDm3x30AZd0HMFRiggACiQMFAAXgzKmq4UIKwIsIQonvNb7K2Y7GUABWBI39oQDKVABeCQoCiCQIFAAUgJFDR+1+5MiR1nybm5uq+Ulx7OysHoSUuwBAAaiWz79x7gDw90g3Ym0FAASgW99o69oCIOqQsgEIIO8WQLlcCysB8Plyrz8UgDYCnNvnDgBnd9TD1VYAJkcAtS2AOsLQAQhUhEDsqoTVFTp7oHHWQABWSNEfCPghAALwwxIjAYHqEAABVLdkMBgI+CEwOgFsbGz0vplFe50zBsX6wZuaJlsX3mx+3vHYmVaXt8+/2vr9Px79ZmzI3s//+KG/aX1+/eFbW7+/+OB9ze/cLtOkezrzQyM+rje+Wrtz2xcLeL5n1fo39Pgxe2L4LpfLrAfxayAAEEBfEA9NUEMn6NDjV08AdAgYY6rUykWVlfp/6J5VwlEl/vFffqU19Ge+9b1eRbB8/B+azy9f/r9Wu/3739v8vnH/X/VW/NB8pDx+9WRbkZBSsS50Kn6xea2f02XIWCHwsj+WkFJ/Ok6zpV1726UqDm3+5K78BMLVqwBaB6SVAQTQHW9S/FyiumcQEEAbnNkSAI8RKyFQ4lPFp/Gp8t+wfkvzp3/+iz9tfv71yx9pfv797b9sfn7pH59ufr619Xrzk1duXulJGdA8pCS08/AzCFIEXAlY8Rk6saXjh25E8lIE0or/+XM/bJn8o+N/JnVB1C51/BAhaNe/lIrPwQreB6B1kFc0EEAb6lIqPg8AEEA/f8yWAKyK4M2LW80QfM9PFZnG5wqA/s6VALWjz2MKgBSEdHwalxQHPxO46cZ1UaWhRqUmvJQAeDutInjllVdUeFGFvnljo9XvjeWy+V2rCLzHu+2221T+lFrxxQoABLC6HElbABBA/+ViHi8ggLyX96Rspb4VWLo1sCoArgTod342QH/ne336O50tcEC4wvBSALVUfvJX+2UkqRLQEsCf/9d/98asVAmEKj8f/J8++ofSHGnaSRVALZWfnAcBXDlsBAHI8gEE0I/T5AmA3NcqgY/97eqOvtAZAI370DN/0Pz3fdf/roV0qGLz5Qjt+f/37fc0TR/9k1+0upCi4ATw86+v7iCUSv/aKn+qAqB+3kQQUwCxCm7tH0rrqVb+ZAUAAugOFRBANy7SrYA1ga39QQAyBXi1FV3fjTEknQWEFAANyE/5uRJIVQDSyp+qACjAU28gUcLu3lx7BkAG0MMuY+svJQAaV5vIWkC89/7k3/b2dtZ7+rU4mBUACGAFIQig//IYCCA1Ncfppz4E5GZ5E0FIEdDf+Z6d28OVhLQfXf+X7v1rT3zrGcDQSoCvq1YZaCs9n0+qbGqt/GYFsGeA5r8xwKRbARDAOMwPAujHORbPtUt/NwKQEoGWAMZNg99/5yCmAKZS+b0IQKoEtFuB0PrHlIC18tO8IQKYSuKDAFiESbcAIIDu1IwdCoIAxi5psvnMZwChM4EQk5amBGKJzwO31tP+UDikXgUIjcdfhcUrqZUIhlYAMXtr3/N35Kvvixn41z85oCAAGTOP1QoE0EYaBOAceaHvg9940weamWL3B8QOBUPmxq4W8Mp/8c3/6RxqahWfO+lNAHz82MsxpeHGKz99N4D6828Rep0FTK3iD64AYlsC+hwEIA39YduBAPrxBQE4xV+qEghd15eaFbvXf66Vn/AbmgBoHq0SoG/1bX/ij5oh9v30P5ufsecCpPbj8TT1xCd/3Q8BQ4kJApBS1rjtQADdeIMAnOMwRAC0x6ZHiNGZAH8GYOg5ANzM0HMB6BmBdJ2fnvEXs8sZhuKGy00AtP58HULP8OOHdPxLWDRO6jMAaYFAAM6hGks0EIAz4MLhQABQAL1vBhLGkboZEQI/Zafn+9OAsfcE8In5m3+4kuDvEwjZoXao0g5jEcCePWcTbyHcY08RlioA7XJQHI6Nh9ZO7/ajnQFww0EA3kuZNt7YAU8vogEBpK2Xdy+P7zBnURB7gPDwIRnXub9efWwC6SgknfEnfWKV9UEuuf1PDtwrHT2SBwRgXYWK++dOgBABgwBkQQUCkOEUbAUFsOMRQ8mrIH02oXYCqTLITYBav65RUNYBFosFFIADiLUOkTsBQAC2yPFgbxCAYg3e/M1velvf9MEPKkbL33SqBEDIxpRAbv+tEQACMCKo3QKAAIyAs+5DKQAQgHydoAAEWMUSnw9RixLIXQGtBBCr8LHDxNpeBIIzAEGyappIFQAIQIOqvC0IQI5VV0tsAWz47d7R1quAtIlfmxKAAqjjJaChMAcBgABMCIAAQAA4A+hJISgAE79EO1u3ALEJYmcEuQkwZn/scyiAGEKRz7EFmOaNQLgKIE8MKAAoAHm0OLccSgHEKj+5AQWAOwFxCOic1JrhQAAatK5tiy2ADT9cBdjJuwXAl4FsAQwCsOEHAgABeOSQMQrTu+8an3sPn279YrE4evJU0/+1M483P7cuXVKNF3sCjWqwjsbb29umIfbt22fqX0vn2B133A/pHr10/3OfIYAA1oYlcBCALAVBADKcvFtVTwBWQIZSAHT9f/3mm00mbr3xRtO/lu8GpDoLAkhFztYPBDCQAgAB6AITBKDDy6s1CMCZAEJ3/mmVAFV+vtBTVQIgAK+U1o0DAgAB6CJmoNYggIGAjQwLAnAmgNChH6/oXBHEPqd1nPpVgRgReJ3+3/HYmUEy7sUH71ONi6sAKrj8G3sfAoIAbGsEArDhp+1dvQJYP3Cg5fPBQ4ea35fLpQiLsQiAGyOt+LwfFMCmaF15I17x6R2SSYP1dHpr6/XWpzFFAAVgXAEQgBHAwroPpQBAAN0LXb0COHb6bMszYvaXTtzV/D12Z2AuBfC173x38fwzP7hq+7/+6HlRKkIB6BTAWInPF0+qBKAARGEfblQTAezfv1/k7eXLl4PtQAAgAFEQCRtVrwD4dwG433QmwP9OZwRjKgAigFM//kjn8nzjM79s/g4CCEev9CoAVf6h9vrC/LrajBQBPxOAAtAiydqDAIwAFtbd6wwABCBb2OoVAD8EJLd55Q9dFRhLAXz6819Y/NvL/3J1VbgKoOq/2+CTt392EToTwBZAtgUAAYAAWgiURgC7xhEJ7E1+EMCR3sjFFkCW2NJW1SuAkKP8cPCFB+7tbJpLAYAAVssx1B15pez9edDxqwMvPHDvsN9HjzABCGCkW4GxBeiORBAACECqVkTtqPLzCkDMy5XAWApg13hcBbh2Cb0JoNTKH1ICUACitJY3AgHIsSqhJQggswI4dvps65mAob1yCcGyawPf24fsCikA3j52r7bW775HgOFGoGvR/PKTz2ohnlT7c3ffmfcMAASg+/pmLPqkzwDcPRPY+2+utwKDADITwPGnnmspAH5KSUE6tjIIVfrQHo/bHVMA9PkP7vliLKdVn/MnAmmfBMQn498anNoTgUAAIIDOBAMBrGABAaj4t7rG2bcAXAGEEBxaGYS+1CNd0VIVALc/pghCzwKkcaAApBFRRzsQwJV1AgF0V3wexiCAOhJbamU1BCB1iNqFFAN9Xsr12qHPALS4xdqDAGII1fU5CCDzeoEA8i4ADgELOwT0CoexFADNo1UUoe9ne/kfej9A6vhTq/yEAwgABJCaE00/EIAJvuydQQATI4DU0/jUSCyVAMgfqxKYauWHAlghMLkzABBAm8pAAP3UDgUwUQUQ25OnVm4eTjHCibX3/i4Anw8EAALoQ2CyCgAEsFp2EAAIYFYEkLqXT+0HBZCKXBn9Up/dF7rKFCs8IUWY2o/GS+2f/XkA0luBywiXa60AAZS6MjK7QACZnwewWCxa3wbky7axsSFbyUAr6Tv6TJMYOns/EQhnALrF0L65J3Z/ibQipyqI2PwxJVDdMwFBALqABgHo8AIBQAHoIsa5NRSAM6DK4fiLQA6eeLgZIfQ8B37VJvQiEek43vNL5yWYin8zEBSAMqJZc1wF6MfPOwFpNmkies8vnbcaAiBDtURQ+t6f/BpaAdA8WiKY+h2AhAtPQHrxB98aXHjikaYLfzFISAFIx/GeXzovCMBWWN16gwDcoEwayDsByQhpInrPL523OgKQKoFaKv/YCkCqBOZS+bkCCL3yixI09jlPfM5GoXGk48fmT/28+DMADmRsKwAC6C+Esa0ACKCNnzRBQQBJAmyhfjUYCCANaPTqRkCa4LEKCwJIizAQgPO7AdOWYb69QAA7eV8MErsTULr35yFcy1ZgrEPA+aZ4v+cgABBA1twAAWSFfwECyE8A1gjo/S6BYPCsEkhgH5oAgcki4JF8IIDJhgccmzoCIICprzD8AwI9CIAAEB5AYMYIgABmvPhwHQiAABADQGDGCIAAZrz4cB0IgAAQA0BgxgiAAGa8+HAdCIAAEANAYMYIgABmvPhwHQiAABADQGDGCIAAZrz4cB0IgAAQA0BgxgiAAGa8+HAdCIAAEANAYMYIqB8JVhpWR0+eakx67czjzc+tS5e0JnqQoHZOtAcCRSAAAlg0D0bFPyAwSwTMBMCfElzLswD3rDYIYJahD6d3EQABQAEgE2aMAAgABDDj8IfrIAAQALJgxggkE0DoDUE4A5hxNMH16hAAAUABVBe0MNgPAXcCINPGUgLrBw600Dh46FDzu2J+XAXwiyeMVBkCIAAogMpCFuZ6IlA9ARw7fbaFxw3rtzS/v3Tiruan4M5AKADPiMJYVSEAAoACqCpgYawvAn3Vr/eVX6GrAAOeAXTaevTkqcZO+i4Ah4fOBPjfl8slKr9vLGG0ChEAAVS4aDAZCHghoK6CGxsbopeBKk7huS8qm9YPHOi0h1d+VHyvkME4U0JAlWy7joMAprT88GXuCKgJgAAbgAiSbZn7IsJ/IJCKQHLSgQBSIUc/IFAOAskEIFUCgrMAsw3lwAlLgEBdCJiTL6YEQAB1BQSsnRcCZgKYF1zwFghMCwEQwLTWE94AARUCIAAVXGgMBKaFAAhgWusJb4CACgEQgAouNAYC00IABDCt9YQ3QECFAAhABRcaA4FpIQACmNZ6whsgoEIABKCCC42BwLQQAAFMaz3hDRBQIQACUMGFxkBgWgiAAKa1nvAGCKgQWDv+1HO9T/h5a+v1zgHp6buh2c7/5OXWR4c/dXuvYaF53n/j6im/Xv+uY5R37u47QYIGcOnLYBd+/etmlK1Ll4CnAc+xu4IAQACmmAMBmODL3llNALHKT5X87fOvtpy7/vCtze/S/tRZqwDeubhSLLwf/Z3PDwWQFoM88fkoUAJpuI7dCwQABZAUcyCAJNiK66Ter0nPDF544N7W2MdOn23OGngFfpedQDx9j21PTvOEkOZ2FbcilRgUIwByA0qg7AUFAZS9PsVaBwIodmlUhrkRAO39YxWWKwEoANV6ZW8cU4D0TkYogOxLJTIABCCCCY0IARDAtGJBTQDTch/eSBEIPfyVrv/TOB//u0eb//7sqw81P3EGIEU4TzsQQB7cq5sVBFDdkokMBgGIYEIjLv35Xp8jREogdiYEZPMiAALIi381s4MAqlkqlaEgABVc82kckvwf/eq3GhDoqg/t9TkyH77v/uZP//7tbyDGCg4bLE7Bi5PTNBBATvTHmxsEMB7WVc0Uk/y0xyencOpf1fJeNRYEUOe6DW41CGBwiIuYwIMAep8nIPDSwwbBNIM1sfpvNSwrfrGXw5Jzm5ubVj87++/s7GT1fxCnRhzUAzxrAnjYMCJk10xl9d9qe1b8QADW5cvb3yN4rAngYUNOFK3+W23Pip+UAEJOSpXBkSNHOodYLpdZ/bcuXu7+HuBZE8DDhpw4Wv232p4VPxCAdfny9vcIHmsCeNiQE0Wr/1bbs+I3NAGEKj+BBgVgCx+P4LEmgIcNNhRsva3+22ZfLLLiBwKwLl/e/h7BY00ADxtyomj132p7VvysBBBzPnZGgKsAMQT7P/cIHmsCeNhgQ8HW2+q/bfbKFUDMeRBADCHb5x7JZ00ADxtsKNh6W/23zQ4CqD1+rOtv6u8BnjUBPGwwgWDsbPXfOH3dZwAx56EAYgjZPvdIPmsCeNhgQ8HW2+q/bfbKFUAswXEVwBoeOAMYFsHFAgRgQBgEYADPoatH9bUmgIcNDlAkD2H1P3niKx2z4me9ChAjAAIHdwJaw6S7v0fwWBPAw4Zh0JGNavVfNku4VVb8QADW5cvb3yN4rAngYUNOFK3+W23Pip+UAKSVXgsG7gPQItZu7xE81gTwsMGGgq231X/b7JUcAoIArMs8TP/d5MsdwIN4dvyp55pxY8+u27p0aZD5cw8q9f/ib3+b29Rm/thpPzfSi1DmriBAACAAEEARCOQxYnQC2NjYaHm6XC5dPefj0+AHTzzc/Pft8682P18783jzc2oKQOs/FMC8nygEApiYAgAB6OoJtgAjnwEMrQCOnT7bRMCFJx5pRQJ/h93UKj85q/V/ba2MM1icAeiIy6v15BSANgG8gCxlHK3/IABsAUa9ChCSqF5nAZQAtNenxKQ9/9UzgUOHmv96zVsaAUj9p7MRL/tffPA+01AxJWA9/b/jsTMt++b+7sLsCoBWwysRQQCrLRAIoJuHQABtXEYjgFDl9yYAGm/9wIGWp/xNNjes39J8fu7uO00Vq9TOUv+97af7LmhcrSLwVgA84Wndyb5zd99ZxiGI90IIxwMBgACEoSJrBgKQ4VRKq8kSAAf46MlTzZ/4WcBUrwZI/f/cE98fJBZTicBLAcQqPxTACgEQwMTuAwhlc4gAQQDYAoRixvXqgMMZgGmvdvTkqcYfUgB73l8/SAUsbVBOAOT/4U/dPoqppAhiZwJWBUCVn+/1Q07iDCC8/CCAUVJjnElAAN04gwD08ZdEDDEFQGZkeNNLkj962MbtQZdDadYXHri30wD61uDQ1o2lAL785LMqV0AAKriaxkkJAwLQA23pAQKQoQcCkOHU1UpEBAUnPvkk8iMdpjw9SyMAQoFfHfBGR7r3p3lBAOkrIEocEEA6wJaeIAAZeiAAGU59rXqJIEYAGfb83BcRkdlhGmcESnxeCany8rOAsc4AuPfeSkBb+aEAVgiYLq1dAREEME5ui2YBAYhgutoICkCH1+RaHzt9tkVgodPyUhzn0j5kV0gB8PaplbMUPKx2gACsCFbeHwSw+lLUXP+BAOa68lf8Pv7Ucy0FENqbjq0MQpU+VLG53TEFMPfKjzMAvzOAqikEBFD18pmNhwIwQ1j3AJwAQt4MpQy0lb5utMuzHgRQ3pqMahEIYFS4i5sMBFDckoxrkJQApFbFrm9j7y1Fcpx2IIBxcC52FhBAsUszimEggFFgLneS2gmAFIdWWaT2K3cl0ywDAaThNpleIADcBzCZYE5wxONW4IRpy+niRQC5rsOnVvLUfuWsnI8lUAA+OFY7CggACqDa4HUwfC2WAKFT7die8/xPXm6ZF3v2XGie99/oG6DXDaR5pBVV2k66tjHlwcfRtpfaUWu72SsAEIBP6EoTW9pOapU2obXtpXbU2g4EwO6Ft1YMCjD+aqrrD9/aDB1TDjxAtQrgnYuvN/PwfvT32Py1BbI2oVMVnTUuSu0/+3cDahVALIFAAONSCAhghXcsLkMENHsC0IarlDA4sPS1W75Q77LHiTx9j+1FDfzrvdy/jgU3PdAkhp/XS09j86R+zr+LEEsk652OpfUHASgjBwSgAwwE0MYLBKCLn6Fbq8/EQwSw55lzvWNyJQAFMPQS94/Pn9l48MTDnZKa1pe/2Ye/yae2/js7O+ocyLtivrOrnQcB6BagdAUAAgAB6CJ6eq1FTwWOPd2Yw1J64pO93K/Nzc3mI/523QtPPNL8nT6n/lwB1NYfCmB6Ca31CASwB7HaEthKQCAAbbpMr72IAEIVs9bKz/3hlZ1X+Kl+DgKYXkJrPQIBdEh7EIA2jOpsrz4ErNPNXqtBACCACYa1zCUQgPJtx7HDwFoO/7AFWCGALYCMKKbcSqQAYolf61kA+TXVPT4dEob8AwFMObVlvoEAsAWQRcoEW/0/YJEJF9RFh/0AAAAASUVORK5CYII="}, {"name": "pasted", "offset": [96, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 64, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAgCAYAAACinX6EAAAAAXNSR0IArs4c6QAAAZVJREFUaENjZKAiEJaQ/w8zjo2DC6fJv358Q5FbnyIF53NxMOHU9+3HPxQ5u5bjjJQ6n2IDkB0wGgB4UgByivjy4Q1KxO3MkUHwGblxR+r/ryhyJjVHKY5Aig0gNgWMBgBSmTCaAoZrFghevh1eC5BSOlfebRkeZcBoAIz0FHCmxRqeBbg4eXE3aL5/RpVDqvpIaQiZVO+iuBaj2ABkn4wGwGgKGOFZ4FCNJaIMIKFTg5Lvh3JTeDQAhnIKQO7KotdfDv0z4EIorTYGBgb0PjpMISnVGbJ96PralWvg0gcKM3BWrbVKpLQ9kRqeMOZoACD15UdTAFoIjIgswCsgirMn5zZ9EaIMuNePGjxoozRwSRKqMxQD0fS1KxXCpXdlxuHM6M1auMcg8ZUO8KbwaACM9BSA3JFhQEuGIgIs8FSEXu0RWw0iV29vPvzBmSrRq0G8+pCy35FtT8iqB+FZYDQAkHpyoylgJGYBYjsy6KmDgcbVIE7z0Zrh5/Y9o6wMGA0AIntywzYFkJV+hoEmqg6KDsXwAABrN2PgEINvLAAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/recalibrated/r_kineceleran_mask.png", "source": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAGydJREFUeF7tXV2MXddVvmOoKaGVHXkaJ5EhcglS4xF/pn2oohTiIqQgQKh+aYKEUFIJo5JIdkCirvtE2lSiwShWwDzYQkjEvLigFMWiah3aEOWhUH6qO0Ei1GqIkjiMFbuEUFzwoDl31uSe5bvPXmuvdc4++5zPL+OZs3/W+tba3/723udnaZL539LS0npmE7J2v76+vuRlwK6bb9vCcvs7bwg2e/U7b9Wu/cXHbt36/YZ3bgvWe+s712rXPvTI82bbc8ffE3+vOHbZjjmAVmNzJ4DVfmt9zwQEAeij4Ym/vvf8NUAAmWPgmYBNBDCvCN68vFbz+q9/c8/bvy/9QBiR9f+qXXv/sefM+ZN7AvDEP3MqJXVvDmBSr3OVcieA1X5rfc8EBAHoo+GJv773/DVAAJlj4JmAIAB9MD3x1/eevwYIIHMMPBMQBKAPpif++t7z1wABZI6BZwIePHMu6UTlE//2CPYAMudBru5BALmQ3+wXBJD3GNgT/8yplNQ9CCAJNr9KngkIBaCPiyf++t7z1wABZI6BZwL+3SN3bi0Bbvj+dwc9e+u//7N+be7oT3Mj0Ps/+UVz/uQ+BfLEP3MqJXVvDmBSr3OVcieA1X5rfc8EBAHoo+GJv773/DVAAJlj4JmAIAB9MD3x1/eevwYIIHMMPBMQBKAPpif++t7z1wABZI6BZwJ+9dgH394DUDzUU1v341bgzBnRbfcggG7xvq43EEDeY8ClpdkQuHbtWqdjYdu2bRVZd90vT8BOnV401rAJ6Pc4MBSAns1BAHrMXGuAAGYEMH8bLwf4Z46f3PpT7a69yWTCn9GngprjvPn+eL1Hf/jY1uW/OXwoGPtPvTctLQ4//1JaRadaRADUXNszMs38XfUXgwkKIIZQy9dpCQACaBnoQPMggDy4b/UKBQAFkDMFQQA50Z9MJiAAEEDOFAQB5EQfBDChJcC7d74n+CTfz/3Rn769B/DN4/WIsbf0vC2t5G/2qTXIjgEffe/hrctf/I1fDWbL7+4Lv4OwKcWwB9Dt6QOPBfYAMhMgCACbgDlTEASQE/3JBAoApwBZx2DWzjfGHvYAZnsA87fxTpgMX975vVs0xY/9pMeA88d7a5f/N0h7/Biwsd7c8uNvn345iUqxBMASIOktNknZ1sNKtAQAAeQJDt8EbOt8np//t9WPFkUoAC1izuVBAP3aA2hrYIIAwjdiQAFgCeBMq/LmQgrAiwhCA9+rfbmni0tCAVgRNNYnBSC9j5/vD0xaPgYMts9uQ/76+VeSkOjbHgB3wnprMAggkhbYBJxtAoIAkvij80r79u2r9bm6uqqygRTH+vpM+Ho+DaoyZLMwFEAKao51oADy7gFoQwkC0CIGBdCIQO4ZwDmc6uZKU4BWAuAA5Y4/FIA6ZX0r5E4AX2/0rYEA/N4HoUe/ug8n7xtZUoxGHSAwVARipxJWv2nvgdoBAVgRRX0g4IgACMARTDQFBEpDAARQWsRgLxBwRKBzAlhZWWm8E097zhnDYnn3TVWRtYuvVz/vfvxUrcrlCy/Wfv+H44/Gmmy8/pOHP1G7vnPv7bXfn3nogep3bpep07nKfNeYt+uNr9bu3PbFEp6vWbX+td1+zJ4YvtPpNOtG/BIIAATQlMRtE1TbA7Tt9osnADoFiDFV6sxFMyvV/8H7ZgOOZuLnfuvXa03f+bk/blQE0yd+r7p+9Wr9kdbt22ePzK58/LcbZ/xQf6Q8/v3JuiIhpWINdCp+sX6t1+kYMjYReNkfG5BSfxbsZkurNpZLVRza8ZN75icQtk4BtA5IZwYQwOJ8k+LnktUNjYAA6uCMlgB4jlgJgQY+zfjUPs38Ny7fUv3pr37tl6qfv/PsbdXPz971rernL/zJU9XPN9ZerX7ymZvP9KQMqB9SEtp++B4EKQKuBKz4tD2wpe2HbkTyUgTSGf/DZ/+yZvKXD/6y1AVRudT2Q4SgjX9fZnwOVvA+AK2DfEYDAdSh7suMzxMABNDMH6MlAKsieP3SWtUEX/PTjEztcwVAf+dKgMrR9ZgCIAUhbZ/aJcXB9wRu2rUsmmmoUF8HvJQAeDmtInjhhRdUeNEMffPKSq3ea9Np9btWEXi3d8cdd6j86euML1YAIIDZcSQtAUAAzcfFPF9AAHmP96Rspb4VWLo0sCoArgTod743QH/na336O+0tcEC4wvBSAKXM/OSv9mEkqRLQEsCv/Mu/NuasVAmEZn7e+J+970ekY6QqJ1UApcz85DwIYHOzEQQgGw8ggGacBk8A5L5WCfzoJ2d39IX2AKjdw1+YfWb2+3b+Xw3p0IzNwxFa8//P5e+pih7/xW/WqpCi4ATwjU/P7iCUSv/SZv5UBUD1vIkgpgBiM7i1fmhYD3XmT1YAIIDFqQICWIyLdClgHcDW+iAAmQLcKkXnuzGGpL2AkAKgBvkuP1cCqQpAOvOnKgBK8NQbSJSwuxfX7gGQAfSyy1j8pQRA7WoHshYQ77U/+Wd9eajWD6/y6j2AOelQ/TeWACAAr1C10w4IoBnXWH6PlgDaIoKQIqC/8zU7Dx9XEtJ6dP4vXfuXPvNb9wDaVgI8rlploJ3peX9DH/jmPQAQwOxGl1KlPwhg3DO/GwFIiUC6FGhH6MZblSqAocz8XgQgVQLavYBQxGJKwDrzU78hBVC65Oe4Ju8BLGiocU8ABBAnoRwlUvcAuK2xTUEQQI7oxvt0IwCuBEJM2jciiM38PHFLl/w8JbwIgCuBUPytRNC2AuAzP7e31N3+EBWAADYfNw5t/oEA4rPIfAn+LbzYgNK1PpmAALSINZd3J4DQ0oD/fddN76n+FLs/gOqFdvdD7sVOC/jMf+n1/1jY1NBm/LYVQGhpYE1bPvDp2QBqlz9F6LUXMLQZv7U9gAaJsfASCMA6JHzqey8BQAA+cemqldYVQGhvgP4eIwLtzM+Bi93rP9aZn3BqmwBCewOxBKen+q79+E9URbf90z9WP2PvBUitx+0Z+sw/Ny67+TRY6NVQIIDYUGj3OghgMb4gAOe8CxEArbHpFWK0J8DfARh6DwA3M/ReAHpHIG320Tv+YnY5w9C75nITAMWfxyH0Dj++qcgfwqJ2Ut8BSAECATinamyggQCcARc2BwKAAmj8MpAwj9TFiBD4Lju9358ajH0ngHfMv/zDlQT/nkDIDrVDhVboigDm1pxVvoVwj71FWKoAtOGgPOwaD62d3uU72wTkhoMAvEOZ1l7XCU8fogEBpMXLu5bHd8myKIg5IDx8SMaVEjq5gcIrdk0gCyaShfknfWOV9UUuuf23po/H4AEBWKNQcP3cAyBEwCAAWVKBAGQ4BUtBAax75FByFKTvJtR2IFUGuQlQ69d1CsrawMYj8Q5tWJrImoAgABCAJXlz1/UYPCAARRRff+WVxtI33XqrorX8RXPPgG0pAEI2pgRy+2/NABCAEUGtAgABGAFn1UEANjxBADb8Ns6zRQooNvC5GaUogdwzoJUAYjN8bDOxtA+BYA/AOOClx1C8HAjAGfjN5kAANlyhAGz4RRWAduCXpgSgAMr4CGgozUEAIAATAiAAEIBoDWzKsubKHiSWbF5sDwAKIBlaUUXrEiDWSWyPIDcBxuyPXfcYPCCABpRBALEUtF0HAdjwAwHY8MMewDpuBDKmUNbqIAAj/FgCgACMKZS1OgjACD8IIC8B4GEgWwKDAGz4YQmQeQkAArAl8AYB5N7EM3mw/8jRqv5Lp56ofq5duaJqL/YGGlVjCwpfu3bN1MS2bdtM9UupHLvjjvsR250vxe/cpwgggCUPERRONxCAbCiCAGQ4eZcqngCsgLSlAOj4b/nmm00mrr32WlW/lGcDUp0FAaQiZ6sHAmhJAYAAdIkJAtDh5VUaBOBMAKEbf7RKgGZ+HuihKgEQgNeQ1rUDAgAB6DKmpdIggJaAjTQLAnAmgNCmH5/RuSKIXac4Dv1UIEYEXrv/dz9+qpUR98xDD6jaxSmACi7/wt6bgCAAW4xAADb8tLWLVwDLO3bUfN69Z0/1+3Q6FWHRFQFwY6QzPq8HBbAqiisvxGd8+oZkUmMNld5Ye7V2NaYIoACMEQABGAHsWfW2FAAIYHGgi1cAB06crnlGzP6VQx+t/h67MzCXAvjUH/zh5Etf+PyW7c9/+UuioQgFoFMAXQ18HjypEoACEKV9uFBJBLB9+3aRt1evXg2WAwGAAERJJCxUvALgzwJwv2lPgP+d9gi6VABEAEefu21heD5z57eqv4MAwtkrPQWgmb+ttb5wfG0VI0XA9wSgALRIsvIgACOAPavutQcAApAFtngFwDcByW0+84dOBbpSAB/88M9O/v7Zr25FhasAmv03CvzUXR+ahPYEsASQLQFAACCAGgJ9I4AN44gE5gc/CGBfY+ZiCSAb2NJSxSuAkKN8c/D8g/cvLJpLAYAAZuFo6468vqz9edLx04HzD97f7vPoESYAAXR0KzCWAIszEQQAApCqFVE5mvn5DEDMy5VAVwpgw3icAlwfQm8C6OvMH1ICUACiYS0vBAKQY9WHkiCAzArgwInTtXcChtbKfUiWDRv42j5kV0gB8PKxe7W1fje9Agw3Al2P5keefFoL8aDKn733nrx7ACAA3eObseyTvgNwY09g/t9YbwUGAWQmgINnztUUAN+lpCTtWhmEZvrQGo/bHVMAdP3z9/18bEyrrvM3AmnfBMQ7408NDu2NQCAAEMDCAQYCmMECAlDxb3GFsy8BuAIIIdi2Mgg91CONaF8VALc/pghC7wKkdqAApBlRRjkQwGacQACLZ3yexiCAMga21MpiCEDqEJULKQa63pfz2rb3ALS4xcqDAGIIlXUdBJA5XiCAvAHAJmDPNgG90qErBUD9aBVF6PlsL/9D3wdIbX9oMz/hAAIAAaSOiaoeCMAEX/bKIICBEUDqbnxqJvaVAMgfqxIY6swPBTBDYHB7ACCAOpWBAJqpHQpgoAogtiZPnbl5OsUIJ1be+1kA3h8IAATQhMBgFQAIYBZ2EAAIYFQEkLqWT60HBZCKXD/qpb67L3TKFJt4QoowtR61l1o/+/sApLcC9yNdrrcCBNDXyMjsAgFkfh/AZDKpPQ3Iw7aysiKLZKCU9Bt9pk4Mlb3fCIQ9AF0wtF/uid1fIp2RUxVErP+YEijunYAgAF1CgwB0eIEAoAB0GeNcGgrAGVBlc/xDILsPPVy1EHqfAz+1CX1IRNqOd//Sfgmm3n8ZCApAmdGsOE4BmvHzHoDUm3Qgevcv7bcYAiBDtUTQ97U/+dW2AqB+tEQw9DsACRc+AOnDH3xpcPHkY1UV/mGQkAKQtuPdv7RfEIBtYnWrDQJwgzKpIe8BSEZIB6J3/9J+iyMAqRIoZebvWgFIlcBYZn6uAEKf/KIBGrvOBz5no1A70vZj/ade7/0eAAcythQAATRPhLGlAAigjp90gIIAkgTYRP1pMBBAGtCotRgB6QCPzbAggLQMAwE4fxswLQzjrQUCWM/7YZDYnYDStT9P4VKWAl1tAo53iDd7DgIAAWQdGyCArPBPQAD5CcCaAY3PEggazyqBBPahCBAYLAIegw8EMNj0gGNDRwAEMPQIwz8g0IAACADpAQRGjAAIYMTBh+tAAASAHAACI0YABDDi4MN1IAACQA4AgREjAAIYcfDhOhAAASAHgMCIEQABjDj4cB0IgACQA0BgxAiAAEYcfLgOBEAAyAEgMGIEQAAjDj5cBwIgAOQAEBgxAupXgvUNq/1HjlYmvXTqiern2pUrWhM9SFDbJ8oDgV4gAAKYVC9GxT8gMEoEzATA3xJcyrsA56INAhhl6sPpDQRAAFAAGAkjRgAEAAIYcfrDdRAACACjYMQIJBNA6AtB2AMYcTbB9eIQAAFAARSXtDDYDwF3AiDTulICyzt21NDYvWdP9buif5wC+OUTWioMARAAFEBhKQtzPREongAOnDhdw+PG5Vuq379y6KPVT8GdgVAAnhmFtopCAAQABVBUwsJYXwSaZr/GT36FTgFa3ANYaOv+I0crO+lZAA4P7Qnwv0+nU8z8vrmE1gpEAARQYNBgMhDwQkA9C66srIg+BqrYhee+qGxa3rFjoT185seM75UyaGdICKgG24bjIIAhhR++jB0BNQEQYC0QQbItYw8i/AcCqQgkDzoQQCrkqAcE+oNAMgFIlYBgL8BsQ3/ghCVAoCwEzIMvpgRAAGUlBKwdFwJmAhgXXPAWCAwLARDAsOIJb4CACgEQgAouFAYCw0IABDCseMIbIKBCAASggguFgcCwEAABDCue8AYIqBAAAajgQmEgMCwEQADDiie8AQIqBEAAKrhQGAgMCwEQwLDiCW+AgAoBEIAKLhQGAsNCAAQwrHjCGyCgQmDp4JlzjW/4eWPt1YUN0tt3Q71d+NqztUt7P3BXo2Ghft61a/aWX69/72CUd/bee0CCBnDpYbCLL79ctbJ25QrwNODZdVUQAAjAlHMgABN82SurCSA289NMfvnCizXndu69vfpdWp8qaxXAm5dmioXXo7/z/qEA0nKQD3zeCpRAGq5d1wIBQAEk5RwIIAm23lVSr9ekewbnH7y/1vaBE6ervQY+A3+X7UA8dZ9tTU79hJDmdvUuIoUYFCMAcgNKoN8BBQH0Oz69tQ4E0NvQqAxzIwBa+8dmWK4EoABU8cpeOKYA6ZuMUADZQyUyAAQgggmFCAEQwLByQU0Aw3If3kgRCL38lc7/qZ0fe+R49d9/Pna4+ok9ACnCecqBAPLgXlyvIIDiQiYyGAQgggmFuPTna32OECmB2J4QkM2LAAggL/7F9A4CKCZUKkNBACq4xlM4JPnfd+xzFQh06kNrfY7MDz3w8epPX//9zyDHepw2CE6Pg5PTNBBATvS76xsE0B3WRfUUk/y0xiensOtfVHi3jAUBlBm31q0GAbQOcS868CCAxvcJCLz0sEHQTWtFrP5bDcuKX+zjsOTc6uqq1c+F9dfX17P634pTHTbqAZ51AHjY0CFk13Vl9d9qe1b8QADW8OWt75E81gHgYUNOFK3+W23Pip+UAEJOSpXBvn37FjYxnU6z+m8NXu76HuBZB4CHDTlxtPpvtT0rfiAAa/jy1vdIHusA8LAhJ4pW/622Z8WvbQIIzfwEGhSALX08ksc6ADxssKFgq23139b7ZJIVPxCANXx563skj3UAeNiQE0Wr/1bbs+JnJYCY87E9ApwCxBBsvu6RPNYB4GGDDQVbbav/tt4LVwAx50EAMYRs1z0Gn3UAeNhgQ8FW2+q/rXcQQOn5Y42/qb4HeNYB4GGDCQRjZav/xu7L3gOIOQ8FEEPIdt1j8FkHgIcNNhRsta3+23ovXAHEBjhOAazpgT2AdhGcTEAABoRBAAbwHKp6zL7WAeBhgwMUyU1Y/U/ueLNiVvyspwAxAiBwcCegNU0W1/dIHusA8LChHXRkrVr9l/USLpUVPxCANXx563skj3UAeNiQE0Wr/1bbs+InJQDpTK8FA/cBaBGrl/dIHusA8LDBhoKtttV/W++FbAKCAKxhbqf+xuDLncCteHbwzLmq3di769auXGml/9yNSv2/9O1v5za16j+228+N9CKUsSsIEAAIAATQCwTyGNE5AaysrNQ8nU6nrp7z9qnx3Ycerv57+cKL1c+XTj1R/RyaAtD6DwUw7jcKgQAGpgBAALr5BEuAjvcA2lYAB06crjLg4snHapnAv2E3tJmfnNX6v7TUjz1Y7AHoiMur9OAUgHYAeAHZl3a0/oMAsATo9BQgJFG99gJoANBanwYmrfm39gT27Kn+69Vv3whA6j/tjXjZ/8xDD5iaiikB6+7/3Y+fqtk39m8XZlcAFA2vgQgCmC2BQACLeQgEUMelMwIIzfzeBEDtLe/YUfOUf8nmxuVbqutn773HNGP1tbLUf2/76b4LalerCLwVAB/wFHey7+y99/RjE8Q7EML2QAAgAGGqyIqBAGQ49aXUYAmAA7z/yNHqT3wvYKinAVL/f/rkn7eSi6lE4KUAYjM/FMAMARDAwO4DCI3mEAGCALAECOWM6+mAwx6Aaa22/8jRyh9SAHPfr29lBuxbo5wAyP+9H7irE1NJEcT2BKwKgGZ+vtYPOYk9gHD4QQCdDI1uOgEBLMYZBKDPvyRiiCkAMiPDl16S/NHD1m0NOg6lXs8/eP9CA+ipwbat60oBfOTJp1WugABUcFWFkwYMCEAPtKUGCECGHghAhtOiUiIi6PHAJ59EfqTDlKdm3wiAUOCnA97oSNf+1C8IID0CooEDAkgH2FITBCBDDwQgw6mpVCMRxAggw5qf+yIiMjtM3bRAA5/PhDTz8r2ArvYAuPfeSkA780MBzBAwHa1tgggC6GZsi3oBAYhg2ioEBaDDa3ClD5w4XSOw0G55Xxzn0j5kV0gB8PKpM2df8LDaAQKwIlh4fRDA7KGosf4DAYw18pt+HzxzrqYAQmvTrpVBaKYPzdjc7pgCGPvMjz0Avz2AoikEBFB0+MzGQwGYISy7AU4AIW/aUgbamb5stPtnPQigfzHp1CIQQKdw964zEEDvQtKtQVICkFoVO9/G2luKZDflQADd4NzbXkAAvQ1NJ4aBADqBub+dlE4ApDi0yiK1Xn8jmWYZCCANt8HUAgHgPoDBJHOCIx63Aid0258qXgSQ6xw+dSZPrdefyPlYAgXgg2OxrYAAoACKTV4Hw5diAyC0qx1bc1742rM182Lvngv1865dvgn6jpY0j3RGlZaTxjamPHg72vJSO0otN3oFAALwSV3pwJaWk1qlHdDa8lI7Si0HAmD3wltnDEow/mmqnXtvr5qOKQeeoFoF8OalV6t+eD36e6z/0hJZO6BTFZ01L/paf/TfBtQqgNgAAgF0SyEggBnesbwMEdDoCUCbrlLC4MDSY7c8UN9lrxN56j7bhxr4473cvwUBN73QJIaf10dPY/2kXufPIsQGkvVOx77VBwEoMwcEoAMMBFDHCwSgy5+2S6v3xEMEMPfOucY2uRKAAmg7xM3t83c27j708EJJTfHlX/bhX/Iprf76+rp6DOSNmG/vaudBALoA9F0BgABAALqMHl5p0VuBY2835rD0feCTvdyv1dXV6hL/uu7Fk49Vf6frVJ8rgNLqQwEMb0BrPQIBzCFW2gC2EhAIQDtchldeRAChGbPUmZ/7w2d2PsMP9ToIYHgDWusRCGCBtAcBaNOozPLqTcAy3Wy0GgQAAhhgWstcAgEov3Yc2wwsZfMPS4AZAlgCyIhiyKVECiA28EvdCyC/hrrGp03CkH8ggCEPbZlvIAAsAWSZMsBS/w9k6nwm3iw2fAAAAABJRU5ErkJggg=="}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\kinecelerank.png", "name": "kinecelerank.png", "folder": "", "namespace": "", "id": "6", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "9b14ffb2-f383-e17c-f8d8-b0eb640e4c7b", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [96, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 64, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAgCAYAAACinX6EAAAAAXNSR0IArs4c6QAAAZVJREFUaENjZKAiEJaQ/w8zjo2DC6fJv358Q5FbnyIF53NxMOHU9+3HPxQ5u5bjjJQ6n2IDkB0wGgB4UgByivjy4Q1KxO3MkUHwGblxR+r/ryhyJjVHKY5Aig0gNgWMBgBSmTCaAoZrFghevh1eC5BSOlfebRkeZcBoAIz0FHCmxRqeBbg4eXE3aL5/RpVDqvpIaQiZVO+iuBaj2ABkn4wGwGgKGOFZ4FCNJaIMIKFTg5Lvh3JTeDQAhnIKQO7KotdfDv0z4EIorTYGBgb0PjpMISnVGbJ96PralWvg0gcKM3BWrbVKpLQ9kRqeMOZoACD15UdTAFoIjIgswCsgirMn5zZ9EaIMuNePGjxoozRwSRKqMxQD0fS1KxXCpXdlxuHM6M1auMcg8ZUO8KbwaACM9BSA3JFhQEuGIgIs8FSEXu0RWw0iV29vPvzBmSrRq0G8+pCy35FtT8iqB+FZYDQAkHpyoylgJGYBYjsy6KmDgcbVIE7z0Zrh5/Y9o6wMGA0AIntywzYFkJV+hoEmqg6KDsXwAABrN2PgEINvLAAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/kinecelerank.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\kineceleran.png", "name": "kineceleran.png", "folder": "", "namespace": "", "id": "7", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "355e358e-2a3a-6343-80d3-c17f4e0818a0", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [96, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 64, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAgCAYAAACinX6EAAAAAXNSR0IArs4c6QAAAZVJREFUaENjZKAiEJaQ/w8zjo2DC6fJv358Q5FbnyIF53NxMOHU9+3HPxQ5u5bjjJQ6n2IDkB0wGgB4UgByivjy4Q1KxO3MkUHwGblxR+r/ryhyJjVHKY5Aig0gNgWMBgBSmTCaAoZrFghevh1eC5BSOlfebRkeZcBoAIz0FHCmxRqeBbg4eXE3aL5/RpVDqvpIaQiZVO+iuBaj2ABkn4wGwGgKGOFZ4FCNJaIMIKFTg5Lvh3JTeDQAhnIKQO7KotdfDv0z4EIorTYGBgb0PjpMISnVGbJ96PralWvg0gcKM3BWrbVKpLQ9kRqeMOZoACD15UdTAFoIjIgswCsgirMn5zZ9EaIMuNePGjxoozRwSRKqMxQD0fS1KxXCpXdlxuHM6M1auMcg8ZUO8KbwaACM9BSA3JFhQEuGIgIs8FSEXu0RWw0iV29vPvzBmSrRq0G8+pCy35FtT8iqB+FZYDQAkHpyoylgJGYBYjsy6KmDgcbVIE7z0Zrh5/Y9o6wMGA0AIntywzYFkJV+hoEmqg6KDsXwAABrN2PgEINvLAAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/kineceleran.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\kineceleran_maskk.png", "name": "kineceleran_maskk.png", "folder": "", "namespace": "", "id": "8", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "4f8b3716-9563-16db-ea40-df12e04028b6", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [96, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 64, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAgCAYAAACinX6EAAAAAXNSR0IArs4c6QAAAZVJREFUaENjZKAiEJaQ/w8zjo2DC6fJv358Q5FbnyIF53NxMOHU9+3HPxQ5u5bjjJQ6n2IDkB0wGgB4UgByivjy4Q1KxO3MkUHwGblxR+r/ryhyJjVHKY5Aig0gNgWMBgBSmTCaAoZrFghevh1eC5BSOlfebRkeZcBoAIz0FHCmxRqeBbg4eXE3aL5/RpVDqvpIaQiZVO+iuBaj2ABkn4wGwGgKGOFZ4FCNJaIMIKFTg5Lvh3JTeDQAhnIKQO7KotdfDv0z4EIorTYGBgb0PjpMISnVGbJ96PralWvg0gcKM3BWrbVKpLQ9kRqeMOZoACD15UdTAFoIjIgswCsgirMn5zZ9EaIMuNePGjxoozRwSRKqMxQD0fS1KxXCpXdlxuHM6M1auMcg8ZUO8KbwaACM9BSA3JFhQEuGIgIs8FSEXu0RWw0iV29vPvzBmSrRq0G8+pCy35FtT8iqB+FZYDQAkHpyoylgJGYBYjsy6KmDgcbVIE7z0Zrh5/Y9o6wMGA0AIntywzYFkJV+hoEmqg6KDsXwAABrN2PgEINvLAAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/kineceleran_maskk.png", "source": "data:image/png;base64,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****************************/NwIYG3b0JUAaQdgsPKDACIfB44iBl8H0LkaoFrpI2o2yp+IeSCyTriBOw8tNgEJXHQAYWkWU2xRBQMCCAvMUke7bhSKIYGhZwpAAGGZE0MAR2QrmSrjwg/yQ+IrxhSJgKYGinS4a7TGeVEnAAIoPkeW7oCmBorHxsL5USLwEUAGX3cVEVnxkYYDLgQsaqBYdC2cBwEUG34YHrkRvhjgLAigdDDQAZQeQZ39VddA1c4f5g0IQFdApUtXXQNVOw8CKL12Teyvugaqdh4EYFJApSupugaqdh4EUHrtmthfdQ1U7TwIwKSASldSdQ1U7TwIoPTaNbG/6hqo2nkQgEkBla6k6hqo2nkQQOm1a2J/1TVQtfMgAJMCKl1J1TVQtfMggKZxvUa89KqW2o9Pg0mRWu64qu8EBAFsVb0Irp2vugCWy2syz0AAIAAQgKxWFjkKBAACAAEssrRlToEAQAAgAFmtLHIUCAAEEJrYUsLgmyuxctb2Bdnle6ORz7jcvz4MAgAB+HKYH48t5Fg5a/tAAB1EQQAggKkLLJRAtJdlfEQDAuhEhHc4vo6Fv9abf8ijNPnVaqXNt9D6yWp8jPOhBQYCyCrkfWNAACCAjNNzFtN8hNYaEboX4FsJZ/FMMAn3a3d3t5XiH9igcXScVPMOoDR5dACCJFn4EBBAJ8ClFbCWgEAAC69ugXsiAiA9vk6glJWf+8NXdr7CL/U4CEBQIQsfAgJommapBU4dgss/EMDCq1vgHggABCBIk2UOibkKsDQkQAAggKXltNgfEIDwaUjfuT9HvJS9ANfuPvYAxDVU9EAQAAigTWDsARRdx9HG/x9cl6r5XK6HTAAAAABJRU5ErkJggg=="}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\kineceleran_mask.png", "name": "kineceleran_mask.png", "folder": "", "namespace": "", "id": "9", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "ddc7f4d7-88ca-7e60-6dbe-b5de42c31159", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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********************************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"}, {"name": "pasted", "offset": [96, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 64, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAgCAYAAACinX6EAAAAAXNSR0IArs4c6QAAAZVJREFUaENjZKAiEJaQ/w8zjo2DC6fJv358Q5FbnyIF53NxMOHU9+3HPxQ5u5bjjJQ6n2IDkB0wGgB4UgByivjy4Q1KxO3MkUHwGblxR+r/ryhyJjVHKY5Aig0gNgWMBgBSmTCaAoZrFghevh1eC5BSOlfebRkeZcBoAIz0FHCmxRqeBbg4eXE3aL5/RpVDqvpIaQiZVO+iuBaj2ABkn4wGwGgKGOFZ4FCNJaIMIKFTg5Lvh3JTeDQAhnIKQO7KotdfDv0z4EIorTYGBgb0PjpMISnVGbJ96PralWvg0gcKM3BWrbVKpLQ9kRqeMOZoACD15UdTAFoIjIgswCsgirMn5zZ9EaIMuNePGjxoozRwSRKqMxQD0fS1KxXCpXdlxuHM6M1auMcg8ZUO8KbwaACM9BSA3JFhQEuGIgIs8FSEXu0RWw0iV29vPvzBmSrRq0G8+pCy35FtT8iqB+FZYDQAkHpyoylgJGYBYjsy6KmDgcbVIE7z0Zrh5/Y9o6wMGA0AIntywzYFkJV+hoEmqg6KDsXwAABrN2PgEINvLAAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/kineceleran_mask.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\5yl\\kineceleran.png", "name": "kineceleran.png", "folder": "", "namespace": "", "id": "10", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "222dd592-1eb1-729f-fa97-abe93250e8f0", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [96, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 64, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAgCAYAAACinX6EAAAAAXNSR0IArs4c6QAAAZVJREFUaENjZKAiEJaQ/w8zjo2DC6fJv358Q5FbnyIF53NxMOHU9+3HPxQ5u5bjjJQ6n2IDkB0wGgB4UgByivjy4Q1KxO3MkUHwGblxR+r/ryhyJjVHKY5Aig0gNgWMBgBSmTCaAoZrFghevh1eC5BSOlfebRkeZcBoAIz0FHCmxRqeBbg4eXE3aL5/RpVDqvpIaQiZVO+iuBaj2ABkn4wGwGgKGOFZ4FCNJaIMIKFTg5Lvh3JTeDQAhnIKQO7KotdfDv0z4EIorTYGBgb0PjpMISnVGbJ96PralWvg0gcKM3BWrbVKpLQ9kRqeMOZoACD15UdTAFoIjIgswCsgirMn5zZ9EaIMuNePGjxoozRwSRKqMxQD0fS1KxXCpXdlxuHM6M1auMcg8ZUO8KbwaACM9BSA3JFhQEuGIgIs8FSEXu0RWw0iV29vPvzBmSrRq0G8+pCy35FtT8iqB+FZYDQAkHpyoylgJGYBYjsy6KmDgcbVIE7z0Zrh5/Y9o6wMGA0AIntywzYFkJV+hoEmqg6KDsXwAABrN2PgEINvLAAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/5yl/kineceleran.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\5yl\\kineceleran_visor.png", "name": "kineceleran_visor.png", "folder": "", "namespace": "", "id": "11", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "39ed35aa-b3e6-fbc8-35dc-9c06ce6537a4", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [96, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 64, "height": 32, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAgCAYAAACinX6EAAAAAXNSR0IArs4c6QAAAZVJREFUaENjZKAiEJaQ/w8zjo2DC6fJv358Q5FbnyIF53NxMOHU9+3HPxQ5u5bjjJQ6n2IDkB0wGgB4UgByivjy4Q1KxO3MkUHwGblxR+r/ryhyJjVHKY5Aig0gNgWMBgBSmTCaAoZrFghevh1eC5BSOlfebRkeZcBoAIz0FHCmxRqeBbg4eXE3aL5/RpVDqvpIaQiZVO+iuBaj2ABkn4wGwGgKGOFZ4FCNJaIMIKFTg5Lvh3JTeDQAhnIKQO7KotdfDv0z4EIorTYGBgb0PjpMISnVGbJ96PralWvg0gcKM3BWrbVKpLQ9kRqeMOZoACD15UdTAFoIjIgswCsgirMn5zZ9EaIMuNePGjxoozRwSRKqMxQD0fS1KxXCpXdlxuHM6M1auMcg8ZUO8KbwaACM9BSA3JFhQEuGIgIs8FSEXu0RWw0iV29vPvzBmSrRq0G8+pCy35FtT8iqB+FZYDQAkHpyoylgJGYBYjsy6KmDgcbVIE7z0Zrh5/Y9o6wMGA0AIntywzYFkJV+hoEmqg6KDsXwAABrN2PgEINvLAAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/5yl/kineceleran_visor.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_chronosapien_kineceleran.png", "name": "ov_chronosapien_kineceleran.png", "folder": "", "namespace": "", "id": "12", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "90585900-8abe-a0f7-4dea-eaae3775e189", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAAAXNSR0IArs4c6QAAG/hJREFUeF7tnVuoHtd1x0dKrEqyJRlbUhMMNqWlQZyQCCpoazWmBNctgZq+6angNtF5sAqN1BBf5EfJlza1W4jycOQUP1ovpThgWteYkESOC2pxQg4iTd2iYDeOJAtLx7pUluUy36f1nW+WZs/ea681s2dm//UivjP7+t9r//bal9mzpkj879nFTR/XFWHTretNSraw467GdO5dfHONSUY9SGRey/96112g3/hU9dmu7Z+d/WHj+rXOiJeuXK88u+/QD9Xaudq/KzkPLK2o69BVWdvIJ3nlAQC7ZgUA5FoCAHLNTGNwAFiN/LyQLk8gFw/ggZ2fmEny3+9+VB3J7/7c6u81t7rb9+OLlWe7njiuHkDgAZh2J3Fi6gYU58giAABaBVfjN3kAAEC9zvAA7OwvKiUy2rZGfp8nAA+gKO6DBxBlu2OI1BsPAADQmxM8ALmG8ADkmpnGgAdgKmftjoovhxOHdmMNwCfSSJ/DAxjRNmBRFACAsKPCAxAKZh081gPgq/rLJ98JKhqPN6Y1AAAgyAQqgQAAuWamMQAAOzlPHNo98wA2btjkTPjS5ZXqs7mtP8lBoF0HX1F7kNgGtGv/mJTUDRiT6XwcKQBoBH/jxFuVrH9n169Pfvs8gTF7AACA3BrhAcg1M40BANjJCQDItQQA5JqZxji6f1vQwtXKxSuVfPm2oe85RYYHUBSYAqyaEgBg2p3liQEAcs1cMb73xO+urgEIXuqpzPtxFNiuQQaQUvI1ACkAfAeGyBNwhRuzBwAAyHvc156fvt9w/fr1TvvC2rVrJ7DuOl+uUKeVrmseAEButPAA7DQDAOy0jEpJCgDKZGxrAE3bYQeWVrftKqf2iqLg7+iTPpLtvPmG4/F2PXF89vjZRffW4tqf3BHV/tc/ey4qnlUkAgCl1/aITCN/V/n5dBqcBwAAzB3bBQB89u19DgB4JWo3QKgHQKUY6zkAeADt2pkrdQAgje6zXAGAqRQAQBpDBADS6B4NAO4J0G/fCUBXvL68C/Dwg/V3I5bl/tZLc2sAhx+othi7pWf2ULCdV0mQxdt18JXZ44cfdK8B/OZZrAGEdCWsATCVpB4AAAAAhHS00DDwAEKVailcLABii9PXcwDwAGJbVBcPANDpp44NAEwlnD/HXzA3fOvtn5zpzLf9QrcB57f3zr5/zdlufBuwMd7c9OMHL78dZQvYBuz2ABJvpMFsA0ZZV02kvnoAAIBVC8vS4R4AxbY+D8Dn/m3lI6t9UQAAPbkRCACQmq5NeADARsfoVDAFwBQg2ngMIroAYDVCu0Z+q/S1EsAD6IkHEPoiD18fKFreBnSmz04h/sdr/xtli31bA+CV0E4FAACPWXTlAfT9y0AAQBQ/1JH+6ugH3jT+du9tszDfPn53JfyXd//cG38+AHkc3/jK9AtMqe8jyMYDAADmzNDlNZRB+AGihrDzOxBD9QAAABG/7AO37QHk9HVg+9ZpP8XUl4J2DQCuKDyAwCvBYk0RAIhVLo94UgBppwC9A4BUgDzMArWEAmkU8O1KWJdqDQBgLSnSgwLxCgAA8dohJhQYvAIAwOCbEBWAAvEKZAGAp17aWDz24KWJSo++UL3vn0v39EPr49UUpF+WqfxH5VJlOqDIfFGLF126zy2tum8VXDtFbTv9kPo2aby8vJx0K77zNQDe0QCAEBNqLwwA4L6IxUJ1n76jBwB1eBLz/LnqLbCP/eCeis5P/d6pRt0p/Jbnf1kJd/4rvzr5HRqfIvPwW+6o3mwT6hH4GtpVqbZHWJ8R0wi5sLAQ9IUmSi+23L4R2Vdees49g7bSDS2PtP1Td3yq1xppw9cJ0mQMAECzCcV2pFDD9IUDAKoKxU45sgZAKSE3ZOr4fMTnBsk9ANfI7PMUpM9d+fDykUfAPQFpg/fdA+Dlkw4MLpBZjcw+kLX13AUEafv3ZcTnOpl4AABAvPn1xQMAAOrbEACItO3T752dxIz1APgILR3haW7v8jCkHsD2O7dGKjGMKYCvclKPoK8jnq+erudjrb+ZB8CFAwDCTK2vHoDWIwAA0m7vhVlfUfQeALwi0lV+nxC+9GgNIHcPgHQMHQlzBcDQ6g0AeLYdAYAqQgGA5iEFAGD6+KYCfI7+f+9/YpLCr9z+Ua3SvhHbNed3petKT9rxU7vyPk/H9Tx2lT4XEIy9nq15AGRwAEBs1+wmHgDQrDMAoLRDHwAo+VBPQOoBNHkUTWmFegAnT56cVIHueFPK1Xn0WADQZZc7duxoLPPQXGLp4ie1v/by0M4b/kaGyT2AVADwgQQAaDZJAGCqDwBwQwTfSMA9Ad8JQN9zl3n64vme59LxSb9YD4DijxUEPtd/6B2f2s/EAyjFAABSOXG6fAGAev2yAUDsyw9cNrrIwAUC8gAefunmb8z73HGdiTfH5h7Btx5cmURw7fsPfc7P1dACINQTGNpagAsAYxn5Zx5AagCk7PylCADAismFFL6pAADQ5jAWn7b5hSD8SiPuEYTuCsRXKS6ma+5PxKdUh7ra71LFygPgngD95u3fdxDwkZ+3/1BX+13tDwDcUAYAiAMnj8W/hQcA2OjaVirmAHCtDfC/37l92+RPvrcFKZ7vrT6efujUgjr+e6fP1Go8thG/rTUAl4G6Po7Zt5F0KOW0BgEAcOMKMADA2rSm6Q2lYw2lnNat1DoAqMCu6459noB05A/1BHIf+Ukn6zWAoXkCuXZ8aicAIFPXHwAYlodiPfL3BgA0x+Z3CPpuAPIJ4orP7/hzeSZjn/v3BQBUjtA1Ab5Kz9/CDPVoXCO/tDw+O+z78+QeAACQ1kRCO4y2lFYdDgDQtkQ1fmcA4MWmkZePtHS/P4UPXc13ycI9Af49AVc5bGXub2pdAYAU0IJACwBt/v1tybiSAQDPX5wol4vLz80EAKgqEjoVietu/YulPgZ6dP820RdluAQrF5u/DeiTrGsD5uWxOkrtq2dfn6fWP3f709oFAKBUEACweZcgthkAgFjlpvEAAJ1+BQAAAChNKGl0AEApPwAAAChNKGl0AEApPwAwLgCcu3BNZBGHXrys7kOiDI0Dqwuf+xwMAAAAjPtkp8kBAEq5AQAAQGlCSaMDAEr5AQAAQGlCSaMDAEr5AYBxAUBqDqnPQUjLy8MDAEoFAQAAQGlCSaMDAEr5AQAAQGlCSaMDAEr5AQAAQGlCSaMDAEr5AYC0AHh9aafqXZQ3TrxVsQCcAxB2CJwD2KQyQKHcvQueehEMANCZBDwAnX54F2AJHoDShJJGHzwAfOq1PUJhCgAA+Gywz8/NLgTZdOv6qHpq7wPwZcrLxfOLLbcvXzxvR4GFHXeZJrx88h3T9KSJ7X3ujHoQluY5Hx4AiASXRnTEjVcAAIjXri7mmnXrblEtYm3esHGW7oXLl5ylO7Lv9tpnbXsAj77QfOOQq1y2MiM1KwVCAZB6ZA+tb3IPAACoB1NoAyJctwoAALZ6d+YB8GKT59DkNZRxnn4obm2B8oMHYGswqVPzASB05Lf2PGPXkrLxAACA1F1nHPkDALbtCA/AsTZhK/Nqav/8a/dMfnz9Dz5Vm8WPl/6traxHkW4sAKxHfJ+YoR4BPICGhcMxTgEAAF/XaX4OAOj047HhAbTsAVCHdzWbyxOg8PAIqspJAdD1yM/b2ecJwAMYuQcAANiOWACArZ6j9wBCTwLuO/K+qbJ//I3P1871//pf363k4/IAKBw9/8Ln/8W0fEisHwpcvfph2pOAXZ0DSLULAAD0w9BRinoFAICeTAGsPIDv/+gPJy3NR3A+8pM5hHoAFB6ewLhQAgAAALUWzQECAIyr41NtAICeAMDKvD63+NtBHsCOj/6sePU7/zjL9u+PbGgEAT38o/85ZVXUQaaDRUDbZhv9ImDXR4F9ACg7fvnvma9/tdKSv/WF+ya/OQj41AEAaH4dmB8FxjZgMzCyB4AVT0Pm/vOd//Hj0xOB9O/J3acKFwTqyoi1AKuWS5sOpgCeKUDa5gnPPQQA7/zTluLfv/+9gnd+DgHXdGC+NABAeNv0OSQAMBIA0L4/GRut7pMLT52fntd5APSs9ATu+pPzk5+uXQIAoM/dOrxsAECmAChNhCBQuv/z/wCA8A409JAAgAEAJLf6tHUnID/y6/MAtADI9R0B7ALYIq/3i4Ah1R0iALRTAACg3jKwCxDSY1bDJAfA43uuy0rMQvvetuKJcw/At00YWjjfGkDpEfzlvstBi4A0/5/Pm68FYA0gtGX6HS77KUBuACjNsW4nYH4bEADod6e1LF32APDdCSgV2zcdaMsDoHLSdqCr3KUXQBDgC3/lb9dBILwVKLWEYYTvHQCkl3A+eWztTGlJZw69FFTajH0HANWHQEC/fUeBAQCpJQwjfHIAaD9t1TcAULOHfofAag3A5QG4XuoJNU9+FPg7X/tRaFSEG4AC2QFg/kMiZftIvAZJewIAErUQNpUCgwcAjaChXwjirr90ysEbinYB+Pv8oQCQ7iKEGgp/KYji+e4ApHB4CaheaZwDCLXAsHDqbwNKAcCLBQDUNxQAAACEdWFdKDUAXCMy/d110w6N0NrXNV0eAF8LcOVjvQbA9Qi9G9DlIYx9v//q1Q91FsxiH92/rTY9rZ1JC+nyLPc+d4YnlfZOQO0iIADQbBoAQLM+AEABAEjpOh++7x4Ar5vvteHcVvmtAUB6c08gtQdQM/JTUQEAAGD1ElEAQGMNq3EBgDAd1e8ChGWzGoqvzmvJzOda0tt9fQeHpPVD+HYV8O0C3Lv4ZrsFUKbOwTT4LwNJ9QAApIoh/LwCAICtPXTmAYTuy0ur51ptDfUE4AFIFU8b3gcAeh24Yc6dpAKu3YlsPAAAIIndjS5TAMC2SVs/B+ArrvUagCs/Vz5tnQT01RvP4xQIBQBPXWtn0tKG2lVyD6DtcwA+4bQNEyo0AOBriWE8BwBs2wkewK3rbRVFaq0qIAXAqV980FieOzZ/stXy+gYoeAAXr6gawCcwJQ4PQCVzbyIDALZNAQ8AHoCtRbWcGgBgKzAAAADYWlTLqQEAtgKbA8C2eO2nFjqFaL8keebA5+j3fPq2ihD8+Ze++JnK85df+2nlN4/PVeXpYQ1gcdPHeZretNYAQNrWBwDOpH0Z6Oj+bVkDIK3535y7y8XlH7zoW7ljywMAAACxtjPKeAAApgBdGraF+6HyIJ5d3KSq74GlFYs6qMqAyOkU0Hqw2oNoQ7c/i84DAKSz/+xzBgB0JgAA6PRD7MQKAAC6BgAAdPohdmIFAABdAwAAOv0QO7EC1gA4d+GaqEaHXrxs0YdEeVoGtig81gAsWwRpiRQAAERy3RQYANDph9iJFQAAdA0AAOj0Q+zECgAAugYAAHT6IXZiBawBIK0OzgEUBdYApFaD8GYKAAA6KeEB6PRD7MQKAAC6BgAAdPohdmIFAABdAwAAOv0QO7ECry/tVE1B3zjxVqUGOAcgb1BVA+BlILngiLGqAACgswZ4ADr9EDuxAgCArgEAAJ1+iJ1YAQBA1wCDvxPw0Rem14pv3rBx8v/Z8+dFiminIKLMEPgmBbT76FoApL5paXTfBejaxgGArhW3zQ8ASHwlWKE8yLOwsFCxiOXlZVsLaTk1eAAtC+xJvi0ApB7ZQ1VN7gEAALoryUIbGuHqFQAAEnsA1h8H7drQDyytqLKM9QBSXScu/TAGiRN69x3Vi49MUjvhHZsO7PByWAMgdOQP1SPUuGLtIbkHIG3YUEG6CgcAVJV2dYBQgwcA4ix3sACInQLwuT/J1sUawPyoPWewUS0HD6AqWygA+MjNB5K+eQChAIwyoppIoUBI7gEMEQCkd9l5AYA0HgAA0IyKwQBg6FMA3gxSIHTtAew78n6j5RzZd3vj89RrAC4Pgc/x++IBdD3yu+zR1ajJPQAAIG4XIJTwvOEBgOnBLfrX9iIgANDsqZRHgaNe5nGtAXS5FlDmtW7dLbU1fPqh9ZO/+xYJ2/YAeIe/evXDxuPX69bdUmkP7hHAA7CapSOdUgEAIPLTZKEeAAAw7WhtbQOiG+sUGMy7AC5XkUZMGvFD52A099JOgVwgoI7vG/F9zUf1I09A6gHEusC+3QBeb8ondO5vNQXw6YfnnimAtgN0JTAAMF0cBAC6srg88pnMR48d3CpaB7hz89qZOu9duF6r1HyYJinvf+Q0fyx6RdkFMD5Cbd54cxX3HD6r9oBy8wCk3cLl+sMDkCrZTvgoAJRFoQ5eB4A9h8/OSvvqM9sbSw4ANDds36YAUjMEAKSKdRveHABl5z92cOukFiUkyg7eBIE5AIhGfpLJ5b2EeCD3P3La3AOwmvtzMyAQfPeb1bcveTh+FDh2DYDSdXk4fP/adTmnL3/tNmC33WV8uQEAi5tE0x9uAryDAABVhQCAfkMjGgBltco5dN0ILJwCRI38Lg8gZOSnuBYeAB8pAQAAoN9dvlq6VgBAWYR0xrITagTjAArJEwAIVxxTgHCthhhS1fmGWGGrMrt2H+iKMu3+v3QN4N7FN6Pa0rcNDABYWUw/04kymn5WpdtSAQDVm2ywCNit/Vnlpl4FtypIbDraVWTfCMjL5cqP0unKA3CN+Lwjhi7C+XTgnoBvFyA039h2RzwbBQAA4S4AADA1PADApgOmTiX7KYBv5HM1EI2I/Ay8tSfA33UgANF9+LTvT0eEY88BULqhJytjAcDz0XpwqTvQ0PMHAIQeADU4ANC8BuCaAgAA/UIGpgCRAHCtDXTtAfBywAPoVwfre2kAAABgYqOYAvS9q7ZTPkwBIgHgWwykKYLVhSCu9+3JLPiaAP1duhrf1RpAO+aMVKUKAAAAQMUGAABpFxp2+OwBENt8rltweQdyvSzkypffAej6kg4f8WNvl6XyYgoQawnDjgcARLYfAIBdgEjT6VW07AEQew7AtwvAtwtjW93nAaTeBVhYWJi8Tv3VB6o3O2EbMLbFu40HAESuAQAAUw8AAOi2w1rnBgBEAsC3C9C2B8ANoetdgL97ZXrV2y/ffnvy/1N/vq5SJJ8HYG3ISC9OAQAAAIjaBQAA4jpc32JlD4DYBnF9Dde3CyDNL3QNQHsfgHQXQAsAvvsg1QXhbRQAACJ1BAB0UwAAINLwjKNlD4DYXQDfy0BtrwGkfhuQn3D0nV8gPfAykHEPViYHAESuAQAA1c+cAwDKnpgoOgAQCYDctwFpDeDLu38+kcL1rUCfTrgPIFHPv5EtAAAATEwhdhEQAEjbgbW5Zw8ArYC5xKebiai+rq8x0/O27kbMRe+u6gkAdKX0wPMBAAbegI7iAwDjbFd1rVzXfPPVf5cnAA9A3QSdJAAAdCLz8DIBAIbXZjElBgBiVMsgDnf5N2/YWDy+53ql5jTKkxfw5LG1xdnz52FTA7IPNNaAGqvLogIAXaqdLi8LAKg+r10UhUUZkinocpVDC+S7s8+XTup99Nzr72ufvj+36HwAgKKVAYArCvVWzy+oEsk4MgCgbPzcR8Dc6680n+TRAQBlE+TeAXKvv9J8kkcHAJRNkHsHsK7/uQvXRC1y6MXLFjYsynNMgS3EwxqAwiKwBlBdAwAAFMYUERUAiBBtPor1CCgtzth2AQAAqQXowgMAOv0KAGCbygPkHhAAoDRIYXQAQCgYDw4A2AJA2hypPSBpefsWHgBQtggAAAAoTShpdABAKT8AAAAoTShpdABAKT8AAAAoTShpdABAKX/uAKDbiWNlfOPEW5WoWASMVTIuHgAQp9ssFgCwU7ULAAAoDVAZHQBQCggAAABKE0oaHQBQyg8AAABKE0oafU3sl3GSlnou8wNLK7VFWbfulsrf5++uK2+uoX/8lhtpvfh9+As77qpNYvnkO5W/a48Au8rJyyOtjzS8q77SdCg81yk2ndB4e5+bfuY8138AALvmSmoIAEA98KQ6AgCxiunilfRTLeJIs19YWKhEWV5elibRGP7o/m21z/lttuUdd+U/Kw+Aj4S//xe29dKKxD/dpU1PGj/UU4AHIFVWFx4AMPIAAIBmQwQAdB21rdijmwLQ3N/3sUpaO+BrBW0J3dd0fV/4CS136NqDDwRdeQC0BpP7uwQAAFssDDV4CkdTifL3hcuXpNGThwcAVvJeBEy9BjBb/DFaC3CN6NzQv3387knWP/vZf6o6IQBQL5/LI0jlAbh2XbL3AACANAAgcKT2Gqw8AI4BAEA1rnQWeXRTAFJu65YtFRHb6mixHsDYAUDih26TzjxBdl5C2xN85y2y9wDGehAIAAjrOm15AABAmP6pQ3W2Dcj3/3nFrc8D8PRpbWD+O3ZlGO05gPlThRIvw+ocQqjr7TI0fj4itUF2nf/Vqx9iEbAL0QGAqsoAQBdW588jewCMYAqw5tjBrd7TjH/6N+cn1kAeAH3Z1m8izSGGvgagrf/Q4wMAi5u8nafPjVwu4gAAfW6hfpctewBENE8tMF59ZntjUu9dqH5bngLfuXn1zbzyb65wdYnvOXw2qPO74pZ/55/BluoxdA8Ai4A4CCS1eREA7n/k9Cz9Ywe33pQXAJD29CAAAABIAUDhKyCo8wDKzj/f6fccPlv5XSZEAJCM/FSA0gN49Znt3imMK+0yfu4eQGzjjyUepgDxLQkAFEUx9ClAfPOPIyYAoG/HCQhcawC+KYB2Hh/iAVAVuScAD0Df+ENPAQDQt2AjAHyuPQCQdg1A3/zDTgEAGHb7qUpfXujpOyvuy0B7ElByetBVlvmFvND38iktbf19+uBdAJ9CaZ9nfQwSACgKACDzXYAxnASMZeiQAeDavpN6AHgXIPN3AQCAK7H8mMTTTgHKl5HqjiXH7s8DALLmxBqATK/RhU59DkD7NiJvEB8Ach/xuV4AwOi6tKxCAIBMr7GFzh4AOU8BSmNOBQDqSLGuvqsjujwAjPz1igEAI3gbUDMqAQAa9YYfN3sADL8J42tgsQtQjriP/cPVSSFi9vRTeQD03QRsA2a+DRjffYYfMyUAur4RiE8BAICp/WZ/Kejwu3F8DboAwHe/Of0WIn3xhrwFKrX1LgClS2sBrrl/2wBwrUXsfe5MfIMFxHR9GxLfBagXDycBL+rOAZChz3fs+amACwBtdXwAoP7jsAAAAHCTAlYeQJlwKADannP3FQBtj/y8cbknAAAAAK0CwOVqcw8AAAjw4w2CAABhImIKYDQFILn7vt/Ov5rc9cj8+tLORsvUfh04oj5Z94GsK285BQAAwkYcACBMp65CAQDGHkBfQcBHfipnxIgZZZvkknf1dWBBvbLuA1lXvg0PAACo5wMAEMXN1iMBAC15AHyko7lt14uAPgtqqzz4PLhP+X48BwAAgFYsEQBoRVbzRAEAAEBlVOcuXGuMf8+nb6s872oNgDL1eTg4Cqxq/mFHbnMNIJcpAAAw7D4ADwAegMqCAQCVfMkjAwAAgMoIAQCVfMkjZw2Aod+GlNx6iqLgAOBzfl7GU7/4oPKnL33xM5XfL7/208pvnh6P78sPawDNVgIA9KEXDbgMAMCAG68oiqwBUC4CttV8rtVu7Vl36/L6RkhffgCAT6F+PwcAWmofAKBeWEwBWjK4yGT/Hz74aX15OqefAAAAAElFTkSuQmCC"}], "relative_path": "../textures/models/ben/ov_chronosapien_kineceleran.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_celestial_kineceleran_mask.png", "name": "ov_celestial_kineceleran_mask.png", "folder": "", "namespace": "", "id": "13", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "81886c44-889b-a6e3-353d-bb9d41de2ffb", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 512, "height": 512, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 63, "height": 47, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD8AAAAvCAYAAABUiRJEAAAAAXNSR0IArs4c6QAAAaNJREFUaEPtWTuOgzAQNQ0noOIY0RY5wSrFHneLVU6wRZRjUHECmo1YydIwMjNMbAYSXmoy4/eZZ2Oq4Phr2/ZPatd1XeW4nODaDOAFaQ+j/M/1O1w+vyZUALzj0GPmvchG4CHw0gwcJvBS8N3B0zlcuzmf+b7vw+3+Gz5O538uhmFYNYB5/2pr8NQBAL/i1gPlCbmwfSRjDKCSM5gKOMnVTdMUPevXdT15heb1J8qXTt89gKe7CcDPzfzhbX+omadgrTOqHUqs9ayBxwONh6lWb3KcXLrYGCIAL+xdS8mMJTSleKtNlI+LyFWebkNjzbcFP15Yxre3ObPkgreSKb5CcttqALgTSts+1+acdICfs6FVuRLK0/t8frmyqfL8BMhJKwGe1nwWfCRQu5ky2f5VwEcCAV44h5guDGkGLPnWpn2k4E7StrpYL9V7xKgpbUp7/nAKjBRQa4GfE9MdvBRQAC/M3LO2f3nlU6fFpTO/CfjcK3VtDKwzm7seU9rnNgN4gUEon2svw/9hewNZb/Woq/J7Yw7g96aI13qgvBfTe+vzAAcjB1CR1rBLAAAAAElFTkSuQmCC"}], "relative_path": "../textures/models/ben/ov_celestial_kineceleran_mask.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_celestial_kineceleran_maska.png", "name": "ov_celestial_kineceleran_maska.png", "folder": "", "namespace": "", "id": "14", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "02a58c7d-763b-3b4d-a73f-2c0dbad44d38", "relative_path": "../textures/models/ben/ov_celestial_kineceleran_maska.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\ov_celestial_kineceleran.png", "name": "ov_celestial_kineceleran.png", "folder": "", "namespace": "", "id": "15", "width": 512, "height": 512, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "8aa7416f-3599-8dfe-a2ff-c58898377e22", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 512, "height": 512, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAAAXNSR0IArs4c6QAAIABJREFUeF7tnU+Mndd12GdocAQ1gGzVtGxOA2ThBglAchgPFVSw0UXSVFABddGkdbvqqgsH6SIouumiaIouuukiixb1oquuWrdJFxUQQnVWBVyjFumSoowUqRdZdOioVGQKkA2TMKeYRz2F39W87/47555z7/1pI8vv/v2d8737e+f73pv9Pf6BAAQgMCmB/f3900m3vtn26enp/sz7n33vBH/2DGD/EJiYAAKAAEyc/nsIwMzRZ+8QmJzALgG48/atDZnr126IEtIat3SRVABKyY3RDwEYI47sAgIQKCCAAFABKEibYbogAMOEko1AAAK5BEa9BZBaaaACkJsxY7VHAMaKJ7uBAAQyCCAAVAAy0mW4pgjAcCFlQxCAQCqBUQUgdf9UAFJJjdkOARgzruwKAhBIIIAAUAFISJNhmyAAw4aWjUEAAjECCAACEMuRkV9HAEaOLnuDAARWCSAACMDMlwgCMHP02TsEJieAACAAM18CCMDM0WfvEJicAAKAAMx8CSAAM0efvUNgcgIIAAIw8yWAAMwcffYOgckJIAAIwMyXAAIwc/TZOwQmJxATgNRf1JPG2GpefgdAOnJ9jYcA9BUvVgsBCAgSQACoAAimU3dDIQDdhYwFQwACUgRiAiA1j9dxqAB4jUybdSEAbTgzCwQg4JAAAkAFwGFaNlsSAtAMNRNBAALeCIwqAKnPEFAB8JaRbdeDALTlzWwQgIAjAggAFQBH6dh8KQhAc+RMCAEIeCEwqgCk8qUCkEpqzHYIwJhxZVcQgEACAQSACkBCmgzbBAEYNrRsDAIQiBFAABCAWI6M/DoCMHJ02RsEILBKAAFAAGa+RBCAmaPP3iEwOQFpAUh9+t4Ldp4B8BIJm3UgADbcmRUCEHBAAAGgAuAgDc2WgACYoWdiCEDAmoC0AFjvJ3d+KgC5xMZqjwCMFU92AwEIZBBAAKgAZKTLcE0RgOFCyoYgAIFUArMLwP7+8gh48uTJ0GfChQsXTp/NjdH3G7sOhg52bPO8DgEIzE0AAUAAZr4CEICZo8/eITA5AQQAAZj5EkAAZo4+e4fA5AQQAARg5ksAAZg5+uwdApMTQADWj4De75GH9/zDdO99f7WXLwJQS5D+EIBAtwQQAASg2+QVWDgCIACRISAAgT4J7BKA3n7Rr5R++C2A0T4hUwFYzwwEoPTKoR8EINA9AQSACkD3SVyxAQSgAh5dIQCBvgmMcgugtGJBBWDs3z2IXZ0IQIwQr0MAAsMSQACoAAyb3AkbQwASINEEAhAYk8AoAlAaHSoAVABKc4d+EIAABLomgABQAeg6gSsXTwWgEiDdIQCBfgkgAAhAv9lbv3IEoJ4hI0AAAp0SQAAQgE5TV2TZCIAIRgaBAAR6JIAAIAA95q3UmhEAKZKMAwEIdEcAAUAAuktawQUjAIIwGQoCEOiLgLUAlH5/X4oy3wLgWwBSucQ4EIAABLoigABQAegqYYUXSwVAGCjDQQAC/RCwFgBrUlQAqABY5yDzQwACEDAhgABQATBJPCeTUgFwEgiWAQEItCeAAOQdAU+e+P7EHPvrf2GGed+P9hWRF33t1TA+BCAAgYYEEIC8I8D7gYkA5F08edHPG5vWEIAABFwTSBUA66f1tSDGngHo7RMzApCXKQhAHi9aQwACAxFAAPKOACoAAyX/3t5eXvTH2ju7gQAEJieQKgCjYsqtAHirCOR+4ve2/ty8evvtW79+1ufatRu/n9v3vPYIgARFxoAABLokgADUHQHWFQEEoO6yq4t+3dz0hgAEIKBC4O692398NvDR1eOfX5sAAag7AhAAlfRtNmhd9Jstk4kgAAEIpBNAANJYcQvA99ca06JY3goBKGdHTwhAoHMCs1cAtMN3+fLl1Snu37+vuoRQcE5PTxfznZ6eTn0GTr151cxjcAhAwD0BBEA3RAiALt/a0RGAWoLC/e+8c+uvng15/cqN/y48NMNBAAIBgdEEwNvvFSAAvi85BMBZfBAAZwFhOUMTQAB0w4sA6PKtHR0BqCVIfwhAoFsCowmAt0DsEoCbb76xWer1azdMl8wzAKb4mRwCEICAHQEEQJc9AqDLt3Z0KgC1BOkPAQh0SwAB0A2d9S2A2O6oAMQI8ToEIACBQQkgALqBRQB0+daOvs8FUIuQ/hCAAAQgMCKB2h9KsmYS/u5BuB4EwDpCzA8BCEAAAi4JIAAuwzLuorx9j3dc0ufvDP6zRZz9eiLg7forFYD/dfetDdZfOnq5Cd5d81EBaIJfbhJvF4DczvoYCf59xIlVjknA2/U3jQB4Az9merOrUgLkZyk53/1ax7X1fL7pl6+uV4656y4VgHKysj2TKwC5YGSXyWgQWCdAfo6ZIa3j2nq+MaO2t9crx9x1TyMArRI1NwCt1pU6T+/rT90n7WwJSOVZ6Til/WypMTsEZAkgALI8uzXHLQbeGIUTguHOJSCVZ6XjlPYjnBAYiQAC0Hk0L33+pcUOHvzpu4v/vnjxYtUOHz9+XNW/trP0+mO8atdr1Z8DbZ187AdbdvXW/nvu2vlS+0tw299RscovqfVrc9YavzRvt+s5OTmZ+tdwh/8dgNiBJn2AaiX6rnGl1x/j1Xp/UvNZvUFLrV97nNI3UgRg//QsNlb5hQBcrro0EID9pwk86j+xA036AG3NUXr9MV6t98d8bQggAGWcrX9JFQFAAMoy92kvKgDcAljkDwJQczn12xcBKIsdAlDGTapXad5yC+AjATg8PHRRASgtJYYH1jawf/jNP9j8z5ePX1nNtZ/85Cerrz/33HNVuRqOf+HChdXxnjx5snhdev5w8tj4n/6LL66uN3ymogrW3t5e7QUdm780z2Lj9v769pOk9vtBa/61n5Cl4xoTht7WK81H+/qfveQfxmtf+4JPTZDSNwYEYJ1wreAgAKkZ3Hc7BKBN/BCAdc4IQJs83M4yrABsN/jwz953XQEIHx6iAlB3Ty92+ZSKZmzc3l9HAHQiePed7/7+2chHV77062f/RgAQAJ1MKxsVATC+BYAALBNX+xMAAnD+GwUCUPYGGuuFAMQItb3+uQWw5O1GALbLir1BhyX/2Cf8WPqFJfLf+c7PL7r8zi//8ea/P/jgg82/P/e5z6lWFGpL9qn72W4i9gxAjF94iyD2TID2AR9bbyy/dvW3+ppXbD9S602996x9yzA3PqnrLuVIv3UCsYpG2Fv7+ueAz8tYBCCoACAAeQmEAOTxatU6V1hSD1IEoFUE+5gHAegjTrtWGRWAm2++sen72quvN9lp7BNA6wrAP/qFW5t9v/DCC5t/xz4xS3+CD6Hnzr9LaKgANEnnbiZBALoJlauFWgnA7/2X/7jh8Bt/6+8ueFAByEsPBCBSAUAA1hNqlgpA3mXVX2sEoL+YeVgxAuAhCuVriApA+dDn96ytKDx+8tPFwNrPAIS72D4TsOsTtFQF4O13vruZ4tqVLy2WEFYAYvf8c9efG+9QAC5e+NRiiNp4bweTGidWYcrd/yjtUwUgtl/tWwR8wotFwPb1MP5S1+12V8RfNr4IQKQCkHuAIgAIgOwl2mY0BKAN59FnQQD6inBzAcjFExpk6wrAb/yFb2yW/Hs/+urm39sKwI9+9KPNf7/44vKX8rYCkPoJPuSRKxClFYDt+mK/lBiLV6wCEOvf+nUqAOcTRwBaZ+KY81EB6iuuCECkAoAArCc0AtDXBb9rtQjAGHG03gUCYB2BvPndC0C4ndYVgBjO8JmAsH3sqf3Y+LGKQPiUf2y82DMMsf7h67MKQO7X7HK5tm4vJQDhuqUPBO4Bt86MvPmIdx4v69YIQOYzAGHAEIDlLZDwIUDrBA/nl7oFgACkRZYDIY3TKK2Id1+RRAAQgKqMnbUCUAXNYefRKgB3793e/JXTo6vH+w5xD7skBKCv0CIACEBVxiIAVfjcdEYA3ISi64UgAH2Fbz/3hxy0tlf6G9G1zwTE7rH/k//2i4stP/eZ5e8QxG4JSD8DELvn/5MfLr+G9y//+h+thix3fdoHvlSJftemvZTurdaxa14tAeCZAK13TB/jcuD7iEPpKhCAyF8DRACWqYUAlF5qy34IgAxHHgqU4Vg6CgJQSs5Hv2oBkHojowJwfkLkfs+fCoCPC6u3VVAB6C1iPtaLAPiIQ+kqEAAqAFm503sFIGuzEzVGACYKtuBWEQBBmAZDVQuA1JqtKgDh+mufCZD+nn2sAqD9iT/kIy0AP/jBDxZTnJ5uHt7mn8YEWgnAhQsXFgH+whe+ULVTbgFU4avuXCsA4fX/5MkTvrVRHZX0ARCAgBUCsJ48CED6xdVTSwSgp2j5WSsC4CcWJStBABCArLxBALJwddPYqwDE/pocFQDbFEMAbPnXzo4AIABZOYQAZOHqpjEC0E2oXC0UAXAVjuzF7BQAqaf7U1e0v//01k/tPcHY7wLESvzhesPvyX/nO/9z0+To6HrS1nK/Zx8OGltv+FcHw/li/WP75cBPCnP3jVoJQAiKZwL6Sp3SA39byfmlo5cXG+aev238EYAIfwRA9rf+eejP9oLfNTsC4DMu3laFAHiLSN163NwC6KUCkPuJevsJ/eXjV4oilTsfFYAizJ/o1LoCJrPq8lEQgHJ2M/UsFYAtI57695UtCEBmBSD3QEYAloB7qQAgAG3eqLgF0Iaz1CwIgBRJH+O4EYAtjm0lYPvfuc8EhM8AvHt/+T3z2D1vH2Fpt4qY0Lx0efk97dw/9yt14M92ILfLgKczaVcA7rxz+9+fzXP9yvHfX9tbrRDwrQDdzMkVAD7x68ajdnQE4Lnnahl23R8B6Dp8YotHAMRQDj0QAjBWeBEABGA1o71UAMa67Mp3o1UJ0RaA1B1TAUglZdMOAbDhrjUrAoAAIABaV5fCuAjAOlRuASgk3TNDIgC6fFuP7k4AQgDhMwHh67FnBGK/C9AauPf5cr/3H97jC/fHb/s/JaJ1cEvlk5cKQLifsCKQe/0jBHUZEjvwY9c/3/Ov46/dGwHQJtzZ+AiATsAQgDKuCEAZN6leCIAUSZ/jiAuA9BsdFYC2iYMAtOXtZTYqAF4i4WsdCICveEivBgGQJtr5eAhA5wEsXD4CUAhu8G4IwNgBFheA1rhiFYJwPZ996XOL/+vhn70vuuTY1+pqJ6v92wLh/OGB/967/y9ridzjz8LltrFXAYgBi90iCPtzT3qdKDxjGTfW6wgAArDIaARgrAs8dTcIQCqpsdshAGPHN9wdAoAAmAqA9DMjc12+crtFAORY9jzSd279j9Oz9f+VX/5K0jZqKyp3793ezHd09fjpn4Pln6YEEAAEAAFoesn5nAwB8BmX1qtCAFoTt52vewE4p6SRRXT7TMAffvMPNv1y/2pf6T3/7R8JunblS1nrDRvnPhPw1u1vb4b4a7/2Nzb/puRfhX+Yzr0KQBgASth5KQmvPF6jtUYAPnooEAFIS20e+kvj1FsrBKC3iMmsFwGQ4djrKO4FIPcecetvBZRWAKQSJrcCwFP/UuTHGgcBGCueqbtBAFJJjdkOAaj8WiACMOaFMduuEIDZIv50vwjAnHHf7tq9AOSGJ7cCEJa0L33+pcWU4e8ExA783E/kufvLnT/8xP/gT99dTFnLaztYbqUmd9+ztLfiOKsAhNe/NIcrV65snnLf9c/3vve91dSWXk/ugR8urvap/1mu4172iQCcLq9PBGA9dXc9A2B1cPVyoaWu04qj9EGTul/pdrkHHAKQFwEEII+X99YfC4DVG480oNpPtAhAmQBIx5Hx2hJAAJ7yluZABaBtHjNbHgEEgApAVsbwLYAsXN00lj74rDZOBWCdfC4fbgFYZXKbeZvfArCuNIQVgtiB9uTJk9VIaN/zj6VB7JmACxcurA6RyyO2Hu+v5+Zfbnvv+9+1vlEEINzf/v7+4h5fbr7XcrGuAOQe+Nq3RHq9PkZdNwIQVADOMV4EYKDszz3Qc9v3iqr2oPO6bwTgwupDiGHcEACvmayzruYCoLON9FFzPwFQAUhnS8t+CSAA58eulgsVgH6viRlWjgBQAVjkeeyWyAwXxYx7rD3ovDKjAkAFwGtueliXh7/AlFWi8gBNeA0eYiC8pfThwjfo9J60HIHAqOKRGpvc/L98+XLq0Ent7t+/n9ROq9Hs8dfimjquh8MHAUiN1oDtct8AB0Qw9ZZmPwBy8x8BmPpyEd88AiCONHtADzHIXrRUh9w3QKl5GccHAQRg+S2FWFQQgBghXs8h4OHwoQKQE7HB2iIAgwU0czsIAAKQmTI0FySAAAjCLBzKQwwKl17fDQGoZ9jzCLMLwOHhYVcfgKSfGZg9/tbXrofDp6sLQCFgHmKgsK20IRGANE6jtpr9AEAATqd+/7O+rj3ARwCss8Bw/tEEYJYfDpJKGQSACoBULjFOPgEEIJ+ZdA8PMZDeU/J4CEAyqiEbIgAIwJCJ3cmmPBw+VAA6SRaNZWoLwEtf+2zVst/9+ntV/em8TgAB6EsAwmimPhOwqzI2e/yt3x8QAOsI7O15iIEZBQTADL2LiWc/AHp7BgABcHHZiC3Cw+EzdAXg7r3bm2AdXT3eFTQPMRBLqNyBEIBcYmO1RwDmqADsytrZ4299NXs4fBAA6ywwnB8BMITvYOrZD4BZKgAIgIOL7ZwlIAD2cfEQAzMK0gJQe88/BoJnAs4nVPrtBwTAtgKQeg9/13VR+8uEJycnU7//xd5vtF/3AH/oCkBCAD3EIGGZOk0QAB2urUdFAMqIW1cAEICyuI3Sy8PhgwCMkk0F+0AACqAN1IUKABWAgdK5u60gAPYh8xADMwoIgBl6FxMjAAiAi0ScdBEeDh8qAJMm39m2awVA+55/LDQ8ExAjtP46AtCXAIS3engGoC7/rXsjANYR4HcAqgQQAbBP4JoVIAAIQE3+0LeOAAKQyC/h+/yJI32imYcYlK69uh8VgGqEXQ+AAKwLwM0339jE97VXX1eJMw8BqmDtZlAPh0/VJ8BWpBEAHdIIgA7XXkZFABCAXnJ1xHW6FQDFA9dbHD3EwIwJAmCG/tyJS7/OV7oLBMD2FkBp3Lb9aisIs8e/ln9tfw+Hz7kVAASgNrR99EcAfMUJAWgbD+vfAajdLQJQS9C2v1sBsMXSdHYPMWi64Wcnm10AWh+4ZoHeMfHsnwCtBEDq2QIEwNsVlbceD4dPF88A5GHNau0hBlkLlmyMANza4Lx+7YYk1m7GQgBsbgEgAN1cIqoL9XD4NBUAh7cWPMRANcnWBp9dAMzAO5kYAbARgNLw137iD+edPf6lcZDq5+HwQQCkotnhOAhAh0ETXPLsB4DVLYDSECIApeR89ptOAByGwUMMzLAgADroe3m2AAGgAqBzBTBqCgEPh0/TCkAKlMZtPMSg8Zb/fDoEQAc9AqDDVXpUKgCnU7//SedT7nge4CMAuVEbqD0CMFAwC7YyewUgN/9rf3s/DJF0ST83BWaPfy4v6fYIgDTR/PE8xCB/1UI9ct8Aw2n5WwBCgTAaZvYDIDf/EQCjRB10Wg+HDxWAQZMrZVu5b4AIQArVftogAPtZ738IQD+53cNKEQD7KHmIgRkFBMAMvYuJEQAEwEUiTrqIs8Mny0An5aS27YODg8XYLzz//OK/Hzx8qDb32cD7+337xy/86y+q8okN/r//4fdjTXi9IwLSn7DDrVvfc+8oFE2WOr2AIgBN8mznJAhAHX8EoI4fvZcEEIC5MgIBoAJgmvEIQB3+3gWgl6/r1UWpn94IQD+xklgpAoAASORR8RgIQDG6TUcEoI4fvakAzJwDCAACkJT/Dv+GQNK6Y41KnwGw+uQafu3vxaufiW1R9fX37/1wMf67X39PdT4G1yVABUCXr7fREQAEICknEYAlJgTgKQ8EIOny6aYRAtBNqEQWigAgACKJ1OsgpRUAq/1SAbAiP+a8ocgiALZxbv3BAgFAAGwz3nh2BKAuAFQA6vhZ90YArCNgW1lEAAoFYNSSuK/LQX813gUg96d+tZ8JCA/8WIRKnwlo/Ukoto9ZXqcCMEukn+4TAUAA5sr4YLcIQF74EYA8Xr21RgB6i1jdehGAQgGow67Xm8rEkm2MBwKQl4utBCBvVXKtZ688eBOA2eMhl9nnj4QAIADaOWY6PgIgix8BkOXpbTQEwFtEdNeDAAwmALrpMt7o3isAuT/0EzugY88I1PYPM4S/FSBzzVh9Eq4VAm+//X/x4kWRgLx1+9ubcV4+fkVkvF2DPH78WHV8BAABUE0w74MjAMsIIQA+MxYBkIkLArDkiAAIC0Cs5CyTxowiRQABGFsArA5Oqfy0HocKgG0EqADo8hf/c8AIgG7ApEdHABAA6ZwaaTwEwDaaCIAuf3EB0F3ueKNf+vSnVzf1+Z/92cXr77zzjiiE0QQgBke6xB+bb5RnAGatJHgVgF3xkCrxx/Lay+u1gsAtAOFbAF4So5d1IADrkcp9CDAWdwQgRuj81xGAMm5aDwEiAE/jgQCU5eW2FxWAOn7VvRGAOQVg1gO1+oJpPIDXCsAuDFQA8hKECgAVgLyMEW6NACAAwinFcIIEEABBmApDUQGog+q2AmD9MGHp/Ln9Dg4OkiK4/d7tr37lVxbtHzx8mNR/V6PUZwBKP7GW9tuuV/oWwB/91v85y/md//ziv/nLp1VAg86jPAMgyaSnsbwLwGyf+GO5kysEVACcVgByD9JYYuS+Xjp/bj8EoG0FAAHIvRLmbo8A9BV/BCAvXm4rAHnb6Ld1qgBsd/jC88+bVACsCJdWAL7/238iuuQv/u7PFY1HBWCJrbYiVBSEZzrlzr8VgJtvvrEZ5bVXX89agtZDgNtF1FYAWv2i3y5opfPv6ocAZKXnHgKQx0u8NQKgUwFAAMRTVWTA3ANYZFIEYCfG0gNYKi6l8yMAMhHIFoDcErfMMscdJRSA8BN+bOfh7wTE2oe/I5D6DEBsXK3XpSoA//ybL2ct8Z/92luL9lQAsvAN09jbLYDaT/zDBCZxI7GKAM8AZD4DgAAkZl5iMwSgTQUAAUhMSJotCCAAfScEArAev+wKQN/p4G/1CMCcAmBdCg+pe1uPlyvVSgD4oR+ZDEAAEACZTFIaBQFAAJRSK2tYBOB8XAhAVhq5a4wAIADukvLZBcV+CChcfOyef+7fChjtGYDt1/wuXrx47vf5Y7cCwnv/W/6PHz/e/H5A7u8E8C0A15dfdHFWArBrYTwDEA3ZogECgADkZUzj1giAbAUAAWicwINPhwD0HWAEAAFwncEIQFsB2M4WVgJ2ffKnAuD68lFfHAKgjlh1AgQAAVBNsNrBEQAEoDaH6K9HAAHQY9tiZARAWAD4GmCLtN09R+yHgx49epS1wNGeAfjHf+mfbvb/m3/nH6xyyK0A/Nv/9O824/2r//svsvjyDEAWruaNuafeHLmrCR89erT6t0FcLVZhMdlfA0QAFKKQMSQCsA4LAchIJpruIQBzJwECkPlDQHOni/3uEYA6ASj9FsDoFYBZvwboVQBKfyLX/h2qrxUgAAhAVxmLACAAGgmLAGhQLR8TAShnl9MTAUAAcvIlua3UrZLYgR9bUOyZgFbPAJQeMPwtgFiEeb2GgNcKQM2e6JtOAAFAANKzJaMlArCEhQBkJA9NmxFAAJqhdjkRAoAAuEzM7aJGqQCUQh6lAlAqQKXc6JdGIFcAKM2ncW3VqjYeCAAC0CpXi+ZBAL5YxO37v/0ni36xh//CSaT/HDACUBRG9U4IgDpi1QkQgDq8+wcHB+f+Zvp22Ng95Lrpx+9de4BrE4r9UIb2/LHxpSoAsXlir3/xd38u1uTc1/kdgCJszTrlCkCzhTFREwLTVwAQAN08QwDq+CIAdfzovU4AAZg7QxAAKgCqVwACUIe3VAB2zbr9oaBdr+f+0l9sd1QAYoRsX0cAbPlbz44AIACqOYgA1OFFAOr40ZsKADmwmwACEBGA3IcsRn9mwPuBnnuxe38G4KWvfXZ1Sy9e/UzulkXbv3/vh6vjvfv191ZfH+XhwF73QQVA9HLobjAEAAHISloEIAtXdWMEoBphkwEQgCaYmUSYAAIQEYBc3lQAconZtqcCUMe/tgJQN3u8966DudcDO77jvBZeKgC5lda8XdJ6FwEEAAHIujqoAGThqm48egWgGlBkAARgHRACoJ2BvsdHAIQFIDfc3ioGox3wsXj0XgGI7a/2GYHYJ/zY/LFnAGL9eV2XgBcB0N0lo1MBOJ9A9IeAtFMHAdAmvD4+ArDOBwGwzU/t2REAbcK+x6cCUFgBkLpnZS0A4R/t0a4ASHGTuqxCAWh9bzg2X+wWQIwDFYAYoblfRwDmjj8CgABsroCjq8ebfyMAtzYcrl+70eSdAQFogplJdhBAAOZODQSgUAC008bbJ2Xt/cbG1+Ix+i2AGFft13t7BiAmZNq8pMZP3YeUAGhdn1rjSnFOHaf1PlLnQwAQgNQcNm2XmtC5i0QAconltUcA8nhJtUYApEjKjKP1/rVrdanzIQBOBUAm7RglRgABiBGqe703Aajbbbx36sEcH0mmhVQFQGY17UdJPSjbr6zNjAgAAtAm05zOggDoBgYBWPJFAHTzLXd0BODRfi6zkdqbfw1wJJg97sW7AIRMa78VoB0jDnxtwrLjz14BkKXZ32hUAKgA9Je1gitGAARh7u3tIQCyPLVHQwC0CfseHwFAAHxnqPLqEABZwAiALE/t0RAAbcK+x0cAEADfGaq8OgRAFjACIMtTezQEQJuw7/ERAATAd4Yqr643AfD2TECrA9/bw3PKadlseASgGWqXEyEACIDLxGy1KASgjjQCUMfPujcCYB0B2/kRAATANgONZ0cA6gLQSgDqVknvXQQQgLlzAwFAAKa+AhCAuvAjAHX8rHsjANYRsJ0fAUAAbDPQeHYEoC4ACEAdP+veCIB1BGznRwAQANsMNJ4dAagLAAJQx8+6NwJgHQHb+REAZQGY/adjnD0vAAAfxklEQVQmbdM7PjsCEGe01gIBqONn3RsBsI6A7fwIAAJgm4HGsyMAdQFAAOr4WfdGAKwjYDs/AqAsALbhZfYYAQQgRmj99VkFYJTfJRhFAKi0ll3HCAACUJY5g/RCAOoCiQDcqANo3BsBMA6A8fQIAAJgnIK20yMAdfxnFYA6an56jyIAfoj2tRIEAAHoK2OFV4sA1AFFAOr4WfdGAKwjYDv/9AKwt7d3WhOCw8PDmu7VfU9OTqrH0Bjg7r3bm2GPrh5rDC825v7+vthYawNp3TN+6WufbbL+XZMgAKb4qydHAKoRdj0AAoAAqCQwArDEigCopBmDVhJAACoBdt4dAUAAOk/huuW3qgDUrXJ3byoAWmTnGDdXAHja3lde1MYDAUAAfGV049UgAHXAuQVQx8+6NwJgHYG6+RGAOn5nN4B5BqCOYde9EYC68HkVAK1bLnW0/PW+fPny6qIePHhQtejYQ7ax+cPJpdcTm196vnA/1vOfnp62eQiqKov0OiMAemy7GBkBqAsTAlDHz7q39QEUmx8B0BUwBIAKgPV7kOn8CEAdfq8CULereXrHDmDrT8AIAAKgeTVSAdCk28HYCEBdkBCAOn7WvREA37dAtAWMCkBlBSC8gLV/F6D19/57+Tpf6RtprgB4v7es/a0ADvzSTPPZLyYA9+/fXyw89tDgpUuXFu3D/rn3wMP20uvxvn/p/YY8EQAEYPWdCQFY4kEA3vN5krGqIgLeD0AEQFfAEABnAnDzzTc2Of/aq6+fe0G3rgAUvasIdtIWkNwKgODWVIaiAqCCdbhBtyK7631mu2HtT6AxAZlFAHa972vzRwAQANdvbghAXngQgDxes7ZGAJ5GPiYg2gfwdn4EwOZKrH4IMLbs2mcCZvvEH+Mp/fpoFYCQT60QcM9fJuO83joKD8DYPfuQhnT/2Cf+WDRS17NLgKz333p+KgDCFYAwQRGA2CVr+zoCsM4fAZDJTwTgfI65n8Bj0UAAls8MxHghAJMIgHYpPZZo3l7f8rh+7Ya3pYmuhwqAKM7hBks9MHdtXLp/qwrAdh7p9bf+BF+7fgQAARjuTS1lQwhACqW9PSoAaZx6bVV7gEj3RwDyPsHX8kcAJhGAXt+gtNc9+i0AbX6Mb0ug9tZC7QEi3b93Adg+zJdaWZTml1uBQAAQANt3MOPZEQDjADB9FQEEYIkv90DNbR8GK+yPAFSlc/POfAugOXJfEyIAvuLBatoSkD4Acz+BWj0EyDMATwlQAaAC0PYdx9lsCICzgLCcpgQQgOXfAqgVmN76IwDCAlD7tb/Y1c/vAsQI5b2OAOTxovVYBBAABGCsjM7bjfgtAAQgLwDWrREA6wgwvyUBBAABsMw/67kRAOsIGM+PABgHgOlNCSAACIBpAhpPjgAYB8B6egTAOgLMb0kAAUAALPPPeu4zAbD+59R4AR4YGCNgeghAAAIQmI2Ah8MPAZgt69gvBCAAAQiYE0AA9vY8MDBPBBYAAQhAAAJzEfBw+FEBmCvn2C0EIAABCDgggABQAXCQhiwBAhCAAARaE0AAEIDWOcd8EIAABCDggAACgAA4SEOWAAEIQAACrQkgAAhA65xjPghAAAIQcEAAAUAAHKQhS4AABCAAgdYEEAAEoHXOMR8EIAABCDgggAAgAA7SkCVAAAIQgEBrAggAAtA655gPAhCAAAQcEEAAEAAHacgSIAABCECgNQEEAAFonXPMBwEIQAACDggMLwB3793eYD66erwLtwcGDlKBJUAAAhCAwEwEPBx+qn8LAAGYKZ3ZKwQgAAEIpBIYXgASQHhgkLBMmkAAAhCAAATkCHg4/FQrAAmoPDBIWCZNIAABCEAAAnIEPBx+CIBcPBkJAhCAAAQgkEQAAeBbAEmJQiMIQAACEBiLAAKAAIyV0ewGAhCAAASSCCAACEBSotAIAhCAAATGIoAAIABjZTS7gQAEIACBJAIIAAKQlCg0ggAEIACBsQh0IwAJP+hTGpkkBnfeuf23zya4fuX4P5dORD8IQAACEICAFwJJh5/yYpO+BogAKEeB4SEAAQhAYCoC3QiAYlQ8MFDcHkNDAAIQgAAEPknAw+GXVAFQDJ4HBorbY2gIQAACEICAggDcvXd7c4AfXT0uPUgRADITAhCAAAQg0JhA6aH98TIRgMYRYzoIQAACEICAAIFqARBYAxUAAYgMAQEIQAACEMghgADwOwA5+UJbCEAAAhAYhMCZAFh/Ah8EZdk2Dg4OFh0vXbq0+O+Tk5OygdN7eZDA9NXSEgIQgAAERAggACIYywdBAMrZ0RMCEIAABMoJIADl7ER6IgAiGBkEAhCAAAQyCSAAmcCkmyMA0kQZDwIQgAAEUghUC0DtT/QeHh6urrPBPfAUTiO34RmAkaPL3iAAAQjsIIAAkBoIADkAAQhAYEIC1QJQy4wKQC3B6v4IQDVCBoAABCDQHwEEoL+YSa8YAZAmyngQgAAEOiCAADQKUu2zEorLRAAU4TI0BCAAAa8EEIBGkUEAGoFmGghAAAIQSCKAACRhGroRFYChw8vmIAABCJxPAAEgMxAAcgACEIDAhASaC0Dsqf8wBvwOgHpWIgDqiJkAAhCAgD8CCIC/mLReEQLQmjjzQQACEHBAAAFwEATjJSAAxgFgeghAAAIWBBAAC+q+5kQAfMWD1UAAAhBoQiBbAGq/zpb7DMDozwTk8lB4JgIBaHKpMQkEIAABXwQQAON4IADGAWB6CEAAApMSyBaAWk65Bx4VgCUBKgC1GUh/CEAAAhA4I4AAGOdBrhAhAMYBY3oIQAACgxBAAIwDeXBwkLWCS5cuLdoLCAHPAGRFgMYQgAAExiCAABjHEQEwDgDTQwACEJiUgJgApH47ILfk3foZgNR9SOXLdr6Xj19JGpIKQBImGkEAAhCAQIQAAhAAQgC4ZiAAAQhAYAYCJfd/T2vAlFYAbr75xmbao6vHNdO36JvF9ODgYMEz/IQvveCTk5Os9UnPz3gQgAAEIOCDQMlhgACsxy6LKQLg40JgFRCAAARmI5B1WH0Ex0QAtoEReOpdO8ZZTBEA7XAwPgQgAAEInEcg67BCAJKSKIspApDElEYQgAAEICBMIOuwKpn78PCwqmIQzmlQAVBlpMBHdb0lOUAfCEAAAhDwR0D9sFA44FpTVGWkwEd1va3hMx8EIAABCOgQUD8sFA44HRK7R1VlpMBHdb2t4TMfBCAAAQjoEFA/LBQOOB0SCEBrrswHAQhAAAKGBNQFINxbB0LQnIlh/JkaAhCAAAQmJdD8sEMAJs00tg0BCEAAAq4IIACfDEdzJq4ygsVAAAIQgMAUBJofdlQApsgrNgkBCEAAAs4JNBcA6WcCBH4XwJyB8xxheRCAAAQgMCAB88OvtiKAAAyYlWwJAhCAAATUCSAAe3vmDNSjzAQQgAAEIACBgID54UcFgJyEAAQgAAEItCdgLgDtt8yMEIAABCAAAQggAOQABCAAAQhAYEICCMCEQWfLEIAABCAAAQSAHIAABCAAAQhMSAABmDDobBkCEIAABCCAAJADEIAABCAAgQkJIAATBp0tQwACEIAABBAAcgACEIAABCAwIQEEYMKgs2UIQAACEIAAAkAOQAACEIAABCYkgABMGHS2DAEIQAACEEAAyAEIQAACEIDAhAQQgAmDzpYh0JrAN77xjU+dzfnVr371p63nZj4IQOB8AggAmQEBCKgTQADUETMBBLIJFAvA3bt3Xzyb7ejo6P3sWekAAQhAAAIQgIApAQTAFD+TQwACEIAABGwIFAuAzXKZFQIQgAAEIAABCQIIgARFxoAABCAAAQh0RgAB6CxgLBcCEIAABCAgQQABkKDIGBCAAAQgAIHOCIgLwN17t0/PGBxdPRYfuzO2LBcCEIAABCDgloD4IY0AuI01C4MABCAAAQh8TEBcAGALAQhAAAIQgIB/AgiA/xixQghAAAIQgIA4gf2Dg4PNPXv+mZPAo0ePkMA5Q+9i14eHh4v3n0cffrhY14OHD8lPF5FiESMSQABGjGrGnhCADFg0FSeAAIgjZUAIJBNwKwBv3f72ZhMvH7+SvBnJhqXzl/aTXHvOWAhADi3aShNAAKSJMh4E0gkgADtYlR7kpf3SQybbEgGQ5cloeQQQgDxetIaAJAG3AiC5ScbaTQABIDtaEogd+LG18ExAjBCvQyCdAAKQzmrIlgjAkGF1uykEwG1oWNiEBLIFoLcS94QxzdoyApCFi8aVBBCASoB0h4AgAQRAEGaPQyEAPUat3zUjAP3GjpWPR0D9O7bSvzMQO7Baz6edErX7ifHSXj/jQ+BZArUCENLkmQDyCwLlBBCACDvrAxQBKE9uevojgAD4iwkrmpcAAoAAzJv97Lw5AQSgOXImhMBOAggAAsDlAYFmBBCAZqiZCAJRAu4FoLYE33sJvff1RzOQBkMTqM3fF55/fpUPzwAMnT5sTpkAAkAFQDnFGH5mAgjAzNFn794JIAAIgPccZX0dE0AAOg4eSx+eAAKAAAyf5GzQjgACYMeemSEQI6AuALEF8DoEIDAOgfAhv9jOHn344WqTD37848Xr4TMBPAMQI8zrENhNAAEgOyAAATECCIAYSgaCgDoBBEAdMRNAYB4C1gLwrW99a/O1gS9/+cvL0sE8IWCnEEgmgAAko6IhBCAQI4AAxAjxOgT8EEAA/MSClUCgewK7Hvrb/hXRX/3Kr1TtMXwmoPZ3QqoWQ2cIdE4AAeg8gCwfAp4IIACeosFaILBOAAEgQyAAATECsa/9xX7ZL7YQKgAxQrwOgXQCCEA6K1pCAAIRAggAKQKBfgggAP3EipVCwJxA7kN+Dx48WF1zbkWACoB5CrCAgQggAAMFk61AQJsAAqBNmPEh0I4AAtCONTNBoHsCCED3IWQDEPiYAAJAMkAAAskEEIBkVDSEgHsCCID7ELFACPghUPuQX3gPP9wZv/XvJ9asZHwCCMD4MWaHEBAjgACIoWQgCJgTQADMQ8ACINAPAQSgn1ixUgjECCAAMUK8DgEIfEwAASAZIDAOAQ8CcGqM0wMDYwSm01vH33Tze3t7U+df7kOFsWDdv38/1sTV66enp1PH31UwJlyMh+SzPgA8MJgw9T7esnX8rdlPnX8IAAJgfQHOPL+HNx/rA8ADg5lzUDX+d+/d3rA9unrslfHU+YcAIABeL8wZ1uXhzUf1AEgIogcGCcsctolq/BEA33mDACAAvjN07NV5OPxUD4CE8HlgkLDMYZtYx98a7NT5Jy0AucGUfmbg8uXLWUs4OTmZOv5ZsGgsTsBD8lkfAB4YiAe2owGt42+Naur8QwAQAOsLcOb5Pbz5WB8AHhjMnIPW8bdmP3X+IQAIgPUFOPP8Ht58rA8ADwxmzkHr+Fuznzr/EAAEwPoCnHl+D28+1geABwYz56B1/K3ZT51/vQjAnbdvbfLk+rUbi3zJvecfJhvPAFhffnPP7+HNx/oA8MBg5iy0jr81+6nzDwGgAmB9Ac48v4c3H+sDwAODmXPQOv7W7KfOv14EYFeSUAGwvnyYv4aAhzcf6wPAA4OaGPbe1zr+1vymzj8EgAqA9QU48/we3nysDwAPDGbOwaT4W/+gT+n8Cf2mzj9rAai98Gp/R4C/BVAbAfrXEPDw5pN0ANRsMtLXAwPF7bkfOin+CQep6kZL50/oN3X+IQD8EqDqhcvgqwQ8vPkkHQCKcfTAQHF77oe2jr81oKnzDwFAAKwvwJnn9/DmY30AeGAwcw5ax9+a/dT5hwAgANYX4Mzze3jzsT4APDCYOQet42/Nfur8ay0AN998YxPv1159XSTuPAMggpFBjAh4ePOxPgA8MDAKv4tpreNvDWHq/EMAqABYX4Azz+/hzcf6APDAYOYctI6/Nfup86+1AEgHmwqANFHGa0nAw5uP9QHggUHLmHubyzr+1jymzj8EgAqA9QU48/we3nysDwAPDGbOQev4W7OfOv+sBaD2Ezy/BGh9+TB/DQEPbz7WB4AHBjUx7L2vdfyt+U2dfwgAvwRofQHOPL+HN5+iAyDhB1ZS45rE4M697/7XswGvX/3S30wdmHZJBIrinzRyH42S8q+PreSvslQApJ7mpwKQHzN6jEPAw5tP0QGAAAyThEXxH2b3e3serkEznAgAFQCz5GNi+Tefu/du//SM69HV408l8rU+AKZ+A06MkWYz6/hr7i1l7Knzr1QAUsCmtKmtAIRz5D4TcHKCAKTEiTY6BMTffBAAnUANPCoCMHBwY1tDABCAWI7wuh4BcQEoWKr1AeCBQQG2YbpYx98a5NT5hwAgANYX4Mzze3jzsT4APDCYOQet42/Nfur8QwAQAOsLcOb5Pbz5WB8AHhjMnIPW8W/CfuWh1anzT1oApO/payfH6Sk/BKTNmPF3E/Dw5mN9AHhgMHOOWse/CXsE4HzMCAAC0OQCZJJzCXg4/KwPAA8MZk5P6/hbs586/xAABMD6Apx5fg9vPtYHgAcGM+egdfyt2U+dfwgAAmB9Ac48/9mbz+xvwF3F/+DgYHW9ly5dytrPyclJVnsa2xKIxf+F55/PWuB7H3yQ1X70xrnf48/l4e0ZBZ5ByI3gWO0RgM7iGTsAEIDOApq53Fj8EYBMoEFzBKCOH737IoAA9BWvvdgBgADoBlTwJ6iLFhqLPwJQhPXjTghAHT9690UAAegrXgiAcbwQAOMAKE+PACgDZnhXBIYXgMPDw1Xg3u+Bx9Yfbu7Bgwer+w0rBN737+pqMViMdPzDCgHPACyDigAYJDlTmhFAAJw/BCd9ACAAZtda0cTS8UcA1sOAABSlKZ06JbBTAKxLnVI8Y2+g3j8Bx9ZvXQEYJU+k8k16HOn4IwAIwLME+BaA9BXb13gIABWAqoxFAKrwRTsjAFFEog2oAIjiZDDnBLgF4FwAwqe+c5/yD/PPe8VD6noZRUxy4//oww9XET54+HDx+v5+X79DdOftW5v1X792QypVFuMgACpYGdQpAQQAAXCamnXLQgDO54cAcAuAWwB17y0j9UYAEICR8nm4vVABaBtSKgBteTObLQEEAAGwzUBmXyWAALRNEASgLW9msyUwvQCE+G+++cbm/zq6emwbmY9mj/3yW7jI2DMC4e8EPHr0SGWfo5TgVeBkDBqL/1u3v70Z7eXjVzb/jv0S4MHP/Mxi9tjvRmQsVaXp48ePVcZNHTRVCLbvG6+9+vpiaOvf/r948eLqVh89etTXQyCpgaNdEgEEIMCEACTlTbQRAhBFlNQAAUAAkhJlRyMEoIbe+H0RgCDG3p6Sjx0AXisA4186bXaYG38vFYCwMlFKq5cKwK79WVcAtt+a2FaIwnVSASjNzDH6IQAIwBiZPOguEIA+KgAIwKAX4ODbGk4Acn84JYyvtwpAuL7Y/nLv6Wo9AzD4dWO2vUuf/vTq3B/8+Mdma7OYWLtCkPoMgJUAxEr8sZhQAYgRGvt1BMB5BQABGPsCzN0dArAkhgCsP+QXyy8EIEZo7NcRAATAdYZbP0xYOn9pv1gwUgVA6h58bD3Wr3sXgO1DxVq/XEgFwDoD+54fAUAAXGew1kGauunS+Uv7xdaFAPRVAUAAYhnN65YEhhWAXd/LjcH2/gxAbP25PxzT+35jPGZ7PYx/7FsBoz0zIF0R8PYMQO0n/vB64BbAbO8Qy/0iAJ1VAGLpigDECI39OgIg+60BBGDs62X23YkJgFbJMzdAsafkY+P1/om4lQB4iXcsnrO9jgAgADk5TwUgh9Z4bREAKgBFWY0AFGFT74QAIAA5SYYA5NAar23J70CfesbgoAJQwlQM6cHBwSI+4d8GaPW3AMQ2xEBZBGICkHvP3/rbBKXzb/vVPn1vfQtge8+/lEMseRCAGKGxXy85rBCA9ZwoYSqWZQiAGMouB0IAnoYNAUhLXwQgjdOorUoOq6YCkFtqpgJABWDUizVlX9ICkDKn5za13wrwUgHQYowAaJHtY1wEQP4ZgBKmYtlCBUAMZZcDIQDLsCEA62mMAHR5mYstusVh1bRiUFsBCMmenJy0YCQW0IKBmsanYH10eYbA9oDfdU8492875P6xod6CoS0Asd8bqf1rgNLf+w/jhwD0ltGy621xuDU9YBCA7ARpGp/s1dFhQQAByEsIBIAKQF7GzNUaAYjEmwrA+YByn82Y67LS223sEzsVAG4B5GQfFYAcWuO1RQAQgKIKAAJg82aAAORx164AxFbDLYAYIV63JNBCAML9FR04uyBR8q9OH9F4VK+GAVYJIAAkiCQBKgCSNPsbCwEIYjZByV9VyPq7BPpaMQLQV7y8rxYB8B4h3fUhAAgAFQDFa0z6VgkCoBisCYdGACYM+jNbRgAQAARA8T0AAVCEy9DVBBCAaoRdD2AhAKIl6NpnACYs+Yvy7zr7O1j8rk/8qb8NH/tWQKyi0AEi0yWmxsF0kSuTIwBeI9NmXQjA+D/0E8skKgAxQoavIwCG8BOmRgASINHELQEEAAFAANxennt7tZ/QqQA4Dq6DpVEBcBAEwyUgAAgAAmB4AcamRgBihHi9hgACUEOv/74eBKB/ih3vIPzjQeFWYp8gO956k6XXHuBNFjnwJL2X6LVDgwBoE/Y9PgLgOz7qq0MAdBEjALp8Y6MjAOuEEIBYBo39OgIwdnyju0sVAOmvs0UXNkgDBGCQQA66DQRg0MAmbgsBSAQ1ajMEQDeyCIAuX0avI4AA1PHrvTcC0HsEK9cfE4Dc4Ud/ZoADPTcjaO+ZAALgOTr6a0MA9Bm7ngEByAsPApDHi9a+CSAAvuOjvToEQJuw8/ERgLwAIQB5vGjtmwAC4Ds+2qtDALQJOx8fAcgLEAKQx4vWvgkgAL7jo706BECbsPPxawWg9mtW1s8MbA/02n3khjl3vtz2ueuhfRqB0eKAAKTFfdRWCMCokU3cFwJwsCHV+o09d77c9onhp1kmgdHigABkJsBgzRGAwQKau51aAcidL2wfqwBo//4AJf3aCNK/ZwIIQM/Rq187AlDPsOsREICnFQD+gcCMBBCAGaP+53tGAOaO/9lfm+OPAU2eA2x/XgIIwLyxP9s5AjB3/BGAyePP9ucmgADMHX8EYO74IwCdx3+Uh9Ja70N6PunxWqUlAtCKtM95EACfcWm2Km4BNEOtMlGvB08Io/U+pOeTHk8lWc4ZFAFoRdrnPAiAz7g0WxUC0Ax1lxP1erB1CXtl0VpxQABGy5S8/SAAebyGa40ADBdS0Q1pHTyii5xgMK04IAATJM/KFhGAuePPMwCTx5/tz00AAZg7/gjA3PFHACaPP9ufmwACMHf8xQTgzr3bf+8M5fWrx/9hbqR97Z5bAH3Fi9VCQJIAAiBJs7+xEID+Yia6YgRAFCeDQaArAghAV+ESX6yYAIivjAGbEEAAmmBmEgi4JIAAuAxLs0UhAM1Q+5wIAfAZF1YFgRYEEIAWlP3Osc8B4Dc4I61M62tMIzFiLxBoTQABaE3c13wIgK94DLsaBGDY0LKxjgkgAB0HT2Dp7gTA+qAonb+0n0AMGQICEIBAEQEEoAjbMJ0QgCCUpQd5ab9hMomNQAAC3RFAALoLmeiC3QmA6O4YrJgAQlOMjo4Q6IYAAtBNqFQWigCoYO1/UASg/xiyAwjECCAAMUJjv54sABwIYycCu9Mh4OW68bIOHcr9jOotDghAP7mjsVIEQIMqY0LgIwJe3vC9rGP2xPAWBwRg7oxU/yEg6d8ZiCVs6/m00yfcT+4bSIzX3t7eac0eDg8Pa7pX9z05OakeY+YBDg4OZt7+9HtPeH+YntHIABCASHStLxAEYD1ACEDd2xMCUMev997W72+98+t9/QhAZwKQm3AJFzgVgFyoA7VHAAYKZsFWEt4fCkalSy8EEAAEAAHo5WpVWCcCoAC1oyERgI6CpbBU9wJQm6C1zwTUzl8bswbrRwBqg9Rx/9gzHA8ePKja3ePHj1f7X758efX12vm3z8xcv3bj3Hm057fef2z+09NT9TOgKoHorEpAPfgNDrBVQNbz10avwfoRgNogddwfAdAVkNgBbC0gCEDHF6/A0pMF4M47t147m+/6lRs3c+ZtcIAhACsEEioYCEBCQt+9d3vT6ujqcULrfpqMLgDeD2AEoJ9rZcSVIgCRqCYcoKp50UCgEICECCIACZDOacIBbHsLJMafCkBZXo/SK1kARtkw+/gEgSoBCEeLfaJM5X/zzTc2TV979fVFF772l0owrV0sXvfv318MdPHixdWBL126tHg97B92jn0CZn5d/ghA2nUyaisEYNTIpu8LAUhnNVxLBGD9GYDRBQQBGO6SztoQApCFa8jGLgVgF2kqALI5iAAgALIZxWg9EUAAeoqWzloRAB2uXYyKACAAXSQqi1QhgACoYO1q0HMFQOqht9gBEyMl/Ylfal+xdffyehif2D372D18rf533r61mTr8Pn/4DIHW/Lvi2fv83ALo5UrVWScCoMO1p1ERgJ6iJbxWBGBZAZhNIBAA4Quqs+EQgM4CprBc0VsA4fq8VQAU+HU95C4B2PWJ26oCMOon8FgFIRaHWP9YciIAMUJjv44AjB3flN0hACmUBm2DAPiuACAAg154TraFADgJhOEyEABD+NZT93ILYNYKQCw/qADECPH6GgEEgPxAACbOAQTAdwUglpoIQIwQryMA5MAaAQRg4vxoLQBhSbv2AKN/mcBs43B09ZgPgRNf/wR/4uB/tHUEYOIcQADKDtBtyvQqIAjAxBf9M1tHAMgDUQGofeo/Fg7p3wWIzTf6660FIOTZ6wHauwBs18+3AEa/wtf3hwDMHf+z3SMAE+cAAjBnBQABmPiipwJA8J8hgABMnA4IAAIwcfpPv3UqANOnABWAmVMAAUAAZs7/2ff+/wFy2V4PlH1IXgAAAABJRU5ErkJggg=="}, {"name": "pasted", "offset": [194, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 128, "height": 64, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAABACAYAAADS1n9/AAAAAXNSR0IArs4c6QAAA9ZJREFUeF7tXLGOEzEQ3Y0o4E6gbKCmv/pScByi5H9oSURDruZvkLgCCXSnKwh8BEJUkERCHBQoi0jWgL0ej+3YPu/y0kX22LPPz2/G9nrLIrNfXdf1b5cGg4HkWTUcenm6XK2Mdq8n97Tle9fl/m07v/yxNlZ9OLsobduKWa8syw3OWTjz74OCADGH/W/b2RJAOKbC4KsAXxaLTVOqooj2oQBpCGfdCwhgDdVOFXunAIvlUgtIk1KQCvD22X09kOW+H8D1N6PdeHqeRdgFAZphAgH8eB7NigsBIqbfHo0kH6AAbkPSWQUAAdwGmqrdGQKs19t1tTrj1QejFKAszSEXISAMoYK1ooYAECAYtFJDnVEA4TW3DwAFcCMKCIBVQJ5bwUVRbBwL9RP7AFQuMJ8dYx8gFNiB2gEBAgFpaibbEEApADeTuXIKDChAArY5dqFVAG6AuXIQQEagcwrgSCLr6lAAa6iSVQyaA3BegwAcQonL57NjLQH2btz08uTy+1ezHXHqF+uNoPHkFKeBphEBAbx47myUbQ4AAjiPpZcBCCBgQwjwIlA0IyhANGilhqEAUIA8zwKgAFAALAMTcCDbEPBmeqQnQKSbOuR6H28FJ6ChpgsQIA3uUIAGZyhAGsJZ9wIFsIZqp4pQAChAnstAKMBOE9vauKUA1I0crkXxIoZajzxmbSpy9+jV9mKdzlHPx/U3np5rTbl7CFR/z4/uclAHLX988WHT3p+jSRBAxhcEsOSbuLihMr/vCnA4OdvOIOXmEacAAi/1ewWdVQAQQH6/o3cEoBgrBELNAcT/dycPjBqCHECGJ1sFAAH0PEYS2OCCVYBMEO6LJFgF9HwZ2FkCjKpKOoWjPsSgMpi6lTs/eWReRzDf0mkZRzqdI51k+htPTrWmo6qyWj+p+D49uGVlF6pSax8ABFCg/d8JYMs0oQDq1ay+K8Dhk5fafQBKAThFzU4BQADzZ+JAgIYhyAHkqWKbA6gTrPMKoD5Q30PArklgdgSg3sYtmGTozvCaNlpwO31cudoodzhDlX9e/bSNZlK9aP0Rq5+zFx+9/PQ1aq0CQAAZShBA4AEF0E4yb8WBAmzxRAiQeYUQwASzaJJM9ButPygAFEDHOSgAFMA3ofeywyrgqgiXawgI/To2t39QdPw00Nn/hnBU8vv+1Sevmexr1FIAEECBkjt+diUwCKAA7AogNyDUVHDtx3L/AwrAaQ83YK4Dw7UHAnAjIpUjBHBwcYRzJTBCAEKAaQf0ypNAbkKgvF8IZHs9vF8w5/s0IEC+Y5PUsyw+XJz0idGZhMAvoiF46nWwpZUAAAAASUVORK5CYII="}, {"name": "pasted", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 514, "height": 94, "data_url": "data:image/png;base64,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"}], "relative_path": "../textures/models/ben/ov_celestial_kineceleran.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio\\ov_chronosapien_kineceleran_mask.png", "name": "ov_chronosapien_kineceleran_mask.png", "folder": "", "namespace": "", "id": "16", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "735be95a-a59c-e060-e8c1-ab9345190d29", "relative_path": "../textures/models/ben/bio/ov_chronosapien_kineceleran_mask.png", "source": "data:image/png;base64,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****************************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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio_kinecerakayah.png", "name": "bio_kinecerakayah.png", "folder": "", "namespace": "", "id": "17", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": true, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "cf1dfa36-7e91-8528-fbc5-21090b7c16e5", "layers": [{"name": "layer", "offset": [0, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 256, "height": 256, "data_url": "data:image/png;base64,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"}, {"name": "pasted", "offset": [96, 0], "scale": [1, 1], "opacity": 100, "visible": true, "blend_mode": "default", "width": 62, "height": 30, "data_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD4AAAAeCAYAAAB0ba1yAAAAAXNSR0IArs4c6QAAAZtJREFUWEdjZKAiEJaQ/w8zjo2DC6fJv358Q5FbnyIF53NxMOHU9+3HPxQ5u5bjjOQ6n2yN2Cwc9TgDAwN6jCPzv3x4gxJuO3NkEHxGbtyR+P8ripxJzVGyI45sjaTG+KjHGRgYRmMcmmyGfFIPXr4dXqqTUtpW3m0Z2nl81OOkRDcDA8OQj/EzLdbwpM7FyYu7IfL9M6ocUhVGSgPGpHoX2bUS2Rqx+WrU4wwMDKMxjiVpfBtuSf1QjSUij5PQ2UDJ10OxyTrqcVAeHyoxjtylRM+WDv0z4EIodS4DAwN6HxmmkBSPI9uHrq9duQYufaAwA2cVWatEWuMBXp2NehxLwI3GOHrzcjgkdV4BUZw9K7fpixB5/F4/appAGxWBS5JQLaEYiKavXakQLr0rMw5nRm7Wwj3Gh00TPI+PehxL8AzrGEfuYDCgJTcRARZ4cKBXX8RWZ8jV1JsPf3AmWfTqDK8+pGx2ZNsTkuozeFIf9Tgo3EZjHJJ6hnVSJ7aDgZ4aGGhcneE0H60NcW7fM/Ly+KjHCfSshkuMAwD/IGPedun7tgAAAABJRU5ErkJggg=="}], "relative_path": "../textures/models/ben/bio_kinecerakayah.png", "source": "data:image/png;base64,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"}, {"path": "C:\\Users\\<USER>\\curseforge\\minecraft\\Instances\\Testeos (2)\\addonpacks\\MOONS_OMNITRIX_ADDON_1.05\\assets\\m_ten\\textures\\models\\ben\\bio_kinecerakayah_mask.png", "name": "bio_kinecerakayah_mask.png", "folder": "", "namespace": "", "id": "18", "width": 256, "height": 256, "uv_width": 64, "uv_height": 64, "particle": false, "use_as_default": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "6e49ce21-9a44-9e32-0d88-b397aad145bd", "relative_path": "../textures/models/ben/bio_kinecerakayah_mask.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "c10bb317-bf81-dc6f-e744-34d4b7502402", "name": "animation.model.loop", "loop": "loop", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": true, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"484084a5-45a8-f775-cd36-395e0d28c46b": {"name": "antenna", "type": "bone", "keyframes": [{"channel": "position", "data_points": [{"x": 0, "y": "0", "z": "0"}], "uuid": "fa2ba026-abe1-09cb-6438-8644ae083325", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": -4, "y": 0, "z": 0}], "uuid": "1b51066d-0be3-480a-5c6b-31e69d433032", "time": 0.16666666666666666, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 4, "y": 0, "z": 0}], "uuid": "816de104-8dbc-2c03-40e8-707b0a97d845", "time": 0.5, "color": -1, "interpolation": "linear"}, {"channel": "position", "data_points": [{"x": 0, "y": 0, "z": 0}], "uuid": "6b1c5abe-adc1-e4b7-182c-1e0d6e36abef", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "15e84d26-724a-c9af-8254-8c69ea6f87b6": {"type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "6285147a-8da4-075f-09e2-777e150ce1cd", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "29402106-f281-5a1f-b379-50dedd612459", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "947291ac-1812-eff4-300a-d6108e0dd4f5", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "18ed2df1-3589-7f4b-da6b-d2a2bcbd2447", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "13b6891f-0ea7-a254-7499-1dc7b8c72957", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "584e80d8-6bed-db02-87fc-cbe7a3377422", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b762dfd4-92cd-60a8-19be-01de46404a10", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "6d47c91f-5f33-0795-dcb6-31a5ecbef67e", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "8a7a27ec-ec85-662d-20d5-bf821da94350", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "7c6d7057-e485-f1ec-4cdb-a2e273c58301", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "1da212f2-8d24-2c65-4b89-6ce3078f0c58": {"name": "wingRight", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "0b951588-0d2b-7d81-3ecb-271c41a8c21a", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "a78b2fa7-8c6b-e6cf-76b7-058d20e1b7e6", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "b66ed4de-3597-1a29-4977-73d3d1aa8a3f", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "5e049db7-c9e1-5e0b-9af8-40f58378e929", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "664f9033-2b05-bafa-2c88-44dfb2871068", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "970866ce-eeca-1f86-c292-b82c3bd9122f", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ebc44815-31ce-2b4a-d7f5-7e35a2d22866", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "48f8bb5a-056f-0ccb-9f5b-017a0bc472d8", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "ac7a3dc6-1430-1fb2-2f77-e75f322fa381", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "d3b96ed0-8ac8-91db-338c-c915342cd638", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}, "e129c0e3-c239-4e9e-bb13-315ee403caf0": {"type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "0"}], "uuid": "878f3d53-1f96-9b3d-ed5e-e8a9db1124d5", "time": 0, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "c506db17-7e9c-80f5-b4ad-e7401442e401", "time": 0.041666666666666664, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "82b021fa-fa8a-1329-8b38-76c366dc13bc", "time": 0.125, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "710e25cf-12b2-fbae-db93-ea31d3e1860b", "time": 0.20833333333333334, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "1bf7b135-8c1e-d0a4-f5ec-4c2b89f037aa", "time": 0.2916666666666667, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25\n", "z": 0}], "uuid": "d5f12279-bfae-853e-76d1-e4e6a80db06c", "time": 0.375, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "3060ed88-a12e-d158-0245-e115c5261675", "time": 0.4583333333333333, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "25", "z": 0}], "uuid": "46fa366d-cdd4-f3cc-c870-719f97d3d3ef", "time": 0.5416666666666666, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "-25", "z": 0}], "uuid": "9a3674b4-50a3-34d1-48ca-ccac31fab922", "time": 0.625, "color": -1, "interpolation": "linear"}, {"channel": "rotation", "data_points": [{"x": 0, "y": "0", "z": 0}], "uuid": "38c5208c-4b4b-c56d-254f-60057eccbc41", "time": 0.6666666666666666, "color": -1, "interpolation": "linear"}]}}}, {"uuid": "aadf3cb9-b32c-2e79-d6cb-49dd9ffd77f2", "name": "animation.model.extend_arm", "loop": "once", "override": false, "length": 0.6666666666666666, "snapping": 24, "selected": false, "saved": false, "path": "F:\\Development\\Palladium\\run\\addonpacks\\Test Pack\\assets\\test\\animations\\gecko_model_test.animation.json", "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": ""}], "animation_variable_placeholders": " ", "geckoSettings": {"formatVersion": 2, "modSDK": "Forge 1.12 - 1.16", "objectType": "OBJ_TYPE_ENTITY", "entityType": "Entity", "javaPackage": "com.example.mod", "animFileNamespace": "MODID", "animFilePath": "animations/ANIMATIONFILE.json"}}