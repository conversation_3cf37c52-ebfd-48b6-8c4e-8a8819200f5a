# Sample XMBC Language template file.
# Base version 2.19.2
#
# Changes to this template are marked:
#   -- Updated in <version> --
# or
#   -- Added in <version> --
# or
#   -- Removed in <version> (and left in template for backwards compatibility) --
#
# Ensure the file extension is .xmbclp (X-Mouse Button Control Language Pack)
# which you can "install" into XMBC simply by double clicking it, or drop it into the profile folder.
#
# All comments must be prefixed with s hash/pound '#' character.
#
# Submit translations to the Highrez Forums or directly to the Author and they will be included in
# future versions of the installation package and the language pack website.
#
#
#Describe the language pack here - this is displayed in the language drop-down in the settings tab.
#Ideally (for consistency) put your localised language name, followed by the English language name in brackets
#For example: Language=Français (French)
#
Language=简体中文 (Simplified Chinese)
Description=依次由 NightSharp YFdyh000 Yuhanawa 翻译/完善/改进

#
#Put your name here and you will be credited in the user interface
#
Author=NightSharp / YFdyh000 / Yuhanawa

#Section: Setup screen
"Scrolling && Navigation" => "滚动和导航" #Removed in 2.15 Beta 5

"Scrolling" => "滚动" #Added in 2.15 Beta 5

"Options" => "选项" #Added in 2.15 Beta 5

"Default" => "默认"
"A&dd" => "添加[&D]"
"Edi&t" => "编辑[&T]"
"Cop&y" => "复制[&O]" #Changed in 2.16 Beta 12

"Remo&ve" => "移除[&V]"
"&Settings" => "设置[&S]"
"Abo&ut" => "关于[&U]"
"&Apply" => "应用[&A]"
"&Close" => "关闭[&C]"
"Description" => "描述" #Also shared with the language settings tab!

"Process" => "进程"
"Class" => "类"
"Window Class" => "窗口类"
"Parent Class" => "父类"
"Application / Window Profiles:" => "应用程序/窗口 配置文件"
"Profile Information" => "配置文件信息"
"Setup" => "设置"
"Profile:" => "配置:"

# -- Added in 2.7 Beta 4 --
"Disabled because Scroll Lock is ON!" => "“滚动锁定键”已开启, 此项不可用！"
"Disabled under Remote Desktop!" => "远程桌面下不可用！"
# --

# -- Added in 2.8 Beta 7 --
"U&p" => "上移[&P]"
"Do&wn" => "下移[&W]"
"&Export" => "导出[&E]"
"&Import" => "导入[&I]"
"Unable to save the settings file" => "无法保存配置文件"
"Please check you installation settings." => "请检查您的安装设置。"
"Contact the developer for support." => "联系开发者以获取帮助。"
# --

# -- Added in 2.8 Beta 10 --
"Window Caption" => "窗口标题"
# --

# -- Added in 2.16 Beta 3 --
"Save &Profile" => "保存配置" #Changed in 2.16 Beta 12

"L&oad Profile" => "加载配置" #Changed in 2.16 Beta 12

"Save %s Profile..." => "保存 %s 配置"
"Load %s Profile..." => "加载 %s 配置"
"Unable to load the profile '%s'." => "无法加载配置: "
"Unable to save the profile as '%s'." => "无法保存配置:"
# --

#Section: Global strings (used everywhere)
"Layer" => "方案"
"Portable" => "便携版"
"All" => "所有"
"Not Defined" => "未定义"
"Default: All windows that do not match any profile." => "默认: 所有不匹配任何配置文件的窗口。"
"&OK" => "确定[&O]"
"&Cancel" => "取消[&C]"
"OK" => "确定"
"Cancel" => "取消"
"Global Settings" => "全局设置"

# -- Added in 2.10 Beta 10 --
"Number of  &layers" => "方案数量[&L]"


#Section: General settings tab
"General" => "常规"
"Enable profi&le switching on mouse move" => "允许在鼠标移动时切换配置文件[&L]"
"Ma&ke scroll wheel scroll window under cursor" => "滚轮可以滚动光标下的窗口(即使该窗口未被激活][&K]"
"B&ypass ALL actions when SCROLL LOCK is ON" => "“滚动锁定键”开启时跳过所有动作[&Y]"
"By&pass 'disabled' actions when SCROLL LOCK is ON" => "“滚动锁定键”开启时跳过‘已禁用’动作[&P]"
"&Disable when running under remote desktop (RDP)" => "在远程桌面(RDP]下运行时禁用[&D]"
"Show layer switch &balloon notifications" => "切换方案时显示气泡提示[&B]"
"A&ctivate window when scrolling" => "滚轮滚动时激活窗口[&C]"
"Always show the volume &On Screen Display" => "始终在屏幕上显示音量[&O]" #This option is only displayed on OS's where it is applicable - ie. Vista, 7, 2008, 2008R2

"&Enable On Screen Display for volume and brightness settings" => "启用在屏幕上显示音量和亮度设置[&E]" #This option is only displayed on OS's where it is applicable - ie. Vista, 7, 2008, 2008R2

"Enable &notifications" => "启用通知[&N]"

# -- Removed in 2.17 Beta 9
"S&wap 4th and 5th buttons" => "交换鼠标第四与第五按钮[&W]"
"Sw&ap 4th and 5th buttons when connected by remote desktop (RDP)" => "与远程桌面(RDP]连接时交换第四与第五键[&A]"

# -- Added (replaced) in 2.17 Beta 9
"S&wap 4th and 5th button actions (when set)" => "交换鼠标第四与第五按钮[&W]"
"Sw&ap 4th and 5th button actions when connected by remote desktop (RDP)" => "与远程桌面(RDP)连接时交换第四与第五键[&A]"

# -- Updated in 2.14 Beta 7 --
"NOTE: You must press OK and apply the settings on the main screen before they take effect." => "注意: 您必须点击确定并在主界面应用这些设置才能使设置生效。"

# -- Added in 2.14 Beta 10
"So&rt mouse button action list alphabetically" => "按字母顺序排列鼠标按钮动作列表[&R]"

# -- Added in 2.16 Beta 4
"Disable &Inverted Scroll when running under remote desktop (RDP)" => "在远程桌面禁用反转滚轮[&I]"

# -- Added in 2.18.6
"Redirect mouse wheel to On Screen Display when &visible" => "当可见时将滚轮重定向到屏幕[V]" #This option is only displayed on OS's where it is applicable - ie. Vista, 7, 2008, 2008R2


# -- Added in 2.19 Beta 1
"Remember c&urrent layer when restarting" => "重启时记住当前方案[&U]"
"Ignore &mouse events from touch" => "忽略触屏事件[&M]"


#
#Section: Pointer settings tab (new in 2.19 Beta 7)
"Pointer" => "光标"
"Motion" => "移动"
"Allow &XMBC to set the mouse speed and DPI" => "允许 XMBC 修改鼠标移动速度和DPI" #-- Added in 2.19 Beta 6

"Mouse &Speed" => "鼠标移动速度[&S]"
"En&hance pointer precision" => "提高光标精确度[&H]"
"Mouse speed for each cursor speed cycle:" => "光标旋转速度"
"Show cursor speed &balloon notifications" => "显示光标速度和弹窗(balloon)通知(B]"
"Change cursor when using &Movement To Scroll" => "使用移动来滚动时更改光标[&M]"

# -- Added in 2.19 Beta 8
"Change cursor when using Button &Chording" => "使用按钮和录音时更改光标[&C]"
"Change cursor when using Button H&eld" => "使用 Button Held 时更改光标[&E]"
"Change cursor to indicate active &layer" => "更改光标以表明激活方案"

# -- Moved to general tab in 2.19.2
"Show &tray icon by default" => "默认显示托盘图标[&T]"
"Cycle layers by left clickin&g tray icon" => "左键单击托盘图标切换方案[&A]" #-- Updated in 2.19.2 --


#Section: Advanced settings tab
"Advanced" => "高级"
"NOTE: You must press OK and apply the settings on the main screen before they take effect." => "注意: 您必须点击确定并在主界面应用这些设置才能使设置生效。"
"Process &Non-Client mouse messages *RECOMMENDED*" => "处理非客户端的鼠标消息。*推荐*[&N]"
"Reset &sticky buttons when any other button is pressed" => "按下其他任意鼠标按钮时重置粘滞键[&S]"
"R&eset sticky buttons when any other key is pressed" => "按下其他任意按键时重置粘滞键[&E]"
"&Ignore numlock state for Simulated Keystrokes" => "模拟击键时忽略数字锁定键状态[&I]"
"&Remap simulated input from other applications" => "映射其他应用程序的模拟输入" #-- Added in 2.17 Beta 12

"&Fixup (de-bounce) tilt wheel auto-repeat" => "修正(去抖动]倾斜滚轮的自动重复操作[&F]"
"Initial repeat r&ate" => "初始重复速率[&P]" #-- Changed in 2.16 Beta 18 - DON'T USE &P for accelerator --

"&Repeat tilt rate" => "滚轮水平向操作的重复速率[&R]" #-- Changed in 2.16 Beta 18 - DON'T USE &P for accelerator --

"(ms)" => "(毫秒)"
"Dela&y between simulated keystrokes" => "模拟击键延时[&Y]"
"&CPU Priority" => "CPU优先级[&C]"
"Realtime" => "实时"
"High" => "高"
"Above normal" => "高于正常"
"Normal" => "正常"
"Below normal" => "低于正常"
"&De-bounce (ignore) rapid mouse button clicks" => "忽略(去抖动]快速的鼠标单击[&D]"
"Start de-bounce timer when button is released" => "按钮松开时启动去抖动计时器"
"Load &US English keyboard if no equivalent is available" => "无等价可用时加载英语美国键盘[&U]"
"Reset stic&ky buttons when the layer changes" => "方案改变时重置粘滞按钮[&K]"
"Disable inactivity timer while inactive for" => "禁用非活动计时器, 当闲置"
"(min)" => "(分钟)"
"&Windows Low Level Hook Timeout (Reboot required)" => "Windows 底层 Hook 超时(需要重启)[&W]"
"Reset mouse &hooks when resuming from sleep" => "从睡眠状态恢复时重置鼠标钩子(hooks)[&H]" #-- Added in 2.19.2


#Section: Language settings tab (Added in 2.14 Beta 7)
"NOTE: You must press OK and apply the settings on the main screen before they take effect. Language changes will not be applied until the main window is re-opened." => "注意: 您必须点击“确定”并在主界面应用这些设置才能使设置生效。语言更改将在主窗口重新打开后生效。"
"Author" => "作者" #The label on the language settings page. The word 'Author' should be directly translated, i.e. Don't put your name here :)

"Language" => "语言"

#Section: Update and Logging Settings tab (Added in 2.14 Beta 7)
"Updates && Logging" => "更新和日志"
"Check for new &version every" => "检测新版本每过[&V]"
"Check for &beta versions" => "检测beta版本[&B]"
"days (at application startup)" => "天(当应用程序启动时)"
"Enable &DEBUG logging" => "启用调试日志[&D]"
"*** Debug logging will degrade performance and response times! ***" => "*** 调试日志将会降低性能和延长响应时间！ ***"
"&Log File Folder" => "日志文件文件夹[&L]"
"Select a folder to store the log file..." => "选择日志保存位置..."
"Leave blank to use the default folder!" => "留空将使用默认文件夹！"
"*** Changes to the log file folder will be applied next time you start XMBC! ***" => "*** 更改日志文件夹将在您下次启动 XMBC 时生效！"
"Updates" => "更新"
"Logging" => "日志"

#Section: Hotkey settings tab
"Hotkeys" => "快捷键"
"Enable &global hotkeys" => "启用全局快捷键[&G]"
"You can assign your hotkeys using the boxes below..." => "您可以在下面的框中配置快捷键..."
"Switch to Layer &1" => "切换至方案&1"
"Switch to Layer &2" => "切换至方案&2"
"Switch to Layer &3" => "切换至方案&3"
"Switch to Layer &4" => "切换至方案&4"
"Switch to Layer &5" => "切换至方案&5"
"Switch to Layer &6" => "切换至方案&6"
"Switch to Layer &7" => "切换至方案&7"
"Switch to Layer &8" => "切换至方案&8"
"Switch to Layer &9" => "切换至方案&9"
"Switch to Layer 1&0" => "切换至方案1&0"
"&Next Layer" => "下一个方案[&N]"
"&Previous Layer" => "上一个方案[&P]"
"&Enable/Disable XMBC" => "启用/禁用 XBMC[&E]"
"&Toggle Debug mode" => "切换调试模式开关[&T]"
"&Save desktop icon layout" => "保存桌面图标布局[&S]"
"Restore desktop icon &layout" => "恢复桌面图标布局[&L]"
# -- Updated in 2.5 beta 4 --
"Toggle &X Axis Lock" => "切换X轴锁定状态[&X]"
"Toggle &Y Axis Lock" => "切换Y轴锁定状态[&Y]"
"In&vert X Axis" => "反转X轴[&V]" #-- Added in 2.19 Beta 10

"Inve&rt Y Axis" => "反转Y轴[&R]" #-- Added in 2.19 Beta 10

"S&nap cursor to primary monitor" => "将光标锁定在主显示器上" #-- Added in 2.15 beta 3 --

"Snap cursor to &active window" => "将光标锁定在聚焦的窗口上" #-- Added in 2.15 beta 8 --

"Snap cursor to &profile window" => "将光标锁定在配置窗口上" #-- Added in 2.15 beta 8 --

"Open Settings Window" => "打开设置窗口" #-- Added in 2.10 beta 5 --

"NOTE: Only valid hotkey combinations will be allowed." => "注意: 只接受有效的快捷键组合。"

"Global hotkeys are disabled when in the settings dialog. You have to apply and close the settings before they will work." => "全局快捷键在本设置对话框打开时不起作用。您必须“应用”并关闭“设置”才能使设置生效。"
"Global hotkeys" => "全局快捷键"
"F12 can not be used as a hotkey on its own as this is reserved by Windows." => "F12 因被 Windows 自身保留, 不能作为快捷键。" #Error shown if user tries to assign F12 as a global hotkey.

# -- Updated in 2.14 Beta 7 --
"Layer Hotkeys" => "方案快捷键"
"Other Hotkeys" => "其他快捷键"
# --

#Section: Layer Modifier key settings tab
"Modifier Keys" => "辅助按键组合"
"Enab&le modifier keys (switching layers && axis locking)" => "启用辅助按键组合 (用于切换不同方案与两坐标轴的锁定状态)"
"Modifier keys are keyboard shortcuts that will temporarily activate the function while the key combination is held down. As soon as you release the key(s), the previous state will return." => "辅助按键(如Ctrl、Alt)是键盘上的特殊按键, 按住它们可以激活特定按键的其他功能。在您松开这些按键后, 所有按键变回原有状态。"
"Layers" => "方案"
"Activate Layer 1" => "激活方案1"
"Activate Layer 2" => "激活方案2"
"Activate Layer 3" => "激活方案3"
"Activate Layer 4" => "激活方案4"
"Activate Layer 5" => "激活方案5"
# -- Added in 2.10 Beta 2 --
"Activate Layer 6" => "激活方案6"
"Activate Layer 7" => "激活方案7"
"Activate Layer 8" => "激活方案8"
# -- Added in 2.10 Beta 3 --
"Activate Layer 9" => "激活方案9"
"Activate Layer 10" => "激活方案10"
# --
"Axis Locking" => "轴锁定"
"Lock X-Axis" => "锁定X轴"
"Lock Y-Axis" => "锁定Y轴"

# -- Added in 2.15 Beta 5 --
"Slow down cursor" => "减慢光标速度"

"NOTE: Axis locking requires that you enable axis locking for each application specific profile where you wish to use axis locking. This allows you to lock axis only for a specific application." => "注意: 轴锁定需要您在您希望使用该功能的每个应用程序的具体配置文件中启用该项功能。这使得您能只对一个专门的程序进行轴锁定操作。"
"None" => "无"
"Shift" => "Shift"
"Control" => "Ctrl"
"Alt" => "Alt"
"Shift + Control" => "Shift + Ctrl"
"Shift + Alt" => "Shift + Alt"
"Shift + Control + Alt" => "Shift + Ctrl + Alt"
"Control + Alt" => "Ctrl + Alt"
"Left Windows Key" => "左侧微软标徽" #-- Changed in 2.18 Beta 6 (and missing until 2.19 Beta 13!)

"Right Windows Key" => "右侧微软标徽" #-- Changed in 2.19 Beta 13

"App Menu Key" => "菜单键"

# -- Updated in 2.14 Beta 7 --
"Layer Modifiers" => "方案辅助按键"
"Other Modifiers" => "其他辅助按键"
# --

# -- Added in 2.18 Beta 6 (and missing until 2.19 Beta 13!)
"Left Windows + Shift" => "左微软标徽 + Shift"
"Left Windows + Control" => "左微软标徽 + Ctrl"
"Left Windows + Alt" => "左微软标徽 + Ait"
"Right Windows + Shift" => "右微软标徽 + Shift"
"Right Windows + Control" => "右微软标徽 + Ctrl"
"Right Windows + Alt" => "右微软标徽 + Ait"
"App Menu + Shift" => "菜单键 + Shift"
"App Menu + Control" => "菜单键 + Ctrl"
"App Menu + Alt" => "菜单键 + Alt"
"App Menu + Right Windows" => "菜单键 + 右微软标徽"
# --

#Section: Context Menus
"Edit" => "编辑"
"Copy" => "复制"
"Remove" => "移除"
"Import" => "导入"
"Export Selected" => "导出已选项"
"Export All" => "导出所有"
"&Layers" => "方案[&L]"
"Layer &1" => "方案&1"
"Layer &2" => "方案&2"
"Layer &3" => "方案&3"
"Layer &4" => "方案&4"
"Layer &5" => "方案&5"
# -- Added in 2.10 Beta 2 --
"Layer &6" => "方案&6"
"Layer &7" => "方案&7"
"Layer &8" => "方案&8"
# -- Added in 2.10 Beta 3 --
"Layer &9" => "方案&9"
"Layer 1&0" => "方案1&0"
# --
"&Setup" => "设置[&S]"
"&Hide Icon" => "隐藏图标[&H]"
"&Open log file" => "打开日志文件[&O]"
"&Disable X-Mouse Button Control" => "禁用 X-Mouse Button Control[&D]"
"S&ave desktop icon layout" => "保存桌面图标布局[&A]"
"&Restore desktop icon layout" => "恢复桌面图标布局[&R]" #-- Updated in 2.6 beta 1 --

"Exit" => "退出"

#Section: Tooltips
# -- Updated in 2.10 Beta 3
"This graphic shows the active window regions that apply to this profile.\nYou can define these regions in the profile editor." => "此图表显示了适用此配置文件的活动的窗口区域。您可以在配置文件编辑器中定义这些区域。"
"Add/Edit setting for individual layers\nor specific scrolling and navigation behaviour." => "添加/编辑单个方案\n或特定滚动和导航行为的设置。"
# -- Added in 2.10 Beta 2
"Add a new application or window specific profile." => "添加新的应用程序或窗口特定配置。"
"Clone the selected application or window specific profile." => "克隆选中的应用程序或窗口特定配置。"
"Edit the selected application or window specific profile." => "编辑选中的应用程序或窗口特定配置。"
"Delete the selected application or window specific profile." => "删除选中的应用程序或窗口特定配置。"
"Export the selected application or window specific profile." => "导出选中的应用程序或窗口特定配置。"
"Import a saved application or window specific profile." => "导入已保存的应用程序或窗口特定配置。"
"Move the selected application or window specific profile up." => "向上移动选中的应用程序或窗口特定配置。"
"Move the selected application or window specific profile down." => "向下移动选中的应用程序或窗口特定配置。"
# --

#Section: Layer tab(s)
"Layer name" => "方案名称"
"&Left Button" => "鼠标左键[&L]"
"&Right Button" => "鼠标右键[&R]"
"&Middle Button" => "鼠标中键[&M]"
"&Middle Button 2" => "鼠标中键2[&M]"
"Mouse Button &4" => "鼠标第四按钮[&4]"
"Mouse Button &5" => "鼠标第五按钮[&5]"
"W&heel Up" => "滚轮上滚[&H]"
"Wheel &Down" => "滚轮下滚[&E]" #Changed in 2.16 Beta 12

"Tilt Wheel Le&ft" => "倾斜滚轮左滚[&F]"
"Tilt Wheel Ri&ght" => "倾斜滚轮右滚[&G]"
"Reset Layer" => "重置该方案"

# -- Added in 2.4 Beta 17 --
"This item is reserved to when a layer modifier\nis used to switch to this layer.\n\nYou can not select it directly." => "该项目仅在一个方案辅助按键组合被用于\n切换至该方案时留作使用。\n\n您不能直接选择它。"
"Layer changing commands need to be applied to all affected layers.\n\nContinue?" => "方案修改操作需要被应用至所有受到影响的方案。\n\n是否继续？"
"You can not add a LAYER X modifier to this layer because another layer already has a layer modifier setup to this layer.\nDoing so would cause undetermined behaviour and is therefore not supported." => "您无法为这一方案添加一个对应于 方案X 的辅助按键组合因为要被添加至这一方案的辅助按键组合已经被另一方案(方案X)占用。\n继续这样做将会出现无法确定的行为, 因此不支持这样做。"
"If you remove a layer changing command, it will be removed from ALL affected layers.\n\nContinue?" => "如果您移除一个方案修改操作, 它将会从所有受到影响的方案中被移除。\n\n是否继续？"
#--

# -- Added in 2.8 Beta 6 --
"Switch to" => "切换至"
"after" => "在"
"seconds" => "秒之后"
"Always revert to layer 1" => "总是切换回方案1"
# --

# -- Added in 2.10 Beta 2 --
"Disable in Next/Previous layer commands" => "在下一/上一方案的命令中禁用"
# --

# -- Added in 2.14 Beta 5 --
"Are you sure you want to replace layer %d in the default profile?" => "您确定要替换默认配置中的 %d 吗？" #The %d is replaced at runtime - leave it there!

"Are you sure you want to replace layer %d in %s (%s)?" => "您确定要替换 %s (%s) 中的 %d 方案吗？" #The %d and %s are replaced at runtime - leave them there!

"Do you want to apply your settings before copying the layer?" => "您想在复制方案前应用您的设置吗？"
"Do you want to apply your settings before swapping the layer?" => "您想在交换方案前应用您的设置吗？"

# -- Added in 2.14 Beta 6 --
"Copy Layer" => "复制方案"
"Swap Layer" => "交换方案"
# --

#Section: Copy Layer dialog - Added in 2.14 Beta 5
"Destination Profile:" => "目标配置: "
"Destination Layer:" => "目标方案: "

#Section: Swap Layer dialog - Added in 2.14 Beta 5
"Swap with layer:" => "与方案交换: "
"Swap" => "交换"

#Section: Simulated keystroke methods
"(undefined)" => "(未定义)"
"(pressed)" => "(按下)"
"(released)" => "(松开)"
"(during)" => "(跟随)"
"(thread-down)" => "(独立线程按下)"
"(thread-up)" => "(独立线程松开)"
"(repeat)" => "(重复)"
"(sticky repeat)" => "(粘滞键重复)"
"(sticky hold)" => "(粘滞键按下)"
"(pressed & released)" => "(按下与松开)" #-- Added in 2.4 Beta 12 --

"(custom)" => "(自定义)" #-- Added in 2.17 Beta 9 --



#Section: Simulated Keystrokes dialog
"Simulated Keystrokes" => "模拟点击" #-- Updated in 2.4 Beta 15 --

"Enter the custom key(s)" => "请输入自定义按键"
"How to send the simulated key strokes:" => "如何键入这个模拟按键: "
"1 As mouse button is pressed" => "1 鼠标按钮按下时键入"
"2 As mouse button is released" => "2 鼠标按钮松开时键入"
"3 During (press on down, release on up)" => "3 按下期间保持状态(跟随按钮按下与松开)"
"4 In another thread as mouse button is pressed" => "4 在另一线程中鼠标按钮按下时键入"
"5 In another thread as mouse button is released" => "5 在另一线程中鼠标按钮松开时松开"
"6 Repeatedly while the button is down" => "6 按钮按下时不断重复"
"7 Sticky (repeatedly until button is pressed again)" => "7 粘滞键(重复键入, 直到按钮被再次按下)"
"8 Sticky (held down until button is pressed again)" => "8 粘滞键(按下不放, 直到按钮被再次按下)"
"9 As mouse button is pressed & when released" => "9 鼠标按钮按下与松开时均键入" #-- Added in 2.4 Beta 12 --

"Only send if profile's process is active" => "仅在配置文件的进程活动时发送键入操作"
"Block original mouse input" => "拦截原始鼠标输入" #-- Added in 2.7 Beta 5 --

"Randomise auto repeat delay by 10%" => "自动重复随机(10%)延时" #-- Added in 2.11 Beta 6 --

"Auto repeat delay" => "自动重复延迟"
"(milliseconds)" => "(毫秒)"
"0 = Windows default repeat delay && speed." => "0 = 系统的默认重复延迟与速度。"

# -- Added in 2.19 beta 3 --
"Click this button to add {} tags" => "点击此按钮添加 {} 标签"
"Hold this button and press a key to add that key's tag" => "按住这个按钮并按下一个键以添加该键的标签"

# -- Added in 2.19 Beta 4
"Drop down button to select a tag from the menu" => "按下下拉按钮并从菜单中选择一个标签"

# -- Added in 2.19 Beta 8
"Right click to select a tag from the menu" => "右击从菜单中选择一个标签" #Windows XP Only (as it down not support dropdown split button)


# -----[ Added in 2.19 Beta 4 - SimKeys {} dropdown menu ]------------------
"&Modifier Keys" => "修改按键[&M]"
"&Standard Keys" => "标准按键[&S]"
"&Direction Keys" => "方向按键[&D]"
"&Function Keys" => "功能按键[&F]"
"&Numeric Keypad" => "数字按键[N]"
"M&edia Keys" => "媒体按键[E]"
"&Browser Keys" => "浏览器按键[B]"
"Mo&use Buttons" => "鼠标按键[U]" #Changed position of & in 2.19 Beta 10

"&Layer Tags" => "方案选项卡[L]"
"Mo&vement Tags" => "移动选项卡[V]"
"&Action Tags" => "执行选项卡[A]"
"&Other Tags" => "其他选项卡[O]"
#-----[The following is the menu item for each special simkey {TAG}]
"&Control" => "左 Ctrl"
"&Right Control" => "右 Ctrl[R]"
"&Alt" => "左 Alt[A]"
"R&ight Alt (Alt Gr)" => "右 Alt[I]"
"&Shift" => "左 Shift[S]"
"Ri&ght Shift" => "右 Shift[G]"
"&Windows Key" => "左微软标徽键[W]"
"Rig&ht Windows Key" => "右微软标徽键[H]"
"A&pps (Context Menu) key" => "菜单键[P]"
"&Escape" => ""
"&Space" => "空格键[S]"
"&Return" => "返回键[R]"
"&Tab" => "TAB键[T]"
"&Backspace" => "退格键[B]"
"&Delete" => "删除键[D]"
"&Insert" => "插入键[I]"
"&Home" => "Home键[H]"
"E&nd" => "End键[N]"
"Page U&p" => "" #Changed position of & in 2.19 Beta 9

"Page Do&wn" => "" #Changed position of & in 2.19 Beta 9

"PrtScn" => "" #Removed conflicting & in 2.19 Beta 9

"Pa&use" => ""
"Break" => "" #Removed conflicting & in 2.19 Beta 9

"CAPS Lock To&ggle" => ""
"Caps Lock &On" => ""
"Caps Lock O&ff" => ""
"Scroll &Lock Toggle" => "" #Changed position of & in 2.19 Beta 9

"Scroll Lo&ck On" => ""
"Scroll Loc&k Off" => ""
"&Up" => ""
"&Down" => ""
"&Left" => ""
"&Right" => ""
"Num Lock &Toggle" => ""
"Num Lock &On" => ""
"Num Lock O&ff" => ""
"&Divide" => ""
"&Multiply" => ""
"&Subtract" => ""
"&Add" => ""
"Decima&l" => "" #Changed position of & in 2.19 Beta 9

"&Enter" => "回车键[E]"
"Num &0" => "数字0[0]"
"Num &1" => "数字1[1]"
"Num &2" => "数字2[2]"
"Num &3" => "数字3[3]"
"Num &4" => "数字4[4]"
"Num &5" => "数字5[5]"
"Num &6" => "数字6[6]"
"Num &7" => "数字7[7]"
"Num &8" => "数字8[8]"
"Num &9" => "数字9[9]"
"Volume &Up" => ""
"Volume &Down" => ""
"&Mute" => ""
"&Play/Pause" => ""
"&Stop" => ""
"&Next Track" => ""
"P&revious Track" => ""
"Launch Me&dia Select" => ""
"Ho&me" => "" #Changed position of & in 2.19 Beta 12 ##This is HOME in the web browser not the home key!

"&Back" => ""
"&Forward" => ""
"F&avourites" => ""
"R&efresh" => "" #Changed position of & in 2.19 Beta 9

"&Search" => ""
"Sto&p" => ""
"L&eft Button" => "" #Changed position of & in 2.19 Beta 9

"R&ight Button" => "" #Changed position of & in 2.19 Beta 9

"Mi&ddle Button" => "" #Changed position of & in 2.19 Beta 9

"4&th Button" => "" #Changed position of & in 2.19 Beta 9

"5t&h Button" => "" #Changed position of & in 2.19 Beta 9

"Wheel U&p" => "" #Changed position of & in 2.19 Beta 9

"Wheel D&own" => "" #Changed position of & in 2.19 Beta 9

"Wheel &Tilt Left" => ""
"Wheel Tilt Ri&ght" => "" #Changed position of & in 2.19 Beta 9

"Ne&xt Layer" => "" #Changed position of & in 2.19 Beta 9

"P&revious Layer" => "" #Changed position of & in 2.19 Beta 9

"&Last Layer" => ""
"&Move Cursor (X,Y)" => ""
"&Set position relative to primary monitor (X,Y)" => "为主显示器设置相对位置(X,Y)[&S]"
"S&et position relative to primary profile window (X,Y)" => "为主窗口设置相对位置(X,Y)[&E]"
"Se&t position relative to active window (X,Y)" => "为激活的窗口设置相对位置(X,Y)[&T]"
"S&ave position" => "保存位置[&A]"
"&Restore saved position" => ""
"&Activate window" => ""
"Activate &parent" => ""
"Activate &top" => ""
"&Busy Cursor" => ""
"&Default Cursor" => ""
"&Invert X && Y Axis" => "" #Fixed missing double & in 2.19 Beta 9

"I&nvert X Axis" => ""
"In&vert Y Axis" => "" #Fixed double & in 2.19 Beta 9

"Lock &X Axis" => ""
"Lock &Y Axis" => ""
"Set &Clipboard Text" => ""
"&Run application" => ""
"La&unch EMail" => ""
"Launch App &1" => ""
"Launch App &2" => ""
"Launch &Media Select" => ""
"Brightness &Up" => ""
"Brightness &Down" => ""
"&Wait <x> second(s)" => ""
"W&ait <x> millisecond(s)" => ""
"&Hold for <x> second(s)" => ""
"H&old for <x> millisecond(s)" => ""
"Send &virtual key code" => ""
"Send &extended virtual key code" => ""
"Send &keyboard scancode" => ""
"Send e&xtended keyboard scancode" => ""
"&Clear modifier tag state" => ""
"&Press key (Methods 1,2,4,5,6,7 && 9)" => ""
"Release ke&y (Methods 1,2,4,5,6,7 && 9)" => "" #Changed position of & in 2.19 Beta 9

"&Send only when pressing" => "" #Changed position of & in 2.19 Beta 9

"Send o&nly when releasing" => ""
"Send on&ly when repeating" => ""

#Added for 2.19 beta 8
"&Press" => "按下[&P]"
"Re&lease" => "发布[&L]"
#---------------------------------------------------------------


#Section: Simulated Keystrokes dialog - help text
# NOTE: For the following texts, \r\n is a NEW LINE and \t is a TAB. Be sure to check the layout on the simkeys
# dialog when making translations to ensure your text is visible and fits nicely.
"Type the key sequence into the box above.\r\nYou can simulate the following extended keys using the specified tags..." => "请在上方的方框内输入按键序列。\r\n您可以使用特定的标签来模拟下列的扩展按键..."
"Modifier tags:\r\n  {CTRL} {RCTRL} {ALT} {RALT} {SHIFT} {RSHIFT} {LWIN} {RWIN} {APPS}\r\n  NOTE:\tmodifier tags can be combined like {CTRL}{ALT} but apply to the NEXT KEY ONLY.\r\n\tFor example, to send CTRL+a+CTRL+s you should type '{CTRL}A{CTRL}S'." => "辅助按键标签: \r\n  {CTRL} {RCTRL} {ALT} {RALT} {SHIFT} {RSHIFT} {LWIN} {RWIN} {APPS}\r\n  注意: \t辅助按键标签可以如{CTRL}{ALT}一样组合使用, 但‘仅应用到下一个’普通按键。\r\n\t举个例子, 若想发送 CTRL+a+CTRL+s, 您应该输入‘{CTRL}A{CTRL}S’。"

# -- Updated in 2.14 beta 2 --
"Extended key tags:\r\n  {DEL} {INS} {PGUP} {PGDN} {HOME} {END} {RETURN} {ESCAPE} {BACKSPACE}\r\n  {TAB} {PRTSCN} {PAUSE} {SPACE} {CAPSLOCK} {NUMLOCK} {SCROLLLOCK} {BREAK} {CTRLBREAK}" => "扩展键标签: \r\n  {DEL} {INS} {PGUP} {PGDN} {HOME} {END} {RETURN} {ESCAPE} {BACKSPACE}\r\n  {TAB} {PRTSCN} {PAUSE} {SPACE} {CAPSLOCK} {NUMLOCK} {SCROLLLOCK} {BREAK} {CTRLBREAK}"

# -- Updated in 2.9 beta 6 --
"For example:\r\n  To close a window: {ALT}{F4}\r\n  To select all items: {CTRL}A\r\n  To type bold 'my text' in Microsoft Word: {CTRL}bmy text{CTRL}b\r\n  To send an 'r' key with a 50ms delay between the down and up, {HOLDMS:50}r\r\n  To turn OFF numlock, {NUMLOCKOFF}" => "举个例子: \r\n    关闭一个窗口: {ALT}{F4}\r\n    选中所有项目: {CTRL}A\r\n    在Microsoft Word中输入加粗的‘my text’文本: {CTRL}bmy text{CTRL}b\r\n在按钮按下与松开期间以50ms延时反复键入‘r’键: {HOLDMS:50}r\r\n关闭数字键锁定: {NUMLOCKOFF}"

# -- Added in 2.9 beta 6 --
"Special tags for send methods 1,2,4,5,6,7 & 9:\r\n\t\t\t{PRESS} Press down the following key\r\n\t\t\t{RELEASE} Release (let go of) the following key\r\n\t\t\tNOTE: You must {RELEASE} any key you {PRESS} in the same sequence!" => "发送方法 1,2,4,5,6,7,9 的特殊标签: \r\n\t\t\t{PRESS} 按下下列键: \r\n\t\t\t{RELEASE}松开下列键\r\n\t\t\t注意: 您必须在同一序列中松开({RELEASE})您按下({PRESS})的所有键！"
"To press a b and c, wait 100ms and release them: {PRESS}abc{WAITMS100}{RELEASE}cba" => "要按下 a b c 并在等待100毫秒后松开, 只需: {PRESS}abc{WAITMS100}{RELEASE}cba"
# --

# -- Updated in 2.4 Beta 10 and removed in 2.9 Beta 6 --
"NOTE: To avoid lag when using the {WAIT}, {WAITMS}, {HOLD} or {HOLDMS} tags, try using the threaded\r\nsend methods in the dropdown above." => "注意: 为避免在使用{WAIT}、{WAITMS}、{HOLD}以及{HOLDMS}标签时发生延迟卡顿, 请尝试在上方的下拉列表中\r\n选择使用独立线程的发送方式。"

# -- Updated in 2.4 Beta 15 --
"Special function tags:\t{WAIT:n} inserts a delay of <n> seconds.\r\n\t\t\t{WAITMS:n} inserts a delay of <n> milliseconds.\r\n\t\t\t{HOLD:n} holds the next key down for <n> seconds.\r\n\t\t\t{HOLDMS:n} holds the next key down for <n> milliseconds.\r\n\t\t\t{VKC:n} Sends the specified virtual key code.\r\n\t\t\t{EXT:n} Sends the specified extended virtual key code.\r\n\t\t\t{CLEAR} Clears any modifier tags {CTRL},{ALT} etc." => "特殊功能标签: \t{WAIT:n}  插入一个<n>秒的延迟。\r\n\t\t\t{WAITMS:n}  插入一个<n> 毫秒的延迟。\r\n\t\t\t{HOLD:n}  按住下一个按键不放维持<n>秒。\r\n\t\t\t{HOLDMS:n} 按住下一个按键不放维持<n>毫秒。\r\n\t\t\t{VKC:n}  发送指定的虚拟键盘编码。\r\n\t\t\t{EXT:n}  发送指定的扩展虚拟键盘编码\r\n\t\t\t{CLEAR}  清除所有辅助按键标签包括{CTRL}、{ALT}等。"

# -- Updated in 2.20 Beta 4 --
"Special function tags:\t{WAIT:n} inserts a delay of <n> seconds." => "特殊功能标签: \t{WAIT:n}  插入一个<n>秒的延迟。"
"\t\t\t{WAITMS:n} inserts a delay of <n> milliseconds." => "\t\t\t{WAITMS:n}  插入一个<n> 毫秒的延迟。"
"\t\t\t{WAITMS:x-y} inserts a random delay of between <x> and <y> milliseconds." => ""
"\t\t\t{HOLD:n} holds the next key down for <n> seconds." => "\t\t\t{HOLD:n}  按住下一个按键不放维持<n>秒。"
"\t\t\t{HOLDMS:n} holds the next key down for <n> milliseconds." => "\t\t\t{HOLDMS:n} 按住下一个按键不放维持<n>毫秒。"
"\t\t\t{VKC:n} Sends the specified virtual key code." => "\t\t\t{VKC:n}  发送指定的虚拟键盘编码。"
"\t\t\t{EXT:n} Sends the specified extended virtual key code." => "\t\t\t{EXT:n}  发送指定的扩展虚拟键盘编码"
"\t\t\t{CLEAR} Clears any modifier tags {CTRL},{ALT} etc." => "\t\t\t{CLEAR}  清除所有辅助按键标签包括{CTRL}、{ALT}等。"

#Added in 2.17 Beta 16
"\t\t\t{SC:n} Sends the specified keyboard scan code." => ""
"\t\t\t{SCE:n} Sends the specified extended keyboard scan code." => ""
"\t\t\t{CB:<text>} Copies the specified <text> to the clipboard." => ""
#

# -- Updated in 2.14 Beta 2 --
"Direction key tags:\t\t{UP} {DOWN} {LEFT} {RIGHT}\r\nFunction key tags:\t\t{F1, F2, F3 ... F24}\r\nVolume key tags:\t\t{VOL+}, {VOL-}, {MUTE}\r\nBrightness control tags:\t\t{BRIGHTNESS+}, {BRIGHTNESS-}\r\nMouse button tags:\t\t{LMB}, {RMB}, {MMB}, {MB4/XMB1}, {MB5/XMB2}\r\nMouse wheel tags:\t\t{MWUP}, {MWDN}, {TILTL}, {TILTR}\r\nNumeric keypad tags:\t{NUM0-NUM9} {NUM+} {NUM-} {NUM.} {NUM/} {NUM*} {NUMENTER}\r\nWeb/Browser keys:\t\t{BACK}, {FORWARD}, {STOP}, {REFRESH}, {WEBHOME},\r\n\t\t\t{SEARCH}, {FAVORITES}\r\nToggle keys:\t\t{NUMLOCKON}, {NUMLOCKOFF}, {CAPSLOCKON}\r\n\t\t\t{CAPSLOCKOFF}, {SCROLLLOCKON}, {SCROLLLOCKOFF}" => "方向键标签: \t\t{UP} {DOWN} {LEFT} {RIGHT}\r\n功能键标签: \t\t{F1、F2、F3 ... F24}\r\n音量键标签: \t\t{VOL+}、{VOL-}、{MUTE}\r\n\r\n亮度控制标签: \t\t{BRIGHTNESS+}、{BRIGHTNESS-}\r\n鼠标按钮标签: \t\t{LMB}、{RMB}、{MMB}、{MB4/XMB1}、{MB5/XMB2}\r\n鼠标滚轮标签: \t\t{MWUP}、{MWDN}、{TILTL}、{TILTR}\r\n数字小键盘标签: \t{NUM0-NUM9} {NUM+} {NUM-} {NUM.} {NUM/} {NUM*} {NUMENTER}\r\n网页/浏览器按键: \t\t{BACK}、{FORWARD}、{STOP}、{REFRESH}、{WEBHOME}、\r\n\t\t\t{SEARCH}、{FAVORITES}\r\n开关切换按键: \t\t{NUMLOCKON}、{NUMLOCKOFF}、{CAPSLOCKON}\r\n\t\t\t{CAPSLOCKOFF}、{SCROLLLOCKON}、{SCROLLLOCKOFF}"

# -- Removed in 2.18 Beta 12 --
"Direction key tags:\t\t{UP} {DOWN} {LEFT} {RIGHT}\r\nFunction key tags:\t\t{F1, F2, F3 ... F24}\r\nVolume key tags:\t\t{VOL+}, {VOL-}, {MUTE}\r\nBrightness control tags:\t{BRIGHTNESS+}, {BRIGHTNESS-}\r\nMouse button tags:\t\t{LMB}, {RMB}, {MMB}, {MB4/XMB1}, {MB5/XMB2}\r\nMouse wheel tags:\t\t{MWUP}, {MWDN}, {TILTL}, {TILTR}\r\nNumeric keypad tags:\t{NUM0-NUM9} {NUM+} {NUM-} {NUM.} {NUM/} {NUM*} {NUMENTER}\r\nWeb/Browser keys:\t\t{BACK}, {FORWARD}, {STOP}, {REFRESH}, {WEBHOME},\r\n\t\t\t{SEARCH}, {FAVORITES}\r\nToggle keys:\t\t{NUMLOCKON}, {NUMLOCKOFF}, {CAPSLOCKON}\r\n\t\t\t{CAPSLOCKOFF}, {SCROLLLOCKON}, {SCROLLLOCKOFF}" => "方向键标签: \t\t{UP} {DOWN} {LEFT} {RIGHT}\r\n功能键标签: \t\t{F1、F2、F3 ... F24}\r\n音量键标签: \t\t{VOL+}、{VOL-}、{MUTE}\r\n\r\n亮度控制标签: \t{BRIGHTNESS+}、{BRIGHTNESS-}\r\n鼠标按钮标签: \t\t{LMB}、{RMB}、{MMB}、{MB4/XMB1}、{MB5/XMB2}\r\n鼠标滚轮标签: \t\t{MWUP}、{MWDN}、{TILTL}、{TILTR}\r\n数字小键盘标签: \t{NUM0-NUM9} {NUM+} {NUM-} {NUM.} {NUM/} {NUM*} {NUMENTER}\r\n网页/浏览器按键: \t\t{BACK}、{FORWARD}、{STOP}、{REFRESH}、{WEBHOME}、\r\n\t\t\t{SEARCH}、{FAVORITES}\r\n开关切换按键: \t\t{NUMLOCKON}、{NUMLOCKOFF}、{CAPSLOCKON}\r\n\t\t\t{CAPSLOCKOFF}、{SCROLLLOCKON}、{SCROLLLOCKOFF}"

# -- Changed (split up) in 2.18 Beta 12
"Direction key tags:\t\t{UP} {DOWN} {LEFT} {RIGHT}" => ""
"Function key tags:\t\t{F1, F2, F3 ... F24}" => ""
"Volume key tags:\t\t{VOL+}, {VOL-}, {MUTE}" => ""
"Brightness control tags:\t{BRIGHTNESS+}, {BRIGHTNESS-}" => ""
"Mouse button tags:\t\t{LMB}, {RMB}, {MMB}, {MB4/XMB1}, {MB5/XMB2}" => ""
"Mouse button up/down tags:\tAdd a D (for down/pressed)" => ""
"\t\t\tor a U (for up/released) to the mouse button tags (above)" => ""
"\t\t\te.g. {LMBD}, {LMBU}, {RMBD}, {RMBU}, {MB4D}, {MB5U} etc." => "" #Updated in 2.18 Beta 13

"Mouse wheel tags:\t\t{MWUP}, {MWDN}, {TILTL}, {TILTR}" => ""
"Numeric keypad tags:\t{NUM0-NUM9} {NUM+} {NUM-} {NUM.} {NUM/} {NUM*} {NUMENTER}" => "" #Fixed in 2.18 Beta 13

"Web/Browser keys:\t\t{BACK}, {FORWARD}, {STOP}, {REFRESH}, {WEBHOME}, {SEARCH}, {FAVORITES}" => ""
"Toggle keys:\t\t{NUMLOCKON}, {NUMLOCKOFF}, {CAPSLOCKON}, {CAPSLOCKOFF},\r\n\t\t\t{SCROLLLOCKON}, {SCROLLLOCKOFF}" => ""
# --


# -- Added in 2.11 Beta 16 --
"Movement tags\t\t{MXSET:n} {MYSET:n} {MXADD:n} {MYADD:n}\r\n\t\t\tSET sets the mouse cursor's X or Y position to <n>\r\n\t\t\tADD moves the mouse cursor's X or Y position by <n> pixels" => "移动标签\t\t{MXSET:n} {MYSET:n} {MXADD:n} {MYADD:n}\r\n\t\t\t设定光标的 X 和 Y 位置到 <n>\r\n\t\t\t增量移动光标的 X 或 Y 位置, <n> 像素"

# -- Changed in 2.15 beta 7, 8 & 9 --
"Movement tags:\t\t{MADD:x,y} Move cursor X & Y position by <x,y> pixels" => ""
"\t\t\t{MSET:x,y} Set cursor position to <x,y> (relative to primary monitor)" => ""
"\t\t\t{PSET:x,y} Set cursor position to <x,y> (relative to profile window)" => ""
"\t\t\t{ASET:x,y} Set cursor position to <x,y> (relative to active window)" => ""
"\t\t\t{MSAVE:n} Save the current cursor position to memory <n> (1-10)" => "" #Updated in 2.16 Beta 17

"\t\t\t{MREST:n} Restore the saved cursor position from memory <n> (1-10)" => "" #Updated in 2.16 Beta 17


# -- Deprecated in 2.15 Beta 9 - they still work but are not displayed
"Movement tags:\t\t{MXADD:n} Move cursor X position (horizontally) by <n> pixels" => ""
"\t\t\t{MYADD:n} Move cursor Y position (vertically) by <n> pixels" => ""
"\t\t\t{MXSET:n} Set cursor X position to <n> (relative to primary monitor)" => ""
"\t\t\t{MYSET:n} Set cursor Y position to <n> (relative to primary monitor)" => ""
"\t\t\t{PXSET:n} Set cursor X position to <n> (relative to profile window)" => ""
"\t\t\t{PYSET:n} Set cursor Y position to <n> (relative to profile window)" => ""
"\t\t\t{AXSET:n} Set cursor X position to <n> (relative to active window)" => ""
"\t\t\t{AYSET:n} Set cursor Y position to <n> (relative to active window)" => ""
# --

# -- Added in 2.15 Beta 2 --
"Application tags:\t\t{RUN:<application>} {RUN:<full path to exe>}\r\n\t\t\tRun the specified application. If the application is not in the system PATH\r\n\t\t\tplease specify the full path to the executable (including .EXE)" => ""

# -- Added in 2.15 Beta 7 --
"Cursor Position: X,Y (AX, AY)" => ""

# -- Added in 2.16 Beta 17
"Action tags:" => ""
"\t\t{ACTIVATE} Activate the window under the cursor." => ""

# -- Added in 2.18 beta 8 --
"Layer modifier tags:" => ""
"\t{LAYER:x} Switch to layer <x>" => ""
"\t\t\t{LAYER:next} Switch to next layer" => ""
"\t\t\t{LAYER:back} Switch to preceding layer" => ""
"\t\t\t{LAYER:last} Switch to the last active layer" => ""
"On Button Pressed/Released/Repeated tags:" => ""
"\t\t\t{OD} Only send when the button is pressed" => ""
"\t\t\t{OU} Only send when the button is released" => ""
"\t\t\t{OR} Only send when repeating" => ""
# --

# -- Added in 2.17 Beta 1
"\t\t\t{ACTIVATEPARENT} Activate the parent of the window under the cursor." => ""
"\t\t\t{ACTIVATETOP} Activate the top level (root) owner of the window under the cursor." => ""
# --

# -- Updated in 2.17 Beta 10
# These functions can send any Windows Messages https://msdn.microsoft.com/en-us/library/windows/desktop/ff381405(v=vs.85).aspx
"Windows Message tags:" => ""
"\t{POSTWM:<M>,<L>,<W>} Post a message to the window under the cursor." => ""
"\t\t\t{SENDWM:<M>,<L>,<W>} Send a message to the window under the cursor." => ""
"\t\t\tWHERE:\t<M> is the message, <L> is the LPARAM value and <W> is the WPARAM value." => ""
"\t\t\tNOTE:\t<M>, <L> & <W> can be specified in decimal (eg. 16) or hex (eg. 0x10)." => ""

# -- Added in 2.19 Beta 4
"Media key tags:\t\t{MEDIAPLAY}, {MEDIASTOP}, {MEDIANEXT}, {MEDIAPREV}" => ""
"\t\t\t{CURSORBUSY} Switch the mouse pointer to the busy cursor." => ""
"\t\t\t{CURSORDEFAULT} Switch the mouse pointer to the default cursor." => ""
"\t\t\t{INVERTXY} Invert X and Y Axis." => ""
"\t\t\t{INVERTX} Invert X Axis." => ""
"\t\t\t{INVERTY} Invert Y Axis." => ""
"\t\t\t{LOCKX} Lock X Axis." => ""
"\t\t\t{LOCKY} Lock Y Axis." => ""

#Run Application dialog
"Type the name of a program, folder, document or internet resource, and X-Mouse Button Control will open it for you when the button is pressed." => "输入一个程序、文件夹、文档或者网络资源的名称, X-Mouse Button Control 将会在该按钮被按下时为您打开它。" #-- Updated in 2.19 Beta 1


#Open Explorer at specified folder dialog
"Please select a folder to open" => ""

#Section: Change Movement to Scroll Configuration -- Added in 2.15 Beta 1
"Movement to Scroll Configuration" => ""
"Sensitivity" => ""
"V&ertical" => "" #-- Added in 2.19 Beta 11

"H&orizontal" => "" #-- Added in 2.19 Beta 11

"Invert &Vertical Axis" => ""
"Invert &Horizontal Axis" => ""
"&Sticky (until next click)" => ""

"&Default Action (no movement)" => "" #-- Added in 2.17 Beta 1


#Added in 2.17 Beta 6
"Block all other buttons when sticky" => ""
"Lock Scroll Axis/Direction" => ""
"Don't lock (scroll in any direction)" => ""
"Lock to axis determined by cursor movement" => ""
"Lock to Y axis (scroll vertically)" => ""
"Lock to X axis (scroll horizontally)" => ""
#

#Added in 2.17 Beta  16
"Movement To Scroll Default Action" => ""

#Section: Scrolling & navigation tab (or Scrolling ad Options tabs post 2.15 Beta 5)
"&Invert mouse wheel scrolling" => "反转鼠标滚轮垂直滚动方向[&I]"
"I&nvert mouse horizontal (tilt) scrolling" => "反转鼠标滚轮水平(倾斜)滚动方向[&N]"
"Wheel scrolls i&n pages instead of lines" => "鼠标滚轮按页而非按行滚动[&N]"
"Lines to scroll using the scroll wheel:" => "鼠标滚轮每滚动一格将会滚动的行数" #-- Removed in 2.7 Beta 4. Left for backward compatibility --

"Lines to scroll using the scroll wheel" => "行滚动, 每当鼠标滚轮滚动一格" #-- Added/Updated in 2.7 Beta 4 --

"Enable a&xis locking modifier keys" => "启用轴锁定辅助按键组合[&X]"
"Advanced Window Scrolling" => "高级窗口滚动"
"&Scroll Method" => "滚动方式[&S]"
"&Vertical Lines" => "垂直方向行数[&V]"
"&Horizontal characters" => "水平方向字符数[&H]"
"Method 1 (SCROLL Msg)" => "方式1 (SCROLL Msg)"
"Method 2 (ScrollInfo)" => "方式2 (ScrollInfo)"
"Method 3 (Left/Right Keys)" => "方式3 (左/右方向键)"
"Method 4 (Wheel Msg)" => "方式4 (滚轮消息)"
"Method 5 (WPF Scroll Msg)" => "方式5 (WPF滚动消息)"
"None (Disable Scroll)" => "无(禁用滚动)"

# -- Added in 2.7 Beta 7 --
"&Disable Scroll Window Under Cursor" => "禁用直接滚动光标下的窗口(无论是否激活)[&D]"

# -- Added in 2.8 Beta 6 --
"Switch primary and secondary mouse buttons" => "交换鼠标第一第二按钮(左右键)"
"Don't swap buttons" => "不交换鼠标按钮"
"Swap buttons" => "交换鼠标按钮"
# --

# -- Added in 2.9 Beta 3 --
"Ignore &repeated remapped vertical scroll" => "忽略重复的重映射垂直滚动[&R]"
"Ignore repeated remapped hori&zontal scroll" => "忽略重复的重映射水平滚动[&Z]"
"for" => "即间隔小于"
"milliseconds" => "毫秒"
# --

# -- Added in 2.13 Beta 9 --
"&Force Scroll Pages/Lines (If application does not respond to setting)" => "强制滚动页/行 (如果应用不响应设置)[&F]" #This option forces the scroll lins/button swap setting to be pushed to ALL open windows.


# -- Added in 2.15 Beta 1 --
"&Clip (lock) mouse cursor to active window" => ""

# -- Added in 2.15 Beta 5 --
"&Override Mouse Speed" => "覆盖鼠标速度[&O]"

# -- Added in 2.17 Beta 3 --
"Active window on hover" => "悬停激活窗口"

"Disable &profile switching on mouse move for this profile" => ""

# -- Added in 2.19 Beta 1 --
"Disable &layer modifier keys for this profile" => ""

# -- Added in 2.19 Beta 8
"Change mouse c&ursor when profile is active" => ""

# -- Added in 2.19 Beta 9 -- Coloured cursor options....
"Red" => "红"
"Green" => "绿"
"Blue" => "蓝"
"Orange" => "橙"
"Turquoise" => "蓝绿色"
"Yellow" => "黄"

#Section: About Dialog
"XML support provided by XML++ from Michael at turboirc.com" => "XML支持由turboirc.com网站的Michael制作的XML++提供"
"XMBC is distributed freely (with no warranty or guarantee) for the use of anyone, anywhere. If you like XMBC and wish to show your appreciation please feel free to make a donation by clicking the PayPal button below." => "XMBC 为免费发布 (不需要授权, 亦无担保), 任何人可在任何地点使用。如果您喜欢XMBC并希望表达您的赞赏, 您可以点击下方的PayPal按钮进行捐赠。"
"More information can be found on my download page or user forums." => "获取更多信息可访问我的下载页面和用户论坛。"
"&Donate" => "捐赠" #-- Updated in 2.16 Beta 14

"&Homepage" => "官方网站" #-- Updated in 2.16 Beta 14

"&Forums" => "论坛" #-- Updated in 2.16 Beta 14


# -- Added in 2.16 Beta 14
"Open &Userguide" => "打开用户指南[&U]"
"Open &XMBC Folder" => "打开 XMBC 文件夹[&X]"
"&Check for Update" => "检测更新[&C]"
# --

# -- Added in 2.4 Beta 15 --
"About" => "关于"
"Version" => "版本"
"This application is designed and written by %s of %s." => "本应用程序由 %s设计和编写, 他隶属于%s。" #NOTE: The %s are replace at runtime; leave them there!

# --

#Section: Find Window Dialog
"Find Window" => "窗口查找"
"Find Method" => "查找方法"
"Click and drag the window finder icon to the window you want to select." => "点击并拖动窗口查找器图标到您想要选择的窗口。"
"Hover" => "悬停"
"Move the mouse over the window you want to select && turn on CAPS LOCK to lock the window details in place." => "移动鼠标到您想要选择的窗口上而后在合适的位置开启 大写锁定键 以锁定确认窗口细节。"
"You can also manually enter/edit the window details below." => "您也可以手动在下方输入或编辑窗口细节。"
"Window Details" => "窗口细节"
"Caption" => "标题"
"Handle" => "句柄"
"&Process" => "进程[&P]"
"C&lass" => "类[&L]"
"Pa&rent Class" => "父类[&R]"
"Only match if there is no parent class." => "仅在其没有父类时匹配。"
"Enable Regular Expression Matching" => "启用正则表达式匹配" #-- Added in 2.8 Beta 1 --

"NOTE: The fields can be empty which will match ALL (*) windows (unless you tick the checkbox for empty parent class). When set, the text must match that of the window's properties." => "注意: 这些字段可以为空, 意味着将匹配 所有(*) 窗口(除非您勾选了有关空白父类的复选框)。一旦对字段进行了设置, 该部分内容必须与窗口属性相应部分匹配。"

# -- Added in 2.8 Beta 3 --
"Window Regions" => "窗口区域"
"Activate the profile only if the cursor is over the selected areas of the window." => "光标悬停在右侧已选中的窗口方位时激活该配置文件。"
"You must define at least one window region!" => "您必须约定至少一项窗口区域！"
# --

# -- Added in 2.16 Beta 2 --
"Match Type" => "匹配类型"
"Window" => "窗口"
"Parent Window" => "父窗口"
"Window and Parent Window" => "窗口和父窗口"
"Ancestor Window" => "底层窗口"
"Window and Ancestor Window" => "窗口和底层窗口"
# --

# -- Added in 2.16 Beta 8 --
"&Activate the profile only when the cursor is in the specified area" => "只有当光标在指定区域时才激活配置[&A]"
"Top" => "上"
"Left" => "左"
"Bottom" => "下"
"Right" => "右"
"● Positive = Relative to top of window\n● Negative = Relative to bottom of window" => "" #Updated in 2.16 Beta 11

"● Positive = Relative to left of window\n● Negative = Relative to right of window" => "" #Updated in 2.16 Beta 11

"Top Left Quadrant" => "左上象限"
"Top Right Quadrant" => "右上象限"
"Bottom Left Quadrant" => "左下象限"
"Bottom Right Quadrant" => "右下象限"
"Enable caption matching" => "启用标题匹配"

# -- Added in 2.17 Beta 1
"Non regular expression matches can contain multiple search entries, separated by two pipe characters e.g. 'app1.exe||app2.exe||app3.exe'" => ""


#Section: Choose Application dialog (custom window profile)
"Choose Application" => "选择应用程序"
"Select from the list of running applications:" => "请选择正在运行的应用程序: "
"Or type in/browse to the application executable (.EXE) file" => "或者输入/浏览应用程序的可执行文件(.EXE)"
"Application" => "应用程序"
"&Specific Window" => "特定窗口[&S]"

# -- Added in 2.4 Beta 15 --
"&Refresh" => "刷新[&R]"
"Process Name" => "进程名称"
"Window Title" => "窗口标题"
# --


#Section: Errors/Message boxes
"Please close the settings dialog in order to fully apply your language selection." => "请关闭设置对话框以完全应用您的语言选择。"
"The specified application already exists.\nYou can not add two entries for the same application." => "指定的应用程序已经存在。\n您不能为同一应用程序添加两个条目。"
"You must select a profile to remove!" => "您必须选中一个配置文件以进行移除操作！"
"You can not edit the default profile!" => "您不能编辑默认配置文件！"
"You can not remove the default profile!" => "您不能移除默认配置文件！"
"You must select a profile to edit." => "您必须选中一个配置文件以进行编辑操作。"
"The specified window configuration is invalid!" => "指定窗口的设置无效！"
"The specified profile already exists.\nYou can not add two profile entries for the same application/window." => "指定的配置文件已经存在。\n您不能为同一应用程序或窗口添加两个配置项。"
"You must select an application to copy." => "您必须选择一个应用程序以进行复制操作。"
"Warning: If you say 'No', any changes will be lost!" => "警告: 如果你点击 "否", 所有修改都会丢失"
"Do you want to overwrite profile" => "是否覆盖当前配置文件" #-- Updated in 2.7 Beta 5 --

"The selected file is not a valid settings file." => "被选中的文件并不是一个有效的设置文件。"
"The log file can not be opened as it does not exist." => "无法打开日志文件因为其不存在。"
"is unable to open the log file" => "无法打开日志文件"
"Unable to save the desktop icons using your current shell and this version of" => "无法保存桌面图标因其正在使用您当前的shell并且该版本的"
"Unable to restore the desktop icons using your current shell and this version of" => "无法恢复桌面图标因其正在使用您当前的shell并且该版本的"

# -- Added in 2.16 beta 3
"Do you want to save the changes you have made to the configuration?\n\nWarning: If you say 'No', any changes will be lost!" => "是否保存您对该配置所做的更改？\n\n警告: 如果您选择“否”, 此次所有更改都将丢失！"
"Do you want to save the changes you have made to the current profile?" => "你是否想保存你对当前配置文件所做的修改？"
# --


# -- Updated in v2.4 Beta 14 --
"There is a new version available!" => "发现有新版本可用！"
"A new version is available." => "有新的版本可用。"
"Open the setup dialog and hit the about button for more information." => "打开设置对话框点击 关于 按钮以获取更多信息。"
"Click the 'Homepage' button to go and download it." => "点击‘主页’按钮去下载它。"
"There is a new beta version available!" => "发现有新的beta版本可用！"
"A new beta version is available." => "有新的beta版本可用。"
"Click the 'Forums' button to go and download it." => "点击‘论坛’按钮去下载它。"
# --

# -- Updated / Added in v2.14 Beta 3 --
"The specified profile already exists.\nYou can not add two profile entries for the same application/window/region." => "指定的配置文件已存在。\n您不能为同一个应用程序/窗口/区域添加两个配置文件。"
"The specified profile already exists.\nYou can not add two profile entries for the same window with overlapping regions." => "指定的配置文件已存在。\n您不能为同一个窗口的覆盖区域添加两个配置文件。"
# --

# -- Updated / Added in v2.16 Beta 2 --
"The specified profile already exists.\nYou can not add two profile entries for the same application/window/region with overlapping match types." => "指定的配置文件已存在。\n您不能为同一程序/窗口/区域添加两个匹配类型相同的配置文件。"
"The specified profile already exists.\nYou can not add two profile entries for the same application/window/region/match type." => ""
"The specified profile already exists.\nYou can not add two profile entries for the same application/window/match type with overlapping regions." => ""
"The specified profile already exists.\nYou can not add two profile entries for the same application/window/match type with overlapping regions and match types." => ""


"You have to select or enter a process name, or press cancel." => "您必须选中一个进程或输入一个进程名称, 否则请点击取消。"
"Enabling debug logging will decrease the performance of the mouse. It is recommended that you only enable this temporarily to assist in problem finding. As such, the debug setting will not be saved." => "开启调试日志记录会降低鼠标的性能。建议您仅在需要用其辅助以发现问题时暂时开启它。也正因此, 调试设置将不会被保存。"
"Are you sure you want to enable debug logging?" => "确定开启调试日志记录？"
"Please be aware that checking if a mouse message is 'non-client', in order to determine if it should be ignored or not, much more processing is required." => "请注意检查一个鼠标消息是否为‘非客户’的——以便确定其是否应该被忽略——需要远多于平时的处理步骤。"
"This can slow down mouse operation in general, sometimes causing strange results." => "这个一般会降低鼠标操作速度, 并偶尔会导致奇怪的问题发生。"
"Are you sure you want to ignore non-client messages?" => "确定忽略非客户端消息？"
"By default, the icon in the system tray (by the clock) is always shown. If you turn this off, there will be no icon in the system tray by default." => "默认情况下, 系统托盘(系统时钟旁边)处的本软件图标会始终显示。如果您关闭此项, 那么默认情况下系统托盘处将不会显示本软件的图标。"
"To restore the icon, you can run the program again from your start menu. This will force the icon to be displayed temporarily, allowing you to access this setup screen." => "若要恢复该图标, 您可以从您的开始菜单重新运行本程序。这将暂时地强制显示该图标, 以允许您进入此设置界面。"

#Section: Balloon pop-up windows
"Debugging enabled" => "调试模式开启"
"Debugging disabled" => "调试模式关闭"
"Successfully saved desktop icon positions" => "成功保存桌面图标位置布局"
"Successfully restored desktop icon positions" => "成功恢复桌面图标位置布局"
"Mouse button re-mapping is now disabled" => "鼠标按钮重映射现在已禁用"
"Mouse button re-mapping is now enabled" => "鼠标按钮重映射现在已启用"

#Section: Import / Export dialogs
# -- Added in 2.7 Beta 3 --
"The file to import is corrupt or not a valid file." => "用于导入的文件已损坏或者不是一个有效的文件。"
"The XML settings file is corrupt and could not be loaded.\nYou can not make any changes until this error has been fixed." => "XML设置文件已损坏且无法被加载。\n在该错误被修复前您无法进行任何更改。"
"The XML settings file is corrupt and could not be updated safely.\nPlease fix the error and try again." => "XML设置文件已损坏且无法被安全地更新。\n请修复这个问题然后重试。"
# --

#Section: Actions (displayed in the drop-down lists)
"Left Click" => "鼠标左键单击"
"Right Click" => "鼠标右键单击"
"Next Window" => "下一个窗口"
"Back" => "后退"
"Forward" => "前进"
"Middle Click" => "鼠标中键单击"
"Disable" => "禁用"
"Close (Alt+F4)" => "关闭(Alt+F4)"
"Copy (Ctrl+C)" => "复制(Ctrl+C)"
"Cut (Ctrl+X)" => "剪切(Ctrl+X)"
"Double Click" => "双击"
"Enter" => "回车"
"Maximize Window" => "最大化窗口"
"Minimize Window" => "最小化窗口"
"New" => "新建"
"Open" => "打开"
"Paste (Ctrl+V)" => "粘贴(Ctrl+V)"
"Redo (Ctrl+Y)" => "重做(Ctrl+Y)"
"Show/Hide Desktop" => "显示/隐藏桌面"
"Undo (Ctrl+Z)" => "撤销(Ctrl+Z)"
"Media - Mute" => "多媒体 - 静音"
"Media - Volume Up" => "多媒体 - 音量增大"
"Media - Volume Down" => "多媒体 - 音量减小"
"Media - Next Track" => "多媒体 - 下一首"
"Media - Last Track" => "多媒体 - 上一首"
"Media - Stop" => "多媒体 - 停止"
"Media - Play/Pause" => "多媒体 - 播放/暂停"
"Launch EMail" => "启动电子邮件客户端" #-- (re?)Added missing translation in 2.19 Beta 13

"Simulated Keys" => "模拟按键"
"Lock Workstation" => "锁定工作区"
"Open Explorer" => "打开资源管理器"
"Open RUN Command" => "打开 运行 命令"
"Restore Window" => "恢复窗口"
"Escape" => "退出"
"Open Help" => "打开帮助"
"Open Web Browser" => "打开网页浏览器"
"Browser Stop" => "浏览器网页加载停止"
"Browser Refresh" => "浏览器刷新"
"Browser Search" => "浏览器搜索" #Updated in 2.14 Beta 10

"Browser Favourites" => "浏览器收藏夹"
"** No Change (Don't intercept) **" => "** 无修改(不拦截) **"
"Run Application" => "运行应用程序"
"Mouse Wheel Up" => "鼠标滚轮上滚"
"Mouse Wheel Down" => "鼠标滚轮下滚"
"Print Screen" => "全屏截图"
"Print Active Window" => "活动窗口截图"
"Sticky Left Button [Click-Drag]" => "粘滞鼠标左键[点击并拖动]"
"Sticky Right Button" => "粘滞鼠标右键(右键按住不放)"
"Sticky Middle Button" => "粘滞鼠标中键(中键按住不放)"
"Layer (Next)" => "方案(下一个)"
"Layer (Previous)" => "方案(上一个)"
"Layer 1" => "方案 1"
"Layer 2" => "方案 2"
"Layer 3" => "方案 3"
"Layer 4" => "方案 4"
"Layer 5" => "方案 5"
#-- Added in 2.10 Beta 2
"Layer 6" => "方案 6"
"Layer 7" => "方案 7"
"Layer 8" => "方案 8"
#-- Added in 2.10 Beta 3
"Layer 9" => "方案 9"
"Layer 10" => "方案 10"
# --
"Open Explorer at specified folder" => "打开指定文件夹"
"Open Explorer at My Documents" => "打开 我的文档"
"Open Explorer at My Computer" => "打开 我的电脑"
"Open Explorer at Network Favourites" => "打开 网络收藏夹"
"Open Control Panel" => "打开 控制面板"
"Double Click Drag" => "双击并拖动"
"Mouse Wheel Tilt Left" => "鼠标倾斜滚轮水平左滚"
"Mouse Wheel Tilt Right" => "鼠标倾斜滚轮水平右滚"
"Scroll Window Up" => "向上滚动窗口"
"Scroll Window Down" => "向下滚动窗口"
"Scroll Window Left" => "向左滚动窗口"
"Scroll Window Right" => "向右滚动窗口"
"Previous Window" => "上一个窗口"
"Activate Screensaver" => "激活 屏幕保护"
"Activate Monitor Power Saving" => "激活 显示器省电模式"
"** Same As Default Profile **" => "** 与默认配置文件相同 **"
"Sticky 4th Button" => "粘滞鼠标第四按钮(第四按钮按住不放)"
"Sticky 5th Button" => "粘滞鼠标第五按钮(第五按钮按住不放)"
"Change Movement to Scroll" => "将移动改为滚动"
"Change Movement to Scroll (Sticky)" => "将移动改为滚动(粘滞)"
"Sticky Left Button [Click-Drag] X-Axis" => "粘滞鼠标左键[点击并拖动]X轴移动"
"Sticky Left Button [Click-Drag] Y-Axis" => "粘滞鼠标左键[点击并拖动]Y轴移动"

# -- Updated in 2.4 Beta 12 --
"Flip 3D" => "3D任务栏切换"
"ALT-TAB" => "Alt+TAB"
"Show sidebar" => "显示侧边栏"
"Reveal Desktop" => "显示桌面" #-- Added in 2.4 Beta 12 -- same action as Show sidebar

"** Same As Layer 1 (current profile) **" => "** 与方案 1 (当前配置文件)相同 **" #-- Added in 2.4 Beta 12 --


# -- Updated in 2.4 Beta 15 --
"Layer Modifier Revert" => "重置方案辅助按键组合"

# -- Updated in 2.6 Beta 1 (For Windows 8) --
"Task Switcher" => "任务切换器"
"Charms Bar (WIN+C)" => "超级按钮(WIN+C)"
"PC Settings (WIN+I)" => "电脑设置(WIN+I)"
"Search Charm (WIN+Q)" => "超级按钮搜索(WIN+Q)"
"Search Apps (WIN+W)" => "搜索应用(WIN+W)"
"Search Files (WIN+F)" => "搜索文件(WIN+F)"
"Metro Snap Right" => "Metro贴靠右侧"
"Metro Snap Left" => "Metro贴靠左侧"

# -- Updated in 2.14 Beta 6 (for Windows 10) --
"Cortana Search" => "小娜搜索"

# -- Added in 2.7 Beta 7 --
"Magnifier Toggle On/Off" => "屏幕放大镜开关 打开/关闭"


# -- Added in 2.8 Beta 1 --
"Brightness Down" => "降低亮度"
"Brightness Up" => "提升亮度"

# -- Added in 2.9 Beta 1 --
"Snap Modern UI Left" => "现代界面贴靠左侧"
"Snap Modern UI Right" => "现代界面贴靠右侧"
"Snap Window Left" => "窗口贴靠左侧"
"Snap Window Right" => "窗口贴靠右侧"
"Snap Window Up" => "窗口贴靠上半部"
"Snap Window Down" => "窗口贴靠下半部"
"Virtual Desktop: New" => "虚拟桌面: 新建"
"Virtual Desktop: Close" => "虚拟桌面: 关闭"
"Virtual Desktop: Switch Left" => "虚拟桌面: 向左切换"
"Virtual Desktop: Switch Right" => "虚拟桌面: 向右切换"
# --

# -- Added in 2.11 Beta 15
"Task View" => "任务视图"

# -- Added in 2.11 Beta 15
"Magnifier Zoom In" => "放大镜放大"
"Magnifier Zoom Out" => "放大镜缩小"

# -- Added in 2.12 Beta 1
"Click Button 4" => "单击第四按钮"
"Click Button 5" => "单击第五按钮"

# -- Added in 2.12 Beta 11
"Change Movement to Scroll (Lock Axis)" => "更改移动为滚动(锁定轴)"
"Change Movement to Scroll (Sticky Lock Axis)" => "更改移动为滚动(粘滞锁定轴)"

# -- Added in 2.13 Beta 1
"Change Movement to Scroll Inverted" => "更改移动为反向滚动"
"Change Movement to Scroll Inverted (Sticky)" => "更改移动为反向滚动(粘滞)"
"Change Movement to Scroll Inverted (Lock Axis)" => "更改移动为反向滚动(锁定轴)"
"Change Movement to Scroll Inverted (Sticky Lock Axis)" => "更改移动为反向滚动(粘滞锁定轴)"

# -- Added in 2.13 Beta 4
"Search Selected Text" => "搜索选中文本"

# -- Changed in 2.14 Beta 6
"Web Search Selected Text" => "网络搜索选中文本"

# -- Added in 2.14 Beta 6 (missing for some time!) --
"System Menu (WIN+X)" => "系统菜单 (WIN+X)"

# -- Added in 2.14 Beta 10
"Lock Mouse X-Axis (While pressed)" => "锁定鼠标X轴(按住期间)"
"Lock Mouse X-Axis (Toggle)" => "锁定鼠标X轴(按压切换)"
"Lock Mouse Y-Axis (While pressed)" => "锁定鼠标Y轴(按住期间)"
"Lock Mouse Y-Axis (Toggle)" => "锁定鼠标Y轴(按压切换)"

# -- Added in 2.18 Beta 4
"Drag to Move Window" => "拖曳移动窗口"
"Drag to Size Window" => "拖曳调整窗口大小"

# -- Added in 2.18 Beta 14
"Slow down mouse cursor (While pressed)" => "按下时减慢光标速度"
"Slow down mouse cursor (Sticky)" => "Sticky时减慢光标速度"

# -- Added in 2.19 Beta 7
"Cycle mouse cursor speed" => "" #NOTE: Action text also used on the global hotkey tab!


# -- Added in 2.19 Beta 10
"Open Emoji Keyboard" => "打开表情(Emoji)键盘" #-- Only available on Windows 10


# -- End of button actions list --------------------------------------------------------------------------------

#Section: Layer change notifications
# -- Added in 2.10 Beta 14 --
"Activating layer %d" => "激活方案 %d" #In the balloon tooltip when switching layers %d is the layer number

"Activating layer '%s'" => "激活方案“%s”" #In the balloon tooltip when switching layers %s is the layer name


#Section: Version/Update checking
# -- Added in 2.10 Beta 1 --
"&Check for update..." => "检查更新...[&C]"
"Click here to update." => "点击这里进行更新。"
"Right click the XMBC Icon to download the update." => "右击 XMBC 图标下载此更新。"
"Would you like to upgrade %s from v%s to v%s" => "确认将 %s 从 v%s 升级至 v%s？" #NOTE: The %s are replace at runtime; leave them there!

"Portable v%s is available. Would you like to visit the website to find out more?" => "便携版 v%s 可用。要访问网站查找吗？" #Fixed/changed in 2.19 Beta 2

"You have the latest available version of %s" => "您正在使用 %s 的最新版本"
"An error occurred while attempting to download the update package." => "尝试下载更新包时出错。"
"Please visit the website to download the latest version or try again later." => "请访问网站下载最新版, 或者稍后重试。"
"An error occurred while running the update package" => "运行更新包时出错"

# -- Updated in 2.14 Beta 9
"Click here for more details." => "点击此处查阅更多信息。"


# -- Added in 2.10 Beta 12 --
"Downloading" => "正在下载"
"Launching the update installation..." => "正在开始安装更新..."
"Downloading update..." => "正在下载更新..." #Initial progress on download dialog

"Downloaded %d of %d bytes" => "已下载 %d / %d 字节" #Progress on download dialog


#Section: Button Chording
# -- Added in 2.14 Beta 1 --
"Button Chording" => "按键和弦"
"Default (No Chord)" => "默认(无和弦)"
"Button chording allows you to make assign chorded actions that apply when one button is pressed, followed by a second button." => "按钮和弦允许您分配“和弦”操作, 即一个按钮被按下后再按下第二个按钮。"

# -- Added in 2.14 Beta 3 --
"&Block / Delay original button action until chord is established" => ""

# -- Added in 2.15 Beta 11 --
"&Unblock after" => ""
"Unblock when the mouse moves" => "" #-- Added in 2.16 Beta 2 --


# -- Added in 2.14 Beta 8
"Change Log:" => "更新日志: "
"Unable to locate change log!" => "无法找到更新日志！"
"&Upgrade" => "升级[$U]" #Fixed in 2.16 Beta 14

"&Skip" => "跳过[&S]" #Fixed in 2.16 Beta 14


# -- Added in 2.17 Beta 4 and updated in 2.17 beta 5 --
#Section Button Held Time Dependent Actions
"Button Held Time Dependent Actions" => ""
"Define different actions based on how long you hold the mouse button down for when clicking." => ""
"Normal Click" => ""
"Held for" => ""
"Held for time for action %d must be greater than that for action %d." => "" #NOTE: The %d's are interpreted at runtime. Leave them there


# Added in 2.17 Beta 6
"&Abort and revert to the normal click action when the mouse moves." => ""
"NOTE: All but the last enable action above will not be performed until the button is released (once the hold time has been determined). The last action will be started once the minimum time period is reached." => "" #Removed in 2.18 Beta 7 (see below)

"NOTE: All but the last enabled 'held for' action above will not be performed until the button is released (once the hold time has been determined). The last action will be started once the minimum time period is reached." => "" #Changed in 2.18 Beta 7


# Added in 2.18 Beta 7
"&Wait until the physical button is released before releasing the last timed action" => ""

#Added in 2.17 Beta 7
"Description (to show in button drop-down)" => ""

#Added in 2.17 beta 16
"Button Held" => ""
