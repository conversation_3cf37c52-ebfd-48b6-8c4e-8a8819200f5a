@echo off
echo 强制删除C盘遗留文件...

echo 正在尝试删除MongoDB...
rd /s /q "C:\Program Files\MongoDB" 2>nul
if exist "C:\Program Files\MongoDB" (
    echo MongoDB删除失败，尝试使用takeown...
    takeown /f "C:\Program Files\MongoDB" /r /d y >nul 2>&1
    icacls "C:\Program Files\MongoDB" /grant %USERNAME%:F /T /C >nul 2>&1
    rd /s /q "C:\Program Files\MongoDB" 2>nul
)

echo 正在尝试删除JetBrains...
rd /s /q "C:\Program Files\JetBrains" 2>nul
if exist "C:\Program Files\JetBrains" (
    echo JetBrains删除失败，尝试使用takeown...
    takeown /f "C:\Program Files\JetBrains" /r /d y >nul 2>&1
    icacls "C:\Program Files\JetBrains" /grant %USERNAME%:F /T /C >nul 2>&1
    rd /s /q "C:\Program Files\JetBrains" 2>nul
)

echo 正在尝试删除FeverGames...
rd /s /q "C:\Program Files\FeverGames" 2>nul

echo 删除操作完成！
pause
