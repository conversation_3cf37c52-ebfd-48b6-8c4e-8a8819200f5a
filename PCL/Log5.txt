[21:39:16.491] [Start] 程序版本：Release 2.10.3 (361)
[21:39:16.491] [Start] 识别码：93DA-DA21-65DF-59A3，已解锁反馈主题
[21:39:16.491] [Start] 程序路径：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[21:39:16.491] [Start] 系统编码：gb2312 (936, GBK=True)
[21:39:16.491] [Start] 管理员权限：False
[21:39:16.495] [Start] 第一阶段加载用时：63 ms
[21:39:16.664] [UI] 刷新主题：0
[21:39:16.694] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[21:39:16.694] [Start] 第二阶段加载用时：187 ms
[21:39:16.703] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[21:39:17.036] [System] 窗口已置顶，位置：(403.333333333333, 231.333333333333), 900 x 556
[21:39:17.039] [Animation] 动画线程开始
[21:39:17.039] [Start] 第三阶段加载用时：47 ms
[21:39:17.041] [Start] 加载 DLL：Json
[21:39:17.041] [System] 获取资源：Json
[21:39:17.050] [Launch] Minecraft 文件夹：C:\Users\<USER>\Desktop\.minecraft\
[21:39:17.099] [Java] 缓存中有 10 个可用的 Java：
[21:39:17.100] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[21:39:17.100] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[21:39:17.100] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[21:39:17.100] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[21:39:17.100] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[21:39:17.100] [Java]  - JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[21:39:17.100] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[21:39:17.100] [Java]  - JDK 21 (21.0.8)：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\
[21:39:17.100] [Java]  - JDK 21 (21.0.5)：C:\Program Files\Java\jdk-21\bin\
[21:39:17.100] [Java]  - JDK 11 (11.0.24)：C:\Program Files\Java\jdk-11\bin\
[21:39:17.129] [Page] 实例化：清空主页 UI，来源为空
[21:39:17.130] [Minecraft] 启动按钮：Minecraft 版本：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\
[21:39:17.153] [Loader] 加载器 登录 状态改变：Loading
[21:39:17.156] [Launch] 登录加载已开始
[21:39:17.156] [Loader] 加载器 Loader Login Legacy 状态改变：Loading
[21:39:17.157] [Launch] 登录方式：离线（Ultimate_Kevin0）
[21:39:17.157] [Loader] 加载器 Loader Login Legacy 状态改变：Finished
[21:39:17.164] [Loader] 加载器 Loader Skin Legacy 状态改变：Loading
[21:39:17.166] [Loader] 加载器 Loader Skin Legacy 状态改变：Finished
[21:39:17.175] [Skin] 载入头像成功：Loader Skin Legacy
[21:39:17.182] [Launch] 登录加载已结束
[21:39:17.182] [Loader] 加载器 登录 状态改变：Finished
[21:39:17.215] [Loader] 加载器 DlClientList Mojang 状态改变：Loading
[21:39:17.216] [Loader] 加载器 PCL 服务 状态改变：Loading
[21:39:17.216] [Server] 正在连接到 PCL 服务器
[21:39:17.217] [System] 开始清理任务缓存文件夹
[21:39:17.220] [Net] 获取网络结果：https://launchermeta.mojang.com/mc/game/version_manifest.json，超时 10000ms，要求 json
[21:39:17.222] [Net] 获取网络结果：https://pcl2-server-1253424809.file.myqcloud.com/notice.cfg?sign=1753709957-19941c8f-0-e8d441450c379f59f2f4a29bb652a3fd，超时 10000ms
[21:39:17.388] [System] 已清理任务缓存文件夹
[21:39:17.422] [Server] 服务器公告：171|364|361|5，无需更新
[21:39:17.425] [Loader] 加载器 PCL 服务 状态改变：Finished
[21:39:17.751] [System] DPI：144，系统版本：Microsoft Windows NT 10.0.22631.0，PCL 位置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[21:39:18.169] [Control] 按下按钮：版本选择
[21:39:18.181] [Control] 切换主要页面：VersionSelect, 0
[21:39:18.299] [Minecraft] 有效的 Minecraft 文件夹：modding > C:\Users\<USER>\Desktop\.minecraft\
[21:39:18.301] [Minecraft] 有效的 Minecraft 文件夹：官方启动器文件夹 > C:\Users\<USER>\AppData\Roaming\.minecraft\
[21:39:18.306] [Minecraft] 有效的 Minecraft 文件夹：(导入包)奥特英雄激战重奏 > D:\(导入包)奥特英雄激战重奏\.minecraft\
[21:39:18.307] [Minecraft] 有效的 Minecraft 文件夹：ben10 > D:\ben10\
[21:39:18.310] [Minecraft] 有效的 Minecraft 文件夹：BEN 10 > D:\BEN 10\
[21:39:18.311] [Minecraft] 有效的 Minecraft 文件夹：寰宇特摄 > D:\寰宇特摄\
[21:39:18.313] [Minecraft] 有效的 Minecraft 文件夹：paBEN10 > D:\paBEN10\
[21:39:18.314] [Minecraft] 有效的 Minecraft 文件夹：特摄 > D:\特摄\
[21:39:18.325] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[21:39:18.335] [Setup] 当前选择的 Minecraft 版本：Chinese Cuisine
[21:39:18.335] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\
[21:39:18.373] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[21:39:18.488] [Download] Mojang 官方源加载耗时：1266ms，可优先使用官方源
[21:39:18.492] [Loader] 加载器 DlClientList Mojang 状态改变：Finished
[21:39:19.358] [Control] 切换主要页面：Launch, -1
[21:39:19.358] [Control] 按下单击列表项：Chinese Cuisine
[21:39:20.164] [Control] 按下按钮：启动游戏
[21:39:20.165] [Loader] 加载器 Loader Launch 状态改变：Loading
[21:39:20.194] [Launch] 预检测已通过
[21:39:20.195] [Download] 开始后台检查资源文件索引
[21:39:20.196] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[21:39:20.196] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[21:39:20.198] [Loader] 加载器 登录 状态改变：Waiting
[21:39:20.198] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[21:39:20.198] [Loader] 加载器 获取 Java 状态改变：Loading
[21:39:20.198] [Loader] 加载器 登录 状态改变：Loading
[21:39:20.199] [Loader] 加载器 补全文件 状态改变：Loading
[21:39:20.199] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[21:39:20.199] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[21:39:20.199] [Launch] 登录加载已开始
[21:39:20.199] [Launch] 登录加载已结束
[21:39:20.199] [Loader] 加载器 登录 状态改变：Finished
[21:39:20.199] [Download] 版本 Chinese Cuisine 对应的资源文件索引为 5
[21:39:20.199] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[21:39:20.199] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[21:39:20.199] [Loader] 加载器 内存优化 状态改变：Loading
[21:39:20.199] [Download] 版本 Chinese Cuisine 对应的资源文件索引为 5
[21:39:20.199] [Taskbar] Minecraft 启动 已加入任务列表
[21:39:20.200] [Launch] 内存优化开始
[21:39:20.200] [Download] 无需更新资源文件索引，取消
[21:39:20.201] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Aborted
[21:39:20.201] [Loader] 加载器 后台更新资源文件索引 状态改变：Aborted
[21:39:20.201] [Test] 没有管理员权限，将以命令行方式进行内存优化
[21:39:20.202] [Loader] 加载器 后台下载资源文件索引 状态改变：Aborted
[21:39:20.202] [Download] 后台下载资源文件索引 已取消！
[21:39:20.202] [Loader] 加载线程 后台分析资源文件索引地址 (9) 已中断但线程正常运行至结束，输出被弃用（最新线程：-1）
[21:39:20.214] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[21:39:20.215] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[21:39:20.217] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[21:39:20.218] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[21:39:20.264] [Minecraft] 获取支持库列表：Chinese Cuisine
[21:39:20.312] [Launch] Mojang 推荐使用 Java 17
[21:39:20.313] [Launch] Java 版本需求：最低 ********，最高 999.999.999.999
[21:39:20.321] [Java] 开始完全遍历查找：C:\Users\<USER>\Desktop\
[21:39:20.334] [Java] 开始完全遍历查找：C:\Users\<USER>\Desktop\.minecraft\
[21:39:20.343] [Java] 开始完全遍历查找：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\
[21:39:20.350] [Java] 排序后的 Java 优先顺序：
[21:39:20.350] [Java]  - JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[21:39:20.350] [Java]  - JDK 21 (21.0.8)：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\
[21:39:20.350] [Java]  - JDK 21 (21.0.5)：C:\Program Files\Java\jdk-21\bin\
[21:39:20.354] [System] 执行外部命令并等待返回结果：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.exe -version
[21:39:20.442] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[21:39:20.442] [Loader] 加载器 下载支持库文件 状态改变：Loading
[21:39:20.442] [Loader] 加载器 下载支持库文件 状态改变：Finished
[21:39:20.442] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[21:39:20.540] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[21:39:20.540] [Loader] 加载器 下载资源文件 状态改变：Loading
[21:39:20.541] [Loader] 加载器 下载资源文件 状态改变：Finished
[21:39:20.541] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[21:39:20.541] [Loader] 加载器 补全文件 状态改变：Finished
[21:39:20.689] [Java] 最终选定的 Java：JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[21:39:20.689] [Launch] 选择的 Java：JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[21:39:20.689] [Loader] 加载器 获取 Java 状态改变：Finished
[21:39:32.780] [Test] 内存优化完成，可用内存改变量：4.75 G，大致剩余内存：9.13 G
[21:39:32.819] [Loader] 加载器 内存优化 状态改变：Finished
[21:39:32.832] [Loader] 加载器 获取启动参数 状态改变：Loading
[21:39:32.853] [Launch] 开始获取 Minecraft 启动参数
[21:39:32.853] [Launch] 获取新版 JVM 参数
[21:39:32.867] [Launch] 当前剩余内存：9.1G
[21:39:32.873] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[21:39:32.873] [System] 获取资源：JavaWrapper
[21:39:32.875] [Launch] 新版 JVM 参数获取成功：
[21:39:32.879] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Djava.library.path=${natives_directory} -Djna.tmpdir=${natives_directory} -Dorg.lwjgl.system.SharedLibraryExtractPath=${natives_directory} -Dio.netty.native.workdir=${natives_directory} -Dminecraft.launcher.brand=${launcher_name} -Dminecraft.launcher.version=${launcher_version} -cp ${classpath} -Djava.net.preferIPv6Addresses=system -DignoreList=bootstraplauncher,securejarhandler,asm-commons,asm-util,asm-analysis,asm-tree,asm,JarJarFileSystems,client-extra,fmlcore,javafmllanguage,lowcodelanguage,mclanguage,forge-,${version_name}.jar -DmergeModules=jna-5.10.0.jar,jna-platform-5.10.0.jar -DlibraryDirectory=${library_directory} -p ${library_directory}/cpw/mods/bootstraplauncher/1.1.2/bootstraplauncher-1.1.2.jar${classpath_separator}${library_directory}/cpw/mods/securejarhandler/2.1.10/securejarhandler-2.1.10.jar${classpath_separator}${library_directory}/org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar${classpath_separator}${library_directory}/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar${classpath_separator}${library_directory}/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar${classpath_separator}${library_directory}/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar${classpath_separator}${library_directory}/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar${classpath_separator}${library_directory}/net/minecraftforge/JarJarFileSystems/0.3.19/JarJarFileSystems-0.3.19.jar --add-modules ALL-MODULE-PATH --add-opens java.base/java.util.jar=cpw.mods.securejarhandler --add-opens java.base/java.lang.invoke=cpw.mods.securejarhandler --add-exports java.base/sun.security.util=cpw.mods.securejarhandler --add-exports jdk.naming.dns/com.sun.jndi.dns=java.naming -Xmn2304m -Xmx15360m --add-exports cpw.mods.bootstraplauncher/cpw.mods.bootstraplauncher=ALL-UNNAMED -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" cpw.mods.bootstraplauncher.BootstrapLauncher
[21:39:32.879] [Launch] 获取新版 Game 参数
[21:39:32.884] [Launch] 新版 Game 参数获取成功
[21:39:32.887] [Minecraft] 获取支持库列表：Chinese Cuisine
[21:39:32.890] [Launch] Minecraft 启动参数：
[21:39:32.893] [Launch] -Dstderr.encoding=UTF-8 -Dstdout.encoding=UTF-8 -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Djava.library.path="C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives" -Djna.tmpdir="C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives" -Dorg.lwjgl.system.SharedLibraryExtractPath="C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives" -Dio.netty.native.workdir="C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives" -Dminecraft.launcher.brand=PCL -Dminecraft.launcher.version=361 -cp "C:\Users\<USER>\Desktop\.minecraft\libraries\com\github\oshi\oshi-core\6.2.2\oshi-core-6.2.2.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\google\code\gson\gson\2.10\gson-2.10.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\ibm\icu\icu4j\71.1\icu4j-71.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\authlib\4.0.43\authlib-4.0.43.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\blocklist\1.0.10\blocklist-1.0.10.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\brigadier\1.1.8\brigadier-1.1.8.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\datafixerupper\6.0.8\datafixerupper-6.0.8.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\logging\1.1.1\logging-1.1.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\patchy\2.2.10\patchy-2.2.10.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\text2speech\1.17.9\text2speech-1.17.9.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-buffer\4.1.82.Final\netty-buffer-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-codec\4.1.82.Final\netty-codec-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-common\4.1.82.Final\netty-common-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-handler\4.1.82.Final\netty-handler-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-resolver\4.1.82.Final\netty-resolver-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-transport-classes-epoll\4.1.82.Final\netty-transport-classes-epoll-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-transport-native-unix-common\4.1.82.Final\netty-transport-native-unix-common-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-transport\4.1.82.Final\netty-transport-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\it\unimi\dsi\fastutil\8.5.9\fastutil-8.5.9.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\java\dev\jna\jna-platform\5.12.1\jna-platform-5.12.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\java\dev\jna\jna\5.12.1\jna-5.12.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\commons\commons-compress\1.21\commons-compress-1.21.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\httpcomponents\httpcore\4.4.15\httpcore-4.4.15.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\logging\log4j\log4j-api\2.19.0\log4j-api-2.19.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\logging\log4j\log4j-core\2.19.0\log4j-core-2.19.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\logging\log4j\log4j-slf4j2-impl\2.19.0\log4j-slf4j2-impl-2.19.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\joml\joml\1.10.5\joml-1.10.5.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-glfw\3.3.1\lwjgl-glfw-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-glfw\3.3.1\lwjgl-glfw-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-glfw\3.3.1\lwjgl-glfw-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-glfw\3.3.1\lwjgl-glfw-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-jemalloc\3.3.1\lwjgl-jemalloc-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-jemalloc\3.3.1\lwjgl-jemalloc-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-jemalloc\3.3.1\lwjgl-jemalloc-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-jemalloc\3.3.1\lwjgl-jemalloc-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-openal\3.3.1\lwjgl-openal-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-openal\3.3.1\lwjgl-openal-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-openal\3.3.1\lwjgl-openal-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-openal\3.3.1\lwjgl-openal-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-opengl\3.3.1\lwjgl-opengl-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-opengl\3.3.1\lwjgl-opengl-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-opengl\3.3.1\lwjgl-opengl-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-opengl\3.3.1\lwjgl-opengl-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-stb\3.3.1\lwjgl-stb-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-stb\3.3.1\lwjgl-stb-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-stb\3.3.1\lwjgl-stb-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-stb\3.3.1\lwjgl-stb-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-tinyfd\3.3.1\lwjgl-tinyfd-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-tinyfd\3.3.1\lwjgl-tinyfd-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-tinyfd\3.3.1\lwjgl-tinyfd-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-tinyfd\3.3.1\lwjgl-tinyfd-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl\3.3.1\lwjgl-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl\3.3.1\lwjgl-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl\3.3.1\lwjgl-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl\3.3.1\lwjgl-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\slf4j\slf4j-api\2.0.1\slf4j-api-2.0.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\cpw\mods\securejarhandler\2.1.10\securejarhandler-2.1.10.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\ow2\asm\asm\9.7.1\asm-9.7.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\ow2\asm\asm-commons\9.7.1\asm-commons-9.7.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\ow2\asm\asm-tree\9.7.1\asm-tree-9.7.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\ow2\asm\asm-util\9.7.1\asm-util-9.7.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\ow2\asm\asm-analysis\9.7.1\asm-analysis-9.7.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\accesstransformers\8.0.4\accesstransformers-8.0.4.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\antlr\antlr4-runtime\4.9.1\antlr4-runtime-4.9.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\eventbus\6.0.5\eventbus-6.0.5.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\forgespi\7.0.1\forgespi-7.0.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\coremods\5.2.4\coremods-5.2.4.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\cpw\mods\modlauncher\10.0.9\modlauncher-10.0.9.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\unsafe\0.2.0\unsafe-0.2.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\mergetool\1.1.5\mergetool-1.1.5-api.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\electronwill\night-config\core\3.6.4\core-3.6.4.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\electronwill\night-config\toml\3.6.4\toml-3.6.4.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\maven\maven-artifact\3.8.5\maven-artifact-3.8.5.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\jodah\typetools\0.6.3\typetools-0.6.3.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecrell\terminalconsoleappender\1.2.0\terminalconsoleappender-1.2.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\jline\jline-reader\3.12.1\jline-reader-3.12.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\jline\jline-terminal\3.12.1\jline-terminal-3.12.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\spongepowered\mixin\0.8.5\mixin-0.8.5.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\openjdk\nashorn\nashorn-core\15.4\nashorn-core-15.4.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\JarJarSelector\0.3.19\JarJarSelector-0.3.19.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\JarJarMetadata\0.3.19\JarJarMetadata-0.3.19.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\cpw\mods\bootstraplauncher\1.1.2\bootstraplauncher-1.1.2.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\JarJarFileSystems\0.3.19\JarJarFileSystems-0.3.19.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\fmlloader\1.20.1-47.4.0\fmlloader-1.20.1-47.4.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\fmlearlydisplay\1.20.1-47.4.0\fmlearlydisplay-1.20.1-47.4.0.jar;C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine.jar" -Djava.net.preferIPv6Addresses=system -DignoreList=bootstraplauncher,securejarhandler,asm-commons,asm-util,asm-analysis,asm-tree,asm,JarJarFileSystems,client-extra,fmlcore,javafmllanguage,lowcodelanguage,mclanguage,forge-,"Chinese Cuisine".jar -DmergeModules=jna-5.10.0.jar,jna-platform-5.10.0.jar -DlibraryDirectory="C:\Users\<USER>\Desktop\.minecraft\libraries" -p "C:\Users\<USER>\Desktop\.minecraft\libraries"/cpw/mods/bootstraplauncher/1.1.2/bootstraplauncher-1.1.2.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/cpw/mods/securejarhandler/2.1.10/securejarhandler-2.1.10.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/net/minecraftforge/JarJarFileSystems/0.3.19/JarJarFileSystems-0.3.19.jar --add-modules ALL-MODULE-PATH --add-opens java.base/java.util.jar=cpw.mods.securejarhandler --add-opens java.base/java.lang.invoke=cpw.mods.securejarhandler --add-exports java.base/sun.security.util=cpw.mods.securejarhandler --add-exports jdk.naming.dns/com.sun.jndi.dns=java.naming -Xmn2304m -Xmx15360m --add-exports cpw.mods.bootstraplauncher/cpw.mods.bootstraplauncher=ALL-UNNAMED -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" cpw.mods.bootstraplauncher.BootstrapLauncher --username Ultimate_Kevin0 --version "Chinese Cuisine" --gameDir "C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine" --assetsDir "C:\Users\<USER>\Desktop\.minecraft\assets" --assetIndex 5 --uuid 00000**********************CF8C3 --accessToken 00000**********************CF8C3 --clientId ${clientid} --xuid ${auth_xuid} --userType msa --versionType PCL --width 854 --height 480 --launchTarget forgeclient --fml.forgeVersion 47.4.0 --fml.mcVersion 1.20.1 --fml.forgeGroup net.minecraftforge --fml.mcpVersion 20230612.114412 
[21:39:32.893] [Loader] 加载器 获取启动参数 状态改变：Finished
[21:39:32.898] [Loader] 加载器 解压文件 状态改变：Loading
[21:39:32.903] [Launch] 正在解压 Natives 文件
[21:39:32.903] [Loader] 加载器 解压文件 状态改变：Finished
[21:39:32.917] [Loader] 加载器 预启动处理 状态改变：Loading
[21:39:32.933] [System] 已调整显卡设置：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\javaw.exe
[21:39:32.933] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[21:39:32.935] [Launch] 已将语言从 none 修改为 zh_cn
[21:39:32.936] [Loader] 加载器 预启动处理 状态改变：Finished
[21:39:32.940] [Loader] 加载器 执行自定义命令 状态改变：Loading
[21:39:32.947] [Loader] 加载器 执行自定义命令 状态改变：Finished
[21:39:32.966] [Loader] 加载器 启动进程 状态改变：Loading
[21:39:33.126] [Launch] 已启动游戏进程：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\javaw.exe
[21:39:33.127] [Loader] 加载器 启动进程 状态改变：Finished
[21:39:33.153] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[21:39:33.154] [Launch] 
[21:39:33.154] [Launch] ~ 基础参数 ~
[21:39:33.154] [Launch] PCL 版本：Release 2.10.3 (361)
[21:39:33.154] [Launch] 游戏版本：1.20.1, Forge 47.4.0（识别为 1.20.1）
[21:39:33.154] [Launch] 资源版本：5
[21:39:33.154] [Launch] 版本继承：无
[21:39:33.154] [Launch] 分配的内存：15 GB（15360 MB）
[21:39:33.154] [Launch] MC 文件夹：C:\Users\<USER>\Desktop\.minecraft\
[21:39:33.154] [Launch] 版本文件夹：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\
[21:39:33.154] [Launch] 版本隔离：True
[21:39:33.154] [Launch] HMCL 格式：False
[21:39:33.154] [Launch] Java 信息：JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[21:39:33.154] [Launch] 环境变量：未设置
[21:39:33.154] [Launch] Natives 文件夹：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives
[21:39:33.154] [Launch] 
[21:39:33.154] [Launch] ~ 登录参数 ~
[21:39:33.154] [Launch] 玩家用户名：Ultimate_Kevin0
[21:39:33.154] [Launch] AccessToken：000000000000300F9866876A97ACF8C3
[21:39:33.154] [Launch] ClientToken：000000000000300F9866876A97ACF8C3
[21:39:33.154] [Launch] UUID：000000000000300F9866876A97ACF8C3
[21:39:33.154] [Launch] 登录方式：Legacy
[21:39:33.154] [Launch] 
[21:39:33.168] [Launch] [11228] 开始 Minecraft 日志监控
[21:39:33.168] [Launch] [全局] 出现运行中的 Minecraft
[21:39:33.655] [Launch] [11228] 日志 1/5：已出现日志输出
[21:39:37.375] [Launch] [11228] Minecraft 窗口已加载：Minecraft* 1.20.1（527576）
[21:39:37.375] [Launch] [11228] Minecraft 加载已完成
[21:39:37.396] [Loader] 加载器 等待游戏窗口出现 状态改变：Finished
[21:39:37.397] [Loader] 加载器 结束处理 状态改变：Loading
[21:39:37.398] [Launch] 开始启动结束处理
[21:39:37.398] [Launch] 启动器可见性：5
[21:39:37.400] [Loader] 加载器 结束处理 状态改变：Finished
[21:39:37.421] [Loader] 加载器 Minecraft 启动 状态改变：Finished
[21:39:37.458] [Taskbar] Minecraft 启动 已移出任务列表
[21:39:37.496] [Loader] 加载器 Loader Launch 状态改变：Finished
[21:39:37.522] [UI] 弹出提示：Chinese Cuisine 启动成功！
[21:40:57.420] [Control] 按下按钮：版本选择
[21:40:57.420] [Control] 切换主要页面：VersionSelect, 0
[21:40:57.534] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[21:40:57.537] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\
[21:40:57.577] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[21:40:58.794] [Control] 按下图标按钮
[21:40:58.823] [Control] 切换主要页面：VersionSetup, 0
[21:40:59.838] [Control] 按下单选列表项：Mod 管理
[21:40:59.996] [Loader] 加载器 Mod List Loader 状态改变：Loading
[21:40:59.996] [System] 已刷新 Mod 列表
[21:41:00.421] [Mod] 共有 74 个 Mod，其中 0 个需要联网获取信息，0 个需要更新信息
[21:41:00.435] [System] 获取资源：ModData
[21:41:00.497] [Start] 加载 DLL：Imazen.WebP
[21:41:00.497] [System] 获取资源：Imazen_WebP
[21:41:00.498] [System] 获取资源：libwebp64
[21:41:00.807] [Loader] 加载器 Mod List Loader 状态改变：Finished
[21:41:00.906] [Animation] 两个动画帧间隔 360 ms
[21:41:01.624] [Animation] 两个动画帧间隔 328 ms
[21:43:43.933] [Launch] [11228] 日志 2/5：游戏用户已设置
[21:43:44.865] [Launch] [11228] 日志 3/5：LWJGL 版本已确认
[21:44:38.542] [Launch] [11228] 日志 4/5：OpenAL 已加载
[21:44:38.542] [Launch] [11228] 日志 5/5：材质已加载
[21:58:35.939] [Launch] [11228] Minecraft 已退出，返回值：0
[21:58:35.940] [Launch] [全局] 已无运行中的 Minecraft
[21:58:36.171] [Launch] [11228] Minecraft 日志监控已退出
[21:58:37.284] [Control] 按下图标按钮：BtnTitleInner
[21:58:37.284] [Control] 切换主要页面：VersionSelect, -1
[21:58:37.410] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[21:58:37.411] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\
[21:58:37.442] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[21:58:38.245] [Control] 切换主要页面：Launch, -1
[21:58:38.245] [Control] 按下单击列表项：Chinese Cuisine
[22:00:22.193] [Control] 按下按钮：启动游戏
[22:00:22.193] [Loader] 加载器 Loader Launch 状态改变：Loading
[22:00:22.201] [Launch] 预检测已通过
[22:00:22.202] [Download] 开始后台检查资源文件索引
[22:00:22.202] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[22:00:22.202] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[22:00:22.202] [Download] 版本 Chinese Cuisine 对应的资源文件索引为 5
[22:00:22.202] [Loader] 加载器 登录 状态改变：Waiting
[22:00:22.202] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[22:00:22.202] [Loader] 加载器 获取 Java 状态改变：Loading
[22:00:22.202] [Loader] 加载器 登录 状态改变：Loading
[22:00:22.202] [Loader] 加载器 补全文件 状态改变：Loading
[22:00:22.203] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[22:00:22.203] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[22:00:22.203] [Launch] 登录加载已开始
[22:00:22.203] [Download] 无需更新资源文件索引，取消
[22:00:22.203] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Aborted
[22:00:22.203] [Loader] 加载器 后台更新资源文件索引 状态改变：Aborted
[22:00:22.203] [Loader] 加载器 后台下载资源文件索引 状态改变：Aborted
[22:00:22.203] [Download] 后台下载资源文件索引 已取消！
[22:00:22.203] [Loader] 加载线程 后台分析资源文件索引地址 (42) 已中断但线程正常运行至结束，输出被弃用（最新线程：-1）
[22:00:22.203] [Launch] 登录加载已结束
[22:00:22.203] [Loader] 加载器 登录 状态改变：Finished
[22:00:22.203] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[22:00:22.203] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[22:00:22.203] [Loader] 加载器 内存优化 状态改变：Loading
[22:00:22.203] [Download] 版本 Chinese Cuisine 对应的资源文件索引为 5
[22:00:22.203] [Launch] 内存优化开始
[22:00:22.203] [Taskbar] Minecraft 启动 已加入任务列表
[22:00:22.204] [Test] 没有管理员权限，将以命令行方式进行内存优化
[22:00:22.216] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[22:00:22.216] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[22:00:22.217] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[22:00:22.217] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[22:00:22.285] [Minecraft] 获取支持库列表：Chinese Cuisine
[22:00:22.324] [Launch] Mojang 推荐使用 Java 17
[22:00:22.324] [Launch] Java 版本需求：最低 ********，最高 999.999.999.999
[22:00:22.324] [Java] 开始完全遍历查找：C:\Users\<USER>\Desktop\
[22:00:22.346] [Java] 开始完全遍历查找：C:\Users\<USER>\Desktop\.minecraft\
[22:00:22.351] [Java] 开始完全遍历查找：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\
[22:00:22.356] [Java] 排序后的 Java 优先顺序：
[22:00:22.356] [Java]  - JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[22:00:22.356] [Java]  - JDK 21 (21.0.8)：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\
[22:00:22.356] [Java]  - JDK 21 (21.0.5)：C:\Program Files\Java\jdk-21\bin\
[22:00:22.356] [Java] 最终选定的 Java：JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[22:00:22.356] [Launch] 选择的 Java：JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[22:00:22.356] [Loader] 加载器 获取 Java 状态改变：Finished
[22:00:22.455] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[22:00:22.455] [Loader] 加载器 下载支持库文件 状态改变：Loading
[22:00:22.455] [Loader] 加载器 下载支持库文件 状态改变：Finished
[22:00:22.456] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[22:00:22.567] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[22:00:22.567] [Loader] 加载器 下载资源文件 状态改变：Loading
[22:00:22.567] [Loader] 加载器 下载资源文件 状态改变：Finished
[22:00:22.567] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[22:00:22.567] [Loader] 加载器 补全文件 状态改变：Finished
[22:00:23.351] [Control] 按下图标按钮：BtnTitleMin
[22:00:33.502] [Test] 内存优化完成，可用内存改变量：3.24 G，大致剩余内存：10.3 G
[22:00:33.594] [Loader] 加载器 内存优化 状态改变：Finished
[22:00:33.597] [Loader] 加载器 获取启动参数 状态改变：Loading
[22:00:33.601] [Launch] 开始获取 Minecraft 启动参数
[22:00:33.601] [Launch] 获取新版 JVM 参数
[22:00:33.602] [Launch] 当前剩余内存：10.3G
[22:00:33.603] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[22:00:33.603] [System] 获取资源：JavaWrapper
[22:00:33.604] [Launch] 新版 JVM 参数获取成功：
[22:00:33.605] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Djava.library.path=${natives_directory} -Djna.tmpdir=${natives_directory} -Dorg.lwjgl.system.SharedLibraryExtractPath=${natives_directory} -Dio.netty.native.workdir=${natives_directory} -Dminecraft.launcher.brand=${launcher_name} -Dminecraft.launcher.version=${launcher_version} -cp ${classpath} -Djava.net.preferIPv6Addresses=system -DignoreList=bootstraplauncher,securejarhandler,asm-commons,asm-util,asm-analysis,asm-tree,asm,JarJarFileSystems,client-extra,fmlcore,javafmllanguage,lowcodelanguage,mclanguage,forge-,${version_name}.jar -DmergeModules=jna-5.10.0.jar,jna-platform-5.10.0.jar -DlibraryDirectory=${library_directory} -p ${library_directory}/cpw/mods/bootstraplauncher/1.1.2/bootstraplauncher-1.1.2.jar${classpath_separator}${library_directory}/cpw/mods/securejarhandler/2.1.10/securejarhandler-2.1.10.jar${classpath_separator}${library_directory}/org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar${classpath_separator}${library_directory}/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar${classpath_separator}${library_directory}/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar${classpath_separator}${library_directory}/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar${classpath_separator}${library_directory}/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar${classpath_separator}${library_directory}/net/minecraftforge/JarJarFileSystems/0.3.19/JarJarFileSystems-0.3.19.jar --add-modules ALL-MODULE-PATH --add-opens java.base/java.util.jar=cpw.mods.securejarhandler --add-opens java.base/java.lang.invoke=cpw.mods.securejarhandler --add-exports java.base/sun.security.util=cpw.mods.securejarhandler --add-exports jdk.naming.dns/com.sun.jndi.dns=java.naming -Xmn2304m -Xmx15360m --add-exports cpw.mods.bootstraplauncher/cpw.mods.bootstraplauncher=ALL-UNNAMED -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" cpw.mods.bootstraplauncher.BootstrapLauncher
[22:00:33.605] [Launch] 获取新版 Game 参数
[22:00:33.606] [Launch] 新版 Game 参数获取成功
[22:00:33.606] [Minecraft] 获取支持库列表：Chinese Cuisine
[22:00:33.612] [Launch] Minecraft 启动参数：
[22:00:33.615] [Launch] -Dstderr.encoding=UTF-8 -Dstdout.encoding=UTF-8 -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Djava.library.path="C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives" -Djna.tmpdir="C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives" -Dorg.lwjgl.system.SharedLibraryExtractPath="C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives" -Dio.netty.native.workdir="C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives" -Dminecraft.launcher.brand=PCL -Dminecraft.launcher.version=361 -cp "C:\Users\<USER>\Desktop\.minecraft\libraries\com\github\oshi\oshi-core\6.2.2\oshi-core-6.2.2.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\google\code\gson\gson\2.10\gson-2.10.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\ibm\icu\icu4j\71.1\icu4j-71.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\authlib\4.0.43\authlib-4.0.43.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\blocklist\1.0.10\blocklist-1.0.10.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\brigadier\1.1.8\brigadier-1.1.8.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\datafixerupper\6.0.8\datafixerupper-6.0.8.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\logging\1.1.1\logging-1.1.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\patchy\2.2.10\patchy-2.2.10.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\mojang\text2speech\1.17.9\text2speech-1.17.9.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-buffer\4.1.82.Final\netty-buffer-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-codec\4.1.82.Final\netty-codec-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-common\4.1.82.Final\netty-common-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-handler\4.1.82.Final\netty-handler-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-resolver\4.1.82.Final\netty-resolver-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-transport-classes-epoll\4.1.82.Final\netty-transport-classes-epoll-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-transport-native-unix-common\4.1.82.Final\netty-transport-native-unix-common-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\io\netty\netty-transport\4.1.82.Final\netty-transport-4.1.82.Final.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\it\unimi\dsi\fastutil\8.5.9\fastutil-8.5.9.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\java\dev\jna\jna-platform\5.12.1\jna-platform-5.12.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\java\dev\jna\jna\5.12.1\jna-5.12.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\commons\commons-compress\1.21\commons-compress-1.21.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\httpcomponents\httpcore\4.4.15\httpcore-4.4.15.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\logging\log4j\log4j-api\2.19.0\log4j-api-2.19.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\logging\log4j\log4j-core\2.19.0\log4j-core-2.19.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\logging\log4j\log4j-slf4j2-impl\2.19.0\log4j-slf4j2-impl-2.19.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\joml\joml\1.10.5\joml-1.10.5.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-glfw\3.3.1\lwjgl-glfw-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-glfw\3.3.1\lwjgl-glfw-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-glfw\3.3.1\lwjgl-glfw-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-glfw\3.3.1\lwjgl-glfw-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-jemalloc\3.3.1\lwjgl-jemalloc-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-jemalloc\3.3.1\lwjgl-jemalloc-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-jemalloc\3.3.1\lwjgl-jemalloc-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-jemalloc\3.3.1\lwjgl-jemalloc-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-openal\3.3.1\lwjgl-openal-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-openal\3.3.1\lwjgl-openal-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-openal\3.3.1\lwjgl-openal-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-openal\3.3.1\lwjgl-openal-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-opengl\3.3.1\lwjgl-opengl-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-opengl\3.3.1\lwjgl-opengl-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-opengl\3.3.1\lwjgl-opengl-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-opengl\3.3.1\lwjgl-opengl-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-stb\3.3.1\lwjgl-stb-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-stb\3.3.1\lwjgl-stb-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-stb\3.3.1\lwjgl-stb-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-stb\3.3.1\lwjgl-stb-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-tinyfd\3.3.1\lwjgl-tinyfd-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-tinyfd\3.3.1\lwjgl-tinyfd-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-tinyfd\3.3.1\lwjgl-tinyfd-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl-tinyfd\3.3.1\lwjgl-tinyfd-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl\3.3.1\lwjgl-3.3.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl\3.3.1\lwjgl-3.3.1-natives-windows.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl\3.3.1\lwjgl-3.3.1-natives-windows-arm64.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\lwjgl\lwjgl\3.3.1\lwjgl-3.3.1-natives-windows-x86.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\slf4j\slf4j-api\2.0.1\slf4j-api-2.0.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\cpw\mods\securejarhandler\2.1.10\securejarhandler-2.1.10.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\ow2\asm\asm\9.7.1\asm-9.7.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\ow2\asm\asm-commons\9.7.1\asm-commons-9.7.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\ow2\asm\asm-tree\9.7.1\asm-tree-9.7.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\ow2\asm\asm-util\9.7.1\asm-util-9.7.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\ow2\asm\asm-analysis\9.7.1\asm-analysis-9.7.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\accesstransformers\8.0.4\accesstransformers-8.0.4.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\antlr\antlr4-runtime\4.9.1\antlr4-runtime-4.9.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\eventbus\6.0.5\eventbus-6.0.5.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\forgespi\7.0.1\forgespi-7.0.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\coremods\5.2.4\coremods-5.2.4.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\cpw\mods\modlauncher\10.0.9\modlauncher-10.0.9.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\unsafe\0.2.0\unsafe-0.2.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\mergetool\1.1.5\mergetool-1.1.5-api.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\electronwill\night-config\core\3.6.4\core-3.6.4.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\com\electronwill\night-config\toml\3.6.4\toml-3.6.4.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\apache\maven\maven-artifact\3.8.5\maven-artifact-3.8.5.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\jodah\typetools\0.6.3\typetools-0.6.3.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecrell\terminalconsoleappender\1.2.0\terminalconsoleappender-1.2.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\jline\jline-reader\3.12.1\jline-reader-3.12.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\jline\jline-terminal\3.12.1\jline-terminal-3.12.1.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\spongepowered\mixin\0.8.5\mixin-0.8.5.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\org\openjdk\nashorn\nashorn-core\15.4\nashorn-core-15.4.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\JarJarSelector\0.3.19\JarJarSelector-0.3.19.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\JarJarMetadata\0.3.19\JarJarMetadata-0.3.19.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\cpw\mods\bootstraplauncher\1.1.2\bootstraplauncher-1.1.2.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\JarJarFileSystems\0.3.19\JarJarFileSystems-0.3.19.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\fmlloader\1.20.1-47.4.0\fmlloader-1.20.1-47.4.0.jar;C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\fmlearlydisplay\1.20.1-47.4.0\fmlearlydisplay-1.20.1-47.4.0.jar;C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine.jar" -Djava.net.preferIPv6Addresses=system -DignoreList=bootstraplauncher,securejarhandler,asm-commons,asm-util,asm-analysis,asm-tree,asm,JarJarFileSystems,client-extra,fmlcore,javafmllanguage,lowcodelanguage,mclanguage,forge-,"Chinese Cuisine".jar -DmergeModules=jna-5.10.0.jar,jna-platform-5.10.0.jar -DlibraryDirectory="C:\Users\<USER>\Desktop\.minecraft\libraries" -p "C:\Users\<USER>\Desktop\.minecraft\libraries"/cpw/mods/bootstraplauncher/1.1.2/bootstraplauncher-1.1.2.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/cpw/mods/securejarhandler/2.1.10/securejarhandler-2.1.10.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar;"C:\Users\<USER>\Desktop\.minecraft\libraries"/net/minecraftforge/JarJarFileSystems/0.3.19/JarJarFileSystems-0.3.19.jar --add-modules ALL-MODULE-PATH --add-opens java.base/java.util.jar=cpw.mods.securejarhandler --add-opens java.base/java.lang.invoke=cpw.mods.securejarhandler --add-exports java.base/sun.security.util=cpw.mods.securejarhandler --add-exports jdk.naming.dns/com.sun.jndi.dns=java.naming -Xmn2304m -Xmx15360m --add-exports cpw.mods.bootstraplauncher/cpw.mods.bootstraplauncher=ALL-UNNAMED -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" cpw.mods.bootstraplauncher.BootstrapLauncher --username Ultimate_Kevin0 --version "Chinese Cuisine" --gameDir "C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine" --assetsDir "C:\Users\<USER>\Desktop\.minecraft\assets" --assetIndex 5 --uuid 00000**********************CF8C3 --accessToken 00000**********************CF8C3 --clientId ${clientid} --xuid ${auth_xuid} --userType msa --versionType PCL --width 854 --height 480 --launchTarget forgeclient --fml.forgeVersion 47.4.0 --fml.mcVersion 1.20.1 --fml.forgeGroup net.minecraftforge --fml.mcpVersion 20230612.114412 
[22:00:33.615] [Loader] 加载器 获取启动参数 状态改变：Finished
[22:00:33.616] [Loader] 加载器 解压文件 状态改变：Loading
[22:00:33.617] [Launch] 正在解压 Natives 文件
[22:00:33.617] [Launch] 删除：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives\glfw.dll
[22:00:33.617] [Launch] 删除：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives\jemalloc.dll
[22:00:33.617] [Launch] 删除：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives\jna8769041825783775959.dll
[22:00:33.619] [Launch] 删除：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives\jna8769041825783775959.dll.x
[22:00:33.619] [Launch] 删除：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives\lwjgl.dll
[22:00:33.620] [Launch] 删除：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives\lwjgl_opengl.dll
[22:00:33.620] [Launch] 删除：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives\lwjgl_stb.dll
[22:00:33.621] [Launch] 删除：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives\OpenAL.dll
[22:00:33.621] [Loader] 加载器 解压文件 状态改变：Finished
[22:00:33.643] [Loader] 加载器 预启动处理 状态改变：Loading
[22:00:33.644] [System] 无需调整显卡设置：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\javaw.exe
[22:00:33.644] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[22:00:33.644] [Launch] 需要的语言为 zh_cn，当前语言为 zh_cn，无需修改
[22:00:33.645] [Loader] 加载器 预启动处理 状态改变：Finished
[22:00:33.688] [Loader] 加载器 执行自定义命令 状态改变：Loading
[22:00:33.691] [Loader] 加载器 执行自定义命令 状态改变：Finished
[22:00:33.739] [Loader] 加载器 启动进程 状态改变：Loading
[22:00:33.849] [Launch] 已启动游戏进程：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\javaw.exe
[22:00:33.849] [Loader] 加载器 启动进程 状态改变：Finished
[22:00:33.849] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[22:00:33.850] [Launch] 
[22:00:33.850] [Launch] ~ 基础参数 ~
[22:00:33.850] [Launch] PCL 版本：Release 2.10.3 (361)
[22:00:33.850] [Launch] 游戏版本：1.20.1, Forge 47.4.0（识别为 1.20.1）
[22:00:33.850] [Launch] 资源版本：5
[22:00:33.850] [Launch] 版本继承：无
[22:00:33.850] [Launch] 分配的内存：15 GB（15360 MB）
[22:00:33.850] [Launch] MC 文件夹：C:\Users\<USER>\Desktop\.minecraft\
[22:00:33.850] [Launch] 版本文件夹：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\
[22:00:33.850] [Launch] 版本隔离：True
[22:00:33.850] [Launch] HMCL 格式：False
[22:00:33.850] [Launch] Java 信息：JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[22:00:33.850] [Launch] 环境变量：未设置
[22:00:33.850] [Launch] Natives 文件夹：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\Chinese Cuisine-natives
[22:00:33.850] [Launch] 
[22:00:33.850] [Launch] ~ 登录参数 ~
[22:00:33.851] [Launch] 玩家用户名：Ultimate_Kevin0
[22:00:33.851] [Launch] AccessToken：000000000000300F9866876A97ACF8C3
[22:00:33.851] [Launch] ClientToken：000000000000300F9866876A97ACF8C3
[22:00:33.851] [Launch] UUID：000000000000300F9866876A97ACF8C3
[22:00:33.851] [Launch] 登录方式：Legacy
[22:00:33.851] [Launch] 
[22:00:33.851] [Launch] [5908] 开始 Minecraft 日志监控
[22:00:33.851] [Launch] [全局] 出现运行中的 Minecraft
[22:00:34.094] [Launch] [5908] 日志 1/5：已出现日志输出
[22:00:37.810] [Launch] [5908] Minecraft 窗口已加载：Minecraft* 1.20.1（1772824）
[22:00:37.810] [Launch] [5908] Minecraft 加载已完成
[22:00:37.871] [Loader] 加载器 等待游戏窗口出现 状态改变：Finished
[22:00:37.873] [Loader] 加载器 结束处理 状态改变：Loading
[22:00:37.875] [Launch] 开始启动结束处理
[22:00:37.875] [Launch] 启动器可见性：5
[22:00:37.880] [Loader] 加载器 结束处理 状态改变：Finished
[22:00:37.929] [Loader] 加载器 Minecraft 启动 状态改变：Finished
[22:00:37.933] [Taskbar] Minecraft 启动 已移出任务列表
[22:00:38.138] [Loader] 加载器 Loader Launch 状态改变：Finished
[22:00:38.184] [UI] 弹出提示：Chinese Cuisine 启动成功！
[22:00:55.450] [Launch] [5908] 日志 2/5：游戏用户已设置
[22:00:55.681] [Launch] [5908] 日志 3/5：LWJGL 版本已确认
[22:01:49.701] [Launch] [5908] 日志 4/5：OpenAL 已加载
[22:01:49.935] [Launch] [5908] 日志 5/5：材质已加载
[22:08:14.189] [Launch] [5908] Minecraft 已退出，返回值：0
[22:08:14.189] [Launch] [全局] 已无运行中的 Minecraft
[22:08:14.422] [Launch] [5908] Minecraft 日志监控已退出
[23:02:58.158] [Animation] 两个动画帧间隔 703 ms
[01:07:27.203] [Animation] 两个动画帧间隔 719 ms
[01:26:11.133] [Animation] 两个动画帧间隔 1625 ms
[01:34:23.326] [Control] 按下按钮：版本选择
[01:34:23.329] [Control] 切换主要页面：VersionSelect, 0
[01:34:23.442] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[01:34:23.442] [Minecraft] 启动按钮：正在加载 Minecraft 版本
[01:34:23.444] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\
[01:34:23.481] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[01:34:24.398] [Report] FPS 0, 动画 8, 下载中 0（0 B/s）
[01:34:25.259] [Control] 按下单选列表项：特摄
[01:34:25.263] [Setup] 当前选择的 Minecraft 文件夹：D:\特摄\
[01:34:25.263] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[01:34:25.264] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[01:34:25.267] [Setup] 当前选择的 Minecraft 版本：MARSHY'S SECRETS OF THE OMNITRIX - BETA PALLADIUM MODPACK
[01:34:25.267] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\特摄\versions\MARSHY'S SECRETS OF THE OMNITRIX - BETA PALLADIUM MODPACK\
[01:34:25.278] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[01:34:25.390] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[01:34:25.571] [Control] 按下单选列表项：paBEN10
[01:34:25.572] [Setup] 当前选择的 Minecraft 文件夹：D:\paBEN10\
[01:34:25.572] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[01:34:25.573] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[01:34:25.577] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[01:34:25.577] [Setup] 当前选择的 Minecraft 版本：1.21.4-Fabric 0.16.14
[01:34:25.577] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\
[01:34:25.702] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[01:34:26.992] [Control] 按下图标按钮
[01:34:26.992] [Control] 切换主要页面：VersionSetup, 0
[01:34:28.102] [Control] 按下单选列表项：Mod 管理
[01:34:28.260] [Loader] 加载器 Mod List Loader 状态改变：Loading
[01:34:28.262] [System] 已刷新 Mod 列表
[01:34:28.484] [System] 欲读取的文件不存在，已返回空内容：C:\Users\<USER>\AppData\Local\Temp\PCL\Cache\LocalMod.json
[01:34:28.494] [Animation] 两个动画帧间隔 234 ms
[01:34:28.498] [Mod] 共有 4 个 Mod，其中 4 个需要联网获取信息，0 个需要更新信息
[01:34:28.507] [Loader] 加载器 Mod List Detail Loader 状态改变：Loading
[01:34:28.512] [Mod] 目标加载器：Fabric，版本：1.21.4
[01:34:28.535] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files），最大超时 5000
[01:34:28.543] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/fingerprints/432），最大超时 5000
[01:34:28.556] [Loader] 加载器 Mod List Loader 状态改变：Finished
[01:34:29.395] [Mod] 从 CurseForge 获取到 3 个本地 Mod 的对应信息
[01:34:29.399] [Mod] 需要从 CurseForge 获取 3 个本地 Mod 的工程信息
[01:34:29.400] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/mods），最大超时 5000
[01:34:29.684] [Mod] 从 Modrinth 获取到 3 个本地 Mod 的对应信息
[01:34:29.686] [Mod] 需要从 Modrinth 获取 3 个本地 Mod 的工程信息
[01:34:29.686] [Net] 发起网络请求（GET，https://api.modrinth.com/v2/projects?ids=["P7dR8mSH","eXts2L7r","mOgUt4GM"]），最大超时 5000
[01:34:30.322] [Mod] 已从 CurseForge 获取本地 Mod 信息，需要获取 3 个用于检查更新的文件信息
[01:34:30.322] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/mods/files），最大超时 5000
[01:34:30.327] [Mod] 已从 Modrinth 获取本地 Mod 信息，继续获取更新信息
[01:34:30.327] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files/update），最大超时 5000
[01:34:30.720] [Mod] 从 CurseForge 获取 Mod 更新信息结束
[01:34:30.805] [Mod] 从 Modrinth 获取本地 Mod 信息结束
[01:34:30.811] [Mod] 联网获取本地 Mod 信息完成，为 3 个 Mod 更新缓存
[01:34:30.823] [Loader] 加载器 Mod List Detail Loader 状态改变：Finished
[01:34:30.930] [Control] 按下图标按钮
[01:34:30.982] [UI] 弹出提示：已将 riftbridge-client-1.0.0.jar 删除到回收站！
[01:34:31.822] [Control] 按下按钮：打开文件夹
[01:34:31.826] [System] 正在打开资源管理器：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\mods\
[01:34:31.827] [System] 执行外部命令：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\mods\ 
[01:34:35.313] [Loader] 加载器 Mod List Loader 状态改变：Loading
[01:34:35.314] [System] 已刷新 Mod 列表
[01:34:35.332] [Mod] 共有 4 个 Mod，其中 1 个需要联网获取信息，0 个需要更新信息
[01:34:35.332] [Loader] 加载器 Mod List Detail Loader 状态改变：Loading
[01:34:35.332] [Mod] 目标加载器：Fabric，版本：1.21.4
[01:34:35.332] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files），最大超时 5000
[01:34:35.337] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/fingerprints/432），最大超时 5000
[01:34:35.468] [Loader] 加载器 Mod List Loader 状态改变：Finished
[01:34:35.751] [Mod] 从 CurseForge 获取到 0 个本地 Mod 的对应信息
[01:34:36.066] [Mod] 从 Modrinth 获取到 0 个本地 Mod 的对应信息
[01:34:36.073] [Mod] 联网获取本地 Mod 信息完成，为 0 个 Mod 更新缓存
[01:34:36.073] [Loader] 加载器 Mod List Detail Loader 状态改变：Finished
[01:34:36.274] [Control] 按下图标按钮：BtnTitleInner
[01:34:36.274] [Control] 切换主要页面：VersionSelect, -1
[01:34:37.567] [Control] 切换主要页面：Launch, -1
[01:34:37.567] [Control] 按下单击列表项：1.21.4-Fabric 0.16.14
[01:34:37.685] [Minecraft] 启动按钮：Minecraft 版本：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\
[01:34:38.947] [Control] 按下单选按钮：正版
[01:34:39.058] [Loader] 加载器 Loader Skin Ms 状态改变：Loading
[01:34:39.062] [Net] 获取网络结果：https://sessionserver.mojang.com/session/minecraft/profile/077e3afcb77643c48ae30f9dbb755bef，超时 10000ms
[01:34:39.889] [Skin] UUID 077e3afcb77643c48ae30f9dbb755bef 对应的皮肤文件为 https://textures.minecraft.net/texture/27be4c8f5515e06ac7dcc835b13bd0232994f0377a22e4a8fc4eae740807b8f3
[01:34:39.890] [Net] 直接下载文件：https://textures.minecraft.net/texture/27be4c8f5515e06ac7dcc835b13bd0232994f0377a22e4a8fc4eae740807b8f3
[01:34:40.857] [Control] 按下按钮：启动游戏
[01:34:40.857] [Loader] 加载器 Loader Launch 状态改变：Loading
[01:34:40.869] [Launch] 预检测已通过
[01:34:40.869] [Download] 开始后台检查资源文件索引
[01:34:40.869] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[01:34:40.869] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[01:34:40.869] [Loader] 加载器 登录 状态改变：Waiting
[01:34:40.869] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[01:34:40.869] [Loader] 加载器 获取 Java 状态改变：Loading
[01:34:40.869] [Download] 版本 1.21.4-Fabric 0.16.14 对应的资源文件索引为 19
[01:34:40.870] [Loader] 加载器 登录 状态改变：Loading
[01:34:40.870] [Loader] 加载器 补全文件 状态改变：Loading
[01:34:40.870] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[01:34:40.870] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[01:34:40.870] [Launch] 登录加载已开始
[01:34:40.870] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[01:34:40.870] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[01:34:40.870] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[01:34:40.870] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Finished
[01:34:40.870] [Loader] 加载器 内存优化 状态改变：Loading
[01:34:40.870] [Download] 版本 1.21.4-Fabric 0.16.14 对应的资源文件索引为 19
[01:34:40.870] [Loader] 加载器 后台下载资源文件索引 状态改变：Loading
[01:34:40.871] [Taskbar] Minecraft 启动 已加入任务列表
[01:34:40.871] [Launch] 内存优化开始
[01:34:40.871] [Test] 没有管理员权限，将以命令行方式进行内存优化
[01:34:40.872] [Launch] 登录方式：正版（Quasar2323）
[01:34:40.872] [Launch] 开始微软登录步骤 1/6（刷新登录）
[01:34:40.874] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[01:34:40.884] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[01:34:40.884] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[01:34:40.884] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[01:34:40.884] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[01:34:40.938] [Download] 19.json 2951#：开始，起始点 0，https://piston-meta.mojang.com/v1/packages/f2084084f79028ebcd1bf88006915d3274cb5beb/19.json
[01:34:40.939] [Minecraft] 皮肤下载成功：C:\Users\<USER>\AppData\Local\Temp\PCL\Cache\Skin\2905352495719792759.png
[01:34:40.939] [Loader] 加载器 Loader Skin Ms 状态改变：Finished
[01:34:40.941] [Skin] 载入头像成功：Loader Skin Ms
[01:34:40.942] [Minecraft] 获取支持库列表：1.21.4-Fabric 0.16.14
[01:34:40.943] [Minecraft] 发现重复的支持库：0 B | D:\paBEN10\libraries\org\ow2\asm\asm\9.8\asm-9.8.jar (9.8) 与 121 K | D:\paBEN10\libraries\org\ow2\asm\asm\9.6\asm-9.6.jar (9.6)，已忽略其中之一
[01:34:41.114] [Launch] Mojang 推荐使用 Java 21
[01:34:41.115] [Launch] Java 版本需求：最低 ********，最高 999.999.999.999
[01:34:41.115] [Java] 开始完全遍历查找：D:\paBEN10\
[01:34:41.134] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[01:34:41.174] [Java] 开始完全遍历查找：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\
[01:34:41.185] [Java] 排序后的 Java 优先顺序：
[01:34:41.185] [Java]  - JDK 21 (21.0.8)：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\
[01:34:41.185] [Java]  - JDK 21 (21.0.5)：C:\Program Files\Java\jdk-21\bin\
[01:34:41.186] [System] 执行外部命令并等待返回结果：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.exe -version
[01:34:41.361] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[01:34:41.361] [Loader] 加载器 下载支持库文件 状态改变：Loading
[01:34:41.362] [Loader] 加载器 下载支持库文件 状态改变：Finished
[01:34:41.362] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[01:34:41.602] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[01:34:41.603] [Loader] 加载器 下载资源文件 状态改变：Loading
[01:34:41.603] [Loader] 加载器 下载资源文件 状态改变：Finished
[01:34:41.603] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[01:34:41.603] [Loader] 加载器 补全文件 状态改变：Finished
[01:34:41.605] [Java] 最终选定的 Java：JDK 21 (21.0.8)：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\
[01:34:41.605] [Launch] 选择的 Java：JDK 21 (21.0.8)：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\
[01:34:41.605] [Loader] 加载器 获取 Java 状态改变：Finished
[01:34:41.818] [Download] 19.json 2951#：文件大小 464024（453 K）
[01:34:42.011] [Launch] 开始微软登录步骤 2/6
[01:34:42.018] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[01:34:42.288] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[01:34:42.563] [Download] 速度下限已提升到 287 K
[01:34:42.655] [Download] 速度下限已提升到 353 K
[01:34:42.748] [Download] 速度下限已提升到 411 K
[01:34:42.766] [Download] 19.json：已完成，剩余文件 0
[01:34:42.766] [Loader] 加载器 后台下载资源文件索引 状态改变：Finished
[01:34:42.767] [Loader] 加载器 后台复制资源文件索引 状态改变：Loading
[01:34:42.768] [Launch] 后台更新资源文件索引成功：C:\Users\<USER>\AppData\Local\Temp\PCL\Cache\19.json
[01:34:42.768] [Loader] 加载器 后台复制资源文件索引 状态改变：Finished
[01:34:42.768] [Loader] 加载器 后台更新资源文件索引 状态改变：Finished
[01:34:42.795] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[01:34:43.553] [Launch] 开始微软登录步骤 3/6
[01:34:43.553] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[01:34:43.812] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[01:34:44.327] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[01:34:45.114] [Launch] 开始微软登录步骤 4/6
[01:34:45.115] [Net] 发起网络请求（POST，https://api.minecraftservices.com/authentication/login_with_xbox），最大超时 15000
[01:34:46.557] [Launch] 开始微软登录步骤 5/6
[01:34:46.557] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[01:34:46.823] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[01:34:47.490] [Launch] 开始微软登录步骤 6/6
[01:34:47.490] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[01:34:47.754] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[01:34:48.276] [Launch] 微软登录完成
[01:34:48.276] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[01:34:48.284] [Launch] 登录加载已结束
[01:34:48.284] [Loader] 加载器 登录 状态改变：Finished
[01:34:48.284] [Loader] 由于输入条件变更，重启已完成的加载器 登录
[01:34:48.284] [Loader] 加载器 登录 状态改变：Loading
[01:34:48.285] [Launch] 登录加载已开始
[01:34:48.285] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[01:34:48.285] [Launch] 登录方式：正版（Quasar2323）
[01:34:48.285] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[01:34:48.300] [Launch] 登录加载已结束
[01:34:48.300] [Loader] 加载器 登录 状态改变：Finished
[01:34:53.853] [Test] 内存优化完成，可用内存改变量：4.96 G，大致剩余内存：6.72 G
[01:34:53.870] [Loader] 加载器 内存优化 状态改变：Finished
[01:34:53.870] [Loader] 加载器 获取启动参数 状态改变：Loading
[01:34:53.872] [Launch] 开始获取 Minecraft 启动参数
[01:34:53.872] [Launch] 获取新版 JVM 参数
[01:34:53.872] [Launch] 当前剩余内存：6.7G
[01:34:53.873] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[01:34:53.873] [System] 获取资源：JavaWrapper
[01:34:53.875] [Launch] 新版 JVM 参数获取成功：
[01:34:53.875] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Djava.library.path=${natives_directory} -Djna.tmpdir=${natives_directory} -Dorg.lwjgl.system.SharedLibraryExtractPath=${natives_directory} -Dio.netty.native.workdir=${natives_directory} -Dminecraft.launcher.brand=${launcher_name} -Dminecraft.launcher.version=${launcher_version} -cp ${classpath} -DFabricMcEmu=net.minecraft.client.main.Main -Xmn2304m -Xmx15360m --add-exports cpw.mods.bootstraplauncher/cpw.mods.bootstraplauncher=ALL-UNNAMED -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.fabricmc.loader.impl.launch.knot.KnotClient
[01:34:53.875] [Launch] 获取新版 Game 参数
[01:34:53.876] [Launch] 新版 Game 参数获取成功
[01:34:53.876] [Minecraft] 获取支持库列表：1.21.4-Fabric 0.16.14
[01:34:53.877] [Minecraft] 发现重复的支持库：0 B | D:\paBEN10\libraries\org\ow2\asm\asm\9.8\asm-9.8.jar (9.8) 与 121 K | D:\paBEN10\libraries\org\ow2\asm\asm\9.6\asm-9.6.jar (9.6)，已忽略其中之一
[01:34:53.880] [Launch] Minecraft 启动参数：
[01:34:53.882] [Launch] -Dfile.encoding=COMPAT -Dstderr.encoding=UTF-8 -Dstdout.encoding=UTF-8 -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Djava.library.path="D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives" -Djna.tmpdir="D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives" -Dorg.lwjgl.system.SharedLibraryExtractPath="D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives" -Dio.netty.native.workdir="D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives" -Dminecraft.launcher.brand=PCL -Dminecraft.launcher.version=361 -cp "D:\paBEN10\libraries\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;D:\paBEN10\libraries\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;D:\paBEN10\libraries\com\fasterxml\jackson\core\jackson-databind\********\jackson-databind-********.jar;D:\paBEN10\libraries\com\github\oshi\oshi-core\6.6.5\oshi-core-6.6.5.jar;D:\paBEN10\libraries\com\github\stephenc\jcip\jcip-annotations\1.0-1\jcip-annotations-1.0-1.jar;D:\paBEN10\libraries\com\google\code\gson\gson\2.11.0\gson-2.11.0.jar;D:\paBEN10\libraries\com\google\guava\failureaccess\1.0.2\failureaccess-1.0.2.jar;D:\paBEN10\libraries\com\google\guava\guava\33.3.1-jre\guava-33.3.1-jre.jar;D:\paBEN10\libraries\com\ibm\icu\icu4j\76.1\icu4j-76.1.jar;D:\paBEN10\libraries\com\microsoft\azure\msal4j\1.17.2\msal4j-1.17.2.jar;D:\paBEN10\libraries\com\mojang\authlib\6.0.57\authlib-6.0.57.jar;D:\paBEN10\libraries\com\mojang\blocklist\1.0.10\blocklist-1.0.10.jar;D:\paBEN10\libraries\com\mojang\brigadier\1.3.10\brigadier-1.3.10.jar;D:\paBEN10\libraries\com\mojang\datafixerupper\8.0.16\datafixerupper-8.0.16.jar;D:\paBEN10\libraries\com\mojang\jtracy\1.0.29\jtracy-1.0.29.jar;D:\paBEN10\libraries\com\mojang\jtracy\1.0.29\jtracy-1.0.29-natives-windows.jar;D:\paBEN10\libraries\com\mojang\logging\1.5.10\logging-1.5.10.jar;D:\paBEN10\libraries\com\mojang\patchy\2.2.10\patchy-2.2.10.jar;D:\paBEN10\libraries\com\mojang\text2speech\1.17.9\text2speech-1.17.9.jar;D:\paBEN10\libraries\com\nimbusds\content-type\2.3\content-type-2.3.jar;D:\paBEN10\libraries\com\nimbusds\lang-tag\1.7\lang-tag-1.7.jar;D:\paBEN10\libraries\com\nimbusds\nimbus-jose-jwt\9.40\nimbus-jose-jwt-9.40.jar;D:\paBEN10\libraries\com\nimbusds\oauth2-oidc-sdk\11.18\oauth2-oidc-sdk-11.18.jar;D:\paBEN10\libraries\commons-codec\commons-codec\1.17.1\commons-codec-1.17.1.jar;D:\paBEN10\libraries\commons-io\commons-io\2.17.0\commons-io-2.17.0.jar;D:\paBEN10\libraries\commons-logging\commons-logging\1.3.4\commons-logging-1.3.4.jar;D:\paBEN10\libraries\io\netty\netty-buffer\4.1.115.Final\netty-buffer-4.1.115.Final.jar;D:\paBEN10\libraries\io\netty\netty-codec\4.1.115.Final\netty-codec-4.1.115.Final.jar;D:\paBEN10\libraries\io\netty\netty-common\4.1.115.Final\netty-common-4.1.115.Final.jar;D:\paBEN10\libraries\io\netty\netty-handler\4.1.115.Final\netty-handler-4.1.115.Final.jar;D:\paBEN10\libraries\io\netty\netty-resolver\4.1.115.Final\netty-resolver-4.1.115.Final.jar;D:\paBEN10\libraries\io\netty\netty-transport-classes-epoll\4.1.115.Final\netty-transport-classes-epoll-4.1.115.Final.jar;D:\paBEN10\libraries\io\netty\netty-transport-native-unix-common\4.1.115.Final\netty-transport-native-unix-common-4.1.115.Final.jar;D:\paBEN10\libraries\io\netty\netty-transport\4.1.115.Final\netty-transport-4.1.115.Final.jar;D:\paBEN10\libraries\it\unimi\dsi\fastutil\8.5.15\fastutil-8.5.15.jar;D:\paBEN10\libraries\net\java\dev\jna\jna-platform\5.15.0\jna-platform-5.15.0.jar;D:\paBEN10\libraries\net\java\dev\jna\jna\5.15.0\jna-5.15.0.jar;D:\paBEN10\libraries\net\minidev\accessors-smart\2.5.1\accessors-smart-2.5.1.jar;D:\paBEN10\libraries\net\minidev\json-smart\2.5.1\json-smart-2.5.1.jar;D:\paBEN10\libraries\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar;D:\paBEN10\libraries\org\apache\commons\commons-compress\1.27.1\commons-compress-1.27.1.jar;D:\paBEN10\libraries\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;D:\paBEN10\libraries\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;D:\paBEN10\libraries\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\paBEN10\libraries\org\apache\logging\log4j\log4j-api\2.24.1\log4j-api-2.24.1.jar;D:\paBEN10\libraries\org\apache\logging\log4j\log4j-core\2.24.1\log4j-core-2.24.1.jar;D:\paBEN10\libraries\org\apache\logging\log4j\log4j-slf4j2-impl\2.24.1\log4j-slf4j2-impl-2.24.1.jar;D:\paBEN10\libraries\org\jcraft\jorbis\0.0.17\jorbis-0.0.17.jar;D:\paBEN10\libraries\org\joml\joml\1.10.8\joml-1.10.8.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-freetype\3.3.3\lwjgl-freetype-3.3.3.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-freetype\3.3.3\lwjgl-freetype-3.3.3-natives-windows.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-freetype\3.3.3\lwjgl-freetype-3.3.3-natives-windows-arm64.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-freetype\3.3.3\lwjgl-freetype-3.3.3-natives-windows-x86.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-glfw\3.3.3\lwjgl-glfw-3.3.3.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-glfw\3.3.3\lwjgl-glfw-3.3.3-natives-windows.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-glfw\3.3.3\lwjgl-glfw-3.3.3-natives-windows-arm64.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-glfw\3.3.3\lwjgl-glfw-3.3.3-natives-windows-x86.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-jemalloc\3.3.3\lwjgl-jemalloc-3.3.3.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-jemalloc\3.3.3\lwjgl-jemalloc-3.3.3-natives-windows.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-jemalloc\3.3.3\lwjgl-jemalloc-3.3.3-natives-windows-arm64.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-jemalloc\3.3.3\lwjgl-jemalloc-3.3.3-natives-windows-x86.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-openal\3.3.3\lwjgl-openal-3.3.3.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-openal\3.3.3\lwjgl-openal-3.3.3-natives-windows.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-openal\3.3.3\lwjgl-openal-3.3.3-natives-windows-arm64.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-openal\3.3.3\lwjgl-openal-3.3.3-natives-windows-x86.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-opengl\3.3.3\lwjgl-opengl-3.3.3.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-opengl\3.3.3\lwjgl-opengl-3.3.3-natives-windows.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-opengl\3.3.3\lwjgl-opengl-3.3.3-natives-windows-arm64.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-opengl\3.3.3\lwjgl-opengl-3.3.3-natives-windows-x86.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-stb\3.3.3\lwjgl-stb-3.3.3.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-stb\3.3.3\lwjgl-stb-3.3.3-natives-windows.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-stb\3.3.3\lwjgl-stb-3.3.3-natives-windows-arm64.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-stb\3.3.3\lwjgl-stb-3.3.3-natives-windows-x86.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-tinyfd\3.3.3\lwjgl-tinyfd-3.3.3.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-tinyfd\3.3.3\lwjgl-tinyfd-3.3.3-natives-windows.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-tinyfd\3.3.3\lwjgl-tinyfd-3.3.3-natives-windows-arm64.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl-tinyfd\3.3.3\lwjgl-tinyfd-3.3.3-natives-windows-x86.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl\3.3.3\lwjgl-3.3.3.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl\3.3.3\lwjgl-3.3.3-natives-windows.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl\3.3.3\lwjgl-3.3.3-natives-windows-arm64.jar;D:\paBEN10\libraries\org\lwjgl\lwjgl\3.3.3\lwjgl-3.3.3-natives-windows-x86.jar;D:\paBEN10\libraries\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;D:\paBEN10\libraries\org\ow2\asm\asm\9.8\asm-9.8.jar;D:\paBEN10\libraries\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;D:\paBEN10\libraries\org\ow2\asm\asm-analysis\9.8\asm-analysis-9.8.jar;D:\paBEN10\libraries\org\ow2\asm\asm-commons\9.8\asm-commons-9.8.jar;D:\paBEN10\libraries\org\ow2\asm\asm-tree\9.8\asm-tree-9.8.jar;D:\paBEN10\libraries\org\ow2\asm\asm-util\9.8\asm-util-9.8.jar;D:\paBEN10\libraries\net\fabricmc\sponge-mixin\0.15.5+mixin.0.8.7\sponge-mixin-0.15.5+mixin.0.8.7.jar;D:\paBEN10\libraries\net\fabricmc\intermediary\1.21.4\intermediary-1.21.4.jar;D:\paBEN10\libraries\net\fabricmc\fabric-loader\0.16.14\fabric-loader-0.16.14.jar;D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14.jar" -DFabricMcEmu=net.minecraft.client.main.Main -Xmn2304m -Xmx15360m --add-exports cpw.mods.bootstraplauncher/cpw.mods.bootstraplauncher=ALL-UNNAMED -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.fabricmc.loader.impl.launch.knot.KnotClient --username Quasar2323 --version "1.21.4-Fabric 0.16.14" --gameDir "D:\paBEN10\versions\1.21.4-Fabric 0.16.14" --assetsDir "D:\paBEN10\assets" --assetIndex 19 --uuid 077e3afcb77643c48ae30f9dbb755bef --accessToken eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************DXlHg --clientId ${clientid} --xuid ${auth_xuid} --userType msa --versionType PCL --width 854 --height 480 
[01:34:53.882] [Loader] 加载器 获取启动参数 状态改变：Finished
[01:34:53.887] [Loader] 加载器 解压文件 状态改变：Loading
[01:34:53.888] [Launch] 正在解压 Natives 文件
[01:34:53.893] [Launch] 删除：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives\glfw.dll
[01:34:53.893] [Launch] 删除：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives\jemalloc.dll
[01:34:53.894] [Launch] 删除：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives\jna3143172915856499456.dll
[01:34:53.894] [Launch] 删除：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives\jna3143172915856499456.dll.x
[01:34:53.895] [Launch] 删除：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives\lwjgl.dll
[01:34:53.896] [Launch] 删除：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives\lwjgl_opengl.dll
[01:34:53.896] [Launch] 删除：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives\lwjgl_stb.dll
[01:34:53.897] [Launch] 删除：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives\OpenAL.dll
[01:34:53.897] [Loader] 加载器 解压文件 状态改变：Finished
[01:34:53.912] [Loader] 加载器 预启动处理 状态改变：Loading
[01:34:53.912] [System] 无需调整显卡设置：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\javaw.exe
[01:34:53.912] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[01:34:53.922] [Launch] 已更新 launcher_profiles.json
[01:34:53.923] [Launch] 需要的语言为 zh_cn，当前语言为 zh_cn，无需修改
[01:34:53.923] [Loader] 加载器 预启动处理 状态改变：Finished
[01:34:53.957] [Loader] 加载器 执行自定义命令 状态改变：Loading
[01:34:53.961] [Loader] 加载器 执行自定义命令 状态改变：Finished
[01:34:53.981] [Loader] 加载器 启动进程 状态改变：Loading
[01:34:54.240] [Launch] 已启动游戏进程：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\javaw.exe
[01:34:54.240] [Loader] 加载器 启动进程 状态改变：Finished
[01:34:54.263] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[01:34:54.263] [Launch] 
[01:34:54.263] [Launch] ~ 基础参数 ~
[01:34:54.263] [Launch] PCL 版本：Release 2.10.3 (361)
[01:34:54.263] [Launch] 游戏版本：1.21.4, Fabric 0.16.14（识别为 1.21.4）
[01:34:54.263] [Launch] 资源版本：19
[01:34:54.263] [Launch] 版本继承：无
[01:34:54.263] [Launch] 分配的内存：15 GB（15360 MB）
[01:34:54.263] [Launch] MC 文件夹：D:\paBEN10\
[01:34:54.263] [Launch] 版本文件夹：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\
[01:34:54.263] [Launch] 版本隔离：True
[01:34:54.263] [Launch] HMCL 格式：False
[01:34:54.263] [Launch] Java 信息：JDK 21 (21.0.8)：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\
[01:34:54.264] [Launch] 环境变量：已设置
[01:34:54.264] [Launch] Natives 文件夹：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\1.21.4-Fabric 0.16.14-natives
[01:34:54.264] [Launch] 
[01:34:54.264] [Launch] ~ 登录参数 ~
[01:34:54.264] [Launch] 玩家用户名：Quasar2323
[01:34:54.264] [Launch] AccessToken：eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************DXlHg
[01:34:54.264] [Launch] ClientToken：077e3afcb77643c48ae30f9dbb755bef
[01:34:54.264] [Launch] UUID：077e3afcb77643c48ae30f9dbb755bef
[01:34:54.264] [Launch] 登录方式：Microsoft
[01:34:54.264] [Launch] 
[01:34:54.264] [Launch] [27660] 开始 Minecraft 日志监控
[01:34:54.264] [Launch] [全局] 出现运行中的 Minecraft
[01:34:54.520] [Launch] [27660] 日志 1/5：已出现日志输出
[01:35:03.079] [Launch] [27660] Minecraft 已退出，返回值：1
[01:35:03.079] [Launch] [27660] Minecraft 尚未加载完成，可能已崩溃
[01:35:03.080] [Launch] [全局] 已无运行中的 Minecraft
[01:35:03.080] [Launch] [27660] Minecraft 已崩溃，将在 2 秒后开始崩溃分析
[01:35:03.146] [UI] 弹出提示：检测到 Minecraft 出现错误，错误分析已开始……
[01:35:03.174] 加载线程 等待游戏窗口出现 (70) 出错，已完成 30%：$$
   在 PCL.ModLaunch.McLaunchWait(LoaderTask`2 Loader)
   在 PCL.ModLoader.LoaderTask`2._Closure$__13-0._Lambda$__0()
[01:35:03.174] [Loader] 加载器 等待游戏窗口出现 状态改变：Failed
[01:35:03.174] [Loader] 加载器 Minecraft 启动 状态改变：Failed
[01:35:03.205] [Taskbar] Minecraft 启动 已移出任务列表
[01:35:03.383] [System] 诊断信息：
操作系统：Microsoft Windows 11 家庭中文版（32 位：False）
剩余内存：7189 M / 16108 M
DPI：144（150%）
MC 文件夹：D:\paBEN10\
文件位置：C:\Users\<USER>\Desktop\
[01:35:03.409] 加载线程 Loader Launch (53) 出错，已完成 2%：等待游戏窗口出现失败
   在 PCL.ModLaunch.McLaunchStart(LoaderTask`2 Loader)
   在 PCL.ModLoader.LoaderTask`2._Closure$__13-0._Lambda$__0()
→ $$
   在 PCL.ModLaunch.McLaunchWait(LoaderTask`2 Loader)
   在 PCL.ModLoader.LoaderTask`2._Closure$__13-0._Lambda$__0()
[01:35:03.409] [Loader] 加载器 Loader Launch 状态改变：Failed
[01:35:03.606] [Launch] [27660] Minecraft 日志监控已退出
[01:35:05.392] [Launch] [27660] 崩溃分析开始
[01:35:05.405] [Crash] 崩溃分析暂存文件夹：C:\Users\<USER>\AppData\Local\Temp\PCL\TaskTemp\3100-790927\
[01:35:05.411] [Crash] 步骤 1：收集日志文件
[01:35:05.413] [Crash] 可能可用的日志文件：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\logs\latest.log（0 分钟）
[01:35:05.413] [Crash] 以下为游戏输出的最后一段内容：
Picked up JAVA_TOOL_OPTIONS: -Dfile.encoding=UTF-8
WARNING: Unknown module: cpw.mods.bootstraplauncher specified to --add-exports
[01:34:55] [main/INFO]: Loading Minecraft 1.21.4 with Fabric Loader 0.16.14
[01:34:55] [main/INFO]: Loading 57 mods:
	- fabric-api 0.119.3+1.21.4
	   |-- fabric-api-base 0.4.54+b47eab6b04
	   |-- fabric-api-lookup-api-v1 1.6.86+b1caf1e904
	   |-- fabric-biome-api-v1 15.0.6+b1c29d8e04
	   |-- fabric-block-api-v1 1.0.31+7feeb73304
	   |-- fabric-block-view-api-v2 1.0.20+9c49cc8c04
	   |-- fabric-blockrenderlayer-v1 2.0.8+7feeb73304
	   |-- fabric-client-tags-api-v1 1.1.29+20ea1e2304
	   |-- fabric-command-api-v1 1.2.62+f71b366f04
	   |-- fabric-command-api-v2 2.2.41+e496eb1504
	   |-- fabric-commands-v0 0.2.79+df3654b304
	   |-- fabric-content-registries-v0 9.1.19+25d1a67604
	   |-- fabric-convention-tags-v1 2.1.20+7f945d5b04
	   |-- fabric-convention-tags-v2 2.14.1+aebda09404
	   |-- fabric-crash-report-info-v1 0.3.6+7feeb73304
	   |-- fabric-data-attachment-api-v1 1.6.2+e99da0f704
	   |-- fabric-data-generation-api-v1 22.3.1+0f4e5f5504
	   |-- fabric-dimensions-v1 4.0.10+7feeb73304
	   |-- fabric-entity-events-v1 2.0.15+62245bef04
	   |-- fabric-events-interaction-v0 4.0.4+a4eebcf004
	   |-- fabric-game-rule-api-v1 1.0.63+7d48d43904
	   |-- fabric-item-api-v1 11.3.0+ee91fa1f04
	   |-- fabric-item-group-api-v1 4.2.2+fcb9601404
	   |-- fabric-key-binding-api-v1 1.0.57+7d48d43904
	   |-- fabric-keybindings-v0 0.2.55+df3654b304
	   |-- fabric-lifecycle-events-v1 2.5.4+bf2a60eb04
	   |-- fabric-loot-api-v2 3.0.38+3f89f5a504
	   |-- fabric-loot-api-v3 1.0.26+203e6b2304
	   |-- fabric-message-api-v1 6.0.26+238a33c004
	   |-- fabric-model-loading-api-v1 4.3.0+ae23723504
	   |-- fabric-networking-api-v1 4.4.0+db5e668204
	   |-- fabric-object-builder-api-v1 18.0.14+38b0d59804
	   |-- fabric-particles-v1 4.0.14+7feeb73304
	   |-- fabric-recipe-api-v1 8.1.1+640e77ae04
	   |-- fabric-registry-sync-v0 6.1.11+4a9c1ece04
	   |-- fabric-renderer-api-v1 5.0.3+50f0feb204
	   |-- fabric-renderer-indigo 2.0.3+50f0feb204
	   |-- fabric-rendering-data-attachment-v1 0.3.58+73761d2e04
	   |-- fabric-rendering-fluids-v1 3.1.19+7feeb73304
	   |-- fabric-rendering-v1 10.2.1+0d31b09f04
	   |-- fabric-resource-conditions-api-v1 5.0.13+203e6b2304
	   |-- fabric-resource-loader-v0 3.1.1+360374ac04
	   |-- fabric-screen-api-v1 2.0.38+7feeb73304
	   |-- fabric-screen-handler-api-v1 1.3.118+7feeb73304
	   |-- fabric-sound-api-v1 1.0.32+7feeb73304
	   |-- fabric-tag-api-v1 1.0.7+7d48d43904
	   |-- fabric-transfer-api-v1 5.4.9+efa825c904
	   \-- fabric-transitive-access-wideners-v1 6.3.2+56e78b9b04
	- fabricloader 0.16.14
	   \-- mixinextras 0.4.1
	- java 21
	- minecraft 1.21.4
	- modmenu 13.0.3
	- placeholder-api 2.5.2+1.21.3
	- riftbridge 1.0.0
	   |-- com_jcraft_jsch 0.1.55
	   \-- org_apache_commons_commons-lang3 3.12.0
[01:34:55] [main/INFO]: SpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/D:/paBEN10/libraries/net/fabricmc/sponge-mixin/0.15.5+mixin.0.8.7/sponge-mixin-0.15.5+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[01:34:55] [main/INFO]: Compatibility level set to JAVA_21
[01:34:55] [main/WARN]: Reference map 'riftbridge.refmap.json' for riftbridge.mixins.json could not be read. If this is a development environment you can ignore this message
[01:34:56] [main/INFO]: Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[01:34:57] [Datafixer Bootstrap/INFO]: 243 Datafixer optimizations took 378 milliseconds
[01:35:02] [Render thread/ERROR]: Mixin apply for mod riftbridge failed riftbridge.mixins.json:MixinMinecraftClient from mod riftbridge -> net.minecraft.class_310: org.spongepowered.asm.mixin.injection.throwables.InvalidInjectionException Critical injection failure: @Inject annotation on onRun could not find any targets matching 'run' in net/minecraft/class_310. No refMap loaded. [INJECT_PREPARE Applicator Phase -> riftbridge.mixins.json:MixinMinecraftClient from mod riftbridge -> Prepare Injections -> handler$znm000$riftbridge$onRun(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V -> Parse ->  -> Validate Targets]
org.spongepowered.asm.mixin.injection.throwables.InvalidInjectionException: Critical injection failure: @Inject annotation on onRun could not find any targets matching 'run' in net/minecraft/class_310. No refMap loaded. [INJECT_PREPARE Applicator Phase -> riftbridge.mixins.json:MixinMinecraftClient from mod riftbridge -> Prepare Injections -> handler$znm000$riftbridge$onRun(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V -> Parse ->  -> Validate Targets]
	at org.spongepowered.asm.mixin.injection.selectors.TargetSelectors.validate(TargetSelectors.java:346) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.readAnnotation(InjectionInfo.java:369) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.<init>(InjectionInfo.java:340) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.<init>(InjectionInfo.java:331) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.CallbackInjectionInfo.<init>(CallbackInjectionInfo.java:48) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62) ~[?:?]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502) ~[?:?]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486) ~[?:?]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo$InjectorEntry.create(InjectionInfo.java:196) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.parse(InjectionInfo.java:664) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTargetContext.prepareInjections(MixinTargetContext.java:1399) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.prepareInjections(MixinApplicatorStandard.java:731) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.applyMixin(MixinApplicatorStandard.java:315) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.apply(MixinApplicatorStandard.java:246) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.TargetClassContext.apply(TargetClassContext.java:437) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.TargetClassContext.applyMixins(TargetClassContext.java:418) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:363) [sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClass(MixinTransformer.java:234) [sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClassBytes(MixinTransformer.java:202) [sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.getPostMixinClassByteArray(KnotClassDelegate.java:422) [fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.tryLoadClass(KnotClassDelegate.java:323) [fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.loadClass(KnotClassDelegate.java:218) [fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassLoader.loadClass(KnotClassLoader.java:119) [fabric-loader-0.16.14.jar:?]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) [?:?]
	at knot/net.minecraft.client.main.Main.main(Main.java:250) [client-intermediary.jar:?]
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480) [fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74) [fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23) [fabric-loader-0.16.14.jar:?]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
[01:35:02] [Render thread/ERROR]: Mixin apply for mod riftbridge failed riftbridge.mixins.json:MixinMinecraftClient from mod riftbridge -> net.minecraft.class_310: org.spongepowered.asm.mixin.injection.throwables.InvalidInjectionException Critical injection failure: @Inject annotation on onRun could not find any targets matching 'run' in net/minecraft/class_310. No refMap loaded. [INJECT_PREPARE Applicator Phase -> riftbridge.mixins.json:MixinMinecraftClient from mod riftbridge -> Prepare Injections -> handler$znm000$riftbridge$onRun(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V -> Parse ->  -> Validate Targets]
org.spongepowered.asm.mixin.injection.throwables.InvalidInjectionException: Critical injection failure: @Inject annotation on onRun could not find any targets matching 'run' in net/minecraft/class_310. No refMap loaded. [INJECT_PREPARE Applicator Phase -> riftbridge.mixins.json:MixinMinecraftClient from mod riftbridge -> Prepare Injections -> handler$znm000$riftbridge$onRun(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V -> Parse ->  -> Validate Targets]
	at org.spongepowered.asm.mixin.injection.selectors.TargetSelectors.validate(TargetSelectors.java:346) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.readAnnotation(InjectionInfo.java:369) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.<init>(InjectionInfo.java:340) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.<init>(InjectionInfo.java:331) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.CallbackInjectionInfo.<init>(CallbackInjectionInfo.java:48) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62) ~[?:?]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502) ~[?:?]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486) ~[?:?]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo$InjectorEntry.create(InjectionInfo.java:196) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.parse(InjectionInfo.java:664) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTargetContext.prepareInjections(MixinTargetContext.java:1399) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.prepareInjections(MixinApplicatorStandard.java:731) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.applyMixin(MixinApplicatorStandard.java:315) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.apply(MixinApplicatorStandard.java:246) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.TargetClassContext.apply(TargetClassContext.java:437) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.TargetClassContext.applyMixins(TargetClassContext.java:418) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:363) [sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClass(MixinTransformer.java:234) [sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClassBytes(MixinTransformer.java:202) [sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.getPostMixinClassByteArray(KnotClassDelegate.java:422) [fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.tryLoadClass(KnotClassDelegate.java:323) [fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.loadClass(KnotClassDelegate.java:218) [fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassLoader.loadClass(KnotClassLoader.java:119) [fabric-loader-0.16.14.jar:?]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) [?:?]
	at knot/net.minecraft.client.main.Main.main(Main.java:260) [client-intermediary.jar:?]
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480) [fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74) [fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23) [fabric-loader-0.16.14.jar:?]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
[01:35:02] [Render thread/ERROR]: Minecraft has crashed!
net.fabricmc.loader.impl.FormattedException: java.lang.RuntimeException: Mixin transformation of net.minecraft.class_310 failed
	at net.fabricmc.loader.impl.FormattedException.ofLocalized(FormattedException.java:63) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:482) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74) [fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23) [fabric-loader-0.16.14.jar:?]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.lang.RuntimeException: Mixin transformation of net.minecraft.class_310 failed
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.getPostMixinClassByteArray(KnotClassDelegate.java:427) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.tryLoadClass(KnotClassDelegate.java:323) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.loadClass(KnotClassDelegate.java:218) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassLoader.loadClass(KnotClassLoader.java:119) ~[fabric-loader-0.16.14.jar:?]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[?:?]
	at knot/net.minecraft.client.main.Main.main(Main.java:260) ~[1.21.4-Fabric%200.16.14.jar:?]
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480) ~[fabric-loader-0.16.14.jar:?]
	... 6 more
Caused by: org.spongepowered.asm.mixin.transformer.throwables.MixinTransformerError: An unexpected critical error was encountered
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:392) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClass(MixinTransformer.java:234) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClassBytes(MixinTransformer.java:202) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.getPostMixinClassByteArray(KnotClassDelegate.java:422) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.tryLoadClass(KnotClassDelegate.java:323) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.loadClass(KnotClassDelegate.java:218) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassLoader.loadClass(KnotClassLoader.java:119) ~[fabric-loader-0.16.14.jar:?]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[?:?]
	at knot/net.minecraft.client.main.Main.main(Main.java:260) ~[1.21.4-Fabric%200.16.14.jar:?]
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480) ~[fabric-loader-0.16.14.jar:?]
	... 6 more
Caused by: org.spongepowered.asm.mixin.throwables.MixinApplyError: Mixin [riftbridge.mixins.json:MixinMinecraftClient from mod riftbridge] from phase [DEFAULT] in config [riftbridge.mixins.json] FAILED during APPLY
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.handleMixinError(MixinProcessor.java:638) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.handleMixinApplyError(MixinProcessor.java:589) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:379) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClass(MixinTransformer.java:234) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClassBytes(MixinTransformer.java:202) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.getPostMixinClassByteArray(KnotClassDelegate.java:422) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.tryLoadClass(KnotClassDelegate.java:323) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.loadClass(KnotClassDelegate.java:218) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassLoader.loadClass(KnotClassLoader.java:119) ~[fabric-loader-0.16.14.jar:?]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[?:?]
	at knot/net.minecraft.client.main.Main.main(Main.java:260) ~[1.21.4-Fabric%200.16.14.jar:?]
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480) ~[fabric-loader-0.16.14.jar:?]
	... 6 more
Caused by: org.spongepowered.asm.mixin.injection.throwables.InvalidInjectionException: Critical injection failure: @Inject annotation on onRun could not find any targets matching 'run' in net/minecraft/class_310. No refMap loaded. [INJECT_PREPARE Applicator Phase -> riftbridge.mixins.json:MixinMinecraftClient from mod riftbridge -> Prepare Injections -> handler$znm000$riftbridge$onRun(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V -> Parse ->  -> Validate Targets]
	at org.spongepowered.asm.mixin.injection.selectors.TargetSelectors.validate(TargetSelectors.java:346) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.readAnnotation(InjectionInfo.java:369) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.<init>(InjectionInfo.java:340) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.<init>(InjectionInfo.java:331) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.CallbackInjectionInfo.<init>(CallbackInjectionInfo.java:48) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62) ~[?:?]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502) ~[?:?]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486) ~[?:?]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo$InjectorEntry.create(InjectionInfo.java:196) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.parse(InjectionInfo.java:664) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTargetContext.prepareInjections(MixinTargetContext.java:1399) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.prepareInjections(MixinApplicatorStandard.java:731) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.applyMixin(MixinApplicatorStandard.java:315) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.apply(MixinApplicatorStandard.java:246) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.TargetClassContext.apply(TargetClassContext.java:437) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.TargetClassContext.applyMixins(TargetClassContext.java:418) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:363) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClass(MixinTransformer.java:234) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClassBytes(MixinTransformer.java:202) ~[sponge-mixin-0.15.5+mixin.0.8.7.jar:0.15.5+mixin.0.8.7]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.getPostMixinClassByteArray(KnotClassDelegate.java:422) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.tryLoadClass(KnotClassDelegate.java:323) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.loadClass(KnotClassDelegate.java:218) ~[fabric-loader-0.16.14.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClassLoader.loadClass(KnotClassLoader.java:119) ~[fabric-loader-0.16.14.jar:?]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[?:?]
	at knot/net.minecraft.client.main.Main.main(Main.java:260) ~[1.21.4-Fabric%200.16.14.jar:?]
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480) ~[fabric-loader-0.16.14.jar:?]
	... 6 more
[01:35:05.414] [Crash] 步骤 1：收集日志文件完成，收集到 2 个文件
[01:35:05.434] [Crash] 步骤 2：准备日志文本
[01:35:05.435] [Crash] latest.log 分类为 MinecraftLog
[01:35:05.435] [Crash] rawoutput.log 分类为 MinecraftLog
[01:35:05.438] [Crash] 输出报告：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\logs\latest.log，作为 Minecraft 或启动器日志
[01:35:05.438] [Crash] 输出报告：C:\Users\<USER>\AppData\Local\Temp\PCL\TaskTemp\3100-790927\RawOutput.log，作为 Minecraft 或启动器日志
[01:35:05.439] [Crash] 导入分析：C:\Users\<USER>\AppData\Local\Temp\PCL\TaskTemp\3100-790927\RawOutput.log，作为启动器日志
[01:35:05.439] [Crash] 导入分析：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\logs\latest.log，作为 Minecraft 日志
[01:35:05.439] [Crash] 步骤 2：准备日志文本完成，找到游戏日志用作分析
[01:35:05.444] [Crash] 步骤 3：分析崩溃原因
[01:35:05.457] [Crash] 可能的崩溃原因：ModMixin失败（riftbridge）
[01:35:05.457] [Crash] 步骤 3：分析崩溃原因完成，找到 1 条可能的原因
[01:35:05.457] [Crash]  - ModMixin失败（riftbridge）
[01:35:05.468] [System] 欲读取的文件不存在，已返回空内容：C:\Users\<USER>\AppData\Local\Temp\PCL\Cache\Notice.cfg
[01:35:05.472] [System] 窗口已置顶，位置：(652.666666666667, 286), 900 x 556
[01:35:05.540] [Control] 普通弹窗：Minecraft 出现错误
名为 riftbridge 的 Mod 注入失败，导致游戏出错。
这一般代表着它与其他 Mod 或当前环境不兼容，或是它存在 Bug。
你可以尝试禁用此 Mod，然后观察游戏是否还会崩溃。

你可以查看错误报告了解错误具体是如何发生的。
如果要寻求帮助，请把错误报告文件发给对方，而不是发送这个窗口的照片或者截图。
[01:35:31.891] [Control] 按下按钮：确定
[01:35:31.892] [Control] 普通弹框返回：1
[01:35:33.080] [Control] 按下按钮：版本设置
[01:35:33.089] [Control] 切换主要页面：VersionSetup, 0
[01:35:33.208] [Report] FPS 0, 动画 7, 下载中 0（0 B/s）
[01:35:35.765] [Control] 按下按钮：版本文件夹
[01:35:35.766] [System] 正在打开资源管理器：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\
[01:35:35.766] [System] 执行外部命令：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\ 
[02:15:30.247] [Animation] 两个动画帧间隔 735 ms
[03:25:25.136] [Animation] 两个动画帧间隔 1609 ms
[04:35:26.263] [Animation] 两个动画帧间隔 218 ms
[06:35:36.274] [Animation] 两个动画帧间隔 734 ms
[12:05:35.641] [Animation] 两个动画帧间隔 328 ms
[15:05:38.708] [Animation] 两个动画帧间隔 235 ms
[17:55:55.504] [Animation] 两个动画帧间隔 1593 ms
[19:52:30.739] [Animation] 两个动画帧间隔 750 ms
[20:58:15.669] [Animation] 两个动画帧间隔 1594 ms
[21:16:53.250] [Animation] 两个动画帧间隔 735 ms
[21:20:49.410] [Animation] 两个动画帧间隔 1593 ms
[21:31:49.088] [Animation] 两个动画帧间隔 328 ms
[22:53:39.106] [Animation] 两个动画帧间隔 750 ms
[23:13:19.086] [Animation] 两个动画帧间隔 1625 ms
[00:50:20.923] [Animation] 两个动画帧间隔 657 ms
[02:08:39.069] [Animation] 两个动画帧间隔 1594 ms
[02:08:49.446] [Control] 按下图标按钮：BtnTitleInner
[02:08:49.450] [Control] 切换主要页面：Launch, -1
[02:08:50.516] [Control] 按下按钮：版本选择
[02:08:50.516] [Control] 切换主要页面：VersionSelect, 0
[02:08:54.053] [Control] 按下单选列表项：寰宇特摄
[02:08:54.055] [Setup] 当前选择的 Minecraft 文件夹：D:\寰宇特摄\
[02:08:54.056] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:08:54.058] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:08:54.062] [Setup] 当前选择的 Minecraft 版本：Immersive Fight 3.2.3
[02:08:54.062] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\寰宇特摄\versions\Immersive Fight 3.2.3\
[02:08:54.065] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:08:54.200] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:08:56.064] [Control] 按下图标按钮
[02:08:56.064] [Control] 切换主要页面：VersionSetup, 0
[02:08:57.027] [Control] 按下单选列表项：Mod 管理
[02:08:57.167] [Minecraft] 版本隔离初始化（Immersive Fight 3.2.3）：从老的版本独立设置中迁移
[02:08:57.167] [Loader] 加载器 Mod List Loader 状态改变：Loading
[02:08:57.169] [System] 已刷新 Mod 列表
[02:08:59.540] [Mod] 共有 282 个 Mod，其中 282 个需要联网获取信息，0 个需要更新信息
[02:08:59.565] [Loader] 加载器 Mod List Detail Loader 状态改变：Loading
[02:08:59.565] [Mod] 目标加载器：Forge，版本：1.20.1
[02:08:59.566] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files），最大超时 5000
[02:09:00.088] [Loader] 加载器 Mod List Loader 状态改变：Finished
[02:09:00.516] [Animation] 两个动画帧间隔 485 ms
[02:09:00.521] [Animation] 两个动画帧间隔 468 ms
[02:09:01.609] [Mod] 从 Modrinth 获取到 162 个本地 Mod 的对应信息
[02:09:01.618] [Mod] 需要从 Modrinth 获取 161 个本地 Mod 的工程信息
[02:09:01.618] [Net] 发起网络请求（GET，https://api.modrinth.com/v2/projects?ids=["9NM0dXub","ynxotL0Y","U6GY0xp0","2cMuAZAp","fM515JnW","k23mNPhZ","lhGA9TYQ","USLVyT7V","b1ZV3DIJ","lOOpEntO","tbDE3NLR","temczoTQ","ANu8FMzT","BgNRHReB","5srFLIaK","gc8OEnCC","QmTVMKNG","wHboX6Zr","KUZAAwdD","ZvaHbwoZ","Vt8TI045","ZucWZEBV","VsM5EDoI","uy4Cnpcm","GkIc6rRo","b5GyyYkp","DnNYdJsx","MOqt4Z5n","2JkvF271","jJfV67b1","gACd0cTc","485Cv9lY","9s6osm5g","2gq0ALnz","FYpiwiBR","rLLJ1OZM","OsZiaDHq","MI1LWe93","GNxdLCoP","vvuO3ImH","idMHQ4n2","WEg59z5b","4XJZeZbM","s8dABaWE","v3CYg2V9","Adega8YN","tpehi7ww","rYocd2LE","OZBR5JT5","N8MNCv66","6YTvv3aI","mSQF1NpT","sk9rgfiA","UVtY3ZAC","P7SsQE5n","Kfr0BQO9","Lq6ojcWv","NNAgCjsB","4I1XuqiY","BVzZfTc1","vu3NZ5Ma","BKC84WgU","OSQ8mw2r","jv7tzVE4","Aqlf1Shp","Wq5SjeWM","9mtu0sUO","uXXizFIs","4Rh1Mobu","FAUSd09K","5WeWGLoJ","BOCJKD49","hYykXjDp","iAlZk5aS","H1sntfo8","PJbBnV4i","kOxHV5Um","5faXoLqX","kLoMCc4b","5ZwdcRci","MBKJWOuQ","O7RBXm3n","n2de3t2z","nvQzSEkH","v3UlehfS","u6dRKJwZ","ufdDoWPd","J81TRJWm","ordsPcFz","S3D3QF0M","GyKzAh3l","DOB2l4oJ","TqCKvqjC","46KJle7n","iwkcspV8","sc9lpPiU","NRjRiSSD","r4PuRGfV","nGUXvjTa","nmDcB62a","twkfQtEc","423SG4Jc","aC3cM3Vq","BFbX9xcm","fPetb5Kh","P1Kv5EAO","bQh7xzFq","8LbrWrCF","qQyHxfxd","KuNKN7d2","MPCX6s5C","E0L8mfJZ","RH2KUdKJ","ndHYMY2K","GchcoXML","psKAO98W","c7m1mi73","uaLW6JVR","KdJhOYVV","ZX66K16c","FCFcFw09","hqQqvaa4","QAGBst4M","ZAfRfkTo","AtT9wm5O","tu54PMAb","qw2Ls89j","Tkikq67H","OCJRPujW","cC1LNyTQ","M1953qlQ","G1hIVOrD","oY2B1pjg","TwL8m46w","cVIr8Vz1","bK3Ubu9p","ydZic5r4","oXzIQwRj","s5d4P01r","T8TGycIQ","LN9BxssP","rOUBggPv","fFEIiSDQ","pe7FN3d6","t2pgJYye","84MLqQxZ","JtifUr64","HsdNFinx","T21szC0a","klXONLDA","z13R7Et1","oWx9p4ug","QjzFB2iK","AVo2esap","NcUtCpym","1bokaNcj","1eAoo2KR","Ua7DFN59","2BwBOmBQ","HjmxVlSr","Z2mXHnxP"]），最大超时 5000
[02:09:02.395] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/fingerprints/432），最大超时 5000
[02:09:02.683] [Mod] 已从 Modrinth 获取本地 Mod 信息，继续获取更新信息
[02:09:02.683] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files/update），最大超时 5000
[02:09:03.332] [Mod] 从 Modrinth 获取本地 Mod 信息结束
[02:09:11.211] NetRequestOnce 失败：你的网络环境不佳，导致难以连接到服务器。请稍后重试，或使用 VPN 以改善网络环境。

————————————
详细错误信息：
连接服务器超时，请检查你的网络环境是否良好（操作已超时。，https://api.curseforge.com/v1/fingerprints/432）
   错误类型：System.Net.WebException
→ 操作已超时。
   在 System.Net.ConnectStream.Read(Byte[] buffer, Int32 offset, Int32 size)
   在 System.IO.StreamReader.ReadBuffer()
   在 System.IO.StreamReader.ReadToEnd()
   在 PCL.ModNet.NetRequestOnce(String Url, String Method, Object Data, String ContentType, Int32 Timeout, Dictionary`2 Headers, Boolean MakeLog, Boolean UseBrowserUserAgent)
   错误类型：System.Net.WebException
[02:09:11.211] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/fingerprints/432），最大超时 20000
[02:09:13.351] [Control] 按下图标按钮：BtnTitleInner
[02:09:13.352] [Control] 切换主要页面：VersionSelect, -1
[02:09:17.534] [Control] 按下单选列表项：BEN 10
[02:09:17.534] [Setup] 当前选择的 Minecraft 文件夹：D:\BEN 10\
[02:09:17.534] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:09:17.535] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:09:17.539] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:09:17.540] 版本 JSON 可用性检查失败（D:\BEN 10\versions\Kevin_11-1.12.2-3.0\）：未找到版本 JSON 文件：D:\BEN 10\versions\Kevin_11-1.12.2-3.0\Kevin_11-1.12.2-3.0.json
   在 PCL.ModMinecraft.McVersion.DisableException()
   在 PCL.ModMinecraft.McVersion.AssetException()
   在 PCL.ModMinecraft.McVersion.Check()
[02:09:17.543] 版本 JSON 可用性检查失败（D:\BEN 10\versions\〖矛盾附属〗Random Ben 10 Stuff 1.0\）：未找到版本 JSON 文件：D:\BEN 10\versions\〖矛盾附属〗Random Ben 10 Stuff 1.0\〖矛盾附属〗Random Ben 10 Stuff 1.0.json
   在 PCL.ModMinecraft.McVersion.DisableException()
   在 PCL.ModMinecraft.McVersion.AssetException()
   在 PCL.ModMinecraft.McVersion.Check()
[02:09:17.544] [Setup] 当前选择的 Minecraft 版本：Ben10Craft
[02:09:17.544] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\BEN 10\versions\Ben10Craft\
[02:09:17.686] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:09:18.865] [Control] 按下单选列表项：ben10
[02:09:18.866] [Setup] 当前选择的 Minecraft 文件夹：D:\ben10\
[02:09:18.866] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:09:18.871] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:09:18.873] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:09:18.874] [Setup] 当前选择的 Minecraft 版本：MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK
[02:09:18.874] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\ben10\versions\MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK\
[02:09:19.018] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:09:19.977] [Control] 按下单选列表项：(导入包)奥特英雄激战重奏
[02:09:19.978] [Setup] 当前选择的 Minecraft 文件夹：D:\(导入包)奥特英雄激战重奏\.minecraft\
[02:09:19.978] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:09:19.979] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:09:19.985] [Setup] 当前选择的 Minecraft 版本：奥特英雄激战重奏
[02:09:19.985] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\(导入包)奥特英雄激战重奏\.minecraft\versions\奥特英雄激战重奏\
[02:09:19.986] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:09:20.134] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:09:20.782] [Control] 按下单选列表项：官方启动器文件夹
[02:09:20.783] [Setup] 当前选择的 Minecraft 文件夹：C:\Users\<USER>\AppData\Roaming\.minecraft\
[02:09:20.783] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:09:20.784] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:09:20.786] [Setup] 当前选择的 Minecraft 版本：[幽]ANZNB火影忍者懒人包
[02:09:20.786] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\AppData\Roaming\.minecraft\versions\[幽]ANZNB火影忍者懒人包\
[02:09:20.789] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:09:20.906] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:09:21.224] NetRequestOnce 失败：你的网络环境不佳，导致难以连接到服务器。请稍后重试，或使用 VPN 以改善网络环境。

————————————
详细错误信息：
网络请求失败（SendFailure，基础连接已经关闭: 发送时发生错误。，https://api.curseforge.com/v1/fingerprints/432）
   错误类型：System.Net.WebException
→ 基础连接已经关闭: 发送时发生错误。
   在 System.Net.HttpWebRequest.GetRequestStream(TransportContext& context)
   在 System.Net.HttpWebRequest.GetRequestStream()
   在 PCL.ModNet.NetRequestOnce(String Url, String Method, Object Data, String ContentType, Int32 Timeout, Dictionary`2 Headers, Boolean MakeLog, Boolean UseBrowserUserAgent)
   错误类型：System.Net.WebException
→ 由于远程方已关闭传输流，身份验证失败。
   在 System.Net.Security.SslState.StartReadFrame(Byte[] buffer, Int32 readBytes, AsyncProtocolRequest asyncRequest)
   在 System.Net.Security.SslState.StartReceiveBlob(Byte[] buffer, AsyncProtocolRequest asyncRequest)
   在 System.Net.Security.SslState.CheckCompletionBeforeNextReceive(ProtocolToken message, AsyncProtocolRequest asyncRequest)
   在 System.Net.Security.SslState.ForceAuthentication(Boolean receiveFirst, Byte[] buffer, AsyncProtocolRequest asyncRequest, Boolean renegotiation)
   在 System.Net.Security.SslState.ProcessAuthentication(LazyAsyncResult lazyResult)
   在 System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state, Boolean preserveSyncCtx)
   在 System.Threading.ExecutionContext.Run(ExecutionContext executionContext, ContextCallback callback, Object state, Boolean preserveSyncCtx)
   在 System.Threading.ExecutionContext.Run(ExecutionContext executionContext, ContextCallback callback, Object state)
   在 System.Net.TlsStream.ProcessAuthentication(LazyAsyncResult result)
   在 System.Net.TlsStream.Write(Byte[] buffer, Int32 offset, Int32 size)
   在 System.Net.PooledStream.Write(Byte[] buffer, Int32 offset, Int32 size)
   在 System.Net.ConnectStream.WriteHeaders(Boolean async)
   错误类型：System.IO.IOException
[02:09:21.224] 从 CurseForge 获取本地 Mod 信息失败：你的网络环境不佳，导致难以连接到服务器。请稍后重试，或使用 VPN 以改善网络环境。

————————————
详细错误信息：
连接服务器超时，请检查你的网络环境是否良好（操作已超时。，https://api.curseforge.com/v1/fingerprints/432）
网络请求失败（SendFailure，基础连接已经关闭: 发送时发生错误。，https://api.curseforge.com/v1/fingerprints/432）

   在 PCL.ModDownload.DlModRequest(String Url, String Method, String Data, String ContentType)
   在 PCL.ModMod._Closure$__6-0._Lambda$__4()
[02:09:21.239] [Mod] 联网获取本地 Mod 信息完成，为 161 个 Mod 更新缓存
[02:09:21.243] [Loader] 加载器 Mod List Detail Loader 状态改变：Finished
[02:09:21.345] [Control] 按下单选列表项：modding
[02:09:21.346] [Setup] 当前选择的 Minecraft 文件夹：C:\Users\<USER>\Desktop\.minecraft\
[02:09:21.346] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:09:21.347] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:09:21.349] [Setup] 当前选择的 Minecraft 版本：Chinese Cuisine
[02:09:21.349] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\
[02:09:21.351] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:09:21.471] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:09:21.881] [Control] 按下单选列表项：官方启动器文件夹
[02:09:21.881] [Setup] 当前选择的 Minecraft 文件夹：C:\Users\<USER>\AppData\Roaming\.minecraft\
[02:09:21.882] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:09:21.882] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:09:21.883] [Setup] 当前选择的 Minecraft 版本：[幽]ANZNB火影忍者懒人包
[02:09:21.883] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\AppData\Roaming\.minecraft\versions\[幽]ANZNB火影忍者懒人包\
[02:09:21.886] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:09:22.024] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:09:23.029] [Control] 按下单选列表项：modding
[02:09:23.031] [Setup] 当前选择的 Minecraft 文件夹：C:\Users\<USER>\Desktop\.minecraft\
[02:09:23.031] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:09:23.031] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:09:23.033] [Setup] 当前选择的 Minecraft 版本：Chinese Cuisine
[02:09:23.033] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\
[02:09:23.035] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:09:23.179] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:09:23.408] [Control] 按下单选列表项：paBEN10
[02:09:23.408] [Setup] 当前选择的 Minecraft 文件夹：D:\paBEN10\
[02:09:23.408] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:09:23.409] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:09:23.414] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:09:23.414] [Setup] 当前选择的 Minecraft 版本：1.21.4-Fabric 0.16.14
[02:09:23.414] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\
[02:09:23.532] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:09:24.409] [Control] 按下单选列表项：寰宇特摄
[02:09:24.409] [Setup] 当前选择的 Minecraft 文件夹：D:\寰宇特摄\
[02:09:24.409] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:09:24.411] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:09:24.412] [Setup] 当前选择的 Minecraft 版本：Immersive Fight 3.2.3
[02:09:24.412] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\寰宇特摄\versions\Immersive Fight 3.2.3\
[02:09:24.415] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:09:24.541] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:09:25.083] [Control] 按下单选列表项：paBEN10
[02:09:25.084] [Setup] 当前选择的 Minecraft 文件夹：D:\paBEN10\
[02:09:25.084] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:09:25.085] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:09:25.087] [Setup] 当前选择的 Minecraft 版本：1.21.4-Fabric 0.16.14
[02:09:25.087] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\
[02:09:25.090] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:09:25.208] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:09:25.556] [Control] 按下单选列表项：特摄
[02:09:25.557] [Setup] 当前选择的 Minecraft 文件夹：D:\特摄\
[02:09:25.557] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:09:25.562] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:09:25.563] [Setup] 当前选择的 Minecraft 版本：MARSHY'S SECRETS OF THE OMNITRIX - BETA PALLADIUM MODPACK
[02:09:25.563] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\特摄\versions\MARSHY'S SECRETS OF THE OMNITRIX - BETA PALLADIUM MODPACK\
[02:09:25.564] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:09:25.687] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:09:28.465] [Control] 按下图标按钮
[02:09:28.465] [Control] 切换主要页面：VersionSetup, 0
[02:09:32.010] [Control] 按下单选列表项：Mod 管理
[02:09:32.182] [Loader] 加载器 Mod List Loader 状态改变：Loading
[02:09:32.182] [System] 已刷新 Mod 列表
[02:09:32.541] [Animation] 两个动画帧间隔 375 ms
[02:09:32.846] [Mod] 共有 49 个 Mod，其中 49 个需要联网获取信息，0 个需要更新信息
[02:09:32.847] [Loader] 加载器 Mod List Detail Loader 状态改变：Loading
[02:09:32.847] [Mod] 目标加载器：Forge，版本：1.16.5
[02:09:32.847] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files），最大超时 5000
[02:09:32.955] [Loader] 加载器 Mod List Loader 状态改变：Finished
[02:09:33.176] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/fingerprints/432），最大超时 5000
[02:09:33.369] [Mod] 从 Modrinth 获取到 20 个本地 Mod 的对应信息
[02:09:33.370] [Mod] 需要从 Modrinth 获取 19 个本地 Mod 的工程信息
[02:09:33.370] [Net] 发起网络请求（GET，https://api.modrinth.com/v2/projects?ids=["lhGA9TYQ","wHboX6Zr","IXWeU7kR","uy4Cnpcm","40FYwb4z","9s6osm5g","e0M1UDsY","kr1RaEqy","t5W7Jfwy","gedNE4y2","RvVFJXfy","zQKV24yz","twkfQtEc","iZ10HXDj","JkSi2Fzx","Ins7SzzR","nmDcB62a","EsAfCjCV","uXXizFIs"]），最大超时 5000
[02:09:33.493] 加载图片失败（C:\Users\<USER>\AppData\Local\Temp\PCL\MyImage\16714955283410638088.png）：加载 MyBitmap 失败（C:\Users\<USER>\AppData\Local\Temp\PCL\MyImage\16714955283410638088.png）
   在 PCL.MyBitmap..ctor(String FilePathOrResourceName)
   在 PCL.MyImage.VisitInitializer(String value)
→ Failed to decode WebP image with error 0
   在 Imazen.WebP.SimpleDecoder.DecodeFromPointer(IntPtr data, Int64 length)
   在 Imazen.WebP.SimpleDecoder.DecodeFromBytes(Byte[] data, Int64 length)
   在 PCL.MyBitmap.WebPDecoder.DecodeFromBytes(Byte[] Bytes)
   在 PCL.MyBitmap..ctor(String FilePathOrResourceName)
[02:09:34.205] [Mod] 已从 Modrinth 获取本地 Mod 信息，继续获取更新信息
[02:09:34.205] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files/update），最大超时 5000
[02:09:34.377] [Mod] 从 CurseForge 获取到 26 个本地 Mod 的对应信息
[02:09:34.378] [Mod] 需要从 CurseForge 获取 26 个本地 Mod 的工程信息
[02:09:34.379] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/mods），最大超时 5000
[02:09:34.719] [Mod] 从 Modrinth 获取本地 Mod 信息结束
[02:09:34.989] [Mod] 已从 CurseForge 获取本地 Mod 信息，需要获取 24 个用于检查更新的文件信息
[02:09:34.989] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/mods/files），最大超时 5000
[02:09:36.059] [Mod] 从 CurseForge 获取 Mod 更新信息结束
[02:09:36.060] [Mod] 联网获取本地 Mod 信息完成，为 28 个 Mod 更新缓存
[02:09:36.062] [Loader] 加载器 Mod List Detail Loader 状态改变：Finished
[02:12:18.162] [Control] 按下图标按钮：BtnTitleClose
[02:12:18.172] [System] 收到关闭指令
[02:12:18.392] [System] 程序已退出，返回值：Success
