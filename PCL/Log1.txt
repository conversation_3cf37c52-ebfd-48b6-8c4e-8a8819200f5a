[20:29:05.487] [Start] 程序版本：Release 2.10.3 (361)
[20:29:05.487] [Start] 识别码：93DA-DA21-65DF-59A3，已解锁反馈主题
[20:29:05.487] [Start] 程序路径：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[20:29:05.488] [Start] 系统编码：gb2312 (936, GBK=True)
[20:29:05.488] [Start] 管理员权限：False
[20:29:05.498] [Start] 第一阶段加载用时：94 ms
[20:29:05.784] [UI] 刷新主题：0
[20:29:05.839] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[20:29:05.839] [Start] 第二阶段加载用时：343 ms
[20:29:05.855] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[20:29:06.279] [System] 窗口已置顶，位置：(403.333333333333, 231.333333333333), 900 x 556
[20:29:06.285] [Animation] 动画线程开始
[20:29:06.285] [Start] 第三阶段加载用时：63 ms
[20:29:06.288] [Start] 加载 DLL：Json
[20:29:06.289] [System] 获取资源：Json
[20:29:06.297] [Launch] Minecraft 文件夹：C:\Users\<USER>\Desktop\.minecraft\
[20:29:06.337] [Loader] 加载器 Loader Skin Ms 状态改变：Loading
[20:29:06.343] [Loader] 加载器 Loader Skin Ms 状态改变：Finished
[20:29:06.357] [Java] 缓存中有 10 个可用的 Java：
[20:29:06.358] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[20:29:06.358] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[20:29:06.358] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[20:29:06.358] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[20:29:06.358] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[20:29:06.358] [Java]  - JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[20:29:06.358] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[20:29:06.358] [Java]  - JDK 21 (21.0.8)：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\
[20:29:06.358] [Java]  - JDK 21 (21.0.5)：C:\Program Files\Java\jdk-21\bin\
[20:29:06.358] [Java]  - JDK 11 (11.0.24)：C:\Program Files\Java\jdk-11\bin\
[20:29:06.368] [Page] 实例化：清空主页 UI，来源为空
[20:29:06.378] [Skin] 载入头像成功：Loader Skin Ms
[20:29:06.386] [Minecraft] 启动按钮：Minecraft 版本：C:\Users\<USER>\Desktop\.minecraft\versions\1.21.1-Fabric 0.16.14\
[20:29:06.390] [Loader] 加载器 登录 状态改变：Loading
[20:29:06.395] [Launch] 登录加载已开始
[20:29:06.396] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[20:29:06.397] [Launch] 登录方式：正版（Quasar2323）
[20:29:06.398] [Launch] 开始微软登录步骤 1/6（刷新登录）
[20:29:06.403] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[20:29:06.474] [Loader] 加载器 DlClientList Mojang 状态改变：Loading
[20:29:06.475] [Loader] 加载器 PCL 服务 状态改变：Loading
[20:29:06.475] [Server] 正在连接到 PCL 服务器
[20:29:06.476] [System] 开始清理任务缓存文件夹
[20:29:06.478] [Net] 获取网络结果：https://launchermeta.mojang.com/mc/game/version_manifest.json，超时 10000ms，要求 json
[20:29:06.482] [Net] 获取网络结果：https://pcl2-server-1253424809.file.myqcloud.com/notice.cfg?sign=1754051346-5069241c-0-7867dcf18159a76a5e505fcfdce6a962，超时 10000ms
[20:29:06.489] [System] 已清理任务缓存文件夹
[20:29:06.654] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[20:29:07.008] [System] DPI：144，系统版本：Microsoft Windows NT 10.0.22631.0，PCL 位置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[20:29:07.140] [Download] Mojang 官方源加载耗时：672ms，可优先使用官方源
[20:29:07.144] [Loader] 加载器 DlClientList Mojang 状态改变：Finished
[20:29:07.594] [Launch] 开始微软登录步骤 2/6
[20:29:07.594] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[20:29:07.854] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[20:29:08.365] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[20:29:08.611] [Control] 按下图标按钮：BtnTitleMin
[20:29:09.117] [Launch] 开始微软登录步骤 3/6
[20:29:09.117] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[20:29:09.376] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[20:29:09.888] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[20:29:10.644] [Launch] 开始微软登录步骤 4/6
[20:29:10.657] [Net] 发起网络请求（POST，https://api.minecraftservices.com/authentication/login_with_xbox），最大超时 15000
[20:29:11.661] [Launch] 开始微软登录步骤 5/6
[20:29:11.662] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[20:29:11.919] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[20:29:12.522] [Launch] 开始微软登录步骤 6/6
[20:29:12.522] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[20:29:12.788] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[20:29:13.306] [Launch] 微软登录完成
[20:29:13.306] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[20:29:13.313] [Launch] 登录加载已结束
[20:29:13.313] [Loader] 加载器 登录 状态改变：Finished
[20:29:17.039] [Net] 获取网络结果：https://pcl2-server-1253424809.file.myqcloud.com/notice.cfg?sign=1754051357-6084512c-0-0d4b5655c4db59748d9e67a87698b03d，超时 30000ms
[20:29:17.917] [Server] 服务器公告：171|364|361|5，无需更新
[20:29:17.926] [Loader] 加载器 PCL 服务 状态改变：Finished
[20:48:02.295] [Animation] 两个动画帧间隔 329 ms
[21:02:05.884] [Animation] 两个动画帧间隔 781 ms
[21:02:14.074] [Animation] 两个动画帧间隔 250 ms
[21:05:54.818] [Animation] 两个动画帧间隔 203 ms
[21:26:11.250] [Animation] 两个动画帧间隔 735 ms
[21:31:10.690] [Animation] 两个动画帧间隔 750 ms
[21:33:37.557] [Animation] 两个动画帧间隔 1594 ms
[22:22:48.652] [Animation] 两个动画帧间隔 219 ms
[22:26:19.395] [Animation] 两个动画帧间隔 203 ms
[23:03:04.484] [Animation] 两个动画帧间隔 281 ms
[23:07:21.193] [Control] 按下按钮：版本选择
[23:07:21.256] [Control] 切换主要页面：VersionSelect, 0
[23:07:21.396] [Minecraft] 有效的 Minecraft 文件夹：modding > C:\Users\<USER>\Desktop\.minecraft\
[23:07:21.398] [Minecraft] 有效的 Minecraft 文件夹：官方启动器文件夹 > C:\Users\<USER>\AppData\Roaming\.minecraft\
[23:07:21.401] [Minecraft] 有效的 Minecraft 文件夹：(导入包)奥特英雄激战重奏 > D:\(导入包)奥特英雄激战重奏\.minecraft\
[23:07:21.404] [Minecraft] 有效的 Minecraft 文件夹：ben10 > D:\ben10\
[23:07:21.405] [Minecraft] 有效的 Minecraft 文件夹：BEN 10 > D:\BEN 10\
[23:07:21.407] [Minecraft] 有效的 Minecraft 文件夹：寰宇特摄 > D:\寰宇特摄\
[23:07:21.410] [Minecraft] 有效的 Minecraft 文件夹：paBEN10 > D:\paBEN10\
[23:07:21.419] [Minecraft] 有效的 Minecraft 文件夹：特摄 > D:\特摄\
[23:07:21.442] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[23:07:21.466] [Setup] 当前选择的 Minecraft 版本：1.21.1-Fabric 0.16.14
[23:07:21.466] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\Desktop\.minecraft\versions\1.21.1-Fabric 0.16.14\
[23:07:21.586] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[23:07:22.946] [Control] 按下单选列表项：paBEN10
[23:07:22.952] [Setup] 当前选择的 Minecraft 文件夹：D:\paBEN10\
[23:07:22.952] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[23:07:22.953] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[23:07:22.959] [Setup] 当前选择的 Minecraft 版本：1.21.4-Fabric 0.16.14
[23:07:22.959] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\
[23:07:22.963] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[23:07:23.103] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[23:07:24.886] [Control] 按下单选列表项：ben10
[23:07:24.887] [Setup] 当前选择的 Minecraft 文件夹：D:\ben10\
[23:07:24.887] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[23:07:24.888] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[23:07:24.893] [Setup] 当前选择的 Minecraft 版本：MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK
[23:07:24.893] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\ben10\versions\MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK\
[23:07:24.894] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[23:07:25.017] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[23:07:25.440] [Control] 按下单选列表项：BEN 10
[23:07:25.441] [Setup] 当前选择的 Minecraft 文件夹：D:\BEN 10\
[23:07:25.441] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[23:07:25.441] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[23:07:25.446] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[23:07:25.460] 版本 JSON 可用性检查失败（D:\BEN 10\versions\Kevin_11-1.12.2-3.0\）：未找到版本 JSON 文件：D:\BEN 10\versions\Kevin_11-1.12.2-3.0\Kevin_11-1.12.2-3.0.json
   在 PCL.ModMinecraft.McVersion.DisableException()
   在 PCL.ModMinecraft.McVersion.AssetException()
   在 PCL.ModMinecraft.McVersion.Check()
[23:07:25.463] 版本 JSON 可用性检查失败（D:\BEN 10\versions\〖矛盾附属〗Random Ben 10 Stuff 1.0\）：未找到版本 JSON 文件：D:\BEN 10\versions\〖矛盾附属〗Random Ben 10 Stuff 1.0\〖矛盾附属〗Random Ben 10 Stuff 1.0.json
   在 PCL.ModMinecraft.McVersion.DisableException()
   在 PCL.ModMinecraft.McVersion.AssetException()
   在 PCL.ModMinecraft.McVersion.Check()
[23:07:25.465] [Setup] 当前选择的 Minecraft 版本：Ben10Craft
[23:07:25.465] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\BEN 10\versions\Ben10Craft\
[23:07:25.585] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[23:07:26.576] [Control] 按下单选列表项：寰宇特摄
[23:07:26.577] [Setup] 当前选择的 Minecraft 文件夹：D:\寰宇特摄\
[23:07:26.577] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[23:07:26.581] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[23:07:26.583] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[23:07:26.583] [Setup] 当前选择的 Minecraft 版本：Immersive Fight 3.2.3
[23:07:26.583] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\寰宇特摄\versions\Immersive Fight 3.2.3\
[23:07:26.719] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[23:07:27.283] [Control] 按下单选列表项：paBEN10
[23:07:27.284] [Setup] 当前选择的 Minecraft 文件夹：D:\paBEN10\
[23:07:27.284] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[23:07:27.284] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[23:07:27.286] [Setup] 当前选择的 Minecraft 版本：1.21.4-Fabric 0.16.14
[23:07:27.286] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\
[23:07:27.290] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[23:07:27.412] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[23:07:28.834] [Control] 按下单选列表项：特摄
[23:07:28.835] [Setup] 当前选择的 Minecraft 文件夹：D:\特摄\
[23:07:28.835] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[23:07:28.836] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[23:07:28.839] [Setup] 当前选择的 Minecraft 版本：1.16.5-Forge_36.2.42
[23:07:28.839] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\特摄\versions\1.16.5-Forge_36.2.42\
[23:07:28.840] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[23:07:28.972] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[23:07:29.590] [Control] 按下单选列表项：paBEN10
[23:07:29.591] [Setup] 当前选择的 Minecraft 文件夹：D:\paBEN10\
[23:07:29.591] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[23:07:29.591] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[23:07:29.592] [Setup] 当前选择的 Minecraft 版本：1.21.4-Fabric 0.16.14
[23:07:29.592] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\
[23:07:29.595] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[23:07:29.722] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[23:07:30.239] [Control] 按下单选列表项：特摄
[23:07:30.240] [Setup] 当前选择的 Minecraft 文件夹：D:\特摄\
[23:07:30.240] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[23:07:30.240] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[23:07:30.241] [Setup] 当前选择的 Minecraft 版本：1.16.5-Forge_36.2.42
[23:07:30.241] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\特摄\versions\1.16.5-Forge_36.2.42\
[23:07:30.246] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[23:07:30.369] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[23:07:31.442] [Control] 按下图标按钮
[23:07:31.483] [Control] 切换主要页面：VersionSetup, 0
[23:07:32.988] [Control] 按下单选列表项：Mod 管理
[23:07:33.144] [Loader] 加载器 Mod List Loader 状态改变：Loading
[23:07:33.144] [System] 已刷新 Mod 列表
[23:07:33.681] [Mod] 共有 49 个 Mod，其中 49 个需要联网获取信息，0 个需要更新信息
[23:07:33.701] [Loader] 加载器 Mod List Detail Loader 状态改变：Loading
[23:07:33.706] [Mod] 目标加载器：Forge，版本：1.16.5
[23:07:33.720] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files），最大超时 5000
[23:07:33.929] [Loader] 加载器 Mod List Loader 状态改变：Finished
[23:07:34.153] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/fingerprints/432），最大超时 5000
[23:07:35.142] [Control] 按下按钮：打开文件夹
[23:07:35.143] [System] 正在打开资源管理器：D:\特摄\versions\1.16.5-Forge_36.2.42\mods\
[23:07:35.143] [System] 执行外部命令：D:\特摄\versions\1.16.5-Forge_36.2.42\mods\ 
[23:07:35.189] [Mod] 从 Modrinth 获取到 20 个本地 Mod 的对应信息
[23:07:35.206] [Mod] 需要从 Modrinth 获取 19 个本地 Mod 的工程信息
[23:07:35.207] [Net] 发起网络请求（GET，https://api.modrinth.com/v2/projects?ids=["lhGA9TYQ","wHboX6Zr","IXWeU7kR","uy4Cnpcm","40FYwb4z","9s6osm5g","e0M1UDsY","kr1RaEqy","t5W7Jfwy","gedNE4y2","RvVFJXfy","zQKV24yz","twkfQtEc","iZ10HXDj","JkSi2Fzx","Ins7SzzR","nmDcB62a","EsAfCjCV","uXXizFIs"]），最大超时 5000
[23:07:35.884] [Mod] 已从 Modrinth 获取本地 Mod 信息，继续获取更新信息
[23:07:35.885] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files/update），最大超时 5000
[23:07:35.888] [System] 获取资源：ModData
[23:07:35.918] [Start] 加载 DLL：Imazen.WebP
[23:07:35.918] [System] 获取资源：Imazen_WebP
[23:07:35.921] [System] 获取资源：libwebp64
[23:07:36.003] [Mod] 从 CurseForge 获取到 26 个本地 Mod 的对应信息
[23:07:36.007] [Mod] 需要从 CurseForge 获取 26 个本地 Mod 的工程信息
[23:07:36.007] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/mods），最大超时 5000
[23:07:36.460] [Mod] 从 Modrinth 获取本地 Mod 信息结束
[23:07:36.863] [Mod] 已从 CurseForge 获取本地 Mod 信息，需要获取 24 个用于检查更新的文件信息
[23:07:36.863] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/mods/files），最大超时 5000
[23:07:37.374] [Mod] 从 CurseForge 获取 Mod 更新信息结束
[23:07:37.387] [Mod] 联网获取本地 Mod 信息完成，为 28 个 Mod 更新缓存
[23:07:37.402] [Loader] 加载器 Mod List Detail Loader 状态改变：Finished
[23:07:41.162] [Control] 按下图标按钮：BtnTitleInner
[23:07:41.162] [Control] 切换主要页面：VersionSelect, -1
[23:07:43.196] [Control] 切换主要页面：Launch, -1
[23:07:43.196] [Control] 按下单击列表项：1.16.5-Forge_36.2.42
[23:07:43.311] [Minecraft] 启动按钮：Minecraft 版本：D:\特摄\versions\1.16.5-Forge_36.2.42\
[23:07:43.898] [Control] 按下按钮：启动游戏
[23:07:43.900] [Loader] 加载器 Loader Launch 状态改变：Loading
[23:07:43.911] [Launch] 预检测已通过
[23:07:43.919] [Download] 开始后台检查资源文件索引
[23:07:43.919] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[23:07:43.920] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[23:07:43.922] [Loader] 加载器 登录 状态改变：Waiting
[23:07:43.922] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[23:07:43.922] [Loader] 加载器 获取 Java 状态改变：Loading
[23:07:43.922] [Loader] 加载器 登录 状态改变：Loading
[23:07:43.923] [Loader] 加载器 补全文件 状态改变：Loading
[23:07:43.923] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[23:07:43.923] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[23:07:43.923] [Launch] 登录加载已开始
[23:07:43.923] [Download] 版本 1.16.5-Forge_36.2.42 对应的资源文件索引为 1.16
[23:07:43.923] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[23:07:43.923] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[23:07:43.923] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[23:07:43.923] [Launch] 登录方式：正版（Quasar2323）
[23:07:43.923] [Launch] 开始微软登录步骤 1/6（刷新登录）
[23:07:43.923] [Loader] 加载器 内存优化 状态改变：Loading
[23:07:43.923] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[23:07:43.923] [Download] 版本 1.16.5-Forge_36.2.42 对应的资源文件索引为 1.16
[23:07:43.924] [Launch] 内存优化开始
[23:07:43.924] [Taskbar] Minecraft 启动 已加入任务列表
[23:07:43.927] [Test] 没有管理员权限，将以命令行方式进行内存优化
[23:07:43.927] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Finished
[23:07:43.929] [Loader] 加载器 后台下载资源文件索引 状态改变：Loading
[23:07:43.933] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[23:07:43.933] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[23:07:43.933] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[23:07:43.933] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[23:07:43.987] [Download] 1.16.json 983#：开始，起始点 0，https://piston-meta.mojang.com/v1/packages/f3c4aa96e12951cd2781b3e1c0e8ab82bf719cf2/1.16.json
[23:07:43.990] [Minecraft] 获取支持库列表：1.16.5-Forge_36.2.42
[23:07:44.002] [Minecraft] 发现重复的支持库：295 K | D:\特摄\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\特摄\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[23:07:44.002] [Minecraft] 发现重复的支持库：1.71 M | D:\特摄\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\特摄\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[23:07:44.002] [Minecraft] 发现重复的支持库：76.3 K | D:\特摄\libraries\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar (5.0.4) 与 76.3 K | D:\特摄\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[23:07:44.015] [Launch] Java 版本需求：最低 1.8.0.0，最高 999.999.999.999
[23:07:44.022] [Java] 开始完全遍历查找：D:\特摄\
[23:07:44.045] [Java] 开始完全遍历查找：D:\特摄\versions\1.16.5-Forge_36.2.42\
[23:07:44.061] [Java] 排序后的 Java 优先顺序：
[23:07:44.061] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[23:07:44.061] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[23:07:44.061] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[23:07:44.061] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[23:07:44.061] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[23:07:44.061] [Java]  - JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[23:07:44.061] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[23:07:44.061] [Java]  - JDK 21 (21.0.8)：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\
[23:07:44.061] [Java]  - JDK 21 (21.0.5)：C:\Program Files\Java\jdk-21\bin\
[23:07:44.061] [Java]  - JDK 11 (11.0.24)：C:\Program Files\Java\jdk-11\bin\
[23:07:44.064] [System] 执行外部命令并等待返回结果：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\java.exe -version
[23:07:44.185] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[23:07:44.190] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[23:07:44.190] [Loader] 加载器 下载支持库文件 状态改变：Loading
[23:07:44.190] [Loader] 加载器 下载支持库文件 状态改变：Finished
[23:07:44.190] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[23:07:44.220] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[23:07:44.220] [Loader] 加载器 下载资源文件 状态改变：Loading
[23:07:44.221] [Loader] 加载器 下载资源文件 状态改变：Finished
[23:07:44.221] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[23:07:44.221] [Loader] 加载器 补全文件 状态改变：Finished
[23:07:44.221] [Loader] 由于输入条件变更，重启进行中的加载器 登录
[23:07:44.923] [Download] 1.16.json 983#：文件大小 295227（288 K）
[23:07:45.582] [Launch] 开始微软登录步骤 2/6
[23:07:45.582] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[23:07:45.606] [Java] 最终选定的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[23:07:45.606] [Launch] 选择的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[23:07:45.606] [Loader] 加载器 获取 Java 状态改变：Finished
[23:07:45.606] [Loader] 由于输入条件变更，重启进行中的加载器 登录
[23:07:45.608] [Launch] 登录加载已开始
[23:07:45.610] [Launch] 登录方式：正版（Quasar2323）
[23:07:45.610] [Launch] 开始微软登录步骤 1/6（刷新登录）
[23:07:45.610] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[23:07:45.875] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[23:07:46.082] [Download] 1.16.json：已完成，剩余文件 0
[23:07:46.082] [Loader] 加载器 后台下载资源文件索引 状态改变：Finished
[23:07:46.082] [Loader] 加载器 后台复制资源文件索引 状态改变：Loading
[23:07:46.084] [Launch] 后台更新资源文件索引成功：C:\Users\<USER>\AppData\Local\Temp\PCL\Cache\1.16.json
[23:07:46.084] [Loader] 加载器 后台复制资源文件索引 状态改变：Finished
[23:07:46.084] [Loader] 加载器 后台更新资源文件索引 状态改变：Finished
[23:07:46.385] [Launch] 开始微软登录步骤 2/6
[23:07:46.385] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[23:07:46.646] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[23:07:47.152] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[23:07:47.905] [Launch] 开始微软登录步骤 3/6
[23:07:47.906] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[23:07:48.168] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[23:07:48.671] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[23:07:49.523] [Launch] 开始微软登录步骤 4/6
[23:07:49.523] [Net] 发起网络请求（POST，https://api.minecraftservices.com/authentication/login_with_xbox），最大超时 15000
[23:07:50.802] [Launch] 开始微软登录步骤 5/6
[23:07:50.802] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[23:07:51.056] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[23:07:51.565] [Launch] 开始微软登录步骤 6/6
[23:07:51.565] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[23:07:51.825] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[23:07:52.330] [Launch] 微软登录完成
[23:07:52.330] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[23:07:52.344] [Launch] 登录加载已结束
[23:07:52.344] [Loader] 加载器 登录 状态改变：Finished
[23:07:52.344] [Loader] 由于输入条件变更，重启已完成的加载器 登录
[23:07:52.344] [Loader] 加载器 登录 状态改变：Loading
[23:07:52.345] [Launch] 登录加载已开始
[23:07:52.345] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[23:07:52.345] [Launch] 登录方式：正版（Quasar2323）
[23:07:52.345] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[23:07:52.359] [Launch] 登录加载已结束
[23:07:52.359] [Loader] 加载器 登录 状态改变：Finished
[23:07:56.834] [Test] 内存优化完成，可用内存改变量：2.83 G，大致剩余内存：3.47 G
[23:07:56.875] [Loader] 加载器 内存优化 状态改变：Finished
[23:07:56.882] [Loader] 加载器 获取启动参数 状态改变：Loading
[23:07:56.903] [Launch] 开始获取 Minecraft 启动参数
[23:07:56.903] [Launch] 获取新版 JVM 参数
[23:07:56.914] [Launch] 当前剩余内存：3.4G
[23:07:56.923] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[23:07:56.923] [System] 获取资源：JavaWrapper
[23:07:56.926] [Launch] 新版 JVM 参数获取成功：
[23:07:56.927] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Dos.name=Windows 10 -Dos.version=10.0 -Djava.library.path=${natives_directory} -Dminecraft.launcher.brand=${launcher_name} -Dminecraft.launcher.version=${launcher_version} -cp ${classpath} -XX:+IgnoreUnrecognizedVMOptions --add-exports=java.base/sun.security.util=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=java.naming --add-opens=java.base/java.util.jar=ALL-UNNAMED -Xmn2304m -Xmx15360m -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" cpw.mods.modlauncher.Launcher
[23:07:56.927] [Launch] 获取新版 Game 参数
[23:07:56.932] [Launch] 新版 Game 参数获取成功
[23:07:56.937] [Minecraft] 获取支持库列表：1.16.5-Forge_36.2.42
[23:07:56.938] [Minecraft] 发现重复的支持库：295 K | D:\特摄\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\特摄\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[23:07:56.938] [Minecraft] 发现重复的支持库：1.71 M | D:\特摄\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\特摄\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[23:07:56.938] [Minecraft] 发现重复的支持库：76.3 K | D:\特摄\libraries\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar (5.0.4) 与 76.3 K | D:\特摄\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[23:07:56.942] [Launch] Minecraft 启动参数：
[23:07:56.944] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Dos.name="Windows 10" -Dos.version=10.0 -Djava.library.path="D:\特摄\versions\1.16.5-Forge_36.2.42\1.16.5-Forge_36.2.42-natives" -Dminecraft.launcher.brand=PCL -Dminecraft.launcher.version=361 -cp "D:\特摄\libraries\com\mojang\patchy\1.3.9\patchy-1.3.9.jar;D:\特摄\libraries\oshi-project\oshi-core\1.1\oshi-core-1.1.jar;D:\特摄\libraries\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;D:\特摄\libraries\net\java\dev\jna\platform\3.4.0\platform-3.4.0.jar;D:\特摄\libraries\com\ibm\icu\icu4j\66.1\icu4j-66.1.jar;D:\特摄\libraries\com\mojang\javabridge\1.0.22\javabridge-1.0.22.jar;D:\特摄\libraries\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar;D:\特摄\libraries\io\netty\netty-all\4.1.25.Final\netty-all-4.1.25.Final.jar;D:\特摄\libraries\com\google\guava\guava\21.0\guava-21.0.jar;D:\特摄\libraries\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;D:\特摄\libraries\commons-io\commons-io\2.5\commons-io-2.5.jar;D:\特摄\libraries\commons-codec\commons-codec\1.10\commons-codec-1.10.jar;D:\特摄\libraries\net\java\jinput\jinput\2.0.5\jinput-2.0.5.jar;D:\特摄\libraries\net\java\jutils\jutils\1.0.0\jutils-1.0.0.jar;D:\特摄\libraries\com\mojang\brigadier\1.0.17\brigadier-1.0.17.jar;D:\特摄\libraries\com\mojang\datafixerupper\4.0.26\datafixerupper-4.0.26.jar;D:\特摄\libraries\com\google\code\gson\gson\2.8.0\gson-2.8.0.jar;D:\特摄\libraries\com\mojang\authlib\2.1.28\authlib-2.1.28.jar;D:\特摄\libraries\org\apache\commons\commons-compress\1.8.1\commons-compress-1.8.1.jar;D:\特摄\libraries\org\apache\httpcomponents\httpclient\4.3.3\httpclient-4.3.3.jar;D:\特摄\libraries\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\特摄\libraries\org\apache\httpcomponents\httpcore\4.3.2\httpcore-4.3.2.jar;D:\特摄\libraries\it\unimi\dsi\fastutil\8.2.1\fastutil-8.2.1.jar;D:\特摄\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar;D:\特摄\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar;D:\特摄\libraries\org\lwjgl\lwjgl\3.2.2\lwjgl-3.2.2.jar;D:\特摄\libraries\org\lwjgl\lwjgl-jemalloc\3.2.2\lwjgl-jemalloc-3.2.2.jar;D:\特摄\libraries\org\lwjgl\lwjgl-openal\3.2.2\lwjgl-openal-3.2.2.jar;D:\特摄\libraries\org\lwjgl\lwjgl-opengl\3.2.2\lwjgl-opengl-3.2.2.jar;D:\特摄\libraries\org\lwjgl\lwjgl-glfw\3.2.2\lwjgl-glfw-3.2.2.jar;D:\特摄\libraries\org\lwjgl\lwjgl-stb\3.2.2\lwjgl-stb-3.2.2.jar;D:\特摄\libraries\org\lwjgl\lwjgl-tinyfd\3.2.2\lwjgl-tinyfd-3.2.2.jar;D:\特摄\libraries\com\mojang\text2speech\1.11.3\text2speech-1.11.3.jar;D:\特摄\libraries\net\minecraftforge\forge\1.16.5-36.2.42\forge-1.16.5-36.2.42.jar;D:\特摄\libraries\org\ow2\asm\asm\9.6\asm-9.6.jar;D:\特摄\libraries\org\ow2\asm\asm-commons\9.6\asm-commons-9.6.jar;D:\特摄\libraries\org\ow2\asm\asm-tree\9.6\asm-tree-9.6.jar;D:\特摄\libraries\org\ow2\asm\asm-util\9.6\asm-util-9.6.jar;D:\特摄\libraries\org\ow2\asm\asm-analysis\9.6\asm-analysis-9.6.jar;D:\特摄\libraries\cpw\mods\modlauncher\8.1.3\modlauncher-8.1.3.jar;D:\特摄\libraries\cpw\mods\grossjava9hacks\1.3.3\grossjava9hacks-1.3.3.jar;D:\特摄\libraries\net\minecraftforge\accesstransformers\3.0.1\accesstransformers-3.0.1.jar;D:\特摄\libraries\org\antlr\antlr4-runtime\4.9.1\antlr4-runtime-4.9.1.jar;D:\特摄\libraries\net\minecraftforge\eventbus\4.0.0\eventbus-4.0.0.jar;D:\特摄\libraries\net\minecraftforge\forgespi\3.2.0\forgespi-3.2.0.jar;D:\特摄\libraries\net\minecraftforge\coremods\4.0.6\coremods-4.0.6.jar;D:\特摄\libraries\net\minecraftforge\unsafe\0.2.0\unsafe-0.2.0.jar;D:\特摄\libraries\com\electronwill\night-config\core\3.6.3\core-3.6.3.jar;D:\特摄\libraries\com\electronwill\night-config\toml\3.6.3\toml-3.6.3.jar;D:\特摄\libraries\org\jline\jline\3.12.1\jline-3.12.1.jar;D:\特摄\libraries\org\apache\maven\maven-artifact\3.6.3\maven-artifact-3.6.3.jar;D:\特摄\libraries\net\jodah\typetools\0.8.3\typetools-0.8.3.jar;D:\特摄\libraries\org\apache\logging\log4j\log4j-slf4j18-impl\2.15.0\log4j-slf4j18-impl-2.15.0.jar;D:\特摄\libraries\net\minecrell\terminalconsoleappender\1.2.0\terminalconsoleappender-1.2.0.jar;D:\特摄\libraries\org\spongepowered\mixin\0.8.4\mixin-0.8.4.jar;D:\特摄\libraries\net\minecraftforge\nashorn-core-compat\15.1.1.1\nashorn-core-compat-15.1.1.1.jar;D:\特摄\versions\1.16.5-Forge_36.2.42\1.16.5-Forge_36.2.42.jar" -XX:+IgnoreUnrecognizedVMOptions --add-exports=java.base/sun.security.util=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=java.naming --add-opens=java.base/java.util.jar=ALL-UNNAMED -Xmn2304m -Xmx15360m -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" cpw.mods.modlauncher.Launcher --username Quasar2323 --version 1.16.5-Forge_36.2.42 --gameDir "D:\特摄\versions\1.16.5-Forge_36.2.42" --assetsDir "D:\特摄\assets" --assetIndex 1.16 --uuid 077e3afcb77643c48ae30f9dbb755bef --accessToken eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************uEhxQ --userType msa --versionType PCL --width 854 --height 480 --launchTarget fmlclient --fml.forgeVersion 36.2.42 --fml.mcVersion 1.16.5 --fml.forgeGroup net.minecraftforge --fml.mcpVersion 20210115.111550 
[23:07:56.944] [Loader] 加载器 获取启动参数 状态改变：Finished
[23:07:56.955] [Loader] 加载器 解压文件 状态改变：Loading
[23:07:56.960] [Launch] 正在解压 Natives 文件
[23:07:56.966] [Loader] 加载器 解压文件 状态改变：Finished
[23:07:56.983] [Loader] 加载器 预启动处理 状态改变：Loading
[23:07:56.999] [System] 无需调整显卡设置：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[23:07:56.999] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[23:07:57.007] [Launch] 已更新 launcher_profiles.json
[23:07:57.008] [Launch] 需要的语言为 zh_cn，当前语言为 zh_cn，无需修改
[23:07:57.008] [Loader] 加载器 预启动处理 状态改变：Finished
[23:07:57.017] [Loader] 加载器 执行自定义命令 状态改变：Loading
[23:07:57.032] [Loader] 加载器 执行自定义命令 状态改变：Finished
[23:07:57.057] [Loader] 加载器 启动进程 状态改变：Loading
[23:07:57.310] [Launch] 已启动游戏进程：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[23:07:57.311] [Loader] 加载器 启动进程 状态改变：Finished
[23:07:57.342] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[23:07:57.344] [Launch] 
[23:07:57.345] [Launch] ~ 基础参数 ~
[23:07:57.345] [Launch] PCL 版本：Release 2.10.3 (361)
[23:07:57.345] [Launch] 游戏版本：1.16.5, Forge 36.2.42（识别为 1.16.5）
[23:07:57.345] [Launch] 资源版本：1.16
[23:07:57.345] [Launch] 版本继承：无
[23:07:57.345] [Launch] 分配的内存：15 GB（15360 MB）
[23:07:57.345] [Launch] MC 文件夹：D:\特摄\
[23:07:57.345] [Launch] 版本文件夹：D:\特摄\versions\1.16.5-Forge_36.2.42\
[23:07:57.345] [Launch] 版本隔离：True
[23:07:57.345] [Launch] HMCL 格式：False
[23:07:57.345] [Launch] Java 信息：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[23:07:57.345] [Launch] 环境变量：未设置
[23:07:57.345] [Launch] Natives 文件夹：D:\特摄\versions\1.16.5-Forge_36.2.42\1.16.5-Forge_36.2.42-natives
[23:07:57.345] [Launch] 
[23:07:57.345] [Launch] ~ 登录参数 ~
[23:07:57.345] [Launch] 玩家用户名：Quasar2323
[23:07:57.345] [Launch] AccessToken：eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************uEhxQ
[23:07:57.345] [Launch] ClientToken：077e3afcb77643c48ae30f9dbb755bef
[23:07:57.345] [Launch] UUID：077e3afcb77643c48ae30f9dbb755bef
[23:07:57.345] [Launch] 登录方式：Microsoft
[23:07:57.345] [Launch] 
[23:07:57.359] [Launch] [29136] 开始 Minecraft 日志监控
[23:07:57.360] [Launch] [全局] 出现运行中的 Minecraft
[23:07:59.099] [Launch] [29136] 日志 1/5：已出现日志输出
[23:08:00.466] [Launch] [29136] Mod Loader 窗口已加载：FML early loading progress（1509230）
[23:08:02.484] [Launch] [29136] Minecraft 加载已完成
[23:08:02.583] [Loader] 加载器 等待游戏窗口出现 状态改变：Finished
[23:08:02.584] [Loader] 加载器 结束处理 状态改变：Loading
[23:08:02.585] [Launch] 开始启动结束处理
[23:08:02.586] [Launch] 启动器可见性：5
[23:08:02.586] [Loader] 加载器 结束处理 状态改变：Finished
[23:08:02.632] [Loader] 加载器 Minecraft 启动 状态改变：Finished
[23:08:02.646] [Taskbar] Minecraft 启动 已移出任务列表
[23:08:02.686] [Loader] 加载器 Loader Launch 状态改变：Finished
[23:08:02.714] [UI] 弹出提示：1.16.5-Forge_36.2.42 启动成功！
[23:08:06.463] [Launch] [29136] Minecraft 已退出，返回值：1
[23:08:06.463] [Launch] [29136] Minecraft 返回值异常，可能已崩溃
[23:08:06.465] [Launch] [全局] 已无运行中的 Minecraft
[23:08:06.465] [Launch] [29136] Minecraft 已崩溃，将在 2 秒后开始崩溃分析
[23:08:06.549] [UI] 弹出提示：检测到 Minecraft 出现错误，错误分析已开始……
[23:08:06.791] [System] 诊断信息：
操作系统：Microsoft Windows 11 家庭中文版（32 位：False）
剩余内存：1702 M / 16108 M
DPI：144（150%）
MC 文件夹：D:\特摄\
文件位置：C:\Users\<USER>\Desktop\
[23:08:07.019] [Launch] [29136] Minecraft 日志监控已退出
[23:08:08.798] [Launch] [29136] 崩溃分析开始
[23:08:08.809] [Crash] 崩溃分析暂存文件夹：C:\Users\<USER>\AppData\Local\Temp\PCL\TaskTemp\1115-327505\
[23:08:08.813] [Crash] 步骤 1：收集日志文件
[23:08:08.814] [Crash] 可能可用的日志文件：D:\特摄\versions\1.16.5-Forge_36.2.42\logs\latest.log（0 分钟）
[23:08:08.814] [Crash] 可能可用的日志文件：D:\特摄\versions\1.16.5-Forge_36.2.42\logs\debug.log（0 分钟）
[23:08:08.817] [Crash] 以下为游戏输出的最后一段内容：
2025-08-01 23:07:59,084 main WARN Advanced terminal features are not available in this environment
[23:07:59] [main/INFO] [cp.mo.mo.Launcher/MODLAUNCHER]: ModLauncher running: args [--username, Quasar2323, --version, 1.16.5-Forge_36.2.42, --gameDir, D:\鐗规憚\versions\1.16.5-Forge_36.2.42, --assetsDir, D:\鐗规憚\assets, --assetIndex, 1.16, --uuid, 077e3afcb77643c48ae30f9dbb755bef, --accessToken, 鉂勨潉鉂勨潉鉂勨潉鉂勨潉, --userType, msa, --versionType, PCL, --width, 854, --height, 480, --launchTarget, fmlclient, --fml.forgeVersion, 36.2.42, --fml.mcVersion, 1.16.5, --fml.forgeGroup, net.minecraftforge, --fml.mcpVersion, 20210115.111550]
[23:07:59] [main/INFO] [cp.mo.mo.Launcher/MODLAUNCHER]: ModLauncher 8.1.3+8.1.3+main-8.1.x.c94d18ec starting: java version 1.8.0_462 by IBM Corporation
[23:07:59] [main/INFO] [ne.mi.fm.lo.FixSSL/CORE]: Added Lets Encrypt root certificates as additional trust
[23:07:59] [main/INFO] [mixin/]: SpongePowered MIXIN Subsystem Version=0.8.4 Source=file:/D:/鐗规憚/libraries/org/spongepowered/mixin/0.8.4/mixin-0.8.4.jar Service=ModLauncher Env=CLIENT
[23:08:02] [main/ERROR] [mixin/]: Mixin config rotp_zhp.mixin.json does not specify "minVersion" property
[23:08:02] [main/INFO] [cp.mo.mo.LaunchServiceHandler/MODLAUNCHER]: Launching target 'fmlclient' with arguments [--version, 1.16.5-Forge_36.2.42, --gameDir, D:\鐗规憚\versions\1.16.5-Forge_36.2.42, --assetsDir, D:\鐗规憚\assets, --uuid, 077e3afcb77643c48ae30f9dbb755bef, --username, Quasar2323, --assetIndex, 1.16, --accessToken, 鉂勨潉鉂勨潉鉂勨潉鉂勨潉, --userType, msa, --versionType, PCL, --width, 854, --height, 480]
[23:08:02] [main/INFO] [ModernFix/]: Loaded configuration file for ModernFix 5.18.0+mc1.16.5: 89 options available, 0 override(s) found
[23:08:02] [main/INFO] [ModernFix/]: Applied Forge config corruption patch
[23:08:02] [main/WARN] [mixin/]: Reference map 'rotp_ab.refmap.json' for mixins.rotp_ab.json could not be read. If this is a development environment you can ignore this message
[23:08:02] [main/WARN] [mixin/]: Reference map 'rotp_mih.mixins.refmap.json' for rotp_mih.mixins.json could not be read. If this is a development environment you can ignore this message
[23:08:02] [main/WARN] [mixin/]: Reference map 'rotp_whitesnake.mixin-refmap.json' for rotp_whitesnake.mixin.json could not be read. If this is a development environment you can ignore this message
[23:08:03] [main/WARN] [mixin/]: Error loading class: yesman/epicfight/client/renderer/patched/entity/PPlayerRenderer (java.lang.ClassNotFoundException: null)
[23:08:03] [main/WARN] [mixin/]: @Mixin target yesman.epicfight.client.renderer.patched.entity.PPlayerRenderer was not found rotp_zhp.mixin.json:MixinPPlayerRenderer
[23:08:03] [main/INFO] [MixinExtras|Service/]: Initializing MixinExtras via virtuoel.mixinextras.service.MixinExtrasServiceImpl(version=0.3.6).
[23:08:03] [pool-3-thread-1/INFO] [minecraft/Bootstrap]: ModernFix reached bootstrap stage (5.359 s after launch)
[23:08:04] [pool-3-thread-1/INFO] [FerriteCore - class definer/]: Using Java 8 class definer
[23:08:06] [pool-3-thread-1/FATAL] [mixin/]: Mixin apply failed mixins.rotp_ab.json:BlockItemMixin -> net.minecraft.item.BlockItem: org.spongepowered.asm.mixin.injection.throwables.InvalidInjectionException Critical injection failure: @Inject annotation on onBlockPlaced could not find any targets matching 'place' in net.minecraft.item.BlockItem. No refMap loaded. [PREINJECT Applicator Phase -> mixins.rotp_ab.json:BlockItemMixin -> Prepare Injections ->  -> handler$bea000$onBlockPlaced(Lnet/minecraft/item/BlockItemUseContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V -> Parse]
org.spongepowered.asm.mixin.injection.throwables.InvalidInjectionException: Critical injection failure: @Inject annotation on onBlockPlaced could not find any targets matching 'place' in net.minecraft.item.BlockItem. No refMap loaded. [PREINJECT Applicator Phase -> mixins.rotp_ab.json:BlockItemMixin -> Prepare Injections ->  -> handler$bea000$onBlockPlaced(Lnet/minecraft/item/BlockItemUseContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V -> Parse]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.validateTargets(InjectionInfo.java:656) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {re:classloading,re:classloading}
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.findTargets(InjectionInfo.java:587) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {re:classloading,re:classloading}
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.readAnnotation(InjectionInfo.java:330) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {re:classloading,re:classloading}
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.<init>(InjectionInfo.java:316) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {re:classloading,re:classloading}
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.<init>(InjectionInfo.java:308) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {re:classloading,re:classloading}
	at org.spongepowered.asm.mixin.injection.struct.CallbackInjectionInfo.<init>(CallbackInjectionInfo.java:46) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {}
	at sun.reflect.GeneratedConstructorAccessor24.newInstance(Unknown Source) ~[?:?] {}
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_462] {}
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_462] {}
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo$InjectorEntry.create(InjectionInfo.java:149) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {re:classloading}
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.parse(InjectionInfo.java:708) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {re:classloading,re:classloading}
	at org.spongepowered.asm.mixin.transformer.MixinTargetContext.prepareInjections(MixinTargetContext.java:1311) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {re:classloading}
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.prepareInjections(MixinApplicatorStandard.java:1042) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {}
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.applyMixin(MixinApplicatorStandard.java:393) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {}
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.apply(MixinApplicatorStandard.java:325) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {}
	at org.spongepowered.asm.mixin.transformer.TargetClassContext.apply(TargetClassContext.java:383) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {re:classloading}
	at org.spongepowered.asm.mixin.transformer.TargetClassContext.applyMixins(TargetClassContext.java:365) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {re:classloading}
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:363) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {}
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClass(MixinTransformer.java:250) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {}
	at org.spongepowered.asm.service.modlauncher.MixinTransformationHandler.processClass(MixinTransformationHandler.java:131) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {}
	at org.spongepowered.asm.launch.MixinLaunchPluginLegacy.processClass(MixinLaunchPluginLegacy.java:131) ~[mixin-0.8.4.jar:0.8.4+Jenkins-b308.git-2accda5000f7602229606b39437565542cc6fba4] {}
	at cpw.mods.modlauncher.serviceapi.ILaunchPluginService.processClassWithFlags(ILaunchPluginService.java:154) ~[modlauncher-8.1.3.jar:8.1.3+8.1.3+main-8.1.x.c94d18ec] {}
	at cpw.mods.modlauncher.LaunchPluginHandler.offerClassNodeToPlugins(LaunchPluginHandler.java:85) ~[modlauncher-8.1.3.jar:?] {}
	at cpw.mods.modlauncher.ClassTransformer.transform(ClassTransformer.java:120) ~[modlauncher-8.1.3.jar:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader$DelegatedClassLoader.findClass(TransformingClassLoader.java:265) ~[modlauncher-8.1.3.jar:?] {}
	at cpw.mods.modlauncher.TransformingClassLoader.loadClass(TransformingClassLoader.java:136) ~[modlauncher-8.1.3.jar:?] {re:classloading}
	at cpw.mods.modlauncher.TransformingClassLoader.loadClass(TransformingClassLoader.java:98) ~[modlauncher-8.1.3.jar:?] {re:classloading}
	at java.lang.ClassLoader.loadClass(ClassLoader.java:886) ~[?:1.8.0_462] {}
	at net.minecraft.util.registry.Registry.func_218353_F(Registry.java:159) ~[?:?] {re:classloading,re:mixin}
	at net.minecraft.util.registry.Registry.lambda$static$51(Registry.java:466) ~[?:?] {re:classloading,re:mixin}
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:1.8.0_462] {}
	at net.minecraft.util.registry.Registry.<clinit>(Registry.java:465) ~[?:?] {re:classloading,re:mixin}
	at net.minecraft.util.registry.Bootstrap.func_151354_b(Bootstrap.java:38) ~[?:?] {re:mixin,re:classloading,pl:mixin:APP:modernfix-common.mixins.json:feature.measure_time.BootstrapMixin,pl:mixin:APP:modernfix-forge.mixins.json:core.BootstrapMixin,pl:mixin:APP:modernfix-forge.mixins.json:core.BootstrapClientMixin,pl:mixin:A}
	at net.minecraft.client.main.Main.lambda$main$0(Main.java:123) ~[?:?] {re:classloading,pl:runtimedistcleaner:A}
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [?:1.8.0_462] {}
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) [?:1.8.0_462] {}
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_462] {}
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_462] {}
	at java.lang.Thread.run(Thread.java:822) [?:1.8.0_462] {}
Exception in thread "main" java.lang.reflect.InvocationTargetException
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:503)
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112)
	at oolloo.jlw.Wrapper.main(Wrapper.java:105)
Caused by: java.lang.RuntimeException: java.lang.reflect.InvocationTargetException
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:39)
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:54)
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:72)
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:82)
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:66)
	... 6 more
Caused by: java.lang.reflect.InvocationTargetException
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:503)
	at net.minecraftforge.fml.loading.FMLClientLaunchProvider.lambda$launchService$0(FMLClientLaunchProvider.java:37)
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:37)
	... 10 more
Caused by: java.lang.RuntimeException: org.spongepowered.asm.mixin.transformer.throwables.MixinTransformerError: An unexpected critical error was encountered
	at net.minecraftforge.fml.loading.BackgroundWaiter.runAndTick(BackgroundWaiter.java:29)
	at net.minecraft.client.main.Main.main(Main.java:123)
	... 16 more
Caused by: org.spongepowered.asm.mixin.transformer.throwables.MixinTransformerError: An unexpected critical error was encountered
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:392)
	at org.spongepowered.asm.mixin.transformer.MixinTransformer.transformClass(MixinTransformer.java:250)
	at org.spongepowered.asm.service.modlauncher.MixinTransformationHandler.processClass(MixinTransformationHandler.java:131)
	at org.spongepowered.asm.launch.MixinLaunchPluginLegacy.processClass(MixinLaunchPluginLegacy.java:131)
	at cpw.mods.modlauncher.serviceapi.ILaunchPluginService.processClassWithFlags(ILaunchPluginService.java:154)
	at cpw.mods.modlauncher.LaunchPluginHandler.offerClassNodeToPlugins(LaunchPluginHandler.java:85)
	at cpw.mods.modlauncher.ClassTransformer.transform(ClassTransformer.java:120)
	at cpw.mods.modlauncher.TransformingClassLoader$DelegatedClassLoader.findClass(TransformingClassLoader.java:265)
	at cpw.mods.modlauncher.TransformingClassLoader.loadClass(TransformingClassLoader.java:136)
	at cpw.mods.modlauncher.TransformingClassLoader.loadClass(TransformingClassLoader.java:98)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:886)
	at net.minecraft.util.registry.Registry.func_218353_F(Registry.java:159)
	at net.minecraft.util.registry.Registry.lambda$static$51(Registry.java:466)
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684)
	at net.minecraft.util.registry.Registry.<clinit>(Registry.java:465)
	at net.minecraft.util.registry.Bootstrap.func_151354_b(Bootstrap.java:38)
	at net.minecraft.client.main.Main.lambda$main$0(Main.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:822)
Caused by: org.spongepowered.asm.mixin.throwables.MixinApplyError: Mixin [mixins.rotp_ab.json:BlockItemMixin] from phase [DEFAULT] in config [mixins.rotp_ab.json] FAILED during APPLY
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.handleMixinError(MixinProcessor.java:636)
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.handleMixinApplyError(MixinProcessor.java:588)
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:379)
	... 21 more
Caused by: org.spongepowered.asm.mixin.injection.throwables.InvalidInjectionException: Critical injection failure: @Inject annotation on onBlockPlaced could not find any targets matching 'place' in net.minecraft.item.BlockItem. No refMap loaded. [PREINJECT Applicator Phase -> mixins.rotp_ab.json:BlockItemMixin -> Prepare Injections ->  -> handler$bea000$onBlockPlaced(Lnet/minecraft/item/BlockItemUseContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V -> Parse]
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.validateTargets(InjectionInfo.java:656)
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.findTargets(InjectionInfo.java:587)
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.readAnnotation(InjectionInfo.java:330)
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.<init>(InjectionInfo.java:316)
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.<init>(InjectionInfo.java:308)
	at org.spongepowered.asm.mixin.injection.struct.CallbackInjectionInfo.<init>(CallbackInjectionInfo.java:46)
	at sun.reflect.GeneratedConstructorAccessor24.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo$InjectorEntry.create(InjectionInfo.java:149)
	at org.spongepowered.asm.mixin.injection.struct.InjectionInfo.parse(InjectionInfo.java:708)
	at org.spongepowered.asm.mixin.transformer.MixinTargetContext.prepareInjections(MixinTargetContext.java:1311)
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.prepareInjections(MixinApplicatorStandard.java:1042)
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.applyMixin(MixinApplicatorStandard.java:393)
	at org.spongepowered.asm.mixin.transformer.MixinApplicatorStandard.apply(MixinApplicatorStandard.java:325)
	at org.spongepowered.asm.mixin.transformer.TargetClassContext.apply(TargetClassContext.java:383)
	at org.spongepowered.asm.mixin.transformer.TargetClassContext.applyMixins(TargetClassContext.java:365)
	at org.spongepowered.asm.mixin.transformer.MixinProcessor.applyMixins(MixinProcessor.java:363)
	... 21 more
[23:08:08.818] [Crash] 步骤 1：收集日志文件完成，收集到 3 个文件
[23:08:08.827] [Crash] 步骤 2：准备日志文本
[23:08:08.827] [Crash] latest.log 分类为 MinecraftLog
[23:08:08.827] [Crash] debug.log 分类为 MinecraftLog
[23:08:08.827] [Crash] rawoutput.log 分类为 MinecraftLog
[23:08:08.831] [Crash] 输出报告：D:\特摄\versions\1.16.5-Forge_36.2.42\logs\latest.log，作为 Minecraft 或启动器日志
[23:08:08.831] [Crash] 输出报告：D:\特摄\versions\1.16.5-Forge_36.2.42\logs\debug.log，作为 Minecraft 或启动器日志
[23:08:08.831] [Crash] 输出报告：C:\Users\<USER>\AppData\Local\Temp\PCL\TaskTemp\1115-327505\RawOutput.log，作为 Minecraft 或启动器日志
[23:08:08.832] [Crash] 导入分析：C:\Users\<USER>\AppData\Local\Temp\PCL\TaskTemp\1115-327505\RawOutput.log，作为启动器日志
[23:08:08.832] [Crash] 导入分析：D:\特摄\versions\1.16.5-Forge_36.2.42\logs\latest.log，作为 Minecraft 日志
[23:08:08.834] [Crash] 导入分析：D:\特摄\versions\1.16.5-Forge_36.2.42\logs\debug.log，作为 Minecraft Debug 日志
[23:08:08.834] [Crash] 步骤 2：准备日志文本完成，找到游戏日志、游戏 Debug 日志用作分析
[23:08:08.838] [Crash] 步骤 3：分析崩溃原因
[23:08:08.883] [Crash] Debug 信息中找到 51 个可能的 Mod 项目行
[23:08:08.883] [Crash] Debug 信息中找到 1 个可能的崩溃 Mod 匹配行
[23:08:08.883] [Crash]  - RotP-紫色隐者-1.3.jar with {rotp_zhp} mods - versions {1.3}
[23:08:08.883] [Crash] 找到 1 个可能的崩溃 Mod 文件名
[23:08:08.883] [Crash]  - RotP-紫色隐者-1.3.jar
[23:08:08.886] [Crash] 可能的崩溃原因：ModMixin失败（RotP-紫色隐者-1.3.jar）
[23:08:08.886] [Crash] 步骤 3：分析崩溃原因完成，找到 1 条可能的原因
[23:08:08.886] [Crash]  - ModMixin失败（RotP-紫色隐者-1.3.jar）
[23:08:08.895] [System] 窗口已置顶，位置：(403.333333333333, 231.333333333333), 900 x 556
[23:08:08.949] [Control] 普通弹窗：Minecraft 出现错误
名为 RotP-紫色隐者-1.3.jar 的 Mod 注入失败，导致游戏出错。
这一般代表着它与其他 Mod 或当前环境不兼容，或是它存在 Bug。
你可以尝试禁用此 Mod，然后观察游戏是否还会崩溃。

你可以查看错误报告了解错误具体是如何发生的。
如果要寻求帮助，请把错误报告文件发给对方，而不是发送这个窗口的照片或者截图。
[23:08:13.228] [Control] 按下按钮：确定
[23:08:13.229] [Control] 普通弹框返回：1
[23:10:56.703] [Control] 按下图标按钮：BtnTitleMin
[23:41:38.623] [Control] 按下按钮：版本选择
[23:41:38.624] [Control] 切换主要页面：VersionSelect, 0
[23:41:40.463] [Control] 按下单选列表项：ben10
[23:41:40.469] [Setup] 当前选择的 Minecraft 文件夹：D:\ben10\
[23:41:40.469] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[23:41:40.470] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[23:41:40.471] [Setup] 当前选择的 Minecraft 版本：MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK
[23:41:40.471] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\ben10\versions\MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK\
[23:41:40.475] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[23:41:40.599] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[23:41:40.932] [Control] 按下单选列表项：BEN 10
[23:41:40.932] [Setup] 当前选择的 Minecraft 文件夹：D:\BEN 10\
[23:41:40.932] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[23:41:40.933] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[23:41:40.935] 版本 JSON 可用性检查失败（D:\BEN 10\versions\Kevin_11-1.12.2-3.0\）：未找到版本 JSON 文件：D:\BEN 10\versions\Kevin_11-1.12.2-3.0\Kevin_11-1.12.2-3.0.json
   在 PCL.ModMinecraft.McVersion.DisableException()
   在 PCL.ModMinecraft.McVersion.AssetException()
   在 PCL.ModMinecraft.McVersion.Check()
[23:41:40.937] 版本 JSON 可用性检查失败（D:\BEN 10\versions\〖矛盾附属〗Random Ben 10 Stuff 1.0\）：未找到版本 JSON 文件：D:\BEN 10\versions\〖矛盾附属〗Random Ben 10 Stuff 1.0\〖矛盾附属〗Random Ben 10 Stuff 1.0.json
   在 PCL.ModMinecraft.McVersion.DisableException()
   在 PCL.ModMinecraft.McVersion.AssetException()
   在 PCL.ModMinecraft.McVersion.Check()
[23:41:40.937] [Setup] 当前选择的 Minecraft 版本：Ben10Craft
[23:41:40.937] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\BEN 10\versions\Ben10Craft\
[23:41:40.937] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[23:41:41.063] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[23:41:43.204] [Control] 按下图标按钮：BtnTitleMin
[23:55:32.915] [Control] 切换主要页面：Launch, -1
[23:55:32.915] [Control] 按下单击列表项：Ben10Craft
[23:55:33.045] [Minecraft] 启动按钮：Minecraft 版本：D:\BEN 10\versions\Ben10Craft\
[23:55:35.404] [Control] 按下按钮：启动游戏
[23:55:35.405] [Loader] 加载器 Loader Launch 状态改变：Loading
[23:55:35.408] [Launch] 预检测已通过
[23:55:35.409] [Download] 开始后台检查资源文件索引
[23:55:35.409] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[23:55:35.409] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[23:55:35.409] [Loader] 加载器 登录 状态改变：Waiting
[23:55:35.409] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[23:55:35.409] [Loader] 加载器 获取 Java 状态改变：Loading
[23:55:35.409] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[23:55:35.409] [Loader] 加载器 登录 状态改变：Loading
[23:55:35.409] [Loader] 加载器 补全文件 状态改变：Loading
[23:55:35.409] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[23:55:35.409] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[23:55:35.409] [Launch] 登录加载已开始
[23:55:35.409] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[23:55:35.409] [Launch] 登录方式：正版（Quasar2323）
[23:55:35.409] [Launch] 开始微软登录步骤 1/6（刷新登录）
[23:55:35.411] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[23:55:35.411] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[23:55:35.411] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[23:55:35.411] [Loader] 加载器 内存优化 状态改变：Loading
[23:55:35.411] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[23:55:35.411] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Finished
[23:55:35.411] [Launch] 内存优化开始
[23:55:35.411] [Taskbar] Minecraft 启动 已加入任务列表
[23:55:35.411] [Loader] 加载器 后台下载资源文件索引 状态改变：Loading
[23:55:35.412] [Test] 没有管理员权限，将以命令行方式进行内存优化
[23:55:35.420] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[23:55:35.420] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[23:55:35.421] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[23:55:35.421] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[23:55:35.434] [Download] 1.12.json 1374#：开始，起始点 0，https://piston-meta.mojang.com/v1/packages/a21e1ded1a24ea1548dd8db0cf30b6acb02655a9/1.12.json
[23:55:35.445] [Minecraft] 获取支持库列表：Ben10Craft
[23:55:35.445] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[23:55:35.445] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[23:55:35.445] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[23:55:35.456] [Launch] Java 版本需求：最低 1.8.0.0，最高 1.8.999.999
[23:55:35.456] [Java] 开始完全遍历查找：D:\BEN 10\
[23:55:35.489] [Java] 开始完全遍历查找：D:\BEN 10\versions\Ben10Craft\
[23:55:35.513] [Java] 排序后的 Java 优先顺序：
[23:55:35.513] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[23:55:35.513] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[23:55:35.513] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[23:55:35.513] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[23:55:35.513] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[23:55:35.513] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[23:55:35.513] [Java] 最终选定的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[23:55:35.513] [Launch] 选择的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[23:55:35.513] [Loader] 加载器 获取 Java 状态改变：Finished
[23:55:35.515] [Loader] 由于输入条件变更，重启进行中的加载器 登录
[23:55:35.515] [Launch] 登录加载已开始
[23:55:35.516] [Launch] 登录方式：正版（Quasar2323）
[23:55:35.516] [Launch] 开始微软登录步骤 1/6（刷新登录）
[23:55:35.517] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[23:55:35.651] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[23:55:35.651] [Loader] 加载器 下载资源文件 状态改变：Loading
[23:55:35.651] [Loader] 加载器 下载资源文件 状态改变：Finished
[23:55:35.651] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[23:55:35.705] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[23:55:35.705] [Loader] 加载器 下载支持库文件 状态改变：Loading
[23:55:35.705] [Loader] 加载器 下载支持库文件 状态改变：Finished
[23:55:35.705] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[23:55:35.705] [Loader] 加载器 补全文件 状态改变：Finished
[23:55:35.775] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[23:55:36.022] [Report] FPS 0, 动画 4, 下载中 1（0 B/s）
[23:55:36.309] [Download] 1.12.json 1374#：文件大小 143136（140 K）
[23:55:36.683] [Download] 1.12.json：已完成，剩余文件 0
[23:55:36.683] [Loader] 加载器 后台下载资源文件索引 状态改变：Finished
[23:55:36.684] [Loader] 加载器 后台复制资源文件索引 状态改变：Loading
[23:55:36.685] [Launch] 后台更新资源文件索引成功：C:\Users\<USER>\AppData\Local\Temp\PCL\Cache\1.12.json
[23:55:36.685] [Loader] 加载器 后台复制资源文件索引 状态改变：Finished
[23:55:36.685] [Loader] 加载器 后台更新资源文件索引 状态改变：Finished
[23:55:36.915] [Control] 按下图标按钮：BtnTitleMin
[23:55:37.054] [Launch] 开始微软登录步骤 2/6
[23:55:37.066] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[23:55:37.332] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[23:55:37.845] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[23:55:38.597] [Launch] 开始微软登录步骤 3/6
[23:55:38.597] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[23:55:38.856] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[23:55:39.363] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[23:55:40.120] [Launch] 开始微软登录步骤 4/6
[23:55:40.120] [Net] 发起网络请求（POST，https://api.minecraftservices.com/authentication/login_with_xbox），最大超时 15000
[23:55:41.196] [Launch] 开始微软登录步骤 5/6
[23:55:41.197] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[23:55:41.462] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[23:55:41.978] [Launch] 开始微软登录步骤 6/6
[23:55:41.978] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[23:55:42.241] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[23:55:42.754] [Launch] 微软登录完成
[23:55:42.754] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[23:55:42.767] [Launch] 登录加载已结束
[23:55:42.767] [Loader] 加载器 登录 状态改变：Finished
[23:55:42.772] [Loader] 由于输入条件变更，重启已完成的加载器 登录
[23:55:42.772] [Loader] 加载器 登录 状态改变：Loading
[23:55:42.772] [Launch] 登录加载已开始
[23:55:42.772] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[23:55:42.773] [Launch] 登录方式：正版（Quasar2323）
[23:55:42.773] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[23:55:42.798] [Launch] 登录加载已结束
[23:55:42.798] [Loader] 加载器 登录 状态改变：Finished
[23:55:48.368] [Test] 内存优化完成，可用内存改变量：2.5 G，大致剩余内存：4.96 G
[23:55:48.411] [Loader] 加载器 内存优化 状态改变：Finished
[23:55:48.411] [Loader] 加载器 获取启动参数 状态改变：Loading
[23:55:48.412] [Launch] 开始获取 Minecraft 启动参数
[23:55:48.413] [Launch] 获取旧版 JVM 参数
[23:55:48.422] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[23:55:48.422] [System] 获取资源：JavaWrapper
[23:55:48.424] [Launch] 旧版 JVM 参数获取成功：
[23:55:48.426] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp ${classpath} -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch
[23:55:48.426] [Launch] 获取旧版 Game 参数
[23:55:48.428] [Launch] 旧版 Game 参数获取成功
[23:55:48.428] [Minecraft] 获取支持库列表：Ben10Craft
[23:55:48.429] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[23:55:48.429] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[23:55:48.429] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[23:55:48.431] [Launch] Minecraft 启动参数：
[23:55:48.433] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp "D:\BEN 10\libraries\com\mojang\patchy\1.3.9\patchy-1.3.9.jar;D:\BEN 10\libraries\oshi-project\oshi-core\1.1\oshi-core-1.1.jar;D:\BEN 10\libraries\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;D:\BEN 10\libraries\net\java\dev\jna\platform\3.4.0\platform-3.4.0.jar;D:\BEN 10\libraries\com\ibm\icu\icu4j-core-mojang\51.2\icu4j-core-mojang-51.2.jar;D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar;D:\BEN 10\libraries\com\paulscode\codecjorbis\20101023\codecjorbis-20101023.jar;D:\BEN 10\libraries\com\paulscode\codecwav\20101023\codecwav-20101023.jar;D:\BEN 10\libraries\com\paulscode\libraryjavasound\20101123\libraryjavasound-20101123.jar;D:\BEN 10\libraries\com\paulscode\librarylwjglopenal\20100824\librarylwjglopenal-20100824.jar;D:\BEN 10\libraries\com\paulscode\soundsystem\20120107\soundsystem-20120107.jar;D:\BEN 10\libraries\io\netty\netty-all\4.1.9.Final\netty-all-4.1.9.Final.jar;D:\BEN 10\libraries\com\google\guava\guava\21.0\guava-21.0.jar;D:\BEN 10\libraries\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;D:\BEN 10\libraries\commons-io\commons-io\2.5\commons-io-2.5.jar;D:\BEN 10\libraries\commons-codec\commons-codec\1.10\commons-codec-1.10.jar;D:\BEN 10\libraries\net\java\jinput\jinput\2.0.5\jinput-2.0.5.jar;D:\BEN 10\libraries\net\java\jutils\jutils\1.0.0\jutils-1.0.0.jar;D:\BEN 10\libraries\com\google\code\gson\gson\2.8.0\gson-2.8.0.jar;D:\BEN 10\libraries\com\mojang\authlib\1.5.25\authlib-1.5.25.jar;D:\BEN 10\libraries\com\mojang\realms\1.10.22\realms-1.10.22.jar;D:\BEN 10\libraries\org\apache\commons\commons-compress\1.8.1\commons-compress-1.8.1.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpclient\4.3.3\httpclient-4.3.3.jar;D:\BEN 10\libraries\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpcore\4.3.2\httpcore-4.3.2.jar;D:\BEN 10\libraries\it\unimi\dsi\fastutil\7.1.0\fastutil-7.1.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl\2.9.4-nightly-20150209\lwjgl-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl_util\2.9.4-nightly-20150209\lwjgl_util-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\com\mojang\text2speech\1.10.3\text2speech-1.10.3.jar;D:\BEN 10\libraries\net\minecraftforge\forge\1.12.2-14.23.5.2860\forge-1.12.2-14.23.5.2860.jar;D:\BEN 10\libraries\org\ow2\asm\asm-debug-all\5.2\asm-debug-all-5.2.jar;D:\BEN 10\libraries\net\minecraft\launchwrapper\1.12\launchwrapper-1.12.jar;D:\BEN 10\libraries\org\jline\jline\3.5.1\jline-3.5.1.jar;D:\BEN 10\libraries\com\typesafe\akka\akka-actor_2.11\2.3.3\akka-actor_2.11-2.3.3.jar;D:\BEN 10\libraries\com\typesafe\config\1.2.1\config-1.2.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-actors-migration_2.11\1.1.0\scala-actors-migration_2.11-1.1.0.jar;D:\BEN 10\libraries\org\scala-lang\scala-compiler\2.11.1\scala-compiler-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-library_2.11\1.0.2_mc\scala-continuations-library_2.11-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-plugin_2.11.1\1.0.2_mc\scala-continuations-plugin_2.11.1-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\scala-library\2.11.1\scala-library-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-parser-combinators_2.11\1.0.1\scala-parser-combinators_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-reflect\2.11.1\scala-reflect-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-swing_2.11\1.0.1\scala-swing_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-xml_2.11\1.0.2\scala-xml_2.11-1.0.2.jar;D:\BEN 10\libraries\lzma\lzma\0.0.1\lzma-0.0.1.jar;D:\BEN 10\libraries\java3d\vecmath\1.5.2\vecmath-1.5.2.jar;D:\BEN 10\libraries\net\sf\trove4j\trove4j\3.0.3\trove4j-3.0.3.jar;D:\BEN 10\libraries\org\apache\maven\maven-artifact\3.5.3\maven-artifact-3.5.3.jar;D:\BEN 10\versions\Ben10Craft\Ben10Craft.jar" -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch --username Quasar2323 --version Ben10Craft --gameDir "D:\BEN 10\versions\Ben10Craft" --assetsDir "D:\BEN 10\assets" --assetIndex 1.12 --uuid 077e3afcb77643c48ae30f9dbb755bef --accessToken eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************KjSWg --userType msa --versionType PCL --tweakClass net.minecraftforge.fml.common.launcher.FMLTweaker --versionType Forge --height 480 --width 854 
[23:55:48.433] [Loader] 加载器 获取启动参数 状态改变：Finished
[23:55:48.446] [Loader] 加载器 解压文件 状态改变：Loading
[23:55:48.447] [Launch] 正在解压 Natives 文件
[23:55:48.449] [Loader] 加载器 解压文件 状态改变：Finished
[23:55:48.491] [Loader] 加载器 预启动处理 状态改变：Loading
[23:55:48.492] [System] 无需调整显卡设置：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[23:55:48.492] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[23:55:48.494] [Launch] 已更新 launcher_profiles.json
[23:55:48.495] [Launch] 需要的语言为 zh_cn，当前语言为 zh_cn，无需修改
[23:55:48.495] [Loader] 加载器 预启动处理 状态改变：Finished
[23:55:48.523] [Loader] 加载器 执行自定义命令 状态改变：Loading
[23:55:48.525] [Loader] 加载器 执行自定义命令 状态改变：Finished
[23:55:48.554] [Loader] 加载器 启动进程 状态改变：Loading
[23:55:48.717] [Launch] 已启动游戏进程：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[23:55:48.717] [Loader] 加载器 启动进程 状态改变：Finished
[23:55:48.717] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[23:55:48.717] [Launch] 
[23:55:48.718] [Launch] ~ 基础参数 ~
[23:55:48.718] [Launch] PCL 版本：Release 2.10.3 (361)
[23:55:48.718] [Launch] 游戏版本：1.12.2, Forge 14.23.5.2860（识别为 1.12.2）
[23:55:48.718] [Launch] 资源版本：1.12
[23:55:48.718] [Launch] 版本继承：无
[23:55:48.718] [Launch] 分配的内存：15 GB（15360 MB）
[23:55:48.718] [Launch] MC 文件夹：D:\BEN 10\
[23:55:48.718] [Launch] 版本文件夹：D:\BEN 10\versions\Ben10Craft\
[23:55:48.718] [Launch] 版本隔离：True
[23:55:48.718] [Launch] HMCL 格式：False
[23:55:48.718] [Launch] Java 信息：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[23:55:48.718] [Launch] 环境变量：未设置
[23:55:48.718] [Launch] Natives 文件夹：D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives
[23:55:48.718] [Launch] 
[23:55:48.718] [Launch] ~ 登录参数 ~
[23:55:48.718] [Launch] 玩家用户名：Quasar2323
[23:55:48.720] [Launch] AccessToken：eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************KjSWg
[23:55:48.720] [Launch] ClientToken：077e3afcb77643c48ae30f9dbb755bef
[23:55:48.720] [Launch] UUID：077e3afcb77643c48ae30f9dbb755bef
[23:55:48.720] [Launch] 登录方式：Microsoft
[23:55:48.720] [Launch] 
[23:55:48.720] [Launch] [3508] 开始 Minecraft 日志监控
[23:55:48.720] [Launch] [全局] 出现运行中的 Minecraft
[23:55:50.093] [Launch] [3508] 日志 1/5：已出现日志输出
[23:55:54.619] [Launch] [3508] 日志 2/5：游戏用户已设置
[23:55:57.566] [Launch] [3508] 日志 3/5：LWJGL 版本已确认
[23:55:57.806] [Launch] [3508] Minecraft 窗口已加载：（7604672）
[23:55:58.949] [Launch] [3508] Minecraft 加载已完成
[23:55:58.994] [Loader] 加载器 等待游戏窗口出现 状态改变：Finished
[23:55:58.999] [Loader] 加载器 结束处理 状态改变：Loading
[23:55:59.000] [Launch] 开始启动结束处理
[23:55:59.000] [Launch] 启动器可见性：5
[23:55:59.001] [Loader] 加载器 结束处理 状态改变：Finished
[23:55:59.065] [Loader] 加载器 Minecraft 启动 状态改变：Finished
[23:55:59.065] [Taskbar] Minecraft 启动 已移出任务列表
[23:55:59.079] 加载线程 结束处理 (28) 出错，已完成 100%：未将对象引用设置到对象的实例。
   在 PCL.ModNet.HasDownloadingTask(Boolean IgnoreCustomDownload)
   在 PCL.FormMain.BtnExtraDownload_ShowCheck()
   在 PCL.MyExtraButton.ShowRefresh()
   在 PCL.ModLoader.LoaderCombo`1.Update()
   在 PCL.ModLoader.LoaderCombo`1.SubTaskStateChanged(LoaderBase Loader, LoadState NewState, LoadState OldState)
   在 PCL.ModLoader.LoaderBase._Closure$__32-0._Lambda$__1()
   在 PCL.ModBase.RunInThread(Action Action)
   在 PCL.ModLoader.LoaderBase.set_State(LoadState value)
   在 PCL.ModLoader.LoaderTask`2._Closure$__13-0._Lambda$__0()
   错误类型：System.NullReferenceException
[23:55:59.079] [Loader] 加载器 结束处理 状态改变：Failed
[23:55:59.149] [Loader] 加载器 Loader Launch 状态改变：Finished
[23:55:59.183] [UI] 弹出提示：Ben10Craft 启动成功！
[23:56:36.951] [Launch] [3508] 日志 4/5：OpenAL 已加载
[23:57:19.143] [Launch] [3508] 日志 5/5：材质已加载
[00:00:33.054] [Launch] [3508] Minecraft 已退出，返回值：0
[00:00:33.054] [Launch] [全局] 已无运行中的 Minecraft
[00:00:33.286] [Launch] [3508] Minecraft 日志监控已退出
[00:11:01.539] [Animation] 两个动画帧间隔 656 ms
[00:13:07.785] [Animation] 两个动画帧间隔 1593 ms
[00:19:13.687] [Control] 按下按钮：版本选择
[00:19:13.688] [Control] 切换主要页面：VersionSelect, 0
[00:19:15.861] [Control] 按下单选列表项：modding
[00:19:15.862] [Setup] 当前选择的 Minecraft 文件夹：C:\Users\<USER>\Desktop\.minecraft\
[00:19:15.862] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[00:19:15.863] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[00:19:15.865] [Setup] 当前选择的 Minecraft 版本：1.21.1-Fabric 0.16.14
[00:19:15.865] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\Desktop\.minecraft\versions\1.21.1-Fabric 0.16.14\
[00:19:15.870] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[00:19:16.030] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[00:19:26.962] [Control] 按下单选列表项：官方启动器文件夹
[00:19:26.963] [Setup] 当前选择的 Minecraft 文件夹：C:\Users\<USER>\AppData\Roaming\.minecraft\
[00:19:26.963] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[00:19:26.964] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[00:19:26.967] [Setup] 当前选择的 Minecraft 版本：[幽]ANZNB火影忍者懒人包
[00:19:26.968] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\AppData\Roaming\.minecraft\versions\[幽]ANZNB火影忍者懒人包\
[00:19:26.968] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[00:19:27.087] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[00:19:28.853] [Control] 按下图标按钮
[00:19:28.853] [Control] 切换主要页面：VersionSetup, 0
[00:19:31.173] [Control] 按下图标按钮：BtnTitleInner
[00:19:31.173] [Control] 切换主要页面：VersionSelect, -1
[00:19:40.963] [Control] 按下单选列表项：BEN 10
[00:19:40.964] [Setup] 当前选择的 Minecraft 文件夹：D:\BEN 10\
[00:19:40.964] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[00:19:40.965] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[00:19:40.971] 版本 JSON 可用性检查失败（D:\BEN 10\versions\Kevin_11-1.12.2-3.0\）：未找到版本 JSON 文件：D:\BEN 10\versions\Kevin_11-1.12.2-3.0\Kevin_11-1.12.2-3.0.json
   在 PCL.ModMinecraft.McVersion.DisableException()
   在 PCL.ModMinecraft.McVersion.AssetException()
   在 PCL.ModMinecraft.McVersion.Check()
[00:19:40.971] 版本 JSON 可用性检查失败（D:\BEN 10\versions\〖矛盾附属〗Random Ben 10 Stuff 1.0\）：未找到版本 JSON 文件：D:\BEN 10\versions\〖矛盾附属〗Random Ben 10 Stuff 1.0\〖矛盾附属〗Random Ben 10 Stuff 1.0.json
   在 PCL.ModMinecraft.McVersion.DisableException()
   在 PCL.ModMinecraft.McVersion.AssetException()
   在 PCL.ModMinecraft.McVersion.Check()
[00:19:40.972] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[00:19:40.972] [Setup] 当前选择的 Minecraft 版本：Ben10Craft
[00:19:40.972] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\BEN 10\versions\Ben10Craft\
[00:19:41.094] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[00:19:41.893] [Control] 切换主要页面：Launch, -1
[00:19:41.893] [Control] 按下单击列表项：Ben10Craft
[00:19:43.061] [Control] 按下按钮：版本选择
[00:19:43.062] [Control] 切换主要页面：VersionSelect, 0
[00:19:43.751] [Control] 切换主要页面：VersionSetup, 0
[00:19:44.718] [Control] 按下单选列表项：Mod 管理
[00:19:44.892] [Loader] 加载器 Mod List Loader 状态改变：Loading
[00:19:44.892] [System] 已刷新 Mod 列表
[00:19:45.063] [Report] FPS 0, 动画 8, 下载中 0（0 B/s）
[00:19:45.553] [Mod] 共有 63 个 Mod，其中 56 个需要联网获取信息，7 个需要更新信息
[00:19:45.555] [Loader] 加载器 Mod List Detail Loader 状态改变：Loading
[00:19:45.555] [Mod] 目标加载器：Forge，版本：1.12.2
[00:19:45.555] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files），最大超时 5000
[00:19:45.702] [Loader] 加载器 Mod List Loader 状态改变：Finished
[00:19:45.905] [Animation] 两个动画帧间隔 219 ms
[00:19:45.949] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/fingerprints/432），最大超时 5000
[00:19:46.818] [Mod] 从 Modrinth 获取到 21 个本地 Mod 的对应信息
[00:19:46.836] [Mod] 需要从 Modrinth 获取 21 个本地 Mod 的工程信息
[00:19:46.837] [Net] 发起网络请求（GET，https://api.modrinth.com/v2/projects?ids=["G1ckZuWK","EsAfCjCV","1jDdpgcc","Wnxd13zP","jupr7Bf5","8BmcQJ2H","BgGyndL5","PWERr14M","GcowSBDA","KP2WhfaM","KDvYkUg3","Z4Z5ccuT","trhPSzT0","bApyjH1r","rlloIFEV","u6dRKJwZ","rxIIYO6c","jL4QSgdv","Cg6Uc79H","ZnmdXAk2","cudtvDnd"]），最大超时 5000
[00:19:46.974] [Control] 按下按钮：打开文件夹
[00:19:46.974] [System] 正在打开资源管理器：D:\BEN 10\versions\Ben10Craft\mods\
[00:19:46.974] [System] 执行外部命令：D:\BEN 10\versions\Ben10Craft\mods\ 
[00:19:47.597] [Mod] 已从 Modrinth 获取本地 Mod 信息，继续获取更新信息
[00:19:47.597] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files/update），最大超时 5000
[00:19:47.812] [Mod] 从 CurseForge 获取到 59 个本地 Mod 的对应信息
[00:19:47.849] [Mod] 需要从 CurseForge 获取 59 个本地 Mod 的工程信息
[00:19:47.849] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/mods），最大超时 5000
[00:19:48.188] [Mod] 从 Modrinth 获取本地 Mod 信息结束
[00:19:48.701] [Mod] 已从 CurseForge 获取本地 Mod 信息，需要获取 38 个用于检查更新的文件信息
[00:19:48.701] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/mods/files），最大超时 5000
[00:19:49.218] [Mod] 从 CurseForge 获取 Mod 更新信息结束
[00:19:49.223] [Mod] 联网获取本地 Mod 信息完成，为 58 个 Mod 更新缓存
[00:19:49.233] [Loader] 加载器 Mod List Detail Loader 状态改变：Finished
[00:34:49.785] [Control] 按下图标按钮：BtnTitleInner
[00:34:49.786] [Control] 切换主要页面：VersionSelect, -1
[00:34:50.970] [Control] 切换主要页面：Launch, -1
[00:34:50.970] [Control] 按下单击列表项：Ben10Craft
[00:34:52.679] [Control] 按下按钮：启动游戏
[00:34:52.679] [Loader] 加载器 Loader Launch 状态改变：Loading
[00:34:52.686] [Launch] 预检测已通过
[00:34:52.686] [Download] 开始后台检查资源文件索引
[00:34:52.686] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[00:34:52.686] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[00:34:52.686] [Loader] 加载器 登录 状态改变：Waiting
[00:34:52.686] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[00:34:52.686] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[00:34:52.686] [Loader] 加载器 获取 Java 状态改变：Loading
[00:34:52.687] [Loader] 加载器 登录 状态改变：Loading
[00:34:52.687] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Finished
[00:34:52.687] [Loader] 加载器 补全文件 状态改变：Loading
[00:34:52.687] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[00:34:52.687] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[00:34:52.687] [Launch] 登录加载已开始
[00:34:52.687] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[00:34:52.687] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[00:34:52.687] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[00:34:52.687] [Launch] 登录方式：正版（Quasar2323）
[00:34:52.687] [Launch] 开始微软登录步骤 1/6（刷新登录）
[00:34:52.687] [Loader] 加载器 后台下载资源文件索引 状态改变：Loading
[00:34:52.687] [Loader] 加载器 内存优化 状态改变：Loading
[00:34:52.687] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[00:34:52.687] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[00:34:52.687] [Taskbar] Minecraft 启动 已加入任务列表
[00:34:52.687] [Launch] 内存优化开始
[00:34:52.687] [Test] 没有管理员权限，将以命令行方式进行内存优化
[00:34:52.689] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[00:34:52.690] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[00:34:52.690] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[00:34:52.690] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[00:34:52.706] [Download] 1.12.json 2657#：开始，起始点 0，https://piston-meta.mojang.com/v1/packages/a21e1ded1a24ea1548dd8db0cf30b6acb02655a9/1.12.json
[00:34:52.715] [Minecraft] 获取支持库列表：Ben10Craft
[00:34:52.716] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[00:34:52.716] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[00:34:52.716] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[00:34:52.719] [Launch] Java 版本需求：最低 1.8.0.0，最高 1.8.999.999
[00:34:52.719] [Java] 开始完全遍历查找：D:\BEN 10\
[00:34:52.729] [Java] 开始完全遍历查找：D:\BEN 10\versions\Ben10Craft\
[00:34:52.729] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[00:34:52.729] [Loader] 加载器 下载资源文件 状态改变：Loading
[00:34:52.729] [Loader] 加载器 下载资源文件 状态改变：Finished
[00:34:52.729] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[00:34:52.734] [Java] 排序后的 Java 优先顺序：
[00:34:52.734] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[00:34:52.734] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[00:34:52.734] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[00:34:52.734] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[00:34:52.734] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[00:34:52.734] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[00:34:52.734] [Java] 最终选定的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[00:34:52.734] [Launch] 选择的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[00:34:52.734] [Loader] 加载器 获取 Java 状态改变：Finished
[00:34:52.736] [Loader] 由于输入条件变更，重启进行中的加载器 登录
[00:34:52.737] [Launch] 登录加载已开始
[00:34:52.738] [Launch] 登录方式：正版（Quasar2323）
[00:34:52.738] [Launch] 开始微软登录步骤 1/6（刷新登录）
[00:34:52.738] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[00:34:52.925] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[00:34:52.926] [Loader] 加载器 下载支持库文件 状态改变：Loading
[00:34:52.926] [Loader] 加载器 下载支持库文件 状态改变：Finished
[00:34:52.926] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[00:34:52.926] [Loader] 加载器 补全文件 状态改变：Finished
[00:34:52.997] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[00:34:53.627] [Download] 1.12.json 2657#：文件大小 143136（140 K）
[00:34:54.007] [Download] 1.12.json：已完成，剩余文件 0
[00:34:54.007] [Loader] 加载器 后台下载资源文件索引 状态改变：Finished
[00:34:54.008] [Loader] 加载器 后台复制资源文件索引 状态改变：Loading
[00:34:54.008] [Launch] 后台更新资源文件索引成功：C:\Users\<USER>\AppData\Local\Temp\PCL\Cache\1.12.json
[00:34:54.008] [Loader] 加载器 后台复制资源文件索引 状态改变：Finished
[00:34:54.009] [Loader] 加载器 后台更新资源文件索引 状态改变：Finished
[00:34:54.249] [Launch] 开始微软登录步骤 2/6
[00:34:54.249] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[00:34:54.514] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[00:34:55.027] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[00:34:55.784] [Launch] 开始微软登录步骤 3/6
[00:34:55.785] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[00:34:56.047] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[00:34:56.555] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[00:34:57.346] [Launch] 开始微软登录步骤 4/6
[00:34:57.346] [Net] 发起网络请求（POST，https://api.minecraftservices.com/authentication/login_with_xbox），最大超时 15000
[00:34:58.545] [Launch] 开始微软登录步骤 5/6
[00:34:58.545] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[00:34:58.798] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[00:34:59.309] [Launch] 开始微软登录步骤 6/6
[00:34:59.309] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[00:34:59.570] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[00:35:00.084] [Launch] 微软登录完成
[00:35:00.084] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[00:35:00.098] [Launch] 登录加载已结束
[00:35:00.098] [Loader] 加载器 登录 状态改变：Finished
[00:35:00.100] [Loader] 由于输入条件变更，重启已完成的加载器 登录
[00:35:00.101] [Loader] 加载器 登录 状态改变：Loading
[00:35:00.102] [Launch] 登录加载已开始
[00:35:00.102] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[00:35:00.103] [Launch] 登录方式：正版（Quasar2323）
[00:35:00.103] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[00:35:00.128] [Launch] 登录加载已结束
[00:35:00.128] [Loader] 加载器 登录 状态改变：Finished
[00:35:04.790] [Test] 内存优化完成，可用内存改变量：2.6 G，大致剩余内存：4.89 G
[00:35:04.815] [Loader] 加载器 内存优化 状态改变：Finished
[00:35:04.816] [Loader] 加载器 获取启动参数 状态改变：Loading
[00:35:04.817] [Launch] 开始获取 Minecraft 启动参数
[00:35:04.817] [Launch] 获取旧版 JVM 参数
[00:35:04.818] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[00:35:04.818] [System] 获取资源：JavaWrapper
[00:35:04.820] [Launch] 旧版 JVM 参数获取成功：
[00:35:04.821] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp ${classpath} -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch
[00:35:04.821] [Launch] 获取旧版 Game 参数
[00:35:04.821] [Launch] 旧版 Game 参数获取成功
[00:35:04.821] [Minecraft] 获取支持库列表：Ben10Craft
[00:35:04.821] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[00:35:04.821] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[00:35:04.821] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[00:35:04.822] [Launch] Minecraft 启动参数：
[00:35:04.823] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp "D:\BEN 10\libraries\com\mojang\patchy\1.3.9\patchy-1.3.9.jar;D:\BEN 10\libraries\oshi-project\oshi-core\1.1\oshi-core-1.1.jar;D:\BEN 10\libraries\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;D:\BEN 10\libraries\net\java\dev\jna\platform\3.4.0\platform-3.4.0.jar;D:\BEN 10\libraries\com\ibm\icu\icu4j-core-mojang\51.2\icu4j-core-mojang-51.2.jar;D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar;D:\BEN 10\libraries\com\paulscode\codecjorbis\20101023\codecjorbis-20101023.jar;D:\BEN 10\libraries\com\paulscode\codecwav\20101023\codecwav-20101023.jar;D:\BEN 10\libraries\com\paulscode\libraryjavasound\20101123\libraryjavasound-20101123.jar;D:\BEN 10\libraries\com\paulscode\librarylwjglopenal\20100824\librarylwjglopenal-20100824.jar;D:\BEN 10\libraries\com\paulscode\soundsystem\20120107\soundsystem-20120107.jar;D:\BEN 10\libraries\io\netty\netty-all\4.1.9.Final\netty-all-4.1.9.Final.jar;D:\BEN 10\libraries\com\google\guava\guava\21.0\guava-21.0.jar;D:\BEN 10\libraries\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;D:\BEN 10\libraries\commons-io\commons-io\2.5\commons-io-2.5.jar;D:\BEN 10\libraries\commons-codec\commons-codec\1.10\commons-codec-1.10.jar;D:\BEN 10\libraries\net\java\jinput\jinput\2.0.5\jinput-2.0.5.jar;D:\BEN 10\libraries\net\java\jutils\jutils\1.0.0\jutils-1.0.0.jar;D:\BEN 10\libraries\com\google\code\gson\gson\2.8.0\gson-2.8.0.jar;D:\BEN 10\libraries\com\mojang\authlib\1.5.25\authlib-1.5.25.jar;D:\BEN 10\libraries\com\mojang\realms\1.10.22\realms-1.10.22.jar;D:\BEN 10\libraries\org\apache\commons\commons-compress\1.8.1\commons-compress-1.8.1.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpclient\4.3.3\httpclient-4.3.3.jar;D:\BEN 10\libraries\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpcore\4.3.2\httpcore-4.3.2.jar;D:\BEN 10\libraries\it\unimi\dsi\fastutil\7.1.0\fastutil-7.1.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl\2.9.4-nightly-20150209\lwjgl-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl_util\2.9.4-nightly-20150209\lwjgl_util-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\com\mojang\text2speech\1.10.3\text2speech-1.10.3.jar;D:\BEN 10\libraries\net\minecraftforge\forge\1.12.2-14.23.5.2860\forge-1.12.2-14.23.5.2860.jar;D:\BEN 10\libraries\org\ow2\asm\asm-debug-all\5.2\asm-debug-all-5.2.jar;D:\BEN 10\libraries\net\minecraft\launchwrapper\1.12\launchwrapper-1.12.jar;D:\BEN 10\libraries\org\jline\jline\3.5.1\jline-3.5.1.jar;D:\BEN 10\libraries\com\typesafe\akka\akka-actor_2.11\2.3.3\akka-actor_2.11-2.3.3.jar;D:\BEN 10\libraries\com\typesafe\config\1.2.1\config-1.2.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-actors-migration_2.11\1.1.0\scala-actors-migration_2.11-1.1.0.jar;D:\BEN 10\libraries\org\scala-lang\scala-compiler\2.11.1\scala-compiler-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-library_2.11\1.0.2_mc\scala-continuations-library_2.11-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-plugin_2.11.1\1.0.2_mc\scala-continuations-plugin_2.11.1-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\scala-library\2.11.1\scala-library-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-parser-combinators_2.11\1.0.1\scala-parser-combinators_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-reflect\2.11.1\scala-reflect-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-swing_2.11\1.0.1\scala-swing_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-xml_2.11\1.0.2\scala-xml_2.11-1.0.2.jar;D:\BEN 10\libraries\lzma\lzma\0.0.1\lzma-0.0.1.jar;D:\BEN 10\libraries\java3d\vecmath\1.5.2\vecmath-1.5.2.jar;D:\BEN 10\libraries\net\sf\trove4j\trove4j\3.0.3\trove4j-3.0.3.jar;D:\BEN 10\libraries\org\apache\maven\maven-artifact\3.5.3\maven-artifact-3.5.3.jar;D:\BEN 10\versions\Ben10Craft\Ben10Craft.jar" -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch --username Quasar2323 --version Ben10Craft --gameDir "D:\BEN 10\versions\Ben10Craft" --assetsDir "D:\BEN 10\assets" --assetIndex 1.12 --uuid 077e3afcb77643c48ae30f9dbb755bef --accessToken eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************0WmRQ --userType msa --versionType PCL --tweakClass net.minecraftforge.fml.common.launcher.FMLTweaker --versionType Forge --height 480 --width 854 
[00:35:04.823] [Loader] 加载器 获取启动参数 状态改变：Finished
[00:35:04.828] [Loader] 加载器 解压文件 状态改变：Loading
[00:35:04.828] [Launch] 正在解压 Natives 文件
[00:35:04.830] [Loader] 加载器 解压文件 状态改变：Finished
[00:35:04.845] [Loader] 加载器 预启动处理 状态改变：Loading
[00:35:04.846] [System] 无需调整显卡设置：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[00:35:04.846] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[00:35:04.848] [Launch] 已更新 launcher_profiles.json
[00:35:04.848] [Launch] 需要的语言为 zh_cn，当前语言为 zh_cn，无需修改
[00:35:04.848] [Loader] 加载器 预启动处理 状态改变：Finished
[00:35:04.874] [Loader] 加载器 执行自定义命令 状态改变：Loading
[00:35:04.876] [Loader] 加载器 执行自定义命令 状态改变：Finished
[00:35:04.897] [Loader] 加载器 启动进程 状态改变：Loading
[00:35:05.061] [Launch] 已启动游戏进程：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[00:35:05.061] [Loader] 加载器 启动进程 状态改变：Finished
[00:35:05.089] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[00:35:05.090] [Launch] 
[00:35:05.091] [Launch] ~ 基础参数 ~
[00:35:05.091] [Launch] PCL 版本：Release 2.10.3 (361)
[00:35:05.091] [Launch] 游戏版本：1.12.2, Forge 14.23.5.2860（识别为 1.12.2）
[00:35:05.091] [Launch] 资源版本：1.12
[00:35:05.091] [Launch] 版本继承：无
[00:35:05.091] [Launch] 分配的内存：15 GB（15360 MB）
[00:35:05.091] [Launch] MC 文件夹：D:\BEN 10\
[00:35:05.091] [Launch] 版本文件夹：D:\BEN 10\versions\Ben10Craft\
[00:35:05.091] [Launch] 版本隔离：True
[00:35:05.091] [Launch] HMCL 格式：False
[00:35:05.091] [Launch] Java 信息：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[00:35:05.091] [Launch] 环境变量：未设置
[00:35:05.091] [Launch] Natives 文件夹：D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives
[00:35:05.091] [Launch] 
[00:35:05.091] [Launch] ~ 登录参数 ~
[00:35:05.091] [Launch] 玩家用户名：Quasar2323
[00:35:05.091] [Launch] AccessToken：eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************0WmRQ
[00:35:05.091] [Launch] ClientToken：077e3afcb77643c48ae30f9dbb755bef
[00:35:05.091] [Launch] UUID：077e3afcb77643c48ae30f9dbb755bef
[00:35:05.091] [Launch] 登录方式：Microsoft
[00:35:05.091] [Launch] 
[00:35:05.093] [Launch] [21508] 开始 Minecraft 日志监控
[00:35:05.093] [Launch] [全局] 出现运行中的 Minecraft
[00:35:06.499] [Launch] [21508] 日志 1/5：已出现日志输出
[00:35:10.417] [Launch] [21508] 日志 2/5：游戏用户已设置
[00:35:13.282] [Launch] [21508] Minecraft 窗口已加载：（2690552）
[00:35:13.282] [Launch] [21508] 日志 3/5：LWJGL 版本已确认
[00:35:13.282] [Launch] [21508] Minecraft 加载已完成
[00:35:13.330] [Loader] 加载器 等待游戏窗口出现 状态改变：Finished
[00:35:13.331] [Loader] 加载器 结束处理 状态改变：Loading
[00:35:13.332] [Launch] 开始启动结束处理
[00:35:13.332] [Launch] 启动器可见性：5
[00:35:13.333] [Loader] 加载器 结束处理 状态改变：Finished
[00:35:13.364] [Loader] 加载器 Minecraft 启动 状态改变：Finished
[00:35:13.377] [Taskbar] Minecraft 启动 已移出任务列表
[00:35:13.456] [Loader] 加载器 Loader Launch 状态改变：Finished
[00:35:13.504] [UI] 弹出提示：Ben10Craft 启动成功！
[00:35:47.536] [Launch] [21508] 日志 4/5：OpenAL 已加载
[00:36:22.370] [Launch] [21508] 日志 5/5：材质已加载
[00:41:00.972] [Animation] 两个动画帧间隔 734 ms
[01:48:16.641] [System] 系统时间修改为：2025年8月2日 1:48:16
[01:48:16.648] [System] 系统时间修改为：2025年8月2日 1:48:16
[17:11:35.833] [Animation] 两个动画帧间隔 234 ms
[17:11:35.842] [Animation] 两个动画帧间隔 281 ms
[17:11:51.351] [Launch] [21508] Minecraft 已退出，返回值：0
[17:11:51.352] [Launch] [全局] 已无运行中的 Minecraft
[17:11:51.586] [Launch] [21508] Minecraft 日志监控已退出
[17:49:53.369] [Control] 按下按钮：启动游戏
[17:49:53.372] [Loader] 加载器 Loader Launch 状态改变：Loading
[17:49:53.383] [Launch] 预检测已通过
[17:49:53.384] [Download] 开始后台检查资源文件索引
[17:49:53.384] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[17:49:53.384] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[17:49:53.385] [Loader] 加载器 登录 状态改变：Waiting
[17:49:53.385] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[17:49:53.385] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[17:49:53.385] [Loader] 加载器 获取 Java 状态改变：Loading
[17:49:53.385] [Launch] Java 版本需求：最低 1.8.0.0，最高 1.8.999.999
[17:49:53.385] [Java] 开始完全遍历查找：D:\BEN 10\
[17:49:53.385] [Loader] 加载器 登录 状态改变：Loading
[17:49:53.386] [Loader] 加载器 补全文件 状态改变：Loading
[17:49:53.386] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[17:49:53.386] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[17:49:53.386] [Launch] 登录加载已开始
[17:49:53.386] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[17:49:53.386] [Download] 无需更新资源文件索引，取消
[17:49:53.386] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[17:49:53.386] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[17:49:53.386] [Launch] 登录方式：正版（Quasar2323）
[17:49:53.386] [Launch] 开始微软登录步骤 1/6（刷新登录）
[17:49:53.386] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[17:49:53.387] [Loader] 加载器 内存优化 状态改变：Loading
[17:49:53.387] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[17:49:53.387] [Taskbar] Minecraft 启动 已加入任务列表
[17:49:53.387] [Launch] 内存优化开始
[17:49:53.388] [Test] 没有管理员权限，将以命令行方式进行内存优化
[17:49:53.389] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Aborted
[17:49:53.390] [Loader] 加载器 后台更新资源文件索引 状态改变：Aborted
[17:49:53.392] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[17:49:53.392] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[17:49:53.392] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[17:49:53.392] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[17:49:53.393] [Loader] 加载器 后台下载资源文件索引 状态改变：Aborted
[17:49:53.393] [Download] 后台下载资源文件索引 已取消！
[17:49:53.395] [Loader] 加载线程 后台分析资源文件索引地址 (41) 已中断但线程正常运行至结束，输出被弃用（最新线程：-1）
[17:49:53.421] [Java] 开始完全遍历查找：D:\BEN 10\versions\Ben10Craft\
[17:49:53.428] [Minecraft] 获取支持库列表：Ben10Craft
[17:49:53.429] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[17:49:53.429] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[17:49:53.429] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[17:49:53.442] [Java] 排序后的 Java 优先顺序：
[17:49:53.442] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[17:49:53.442] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[17:49:53.442] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[17:49:53.442] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[17:49:53.442] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[17:49:53.442] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[17:49:53.442] [Java] 最终选定的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[17:49:53.442] [Launch] 选择的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[17:49:53.442] [Loader] 加载器 获取 Java 状态改变：Finished
[17:49:53.443] [Loader] 由于输入条件变更，重启进行中的加载器 登录
[17:49:53.447] [Launch] 登录加载已开始
[17:49:53.448] [Launch] 登录方式：正版（Quasar2323）
[17:49:53.448] [Launch] 开始微软登录步骤 1/6（刷新登录）
[17:49:53.448] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[17:49:53.638] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[17:49:53.638] [Loader] 加载器 下载资源文件 状态改变：Loading
[17:49:53.639] [Loader] 加载器 下载资源文件 状态改变：Finished
[17:49:53.639] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[17:49:53.702] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[17:49:53.711] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[17:49:53.711] [Loader] 加载器 下载支持库文件 状态改变：Loading
[17:49:53.711] [Loader] 加载器 下载支持库文件 状态改变：Finished
[17:49:53.711] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[17:49:53.711] [Loader] 加载器 补全文件 状态改变：Finished
[17:49:54.619] [Launch] 开始微软登录步骤 2/6
[17:49:54.619] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[17:49:54.882] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[17:49:55.390] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[17:49:56.149] [Launch] 开始微软登录步骤 3/6
[17:49:56.149] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[17:49:56.413] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[17:49:56.926] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[17:49:57.689] [Launch] 开始微软登录步骤 4/6
[17:49:57.689] [Net] 发起网络请求（POST，https://api.minecraftservices.com/authentication/login_with_xbox），最大超时 15000
[17:49:58.973] [Launch] 开始微软登录步骤 5/6
[17:49:58.973] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[17:49:59.236] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[17:49:59.812] [Launch] 开始微软登录步骤 6/6
[17:49:59.813] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[17:50:00.076] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[17:50:00.592] [Launch] 微软登录完成
[17:50:00.592] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[17:50:00.605] [Launch] 登录加载已结束
[17:50:00.605] [Loader] 加载器 登录 状态改变：Finished
[17:50:00.608] [Loader] 由于输入条件变更，重启已完成的加载器 登录
[17:50:00.608] [Loader] 加载器 登录 状态改变：Loading
[17:50:00.609] [Launch] 登录加载已开始
[17:50:00.609] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[17:50:00.609] [Launch] 登录方式：正版（Quasar2323）
[17:50:00.609] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[17:50:00.620] [Launch] 登录加载已结束
[17:50:00.620] [Loader] 加载器 登录 状态改变：Finished
[17:50:06.861] [Test] 内存优化完成，可用内存改变量：2.49 G，大致剩余内存：4.37 G
[17:50:06.863] [Loader] 加载器 内存优化 状态改变：Finished
[17:50:06.863] [Loader] 加载器 获取启动参数 状态改变：Loading
[17:50:06.864] [Launch] 开始获取 Minecraft 启动参数
[17:50:06.864] [Launch] 获取旧版 JVM 参数
[17:50:06.865] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[17:50:06.865] [System] 获取资源：JavaWrapper
[17:50:06.866] [Launch] 旧版 JVM 参数获取成功：
[17:50:06.867] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp ${classpath} -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch
[17:50:06.867] [Launch] 获取旧版 Game 参数
[17:50:06.867] [Launch] 旧版 Game 参数获取成功
[17:50:06.867] [Minecraft] 获取支持库列表：Ben10Craft
[17:50:06.867] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[17:50:06.867] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[17:50:06.867] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[17:50:06.868] [Launch] Minecraft 启动参数：
[17:50:06.870] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp "D:\BEN 10\libraries\com\mojang\patchy\1.3.9\patchy-1.3.9.jar;D:\BEN 10\libraries\oshi-project\oshi-core\1.1\oshi-core-1.1.jar;D:\BEN 10\libraries\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;D:\BEN 10\libraries\net\java\dev\jna\platform\3.4.0\platform-3.4.0.jar;D:\BEN 10\libraries\com\ibm\icu\icu4j-core-mojang\51.2\icu4j-core-mojang-51.2.jar;D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar;D:\BEN 10\libraries\com\paulscode\codecjorbis\20101023\codecjorbis-20101023.jar;D:\BEN 10\libraries\com\paulscode\codecwav\20101023\codecwav-20101023.jar;D:\BEN 10\libraries\com\paulscode\libraryjavasound\20101123\libraryjavasound-20101123.jar;D:\BEN 10\libraries\com\paulscode\librarylwjglopenal\20100824\librarylwjglopenal-20100824.jar;D:\BEN 10\libraries\com\paulscode\soundsystem\20120107\soundsystem-20120107.jar;D:\BEN 10\libraries\io\netty\netty-all\4.1.9.Final\netty-all-4.1.9.Final.jar;D:\BEN 10\libraries\com\google\guava\guava\21.0\guava-21.0.jar;D:\BEN 10\libraries\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;D:\BEN 10\libraries\commons-io\commons-io\2.5\commons-io-2.5.jar;D:\BEN 10\libraries\commons-codec\commons-codec\1.10\commons-codec-1.10.jar;D:\BEN 10\libraries\net\java\jinput\jinput\2.0.5\jinput-2.0.5.jar;D:\BEN 10\libraries\net\java\jutils\jutils\1.0.0\jutils-1.0.0.jar;D:\BEN 10\libraries\com\google\code\gson\gson\2.8.0\gson-2.8.0.jar;D:\BEN 10\libraries\com\mojang\authlib\1.5.25\authlib-1.5.25.jar;D:\BEN 10\libraries\com\mojang\realms\1.10.22\realms-1.10.22.jar;D:\BEN 10\libraries\org\apache\commons\commons-compress\1.8.1\commons-compress-1.8.1.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpclient\4.3.3\httpclient-4.3.3.jar;D:\BEN 10\libraries\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpcore\4.3.2\httpcore-4.3.2.jar;D:\BEN 10\libraries\it\unimi\dsi\fastutil\7.1.0\fastutil-7.1.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl\2.9.4-nightly-20150209\lwjgl-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl_util\2.9.4-nightly-20150209\lwjgl_util-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\com\mojang\text2speech\1.10.3\text2speech-1.10.3.jar;D:\BEN 10\libraries\net\minecraftforge\forge\1.12.2-14.23.5.2860\forge-1.12.2-14.23.5.2860.jar;D:\BEN 10\libraries\org\ow2\asm\asm-debug-all\5.2\asm-debug-all-5.2.jar;D:\BEN 10\libraries\net\minecraft\launchwrapper\1.12\launchwrapper-1.12.jar;D:\BEN 10\libraries\org\jline\jline\3.5.1\jline-3.5.1.jar;D:\BEN 10\libraries\com\typesafe\akka\akka-actor_2.11\2.3.3\akka-actor_2.11-2.3.3.jar;D:\BEN 10\libraries\com\typesafe\config\1.2.1\config-1.2.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-actors-migration_2.11\1.1.0\scala-actors-migration_2.11-1.1.0.jar;D:\BEN 10\libraries\org\scala-lang\scala-compiler\2.11.1\scala-compiler-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-library_2.11\1.0.2_mc\scala-continuations-library_2.11-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-plugin_2.11.1\1.0.2_mc\scala-continuations-plugin_2.11.1-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\scala-library\2.11.1\scala-library-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-parser-combinators_2.11\1.0.1\scala-parser-combinators_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-reflect\2.11.1\scala-reflect-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-swing_2.11\1.0.1\scala-swing_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-xml_2.11\1.0.2\scala-xml_2.11-1.0.2.jar;D:\BEN 10\libraries\lzma\lzma\0.0.1\lzma-0.0.1.jar;D:\BEN 10\libraries\java3d\vecmath\1.5.2\vecmath-1.5.2.jar;D:\BEN 10\libraries\net\sf\trove4j\trove4j\3.0.3\trove4j-3.0.3.jar;D:\BEN 10\libraries\org\apache\maven\maven-artifact\3.5.3\maven-artifact-3.5.3.jar;D:\BEN 10\versions\Ben10Craft\Ben10Craft.jar" -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch --username Quasar2323 --version Ben10Craft --gameDir "D:\BEN 10\versions\Ben10Craft" --assetsDir "D:\BEN 10\assets" --assetIndex 1.12 --uuid 077e3afcb77643c48ae30f9dbb755bef --accessToken eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************YvPCg --userType msa --versionType PCL --tweakClass net.minecraftforge.fml.common.launcher.FMLTweaker --versionType Forge --height 480 --width 854 
[17:50:06.870] [Loader] 加载器 获取启动参数 状态改变：Finished
[17:50:06.873] [Loader] 加载器 解压文件 状态改变：Loading
[17:50:06.874] [Launch] 正在解压 Natives 文件
[17:50:06.877] [Loader] 加载器 解压文件 状态改变：Finished
[17:50:06.906] [Loader] 加载器 预启动处理 状态改变：Loading
[17:50:06.906] [System] 无需调整显卡设置：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[17:50:06.906] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[17:50:06.908] [Launch] 已更新 launcher_profiles.json
[17:50:06.909] [Launch] 需要的语言为 zh_cn，当前语言为 zh_cn，无需修改
[17:50:06.909] [Loader] 加载器 预启动处理 状态改变：Finished
[17:50:06.948] [Loader] 加载器 执行自定义命令 状态改变：Loading
[17:50:06.950] [Loader] 加载器 执行自定义命令 状态改变：Finished
[17:50:06.983] [Loader] 加载器 启动进程 状态改变：Loading
[17:50:07.133] [Launch] 已启动游戏进程：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[17:50:07.134] [Loader] 加载器 启动进程 状态改变：Finished
[17:50:07.167] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[17:50:07.168] [Launch] 
[17:50:07.174] [Launch] ~ 基础参数 ~
[17:50:07.174] [Launch] PCL 版本：Release 2.10.3 (361)
[17:50:07.174] [Launch] 游戏版本：1.12.2, Forge 14.23.5.2860（识别为 1.12.2）
[17:50:07.174] [Launch] 资源版本：1.12
[17:50:07.174] [Launch] 版本继承：无
[17:50:07.174] [Launch] 分配的内存：15 GB（15360 MB）
[17:50:07.174] [Launch] MC 文件夹：D:\BEN 10\
[17:50:07.174] [Launch] 版本文件夹：D:\BEN 10\versions\Ben10Craft\
[17:50:07.174] [Launch] 版本隔离：True
[17:50:07.174] [Launch] HMCL 格式：False
[17:50:07.174] [Launch] Java 信息：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[17:50:07.174] [Launch] 环境变量：未设置
[17:50:07.174] [Launch] Natives 文件夹：D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives
[17:50:07.174] [Launch] 
[17:50:07.174] [Launch] ~ 登录参数 ~
[17:50:07.174] [Launch] 玩家用户名：Quasar2323
[17:50:07.174] [Launch] AccessToken：eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************YvPCg
[17:50:07.174] [Launch] ClientToken：077e3afcb77643c48ae30f9dbb755bef
[17:50:07.174] [Launch] UUID：077e3afcb77643c48ae30f9dbb755bef
[17:50:07.174] [Launch] 登录方式：Microsoft
[17:50:07.174] [Launch] 
[17:50:07.174] [Launch] [35288] 开始 Minecraft 日志监控
[17:50:07.174] [Launch] [全局] 出现运行中的 Minecraft
[17:50:07.258] [Report] FPS 0, 动画 10, 下载中 0（0 B/s）
[17:50:08.501] [Launch] [35288] 日志 1/5：已出现日志输出
[17:50:12.563] [Launch] [35288] 日志 2/5：游戏用户已设置
[17:50:15.148] [Launch] [35288] 日志 3/5：LWJGL 版本已确认
[17:50:15.395] [Launch] [35288] Minecraft 窗口已加载：（3082064）
[17:50:16.092] [Launch] [35288] Minecraft 加载已完成
[17:50:16.137] [Loader] 加载器 等待游戏窗口出现 状态改变：Finished
[17:50:16.162] [Loader] 加载器 结束处理 状态改变：Loading
[17:50:16.163] [Launch] 开始启动结束处理
[17:50:16.163] [Launch] 启动器可见性：5
[17:50:16.163] [Loader] 加载器 结束处理 状态改变：Finished
[17:50:16.235] [Loader] 加载器 Minecraft 启动 状态改变：Finished
[17:50:16.259] [Loader] 加载器 Loader Launch 状态改变：Finished
[17:50:16.291] [UI] 弹出提示：Ben10Craft 启动成功！
[17:50:16.291] [Taskbar] Minecraft 启动 已移出任务列表
[17:50:57.153] [Launch] [35288] 日志 4/5：OpenAL 已加载
[17:51:32.516] [Launch] [35288] 日志 5/5：材质已加载
[18:03:13.430] [Launch] [35288] Minecraft 已退出，返回值：0
[18:03:13.431] [Launch] [全局] 已无运行中的 Minecraft
[18:03:13.668] [Launch] [35288] Minecraft 日志监控已退出
[18:05:03.180] [Control] 按下按钮：启动游戏
[18:05:03.183] [Loader] 加载器 Loader Launch 状态改变：Loading
[18:05:03.188] [Launch] 预检测已通过
[18:05:03.189] [Download] 开始后台检查资源文件索引
[18:05:03.189] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[18:05:03.189] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[18:05:03.189] [Loader] 加载器 登录 状态改变：Waiting
[18:05:03.189] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[18:05:03.189] [Loader] 加载器 获取 Java 状态改变：Loading
[18:05:03.190] [Launch] Java 版本需求：最低 1.8.0.0，最高 1.8.999.999
[18:05:03.190] [Java] 开始完全遍历查找：D:\BEN 10\
[18:05:03.190] [Loader] 加载器 登录 状态改变：Loading
[18:05:03.190] [Loader] 加载器 补全文件 状态改变：Loading
[18:05:03.190] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[18:05:03.190] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[18:05:03.190] [Launch] 登录加载已开始
[18:05:03.190] [Download] 无需更新资源文件索引，取消
[18:05:03.190] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[18:05:03.190] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Aborted
[18:05:03.190] [Loader] 加载器 后台更新资源文件索引 状态改变：Aborted
[18:05:03.190] [Loader] 加载器 后台下载资源文件索引 状态改变：Aborted
[18:05:03.190] [Download] 后台下载资源文件索引 已取消！
[18:05:03.191] [Loader] 加载线程 后台分析资源文件索引地址 (36) 已中断但线程正常运行至结束，输出被弃用（最新线程：-1）
[18:05:03.191] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[18:05:03.191] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[18:05:03.191] [Launch] 登录方式：正版（Quasar2323）
[18:05:03.191] [Launch] 开始微软登录步骤 1/6（刷新登录）
[18:05:03.191] [Loader] 加载器 内存优化 状态改变：Loading
[18:05:03.191] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[18:05:03.191] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[18:05:03.191] [Launch] 内存优化开始
[18:05:03.191] [Taskbar] Minecraft 启动 已加入任务列表
[18:05:03.191] [Test] 没有管理员权限，将以命令行方式进行内存优化
[18:05:03.203] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[18:05:03.203] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[18:05:03.203] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[18:05:03.203] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[18:05:03.221] [Java] 开始完全遍历查找：D:\BEN 10\versions\Ben10Craft\
[18:05:03.221] [Minecraft] 获取支持库列表：Ben10Craft
[18:05:03.222] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[18:05:03.222] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[18:05:03.222] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[18:05:03.232] [Java] 排序后的 Java 优先顺序：
[18:05:03.232] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[18:05:03.232] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[18:05:03.232] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[18:05:03.232] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[18:05:03.232] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[18:05:03.232] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[18:05:03.232] [Java] 最终选定的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[18:05:03.232] [Launch] 选择的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[18:05:03.232] [Loader] 加载器 获取 Java 状态改变：Finished
[18:05:03.232] [Loader] 由于输入条件变更，重启进行中的加载器 登录
[18:05:03.233] [Launch] 登录加载已开始
[18:05:03.233] [Launch] 登录方式：正版（Quasar2323）
[18:05:03.233] [Launch] 开始微软登录步骤 1/6（刷新登录）
[18:05:03.233] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[18:05:03.298] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[18:05:03.298] [Loader] 加载器 下载资源文件 状态改变：Loading
[18:05:03.298] [Loader] 加载器 下载资源文件 状态改变：Finished
[18:05:03.298] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[18:05:03.459] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[18:05:03.459] [Loader] 加载器 下载支持库文件 状态改变：Loading
[18:05:03.459] [Loader] 加载器 下载支持库文件 状态改变：Finished
[18:05:03.459] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[18:05:03.459] [Loader] 加载器 补全文件 状态改变：Finished
[18:05:03.491] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[18:05:04.405] [Launch] 开始微软登录步骤 2/6
[18:05:04.405] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[18:05:04.670] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[18:05:05.182] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[18:05:05.943] [Launch] 开始微软登录步骤 3/6
[18:05:05.943] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[18:05:06.204] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[18:05:06.713] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[18:05:07.472] [Launch] 开始微软登录步骤 4/6
[18:05:07.472] [Net] 发起网络请求（POST，https://api.minecraftservices.com/authentication/login_with_xbox），最大超时 15000
[18:05:08.735] [Launch] 开始微软登录步骤 5/6
[18:05:08.735] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[18:05:08.990] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[18:05:09.503] [Launch] 开始微软登录步骤 6/6
[18:05:09.503] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[18:05:09.768] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[18:05:10.282] [Launch] 微软登录完成
[18:05:10.282] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[18:05:10.296] [Launch] 登录加载已结束
[18:05:10.296] [Loader] 加载器 登录 状态改变：Finished
[18:05:10.299] [Loader] 由于输入条件变更，重启已完成的加载器 登录
[18:05:10.299] [Loader] 加载器 登录 状态改变：Loading
[18:05:10.299] [Launch] 登录加载已开始
[18:05:10.299] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[18:05:10.300] [Launch] 登录方式：正版（Quasar2323）
[18:05:10.300] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[18:05:10.312] [Launch] 登录加载已结束
[18:05:10.312] [Loader] 加载器 登录 状态改变：Finished
[18:05:14.835] [Test] 内存优化完成，可用内存改变量：1.96 G，大致剩余内存：7.17 G
[18:05:14.932] [Loader] 加载器 内存优化 状态改变：Finished
[18:05:14.933] [Loader] 加载器 获取启动参数 状态改变：Loading
[18:05:14.935] [Launch] 开始获取 Minecraft 启动参数
[18:05:14.935] [Launch] 获取旧版 JVM 参数
[18:05:14.935] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[18:05:14.935] [System] 获取资源：JavaWrapper
[18:05:14.938] [Launch] 旧版 JVM 参数获取成功：
[18:05:14.939] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp ${classpath} -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch
[18:05:14.939] [Launch] 获取旧版 Game 参数
[18:05:14.939] [Launch] 旧版 Game 参数获取成功
[18:05:14.939] [Minecraft] 获取支持库列表：Ben10Craft
[18:05:14.940] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[18:05:14.940] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[18:05:14.940] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[18:05:14.942] [Launch] Minecraft 启动参数：
[18:05:14.943] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp "D:\BEN 10\libraries\com\mojang\patchy\1.3.9\patchy-1.3.9.jar;D:\BEN 10\libraries\oshi-project\oshi-core\1.1\oshi-core-1.1.jar;D:\BEN 10\libraries\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;D:\BEN 10\libraries\net\java\dev\jna\platform\3.4.0\platform-3.4.0.jar;D:\BEN 10\libraries\com\ibm\icu\icu4j-core-mojang\51.2\icu4j-core-mojang-51.2.jar;D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar;D:\BEN 10\libraries\com\paulscode\codecjorbis\20101023\codecjorbis-20101023.jar;D:\BEN 10\libraries\com\paulscode\codecwav\20101023\codecwav-20101023.jar;D:\BEN 10\libraries\com\paulscode\libraryjavasound\20101123\libraryjavasound-20101123.jar;D:\BEN 10\libraries\com\paulscode\librarylwjglopenal\20100824\librarylwjglopenal-20100824.jar;D:\BEN 10\libraries\com\paulscode\soundsystem\20120107\soundsystem-20120107.jar;D:\BEN 10\libraries\io\netty\netty-all\4.1.9.Final\netty-all-4.1.9.Final.jar;D:\BEN 10\libraries\com\google\guava\guava\21.0\guava-21.0.jar;D:\BEN 10\libraries\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;D:\BEN 10\libraries\commons-io\commons-io\2.5\commons-io-2.5.jar;D:\BEN 10\libraries\commons-codec\commons-codec\1.10\commons-codec-1.10.jar;D:\BEN 10\libraries\net\java\jinput\jinput\2.0.5\jinput-2.0.5.jar;D:\BEN 10\libraries\net\java\jutils\jutils\1.0.0\jutils-1.0.0.jar;D:\BEN 10\libraries\com\google\code\gson\gson\2.8.0\gson-2.8.0.jar;D:\BEN 10\libraries\com\mojang\authlib\1.5.25\authlib-1.5.25.jar;D:\BEN 10\libraries\com\mojang\realms\1.10.22\realms-1.10.22.jar;D:\BEN 10\libraries\org\apache\commons\commons-compress\1.8.1\commons-compress-1.8.1.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpclient\4.3.3\httpclient-4.3.3.jar;D:\BEN 10\libraries\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpcore\4.3.2\httpcore-4.3.2.jar;D:\BEN 10\libraries\it\unimi\dsi\fastutil\7.1.0\fastutil-7.1.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl\2.9.4-nightly-20150209\lwjgl-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl_util\2.9.4-nightly-20150209\lwjgl_util-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\com\mojang\text2speech\1.10.3\text2speech-1.10.3.jar;D:\BEN 10\libraries\net\minecraftforge\forge\1.12.2-14.23.5.2860\forge-1.12.2-14.23.5.2860.jar;D:\BEN 10\libraries\org\ow2\asm\asm-debug-all\5.2\asm-debug-all-5.2.jar;D:\BEN 10\libraries\net\minecraft\launchwrapper\1.12\launchwrapper-1.12.jar;D:\BEN 10\libraries\org\jline\jline\3.5.1\jline-3.5.1.jar;D:\BEN 10\libraries\com\typesafe\akka\akka-actor_2.11\2.3.3\akka-actor_2.11-2.3.3.jar;D:\BEN 10\libraries\com\typesafe\config\1.2.1\config-1.2.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-actors-migration_2.11\1.1.0\scala-actors-migration_2.11-1.1.0.jar;D:\BEN 10\libraries\org\scala-lang\scala-compiler\2.11.1\scala-compiler-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-library_2.11\1.0.2_mc\scala-continuations-library_2.11-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-plugin_2.11.1\1.0.2_mc\scala-continuations-plugin_2.11.1-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\scala-library\2.11.1\scala-library-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-parser-combinators_2.11\1.0.1\scala-parser-combinators_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-reflect\2.11.1\scala-reflect-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-swing_2.11\1.0.1\scala-swing_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-xml_2.11\1.0.2\scala-xml_2.11-1.0.2.jar;D:\BEN 10\libraries\lzma\lzma\0.0.1\lzma-0.0.1.jar;D:\BEN 10\libraries\java3d\vecmath\1.5.2\vecmath-1.5.2.jar;D:\BEN 10\libraries\net\sf\trove4j\trove4j\3.0.3\trove4j-3.0.3.jar;D:\BEN 10\libraries\org\apache\maven\maven-artifact\3.5.3\maven-artifact-3.5.3.jar;D:\BEN 10\versions\Ben10Craft\Ben10Craft.jar" -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch --username Quasar2323 --version Ben10Craft --gameDir "D:\BEN 10\versions\Ben10Craft" --assetsDir "D:\BEN 10\assets" --assetIndex 1.12 --uuid 077e3afcb77643c48ae30f9dbb755bef --accessToken eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************9slpw --userType msa --versionType PCL --tweakClass net.minecraftforge.fml.common.launcher.FMLTweaker --versionType Forge --height 480 --width 854 
[18:05:14.943] [Loader] 加载器 获取启动参数 状态改变：Finished
[18:05:14.949] [Loader] 加载器 解压文件 状态改变：Loading
[18:05:14.950] [Launch] 正在解压 Natives 文件
[18:05:14.955] [Loader] 加载器 解压文件 状态改变：Finished
[18:05:14.969] [Loader] 加载器 预启动处理 状态改变：Loading
[18:05:14.970] [System] 无需调整显卡设置：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[18:05:14.970] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[18:05:14.973] [Launch] 已更新 launcher_profiles.json
[18:05:14.974] [Launch] 需要的语言为 zh_cn，当前语言为 zh_cn，无需修改
[18:05:14.974] [Loader] 加载器 预启动处理 状态改变：Finished
[18:05:14.987] [Loader] 加载器 执行自定义命令 状态改变：Loading
[18:05:14.990] [Loader] 加载器 执行自定义命令 状态改变：Finished
[18:05:15.010] [Loader] 加载器 启动进程 状态改变：Loading
[18:05:15.166] [Launch] 已启动游戏进程：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[18:05:15.166] [Loader] 加载器 启动进程 状态改变：Finished
[18:05:15.202] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[18:05:15.202] [Launch] 
[18:05:15.203] [Launch] ~ 基础参数 ~
[18:05:15.203] [Launch] PCL 版本：Release 2.10.3 (361)
[18:05:15.203] [Launch] 游戏版本：1.12.2, Forge 14.23.5.2860（识别为 1.12.2）
[18:05:15.203] [Launch] 资源版本：1.12
[18:05:15.203] [Launch] 版本继承：无
[18:05:15.203] [Launch] 分配的内存：15 GB（15360 MB）
[18:05:15.203] [Launch] MC 文件夹：D:\BEN 10\
[18:05:15.203] [Launch] 版本文件夹：D:\BEN 10\versions\Ben10Craft\
[18:05:15.203] [Launch] 版本隔离：True
[18:05:15.203] [Launch] HMCL 格式：False
[18:05:15.203] [Launch] Java 信息：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[18:05:15.203] [Launch] 环境变量：未设置
[18:05:15.203] [Launch] Natives 文件夹：D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives
[18:05:15.203] [Launch] 
[18:05:15.203] [Launch] ~ 登录参数 ~
[18:05:15.203] [Launch] 玩家用户名：Quasar2323
[18:05:15.203] [Launch] AccessToken：eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************9slpw
[18:05:15.203] [Launch] ClientToken：077e3afcb77643c48ae30f9dbb755bef
[18:05:15.203] [Launch] UUID：077e3afcb77643c48ae30f9dbb755bef
[18:05:15.203] [Launch] 登录方式：Microsoft
[18:05:15.203] [Launch] 
[18:05:15.213] [Launch] [25260] 开始 Minecraft 日志监控
[18:05:15.213] [Launch] [全局] 出现运行中的 Minecraft
[18:05:16.550] [Launch] [25260] 日志 1/5：已出现日志输出
[18:05:20.279] [Launch] [25260] 日志 2/5：游戏用户已设置
[18:05:22.774] [Launch] [25260] Minecraft 窗口已加载：（11734246）
[18:05:22.774] [Launch] [25260] 日志 3/5：LWJGL 版本已确认
[18:05:22.774] [Launch] [25260] Minecraft 加载已完成
[18:05:22.811] [Loader] 加载器 等待游戏窗口出现 状态改变：Finished
[18:05:22.817] [Loader] 加载器 结束处理 状态改变：Loading
[18:05:22.818] [Launch] 开始启动结束处理
[18:05:22.818] [Launch] 启动器可见性：5
[18:05:22.819] [Loader] 加载器 结束处理 状态改变：Finished
[18:05:22.854] [Loader] 加载器 Minecraft 启动 状态改变：Finished
[18:05:22.873] [Taskbar] Minecraft 启动 已移出任务列表
[18:05:23.029] [Loader] 加载器 Loader Launch 状态改变：Finished
[18:05:23.062] [UI] 弹出提示：Ben10Craft 启动成功！
[18:05:53.853] [Launch] [25260] 日志 4/5：OpenAL 已加载
[18:06:29.592] [Launch] [25260] 日志 5/5：材质已加载
[18:07:52.841] [Launch] [25260] Minecraft 已退出，返回值：0
[18:07:52.841] [Launch] [全局] 已无运行中的 Minecraft
[18:07:53.071] [Launch] [25260] Minecraft 日志监控已退出
[18:09:23.811] [Control] 按下按钮：启动游戏
[18:09:23.812] [Loader] 加载器 Loader Launch 状态改变：Loading
[18:09:23.818] [Launch] 预检测已通过
[18:09:23.818] [Download] 开始后台检查资源文件索引
[18:09:23.818] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[18:09:23.818] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[18:09:23.819] [Loader] 加载器 登录 状态改变：Waiting
[18:09:23.819] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[18:09:23.819] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[18:09:23.819] [Loader] 加载器 获取 Java 状态改变：Loading
[18:09:23.819] [Launch] Java 版本需求：最低 1.8.0.0，最高 1.8.999.999
[18:09:23.819] [Java] 开始完全遍历查找：D:\BEN 10\
[18:09:23.819] [Download] 无需更新资源文件索引，取消
[18:09:23.819] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Aborted
[18:09:23.819] [Loader] 加载器 后台更新资源文件索引 状态改变：Aborted
[18:09:23.819] [Loader] 加载器 后台下载资源文件索引 状态改变：Aborted
[18:09:23.819] [Download] 后台下载资源文件索引 已取消！
[18:09:23.819] [Loader] 加载线程 后台分析资源文件索引地址 (90) 已中断但线程正常运行至结束，输出被弃用（最新线程：-1）
[18:09:23.819] [Loader] 加载器 登录 状态改变：Loading
[18:09:23.819] [Loader] 加载器 补全文件 状态改变：Loading
[18:09:23.819] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[18:09:23.819] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[18:09:23.819] [Launch] 登录加载已开始
[18:09:23.819] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[18:09:23.820] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[18:09:23.820] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[18:09:23.820] [Launch] 登录方式：正版（Quasar2323）
[18:09:23.820] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[18:09:23.820] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[18:09:23.820] [Loader] 加载器 内存优化 状态改变：Loading
[18:09:23.820] [Launch] 内存优化开始
[18:09:23.820] [Taskbar] Minecraft 启动 已加入任务列表
[18:09:23.821] [Test] 没有管理员权限，将以命令行方式进行内存优化
[18:09:23.824] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[18:09:23.824] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[18:09:23.825] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[18:09:23.825] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[18:09:23.839] [Launch] 登录加载已结束
[18:09:23.839] [Loader] 加载器 登录 状态改变：Finished
[18:09:23.840] [Java] 开始完全遍历查找：D:\BEN 10\versions\Ben10Craft\
[18:09:23.846] [Minecraft] 获取支持库列表：Ben10Craft
[18:09:23.847] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[18:09:23.847] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[18:09:23.847] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[18:09:23.850] [Java] 排序后的 Java 优先顺序：
[18:09:23.850] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[18:09:23.850] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[18:09:23.850] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[18:09:23.850] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[18:09:23.850] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[18:09:23.850] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[18:09:23.850] [Java] 最终选定的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[18:09:23.850] [Launch] 选择的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[18:09:23.850] [Loader] 加载器 获取 Java 状态改变：Finished
[18:09:23.878] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[18:09:23.878] [Loader] 加载器 下载资源文件 状态改变：Loading
[18:09:23.879] [Loader] 加载器 下载资源文件 状态改变：Finished
[18:09:23.879] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[18:09:24.048] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[18:09:24.048] [Loader] 加载器 下载支持库文件 状态改变：Loading
[18:09:24.048] [Loader] 加载器 下载支持库文件 状态改变：Finished
[18:09:24.048] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[18:09:24.048] [Loader] 加载器 补全文件 状态改变：Finished
[18:09:34.909] [Test] 内存优化完成，可用内存改变量：1.75 G，大致剩余内存：7.19 G
[18:09:35.017] [Loader] 加载器 内存优化 状态改变：Finished
[18:09:35.019] [Loader] 加载器 获取启动参数 状态改变：Loading
[18:09:35.021] [Launch] 开始获取 Minecraft 启动参数
[18:09:35.022] [Launch] 获取旧版 JVM 参数
[18:09:35.023] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[18:09:35.023] [System] 获取资源：JavaWrapper
[18:09:35.025] [Launch] 旧版 JVM 参数获取成功：
[18:09:35.026] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp ${classpath} -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch
[18:09:35.026] [Launch] 获取旧版 Game 参数
[18:09:35.026] [Launch] 旧版 Game 参数获取成功
[18:09:35.026] [Minecraft] 获取支持库列表：Ben10Craft
[18:09:35.027] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[18:09:35.027] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[18:09:35.027] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[18:09:35.029] [Launch] Minecraft 启动参数：
[18:09:35.030] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp "D:\BEN 10\libraries\com\mojang\patchy\1.3.9\patchy-1.3.9.jar;D:\BEN 10\libraries\oshi-project\oshi-core\1.1\oshi-core-1.1.jar;D:\BEN 10\libraries\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;D:\BEN 10\libraries\net\java\dev\jna\platform\3.4.0\platform-3.4.0.jar;D:\BEN 10\libraries\com\ibm\icu\icu4j-core-mojang\51.2\icu4j-core-mojang-51.2.jar;D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar;D:\BEN 10\libraries\com\paulscode\codecjorbis\20101023\codecjorbis-20101023.jar;D:\BEN 10\libraries\com\paulscode\codecwav\20101023\codecwav-20101023.jar;D:\BEN 10\libraries\com\paulscode\libraryjavasound\20101123\libraryjavasound-20101123.jar;D:\BEN 10\libraries\com\paulscode\librarylwjglopenal\20100824\librarylwjglopenal-20100824.jar;D:\BEN 10\libraries\com\paulscode\soundsystem\20120107\soundsystem-20120107.jar;D:\BEN 10\libraries\io\netty\netty-all\4.1.9.Final\netty-all-4.1.9.Final.jar;D:\BEN 10\libraries\com\google\guava\guava\21.0\guava-21.0.jar;D:\BEN 10\libraries\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;D:\BEN 10\libraries\commons-io\commons-io\2.5\commons-io-2.5.jar;D:\BEN 10\libraries\commons-codec\commons-codec\1.10\commons-codec-1.10.jar;D:\BEN 10\libraries\net\java\jinput\jinput\2.0.5\jinput-2.0.5.jar;D:\BEN 10\libraries\net\java\jutils\jutils\1.0.0\jutils-1.0.0.jar;D:\BEN 10\libraries\com\google\code\gson\gson\2.8.0\gson-2.8.0.jar;D:\BEN 10\libraries\com\mojang\authlib\1.5.25\authlib-1.5.25.jar;D:\BEN 10\libraries\com\mojang\realms\1.10.22\realms-1.10.22.jar;D:\BEN 10\libraries\org\apache\commons\commons-compress\1.8.1\commons-compress-1.8.1.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpclient\4.3.3\httpclient-4.3.3.jar;D:\BEN 10\libraries\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpcore\4.3.2\httpcore-4.3.2.jar;D:\BEN 10\libraries\it\unimi\dsi\fastutil\7.1.0\fastutil-7.1.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl\2.9.4-nightly-20150209\lwjgl-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl_util\2.9.4-nightly-20150209\lwjgl_util-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\com\mojang\text2speech\1.10.3\text2speech-1.10.3.jar;D:\BEN 10\libraries\net\minecraftforge\forge\1.12.2-14.23.5.2860\forge-1.12.2-14.23.5.2860.jar;D:\BEN 10\libraries\org\ow2\asm\asm-debug-all\5.2\asm-debug-all-5.2.jar;D:\BEN 10\libraries\net\minecraft\launchwrapper\1.12\launchwrapper-1.12.jar;D:\BEN 10\libraries\org\jline\jline\3.5.1\jline-3.5.1.jar;D:\BEN 10\libraries\com\typesafe\akka\akka-actor_2.11\2.3.3\akka-actor_2.11-2.3.3.jar;D:\BEN 10\libraries\com\typesafe\config\1.2.1\config-1.2.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-actors-migration_2.11\1.1.0\scala-actors-migration_2.11-1.1.0.jar;D:\BEN 10\libraries\org\scala-lang\scala-compiler\2.11.1\scala-compiler-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-library_2.11\1.0.2_mc\scala-continuations-library_2.11-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-plugin_2.11.1\1.0.2_mc\scala-continuations-plugin_2.11.1-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\scala-library\2.11.1\scala-library-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-parser-combinators_2.11\1.0.1\scala-parser-combinators_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-reflect\2.11.1\scala-reflect-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-swing_2.11\1.0.1\scala-swing_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-xml_2.11\1.0.2\scala-xml_2.11-1.0.2.jar;D:\BEN 10\libraries\lzma\lzma\0.0.1\lzma-0.0.1.jar;D:\BEN 10\libraries\java3d\vecmath\1.5.2\vecmath-1.5.2.jar;D:\BEN 10\libraries\net\sf\trove4j\trove4j\3.0.3\trove4j-3.0.3.jar;D:\BEN 10\libraries\org\apache\maven\maven-artifact\3.5.3\maven-artifact-3.5.3.jar;D:\BEN 10\versions\Ben10Craft\Ben10Craft.jar" -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch --username Quasar2323 --version Ben10Craft --gameDir "D:\BEN 10\versions\Ben10Craft" --assetsDir "D:\BEN 10\assets" --assetIndex 1.12 --uuid 077e3afcb77643c48ae30f9dbb755bef --accessToken eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************9slpw --userType msa --versionType PCL --tweakClass net.minecraftforge.fml.common.launcher.FMLTweaker --versionType Forge --height 480 --width 854 
[18:09:35.030] [Loader] 加载器 获取启动参数 状态改变：Finished
[18:09:35.034] [Loader] 加载器 解压文件 状态改变：Loading
[18:09:35.042] [Launch] 正在解压 Natives 文件
[18:09:35.045] [Loader] 加载器 解压文件 状态改变：Finished
[18:09:35.057] [Loader] 加载器 预启动处理 状态改变：Loading
[18:09:35.058] [System] 无需调整显卡设置：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[18:09:35.058] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[18:09:35.060] [Launch] 已更新 launcher_profiles.json
[18:09:35.061] [Launch] 需要的语言为 zh_cn，当前语言为 zh_cn，无需修改
[18:09:35.061] [Loader] 加载器 预启动处理 状态改变：Finished
[18:09:35.079] [Loader] 加载器 执行自定义命令 状态改变：Loading
[18:09:35.081] [Loader] 加载器 执行自定义命令 状态改变：Finished
[18:09:35.098] [Loader] 加载器 启动进程 状态改变：Loading
[18:09:35.279] [Launch] 已启动游戏进程：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[18:09:35.279] [Loader] 加载器 启动进程 状态改变：Finished
[18:09:35.307] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[18:09:35.307] [Launch] 
[18:09:35.307] [Launch] ~ 基础参数 ~
[18:09:35.307] [Launch] PCL 版本：Release 2.10.3 (361)
[18:09:35.307] [Launch] 游戏版本：1.12.2, Forge 14.23.5.2860（识别为 1.12.2）
[18:09:35.307] [Launch] 资源版本：1.12
[18:09:35.307] [Launch] 版本继承：无
[18:09:35.307] [Launch] 分配的内存：15 GB（15360 MB）
[18:09:35.307] [Launch] MC 文件夹：D:\BEN 10\
[18:09:35.307] [Launch] 版本文件夹：D:\BEN 10\versions\Ben10Craft\
[18:09:35.307] [Launch] 版本隔离：True
[18:09:35.307] [Launch] HMCL 格式：False
[18:09:35.310] [Launch] Java 信息：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[18:09:35.310] [Launch] 环境变量：未设置
[18:09:35.310] [Launch] Natives 文件夹：D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives
[18:09:35.310] [Launch] 
[18:09:35.310] [Launch] ~ 登录参数 ~
[18:09:35.310] [Launch] 玩家用户名：Quasar2323
[18:09:35.310] [Launch] AccessToken：eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************9slpw
[18:09:35.310] [Launch] ClientToken：077e3afcb77643c48ae30f9dbb755bef
[18:09:35.310] [Launch] UUID：077e3afcb77643c48ae30f9dbb755bef
[18:09:35.310] [Launch] 登录方式：Microsoft
[18:09:35.310] [Launch] 
[18:09:35.310] [Launch] [18168] 开始 Minecraft 日志监控
[18:09:35.310] [Launch] [全局] 出现运行中的 Minecraft
[18:09:36.381] [Launch] [18168] 日志 1/5：已出现日志输出
[18:09:39.842] [Launch] [18168] 日志 2/5：游戏用户已设置
[18:09:42.312] [Launch] [18168] 日志 3/5：LWJGL 版本已确认
[18:09:42.554] [Launch] [18168] Minecraft 窗口已加载：（921692）
[18:09:43.470] [Launch] [18168] Minecraft 加载已完成
[18:09:43.580] [Loader] 加载器 等待游戏窗口出现 状态改变：Finished
[18:09:43.580] [Loader] 加载器 结束处理 状态改变：Loading
[18:09:43.582] [Launch] 开始启动结束处理
[18:09:43.582] [Launch] 启动器可见性：5
[18:09:43.587] [Loader] 加载器 结束处理 状态改变：Finished
[18:09:43.607] [Loader] 加载器 Minecraft 启动 状态改变：Finished
[18:09:43.612] [Loader] 加载器 Loader Launch 状态改变：Finished
[18:09:43.644] [UI] 弹出提示：Ben10Craft 启动成功！
[18:09:43.644] [Taskbar] Minecraft 启动 已移出任务列表
[18:10:13.894] [Launch] [18168] 日志 4/5：OpenAL 已加载
[18:10:49.425] [Launch] [18168] 日志 5/5：材质已加载
[18:11:48.098] [Launch] [18168] Minecraft 已退出，返回值：0
[18:11:48.098] [Launch] [全局] 已无运行中的 Minecraft
[18:11:48.331] [Launch] [18168] Minecraft 日志监控已退出
[18:20:51.982] [Animation] 两个动画帧间隔 734 ms
[19:10:39.215] [Control] 按下按钮：启动游戏
[19:10:39.216] [Loader] 加载器 Loader Launch 状态改变：Loading
[19:10:39.228] [Launch] 预检测已通过
[19:10:39.228] [Download] 开始后台检查资源文件索引
[19:10:39.228] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[19:10:39.228] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[19:10:39.228] [Loader] 加载器 登录 状态改变：Waiting
[19:10:39.228] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[19:10:39.228] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[19:10:39.228] [Loader] 加载器 获取 Java 状态改变：Loading
[19:10:39.229] [Launch] Java 版本需求：最低 1.8.0.0，最高 1.8.999.999
[19:10:39.229] [Java] 开始完全遍历查找：D:\BEN 10\
[19:10:39.229] [Loader] 加载器 登录 状态改变：Loading
[19:10:39.229] [Loader] 加载器 补全文件 状态改变：Loading
[19:10:39.229] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[19:10:39.229] [Launch] 登录加载已开始
[19:10:39.229] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[19:10:39.229] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[19:10:39.229] [Download] 无需更新资源文件索引，取消
[19:10:39.229] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Aborted
[19:10:39.229] [Loader] 加载器 后台更新资源文件索引 状态改变：Aborted
[19:10:39.229] [Loader] 加载器 后台下载资源文件索引 状态改变：Aborted
[19:10:39.229] [Download] 后台下载资源文件索引 已取消！
[19:10:39.229] [Loader] 加载线程 后台分析资源文件索引地址 (29) 已中断但线程正常运行至结束，输出被弃用（最新线程：-1）
[19:10:39.229] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[19:10:39.229] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[19:10:39.229] [Launch] 登录方式：正版（Quasar2323）
[19:10:39.229] [Loader] 加载器 内存优化 状态改变：Loading
[19:10:39.229] [Launch] 开始微软登录步骤 1/6（刷新登录）
[19:10:39.229] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[19:10:39.230] [Launch] 内存优化开始
[19:10:39.230] [Taskbar] Minecraft 启动 已加入任务列表
[19:10:39.230] [Test] 没有管理员权限，将以命令行方式进行内存优化
[19:10:39.230] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[19:10:39.243] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[19:10:39.243] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[19:10:39.244] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[19:10:39.244] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[19:10:39.262] [Minecraft] 获取支持库列表：Ben10Craft
[19:10:39.262] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[19:10:39.262] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[19:10:39.262] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[19:10:39.265] [Java] 开始完全遍历查找：D:\BEN 10\versions\Ben10Craft\
[19:10:39.284] [Java] 排序后的 Java 优先顺序：
[19:10:39.284] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[19:10:39.284] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[19:10:39.284] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[19:10:39.284] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[19:10:39.284] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[19:10:39.284] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[19:10:39.284] [Java] 最终选定的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[19:10:39.284] [Launch] 选择的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[19:10:39.284] [Loader] 加载器 获取 Java 状态改变：Finished
[19:10:39.287] [Loader] 由于输入条件变更，重启进行中的加载器 登录
[19:10:39.288] [Launch] 登录加载已开始
[19:10:39.288] [Launch] 登录方式：正版（Quasar2323）
[19:10:39.288] [Launch] 开始微软登录步骤 1/6（刷新登录）
[19:10:39.288] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[19:10:39.480] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[19:10:39.480] [Loader] 加载器 下载支持库文件 状态改变：Loading
[19:10:39.480] [Loader] 加载器 下载支持库文件 状态改变：Finished
[19:10:39.480] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[19:10:39.482] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[19:10:39.482] [Loader] 加载器 下载资源文件 状态改变：Loading
[19:10:39.483] [Loader] 加载器 下载资源文件 状态改变：Finished
[19:10:39.483] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[19:10:39.483] [Loader] 加载器 补全文件 状态改变：Finished
[19:10:39.547] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[19:10:40.685] [Launch] 开始微软登录步骤 2/6
[19:10:40.686] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[19:10:40.949] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[19:10:41.465] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[19:10:42.224] [Launch] 开始微软登录步骤 3/6
[19:10:42.224] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[19:10:42.490] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[19:10:42.998] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[19:10:43.760] [Launch] 开始微软登录步骤 4/6
[19:10:43.760] [Net] 发起网络请求（POST，https://api.minecraftservices.com/authentication/login_with_xbox），最大超时 15000
[19:10:44.973] [Launch] 开始微软登录步骤 5/6
[19:10:44.973] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[19:10:45.223] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[19:10:45.733] [Launch] 开始微软登录步骤 6/6
[19:10:45.733] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[19:10:45.997] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[19:10:46.506] [Launch] 微软登录完成
[19:10:46.506] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[19:10:46.520] [Launch] 登录加载已结束
[19:10:46.521] [Loader] 加载器 登录 状态改变：Finished
[19:10:46.521] [Loader] 由于输入条件变更，重启已完成的加载器 登录
[19:10:46.525] [Loader] 加载器 登录 状态改变：Loading
[19:10:46.525] [Launch] 登录加载已开始
[19:10:46.525] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[19:10:46.525] [Launch] 登录方式：正版（Quasar2323）
[19:10:46.525] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[19:10:46.536] [Launch] 登录加载已结束
[19:10:46.536] [Loader] 加载器 登录 状态改变：Finished
[19:10:51.206] [Test] 内存优化完成，可用内存改变量：3.06 G，大致剩余内存：4.56 G
[19:10:51.285] [Loader] 加载器 内存优化 状态改变：Finished
[19:10:51.285] [Loader] 加载器 获取启动参数 状态改变：Loading
[19:10:51.286] [Launch] 开始获取 Minecraft 启动参数
[19:10:51.286] [Launch] 获取旧版 JVM 参数
[19:10:51.286] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[19:10:51.286] [System] 获取资源：JavaWrapper
[19:10:51.288] [Launch] 旧版 JVM 参数获取成功：
[19:10:51.288] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp ${classpath} -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch
[19:10:51.288] [Launch] 获取旧版 Game 参数
[19:10:51.288] [Launch] 旧版 Game 参数获取成功
[19:10:51.288] [Minecraft] 获取支持库列表：Ben10Craft
[19:10:51.288] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[19:10:51.288] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[19:10:51.288] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[19:10:51.291] [Launch] Minecraft 启动参数：
[19:10:51.292] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp "D:\BEN 10\libraries\com\mojang\patchy\1.3.9\patchy-1.3.9.jar;D:\BEN 10\libraries\oshi-project\oshi-core\1.1\oshi-core-1.1.jar;D:\BEN 10\libraries\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;D:\BEN 10\libraries\net\java\dev\jna\platform\3.4.0\platform-3.4.0.jar;D:\BEN 10\libraries\com\ibm\icu\icu4j-core-mojang\51.2\icu4j-core-mojang-51.2.jar;D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar;D:\BEN 10\libraries\com\paulscode\codecjorbis\20101023\codecjorbis-20101023.jar;D:\BEN 10\libraries\com\paulscode\codecwav\20101023\codecwav-20101023.jar;D:\BEN 10\libraries\com\paulscode\libraryjavasound\20101123\libraryjavasound-20101123.jar;D:\BEN 10\libraries\com\paulscode\librarylwjglopenal\20100824\librarylwjglopenal-20100824.jar;D:\BEN 10\libraries\com\paulscode\soundsystem\20120107\soundsystem-20120107.jar;D:\BEN 10\libraries\io\netty\netty-all\4.1.9.Final\netty-all-4.1.9.Final.jar;D:\BEN 10\libraries\com\google\guava\guava\21.0\guava-21.0.jar;D:\BEN 10\libraries\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;D:\BEN 10\libraries\commons-io\commons-io\2.5\commons-io-2.5.jar;D:\BEN 10\libraries\commons-codec\commons-codec\1.10\commons-codec-1.10.jar;D:\BEN 10\libraries\net\java\jinput\jinput\2.0.5\jinput-2.0.5.jar;D:\BEN 10\libraries\net\java\jutils\jutils\1.0.0\jutils-1.0.0.jar;D:\BEN 10\libraries\com\google\code\gson\gson\2.8.0\gson-2.8.0.jar;D:\BEN 10\libraries\com\mojang\authlib\1.5.25\authlib-1.5.25.jar;D:\BEN 10\libraries\com\mojang\realms\1.10.22\realms-1.10.22.jar;D:\BEN 10\libraries\org\apache\commons\commons-compress\1.8.1\commons-compress-1.8.1.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpclient\4.3.3\httpclient-4.3.3.jar;D:\BEN 10\libraries\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpcore\4.3.2\httpcore-4.3.2.jar;D:\BEN 10\libraries\it\unimi\dsi\fastutil\7.1.0\fastutil-7.1.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl\2.9.4-nightly-20150209\lwjgl-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl_util\2.9.4-nightly-20150209\lwjgl_util-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\com\mojang\text2speech\1.10.3\text2speech-1.10.3.jar;D:\BEN 10\libraries\net\minecraftforge\forge\1.12.2-14.23.5.2860\forge-1.12.2-14.23.5.2860.jar;D:\BEN 10\libraries\org\ow2\asm\asm-debug-all\5.2\asm-debug-all-5.2.jar;D:\BEN 10\libraries\net\minecraft\launchwrapper\1.12\launchwrapper-1.12.jar;D:\BEN 10\libraries\org\jline\jline\3.5.1\jline-3.5.1.jar;D:\BEN 10\libraries\com\typesafe\akka\akka-actor_2.11\2.3.3\akka-actor_2.11-2.3.3.jar;D:\BEN 10\libraries\com\typesafe\config\1.2.1\config-1.2.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-actors-migration_2.11\1.1.0\scala-actors-migration_2.11-1.1.0.jar;D:\BEN 10\libraries\org\scala-lang\scala-compiler\2.11.1\scala-compiler-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-library_2.11\1.0.2_mc\scala-continuations-library_2.11-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-plugin_2.11.1\1.0.2_mc\scala-continuations-plugin_2.11.1-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\scala-library\2.11.1\scala-library-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-parser-combinators_2.11\1.0.1\scala-parser-combinators_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-reflect\2.11.1\scala-reflect-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-swing_2.11\1.0.1\scala-swing_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-xml_2.11\1.0.2\scala-xml_2.11-1.0.2.jar;D:\BEN 10\libraries\lzma\lzma\0.0.1\lzma-0.0.1.jar;D:\BEN 10\libraries\java3d\vecmath\1.5.2\vecmath-1.5.2.jar;D:\BEN 10\libraries\net\sf\trove4j\trove4j\3.0.3\trove4j-3.0.3.jar;D:\BEN 10\libraries\org\apache\maven\maven-artifact\3.5.3\maven-artifact-3.5.3.jar;D:\BEN 10\versions\Ben10Craft\Ben10Craft.jar" -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch --username Quasar2323 --version Ben10Craft --gameDir "D:\BEN 10\versions\Ben10Craft" --assetsDir "D:\BEN 10\assets" --assetIndex 1.12 --uuid 077e3afcb77643c48ae30f9dbb755bef --accessToken eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************XQwsg --userType msa --versionType PCL --tweakClass net.minecraftforge.fml.common.launcher.FMLTweaker --versionType Forge --height 480 --width 854 
[19:10:51.292] [Loader] 加载器 获取启动参数 状态改变：Finished
[19:10:51.295] [Loader] 加载器 解压文件 状态改变：Loading
[19:10:51.296] [Launch] 正在解压 Natives 文件
[19:10:51.298] [Loader] 加载器 解压文件 状态改变：Finished
[19:10:51.311] [Loader] 加载器 预启动处理 状态改变：Loading
[19:10:51.312] [System] 无需调整显卡设置：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[19:10:51.312] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[19:10:51.313] [Launch] 已更新 launcher_profiles.json
[19:10:51.316] [Launch] 需要的语言为 zh_cn，当前语言为 zh_cn，无需修改
[19:10:51.316] [Loader] 加载器 预启动处理 状态改变：Finished
[19:10:51.334] [Loader] 加载器 执行自定义命令 状态改变：Loading
[19:10:51.336] [Loader] 加载器 执行自定义命令 状态改变：Finished
[19:10:51.358] [Loader] 加载器 启动进程 状态改变：Loading
[19:10:51.517] [Launch] 已启动游戏进程：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[19:10:51.517] [Loader] 加载器 启动进程 状态改变：Finished
[19:10:51.540] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[19:10:51.540] [Launch] 
[19:10:51.546] [Launch] ~ 基础参数 ~
[19:10:51.546] [Launch] PCL 版本：Release 2.10.3 (361)
[19:10:51.546] [Launch] 游戏版本：1.12.2, Forge 14.23.5.2860（识别为 1.12.2）
[19:10:51.546] [Launch] 资源版本：1.12
[19:10:51.546] [Launch] 版本继承：无
[19:10:51.546] [Launch] 分配的内存：15 GB（15360 MB）
[19:10:51.546] [Launch] MC 文件夹：D:\BEN 10\
[19:10:51.546] [Launch] 版本文件夹：D:\BEN 10\versions\Ben10Craft\
[19:10:51.546] [Launch] 版本隔离：True
[19:10:51.546] [Launch] HMCL 格式：False
[19:10:51.546] [Launch] Java 信息：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[19:10:51.546] [Launch] 环境变量：未设置
[19:10:51.546] [Launch] Natives 文件夹：D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives
[19:10:51.546] [Launch] 
[19:10:51.546] [Launch] ~ 登录参数 ~
[19:10:51.546] [Launch] 玩家用户名：Quasar2323
[19:10:51.546] [Launch] AccessToken：eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************XQwsg
[19:10:51.546] [Launch] ClientToken：077e3afcb77643c48ae30f9dbb755bef
[19:10:51.546] [Launch] UUID：077e3afcb77643c48ae30f9dbb755bef
[19:10:51.546] [Launch] 登录方式：Microsoft
[19:10:51.546] [Launch] 
[19:10:51.547] [Launch] [27684] 开始 Minecraft 日志监控
[19:10:51.547] [Launch] [全局] 出现运行中的 Minecraft
[19:10:52.715] [Launch] [27684] 日志 1/5：已出现日志输出
[19:10:56.537] [Launch] [27684] 日志 2/5：游戏用户已设置
[19:10:58.724] [Launch] [27684] 日志 3/5：LWJGL 版本已确认
[19:10:59.029] [Launch] [27684] Minecraft 窗口已加载：（9441584）
[19:10:59.954] [Launch] [27684] Minecraft 加载已完成
[19:11:00.015] [Loader] 加载器 等待游戏窗口出现 状态改变：Finished
[19:11:00.016] [Loader] 加载器 结束处理 状态改变：Loading
[19:11:00.016] [Launch] 开始启动结束处理
[19:11:00.016] [Launch] 启动器可见性：5
[19:11:00.017] [Loader] 加载器 结束处理 状态改变：Finished
[19:11:00.056] [Loader] 加载器 Minecraft 启动 状态改变：Finished
[19:11:00.061] [Loader] 加载器 Loader Launch 状态改变：Finished
[19:11:00.079] [UI] 弹出提示：Ben10Craft 启动成功！
[19:11:00.079] [Taskbar] Minecraft 启动 已移出任务列表
[19:11:32.904] [Launch] [27684] 日志 4/5：OpenAL 已加载
[19:11:51.659] [Control] 按下按钮：启动游戏
[19:11:51.659] [Loader] 加载器 Loader Launch 状态改变：Loading
[19:11:51.666] [Launch] 预检测已通过
[19:11:51.666] [Download] 开始后台检查资源文件索引
[19:11:51.666] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[19:11:51.666] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[19:11:51.666] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[19:11:51.666] [Loader] 加载器 登录 状态改变：Waiting
[19:11:51.666] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[19:11:51.666] [Loader] 加载器 获取 Java 状态改变：Loading
[19:11:51.667] [Launch] Java 版本需求：最低 1.8.0.0，最高 1.8.999.999
[19:11:51.667] [Java] 开始完全遍历查找：D:\BEN 10\
[19:11:51.667] [Download] 无需更新资源文件索引，取消
[19:11:51.667] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Aborted
[19:11:51.667] [Loader] 加载器 后台更新资源文件索引 状态改变：Aborted
[19:11:51.667] [Loader] 加载器 后台下载资源文件索引 状态改变：Aborted
[19:11:51.667] [Download] 后台下载资源文件索引 已取消！
[19:11:51.667] [Loader] 加载器 登录 状态改变：Loading
[19:11:51.667] [Loader] 加载线程 后台分析资源文件索引地址 (41) 已中断但线程正常运行至结束，输出被弃用（最新线程：-1）
[19:11:51.667] [Loader] 加载器 补全文件 状态改变：Loading
[19:11:51.667] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[19:11:51.667] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[19:11:51.667] [Launch] 登录加载已开始
[19:11:51.667] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[19:11:51.667] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[19:11:51.667] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[19:11:51.668] [Launch] 登录方式：正版（Quasar2323）
[19:11:51.668] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[19:11:51.668] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[19:11:51.668] [Launch] 内存优化开始
[19:11:51.668] [Taskbar] Minecraft 启动 已加入任务列表
[19:11:51.669] [Test] 没有管理员权限，将以命令行方式进行内存优化
[19:11:51.670] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[19:11:51.671] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[19:11:51.671] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[19:11:51.671] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[19:11:51.690] [Java] 开始完全遍历查找：D:\BEN 10\versions\Ben10Craft\
[19:11:51.692] [Launch] 登录加载已结束
[19:11:51.692] [Loader] 加载器 登录 状态改变：Finished
[19:11:51.696] [Minecraft] 获取支持库列表：Ben10Craft
[19:11:51.697] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[19:11:51.697] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[19:11:51.697] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[19:11:51.705] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[19:11:51.705] [Loader] 加载器 下载资源文件 状态改变：Loading
[19:11:51.705] [Loader] 加载器 下载资源文件 状态改变：Finished
[19:11:51.705] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[19:11:51.718] [Java] 排序后的 Java 优先顺序：
[19:11:51.718] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[19:11:51.718] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[19:11:51.718] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[19:11:51.718] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[19:11:51.718] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[19:11:51.718] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[19:11:51.718] [Java] 最终选定的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[19:11:51.718] [Launch] 选择的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[19:11:51.719] [Loader] 加载器 获取 Java 状态改变：Finished
[19:11:51.923] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[19:11:51.923] [Loader] 加载器 下载支持库文件 状态改变：Loading
[19:11:51.923] [Loader] 加载器 下载支持库文件 状态改变：Finished
[19:11:51.923] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[19:11:51.923] [Loader] 加载器 补全文件 状态改变：Finished
[19:11:53.459] [Control] 按下图标按钮：BtnTitleMin
[19:12:04.507] [Test] 内存优化完成，可用内存改变量：2.34 G，大致剩余内存：4.16 G
[19:12:04.610] [Loader] 加载器 内存优化 状态改变：Finished
[19:12:04.612] [Loader] 加载器 获取启动参数 状态改变：Loading
[19:12:04.613] [Launch] 开始获取 Minecraft 启动参数
[19:12:04.613] [Launch] 获取旧版 JVM 参数
[19:12:04.614] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[19:12:04.614] [System] 获取资源：JavaWrapper
[19:12:04.615] [Launch] 旧版 JVM 参数获取成功：
[19:12:04.616] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp ${classpath} -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch
[19:12:04.616] [Launch] 获取旧版 Game 参数
[19:12:04.616] [Launch] 旧版 Game 参数获取成功
[19:12:04.616] [Minecraft] 获取支持库列表：Ben10Craft
[19:12:04.618] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[19:12:04.618] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[19:12:04.618] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[19:12:04.619] [Launch] Minecraft 启动参数：
[19:12:04.620] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp "D:\BEN 10\libraries\com\mojang\patchy\1.3.9\patchy-1.3.9.jar;D:\BEN 10\libraries\oshi-project\oshi-core\1.1\oshi-core-1.1.jar;D:\BEN 10\libraries\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;D:\BEN 10\libraries\net\java\dev\jna\platform\3.4.0\platform-3.4.0.jar;D:\BEN 10\libraries\com\ibm\icu\icu4j-core-mojang\51.2\icu4j-core-mojang-51.2.jar;D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar;D:\BEN 10\libraries\com\paulscode\codecjorbis\20101023\codecjorbis-20101023.jar;D:\BEN 10\libraries\com\paulscode\codecwav\20101023\codecwav-20101023.jar;D:\BEN 10\libraries\com\paulscode\libraryjavasound\20101123\libraryjavasound-20101123.jar;D:\BEN 10\libraries\com\paulscode\librarylwjglopenal\20100824\librarylwjglopenal-20100824.jar;D:\BEN 10\libraries\com\paulscode\soundsystem\20120107\soundsystem-20120107.jar;D:\BEN 10\libraries\io\netty\netty-all\4.1.9.Final\netty-all-4.1.9.Final.jar;D:\BEN 10\libraries\com\google\guava\guava\21.0\guava-21.0.jar;D:\BEN 10\libraries\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;D:\BEN 10\libraries\commons-io\commons-io\2.5\commons-io-2.5.jar;D:\BEN 10\libraries\commons-codec\commons-codec\1.10\commons-codec-1.10.jar;D:\BEN 10\libraries\net\java\jinput\jinput\2.0.5\jinput-2.0.5.jar;D:\BEN 10\libraries\net\java\jutils\jutils\1.0.0\jutils-1.0.0.jar;D:\BEN 10\libraries\com\google\code\gson\gson\2.8.0\gson-2.8.0.jar;D:\BEN 10\libraries\com\mojang\authlib\1.5.25\authlib-1.5.25.jar;D:\BEN 10\libraries\com\mojang\realms\1.10.22\realms-1.10.22.jar;D:\BEN 10\libraries\org\apache\commons\commons-compress\1.8.1\commons-compress-1.8.1.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpclient\4.3.3\httpclient-4.3.3.jar;D:\BEN 10\libraries\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpcore\4.3.2\httpcore-4.3.2.jar;D:\BEN 10\libraries\it\unimi\dsi\fastutil\7.1.0\fastutil-7.1.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl\2.9.4-nightly-20150209\lwjgl-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl_util\2.9.4-nightly-20150209\lwjgl_util-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\com\mojang\text2speech\1.10.3\text2speech-1.10.3.jar;D:\BEN 10\libraries\net\minecraftforge\forge\1.12.2-14.23.5.2860\forge-1.12.2-14.23.5.2860.jar;D:\BEN 10\libraries\org\ow2\asm\asm-debug-all\5.2\asm-debug-all-5.2.jar;D:\BEN 10\libraries\net\minecraft\launchwrapper\1.12\launchwrapper-1.12.jar;D:\BEN 10\libraries\org\jline\jline\3.5.1\jline-3.5.1.jar;D:\BEN 10\libraries\com\typesafe\akka\akka-actor_2.11\2.3.3\akka-actor_2.11-2.3.3.jar;D:\BEN 10\libraries\com\typesafe\config\1.2.1\config-1.2.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-actors-migration_2.11\1.1.0\scala-actors-migration_2.11-1.1.0.jar;D:\BEN 10\libraries\org\scala-lang\scala-compiler\2.11.1\scala-compiler-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-library_2.11\1.0.2_mc\scala-continuations-library_2.11-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-plugin_2.11.1\1.0.2_mc\scala-continuations-plugin_2.11.1-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\scala-library\2.11.1\scala-library-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-parser-combinators_2.11\1.0.1\scala-parser-combinators_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-reflect\2.11.1\scala-reflect-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-swing_2.11\1.0.1\scala-swing_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-xml_2.11\1.0.2\scala-xml_2.11-1.0.2.jar;D:\BEN 10\libraries\lzma\lzma\0.0.1\lzma-0.0.1.jar;D:\BEN 10\libraries\java3d\vecmath\1.5.2\vecmath-1.5.2.jar;D:\BEN 10\libraries\net\sf\trove4j\trove4j\3.0.3\trove4j-3.0.3.jar;D:\BEN 10\libraries\org\apache\maven\maven-artifact\3.5.3\maven-artifact-3.5.3.jar;D:\BEN 10\versions\Ben10Craft\Ben10Craft.jar" -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch --username Quasar2323 --version Ben10Craft --gameDir "D:\BEN 10\versions\Ben10Craft" --assetsDir "D:\BEN 10\assets" --assetIndex 1.12 --uuid 077e3afcb77643c48ae30f9dbb755bef --accessToken eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************XQwsg --userType msa --versionType PCL --tweakClass net.minecraftforge.fml.common.launcher.FMLTweaker --versionType Forge --height 480 --width 854 
[19:12:04.620] [Loader] 加载器 获取启动参数 状态改变：Finished
[19:12:04.627] [Loader] 加载器 解压文件 状态改变：Loading
[19:12:04.628] [Launch] 正在解压 Natives 文件
[19:12:04.632] [Loader] 加载器 解压文件 状态改变：Finished
[19:12:04.676] [Loader] 加载器 预启动处理 状态改变：Loading
[19:12:04.677] [System] 无需调整显卡设置：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[19:12:04.677] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[19:12:04.679] [Launch] 已更新 launcher_profiles.json
[19:12:04.680] [Launch] 需要的语言为 zh_cn，当前语言为 zh_cn，无需修改
[19:12:04.680] [Loader] 加载器 预启动处理 状态改变：Finished
[19:12:04.712] [Loader] 加载器 执行自定义命令 状态改变：Loading
[19:12:04.713] [Loader] 加载器 执行自定义命令 状态改变：Finished
[19:12:04.755] [Loader] 加载器 启动进程 状态改变：Loading
[19:12:04.914] [Launch] 已启动游戏进程：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[19:12:04.914] [Loader] 加载器 启动进程 状态改变：Finished
[19:12:04.957] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[19:12:04.957] [Launch] 
[19:12:04.957] [Launch] ~ 基础参数 ~
[19:12:04.957] [Launch] PCL 版本：Release 2.10.3 (361)
[19:12:04.957] [Launch] 游戏版本：1.12.2, Forge 14.23.5.2860（识别为 1.12.2）
[19:12:04.957] [Launch] 资源版本：1.12
[19:12:04.957] [Launch] 版本继承：无
[19:12:04.957] [Launch] 分配的内存：15 GB（15360 MB）
[19:12:04.957] [Launch] MC 文件夹：D:\BEN 10\
[19:12:04.957] [Launch] 版本文件夹：D:\BEN 10\versions\Ben10Craft\
[19:12:04.958] [Launch] 版本隔离：True
[19:12:04.958] [Launch] HMCL 格式：False
[19:12:04.970] [Launch] Java 信息：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[19:12:04.970] [Launch] 环境变量：未设置
[19:12:04.970] [Launch] Natives 文件夹：D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives
[19:12:04.970] [Launch] 
[19:12:04.970] [Launch] ~ 登录参数 ~
[19:12:04.970] [Launch] 玩家用户名：Quasar2323
[19:12:04.970] [Launch] AccessToken：eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************XQwsg
[19:12:04.970] [Launch] ClientToken：077e3afcb77643c48ae30f9dbb755bef
[19:12:04.970] [Launch] UUID：077e3afcb77643c48ae30f9dbb755bef
[19:12:04.970] [Launch] 登录方式：Microsoft
[19:12:04.970] [Launch] 
[19:12:04.970] [Launch] [28148] 开始 Minecraft 日志监控
[19:12:06.202] [Launch] [28148] 日志 1/5：已出现日志输出
[19:12:08.722] [Launch] [27684] 日志 5/5：材质已加载
[19:12:10.411] [Launch] [28148] 日志 2/5：游戏用户已设置
[19:12:13.561] [Launch] [28148] Minecraft 窗口已加载：（332290）
[19:12:13.561] [Launch] [28148] 日志 3/5：LWJGL 版本已确认
[19:12:13.561] [Launch] [28148] Minecraft 加载已完成
[19:12:13.572] [Loader] 加载器 等待游戏窗口出现 状态改变：Finished
[19:12:13.623] [Loader] 加载器 结束处理 状态改变：Loading
[19:12:13.624] [Launch] 开始启动结束处理
[19:12:13.624] [Launch] 启动器可见性：5
[19:12:13.628] [Loader] 加载器 结束处理 状态改变：Finished
[19:12:13.679] [Loader] 加载器 Minecraft 启动 状态改变：Finished
[19:12:13.742] [Taskbar] Minecraft 启动 已移出任务列表
[19:12:13.758] [Loader] 加载器 Loader Launch 状态改变：Finished
[19:12:13.807] [UI] 弹出提示：Ben10Craft 启动成功！
[19:12:25.058] [Launch] [27684] Minecraft 已退出，返回值：-805306369
[19:12:25.058] [Launch] [27684] Minecraft 返回值异常，可能已崩溃
[19:12:25.059] [Launch] [27684] Minecraft 已崩溃，将在 2 秒后开始崩溃分析
[19:12:25.122] [UI] 弹出提示：检测到 Minecraft 出现错误，错误分析已开始……
[19:12:25.483] [System] 诊断信息：
操作系统：Microsoft Windows 11 家庭中文版（32 位：False）
剩余内存：1565 M / 16108 M
DPI：144（150%）
MC 文件夹：D:\BEN 10\
文件位置：C:\Users\<USER>\Desktop\
[19:12:25.712] [Launch] [27684] Minecraft 日志监控已退出
[19:12:27.495] [Launch] [27684] 崩溃分析开始
[19:12:27.502] [Crash] 崩溃分析暂存文件夹：C:\Users\<USER>\AppData\Local\Temp\PCL\TaskTemp\3760-448490\
[19:12:27.507] [Crash] 步骤 1：收集日志文件
[19:12:27.510] [Crash] 可能可用的日志文件：D:\BEN 10\versions\Ben10Craft\crafttweaker.log（0.1 分钟）
[19:12:27.510] [Crash] 可能可用的日志文件：D:\BEN 10\versions\Ben10Craft\logs\latest.log（0 分钟）
[19:12:27.510] [Crash] 可能可用的日志文件：D:\BEN 10\versions\Ben10Craft\logs\debug.log（0 分钟）
[19:12:27.562] [Crash] 以下为游戏输出的最后一段内容：
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/snare_hand_7.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:19] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/snare_hand_8 for model marsh_10_addon:models/item/snare
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/snare_hand_8 with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/snare_hand_8.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:19] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/snare_hand_9 for model marsh_10_addon:models/item/snare
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/snare_hand_9 with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/snare_hand_9.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:19] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/snare_hand_3 for model marsh_10_addon:models/item/snare
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/snare_hand_3 with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/snare_hand_3.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:19] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/snare_hand_4 for model marsh_10_addon:models/item/snare
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/snare_hand_4 with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/snare_hand_4.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:19] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/snare_hand_5 for model marsh_10_addon:models/item/snare
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/snare_hand_5 with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/snare_hand_5.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:19] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/snare_hand_6 for model marsh_10_addon:models/item/snare
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/snare_hand_6 with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/snare_hand_6.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:19] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/crypto_shovel_ult for model minecraft:models/item/diamond_shovel
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/crypto_shovel_ult with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/crypto_shovel_ult.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:19] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/wildvine/walk_2 for model minecraft:models/item/diamond_shovel
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/wildvine/walk_2 with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/wildvine/walk_2.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:21] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/crypto_shovel_ult for model minecraft:models/item/diamond_sword
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/crypto_shovel_ult with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/crypto_shovel_ult.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:21] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/wildvine/walk_2 for model minecraft:models/item/diamond_sword
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/wildvine/walk_2 with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/wildvine/walk_2.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:21] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/staff_alts/force_field_2 for model marsh_10_addon:models/item/snare_hand
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/staff_alts/force_field_2 with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/staff_alts/force_field_2.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:21] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/hover_bar for model marsh_10_addon:models/item/snare_hand
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/hover_bar with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/hover_bar.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:22] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/skurd2 for model minecraft:models/item/iron_axe
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/skurd2 with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/skurd2.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:22] [Client thread/ERROR] [FML]: Could not load override model minecraft:item/skurdice for model minecraft:models/item/iron_axe
net.minecraftforge.client.model.ModelLoaderRegistry$LoaderException: Exception loading model minecraft:item/skurdice with loader VanillaLoader.INSTANCE, skipping
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:161) ~[ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModelOrLogError(ModelLoaderRegistry.java:211) [ModelLoaderRegistry.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaModelWrapper.getDependencies(ModelLoader.java:364) [ModelLoader$VanillaModelWrapper.class:?]
	at team.chisel.ctm.client.util.TextureMetadataHandler.onModelBake(TextureMetadataHandler.java:149) [TextureMetadataHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler_1093_TextureMetadataHandler_onModelBake_ModelBakeEvent.invoke(.dynamic) [?:?]
	at net.minecraftforge.fml.common.eventhandler.ASMEventHandler.invoke(ASMEventHandler.java:90) [ASMEventHandler.class:?]
	at net.minecraftforge.fml.common.eventhandler.EventBus.post(EventBus.java:182) [EventBus.class:?]
	at net.minecraftforge.client.ForgeHooksClient.onModelBake(ForgeHooksClient.java:414) [ForgeHooksClient.class:?]
	at net.minecraft.client.renderer.block.model.ModelManager.func_110549_a(ModelManager.java:30) [cgc.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110542_a(SimpleReloadableResourceManager.java:121) [cev.class:?]
	at net.minecraft.client.Minecraft.func_71384_a(Minecraft.java:513) [bib.class:?]
	at net.minecraft.client.Minecraft.func_99999_d(Minecraft.java:378) [bib.class:?]
	at net.minecraft.client.main.Main.main(SourceFile:123) [Main.class:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at net.minecraft.launchwrapper.Launch.launch(Launch.java:135) [launchwrapper-1.12.jar:?]
	at net.minecraft.launchwrapper.Launch.main(Launch.java:28) [launchwrapper-1.12.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:503) ~[?:1.8.0_462]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) [JavaWrapper.jar:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) [JavaWrapper.jar:?]
Caused by: java.io.FileNotFoundException: minecraft:models/item/skurdice.json
	at net.minecraft.client.resources.FallbackResourceManager.func_110536_a(FallbackResourceManager.java:69) ~[cei.class:?]
	at net.minecraft.client.resources.SimpleReloadableResourceManager.func_110536_a(SimpleReloadableResourceManager.java:65) ~[cev.class:?]
	at net.minecraft.client.renderer.block.model.ModelBakery.func_177594_c(ModelBakery.java:365) ~[cgb.class:?]
	at net.minecraftforge.client.model.ModelLoader.access$1400(ModelLoader.java:115) ~[ModelLoader.class:?]
	at net.minecraftforge.client.model.ModelLoader$VanillaLoader.loadModel(ModelLoader.java:861) ~[ModelLoader$VanillaLoader.class:?]
	at net.minecraftforge.client.model.ModelLoaderRegistry.getModel(ModelLoaderRegistry.java:157) ~[ModelLoaderRegistry.class:?]
	... 24 more
[19:12:27.564] [Crash] 步骤 1：收集日志文件完成，收集到 4 个文件
[19:12:27.565] [Crash] 步骤 2：准备日志文本
[19:12:27.565] [Crash] crafttweaker.log 分类为 ExtraLogFile
[19:12:27.565] [Crash] latest.log 分类为 MinecraftLog
[19:12:27.565] [Crash] debug.log 分类为 MinecraftLog
[19:12:27.565] [Crash] rawoutput.log 分类为 MinecraftLog
[19:12:27.567] [Crash] 输出报告：D:\BEN 10\versions\Ben10Craft\logs\latest.log，作为 Minecraft 或启动器日志
[19:12:27.567] [Crash] 输出报告：D:\BEN 10\versions\Ben10Craft\logs\debug.log，作为 Minecraft 或启动器日志
[19:12:27.567] [Crash] 输出报告：C:\Users\<USER>\AppData\Local\Temp\PCL\TaskTemp\3760-448490\RawOutput.log，作为 Minecraft 或启动器日志
[19:12:27.568] [Crash] 导入分析：C:\Users\<USER>\AppData\Local\Temp\PCL\TaskTemp\3760-448490\RawOutput.log，作为启动器日志
[19:12:27.578] [Crash] 导入分析：D:\BEN 10\versions\Ben10Craft\logs\latest.log，作为 Minecraft 日志
[19:12:27.582] [Crash] 导入分析：D:\BEN 10\versions\Ben10Craft\logs\debug.log，作为 Minecraft Debug 日志
[19:12:27.582] [Crash] 输出报告：D:\BEN 10\versions\Ben10Craft\crafttweaker.log，不用作分析
[19:12:27.582] [Crash] 步骤 2：准备日志文本完成，找到游戏日志、游戏 Debug 日志用作分析
[19:12:27.582] [Crash] 步骤 3：分析崩溃原因
[19:12:27.620] [Crash] 开始进行 Minecraft 日志堆栈分析，发现 0 个报错项
[19:12:27.632] [Crash] 步骤 3：分析崩溃原因完成，未找到可能的原因
[19:12:27.699] [System] 窗口已置顶，位置：(692, 254.666666666667), 900 x 556
[19:12:27.745] [Control] 普通弹窗：Minecraft 出现错误
很抱歉，你的游戏出现了一些问题……
如果要寻求帮助，请把错误报告文件发给对方，而不是发送这个窗口的照片或者截图。
[19:12:52.123] [Launch] [28148] 日志 4/5：OpenAL 已加载
[19:13:25.108] [Launch] [28148] 日志 5/5：材质已加载
[19:16:30.513] [Launch] [28148] Minecraft 已退出，返回值：0
[19:16:30.515] [Launch] [全局] 已无运行中的 Minecraft
[19:16:30.743] [Launch] [28148] Minecraft 日志监控已退出
[19:20:47.536] [Control] 按下按钮：确定
[19:20:47.537] [Control] 普通弹框返回：1
[19:34:57.324] [Animation] 两个动画帧间隔 735 ms
[19:35:17.142] [Animation] 两个动画帧间隔 250 ms
[19:41:30.279] [Control] 按下按钮：启动游戏
[19:41:30.280] [Loader] 加载器 Loader Launch 状态改变：Loading
[19:41:30.288] [Launch] 预检测已通过
[19:41:30.288] [Download] 开始后台检查资源文件索引
[19:41:30.288] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[19:41:30.288] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[19:41:30.288] [Loader] 加载器 登录 状态改变：Waiting
[19:41:30.288] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[19:41:30.289] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[19:41:30.289] [Loader] 加载器 获取 Java 状态改变：Loading
[19:41:30.289] [Launch] Java 版本需求：最低 1.8.0.0，最高 1.8.999.999
[19:41:30.289] [Java] 开始完全遍历查找：D:\BEN 10\
[19:41:30.289] [Download] 无需更新资源文件索引，取消
[19:41:30.289] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Aborted
[19:41:30.289] [Loader] 加载器 后台更新资源文件索引 状态改变：Aborted
[19:41:30.289] [Loader] 加载器 后台下载资源文件索引 状态改变：Aborted
[19:41:30.289] [Download] 后台下载资源文件索引 已取消！
[19:41:30.289] [Loader] 加载线程 后台分析资源文件索引地址 (149) 已中断但线程正常运行至结束，输出被弃用（最新线程：-1）
[19:41:30.289] [Loader] 加载器 登录 状态改变：Loading
[19:41:30.289] [Loader] 加载器 补全文件 状态改变：Loading
[19:41:30.289] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[19:41:30.289] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[19:41:30.289] [Launch] 登录加载已开始
[19:41:30.289] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[19:41:30.290] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[19:41:30.290] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[19:41:30.290] [Launch] 登录方式：正版（Quasar2323）
[19:41:30.290] [Launch] 开始微软登录步骤 1/6（刷新登录）
[19:41:30.290] [Loader] 加载器 内存优化 状态改变：Loading
[19:41:30.290] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[19:41:30.290] [Download] 版本 Ben10Craft 对应的资源文件索引为 1.12
[19:41:30.290] [Launch] 内存优化开始
[19:41:30.290] [Taskbar] Minecraft 启动 已加入任务列表
[19:41:30.290] [Test] 没有管理员权限，将以命令行方式进行内存优化
[19:41:30.301] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[19:41:30.301] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[19:41:30.302] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[19:41:30.302] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[19:41:30.304] [Java] 开始完全遍历查找：D:\BEN 10\versions\Ben10Craft\
[19:41:30.309] [Java] 排序后的 Java 优先顺序：
[19:41:30.310] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[19:41:30.310] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[19:41:30.310] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[19:41:30.310] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[19:41:30.310] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[19:41:30.310] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[19:41:30.310] [Java] 最终选定的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[19:41:30.310] [Launch] 选择的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[19:41:30.310] [Loader] 加载器 获取 Java 状态改变：Finished
[19:41:30.312] [Loader] 由于输入条件变更，重启进行中的加载器 登录
[19:41:30.312] [Launch] 登录加载已开始
[19:41:30.312] [Launch] 登录方式：正版（Quasar2323）
[19:41:30.312] [Launch] 开始微软登录步骤 1/6（刷新登录）
[19:41:30.312] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[19:41:30.319] [Minecraft] 获取支持库列表：Ben10Craft
[19:41:30.319] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[19:41:30.319] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[19:41:30.319] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[19:41:30.347] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[19:41:30.347] [Loader] 加载器 下载资源文件 状态改变：Loading
[19:41:30.348] [Loader] 加载器 下载资源文件 状态改变：Finished
[19:41:30.348] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[19:41:30.524] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[19:41:30.525] [Loader] 加载器 下载支持库文件 状态改变：Loading
[19:41:30.525] [Loader] 加载器 下载支持库文件 状态改变：Finished
[19:41:30.525] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[19:41:30.525] [Loader] 加载器 补全文件 状态改变：Finished
[19:41:30.568] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[19:41:30.602] [Report] FPS 0, 动画 5, 下载中 0（0 B/s）
[19:41:31.813] [Launch] 开始微软登录步骤 2/6
[19:41:31.815] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[19:41:32.073] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[19:41:32.581] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[19:41:33.333] [Launch] 开始微软登录步骤 3/6
[19:41:33.333] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[19:41:33.593] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[19:41:34.107] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[19:41:34.874] [Launch] 开始微软登录步骤 4/6
[19:41:34.874] [Net] 发起网络请求（POST，https://api.minecraftservices.com/authentication/login_with_xbox），最大超时 15000
[19:41:36.052] [Launch] 开始微软登录步骤 5/6
[19:41:36.053] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[19:41:36.316] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[19:41:36.827] [Launch] 开始微软登录步骤 6/6
[19:41:36.827] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[19:41:37.089] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[19:41:37.598] [Launch] 微软登录完成
[19:41:37.598] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[19:41:37.613] [Launch] 登录加载已结束
[19:41:37.613] [Loader] 加载器 登录 状态改变：Finished
[19:41:37.618] [Loader] 由于输入条件变更，重启已完成的加载器 登录
[19:41:37.619] [Loader] 加载器 登录 状态改变：Loading
[19:41:37.619] [Launch] 登录加载已开始
[19:41:37.619] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[19:41:37.619] [Launch] 登录方式：正版（Quasar2323）
[19:41:37.619] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[19:41:37.643] [Launch] 登录加载已结束
[19:41:37.643] [Loader] 加载器 登录 状态改变：Finished
[19:41:42.177] [Test] 内存优化完成，可用内存改变量：1.89 G，大致剩余内存：6.11 G
[19:41:42.179] [Loader] 加载器 内存优化 状态改变：Finished
[19:41:42.179] [Loader] 加载器 获取启动参数 状态改变：Loading
[19:41:42.180] [Launch] 开始获取 Minecraft 启动参数
[19:41:42.180] [Launch] 获取旧版 JVM 参数
[19:41:42.181] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[19:41:42.181] [System] 获取资源：JavaWrapper
[19:41:42.182] [Launch] 旧版 JVM 参数获取成功：
[19:41:42.183] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp ${classpath} -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch
[19:41:42.183] [Launch] 获取旧版 Game 参数
[19:41:42.183] [Launch] 旧版 Game 参数获取成功
[19:41:42.183] [Minecraft] 获取支持库列表：Ben10Craft
[19:41:42.183] [Minecraft] 发现重复的支持库：76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3) 与 76.3 K | D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[19:41:42.183] [Minecraft] 发现重复的支持库：295 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[19:41:42.183] [Minecraft] 发现重复的支持库：1.71 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[19:41:42.186] [Launch] Minecraft 启动参数：
[19:41:42.188] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp "D:\BEN 10\libraries\com\mojang\patchy\1.3.9\patchy-1.3.9.jar;D:\BEN 10\libraries\oshi-project\oshi-core\1.1\oshi-core-1.1.jar;D:\BEN 10\libraries\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;D:\BEN 10\libraries\net\java\dev\jna\platform\3.4.0\platform-3.4.0.jar;D:\BEN 10\libraries\com\ibm\icu\icu4j-core-mojang\51.2\icu4j-core-mojang-51.2.jar;D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar;D:\BEN 10\libraries\com\paulscode\codecjorbis\20101023\codecjorbis-20101023.jar;D:\BEN 10\libraries\com\paulscode\codecwav\20101023\codecwav-20101023.jar;D:\BEN 10\libraries\com\paulscode\libraryjavasound\20101123\libraryjavasound-20101123.jar;D:\BEN 10\libraries\com\paulscode\librarylwjglopenal\20100824\librarylwjglopenal-20100824.jar;D:\BEN 10\libraries\com\paulscode\soundsystem\20120107\soundsystem-20120107.jar;D:\BEN 10\libraries\io\netty\netty-all\4.1.9.Final\netty-all-4.1.9.Final.jar;D:\BEN 10\libraries\com\google\guava\guava\21.0\guava-21.0.jar;D:\BEN 10\libraries\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;D:\BEN 10\libraries\commons-io\commons-io\2.5\commons-io-2.5.jar;D:\BEN 10\libraries\commons-codec\commons-codec\1.10\commons-codec-1.10.jar;D:\BEN 10\libraries\net\java\jinput\jinput\2.0.5\jinput-2.0.5.jar;D:\BEN 10\libraries\net\java\jutils\jutils\1.0.0\jutils-1.0.0.jar;D:\BEN 10\libraries\com\google\code\gson\gson\2.8.0\gson-2.8.0.jar;D:\BEN 10\libraries\com\mojang\authlib\1.5.25\authlib-1.5.25.jar;D:\BEN 10\libraries\com\mojang\realms\1.10.22\realms-1.10.22.jar;D:\BEN 10\libraries\org\apache\commons\commons-compress\1.8.1\commons-compress-1.8.1.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpclient\4.3.3\httpclient-4.3.3.jar;D:\BEN 10\libraries\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpcore\4.3.2\httpcore-4.3.2.jar;D:\BEN 10\libraries\it\unimi\dsi\fastutil\7.1.0\fastutil-7.1.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl\2.9.4-nightly-20150209\lwjgl-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl_util\2.9.4-nightly-20150209\lwjgl_util-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\com\mojang\text2speech\1.10.3\text2speech-1.10.3.jar;D:\BEN 10\libraries\net\minecraftforge\forge\1.12.2-14.23.5.2860\forge-1.12.2-14.23.5.2860.jar;D:\BEN 10\libraries\org\ow2\asm\asm-debug-all\5.2\asm-debug-all-5.2.jar;D:\BEN 10\libraries\net\minecraft\launchwrapper\1.12\launchwrapper-1.12.jar;D:\BEN 10\libraries\org\jline\jline\3.5.1\jline-3.5.1.jar;D:\BEN 10\libraries\com\typesafe\akka\akka-actor_2.11\2.3.3\akka-actor_2.11-2.3.3.jar;D:\BEN 10\libraries\com\typesafe\config\1.2.1\config-1.2.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-actors-migration_2.11\1.1.0\scala-actors-migration_2.11-1.1.0.jar;D:\BEN 10\libraries\org\scala-lang\scala-compiler\2.11.1\scala-compiler-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-library_2.11\1.0.2_mc\scala-continuations-library_2.11-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-plugin_2.11.1\1.0.2_mc\scala-continuations-plugin_2.11.1-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\scala-library\2.11.1\scala-library-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-parser-combinators_2.11\1.0.1\scala-parser-combinators_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-reflect\2.11.1\scala-reflect-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-swing_2.11\1.0.1\scala-swing_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-xml_2.11\1.0.2\scala-xml_2.11-1.0.2.jar;D:\BEN 10\libraries\lzma\lzma\0.0.1\lzma-0.0.1.jar;D:\BEN 10\libraries\java3d\vecmath\1.5.2\vecmath-1.5.2.jar;D:\BEN 10\libraries\net\sf\trove4j\trove4j\3.0.3\trove4j-3.0.3.jar;D:\BEN 10\libraries\org\apache\maven\maven-artifact\3.5.3\maven-artifact-3.5.3.jar;D:\BEN 10\versions\Ben10Craft\Ben10Craft.jar" -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch --username Quasar2323 --version Ben10Craft --gameDir "D:\BEN 10\versions\Ben10Craft" --assetsDir "D:\BEN 10\assets" --assetIndex 1.12 --uuid 077e3afcb77643c48ae30f9dbb755bef --accessToken eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************RxweQ --userType msa --versionType PCL --tweakClass net.minecraftforge.fml.common.launcher.FMLTweaker --versionType Forge --height 480 --width 854 
[19:41:42.188] [Loader] 加载器 获取启动参数 状态改变：Finished
[19:41:42.190] [Loader] 加载器 解压文件 状态改变：Loading
[19:41:42.190] [Launch] 正在解压 Natives 文件
[19:41:42.194] [Loader] 加载器 解压文件 状态改变：Finished
[19:41:42.221] [Loader] 加载器 预启动处理 状态改变：Loading
[19:41:42.222] [System] 无需调整显卡设置：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[19:41:42.222] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[19:41:42.223] [Launch] 已更新 launcher_profiles.json
[19:41:42.224] [Launch] 需要的语言为 zh_cn，当前语言为 zh_cn，无需修改
[19:41:42.224] [Loader] 加载器 预启动处理 状态改变：Finished
[19:41:42.245] [Loader] 加载器 执行自定义命令 状态改变：Loading
[19:41:42.247] [Loader] 加载器 执行自定义命令 状态改变：Finished
[19:41:42.286] [Loader] 加载器 启动进程 状态改变：Loading
[19:41:42.448] [Launch] 已启动游戏进程：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[19:41:42.448] [Loader] 加载器 启动进程 状态改变：Finished
[19:41:42.490] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[19:41:42.490] [Launch] 
[19:41:42.505] [Launch] ~ 基础参数 ~
[19:41:42.505] [Launch] PCL 版本：Release 2.10.3 (361)
[19:41:42.505] [Launch] 游戏版本：1.12.2, Forge 14.23.5.2860（识别为 1.12.2）
[19:41:42.505] [Launch] 资源版本：1.12
[19:41:42.505] [Launch] 版本继承：无
[19:41:42.505] [Launch] 分配的内存：15 GB（15360 MB）
[19:41:42.505] [Launch] MC 文件夹：D:\BEN 10\
[19:41:42.505] [Launch] 版本文件夹：D:\BEN 10\versions\Ben10Craft\
[19:41:42.505] [Launch] 版本隔离：True
[19:41:42.505] [Launch] HMCL 格式：False
[19:41:42.505] [Launch] Java 信息：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[19:41:42.505] [Launch] 环境变量：未设置
[19:41:42.505] [Launch] Natives 文件夹：D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives
[19:41:42.505] [Launch] 
[19:41:42.505] [Launch] ~ 登录参数 ~
[19:41:42.505] [Launch] 玩家用户名：Quasar2323
[19:41:42.505] [Launch] AccessToken：eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************RxweQ
[19:41:42.505] [Launch] ClientToken：077e3afcb77643c48ae30f9dbb755bef
[19:41:42.505] [Launch] UUID：077e3afcb77643c48ae30f9dbb755bef
[19:41:42.505] [Launch] 登录方式：Microsoft
[19:41:42.505] [Launch] 
[19:41:42.510] [Launch] [24712] 开始 Minecraft 日志监控
[19:41:42.510] [Launch] [全局] 出现运行中的 Minecraft
[19:41:43.671] [Launch] [24712] 日志 1/5：已出现日志输出
[19:41:46.945] [Launch] [24712] 日志 2/5：游戏用户已设置
[19:41:49.143] [Launch] [24712] 日志 3/5：LWJGL 版本已确认
[19:41:49.377] [Launch] [24712] Minecraft 窗口已加载：（3017146）
[19:41:50.064] [Launch] [24712] Minecraft 加载已完成
[19:41:50.094] [Loader] 加载器 等待游戏窗口出现 状态改变：Finished
[19:41:50.118] [Loader] 加载器 结束处理 状态改变：Loading
[19:41:50.119] [Launch] 开始启动结束处理
[19:41:50.119] [Launch] 启动器可见性：5
[19:41:50.119] [Loader] 加载器 结束处理 状态改变：Finished
[19:41:50.168] [Loader] 加载器 Minecraft 启动 状态改变：Finished
[19:41:50.171] [Taskbar] Minecraft 启动 已移出任务列表
[19:41:50.374] [Loader] 加载器 Loader Launch 状态改变：Finished
[19:41:50.422] [UI] 弹出提示：Ben10Craft 启动成功！
[19:42:18.320] [Launch] [24712] 日志 4/5：OpenAL 已加载
[19:42:40.973] [Animation] 两个动画帧间隔 219 ms
[19:42:54.795] [Launch] [24712] 日志 5/5：材质已加载
[19:49:20.534] [Animation] 两个动画帧间隔 219 ms
[19:49:21.470] [Launch] [24712] Minecraft 已退出，返回值：1073807364
[19:49:21.470] [Launch] [24712] Minecraft 返回值异常，可能已崩溃
[19:49:21.470] [Launch] [全局] 已无运行中的 Minecraft
[19:49:21.471] [Launch] [24712] Minecraft 已崩溃，将在 2 秒后开始崩溃分析
[19:49:21.581] [UI] 弹出提示：检测到 Minecraft 出现错误，错误分析已开始……
[19:49:21.979] [System] 诊断信息：
操作系统：Microsoft Windows 11 家庭中文版（32 位：False）
剩余内存：870 M / 16108 M
DPI：144（150%）
MC 文件夹：D:\BEN 10\
文件位置：C:\Users\<USER>\Desktop\
[19:49:22.211] [Launch] [24712] Minecraft 日志监控已退出
[19:49:23.994] [Launch] [24712] 崩溃分析开始
[19:49:24.001] [Crash] 崩溃分析暂存文件夹：C:\Users\<USER>\AppData\Local\Temp\PCL\TaskTemp\3977-557253\
[19:49:24.001] [Crash] 步骤 1：收集日志文件
[19:49:24.004] [Crash] 可能可用的日志文件：D:\BEN 10\versions\Ben10Craft\logs\latest.log（0 分钟）
[19:49:24.004] [Crash] 可能可用的日志文件：D:\BEN 10\versions\Ben10Craft\logs\debug.log（0 分钟）
[19:49:24.207] [Crash] 以下为游戏输出的最后一段内容：
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:17] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:18] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:19] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:20] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑void_damage锛圛D 120锛?4鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鎶楁€ф彁鍗囷紙ID 11锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑闃茬伀锛圛D 12锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑姘翠笅鍛煎惛锛圛D 13锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑澶滆锛圛D 16锛?255鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑鐢熷懡鎭㈠锛圛D 10锛?2鏁堟灉]
[19:49:21] [Server thread/INFO] [minecraft/MinecraftServer]: [Command Ability: 缁欎簣Quasar2323鏃堕暱涓?绉掔殑浼ゅ鍚告敹锛圛D 22锛?5鏁堟灉]
[19:49:24.209] [Crash] 步骤 1：收集日志文件完成，收集到 3 个文件
[19:49:24.209] [Crash] 步骤 2：准备日志文本
[19:49:24.209] [Crash] latest.log 分类为 MinecraftLog
[19:49:24.209] [Crash] debug.log 分类为 MinecraftLog
[19:49:24.209] [Crash] rawoutput.log 分类为 MinecraftLog
[19:49:24.210] [Crash] 输出报告：D:\BEN 10\versions\Ben10Craft\logs\latest.log，作为 Minecraft 或启动器日志
[19:49:24.210] [Crash] 输出报告：D:\BEN 10\versions\Ben10Craft\logs\debug.log，作为 Minecraft 或启动器日志
[19:49:24.210] [Crash] 输出报告：C:\Users\<USER>\AppData\Local\Temp\PCL\TaskTemp\3977-557253\RawOutput.log，作为 Minecraft 或启动器日志
[19:49:24.210] [Crash] 导入分析：C:\Users\<USER>\AppData\Local\Temp\PCL\TaskTemp\3977-557253\RawOutput.log，作为启动器日志
[19:49:24.322] [Crash] 导入分析：D:\BEN 10\versions\Ben10Craft\logs\latest.log，作为 Minecraft 日志
[19:49:24.325] [Crash] 导入分析：D:\BEN 10\versions\Ben10Craft\logs\debug.log，作为 Minecraft Debug 日志
[19:49:24.325] [Crash] 步骤 2：准备日志文本完成，找到游戏日志、游戏 Debug 日志用作分析
[19:49:24.325] [Crash] 步骤 3：分析崩溃原因
[19:49:24.335] [Crash] 开始进行 Minecraft 日志堆栈分析，发现 0 个报错项
[19:49:24.335] [Crash] 步骤 3：分析崩溃原因完成，未找到可能的原因
[19:49:24.341] [System] 窗口已置顶，位置：(692, 254.666666666667), 900 x 556
[19:49:24.382] [Control] 普通弹窗：Minecraft 出现错误
很抱歉，你的游戏出现了一些问题……
如果要寻求帮助，请把错误报告文件发给对方，而不是发送这个窗口的照片或者截图。
[19:49:25.977] [System] 收到关闭指令
[19:49:25.997] [System] 程序已退出，返回值：Success
