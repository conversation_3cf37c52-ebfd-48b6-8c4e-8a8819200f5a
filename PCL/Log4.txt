[02:15:33.447] [Start] 程序版本：Release 2.10.3 (361)
[02:15:33.447] [Start] 识别码：93DA-DA21-65DF-59A3，已解锁反馈主题
[02:15:33.447] [Start] 程序路径：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[02:15:33.447] [Start] 系统编码：gb2312 (936, GBK=True)
[02:15:33.447] [Start] 管理员权限：False
[02:15:33.452] [Start] 第一阶段加载用时：78 ms
[02:15:33.634] [UI] 刷新主题：0
[02:15:33.664] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[02:15:33.664] [Start] 第二阶段加载用时：203 ms
[02:15:33.675] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[02:15:34.105] [System] 窗口已置顶，位置：(403.333333333333, 231.333333333333), 900 x 556
[02:15:34.112] [Animation] 动画线程开始
[02:15:34.112] [Start] 第三阶段加载用时：62 ms
[02:15:34.114] [Start] 加载 DLL：Json
[02:15:34.114] [System] 获取资源：Json
[02:15:34.127] [Launch] Minecraft 文件夹：D:\特摄\
[02:15:34.172] [Loader] 加载器 Loader Skin Ms 状态改变：Loading
[02:15:34.180] [Net] 获取网络结果：https://sessionserver.mojang.com/session/minecraft/profile/077e3afcb77643c48ae30f9dbb755bef，超时 10000ms
[02:15:34.180] [Java] 缓存中有 10 个可用的 Java：
[02:15:34.180] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[02:15:34.180] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[02:15:34.180] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[02:15:34.180] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[02:15:34.180] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[02:15:34.180] [Java]  - JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[02:15:34.180] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[02:15:34.180] [Java]  - JDK 21 (21.0.8)：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\
[02:15:34.180] [Java]  - JDK 21 (21.0.5)：C:\Program Files\Java\jdk-21\bin\
[02:15:34.180] [Java]  - JDK 11 (11.0.24)：C:\Program Files\Java\jdk-11\bin\
[02:15:34.216] [Page] 实例化：清空主页 UI，来源为空
[02:15:34.218] [Minecraft] 启动按钮：Minecraft 版本：D:\特摄\versions\MARSHY'S SECRETS OF THE OMNITRIX - BETA PALLADIUM MODPACK\
[02:15:34.221] [Loader] 加载器 登录 状态改变：Loading
[02:15:34.223] [Launch] 登录加载已开始
[02:15:34.224] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[02:15:34.225] [Launch] 登录方式：正版（Quasar2323）
[02:15:34.225] [Launch] 开始微软登录步骤 1/6（刷新登录）
[02:15:34.230] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[02:15:34.285] [Loader] 加载器 DlClientList Mojang 状态改变：Loading
[02:15:34.287] [Loader] 加载器 PCL 服务 状态改变：Loading
[02:15:34.287] [Server] 正在连接到 PCL 服务器
[02:15:34.288] [System] 开始清理任务缓存文件夹
[02:15:34.289] [Net] 获取网络结果：https://launchermeta.mojang.com/mc/game/version_manifest.json，超时 10000ms，要求 json
[02:15:34.297] [Net] 获取网络结果：https://pcl2-server-1253424809.file.myqcloud.com/notice.cfg?sign=1753812934-6644cf60-0-18f41b0cdd751d12500446a564545a9f，超时 10000ms
[02:15:34.300] [System] 已清理任务缓存文件夹
[02:15:34.480] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[02:15:34.659] [Server] 服务器公告：171|364|361|5，无需更新
[02:15:34.660] [Server] 复制自身为最新正式版：版本号升高到 361
[02:15:34.673] [Loader] 加载器 源码获取 85# 状态改变：Loading
[02:15:34.722] [Download] download.txt 88#：开始，起始点 0，https://pcl2-server-1253424809.file.myqcloud.com/minecraft/download.json?sign=1753812935-64d8ea39-0-ae3c8347aacf137c577c326aad8384da
[02:15:34.744] [Download] download.txt 88#：文件大小 3906（3.81 K）
[02:15:34.751] [Download] download.txt：已完成，剩余文件 0
[02:15:34.751] [Loader] 加载器 源码获取 85# 状态改变：Finished
[02:15:34.759] [Net] 获取网络结果：https://pcl2-server-1253424809.file.myqcloud.com/notice.json?sign=1753812935-7212d54b-0-08c100371c71deac1618376aab599211，超时 10000ms
[02:15:34.787] [Loader] 加载器 PCL 服务 状态改变：Finished
[02:15:34.834] [System] DPI：144，系统版本：Microsoft Windows NT 10.0.22631.0，PCL 位置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[02:15:34.983] [Skin] UUID 077e3afcb77643c48ae30f9dbb755bef 对应的皮肤文件为 https://textures.minecraft.net/texture/27be4c8f5515e06ac7dcc835b13bd0232994f0377a22e4a8fc4eae740807b8f3
[02:15:34.983] [Net] 直接下载文件：https://textures.minecraft.net/texture/27be4c8f5515e06ac7dcc835b13bd0232994f0377a22e4a8fc4eae740807b8f3
[02:15:35.185] [Download] Mojang 官方源加载耗时：906ms，可优先使用官方源
[02:15:35.188] [Loader] 加载器 DlClientList Mojang 状态改变：Finished
[02:15:35.756] [Launch] 开始微软登录步骤 2/6
[02:15:35.757] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[02:15:35.981] [Minecraft] 皮肤下载成功：C:\Users\<USER>\AppData\Local\Temp\PCL\Cache\Skin\2905352495719792759.png
[02:15:35.981] [Loader] 加载器 Loader Skin Ms 状态改变：Finished
[02:15:35.992] [Skin] 载入头像成功：Loader Skin Ms
[02:15:36.016] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[02:15:36.365] [Control] 按下按钮：版本选择
[02:15:36.380] [Control] 切换主要页面：VersionSelect, 0
[02:15:36.522] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[02:15:36.532] [Minecraft] 有效的 Minecraft 文件夹：modding > C:\Users\<USER>\Desktop\.minecraft\
[02:15:36.533] [Minecraft] 有效的 Minecraft 文件夹：官方启动器文件夹 > C:\Users\<USER>\AppData\Roaming\.minecraft\
[02:15:36.542] [Minecraft] 有效的 Minecraft 文件夹：(导入包)奥特英雄激战重奏 > D:\(导入包)奥特英雄激战重奏\.minecraft\
[02:15:36.544] [Minecraft] 有效的 Minecraft 文件夹：ben10 > D:\ben10\
[02:15:36.545] [Minecraft] 有效的 Minecraft 文件夹：BEN 10 > D:\BEN 10\
[02:15:36.546] [Minecraft] 有效的 Minecraft 文件夹：寰宇特摄 > D:\寰宇特摄\
[02:15:36.548] [Minecraft] 有效的 Minecraft 文件夹：paBEN10 > D:\paBEN10\
[02:15:36.549] [Minecraft] 有效的 Minecraft 文件夹：特摄 > D:\特摄\
[02:15:36.560] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[02:15:36.575] [Setup] 当前选择的 Minecraft 版本：MARSHY'S SECRETS OF THE OMNITRIX - BETA PALLADIUM MODPACK
[02:15:36.575] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\特摄\versions\MARSHY'S SECRETS OF THE OMNITRIX - BETA PALLADIUM MODPACK\
[02:15:36.631] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[02:15:37.283] [Launch] 开始微软登录步骤 3/6
[02:15:37.283] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[02:15:37.545] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[02:15:37.953] [Setup] 当前选择的 Minecraft 版本：1.16.5-Forge_36.2.42
[02:15:37.954] [Control] 切换主要页面：Launch, -1
[02:15:37.954] [Control] 按下单击列表项：1.16.5-Forge_36.2.42
[02:15:38.046] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[02:15:38.064] [Minecraft] 启动按钮：Minecraft 版本：D:\特摄\versions\1.16.5-Forge_36.2.42\
[02:15:39.114] [Launch] 开始微软登录步骤 4/6
[02:15:39.122] [Net] 发起网络请求（POST，https://api.minecraftservices.com/authentication/login_with_xbox），最大超时 15000
[02:15:39.821] [Control] 按下按钮：版本选择
[02:15:39.821] [Control] 切换主要页面：VersionSelect, 0
[02:15:40.491] [Control] 切换主要页面：VersionSetup, 0
[02:15:40.921] [Launch] 开始微软登录步骤 5/6
[02:15:40.921] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[02:15:41.174] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[02:15:41.363] [Control] 按下单选列表项：Mod 管理
[02:15:41.519] [Loader] 加载器 Mod List Loader 状态改变：Loading
[02:15:41.519] [System] 已刷新 Mod 列表
[02:15:41.526] [System] 欲读取的文件不存在，已返回空内容：C:\Users\<USER>\AppData\Local\Temp\PCL\Cache\LocalMod.json
[02:15:41.675] [Launch] 开始微软登录步骤 6/6
[02:15:41.675] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[02:15:41.832] [Mod] 共有 49 个 Mod，其中 49 个需要联网获取信息，0 个需要更新信息
[02:15:41.837] [Loader] 加载器 Mod List Detail Loader 状态改变：Loading
[02:15:41.839] [Mod] 目标加载器：Forge，版本：1.16.5
[02:15:41.848] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files），最大超时 5000
[02:15:41.937] [Loader] 加载器 Mod List Loader 状态改变：Finished
[02:15:41.937] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[02:15:42.142] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/fingerprints/432），最大超时 5000
[02:15:42.717] [Launch] 微软登录完成
[02:15:42.717] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[02:15:42.719] [Launch] 登录加载已结束
[02:15:42.719] [Loader] 加载器 登录 状态改变：Finished
[02:15:43.171] [Mod] 从 Modrinth 获取到 20 个本地 Mod 的对应信息
[02:15:43.180] [Mod] 需要从 Modrinth 获取 19 个本地 Mod 的工程信息
[02:15:43.180] [Net] 发起网络请求（GET，https://api.modrinth.com/v2/projects?ids=["lhGA9TYQ","wHboX6Zr","IXWeU7kR","uy4Cnpcm","40FYwb4z","9s6osm5g","e0M1UDsY","kr1RaEqy","t5W7Jfwy","gedNE4y2","RvVFJXfy","zQKV24yz","twkfQtEc","iZ10HXDj","JkSi2Fzx","Ins7SzzR","nmDcB62a","EsAfCjCV","uXXizFIs"]），最大超时 5000
[02:15:43.397] [Mod] 从 CurseForge 获取到 26 个本地 Mod 的对应信息
[02:15:43.402] [Mod] 需要从 CurseForge 获取 26 个本地 Mod 的工程信息
[02:15:43.402] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/mods），最大超时 5000
[02:15:44.156] [Mod] 已从 Modrinth 获取本地 Mod 信息，继续获取更新信息
[02:15:44.156] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files/update），最大超时 5000
[02:15:44.159] [System] 获取资源：ModData
[02:15:44.187] [Start] 加载 DLL：Imazen.WebP
[02:15:44.187] [System] 获取资源：Imazen_WebP
[02:15:44.188] [System] 获取资源：libwebp64
[02:15:44.669] [Mod] 从 Modrinth 获取本地 Mod 信息结束
[02:15:45.431] [Mod] 已从 CurseForge 获取本地 Mod 信息，需要获取 24 个用于检查更新的文件信息
[02:15:45.431] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/mods/files），最大超时 5000
[02:15:46.099] [Mod] 从 CurseForge 获取 Mod 更新信息结束
[02:15:46.105] [Mod] 联网获取本地 Mod 信息完成，为 28 个 Mod 更新缓存
[02:15:46.112] [Loader] 加载器 Mod List Detail Loader 状态改变：Finished
[02:15:54.606] [Animation] 两个动画帧间隔 500 ms
[02:16:02.464] [Report] FPS 0, 动画 29, 下载中 0（0 B/s）
[02:16:26.415] [Control] 按下图标按钮
[02:16:26.429] [Control] 普通弹窗：RotP THE WORLD
An addon for Ripples of the Past mod,
adding a new Stand - THE WORLD.

文件：RotP-迪亚哥世界-1.1.1.jar（1.24 M）
版本：1.1.1

Mod ID：rotp_theworld
依赖于：
 - forge
 - minecraft，版本：[1.16.5,1.17)
 - jojo，版本：[0.2.2,0.3)
[02:16:28.004] [Control] 按下按钮：百科搜索
[02:16:28.009] [Control] 普通弹框返回：1
[02:16:28.011] [System] 正在打开网页：https://www.mcmod.cn/s?key=Rot+P+THE+WORLD&site=all&filter=0
[02:17:12.583] [Control] 按下图标按钮
[02:17:12.591] [Loader] 加载器 Comp File 状态改变：Loading
[02:17:12.591] [Control] 切换主要页面：CompDetail, 0
[02:17:12.595] [Comp] 开始获取文件列表：RvVFJXfy
[02:17:12.596] [Net] 获取网络结果：https://api.modrinth.com/v2/project/RvVFJXfy/version，超时 5000ms，要求 json
[02:17:13.376] [Comp] RvVFJXfy 文件列表中还需要获取信息的前置：G62vhGwQ
[02:17:13.376] [Net] 获取网络结果：https://api.modrinth.com/v2/projects?ids=["G62vhGwQ"]，超时 5000ms，要求 json
[02:17:13.897] [Loader] 加载器 Comp File 状态改变：Finished
[02:17:18.183] [Comp] 记录当前已展开的卡片：所选版本：Forge 1.16.5
[02:17:18.183] [Control] 切换主要页面：CompDetail, 0
[02:17:18.183] [Control] 按下资源工程列表项：Ripples of the Past
[02:17:18.336] [Loader] 加载器 Comp File 状态改变：Loading
[02:17:18.337] [Comp] 开始获取文件列表：G62vhGwQ
[02:17:18.337] [Net] 获取网络结果：https://api.modrinth.com/v2/project/G62vhGwQ/version，超时 5000ms，要求 json
[02:17:18.800] [Loader] 加载器 Comp File 状态改变：Finished
[02:17:20.866] [Control] 按下图标按钮：BtnTitleInner
[02:17:20.866] [Control] 切换主要页面：CompDetail, -1
[02:17:21.022] [Loader] 加载器 Comp File 状态改变：Loading
[02:17:21.068] [Loader] 加载器 Comp File 状态改变：Finished
[02:17:21.624] [Control] 按下图标按钮：BtnTitleInner
[02:17:21.624] [Control] 切换主要页面：VersionSetup, -1
[03:43:26.252] [System] 系统时间微调为：2025年7月30日 3:43:26
[03:43:26.257] [System] 系统时间微调为：2025年7月30日 3:43:26
[03:58:15.089] [Control] 按下图标按钮：BtnTitleInner
[03:58:15.095] [Control] 切换主要页面：VersionSelect, -1
[03:58:16.740] [Control] 按下单选列表项：BEN 10
[03:58:16.742] [Setup] 当前选择的 Minecraft 文件夹：D:\BEN 10\
[03:58:16.742] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[03:58:16.744] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[03:58:16.757] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[03:58:16.761] 版本 JSON 可用性检查失败（D:\BEN 10\versions\Kevin_11-1.12.2-3.0\）：未找到版本 JSON 文件：D:\BEN 10\versions\Kevin_11-1.12.2-3.0\Kevin_11-1.12.2-3.0.json
   在 PCL.ModMinecraft.McVersion.DisableException()
   在 PCL.ModMinecraft.McVersion.AssetException()
   在 PCL.ModMinecraft.McVersion.Check()
[03:58:16.764] 版本 JSON 可用性检查失败（D:\BEN 10\versions\〖矛盾附属〗Random Ben 10 Stuff 1.0\）：未找到版本 JSON 文件：D:\BEN 10\versions\〖矛盾附属〗Random Ben 10 Stuff 1.0\〖矛盾附属〗Random Ben 10 Stuff 1.0.json
   在 PCL.ModMinecraft.McVersion.DisableException()
   在 PCL.ModMinecraft.McVersion.AssetException()
   在 PCL.ModMinecraft.McVersion.Check()
[03:58:16.764] [Setup] 当前选择的 Minecraft 版本：Ben10Craft
[03:58:16.764] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\BEN 10\versions\Ben10Craft\
[03:58:16.867] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[03:58:18.344] [Control] 按下图标按钮
[03:58:18.344] [Control] 切换主要页面：VersionSetup, 0
[03:58:19.795] [Control] 按下单选列表项：Mod 管理
[03:58:19.928] [Loader] 加载器 Mod List Loader 状态改变：Loading
[03:58:19.928] [System] 已刷新 Mod 列表
[03:58:20.557] [Mod] 共有 63 个 Mod，其中 63 个需要联网获取信息，0 个需要更新信息
[03:58:20.558] [Loader] 加载器 Mod List Detail Loader 状态改变：Loading
[03:58:20.558] [Mod] 目标加载器：Forge，版本：1.12.2
[03:58:20.558] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files），最大超时 5000
[03:58:20.684] [Loader] 加载器 Mod List Loader 状态改变：Finished
[03:58:21.006] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/fingerprints/432），最大超时 5000
[03:58:21.666] [Mod] 从 Modrinth 获取到 21 个本地 Mod 的对应信息
[03:58:21.682] [Mod] 需要从 Modrinth 获取 21 个本地 Mod 的工程信息
[03:58:21.682] [Net] 发起网络请求（GET，https://api.modrinth.com/v2/projects?ids=["G1ckZuWK","EsAfCjCV","1jDdpgcc","Wnxd13zP","jupr7Bf5","8BmcQJ2H","BgGyndL5","PWERr14M","GcowSBDA","KP2WhfaM","KDvYkUg3","Z4Z5ccuT","trhPSzT0","bApyjH1r","rlloIFEV","u6dRKJwZ","rxIIYO6c","jL4QSgdv","Cg6Uc79H","ZnmdXAk2","cudtvDnd"]），最大超时 5000
[03:58:22.120] [Mod] 从 CurseForge 获取到 59 个本地 Mod 的对应信息
[03:58:22.166] [Mod] 需要从 CurseForge 获取 59 个本地 Mod 的工程信息
[03:58:22.166] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/mods），最大超时 5000
[03:58:22.376] [Mod] 已从 Modrinth 获取本地 Mod 信息，继续获取更新信息
[03:58:22.376] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files/update），最大超时 5000
[03:58:22.800] [Mod] 从 Modrinth 获取本地 Mod 信息结束
[03:58:23.236] [Mod] 已从 CurseForge 获取本地 Mod 信息，需要获取 38 个用于检查更新的文件信息
[03:58:23.236] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/mods/files），最大超时 5000
[03:58:23.608] [Mod] 从 CurseForge 获取 Mod 更新信息结束
[03:58:23.614] [Mod] 联网获取本地 Mod 信息完成，为 58 个 Mod 更新缓存
[03:58:23.619] [Loader] 加载器 Mod List Detail Loader 状态改变：Finished
[03:58:26.401] [Control] 按下单选按钮：启用 (61)
[03:58:28.892] [Control] 按下单选按钮：禁用 (2)
[03:58:30.548] [Control] 按下单选按钮：启用 (61)
[03:58:38.398] [Control] 按下图标按钮
[03:58:38.399] [Control] 切换主要页面：CompDetail, 0
[03:58:38.551] [Loader] 加载器 Comp File 状态改变：Loading
[03:58:38.551] [Comp] 开始获取文件列表：1286024
[03:58:38.551] [Net] 获取网络结果：https://api.curseforge.com/v1/mods/1286024/files?pageSize=10000，超时 5000ms，要求 json
[03:58:38.844] [Loader] 加载器 Comp File 状态改变：Finished
[03:58:43.866] [Control] 按下按钮：转到 CurseForge
[03:58:43.866] [System] 正在打开网页：https://www.curseforge.com/minecraft/mc-mods/aimod
[03:58:53.705] [Control] 按下图标按钮：BtnTitleInner
[03:58:53.705] [Control] 切换主要页面：VersionSetup, -1
[03:58:54.807] [Control] 按下图标按钮：BtnTitleMin
[06:07:59.299] [Animation] 两个动画帧间隔 735 ms
[07:19:35.553] [Animation] 两个动画帧间隔 1609 ms
[11:00:18.195] [Animation] 两个动画帧间隔 719 ms
[11:00:18.817] [Animation] 两个动画帧间隔 235 ms
[11:09:05.555] [Animation] 两个动画帧间隔 719 ms
[11:09:07.571] [Animation] 两个动画帧间隔 235 ms
[11:21:20.546] [Animation] 两个动画帧间隔 219 ms
[13:44:54.768] [Animation] 两个动画帧间隔 687 ms
[13:45:34.085] [Animation] 两个动画帧间隔 1594 ms
[14:06:26.510] [Animation] 两个动画帧间隔 703 ms
[15:02:52.698] [Animation] 两个动画帧间隔 1609 ms
[15:38:43.907] [Animation] 两个动画帧间隔 563 ms
[17:40:49.131] [Control] 按下图标按钮：BtnTitleInner
[17:40:49.135] [Control] 切换主要页面：VersionSelect, -1
[17:40:51.971] [Control] 按下单选列表项：(导入包)奥特英雄激战重奏
[17:40:51.974] [Setup] 当前选择的 Minecraft 文件夹：D:\(导入包)奥特英雄激战重奏\.minecraft\
[17:40:51.974] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[17:40:51.975] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[17:40:51.982] [Setup] 当前选择的 Minecraft 版本：奥特英雄激战重奏
[17:40:51.982] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\(导入包)奥特英雄激战重奏\.minecraft\versions\奥特英雄激战重奏\
[17:40:51.986] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[17:40:52.148] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[17:40:52.465] [Control] 按下单选列表项：ben10
[17:40:52.466] [Setup] 当前选择的 Minecraft 文件夹：D:\ben10\
[17:40:52.466] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[17:40:52.467] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[17:40:52.470] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[17:40:52.470] [Setup] 当前选择的 Minecraft 版本：MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK
[17:40:52.470] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\ben10\versions\MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK\
[17:40:52.590] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[17:40:52.884] [Control] 按下单选列表项：paBEN10
[17:40:52.885] [Setup] 当前选择的 Minecraft 文件夹：D:\paBEN10\
[17:40:52.885] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[17:40:52.885] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[17:40:52.890] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[17:40:52.890] [Setup] 当前选择的 Minecraft 版本：1.21.4-Fabric 0.16.14
[17:40:52.890] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\
[17:40:53.008] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[17:40:55.106] [Control] 按下单选列表项：modding
[17:40:55.107] [Setup] 当前选择的 Minecraft 文件夹：C:\Users\<USER>\Desktop\.minecraft\
[17:40:55.107] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[17:40:55.107] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[17:40:55.110] [Setup] 当前选择的 Minecraft 版本：Chinese Cuisine
[17:40:55.110] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\
[17:40:55.112] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[17:40:55.239] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[17:41:05.318] [Control] 按下单选列表项：官方启动器文件夹
[17:41:05.319] [Setup] 当前选择的 Minecraft 文件夹：C:\Users\<USER>\AppData\Roaming\.minecraft\
[17:41:05.319] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[17:41:05.324] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[17:41:05.326] [Setup] 当前选择的 Minecraft 版本：[幽]ANZNB火影忍者懒人包
[17:41:05.326] [Minecraft] 选择该文件夹储存的 Minecraft 版本：C:\Users\<USER>\AppData\Roaming\.minecraft\versions\[幽]ANZNB火影忍者懒人包\
[17:41:05.327] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[17:41:05.455] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[17:41:05.994] [Control] 按下单选列表项：(导入包)奥特英雄激战重奏
[17:41:05.995] [Setup] 当前选择的 Minecraft 文件夹：D:\(导入包)奥特英雄激战重奏\.minecraft\
[17:41:05.995] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[17:41:05.995] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[17:41:05.996] [Setup] 当前选择的 Minecraft 版本：奥特英雄激战重奏
[17:41:05.996] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\(导入包)奥特英雄激战重奏\.minecraft\versions\奥特英雄激战重奏\
[17:41:05.999] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[17:41:06.151] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[17:41:06.557] [Control] 按下单选列表项：ben10
[17:41:06.558] [Setup] 当前选择的 Minecraft 文件夹：D:\ben10\
[17:41:06.558] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[17:41:06.559] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[17:41:06.560] [Setup] 当前选择的 Minecraft 版本：MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK
[17:41:06.560] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\ben10\versions\MARSHY'S SECRETS OF THE OMNITRIX OFFICIAL SERVER MODPACK\
[17:41:06.564] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[17:41:06.693] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[17:41:07.442] [Control] 按下单选列表项：BEN 10
[17:41:07.443] [Setup] 当前选择的 Minecraft 文件夹：D:\BEN 10\
[17:41:07.443] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[17:41:07.444] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[17:41:07.448] 版本 JSON 可用性检查失败（D:\BEN 10\versions\Kevin_11-1.12.2-3.0\）：未找到版本 JSON 文件：D:\BEN 10\versions\Kevin_11-1.12.2-3.0\Kevin_11-1.12.2-3.0.json
   在 PCL.ModMinecraft.McVersion.DisableException()
   在 PCL.ModMinecraft.McVersion.AssetException()
   在 PCL.ModMinecraft.McVersion.Check()
[17:41:07.448] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[17:41:07.450] 版本 JSON 可用性检查失败（D:\BEN 10\versions\〖矛盾附属〗Random Ben 10 Stuff 1.0\）：未找到版本 JSON 文件：D:\BEN 10\versions\〖矛盾附属〗Random Ben 10 Stuff 1.0\〖矛盾附属〗Random Ben 10 Stuff 1.0.json
   在 PCL.ModMinecraft.McVersion.DisableException()
   在 PCL.ModMinecraft.McVersion.AssetException()
   在 PCL.ModMinecraft.McVersion.Check()
[17:41:07.450] [Setup] 当前选择的 Minecraft 版本：Ben10Craft
[17:41:07.450] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\BEN 10\versions\Ben10Craft\
[17:41:07.572] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[17:41:08.430] [Control] 按下单选列表项：寰宇特摄
[17:41:08.430] [Setup] 当前选择的 Minecraft 文件夹：D:\寰宇特摄\
[17:41:08.430] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[17:41:08.432] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[17:41:08.436] [Setup] 当前选择的 Minecraft 版本：Immersive Fight 3.2.3
[17:41:08.436] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\寰宇特摄\versions\Immersive Fight 3.2.3\
[17:41:08.438] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[17:41:08.579] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[17:41:09.157] [Control] 按下单选列表项：paBEN10
[17:41:09.157] [Setup] 当前选择的 Minecraft 文件夹：D:\paBEN10\
[17:41:09.157] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[17:41:09.158] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[17:41:09.160] [Setup] 当前选择的 Minecraft 版本：1.21.4-Fabric 0.16.14
[17:41:09.160] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\paBEN10\versions\1.21.4-Fabric 0.16.14\
[17:41:09.162] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[17:41:09.309] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[17:41:10.116] [Control] 按下单选列表项：特摄
[17:41:10.118] [Setup] 当前选择的 Minecraft 文件夹：D:\特摄\
[17:41:10.118] [Loader] 加载器 Minecraft Folder List 状态改变：Loading
[17:41:10.119] [Loader] 加载器 Minecraft Version List 状态改变：Loading
[17:41:10.122] [Setup] 当前选择的 Minecraft 版本：1.16.5-Forge_36.2.42
[17:41:10.122] [Minecraft] 选择该文件夹储存的 Minecraft 版本：D:\特摄\versions\1.16.5-Forge_36.2.42\
[17:41:10.124] [Loader] 加载器 Minecraft Folder List 状态改变：Finished
[17:41:10.252] [Loader] 加载器 Minecraft Version List 状态改变：Finished
[17:41:12.180] [Control] 按下图标按钮
[17:41:12.181] [Control] 切换主要页面：VersionSetup, 0
[17:41:14.324] [Control] 按下单选列表项：Mod 管理
[17:41:14.474] [Loader] 加载器 Mod List Loader 状态改变：Loading
[17:41:14.474] [System] 已刷新 Mod 列表
[17:41:14.805] [Mod] 共有 49 个 Mod，其中 21 个需要联网获取信息，28 个需要更新信息
[17:41:14.805] [Loader] 加载器 Mod List Detail Loader 状态改变：Loading
[17:41:14.806] [Mod] 目标加载器：Forge，版本：1.16.5
[17:41:14.806] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files），最大超时 5000
[17:41:14.807] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/fingerprints/432），最大超时 5000
[17:41:14.972] [Loader] 加载器 Mod List Loader 状态改变：Finished
[17:41:15.800] [Mod] 从 Modrinth 获取到 20 个本地 Mod 的对应信息
[17:41:15.801] [Mod] 需要从 Modrinth 获取 19 个本地 Mod 的工程信息
[17:41:15.801] [Net] 发起网络请求（GET，https://api.modrinth.com/v2/projects?ids=["lhGA9TYQ","wHboX6Zr","IXWeU7kR","uy4Cnpcm","40FYwb4z","9s6osm5g","e0M1UDsY","kr1RaEqy","t5W7Jfwy","gedNE4y2","RvVFJXfy","zQKV24yz","twkfQtEc","iZ10HXDj","JkSi2Fzx","Ins7SzzR","nmDcB62a","EsAfCjCV","uXXizFIs"]），最大超时 5000
[17:41:16.561] [Mod] 已从 Modrinth 获取本地 Mod 信息，继续获取更新信息
[17:41:16.562] [Net] 发起网络请求（POST，https://api.modrinth.com/v2/version_files/update），最大超时 5000
[17:41:16.632] [Control] 按下按钮：打开文件夹
[17:41:16.634] [System] 正在打开资源管理器：D:\特摄\versions\1.16.5-Forge_36.2.42\mods\
[17:41:16.635] [System] 执行外部命令：D:\特摄\versions\1.16.5-Forge_36.2.42\mods\ 
[17:41:17.039] [Mod] 从 Modrinth 获取本地 Mod 信息结束
[17:41:19.847] NetRequestOnce 失败：你的网络环境不佳，导致难以连接到服务器。请稍后重试，或使用 VPN 以改善网络环境。

————————————
详细错误信息：
连接服务器超时，请检查你的网络环境是否良好（操作超时，https://api.curseforge.com/v1/fingerprints/432）
   错误类型：System.Net.WebException
→ 操作超时
   在 System.Net.HttpWebRequest.GetResponse()
   在 PCL.ModNet.NetRequestOnce(String Url, String Method, Object Data, String ContentType, Int32 Timeout, Dictionary`2 Headers, Boolean MakeLog, Boolean UseBrowserUserAgent)
   错误类型：System.Net.WebException
[17:41:19.847] [Net] 发起网络请求（POST，https://api.curseforge.com/v1/fingerprints/432），最大超时 20000
[17:41:24.689] [Control] 按下图标按钮：BtnTitleInner
[17:41:24.689] [Control] 切换主要页面：VersionSelect, -1
[17:41:25.815] [Control] 切换主要页面：Launch, -1
[17:41:25.815] [Control] 按下单击列表项：1.16.5-Forge_36.2.42
[17:41:26.367] [Control] 按下按钮：启动游戏
[17:41:26.377] [Loader] 加载器 Loader Launch 状态改变：Loading
[17:41:26.393] [Launch] 预检测已通过
[17:41:26.396] [Download] 开始后台检查资源文件索引
[17:41:26.396] [Loader] 加载器 后台更新资源文件索引 状态改变：Loading
[17:41:26.398] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Loading
[17:41:26.398] [Loader] 加载器 登录 状态改变：Waiting
[17:41:26.398] [Loader] 加载器 Minecraft 启动 状态改变：Loading
[17:41:26.398] [Loader] 加载器 获取 Java 状态改变：Loading
[17:41:26.399] [Loader] 加载器 登录 状态改变：Loading
[17:41:26.399] [Loader] 加载器 补全文件 状态改变：Loading
[17:41:26.399] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Loading
[17:41:26.399] [Loader] 加载器 分析缺失支持库文件 状态改变：Loading
[17:41:26.399] [Launch] 登录加载已开始
[17:41:26.399] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[17:41:26.399] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Loading
[17:41:26.399] [Loader] 加载器 分析资源文件索引地址 状态改变：Loading
[17:41:26.400] [Launch] 登录方式：正版（Quasar2323）
[17:41:26.400] [Launch] 开始微软登录步骤 1/6（刷新登录）
[17:41:26.400] [Loader] 加载器 内存优化 状态改变：Loading
[17:41:26.400] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[17:41:26.400] [Download] 版本 1.16.5-Forge_36.2.42 对应的资源文件索引为 1.16
[17:41:26.400] [Download] 版本 1.16.5-Forge_36.2.42 对应的资源文件索引为 1.16
[17:41:26.400] [Launch] 内存优化开始
[17:41:26.402] [Loader] 加载器 后台分析资源文件索引地址 状态改变：Finished
[17:41:26.402] [Taskbar] Minecraft 启动 已加入任务列表
[17:41:26.402] [Test] 没有管理员权限，将以命令行方式进行内存优化
[17:41:26.402] [Loader] 加载器 后台下载资源文件索引 状态改变：Loading
[17:41:26.412] [Loader] 加载器 分析资源文件索引地址 状态改变：Finished
[17:41:26.413] [Loader] 加载器 下载资源文件索引 状态改变：Loading
[17:41:26.413] [Loader] 加载器 下载资源文件索引 状态改变：Finished
[17:41:26.413] [Loader] 加载器 分析缺失资源文件 状态改变：Loading
[17:41:26.423] [Download] 1.16.json 5527#：开始，起始点 0，https://piston-meta.mojang.com/v1/packages/f3c4aa96e12951cd2781b3e1c0e8ab82bf719cf2/1.16.json
[17:41:26.448] [Minecraft] 获取支持库列表：1.16.5-Forge_36.2.42
[17:41:26.462] [Minecraft] 发现重复的支持库：295 K | D:\特摄\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\特摄\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[17:41:26.462] [Minecraft] 发现重复的支持库：1.71 M | D:\特摄\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\特摄\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[17:41:26.462] [Minecraft] 发现重复的支持库：76.3 K | D:\特摄\libraries\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar (5.0.4) 与 76.3 K | D:\特摄\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[17:41:26.481] [Launch] Java 版本需求：最低 1.8.0.0，最高 999.999.999.999
[17:41:26.492] [Java] 开始完全遍历查找：D:\特摄\
[17:41:26.514] [Java] 开始完全遍历查找：D:\特摄\versions\1.16.5-Forge_36.2.42\
[17:41:26.530] [Java] 排序后的 Java 优先顺序：
[17:41:26.530] [Java]  - JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[17:41:26.530] [Java]  - JDK 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\bin\
[17:41:26.530] [Java]  - JRE 8 (8.0.151)：D:\Wurst-Client-v6.35.3-MC1.12 (1)\jre\bin\
[17:41:26.530] [Java]  - JRE 8 (8.0.401)：C:\Program Files\Java\jre-1.8\bin\
[17:41:26.530] [Java]  - JRE 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\
[17:41:26.530] [Java]  - JDK 17 (17.0.15)：C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\
[17:41:26.530] [Java]  - JDK 8 (8.0.452)：C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\
[17:41:26.530] [Java]  - JDK 21 (21.0.8)：C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\
[17:41:26.530] [Java]  - JDK 21 (21.0.5)：C:\Program Files\Java\jdk-21\bin\
[17:41:26.530] [Java]  - JDK 11 (11.0.24)：C:\Program Files\Java\jdk-11\bin\
[17:41:26.535] [System] 执行外部命令并等待返回结果：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\java.exe -version
[17:41:26.641] [Loader] 加载器 分析缺失支持库文件 状态改变：Finished
[17:41:26.641] [Loader] 加载器 下载支持库文件 状态改变：Loading
[17:41:26.642] [Loader] 加载器 下载支持库文件 状态改变：Finished
[17:41:26.642] [Loader] 加载器 下载支持库文件（主加载器） 状态改变：Finished
[17:41:26.653] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[17:41:26.712] [Loader] 加载器 分析缺失资源文件 状态改变：Finished
[17:41:26.712] [Loader] 加载器 下载资源文件 状态改变：Loading
[17:41:26.713] [Loader] 加载器 下载资源文件 状态改变：Finished
[17:41:26.713] [Loader] 加载器 下载资源文件（主加载器） 状态改变：Finished
[17:41:26.713] [Loader] 加载器 补全文件 状态改变：Finished
[17:41:26.714] [Loader] 由于输入条件变更，重启进行中的加载器 登录
[17:41:26.894] [Download] 1.16.json 5527#：文件大小 295227（288 K）
[17:41:27.279] [Java] 最终选定的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[17:41:27.279] [Launch] 选择的 Java：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[17:41:27.279] [Loader] 加载器 获取 Java 状态改变：Finished
[17:41:27.279] [Loader] 由于输入条件变更，重启进行中的加载器 登录
[17:41:27.280] [Launch] 登录加载已开始
[17:41:27.280] [Launch] 登录方式：正版（Quasar2323）
[17:41:27.280] [Launch] 开始微软登录步骤 1/6（刷新登录）
[17:41:27.280] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[17:41:27.382] [Download] 1.16.json：已完成，剩余文件 0
[17:41:27.382] [Loader] 加载器 后台下载资源文件索引 状态改变：Finished
[17:41:27.382] [Loader] 加载器 后台复制资源文件索引 状态改变：Loading
[17:41:27.383] [Launch] 后台更新资源文件索引成功：C:\Users\<USER>\AppData\Local\Temp\PCL\Cache\1.16.json
[17:41:27.383] [Loader] 加载器 后台复制资源文件索引 状态改变：Finished
[17:41:27.383] [Loader] 加载器 后台更新资源文件索引 状态改变：Finished
[17:41:27.395] [Download] 速度下限已提升到 261 K
[17:41:27.534] [Net] 发起网络请求（POST，https://login.live.com/oauth20_token.srf），最大超时 30000
[17:41:28.105] [Launch] 开始微软登录步骤 2/6
[17:41:28.105] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[17:41:28.368] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[17:41:28.879] [Net] 发起网络请求（POST，https://user.auth.xboxlive.com/user/authenticate），最大超时 30000
[17:41:29.644] [Launch] 开始微软登录步骤 3/6
[17:41:29.644] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[17:41:29.904] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[17:41:30.414] [Net] 发起网络请求（POST，https://xsts.auth.xboxlive.com/xsts/authorize），最大超时 30000
[17:41:31.170] [Launch] 开始微软登录步骤 4/6
[17:41:31.170] [Net] 发起网络请求（POST，https://api.minecraftservices.com/authentication/login_with_xbox），最大超时 15000
[17:41:32.151] [Launch] 开始微软登录步骤 5/6
[17:41:32.152] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[17:41:32.405] [Net] 发起网络请求（GET，https://api.minecraftservices.com/entitlements/mcstore），最大超时 30000
[17:41:33.009] [Launch] 开始微软登录步骤 6/6
[17:41:33.009] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[17:41:33.271] [Net] 发起网络请求（GET，https://api.minecraftservices.com/minecraft/profile），最大超时 30000
[17:41:33.787] [Launch] 微软登录完成
[17:41:33.787] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[17:41:33.797] [Launch] 登录加载已结束
[17:41:33.797] [Loader] 加载器 登录 状态改变：Finished
[17:41:33.797] [Loader] 由于输入条件变更，重启已完成的加载器 登录
[17:41:33.797] [Loader] 加载器 登录 状态改变：Loading
[17:41:33.797] [Launch] 登录加载已开始
[17:41:33.797] [Loader] 加载器 Loader Login Ms 状态改变：Loading
[17:41:33.798] [Launch] 登录方式：正版（Quasar2323）
[17:41:33.798] [Loader] 加载器 Loader Login Ms 状态改变：Finished
[17:41:33.813] [Launch] 登录加载已结束
[17:41:33.813] [Loader] 加载器 登录 状态改变：Finished
[17:41:38.498] [Test] 内存优化完成，可用内存改变量：3.59 G，大致剩余内存：7.33 G
[17:41:38.546] [Loader] 加载器 内存优化 状态改变：Finished
[17:41:38.555] [Loader] 加载器 获取启动参数 状态改变：Loading
[17:41:38.571] [Launch] 开始获取 Minecraft 启动参数
[17:41:38.571] [Launch] 获取新版 JVM 参数
[17:41:38.579] [Launch] 当前剩余内存：7.3G
[17:41:38.589] [Java] 选定的 Java Wrapper 路径：C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar
[17:41:38.589] [System] 获取资源：JavaWrapper
[17:41:38.591] [Launch] 新版 JVM 参数获取成功：
[17:41:38.592] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Dos.name=Windows 10 -Dos.version=10.0 -Djava.library.path=${natives_directory} -Dminecraft.launcher.brand=${launcher_name} -Dminecraft.launcher.version=${launcher_version} -cp ${classpath} -XX:+IgnoreUnrecognizedVMOptions --add-exports=java.base/sun.security.util=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=java.naming --add-opens=java.base/java.util.jar=ALL-UNNAMED -Xmn2304m -Xmx15360m -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" cpw.mods.modlauncher.Launcher
[17:41:38.592] [Launch] 获取新版 Game 参数
[17:41:38.594] [Launch] 新版 Game 参数获取成功
[17:41:38.597] [Minecraft] 获取支持库列表：1.16.5-Forge_36.2.42
[17:41:38.597] [Minecraft] 发现重复的支持库：295 K | D:\特摄\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar (2.15.0) 与 223 K | D:\特摄\libraries\org\apache\logging\log4j\log4j-api\2.8.1\log4j-api-2.8.1.jar (2.8.1)，已忽略其中之一
[17:41:38.597] [Minecraft] 发现重复的支持库：1.71 M | D:\特摄\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar (2.15.0) 与 1.34 M | D:\特摄\libraries\org\apache\logging\log4j\log4j-core\2.8.1\log4j-core-2.8.1.jar (2.8.1)，已忽略其中之一
[17:41:38.597] [Minecraft] 发现重复的支持库：76.3 K | D:\特摄\libraries\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar (5.0.4) 与 76.3 K | D:\特摄\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar (5.0.3)，已忽略其中之一
[17:41:38.601] [Launch] Minecraft 启动参数：
[17:41:38.602] [Launch] -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Dos.name="Windows 10" -Dos.version=10.0 -Djava.library.path="D:\特摄\versions\1.16.5-Forge_36.2.42\1.16.5-Forge_36.2.42-natives" -Dminecraft.launcher.brand=PCL -Dminecraft.launcher.version=361 -cp "D:\特摄\libraries\com\mojang\patchy\1.3.9\patchy-1.3.9.jar;D:\特摄\libraries\oshi-project\oshi-core\1.1\oshi-core-1.1.jar;D:\特摄\libraries\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;D:\特摄\libraries\net\java\dev\jna\platform\3.4.0\platform-3.4.0.jar;D:\特摄\libraries\com\ibm\icu\icu4j\66.1\icu4j-66.1.jar;D:\特摄\libraries\com\mojang\javabridge\1.0.22\javabridge-1.0.22.jar;D:\特摄\libraries\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar;D:\特摄\libraries\io\netty\netty-all\4.1.25.Final\netty-all-4.1.25.Final.jar;D:\特摄\libraries\com\google\guava\guava\21.0\guava-21.0.jar;D:\特摄\libraries\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;D:\特摄\libraries\commons-io\commons-io\2.5\commons-io-2.5.jar;D:\特摄\libraries\commons-codec\commons-codec\1.10\commons-codec-1.10.jar;D:\特摄\libraries\net\java\jinput\jinput\2.0.5\jinput-2.0.5.jar;D:\特摄\libraries\net\java\jutils\jutils\1.0.0\jutils-1.0.0.jar;D:\特摄\libraries\com\mojang\brigadier\1.0.17\brigadier-1.0.17.jar;D:\特摄\libraries\com\mojang\datafixerupper\4.0.26\datafixerupper-4.0.26.jar;D:\特摄\libraries\com\google\code\gson\gson\2.8.0\gson-2.8.0.jar;D:\特摄\libraries\com\mojang\authlib\2.1.28\authlib-2.1.28.jar;D:\特摄\libraries\org\apache\commons\commons-compress\1.8.1\commons-compress-1.8.1.jar;D:\特摄\libraries\org\apache\httpcomponents\httpclient\4.3.3\httpclient-4.3.3.jar;D:\特摄\libraries\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\特摄\libraries\org\apache\httpcomponents\httpcore\4.3.2\httpcore-4.3.2.jar;D:\特摄\libraries\it\unimi\dsi\fastutil\8.2.1\fastutil-8.2.1.jar;D:\特摄\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar;D:\特摄\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar;D:\特摄\libraries\org\lwjgl\lwjgl\3.2.2\lwjgl-3.2.2.jar;D:\特摄\libraries\org\lwjgl\lwjgl-jemalloc\3.2.2\lwjgl-jemalloc-3.2.2.jar;D:\特摄\libraries\org\lwjgl\lwjgl-openal\3.2.2\lwjgl-openal-3.2.2.jar;D:\特摄\libraries\org\lwjgl\lwjgl-opengl\3.2.2\lwjgl-opengl-3.2.2.jar;D:\特摄\libraries\org\lwjgl\lwjgl-glfw\3.2.2\lwjgl-glfw-3.2.2.jar;D:\特摄\libraries\org\lwjgl\lwjgl-stb\3.2.2\lwjgl-stb-3.2.2.jar;D:\特摄\libraries\org\lwjgl\lwjgl-tinyfd\3.2.2\lwjgl-tinyfd-3.2.2.jar;D:\特摄\libraries\com\mojang\text2speech\1.11.3\text2speech-1.11.3.jar;D:\特摄\libraries\net\minecraftforge\forge\1.16.5-36.2.42\forge-1.16.5-36.2.42.jar;D:\特摄\libraries\org\ow2\asm\asm\9.6\asm-9.6.jar;D:\特摄\libraries\org\ow2\asm\asm-commons\9.6\asm-commons-9.6.jar;D:\特摄\libraries\org\ow2\asm\asm-tree\9.6\asm-tree-9.6.jar;D:\特摄\libraries\org\ow2\asm\asm-util\9.6\asm-util-9.6.jar;D:\特摄\libraries\org\ow2\asm\asm-analysis\9.6\asm-analysis-9.6.jar;D:\特摄\libraries\cpw\mods\modlauncher\8.1.3\modlauncher-8.1.3.jar;D:\特摄\libraries\cpw\mods\grossjava9hacks\1.3.3\grossjava9hacks-1.3.3.jar;D:\特摄\libraries\net\minecraftforge\accesstransformers\3.0.1\accesstransformers-3.0.1.jar;D:\特摄\libraries\org\antlr\antlr4-runtime\4.9.1\antlr4-runtime-4.9.1.jar;D:\特摄\libraries\net\minecraftforge\eventbus\4.0.0\eventbus-4.0.0.jar;D:\特摄\libraries\net\minecraftforge\forgespi\3.2.0\forgespi-3.2.0.jar;D:\特摄\libraries\net\minecraftforge\coremods\4.0.6\coremods-4.0.6.jar;D:\特摄\libraries\net\minecraftforge\unsafe\0.2.0\unsafe-0.2.0.jar;D:\特摄\libraries\com\electronwill\night-config\core\3.6.3\core-3.6.3.jar;D:\特摄\libraries\com\electronwill\night-config\toml\3.6.3\toml-3.6.3.jar;D:\特摄\libraries\org\jline\jline\3.12.1\jline-3.12.1.jar;D:\特摄\libraries\org\apache\maven\maven-artifact\3.6.3\maven-artifact-3.6.3.jar;D:\特摄\libraries\net\jodah\typetools\0.8.3\typetools-0.8.3.jar;D:\特摄\libraries\org\apache\logging\log4j\log4j-slf4j18-impl\2.15.0\log4j-slf4j18-impl-2.15.0.jar;D:\特摄\libraries\net\minecrell\terminalconsoleappender\1.2.0\terminalconsoleappender-1.2.0.jar;D:\特摄\libraries\org\spongepowered\mixin\0.8.4\mixin-0.8.4.jar;D:\特摄\libraries\net\minecraftforge\nashorn-core-compat\15.1.1.1\nashorn-core-compat-15.1.1.1.jar;D:\特摄\versions\1.16.5-Forge_36.2.42\1.16.5-Forge_36.2.42.jar" -XX:+IgnoreUnrecognizedVMOptions --add-exports=java.base/sun.security.util=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=java.naming --add-opens=java.base/java.util.jar=ALL-UNNAMED -Xmn2304m -Xmx15360m -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" cpw.mods.modlauncher.Launcher --username Quasar2323 --version 1.16.5-Forge_36.2.42 --gameDir "D:\特摄\versions\1.16.5-Forge_36.2.42" --assetsDir "D:\特摄\assets" --assetIndex 1.16 --uuid 077e3afcb77643c48ae30f9dbb755bef --accessToken eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************gQ8rw --userType msa --versionType PCL --width 854 --height 480 --launchTarget fmlclient --fml.forgeVersion 36.2.42 --fml.mcVersion 1.16.5 --fml.forgeGroup net.minecraftforge --fml.mcpVersion 20210115.111550 
[17:41:38.604] [Loader] 加载器 获取启动参数 状态改变：Finished
[17:41:38.608] [Loader] 加载器 解压文件 状态改变：Loading
[17:41:38.611] [Launch] 正在解压 Natives 文件
[17:41:38.616] [Loader] 加载器 解压文件 状态改变：Finished
[17:41:38.627] [Loader] 加载器 预启动处理 状态改变：Loading
[17:41:38.644] [System] 已调整显卡设置：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[17:41:38.644] [System] 无需调整显卡设置：C:\Users\<USER>\Desktop\._cache_Plain Craft Launcher.exe
[17:41:38.649] [Launch] 已更新 launcher_profiles.json
[17:41:38.650] [Launch] 需要的语言为 zh_cn，当前语言为 zh_cn，无需修改
[17:41:38.650] [Loader] 加载器 预启动处理 状态改变：Finished
[17:41:38.655] [Loader] 加载器 执行自定义命令 状态改变：Loading
[17:41:38.661] [Loader] 加载器 执行自定义命令 状态改变：Finished
[17:41:38.686] [Loader] 加载器 启动进程 状态改变：Loading
[17:41:38.807] [Launch] 已启动游戏进程：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\javaw.exe
[17:41:38.809] [Loader] 加载器 启动进程 状态改变：Finished
[17:41:38.837] [Loader] 加载器 等待游戏窗口出现 状态改变：Loading
[17:41:38.840] [Launch] 
[17:41:38.840] [Launch] ~ 基础参数 ~
[17:41:38.840] [Launch] PCL 版本：Release 2.10.3 (361)
[17:41:38.840] [Launch] 游戏版本：1.16.5, Forge 36.2.42（识别为 1.16.5）
[17:41:38.840] [Launch] 资源版本：1.16
[17:41:38.840] [Launch] 版本继承：无
[17:41:38.840] [Launch] 分配的内存：15 GB（15360 MB）
[17:41:38.840] [Launch] MC 文件夹：D:\特摄\
[17:41:38.840] [Launch] 版本文件夹：D:\特摄\versions\1.16.5-Forge_36.2.42\
[17:41:38.840] [Launch] 版本隔离：True
[17:41:38.840] [Launch] HMCL 格式：False
[17:41:38.840] [Launch] Java 信息：JRE 8 (8.0.462)：C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\
[17:41:38.841] [Launch] 环境变量：未设置
[17:41:38.841] [Launch] Natives 文件夹：D:\特摄\versions\1.16.5-Forge_36.2.42\1.16.5-Forge_36.2.42-natives
[17:41:38.841] [Launch] 
[17:41:38.841] [Launch] ~ 登录参数 ~
[17:41:38.841] [Launch] 玩家用户名：Quasar2323
[17:41:38.841] [Launch] AccessToken：eyJra******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************gQ8rw
[17:41:38.841] [Launch] ClientToken：077e3afcb77643c48ae30f9dbb755bef
[17:41:38.841] [Launch] UUID：077e3afcb77643c48ae30f9dbb755bef
[17:41:38.841] [Launch] 登录方式：Microsoft
[17:41:38.841] [Launch] 
[17:41:38.879] [Launch] [36260] 开始 Minecraft 日志监控
[17:41:38.880] [Launch] [全局] 出现运行中的 Minecraft
[17:41:39.842] [Launch] [36260] 日志 1/5：已出现日志输出
[17:41:39.887] NetRequestOnce 失败：你的网络环境不佳，导致难以连接到服务器。请稍后重试，或使用 VPN 以改善网络环境。

————————————
详细错误信息：
连接服务器超时，请检查你的网络环境是否良好（操作超时，https://api.curseforge.com/v1/fingerprints/432）
   错误类型：System.Net.WebException
→ 操作超时
   在 System.Net.HttpWebRequest.GetResponse()
   在 PCL.ModNet.NetRequestOnce(String Url, String Method, Object Data, String ContentType, Int32 Timeout, Dictionary`2 Headers, Boolean MakeLog, Boolean UseBrowserUserAgent)
   错误类型：System.Net.WebException
[17:41:39.888] 从 CurseForge 获取本地 Mod 信息失败：你的网络环境不佳，导致难以连接到服务器。请稍后重试，或使用 VPN 以改善网络环境。

————————————
详细错误信息：
连接服务器超时，请检查你的网络环境是否良好（操作超时，https://api.curseforge.com/v1/fingerprints/432）
连接服务器超时，请检查你的网络环境是否良好（操作超时，https://api.curseforge.com/v1/fingerprints/432）

   在 PCL.ModDownload.DlModRequest(String Url, String Method, String Data, String ContentType)
   在 PCL.ModMod._Closure$__6-0._Lambda$__4()
[17:41:39.894] [Mod] 联网获取本地 Mod 信息完成，为 28 个 Mod 更新缓存
[17:41:39.901] [Loader] 加载器 Mod List Detail Loader 状态改变：Finished
[17:41:41.242] [Launch] [36260] Mod Loader 窗口已加载：FML early loading progress（791502）
[17:41:43.371] [Launch] [36260] Minecraft 加载已完成
[17:41:43.441] [Loader] 加载器 等待游戏窗口出现 状态改变：Finished
[17:41:43.442] [Loader] 加载器 结束处理 状态改变：Loading
[17:41:43.446] [Launch] 开始启动结束处理
[17:41:43.447] [Launch] 启动器可见性：5
[17:41:43.447] [Loader] 加载器 结束处理 状态改变：Finished
[17:41:43.495] [Loader] 加载器 Minecraft 启动 状态改变：Finished
[17:41:43.551] [Taskbar] Minecraft 启动 已移出任务列表
[17:41:43.553] [Loader] 加载器 Loader Launch 状态改变：Finished
[17:41:43.618] [UI] 弹出提示：1.16.5-Forge_36.2.42 启动成功！
[17:41:47.352] [Launch] [36260] Minecraft 已退出，返回值：1
[17:41:47.352] [Launch] [36260] Minecraft 返回值异常，可能已崩溃
[17:41:47.354] [Launch] [全局] 已无运行中的 Minecraft
[17:41:47.354] [Launch] [36260] Minecraft 已崩溃，将在 2 秒后开始崩溃分析
[17:41:47.419] [UI] 弹出提示：检测到 Minecraft 出现错误，错误分析已开始……
[17:41:47.659] [System] 诊断信息：
操作系统：Microsoft Windows 11 家庭中文版（32 位：False）
剩余内存：6744 M / 16108 M
DPI：144（150%）
MC 文件夹：D:\特摄\
文件位置：C:\Users\<USER>\Desktop\
[17:41:47.880] [Launch] [36260] Minecraft 日志监控已退出
[17:41:48.777] [Control] 按下图标按钮：BtnTitleClose
[17:41:48.785] [System] 收到关闭指令
[17:41:49.025] [System] 程序已退出，返回值：Success
