@echo off
title ���� - Ben10<PERSON><PERSON>
echo ��Ϸ�������������Ժ�
cd /D "D:\BEN 10\versions\Ben10Craft\"


"C:\Users\<USER>\.jdks\semeru-1.8.0_462\jre\bin\java.exe" -XX:+UseG1GC -XX:-UseAdaptiveSizePolicy -XX:-OmitStackTraceInFastThrow -Djdk.lang.Process.allowAmbiguousCommands=true -Dfml.ignoreInvalidMinecraftCertificates=True -Dfml.ignorePatchDiscrepancies=True -Dlog4j2.formatMsgNoLookups=true-Dforge.logging.console.level=debug -Dforge.logging.markers=REGISTRIES -XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump -Xmn2304m -Xmx15360m "-Djava.library.path=D:\BEN 10\versions\Ben10Craft\Ben10Craft-natives" -cp "D:\BEN 10\libraries\com\mojang\patchy\1.3.9\patchy-1.3.9.jar;D:\BEN 10\libraries\oshi-project\oshi-core\1.1\oshi-core-1.1.jar;D:\BEN 10\libraries\net\java\dev\jna\jna\4.4.0\jna-4.4.0.jar;D:\BEN 10\libraries\net\java\dev\jna\platform\3.4.0\platform-3.4.0.jar;D:\BEN 10\libraries\com\ibm\icu\icu4j-core-mojang\51.2\icu4j-core-mojang-51.2.jar;D:\BEN 10\libraries\net\sf\jopt-simple\jopt-simple\5.0.3\jopt-simple-5.0.3.jar;D:\BEN 10\libraries\com\paulscode\codecjorbis\20101023\codecjorbis-20101023.jar;D:\BEN 10\libraries\com\paulscode\codecwav\20101023\codecwav-20101023.jar;D:\BEN 10\libraries\com\paulscode\libraryjavasound\20101123\libraryjavasound-20101123.jar;D:\BEN 10\libraries\com\paulscode\librarylwjglopenal\20100824\librarylwjglopenal-20100824.jar;D:\BEN 10\libraries\com\paulscode\soundsystem\20120107\soundsystem-20120107.jar;D:\BEN 10\libraries\io\netty\netty-all\4.1.9.Final\netty-all-4.1.9.Final.jar;D:\BEN 10\libraries\com\google\guava\guava\21.0\guava-21.0.jar;D:\BEN 10\libraries\org\apache\commons\commons-lang3\3.5\commons-lang3-3.5.jar;D:\BEN 10\libraries\commons-io\commons-io\2.5\commons-io-2.5.jar;D:\BEN 10\libraries\commons-codec\commons-codec\1.10\commons-codec-1.10.jar;D:\BEN 10\libraries\net\java\jinput\jinput\2.0.5\jinput-2.0.5.jar;D:\BEN 10\libraries\net\java\jutils\jutils\1.0.0\jutils-1.0.0.jar;D:\BEN 10\libraries\com\google\code\gson\gson\2.8.0\gson-2.8.0.jar;D:\BEN 10\libraries\com\mojang\authlib\1.5.25\authlib-1.5.25.jar;D:\BEN 10\libraries\com\mojang\realms\1.10.22\realms-1.10.22.jar;D:\BEN 10\libraries\org\apache\commons\commons-compress\1.8.1\commons-compress-1.8.1.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpclient\4.3.3\httpclient-4.3.3.jar;D:\BEN 10\libraries\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\BEN 10\libraries\org\apache\httpcomponents\httpcore\4.3.2\httpcore-4.3.2.jar;D:\BEN 10\libraries\it\unimi\dsi\fastutil\7.1.0\fastutil-7.1.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-api\2.15.0\log4j-api-2.15.0.jar;D:\BEN 10\libraries\org\apache\logging\log4j\log4j-core\2.15.0\log4j-core-2.15.0.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl\2.9.4-nightly-20150209\lwjgl-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\org\lwjgl\lwjgl\lwjgl_util\2.9.4-nightly-20150209\lwjgl_util-2.9.4-nightly-20150209.jar;D:\BEN 10\libraries\com\mojang\text2speech\1.10.3\text2speech-1.10.3.jar;D:\BEN 10\libraries\net\minecraftforge\forge\1.12.2-14.23.5.2860\forge-1.12.2-14.23.5.2860.jar;D:\BEN 10\libraries\org\ow2\asm\asm-debug-all\5.2\asm-debug-all-5.2.jar;D:\BEN 10\libraries\net\minecraft\launchwrapper\1.12\launchwrapper-1.12.jar;D:\BEN 10\libraries\org\jline\jline\3.5.1\jline-3.5.1.jar;D:\BEN 10\libraries\com\typesafe\akka\akka-actor_2.11\2.3.3\akka-actor_2.11-2.3.3.jar;D:\BEN 10\libraries\com\typesafe\config\1.2.1\config-1.2.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-actors-migration_2.11\1.1.0\scala-actors-migration_2.11-1.1.0.jar;D:\BEN 10\libraries\org\scala-lang\scala-compiler\2.11.1\scala-compiler-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-library_2.11\1.0.2_mc\scala-continuations-library_2.11-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\plugins\scala-continuations-plugin_2.11.1\1.0.2_mc\scala-continuations-plugin_2.11.1-1.0.2_mc.jar;D:\BEN 10\libraries\org\scala-lang\scala-library\2.11.1\scala-library-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-parser-combinators_2.11\1.0.1\scala-parser-combinators_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-reflect\2.11.1\scala-reflect-2.11.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-swing_2.11\1.0.1\scala-swing_2.11-1.0.1.jar;D:\BEN 10\libraries\org\scala-lang\scala-xml_2.11\1.0.2\scala-xml_2.11-1.0.2.jar;D:\BEN 10\libraries\lzma\lzma\0.0.1\lzma-0.0.1.jar;D:\BEN 10\libraries\java3d\vecmath\1.5.2\vecmath-1.5.2.jar;D:\BEN 10\libraries\net\sf\trove4j\trove4j\3.0.3\trove4j-3.0.3.jar;D:\BEN 10\libraries\org\apache\maven\maven-artifact\3.5.3\maven-artifact-3.5.3.jar;D:\BEN 10\versions\Ben10Craft\Ben10Craft.jar" -Doolloo.jlw.tmpdir="C:\Users\<USER>\Desktop\PCL" -jar "C:\Users\<USER>\Desktop\PCL\JavaWrapper.jar" net.minecraft.launchwrapper.Launch --username Quasar2323 --version Ben10Craft --gameDir "D:\BEN 10\versions\Ben10Craft" --assetsDir "D:\BEN 10\assets" --assetIndex 1.12 --uuid 077e3afcb77643c48ae30f9dbb755bef --accessToken eyJraFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFRxweQ --userType msa --versionType PCL --tweakClass net.minecraftforge.fml.common.launcher.FMLTweaker --versionType Forge --height 480 --width 854 
echo ��Ϸ���˳���
pause