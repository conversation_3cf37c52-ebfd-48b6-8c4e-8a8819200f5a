# 程序迁移脚本 - 安全版本
# 运行前请确保以管理员身份运行PowerShell

param(
    [switch]$DryRun = $false,  # 预览模式，不实际执行
    [switch]$Conservative = $true  # 保守模式，只迁移最安全的程序
)

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "此脚本需要管理员权限。请以管理员身份运行PowerShell。"
    exit 1
}

# 配置
$SourceDrive = "C:"
$TargetDrive = "D:"
$BackupPath = "$TargetDrive\Migration_Backup"

# 要迁移的程序列表（保守模式）
$ConservativePrograms = @(
    @{Name="MongoDB"; Path="C:\Program Files\MongoDB"; Size=11.91},
    @{Name="JetBrains"; Path="C:\Program Files\JetBrains"; Size=3.14},
    @{Name="FeverGames"; Path="C:\Program Files\FeverGames"; Size=1.33}
)

# 积极模式额外程序
$AggressivePrograms = @(
    @{Name="Adobe"; Path="C:\Program Files\Adobe"; Size=4.21},
    @{Name="Docker"; Path="C:\Program Files\Docker"; Size=3.08},
    @{Name="Visual Studio"; Path="C:\Program Files\Microsoft Visual Studio"; Size=4.68}
)

function Test-DiskSpace {
    param($RequiredGB)
    
    $disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='$TargetDrive'"
    $freeSpaceGB = [math]::Round($disk.FreeSpace/1GB, 2)
    
    Write-Host "D盘可用空间: $freeSpaceGB GB"
    Write-Host "需要空间: $RequiredGB GB"
    
    if ($freeSpaceGB -lt $RequiredGB) {
        Write-Error "D盘空间不足！需要 $RequiredGB GB，但只有 $freeSpaceGB GB 可用。"
        return $false
    }
    return $true
}

function Backup-Registry {
    Write-Host "备份注册表..." -ForegroundColor Yellow
    
    if (-not (Test-Path $BackupPath)) {
        New-Item -Path $BackupPath -ItemType Directory -Force | Out-Null
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    
    try {
        reg export "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall" "$BackupPath\uninstall_$timestamp.reg" /y
        reg export "HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall" "$BackupPath\uninstall_x86_$timestamp.reg" /y
        Write-Host "注册表备份完成: $BackupPath" -ForegroundColor Green
    }
    catch {
        Write-Warning "注册表备份失败: $($_.Exception.Message)"
    }
}

function Stop-RelatedServices {
    param($ProgramName)
    
    $servicesToStop = @()
    
    switch ($ProgramName) {
        "MongoDB" { $servicesToStop = @("MongoDB") }
        "Docker" { $servicesToStop = @("Docker Desktop Service", "com.docker.service") }
        "Visual Studio" { $servicesToStop = @("VSStandardCollectorService150") }
    }
    
    foreach ($service in $servicesToStop) {
        try {
            $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
            if ($svc -and $svc.Status -eq 'Running') {
                Write-Host "停止服务: $service" -ForegroundColor Yellow
                if (-not $DryRun) {
                    Stop-Service -Name $service -Force
                }
            }
        }
        catch {
            Write-Warning "无法停止服务 $service : $($_.Exception.Message)"
        }
    }
}

function Start-RelatedServices {
    param($ProgramName)
    
    $servicesToStart = @()
    
    switch ($ProgramName) {
        "MongoDB" { $servicesToStart = @("MongoDB") }
        "Docker" { $servicesToStart = @("Docker Desktop Service", "com.docker.service") }
        "Visual Studio" { $servicesToStart = @("VSStandardCollectorService150") }
    }
    
    foreach ($service in $servicesToStart) {
        try {
            $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
            if ($svc) {
                Write-Host "启动服务: $service" -ForegroundColor Green
                if (-not $DryRun) {
                    Start-Service -Name $service
                }
            }
        }
        catch {
            Write-Warning "无法启动服务 $service : $($_.Exception.Message)"
        }
    }
}

function Move-Program {
    param($Program)
    
    $sourcePath = $Program.Path
    $programName = $Program.Name
    $targetPath = $sourcePath.Replace("C:\", "D:\")
    
    Write-Host "`n正在处理: $programName ($($Program.Size) GB)" -ForegroundColor Cyan
    Write-Host "源路径: $sourcePath"
    Write-Host "目标路径: $targetPath"
    
    if (-not (Test-Path $sourcePath)) {
        Write-Warning "源路径不存在，跳过: $sourcePath"
        return $false
    }
    
    if ($DryRun) {
        Write-Host "[预览模式] 将会移动 $sourcePath 到 $targetPath" -ForegroundColor Yellow
        return $true
    }
    
    try {
        # 停止相关服务
        Stop-RelatedServices -ProgramName $programName
        
        # 创建目标目录
        $targetDir = Split-Path $targetPath -Parent
        if (-not (Test-Path $targetDir)) {
            New-Item -Path $targetDir -ItemType Directory -Force | Out-Null
        }
        
        # 移动文件夹
        Write-Host "移动文件夹..." -ForegroundColor Yellow
        Move-Item -Path $sourcePath -Destination $targetPath -Force
        
        # 创建符号链接
        Write-Host "创建符号链接..." -ForegroundColor Yellow
        New-Item -ItemType SymbolicLink -Path $sourcePath -Target $targetPath -Force | Out-Null
        
        # 重启相关服务
        Start-RelatedServices -ProgramName $programName
        
        Write-Host "✓ $programName 迁移完成" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Error "迁移 $programName 失败: $($_.Exception.Message)"
        
        # 尝试回滚
        if (Test-Path $targetPath) {
            try {
                Move-Item -Path $targetPath -Destination $sourcePath -Force
                Write-Host "已回滚 $programName" -ForegroundColor Yellow
            }
            catch {
                Write-Error "回滚失败！请手动处理。"
            }
        }
        return $false
    }
}

# 主程序
Write-Host "=== 程序迁移工具 ===" -ForegroundColor Cyan
Write-Host "模式: $(if($DryRun){'预览模式'}else{'执行模式'})"
Write-Host "策略: $(if($Conservative){'保守模式'}else{'积极模式'})"

# 选择要迁移的程序
$programsToMigrate = $ConservativePrograms
if (-not $Conservative) {
    $programsToMigrate += $AggressivePrograms
}

# 计算总大小
$totalSize = ($programsToMigrate | Measure-Object -Property Size -Sum).Sum
Write-Host "`n计划迁移 $($programsToMigrate.Count) 个程序，总大小: $totalSize GB"

# 检查磁盘空间
if (-not (Test-DiskSpace -RequiredGB $totalSize)) {
    exit 1
}

# 显示迁移列表
Write-Host "`n迁移列表:" -ForegroundColor Yellow
foreach ($program in $programsToMigrate) {
    Write-Host "  - $($program.Name) ($($program.Size) GB)"
}

if (-not $DryRun) {
    $confirm = Read-Host "`n确认执行迁移？(y/N)"
    if ($confirm -ne 'y' -and $confirm -ne 'Y') {
        Write-Host "操作已取消。" -ForegroundColor Yellow
        exit 0
    }
    
    # 备份注册表
    Backup-Registry
}

# 执行迁移
$successCount = 0
$totalCount = $programsToMigrate.Count

foreach ($program in $programsToMigrate) {
    if (Move-Program -Program $program) {
        $successCount++
    }
}

Write-Host "`n=== 迁移完成 ===" -ForegroundColor Cyan
Write-Host "成功: $successCount/$totalCount"
Write-Host "预计释放空间: $([math]::Round(($programsToMigrate | Where-Object {$_.Name -in $successPrograms} | Measure-Object -Property Size -Sum).Sum, 2)) GB"

if (-not $DryRun) {
    Write-Host "`n建议重启计算机以确保所有更改生效。" -ForegroundColor Yellow
}
