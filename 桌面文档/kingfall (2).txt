一个基于 Minecraft Forge 1.12.2 的语言模型接入模组。
这是一个相当复杂的请求，涉及服务端/客户端通信、事件监听、指令处理、配置文件、异步HTTP请求、JSON解析以及Minecraft 1.12.2这个较老版本的特性。

我将把你当成一个新手，提供详细的结构和代码，并加上注释。

重要前提与免责声明：

环境配置：你需要自行配置好基本的 Forge 1.12.2 MDK 开发环境 (包括 build.gradle, gradlew 等)。此代码需要放入 src/main/java 目录。

API兼容性：代码将假定你的模型提供商的API接口遵循 OpenAI 的 Chat Completion 接口格式 (请求体包含 model, messages, temperature；响应体包含 choices[0].message.content)。如果你的API格式不同，你需要修改 ai/AIClient.java 以及 ai/api/ 下的类。

依赖：Minecraft Forge 1.12.2 自带 Gson 库 (用于JSON) 和 Apache HttpComponents / Java内置 HttpURLConnection (用于网络请求)。我们将使用 Gson 和 HttpURLConnection。

异步处理：网络请求是异步的，不会卡住主服务器线程。结果处理会被调度回主线程执行。

安全性：API Key 明文存储在配置文件中，请注意保护配置文件。

错误处理：包含了基本的错误处理，但无法覆盖所有网络或API异常。

性能与费用：高频调用API会产生费用并可能达到速率限制，请自行斟酌。公用模式可能会非常“吵闹”且昂贵。

1.12.2限制：老版本API有些笨拙，代码会尽量兼容。

代码量大: 请耐心复制。

文件结构

请在你的 src/main/java 下创建如下结构和文件：

src/main/java/
└── com
└── yourname
└── aimod
│
├── AiMod.java                    // 主类
├── ModConfig.java                // 配置文件管理
├── Reference.java                // 常量 (MODID, Version)
│
├── ai
│   ├── AIClient.java             // AI HTTP 请求逻辑
│   ├── ChatStateManager.java     // 聊天状态与历史记录管理
│   ├── ModelProfile.java         // 单个模型配置数据结构
│   └── api                        // API 请求/响应的 Java对象 (POJO)
│       ├── ApiMessage.java
│       ├── ApiRequest.java
│       ├── ApiResponse.java
│       └── Choice.java
│
├── command
│   └── CommandAiChat.java        // 指令处理
│
├── event
│    ├── ClientChatEventHandler.java // 客户端事件 (隐藏非私聊消息)
│    └── ServerChatEventHandler.java // 服务端聊天/登出事件
│
├── network                       // 数据包 (告知客户端进入/离开私聊)
├── MessageTogglePrivate.java // 数据包消息和处理器
└── PacketHandler.java        // 数据包注册中心
│
├── proxy
│   ├── ClientProxy.java          // 客户端代理
│   └── CommonProxy.java          // 通用(服务端)代理
│
└── util
└── ChatUtil.java           // 聊天消息发送工具

// 同时，在 src/main/resources 下创建:
src/main/resources/
└── mcmod.info // Mod信息文件

完整代码

请逐个文件复制粘贴：

src/main/resources/mcmod.info

[
{
"modid": "aimod",
"name": "AI Chat Mod",
"description": "Integrates Language Models into Minecraft Chat.",
"version": "1.0.0",
"mcversion": "1.12.2",
"url": "",
"updateUrl": "",
"authorList": ["AI"],
"credits": "",
"logoFile": "",
"screenshots": [],
"dependencies": []
}
]


com/yourname/aimod/Reference.java

package com.yourname.aimod;

public class Reference {
public static final String MODID = "aimod";
public static final String NAME = "AI Chat Mod";
public static final String VERSION = "1.0.0";
public static final String ACCEPTED_VERSIONS = "[1.12.2]";
public static final String CLIENT_PROXY_CLASS = "com.yourname.aimod.proxy.ClientProxy";
public static final String COMMON_PROXY_CLASS = "com.yourname.aimod.proxy.CommonProxy";
public static final String CHANNEL_NAME = "aimodchannel";
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/proxy/CommonProxy.java

package com.yourname.aimod.proxy;

import com.yourname.aimod.ModConfig;
import com.yourname.aimod.event.ServerChatEventHandler;
import com.yourname.aimod.network.PacketHandler;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPostInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;

public class CommonProxy {

Generated code
public void preInit(FMLPreInitializationEvent event) {
     ModConfig.loadConfig(event.getSuggestedConfigurationFile());
     PacketHandler.init();
}

public void init(FMLInitializationEvent event) {
     MinecraftForge.EVENT_BUS.register(new ServerChatEventHandler());
}

public void postInit(FMLPostInitializationEvent event) {

}
 
 // 客户端用来检查自己是否在私聊模式，服务端调用返回false
 public boolean isClientInPrivateMode() {
    return false;
 }
 // 客户端用来设置自己是否在私聊模式，服务端调用为空
 public void setClientPrivateMode(boolean active){
    // Only on client
 }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/proxy/ClientProxy.java

package com.yourname.aimod.proxy;

import com.yourname.aimod.event.ClientChatEventHandler;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPostInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

// 标记只在客户端加载
@SideOnly(Side.CLIENT)
public class ClientProxy extends CommonProxy {

// 标记玩家客户端当前是否处于私聊模式（用于屏蔽他人聊天）
private boolean clientInPrivateMode = false;

public static ClientProxy INSTANCE;

Generated code
@Override
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

public void preInit(FMLPreInitializationEvent event) {
super.preInit(event);
INSTANCE = this; // Set instance for packet handler access
}

@Override
public void init(FMLInitializationEvent event) {
super.init(event);
// 注册客户端独有的事件监听器
MinecraftForge.EVENT_BUS.register(new ClientChatEventHandler());
}

Generated code
@Override
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

public void postInit(FMLPostInitializationEvent event) {
super.postInit(event);
}

Generated code
@Override
 public boolean isClientInPrivateMode() {
   return clientInPrivateMode;
}
 @Override
 public void setClientPrivateMode(boolean active){
    this.clientInPrivateMode = active;
     // net.minecraft.client.Minecraft.getMinecraft().player.sendMessage(new net.minecraft.util.text.TextComponentString("Client Private Mode: " + active));
 }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/AiMod.java

package com.yourname.aimod;

import com.yourname.aimod.ai.ChatStateManager;
import com.yourname.aimod.command.CommandAiChat;
import com.yourname.aimod.proxy.CommonProxy;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.Mod.EventHandler;
import net.minecraftforge.fml.common.Mod.Instance;
import net.minecraftforge.fml.common.SidedProxy;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPostInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.common.event.FMLServerStartingEvent;
import net.minecraftforge.fml.common.event.FMLServerStoppingEvent;
import org.apache.logging.log4j.Logger;

@Mod(modid = Reference.MODID, name = Reference.NAME, version = Reference.VERSION, acceptedMinecraftVersions = Reference.ACCEPTED_VERSIONS, acceptableRemoteVersions="*") // acceptableRemoteVersions 允许客户端/服务端只装一个也能连接，但功能不全
public class AiMod
{
@Instance
public static AiMod instance;

Generated code
public static Logger logger;

 @SidedProxy(clientSide = Reference.CLIENT_PROXY_CLASS, serverSide = Reference.COMMON_PROXY_CLASS)
 public static CommonProxy proxy;

 @EventHandler
 public void preInit(FMLPreInitializationEvent event) {
     logger = event.getModLog();
     proxy.preInit(event);
     logger.info("AI Mod Pre-Initialization");
 }

 @EventHandler
 public void init(FMLInitializationEvent event) {
      proxy.init(event);
       logger.info("AI Mod Initialization");
 }
  @EventHandler
 public void postInit(FMLPostInitializationEvent event) {
       proxy.postInit(event);
        logger.info("AI Mod Post-Initialization");
 }

  @EventHandler
 public void serverStarting(FMLServerStartingEvent event) {
       // 注册我们的指令
	 event.registerServerCommand(new CommandAiChat());
       ChatStateManager.INSTANCE.clearAll(); // Clear state on server start
        logger.info("AI Mod Command Registered and State Cleared.");
 }
 
  @EventHandler
 public void serverStopping(FMLServerStoppingEvent event) {
      // 服务器关闭时清理状态
      ChatStateManager.INSTANCE.clearAll();
       logger.info("AI Mod State Cleared on Server Stop.");
 }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/ModConfig.java

package com.yourname.aimod;

import com.yourname.aimod.ai.ModelProfile;
import net.minecraftforge.common.config.Configuration;
import net.minecraftforge.common.config.Property;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ModConfig {

Generated code
public static Configuration config;
 // 存储所有从配置加载的模型 <模型名称, 模型配置>
 public static final Map<String, ModelProfile> modelProfiles = new HashMap<>();

 public static void loadConfig(File configFile) {
    config = new Configuration(configFile);
     config.load();
     syncConfig();
 }

 public static void reloadConfig() {
    if(config != null) {
       config.load();
        syncConfig();
         AiMod.logger.info("AI Mod Config reloaded.");
    }
 }

 public static void syncConfig() {
     modelProfiles.clear();
      AiMod.logger.info("Loading AI Model Profiles from config...");

     // 定义默认模型方便用户参考
     defineDefaultModels();

     // 遍历配置中所有以 "model_" 开头的 Category
     for (String categoryName : config.getCategoryNames()) {
         if (categoryName.startsWith("model_")) {
             String profileName = categoryName.substring("model_".length());
             if (profileName.isEmpty()) continue;

             try {
                 boolean isPublic = config.getBoolean("isPublic", categoryName, false, "Set to true for public model, false for private model");
                 String baseUrl = config.getString("baseUrl", categoryName, "http://localhost:1234/v1/chat/completions", "API Base URL");
                 String apiKey = config.getString("apiKey", categoryName, "lm-studio", "API Key (use 'none' or empty if not required)");
                 String modelId = config.getString("modelId", categoryName, "model-id-from-provider", "Model ID string required by the provider");
                 float temperature = config.getFloat("temperature", categoryName, 0.7f, 0.0f, 2.0f, "Model temperature (creativity)");
                 int maxContext = config.getInt("maxContext", categoryName, 5, 1, 100, "Number of past messages (user+ai) to remember");
                 String systemPrompt = config.getString("systemPrompt", categoryName, "You are a helpful assistant in Minecraft.", "System prompt / Preset");
                 
                 // 读取关键词指令 S:keywordActions < keyword1::/say cmd1|/give @p diamond keyword2::/time set day >
                 Map<String, List<String>> keywordActions = new HashMap<>();
                  String[] actionsArray = config.getStringList("keywordActions", categoryName, new String[]{}, 
                     "Keyword actions format: keyword::/command1|/command2. Each entry is one keyword mapping. Commands run via console.");
                  for(String actionEntry : actionsArray) {
                       // 格式 keyword::/cmd1|/cmd2
                      String[] parts = actionEntry.split("::", 2);
                      if(parts.length == 2) {
                          String keyword = parts[0].trim();
                          String[] commands = parts[1].split("\\|");
                           List<String> commandList = new ArrayList<>();
                            for(String cmd : commands) {
                                 if(!cmd.trim().isEmpty()) commandList.add(cmd.trim());
                            }
                            if(!keyword.isEmpty() && !commandList.isEmpty()){
                                 keywordActions.put(keyword, commandList);
                                 AiMod.logger.info("Loaded keyword action for '"+ profileName +"': [" + keyword + "] -> " + String.join(", ", commandList) );
                            }
                      }
                  }

                 ModelProfile profile = new ModelProfile(
                         profileName, isPublic, baseUrl, apiKey, modelId,
                         temperature, maxContext, systemPrompt, keywordActions);
                 modelProfiles.put(profileName.toLowerCase(), profile);
                  AiMod.logger.info("Loaded " + (isPublic ? "PUBLIC" : "PRIVATE") + " profile: " + profileName);

             } catch (Exception e) {
                  AiMod.logger.error("Error loading model profile category: " + categoryName, e);
             }
         }
     }
      if (config.hasChanged()) {
         config.save();
     }
      AiMod.logger.info("Loaded " + modelProfiles.size() + " model profiles.");
 }
 
 // 创建默认配置项，如果它们不存在
 private static void defineDefaultModels(){
    String catPub = "model_public_bot";
     config.getBoolean("isPublic", catPub, true, "Set to true for public model, false for private model");
     config.getString("baseUrl", catPub, "http://127.0.0.1:1234/v1/chat/completions", "API Base URL");
      config.getString("apiKey", catPub, "no-key", "API Key");
       config.getString("modelId", catPub, "default-model", "Model ID");
       config.getFloat("temperature", catPub, 0.8f, 0.0f, 2.0f, "Model temperature");
        config.getInt("maxContext", catPub, 6, 1, 100, "Context length");
         config.getString("systemPrompt", catPub, "You are MineBot, a helpful chat bot in a Minecraft server. Keep responses brief, friendly and relevant. Always mention player name when replying.");
         config.getStringList("keywordActions", catPub, new String[]{"diamond time::/give @p diamond 1|/say Here is your diamond!","day lease::/time set day|/weather clear"}, "Keyword Actions");

      String catPriv = "model_private_guide";
      config.getBoolean("isPublic", catPriv, false, "Set to true for public model, false for private model");
       config.getString("baseUrl", catPriv, "http://127.0.0.1:1234/v1/chat/completions", "API Base URL");
        config.getString("apiKey", catPriv, "no-key", "API Key");
         config.getString("modelId", catPriv, "private-model", "Model ID");
          config.getFloat("temperature", catPriv, 0.6f, 0.0f, 2.0f, "Model temperature");
           config.getInt("maxContext", catPriv, 10, 1, 100, "Context length");
            config.getString("systemPrompt", catPriv, "You are a private Minecraft guide, assisting one player. Be detailed and roleplay slightly.");
   		 config.getStringList("keywordActions", catPriv, new String[]{}, "Keyword Actions");
 }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/ai/ModelProfile.java

package com.yourname.aimod.ai;

import java.util.Collections;
import java.util.List;
import java.util.Map;

// 存储单个模型的配置信息
public class ModelProfile {
public final String profileName;
public final boolean isPublic;
public final String baseUrl;
public final String apiKey;
public final String modelId;
public final float temperature;
public final int maxContext;
public final String systemPrompt;
public final Map<String, List<String>> keywordActions;

Generated code
public ModelProfile(String profileName, boolean isPublic, String baseUrl, String apiKey, String modelId, float temperature, int maxContext, String systemPrompt,  Map<String, List<String>> keywordActions) {
    this.profileName = profileName;
    this.isPublic = isPublic;
    this.baseUrl = baseUrl;
    // 如果apikey是 "none" 或空，则设为 null
    this.apiKey = (apiKey == null || apiKey.isEmpty() || apiKey.equalsIgnoreCase("none") || apiKey.equalsIgnoreCase("no-key")) ? null : apiKey;
    this.modelId = modelId;
    this.temperature = temperature;
    this.maxContext = Math.max(1, maxContext); // 至少保留1条
    this.systemPrompt = systemPrompt;
     this.keywordActions = keywordActions != null ? keywordActions : Collections.emptyMap();
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/ai/ChatStateManager.java

package com.yourname.aimod.ai;

import com.google.common.collect.EvictingQueue;
import com.yourname.aimod.AiMod;
import com.yourname.aimod.ModConfig;
import com.yourname.aimod.ai.api.ApiMessage;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

// 聊天状态与历史记录管理器 (单例)
// 注意：历史记录操作需要考虑线程安全，或保证只在主线程操作
public class ChatStateManager {

Generated code
public static final ChatStateManager INSTANCE = new ChatStateManager();

private volatile String activePublicModelName = null;
// <玩家UUID, 私聊模型名称>
private final Map<UUID, String> privateChatSessions = new ConcurrentHashMap<>();
// <模型名称 或 玩家UUID, 聊天历史>
// 公用模式key=模型名称, 私有模式key=玩家UUID
private final Map<String, Queue<ApiMessage>> chatHistories = new ConcurrentHashMap<>();

 // 正在处理中的请求集合，防止短时间重复触发
 private final Set<String> processingKeys = Collections.newSetFromMap(new ConcurrentHashMap<>());

private ChatStateManager() {}

@Nullable
public ModelProfile getActivePublicModel() {
    if (activePublicModelName == null) return null;
    return ModConfig.modelProfiles.get(activePublicModelName);
}

 public String getActivePublicModelName() {
   return activePublicModelName;
}

// 设置或关闭公用模型，只在主线程调用
public boolean setPublicModel(@Nullable String modelName) {
   if(modelName == null) {
        if(activePublicModelName != null){
            chatHistories.remove(activePublicModelName); // 清除历史
            processingKeys.remove(activePublicModelName);
        }
       activePublicModelName = null;
       return true;
   }
    ModelProfile profile = ModConfig.modelProfiles.get(modelName.toLowerCase());
    if (profile != null && profile.isPublic) {
        if(activePublicModelName != null && !activePublicModelName.equals(modelName.toLowerCase())){
             chatHistories.remove(activePublicModelName); // 切换模型时清除旧历史
              processingKeys.remove(activePublicModelName);
        }
        activePublicModelName = modelName.toLowerCase();
        // 初始化历史队列
         chatHistories.computeIfAbsent(activePublicModelName, k -> EvictingQueue.create(profile.maxContext));
        return true;
    }
    return false; // 模型不存在或不是公用模型
}

@Nullable
public ModelProfile getPrivateSessionModel(UUID playerUUID) {
    String modelName = privateChatSessions.get(playerUUID);
    if (modelName == null) return null;
    return ModConfig.modelProfiles.get(modelName);
}
 
 public boolean isPlayerInPrivateSession(UUID playerUUID) {
     return privateChatSessions.containsKey(playerUUID);
 }

// 开启玩家私聊，只在主线程调用
public boolean startPrivateSession(UUID playerUUID, String modelName) {
    ModelProfile profile = ModConfig.modelProfiles.get(modelName.toLowerCase());
     // 检查模型是否存在，是否为私有模型，以及玩家当前是否不在任何私聊中
    if (profile != null && !profile.isPublic && !privateChatSessions.containsKey(playerUUID)) {
        privateChatSessions.put(playerUUID, modelName.toLowerCase());
         // 初始化历史队列，key为UUID
         chatHistories.computeIfAbsent(playerUUID.toString(), k -> EvictingQueue.create(profile.maxContext));
        return true;
    }
    return false;
}

// 结束玩家私聊，只在主线程调用
public boolean endPrivateSession(UUID playerUUID) {
    if (privateChatSessions.remove(playerUUID) != null) {
         chatHistories.remove(playerUUID.toString()); // 清除历史
         processingKeys.remove(playerUUID.toString());
        return true;
    }
    return false;
}

 // 玩家登出时调用
 public void onPlayerLogout(UUID playerUUID){
    endPrivateSession(playerUUID);
 }

// 添加消息到历史，只在主线程调用
public void addMessage(String key, ApiMessage message, int maxContext) {
     // 使用 Guava 的 EvictingQueue 自动保持大小
    Queue<ApiMessage> history = chatHistories.computeIfAbsent(key, k -> EvictingQueue.create(maxContext));
     // 检查并更新队列大小，如果配置被修改了
     if(history instanceof EvictingQueue && ((EvictingQueue<ApiMessage>) history).size() >= maxContext && maxContext > 0){
        // A bit hacky to resize, recreate if needed or just let evicting queue handle it
        // For simplicity, we rely on EvictingQueue created with initial maxContext. Dynamic resize is complex.
        // If config changes, queue size only updates on session restart or model switch.
    }
   history.add(message);
 }
 
  // 添加玩家消息并尝试触发AI
  public void processUserMessageAndTriggerAI(String key, ModelProfile profile, String playerName, String content, AIClient.AIResponseCallback callback){
      if(isProcessing(key)) {
          AiMod.logger.warn("AI is still processing for key: " + key + ". Ignoring new message.");
          return; // 如果AI还在处理，则忽略
      }
      setProcessing(key, true);
       addMessage(key, new ApiMessage("user", content, playerName), profile.maxContext);
       List<ApiMessage> context = getContext(key, profile);
       AIClient.callAI(profile, context, response -> {
            // --- 以下代码会在服务器主线程执行 (由 callAI 保证) ---
            // 无论成功失败，标记为处理完成
             setProcessing(key, false);
             if(response.isSuccess()){
                  // AI回复也加入历史
                 addMessage(key, new ApiMessage("assistant", response.getContent()), profile.maxContext);
             }
             callback.onResponse(response);
              // ---------------------------------------------------
       });
  }


// 获取上下文（包含系统提示），只在主线程调用
public List<ApiMessage> getContext(String key, ModelProfile profile) {
    List<ApiMessage> context = new ArrayList<>();
    if(profile.systemPrompt != null && !profile.systemPrompt.isEmpty()){
       context.add(new ApiMessage("system", profile.systemPrompt));
    }
    Queue<ApiMessage> history = chatHistories.get(key);
    if (history != null) {
        context.addAll(history);
    }
    return context;
}

 public boolean isProcessing(String key) {
     return processingKeys.contains(key);
 }

 public void setProcessing(String key, boolean processing) {
    if (processing) {
        processingKeys.add(key);
    } else {
        processingKeys.remove(key);
    }
 }
 
 public void clearAll() {
      activePublicModelName = null;
      privateChatSessions.clear();
      chatHistories.clear();
      processingKeys.clear();
 }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/ai/AIClient.java

package com.yourname.aimod.ai;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.yourname.aimod.AiMod;
import com.yourname.aimod.ai.api.ApiMessage;
import com.yourname.aimod.ai.api.ApiRequest;
import com.yourname.aimod.ai.api.ApiResponse;
import com.yourname.aimod.ai.api.Choice;
import net.minecraft.server.MinecraftServer;
import net.minecraftforge.fml.common.FMLCommonHandler;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.CompletableFuture;

// 处理 HTTP 请求和 JSON 解析
public class AIClient {

Generated code
private static final Gson GSON = new GsonBuilder().create();
 private static final int TIMEOUT_MS = 20000; // 20 seconds timeout

// 异步调用AI
public static void callAI(final ModelProfile profile, final List<ApiMessage> messages, final AIResponseCallback callback) {

    CompletableFuture.supplyAsync(() -> {
        // --- 这部分在后台线程执行 ---
        HttpURLConnection connection = null;
        try {
            URL url = new URL(profile.baseUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; utf-8");
            connection.setRequestProperty("Accept", "application/json");
             connection.setConnectTimeout(TIMEOUT_MS);
             connection.setReadTimeout(TIMEOUT_MS);
            if (profile.apiKey != null) {
                connection.setRequestProperty("Authorization", "Bearer " + profile.apiKey);
            }
            connection.setDoOutput(true);

            ApiRequest requestBody = new ApiRequest(profile.modelId, messages, profile.temperature);
            String jsonInputString = GSON.toJson(requestBody);
            // AiMod.logger.info("AI Request: " + jsonInputString);

            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonInputString.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            int responseCode = connection.getResponseCode();
            if (responseCode >= 200 && responseCode < 300) {
                 try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder responseBuilder = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        responseBuilder.append(responseLine.trim());
                    }
                    String jsonResponse = responseBuilder.toString();
                   //  AiMod.logger.info("AI Response: " + jsonResponse);
                     
                    ApiResponse response = GSON.fromJson(jsonResponse, ApiResponse.class);
                     if (response != null && response.choices != null && !response.choices.isEmpty()) {
                        Choice firstChoice = response.choices.get(0);
                         if (firstChoice != null && firstChoice.message != null && firstChoice.message.content != null) {
                             return AIResponse.success(firstChoice.message.content);
                         }
                    }
                     return AIResponse.error("Invalid or empty response structure from API.");
                 }
            } else {
                 // 读取错误流
                  StringBuilder errorResponse = new StringBuilder();
                   try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                      String responseLine;
                       while ((responseLine = br.readLine()) != null) {
                           errorResponse.append(responseLine.trim());
                      }
                  } catch(Exception ignore){}
                 return AIResponse.error("API Error: HTTP " + responseCode + " - "+ connection.getResponseMessage() + " | " + errorResponse.toString());
            }

        } catch (Exception e) {
            AiMod.logger.error("Error during AI API call for model " + profile.profileName, e);
            return AIResponse.error("Exception during API call: " + e.getMessage());
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
         // --- 后台线程结束 ---
    }).thenAccept(response -> {
         // 将回调函数调度回主服务器线程执行
        MinecraftServer server = FMLCommonHandler.instance().getMinecraftServerInstance();
        if (server != null) {
            server.addScheduledTask(() -> callback.onResponse(response));
        } else {
           // 如果服务器不在，至少记录错误
            AiMod.logger.error("Could not get server instance to schedule AI response callback!");
             if(!response.isSuccess()) AiMod.logger.error("Original AI Error: " + response.getError());
        }
    });
}

 // 回调接口
public interface AIResponseCallback {
    void onResponse(AIResponse response);
}

 // 响应封装类
 public static class AIResponse {
    private final boolean success;
    private final String content; // null if error
    private final String error;   // null if success

     private AIResponse(boolean success, String content, String error) {
         this.success = success;
         this.content = content;
         this.error = error;
     }
     public static AIResponse success(String content) { return new AIResponse(true, content, null); }
      public static AIResponse error(String error) { return new AIResponse(false, null, error); }
       public boolean isSuccess() { return success; }
        public String getContent() { return content; }
         public String getError() { return error; }
 }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/ai/api/ApiMessage.java

package com.yourname.aimod.ai.api;
import javax.annotation.Nullable;

// OpenAI 消息格式
public class ApiMessage {
public String role; // "system", "user", "assistant"
public String content;
@Nullable
public String name; // Optional: name of the user

Generated code
public ApiMessage(String role, String content) {
   this(role, content, null);
}
 public ApiMessage(String role, String content, @Nullable String name) {
    this.role = role;
    this.content = content;
    this.name = name; // only useful for role=user usually
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/ai/api/ApiRequest.java

package com.yourname.aimod.ai.api;
import java.util.List;

// OpenAI 请求体
public class ApiRequest {
public String model;
public List<ApiMessage> messages;
public float temperature;
public boolean stream = false; // We don't support streaming here

Generated code
public ApiRequest(String model, List<ApiMessage> messages, float temperature) {
       this.model = model;
       this.messages = messages;
       this.temperature = temperature;
  }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/ai/api/Choice.java

package com.yourname.aimod.ai.api;

// OpenAI 响应中的 choice 部分
public class Choice {
public int index;
public ApiMessage message;
public String finish_reason;
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/ai/api/ApiResponse.java

package com.yourname.aimod.ai.api;
import java.util.List;

// OpenAI 响应体
public class ApiResponse {
public String id;
public String object;
public long created;
public String model;
public List<Choice> choices;
// usage not mapped
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/util/ChatUtil.java

package com.yourname.aimod.util;

import com.yourname.aimod.AiMod;
import com.yourname.aimod.ai.ModelProfile;
import net.minecraft.command.ICommandSender;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.text.ITextComponent;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;
import net.minecraftforge.fml.common.FMLCommonHandler;

import java.util.List;
import java.util.Map;

public class ChatUtil {

Generated code
// 私聊消息标识符, 用于客户端识别并放行这些消息，同时屏蔽其他消息
// 使用颜色代码作为一种隐藏标记
public static final String PRIVATE_MARKER = TextFormatting.DARK_PURPLE.toString() + TextFormatting.OBFUSCATED.toString() + "P" + TextFormatting.RESET.toString();
public static final String PUBLIC_PREFIX = TextFormatting.AQUA + "[AI] ";
public static final String PRIVATE_AI_PREFIX = PRIVATE_MARKER + TextFormatting.LIGHT_PURPLE + "[Private AI] ";
 public static final String PRIVATE_USER_PREFIX = PRIVATE_MARKER + TextFormatting.GRAY + "[You] ";
 public static final String ERROR_PREFIX = TextFormatting.RED + "[AI Error] ";
  public static final String INFO_PREFIX = TextFormatting.YELLOW + "[AI Mod] ";

public static void sendPublicMessage(MinecraftServer server, String modelName, String message) {
    if (server != null) {
         ITextComponent component = new TextComponentString(PUBLIC_PREFIX + TextFormatting.WHITE + "<"+modelName+"> " + message);
        server.getPlayerList().sendMessage(component);
    }
}

 public static void sendPrivateAiMessage(EntityPlayerMP player, String message) {
    if (player != null) {
        player.sendMessage(new TextComponentString(PRIVATE_AI_PREFIX + TextFormatting.WHITE + message));
    }
 }
  // 私聊时，玩家自己消息的回显
  public static void sendPrivateUserEcho(EntityPlayerMP player, String message) {
     if (player != null) {
         player.sendMessage(new TextComponentString(PRIVATE_USER_PREFIX + TextFormatting.ITALIC + message));
     }
  }

 public static void sendError(ICommandSender sender, String message) {
     if(sender != null){
         sender.sendMessage(new TextComponentString(ERROR_PREFIX + message));
     }
     AiMod.logger.error(message);
 }
  public static void sendInfo(ICommandSender sender, String message) {
      if(sender != null){
         sender.sendMessage(new TextComponentString(INFO_PREFIX + message));
      }
   }
 
 // 检查关键词并执行指令
 public static void checkAndExecuteCommands(ModelProfile profile, String aiResponseText, String playerName) {
      MinecraftServer server = FMLCommonHandler.instance().getMinecraftServerInstance();
       if(server == null || profile == null || aiResponseText == null || aiResponseText.isEmpty() || profile.keywordActions.isEmpty()) {
          return;
      }
       String textLower = aiResponseText.toLowerCase();

       for(Map.Entry<String, List<String>> entry : profile.keywordActions.entrySet()) {
           String keyword = entry.getKey().toLowerCase();
            if(textLower.contains(keyword)) {
                 AiMod.logger.info("Keyword '"+ keyword +"' detected for model '"+ profile.profileName +"'. Executing commands...");
                 List<String> commands = entry.getValue();
                 for(String command : commands) {
                     try {
                         // 替换指令中的 @p 为触发AI的玩家名字 (只替换精确的 @p)
                          String commandToExecute = command.replaceAll("\\@p\\b", playerName);
                          AiMod.logger.info("Executing: /" + commandToExecute);
                          // 以服务器控制台身份执行指令
                         server.getCommandManager().executeCommand(server, commandToExecute);
                     } catch (Exception e) {
                          AiMod.logger.error("Failed to execute command: " + command, e);
                     }
                 }
            }
       }
 }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/event/ServerChatEventHandler.java

package com.yourname.aimod.event;

import com.yourname.aimod.AiMod;
import com.yourname.aimod.ai.AIClient;
import com.yourname.aimod.ai.ChatStateManager;
import com.yourname.aimod.ai.ModelProfile;
import com.yourname.aimod.util.ChatUtil;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.server.MinecraftServer;
import net.minecraftforge.event.ServerChatEvent;
import net.minecraftforge.fml.common.FMLCommonHandler;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.PlayerEvent;

import java.util.UUID;

// 服务端事件处理
public class ServerChatEventHandler {

Generated code
private final ChatStateManager stateManager = ChatStateManager.INSTANCE;

@SubscribeEvent
public void onServerChat(ServerChatEvent event) {
    EntityPlayerMP player = event.getPlayer();
    String message = event.getMessage();
    UUID playerUUID = player.getUniqueID();
    String playerName = player.getName();
     MinecraftServer server = FMLCommonHandler.instance().getMinecraftServerInstance();
      if(server == null) return;

    // 忽略指令
    if (message.startsWith("/")) {
        return;
    }

    // 1. 检查玩家是否在私聊模式
    ModelProfile privateModel = stateManager.getPrivateSessionModel(playerUUID);
    if (privateModel != null) {
        event.setCanceled(true); // 取消消息广播给其他人
        ChatUtil.sendPrivateUserEcho(player, message); // 把消息回显给玩家自己（带标记）
        
         stateManager.processUserMessageAndTriggerAI(playerUUID.toString(), privateModel, playerName, message, response -> {
             // --- 在主线程执行 ---
             if(response.isSuccess()){
                  ChatUtil.sendPrivateAiMessage(player, response.getContent());
                  ChatUtil.checkAndExecuteCommands(privateModel, response.getContent(), playerName); // 检查关键词执行指令
             } else {
                 ChatUtil.sendError(player, "Private AI Error: " + response.getError());
             }
              // --------------------
         });
         return; // 私聊模式下不处理公聊
    }

    // 2. 检查公用AI是否激活
    ModelProfile publicModel = stateManager.getActivePublicModel();
    if (publicModel != null) {
         // 消息正常广播，但AI也会看到
         // 忽略空消息或纯空格
          if(message.trim().isEmpty()) return;
          // 可选: 添加触发词检测, 例如: if(!message.toLowerCase().startsWith("ai,")) return;
          
           String publicModelKey = stateManager.getActivePublicModelName();
           stateManager.processUserMessageAndTriggerAI(publicModelKey, publicModel, playerName, message, response -> {
                // --- 在主线程执行 ---
                 if(response.isSuccess()){
                       ChatUtil.sendPublicMessage(server, publicModel.profileName, response.getContent());
                        ChatUtil.checkAndExecuteCommands(publicModel, response.getContent(), playerName); // 检查关键词执行指令
                 } else {
                      // 公共错误只发给控制台和OP, 避免刷屏
                      AiMod.logger.error("Public AI ("+publicModel.profileName+") Error: " + response.getError());
                       ChatUtil.sendError(server, "Public AI Error: " + response.getError()); // send to console
                 }
                // --------------------
           });
    }
}
 
// 玩家退出事件
 @SubscribeEvent
 public void onPlayerLogout(PlayerEvent.PlayerLoggedOutEvent event) {
      if(event.player instanceof EntityPlayerMP) {
          // 玩家退出时，清理其私聊状态和历史记录
         stateManager.onPlayerLogout(event.player.getUniqueID());
           // AiMod.logger.info("Cleaned up AI session for player: " + event.player.getName());
      }
 }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/event/ClientChatEventHandler.java

package com.yourname.aimod.event;

import com.yourname.aimod.AiMod;
import com.yourname.aimod.proxy.ClientProxy;
import com.yourname.aimod.util.ChatUtil;
import net.minecraftforge.client.event.ClientChatReceivedEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraft.util.text.TextComponentString;

// 客户端事件处理，只在客户端加载和注册
@SideOnly(Side.CLIENT)
public class ClientChatEventHandler {

Generated code
@SubscribeEvent
 public void onClientChatReceived(ClientChatReceivedEvent event) {
      // 检查客户端代理，判断当前玩家是否处于私聊模式
      if (AiMod.proxy.isClientInPrivateMode()) {
           // 获取包含格式代码的消息文本
          String formattedText = event.getMessage().getFormattedText();
          // 如果消息不是以私聊标记开头的，就取消显示
          if (!formattedText.startsWith(ChatUtil.PRIVATE_MARKER)) {
               event.setCanceled(true);
          } 
          /*
           // 可选: 如果你想移除标记再显示, 但可能丢失部分格式, 且需要小心处理
          else {
              try {
                   String cleanText = formattedText.substring(ChatUtil.PRIVATE_MARKER.length());
                    event.setMessage(new TextComponentString(cleanText));
              } catch (Exception e){
                  // fallback, keep original if error
              }
          }
          */
      }
 }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/command/CommandAiChat.java

package com.yourname.aimod.command;

import com.yourname.aimod.AiMod;
import com.yourname.aimod.ModConfig;
import com.yourname.aimod.ai.ChatStateManager;
import com.yourname.aimod.ai.ModelProfile;
import com.yourname.aimod.network.MessageTogglePrivate;
import com.yourname.aimod.network.PacketHandler;
import com.yourname.aimod.util.ChatUtil;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.command.WrongUsageException;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.TextFormatting;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class CommandAiChat extends CommandBase {
private final ChatStateManager stateManager = ChatStateManager.INSTANCE;

Generated code
@Nonnull
 @Override
 public String getName() {
     return "aichat";
 }

 @Nonnull
 @Override
 public String getUsage(@Nonnull ICommandSender sender) {
      return "/aichat public <ModelName> on\n" +
             "/aichat public off\n" +
             "/aichat private <ModelName> <PlayerSelector> on\n" +
             "/aichat private <PlayerSelector> off\n" +
             "/aichat reload\n" +
             "/aichat list";
 }

  @Override
 public int getRequiredPermissionLevel() {
     return 2; // 需要OP权限
 }

 @Override
 public void execute(@Nonnull MinecraftServer server, @Nonnull ICommandSender sender, @Nonnull String[] args) throws CommandException {
     if (args.length == 0) {
         throw new WrongUsageException(getUsage(sender));
     }

     String subCommand = args[0].toLowerCase();

     switch (subCommand) {
         case "reload":
             ModConfig.reloadConfig();
             stateManager.clearAll(); // 重载配置后清除所有状态
             ChatUtil.sendInfo(sender, "Config reloaded and all AI states cleared.");
             break;
         
          case "list":
             ChatUtil.sendInfo(sender, "Available Models:");
              ModConfig.modelProfiles.values().forEach(p -> 
                   ChatUtil.sendInfo(sender, "- " + p.profileName + " (" + (p.isPublic ? "PUBLIC" : "PRIVATE") + ")")
              );
               String activePublic = stateManager.getActivePublicModelName();
               ChatUtil.sendInfo(sender, "Active Public: " + (activePublic == null ? "None" : activePublic));
              break;

         case "public":
             // /aichat public <ModelName> on  OR /aichat public off
             handlePublic(sender, args);
             break;

         case "private":
              // /aichat private <ModelName> <Selector> on OR /aichat private <Selector> off
             handlePrivate(server, sender, args);
             break;

         default:
             throw new WrongUsageException(getUsage(sender));
     }
 }

  private void handlePublic(ICommandSender sender, String[] args) throws CommandException {
     if (args.length == 2 && args[1].equalsIgnoreCase("off")) {
          String oldModel = stateManager.getActivePublicModelName();
         stateManager.setPublicModel(null);
         ChatUtil.sendInfo(sender, "Public AI chat disabled." + (oldModel != null ? " Model: " + oldModel : ""));
         return;
     }
     if (args.length == 3 && args[2].equalsIgnoreCase("on")) {
         String modelName = args[1].toLowerCase();
          ModelProfile profile = ModConfig.modelProfiles.get(modelName);
           if(profile == null){
               ChatUtil.sendError(sender, "Model '" + modelName + "' not found.");
                return;
           }
           if(!profile.isPublic){
                ChatUtil.sendError(sender, "Model '" + modelName + "' is not configured as public.");
                return;
           }
         if (stateManager.setPublicModel(modelName)) {
             ChatUtil.sendInfo(sender, "Public AI chat enabled with model: " + TextFormatting.GREEN + modelName);
         } else {
             ChatUtil.sendError(sender, "Failed to enable public AI with model: " + modelName + ". Check if model exists and is public.");
         }
         return;
     }
      throw new WrongUsageException("Usage: /aichat public <ModelName> on | /aichat public off");
 }
  
   private void handlePrivate(MinecraftServer server, ICommandSender sender, String[] args) throws CommandException {
       List<EntityPlayerMP> targets;
       // Case 1: /aichat private <Selector> off
       if (args.length == 3 && args[2].equalsIgnoreCase("off")) {
           targets = getPlayers(server, sender, args[1]);
           for (EntityPlayerMP target : targets) {
               if(stateManager.isPlayerInPrivateSession(target.getUniqueID())){
                   stateManager.endPrivateSession(target.getUniqueID());
                    PacketHandler.INSTANCE.sendTo(new MessageTogglePrivate(false), target); // 告知客户端关闭屏蔽
                    ChatUtil.sendInfo(target, "Your private AI chat session has been closed by admin.");
                     ChatUtil.sendInfo(sender, "Closed private AI session for " + target.getName());
               } else {
                    ChatUtil.sendInfo(sender,  target.getName() + " was not in a private session.");
               }
           }
           return;
       }
        // Case 2: /aichat private <ModelName> <Selector> on
        if (args.length == 4 && args[3].equalsIgnoreCase("on")) {
           String modelName = args[1].toLowerCase();
           ModelProfile profile = ModConfig.modelProfiles.get(modelName);
            if(profile == null){
                ChatUtil.sendError(sender, "Model '" + modelName + "' not found.");
                return;
            }
             if(profile.isPublic){
                 ChatUtil.sendError(sender, "Model '" + modelName + "' is configured as public, cannot be used for private chat.");
                return;
             }

           targets = getPlayers(server, sender, args[2]);
            for (EntityPlayerMP target : targets) {
                if(stateManager.isPlayerInPrivateSession(target.getUniqueID())) {
                     ChatUtil.sendInfo(sender, target.getName() + " is already in a private session. Close it first.");
                     continue;
                }
                if(stateManager.startPrivateSession(target.getUniqueID(), modelName)){
                    PacketHandler.INSTANCE.sendTo(new MessageTogglePrivate(true), target); // 告知客户端开启屏蔽
                    ChatUtil.sendInfo(target, "You entered a private AI chat session with: "+ TextFormatting.GREEN + modelName + TextFormatting.YELLOW + ". Other players' chat is hidden. Your messages are only seen by AI.");
                     ChatUtil.sendInfo(sender, "Started private AI session for " + target.getName() + " with model " + modelName);
                } else {
                    ChatUtil.sendError(sender, "Failed to start private session for " + target.getName() + " with model " + modelName + ". Check logs.");
                }
            }
           return;
        }
       throw new WrongUsageException("Usage: /aichat private <ModelName> <Selector> on | /aichat private <Selector> off");
  }


 @Nonnull
 @Override
 public List<String> getTabCompletions(@Nonnull MinecraftServer server, @Nonnull ICommandSender sender, @Nonnull String[] args, @Nullable BlockPos targetPos) {
    if (args.length == 1) {
         return getListOfStringsMatchingLastWord(args, "public", "private", "reload", "list");
     }
      String sub = args[0].toLowerCase();
     if(sub.equals("public")) {
        if (args.length == 2) {
             List<String> publicModels = ModConfig.modelProfiles.values().stream()
                                .filter(p -> p.isPublic)
                                .map(p -> p.profileName)
                                .collect(Collectors.toList());
             publicModels.add("off");
            return getListOfStringsMatchingLastWord(args, publicModels);
        }
        if(args.length == 3 && !args[1].equalsIgnoreCase("off")){
            return getListOfStringsMatchingLastWord(args, "on");
        }
     }
       if(sub.equals("private")) {
          if(args.length == 2){
               List<String> privateModels = ModConfig.modelProfiles.values().stream()
                                  .filter(p -> !p.isPublic)
                                  .map(p -> p.profileName)
                                  .collect(Collectors.toList());
                // Also allow player names/selectors for the 'off' command form: /aichat private <selector> off
                List<String> allOptions = new ArrayList<>(privateModels);
                 allOptions.addAll(Arrays.asList(server.getOnlinePlayerNames()));
                 allOptions.add("@a"); allOptions.add("@p");
                return getListOfStringsMatchingLastWord(args, allOptions);
          }
          if(args.length == 3) {
               // check if arg[1] is a model or a player selector to decide if it's on or off command
               ModelProfile profile = ModConfig.modelProfiles.get(args[1].toLowerCase());
               boolean isModel = ( profile != null && !profile.isPublic);
                if(isModel) {
                   // form: /aichat private <model> <HERE> on
                     List<String> players = new ArrayList<>(Arrays.asList(server.getOnlinePlayerNames()));
                     players.add("@a"); players.add("@p");
                     return getListOfStringsMatchingLastWord(args, players);
                } else {
                     // form: /aichat private <selector> <HERE> off
                     return getListOfStringsMatchingLastWord(args, "off");
                }
          }
           if(args.length == 4){
                ModelProfile profile = ModConfig.modelProfiles.get(args[1].toLowerCase());
                boolean isModel = ( profile != null && !profile.isPublic);
                 if(isModel) { // only show "on" if the second arg was a valid private model
                      return getListOfStringsMatchingLastWord(args, "on");
                 }
           }
       }

     return Collections.emptyList();
 }
  
  @Override
  public boolean isUsernameIndex(String[] args, int index) {
     // Tell MC that player selectors can be here
     if(args.length > 0 && args[0].equalsIgnoreCase("private")){
        if(index == 1) return true; // for /aichat private <selector> off
        if(index == 2) return true; // for /aichat private <model> <selector> on
     }
     return false;
 }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/network/PacketHandler.java

package com.yourname.aimod.network;

import com.yourname.aimod.Reference;
import net.minecraftforge.fml.common.network.NetworkRegistry;
import net.minecraftforge.fml.common.network.simpleimpl.SimpleNetworkWrapper;
import net.minecraftforge.fml.relauncher.Side;

public class PacketHandler {
public static final SimpleNetworkWrapper INSTANCE = NetworkRegistry.INSTANCE.newSimpleChannel(Reference.CHANNEL_NAME);
private static int packetId = 0;

Generated code
public static void init() {
    // 注册数据包，服务器发往客户端 (Side.CLIENT)
    INSTANCE.registerMessage(MessageTogglePrivate.Handler.class, MessageTogglePrivate.class, packetId++, Side.CLIENT);
   // 如果有客户端发往服务器的包，使用 Side.SERVER
 }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END

com/yourname/aimod/network/MessageTogglePrivate.java

package com.yourname.aimod.network;

import com.yourname.aimod.AiMod;
import com.yourname.aimod.proxy.ClientProxy;
import io.netty.buffer.ByteBuf;
import net.minecraft.client.Minecraft;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

// 服务器发送给客户端，告知其开启/关闭私聊屏蔽模式
public class MessageTogglePrivate implements IMessage {

Generated code
private boolean active;

 // 需要一个无参构造函数
public MessageTogglePrivate() {}

public MessageTogglePrivate(boolean active) {
    this.active = active;
}

@Override
public void fromBytes(ByteBuf buf) {
    this.active = buf.readBoolean();
}

@Override
public void toBytes(ByteBuf buf) {
     buf.writeBoolean(this.active);
}

 // 消息处理器，内部静态类
 // 标记只在客户端运行
public static class Handler implements IMessageHandler<MessageTogglePrivate, IMessage> {
    @Override
     // 这个方法在网络线程运行
    public IMessage onMessage(MessageTogglePrivate message, MessageContext ctx) {
       if (ctx.side == Side.CLIENT) {
           // 调度到客户端主线程处理
            Minecraft.getMinecraft().addScheduledTask(() -> handleClient(message));
       }
        return null; // No response packet
    }
    
    @SideOnly(Side.CLIENT)
     // 这个方法在客户端主线程运行
    private void handleClient(MessageTogglePrivate message){
        // 通过代理设置客户端状态
        // 确保 proxy 是 ClientProxy 实例
         if(AiMod.proxy instanceof ClientProxy) {
             ((ClientProxy)AiMod.proxy).setClientPrivateMode(message.active);
            // AiMod.logger.info("Client received private mode toggle: " + message.active);
         }
    }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END
3. 配置文件示例 config/aimod.cfg

首次运行mod后，会在 .minecraft/config (或服务器的 config ) 目录下生成 aimod.cfg。ModConfig.java中的 defineDefaultModels() 会创建两个示例模型。
你可以参考修改：

Configuration file

##########################################################################################################

model_private_guide

#--------------------------------------------------------------------------------------------------------#

Model Profile: model_private_guide

##########################################################################################################

model_private_guide {
# API Key (use 'none' or empty if not required)
S:apiKey=no-key

Generated code
# API Base URL
S:baseUrl=http://127.0.0.1:1234/v1/chat/completions

# Set to true for public model, false for private model
B:isPublic=false

# Keyword actions format: keyword::/command1|/command2. Each entry is one keyword mapping. Commands run via console.
S:keywordActions <
 >

# Number of past messages (user+ai) to remember [range: 1 ~ 100, default: 10]
I:maxContext=10

# Model ID string required by the provider
S:modelId=private-model

# System prompt / Preset
S:systemPrompt=You are a private Minecraft guide, assisting one player. Be detailed and roleplay slightly.

# Model temperature (creativity) [range: 0.0 ~ 2.0, default: 0.6]
D:temperature=0.6
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}

##########################################################################################################

model_public_bot

#--------------------------------------------------------------------------------------------------------#

Model Profile: model_public_bot

##########################################################################################################

model_public_bot {
# API Key (use 'none' or empty if not required)
S:apiKey=no-key

Generated code
# API Base URL
S:baseUrl=http://127.0.0.1:1234/v1/chat/completions

# Set to true for public model, false for private model
B:isPublic=true

# Keyword actions format: keyword::/command1|/command2. Each entry is one keyword mapping. Commands run via console.
# Example:
#    diamond please::/give @p diamond 1|/say Diamond for you, @p!
#    make it day::/time set day
S:keywordActions <
    diamond time::/give @p diamond 1|/say Here is your diamond, @p!
    day please::/time set day|/weather clear
 >

# Number of past messages (user+ai) to remember [range: 1 ~ 100, default: 6]
I:maxContext=6

 # Model ID string required by the provider
S:modelId=default-model

# System prompt / Preset
S:systemPrompt=You are MineBot, a helpful chat bot in a Minecraft server. Keep responses brief, friendly and relevant. Always mention player name when replying.

 # Model temperature (creativity) [range: 0.0 ~ 2.0, default: 0.8]
D:temperature=0.8
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

}

你可以按格式复制并添加更多模型, 分类名必须以 model_ 开头, 如 model_my_other_ai { ... }

IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Cfg
IGNORE_WHEN_COPYING_END

配置要点:

每个模型是一个 category，名称必须以 model_ 开头，后面跟的就是你在指令里用的 ModelName。

isPublic 决定模型类型。

keywordActions 是一个列表，每行的格式是 关键词::/指令1|/指令2|/指令3。指令中的 @p 会被替换为触发AI的玩家名字。指令以服务器控制台身份执行。关键词检测不区分大小写。

指令用法 (需要OP权限)

/aichat list: 列出所有配置的模型及其类型，以及当前激活的公用模型。

/aichat reload: 重新加载配置文件，并清除所有AI的聊天状态和历史记录。

/aichat public <ModelName> on: 开启公用模型 (ModelName必须在配置中且 isPublic=true)。示例: /aichat public public_bot on

/aichat public off: 关闭当前激活的公用模型。

/aichat private <ModelName> <PlayerSelector> on: 为指定玩家(可使用@p, @a, @r或玩家名)开启私聊模式 (ModelName必须在配置中且 isPublic=false)。示例: /aichat private private_guide Steve on 或 /aichat private private_guide @p on。

玩家进入私聊后，他发送的消息只有AI和他自己能看见。

AI回复的消息只有他自己能看见。

其他玩家的任何消息(包括公用AI的消息)，该玩家都看不见(客户端屏蔽)。

/aichat private <PlayerSelector> off: 关闭指定玩家的私聊模式。示例: /aichat private Steve off。

指令支持Tab补全。

编译与运行

将所有代码放入你的 Forge MDK src/main/java 对应包路径下。

mcmod.info 放入 src/main/resources.

在项目根目录执行 gradlew build (或在IDE中构建)。

将生成的 jar 文件放入服务器和客户端的 mods 文件夹。

启动游戏/服务器，修改 config/aimod.cfg 文件，配置你的 baseUrl 和 apiKey 等。

在游戏中使用 /aichat reload 重新加载配置。

使用指令开启AI。

这套代码应该能满足你的需求。由于代码量大且复杂，复制粘贴时请务必仔细核对文件名和路径！如果遇到 API 格式不兼容，主要修改 AIClient.java 和 api 包下的类。
祝你成功！