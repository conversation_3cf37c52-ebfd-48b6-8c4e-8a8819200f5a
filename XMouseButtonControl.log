
*******************************************************************************************************************************************
16-05-2024 07:36:09.604> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/notportable'
16-05-2024 07:36:09.605> Running in high integrity mode (0x00003000)
16-05-2024 07:36:09.607> Loaded 0 application specific profiles (0 normal and 0 custom window).
16-05-2024 07:36:09.607> NewApplicationSettings: SPIThread is not initialised.
16-05-2024 07:36:09.607> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
16-05-2024 07:36:09.607> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
16-05-2024 07:36:09.607> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
16-05-2024 07:36:09.607> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
16-05-2024 07:36:09.608> Using keyboard language 0x04090409
16-05-2024 07:37:38.602> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:37:43.072> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:37:44.665> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:37:46.274> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:38:09.653> There is a new version available! (2200500)
16-05-2024 07:38:14.479> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:38:15.259> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:38:15.884> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:38:17.650> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:38:28.088> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:38:33.781> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:38:35.665> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:38:36.900> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:38:43.222> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:39:00.915> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:39:06.276> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:39:07.680> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:39:14.181> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:39:19.852> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:39:30.165> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:39:42.493> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:39:51.900> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:39:59.415> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:40:02.525> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:40:02.760> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:40:12.056> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:40:12.196> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:40:17.758> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:40:19.806> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:40:24.025> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:40:25.258> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:40:25.518> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:41:02.774> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:41:16.416> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:41:16.462> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:41:20.306> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:41:49.166> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:41:52.088> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:41:54.290> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:00.915> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:06.994> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:10.681> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:10.899> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:17.056> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:17.259> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:26.342> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:27.337> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:27.494> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:46.698> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:50.025> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:50.152> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:50.197> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:50.291> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:50.338> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:50.494> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:51.652> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:51.868> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:54.290> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:54.337> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:55.494> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:57.431> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:57.650> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:42:58.712> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:43:00.369> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:43:00.431> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:43:02.056> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:43:03.501> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:43:31.504> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:43:35.368> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:43:58.040> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:43:59.353> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:05.916> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:37.743> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:40.493> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:40.587> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:40.712> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:40.854> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:41.181> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:41.604> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:42.603> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:55.272> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:55.352> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:55.416> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:55.478> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:57.588> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:58.979> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:59.071> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:44:59.932> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:45:00.025> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:45:07.603> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:45:07.665> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:45:08.603> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:45:09.619> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:21.618> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:26.040> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:27.150> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:27.197> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:30.103> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:30.385> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:30.431> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:32.962> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:34.589> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:34.681> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:34.806> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:36.088> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:38.806> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:38.900> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:48.161> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:49.182> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:49.479> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:50.993> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:55.462> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:57.884> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:48:59.118> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:03.071> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:03.290> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:04.525> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:05.837> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:06.025> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:06.118> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:07.806> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:15.744> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:15.821> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:18.540> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:18.775> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:20.057> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:20.150> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:20.228> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:20.368> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:23.104> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:23.165> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:27.447> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:31.369> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:36.337> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:38.040> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:38.102> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:38.197> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:41.056> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:41.150> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:41.370> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:49:41.415> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:51:58.571> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:51:59.604> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:51:59.853> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:06.962> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:08.634> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:08.760> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:08.821> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:14.665> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:26.590> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:53.915> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:53.978> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:55.040> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:55.900> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:55.994> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:56.259> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:56.402> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:52:56.696> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:53:18.194> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:53:54.151> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:53:55.504> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:53:58.868> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:00.619> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:01.853> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:02.071> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:02.353> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:03.134> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:03.635> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:04.432> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:08.134> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:08.353> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:09.602> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:11.430> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:11.666> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:12.634> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:13.618> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:17.040> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:18.619> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:18.978> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:20.181> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:20.384> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:20.463> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:21.680> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:21.727> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:21.792> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:21.977> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:25.510> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:25.759> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:26.993> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:28.821> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:31.587> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:38.120> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:50.259> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:54:51.430> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:00.681> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:00.806> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:00.868> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:04.665> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:04.728> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:04.853> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:13.759> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:13.978> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:14.884> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:15.151> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:26.681> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:27.150> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:28.665> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:29.134> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:29.509> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:31.041> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:35.338> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:35.462> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:37.790> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:37.837> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:38.197> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:40.228> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:41.962> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:42.197> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:44.383> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:44.619> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:44.743> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:47.852> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:47.947> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:50.009> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:50.290> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:51.587> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:51.837> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:54.103> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:55:56.479> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:56:12.477> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:56:12.649> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:56:14.993> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:56:15.228> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:56:15.416> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 07:56:17.212> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:00:44.790> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:00:53.853> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:00:57.400> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:01:16.776> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:02:32.258> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:02:41.165> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:03:14.196> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:03:18.603> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:03:26.246> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:03:27.510> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:03:34.916> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:03:44.009> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:12.337> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:14.104> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:14.212> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:29.932> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:30.103> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:30.181> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:31.510> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:34.384> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:40.870> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:44.587> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:45.040> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:50.774> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:04:52.805> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:05:01.072> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:05:04.040> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:05:05.354> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:05:09.446> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:05:11.462> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
16-05-2024 08:05:18.704> SendInputThreaded: Ignoring input as the send thread has been blocked.
16-05-2024 08:05:18.704> SendInputThreaded: Ignoring input as the send thread has been blocked.

*******************************************************************************************************************************************
20-05-2024 00:28:32.035> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/notportable'
20-05-2024 00:28:32.036> Running in high integrity mode (0x00003000)
20-05-2024 00:28:32.039> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 00:28:32.040> NewApplicationSettings: SPIThread is not initialised.
20-05-2024 00:28:32.040> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
20-05-2024 00:28:32.040> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
20-05-2024 00:28:32.040> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
20-05-2024 00:28:32.040> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
20-05-2024 00:28:32.042> Using keyboard language 0x04090409
20-05-2024 00:28:34.072> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:28:48.812> CMyMessageWnd::OnSetup   -   Opening setup dialog.
20-05-2024 00:28:48.837> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 00:28:54.353> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:28:56.700> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:28:57.384> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:28:58.135> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:28:58.275> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:28:58.369> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:04.979> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:06.104> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:06.807> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:08.807> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:08.886> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:09.995> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:10.150> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:12.338> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:12.385> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:14.588> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:15.572> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:16.589> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:16.697> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:17.400> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:18.197> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:19.276> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:19.354> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:39.650> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:39.697> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:40.166> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:40.369> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:40.463> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:40.557> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:40.809> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:41.557> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:43.478> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:46.229> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:29:58.789> CMyMessageWnd::OnSetup   -   Setup dialog closed.
20-05-2024 00:29:58.794> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 00:30:02.083> Exit requested by the user.
20-05-2024 00:30:02.083> Removing any active XMBCHook Timers...
20-05-2024 00:30:02.083> Timer thread exited with error code 0.
20-05-2024 00:30:02.083> CSendInput: Exiting SendInput thread.
20-05-2024 00:30:02.096> HookThread: Finished cleaning up.
20-05-2024 00:30:02.096> Hook thread exited with error code 0.
20-05-2024 00:30:02.096> X-Mouse Button Control Shutdown.


*******************************************************************************************************************************************
20-05-2024 00:36:13.407> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/notportable'
20-05-2024 00:36:13.408> Running in high integrity mode (0x00003000)
20-05-2024 00:36:13.409> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 00:36:13.409> NewApplicationSettings: SPIThread is not initialised.
20-05-2024 00:36:13.409> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
20-05-2024 00:36:13.409> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
20-05-2024 00:36:13.409> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
20-05-2024 00:36:13.410> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
20-05-2024 00:36:13.410> Using keyboard language 0x04090409
20-05-2024 00:36:15.634> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:16.447> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:17.478> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:18.197> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:18.588> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:19.713> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:19.900> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:20.838> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:20.994> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:24.838> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:24.884> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:27.119> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:27.166> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:28.698> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:29.978> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:43.056> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:43.134> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:46.510> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:50.838> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:50.994> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:51.994> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:52.057> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:57.728> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:57.793> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:57.885> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:59.057> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:36:59.105> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:02.776> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:03.791> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:04.197> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:05.198> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:06.979> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:08.792> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:08.845> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:09.011> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:09.089> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:27.478> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:33.291> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:34.093> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:35.213> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:35.291> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:36.838> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:37.541> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:38.447> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:38.494> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:38.681> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:39.900> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:39.964> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:40.013> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:40.088> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:40.198> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:42.276> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:42.338> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:42.385> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:43.260> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:43.307> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:43.401> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:44.384> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 00:37:52.662> CMyMessageWnd::OnSetup   -   Opening setup dialog.
20-05-2024 00:37:52.673> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 00:37:57.892> Exit requested by the user.
20-05-2024 00:37:57.908> CMyMessageWnd::OnSetup   -   Setup dialog closed.
20-05-2024 00:37:57.910> Removing any active XMBCHook Timers...
20-05-2024 00:37:57.910> Timer thread exited with error code 0.
20-05-2024 00:37:57.910> CSendInput: Exiting SendInput thread.
20-05-2024 00:37:57.920> HookThread: Finished cleaning up.
20-05-2024 00:37:57.920> Hook thread exited with error code 0.
20-05-2024 00:37:57.949> X-Mouse Button Control Shutdown.


*******************************************************************************************************************************************
20-05-2024 00:38:27.134> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/install:"c:\users\<USER>\desktop\simplified_chinese.xmbclp"'
20-05-2024 00:38:27.135> Running in high integrity mode (0x00003000)
20-05-2024 00:38:27.135> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 00:38:27.135> NewApplicationSettings: SPIThread is not initialised.
20-05-2024 00:38:27.135> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
20-05-2024 00:38:27.136> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
20-05-2024 00:38:27.136> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
20-05-2024 00:38:27.136> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
20-05-2024 00:38:27.136> Using keyboard language 0x04090409
20-05-2024 00:38:27.159> OnNextInstance: Installing language pack file: c:\users\<USER>\desktop\simplified_chinese.xmbclp (�������� (Simplified Chinese) : ������ NightSharp YFdyh000 Yuhanawa ����/����/�Ľ� : NightSharp / YFdyh000 / Yuhanawa)
20-05-2024 00:38:27.159> Unable to install language 'C:\Users\<USER>\Desktop\simplified_chinese.xmbclp' as it is already installed. Enabling it instead!
20-05-2024 00:38:27.161> CMyMessageWnd::OnSetup   -   Opening setup dialog.
20-05-2024 00:38:27.168> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 00:38:57.091> CMyMessageWnd::OnSetup   -   Setup dialog closed.
20-05-2024 00:38:57.095> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 00:38:59.855> Exit requested by the user.
20-05-2024 00:38:59.856> Removing any active XMBCHook Timers...
20-05-2024 00:38:59.856> Timer thread exited with error code 0.
20-05-2024 00:38:59.856> CSendInput: Exiting SendInput thread.
20-05-2024 00:38:59.865> HookThread: Finished cleaning up.
20-05-2024 00:38:59.865> Hook thread exited with error code 0.
20-05-2024 00:38:59.865> X-Mouse Button Control Shutdown.


*******************************************************************************************************************************************
20-05-2024 01:01:48.025> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/install:"c:\users\<USER>\desktop\simplified_chinese.xmbclp"'
20-05-2024 01:01:48.026> Running in high integrity mode (0x00003000)
20-05-2024 01:01:48.026> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 01:01:48.026> NewApplicationSettings: SPIThread is not initialised.
20-05-2024 01:01:48.026> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
20-05-2024 01:01:48.027> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
20-05-2024 01:01:48.027> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
20-05-2024 01:01:48.027> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
20-05-2024 01:01:48.027> Using keyboard language 0x04090409
20-05-2024 01:01:48.050> OnNextInstance: Installing language pack file: c:\users\<USER>\desktop\simplified_chinese.xmbclp (�������� (Simplified Chinese) : ������ NightSharp YFdyh000 Yuhanawa ����/����/�Ľ� : NightSharp / YFdyh000 / Yuhanawa)
20-05-2024 01:01:48.050> Unable to install language 'C:\Users\<USER>\Desktop\simplified_chinese.xmbclp' as it is already installed. Enabling it instead!
20-05-2024 01:01:48.052> CMyMessageWnd::OnSetup   -   Opening setup dialog.
20-05-2024 01:01:48.060> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 01:02:07.253> Applying changes to X-Mouse Button Control settings...
20-05-2024 01:02:07.260> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 01:02:09.126> CMyMessageWnd::OnSetup   -   Setup dialog closed.
20-05-2024 01:02:09.131> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 01:02:09.587> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:09.696> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:12.792> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:14.635> There is a new version available! (2200500)
20-05-2024 01:02:18.369> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:18.446> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:19.638> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:19.727> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:19.853> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:19.917> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:19.962> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:20.072> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:20.290> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:26.385> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:26.806> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:27.868> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:28.463> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:30.588> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:30.650> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:30.697> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:31.214> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:02:53.804> Exit requested by the user.
20-05-2024 01:02:53.804> Removing any active XMBCHook Timers...
20-05-2024 01:02:53.805> Timer thread exited with error code 0.
20-05-2024 01:02:53.805> CSendInput: Exiting SendInput thread.
20-05-2024 01:02:53.813> HookThread: Finished cleaning up.
20-05-2024 01:02:53.813> Hook thread exited with error code 0.
20-05-2024 01:02:53.813> X-Mouse Button Control Shutdown.


*******************************************************************************************************************************************
20-05-2024 01:03:54.469> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/install:"c:\users\<USER>\desktop\simplified_chinese.xmbclp"'
20-05-2024 01:03:54.470> Running in high integrity mode (0x00003000)
20-05-2024 01:03:54.470> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 01:03:54.470> NewApplicationSettings: SPIThread is not initialised.
20-05-2024 01:03:54.470> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
20-05-2024 01:03:54.471> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
20-05-2024 01:03:54.471> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
20-05-2024 01:03:54.471> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
20-05-2024 01:03:54.472> Using keyboard language 0x04090409
20-05-2024 01:03:54.500> OnNextInstance: Installing language pack file: c:\users\<USER>\desktop\simplified_chinese.xmbclp (�������� (Simplified Chinese) : ������ NightSharp YFdyh000 Yuhanawa ����/����/�Ľ� : NightSharp / YFdyh000 / Yuhanawa)
20-05-2024 01:03:54.500> Unable to install language 'C:\Users\<USER>\Desktop\simplified_chinese.xmbclp' as it is already installed. Enabling it instead!
20-05-2024 01:03:54.503> CMyMessageWnd::OnSetup   -   Opening setup dialog.
20-05-2024 01:03:54.515> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 01:04:25.815> Applying changes to X-Mouse Button Control settings...
20-05-2024 01:04:25.819> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 01:04:45.508> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:05:54.484> There is a new version available! (2200500)
20-05-2024 01:07:00.496> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:09:24.260> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:09:59.698> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:11:29.401> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:11:54.056> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:11:55.149> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:13:17.640> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:14:11.556> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:15:07.149> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:15:10.681> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:15:11.244> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:15:32.290> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:15:41.571> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:15:48.386> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:16:11.916> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:17:11.681> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
20-05-2024 01:18:27.771> CMyMessageWnd::OnSetup   -   Setup dialog closed.
20-05-2024 01:18:27.776> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-05-2024 01:18:32.781> There is a new version available! (2200500)

*******************************************************************************************************************************************
22-05-2024 19:43:07.036> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/install:"c:\users\<USER>\desktop\simplified_chinese.xmbclp"'
22-05-2024 19:43:07.036> Running in high integrity mode (0x00003000)
22-05-2024 19:43:07.044> Loaded 0 application specific profiles (0 normal and 0 custom window).
22-05-2024 19:43:07.044> NewApplicationSettings: SPIThread is not initialised.
22-05-2024 19:43:07.044> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
22-05-2024 19:43:07.044> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
22-05-2024 19:43:07.044> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
22-05-2024 19:43:07.044> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
22-05-2024 19:43:07.044> Using keyboard language 0x04090409
22-05-2024 19:43:07.078> OnNextInstance: Installing language pack file: c:\users\<USER>\desktop\simplified_chinese.xmbclp (�������� (Simplified Chinese) : ������ NightSharp YFdyh000 Yuhanawa ����/����/�Ľ� : NightSharp / YFdyh000 / Yuhanawa)
22-05-2024 19:43:07.078> Unable to install language 'C:\Users\<USER>\Desktop\simplified_chinese.xmbclp' as it is already installed. Enabling it instead!
22-05-2024 19:43:07.078> CMyMessageWnd::OnSetup   -   Opening setup dialog.
22-05-2024 19:43:07.094> Loaded 0 application specific profiles (0 normal and 0 custom window).
22-05-2024 19:45:07.099> There is a new version available! (2200500)
22-05-2024 19:46:59.407> CMyMessageWnd::OnSetup   -   Setup dialog closed.
22-05-2024 19:46:59.413> Loaded 0 application specific profiles (0 normal and 0 custom window).
22-05-2024 19:47:04.409> There is a new version available! (2200500)
22-05-2024 19:47:21.593> CMyMessageWnd::OnSetup   -   Opening setup dialog.
22-05-2024 19:47:21.608> Loaded 0 application specific profiles (0 normal and 0 custom window).
22-05-2024 19:47:35.479> Applying changes to X-Mouse Button Control settings...
22-05-2024 19:47:35.485> Loaded 0 application specific profiles (0 normal and 0 custom window).
22-05-2024 19:47:36.320> CMyMessageWnd::OnSetup   -   Setup dialog closed.
22-05-2024 19:47:36.326> Loaded 0 application specific profiles (0 normal and 0 custom window).
22-05-2024 19:47:41.325> There is a new version available! (2200500)
22-05-2024 19:48:11.384> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:48:54.840> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:48:58.322> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:48:58.884> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:48:59.712> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:49:01.346> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:49:53.338> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:49:58.073> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:49:58.903> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:50:19.323> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:50:19.885> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:50:26.041> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:50:26.605> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
22-05-2024 19:50:27.437> RepeatTimerProc: Still sending previous simkeys. Resetting timer.

*******************************************************************************************************************************************
02-06-2024 20:26:37.842> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/notportable'
02-06-2024 20:26:37.843> Running in high integrity mode (0x00003000)
02-06-2024 20:26:37.844> Loaded 0 application specific profiles (0 normal and 0 custom window).
02-06-2024 20:26:37.844> NewApplicationSettings: SPIThread is not initialised.
02-06-2024 20:26:37.844> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
02-06-2024 20:26:37.844> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
02-06-2024 20:26:37.844> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
02-06-2024 20:26:37.844> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
02-06-2024 20:26:37.851> Using keyboard language 0x04090409
02-06-2024 20:26:44.561> MultInstance: Sending commandline to an existing instance of X-Mouse Button Control
02-06-2024 20:26:44.561> MultInstance: Successfully finished communicating with the first instance of X-Mouse Button Control.
02-06-2024 20:26:44.561> Received 2048 bytes on the IPC named pipe.
02-06-2024 20:26:44.561> Received message with data 0x80000204 from another instance of X-Mouse Button Control.

02-06-2024 20:26:44.567> CMyMessageWnd::OnSetup   -   Opening setup dialog.
02-06-2024 20:26:44.583> Loaded 0 application specific profiles (0 normal and 0 custom window).
02-06-2024 20:26:55.695> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:27:48.032> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:27:51.256> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:27:59.892> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:28:13.752> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:28:38.382> There is a new version available! (2200500)
02-06-2024 20:28:47.363> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:28:53.158> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:28:55.314> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:28:56.408> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:29:27.290> Applying changes to X-Mouse Button Control settings...
02-06-2024 20:29:27.298> Loaded 0 application specific profiles (0 normal and 0 custom window).
02-06-2024 20:29:58.255> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:29:59.079> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:30:06.017> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:30:08.193> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:30:09.815> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:30:18.111> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:30:55.408> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:31:22.783> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:31:24.144> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:31:28.488> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:31:57.316> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:32:04.283> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:32:10.766> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:32:27.057> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:32:31.581> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:32:33.210> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:33:05.064> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:33:25.425> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:33:32.378> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:33:33.205> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:33:34.839> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:33:36.724> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:33:38.345> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:35:50.286> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:36:14.878> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:36:20.268> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:36:44.956> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:37:10.206> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:37:39.552> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:37:40.112> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:37:52.508> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:37:59.455> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:38:00.569> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:38:21.407> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:38:26.927> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:38:36.815> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:38:53.017> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:39:02.096> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:39:19.896> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:39:33.239> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:41:00.507> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:41:04.739> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:41:07.552> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:41:09.181> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:41:11.067> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:41:44.533> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:45:11.581> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:45:14.035> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:45:23.111> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:45:37.237> RepeatTimerProc: Still sending previous simkeys. Resetting timer.
02-06-2024 20:49:36.291> CMyMessageWnd::OnSetup   -   Setup dialog closed.
02-06-2024 20:49:36.297> Loaded 0 application specific profiles (0 normal and 0 custom window).
02-06-2024 20:49:38.808> Exit requested by the user.
02-06-2024 20:49:38.809> Removing any active XMBCHook Timers...
02-06-2024 20:49:38.809> Timer thread exited with error code 0.
02-06-2024 20:49:38.809> CSendInput: Exiting SendInput thread.
02-06-2024 20:49:38.820> HookThread: Finished cleaning up.
02-06-2024 20:49:38.820> Hook thread exited with error code 0.
02-06-2024 20:49:38.820> X-Mouse Button Control Shutdown.


*******************************************************************************************************************************************
25-06-2024 08:05:55.659> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/notportable'
25-06-2024 08:05:55.660> Running in high integrity mode (0x00003000)
25-06-2024 08:05:55.664> Loaded 0 application specific profiles (0 normal and 0 custom window).
25-06-2024 08:05:55.664> NewApplicationSettings: SPIThread is not initialised.
25-06-2024 08:05:55.664> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
25-06-2024 08:05:55.664> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
25-06-2024 08:05:55.664> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
25-06-2024 08:05:55.665> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
25-06-2024 08:05:55.679> Using keyboard language 0x04090409
25-06-2024 08:06:05.231> CMyMessageWnd::OnSetup   -   Opening setup dialog.
25-06-2024 08:06:05.244> Loaded 0 application specific profiles (0 normal and 0 custom window).
25-06-2024 08:07:55.713> There is a new version available! (2200500)
25-06-2024 08:17:36.065> CMyMessageWnd::OnSetup   -   Setup dialog closed.
25-06-2024 08:17:36.070> Loaded 0 application specific profiles (0 normal and 0 custom window).
25-06-2024 08:17:38.210> Exit requested by the user.
25-06-2024 08:17:38.210> Removing any active XMBCHook Timers...
25-06-2024 08:17:38.211> Timer thread exited with error code 0.
25-06-2024 08:17:38.211> CSendInput: Exiting SendInput thread.
25-06-2024 08:17:38.239> HookThread: Finished cleaning up.
25-06-2024 08:17:38.241> Hook thread exited with error code 0.
25-06-2024 08:17:38.241> RemoveMouseHook took 31ms
25-06-2024 08:17:38.241> X-Mouse Button Control Shutdown.


*******************************************************************************************************************************************
03-07-2024 01:31:43.332> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/install:"c:\users\<USER>\desktop\simplified_chinese.xmbclp"'
03-07-2024 01:31:43.333> Running in high integrity mode (0x00003000)
03-07-2024 01:31:43.333> Loaded 0 application specific profiles (0 normal and 0 custom window).
03-07-2024 01:31:43.333> NewApplicationSettings: SPIThread is not initialised.
03-07-2024 01:31:43.333> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
03-07-2024 01:31:43.333> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
03-07-2024 01:31:43.333> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
03-07-2024 01:31:43.333> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
03-07-2024 01:31:43.335> Using keyboard language 0x04090409
03-07-2024 01:31:43.374> OnNextInstance: Installing language pack file: c:\users\<USER>\desktop\simplified_chinese.xmbclp (�������� (Simplified Chinese) : ������ NightSharp YFdyh000 Yuhanawa ����/����/�Ľ� : NightSharp / YFdyh000 / Yuhanawa)
03-07-2024 01:31:43.375> Unable to install language 'C:\Users\<USER>\Desktop\simplified_chinese.xmbclp' as it is already installed. Enabling it instead!
03-07-2024 01:31:43.377> CMyMessageWnd::OnSetup   -   Opening setup dialog.
03-07-2024 01:31:43.405> Loaded 0 application specific profiles (0 normal and 0 custom window).
03-07-2024 01:31:46.238> CMyMessageWnd::OnSetup   -   Setup dialog closed.
03-07-2024 01:31:46.243> Loaded 0 application specific profiles (0 normal and 0 custom window).
03-07-2024 01:31:50.389> Exit requested by the user.
03-07-2024 01:31:50.389> Removing any active XMBCHook Timers...
03-07-2024 01:31:50.389> Timer thread exited with error code 0.
03-07-2024 01:31:50.389> CSendInput: Exiting SendInput thread.
03-07-2024 01:31:50.516> HookThread: Finished cleaning up.
03-07-2024 01:31:50.516> Hook thread exited with error code 0.
03-07-2024 01:31:50.517> RemoveMouseHook took 125ms
03-07-2024 01:31:50.517> X-Mouse Button Control Shutdown.


*******************************************************************************************************************************************
03-07-2024 01:32:25.799> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/install:"c:\users\<USER>\desktop\simplified_chinese.xmbclp"'
03-07-2024 01:32:25.800> Running in high integrity mode (0x00003000)
03-07-2024 01:32:25.801> Loaded 0 application specific profiles (0 normal and 0 custom window).
03-07-2024 01:32:25.801> NewApplicationSettings: SPIThread is not initialised.
03-07-2024 01:32:25.801> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
03-07-2024 01:32:25.801> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
03-07-2024 01:32:25.801> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
03-07-2024 01:32:25.801> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
03-07-2024 01:32:25.802> Using keyboard language 0x04090409
03-07-2024 01:32:25.822> OnNextInstance: Installing language pack file: c:\users\<USER>\desktop\simplified_chinese.xmbclp (�������� (Simplified Chinese) : ������ NightSharp YFdyh000 Yuhanawa ����/����/�Ľ� : NightSharp / YFdyh000 / Yuhanawa)
03-07-2024 01:32:25.822> Unable to install language 'C:\Users\<USER>\Desktop\simplified_chinese.xmbclp' as it is already installed. Enabling it instead!
03-07-2024 01:32:25.825> CMyMessageWnd::OnSetup   -   Opening setup dialog.
03-07-2024 01:32:25.831> Loaded 0 application specific profiles (0 normal and 0 custom window).
03-07-2024 01:33:36.117> Applying changes to X-Mouse Button Control settings...
03-07-2024 01:33:36.125> Loaded 0 application specific profiles (0 normal and 0 custom window).
03-07-2024 01:33:36.137> CMyMessageWnd::OnSetup   -   Setup dialog closed.
03-07-2024 01:33:36.142> Loaded 0 application specific profiles (0 normal and 0 custom window).
03-07-2024 01:33:38.288> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
03-07-2024 01:33:38.460> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
03-07-2024 01:33:39.100> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
03-07-2024 01:33:39.506> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
03-07-2024 01:33:39.712> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
03-07-2024 01:33:39.873> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
03-07-2024 01:33:40.921> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
03-07-2024 01:33:41.077> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
03-07-2024 01:33:41.225> There is a new version available! (2200500)
03-07-2024 01:33:41.432> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
03-07-2024 01:33:41.566> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
03-07-2024 01:33:52.365> CMyMessageWnd::OnSetup   -   Opening setup dialog.
03-07-2024 01:33:52.372> Loaded 0 application specific profiles (0 normal and 0 custom window).
03-07-2024 01:33:58.292> CMyMessageWnd::OnSetup   -   Setup dialog closed.
03-07-2024 01:33:58.297> Loaded 0 application specific profiles (0 normal and 0 custom window).
03-07-2024 01:34:00.725> Exit requested by the user.
03-07-2024 01:34:00.726> Removing any active XMBCHook Timers...
03-07-2024 01:34:00.726> Timer thread exited with error code 0.
03-07-2024 01:34:00.726> CSendInput: Exiting SendInput thread.
03-07-2024 01:34:00.736> HookThread: Finished cleaning up.
03-07-2024 01:34:00.736> Hook thread exited with error code 0.
03-07-2024 01:34:00.736> X-Mouse Button Control Shutdown.


*******************************************************************************************************************************************
13-07-2024 15:42:56.774> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/notportable'
13-07-2024 15:42:56.775> Running in high integrity mode (0x00003000)
13-07-2024 15:42:56.776> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 15:42:56.776> NewApplicationSettings: SPIThread is not initialised.
13-07-2024 15:42:56.776> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
13-07-2024 15:42:56.776> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
13-07-2024 15:42:56.776> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
13-07-2024 15:42:56.776> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
13-07-2024 15:42:56.780> Using keyboard language 0x04090409
13-07-2024 15:43:00.408> MultInstance: Sending commandline to an existing instance of X-Mouse Button Control
13-07-2024 15:43:00.409> MultInstance: Successfully finished communicating with the first instance of X-Mouse Button Control.
13-07-2024 15:43:00.409> Received 2048 bytes on the IPC named pipe.
13-07-2024 15:43:00.409> Received message with data 0x80000204 from another instance of X-Mouse Button Control.

13-07-2024 15:43:00.416> CMyMessageWnd::OnSetup   -   Opening setup dialog.
13-07-2024 15:43:00.431> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 15:44:08.432> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
13-07-2024 15:44:08.586> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
13-07-2024 15:44:08.800> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
13-07-2024 15:44:09.358> SendKeyStateWhileDown: Invalid/unknown special key tag {V}
13-07-2024 15:44:11.160> Applying changes to X-Mouse Button Control settings...
13-07-2024 15:44:11.164> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 15:44:14.288> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:14.981> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:15.204> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:15.731> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:15.834> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:16.188> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:16.297> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:16.560> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:16.653> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:16.774> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:16.894> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:17.038> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:17.233> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:19.002> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:19.186> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:19.571> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:19.663> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:19.807> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:19.881> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:20.002> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:20.158> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:20.302> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:44:39.031> CMyMessageWnd::OnSetup   -   Setup dialog closed.
13-07-2024 15:44:39.036> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 15:44:49.132> CMyMessageWnd::OnSetup   -   Opening setup dialog.
13-07-2024 15:44:49.138> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 15:44:55.089> Failed to query the version DNS record. Status=1460
13-07-2024 15:44:55.089> You are using the latest available version of X-Mouse Button Control.
13-07-2024 15:46:24.351> Applying changes to X-Mouse Button Control settings...
13-07-2024 15:46:24.355> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 15:46:25.781> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:26.055> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:26.320> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:26.364> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:26.466> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:26.607> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:26.699> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:26.844> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:26.921> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:27.187> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:27.385> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:27.502> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:27.771> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:27.941> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:28.306> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:28.572> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:28.833> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:29.090> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:29.356> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:29.425> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:29.557> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:29.836> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:46:29.954> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:49:26.994> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:49:27.204> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:49:27.453> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:49:27.646> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:12.849> Applying changes to X-Mouse Button Control settings...
13-07-2024 15:50:12.853> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 15:50:15.514> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:15.782> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:16.043> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:16.126> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:16.281> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:16.555> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:16.815> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:17.078> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:17.112> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:17.258> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:17.512> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:17.573> SendKeyStateWhileDown: Invalid/unknown special key tag {LMBX}
13-07-2024 15:50:25.399> CMyMessageWnd::OnSetup   -   Setup dialog closed.
13-07-2024 15:50:25.403> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 15:50:27.859> Exit requested by the user.
13-07-2024 15:50:27.859> Removing any active XMBCHook Timers...
13-07-2024 15:50:27.859> Timer thread exited with error code 0.
13-07-2024 15:50:27.859> CSendInput: Exiting SendInput thread.
13-07-2024 15:50:27.870> HookThread: Finished cleaning up.
13-07-2024 15:50:27.870> Hook thread exited with error code 0.
13-07-2024 15:50:27.870> X-Mouse Button Control Shutdown.


*******************************************************************************************************************************************
13-07-2024 17:21:37.623> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/notportable'
13-07-2024 17:21:37.624> Running in high integrity mode (0x00003000)
13-07-2024 17:21:37.627> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 17:21:37.627> NewApplicationSettings: SPIThread is not initialised.
13-07-2024 17:21:37.627> UpdateSettings: System (default) Mouse cursor speed is: 17. Setting cursor speed to 10
13-07-2024 17:21:37.628> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
13-07-2024 17:21:37.628> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
13-07-2024 17:21:37.628> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
13-07-2024 17:21:37.630> Using keyboard language 0x04090409
13-07-2024 17:21:48.222> CMyMessageWnd::OnSetup   -   Opening setup dialog.
13-07-2024 17:21:48.235> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 17:21:56.784> Applying changes to X-Mouse Button Control settings...
13-07-2024 17:21:56.787> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 17:22:24.327> Applying changes to X-Mouse Button Control settings...
13-07-2024 17:22:24.330> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 17:23:46.938> CMyMessageWnd::OnSetup   -   Setup dialog closed.
13-07-2024 17:23:46.943> Loaded 0 application specific profiles (0 normal and 0 custom window).
13-07-2024 17:23:48.724> Failed to query the version DNS record. Status=1460
13-07-2024 17:23:48.725> You are using the latest available version of X-Mouse Button Control.
13-07-2024 17:23:51.952> Failed to query the version DNS record. Status=1460
13-07-2024 17:23:51.952> You are using the latest available version of X-Mouse Button Control.
13-07-2024 17:37:04.319> Exit requested by the user.
13-07-2024 17:37:04.319> Removing any active XMBCHook Timers...
13-07-2024 17:37:04.320> Timer thread exited with error code 0.
13-07-2024 17:37:04.321> CSendInput: Exiting SendInput thread.
13-07-2024 17:37:04.349> HookThread: Finished cleaning up.
13-07-2024 17:37:04.349> Hook thread exited with error code 0.
13-07-2024 17:37:04.350> RemoveMouseHook took 31ms
13-07-2024 17:37:04.350> X-Mouse Button Control Shutdown.


*******************************************************************************************************************************************
20-09-2024 22:23:24.378> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/install:"c:\users\<USER>\desktop\simplified_chinese.xmbclp"'
20-09-2024 22:23:24.379> Running in high integrity mode (0x00003000)
20-09-2024 22:23:24.379> Unable to find the default settings file 'C:\Users\<USER>\Desktop\XMBCSettings.xml' (Error code 2: ϵͳ�Ҳ���ָ�����ļ��� )
20-09-2024 22:23:24.379> HookDLL: Unable to load XML settings
20-09-2024 22:23:24.380> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
20-09-2024 22:23:24.380> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
20-09-2024 22:23:24.380> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
20-09-2024 22:23:24.381> Using keyboard language 0x04090409
20-09-2024 22:23:24.417> OnNextInstance: Installing language pack file: c:\users\<USER>\desktop\simplified_chinese.xmbclp (�������� (Simplified Chinese) : ������ NightSharp YFdyh000 Yuhanawa ����/����/�Ľ� : NightSharp / YFdyh000 / Yuhanawa)
20-09-2024 22:23:24.417> Unable to install language 'C:\Users\<USER>\Desktop\simplified_chinese.xmbclp' as it is already installed. Enabling it instead!
20-09-2024 22:23:24.419> CMyMessageWnd::OnSetup   -   Opening setup dialog.
20-09-2024 22:23:24.452> Unable to find the default settings file 'C:\Users\<USER>\Desktop\XMBCSettings.xml' (Error code 2: ϵͳ�Ҳ���ָ�����ļ��� )
20-09-2024 22:23:24.452> SetupDialog: Settings file 'C:\Users\<USER>\Desktop\XMBCSettings.xml' does not exist!
20-09-2024 22:23:24.672> GetLayerName: Layer 0 does not exist or has no name!
20-09-2024 22:23:24.672> GetLayerName: Layer 0 does not exist or has no name!
20-09-2024 22:23:24.672> GetLayerName: Layer 0 does not exist or has no name!
20-09-2024 22:23:24.672> GetLayerName: Layer 0 does not exist or has no name!
20-09-2024 22:23:24.680> GetLayerName: Layer 1 does not exist or has no name!
20-09-2024 22:23:24.680> GetLayerName: Layer 1 does not exist or has no name!
20-09-2024 22:23:24.680> GetLayerName: Layer 1 does not exist or has no name!
20-09-2024 22:23:24.680> GetLayerName: Layer 1 does not exist or has no name!
20-09-2024 22:23:34.581> Applying changes to X-Mouse Button Control settings...
20-09-2024 22:23:34.582> Unable to find the default settings file 'C:\Users\<USER>\Desktop\XMBCSettings.xml' (Error code 2: ϵͳ�Ҳ���ָ�����ļ��� )
20-09-2024 22:23:34.582> SetupDialog: Settings file 'C:\Users\<USER>\Desktop\XMBCSettings.xml' does not exist!
20-09-2024 22:23:34.582> Updating Windows hook timeout: 200
20-09-2024 22:23:34.582> There is no existing settings file. Attempting to creating a new one.
20-09-2024 22:23:34.586> GetLayerName: Layer 2 does not exist or has no name!
20-09-2024 22:23:34.586> GetLayerName: Layer 3 does not exist or has no name!
20-09-2024 22:23:34.586> GetLayerName: Layer 4 does not exist or has no name!
20-09-2024 22:23:34.586> GetLayerName: Layer 5 does not exist or has no name!
20-09-2024 22:23:34.586> GetLayerName: Layer 6 does not exist or has no name!
20-09-2024 22:23:34.586> GetLayerName: Layer 7 does not exist or has no name!
20-09-2024 22:23:34.586> GetLayerName: Layer 8 does not exist or has no name!
20-09-2024 22:23:34.586> GetLayerName: Layer 9 does not exist or has no name!
20-09-2024 22:23:34.588> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-09-2024 22:23:34.602> CMyMessageWnd::OnSetup   -   Setup dialog closed.
20-09-2024 22:23:34.606> Loaded 0 application specific profiles (0 normal and 0 custom window).
20-09-2024 22:23:39.613> There is a new version available! (2200500)
21-09-2024 01:38:12.949> The lock for 'MouseHookLLProc 1' took 656ms to release!

*******************************************************************************************************************************************
26-05-2025 04:49:57.556> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/install:"c:\users\<USER>\desktop\simplified_chinese.xmbclp"'
26-05-2025 04:49:57.557> Running in high integrity mode (0x00003000)
26-05-2025 04:49:57.558> Unable to find the default settings file 'C:\Users\<USER>\Desktop\XMBCSettings.xml' (Error code 2: ϵͳ�Ҳ���ָ�����ļ��� )
26-05-2025 04:49:57.558> HookDLL: Unable to load XML settings
26-05-2025 04:49:57.558> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 3000 ms
26-05-2025 04:49:57.558> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
26-05-2025 04:49:57.558> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
26-05-2025 04:49:57.561> Using keyboard language 0x04090409
26-05-2025 04:49:57.587> OnNextInstance: Installing language pack file: c:\users\<USER>\desktop\simplified_chinese.xmbclp (�������� (Simplified Chinese) : ������ NightSharp YFdyh000 Yuhanawa ����/����/�Ľ� : NightSharp / YFdyh000 / Yuhanawa)
26-05-2025 04:49:57.588> Unable to install language 'C:\Users\<USER>\Desktop\simplified_chinese.xmbclp' as it is already installed. Enabling it instead!
26-05-2025 04:49:57.590> CMyMessageWnd::OnSetup   -   Opening setup dialog.
26-05-2025 04:49:57.607> Unable to find the default settings file 'C:\Users\<USER>\Desktop\XMBCSettings.xml' (Error code 2: ϵͳ�Ҳ���ָ�����ļ��� )
26-05-2025 04:49:57.607> SetupDialog: Settings file 'C:\Users\<USER>\Desktop\XMBCSettings.xml' does not exist!
26-05-2025 04:49:57.757> GetLayerName: Layer 0 does not exist or has no name!
26-05-2025 04:49:57.757> GetLayerName: Layer 0 does not exist or has no name!
26-05-2025 04:49:57.757> GetLayerName: Layer 0 does not exist or has no name!
26-05-2025 04:49:57.757> GetLayerName: Layer 0 does not exist or has no name!
26-05-2025 04:49:57.762> GetLayerName: Layer 1 does not exist or has no name!
26-05-2025 04:49:57.762> GetLayerName: Layer 1 does not exist or has no name!
26-05-2025 04:49:57.762> GetLayerName: Layer 1 does not exist or has no name!
26-05-2025 04:49:57.762> GetLayerName: Layer 1 does not exist or has no name!
26-05-2025 04:50:31.681> Applying changes to X-Mouse Button Control settings...
26-05-2025 04:50:31.681> Unable to find the default settings file 'C:\Users\<USER>\Desktop\XMBCSettings.xml' (Error code 2: ϵͳ�Ҳ���ָ�����ļ��� )
26-05-2025 04:50:31.681> SetupDialog: Settings file 'C:\Users\<USER>\Desktop\XMBCSettings.xml' does not exist!
26-05-2025 04:50:31.681> Updating Windows hook timeout: 200
26-05-2025 04:50:31.681> There is no existing settings file. Attempting to creating a new one.
26-05-2025 04:50:31.682> GetLayerName: Layer 2 does not exist or has no name!
26-05-2025 04:50:31.682> GetLayerName: Layer 3 does not exist or has no name!
26-05-2025 04:50:31.682> GetLayerName: Layer 4 does not exist or has no name!
26-05-2025 04:50:31.682> GetLayerName: Layer 5 does not exist or has no name!
26-05-2025 04:50:31.682> GetLayerName: Layer 6 does not exist or has no name!
26-05-2025 04:50:31.682> GetLayerName: Layer 7 does not exist or has no name!
26-05-2025 04:50:31.682> GetLayerName: Layer 8 does not exist or has no name!
26-05-2025 04:50:31.682> GetLayerName: Layer 9 does not exist or has no name!
26-05-2025 04:50:31.683> Loaded 0 application specific profiles (0 normal and 0 custom window).
26-05-2025 04:50:31.698> CMyMessageWnd::OnSetup   -   Setup dialog closed.
26-05-2025 04:50:31.702> Loaded 0 application specific profiles (0 normal and 0 custom window).
26-05-2025 04:50:36.725> There is a new version available! (2200500)
26-05-2025 04:51:25.063> Exit requested by the user.
26-05-2025 04:51:25.063> Removing any active XMBCHook Timers...
26-05-2025 04:51:25.063> Timer thread exited with error code 0.
26-05-2025 04:51:25.063> CSendInput: Exiting SendInput thread.
26-05-2025 04:51:25.094> HookThread: Finished cleaning up.
26-05-2025 04:51:25.094> Hook thread exited with error code 0.
26-05-2025 04:51:25.094> RemoveMouseHook took 32ms
26-05-2025 04:51:25.094> X-Mouse Button Control Shutdown.


*******************************************************************************************************************************************
26-05-2025 04:55:16.611> X-Mouse Button Control v2.18.8 (x64) Startup. Commandline '/install:"c:\users\<USER>\desktop\simplified_chinese.xmbclp"'
26-05-2025 04:55:16.612> Running in high integrity mode (0x00003000)
26-05-2025 04:55:16.613> Loaded 0 application specific profiles (0 normal and 0 custom window).
26-05-2025 04:55:16.613> NewApplicationSettings: SPIThread is not initialised.
26-05-2025 04:55:16.613> Running on Microsoft Windows 10 Home China Edition (build 22631), 64-bit, Hook Timeout: 200 ms
26-05-2025 04:55:16.613> Warning: Low Level Hook Timeout is low. If you have problems try increasing this value.
26-05-2025 04:55:16.613> Startup folder: 'C:\Program Files\Highresolution Enterprises\X-Mouse Button Control\'
26-05-2025 04:55:16.613> Settings (and log) folder: 'C:\Users\<USER>\Desktop\'
26-05-2025 04:55:16.614> Using keyboard language 0x04090409
26-05-2025 04:55:16.639> OnNextInstance: Installing language pack file: c:\users\<USER>\desktop\simplified_chinese.xmbclp (�������� (Simplified Chinese) : ������ NightSharp YFdyh000 Yuhanawa ����/����/�Ľ� : NightSharp / YFdyh000 / Yuhanawa)
26-05-2025 04:55:16.640> Unable to install language 'C:\Users\<USER>\Desktop\simplified_chinese.xmbclp' as it is already installed. Enabling it instead!
26-05-2025 04:55:16.642> CMyMessageWnd::OnSetup   -   Opening setup dialog.
26-05-2025 04:55:16.650> Loaded 0 application specific profiles (0 normal and 0 custom window).
26-05-2025 04:57:16.614> There is a new version available! (2200500)
26-05-2025 05:02:04.613> Applying changes to X-Mouse Button Control settings...
26-05-2025 05:02:04.616> Loaded 0 application specific profiles (0 normal and 0 custom window).
26-05-2025 05:02:04.628> CMyMessageWnd::OnSetup   -   Setup dialog closed.
26-05-2025 05:02:04.633> Loaded 0 application specific profiles (0 normal and 0 custom window).
26-05-2025 05:02:07.009> Exit requested by the user.
26-05-2025 05:02:07.009> Removing any active XMBCHook Timers...
26-05-2025 05:02:07.009> Timer thread exited with error code 0.
26-05-2025 05:02:07.009> CSendInput: Exiting SendInput thread.
26-05-2025 05:02:07.019> HookThread: Finished cleaning up.
26-05-2025 05:02:07.019> Hook thread exited with error code 0.
26-05-2025 05:02:07.019> X-Mouse Button Control Shutdown.

