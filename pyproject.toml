[project]
name = "MaiBotNapcatAdapter"
version = "0.1.0"
description = "A MaiBot adapter for Napcat"

[tool.ruff]

include = ["*.py"]

# 行长度设置
line-length = 120

[tool.ruff.lint]
fixable = ["ALL"]
unfixable = []

# 启用的规则
select = [
    "E", # pycodestyle 错误
    "F", # pyflakes
    "B", # flake8-bugbear
]

ignore = ["E711","E501"]

[tool.ruff.format]
docstring-code-format = true
indent-style = "space"


# 使用双引号表示字符串
quote-style = "double"

# 尊重魔法尾随逗号
# 例如：
# items = [
#     "apple",
#     "banana",
#     "cherry",
# ]
skip-magic-trailing-comma = false

# 自动检测合适的换行符
line-ending = "auto"
