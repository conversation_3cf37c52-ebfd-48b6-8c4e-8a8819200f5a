//Microsoft Developer Studio generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// Korean resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_KOR)
#ifdef _WIN32
LANGUAGE LANG_KOREAN, SUBLANG_DEFAULT
#pragma code_page(949)
#endif //_WIN32

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE DISCARDABLE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE DISCARDABLE 
BEGIN
    "#include ""afxres.h""\r\n"
    "\0"
END

3 TEXTINCLUDE DISCARDABLE 
BEGIN
    "#define _AFX_NO_SPLITTER_RESOURCES\r\n"
    "#define _AFX_NO_OLE_RESOURCES\r\n"
    "#define _AFX_NO_TRACKER_RESOURCES\r\n"
    "#define _AFX_NO_PROPERTY_RESOURCES\r\n"
    "\r\n"
    "#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)\r\n"
    "#ifdef _WIN32\r\n"
    "LANGUAGE 9, 1\r\n"
    "#pragma code_page(1252)\r\n"
    "#endif //_WIN32\r\n"
    "#include ""afxres.rc""         // Standard components\r\n"
    "#endif\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDR_MAINFRAME           ICON    DISCARDABLE     "res\\favicon.ico"
#endif    // Korean resources
/////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_ABOUTBOX DIALOG DISCARDABLE  0, 0, 235, 55
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "About AutoClicker"
FONT 8, "Helvetica"
BEGIN
    ICON            IDR_MAINFRAME,IDC_STATIC,11,17,20,20
    LTEXT           "AutoClicker Version 1.2.1.0",IDC_STATIC,40,10,119,8,
                    SS_NOPREFIX
    LTEXT           "Copyright (C) 2018-2019",IDC_STATIC,40,25,119,8
    DEFPUSHBUTTON   "OK",IDOK,178,7,50,14,WS_GROUP
END

IDD_AUTOCLICKER_DIALOG DIALOGEX 0, 0, 239, 289
STYLE DS_MODALFRAME | WS_VISIBLE | WS_OVERLAPPED | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME | WS_MINIMIZEBOX
EXSTYLE WS_EX_APPWINDOW
CAPTION "AutoClicker"
FONT 8, "Helvetica"
BEGIN
    DEFPUSHBUTTON   "Start [SPACE]",IDOK,73,218,60,14
    //PUSHBUTTON      "Cancel",IDCANCEL,126,246,50,14
    LISTBOX         IDC_XY_LIST,154,17,75,135,//LBS_SORT | 
                    LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP
    GROUPBOX        "Mouse XY List",IDC_STATIC,152,7,80,169
    GROUPBOX        "Mouse Coordinates",IDC_STATIC,7,7,141,46
    LTEXT           "Coordinate-X:",IDC_STATIC,13,21,47,8
    EDITTEXT        IDC_X_EDT,98,17,40,14,ES_RIGHT | ES_AUTOHSCROLL | 
                    ES_READONLY | ES_NUMBER
    LTEXT           "Coordinate-Y:",IDC_STATIC,13,37,47,8
    EDITTEXT        IDC_Y_EDT,98,33,40,14,ES_RIGHT | ES_AUTOHSCROLL | 
                    ES_READONLY | ES_NUMBER
    GROUPBOX        "Time Settings",IDC_STATIC,7,56,141,97
    LTEXT           "Click Interval:",IDC_STATIC,14,67,44,8
    EDITTEXT        IDC_CLICK_INTERVAL_EDT,70,65,51,14,ES_RIGHT | 
                    ES_AUTOHSCROLL | ES_NUMBER
    LTEXT           "ms",IDC_STATIC,124,67,10,8
    LTEXT           "Stop After:",IDC_STATIC,14,83,35,8
    EDITTEXT        IDC_STOP_AFTER_CLICKS_EDT,70,81,51,14,ES_RIGHT | 
                    ES_AUTOHSCROLL | ES_NUMBER
    LTEXT           "Clicks",IDC_STATIC,124,83,20,8
    LTEXT           "Stop After Time:",IDC_STATIC,14,116,77,8
    EDITTEXT        IDC_STOP_AFTER_MINS_EDT,70,114,51,14,ES_RIGHT | 
                    ES_AUTOHSCROLL | ES_NUMBER
    LTEXT           "mins",IDC_STATIC,124,116,16,8
    GROUPBOX        "Mouse Button",IDC_STATIC,7,158,58,63
    CONTROL         "Left",IDC_MOUSE_BUTTON_RDO,"Button",BS_AUTORADIOBUTTON | 
                    WS_GROUP | WS_TABSTOP,14,174,28,10
    CONTROL         "Right",IDC_RIGHT_BUTTON_RDO,"Button",BS_AUTORADIOBUTTON | 
                    WS_TABSTOP,14,193,33,10
    PUSHBUTTON      "Save",IDC_SAVE_BTN,152,178,38,14
    PUSHBUTTON      "Load",IDC_LOAD_BTN,194,178,38,14
	PUSHBUTTON      "Randomize Points", IDC_RANDOMIZE_BTN, 152, 195, 80, 14
	PUSHBUTTON      "Bounding Rectangle", IDC_BOUNDRECT_BTN, 152, 212, 80, 14
    GROUPBOX        "Quick Help",IDC_STATIC,69,158,79,80
    LTEXT           "Add 1 Point [F3]",IDC_STATIC,73,170,72,8
    LTEXT           "Delete 1 Point [DEL]",IDC_STATIC,73,182,72,8
	LTEXT           "Pause [SPACE]", IDC_STATIC, 73, 194, 72, 8
    LTEXT           "Stop Clicking [ESC]",IDC_STATIC,73,206,72,8
    CONTROL         "Window Always Top",IDC_WIN_ALWAYS_TOP_CHK,"Button",
                    BS_AUTOCHECKBOX | WS_TABSTOP,7,250,81,10
    LTEXT           "Count:",IDC_STATIC,155,157,24,8
    EDITTEXT        IDC_COUNT_EDT,185,157,40,12,ES_RIGHT | ES_AUTOHSCROLL | 
                    ES_READONLY | ES_NUMBER | NOT WS_BORDER
    EDITTEXT        IDC_CURRENT_CLICKS_EDT,77,98,43,12,ES_RIGHT | 
                    ES_AUTOHSCROLL | ES_READONLY | ES_NUMBER | NOT WS_BORDER
    LTEXT           "Clicks",IDC_STATIC,124,98,20,8
    LTEXT           "Current:",IDC_STATIC,14,98,26,8
    EDITTEXT        IDC_CURRENT_MINS_EDT,74,134,45,12,ES_RIGHT | 
                    ES_AUTOHSCROLL | ES_READONLY | ES_NUMBER | NOT WS_BORDER
    LTEXT           "Current Time:",IDC_STATIC,15,134,77,8
	PUSHBUTTON      "Run single app", IDC_RUNSINGLEAPP_BTN, 70, 268, 67, 14
	LTEXT           "Just Click At Mouse Cursor [RETURN]", IDC_STATIC, 106, 251, 130, 8
END


#ifndef _MAC
/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,0,1
 PRODUCTVERSION 1,0,0,1
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "Comments", "\0"
            VALUE "CompanyName", "\0"
            VALUE "FileDescription", "AutoClicker MFC Application\0"
            VALUE "FileVersion", "1, 0, 0, 1\0"
            VALUE "InternalName", "AutoClicker\0"
            VALUE "LegalCopyright", "Copyright (C) 2018-2019\0"
            VALUE "LegalTrademarks", "\0"
            VALUE "OriginalFilename", "AutoClicker.EXE\0"
            VALUE "PrivateBuild", "\0"
            VALUE "ProductName", "AutoClicker Application\0"
            VALUE "ProductVersion", "1, 0, 0, 1\0"
            VALUE "SpecialBuild", "\0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

#endif    // !_MAC


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO DISCARDABLE 
BEGIN
    IDD_ABOUTBOX, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 228
        TOPMARGIN, 7
        BOTTOMMARGIN, 48
    END

    IDD_AUTOCLICKER_DIALOG, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 232
        TOPMARGIN, 7
        BOTTOMMARGIN, 282
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE DISCARDABLE 
BEGIN
    IDS_ABOUTBOX            "&About AutoClicker..."
END

#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//
#define _AFX_NO_SPLITTER_RESOURCES
#define _AFX_NO_OLE_RESOURCES
#define _AFX_NO_TRACKER_RESOURCES
#define _AFX_NO_PROPERTY_RESOURCES

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE 9, 1
#pragma code_page(1252)
#endif //_WIN32
#include "afxres.rc"         // Standard components
#endif

/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

