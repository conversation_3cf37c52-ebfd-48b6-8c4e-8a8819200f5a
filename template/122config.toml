[inner]
version = "1.6.0"

#----以下是给开发人员阅读的，如果你只是部署了麦麦，不需要阅读----
#如果你想要修改配置文件，请在修改后将version的值进行变更
#如果新增项目，请在BotConfig类下新增相应的变量
#1.如果你修改的是[]层级项目，例如你新增了 [memory],那么请在config.py的 load_config函数中的include_configs字典中新增"内容":{
#"func":memory,
#"support":">=0.0.0",  #新的版本号
#"necessary":False      #是否必须
#}
#2.如果你修改的是[]下的项目，例如你新增了[memory]下的 memory_ban_words ,那么请在config.py的 load_config函数中的 memory函数下新增版本判断:
            # if config.INNER_VERSION in SpecifierSet(">=0.0.2"):
            #     config.memory_ban_words = set(memory_config.get("memory_ban_words", []))

# 版本格式：主版本号.次版本号.修订号，版本号递增规则如下：
#     主版本号：当你做了不兼容的 API 修改，
#     次版本号：当你做了向下兼容的功能性新增，
#     修订号：当你做了向下兼容的问题修正。
# 先行版本号及版本编译信息可以加到"主版本号.次版本号.修订号"的后面，作为延伸。
#----以上是给开发人员阅读的，如果你只是部署了麦麦，不需要阅读----

[bot]
qq = 1145141919810
nickname = "麦麦"
alias_names = ["麦叠", "牢麦"] #该选项还在调试中，暂时未生效

[groups]
talk_allowed = [
    123,
    123,
]  #可以回复消息的群号码
talk_frequency_down = []  #降低回复频率的群号码
ban_user_id = []  #禁止回复和读取消息的QQ号

[personality] #未完善
personality_core = "用一句话或几句话描述人格的核心特点" # 建议20字以内，谁再写3000字小作文敲谁脑袋
personality_sides = [
    "用一句话或几句话描述人格的一些细节",
    "用一句话或几句话描述人格的一些细节",
    "用一句话或几句话描述人格的一些细节",
    "用一句话或几句话描述人格的一些细节",
    "用一句话或几句话描述人格的一些细节",
]# 条数任意，不能为0, 该选项还在调试中，可能未完全生效

[identity] #アイデンティティがない 生まれないらららら
# 兴趣爱好 未完善，有些条目未使用
identity_detail = [
    "身份特点",
    "身份特点",
]# 条数任意，不能为0, 该选项还在调试中
#外貌特征
age = 20 # 年龄 单位岁 
gender = "男" # 性别 
appearance = "用几句话描述外貌特征" # 外貌特征 该选项还在调试中，暂时未生效

[schedule]
enable_schedule_gen = true # 是否启用日程表
enable_schedule_interaction = true # 日程表是否影响回复模式
prompt_schedule_gen = "用几句话描述描述性格特点或行动规律，这个特征会用来生成日程表"
schedule_doing_update_interval = 900 # 日程表更新间隔 单位秒
schedule_temperature = 0.1 # 日程表温度，建议0.1-0.5
time_zone = "Asia/Shanghai" # 给你的机器人设置时区，可以解决运行电脑时区和国内时区不同的情况，或者模拟国外留学生日程

[platforms] # 必填项目，填写每个平台适配器提供的链接
nonebot-qq="http://127.0.0.1:18002/api/message"

[chat] #麦麦的聊天通用设置
allow_focus_mode = true # 是否允许专注聊天状态
# 是否启用heart_flowC(HFC)模式
# 启用后麦麦会自主选择进入heart_flowC模式(持续一段时间），进行主动的观察和回复，并给出回复，比较消耗token
base_normal_chat_num = 3 # 最多允许多少个群进行普通聊天
base_focused_chat_num = 2 # 最多允许多少个群进行专注聊天

observation_context_size = 15 # 观察到的最长上下文大小,建议15，太短太长都会导致脑袋尖尖
message_buffer = true # 启用消息缓冲器？启用此项以解决消息的拆分问题，但会使麦麦的回复延迟

# 以下是消息过滤，可以根据规则过滤特定消息，将不会读取这些消息
ban_words = [
    # "403","张三"
    ]

ban_msgs_regex = [
    # 需要过滤的消息（原始消息）匹配的正则表达式，匹配到的消息将被过滤（支持CQ码），若不了解正则表达式请勿修改
    #"https?://[^\\s]+", # 匹配https链接
    #"\\d{4}-\\d{2}-\\d{2}", # 匹配日期
    # "\\[CQ:at,qq=\\d+\\]" # 匹配@
]

[normal_chat] #普通聊天
#一般回复参数
model_reasoning_probability = 0.7 # 麦麦回答时选择推理模型 模型的概率
model_normal_probability = 0.3 # 麦麦回答时选择一般模型 模型的概率

emoji_chance = 0.2 # 麦麦一般回复时使用表情包的概率，设置为1让麦麦自己决定发不发
thinking_timeout = 100 # 麦麦最长思考时间，超过这个时间的思考会放弃（往往是api反应太慢）

willing_mode = "classical" # 回复意愿模式 —— 经典模式：classical，动态模式：dynamic，mxp模式：mxp，自定义模式：custom（需要你自己实现）
response_willing_amplifier = 1 # 麦麦回复意愿放大系数，一般为1
response_interested_rate_amplifier = 1 # 麦麦回复兴趣度放大系数,听到记忆里的内容时放大系数
down_frequency_rate = 3 # 降低回复频率的群组回复意愿降低系数 除法
emoji_response_penalty = 0 # 表情包回复惩罚系数，设为0为不回复单个表情包，减少单独回复表情包的概率
mentioned_bot_inevitable_reply = false # 提及 bot 必然回复
at_bot_inevitable_reply = false # @bot 必然回复

[focus_chat] #专注聊天
reply_trigger_threshold = 3.6 # 专注聊天触发阈值，越低越容易进入专注聊天
default_decay_rate_per_second = 0.95 # 默认衰减率，越大衰减越快，越高越难进入专注聊天
consecutive_no_reply_threshold = 3 # 连续不回复的阈值，越低越容易结束专注聊天

# 以下选项暂时无效
compressed_length = 5 # 不能大于observation_context_size,心流上下文压缩的最短压缩长度，超过心流观察到的上下文长度，会压缩，最短压缩长度为5
compress_length_limit = 5 #最多压缩份数，超过该数值的压缩上下文会被删除


[emoji]
max_emoji_num = 40 # 表情包最大数量
max_reach_deletion = true # 开启则在达到最大数量时删除表情包，关闭则达到最大数量时不删除，只是不会继续收集表情包
check_interval = 10 # 检查表情包（注册，破损，删除）的时间间隔(分钟)
save_pic = false # 是否保存图片
save_emoji = false # 是否保存表情包
steal_emoji = true # 是否偷取表情包，让麦麦可以发送她保存的这些表情包
enable_check = false  # 是否启用表情包过滤，只有符合该要求的表情包才会被保存
check_prompt = "符合公序良俗" # 表情包过滤要求，只有符合该要求的表情包才会被保存

[memory]
build_memory_interval = 2000 # 记忆构建间隔 单位秒   间隔越低，麦麦学习越多，但是冗余信息也会增多
build_memory_distribution = [6.0,3.0,0.6,32.0,12.0,0.4] # 记忆构建分布，参数：分布1均值，标准差，权重，分布2均值，标准差，权重
build_memory_sample_num = 8 # 采样数量，数值越高记忆采样次数越多
build_memory_sample_length = 40 # 采样长度，数值越高一段记忆内容越丰富
memory_compress_rate = 0.1 # 记忆压缩率 控制记忆精简程度 建议保持默认,调高可以获得更多信息，但是冗余信息也会增多

forget_memory_interval = 1000 # 记忆遗忘间隔 单位秒   间隔越低，麦麦遗忘越频繁，记忆更精简，但更难学习
memory_forget_time = 24 #多长时间后的记忆会被遗忘 单位小时 
memory_forget_percentage = 0.01 # 记忆遗忘比例 控制记忆遗忘程度 越大遗忘越多 建议保持默认

consolidate_memory_interval = 1000 # 记忆整合间隔 单位秒   间隔越低，麦麦整合越频繁，记忆更精简
consolidation_similarity_threshold = 0.7 # 相似度阈值
consolidation_check_percentage = 0.01 # 检查节点比例

#不希望记忆的词，已经记忆的不会受到影响
memory_ban_words = [ 
    # "403","张三"
]

[mood]
mood_update_interval = 1.0 # 情绪更新间隔 单位秒
mood_decay_rate = 0.95 # 情绪衰减率
mood_intensity_factor = 1.0 # 情绪强度因子

[keywords_reaction] # 针对某个关键词作出反应
enable = true  # 关键词反应功能的总开关

[[keywords_reaction.rules]] # 如果想要新增多个关键词，直接复制本条，修改keywords和reaction即可
enable = true # 是否启用此条（为了人类在未来AI战争能更好地识别AI（bushi），默认开启）
keywords = ["人机", "bot", "机器", "入机", "robot", "机器人","ai","AI"] # 会触发反应的关键词
reaction = "有人说你是人机或者机器人，否定这一事实，攻击或反问他。注意，只有认为你是机器人才回复，否则不要否认" # 触发之后添加的提示词

[[keywords_reaction.rules]] # 就像这样复制
enable = false # 仅作示例，不会触发
keywords = ["测试关键词回复","test",""]
reaction = "回答“测试成功”" # 修复错误的引号

[[keywords_reaction.rules]] # 使用正则表达式匹配句式
enable = false # 仅作示例，不会触发
regex = ["^(?P<n>\\S{1,20})是这样的$"] # 将匹配到的词汇命名为n，反应中对应的[n]会被替换为匹配到的内容，若不了解正则表达式请勿编写
reaction = "请按照以下模板造句：[n]是这样的，xx只要xx就可以，可是[n]要考虑的事情就很多了，比如什么时候xx，什么时候xx，什么时候xx。（请自由发挥替换xx部分，只需保持句式结构，同时表达一种将[n]过度重视的反讽意味）"

[chinese_typo]
enable = true # 是否启用中文错别字生成器
error_rate=0.001 # 单字替换概率
min_freq=9 # 最小字频阈值
tone_error_rate=0.1 # 声调错误概率
word_replace_rate=0.006 # 整词替换概率

[response_splitter]
enable_response_splitter = true # 是否启用回复分割器
response_max_length = 256 # 回复允许的最大长度
response_max_sentence_num = 4 # 回复允许的最大句子数
enable_kaomoji_protection = false # 是否启用颜文字保护

model_max_output_length = 256 # 模型单次返回的最大token数

[remote] #发送统计信息，主要是看全球有多少只麦麦
enable = true

[experimental] #实验性功能
enable_friend_chat = false # 是否启用好友聊天
pfc_chatting = false # 是否启用PFC聊天，该功能仅作用于私聊，与回复模式独立

#下面的模型若使用硅基流动则不需要更改，使用ds官方则改成.env自定义的宏，使用自定义模型则选择定位相似的模型自己填写
#推理模型

# 额外字段
# 下面的模型有以下额外字段可以添加：

# stream = <true|false> : 用于指定模型是否是使用流式输出
# 如果不指定，则该项是 False

#这个模型必须是推理模型
[model.llm_reasoning] # 一般聊天模式的推理回复模型
name = "Pro/deepseek-ai/DeepSeek-R1"
provider = "SILICONFLOW"
pri_in = 1.0 #模型的输入价格（非必填，可以记录消耗）
pri_out = 4.0 #模型的输出价格（非必填，可以记录消耗）

[model.llm_normal] #V3 回复模型 专注和一般聊天模式共用的回复模型
name = "Pro/deepseek-ai/DeepSeek-V3"
provider = "SILICONFLOW"
pri_in = 2 #模型的输入价格（非必填，可以记录消耗）
pri_out = 8 #模型的输出价格（非必填，可以记录消耗）
#默认temp 0.2 如果你使用的是老V3或者其他模型，请自己修改temp参数
temp = 0.2 #模型的温度，新V3建议0.1-0.3

[model.llm_topic_judge] #主题判断模型：建议使用qwen2.5 7b
name = "Pro/Qwen/Qwen2.5-7B-Instruct"
provider = "SILICONFLOW"
pri_in = 0.35
pri_out = 0.35

[model.llm_summary] #概括模型，建议使用qwen2.5 32b 及以上
name = "Qwen/Qwen2.5-32B-Instruct"
provider = "SILICONFLOW"
pri_in = 1.26
pri_out = 1.26

[model.vlm] # 图像识别模型
name = "Pro/Qwen/Qwen2.5-VL-7B-Instruct"
provider = "SILICONFLOW"
pri_in = 0.35
pri_out = 0.35

[model.llm_heartflow] # 用于控制麦麦是否参与聊天的模型
name = "Qwen/Qwen2.5-32B-Instruct"
provider = "SILICONFLOW"
pri_in = 1.26
pri_out = 1.26

[model.llm_observation] #观察模型，压缩聊天内容，建议用免费的
# name = "Pro/Qwen/Qwen2.5-7B-Instruct"
name = "Qwen/Qwen2.5-7B-Instruct"
provider = "SILICONFLOW"
pri_in = 0
pri_out = 0

[model.llm_sub_heartflow] #心流：认真水群时,生成麦麦的内心想法，必须使用具有工具调用能力的模型
name = "Pro/deepseek-ai/DeepSeek-V3"
provider = "SILICONFLOW"
pri_in = 2
pri_out = 8
temp = 0.3 #模型的温度，新V3建议0.1-0.3

[model.llm_plan] #决策：认真水群时,负责决定麦麦该做什么
name = "Pro/deepseek-ai/DeepSeek-V3"
provider = "SILICONFLOW"
pri_in = 2
pri_out = 8

#嵌入模型

[model.embedding] #嵌入
name = "BAAI/bge-m3"
provider = "SILICONFLOW"
pri_in = 0
pri_out = 0


#私聊PFC：需要开启PFC功能，默认三个模型均为硅基流动v3，如果需要支持多人同时私聊或频繁调用，建议把其中的一个或两个换成官方v3或其它模型，以免撞到429

#PFC决策模型
[model.llm_PFC_action_planner]
name = "Pro/deepseek-ai/DeepSeek-V3"
provider = "SILICONFLOW"
temp = 0.3
pri_in = 2
pri_out = 8

#PFC聊天模型
[model.llm_PFC_chat]
name = "Pro/deepseek-ai/DeepSeek-V3"
provider = "SILICONFLOW"
temp = 0.3
pri_in = 2
pri_out = 8

#PFC检查模型
[model.llm_PFC_reply_checker]
name = "Pro/deepseek-ai/DeepSeek-V3"
provider = "SILICONFLOW"
pri_in = 2
pri_out = 8


#以下模型暂时没有使用！！
#以下模型暂时没有使用！！
#以下模型暂时没有使用！！
#以下模型暂时没有使用！！
#以下模型暂时没有使用！！

[model.llm_tool_use] #工具调用模型，需要使用支持工具调用的模型，建议使用qwen2.5 32b
name = "Qwen/Qwen2.5-32B-Instruct"
provider = "SILICONFLOW"
pri_in = 1.26
pri_out = 1.26