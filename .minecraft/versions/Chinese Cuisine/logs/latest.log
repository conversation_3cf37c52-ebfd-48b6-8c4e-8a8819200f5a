[018月2025 17:54:21.688] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher running: args [--username, Quasar2323, --version, Chinese Cuisine, --gameDir, C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine, --assetsDir, C:\Users\<USER>\Desktop\.minecraft\assets, --assetIndex, 5, --uuid, 077e3afcb77643c48ae30f9dbb755bef, --accessToken, ❄❄❄❄❄❄❄❄, --clientId, ${clientid}, --xuid, ${auth_xuid}, --userType, msa, --versionType, PCL, --width, 854, --height, 480, --launchTarget, forgeclient, --fml.forgeVersion, 47.4.0, --fml.mcVersion, 1.20.1, --fml.forgeGroup, net.minecraftforge, --fml.mcpVersion, ********.114412]
[018月2025 17:54:21.699] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher 10.0.9+10.0.9+main.dcd20f30 starting: java version 17.0.15 by Eclipse Adoptium; OS Windows 11 arch amd64 version 10.0
[018月2025 17:54:22.856] [main/INFO] [net.minecraftforge.fml.loading.ImmediateWindowHandler/]: Loading ImmediateWindowProvider fmlearlywindow
[018月2025 17:54:22.984] [main/INFO] [EARLYDISPLAY/]: Trying GL version 4.6
[018月2025 17:54:23.831] [main/INFO] [EARLYDISPLAY/]: Requested GL version 4.6 got version 4.6
[018月2025 17:54:23.919] [main/INFO] [mixin/]: SpongePowered MIXIN Subsystem Version=0.8.5 Source=union:/C:/Users/<USER>/Desktop/.minecraft/libraries/org/spongepowered/mixin/0.8.5/mixin-0.8.5.jar%23150!/ Service=ModLauncher Env=CLIENT
[018月2025 17:54:23.965] [pool-2-thread-1/INFO] [EARLYDISPLAY/]: GL info: NVIDIA GeForce RTX 4060 Laptop GPU/PCIe/SSE2 GL version 4.6.0 NVIDIA 572.70, NVIDIA Corporation
[018月2025 17:54:29.273] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\fmlcore\1.20.1-47.4.0\fmlcore-1.20.1-47.4.0.jar is missing mods.toml file
[018月2025 17:54:29.280] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\javafmllanguage\1.20.1-47.4.0\javafmllanguage-1.20.1-47.4.0.jar is missing mods.toml file
[018月2025 17:54:29.284] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\lowcodelanguage\1.20.1-47.4.0\lowcodelanguage-1.20.1-47.4.0.jar is missing mods.toml file
[018月2025 17:54:29.290] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\Desktop\.minecraft\libraries\net\minecraftforge\mclanguage\1.20.1-47.4.0\mclanguage-1.20.1-47.4.0.jar is missing mods.toml file
[018月2025 17:54:30.031] [main/WARN] [net.minecraftforge.jarjar.selection.JarSelector/]: Attempted to select two dependency jars from JarJar which have the same identification: Mod File:  and Mod File: . Using Mod File: 
[018月2025 17:54:30.032] [main/INFO] [net.minecraftforge.fml.loading.moddiscovery.JarInJarDependencyLocator/]: Found 41 dependencies adding them to mods collection
[018月2025 17:54:30.365] [main/INFO] [org.groovymc.gml.mappings.MappingsProvider/]: Starting runtime mappings setup...
[018月2025 17:54:30.399] [main/INFO] [org.groovymc.gml.internal.locator.ModLocatorInjector/]: Injecting ScriptModLocator candidates...
[018月2025 17:54:30.425] [main/INFO] [org.groovymc.gml.scriptmods.ScriptModLocator/]: Injected Jimfs file system
[018月2025 17:54:30.438] [main/INFO] [org.groovymc.gml.scriptmods.ScriptModLocator/]: Skipped loading script mods from directory C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\mods\scripts as it did not exist.
[018月2025 17:54:30.444] [main/INFO] [org.groovymc.gml.internal.locator.ModLocatorInjector/]: Injected ScriptModLocator mod candidates. Found 0 valid mod candidates and 0 broken mod files.
[018月2025 17:54:31.494] [GML Mappings Thread/INFO] [org.groovymc.gml.mappings.MappingsProvider/]: Found version metadata from piston-meta.
[018月2025 17:54:31.638] [GML Mappings Thread/INFO] [org.groovymc.gml.mappings.MappingsProvider/]: version.json is up to date.
[018月2025 17:54:32.389] [GML Mappings Thread/INFO] [org.groovymc.gml.mappings.MappingsProvider/]: Official mappings are up to date.
[018月2025 17:54:33.409] [GML Mappings Thread/INFO] [org.groovymc.gml.mappings.MappingsProvider/]: MCPConfig is up to date.
[018月2025 17:54:33.659] [main/INFO] [mixin/]: Compatibility level set to JAVA_17
[018月2025 17:54:33.822] [main/INFO] [cpw.mods.modlauncher.LaunchServiceHandler/MODLAUNCHER]: Launching target 'forgeclient' with arguments [--version, Chinese Cuisine, --gameDir, C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine, --assetsDir, C:\Users\<USER>\Desktop\.minecraft\assets, --uuid, 077e3afcb77643c48ae30f9dbb755bef, --username, Quasar2323, --assetIndex, 5, --accessToken, ❄❄❄❄❄❄❄❄, --clientId, ${clientid}, --xuid, ${auth_xuid}, --userType, msa, --versionType, PCL, --width, 854, --height, 480]
[018月2025 17:54:33.918] [main/WARN] [mixin/]: Reference map 'trashslot.refmap.json' for trashslot.mixins.json could not be read. If this is a development environment you can ignore this message
[018月2025 17:54:34.127] [main/INFO] [ModernFix/]: Loaded configuration file for ModernFix 5.24.3+mc1.20.1: 96 options available, 0 override(s) found
[018月2025 17:54:34.128] [main/INFO] [ModernFix/]: Applying Nashorn fix
[018月2025 17:54:34.178] [main/INFO] [ModernFix/]: Applied Forge config corruption patch
[018月2025 17:54:34.388] [main/WARN] [mixin/]: Reference map 'createbitterballen.refmap.json' for create_bic_bit.mixins.json could not be read. If this is a development environment you can ignore this message
[018月2025 17:54:34.436] [main/INFO] [Embeddium/]: Loaded configuration file for Embeddium: 127 options available, 3 override(s) found
[018月2025 17:54:34.440] [main/INFO] [Embeddium-GraphicsAdapterProbe/]: Searching for graphics cards...
[018月2025 17:54:35.074] [GML Mappings Thread/INFO] [org.groovymc.gml.mappings.MappingsProvider/]: Loaded runtime mappings in 4617ms
[018月2025 17:54:35.074] [GML Mappings Thread/INFO] [org.groovymc.gml.mappings.MappingsProvider/]: Finished runtime mappings setup.
[018月2025 17:54:35.087] [main/INFO] [Embeddium-GraphicsAdapterProbe/]: Found graphics card: GraphicsAdapterInfo[vendor=UNKNOWN, name=OrayIddDriver Device, version=DriverVersion=17.1.58.818]
[018月2025 17:54:35.087] [main/INFO] [Embeddium-GraphicsAdapterProbe/]: Found graphics card: GraphicsAdapterInfo[vendor=INTEL, name=Intel(R) UHD Graphics, version=DriverVersion=31.0.101.5333]
[018月2025 17:54:35.087] [main/INFO] [Embeddium-GraphicsAdapterProbe/]: Found graphics card: GraphicsAdapterInfo[vendor=NVIDIA, name=NVIDIA GeForce RTX 4060 Laptop GPU, version=DriverVersion=32.0.15.7270]
[018月2025 17:54:35.098] [main/WARN] [Embeddium-Workarounds/]: Embeddium has applied one or more workarounds to prevent crashes or other issues on your system: [NVIDIA_THREADED_OPTIMIZATIONS]
[018月2025 17:54:35.098] [main/WARN] [Embeddium-Workarounds/]: This is not necessarily an issue, but it may result in certain features or optimizations being disabled. You can sometimes fix these issues by upgrading your graphics driver.
[018月2025 17:54:35.182] [main/WARN] [mixin/]: Reference map 'create_cuisine.refmap.json' for create_cuisine.mixins.json could not be read. If this is a development environment you can ignore this message
[018月2025 17:54:35.251] [main/WARN] [mixin/]: Reference map 'probejs-forge-refmap.json' for probejs.mixins.json could not be read. If this is a development environment you can ignore this message
[018月2025 17:54:35.257] [main/WARN] [mixin/]: Reference map 'cuisinedelight.refmap.json' for cuisinedelight.mixins.json could not be read. If this is a development environment you can ignore this message
[018月2025 17:54:35.947] [main/WARN] [mixin/]: Error loading class: com/ultramega/showcaseitem/ShowcaseItemFeature (java.lang.ClassNotFoundException: com.ultramega.showcaseitem.ShowcaseItemFeature)
[018月2025 17:54:36.196] [main/WARN] [mixin/]: Error loading class: dev/emi/emi/screen/EmiScreenManager (java.lang.ClassNotFoundException: dev.emi.emi.screen.EmiScreenManager)
[018月2025 17:54:36.200] [main/WARN] [mixin/]: Error loading class: me/shedaniel/rei/impl/client/gui/ScreenOverlayImpl (java.lang.ClassNotFoundException: me.shedaniel.rei.impl.client.gui.ScreenOverlayImpl)
[018月2025 17:54:36.223] [main/WARN] [mixin/]: Error loading class: com/copycatsplus/copycats/content/copycat/slab/CopycatSlabBlock (java.lang.ClassNotFoundException: com.copycatsplus.copycats.content.copycat.slab.CopycatSlabBlock)
[018月2025 17:54:36.223] [main/WARN] [mixin/]: @Mixin target com.copycatsplus.copycats.content.copycat.slab.CopycatSlabBlock was not found create_connected.mixins.json:compat.CopycatBlockMixin
[018月2025 17:54:36.225] [main/WARN] [mixin/]: Error loading class: com/copycatsplus/copycats/content/copycat/board/CopycatBoardBlock (java.lang.ClassNotFoundException: com.copycatsplus.copycats.content.copycat.board.CopycatBoardBlock)
[018月2025 17:54:36.225] [main/WARN] [mixin/]: @Mixin target com.copycatsplus.copycats.content.copycat.board.CopycatBoardBlock was not found create_connected.mixins.json:compat.CopycatBlockMixin
[018月2025 17:54:36.237] [main/WARN] [mixin/]: Error loading class: me/jellysquid/mods/lithium/common/ai/pathing/PathNodeDefaults (java.lang.ClassNotFoundException: me.jellysquid.mods.lithium.common.ai.pathing.PathNodeDefaults)
[018月2025 17:54:36.414] [main/WARN] [mixin/]: Error loading class: mekanism/client/render/entity/RenderFlame (java.lang.ClassNotFoundException: mekanism.client.render.entity.RenderFlame)
[018月2025 17:54:36.416] [main/WARN] [mixin/]: Error loading class: mekanism/client/render/armor/MekaSuitArmor (java.lang.ClassNotFoundException: mekanism.client.render.armor.MekaSuitArmor)
[018月2025 17:54:36.896] [main/WARN] [mixin/]: Error loading class: noobanidus/mods/lootr/config/ConfigManager (java.lang.ClassNotFoundException: noobanidus.mods.lootr.config.ConfigManager)
[018月2025 17:54:36.896] [main/WARN] [mixin/]: @Mixin target noobanidus.mods.lootr.config.ConfigManager was not found quark_integrations.mixins.json:lootr.ConfigManagerMixin
[018月2025 17:54:36.941] [main/WARN] [mixin/]: Error loading class: mezz/modnametooltip/TooltipEventHandler (java.lang.ClassNotFoundException: mezz.modnametooltip.TooltipEventHandler)
[018月2025 17:54:36.943] [main/WARN] [mixin/]: Error loading class: me/shedaniel/rei/impl/client/ClientHelperImpl (java.lang.ClassNotFoundException: me.shedaniel.rei.impl.client.ClientHelperImpl)
[018月2025 17:54:37.001] [main/WARN] [mixin/]: Error loading class: journeymap/client/ui/fullscreen/Fullscreen (java.lang.ClassNotFoundException: journeymap.client.ui.fullscreen.Fullscreen)
[018月2025 17:54:37.001] [main/WARN] [mixin/]: @Mixin target journeymap.client.ui.fullscreen.Fullscreen was not found create.mixins.json:compat.JourneyFullscreenMapMixin
[018月2025 17:54:37.009] [main/WARN] [mixin/]: Error loading class: studio/fantasyit/ars_botania/event/CapEvent (java.lang.ClassNotFoundException: studio.fantasyit.ars_botania.event.CapEvent)
[018月2025 17:54:37.009] [main/WARN] [mixin/]: @Mixin target studio.fantasyit.ars_botania.event.CapEvent was not found create_central_kitchen.mixins.json:common.arsbotania.CapEventMixin
[018月2025 17:54:37.011] [main/WARN] [mixin/]: Error loading class: earth/terrarium/botarium/forge/BotariumForge (java.lang.ClassNotFoundException: earth.terrarium.botarium.forge.BotariumForge)
[018月2025 17:54:37.011] [main/WARN] [mixin/]: @Mixin target earth.terrarium.botarium.forge.BotariumForge was not found create_central_kitchen.mixins.json:common.botarium.BotariumForgeMixin
[018月2025 17:54:37.017] [main/WARN] [mixin/]: Error loading class: dan200/computercraft/shared/integration/MoreRedIntegration (java.lang.ClassNotFoundException: dan200.computercraft.shared.integration.MoreRedIntegration)
[018月2025 17:54:37.017] [main/WARN] [mixin/]: @Mixin target dan200.computercraft.shared.integration.MoreRedIntegration was not found create_central_kitchen.mixins.json:common.computercraft.MoreRedIntegrationMixin
[018月2025 17:54:37.093] [main/WARN] [mixin/]: Error loading class: umpaz/farmersrespite/common/block/CoffeeBushBlock (java.lang.ClassNotFoundException: umpaz.farmersrespite.common.block.CoffeeBushBlock)
[018月2025 17:54:37.094] [main/WARN] [mixin/]: @Mixin target umpaz.farmersrespite.common.block.CoffeeBushBlock was not found create_central_kitchen.mixins.json:common.farmersrespite.CoffeeBushBlockMixin
[018月2025 17:54:37.095] [main/WARN] [mixin/]: Error loading class: umpaz/farmersrespite/common/block/CoffeeBushTopBlock (java.lang.ClassNotFoundException: umpaz.farmersrespite.common.block.CoffeeBushTopBlock)
[018月2025 17:54:37.095] [main/WARN] [mixin/]: @Mixin target umpaz.farmersrespite.common.block.CoffeeBushTopBlock was not found create_central_kitchen.mixins.json:common.farmersrespite.CoffeeBushTopBlockMixin
[018月2025 17:54:37.097] [main/WARN] [mixin/]: Error loading class: umpaz/farmersrespite/common/block/CoffeeDoubleStemBlock (java.lang.ClassNotFoundException: umpaz.farmersrespite.common.block.CoffeeDoubleStemBlock)
[018月2025 17:54:37.097] [main/WARN] [mixin/]: @Mixin target umpaz.farmersrespite.common.block.CoffeeDoubleStemBlock was not found create_central_kitchen.mixins.json:common.farmersrespite.CoffeeDoubleStemBlockMixin
[018月2025 17:54:37.100] [main/WARN] [mixin/]: Error loading class: umpaz/farmersrespite/common/block/CoffeeMiddleStemBlock (java.lang.ClassNotFoundException: umpaz.farmersrespite.common.block.CoffeeMiddleStemBlock)
[018月2025 17:54:37.100] [main/WARN] [mixin/]: @Mixin target umpaz.farmersrespite.common.block.CoffeeMiddleStemBlock was not found create_central_kitchen.mixins.json:common.farmersrespite.CoffeeMiddleStemBlockMixin
[018月2025 17:54:37.102] [main/WARN] [mixin/]: Error loading class: umpaz/farmersrespite/common/block/CoffeeStemBlock (java.lang.ClassNotFoundException: umpaz.farmersrespite.common.block.CoffeeStemBlock)
[018月2025 17:54:37.102] [main/WARN] [mixin/]: @Mixin target umpaz.farmersrespite.common.block.CoffeeStemBlock was not found create_central_kitchen.mixins.json:common.farmersrespite.CoffeeStemBlockMixin
[018月2025 17:54:37.104] [main/WARN] [mixin/]: Error loading class: umpaz/farmersrespite/common/block/entity/KettleBlockEntity (java.lang.ClassNotFoundException: umpaz.farmersrespite.common.block.entity.KettleBlockEntity)
[018月2025 17:54:37.104] [main/WARN] [mixin/]: @Mixin target umpaz.farmersrespite.common.block.entity.KettleBlockEntity was not found create_central_kitchen.mixins.json:common.farmersrespite.KettleBlockEntityMixin
[018月2025 17:54:37.106] [main/WARN] [mixin/]: Error loading class: umpaz/farmersrespite/common/block/SmallTeaBushBlock (java.lang.ClassNotFoundException: umpaz.farmersrespite.common.block.SmallTeaBushBlock)
[018月2025 17:54:37.106] [main/WARN] [mixin/]: @Mixin target umpaz.farmersrespite.common.block.SmallTeaBushBlock was not found create_central_kitchen.mixins.json:common.farmersrespite.SmallTeaBushBlockMixin
[018月2025 17:54:37.107] [main/WARN] [mixin/]: Error loading class: umpaz/farmersrespite/common/block/TeaBushBlock (java.lang.ClassNotFoundException: umpaz.farmersrespite.common.block.TeaBushBlock)
[018月2025 17:54:37.108] [main/WARN] [mixin/]: @Mixin target umpaz.farmersrespite.common.block.TeaBushBlock was not found create_central_kitchen.mixins.json:common.farmersrespite.TeaBushBlockMixin
[018月2025 17:54:37.109] [main/WARN] [mixin/]: Error loading class: com/sammy/minersdelight/content/block/copper_pot/CopperPotBlockEntity (java.lang.ClassNotFoundException: com.sammy.minersdelight.content.block.copper_pot.CopperPotBlockEntity)
[018月2025 17:54:37.109] [main/WARN] [mixin/]: @Mixin target com.sammy.minersdelight.content.block.copper_pot.CopperPotBlockEntity was not found create_central_kitchen.mixins.json:common.minersdelight.CopperPotBlockEntityMixin
[018月2025 17:54:37.111] [main/WARN] [mixin/]: Error loading class: com/sammy/minersdelight/content/block/sticky_basket/StickyBasketBlockEntity (java.lang.ClassNotFoundException: com.sammy.minersdelight.content.block.sticky_basket.StickyBasketBlockEntity)
[018月2025 17:54:37.111] [main/WARN] [mixin/]: @Mixin target com.sammy.minersdelight.content.block.sticky_basket.StickyBasketBlockEntity was not found create_central_kitchen.mixins.json:common.minersdelight.StickyBasketBlockEntityAccessor
[018月2025 17:54:37.113] [main/WARN] [mixin/]: Error loading class: com/sammy/minersdelight/content/block/sticky_basket/StickyBasketBlockEntity (java.lang.ClassNotFoundException: com.sammy.minersdelight.content.block.sticky_basket.StickyBasketBlockEntity)
[018月2025 17:54:37.113] [main/WARN] [mixin/]: @Mixin target com.sammy.minersdelight.content.block.sticky_basket.StickyBasketBlockEntity was not found create_central_kitchen.mixins.json:common.minersdelight.StickyBasketBlockEntityMixin
[018月2025 17:54:37.115] [main/WARN] [mixin/]: Error loading class: com/teamabnormals/neapolitan/common/item/DrinkItem (java.lang.ClassNotFoundException: com.teamabnormals.neapolitan.common.item.DrinkItem)
[018月2025 17:54:37.115] [main/WARN] [mixin/]: @Mixin target com.teamabnormals.neapolitan.common.item.DrinkItem was not found create_central_kitchen.mixins.json:common.neapolitan.DrinkItemMixin
[018月2025 17:54:37.117] [main/WARN] [mixin/]: Error loading class: net/orcinus/overweightfarming/blocks/CropFullBlock (java.lang.ClassNotFoundException: net.orcinus.overweightfarming.blocks.CropFullBlock)
[018月2025 17:54:37.117] [main/WARN] [mixin/]: @Mixin target net.orcinus.overweightfarming.blocks.CropFullBlock was not found create_central_kitchen.mixins.json:common.overweightfarming.CropFullBlockMixin
[018月2025 17:54:37.177] [main/WARN] [Embeddium/]: Force-disabling mixin 'features.render.world.sky.WorldRendererMixin' as rule 'mixin.features.render.world.sky' (added by mods [oculus]) disables it and children
[018月2025 17:54:37.177] [main/WARN] [Embeddium/]: Force-disabling mixin 'features.render.world.sky.ClientWorldMixin' as rule 'mixin.features.render.world.sky' (added by mods [oculus]) disables it and children
[018月2025 17:54:37.180] [main/WARN] [Embeddium/]: Force-disabling mixin 'features.render.world.sky.BackgroundRendererMixin' as rule 'mixin.features.render.world.sky' (added by mods [oculus]) disables it and children
[018月2025 17:54:37.183] [main/WARN] [Embeddium/]: Force-disabling mixin 'features.render.gui.font.GlyphRendererMixin' as rule 'mixin.features.render.gui.font' (added by mods [oculus]) disables it and children
[018月2025 17:54:37.183] [main/WARN] [Embeddium/]: Force-disabling mixin 'features.render.gui.font.FontSetMixin' as rule 'mixin.features.render.gui.font' (added by mods [oculus]) disables it and children
[018月2025 17:54:37.183] [main/WARN] [Embeddium/]: Force-disabling mixin 'features.render.entity.shadows.EntityRenderDispatcherMixin' as rule 'mixin.features.render.entity' (added by mods [oculus]) disables it and children
[018月2025 17:54:37.184] [main/WARN] [Embeddium/]: Force-disabling mixin 'features.render.entity.remove_streams.ModelPartMixin' as rule 'mixin.features.render.entity' (added by mods [oculus]) disables it and children
[018月2025 17:54:37.184] [main/WARN] [Embeddium/]: Force-disabling mixin 'features.render.entity.remove_streams.HierarchicalModelMixin' as rule 'mixin.features.render.entity' (added by mods [oculus]) disables it and children
[018月2025 17:54:37.184] [main/WARN] [Embeddium/]: Force-disabling mixin 'features.render.entity.fast_render.ModelPartMixin' as rule 'mixin.features.render.entity' (added by mods [oculus]) disables it and children
[018月2025 17:54:37.184] [main/WARN] [Embeddium/]: Force-disabling mixin 'features.render.entity.fast_render.CuboidMixin' as rule 'mixin.features.render.entity' (added by mods [oculus]) disables it and children
[018月2025 17:54:37.184] [main/WARN] [Embeddium/]: Force-disabling mixin 'features.render.entity.cull.EntityRendererMixin' as rule 'mixin.features.render.entity' (added by mods [oculus]) disables it and children
[018月2025 17:54:37.314] [main/INFO] [MixinExtras|Service/]: Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[018月2025 17:54:37.597] [main/INFO] [mixin/]: Mixing client.MixinMinecraft from mixins/common/nochatreports.mixins.json into net.minecraft.client.Minecraft
[018月2025 17:54:38.109] [main/WARN] [mixin/]: Static binding violation: PRIVATE @Overwrite method m_216202_ in modernfix-forge.mixins.json:perf.tag_id_caching.TagOrElementLocationMixin cannot reduce visibiliy of PUBLIC target method, visibility will be upgraded.
[018月2025 17:54:39.453] [pool-4-thread-1/INFO] [net.minecraft.server.Bootstrap/]: ModernFix reached bootstrap stage (20.65 s after launch)
[018月2025 17:54:39.512] [pool-4-thread-1/WARN] [mixin/]: @Final field delegatesByName:Ljava/util/Map; in modernfix-forge.mixins.json:perf.forge_registry_alloc.ForgeRegistryMixin should be final
[018月2025 17:54:39.513] [pool-4-thread-1/WARN] [mixin/]: @Final field delegatesByValue:Ljava/util/Map; in modernfix-forge.mixins.json:perf.forge_registry_alloc.ForgeRegistryMixin should be final
[018月2025 17:54:40.583] [pool-4-thread-1/WARN] [mixin/]: Method overwrite conflict for m_6104_ in embeddium.mixins.json:features.options.render_layers.LeavesBlockMixin, previously written by me.srrapero720.chloride.mixins.impl.leaves_culling.LeavesBlockMixin. Skipping method.
[018月2025 17:54:40.911] [pool-4-thread-1/INFO] [net.minecraft.server.Bootstrap/]: Vanilla bootstrap took 1455 milliseconds
[018月2025 17:54:40.958] [pool-4-thread-1/INFO] [mixin/]: Mixing common.MixinFriendlyByteBuf from mixins/common/nochatreports.mixins.json into net.minecraft.network.FriendlyByteBuf
[018月2025 17:54:40.959] [pool-4-thread-1/INFO] [mixin/]: Renaming synthetic method lambda$onWriteJsonWithCodec$1(Ljava/lang/Object;Ljava/lang/String;)Lio/netty/handler/codec/EncoderException; to mdac68fa$lambda$onWriteJsonWithCodec$1$0 in mixins/common/nochatreports.mixins.json:common.MixinFriendlyByteBuf
[018月2025 17:54:40.960] [pool-4-thread-1/INFO] [mixin/]: Renaming synthetic method lambda$onReadJsonWithCodec$0(Ljava/lang/String;)Lio/netty/handler/codec/DecoderException; to mdac68fa$lambda$onReadJsonWithCodec$0$1 in mixins/common/nochatreports.mixins.json:common.MixinFriendlyByteBuf
[018月2025 17:54:42.662] [pool-4-thread-1/INFO] [mixin/]: Mixing server.MixinServerGamePacketListenerImpl from mixins/common/nochatreports.mixins.json into net.minecraft.server.network.ServerGamePacketListenerImpl
[018月2025 17:54:43.795] [Render thread/INFO] [mixin/]: Mixing client.MixinTitleScreen from mixins/common/nochatreports.mixins.json into net.minecraft.client.gui.screens.TitleScreen
[018月2025 17:54:43.801] [Render thread/INFO] [mixin/]: Mixing client.MixinChatScreen from mixins/common/nochatreports.mixins.json into net.minecraft.client.gui.screens.ChatScreen
[018月2025 17:54:43.801] [Render thread/INFO] [mixin/]: Renaming synthetic method lambda$onInit$4()Lnet/minecraft/network/chat/Component; to mdac68fa$lambda$onInit$4$0 in mixins/common/nochatreports.mixins.json:client.MixinChatScreen
[018月2025 17:54:43.801] [Render thread/INFO] [mixin/]: Renaming synthetic method lambda$onInit$3(Lnet/minecraft/client/gui/components/Button;)V to mdac68fa$lambda$onInit$3$1 in mixins/common/nochatreports.mixins.json:client.MixinChatScreen
[018月2025 17:54:43.801] [Render thread/INFO] [mixin/]: Renaming synthetic method lambda$onInit$2()Lnet/minecraft/network/chat/Component; to mdac68fa$lambda$onInit$2$2 in mixins/common/nochatreports.mixins.json:client.MixinChatScreen
[018月2025 17:54:43.801] [Render thread/INFO] [mixin/]: Renaming synthetic method lambda$onInit$1(Lnet/minecraft/client/gui/components/Button;)V to mdac68fa$lambda$onInit$1$3 in mixins/common/nochatreports.mixins.json:client.MixinChatScreen
[018月2025 17:54:43.802] [Render thread/INFO] [mixin/]: Renaming synthetic method lambda$onBeforeMessage$0(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lcom/aizistral/nochatreports/common/encryption/Encryptor;)V to mdac68fa$lambda$onBeforeMessage$0$4 in mixins/common/nochatreports.mixins.json:client.MixinChatScreen
[018月2025 17:54:43.922] [Render thread/WARN] [net.minecraft.server.packs.VanillaPackResourcesBuilder/]: Assets URL 'union:/C:/Users/<USER>/Desktop/.minecraft/libraries/net/minecraft/client/1.20.1-********.114412/client-1.20.1-********.114412-srg.jar%23306!/assets/.mcassetsroot' uses unexpected schema
[018月2025 17:54:43.922] [Render thread/WARN] [net.minecraft.server.packs.VanillaPackResourcesBuilder/]: Assets URL 'union:/C:/Users/<USER>/Desktop/.minecraft/libraries/net/minecraft/client/1.20.1-********.114412/client-1.20.1-********.114412-srg.jar%23306!/data/.mcassetsroot' uses unexpected schema
[018月2025 17:54:43.968] [Render thread/INFO] [com.mojang.authlib.yggdrasil.YggdrasilAuthenticationService/]: Environment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[018月2025 17:54:44.677] [Render thread/INFO] [net.minecraft.client.Minecraft/]: Setting user: Quasar2323
[018月2025 17:54:44.730] [Render thread/INFO] [ModernFix/]: Bypassed Mojang DFU
[018月2025 17:54:44.731] [Render thread/INFO] [mixin/]: Mixing client.MixinToastComponent from mixins/common/nochatreports.mixins.json into net.minecraft.client.gui.components.toasts.ToastComponent
[018月2025 17:54:44.733] [Render thread/INFO] [mixin/]: Mixing client.MixinOptions from mixins/common/nochatreports.mixins.json into net.minecraft.client.Options
[018月2025 17:54:44.733] [Render thread/INFO] [mixin/]: Renaming synthetic method lambda$onlyShowSecureChat$0(Ljava/lang/Boolean;)V to mdac68fa$lambda$onlyShowSecureChat$0$0 in mixins/common/nochatreports.mixins.json:client.MixinOptions
[018月2025 17:54:44.774] [Render thread/INFO] [mixin/]: Mixing client.MixinChatComponent from mixins/common/nochatreports.mixins.json into net.minecraft.client.gui.components.ChatComponent
[018月2025 17:54:44.774] [Render thread/INFO] [mixin/]: Renaming synthetic method lambda$modifyGUIMessage$1()V to mdac68fa$lambda$modifyGUIMessage$1$0 in mixins/common/nochatreports.mixins.json:client.MixinChatComponent
[018月2025 17:54:44.774] [Render thread/INFO] [mixin/]: Renaming synthetic method lambda$modifyGUIMessage$0(Lnet/minecraft/network/chat/FormattedText;Lnet/minecraft/network/chat/Component;)V to mdac68fa$lambda$modifyGUIMessage$0$1 in mixins/common/nochatreports.mixins.json:client.MixinChatComponent
[018月2025 17:54:44.786] [Render thread/INFO] [mixin/]: Mixing client.MixinGuiMessageTagIcon from mixins/common/nochatreports.mixins.json into net.minecraft.client.GuiMessageTag$Icon
[018月2025 17:54:44.786] [Render thread/INFO] [mixin/]: Renaming @Invoker method create(Ljava/lang/String;IIIII)Lnet/minecraft/client/GuiMessageTag$Icon; to create_$md$ac68fa$0 in mixins/common/nochatreports.mixins.json:client.MixinGuiMessageTagIcon
[018月2025 17:54:44.852] [Render thread/INFO] [net.minecraft.client.Minecraft/]: Backend library: LWJGL version 3.3.1 build 7
[018月2025 17:54:44.895] [Render thread/INFO] [KubeJS/]: Loaded client.properties
[018月2025 17:54:44.897] [Render thread/WARN] [Embeddium-NvidiaWorkarounds/]: Applying workaround: Prevent the NVIDIA OpenGL driver from using broken optimizations (NVIDIA_THREADED_OPTIMIZATIONS)
[018月2025 17:54:44.904] [Render thread/ERROR] [Embeddium-MixinTaintDetector/]: Mod mixin into Embeddium internals detected. This instance is now tainted. The Embeddium team does not provide any guarantee of support for issues encountered while such mods are installed.
[018月2025 17:54:44.904] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.gui.SodiumGameOptions, which may cause instability.
[018月2025 17:54:44.934] [Render thread/INFO] [Embeddium-PostlaunchChecks/]: OpenGL Vendor: NVIDIA Corporation
[018月2025 17:54:44.934] [Render thread/INFO] [Embeddium-PostlaunchChecks/]: OpenGL Renderer: NVIDIA GeForce RTX 4060 Laptop GPU/PCIe/SSE2
[018月2025 17:54:44.935] [Render thread/INFO] [Embeddium-PostlaunchChecks/]: OpenGL Version: 4.6.0 NVIDIA 572.70
[018月2025 17:54:44.935] [Render thread/WARN] [Embeddium-PostlaunchChecks/]: Enabling secondary workaround for NVIDIA threaded optimizations
[018月2025 17:54:45.295] [Render thread/INFO] [Oculus/]: Debug functionality is disabled.
[018月2025 17:54:45.297] [Render thread/INFO] [Oculus/]: OpenGL 4.5 detected, enabling DSA.
[018月2025 17:54:45.332] [Render thread/INFO] [Oculus/]: Shaders are disabled because no valid shaderpack is selected
[018月2025 17:54:45.332] [Render thread/INFO] [Oculus/]: Shaders are disabled
[018月2025 17:54:45.798] [modloading-worker-0/INFO] [Easy NPC/]: Initializing Easy NPC (Forge) ...
[018月2025 17:54:45.799] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Debug Manager ...
[018月2025 17:54:45.803] [modloading-worker-0/WARN] [Easy NPC/]: [Debug Manager] Detected debug log level for Easy NPC with ALL!
[018月2025 17:54:45.804] [modloading-worker-0/WARN] [Easy NPC/]: [Debug Manager] Adjusting log level for Easy NPC from ALL to INFO, for performance reasons!
[018月2025 17:54:45.804] [modloading-worker-0/INFO] [Easy NPC/]: [Debug Manager] Add new logger config for Easy NPC with level INFO ...
[018月2025 17:54:45.805] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Constants ...
[018月2025 17:54:45.815] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Configuration ...
[018月2025 17:54:45.820] [modloading-worker-0/INFO] [Easy NPC/]: [Config] Updated configuration path to C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc
[018月2025 17:54:45.821] [modloading-worker-0/INFO] [Easy NPC/]: [Config] Registering common configuration ...
[018月2025 17:54:45.829] [modloading-worker-0/INFO] [Easy NPC/]: [Config] Render Entity Type Support Configuration

 Please note that this configuration file only includes confirmed entity types.
 If an entity type is not listed here, it doesn't mean it's automatically supported or unsupported!
 is up to date: {minecraft:giant=true, dannys_expansion:mundane_slime=false, botania:pool_minecart=false, botania:ender_air=false, minecraft:end_crystal=false, simple_mobs:rumble=false, minecraft:experience_bottle=false, minecraft:glow_squid=true, minecraft:trader_llama=true, orcz:decaystrikecustom=false, simple_mobs:staff_usage=false, minecraft:horse=true, the_bumblezone:purple_spike_entity=false, minecraft:llama=true, minecraft:husk=true, minecraft:cave_spider=true, minecraft:enderman=true, minecraft:dolphin=true, frostiful:freezing_wind=false, minecraft:shulker_bullet=false, simple_mobs:projecttest=false, minecraft:donkey=true, minecraft:area_effect_cloud=false, minecraft:ghast=false, frostiful:packed_snowball=false, twigs:pebble=false, handcrafted:fancy_painting=false, moretotems:summoned_zombie=false, minecraft:piglin_brute=true, minecraft:creeper=true, minecraft:glow_item_frame=false, minecraft:bee=true, botania:mana_storm=false, minecraft:illusioner=true, minecraft:player=false, minecraft:wither_skull=false, minecraft:mooshroom=true, smallships:galley=false, create:stationary_contraption=false, minecraft:eye_of_ender=false, minecraft:villager=true, ad_astra:tier_1_rocket=false, botania:magic_landmine=false, minecraft:stray=true, smallships:cog=false, minecraft:falling_block=false, minecraft:drowned=true, minecraft:axolotl=true, botania:doppleganger=false, botania:flame_ring=false, botania:ender_air_bottle=false, minecraft:tnt=false, ae2:tiny_tnt_primed=false, botania:magic_missile=false, create:crafting_blueprint=false, create:contraption=false, minecraft:strider=true, smallships:brigg=false, minecraft:guardian=true, minecraft:parrot=true, minecraft:arrow=false, lootr:lootr_minecart=false, the_bumblezone:pollen_puff=false, minecraft:chest_minecart=false, minecraft:wither=true, minecraft:ender_pearl=false, create:super_glue=false, minecraft:wandering_trader=true, handcrafted:seat=false, simple_mobs:dragon_smoke=false, minecraft:wither_skeleton=true, botania:thorn_chakram=false, minecraft:iron_golem=true, botania:pixie=false, dummmmmmy:target_dummy=false, minecraft:elder_guardian=true, graveyard:skull=false, minecraft:mule=true, the_bumblezone:bee_stinger=false, the_bumblezone:thrown_stinger_spear=false, minecraft:armor_stand=false, aquamirae:pillagers_patrol=false, minecraft:vindicator=true, botania:mana_burst=false, minecraft:evoker=true, minecraft:slime=false, minecraft:leash_knot=false, botania:babylon_weapon=false, minecraft:pufferfish=true, terraform:boat=false, swampier_swamps:swamp_gas=false, minecraft:experience_orb=false, ad_astra:tier_3_rocket=false, minecraft:fox=true, botania:thrown_item=false, the_bumblezone:cosmic_crystal_entity=false, graveyard:ghouling=false, botania:player_mover=false, minecraft:silverfish=true, minecraft:endermite=true, minecraft:firework_rocket=false, minecraft:chicken=true, minecraft:zombie=true, the_bumblezone:sentry_watcher=false, minecraft:spawner_minecart=false, botania:spark=false, botania:falling_star=false, minecraft:hoglin=true, minecraft:boat=false, minecraft:trident=false, friendsandfoes:player_illusion=false, minecraft:polar_bear=true, minecraft:phantom=false, ad_astra:tier_4_rocket=false, minecraft:zombified_piglin=true, minecraft:witch=true, ad_astra:tier_2_rocket=false, minecraft:pig=true, minecraft:turtle=true, moretotems:summoned_bee=false, minecraft:sheep=true, minecraft:ender_dragon=false, minecraft:goat=true, minecraft:item_frame=false, create:carriage_contraption=false, minecraft:vex=true, minecraft:minecart=false, minecraft:item=false, minecraft:dragon_fireball=false, minecraft:cod=true, minecraft:skeleton=true, ad_astra:space_painting=false, create:seat=false, simple_mobs:staff_interact=false, minecraft:ravager=true, minecraft:squid=true, minecraft:skeleton_horse=true, minecraft:cow=true, minecraft:potion=false, ad_astra:lander=false, minecraft:salmon=true, minecraft:snow_golem=true, minecraft:shulker=true, friendsandfoes:ice_chunk=false, minecraft:hopper_minecart=false, the_bumblezone:honey_crystal_shard=false, simple_mobs:lightning_spear=false, minecraft:rabbit=true, minecraft:painting=false, minecraft:pillager=true, the_bumblezone:electric_ring_entity=false, minecraft:zoglin=true, minecraft:lightning_bolt=false, frostiful:frost_spell=false, minecraft:fishing_bobber=false, minecraft:tnt_minecart=false, simple_mobs:ground_spike=false, minecraft:command_block_minecart=false, minecraft:bat=false, the_bumblezone:dirt_pellet=false, minecraft:fireball=false, minecraft:llama_spit=false, minecraft:furnace_minecart=false, minecraft:evoker_fangs=false, minecraft:piglin=true, minecraft:snowball=false, minecraft:spectral_arrow=false, orcz:wither_strikeringcustom=false, simple_mobs:elemental_chain=false, minecraft:blaze=true, ad_astra:ice_spit=false, techreborn:nuke=false, minecraft:egg=false, minecraft:zombie_horse=true, minecraft:magma_cube=false, minecraft:small_fireball=false, frostiful:thrown_icicle=false, minecraft:spider=true, ad_astra:tier_1_rover=false, minecraft:marker=false, majruszsdifficulty:cursed_armor=false, minecraft:text_display=false, farmersdelight:rotten_tomato=false, minecraft:cat=true, armourers_workshop:mannequin=false, create:gantry_contraption=false, minecraft:tropical_fish=true, botania:corporea_spark=false, minecraft:ocelot=true, minecraft:wolf=true, minecraft:panda=true, minecraft:zombie_villager=true}
[018月2025 17:54:45.829] [modloading-worker-0/INFO] [Easy NPC/]: [Config] Registering client configuration ...
[018月2025 17:54:45.829] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Common Data Files ...
[018月2025 17:54:45.833] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Common data folders ...
[018月2025 17:54:45.834] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Entity Data Serializers ...
[018月2025 17:54:45.835] [modloading-worker-0/INFO] [mixin/]: Mixing server.MixinPlayerList from mixins/common/nochatreports.mixins.json into net.minecraft.server.players.PlayerList
[018月2025 17:54:45.935] [modloading-worker-0/INFO] [mixin/]: Mixing client.MixinClientConnection from mixins/forge/nochatreports-forge.mixins.json into net.minecraft.network.Connection
[018月2025 17:54:45.975] [modloading-worker-0/INFO] [net.minecraftforge.common.ForgeMod/FORGEMOD]: Forge mod loading, version 47.4.0, for MC 1.20.1 with MCP ********.114412
[018月2025 17:54:45.976] [modloading-worker-0/INFO] [net.minecraftforge.common.MinecraftForge/FORGE]: MinecraftForge v47.4.0 Initialized
[018月2025 17:54:45.985] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id architectury:sync_ids
[018月2025 17:54:45.986] [modloading-worker-0/INFO] [chloride/Main]: Chloride is here, lets make your experience taste-able
[018月2025 17:54:45.991] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id architectury:sync_ids
[018月2025 17:54:45.991] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbessentials:update_tab_name
[018月2025 17:54:46.028] [modloading-worker-0/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [ruokmod, oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.gui.SodiumOptionsGUI, which may cause instability.
[018月2025 17:54:46.066] [modloading-worker-0/WARN] [mixin/]: Static binding violation: PRIVATE @Overwrite method m_109501_ in embeddium.mixins.json:core.render.world.WorldRendererMixin cannot reduce visibiliy of PUBLIC target method, visibility will be upgraded.
[018月2025 17:54:46.187] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer ActionEventSet with id 28
[018月2025 17:54:46.187] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer CustomAttributes with id 29
[018月2025 17:54:46.187] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer DialogDataSet with id 30
[018月2025 17:54:46.187] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer DisplayAttributeSet with id 31
[018月2025 17:54:46.187] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer EntityAttributes with id 32
[018月2025 17:54:46.187] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer MerchantOffers with id 33
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer ModelPose with id 34
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer ObjectiveDataSet with id 35
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer CustomPosition with id 36
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer Profession with id 37
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer RenderDataSet with id 38
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer CustomRotation with id 39
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer CustomScale with id 40
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer SkinDataEntry with id 41
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer SoundDataSet with id 42
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer HashSet:UUID with id 43
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer HashSet:String with id 44
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer TradingDataSet with id 45
[018月2025 17:54:46.188] [modloading-worker-0/INFO] [Easy NPC/]: Registered entity data serializer UUID with id 46
[018月2025 17:54:46.189] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Compatibility Handler ...
[018月2025 17:54:46.203] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Compat Handler ...
[018月2025 17:54:46.206] [modloading-worker-0/INFO] [Easy NPC/]: [Compat Manager] Epic Fight mod with id 'epicfight' loaded: false
[018月2025 17:54:46.206] [modloading-worker-0/INFO] [Easy NPC/]: [Compat Manager] Cobblemon mod with id 'cobblemon' loaded: false
[018月2025 17:54:46.206] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Command Argument Types ...
[018月2025 17:54:46.214] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Entity Types ...
[018月2025 17:54:46.228] [modloading-worker-0/WARN] [mixin/]: Method overwrite conflict for sodium$createViewport in embeddium.mixins.json:core.render.frustum.FrustumMixin, previously written by com.moepus.flerovium.mixins.Chunk.FrustumMixin. Skipping method.
[018月2025 17:54:46.243] [modloading-worker-0/INFO] [STDOUT/]: [yalter.mousetweaks.Logger:Log:6]: [Mouse Tweaks] Main.initialize()
[018月2025 17:54:46.252] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Blocks ...
[018月2025 17:54:46.258] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Blocks Entity Types ...
[018月2025 17:54:46.258] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Items ...
[018月2025 17:54:46.261] [modloading-worker-0/INFO] [STDOUT/]: [yalter.mousetweaks.Logger:Log:6]: [Mouse Tweaks] Initialized.
[018月2025 17:54:46.263] [modloading-worker-0/INFO] [gml/]: Initialised GML mod. Version: 4.0.10
[018月2025 17:54:46.283] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Menu Types ...
[018月2025 17:54:46.302] [modloading-worker-0/INFO] [NoChatReports/]: KONNICHIWA ZA WARUDO!
[018月2025 17:54:46.303] [modloading-worker-0/INFO] [NoChatReports/]: Default JVM text encoding is: UTF-8
[018月2025 17:54:46.326] [modloading-worker-0/INFO] [NoChatReports/]: Reading config file NoChatReports/NCR-Common.json...
[018月2025 17:54:46.345] [modloading-worker-0/INFO] [NoChatReports/]: Reading config file NoChatReports/NCR-Client.json...
[018月2025 17:54:46.353] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Menu Handler ...
[018月2025 17:54:46.364] [modloading-worker-0/INFO] [NoChatReports/]: Reading config file NoChatReports/NCR-ServerPreferences.json...
[018月2025 17:54:46.378] [modloading-worker-0/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.gui.options.OptionImpl, which may cause instability.
[018月2025 17:54:46.381] [modloading-worker-0/INFO] [NoChatReports/]: Reading config file NoChatReports/NCR-Encryption.json...
[018月2025 17:54:46.383] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Network Handler ...
[018月2025 17:54:46.389] [modloading-worker-0/INFO] [Easy NPC/]: Registering server network handler for Server -> Client messages.
[018月2025 17:54:46.392] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Client Network Message Handler ...
[018月2025 17:54:46.400] [modloading-worker-0/INFO] [Easy NPC/]: Initializing Easy NPC (Forge-Client) ...
[018月2025 17:54:46.443] [modloading-worker-0/INFO] [Easy NPC/]: Registering client network handler for Client -> Server messages.
[018月2025 17:54:46.443] [modloading-worker-0/INFO] [Easy NPC/]: 🗣 Register Easy NPC Server Network Message Handler ...
[018月2025 17:54:46.453] [modloading-worker-0/INFO] [net.liopyu.entityjs.EntityJSMod/]: Loading EntityJS-Liopyu
[018月2025 17:54:46.518] [modloading-worker-0/INFO] [KubeJS/]: Loaded common.properties
[018月2025 17:54:46.520] [modloading-worker-0/INFO] [KubeJS/]: Loaded dev.properties
[018月2025 17:54:46.710] [modloading-worker-0/INFO] [NoChatReports/]: Writing config file NoChatReports/NCR-Common.json...
[018月2025 17:54:46.713] [modloading-worker-0/INFO] [NoChatReports/]: Writing config file NoChatReports/NCR-Client.json...
[018月2025 17:54:46.716] [modloading-worker-0/INFO] [NoChatReports/]: Writing config file NoChatReports/NCR-ServerPreferences.json...
[018月2025 17:54:46.719] [modloading-worker-0/INFO] [NoChatReports/]: Writing config file NoChatReports/NCR-Encryption.json...
[018月2025 17:54:46.725] [modloading-worker-0/INFO] [NoChatReports/]: Client initialization...
[018月2025 17:54:46.742] [modloading-worker-0/INFO] [mixin/]: Mixing client.MixinClientPacketListener from mixins/common/nochatreports.mixins.json into net.minecraft.client.multiplayer.ClientPacketListener
[018月2025 17:54:46.744] [modloading-worker-0/INFO] [mixin/]: Mixing client.MixinClientPacketListener from mixins/forge/nochatreports-forge.mixins.json into net.minecraft.client.multiplayer.ClientPacketListener
[018月2025 17:54:46.774] [modloading-worker-0/INFO] [com.simibubi.create.Create/]: Create 6.0.6 initializing! Commit hash: 338bfa0aec952fa51656e8f61bd621ca9b3b2e00
[018月2025 17:54:46.896] [modloading-worker-0/INFO] [irisflw/]: IrisFLW backends initialized: dev.engine_room.flywheel.lib.backend.SimpleBackend@4935a391
[018月2025 17:54:46.998] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbultimine:send_shape
[018月2025 17:54:47.009] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbultimine:key_pressed
[018月2025 17:54:47.015] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbultimine:mode_changed
[018月2025 17:54:47.034] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbultimine:sync_config_from_server
[018月2025 17:54:47.040] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbultimine:sync_config_to_server
[018月2025 17:54:47.047] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbultimine:edit_config
[018月2025 17:54:47.056] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbultimine:sync_ultimine_time
[018月2025 17:54:47.080] [modloading-worker-0/INFO] [mezz.jei.library.load.PluginCaller/]: Sending ConfigManager...
[018月2025 17:54:47.099] [modloading-worker-0/INFO] [mezz.jei.library.load.PluginCaller/]: Sending ConfigManager took 15.93 ms
[018月2025 17:54:47.244] [modloading-worker-0/INFO] [KubeJS/]: Looking for KubeJS plugins...
[018月2025 17:54:47.246] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftblibrary:edit_nbt
[018月2025 17:54:47.256] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftblibrary:edit_nbt_response
[018月2025 17:54:47.265] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftblibrary:sync_known_server_registries
[018月2025 17:54:47.267] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source kubejs
[018月2025 17:54:47.281] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftblibrary:edit_config
[018月2025 17:54:47.304] [modloading-worker-0/WARN] [KubeJS/]: Plugin dev.latvian.mods.kubejs.integration.forge.gamestages.GameStagesIntegration does not have required mod gamestages loaded, skipping
[018月2025 17:54:47.306] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source ftbxmodcompat
[018月2025 17:54:47.327] [modloading-worker-0/WARN] [KubeJS/]: Plugin dev.ftb.mods.ftbxmodcompat.ftbfiltersystem.kubejs.FFSKubeJSPlugin does not have required mod ftbfiltersystem loaded, skipping
[018月2025 17:54:47.328] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source createsifter
[018月2025 17:54:47.374] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:request_map_data
[018月2025 17:54:47.384] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:share_waypoint
[018月2025 17:54:47.390] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:send_all_chunks
[018月2025 17:54:47.396] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:login_data
[018月2025 17:54:47.405] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:request_chunk_change
[018月2025 17:54:47.411] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:send_chunk
[018月2025 17:54:47.417] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbteams:sync_teams
[018月2025 17:54:47.420] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:send_general_data
[018月2025 17:54:47.423] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbteams:sync_message_history
[018月2025 17:54:47.427] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:teleport_from_map
[018月2025 17:54:47.430] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:open_gui
[018月2025 17:54:47.434] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:player_death
[018月2025 17:54:47.438] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbteams:open_my_team_gui
[018月2025 17:54:47.444] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:send_visible_player_list
[018月2025 17:54:47.447] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:update_settings
[018月2025 17:54:47.452] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:sync_tx
[018月2025 17:54:47.454] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbteams:update_settings_response
[018月2025 17:54:47.458] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:sync_rx
[018月2025 17:54:47.461] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:send_message
[018月2025 17:54:47.465] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:loaded_chunk_view
[018月2025 17:54:47.470] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbteams:send_message_response
[018月2025 17:54:47.473] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:send_player_position
[018月2025 17:54:47.478] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbteams:update_presence
[018月2025 17:54:47.483] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:update_force_load_expiry
[018月2025 17:54:47.488] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:create_party
[018月2025 17:54:47.490] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbchunks:server_config_request
[018月2025 17:54:47.495] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:server_config_response
[018月2025 17:54:47.495] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbteams:player_gui_operation
[018月2025 17:54:47.501] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:chunk_change_response
[018月2025 17:54:47.506] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:request_block_color
[018月2025 17:54:47.514] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbchunks:add_waypoint
[018月2025 17:54:47.565] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source entityjs
[018月2025 17:54:47.569] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source kubejs_create
[018月2025 17:54:47.691] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id itemfilters:main/14e7fa1454283aec8ae811ef844ada28
[018月2025 17:54:47.691] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id itemfilters:main/14e7fa1454283aec8ae811ef844ada28
[018月2025 17:54:47.699] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id itemfilters:main/8f6a899247753217b9d86ab427a2b279
[018月2025 17:54:47.699] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id itemfilters:main/8f6a899247753217b9d86ab427a2b279
[018月2025 17:54:47.813] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id polylib:container_to_client
[018月2025 17:54:47.814] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id polylib:tile_to_client
[018月2025 17:54:47.815] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id polylib:container_packet_server
[018月2025 17:54:47.816] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id polylib:tile_data_server
[018月2025 17:54:47.818] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id polylib:tile_packet_server
[018月2025 17:54:47.843] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source probejs
[018月2025 17:54:47.846] [modloading-worker-0/INFO] [KubeJS/]: Found plugin source kaleidoscope_cookery
[018月2025 17:54:47.850] [modloading-worker-0/INFO] [KubeJS/]: Done in 604.6 ms
[018月2025 17:54:47.897] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:sync_quests
[018月2025 17:54:47.903] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:sync_team_data
[018月2025 17:54:47.908] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:update_task_progress
[018月2025 17:54:47.915] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:submit_task
[018月2025 17:54:47.924] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:claim_reward
[018月2025 17:54:47.931] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:claim_reward_response
[018月2025 17:54:47.938] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:sync_editing_mode
[018月2025 17:54:47.943] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:get_emergency_items
[018月2025 17:54:47.948] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:create_other_team_data
[018月2025 17:54:47.954] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:claim_all_rewards
[018月2025 17:54:47.968] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:claim_choice_reward
[018月2025 17:54:47.975] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:display_completion_toast
[018月2025 17:54:47.981] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:display_reward_toast
[018月2025 17:54:47.987] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:display_item_reward_toast
[018月2025 17:54:47.993] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:toggle_pinned
[018月2025 17:54:47.999] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:toggle_pinned_response
[018月2025 17:54:48.004] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:toggle_chapter_pinned
[018月2025 17:54:48.009] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:toggle_chapter_pinned_response
[018月2025 17:54:48.014] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:toggle_editing_mode
[018月2025 17:54:48.018] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:force_save
[018月2025 17:54:48.024] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:update_team_data
[018月2025 17:54:48.028] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:set_custom_image
[018月2025 17:54:48.034] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:object_started
[018月2025 17:54:48.039] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:object_completed
[018月2025 17:54:48.044] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:object_started_reset
[018月2025 17:54:48.049] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:object_completed_reset
[018月2025 17:54:48.053] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:sync_lock
[018月2025 17:54:48.058] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:reset_reward
[018月2025 17:54:48.062] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:team_data_changed
[018月2025 17:54:48.066] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:task_screen_config_req
[018月2025 17:54:48.069] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:task_screen_config_resp
[018月2025 17:54:48.074] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:change_progress
[018月2025 17:54:48.084] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:create_object
[018月2025 17:54:48.091] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:create_object_response
[018月2025 17:54:48.103] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:create_task_at
[018月2025 17:54:48.108] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:delete_object
[018月2025 17:54:48.112] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:delete_object_response
[018月2025 17:54:48.117] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:edit_object
[018月2025 17:54:48.121] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:edit_object_response
[018月2025 17:54:48.126] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:move_chapter
[018月2025 17:54:48.131] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:move_chapter_response
[018月2025 17:54:48.137] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:move_quest
[018月2025 17:54:48.142] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:move_quest_response
[018月2025 17:54:48.148] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:change_chapter_group
[018月2025 17:54:48.153] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:change_chapter_group_response
[018月2025 17:54:48.160] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:move_chapter_group
[018月2025 17:54:48.168] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:move_chapter_group_response
[018月2025 17:54:48.173] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:sync_reward_blocking
[018月2025 17:54:48.180] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:copy_quest
[018月2025 17:54:48.188] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:copy_chapter_image
[018月2025 17:54:48.194] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:sync_structures_request
[018月2025 17:54:48.199] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:sync_structures_response
[018月2025 17:54:48.205] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id ftbquests:request_team_data
[018月2025 17:54:48.209] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:sync_editor_permission
[018月2025 17:54:48.214] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:open_quest_book
[018月2025 17:54:48.219] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id ftbquests:clear_display_cache
[018月2025 17:54:48.307] [modloading-worker-0/INFO] [FTB XMod Compat/]: [FTB Quests] Enabled KubeJS integration
[018月2025 17:54:48.317] [modloading-worker-0/INFO] [FTB XMod Compat/]: [FTB Chunks] Enabled KubeJS integration
[018月2025 17:54:48.322] [modloading-worker-0/INFO] [net.creeperhost.ftbbackups.repack.org.quartz.impl.StdSchedulerFactory/]: Using default implementation for ThreadExecutor
[018月2025 17:54:48.333] [modloading-worker-0/INFO] [zeta/]: Doing super early config setup for zeta
[018月2025 17:54:48.427] [modloading-worker-0/INFO] [net.creeperhost.ftbbackups.repack.org.quartz.core.SchedulerSignalerImpl/]: Initialized Scheduler Signaller of type: class net.creeperhost.ftbbackups.repack.org.quartz.core.SchedulerSignalerImpl
[018月2025 17:54:48.431] [modloading-worker-0/INFO] [net.creeperhost.ftbbackups.repack.org.quartz.core.QuartzScheduler/]: Quartz Scheduler v.2.0.2 created.
[018月2025 17:54:48.446] [modloading-worker-0/INFO] [net.creeperhost.ftbbackups.repack.org.quartz.simpl.RAMJobStore/]: RAMJobStore initialized.
[018月2025 17:54:48.452] [modloading-worker-0/INFO] [net.creeperhost.ftbbackups.repack.org.quartz.core.QuartzScheduler/]: Scheduler meta-data: Quartz Scheduler (v2.0.2) 'ftbbackups2' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'net.creeperhost.ftbbackups.repack.org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'net.creeperhost.ftbbackups.repack.org.quartz.simpl.SimpleThreadPool' - with 1 threads.
  Using job-store 'net.creeperhost.ftbbackups.repack.org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[018月2025 17:54:48.452] [modloading-worker-0/INFO] [net.creeperhost.ftbbackups.repack.org.quartz.impl.StdSchedulerFactory/]: Quartz scheduler 'ftbbackups2' initialized from an externally provided properties instance.
[018月2025 17:54:48.452] [modloading-worker-0/INFO] [net.creeperhost.ftbbackups.repack.org.quartz.impl.StdSchedulerFactory/]: Quartz scheduler version: 2.0.2
[018月2025 17:54:48.486] [modloading-worker-0/INFO] [net.creeperhost.ftbbackups.repack.org.quartz.core.QuartzScheduler/]: Scheduler ftbbackups2_$_NON_CLUSTERED started.
[018月2025 17:54:48.786] [modloading-worker-0/INFO] [Rhino Script Remapper/]: Loading Rhino Minecraft remapper...
[018月2025 17:54:48.791] [modloading-worker-0/INFO] [dev.latvian.mods.rhino.mod.util.RhinoProperties/]: Rhino properties loaded.
[018月2025 17:54:48.797] [modloading-worker-0/INFO] [Rhino Script Remapper/]: Loading mappings for 1.20.1
[018月2025 17:54:48.885] [modloading-worker-0/INFO] [Rhino Script Remapper/]: Done in 0.099 s
[018月2025 17:54:48.922] [modloading-worker-0/INFO] [quark-zeta/]: Discovered 164 modules to load.
[018月2025 17:54:48.922] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Abacus...
[018月2025 17:54:48.923] [modloading-worker-0/WARN] [mixin/]: @Inject(@At("INVOKE")) Shift.BY=2 on create_connected.mixins.json:sequencedgearshift.SequencedGearshiftScreenMixin::handler$bbf000$updateParamsOfRow exceeds the maximum allowed value: 0. Increase the value of maxShiftBy to suppress this warning.
[018月2025 17:54:48.940] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Adjustable Chat...
[018月2025 17:54:48.941] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Ambient Discs...
[018月2025 17:54:48.944] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Ancient Tomes...
[018月2025 17:54:48.963] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Ancient Wood...
[018月2025 17:54:48.973] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Armed Armor Stands...
[018月2025 17:54:48.974] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Auto Walk Keybind...
[018月2025 17:54:48.975] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Automatic Recipe Unlock...
[018月2025 17:54:48.977] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Automatic Tool Restock...
[018月2025 17:54:48.982] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Azalea Wood...
[018月2025 17:54:48.982] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Back Button Keybind...
[018月2025 17:54:48.984] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Backpack...
[018月2025 17:54:48.992] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Beacon Redirection...
[018月2025 17:54:48.993] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Better Elytra Rocket...
[018月2025 17:54:48.993] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Big Stone Clusters...
[018月2025 17:54:49.015] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Blossom Trees...
[018月2025 17:54:49.018] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Bottled Cloud...
[018月2025 17:54:49.022] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Buckets Show Inhabitants...
[018月2025 17:54:49.023] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Camera...
[018月2025 17:54:49.027] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Campfires Boost Elytra...
[018月2025 17:54:49.028] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Celebratory Lamps...
[018月2025 17:54:49.028] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Chains Connect Blocks...
[018月2025 17:54:49.029] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Chest Searching...
[018月2025 17:54:49.042] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Chorus Vegetation...
[018月2025 17:54:49.044] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Chute...
[018月2025 17:54:49.045] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Climate Control Remover...
[018月2025 17:54:49.047] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Color Runes...
[018月2025 17:54:49.052] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Compasses Work Everywhere...
[018月2025 17:54:49.054] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Compressed Blocks...
[018月2025 17:54:49.055] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Coral On Cactus...
[018月2025 17:54:49.055] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Corundum...
[018月2025 17:54:49.099] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Crabs...
[018月2025 17:54:49.104] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Crafter...
[018月2025 17:54:49.107] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Crate...
[018月2025 17:54:49.109] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Diamond Repair...
[018月2025 17:54:49.110] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Dispensers Place Blocks...
[018月2025 17:54:49.110] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Double Door Opening...
[018月2025 17:54:49.111] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Dragon Scales...
[018月2025 17:54:49.112] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Duskbound Blocks...
[018月2025 17:54:49.112] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Dyeable Item Frames...
[018月2025 17:54:49.115] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Easy Transfering...
[018月2025 17:54:49.115] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Elytra Indicator...
[018月2025 17:54:49.116] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Emotes...
[018月2025 17:54:49.122] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Enchantment Predicates...
[018月2025 17:54:49.123] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Enchantments Begone...
[018月2025 17:54:49.123] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Ender Watcher...
[018月2025 17:54:49.127] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Endermosh Music Disc...
[018月2025 17:54:49.134] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Enhanced Ladders...
[018月2025 17:54:49.137] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Expanded Item Interactions...
[018月2025 17:54:49.138] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Fairy Rings...
[018月2025 17:54:49.139] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Fallen Logs...
[018月2025 17:54:49.141] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Feeding Trough...
[018月2025 17:54:49.145] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Forgotten...
[018月2025 17:54:49.146] [modloading-worker-0/INFO] [thedarkcolour.kotlinforforge.test.KotlinForForge/]: Kotlin For Forge Enabled!
[018月2025 17:54:49.151] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Foxhound...
[018月2025 17:54:49.153] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Framed Glass...
[018月2025 17:54:49.154] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Game Nerfs...
[018月2025 17:54:49.155] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Glass Item Frame...
[018月2025 17:54:49.158] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Glass Shard...
[018月2025 17:54:49.159] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Glimmering Weald...
[018月2025 17:54:49.172] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Gold Bars...
[018月2025 17:54:49.172] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Gold Tools Have Fortune...
[018月2025 17:54:49.174] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Grab Chickens...
[018月2025 17:54:49.175] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Grate...
[018月2025 17:54:49.182] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Gravisand...
[018月2025 17:54:49.184] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Greener Grass...
[018月2025 17:54:49.190] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Hedges...
[018月2025 17:54:49.195] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Hoe Harvesting...
[018月2025 17:54:49.196] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Hollow Logs...
[018月2025 17:54:49.197] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Horses Swim...
[018月2025 17:54:49.197] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Hotbar Changer...
[018月2025 17:54:49.199] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Improved Sponges...
[018月2025 17:54:49.199] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Improved Tooltips...
[018月2025 17:54:49.201] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Industrial Palette...
[018月2025 17:54:49.204] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Inventory Sorting...
[018月2025 17:54:49.205] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Iron Rod...
[018月2025 17:54:49.216] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Item Sharing...
[018月2025 17:54:49.218] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Japanese Palette...
[018月2025 17:54:49.218] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Leaf Carpet...
[018月2025 17:54:49.220] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Lock Rotation...
[018月2025 17:54:49.222] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Long Range Pick Block...
[018月2025 17:54:49.223] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Magma Keeps Concrete Powder...
[018月2025 17:54:49.223] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Magnets...
[018月2025 17:54:49.226] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Map Washing...
[018月2025 17:54:49.228] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Matrix Enchanting...
[018月2025 17:54:49.233] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Metal Buttons...
[018月2025 17:54:49.234] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Microcrafting Helper...
[018月2025 17:54:49.235] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Midori...
[018月2025 17:54:49.236] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Monster Box...
[018月2025 17:54:49.237] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Banner Layers...
[018月2025 17:54:49.239] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Brick Types...
[018月2025 17:54:49.240] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Mud Blocks...
[018月2025 17:54:49.240] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Note Block Sounds...
[018月2025 17:54:49.241] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Potted Plants...
[018月2025 17:54:49.242] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Villagers...
[018月2025 17:54:49.242] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Narrator Readout...
[018月2025 17:54:49.244] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Nether Brick Fence Gate...
[018月2025 17:54:49.244] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Nether Obsidian Spikes...
[018月2025 17:54:49.245] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module New Stone Types...
[018月2025 17:54:49.254] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module No Durability On Cosmetics...
[018月2025 17:54:49.254] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module No More Lava Pockets...
[018月2025 17:54:49.255] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Obsidian Plate...
[018月2025 17:54:49.255] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Overlay Shader...
[018月2025 17:54:49.256] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Parrot Eggs...
[018月2025 17:54:49.257] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pat The Dogs...
[018月2025 17:54:49.259] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pathfinder Maps...
[018月2025 17:54:49.264] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Permafrost...
[018月2025 17:54:49.266] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Petals On Water...
[018月2025 17:54:49.268] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pickarang...
[018月2025 17:54:49.276] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pig Litters...
[018月2025 17:54:49.277] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pipes...
[018月2025 17:54:49.281] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Pistons Move Tile Entities...
[018月2025 17:54:49.282] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Poison Potato Usage...
[018月2025 17:54:49.283] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Quick Armor Swapping...
[018月2025 17:54:49.284] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Rainbow Lamps...
[018月2025 17:54:49.285] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Raw Metal Bricks...
[018月2025 17:54:49.285] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Reacharound Placing...
[018月2025 17:54:49.288] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Redstone Randomizer...
[018月2025 17:54:49.291] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Renewable Spore Blossoms...
[018月2025 17:54:49.291] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Replace Scaffolding...
[018月2025 17:54:49.292] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Rope...
[018月2025 17:54:49.298] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Safer Creatures...
[018月2025 17:54:49.298] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Seed Pouch...
[018月2025 17:54:49.305] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Shear Vines...
[018月2025 17:54:49.325] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Shiba...
[018月2025 17:54:49.328] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Shingles...
[018月2025 17:54:49.329] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Shulker Packing...
[018月2025 17:54:49.329] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Simple Harvest...
[018月2025 17:54:49.332] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Skull Pikes...
[018月2025 17:54:49.336] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Slabs To Blocks...
[018月2025 17:54:49.337] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Slime In A Bucket...
[018月2025 17:54:49.341] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Slimes To Magma Cubes...
[018月2025 17:54:49.341] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Snow Golem Player Heads...
[018月2025 17:54:49.342] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Soul Candles...
[018月2025 17:54:49.343] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Soul Sandstone...
[018月2025 17:54:49.343] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Spawner Replacer...
[018月2025 17:54:49.344] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Spiral Spires...
[018月2025 17:54:49.349] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Stonelings...
[018月2025 17:54:49.351] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Stools...
[018月2025 17:54:49.352] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Sturdy Stone...
[018月2025 17:54:49.354] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Thatch...
[018月2025 17:54:49.357] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Tiny Potato...
[018月2025 17:54:49.361] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Torch Arrow...
[018月2025 17:54:49.364] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Toretoise...
[018月2025 17:54:49.365] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Totem Of Holding...
[018月2025 17:54:49.368] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Trowel...
[018月2025 17:54:49.369] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Usage Ticker...
[018月2025 17:54:49.370] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Uses For Curses...
[018月2025 17:54:49.372] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Utility Recipes...
[018月2025 17:54:49.373] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Animal Textures...
[018月2025 17:54:49.376] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Bookshelves...
[018月2025 17:54:49.376] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Chests...
[018月2025 17:54:49.386] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Furnaces...
[018月2025 17:54:49.394] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Ladders...
[018月2025 17:54:49.395] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Variant Selector...
[018月2025 17:54:49.407] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Vertical Planks...
[018月2025 17:54:49.408] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Vertical Slabs...
[018月2025 17:54:49.418] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Vexes Die With Their Masters...
[018月2025 17:54:49.418] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Villager Rerolling Rework...
[018月2025 17:54:49.419] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Villagers Follow Emeralds...
[018月2025 17:54:49.420] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Wooden Posts...
[018月2025 17:54:49.421] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Wool Shuts Up Minecarts...
[018月2025 17:54:49.422] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Wraith...
[018月2025 17:54:49.425] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module Zombie Villagers On Normal...
[018月2025 17:54:49.425] [modloading-worker-0/INFO] [quark-zeta/]: Constructing module More Stone Variants...
[018月2025 17:54:49.426] [modloading-worker-0/INFO] [quark-zeta/]: Constructed 164 modules.
[018月2025 17:54:49.491] [modloading-worker-0/INFO] [quark-zeta/]: Doing super early config setup for quark
[018月2025 17:54:49.633] [modloading-worker-0/INFO] [mixin/]: Mixing client.MixinChatListener from mixins/common/nochatreports.mixins.json into net.minecraft.client.multiplayer.chat.ChatListener
[018月2025 17:54:49.652] [modloading-worker-0/INFO] [mixin/]: Mixing client.MixinServerData from mixins/common/nochatreports.mixins.json into net.minecraft.client.multiplayer.ServerData
[018月2025 17:54:49.863] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:Chinese_cuisine_block.js in 0.11 s
[018月2025 17:54:49.865] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:Chinese_cuisine_fluid.js in 0.001 s
[018月2025 17:54:49.873] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:Chinese_cuisine_item.js in 0.005 s
[018月2025 17:54:49.874] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:Chinese_cuisine_item_events.js in 0.001 s
[018月2025 17:54:49.875] [modloading-worker-0/INFO] [KubeJS Startup/]: example.js#5: Hello, World! (Loaded startup scripts)
[018月2025 17:54:49.879] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:example.js in 0.002 s
[018月2025 17:54:49.880] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded script startup_scripts:suxu.js in 0.001 s
[018月2025 17:54:49.891] [modloading-worker-0/INFO] [KubeJS Startup/]: Loaded 6/6 KubeJS startup scripts in 0.994 s with 0 errors and 0 warnings
[018月2025 17:54:50.035] [modloading-worker-0/INFO] [KubeJS Client/]: Loaded script client_scripts:Chinese_cuisine.js in 0.003 s
[018月2025 17:54:50.035] [modloading-worker-0/INFO] [KubeJS Client/]: example.js#5: Hello, World! (Loaded client scripts)
[018月2025 17:54:50.036] [modloading-worker-0/INFO] [KubeJS Client/]: Loaded script client_scripts:example.js in 0.0 s
[018月2025 17:54:50.037] [modloading-worker-0/INFO] [KubeJS Client/]: Loaded 2/2 KubeJS client scripts in 0.088 s with 0 errors and 0 warnings
[018月2025 17:54:50.204] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id kubejs:send_data_from_client
[018月2025 17:54:50.207] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id kubejs:send_data_from_server
[018月2025 17:54:50.210] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id kubejs:paint
[018月2025 17:54:50.212] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id kubejs:add_stage
[018月2025 17:54:50.214] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id kubejs:remove_stage
[018月2025 17:54:50.216] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id kubejs:sync_stages
[018月2025 17:54:50.218] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering C2S receiver with id kubejs:first_click
[018月2025 17:54:50.220] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id kubejs:toast
[018月2025 17:54:50.222] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id kubejs:reload_startup_scripts
[018月2025 17:54:50.225] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id kubejs:display_server_errors
[018月2025 17:54:50.227] [modloading-worker-0/INFO] [dev.architectury.networking.forge.NetworkManagerImpl/]: Registering S2C receiver with id kubejs:display_client_errors
[018月2025 17:54:50.760] [modloading-worker-0/INFO] [mixin/]: Mixing common.MixinServerStatus from mixins/common/nochatreports.mixins.json into net.minecraft.network.protocol.status.ServerStatus
[018月2025 17:54:53.683] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: CoinsInChestModifier was deserialized!
[018月2025 17:54:53.689] [Render thread/WARN] [net.minecraftforge.registries.ForgeRegistry/REGISTRIES]: Registry forge:global_loot_modifier_serializers: The object RecordCodec[UnitDecoder[com.tyxcnjiu.main.dumplingsdelight.event.loot.CalamariAdditionModifier$$Lambda$15410/0x00000294fef56518@5f177e0] * Field[conditions: passthrough[flatXmapped]] * Field[item: net.minecraftforge.registries.ForgeRegistry$RegistryCodec@132ba2ab]] has been registered twice for the same name dumplings_delight:add_calamari.
[018月2025 17:54:53.719] [Render thread/INFO] [Easy NPC/]: - Registering Synched Attack Data for EasyNPCBaseEntity.
[018月2025 17:54:53.720] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.AttackData
[018月2025 17:54:53.720] [Render thread/INFO] [Easy NPC/]: - Registering Synched Attribute Data for EasyNPCBaseEntity.
[018月2025 17:54:53.720] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.AttributeData
[018月2025 17:54:53.720] [Render thread/INFO] [Easy NPC/]: - Registering Synched Custom Attribute Data for EasyNPCBaseEntity.
[018月2025 17:54:53.720] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.CustomAttributeData
[018月2025 17:54:53.720] [Render thread/INFO] [Easy NPC/]: - Registering Synched Display Attribute Data for EasyNPCBaseEntity.
[018月2025 17:54:53.720] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.DisplayAttributeData
[018月2025 17:54:53.720] [Render thread/INFO] [Easy NPC/]: - Registering Synched Navigation Data for EasyNPCBaseEntity.
[018月2025 17:54:53.720] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.NavigationData
[018月2025 17:54:53.720] [Render thread/INFO] [Easy NPC/]: - Registering Synched Owner Data for EasyNPCBaseEntity.
[018月2025 17:54:53.720] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.OwnerData
[018月2025 17:54:53.720] [Render thread/INFO] [Easy NPC/]: - Registering Synched Profession Data for EasyNPCBaseEntity.
[018月2025 17:54:53.721] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ProfessionData
[018月2025 17:54:53.721] [Render thread/INFO] [Easy NPC/]: - Registering Synched Render Data for EasyNPCBaseEntity.
[018月2025 17:54:53.721] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.RenderData
[018月2025 17:54:53.721] [Render thread/INFO] [Easy NPC/]: - Registering Synched Skin Data for EasyNPCBaseEntity.
[018月2025 17:54:53.721] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.SkinData
[018月2025 17:54:53.721] [Render thread/INFO] [Easy NPC/]: - Registering Synched Sound Data for EasyNPCBaseEntity.
[018月2025 17:54:53.721] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.SoundData
[018月2025 17:54:53.721] [Render thread/INFO] [Easy NPC/]: - Registering Synched Trading Data for EasyNPCBaseEntity.
[018月2025 17:54:53.721] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.TradingData
[018月2025 17:54:53.721] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.TradingData
[018月2025 17:54:53.721] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.TradingData
[018月2025 17:54:53.721] [Render thread/INFO] [Easy NPC/]: - Registering Synched Variant Data for EasyNPCBaseEntity.
[018月2025 17:54:53.721] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.VariantData
[018月2025 17:54:53.722] [Render thread/INFO] [Easy NPC/]: - Registering Synched Model Data for EasyNPCBaseModelEntity.
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelData
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelData
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelData
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelData
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelData
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelData
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelData
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelData
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelData
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelData
[018月2025 17:54:53.722] [Render thread/INFO] [Easy NPC/]: - Registering Synched Model Position Data for EasyNPCBaseModelEntity.
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelPositionData
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelPositionData
[018月2025 17:54:53.722] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelPositionData
[018月2025 17:54:53.723] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelPositionData
[018月2025 17:54:53.723] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelPositionData
[018月2025 17:54:53.723] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelPositionData
[018月2025 17:54:53.723] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelPositionData
[018月2025 17:54:53.723] [Render thread/INFO] [Easy NPC/]: - Registering Synched Model Rotation Data for EasyNPCBaseModelEntity.
[018月2025 17:54:53.723] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelRotationData
[018月2025 17:54:53.723] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelRotationData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelRotationData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelRotationData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelRotationData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelRotationData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelRotationData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelRotationData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelRotationData
[018月2025 17:54:53.724] [Render thread/INFO] [Easy NPC/]: - Registering Synched Model Visibility Data for EasyNPCBaseModelEntity.
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelVisibilityData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelVisibilityData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelVisibilityData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelVisibilityData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelVisibilityData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelVisibilityData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelVisibilityData
[018月2025 17:54:53.724] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelVisibilityData
[018月2025 17:54:53.725] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelVisibilityData
[018月2025 17:54:53.725] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelVisibilityData
[018月2025 17:54:53.725] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ModelVisibilityData
[018月2025 17:54:53.725] [Render thread/INFO] [Easy NPC/]: - Registering Synched Scale Data for EasyNPCBaseModelEntity.
[018月2025 17:54:53.725] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ScaleData
[018月2025 17:54:53.725] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ScaleData
[018月2025 17:54:53.725] [Render thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class de.markusbordihn.easynpc.entity.EasyNPCBaseModelEntity from interface de.markusbordihn.easynpc.entity.easynpc.data.ScaleData
[018月2025 17:54:53.764] [Render thread/INFO] [ModernFix/]: Replacing search trees with 'JEI' provider
[018月2025 17:54:53.885] [Render thread/INFO] [chloride/]: Registering CHLORIDE built-in packs
[018月2025 17:54:54.148] [Render thread/INFO] [Oculus/]: Hardware information:
[018月2025 17:54:54.149] [Render thread/INFO] [Oculus/]: CPU: 16x 13th Gen Intel(R) Core(TM) i7-13620H
[018月2025 17:54:54.149] [Render thread/INFO] [Oculus/]: GPU: NVIDIA GeForce RTX 4060 Laptop GPU/PCIe/SSE2 (Supports OpenGL 4.6.0 NVIDIA 572.70)
[018月2025 17:54:54.149] [Render thread/INFO] [Oculus/]: OS: Windows 11 (10.0)
[018月2025 17:54:54.166] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.vertex.VertexFormatDescriptionImpl, which may cause instability.
[018月2025 17:54:54.171] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.vertex.buffer.SodiumBufferBuilder, which may cause instability.
[018月2025 17:54:54.186] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.SodiumWorldRenderer, which may cause instability.
[018月2025 17:54:54.277] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC Entity Layer Definitions ...
[018月2025 17:54:54.295] [Render thread/INFO] [quark/]: Registering model layer quark:crab#main
[018月2025 17:54:54.295] [Render thread/INFO] [quark/]: Registering model layer quark:shiba#main
[018月2025 17:54:54.297] [Render thread/INFO] [quark/]: Registering model layer quark:quark_boat_chest#main
[018月2025 17:54:54.297] [Render thread/INFO] [quark/]: Registering model layer quark:wraith#main
[018月2025 17:54:54.297] [Render thread/INFO] [quark/]: Registering model layer quark:forgotten_hat#main
[018月2025 17:54:54.297] [Render thread/INFO] [quark/]: Registering model layer quark:stoneling#main
[018月2025 17:54:54.297] [Render thread/INFO] [quark/]: Registering model layer quark:toretoise#main
[018月2025 17:54:54.297] [Render thread/INFO] [quark/]: Registering model layer quark:quark_boat#main
[018月2025 17:54:54.297] [Render thread/INFO] [quark/]: Registering model layer quark:foxhound#main
[018月2025 17:54:54.297] [Render thread/INFO] [quark/]: Registering model layer quark:backpack#main
[018月2025 17:54:54.299] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC Entity Renders ...
[018月2025 17:54:54.686] [Render thread/INFO] [net.minecraft.server.packs.resources.ReloadableResourceManager/]: Reloading ResourceManager: vanilla, mod_resources, KubeJS Resource Pack [assets], file/Minecraft-Mod-Language-Modpack-Converted-1.20.1.zip
[018月2025 17:54:54.738] [Render thread/INFO] [mixin/]: Mixing client.MixinJoinMultiplayerScreen from mixins/common/nochatreports.mixins.json into net.minecraft.client.gui.screens.multiplayer.JoinMultiplayerScreen
[018月2025 17:54:54.738] [Render thread/INFO] [mixin/]: Renaming synthetic method lambda$onInit$2()Lnet/minecraft/network/chat/Component; to mdac68fa$lambda$onInit$2$0 in mixins/common/nochatreports.mixins.json:client.MixinJoinMultiplayerScreen
[018月2025 17:54:54.738] [Render thread/INFO] [mixin/]: Renaming synthetic method lambda$onInit$1(Lnet/minecraft/client/gui/components/Button;)V to mdac68fa$lambda$onInit$1$1 in mixins/common/nochatreports.mixins.json:client.MixinJoinMultiplayerScreen
[018月2025 17:54:54.739] [Render thread/INFO] [mixin/]: Renaming synthetic method lambda$onInit$0(Lnet/minecraft/client/gui/components/Button;)V to mdac68fa$lambda$onInit$0$2 in mixins/common/nochatreports.mixins.json:client.MixinJoinMultiplayerScreen
[018月2025 17:54:54.977] [Finalizer/WARN] [ModernFix/]: One or more BufferBuilders have been leaked, ModernFix will attempt to correct this.
[018月2025 17:54:54.985] [modloading-worker-0/INFO] [net.blay09.mods.craftingtweaks.registry.ModFileJsonCompatLoader/]: Mod file craftingtweaks has registered com.tom.storagemod.gui.CraftingTerminalMenu of toms_storage with CraftingTweaks
[018月2025 17:54:55.005] [modloading-worker-0/INFO] [com.tom.storagemod.StorageMod/]: Loaded Tom's Simple Storage config file toms_storage-common.toml
[018月2025 17:54:55.120] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [configured] Starting version check at https://mrcrayfish.com/modupdatejson?id=configured
[018月2025 17:54:55.152] [Worker-ResourceReload-10/INFO] [com.tom.storagemod.StorageMod/]: Tom's Storage Setup starting
[018月2025 17:54:55.155] [Worker-ResourceReload-5/INFO] [FTB Library/]: Setting game stages provider implementation to: KubeJS Stages
[018月2025 17:54:55.155] [Worker-ResourceReload-5/INFO] [FTB XMod Compat/]: Chose [KubeJS Stages] as the active game stages implementation
[018月2025 17:54:55.163] [Worker-ResourceReload-5/INFO] [FTB XMod Compat/]: Chose [FALLBACK] as the active permissions implementation
[018月2025 17:54:55.167] [Worker-ResourceReload-10/INFO] [com.tom.storagemod.StorageMod/]: Initilaized Network Handler
[018月2025 17:54:55.169] [Worker-ResourceReload-5/INFO] [FTB XMod Compat/]: [FTB Quests] recipe helper provider is [JEI]
[018月2025 17:54:55.180] [Worker-ResourceReload-5/INFO] [FTB XMod Compat/]: [FTB Quests] Enabled Item Filters integration
[018月2025 17:54:56.385] [Forge Version Check/WARN] [net.minecraftforge.fml.VersionChecker/]: Failed to process update information
com.google.gson.JsonSyntaxException: java.lang.IllegalStateException: Expected BEGIN_OBJECT but was STRING at line 1 column 1 path $
	at com.google.gson.Gson.fromJson(Gson.java:1226) ~[gson-2.10.jar%2386!/:?]
	at com.google.gson.Gson.fromJson(Gson.java:1124) ~[gson-2.10.jar%2386!/:?]
	at com.google.gson.Gson.fromJson(Gson.java:1034) ~[gson-2.10.jar%2386!/:?]
	at com.google.gson.Gson.fromJson(Gson.java:969) ~[gson-2.10.jar%2386!/:?]
	at net.minecraftforge.fml.VersionChecker$1.process(VersionChecker.java:186) ~[fmlcore-1.20.1-47.4.0.jar%23307!/:?]
	at java.lang.Iterable.forEach(Iterable.java:75) ~[?:?]
	at net.minecraftforge.fml.VersionChecker$1.run(VersionChecker.java:117) ~[fmlcore-1.20.1-47.4.0.jar%23307!/:?]
Caused by: java.lang.IllegalStateException: Expected BEGIN_OBJECT but was STRING at line 1 column 1 path $
	at com.google.gson.stream.JsonReader.beginObject(JsonReader.java:393) ~[gson-2.10.jar%2386!/:?]
	at com.google.gson.internal.bind.MapTypeAdapterFactory$Adapter.read(MapTypeAdapterFactory.java:182) ~[gson-2.10.jar%2386!/:?]
	at com.google.gson.internal.bind.MapTypeAdapterFactory$Adapter.read(MapTypeAdapterFactory.java:144) ~[gson-2.10.jar%2386!/:?]
	at com.google.gson.Gson.fromJson(Gson.java:1214) ~[gson-2.10.jar%2386!/:?]
	... 6 more
[018月2025 17:54:56.386] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [catalogue] Starting version check at https://mrcrayfish.com/modupdatejson?id=catalogue
[018月2025 17:54:56.721] [Forge Version Check/WARN] [net.minecraftforge.fml.VersionChecker/]: Failed to process update information
com.google.gson.JsonSyntaxException: java.lang.IllegalStateException: Expected BEGIN_OBJECT but was STRING at line 1 column 1 path $
	at com.google.gson.Gson.fromJson(Gson.java:1226) ~[gson-2.10.jar%2386!/:?]
	at com.google.gson.Gson.fromJson(Gson.java:1124) ~[gson-2.10.jar%2386!/:?]
	at com.google.gson.Gson.fromJson(Gson.java:1034) ~[gson-2.10.jar%2386!/:?]
	at com.google.gson.Gson.fromJson(Gson.java:969) ~[gson-2.10.jar%2386!/:?]
	at net.minecraftforge.fml.VersionChecker$1.process(VersionChecker.java:186) ~[fmlcore-1.20.1-47.4.0.jar%23307!/:?]
	at java.lang.Iterable.forEach(Iterable.java:75) ~[?:?]
	at net.minecraftforge.fml.VersionChecker$1.run(VersionChecker.java:117) ~[fmlcore-1.20.1-47.4.0.jar%23307!/:?]
Caused by: java.lang.IllegalStateException: Expected BEGIN_OBJECT but was STRING at line 1 column 1 path $
	at com.google.gson.stream.JsonReader.beginObject(JsonReader.java:393) ~[gson-2.10.jar%2386!/:?]
	at com.google.gson.internal.bind.MapTypeAdapterFactory$Adapter.read(MapTypeAdapterFactory.java:182) ~[gson-2.10.jar%2386!/:?]
	at com.google.gson.internal.bind.MapTypeAdapterFactory$Adapter.read(MapTypeAdapterFactory.java:144) ~[gson-2.10.jar%2386!/:?]
	at com.google.gson.Gson.fromJson(Gson.java:1214) ~[gson-2.10.jar%2386!/:?]
	... 6 more
[018月2025 17:54:56.723] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [forge] Starting version check at https://files.minecraftforge.net/net/minecraftforge/forge/promotions_slim.json
[018月2025 17:54:57.332] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [forge] Found status: UP_TO_DATE Current: 47.4.0 Target: null
[018月2025 17:54:57.332] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [create_connected] Starting version check at https://raw.githubusercontent.com/hlysine/create_connected/main/update.json
[018月2025 17:54:57.609] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [create_connected] Found status: UP_TO_DATE Current: 1.1.7-mc1.20.1 Target: null
[018月2025 17:54:57.609] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [gml] Starting version check at https://maven.moddinginquisition.org/releases/org/groovymc/gml/gml/forge-promotions.json
[018月2025 17:54:57.937] [FTB Backups Config Watcher 0/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Config at C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\ftbbackups2.json has changed, reloaded!
[018月2025 17:54:58.308] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [gml] Found status: BETA Current: 4.0.10 Target: null
[018月2025 17:54:58.308] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [createsifter] Starting version check at https://api.modrinth.com/updates/create-sifting/forge_updates.json
[018月2025 17:54:58.851] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [createsifter] Found status: UP_TO_DATE Current: 1.20.1-1.8.6-6.0.6 Target: null
[018月2025 17:54:58.851] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [toms_storage] Starting version check at https://raw.githubusercontent.com/tom5454/Toms-Storage/master/version-check.json
[018月2025 17:54:59.111] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [toms_storage] Found status: BETA Current: 1.7.1 Target: 1.7.1
[018月2025 17:54:59.709] [Render thread/INFO] [KubeJS Client/]: Client resource reload complete!
[018月2025 17:54:59.789] [Worker-ResourceReload-14/INFO] [net.minecraft.client.gui.font.providers.UnihexProvider/]: Found unifont_all_no_pua-15.0.06.hex, loading
[018月2025 17:55:09.935] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC Network Handler for net.minecraftforge.network.simple.SimpleChannel@345f49a4 with version 23
[018月2025 17:55:09.936] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC Network Handler ...
[018月2025 17:55:09.936] [Render thread/INFO] [Easy NPC/]: Registering network messages for BOTH side ...
[018月2025 17:55:09.937] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering client network message ExportClientPresetMessage with easy_npc:preset_export_client (0)
[018月2025 17:55:09.937] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering client network message OpenMenuCallbackMessage with easy_npc:open_menu_callback_message (1)
[018月2025 17:55:09.938] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering client network message SyncDataMessage with easy_npc:sync_data (2)
[018月2025 17:55:09.939] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message AddOrUpdateObjectiveMessage with easy_npc:add_objective (3)
[018月2025 17:55:09.939] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeActionEventMessage with easy_npc:change_action_event (4)
[018月2025 17:55:09.940] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeAdvancedTradingMessage with easy_npc:change_advanced_trading (5)
[018月2025 17:55:09.941] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeBasicTradingMessage with easy_npc:change_basic_trading (6)
[018月2025 17:55:09.941] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeCombatAttributeMessage with easy_npc:change_combat_attribute (7)
[018月2025 17:55:09.942] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeDisplayAttributeMessage with easy_npc:change_display_attribute (8)
[018月2025 17:55:09.943] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeEntityAttributeMessage with easy_npc:change_entity_attribute (9)
[018月2025 17:55:09.943] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeEntityBaseAttributeMessage with easy_npc:change_entity_base_attribute (10)
[018月2025 17:55:09.944] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeEnvironmentalAttributeMessage with easy_npc:change_environmental_attribute (11)
[018月2025 17:55:09.944] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeInteractionAttributeMessage with easy_npc:change_interaction_attribute (12)
[018月2025 17:55:09.945] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeModelEquipmentVisibilityMessage with easy_npc:change_model_equipment_visibility (13)
[018月2025 17:55:09.946] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeModelLockRotationMessage with easy_npc:change_model_lock_rotation (14)
[018月2025 17:55:09.946] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeModelPoseMessage with easy_npc:change_model_pose (15)
[018月2025 17:55:09.947] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeModelPositionMessage with easy_npc:change_model_position (16)
[018月2025 17:55:09.947] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeModelRotationMessage with easy_npc:change_model_rotation (17)
[018月2025 17:55:09.948] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeModelVisibilityMessage with easy_npc:change_model_visibility (18)
[018月2025 17:55:09.948] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeMovementAttributeMessage with easy_npc:change_movement_attribute (19)
[018月2025 17:55:09.948] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeNameMessage with easy_npc:change_name (20)
[018月2025 17:55:09.949] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangePoseMessage with easy_npc:change_pose (21)
[018月2025 17:55:09.949] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangePositionMessage with easy_npc:change_position (22)
[018月2025 17:55:09.950] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeProfessionMessage with easy_npc:change_profession (23)
[018月2025 17:55:09.950] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeRendererMessage with easy_npc:change_renderer (24)
[018月2025 17:55:09.951] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeScaleMessage with easy_npc:change_scale (25)
[018月2025 17:55:09.952] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeSkinMessage with easy_npc:change_skin (26)
[018月2025 17:55:09.953] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeSpawnerSettingMessage with easy_npc:change_spawner_settings (27)
[018月2025 17:55:09.953] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ChangeTradingTypeMessage with easy_npc:change_trading_type (28)
[018月2025 17:55:09.954] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ExecuteActionEventMessage with easy_npc:trigger_action_event (29)
[018月2025 17:55:09.954] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ExecuteDialogButtonActionMessage with easy_npc:dialog_button_action (30)
[018月2025 17:55:09.954] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ExportPresetMessage with easy_npc:export_preset (31)
[018月2025 17:55:09.955] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ExportWorldPresetMessage with easy_npc:export_world_preset (32)
[018月2025 17:55:09.955] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message ImportPresetMessage with easy_npc:import_preset (33)
[018月2025 17:55:09.956] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message OpenActionDataEditorMessage with easy_npc:open_action_data_editor (34)
[018月2025 17:55:09.956] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message OpenActionDataEntryEditorMessage with easy_npc:open_action_data_entry_editor (35)
[018月2025 17:55:09.956] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message OpenConfigurationMessage with easy_npc:open_configuration_screen (36)
[018月2025 17:55:09.957] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message OpenDialogButtonEditorMessage with easy_npc:open_dialog_button_editor (37)
[018月2025 17:55:09.957] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message OpenDialogEditorMessage with easy_npc:open_dialog_editor (38)
[018月2025 17:55:09.958] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message OpenMenuMessage with easy_npc:open_menu_message (39)
[018月2025 17:55:09.959] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message OpenDialogTextEditorMessage with easy_npc:open_dialog_text_editor (40)
[018月2025 17:55:09.959] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message RemoveDialogButtonMessage with easy_npc:remove_dialog_button (41)
[018月2025 17:55:09.959] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message RemoveDialogMessage with easy_npc:remove_dialog (42)
[018月2025 17:55:09.960] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message RemoveNPCMessage with easy_npc:remove_npc (43)
[018月2025 17:55:09.960] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message RemoveObjectiveMessage with easy_npc:remove_objective (44)
[018月2025 17:55:09.960] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message RequestDataSyncMessage with easy_npc:request_data_sync (45)
[018月2025 17:55:09.961] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message RespawnNPCMessage with easy_npc:respawn_npc (46)
[018月2025 17:55:09.961] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message SaveDialogButtonMessage with easy_npc:save_dialog_button (47)
[018月2025 17:55:09.961] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message SaveDialogMessage with easy_npc:save_dialog (48)
[018月2025 17:55:09.962] [Render thread/INFO] [Easy NPC/]: [NetworkHandler] Registering server network message SaveDialogSetMessage with easy_npc:save_dialog_set (49)
[018月2025 17:55:09.968] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Reloading config/lightmanscurrency-common.txt
[018月2025 17:55:09.983] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: ATM Icon Type 'lightmanscurrency:item' has been registered successfully.
[018月2025 17:55:09.984] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: ATM Icon Type 'lightmanscurrency:small_arrow' has been registered successfully.
[018月2025 17:55:09.985] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: ATM Icon Type 'lightmanscurrency:sprite' has been registered successfully.
[018月2025 17:55:10.025] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TraderType lightmanscurrency:item_trader
[018月2025 17:55:10.026] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TraderType lightmanscurrency:item_trader_armor
[018月2025 17:55:10.026] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TraderType lightmanscurrency:item_trader_ticket
[018月2025 17:55:10.027] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TraderType lightmanscurrency:item_trader_book
[018月2025 17:55:10.028] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TraderType lightmanscurrency:slot_machine_trader
[018月2025 17:55:10.031] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TraderType lightmanscurrency:paygate
[018月2025 17:55:10.035] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TraderType lightmanscurrency:auction_house
[018月2025 17:55:10.036] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TraderType lightmanscurrency:commands
[018月2025 17:55:10.037] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TraderType lightmanscurrency:gacha
[018月2025 17:55:10.045] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TradeRuleType lightmanscurrency:player_list
[018月2025 17:55:10.045] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TradeRuleType lightmanscurrency:player_trade_limit
[018月2025 17:55:10.045] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TradeRuleType lightmanscurrency:discount_list
[018月2025 17:55:10.045] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TradeRuleType lightmanscurrency:timed_sale
[018月2025 17:55:10.045] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TradeRuleType lightmanscurrency:trade_limit
[018月2025 17:55:10.045] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TradeRuleType lightmanscurrency:free_sample
[018月2025 17:55:10.045] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TradeRuleType lightmanscurrency:price_fluctuation
[018月2025 17:55:10.045] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TradeRuleType lightmanscurrency:demand_pricing
[018月2025 17:55:10.045] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered TradeRuleType lightmanscurrency:daily_trades
[018月2025 17:55:10.046] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:item_trade
[018月2025 17:55:10.046] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:paygate_trade
[018月2025 17:55:10.046] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:slot_machine_trade
[018月2025 17:55:10.046] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:out_of_stock
[018月2025 17:55:10.046] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:bank_low_balance
[018月2025 17:55:10.046] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:auction_house_seller
[018月2025 17:55:10.046] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:auction_house_buyer
[018月2025 17:55:10.046] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:auction_house_seller_nobid
[018月2025 17:55:10.046] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:auction_house_outbid
[018月2025 17:55:10.046] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:auction_house_canceled
[018月2025 17:55:10.047] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:text
[018月2025 17:55:10.047] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:add_remove_ally
[018月2025 17:55:10.047] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:add_remove_trade
[018月2025 17:55:10.047] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:change_ally_permissions
[018月2025 17:55:10.047] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:change_creative
[018月2025 17:55:10.047] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:changed_name
[018月2025 17:55:10.047] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:change_ownership
[018月2025 17:55:10.047] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:change_settings_simple
[018月2025 17:55:10.047] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:change_settings_advanced
[018月2025 17:55:10.047] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:change_settings_dumb
[018月2025 17:55:10.048] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:bank_deposit_player
[018月2025 17:55:10.048] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:bank_deposit_trader
[018月2025 17:55:10.048] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:bank_deposit_server
[018月2025 17:55:10.048] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:bank_transfer
[018月2025 17:55:10.048] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:bank_interest
[018月2025 17:55:10.048] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:taxes_collected
[018月2025 17:55:10.048] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:taxes_paid
[018月2025 17:55:10.048] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:block_ejected
[018月2025 17:55:10.048] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:command_trade
[018月2025 17:55:10.048] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationType lightmanscurrency:gacha_trade
[018月2025 17:55:10.049] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationCategoryType lightmanscurrency:general
[018月2025 17:55:10.049] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationCategoryType lightmanscurrency:null
[018月2025 17:55:10.049] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationCategoryType lightmanscurrency:seasonal_event
[018月2025 17:55:10.050] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationCategoryType lightmanscurrency:trader
[018月2025 17:55:10.050] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationCategoryType lightmanscurrency:bank
[018月2025 17:55:10.050] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationCategoryType lightmanscurrency:auction_house
[018月2025 17:55:10.050] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered NotificationCategoryType lightmanscurrency:tax_entry
[018月2025 17:55:10.061] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered Item Listing serializer 'lightmanscurrency:simple'
[018月2025 17:55:10.062] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered Item Listing serializer 'lightmanscurrency:random_selection'
[018月2025 17:55:10.062] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered Item Listing serializer 'lightmanscurrency:enchanted_item_for_coins'
[018月2025 17:55:10.063] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered Item Listing serializer 'lightmanscurrency:enchanted_book_for_coins'
[018月2025 17:55:10.064] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registered Item Listing serializer 'lightmanscurrency:items_for_map'
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: java.lang.NoSuchMethodException: net.minecraft.world.item.alchemy.PotionBrewing.addMix(net.minecraft.world.item.alchemy.Potion,net.minecraft.world.item.Item,net.minecraft.world.item.alchemy.Potion)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.lang.Class.getDeclaredMethod(Class.java:2675)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/vintagedelight@0.1.6/net.ribs.vintagedelight.mobEffects.CustomPotionBrewing.addMix(CustomPotionBrewing.java:11)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/vintagedelight@0.1.6/net.ribs.vintagedelight.VintageDelight.lambda$setup$1(VintageDelight.java:92)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at LAYER PLUGIN/fmlcore@1.20.1-47.4.0/net.minecraftforge.fml.DeferredWorkQueue.lambda$makeRunnable$2(DeferredWorkQueue.java:81)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at LAYER PLUGIN/fmlcore@1.20.1-47.4.0/net.minecraftforge.fml.DeferredWorkQueue.makeRunnable(DeferredWorkQueue.java:76)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at LAYER PLUGIN/fmlcore@1.20.1-47.4.0/net.minecraftforge.fml.DeferredWorkQueue.lambda$runTasks$0(DeferredWorkQueue.java:60)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.util.concurrent.ConcurrentLinkedDeque.forEach(ConcurrentLinkedDeque.java:1650)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at LAYER PLUGIN/fmlcore@1.20.1-47.4.0/net.minecraftforge.fml.DeferredWorkQueue.runTasks(DeferredWorkQueue.java:60)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/forge@47.4.0/net.minecraftforge.fml.core.ParallelTransition.lambda$finalActivityGenerator$2(ParallelTransition.java:35)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:646)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:482)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.server.packs.resources.SimpleReloadInstance.m_143940_(SimpleReloadInstance.java:69)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.util.thread.BlockableEventLoop.m_6367_(BlockableEventLoop.java:156)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.util.thread.ReentrantBlockableEventLoop.m_6367_(ReentrantBlockableEventLoop.java:23)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.util.thread.BlockableEventLoop.m_7245_(BlockableEventLoop.java:130)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.util.thread.BlockableEventLoop.m_18699_(BlockableEventLoop.java:115)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.client.Minecraft.m_91383_(Minecraft.java:1106)
[018月2025 17:55:10.113] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.client.Minecraft.m_91374_(Minecraft.java:718)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.client.main.Main.main(Main.java:218)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/fmlloader@1.20.1-47.4.0/net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/fmlloader@1.20.1-47.4.0/net.minecraftforge.fml.loading.targets.CommonLaunchHandler.clientService(CommonLaunchHandler.java:99)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/fmlloader@1.20.1-47.4.0/net.minecraftforge.fml.loading.targets.CommonClientLaunchHandler.lambda$makeService$0(CommonClientLaunchHandler.java:25)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.Launcher.run(Launcher.java:108)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.Launcher.main(Launcher.java:78)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at cpw.mods.bootstraplauncher@1.1.2/cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at oolloo.jlw.Wrapper.main(Wrapper.java:105)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: java.lang.NoSuchMethodException: net.minecraft.world.item.alchemy.PotionBrewing.addMix(net.minecraft.world.item.alchemy.Potion,net.minecraft.world.item.Item,net.minecraft.world.item.alchemy.Potion)
[018月2025 17:55:10.114] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.lang.Class.getDeclaredMethod(Class.java:2675)
[018月2025 17:55:10.115] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/vintagedelight@0.1.6/net.ribs.vintagedelight.mobEffects.CustomPotionBrewing.addMix(CustomPotionBrewing.java:11)
[018月2025 17:55:10.115] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/vintagedelight@0.1.6/net.ribs.vintagedelight.VintageDelight.lambda$setup$1(VintageDelight.java:93)
[018月2025 17:55:10.115] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
[018月2025 17:55:10.115] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at LAYER PLUGIN/fmlcore@1.20.1-47.4.0/net.minecraftforge.fml.DeferredWorkQueue.lambda$makeRunnable$2(DeferredWorkQueue.java:81)
[018月2025 17:55:10.115] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at LAYER PLUGIN/fmlcore@1.20.1-47.4.0/net.minecraftforge.fml.DeferredWorkQueue.makeRunnable(DeferredWorkQueue.java:76)
[018月2025 17:55:10.115] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at LAYER PLUGIN/fmlcore@1.20.1-47.4.0/net.minecraftforge.fml.DeferredWorkQueue.lambda$runTasks$0(DeferredWorkQueue.java:60)
[018月2025 17:55:10.115] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.util.concurrent.ConcurrentLinkedDeque.forEach(ConcurrentLinkedDeque.java:1650)
[018月2025 17:55:10.115] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at LAYER PLUGIN/fmlcore@1.20.1-47.4.0/net.minecraftforge.fml.DeferredWorkQueue.runTasks(DeferredWorkQueue.java:60)
[018月2025 17:55:10.115] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/forge@47.4.0/net.minecraftforge.fml.core.ParallelTransition.lambda$finalActivityGenerator$2(ParallelTransition.java:35)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:646)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:482)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.server.packs.resources.SimpleReloadInstance.m_143940_(SimpleReloadInstance.java:69)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.util.thread.BlockableEventLoop.m_6367_(BlockableEventLoop.java:156)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.util.thread.ReentrantBlockableEventLoop.m_6367_(ReentrantBlockableEventLoop.java:23)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.util.thread.BlockableEventLoop.m_7245_(BlockableEventLoop.java:130)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.util.thread.BlockableEventLoop.m_18699_(BlockableEventLoop.java:115)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.client.Minecraft.m_91383_(Minecraft.java:1106)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.client.Minecraft.m_91374_(Minecraft.java:718)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.client.main.Main.main(Main.java:218)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/fmlloader@1.20.1-47.4.0/net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/fmlloader@1.20.1-47.4.0/net.minecraftforge.fml.loading.targets.CommonLaunchHandler.clientService(CommonLaunchHandler.java:99)
[018月2025 17:55:10.116] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/fmlloader@1.20.1-47.4.0/net.minecraftforge.fml.loading.targets.CommonClientLaunchHandler.lambda$makeService$0(CommonClientLaunchHandler.java:25)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.Launcher.run(Launcher.java:108)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.Launcher.main(Launcher.java:78)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at cpw.mods.bootstraplauncher@1.1.2/cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at oolloo.jlw.Wrapper.main(Wrapper.java:105)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: java.lang.NoSuchMethodException: net.minecraft.world.item.alchemy.PotionBrewing.addMix(net.minecraft.world.item.alchemy.Potion,net.minecraft.world.item.Item,net.minecraft.world.item.alchemy.Potion)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.lang.Class.getDeclaredMethod(Class.java:2675)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/vintagedelight@0.1.6/net.ribs.vintagedelight.mobEffects.CustomPotionBrewing.addMix(CustomPotionBrewing.java:11)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/vintagedelight@0.1.6/net.ribs.vintagedelight.VintageDelight.lambda$setup$1(VintageDelight.java:94)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at LAYER PLUGIN/fmlcore@1.20.1-47.4.0/net.minecraftforge.fml.DeferredWorkQueue.lambda$makeRunnable$2(DeferredWorkQueue.java:81)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at LAYER PLUGIN/fmlcore@1.20.1-47.4.0/net.minecraftforge.fml.DeferredWorkQueue.makeRunnable(DeferredWorkQueue.java:76)
[018月2025 17:55:10.117] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at LAYER PLUGIN/fmlcore@1.20.1-47.4.0/net.minecraftforge.fml.DeferredWorkQueue.lambda$runTasks$0(DeferredWorkQueue.java:60)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.util.concurrent.ConcurrentLinkedDeque.forEach(ConcurrentLinkedDeque.java:1650)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at LAYER PLUGIN/fmlcore@1.20.1-47.4.0/net.minecraftforge.fml.DeferredWorkQueue.runTasks(DeferredWorkQueue.java:60)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/forge@47.4.0/net.minecraftforge.fml.core.ParallelTransition.lambda$finalActivityGenerator$2(ParallelTransition.java:35)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.util.concurrent.CompletableFuture$UniApply.tryFire(CompletableFuture.java:646)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.util.concurrent.CompletableFuture$Completion.run(CompletableFuture.java:482)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.server.packs.resources.SimpleReloadInstance.m_143940_(SimpleReloadInstance.java:69)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.util.thread.BlockableEventLoop.m_6367_(BlockableEventLoop.java:156)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.util.thread.ReentrantBlockableEventLoop.m_6367_(ReentrantBlockableEventLoop.java:23)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.util.thread.BlockableEventLoop.m_7245_(BlockableEventLoop.java:130)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.util.thread.BlockableEventLoop.m_18699_(BlockableEventLoop.java:115)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.client.Minecraft.m_91383_(Minecraft.java:1106)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.client.Minecraft.m_91374_(Minecraft.java:718)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at TRANSFORMER/minecraft@1.20.1/net.minecraft.client.main.Main.main(Main.java:218)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
[018月2025 17:55:10.118] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/fmlloader@1.20.1-47.4.0/net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/fmlloader@1.20.1-47.4.0/net.minecraftforge.fml.loading.targets.CommonLaunchHandler.clientService(CommonLaunchHandler.java:99)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/fmlloader@1.20.1-47.4.0/net.minecraftforge.fml.loading.targets.CommonClientLaunchHandler.lambda$makeService$0(CommonClientLaunchHandler.java:25)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.Launcher.run(Launcher.java:108)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.Launcher.main(Launcher.java:78)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at MC-BOOTSTRAP/cpw.mods.modlauncher@10.0.9/cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at cpw.mods.bootstraplauncher@1.1.2/cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
[018月2025 17:55:10.119] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112)
[018月2025 17:55:10.120] [Render thread/INFO] [STDERR/]: [net.ribs.vintagedelight.mobEffects.CustomPotionBrewing:addMix:15]: 	at oolloo.jlw.Wrapper.main(Wrapper.java:105)
[018月2025 17:55:10.122] [Render thread/INFO] [zeta/]: Common setup: Performing initial refresh of zeta's config on thread 'Render thread'
[018月2025 17:55:10.123] [Render thread/INFO] [zeta/]: 'zeta' is enabling Zeta's piston structure resolver.
[018月2025 17:55:10.124] [Render thread/INFO] [zeta/]: zeta's config is now ready to accept filewatcher changes
[018月2025 17:55:10.124] [Render thread/INFO] [quark-zeta/]: Common setup: Performing initial refresh of quark's config on thread 'Render thread'
[018月2025 17:55:10.146] [Render thread/INFO] [quark-zeta/]: quark's config is now ready to accept filewatcher changes
[018月2025 17:55:10.189] [Worker-ResourceReload-1/INFO] [Easy NPC/]: 🗣 Register Easy NPC Client Screens ...
[018月2025 17:55:10.191] [Worker-ResourceReload-1/INFO] [xaero.map.WorldMap/]: Loading Xaero's World Map - Stage 1/2
[018月2025 17:55:10.333] [Worker-ResourceReload-2/INFO] [chloride/]: LOADED CHLORIDE
[018月2025 17:55:10.377] [Render thread/WARN] [mixin/]: @Final field shadeSwapVertices:Lit/unimi/dsi/fastutil/ints/IntList; in createbetterfps.mixins.json:ShadedBlockSbbBuilderMixin should be final
[018月2025 17:55:10.463] [Worker-ResourceReload-2/INFO] [xaero.minimap.XaeroMinimap/]: Loading Xaero's Minimap - Stage 1/2
[018月2025 17:55:11.350] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/sunflower_seeds.json' missing model for variant: 'create_bic_bit:sunflower_seeds#age=3,half=upper'
[018月2025 17:55:11.350] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/sunflower_seeds.json' missing model for variant: 'create_bic_bit:sunflower_seeds#age=3,half=lower'
[018月2025 17:55:11.350] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/sunflower_seeds.json' missing model for variant: 'create_bic_bit:sunflower_seeds#age=1,half=upper'
[018月2025 17:55:11.350] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/sunflower_seeds.json' missing model for variant: 'create_bic_bit:sunflower_seeds#age=0,half=upper'
[018月2025 17:55:11.350] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=1'
[018月2025 17:55:11.350] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=0'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=9'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=8'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=7'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=6'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=5'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=4'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=3'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=2'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=10'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=11'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=12'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=13'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=14'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/frying_oil.json' missing model for variant: 'create_bic_bit:frying_oil#level=15'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=12'
[018月2025 17:55:11.351] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=11'
[018月2025 17:55:11.352] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=10'
[018月2025 17:55:11.352] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=15'
[018月2025 17:55:11.352] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=14'
[018月2025 17:55:11.352] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=13'
[018月2025 17:55:11.352] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=6'
[018月2025 17:55:11.352] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=7'
[018月2025 17:55:11.352] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=4'
[018月2025 17:55:11.352] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=5'
[018月2025 17:55:11.352] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=8'
[018月2025 17:55:11.352] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=9'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=2'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=3'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=0'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/ketchup.json' missing model for variant: 'create_bic_bit:ketchup#level=1'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=8'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=10'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=9'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=6'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=7'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=14'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=13'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=12'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=11'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=0'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=1'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=15'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=4'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=5'
[018月2025 17:55:11.353] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=2'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/mayonnaise.json' missing model for variant: 'create_bic_bit:mayonnaise#level=3'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=13'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=12'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=11'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=10'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=4'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=3'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=2'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=1'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=0'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=9'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=8'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=7'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=6'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=5'
[018月2025 17:55:11.354] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=15'
[018月2025 17:55:11.355] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'create_bic_bit:blockstates/curdled_milk.json' missing model for variant: 'create_bic_bit:curdled_milk#level=14'
[018月2025 17:55:11.648] [Worker-ResourceReload-1/WARN] [xaero.hud.minimap.MinimapLogs/]: io exception while checking patreon: Online mod data expired! Date: Wed Jul 30 14:25:43 CST 2025
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=11'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=10'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=13'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=12'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=15'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=14'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=7'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=8'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=9'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=0'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=1'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=2'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=3'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=4'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=5'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/blood_fluid_block.json' missing model for variant: 'butchercraft:blood_fluid_block#level=6'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=2'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=3'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=4'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=5'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=0'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=1'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=10'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=6'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=7'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=8'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=9'
[018月2025 17:55:12.502] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=15'
[018月2025 17:55:12.505] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=11'
[018月2025 17:55:12.505] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=12'
[018月2025 17:55:12.505] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=13'
[018月2025 17:55:12.505] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head.json' missing model for variant: 'butchercraft:cow_head#rotation=14'
[018月2025 17:55:12.505] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head_wall.json' missing model for variant: 'butchercraft:cow_head_wall#facing=west'
[018月2025 17:55:12.505] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head_wall.json' missing model for variant: 'butchercraft:cow_head_wall#facing=east'
[018月2025 17:55:12.505] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head_wall.json' missing model for variant: 'butchercraft:cow_head_wall#facing=north'
[018月2025 17:55:12.505] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_head_wall.json' missing model for variant: 'butchercraft:cow_head_wall#facing=south'
[018月2025 17:55:12.505] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=9'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=8'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=12'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=11'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=10'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=15'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=14'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=13'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=3'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=2'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=1'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=0'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=7'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=6'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=5'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head.json' missing model for variant: 'butchercraft:cow_skull_head#rotation=4'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head_wall.json' missing model for variant: 'butchercraft:cow_skull_head_wall#facing=south'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head_wall.json' missing model for variant: 'butchercraft:cow_skull_head_wall#facing=east'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head_wall.json' missing model for variant: 'butchercraft:cow_skull_head_wall#facing=west'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/cow_skull_head_wall.json' missing model for variant: 'butchercraft:cow_skull_head_wall#facing=north'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=15'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=13'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=14'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=11'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=12'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=10'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=9'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=8'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=7'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=6'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=5'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=4'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=3'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=2'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=1'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head.json' missing model for variant: 'butchercraft:chicken_head#rotation=0'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head_wall.json' missing model for variant: 'butchercraft:chicken_head_wall#facing=south'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head_wall.json' missing model for variant: 'butchercraft:chicken_head_wall#facing=east'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head_wall.json' missing model for variant: 'butchercraft:chicken_head_wall#facing=north'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_head_wall.json' missing model for variant: 'butchercraft:chicken_head_wall#facing=west'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=15'
[018月2025 17:55:12.506] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=13'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=14'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=11'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=12'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=4'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=5'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=2'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=3'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=0'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=1'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=10'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=8'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=9'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=6'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head.json' missing model for variant: 'butchercraft:chicken_skull_head#rotation=7'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head_wall.json' missing model for variant: 'butchercraft:chicken_skull_head_wall#facing=east'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head_wall.json' missing model for variant: 'butchercraft:chicken_skull_head_wall#facing=north'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head_wall.json' missing model for variant: 'butchercraft:chicken_skull_head_wall#facing=south'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/chicken_skull_head_wall.json' missing model for variant: 'butchercraft:chicken_skull_head_wall#facing=west'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=9'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=8'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=7'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=6'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=10'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=13'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=14'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=11'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=12'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=15'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=1'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=0'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=5'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=4'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=3'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head.json' missing model for variant: 'butchercraft:goat_head#rotation=2'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head_wall.json' missing model for variant: 'butchercraft:goat_head_wall#facing=west'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head_wall.json' missing model for variant: 'butchercraft:goat_head_wall#facing=south'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head_wall.json' missing model for variant: 'butchercraft:goat_head_wall#facing=east'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_head_wall.json' missing model for variant: 'butchercraft:goat_head_wall#facing=north'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=9'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=8'
[018月2025 17:55:12.507] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=11'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=12'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=10'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=15'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=13'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=14'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=3'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=2'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=1'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=0'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=7'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=6'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=5'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head.json' missing model for variant: 'butchercraft:goat_skull_head#rotation=4'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head_wall.json' missing model for variant: 'butchercraft:goat_skull_head_wall#facing=west'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head_wall.json' missing model for variant: 'butchercraft:goat_skull_head_wall#facing=south'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head_wall.json' missing model for variant: 'butchercraft:goat_skull_head_wall#facing=north'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/goat_skull_head_wall.json' missing model for variant: 'butchercraft:goat_skull_head_wall#facing=east'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=6'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=5'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=4'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=3'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=9'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=8'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=7'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=11'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=10'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=15'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=14'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=13'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=12'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=2'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=1'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head.json' missing model for variant: 'butchercraft:pig_head#rotation=0'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head_wall.json' missing model for variant: 'butchercraft:pig_head_wall#facing=west'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head_wall.json' missing model for variant: 'butchercraft:pig_head_wall#facing=north'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head_wall.json' missing model for variant: 'butchercraft:pig_head_wall#facing=south'
[018月2025 17:55:12.508] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_head_wall.json' missing model for variant: 'butchercraft:pig_head_wall#facing=east'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=9'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=6'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=5'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=8'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=7'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=14'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=15'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=10'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=11'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=12'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=13'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=2'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=1'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=4'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=3'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head.json' missing model for variant: 'butchercraft:pig_skull_head#rotation=0'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head_wall.json' missing model for variant: 'butchercraft:pig_skull_head_wall#facing=south'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head_wall.json' missing model for variant: 'butchercraft:pig_skull_head_wall#facing=east'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head_wall.json' missing model for variant: 'butchercraft:pig_skull_head_wall#facing=north'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/pig_skull_head_wall.json' missing model for variant: 'butchercraft:pig_skull_head_wall#facing=west'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=11'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=12'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=13'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=14'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=15'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=6'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=7'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=8'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=9'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=10'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=0'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=1'
[018月2025 17:55:12.509] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=2'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=3'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=4'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head.json' missing model for variant: 'butchercraft:sheep_head#rotation=5'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head_wall.json' missing model for variant: 'butchercraft:sheep_head_wall#facing=east'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head_wall.json' missing model for variant: 'butchercraft:sheep_head_wall#facing=north'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head_wall.json' missing model for variant: 'butchercraft:sheep_head_wall#facing=south'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_head_wall.json' missing model for variant: 'butchercraft:sheep_head_wall#facing=west'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=4'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=5'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=6'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=7'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=0'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=1'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=2'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=3'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=13'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=14'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=15'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=10'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=11'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=12'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=8'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head.json' missing model for variant: 'butchercraft:sheep_skull_head#rotation=9'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head_wall.json' missing model for variant: 'butchercraft:sheep_skull_head_wall#facing=west'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head_wall.json' missing model for variant: 'butchercraft:sheep_skull_head_wall#facing=north'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head_wall.json' missing model for variant: 'butchercraft:sheep_skull_head_wall#facing=east'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/sheep_skull_head_wall.json' missing model for variant: 'butchercraft:sheep_skull_head_wall#facing=south'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=8'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=9'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=10'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=11'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=12'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=0'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=1'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=2'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=3'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=4'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=5'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=6'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=7'
[018月2025 17:55:12.510] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=13'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=14'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head.json' missing model for variant: 'butchercraft:rabbit_brown_head#rotation=15'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head_wall.json' missing model for variant: 'butchercraft:rabbit_brown_head_wall#facing=west'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head_wall.json' missing model for variant: 'butchercraft:rabbit_brown_head_wall#facing=north'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head_wall.json' missing model for variant: 'butchercraft:rabbit_brown_head_wall#facing=east'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_brown_head_wall.json' missing model for variant: 'butchercraft:rabbit_brown_head_wall#facing=south'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=6'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=5'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=4'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=3'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=2'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=1'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=0'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=11'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=10'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=9'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=8'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=7'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=15'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=14'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=13'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head.json' missing model for variant: 'butchercraft:rabbit_black_head#rotation=12'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head_wall.json' missing model for variant: 'butchercraft:rabbit_black_head_wall#facing=west'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head_wall.json' missing model for variant: 'butchercraft:rabbit_black_head_wall#facing=south'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head_wall.json' missing model for variant: 'butchercraft:rabbit_black_head_wall#facing=east'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_black_head_wall.json' missing model for variant: 'butchercraft:rabbit_black_head_wall#facing=north'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=15'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=13'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=14'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=11'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=12'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=10'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=3'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=2'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=1'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=0'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=9'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=8'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=7'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=6'
[018月2025 17:55:12.511] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=5'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head.json' missing model for variant: 'butchercraft:rabbit_gold_head#rotation=4'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head_wall.json' missing model for variant: 'butchercraft:rabbit_gold_head_wall#facing=south'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head_wall.json' missing model for variant: 'butchercraft:rabbit_gold_head_wall#facing=east'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head_wall.json' missing model for variant: 'butchercraft:rabbit_gold_head_wall#facing=north'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_gold_head_wall.json' missing model for variant: 'butchercraft:rabbit_gold_head_wall#facing=west'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=15'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=12'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=11'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=14'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=13'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=10'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=8'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=9'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=6'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=7'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=4'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=5'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=2'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=3'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=0'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head.json' missing model for variant: 'butchercraft:rabbit_salt_head#rotation=1'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head_wall.json' missing model for variant: 'butchercraft:rabbit_salt_head_wall#facing=east'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head_wall.json' missing model for variant: 'butchercraft:rabbit_salt_head_wall#facing=south'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head_wall.json' missing model for variant: 'butchercraft:rabbit_salt_head_wall#facing=north'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_salt_head_wall.json' missing model for variant: 'butchercraft:rabbit_salt_head_wall#facing=west'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=15'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=12'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=11'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=14'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=13'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=4'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=5'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=2'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=3'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=0'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=1'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=10'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=8'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=9'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=6'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head.json' missing model for variant: 'butchercraft:rabbit_splotched_head#rotation=7'
[018月2025 17:55:12.512] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head_wall.json' missing model for variant: 'butchercraft:rabbit_splotched_head_wall#facing=west'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head_wall.json' missing model for variant: 'butchercraft:rabbit_splotched_head_wall#facing=east'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head_wall.json' missing model for variant: 'butchercraft:rabbit_splotched_head_wall#facing=south'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_splotched_head_wall.json' missing model for variant: 'butchercraft:rabbit_splotched_head_wall#facing=north'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=3'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=4'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=1'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=2'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=0'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=9'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=7'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=8'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=5'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=6'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=14'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=15'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=12'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=13'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=10'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head.json' missing model for variant: 'butchercraft:rabbit_white_head#rotation=11'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head_wall.json' missing model for variant: 'butchercraft:rabbit_white_head_wall#facing=south'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head_wall.json' missing model for variant: 'butchercraft:rabbit_white_head_wall#facing=north'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head_wall.json' missing model for variant: 'butchercraft:rabbit_white_head_wall#facing=west'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_white_head_wall.json' missing model for variant: 'butchercraft:rabbit_white_head_wall#facing=east'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=13'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=12'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=11'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=10'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=9'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=15'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=14'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=0'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=8'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=7'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=6'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=5'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=4'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=3'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=2'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head.json' missing model for variant: 'butchercraft:rabbit_skull_head#rotation=1'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head_wall.json' missing model for variant: 'butchercraft:rabbit_skull_head_wall#facing=west'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head_wall.json' missing model for variant: 'butchercraft:rabbit_skull_head_wall#facing=south'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head_wall.json' missing model for variant: 'butchercraft:rabbit_skull_head_wall#facing=north'
[018月2025 17:55:12.513] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Exception loading blockstate definition: 'butchercraft:blockstates/rabbit_skull_head_wall.json' missing model for variant: 'butchercraft:rabbit_skull_head_wall#facing=east'
[018月2025 17:55:12.545] [Worker-ResourceReload-2/WARN] [net.minecraft.client.resources.model.ModelBakery/]: Unable to load model: 'vintagedelight:vinegar_jar#inventory' referenced from: vintagedelight:vinegar_jar#inventory: java.io.FileNotFoundException: vintagedelight:models/item/vinegar_jar.json
[018月2025 17:55:12.643] [Worker-ResourceReload-1/ERROR] [xaero.map.WorldMap/]: io exception while checking versions: Online mod data expired! Date: Wed Jul 30 14:25:43 CST 2025
[018月2025 17:55:12.714] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC Client starting Events ...
[018月2025 17:55:12.715] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC Client data folders ...
[018月2025 17:55:12.715] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC skin data folders ...
[018月2025 17:55:12.715] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC custom skin data ...
[018月2025 17:55:17.161] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\allay\allay_template.png already exists, skipping copy!
[018月2025 17:55:19.023] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\cat\cat_template.png already exists, skipping copy!
[018月2025 17:55:20.813] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\chicken\chicken_template.png already exists, skipping copy!
[018月2025 17:55:22.637] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\fairy\fairy_template.png already exists, skipping copy!
[018月2025 17:55:24.436] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\humanoid\humanoid_template.png already exists, skipping copy!
[018月2025 17:55:25.807] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\humanoid_slim\humanoid_slim_template.png already exists, skipping copy!
[018月2025 17:55:26.931] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\illager\illager_template.png already exists, skipping copy!
[018月2025 17:55:27.989] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\iron_golem\iron_golem_template.png already exists, skipping copy!
[018月2025 17:55:29.076] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\orc\orc_template.png already exists, skipping copy!
[018月2025 17:55:30.159] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\pig\pig_template.png already exists, skipping copy!
[018月2025 17:55:30.357] [Worker-ResourceReload-14/WARN] [net.minecraft.client.renderer.texture.SpriteContents/]: Invalid frame index on sprite kaleidoscope_cookery:block/suspicious_stir_fry frame 1: 1
[018月2025 17:55:30.357] [Worker-ResourceReload-14/WARN] [net.minecraft.client.renderer.texture.SpriteContents/]: Invalid frame index on sprite kaleidoscope_cookery:block/suspicious_stir_fry frame 10: 1
[018月2025 17:55:30.357] [Worker-ResourceReload-14/WARN] [net.minecraft.client.renderer.texture.SpriteContents/]: Invalid frame index on sprite kaleidoscope_cookery:block/suspicious_stir_fry frame 23: 2
[018月2025 17:55:30.357] [Worker-ResourceReload-14/WARN] [net.minecraft.client.renderer.texture.SpriteContents/]: Invalid frame index on sprite kaleidoscope_cookery:block/suspicious_stir_fry frame 34: 3
[018月2025 17:55:30.357] [Worker-ResourceReload-14/WARN] [net.minecraft.client.renderer.texture.SpriteContents/]: Invalid frame index on sprite kaleidoscope_cookery:block/suspicious_stir_fry frame 41: 2
[018月2025 17:55:30.367] [Worker-ResourceReload-12/WARN] [net.minecraft.client.renderer.texture.SpriteContents/]: Invalid frame index on sprite garnished:item/volatile_dust frame 6: 6
[018月2025 17:55:30.392] [Worker-ResourceReload-8/WARN] [net.minecraft.client.renderer.texture.SpriteLoader/]: Texture chinese_cuisine:item/sweet_sour_ribs with size 526x358 limits mip level from 4 to 1
[018月2025 17:55:30.392] [Worker-ResourceReload-8/WARN] [net.minecraft.client.renderer.texture.SpriteLoader/]: Texture chinese_cuisine:item/bread_ice_cream with size 1205x1205 limits mip level from 1 to 0
[018月2025 17:55:30.689] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\piglin\piglin_template.png already exists, skipping copy!
[018月2025 17:55:31.129] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\wolf\wolf_template.png already exists, skipping copy!
[018月2025 17:55:31.715] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\skeleton\skeleton_template.png already exists, skipping copy!
[018月2025 17:55:32.070] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\villager\villager_template.png already exists, skipping copy!
[018月2025 17:55:32.070] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\villager\villager_without_hat_template.png already exists, skipping copy!
[018月2025 17:55:32.439] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\zombie\zombie_template.png already exists, skipping copy!
[018月2025 17:55:32.589] [Worker-ResourceReload-4/WARN] [net.minecraft.client.resources.model.ModelManager/]: Missing textures in model create_connected:fan_enriched_catalyst#:
    minecraft:textures/atlas/blocks.png:createnuclear:block/enriched_soul_soil
    minecraft:textures/atlas/blocks.png:createnuclear:block/enriching/campfire/enriching_flame
[018月2025 17:55:32.589] [Worker-ResourceReload-4/WARN] [net.minecraft.client.resources.model.ModelManager/]: Missing textures in model chinese_cuisine:counter#:
    minecraft:textures/atlas/blocks.png:chinese_cuisine:block/counter
[018月2025 17:55:32.589] [Worker-ResourceReload-4/WARN] [net.minecraft.client.resources.model.ModelManager/]: Missing textures in model create_connected:fan_ending_catalyst_dragons_breath#inventory:
    minecraft:textures/atlas/blocks.png:create_dragons_plus:fluid/dragon_breath_flow
[018月2025 17:55:32.589] [Worker-ResourceReload-4/WARN] [net.minecraft.client.resources.model.ModelManager/]: Missing textures in model butchercraft:meathook/rabbit_head:
    minecraft:textures/atlas/blocks.png:minecraft:rabbit
[018月2025 17:55:32.589] [Worker-ResourceReload-4/WARN] [net.minecraft.client.resources.model.ModelManager/]: Missing textures in model chinese_cuisine:counter#inventory:
    minecraft:textures/atlas/blocks.png:chinese_cuisine:block/counter
[018月2025 17:55:32.590] [Worker-ResourceReload-4/WARN] [net.minecraft.client.resources.model.ModelManager/]: Missing textures in model create_connected:fan_ending_catalyst_dragons_breath#:
    minecraft:textures/atlas/blocks.png:create_dragons_plus:fluid/dragon_breath_flow
[018月2025 17:55:32.590] [Worker-ResourceReload-4/WARN] [net.minecraft.client.resources.model.ModelManager/]: Missing textures in model createaddition:small_light_connector#facing=west,mode=push,powered=true,rotation=x_clockwise_180,variant=default:
    minecraft:textures/atlas/blocks.png:create:block/chute_block
[018月2025 17:55:32.590] [Worker-ResourceReload-4/WARN] [net.minecraft.client.resources.model.ModelManager/]: Missing textures in model create_connected:fan_enriched_catalyst#inventory:
    minecraft:textures/atlas/blocks.png:createnuclear:block/enriched_soul_soil
    minecraft:textures/atlas/blocks.png:createnuclear:block/enriching/campfire/enriching_flame
[018月2025 17:55:32.590] [Worker-ResourceReload-4/WARN] [net.minecraft.client.resources.model.ModelManager/]: Missing textures in model create_bic_bit:mechanical_fryer#inventory:
    minecraft:textures/atlas/blocks.png:minecraft:block/pasted
[018月2025 17:55:32.752] [Render thread/WARN] [Easy NPC/]: Skin model template file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\zombie_villager\zombie_villager_template.png already exists, skipping copy!
[018月2025 17:55:32.752] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC custom skins from C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin ...
[018月2025 17:55:32.762] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\allay\allay_template.png with image NativeImage[RGBA 32x32@2840467275776S] for texture TextureModelKey{uuid=c3ed6254-b84e-3963-a2c0-a5a81d201a62, skinModel=ALLAY, subType='ALLAY, resourceName='allay_template.png'} with minecraft:dynamic/easy_npc_client_texture_allay_c3ed6254-b84e-3963-a2c0-a5a81d201a62_1.
[018月2025 17:55:32.764] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\cat\cat_template.png with image NativeImage[RGBA 64x32@2840466554880S] for texture TextureModelKey{uuid=dd9e7b53-35bf-3ccc-86d5-8494402e394b, skinModel=CAT, subType='CAT, resourceName='cat_template.png'} with minecraft:dynamic/easy_npc_client_texture_cat_dd9e7b53-35bf-3ccc-86d5-8494402e394b_1.
[018月2025 17:55:32.765] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\chicken\chicken_template.png with image NativeImage[RGBA 64x32@2840466620416S] for texture TextureModelKey{uuid=3b5e083d-8b48-33d6-8f3c-85ab1ebb9b84, skinModel=CHICKEN, subType='CHICKEN, resourceName='chicken_template.png'} with minecraft:dynamic/easy_npc_client_texture_chicken_3b5e083d-8b48-33d6-8f3c-85ab1ebb9b84_1.
[018月2025 17:55:32.769] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\fairy\fairy_template.png with image NativeImage[RGBA 128x128@2840469507456S] for texture TextureModelKey{uuid=a7e321fe-1535-3095-aa13-0a79244178f2, skinModel=FAIRY, subType='FAIRY, resourceName='fairy_template.png'} with minecraft:dynamic/easy_npc_client_texture_fairy_a7e321fe-1535-3095-aa13-0a79244178f2_1.
[018月2025 17:55:32.772] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\humanoid\humanoid_template.png with image NativeImage[RGBA 64x64@2840475142592S] for texture TextureModelKey{uuid=0a2b92c8-d54b-36dc-9352-7e054e081f0c, skinModel=HUMANOID, subType='HUMANOID, resourceName='humanoid_template.png'} with minecraft:dynamic/easy_npc_client_texture_humanoid_0a2b92c8-d54b-36dc-9352-7e054e081f0c_1.
[018月2025 17:55:32.775] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\humanoid_slim\humanoid_slim_template.png with image NativeImage[RGBA 64x64@2837522482048S] for texture TextureModelKey{uuid=8b30b738-461f-30c4-8cd2-10a94b202cdf, skinModel=HUMANOID_SLIM, subType='HUMANOID_SLIM, resourceName='humanoid_slim_template.png'} with minecraft:dynamic/easy_npc_client_texture_humanoid_slim_8b30b738-461f-30c4-8cd2-10a94b202cdf_1.
[018月2025 17:55:32.778] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\illager\illager_template.png with image NativeImage[RGBA 64x64@2837522547520S] for texture TextureModelKey{uuid=617322a7-89fc-358d-8c3d-bb3e4da80ece, skinModel=ILLAGER, subType='ILLAGER, resourceName='illager_template.png'} with minecraft:dynamic/easy_npc_client_texture_illager_617322a7-89fc-358d-8c3d-bb3e4da80ece_1.
[018月2025 17:55:32.782] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\iron_golem\iron_golem_template.png with image NativeImage[RGBA 128x128@2840469637824S] for texture TextureModelKey{uuid=ff8e17aa-51b0-35a3-a60d-7d789c27e837, skinModel=IRON_GOLEM, subType='IRON_GOLEM, resourceName='iron_golem_template.png'} with minecraft:dynamic/easy_npc_client_texture_iron_golem_ff8e17aa-51b0-35a3-a60d-7d789c27e837_1.
[018月2025 17:55:32.786] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\orc\orc_template.png with image NativeImage[RGBA 128x128@2837522810176S] for texture TextureModelKey{uuid=d1a5421d-4241-3209-ae8a-d578a7bfe15f, skinModel=ORC, subType='ORC, resourceName='orc_template.png'} with minecraft:dynamic/easy_npc_client_texture_orc_d1a5421d-4241-3209-ae8a-d578a7bfe15f_1.
[018月2025 17:55:32.789] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\pig\pig_template.png with image NativeImage[RGBA 64x32@2840466685952S] for texture TextureModelKey{uuid=1ad7df8e-fd8e-37ba-aeae-c7fa18c49a1d, skinModel=PIG, subType='PIG, resourceName='pig_template.png'} with minecraft:dynamic/easy_npc_client_texture_pig_1ad7df8e-fd8e-37ba-aeae-c7fa18c49a1d_1.
[018月2025 17:55:32.791] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\piglin\piglin_template.png with image NativeImage[RGBA 64x64@2837522745024S] for texture TextureModelKey{uuid=53afd629-8964-35a8-bb66-7d64359d6eee, skinModel=PIGLIN, subType='PIGLIN, resourceName='piglin_template.png'} with minecraft:dynamic/easy_npc_client_texture_piglin_53afd629-8964-35a8-bb66-7d64359d6eee_1.
[018月2025 17:55:32.794] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\wolf\wolf_template.png with image NativeImage[RGBA 64x32@2840466751488S] for texture TextureModelKey{uuid=fc13cd6c-**************-346b87daeefb, skinModel=WOLF, subType='WOLF, resourceName='wolf_template.png'} with minecraft:dynamic/easy_npc_client_texture_wolf_fc13cd6c-**************-346b87daeefb_1.
[018月2025 17:55:32.797] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\skeleton\skeleton_template.png with image NativeImage[RGBA 64x32@2840466817024S] for texture TextureModelKey{uuid=99b88725-4c39-3e23-8b8c-ed8cd073aeb7, skinModel=SKELETON, subType='SKELETON, resourceName='skeleton_template.png'} with minecraft:dynamic/easy_npc_client_texture_skeleton_99b88725-4c39-3e23-8b8c-ed8cd073aeb7_1.
[018月2025 17:55:32.800] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\villager\villager_template.png with image NativeImage[RGBA 64x64@2837522943936S] for texture TextureModelKey{uuid=0e515082-1396-3a68-8959-79846eeb0d96, skinModel=VILLAGER, subType='VILLAGER, resourceName='villager_template.png'} with minecraft:dynamic/easy_npc_client_texture_villager_0e515082-1396-3a68-8959-79846eeb0d96_1.
[018月2025 17:55:32.802] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\villager\villager_without_hat_template.png with image NativeImage[RGBA 64x64@2837523006336S] for texture TextureModelKey{uuid=44c87ffb-a92c-336b-b3b4-bee539e9d303, skinModel=VILLAGER, subType='VILLAGER, resourceName='villager_without_hat_template.png'} with minecraft:dynamic/easy_npc_client_texture_villager_44c87ffb-a92c-336b-b3b4-bee539e9d303_1.
[018月2025 17:55:32.805] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\zombie\zombie_template.png with image NativeImage[RGBA 64x64@2837523072320S] for texture TextureModelKey{uuid=f14e7212-e76d-376d-ba9e-7776d0d70850, skinModel=ZOMBIE, subType='ZOMBIE, resourceName='zombie_template.png'} with minecraft:dynamic/easy_npc_client_texture_zombie_f14e7212-e76d-376d-ba9e-7776d0d70850_1.
[018月2025 17:55:32.807] [Render thread/INFO] [Easy NPC/]: [Texture Manager] Registered file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\skin\zombie_villager\zombie_villager_template.png with image NativeImage[RGBA 64x64@2837523137536S] for texture TextureModelKey{uuid=70d6fde8-652d-36e1-a005-45e67a679eda, skinModel=ZOMBIE_VILLAGER, subType='ZOMBIE_VILLAGER, resourceName='zombie_villager_template.png'} with minecraft:dynamic/easy_npc_client_texture_zombie_villager_70d6fde8-652d-36e1-a005-45e67a679eda_1.
[018月2025 17:55:32.808] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC player skin data ...
[018月2025 17:55:32.812] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC remote skin data ...
[018月2025 17:55:32.831] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC preset data folders ...
[018月2025 17:55:32.832] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC custom preset data ...
[018月2025 17:55:32.848] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC Register Entity Type Manager ...
[018月2025 17:55:32.850] [Render thread/INFO] [Easy NPC/]: [Entity Type Manager] Found 67 supported, 50 unsupported and 31 unknown entity types.
[018月2025 17:55:32.850] [Render thread/WARN] [net.minecraftforge.fml.DeferredWorkQueue/LOADING]: Mod 'easy_npc' took 20.14 s to run a deferred task.
[018月2025 17:55:32.850] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Reloading config/lightmanscurrency-client.txt
[018月2025 17:55:32.913] [Render thread/INFO] [xaero.minimap.XaeroMinimap/]: Loading Xaero's Minimap - Stage 2/2
[018月2025 17:55:34.262] [Render thread/WARN] [xaero.hud.minimap.MinimapLogs/]: io exception while checking versions: Online mod data expired! Date: Wed Jul 30 14:25:43 CST 2025
[018月2025 17:55:34.264] [Render thread/INFO] [xaero.hud.minimap.MinimapLogs/]: Registered player tracker system: minimap_synced
[018月2025 17:55:34.265] [Render thread/INFO] [xaero.hud.minimap.MinimapLogs/]: Xaero's Minimap: World Map found!
[018月2025 17:55:34.265] [Render thread/INFO] [xaero.hud.minimap.MinimapLogs/]: No Optifine!
[018月2025 17:55:34.265] [Render thread/INFO] [xaero.hud.minimap.MinimapLogs/]: Xaero's Minimap: No Vivecraft!
[018月2025 17:55:34.265] [Render thread/INFO] [xaero.hud.minimap.MinimapLogs/]: Xaero's Minimap: Iris found!
[018月2025 17:55:34.265] [Render thread/WARN] [net.minecraftforge.fml.DeferredWorkQueue/LOADING]: Mod 'xaerominimap' took 1.352 s to run a deferred task.
[018月2025 17:55:34.286] [Render thread/INFO] [xaero.map.WorldMap/]: Loading Xaero's World Map - Stage 2/2
[018月2025 17:55:34.292] [Render thread/INFO] [xaero.map.WorldMap/]: New world map region cache hash code: 1701371195
[018月2025 17:55:34.293] [Render thread/INFO] [xaero.map.WorldMap/]: Registered player tracker system: map_synced
[018月2025 17:55:34.295] [Render thread/INFO] [xaero.map.WorldMap/]: Xaero's WorldMap Mod: Xaero's minimap found!
[018月2025 17:55:34.298] [Render thread/INFO] [xaero.map.WorldMap/]: Registered player tracker system: minimap_synced
[018月2025 17:55:34.298] [Render thread/INFO] [xaero.map.WorldMap/]: No Optifine!
[018月2025 17:55:34.298] [Render thread/INFO] [xaero.map.WorldMap/]: Xaero's World Map: No Vivecraft!
[018月2025 17:55:34.298] [Render thread/INFO] [xaero.map.WorldMap/]: Xaero's World Map: Iris found!
[018月2025 17:55:34.462] [Worker-ResourceReload-2/INFO] [STDOUT/]: [com.mrh0.createaddition.CreateAddition:postInit:136]: Create Crafts & Additions Initialized!
[018月2025 17:55:34.509] [Worker-ResourceReload-11/INFO] [Jade/]: Start loading plugin at net.blay09.mods.balm.forge.compat.hudinfo.ForgeJadeModCompat
[018月2025 17:55:34.526] [Worker-ResourceReload-11/INFO] [Jade/]: Start loading plugin at com.oierbravo.createsifter.compat.jade.SifterPlugin
[018月2025 17:55:34.527] [Worker-ResourceReload-11/INFO] [Jade/]: Start loading plugin at snownee.jade.addon.vanilla.VanillaPlugin
[018月2025 17:55:34.554] [Worker-ResourceReload-11/INFO] [Jade/]: Start loading plugin at snownee.jade.addon.universal.UniversalPlugin
[018月2025 17:55:34.560] [Worker-ResourceReload-11/INFO] [Jade/]: Start loading plugin at snownee.jade.addon.core.CorePlugin
[018月2025 17:55:34.565] [Worker-ResourceReload-11/INFO] [Jade/]: Start loading plugin at com.mao.barbequesdelight.compat.jade.JadeCompat
[018月2025 17:55:34.595] [Worker-ResourceReload-10/INFO] [PonderIndex/]: Registering Ponder Scenes took 136.8 ms
[018月2025 17:55:34.606] [Worker-ResourceReload-10/INFO] [PonderIndex/]: Registering Ponder Tags took 11.47 ms
[018月2025 17:55:34.630] [Render thread/INFO] [Configured/]: Successfully loaded config provider: com.mrcrayfish.configured.impl.forge.ForgeConfigProvider
[018月2025 17:55:34.630] [Render thread/INFO] [Configured/]: Successfully loaded config provider: com.mrcrayfish.configured.impl.framework.FrameworkConfigProvider
[018月2025 17:55:34.630] [Render thread/INFO] [Configured/]: Successfully loaded config provider: com.mrcrayfish.configured.impl.jei.JeiConfigProvider
[018月2025 17:55:34.630] [Render thread/INFO] [Configured/]: Creating config GUI factories...
[018月2025 17:55:34.635] [Render thread/INFO] [Configured/]: Registering config factory for mod trashslot. Found 1 config(s)
[018月2025 17:55:34.636] [Render thread/INFO] [Configured/]: Registering config factory for mod jei. Found 5 config(s)
[018月2025 17:55:34.636] [Render thread/INFO] [Configured/]: Registering config factory for mod sophisticatedcore. Found 2 config(s)
[018月2025 17:55:34.637] [Render thread/INFO] [Configured/]: Registering config factory for mod create_central_kitchen. Found 1 config(s)
[018月2025 17:55:34.637] [Render thread/INFO] [Configured/]: Registering config factory for mod sophisticatedbackpacks. Found 2 config(s)
[018月2025 17:55:34.637] [Render thread/INFO] [Configured/]: Registering config factory for mod jeed. Found 1 config(s)
[018月2025 17:55:34.637] [Render thread/INFO] [Configured/]: Registering config factory for mod balm. Found 2 config(s)
[018月2025 17:55:34.637] [Render thread/INFO] [Configured/]: Registering config factory for mod carryon. Found 2 config(s)
[018月2025 17:55:34.637] [Render thread/INFO] [Configured/]: Registering config factory for mod jeresources. Found 1 config(s)
[018月2025 17:55:34.638] [Render thread/INFO] [Configured/]: Registering config factory for mod forge. Found 2 config(s)
[018月2025 17:55:34.638] [Render thread/INFO] [Configured/]: Registering config factory for mod craftingtweaks. Found 1 config(s)
[018月2025 17:55:34.639] [Render thread/INFO] [Configured/]: Registering config factory for mod create_connected. Found 2 config(s)
[018月2025 17:55:34.639] [Render thread/INFO] [Configured/]: Registering config factory for mod farmersdelight. Found 2 config(s)
[018月2025 17:55:34.639] [Render thread/INFO] [Configured/]: Registering config factory for mod createsifter. Found 2 config(s)
[018月2025 17:55:34.639] [Render thread/INFO] [Configured/]: Registering config factory for mod flywheel. Found 1 config(s)
[018月2025 17:55:34.639] [Render thread/INFO] [Configured/]: Registering config factory for mod curios. Found 3 config(s)
[018月2025 17:55:34.640] [Render thread/INFO] [Configured/]: Registering config factory for mod zeta. Found 1 config(s)
[018月2025 17:55:34.640] [Render thread/INFO] [Configured/]: Registering config factory for mod jecharacters. Found 1 config(s)
[018月2025 17:55:34.640] [Render thread/INFO] [Configured/]: Registering config factory for mod butchercraft. Found 1 config(s)
[018月2025 17:55:34.640] [Render thread/INFO] [Configured/]: Registering config factory for mod l2library. Found 2 config(s)
[018月2025 17:55:34.640] [Render thread/INFO] [Configured/]: Registering config factory for mod toms_storage. Found 2 config(s)
[018月2025 17:55:34.640] [Render thread/INFO] [Configured/]: Registering config factory for mod cuisinedelight. Found 2 config(s)
[018月2025 17:55:34.640] [Render thread/INFO] [Configured/]: Registering config factory for mod createaddition. Found 1 config(s)
[018月2025 17:55:34.640] [Render thread/INFO] [Configured/]: Registering config factory for mod betteradvancements. Found 1 config(s)
[018月2025 17:55:35.270] [Render thread/WARN] [net.minecraft.client.resources.language.ClientLanguage/]: Skipped language file: minecells:lang/zh_cn.json (com.google.gson.JsonSyntaxException: Expected chat.minecells.wipe_try to be a string, was an array (["\n...n"]))
[018月2025 17:55:35.474] [Render thread/WARN] [net.minecraft.client.resources.language.ClientLanguage/]: Skipped language file: isometric-renders:lang/zh_cn.json (com.google.gson.JsonSyntaxException: Expected gui.isometric-renders.hud.area_selection.clear_hint to be a string, was an array ([{"t..."}]))
[018月2025 17:55:35.508] [Render thread/WARN] [net.minecraft.client.resources.language.ClientLanguage/]: Skipped language file: mythicmetals:lang/zh_cn.json (com.google.gson.JsonSyntaxException: Expected tooltip.mythril_drill.refuel to be a string, was an array ([{"t..."}]))
[018月2025 17:55:36.353] [Render thread/WARN] [net.minecraft.client.sounds.SoundEngine/]: Missing sound for event: minecraft:item.goat_horn.play
[018月2025 17:55:36.353] [Render thread/WARN] [net.minecraft.client.sounds.SoundEngine/]: Missing sound for event: minecraft:entity.goat.screaming.horn_break
[018月2025 17:55:37.050] [Render thread/INFO] [com.mojang.blaze3d.audio.Library/]: OpenAL initialized on device OpenAL Soft on 扬声器 (Realtek(R) Audio)
[018月2025 17:55:37.051] [Render thread/INFO] [net.minecraft.client.sounds.SoundEngine/SOUNDS]: Sound engine started
[018月2025 17:55:37.193] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 4096x2048x4 minecraft:textures/atlas/blocks.png-atlas
[018月2025 17:55:37.272] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x512x4 minecraft:textures/atlas/signs.png-atlas
[018月2025 17:55:37.274] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[018月2025 17:55:37.275] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[018月2025 17:55:37.276] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 2048x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[018月2025 17:55:37.285] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 1024x1024x4 minecraft:textures/atlas/chest.png-atlas
[018月2025 17:55:37.289] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[018月2025 17:55:37.289] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[018月2025 17:55:37.290] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[018月2025 17:55:37.500] [Render thread/WARN] [net.minecraft.client.renderer.ShaderInstance/]: Shader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
[018月2025 17:55:37.567] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x256x0 minecraft:textures/atlas/particles.png-atlas
[018月2025 17:55:37.568] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x0 minecraft:textures/atlas/paintings.png-atlas
[018月2025 17:55:37.568] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x128x0 minecraft:textures/atlas/mob_effects.png-atlas
[018月2025 17:55:37.569] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x0 jei:textures/atlas/gui.png-atlas
[018月2025 17:55:37.573] [Render thread/INFO] [xaero.map.WorldMap/]: Successfully reloaded the world map shaders!
[018月2025 17:55:38.146] [Render thread/INFO] [flywheel/backend/shaders/]: Loaded 82 shader sources in 556.079 ms
[018月2025 17:55:38.783] [Render thread/INFO] [com.simibubi.create.Create/]: Loaded 56 train hat configurations.
[018月2025 17:55:38.788] [Render thread/INFO] [xaero.hud.minimap.MinimapLogs/]: Successfully reloaded the minimap shaders!
[018月2025 17:55:38.790] [Render thread/INFO] [com.github.ysbbbbbb.kaleidoscopecookery.KaleidoscopeCookery/]: Successfully loaded item render replacer data
[018月2025 17:55:38.790] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x256x0 polylib:textures/atlas/gui.png-atlas
[018月2025 17:55:38.799] [Render thread/INFO] [mixin/]: Mixing client.MixinRowHelper from mixins/common/nochatreports.mixins.json into net.minecraft.client.gui.layouts.GridLayout$RowHelper
[018月2025 17:55:38.814] [Render thread/INFO] [mixin/]: Mixing client.MixinChatOptionsScreen from mixins/common/nochatreports.mixins.json into net.minecraft.client.gui.screens.ChatOptionsScreen
[018月2025 17:55:38.815] [Render thread/INFO] [mixin/]: Renaming synthetic method lambda$init$0(Lnet/minecraft/client/gui/components/AbstractWidget;)V to mdac68fa$lambda$init$0$0 in mixins/common/nochatreports.mixins.json:client.MixinChatOptionsScreen
[018月2025 17:55:38.831] [Render thread/WARN] [flywheel/]: Flywheel backend fell back from 'irisflw:iris_instancing' to 'flywheel:indirect'
[018月2025 17:55:38.886] [Render thread/INFO] [Oculus/]: Creating pipeline for dimension NamespacedId{namespace='minecraft', name='overworld'}
[018月2025 17:55:38.895] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.gl.device.GLRenderDevice$ImmediateDrawCommandList, which may cause instability.
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:large_fern had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:tall_grass had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:grass_block had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:fern had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:grass had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:potted_fern had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:sugar_cane had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:spruce_leaves had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:birch_leaves had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:oak_leaves had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:jungle_leaves had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:acacia_leaves had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.927] [Render thread/INFO] [Embeddium/]: Block minecraft:dark_oak_leaves had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:38.928] [Render thread/INFO] [Embeddium/]: Block minecraft:vine had its color provider replaced and will not use per-vertex coloring
[018月2025 17:55:40.951] [Render thread/WARN] [ModernFix/]: Game took 82.146 seconds to start
[018月2025 17:57:05.618] [Render thread/INFO] [chloride/]: Registering CHLORIDE built-in packs
[018月2025 17:57:05.664] [Render thread/INFO] [com.simibubi.create.Create/]: Created 217 recipes which will be injected into the game
[018月2025 17:57:05.665] [Render thread/INFO] [com.simibubi.create.Create/]: Created 0 tags which will be injected into the game
[018月2025 17:57:06.104] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:cfk.js in 0.001 s
[018月2025 17:57:06.106] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:Chinese_cuisine.js in 0.002 s
[018月2025 17:57:06.107] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:createsifter.js in 0.0 s
[018月2025 17:57:06.107] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:cuisine_delight.js in 0.0 s
[018月2025 17:57:06.116] [Render thread/INFO] [KubeJS Server/]: NPC/automated_test_suite.js#10: [AutoTest] 自动化测试套件已初始化
[018月2025 17:57:06.116] [Render thread/INFO] [KubeJS Server/]: NPC/automated_test_suite.js#346: [AutoTest] 自动化测试套件已加载
[018月2025 17:57:06.118] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:NPC/automated_test_suite.js in 0.009 s
[018月2025 17:57:06.123] [Render thread/INFO] [KubeJS Server/]: NPC/chef_controller.js#451: [ChefController] 厨师AI控制器已加载
[018月2025 17:57:06.124] [Render thread/INFO] [KubeJS Server/]: NPC/chef_controller.js#452: [AutoCookingSystem] 自动烹饪系统已加载
[018月2025 17:57:06.124] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:NPC/chef_controller.js in 0.006 s
[018月2025 17:57:06.129] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#651: [DebugCommands] 餐厅AI调试命令系统已加载
[018月2025 17:57:06.130] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#652: [DebugCommands] 使用 /restaurant_debug 来访问调试功能
[018月2025 17:57:06.130] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#653: [DebugCommands] === 基础命令 ===
[018月2025 17:57:06.130] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#654: [DebugCommands]   spawn_customer - 生成测试顾客
[018月2025 17:57:06.130] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#655: [DebugCommands]   spawn_waiter - 生成测试服务员
[018月2025 17:57:06.130] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#656: [DebugCommands]   spawn_chef - 生成测试厨师
[018月2025 17:57:06.130] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#657: [DebugCommands]   spawn_all_types - 生成所有类型NPC
[018月2025 17:57:06.130] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#658: [DebugCommands] === 监控命令 ===
[018月2025 17:57:06.130] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#659: [DebugCommands]   status - 显示系统状态
[018月2025 17:57:06.130] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#660: [DebugCommands]   orders - 显示订单状态
[018月2025 17:57:06.130] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#661: [DebugCommands]   clear_npcs - 清理所有NPC
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#662: [DebugCommands] === 管理命令 ===
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#663: [DebugCommands]   add_seat - 在玩家位置添加座位
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#664: [DebugCommands]   list_seats - 列出所有座位
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#665: [DebugCommands] === 热重载命令 ===
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#666: [DebugCommands]   reload - 执行热重载流程
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#667: [DebugCommands]   save_state - 保存系统状态
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#668: [DebugCommands]   restore_state - 恢复系统状态
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#669: [DebugCommands]   hot_reload_status - 显示热重载状态
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#670: [DebugCommands] === 测试命令 ===
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#671: [DebugCommands]   auto_test - 运行完整自动化测试
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#672: [DebugCommands]   quick_verify - 快速系统验证
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#673: [DebugCommands]   performance_test - 性能基准测试
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#674: [DebugCommands] === 交互调试 ===
[018月2025 17:57:06.131] [Render thread/INFO] [KubeJS Server/]: NPC/debug_commands.js#675: [DebugCommands] 手持钻石并潜行来查看附近NPC状态
[018月2025 17:57:06.132] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:NPC/debug_commands.js in 0.007 s
[018月2025 17:57:06.134] [Render thread/ERROR] [KubeJS Server/]: NPC/events.js#5: [NPCEvents] OrderManager未定义，请确保order_manager.js已加载
[018月2025 17:57:06.134] [Render thread/ERROR] [KubeJS Server/]: NPC/events.js#8: [NPCEvents] PaymentProcessor未定义，请确保order_manager.js已加载
[018月2025 17:57:06.135] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:NPC/events.js in 0.003 s
[018月2025 17:57:06.144] [Render thread/INFO] [KubeJS Server/]: NPC/hot_reload_manager.js#10: [HotReload] 热重载管理器初始化，版本: 1754042226138
[018月2025 17:57:06.145] [Render thread/INFO] [KubeJS Server/]: NPC/hot_reload_manager.js#277: [HotReload] 热重载管理器已加载
[018月2025 17:57:06.145] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:NPC/hot_reload_manager.js in 0.01 s
[018月2025 17:57:06.149] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:NPC/npc_controller.js in 0.004 s
[018月2025 17:57:06.152] [Render thread/INFO] [KubeJS Server/]: NPC/order_manager.js#51: [OrderManager] 订单管理器初始化完成
[018月2025 17:57:06.153] [Render thread/INFO] [KubeJS Server/]: NPC/order_manager.js#52: [OrderManager] 菜单包含 5 道菜
[018月2025 17:57:06.153] [Render thread/INFO] [KubeJS Server/]: NPC/order_manager.js#274: [OrderManager] 订单管理系统已加载
[018月2025 17:57:06.154] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:NPC/order_manager.js in 0.005 s
[018月2025 17:57:06.157] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:NPC/pathfinding.js in 0.003 s
[018月2025 17:57:06.159] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:NPC/seat_manager.js in 0.001 s
[018月2025 17:57:06.161] [Render thread/INFO] [KubeJS Server/]: NPC/system_test.js#266: [SystemTest] 餐厅AI系统集成测试已加载
[018月2025 17:57:06.162] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:NPC/system_test.js in 0.002 s
[018月2025 17:57:06.163] [Render thread/INFO] [KubeJS Server/]: NPC/test_system.js#143: [TestSystem] 餐厅AI系统测试脚本已加载
[018月2025 17:57:06.163] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:NPC/test_system.js in 0.001 s
[018月2025 17:57:06.169] [Render thread/INFO] [KubeJS Server/]: NPC/waiter_controller.js#302: [WaiterController] 服务员AI控制器已加载
[018月2025 17:57:06.169] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:NPC/waiter_controller.js in 0.005 s
[018月2025 17:57:06.171] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:suxu.js in 0.001 s
[018月2025 17:57:06.171] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:tag.js in 0.0 s
[018月2025 17:57:06.172] [Render thread/INFO] [KubeJS Server/]: Loaded script server_scripts:tom.js in 0.0 s
[018月2025 17:57:06.172] [Render thread/INFO] [KubeJS Server/]: Loaded 19/19 KubeJS server scripts in 0.074 s with 2 errors and 0 warnings
[018月2025 17:57:06.173] [Render thread/INFO] [KubeJS Server/]: Scripts loaded
[018月2025 17:57:06.196] [Render thread/INFO] [KubeJS Server/]: Initializing recipe event...
[018月2025 17:57:07.114] [Render thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC /easy_npc commands for Easy NPC ...
[018月2025 17:57:07.180] [Worker-ResourceReload-4/WARN] [mixin/]: Static binding violation: PRIVATE @Overwrite method m_215924_ in modernfix-forge.mixins.json:perf.tag_id_caching.TagEntryMixin cannot reduce visibiliy of PUBLIC target method, visibility will be upgraded.
[018月2025 17:57:07.202] [Worker-ResourceReload-14/INFO] [KubeJS Server/]: [minecraft:fluid] Found 27 tags, added 3 objects, removed 0 objects
[018月2025 17:57:07.265] [Worker-ResourceReload-7/INFO] [KubeJS Server/]: [minecraft:item] Found 1044 tags, added 9 objects, removed 0 objects
[018月2025 17:57:07.273] [Worker-ResourceReload-7/ERROR] [net.minecraft.tags.TagLoader/]: Couldn't load tag forge:fruits as it is missing following references: #forge:fruits/gearo_berry (from [腌渍乐事] vintagedelight-0.1.6.jar)
[018月2025 17:57:07.274] [Worker-ResourceReload-7/ERROR] [net.minecraft.tags.TagLoader/]: Couldn't load tag barbequesdelight:skewer_fruits as it is missing following references: #forge:fruits (from [烧烤乐事] barbequesdelight-1.0.5.jar)
[018月2025 17:57:07.274] [Worker-ResourceReload-7/ERROR] [net.minecraft.tags.TagLoader/]: Couldn't load tag create_bic_bit:snacks_deepfried as it is missing following references: create_deepfried:corn_dog (from createbicbit-1.20.1-1.0.1B.jar), 
	create_deepfried:chicken_nuggets (from createbicbit-1.20.1-1.0.1B.jar), 
	create_deepfried:yuca_fries (from createbicbit-1.20.1-1.0.1B.jar), 
	create_deepfried:springroll (from createbicbit-1.20.1-1.0.1B.jar), 
	create_deepfried:tempura (from createbicbit-1.20.1-1.0.1B.jar), 
	create_deepfried:calamari (from createbicbit-1.20.1-1.0.1B.jar), 
	create_deepfried:apfelkuchle (from createbicbit-1.20.1-1.0.1B.jar), 
	create_deepfried:berliner (from createbicbit-1.20.1-1.0.1B.jar), 
	create_deepfried:deepfried_chocolate_bar (from createbicbit-1.20.1-1.0.1B.jar), 
	create_deepfried:fried_chicken (from createbicbit-1.20.1-1.0.1B.jar), 
	create_deepfried:panzerotto (from createbicbit-1.20.1-1.0.1B.jar), 
	create_deepfried:onion_rings (from createbicbit-1.20.1-1.0.1B.jar), 
	create_deepfried:blooming_onion (from createbicbit-1.20.1-1.0.1B.jar)
[018月2025 17:57:07.275] [Worker-ResourceReload-7/ERROR] [net.minecraft.tags.TagLoader/]: Couldn't load tag forge:protein as it is missing following references: #forge:protein/peanut (from [腌渍乐事] vintagedelight-0.1.6.jar)
[018月2025 17:57:07.711] [Render thread/INFO] [KubeJS Server/]: Processing recipes...
[018月2025 17:57:07.841] [Render thread/INFO] [KubeJS Server/]: Found 8027 recipes in 128.0 ms
[018月2025 17:57:07.874] [KubeJS Recipe Event Worker 0/WARN] [com.simibubi.create.Create/]: Your custom createaddition:rolling/gold_plate recipe (createaddition:rolling/gold_plate) specified a duration. Durations have no impact on this type of recipe.
[018月2025 17:57:07.874] [KubeJS Recipe Event Worker 0/WARN] [com.simibubi.create.Create/]: Your custom createaddition:rolling/iron_plate recipe (createaddition:rolling/iron_plate) specified a duration. Durations have no impact on this type of recipe.
[018月2025 17:57:07.901] [KubeJS Recipe Event Worker 0/WARN] [com.simibubi.create.Create/]: Your custom createaddition:rolling/copper_ingot recipe (createaddition:rolling/copper_ingot) specified a duration. Durations have no impact on this type of recipe.
[018月2025 17:57:07.917] [KubeJS Recipe Event Worker 0/WARN] [com.simibubi.create.Create/]: Your custom createaddition:rolling/straw recipe (createaddition:rolling/straw) specified a duration. Durations have no impact on this type of recipe.
[018月2025 17:57:07.937] [KubeJS Recipe Event Worker 0/WARN] [com.simibubi.create.Create/]: Your custom createaddition:rolling/copper_plate recipe (createaddition:rolling/copper_plate) specified a duration. Durations have no impact on this type of recipe.
[018月2025 17:57:07.938] [KubeJS Recipe Event Worker 0/WARN] [com.simibubi.create.Create/]: Your custom createaddition:rolling/gold_ingot recipe (createaddition:rolling/gold_ingot) specified a duration. Durations have no impact on this type of recipe.
[018月2025 17:57:07.940] [KubeJS Recipe Event Worker 0/WARN] [com.simibubi.create.Create/]: Your custom createaddition:rolling/brass_ingot recipe (createaddition:rolling/brass_ingot) specified a duration. Durations have no impact on this type of recipe.
[018月2025 17:57:07.941] [KubeJS Recipe Event Worker 0/WARN] [com.simibubi.create.Create/]: Your custom createaddition:rolling/electrum_ingot recipe (createaddition:rolling/electrum_ingot) specified a duration. Durations have no impact on this type of recipe.
[018月2025 17:57:07.978] [KubeJS Recipe Event Worker 0/WARN] [com.simibubi.create.Create/]: Your custom createaddition:rolling/iron_ingot recipe (createaddition:rolling/iron_ingot) specified a duration. Durations have no impact on this type of recipe.
[018月2025 17:57:07.978] [KubeJS Recipe Event Worker 0/WARN] [com.simibubi.create.Create/]: Your custom createaddition:rolling/electrum_plate recipe (createaddition:rolling/electrum_plate) specified a duration. Durations have no impact on this type of recipe.
[018月2025 17:57:08.023] [Render thread/INFO] [KubeJS Server/]: Posted recipe events in 180.7 ms
[018月2025 17:57:08.057] [KubeJS Recipe Event Worker 0/WARN] [KubeJS Server/]: Error parsing recipe create:kjs/a3iyd9jt3bbsfaggluku8794[create:filling]: {"type":"create:filling","results":[{"fluid":"fruitsdelight:lemon_juice","amount":1000}],"ingredients":[{"item":"minecraft:glass_bottle"},{"fluid":"minecraft:empty","nbt":{},"amount":0}]}: Invalid empty fluid 'minecraft:empty'
[018月2025 17:57:08.062] [KubeJS Recipe Event Worker 0/WARN] [KubeJS Server/]: Error parsing recipe create:kjs/9y8dv8bk9x0djj8uqx8f2574d[create:emptying]: {"type":"create:emptying","results":[{"fluid":"fruitsdelight:lemon_juice","amount":250},{"item":"minecraft:glass_bottle","count":1}],"ingredients":[{"fluid":"minecraft:empty","nbt":{},"amount":0}]}: Invalid empty fluid 'minecraft:empty'
[018月2025 17:57:08.064] [KubeJS Recipe Event Worker 0/WARN] [KubeJS Server/]: Error parsing recipe create:kjs/9xbb6bymd9biizef2r6w5x20c[create:mixing]: {"type":"create:mixing","results":[{"fluid":"fruitsdelight:lemon_juice","amount":250}],"ingredients":[{"fluid":"minecraft:water","nbt":{},"amount":250},{"fluid":"minecraft:empty","nbt":{},"amount":0}]}: Invalid empty fluid 'minecraft:empty'
[018月2025 17:57:08.078] [Render thread/INFO] [KubeJS Server/]: Added 33 recipes, removed 2220 recipes, modified 3 recipes, with 3 failed recipes in 47.72 ms
[018月2025 17:57:08.078] [Render thread/INFO] [ModernFix/]: Clearing KubeJS recipe lists...
[018月2025 17:57:08.139] [Render thread/ERROR] [net.minecraft.server.ServerAdvancementManager/]: Parsing error loading custom advancement suxu:join: Expected advancement to be a JsonObject, was "criteria"
[018月2025 17:57:08.187] [Render thread/INFO] [quark/]: [Automatic Recipe Unlock] Removed 3470 recipe advancements
[018月2025 17:57:08.192] [Render thread/INFO] [net.minecraft.advancements.AdvancementList/]: Loaded 448 advancements
[018月2025 17:57:08.209] [Render thread/WARN] [net.minecraftforge.common.loot.LootModifierManager/]: Could not decode GlobalLootModifier with json id dumplings_delight:add_calamari - error: Input does not contain a key [type]: MapLike[{"conditions":[{"condition":"forge:loot_table_id","loot_table_id":"minecraft:entities/squid"}],"item":"dumplings_delight:calamari"}]
[018月2025 17:57:08.225] [Render thread/INFO] [top.theillusivec4.curios.Curios/]: Loaded 11 curio slots
[018月2025 17:57:08.234] [Render thread/INFO] [top.theillusivec4.curios.Curios/]: Loaded 1 curio entities
[018月2025 17:57:08.271] [Render thread/INFO] [quark-zeta/]: Modified advancement minecraft:husbandry/wax_on with 2 patches
[018月2025 17:57:08.272] [Render thread/INFO] [quark-zeta/]: Modified advancement minecraft:adventure/kill_a_mob with 3 patches
[018月2025 17:57:08.272] [Render thread/INFO] [quark-zeta/]: Modified advancement minecraft:husbandry/bred_all_animals with 3 patches
[018月2025 17:57:08.272] [Render thread/INFO] [quark-zeta/]: Modified advancement minecraft:adventure/kill_all_mobs with 3 patches
[018月2025 17:57:08.273] [Render thread/INFO] [quark-zeta/]: Modified advancement minecraft:husbandry/balanced_diet with 2 patches
[018月2025 17:57:08.273] [Render thread/INFO] [quark-zeta/]: Modified advancement minecraft:nether/all_effects with 1 patches
[018月2025 17:57:08.273] [Render thread/INFO] [quark-zeta/]: Modified advancement minecraft:husbandry/wax_off with 2 patches
[018月2025 17:57:08.273] [Render thread/INFO] [quark-zeta/]: Modified advancement minecraft:adventure/adventuring_time with 1 patches
[018月2025 17:57:08.273] [Render thread/INFO] [quark-zeta/]: Modified advancement minecraft:nether/all_potions with 1 patches
[018月2025 17:57:08.322] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Added custom Lightman's Currency village structures to their respective pools
[018月2025 17:57:08.325] [Render thread/WARN] [ModernFix/]: Initial datapack load took 2.507 s
[018月2025 17:57:08.505] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Starting integrated minecraft server version 1.20.1
[018月2025 17:57:08.505] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Generating keypair
[018月2025 17:57:08.581] [Server thread/INFO] [com.tom.storagemod.StorageMod/]: Loaded Tom's Simple Storage config file toms_storage-server.toml
[018月2025 17:57:08.762] [Server thread/INFO] [FTB Teams/]: loaded team data: 1 known players, 1 teams total
[018月2025 17:57:08.786] [Server thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registering banker trades.
[018月2025 17:57:08.787] [Server thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Registering cashier trades.
[018月2025 17:57:08.796] [Server thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Reloading Money Data
[018月2025 17:57:09.250] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Preparing start region for dimension minecraft:overworld
[018月2025 17:57:09.255] [Server thread/INFO] [KubeJS Server/]: Server resource reload complete!
[018月2025 17:57:10.802] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[018月2025 17:57:10.807] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[018月2025 17:57:10.808] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[018月2025 17:57:10.808] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[018月2025 17:57:11.513] [Server thread/INFO] [net.minecraftforge.server.permission.PermissionAPI/]: Successfully initialized permission handler forge:default_handler
[018月2025 17:57:11.524] [Server thread/INFO] [KubeJS Server/]: NPC/events.js#18: [NPCEvents] 服务器加载，初始化NPC系统...
[018月2025 17:57:11.525] [Server thread/INFO] [KubeJS Server/]: NPC/events.js#23: [NPCEvents] 座位管理器已初始化
[018月2025 17:57:11.526] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: Time elapsed: 2273 ms
[018月2025 17:57:11.527] [Server thread/INFO] [xaero.map.WorldMap/]: Registered synced player tracker system: ftb_teams
[018月2025 17:57:11.531] [Server thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC Server is starting Events ...
[018月2025 17:57:11.532] [Server thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC Server data folders ...
[018月2025 17:57:11.532] [Server thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC pose data folders ...
[018月2025 17:57:11.533] [Server thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC custom pose data ...
[018月2025 17:57:11.543] [Server thread/WARN] [Easy NPC/]: Skin model pose file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\pose\humanoid_slim\humanoid_slim_poses.json already exists, skipping copy!
[018月2025 17:57:11.550] [Server thread/INFO] [Easy NPC/]: 🗣 Register Easy NPC custom poses from C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\pose ...
[018月2025 17:57:11.557] [Server thread/INFO] [Easy NPC/]: Found custom pose file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\easy_npc\pose\humanoid_slim\humanoid_slim_poses.json ...
[018月2025 17:57:11.567] [Server thread/INFO] [Easy NPC/]: [Pose Manager] Registering pose data easy_npc:pose/humanoid_slim/crouching with Animation{name='Crouching', loop='hold_on_last_frame', animation_length=null, bones={Head=Bone{position=[0.0, -4.2, 0.0], rotation=[-7.5, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, Body=Bone{position=[0.0, -3.2, 0.0], rotation=[20.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, RightArm=Bone{position=[0.0, -3.2, 1.0], rotation=[40.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, LeftArm=Bone{position=[0.0, -3.2, 1.0], rotation=[40.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, RightLeg=Bone{position=[0.0, 0.0, 4.0], rotation=[-25.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, LeftLeg=Bone{position=[0.0, 0.0, 3.0], rotation=[-10.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}}}
[018月2025 17:57:11.567] [Server thread/INFO] [Easy NPC/]: [Pose Manager] Registering pose data easy_npc:pose/humanoid_slim/standing with Animation{name='Standing', loop='hold_on_last_frame', animation_length=null, bones={Head=Bone{position=[0.0, 0.0, 0.0], rotation=[0.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, Body=Bone{position=[0.0, 0.0, 0.0], rotation=[0.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, RightArm=Bone{position=[0.0, 0.0, 0.0], rotation=[0.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, LeftArm=Bone{position=[0.0, 0.0, 0.0], rotation=[0.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, RightLeg=Bone{position=[0.0, 0.0, 0.0], rotation=[0.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, LeftLeg=Bone{position=[0.0, 0.0, 0.0], rotation=[0.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}}}
[018月2025 17:57:11.567] [Server thread/INFO] [Easy NPC/]: [Pose Manager] Registering pose data easy_npc:pose/humanoid_slim/sitting with Animation{name='Sitting', loop='hold_on_last_frame', animation_length=null, bones={Head=Bone{position=[0.0, -10.5, 0.0], rotation=[0.0, 0.0, -2.5], scale=null, keyframePosition=null, keyframeRotation=null}, Body=Bone{position=[0.0, -10.5, 0.0], rotation=null, scale=null, keyframePosition=null, keyframeRotation=null}, RightArm=Bone{position=[0.0, -10.5, 0.0], rotation=[-35.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, LeftArm=Bone{position=[0.0, -10.5, 0.0], rotation=[-35.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, RightLeg=Bone{position=[0.0, -10.0, 0.0], rotation=[-90.0, 15.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, LeftLeg=Bone{position=[0.0, -10.0, 0.0], rotation=[-90.0, -10.5, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}}}
[018月2025 17:57:11.567] [Server thread/INFO] [Easy NPC/]: [Pose Manager] Registering pose data easy_npc:pose/humanoid_slim/chilling with Animation{name='Chilling', loop='hold_on_last_frame', animation_length=null, bones={Head=Bone{position=[0.0, -12.5, 2.0], rotation=[-2.5, 0.0, 2.5], scale=null, keyframePosition=null, keyframeRotation=null}, Body=Bone{position=[0.0, -12.25, 3.0], rotation=[-20.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, RightArm=Bone{position=[0.0, -12.5, 2.0], rotation=[17.5, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, LeftArm=Bone{position=[0.0, -12.5, 2.0], rotation=[20.0, 0.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, RightLeg=Bone{position=[0.0, -10.0, 0.0], rotation=[-90.0, 15.0, -5.0], scale=null, keyframePosition=null, keyframeRotation=null}, LeftLeg=Bone{position=[0.0, -10.0, 0.75], rotation=[-90.0, -10.5, 5.0], scale=null, keyframePosition=null, keyframeRotation=null}}}
[018月2025 17:57:11.567] [Server thread/INFO] [Easy NPC/]: [Pose Manager] Registering pose data easy_npc:pose/humanoid_slim/rest with Animation{name='Rest', loop='hold_on_last_frame', animation_length=null, bones={Head=Bone{position=[0.0, -12.0, 0.0], rotation=[5.0, 0.0, 5.0], scale=null, keyframePosition=null, keyframeRotation=null}, Body=Bone{position=[0.0, -12.0, 0.0], rotation=[0.0, 0.0, 2.5], scale=null, keyframePosition=null, keyframeRotation=null}, RightArm=Bone{position=[-1.0, -11.0, 0.0], rotation=[-75.0, -30.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, LeftArm=Bone{position=[0.0, -12.0, 0.0], rotation=[-52.5, -5.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, RightLeg=Bone{position=[-3.0, -2.0, -2.0], rotation=[-32.5, -20.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}, LeftLeg=Bone{position=[6.75, -10.0, 0.0], rotation=[-90.0, 40.0, 0.0], scale=null, keyframePosition=null, keyframeRotation=null}}}
[018月2025 17:57:11.581] [Server thread/INFO] [xaero.hud.minimap.MinimapLogs/]: Registered synced player tracker system: ftb_teams
[018月2025 17:57:11.582] [Server thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Reloading config/lightmanscurrency-server.txt
[018月2025 17:57:11.611] [Server thread/INFO] [FTB Quests/]: Loading quests from C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\config\ftbquests\quests
[018月2025 17:57:11.647] [Server thread/INFO] [FTB Quests/]: Loaded 3 chapter groups, 5 chapters, 76 quests, 0 reward tables
[018月2025 17:57:11.669] [Server thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Loaded NextID (1) from tag.
[018月2025 17:57:12.016] [Server thread/ERROR] [KubeJS Server/]: NPC/automated_test_suite.js#332: Error occurred while handling scheduled event callback: TypeError: redeclaration of var verification.
[018月2025 17:57:12.017] [Server thread/INFO] [KubeJS Server/]: NPC/system_test.js#12: [SystemTest] 开始餐厅AI系统集成测试...
[018月2025 17:57:12.019] [Server thread/INFO] [KubeJS Server/]: NPC/order_manager.js#265: [PaymentProcessor] 支付处理: ¥10 by test_customer
[018月2025 17:57:12.019] [Server thread/INFO] [KubeJS Server/]: NPC/system_test.js#170: 
[SystemTest] ===== 餐厅AI系统测试结果 =====
[018月2025 17:57:12.019] [Server thread/INFO] [KubeJS Server/]: NPC/system_test.js#37: [SystemTest] ⚠ seatManager: 座位管理器测试失败: InternalError: TypeError: redeclaration of var stats.
[018月2025 17:57:12.019] [Server thread/INFO] [KubeJS Server/]: NPC/system_test.js#57: [SystemTest] ⚠ orderManager: 订单管理器测试失败: InternalError: TypeError: redeclaration of var stats.
[018月2025 17:57:12.020] [Server thread/INFO] [KubeJS Server/]: NPC/system_test.js#82: [SystemTest] ⚠ paymentProcessor: 支付处理器测试失败: InternalError: TypeError: redeclaration of var testTransaction.
[018月2025 17:57:12.020] [Server thread/INFO] [KubeJS Server/]: NPC/system_test.js#181: [SystemTest] ✓ autoCookingSystem: 自动烹饪系统正常运行
[018月2025 17:57:12.020] [Server thread/INFO] [KubeJS Server/]: NPC/system_test.js#184: [SystemTest]   详情: {"enabled":true}
[018月2025 17:57:12.020] [Server thread/INFO] [KubeJS Server/]: NPC/system_test.js#120: [SystemTest] ⚠ npcControllers: NPC控制器测试失败: InternalError: TypeError: redeclaration of var controllerCount.
[018月2025 17:57:12.021] [Server thread/INFO] [KubeJS Server/]: NPC/system_test.js#137: [SystemTest] ⚠ eventHandlers: 事件处理器测试失败: InternalError: TypeError: redeclaration of var requiredFunctions.
[018月2025 17:57:12.021] [Server thread/INFO] [KubeJS Server/]: NPC/system_test.js#192: [SystemTest] ===== 测试总结 =====
[018月2025 17:57:12.021] [Server thread/INFO] [KubeJS Server/]: NPC/system_test.js#193: [SystemTest] 通过: 1, 失败: 0, 错误: 5
[018月2025 17:57:12.021] [Server thread/INFO] [KubeJS Server/]: NPC/system_test.js#196: [SystemTest] 总体状态: 系统存在问题
[018月2025 17:57:12.021] [Server thread/ERROR] [KubeJS Server/]: NPC/system_test.js#254: Error occurred while handling scheduled event callback: TypeError: redeclaration of var results.
[018月2025 17:57:12.021] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#13: === 餐厅AI系统基础测试开始 ===
[018月2025 17:57:12.021] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#21: ✓ 座位管理器初始化成功
[018月2025 17:57:12.021] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#30: ✓ NPC控制器映射初始化成功
[018月2025 17:57:12.021] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#39: ✓ 座位管理器方法可用
[018月2025 17:57:12.021] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#48: ✓ 寻路算法已加载
[018月2025 17:57:12.021] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#57: ✓ 顾客控制器类已加载
[018月2025 17:57:12.021] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#64: === 测试完成: 5/5 通过 ===
[018月2025 17:57:12.021] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#67: 🎉 所有基础测试通过！餐厅AI系统已就绪
[018月2025 17:57:12.022] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#68: 💡 提示：
[018月2025 17:57:12.022] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#69:    - 放置台阶方块来创建座位
[018月2025 17:57:12.022] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#70:    - 生成村民来测试顾客AI
[018月2025 17:57:12.022] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#71:    - 使用 /restaurant_debug 来访问调试功能
[018月2025 17:57:12.022] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Changing view distance to 12, from 10
[018月2025 17:57:12.023] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Changing simulation distance to 12, from 0
[018月2025 17:57:12.204] [Render thread/WARN] [io.netty.util.internal.SystemPropertyUtil/]: Unable to parse the boolean system property 'java.net.preferIPv6Addresses':system - using the default value: false
[018月2025 17:57:12.369] [Render thread/INFO] [mixin/]: Mixing client.MixinClientHandshakePacketListenerImpl from mixins/forge/nochatreports-forge.mixins.json into net.minecraft.client.multiplayer.ClientHandshakePacketListenerImpl
[018月2025 17:57:12.595] [Netty Local Client IO #0/INFO] [mixin/]: Mixing server.MixinServerboundChatCommandPacket from mixins/common/nochatreports.mixins.json into net.minecraft.network.protocol.game.ServerboundChatCommandPacket
[018月2025 17:57:12.597] [Netty Local Client IO #0/INFO] [mixin/]: Mixing server.MixinServerboundChatPacket from mixins/common/nochatreports.mixins.json into net.minecraft.network.protocol.game.ServerboundChatPacket
[018月2025 17:57:12.599] [Netty Local Client IO #0/INFO] [mixin/]: Mixing server.MixinServerboundChatSessionUpdatePacket from mixins/common/nochatreports.mixins.json into net.minecraft.network.protocol.game.ServerboundChatSessionUpdatePacket
[018月2025 17:57:12.698] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#138: ⚠️ 服务器tick延迟: 6529ms (平均: 6529.0ms)
[018月2025 17:57:13.165] [Netty Local Client IO #0/INFO] [net.minecraftforge.network.NetworkHooks/]: Connected to a modded server.
[018月2025 17:57:13.165] [Server thread/WARN] [net.minecraft.network.syncher.SynchedEntityData/]: defineId called for: class net.minecraft.world.entity.player.Player from class tschipp.carryon.common.carry.CarryOnDataManager
[018月2025 17:57:13.225] [Server thread/INFO] [net.minecraft.server.players.PlayerList/]: Quasar2323[local:E:090d7575] logged in with entity id 86 at (47.69999998807907, -60.0, 76.79904067223296)
[018月2025 17:57:13.287] [Server thread/INFO] [ModernFix/]: Using enhanced recipe sync for player Quasar2323
[018月2025 17:57:13.346] [Render thread/INFO] [xaero.map.WorldMap/]: New world map session initialized!
[018月2025 17:57:13.350] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Quasar2323加入了游戏
[018月2025 17:57:13.419] [Render thread/INFO] [xaero.hud.minimap.MinimapLogs/]: New Xaero hud session initialized!
[018月2025 17:57:13.450] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Quasar2323取得了进度[我的第一块钱！]
[018月2025 17:57:13.498] [Render thread/INFO] [Oculus/]: Reloading pipeline on dimension change: NamespacedId{namespace='minecraft', name='overworld'} => NamespacedId{namespace='minecraft', name='overworld'}
[018月2025 17:57:13.499] [Render thread/INFO] [Oculus/]: Destroying pipeline NamespacedId{namespace='minecraft', name='overworld'}
[018月2025 17:57:13.506] [Render thread/INFO] [Oculus/]: Creating pipeline for dimension NamespacedId{namespace='minecraft', name='overworld'}
[018月2025 17:57:13.539] [Render thread/WARN] [flywheel/]: Flywheel backend fell back from 'irisflw:iris_instancing' to 'flywheel:indirect'
[018月2025 17:57:13.548] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.chunk.RenderSectionManager, which may cause instability.
[018月2025 17:57:13.561] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.chunk.compile.tasks.ChunkBuilderMeshingTask, which may cause instability.
[018月2025 17:57:13.584] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.chunk.vertex.format.ChunkMeshFormats, which may cause instability.
[018月2025 17:57:13.588] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.chunk.vertex.format.ChunkMeshAttribute, which may cause instability.
[018月2025 17:57:13.592] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.gl.attribute.GlVertexFormat$Builder, which may cause instability.
[018月2025 17:57:13.597] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.gl.attribute.GlVertexAttributeFormat, which may cause instability.
[018月2025 17:57:13.605] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.chunk.DefaultChunkRenderer, which may cause instability.
[018月2025 17:57:13.608] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.chunk.ShaderChunkRenderer, which may cause instability.
[018月2025 17:57:13.616] [Netty Local Client IO #0/INFO] [FTB Quests/]: Read 10029 bytes, 255 objects
[018月2025 17:57:13.636] [Server thread/INFO] [Jade/]: Syncing config to Quasar2323 (077e3afc-b776-43c4-8ae3-0f9dbb755bef)
[018月2025 17:57:13.639] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.chunk.compile.ChunkBuildBuffers, which may cause instability.
[018月2025 17:57:13.644] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.chunk.vertex.builder.ChunkMeshBufferBuilder, which may cause instability.
[018月2025 17:57:13.648] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.chunk.compile.buffers.BakedChunkModelBuilder, which may cause instability.
[018月2025 17:57:13.705] [Render thread/WARN] [mixin/]: Error loading class: net/fabricmc/fabric/api/blockview/v2/FabricBlockView (java.lang.ClassNotFoundException: net.fabricmc.fabric.api.blockview.v2.FabricBlockView)
[018月2025 17:57:13.721] [Render thread/WARN] [mixin/]: Error loading class: net/fabricmc/fabric/api/rendering/data/v1/RenderAttachedBlockView (java.lang.ClassNotFoundException: net.fabricmc.fabric.api.rendering.data.v1.RenderAttachedBlockView)
[018月2025 17:57:13.767] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.model.light.smooth.SmoothLightPipeline, which may cause instability.
[018月2025 17:57:13.773] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.model.light.flat.FlatLightPipeline, which may cause instability.
[018月2025 17:57:13.848] [Render thread/INFO] [ChunkBuilder/]: Started 10 worker threads
[018月2025 17:57:13.867] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [flerovium] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.chunk.occlusion.OcclusionCuller, which may cause instability.
[018月2025 17:57:13.951] [Render thread/INFO] [mezz.jei.forge.startup.StartEventObserver/]: JEI StartEventObserver received class net.minecraftforge.client.event.ClientPlayerNetworkEvent$LoggingIn
[018月2025 17:57:13.951] [Render thread/INFO] [mezz.jei.forge.startup.StartEventObserver/]: JEI StartEventObserver transitioning state from DISABLED to ENABLED
[018月2025 17:57:13.960] [Render thread/INFO] [flywheel/]: Started 10 worker threads
[018月2025 17:57:14.018] [Render thread/INFO] [mezz.jei.forge.startup.StartEventObserver/]: JEI StartEventObserver received class net.minecraftforge.event.TagsUpdatedEvent
[018月2025 17:57:14.031] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@5728c422=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/fermented_spider_eye_from_fermenting
[018月2025 17:57:14.032] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@7d636785=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/pickled_egg_from_fermenting
[018月2025 17:57:14.033] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@58dd2f68=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/goblins_piss
[018月2025 17:57:14.033] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@2a3b2609=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/pearlescent_froglight_from_fermenting
[018月2025 17:57:14.033] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@3417dad7=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/century_egg_from_fermenting
[018月2025 17:57:14.033] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@5800552=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/vinegar_from_jam
[018月2025 17:57:14.034] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@76f87085=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/pickled_ghost_pepper_from_fermenting
[018月2025 17:57:14.034] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@52f0d212=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/oily_oaf
[018月2025 17:57:14.036] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@583b7bf9=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/verdant_froglight_from_fermenting
[018月2025 17:57:14.036] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@5d144f39=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/leather_from_fermenting
[018月2025 17:57:14.037] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@685d7046=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/ochre_froglight_from_fermenting
[018月2025 17:57:14.037] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@3ce94f1d=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/overnight_oats_from_fermenting
[018月2025 17:57:14.037] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@3f8e1fca=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/pickled_onion_from_fermenting
[018月2025 17:57:14.038] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@409ce285=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/dragons_belch
[018月2025 17:57:14.039] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@35a74b42=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/vinegar_from_jam_bottles
[018月2025 17:57:14.039] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@7a186273=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/pickled_beetroot_from_fermenting
[018月2025 17:57:14.040] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@5db72f7f=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/pickle_from_fermenting
[018月2025 17:57:14.040] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@1d1badbc=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/kimchi_from_fermenting
[018月2025 17:57:14.041] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@12691806=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/berserks_brew
[018月2025 17:57:14.043] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@2bdbebc2=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/brashing_tonic
[018月2025 17:57:14.044] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@4ff3e511=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/slime_block_from_fermenting
[018月2025 17:57:14.044] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@fc5d71e=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/surstromming_from_fermenting
[018月2025 17:57:14.045] [Render thread/WARN] [net.minecraft.client.ClientRecipeBook/]: Unknown recipe category: [!!!com.mojang.logging.LogUtils$1ToString@553657c8=>java.lang.NullPointerException:Cannot invoke "Object.toString()" because the return value of "java.util.function.Supplier.get()" is null!!!]/vintagedelight:fermenting/pickled_pitcher_pod_from_fermenting
[018月2025 17:57:14.054] [Render thread/INFO] [mezz.jei.forge.startup.StartEventObserver/]: JEI StartEventObserver received class net.minecraftforge.client.event.RecipesUpdatedEvent
[018月2025 17:57:14.054] [Render thread/INFO] [mezz.jei.forge.startup.StartEventObserver/]: JEI StartEventObserver transitioning state from ENABLED to JEI_STARTED
[018月2025 17:57:14.056] [Render thread/INFO] [mezz.jei.core.util.LoggedTimer/]: Starting JEI...
[018月2025 17:57:14.074] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering item subtypes...
[018月2025 17:57:14.086] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering item subtypes: jei:minecraft took 11.38 milliseconds
[018月2025 17:57:14.099] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering item subtypes: sophisticatedbackpacks:default took 13.47 milliseconds
[018月2025 17:57:14.104] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering item subtypes took 30.38 ms
[018月2025 17:57:14.105] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering fluid subtypes...
[018月2025 17:57:14.112] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering fluid subtypes took 6.215 ms
[018月2025 17:57:14.117] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering ingredients...
[018月2025 17:57:14.252] [Render thread/WARN] [mezz.jei.library.plugins.vanilla.ingredients.ItemStackListFactory/]: Item Group has no display items and no search tab display items. Items from this group will be missing from the JEI ingredient list. 管理员用品
[018月2025 17:57:14.376] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering ingredients: jei:minecraft took 258.4 milliseconds
[018月2025 17:57:14.396] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering ingredients: jeed:jei_plugin took 19.72 milliseconds
[018月2025 17:57:14.397] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering ingredients took 279.9 ms
[018月2025 17:57:14.398] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering extra ingredients...
[018月2025 17:57:14.404] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering extra ingredients took 5.577 ms
[018月2025 17:57:14.405] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering search ingredient aliases...
[018月2025 17:57:14.406] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering search ingredient aliases took 1.394 ms
[018月2025 17:57:14.439] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering categories...
[018月2025 17:57:14.482] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering categories: jei:minecraft took 41.40 milliseconds
[018月2025 17:57:14.511] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering categories: create_bic_bit:jei_plugin took 21.67 milliseconds
[018月2025 17:57:16.000] [Render thread/WARN] [jeresources/]: Failed loading villager cartographer registered at cartographer
[018月2025 17:57:16.000] [Render thread/WARN] [jeresources/]: Exception caught when registering villager
java.lang.NullPointerException: Cannot invoke "net.minecraft.world.entity.Entity.m_9236_()" because "p_219708_" is null
	at net.minecraft.world.entity.npc.VillagerTrades$TreasureMapForEmeralds.m_213663_(VillagerTrades.java:378) ~[client-1.20.1-********.114412-srg.jar%23306!/:?]
	at jeresources.collection.TradeList.addMerchantRecipe(TradeList.java:58) ~[JustEnoughResources-1.20.1-*********.jar%23294!/:*********]
	at jeresources.collection.TradeList.addITradeList(TradeList.java:69) ~[JustEnoughResources-1.20.1-*********.jar%23294!/:*********]
	at jeresources.entry.AbstractVillagerEntry.addITradeLists(AbstractVillagerEntry.java:29) ~[JustEnoughResources-1.20.1-*********.jar%23294!/:*********]
	at jeresources.entry.AbstractVillagerEntry.<init>(AbstractVillagerEntry.java:22) ~[JustEnoughResources-1.20.1-*********.jar%23294!/:*********]
	at jeresources.entry.VillagerEntry.<init>(VillagerEntry.java:19) ~[JustEnoughResources-1.20.1-*********.jar%23294!/:*********]
	at jeresources.util.VillagersHelper.initRegistry(VillagersHelper.java:23) ~[JustEnoughResources-1.20.1-*********.jar%23294!/:*********]
	at jeresources.compatibility.Compatibility.init(Compatibility.java:33) ~[JustEnoughResources-1.20.1-*********.jar%23294!/:*********]
	at jeresources.proxy.CommonProxy.initCompatibility(CommonProxy.java:14) ~[JustEnoughResources-1.20.1-*********.jar%23294!/:*********]
	at jeresources.jei.JEIConfig.registerCategories(JEIConfig.java:98) ~[JustEnoughResources-1.20.1-*********.jar%23294!/:*********]
	at mezz.jei.library.load.PluginLoader.lambda$createRecipeCategories$5(PluginLoader.java:119) ~[%5BJEI物品管理器%5D%20jei-1.20.1-forge-***********.jar%23237!/:***********]
	at mezz.jei.library.load.PluginCaller.callOnPlugins(PluginCaller.java:25) ~[%5BJEI物品管理器%5D%20jei-1.20.1-forge-***********.jar%23237!/:***********]
	at mezz.jei.library.load.PluginLoader.createRecipeCategories(PluginLoader.java:119) ~[%5BJEI物品管理器%5D%20jei-1.20.1-forge-***********.jar%23237!/:***********]
	at mezz.jei.library.load.PluginLoader.createRecipeManager(PluginLoader.java:154) ~[%5BJEI物品管理器%5D%20jei-1.20.1-forge-***********.jar%23237!/:***********]
	at mezz.jei.library.startup.JeiStarter.start(JeiStarter.java:115) ~[%5BJEI物品管理器%5D%20jei-1.20.1-forge-***********.jar%23237!/:***********]
	at mezz.jei.forge.startup.StartEventObserver.transitionState(StartEventObserver.java:130) ~[%5BJEI物品管理器%5D%20jei-1.20.1-forge-***********.jar%23237!/:***********]
	at mezz.jei.forge.startup.StartEventObserver.onEvent(StartEventObserver.java:98) ~[%5BJEI物品管理器%5D%20jei-1.20.1-forge-***********.jar%23237!/:***********]
	at net.minecraftforge.eventbus.EventBus.doCastFilter(EventBus.java:260) ~[eventbus-6.0.5.jar%23137!/:?]
	at net.minecraftforge.eventbus.EventBus.lambda$addListener$11(EventBus.java:252) ~[eventbus-6.0.5.jar%23137!/:?]
	at net.minecraftforge.eventbus.EventBus.post(EventBus.java:315) ~[eventbus-6.0.5.jar%23137!/:?]
	at net.minecraftforge.eventbus.EventBus.post(EventBus.java:296) ~[eventbus-6.0.5.jar%23137!/:?]
	at net.minecraftforge.client.ForgeHooksClient.onRecipesUpdated(ForgeHooksClient.java:718) ~[forge-1.20.1-47.4.0-universal.jar%23311!/:?]
	at net.minecraft.client.multiplayer.ClientPacketListener.m_6327_(ClientPacketListener.java:1354) ~[client-1.20.1-********.114412-srg.jar%23306!/:?]
	at net.minecraft.network.protocol.game.ClientboundUpdateRecipesPacket.m_5797_(ClientboundUpdateRecipesPacket.java:32) ~[client-1.20.1-********.114412-srg.jar%23306!/:?]
	at net.minecraft.network.protocol.game.ClientboundUpdateRecipesPacket.m_5797_(ClientboundUpdateRecipesPacket.java:14) ~[client-1.20.1-********.114412-srg.jar%23306!/:?]
	at net.minecraft.network.protocol.PacketUtils.m_263899_(PacketUtils.java:22) ~[client-1.20.1-********.114412-srg.jar%23306!/:?]
	at net.minecraft.util.thread.BlockableEventLoop.m_6367_(BlockableEventLoop.java:156) ~[client-1.20.1-********.114412-srg.jar%23306!/:?]
	at net.minecraft.util.thread.ReentrantBlockableEventLoop.m_6367_(ReentrantBlockableEventLoop.java:23) ~[client-1.20.1-********.114412-srg.jar%23306!/:?]
	at net.minecraft.util.thread.BlockableEventLoop.m_7245_(BlockableEventLoop.java:130) ~[client-1.20.1-********.114412-srg.jar%23306!/:?]
	at net.minecraft.util.thread.BlockableEventLoop.m_18699_(BlockableEventLoop.java:115) ~[client-1.20.1-********.114412-srg.jar%23306!/:?]
	at net.minecraft.client.Minecraft.m_91383_(Minecraft.java:1106) ~[client-1.20.1-********.114412-srg.jar%23306!/:?]
	at net.minecraft.client.Minecraft.m_91374_(Minecraft.java:718) ~[client-1.20.1-********.114412-srg.jar%23306!/:?]
	at net.minecraft.client.main.Main.main(Main.java:218) ~[Chinese%20Cuisine.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.runTarget(CommonLaunchHandler.java:111) ~[fmlloader-1.20.1-47.4.0.jar:?]
	at net.minecraftforge.fml.loading.targets.CommonLaunchHandler.clientService(CommonLaunchHandler.java:99) ~[fmlloader-1.20.1-47.4.0.jar:?]
	at net.minecraftforge.fml.loading.targets.CommonClientLaunchHandler.lambda$makeService$0(CommonClientLaunchHandler.java:25) ~[fmlloader-1.20.1-47.4.0.jar:?]
	at cpw.mods.modlauncher.LaunchServiceHandlerDecorator.launch(LaunchServiceHandlerDecorator.java:30) ~[modlauncher-10.0.9.jar:?]
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:53) ~[modlauncher-10.0.9.jar:?]
	at cpw.mods.modlauncher.LaunchServiceHandler.launch(LaunchServiceHandler.java:71) ~[modlauncher-10.0.9.jar:?]
	at cpw.mods.modlauncher.Launcher.run(Launcher.java:108) ~[modlauncher-10.0.9.jar:?]
	at cpw.mods.modlauncher.Launcher.main(Launcher.java:78) ~[modlauncher-10.0.9.jar:?]
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:26) ~[modlauncher-10.0.9.jar:?]
	at cpw.mods.modlauncher.BootstrapLaunchConsumer.accept(BootstrapLaunchConsumer.java:23) ~[modlauncher-10.0.9.jar:?]
	at cpw.mods.bootstraplauncher.BootstrapLauncher.main(BootstrapLauncher.java:141) ~[bootstraplauncher-1.1.2.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at oolloo.jlw.Wrapper.invokeMain(Wrapper.java:112) ~[?:?]
	at oolloo.jlw.Wrapper.main(Wrapper.java:105) ~[?:?]
[018月2025 17:57:16.089] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering categories: minecraft:jeresources took 1.572 seconds
[018月2025 17:57:16.132] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering categories: create:jei_plugin took 25.05 milliseconds
[018月2025 17:57:16.147] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering categories took 1.707 s
[018月2025 17:57:16.147] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering vanilla category extensions...
[018月2025 17:57:16.151] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering vanilla category extensions took 3.644 ms
[018月2025 17:57:16.152] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering recipe catalysts...
[018月2025 17:57:16.155] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering recipe catalysts took 3.857 ms
[018月2025 17:57:16.156] [Render thread/INFO] [mezz.jei.core.util.LoggedTimer/]: Building recipe registry...
[018月2025 17:57:16.166] [Render thread/INFO] [mezz.jei.core.util.LoggedTimer/]: Building recipe registry took 10.31 ms
[018月2025 17:57:16.167] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering advanced plugins...
[018月2025 17:57:16.171] [Render thread/INFO] [mezz.jei.library.load.registration.AdvancedRegistration/]: Added recipe manager plugin: class dev.ftb.mods.ftbxmodcompat.ftbquests.jei.QuestRecipeManagerPlugin
[018月2025 17:57:16.172] [Render thread/INFO] [mezz.jei.library.load.registration.AdvancedRegistration/]: Added recipe manager plugin: class dev.ftb.mods.ftbxmodcompat.ftbquests.jei.LootCrateRecipeManagerPlugin
[018月2025 17:57:16.173] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering advanced plugins took 4.881 ms
[018月2025 17:57:16.173] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering recipes...
[018月2025 17:57:16.509] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering recipes: jei:minecraft took 335.5 milliseconds
[018月2025 17:57:16.534] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering recipes: create_bic_bit:jei_plugin took 12.63 milliseconds
[018月2025 17:57:16.570] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering recipes: jeed:jei_plugin took 29.50 milliseconds
[018月2025 17:57:16.858] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering recipes: minecraft:jeresources took 286.9 milliseconds
[018月2025 17:57:17.225] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering recipes: create:jei_plugin took 357.4 milliseconds
[018月2025 17:57:17.237] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering recipes: create_central_kitchen:jei_plugin took 11.68 milliseconds
[018月2025 17:57:17.264] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering recipes: zeta:zeta took 14.54 milliseconds
[018月2025 17:57:17.282] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering recipes took 1.109 s
[018月2025 17:57:17.299] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering recipes transfer handlers...
[018月2025 17:57:17.319] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering recipes transfer handlers took 19.85 ms
[018月2025 17:57:17.324] [Render thread/INFO] [mezz.jei.core.util.LoggedTimer/]: Building runtime...
[018月2025 17:57:17.329] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering gui handlers...
[018月2025 17:57:17.349] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering gui handlers took 20.69 ms
[018月2025 17:57:17.352] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering Runtime...
[018月2025 17:57:17.354] [Render thread/INFO] [mezz.jei.gui.startup.JeiGuiStarter/]: Starting JEI GUI
[018月2025 17:57:17.354] [Render thread/INFO] [mezz.jei.core.util.LoggedTimer/]: Building ingredient list...
[018月2025 17:57:17.400] [Render thread/INFO] [mezz.jei.core.util.LoggedTimer/]: Building ingredient list took 45.50 ms
[018月2025 17:57:17.400] [Render thread/INFO] [mezz.jei.core.util.LoggedTimer/]: Building ingredient filter...
[018月2025 17:57:17.449] [Render thread/INFO] [mezz.jei.gui.ingredients.IngredientFilter/]: Adding 5684 ingredients
[018月2025 17:57:17.943] [Render thread/INFO] [mezz.jei.gui.ingredients.IngredientFilter/]: Added 5684 ingredients
[018月2025 17:57:17.944] [Render thread/INFO] [mezz.jei.core.util.LoggedTimer/]: Building ingredient filter took 544.3 ms
[018月2025 17:57:18.088] [Render thread/INFO] [mezz.jei.library.load.PluginCallerTimerRunnable/]: Registering Runtime: jei:forge_gui took 734.9 milliseconds
[018月2025 17:57:18.088] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Registering Runtime took 735.6 ms
[018月2025 17:57:18.089] [Render thread/INFO] [mezz.jei.core.util.LoggedTimer/]: Building runtime took 764.8 ms
[018月2025 17:57:18.089] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Sending Runtime...
[018月2025 17:57:18.091] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Sending Runtime took 1.562 ms
[018月2025 17:57:18.091] [Render thread/INFO] [mezz.jei.core.util.LoggedTimer/]: Starting JEI took 4.035 s
[018月2025 17:57:18.114] [Render thread/INFO] [xaero.hud.minimap.MinimapLogs/]: Minimap updated server level id: -1720293123 for world ResourceKey[minecraft:dimension / minecraft:overworld]
[018月2025 17:57:18.209] [Render thread/INFO] [mixin/]: Mixing client.MixinGuiMessageTag from mixins/common/nochatreports.mixins.json into net.minecraft.client.GuiMessageTag
[018月2025 17:57:18.210] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [CHAT] Quasar2323取得了进度[我的第一块钱！]
[018月2025 17:57:18.212] [Render thread/WARN] [chat_heads/]: couldn't find player name inside chat message
[018月2025 17:57:18.218] [Render thread/INFO] [dev.ftb.mods.ftbultimine.FTBUltimine/]: received server config settings
[018月2025 17:57:18.231] [Render thread/INFO] [FTB Chunks/]: Loading FTB Chunks client data from world 454f3df8-1eaa-4928-98cf-ff329a0613d0
[018月2025 17:57:18.238] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [CHAT] [中餐厅] NPC系统已激活
[018月2025 17:57:18.238] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [CHAT] 使用 /kubejs hand 获取当前物品信息
[018月2025 17:57:18.239] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [CHAT] 放置台阶作为椅子来创建座位
[018月2025 17:57:18.239] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [CHAT] KubeJS errors found [2]! Run '/kubejs errors server' for more info
[018月2025 17:57:18.242] [Render thread/INFO] [Jade/]: Received config from the server: 
[018月2025 17:57:18.245] [Render thread/INFO] [io.github.lightman314.lightmanscurrency.LightmansCurrency/]: Received config data for 'lightmanscurrency:server' from the server!
[018月2025 17:57:18.251] [Render thread/INFO] [net.minecraft.advancements.AdvancementList/]: Loaded 59 advancements
[018月2025 17:57:18.274] [Render thread/INFO] [mezz.jei.library.ingredients.IngredientManager/]: Ingredients are being removed at runtime: 54 net.minecraft.world.item.ItemStack
[018月2025 17:57:18.316] [Render thread/ERROR] [mezz.jei.gui.ingredients.IngredientFilter/]: Could not find a matching ingredient to remove: 1 fan_seething_catalyst create_connected:fan_seething_catalyst
[018月2025 17:57:18.316] [Render thread/ERROR] [mezz.jei.gui.ingredients.IngredientFilter/]: Could not find a matching ingredient to remove: 1 fan_sanding_catalyst create_connected:fan_sanding_catalyst
[018月2025 17:57:18.316] [Render thread/ERROR] [mezz.jei.gui.ingredients.IngredientFilter/]: Could not find a matching ingredient to remove: 1 fan_enriched_catalyst create_connected:fan_enriched_catalyst
[018月2025 17:57:18.316] [Render thread/ERROR] [mezz.jei.gui.ingredients.IngredientFilter/]: Could not find a matching ingredient to remove: 1 fan_ending_catalyst_dragons_breath create_connected:fan_ending_catalyst_dragons_breath
[018月2025 17:57:18.316] [Render thread/ERROR] [mezz.jei.gui.ingredients.IngredientFilter/]: Could not find a matching ingredient to remove: 1 fan_ending_catalyst_dragon_head create_connected:fan_ending_catalyst_dragon_head
[018月2025 17:57:18.316] [Render thread/ERROR] [mezz.jei.gui.ingredients.IngredientFilter/]: Could not find a matching ingredient to remove: 1 fan_withering_catalyst create_connected:fan_withering_catalyst
[018月2025 17:57:18.318] [Render thread/INFO] [mezz.jei.library.ingredients.IngredientManager/]: Ingredients are being added at runtime: 48 net.minecraft.world.item.ItemStack
[018月2025 17:57:18.331] [Render thread/INFO] [mezz.jei.library.ingredients.IngredientManager/]: Ingredients are being removed at runtime: 54 net.minecraft.world.item.ItemStack
[018月2025 17:57:18.341] [Render thread/ERROR] [mezz.jei.gui.ingredients.IngredientFilter/]: Could not find a matching ingredient to remove: 1 fan_seething_catalyst create_connected:fan_seething_catalyst
[018月2025 17:57:18.341] [Render thread/ERROR] [mezz.jei.gui.ingredients.IngredientFilter/]: Could not find a matching ingredient to remove: 1 fan_sanding_catalyst create_connected:fan_sanding_catalyst
[018月2025 17:57:18.341] [Render thread/ERROR] [mezz.jei.gui.ingredients.IngredientFilter/]: Could not find a matching ingredient to remove: 1 fan_enriched_catalyst create_connected:fan_enriched_catalyst
[018月2025 17:57:18.341] [Render thread/ERROR] [mezz.jei.gui.ingredients.IngredientFilter/]: Could not find a matching ingredient to remove: 1 fan_ending_catalyst_dragons_breath create_connected:fan_ending_catalyst_dragons_breath
[018月2025 17:57:18.341] [Render thread/ERROR] [mezz.jei.gui.ingredients.IngredientFilter/]: Could not find a matching ingredient to remove: 1 fan_ending_catalyst_dragon_head create_connected:fan_ending_catalyst_dragon_head
[018月2025 17:57:18.341] [Render thread/ERROR] [mezz.jei.gui.ingredients.IngredientFilter/]: Could not find a matching ingredient to remove: 1 fan_withering_catalyst create_connected:fan_withering_catalyst
[018月2025 17:57:18.342] [Render thread/INFO] [mezz.jei.library.ingredients.IngredientManager/]: Ingredients are being added at runtime: 48 net.minecraft.world.item.ItemStack
[018月2025 17:57:18.676] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.gl.shader.ShaderType, which may cause instability.
[018月2025 17:57:18.689] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.gl.shader.GlProgram, which may cause instability.
[018月2025 17:57:18.759] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.immediate.CloudRenderer, which may cause instability.
[018月2025 17:57:18.866] [Render thread/INFO] [xaero.hud.minimap.MinimapLogs/]: Reloading radar icon resources...
[018月2025 17:57:18.878] [Render thread/INFO] [xaero.hud.minimap.MinimapLogs/]: Reloaded radar icon resources!
[018月2025 17:57:19.005] [Server thread/WARN] [KubeJS Server/]: NPC/events.js#99: [NPCEvents] 设置实体属性时出错: InternalError: Java class "net.minecraft.world.entity.npc.Villager" has no public instance field or method named "persistent".
[018月2025 17:57:19.006] [Server thread/INFO] [KubeJS Server/]: NPC/npc_controller.js#32: [CustomerController] 新顾客控制器创建: 57edf764-1ddc-4e3c-b5fa-766a2a673325
[018月2025 17:57:19.006] [Server thread/INFO] [KubeJS Server/]: NPC/events.js#117: [NPCEvents] 为实体 minecraft:villager (57edf764-1ddc-4e3c-b5fa-766a2a673325) 创建顾客控制器
[018月2025 17:57:19.011] [Server thread/WARN] [KubeJS Server/]: NPC/events.js#99: [NPCEvents] 设置实体属性时出错: InternalError: Java class "net.minecraft.world.entity.npc.Villager" has no public instance field or method named "persistent".
[018月2025 17:57:19.012] [Server thread/INFO] [KubeJS Server/]: NPC/npc_controller.js#32: [CustomerController] 新顾客控制器创建: 093aa9f5-3d24-4570-a48f-fffd1d9f5015
[018月2025 17:57:19.012] [Server thread/INFO] [KubeJS Server/]: NPC/events.js#117: [NPCEvents] 为实体 minecraft:villager (093aa9f5-3d24-4570-a48f-fffd1d9f5015) 创建顾客控制器
[018月2025 17:57:19.014] [Server thread/WARN] [KubeJS Server/]: NPC/events.js#99: [NPCEvents] 设置实体属性时出错: InternalError: Java class "net.minecraft.world.entity.npc.Villager" has no public instance field or method named "persistent".
[018月2025 17:57:19.014] [Server thread/INFO] [KubeJS Server/]: NPC/npc_controller.js#32: [CustomerController] 新顾客控制器创建: cfc51c0d-1418-42cc-8576-facad641cf23
[018月2025 17:57:19.014] [Server thread/INFO] [KubeJS Server/]: NPC/events.js#117: [NPCEvents] 为实体 minecraft:villager (cfc51c0d-1418-42cc-8576-facad641cf23) 创建顾客控制器
[018月2025 17:57:19.015] [Server thread/WARN] [KubeJS Server/]: NPC/events.js#99: [NPCEvents] 设置实体属性时出错: InternalError: Java class "net.minecraft.world.entity.npc.Villager" has no public instance field or method named "persistent".
[018月2025 17:57:19.015] [Server thread/INFO] [KubeJS Server/]: NPC/npc_controller.js#32: [CustomerController] 新顾客控制器创建: 2544fdb6-1113-4114-ad91-c8919a842ab2
[018月2025 17:57:19.016] [Server thread/INFO] [KubeJS Server/]: NPC/events.js#117: [NPCEvents] 为实体 minecraft:villager (2544fdb6-1113-4114-ad91-c8919a842ab2) 创建顾客控制器
[018月2025 17:57:19.016] [Server thread/WARN] [KubeJS Server/]: NPC/events.js#99: [NPCEvents] 设置实体属性时出错: InternalError: Java class "net.minecraft.world.entity.npc.Villager" has no public instance field or method named "persistent".
[018月2025 17:57:19.016] [Server thread/INFO] [KubeJS Server/]: NPC/npc_controller.js#32: [CustomerController] 新顾客控制器创建: 87abf4d7-7c63-42ac-a0c0-3859b39b1124
[018月2025 17:57:19.016] [Server thread/INFO] [KubeJS Server/]: NPC/events.js#117: [NPCEvents] 为实体 minecraft:villager (87abf4d7-7c63-42ac-a0c0-3859b39b1124) 创建顾客控制器
[018月2025 17:57:19.017] [Server thread/WARN] [KubeJS Server/]: NPC/events.js#99: [NPCEvents] 设置实体属性时出错: InternalError: Java class "net.minecraft.world.entity.npc.Villager" has no public instance field or method named "persistent".
[018月2025 17:57:19.017] [Server thread/INFO] [KubeJS Server/]: NPC/npc_controller.js#32: [CustomerController] 新顾客控制器创建: f5d2369a-cbac-455d-aeed-6234f98d87b5
[018月2025 17:57:19.017] [Server thread/INFO] [KubeJS Server/]: NPC/events.js#117: [NPCEvents] 为实体 minecraft:villager (f5d2369a-cbac-455d-aeed-6234f98d87b5) 创建顾客控制器
[018月2025 17:57:19.029] [Server thread/ERROR] [KubeJS Server/]: NPC/events.js#215: Error occurred while handling scheduled event callback: TypeError: Cannot find function scheduleRepeatingTask in object net.minecraft.client.server.IntegratedServer@20ffbd6a.
[018月2025 17:57:19.029] [Server thread/ERROR] [KubeJS Server/]: NPC/events.js#215: Error occurred while handling scheduled event callback: TypeError: Cannot find function scheduleRepeatingTask in object net.minecraft.client.server.IntegratedServer@20ffbd6a.
[018月2025 17:57:19.029] [Server thread/ERROR] [KubeJS Server/]: NPC/events.js#215: Error occurred while handling scheduled event callback: TypeError: Cannot find function scheduleRepeatingTask in object net.minecraft.client.server.IntegratedServer@20ffbd6a.
[018月2025 17:57:19.030] [Server thread/ERROR] [KubeJS Server/]: NPC/events.js#215: Error occurred while handling scheduled event callback: TypeError: Cannot find function scheduleRepeatingTask in object net.minecraft.client.server.IntegratedServer@20ffbd6a.
[018月2025 17:57:19.030] [Server thread/ERROR] [KubeJS Server/]: NPC/events.js#215: Error occurred while handling scheduled event callback: TypeError: Cannot find function scheduleRepeatingTask in object net.minecraft.client.server.IntegratedServer@20ffbd6a.
[018月2025 17:57:19.030] [Server thread/ERROR] [KubeJS Server/]: NPC/events.js#215: Error occurred while handling scheduled event callback: TypeError: Cannot find function scheduleRepeatingTask in object net.minecraft.client.server.IntegratedServer@20ffbd6a.
[018月2025 17:57:19.284] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#138: ⚠️ 服务器tick延迟: 6592ms (平均: 6560.5ms)
[018月2025 17:57:19.381] [Render thread/WARN] [ModernFix/]: Time from main menu to in-game was 11.053387 seconds
[018月2025 17:57:19.382] [Render thread/WARN] [ModernFix/]: Total time to load game and open world was 93.19939 seconds
[018月2025 17:57:19.438] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.chunk.region.RenderRegion$DeviceResources, which may cause instability.
[018月2025 17:57:19.559] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Saving and pausing game...
[018月2025 17:57:19.608] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:overworld
[018月2025 17:57:19.687] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_nether
[018月2025 17:57:19.689] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_end
[018月2025 17:57:21.956] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Saving and pausing game...
[018月2025 17:57:21.989] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:overworld
[018月2025 17:57:21.999] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_nether
[018月2025 17:57:22.001] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_end
[018月2025 17:57:25.666] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#138: ⚠️ 服务器tick延迟: 3729ms (平均: 3900.6ms)
[018月2025 17:57:25.758] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.immediate.model.BakedModelEncoder, which may cause instability.
[018月2025 17:57:26.460] [Render thread/WARN] [Embeddium-MixinTaintDetector/]: Mod(s) [oculus] are modifying Embeddium class me.jellysquid.mods.sodium.client.render.vertex.serializers.VertexSerializerRegistryImpl, which may cause instability.
[018月2025 17:57:28.657] [Render thread/INFO] [ModernFix/]: Building search tree for 5675 items (this may take a while)...
[018月2025 17:57:28.657] [Render thread/INFO] [ModernFix/]: Building search tree for 5675 items took 39.50 μs
[018月2025 17:58:16.056] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Saving and pausing game...
[018月2025 17:58:16.074] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:overworld
[018月2025 17:58:16.081] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_nether
[018月2025 17:58:16.083] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_end
[018月2025 17:58:20.475] [Render thread/WARN] [Catalogue/]: Skipped loading logo file from Barbeque's Delight. The file name 'null' contained illegal characters '/' or '\'
[018月2025 17:58:33.862] [Render thread/WARN] [Catalogue/]: Failed to load banner image for gml as it exceeds the maximum size of 1200x240px
[018月2025 18:00:00.005] [ftbbackups2_Worker-1/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Attempting to create an automatic backup
[018月2025 18:00:00.005] [ftbbackups2_Worker-1/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Found world folder at C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\saves\新的世界\.
[018月2025 18:00:00.073] [ftbbackups2_Worker-1/ERROR] [net.creeperhost.ftbbackups.FTBBackups/]: Current world size: 13.6MB Current Available space: 372.4MB
[018月2025 18:00:00.073] [ftbbackups2_Worker-1/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Setting world minecraft:overworld save state to true
[018月2025 18:00:00.073] [ftbbackups2_Worker-1/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Setting world minecraft:the_nether save state to true
[018月2025 18:00:00.074] [ftbbackups2_Worker-1/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Setting world minecraft:the_end save state to true
[018月2025 18:00:00.074] [ftbbackups2_Worker-1/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Writing to file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\backups\backups.json
[018月2025 18:00:00.075] [FTB Backups backup thread 0/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Waiting for world save to complete.
[018月2025 18:00:00.139] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [CHAT] 服务端备份开始
[018月2025 18:00:00.205] [FTB Backups backup thread 0/INFO] [net.creeperhost.levelio.loader.SaveInfo/]: Detected MC Version 1.20.1, Data version 3465
[018月2025 18:00:00.205] [FTB Backups backup thread 0/INFO] [net.creeperhost.levelio.loader.SaveInfo/]: Save Name: 新的世界
[018月2025 18:00:00.225] [FTB Backups backup thread 0/WARN] [net.creeperhost.levelio.data.Level/]: Did not find a region folder for level: C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\saves\新的世界\.\DIM-1
[018月2025 18:00:00.225] [FTB Backups backup thread 0/WARN] [net.creeperhost.levelio.data.Level/]: Did not find a region folder for level: C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\saves\新的世界\.\DIM1
[018月2025 18:00:01.245] [FTB Backups backup thread 0/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Backup preview created, Scan took 94ms, Capture took 970ms
[018月2025 18:00:01.411] [FTB Backups backup thread 0/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Setting world minecraft:overworld save state to false
[018月2025 18:00:01.411] [FTB Backups backup thread 0/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Setting world minecraft:the_nether save state to false
[018月2025 18:00:01.411] [FTB Backups backup thread 0/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Setting world minecraft:the_end save state to false
[018月2025 18:00:01.415] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [CHAT] Backup finished in 0m, 1s, 337ms
[018月2025 18:00:01.439] [FTB Backups backup thread 0/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Backup size 1.8MB World Size 13.6MB
[018月2025 18:00:01.439] [FTB Backups backup thread 0/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Writing to file C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\backups\backups.json
[018月2025 18:00:01.440] [FTB Backups backup thread 0/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: New backup created at C:\Users\<USER>\Desktop\.minecraft\versions\Chinese Cuisine\backups\2025-8-1_18-0-0.zip size: 1.8MB Took: 0m, 1s, 337ms Sha1: b0e390bf261e63c0452882354895e3adb6fe9d7d
[018月2025 18:01:15.710] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#138: ⚠️ 服务器tick延迟: 180049ms (平均: 4456.2ms)
[018月2025 18:01:19.057] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Saving and pausing game...
[018月2025 18:01:19.073] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:overworld
[018月2025 18:01:19.078] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_nether
[018月2025 18:01:19.079] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_end
[018月2025 18:01:21.861] [Server thread/INFO] [KubeJS Server/]: NPC/events.js#381: [NPCEvents] 系统状态 - 座位: 0/0, NPC: 6
[018月2025 18:01:21.862] [Server thread/ERROR] [KubeJS Server/]: NPC/events.js#385: Error in 'ServerEvents.tick': TypeError: redeclaration of var orderStats.
[018月2025 18:01:21.862] [Server thread/ERROR] [KubeJS Server/]: …rhino.EvaluatorException: TypeError: redeclaration of var orderStats. (server_scripts:NPC/events.js#385)
[018月2025 18:01:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.DefaultErrorReporter.runtimeError(DefaultErrorReporter.java:67)
[018月2025 18:01:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.reportRuntimeError(Context.java:133)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.reportRuntimeError(Context.java:173)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.reportRuntimeError1(Context.java:145)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ScriptableObject.putConstImpl(ScriptableObject.java:2539)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ScriptableObject.putConst(ScriptableObject.java:1571)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ScriptableObject.putConstProperty(ScriptableObject.java:1124)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ScriptRuntime.setConst(ScriptRuntime.java:1623)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Interpreter.interpretLoop(Interpreter.java:754)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at script.printSystemStatus(server_scripts:NPC/events.js:385)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at script(server_scripts:NPC/events.js:341)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Interpreter.interpret(Interpreter.java:370)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.InterpretedFunction.call(InterpretedFunction.java:72)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.callSync(Context.java:1357)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.doTopCall(Context.java:1374)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.InterpretedFunction.call(InterpretedFunction.java:70)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.callSync(Context.java:1357)
[018月2025 18:01:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ArrowFunction.call(ArrowFunction.java:42)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.callSync(Context.java:1357)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.InterfaceAdapter.invoke(InterfaceAdapter.java:125)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.VMBridge.lambda$newInterfaceProxy$0(VMBridge.java:74)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at TRANSFORMER/jdk.proxy4/jdk.proxy4.$Proxy91.onEvent(Unknown Source)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandlerContainer.handle(EventHandlerContainer.java:39)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandler.postToHandlers(EventHandler.java:304)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandler.post(EventHandler.java:272)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandler.post(EventHandler.java:226)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandler.post(EventHandler.java:189)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.server.MinecraftServer.handler$cgm000$kjs$postTickServer(MinecraftServer.java:5438)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.server.MinecraftServer.m_5705_(MinecraftServer.java:836)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.client.server.IntegratedServer.m_5705_(IntegratedServer.java:89)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.server.MinecraftServer.m_130011_(MinecraftServer.java:661)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.server.MinecraftServer.m_206580_(MinecraftServer.java:251)
[018月2025 18:01:21.864] [Server thread/ERROR] [KubeJS Server/]:   at java.lang.Thread.run(Thread.java:840)
[018月2025 18:01:22.859] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#138: ⚠️ 服务器tick延迟: 4149ms (平均: 4278.3ms)
[018月2025 18:01:24.685] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: [Quasar2323: 已将自己的游戏模式设置为生存模式]
[018月2025 18:01:24.701] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [CHAT] 已将自己的游戏模式设置为生存模式
[018月2025 18:01:43.160] [Render thread/INFO] [net.minecraft.advancements.AdvancementList/]: Loaded 86 advancements
[018月2025 18:02:21.861] [Server thread/INFO] [KubeJS Server/]: NPC/events.js#381: [NPCEvents] 系统状态 - 座位: 0/0, NPC: 6
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]: NPC/events.js#385: Error in 'ServerEvents.tick': TypeError: redeclaration of var orderStats.
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]: …rhino.EvaluatorException: TypeError: redeclaration of var orderStats. (server_scripts:NPC/events.js#385)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.DefaultErrorReporter.runtimeError(DefaultErrorReporter.java:67)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.reportRuntimeError(Context.java:133)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.reportRuntimeError(Context.java:173)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.reportRuntimeError1(Context.java:145)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ScriptableObject.putConstImpl(ScriptableObject.java:2539)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ScriptableObject.putConst(ScriptableObject.java:1571)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ScriptableObject.putConstProperty(ScriptableObject.java:1124)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ScriptRuntime.setConst(ScriptRuntime.java:1623)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Interpreter.interpretLoop(Interpreter.java:754)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at script.printSystemStatus(server_scripts:NPC/events.js:385)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at script(server_scripts:NPC/events.js:341)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Interpreter.interpret(Interpreter.java:370)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.InterpretedFunction.call(InterpretedFunction.java:72)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.callSync(Context.java:1357)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.doTopCall(Context.java:1374)
[018月2025 18:02:21.861] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.InterpretedFunction.call(InterpretedFunction.java:70)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.callSync(Context.java:1357)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ArrowFunction.call(ArrowFunction.java:42)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.callSync(Context.java:1357)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.InterfaceAdapter.invoke(InterfaceAdapter.java:125)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.VMBridge.lambda$newInterfaceProxy$0(VMBridge.java:74)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at TRANSFORMER/jdk.proxy4/jdk.proxy4.$Proxy91.onEvent(Unknown Source)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandlerContainer.handle(EventHandlerContainer.java:39)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandler.postToHandlers(EventHandler.java:304)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandler.post(EventHandler.java:272)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandler.post(EventHandler.java:226)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandler.post(EventHandler.java:189)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.server.MinecraftServer.handler$cgm000$kjs$postTickServer(MinecraftServer.java:5438)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.server.MinecraftServer.m_5705_(MinecraftServer.java:836)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.client.server.IntegratedServer.m_5705_(IntegratedServer.java:89)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.server.MinecraftServer.m_130011_(MinecraftServer.java:661)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.server.MinecraftServer.m_206580_(MinecraftServer.java:251)
[018月2025 18:02:21.862] [Server thread/ERROR] [KubeJS Server/]:   at java.lang.Thread.run(Thread.java:840)
[018月2025 18:03:21.859] [Server thread/INFO] [KubeJS Server/]: NPC/events.js#381: [NPCEvents] 系统状态 - 座位: 0/0, NPC: 6
[018月2025 18:03:21.860] [Server thread/ERROR] [KubeJS Server/]: NPC/events.js#385: Error in 'ServerEvents.tick': TypeError: redeclaration of var orderStats.
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]: …rhino.EvaluatorException: TypeError: redeclaration of var orderStats. (server_scripts:NPC/events.js#385)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.DefaultErrorReporter.runtimeError(DefaultErrorReporter.java:67)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.reportRuntimeError(Context.java:133)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.reportRuntimeError(Context.java:173)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.reportRuntimeError1(Context.java:145)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ScriptableObject.putConstImpl(ScriptableObject.java:2539)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ScriptableObject.putConst(ScriptableObject.java:1571)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ScriptableObject.putConstProperty(ScriptableObject.java:1124)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ScriptRuntime.setConst(ScriptRuntime.java:1623)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Interpreter.interpretLoop(Interpreter.java:754)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at script.printSystemStatus(server_scripts:NPC/events.js:385)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at script(server_scripts:NPC/events.js:341)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Interpreter.interpret(Interpreter.java:370)
[018月2025 18:03:21.862] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.InterpretedFunction.call(InterpretedFunction.java:72)
[018月2025 18:03:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.callSync(Context.java:1357)
[018月2025 18:03:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.doTopCall(Context.java:1374)
[018月2025 18:03:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.InterpretedFunction.call(InterpretedFunction.java:70)
[018月2025 18:03:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.callSync(Context.java:1357)
[018月2025 18:03:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.ArrowFunction.call(ArrowFunction.java:42)
[018月2025 18:03:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.Context.callSync(Context.java:1357)
[018月2025 18:03:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.InterfaceAdapter.invoke(InterfaceAdapter.java:125)
[018月2025 18:03:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …rhino.VMBridge.lambda$newInterfaceProxy$0(VMBridge.java:74)
[018月2025 18:03:21.863] [Server thread/ERROR] [KubeJS Server/]:   at TRANSFORMER/jdk.proxy4/jdk.proxy4.$Proxy91.onEvent(Unknown Source)
[018月2025 18:03:21.863] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandlerContainer.handle(EventHandlerContainer.java:39)
[018月2025 18:03:21.864] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandler.postToHandlers(EventHandler.java:304)
[018月2025 18:03:21.864] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandler.post(EventHandler.java:272)
[018月2025 18:03:21.864] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandler.post(EventHandler.java:226)
[018月2025 18:03:21.864] [Server thread/ERROR] [KubeJS Server/]:   at …kubejs.event.EventHandler.post(EventHandler.java:189)
[018月2025 18:03:21.864] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.server.MinecraftServer.handler$cgm000$kjs$postTickServer(MinecraftServer.java:5438)
[018月2025 18:03:21.864] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.server.MinecraftServer.m_5705_(MinecraftServer.java:836)
[018月2025 18:03:21.864] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.client.server.IntegratedServer.m_5705_(IntegratedServer.java:89)
[018月2025 18:03:21.864] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.server.MinecraftServer.m_130011_(MinecraftServer.java:661)
[018月2025 18:03:21.864] [Server thread/ERROR] [KubeJS Server/]:   at net.minecraft.server.MinecraftServer.m_206580_(MinecraftServer.java:251)
[018月2025 18:03:21.864] [Server thread/ERROR] [KubeJS Server/]:   at java.lang.Thread.run(Thread.java:840)
[018月2025 18:03:22.859] [Server thread/INFO] [KubeJS Server/]: NPC/test_system.js#138: ⚠️ 服务器tick延迟: 2001ms (平均: 2116.3ms)
[018月2025 18:04:06.591] [Render thread/INFO] [net.minecraft.client.Minecraft/]: Stopping!
[018月2025 18:04:06.597] [Render thread/INFO] [xaero.map.WorldMap/]: Finalizing world map session...
[018月2025 18:04:06.609] [Server thread/INFO] [net.minecraft.server.network.ServerGamePacketListenerImpl/]: Quasar2323 lost connection: 连接中断
[018月2025 18:04:06.609] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Quasar2323退出了游戏
[018月2025 18:04:06.627] [Server thread/INFO] [net.minecraft.server.network.ServerGamePacketListenerImpl/]: Stopping singleplayer server as player logged out
[018月2025 18:04:06.637] [Thread-24/INFO] [xaero.map.WorldMap/]: World map cleaned normally!
[018月2025 18:04:06.656] [Server thread/INFO] [net.creeperhost.ftbbackups.FTBBackups/]: Shutdown Complete
[018月2025 18:04:06.657] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Stopping server
[018月2025 18:04:06.657] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving players
[018月2025 18:04:06.657] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving worlds
[018月2025 18:04:06.692] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:overworld
[018月2025 18:04:06.700] [Render thread/INFO] [xaero.map.WorldMap/]: World map session finalized.
[018月2025 18:04:06.700] [Render thread/INFO] [xaero.hud.minimap.MinimapLogs/]: Xaero hud session finalized.
[018月2025 18:04:06.702] [Render thread/INFO] [mezz.jei.forge.startup.StartEventObserver/]: JEI StartEventObserver received class net.minecraftforge.client.event.ClientPlayerNetworkEvent$LoggingOut
[018月2025 18:04:06.702] [Render thread/INFO] [mezz.jei.forge.startup.StartEventObserver/]: JEI StartEventObserver transitioning state from JEI_STARTED to DISABLED
[018月2025 18:04:06.702] [Render thread/INFO] [mezz.jei.library.startup.JeiStarter/]: Stopping JEI
[018月2025 18:04:06.702] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Sending Runtime Unavailable...
[018月2025 18:04:06.702] [Render thread/INFO] [mezz.jei.forge.plugins.forge.ForgeGuiPlugin/]: Stopping JEI GUI
[018月2025 18:04:06.705] [Render thread/INFO] [mezz.jei.library.load.PluginCaller/]: Sending Runtime Unavailable took 3.266 ms
[018月2025 18:04:06.722] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_nether
[018月2025 18:04:06.724] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_end
[018月2025 18:04:06.762] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: ThreadedAnvilChunkStorage (新的世界): All chunks are saved
[018月2025 18:04:06.762] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: ThreadedAnvilChunkStorage (DIM-1): All chunks are saved
[018月2025 18:04:06.762] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: ThreadedAnvilChunkStorage (DIM1): All chunks are saved
[018月2025 18:04:06.762] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: ThreadedAnvilChunkStorage: All dimensions are saved
[018月2025 18:04:06.839] [Render thread/INFO] [ChunkBuilder/]: Stopping worker threads
[018月2025 18:04:06.868] [Render thread/INFO] [FTB Chunks/]: Shutting down map thread
