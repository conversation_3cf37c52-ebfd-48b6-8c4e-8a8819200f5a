{"itemGroup.create.garnished": "机械动力：装食", "itemGroup.create.garnished.blocks": "机械动力：装食 建筑方块", "text.garnished.conditional_effect.upon_consumption": "食用后：", "text.garnished.effect.duration": "(%s)", "text.garnished.applies_effect": "赋予 %s %s（%s）", "text.garnished.applies_effect.no_amplifier": "赋予 %s（%s）", "text.garnished.applies_effect.no_amplifier.conditional.pos": " 赋予 %s %s", "text.garnished.applies_effect.no_amplifier.conditional.neg": " 赋予 %s %s", "text.garnished.applies_effect.chance": " 概率 %s", "text.garnished.conditional_effect.from_sugar_high": "若处于高糖状态：", "text.garnished.conditional_effect.from_freezing": "若处于冰冻状态：", "text.garnished.conditional_effect.from_hunger": "若处于饥饿状态：", "text.garnished.conditional_effect.from_levitation": "若处于漂浮状态：", "text.garnished.conditional_effect.from_bad_omen_conditions.not_present": "若未处于不祥之兆状态：", "text.garnished.conditional_effect.from_bad_omen_conditions.present": "若处于不祥之兆状态：", "text.garnished.conditional_effect.from_flame": "若玩家着火：", "text.garnished.conditional_effect.cryptic_apple_cider": " 可施加力量或缓慢效果", "text.garnished.senile_sweet.brew_potion": "可用于酿造 %s 药水", "text.garnished.hold_shift": "按住 [§7Shift§8] 查看简介", "text.garnished.holding_shift": "按住 [§fShift§8] 查看简介", "text.garnished.effect.clears_poison": "清除中毒", "text.garnished.effect.clears_wither": "清除凋零", "text.garnished.incendiary_stew.desc": "希望这顿饭值得！", "item.garnished.pecan_pie_slice": "山核桃派切片", "item.garnished.phantom_beef_patty": "幻影牛肉饼", "item.garnished.phantom_burger": "幻影汉堡", "item.garnished.glow_ink_roll": "荧光墨卷", "item.garnished.glow_ink_roll_slice": "荧光墨卷切片", "item.garnished.anniversary_cake_slice": "周年蛋糕切片", "item.garnished.brownie_walnuts_piece": "核桃布朗尼小块", "text.garnished.integration.farmersdelight.missing": "请安装 Farmer's Delight（农夫乐事）！", "item.garnished.cashew": "调味腰果", "item.garnished.ungarnished_cashew": "原味腰果", "item.garnished.cinder_cashew": "焦壳腰果", "item.garnished.melted_cinder_cashew": "加热焦壳腰果", "item.garnished.sweetened_cashew": "蜜渍腰果", "block.garnished.cashew_sapling": "破壳腰果", "item.garnished.cracked_cashew": "破壳腰果", "item.garnished.cinder_cashew_speed": "冥火腰果", "item.garnished.honeyed_cashew": "蜂蜜腰果", "item.garnished.chocolate_glazed_cashew": "巧克力腰果", "item.garnished.walnut": "调味核桃", "item.garnished.ungarnished_walnut": "原味核桃", "item.garnished.cinder_walnut": "焦壳核桃", "item.garnished.melted_cinder_walnut": "加热焦壳核桃", "item.garnished.sweetened_walnut": "蜜渍核桃", "block.garnished.walnut_sapling": "破壳核桃", "item.garnished.cracked_walnut": "破壳核桃", "item.garnished.cinder_walnut_strength": "冥火核桃", "item.garnished.honeyed_walnut": "蜂蜜核桃", "item.garnished.chocolate_glazed_walnut": "巧克力核桃", "item.garnished.almond": "调味杏仁", "item.garnished.ungarnished_almond": "原味杏仁", "item.garnished.cinder_almond": "焦壳杏仁", "item.garnished.melted_cinder_almond": "加热焦壳杏仁", "item.garnished.sweetened_almond": "蜜渍杏仁", "block.garnished.almond_sapling": "破壳杏仁", "item.garnished.cracked_almond": "破壳杏仁", "item.garnished.cinder_almond_haste": "冥火杏仁", "item.garnished.honeyed_almond": "蜂蜜杏仁", "item.garnished.chocolate_glazed_almond": "巧克力杏仁", "item.garnished.pecan": "调味山核桃", "item.garnished.ungarnished_pecan": "原味山核桃", "item.garnished.cinder_pecan": "焦壳山核桃", "item.garnished.melted_cinder_pecan": "加热焦壳山核桃", "item.garnished.sweetened_pecan": "蜜渍山核桃", "block.garnished.pecan_sapling": "破壳山核桃", "item.garnished.cracked_pecan": "破壳山核桃", "item.garnished.cinder_pecan_resistance": "冥火山核桃", "item.garnished.honeyed_pecan": "蜂蜜山核桃", "item.garnished.chocolate_glazed_pecan": "巧克力山核桃", "item.garnished.pistachio": "调味开心果", "item.garnished.ungarnished_pistachio": "原味开心果", "item.garnished.cinder_pistachio": "焦壳开心果", "item.garnished.melted_cinder_pistachio": "加热焦壳开心果", "item.garnished.sweetened_pistachio": "蜜渍开心果", "block.garnished.pistachio_sapling": "破壳开心果", "item.garnished.cracked_pistachio": "破壳开心果", "item.garnished.cinder_pistachio_night_vision": "冥火开心果", "item.garnished.honeyed_pistachio": "蜂蜜开心果", "item.garnished.chocolate_glazed_pistachio": "巧克力开心果", "item.garnished.macadamia": "调味夏威夷果", "item.garnished.ungarnished_macadamia": "原味夏威夷果", "item.garnished.cinder_macadamia": "焦壳夏威夷果", "item.garnished.melted_cinder_macadamia": "加热焦壳夏威夷果", "item.garnished.sweetened_macadamia": "蜜渍夏威夷果", "block.garnished.macadamia_sapling": "破壳夏威夷果", "item.garnished.cracked_macadamia": "破壳夏威夷果", "item.garnished.cinder_macadamia_fire_resistance": "冥火夏威夷果", "item.garnished.honeyed_macadamia": "蜂蜜夏威夷果", "item.garnished.chocolate_glazed_macadamia": "巧克力夏威夷果", "item.garnished.peanut": "调味花生", "item.garnished.ungarnished_peanut": "原味花生", "item.garnished.cinder_peanut": "焦壳花生", "item.garnished.melted_cinder_peanut": "加热焦壳花生", "item.garnished.sweetened_peanut": "蜜渍花生", "block.garnished.peanut_sapling": "破壳花生", "item.garnished.cracked_peanut": "破壳花生", "item.garnished.cinder_peanut_effect": "冥火花生", "item.garnished.honeyed_peanut": "蜂蜜花生", "item.garnished.chocolate_glazed_peanut": "巧克力花生", "item.garnished.hazelnut": "调味榛子", "item.garnished.ungarnished_hazelnut": "原味榛子", "item.garnished.cinder_hazelnut": "焦壳榛子", "item.garnished.melted_cinder_hazelnut": "加热焦壳榛子", "item.garnished.sweetened_hazelnut": "蜜渍榛子", "block.garnished.hazelnut_sapling": "破壳榛子", "item.garnished.cracked_hazelnut": "破壳榛子", "item.garnished.cinder_hazelnut_speed_potent": "冥火榛子", "item.garnished.honeyed_hazelnut": "蜂蜜榛子", "item.garnished.chocolate_glazed_hazelnut": "巧克力榛子", "item.garnished.chestnut": "调味栗子", "item.garnished.ungarnished_chestnut": "原味栗子", "item.garnished.cinder_chestnut": "焦壳栗子", "item.garnished.melted_cinder_chestnut": "加热焦壳栗子", "item.garnished.sweetened_chestnut": "蜜渍栗子", "block.garnished.chestnut_sapling": "破壳栗子", "item.garnished.cracked_chestnut": "破壳栗子", "item.garnished.cinder_chestnut_slow_falling": "冥火栗子", "item.garnished.honeyed_chestnut": "蜂蜜栗子", "item.garnished.chocolate_glazed_chestnut": "巧克力栗子", "item.garnished.garnished_sweet_berries": "调味甜浆果", "item.garnished.honeyed_sweet_berries": "蜂蜜甜浆果", "item.garnished.nut_mix": "坚果混合", "item.garnished.sweetened_nut_mix": "蜜渍坚果混合", "item.garnished.honeyed_nut_mix": "蜂蜜坚果混合", "item.garnished.chocolate_glazed_nut_mix": "巧克力坚果混合", "item.garnished.crushed_salt": "研磨盐", "item.garnished.unprocessed_salt_compound": "未加工盐化合物", "item.garnished.salt_compound": "盐化合物", "item.garnished.salad": "沙拉", "item.garnished.incomplete_phantom_steak": "未完成的幻影牛排", "item.garnished.phantom_steak": "幻影牛排", "item.garnished.garnished_meal": "调味餐", "item.garnished.apple_cider": "瓶装苹果酒", "item.garnished.cryptic_apple_cider": "瓶装神秘苹果酒", "item.garnished.bitter_apple_cider": "瓶装苦苹果酒", "item.garnished.peanut_oil": "瓶装花生油", "item.garnished.cashew_fruit": "腰果果", "item.garnished.bitter_almond": "苦杏仁", "item.garnished.brownie_walnuts": "核桃布朗尼", "item.garnished.incomplete_brownie_walnuts": "未完成的核桃布朗尼", "item.garnished.incomplete_cookie": "未完成的曲奇", "item.garnished.pecan_pie": "山核桃派", "item.garnished.cashew_cookie": "腰果曲奇", "item.garnished.incomplete_cashew_cookie": "未完成的腰果曲奇", "item.garnished.almond_cheese": "杏仁奶酪", "item.garnished.peanut_cinder_sandwich": "花生油焦炭三明治", "item.garnished.incomplete_peanut_cinder_sandwich": "未完成的花生油焦炭三明治", "item.garnished.tophet_brew": "地狱酿酒", "item.garnished.incomplete_tophet_brew": "未完成的地狱酿酒", "item.garnished.grim_stew": "阴郁炖菜", "item.garnished.wrapped_warped_tangle": "包裹的诡异缠结", "item.garnished.wrapped_crimson_tangle": "包裹的猩红缠结", "item.garnished.wrapped_sepia_tangle": "包裹的褐色缠结", "item.garnished.weeping_tangle": "哀泣缠结", "item.garnished.incomplete_tangle": "未完成的缠结", "item.garnished.incomplete_weeping_tangle": "未完成的哀泣缠结", "item.garnished.cinder_roll": "焦炭蘑菇卷", "item.garnished.incomplete_cinder_roll": "未完成的焦炭蘑菇卷", "item.garnished.blazing_delight": "炽焰美味", "item.garnished.cashew_mixture": "瓶装发酵腰果混合物", "item.garnished.incomplete_cashew_mixture": "未完成的发酵腰果混合物", "item.garnished.crushed_warped_fungus": "粉碎的诡异真菌", "item.garnished.crushed_crimson_fungus": "粉碎的猩红真菌", "item.garnished.crushed_sepia_fungus": "粉碎的褐色真菌", "item.garnished.enflamed_mandible": "炽热颚骨", "item.garnished.crushed_ender_pearl": "末影尘", "item.garnished.crimson_tusk": "猩红獠牙", "item.garnished.incomplete_crimson_tusk": "未完成的猩红獠牙", "item.garnished.empty_crimson_tusk": "空的猩红獠牙", "item.garnished.hardened_wrap": "硅化硬包裹物", "item.garnished.incomplete_hardened_wrap": "未完成的硅化硬包裹物", "item.garnished.crushed_shroomlight": "粉碎的菌光体", "item.garnished.nut_flour": "坚果面粉", "item.garnished.brittle_dust": "脆弱之尘", "item.garnished.senile_dust": "老朽之尘", "item.garnished.senile_spread": "老朽涂抹酱", "item.garnished.putrid_stew": "腐臭炖菜", "text.garnished.putrid_stew.cures_nut_allergy": "治疗坚果过敏", "item.garnished.soul_khana": "灵魂盛宴", "item.garnished.spirited_concoction": "活力混合饮", "effect.garnished.spirited_resistance": "活力抗性", "effect.garnished.aversion": "厌恶", "item.minecraft.potion.effect.aversion": "厌恶药水", "item.minecraft.splash_potion.effect.aversion": "飞溅厌恶药水", "item.minecraft.lingering_potion.effect.aversion": "滞留厌恶药水", "item.minecraft.tipped_arrow.effect.aversion": "附厌恶效果的箭", "block.garnished.sepia_fungus": "赭黄菌", "block.garnished.sepia_stem": "赭黄菌柄", "block.garnished.stripped_sepia_stem": "剥皮赭黄菌柄", "block.garnished.sepia_hyphae": "赭黄菌丝", "block.garnished.stripped_sepia_hyphae": "剥皮赭黄菌丝", "block.garnished.sepia_planks": "赭黄菌木板", "block.garnished.sepia_wart_block": "赭黄疣块", "block.garnished.sepia_slab": "赭黄菌台阶", "block.garnished.sepia_stairs": "赭黄菌楼梯", "block.garnished.sepia_door": "赭黄菌门", "block.garnished.sepia_trapdoor": "赭黄菌活板门", "block.garnished.soul_roots": "灵魂根须", "item.garnished.senile_sweet_blackstone": "年老甜块", "item.garnished.senile_sweet_basalt": "年老甜块", "item.garnished.senile_sweet_scoria": "年老甜块", "item.garnished.senile_sweet_scorchia": "年老甜块", "item.garnished.incomplete_senile_sweet_blackstone": "未完成的年老甜块", "item.garnished.incomplete_senile_sweet_basalt": "未完成的年老甜块", "item.garnished.incomplete_senile_sweet_scoria": "未完成的年老甜块", "item.garnished.incomplete_senile_sweet_scorchia": "未完成的年老甜块", "item.garnished.nut_nacho": "坚果玉米片", "item.garnished.nut_nacho_bowl": "一碗坚果玉米片", "item.minecraft.potion.effect.blindness": "失明药水", "item.minecraft.splash_potion.effect.blindness": "飞溅失明药水", "item.minecraft.lingering_potion.effect.blindness": "滞留失明药水", "item.minecraft.tipped_arrow.effect.blindness": "附失明效果的箭", "item.garnished.ender_jelly_bottle": "终界果冻瓶", "item.garnished.chorus_cocktail": "歌声鸡尾酒", "item.garnished.cosmic_brew": "宇宙酿造物", "item.garnished.desolate_stew": "荒寂炖菜", "item.garnished.ethereal_concoction": "空灵混合饮", "item.garnished.void_mixture": "虚空混合物", "item.garnished.chorus_bowl": "歌声果碗", "item.garnished.chorus_cookie": "歌声饼干", "item.garnished.ender_jelly": "终界果冻", "item.garnished.illuminating_cocktail": "照明鸡尾酒", "item.garnished.void_dust": "虚空之尘", "item.garnished.chorus_tuft": "歌声簇", "item.garnished.hollowed_chorus_fruit": "空心歌声果", "item.garnished.ethereal_compound": "空灵复合物", "item.garnished.cosmic_powder": "宇宙粉末", "item.garnished.mulch": "覆盖物", "item.garnished.mud_pie": "泥巴派", "item.garnished.desolate_spread": "荒寂酱料", "block.garnished.ender_jelly_block": "终界果冻块", "block.garnished.ungarnished_nut_block": "未装饰坚果块", "block.garnished.garnished_nut_block": "装饰坚果块", "block.garnished.small_chorus_plant": "小型歌声植物", "block.garnished.barren_roots": "枯萎根须", "effect.garnished.cognate": "共鸣", "effect.garnished.flagrant": "炽热", "item.minecraft.potion.effect.flagrant": "炽热药水", "item.minecraft.splash_potion.effect.flagrant": "飞溅炽热药水", "item.minecraft.lingering_potion.effect.flagrant": "滞留炽热药水", "item.minecraft.tipped_arrow.effect.flagrant": "附炽热效果的箭", "item.garnished.wooden_hatchet": "木斧", "item.garnished.stone_hatchet": "石斧", "item.garnished.iron_hatchet": "铁斧", "item.garnished.golden_hatchet": "金斧", "item.garnished.diamond_hatchet": "钻石斧", "item.garnished.netherite_hatchet": "下界合金斧", "enchantment.garnished.salvaging": "回收", "enchantment.garnished.ravaging": "破坏", "enchantment.garnished.striking": "猛击", "enchantment.garnished.quick_step": "疾行", "enchantment.garnished.rejuvenate": "复苏", "enchantment.garnished.leeching_curse": "吸血诅咒", "item.garnished.jade_hatchet": "翡翠短柄斧", "item.garnished.topaz_hatchet": "黄玉短柄斧", "item.garnished.ametrine_hatchet": "紫晶短柄斧", "item.garnished.aquamarine_hatchet": "海蓝宝石短柄斧", "item.garnished.ruby_hatchet": "红宝石短柄斧", "item.garnished.sapphire_hatchet": "蓝宝石短柄斧", "item.garnished.warden_hatchet": "守卫者短柄斧", "item.garnished.rose_gold_hatchet": "玫瑰金短柄斧", "item.garnished.gilded_netherite_hatchet": "镀金下界合金短柄斧", "item.garnished.nether_ruby_hatchet": "下界红宝石短柄斧", "item.garnished.flaming_ruby_hatchet": "火红宝石短柄斧", "item.garnished.cincinnasite_hatchet": "辛辛那石短柄斧", "item.garnished.cincinnasite_diamond_hatchet": "辛辛那石钻石短柄斧", "item.garnished.nether_quartz_hatchet": "下界石英短柄斧", "item.garnished.certus_quartz_hatchet": "赛特斯石英短柄斧", "item.garnished.fluix_hatchet": "流晶短柄斧", "item.garnished.thallasium_hatchet_head": "塔拉斯金斧头", "item.garnished.terminite_hatchet_head": "终极合金斧头", "item.garnished.aeternium_hatchet_head": "永恒合金斧头", "item.garnished.thallasium_hatchet": "塔拉斯金短柄斧", "item.garnished.terminite_hatchet": "终极合金短柄斧", "item.garnished.aeternium_hatchet": "永恒合金短柄斧", "item.garnished.zinc_hatchet": "锌短柄斧", "item.garnished.rose_quartz_hatchet": "镀金石英短柄斧", "item.garnished.experience_hatchet": "经验短柄斧", "item.garnished.copper_hatchet": "铜短柄斧", "item.garnished.brass_hatchet": "黄铜短柄斧", "item.garnished.blazing_hatchet": "炽焰短柄斧", "text.garnished.integration.create_sa.missing": "请安装 Create：Stuff & Additions！", "text.garnished.integration.deeperdarker.missing": "请安装 Deeper & Darker！", "text.garnished.integration.additionaladditions.missing": "请安装 Additional Additions！", "text.garnished.integration.mythicupgrades.missing": "请安装 Mythic Upgrades！", "text.garnished.integration.ae2.missing": "请安装 Applied Energistics 2！", "text.garnished.integration.betterend.missing": "请安装 Better End！", "text.garnished.integration.betternether.missing": "请安装 Better Nether！", "garnished.aquamarine_hatchet.description": "命中敌人时冻结其行动。", "garnished.ametrine_hatchet.description": "命中敌人时使其漂浮。", "garnished.topaz_hatchet.description": "耐久度为下界合金斧的三倍。", "garnished.ruby_hatchet.description": "命中敌人时冻结其行动。", "garnished.jade_hatchet.description": "命中敌人时冻结其行动。", "garnished.sapphire_hatchet.description": "根据敌人生命值造成百分比伤害。", "enchantment.garnished.ravaging.desc": "击杀某些生物时额外掉落物品，生命值低于一半时增加攻击力。", "enchantment.garnished.salvaging.desc": "击杀某些生物时额外掉落物品。", "enchantment.garnished.striking.desc": "增加斧头的攻击伤害。", "enchantment.garnished.quick_step.desc": "当生命值低于一半时提高移动速度。", "enchantment.garnished.rejuvenate.desc": "每次攻击生物时恢复生命值。", "enchantment.garnished.leeching_curse.desc": "每次攻击生物时会伤害自身。", "item.garnished.molten_remnant": "熔火残余", "item.garnished.molten_stew": "熔火炖汤", "block.garnished.nut_log": "坚果原木", "block.garnished.stripped_nut_log": "去皮坚果原木", "block.garnished.nut_wood": "坚果木", "block.garnished.stripped_nut_wood": "去皮坚果木", "block.garnished.nut_planks": "坚果木板", "block.garnished.nut_slab": "坚果木台阶", "block.garnished.nut_stairs": "坚果木楼梯", "block.garnished.nut_door": "坚果木门", "block.garnished.nut_trapdoor": "坚果活板门", "block.garnished.nut_fence": "坚果栅栏", "block.garnished.nut_fence_gate": "坚果栅栏门", "block.garnished.nut_button": "坚果按钮", "block.garnished.nut_pressure_plate": "坚果压力板", "block.garnished.nut_window": "坚果窗", "block.garnished.nut_window_pane": "坚果窗格", "block.garnished.nut_sign": "坚果告示牌", "block.garnished.nut_hanging_sign": "坚果吊挂告示牌", "item.garnished.nut_boat": "坚果木船", "item.garnished.nut_chest_boat": "坚果运输船", "entity.garnished.nut_boat": "船", "entity.garnished.nut_chest_boat": "运输船", "block.garnished.sepia_fence": "褐调栅栏", "block.garnished.sepia_fence_gate": "褐调栅栏门", "block.garnished.sepia_button": "褐调按钮", "block.garnished.sepia_pressure_plate": "褐调压力板", "block.garnished.sepia_window": "褐调窗", "block.garnished.sepia_window_pane": "褐调窗格", "block.garnished.sepia_sign": "褐调告示牌", "block.garnished.sepia_hanging_sign": "褐调吊挂告示牌", "block.garnished.polar_bear_hide_block": "北极熊皮方块", "block.garnished.packed_polar_bear_hide_block": "压缩北极熊皮方块", "block.garnished.numbing_parchment_block": "麻痹羊皮纸方块", "block.garnished.numbing_parchment_carpet": "麻痹布料", "block.garnished.amber_remnant_block": "琥珀残余块", "block.garnished.amber_remnant_slab": "琥珀残余台阶", "block.garnished.amber_remnant_stairs": "琥珀残余楼梯", "block.garnished.amber_remnant_wall": "琥珀残余墙", "block.garnished.amber_remnant_bricks": "琥珀残余砖", "block.garnished.amber_remnant_brick_slab": "琥珀残砖台阶", "block.garnished.amber_remnant_brick_stairs": "琥珀残砖楼梯", "block.garnished.amber_remnant_brick_wall": "琥珀残砖墙", "block.garnished.anniversary_cake": "周年庆蛋糕", "block.garnished.blue_zultanite_brick_stairs": "切割蓝色祖尔坦石砖楼梯", "block.garnished.cut_pink_zultanite_wall": "切割粉红祖尔坦石墙", "item.garnished.baklava": "果仁蜜饼", "item.garnished.slime_drop": "史莱姆软糖", "item.garnished.slime_drop_red": "红色乳香软糖", "item.garnished.slime_drop_orange": "橙色乳香软糖", "item.garnished.slime_drop_yellow": "黄色乳香软糖", "item.garnished.slime_drop_green": "绿色乳香软糖", "item.garnished.slime_drop_blue": "蓝色乳香软糖", "item.garnished.slime_drop_purple": "紫色乳香软糖", "item.garnished.mastic_resin": "乳香树脂", "item.garnished.mastic_resin_red": "红色乳香树脂", "item.garnished.mastic_resin_orange": "橙色乳香树脂", "item.garnished.mastic_resin_yellow": "黄色乳香树脂", "item.garnished.mastic_resin_green": "绿色乳香树脂", "item.garnished.mastic_resin_blue": "蓝色乳香树脂", "item.garnished.mastic_resin_purple": "紫色乳香树脂", "item.garnished.mastic_paste": "乳香膏", "item.garnished.mastic_paste_red": "红色乳香膏", "item.garnished.mastic_paste_orange": "橙色乳香膏", "item.garnished.mastic_paste_yellow": "黄色乳香膏", "item.garnished.mastic_paste_green": "绿色乳香膏", "item.garnished.mastic_paste_blue": "蓝色乳香膏", "item.garnished.mastic_paste_purple": "紫色乳香膏", "item.garnished.venerable_dough": "珍贵面团", "item.garnished.venerable_delicacy_red": "红色珍味点心", "item.garnished.venerable_delicacy_orange": "橙色珍味点心", "item.garnished.venerable_delicacy_yellow": "黄色珍味点心", "item.garnished.venerable_delicacy_green": "绿色珍味点心", "item.garnished.venerable_delicacy_blue": "蓝色珍味点心", "item.garnished.venerable_delicacy_purple": "紫色尊享佳肴", "item.garnished.unfinished_venerable_delicacy": "未完成的尊享佳肴", "item.garnished.merry_treat": "欢欣美味", "item.garnished.galactic_cane": "星界甘蔗", "item.garnished.icy_mastic_chunk": "冰凉乳香块", "item.garnished.coal_truffle": "煤炭松露", "item.garnished.sturdy_waffle": "坚实华夫饼", "item.garnished.volatile_dust": "挥发之尘", "effect.garnished.sugar_high": "糖分飙升", "effect.garnished.sanctity": "圣洁", "item.minecraft.potion.effect.sanctity": "圣洁药水", "item.minecraft.splash_potion.effect.sanctity": "圣洁喷溅药水", "item.minecraft.lingering_potion.effect.sanctity": "圣洁滞留药水", "item.minecraft.tipped_arrow.effect.sanctity": "附圣洁效果的箭", "block.garnished.mastic_block": "乳香树脂块", "block.garnished.red_mastic_block": "红色乳香树脂块", "block.garnished.orange_mastic_block": "橙色乳香树脂块", "block.garnished.yellow_mastic_block": "黄色乳香树脂块", "block.garnished.green_mastic_block": "绿色乳香树脂块", "block.garnished.blue_mastic_block": "蓝色乳香树脂块", "block.garnished.purple_mastic_block": "紫色乳香树脂块", "block.garnished.abyssal_stone": "深渊石", "block.garnished.abyssal_stone_slab": "深渊石台阶", "block.garnished.abyssal_stone_stairs": "深渊石楼梯", "block.garnished.abyssal_stone_wall": "深渊石墙", "block.garnished.abyssal_stone_bricks": "切制深渊石砖", "block.garnished.abyssal_stone_brick_slab": "切制深渊石砖台阶", "block.garnished.abyssal_stone_brick_stairs": "切制深渊石砖楼梯", "block.garnished.abyssal_stone_brick_wall": "切制深渊石砖墙", "block.garnished.small_abyssal_stone_bricks": "小型深渊石砖", "block.garnished.small_abyssal_stone_brick_slab": "小型深渊石砖台阶", "block.garnished.small_abyssal_stone_brick_stairs": "小型深渊石砖楼梯", "block.garnished.small_abyssal_stone_brick_wall": "小型深渊石砖墙", "block.garnished.polished_abyssal_stone": "抛光切制深渊石", "block.garnished.polished_abyssal_stone_slab": "抛光切制深渊石台阶", "block.garnished.polished_abyssal_stone_stairs": "抛光切制深渊石楼梯", "block.garnished.polished_abyssal_stone_wall": "抛光切制深渊石墙", "block.garnished.cut_abyssal_stone": "切制深渊石", "block.garnished.cut_abyssal_stone_slab": "切制深渊石台阶", "block.garnished.cut_abyssal_stone_stairs": "切制深渊石楼梯", "block.garnished.cut_abyssal_stone_wall": "切制深渊石墙", "block.garnished.chiseled_abyssal_stone_bricks": "浮雕深渊石砖", "block.garnished.carnotite": "硒铀矿石", "block.garnished.carnotite_slab": "硒铀矿石台阶", "block.garnished.carnotite_stairs": "硒铀矿石楼梯", "block.garnished.carnotite_wall": "硒铀矿石墙", "block.garnished.carnotite_bricks": "切制硒铀矿砖", "block.garnished.carnotite_brick_slab": "切制硒铀矿砖台阶", "block.garnished.carnotite_brick_stairs": "切制硒铀矿砖楼梯", "block.garnished.carnotite_brick_wall": "切制硒铀矿砖墙", "block.garnished.small_carnotite_bricks": "小型硒铀矿砖", "block.garnished.small_carnotite_brick_slab": "小型硒铀矿砖台阶", "block.garnished.small_carnotite_brick_stairs": "小型硒铀矿砖楼梯", "block.garnished.small_carnotite_brick_wall": "小型硒铀矿砖墙", "block.garnished.polished_carnotite": "抛光切制硒铀矿石", "block.garnished.polished_carnotite_slab": "抛光切制硒铀矿石台阶", "block.garnished.polished_carnotite_stairs": "抛光切制硒铀矿石楼梯", "block.garnished.polished_carnotite_wall": "抛光切制硒铀矿石墙", "block.garnished.cut_carnotite": "切制硒铀矿石", "block.garnished.cut_carnotite_slab": "切制硒铀矿石台阶", "block.garnished.cut_carnotite_stairs": "切制硒铀矿石楼梯", "block.garnished.cut_carnotite_wall": "切制硒铀矿石墙", "block.garnished.chiseled_carnotite_bricks": "浮雕硒铀矿砖", "block.garnished.unstable_stone": "不稳定石", "block.garnished.unstable_stone_slab": "不稳定石台阶", "block.garnished.unstable_stone_stairs": "不稳定石楼梯", "block.garnished.unstable_stone_wall": "不稳定石墙", "block.garnished.unstable_stone_bricks": "切制不稳定石砖", "block.garnished.unstable_stone_brick_slab": "切制不稳定石砖台阶", "block.garnished.unstable_stone_brick_stairs": "切制不稳定石砖楼梯", "block.garnished.unstable_stone_brick_wall": "切制不稳定石砖墙", "block.garnished.small_unstable_stone_bricks": "小型不稳定石砖", "block.garnished.small_unstable_stone_brick_slab": "小型不稳定石砖台阶", "block.garnished.small_unstable_stone_brick_stairs": "小型不稳定石砖楼梯", "block.garnished.small_unstable_stone_brick_wall": "小型不稳定石砖墙", "block.garnished.polished_unstable_stone": "抛光切制不稳定石", "block.garnished.polished_unstable_stone_slab": "抛光切制不稳定石台阶", "block.garnished.polished_unstable_stone_stairs": "抛光切制不稳定石楼梯", "block.garnished.polished_unstable_stone_wall": "抛光切制不稳定石墙", "block.garnished.cut_unstable_stone": "切制不稳定石", "block.garnished.cut_unstable_stone_slab": "切制不稳定石台阶", "block.garnished.cut_unstable_stone_stairs": "切制不稳定石楼梯", "block.garnished.cut_unstable_stone_wall": "切制不稳定石墙", "block.garnished.chiseled_unstable_stone_bricks": "浮雕不稳定石砖", "item.garnished.ghast_tendril": "恶魂触须", "item.garnished.raw_tenebrous_meat": "生幽暗肉", "item.garnished.cooked_tenebrous_meat": "熟幽暗肉", "item.garnished.aching_tenebrous_clump": "隐痛幽暗团块", "item.garnished.gloomy_gathering": "忧郁拼盘", "item.garnished.fishy_surprise": "鱼香惊喜", "item.garnished.ghandercken": "甘德肯", "item.garnished.walnut_gorge_cream": "核桃谷奶油碗", "item.garnished.cashew_sorbet_scoop": "腰果雪酪球", "item.garnished.cashew_sorbet_delight": "腰果雪酪盛宴", "item.garnished.wheat_graze": "麦香嚼趣", "item.garnished.porkchop_and_graze": "猪排配麦嚼", "item.garnished.murky_jelly": "混沌果冻", "item.garnished.nutty_melody": "坚果旋律", "item.garnished.incomplete_nutty_melody": "未完成的坚果旋律", "item.garnished.murky_macadamia_malice": "混沌夏威夷果之恶", "item.garnished.cackling_pie": "咯咯笑派", "item.garnished.yam_o_glow_puffs": "闪耀山药泡泡", "item.garnished.shining_dish": "闪耀料理", "item.garnished.muesli": "什锦麦片", "item.garnished.incomplete_muesli": "未完成的什锦麦片", "item.garnished.lustrous_pearl": "光泽珍珠", "item.garnished.vast_brew": "浩瀚浓汤", "item.garnished.vermilion_stew": "朱红炖菜", "item.garnished.galvanic_haunting": "电灵缠绕", "item.garnished.coral_wrapping": "珊瑚包裹", "item.garnished.bewildered_pastry": "迷惘酥点", "item.garnished.dusty_regale": "尘封盛宴", "item.garnished.incendiary_stew": "燃火炖菜", "block.garnished.vermilion_kelp": "朱红海带", "item.garnished.dried_vermilion_kelp": "干朱红海带", "block.garnished.dried_vermilion_kelp_block": "干朱红海带块", "block.garnished.dulse_kelp": "紫菜海带", "item.garnished.dried_dulse_kelp": "干紫菜海带", "block.garnished.dried_dulse_kelp_block": "干紫菜海带块", "block.garnished.voltaic_sea_grass": "电光海草", "block.garnished.ritualistic_stone": "仪式石", "block.garnished.ritualistic_stone_slab": "仪式石台阶", "block.garnished.ritualistic_stone_stairs": "仪式石楼梯", "block.garnished.ritualistic_stone_wall": "仪式石墙", "block.garnished.ritualistic_stone_bricks": "雕刻仪式石砖", "block.garnished.ritualistic_stone_brick_slab": "雕刻仪式石砖台阶", "block.garnished.ritualistic_stone_brick_stairs": "雕刻仪式石砖楼梯", "block.garnished.ritualistic_stone_brick_wall": "雕刻仪式石砖墙", "block.garnished.small_ritualistic_stone_bricks": "小型仪式石砖", "block.garnished.small_ritualistic_stone_brick_slab": "小型仪式石砖台阶", "block.garnished.small_ritualistic_stone_brick_stairs": "小型仪式石砖楼梯", "block.garnished.small_ritualistic_stone_brick_wall": "小型仪式石砖墙", "block.garnished.polished_ritualistic_stone": "抛光雕刻仪式石", "block.garnished.polished_ritualistic_stone_slab": "抛光雕刻仪式石台阶", "block.garnished.polished_ritualistic_stone_stairs": "抛光雕刻仪式石楼梯", "block.garnished.polished_ritualistic_stone_wall": "抛光雕刻仪式石墙", "block.garnished.cut_ritualistic_stone": "切制仪式石", "block.garnished.cut_ritualistic_stone_slab": "切制仪式石台阶", "block.garnished.cut_ritualistic_stone_stairs": "切制仪式石楼梯", "block.garnished.cut_ritualistic_stone_wall": "切制仪式石墙", "block.garnished.chiseled_ritualistic_stone_bricks": "錾制仪式石砖", "block.garnished.smooth_ritualistic_stone": "平滑仪式石", "block.garnished.smooth_ritualistic_stone_stairs": "平滑仪式石楼梯", "block.garnished.smooth_ritualistic_stone_slab": "平滑仪式石台阶", "block.garnished.smooth_ritualistic_stone_wall": "平滑仪式石墙", "block.garnished.smooth_unstable_stone": "平滑不稳石", "block.garnished.smooth_unstable_stone_stairs": "平滑不稳石楼梯", "block.garnished.smooth_unstable_stone_slab": "平滑不稳石台阶", "block.garnished.smooth_unstable_stone_wall": "平滑不稳石墙", "block.garnished.smooth_abyssal_stone": "平滑深渊石", "block.garnished.smooth_abyssal_stone_stairs": "平滑深渊石楼梯", "block.garnished.smooth_abyssal_stone_slab": "平滑深渊石台阶", "block.garnished.smooth_abyssal_stone_wall": "平滑深渊石墙", "block.garnished.smooth_carnotite": "平滑硒铀矿", "block.garnished.smooth_carnotite_stairs": "平滑硒铀矿楼梯", "block.garnished.smooth_carnotite_slab": "平滑硒铀矿台阶", "block.garnished.smooth_carnotite_wall": "平滑硒铀矿墙", "block.garnished.garnish_compound_block": "装饰复合块", "block.garnished.salt_compound_block": "盐复合块", "block.garnished.ethereal_compound_block": "虚灵复合块", "block.garnished.mulch_block": "腐殖块", "item.garnished.prickly_pear": "仙人果", "item.garnished.bamboo_clod": "竹团", "item.garnished.nopalito_wrap": "仙人掌卷", "item.garnished.nopalito_wrap_supreme": "至尊仙人掌卷", "item.garnished.prickly_pear_stew": "仙人果炖菜", "item.garnished.sinopia_rock_sweet": "赤岩甜点", "item.garnished.thorn_on_a_stick": "荆棘串", "item.garnished.overgrown_brew": "蔓生浓汤", "item.garnished.stew_of_the_damned": "诅咒之炖", "item.garnished.rosy_cocktail": "玫瑰鸡尾酒", "item.garnished.antique_swathe": "古饰包裹", "item.garnished.amber_remnant": "琥珀残片", "item.garnished.shattered_amber_remnant": "碎琥珀残片", "item.garnished.bristled_flour": "刺麦粉", "item.garnished.bristled_tortilla": "刺麦薄饼", "item.garnished.solemn_dust": "肃穆之尘", "effect.garnished.thorns": "荆棘", "effect.garnished.mummification": "木乃伊化", "item.minecraft.potion.effect.mummification": "木乃伊化药水", "item.minecraft.splash_potion.effect.mummification": "木乃伊化喷溅药水", "item.minecraft.lingering_potion.effect.mummification": "木乃伊化滞留药水", "item.minecraft.tipped_arrow.effect.mummification": "木乃伊化箭", "item.garnished.frost": "霜华", "item.garnished.numbing_parchment": "麻痹羊皮纸", "item.garnished.polar_bear_hide": "北极熊皮", "item.garnished.chilled_apple": "冰苹果", "item.garnished.raw_polar_bear_meat": "生北极熊肉", "item.garnished.cooked_polar_bear_meat": "熟北极熊肉", "item.garnished.frosted_dessert": "冰霜甜品", "item.garnished.void_stroganoff": "虚空斯特罗加诺夫", "item.garnished.explorers_concoction": "探险者的混合饮", "item.garnished.polar_hide_scratch_paper": "熊皮草稿纸", "effect.garnished.freezing": "冻结", "item.minecraft.potion.effect.freezing": "冻结药水", "item.minecraft.splash_potion.effect.freezing": "冻结喷溅药水", "item.minecraft.lingering_potion.effect.freezing": "冻结滞留药水", "item.minecraft.tipped_arrow.effect.freezing": "冻结箭", "item.minecraft.potion.effect.long_freezing": "长效冻结药水", "item.minecraft.splash_potion.effect.long_freezing": "长效冻结喷溅药水", "item.minecraft.tipped_arrow.effect.long_freezing": "冻结药水箭", "recipe.garnished.fan_freezing": "批量冷冻", "create.recipe.garnished.red_dye_blowing.fan": "红色液态胶脂风扇", "recipe.garnished.red_dye_blowing": "红色批量染色", "create.recipe.garnished.orange_dye_blowing.fan": "橙色液态胶脂风扇", "recipe.garnished.orange_dye_blowing": "橙色批量染色", "create.recipe.garnished.yellow_dye_blowing.fan": "黄色液态胶脂风扇", "recipe.garnished.yellow_dye_blowing": "黄色批量染色", "create.recipe.garnished.green_dye_blowing.fan": "绿色液态胶脂风扇", "recipe.garnished.green_dye_blowing": "绿色批量染色", "create.recipe.garnished.lime_dye_blowing.fan": "黄绿色液态胶脂风扇", "recipe.garnished.lime_dye_blowing": "黄绿色批量染色", "create.recipe.garnished.blue_dye_blowing.fan": "蓝色液态胶脂风扇", "recipe.garnished.blue_dye_blowing": "蓝色批量染色", "create.recipe.garnished.light_blue_dye_blowing.fan": "浅蓝色液态胶脂风扇", "recipe.garnished.light_blue_dye_blowing": "浅蓝色批量染色", "create.recipe.garnished.cyan_dye_blowing.fan": "青色液态胶脂风扇", "recipe.garnished.cyan_dye_blowing": "青色批量染色", "create.recipe.garnished.purple_dye_blowing.fan": "紫色液态胶脂风扇", "recipe.garnished.purple_dye_blowing": "紫色批量染色", "create.recipe.garnished.magenta_dye_blowing.fan": "品红色液态胶脂风扇", "recipe.garnished.magenta_dye_blowing": "品红色批量染色", "create.recipe.garnished.pink_dye_blowing.fan": "粉色液态胶脂风扇", "recipe.garnished.pink_dye_blowing": "粉色批量染色", "create.recipe.garnished.black_dye_blowing.fan": "黑色液态胶脂风扇", "recipe.garnished.black_dye_blowing": "黑色批量染色", "create.recipe.garnished.gray_dye_blowing.fan": "灰色液态胶脂风扇", "recipe.garnished.gray_dye_blowing": "灰色批量染色", "create.recipe.garnished.light_gray_dye_blowing.fan": "浅灰色液态胶脂风扇", "recipe.garnished.light_gray_dye_blowing": "浅灰色批量染色", "create.recipe.garnished.white_dye_blowing.fan": "白色液态胶脂风扇", "recipe.garnished.white_dye_blowing": "白色批量染色", "create.recipe.garnished.brown_dye_blowing.fan": "棕色液态胶脂风扇", "recipe.garnished.brown_dye_blowing": "棕色批量染色", "block.garnished.senile_bone_block": "老骨块", "block.garnished.pansophical_daisy": "全知雏菊", "block.garnished.incandescent_lily": "炽热百合", "block.garnished.sorrowful_lichen": "悲伤地衣", "item.garnished.pansophical_petal": "全知花瓣", "item.garnished.incandescent_petal": "炽热花瓣", "item.garnished.senile_bone": "老骨头", "item.garnished.tusk": "獠牙", "item.garnished.irate_tusk": "愤怒獠牙", "item.garnished.vex_wing": "恼魂翅膀", "item.garnished.meat_scraps": "肉碎片", "item.garnished.dimmed_scale": "暗淡鳞片", "item.garnished.preliminary_nucleus": "初步核心", "item.garnished.omniscient_stew": "全知炖菜", "item.garnished.warped_brew": "扭曲药剂", "item.garnished.iniquitous_brew": "邪恶药剂", "item.garnished.crestfallen_flora": "瓶装凋零植物", "item.garnished.bottled_malodorous_mixture": "瓶装恶臭混合物", "item.garnished.piquant_pretzel": "辛辣椒盐卷饼", "item.garnished.fiendish_spore": "恶魔孢子", "item.garnished.torrid_blend": "炽热混合物", "item.garnished.malodorous_mixture": "恶臭混合物", "block.garnished.aureate_shrub": "金色灌木", "block.garnished.dragon_stone": "§e飞龙石", "block.garnished.dragon_stone_slab": "§e飞龙石台阶", "block.garnished.dragon_stone_stairs": "§e飞龙石楼梯", "block.garnished.dragon_stone_wall": "§e飞龙石墙", "block.garnished.dragon_stone_bricks": "§e切割飞龙石砖", "block.garnished.dragon_stone_brick_slab": "§e切割飞龙石砖台阶", "block.garnished.dragon_stone_brick_stairs": "§e切割飞龙石砖楼梯", "block.garnished.dragon_stone_brick_wall": "§e切割飞龙石砖墙", "block.garnished.small_dragon_stone_bricks": "§e小飞龙石砖", "block.garnished.small_dragon_stone_brick_slab": "§e小飞龙石砖台阶", "block.garnished.small_dragon_stone_brick_stairs": "§e小飞龙石砖楼梯", "block.garnished.small_dragon_stone_brick_wall": "§e小飞龙石砖墙", "block.garnished.polished_dragon_stone": "§e抛光飞龙石", "block.garnished.polished_dragon_stone_slab": "§e抛光飞龙石台阶", "block.garnished.polished_dragon_stone_stairs": "§e抛光飞龙石楼梯", "block.garnished.polished_dragon_stone_wall": "§e抛光飞龙石墙", "block.garnished.cut_dragon_stone": "§e切割飞龙石", "block.garnished.cut_dragon_stone_slab": "§e切割飞龙石台阶", "block.garnished.cut_dragon_stone_stairs": "§e切割飞龙石楼梯", "block.garnished.cut_dragon_stone_wall": "§e切割飞龙石墙", "block.garnished.chiseled_dragon_stone_bricks": "§e雕刻飞龙石砖", "block.garnished.smooth_dragon_stone": "§e光滑飞龙石", "block.garnished.smooth_dragon_stone_stairs": "§e光滑飞龙石楼梯", "block.garnished.smooth_dragon_stone_slab": "§e光滑飞龙石台阶", "block.garnished.smooth_dragon_stone_wall": "§e光滑飞龙石墙", "item.garnished.ender_egg_shell": "龙蛋壳", "item.garnished.frail_ender_egg_shell": "§e脆弱的龙蛋壳", "item.garnished.champion_omelette": "冠军蛋卷", "item.garnished.farseer_brew": "先知药剂", "item.garnished.shelled_dumpling": "带壳饺子", "item.garnished.incomplete_champion_omelette": "未完成的冠军蛋卷", "item.garnished.incomplete_ender_dragon_egg": "未完成的龙蛋", "item.garnished.bok_choy": "小白菜", "item.garnished.bok_choy_seeds": "小白菜叶", "block.garnished.bok_choy_plant": "小白菜", "item.garnished.sweet_tea": "甜茶", "item.garnished.sugar_cube": "方糖", "effect.garnished.truth_seeker": "真理探寻者", "effect.garnished.augmented": "增强状态", "effect.garnished.aversion.description": "类似坚果过敏，处于该效果时吃坚果会导致恶心。", "effect.garnished.spirited_resistance.description": "移除某些负面状态效果。被特定亡灵生物攻击时，类似荆棘效果。", "effect.garnished.cognate.description": "允许受影响者自由直视末影人。", "effect.garnished.flagrant.description": "末影人对受影响者始终保持攻击状态。", "effect.garnished.sugar_high.description": "提供轻微速度提升。若食用煤炭松露，则获得回复效果。", "effect.garnished.sanctity.description": "提供临时护甲点和吸收生命值。", "effect.garnished.thorns.description": "类似荆棘附魔的效果，但作为一种状态效果。", "effect.garnished.mummification.description": "减缓受影响者行动速度。效果结束时对受影响者造成严重伤害。", "effect.garnished.freezing.description": "减缓受影响者，效果类似于处于粉雪中。", "effect.garnished.truth_seeker.description": "使受影响者对箭矢免疫，类似潜影贝。高等级时完全无敌。", "effect.garnished.augmented.description": "减缓受影响者，但提升移动速度。", "jei.garnished.enflamed_mandible.information": "使用毁灭附魔时，烈焰人可掉落。", "jei.garnished.ghast_tendril.information": "使用毁灭附魔时，恶魂可掉落。", "jei.garnished.molten_remnant.information": "使用毁灭附魔时，岩浆怪可掉落。", "jei.garnished.polar_bear_meat.information": "使用毁灭附魔时，北极熊可掉落。", "jei.garnished.polar_bear_hide.information": "使用毁灭附魔时，北极熊可掉落。", "jei.garnished.stray_parchment.information": "使用毁灭附魔时，流浪者可掉落。", "jei.garnished.tenebrous_meat.information": "使用毁灭附魔时，守卫者可掉落，也可在古代之城战利品箱中找到。", "jei.garnished.tusk.information": "使用毁灭附魔时，疣猪兽可掉落，使用回收附魔时猪也有极小概率掉落。", "jei.garnished.irate_tusk.information": "使用毁灭附魔时，疣猪兽有极小概率掉落。", "jei.garnished.ender_scale.information": "使用毁灭附魔时，末影人、末影螨和末影龙有不同几率掉落。", "jei.garnished.endermite_heart.information": "使用毁灭附魔时，末影螨可掉落。", "jei.garnished.vex_wing.information": "使用毁灭附魔时，恶魂使可掉落；使用回收附魔时，游荡者有极小概率掉落。", "jei.garnished.ravager_meat.information": "使用毁灭附魔时，劫掠兽可掉落。", "block.garnished.lime_mastic_block": "青柠树脂块", "block.garnished.light_blue_mastic_block": "淡蓝树脂块", "block.garnished.cyan_mastic_block": "青色树脂块", "block.garnished.magenta_mastic_block": "洋红树脂块", "block.garnished.pink_mastic_block": "粉红树脂块", "block.garnished.black_mastic_block": "黑色树脂块", "block.garnished.gray_mastic_block": "灰色树脂块", "block.garnished.light_gray_mastic_block": "浅灰树脂块", "block.garnished.white_mastic_block": "白色树脂块", "block.garnished.brown_mastic_block": "棕色树脂块", "block.garnished.zultanite": "祖尔坦石", "block.garnished.red_zultanite": "红色祖尔坦石", "block.garnished.orange_zultanite": "橙色祖尔坦石", "block.garnished.yellow_zultanite": "黄色祖尔坦石", "block.garnished.green_zultanite": "绿色祖尔坦石", "block.garnished.lime_zultanite": "青柠祖尔坦石", "block.garnished.blue_zultanite": "蓝色祖尔坦石", "block.garnished.light_blue_zultanite": "淡蓝祖尔坦石", "block.garnished.cyan_zultanite": "青色祖尔坦石", "block.garnished.purple_zultanite": "紫色祖尔坦石", "block.garnished.magenta_zultanite": "洋红祖尔坦石", "block.garnished.pink_zultanite": "粉红祖尔坦石", "block.garnished.black_zultanite": "黑色祖尔坦石", "block.garnished.gray_zultanite": "灰色祖尔坦石", "block.garnished.light_gray_zultanite": "浅灰祖尔坦石", "block.garnished.white_zultanite": "白色祖尔坦石", "block.garnished.brown_zultanite": "棕色祖尔坦石", "block.garnished.cut_zultanite": "切割祖尔坦石", "block.garnished.cut_red_zultanite": "切割红色祖尔坦石", "block.garnished.cut_orange_zultanite": "切割橙色祖尔坦石", "block.garnished.cut_yellow_zultanite": "切割黄色祖尔坦石", "block.garnished.cut_green_zultanite": "切割绿色祖尔坦石", "block.garnished.cut_lime_zultanite": "切割青柠祖尔坦石", "block.garnished.cut_blue_zultanite": "切割蓝色祖尔坦石", "block.garnished.cut_light_blue_zultanite": "切割淡蓝祖尔坦石", "block.garnished.cut_cyan_zultanite": "切割青色祖尔坦石", "block.garnished.cut_purple_zultanite": "切割紫色祖尔坦石", "block.garnished.cut_magenta_zultanite": "切割洋红祖尔坦石", "block.garnished.cut_pink_zultanite": "切割粉红祖尔坦石", "block.garnished.cut_black_zultanite": "切割黑色祖尔坦石", "block.garnished.cut_gray_zultanite": "切割灰色祖尔坦石", "block.garnished.cut_light_gray_zultanite": "切割浅灰祖尔坦石", "block.garnished.cut_white_zultanite": "切割白色祖尔坦石", "block.garnished.cut_brown_zultanite": "切割棕色祖尔坦石", "block.garnished.cut_brown_zultanite_slab": "切割棕色祖尔坦石台阶", "block.garnished.zultanite_bricks": "祖尔坦石砖", "block.garnished.red_zultanite_bricks": "红色祖尔坦石砖", "block.garnished.orange_zultanite_bricks": "橙色祖尔坦石砖", "block.garnished.yellow_zultanite_bricks": "黄色祖尔坦石砖", "block.garnished.green_zultanite_bricks": "绿色祖尔坦石砖", "block.garnished.lime_zultanite_bricks": "青柠祖尔坦石砖", "block.garnished.blue_zultanite_bricks": "蓝色祖尔坦石砖", "block.garnished.light_blue_zultanite_bricks": "淡蓝祖尔坦石砖", "block.garnished.cyan_zultanite_bricks": "青色祖尔坦石砖", "block.garnished.purple_zultanite_bricks": "紫色祖尔坦石砖", "block.garnished.magenta_zultanite_bricks": "洋红祖尔坦石砖", "block.garnished.pink_zultanite_bricks": "粉红祖尔坦石砖", "block.garnished.black_zultanite_bricks": "黑色祖尔坦石砖", "block.garnished.gray_zultanite_bricks": "灰色祖尔坦石砖", "block.garnished.light_gray_zultanite_bricks": "浅灰祖尔坦石砖", "block.garnished.white_zultanite_bricks": "白色祖尔坦石砖", "block.garnished.brown_zultanite_bricks": "棕色祖尔坦石砖", "block.garnished.small_zultanite_bricks": "小块祖尔坦石砖", "block.garnished.small_red_zultanite_bricks": "小块红色祖尔坦石砖", "block.garnished.small_orange_zultanite_bricks": "小块橙色祖尔坦石砖", "block.garnished.small_yellow_zultanite_bricks": "小块黄色祖尔坦石砖", "block.garnished.small_green_zultanite_bricks": "小块绿色祖尔坦石砖", "block.garnished.small_lime_zultanite_bricks": "小块青柠祖尔坦石砖", "block.garnished.small_blue_zultanite_bricks": "小块蓝色祖尔坦石砖", "block.garnished.small_light_blue_zultanite_bricks": "小块淡蓝祖尔坦石砖", "block.garnished.small_cyan_zultanite_bricks": "小块青色祖尔坦石砖", "block.garnished.small_purple_zultanite_bricks": "小块紫色祖尔坦石砖", "block.garnished.small_magenta_zultanite_bricks": "小块洋红祖尔坦石砖", "block.garnished.small_pink_zultanite_bricks": "小块粉红祖尔坦石砖", "block.garnished.small_gray_zultanite_bricks": "小块灰色祖尔坦石砖", "block.garnished.small_light_gray_zultanite_bricks": "小块浅灰祖尔坦石砖", "block.garnished.small_white_zultanite_bricks": "小块白色祖尔坦石砖", "block.garnished.small_brown_zultanite_bricks": "小块棕色祖尔坦石砖", "block.garnished.small_black_zultanite_bricks": "小块黑色祖尔坦石砖", "block.garnished.polished_zultanite": "抛光祖尔坦石", "block.garnished.polished_red_zultanite": "抛光红色祖尔坦石", "block.garnished.polished_orange_zultanite": "抛光橙色祖尔坦石", "block.garnished.polished_yellow_zultanite": "抛光黄色祖尔坦石", "block.garnished.polished_green_zultanite": "抛光绿色祖尔坦石", "block.garnished.polished_lime_zultanite": "抛光青柠祖尔坦石", "block.garnished.polished_blue_zultanite": "抛光蓝色祖尔坦石", "block.garnished.polished_light_blue_zultanite": "抛光淡蓝祖尔坦石", "block.garnished.polished_cyan_zultanite": "抛光青色祖尔坦石", "block.garnished.polished_purple_zultanite": "抛光紫色祖尔坦石", "block.garnished.polished_magenta_zultanite": "抛光洋红祖尔坦石", "block.garnished.polished_pink_zultanite": "抛光粉红祖尔坦石", "block.garnished.polished_black_zultanite": "抛光黑色祖尔坦石", "block.garnished.polished_gray_zultanite": "抛光灰色祖尔坦石", "block.garnished.polished_light_gray_zultanite": "抛光浅灰祖尔坦石", "block.garnished.polished_white_zultanite": "抛光白色祖尔坦石", "block.garnished.polished_brown_zultanite": "抛光棕色祖尔坦石", "block.garnished.smooth_zultanite": "光滑祖尔坦石", "block.garnished.smooth_red_zultanite": "光滑红色祖尔坦石", "block.garnished.smooth_orange_zultanite": "光滑橙色祖尔坦石", "block.garnished.smooth_yellow_zultanite": "光滑黄色祖尔坦石", "block.garnished.smooth_green_zultanite": "光滑绿色祖尔坦石", "block.garnished.smooth_lime_zultanite": "光滑青柠祖尔坦石", "block.garnished.smooth_blue_zultanite": "光滑蓝色祖尔坦石", "block.garnished.smooth_light_blue_zultanite": "光滑淡蓝祖尔坦石", "block.garnished.smooth_cyan_zultanite": "光滑青色祖尔坦石", "block.garnished.smooth_purple_zultanite": "光滑紫色祖尔坦石", "block.garnished.smooth_magenta_zultanite": "光滑洋红祖尔坦石", "block.garnished.smooth_pink_zultanite": "光滑粉红祖尔坦石", "block.garnished.smooth_black_zultanite": "光滑黑色祖尔坦石", "block.garnished.smooth_gray_zultanite": "光滑灰色祖尔坦石", "block.garnished.smooth_light_gray_zultanite": "光滑浅灰祖尔坦石", "block.garnished.smooth_white_zultanite": "光滑白色祖尔坦石", "block.garnished.smooth_brown_zultanite": "光滑棕色祖尔坦石", "block.garnished.chiseled_zultanite_bricks": "雕刻祖尔坦石砖", "block.garnished.chiseled_red_zultanite_bricks": "雕刻红色祖尔坦石砖", "block.garnished.chiseled_orange_zultanite_bricks": "雕刻橙色祖尔坦石砖", "block.garnished.chiseled_yellow_zultanite_bricks": "雕刻黄色祖尔坦石砖", "block.garnished.chiseled_green_zultanite_bricks": "雕刻绿色祖尔坦石砖", "block.garnished.chiseled_lime_zultanite_bricks": "雕刻青柠祖尔坦石砖", "block.garnished.chiseled_blue_zultanite_bricks": "雕刻蓝色祖尔坦石砖", "block.garnished.chiseled_light_blue_zultanite_bricks": "雕刻淡蓝祖尔坦石砖", "block.garnished.chiseled_cyan_zultanite_bricks": "雕刻青色祖尔坦石砖", "block.garnished.chiseled_purple_zultanite_bricks": "雕刻紫色祖尔坦石砖", "block.garnished.chiseled_magenta_zultanite_bricks": "雕刻洋红祖尔坦石砖", "block.garnished.chiseled_pink_zultanite_bricks": "雕刻粉红祖尔坦石砖", "block.garnished.chiseled_black_zultanite_bricks": "雕刻黑色祖尔坦石砖", "block.garnished.chiseled_gray_zultanite_bricks": "雕刻灰色祖尔坦石砖", "block.garnished.chiseled_light_gray_zultanite_bricks": "雕刻浅灰祖尔坦石砖", "block.garnished.chiseled_white_zultanite_bricks": "雕刻白色祖尔坦石砖", "block.garnished.chiseled_brown_zultanite_bricks": "雕刻棕色祖尔坦石砖", "block.garnished.zultanite_stairs": "祖尔坦石楼梯", "block.garnished.red_zultanite_stairs": "红色祖尔坦石楼梯", "block.garnished.orange_zultanite_stairs": "橙色祖尔坦石楼梯", "block.garnished.yellow_zultanite_stairs": "黄色祖尔坦石楼梯", "block.garnished.green_zultanite_stairs": "绿色祖尔坦石楼梯", "block.garnished.lime_zultanite_stairs": "青柠祖尔坦石楼梯", "block.garnished.blue_zultanite_stairs": "蓝色祖尔坦石楼梯", "block.garnished.light_blue_zultanite_stairs": "淡蓝祖尔坦石楼梯", "block.garnished.cyan_zultanite_stairs": "青色祖尔坦石楼梯", "block.garnished.purple_zultanite_stairs": "紫色祖尔坦石楼梯", "block.garnished.magenta_zultanite_stairs": "洋红祖尔坦石楼梯", "block.garnished.pink_zultanite_stairs": "粉红祖尔坦石楼梯", "block.garnished.black_zultanite_stairs": "黑色祖尔坦石楼梯", "block.garnished.gray_zultanite_stairs": "灰色祖尔坦石楼梯", "block.garnished.light_gray_zultanite_stairs": "浅灰祖尔坦石楼梯", "block.garnished.white_zultanite_stairs": "白色祖尔坦石楼梯", "block.garnished.brown_zultanite_stairs": "棕色祖尔坦石楼梯", "block.garnished.cut_zultanite_stairs": "切割祖尔坦石楼梯", "block.garnished.cut_red_zultanite_stairs": "切割红色祖尔坦石楼梯", "block.garnished.cut_orange_zultanite_stairs": "切割橙色祖尔坦石楼梯", "block.garnished.cut_yellow_zultanite_stairs": "切割黄色祖尔坦石楼梯", "block.garnished.cut_green_zultanite_stairs": "切割绿色祖尔坦石楼梯", "block.garnished.cut_lime_zultanite_stairs": "切割青柠祖尔坦石楼梯", "block.garnished.cut_blue_zultanite_stairs": "切割蓝色祖尔坦石楼梯", "block.garnished.cut_light_blue_zultanite_stairs": "切割淡蓝祖尔坦石楼梯", "block.garnished.cut_cyan_zultanite_stairs": "切割青色祖尔坦石楼梯", "block.garnished.cut_purple_zultanite_stairs": "切割紫色祖尔坦石楼梯", "block.garnished.cut_magenta_zultanite_stairs": "切割洋红祖尔坦石楼梯", "block.garnished.cut_pink_zultanite_stairs": "切割粉红祖尔坦石楼梯", "block.garnished.cut_black_zultanite_stairs": "切割黑色祖尔坦石楼梯", "block.garnished.cut_gray_zultanite_stairs": "切割灰色祖尔坦石楼梯", "block.garnished.cut_light_gray_zultanite_stairs": "切割浅灰祖尔坦石楼梯", "block.garnished.cut_white_zultanite_stairs": "切割白色祖尔坦石楼梯", "block.garnished.cut_brown_zultanite_stairs": "切割棕色祖尔坦石楼梯", "block.garnished.zultanite_brick_stairs": "祖尔坦石砖楼梯", "block.garnished.red_zultanite_brick_stairs": "红色祖尔坦石砖楼梯", "block.garnished.orange_zultanite_brick_stairs": "橙色祖尔坦石砖楼梯", "block.garnished.yellow_zultanite_brick_stairs": "黄色祖尔坦石砖楼梯", "block.garnished.green_zultanite_brick_stairs": "绿色祖尔坦石砖楼梯", "block.garnished.lime_zultanite_brick_stairs": "青柠祖尔坦石砖楼梯", "block.garnished.light_blue_zultanite_brick_stairs": "切割淡蓝祖尔坦石砖楼梯", "block.garnished.cyan_zultanite_brick_stairs": "切割青色祖尔坦石砖楼梯", "block.garnished.purple_zultanite_brick_stairs": "切割紫色祖尔坦石砖楼梯", "block.garnished.magenta_zultanite_brick_stairs": "切割洋红祖尔坦石砖楼梯", "block.garnished.pink_zultanite_brick_stairs": "切割粉红祖尔坦石砖楼梯", "block.garnished.black_zultanite_brick_stairs": "切割黑色祖尔坦石砖楼梯", "block.garnished.gray_zultanite_brick_stairs": "切割灰色祖尔坦石砖楼梯", "block.garnished.light_gray_zultanite_brick_stairs": "切割浅灰祖尔坦石砖楼梯", "block.garnished.white_zultanite_brick_stairs": "切割白色祖尔坦石砖楼梯", "block.garnished.brown_zultanite_brick_stairs": "切割棕色祖尔坦石砖楼梯", "block.garnished.small_zultanite_brick_stairs": "小块祖尔坦石砖楼梯", "block.garnished.small_red_zultanite_brick_stairs": "小块红色祖尔坦石砖楼梯", "block.garnished.small_orange_zultanite_brick_stairs": "小块橙色祖尔坦石砖楼梯", "block.garnished.small_yellow_zultanite_brick_stairs": "小块黄色祖尔坦石砖楼梯", "block.garnished.small_green_zultanite_brick_stairs": "小块绿色祖尔坦石砖楼梯", "block.garnished.small_lime_zultanite_brick_stairs": "小块青柠祖尔坦石砖楼梯", "block.garnished.small_blue_zultanite_brick_stairs": "小块蓝色祖尔坦石砖楼梯", "block.garnished.small_light_blue_zultanite_brick_stairs": "小块淡蓝祖尔坦石砖楼梯", "block.garnished.small_cyan_zultanite_brick_stairs": "小块青色祖尔坦石砖楼梯", "block.garnished.small_purple_zultanite_brick_stairs": "小块紫色祖尔坦石砖楼梯", "block.garnished.small_magenta_zultanite_brick_stairs": "小块洋红祖尔坦石砖楼梯", "block.garnished.small_pink_zultanite_brick_stairs": "小块粉红祖尔坦石砖楼梯", "block.garnished.small_black_zultanite_brick_stairs": "小块黑色祖尔坦石砖楼梯", "block.garnished.small_gray_zultanite_brick_stairs": "小块灰色祖尔坦石砖楼梯", "block.garnished.small_light_gray_zultanite_brick_stairs": "小块浅灰祖尔坦石砖楼梯", "block.garnished.small_white_zultanite_brick_stairs": "小块白色祖尔坦石砖楼梯", "block.garnished.small_brown_zultanite_brick_stairs": "小块棕色祖尔坦石砖楼梯", "block.garnished.polished_zultanite_stairs": "抛光祖尔坦石楼梯", "block.garnished.polished_red_zultanite_stairs": "抛光红色祖尔坦石楼梯", "block.garnished.polished_orange_zultanite_stairs": "抛光橙色祖尔坦石楼梯", "block.garnished.polished_yellow_zultanite_stairs": "抛光黄色祖尔坦石楼梯", "block.garnished.polished_green_zultanite_stairs": "抛光绿色祖尔坦石楼梯", "block.garnished.polished_lime_zultanite_stairs": "抛光青柠祖尔坦石楼梯", "block.garnished.polished_blue_zultanite_stairs": "抛光蓝色祖尔坦石楼梯", "block.garnished.polished_light_blue_zultanite_stairs": "抛光淡蓝祖尔坦石楼梯", "block.garnished.polished_cyan_zultanite_stairs": "抛光青色祖尔坦石楼梯", "block.garnished.polished_purple_zultanite_stairs": "抛光紫色祖尔坦石楼梯", "block.garnished.polished_magenta_zultanite_stairs": "抛光洋红祖尔坦石楼梯", "block.garnished.polished_pink_zultanite_stairs": "抛光粉红祖尔坦石楼梯", "block.garnished.polished_black_zultanite_stairs": "抛光黑色祖尔坦石楼梯", "block.garnished.polished_gray_zultanite_stairs": "抛光灰色祖尔坦石楼梯", "block.garnished.polished_light_gray_zultanite_stairs": "抛光浅灰祖尔坦石楼梯", "block.garnished.polished_white_zultanite_stairs": "抛光白色祖尔坦石楼梯", "block.garnished.polished_brown_zultanite_stairs": "抛光棕色祖尔坦石楼梯", "block.garnished.smooth_zultanite_stairs": "光滑祖尔坦石楼梯", "block.garnished.smooth_red_zultanite_stairs": "光滑红色祖尔坦石楼梯", "block.garnished.smooth_orange_zultanite_stairs": "光滑橙色祖尔坦石楼梯", "block.garnished.smooth_yellow_zultanite_stairs": "光滑黄色祖尔坦石楼梯", "block.garnished.smooth_green_zultanite_stairs": "光滑绿色祖尔坦石楼梯", "block.garnished.smooth_lime_zultanite_stairs": "光滑青柠祖尔坦石楼梯", "block.garnished.smooth_blue_zultanite_stairs": "光滑蓝色祖尔坦石楼梯", "block.garnished.smooth_light_blue_zultanite_stairs": "光滑淡蓝祖尔坦石楼梯", "block.garnished.smooth_cyan_zultanite_stairs": "光滑青色祖尔坦石楼梯", "block.garnished.smooth_purple_zultanite_stairs": "光滑紫色祖尔坦石楼梯", "block.garnished.smooth_magenta_zultanite_stairs": "光滑洋红祖尔坦石楼梯", "block.garnished.smooth_pink_zultanite_stairs": "光滑粉红祖尔坦石楼梯", "block.garnished.smooth_black_zultanite_stairs": "光滑黑色祖尔坦石楼梯", "block.garnished.smooth_gray_zultanite_stairs": "光滑灰色祖尔坦石楼梯", "block.garnished.smooth_light_gray_zultanite_stairs": "光滑浅灰祖尔坦石楼梯", "block.garnished.smooth_white_zultanite_stairs": "光滑白色祖尔坦石楼梯", "block.garnished.smooth_brown_zultanite_stairs": "光滑棕色祖尔坦石楼梯", "block.garnished.zultanite_slab": "祖尔坦石台阶", "block.garnished.red_zultanite_slab": "红色祖尔坦石台阶", "block.garnished.orange_zultanite_slab": "橙色祖尔坦石台阶", "block.garnished.yellow_zultanite_slab": "黄色祖尔坦石台阶", "block.garnished.green_zultanite_slab": "绿色祖尔坦石台阶", "block.garnished.lime_zultanite_slab": "青柠祖尔坦石台阶", "block.garnished.blue_zultanite_slab": "蓝色祖尔坦石台阶", "block.garnished.light_blue_zultanite_slab": "淡蓝祖尔坦石台阶", "block.garnished.cyan_zultanite_slab": "青色祖尔坦石台阶", "block.garnished.purple_zultanite_slab": "紫色祖尔坦石台阶", "block.garnished.magenta_zultanite_slab": "洋红祖尔坦石台阶", "block.garnished.pink_zultanite_slab": "粉红祖尔坦石台阶", "block.garnished.black_zultanite_slab": "黑色祖尔坦石台阶", "block.garnished.gray_zultanite_slab": "灰色祖尔坦石台阶", "block.garnished.light_gray_zultanite_slab": "浅灰祖尔坦石台阶", "block.garnished.white_zultanite_slab": "白色祖尔坦石台阶", "block.garnished.brown_zultanite_slab": "棕色祖尔坦石台阶", "block.garnished.cut_zultanite_slab": "切割祖尔坦石台阶", "block.garnished.cut_red_zultanite_slab": "切割红色祖尔坦石台阶", "block.garnished.cut_orange_zultanite_slab": "切割橙色祖尔坦石台阶", "block.garnished.cut_yellow_zultanite_slab": "切割黄色祖尔坦石台阶", "block.garnished.cut_green_zultanite_slab": "切割绿色祖尔坦石台阶", "block.garnished.cut_lime_zultanite_slab": "切割青柠祖尔坦石台阶", "block.garnished.cut_blue_zultanite_slab": "切割蓝色祖尔坦石台阶", "block.garnished.cut_light_blue_zultanite_slab": "切割淡蓝祖尔坦石台阶", "block.garnished.cut_cyan_zultanite_slab": "切割青色祖尔坦石台阶", "block.garnished.cut_purple_zultanite_slab": "切割紫色祖尔坦石台阶", "block.garnished.cut_magenta_zultanite_slab": "切割洋红祖尔坦石台阶", "block.garnished.cut_pink_zultanite_slab": "切割粉红祖尔坦石台阶", "block.garnished.cut_black_zultanite_slab": "切割黑色祖尔坦石台阶", "block.garnished.cut_gray_zultanite_slab": "切割灰色祖尔坦石台阶", "block.garnished.cut_light_gray_zultanite_slab": "切割浅灰祖尔坦石台阶", "block.garnished.cut_white_zultanite_slab": "切割白色祖尔坦石台阶", "block.garnished.zultanite_brick_slab": "切割祖尔坦石砖台阶", "block.garnished.red_zultanite_brick_slab": "切割红色祖尔坦石砖台阶", "block.garnished.orange_zultanite_brick_slab": "切割橙色祖尔坦石砖台阶", "block.garnished.yellow_zultanite_brick_slab": "切割黄色祖尔坦石砖台阶", "block.garnished.green_zultanite_brick_slab": "切割绿色祖尔坦石砖台阶", "block.garnished.lime_zultanite_brick_slab": "切割青柠祖尔坦石砖台阶", "block.garnished.blue_zultanite_brick_slab": "切割蓝色祖尔坦石砖台阶", "block.garnished.light_blue_zultanite_brick_slab": "切割淡蓝祖尔坦石砖台阶", "block.garnished.cyan_zultanite_brick_slab": "切割青色祖尔坦石砖台阶", "block.garnished.purple_zultanite_brick_slab": "切割紫色祖尔坦石砖台阶", "block.garnished.magenta_zultanite_brick_slab": "切割洋红祖尔坦石砖台阶", "block.garnished.pink_zultanite_brick_slab": "切割粉红祖尔坦石砖台阶", "block.garnished.black_zultanite_brick_slab": "切割黑色祖尔坦石砖台阶", "block.garnished.gray_zultanite_brick_slab": "切割灰色祖尔坦石砖台阶", "block.garnished.light_gray_zultanite_brick_slab": "切割浅灰祖尔坦石砖台阶", "block.garnished.white_zultanite_brick_slab": "切割白色祖尔坦石砖台阶", "block.garnished.brown_zultanite_brick_slab": "切割棕色祖尔坦石砖台阶", "block.garnished.small_zultanite_brick_slab": "小块祖尔坦石砖台阶", "block.garnished.small_red_zultanite_brick_slab": "小块红色祖尔坦石砖台阶", "block.garnished.small_orange_zultanite_brick_slab": "小块橙色祖尔坦石砖台阶", "block.garnished.small_yellow_zultanite_brick_slab": "小块黄色祖尔坦石砖台阶", "block.garnished.small_green_zultanite_brick_slab": "小块绿色祖尔坦石砖台阶", "block.garnished.small_lime_zultanite_brick_slab": "小块青柠祖尔坦石砖台阶", "block.garnished.small_blue_zultanite_brick_slab": "小块蓝色祖尔坦石砖台阶", "block.garnished.small_light_blue_zultanite_brick_slab": "小块淡蓝祖尔坦石砖台阶", "block.garnished.small_cyan_zultanite_brick_slab": "小块青色祖尔坦石砖台阶", "block.garnished.small_purple_zultanite_brick_slab": "小块紫色祖尔坦石砖台阶", "block.garnished.small_magenta_zultanite_brick_slab": "小块洋红祖尔坦石砖台阶", "block.garnished.small_pink_zultanite_brick_slab": "小块粉红祖尔坦石砖台阶", "block.garnished.small_black_zultanite_brick_slab": "小块黑色祖尔坦石砖台阶", "block.garnished.small_gray_zultanite_brick_slab": "小块灰色祖尔坦石砖台阶", "block.garnished.small_light_gray_zultanite_brick_slab": "小块浅灰祖尔坦石砖台阶", "block.garnished.small_white_zultanite_brick_slab": "小块白色祖尔坦石砖台阶", "block.garnished.small_brown_zultanite_brick_slab": "小块棕色祖尔坦石砖台阶", "block.garnished.polished_zultanite_slab": "抛光祖尔坦石台阶", "block.garnished.polished_red_zultanite_slab": "抛光红色祖尔坦石台阶", "block.garnished.polished_orange_zultanite_slab": "抛光橙色祖尔坦石台阶", "block.garnished.polished_yellow_zultanite_slab": "抛光黄色祖尔坦石台阶", "block.garnished.polished_green_zultanite_slab": "抛光绿色祖尔坦石台阶", "block.garnished.polished_lime_zultanite_slab": "抛光青柠祖尔坦石台阶", "block.garnished.polished_blue_zultanite_slab": "抛光蓝色祖尔坦石台阶", "block.garnished.polished_light_blue_zultanite_slab": "抛光淡蓝祖尔坦石台阶", "block.garnished.polished_cyan_zultanite_slab": "抛光青色祖尔坦石台阶", "block.garnished.polished_purple_zultanite_slab": "抛光紫色祖尔坦石台阶", "block.garnished.polished_magenta_zultanite_slab": "抛光洋红祖尔坦石台阶", "block.garnished.polished_pink_zultanite_slab": "抛光粉红祖尔坦石台阶", "block.garnished.polished_black_zultanite_slab": "抛光黑色祖尔坦石台阶", "block.garnished.polished_gray_zultanite_slab": "抛光灰色祖尔坦石台阶", "block.garnished.polished_light_gray_zultanite_slab": "抛光浅灰祖尔坦石台阶", "block.garnished.polished_white_zultanite_slab": "抛光白色祖尔坦石台阶", "block.garnished.polished_brown_zultanite_slab": "抛光棕色祖尔坦石台阶", "block.garnished.smooth_zultanite_slab": "光滑祖尔坦石台阶", "block.garnished.smooth_red_zultanite_slab": "光滑红色祖尔坦石台阶", "block.garnished.smooth_orange_zultanite_slab": "光滑橙色祖尔坦石台阶", "block.garnished.smooth_yellow_zultanite_slab": "光滑黄色祖尔坦石台阶", "block.garnished.smooth_green_zultanite_slab": "光滑绿色祖尔坦石台阶", "block.garnished.smooth_lime_zultanite_slab": "光滑青柠祖尔坦石台阶", "block.garnished.smooth_blue_zultanite_slab": "光滑蓝色祖尔坦石台阶", "block.garnished.smooth_light_blue_zultanite_slab": "光滑淡蓝祖尔坦石台阶", "block.garnished.smooth_cyan_zultanite_slab": "光滑青色祖尔坦石台阶", "block.garnished.smooth_purple_zultanite_slab": "光滑紫色祖尔坦石台阶", "block.garnished.smooth_magenta_zultanite_slab": "光滑洋红祖尔坦石台阶", "block.garnished.smooth_pink_zultanite_slab": "光滑粉红祖尔坦石台阶", "block.garnished.smooth_black_zultanite_slab": "光滑黑色祖尔坦石台阶", "block.garnished.smooth_gray_zultanite_slab": "光滑灰色祖尔坦石台阶", "block.garnished.smooth_light_gray_zultanite_slab": "光滑浅灰祖尔坦石台阶", "block.garnished.smooth_white_zultanite_slab": "光滑白色祖尔坦石台阶", "block.garnished.smooth_brown_zultanite_slab": "光滑棕色祖尔坦石台阶", "block.garnished.zultanite_wall": "祖尔坦石墙", "block.garnished.red_zultanite_wall": "红色祖尔坦石墙", "block.garnished.orange_zultanite_wall": "橙色祖尔坦石墙", "block.garnished.yellow_zultanite_wall": "黄色祖尔坦石墙", "block.garnished.green_zultanite_wall": "绿色祖尔坦石墙", "block.garnished.lime_zultanite_wall": "青柠祖尔坦石墙", "block.garnished.blue_zultanite_wall": "蓝色祖尔坦石墙", "block.garnished.light_blue_zultanite_wall": "淡蓝祖尔坦石墙", "block.garnished.cyan_zultanite_wall": "青色祖尔坦石墙", "block.garnished.purple_zultanite_wall": "紫色祖尔坦石墙", "block.garnished.magenta_zultanite_wall": "洋红祖尔坦石墙", "block.garnished.pink_zultanite_wall": "粉红祖尔坦石墙", "block.garnished.black_zultanite_wall": "黑色祖尔坦石墙", "block.garnished.gray_zultanite_wall": "灰色祖尔坦石墙", "block.garnished.light_gray_zultanite_wall": "浅灰祖尔坦石墙", "block.garnished.white_zultanite_wall": "白色祖尔坦石墙", "block.garnished.brown_zultanite_wall": "棕色祖尔坦石墙", "block.garnished.cut_zultanite_wall": "切割祖尔坦石墙", "block.garnished.cut_red_zultanite_wall": "切割红色祖尔坦石墙", "block.garnished.cut_orange_zultanite_wall": "切割橙色祖尔坦石墙", "block.garnished.cut_yellow_zultanite_wall": "切割黄色祖尔坦石墙", "block.garnished.cut_green_zultanite_wall": "切割绿色祖尔坦石墙", "block.garnished.cut_lime_zultanite_wall": "切割青柠祖尔坦石墙", "block.garnished.cut_blue_zultanite_wall": "切割蓝色祖尔坦石墙", "block.garnished.cut_light_blue_zultanite_wall": "切割淡蓝祖尔坦石墙", "block.garnished.cut_cyan_zultanite_wall": "切割青色祖尔坦石墙", "block.garnished.cut_purple_zultanite_wall": "切割紫色祖尔坦石墙", "block.garnished.cut_magenta_zultanite_wall": "切割洋红祖尔坦石墙", "block.garnished.cut_black_zultanite_wall": "切割黑色祖尔坦石墙", "block.garnished.cut_gray_zultanite_wall": "切割灰色祖尔坦石墙", "block.garnished.cut_light_gray_zultanite_wall": "切割浅灰祖尔坦石墙", "block.garnished.cut_white_zultanite_wall": "切割白色祖尔坦石墙", "block.garnished.cut_brown_zultanite_wall": "切割棕色祖尔坦石墙", "block.garnished.zultanite_brick_wall": "切割祖尔坦石砖墙", "block.garnished.red_zultanite_brick_wall": "切割红色祖尔坦石砖墙", "block.garnished.orange_zultanite_brick_wall": "切割橙色祖尔坦石砖墙", "block.garnished.yellow_zultanite_brick_wall": "切割黄色祖尔坦石砖墙", "block.garnished.green_zultanite_brick_wall": "切割绿色祖尔坦石砖墙", "block.garnished.lime_zultanite_brick_wall": "切割青柠祖尔坦石砖墙", "block.garnished.blue_zultanite_brick_wall": "切割蓝色祖尔坦石砖墙", "block.garnished.light_blue_zultanite_brick_wall": "切割淡蓝祖尔坦石砖墙", "block.garnished.cyan_zultanite_brick_wall": "切割青色祖尔坦石砖墙", "block.garnished.purple_zultanite_brick_wall": "切割紫色祖尔坦石砖墙", "block.garnished.magenta_zultanite_brick_wall": "切割洋红祖尔坦石砖墙", "block.garnished.pink_zultanite_brick_wall": "切割粉红祖尔坦石砖墙", "block.garnished.black_zultanite_brick_wall": "切割黑色祖尔坦石砖墙", "block.garnished.gray_zultanite_brick_wall": "切割灰色祖尔坦石砖墙", "block.garnished.light_gray_zultanite_brick_wall": "切割浅灰祖尔坦石砖墙", "block.garnished.white_zultanite_brick_wall": "切割白色祖尔坦石砖墙", "block.garnished.brown_zultanite_brick_wall": "切割棕色祖尔坦石砖墙", "block.garnished.small_zultanite_brick_wall": "小块祖尔坦石砖墙", "block.garnished.small_red_zultanite_brick_wall": "小块红色祖尔坦石砖墙", "block.garnished.small_orange_zultanite_brick_wall": "小块橙色祖尔坦石砖墙", "block.garnished.small_yellow_zultanite_brick_wall": "小块黄色祖尔坦石砖墙", "block.garnished.small_green_zultanite_brick_wall": "小块绿色祖尔坦石砖墙", "block.garnished.small_lime_zultanite_brick_wall": "小块青柠祖尔坦石砖墙", "block.garnished.small_blue_zultanite_brick_wall": "小块蓝色祖尔坦石砖墙", "block.garnished.small_light_blue_zultanite_brick_wall": "小块淡蓝祖尔坦石砖墙", "block.garnished.small_cyan_zultanite_brick_wall": "小块青色祖尔坦石砖墙", "block.garnished.small_purple_zultanite_brick_wall": "小块紫色祖尔坦石砖墙", "block.garnished.small_magenta_zultanite_brick_wall": "小块洋红祖尔坦石砖墙", "block.garnished.small_pink_zultanite_brick_wall": "小块粉红祖尔坦石砖墙", "block.garnished.small_black_zultanite_brick_wall": "小块黑色祖尔坦石砖墙", "block.garnished.small_gray_zultanite_brick_wall": "小块灰色祖尔坦石砖墙", "block.garnished.small_light_gray_zultanite_brick_wall": "小块浅灰祖尔坦石砖墙", "block.garnished.small_white_zultanite_brick_wall": "小块白色祖尔坦石砖墙", "block.garnished.small_brown_zultanite_brick_wall": "小块棕色祖尔坦石砖墙", "block.garnished.polished_zultanite_wall": "抛光祖尔坦石墙", "block.garnished.polished_red_zultanite_wall": "抛光红色祖尔坦石墙", "block.garnished.polished_orange_zultanite_wall": "抛光橙色祖尔坦石墙", "block.garnished.polished_yellow_zultanite_wall": "抛光黄色祖尔坦石墙", "block.garnished.polished_green_zultanite_wall": "抛光绿色祖尔坦石墙", "block.garnished.polished_lime_zultanite_wall": "抛光青柠祖尔坦石墙", "block.garnished.polished_blue_zultanite_wall": "抛光蓝色祖尔坦石墙", "block.garnished.polished_light_blue_zultanite_wall": "抛光淡蓝祖尔坦石墙", "block.garnished.polished_cyan_zultanite_wall": "抛光青色祖尔坦石墙", "block.garnished.polished_purple_zultanite_wall": "抛光紫色祖尔坦石墙", "block.garnished.polished_magenta_zultanite_wall": "抛光洋红祖尔坦石墙", "block.garnished.polished_pink_zultanite_wall": "抛光粉红祖尔坦石墙", "block.garnished.polished_black_zultanite_wall": "抛光黑色祖尔坦石墙", "block.garnished.polished_gray_zultanite_wall": "抛光灰色祖尔坦石墙", "block.garnished.polished_light_gray_zultanite_wall": "抛光浅灰祖尔坦石墙", "block.garnished.polished_white_zultanite_wall": "抛光白色祖尔坦石墙", "block.garnished.polished_brown_zultanite_wall": "抛光棕色祖尔坦石墙", "block.garnished.smooth_zultanite_wall": "光滑祖尔坦石墙", "block.garnished.smooth_red_zultanite_wall": "光滑红色祖尔坦石墙", "block.garnished.smooth_orange_zultanite_wall": "光滑橙色祖尔坦石墙", "block.garnished.smooth_yellow_zultanite_wall": "光滑黄色祖尔坦石墙", "block.garnished.smooth_green_zultanite_wall": "光滑绿色祖尔坦石墙", "block.garnished.smooth_lime_zultanite_wall": "光滑青柠祖尔坦石墙", "block.garnished.smooth_blue_zultanite_wall": "光滑蓝色祖尔坦石墙", "block.garnished.smooth_light_blue_zultanite_wall": "光滑淡蓝祖尔坦石墙", "block.garnished.smooth_cyan_zultanite_wall": "光滑青色祖尔坦石墙", "block.garnished.smooth_purple_zultanite_wall": "光滑紫色祖尔坦石墙", "block.garnished.smooth_magenta_zultanite_wall": "光滑洋红祖尔坦石墙", "block.garnished.smooth_pink_zultanite_wall": "光滑粉红祖尔坦石墙", "block.garnished.smooth_black_zultanite_wall": "光滑黑色祖尔坦石墙", "block.garnished.smooth_gray_zultanite_wall": "光滑灰色祖尔坦石墙", "block.garnished.smooth_light_gray_zultanite_wall": "光滑浅灰祖尔坦石墙", "block.garnished.smooth_white_zultanite_wall": "光滑白色祖尔坦石墙", "block.garnished.smooth_brown_zultanite_wall": "光滑棕色祖尔坦石墙", "item.garnished.mastic_resin_lime": "青柠乳香树脂", "item.garnished.mastic_resin_light_blue": "淡蓝乳香树脂", "item.garnished.mastic_resin_cyan": "青色乳香树脂", "item.garnished.mastic_resin_magenta": "洋红乳香树脂", "item.garnished.mastic_resin_pink": "粉红乳香树脂", "item.garnished.mastic_resin_black": "黑色乳香树脂", "item.garnished.mastic_resin_gray": "灰色乳香树脂", "item.garnished.mastic_resin_light_gray": "浅灰乳香树脂", "item.garnished.mastic_resin_white": "白色乳香树脂", "item.garnished.mastic_resin_brown": "棕色乳香树脂", "item.garnished.mastic_paste_lime": "青柠乳香糊", "item.garnished.mastic_paste_light_blue": "淡蓝乳香糊", "item.garnished.mastic_paste_cyan": "青色乳香糊", "item.garnished.mastic_paste_magenta": "洋红乳香糊", "item.garnished.mastic_paste_pink": "粉红乳香糊", "item.garnished.mastic_paste_black": "黑色乳香糊", "item.garnished.mastic_paste_gray": "灰色乳香糊", "item.garnished.mastic_paste_light_gray": "浅灰乳香糊", "item.garnished.mastic_paste_white": "白色乳香糊", "item.garnished.mastic_paste_brown": "棕色乳香糊", "item.garnished.boarded_pulp": "压制纸浆", "emi.garnished.enflamed_mandible.information": "使用毁灭附魔时，烈焰人可掉落", "emi.garnished.ghast_tendril.information": "使用毁灭附魔时，恶魂可掉落", "emi.garnished.molten_remnant.information": "使用毁灭附魔时，岩浆怪可掉落", "emi.garnished.polar_bear_meat.information": "使用毁灭附魔时，北极熊可掉落", "emi.garnished.polar_bear_hide.information": "使用毁灭附魔时，北极熊可掉落", "emi.garnished.stray_parchment.information": "使用毁灭附魔时，流浪者可掉落", "emi.garnished.tenebrous_meat.information": "使用毁灭附魔时，守卫者可掉落，也可在古代之城战利品箱中找到", "emi.garnished.tusk.information": "使用毁灭附魔时，疣猪兽可掉落；使用回收附魔时猪也有极小概率掉落", "emi.garnished.irate_tusk.information": "使用毁灭附魔时，疣猪兽有极小概率掉落", "emi.garnished.ender_scale.information": "使用毁灭附魔时，末影人、末影螨和末影龙有不同几率掉落", "emi.garnished.endermite_heart.information": "使用毁灭附魔时，末影螨可掉落", "emi.garnished.vex_wing.information": "使用毁灭附魔时，恶魂使可掉落；使用回收附魔时游荡者有极小概率掉落", "emi.garnished.ravager_meat.information": "使用毁灭附魔时，劫掠兽可掉落", "tag.item.c.buckets.mastic_resin": "液化乳香树脂桶", "tag.item.c.cheese": "奶酪", "tag.item.c.coral_fans": "珊瑚扇", "tag.item.c.ender_dusts": "末影尘", "tag.item.c.pumpkins": "南瓜", "tag.item.c.tools.hatchets": "斧头", "tag.item.c.tools.knives": "刀具", "tag.item.c.pecan": "碧根果", "tag.item.c.almond": "杏仁", "tag.item.c.cashew": "腰果", "tag.item.c.walnut": "核桃", "tag.item.c.peanut": "花生", "tag.item.c.chestnut": "板栗", "tag.item.c.hazelnut": "榛子", "tag.item.c.pistachio": "开心果", "tag.item.c.macadamia": "澳洲坚果", "tag.item.c.crops.pecan": "碧根果作物", "tag.item.c.crops.almond": "杏仁作物", "tag.item.c.crops.cashew": "腰果作物", "tag.item.c.crops.walnut": "核桃作物", "tag.item.c.crops.peanut": "花生作物", "tag.item.c.crops.chestnut": "板栗作物", "tag.item.c.crops.hazelnut": "榛子作物", "tag.item.c.crops.pistachio": "开心果作物", "tag.item.c.crops.macadamia": "澳洲坚果作物", "tag.item.c.nuts.pecan": "碧根果坚果", "tag.item.c.nuts.almond": "杏仁坚果", "tag.item.c.nuts.cashew": "腰果坚果", "tag.item.c.nuts.walnut": "核桃坚果", "tag.item.c.nuts.peanut": "花生坚果", "tag.item.c.nuts.chestnut": "板栗坚果", "tag.item.c.nuts.hazelnut": "榛子坚果", "tag.item.c.nuts.pistachio": "开心果坚果", "tag.item.c.nuts.macadamia": "澳洲坚果坚果", "tag.item.c.vegetables.pecan": "碧根果蔬菜", "tag.item.c.vegetables.almond": "杏仁蔬菜", "tag.item.c.vegetables.cashew": "腰果蔬菜", "tag.item.c.vegetables.walnut": "核桃蔬菜", "tag.item.c.vegetables.peanut": "花生蔬菜", "tag.item.c.vegetables.chestnut": "板栗蔬菜", "tag.item.c.vegetables.hazelnut": "榛子蔬菜", "tag.item.c.vegetables.pistachio": "开心果蔬菜", "tag.item.c.vegetables.macadamia": "澳洲坚果蔬菜", "tag.item.c.vegetables": "蔬菜", "tag.item.c.salt": "盐", "tag.item.c.nuts": "坚果", "tag.item.c.doors": "门", "tag.item.c.trapdoors": "活板门", "tag.item.c.wooden_buttons": "木按钮", "tag.item.c.wooden_doors": "木门", "tag.item.c.wooden_trapdoors": "木活板门", "tag.item.c.wooden_slabs": "木台阶", "tag.item.c.wooden_stairs": "木楼梯", "tag.item.garnished.quartz_hatchet": "石英斧", "tag.item.garnished.stone_types.zultanite": "所有祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.basic": "祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.red": "红色祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.orange": "橙色祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.yellow": "黄色祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.green": "绿色祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.lime": "青柠祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.blue": "蓝色祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.light_blue": "淡蓝祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.cyan": "青色祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.purple": "紫色祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.magenta": "洋红祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.pink": "粉红祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.black": "黑色祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.gray": "灰色祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.light_gray": "浅灰祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.white": "白色祖尔坦石类型", "tag.item.garnished.stone_types.zultanite.brown": "棕色祖尔坦石类型", "tag.item.garnished.acceptable_nopalito_meat_ingredients": "可用于仙人掌卷的肉类", "tag.item.garnished.aversion_foods": "会造成厌恶效果的食物", "tag.item.garnished.berries": "浆果", "tag.item.garnished.bottled.apple_cider": "瓶装苹果酒", "tag.item.garnished.bottled.peanut_oil": "瓶装花生油", "tag.item.garnished.bottled.cashew_mixture": "瓶装腰果混合物", "tag.item.garnished.buhg": "可解锁Buhg成就的物品", "tag.item.garnished.chocolate_glazed_nuts": "巧克力覆盖坚果", "tag.item.garnished.cinder_nuts": "灰烬坚果", "tag.item.garnished.coloured_mastic_paste": "彩色乳香糊", "tag.item.garnished.coloured_mastic_resin": "彩色乳香树脂", "tag.item.garnished.cracked_nuts": "破壳坚果", "tag.item.garnished.crimson_tusks": "赤红獠牙", "tag.item.garnished.crushable.almond": "可制成破壳杏仁的物品", "tag.item.garnished.crushable.cashew": "可制成破壳腰果的物品", "tag.item.garnished.crushable.chestnut": "可制成破壳板栗的物品", "tag.item.garnished.crushable.hazelnut": "可制成破壳榛子的物品", "tag.item.garnished.crushable.macadamia": "可制成破壳澳洲坚果的物品", "tag.item.garnished.crushable.peanut": "可制成破壳花生的物品", "tag.item.garnished.crushable.pecan": "可制成破壳碧根果的物品", "tag.item.garnished.crushable.pistachio": "可制成破壳开心果的物品", "tag.item.garnished.crushable.walnut": "可制成破壳核桃的物品", "tag.item.garnished.crushable": "可制成破壳坚果的物品", "tag.item.garnished.dried_kelp": "Garnished 模组干海带", "tag.item.garnished.garnished_foods": "含装饰液或装饰复合物的食物", "tag.item.garnished.garnished_nuts": "装饰坚果", "tag.item.garnished.kelp": "Garnished 模组海带", "tag.item.garnished.kelp_blocks": "Garnished 模组海带块", "tag.item.garnished.mastic_blocks": "乳香块", "tag.item.garnished.mastic_paste": "乳香糊", "tag.item.garnished.mastic_resin": "乳香树脂", "tag.item.garnished.melted_cinder_nuts": "融化灰烬面粉覆盖的坚果", "tag.item.garnished.melted_cinder_nuts_with_effects": "附带效果的融化灰烬面粉坚果", "tag.item.garnished.nut_logs": "坚果原木", "tag.item.garnished.nut_mix": "坚果混合物", "tag.item.garnished.nuts": "坚果", "tag.item.garnished.senile_sweets": "年老甜点", "tag.item.garnished.sepia_stems": "赭黄菌柄", "tag.item.garnished.slime_drops": "史莱姆软糖", "tag.item.garnished.stone_types.abyssal_stone": "深渊石类型", "tag.item.garnished.stone_types.carnotite": "硒铀矿石类型", "tag.item.garnished.stone_types.ritualistic_stone": "仪式石类型", "tag.item.garnished.stone_types.unstable_stone": "不稳定石类型", "tag.item.garnished.stone_types.dragon_stone": "飞龙石类型", "tag.item.garnished.stripped_nut_logs": "去皮坚果原木", "tag.item.garnished.stripped_sepia_stems": "去皮赭黄菌柄", "tag.item.garnished.sweetened_nuts": "甜味坚果", "tag.item.garnished.tangles": "Garnished 模组海藻结", "tag.item.garnished.ungarnished_nuts": "未装饰坚果", "tag.item.garnished.honeyed_nuts": "蜜渍坚果", "tag.item.garnished.venerable_delicacies": "尊享佳肴", "tag.item.minecraft.glazed_terracotta": "彩釉陶瓦", "tag.item.minecraft.shulker_boxes": "潜影贝盒", "tag.item.c.buckets.apple_cider": "苹果酒桶", "tag.item.c.buckets.liquid_garnish": "装饰液桶", "tag.item.c.buckets.cashew_mixture": "腰果混合液桶", "tag.item.c.buckets.dragon_breath": "龙息桶", "tag.item.c.buckets.peanut_oil": "花生油桶", "tag.item.c.buckets.sweet_tea": "甜茶桶", "tag.fluid.garnished.apple_cider": "苹果酒", "tag.fluid.garnished.cashew_mixture": "腰果混合液", "tag.fluid.garnished.fluids": "Garnished 模组流体", "tag.fluid.garnished.liquid_garnish": "装饰液", "tag.fluid.garnished.mastic_resin": "液化乳香树脂", "tag.fluid.garnished.peanut_oil": "花生油", "tag.fluid.garnished.dragon_breath": "龙息", "tag.fluid.garnished.sweet_tea": "甜茶", "create.item_attributes.freezable": "可冻结", "create.item_attributes.freezable.inverted": "不可冻结", "create.item_attributes.can_be_dyed_red": "可染成红色", "create.item_attributes.can_be_dyed_red.inverted": "不可染成红色", "create.item_attributes.can_be_dyed_orange": "可染成橙色", "create.item_attributes.can_be_dyed_orange.inverted": "不可染成橙色", "create.item_attributes.can_be_dyed_yellow": "可染成黄色", "create.item_attributes.can_be_dyed_yellow.inverted": "不可染成黄色", "create.item_attributes.can_be_dyed_green": "可染成绿色", "create.item_attributes.can_be_dyed_green.inverted": "不可染成绿色", "create.item_attributes.can_be_dyed_lime": "可染成青柠色", "create.item_attributes.can_be_dyed_lime.inverted": "不可染成青柠色", "create.item_attributes.can_be_dyed_blue": "可染成蓝色", "create.item_attributes.can_be_dyed_blue.inverted": "不可染成蓝色", "create.item_attributes.can_be_dyed_light_blue": "可染成淡蓝色", "create.item_attributes.can_be_dyed_light_blue.inverted": "不可染成淡蓝色", "create.item_attributes.can_be_dyed_cyan": "可染成青色", "create.item_attributes.can_be_dyed_cyan.inverted": "不可染成青色", "create.item_attributes.can_be_dyed_purple": "可染成紫色", "create.item_attributes.can_be_dyed_purple.inverted": "不可染成紫色", "create.item_attributes.can_be_dyed_magenta": "可染成洋红色", "create.item_attributes.can_be_dyed_magenta.inverted": "不可染成洋红色", "create.item_attributes.can_be_dyed_pink": "可染成粉红色", "create.item_attributes.can_be_dyed_pink.inverted": "不可染成粉红色", "create.item_attributes.can_be_dyed_black": "可染成黑色", "create.item_attributes.can_be_dyed_black.inverted": "不可染成黑色", "create.item_attributes.can_be_dyed_gray": "可染成灰色", "create.item_attributes.can_be_dyed_gray.inverted": "不可染成灰色", "create.item_attributes.can_be_dyed_light_gray": "可染成浅灰色", "create.item_attributes.can_be_dyed_light_gray.inverted": "不可染成浅灰色", "create.item_attributes.can_be_dyed_white": "可染成白色", "create.item_attributes.can_be_dyed_white.inverted": "不可染成白色", "create.item_attributes.can_be_dyed_brown": "可染成棕色", "create.item_attributes.can_be_dyed_brown.inverted": "不可染成棕色", "emi.category.garnished.fan_orange_dyeing": "批量染色", "garnished.recipe.garnished.orange_dye_blowing": "批量染色", "create.recipe.garnished.fan_orange_dyeing.fan": "橙色液化乳香风扇", "emi.category.garnished.fan_yellow_dyeing": "批量染色", "garnished.recipe.garnished.yellow_dye_blowing": "批量染色", "create.recipe.garnished.fan_yellow_dyeing.fan": "黄色液化乳香风扇", "emi.category.garnished.fan_green_dyeing": "批量染色", "garnished.recipe.garnished.green_dye_blowing": "批量染色", "create.recipe.garnished.fan_green_dyeing.fan": "绿色液化乳香风扇", "emi.category.garnished.fan_lime_dyeing": "批量染色", "garnished.recipe.garnished.lime_dye_blowing": "批量染色", "create.recipe.garnished.fan_lime_dyeing.fan": "青柠液化乳香风扇", "emi.category.garnished.fan_blue_dyeing": "批量染色", "garnished.recipe.garnished.blue_dye_blowing": "批量染色", "create.recipe.garnished.fan_blue_dyeing.fan": "蓝色液化乳香风扇", "emi.category.garnished.fan_light_blue_dyeing": "批量染色", "garnished.recipe.garnished.light_blue_dye_blowing": "批量染色", "create.recipe.garnished.fan_light_blue_dyeing.fan": "淡蓝液化乳香风扇", "emi.category.garnished.fan_cyan_dyeing": "批量染色", "garnished.recipe.garnished.cyan_dye_blowing": "批量染色", "create.recipe.garnished.fan_cyan_dyeing.fan": "青色液化乳香风扇", "emi.category.garnished.fan_purple_dyeing": "批量染色", "garnished.recipe.garnished.purple_dye_blowing": "批量染色", "create.recipe.garnished.fan_purple_dyeing.fan": "紫色液化乳香风扇", "emi.category.garnished.fan_magenta_dyeing": "批量染色", "garnished.recipe.garnished.magenta_dye_blowing": "批量染色", "create.recipe.garnished.fan_magenta_dyeing.fan": "洋红液化乳香风扇", "emi.category.garnished.fan_pink_dyeing": "批量染色", "garnished.recipe.garnished.pink_dye_blowing": "批量染色", "create.recipe.garnished.fan_pink_dyeing.fan": "粉红液化乳香风扇", "emi.category.garnished.fan_black_dyeing": "批量染色", "garnished.recipe.garnished.black_dye_blowing": "批量染色", "create.recipe.garnished.fan_black_dyeing.fan": "黑色液化乳香风扇", "emi.category.garnished.fan_gray_dyeing": "批量染色", "garnished.recipe.garnished.gray_dye_blowing": "批量染色", "create.recipe.garnished.fan_gray_dyeing.fan": "灰色液化乳香风扇", "emi.category.garnished.fan_light_gray_dyeing": "批量染色", "garnished.recipe.garnished.light_gray_dye_blowing": "批量染色", "create.recipe.garnished.fan_light_gray_dyeing.fan": "浅灰液化乳香风扇", "emi.category.garnished.fan_white_dyeing": "批量染色", "garnished.recipe.garnished.white_dye_blowing": "批量染色", "create.recipe.garnished.fan_white_dyeing.fan": "白色液化乳香风扇", "emi.category.garnished.fan_brown_dyeing": "批量染色", "garnished.recipe.garnished.brown_dye_blowing": "批量染色", "create.recipe.garnished.fan_brown_dyeing.fan": "棕色液化乳香风扇", "block.garnished.peanut_sack": "压缩花生袋", "block.garnished.cashew_sack": "压缩腰果袋", "block.garnished.almond_sack": "压缩杏仁袋", "block.garnished.walnut_sack": "压缩核桃袋", "block.garnished.macadamia_sack": "压缩澳洲坚果袋", "block.garnished.pecan_sack": "压缩碧根果袋", "block.garnished.pistachio_sack": "压缩开心果袋", "block.garnished.hazelnut_sack": "压缩榛子袋", "block.garnished.chestnut_sack": "压缩板栗袋", "block.garnished.nut_leaves": "坚果树叶", "block.garnished.unassigned_nut_leaves": "坚果树叶", "block.garnished.peanut_leaves": "花生叶", "block.garnished.cashew_leaves": "腰果叶", "block.garnished.almond_leaves": "杏仁叶", "block.garnished.walnut_leaves": "核桃叶", "block.garnished.macadamia_leaves": "澳洲坚果叶", "block.garnished.pistachio_leaves": "开心果叶", "block.garnished.hazelnut_leaves": "榛子叶", "block.garnished.pecan_leaves": "碧根果叶", "block.garnished.chestnut_leaves": "板栗叶", "item.garnished.garnish_bucket": "装饰液桶", "item.garnished.apple_cider_bucket": "苹果酒桶", "item.garnished.peanut_oil_bucket": "花生油桶", "item.garnished.cashew_mixture_bucket": "腰果混合液桶", "item.garnished.mastic_resin_bucket": "液化乳香树脂桶", "item.garnished.red_mastic_resin_bucket": "红色液化乳香桶", "item.garnished.orange_mastic_resin_bucket": "橙色液化乳香桶", "item.garnished.yellow_mastic_resin_bucket": "黄色液化乳香桶", "item.garnished.green_mastic_resin_bucket": "绿色液化乳香桶", "item.garnished.lime_mastic_resin_bucket": "青柠液化乳香桶", "item.garnished.blue_mastic_resin_bucket": "蓝色液化乳香桶", "item.garnished.light_blue_mastic_resin_bucket": "淡蓝液化乳香桶", "item.garnished.cyan_mastic_resin_bucket": "青色液化乳香桶", "item.garnished.purple_mastic_resin_bucket": "紫色液化乳香桶", "item.garnished.magenta_mastic_resin_bucket": "洋红液化乳香桶", "item.garnished.pink_mastic_resin_bucket": "粉红液化乳香桶", "item.garnished.black_mastic_resin_bucket": "黑色液化乳香桶", "item.garnished.gray_mastic_resin_bucket": "灰色液化乳香桶", "item.garnished.light_gray_mastic_resin_bucket": "浅灰液化乳香桶", "item.garnished.white_mastic_resin_bucket": "白色液化乳香桶", "item.garnished.brown_mastic_resin_bucket": "棕色液化乳香桶", "item.garnished.dragon_breath_bucket": "龙息桶", "item.garnished.sweet_tea_bucket": "甜茶桶", "fluid.garnished.garnish": "装饰液", "block.garnished.garnish": "装饰液", "fluid.garnished.apple_cider": "苹果酒", "block.garnished.apple_cider": "苹果酒", "fluid.garnished.peanut_oil": "花生油", "block.garnished.peanut_oil": "花生油", "fluid.garnished.cashew_mixture": "腰果混合液", "block.garnished.cashew_mixture": "腰果混合液", "fluid.garnished.mastic_resin": "液化乳香树脂", "block.garnished.mastic_resin": "液化乳香树脂", "fluid.garnished.red_mastic_resin": "红色液化乳香", "block.garnished.red_mastic_resin": "红色液化乳香", "fluid.garnished.orange_mastic_resin": "橙色液化乳香", "block.garnished.orange_mastic_resin": "橙色液化乳香", "fluid.garnished.yellow_mastic_resin": "黄色液化乳香", "block.garnished.yellow_mastic_resin": "黄色液化乳香", "fluid.garnished.green_mastic_resin": "绿色液化乳香", "fluid.garnished.lime_mastic_resin": "液化青柠乳香树脂", "block.garnished.lime_mastic_resin": "液化青柠乳香树脂", "fluid.garnished.blue_mastic_resin": "液化蓝色乳香树脂", "block.garnished.blue_mastic_resin": "液化蓝色乳香树脂", "fluid.garnished.light_blue_mastic_resin": "液化淡蓝乳香树脂", "block.garnished.light_blue_mastic_resin": "液化淡蓝乳香树脂", "fluid.garnished.cyan_mastic_resin": "液化青色乳香树脂", "block.garnished.cyan_mastic_resin": "液化青色乳香树脂", "fluid.garnished.purple_mastic_resin": "液化紫色乳香树脂", "block.garnished.purple_mastic_resin": "液化紫色乳香树脂", "fluid.garnished.magenta_mastic_resin": "液化洋红乳香树脂", "block.garnished.magenta_mastic_resin": "液化洋红乳香树脂", "fluid.garnished.pink_mastic_resin": "液化粉红乳香树脂", "block.garnished.pink_mastic_resin": "液化粉红乳香树脂", "fluid.garnished.black_mastic_resin": "液化黑色乳香树脂", "block.garnished.black_mastic_resin": "液化黑色乳香树脂", "fluid.garnished.gray_mastic_resin": "液化灰色乳香树脂", "block.garnished.gray_mastic_resin": "液化灰色乳香树脂", "fluid.garnished.light_gray_mastic_resin": "液化浅灰乳香树脂", "block.garnished.light_gray_mastic_resin": "液化浅灰乳香树脂", "fluid.garnished.white_mastic_resin": "液化白色乳香树脂", "block.garnished.white_mastic_resin": "液化白色乳香树脂", "fluid.garnished.brown_mastic_resin": "液化棕色乳香树脂", "block.garnished.brown_mastic_resin": "液化棕色乳香树脂", "fluid.garnished.dragon_breath": "龙息", "block.garnished.dragon_breath": "龙息", "fluid.garnished.sweet_tea": "甜茶", "block.garnished.sweet_tea": "甜茶", "block.garnished.nut_plant": "[Legacy] 坚果生长", "block.garnished.nut_sapling": "坚果树苗", "block.garnished.solidified_garnish": "固化装饰块", "item.garnished.garnish_compound": "装饰复合物", "item.garnished.garnish_powder": "装饰粉", "death.attack.garnished.mulch_munching": "%s 因吃太多腐殖而死亡", "death.attack.garnished.mulch_munching.player": "%1$s 被 %2$s 强行喂食过多腐殖致死", "death.attack.garnished.fan_freezing": "%1$s 被风扇冻成冰", "death.attack.garnished.fan_freezing.player": "%1$s 被 %2$s 冻死", "death.attack.garnished.leeching": "%1$s 受不了行动的后果", "death.attack.garnished.leeching.player": "%1$s…真不该和 %2$s 同时存在", "advancement.garnished.nut_tree.title": "欢迎来到 Create: Garnished", "advancement.garnished.nut_tree.description": "探索世界以获取未装饰坚果", "advancement.garnished.cracked_nuts.title": "碎坚果术！", "advancement.garnished.cracked_nuts.description": "将你的坚果敲开，为自动化做好准备", "advancement.garnished.garnished_nuts.title": "装饰进行时", "advancement.garnished.garnished_nuts.description": "在坚果上涂抹装饰液", "advancement.garnished.sweetened_nuts.title": "甜品…等等", "advancement.garnished.sweetened_nuts.description": "使用部署器和糖为坚果加糖", "advancement.garnished.liquid_garnish.title": "厨师之吻", "advancement.garnished.liquid_garnish.description": "混合糖、水和种子制作装饰液", "advancement.garnished.garnish_compound.title": "复合完成！", "advancement.garnished.garnish_compound.description": "获取装饰复合物以更高效地利用材料", "advancement.garnished.honeyed_nuts.title": "你喜欢爵士吗？", "advancement.garnished.honeyed_nuts.description": "在坚果上涂抹蜂蜜", "advancement.garnished.chocolate_glazed_nuts.title": "威利·旺卡扩展", "advancement.garnished.chocolate_glazed_nuts.description": "在坚果上涂抹巧克力", "advancement.garnished.nut_mix.title": "压缩坚果", "advancement.garnished.nut_mix.description": "获取坚果混合物", "advancement.garnished.garnished_meal.title": "走近深渊", "advancement.garnished.garnished_meal.description": "获取装饰餐点", "advancement.garnished.melted_cinder_effect_nuts.title": "谁还需要信标？", "advancement.garnished.melted_cinder_effect_nuts.description": "获取带效果的融化灰烬坚果", "advancement.garnished.apple_cider.title": "经典饮品", "advancement.garnished.apple_cider.description": "混合苹果、水、破壳坚果和装饰复合物制作苹果酒", "advancement.garnished.cryptic_apple_cider.title": "赌博", "advancement.garnished.cryptic_apple_cider.description": "在瓶装苹果酒中加入花朵", "advancement.garnished.ina_buhg.title": "这是一个Buhg", "advancement.garnished.ina_buhg.description": "获取某种花生变体", "advancement.garnished.walnut_brownie.title": "别生气", "advancement.garnished.walnut_brownie.description": "获取带核桃的布朗尼", "advancement.garnished.cashew_mixture.title": "举杯庆祝！", "advancement.garnished.cashew_mixture.description": "获取瓶装发酵腰果混合物", "advancement.garnished.tophet_brew.title": "地狱盛宴", "advancement.garnished.tophet_brew.description": "获取炼狱药剂", "advancement.garnished.aversion_effect.title": "情况可能更糟", "advancement.garnished.aversion_effect.description": "体验你的新坚果过敏", "advancement.garnished.senile_sweet.title": "石化甜点", "advancement.garnished.hatchets.title": "别再砍了！", "advancement.garnished.hatchets.description": "获得任意一种砍刀工具", "advancement.garnished.all_slime_drops.title": "彩虹史莱姆", "advancement.garnished.all_slime_drops.description": "获得所有颜色的史莱姆软糖", "advancement.garnished.slime_drops.title": "黏糊软糖", "advancement.garnished.slime_drops.description": "获得任意一种史莱姆软糖", "advancement.garnished.baklava.title": "丰盛大餐", "advancement.garnished.baklava.description": "获得一块巴克拉瓦", "advancement.garnished.orange_mastic.title": "橙意浓浓", "advancement.garnished.orange_mastic.description": "获得任何使用橙色乳香树脂制作的食物", "advancement.garnished.venerable_delicacies.title": "尊享盛宴！", "advancement.garnished.venerable_delicacies.description": "获得任意尊享佳肴", "advancement.garnished.liquid_mastic_resin.title": "液化树脂", "advancement.garnished.liquid_mastic_resin.description": "获得任意乳香树脂桶", "advancement.garnished.carnotite.title": "半糖石？", "advancement.garnished.carnotite.description": "获得一块硒铀矿", "advancement.garnished.abyssal_stone.title": "虚空？还是…？", "advancement.garnished.abyssal_stone.description": "获得一块深渊石", "advancement.garnished.unstable_stone.title": "凡界抽象化", "advancement.garnished.unstable_stone.description": "获得一块不稳定石", "advancement.garnished.consume_all_foods.title": "地狱之厨欢迎你！", "advancement.garnished.consume_all_foods.description": "食用 Create: Garnished 模组中的所有食物", "advancement.garnished.incendiary_stew.title": "炸！", "advancement.garnished.incendiary_stew.description": "获得一碗燃火炖菜", "advancement.garnished.lustrous_pearl.title": "色彩与形状", "advancement.garnished.lustrous_pearl.description": "获得一颗光泽珍珠", "advancement.garnished.voltaic_sea_grass.title": "惊电之草", "advancement.garnished.voltaic_sea_grass.description": "发现电光海草（别嚼它们！）", "advancement.garnished.dulse_kelp.title": "伪培根", "advancement.garnished.dulse_kelp.description": "获得一束紫菜海带", "advancement.garnished.ritualistic_stone.title": "新型远古残骸", "advancement.garnished.ritualistic_stone.description": "获得一块仪式石", "advancement.garnished.rosy_cocktail.title": "冰镇玫瑰汁", "advancement.garnished.rosy_cocktail.description": "获得一杯玫瑰鸡尾酒", "advancement.garnished.wyvern_stone.title": "＊咆哮＊", "advancement.garnished.wyvern_stone.description": "获得一块飞龙石", "advancement.garnished.bok_choyo.title": "白菜脱壳", "advancement.garnished.bok_choyo.description": "获得一株小白菜", "advancement.garnished.sweet_tea.title": "南方甜蜜", "advancement.garnished.sweet_tea.description": "获得一杯甜茶", "advancement.garnished.dejojotheawsome.title": "了不起！", "advancement.garnished.dejojotheawsome.description": "以创建者身份加入世界 – 感谢你，Dejojo", "advancement.garnished.anniversary_cake.title": "周年快乐！", "advancement.garnished.anniversary_cake.description": "食用一块周年庆蛋糕 – 感谢你对本项目的支持！"}