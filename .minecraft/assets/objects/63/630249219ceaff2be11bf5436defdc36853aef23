{"accessibility.onboarding.accessibility.button": "Penjetelan keterkaïtan...", "accessibility.onboarding.screen.narrator": "<PERSON><PERSON> enter untuk menyalakan pengisah", "accessibility.onboarding.screen.title": "Selamat datang di-Minecraft!\n\nApakah toean ingin menjalakan pengisah atau mengoendjoengi penjetelan keterkaïtan?", "addServer.add": "Se<PERSON><PERSON>", "addServer.enterIp": "<PERSON><PERSON><PERSON> p<PERSON>", "addServer.enterName": "<PERSON><PERSON> peladen", "addServer.resourcePack": "<PERSON>et soember peladen", "addServer.resourcePack.disabled": "mati", "addServer.resourcePack.enabled": "njala", "addServer.resourcePack.prompt": "kon<PERSON><PERSON><PERSON>", "addServer.title": "Oebah informasi peladen", "advMode.command": "<PERSON><PERSON><PERSON> k<PERSON>", "advMode.mode": "<PERSON><PERSON><PERSON>", "advMode.mode.auto": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "<PERSON><PERSON><PERSON>", "advMode.mode.conditional": "Bersjarat", "advMode.mode.redstone": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.redstoneTriggered": "Perloe redstone", "advMode.mode.sequence": "Berantai", "advMode.mode.unconditional": "<PERSON><PERSON> sjarat", "advMode.notAllowed": "<PERSON><PERSON> pengelola diragam kreatif", "advMode.notEnabled": "Balok perintah tidak diperbolehkan dalam peladen ini", "advMode.previousOutput": "<PERSON><PERSON><PERSON><PERSON>", "advMode.setCommand": "Tetapkan perintah kendali oentoek balok", "advMode.setCommand.success": "<PERSON><PERSON><PERSON> disetel: %s", "advMode.trackOutput": "Lacak keluaran", "advMode.triggering": "Memicu", "advMode.type": "<PERSON><PERSON>", "advancement.advancementNotFound": "Kemadjoean tidak dikenal: %s", "advancements.adventure.adventuring_time.description": "<PERSON><PERSON><PERSON><PERSON> set<PERSON> bioom", "advancements.adventure.adventuring_time.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.arbalistic.description": "Boenoeh lima machloek berbeda dengan satoe tembakan boesoer silang", "advancements.adventure.arbalistic.title": "Srikandi", "advancements.adventure.avoid_vibration.description": "Se<PERSON>edoek didekat pengindra sculk atau penoenggoe oentoek mentjegahnja mengindra toean", "advancements.adventure.avoid_vibration.title": "<PERSON><PERSON>-<PERSON><PERSON>", "advancements.adventure.blowback.description": "<PERSON><PERSON><PERSON> pawana dengan bola angin tembakan pawana jang dipantoelkan", "advancements.adventure.blowback.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.brush_armadillo.description": "Pakai sikat pada armadilo oentoek mendapatkan sisik armadilo", "advancements.adventure.brush_armadillo.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.bullseye.description": "Tembak tepat sasaran balok sasaran setidaknja dari djarak 30 meter", "advancements.adventure.bullseye.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Boeat pasoe dari 4 serpihan pasoe", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>i", "advancements.adventure.crafters_crafting_crafters.description": "Berada didekat mesin keradjinan jang sedang memboeat mesin keradjinan", "advancements.adventure.crafters_crafting_crafters.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.fall_from_world_height.description": "<PERSON><PERSON><PERSON><PERSON> bebas dari poentjak doenia (batas pembangoenan) sampai kebagian terendah doenia dan selamat", "advancements.adventure.fall_from_world_height.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.heart_transplanter.description": "Djadjar<PERSON> d<PERSON><PERSON> k<PERSON> (dengan arah jang benar) diantara doea gelondong ek poetjat", "advancements.adventure.heart_transplanter.title": "Pentjangkok Djantoeng", "advancements.adventure.hero_of_the_village.description": "<PERSON><PERSON><PERSON><PERSON> desa dari serboean", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON> jang <PERSON>", "advancements.adventure.honey_block_slide.description": "Lompat ke balok madoe oentoek mengham<PERSON> dja<PERSON><PERSON>an", "advancements.adventure.honey_block_slide.title": "Bagai Emped<PERSON>", "advancements.adventure.kill_a_mob.description": "Boenoeh monster djahat apa poen", "advancements.adventure.kill_a_mob.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_all_mobs.description": "<PERSON><PERSON><PERSON> set<PERSON> monster djahat", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "<PERSON><PERSON><PERSON> mac<PERSON>k didekat katalisator sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "<PERSON><PERSON>", "advancements.adventure.lighten_up.description": "<PERSON><PERSON> bohlam tembaga dengan kapak oentoek memboeatnja lebih terang", "advancements.adventure.lighten_up.title": "Terang se<PERSON>ai Siang", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Lindoengi pendoedoek dari petir tanpa menimboelkan kebakaran", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "<PERSON><PERSON>", "advancements.adventure.minecraft_trials_edition.description": "<PERSON><PERSON><PERSON><PERSON> bilik tjo<PERSON>n", "advancements.adventure.minecraft_trials_edition.title": "<PERSON><PERSON><PERSON> nan <PERSON>", "advancements.adventure.ol_betsy.description": "Tembak boesoer silang", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.overoverkill.description": "<PERSON><PERSON><PERSON><PERSON> serangan sebesar 50 hati dalam satoe bantingan gada", "advancements.adventure.overoverkill.title": "Hanoman", "advancements.adventure.play_jukebox_in_meadows.description": "Hidoepkan padang roempoet dengan soeara lagoe dari pemoetar pelat", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "<PERSON><PERSON> sinjal daja dari rak boekoe pahatan dengan pembanding", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "<PERSON><PERSON>", "advancements.adventure.revaulting.description": "Boeka brankas seram dengan koentji tjoba<PERSON>n seram", "advancements.adventure.revaulting.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.root.description": "<PERSON><PERSON><PERSON><PERSON>, mend<PERSON><PERSON><PERSON> dan be<PERSON><PERSON><PERSON>", "advancements.adventure.root.title": "Petoealangan", "advancements.adventure.salvage_sherd.description": "Sikat balok mentjoerigakan oentoek mendapatkan serpihan pasoe", "advancements.adventure.salvage_sherd.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.shoot_arrow.description": "Tembak se<PERSON>eatoe dengan panah", "advancements.adventure.shoot_arrow.title": "<PERSON><PERSON>", "advancements.adventure.sleep_in_bed.description": "<PERSON><PERSON><PERSON> dikatil oentoek mengoebah titik bangkit toean", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.sniper_duel.description": "<PERSON><PERSON><PERSON> ben<PERSON> dari djarak set<PERSON> 50 meter", "advancements.adventure.sniper_duel.title": "Tanding Tembak", "advancements.adventure.spyglass_at_dragon.description": "<PERSON><PERSON> Ender melaloei teropong", "advancements.adventure.spyglass_at_dragon.title": "<PERSON><PERSON><PERSON><PERSON>?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON><PERSON> ghast melaloei teropong", "advancements.adventure.spyglass_at_ghast.title": "Apakah Itoe Balon?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON><PERSON> bajan melaloei teropong", "advancements.adventure.spyglass_at_parrot.title": "Apakah Itoe Boeroeng?", "advancements.adventure.summon_iron_golem.description": "Panggil golem besi oentoek mempertahankan desa", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.throw_trident.description": "Lempar trisoela kepada se<PERSON>.\nPerhatian: melem<PERSON> satoe²-nja <PERSON>jata toean boekanlah gagasan bagoes.", "advancements.adventure.throw_trident.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.totem_of_undying.description": "Pakai azimat kea<PERSON>ian oentoek menghindari kematian", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.trade.description": "<PERSON><PERSON><PERSON><PERSON> berdagang dengan seorang pendo<PERSON>k", "advancements.adventure.trade.title": "<PERSON><PERSON>, <PERSON><PERSON>!", "advancements.adventure.trade_at_world_height.description": "Berdagang dengan pendoedoek dibatas tinggi pembangoenan", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Terapkan sablon tempa ini setidaknja sekali: men<PERSON>, montjong, r<PERSON><PERSON><PERSON>, djaga, keh<PERSON><PERSON>, o<PERSON><PERSON>, gel<PERSON>ang pasang, pentjari arah", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_any_armor_pattern.description": "<PERSON>as zirah dimedja tempa", "advancements.adventure.trim_with_any_armor_pattern.title": "In de mode", "advancements.adventure.two_birds_one_arrow.description": "<PERSON><PERSON><PERSON> doea angan-angan dengan panah men<PERSON>k", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON>, <PERSON><PERSON>", "advancements.adventure.under_lock_and_key.description": "Boeka brankas dengan koent<PERSON> t<PERSON>n", "advancements.adventure.under_lock_and_key.title": "Dibalik Koentji dan Gem<PERSON>k", "advancements.adventure.use_lodestone.description": "Pakai kompas pada batoe magnet", "advancements.adventure.use_lodestone.title": "Biarpoen <PERSON>", "advancements.adventure.very_very_frightening.description": "Sambar pendoedoek dengan petir", "advancements.adventure.very_very_frightening.title": "<PERSON><PERSON>", "advancements.adventure.voluntary_exile.description": "<PERSON><PERSON><PERSON> panglima penjer<PERSON>.\nMoengkin pertimbangkan oentoek mendjaoeh dari desa oentoek sa'at ini...", "advancements.adventure.voluntary_exile.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "<PERSON><PERSON><PERSON><PERSON> diata<PERSON>, tan<PERSON> terd<PERSON><PERSON><PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON>", "advancements.adventure.who_needs_rockets.description": "Pak<PERSON> bola angin oentoek meloentjoerkan diri toean setinggi 8 balok", "advancements.adventure.who_needs_rockets.title": "Oentoek Apa Roket?", "advancements.adventure.whos_the_pillager_now.description": "Boeat pendjarah merasakan dari send<PERSON><PERSON>ja sendiri", "advancements.adventure.whos_the_pillager_now.title": "Siapa Sang Pendjarah Se<PERSON>ng?", "advancements.empty": "Tidak ada apa poen disini...", "advancements.end.dragon_breath.description": "Koempoelkan napas Naga dalam botol katja", "advancements.end.dragon_breath.title": "<PERSON><PERSON>", "advancements.end.dragon_egg.description": "Pegang teloer Naga", "advancements.end.dragon_egg.title": "<PERSON><PERSON><PERSON>", "advancements.end.elytra.description": "Temo<PERSON><PERSON>", "advancements.end.elytra.title": "Langit sebagai Batas", "advancements.end.enter_end_gateway.description": "<PERSON><PERSON>r <PERSON><PERSON>", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON>", "advancements.end.find_end_city.description": "Masoeklah! Apa sadja jang moengkin bisa terdjadi?", "advancements.end.find_end_city.title": "Kota pada Achir <PERSON>", "advancements.end.kill_dragon.description": "<PERSON><PERSON><PERSON>", "advancements.end.kill_dragon.title": "Merdeka atau Mati!", "advancements.end.levitate.description": "Melajang hingga 50 balok dari serangan shulker", "advancements.end.levitate.title": "Tinggi, Tinggi Sekali", "advancements.end.respawn_dragon.description": "Bangkitkan kembali Na<PERSON>", "advancements.end.respawn_dragon.title": "<PERSON><PERSON><PERSON>... Lagi...", "advancements.end.root.description": "Apakah kita berada dipengoedjoeng?", "advancements.end.root.title": "End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Boeat penolong mendjatoehkan tar di kotak nada", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Pandjang Oemoernja!", "advancements.husbandry.allay_deliver_item_to_player.description": "Boeat penolong mengirimkan benda kepada toean", "advancements.husbandry.allay_deliver_item_to_player.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.axolotl_in_a_bucket.description": "Tangkap axolotl kedalam ember", "advancements.husbandry.axolotl_in_a_bucket.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON> se<PERSON> jang bisa dimakan, bahkan bila itoe tidak baïk oentoek <PERSON>an", "advancements.husbandry.balanced_diet.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.breed_all_animals.description": "Kembangbiakkan semoea hewan!", "advancements.husbandry.breed_all_animals.title": "<PERSON><PERSON> kali <PERSON>", "advancements.husbandry.breed_an_animal.description": "Kembangbiakkan doea hewan", "advancements.husbandry.breed_an_animal.title": "Bangau Pembawa Anak", "advancements.husbandry.complete_catalogue.description": "Djinakkan semoea djenis koetjing!", "advancements.husbandry.complete_catalogue.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.feed_snifflet.description": "<PERSON><PERSON> makan anak pengen<PERSON>", "advancements.husbandry.feed_snifflet.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.fishy_business.description": "Tangkap seékor ikan", "advancements.husbandry.fishy_business.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.froglights.description": "<PERSON><PERSON><PERSON> semoea lampoe katak didalam persed<PERSON>n <PERSON>an", "advancements.husbandry.froglights.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.kill_axolotl_target.description": "Menangkan per<PERSON>ngan bersama axolotl", "advancements.husbandry.kill_axolotl_target.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.leash_all_frog_variants.description": "Dapatkan setiap djenis katak pada tali pengikat", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON><PERSON>ng Kekot<PERSON>", "advancements.husbandry.make_a_sign_glow.description": "Boeat teks pada djenis papan tanda apa poen bersinar", "advancements.husbandry.make_a_sign_glow.title": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>!", "advancements.husbandry.netherite_hoe.description": "Pakai netherit batangan oentoek meningkatkan tjan<PERSON>el, laloe pikirkan kembali pilihan hidoep toean", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "advancements.husbandry.obtain_sniffer_egg.description": "Dapatkan seboeah teloer pengendoes", "advancements.husbandry.obtain_sniffer_egg.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.place_dried_ghast_in_water.description": "<PERSON><PERSON><PERSON> g<PERSON>t kering kedalam a<PERSON>r", "advancements.husbandry.place_dried_ghast_in_water.title": "Haoes Diberi Aïr", "advancements.husbandry.plant_any_sniffer_seed.description": "<PERSON><PERSON> benih pengendoes apa poen", "advancements.husbandry.plant_any_sniffer_seed.title": "Tanaman Tempo Doeloe", "advancements.husbandry.plant_seed.description": "<PERSON><PERSON> benih dan lihat itoe bertoe<PERSON><PERSON>", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "Lepas zirah serigala dari serigala dengan goenting", "advancements.husbandry.remove_wolf_armor.title": "Potongan Mena'd<PERSON>", "advancements.husbandry.repair_wolf_armor.description": "Perbaïki zirah serigala jang roesak dengan sisik armadilo", "advancements.husbandry.repair_wolf_armor.title": "Tampak Bagoes bak Baharoe", "advancements.husbandry.ride_a_boat_with_a_goat.description": "<PERSON><PERSON> per<PERSON>oe bersama kambing", "advancements.husbandry.ride_a_boat_with_a_goat.title": "<PERSON><PERSON><PERSON>-ambing!", "advancements.husbandry.root.description": "Doenia ini penoeh dengan kawan² dan makanan", "advancements.husbandry.root.title": "Peterna<PERSON>", "advancements.husbandry.safely_harvest_honey.description": "Pakai api oenggoen oentoek mengoempoelkan madoe dari sarang lebah dengan botol katja tanpa mengganggoe lebah", "advancements.husbandry.safely_harvest_honey.title": "<PERSON> Boenga Ada Lebah", "advancements.husbandry.silk_touch_nest.description": "Pindahkan sarang lebah atau indo<PERSON> madoe, dengan 3 lebah did<PERSON>, dengan sent<PERSON>han soetera", "advancements.husbandry.silk_touch_nest.title": "Sialan<PERSON>", "advancements.husbandry.tactical_fishing.description": "<PERSON><PERSON><PERSON> se<PERSON> ikan, tanpa tongkat pantjing!", "advancements.husbandry.tactical_fishing.title": "Pemantjing Taktis", "advancements.husbandry.tadpole_in_a_bucket.description": "Tang<PERSON>p beroedoe kedalam ember", "advancements.husbandry.tadpole_in_a_bucket.title": "<PERSON><PERSON>", "advancements.husbandry.tame_an_animal.description": "Djinakkan se<PERSON> hewan", "advancements.husbandry.tame_an_animal.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.wax_off.description": "<PERSON><PERSON> lilin dari balok tembaga!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.wax_on.description": "Loemoerkan sarang madoe kebalok tembaga!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.whole_pack.description": "<PERSON><PERSON><PERSON><PERSON> set<PERSON> dje<PERSON> se<PERSON>", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON><PERSON>", "advancements.nether.all_effects.description": "Dapatkan semoea efek disa'at bersamaän", "advancements.nether.all_effects.title": "Orang Terkebal Diantéro Doenia", "advancements.nether.all_potions.description": "Dapatkan semoea efek ramoean disa'at bersamaän", "advancements.nether.all_potions.title": "<PERSON><PERSON><PERSON>", "advancements.nether.brew_potion.description": "Ratjik sebotol ramoean", "advancements.nether.brew_potion.title": "Apoték <PERSON>t", "advancements.nether.charge_respawn_anchor.description": "<PERSON><PERSON> ang<PERSON> pembangkit hingga pen<PERSON>h", "advancements.nether.charge_respawn_anchor.title": "Tidak Persis \"Sembilan\" Njawa", "advancements.nether.create_beacon.description": "<PERSON><PERSON>n dan letakkan soear", "advancements.nether.create_beacon.title": "<PERSON><PERSON>, <PERSON><PERSON>!", "advancements.nether.create_full_beacon.description": "<PERSON><PERSON> so<PERSON> kekoeatan penoeh", "advancements.nether.create_full_beacon.title": "<PERSON><PERSON><PERSON>", "advancements.nether.distract_piglin.description": "<PERSON><PERSON><PERSON> per<PERSON>ian piglin dengan emas", "advancements.nether.distract_piglin.title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>!", "advancements.nether.explore_nether.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> semoea bioom Nether", "advancements.nether.explore_nether.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.fast_travel.description": "Pakai Nether oentoek bepergian 7 km di-Permoekaän", "advancements.nether.fast_travel.title": "Loebang <PERSON>", "advancements.nether.find_bastion.description": "<PERSON><PERSON><PERSON> k<PERSON> koe<PERSON>", "advancements.nether.find_bastion.title": "<PERSON><PERSON>", "advancements.nether.find_fortress.description": "<PERSON><PERSON><PERSON><PERSON> djalan k<PERSON>", "advancements.nether.find_fortress.title": "Benteng Laoetan Api", "advancements.nether.get_wither_skull.description": "Dapatkan tengkorak bengkarak Wither", "advancements.nether.get_wither_skull.title": "Rangka Djangka Langka", "advancements.nether.loot_bastion.description": "<PERSON><PERSON> seboeah peti dire<PERSON><PERSON><PERSON><PERSON> koeboe", "advancements.nether.loot_bastion.title": "<PERSON><PERSON>", "advancements.nether.netherite_armor.description": "Dapatkan seperangkat zirah netherit", "advancements.nether.netherite_armor.title": "<PERSON><PERSON><PERSON><PERSON> den<PERSON>", "advancements.nether.obtain_ancient_debris.description": "Dapatkan poeing poerba", "advancements.nether.obtain_ancient_debris.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.obtain_blaze_rod.description": "Dapatkan tongkat kemamang", "advancements.nether.obtain_blaze_rod.title": "<PERSON> Reboet Kembali", "advancements.nether.obtain_crying_obsidian.description": "Dapatkan batoe gelas menangis", "advancements.nether.obtain_crying_obsidian.title": "Mengapa <PERSON>?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON><PERSON> ghast dengan bola api", "advancements.nether.return_to_sender.title": "<PERSON><PERSON><PERSON>", "advancements.nether.ride_strider.description": "<PERSON><PERSON><PERSON> pen<PERSON>ng dengan djamoer keroekoet ditongkat", "advancements.nether.ride_strider.title": "Perah<PERSON> Ini <PERSON>", "advancements.nether.ride_strider_in_overworld_lava.description": "Bawa pengaroeng dalam perdjalanan djaoeh mengaroengi danau lava di-Permoekaän", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "advancements.nether.root.description": "<PERSON><PERSON><PERSON> pakai badjoe tebal", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON>, Diang<PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "Selamat<PERSON> ghast da<PERSON>, bawa poelang ke-<PERSON><PERSON><PERSON><PERSON> dengan selamat, kemoedian di<PERSON><PERSON><PERSON>", "advancements.nether.uneasy_alliance.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.use_lodestone.description": "Pakai kompas pada batoe magnet", "advancements.nether.use_lodestone.title": "Biarpoen <PERSON>", "advancements.progress": "%s/%s", "advancements.sad_label": "❌", "advancements.story.cure_zombie_villager.description": "Lemahkan laloe semboehkan zombi pendo<PERSON>k", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON>", "advancements.story.deflect_arrow.description": "Tangkis tembakan dengan perisai", "advancements.story.deflect_arrow.title": "Tidak <PERSON>, <PERSON><PERSON> Kasi<PERSON>", "advancements.story.enchant_item.description": "<PERSON><PERSON>akan satoe benda dimedja pesona", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "Masoek kegerbang End", "advancements.story.enter_the_end.title": "Tamat?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON><PERSON>, nja<PERSON>an dan masoek gerbang <PERSON>", "advancements.story.enter_the_nether.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON> mata <PERSON>", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.form_obsidian.description": "Dapatkan balok batoe gelas", "advancements.story.form_obsidian.title": "<PERSON><PERSON>", "advancements.story.iron_tools.description": "Tingkatkan belioeng <PERSON>an", "advancements.story.iron_tools.title": "Belioeng Tjap „Badja”", "advancements.story.lava_bucket.description": "Isi ember dengan lava", "advancements.story.lava_bucket.title": "<PERSON><PERSON>", "advancements.story.mine_diamond.description": "Dapat<PERSON> berlian", "advancements.story.mine_diamond.title": "<PERSON><PERSON><PERSON>!", "advancements.story.mine_stone.description": "Tambang batoe dengan belioeng baroe toean", "advancements.story.mine_stone.title": "<PERSON><PERSON>", "advancements.story.obtain_armor.description": "Lindoengi diri toean dengan zirah besi", "advancements.story.obtain_armor.title": "Siap Sedia", "advancements.story.root.description": "<PERSON><PERSON><PERSON> k<PERSON>", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>wa", "advancements.story.shiny_gear.title": "Toetoepi <PERSON> den<PERSON>", "advancements.story.smelt_iron.description": "Leboe<PERSON> be<PERSON> batangan", "advancements.story.smelt_iron.title": "<PERSON><PERSON>", "advancements.story.upgrade_tools.description": "<PERSON><PERSON><PERSON> be<PERSON> jang lebih ba<PERSON>k", "advancements.story.upgrade_tools.title": "Mendapatkan Peningkatan", "advancements.toast.challenge": "Tantangan selesai!", "advancements.toast.goal": "<PERSON><PERSON><PERSON> di<PERSON>!", "advancements.toast.task": "Kemadjoean tertjapai!", "argument.anchor.invalid": "Posisi penambatan entitet „%s” tidak sah", "argument.angle.incomplete": "Tidak lengkap (1 soedoet diharapkan)", "argument.angle.invalid": "Soedoet tidak sah", "argument.block.id.invalid": "Djenis balok „%s” tidak dikenal", "argument.block.property.duplicate": "Sifat „%s” hanja bisa disetel sekali setiap balok %s", "argument.block.property.invalid": "Balok %s tidak menerima „%s” sebagai sifat %s", "argument.block.property.novalue": "<PERSON><PERSON> si<PERSON> „%s” pada balok %s diharapkan", "argument.block.property.unclosed": "Koeroeng penoetoep „]” oentoek sifat keadaän balok diharapkan", "argument.block.property.unknown": "Balok %s tidak poenja sifat „%s”", "argument.block.tag.disallowed": "Etikét tidak diïzinkan disini, hanja balok", "argument.color.invalid": "Warna „%s” tidak dikenal", "argument.component.invalid": "Komponen obrolan tidak sah: %s", "argument.criteria.invalid": "Patokan „%s” tidak dikenal", "argument.dimension.invalid": "Dimensi „%s” tidak dikenal", "argument.double.big": "Angka ganda ta' boleh lebih dari %s, ditemoekan %s", "argument.double.low": "Angka ganda ta' boleh koerang dari %s, ditemoekan %s", "argument.entity.invalid": "Nama atau pengenal choesoes semesta tidak sah", "argument.entity.notfound.entity": "Entitet tidak ditemoekan", "argument.entity.notfound.player": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "argument.entity.options.advancements.description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "argument.entity.options.distance.description": "Djarak keëntitet", "argument.entity.options.distance.negative": "Djarak tidak boleh negatif", "argument.entity.options.dx.description": "Entitet antara x dan x + dx", "argument.entity.options.dy.description": "Entitet antara y dan y + dy", "argument.entity.options.dz.description": "Entitet antara z dan z + dz", "argument.entity.options.gamemode.description": "<PERSON><PERSON><PERSON><PERSON> dengan ragam permaïnan", "argument.entity.options.inapplicable": "Opsi „%s” tidak bisa diterapkan disini", "argument.entity.options.level.description": "Tingkat pen<PERSON>aman", "argument.entity.options.level.negative": "Tingkat tidak boleh negatif", "argument.entity.options.limit.description": "<PERSON><PERSON><PERSON><PERSON> maksimal entitet oentoek dikembalikan", "argument.entity.options.limit.toosmall": "Batas haroes setidaknja 1", "argument.entity.options.mode.invalid": "Ragam permaïnan „%s” tidak dikenal atau tidak sah", "argument.entity.options.name.description": "<PERSON><PERSON> en<PERSON>t", "argument.entity.options.nbt.description": "Entitet dengan é<PERSON> bin<PERSON>", "argument.entity.options.predicate.description": "<PERSON><PERSON><PERSON> o<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "argument.entity.options.scores.description": "En<PERSON>t dengan nilai", "argument.entity.options.sort.description": "Oeroetkan entitet", "argument.entity.options.sort.irreversible": "<PERSON><PERSON><PERSON> „%s” tidak dikenal atau tidak sah", "argument.entity.options.tag.description": "Entitet dengan <PERSON>", "argument.entity.options.team.description": "Entitet pada regoe", "argument.entity.options.type.description": "<PERSON><PERSON><PERSON> en<PERSON>", "argument.entity.options.type.invalid": "<PERSON><PERSON><PERSON> entitet „%s” tidak dikenal atau tidak sah", "argument.entity.options.unknown": "Opsi „%s” tidak dikenal", "argument.entity.options.unterminated": "<PERSON><PERSON>r opsi diharapkan", "argument.entity.options.valueless": "<PERSON><PERSON> opsi „%s” diharapkan", "argument.entity.options.x.description": "Posisi x", "argument.entity.options.x_rotation.description": "Rotasi x entitet", "argument.entity.options.y.description": "Posisi y", "argument.entity.options.y_rotation.description": "Rotasi y entitet", "argument.entity.options.z.description": "Posisi z", "argument.entity.selector.allEntities": "<PERSON><PERSON><PERSON> entitet", "argument.entity.selector.allPlayers": "<PERSON><PERSON><PERSON>", "argument.entity.selector.missing": "<PERSON><PERSON><PERSON> pem<PERSON> hilang", "argument.entity.selector.nearestEntity": "Entitet terdekat", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON><PERSON>", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON><PERSON>", "argument.entity.selector.self": "Entitet sa'at ini", "argument.entity.selector.unknown": "<PERSON><PERSON><PERSON> pem<PERSON> „%s” tidak dikenal", "argument.entity.toomany": "<PERSON><PERSON> satoe entitet jang di<PERSON>, tetapi pemilih jang disediakan memoengkinkan entitet lebih dari satoe", "argument.enum.invalid": "<PERSON><PERSON> tidak sah „%s”", "argument.float.big": "Angka mengambang ta' boleh lebih dari %s, ditemoekan %s", "argument.float.low": "Angka mengambang ta' boleh koerang dari %s, dtemoekan %s", "argument.gamemode.invalid": "Ragam permaïnan tidak dikenal: %s", "argument.hexcolor.invalid": "Kode warna heksadesimal „%s” tidak sah", "argument.id.invalid": "Pengenal tidak sah", "argument.id.unknown": "Pengenal tidak dikenal: %s", "argument.integer.big": "Angka boelat ta' boleh lebih dari %s, ditemoekan %s", "argument.integer.low": "Angka boelat ta' boleh koerang dari %s, ditemoekan %s", "argument.item.id.invalid": "Benda „%s” tidak dikenal", "argument.item.tag.disallowed": "Etikét tidak diïzinkan disini, hanja benda", "argument.literal.incorrect": "Harfiah %s diharap<PERSON>", "argument.long.big": "Angka besar ta' boleh lebih dari %s, ditemoekan %s", "argument.long.low": "Angka besar ta' boleh koerang dari %s, ditemoekan %s", "argument.message.too_long": "Pesan obrolan terlaloe pandjang (%s > maksimal %s aksara)", "argument.nbt.array.invalid": "<PERSON><PERSON><PERSON> la<PERSON> „%s” tidak sah", "argument.nbt.array.mixed": "Tidak bisa memasoekkan %s kedalam %s", "argument.nbt.expected.compound": "Etik<PERSON><PERSON>", "argument.nbt.expected.key": "<PERSON><PERSON><PERSON>", "argument.nbt.expected.value": "<PERSON><PERSON>", "argument.nbt.list.mixed": "Tidak bisa memasoekkan %s kedalam daftar %s", "argument.nbt.trailing": "Ekor data tidak diharapkan", "argument.player.entities": "<PERSON>intah hanja memen<PERSON><PERSON><PERSON> p<PERSON>, tetapi pemilih jang disediakan menjertakan entitet", "argument.player.toomany": "<PERSON><PERSON> diper<PERSON> satoe pemaïn, tetapi pemilih jang diberikan memoengkinkan lebih dari satoe pemaïn", "argument.player.unknown": "<PERSON><PERSON><PERSON>n itoe tidak ada", "argument.pos.missing.double": "Koördinat diharapkan", "argument.pos.missing.int": "Posisi balok diharapkan", "argument.pos.mixed": "Tidak bisa mentjampoerkan koördinat doenia dan lokal (semoea haroes memakai antara ^ atau tidak)", "argument.pos.outofbounds": "Posisi itoe berada diloear batas jang diperbolehkan.", "argument.pos.outofworld": "Posisi itoe berada diloear doenia ini!", "argument.pos.unloaded": "Posisi itoe tidak termoeat", "argument.pos2d.incomplete": "Tidak lengkap (2 koördinat diharapkan)", "argument.pos3d.incomplete": "Tidak lengkap (3 koördinat diharapkan)", "argument.range.empty": "<PERSON><PERSON> atau rentang nilai diharapkan", "argument.range.ints": "<PERSON><PERSON> men<PERSON> angka boe<PERSON>, boekan pet<PERSON>an", "argument.range.swapped": "<PERSON><PERSON> tidak boleh lebih besar daripada nilai maksimal", "argument.resource.invalid_type": "Elemen „%s” poenja djenis „%s” jang salah (mengharapkan „%s”)", "argument.resource.not_found": "Tidak bisa menemoekan elemen „%s” berdjenis „%s”", "argument.resource_or_id.failed_to_parse": "Bangoenan gagal dioeraikan: %s", "argument.resource_or_id.invalid": "Pengenal atau étikét tidak sah", "argument.resource_or_id.no_such_element": "Tidak bisa mentjari elemen „%s” di pendaftaran „%s”", "argument.resource_selector.not_found": "Pemilih „%s” berdjenis „%s” tidak ditemoekan", "argument.resource_tag.invalid_type": "Etikét „%s” poenja djenis „%s” jang salah („%s” diharapkan)", "argument.resource_tag.not_found": "Tidak bisa menemoekan étikét „%s” berdjenis „%s”", "argument.rotation.incomplete": "Tidak lengkap (2 koördinat diharapkan)", "argument.scoreHolder.empty": "Pemilik nilai tidak bisa ditem<PERSON>kan", "argument.scoreboardDisplaySlot.invalid": "Petak tampil „%s” tidak dikenal", "argument.style.invalid": "Gaja tidak sah: %s", "argument.time.invalid_tick_count": "<PERSON><PERSON><PERSON><PERSON> tik tidak boleh negatif", "argument.time.invalid_unit": "Satoean tidak sah", "argument.time.tick_count_too_low": "Djoemlah tik tidak boleh koerang dari %s, ditemoekan %s", "argument.uuid.invalid": "Pengenal choesoes semesta tidak sah", "argument.waypoint.invalid": "Entitet terpilih boekan titik-djalan", "arguments.block.tag.unknown": "Etikét balok „%s” tidak dikenal", "arguments.function.tag.unknown": "Etikét roetin „%s” tidak dikenal", "arguments.function.unknown": "Roetin „%s” tidak dikenal", "arguments.item.component.expected": "Komponen benda diharapkan", "arguments.item.component.malformed": "Komponen „%s” tjatjat: „%s”", "arguments.item.component.repeated": "Komponen benda „%s<PERSON> di<PERSON>, tetapi hanja satoe nilai jang bisa ditentoekan", "arguments.item.component.unknown": "Komponen benda „%s” tidak dikenal", "arguments.item.malformed": "<PERSON><PERSON>: „%s”", "arguments.item.overstacked": "%s hanja bisa ditoempoek hingga %s", "arguments.item.predicate.malformed": "Predikat „%s” tjatjat: „%s”", "arguments.item.predicate.unknown": "Predikat benda tidak dikenal: „%s”", "arguments.item.tag.unknown": "Etikét benda „%s” tidak dikenal", "arguments.nbtpath.node.invalid": "Elemen d<PERSON> bin<PERSON>r bernama tidak sah", "arguments.nbtpath.nothing_found": "Elemen jang sesoeai dengan %s tidak ditemoekan", "arguments.nbtpath.too_deep": "<PERSON><PERSON><PERSON><PERSON><PERSON> bin<PERSON>r bernama jang dihasilkan bersarang terlaloe dalam", "arguments.nbtpath.too_large": "<PERSON><PERSON><PERSON><PERSON><PERSON> bin<PERSON>r bernama jang dihasilkan terlaloe besar", "arguments.objective.notFound": "Toed<PERSON>ean papan-nilai „%s” tidak dikenal", "arguments.objective.readonly": "Toedjoean papan-nilai „%s” bersifat hanja-batja", "arguments.operation.div0": "Tidak bisa membagi dengan nol", "arguments.operation.invalid": "Operasi tidak sah", "arguments.swizzle.invalid": "Kombinasi soemboe tidak sah, mengharapkan kombinasi „x”, „y” dan „z”", "attribute.modifier.equals.0": "%2$s sebesar %1$s", "attribute.modifier.equals.1": "%2$s %1$s%%", "attribute.modifier.equals.2": "%2$s %1$s%%", "attribute.modifier.plus.0": "%2$s +%1$s", "attribute.modifier.plus.1": "%2$s +%1$s%%", "attribute.modifier.plus.2": "%2$s +%1$s%%", "attribute.modifier.take.0": "%2$s -%1$s", "attribute.modifier.take.1": "%2$s -%1$s%%", "attribute.modifier.take.2": "%2$s -%1$s%%", "attribute.name.armor": "Zira<PERSON>", "attribute.name.armor_toughness": "Ke<PERSON>gg<PERSON><PERSON> zirah", "attribute.name.attack_damage": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.attack_knockback": "Serangan tolak moendoer", "attribute.name.attack_speed": "<PERSON><PERSON>je<PERSON><PERSON> serangan", "attribute.name.block_break_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON> hant<PERSON> balok", "attribute.name.block_interaction_range": "Rentang interaksi balok", "attribute.name.burning_time": "Tempo terbakar", "attribute.name.camera_distance": "<PERSON><PERSON><PERSON> kamera", "attribute.name.entity_interaction_range": "Rentang interaksi entitet", "attribute.name.explosion_knockback_resistance": "<PERSON><PERSON><PERSON><PERSON> tolak moendoer ledakan", "attribute.name.fall_damage_multiplier": "Pengali loeka dja<PERSON>eh", "attribute.name.flying_speed": "Ketjepatan terbang", "attribute.name.follow_range": "<PERSON><PERSON><PERSON>", "attribute.name.generic.armor": "Zira<PERSON>", "attribute.name.generic.armor_toughness": "Ke<PERSON>gg<PERSON><PERSON> zirah", "attribute.name.generic.attack_damage": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_knockback": "Serangan tolak moendoer", "attribute.name.generic.attack_speed": "<PERSON><PERSON>je<PERSON><PERSON> serangan", "attribute.name.generic.block_interaction_range": "Rentang interaksi balok", "attribute.name.generic.burning_time": "Tempo terbakar", "attribute.name.generic.entity_interaction_range": "Rentang interaksi entitet", "attribute.name.generic.explosion_knockback_resistance": "<PERSON><PERSON><PERSON><PERSON> tolak moendoer ledakan", "attribute.name.generic.fall_damage_multiplier": "Pengali loeka dja<PERSON>eh", "attribute.name.generic.flying_speed": "Ketjepatan terbang", "attribute.name.generic.follow_range": "<PERSON><PERSON><PERSON>", "attribute.name.generic.gravity": "<PERSON>aja berat", "attribute.name.generic.jump_strength": "Ke<PERSON>eatan lo<PERSON>n", "attribute.name.generic.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON> tolak moendoer", "attribute.name.generic.luck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.max_absorption": "Penjerapan maksimal", "attribute.name.generic.max_health": "<PERSON><PERSON><PERSON>", "attribute.name.generic.movement_efficiency": "Kesangkilan per<PERSON>akan", "attribute.name.generic.movement_speed": "Ketjepatan", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON><PERSON> napas bawah a<PERSON>r", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON><PERSON> d<PERSON> aman", "attribute.name.generic.scale": "<PERSON><PERSON><PERSON>", "attribute.name.generic.step_height": "<PERSON>gg<PERSON> langkah", "attribute.name.generic.water_movement_efficiency": "Kesangki<PERSON> per<PERSON> a<PERSON>", "attribute.name.gravity": "<PERSON>aja berat", "attribute.name.horse.jump_strength": "Kekoeatan lompatan koeda", "attribute.name.jump_strength": "Ke<PERSON>eatan lo<PERSON>n", "attribute.name.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON> tolak moendoer", "attribute.name.luck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.max_absorption": "Penjerapan maksimal", "attribute.name.max_health": "<PERSON><PERSON><PERSON>", "attribute.name.mining_efficiency": "Kesangkilan menambang", "attribute.name.movement_efficiency": "Kesangkilan per<PERSON>akan", "attribute.name.movement_speed": "Ketjepatan", "attribute.name.oxygen_bonus": "<PERSON><PERSON><PERSON> napas bawah a<PERSON>r", "attribute.name.player.block_break_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON> hant<PERSON> balok", "attribute.name.player.block_interaction_range": "Rentang interaksi balok", "attribute.name.player.entity_interaction_range": "Rentang interaksi entitet", "attribute.name.player.mining_efficiency": "Kesangkilan menambang", "attribute.name.player.sneaking_speed": "Ketjepatan seloedoek", "attribute.name.player.submerged_mining_speed": "Ketjepa<PERSON> menambang bawah a<PERSON>", "attribute.name.player.sweeping_damage_ratio": "<PERSON><PERSON>", "attribute.name.safe_fall_distance": "<PERSON><PERSON><PERSON> d<PERSON> aman", "attribute.name.scale": "<PERSON><PERSON><PERSON>", "attribute.name.sneaking_speed": "Ketjepatan seloedoek", "attribute.name.spawn_reinforcements": "<PERSON><PERSON> ban<PERSON> zombi", "attribute.name.step_height": "<PERSON>gg<PERSON> langkah", "attribute.name.submerged_mining_speed": "Ketjepa<PERSON> menambang bawah a<PERSON>", "attribute.name.sweeping_damage_ratio": "<PERSON><PERSON>", "attribute.name.tempt_range": "<PERSON><PERSON><PERSON>", "attribute.name.water_movement_efficiency": "Kesangki<PERSON> per<PERSON> a<PERSON>", "attribute.name.waypoint_receive_range": "Djarak terima titik-djalan", "attribute.name.waypoint_transmit_range": "Djarak pantjar titik-djalan", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON> ban<PERSON> zombi", "biome.minecraft.badlands": "<PERSON><PERSON> tandoes", "biome.minecraft.bamboo_jungle": "<PERSON><PERSON><PERSON> bamboe", "biome.minecraft.basalt_deltas": "Délta basal", "biome.minecraft.beach": "Pantai", "biome.minecraft.birch_forest": "<PERSON>etan berk", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON> kersen", "biome.minecraft.cold_ocean": "<PERSON><PERSON><PERSON> dingin", "biome.minecraft.crimson_forest": "<PERSON><PERSON><PERSON> kir<PERSON>", "biome.minecraft.dark_forest": "Hoetan gelap", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON><PERSON> dingin dalam", "biome.minecraft.deep_dark": "Goea gelita", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON><PERSON> bekoe dalam", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON><PERSON> so<PERSON>m dalam", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "<PERSON><PERSON> batoe tetes", "biome.minecraft.end_barrens": "<PERSON><PERSON> gers<PERSON>", "biome.minecraft.end_highlands": "Tanah tinggi End", "biome.minecraft.end_midlands": "<PERSON><PERSON> tangg<PERSON>", "biome.minecraft.eroded_badlands": "<PERSON><PERSON> tandoes kikis", "biome.minecraft.flower_forest": "<PERSON><PERSON><PERSON> boenga", "biome.minecraft.forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON> bekoe", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON> bekoe", "biome.minecraft.frozen_river": "Soengai bekoe", "biome.minecraft.grove": "<PERSON><PERSON><PERSON> go<PERSON>", "biome.minecraft.ice_spikes": "Pak<PERSON> es", "biome.minecraft.jagged_peaks": "<PERSON><PERSON><PERSON>", "biome.minecraft.jungle": "Rimba", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON> so<PERSON>m", "biome.minecraft.lush_caves": "<PERSON><PERSON>", "biome.minecraft.mangrove_swamp": "<PERSON>a bakau", "biome.minecraft.meadow": "Padang roe<PERSON>", "biome.minecraft.mushroom_fields": "Padan<PERSON> d<PERSON>", "biome.minecraft.nether_wastes": "<PERSON>ah kosong Nether", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "<PERSON><PERSON><PERSON> berk <PERSON>a", "biome.minecraft.old_growth_pine_taiga": "<PERSON><PERSON>a", "biome.minecraft.old_growth_spruce_taiga": "Taiga spar toea", "biome.minecraft.pale_garden": "Keboen poet<PERSON><PERSON>", "biome.minecraft.plains": "Dataran", "biome.minecraft.river": "Soengai", "biome.minecraft.savanna": "Sabana", "biome.minecraft.savanna_plateau": "Plato sabana", "biome.minecraft.small_end_islands": "<PERSON><PERSON> <PERSON>", "biome.minecraft.snowy_beach": "<PERSON><PERSON>", "biome.minecraft.snowy_plains": "<PERSON><PERSON>", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON>", "biome.minecraft.snowy_taiga": "<PERSON><PERSON>", "biome.minecraft.soul_sand_valley": "<PERSON><PERSON><PERSON> p<PERSON>r d<PERSON>wa", "biome.minecraft.sparse_jungle": "R<PERSON>ba d<PERSON>", "biome.minecraft.stony_peaks": "Poentjak berbatoe", "biome.minecraft.stony_shore": "P<PERSON>ir berb<PERSON>", "biome.minecraft.sunflower_plains": "<PERSON><PERSON> boenga mat<PERSON>", "biome.minecraft.swamp": "<PERSON><PERSON>", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "End", "biome.minecraft.the_void": "<PERSON><PERSON>", "biome.minecraft.warm_ocean": "<PERSON><PERSON><PERSON> hangat", "biome.minecraft.warped_forest": "Hoetan keroekoet", "biome.minecraft.windswept_forest": "<PERSON><PERSON><PERSON> boekit berangin", "biome.minecraft.windswept_gravelly_hills": "<PERSON><PERSON><PERSON> keri<PERSON>l berangin", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON> be<PERSON>", "biome.minecraft.windswept_savanna": "Sabana berangin", "biome.minecraft.wooded_badlands": "<PERSON><PERSON> tandoes berpohon", "block.minecraft.acacia_button": "<PERSON><PERSON> akasia", "block.minecraft.acacia_door": "<PERSON><PERSON><PERSON> akasia", "block.minecraft.acacia_fence": "Pagar akasia", "block.minecraft.acacia_fence_gate": "<PERSON><PERSON><PERSON> pagar akasia", "block.minecraft.acacia_hanging_sign": "Papan tanda gantoeng akasia", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON> akasia", "block.minecraft.acacia_log": "Gelondong akasia", "block.minecraft.acacia_planks": "Papan akasia", "block.minecraft.acacia_pressure_plate": "<PERSON><PERSON>t penekan akasia", "block.minecraft.acacia_sapling": "<PERSON><PERSON><PERSON> aka<PERSON>", "block.minecraft.acacia_sign": "<PERSON><PERSON> tanda akasia", "block.minecraft.acacia_slab": "Pelat akasia", "block.minecraft.acacia_stairs": "Tangga akasia", "block.minecraft.acacia_trapdoor": "Pintoe kolong akasia", "block.minecraft.acacia_wall_hanging_sign": "Papan tanda gantoeng dinding akasia", "block.minecraft.acacia_wall_sign": "<PERSON>n tanda dinding akasia", "block.minecraft.acacia_wood": "<PERSON><PERSON><PERSON> aka<PERSON>", "block.minecraft.activator_rail": "<PERSON><PERSON>", "block.minecraft.air": "<PERSON><PERSON><PERSON>", "block.minecraft.allium": "<PERSON><PERSON><PERSON> bawang", "block.minecraft.amethyst_block": "Balok ketjoeboeng", "block.minecraft.amethyst_cluster": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.ancient_debris": "<PERSON><PERSON> poerba", "block.minecraft.andesite": "Andesit", "block.minecraft.andesite_slab": "<PERSON><PERSON><PERSON> and<PERSON>", "block.minecraft.andesite_stairs": "Tangga andesit", "block.minecraft.andesite_wall": "Tembok andesit", "block.minecraft.anvil": "<PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Batang semangka tertempel", "block.minecraft.attached_pumpkin_stem": "Batang laboe tertempel", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Dedaoenan azalea", "block.minecraft.azure_bluet": "<PERSON><PERSON><PERSON> biroe", "block.minecraft.bamboo": "Bamboe", "block.minecraft.bamboo_block": "<PERSON><PERSON><PERSON> bamboe", "block.minecraft.bamboo_button": "<PERSON><PERSON> bamboe", "block.minecraft.bamboo_door": "<PERSON><PERSON><PERSON> bamboe", "block.minecraft.bamboo_fence": "<PERSON><PERSON> bamboe", "block.minecraft.bamboo_fence_gate": "<PERSON><PERSON><PERSON> pagar bamboe", "block.minecraft.bamboo_hanging_sign": "<PERSON>n tanda gantoeng bamboe", "block.minecraft.bamboo_mosaic": "<PERSON><PERSON><PERSON> bamboe", "block.minecraft.bamboo_mosaic_slab": "<PERSON><PERSON><PERSON> an<PERSON> bamboe", "block.minecraft.bamboo_mosaic_stairs": "<PERSON><PERSON> anja<PERSON> bamboe", "block.minecraft.bamboo_planks": "<PERSON><PERSON> bamboe", "block.minecraft.bamboo_pressure_plate": "<PERSON><PERSON><PERSON> pen<PERSON>n bamboe", "block.minecraft.bamboo_sapling": "Reboeng", "block.minecraft.bamboo_sign": "<PERSON><PERSON> tanda bamboe", "block.minecraft.bamboo_slab": "<PERSON><PERSON><PERSON> bamboe", "block.minecraft.bamboo_stairs": "<PERSON><PERSON> bamboe", "block.minecraft.bamboo_trapdoor": "<PERSON><PERSON><PERSON> kolong bamboe", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON>n tanda gantoeng dinding bamboe", "block.minecraft.bamboo_wall_sign": "<PERSON>n tanda gantoeng bamboe", "block.minecraft.banner.base.black": "<PERSON><PERSON> hitam", "block.minecraft.banner.base.blue": "<PERSON><PERSON> bi<PERSON>e", "block.minecraft.banner.base.brown": "<PERSON><PERSON> pirang", "block.minecraft.banner.base.cyan": "<PERSON><PERSON>", "block.minecraft.banner.base.gray": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.base.green": "<PERSON><PERSON>", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON> biroe moeda", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON> kelaboe moeda", "block.minecraft.banner.base.lime": "<PERSON><PERSON> hid<PERSON> p<PERSON>", "block.minecraft.banner.base.magenta": "<PERSON><PERSON> patma", "block.minecraft.banner.base.orange": "<PERSON><PERSON> oran<PERSON>", "block.minecraft.banner.base.pink": "Moeka djambon", "block.minecraft.banner.base.purple": "<PERSON><PERSON>", "block.minecraft.banner.base.red": "<PERSON><PERSON> merah", "block.minecraft.banner.base.white": "<PERSON><PERSON> poet<PERSON>", "block.minecraft.banner.base.yellow": "<PERSON><PERSON> koening", "block.minecraft.banner.border.black": "<PERSON><PERSON> hitam", "block.minecraft.banner.border.blue": "<PERSON><PERSON> bi<PERSON>e", "block.minecraft.banner.border.brown": "<PERSON><PERSON> p<PERSON>ng", "block.minecraft.banner.border.cyan": "<PERSON><PERSON>", "block.minecraft.banner.border.gray": "<PERSON><PERSON>", "block.minecraft.banner.border.green": "<PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON> biroe moeda", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON> k<PERSON> moeda", "block.minecraft.banner.border.lime": "<PERSON><PERSON>", "block.minecraft.banner.border.magenta": "<PERSON><PERSON> pat<PERSON>", "block.minecraft.banner.border.orange": "<PERSON><PERSON>", "block.minecraft.banner.border.pink": "Tepi djambon", "block.minecraft.banner.border.purple": "<PERSON><PERSON>", "block.minecraft.banner.border.red": "<PERSON><PERSON> merah", "block.minecraft.banner.border.white": "<PERSON><PERSON>", "block.minecraft.banner.border.yellow": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.bricks.black": "<PERSON><PERSON>-bata hitam", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON>-bata biroe", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON>-bata pirang", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON>-<PERSON>a <PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON>-bata k<PERSON>", "block.minecraft.banner.bricks.green": "<PERSON><PERSON>-<PERSON>a <PERSON>", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON>-bata biroe moeda", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON>-bata kela<PERSON> moeda", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON>-bata hid<PERSON> p<PERSON>", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON>-bata patma", "block.minecraft.banner.bricks.orange": "Bata-bata oranje", "block.minecraft.banner.bricks.pink": "Bata-bata djambon", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON>-<PERSON>a o<PERSON>", "block.minecraft.banner.bricks.red": "<PERSON>a-bata merah", "block.minecraft.banner.bricks.white": "<PERSON><PERSON>-bata poetih", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON>-bata koening", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON> hit<PERSON>", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON> pirang", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON><PERSON> biroe moeda", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON><PERSON> k<PERSON> moeda", "block.minecraft.banner.circle.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON> pat<PERSON>", "block.minecraft.banner.circle.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.pink": "Boendar djambon", "block.minecraft.banner.circle.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.red": "<PERSON><PERSON><PERSON> merah", "block.minecraft.banner.circle.white": "<PERSON><PERSON><PERSON> poet<PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON><PERSON> koening", "block.minecraft.banner.creeper.black": "<PERSON>pa creeper hitam", "block.minecraft.banner.creeper.blue": "Roepa creeper biroe", "block.minecraft.banner.creeper.brown": "Roepa creeper pirang", "block.minecraft.banner.creeper.cyan": "Roepa creeper nilang<PERSON>eka", "block.minecraft.banner.creeper.gray": "Roepa creeper kelaboe", "block.minecraft.banner.creeper.green": "Roepa creeper hidjau", "block.minecraft.banner.creeper.light_blue": "Roepa creeper biroe moeda", "block.minecraft.banner.creeper.light_gray": "Roepa creeper kelaboe moeda", "block.minecraft.banner.creeper.lime": "Roepa creeper hidjau poepoes", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON> creeper patma", "block.minecraft.banner.creeper.orange": "<PERSON>pa creeper oranje", "block.minecraft.banner.creeper.pink": "Roepa creeper djambon", "block.minecraft.banner.creeper.purple": "Roepa creeper oengoe", "block.minecraft.banner.creeper.red": "Roepa creeper merah", "block.minecraft.banner.creeper.white": "<PERSON><PERSON> creeper poetih", "block.minecraft.banner.creeper.yellow": "Roepa creeper koening", "block.minecraft.banner.cross.black": "Silang hitam", "block.minecraft.banner.cross.blue": "Silang biroe", "block.minecraft.banner.cross.brown": "Silang pirang", "block.minecraft.banner.cross.cyan": "Silang nilangsoeka", "block.minecraft.banner.cross.gray": "Silang kelaboe", "block.minecraft.banner.cross.green": "Silang hidjau", "block.minecraft.banner.cross.light_blue": "Silang biroe moeda", "block.minecraft.banner.cross.light_gray": "Silang kelaboe moeda", "block.minecraft.banner.cross.lime": "Silang hidjau p<PERSON>", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON> patma", "block.minecraft.banner.cross.orange": "Silang oranje", "block.minecraft.banner.cross.pink": "Silang djambon", "block.minecraft.banner.cross.purple": "Silang oengoe", "block.minecraft.banner.cross.red": "Silang merah", "block.minecraft.banner.cross.white": "Silang poetih", "block.minecraft.banner.cross.yellow": "Silang koening", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON> hitam", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON> bi<PERSON>e", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON> pirang", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON> men<PERSON><PERSON> biroe moeda", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON> men<PERSON> kela<PERSON> moeda", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON> hid<PERSON> p<PERSON>", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON> patma", "block.minecraft.banner.curly_border.orange": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON> djambon", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON> o<PERSON>", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON> men<PERSON> merah", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON> poetih", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON> men<PERSON> koening", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON><PERSON> kiri atas hitam", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON><PERSON> kiri atas biroe", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON> kiri atas pirang", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON>eh kiri atas nilangsoeka", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON> kiri atas kela<PERSON>", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON> kiri atas hidjau", "block.minecraft.banner.diagonal_left.light_blue": "<PERSON><PERSON><PERSON> kiri atas biroe moeda", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON><PERSON> kiri atas kelaboe moeda", "block.minecraft.banner.diagonal_left.lime": "<PERSON><PERSON><PERSON> kiri atas hidjau poepoes", "block.minecraft.banner.diagonal_left.magenta": "<PERSON><PERSON><PERSON> kiri atas patma", "block.minecraft.banner.diagonal_left.orange": "<PERSON><PERSON><PERSON> kiri atas oranje", "block.minecraft.banner.diagonal_left.pink": "Paroeh kiri atas djambon", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON>eh kiri atas oengoe", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON><PERSON> kiri atas merah", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> kiri atas poetih", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON> kiri atas koening", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON><PERSON> kanan atas hitam", "block.minecraft.banner.diagonal_right.blue": "<PERSON><PERSON><PERSON> kanan atas biroe", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON> kanan atas pirang", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON> kanan atas nilang<PERSON>eka", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON> kanan atas kela<PERSON>", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON> kanan atas hidjau", "block.minecraft.banner.diagonal_right.light_blue": "<PERSON><PERSON><PERSON> kanan atas biroe moeda", "block.minecraft.banner.diagonal_right.light_gray": "<PERSON><PERSON><PERSON> kanan atas kelaboe moeda", "block.minecraft.banner.diagonal_right.lime": "<PERSON><PERSON><PERSON> kanan atas hidjau poe<PERSON>es", "block.minecraft.banner.diagonal_right.magenta": "<PERSON><PERSON><PERSON> kanan atas patma", "block.minecraft.banner.diagonal_right.orange": "<PERSON><PERSON><PERSON> kanan atas oranje", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON> kanan atas djambon", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON><PERSON> kanan atas oengoe", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON><PERSON> kanan atas merah", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON> kanan atas poetih", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON> kanan atas koening", "block.minecraft.banner.diagonal_up_left.black": "<PERSON><PERSON><PERSON> kiri bawah hitam", "block.minecraft.banner.diagonal_up_left.blue": "<PERSON><PERSON><PERSON> kiri bawah biroe", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON><PERSON> kiri bawah pirang", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON><PERSON> kiri bawah ni<PERSON>", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON><PERSON> kiri bawah kela<PERSON>", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON> kiri bawah hidjau", "block.minecraft.banner.diagonal_up_left.light_blue": "<PERSON><PERSON><PERSON> kiri bawah biroe moeda", "block.minecraft.banner.diagonal_up_left.light_gray": "<PERSON><PERSON><PERSON> kiri bawah kelaboe moeda", "block.minecraft.banner.diagonal_up_left.lime": "<PERSON><PERSON><PERSON> kiri bawah hidjau poe<PERSON>es", "block.minecraft.banner.diagonal_up_left.magenta": "<PERSON><PERSON><PERSON> kiri bawah patma", "block.minecraft.banner.diagonal_up_left.orange": "<PERSON><PERSON><PERSON> kiri bawah oranje", "block.minecraft.banner.diagonal_up_left.pink": "Paroeh kiri bawah djambon", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON><PERSON><PERSON> kiri bawah o<PERSON>oe", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON><PERSON> kiri bawah merah", "block.minecraft.banner.diagonal_up_left.white": "<PERSON><PERSON><PERSON> kiri bawah poetih", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON><PERSON> kiri bawah koening", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON><PERSON> kanan bawah hitam", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON><PERSON> kanan bawah biroe", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON><PERSON> kanan bawah pirang", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON><PERSON> kanan bawah ni<PERSON>", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON><PERSON> kanan bawah kela<PERSON>", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON> kanan bawah hidjau", "block.minecraft.banner.diagonal_up_right.light_blue": "<PERSON><PERSON><PERSON> kanan bawah biroe moeda", "block.minecraft.banner.diagonal_up_right.light_gray": "<PERSON><PERSON><PERSON> kanan bawah kela<PERSON> moeda", "block.minecraft.banner.diagonal_up_right.lime": "<PERSON><PERSON><PERSON> kanan bawah hidjau p<PERSON>", "block.minecraft.banner.diagonal_up_right.magenta": "<PERSON><PERSON><PERSON> kanan bawah patma", "block.minecraft.banner.diagonal_up_right.orange": "<PERSON><PERSON><PERSON> kanan bawah oranje", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON><PERSON> kanan bawah djambon", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON><PERSON> kanan bawah o<PERSON>oe", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON><PERSON> kanan bawah merah", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON> kanan bawah poetih", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON> kanan bawah koening", "block.minecraft.banner.flow.black": "<PERSON><PERSON> hitam", "block.minecraft.banner.flow.blue": "<PERSON><PERSON> biroe", "block.minecraft.banner.flow.brown": "<PERSON><PERSON> pirang", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON>", "block.minecraft.banner.flow.gray": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.flow.green": "<PERSON><PERSON>", "block.minecraft.banner.flow.light_blue": "<PERSON>lin biroe moeda", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON> kela<PERSON> moeda", "block.minecraft.banner.flow.lime": "<PERSON><PERSON> p<PERSON>", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON> patma", "block.minecraft.banner.flow.orange": "<PERSON><PERSON>", "block.minecraft.banner.flow.pink": "Pilin djambon", "block.minecraft.banner.flow.purple": "<PERSON><PERSON>", "block.minecraft.banner.flow.red": "<PERSON><PERSON> merah", "block.minecraft.banner.flow.white": "<PERSON><PERSON> poetih", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON> koening", "block.minecraft.banner.flower.black": "<PERSON><PERSON> boenga hitam", "block.minecraft.banner.flower.blue": "<PERSON><PERSON> boenga biroe", "block.minecraft.banner.flower.brown": "<PERSON><PERSON> boenga pirang", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON> boenga ni<PERSON>", "block.minecraft.banner.flower.gray": "<PERSON><PERSON> boenga kela<PERSON>", "block.minecraft.banner.flower.green": "<PERSON><PERSON> b<PERSON>", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON> boenga biroe moeda", "block.minecraft.banner.flower.light_gray": "<PERSON><PERSON> boenga kelaboe moeda", "block.minecraft.banner.flower.lime": "<PERSON><PERSON> boenga hid<PERSON>u poepoes", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON> boenga patma", "block.minecraft.banner.flower.orange": "<PERSON><PERSON> boe<PERSON> or<PERSON>", "block.minecraft.banner.flower.pink": "<PERSON><PERSON> boenga djambon", "block.minecraft.banner.flower.purple": "<PERSON><PERSON> boenga o<PERSON>", "block.minecraft.banner.flower.red": "<PERSON><PERSON> boenga merah", "block.minecraft.banner.flower.white": "<PERSON><PERSON> boe<PERSON> poetih", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON> boenga koening", "block.minecraft.banner.globe.black": "Globe hitam", "block.minecraft.banner.globe.blue": "Globe biroe", "block.minecraft.banner.globe.brown": "Globe pirang", "block.minecraft.banner.globe.cyan": "Globe nilangsoeka", "block.minecraft.banner.globe.gray": "Globe kelaboe", "block.minecraft.banner.globe.green": "Globe hidjau", "block.minecraft.banner.globe.light_blue": "Globe biroe moeda", "block.minecraft.banner.globe.light_gray": "Globe kelaboe moeda", "block.minecraft.banner.globe.lime": "Globe hidjau poepoes", "block.minecraft.banner.globe.magenta": "Globe patma", "block.minecraft.banner.globe.orange": "Globe oranje", "block.minecraft.banner.globe.pink": "Globe djambon", "block.minecraft.banner.globe.purple": "Globe oengoe", "block.minecraft.banner.globe.red": "Globe merah", "block.minecraft.banner.globe.white": "Globe poetih", "block.minecraft.banner.globe.yellow": "Globe koening", "block.minecraft.banner.gradient.black": "<PERSON><PERSON><PERSON> hitam", "block.minecraft.banner.gradient.blue": "Gradasi biroe", "block.minecraft.banner.gradient.brown": "G<PERSON>si pirang", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_blue": "Gradasi biroe moeda", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON><PERSON> kela<PERSON> moeda", "block.minecraft.banner.gradient.lime": "<PERSON><PERSON><PERSON> hid<PERSON>u p<PERSON>", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON> patma", "block.minecraft.banner.gradient.orange": "<PERSON><PERSON><PERSON> oranje", "block.minecraft.banner.gradient.pink": "Gradasi djambon", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON> merah", "block.minecraft.banner.gradient.white": "Gradasi poetih", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON> koening", "block.minecraft.banner.gradient_up.black": "<PERSON><PERSON><PERSON> dasar hitam", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON><PERSON> dasar biroe", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON><PERSON> dasar pirang", "block.minecraft.banner.gradient_up.cyan": "<PERSON><PERSON><PERSON> dasar nilang<PERSON>", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON> dasar kelaboe", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON> dasar hidjau", "block.minecraft.banner.gradient_up.light_blue": "<PERSON><PERSON><PERSON> dasar biroe moeda", "block.minecraft.banner.gradient_up.light_gray": "<PERSON><PERSON><PERSON> dasar kelaboe moeda", "block.minecraft.banner.gradient_up.lime": "<PERSON><PERSON><PERSON> dasar hidjau poe<PERSON>es", "block.minecraft.banner.gradient_up.magenta": "<PERSON><PERSON><PERSON> dasar patma", "block.minecraft.banner.gradient_up.orange": "<PERSON><PERSON><PERSON> dasar oranje", "block.minecraft.banner.gradient_up.pink": "Gradasi dasar djambon", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON><PERSON> dasar o<PERSON>oe", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON><PERSON> dasar merah", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON><PERSON> dasar poetih", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON><PERSON> dasar koening", "block.minecraft.banner.guster.black": "<PERSON><PERSON><PERSON> hitam", "block.minecraft.banner.guster.blue": "<PERSON><PERSON><PERSON> bi<PERSON>e", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON> pirang", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON> biroe moeda", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON> k<PERSON> moeda", "block.minecraft.banner.guster.lime": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON> patma", "block.minecraft.banner.guster.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.pink": "Prahara djambon", "block.minecraft.banner.guster.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON> merah", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON> poetih", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON> koe<PERSON>", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON><PERSON> atas hitam", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON> atas biroe", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON> atas pirang", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON> atas ni<PERSON>", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON><PERSON> atas kela<PERSON>", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON> at<PERSON> hid<PERSON>", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON><PERSON> atas biroe moeda", "block.minecraft.banner.half_horizontal.light_gray": "<PERSON><PERSON><PERSON> atas kelaboe moeda", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON><PERSON> atas hidjau p<PERSON>", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON><PERSON> at<PERSON> patma", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON> at<PERSON> oranje", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON>eh atas djambon", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON><PERSON> atas o<PERSON>oe", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON><PERSON> atas merah", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON> at<PERSON> poetih", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON><PERSON> atas koening", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON><PERSON><PERSON> bawah hitam", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON><PERSON> bawah biroe", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON><PERSON> bawah pirang", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON><PERSON> bawah <PERSON>", "block.minecraft.banner.half_horizontal_bottom.gray": "<PERSON><PERSON><PERSON> bawah kela<PERSON>", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON><PERSON> bawah <PERSON>", "block.minecraft.banner.half_horizontal_bottom.light_blue": "<PERSON><PERSON><PERSON> bawah biroe moeda", "block.minecraft.banner.half_horizontal_bottom.light_gray": "<PERSON><PERSON><PERSON> bawah kela<PERSON> moeda", "block.minecraft.banner.half_horizontal_bottom.lime": "<PERSON><PERSON><PERSON> bawah hid<PERSON> p<PERSON>", "block.minecraft.banner.half_horizontal_bottom.magenta": "<PERSON><PERSON><PERSON> bawah patma", "block.minecraft.banner.half_horizontal_bottom.orange": "<PERSON><PERSON><PERSON> bawah oranje", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON><PERSON><PERSON> bawah djambon", "block.minecraft.banner.half_horizontal_bottom.purple": "<PERSON><PERSON><PERSON> bawah o<PERSON>", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON><PERSON> bawah merah", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON><PERSON> bawah poetih", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON><PERSON> bawah koening", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON><PERSON> kiri hitam", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON><PERSON> kiri biroe", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON> kiri pirang", "block.minecraft.banner.half_vertical.cyan": "Pa<PERSON>eh kiri nilang<PERSON>", "block.minecraft.banner.half_vertical.gray": "<PERSON><PERSON><PERSON> kiri kela<PERSON>", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON><PERSON> kiri <PERSON>u", "block.minecraft.banner.half_vertical.light_blue": "<PERSON><PERSON><PERSON> kiri biroe moeda", "block.minecraft.banner.half_vertical.light_gray": "<PERSON><PERSON><PERSON> kiri kelaboe moeda", "block.minecraft.banner.half_vertical.lime": "<PERSON><PERSON><PERSON> kiri hid<PERSON>u p<PERSON>", "block.minecraft.banner.half_vertical.magenta": "<PERSON><PERSON><PERSON> kiri patma", "block.minecraft.banner.half_vertical.orange": "<PERSON><PERSON><PERSON> kiri oranje", "block.minecraft.banner.half_vertical.pink": "Paroeh kiri djambon", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON><PERSON> kiri o<PERSON>oe", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON><PERSON> kiri merah", "block.minecraft.banner.half_vertical.white": "<PERSON><PERSON><PERSON> kiri <PERSON>ih", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON>eh kiri koening", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON><PERSON> kanan hitam", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON><PERSON> kanan biroe", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON><PERSON> kanan pirang", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON><PERSON> kanan ni<PERSON>", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON><PERSON> kanan kela<PERSON>", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON><PERSON> kanan <PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON><PERSON> kanan biroe moeda", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON><PERSON><PERSON> kanan kela<PERSON> moeda", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON><PERSON> kanan hid<PERSON>u p<PERSON>", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON><PERSON><PERSON> kanan <PERSON>", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON><PERSON> kanan oranje", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON><PERSON> kanan djambon", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON><PERSON> kanan o<PERSON>", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON><PERSON> kanan merah", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON><PERSON> kanan <PERSON>ih", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON><PERSON> kanan koening", "block.minecraft.banner.mojang.black": "Lambang hitam", "block.minecraft.banner.mojang.blue": "Lambang biroe", "block.minecraft.banner.mojang.brown": "Lambang pirang", "block.minecraft.banner.mojang.cyan": "Lambang nilangsoeka", "block.minecraft.banner.mojang.gray": "Lambang kela<PERSON>", "block.minecraft.banner.mojang.green": "<PERSON><PERSON> hid<PERSON>u", "block.minecraft.banner.mojang.light_blue": "Lambang biroe moeda", "block.minecraft.banner.mojang.light_gray": "Lambang kelaboe moeda", "block.minecraft.banner.mojang.lime": "Lambang hidjau poepoes", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON> patma", "block.minecraft.banner.mojang.orange": "Lambang oranje", "block.minecraft.banner.mojang.pink": "Lambang djambon", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON>", "block.minecraft.banner.mojang.red": "Lambang merah", "block.minecraft.banner.mojang.white": "<PERSON>ang poetih", "block.minecraft.banner.mojang.yellow": "Lambang koening", "block.minecraft.banner.piglin.black": "<PERSON><PERSON> hitam", "block.minecraft.banner.piglin.blue": "Montjong biroe", "block.minecraft.banner.piglin.brown": "Montjong pirang", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON> nilangsoeka", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.piglin.green": "<PERSON><PERSON>", "block.minecraft.banner.piglin.light_blue": "Montjong biroe moeda", "block.minecraft.banner.piglin.light_gray": "<PERSON><PERSON> kelaboe moeda", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON> p<PERSON>", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON>", "block.minecraft.banner.piglin.orange": "Montjong oranje", "block.minecraft.banner.piglin.pink": "Montjong djambon", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON>", "block.minecraft.banner.piglin.red": "<PERSON><PERSON> merah", "block.minecraft.banner.piglin.white": "<PERSON><PERSON>", "block.minecraft.banner.piglin.yellow": "Montjong koening", "block.minecraft.banner.rhombus.black": "Belah-ketoepat hitam", "block.minecraft.banner.rhombus.blue": "Belah-ketoepat biroe", "block.minecraft.banner.rhombus.brown": "Belah-ketoepat pirang", "block.minecraft.banner.rhombus.cyan": "Belah-ketoepat nilangsoeka", "block.minecraft.banner.rhombus.gray": "Belah-ketoepat kelaboe", "block.minecraft.banner.rhombus.green": "Belah-ketoepat hidjau", "block.minecraft.banner.rhombus.light_blue": "Belah-ketoepat biroe moeda", "block.minecraft.banner.rhombus.light_gray": "Belah-ketoepat kelaboe moeda", "block.minecraft.banner.rhombus.lime": "Belah-ketoepat hidjau p<PERSON>es", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON>k<PERSON><PERSON><PERSON> patma", "block.minecraft.banner.rhombus.orange": "Belah-ketoepat oranje", "block.minecraft.banner.rhombus.pink": "Belah-ketoepat djambon", "block.minecraft.banner.rhombus.purple": "Belah-ketoepat oengoe", "block.minecraft.banner.rhombus.red": "Belah-ketoepat merah", "block.minecraft.banner.rhombus.white": "Belah-ketoepat poetih", "block.minecraft.banner.rhombus.yellow": "Belah-ketoepat koening", "block.minecraft.banner.skull.black": "<PERSON><PERSON> teng<PERSON>ak hitam", "block.minecraft.banner.skull.blue": "<PERSON><PERSON> teng<PERSON>ak biroe", "block.minecraft.banner.skull.brown": "<PERSON><PERSON> teng<PERSON>ak pirang", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON> teng<PERSON>ak ni<PERSON>", "block.minecraft.banner.skull.gray": "<PERSON><PERSON> teng<PERSON> kela<PERSON>", "block.minecraft.banner.skull.green": "<PERSON><PERSON> teng<PERSON> hidjau", "block.minecraft.banner.skull.light_blue": "<PERSON><PERSON> teng<PERSON>ak biroe moeda", "block.minecraft.banner.skull.light_gray": "<PERSON><PERSON> teng<PERSON>ak kelaboe moeda", "block.minecraft.banner.skull.lime": "<PERSON><PERSON> teng<PERSON>ak hidjau p<PERSON>", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON> teng<PERSON> patma", "block.minecraft.banner.skull.orange": "<PERSON><PERSON> teng<PERSON>ak oranje", "block.minecraft.banner.skull.pink": "Roepa tengkorak djambon", "block.minecraft.banner.skull.purple": "<PERSON><PERSON> teng<PERSON> o<PERSON>oe", "block.minecraft.banner.skull.red": "<PERSON><PERSON> teng<PERSON>ak merah", "block.minecraft.banner.skull.white": "<PERSON><PERSON> teng<PERSON>ak poetih", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON> tengkorak koening", "block.minecraft.banner.small_stripes.black": "Belang-belang hitam", "block.minecraft.banner.small_stripes.blue": "Belang-belang biroe", "block.minecraft.banner.small_stripes.brown": "Belang-belang pirang", "block.minecraft.banner.small_stripes.cyan": "Belang-belang nilangsoeka", "block.minecraft.banner.small_stripes.gray": "Belang-belang kelaboe", "block.minecraft.banner.small_stripes.green": "Belang-belang hidjau", "block.minecraft.banner.small_stripes.light_blue": "Belang-belang biroe moeda", "block.minecraft.banner.small_stripes.light_gray": "Belang-belang kelaboe moeda", "block.minecraft.banner.small_stripes.lime": "Belang-belang hidjau poepoes", "block.minecraft.banner.small_stripes.magenta": "Bel<PERSON>-<PERSON><PERSON> patma", "block.minecraft.banner.small_stripes.orange": "Belang-belang oranje", "block.minecraft.banner.small_stripes.pink": "Belang-belang djambon", "block.minecraft.banner.small_stripes.purple": "Belang-belang o<PERSON>oe", "block.minecraft.banner.small_stripes.red": "Belang-belang merah", "block.minecraft.banner.small_stripes.white": "Belang-belang poetih", "block.minecraft.banner.small_stripes.yellow": "Belang-belang koening", "block.minecraft.banner.square_bottom_left.black": "Podjok kiri bawah hitam", "block.minecraft.banner.square_bottom_left.blue": "Podjok kiri bawah biroe", "block.minecraft.banner.square_bottom_left.brown": "Podjok kiri bawah pirang", "block.minecraft.banner.square_bottom_left.cyan": "Podjok kiri bawah ni<PERSON>", "block.minecraft.banner.square_bottom_left.gray": "Podjok kiri bawah kelaboe", "block.minecraft.banner.square_bottom_left.green": "Podjok kiri bawah hidjau", "block.minecraft.banner.square_bottom_left.light_blue": "Podjok kiri bawah biroe moeda", "block.minecraft.banner.square_bottom_left.light_gray": "Podjok kiri bawah kelaboe moeda", "block.minecraft.banner.square_bottom_left.lime": "Podjok kiri bawah hidjau poepoes", "block.minecraft.banner.square_bottom_left.magenta": "Podjok kiri bawah patma", "block.minecraft.banner.square_bottom_left.orange": "Podjok kiri bawah oranje", "block.minecraft.banner.square_bottom_left.pink": "Podjok kiri bawah djambon", "block.minecraft.banner.square_bottom_left.purple": "Podjok kiri bawah oengoe", "block.minecraft.banner.square_bottom_left.red": "Podjok kiri bawah merah", "block.minecraft.banner.square_bottom_left.white": "Podjok kiri bawah poetih", "block.minecraft.banner.square_bottom_left.yellow": "Podjok kiri bawah koening", "block.minecraft.banner.square_bottom_right.black": "<PERSON><PERSON><PERSON><PERSON> kanan bawah hitam", "block.minecraft.banner.square_bottom_right.blue": "<PERSON><PERSON><PERSON><PERSON> kanan bawah biroe", "block.minecraft.banner.square_bottom_right.brown": "<PERSON><PERSON><PERSON><PERSON> kanan bawah pirang", "block.minecraft.banner.square_bottom_right.cyan": "<PERSON><PERSON><PERSON><PERSON> kanan bawah ni<PERSON>", "block.minecraft.banner.square_bottom_right.gray": "<PERSON><PERSON><PERSON><PERSON> kanan bawah kela<PERSON>", "block.minecraft.banner.square_bottom_right.green": "<PERSON><PERSON><PERSON><PERSON> kanan bawah hidjau", "block.minecraft.banner.square_bottom_right.light_blue": "<PERSON><PERSON><PERSON><PERSON> kanan bawah biroe moeda", "block.minecraft.banner.square_bottom_right.light_gray": "<PERSON><PERSON><PERSON><PERSON> kanan bawah kelaboe moeda", "block.minecraft.banner.square_bottom_right.lime": "<PERSON><PERSON><PERSON><PERSON> kanan bawah hidjau p<PERSON>", "block.minecraft.banner.square_bottom_right.magenta": "<PERSON><PERSON><PERSON><PERSON> kanan bawah patma", "block.minecraft.banner.square_bottom_right.orange": "<PERSON><PERSON><PERSON><PERSON> kanan bawah oranje", "block.minecraft.banner.square_bottom_right.pink": "Podjok kanan bawah djambon", "block.minecraft.banner.square_bottom_right.purple": "<PERSON><PERSON><PERSON><PERSON> kanan bawah o<PERSON>oe", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON><PERSON><PERSON> kanan bawah merah", "block.minecraft.banner.square_bottom_right.white": "<PERSON><PERSON><PERSON><PERSON> kanan bawah poetih", "block.minecraft.banner.square_bottom_right.yellow": "<PERSON><PERSON><PERSON><PERSON> kanan bawah koening", "block.minecraft.banner.square_top_left.black": "Podjok kiri atas hitam", "block.minecraft.banner.square_top_left.blue": "Podjok kiri atas biroe", "block.minecraft.banner.square_top_left.brown": "Podjok kiri atas pirang", "block.minecraft.banner.square_top_left.cyan": "Podjok kiri atas nilangsoeka", "block.minecraft.banner.square_top_left.gray": "Podjok kiri atas kelaboe", "block.minecraft.banner.square_top_left.green": "Podjok kiri atas hidjau", "block.minecraft.banner.square_top_left.light_blue": "Podjok kiri atas biroe moeda", "block.minecraft.banner.square_top_left.light_gray": "Podjok kiri atas kelaboe moeda", "block.minecraft.banner.square_top_left.lime": "Podjok kiri atas hidjau poepoes", "block.minecraft.banner.square_top_left.magenta": "Podjok kiri atas patma", "block.minecraft.banner.square_top_left.orange": "Podjok kiri atas oranje", "block.minecraft.banner.square_top_left.pink": "Podjok kiri atas djambon", "block.minecraft.banner.square_top_left.purple": "Podjok kiri atas oengoe", "block.minecraft.banner.square_top_left.red": "Podjok kiri atas merah", "block.minecraft.banner.square_top_left.white": "Podjok kiri atas poetih", "block.minecraft.banner.square_top_left.yellow": "Podjok kiri atas koening", "block.minecraft.banner.square_top_right.black": "<PERSON><PERSON><PERSON><PERSON> kanan atas hitam", "block.minecraft.banner.square_top_right.blue": "Podjok kanan atas biroe", "block.minecraft.banner.square_top_right.brown": "Podjok kanan atas pirang", "block.minecraft.banner.square_top_right.cyan": "Podjok kanan atas nilangsoeka", "block.minecraft.banner.square_top_right.gray": "<PERSON><PERSON><PERSON><PERSON> kanan atas kelaboe", "block.minecraft.banner.square_top_right.green": "<PERSON>d<PERSON><PERSON> kanan atas hidjau", "block.minecraft.banner.square_top_right.light_blue": "<PERSON>d<PERSON><PERSON> kanan atas biroe moeda", "block.minecraft.banner.square_top_right.light_gray": "<PERSON><PERSON><PERSON><PERSON> kanan atas kelaboe moeda", "block.minecraft.banner.square_top_right.lime": "Podjok kanan atas hidjau poepoes", "block.minecraft.banner.square_top_right.magenta": "<PERSON><PERSON><PERSON><PERSON> kanan atas patma", "block.minecraft.banner.square_top_right.orange": "Podjok kanan atas oranje", "block.minecraft.banner.square_top_right.pink": "Podjok kanan atas djambon", "block.minecraft.banner.square_top_right.purple": "Podjok kanan atas oengoe", "block.minecraft.banner.square_top_right.red": "<PERSON><PERSON><PERSON><PERSON> kanan atas merah", "block.minecraft.banner.square_top_right.white": "<PERSON><PERSON><PERSON><PERSON> kanan atas poetih", "block.minecraft.banner.square_top_right.yellow": "Podjok kanan atas koening", "block.minecraft.banner.straight_cross.black": "Palang hitam", "block.minecraft.banner.straight_cross.blue": "Palang biroe", "block.minecraft.banner.straight_cross.brown": "Palang pirang", "block.minecraft.banner.straight_cross.cyan": "Palang nilangsoeka", "block.minecraft.banner.straight_cross.gray": "Palang kelaboe", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON> hidjau", "block.minecraft.banner.straight_cross.light_blue": "Palang biroe moeda", "block.minecraft.banner.straight_cross.light_gray": "Palang kelaboe moeda", "block.minecraft.banner.straight_cross.lime": "Palang hid<PERSON>u p<PERSON>", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON> patma", "block.minecraft.banner.straight_cross.orange": "Palang oranje", "block.minecraft.banner.straight_cross.pink": "Palang djambon", "block.minecraft.banner.straight_cross.purple": "Palang oengoe", "block.minecraft.banner.straight_cross.red": "Palang merah", "block.minecraft.banner.straight_cross.white": "Palang poetih", "block.minecraft.banner.straight_cross.yellow": "Palang koening", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON> hitam", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON> bi<PERSON>e", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON> pirang", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "<PERSON><PERSON> biroe moeda", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON><PERSON> k<PERSON> moeda", "block.minecraft.banner.stripe_bottom.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON> pat<PERSON>", "block.minecraft.banner.stripe_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.pink": "Dasar djambon", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON> merah", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON> poet<PERSON>", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.stripe_center.black": "Tonggak hitam", "block.minecraft.banner.stripe_center.blue": "Tonggak biroe", "block.minecraft.banner.stripe_center.brown": "Tonggak pirang", "block.minecraft.banner.stripe_center.cyan": "Tonggak nilangsoeka", "block.minecraft.banner.stripe_center.gray": "Tonggak kelaboe", "block.minecraft.banner.stripe_center.green": "Tonggak hidjau", "block.minecraft.banner.stripe_center.light_blue": "Tonggak biroe moeda", "block.minecraft.banner.stripe_center.light_gray": "Tonggak kelaboe moeda", "block.minecraft.banner.stripe_center.lime": "Tonggak hidjau p<PERSON>", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON> patma", "block.minecraft.banner.stripe_center.orange": "Tonggak oranje", "block.minecraft.banner.stripe_center.pink": "Tonggak djambon", "block.minecraft.banner.stripe_center.purple": "Tonggak oengoe", "block.minecraft.banner.stripe_center.red": "Tonggak merah", "block.minecraft.banner.stripe_center.white": "Tonggak poetih", "block.minecraft.banner.stripe_center.yellow": "Tonggak koening", "block.minecraft.banner.stripe_downleft.black": "<PERSON><PERSON> kanan hitam", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON> kanan biroe", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON> kanan pirang", "block.minecraft.banner.stripe_downleft.cyan": "Serong kanan nilang<PERSON>", "block.minecraft.banner.stripe_downleft.gray": "<PERSON><PERSON> kanan kela<PERSON>", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON> kanan <PERSON>u", "block.minecraft.banner.stripe_downleft.light_blue": "<PERSON><PERSON> kanan biroe moeda", "block.minecraft.banner.stripe_downleft.light_gray": "<PERSON><PERSON> kanan kelaboe moeda", "block.minecraft.banner.stripe_downleft.lime": "<PERSON><PERSON> kanan hidjau poe<PERSON>es", "block.minecraft.banner.stripe_downleft.magenta": "<PERSON><PERSON> kanan patma", "block.minecraft.banner.stripe_downleft.orange": "<PERSON><PERSON> kanan oranje", "block.minecraft.banner.stripe_downleft.pink": "Serong kanan djambon", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON> kanan o<PERSON>oe", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON> kanan merah", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON> kanan poetih", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON> kanan koening", "block.minecraft.banner.stripe_downright.black": "Serong kiri hitam", "block.minecraft.banner.stripe_downright.blue": "Serong kiri biroe", "block.minecraft.banner.stripe_downright.brown": "Serong kiri pirang", "block.minecraft.banner.stripe_downright.cyan": "Serong kiri nilangsoeka", "block.minecraft.banner.stripe_downright.gray": "Serong kiri kelaboe", "block.minecraft.banner.stripe_downright.green": "Serong kiri hidjau", "block.minecraft.banner.stripe_downright.light_blue": "Serong kiri biroe moeda", "block.minecraft.banner.stripe_downright.light_gray": "Serong kiri kelaboe moeda", "block.minecraft.banner.stripe_downright.lime": "Serong kiri hidjau poepoes", "block.minecraft.banner.stripe_downright.magenta": "Serong kiri patma", "block.minecraft.banner.stripe_downright.orange": "Serong kiri oranje", "block.minecraft.banner.stripe_downright.pink": "Serong kiri djambon", "block.minecraft.banner.stripe_downright.purple": "Serong kiri oengoe", "block.minecraft.banner.stripe_downright.red": "Serong kiri merah", "block.minecraft.banner.stripe_downright.white": "Serong kiri poetih", "block.minecraft.banner.stripe_downright.yellow": "Serong kiri koening", "block.minecraft.banner.stripe_left.black": "Tonggak kiri hitam", "block.minecraft.banner.stripe_left.blue": "Tonggak kiri biroe", "block.minecraft.banner.stripe_left.brown": "Tonggak kiri pirang", "block.minecraft.banner.stripe_left.cyan": "Tonggak kiri nilangsoeka", "block.minecraft.banner.stripe_left.gray": "Tonggak kiri kelaboe", "block.minecraft.banner.stripe_left.green": "Tonggak kiri hidjau", "block.minecraft.banner.stripe_left.light_blue": "Tonggak kiri biroe moeda", "block.minecraft.banner.stripe_left.light_gray": "Tonggak kiri kelaboe moeda", "block.minecraft.banner.stripe_left.lime": "Tonggak kiri hidjau poepoes", "block.minecraft.banner.stripe_left.magenta": "Tonggak kiri patma", "block.minecraft.banner.stripe_left.orange": "Tonggak kiri oranje", "block.minecraft.banner.stripe_left.pink": "Tonggak kiri djambon", "block.minecraft.banner.stripe_left.purple": "Tonggak kiri oengoe", "block.minecraft.banner.stripe_left.red": "Tonggak kiri merah", "block.minecraft.banner.stripe_left.white": "Tonggak kiri poetih", "block.minecraft.banner.stripe_left.yellow": "Tonggak kiri koening", "block.minecraft.banner.stripe_middle.black": "<PERSON>lak datar hitam", "block.minecraft.banner.stripe_middle.blue": "Balak datar biroe", "block.minecraft.banner.stripe_middle.brown": "Balak datar pirang", "block.minecraft.banner.stripe_middle.cyan": "Balak datar nilangsoeka", "block.minecraft.banner.stripe_middle.gray": "Balak datar kelaboe", "block.minecraft.banner.stripe_middle.green": "<PERSON>lak datar hidjau", "block.minecraft.banner.stripe_middle.light_blue": "Balak datar biroe moeda", "block.minecraft.banner.stripe_middle.light_gray": "Balak datar kela<PERSON> moeda", "block.minecraft.banner.stripe_middle.lime": "Balak datar hid<PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON> datar patma", "block.minecraft.banner.stripe_middle.orange": "Balak datar oranje", "block.minecraft.banner.stripe_middle.pink": "Balak datar djambon", "block.minecraft.banner.stripe_middle.purple": "Balak datar oengoe", "block.minecraft.banner.stripe_middle.red": "<PERSON>lak datar merah", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON> datar poetih", "block.minecraft.banner.stripe_middle.yellow": "Balak datar koening", "block.minecraft.banner.stripe_right.black": "Tonggak kanan hitam", "block.minecraft.banner.stripe_right.blue": "Tonggak kanan biroe", "block.minecraft.banner.stripe_right.brown": "Tonggak kanan pirang", "block.minecraft.banner.stripe_right.cyan": "Tonggak kanan nilangsoeka", "block.minecraft.banner.stripe_right.gray": "Tonggak kanan kelaboe", "block.minecraft.banner.stripe_right.green": "Tonggak kanan hidjau", "block.minecraft.banner.stripe_right.light_blue": "Tonggak kanan biroe moeda", "block.minecraft.banner.stripe_right.light_gray": "Tonggak kanan kelaboe moeda\n", "block.minecraft.banner.stripe_right.lime": "Tonggak kanan hidjau poe<PERSON>es", "block.minecraft.banner.stripe_right.magenta": "Tonggak kanan patma", "block.minecraft.banner.stripe_right.orange": "Tonggak kanan oranje", "block.minecraft.banner.stripe_right.pink": "Tonggak kanan djambon", "block.minecraft.banner.stripe_right.purple": "Tonggak kanan oengoe", "block.minecraft.banner.stripe_right.red": "Tonggak kanan merah", "block.minecraft.banner.stripe_right.white": "Tonggak kanan poetih", "block.minecraft.banner.stripe_right.yellow": "Tonggak kanan kuning", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON><PERSON> hitam", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON><PERSON> bi<PERSON>e", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON><PERSON> p<PERSON>ng", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_blue": "<PERSON><PERSON><PERSON> biroe moeda", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON><PERSON> k<PERSON> moeda", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON> pat<PERSON>", "block.minecraft.banner.stripe_top.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.pink": "Hoeloe djambon", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON> merah", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON> poet<PERSON>", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON><PERSON> koe<PERSON>", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON> hitam", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON> biroe", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON> pirang", "block.minecraft.banner.triangle_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "<PERSON>per biroe moeda", "block.minecraft.banner.triangle_bottom.light_gray": "<PERSON><PERSON> kela<PERSON> moeda", "block.minecraft.banner.triangle_bottom.lime": "<PERSON><PERSON> p<PERSON>", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON> patma", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.pink": "Keper djambon", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON> merah", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON> poet<PERSON>", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON> koening", "block.minecraft.banner.triangle_top.black": "<PERSON><PERSON> so<PERSON> hitam", "block.minecraft.banner.triangle_top.blue": "<PERSON><PERSON> so<PERSON> bi<PERSON>e", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON> so<PERSON>sang pirang", "block.minecraft.banner.triangle_top.cyan": "<PERSON><PERSON> so<PERSON> ni<PERSON>", "block.minecraft.banner.triangle_top.gray": "<PERSON><PERSON> so<PERSON> kela<PERSON>", "block.minecraft.banner.triangle_top.green": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.light_blue": "<PERSON><PERSON> soengsang biroe moeda", "block.minecraft.banner.triangle_top.light_gray": "<PERSON><PERSON> so<PERSON>sang kelaboe moeda", "block.minecraft.banner.triangle_top.lime": "<PERSON><PERSON> so<PERSON> hidjau p<PERSON>", "block.minecraft.banner.triangle_top.magenta": "<PERSON><PERSON> so<PERSON> patma", "block.minecraft.banner.triangle_top.orange": "<PERSON><PERSON> or<PERSON>", "block.minecraft.banner.triangle_top.pink": "<PERSON><PERSON> so<PERSON>sang djambon", "block.minecraft.banner.triangle_top.purple": "<PERSON><PERSON> so<PERSON> o<PERSON>", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON> so<PERSON>sang merah", "block.minecraft.banner.triangle_top.white": "<PERSON><PERSON> so<PERSON> poetih", "block.minecraft.banner.triangle_top.yellow": "<PERSON><PERSON> so<PERSON> koening", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON>am", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON> p<PERSON>ng", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.light_blue": "<PERSON><PERSON> men<PERSON> biroe moeda", "block.minecraft.banner.triangles_bottom.light_gray": "<PERSON><PERSON> men<PERSON> k<PERSON> moeda", "block.minecraft.banner.triangles_bottom.lime": "<PERSON><PERSON> p<PERSON>", "block.minecraft.banner.triangles_bottom.magenta": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.pink": "<PERSON><PERSON> djambon", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON> men<PERSON> merah", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON> poetih", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON> koe<PERSON>", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON><PERSON> hitam", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON><PERSON> bi<PERSON>e", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON> p<PERSON>ng", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON><PERSON> men<PERSON> biroe moeda", "block.minecraft.banner.triangles_top.light_gray": "<PERSON><PERSON><PERSON> kela<PERSON> moeda", "block.minecraft.banner.triangles_top.lime": "<PERSON><PERSON><PERSON> hid<PERSON> p<PERSON>", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.orange": "<PERSON><PERSON><PERSON> djambon", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON><PERSON> djambon", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON><PERSON> men<PERSON> merah", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON> poetih", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON><PERSON> koening", "block.minecraft.barrel": "<PERSON>", "block.minecraft.barrier": "Penghalang", "block.minecraft.basalt": "Basal", "block.minecraft.beacon": "Soear", "block.minecraft.beacon.primary": "<PERSON><PERSON>", "block.minecraft.beacon.secondary": "<PERSON><PERSON>", "block.minecraft.bed.no_sleep": "Toean hanja boleh tidoer sa'at malam atau badai petir", "block.minecraft.bed.not_safe": "<PERSON><PERSON> tidak boleh tidoer sekarang karena ada monster dekat sini", "block.minecraft.bed.obstructed": "Katil ini terhalangi", "block.minecraft.bed.occupied": "Katil ini ditempati", "block.minecraft.bed.too_far_away": "<PERSON><PERSON> tidak boleh tidoer sekarang karena katil terlaloe dja<PERSON>h", "block.minecraft.bedrock": "<PERSON><PERSON>", "block.minecraft.bee_nest": "Sarang lebah", "block.minecraft.beehive": "<PERSON><PERSON> madoe", "block.minecraft.beetroots": "Bit", "block.minecraft.bell": "Bel", "block.minecraft.big_dripleaf": "<PERSON><PERSON><PERSON> tetes besar", "block.minecraft.big_dripleaf_stem": "Batang daoen tetes besar", "block.minecraft.birch_button": "<PERSON><PERSON> berk", "block.minecraft.birch_door": "Pintoe berk", "block.minecraft.birch_fence": "Pagar berk", "block.minecraft.birch_fence_gate": "<PERSON><PERSON><PERSON> pagar berk", "block.minecraft.birch_hanging_sign": "Papan tanda gantoeng berk", "block.minecraft.birch_leaves": "Dedaoenan berk", "block.minecraft.birch_log": "Gelondong berk", "block.minecraft.birch_planks": "<PERSON>n berk", "block.minecraft.birch_pressure_plate": "<PERSON><PERSON>t penekan berk", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON> berk", "block.minecraft.birch_sign": "<PERSON>n tanda berk", "block.minecraft.birch_slab": "<PERSON><PERSON>t berk", "block.minecraft.birch_stairs": "Tangga berk", "block.minecraft.birch_trapdoor": "Pintoe kolong berk", "block.minecraft.birch_wall_hanging_sign": "<PERSON>n tanda gantoeng dinding berk", "block.minecraft.birch_wall_sign": "<PERSON>n tanda dinding berk", "block.minecraft.birch_wood": "<PERSON><PERSON><PERSON> berk", "block.minecraft.black_banner": "<PERSON>ndoek hitam", "block.minecraft.black_bed": "<PERSON><PERSON>", "block.minecraft.black_candle": "<PERSON>in hitam", "block.minecraft.black_candle_cake": "Tar dengan lilin hitam", "block.minecraft.black_carpet": "<PERSON><PERSON>", "block.minecraft.black_concrete": "<PERSON><PERSON> hitam", "block.minecraft.black_concrete_powder": "Semen hitam", "block.minecraft.black_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_shulker_box": "Kotak shulker hitam", "block.minecraft.black_stained_glass": "<PERSON><PERSON>", "block.minecraft.black_stained_glass_pane": "<PERSON> katja hitam", "block.minecraft.black_terracotta": "Térakota hitam", "block.minecraft.black_wool": "Wol hitam", "block.minecraft.blackstone": "<PERSON><PERSON> hitam", "block.minecraft.blackstone_slab": "<PERSON><PERSON><PERSON> batoe hitam", "block.minecraft.blackstone_stairs": "Tangga batoe hitam", "block.minecraft.blackstone_wall": "Tembok batoe hitam", "block.minecraft.blast_furnace": "<PERSON><PERSON><PERSON> tioep", "block.minecraft.blue_banner": "Spandoek biru", "block.minecraft.blue_bed": "<PERSON><PERSON>", "block.minecraft.blue_candle": "<PERSON><PERSON> biroe", "block.minecraft.blue_candle_cake": "Tar dengan lilin biroe", "block.minecraft.blue_carpet": "<PERSON><PERSON>", "block.minecraft.blue_concrete": "<PERSON><PERSON> biroe", "block.minecraft.blue_concrete_powder": "Semen biroe", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_ice": "<PERSON><PERSON> biroe", "block.minecraft.blue_orchid": "<PERSON><PERSON><PERSON> biroe", "block.minecraft.blue_shulker_box": "Kotak shulker biroe", "block.minecraft.blue_stained_glass": "<PERSON><PERSON>", "block.minecraft.blue_stained_glass_pane": "<PERSON> katja biroe", "block.minecraft.blue_terracotta": "Térakota biroe", "block.minecraft.blue_wool": "<PERSON>ol biroe", "block.minecraft.bone_block": "Balok toelang", "block.minecraft.bookshelf": "<PERSON><PERSON> boe<PERSON>e", "block.minecraft.brain_coral": "Karang otak", "block.minecraft.brain_coral_block": "Balok karang otak", "block.minecraft.brain_coral_fan": "Kipas laoet otak", "block.minecraft.brain_coral_wall_fan": "Kipas laoet otak dinding", "block.minecraft.brewing_stand": "<PERSON><PERSON> per<PERSON>", "block.minecraft.brick_slab": "<PERSON><PERSON><PERSON> bata", "block.minecraft.brick_stairs": "<PERSON><PERSON> bata", "block.minecraft.brick_wall": "Tembok bata", "block.minecraft.bricks": "Balok bata", "block.minecraft.brown_banner": "Spandoek pirang", "block.minecraft.brown_bed": "<PERSON><PERSON> p<PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON> pirang", "block.minecraft.brown_candle_cake": "Tar dengan lilin pirang", "block.minecraft.brown_carpet": "<PERSON><PERSON> p<PERSON>ng", "block.minecraft.brown_concrete": "<PERSON><PERSON> pirang", "block.minecraft.brown_concrete_powder": "Semen pirang", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> pirang", "block.minecraft.brown_mushroom": "<PERSON><PERSON><PERSON><PERSON> pirang", "block.minecraft.brown_mushroom_block": "Balok dja<PERSON> pirang", "block.minecraft.brown_shulker_box": "Kotak shulker pirang", "block.minecraft.brown_stained_glass": "<PERSON><PERSON> p<PERSON>", "block.minecraft.brown_stained_glass_pane": "Panel katja pirang", "block.minecraft.brown_terracotta": "Térakota pirang", "block.minecraft.brown_wool": "<PERSON>ol pirang", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "Karang gelemboeng", "block.minecraft.bubble_coral_block": "Balok karang gelemboeng", "block.minecraft.bubble_coral_fan": "Kipas laoet gelemboeng", "block.minecraft.bubble_coral_wall_fan": "<PERSON><PERSON> laoet gelemboeng dinding", "block.minecraft.budding_amethyst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> berk<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bush": "Semak", "block.minecraft.cactus": "Kaktoes", "block.minecraft.cactus_flower": "<PERSON>eng<PERSON> kaktoes", "block.minecraft.cake": "Tar", "block.minecraft.calcite": "<PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "<PERSON><PERSON>dra sculk terkalibrasi", "block.minecraft.campfire": "<PERSON><PERSON>", "block.minecraft.candle": "<PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "Tar dengan lilin", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "<PERSON><PERSON><PERSON>", "block.minecraft.carved_pumpkin": "<PERSON><PERSON>", "block.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_air": "<PERSON><PERSON><PERSON> goea", "block.minecraft.cave_vines": "<PERSON><PERSON><PERSON> go<PERSON>", "block.minecraft.cave_vines_plant": "Tanaman perambat goea", "block.minecraft.chain": "<PERSON><PERSON><PERSON>", "block.minecraft.chain_command_block": "Balok perintah berantai", "block.minecraft.cherry_button": "<PERSON><PERSON> k<PERSON>n", "block.minecraft.cherry_door": "<PERSON><PERSON><PERSON> kersen", "block.minecraft.cherry_fence": "<PERSON><PERSON> kersen", "block.minecraft.cherry_fence_gate": "<PERSON><PERSON><PERSON> pagar kersen", "block.minecraft.cherry_hanging_sign": "<PERSON>n tanda gantoeng kersen", "block.minecraft.cherry_leaves": "<PERSON><PERSON><PERSON><PERSON> kersen", "block.minecraft.cherry_log": "Gelondong kersen", "block.minecraft.cherry_planks": "<PERSON><PERSON> kersen", "block.minecraft.cherry_pressure_plate": "<PERSON><PERSON><PERSON> penekan kersen", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON> kersen", "block.minecraft.cherry_sign": "<PERSON><PERSON> tanda kersen", "block.minecraft.cherry_slab": "<PERSON><PERSON><PERSON> kersen", "block.minecraft.cherry_stairs": "<PERSON><PERSON> kersen", "block.minecraft.cherry_trapdoor": "<PERSON>ntoe kolong kersen", "block.minecraft.cherry_wall_hanging_sign": "<PERSON>n tanda gantoeng dinding kersen", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON> tanda dinding kersen", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON> k<PERSON>n", "block.minecraft.chest": "<PERSON><PERSON>", "block.minecraft.chipped_anvil": "<PERSON><PERSON>", "block.minecraft.chiseled_bookshelf": "<PERSON>k boekoe pahatan", "block.minecraft.chiseled_copper": "Tembaga pahatan", "block.minecraft.chiseled_deepslate": "Batoe gelita pahatan", "block.minecraft.chiseled_nether_bricks": "<PERSON>a nether pahatan", "block.minecraft.chiseled_polished_blackstone": "<PERSON><PERSON> hitam gilap pahatan", "block.minecraft.chiseled_quartz_block": "Balok kwars pahatan", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON> pasir merah pahatan", "block.minecraft.chiseled_resin_bricks": "<PERSON>a damar pahatan", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON> pasir pahatan", "block.minecraft.chiseled_stone_bricks": "<PERSON>a batoe pahatan", "block.minecraft.chiseled_tuff": "Batoe aboe pahatan", "block.minecraft.chiseled_tuff_bricks": "<PERSON>a batoe aboe pahatan", "block.minecraft.chorus_flower": "<PERSON><PERSON><PERSON> kor", "block.minecraft.chorus_plant": "<PERSON><PERSON> kor", "block.minecraft.clay": "<PERSON>ah liat", "block.minecraft.closed_eyeblossom": "<PERSON><PERSON><PERSON> mata pedjam", "block.minecraft.coal_block": "Balok batoe bara", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON><PERSON> batoe bara", "block.minecraft.coarse_dirt": "<PERSON><PERSON> kasar", "block.minecraft.cobbled_deepslate": "Berangkal gelita", "block.minecraft.cobbled_deepslate_slab": "<PERSON><PERSON>t berangkal gelita", "block.minecraft.cobbled_deepslate_stairs": "Tangga berangkal gelita", "block.minecraft.cobbled_deepslate_wall": "Tembok berangkal gelita", "block.minecraft.cobblestone": "Berangkal", "block.minecraft.cobblestone_slab": "<PERSON><PERSON><PERSON> be<PERSON>", "block.minecraft.cobblestone_stairs": "Tangga berangkal", "block.minecraft.cobblestone_wall": "Tembok berangkal", "block.minecraft.cobweb": "<PERSON><PERSON><PERSON> laba-laba", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Balok perintah", "block.minecraft.comparator": "Pembanding redstone", "block.minecraft.composter": "Bak kompos", "block.minecraft.conduit": "Tenaga la<PERSON>t", "block.minecraft.copper_block": "Balok tembaga", "block.minecraft.copper_bulb": "<PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.copper_door": "<PERSON><PERSON>e te<PERSON>", "block.minecraft.copper_grate": "Kisi-kisi tembaga", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON><PERSON> temba<PERSON>", "block.minecraft.copper_trapdoor": "Pintoe kolong tembaga", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Bata batoe gelita retak", "block.minecraft.cracked_deepslate_tiles": "Tegel batoe gelita retak", "block.minecraft.cracked_nether_bricks": "<PERSON>a nether retak", "block.minecraft.cracked_polished_blackstone_bricks": "<PERSON>a batoe hitam gilap retak", "block.minecraft.cracked_stone_bricks": "<PERSON>a batoe retak", "block.minecraft.crafter": "<PERSON><PERSON>", "block.minecraft.crafting_table": "<PERSON><PERSON><PERSON>", "block.minecraft.creaking_heart": "<PERSON><PERSON><PERSON><PERSON> keri<PERSON>t", "block.minecraft.creeper_head": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.creeper_wall_head": "<PERSON><PERSON><PERSON> dinding creeper", "block.minecraft.crimson_button": "<PERSON><PERSON>", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.crimson_fence": "<PERSON><PERSON> k<PERSON>", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON><PERSON> pagar kir<PERSON>", "block.minecraft.crimson_fungus": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_hanging_sign": "<PERSON>n tanda gantoeng kirmizi", "block.minecraft.crimson_hyphae": "<PERSON><PERSON> k<PERSON> kir<PERSON>", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON><PERSON> kir<PERSON>zi", "block.minecraft.crimson_planks": "<PERSON><PERSON> k<PERSON>", "block.minecraft.crimson_pressure_plate": "<PERSON><PERSON><PERSON> penekan kir<PERSON>", "block.minecraft.crimson_roots": "<PERSON><PERSON>", "block.minecraft.crimson_sign": "<PERSON><PERSON> tanda kir<PERSON>zi", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.crimson_stairs": "<PERSON><PERSON> k<PERSON>", "block.minecraft.crimson_stem": "Batang kirmizi", "block.minecraft.crimson_trapdoor": "Pintoe kolong kirmizi", "block.minecraft.crimson_wall_hanging_sign": "<PERSON>n tanda gantoeng dinding kir<PERSON>zi", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON> tanda dinding kir<PERSON>zi", "block.minecraft.crying_obsidian": "<PERSON><PERSON> gelas menangis", "block.minecraft.cut_copper": "<PERSON><PERSON><PERSON> potongan", "block.minecraft.cut_copper_slab": "<PERSON><PERSON>t tembaga potongan", "block.minecraft.cut_copper_stairs": "Tangga tembaga potongan", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON> pasir merah potongan", "block.minecraft.cut_red_sandstone_slab": "<PERSON><PERSON>t batoe pasir merah potongan", "block.minecraft.cut_sandstone": "<PERSON><PERSON> pasir potongan", "block.minecraft.cut_sandstone_slab": "<PERSON><PERSON><PERSON> batoe pasir potongan", "block.minecraft.cyan_banner": "Spandoek <PERSON>lang<PERSON>eka", "block.minecraft.cyan_bed": "<PERSON><PERSON>", "block.minecraft.cyan_candle": "<PERSON><PERSON>", "block.minecraft.cyan_candle_cake": "Tar dengan lilin ni<PERSON>", "block.minecraft.cyan_carpet": "<PERSON><PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON>", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_shulker_box": "Kotak shulker nilangsoeka", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON>", "block.minecraft.cyan_stained_glass_pane": "Panel katja <PERSON>", "block.minecraft.cyan_terracotta": "Térakota nilangsoeka", "block.minecraft.cyan_wool": "<PERSON><PERSON>", "block.minecraft.damaged_anvil": "<PERSON><PERSON>", "block.minecraft.dandelion": "<PERSON><PERSON>", "block.minecraft.dark_oak_button": "Kenop ek gelap", "block.minecraft.dark_oak_door": "Pintoe ek gelap", "block.minecraft.dark_oak_fence": "<PERSON>gar ek gelap", "block.minecraft.dark_oak_fence_gate": "<PERSON><PERSON><PERSON> pagar ek gelap", "block.minecraft.dark_oak_hanging_sign": "Papan tanda gantoeng ek gelap", "block.minecraft.dark_oak_leaves": "Dedaoenan ek gelap", "block.minecraft.dark_oak_log": "Gelondong ek gelap", "block.minecraft.dark_oak_planks": "Papan ek gelap", "block.minecraft.dark_oak_pressure_plate": "<PERSON><PERSON>t penekan ek gelap", "block.minecraft.dark_oak_sapling": "<PERSON><PERSON>t ek gelap", "block.minecraft.dark_oak_sign": "Papan tanda ek gelap", "block.minecraft.dark_oak_slab": "P<PERSON>t ek gelap", "block.minecraft.dark_oak_stairs": "Tangga ek gelap", "block.minecraft.dark_oak_trapdoor": "Pintoe kolong ek gelap", "block.minecraft.dark_oak_wall_hanging_sign": "Papan tanda gantoeng dinding ek gelap", "block.minecraft.dark_oak_wall_sign": "<PERSON>n tanda dinding ek gelap", "block.minecraft.dark_oak_wood": "<PERSON><PERSON><PERSON> ek gelap", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON><PERSON> gelap", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON>t prismarin gelap", "block.minecraft.dark_prismarine_stairs": "Tangga prismarin gelap", "block.minecraft.daylight_detector": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.dead_brain_coral": "Karang otak mati", "block.minecraft.dead_brain_coral_block": "Balok karang otak mati", "block.minecraft.dead_brain_coral_fan": "Kipas laoet otak mati", "block.minecraft.dead_brain_coral_wall_fan": "Kipas laoet otak dinding mati", "block.minecraft.dead_bubble_coral": "Karang gelemboeng mati", "block.minecraft.dead_bubble_coral_block": "Balok karang gelemboeng mati", "block.minecraft.dead_bubble_coral_fan": "Kipas laoet gelemboeng mati", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON> laoet gelemboeng dinding mati", "block.minecraft.dead_bush": "<PERSON><PERSON><PERSON> mati", "block.minecraft.dead_fire_coral": "<PERSON>ng api mati", "block.minecraft.dead_fire_coral_block": "Balok karang api mati", "block.minecraft.dead_fire_coral_fan": "Ki<PERSON> laoet api mati", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON> laoet api dinding mati", "block.minecraft.dead_horn_coral": "<PERSON>ng tandoek mati", "block.minecraft.dead_horn_coral_block": "Balok karang tandoek mati", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON> laoet tandoek mati", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON> laoet tandoek dinding mati", "block.minecraft.dead_tube_coral": "<PERSON>ng pipa mati", "block.minecraft.dead_tube_coral_block": "Balok karang pipa mati", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON> laoet pipa mati", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON> laoet pipa dinding mati", "block.minecraft.decorated_pot": "Pasoe", "block.minecraft.deepslate": "<PERSON>oe gelita", "block.minecraft.deepslate_brick_slab": "<PERSON><PERSON><PERSON> bata batoe gelita", "block.minecraft.deepslate_brick_stairs": "Tang<PERSON> bata batoe gelita", "block.minecraft.deepslate_brick_wall": "Tembok bata batoe gelita", "block.minecraft.deepslate_bricks": "<PERSON>a batoe gelita", "block.minecraft.deepslate_coal_ore": "<PERSON><PERSON><PERSON><PERSON> batoe bara gelita", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON><PERSON><PERSON> tembaga gelita", "block.minecraft.deepslate_diamond_ore": "<PERSON><PERSON><PERSON><PERSON> berlian gelita", "block.minecraft.deepslate_emerald_ore": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> gelita", "block.minecraft.deepslate_gold_ore": "<PERSON><PERSON><PERSON><PERSON> emas gelita", "block.minecraft.deepslate_iron_ore": "<PERSON><PERSON><PERSON><PERSON> besi gelita", "block.minecraft.deepslate_lapis_ore": "<PERSON><PERSON><PERSON><PERSON> lazo<PERSON>di gelita", "block.minecraft.deepslate_redstone_ore": "Bidjih redstone gelita", "block.minecraft.deepslate_tile_slab": "<PERSON><PERSON>t tegel batoe gelita", "block.minecraft.deepslate_tile_stairs": "Tangga tegel batoe gelita", "block.minecraft.deepslate_tile_wall": "Tembok tegel batoe gelita", "block.minecraft.deepslate_tiles": "Tegel batoe gelita", "block.minecraft.detector_rail": "<PERSON><PERSON>", "block.minecraft.diamond_block": "<PERSON>lok be<PERSON>", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "<PERSON><PERSON><PERSON> di<PERSON>t", "block.minecraft.diorite_stairs": "Tangga diorit", "block.minecraft.diorite_wall": "Tembok diorit", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "<PERSON><PERSON><PERSON> tanah", "block.minecraft.dispenser": "Pelempar", "block.minecraft.dragon_egg": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "<PERSON><PERSON><PERSON> dinding <PERSON>ga", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON> kering", "block.minecraft.dried_kelp_block": "Balok komboe kering", "block.minecraft.dripstone_block": "Balok batoe tetes", "block.minecraft.dropper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "Balok zamroed", "block.minecraft.emerald_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.enchanting_table": "<PERSON><PERSON><PERSON>", "block.minecraft.end_gateway": "Gapoera End", "block.minecraft.end_portal": "Gerbang End", "block.minecraft.end_portal_frame": "Bingkai gerbang End", "block.minecraft.end_rod": "Tongkat End", "block.minecraft.end_stone": "Batoe End", "block.minecraft.end_stone_brick_slab": "<PERSON><PERSON><PERSON> bata batoe End", "block.minecraft.end_stone_brick_stairs": "Tangga bata batoe End", "block.minecraft.end_stone_brick_wall": "Tembok bata batoe End", "block.minecraft.end_stone_bricks": "<PERSON><PERSON> batoe <PERSON>", "block.minecraft.ender_chest": "<PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Tembaga pahatan terbeber", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON> terbeber", "block.minecraft.exposed_copper_bulb": "<PERSON><PERSON><PERSON> tembaga terbeber", "block.minecraft.exposed_copper_door": "<PERSON><PERSON><PERSON> tembaga terbeber", "block.minecraft.exposed_copper_grate": "Kisi-kisi tembaga terbeber", "block.minecraft.exposed_copper_trapdoor": "Pintoe kolong tembaga terbeber", "block.minecraft.exposed_cut_copper": "<PERSON><PERSON><PERSON> potongan terbeber", "block.minecraft.exposed_cut_copper_slab": "<PERSON><PERSON><PERSON> tembaga potongan terbeber", "block.minecraft.exposed_cut_copper_stairs": "Tangga tembaga potongan terbeber", "block.minecraft.farmland": "<PERSON><PERSON> gara<PERSON>", "block.minecraft.fern": "<PERSON><PERSON>", "block.minecraft.fire": "Api", "block.minecraft.fire_coral": "<PERSON><PERSON> api", "block.minecraft.fire_coral_block": "Balok karang api", "block.minecraft.fire_coral_fan": "Kipas laoet api", "block.minecraft.fire_coral_wall_fan": "<PERSON><PERSON> laoet api dinding", "block.minecraft.firefly_bush": "Semak koenang-koenang", "block.minecraft.fletching_table": "<PERSON><PERSON><PERSON>", "block.minecraft.flower_pot": "Pot boenga", "block.minecraft.flowering_azalea": "<PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea_leaves": "<PERSON><PERSON><PERSON>nan azalea mekar", "block.minecraft.frogspawn": "Teloer katak", "block.minecraft.frosted_ice": "<PERSON><PERSON> b<PERSON>", "block.minecraft.furnace": "Oven", "block.minecraft.gilded_blackstone": "<PERSON><PERSON> hitam bersepoeh", "block.minecraft.glass": "<PERSON><PERSON>", "block.minecraft.glass_pane": "Panel katja", "block.minecraft.glow_lichen": "Loemoet kerak sinar", "block.minecraft.glowstone": "<PERSON><PERSON> sinar", "block.minecraft.gold_block": "Balok emas", "block.minecraft.gold_ore": "<PERSON><PERSON><PERSON><PERSON> emas", "block.minecraft.granite": "Granit", "block.minecraft.granite_slab": "<PERSON><PERSON><PERSON> granit", "block.minecraft.granite_stairs": "Tangga granit", "block.minecraft.granite_wall": "Tembok granit", "block.minecraft.grass": "Roempoet", "block.minecraft.grass_block": "Balok roempoet", "block.minecraft.gravel": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_banner": "Spandoek kela<PERSON>", "block.minecraft.gray_bed": "<PERSON><PERSON>", "block.minecraft.gray_candle": "<PERSON><PERSON>", "block.minecraft.gray_candle_cake": "Tar dengan lilin kela<PERSON>", "block.minecraft.gray_carpet": "<PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON> k<PERSON>", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_shulker_box": "Kotak shulker kelaboe", "block.minecraft.gray_stained_glass": "<PERSON><PERSON>", "block.minecraft.gray_stained_glass_pane": "<PERSON> katja k<PERSON>", "block.minecraft.gray_terracotta": "Térakota kelaboe", "block.minecraft.gray_wool": "<PERSON><PERSON> k<PERSON>", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON> hidjau", "block.minecraft.green_bed": "<PERSON><PERSON>", "block.minecraft.green_candle": "<PERSON><PERSON>", "block.minecraft.green_candle_cake": "Tar dengan lilin hidjau", "block.minecraft.green_carpet": "<PERSON><PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON>", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_shulker_box": "Kotak shulker hidjau", "block.minecraft.green_stained_glass": "<PERSON><PERSON>", "block.minecraft.green_stained_glass_pane": "<PERSON> katja <PERSON>", "block.minecraft.green_terracotta": "Térakota hidjau", "block.minecraft.green_wool": "<PERSON><PERSON>", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON>", "block.minecraft.hay_block": "D<PERSON><PERSON>", "block.minecraft.heavy_core": "<PERSON> berat", "block.minecraft.heavy_weighted_pressure_plate": "<PERSON><PERSON>t penekan pemberat berat", "block.minecraft.honey_block": "Balok madoe", "block.minecraft.honeycomb_block": "Balok sarang madoe", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "<PERSON><PERSON> tando<PERSON>", "block.minecraft.horn_coral_block": "Balok karang tandoek", "block.minecraft.horn_coral_fan": "<PERSON><PERSON> laoet tandoek", "block.minecraft.horn_coral_wall_fan": "<PERSON><PERSON> laoet tandoek dinding", "block.minecraft.ice": "Es", "block.minecraft.infested_chiseled_stone_bricks": "<PERSON>a batoe pahatan berhama", "block.minecraft.infested_cobblestone": "Berangkal berhama", "block.minecraft.infested_cracked_stone_bricks": "<PERSON>a batoe retak berhama", "block.minecraft.infested_deepslate": "Batoe gelita berhama", "block.minecraft.infested_mossy_stone_bricks": "<PERSON>a batoe berloemoet berhama", "block.minecraft.infested_stone": "<PERSON><PERSON> berhama", "block.minecraft.infested_stone_bricks": "<PERSON>a batoe berhama", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "Balok besi", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON> besi", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON><PERSON> besi", "block.minecraft.iron_trapdoor": "Pintoe kolong besi", "block.minecraft.jack_o_lantern": "<PERSON><PERSON> laboe", "block.minecraft.jigsaw": "Balok keping", "block.minecraft.jukebox": "Pemoetar pelat", "block.minecraft.jungle_button": "<PERSON><PERSON> rim<PERSON>", "block.minecraft.jungle_door": "<PERSON><PERSON><PERSON> rimba", "block.minecraft.jungle_fence": "<PERSON><PERSON> rimba", "block.minecraft.jungle_fence_gate": "<PERSON><PERSON><PERSON> pagar rimba", "block.minecraft.jungle_hanging_sign": "<PERSON>n tanda gantoeng rimba", "block.minecraft.jungle_leaves": "<PERSON><PERSON><PERSON><PERSON> pohon rimba", "block.minecraft.jungle_log": "Gelondong rimba", "block.minecraft.jungle_planks": "<PERSON>n rimba", "block.minecraft.jungle_pressure_plate": "<PERSON><PERSON><PERSON> penekan rimba", "block.minecraft.jungle_sapling": "<PERSON><PERSON><PERSON> pohon rimba", "block.minecraft.jungle_sign": "<PERSON>n tanda rimba", "block.minecraft.jungle_slab": "<PERSON><PERSON><PERSON> rimba", "block.minecraft.jungle_stairs": "Tangga rimba", "block.minecraft.jungle_trapdoor": "Pintoe kolong rimba", "block.minecraft.jungle_wall_hanging_sign": "<PERSON>n tanda gantoeng dinding rimba", "block.minecraft.jungle_wall_sign": "<PERSON><PERSON> tanda dinding rimba", "block.minecraft.jungle_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp": "Komboe", "block.minecraft.kelp_plant": "Tan<PERSON> komboe", "block.minecraft.ladder": "Tangga", "block.minecraft.lantern": "Lentera", "block.minecraft.lapis_block": "<PERSON>lok <PERSON>", "block.minecraft.lapis_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.large_amethyst_bud": "<PERSON><PERSON><PERSON><PERSON> ket<PERSON> besar", "block.minecraft.large_fern": "<PERSON><PERSON>", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Koeali lava", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON> kering", "block.minecraft.lectern": "<PERSON><PERSON>", "block.minecraft.lever": "<PERSON><PERSON>", "block.minecraft.light": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_banner": "Spandoek biroe moeda", "block.minecraft.light_blue_bed": "<PERSON><PERSON> biroe moeda", "block.minecraft.light_blue_candle": "<PERSON>in biroe moeda", "block.minecraft.light_blue_candle_cake": "Tar dengan lilin biroe moeda", "block.minecraft.light_blue_carpet": "<PERSON><PERSON> biroe moeda", "block.minecraft.light_blue_concrete": "<PERSON><PERSON> biroe moeda", "block.minecraft.light_blue_concrete_powder": "Semen biroe moeda", "block.minecraft.light_blue_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> biroe moeda", "block.minecraft.light_blue_shulker_box": "Kotak shulker biroe moeda", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON> bi<PERSON>e moeda", "block.minecraft.light_blue_stained_glass_pane": "Panel katja biroe moeda", "block.minecraft.light_blue_terracotta": "Térakota biroe moeda", "block.minecraft.light_blue_wool": "Wol biroe moeda", "block.minecraft.light_gray_banner": "Spandoek kelaboe moeda", "block.minecraft.light_gray_bed": "<PERSON><PERSON> k<PERSON> moeda", "block.minecraft.light_gray_candle": "<PERSON><PERSON> k<PERSON> moeda", "block.minecraft.light_gray_candle_cake": "Tar dengan lilin kelaboe moeda", "block.minecraft.light_gray_carpet": "<PERSON><PERSON> k<PERSON> moeda", "block.minecraft.light_gray_concrete": "<PERSON><PERSON> k<PERSON> moeda", "block.minecraft.light_gray_concrete_powder": "Semen kela<PERSON> moeda", "block.minecraft.light_gray_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> moeda", "block.minecraft.light_gray_shulker_box": "Kotak shulker kelaboe moeda", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON> moeda", "block.minecraft.light_gray_stained_glass_pane": "Panel katja k<PERSON> moeda", "block.minecraft.light_gray_terracotta": "Térakota kelaboe moeda", "block.minecraft.light_gray_wool": "<PERSON>ol kelaboe moeda", "block.minecraft.light_weighted_pressure_plate": "P<PERSON>t penekan pemberat ringan", "block.minecraft.lightning_rod": "Penangkal petir", "block.minecraft.lilac": "<PERSON>", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON>", "block.minecraft.lily_pad": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_banner": "Spandoek hidjau p<PERSON>", "block.minecraft.lime_bed": "<PERSON><PERSON>", "block.minecraft.lime_candle": "<PERSON><PERSON>", "block.minecraft.lime_candle_cake": "Tar dengan lilin hidjau p<PERSON>es", "block.minecraft.lime_carpet": "<PERSON><PERSON>", "block.minecraft.lime_concrete": "<PERSON><PERSON> hid<PERSON> p<PERSON>", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON> p<PERSON>", "block.minecraft.lime_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_shulker_box": "Kotak shulker hidjau p<PERSON>es", "block.minecraft.lime_stained_glass": "<PERSON><PERSON>", "block.minecraft.lime_stained_glass_pane": "Panel katja hid<PERSON> p<PERSON>", "block.minecraft.lime_terracotta": "Térakota hidjau p<PERSON>", "block.minecraft.lime_wool": "<PERSON><PERSON> p<PERSON>", "block.minecraft.lodestone": "Batoe magnet", "block.minecraft.loom": "Alat tenoen", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON> patma", "block.minecraft.magenta_bed": "<PERSON><PERSON>", "block.minecraft.magenta_candle": "<PERSON>in patma", "block.minecraft.magenta_candle_cake": "Tar dengan lilin patma", "block.minecraft.magenta_carpet": "<PERSON><PERSON>", "block.minecraft.magenta_concrete": "<PERSON><PERSON> patma", "block.minecraft.magenta_concrete_powder": "Semen patma", "block.minecraft.magenta_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON> shulker patma", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON>", "block.minecraft.magenta_stained_glass_pane": "Panel katja patma", "block.minecraft.magenta_terracotta": "Térakot<PERSON> patma", "block.minecraft.magenta_wool": "<PERSON><PERSON>", "block.minecraft.magma_block": "Balok magma", "block.minecraft.mangrove_button": "<PERSON><PERSON> bakau", "block.minecraft.mangrove_door": "<PERSON><PERSON><PERSON> bakau", "block.minecraft.mangrove_fence": "<PERSON><PERSON> bakau", "block.minecraft.mangrove_fence_gate": "<PERSON><PERSON><PERSON> pagar bakau", "block.minecraft.mangrove_hanging_sign": "<PERSON>n tanda gantoeng bakau", "block.minecraft.mangrove_leaves": "<PERSON><PERSON><PERSON><PERSON> bakau", "block.minecraft.mangrove_log": "<PERSON><PERSON><PERSON><PERSON> bakau", "block.minecraft.mangrove_planks": "<PERSON><PERSON> bakau", "block.minecraft.mangrove_pressure_plate": "<PERSON><PERSON><PERSON> penekan bakau", "block.minecraft.mangrove_propagule": "<PERSON><PERSON><PERSON> bakau", "block.minecraft.mangrove_roots": "<PERSON><PERSON> bakau", "block.minecraft.mangrove_sign": "<PERSON>n tanda bakau", "block.minecraft.mangrove_slab": "<PERSON><PERSON><PERSON> bakau", "block.minecraft.mangrove_stairs": "<PERSON><PERSON> bakau", "block.minecraft.mangrove_trapdoor": "<PERSON><PERSON><PERSON> kolong bakau", "block.minecraft.mangrove_wall_hanging_sign": "<PERSON>n tanda gantoeng dinding bakau", "block.minecraft.mangrove_wall_sign": "<PERSON><PERSON> tanda dinding bakau", "block.minecraft.mangrove_wood": "<PERSON><PERSON><PERSON> bakau", "block.minecraft.medium_amethyst_bud": "<PERSON><PERSON><PERSON><PERSON> ket<PERSON> menengah", "block.minecraft.melon": "Semangka", "block.minecraft.melon_stem": "Batang semangka", "block.minecraft.moss_block": "Balok loemoet", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.mossy_cobblestone": "Berangkal berloemoet", "block.minecraft.mossy_cobblestone_slab": "Pelat berangkal berloemoet", "block.minecraft.mossy_cobblestone_stairs": "Tangga berangkal berloemoet", "block.minecraft.mossy_cobblestone_wall": "Tembok berangkal berloemoet", "block.minecraft.mossy_stone_brick_slab": "<PERSON><PERSON><PERSON> bata batoe berloe<PERSON>et", "block.minecraft.mossy_stone_brick_stairs": "Tangga bata batoe berloe<PERSON>et", "block.minecraft.mossy_stone_brick_wall": "Tembok bata batoe berloemoet", "block.minecraft.mossy_stone_bricks": "<PERSON>a batoe berloe<PERSON>", "block.minecraft.moving_piston": "<PERSON><PERSON> bergerak", "block.minecraft.mud": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_brick_slab": "<PERSON><PERSON><PERSON> bata lo<PERSON>", "block.minecraft.mud_brick_stairs": "<PERSON><PERSON> bata loem<PERSON>", "block.minecraft.mud_brick_wall": "Tembok bata lo<PERSON><PERSON><PERSON>", "block.minecraft.mud_bricks": "<PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON> bakau <PERSON>", "block.minecraft.mushroom_stem": "<PERSON><PERSON> d<PERSON>", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.nether_brick_fence": "<PERSON><PERSON> bata nether", "block.minecraft.nether_brick_slab": "<PERSON><PERSON><PERSON> bata nether", "block.minecraft.nether_brick_stairs": "<PERSON>ga bata nether", "block.minecraft.nether_brick_wall": "Tembok bata nether", "block.minecraft.nether_bricks": "<PERSON><PERSON>k bata nether", "block.minecraft.nether_gold_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_portal": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON><PERSON><PERSON> kwars nether", "block.minecraft.nether_sprouts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_wart": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_wart_block": "Balok keto<PERSON>", "block.minecraft.netherite_block": "Balok netherit", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Kotak nada", "block.minecraft.oak_button": "Kenop ek", "block.minecraft.oak_door": "Pintoe ek", "block.minecraft.oak_fence": "Pagar ek", "block.minecraft.oak_fence_gate": "<PERSON>ntoe pagar ek", "block.minecraft.oak_hanging_sign": "Papan tanda gantoeng ek", "block.minecraft.oak_leaves": "Dedaoenan ek", "block.minecraft.oak_log": "Gelondong ek", "block.minecraft.oak_planks": "Papan ek", "block.minecraft.oak_pressure_plate": "Pelat penekan ek", "block.minecraft.oak_sapling": "Bibit ek", "block.minecraft.oak_sign": "Papan tanda ek", "block.minecraft.oak_slab": "Pelat ek", "block.minecraft.oak_stairs": "Tangga ek", "block.minecraft.oak_trapdoor": "Pintoe kolong ek", "block.minecraft.oak_wall_hanging_sign": "Papan tanda gantoeng dinding ek", "block.minecraft.oak_wall_sign": "<PERSON>n tanda dinding ek", "block.minecraft.oak_wood": "<PERSON><PERSON><PERSON> ek", "block.minecraft.observer": "Pengamat", "block.minecraft.obsidian": "<PERSON><PERSON> gelas", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON> katak hartal", "block.minecraft.ominous_banner": "<PERSON>ndoek seram", "block.minecraft.open_eyeblossom": "<PERSON><PERSON><PERSON> mata djaga", "block.minecraft.orange_banner": "Spandoek oranje", "block.minecraft.orange_bed": "<PERSON><PERSON>", "block.minecraft.orange_candle": "<PERSON><PERSON>", "block.minecraft.orange_candle_cake": "Tar dengan lilin oranje", "block.minecraft.orange_carpet": "<PERSON><PERSON>", "block.minecraft.orange_concrete": "<PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "<PERSON><PERSON> or<PERSON>", "block.minecraft.orange_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_shulker_box": "Kotak shulker oranje", "block.minecraft.orange_stained_glass": "<PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "Panel katja oranje", "block.minecraft.orange_terracotta": "Térakota oranje", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON> or<PERSON>", "block.minecraft.orange_wool": "<PERSON><PERSON> oranje", "block.minecraft.oxeye_daisy": "Marg<PERSON>", "block.minecraft.oxidized_chiseled_copper": "Tembaga pahatan teroksidasi", "block.minecraft.oxidized_copper": "Tembaga te<PERSON>dasi", "block.minecraft.oxidized_copper_bulb": "<PERSON><PERSON><PERSON> tembaga te<PERSON>i", "block.minecraft.oxidized_copper_door": "Pintoe tembaga teroksidasi", "block.minecraft.oxidized_copper_grate": "Kisi-kisi tembaga teroksidasi", "block.minecraft.oxidized_copper_trapdoor": "Pintoe kolong tembaga teroksidasi", "block.minecraft.oxidized_cut_copper": "Tembaga potongan teroksidasi", "block.minecraft.oxidized_cut_copper_slab": "Pelat tembaga potongan teroksidasi", "block.minecraft.oxidized_cut_copper_stairs": "Tangga tembaga potongan teroksidasi", "block.minecraft.packed_ice": "Es padat", "block.minecraft.packed_mud": "<PERSON><PERSON><PERSON><PERSON> padat", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.pale_moss_block": "Balok loemoet poet<PERSON>", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_button": "<PERSON><PERSON> ek <PERSON>", "block.minecraft.pale_oak_door": "Pintoe ek <PERSON>t", "block.minecraft.pale_oak_fence": "<PERSON><PERSON> ek <PERSON>t", "block.minecraft.pale_oak_fence_gate": "<PERSON><PERSON><PERSON> pagar ek <PERSON>t", "block.minecraft.pale_oak_hanging_sign": "Papan tanda gantoeng ek poetjat", "block.minecraft.pale_oak_leaves": "Dedaoenan ek <PERSON>t", "block.minecraft.pale_oak_log": "Gelondong ek poet<PERSON>t", "block.minecraft.pale_oak_planks": "Papan ek <PERSON>", "block.minecraft.pale_oak_pressure_plate": "<PERSON><PERSON>t penekan ek <PERSON>t", "block.minecraft.pale_oak_sapling": "<PERSON><PERSON>t ek <PERSON>t", "block.minecraft.pale_oak_sign": "<PERSON>n tanda ek <PERSON>t", "block.minecraft.pale_oak_slab": "<PERSON><PERSON>t ek <PERSON>t", "block.minecraft.pale_oak_stairs": "Tangga ek poet<PERSON>t", "block.minecraft.pale_oak_trapdoor": "Pintoe kolong ek poet<PERSON>t", "block.minecraft.pale_oak_wall_hanging_sign": "<PERSON>n tanda gantoeng dinding ek poetjat", "block.minecraft.pale_oak_wall_sign": "<PERSON>n tanda gantoeng dinding", "block.minecraft.pale_oak_wood": "<PERSON><PERSON><PERSON> ek <PERSON>t", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON> katak gem<PERSON>lap", "block.minecraft.peony": "<PERSON><PERSON><PERSON>", "block.minecraft.petrified_oak_slab": "Pelat ek membatoe", "block.minecraft.piglin_head": "<PERSON><PERSON><PERSON> piglin", "block.minecraft.piglin_wall_head": "<PERSON><PERSON><PERSON> dinding piglin", "block.minecraft.pink_banner": "Spandoek djambon", "block.minecraft.pink_bed": "<PERSON>il dja<PERSON>n", "block.minecraft.pink_candle": "Lilin djambon", "block.minecraft.pink_candle_cake": "Tar dengan lilin djambon", "block.minecraft.pink_carpet": "Ambal djambon", "block.minecraft.pink_concrete": "Beton djambon", "block.minecraft.pink_concrete_powder": "Semen djambon", "block.minecraft.pink_glazed_terracotta": "Tembikar djambon", "block.minecraft.pink_petals": "<PERSON><PERSON><PERSON><PERSON> kersen", "block.minecraft.pink_shulker_box": "Kotak shulker djambon", "block.minecraft.pink_stained_glass": "Katja djambon", "block.minecraft.pink_stained_glass_pane": "Panel katja djambon", "block.minecraft.pink_terracotta": "Térakota djambon", "block.minecraft.pink_tulip": "Boenga doelban djambon", "block.minecraft.pink_wool": "Wol djambon", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "<PERSON><PERSON><PERSON> piston", "block.minecraft.pitcher_crop": "Tanaman perioek kera", "block.minecraft.pitcher_plant": "Perioek kera", "block.minecraft.player_head": "<PERSON><PERSON><PERSON>", "block.minecraft.player_head.named": "Kepala %s", "block.minecraft.player_wall_head": "<PERSON><PERSON><PERSON> dinding p<PERSON><PERSON><PERSON>", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "Batoe tetes roentjing", "block.minecraft.polished_andesite": "Andesit gilap", "block.minecraft.polished_andesite_slab": "<PERSON><PERSON><PERSON> and<PERSON>t gilap", "block.minecraft.polished_andesite_stairs": "Tangga andesit gilap", "block.minecraft.polished_basalt": "Basal gilap", "block.minecraft.polished_blackstone": "<PERSON><PERSON> hitam gilap", "block.minecraft.polished_blackstone_brick_slab": "<PERSON><PERSON><PERSON> bata batoe hitam gilap", "block.minecraft.polished_blackstone_brick_stairs": "<PERSON>ga bata batoe hitam gilap", "block.minecraft.polished_blackstone_brick_wall": "Tembok bata batoe hitam gilap", "block.minecraft.polished_blackstone_bricks": "<PERSON>a batoe hitam gilap", "block.minecraft.polished_blackstone_button": "<PERSON><PERSON> bata batoe hitam gilap", "block.minecraft.polished_blackstone_pressure_plate": "<PERSON><PERSON><PERSON> penekan batoe hitam gilap", "block.minecraft.polished_blackstone_slab": "<PERSON><PERSON>t batoe hitam gilap", "block.minecraft.polished_blackstone_stairs": "Tangga batoe hitam gilap", "block.minecraft.polished_blackstone_wall": "Tembok batoe hitam gilap", "block.minecraft.polished_deepslate": "Batoe gelita gilap", "block.minecraft.polished_deepslate_slab": "<PERSON>elat batoe gelita gilap", "block.minecraft.polished_deepslate_stairs": "Tangga batoe gelita gilap", "block.minecraft.polished_deepslate_wall": "Tembok batoe gelita gilap", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON> gilap", "block.minecraft.polished_diorite_slab": "<PERSON><PERSON>t diorit gilap", "block.minecraft.polished_diorite_stairs": "Tangga diorit gilap", "block.minecraft.polished_granite": "Granit gilap", "block.minecraft.polished_granite_slab": "<PERSON><PERSON>t granit gilap", "block.minecraft.polished_granite_stairs": "Tangga granit gilap", "block.minecraft.polished_tuff": "Batoe aboe gilap", "block.minecraft.polished_tuff_slab": "<PERSON><PERSON>t batoe aboe gilap", "block.minecraft.polished_tuff_stairs": "Tangga batoe aboe gilap", "block.minecraft.polished_tuff_wall": "Tembok batoe aboe gilap", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "<PERSON><PERSON><PERSON> akasia pot", "block.minecraft.potted_allium": "<PERSON><PERSON><PERSON> bawang pot", "block.minecraft.potted_azalea_bush": "Azalea pot", "block.minecraft.potted_azure_bluet": "Boenga liar biroe pot", "block.minecraft.potted_bamboo": "Bamboe pot", "block.minecraft.potted_birch_sapling": "Bibit berk pot", "block.minecraft.potted_blue_orchid": "Anggrek biroe pot", "block.minecraft.potted_brown_mushroom": "Djamoer pirang pot", "block.minecraft.potted_cactus": "Kaktoes pot", "block.minecraft.potted_cherry_sapling": "<PERSON><PERSON><PERSON> kersen pot", "block.minecraft.potted_closed_eyeblossom": "Boenga mata pedjam pot", "block.minecraft.potted_cornflower": "Boenga djagoeng pot", "block.minecraft.potted_crimson_fungus": "Djamoer kir<PERSON>zi pot", "block.minecraft.potted_crimson_roots": "<PERSON><PERSON> k<PERSON> pot", "block.minecraft.potted_dandelion": "<PERSON>a tapak pot", "block.minecraft.potted_dark_oak_sapling": "B<PERSON>t ek gelap pot", "block.minecraft.potted_dead_bush": "Semak mati pot", "block.minecraft.potted_fern": "Pakis pot", "block.minecraft.potted_flowering_azalea_bush": "Azalea mekar pot", "block.minecraft.potted_jungle_sapling": "<PERSON><PERSON><PERSON> pohon rimba pot", "block.minecraft.potted_lily_of_the_valley": "<PERSON><PERSON> le<PERSON> pot", "block.minecraft.potted_mangrove_propagule": "<PERSON><PERSON><PERSON> bakau pot", "block.minecraft.potted_oak_sapling": "B<PERSON>t ek pot", "block.minecraft.potted_open_eyeblossom": "Boenga mata djaga pot", "block.minecraft.potted_orange_tulip": "<PERSON>eng<PERSON> doelban oranje pot", "block.minecraft.potted_oxeye_daisy": "Margrit pot", "block.minecraft.potted_pale_oak_sapling": "<PERSON><PERSON>t ek <PERSON>t pot", "block.minecraft.potted_pink_tulip": "Boenga doelban djambon pot", "block.minecraft.potted_poppy": "Papaver pot", "block.minecraft.potted_red_mushroom": "Dja<PERSON>er merah pot", "block.minecraft.potted_red_tulip": "<PERSON>enga doelban merah pot", "block.minecraft.potted_spruce_sapling": "Bibit spar pot", "block.minecraft.potted_torchflower": "<PERSON><PERSON><PERSON> obor plot", "block.minecraft.potted_warped_fungus": "Djamoer keroekoet pot", "block.minecraft.potted_warped_roots": "Akar keroekoet pot", "block.minecraft.potted_white_tulip": "<PERSON><PERSON><PERSON> doelban poetih pot", "block.minecraft.potted_wither_rose": "<PERSON><PERSON> pot", "block.minecraft.powder_snow": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>der", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON><PERSON> sa<PERSON> p<PERSON>der", "block.minecraft.powered_rail": "<PERSON><PERSON>", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "<PERSON><PERSON><PERSON> bata prismarin", "block.minecraft.prismarine_brick_stairs": "Tangga bata prismarin", "block.minecraft.prismarine_bricks": "<PERSON><PERSON>", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.prismarine_stairs": "<PERSON><PERSON> prismarin", "block.minecraft.prismarine_wall": "Tembok prismarin", "block.minecraft.pumpkin": "<PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Batang laboe", "block.minecraft.purple_banner": "Spandoek o<PERSON>", "block.minecraft.purple_bed": "<PERSON><PERSON>", "block.minecraft.purple_candle": "<PERSON><PERSON>", "block.minecraft.purple_candle_cake": "Tar dengan lilin o<PERSON>oe", "block.minecraft.purple_carpet": "<PERSON><PERSON>", "block.minecraft.purple_concrete": "<PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON> o<PERSON>", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_shulker_box": "Kotak shulker oengoe", "block.minecraft.purple_stained_glass": "<PERSON><PERSON>", "block.minecraft.purple_stained_glass_pane": "<PERSON> katja o<PERSON>", "block.minecraft.purple_terracotta": "Térakota o<PERSON>oe", "block.minecraft.purple_wool": "<PERSON><PERSON>", "block.minecraft.purpur_block": "Balok purpur", "block.minecraft.purpur_pillar": "Pilar purpur", "block.minecraft.purpur_slab": "<PERSON><PERSON><PERSON> purpur", "block.minecraft.purpur_stairs": "Tangga purpur", "block.minecraft.quartz_block": "Balok kwars", "block.minecraft.quartz_bricks": "<PERSON><PERSON>", "block.minecraft.quartz_pillar": "<PERSON><PERSON> k<PERSON>s", "block.minecraft.quartz_slab": "<PERSON><PERSON><PERSON> k<PERSON>s", "block.minecraft.quartz_stairs": "Tangga kwars", "block.minecraft.rail": "<PERSON><PERSON>", "block.minecraft.raw_copper_block": "Balok tembaga mentah", "block.minecraft.raw_gold_block": "Balok emas mentah", "block.minecraft.raw_iron_block": "Balok besi mentah", "block.minecraft.red_banner": "Spandoek merah", "block.minecraft.red_bed": "<PERSON><PERSON> merah", "block.minecraft.red_candle": "Lilin merah", "block.minecraft.red_candle_cake": "Tar dengan lilin merah", "block.minecraft.red_carpet": "<PERSON><PERSON> merah", "block.minecraft.red_concrete": "<PERSON><PERSON> merah", "block.minecraft.red_concrete_powder": "Semen merah", "block.minecraft.red_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> merah", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON><PERSON> merah", "block.minecraft.red_mushroom_block": "Balok dja<PERSON>er merah", "block.minecraft.red_nether_brick_slab": "<PERSON><PERSON>t bata nether merah", "block.minecraft.red_nether_brick_stairs": "<PERSON>ga bata nether merah", "block.minecraft.red_nether_brick_wall": "Tembok bata nether merah", "block.minecraft.red_nether_bricks": "<PERSON>a nether merah", "block.minecraft.red_sand": "<PERSON><PERSON> merah", "block.minecraft.red_sandstone": "<PERSON><PERSON> pasir merah", "block.minecraft.red_sandstone_slab": "<PERSON><PERSON>t batoe pasir merah", "block.minecraft.red_sandstone_stairs": "Tangga batoe pasir merah", "block.minecraft.red_sandstone_wall": "Tembok batoe pasir merah", "block.minecraft.red_shulker_box": "Kotak shulker merah", "block.minecraft.red_stained_glass": "<PERSON><PERSON> me<PERSON>", "block.minecraft.red_stained_glass_pane": "Panel katja merah", "block.minecraft.red_terracotta": "Térakota merah", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> doelban merah", "block.minecraft.red_wool": "Wol merah", "block.minecraft.redstone_block": "Balok redstone", "block.minecraft.redstone_lamp": "Lampoe redstone", "block.minecraft.redstone_ore": "Bidjih redstone", "block.minecraft.redstone_torch": "Obor redstone", "block.minecraft.redstone_wall_torch": "<PERSON><PERSON> dinding redstone", "block.minecraft.redstone_wire": "Kabel redstone", "block.minecraft.reinforced_deepslate": "Batoe gelita diper<PERSON>h", "block.minecraft.repeater": "Pengoelang redstone", "block.minecraft.repeating_command_block": "Balok perintah mengoelang", "block.minecraft.resin_block": "Balok damar", "block.minecraft.resin_brick_slab": "<PERSON><PERSON><PERSON> bata damar", "block.minecraft.resin_brick_stairs": "<PERSON>ga bata damar", "block.minecraft.resin_brick_wall": "Tembok bata damar", "block.minecraft.resin_bricks": "Balok bata damar", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON> damar", "block.minecraft.respawn_anchor": "Angker pembangkit", "block.minecraft.rooted_dirt": "<PERSON><PERSON>", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.sand": "Pa<PERSON>", "block.minecraft.sandstone": "<PERSON><PERSON> p<PERSON>r", "block.minecraft.sandstone_slab": "<PERSON><PERSON><PERSON> batoe pasir", "block.minecraft.sandstone_stairs": "Tangga batoe pasir", "block.minecraft.sandstone_wall": "Tembok batoe pasir", "block.minecraft.scaffolding": "<PERSON><PERSON><PERSON>", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Katalisator sculk", "block.minecraft.sculk_sensor": "<PERSON><PERSON><PERSON> sculk", "block.minecraft.sculk_shrieker": "Sculk pendjerit", "block.minecraft.sculk_vein": "<PERSON><PERSON><PERSON> sculk", "block.minecraft.sea_lantern": "<PERSON><PERSON> la<PERSON>", "block.minecraft.sea_pickle": "<PERSON><PERSON> la<PERSON>t", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Titik bangkit ditetapkan", "block.minecraft.short_dry_grass": "<PERSON><PERSON><PERSON> kering pendek", "block.minecraft.short_grass": "<PERSON><PERSON><PERSON> pendek", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "Kotak shulker", "block.minecraft.skeleton_skull": "Tengkorak bengkarak", "block.minecraft.skeleton_wall_skull": "<PERSON><PERSON><PERSON><PERSON> dinding ben<PERSON><PERSON>k", "block.minecraft.slime_block": "Balok geladir", "block.minecraft.small_amethyst_bud": "<PERSON><PERSON><PERSON><PERSON> ketjoeb<PERSON>ng ketjil", "block.minecraft.small_dripleaf": "<PERSON><PERSON>n tetes ketjil", "block.minecraft.smithing_table": "Medja tempa", "block.minecraft.smoker": "Pengasap", "block.minecraft.smooth_basalt": "Basal haloes", "block.minecraft.smooth_quartz": "Balok kwars haloes", "block.minecraft.smooth_quartz_slab": "<PERSON><PERSON><PERSON> kwars haloes", "block.minecraft.smooth_quartz_stairs": "Tangga kwars haloes", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON> pasir merah haloes", "block.minecraft.smooth_red_sandstone_slab": "<PERSON><PERSON>t batoe pasir merah haloes", "block.minecraft.smooth_red_sandstone_stairs": "Tangga batoe pasir merah haloes", "block.minecraft.smooth_sandstone": "<PERSON><PERSON> pasir haloes", "block.minecraft.smooth_sandstone_slab": "<PERSON><PERSON><PERSON> batoe pasir haloes", "block.minecraft.smooth_sandstone_stairs": "Tangga batoe pasir haloes", "block.minecraft.smooth_stone": "<PERSON><PERSON> haloes", "block.minecraft.smooth_stone_slab": "<PERSON><PERSON><PERSON> batoe haloes", "block.minecraft.sniffer_egg": "<PERSON><PERSON><PERSON>gen<PERSON>", "block.minecraft.snow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.snow_block": "<PERSON><PERSON><PERSON>", "block.minecraft.soul_campfire": "<PERSON><PERSON> o<PERSON>wa", "block.minecraft.soul_fire": "<PERSON><PERSON>", "block.minecraft.soul_lantern": "Lentera d<PERSON>wa", "block.minecraft.soul_sand": "<PERSON><PERSON>", "block.minecraft.soul_soil": "<PERSON><PERSON>", "block.minecraft.soul_torch": "<PERSON><PERSON>", "block.minecraft.soul_wall_torch": "<PERSON><PERSON> dinding d<PERSON>wa", "block.minecraft.spawn.not_valid": "<PERSON><PERSON> atau angker pembangkit toean tidak ada atau terhalang", "block.minecraft.spawner": "Pembangkit monster", "block.minecraft.spawner.desc1": "Interaksi dengan teloer pewoedjoedan:", "block.minecraft.spawner.desc2": "<PERSON><PERSON>", "block.minecraft.sponge": "Spons", "block.minecraft.spore_blossom": "Kembang spora", "block.minecraft.spruce_button": "Kenop spar", "block.minecraft.spruce_door": "Pintoe spar", "block.minecraft.spruce_fence": "Pagar spar", "block.minecraft.spruce_fence_gate": "<PERSON><PERSON><PERSON> pagar spar", "block.minecraft.spruce_hanging_sign": "Papan tanda gantoeng spar", "block.minecraft.spruce_leaves": "Dedaoenan spar", "block.minecraft.spruce_log": "Gelondong spar", "block.minecraft.spruce_planks": "Papan spar", "block.minecraft.spruce_pressure_plate": "<PERSON><PERSON>t penekan spar", "block.minecraft.spruce_sapling": "Bibit spar", "block.minecraft.spruce_sign": "Papan tanda spar", "block.minecraft.spruce_slab": "Pelat spar", "block.minecraft.spruce_stairs": "Tangga spar", "block.minecraft.spruce_trapdoor": "Pintoe kolong spar", "block.minecraft.spruce_wall_hanging_sign": "Papan tanda gantoeng dinding spar", "block.minecraft.spruce_wall_sign": "<PERSON>n tanda dinding spar", "block.minecraft.spruce_wood": "<PERSON><PERSON>e spar", "block.minecraft.sticky_piston": "Piston lengket", "block.minecraft.stone": "<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "<PERSON><PERSON><PERSON> bata batoe", "block.minecraft.stone_brick_stairs": "Tangga bata batoe", "block.minecraft.stone_brick_wall": "Tembok bata batoe", "block.minecraft.stone_bricks": "<PERSON>a batoe", "block.minecraft.stone_button": "<PERSON><PERSON> batoe", "block.minecraft.stone_pressure_plate": "<PERSON><PERSON>t penekan batoe", "block.minecraft.stone_slab": "<PERSON><PERSON><PERSON> batoe", "block.minecraft.stone_stairs": "Tangga batoe", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON> batoe", "block.minecraft.stripped_acacia_log": "Gelondong akasia koepas", "block.minecraft.stripped_acacia_wood": "<PERSON><PERSON><PERSON> akasia koe<PERSON>", "block.minecraft.stripped_bamboo_block": "Balok bamboe koepas", "block.minecraft.stripped_birch_log": "Gelondong berk koepas", "block.minecraft.stripped_birch_wood": "<PERSON><PERSON><PERSON> berk koepas", "block.minecraft.stripped_cherry_log": "Gelondong kersen koepas", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON><PERSON> kersen koe<PERSON>", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON> koelat kirmizi koepas", "block.minecraft.stripped_crimson_stem": "Batang kirmizi koepas", "block.minecraft.stripped_dark_oak_log": "Gelondong ek gelap koepas", "block.minecraft.stripped_dark_oak_wood": "<PERSON><PERSON><PERSON> ek gelap koepas", "block.minecraft.stripped_jungle_log": "Gelondong rimba koepas", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON><PERSON> rimba koepas", "block.minecraft.stripped_mangrove_log": "Gelondong bakau koepas", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON><PERSON> bakau koe<PERSON>", "block.minecraft.stripped_oak_log": "Gelondong ek koepas", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON><PERSON> ek koepas", "block.minecraft.stripped_pale_oak_log": "Gelondong ek poet<PERSON>t k<PERSON>", "block.minecraft.stripped_pale_oak_wood": "<PERSON><PERSON><PERSON> ek <PERSON> k<PERSON>", "block.minecraft.stripped_spruce_log": "Gelondong spar koepas", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON>e spar koepas", "block.minecraft.stripped_warped_hyphae": "Benang koelat keroekoet koepas", "block.minecraft.stripped_warped_stem": "Batang keroekoet koepas", "block.minecraft.structure_block": "Balok <PERSON>", "block.minecraft.structure_void": "<PERSON><PERSON>", "block.minecraft.sugar_cane": "<PERSON><PERSON>", "block.minecraft.sunflower": "<PERSON><PERSON><PERSON>", "block.minecraft.suspicious_gravel": "<PERSON><PERSON><PERSON>", "block.minecraft.suspicious_sand": "Pa<PERSON> mentjoerigakan", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON><PERSON> boeni manis", "block.minecraft.tall_dry_grass": "Roempoet kering tinggi", "block.minecraft.tall_grass": "Roe<PERSON>et tinggi", "block.minecraft.tall_seagrass": "<PERSON><PERSON><PERSON> tinggi", "block.minecraft.target": "<PERSON><PERSON><PERSON>", "block.minecraft.terracotta": "Térakota", "block.minecraft.test_block": "Balok oedji", "block.minecraft.test_instance_block": "Balok instansi oedji", "block.minecraft.tinted_glass": "<PERSON><PERSON>", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "Ledakan TNT dimatikan", "block.minecraft.torch": "<PERSON><PERSON>", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON> obor", "block.minecraft.torchflower_crop": "<PERSON><PERSON> boenga obor", "block.minecraft.trapped_chest": "<PERSON><PERSON>", "block.minecraft.trial_spawner": "Pembangkit tjobaän", "block.minecraft.tripwire": "<PERSON><PERSON> se<PERSON>", "block.minecraft.tripwire_hook": "Pen<PERSON><PERSON><PERSON> kawat senggol", "block.minecraft.tube_coral": "<PERSON>ng pipa", "block.minecraft.tube_coral_block": "Balok karang pipa", "block.minecraft.tube_coral_fan": "Ki<PERSON> laoet pipa", "block.minecraft.tube_coral_wall_fan": "<PERSON><PERSON> laoet pipa dinding", "block.minecraft.tuff": "<PERSON>oe aboe", "block.minecraft.tuff_brick_slab": "<PERSON><PERSON><PERSON> bata batoe aboe", "block.minecraft.tuff_brick_stairs": "<PERSON>ga bata batoe aboe", "block.minecraft.tuff_brick_wall": "Tembok bata batoe aboe", "block.minecraft.tuff_bricks": "<PERSON>a batoe aboe", "block.minecraft.tuff_slab": "<PERSON><PERSON><PERSON> batoe aboe", "block.minecraft.tuff_stairs": "Tangga batoe aboe", "block.minecraft.tuff_wall": "Tembok batoe aboe", "block.minecraft.turtle_egg": "<PERSON><PERSON><PERSON>", "block.minecraft.twisting_vines": "<PERSON><PERSON><PERSON> meli<PERSON>", "block.minecraft.twisting_vines_plant": "Tanaman perambat melilit", "block.minecraft.vault": "<PERSON><PERSON><PERSON>", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON> katak <PERSON>", "block.minecraft.vine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.void_air": "<PERSON><PERSON><PERSON> hampa", "block.minecraft.wall_torch": "<PERSON><PERSON> dinding", "block.minecraft.warped_button": "<PERSON><PERSON> kero<PERSON>t", "block.minecraft.warped_door": "<PERSON>ntoe kero<PERSON>t", "block.minecraft.warped_fence": "<PERSON><PERSON> kero<PERSON>t", "block.minecraft.warped_fence_gate": "<PERSON><PERSON>e pagar kero<PERSON>t", "block.minecraft.warped_fungus": "<PERSON><PERSON><PERSON><PERSON> kero<PERSON>", "block.minecraft.warped_hanging_sign": "Papan tanda gantoeng keroekoet", "block.minecraft.warped_hyphae": "Benang koe<PERSON> keroek<PERSON>t", "block.minecraft.warped_nylium": "Dje<PERSON>ing keroekoet", "block.minecraft.warped_planks": "<PERSON>n kero<PERSON>", "block.minecraft.warped_pressure_plate": "Pelat penekan keroek<PERSON>t", "block.minecraft.warped_roots": "<PERSON><PERSON> k<PERSON>", "block.minecraft.warped_sign": "<PERSON>n tanda keroek<PERSON>t", "block.minecraft.warped_slab": "Pelat keroekoet", "block.minecraft.warped_stairs": "Tangga keroekoet", "block.minecraft.warped_stem": "Batang keroekoet", "block.minecraft.warped_trapdoor": "Pintoe kolong keroekoet", "block.minecraft.warped_wall_hanging_sign": "<PERSON>n tanda gantoeng dinding keroekoet", "block.minecraft.warped_wall_sign": "<PERSON><PERSON> tanda dinding keroek<PERSON>t", "block.minecraft.warped_wart_block": "Balok ketoeat keroekoet", "block.minecraft.water": "<PERSON><PERSON><PERSON>", "block.minecraft.water_cauldron": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_chiseled_copper": "Tembaga pahatan berlilin", "block.minecraft.waxed_copper_block": "Balok tembaga berlilin", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON><PERSON> tembaga berlilin", "block.minecraft.waxed_copper_door": "Pintoe tembaga berlilin", "block.minecraft.waxed_copper_grate": "Kisi-kisi tembaga berlilin", "block.minecraft.waxed_copper_trapdoor": "Pintoe kolong tembaga berlilin", "block.minecraft.waxed_cut_copper": "Tembaga potongan berlilin", "block.minecraft.waxed_cut_copper_slab": "P<PERSON>t tembaga potongan berlilin", "block.minecraft.waxed_cut_copper_stairs": "Tangga tembaga potongan berlilin", "block.minecraft.waxed_exposed_chiseled_copper": "Tembaga pahatan terbeber berlilin", "block.minecraft.waxed_exposed_copper": "Tembaga terbeber berlilin", "block.minecraft.waxed_exposed_copper_bulb": "<PERSON><PERSON><PERSON> tembaga terbeber berlilin", "block.minecraft.waxed_exposed_copper_door": "Pintoe tembaga terbeber berlilin", "block.minecraft.waxed_exposed_copper_grate": "Kisi-kisi tembaga terbeber berlilin", "block.minecraft.waxed_exposed_copper_trapdoor": "Pintoe kolong tembaga terbeber berlilin", "block.minecraft.waxed_exposed_cut_copper": "Tembaga potongan terbeber berlilin", "block.minecraft.waxed_exposed_cut_copper_slab": "<PERSON><PERSON>t tembaga potongan terbeber berlilin", "block.minecraft.waxed_exposed_cut_copper_stairs": "Tangga tembaga potongan terbeber berlilin", "block.minecraft.waxed_oxidized_chiseled_copper": "Tembaga pahatan teroksidasi berlilin", "block.minecraft.waxed_oxidized_copper": "Tembaga teroksidasi berlilin", "block.minecraft.waxed_oxidized_copper_bulb": "<PERSON><PERSON><PERSON> tembaga teroksidasi berlilin", "block.minecraft.waxed_oxidized_copper_door": "Pintoe tembaga teroksidasi berlilin", "block.minecraft.waxed_oxidized_copper_grate": "Kisi-kisi tembaga teroksidasi berlilin", "block.minecraft.waxed_oxidized_copper_trapdoor": "Pintoe kolong tembaga teroksidasi berlilin", "block.minecraft.waxed_oxidized_cut_copper": "Tembaga potongan teroksidasi berlilin", "block.minecraft.waxed_oxidized_cut_copper_slab": "Pelat tembaga potongan teroksidasi berlilin", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Tangga tembaga potongan teroksidasi berlilin", "block.minecraft.waxed_weathered_chiseled_copper": "Tembaga pahatan berkarat berlilin", "block.minecraft.waxed_weathered_copper": "Tembaga berkarat berl<PERSON>n", "block.minecraft.waxed_weathered_copper_bulb": "<PERSON><PERSON><PERSON> tembaga berkarat berl<PERSON>n", "block.minecraft.waxed_weathered_copper_door": "Pintoe tembaga berkarat berlilin", "block.minecraft.waxed_weathered_copper_grate": "Kisi-kisi tembaga berkarat berlilin", "block.minecraft.waxed_weathered_copper_trapdoor": "Pintoe kolong tembaga berkarat berlilin", "block.minecraft.waxed_weathered_cut_copper": "<PERSON><PERSON><PERSON> potongan berkarat berlilin", "block.minecraft.waxed_weathered_cut_copper_slab": "<PERSON><PERSON><PERSON> tembaga potongan berkarat berlilin", "block.minecraft.waxed_weathered_cut_copper_stairs": "Tangga tembaga potongan berkarat berlilin", "block.minecraft.weathered_chiseled_copper": "Tembaga pahatan berkarat", "block.minecraft.weathered_copper": "Tembaga berkarat", "block.minecraft.weathered_copper_bulb": "<PERSON><PERSON><PERSON> tembaga berkarat", "block.minecraft.weathered_copper_door": "Pintoe tembaga berkarat", "block.minecraft.weathered_copper_grate": "Kisi-kisi tembaga berkarat", "block.minecraft.weathered_copper_trapdoor": "Pintoe kolong tembaga berkarat", "block.minecraft.weathered_cut_copper": "<PERSON><PERSON><PERSON> potongan berkarat", "block.minecraft.weathered_cut_copper_slab": "<PERSON><PERSON>t tembaga potongan berkarat", "block.minecraft.weathered_cut_copper_stairs": "Tangga tembaga potongan berkarat", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON>", "block.minecraft.weeping_vines_plant": "Tanaman per<PERSON> be<PERSON>", "block.minecraft.wet_sponge": "Spons basah", "block.minecraft.wheat": "Gandoem", "block.minecraft.white_banner": "Spandoek poetih", "block.minecraft.white_bed": "<PERSON><PERSON>", "block.minecraft.white_candle": "<PERSON><PERSON>", "block.minecraft.white_candle_cake": "Tar dengan lilin poetih", "block.minecraft.white_carpet": "<PERSON><PERSON>", "block.minecraft.white_concrete": "<PERSON><PERSON> poetih", "block.minecraft.white_concrete_powder": "<PERSON><PERSON> poetih", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> poet<PERSON>", "block.minecraft.white_shulker_box": "Kotak shulker poetih", "block.minecraft.white_stained_glass": "<PERSON><PERSON>", "block.minecraft.white_stained_glass_pane": "Panel katja poetih", "block.minecraft.white_terracotta": "Térakota poetih", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON>ban poetih", "block.minecraft.white_wool": "<PERSON><PERSON>", "block.minecraft.wildflowers": "<PERSON><PERSON><PERSON> liar", "block.minecraft.wither_rose": "<PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Tengkorak beng<PERSON>", "block.minecraft.wither_skeleton_wall_skull": "<PERSON><PERSON><PERSON><PERSON> dinding <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.yellow_banner": "Spandoek koening", "block.minecraft.yellow_bed": "<PERSON><PERSON> k<PERSON>", "block.minecraft.yellow_candle": "<PERSON><PERSON> koe<PERSON>", "block.minecraft.yellow_candle_cake": "Tar dengan lilin koening", "block.minecraft.yellow_carpet": "<PERSON><PERSON> k<PERSON>", "block.minecraft.yellow_concrete": "<PERSON><PERSON> koening", "block.minecraft.yellow_concrete_powder": "Semen koening", "block.minecraft.yellow_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> koe<PERSON>", "block.minecraft.yellow_shulker_box": "Kotak shulker koening", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON> k<PERSON>", "block.minecraft.yellow_stained_glass_pane": "Panel katja koening", "block.minecraft.yellow_terracotta": "Térakota koening", "block.minecraft.yellow_wool": "Wol koening", "block.minecraft.zombie_head": "<PERSON><PERSON><PERSON>i", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON> dinding zombi", "book.byAuthor": "oleh %1$s", "book.edit.title": "<PERSON><PERSON>", "book.editTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> boe<PERSON>:", "book.finalizeButton": "<PERSON><PERSON><PERSON> dan toetoep", "book.finalizeWarning": "Perhatian! <PERSON><PERSON>, boekoe ini ta' bisa dioebah lagi.", "book.generation.0": "Orisinil", "book.generation.1": "Salinan", "book.generation.2": "Salinan dari salinan", "book.generation.3": "Sobek", "book.invalid.tag": "* Etikét boekoe tidak sah *", "book.pageIndicator": "Pagina %1$s dari %2$s", "book.page_button.next": "<PERSON><PERSON><PERSON>", "book.page_button.previous": "<PERSON><PERSON><PERSON>", "book.sign.title": "Layar T<PERSON>", "book.sign.titlebox": "<PERSON><PERSON><PERSON>", "book.signButton": "Téken", "book.view.title": "<PERSON>ar <PERSON>", "build.tooHigh": "Batas tinggi pembangoenan adalah %s", "chat.cannotSend": "Tidak bisa mengirim pesan obrolan", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Pentjet oentoek berteleportir", "chat.copy": "<PERSON><PERSON> kepapan-klip", "chat.copy.click": "Pentjet oentoek menjalin kepapan-klip", "chat.deleted_marker": "Pesan obrolan ini telah dihapoes oleh peladen.", "chat.disabled.chain_broken": "Obrolan dimatikan sebab rantai poetoes. <PERSON>hon tjoba samboengkan kembali.", "chat.disabled.expiredProfileKey": "Obrolan dimatikan sebab koentji oemoem profil habis tempo. Mohon tjoba samboengkan kembali.", "chat.disabled.invalid_command_signature": "<PERSON>intah poenja tékenan peoebah ta' diharapkan atau hilang", "chat.disabled.invalid_signature": "Obrolan poenja tékenan tidak sah. Mohon tjoba samboengkan kembali.", "chat.disabled.launcher": "Obrolan dimatikan oleh pen<PERSON>elan peloentjoer. Tidak bisa mengirim pesan.", "chat.disabled.missingProfileKey": "Obrolan dimatikan sebab koentji oemoem profil tidak ada. Mohon tjoba samboengkan kembali.", "chat.disabled.options": "Obrolan dimatikan diopsi kliën.", "chat.disabled.out_of_order_chat": "Obrolan jang diterima ta' teroeroet. Apakah waktoe sistem toean dioebah?", "chat.disabled.profile": "Obrolan tidak diïzinkan oleh penjetelan akoen. Tekan „%s” lagi ontoek informasi lebih landjoet.", "chat.disabled.profile.moreInfo": "Obrolan tidak diïzinkan oleh penjetelan akoen. Tidak bisa mengirim atau melihat pesan.", "chat.editBox": "obrolan", "chat.filtered": "Disaring oleh peladen.", "chat.filtered_full": "Peladen telah men<PERSON>mboenjikan pesan toean dari beberapa pemaïn.", "chat.link.confirm": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin memboeka laman berikoet?", "chat.link.confirmTrusted": "<PERSON><PERSON><PERSON><PERSON> toean ingin memboeka taoetan atau menjalin kepapan-klip?", "chat.link.open": "Boeka diperamban", "chat.link.warning": "<PERSON>jangan pernah memboeka taoetan dari orang jang toean tidak pertjaja!", "chat.queue": "[+%s baris tertoenda]", "chat.square_brackets": "[%s]", "chat.tag.error": "Peladen mengirim pesan tidak sah.", "chat.tag.modified": "Pesan telah dioebah oleh peladen. Pesan orisinil:", "chat.tag.not_secure": "Pesan beloem diverifikasi. Tidak bisa dilaporkan.", "chat.tag.system": "Pesan peladen. Tidak bisa dilaporkan.", "chat.tag.system_single_player": "<PERSON><PERSON> peladen.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s telah menjelesaikan tantangan %s", "chat.type.advancement.goal": "%s telah mentjapai sasaran %s", "chat.type.advancement.task": "%s telah me<PERSON><PERSON><PERSON> kema<PERSON>ean %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Pesan regoe", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s berkata %s", "chat.validation_error": "Validasi obrolan gagal", "chat_screen.message": "Pesan oentoek dikirim: %s", "chat_screen.title": "<PERSON><PERSON> o<PERSON>", "chat_screen.usage": "Masoekkan pesan dan tekan Enter oentoek mengirim", "chunk.toast.checkLog": "<PERSON>hat tjatatan oentoek perintjian landjoet", "chunk.toast.loadFailure": "Bingkah pada %s gagal dimoeat", "chunk.toast.lowDiskSpace": "Penjimpanan hampir penoeh!", "chunk.toast.lowDiskSpace.description": "Moengkin tidak bisa menjimpan doenia.", "chunk.toast.saveFailure": "Bingkah pada %s gagal disimpan", "clear.failed.multiple": "Tidak ada benda jang ditemoekan pada %s pemaïn", "clear.failed.single": "Tidak ada benda jang ditemoekan pada pemaïn %s", "color.minecraft.black": "<PERSON><PERSON>", "color.minecraft.blue": "B<PERSON><PERSON>", "color.minecraft.brown": "<PERSON><PERSON>", "color.minecraft.cyan": "Nilangsoeka", "color.minecraft.gray": "<PERSON><PERSON><PERSON>", "color.minecraft.green": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "<PERSON><PERSON><PERSON> moeda", "color.minecraft.light_gray": "<PERSON><PERSON><PERSON> moeda", "color.minecraft.lime": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "color.minecraft.magenta": "<PERSON><PERSON>", "color.minecraft.orange": "<PERSON><PERSON><PERSON>", "color.minecraft.pink": "Djambon", "color.minecraft.purple": "<PERSON><PERSON><PERSON>", "color.minecraft.red": "<PERSON><PERSON>", "color.minecraft.white": "<PERSON><PERSON>", "color.minecraft.yellow": "<PERSON><PERSON>", "command.context.here": "<--[DISINI]", "command.context.parse_error": "%s diposisi %s: %s", "command.exception": "Perintah tidak bisa dioeraikan: %s", "command.expected.separator": "Mengharapkan spasi mengachiri satoe peoebah, tetapi data berekor ditemoekan", "command.failed": "<PERSON><PERSON><PERSON><PERSON> kesalahan sa'at mentjoba mendjalankan perintah itoe", "command.forkLimit": "<PERSON><PERSON><PERSON><PERSON> maksimal konte<PERSON> (%s) tertjapai", "command.unknown.argument": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>ah salah", "command.unknown.command": "<PERSON><PERSON>ah tidak dikenal atau tidak leng<PERSON>p, lihat kesalahan dibawah", "commands.advancement.criterionNotFound": "Kemadjoean %1$s tidak mengandoeng patokan „%2$s”", "commands.advancement.grant.criterion.to.many.failure": "Tidak bisa memberikan patokan „%s” dari kema<PERSON> %s sebab soedah dipoenjaï oleh %s pemaïn", "commands.advancement.grant.criterion.to.many.success": "Patokan „%s” dari kema<PERSON> %s diberikan kepada %s pemaïn", "commands.advancement.grant.criterion.to.one.failure": "Tidak bisa memberikan patokan „%s” dari kema<PERSON> %s sebab soedah dipoenjaï oleh %s", "commands.advancement.grant.criterion.to.one.success": "Patokan „%s” dari kema<PERSON> %s diberikan kepada %s", "commands.advancement.grant.many.to.many.failure": "Tidak bisa memberikan %s kemadjoean kepada %s pema<PERSON>n sebab so<PERSON>h dip<PERSON>", "commands.advancement.grant.many.to.many.success": "%s kemadjoean diberikan kepada %s pemaïn", "commands.advancement.grant.many.to.one.failure": "Tidak bisa memberikan %s kemadjoean kepada %s pema<PERSON>n sebab so<PERSON>h dip<PERSON>", "commands.advancement.grant.many.to.one.success": "%s kemadjoean diberikan kepada %s", "commands.advancement.grant.one.to.many.failure": "Tidak bisa memberikan kema<PERSON> „%s” kepada %s pema<PERSON>n sebab soedah dipoenjaï", "commands.advancement.grant.one.to.many.success": "Kemadjoean %s diberikan kepada %s pemaïn", "commands.advancement.grant.one.to.one.failure": "Tidak bisa memberikan kemadjoean %s kepada %s sebab so<PERSON>h dip<PERSON>", "commands.advancement.grant.one.to.one.success": "Kemadjoean %s diberikan kepada %s", "commands.advancement.revoke.criterion.to.many.failure": "Tidak bisa mentjaboet patokan „%s” dari k<PERSON> %s sebab tidak dimiliki oleh %s pemaïn", "commands.advancement.revoke.criterion.to.many.success": "Patokan „%s” dari kemadjoean %s ditjaboet dari %s pemaïn", "commands.advancement.revoke.criterion.to.one.failure": "Tidak bisa mentjaboet patokan „%s” dari kemad<PERSON> %s sebab tidak dimiliki", "commands.advancement.revoke.criterion.to.one.success": "Patokan „%s” dari kemadjoean %s ditjaboet dari %s", "commands.advancement.revoke.many.to.many.failure": "Tidak bisa mentjaboet %s kemadjoean dari %s pema<PERSON>n sebab tidak dimiliki", "commands.advancement.revoke.many.to.many.success": "%s kemad<PERSON><PERSON> ditjaboet dari %s pemaïn", "commands.advancement.revoke.many.to.one.failure": "Tidak bisa mentjaboet %s kemadjoean dari %s sebab tidak dimiliki", "commands.advancement.revoke.many.to.one.success": "%s kemadjoean dari %s ditjaboet", "commands.advancement.revoke.one.to.many.failure": "Tidak bisa mentjaboet kemadjoean %s dari %s pema<PERSON>n sebab tidak dimiliki", "commands.advancement.revoke.one.to.many.success": "Kemadjoean %s ditjaboet dari %s pemaïn", "commands.advancement.revoke.one.to.one.failure": "Tidak bisa mentjaboet kemadjoean %s dari %s sebab tidak dimiliki", "commands.advancement.revoke.one.to.one.success": "Kemadjoean %s dari %s ditjaboet", "commands.attribute.base_value.get.success": "<PERSON><PERSON> dasar dari ketjakapan %s oentoek entitet %s adalah %s", "commands.attribute.base_value.reset.success": "<PERSON><PERSON> dasar dari ketjakapan %s oentoek entitet %s disetel oelang mendjadi %s", "commands.attribute.base_value.set.success": "<PERSON><PERSON> dasar dari ketjakapan %s oentoek entitet %s disetel mendjadi %s", "commands.attribute.failed.entity": "%s boekan entitet sah oentoek perintah ini", "commands.attribute.failed.modifier_already_present": "Pengoebah %s soedah ada pada ketjakapan %s oentoek entitet %s", "commands.attribute.failed.no_attribute": "Entitet %s tidak poenja ketjakapan %s", "commands.attribute.failed.no_modifier": "Ketjakapan %s oentoek entitet %s tidak poenja pengoebah %s", "commands.attribute.modifier.add.success": "Pengoebah %s pada ketjakapan %s oentoek entitet %s telah ditambahkan", "commands.attribute.modifier.remove.success": "Pengoebah %s pada ketjakapan %s oentoek entitet %s telah di<PERSON>es", "commands.attribute.modifier.value.get.success": "<PERSON>lai dari pengoebah %s pada ketjakapan %s oentoek entitet %s adalah %s", "commands.attribute.value.get.success": "Nilai dari ketjakapan %s oentoek entitet %s adalah %s", "commands.ban.failed": "<PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> te<PERSON>", "commands.ban.success": "%s diblokir: %s", "commands.banip.failed": "<PERSON><PERSON><PERSON>, alamat IP itoe soedah diblokir", "commands.banip.info": "Pemblokiran ini memengaroehi %s pemaïn: %s", "commands.banip.invalid": "Alamat IP tidak sah atau pemaïn tidak dikenal", "commands.banip.success": "Alamat IP %s diblokir: %s", "commands.banlist.entry": "%s telah diblokir oleh %s: %s", "commands.banlist.entry.unknown": "(Ta<PERSON> <PERSON>ketah<PERSON><PERSON>)", "commands.banlist.list": "Ada %s pemblokiran:", "commands.banlist.none": "Tidak ada pemblokiran", "commands.bossbar.create.failed": "<PERSON><PERSON>h soear dengan pengenal „%s” soedah ada", "commands.bossbar.create.success": "Bilah soear oebah-soeaian %s telah diboeat", "commands.bossbar.get.max": "<PERSON><PERSON> maksimal bilah soear oebah-soeaian %s adalah %s", "commands.bossbar.get.players.none": "Bilah soear oebah-soeaian %s tidak poenja pemaïn dalam djar<PERSON>n", "commands.bossbar.get.players.some": "Bilah soear oebah-soeaian %s poenja %s pemaïn dalam djaringa<PERSON>: %s", "commands.bossbar.get.value": "Bilah soear oebah-soeaian %s poenja nilai sebesar %s", "commands.bossbar.get.visible.hidden": "Bilah soear oebah-soeaian %s sa'at ini tersemboenji", "commands.bossbar.get.visible.visible": "Bilah soear oebah-soeaian %s sa'at ini tampak", "commands.bossbar.list.bars.none": "Tidak ada bilah soear oebah-soeaian jang aktif", "commands.bossbar.list.bars.some": "Ada %s bilah soear oebah-soeaian jang aktif: %s", "commands.bossbar.remove.success": "B<PERSON>h soear oebah-soeaian %s telah dihapoes", "commands.bossbar.set.color.success": "Warna bilah soear oebah-soeaian %s telah dioebah", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON><PERSON>, warna itoe soedah mendjadi warna bilah soear", "commands.bossbar.set.max.success": "<PERSON><PERSON> maksimal bilah soear oebah-soeaian %s telah dioebah mendjadi %s", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON><PERSON>, nilai itoe soedah mendjadi nilai maksimal bilah soear", "commands.bossbar.set.name.success": "Nama bilah soear oebah-soeaian %s telah dioebah", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON><PERSON>, nama itoe soedah mendjadi nama bilah soear", "commands.bossbar.set.players.success.none": "Bilah soear oebah-soeaian %s so<PERSON>h tidak ada pema<PERSON>", "commands.bossbar.set.players.success.some": "Bilah soear oebah-soeaian %s sekarang ada %s pemaïn: %s", "commands.bossbar.set.players.unchanged": "<PERSON><PERSON><PERSON>, p<PERSON><PERSON>n itoe soedah pada bilah soear tanpa siapa poen ditambah atau dihapoes", "commands.bossbar.set.style.success": "Gaja bilah soear oebah-soeaian %s telah dioebah", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON><PERSON>, gaja itoe soedah mendjadi gaja bilah soear", "commands.bossbar.set.value.success": "Nilai bilah soear oebah-soeaian %s telah dioebah mendjadi %s", "commands.bossbar.set.value.unchanged": "<PERSON><PERSON><PERSON>, nilai itoe soedah mendjadi nilai bilah soear", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON><PERSON>, bilah soear so<PERSON>h terse<PERSON><PERSON>ji", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON><PERSON> be<PERSON>, bilah soear soedah tampak", "commands.bossbar.set.visible.success.hidden": "Bilah soear oebah-soeaian %s disemboenjikan", "commands.bossbar.set.visible.success.visible": "Bilah soear oebah-soeaian %s ditampakkan", "commands.bossbar.unknown": "B<PERSON>h soear dengan pengenal „%s” tidak ada", "commands.clear.success.multiple": "%s benda dari %s pema<PERSON><PERSON> te<PERSON>", "commands.clear.success.single": "%s benda dari pemaïn %s telah di<PERSON>", "commands.clear.test.multiple": "%s benda jang tjotjok ditemoekan pada %s pemaïn", "commands.clear.test.single": "%s benda jang tjot<PERSON>k ditemoekan pada pemaïn %s", "commands.clone.failed": "Tidak ada balok jang digandakan", "commands.clone.overlap": "<PERSON><PERSON><PERSON> asal dan toed<PERSON>ean tidak boleh toempang-tindih", "commands.clone.success": "%s balok ber<PERSON>il digandakan", "commands.clone.toobig": "Terlaloe banjak balok diareal itoe (maksimal %s, ditentoekan %s)", "commands.damage.invulnerable": "<PERSON><PERSON><PERSON> kebal terhadap djenis serangan jang di<PERSON>ikan", "commands.damage.success": "Serangan sebesar %s diberikan pada %s", "commands.data.block.get": "%s pada balok %s, %s, %s setelah faktor skala %s adalah %s", "commands.data.block.invalid": "Balok sasaran bukan soeatoe entitet balok", "commands.data.block.modified": "Data balok pada %s, %s, %s telah dioebah", "commands.data.block.query": "%s, %s, %s poenja data balok sebagai berikoet: %s", "commands.data.entity.get": "%s pada %s setelah faktor skala %s adalah %s", "commands.data.entity.invalid": "Tidak bisa mengoebah data pemaïn", "commands.data.entity.modified": "Data entitet %s telah dioebah", "commands.data.entity.query": "%s poenja data entitet sebagai berikoet: %s", "commands.data.get.invalid": "Tidak bisa memperoleh %s, hanja étikét angka jang diïzinkan", "commands.data.get.multiple": "<PERSON><PERSON>eb<PERSON> ini menerima nilai toe<PERSON> bin<PERSON><PERSON> be<PERSON>", "commands.data.get.unknown": "Tidak bisa memperoleh %s, étikét tidak ada", "commands.data.merge.failed": "<PERSON><PERSON><PERSON>, si<PERSON>t jang ditentoekan soedah poenja nilai² ini", "commands.data.modify.expected_list": "<PERSON><PERSON><PERSON>, didapati: %s", "commands.data.modify.expected_object": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>: %s", "commands.data.modify.expected_value": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>: %s", "commands.data.modify.invalid_index": "Indeks daftar tidak sah: %s", "commands.data.modify.invalid_substring": "Indéks oentaian bawah tidak sah: %s hingga %s", "commands.data.storage.get": "%s dalam penjimpanan %s set<PERSON>h faktor skala %s adalah %s", "commands.data.storage.modified": "Penjimpanan %s dimodifikasi", "commands.data.storage.query": "Penjimpanan %s poenja isi sebagai berikoet: %s", "commands.datapack.create.already_exists": "<PERSON><PERSON> dengan nama „%s” so<PERSON>h ada", "commands.datapack.create.invalid_full_name": "<PERSON><PERSON> paket baroe „%s” tidak sah", "commands.datapack.create.invalid_name": "<PERSON><PERSON><PERSON> ta' sah „%s” pada nama paket baroe", "commands.datapack.create.io_failure": "Tidak bisa memboeat paket dengan nama „%s”, si<PERSON><PERSON> <PERSON><PERSON><PERSON>n", "commands.datapack.create.metadata_encode_failure": "Metadata oentoek paket dengan nama „%s” gagal dikodekan: %s", "commands.datapack.create.success": "Paket kosong baroe dengan nama „%s” telah diboeat", "commands.datapack.disable.failed": "Paket „%s” tidak njala!", "commands.datapack.disable.failed.feature": "Paket „%s” tidak bisa dimatikan, sebab itoe adalah bagian dari bendera njala!", "commands.datapack.enable.failed": "Paket „%s” so<PERSON>h njala!", "commands.datapack.enable.failed.no_flags": "Paket „%s” tidak bisa <PERSON>, sebab bendera jang diperloekan tidak njala didoenia ini: %s!", "commands.datapack.list.available.none": "<PERSON><PERSON>h tidak ada paket data tersedia", "commands.datapack.list.available.success": "Ada %s paket data tersedia: %s", "commands.datapack.list.enabled.none": "Tidak ada paket data jang njala", "commands.datapack.list.enabled.success": "Ada %s paket data jang njala: %s", "commands.datapack.modify.disable": "Mematikan paket data %s", "commands.datapack.modify.enable": "Men<PERSON>lakan paket data %s", "commands.datapack.unknown": "Paket data „%s” tidak dikenal", "commands.debug.alreadyRunning": "<PERSON><PERSON><PERSON><PERSON> tik so<PERSON>h dim<PERSON>", "commands.debug.function.noRecursion": "Tidak bisa melatjak dari dalam roetin", "commands.debug.function.noReturnRun": "<PERSON><PERSON><PERSON><PERSON><PERSON> tidak bisa dipakai dengan /return run", "commands.debug.function.success.multiple": "%s perintah dari %s roetin dilatjak kekeloearan berkas %s", "commands.debug.function.success.single": "%s perintah dari roetin „%s” dilatjak kekeloearan berkas %s", "commands.debug.function.traceFailed": "<PERSON><PERSON> gagal di<PERSON>", "commands.debug.notRunning": "<PERSON>cht<PERSON><PERSON> tik beloem dimoe<PERSON>", "commands.debug.started": "<PERSON><PERSON><PERSON><PERSON><PERSON> tik <PERSON>", "commands.debug.stopped": "Pengichtisaran tik dihentikan setelah %s detik dan %s tik (%s tik per detik)", "commands.defaultgamemode.success": "Ragam permaïnan asali sekarang adalah %s", "commands.deop.failed": "<PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> boe<PERSON> pengelola", "commands.deop.success": "%s soedah boekan pengelola peladen", "commands.dialog.clear.multiple": "Dialog dibersihkan oentoek %s pemaïn", "commands.dialog.clear.single": "Dialog dibersihkan oentoek %s", "commands.dialog.show.multiple": "Dialog ditampilkan kepada %s pemaïn", "commands.dialog.show.single": "Dialog ditampilkan kepada %s", "commands.difficulty.failure": "Tidak beroebah. Kesoelitan doenia ini soedah disetel sebagai %s", "commands.difficulty.query": "Kesoelitan doenia sa'at ini adalah %s", "commands.difficulty.success": "Kesoelitan doenia dioebah mendjadi %s", "commands.drop.no_held_items": "Entitet tidak bisa memegang benda apa poen", "commands.drop.no_loot_table": "Entitet %s tidak poenja tabel djarah", "commands.drop.no_loot_table.block": "Balok %s tidak poenja tabel djarah", "commands.drop.success.multiple": "%s <PERSON>a <PERSON>", "commands.drop.success.multiple_with_table": "%s benda dari tabel djarah %s <PERSON><PERSON><PERSON><PERSON><PERSON>", "commands.drop.success.single": "%s %s didjatoehkan", "commands.drop.success.single_with_table": "%s %s dari tabel djarah %s <PERSON><PERSON><PERSON><PERSON><PERSON>", "commands.effect.clear.everything.failed": "Sasaran tidak poenja efek jang bisa di<PERSON>poes", "commands.effect.clear.everything.success.multiple": "Semoea efek pada %s sasaran di<PERSON>", "commands.effect.clear.everything.success.single": "Semoea efek pada %s dihapoes", "commands.effect.clear.specific.failed": "Sasaran tidak poenja efek jang diminta", "commands.effect.clear.specific.success.multiple": "Efek %s pada %s sasaran di<PERSON>", "commands.effect.clear.specific.success.single": "Efek %s pada %s dihapoes", "commands.effect.give.failed": "Tidak bisa menerapkan efek ini (sasaran kebal terhadap efek atau poenja efek jang lebih koeat)", "commands.effect.give.success.multiple": "Efek %s diterapkan pada %s sasaran", "commands.effect.give.success.single": "Efek %s diterapkan pada %s", "commands.enchant.failed": "<PERSON><PERSON><PERSON>, sasaran tidak poenja benda apa poen ditangan atau pesona tidak bisa diterapkan", "commands.enchant.failed.entity": "%s boekan entitet jang sah oentoek perintah ini", "commands.enchant.failed.incompatible": "%s tidak mendoekoeng pesona itoe", "commands.enchant.failed.itemless": "%s tidak memegang benda apa poen", "commands.enchant.failed.level": "%s lebih besar dari tingkat maksimal %s jang did<PERSON><PERSON> oleh pesona itoe", "commands.enchant.success.multiple": "Pesona %s diterapkan kepada %s entitet", "commands.enchant.success.single": "Pesona %s diterapkan pada benda milik %s", "commands.execute.blocks.toobig": "Terlaloe banjak balok diareal itoe (maksimal %s, ditentoekan %s)", "commands.execute.conditional.fail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gagal", "commands.execute.conditional.fail_count": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON>: %s", "commands.execute.conditional.pass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commands.execute.conditional.pass_count": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON>: %s", "commands.execute.function.instantiationFailure": "Roetin %s gagal diboeat: %s", "commands.experience.add.levels.success.multiple": "Tingkat pengalaman sedjoemlah %s diberikan kepada %s pemaïn", "commands.experience.add.levels.success.single": "Tingkat pengalaman sedjoemlah %s diberikan kepada %s", "commands.experience.add.points.success.multiple": "<PERSON><PERSON> sed<PERSON><PERSON>lah %s diberikan kepada %s pemaïn", "commands.experience.add.points.success.single": "<PERSON><PERSON> sed<PERSON><PERSON>lah %s diberikan kepada %s", "commands.experience.query.levels": "%s poenja tingkat pengalaman sedjoemlah %s", "commands.experience.query.points": "%s poenja nilai pengalaman sedjoemlah %s", "commands.experience.set.levels.success.multiple": "Tingkat pengalaman sedjoemlah %s disetel pada %s pemaïn", "commands.experience.set.levels.success.single": "Tingkat pengalaman sedjoemlah %s disetel pada %s", "commands.experience.set.points.invalid": "Tidak bisa menjetel nilai pengalaman lebih dari nilai maksimal oentoek tingkat pemaïn sa'at ini", "commands.experience.set.points.success.multiple": "<PERSON><PERSON> sed<PERSON><PERSON>lah %s disetel pada %s pemaïn", "commands.experience.set.points.success.single": "<PERSON><PERSON> sed<PERSON><PERSON> %s disetel pada %s", "commands.fill.failed": "Balok tidak terisi", "commands.fill.success": "%s balok be<PERSON><PERSON><PERSON> di<PERSON>si", "commands.fill.toobig": "Terlaloe banjak balok diareal itoe (maksimal %s, ditentoekan %s)", "commands.fillbiome.success": "Bioom disetel antara %s, %s, %s dan %s, %s, %s", "commands.fillbiome.success.count": "%s boetir bioom disetel antara %s, %s, %s dan %s, %s, %s", "commands.fillbiome.toobig": "Terlaloe banjak balok dalam roeang jang ditentoekan (maksimal %s, ditentoekan %s)", "commands.forceload.added.failure": "Bingkah tidak bisa dimoeat paksa", "commands.forceload.added.multiple": "%s bingkah dalam %s dari %s hingga %s dimoeat paksa", "commands.forceload.added.none": "Tidak ada bingkah moeat-paksa jang ditemoekan dalam %s", "commands.forceload.added.single": "Bingkah %s dalam %s dimoeat paksa", "commands.forceload.list.multiple": "%s bingkah moeat-paksa ditemoekan dalam %s pada: %s", "commands.forceload.list.single": "Bingkah moeat-paksa ditemoekan dalam %s pada: %s", "commands.forceload.query.failure": "Bingkah pada %s dalam %s tidak bisa dimoeat paksa", "commands.forceload.query.success": "Bingkah pada %s dalam %s dimoeat paksa", "commands.forceload.removed.all": "<PERSON><PERSON><PERSON> %s so<PERSON>h tidak dimoeat paksa", "commands.forceload.removed.failure": "Tidak ada bingkah jang soedah tidak dimoeat paksa", "commands.forceload.removed.multiple": "%s bingkah da<PERSON> %s dari %s hingga %s soedah tidak dimoeat paksa", "commands.forceload.removed.single": "Bingkah %s dalam %s soedah tidak dimoeat paksa", "commands.forceload.toobig": "Terlaloe banjak bingkah diareal itoe (maksimal %s, ditentoekan %s)", "commands.function.error.argument_not_compound": "<PERSON><PERSON><PERSON> peoebah tidak sah: %s, COMPOUND diharapkan", "commands.function.error.missing_argument": "Peoebah hilang %2$s oentoek roetin %1$s", "commands.function.error.missing_arguments": "Peoebah hilang oentoek roetin %s", "commands.function.error.parse": "Sa'at memboeat makroperintah %s, perintah „%s” menjebabkan kesalahan: %s", "commands.function.instantiationFailure": "Roetin %s gagal diboeat: %s", "commands.function.result": "Roetin %s mengembalikan %s", "commands.function.scheduled.multiple": "Mendjalankan roetin %s", "commands.function.scheduled.no_functions": "Tidak bisa menemoekan roetin apa poen oentoek nama %s", "commands.function.scheduled.single": "Mendjalankan roetin %s", "commands.function.success.multiple": "%s perintah dari %s roetin <PERSON>", "commands.function.success.multiple.result": "%s roe<PERSON>", "commands.function.success.single": "%s perintah dari roetin „%s” <PERSON><PERSON><PERSON><PERSON>", "commands.function.success.single.result": "Roetin „%2$s” mengembalikan %1$s", "commands.gamemode.success.other": "Ragam permaïnan %s disetel mendjadi %s", "commands.gamemode.success.self": "Ragam permaïnan toean disetel mendjadi %s", "commands.gamerule.query": "Atoeran permaïnan %s sa'at ini disetel sebagai: %s", "commands.gamerule.set": "Atoeran permaïnan %s telah disetel mendjadi: %s", "commands.give.failed.toomanyitems": "Tidak bisa memberi %s lebih dari %s", "commands.give.success.multiple": "Memberikan %s %s kepada %s pemaïn", "commands.give.success.single": "%s %s diberikan kepada %s", "commands.help.failed": "<PERSON><PERSON><PERSON>' dikenal atau izin tidak memada<PERSON>", "commands.item.block.set.success": "Petak pada %s, %s, %s diganti dengan %s", "commands.item.entity.set.success.multiple": "Soeatoe petak pada %s entitet diganti dengan %s", "commands.item.entity.set.success.single": "Soeatoe petak pada %s diganti dengan %s", "commands.item.source.no_such_slot": "Soember tidak poenja petak %s", "commands.item.source.not_a_container": "Posisi soember %s, %s, %s boekan meroepakan wadah", "commands.item.target.no_changed.known_item": "Benda %s kepetak %s tidak diterima oleh sasaran apa poen", "commands.item.target.no_changes": "Benda kepetak %s tidak diterima oleh sasaran apa poen", "commands.item.target.no_such_slot": "Sasaran tidak poenja petak %s", "commands.item.target.not_a_container": "Posisi sasaran %s, %s, %s boekan meroepakan wadah", "commands.jfr.dump.failed": "Rekaman JFR gagal diboeang: %s", "commands.jfr.start.failed": "Pemboeatan profil JFR gagal dimoelai", "commands.jfr.started": "Pemboeatan profil JFR dimoelai", "commands.jfr.stopped": "Pemboeatan profil JFR dihentikan dan diboeang ke-%s", "commands.kick.owner.failed": "Tidak bisa mengeloearkan pemilik peladen dari permaïnan djaringan areal lokal", "commands.kick.singleplayer.failed": "Tidak bisa mengel<PERSON><PERSON>an pemaïn dalam permaïnan loear djar<PERSON>n", "commands.kick.success": "%s dikeloearkan: %s", "commands.kill.success.multiple": "%s entitet diboenoeh", "commands.kill.success.single": "%s diboenoeh", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Ada %s dari maks<PERSON> %s p<PERSON><PERSON><PERSON> da<PERSON> djar<PERSON>: %s", "commands.locate.biome.not_found": "Bioom berdjenis „%s” tidak bisa ditemoekan dalam djarak jang wadjar", "commands.locate.biome.success": "%s terdekat berada pada %s (sedjaoeh %s balok)", "commands.locate.poi.not_found": "Titik per<PERSON><PERSON> be<PERSON> „%s” tidak bisa ditemoekan dalam djarak jang wadjar", "commands.locate.poi.success": "%s terdekat berada pada %s (sedjaoeh %s balok)", "commands.locate.structure.invalid": "Bangoenan dengan djenis „%s” tidak ada", "commands.locate.structure.not_found": "Bangoenan berdjenis „%s” tidak bisa ditemoekan disekitar sini", "commands.locate.structure.success": "%s terdekat berada pada %s (sedjaoeh %s balok)", "commands.message.display.incoming": "%s berbisik kepada <PERSON>: %s", "commands.message.display.outgoing": "Toean berbisik kepada %s: %s", "commands.op.failed": "<PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> so<PERSON><PERSON> mend<PERSON> pen<PERSON>", "commands.op.success": "%s ber<PERSON><PERSON> didja<PERSON>kan pengelola peladen", "commands.pardon.failed": "<PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> so<PERSON>h tidak di<PERSON>", "commands.pardon.success": "Menghapoes pemblokiran %s", "commands.pardonip.failed": "<PERSON><PERSON><PERSON>, alamat IP itoe soedah tidak diblokir", "commands.pardonip.invalid": "Alamat IP tidak sah", "commands.pardonip.success": "Menghapoes pemblokiran IP %s", "commands.particle.failed": "<PERSON><PERSON><PERSON> gagal tampak oentoek siapa poen", "commands.particle.success": "Menampilkan partikel %s", "commands.perf.alreadyRunning": "<PERSON><PERSON><PERSON><PERSON> so<PERSON>", "commands.perf.notRunning": "<PERSON>cht<PERSON><PERSON> belo<PERSON>", "commands.perf.reportFailed": "<PERSON><PERSON><PERSON> lepas-kekoetoe gagal diboeat", "commands.perf.reportSaved": "Laporan lepas-kekoetoe diboeat didalam %s", "commands.perf.started": "10 detik pendjalanan pengichtisaran kinerdja dimoelai (pakai „/perf stop” oentoek menghentikannja lebih awal)", "commands.perf.stopped": "Pengichtisaran kinerdja dihentikan setelah %s detik dan %% tik (%s tik per detik)", "commands.place.feature.failed": "<PERSON><PERSON><PERSON> di<PERSON>", "commands.place.feature.invalid": "<PERSON><PERSON><PERSON> den<PERSON> dje<PERSON> „%s” tidak ada", "commands.place.feature.success": "„%s” ditempatkan pada %s, %s, %s", "commands.place.jigsaw.failed": "<PERSON><PERSON><PERSON> gagal diboeat", "commands.place.jigsaw.invalid": "Tidak ada koempoelan sablon dengan djenis „%s”", "commands.place.jigsaw.success": "Kepingan diboeat pada %s, %s, %s", "commands.place.structure.failed": "Bangoenan gagal ditempa<PERSON>kan", "commands.place.structure.invalid": "Bangoenan dengan djenis „%s” tidak ada", "commands.place.structure.success": "Bangoenan „%s” dihasilkan pada %s, %s, %s", "commands.place.template.failed": "Sablon gagal ditempatkan", "commands.place.template.invalid": "Tidak ada koempoelan sablon dengan pengenal „%s”", "commands.place.template.success": "Sablon „%s” dimoeat pada %s, %s, %s", "commands.playsound.failed": "<PERSON><PERSON><PERSON> terl<PERSON>e dja<PERSON>h o<PERSON>k dapat did<PERSON>ar", "commands.playsound.success.multiple": "Soeara %s dimaïnkan kepada %s pemaïn", "commands.playsound.success.single": "Soeara %s dimaïnkan kepada %s", "commands.publish.alreadyPublished": "<PERSON><PERSON><PERSON><PERSON> bersama masih berdjalan pada nomor pintoe %s", "commands.publish.failed": "Tidak bisa mendjalankan permaïnan lokal", "commands.publish.started": "Permaïnan lokal didjalankan pada nomor pintoe %s", "commands.publish.success": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>ma did<PERSON>an pada nomor pintoe %s", "commands.random.error.range_too_large": "Rentang nilai atjak haroes tidak melebihi 2147483646", "commands.random.error.range_too_small": "Rentang nilai atjak haroes paling tidak 2", "commands.random.reset.all.success": "%s oeroetan atjak disetel oelang", "commands.random.reset.success": "Oeroetan atjak %s disetel oelang", "commands.random.roll": "%s mendapat angka %s (antara %s dan %s)", "commands.random.sample.success": "<PERSON><PERSON> atjak: %s", "commands.recipe.give.failed": "Tidak ada resép baroe jang dipela<PERSON>i", "commands.recipe.give.success.multiple": "%s resép diboeka oentoek %s pemaïn", "commands.recipe.give.success.single": "%s resép diboeka oentoek %s", "commands.recipe.take.failed": "Tidak ada resép jang bisa diloepakan", "commands.recipe.take.success.multiple": "%s resép diambil dari %s pemaïn", "commands.recipe.take.success.single": "%s resép diambil dari %s", "commands.reload.failure": "Gagal memoeat oelang; menetapkan data lama", "commands.reload.success": "Memoeat oelang!", "commands.ride.already_riding": "%s soedah menoenggangi %s", "commands.ride.dismount.success": "%s ber<PERSON><PERSON> men<PERSON> %s", "commands.ride.mount.failure.cant_ride_players": "Pema<PERSON>n tidak bisa ditoenggangi", "commands.ride.mount.failure.generic": "%s gagal menoenggangi %s", "commands.ride.mount.failure.loop": "Entitet tidak bisa mengoenggangi dirinja sendiri atau penoenggangnja", "commands.ride.mount.failure.wrong_dimension": "Tidak bisa menoenggangi entitet jang berada didimensi jang berbeda", "commands.ride.mount.success": "%s berhasil menoenggangi %s", "commands.ride.not_riding": "%s sedang tidak menoenggangi kendaraän apa poen", "commands.rotate.success": "%s dipoetar", "commands.save.alreadyOff": "<PERSON><PERSON><PERSON><PERSON><PERSON> soedah mati", "commands.save.alreadyOn": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON> njala", "commands.save.disabled": "Penjimpanan otomatis dimatikan", "commands.save.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON>", "commands.save.failed": "Tidak bisa menjimpan perma<PERSON> (sepertinja penjimpanan tidak mentjoekoepi)", "commands.save.saving": "Menjimpan perma<PERSON> (moengkin perloe beberapa <PERSON>'at)", "commands.save.success": "Permaïnan tersimpan", "commands.schedule.cleared.failure": "Tidak ada djadwal dengan pengenal %s", "commands.schedule.cleared.success": "%s djadwal dengan pengenal %s dihapoes", "commands.schedule.created.function": "<PERSON><PERSON> „%s” <PERSON><PERSON><PERSON><PERSON><PERSON> %s tik pada tempo permaïnan %s", "commands.schedule.created.tag": "Etikét „%s” <PERSON><PERSON><PERSON><PERSON><PERSON> %s tik pada tempo permaïnan %s", "commands.schedule.macro": "Tidak bisa mend<PERSON> ma<PERSON>", "commands.schedule.same_tick": "Tidak bisa mendjadwalkan oentoek tik sa'at ini", "commands.scoreboard.objectives.add.duplicate": "<PERSON><PERSON><PERSON><PERSON> dengan nama jang sama soedah ada", "commands.scoreboard.objectives.add.success": "Toedjoean baroe %s diboeat", "commands.scoreboard.objectives.display.alreadyEmpty": "<PERSON><PERSON><PERSON> be<PERSON>, petak tampil itoe soedah kosong", "commands.scoreboard.objectives.display.alreadySet": "<PERSON><PERSON><PERSON>, petak tampil itoe soedah menamp<PERSON> toed<PERSON>", "commands.scoreboard.objectives.display.cleared": "<PERSON><PERSON><PERSON> toed<PERSON>ean pada petak tampil %s dibersihkan", "commands.scoreboard.objectives.display.set": "Petak tampil %s disetel oentoek menampilkan toedjoean %s", "commands.scoreboard.objectives.list.empty": "Tidak ada toe<PERSON>", "commands.scoreboard.objectives.list.success": "Ada %s toed<PERSON>ean: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Penampilan pembaharoean otomatis oentoek toedjoean %s dimatikan", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Penampilan pembaharoean otomatis oentoek toedjoean %s <PERSON><PERSON><PERSON><PERSON>", "commands.scoreboard.objectives.modify.displayname": "Nama tampilan dioebah dari %s mendjadi %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Format angka asali dari toedjoean %s dihapoes", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Format angka asali dari toedjoean %s dioebah", "commands.scoreboard.objectives.modify.rendertype": "<PERSON><PERSON><PERSON> toed<PERSON> %s dioebah", "commands.scoreboard.objectives.remove.success": "Toedjoean %s telah di<PERSON>", "commands.scoreboard.players.add.success.multiple": "%s ditambahkan ke-%s oentoek %s entitet", "commands.scoreboard.players.add.success.single": "%s ditambahkan ke-%s oentoek %s (sekarang %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Nama tampilan oentoek %s entitet didalam %s telah di<PERSON>poes", "commands.scoreboard.players.display.name.clear.success.single": "Nama tampilan oentoek %s didalam %s telah di<PERSON>es", "commands.scoreboard.players.display.name.set.success.multiple": "Nama tampilan oentoek %$2s entitet didalam %3$s dioebah mendjadi %1$s", "commands.scoreboard.players.display.name.set.success.single": "Nama tampilan oentoek %$2s didalam %3$s dioebah mendjadi %1$s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Format angka oentoek %s entitet didalam %s telah dihapoes", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Format angka oentoek %s didalam %s telah di<PERSON>es", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Format angka oentoek %s entitet didalam %s telah dioebah", "commands.scoreboard.players.display.numberFormat.set.success.single": "Format angka oentoek %s didalam %s telah dioebah", "commands.scoreboard.players.enable.failed": "<PERSON><PERSON><PERSON>, pemit<PERSON><PERSON> itoe soedah njala", "commands.scoreboard.players.enable.invalid": "<PERSON><PERSON><PERSON><PERSON>n hanja bekerdja pada toed<PERSON><PERSON> pemitjoe", "commands.scoreboard.players.enable.success.multiple": "Pemitjoe %s telah njala oentoek %s entitet", "commands.scoreboard.players.enable.success.single": "Pemitjoe %s telah njala oentoek %s", "commands.scoreboard.players.get.null": "Tidak bisa memperoleh nilai %s oentoek %s; tidak ada jang disetel", "commands.scoreboard.players.get.success": "%s poenja %s %s", "commands.scoreboard.players.list.empty": "Tidak ada entitet jang dilatjak", "commands.scoreboard.players.list.entity.empty": "%s tidak poenja nilai oentoek ditampilkan", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s poenja %s nilai:", "commands.scoreboard.players.list.success": "Ada %s entitet jang dilatjak: %s", "commands.scoreboard.players.operation.success.multiple": "%s diperbaharoei oentoek %s entitet", "commands.scoreboard.players.operation.success.single": "%s oentoek %s disetel mendjadi %s", "commands.scoreboard.players.remove.success.multiple": "Sedjoemlah %s dari %s oentoek %s entitet telah dihapoes", "commands.scoreboard.players.remove.success.single": "Sedjoemlah %s dari %s oentoek %s telah dihapoes (sekarang %s)", "commands.scoreboard.players.reset.all.multiple": "Semoea nilai oentoek %s entitet telah disetel oelang", "commands.scoreboard.players.reset.all.single": "Semoea nilai oentoek %s telah disetel oelang", "commands.scoreboard.players.reset.specific.multiple": "%s oentoek %s entitet telah disetel oelang", "commands.scoreboard.players.reset.specific.single": "%s oentoek %s telah disetel oelang", "commands.scoreboard.players.set.success.multiple": "%s oentoek %s entitet disetel mendjadi %s", "commands.scoreboard.players.set.success.single": "%s oentoek %s disetel mendjadi %s", "commands.seed.success": "Benih: %s", "commands.setblock.failed": "Tidak bisa menjetel balok", "commands.setblock.success": "Balok pada %s, %s, %s telah dioebah", "commands.setidletimeout.success": "Tempo toenggoe pemaïn mengangg<PERSON>r sekarang %s menit", "commands.setidletimeout.success.disabled": "Tempo toenggoe pema<PERSON>n men<PERSON>r telah dimatikan", "commands.setworldspawn.failure.not_overworld": "Hanja bisa menjetel titik bangkit oentoek Permoekaän", "commands.setworldspawn.success": "Titik bangkit doenia disetel dikoördinat %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Titik bangkit oentoek %6$s pemaïn disetel dikoördinat %1$s, %%s, %3$s [%4$s] di %5$s", "commands.spawnpoint.success.single": "Titik bangkit oentoek %6$s disetel dikoördinat %1$s, %%s, %3$s [%4$s] di %5$s", "commands.spectate.not_spectator": "%s sedang tidak dalam ragam penonton", "commands.spectate.self": "Tidak bisa menonton diri sendiri", "commands.spectate.success.started": "Sekarang menonton %s", "commands.spectate.success.stopped": "<PERSON><PERSON>h tidak lagi menonton entitet", "commands.spreadplayers.failed.entities": "Tidak bisa menjebarkan %s entitet disekitar %s, %s (terlaloe banjak entitet dalam roeang, tjoba pakai persebaran paling banjak %s)", "commands.spreadplayers.failed.invalid.height": "maxHeight %s tidak sah; diharapkan lebih tinggi dari minimal doenia %s", "commands.spreadplayers.failed.teams": "Tidak bisa menjebarkan %s regoe disekitar %s, %s (terlaloe banjak entitet dalam roeang, tjoba pakai persebaran paling banjak %s)", "commands.spreadplayers.success.entities": "%s entitet disebarkan disekitar %s, %s dengan djarak rata-rata %s balok", "commands.spreadplayers.success.teams": "%s regoe disebarkan disekitar %s, %s dengan djarak rata-rata %s balok", "commands.stop.stopping": "Menghentikan peladen", "commands.stopsound.success.source.any": "Se<PERSON>ea soeara „%s” dihentikan", "commands.stopsound.success.source.sound": "Se<PERSON><PERSON> soeara „%s” dengan soember „%s” dihentikan", "commands.stopsound.success.sourceless.any": "<PERSON><PERSON><PERSON> soeara di<PERSON>", "commands.stopsound.success.sourceless.sound": "<PERSON><PERSON>a „%s” dihentikan", "commands.summon.failed": "Tidak bisa memanggil entitet", "commands.summon.failed.uuid": "Tidak bisa memanggil entitet karena pengenal choesoes semesta seroepa", "commands.summon.invalidPosition": "Posisi tidak sah oentoek pemanggilan", "commands.summon.success": "%s baroe dipanggil", "commands.tag.add.failed": "<PERSON><PERSON><PERSON> so<PERSON>h poenja étikét itoe atau poenja terlaloe banjak étikét", "commands.tag.add.success.multiple": "Etikét „%s” ditambahkan pada %s entitet", "commands.tag.add.success.single": "Etikét „%s” ditambahkan pada %s", "commands.tag.list.multiple.empty": "Tidak ada étikét pada %s entitet", "commands.tag.list.multiple.success": "Sedjoemlah %s entitet poenja %s étikét: %s", "commands.tag.list.single.empty": "%s tidak poenja étikét", "commands.tag.list.single.success": "%s poenja %s étikét: %s", "commands.tag.remove.failed": "Sasaran tidak poenja étikét ini", "commands.tag.remove.success.multiple": "Etikét „%s” dihapoes dari %s entitet", "commands.tag.remove.success.single": "Etikét „%s” dihapoes dari %s", "commands.team.add.duplicate": "<PERSON><PERSON> dengan nama jang sama soedah ada", "commands.team.add.success": "Regoe %s ditambah", "commands.team.empty.success": "%s anggota telah dihapoes dari regoe %s", "commands.team.empty.unchanged": "<PERSON><PERSON><PERSON> be<PERSON>, regoe itoe soedah kosong", "commands.team.join.success.multiple": "%s anggota telah ditambahkan keregoe %s", "commands.team.join.success.single": "%s telah ditambahkan keregoe %s", "commands.team.leave.success.multiple": "%s anggota telah dihapoes dari semoea regoe", "commands.team.leave.success.single": "%s telah dihapoes dari semoea regoe", "commands.team.list.members.empty": "Regoe %s tidak poenja anggota", "commands.team.list.members.success": "Regoe %s poenja %s anggota: %s", "commands.team.list.teams.empty": "Tidak ada regoe", "commands.team.list.teams.success": "Ada %s regoe: %s", "commands.team.option.collisionRule.success": "Atoeran toemboekan oentoek regoe %s sekarang disetel mendjadi „%s”", "commands.team.option.collisionRule.unchanged": "<PERSON><PERSON><PERSON>, atoeran toemboekan soedah bernilai ioe", "commands.team.option.color.success": "Warna regoe diperbaharoei dari %s mendjadi %s", "commands.team.option.color.unchanged": "<PERSON><PERSON><PERSON>, warna itoe soedah mendjadi warna regoe", "commands.team.option.deathMessageVisibility.success": "Ketampakan pesan kematian oentoek regoe %s sekarang disetel mendjadi „%s”", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON><PERSON><PERSON> be<PERSON>, ketampakan pesan kematian so<PERSON>h bern<PERSON>i itoe", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON><PERSON><PERSON>, tembak teman soedah mati oentoek regoe itoe", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON><PERSON><PERSON>, tembak teman soedah njala oentoek regoe itoe", "commands.team.option.friendlyfire.disabled": "Tembak teman dimatikan oentoek regoe %s", "commands.team.option.friendlyfire.enabled": "Tembak teman dinjalakan oentoek regoe %s", "commands.team.option.name.success": "Nama regoe %s telah diperbaharoei", "commands.team.option.name.unchanged": "<PERSON><PERSON><PERSON>, nama itoe soedah mendjadi nama regoe", "commands.team.option.nametagVisibility.success": "Ketampakan étikét nama oentoek regoe %s sekarang disetel mendjadi „%s”", "commands.team.option.nametagVisibility.unchanged": "T<PERSON>k <PERSON>, ketampakan étikét nama soedah bernilai itoe", "commands.team.option.prefix.success": "Awalan regoe disetel mendjadi %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON><PERSON><PERSON> be<PERSON>, regoe itoe soedah tidak bisa melihat rekan regoe limoenan", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON><PERSON><PERSON> be<PERSON>, regoe itoe soedah bisa melihat rekan regoe limoenan", "commands.team.option.seeFriendlyInvisibles.disabled": "Regoe %s soedah tidak bisa melihat rekan regoe limoenan", "commands.team.option.seeFriendlyInvisibles.enabled": "Regoe %s sekarang bisa melihat rekan regoe limoenan", "commands.team.option.suffix.success": "Achiran regoe disetel mendjadi %s", "commands.team.remove.success": "Regoe %s telah dihapoes", "commands.teammsg.failed.noteam": "<PERSON><PERSON> haroes berada didalam regoe agar bisa mengirim pesan kepada regoe toean", "commands.teleport.invalidPosition": "Posisi tidak sah oentoek teleportasi", "commands.teleport.success.entity.multiple": "Meneleportir %s entitet ke-%s", "commands.teleport.success.entity.single": "Meneleportir %s ke-%s", "commands.teleport.success.location.multiple": "Meneleportir %s entitet kekoördinat %s, %s, %s", "commands.teleport.success.location.single": "Meneleportir %s kekoördinat %s, %s, %s", "commands.test.batch.starting": "Memoelai lingkoengan %s toempak %s", "commands.test.clear.error.no_tests": "Tidak bisa mentjari oedji oentoek dibersihkan", "commands.test.clear.success": "%s bangoenan dibersihkan", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Pentjet oentoek menjalin kepapan-klip", "commands.test.create.success": "Pemasangan oedji oentoek oedji %s telah diboeat", "commands.test.error.no_test_containing_pos": "Peristiwa oedji jang mengandoeng %s, %s, %s tidak bisa ditjari", "commands.test.error.no_test_instances": "Peristiwa oedji tidak ditemoekan", "commands.test.error.non_existant_test": "Oedji %s tidak bisa ditjari", "commands.test.error.structure_not_found": "Bangoenan oedji %s tidak bisa ditjari", "commands.test.error.test_instance_not_found": "Entitet balok peristiwa oedji tidak bisa ditjari", "commands.test.error.test_instance_not_found.position": "Entitet balok peristiwa oedji oentoek oedji pada %s, %s, %s tidak bisa ditjari", "commands.test.error.too_large": "Oekoeran bangoenan haroes koerang dari %s balok sepandjang setiap soemboe", "commands.test.locate.done": "<PERSON><PERSON><PERSON><PERSON>, ditemoekan %s bangoenan", "commands.test.locate.found": "Bangoenan ditemoekan pada: %s (djarak: %s)", "commands.test.locate.started": "Pentjar<PERSON> bang<PERSON><PERSON> o<PERSON>, ini moengkin perloe waktoe sesa'at...", "commands.test.no_tests": "Tidak ada oedji oentoek did<PERSON>lank<PERSON>", "commands.test.relative_position": "Posisi relatif terhadap %s: %s", "commands.test.reset.error.no_tests": "Tidak bisa mentjari oetji oentoek disetel oelang", "commands.test.reset.success": "%s bangoenan disetel oelang", "commands.test.run.no_tests": "Oedji t<PERSON>", "commands.test.run.running": "Mendjalankan %s oedji...", "commands.test.summary": "Oedji <PERSON>nan selesai! %s oedji telah <PERSON>", "commands.test.summary.all_required_passed": "<PERSON><PERSON><PERSON> oedji jang diperloekan lo<PERSON> ☺", "commands.test.summary.failed": "%s oedji jang diperloekan gagal ☹", "commands.test.summary.optional_failed": "%s oedji op<PERSON>il gagal", "commands.tick.query.percentiles": "Persentil: P50: %smd P95: %smd P99: %smd, pertjontoh: %s", "commands.tick.query.rate.running": "Ladjoe tik sasaran: %s per detik.\nRata-rata waktoe per tik: %smd (sasaran: %smd)", "commands.tick.query.rate.sprinting": "Ladjoe tik sasaran: %s per detik. (di<PERSON><PERSON><PERSON>, roed<PERSON><PERSON>n sadja)\nRata-rata waktoe per tik: %smd", "commands.tick.rate.success": "Ladjoe tik sasaran disetel ke %s per detik", "commands.tick.sprint.report": "Larian selesai dengan %s tik per detik, atau %s md per tik", "commands.tick.sprint.stop.fail": "Tidak ada larian tik jang sedang berlang<PERSON>", "commands.tick.sprint.stop.success": "<PERSON><PERSON> tik disela", "commands.tick.status.frozen": "Permaï<PERSON>", "commands.tick.status.lagging": "<PERSON><PERSON><PERSON><PERSON>, tetapi tidak bisa mengedjar ladjoe tik sasaran", "commands.tick.status.running": "<PERSON><PERSON><PERSON><PERSON>", "commands.tick.status.sprinting": "Permaïnan sedang melakoekan larian", "commands.tick.step.fail": "Tidak bisa memad<PERSON><PERSON><PERSON>, perma<PERSON><PERSON> haroes dibekoekan terlebih <PERSON>e", "commands.tick.step.stop.fail": "Tidak ada pemadjoean tik jang sedang berlang<PERSON>", "commands.tick.step.stop.success": "Pemadjoean tik disela", "commands.tick.step.success": "Memadjoekan sed<PERSON> %s tik", "commands.time.query": "Waktoe sa'at ini adalah %s", "commands.time.set": "Waktoe disetel mendjadi %s", "commands.title.cleared.multiple": "Djoedoel dibersihkan oentoek %% pemaïn", "commands.title.cleared.single": "Djoedoel dibersihkan oentoek %s", "commands.title.reset.multiple": "Opsi djoedoel disetel oelang oentoek %s pemaïn", "commands.title.reset.single": "Opsi djoedoel disetel oelang oentoek %s", "commands.title.show.actionbar.multiple": "Djoedoel bilah aksi baroe ditampilkan kepada %s pemaïn", "commands.title.show.actionbar.single": "Djoedoel bilah aksi baroe ditampilkan kepada %s", "commands.title.show.subtitle.multiple": "Anak djoedoel baroe ditampilkan kepada %s pemaïn", "commands.title.show.subtitle.single": "Anak djoedoel baroe ditampilkan kepada %s", "commands.title.show.title.multiple": "Djoedoel baroe ditampilkan kepada %s pemaïn", "commands.title.show.title.single": "Djoedoel baroe ditampilkan kepada %s", "commands.title.times.multiple": "Tempo tampil djoedoel dioebah oentoek %s pemaïn", "commands.title.times.single": "Tempo tampil djoedoel dioebah oentoek %s", "commands.transfer.error.no_players": "<PERSON><PERSON><PERSON><PERSON> menent<PERSON>kan minimal satoe pemaïn jang akan dip<PERSON>kan", "commands.transfer.success.multiple": "Memindahkan %s pemaïn ke-%s:%s", "commands.transfer.success.single": "Memindahkan %s ke %s:%s", "commands.trigger.add.success": "%s dipitjoe (%s ditambahkan ke nilai)", "commands.trigger.failed.invalid": "Toean hanja bisa memitjoe sasaran dengan djenis „trigger”", "commands.trigger.failed.unprimed": "<PERSON><PERSON> beloem bisa memitjoe sasaran ini", "commands.trigger.set.success": "%s dipitjoe (nilai disetel sebesar %s)", "commands.trigger.simple.success": "%s dipitjoe", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "Tidak ada titik-djalan di-%s", "commands.waypoint.list.success": "Ada %s titik-djalan di-%s: %s", "commands.waypoint.modify.color": "Warna titik-d<PERSON><PERSON> dioebah mendjadi %s", "commands.waypoint.modify.color.reset": "<PERSON><PERSON> titi<PERSON>-d<PERSON><PERSON> di<PERSON> o<PERSON>ng", "commands.waypoint.modify.style": "Gaja titik-djalan telah di<PERSON>", "commands.weather.set.clear": "<PERSON><PERSON><PERSON><PERSON> dioebah mendjadi tjerah", "commands.weather.set.rain": "<PERSON><PERSON><PERSON><PERSON> di<PERSON> mendjadi hudjan", "commands.weather.set.thunder": "<PERSON><PERSON><PERSON><PERSON> dioebah mendjadi hudjan petir", "commands.whitelist.add.failed": "<PERSON><PERSON><PERSON><PERSON> didalam daftar poetih", "commands.whitelist.add.success": "%s ditambahkan kedalam daftar poetih", "commands.whitelist.alreadyOff": "Daftar poetih soedah mati", "commands.whitelist.alreadyOn": "Daftar poet<PERSON> n<PERSON>la", "commands.whitelist.disabled": "Daftar poetih telah dimatikan", "commands.whitelist.enabled": "Daftar poetih te<PERSON>", "commands.whitelist.list": "Ada %s p<PERSON><PERSON><PERSON> did<PERSON>m da<PERSON>ar poetih: %s", "commands.whitelist.none": "Tidak ada pema<PERSON>n dalam daftar poetih", "commands.whitelist.reloaded": "Daftar poetih telah dimoeat oelang", "commands.whitelist.remove.failed": "<PERSON><PERSON><PERSON><PERSON> tidak didalam daftar poetih", "commands.whitelist.remove.success": "%s dihapoes dari daftar poetih", "commands.worldborder.center.failed": "T<PERSON>k be<PERSON>, batas doenia soedah berpoesat disitoe", "commands.worldborder.center.success": "Poesat batas doenia disetel ke-%s, %s", "commands.worldborder.damage.amount.failed": "<PERSON><PERSON><PERSON>, loeka serangan batas doenia soedah sebesar itoe", "commands.worldborder.damage.amount.success": "Serangan batas doenia disetel sebesar %s per balok setiap detik", "commands.worldborder.damage.buffer.failed": "<PERSON><PERSON><PERSON>, djarak penjangga serangan batas doenia soedah berdjarak itoe", "commands.worldborder.damage.buffer.success": "Penjangga serangan batas doenia disetel mendjadi %s balok", "commands.worldborder.get": "Batas doenia sa'at ini sebesar %s balok", "commands.worldborder.set.failed.big": "Lebar batas doenia tidak boleh lebih besar dari %s balok", "commands.worldborder.set.failed.far": "Batas doenia tidak boleh lebih djaoeh dari %s balok", "commands.worldborder.set.failed.nochange": "<PERSON><PERSON><PERSON> be<PERSON>, batas doenia soedah beroekoeran itoe", "commands.worldborder.set.failed.small": "Lebar batas doenia tidak boleh lebih ketjil dari 1 balok", "commands.worldborder.set.grow": "Batas doenia dilebarkan sebesar %s balok selama %s detik", "commands.worldborder.set.immediate": "Lebar batas doenia disetel selebar %s balok", "commands.worldborder.set.shrink": "Batas doenia disempitkan mendjadi %s balok selama %s detik", "commands.worldborder.warning.distance.failed": "<PERSON><PERSON><PERSON>, djarak per<PERSON>tan batas doenia soedah sedja<PERSON>h itoe", "commands.worldborder.warning.distance.success": "Djarak peringatan batas doenia disetel mendjadi %s balok", "commands.worldborder.warning.time.failed": "<PERSON><PERSON><PERSON>, tempo peringatan batas doenia soedah selama itoe", "commands.worldborder.warning.time.success": "Tempo peringatan batas doenia disetel mendjadi %s detik", "compliance.playtime.greaterThan24Hours": "<PERSON><PERSON> telah be<PERSON><PERSON> le<PERSON>h dari 24 djam", "compliance.playtime.hours": "Toean telah bermaïn selama %s djam", "compliance.playtime.message": "<PERSON><PERSON><PERSON><PERSON> set<PERSON>a be<PERSON>han dapat mengganggoe keh<PERSON><PERSON>an se<PERSON>-hari", "connect.aborted": "Di<PERSON><PERSON><PERSON>", "connect.authorizing": "Memasoeki...", "connect.connecting": "Menghoeboengkan kepeladen...", "connect.encrypting": "Menjandi<PERSON>...", "connect.failed": "<PERSON><PERSON>ngkan kepeladen", "connect.failed.transfer": "Samboengan gagal sa'at memindahkan kepeladen", "connect.joining": "Bergaboeng kedoenia...", "connect.negotiating": "Menghoeboengkan...", "connect.reconfiging": "<PERSON><PERSON><PERSON> o<PERSON>...", "connect.reconfiguring": "<PERSON><PERSON><PERSON> o<PERSON>...", "connect.transferring": "Memindahkan kepeladen baroe...", "container.barrel": "<PERSON>", "container.beacon": "Soear", "container.beehive.bees": "Lebah: %s/%s", "container.beehive.honey": "Madoe: %s/%s", "container.blast_furnace": "<PERSON><PERSON><PERSON> tioep", "container.brewing": "<PERSON><PERSON> per<PERSON>", "container.cartography_table": "<PERSON><PERSON><PERSON>", "container.chest": "<PERSON><PERSON>", "container.chestDouble": "<PERSON><PERSON>", "container.crafter": "<PERSON><PERSON>", "container.crafting": "<PERSON><PERSON><PERSON><PERSON>", "container.creative": "<PERSON><PERSON><PERSON><PERSON>a", "container.dispenser": "Pelempar", "container.dropper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.enchant": "<PERSON><PERSON><PERSON><PERSON>", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s lazoeardi", "container.enchant.lapis.one": "1 lazoeardi", "container.enchant.level.many": "%s tingkat pesona", "container.enchant.level.one": "1 tingkat pesona", "container.enchant.level.requirement": "Persjaratan tingkat: %s", "container.enderchest": "<PERSON><PERSON>", "container.furnace": "Oven", "container.grindstone_title": "Perbaiki dan lepas pesona", "container.hopper": "<PERSON><PERSON><PERSON>", "container.inventory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.isLocked": "%s terkoentji!", "container.lectern": "<PERSON><PERSON>", "container.loom": "Alat tenoen", "container.repair": "Perbaiki dan na<PERSON>", "container.repair.cost": "Biaja pesona: %1$s", "container.repair.expensive": "Terl<PERSON><PERSON> mahal!", "container.shulkerBox": "Kotak shulker", "container.shulkerBox.itemCount": "%s sebanjak %s", "container.shulkerBox.more": "dan %s lagi...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Pengasap", "container.spectatorCantOpen": "Tidak bisa diboeka, d<PERSON><PERSON> beloem diboeat.", "container.stonecutter": "<PERSON><PERSON><PERSON> batoe", "container.upgrade": "Tingkatkan alat dan zirah", "container.upgrade.error_tooltip": "Benda tidak bisa ditingkatkan dengan tjara ini", "container.upgrade.missing_template_tooltip": "Tambah sablon tempa", "controls.keybinds": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>...", "controls.keybinds.duplicateKeybinds": "Koentji ini djoega dipakai oentoek:\n%s", "controls.keybinds.title": "<PERSON><PERSON><PERSON><PERSON>", "controls.reset": "<PERSON><PERSON>", "controls.resetAll": "<PERSON><PERSON> koe<PERSON>", "controls.title": "<PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Mohon pilih bioom", "createWorld.customize.buffet.title": "Pengoebah-<PERSON><PERSON>ian <PERSON>", "createWorld.customize.flat.height": "Tingg<PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Bawah - %s", "createWorld.customize.flat.layer.top": "Atas - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON> lap<PERSON>", "createWorld.customize.flat.tile": "<PERSON><PERSON> lapisan", "createWorld.customize.flat.title": "Pen<PERSON>eb<PERSON><PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.presets": "Sablon", "createWorld.customize.presets.list": "<PERSON><PERSON>, inilah beberapa jang telah kami boeat sebeloemnja!", "createWorld.customize.presets.select": "Pakai sablon", "createWorld.customize.presets.share": "Ingin membagikan sablon toean dengan jang la<PERSON>n? Pakai kotak dibawah ini!", "createWorld.customize.presets.title": "<PERSON><PERSON><PERSON>", "createWorld.preparing": "Menjiapkan pentjiptaän doenia...", "createWorld.tab.game.title": "Per<PERSON>ï<PERSON>", "createWorld.tab.more.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.tab.world.title": "Doenia", "credits_and_attribution.button.attribution": "Pengaïtan", "credits_and_attribution.button.credits": "Pen<PERSON><PERSON><PERSON>", "credits_and_attribution.button.licenses": "<PERSON><PERSON><PERSON>", "credits_and_attribution.screen.title": "Pengaï<PERSON> dan <PERSON>", "dataPack.bundle.description": "<PERSON><PERSON><PERSON><PERSON> benda pert<PERSON>n kantong", "dataPack.bundle.name": "Kantong", "dataPack.locator_bar.description": "<PERSON><PERSON><PERSON><PERSON> arah pemaïn laïn sa'at berma<PERSON>n bersama", "dataPack.locator_bar.name": "<PERSON><PERSON><PERSON>", "dataPack.minecart_improvements.description": "Peningkatan pergerakan kereta tambang", "dataPack.minecart_improvements.name": "Peningkatan kereta tambang", "dataPack.redstone_experiments.description": "Pertjobaän per<PERSON>an redstone", "dataPack.redstone_experiments.name": "Pertjobaän redstone", "dataPack.title": "<PERSON><PERSON><PERSON>", "dataPack.trade_rebalance.description": "Dagangan pendoedoek jang diperbaharoei", "dataPack.trade_rebalance.name": "Penjeïmbangan dagangan pendo<PERSON>k", "dataPack.update_1_20.description": "Moeatan baroe oentoek Minecraft 1.20", "dataPack.update_1_20.name": "Pembaharoean 1.20", "dataPack.update_1_21.description": "Moeatan baroe oentoek Minecraft 1.21", "dataPack.update_1_21.name": "Pembaharoean 1.21", "dataPack.validation.back": "Kembali", "dataPack.validation.failed": "Validasi paket data gagal!", "dataPack.validation.reset": "<PERSON><PERSON> o<PERSON> mend<PERSON>di asali", "dataPack.validation.working": "Memvalidasi paket data terpilih...", "dataPack.vanilla.description": "Data asali oentoek Minecraft", "dataPack.vanilla.name": "<PERSON><PERSON>", "dataPack.winter_drop.description": "Moeatan baroe oentoek keloearan moesim dingin", "dataPack.winter_drop.name": "<PERSON><PERSON><PERSON><PERSON> moesim dingin", "datapackFailure.safeMode": "<PERSON><PERSON> aman", "datapackFailure.safeMode.failed.description": "Doenia ini berisi data tersimpan jang tidak sah atau roesak.", "datapackFailure.safeMode.failed.title": "Doenia dalam ragam aman gagal dimoeat.", "datapackFailure.title": "Kesalahan pada paket data terpilih sa'at ini menghalangi pemoeatan doenia.\nToean bisa mentjoba memoeatnja hanja dengan paket data asali („ragam aman”), atau kembali kehalaman oetama dan memperbaïkinja dengan tangan.", "death.attack.anvil": "%1$s tertimpa paron", "death.attack.anvil.player": "%1$s tertimpa paron sa'at melawan %2$s", "death.attack.arrow": "%1$s dipanah oleh %2$s", "death.attack.arrow.item": "%1$s dipanah oleh %2$s memakai %3$s", "death.attack.badRespawnPoint.link": "rant<PERSON><PERSON> permaïnan jang disengadja", "death.attack.badRespawnPoint.message": "%1$s diboenoeh oleh %2$s", "death.attack.cactus": "%1$s tertoesoek sampai mati", "death.attack.cactus.player": "%1$s berd<PERSON><PERSON> kekaktoes sa'at mentjoba kaboer dari %2$s", "death.attack.cramming": "%1$s terlaloe digentjet", "death.attack.cramming.player": "%1$s tergentjet oleh %2$s", "death.attack.dragonBreath": "%1$s dipanggang dalam napas naga", "death.attack.dragonBreath.player": "%1$s dipanggang dalam napas naga oleh %2$s", "death.attack.drown": "%1$s tenggelam", "death.attack.drown.player": "%1$s tenggelam sa'at mentjoba kaboer dari %2$s", "death.attack.dryout": "%1$s mati karena de<PERSON>si", "death.attack.dryout.player": "%1$s mati karena dehidrasi sa'at mentjoba kaboer dari %2$s", "death.attack.even_more_magic": "%1$s telah diboenoeh oleh soeatoe kekoeatan gaïb", "death.attack.explosion": "%1$s meledak", "death.attack.explosion.player": "%1$s diledakkan oleh %2$s", "death.attack.explosion.player.item": "%1$s diledakkan oleh %2$s memakai %3$s", "death.attack.fall": "%1$s djatoeh terlaloe keras", "death.attack.fall.player": "%1$s djatoeh terlaloe keras sa'at mentjoba kaboer dari %2$s", "death.attack.fallingBlock": "%1$s tergentjet balok djatoeh", "death.attack.fallingBlock.player": "%1$s tergentjet balok djatoeh sa'at melawan %2$s", "death.attack.fallingStalactite": "%1$s tertoesoek stalaktit djatoeh", "death.attack.fallingStalactite.player": "%1$s tertoesoek stalaktit djatoeh sa'at melawan %2$s", "death.attack.fireball": "%1$s ditembak bola api oleh %2$s", "death.attack.fireball.item": "%1$s ditembak bola api oleh %2$s memakai %3$s", "death.attack.fireworks": "%1$s meledak keras", "death.attack.fireworks.item": "%1$s meledak keras sebab kembang api jang ditembak dari %3$s oleh %2$s", "death.attack.fireworks.player": "%1$s meledak keras sa'at melawan %2$s", "death.attack.flyIntoWall": "%1$s mengalami energi kinetis", "death.attack.flyIntoWall.player": "%1$s mengalami energi kinetis sa'at mentjoba kaboer dari %2$s", "death.attack.freeze": "%1$s mati membekoe", "death.attack.freeze.player": "%1$s membekoe hingga tewas oleh %2$s", "death.attack.generic": "%1$s tewas", "death.attack.generic.player": "%1$s diboenoeh oleh %2$s", "death.attack.genericKill": "%1$s diboenoeh", "death.attack.genericKill.player": "%1$s diboenoeh sa'at melawan %2$s", "death.attack.hotFloor": "%1$s mendapati lantai adalah lava", "death.attack.hotFloor.player": "%1$s berd<PERSON><PERSON> kezone bahaja sebab %2$s", "death.attack.inFire": "%1$s tewas terbakar", "death.attack.inFire.player": "%1$s berd<PERSON><PERSON> keapi sa'at melawan %2$s", "death.attack.inWall": "%1$s tertjekik dalam dinding", "death.attack.inWall.player": "%1$s tertjekik dalam dinding sa'at melawan %2$s", "death.attack.indirectMagic": "%1$s diboenoeh oleh %2$s memakai sihir", "death.attack.indirectMagic.item": "%1$s diboe<PERSON>eh oleh %2$s memakai %3$s", "death.attack.lava": "%1$s mentjoba berenang dilava", "death.attack.lava.player": "%1$s mentjoba berenang dilava oentoek kaboer dari %2$s", "death.attack.lightningBolt": "%1$s disambar petir", "death.attack.lightningBolt.player": "%1$s disambar petir sa'at melawan %2$s", "death.attack.mace_smash": "%1$s digetjek oleh %2$s", "death.attack.mace_smash.item": "%1$s digetjek oleh %2$s memakai %3$s", "death.attack.magic": "%1$s diboenoeh oleh sihir", "death.attack.magic.player": "%1$s diboenoeh oleh sihir sa'at mentjoba kaboer dari %2$s", "death.attack.message_too_long": "<PERSON><PERSON><PERSON><PERSON>, pesan terlaloe pandjang oentoek dikirimkan seloeroehnja. Ma'af! Ini versi jang soedah dipendekkan: %s", "death.attack.mob": "%1$s dibantai oleh %2$s", "death.attack.mob.item": "%1$s dibantai oleh %2$s memakai %3$s", "death.attack.onFire": "%1$s terbakar sampai mati", "death.attack.onFire.item": "%1$s terbakar mend<PERSON>di aboe sa'at melawan %2$s jang memakai %3$s", "death.attack.onFire.player": "%1$s terbakar mendjadi aboe sa'at melawan %2$s", "death.attack.outOfWorld": "%1$s djatoeh dari doenia", "death.attack.outOfWorld.player": "%1$s tidak ingin hidoep didoenia jang sama dengan %2$s", "death.attack.outsideBorder": "%1$s keloear dari batas doenia", "death.attack.outsideBorder.player": "%1$s keloear dari batas doenia sa'at melawan %2$s", "death.attack.player": "%1$s dibantai oleh %2$s", "death.attack.player.item": "%1$s dibantai oleh %2$s memakai %3$s", "death.attack.sonic_boom": "%1$s dimoesnahkan oleh djeritan sonis", "death.attack.sonic_boom.item": "%1$s dimoesnahkan oleh djeritan sonis sa'at mentjoba kaboer dari %2$s jang memakai %3$s", "death.attack.sonic_boom.player": "%1$s dimoesnahkan oleh djeritan sonis sa'at mentjoba kaboer dari %2$s", "death.attack.stalagmite": "%1$s tertoesoek stalagmit", "death.attack.stalagmite.player": "%1$s tertoesoek stalagmit sa'at melawan %2$s", "death.attack.starve": "%1$s mati kelaparan", "death.attack.starve.player": "%1$s mati kelaparan sa'at melawan %2$s", "death.attack.sting": "%1$s disengat hingga mati", "death.attack.sting.item": "%1$s disengat hingga mati oleh %2$s memakai %3$s", "death.attack.sting.player": "%1$s disengat hingga mati oleh %2$s", "death.attack.sweetBerryBush": "%1$s tertoesoek hingga mati oleh semak boeni manis", "death.attack.sweetBerryBush.player": "%1$s tertoesoek hingga mati oleh semak boeni manis sa'at mentjoba kaboer dari %2$s", "death.attack.thorns": "%1$s terboenoeh sa'at mencoba meloekaï %2$s", "death.attack.thorns.item": "%1$s terboenoeh oleh %3$s sa'at mencoba meloekaï %2$s", "death.attack.thrown": "%1$s dihadjar o<PERSON>h %2$s", "death.attack.thrown.item": "%1$s dihadjar o<PERSON>h %2$s memakai %3$s", "death.attack.trident": "%1$s ditoesoek oleh %2$s", "death.attack.trident.item": "%1$s ditoesoek oleh %2$s memakai %3$s", "death.attack.wither": "%1$s mati kelajoean", "death.attack.wither.player": "%1$s mati kelajoean sa'at melawan %2$s", "death.attack.witherSkull": "%1$s ditembak dengan tengkorak dari %2$s", "death.attack.witherSkull.item": "%1$s ditembak dengan tengkorak dari %2$s memakai %3$s", "death.fell.accident.generic": "%1$s djatoeh dari tempat tinggi", "death.fell.accident.ladder": "%1$s djatoeh dari tangga", "death.fell.accident.other_climbable": "%1$s djatoeh sa'at memandjat", "death.fell.accident.scaffolding": "%1$s dja<PERSON><PERSON> dari perantjah", "death.fell.accident.twisting_vines": "%1$s djatoeh dari perambat melilit", "death.fell.accident.vines": "%1$s djato<PERSON> dari toe<PERSON> merambat", "death.fell.accident.weeping_vines": "%1$s djatoeh dari perambat berk<PERSON><PERSON>", "death.fell.assist": "%1$s tewas didjatoehkan oleh %2$s", "death.fell.assist.item": "%1$s tewas didjatoehkan oleh %2$s memakai %3$s", "death.fell.finish": "%1$s dja<PERSON>eh terlaloe djaoeh dan dihabisi oleh %2$s", "death.fell.finish.item": "%1$s dja<PERSON><PERSON> terlaloe djaoeh dan dihabisi oleh %2$s memakai %3$s", "death.fell.killer": "%1$s tewas terdjatoeh", "deathScreen.quit.confirm": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin kelo<PERSON>?", "deathScreen.respawn": "Bangkit kembali", "deathScreen.score": "<PERSON><PERSON>", "deathScreen.score.value": "<PERSON><PERSON>: %s", "deathScreen.spectate": "Tonton do<PERSON>", "deathScreen.title": "Toean mati!", "deathScreen.title.hardcore": "Permaïnan se<PERSON>!", "deathScreen.titleScreen": "<PERSON><PERSON>", "debug.advanced_tooltips.help": "F3 + H = <PERSON><PERSON><PERSON><PERSON><PERSON> benda landjoetan", "debug.advanced_tooltips.off": "<PERSON><PERSON>nd<PERSON><PERSON> benda landjoetan: tersemboenji", "debug.advanced_tooltips.on": "<PERSON><PERSON>nd<PERSON><PERSON> benda landjoetan: tampak", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON><PERSON><PERSON> batas bingkah", "debug.chunk_boundaries.off": "<PERSON><PERSON><PERSON> perbat<PERSON>: terse<PERSON><PERSON><PERSON>", "debug.chunk_boundaries.on": "<PERSON><PERSON><PERSON> perbat<PERSON>n: tampak", "debug.clear_chat.help": "F3 + D = Bersihkan obrolan", "debug.copy_location.help": "F3 + C = Salin lokasi sebagai perintah /tp, tahan F3 + C oentoek memogokkan permaïnan", "debug.copy_location.message": "Lokasi disalin kepapan-klip", "debug.crash.message": "F3 + C sedang ditekan. Permaïnan akan mogok ketjoeali koentji di<PERSON>pas.", "debug.crash.warning": "Mogok dalam %s...", "debug.creative_spectator.error": "Tidak bisa beralih ragam permaïnan karena tidak ada izin", "debug.creative_spectator.help": "F3 + N = <PERSON><PERSON><PERSON> ragam perma<PERSON>nan sebeloemnja <-> ragam penonton", "debug.dump_dynamic_textures": "Tampang dinamis disimpan ke-%s", "debug.dump_dynamic_textures.help": "F3 + S = Simpan tampang dinamis", "debug.gamemodes.error": "Tidak bisa berganti ragam permaïnan karena tidak ada izin", "debug.gamemodes.help": "F3 + F4 = <PERSON><PERSON> peralih ragam perma<PERSON>nan", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s berikoe<PERSON><PERSON>", "debug.help.help": "F3 + Q = Perlihatkan daftar ini", "debug.help.message": "<PERSON><PERSON><PERSON><PERSON><PERSON> koe<PERSON>:", "debug.inspect.client.block": "Data balok dari kliën disalin kepapan-klip", "debug.inspect.client.entity": "Data entitet dari kliën disalin kepapan-klip", "debug.inspect.help": "F3 + I = Salin data entitet atau balok kepapan-klip", "debug.inspect.server.block": "Data balok dari peladen disalin kepapan-klip", "debug.inspect.server.entity": "Data entitet dari peladen disalin kepapan-klip", "debug.pause.help": "F3 + Esc = <PERSON><PERSON><PERSON> tanpa lajar djeda (bila boleh berdjeda)", "debug.pause_focus.help": "F3 + P = <PERSON><PERSON>da sa'at tidak berpoesat perhatian", "debug.pause_focus.off": "Djeda sa'at tidak berpoesat perhatian: mati", "debug.pause_focus.on": "Djeda sa'at tidak berpoesat perhatian: njala", "debug.prefix": "[Lepas-kekoetoe]:", "debug.profiling.help": "F3 + L = Moelai/hentikan pemboeatan profil", "debug.profiling.start": "Pemboeatan profil dimoelai selama %s detik. Gunakan F3 + L oentoek menghentikannja", "debug.profiling.stop": "Pemboeatan profil selesai. <PERSON>il disimpan ke-%s", "debug.reload_chunks.help": "F3 + A = <PERSON>at oelang bingkah", "debug.reload_chunks.message": "Memo<PERSON> oelang semoea bing<PERSON>h", "debug.reload_resourcepacks.help": "F3 + T = <PERSON><PERSON> oelang paket soember", "debug.reload_resourcepacks.message": "Paket soember dimoeat oelang", "debug.show_hitboxes.help": "F3 + B = Tampilkan kotak poekoel", "debug.show_hitboxes.off": "Kotak poekoel: tersemboenji", "debug.show_hitboxes.on": "Kotak poekoel: tampak", "debug.version.header": "Informasi versi kliën:", "debug.version.help": "F3 + V = Informasi versi kliën", "demo.day.1": "Pertjobaän ini akan berdjalan selama lima hari perma<PERSON>. Lakoekan jang terbaïk!", "demo.day.2": "<PERSON>", "demo.day.3": "<PERSON> ketiga", "demo.day.4": "<PERSON> k<PERSON>", "demo.day.5": "Ini adalah hari terachir per<PERSON>!", "demo.day.6": "Toean telah melewati hari kelima. Tekan %s oentoek menjimpan tangkapan lajar dari karja toean.", "demo.day.warning": "Tempo toean hampir habis!", "demo.demoExpired": "Tempo telah habis!", "demo.help.buy": "<PERSON>i sekarang!", "demo.help.fullWrapped": "Pertjobaän ini akan berdjalan selama lima hari per<PERSON> (sekitar 1 jam dan 40 menit tempo njata). <PERSON>iksa kemadjoean oentoek banto<PERSON>! Selamat bersenang-senang!", "demo.help.inventory": "Tekan %1$s oentoek memboeka persedia<PERSON>n toean", "demo.help.jump": "Lompat dengan menekan %1$s", "demo.help.later": "Landjoetkan be<PERSON>ï<PERSON>!", "demo.help.movement": "Pakai %1$s, %2$s, %3$s, %4$s dan tetikoes oentoek bergerak", "demo.help.movementMouse": "<PERSON><PERSON> sekitar dengan teti<PERSON>", "demo.help.movementShort": "Gerak dengan menekan %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft Pertjobaän", "demo.remainingTime": "Tempo tersisa: %s", "demo.reminder": "<PERSON>t<PERSON><PERSON><PERSON><PERSON> telah berachir. <PERSON>i per<PERSON> oentoek melandjoetkan atau boeat doenia baroe!", "difficulty.lock.question": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin mengoentji kesoelitan didoenia ini? %1$s akan mendjadi kesoelitan doenia ini seteroesnja dan toean tidak bisa mengoebah itoe kembali.", "difficulty.lock.title": "<PERSON><PERSON><PERSON>", "disconnect.endOfStream": "<PERSON><PERSON><PERSON>", "disconnect.exceeded_packet_rate": "<PERSON>kel<PERSON><PERSON><PERSON> karena mele<PERSON>hi batas tingkat paket", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Mengabaikan permintaän keada<PERSON>n", "disconnect.loginFailedInfo": "Gagal masoek: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "<PERSON><PERSON><PERSON><PERSON> bersama dimatikan. Mohon periksa penjetelan akoen Microsoft toean.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON> tidak sah (Tjoba djalankan oelang permaïnan dan pelo<PERSON><PERSON><PERSON>)", "disconnect.loginFailedInfo.serversUnavailable": "Peladen autentikasi sa'at ini tidak bisa didjangkau. Mohon tjoba lagi.", "disconnect.loginFailedInfo.userBanned": "<PERSON><PERSON> di<PERSON><PERSON>r dari per<PERSON> da<PERSON> djar<PERSON>n", "disconnect.lost": "Samboengan hilang", "disconnect.packetError": "Kesalahan protokol djaringan", "disconnect.spam": "<PERSON><PERSON><PERSON><PERSON><PERSON> karena <PERSON>", "disconnect.timeout": "Tempo habis", "disconnect.transfer": "Dipindahkan kepeladen laïn", "disconnect.unknownHost": "Inang tidak dikenal", "download.pack.failed": "%s dari %s paket gagal dioendoeh", "download.pack.progress.bytes": "Progresi: %s (oekoeran keseloeroehan ta' diketahoei)", "download.pack.progress.percent": "Progresi: %s%%", "download.pack.title": "Mengoendoeh paket soember %s/%s", "editGamerule.default": "Asali: %s", "editGamerule.title": "<PERSON><PERSON><PERSON>", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Penjerapan", "effect.minecraft.bad_omen": "<PERSON><PERSON><PERSON> b<PERSON>", "effect.minecraft.blindness": "Ke<PERSON><PERSON>ä<PERSON>", "effect.minecraft.conduit_power": "<PERSON><PERSON><PERSON> la<PERSON>t", "effect.minecraft.darkness": "Kegelapan", "effect.minecraft.dolphins_grace": "G<PERSON>oelai lo<PERSON>", "effect.minecraft.fire_resistance": "<PERSON><PERSON> a<PERSON>", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.haste": "Bergegas", "effect.minecraft.health_boost": "<PERSON><PERSON><PERSON>", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON><PERSON> desa", "effect.minecraft.hunger": "<PERSON><PERSON><PERSON>", "effect.minecraft.infested": "<PERSON><PERSON><PERSON> hama", "effect.minecraft.instant_damage": "Tjepat sakit", "effect.minecraft.instant_health": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "effect.minecraft.invisibility": "Limoenan", "effect.minecraft.jump_boost": "Lompat tinggi", "effect.minecraft.levitation": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.luck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "<PERSON><PERSON>", "effect.minecraft.night_vision": "<PERSON><PERSON><PERSON><PERSON> gelap", "effect.minecraft.oozing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.poison": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.raid_omen": "<PERSON><PERSON><PERSON> serb<PERSON>", "effect.minecraft.regeneration": "Regenerasi", "effect.minecraft.resistance": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.saturation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.slowness": "Kelambatan", "effect.minecraft.speed": "Ketjepatan", "effect.minecraft.strength": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.trial_omen": "<PERSON><PERSON><PERSON>", "effect.minecraft.unluck": "Kesialan", "effect.minecraft.water_breathing": "Pernapasa<PERSON>", "effect.minecraft.weakness": "<PERSON><PERSON><PERSON>", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "Teremboes", "effect.minecraft.wither": "<PERSON><PERSON><PERSON><PERSON>", "effect.none": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "<PERSON><PERSON>", "enchantment.minecraft.bane_of_arthropods": "Penoempas artropoda", "enchantment.minecraft.binding_curse": "<PERSON><PERSON><PERSON><PERSON> pengi<PERSON>", "enchantment.minecraft.blast_protection": "<PERSON><PERSON>", "enchantment.minecraft.breach": "Terobos", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.density": "Kepadatan", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "Kesangkilan", "enchantment.minecraft.feather_falling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON> api", "enchantment.minecraft.fire_protection": "<PERSON><PERSON> api", "enchantment.minecraft.flame": "<PERSON><PERSON><PERSON> a<PERSON>", "enchantment.minecraft.fortune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.frost_walker": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "enchantment.minecraft.impaling": "Menikam", "enchantment.minecraft.infinity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.knockback": "<PERSON><PERSON> moendoer", "enchantment.minecraft.looting": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "<PERSON><PERSON>oe<PERSON><PERSON><PERSON> la<PERSON>t", "enchantment.minecraft.lure": "Oempan", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "Tembakan ganda", "enchantment.minecraft.piercing": "Menoesoek", "enchantment.minecraft.power": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "<PERSON><PERSON> te<PERSON>", "enchantment.minecraft.protection": "<PERSON><PERSON><PERSON>nga<PERSON>", "enchantment.minecraft.punch": "<PERSON><PERSON>", "enchantment.minecraft.quick_charge": "<PERSON><PERSON>", "enchantment.minecraft.respiration": "Pernapasan", "enchantment.minecraft.riptide": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.sharpness": "Ke<PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "<PERSON><PERSON><PERSON> soetera", "enchantment.minecraft.smite": "Penoempas bangkai", "enchantment.minecraft.soul_speed": "Ketjepatan djiwa", "enchantment.minecraft.sweeping": "<PERSON><PERSON>", "enchantment.minecraft.sweeping_edge": "<PERSON><PERSON>", "enchantment.minecraft.swift_sneak": "Seloedoek tjepat", "enchantment.minecraft.thorns": "<PERSON><PERSON>", "enchantment.minecraft.unbreaking": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.wind_burst": "<PERSON><PERSON><PERSON> angin", "entity.minecraft.acacia_boat": "<PERSON><PERSON><PERSON> akasia", "entity.minecraft.acacia_chest_boat": "<PERSON><PERSON><PERSON> akasia dengan peti", "entity.minecraft.allay": "Penolong", "entity.minecraft.area_effect_cloud": "Awan efek areal", "entity.minecraft.armadillo": "Armadilo", "entity.minecraft.armor_stand": "Tjagak zirah", "entity.minecraft.arrow": "Panah", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "<PERSON><PERSON><PERSON> bamboe dengan peti", "entity.minecraft.bamboo_raft": "<PERSON><PERSON><PERSON> bamboe", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Lebah", "entity.minecraft.birch_boat": "<PERSON><PERSON><PERSON> berk", "entity.minecraft.birch_chest_boat": "<PERSON><PERSON><PERSON> berk dengan peti", "entity.minecraft.blaze": "Ke<PERSON><PERSON><PERSON>", "entity.minecraft.block_display": "<PERSON><PERSON><PERSON> balok", "entity.minecraft.boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.bogged": "Bengkarak rawa", "entity.minecraft.breeze": "Pawana", "entity.minecraft.breeze_wind_charge": "<PERSON><PERSON> an<PERSON>", "entity.minecraft.camel": "<PERSON><PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cave_spider": "Laba-laba goea", "entity.minecraft.cherry_boat": "<PERSON><PERSON><PERSON> k<PERSON>n", "entity.minecraft.cherry_chest_boat": "<PERSON><PERSON><PERSON> kersen dengan peti", "entity.minecraft.chest_boat": "<PERSON><PERSON><PERSON> den<PERSON> peti", "entity.minecraft.chest_minecart": "<PERSON><PERSON> tambang dengan peti", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "Bakalau", "entity.minecraft.command_block_minecart": "Kereta tambang dengan balok perintah", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "Ke<PERSON>et", "entity.minecraft.creaking_transient": "Ke<PERSON>et", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Perahoe ek gelap", "entity.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON> ek gelap dengan peti", "entity.minecraft.dolphin": "Loemba-loemba", "entity.minecraft.donkey": "Keledai", "entity.minecraft.dragon_fireball": "<PERSON>la api naga", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON>", "entity.minecraft.egg": "<PERSON><PERSON>r te<PERSON>", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON> pend<PERSON>", "entity.minecraft.end_crystal": "Kristal End", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "<PERSON><PERSON>", "entity.minecraft.evoker_fangs": "<PERSON>ring pawang", "entity.minecraft.experience_bottle": "<PERSON><PERSON><PERSON> p<PERSON>a terle<PERSON>ar", "entity.minecraft.experience_orb": "<PERSON><PERSON>", "entity.minecraft.eye_of_ender": "<PERSON>", "entity.minecraft.falling_block": "Balok d<PERSON>", "entity.minecraft.falling_block_type": "%s dja<PERSON><PERSON>", "entity.minecraft.fireball": "<PERSON><PERSON> api", "entity.minecraft.firework_rocket": "Roket kembang api", "entity.minecraft.fishing_bobber": "Pelampoeng pantjingan", "entity.minecraft.fox": "Roebah", "entity.minecraft.frog": "<PERSON><PERSON>", "entity.minecraft.furnace_minecart": "Kereta tambang dengan oven", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "Bing<PERSON> benda sinar", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON><PERSON>-tjo<PERSON>i sinar", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "<PERSON>d<PERSON><PERSON>", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON> riang", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "<PERSON><PERSON> tambang dengan tjorong", "entity.minecraft.horse": "<PERSON><PERSON>", "entity.minecraft.husk": "<PERSON><PERSON><PERSON> kering", "entity.minecraft.illusioner": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.interaction": "Interaksi", "entity.minecraft.iron_golem": "<PERSON><PERSON> besi", "entity.minecraft.item": "<PERSON><PERSON>", "entity.minecraft.item_display": "<PERSON><PERSON><PERSON> benda", "entity.minecraft.item_frame": "<PERSON><PERSON> benda", "entity.minecraft.jungle_boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.jungle_chest_boat": "<PERSON><PERSON><PERSON> rimba dengan peti", "entity.minecraft.killer_bunny": "<PERSON><PERSON><PERSON>", "entity.minecraft.leash_knot": "<PERSON><PERSON><PERSON> tali", "entity.minecraft.lightning_bolt": "Halilintar", "entity.minecraft.lingering_potion": "<PERSON><PERSON><PERSON> be<PERSON>", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "<PERSON><PERSON><PERSON> lama", "entity.minecraft.magma_cube": "Dadoe magma", "entity.minecraft.mangrove_boat": "<PERSON><PERSON><PERSON> bakau", "entity.minecraft.mangrove_chest_boat": "<PERSON><PERSON><PERSON> bakau dengan peti", "entity.minecraft.marker": "<PERSON><PERSON>", "entity.minecraft.minecart": "<PERSON><PERSON> tambang", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "Bagal", "entity.minecraft.oak_boat": "Perahoe ek", "entity.minecraft.oak_chest_boat": "<PERSON>ah<PERSON> ek dengan peti", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Pembangkit benda seram", "entity.minecraft.painting": "Loekisan", "entity.minecraft.pale_oak_boat": "Perahoe ek <PERSON>t", "entity.minecraft.pale_oak_chest_boat": "Perahoe ek poet<PERSON>t dengan peti", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Bajan", "entity.minecraft.phantom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.pig": "<PERSON>bi", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON> ganas", "entity.minecraft.pillager": "Pendjarah", "entity.minecraft.player": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.potion": "Ramoean", "entity.minecraft.pufferfish": "<PERSON><PERSON> b<PERSON>", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Pembinasa", "entity.minecraft.salmon": "Salem", "entity.minecraft.sheep": "Domba", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "Gegat", "entity.minecraft.skeleton": "Bengkara<PERSON>", "entity.minecraft.skeleton_horse": "<PERSON><PERSON>", "entity.minecraft.slime": "G<PERSON><PERSON>", "entity.minecraft.small_fireball": "<PERSON><PERSON> api ketjil", "entity.minecraft.sniffer": "Pengendoes", "entity.minecraft.snow_golem": "<PERSON><PERSON> sa<PERSON>", "entity.minecraft.snowball": "<PERSON><PERSON>", "entity.minecraft.spawner_minecart": "<PERSON><PERSON> tambang dengan pembangkit monster", "entity.minecraft.spectral_arrow": "<PERSON><PERSON>", "entity.minecraft.spider": "Laba-laba", "entity.minecraft.splash_potion": "<PERSON><PERSON><PERSON> t<PERSON>", "entity.minecraft.spruce_boat": "<PERSON>ahoe spar", "entity.minecraft.spruce_chest_boat": "<PERSON><PERSON><PERSON> spar dengan peti", "entity.minecraft.squid": "<PERSON><PERSON><PERSON><PERSON>-t<PERSON><PERSON><PERSON>", "entity.minecraft.stray": "Bengkara<PERSON> saldjoe", "entity.minecraft.strider": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tadpole": "Be<PERSON>ed<PERSON>", "entity.minecraft.text_display": "Tampilan teks", "entity.minecraft.tnt": "TNT tersoeloet", "entity.minecraft.tnt_minecart": "Kereta tambang dengan TNT", "entity.minecraft.trader_llama": "<PERSON>", "entity.minecraft.trident": "Trisoela", "entity.minecraft.tropical_fish": "<PERSON><PERSON> tropis", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON> anemon", "entity.minecraft.tropical_fish.predefined.1": "Botana hitam", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON>-kepe hias", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON> bajan", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON><PERSON><PERSON>oe", "entity.minecraft.tropical_fish.predefined.14": "Cichlid merah", "entity.minecraft.tropical_fish.predefined.15": "<PERSON><PERSON>i bibir merah", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON> bad<PERSON> tomat", "entity.minecraft.tropical_fish.predefined.19": "<PERSON><PERSON> aja<PERSON>-ajam", "entity.minecraft.tropical_fish.predefined.2": "Botana biroe", "entity.minecraft.tropical_fish.predefined.20": "<PERSON><PERSON> bajan ekor koening", "entity.minecraft.tropical_fish.predefined.21": "<PERSON> koening", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON>-kepe", "entity.minecraft.tropical_fish.predefined.4": "Cichlid", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Tjoepang kembang goela", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON> panter", "entity.minecraft.tropical_fish.predefined.8": "Kakap gadjah", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "<PERSON><PERSON> kotak", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON> tanah liat", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "Pendje<PERSON>", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Pengintai", "entity.minecraft.tropical_fish.type.spotty": "Bertotol", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "<PERSON><PERSON> mentari", "entity.minecraft.turtle": "<PERSON><PERSON><PERSON>", "entity.minecraft.vex": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager": "Pendoedoek", "entity.minecraft.villager.armorer": "Toekang zirah", "entity.minecraft.villager.butcher": "Toekang djagal", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "Pendet<PERSON>", "entity.minecraft.villager.farmer": "<PERSON><PERSON>", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "Toekang panah", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON> koelit", "entity.minecraft.villager.librarian": "Poestakawan", "entity.minecraft.villager.mason": "Toekang batoe", "entity.minecraft.villager.nitwit": "<PERSON><PERSON>", "entity.minecraft.villager.none": "Pendoedoek", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "Toekang alat", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON><PERSON>", "entity.minecraft.vindicator": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wandering_trader": "<PERSON><PERSON> k<PERSON>", "entity.minecraft.warden": "Penoenggoe", "entity.minecraft.wind_charge": "<PERSON><PERSON> an<PERSON>", "entity.minecraft.witch": "<PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON><PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON> z<PERSON>i", "entity.minecraft.zombie_villager": "<PERSON><PERSON><PERSON> pen<PERSON>", "entity.minecraft.zombified_piglin": "<PERSON><PERSON><PERSON> piglin", "entity.not_summonable": "Entitet berdjenis %s tidak bisa dipanggil", "event.minecraft.raid": "Serboean", "event.minecraft.raid.defeat": "<PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Serboean - <PERSON><PERSON>", "event.minecraft.raid.raiders_remaining": "Penjerboe tersisa: %s", "event.minecraft.raid.victory": "<PERSON><PERSON>", "event.minecraft.raid.victory.full": "Serboean - <PERSON><PERSON>", "filled_map.buried_treasure": "<PERSON>a harta karoen", "filled_map.explorer_jungle": "<PERSON><PERSON>", "filled_map.explorer_swamp": "<PERSON><PERSON> rawa", "filled_map.id": "Peta ke-%s", "filled_map.level": "(Tingkat %s/%s)", "filled_map.locked": "Terk<PERSON><PERSON><PERSON>", "filled_map.mansion": "<PERSON><PERSON> hoetan", "filled_map.monument": "<PERSON><PERSON> la<PERSON>", "filled_map.scale": "Skala 1:%s", "filled_map.trial_chambers": "<PERSON><PERSON> bi<PERSON> tjo<PERSON>n", "filled_map.unknown": "<PERSON>a ta' dikenal", "filled_map.village_desert": "<PERSON>a desa goeroen", "filled_map.village_plains": "Peta desa dataran", "filled_map.village_savanna": "Peta desa sabana", "filled_map.village_snowy": "<PERSON><PERSON> desa be<PERSON>", "filled_map.village_taiga": "Peta desa taiga", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>t", "flat_world_preset.minecraft.classic_flat": "<PERSON><PERSON><PERSON> k<PERSON>ik", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.redstone_ready": "Siap redstone", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.the_void": "<PERSON><PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "Mimpi <PERSON>ambang", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON> a<PERSON>", "flat_world_preset.unknown": "???", "gameMode.adventure": "<PERSON><PERSON> pet<PERSON>", "gameMode.changed": "%s adalah ragam perma<PERSON> se<PERSON>ng", "gameMode.creative": "<PERSON><PERSON> kreatif", "gameMode.hardcore": "<PERSON><PERSON> menant<PERSON>", "gameMode.spectator": "<PERSON><PERSON> penonton", "gameMode.survival": "<PERSON><PERSON> bertahan hidoep", "gamerule.allowFireTicksAwayFromPlayer": "<PERSON><PERSON> be<PERSON><PERSON> d<PERSON><PERSON> da<PERSON> p<PERSON>", "gamerule.allowFireTicksAwayFromPlayer.description": "Mengendalikan apakah api dan lava dapat bertik pada djarak lebih dari 8 bingkah dari setiap pemaïn", "gamerule.announceAdvancements": "Oemoemkan kema<PERSON>", "gamerule.blockExplosionDropDecay": "Beberapa balok tidak akan mendjatoehkan djarahan ledakan interaksi balok", "gamerule.blockExplosionDropDecay.description": "Beberapa djatoehan dari balok jang dihantjoerkan oleh ledakan jang disebabkan oleh interaksi balok hilang dalam ledakan", "gamerule.category.chat": "<PERSON><PERSON>lan", "gamerule.category.drops": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.misc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.category.mobs": "Machloek", "gamerule.category.player": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Pembangkitan", "gamerule.category.updates": "Pembaharoean doenia", "gamerule.commandBlockOutput": "Siark<PERSON> kelo<PERSON>an balok perintah", "gamerule.commandModificationBlockLimit": "Batas balok modifikasi perintah", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON><PERSON> balok jang dapat dioebah sekaligoes dengan satu perintah, seperti fill atau clone.", "gamerule.disableElytraMovementCheck": "<PERSON><PERSON><PERSON> perik<PERSON> pergerakan élitron", "gamerule.disablePlayerMovementCheck": "<PERSON><PERSON><PERSON> per<PERSON> per<PERSON>akan p<PERSON>n", "gamerule.disableRaids": "<PERSON><PERSON><PERSON> se<PERSON>an", "gamerule.doDaylightCycle": "<PERSON><PERSON><PERSON><PERSON> waktoe", "gamerule.doEntityDrops": "Djatoehkan peralatan entitet", "gamerule.doEntityDrops.description": "Mengendalikan dja<PERSON><PERSON> dari kere<PERSON> (termasuk <PERSON>), bin<PERSON><PERSON>, per<PERSON><PERSON> dll.", "gamerule.doFireTick": "Per<PERSON>aro<PERSON> api", "gamerule.doImmediateRespawn": "Bangkit kembali seketika", "gamerule.doInsomnia": "Bangkitkan angan-angan", "gamerule.doLimitedCrafting": "Memerloekan resep oentoek memboeat keradjinan", "gamerule.doLimitedCrafting.description": "<PERSON><PERSON>, p<PERSON><PERSON>n hanja bisa memboeat benda dengan resep jang so<PERSON>h terb<PERSON>ka", "gamerule.doMobLoot": "Dja<PERSON>eh<PERSON> d<PERSON>", "gamerule.doMobLoot.description": "Mengendalikan d<PERSON><PERSON><PERSON> soember-daja da<PERSON>, <PERSON><PERSON><PERSON><PERSON> bola <PERSON>.", "gamerule.doMobSpawning": "Bangkitkan machloek", "gamerule.doMobSpawning.description": "Beberapa entitet moengkin poenja atoeran terpisah.", "gamerule.doPatrolSpawning": "Bangkitkan ronda pendjarah", "gamerule.doTileDrops": "Djatoehkan balok", "gamerule.doTileDrops.description": "Mengendal<PERSON><PERSON> d<PERSON><PERSON><PERSON> soember-daja dari balok, <PERSON><PERSON><PERSON><PERSON> bola pen<PERSON>.", "gamerule.doTraderSpawning": "Bangkitkan handelar keliling", "gamerule.doVinesSpread": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>", "gamerule.doVinesSpread.description": "Mengendalikan apakah balok toemboehan merambat menjebar setjara atjak kebalok jang bersebelahan. <PERSON><PERSON><PERSON> be<PERSON>, melilit dan sebagainja tidak terpeng<PERSON>eh.", "gamerule.doWardenSpawning": "Bangkitkan penoenggoe", "gamerule.doWeatherCycle": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.drowningDamage": "Dapat mati tenggelam", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON> te<PERSON> akan hilang sa'at mati", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON><PERSON><PERSON><PERSON> moetiara <PERSON>er jang <PERSON> o<PERSON>h pemaïn hilang ketika pemaïn mati.", "gamerule.entitiesWithPassengersCanUsePortals": "Entitet dengan penoempang dapat memakai gerbang", "gamerule.entitiesWithPassengersCanUsePortals.description": "Memoengkinkan entitet dengan penoempang oentoek berteleportir melaloei ger<PERSON>, gerbang End dan gapoera <PERSON>.", "gamerule.fallDamage": "<PERSON><PERSON>", "gamerule.fireDamage": "<PERSON><PERSON> baka<PERSON>", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON><PERSON><PERSON> pema<PERSON>n jang soedah mati", "gamerule.forgiveDeadPlayers.description": "Machloek netral jang terprovokasi berhenti mengedjar ketika pemaïn intjaran mati didekat<PERSON>.", "gamerule.freezeDamage": "<PERSON><PERSON> bekoe", "gamerule.globalSoundEvents": "<PERSON><PERSON><PERSON> semesta", "gamerule.globalSoundEvents.description": "<PERSON><PERSON><PERSON> peristiwa permaïnan te<PERSON> te<PERSON>, se<PERSON>i pembang<PERSON> baas, so<PERSON><PERSON><PERSON> terdengar dimana poen.", "gamerule.keepInventory": "<PERSON><PERSON><PERSON> per<PERSON><PERSON><PERSON><PERSON> set<PERSON>h mati", "gamerule.lavaSourceConversion": "<PERSON><PERSON> be<PERSON><PERSON> mend<PERSON> soember", "gamerule.lavaSourceConversion.description": "Ketika lava mengalir dikelilingi doea sisi oleh soember lava, ia beroebah mendjadi soember.", "gamerule.locatorBar": "<PERSON><PERSON><PERSON><PERSON> bilah p<PERSON> pema<PERSON>", "gamerule.locatorBar.description": "<PERSON><PERSON><PERSON>, akan menampilkan bilah jang menoendjoekkan arah pema<PERSON>n.", "gamerule.logAdminCommands": "<PERSON>ark<PERSON> perintah pengelola", "gamerule.maxCommandChainLength": "<PERSON>as o<PERSON> rantai perintah", "gamerule.maxCommandChainLength.description": "Diterapkan pada rantai dan roetin balok perintah.", "gamerule.maxCommandForkCount": "Batas konteks perintah", "gamerule.maxCommandForkCount.description": "<PERSON><PERSON><PERSON><PERSON> maksimal konteks jang bisa dipakai oleh perintah seperti „execute as”.", "gamerule.maxEntityCramming": "Batas entitet tergentjet", "gamerule.minecartMaxSpeed": "Ketjepatan maksimal kereta tambang", "gamerule.minecartMaxSpeed.description": "Ketjepatan maksimal asali oentoek kereta tambang jang bergerak diatas tanah", "gamerule.mobExplosionDropDecay": "Beberapa machloek tidak akan mendjatoehkan djarahan ledakan interaksi machloek", "gamerule.mobExplosionDropDecay.description": "Beberapa djatoehan dari balok jang dihantjoerkan oleh ledakan jang disebabkan oleh machloek hilang dalam ledakan.", "gamerule.mobGriefing": "Izinkan tindakan machloek peroesak", "gamerule.naturalRegeneration": "Regenerasi njawa", "gamerule.playersNetherPortalCreativeDelay": "<PERSON><PERSON><PERSON>bat<PERSON> gerbang <PERSON>her pemaïn diragam kreatif", "gamerule.playersNetherPortalCreativeDelay.description": "Tempo (dalam tik) pemaïn dalam ragam kreatif oentoek berdiri digerbang Nether sebeloem berpindah dimensi.", "gamerule.playersNetherPortalDefaultDelay": "<PERSON><PERSON><PERSON><PERSON><PERSON> gerbang <PERSON>her pemaïn ragam non<PERSON>f", "gamerule.playersNetherPortalDefaultDelay.description": "Tempo (dalam tik) pemaïn ragam nonkreatif oentoek berdiri digerbang Nether sebeloem berpindah dimensi.", "gamerule.playersSleepingPercentage": "Prosentase tidoer", "gamerule.playersSleepingPercentage.description": "Prosentase pemaïn jang haroes tidoer oentoek melewati malam.", "gamerule.projectilesCanBreakBlocks": "<PERSON>je<PERSON><PERSON> dapat men<PERSON> balok", "gamerule.projectilesCanBreakBlocks.description": "Mengedalikan apakah projektil dampak akan menghantjoerkan balok jang dapat dihantjoerkan oleh projektil itoe.", "gamerule.randomTickSpeed": "Tingkat ketjepatan tik atjak", "gamerule.reducedDebugInfo": "Koerangi informasi lepas-kekoetoe", "gamerule.reducedDebugInfo.description": "Membatasi isi dari lajar lepas-kekoetoe", "gamerule.sendCommandFeedback": "<PERSON><PERSON><PERSON>-b<PERSON><PERSON>ah", "gamerule.showDeathMessages": "<PERSON><PERSON><PERSON><PERSON> pesan kematian", "gamerule.snowAccumulationHeight": "Ketinggian toe<PERSON>ekan saldjoe", "gamerule.snowAccumulationHeight.description": "<PERSON>'at toe<PERSON><PERSON> saldjoe, lapisan saldjoe terbentoek ditanah hingga sebanjak ini.", "gamerule.spawnChunkRadius": "Djari² bingkah bangkitan", "gamerule.spawnChunkRadius.description": "<PERSON><PERSON><PERSON><PERSON> bingkah jang tetap dimoeat disekitar posisi bangkit di-Permoekaän.", "gamerule.spawnRadius": "Djari² lokasi pembangkitan", "gamerule.spawnRadius.description": "Mengendalikan oekoeran areal disekitar titik bangkit dimana pemaïn dapat moentjoel.", "gamerule.spectatorsGenerateChunks": "Izinkan penonton oentoek memboeat medan", "gamerule.tntExplodes": "Izinkan <PERSON>n dan pele<PERSON> TNT", "gamerule.tntExplosionDropDecay": "Beberapa balok tidak akan mendjatoehkan djarahan ledakan TNT", "gamerule.tntExplosionDropDecay.description": "Beberapa djatoehan dari balok jang dihantjoerkan oleh ledakan jang disebabkan oleh TNT hilang dalam ledakan.", "gamerule.universalAnger": "<PERSON><PERSON>", "gamerule.universalAnger.description": "Machloek netral jang diserang akan menjerang pemaïn terdekat siapa sadja, ta' hanja pemaïn jang menjerang mereka. Ini bekerdja lebih baïk ketika forgiveDeadPlayers mati.", "gamerule.waterSourceConversion": "<PERSON><PERSON><PERSON>", "gamerule.waterSourceConversion.description": "<PERSON><PERSON><PERSON> a<PERSON><PERSON> men<PERSON> doea sisi oleh soember aïr, ia be<PERSON><PERSON><PERSON> mend<PERSON>di soember.", "generator.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generator.customized": "Oebah-soeaian lama", "generator.minecraft.amplified": "DIPERKOEAT", "generator.minecraft.amplified.info": "Perhatian: hanja oentoek kesenangan! Memerloekan perangkat jang koeat.", "generator.minecraft.debug_all_block_states": "lepas-kekoetoe", "generator.minecraft.flat": "maharata", "generator.minecraft.large_biomes": "bioom besar", "generator.minecraft.normal": "asali", "generator.minecraft.single_biome_surface": "bioom toenggal", "generator.single_biome_caves": "Goea", "generator.single_biome_floating_islands": "<PERSON><PERSON>", "gui.abuseReport.attestation": "Dengan mengiri<PERSON>kan laporan ini, toean mengon<PERSON> bahwa informasi jang telah toean berikan adalah tepat dan lengkap sesoeai dengan pengetahoean toean.", "gui.abuseReport.comments": "Komentar", "gui.abuseReport.describe": "<PERSON>i tahoe per<PERSON>an akan membantoe kami memboeat kepoetoesan jang ba<PERSON>.", "gui.abuseReport.discard.content": "<PERSON><PERSON> <PERSON>an pergi, toean akan kehilangan laporan ini dan komentar toean.\nTo<PERSON> jakin ingin pergi?", "gui.abuseReport.discard.discard": "Tingg<PERSON>an dan boeang laporan", "gui.abuseReport.discard.draft": "Simpan sebagai boeram", "gui.abuseReport.discard.return": "Land<PERSON><PERSON><PERSON> menjo<PERSON>", "gui.abuseReport.discard.title": "<PERSON><PERSON>g laporan dan komentar?", "gui.abuseReport.draft.content": "<PERSON><PERSON><PERSON><PERSON> toean ingin landjoet menjoenting laporan jang ada atau memboeangnja dan memboeat jang baroe?", "gui.abuseReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.abuseReport.draft.edit": "Land<PERSON><PERSON><PERSON> menjo<PERSON>", "gui.abuseReport.draft.quittotitle.content": "<PERSON><PERSON><PERSON><PERSON> toean ingin landjoet menjoenting laporan atau memboeangnja?", "gui.abuseReport.draft.quittotitle.title": "Toean poenja boeram laporan obrolan jang akan hilang bila toean keloear", "gui.abuseReport.draft.title": "Soenting boeram laporan obrolan?", "gui.abuseReport.error.title": "<PERSON> masalah dalam mengirim laporan toean", "gui.abuseReport.message": "<PERSON><PERSON> toean mengamati perilakoe boeroek tersebut?\nIni akan membantoe kami dalam meneliti laporan toean.", "gui.abuseReport.more_comments": "<PERSON><PERSON> d<PERSON>kan apa jang terdjadi:", "gui.abuseReport.name.comment_box_label": "<PERSON><PERSON> dje<PERSON>kan mengapa toean ingin melaporkan nama ini:", "gui.abuseReport.name.reporting": "<PERSON><PERSON> melap<PERSON>kan „%s”.", "gui.abuseReport.name.title": "Laporkan <PERSON>", "gui.abuseReport.observed_what": "Mengapa toean melaporkan ini?", "gui.abuseReport.read_info": "Peladjari tentang pelaporan", "gui.abuseReport.reason.alcohol_tobacco_drugs": "<PERSON>bat verboden atau alkohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Seseorang mendorong orang laïn oentoek ikoet serta dalam kegiatan terkaït obat²-an verboden atau mendorong minoem alkohol pada oesia bawah-oemoer.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Eksploitasi atau peletjehan seksoeil terhadap anak", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Seseorang berbitjara tentang atau mempromosikan perilakoe ta' senonoh jang melibatkan anak².", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON><PERSON> na<PERSON> ba<PERSON>", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Seseorang mentjemarkan nama baïk orang laïn, seperti berbagi informasi palsoe dengan toedjoean oentoek mengeksploitasi atau memperdaja orang laïn.", "gui.abuseReport.reason.description": "Deskripsi:", "gui.abuseReport.reason.false_reporting": "Pelaporan palsoe", "gui.abuseReport.reason.generic": "<PERSON>ja ingin me<PERSON>", "gui.abuseReport.reason.generic.description": "Saja terganggoe oleh orang ini / orang ini telah melakoekan sesoeatoe jang tidak saja soeka.", "gui.abuseReport.reason.harassment_or_bullying": "<PERSON><PERSON><PERSON><PERSON><PERSON> atau per<PERSON>", "gui.abuseReport.reason.harassment_or_bullying.description": "Seseorang mempermal<PERSON>kan, menjerang atau meroendoeng toean atau orang laïn. Ini termasoek ketika seseorang beroelang kali mentjoba menghoeboengi toean atau orang laïn tanpa persetoedjoean atau mengoenggah informasi pribadi tentang toean atau orang laïn tanpa persetoedjoean („doksing”).", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.hate_speech.description": "Seseorang menjerang toean atau pemaïn la<PERSON>n be<PERSON> tjiri dari djati diri mere<PERSON>, se<PERSON>i agama, ras atau tjiri seks.", "gui.abuseReport.reason.imminent_harm": "An<PERSON>jaman oentoek meloekaï orang <PERSON>ïn", "gui.abuseReport.reason.imminent_harm.description": "Seseorang mengantjam oentoek menjakiti toean atau orang laïn dalam kehidoepan njata.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "G<PERSON><PERSON> intim tanpa izin", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Seseorang berbitjara tentang, berbagi atau mempromosikan gambar pribadi dan intim.", "gui.abuseReport.reason.self_harm_or_suicide": "Meloekaï diri sendiri atau boenoeh diri", "gui.abuseReport.reason.self_harm_or_suicide.description": "Seseorang mengantjam oentoek meloekaï diri mereka sendiri dalam kehidoepan njata, atau berbitjara tentang meloekaï diri mereka sendiri dalam kehidoepan njata.", "gui.abuseReport.reason.sexually_inappropriate": "Konten seksoeil", "gui.abuseReport.reason.sexually_inappropriate.description": "<PERSON><PERSON> jang mengan<PERSON> o<PERSON> aksi, organ dan kek<PERSON>san seksoeil.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terorisme atau kekerasan ekstremis", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Seseorang berbitjara tentang, mempromosikan atau mengantjam melakoekan tindakan terorisme atau kekerasan ekstremis karena alasan politis, agama, ideologis atau laïnnja.", "gui.abuseReport.reason.title": "<PERSON><PERSON><PERSON>", "gui.abuseReport.report_sent_msg": "<PERSON><PERSON> be<PERSON><PERSON> men<PERSON>ma <PERSON>an. <PERSON><PERSON> kasih!\n\n<PERSON> kami akan menindjaunja sesegera moe<PERSON>.", "gui.abuseReport.select_reason": "<PERSON><PERSON><PERSON> kate<PERSON>i <PERSON>", "gui.abuseReport.send": "<PERSON><PERSON>", "gui.abuseReport.send.comment_too_long": "<PERSON><PERSON> memper<PERSON>kat komentar", "gui.abuseReport.send.error_message": "<PERSON><PERSON><PERSON><PERSON> kesalahan sa'at mengirim laporan toean: „%s”", "gui.abuseReport.send.generic_error": "Kesalahan ta' terdoega ditemoekan sa'at mengirim laporan toean.", "gui.abuseReport.send.http_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han HTTP ta' terdoega sa'at mengirim laporan toean.", "gui.abuseReport.send.json_error": "Moeatan tjatjat ditemoekan sa'at mengirim laporan toean.", "gui.abuseReport.send.no_reason": "<PERSON><PERSON> pilih kategori laporan", "gui.abuseReport.send.not_attested": "Mohon batja teks diatas dan tjentang kotak soepaja dapat mengirimkan laporan", "gui.abuseReport.send.service_unavailable": "Tidak bisa mentjapai lajanan pelaporan penja<PERSON>n. Mohon pastikan toean tersamboeng kedjaringan dan tjoba lagi.", "gui.abuseReport.sending.title": "Mengirim laporan toean...", "gui.abuseReport.sent.title": "Laporan terkirim", "gui.abuseReport.skin.title": "Laporkan Roepa Pemaïn", "gui.abuseReport.title": "Laporkan Pemaïn", "gui.abuseReport.type.chat": "<PERSON><PERSON> o<PERSON>", "gui.abuseReport.type.name": "<PERSON><PERSON>", "gui.abuseReport.type.skin": "<PERSON><PERSON>", "gui.acknowledge": "<PERSON><PERSON>", "gui.advancements": "<PERSON><PERSON><PERSON><PERSON>", "gui.all": "Se<PERSON><PERSON>", "gui.back": "Kembali", "gui.banned.description": "%s\n\n%s\n\nPeladjari lebih landjoet ditaoetan berikoet: %s", "gui.banned.description.permanent": "<PERSON><PERSON><PERSON> toean di<PERSON> set<PERSON> per<PERSON>, jang berarti toean tidak bisa bermaïn dalam djaringan atau bergaboeng dengan Realms.", "gui.banned.description.reason": "Kami baroe<PERSON> ini menerima laporan atas perilakoe boeroek dari akoen toean. Moderator kami sekarang telah menindjau kasoes toean dan mengidentifikasinja sebagai %s, jang bertentangan dengan standar masjarakat Minecraft.", "gui.banned.description.reason_id": "Kode: %s", "gui.banned.description.reason_id_message": "Kode: %s - %s", "gui.banned.description.temporary": "%s <PERSON><PERSON><PERSON>'at itoe, toean tidak bisa berma<PERSON>n dalam djaringan atau bergaboeng dengan Realms.", "gui.banned.description.temporary.duration": "A<PERSON><PERSON> toean ditangg<PERSON>hkan sementara dan akan dinjalakan kembali dalam %s.", "gui.banned.description.unknownreason": "Kami baroe² ini menerima laporan atas perilakoe boeroek dari akoen toean. Moderator kami sekarang telah menindjau kasoes toean dan mengidentifikasi bahwa itoe bertentangan dengan standar masjarakat Minecraft.", "gui.banned.name.description": "<PERSON>a toean sa'at ini—„%s”—melanggar standar masjarakat kami. To<PERSON> dapat berma<PERSON>n sendiri, tetapi haroes mengoebah nama toean oentoek bermaïn dalam djaringan.\n\nPeladjari lebih landjoet atau kirimkan tindjauan masalah ditaoetan berikoet: %s", "gui.banned.name.title": "<PERSON><PERSON> Diïzin<PERSON> dalam <PERSON>", "gui.banned.reason.defamation_impersonation_false_information": "Meniroe atau berbagi informasi oentoek mengeksploitasi atau memperdaja orang laïn", "gui.banned.reason.drugs": "<PERSON><PERSON><PERSON><PERSON><PERSON> terhadap obat²-an terlarang", "gui.banned.reason.extreme_violence_or_gore": "<PERSON><PERSON><PERSON><PERSON> kek<PERSON>san be<PERSON> atau pertoempahan darah didoenia njata", "gui.banned.reason.false_reporting": "<PERSON>poran palsoe atau ta' tepat jang berlebih", "gui.banned.reason.fraud": "<PERSON><PERSON><PERSON> atau pemakaian bahasan jang bersifat menipoe", "gui.banned.reason.generic_violation": "<PERSON><PERSON><PERSON> standar masjarakat", "gui.banned.reason.harassment_or_bullying": "Bahasa kasar jang dito<PERSON><PERSON>n pada seseorang setjara berbahaja", "gui.banned.reason.hate_speech": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>an at<PERSON> disk<PERSON>", "gui.banned.reason.hate_terrorism_notorious_figure": "<PERSON><PERSON><PERSON><PERSON><PERSON> ter<PERSON><PERSON> kelo<PERSON> keb<PERSON>, organisasi teroris atau orang djahat", "gui.banned.reason.imminent_harm_to_person_or_property": "Maksoed oentoek membahajakan orang atau kepemilikan doenia njata", "gui.banned.reason.nudity_or_pornography": "Menampilkan material tjaboel", "gui.banned.reason.sexually_inappropriate": "Topik atau bahasan jang bersifat seksoeil", "gui.banned.reason.spam_or_advertising": "Iklan atau pesan jang bersifat membandjiri", "gui.banned.skin.description": "<PERSON><PERSON> toean sa'at ini melanggar standar masjarakat kami. <PERSON>ean masih dapat berma<PERSON>n dengan roepa asali, atau memilih roepa baroe.\n\nPeladjari lebih landjoet atau kirimkan tindjauan masalah ditaoetan berikoet: %s", "gui.banned.skin.title": "Roepa Ta' Diïzinkan", "gui.banned.title.permanent": "<PERSON><PERSON><PERSON> di<PERSON>r permanen", "gui.banned.title.temporary": "A<PERSON>en ditangg<PERSON>kan sementara", "gui.cancel": "Batalkan", "gui.chatReport.comments": "Komentar", "gui.chatReport.describe": "<PERSON>i tahoe per<PERSON>jian akan membantu kami memboeat kepoetoesan jang ba<PERSON>.", "gui.chatReport.discard.content": "<PERSON><PERSON> <PERSON>an pergi, toean akan kehilangan laporan ini dan komentar toean.\nTo<PERSON> jakin ingin pergi?", "gui.chatReport.discard.discard": "Tingg<PERSON>an dan boeang laporan", "gui.chatReport.discard.draft": "Simpan sebagai boeram", "gui.chatReport.discard.return": "Land<PERSON><PERSON><PERSON> menjo<PERSON>", "gui.chatReport.discard.title": "<PERSON><PERSON>g laporan dan komentar?", "gui.chatReport.draft.content": "<PERSON><PERSON><PERSON><PERSON> toean ingin landjoet menjoenting laporan jang ada atau memboeangnja dan memboeat jang baroe?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "Land<PERSON><PERSON><PERSON> menjo<PERSON>", "gui.chatReport.draft.quittotitle.content": "<PERSON><PERSON><PERSON><PERSON> toean ingin landjoet menjoenting laporan atau memboeangnja?", "gui.chatReport.draft.quittotitle.title": "Toean poenja boeram laporan obrolan jang akan hilang bila toean keloear", "gui.chatReport.draft.title": "Soenting Boeram Lapor<PERSON>?", "gui.chatReport.more_comments": "<PERSON><PERSON> d<PERSON>kan apa jang terdjadi:", "gui.chatReport.observed_what": "Mengapa toean melaporkan ini?", "gui.chatReport.read_info": "Peladjari tentang pelaporan", "gui.chatReport.report_sent_msg": "<PERSON><PERSON> be<PERSON><PERSON> men<PERSON>ma <PERSON>an. <PERSON><PERSON> kasih!\n\n<PERSON> kami akan menindjaunja sesegera moe<PERSON>.", "gui.chatReport.select_chat": "<PERSON><PERSON><PERSON> pesan obrolan oentoek dilaporlan", "gui.chatReport.select_reason": "<PERSON><PERSON><PERSON> kate<PERSON>i <PERSON>", "gui.chatReport.selected_chat": "%s pesan obrolan dipilih oentoek dilaporkan", "gui.chatReport.send": "<PERSON><PERSON>", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON> memper<PERSON>kat komentar", "gui.chatReport.send.no_reason": "<PERSON><PERSON> pilih kategori laporan", "gui.chatReport.send.no_reported_messages": "Mohon pilih setidaknja satoe pesan obrolan oentoek dilaporkan", "gui.chatReport.send.too_many_messages": "<PERSON><PERSON><PERSON><PERSON> banjak pesan dalam laporan", "gui.chatReport.title": "Laporkan Obrolan Pemaïn", "gui.chatSelection.context": "Pesan sepoetar pilihan ini akan disertakan oentoek memberikan konteks tambahan", "gui.chatSelection.fold": "%s pesan disemboenjikan", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s bergaboeng dalam obrolan", "gui.chatSelection.message.narrate": "%s berkata: %s pada %s", "gui.chatSelection.selected": "%s/%s pesan dipilih", "gui.chatSelection.title": "<PERSON><PERSON><PERSON> Obrolan oentoek Dilaporkan", "gui.continue": "Landjoetkan", "gui.copy_link_to_clipboard": "<PERSON>in taoetan kepapan-klip", "gui.days": "%s hari", "gui.done": "Se<PERSON><PERSON>", "gui.down": "<PERSON>wa<PERSON>", "gui.entity_tooltip.type": "Djenis: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s berkas ditolak", "gui.fileDropFailure.title": "<PERSON><PERSON><PERSON> gagal di<PERSON>", "gui.hours": "%s djam", "gui.loadingMinecraft": "Memuat Minecraft", "gui.minutes": "%s menit", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Kenop %s", "gui.narrate.editBox": "Kotak sunting %s: %s", "gui.narrate.slider": "Penggeser %s", "gui.narrate.tab": "Jendela %s", "gui.no": "Tidak", "gui.none": "Tidak ada", "gui.ok": "Baïk", "gui.open_report_dir": "Boeka map laporan", "gui.proceed": "Landjoetkan", "gui.recipebook.moreRecipes": "Pentjet-kanan oentoek selengkapnja", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Tjari...", "gui.recipebook.toggleRecipes.all": "Menampilkan semoea", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON>", "gui.recipebook.toggleRecipes.craftable": "<PERSON><PERSON>", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON>", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON> di<PERSON>", "gui.report_to_server": "Laporkan kepeladen", "gui.socialInteractions.blocking_hint": "<PERSON><PERSON><PERSON> dengan akoen <PERSON>", "gui.socialInteractions.empty_blocked": "Tidak ada pema<PERSON>n terb<PERSON><PERSON>r <PERSON>obrolan", "gui.socialInteractions.empty_hidden": "Tidak ada pema<PERSON>n terse<PERSON><PERSON><PERSON> diobrolan", "gui.socialInteractions.hidden_in_chat": "Pesan obrolan dari %s akan disemboenjikan", "gui.socialInteractions.hide": "Semboenjikan diobrolan", "gui.socialInteractions.narration.hide": "Semboenjikan pesan dari %s", "gui.socialInteractions.narration.report": "Laporkan pemaïn %s", "gui.socialInteractions.narration.show": "Tampilkan pesan dari %s", "gui.socialInteractions.report": "Laporkan", "gui.socialInteractions.search_empty": "Tidak bisa menemoekan pemaïn dengan nama itoe", "gui.socialInteractions.search_hint": "Tjari...", "gui.socialInteractions.server_label.multiple": "%s - %s pemaïn", "gui.socialInteractions.server_label.single": "%s - %s pemaïn", "gui.socialInteractions.show": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.shown_in_chat": "Pesan obrolan dari %s akan ditampilkan", "gui.socialInteractions.status_blocked": "Diblokir", "gui.socialInteractions.status_blocked_offline": "<PERSON><PERSON><PERSON><PERSON>r <PERSON> <PERSON><PERSON>", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Tersemboenji - <PERSON><PERSON>", "gui.socialInteractions.status_offline": "<PERSON><PERSON>", "gui.socialInteractions.tab_all": "Se<PERSON><PERSON>", "gui.socialInteractions.tab_blocked": "Diblokir", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "Interaksi Sosial", "gui.socialInteractions.tooltip.hide": "Semboenjikan pesan", "gui.socialInteractions.tooltip.report": "Laporkan p<PERSON>ïn", "gui.socialInteractions.tooltip.report.disabled": "<PERSON><PERSON><PERSON> pelaporan tidak tersedia", "gui.socialInteractions.tooltip.report.no_messages": "Ta' ada pesan jang dapat dilaporkan dari pemaïn %s", "gui.socialInteractions.tooltip.report.not_reportable": "Pemaïn ini tidak bisa dilaporkan, karena pesan obrolannja tidak bisa diverifikasi dipeladen ini", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON><PERSON><PERSON> pesan", "gui.stats": "Statistik", "gui.toMenu": "<PERSON><PERSON><PERSON> kedaftar peladen", "gui.toRealms": "Kembali kedaftar Realm", "gui.toTitle": "<PERSON><PERSON><PERSON> kehalaman o<PERSON>ma", "gui.toWorld": "<PERSON><PERSON><PERSON> kedaftar doenia", "gui.togglable_slot": "Pentjet oentoek mematikan petak", "gui.up": "Atas", "gui.waitingForResponse.button.inactive": "Kembali (%sd)", "gui.waitingForResponse.title": "Menoenggoe peladen", "gui.yes": "<PERSON>a", "hanging_sign.edit": "Soenting pesan papan tanda gantoeng", "instrument.minecraft.admire_goat_horn": "Kagoem", "instrument.minecraft.call_goat_horn": "Seroean", "instrument.minecraft.dream_goat_horn": "Mimpi", "instrument.minecraft.feel_goat_horn": "Rasa", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "Damba", "inventory.binSlot": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "inventory.hotbarInfo": "Simpan bilah benda dengan %1$s+%2$s", "inventory.hotbarSaved": "Bilah benda disimpan (poelihkan dengan %1$s+%2$s)", "item.canBreak": "<PERSON><PERSON><PERSON>:", "item.canPlace": "Dapat di<PERSON><PERSON>h pada:", "item.canUse.unknown": "Ta' diketahoei", "item.color": "Warna: %s", "item.components": "%s komponen", "item.disabled": "<PERSON><PERSON>", "item.durability": "Ketahanan: %s / %s", "item.dyed": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.acacia_boat": "<PERSON><PERSON><PERSON> akasia", "item.minecraft.acacia_chest_boat": "<PERSON><PERSON><PERSON> akasia dengan peti", "item.minecraft.allay_spawn_egg": "<PERSON><PERSON>r pewoedjo<PERSON>n penolong", "item.minecraft.amethyst_shard": "<PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.angler_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „pengaïl”", "item.minecraft.angler_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „pengaïl”", "item.minecraft.apple": "Apel", "item.minecraft.archer_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „pemanah”", "item.minecraft.archer_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „pemanah”", "item.minecraft.armadillo_scute": "Sisik armadilo", "item.minecraft.armadillo_spawn_egg": "Teloer pewoedjoedan armadilo", "item.minecraft.armor_stand": "Tjagak zirah", "item.minecraft.arms_up_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „angkat tangan”", "item.minecraft.arms_up_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „angkat tangan”", "item.minecraft.arrow": "Panah", "item.minecraft.axolotl_bucket": "Ember axolotl", "item.minecraft.axolotl_spawn_egg": "Teloer pewoedjoedan axolotl", "item.minecraft.baked_potato": "Kentang panggang", "item.minecraft.bamboo_chest_raft": "<PERSON><PERSON><PERSON> bamboe dengan peti", "item.minecraft.bamboo_raft": "<PERSON><PERSON><PERSON> bamboe", "item.minecraft.bat_spawn_egg": "Teloer pewoedjo<PERSON>n kele<PERSON>ar", "item.minecraft.bee_spawn_egg": "<PERSON>oer pewoe<PERSON><PERSON><PERSON><PERSON> le<PERSON>", "item.minecraft.beef": "Daging sapi mentah", "item.minecraft.beetroot": "Bit", "item.minecraft.beetroot_seeds": "<PERSON><PERSON> bit", "item.minecraft.beetroot_soup": "Soep bit", "item.minecraft.birch_boat": "<PERSON><PERSON><PERSON> berk", "item.minecraft.birch_chest_boat": "<PERSON><PERSON><PERSON> berk dengan peti", "item.minecraft.black_bundle": "<PERSON><PERSON>g hitam", "item.minecraft.black_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.black_harness": "<PERSON><PERSON><PERSON><PERSON> hitam", "item.minecraft.blade_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „bilah”", "item.minecraft.blade_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „bilah”", "item.minecraft.blaze_powder": "<PERSON><PERSON> kema<PERSON>", "item.minecraft.blaze_rod": "Tongkat kemamang", "item.minecraft.blaze_spawn_egg": "Teloer pewoedjoedan kemamang", "item.minecraft.blue_bundle": "Kantong biroe", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blue_egg": "<PERSON><PERSON><PERSON> bi<PERSON>e", "item.minecraft.blue_harness": "Roengkoep bi<PERSON>e", "item.minecraft.bogged_spawn_egg": "Teloer pewoed<PERSON><PERSON>n beng<PERSON> rawa", "item.minecraft.bolt_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.bolt_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „baoet”", "item.minecraft.bone": "Toelang", "item.minecraft.bone_meal": "<PERSON><PERSON><PERSON>", "item.minecraft.book": "<PERSON><PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "<PERSON><PERSON> spandoek „tepi menand<PERSON>”", "item.minecraft.bow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bowl": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bread": "R<PERSON><PERSON>", "item.minecraft.breeze_rod": "Tong<PERSON> pawana", "item.minecraft.breeze_spawn_egg": "<PERSON><PERSON>r pewoed<PERSON><PERSON>n pawana", "item.minecraft.brewer_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „peramoe”", "item.minecraft.brewer_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „peramoe”", "item.minecraft.brewing_stand": "<PERSON><PERSON> per<PERSON>", "item.minecraft.brick": "<PERSON><PERSON>", "item.minecraft.brown_bundle": "Kantong pirang", "item.minecraft.brown_dye": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.brown_egg": "<PERSON><PERSON><PERSON> pirang", "item.minecraft.brown_harness": "<PERSON>ng<PERSON><PERSON> pirang", "item.minecraft.brush": "Sikat", "item.minecraft.bucket": "Ember", "item.minecraft.bundle": "Kantong", "item.minecraft.bundle.empty": "Kosong", "item.minecraft.bundle.empty.description": "Dapat menjimpan toempoekan benda tjampoer", "item.minecraft.bundle.full": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „api”", "item.minecraft.burn_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „api”", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON>r pewoed<PERSON><PERSON><PERSON> oenta", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON>", "item.minecraft.cat_spawn_egg": "Teloer pewoedjoedan koetjing", "item.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON><PERSON> laba-laba goea", "item.minecraft.chainmail_boots": "<PERSON> ran<PERSON>", "item.minecraft.chainmail_chestplate": "<PERSON><PERSON><PERSON> rantai", "item.minecraft.chainmail_helmet": "<PERSON><PERSON> rantai", "item.minecraft.chainmail_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.charcoal": "<PERSON><PERSON>", "item.minecraft.cherry_boat": "<PERSON><PERSON><PERSON> k<PERSON>n", "item.minecraft.cherry_chest_boat": "<PERSON><PERSON><PERSON> kersen dengan peti", "item.minecraft.chest_minecart": "<PERSON><PERSON> tambang dengan peti", "item.minecraft.chicken": "Daging ajam mentah", "item.minecraft.chicken_spawn_egg": "Teloer pewoedjoedan ajam", "item.minecraft.chorus_fruit": "<PERSON><PERSON><PERSON> kor", "item.minecraft.clay_ball": "<PERSON><PERSON> tanah liat", "item.minecraft.clock": "Djam", "item.minecraft.coal": "<PERSON><PERSON> bara", "item.minecraft.coast_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.coast_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> z<PERSON>h „pesisir”", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON> ka<PERSON>o", "item.minecraft.cod": "Bakalau mentah", "item.minecraft.cod_bucket": "<PERSON><PERSON> bakalau", "item.minecraft.cod_spawn_egg": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON> bakalau", "item.minecraft.command_block_minecart": "Kereta tambang dengan balok perintah", "item.minecraft.compass": "Kompas", "item.minecraft.cooked_beef": "Daging sapi panggang", "item.minecraft.cooked_chicken": "Daging ajam panggang", "item.minecraft.cooked_cod": "Bakal<PERSON>ang", "item.minecraft.cooked_mutton": "Daging domba panggang", "item.minecraft.cooked_porkchop": "Daging babi pan<PERSON>ang", "item.minecraft.cooked_rabbit": "Da<PERSON> kelintji pan<PERSON>ang", "item.minecraft.cooked_salmon": "Salem matang", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "Tembaga batangan", "item.minecraft.cow_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON>n sapi", "item.minecraft.creaking_spawn_egg": "Teloer pewoedjoedan kerioet", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.desc": "<PERSON><PERSON> creeper", "item.minecraft.creeper_banner_pattern.new": "<PERSON><PERSON> spandoek „roepa creeper”", "item.minecraft.creeper_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON>n creeper", "item.minecraft.crossbow": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>", "item.minecraft.crossbow.projectile": "Projektil:", "item.minecraft.crossbow.projectile.multiple": "Projektil: %s x %s", "item.minecraft.crossbow.projectile.single": "Projektil: %s", "item.minecraft.cyan_bundle": "Kantong nilangsoeka", "item.minecraft.cyan_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cyan_harness": "<PERSON>ng<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „pendesis”", "item.minecraft.danger_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „pendesis”", "item.minecraft.dark_oak_boat": "Perahoe ek gelap", "item.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON> ek gelap dengan peti", "item.minecraft.debug_stick": "Tongkat lepas-kekoetoe", "item.minecraft.debug_stick.empty": "%s tidak poenja sifat", "item.minecraft.debug_stick.select": " „%s” (%s) dipilih", "item.minecraft.debug_stick.update": "„%s” -> %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON> be<PERSON>", "item.minecraft.diamond_boots": "<PERSON>", "item.minecraft.diamond_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_helmet": "<PERSON><PERSON>", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "<PERSON><PERSON><PERSON> koeda be<PERSON>", "item.minecraft.diamond_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON><PERSON> be<PERSON>ian", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON> be<PERSON>", "item.minecraft.diamond_sword": "Pedang berlian", "item.minecraft.disc_fragment_5": "Fragmen pelat", "item.minecraft.disc_fragment_5.desc": "Pelat moesik - 5", "item.minecraft.dolphin_spawn_egg": "Teloer pewoed<PERSON><PERSON>n loemba-loemba", "item.minecraft.donkey_spawn_egg": "Teloer pewoedjoedan keledai", "item.minecraft.dragon_breath": "Napas Naga", "item.minecraft.dried_kelp": "<PERSON><PERSON><PERSON> kering", "item.minecraft.drowned_spawn_egg": "Teloer pewoedjoedan zombi a<PERSON>r", "item.minecraft.dune_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „goemoek”", "item.minecraft.echo_shard": "<PERSON><PERSON><PERSON> g<PERSON>", "item.minecraft.egg": "Teloer", "item.minecraft.elder_guardian_spawn_egg": "Teloer pewoed<PERSON>edan tetoea pendjaga", "item.minecraft.elytra": "Elitron", "item.minecraft.emerald": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "<PERSON><PERSON><PERSON>", "item.minecraft.enchanted_golden_apple": "A<PERSON> emas terpesona", "item.minecraft.end_crystal": "Kristal End", "item.minecraft.ender_dragon_spawn_egg": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_eye": "<PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON><PERSON> enderman", "item.minecraft.endermite_spawn_egg": "Teloer pewoedjoedan endermite", "item.minecraft.evoker_spawn_egg": "<PERSON><PERSON>r pewoed<PERSON><PERSON>n pawang", "item.minecraft.experience_bottle": "Botol memesona", "item.minecraft.explorer_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „pendjeladjah”", "item.minecraft.explorer_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „pendjeladjah”", "item.minecraft.eye_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „mata”", "item.minecraft.feather": "<PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "<PERSON> laba-laba beragi", "item.minecraft.field_masoned_banner_pattern": "<PERSON><PERSON> span<PERSON> „bata-bata”", "item.minecraft.filled_map": "<PERSON><PERSON>", "item.minecraft.fire_charge": "<PERSON><PERSON> api", "item.minecraft.firework_rocket": "Roket kembang api", "item.minecraft.firework_rocket.flight": "<PERSON> terbang:", "item.minecraft.firework_rocket.multiple_stars": "%s sebanjak %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Bintang kembang api", "item.minecraft.firework_star.black": "<PERSON><PERSON>", "item.minecraft.firework_star.blue": "B<PERSON><PERSON>", "item.minecraft.firework_star.brown": "<PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.cyan": "Nilangsoeka", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON>", "item.minecraft.firework_star.flicker": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.gray": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "<PERSON><PERSON><PERSON> moeda", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON><PERSON> moeda", "item.minecraft.firework_star.lime": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.firework_star.magenta": "<PERSON><PERSON>", "item.minecraft.firework_star.orange": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.pink": "Djambon", "item.minecraft.firework_star.purple": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.red": "<PERSON><PERSON>", "item.minecraft.firework_star.shape": "<PERSON><PERSON><PERSON> ta' dikenal", "item.minecraft.firework_star.shape.burst": "Meledak", "item.minecraft.firework_star.shape.creeper": "<PERSON><PERSON> creeper", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON>", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON> k<PERSON>l", "item.minecraft.firework_star.shape.star": "<PERSON><PERSON> bin<PERSON>", "item.minecraft.firework_star.trail": "Berdjedjak", "item.minecraft.firework_star.white": "<PERSON><PERSON>", "item.minecraft.firework_star.yellow": "<PERSON><PERSON>", "item.minecraft.fishing_rod": "Tong<PERSON> pantjing", "item.minecraft.flint": "<PERSON><PERSON> api", "item.minecraft.flint_and_steel": "Pemantik api", "item.minecraft.flow_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.flow_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „pilin”", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON>", "item.minecraft.flow_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.flow_banner_pattern.new": "<PERSON><PERSON> spandoek „pilin”", "item.minecraft.flow_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „pilin”", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON>", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON> b<PERSON>", "item.minecraft.flower_banner_pattern.new": "<PERSON><PERSON> span<PERSON>ek „roe<PERSON> boenga”", "item.minecraft.flower_pot": "Pot boenga", "item.minecraft.fox_spawn_egg": "Teloer pewoed<PERSON><PERSON>n roebah", "item.minecraft.friend_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „teman”", "item.minecraft.friend_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „teman”", "item.minecraft.frog_spawn_egg": "Teloer pewoedjoedan katak", "item.minecraft.furnace_minecart": "Kereta tambang dengan oven", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON><PERSON> ghast", "item.minecraft.ghast_tear": "<PERSON><PERSON><PERSON>", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON> katja", "item.minecraft.glistering_melon_slice": "<PERSON><PERSON> semangka emas", "item.minecraft.globe_banner_pattern": "<PERSON><PERSON>", "item.minecraft.globe_banner_pattern.desc": "Globe", "item.minecraft.globe_banner_pattern.new": "<PERSON><PERSON> span<PERSON> „globe”", "item.minecraft.glow_berries": "<PERSON><PERSON> sinar", "item.minecraft.glow_ink_sac": "Kantong tinta sinar", "item.minecraft.glow_item_frame": "Bing<PERSON> benda sinar", "item.minecraft.glow_squid_spawn_egg": "Teloer pewoedjo<PERSON>n tjoemi-tjoemi sinar", "item.minecraft.glowstone_dust": "Serboek batoe sinar", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON> kamb<PERSON>", "item.minecraft.goat_spawn_egg": "<PERSON><PERSON><PERSON> pewoed<PERSON><PERSON><PERSON> kambing", "item.minecraft.gold_ingot": "Emas batangan", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON><PERSON> emas", "item.minecraft.golden_apple": "<PERSON><PERSON> emas", "item.minecraft.golden_axe": "Ka<PERSON><PERSON> emas", "item.minecraft.golden_boots": "<PERSON>", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON> emas", "item.minecraft.golden_chestplate": "<PERSON><PERSON><PERSON> emas", "item.minecraft.golden_helmet": "<PERSON><PERSON>as", "item.minecraft.golden_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON> emas", "item.minecraft.golden_horse_armor": "<PERSON><PERSON><PERSON> koeda emas", "item.minecraft.golden_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_pickaxe": "Belioeng emas", "item.minecraft.golden_shovel": "Sekop emas", "item.minecraft.golden_sword": "Pedang emas", "item.minecraft.gray_bundle": "<PERSON><PERSON><PERSON> kela<PERSON>", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guardian_spawn_egg": "Teloer pewoed<PERSON><PERSON><PERSON> pendjaga", "item.minecraft.gunpowder": "<PERSON><PERSON> mesioe", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON>", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "<PERSON><PERSON> spandoek „prahara”", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „prahara”", "item.minecraft.happy_ghast_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON>n ghast riang", "item.minecraft.harness": "Roengkoep", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>t", "item.minecraft.heart_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „hati”", "item.minecraft.heart_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „hati”", "item.minecraft.heartbreak_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „patah hati”", "item.minecraft.heartbreak_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „patah hati”", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON>r pewoed<PERSON><PERSON><PERSON> hoglin", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON> madoe", "item.minecraft.honeycomb": "<PERSON><PERSON> madoe", "item.minecraft.hopper_minecart": "<PERSON><PERSON> tambang dengan tjorong", "item.minecraft.horse_spawn_egg": "Teloer pewoedjoedan koeda", "item.minecraft.host_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.host_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> z<PERSON>h „toean roemah”", "item.minecraft.howl_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „lolong”", "item.minecraft.howl_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „lolong”", "item.minecraft.husk_spawn_egg": "Teloer pewoedjoedan zombi kering", "item.minecraft.ink_sac": "Kantong tinta", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON> besi", "item.minecraft.iron_boots": "<PERSON>", "item.minecraft.iron_chestplate": "<PERSON><PERSON><PERSON> besi", "item.minecraft.iron_golem_spawn_egg": "<PERSON><PERSON>r pewoed<PERSON><PERSON>n golem besi", "item.minecraft.iron_helmet": "<PERSON><PERSON> besi", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON> besi", "item.minecraft.iron_horse_armor": "<PERSON><PERSON><PERSON> koeda besi", "item.minecraft.iron_ingot": "<PERSON><PERSON> batangan", "item.minecraft.iron_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON> besi", "item.minecraft.iron_pickaxe": "<PERSON><PERSON><PERSON> besi", "item.minecraft.iron_shovel": "Sekop besi", "item.minecraft.iron_sword": "Pedang besi", "item.minecraft.item_frame": "<PERSON><PERSON> benda", "item.minecraft.jungle_boat": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_chest_boat": "<PERSON><PERSON><PERSON> rimba dengan peti", "item.minecraft.knowledge_book": "<PERSON><PERSON><PERSON>", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Ember lava", "item.minecraft.lead": "<PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON>", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.leather_helmet": "<PERSON><PERSON> k<PERSON>", "item.minecraft.leather_horse_armor": "<PERSON><PERSON><PERSON> koeda koelit", "item.minecraft.leather_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.light_blue_bundle": "Kantong biroe moeda", "item.minecraft.light_blue_dye": "<PERSON><PERSON><PERSON><PERSON> biroe moeda", "item.minecraft.light_blue_harness": "Roengkoep biroe moeda", "item.minecraft.light_gray_bundle": "Ka<PERSON>g kelaboe moeda", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> moeda", "item.minecraft.light_gray_harness": "<PERSON>ng<PERSON><PERSON> kelaboe moeda", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_harness": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.lingering_potion": "<PERSON><PERSON><PERSON> be<PERSON>", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON><PERSON> be<PERSON> aneh", "item.minecraft.lingering_potion.effect.empty": "<PERSON>oean bertahan ta' bisa diboeat", "item.minecraft.lingering_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON> bertahan kebal api", "item.minecraft.lingering_potion.effect.harming": "<PERSON><PERSON><PERSON> be<PERSON>han kes<PERSON>tan", "item.minecraft.lingering_potion.effect.healing": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.infested": "Ramoean bertahan infestasi", "item.minecraft.lingering_potion.effect.invisibility": "<PERSON>oean bertahan limoenan", "item.minecraft.lingering_potion.effect.leaping": "<PERSON><PERSON>an bertahan lo<PERSON>atan", "item.minecraft.lingering_potion.effect.levitation": "<PERSON><PERSON><PERSON> bertahan pen<PERSON>", "item.minecraft.lingering_potion.effect.luck": "<PERSON><PERSON><PERSON> bertahan keb<PERSON>n", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>a", "item.minecraft.lingering_potion.effect.night_vision": "<PERSON><PERSON><PERSON> bertahan pengli<PERSON>an gelap", "item.minecraft.lingering_potion.effect.oozing": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.poison": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.regeneration": "<PERSON><PERSON><PERSON> be<PERSON> regene<PERSON>i", "item.minecraft.lingering_potion.effect.slow_falling": "<PERSON><PERSON><PERSON> be<PERSON><PERSON> dja<PERSON> la<PERSON>t", "item.minecraft.lingering_potion.effect.slowness": "<PERSON><PERSON><PERSON> be<PERSON>han kela<PERSON>", "item.minecraft.lingering_potion.effect.strength": "Ramoean bertahan kek<PERSON>atan", "item.minecraft.lingering_potion.effect.swiftness": "<PERSON><PERSON><PERSON> be<PERSON>han ket<PERSON>tan", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON><PERSON> be<PERSON>han k<PERSON>", "item.minecraft.lingering_potion.effect.turtle_master": "<PERSON><PERSON><PERSON> bertahan goeroe penjoe", "item.minecraft.lingering_potion.effect.water": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.water_breathing": "<PERSON><PERSON><PERSON> be<PERSON><PERSON> a<PERSON>", "item.minecraft.lingering_potion.effect.weakness": "<PERSON><PERSON><PERSON> be<PERSON>han k<PERSON>an", "item.minecraft.lingering_potion.effect.weaving": "<PERSON><PERSON><PERSON> be<PERSON>han <PERSON>", "item.minecraft.lingering_potion.effect.wind_charged": "<PERSON><PERSON><PERSON> be<PERSON>han em<PERSON>", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON>n lama", "item.minecraft.lodestone_compass": "Kompas batoe magnet", "item.minecraft.mace": "Gada", "item.minecraft.magenta_bundle": "<PERSON><PERSON><PERSON> patma", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON><PERSON> pat<PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Krém magma", "item.minecraft.magma_cube_spawn_egg": "Teloer pewoedjoedan dadoe magma", "item.minecraft.mangrove_boat": "<PERSON><PERSON><PERSON> bakau", "item.minecraft.mangrove_chest_boat": "<PERSON><PERSON><PERSON> bakau dengan peti", "item.minecraft.map": "<PERSON><PERSON> kosong", "item.minecraft.melon_seeds": "<PERSON><PERSON>", "item.minecraft.melon_slice": "<PERSON><PERSON>", "item.minecraft.milk_bucket": "<PERSON><PERSON> soesoe", "item.minecraft.minecart": "<PERSON><PERSON> tambang", "item.minecraft.miner_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „penambang”", "item.minecraft.miner_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „penambang”", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.desc": "Lambang", "item.minecraft.mojang_banner_pattern.new": "<PERSON><PERSON> spandoek „lambang”", "item.minecraft.mooshroom_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON><PERSON> mooshroom", "item.minecraft.mourner_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „doeka”", "item.minecraft.mourner_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „doeka”", "item.minecraft.mule_spawn_egg": "<PERSON><PERSON>r pewoed<PERSON><PERSON>n bagal", "item.minecraft.mushroom_stew": "<PERSON><PERSON>", "item.minecraft.music_disc_11": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON> (kotak moesik)", "item.minecraft.music_disc_far": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "<PERSON><PERSON>t moesik", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Daging domba mentah", "item.minecraft.name_tag": "Etik<PERSON>t nama", "item.minecraft.nautilus_shell": "Tjangkang bia gengge", "item.minecraft.nether_brick": "<PERSON><PERSON> nether", "item.minecraft.nether_star": "<PERSON><PERSON><PERSON>", "item.minecraft.nether_wart": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_axe": "Kapak netherit", "item.minecraft.netherite_boots": "<PERSON> netherit", "item.minecraft.netherite_chestplate": "<PERSON><PERSON><PERSON> netherit", "item.minecraft.netherite_helmet": "<PERSON><PERSON> netherit", "item.minecraft.netherite_hoe": "Tjangkoel netherit", "item.minecraft.netherite_ingot": "<PERSON><PERSON>it batangan", "item.minecraft.netherite_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_pickaxe": "Belioeng netherit", "item.minecraft.netherite_scrap": "<PERSON>pingan netherit", "item.minecraft.netherite_shovel": "Sekop netherit", "item.minecraft.netherite_sword": "Pedang netherit", "item.minecraft.netherite_upgrade_smithing_template": "Sablon tempa", "item.minecraft.netherite_upgrade_smithing_template.new": "Peningkatan netherit", "item.minecraft.oak_boat": "Perahoe ek", "item.minecraft.oak_chest_boat": "<PERSON>ah<PERSON> ek dengan peti", "item.minecraft.ocelot_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON><PERSON> oselot", "item.minecraft.ominous_bottle": "<PERSON><PERSON><PERSON> seram", "item.minecraft.ominous_trial_key": "<PERSON><PERSON><PERSON> t<PERSON> seram", "item.minecraft.orange_bundle": "Kantong oranje", "item.minecraft.orange_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.orange_harness": "Roengkoep <PERSON>anje", "item.minecraft.painting": "Loekisan", "item.minecraft.pale_oak_boat": "Perahoe ek <PERSON>t", "item.minecraft.pale_oak_chest_boat": "Perahoe ek poet<PERSON>t dengan peti", "item.minecraft.panda_spawn_egg": "<PERSON><PERSON><PERSON> pewoed<PERSON><PERSON>n panda", "item.minecraft.paper": "<PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "<PERSON><PERSON><PERSON> pew<PERSON><PERSON><PERSON><PERSON><PERSON> bajan", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON><PERSON> angan-angan", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON>r pewoed<PERSON><PERSON>n angan-angan", "item.minecraft.pig_spawn_egg": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON> babi", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "<PERSON><PERSON> spandoek „montjong”", "item.minecraft.piglin_brute_spawn_egg": "<PERSON><PERSON>r pewoed<PERSON><PERSON>n piglin ganas", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON><PERSON> piglin", "item.minecraft.pillager_spawn_egg": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON> pend<PERSON>", "item.minecraft.pink_bundle": "Kantong djambon", "item.minecraft.pink_dye": "Pewarna djambon", "item.minecraft.pink_harness": "Roengkoep djambon", "item.minecraft.pitcher_plant": "Perioek kera", "item.minecraft.pitcher_pod": "Polong perioek kera", "item.minecraft.plenty_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „limpah”", "item.minecraft.plenty_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „limpah”", "item.minecraft.poisonous_potato": "<PERSON><PERSON>", "item.minecraft.polar_bear_spawn_egg": "Teloer pewoed<PERSON><PERSON>n beroeang koe<PERSON>", "item.minecraft.popped_chorus_fruit": "<PERSON><PERSON> boeah kor", "item.minecraft.porkchop": "Daging babi mentah", "item.minecraft.potato": "<PERSON><PERSON>", "item.minecraft.potion": "Ramoean", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON> aneh", "item.minecraft.potion.effect.empty": "Ramoean ta' bisa diboeat", "item.minecraft.potion.effect.fire_resistance": "Ramoean kebal api", "item.minecraft.potion.effect.harming": "Ramoean kesakitan", "item.minecraft.potion.effect.healing": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.infested": "Ramoean infestasi", "item.minecraft.potion.effect.invisibility": "Ramoean limoenan", "item.minecraft.potion.effect.leaping": "Ramoean lompatan", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON><PERSON> penga<PERSON>", "item.minecraft.potion.effect.luck": "Ramoean kebero<PERSON>ngan", "item.minecraft.potion.effect.mundane": "<PERSON><PERSON><PERSON> biasa", "item.minecraft.potion.effect.night_vision": "Ramoean penglihatan gelap", "item.minecraft.potion.effect.oozing": "<PERSON><PERSON><PERSON> pen<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.poison": "<PERSON><PERSON><PERSON> rat<PERSON>en", "item.minecraft.potion.effect.regeneration": "Ram<PERSON><PERSON> regene<PERSON>i", "item.minecraft.potion.effect.slow_falling": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON> la<PERSON>", "item.minecraft.potion.effect.slowness": "Ramoean kelambatan", "item.minecraft.potion.effect.strength": "Ramoean kekoeatan", "item.minecraft.potion.effect.swiftness": "Ramoean ketjepatan", "item.minecraft.potion.effect.thick": "<PERSON><PERSON><PERSON> kental", "item.minecraft.potion.effect.turtle_master": "Ramoean goeroe penjoe", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON><PERSON> a<PERSON>", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON><PERSON> kele<PERSON>an", "item.minecraft.potion.effect.weaving": "<PERSON><PERSON><PERSON> me<PERSON>", "item.minecraft.potion.effect.wind_charged": "<PERSON><PERSON><PERSON> emboesan", "item.minecraft.pottery_shard_archer": "<PERSON><PERSON><PERSON> pasoe „pemanah”", "item.minecraft.pottery_shard_arms_up": "<PERSON><PERSON><PERSON> pasoe „angkat tangan”", "item.minecraft.pottery_shard_prize": "<PERSON><PERSON><PERSON> pasoe „hadiah”", "item.minecraft.pottery_shard_skull": "<PERSON><PERSON><PERSON> pasoe „tengkorak”", "item.minecraft.powder_snow_bucket": "<PERSON>ber saldjoe poeder", "item.minecraft.prismarine_crystals": "<PERSON><PERSON>", "item.minecraft.prismarine_shard": "<PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.prize_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „hadiah”", "item.minecraft.prize_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „hadiah”", "item.minecraft.pufferfish": "<PERSON><PERSON> b<PERSON>", "item.minecraft.pufferfish_bucket": "<PERSON>ber ikan boe<PERSON>", "item.minecraft.pufferfish_spawn_egg": "Teloer pewoed<PERSON><PERSON>n ikan boe<PERSON>", "item.minecraft.pumpkin_pie": "Tar laboe", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON>", "item.minecraft.purple_bundle": "Ka<PERSON>g o<PERSON>", "item.minecraft.purple_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON>ng<PERSON><PERSON>", "item.minecraft.quartz": "<PERSON><PERSON><PERSON>her", "item.minecraft.rabbit": "Daging kelintji mentah", "item.minecraft.rabbit_foot": "<PERSON><PERSON>", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "Teloer pewoe<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.rabbit_stew": "<PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.raiser_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „pemelihara”", "item.minecraft.ravager_spawn_egg": "Teloer pewoed<PERSON><PERSON>n pem<PERSON>sa", "item.minecraft.raw_copper": "Tembaga mentah", "item.minecraft.raw_gold": "Emas mentah", "item.minecraft.raw_iron": "<PERSON><PERSON>", "item.minecraft.recovery_compass": "<PERSON><PERSON><PERSON>", "item.minecraft.red_bundle": "Kantong merah", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON><PERSON> merah", "item.minecraft.red_harness": "Roengkoep merah", "item.minecraft.redstone": "Serboek redstone", "item.minecraft.resin_brick": "<PERSON><PERSON> damar", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON> damar", "item.minecraft.rib_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „roesoek”", "item.minecraft.rotten_flesh": "<PERSON><PERSON> b<PERSON>k", "item.minecraft.saddle": "Pelana", "item.minecraft.salmon": "Salem mentah", "item.minecraft.salmon_bucket": "Ember salem", "item.minecraft.salmon_spawn_egg": "Teloer pewoedjoedan salem", "item.minecraft.scrape_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „kikis”", "item.minecraft.scute": "Tameng", "item.minecraft.sentry_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.sentry_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „pengawal”", "item.minecraft.shaper_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „pembentoek”", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „berkas”", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „berkas”", "item.minecraft.shears": "Go<PERSON>ing", "item.minecraft.sheep_spawn_egg": "Teloer pewoedjoedan domba", "item.minecraft.shelter_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „naoengan”", "item.minecraft.shelter_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „naoengan”", "item.minecraft.shield": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON> hitam", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON> biroe", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON> pirang", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON> biroe moeda", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON> k<PERSON> moeda", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON> pat<PERSON>", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.pink": "Perisai djambon", "item.minecraft.shield.purple": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.red": "Per<PERSON>i merah", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON> poetih", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON> koening", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON><PERSON> shulker", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON><PERSON> pew<PERSON><PERSON><PERSON><PERSON><PERSON> shulker", "item.minecraft.sign": "<PERSON><PERSON> tanda", "item.minecraft.silence_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.silence_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „keheningan”", "item.minecraft.silverfish_spawn_egg": "Teloer pewoedjoedan gegat", "item.minecraft.skeleton_horse_spawn_egg": "Teloer pewoedjoedan koeda ben<PERSON>k", "item.minecraft.skeleton_spawn_egg": "<PERSON>oer pew<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON>", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "<PERSON><PERSON> spandoek „roepa teng<PERSON>”", "item.minecraft.skull_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „tengkorak”", "item.minecraft.skull_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „tengkorak”", "item.minecraft.slime_ball": "<PERSON><PERSON>", "item.minecraft.slime_spawn_egg": "<PERSON><PERSON>r pewoedjoedan geladir", "item.minecraft.smithing_template": "Sablon tempa", "item.minecraft.smithing_template.applies_to": "Diterapkan pada:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Tambah batangan atau kristal", "item.minecraft.smithing_template.armor_trim.applies_to": "Zira<PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Tambah sepotong zirah", "item.minecraft.smithing_template.armor_trim.ingredients": "Batangan dan kristal", "item.minecraft.smithing_template.ingredients": "Bahan:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Tambah netherit batangan", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "<PERSON><PERSON><PERSON> be<PERSON>ian", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "<PERSON><PERSON> zirah, send<PERSON>ta atau alat berlian", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "<PERSON><PERSON>it batangan", "item.minecraft.smithing_template.upgrade": "Tingkatan: ", "item.minecraft.sniffer_spawn_egg": "<PERSON>oer pewoed<PERSON><PERSON><PERSON> pengendoes", "item.minecraft.snort_pottery_shard": "<PERSON><PERSON><PERSON> pasoe „dengoes”", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON> pasoe „dengoes”", "item.minecraft.snout_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.snout_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „montjong”", "item.minecraft.snow_golem_spawn_egg": "<PERSON><PERSON><PERSON> pewoed<PERSON><PERSON>n golem saldjoe", "item.minecraft.snowball": "<PERSON><PERSON>", "item.minecraft.spectral_arrow": "<PERSON><PERSON>", "item.minecraft.spider_eye": "<PERSON> laba-laba", "item.minecraft.spider_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON>n laba-laba", "item.minecraft.spire_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.spire_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „menara”", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON> t<PERSON>", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON><PERSON> tji<PERSON> aneh", "item.minecraft.splash_potion.effect.empty": "Ramoean tjiprat ta' bisa diboeat", "item.minecraft.splash_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON> tjiprat kebal api", "item.minecraft.splash_potion.effect.harming": "<PERSON><PERSON><PERSON> tjiprat kesakitan", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "item.minecraft.splash_potion.effect.infested": "Ramoean tjiprat infestasi", "item.minecraft.splash_potion.effect.invisibility": "<PERSON><PERSON>an tjiprat limoenan", "item.minecraft.splash_potion.effect.leaping": "<PERSON><PERSON><PERSON> tjiprat lompatan", "item.minecraft.splash_potion.effect.levitation": "<PERSON><PERSON><PERSON> tji<PERSON>t penga<PERSON>an", "item.minecraft.splash_potion.effect.luck": "Ram<PERSON><PERSON> tjiprat keb<PERSON>n", "item.minecraft.splash_potion.effect.mundane": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>a", "item.minecraft.splash_potion.effect.night_vision": "<PERSON><PERSON><PERSON> tjiprat penglihatan gelap", "item.minecraft.splash_potion.effect.oozing": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>t rat<PERSON>en", "item.minecraft.splash_potion.effect.regeneration": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> regene<PERSON>i", "item.minecraft.splash_potion.effect.slow_falling": "<PERSON><PERSON><PERSON> tji<PERSON>t dja<PERSON><PERSON> la<PERSON>t", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON><PERSON> tjiprat kelambatan", "item.minecraft.splash_potion.effect.strength": "Ramoean tjiprat kekoeatan", "item.minecraft.splash_potion.effect.swiftness": "Ram<PERSON>an tjiprat ketje<PERSON>tan", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON> tji<PERSON>t k<PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "<PERSON><PERSON><PERSON> tjiprat goeroe penjoe", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.water_breathing": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> a<PERSON>", "item.minecraft.splash_potion.effect.weakness": "<PERSON><PERSON><PERSON> tji<PERSON>t kele<PERSON>an", "item.minecraft.splash_potion.effect.weaving": "<PERSON><PERSON><PERSON> tji<PERSON>t me<PERSON>", "item.minecraft.splash_potion.effect.wind_charged": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> em<PERSON>", "item.minecraft.spruce_boat": "<PERSON>ahoe spar", "item.minecraft.spruce_chest_boat": "<PERSON><PERSON><PERSON> spar dengan peti", "item.minecraft.spyglass": "Teropong", "item.minecraft.squid_spawn_egg": "Teloer pewoedjo<PERSON>n tjoemi-tjoemi", "item.minecraft.stick": "Tongkat", "item.minecraft.stone_axe": "Kapak batoe", "item.minecraft.stone_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON> batoe", "item.minecraft.stone_pickaxe": "Belioeng batoe", "item.minecraft.stone_shovel": "Sekop batoe", "item.minecraft.stone_sword": "Pedang batoe", "item.minecraft.stray_spawn_egg": "Teloer pewoed<PERSON><PERSON>n beng<PERSON> saldjoe", "item.minecraft.strider_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON><PERSON> pen<PERSON>", "item.minecraft.string": "<PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON><PERSON>", "item.minecraft.sweet_berries": "<PERSON><PERSON> manis", "item.minecraft.tadpole_bucket": "<PERSON><PERSON> beroedoe", "item.minecraft.tadpole_spawn_egg": "<PERSON>oer pewoed<PERSON><PERSON>n beroedoe", "item.minecraft.tide_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.tide_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „gelombang pasang”", "item.minecraft.tipped_arrow": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.awkward": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.empty": "Panah tje<PERSON>an ta' bisa diboeat", "item.minecraft.tipped_arrow.effect.fire_resistance": "<PERSON><PERSON> kebal api", "item.minecraft.tipped_arrow.effect.harming": "Panah kesakitan", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.infested": "Panah infestasi", "item.minecraft.tipped_arrow.effect.invisibility": "Panah <PERSON>", "item.minecraft.tipped_arrow.effect.leaping": "Panah lompatan", "item.minecraft.tipped_arrow.effect.levitation": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.luck": "<PERSON>ah k<PERSON>", "item.minecraft.tipped_arrow.effect.mundane": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.night_vision": "<PERSON>ah pengli<PERSON>an gelap", "item.minecraft.tipped_arrow.effect.oozing": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.regeneration": "<PERSON><PERSON> regene<PERSON>", "item.minecraft.tipped_arrow.effect.slow_falling": "<PERSON><PERSON> d<PERSON> la<PERSON>", "item.minecraft.tipped_arrow.effect.slowness": "Panah kelambatan", "item.minecraft.tipped_arrow.effect.strength": "Panah kekoeatan", "item.minecraft.tipped_arrow.effect.swiftness": "Panah ketjepatan", "item.minecraft.tipped_arrow.effect.thick": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.turtle_master": "<PERSON><PERSON> goeroe penjoe", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON> t<PERSON>", "item.minecraft.tipped_arrow.effect.water_breathing": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.weakness": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.weaving": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.wind_charged": "<PERSON><PERSON>", "item.minecraft.tnt_minecart": "Kereta tambang dengan TNT", "item.minecraft.torchflower_seeds": "<PERSON><PERSON> b<PERSON> obor", "item.minecraft.totem_of_undying": "<PERSON><PERSON><PERSON>", "item.minecraft.trader_llama_spawn_egg": "<PERSON><PERSON><PERSON> pewoe<PERSON><PERSON><PERSON>n lama handelar", "item.minecraft.trial_key": "<PERSON><PERSON><PERSON>", "item.minecraft.trident": "Trisoela", "item.minecraft.tropical_fish": "<PERSON><PERSON> tropis", "item.minecraft.tropical_fish_bucket": "Ember ikan tropis", "item.minecraft.tropical_fish_spawn_egg": "<PERSON><PERSON>r pewoedjo<PERSON>n ikan tropis", "item.minecraft.turtle_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>e", "item.minecraft.turtle_scute": "<PERSON><PERSON>", "item.minecraft.turtle_spawn_egg": "<PERSON><PERSON><PERSON> pew<PERSON><PERSON><PERSON><PERSON><PERSON> penjoe", "item.minecraft.vex_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.vex_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „oesik”", "item.minecraft.vex_spawn_egg": "<PERSON><PERSON><PERSON> pew<PERSON><PERSON><PERSON><PERSON><PERSON> toejoel", "item.minecraft.villager_spawn_egg": "Teloer pewoed<PERSON><PERSON><PERSON> pendo<PERSON>k", "item.minecraft.vindicator_spawn_egg": "<PERSON><PERSON>r pewoe<PERSON><PERSON><PERSON><PERSON> pembela", "item.minecraft.wandering_trader_spawn_egg": "<PERSON><PERSON><PERSON> pewoed<PERSON><PERSON>n hand<PERSON>r keliling", "item.minecraft.ward_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „djaga”", "item.minecraft.warden_spawn_egg": "<PERSON><PERSON>r pewoed<PERSON><PERSON>n penoeng<PERSON>e", "item.minecraft.warped_fungus_on_a_stick": "Dja<PERSON>er keroekoet ditongkat", "item.minecraft.water_bucket": "<PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> zirah „pentjari arah”", "item.minecraft.wheat": "Gandoem", "item.minecraft.wheat_seeds": "<PERSON><PERSON>", "item.minecraft.white_bundle": "Kantong poetih", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.white_harness": "<PERSON>ng<PERSON><PERSON> poetih", "item.minecraft.wild_armor_trim_smithing_template": "Sablon tempa", "item.minecraft.wild_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> z<PERSON>h „liar”", "item.minecraft.wind_charge": "<PERSON><PERSON> an<PERSON>", "item.minecraft.witch_spawn_egg": "<PERSON><PERSON><PERSON> pew<PERSON><PERSON><PERSON><PERSON><PERSON> pen<PERSON>", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON><PERSON> pew<PERSON><PERSON><PERSON><PERSON><PERSON> ben<PERSON><PERSON>", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wolf_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.wolf_spawn_egg": "<PERSON>oer pewoed<PERSON><PERSON>n serigala", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON> kajoe", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>e", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON><PERSON> kajoe", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON> kajoe", "item.minecraft.wooden_sword": "<PERSON><PERSON><PERSON> kajoe", "item.minecraft.writable_book": "<PERSON><PERSON><PERSON> dan pena", "item.minecraft.written_book": "<PERSON><PERSON><PERSON> tertoe<PERSON>", "item.minecraft.yellow_bundle": "Kantong koening", "item.minecraft.yellow_dye": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.yellow_harness": "Roengkoep koening", "item.minecraft.zoglin_spawn_egg": "<PERSON>oer pewoed<PERSON><PERSON><PERSON> zoglin", "item.minecraft.zombie_horse_spawn_egg": "Teloer pewoedjoedan koeda zombi", "item.minecraft.zombie_spawn_egg": "Teloer pewoed<PERSON><PERSON>n zombi", "item.minecraft.zombie_villager_spawn_egg": "Teloer pewoedjoedan zombi pendo<PERSON>k", "item.minecraft.zombified_piglin_spawn_egg": "Teloer pewoedjoedan zombi piglin", "item.modifiers.any": "<PERSON><PERSON><PERSON> dipasang:", "item.modifiers.armor": "<PERSON><PERSON><PERSON>:", "item.modifiers.body": "<PERSON><PERSON><PERSON> dipasang:", "item.modifiers.chest": "Sa'at dibadan:", "item.modifiers.feet": "Sa'at dikaki:", "item.modifiers.hand": "<PERSON><PERSON><PERSON>g:", "item.modifiers.head": "Sa'at dikepala:", "item.modifiers.legs": "Sa'at ditoengkai:", "item.modifiers.mainhand": "Sa'at ditangan oetama:", "item.modifiers.offhand": "Sa'at ditangan kedoea:", "item.modifiers.saddle": "Ke<PERSON>ka dipelana:", "item.nbt_tags": "EBB: %s étik<PERSON>t", "item.op_block_warning.line1": "Peringatan:", "item.op_block_warning.line2": "<PERSON><PERSON><PERSON><PERSON> benda ini bisa mendjalankan perintah", "item.op_block_warning.line3": "<PERSON><PERSON><PERSON> pakai ketjoeali toean tahoe isinja setjara pasti!", "item.unbreakable": "<PERSON><PERSON> da<PERSON>t roesak", "itemGroup.buildingBlocks": "Balok <PERSON>", "itemGroup.coloredBlocks": "Balok aneka warna", "itemGroup.combat": "<PERSON><PERSON><PERSON>", "itemGroup.consumables": "Makan-minoem", "itemGroup.crafting": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.foodAndDrink": "<PERSON><PERSON><PERSON> dan minoeman", "itemGroup.functional": "Balok bergoena", "itemGroup.hotbar": "<PERSON><PERSON><PERSON> benda ters<PERSON>pan", "itemGroup.ingredients": "<PERSON><PERSON>", "itemGroup.inventory": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>", "itemGroup.natural": "<PERSON><PERSON><PERSON> <PERSON><PERSON>ami", "itemGroup.op": "Alat² pengelola", "itemGroup.redstone": "Balok redstone", "itemGroup.search": "<PERSON><PERSON><PERSON> benda", "itemGroup.spawnEggs": "<PERSON><PERSON><PERSON> p<PERSON>", "itemGroup.tools": "<PERSON><PERSON><PERSON>", "item_modifier.unknown": "Pengoebah benda tidak dikenal: %s", "jigsaw_block.final_state": "<PERSON><PERSON><PERSON><PERSON>:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "Sedjadjar", "jigsaw_block.joint.rollable": "<PERSON><PERSON>", "jigsaw_block.joint_label": "<PERSON><PERSON><PERSON>:", "jigsaw_block.keep_jigsaws": "Simp<PERSON> kepingan", "jigsaw_block.levels": "Tingkat: %s", "jigsaw_block.name": "Nama:", "jigsaw_block.placement_priority": "Prioritet penempatan:", "jigsaw_block.placement_priority.tooltip": "Ketika balok keping ini menjamboeng keseboeah potongan, ini adalah oeroetan dimana potongan itoe diproses oentoek samboengan dalam bangoenan jang lebih loeas.\n\nPotongan akan diproses berdasarkan prioritet menoeroen dengan oeroetan penjisipan memetjah prioritet setara.", "jigsaw_block.pool": "<PERSON><PERSON><PERSON><PERSON><PERSON> sasaran:", "jigsaw_block.selection_priority": "Prioritet pemilihan:", "jigsaw_block.selection_priority.tooltip": "<PERSON><PERSON><PERSON> potongan indoek sedang diproses oentoek samboengan, ini adalah oeroetan dimana balok keping ini beroesaha oentoek menjamboengkan kepotongan sasarannja.\n\nKepingan akan diproses berdasarkan prioritet menoeroen dengan oeroetan atjak memetjah prioritet setara.", "jigsaw_block.target": "<PERSON><PERSON>:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON> (kotak moesik)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "<PERSON><PERSON><PERSON><PERSON>", "key.attack": "Serang/Hantjoerkan", "key.back": "<PERSON><PERSON><PERSON>", "key.categories.creative": "<PERSON><PERSON> kreatif", "key.categories.gameplay": "Per<PERSON>ï<PERSON>", "key.categories.inventory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.misc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.movement": "Gerakan", "key.categories.multiplayer": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.ui": "An<PERSON><PERSON><PERSON>", "key.chat": "<PERSON><PERSON> o<PERSON>", "key.command": "<PERSON><PERSON>", "key.drop": "Dja<PERSON>eh<PERSON> benda terpilih", "key.forward": "<PERSON><PERSON><PERSON>", "key.fullscreen": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "key.hotbar.1": "Bilah benda petak 1", "key.hotbar.2": "Bilah benda petak 2", "key.hotbar.3": "Bilah benda petak 3", "key.hotbar.4": "Bilah benda petak 4", "key.hotbar.5": "B<PERSON>h benda petak 5", "key.hotbar.6": "<PERSON><PERSON><PERSON> benda petak 6", "key.hotbar.7": "Bilah benda petak 7", "key.hotbar.8": "B<PERSON>h benda petak 8", "key.hotbar.9": "B<PERSON>h benda petak 9", "key.inventory": "<PERSON><PERSON>/toe<PERSON><PERSON>", "key.jump": "Lompat", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "<PERSON><PERSON> bawah", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Koentji 0", "key.keyboard.keypad.1": "Koentji 1", "key.keyboard.keypad.2": "Koentji 2", "key.keyboard.keypad.3": "Koentji 3", "key.keyboard.keypad.4": "Koentji 4", "key.keyboard.keypad.5": "Koentji 5", "key.keyboard.keypad.6": "Koentji 6", "key.keyboard.keypad.7": "Koentji 7", "key.keyboard.keypad.8": "Koentji 8", "key.keyboard.keypad.9": "Koentji 9", "key.keyboard.keypad.add": "Koentji +", "key.keyboard.keypad.decimal": "<PERSON><PERSON><PERSON>", "key.keyboard.keypad.divide": "Koentji /", "key.keyboard.keypad.enter": "<PERSON><PERSON><PERSON> Enter", "key.keyboard.keypad.equal": "<PERSON>ent<PERSON> =", "key.keyboard.keypad.multiply": "<PERSON><PERSON><PERSON> *", "key.keyboard.keypad.subtract": "Koentji -", "key.keyboard.left": "Panah kiri", "key.keyboard.left.alt": "Alt kiri", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Ctrl kiri", "key.keyboard.left.shift": "Shift kiri", "key.keyboard.left.win": "Win kiri", "key.keyboard.menu": "<PERSON><PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "<PERSON><PERSON> kanan", "key.keyboard.right.alt": "Alt kanan", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Ctrl kanan", "key.keyboard.right.shift": "Shift kanan", "key.keyboard.right.win": "<PERSON> kanan", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "<PERSON><PERSON> te<PERSON>t", "key.keyboard.up": "<PERSON><PERSON> atas", "key.keyboard.world.1": "Doenia 1", "key.keyboard.world.2": "Doenia 2", "key.left": "Belok kiri", "key.loadToolbarActivator": "<PERSON>at penjala bilah benda", "key.mouse": "Kenop %1$s", "key.mouse.left": "Kenop kiri", "key.mouse.middle": "<PERSON><PERSON> tengah", "key.mouse.right": "<PERSON><PERSON> kanan", "key.pickItem": "<PERSON>bil balok", "key.playerlist": "<PERSON><PERSON><PERSON>", "key.quickActions": "<PERSON><PERSON><PERSON> segera", "key.right": "<PERSON><PERSON> kanan", "key.saveToolbarActivator": "<PERSON><PERSON><PERSON> penjala bilah benda", "key.screenshot": "<PERSON><PERSON>", "key.smoothCamera": "<PERSON><PERSON><PERSON><PERSON> kamera filmis", "key.sneak": "Djongkok", "key.socialInteractions": "Lajar interaksi sosial", "key.spectatorOutlines": "<PERSON><PERSON> (penonton)", "key.sprint": "<PERSON><PERSON>", "key.swapOffhand": "<PERSON><PERSON><PERSON> benda dengan tangan kedoea", "key.togglePerspective": "<PERSON><PERSON><PERSON> soed<PERSON>t pandang", "key.use": "<PERSON>ai benda/taroeh balok", "known_server_link.announcements": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.community": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.community_guidelines": "<PERSON><PERSON><PERSON>", "known_server_link.feedback": "Oempan-b<PERSON>k", "known_server_link.forums": "Fora", "known_server_link.news": "<PERSON><PERSON>", "known_server_link.report_bug": "Laporkan kekoetoe peladen", "known_server_link.status": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.support": "Doekoengan", "known_server_link.website": "<PERSON><PERSON>", "lanServer.otherPlayers": "Penjetelan oent<PERSON>k pema<PERSON>", "lanServer.port": "Nomor pintoe", "lanServer.port.invalid": "Boekan nomor pintoe jang sah.\nKosongkan kotak soenting atau masoekkan angka antara 1024 dan 65535.", "lanServer.port.invalid.new": "Boekan nomor pintoe jang sah.\nKosongkan kotak soenting atau masoekkan angka antara %s dan %s.", "lanServer.port.unavailable": "Nomor pintoe tidak tersedia.\nKosongkan kotak soenting atau masoekkan angka berbeda antara 1024 dan 65535.", "lanServer.port.unavailable.new": "Nomor pintoe tidak tersedia.\nKosongkan kotak soenting atau masoekkan angka berbeda antara %s dan %s.", "lanServer.scanning": "Men<PERSON>jar<PERSON> per<PERSON> lokal <PERSON>an", "lanServer.start": "<PERSON><PERSON> D<PERSON><PERSON><PERSON><PERSON>.", "lanServer.title": "Doenia Djaringan Areal Lokal", "language.code": "qid_ID", "language.name": "Bhs. Indonesia ed<PERSON><PERSON><PERSON> lama", "language.region": "Indonesia tempo doeloe", "lectern.take_book": "<PERSON><PERSON>", "loading.progress": "%s%%", "mco.account.privacy.info": "<PERSON><PERSON> lebih landjoet tentang Mojang dan hoekoem privasi", "mco.account.privacy.info.button": "Batja lebih landjoet tentang GDPR", "mco.account.privacy.information": "Mojang menerapkan tahapan tertentoe oentoek membantoe melindoengi anak² dan privasi mereka, termasoek mematoehi Oendang-oendang perlindoengan privasi anak-anak <PERSON> d<PERSON> (COPPA) dan Peratoeran perlindoengan data oemoem (GDPR).\n\nToean moengkin perloe mendapat persetoedjoean dari orang toea sebeloem memasoeki akoen Realms toean.", "mco.account.privacyinfo": "Mojang menerapkan tahapan tertentoe oentoek membantoe melindoengi anak² dan privasi mereka, termasoek mematoehi Oendang-oendang perlindoengan privasi anak-anak <PERSON> (COPPA) dan Peratoeran perlindoengan data oemoem (GDPR).\n\nToean moengkin perloe mendapat persetoedjoean dari orang toea sebeloem memasoeki akoen Realms toean.\n\nBila toean poenja akoen Minecraft lama (toean masoek dengan nama penggoena), toean haroes mengalihkan akoen itoe keakoen Mojang agar dapat memasoeki Realms.", "mco.account.update": "Perbaharoei a<PERSON>", "mco.activity.noactivity": "Tidak ada aktivitet selama %s hari", "mco.activity.title": "Aktivitet pemaïn", "mco.backup.button.download": "<PERSON><PERSON><PERSON><PERSON> terb<PERSON>e", "mco.backup.button.reset": "<PERSON><PERSON> o<PERSON>ng do<PERSON>", "mco.backup.button.restore": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.button.upload": "Oenggah doenia", "mco.backup.changes.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry": "Tjadangan (%s)", "mco.backup.entry.description": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.enabledPack": "<PERSON><PERSON> jang njala", "mco.backup.entry.gameDifficulty": "Ke<PERSON>eli<PERSON>", "mco.backup.entry.gameMode": "Ragam perma<PERSON>", "mco.backup.entry.gameServerVersion": "Versi peladen permaï<PERSON>", "mco.backup.entry.name": "<PERSON><PERSON>", "mco.backup.entry.seed": "<PERSON><PERSON>", "mco.backup.entry.templateName": "<PERSON><PERSON> sablon", "mco.backup.entry.undefined": "Peroebahan ta' tentoe", "mco.backup.entry.uploaded": "Dioenggah", "mco.backup.entry.worldType": "<PERSON><PERSON><PERSON>", "mco.backup.generate.world": "<PERSON><PERSON><PERSON>", "mco.backup.info.title": "<PERSON><PERSON>bahan dari tjadangan terachir", "mco.backup.narration": "Tjadangan dari %s", "mco.backup.nobackups": "Realm ini tidak poenja tjadangan sa'at ini.", "mco.backup.restoring": "Memoelihkan realm toean", "mco.backup.unknown": "TA' DIKETAHOEI", "mco.brokenworld.download": "<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "<PERSON><PERSON> setel oelang atau pilih doenia la<PERSON>n.", "mco.brokenworld.message.line2": "<PERSON><PERSON> djoega bisa mengo<PERSON>h doenia oentoek berma<PERSON>n sendiri", "mco.brokenworld.minigame.title": "Permaïnan ketjil ini soedah tidak did<PERSON>", "mco.brokenworld.nonowner.error": "<PERSON><PERSON>, pemilik Realm sedang menjetel oelang doenia", "mco.brokenworld.nonowner.title": "Doenia Habis Tempo", "mco.brokenworld.play": "Maïnkan", "mco.brokenworld.reset": "<PERSON><PERSON>", "mco.brokenworld.title": "<PERSON><PERSON>an soedah tidak did<PERSON>", "mco.client.incompatible.msg.line1": "Kliën toean tidak tjotjok dengan Realms.", "mco.client.incompatible.msg.line2": "Mohon pakai versi terbaroe Minecraft.", "mco.client.incompatible.msg.line3": "Realms tidak tjotjok dengan versi tjitra.", "mco.client.incompatible.title": "Kliën tidak tjotjok!", "mco.client.outdated.stable.version": "Versi kliën toe<PERSON> (%s) tidak tjotjok dengan Realms.\n\nMohon pakai versi terbaroe Minecraft.", "mco.client.unsupported.snapshot.version": "Versi kliën <PERSON> (%s) tidak tjotjok dengan Realms.\n\nRealms tidak tersedia oentoek versi tjitra ini.", "mco.compatibility.downgrade": "Toeroenkan", "mco.compatibility.downgrade.description": "Doenia ini terachir dimaïnkan di-versi %s; toean berada di-versi %s.\nMenoeroenkan versi doenia dalam menjebabkan keroesakan—kami tidak bisa memastikan itoe dapat memoeat atau bekerdja.\n\nTjadangan doenia toean akan disimpan didalam „Tjadangan doenia”. Mohon poelihkan doenia toean bila diperloekan.", "mco.compatibility.incompatible.popup.title": "Versi tidak tjo<PERSON>jok", "mco.compatibility.incompatible.releaseType.popup.message": "Doenia jang toean menjoba bergaboeng tidak tjotjok dengan versi toean sekarang.", "mco.compatibility.incompatible.series.popup.message": "Doenia ini terachir dimaïnkan di-versi %s; toean berada di-versi %s.\n\n<PERSON><PERSON>ri ini tidak tjotjok dengan sesama. Doenia baroe diperloekan oentoek bermaïn di-versi ini.", "mco.compatibility.unverifiable.message": "Versi terachir dimana doenia ini dimaïnkan tidak bisa diverifikasi. Bila versi doenia ditingkatkan atau ditoeroenkan, seboeah tjadangan akan diboeat setjara otomatis dengan disimpan didalam „Tjadangan doenia”.", "mco.compatibility.unverifiable.title": "Kompatibilitet tidak bisa diverifikasi", "mco.compatibility.upgrade": "Tingkatkan", "mco.compatibility.upgrade.description": "Doenia ini terachir dimaïnkan di-versi %s; toean berada di-versi %s.\n\nTjadangan doenia toean akan disimpan didalam „Tjadangan doenia”.\n\nMohon poelihkan doenia toean bila diperloekan.", "mco.compatibility.upgrade.friend.description": "Doenia ini terachir dimaïnkan di-versi %s; toean berada di-versi %s.\n\nTjadangan doenia akan disimpan didalam „Tjadangan doenia”.\n\nPemilik Realm dapat memoelihkan doenia bila diperloekan.", "mco.compatibility.upgrade.title": "Toean benar-benar ingin meningkatkan doenia ini?", "mco.configure.current.minigame": "Sa'at ini", "mco.configure.world.activityfeed.disabled": "<PERSON><PERSON><PERSON> dimatikan sementara", "mco.configure.world.backup": "Tjadangan doenia", "mco.configure.world.buttons.activity": "Aktivitet pemaïn", "mco.configure.world.buttons.close": "Toetoep sementara Realm", "mco.configure.world.buttons.delete": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.done": "Se<PERSON><PERSON>", "mco.configure.world.buttons.edit": "Penjetela<PERSON>", "mco.configure.world.buttons.invite": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.moreoptions": "Opsi landjoet", "mco.configure.world.buttons.newworld": "<PERSON><PERSON> baroe", "mco.configure.world.buttons.open": "Boeka oelang Realm", "mco.configure.world.buttons.options": "Opsi doenia", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "<PERSON><PERSON><PERSON> wilajah...", "mco.configure.world.buttons.resetworld": "<PERSON><PERSON> o<PERSON>ng do<PERSON>", "mco.configure.world.buttons.save": "Simpan", "mco.configure.world.buttons.settings": "Penjetela<PERSON>", "mco.configure.world.buttons.subscription": "Abonemen", "mco.configure.world.buttons.switchminigame": "Ganti permaïnan ketjil", "mco.configure.world.close.question.line1": "Toean bisa menoetoep Realm oentoek sementara, agar tidak bisa dimaïnkan selama toean memboeat penje<PERSON>ian. Boeka lagi sa'at toean soedah siap. \n\nIni tidak akan membatalkan abonemen Realms toean.", "mco.configure.world.close.question.line2": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin landjoet?", "mco.configure.world.close.question.title": "Perloe memboeat peroebahan tanpa ganggoean?", "mco.configure.world.closing": "Menoetoep Realm oentoek sementara...", "mco.configure.world.commandBlocks": "Balok perintah", "mco.configure.world.delete.button": "Ha<PERSON>es Realm", "mco.configure.world.delete.question.line1": "<PERSON> toean akan dihapoes permanen", "mco.configure.world.delete.question.line2": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin landjoet?", "mco.configure.world.description": "Deskripsi Realm", "mco.configure.world.edit.slot.name": "<PERSON><PERSON>", "mco.configure.world.edit.subscreen.adventuremap": "Beberapa penjetelan mati sebab doenia toean adalah pet<PERSON>gan", "mco.configure.world.edit.subscreen.experience": "<PERSON><PERSON><PERSON> penjetelan mati sebab doenia toean adalah pengalaman", "mco.configure.world.edit.subscreen.inspiration": "Beberapa penjetelan mati sebab doenia toean adalah doenia inspirasi", "mco.configure.world.forceGameMode": "Paksa ragam perma<PERSON>", "mco.configure.world.invite.narration": "Toean poenja %s oendangan baroe", "mco.configure.world.invite.profile.name": "<PERSON><PERSON>", "mco.configure.world.invited": "Di<PERSON><PERSON>ng", "mco.configure.world.invited.number": "Dioendang (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON><PERSON><PERSON>a", "mco.configure.world.invites.ops.tooltip": "Pen<PERSON><PERSON>", "mco.configure.world.invites.remove.tooltip": "<PERSON><PERSON><PERSON>", "mco.configure.world.leave.question.line1": "Bila meninggalkan Realm ini, toean ta' dapat melihatnja lagi ketjoeali dioendang kembali", "mco.configure.world.leave.question.line2": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin landjoet?", "mco.configure.world.loading": "Memoeat Realm", "mco.configure.world.location": "<PERSON><PERSON>", "mco.configure.world.minigame": "Sa'at ini: %s", "mco.configure.world.name": "Nama Realm", "mco.configure.world.opening": "Memboeka Realm...", "mco.configure.world.players.error": "<PERSON><PERSON><PERSON><PERSON> dengan nama itoe tidak ada", "mco.configure.world.players.inviting": "Mengoendang p<PERSON>ïn", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "<PERSON><PERSON><PERSON>", "mco.configure.world.region_preference": "Preferensi wilajah", "mco.configure.world.region_preference.title": "Pemilihan Preferensi Wilajah", "mco.configure.world.reset.question.line1": "Doenia toean akan dihasilkan oelang dan doenia toean sa'at ini akan hilang", "mco.configure.world.reset.question.line2": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin landjoet?", "mco.configure.world.resourcepack.question": "Realm ini perloe paket soember oebah-so<PERSON>ian.\n\n<PERSON><PERSON><PERSON><PERSON> toean ingin mengoend<PERSON>hnja dan maïn?", "mco.configure.world.resourcepack.question.line1": "Realm ini perloe paket soember oebah-soeaian.", "mco.configure.world.resourcepack.question.line2": "<PERSON><PERSON><PERSON><PERSON> toean ingin mengo<PERSON>ja dan be<PERSON>?", "mco.configure.world.restore.download.question.line1": "Doenia ini akan dioendoeh dan ditambahkan kedoenia bermaïn sendiri milik <PERSON>an.", "mco.configure.world.restore.download.question.line2": "A<PERSON><PERSON>h toean ingin meland<PERSON>?", "mco.configure.world.restore.question.line1": "Doenia toean akan dipoelihkan pada tanggal „%s” (%s)", "mco.configure.world.restore.question.line2": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin landjoet?", "mco.configure.world.settings.expired": "<PERSON>ean tidak bisa mengoebah penjetelan Realm jang habis tempo", "mco.configure.world.settings.title": "Penjetela<PERSON>", "mco.configure.world.slot": "Doenia %s", "mco.configure.world.slot.empty": "Kosong", "mco.configure.world.slot.switch.question.line1": "<PERSON> <PERSON>an akan dial<PERSON>kan kedoenia laïn", "mco.configure.world.slot.switch.question.line2": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin landjoet?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot.tooltip.active": "Bergaboeng", "mco.configure.world.slot.tooltip.minigame": "<PERSON><PERSON><PERSON> ketjil", "mco.configure.world.spawnAnimals": "Bang<PERSON>t<PERSON> hewan", "mco.configure.world.spawnMonsters": "Bangkitkan monster", "mco.configure.world.spawnNPCs": "Bangkitkan tokoh boekan pemaïn", "mco.configure.world.spawnProtection": "Perlindoengan wilajah bangkit", "mco.configure.world.spawn_toggle.message": "Mematikan opsi ini akan menghapoes seloeroeh entitet jang ada dari djenis itoe", "mco.configure.world.spawn_toggle.message.npc": "Mematikan opsi ini akan menghapoes seloeroeh entitet jang ada dari djenis itoe, se<PERSON>i pendo<PERSON>k", "mco.configure.world.spawn_toggle.title": "Peringatan!", "mco.configure.world.status": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.day": "hari", "mco.configure.world.subscription.days": "hari", "mco.configure.world.subscription.expired": "Habis tempo", "mco.configure.world.subscription.extend": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>men", "mco.configure.world.subscription.less_than_a_day": "Koerang dari sehari", "mco.configure.world.subscription.month": "boelan", "mco.configure.world.subscription.months": "boelan", "mco.configure.world.subscription.recurring.daysleft": "Diperbaharoei otomati<PERSON> da<PERSON>", "mco.configure.world.subscription.recurring.info": "Peroebahan jang diboeat pada abonemen Realms toean seperti mengoeloer tempo ataupoen mematikan pembajaran beroelang tidak akan terlihat hingga tanggal pembajaran berikoetnja.", "mco.configure.world.subscription.remaining.days": "%1$s hari", "mco.configure.world.subscription.remaining.months": "%1$s boelan", "mco.configure.world.subscription.remaining.months.days": "%1$s boelan, %2$s hari", "mco.configure.world.subscription.start": "<PERSON><PERSON> moelai", "mco.configure.world.subscription.tab": "Abonemen", "mco.configure.world.subscription.timeleft": "<PERSON><PERSON> tempo", "mco.configure.world.subscription.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.unknown": "Ta' diketahoei", "mco.configure.world.switch.slot": "<PERSON><PERSON> do<PERSON>", "mco.configure.world.switch.slot.subtitle": "Doenia ini kosong, pilih bagaimana doenia toean akan diboeat", "mco.configure.world.title": "Atoer Realm:", "mco.configure.world.uninvite.player": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin membatalkan oendangan „%s”?", "mco.configure.world.uninvite.question": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin membatalkan oendangan", "mco.configure.worlds.title": "Doenia", "mco.connect.authorizing": "Memasoeki...", "mco.connect.connecting": "Menghoeboengkan ke-Realm...", "mco.connect.failed": "<PERSON><PERSON>kan ke-Realm", "mco.connect.region": "Wilajah peladen: %s", "mco.connect.success": "Se<PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON>", "mco.create.world.error": "To<PERSON> haroes memasoekkan nama!", "mco.create.world.failed": "<PERSON>enia gagal diboeat!", "mco.create.world.reset.title": "Memboeat doenia...", "mco.create.world.skip": "<PERSON><PERSON>", "mco.create.world.subtitle": "Se<PERSON><PERSON> opsi, tentoekan doenia jang akan diletakkan pada Realm baroe milik <PERSON>an", "mco.create.world.wait": "Memboeat Realm...", "mco.download.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON> batal", "mco.download.confirmation.line1": "Doenia jang akan toean oendoeh lebih besar dari %s", "mco.download.confirmation.line2": "Toean tidak akan bisa mengoenggah doenia ini ke-Realm lagi", "mco.download.confirmation.oversized": "Doenia jang akan toean oendoeh lebih besar dari %s\n\n<PERSON><PERSON> tidak akan bisa mengoenggah doenia ini ke-Realm lagi", "mco.download.done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.download.downloading": "<PERSON><PERSON>endoe<PERSON>", "mco.download.extracting": "Membongkar", "mco.download.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> gagal", "mco.download.percent": "%s%%", "mco.download.preparing": "Menjiapkan pen<PERSON>han", "mco.download.resourcePack.fail": "<PERSON>et soember gagal dioendoeh!", "mco.download.speed": "(%s/d)", "mco.download.speed.narration": "%s per detik", "mco.download.title": "Mengoendoeh Do<PERSON>", "mco.error.invalid.session.message": "Mohon tjoba mendjalankan oelang Minecraft", "mco.error.invalid.session.title": "Sesi Tidak Sah", "mco.errorMessage.6001": "Kliën o<PERSON>", "mco.errorMessage.6002": "Ketentoean lajanan tidak diterima", "mco.errorMessage.6003": "Batas <PERSON> di<PERSON>", "mco.errorMessage.6004": "<PERSON><PERSON> dit<PERSON>i", "mco.errorMessage.6005": "<PERSON><PERSON> terk<PERSON>", "mco.errorMessage.6006": "Doenia habis tempo", "mco.errorMessage.6007": "<PERSON><PERSON><PERSON><PERSON> ada di<PERSON><PERSON><PERSON> ban<PERSON>", "mco.errorMessage.6008": "Nama Realm tidak sah", "mco.errorMessage.6009": "Deskripsi Realm tidak sah", "mco.errorMessage.connectionFailure": "<PERSON><PERSON><PERSON><PERSON>, mohon tjoba lagi nanti.", "mco.errorMessage.generic": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han: ", "mco.errorMessage.initialize.failed": "Realm gagal diï<PERSON>", "mco.errorMessage.noDetails": "Tidak diberikan per<PERSON>an k<PERSON>han", "mco.errorMessage.realmsService": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>han (%s):", "mco.errorMessage.realmsService.configurationError": "Kesalahan ta' diharapkan terdjadi sa'at mengoebah opsi doenia", "mco.errorMessage.realmsService.connectivity": "Tidak bisa terhoeboeng ke-Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Tidak bisa memeriksa versi jang tjo<PERSON>, respons jang didapat: %s", "mco.errorMessage.retry": "<PERSON><PERSON><PERSON> o<PERSON>i", "mco.errorMessage.serviceBusy": "Realms sa'at ini sedang siboek.\n<PERSON><PERSON> tjoba menghoeboengkan ke-Realm toean dalam beberapa menit.", "mco.gui.button": "<PERSON><PERSON>", "mco.gui.ok": "Baïk", "mco.info": "Informasi!", "mco.invited.player.narration": "Pemaïn %s dioendang", "mco.invites.button.accept": "Terima", "mco.invites.button.reject": "<PERSON><PERSON>", "mco.invites.nopending": "Tidak ada oendangan tertoenda!", "mco.invites.pending": "Oendangan baroe!", "mco.invites.title": "Oendangan Tertoenda", "mco.minigame.world.changeButton": "<PERSON><PERSON><PERSON> ket<PERSON>", "mco.minigame.world.info.line1": "Ini akan mengganti doenia toean dengan permaïnan ketjil oentoek sementara!", "mco.minigame.world.info.line2": "Toean dapat kembali kedoenia orisinil tanpa kehilangan apa poen nanti.", "mco.minigame.world.noSelection": "Mohon boeat pilihan", "mco.minigame.world.restore": "Men<PERSON><PERSON>ri <PERSON> ketjil...", "mco.minigame.world.restore.question.line1": "Permaïnan ketjil akan berachir dan Realm milik toean akan dipoe<PERSON>kan.", "mco.minigame.world.restore.question.line2": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin landjoet?", "mco.minigame.world.selected": "Permaïnan ketjil terpilih:", "mco.minigame.world.slot.screen.title": "<PERSON><PERSON><PERSON>...", "mco.minigame.world.startButton": "Beral<PERSON>", "mco.minigame.world.starting.screen.title": "<PERSON><PERSON><PERSON><PERSON>...", "mco.minigame.world.stopButton": "<PERSON><PERSON><PERSON>", "mco.minigame.world.switch.new": "<PERSON><PERSON><PERSON> per<PERSON> ket<PERSON>?", "mco.minigame.world.switch.title": "<PERSON><PERSON>", "mco.minigame.world.title": "Alih <PERSON> ke-Per<PERSON>ïnan <PERSON>", "mco.news": "Kabar Realms", "mco.notification.dismiss": "<PERSON><PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.message": "Abonemen Realms Java akan dipindahkan ke Microsoft Store. Djangan biarkan abonemen toean habis tempo! Transfer sekarang dan dapatkan 30 dari Realms setjara gratis. Koendjoengi profil di-minecraft.net oentoek memindahkan abonemen toean.", "mco.notification.visitUrl.buttonText.default": "Boeka ta<PERSON>", "mco.notification.visitUrl.message.default": "<PERSON><PERSON> k<PERSON>i taoetan dibawah", "mco.onlinePlayers": "<PERSON><PERSON><PERSON><PERSON>", "mco.play.button.realm.closed": "Realm ditoetoep", "mco.question": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "Petoealangan", "mco.reset.world.experience": "Pengalaman", "mco.reset.world.generate": "<PERSON><PERSON> baroe", "mco.reset.world.inspiration": "Inspirasi", "mco.reset.world.resetting.screen.title": "<PERSON><PERSON><PERSON> oelang doenia...", "mco.reset.world.seed": "<PERSON><PERSON> (opsionil)", "mco.reset.world.template": "Sablon doenia", "mco.reset.world.title": "<PERSON><PERSON>", "mco.reset.world.upload": "Oenggah doenia", "mco.reset.world.warning": "Ini akan menggantikan Realm milik toean sa'at ini", "mco.selectServer.buy": "Beli Realm!", "mco.selectServer.close": "Toetoep", "mco.selectServer.closed": "Realm ta' aktif", "mco.selectServer.closeserver": "Toetoep Realm", "mco.selectServer.configure": "Setel <PERSON>", "mco.selectServer.configureRealm": "Setel <PERSON>", "mco.selectServer.create": "Boeat Realm", "mco.selectServer.create.subtitle": "<PERSON><PERSON>h doenia mana jang dipakai oentoek Realm baroe milik <PERSON>an", "mco.selectServer.expired": "Realm habis tempo", "mco.selectServer.expiredList": "<PERSON><PERSON><PERSON> toean soedah habis tempo", "mco.selectServer.expiredRenew": "Perbaharoei", "mco.selectServer.expiredSubscribe": "Berabonemen", "mco.selectServer.expiredTrial": "<PERSON><PERSON> toean soedah be<PERSON>hir", "mco.selectServer.expires.day": "Habis tempo dalam satoe hari", "mco.selectServer.expires.days": "Habis tempo dalam %s hari", "mco.selectServer.expires.soon": "Segera habis tempo", "mco.selectServer.leave": "Tinggalkan Realm", "mco.selectServer.loading": "Memoeat daftar Realm", "mco.selectServer.mapOnlySupportedForVersion": "Peta ini tidak didoekoeng dalam %s", "mco.selectServer.minigame": "Permaïnan ketjil:", "mco.selectServer.minigameName": "Permaïnan ketjil: %s", "mco.selectServer.minigameNotSupportedInVersion": "Tidak bisa bermaïn permaïnan ketjil ini dalam %s", "mco.selectServer.noRealms": "Toean tampaknja tidak poenja Realm. Tambahkan Realm oentoek bermaïn bersama dengan teman² toean.", "mco.selectServer.note": "Tjatatan:", "mco.selectServer.open": "Realm terboeka", "mco.selectServer.openserver": "Boeka Realm", "mco.selectServer.play": "Maïnkan", "mco.selectServer.popup": "Realms adalah tjara aman dan sederhana oentoek menikmati doenia Minecraft dalam djaringan dengan hingga sepoeloeh teman bersamaän. Realms mendoekoeng banjak permaïnan ketjil dan doenia oebah-soeaian! Hanja pemilik Realm jang haroes membajar.", "mco.selectServer.purchase": "Tambah Realm", "mco.selectServer.trial": "<PERSON><PERSON><PERSON> gratis!", "mco.selectServer.uninitialized": "Pentjet oentoek memoelai Realm baroe milik <PERSON>an!", "mco.snapshot.createSnapshotPopup.text": "Toean akan memboeat Realm tjitra gratis jang akan dipasangkan dengan abonemen Realms berbajar toean. Realm tjitra baroe ini akan dapat dimasoeki selama abonemen berbajar aktif. Realm berbajar toean tidak akan terpengaroeh.", "mco.snapshot.createSnapshotPopup.title": "Boeat Realm Tjitra?", "mco.snapshot.creating": "Memboeat Realm tjitra...", "mco.snapshot.description": "Dipasangkan dengan „%s”", "mco.snapshot.friendsRealm.downgrade": "Toean perloe berada di-versi %s oentoek bergaboeng dengan Realm ini", "mco.snapshot.friendsRealm.upgrade": "%s perloe meningkatkan Realmnja sebeloem toean bisa bermaïn dari versi ini", "mco.snapshot.paired": "Realm tjitra ini dipasangkan dengan „%s”", "mco.snapshot.parent.tooltip": "Pakai versi terbaroe dari Minecraft oentoek bermaïn di-Realm ini", "mco.snapshot.start": "<PERSON><PERSON> tjitra gratis", "mco.snapshot.subscription.info": "Ini adalah Realm tjitra jang dipasangkan dengan abonemen Realm toean „%s”. Ini akan tetap aktif selama Realm jang dipasangkan tersebut aktif.", "mco.snapshot.tooltip": "Pakai Realms tjitra oentoek melihat sekilas versi mendatang dari <PERSON>craft, jang moengkin mentjakoep tjiri² baroe dan peroebahan laïnnja.\n\nToean bisa mentjari Realms biasa milik toean dalam versi keloearan permaïnan.", "mco.snapshotRealmsPopup.message": "Realms sekarang moelai tersedia dalam tjitra moelai dari tjitra 23w41a. Setiap abonemen Realms hadir dengan seboeah Realm tjitra gratis jang terpisah dari Realm Java toean jang biasa!", "mco.snapshotRealmsPopup.title": "Realms sekarang tersedia ditjitra", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON><PERSON><PERSON><PERSON> lebih landjoet", "mco.template.button.publisher": "Penerbit", "mco.template.button.select": "<PERSON><PERSON><PERSON>", "mco.template.button.trailer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.default.name": "Sablon doenia", "mco.template.info.tooltip": "<PERSON><PERSON> penerbit", "mco.template.name": "Sablon", "mco.template.select.failure": "<PERSON>mi tidak bisa mengambil daftar isi oentoek golongan ini.\nMohon periksa kembali samboengan internet toean, atau tjoba lagi nanti.", "mco.template.select.narrate.authors": "Pengarang: %s", "mco.template.select.narrate.version": "versi %s", "mco.template.select.none": "<PERSON><PERSON><PERSON>, tamp<PERSON><PERSON> golongan ini kosong.\n<PERSON><PERSON> periksa kembali nanti oentoek mendapatkan isi baroe, atau bila toean adalah pentji<PERSON>,\n%s.", "mco.template.select.none.linkTitle": "moengkin toean bisa mengoenggah seso<PERSON>oe sendiri", "mco.template.title": "Sablon doenia", "mco.template.title.minigame": "Permaïnan ketjil", "mco.template.trailer.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> peta", "mco.terms.buttons.agree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.terms.buttons.disagree": "<PERSON><PERSON><PERSON>", "mco.terms.sentence.1": "<PERSON><PERSON><PERSON> lajanan", "mco.terms.sentence.2": "Minecraft Realms", "mco.terms.title": "Ketentoean <PERSON>n <PERSON>s", "mco.time.daysAgo": "%1$s hari jang laloe", "mco.time.hoursAgo": "%1$s djam jang laloe", "mco.time.minutesAgo": "%1$s menit jang laloe", "mco.time.now": "<PERSON><PERSON><PERSON>", "mco.time.secondsAgo": "%1$s detik jang laloe", "mco.trial.message.line1": "Toean ingin poenja <PERSON> djoega?", "mco.trial.message.line2": "Pendjet disini oentoek informasi lebih landjoet!", "mco.upload.button.name": "Oenggah", "mco.upload.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.upload.close.failure": "Tidak bisa menoetoep <PERSON>, mohon tjoba lagi nanti", "mco.upload.done": "<PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>ai", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Pengoenggahan gagal! (%s)", "mco.upload.failed.too_big.description": "Doenia jang dipilih terlaloe besar. Oekoeran maksimal jang diperbolehkan adalah %s.", "mco.upload.failed.too_big.title": "Doenia terlaloe besar", "mco.upload.hardcore": "Doenia menantang tidak bisa dioenggah!", "mco.upload.percent": "%s%%", "mco.upload.preparing": "Menjiapkan do<PERSON>an", "mco.upload.select.world.none": "Doenia bermaïn sendiri tidak ditemoekan", "mco.upload.select.world.subtitle": "Mohon pilih doenia bermaïn sendiri ooentoek dioenggah", "mco.upload.select.world.title": "Oenggah Doenia", "mco.upload.size.failure.line1": "„%s” terlaloe besar!", "mco.upload.size.failure.line2": "Oekoerannja sa'at ini %s, tetapi maksimal jang diïzinkan adalah %s.", "mco.upload.uploading": "Mengoenggah „%s”", "mco.upload.verifying": "Memverifikasi doenia milik <PERSON>an", "mco.version": "Versi: %s", "mco.warning": "Peringatan!", "mco.worldSlot.minigame": "Permaïnan ketjil", "menu.custom_options": "<PERSON>si oebah-so<PERSON>ian...", "menu.custom_options.title": "<PERSON><PERSON> oebah-so<PERSON>ian", "menu.custom_options.tooltip": "Perhatian: <PERSON><PERSON> oebah-so<PERSON><PERSON> disediakan oleh peladen dan/atau isi pihak ketiga.\n<PERSON><PERSON> dengan hati-hati!", "menu.custom_screen_info.button_narration": "<PERSON>i adalah lajar o<PERSON>-so<PERSON><PERSON>. Peladjari lebih landjoet.", "menu.custom_screen_info.contents": "Isi dari lajar ini dikendalikan oleh peladen dan peta pihak ketiga jang tidak dimiliki, di<PERSON><PERSON> atau diawasi oleh Mojang Studios atau Microsoft.\n\nTangani dengan hati-hati! Selal<PERSON> berhati-hati ketika meneloesoeri taoetan dan djangan pernah memberi informasi pribadi toean, termasoek perintjian masoek.\n\nBila lajar ini menghalang toean dari be<PERSON>, toean dapat memoetoeskan samboengan dari peladen sa'at ini dengan kenop dibawah.", "menu.custom_screen_info.disconnect": "<PERSON><PERSON> o<PERSON><PERSON>-<PERSON><PERSON><PERSON> di<PERSON>", "menu.custom_screen_info.title": "Tjatatan tentang lajar oebah-so<PERSON>ian", "menu.custom_screen_info.tooltip": "<PERSON>i adalah lajar o<PERSON>-so<PERSON><PERSON>. Pentjet disini oentoek mempeladjari lebih landjoet.", "menu.disconnect": "Poetoes", "menu.feedback": "Oempan-balik...", "menu.feedback.title": "Oempan-b<PERSON>k", "menu.game": "<PERSON><PERSON>", "menu.modded": " (Dimodifikasi)", "menu.multiplayer": "<PERSON><PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "Opsi...", "menu.paused": "<PERSON><PERSON><PERSON><PERSON>", "menu.playdemo": "Maïnkan do<PERSON>n", "menu.playerReporting": "<PERSON><PERSON><PERSON><PERSON>", "menu.preparingSpawn": "Menjiapkan areal bangkit: %s%%", "menu.quick_actions": "Aksi segera...", "menu.quick_actions.title": "<PERSON><PERSON><PERSON> segera", "menu.quit": "<PERSON><PERSON><PERSON>", "menu.reportBugs": "Laporkan kekoetoe", "menu.resetdemo": "<PERSON><PERSON> o<PERSON>ng do<PERSON>", "menu.returnToGame": "<PERSON><PERSON><PERSON>", "menu.returnToMenu": "<PERSON>mpan dan kelo<PERSON>", "menu.savingChunks": "<PERSON><PERSON><PERSON><PERSON>", "menu.savingLevel": "Menjimpan doenia", "menu.sendFeedback": "<PERSON><PERSON>b<PERSON><PERSON>", "menu.server_links": "Taoetan peladen...", "menu.server_links.title": "Taoetan peladen", "menu.shareToLan": "Boeka ke-Dj.<PERSON><PERSON>Lok.", "menu.singleplayer": "<PERSON><PERSON><PERSON><PERSON>", "menu.working": "Sedang bekerdja...", "merchant.deprecated": "Pendoedoek mengisi oelang persediaän hingga doea kali sehari.", "merchant.level.1": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON><PERSON>", "merchant.level.3": "<PERSON>edagang harian", "merchant.level.4": "'<PERSON><PERSON>", "merchant.level.5": "<PERSON><PERSON><PERSON>", "merchant.title": "%s - %s", "merchant.trades": "Dagangan", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "%1$s kenop oentoek toeroen", "multiplayer.applyingPack": "Menerapkan paket soember", "multiplayer.confirm_command.parse_errors": "Toean sedang mentjoba mendjalankan perintah jang ta' dikenal atau ta' sah.\nToean jakin?\nPerintah: %s", "multiplayer.confirm_command.permissions_required": "Toean sedang mentjoba mendjalankan perintah jang perloe izin lebih tinggi.\nToean jakin?\nPerintah: %s", "multiplayer.confirm_command.title": "Konfirma<PERSON>", "multiplayer.disconnect.authservers_down": "<PERSON><PERSON>den autentikasi mati. <PERSON><PERSON> ma'a<PERSON>, mohon tjoba lagi nanti!", "multiplayer.disconnect.bad_chat_index": "Ditemoekan pesan obrolan terlewat atau teroeroet oelang dari peladen", "multiplayer.disconnect.banned": "<PERSON><PERSON> diblokir dari peladen ini", "multiplayer.disconnect.banned.expiration": "\nPemblokiran toean akan dihapoes pada %s", "multiplayer.disconnect.banned.reason": "Toean diblokir dari peladen ini.\nAlasan: %s", "multiplayer.disconnect.banned_ip.expiration": "\nPemblokiran toean akan dihapoes pada %s", "multiplayer.disconnect.banned_ip.reason": "Alamat IP toean diblokir dari peladen ini.\nAlasan: %s", "multiplayer.disconnect.chat_validation_failed": "Kegagalan validasi pesan obrolan", "multiplayer.disconnect.duplicate_login": "Toean masoek dari lokasi la<PERSON>n", "multiplayer.disconnect.expired_public_key": "<PERSON><PERSON><PERSON> oemoem profil habis tempo. <PERSON><PERSON><PERSON> apakah waktoe sistem toean soedah di<PERSON>, dan tjo<PERSON> d<PERSON><PERSON>an oelang permaïnan toean.", "multiplayer.disconnect.flying": "Terbang tidak dinjalakan dipeladen ini", "multiplayer.disconnect.generic": "Terpoe<PERSON><PERSON>", "multiplayer.disconnect.idling": "Toean menganggoer terlaloe lama!", "multiplayer.disconnect.illegal_characters": "<PERSON><PERSON><PERSON> ta' sah dalam obrolan", "multiplayer.disconnect.incompatible": "Kliën tidak tjotjok! Mohon pakai %s", "multiplayer.disconnect.invalid_entity_attacked": "Mentjoba menjerang entitet ta' sah", "multiplayer.disconnect.invalid_packet": "Peladen mengirimkan paket ta' sah", "multiplayer.disconnect.invalid_player_data": "Data pemaïn tidak sah", "multiplayer.disconnect.invalid_player_movement": "<PERSON><PERSON> per<PERSON>akan ta' sah pemaïn diterima", "multiplayer.disconnect.invalid_public_key_signature": "Tékenan tidak sah oentoek koentji oemoem profil.\nTjoba djalankan oelang permaïnan toean.", "multiplayer.disconnect.invalid_public_key_signature.new": "Tékenan tidak sah oentoek koentji oemoem profil.\nTjoba djalankan oelang permaïnan toean.", "multiplayer.disconnect.invalid_vehicle_movement": "<PERSON><PERSON> pergerakan ta' sah kendaraän diterima", "multiplayer.disconnect.ip_banned": "Alamat <PERSON>an diblokir dari peladen ini", "multiplayer.disconnect.kicked": "<PERSON><PERSON><PERSON><PERSON><PERSON> oleh pengelola", "multiplayer.disconnect.missing_tags": "Koempoelan étikét tidak lengkap tidak diterima dari peladen.\nMohon hoeboengi pengelola peladen.", "multiplayer.disconnect.name_taken": "<PERSON>a itoe soedah diambil", "multiplayer.disconnect.not_whitelisted": "Toean tidak dalam daftar poetih peladen ini!", "multiplayer.disconnect.out_of_order_chat": "<PERSON>et o<PERSON>lan roesak diterima. Apakah waktoe sistem toean dioebah?", "multiplayer.disconnect.outdated_client": "Kliën tidak tjotjok! Mohon pakai %s", "multiplayer.disconnect.outdated_server": "Kliën tidak tjotjok! Mohon pakai %s", "multiplayer.disconnect.server_full": "P<PERSON>den penoeh!", "multiplayer.disconnect.server_shutdown": "Peladen ditoetoep", "multiplayer.disconnect.slow_login": "<PERSON><PERSON><PERSON><PERSON> masoek terlaloe lama", "multiplayer.disconnect.too_many_pending_chats": "<PERSON><PERSON><PERSON><PERSON> banjak pesan jang tidak diakoei", "multiplayer.disconnect.transfers_disabled": "Peladen tidak menerima pem<PERSON>han", "multiplayer.disconnect.unexpected_query_response": "Data oebah-<PERSON><PERSON><PERSON> ta' diharapkan oleh kli<PERSON>n", "multiplayer.disconnect.unsigned_chat": "<PERSON><PERSON><PERSON> paket obrolan dengan tékenan jang hilang atau tidak sah.", "multiplayer.disconnect.unverified_username": "<PERSON><PERSON> pen<PERSON>na gag<PERSON>", "multiplayer.downloadingStats": "Mengambil statistik...", "multiplayer.downloadingTerrain": "Memoeat medan...", "multiplayer.lan.server_found": "Peladen baroe ditemoekan: %s", "multiplayer.message_not_delivered": "Tidak bisa mengirim pesan, si<PERSON>' t<PERSON><PERSON>n peladen: %s", "multiplayer.player.joined": "%s bergaboeng dalam permaïnan", "multiplayer.player.joined.renamed": "%s (sebeloemnja dikenal sebagai %s) bergaboeng dalam permaïnan", "multiplayer.player.left": "%s meninggalkan permaïnan", "multiplayer.player.list.hp": "❤%s", "multiplayer.player.list.narration": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>: %s", "multiplayer.requiredTexturePrompt.disconnect": "<PERSON><PERSON><PERSON> perloe paket soember oebah-so<PERSON>ian", "multiplayer.requiredTexturePrompt.line1": "Peladen ini mewadjibkan pemakaian paket soember oebah-so<PERSON>ian.", "multiplayer.requiredTexturePrompt.line2": "Menolak paket soember oebah-so<PERSON>ian akan memoetoeskan toean dari peladen ini.", "multiplayer.socialInteractions.not_available": "Interaksi sosial hanja tersedia didoenia be<PERSON><PERSON>n be<PERSON>ma", "multiplayer.status.and_more": "... dan %s lainnja ...", "multiplayer.status.cancelled": "Di<PERSON><PERSON><PERSON>", "multiplayer.status.cannot_connect": "Tidak bisa menghoeboengkan kepeladen", "multiplayer.status.cannot_resolve": "Tidak bisa menemoekan nama inang", "multiplayer.status.finished": "Se<PERSON><PERSON>", "multiplayer.status.incompatible": "Versi tidak tjotjok!", "multiplayer.status.motd.narration": "Pesan hari ini: %s", "multiplayer.status.no_connection": "(tiada samboengan)", "multiplayer.status.old": "Oesang", "multiplayer.status.online": "<PERSON><PERSON>", "multiplayer.status.ping": "%s md", "multiplayer.status.ping.narration": "Ping %s milidetik", "multiplayer.status.pinging": "Memeriksa...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s dari %s p<PERSON><PERSON><PERSON> da<PERSON> d<PERSON>n", "multiplayer.status.quitting": "<PERSON><PERSON><PERSON>", "multiplayer.status.request_handled": "<PERSON><PERSON><PERSON><PERSON><PERSON> keada<PERSON>n soedah ditangani", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "<PERSON><PERSON><PERSON>n ta' diminta diterima", "multiplayer.status.version.narration": "Versi peladen: %s", "multiplayer.stopSleeping": "Bangoen", "multiplayer.texturePrompt.failure.line1": "Paket soember peladen tidak bisa diterapkan", "multiplayer.texturePrompt.failure.line2": "Kegoenaän apa poen jang memerloekan soember daja oebah-soeaian moengkin tidak bisa bekerdja se<PERSON><PERSON>i ha<PERSON>an", "multiplayer.texturePrompt.line1": "Peladen ini menjarankan penggoenaän paket soember choesoes.", "multiplayer.texturePrompt.line2": "<PERSON><PERSON><PERSON>h toean ingin mengoendoeh dan memasang setjara otomatis?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nPesan dari peladen:\n%s", "multiplayer.title": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.unsecureserver.toast": "Pesan jang dikirim dipeladen ini moengkin soedah dioebah dan moengkin tidak sesoeai dengan pesan asli", "multiplayer.unsecureserver.toast.title": "Pesan obrolan tidak bisa diverifikasi", "multiplayerWarning.check": "Djangan tampilkan lajar ini lagi", "multiplayerWarning.header": "Perhatian: per<PERSON><PERSON><PERSON> dalam djaringan pihak ketiga", "multiplayerWarning.message": "Perhatian: Permaïnan dalam djaringan ditawarkan oleh peladen pihak ketiga jang tidak dimiliki, di<PERSON><PERSON> atau diawasi oleh Mojang Studios atau Microsoft. Selama permaïnan dalam djaringan, toean moengkin terpapar moeatan boeatan penggoena jang tidak tjotjok oentoek semoea orang, seperti pesan obrolan jang tidak termoderasi, dan se<PERSON><PERSON>.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "Tan<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "Tan<PERSON>ka <PERSON> k<PERSON>bi", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON> ya<PERSON>oku", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Kenop: %s", "narration.button.usage.focused": "Tekan Enter untuk menyal<PERSON>n", "narration.button.usage.hovered": "Pencet kiri untuk menyalakan", "narration.checkbox": "Kotak centang: %s", "narration.checkbox.usage.focused": "Tekan Enter untuk beralih", "narration.checkbox.usage.hovered": "Pencet kiri untuk beralih", "narration.component_list.usage": "<PERSON><PERSON> untuk menavigasi ke elemen selanjutnya", "narration.cycle_button.usage.focused": "Tekan Enter untuk beralih ke %s", "narration.cycle_button.usage.hovered": "Pencet kiri untuk beralih ke %s", "narration.edit_box": "Kotak sunting: %s", "narration.item": "Benda: %s", "narration.recipe": "Resep untuk %s", "narration.recipe.usage": "Pencet kiri untuk memililh", "narration.recipe.usage.more": "Pencet kanan untuk memperlihatkan lebih banyak resep", "narration.selection.usage": "<PERSON><PERSON> tombol atas dan bawah untuk berpindah ke benda lain", "narration.slider.usage.focused": "Tekan panah kiri arau kanan untuk mengganti nilai", "narration.slider.usage.hovered": "<PERSON><PERSON> penggeser untuk mengganti nilai", "narration.suggestion": "Saran %s dari %s dipilih: %s", "narration.suggestion.tooltip": "Saran %s dari %s dipilih: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Tekan Ta<PERSON> untuk beralih ke saran berikutnya", "narration.suggestion.usage.cycle.hidable": "Tekan Tab untuk beralih ke saran berikutnya, atau Escape untuk meninggalkan saran", "narration.suggestion.usage.fill.fixed": "Tekan Tab untuk menggunakan saran", "narration.suggestion.usage.fill.hidable": "Tekan Tab untuk menggunakan saran, atau Escape untuk meninggalkan saran", "narration.tab_navigation.usage": "<PERSON><PERSON> dan Tab untuk beralih antara jendela", "narrator.button.accessibility": "Keterkaitan", "narrator.button.difficulty_lock": "<PERSON><PERSON><PERSON> kesulitan", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "Tak dikunci", "narrator.button.language": "Bahasa", "narrator.controls.bound": "%s terikat dengan %s", "narrator.controls.reset": "Setel ulang kenop %s", "narrator.controls.unbound": "%s tidak terikat", "narrator.joining": "Bergabung", "narrator.loading": "Memuat: %s", "narrator.loading.done": "Se<PERSON><PERSON>", "narrator.position.list": "Daftar baris %s dari %s dipilih", "narrator.position.object_list": "Daftar elemen baris %s dari %s dipilih", "narrator.position.screen": "Elemen layar %s dari %s", "narrator.position.tab": "Jendela %s dari %s dipilih", "narrator.ready_to_play": "<PERSON>ap bermain", "narrator.screen.title": "<PERSON><PERSON>", "narrator.screen.usage": "Pakai petunjuk tetikus atau kenop Tab untuk memilih elemen", "narrator.select": "Dipilih: %s", "narrator.select.world": "%s dipilih, terakhir bermain: %s, %s, %s, versi: %s", "narrator.select.world_info": "%s dipilih, terakhir bermain: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON><PERSON>", "narrator.toast.enabled": "<PERSON><PERSON><PERSON>", "optimizeWorld.confirm.description": "Tindakan ini akan mengoptimalisir doenia toean dengan memastikan bahwa data tersimpan meroepakan format terbaroe. Ini akan memakan tempo jang tjoekoep lama. <PERSON>'at seles<PERSON>, doenia toean moengkin akan berd<PERSON><PERSON> le<PERSON>h tje<PERSON>t, tetapi tidak akan tjotjok dengan versi lama permaïnan. Toean jakin ingin melandjoetkan?", "optimizeWorld.confirm.proceed": "<PERSON><PERSON> tjadangan dan <PERSON>r", "optimizeWorld.confirm.title": "Optimalisir <PERSON>", "optimizeWorld.info.converted": "<PERSON><PERSON><PERSON> jang soedah ditingkatkan: %s", "optimizeWorld.info.skipped": "<PERSON><PERSON><PERSON> jang di<PERSON>: %s", "optimizeWorld.info.total": "<PERSON><PERSON><PERSON><PERSON> bin<PERSON>: %s", "optimizeWorld.progress.counter": "%s/%s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "<PERSON><PERSON><PERSON><PERSON> bing<PERSON>...", "optimizeWorld.stage.failed": "Gagal! ❌", "optimizeWorld.stage.finished": "Men<PERSON><PERSON>ai<PERSON>...", "optimizeWorld.stage.finished.chunks": "Menjelesaikan peningkatan bingkah...", "optimizeWorld.stage.finished.entities": "Menjelesaikan peningkatan entitet...", "optimizeWorld.stage.finished.poi": "Menjelesaikan peningkatan titik perhatian...", "optimizeWorld.stage.upgrading": "Meningkatkan seloeroeh bingkah...", "optimizeWorld.stage.upgrading.chunks": "Meningkatkan seloeroeh bingkah...", "optimizeWorld.stage.upgrading.entities": "Meningkatkan seloeroeh entitet...", "optimizeWorld.stage.upgrading.poi": "Meningkatkan seloeroeh titik perhatian...", "optimizeWorld.title": "Mengoptimalisir Doenia „%s”", "options.accessibility": "Penjetelan keterkaïtan...", "options.accessibility.high_contrast": "Kontras tinggi", "options.accessibility.high_contrast.error.tooltip": "<PERSON>et soember kontras tinggi tidak tersedia.", "options.accessibility.high_contrast.tooltip": "Meningkatkan kontras dari elemen antarmoeka penggoena.", "options.accessibility.high_contrast_block_outline": "Garis loear balok kontras tinggi", "options.accessibility.high_contrast_block_outline.tooltip": "Meningkatkan kontras garis loear balok sasaran.", "options.accessibility.link": "Petoendjoek <PERSON>", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "options.accessibility.menu_background_blurriness.tooltip": "Mengoebah keboeraman latar belakang lajar.", "options.accessibility.narrator_hotkey": "<PERSON><PERSON><PERSON> pintas pengisah", "options.accessibility.narrator_hotkey.mac.tooltip": "Memoengkinkan oentoek menjalakan dan mematikan pengisah dengan „Cmd+B”.", "options.accessibility.narrator_hotkey.tooltip": "Memoengkinkan oentoek menjalakan dan mematikan pengisah dengan „Ctrl+B”.", "options.accessibility.panorama_speed": "Lad<PERSON>e goelir panorama", "options.accessibility.text_background": "Lat. bel. teks", "options.accessibility.text_background.chat": "obrolan", "options.accessibility.text_background.everywhere": "dimana poen", "options.accessibility.text_background_opacity": "Keboeraman lat. bel. teks", "options.accessibility.title": "Penjetela<PERSON>", "options.allowServerListing": "<PERSON>zinkan daftar peladen", "options.allowServerListing.tooltip": "Peladen moengkin mentjantoemkan pemaïn dalam djaringan sebagai keadaän oemoem.\n<PERSON>gan opsi ini mati, nama toean tidak akan moentjoel dalam daftar.", "options.ao": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> haloes", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "minimal", "options.ao.off": "mati", "options.attack.crosshair": "silang pembidik", "options.attack.hotbar": "bilah benda", "options.attackIndicator": "<PERSON><PERSON> se<PERSON>", "options.audioDevice": "<PERSON><PERSON><PERSON>", "options.audioDevice.default": "asali sistem", "options.autoJump": "Lompat otomatis", "options.autoSuggestCommands": "<PERSON><PERSON>", "options.autosaveIndicator": "Tanda simpan otomatis", "options.biomeBlendRadius": "Transisi bioom", "options.biomeBlendRadius.1": "mati (tertjepat)", "options.biomeBlendRadius.11": "11×11 (ekstrem)", "options.biomeBlendRadius.13": "13×13 (pamer)", "options.biomeBlendRadius.15": "15×15 (maksimal)", "options.biomeBlendRadius.3": "3×3 (tjepat)", "options.biomeBlendRadius.5": "5×5 (normal)", "options.biomeBlendRadius.7": "7×7 (tinggi)", "options.biomeBlendRadius.9": "9×9 (sangat tinggi)", "options.chat": "Penjetelan obrolan...", "options.chat.color": "<PERSON><PERSON>", "options.chat.delay": "Djeda o<PERSON>lan: %s detik", "options.chat.delay_none": "<PERSON><PERSON>da o<PERSON>: tidak ada", "options.chat.height.focused": "Tinggi terpoesat", "options.chat.height.unfocused": "Tinggi ta' terpoesat", "options.chat.line_spacing": "<PERSON><PERSON><PERSON> baris", "options.chat.links": "<PERSON><PERSON><PERSON> d<PERSON>", "options.chat.links.prompt": "Konfirmasi ta<PERSON>tan", "options.chat.opacity": "Keboeraman teks obrolan", "options.chat.scale": "Oekoeran teks obrolan", "options.chat.title": "Pen<PERSON><PERSON><PERSON>", "options.chat.visibility": "<PERSON><PERSON>lan", "options.chat.visibility.full": "tampak", "options.chat.visibility.hidden": "terse<PERSON><PERSON><PERSON>", "options.chat.visibility.system": "hanja perintah", "options.chat.width": "<PERSON><PERSON>", "options.chunks": "%s bingkah", "options.clouds.fancy": "mewah", "options.clouds.fast": "tjepat", "options.controls": "Kendali...", "options.credits_and_attribution": "Pengaïtan dan penga<PERSON>...", "options.damageTiltStrength": "<PERSON><PERSON><PERSON> loeka", "options.damageTiltStrength.tooltip": "<PERSON><PERSON><PERSON><PERSON> gojangan kamera jang disebabkan karena terloeka.", "options.darkMojangStudiosBackgroundColor": "<PERSON><PERSON> hitam-poet<PERSON>", "options.darkMojangStudiosBackgroundColor.tooltip": "Mengganti latar belakang lajar memoeat Mojang Studios mendjadi hitam.", "options.darknessEffectScale": "<PERSON><PERSON><PERSON>", "options.darknessEffectScale.tooltip": "Mengendalikan seberapa efek kegelapan berdenjoet ketika penoenggoe atau sculk pendjerit memberikan efek itoe kepada toean.", "options.difficulty": "Kesoelitan", "options.difficulty.easy": "moedah", "options.difficulty.easy.info": "Machloek djahat moentjoel tetapi memberikan loeka lebih ketjil. Bila bilah kela<PERSON>an habis, njawa berkoerang hingga 5 hati.", "options.difficulty.hard": "soelit", "options.difficulty.hard.info": "Machloek djahat moentjoel dan memberikan loeka lebih besar. Dapat mati kelaparan bila bilah kelaparan habis.", "options.difficulty.hardcore": "menantang", "options.difficulty.normal": "normal", "options.difficulty.normal.info": "Machloek djahat moentjoel dan member<PERSON>n loeka <PERSON>a. <PERSON><PERSON> bilah kela<PERSON>an habis, njawa berkoe<PERSON> hingga setengah hati.", "options.difficulty.online": "Kesoelitan peladen", "options.difficulty.peaceful": "damai", "options.difficulty.peaceful.info": "Tidak ada machloek djahat dan hanja beberapa machloek netral moentjoel. Bilah kelaparan tidak berkoerang dan njawa selaloe mengisi sepandjang waktoe.", "options.directionalAudio": "<PERSON><PERSON><PERSON>", "options.directionalAudio.off.tooltip": "Soeara stereo klasik.", "options.directionalAudio.on.tooltip": "Memakai soeara direksionil berbasis HTRF oentoek meningkatkan peniroean soeara tiga dimensi. Memerloekan perangkat keras soeara jang tjotjok dengan HTRF, dan sangat diandjoerkan mengenakan pelantang telinga.", "options.discrete_mouse_scroll": "<PERSON><PERSON><PERSON><PERSON> go<PERSON>", "options.entityDistanceScaling": "<PERSON><PERSON><PERSON> en<PERSON>t", "options.entityShadows": "Bajangan entitet", "options.font": "Penjetelan tipe hoeroef...", "options.font.title": "Penjetelan Tipe <PERSON>", "options.forceUnicodeFont": "Tipe hoeroef Unicode", "options.fov": "Djarak pandang", "options.fov.max": "loeas", "options.fov.min": "normal", "options.fovEffectScale": "Efek djarak pandang", "options.fovEffectScale.tooltip": "Mengendalikan seberapa banjak djarak pandang dapat beroebah oleh efek permaïnan.", "options.framerate": "%s bpd", "options.framerateLimit": "<PERSON><PERSON><PERSON><PERSON> bing<PERSON> maks.", "options.framerateLimit.max": "ta' terbatas", "options.fullscreen": "<PERSON><PERSON>", "options.fullscreen.current": "sa'at ini", "options.fullscreen.entry": "%s×%s@%s (%sbit)", "options.fullscreen.resolution": "Oekoe<PERSON> la<PERSON> pen<PERSON>h", "options.fullscreen.unavailable": "penjetelan ta' tersedia", "options.gamma": "<PERSON><PERSON><PERSON><PERSON>", "options.gamma.default": "asali", "options.gamma.max": "terang", "options.gamma.min": "gelap", "options.generic_value": "%s: %s", "options.glintSpeed": "Ketjepatan kilau", "options.glintSpeed.tooltip": "Mengatoer ketjepatan dari tampilan kilau jang berkilau pada benda terpesona.", "options.glintStrength": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "options.glintStrength.tooltip": "Mengatoer tingkat transparansi dari tampilan kilau pada benda terpesona.", "options.graphics": "<PERSON><PERSON>", "options.graphics.fabulous": "mena'd<PERSON><PERSON><PERSON>!", "options.graphics.fabulous.tooltip": "Grafika „%s” memakai pentjorak lajar oentoek menampilkan tjoeatja, awan dan partikel dibalik balok temboes tjahaja dan aïr.\nIni bisa memengaroehi kinerdja oentoek perangkat moedah dibawa dan perangkat dengan tampilan 4K.", "options.graphics.fancy": "mewah", "options.graphics.fancy.tooltip": "<PERSON><PERSON> „mewah” menjeïmbangkan kinerdja dan kwalitet oentoek sebagian besar perangkat.\n<PERSON><PERSON><PERSON><PERSON>, awan dan partikel moengkin tidak moentjoel dibelakang balok temboes tjahaja atau aïr.", "options.graphics.fast": "tjepat", "options.graphics.fast.tooltip": "<PERSON><PERSON> „tjepat” mengoerangi djoemlah titik hoedjan dan saldjoe jang tampak.\nEfek temboes pandang dimatikan oentoek beberapa balok seperti dedaoenan.", "options.graphics.warning.accept": "Landjoetkan tanpa doek<PERSON>ngan", "options.graphics.warning.cancel": "Kembali", "options.graphics.warning.message": "Perangkat grafika toean terdeteksi tidak mendoekoeng oentoek opsi grafika %s.\n\n<PERSON><PERSON> boleh mengabaikannja dan landjoet, tetapi tidak akan diberikan doekoengan kepada perangkat toean bila memilih memakai grafika %s.", "options.graphics.warning.renderer": "<PERSON>sin reka-t<PERSON>ra terdeteksi: [%s]", "options.graphics.warning.title": "Perangkat grafika tidak did<PERSON>", "options.graphics.warning.vendor": "Leveransir terdeteksi: [%s]", "options.graphics.warning.version": "Versi OpenGL terdeteksi: [%s]", "options.guiScale": "<PERSON><PERSON><PERSON>", "options.guiScale.auto": "otomatis", "options.hidden": "terse<PERSON><PERSON><PERSON>", "options.hideLightningFlashes": "Semboenjikan kilat", "options.hideLightningFlashes.tooltip": "Mentjegah halilintar memboeat langit berdenjar. <PERSON><PERSON> tetap tampak.", "options.hideMatchedNames": "<PERSON>a tjot<PERSON>k ta' tampak", "options.hideMatchedNames.tooltip": "Peladen pihak ketiga dapat mengirim pesan obrolan dalam format ta' standar.\n<PERSON><PERSON> men<PERSON> opsi ini, pema<PERSON><PERSON> terse<PERSON><PERSON>ji akan ditjotjokkan berdasarkan nama pengirim obrolan.", "options.hideSplashTexts": "Semboenjikan teks koening", "options.hideSplashTexts.tooltip": "Menjemboenjikan teks koening pada lajar oetama.", "options.inactivityFpsLimit": "Koerangi BPD ketika", "options.inactivityFpsLimit.afk": "diam", "options.inactivityFpsLimit.afk.tooltip": "Membatasi ladjoe bingkai mendjadi 30 ketika permaïnan tidak mendapat masoekan pemaïn selama lebih dari semenit, laloe di<PERSON> mendjadi 10 setelah 9 menit kemoedian.", "options.inactivityFpsLimit.minimized": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.inactivityFpsLimit.minimized.tooltip": "Membatasi ketjepatan bingkai hanja ketika djendela permaï<PERSON>.", "options.invertMouse": "Balik tetikoes", "options.japaneseGlyphVariants": "<PERSON><PERSON> karak<PERSON>", "options.japaneseGlyphVariants.tooltip": "Memakai varian <PERSON> o<PERSON>k karakter Han didalam tipe hoeroef asali.", "options.key.hold": "<PERSON>han", "options.key.toggle": "tekan", "options.language": "Bahasa...", "options.language.title": "Bahasa", "options.languageAccuracyWarning": "(Penerdjemahan moengkin tidak 100%% tepat)", "options.languageWarning": "Penerdjemahan moengkin tidak 100%% tepat", "options.mainHand": "<PERSON><PERSON>", "options.mainHand.left": "kiri", "options.mainHand.right": "kanan", "options.mipmapLevels": "Tingkat mipmap", "options.modelPart.cape": "Mantel", "options.modelPart.hat": "<PERSON>i", "options.modelPart.jacket": "<PERSON><PERSON>", "options.modelPart.left_pants_leg": "<PERSON><PERSON> tjelana kiri", "options.modelPart.left_sleeve": "Lengan kiri", "options.modelPart.right_pants_leg": "<PERSON><PERSON> t<PERSON> kanan", "options.modelPart.right_sleeve": "<PERSON><PERSON> kanan", "options.mouseWheelSensitivity": "Sensitivitet goelir", "options.mouse_settings": "Penjetelan tetiko<PERSON>...", "options.mouse_settings.title": "<PERSON><PERSON><PERSON><PERSON>", "options.multiplayer.title": "<PERSON><PERSON><PERSON><PERSON>...", "options.multiplier": "%s×", "options.music_frequency": "Kekerapan moesik", "options.music_frequency.constant": "selaloe", "options.music_frequency.default": "djarang", "options.music_frequency.frequent": "sering", "options.music_frequency.tooltip": "<PERSON><PERSON><PERSON><PERSON> seberapa sering moesik dimaïnkan ketika didalam doenia permaïnan.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "k<PERSON><PERSON><PERSON> se<PERSON>", "options.narrator.chat": "k<PERSON><PERSON><PERSON> o<PERSON>lan", "options.narrator.notavailable": "ta' tersedia", "options.narrator.off": "mati", "options.narrator.system": "kisahkan sistem", "options.notifications.display_time": "tempo pemberitahoean", "options.notifications.display_time.tooltip": "Mengatoer lama tempo seloeroeh pemberitahoean tampak pada lajar.", "options.off": "mati", "options.off.composed": "%s: mati", "options.on": "njala", "options.on.composed": "%s: njala", "options.online": "<PERSON><PERSON> d<PERSON>...", "options.online.title": "<PERSON><PERSON>", "options.onlyShowSecureChat": "<PERSON><PERSON><PERSON> aman sadja", "options.onlyShowSecureChat.tooltip": "<PERSON>ja <PERSON> pesan dari pemaïn laïn jang bisa diverifikasi bahwa pesan itoe dikirim oleh pemaïn itoe, dan beloem dimodifikasi.", "options.operatorItemsTab": "<PERSON><PERSON><PERSON><PERSON> benda pengelola", "options.particles": "Partikel", "options.particles.all": "semoea", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "sedikit", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "<PERSON><PERSON><PERSON><PERSON><PERSON> bin<PERSON>", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON>i separ<PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Beberapa aksi didalam bingkah akan langsoeng menghimpoen oelang bingkah. Ini termas<PERSON>k menaroeh dan menghantjoerkan balok.", "options.prioritizeChunkUpdates.nearby": "men<PERSON>i pen<PERSON>", "options.prioritizeChunkUpdates.nearby.tooltip": "Bingkah terdekat selaloe terhimpoen lang<PERSON>eng. Ini dapat berdampak pada kinerdja permaïnan sa'at balok ditaroeh atau dihantjoerkan.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Bingkah terdekat dihimpoen dalam benang paralel. Ini dapat mengakibatkan moentjoelnja loebang jang terlihat setjara singkat sa'at balok dihantjoerkan.", "options.rawMouseInput": "Maso<PERSON><PERSON> mentah", "options.realmsNotifications": "Kabar Realms dan oendangan", "options.realmsNotifications.tooltip": "Mengambil kabar dan oendangan Realms dilajar djoedoel dan menampilkan lambangnja masing² pada kenop Realms.", "options.reducedDebugInfo": "Koerangi info lepas-kekoetoe", "options.renderClouds": "<PERSON><PERSON>", "options.renderCloudsDistance": "<PERSON><PERSON><PERSON>", "options.renderDistance": "Djarak pandang", "options.resourcepack": "<PERSON>et soember...", "options.rotateWithMinecart": "Berpoetar dengan kereta tambang", "options.rotateWithMinecart.tooltip": "Mengatoer apakah pandangan pemaïn berpoetar bersamaän dengan kereta tambang jang berbelok. Hanja tersedia didoenia dengan penjetelan „peningkatan kereta tambang” din<PERSON><PERSON><PERSON>.", "options.screenEffectScale": "Efek distorsi", "options.screenEffectScale.tooltip": "<PERSON><PERSON><PERSON><PERSON> efek distorsi pada lajar moeal dan lajar gerbang <PERSON>.\nPada nilai jang lebih ketjil, efek moeal digantikan dengan paparan hidjau.", "options.sensitivity": "Sensitivitet", "options.sensitivity.max": "MAHATJEPAT!!!", "options.sensitivity.min": "*koeap*", "options.showNowPlayingToast": "Pemberitahoean moesik", "options.showNowPlayingToast.tooltip": "Menampilkan pemberitahoean djoedoel moesik ketika moesik moelai dimaïnkan. Pemberitahoean jang sama akan ditampilkan setjara teroes-meneroes dilajar djeda ketika moesik sedang dimaïnkan.", "options.showSubtitles": "<PERSON><PERSON><PERSON>", "options.simulationDistance": "<PERSON><PERSON><PERSON>", "options.skinCustomisation": "Pengoebah-soeaian roepa...", "options.skinCustomisation.title": "Pen<PERSON>eb<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.sounds": "<PERSON><PERSON> dan soeara...", "options.sounds.title": "Opsi Moesik dan <PERSON>", "options.telemetry": "Pengoempoelan data...", "options.telemetry.button": "Pengoempoelan data", "options.telemetry.button.tooltip": "„%s” mentjakoep data jang diperloekan sadja.\n„%s” mentjakoep data jang diperloekan serta data opsionil.", "options.telemetry.disabled": "Pengoempoelan data mati.", "options.telemetry.state.all": "semoea", "options.telemetry.state.minimal": "sedikit", "options.telemetry.state.none": "tidak ada", "options.title": "Opsi", "options.touchscreen": "<PERSON><PERSON> lajar sentoeh", "options.video": "Penjetelan video...", "options.videoTitle": "Penjetelan video", "options.viewBobbing": "<PERSON><PERSON><PERSON> realistis", "options.visible": "tampak", "options.vsync": "Sinkronisasi vertikal", "outOfMemory.message": "Minecraft telah kehabisan roeang.\n\nIni bisa djadi disebabkan oleh kekoetoe dalam permaïnan atau oleh Java Virtual Machine jang tidak ditentoekan roeang jang tjoekoep.\n\nOentoek menghindari keroesakan doenia, permaïnan telah ditoetoep. <PERSON><PERSON> soedah mentjoba oentoek menambah roeang agar toean bisa kembali kelajar oetama dan kembali berma<PERSON>, tetapi ini beloem tentoe bisa bekerdja.\n\nMohon moeat-oelang permaïnan bila toean melihat pesan ini lagi.", "outOfMemory.title": "Kehabisan roeang!", "pack.available.title": "Tersedia", "pack.copyFailure": "<PERSON>et gagal disalin", "pack.dropConfirm": "<PERSON><PERSON><PERSON><PERSON> toean ingin menambahkan paket² berikoet ke-Minecraft?", "pack.dropInfo": "Seret dan lepas berkas pada djendela ini oentoek menambahkan paket", "pack.dropRejected.message": "<PERSON><PERSON><PERSON> berikoet boekan paket jang sah se<PERSON>ga tidak tersalin:\n %s", "pack.dropRejected.title": "<PERSON><PERSON><PERSON> boekan paket", "pack.folderInfo": "(<PERSON><PERSON><PERSON> berkas paket disini)", "pack.incompatible": "Tidak t<PERSON>jok", "pack.incompatible.confirm.new": "Paket ini diboeat oentoek versi Minecraft jang lebih baroe dan moengkin tidak bisa bekerdja dengan baïk.", "pack.incompatible.confirm.old": "Paket ini diboeat oentoek versi Minecraft jang lebih lama dan moengkin tidak bisa bekerdja dengan baïk.", "pack.incompatible.confirm.title": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin memoeat paket ini?", "pack.incompatible.new": "(Diboeat oentoek versi Minecraft jang lebih baroe)", "pack.incompatible.old": "(Diboeat oentoek versi Minecraft jang lebih lama)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Boeka map paket", "pack.selected.title": "<PERSON><PERSON><PERSON><PERSON>", "pack.source.builtin": "b<PERSON><PERSON><PERSON>", "pack.source.feature": "t<PERSON>ri", "pack.source.local": "lokal", "pack.source.server": "peladen", "pack.source.world": "doenia", "painting.dimensions": "%s×%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albania", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "<PERSON><PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "<PERSON><PERSON>", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Boeket", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "<PERSON>g<PERSON><PERSON>", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Ganti", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Selamat Siang Meneer Courbet", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON>", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Baas Achir", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Penemoean", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Api", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab dengan Tiga <PERSON>oni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON><PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditatif", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Lorong", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Tanaman Giok", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Teloendjoek", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Telaga", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "Kolam", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Perdjalanan Padan<PERSON> Roempo<PERSON>", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "<PERSON><PERSON>", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Lilitan Fan<PERSON>'", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "Pangg<PERSON>ng <PERSON>", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Kanigara", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "<PERSON><PERSON>", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "Pengembara", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "<PERSON><PERSON>", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON><PERSON>, <PERSON>", "painting.random": "<PERSON><PERSON> at<PERSON>", "parsing.bool.expected": "<PERSON><PERSON>", "parsing.bool.invalid": "<PERSON><PERSON> tidak sah, „true” atau „false” di<PERSON><PERSON><PERSON>, tetapi did<PERSON> „%s”", "parsing.double.expected": "<PERSON><PERSON> ganda diharapkan", "parsing.double.invalid": "Angka ganda „%s” tidak sah", "parsing.expected": "„%s” diharapkan", "parsing.float.expected": "Angka mengambang diharapkan", "parsing.float.invalid": "Angka mengambang „%s” tidak sah", "parsing.int.expected": "<PERSON><PERSON> boelat <PERSON>", "parsing.int.invalid": "Angka boelat „%s” tidak sah", "parsing.long.expected": "Ang<PERSON> besar diharapkan", "parsing.long.invalid": "Angka besar „%s” tidak sah", "parsing.quote.escape": "Oeroetan koentji keloear tidak sah „\\%s” dalam oentaian berkoetip", "parsing.quote.expected.end": "Oentaian tidak ada tanda koetip penoe<PERSON>ep", "parsing.quote.expected.start": "Tanda koetip diharapkan oentoek memoelai oentaian", "particle.invalidOptions": "Tidak bisa mengoeraikan opsi partikel: %s", "particle.notFound": "Partikel tidak dikenal: %s", "permissions.requires.entity": "Entitet diperloekan agar bisa mendjalankan perintah disini", "permissions.requires.player": "<PERSON><PERSON><PERSON><PERSON> diperloekan agar bisa mendjalankan perintah disini", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Ketika diterapkan:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predikat tidak dikenal: %s", "quickplay.error.invalid_identifier": "Tidak bisa menemoekan doenia dengan pengenal jang diberikan", "quickplay.error.realm_connect": "Tidak dapat terhoeboeng ke-Realm", "quickplay.error.realm_permission": "Tidak ada izin oentoek terhoeboeng ke-Realm ini", "quickplay.error.title": "<PERSON><PERSON><PERSON>", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, AS", "realms.configuration.region.east_asia": "Hongkong", "realms.configuration.region.east_us": "Virginia, AS", "realms.configuration.region.east_us_2": "North Carolina, AS", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "<PERSON><PERSON><PERSON><PERSON> timoer", "realms.configuration.region.japan_west": "Djepang barat", "realms.configuration.region.korea_central": "Korea Selatan", "realms.configuration.region.north_central_us": "Illinois, AS", "realms.configuration.region.north_europe": "<PERSON><PERSON><PERSON>", "realms.configuration.region.south_central_us": "Texas, AS", "realms.configuration.region.southeast_asia": "Singapoera", "realms.configuration.region.sweden_central": "Swéden", "realms.configuration.region.uae_north": "Persatoean Emirat Arab", "realms.configuration.region.uk_south": "<PERSON><PERSON><PERSON>", "realms.configuration.region.west_central_us": "Utah, AS", "realms.configuration.region.west_europe": "<PERSON><PERSON>", "realms.configuration.region.west_us": "California, AS", "realms.configuration.region.west_us_2": "Washington, AS", "realms.configuration.region_preference.automatic_owner": "Otomatis (latensi pemilik Realm)", "realms.configuration.region_preference.automatic_player": "Otomatis (pertama berga<PERSON> kesesi)", "realms.missing.snapshot.error.text": "Realms sa'at ini tidak didoekoeng dalam versi tjitra", "recipe.notFound": "Resep tidak dikenal: %s", "recipe.toast.description": "<PERSON><PERSON><PERSON> boekoe resep <PERSON>an", "recipe.toast.title": "Resep baroe terboeka!", "record.nowPlaying": "Sekarang memoetar: %s", "recover_world.bug_tracker": "Laporkan kekoetoe", "recover_world.button": "<PERSON><PERSON><PERSON> p<PERSON>", "recover_world.done.failed": "<PERSON><PERSON> poelih dari kead<PERSON><PERSON>n se<PERSON>.", "recover_world.done.success": "<PERSON><PERSON><PERSON><PERSON><PERSON> ber<PERSON>il!", "recover_world.done.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON>", "recover_world.issue.none": "Tidak ada masalah", "recover_world.message": "Beberapa masalah terdjadi sa'at mentjoba membatja map doenia „%s”.\nDoenia bisa dipoelihkan kekeadaän sebeloemnja atau toean bisa melaporkan masalah ini pada pelatjak kekoetoe.", "recover_world.no_fallback": "Tidak ada keadaän pemoelihan jang tersedia", "recover_world.restore": "<PERSON><PERSON><PERSON> p<PERSON>", "recover_world.restoring": "Mencoba memoelihkan dunia...", "recover_world.state_entry": "Keadaän dari %s: ", "recover_world.state_entry.unknown": "ta' diketahoei", "recover_world.title": "Doenia gagal dimoeat", "recover_world.warning": "Ringkasan doenia gagal dimoeat", "resourcePack.broken_assets": "NILAI ROESAK DITEMOEKAN", "resourcePack.high_contrast.name": "Kontras tinggi", "resourcePack.load_fail": "Gagal memoeat oelang soember daja", "resourcePack.programmer_art.name": "<PERSON><PERSON> Sen<PERSON>emrog<PERSON>", "resourcePack.runtime_failure": "Kesalahan paket soember terdeteksi", "resourcePack.server.name": "<PERSON>et soember choesoes doenia", "resourcePack.title": "<PERSON><PERSON><PERSON> paket soember", "resourcePack.vanilla.description": "<PERSON><PERSON><PERSON> dan sensasi asali Minecraft", "resourcePack.vanilla.name": "<PERSON><PERSON>", "resourcepack.downloading": "<PERSON><PERSON><PERSON><PERSON><PERSON> paket soember", "resourcepack.progress": "Mengoendoeh berkas (%s MB)...", "resourcepack.requesting": "Memboeat permintaän...", "screenshot.failure": "Tidak bisa menjimpan tangkapan lajar: %s", "screenshot.success": "Tangkapan lajar disimpan sebagai %s", "selectServer.add": "Tambah peladen", "selectServer.defaultName": "Peladen Minecraft", "selectServer.delete": "<PERSON><PERSON><PERSON>", "selectServer.deleteButton": "<PERSON><PERSON><PERSON>", "selectServer.deleteQuestion": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin menghapoes peladen ini?", "selectServer.deleteWarning": "„%s” akan hilang selamanja! (Tempo jang lama!)", "selectServer.direct": "Samboenga<PERSON> lang<PERSON>", "selectServer.edit": "So<PERSON>ing", "selectServer.hiddenAddress": "(terse<PERSON><PERSON>ji)", "selectServer.refresh": "Segarkan", "selectServer.select": "Bergaboeng", "selectWorld.access_failure": "<PERSON><PERSON> gagal dimas<PERSON>ki", "selectWorld.allowCommands": "<PERSON><PERSON><PERSON> perintah tjoerang", "selectWorld.allowCommands.info": "<PERSON><PERSON><PERSON> /gamemode, /experience", "selectWorld.allowCommands.new": "<PERSON><PERSON><PERSON>", "selectWorld.backupEraseCache": "Hapoes data tembolok", "selectWorld.backupJoinConfirmButton": "Buat tjadangan dan moeat", "selectWorld.backupJoinSkipButton": "<PERSON>at sadja, saja siap!", "selectWorld.backupQuestion.customized": "<PERSON><PERSON> oebah-so<PERSON><PERSON> so<PERSON>h tidak did<PERSON>", "selectWorld.backupQuestion.downgrade": "Penoeroenan versi doenia tidak did<PERSON>", "selectWorld.backupQuestion.experimental": "Doenia dengan penjetelan pert<PERSON>n tidak did<PERSON>", "selectWorld.backupQuestion.snapshot": "Toean benar-benar ingin memoeat doenia ini?", "selectWorld.backupWarning.customized": "<PERSON><PERSON><PERSON><PERSON>, kami soedah tidak mendoekoeng doenia oebah-soeaian dalam versi Minecraft ini. Kami masih bisa memoeat doenia ini dan mendjaga semoea seperti apa adanja, tetapi medan baroe jang dihasilkan tidak akan dioebah-soeai lagi. Mohon ma'af atas ketidaknjamanan jang terdjadi!", "selectWorld.backupWarning.downgrade": "Doenia ini terachir dimaïnkan di-versi %s; toean berada di-versi %s.\nMenoeroenkan versi doenia dalam menjebabkan keroesakan—kami tidak bisa memastikan itoe dapat memoeat atau bekerdja. <PERSON><PERSON> toean masih ingin landjoet, mohon boeat tjadangan.", "selectWorld.backupWarning.experimental": "Doenia ini memakai penjetelan pertjobaän jang bisa berhenti kapan sadja. <PERSON><PERSON> tidak mendjamin bahwa itoe bisa memoeat atau bekerdja. <PERSON>r<PERSON><PERSON>-hat<PERSON><PERSON>!", "selectWorld.backupWarning.snapshot": "Doenia ini terachir dimaïnkan di-versi %s; toean berada di-versi %s. <PERSON><PERSON> boeat tjadangan bila toean mengalami keroesakan doenia.", "selectWorld.bonusItems": "<PERSON><PERSON>", "selectWorld.cheats": "<PERSON><PERSON><PERSON>", "selectWorld.commands": "<PERSON><PERSON><PERSON>", "selectWorld.conversion": "<PERSON><PERSON><PERSON> dikonversi!", "selectWorld.conversion.tooltip": "Do<PERSON> ini haroes diboeka di-versi lama (seperti 1.6.4) agar bisa dikonversi dengan aman", "selectWorld.create": "Boeat doenia baroe", "selectWorld.customizeType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.dataPacks": "Paket data", "selectWorld.data_read": "Membatja data doenia...", "selectWorld.delete": "<PERSON><PERSON><PERSON>", "selectWorld.deleteButton": "<PERSON><PERSON><PERSON>", "selectWorld.deleteQuestion": "<PERSON><PERSON><PERSON><PERSON> toean jakin ingin menghapoes doenia ini?", "selectWorld.deleteWarning": "„%s” akan hilang selamanja! (Tempo jang lama!)", "selectWorld.delete_failure": "<PERSON><PERSON> gagal dihapoes", "selectWorld.edit": "So<PERSON>ing", "selectWorld.edit.backup": "<PERSON><PERSON> t<PERSON>", "selectWorld.edit.backupCreated": "Mentjadangkan: %s", "selectWorld.edit.backupFailed": "Pentjadangan gagal", "selectWorld.edit.backupFolder": "Boeka map tjadangan", "selectWorld.edit.backupSize": "oekoeran: %s MB", "selectWorld.edit.export_worldgen_settings": "Ekspor setelan penghasilan doenia", "selectWorld.edit.export_worldgen_settings.failure": "Ekspor gagal", "selectWorld.edit.export_worldgen_settings.success": "Ekspor berhasil", "selectWorld.edit.openFolder": "Boeka map doenia", "selectWorld.edit.optimize": "Optimalisir <PERSON>", "selectWorld.edit.resetIcon": "<PERSON><PERSON> oelang lambang", "selectWorld.edit.save": "Simpan", "selectWorld.edit.title": "Soenting Doenia", "selectWorld.enterName": "<PERSON><PERSON>", "selectWorld.enterSeed": "<PERSON><PERSON> pengh<PERSON>l do<PERSON>", "selectWorld.experimental": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.experimental.details": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.experimental.details.entry": "<PERSON><PERSON><PERSON>n jang diperloekan: %s", "selectWorld.experimental.details.title": "Persjarata<PERSON> tjiri <PERSON>", "selectWorld.experimental.message": "Berhati-hatilah!\nPenjetelan ini memerloekan tjiri jang masih dalam pengembangan. <PERSON><PERSON>an bi<PERSON> be<PERSON>, r<PERSON><PERSON> atau tidak bekerdja pada pembaharoean berikoetnja.", "selectWorld.experimental.title": "<PERSON><PERSON><PERSON>", "selectWorld.experiments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.experiments.info": "Pertjobaän adalah tjiri baroe potensial. Berhati-hatilah karena beberapa hal dapat roesak. Pertjobaän tidak bisa dimatikan setelah pemboeatan doenia.", "selectWorld.futureworld.error.text": "Terdjadi kesalahan sa'at mentjoba oentoek memoeat doenia dari versi terbaroe. Ini soedah meroepakan tindakan berbahaja dari awal, dan ini tidak berhasil.", "selectWorld.futureworld.error.title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>han!", "selectWorld.gameMode": "Ragam perma<PERSON>", "selectWorld.gameMode.adventure": "pet<PERSON>alangan", "selectWorld.gameMode.adventure.info": "<PERSON>a seperti ragam bertahan hid<PERSON>, tetapi balok tidak bisa ditambahkan atau dihapoes.", "selectWorld.gameMode.adventure.line1": "<PERSON>a seperti ragam be<PERSON>han <PERSON>, tetapi balok tidak bisa", "selectWorld.gameMode.adventure.line2": "ditam<PERSON><PERSON> atau dihapoes", "selectWorld.gameMode.creative": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, bangoen dan d<PERSON><PERSON><PERSON>i tanpa batas. <PERSON><PERSON> bisa terbang, poe<PERSON> bahan-bahan ta' terbatas dan tidak bisa diloeka<PERSON> oleh monster.", "selectWorld.gameMode.creative.line1": "Soember daja ta' terbatas, bebas terbang dan", "selectWorld.gameMode.creative.line2": "balok bisa dihantjoerkan seketika", "selectWorld.gameMode.hardcore": "menantang", "selectWorld.gameMode.hardcore.info": "<PERSON><PERSON> bertahan hidoep terkoentji pada kesoelitan „soelit”. Toean tidak bisa bangkit kembali bila toean mati.", "selectWorld.gameMode.hardcore.line1": "<PERSON>a seperti ragam be<PERSON>, terk<PERSON><PERSON><PERSON> dise<PERSON>", "selectWorld.gameMode.hardcore.line2": "<PERSON><PERSON><PERSON>, dan hanja satoe njawa", "selectWorld.gameMode.spectator": "penonton", "selectWorld.gameMode.spectator.info": "Toean boleh lihat; ta' boleh sentoeh.", "selectWorld.gameMode.spectator.line1": "Toean boleh lihat; ta' boleh sentoeh", "selectWorld.gameMode.survival": "be<PERSON><PERSON>p", "selectWorld.gameMode.survival.info": "<PERSON><PERSON><PERSON><PERSON><PERSON> doenia penoeh keta'djoeban dimana toean membangoen, men<PERSON><PERSON><PERSON><PERSON><PERSON>, memboeat dan melawan monster.", "selectWorld.gameMode.survival.line1": "<PERSON><PERSON><PERSON> so<PERSON> daja, b<PERSON><PERSON>, r<PERSON><PERSON><PERSON>", "selectWorld.gameMode.survival.line2": "ting<PERSON>n, kesehatan dan makanan", "selectWorld.gameRules": "<PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings": "<PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings.failure": "<PERSON><PERSON>", "selectWorld.import_worldgen_settings.select_file": "<PERSON><PERSON><PERSON> (.json)", "selectWorld.incompatible.description": "Doenia ini tidak bisa diboeka dalam versi ini.\nDoenia terachir dimaïnkan dalam versi %s.", "selectWorld.incompatible.info": "Versi tidak tjotjok: %s", "selectWorld.incompatible.title": "Versi Tidak Tjotjok", "selectWorld.incompatible.tooltip": "Doenia ini tidak bisa diboeka karena diboeat oleh versi jang tidak tjotjok.", "selectWorld.incompatible_series": "Diboeat oleh versi jang tidak tjotjok", "selectWorld.load_folder_access": "Tidak bisa membatja atau memasoeki map doenia permaïnan!", "selectWorld.loading_list": "Memoeat daftar doenia", "selectWorld.locked": "Terkoentji oleh program Minecraft laïn jang sedang berdjalan", "selectWorld.mapFeatures": "<PERSON><PERSON><PERSON>", "selectWorld.mapFeatures.info": "<PERSON><PERSON>, kapal karam dll.", "selectWorld.mapType": "<PERSON><PERSON><PERSON>", "selectWorld.mapType.normal": "normal", "selectWorld.moreWorldOptions": "Opsi doenia laïnnja...", "selectWorld.newWorld": "<PERSON><PERSON> baroe", "selectWorld.recreate": "<PERSON><PERSON> o<PERSON>ng", "selectWorld.recreate.customized.text": "Doenia oebah-soeaian so<PERSON>h tidak didoekoeng dalam versi Minecraft ini. Kami masih bisa memboeat oelang lagi doenia ini dengan benih jang sama, tetapi pengoebah-soeaian medan apa poen akan hilang. <PERSON>hon ma'af atas ketidaknjamanan jang terdjadi!", "selectWorld.recreate.customized.title": "<PERSON><PERSON> oebah-so<PERSON><PERSON> so<PERSON>h tidak did<PERSON>", "selectWorld.recreate.error.text": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han sa'at mentjoba memboeat oelang doenia.", "selectWorld.recreate.error.title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>han!", "selectWorld.resource_load": "Menjiapkan soember daja...", "selectWorld.resultFolder": "<PERSON><PERSON> disimpan dalam:", "selectWorld.search": "tjari do<PERSON>", "selectWorld.seedInfo": "Kosongkan bila ingin benih atjak", "selectWorld.select": "Maïnkan doenia terpilih", "selectWorld.targetFolder": "Simpan map: %s", "selectWorld.title": "<PERSON><PERSON><PERSON>", "selectWorld.tooltip.fromNewerVersion1": "Doenia disimpan dalam versi jang lebih baroe,", "selectWorld.tooltip.fromNewerVersion2": "memoeat doenia ini bisa menjebabkan masalah!", "selectWorld.tooltip.snapshot1": "Djangan loepa memboeat tjadangan doenia ini", "selectWorld.tooltip.snapshot2": "sebeloem toean moeat ditjitra ini.", "selectWorld.unable_to_load": "Tidak bisa memoeat doenia", "selectWorld.version": "Versi:", "selectWorld.versionJoinButton": "Tetap moeat", "selectWorld.versionQuestion": "Toean benar-benar ingin memoeat doenia ini?", "selectWorld.versionUnknown": "ta' diketahoei", "selectWorld.versionWarning": "Doenia ini terachir dimaïnkan di-versi %s dan memoeatnja dalam versi ini bisa menjebabkan keroesakan!", "selectWorld.warning.deprecated.question": "<PERSON><PERSON><PERSON> tjiri jang dipakai soedah oesang dan akan berhenti bekerdja dimasa mendatang. A<PERSON><PERSON>h toean ingin landjoet?", "selectWorld.warning.deprecated.title": "Peringatan! Setelan ini memakai tjiri oesang", "selectWorld.warning.experimental.question": "<PERSON>elan ini bersifat pertjobaän dan soeatoe hari nanti bisa berhenti bekerd<PERSON>. A<PERSON><PERSON><PERSON> toean ingin landjoet?", "selectWorld.warning.experimental.title": "Peringatan! Setelan ini memakai tjiri pert<PERSON>n", "selectWorld.warning.lowDiskSpace.description": "<PERSON>ang didalam perangkat toean tidak mentjoekoepi.\nKehabisan roeang penjimpanan sa'at bermaïn bisa meroesak doenia toean.", "selectWorld.warning.lowDiskSpace.title": "Peringatan! Penjimpanan hampir penoeh!", "selectWorld.world": "Doenia", "sign.edit": "<PERSON><PERSON><PERSON> pesan papan tanda", "sleep.not_possible": "Tidak bisa melewati malam dengan berehat", "sleep.players_sleeping": "%s dari %s pemaïn sedang tidoer", "sleep.skipping_night": "<PERSON><PERSON><PERSON> sepandjang malam ini", "slot.only_single_allowed": "Diperbolehkan petak toenggal sadja, tetapi terdapat „%s”", "slot.unknown": "Petak „%s” tidak dikenal", "snbt.parser.empty_key": "Koentji tidak bisa kosong", "snbt.parser.expected_binary_numeral": "Bilangan basis doea (binér) diharapkan", "snbt.parser.expected_decimal_numeral": "Bilangan basis sepoeloeh diharapkan", "snbt.parser.expected_float_type": "Bilangan mengambang diharapkan", "snbt.parser.expected_hex_escape": "<PERSON><PERSON><PERSON> harfiah dengan pandjang %s diharapkan", "snbt.parser.expected_hex_numeral": "Bilangan basis enam belas diharapkan", "snbt.parser.expected_integer_type": "Bilangan boelat di<PERSON>", "snbt.parser.expected_non_negative_number": "Bilangan boekan negatif di<PERSON>", "snbt.parser.expected_number_or_boolean": "Bilangan atau Boole diharapkan", "snbt.parser.expected_string_uuid": "Oentaian jang mewakili pengenal choesoes semesta jang sah diharapkan", "snbt.parser.expected_unquoted_string": "Oentaian ta' dikoetip jang sah diharapkan", "snbt.parser.infinity_not_allowed": "<PERSON>ja diperbolehkan bilangan berbatas", "snbt.parser.invalid_array_element_type": "<PERSON><PERSON><PERSON> elemen larik tidak sah", "snbt.parser.invalid_character_name": "Nama karakter Unicode tidak sah", "snbt.parser.invalid_codepoint": "<PERSON>lai karakter Unicode tidak sah: %s", "snbt.parser.invalid_string_contents": "<PERSON><PERSON> oentaian tidak sah", "snbt.parser.invalid_unquoted_start": "Oentaian ta' dikoetip tidak bisa dimoelai dengan angka 0—9, + atau -", "snbt.parser.leading_zero_not_allowed": "Bilangan basis sepoeloeh tidak bisa diawali dengan 0", "snbt.parser.no_such_operation": "Operasi itoe tidak ada: %s", "snbt.parser.number_parse_failure": "Bilangan gagal dioeraikan: %s", "snbt.parser.undescore_not_allowed": "<PERSON><PERSON><PERSON> garis bawah tidak diperbolehkan pada awal atau achir bilangan", "soundCategory.ambient": "Sekitar/lingkoengan", "soundCategory.block": "Balok", "soundCategory.hostile": "Machloek djahat", "soundCategory.master": "<PERSON><PERSON><PERSON> o<PERSON>", "soundCategory.music": "Moe<PERSON>", "soundCategory.neutral": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.player": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.record": "Pelat/kotak nada", "soundCategory.ui": "Antarmoeka", "soundCategory.voice": "Soeara/oetjapan", "soundCategory.weather": "<PERSON><PERSON><PERSON><PERSON>", "spectatorMenu.close": "<PERSON><PERSON><PERSON> da<PERSON>ar", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON>", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON> se<PERSON>", "spectatorMenu.root.prompt": "<PERSON><PERSON> koentji oentoek memilih per<PERSON>, dan tekan lagi oentoek memakainja.", "spectatorMenu.team_teleport": "Teleportir keanggota regoe", "spectatorMenu.team_teleport.prompt": "<PERSON><PERSON><PERSON> regoe toed<PERSON><PERSON> teleportasi", "spectatorMenu.teleport": "Teleportir kepemaïn", "spectatorMenu.teleport.prompt": "<PERSON><PERSON><PERSON> p<PERSON> toe<PERSON> teleportasi", "stat.generalButton": "Oemoem", "stat.itemsButton": "<PERSON><PERSON>", "stat.minecraft.animals_bred": "<PERSON><PERSON>", "stat.minecraft.aviate_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.bell_ring": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.boat_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.clean_armor": "Potongan zirah di<PERSON>kan", "stat.minecraft.clean_banner": "Spandoek dibersihkan", "stat.minecraft.clean_shulker_box": "Kotak shulker dibersihkan", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_absorbed": "Serangan diserap", "stat.minecraft.damage_blocked_by_shield": "Serangan dihadang perisai", "stat.minecraft.damage_dealt": "Serangan <PERSON>", "stat.minecraft.damage_dealt_absorbed": "Sarangan dilak<PERSON>kan (diserap)", "stat.minecraft.damage_dealt_resisted": "Sarangan <PERSON> (ditahan)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON>an", "stat.minecraft.damage_taken": "Serangan diterima", "stat.minecraft.deaths": "<PERSON> mati", "stat.minecraft.drop": "<PERSON><PERSON>", "stat.minecraft.eat_cake_slice": "<PERSON>an tar dimakan", "stat.minecraft.enchant_item": "<PERSON><PERSON>", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON>", "stat.minecraft.fish_caught": "<PERSON><PERSON>", "stat.minecraft.fly_one_cm": "<PERSON><PERSON><PERSON> terbang", "stat.minecraft.happy_ghast_one_cm": "<PERSON>jarak naïk ghast riang", "stat.minecraft.horse_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.inspect_dispenser": "Pelempar digeledah", "stat.minecraft.inspect_dropper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.inspect_hopper": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_anvil": "<PERSON><PERSON><PERSON> dengan paron", "stat.minecraft.interact_with_beacon": "<PERSON><PERSON><PERSON> dengan soear", "stat.minecraft.interact_with_blast_furnace": "Interaksi dengan tanoer tioep", "stat.minecraft.interact_with_brewingstand": "Interaksi dengan standar peramoe", "stat.minecraft.interact_with_campfire": "Interaksi dengan api o<PERSON>", "stat.minecraft.interact_with_cartography_table": "Interaksi dengan medja kartografi", "stat.minecraft.interact_with_crafting_table": "Interaksi dengan medja k<PERSON>", "stat.minecraft.interact_with_furnace": "Interaksi dengan oven", "stat.minecraft.interact_with_grindstone": "<PERSON><PERSON><PERSON> dengan t<PERSON>i", "stat.minecraft.interact_with_lectern": "<PERSON><PERSON>i dengan lesnar", "stat.minecraft.interact_with_loom": "Interaksi dengan alat tenoen", "stat.minecraft.interact_with_smithing_table": "Interaksi dengan medja tempa", "stat.minecraft.interact_with_smoker": "Interaksi dengan pengasap", "stat.minecraft.interact_with_stonecutter": "Interaksi dengan pemahat batoe", "stat.minecraft.jump": "Kali lompat", "stat.minecraft.leave_game": "Permaïnan ditinggalkan", "stat.minecraft.minecart_one_cm": "Djarak naïk kereta tambang", "stat.minecraft.mob_kills": "Machloek diboenoeh", "stat.minecraft.open_barrel": "Tong diboeka", "stat.minecraft.open_chest": "<PERSON><PERSON>", "stat.minecraft.open_enderchest": "<PERSON><PERSON>", "stat.minecraft.open_shulker_box": "Kotak shuker diboeka", "stat.minecraft.pig_one_cm": "<PERSON><PERSON><PERSON> babi", "stat.minecraft.play_noteblock": "Kotak nada dimaïnkan", "stat.minecraft.play_record": "Pelat moesik dimaïnkan", "stat.minecraft.play_time": "<PERSON><PERSON>", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.pot_flower": "Tanaman ditanam dipot", "stat.minecraft.raid_trigger": "Serboean dip<PERSON><PERSON><PERSON>", "stat.minecraft.raid_win": "Serboean dita'lo<PERSON>kan", "stat.minecraft.sleep_in_bed": "<PERSON> tidoer dikatil", "stat.minecraft.sneak_time": "Tempo djongkok", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON> be<PERSON>", "stat.minecraft.strider_one_cm": "<PERSON><PERSON>ak naïk <PERSON>", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON> be<PERSON>", "stat.minecraft.talked_to_villager": "<PERSON><PERSON><PERSON> den<PERSON> pen<PERSON>", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON>", "stat.minecraft.time_since_death": "Tempo sedjak kematian terachir", "stat.minecraft.time_since_rest": "Tempo sedjak rehat terachir", "stat.minecraft.total_world_time": "Tempo dengan doenia terboeka", "stat.minecraft.traded_with_villager": "<PERSON><PERSON><PERSON><PERSON> dengan pendo<PERSON>", "stat.minecraft.trigger_trapped_chest": "<PERSON><PERSON>", "stat.minecraft.tune_noteblock": "Kotak nada disetem", "stat.minecraft.use_cauldron": "<PERSON><PERSON><PERSON> k<PERSON>", "stat.minecraft.walk_on_water_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.walk_under_water_one_cm": "<PERSON><PERSON><PERSON>", "stat.mobsButton": "Machloek", "stat_type.minecraft.broken": "<PERSON> r<PERSON>", "stat_type.minecraft.crafted": "Kali diboeat", "stat_type.minecraft.dropped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.killed": "Toean memboenoeh %s %s", "stat_type.minecraft.killed.none": "Toean tidak pernah memboenoeh %s", "stat_type.minecraft.killed_by": "%s telah membo<PERSON><PERSON>an %s kali", "stat_type.minecraft.killed_by.none": "Toean tidak pernah diboenoeh %s", "stat_type.minecraft.mined": "Kali ditambang", "stat_type.minecraft.picked_up": "Dipoengoet", "stat_type.minecraft.used": "<PERSON> dipakai", "stats.none": "-", "structure_block.button.detect_size": "PERIKSA", "structure_block.button.load": "MOEAT", "structure_block.button.save": "SIMPAN", "structure_block.custom_data": "Nama étikét data oebah-soeaian", "structure_block.detect_size": "<PERSON><PERSON><PERSON> dan posisi bang<PERSON>:", "structure_block.hover.corner": "Soedoet: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.load": "Moeat: %s", "structure_block.hover.save": "Simpan: %s", "structure_block.include_entities": "Sertakan entitet:", "structure_block.integrity": "<PERSON><PERSON> dan integritet bang<PERSON>nan", "structure_block.integrity.integrity": "Integritet bangoenan", "structure_block.integrity.seed": "<PERSON><PERSON>", "structure_block.invalid_structure_name": "<PERSON><PERSON> „%s” tidak sah", "structure_block.load_not_found": "Bangoenan „%s” tidak tersedia", "structure_block.load_prepare": "Posisi <PERSON>nan „%s” disiapkan", "structure_block.load_success": "Bangoenan dimoeat dari „%s”", "structure_block.mode.corner": "Soed<PERSON>t", "structure_block.mode.data": "Data", "structure_block.mode.load": "Moeat", "structure_block.mode.save": "Simpan", "structure_block.mode_info.corner": "<PERSON><PERSON> soed<PERSON> - penanda letak dan o<PERSON>ran", "structure_block.mode_info.data": "Ragam data - penanda logika permaïnan", "structure_block.mode_info.load": "Ragam moeat - memoeat dari berkas", "structure_block.mode_info.save": "<PERSON><PERSON> simpan - men<PERSON><PERSON> keb<PERSON>", "structure_block.position": "Posisi relatif", "structure_block.position.x": "posisi x relatif", "structure_block.position.y": "posisi y relatif", "structure_block.position.z": "posisi z relatif", "structure_block.save_failure": "Tidak bisa menjimpan bang<PERSON>nan „%s”", "structure_block.save_success": "Bangoenan tersimpan se<PERSON>ai „%s”", "structure_block.show_air": "Tampakkan balok ta' terlihat:", "structure_block.show_boundingbox": "<PERSON><PERSON><PERSON> garis batas", "structure_block.size": "Oekoeran <PERSON>", "structure_block.size.x": "ukuran x bangunan", "structure_block.size.y": "ukuran y bangunan", "structure_block.size.z": "ukuran z bangunan", "structure_block.size_failure": "Tidak bisa memeriksa oekoeran bangoenan. Tambahkan soedoet laïn jang sesoeai dengan nama bangoenan", "structure_block.size_success": "Oekoeran „%s” berhasil diperiksa", "structure_block.strict": "Penempatan ketat:", "structure_block.structure_name": "<PERSON><PERSON>", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON> se<PERSON>", "subtitles.ambient.sound": "<PERSON><PERSON><PERSON> se<PERSON>", "subtitles.block.amethyst_block.chime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> berb<PERSON>", "subtitles.block.amethyst_block.resonate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> berge<PERSON>", "subtitles.block.anvil.destroy": "<PERSON><PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON> mendarat", "subtitles.block.anvil.use": "<PERSON><PERSON>i", "subtitles.block.barrel.close": "Tong menoetoep", "subtitles.block.barrel.open": "Tong memboeka", "subtitles.block.beacon.activate": "<PERSON><PERSON> men<PERSON>la", "subtitles.block.beacon.ambient": "<PERSON>ear be<PERSON>", "subtitles.block.beacon.deactivate": "Soear mati", "subtitles.block.beacon.power_select": "<PERSON><PERSON> so<PERSON> dipilih", "subtitles.block.beehive.drip": "<PERSON>oe menetes", "subtitles.block.beehive.enter": "<PERSON><PERSON> memasoeki sarang", "subtitles.block.beehive.exit": "Lebah meninggalkan sarang", "subtitles.block.beehive.shear": "Goenting men<PERSON><PERSON>", "subtitles.block.beehive.work": "<PERSON><PERSON> be<PERSON>d<PERSON>", "subtitles.block.bell.resonate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.bell.use": "<PERSON><PERSON><PERSON>ng berdenting", "subtitles.block.big_dripleaf.tilt_down": "<PERSON><PERSON><PERSON> tetes menoe<PERSON>", "subtitles.block.big_dripleaf.tilt_up": "<PERSON><PERSON>n tetes mendongak", "subtitles.block.blastfurnace.fire_crackle": "Gemeretak tanoer tioep", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON> per<PERSON>e berb<PERSON>", "subtitles.block.bubble_column.bubble_pop": "Gelemboeng meletoes", "subtitles.block.bubble_column.upwards_ambient": "Gelemboeng mengalir", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON><PERSON> berdesis", "subtitles.block.bubble_column.whirlpool_ambient": "Gelemboeng berpoetar", "subtitles.block.bubble_column.whirlpool_inside": "Gelemboeng bertioep", "subtitles.block.button.click": "Kenop dipentjet", "subtitles.block.cake.add_candle": "Tar ditekan", "subtitles.block.campfire.crackle": "Gemeretak api oenggoen", "subtitles.block.candle.crackle": "Gemeretak lilin", "subtitles.block.candle.extinguish": "<PERSON><PERSON>", "subtitles.block.chest.close": "<PERSON><PERSON>", "subtitles.block.chest.locked": "<PERSON><PERSON>", "subtitles.block.chest.open": "<PERSON><PERSON> me<PERSON>", "subtitles.block.chorus_flower.death": "<PERSON><PERSON><PERSON> kor lajoe", "subtitles.block.chorus_flower.grow": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.block.comparator.click": "Pembanding dipentjet", "subtitles.block.composter.empty": "Bak kompos dikosongkan", "subtitles.block.composter.fill": "<PERSON><PERSON><PERSON> bak kompos", "subtitles.block.composter.ready": "Bak kompos berkompos", "subtitles.block.conduit.activate": "Tenaga laoet dihidoepkan", "subtitles.block.conduit.ambient": "Tenaga la<PERSON>t be<PERSON>", "subtitles.block.conduit.attack.target": "Tenaga laoet menjerang", "subtitles.block.conduit.deactivate": "Tenaga laoet mati", "subtitles.block.copper_bulb.turn_off": "<PERSON><PERSON><PERSON> tembaga padam", "subtitles.block.copper_bulb.turn_on": "<PERSON><PERSON><PERSON> temba<PERSON> menjala", "subtitles.block.copper_trapdoor.close": "Pintoe kolong menoetoep", "subtitles.block.copper_trapdoor.open": "Pintoe kolong memboeka", "subtitles.block.crafter.craft": "<PERSON><PERSON> kera<PERSON> me<PERSON>jin", "subtitles.block.crafter.fail": "<PERSON><PERSON> kera<PERSON>an gagal meradjin", "subtitles.block.creaking_heart.hurt": "Djantung kerioet mengger<PERSON>e", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON> se<PERSON>", "subtitles.block.creaking_heart.spawn": "<PERSON><PERSON><PERSON><PERSON> kerioet bangoen", "subtitles.block.deadbush.idle": "<PERSON><PERSON><PERSON><PERSON> kering", "subtitles.block.decorated_pot.insert": "<PERSON><PERSON><PERSON> pasoe", "subtitles.block.decorated_pot.insert_fail": "<PERSON><PERSON>", "subtitles.block.decorated_pot.shatter": "<PERSON><PERSON>", "subtitles.block.dispenser.dispense": "<PERSON><PERSON>", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON><PERSON> gagal", "subtitles.block.door.toggle": "Pi<PERSON>e menderit", "subtitles.block.dried_ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON> k<PERSON> te<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON><PERSON> kering men<PERSON>ap", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON> k<PERSON> me<PERSON>", "subtitles.block.dry_grass.ambient": "<PERSON><PERSON> bert<PERSON>", "subtitles.block.enchantment_table.use": "<PERSON><PERSON><PERSON> p<PERSON>a <PERSON>i", "subtitles.block.end_portal.spawn": "Gerbang End terboeka", "subtitles.block.end_portal_frame.fill": "<PERSON>", "subtitles.block.eyeblossom.close": "<PERSON><PERSON><PERSON> mata terpedjam", "subtitles.block.eyeblossom.idle": "<PERSON><PERSON><PERSON> mata be<PERSON>", "subtitles.block.eyeblossom.open": "<PERSON><PERSON><PERSON> mata terdjaga", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON><PERSON> pagar menderit", "subtitles.block.fire.ambient": "Gemeretak api", "subtitles.block.fire.extinguish": "<PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Koenang-koenang be<PERSON>", "subtitles.block.frogspawn.hatch": "<PERSON><PERSON><PERSON><PERSON> meneta<PERSON>", "subtitles.block.furnace.fire_crackle": "Gemeretak oven", "subtitles.block.generic.break": "<PERSON><PERSON><PERSON>", "subtitles.block.generic.fall": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> diatas balok", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON><PERSON> kaki", "subtitles.block.generic.hit": "Menghant<PERSON><PERSON><PERSON> balok", "subtitles.block.generic.place": "Balok di<PERSON>h", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON> dipakai", "subtitles.block.growing_plant.crop": "Tanaman dipotong", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON> tanda be<PERSON>g", "subtitles.block.honey_block.slide": "<PERSON><PERSON><PERSON><PERSON><PERSON> toe<PERSON>en balok madoe", "subtitles.block.iron_trapdoor.close": "Pintoe kolong menoetoep", "subtitles.block.iron_trapdoor.open": "Pintoe kolong memboeka", "subtitles.block.lava.ambient": "<PERSON><PERSON> melet<PERSON>", "subtitles.block.lava.extinguish": "<PERSON><PERSON> mendesis", "subtitles.block.lever.click": "Toeas dipetik", "subtitles.block.note_block.note": "Kotak nada dimaïnkan", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON> se<PERSON>", "subtitles.block.piston.move": "<PERSON><PERSON> bergerak", "subtitles.block.pointed_dripstone.drip_lava": "Lava menetes", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava menetes kedalam koeali", "subtitles.block.pointed_dripstone.drip_water": "<PERSON><PERSON><PERSON> menetes", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON><PERSON><PERSON> menetes kedalam koeali", "subtitles.block.pointed_dripstone.land": "Stalaktit djatoeh", "subtitles.block.portal.ambient": "<PERSON><PERSON><PERSON> gerbang", "subtitles.block.portal.travel": "<PERSON><PERSON><PERSON> ger<PERSON>", "subtitles.block.portal.trigger": "<PERSON><PERSON><PERSON> gerbang mengo<PERSON>", "subtitles.block.pressure_plate.click": "<PERSON><PERSON><PERSON> pen<PERSON>n be<PERSON>", "subtitles.block.pumpkin.carve": "Goenting men<PERSON><PERSON><PERSON>", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON> mendesis", "subtitles.block.respawn_anchor.ambient": "<PERSON>busan angker pembangkit", "subtitles.block.respawn_anchor.charge": "<PERSON><PERSON><PERSON> ang<PERSON> pembangkit", "subtitles.block.respawn_anchor.deplete": "Angker pembangkit mengoeras", "subtitles.block.respawn_anchor.set_spawn": "Angker pembangkit menjetel titik bangkit", "subtitles.block.sand.idle": "<PERSON><PERSON><PERSON><PERSON> pasir", "subtitles.block.sand.wind": "<PERSON><PERSON> bert<PERSON>", "subtitles.block.sculk.charge": "Sculk berboeih", "subtitles.block.sculk.spread": "Sculk menjebar", "subtitles.block.sculk_catalyst.bloom": "Katalisator sculk mekar", "subtitles.block.sculk_sensor.clicking": "<PERSON><PERSON><PERSON> sculk berdetik", "subtitles.block.sculk_sensor.clicking_stop": "<PERSON><PERSON><PERSON> sculk ber<PERSON><PERSON> berde<PERSON>k", "subtitles.block.sculk_shrieker.shriek": "Sculk pendjerit mendjerit", "subtitles.block.shulker_box.close": "Kotak shulker menoetoep", "subtitles.block.shulker_box.open": "<PERSON><PERSON> memboeka", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON> tanda be<PERSON>g", "subtitles.block.smithing_table.use": "Medja tempa dipakai", "subtitles.block.smoker.smoke": "Pengasap berasap", "subtitles.block.sniffer_egg.crack": "Teloer pengendoes retak", "subtitles.block.sniffer_egg.hatch": "<PERSON><PERSON>r pengendoes menetas", "subtitles.block.sniffer_egg.plop": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "subtitles.block.sponge.absorb": "Spons mengisap", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON>eni dipetik", "subtitles.block.trapdoor.close": "Pintoe kolong menoetoep", "subtitles.block.trapdoor.open": "Pintoe kolong memboeka", "subtitles.block.trapdoor.toggle": "Pintoe kolong menderit", "subtitles.block.trial_spawner.about_to_spawn_item": "<PERSON><PERSON> seram di<PERSON>", "subtitles.block.trial_spawner.ambient": "Gemeretak pembangkit tjobaän", "subtitles.block.trial_spawner.ambient_charged": "Gemeretak seram", "subtitles.block.trial_spawner.ambient_ominous": "Gemeretak seram", "subtitles.block.trial_spawner.charge_activate": "<PERSON><PERSON><PERSON> boe<PERSON><PERSON> melanda pembang<PERSON>t tjo<PERSON>n", "subtitles.block.trial_spawner.close_shutter": "Pembangkit tjobaän menoetoep", "subtitles.block.trial_spawner.detect_player": "Pembangkit tjobaän memoeat", "subtitles.block.trial_spawner.eject_item": "Pembangkit tjobaän men<PERSON>oeark<PERSON> benda", "subtitles.block.trial_spawner.ominous_activate": "<PERSON><PERSON><PERSON> boe<PERSON><PERSON> melanda pembang<PERSON>t tjo<PERSON>n", "subtitles.block.trial_spawner.open_shutter": "Pembangkit tjobaän memboeka", "subtitles.block.trial_spawner.spawn_item": "<PERSON><PERSON> se<PERSON> moe<PERSON>", "subtitles.block.trial_spawner.spawn_item_begin": "<PERSON><PERSON>", "subtitles.block.trial_spawner.spawn_mob": "Pembangkit tjobaän membangkitkan machloek", "subtitles.block.tripwire.attach": "<PERSON><PERSON> senggol terpasang", "subtitles.block.tripwire.click": "<PERSON><PERSON> senggol disenggol", "subtitles.block.tripwire.detach": "<PERSON><PERSON> seng<PERSON> poetoes", "subtitles.block.vault.activate": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.ambient": "Gemeretak brankas", "subtitles.block.vault.close_shutter": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.deactivate": "<PERSON><PERSON><PERSON> padam", "subtitles.block.vault.eject_item": "<PERSON><PERSON><PERSON> benda", "subtitles.block.vault.insert_item": "Koentji brankas diboeka", "subtitles.block.vault.insert_item_fail": "<PERSON><PERSON><PERSON> brankas gagal diboeka", "subtitles.block.vault.open_shutter": "Brankas memboeka", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON><PERSON>", "subtitles.block.water.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.wet_sponge.dries": "Spons mengering", "subtitles.chiseled_bookshelf.insert": "<PERSON><PERSON><PERSON>", "subtitles.chiseled_bookshelf.insert_enchanted": "<PERSON><PERSON><PERSON> p<PERSON>a di<PERSON>", "subtitles.chiseled_bookshelf.take": "<PERSON><PERSON><PERSON>", "subtitles.chiseled_bookshelf.take_enchanted": "<PERSON><PERSON><PERSON> p<PERSON>a diambil", "subtitles.enchant.thorns.hit": "<PERSON><PERSON><PERSON><PERSON> doeri", "subtitles.entity.allay.ambient_with_item": "Penolong mentjari", "subtitles.entity.allay.ambient_without_item": "Penolong mendambakan", "subtitles.entity.allay.death": "Penolong mati", "subtitles.entity.allay.hurt": "Penolong terloeka", "subtitles.entity.allay.item_given": "<PERSON><PERSON><PERSON> bergelak", "subtitles.entity.allay.item_taken": "Penolong bertenang", "subtitles.entity.allay.item_thrown": "Penolong melempar", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.brush": "Sisik disikat", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.hurt_reduced": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.land": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.roll": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.scute_drop": "Armadilo merontokkan sisik", "subtitles.entity.armadillo.unroll_finish": "<PERSON><PERSON><PERSON> me<PERSON> go<PERSON>", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON>", "subtitles.entity.armor_stand.fall": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.arrow.hit": "<PERSON><PERSON>", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON><PERSON> terk<PERSON>ah", "subtitles.entity.arrow.shoot": "<PERSON><PERSON>", "subtitles.entity.axolotl.attack": "Axolotl menjerang", "subtitles.entity.axolotl.death": "Axolotl mati", "subtitles.entity.axolotl.hurt": "Axolotl terloeka", "subtitles.entity.axolotl.idle_air": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "subtitles.entity.axolotl.idle_water": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "subtitles.entity.axolotl.splash": "Axolotl menjiprat", "subtitles.entity.axolotl.swim": "<PERSON><PERSON><PERSON><PERSON> berenang", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON><PERSON> meleng<PERSON>", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.bat.hurt": "Ke<PERSON>lawar terl<PERSON>", "subtitles.entity.bat.takeoff": "<PERSON><PERSON>law<PERSON> terbang", "subtitles.entity.bee.ambient": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.bee.death": "Le<PERSON> mati", "subtitles.entity.bee.hurt": "<PERSON><PERSON> terl<PERSON>", "subtitles.entity.bee.loop": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.bee.loop_aggressive": "Lebah berdengoeng marah", "subtitles.entity.bee.pollinate": "<PERSON><PERSON> berden<PERSON> bahagia", "subtitles.entity.bee.sting": "Sengatan lebah", "subtitles.entity.blaze.ambient": "<PERSON><PERSON><PERSON><PERSON> berna<PERSON>", "subtitles.entity.blaze.burn": "Gemeretak kemamang", "subtitles.entity.blaze.death": "Kemamang mati", "subtitles.entity.blaze.hurt": "Kemamang terl<PERSON>", "subtitles.entity.blaze.shoot": "Ke<PERSON><PERSON>g <PERSON>", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Bengkarak rawa menggemeretoek", "subtitles.entity.bogged.death": "Bengkarak rawa mati", "subtitles.entity.bogged.hurt": "Bengkarak rawa terloeka", "subtitles.entity.breeze.charge": "<PERSON><PERSON> men<PERSON><PERSON> serangan", "subtitles.entity.breeze.death": "<PERSON><PERSON> mati", "subtitles.entity.breeze.deflect": "<PERSON><PERSON>", "subtitles.entity.breeze.hurt": "Pawana terloeka", "subtitles.entity.breeze.idle_air": "Pawana terbang", "subtitles.entity.breeze.idle_ground": "Pawana menderoe", "subtitles.entity.breeze.inhale": "<PERSON><PERSON>", "subtitles.entity.breeze.jump": "Pawana melompat", "subtitles.entity.breeze.land": "<PERSON><PERSON> mendarat", "subtitles.entity.breeze.shoot": "Pawana <PERSON>", "subtitles.entity.breeze.slide": "<PERSON><PERSON> melo<PERSON>", "subtitles.entity.breeze.whirl": "Pa<PERSON> berpoetar", "subtitles.entity.breeze.wind_burst": "<PERSON><PERSON> angin meledak", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON> menden<PERSON>", "subtitles.entity.camel.dash": "Lompatan oenta", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON> ma<PERSON>", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.camel.saddle": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.sit": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.stand": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.step": "<PERSON><PERSON><PERSON> me<PERSON>", "subtitles.entity.camel.step_sand": "<PERSON><PERSON><PERSON> melang<PERSON>h dipasir", "subtitles.entity.cat.ambient": "<PERSON><PERSON><PERSON><PERSON> men<PERSON>g", "subtitles.entity.cat.beg_for_food": "<PERSON><PERSON><PERSON><PERSON> meminta", "subtitles.entity.cat.death": "<PERSON><PERSON>jing mati", "subtitles.entity.cat.eat": "Koetjing makan", "subtitles.entity.cat.hiss": "<PERSON><PERSON><PERSON>g mendesis", "subtitles.entity.cat.hurt": "Koetjing terloeka", "subtitles.entity.cat.purr": "<PERSON><PERSON><PERSON><PERSON> menden<PERSON>", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.chicken.egg": "<PERSON><PERSON><PERSON>", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.cod.death": "Bakalau mati", "subtitles.entity.cod.flop": "Bakalau melompat", "subtitles.entity.cod.hurt": "Bakalau terloeka", "subtitles.entity.cow.ambient": "<PERSON><PERSON> me<PERSON>", "subtitles.entity.cow.death": "<PERSON>pi mati", "subtitles.entity.cow.hurt": "<PERSON><PERSON>", "subtitles.entity.cow.milk": "<PERSON><PERSON> diperah", "subtitles.entity.creaking.activate": "<PERSON><PERSON><PERSON> melihat", "subtitles.entity.creaking.ambient": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.creaking.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON> be<PERSON>am", "subtitles.entity.creaking.death": "<PERSON><PERSON><PERSON> roboh", "subtitles.entity.creaking.freeze": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.creaking.spawn": "<PERSON><PERSON><PERSON> men<PERSON>", "subtitles.entity.creaking.sway": "Kerioet diserang", "subtitles.entity.creaking.twitch": "Ke<PERSON>et tersentak", "subtitles.entity.creaking.unfreeze": "<PERSON><PERSON><PERSON> bergerak", "subtitles.entity.creeper.death": "C<PERSON>per mati", "subtitles.entity.creeper.hurt": "Creeper terloeka", "subtitles.entity.creeper.primed": "Creeper mendesis", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON><PERSON><PERSON> loemba-loemba", "subtitles.entity.dolphin.ambient_water": "Loemba-lo<PERSON><PERSON> berde<PERSON>s", "subtitles.entity.dolphin.attack": "Loemba-lo<PERSON>ba men<PERSON>", "subtitles.entity.dolphin.death": "Loemba-loemba mati", "subtitles.entity.dolphin.eat": "Loemba-loemba makan", "subtitles.entity.dolphin.hurt": "Loemba-lo<PERSON>ba terloeka", "subtitles.entity.dolphin.jump": "Loemba-loemba melompat", "subtitles.entity.dolphin.play": "Loemba-<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.splash": "Loemba-lo<PERSON><PERSON> men<PERSON>t", "subtitles.entity.dolphin.swim": "Loemba-lo<PERSON>ba berenang", "subtitles.entity.donkey.ambient": "Keledai me<PERSON>ih", "subtitles.entity.donkey.angry": "Keledai meringkik", "subtitles.entity.donkey.chest": "Peti dipasangkan kekeledai", "subtitles.entity.donkey.death": "Keledai mati", "subtitles.entity.donkey.eat": "Keledai makan", "subtitles.entity.donkey.hurt": "Keledai terloeka", "subtitles.entity.donkey.jump": "Keledai melompat", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON> a<PERSON> mati", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON> a<PERSON><PERSON> t<PERSON>", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON> a<PERSON><PERSON>", "subtitles.entity.egg.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON><PERSON> pendjaga mengerang", "subtitles.entity.elder_guardian.ambient_land": "Te<PERSON><PERSON> pendjaga mengg<PERSON>", "subtitles.entity.elder_guardian.curse": "<PERSON><PERSON><PERSON> pen<PERSON>", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON> pendjaga mati", "subtitles.entity.elder_guardian.flop": "Tetoea pendjaga melompat", "subtitles.entity.elder_guardian.hurt": "Teto<PERSON> pendjaga terl<PERSON>", "subtitles.entity.ender_dragon.ambient": "Naga menga<PERSON>m", "subtitles.entity.ender_dragon.death": "Naga mati", "subtitles.entity.ender_dragon.flap": "Kelepak Naga", "subtitles.entity.ender_dragon.growl": "<PERSON>ga men<PERSON>am", "subtitles.entity.ender_dragon.hurt": "Naga terloeka", "subtitles.entity.ender_dragon.shoot": "Naga <PERSON>", "subtitles.entity.ender_eye.death": "<PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON> <PERSON>", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> mendewoep", "subtitles.entity.enderman.death": "<PERSON><PERSON> mati", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> terl<PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON> mendjerit", "subtitles.entity.enderman.stare": "Enderman member<PERSON>", "subtitles.entity.enderman.teleport": "Enderman berteleportir", "subtitles.entity.endermite.ambient": "<PERSON><PERSON><PERSON> berdetjit", "subtitles.entity.endermite.death": "Endermite mati", "subtitles.entity.endermite.hurt": "Endermite terloeka", "subtitles.entity.evoker.ambient": "<PERSON><PERSON>", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON>", "subtitles.entity.evoker.celebrate": "<PERSON><PERSON>", "subtitles.entity.evoker.death": "<PERSON><PERSON> mati", "subtitles.entity.evoker.hurt": "<PERSON><PERSON>", "subtitles.entity.evoker.prepare_attack": "<PERSON><PERSON> men<PERSON> serangan", "subtitles.entity.evoker.prepare_summon": "Pawang men<PERSON>kan pemanggilan", "subtitles.entity.evoker.prepare_wololo": "<PERSON>wang membatja mantra", "subtitles.entity.evoker_fangs.attack": "Taring menggigit", "subtitles.entity.experience_orb.pickup": "Pengalaman diperoleh", "subtitles.entity.firework_rocket.blast": "Ledakan kembang api", "subtitles.entity.firework_rocket.launch": "Kembang api diloentjo<PERSON>kan", "subtitles.entity.firework_rocket.twinkle": "Kembang api berk<PERSON>p", "subtitles.entity.fish.swim": "<PERSON><PERSON>", "subtitles.entity.fishing_bobber.retrieve": "Pelampoeng dikembalikan", "subtitles.entity.fishing_bobber.splash": "Pertjikan pelampoeng pantjingan", "subtitles.entity.fishing_bobber.throw": "Pelampoeng dilempar", "subtitles.entity.fox.aggro": "Roebah marah", "subtitles.entity.fox.ambient": "<PERSON><PERSON> berdet<PERSON>", "subtitles.entity.fox.bite": "Roebah menggigit", "subtitles.entity.fox.death": "Roebah mati", "subtitles.entity.fox.eat": "Roebah makan", "subtitles.entity.fox.hurt": "Roebah terloeka", "subtitles.entity.fox.screech": "Roe<PERSON> melengking", "subtitles.entity.fox.sleep": "<PERSON><PERSON> mendengkoer", "subtitles.entity.fox.sniff": "Roe<PERSON> mengendoes", "subtitles.entity.fox.spit": "Roebah meloedah", "subtitles.entity.fox.teleport": "Roebah berteleportir", "subtitles.entity.frog.ambient": "<PERSON><PERSON> berk<PERSON>", "subtitles.entity.frog.death": "Katak mati", "subtitles.entity.frog.eat": "<PERSON>ak makan", "subtitles.entity.frog.hurt": "<PERSON><PERSON> te<PERSON>", "subtitles.entity.frog.lay_spawn": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.frog.long_jump": "Katak melompat", "subtitles.entity.generic.big_fall": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.burn": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.death": "Sekarat", "subtitles.entity.generic.drink": "Menjeroepoet", "subtitles.entity.generic.eat": "Memakan", "subtitles.entity.generic.explode": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON><PERSON><PERSON> api", "subtitles.entity.generic.hurt": "<PERSON><PERSON><PERSON><PERSON> terl<PERSON>", "subtitles.entity.generic.small_fall": "<PERSON><PERSON><PERSON><PERSON> tersa<PERSON>", "subtitles.entity.generic.splash": "Pertjikan", "subtitles.entity.generic.swim": "Berenang", "subtitles.entity.generic.wind_burst": "<PERSON><PERSON><PERSON> angin", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> mend<PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.ghastling.hurt": "G<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.add_item": "<PERSON><PERSON><PERSON> bingkai benda sinar", "subtitles.entity.glow_item_frame.break": "Bingkai benda sinar di<PERSON>", "subtitles.entity.glow_item_frame.place": "Bingkai benda sinar ditaroeh", "subtitles.entity.glow_item_frame.remove_item": "Mengosongkan bingkai benda sinar", "subtitles.entity.glow_item_frame.rotate_item": "<PERSON><PERSON>", "subtitles.entity.glow_squid.ambient": "Tjo<PERSON><PERSON>-tjoemi sinar berenang", "subtitles.entity.glow_squid.death": "Tjo<PERSON><PERSON>-tjoemi sinar mati", "subtitles.entity.glow_squid.hurt": "T<PERSON><PERSON><PERSON>-tjoemi sinar terloeka", "subtitles.entity.glow_squid.squirt": "Tjo<PERSON><PERSON>-tjoemi sinar menembak tinta", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> men<PERSON>mb<PERSON>", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> makan", "subtitles.entity.goat.horn_break": "<PERSON><PERSON><PERSON> kambing lepas", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> terl<PERSON>", "subtitles.entity.goat.long_jump": "Kambing melompat", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON> diperah", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.goat.step": "<PERSON><PERSON><PERSON> melang<PERSON>h", "subtitles.entity.guardian.ambient": "Pendjaga mengerang", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.guardian.flop": "Pendjaga melompat", "subtitles.entity.guardian.hurt": "Pendjaga terl<PERSON>", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON><PERSON> riang be<PERSON>", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON> riang mati", "subtitles.entity.happy_ghast.equip": "Roengkoep <PERSON>asang", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON> riang be<PERSON>p", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON><PERSON> riang be<PERSON>", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON> riang terl<PERSON>", "subtitles.entity.happy_ghast.unequip": "Roengkoep <PERSON>", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> men<PERSON>am marah", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON> mend<PERSON>lin", "subtitles.entity.hoglin.death": "<PERSON><PERSON>n mati", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> terl<PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> moendoer", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> me<PERSON>h", "subtitles.entity.horse.ambient": "<PERSON><PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON>", "subtitles.entity.horse.armor": "<PERSON><PERSON><PERSON> koeda dipasang", "subtitles.entity.horse.breathe": "<PERSON><PERSON> be<PERSON>", "subtitles.entity.horse.death": "<PERSON><PERSON> mati", "subtitles.entity.horse.eat": "<PERSON><PERSON> makan", "subtitles.entity.horse.gallop": "<PERSON>eda berlari kentjang", "subtitles.entity.horse.hurt": "<PERSON><PERSON>", "subtitles.entity.horse.jump": "Koeda melompat", "subtitles.entity.horse.saddle": "<PERSON><PERSON><PERSON>", "subtitles.entity.husk.ambient": "<PERSON>omb<PERSON> kering mengerang", "subtitles.entity.husk.converted_to_zombie": "<PERSON><PERSON><PERSON> kering be<PERSON><PERSON><PERSON> mendjadi zombi", "subtitles.entity.husk.death": "<PERSON><PERSON>i kering mati", "subtitles.entity.husk.hurt": "<PERSON><PERSON><PERSON> kering terloeka", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.illusioner.hurt": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.illusioner.mirror_move": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.prepare_blindness": "<PERSON><PERSON><PERSON><PERSON> men<PERSON> keb<PERSON>taän", "subtitles.entity.illusioner.prepare_mirror": "<PERSON><PERSON><PERSON><PERSON> bajangan", "subtitles.entity.iron_golem.attack": "<PERSON><PERSON> besi men<PERSON>ang", "subtitles.entity.iron_golem.damage": "<PERSON><PERSON> besi hant<PERSON>er", "subtitles.entity.iron_golem.death": "Go<PERSON> besi mati", "subtitles.entity.iron_golem.hurt": "Golem besi terloeka", "subtitles.entity.iron_golem.repair": "Golem besi <PERSON>", "subtitles.entity.item.break": "<PERSON><PERSON>", "subtitles.entity.item.pickup": "<PERSON><PERSON>", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON> bing<PERSON> benda", "subtitles.entity.item_frame.break": "Bingkai benda di<PERSON>", "subtitles.entity.item_frame.place": "Bing<PERSON> benda di<PERSON>h", "subtitles.entity.item_frame.remove_item": "Mengosongkan bingkai benda", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON>", "subtitles.entity.leash_knot.break": "<PERSON><PERSON><PERSON> tali <PERSON>", "subtitles.entity.leash_knot.place": "<PERSON><PERSON><PERSON> tali terikat", "subtitles.entity.lightning_bolt.impact": "<PERSON>baran petir", "subtitles.entity.lightning_bolt.thunder": "Gemoeroeh petir", "subtitles.entity.llama.ambient": "<PERSON>", "subtitles.entity.llama.angry": "Lama mengembik marah", "subtitles.entity.llama.chest": "<PERSON>i dipasa<PERSON>kan kelama", "subtitles.entity.llama.death": "<PERSON> mati", "subtitles.entity.llama.eat": "<PERSON>", "subtitles.entity.llama.hurt": "<PERSON>", "subtitles.entity.llama.spit": "<PERSON> me<PERSON>", "subtitles.entity.llama.step": "<PERSON>", "subtitles.entity.llama.swag": "<PERSON>", "subtitles.entity.magma_cube.death": "Dadoe magma mati", "subtitles.entity.magma_cube.hurt": "Dadoe magma terloeka", "subtitles.entity.magma_cube.squish": "Dadoe magma berdetjit", "subtitles.entity.minecart.inside": "Kereta tambang berdentang", "subtitles.entity.minecart.inside_underwater": "<PERSON><PERSON> tambang berdentang didalam a<PERSON>", "subtitles.entity.minecart.riding": "<PERSON><PERSON> tambang be<PERSON>", "subtitles.entity.mooshroom.convert": "Mooshroom beroebah", "subtitles.entity.mooshroom.eat": "Mooshroom makan", "subtitles.entity.mooshroom.milk": "Mooshroom diperah", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom diperah tjoeriga", "subtitles.entity.mule.ambient": "<PERSON><PERSON> meringih", "subtitles.entity.mule.angry": "Bagal mering<PERSON>k", "subtitles.entity.mule.chest": "<PERSON>i dipasangkan kebagal", "subtitles.entity.mule.death": "<PERSON>gal mati", "subtitles.entity.mule.eat": "<PERSON>gal makan", "subtitles.entity.mule.hurt": "<PERSON>gal terl<PERSON>", "subtitles.entity.mule.jump": "Bagal melompat", "subtitles.entity.painting.break": "Loekisan dihantjo<PERSON>kan", "subtitles.entity.painting.place": "Loekisan ditar<PERSON>h", "subtitles.entity.panda.aggressive_ambient": "<PERSON><PERSON> goesar", "subtitles.entity.panda.ambient": "Panda terengah-engah", "subtitles.entity.panda.bite": "<PERSON>da mengg<PERSON>t", "subtitles.entity.panda.cant_breed": "Panda <PERSON>", "subtitles.entity.panda.death": "Panda mati", "subtitles.entity.panda.eat": "Panda makan", "subtitles.entity.panda.hurt": "Panda terl<PERSON>ka", "subtitles.entity.panda.pre_sneeze": "Hidoeng panda gatal", "subtitles.entity.panda.sneeze": "Panda bersin", "subtitles.entity.panda.step": "Panda melang<PERSON>h", "subtitles.entity.panda.worried_ambient": "<PERSON>da mere<PERSON>", "subtitles.entity.parrot.ambient": "Bajan bertjak<PERSON>", "subtitles.entity.parrot.death": "Bajan mati", "subtitles.entity.parrot.eats": "Bajan makan", "subtitles.entity.parrot.fly": "Bajan mengepakkan", "subtitles.entity.parrot.hurts": "Bajan terloeka", "subtitles.entity.parrot.imitate.blaze": "Bajan meniroe kemamang", "subtitles.entity.parrot.imitate.bogged": "Bajan meniroe ben<PERSON> rawa", "subtitles.entity.parrot.imitate.breeze": "Bajan meniroe pawana", "subtitles.entity.parrot.imitate.creaking": "Bajan meniroe kerioet", "subtitles.entity.parrot.imitate.creeper": "Bajan meniroe creeper", "subtitles.entity.parrot.imitate.drowned": "Bajan meniroe zombi a<PERSON>r", "subtitles.entity.parrot.imitate.elder_guardian": "Bajan meniroe tetoea pendjaga", "subtitles.entity.parrot.imitate.ender_dragon": "Bajan men<PERSON><PERSON>", "subtitles.entity.parrot.imitate.endermite": "Bajan meniroe endermite", "subtitles.entity.parrot.imitate.evoker": "Bajan meniroe pawang", "subtitles.entity.parrot.imitate.ghast": "Bajan meniroe ghast", "subtitles.entity.parrot.imitate.guardian": "Bajan meniroe pendjaga", "subtitles.entity.parrot.imitate.hoglin": "Bajan meniroe hoglin", "subtitles.entity.parrot.imitate.husk": "Bajan meniroe zombi kering", "subtitles.entity.parrot.imitate.illusioner": "Bajan men<PERSON>e pen<PERSON>", "subtitles.entity.parrot.imitate.magma_cube": "Bajan meniroe dadoe magma", "subtitles.entity.parrot.imitate.phantom": "Bajan meniroe angan-angan", "subtitles.entity.parrot.imitate.piglin": "Bajan meniroe piglin", "subtitles.entity.parrot.imitate.piglin_brute": "Bajan meniroe piglin ganas", "subtitles.entity.parrot.imitate.pillager": "Bajan meniroe pen<PERSON>", "subtitles.entity.parrot.imitate.ravager": "Bajan meniroe pembinasa", "subtitles.entity.parrot.imitate.shulker": "Bajan meniroe shulker", "subtitles.entity.parrot.imitate.silverfish": "Bajan meniroe gegat", "subtitles.entity.parrot.imitate.skeleton": "Bajan men<PERSON><PERSON>", "subtitles.entity.parrot.imitate.slime": "Bajan meniroe geladir", "subtitles.entity.parrot.imitate.spider": "Bajan meniroe laba-laba", "subtitles.entity.parrot.imitate.stray": "Bajan meniroe ben<PERSON> sald<PERSON>e", "subtitles.entity.parrot.imitate.vex": "Bajan meniroe toejoel", "subtitles.entity.parrot.imitate.vindicator": "Bajan meniroe pembela", "subtitles.entity.parrot.imitate.warden": "Bajan meniroe penoenggoe", "subtitles.entity.parrot.imitate.witch": "Bajan meniroe pen<PERSON>", "subtitles.entity.parrot.imitate.wither": "Bajan men<PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "Bajan men<PERSON>e <PERSON>", "subtitles.entity.parrot.imitate.zoglin": "Bajan men<PERSON>e zoglin", "subtitles.entity.parrot.imitate.zombie": "Bajan meniroe zombi", "subtitles.entity.parrot.imitate.zombie_villager": "Bajan meniroe zombi pendo<PERSON>k", "subtitles.entity.phantom.ambient": "<PERSON><PERSON><PERSON><PERSON>gan meleng<PERSON>", "subtitles.entity.phantom.bite": "<PERSON><PERSON><PERSON><PERSON><PERSON> mengg<PERSON>t", "subtitles.entity.phantom.death": "<PERSON><PERSON>-<PERSON>gan mati", "subtitles.entity.phantom.flap": "Kelepak angan-angan", "subtitles.entity.phantom.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> terl<PERSON>", "subtitles.entity.phantom.swoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pig.ambient": "<PERSON><PERSON>", "subtitles.entity.pig.death": "<PERSON><PERSON> mati", "subtitles.entity.pig.hurt": "<PERSON><PERSON>", "subtitles.entity.pig.saddle": "<PERSON><PERSON><PERSON>", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> mengago<PERSON>i benda", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> mendengoes", "subtitles.entity.piglin.angry": "<PERSON><PERSON> mendengoes marah", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON>", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> bero<PERSON>ah mend<PERSON>di zombi piglin", "subtitles.entity.piglin.death": "<PERSON><PERSON> mati", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> terl<PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> mendengoes iri", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> moendoer", "subtitles.entity.piglin.step": "<PERSON><PERSON> me<PERSON>h", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON> ganas mendengoes", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON> ganas mendengoes marah", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON>lin ganas be<PERSON><PERSON><PERSON> mend<PERSON>di zombi piglin", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> ganas mati", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> ganas terl<PERSON>", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON> ganas melang<PERSON>h", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.player.attack.crit": "Serangan kritis", "subtitles.entity.player.attack.knockback": "Serangan tolak moendoer", "subtitles.entity.player.attack.strong": "Serangan koeat", "subtitles.entity.player.attack.sweep": "Seranga<PERSON> men<PERSON>", "subtitles.entity.player.attack.weak": "Serangan lemah", "subtitles.entity.player.burp": "Bersendawa", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.teleport": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON> koe<PERSON> men<PERSON>ang", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON> be<PERSON>g koe<PERSON> be<PERSON>", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> mati", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> terl<PERSON>", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.potion.splash": "Botol dibanting", "subtitles.entity.potion.throw": "Bo<PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_out": "<PERSON><PERSON> b<PERSON> men<PERSON>", "subtitles.entity.puffer_fish.blow_up": "<PERSON><PERSON> boe<PERSON> mengg<PERSON>", "subtitles.entity.puffer_fish.death": "<PERSON><PERSON> boe<PERSON> mati", "subtitles.entity.puffer_fish.flop": "<PERSON><PERSON> boental melompat", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON> boe<PERSON> terl<PERSON>", "subtitles.entity.puffer_fish.sting": "<PERSON><PERSON> b<PERSON>", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.jump": "Kelintji melo<PERSON>", "subtitles.entity.ravager.ambient": "Pem<PERSON><PERSON> menden<PERSON>", "subtitles.entity.ravager.attack": "<PERSON><PERSON><PERSON><PERSON> mengg<PERSON>", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.ravager.hurt": "Pem<PERSON><PERSON> terl<PERSON>", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.step": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>h", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "subtitles.entity.salmon.death": "Salem mati", "subtitles.entity.salmon.flop": "Salem melompat", "subtitles.entity.salmon.hurt": "Salem terloeka", "subtitles.entity.sheep.ambient": "Dom<PERSON> mengembik", "subtitles.entity.sheep.death": "Dom<PERSON> mati", "subtitles.entity.sheep.hurt": "<PERSON><PERSON> terloeka", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> membo<PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON><PERSON> shulker meledak", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.silverfish.ambient": "Gegat mendesis", "subtitles.entity.silverfish.death": "Gegat mati", "subtitles.entity.silverfish.hurt": "Gegat terloeka", "subtitles.entity.skeleton.ambient": "Bengkarak menggemeretoek", "subtitles.entity.skeleton.converted_to_stray": "Bengkarak beroebah mendjadi bengkarak saldjoe", "subtitles.entity.skeleton.death": "Bengkarak mati", "subtitles.entity.skeleton.hurt": "Bengkarak terloeka", "subtitles.entity.skeleton.shoot": "Bengkarak menembak", "subtitles.entity.skeleton_horse.ambient": "<PERSON>eda bengkarak meringkik", "subtitles.entity.skeleton_horse.death": "<PERSON><PERSON> beng<PERSON>k mati", "subtitles.entity.skeleton_horse.hurt": "<PERSON><PERSON> bengkarak terloeka", "subtitles.entity.skeleton_horse.jump_water": "Koeda bengkarak melompat", "subtitles.entity.skeleton_horse.swim": "<PERSON><PERSON> beng<PERSON>k berenang", "subtitles.entity.slime.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.slime.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.slime.squish": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.death": "Pengen<PERSON><PERSON> mati", "subtitles.entity.sniffer.digging": "<PERSON><PERSON><PERSON><PERSON> men<PERSON>ali", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.sniffer.drop_seed": "<PERSON><PERSON><PERSON><PERSON> mendja<PERSON> benih", "subtitles.entity.sniffer.eat": "Pengen<PERSON><PERSON> makan", "subtitles.entity.sniffer.egg_crack": "Teloer pengendoes retak", "subtitles.entity.sniffer.egg_hatch": "<PERSON><PERSON>r pengendoes menetas", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON><PERSON> senang", "subtitles.entity.sniffer.hurt": "Pen<PERSON><PERSON><PERSON> terluka", "subtitles.entity.sniffer.idle": "Pengen<PERSON>es mendengoes", "subtitles.entity.sniffer.scenting": "Pen<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON><PERSON> mengendoes", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>h", "subtitles.entity.snow_golem.death": "<PERSON><PERSON> saldjoe mati", "subtitles.entity.snow_golem.hurt": "Go<PERSON> sald<PERSON> terl<PERSON>", "subtitles.entity.snowball.throw": "<PERSON><PERSON>", "subtitles.entity.spider.ambient": "Laba-laba mendesis", "subtitles.entity.spider.death": "Laba-laba mati", "subtitles.entity.spider.hurt": "Laba-laba terl<PERSON>", "subtitles.entity.squid.ambient": "<PERSON><PERSON><PERSON><PERSON>-tjo<PERSON>i be<PERSON>ng", "subtitles.entity.squid.death": "T<PERSON><PERSON><PERSON>-tjoemi mati", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON><PERSON>-tjo<PERSON>i terl<PERSON>", "subtitles.entity.squid.squirt": "Tjo<PERSON><PERSON>-tjoemi men<PERSON>k tinta", "subtitles.entity.stray.ambient": "Bengkarak sald<PERSON><PERSON>", "subtitles.entity.stray.death": "Bengkarak saldjoe mati", "subtitles.entity.stray.hurt": "Bengkarak saldjoe terloeka", "subtitles.entity.strider.death": "Pengaroeng mati", "subtitles.entity.strider.eat": "Pengaroeng makan", "subtitles.entity.strider.happy": "Pengaroeng berkitjau", "subtitles.entity.strider.hurt": "Pengaroeng terloeka", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.retreat": "<PERSON><PERSON><PERSON><PERSON> moendoer", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.tadpole.flop": "Beroedoe melompat", "subtitles.entity.tadpole.grow_up": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.hurt": "Be<PERSON>ed<PERSON> terl<PERSON>", "subtitles.entity.tnt.primed": "TNT mendesis", "subtitles.entity.tropical_fish.death": "<PERSON><PERSON> tropis mati", "subtitles.entity.tropical_fish.flop": "Ikan tropis melompat", "subtitles.entity.tropical_fish.hurt": "<PERSON><PERSON> tropis terl<PERSON>", "subtitles.entity.turtle.ambient_land": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.turtle.death_baby": "<PERSON><PERSON> penjoe mati", "subtitles.entity.turtle.egg_break": "<PERSON><PERSON><PERSON> penjoe <PERSON>", "subtitles.entity.turtle.egg_crack": "<PERSON><PERSON><PERSON> penjoe retak", "subtitles.entity.turtle.egg_hatch": "<PERSON><PERSON><PERSON> penjoe men<PERSON>s", "subtitles.entity.turtle.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.turtle.hurt_baby": "<PERSON><PERSON> te<PERSON>", "subtitles.entity.turtle.lay_egg": "<PERSON><PERSON><PERSON>", "subtitles.entity.turtle.shamble": "<PERSON><PERSON><PERSON>", "subtitles.entity.turtle.shamble_baby": "<PERSON><PERSON>", "subtitles.entity.turtle.swim": "<PERSON><PERSON><PERSON>", "subtitles.entity.vex.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vex.charge": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vex.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vex.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.ambient": "Pendoedoek bergoemam", "subtitles.entity.villager.celebrate": "Pendoedoek bersorak", "subtitles.entity.villager.death": "Pendoedoek tewas", "subtitles.entity.villager.hurt": "Pendoedoek terloeka", "subtitles.entity.villager.no": "Pendoedoek tidak setoe<PERSON>e", "subtitles.entity.villager.trade": "Pendoedoek berdagang", "subtitles.entity.villager.work_armorer": "<PERSON><PERSON>ng zirah be<PERSON>", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON><PERSON> dja<PERSON> be<PERSON>", "subtitles.entity.villager.work_cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_cleric": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.villager.work_farmer": "<PERSON><PERSON>", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_fletcher": "Toekang panah bekerdja", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON> koelit be<PERSON>", "subtitles.entity.villager.work_librarian": "Poestakawan be<PERSON>d<PERSON>", "subtitles.entity.villager.work_mason": "Toekang batoe bekerdja", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_toolsmith": "Toekang alat bekerdja", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.villager.yes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.death": "<PERSON><PERSON><PERSON><PERSON> mati", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON><PERSON><PERSON> terl<PERSON>", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON> keli<PERSON> be<PERSON>", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON> keliling mati", "subtitles.entity.wandering_trader.disappeared": "<PERSON><PERSON> keli<PERSON> men<PERSON>", "subtitles.entity.wandering_trader.drink_milk": "<PERSON><PERSON> keliling minoem soesoe", "subtitles.entity.wandering_trader.drink_potion": "<PERSON><PERSON> keliling minoem ramoean", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON> keli<PERSON> terl<PERSON>", "subtitles.entity.wandering_trader.no": "<PERSON><PERSON> keli<PERSON> tidak set<PERSON>", "subtitles.entity.wandering_trader.reappeared": "<PERSON><PERSON> k<PERSON> moent<PERSON>el", "subtitles.entity.wandering_trader.trade": "<PERSON><PERSON> keli<PERSON> berdagang", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON> k<PERSON> set<PERSON>", "subtitles.entity.warden.agitated": "Penoenggoe mengerang marah", "subtitles.entity.warden.ambient": "Penoenggoe berden<PERSON>king", "subtitles.entity.warden.angry": "Penoenggoe mengam<PERSON>k", "subtitles.entity.warden.attack_impact": "Penoenggoe <PERSON>", "subtitles.entity.warden.death": "Penoenggoe mati", "subtitles.entity.warden.dig": "<PERSON><PERSON>ng<PERSON><PERSON> men<PERSON>ali", "subtitles.entity.warden.emerge": "Penoenggoe bangkit", "subtitles.entity.warden.heartbeat": "<PERSON><PERSON><PERSON><PERSON> penoenggoe berdetak", "subtitles.entity.warden.hurt": "Penoenggoe terloeka", "subtitles.entity.warden.listening": "Pen<PERSON>ng<PERSON><PERSON>", "subtitles.entity.warden.listening_angry": "<PERSON><PERSON>ng<PERSON><PERSON> menjadari dengan amarah", "subtitles.entity.warden.nearby_close": "Penoenggoe mend<PERSON>", "subtitles.entity.warden.nearby_closer": "<PERSON><PERSON><PERSON><PERSON><PERSON> madjoe", "subtitles.entity.warden.nearby_closest": "Penoenggoe mendekat", "subtitles.entity.warden.roar": "Penoenggoe meraoeng", "subtitles.entity.warden.sniff": "Penoenggoe mengendo<PERSON>", "subtitles.entity.warden.sonic_boom": "Penoenggoe men<PERSON>k", "subtitles.entity.warden.sonic_charge": "Penoenggoe men<PERSON> serangan", "subtitles.entity.warden.step": "Penoenggoe melang<PERSON>h", "subtitles.entity.warden.tendril_clicks": "<PERSON><PERSON>er penoenggo<PERSON> berde<PERSON>", "subtitles.entity.wind_charge.throw": "<PERSON><PERSON> angin <PERSON>", "subtitles.entity.wind_charge.wind_burst": "<PERSON><PERSON> angin meledak", "subtitles.entity.witch.ambient": "Penjihir t<PERSON>", "subtitles.entity.witch.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON> minoem", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON> me<PERSON>", "subtitles.entity.wither.ambient": "<PERSON><PERSON> moe<PERSON>", "subtitles.entity.wither.death": "Wither mati", "subtitles.entity.wither.hurt": "<PERSON><PERSON>", "subtitles.entity.wither.shoot": "<PERSON><PERSON>", "subtitles.entity.wither.spawn": "<PERSON>er te<PERSON>", "subtitles.entity.wither_skeleton.ambient": "Bengkarak Wither <PERSON>ek", "subtitles.entity.wither_skeleton.death": "Bengkarak Wither mati", "subtitles.entity.wither_skeleton.hurt": "Bengkarak Wither terloeka", "subtitles.entity.wolf.ambient": "Ser<PERSON>la terengah-engah", "subtitles.entity.wolf.bark": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.wolf.growl": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.hurt": "Ser<PERSON><PERSON> terl<PERSON>", "subtitles.entity.wolf.pant": "Ser<PERSON>la terengah-engah", "subtitles.entity.wolf.shake": "<PERSON><PERSON><PERSON> mengg<PERSON> badan", "subtitles.entity.wolf.whine": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> men<PERSON>am", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> men<PERSON>am marah", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> mati", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> terl<PERSON>", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> me<PERSON>", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON> men<PERSON>", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.converted_to_drowned": "<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON> mend<PERSON> zomb<PERSON> a<PERSON>", "subtitles.entity.zombie.death": "<PERSON><PERSON>i mati", "subtitles.entity.zombie.destroy_egg": "<PERSON><PERSON> pen<PERSON>", "subtitles.entity.zombie.hurt": "<PERSON>omb<PERSON> terl<PERSON>", "subtitles.entity.zombie.infect": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON> zombi mering<PERSON>k", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON> zombi mati", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON> zombi terloeka", "subtitles.entity.zombie_villager.ambient": "Zombi pendoedoek mengerang", "subtitles.entity.zombie_villager.converted": "<PERSON>ombi pendoed<PERSON>k melengking", "subtitles.entity.zombie_villager.cure": "Zombi pendoed<PERSON><PERSON>", "subtitles.entity.zombie_villager.death": "<PERSON>omb<PERSON> pendoed<PERSON>k tewas", "subtitles.entity.zombie_villager.hurt": "Zombi pendoedoek terloeka", "subtitles.entity.zombified_piglin.ambient": "Zombi piglin mendengoes", "subtitles.entity.zombified_piglin.angry": "Zombi piglin mendengoes marah", "subtitles.entity.zombified_piglin.death": "<PERSON>ombi piglin mati", "subtitles.entity.zombified_piglin.hurt": "<PERSON>ombi piglin terloeka", "subtitles.event.mob_effect.bad_omen": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.event.mob_effect.raid_omen": "<PERSON>oe<PERSON> mendekat", "subtitles.event.mob_effect.trial_omen": "<PERSON><PERSON><PERSON><PERSON><PERSON> seram mendekat", "subtitles.event.raid.horn": "<PERSON><PERSON><PERSON> seram", "subtitles.item.armor.equip": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_chain": "<PERSON><PERSON><PERSON> rantai berge<PERSON>g", "subtitles.item.armor.equip_diamond": "<PERSON><PERSON><PERSON> berl<PERSON> be<PERSON>", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON>", "subtitles.item.armor.equip_gold": "<PERSON><PERSON><PERSON> emas berdenting", "subtitles.item.armor.equip_iron": "<PERSON><PERSON><PERSON> besi berdentang", "subtitles.item.armor.equip_leather": "<PERSON><PERSON><PERSON> kuli<PERSON> be<PERSON>", "subtitles.item.armor.equip_netherite": "<PERSON>irah netherit berdentang", "subtitles.item.armor.equip_turtle": "<PERSON><PERSON><PERSON> tjangkang penjoe", "subtitles.item.armor.equip_wolf": "<PERSON><PERSON>h se<PERSON>ala <PERSON>", "subtitles.item.armor.unequip_wolf": "<PERSON><PERSON><PERSON> se<PERSON>ala <PERSON>", "subtitles.item.axe.scrape": "Kapak men<PERSON>", "subtitles.item.axe.strip": "Kapak mengoepas", "subtitles.item.axe.wax_off": "<PERSON><PERSON>", "subtitles.item.bone_meal.use": "<PERSON><PERSON><PERSON>n tepoeng toelang", "subtitles.item.book.page_turn": "<PERSON><PERSON><PERSON>", "subtitles.item.book.put": "<PERSON><PERSON><PERSON>", "subtitles.item.bottle.empty": "Mengosongkan botol", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON> botol", "subtitles.item.brush.brushing.generic": "Menjikat", "subtitles.item.brush.brushing.gravel": "Menjikat kerikil", "subtitles.item.brush.brushing.gravel.complete": "Menjikat kerikil se<PERSON>ai", "subtitles.item.brush.brushing.sand": "<PERSON><PERSON><PERSON> pasir", "subtitles.item.brush.brushing.sand.complete": "<PERSON><PERSON><PERSON> pasir se<PERSON>ai", "subtitles.item.bucket.empty": "Mengosongkan ember", "subtitles.item.bucket.fill": "<PERSON><PERSON><PERSON> ember", "subtitles.item.bucket.fill_axolotl": "Axolotl diambil", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON>", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bundle.drop_contents": "Mengosongkan kantong", "subtitles.item.bundle.insert": "<PERSON><PERSON> di<PERSON>", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON><PERSON>", "subtitles.item.bundle.remove_one": "<PERSON><PERSON>", "subtitles.item.chorus_fruit.teleport": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.crop.plant": "<PERSON><PERSON> ditanam", "subtitles.item.crossbow.charge": "Boesoer silang memoeat", "subtitles.item.crossbow.hit": "<PERSON><PERSON>", "subtitles.item.crossbow.load": "Boesoer silang termoeat", "subtitles.item.crossbow.shoot": "Boesoer si<PERSON>k", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.elytra.flying": "<PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "<PERSON><PERSON><PERSON> bola api", "subtitles.item.flintandsteel.use": "Pemantik api berdetik", "subtitles.item.glow_ink_sac.use": "Kantong tinta sinar diloemoeri", "subtitles.item.goat_horn.play": "<PERSON><PERSON><PERSON> kamb<PERSON>", "subtitles.item.hoe.till": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.honey_bottle.drink": "<PERSON><PERSON><PERSON>", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON>", "subtitles.item.horse_armor.unequip": "<PERSON><PERSON><PERSON> koeda di<PERSON>", "subtitles.item.ink_sac.use": "Kantong tinta di<PERSON>", "subtitles.item.lead.break": "<PERSON><PERSON> pengi<PERSON>", "subtitles.item.lead.tied": "Tali pengikat mengikat", "subtitles.item.lead.untied": "<PERSON><PERSON> pengikat di<PERSON>", "subtitles.item.llama_carpet.unequip": "<PERSON><PERSON>", "subtitles.item.lodestone_compass.lock": "Kompas batoe magnet terkoentji pada batoe magnet", "subtitles.item.mace.smash_air": "<PERSON><PERSON>", "subtitles.item.mace.smash_ground": "<PERSON><PERSON>", "subtitles.item.nether_wart.plant": "<PERSON><PERSON> ditanam", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON>", "subtitles.item.saddle.unequip": "<PERSON><PERSON><PERSON>", "subtitles.item.shears.shear": "Goenting memotong", "subtitles.item.shears.snip": "Gunting menggoenting", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON>", "subtitles.item.shovel.flatten": "Sekop meratakan", "subtitles.item.spyglass.stop_using": "Teropong menjo<PERSON>t", "subtitles.item.spyglass.use": "Teropong membesar", "subtitles.item.totem.use": "<PERSON><PERSON><PERSON>", "subtitles.item.trident.hit": "<PERSON><PERSON><PERSON>", "subtitles.item.trident.hit_ground": "<PERSON><PERSON><PERSON> berge<PERSON>", "subtitles.item.trident.return": "<PERSON><PERSON><PERSON>", "subtitles.item.trident.riptide": "<PERSON><PERSON><PERSON>", "subtitles.item.trident.throw": "<PERSON><PERSON><PERSON>", "subtitles.item.trident.thunder": "<PERSON><PERSON><PERSON> memanggil petir", "subtitles.item.wolf_armor.break": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.item.wolf_armor.crack": "<PERSON><PERSON><PERSON> serigala retak", "subtitles.item.wolf_armor.damage": "<PERSON><PERSON><PERSON> se<PERSON>ala <PERSON> se<PERSON>an", "subtitles.item.wolf_armor.repair": "<PERSON><PERSON><PERSON>", "subtitles.particle.soul_escape": "Djiwa terlepas", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON> digambar", "subtitles.ui.hud.bubble_pop": "Napas berk<PERSON>", "subtitles.ui.loom.take_result": "Alat tenoen dipakai", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON><PERSON> batoe dipakai", "subtitles.weather.rain": "<PERSON><PERSON><PERSON>", "symlink_warning.message": "Memoeat doenia dari map dengan taoetan simbolis bisa djadi tidak aman bila toean tidak tahoe persis apa jang toean lakoekan. Mohon koendjoengi %s oentoek mempeladjari lebih landjoet.", "symlink_warning.message.pack": "Memoeat paket dengan taoetan simbolis bisa djadi tidak aman bila toean tidak tahoe persis apa jang toean lakoekan. Mohon koendjoengi %s oentoek mempeladjari lebih landjoet.", "symlink_warning.message.world": "Memoeat doenia dari map dengan taoetan simbolis bisa djadi tidak aman bila toean tidak tahoe persis apa jang toean lakoekan. Mohon koendjoengi %s oentoek mempeladjari lebih landjoet.", "symlink_warning.more_info": "Informasi lebih landjoet", "symlink_warning.title": "Map doenia mengandoeng taoetan simbolis", "symlink_warning.title.pack": "Paket jang ditambahkan mengandoeng taoetan simbolis", "symlink_warning.title.world": "Map doenia mengandoeng taoetan simbolis", "team.collision.always": "Selaloe", "team.collision.never": "Ta' pernah", "team.collision.pushOtherTeams": "<PERSON><PERSON> regoe la<PERSON>n", "team.collision.pushOwnTeam": "Dorong regoe sendiri", "team.notFound": "Regoe „%s” tidak dikenal", "team.visibility.always": "Selaloe", "team.visibility.hideForOtherTeams": "Semboenjikan dari regoe la<PERSON>n", "team.visibility.hideForOwnTeam": "Semboenjikan dari regoe sendiri", "team.visibility.never": "Ta' pernah", "telemetry.event.advancement_made.description": "Memahami konteks dibalik menerima kemadjoean bisa membantoe kami lebih memahami dan meningkatkan progresi permaïnan.", "telemetry.event.advancement_made.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.event.game_load_times.description": "Peristiwa ini bisa membantoe kami mengetahoei dimana peningkatan kinerdja penjalaän diperloekan dengan mengoekoer waktoe pendjalanan dari fase penjalaän.", "telemetry.event.game_load_times.title": "Tempo Memoeat <PERSON>", "telemetry.event.optional": "%s (opsionil)", "telemetry.event.optional.disabled": "%s (opsionil) - mati", "telemetry.event.performance_metrics.description": "Mengetahoei ichtisar kinerdja Minecraft setjara keseloeroehan membantoe kami dalam menjetem dan mengoptimalisir permaïnan oentoek berbagai spesifikasi mesin dan sistem operasi. \nVersi permaïnan disertakan oentoek membantoe kami dalam membandingkan ichtisar kinerdja oentoek versi baroe Minecraft.", "telemetry.event.performance_metrics.title": "Prestasi Kinerdja", "telemetry.event.required": "%s (diperloekan)", "telemetry.event.world_load_times.description": "Penting bagi kami oentoek memahami beberapa lama tempo jang diperloekan oentoek bergaboeng kedalam doenia, dan bagaimana hal itoe beroebah dari waktoe kewaktoe. <PERSON><PERSON><PERSON>, sa'at kami menambahkan tjiri baroe atau melakoekan peroebahan teknis jang lebih besar, kami perloe melihat pengaroehnja terhadap tempo memoeat.", "telemetry.event.world_load_times.title": "Tempo Memoeat Doenia", "telemetry.event.world_loaded.description": "Mengetahoei tjara pemaïn bermaïn Minecraft (seperti ragam permaïnan, kliën atau peladen modifikasi dan versi permaïnan) memoengkinkan kami oentoek memoesatkan perhatian kami pada pembaharoean oentoek meningkatkan areal jang paling diperhatikan pemaïn.\nPeristiwa doenia dimoeat dipasangkan dengan peristiwa doenia ditoetoep oentoek menghitoeng berapa lama sesi bermaïn soedah berlang<PERSON>eng.", "telemetry.event.world_loaded.title": "Doenia dimoeat", "telemetry.event.world_unloaded.description": "Peristiwa ini dipasasngkan dengan peristiwa doenia dimoeat oentoek menghitoeng berapa lama sesi doenia soedah berl<PERSON>.\nLama (dalam detik dan tik) dioekoer ketika sesi doenia telah berachir (kembal<PERSON>, memoetoeskan samboengan dari peladen).", "telemetry.event.world_unloaded.title": "Doenia Ditoetoep", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON> (Tik)", "telemetry.property.advancement_id.title": "<PERSON><PERSON><PERSON>", "telemetry.property.client_id.title": "Pengenal K<PERSON>", "telemetry.property.client_modded.title": "Kliën Dimodifikasi", "telemetry.property.dedicated_memory_kb.title": "<PERSON><PERSON> (kB)", "telemetry.property.event_timestamp_utc.title": "Stempel Waktoe <PERSON>iwa (UTC)", "telemetry.property.frame_rate_samples.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (BPD)", "telemetry.property.game_mode.title": "<PERSON><PERSON>", "telemetry.property.game_version.title": "<PERSON><PERSON><PERSON>", "telemetry.property.launcher_name.title": "<PERSON><PERSON>", "telemetry.property.load_time_bootstrap_ms.title": "Tempo Bootstrap (Milidetik)", "telemetry.property.load_time_loading_overlay_ms.title": "Tempo Memoeat <PERSON> (Milidetik)", "telemetry.property.load_time_pre_window_ms.title": "Tempo Sebeloem Djendela Terboeka (Milidetik)", "telemetry.property.load_time_total_time_ms.title": "Total Tempo Memoeat (Milidetik)", "telemetry.property.minecraft_session_id.title": "Pengenal Sesi Minecraft", "telemetry.property.new_world.title": "<PERSON><PERSON>", "telemetry.property.number_of_samples.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.operating_system.title": "Sistem Operasi", "telemetry.property.opt_in.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.property.platform.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.realms_map_content.title": "Moeatan Peta Realms (Nama <PERSON>)", "telemetry.property.render_distance.title": "Djarak <PERSON>dan<PERSON>", "telemetry.property.render_time_samples.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ka-tjitra", "telemetry.property.seconds_since_load.title": "Tempo Sedjak Pemoeatan (Detik)", "telemetry.property.server_modded.title": "Peladen Dimodifikasi", "telemetry.property.server_type.title": "<PERSON><PERSON><PERSON>", "telemetry.property.ticks_since_load.title": "Tempo Sedjak Pemoeatan (Tik)", "telemetry.property.used_memory_samples.title": "<PERSON><PERSON>", "telemetry.property.user_id.title": "<PERSON><PERSON><PERSON>", "telemetry.property.world_load_time_ms.title": "Tempo Pemoeatan Doenia (Milidetik)", "telemetry.property.world_session_id.title": "Pengenal Sesi Doenia", "telemetry_info.button.give_feedback": "<PERSON><PERSON>b<PERSON><PERSON>", "telemetry_info.button.privacy_statement": "<PERSON><PERSON><PERSON><PERSON> privasi", "telemetry_info.button.show_data": "Lihat data saja", "telemetry_info.opt_in.description": "<PERSON><PERSON> setoe<PERSON>e oentoek mengirimkan data opsionil", "telemetry_info.property_title": "Data jang Disertakan", "telemetry_info.screen.description": "Mengoempoelkan data ini membantoe kami meningkatkan Minecraft dengan memandoe kami kearah jang relevan dengan pemaïn kami.\nToean djoga bisa mengirimkan oempan-balik tambahan oentoek membantoe kami terus meningkatkan kwalitet Minecraft.", "telemetry_info.screen.title": "Pengoempoelan Data", "test.error.block_property_mismatch": "Sifat %s diharapkan %s, didapati %s", "test.error.block_property_missing": "Sifat balok hilang, sifat %s diharapkan %s", "test.error.entity_property": "Entitet %s gagal oedji: %s", "test.error.entity_property_details": "Entitet %s gagal oedji: %s, diharapkan: %s, did<PERSON><PERSON>: %s", "test.error.expected_block": "Balok %s diharapkan, didapati %s", "test.error.expected_block_tag": "Balok dalam #%s diharap<PERSON>, didapati %s", "test.error.expected_container_contents": "<PERSON><PERSON><PERSON> mengan<PERSON>: %s", "test.error.expected_container_contents_single": "<PERSON><PERSON><PERSON> mengan<PERSON> satoe: %s", "test.error.expected_empty_container": "<PERSON><PERSON><PERSON> kosong", "test.error.expected_entity": "%s diharapkan", "test.error.expected_entity_around": "%s diharapkan ada disekitar %s, %s, %s", "test.error.expected_entity_count": "%s entitet berdjenis %s diharapkan, ditemoekan %s", "test.error.expected_entity_data": "Data entitet diharapkan beroepa: %s, didapati: %s", "test.error.expected_entity_data_predicate": "Data entitet tidak sama oentoek %s", "test.error.expected_entity_effect": "%s diharapkan poenja efek %s %s", "test.error.expected_entity_having": "Persediaän entitet seharoesnja mengandoeng %s", "test.error.expected_entity_holding": "Entitet seharoesnja memegang %s", "test.error.expected_entity_in_test": "%s diharapkan ada pada oedji", "test.error.expected_entity_not_touching": "%s tidak diharapkan menjentoeh %s, %s, %s (relatif: %s, %s, %s)", "test.error.expected_entity_touching": "%s diharapkan menjentoeh %s, %s, %s (relatif: %s, %s, %s)", "test.error.expected_item": "Benda <PERSON> %s diharap<PERSON>", "test.error.expected_items_count": "%s benda be<PERSON> %s diharapkan, ditemoekan %s", "test.error.fail": "Persjaratan kegagalan dipenoehi", "test.error.invalid_block_type": "<PERSON><PERSON><PERSON> balok jang ta' diharapkan ditemoekan: %s", "test.error.missing_block_entity": "Entitet balok hilang", "test.error.position": "%s pada %s, %s, %s (relatif: %s, %s, %s) pada tik %s", "test.error.sequence.condition_already_triggered": "Persjaratan soedah dipitjoe pada %s", "test.error.sequence.condition_not_triggered": "Persjaratan tidak dipitjoe", "test.error.sequence.invalid_tick": "<PERSON><PERSON><PERSON><PERSON> dalam tik tidak sah: %s diharapkan", "test.error.sequence.not_completed": "Tempo oedji habis sebeloem oeroetan selesai", "test.error.set_biome": "Bioom oentoek oedji gagal disetel", "test.error.spawn_failure": "Entitet %s gagal diboeat", "test.error.state_not_equal": "Keadaän tidak benar. %s diharap<PERSON>, didapati %s", "test.error.structure.failure": "Bangoenan oedji oentoek %s gagal ditempatkan", "test.error.tick": "%s pada tik %s", "test.error.ticking_without_structure": "<PERSON><PERSON><PERSON> berdetik sebeloem menempatkan bangoenan", "test.error.timeout.no_result": "Tidak berhasil atau gagal dalam %s tik", "test.error.timeout.no_sequences_finished": "Tidak ada oeroetan jang selesai dalam %s tik", "test.error.too_many_entities": "<PERSON>ja satoe %s diharapkan ada disekitar %s, %s, %s, tetapi ditemoekan %s", "test.error.unexpected_block": "Balok tidak diharapkan beroepa %s", "test.error.unexpected_entity": "%s tidak diharapkan ada", "test.error.unexpected_item": "Benda be<PERSON> %s tidak diharapkan", "test.error.unknown": "<PERSON><PERSON>ahan intern ta' di<PERSON><PERSON><PERSON><PERSON>: %s", "test.error.value_not_equal": "%s diharapkan beroepa %s, didapati %s", "test.error.wrong_block_entity": "<PERSON><PERSON><PERSON> entitet balok salah: %s", "test_block.error.missing": "Balok %s hilang pada bang<PERSON>nan oedji", "test_block.error.too_many": "Terlaloe banjak balok %s", "test_block.invalid_timeout": "Tempo toenggoe (%s) tidak sah - djoemlah tik haroes positif", "test_block.message": "Pesan:", "test_block.mode.accept": "Terima", "test_block.mode.fail": "Gaga<PERSON>", "test_block.mode.log": "<PERSON><PERSON><PERSON>", "test_block.mode.start": "<PERSON><PERSON>", "test_block.mode_info.accept": "<PERSON>gam terima - <PERSON><PERSON><PERSON> k<PERSON> (sebagian dari) oedji", "test_block.mode_info.fail": "<PERSON><PERSON> gagal - <PERSON><PERSON><PERSON><PERSON><PERSON>ji", "test_block.mode_info.log": "<PERSON><PERSON> tjatat - mentja<PERSON> pesan", "test_block.mode_info.start": "<PERSON><PERSON> moelai - titik awal oedji", "test_instance.action.reset": "<PERSON><PERSON> oelang dan moeat", "test_instance.action.run": "<PERSON><PERSON> dan d<PERSON>", "test_instance.action.save": "<PERSON><PERSON><PERSON>", "test_instance.description.batch": "Toempak: %s", "test_instance.description.failed": "Gagal: %s", "test_instance.description.function": "Foengsi: %s", "test_instance.description.invalid_id": "Pengenal oedji tidak sah", "test_instance.description.no_test": "<PERSON><PERSON><PERSON> itoe tidak ada", "test_instance.description.structure": "Bangoenan: %s", "test_instance.description.type": "Djenis: %s", "test_instance.type.block_based": "Oedji berbasis balok", "test_instance.type.function": "<PERSON><PERSON><PERSON> b<PERSON>", "test_instance_block.entities": "Entitet:", "test_instance_block.error.no_test": "Peristiwa oedji pada %s, %s, %s tidak bisa didjalankan sebab ada oedji jang tidak ditentoekan", "test_instance_block.error.no_test_structure": "Peristiwa oedji pada %s, %s, %s tidak bisa didjalankan sebab tidak ada bang<PERSON>nan oedji", "test_instance_block.error.unable_to_save": "Sablon bangoenan oedji oentoek peristiwa oedji pada %s, %s, %s tidak bisa disimpan", "test_instance_block.invalid": "[ta' sah]", "test_instance_block.reset_success": "<PERSON><PERSON><PERSON><PERSON> men<PERSON><PERSON> oelang oedji: %s", "test_instance_block.rotation": "Rotasi:", "test_instance_block.size": "Oekoeran <PERSON> o<PERSON>", "test_instance_block.starting": "Memoelai oedji %s", "test_instance_block.test_id": "Pengenal peristiwa o<PERSON>ji", "title.32bit.deprecation": "Sistem 32-bit terdeteksi: ini bisa mentjegah toean bermaïn dimasa mendatang karena sistem 64-bit akan diperloekan!", "title.32bit.deprecation.realms": "Minecraft akan segera mewadjibkan sistem 64-bit, jang akan mentjegah toean bermaïn atau memakai Realms pada perangkat ini. <PERSON><PERSON> haroes membatalkan semoea abonemen Realms dengan tangan.", "title.32bit.deprecation.realms.check": "Djangan tampilkan lajar ini lagi", "title.32bit.deprecation.realms.header": "Sistem 32-bit terdeteksi", "title.credits": "Hak tjipta Mojang AB. Djangan disebarkan!", "title.multiplayer.disabled": "<PERSON><PERSON><PERSON><PERSON> bersama dimatikan. Mohon periksa penjetelan akoen Microsoft toean.", "title.multiplayer.disabled.banned.name": "<PERSON><PERSON> haroes mengo<PERSON>ah nama toean sebeloem toean bisa bermaïn dalam djar<PERSON>n", "title.multiplayer.disabled.banned.permanent": "<PERSON><PERSON><PERSON> toean ditangg<PERSON>kan permanen dari permaïnan dalam djaringan", "title.multiplayer.disabled.banned.temporary": "<PERSON><PERSON><PERSON> toean ditangg<PERSON>hkan sementara dari permaïnan dalam djaringan", "title.multiplayer.lan": "<PERSON><PERSON><PERSON><PERSON> (Dj.A.Lok.)", "title.multiplayer.other": "<PERSON><PERSON><PERSON><PERSON> (Peladen Pihak <PERSON>)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "<PERSON><PERSON><PERSON><PERSON>", "translation.test.args": "%s %s", "translation.test.complex": "Awalan, %s%2$s lagi %s dan %1$s terachir %s dan djoega %1$s lagi!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "halo %s", "translation.test.invalid2": "halo %s", "translation.test.none": "Halo, do<PERSON>!", "translation.test.world": "doenia", "trim_material.minecraft.amethyst": "<PERSON><PERSON> ket<PERSON>", "trim_material.minecraft.copper": "<PERSON><PERSON> tembaga", "trim_material.minecraft.diamond": "<PERSON><PERSON>", "trim_material.minecraft.emerald": "<PERSON><PERSON>", "trim_material.minecraft.gold": "<PERSON><PERSON> emas", "trim_material.minecraft.iron": "<PERSON><PERSON> besi", "trim_material.minecraft.lapis": "<PERSON><PERSON>", "trim_material.minecraft.netherite": "<PERSON><PERSON> netherit", "trim_material.minecraft.quartz": "<PERSON><PERSON> k<PERSON>s", "trim_material.minecraft.redstone": "Bahan redstone", "trim_material.minecraft.resin": "<PERSON><PERSON> damar", "trim_pattern.minecraft.bolt": "<PERSON><PERSON><PERSON> zirah „baoet”", "trim_pattern.minecraft.coast": "<PERSON><PERSON><PERSON> z<PERSON>h „pesisir”", "trim_pattern.minecraft.dune": "<PERSON><PERSON><PERSON> zirah „goemoek”", "trim_pattern.minecraft.eye": "<PERSON><PERSON><PERSON> zirah „mata”", "trim_pattern.minecraft.flow": "<PERSON><PERSON><PERSON> zirah „pilin”", "trim_pattern.minecraft.host": "<PERSON><PERSON><PERSON> z<PERSON>h „toean roemah”", "trim_pattern.minecraft.raiser": "<PERSON><PERSON><PERSON> zirah „pemelihara”", "trim_pattern.minecraft.rib": "<PERSON><PERSON><PERSON> zirah „roesoek”", "trim_pattern.minecraft.sentry": "<PERSON><PERSON><PERSON> zirah „pengawal”", "trim_pattern.minecraft.shaper": "<PERSON><PERSON><PERSON> zirah „pembentoek”", "trim_pattern.minecraft.silence": "<PERSON><PERSON><PERSON> zirah „keheningan”", "trim_pattern.minecraft.snout": "<PERSON><PERSON><PERSON> zirah „montjong”", "trim_pattern.minecraft.spire": "<PERSON><PERSON><PERSON> zirah „menara”", "trim_pattern.minecraft.tide": "<PERSON><PERSON><PERSON> zirah „gelombang pasang”", "trim_pattern.minecraft.vex": "<PERSON><PERSON><PERSON> zirah „oesik”", "trim_pattern.minecraft.ward": "<PERSON><PERSON><PERSON> zirah „djaga”", "trim_pattern.minecraft.wayfinder": "<PERSON><PERSON><PERSON> zirah „pentjari arah”", "trim_pattern.minecraft.wild": "<PERSON><PERSON><PERSON> z<PERSON>h „liar”", "tutorial.bundleInsert.description": "Pentjet kanan oentoek menambahkan benda", "tutorial.bundleInsert.title": "<PERSON><PERSON> kantong", "tutorial.craft_planks.description": "Boekoe resep bisa membantoe", "tutorial.craft_planks.title": "Boeat papan kajoe", "tutorial.find_tree.description": "Poekoel oentoek mendapatkan kajoe", "tutorial.find_tree.title": "<PERSON><PERSON><PERSON> satoe pohon", "tutorial.look.description": "Pakai tetikoes toean oentoek berbelok", "tutorial.look.title": "<PERSON><PERSON>", "tutorial.move.description": "Lompat dengan %s", "tutorial.move.title": "Gerak dengan %s, %s, %s dan %s", "tutorial.open_inventory.description": "Tekan %s", "tutorial.open_inventory.title": "<PERSON><PERSON> per<PERSON>", "tutorial.punch_tree.description": "Tahan %s", "tutorial.punch_tree.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> pohon", "tutorial.socialInteractions.description": "Tekan %s oentoek memboeka", "tutorial.socialInteractions.title": "Interaksi Sosial", "upgrade.minecraft.netherite_upgrade": "Peningkatan netherit"}