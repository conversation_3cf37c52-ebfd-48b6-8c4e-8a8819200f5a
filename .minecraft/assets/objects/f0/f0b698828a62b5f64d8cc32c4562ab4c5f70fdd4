{"accessibility.onboarding.accessibility.button": "Ulekcynia dostympu…", "accessibility.onboarding.screen.narrator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> er<PERSON>jlera po kneflu Enter", "accessibility.onboarding.screen.title": "Witej we Minecrafcie!\n\nChcesz załōnczyć ercyjlera abo przelyź ku sekcyji ulekcyń dostympu?", "addServer.add": "<PERSON><PERSON><PERSON>", "addServer.enterIp": "<PERSON><PERSON><PERSON> ser<PERSON>a", "addServer.enterName": "<PERSON><PERSON> se<PERSON>", "addServer.resourcePack": "<PERSON><PERSON> z<PERSON>w serwera", "addServer.resourcePack.disabled": "W<PERSON>łōnc<PERSON><PERSON><PERSON>", "addServer.resourcePack.enabled": "Z<PERSON>ł<PERSON><PERSON><PERSON><PERSON><PERSON>", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON>", "addServer.title": "Edytuj informacyje ô serwerze", "advMode.command": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode": "<PERSON><PERSON>", "advMode.mode.auto": "Powtŏrzany", "advMode.mode.autoexec.bat": "<PERSON><PERSON><PERSON> aktywny", "advMode.mode.conditional": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.redstone": "Impulsowy", "advMode.mode.redstoneTriggered": "Wymŏgŏ zignalu", "advMode.mode.sequence": "<PERSON><PERSON><PERSON>", "advMode.mode.unconditional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.notAllowed": "<PERSON><PERSON><PERSON> mieć uprawniynia ôperatora we kreatywnym trybie", "advMode.notEnabled": "Bloki kōmynd niy sōm dōstymp<PERSON> na tym serwerze", "advMode.previousOutput": "Poprz<PERSON><PERSON><PERSON> kōmynda", "advMode.setCommand": "Nasztal<PERSON>j k<PERSON>õ kōmyndã lŏ bloku", "advMode.setCommand.success": "Nasztalowano kōmynda: %s", "advMode.trackOutput": "Śledzynie wydŏwanych danych", "advMode.triggering": "Aktywacyjny", "advMode.type": "<PERSON><PERSON><PERSON>", "advancement.advancementNotFound": "Niyznany postymp: „%s”", "advancements.adventure.adventuring_time.description": "Ôdekryj kŏ<PERSON>dy biōm", "advancements.adventure.adventuring_time.title": "Czas na przigodã", "advancements.adventure.arbalistic.description": "Ubij p<PERSON><PERSON><PERSON><PERSON> roztōmańtych stworzyń po jednym strzale ôd kuszy", "advancements.adventure.arbalistic.title": "Tref we pōnkt", "advancements.adventure.avoid_vibration.description": "Z<PERSON><PERSON><PERSON><PERSON><PERSON> sie wele skalkowego synsora abo wachmajstra tak, coby niy <PERSON> wykrytym", "advancements.adventure.avoid_vibration.title": "Żŏdyn sie niy dowiy", "advancements.adventure.blowback.description": "Ubij wiytrzika po ôd<PERSON><PERSON> wystrzelōnego ôd niego wietrznego ladōnku", "advancements.adventure.blowback.title": "Agresorowi wiater we ś<PERSON>pia", "advancements.adventure.brush_armadillo.description": "Zdobydź szupy ôbryncznika po pōmocy pyndzla", "advancements.adventure.brush_armadillo.title": "Tyn to je łupny", "advancements.adventure.bullseye.description": "Cylnij we postrzodek szajby ze ôdlygłości przinojmnij 30 metrōw", "advancements.adventure.bullseye.title": "We sōm postrz<PERSON>k", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Stwōrz prziôzdobiōny bōnclŏk ze sztyrech szplitrōw ceramiki", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Ale z<PERSON>ōn!", "advancements.adventure.crafters_crafting_crafters.description": "<PERSON><PERSON><PERSON> kole wytwŏrzŏcza, kedy tyn wytwŏrzi wytwŏrzŏcz", "advancements.adventure.crafters_crafting_crafters.title": "Wytwōrczŏ wytwōrnia wytwŏrzŏczōw", "advancements.adventure.fall_from_world_height.description": "<PERSON><PERSON>ć ze szczytu <PERSON> (ôd limitu budowani<PERSON>) ku jego dnu a przetwej fal", "advancements.adventure.fall_from_world_height.title": "<PERSON><PERSON><PERSON> a klify", "advancements.adventure.heart_transplanter.description": "Umiyś we piōnie syrce grzipŏcza pōmiyndzy dwōma sztamami bladego dymbu", "advancements.adventure.heart_transplanter.title": "Kardiolog", "advancements.adventure.hero_of_the_village.description": "Ôbrōń wiŏsk<PERSON> ôd an<PERSON>lu", "advancements.adventure.hero_of_the_village.title": "Bohatyr wiŏski", "advancements.adventure.honey_block_slide.description": "Wylōnduj na bloku miodu, coby p<PERSON><PERSON><PERSON> fal", "advancements.adventure.honey_block_slide.title": "Lepkawŏ sytuacyjŏ", "advancements.adventure.kill_a_mob.description": "<PERSON><PERSON><PERSON> le<PERSON> jaki<PERSON>", "advancements.adventure.kill_a_mob.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.kill_all_mobs.description": "Ubij po jednym ze kŏżdyj zorty bebŏkōw", "advancements.adventure.kill_all_mobs.title": "Gōn na bebŏki", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Ubij stworzynie wele sculkowego katalizatora", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Ôno sie szyrzi!", "advancements.adventure.lighten_up.description": "Ôszkrobej ze cieślicy mi<PERSON> byrn<PERSON>, coby bard<PERSON><PERSON>", "advancements.adventure.lighten_up.title": "Po jasnŏku lepij", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Ôchrōń wiyśniŏka ôd blicu bez rozpŏlaniŏ przi tym ôgnia", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "<PERSON><PERSON> go blic niy prasknie", "advancements.adventure.minecraft_trials_edition.description": "<PERSON><PERSON><PERSON> ku kōmnacie prōb", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Prōbnŏ Edycyjŏ", "advancements.adventure.ol_betsy.description": "Strzyl ze kuszy", "advancements.adventure.ol_betsy.title": "<PERSON>no sie niy luftnij", "advancements.adventure.overoverkill.description": "Zadej 50 serc zraniyń po jednym szlagu ze buzdyganu", "advancements.adventure.overoverkill.title": "Przebranie miary", "advancements.adventure.play_jukebox_in_meadows.description": "Ôbdarz <PERSON><PERSON><PERSON><PERSON> po zagraniu muzyki ze platynszpilera", "advancements.adventure.play_jukebox_in_meadows.title": "Gr<PERSON> a buczy", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Ôdebier zignal nadŏwany ôd wyr<PERSON>bianego bicherszranku po pōmocy kōmparatora", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Moc ksiōnżek", "advancements.adventure.revaulting.description": "Ôdszperuj lagramyncki szac po lagramynckiym kluczu prōby", "advancements.adventure.revaulting.title": "Szacowny powrōt", "advancements.adventure.root.description": "Prz<PERSON>da, ekszploracyjŏ a bōj", "advancements.adventure.root.title": "Przigoda", "advancements.adventure.salvage_sherd.description": "<PERSON><PERSON><PERSON><PERSON> pody<PERSON>zdr<PERSON>y blok, coby <PERSON><PERSON><PERSON> szpliter cera<PERSON>ki", "advancements.adventure.salvage_sherd.title": "Szplitry ôstały słożōne", "advancements.adventure.shoot_arrow.description": "<PERSON><PERSON><PERSON><PERSON> coś po strzale", "advancements.adventure.shoot_arrow.title": "Wycyluj", "advancements.adventure.sleep_in_bed.description": "Przespej się we łōżku, coby <PERSON><PERSON><PERSON><PERSON> pō<PERSON><PERSON>", "advancements.adventure.sleep_in_bed.title": "Słodkich snōw", "advancements.adventure.sniper_duel.description": "Ubij szkeleta ze ôdlygłości przinojmnij 50 metrōw", "advancements.adventure.sniper_duel.title": "<PERSON><PERSON><PERSON><PERSON> szarpaczka", "advancements.adventure.spyglass_at_dragon.description": "Pojzdrzyj na dracha Endu bez ôptikrułã", "advancements.adventure.spyglass_at_dragon.title": "Je to fliger?", "advancements.adventure.spyglass_at_ghast.description": "Pojzdrzyj na ghasta bez ôptikrułã", "advancements.adventure.spyglass_at_ghast.title": "Je to balōn?", "advancements.adventure.spyglass_at_parrot.description": "Pojzdrzyj na papagaja bez ôptikrułã", "advancements.adventure.spyglass_at_parrot.title": "Je to ptŏk?", "advancements.adventure.summon_iron_golem.description": "<PERSON><PERSON><PERSON><PERSON>, coby s<PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON><PERSON>", "advancements.adventure.summon_iron_golem.title": "Żelazny pōmager", "advancements.adventure.throw_trident.description": "Ciepnij we coś ze trzizymbu.\nChociŏż dŏwej sie baczynie: Wyciepanie swojij jedynyj brōni niyma dobrŏ idyjŏ.", "advancements.adventure.throw_trident.title": "<PERSON><PERSON> choby ch<PERSON>ł a… umioł", "advancements.adventure.totem_of_undying.description": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, coby <PERSON><PERSON><PERSON>", "advancements.adventure.totem_of_undying.title": "Zmŏrtwychstŏcie", "advancements.adventure.trade.description": "Zrōb g<PERSON>zeft ze wiyśniŏkym", "advancements.adventure.trade.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ale szaber!", "advancements.adventure.trade_at_world_height.description": "Zrōb gyszeft ze wiyśniŏkym na sōmeśkim szczycie świŏta", "advancements.adventure.trade_at_world_height.title": "G<PERSON><PERSON>t pod gwiŏzdami", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Nałōż aby rŏz ôrnamynty pancra: szpica, niur<PERSON>k, ziobro, wachmajster, cisza, szerga, wela, wegwajzer", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Kowŏl włŏsnego losu", "advancements.adventure.trim_with_any_armor_pattern.description": "Stwōrz prziôzdobiōny pancer na stole kowŏla", "advancements.adventure.trim_with_any_armor_pattern.title": "Nowe lōnty wojŏka", "advancements.adventure.two_birds_one_arrow.description": "Ubij dwie mory po przeżgajōncyj strzale", "advancements.adventure.two_birds_one_arrow.title": "I po ptŏkach", "advancements.adventure.under_lock_and_key.description": "Ôdszperuj szac po pōmocy klucza prōby", "advancements.adventure.under_lock_and_key.title": "Zawrzite na amynt", "advancements.adventure.use_lodestone.description": "Użyj kōmpasu na magnetycie", "advancements.adventure.use_lodestone.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.very_very_frightening.description": "Prasknij wiyśniŏka blicym", "advancements.adventure.very_very_frightening.title": "<PERSON><PERSON> stracōny ze strachu", "advancements.adventure.voluntary_exile.description": "Ubij prziwōdcy anfalu.\nMożno tyż rozwŏż trzimać sie fōrt ôd wiŏsek bez jakiś czas…", "advancements.adventure.voluntary_exile.title": "Wygnanie na żōndanie", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Prz<PERSON>ń<PERSON><PERSON> sie po sypkim <PERSON> bez zapŏdaniŏ sie", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON> choby <PERSON>", "advancements.adventure.who_needs_rockets.description": "Uż<PERSON>j w<PERSON>rz<PERSON><PERSON>, coby sie w<PERSON><PERSON><PERSON><PERSON> 8 blokōw ku wiyrchu", "advancements.adventure.who_needs_rockets.title": "Na co komu fojerwerki?", "advancements.adventure.whos_the_pillager_now.description": "Ôkrŏdź raubi<PERSON>za jak ôn sōm", "advancements.adventure.whos_the_pillager_now.title": "Fto terŏzki je rau<PERSON>z?", "advancements.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON> sie, i<PERSON>e samkej nic niy ma…", "advancements.end.dragon_breath.description": "<PERSON><PERSON><PERSON> drachowy dych ku flaszce", "advancements.end.dragon_breath.title": "Porŏcz jã szklŏkym", "advancements.end.dragon_egg.description": "Potrzimej d<PERSON> j<PERSON>", "advancements.end.dragon_egg.title": "Nŏstympnŏ gyneracyjŏ", "advancements.end.elytra.description": "Znŏj<PERSON><PERSON>", "advancements.end.elytra.title": "Zniesiynie <PERSON>w", "advancements.end.enter_end_gateway.description": "Uciycz ze wyspy", "advancements.end.enter_end_gateway.title": "Uciyczka ze wyspy", "advancements.end.find_end_city.description": "<PERSON><PERSON><PERSON> no rajn, co może być los?", "advancements.end.find_end_city.title": "Eszcze dalij niźli na kōńcu", "advancements.end.kill_dragon.description": "<PERSON><PERSON> tam jako", "advancements.end.kill_dragon.title": "Uwolnij End", "advancements.end.levitate.description": "<PERSON>zleć 50 blokōw we luft ôd nap<PERSON><PERSON><PERSON>w shulkera", "advancements.end.levitate.title": "Ale piykny zglōnd ze tego wiyrchu", "advancements.end.respawn_dragon.description": "Swołej na zaś dracha Endu", "advancements.end.respawn_dragon.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "advancements.end.root.description": "A możno napocznycie?", "advancements.end.root.title": "End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, coby przŏ<PERSON>uch upuściōł tortã na klangblok", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Niych żyje nōm!", "advancements.husbandry.allay_deliver_item_to_player.description": "Ôdebier rzeczy, kere przŏciuch Ci dolifrowoł", "advancements.husbandry.allay_deliver_item_to_player.title": "Mŏsz we mie przŏciela", "advancements.husbandry.axolotl_in_a_bucket.description": "Chyć aksolotla ku ajmrowi", "advancements.husbandry.axolotl_in_a_bucket.title": "Urodny rojber", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, co je do zjedzyniŏ, aji eźli Ci to nabrechŏ", "advancements.husbandry.balanced_diet.title": "Zrōwnowŏżōnŏ dijyta", "advancements.husbandry.breed_all_animals.description": "Rozmnōż wszycke zwiyrzyńcia!", "advancements.husbandry.breed_all_animals.title": "Dwa po dwa", "advancements.husbandry.breed_an_animal.description": "Rozmnōż pŏrkã zwiyrzyńć", "advancements.husbandry.breed_an_animal.title": "Papagaje a flyjdermausy", "advancements.husbandry.complete_catalogue.description": "Ôswōj wszycke zorty kotōw!", "advancements.husbandry.complete_catalogue.title": "Cołki kotolŏk", "advancements.husbandry.feed_snifflet.description": "Nafutr<PERSON>j <PERSON>", "advancements.husbandry.feed_snifflet.title": "Podany na fatra", "advancements.husbandry.fishy_business.description": "Chyć rybã", "advancements.husbandry.fishy_business.title": "Gyszefty hrubyj ryby", "advancements.husbandry.froglights.description": "<PERSON>yj we inwyntŏrzu wszycke zorty blyndołzōw", "advancements.husbandry.froglights.title": "<PERSON><PERSON><PERSON> je wszycke!", "advancements.husbandry.kill_axolotl_target.description": "Skupluj siyły ze aksolotlym a wygrej gyfecht", "advancements.husbandry.kill_axolotl_target.title": "Siyła we kamractwie!", "advancements.husbandry.leash_all_frog_variants.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> lajnã rapitołzie kŏżdyj zorty", "advancements.husbandry.leash_all_frog_variants.title": "Zebrane cuzamyn do kupy", "advancements.husbandry.make_a_sign_glow.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, coby tekst na znaku napoczōn świycić", "advancements.husbandry.make_a_sign_glow.title": "Pokazujã a ôbjaśniōm!", "advancements.husbandry.netherite_hoe.description": "<PERSON>ż<PERSON>j sztangelki netherytu, coby w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>, a zatym ôbmyśl swoje wybory życiowe", "advancements.husbandry.netherite_hoe.title": "Uwŏżne zaangażowanie", "advancements.husbandry.obtain_sniffer_egg.description": "Zdobydź jajco ze sznupŏczym", "advancements.husbandry.obtain_sniffer_egg.title": "Co tak fajnie wōniŏ?", "advancements.husbandry.place_dried_ghast_in_water.description": "Umiyś wyschnytego ghasta we wodzie", "advancements.husbandry.place_dried_ghast_in_water.title": "Woda do piciŏ, a życie do życiŏ", "advancements.husbandry.plant_any_sniffer_seed.description": "Zasadź dowōlne ziŏrno znŏjdzōne ôd s<PERSON>a", "advancements.husbandry.plant_any_sniffer_seed.title": "Tego jeszcze niy siali", "advancements.husbandry.plant_seed.description": "Zasiyj ziŏrno a pojzdrzij jak rośnie", "advancements.husbandry.plant_seed.title": "Ziŏrno ku ziŏrnie", "advancements.husbandry.remove_wolf_armor.description": "Symnij wilczy pancer ze wilka po pōmocy szyrōw", "advancements.husbandry.remove_wolf_armor.title": "<PERSON><PERSON>h, prach!", "advancements.husbandry.repair_wolf_armor.description": "Zeflikuj popsowany wilczy pancer po pōmocy szupy ôd ôbryncznika", "advancements.husbandry.repair_wolf_armor.title": "<PERSON>by nowy", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Popłyń łōdkōm społym ze cigōm", "advancements.husbandry.ride_a_boat_with_a_goat.title": "<PERSON><PERSON><PERSON><PERSON> cigi", "advancements.husbandry.root.description": "Świŏt je fol kamratōw a roztōmańtych maszketōw", "advancements.husbandry.root.title": "Bauerstwo", "advancements.husbandry.safely_harvest_honey.description": "<PERSON><PERSON><PERSON><PERSON> fojery, coby se<PERSON> miōd ze hula ku flaszce, bez szterowaniŏ przi tym binōw", "advancements.husbandry.safely_harvest_honey.title": "Miodowe lata", "advancements.husbandry.silk_touch_nest.description": "<PERSON>ż<PERSON><PERSON> jedw<PERSON><PERSON><PERSON><PERSON>, coby prz<PERSON><PERSON>ś gniŏzdo abo hul ze trzyma binami we postrzodku", "advancements.husbandry.silk_touch_nest.title": "Binowŏ przekludzka", "advancements.husbandry.tactical_fishing.description": "<PERSON><PERSON><PERSON> ryb<PERSON>… yno bez wyndki!", "advancements.husbandry.tactical_fishing.title": "Rybochycynie", "advancements.husbandry.tadpole_in_a_bucket.description": "Ch<PERSON>ć gowŏcza ku ajmrowi", "advancements.husbandry.tadpole_in_a_bucket.title": "Gadzinkowe Gody", "advancements.husbandry.tame_an_animal.description": "Ôswōj zwiyrzã", "advancements.husbandry.tame_an_animal.title": "Nojlepsze kamraty już na zŏwdy", "advancements.husbandry.wax_off.description": "Zeszkrobej waks ze bloku mi<PERSON>!", "advancements.husbandry.wax_off.title": "Be<PERSON> waksu, d<PERSON>ynki", "advancements.husbandry.wax_on.description": "Naraź ślyzyk miodu na blok miydzi!", "advancements.husbandry.wax_on.title": "Ze waksym?", "advancements.husbandry.whole_pack.description": "Ôswōj wszycke zorty wilkōw", "advancements.husbandry.whole_pack.title": "Sfornŏ sfora", "advancements.nether.all_effects.description": "Miyj na siã wszycke efekty ôrŏz", "advancements.nether.all_effects.title": "Jako my sam do<PERSON>?", "advancements.nether.all_potions.description": "Miyj na siã wszycke efekty wywarōw ôrŏz", "advancements.nether.all_potions.title": "No to pyrsk!", "advancements.nether.brew_potion.description": "<PERSON><PERSON><PERSON><PERSON> wywar", "advancements.nether.brew_potion.title": "Swojski mielcŏrz", "advancements.nether.charge_respawn_anchor.description": "Zaladuj anker ôdrodzyniŏ na fol", "advancements.nether.charge_respawn_anchor.title": "<PERSON>ż niy mŏsz dziewiyńciu żyć", "advancements.nether.create_beacon.description": "Stwōrz a postŏw laternã", "advancements.nether.create_beacon.title": "Cima jak we miechu", "advancements.nether.create_full_beacon.description": "<PERSON><PERSON><PERSON><PERSON> na fol", "advancements.nether.create_full_beacon.title": "Laternik", "advancements.nether.distract_piglin.description": "Ôdwrōć baczynie piglinōw przi anbitowaniu złōta", "advancements.nether.distract_piglin.title": "Och, skarbek", "advancements.nether.explore_nether.description": "Ôdekryj wszycke biōmy we Netherze", "advancements.nether.explore_nether.title": "<PERSON><PERSON> ku cieplic<PERSON>m", "advancements.nether.fast_travel.description": "Wykor<PERSON><PERSON><PERSON>, coby przely<PERSON> 7 km na Wiyrchu", "advancements.nether.fast_travel.title": "Wartkŏ, gibkŏ, szybkŏ drabkość!", "advancements.nether.find_bastion.description": "<PERSON><PERSON>ź ku ôstałościōm zōmku", "advancements.nether.find_bastion.title": "<PERSON><PERSON><PERSON><PERSON> to bōło…", "advancements.nether.find_fortress.description": "<PERSON><PERSON><PERSON> ku netherowym festōngu", "advancements.nether.find_fortress.title": "Ôkropiczny festōng", "advancements.nether.get_wither_skull.description": "Zdobydź szalã witherowego szkeleta", "advancements.nether.get_wither_skull.title": "Gnaty ôstały ciepnyte", "advancements.nether.loot_bastion.description": "Złup kastlã ze ôstałości zōmku", "advancements.nether.loot_bastion.title": "<PERSON><PERSON>", "advancements.nether.netherite_armor.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> wszycke tajle netherytowego pancra", "advancements.nether.netherite_armor.title": "Pogrzeb mie we wygorzelisku", "advancements.nether.obtain_ancient_debris.description": "Zdobydź chańdŏwne wygorzelisko", "advancements.nether.obtain_ancient_debris.title": "Szac ze głymbiōw", "advancements.nether.obtain_blaze_rod.description": "Pozbŏw flamŏka jego ruty", "advancements.nether.obtain_blaze_rod.title": "Ôgniym a miyczym", "advancements.nether.obtain_crying_obsidian.description": "Zdobydź płaczliwy ôbsydiōn", "advancements.nether.obtain_crying_obsidian.title": "Fto kreje cwibel?", "advancements.nether.return_to_sender.description": "Ubij ghasta ze jego włŏsnyj brōni", "advancements.nether.return_to_sender.title": "Nazŏd ku abzyndrowi", "advancements.nether.ride_strider.description": "Porajtuj sie na smykŏczu ze szagim grzibym na kijŏku we rynce", "advancements.nether.ride_strider.title": "<PERSON><PERSON>, co to je łōdka!", "advancements.nether.ride_strider_in_overworld_lava.description": "<PERSON><PERSON><PERSON> smy<PERSON>ŏcza na duuugõ rajzã po jeziōrze lawy na Wiyrchu", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> jak d<PERSON>ma", "advancements.nether.root.description": "Ôblycz sie na lato", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "Uret<PERSON>j ghasta ze Netheru, przik<PERSON><PERSON><PERSON> go bezpiycznie na Wiyrch… a zatym go ubij", "advancements.nether.uneasy_alliance.title": "Ściwiarne przimiyrze", "advancements.nether.use_lodestone.description": "Użyj kōmpasu na magnetycie", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON>", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Ôsłŏb a wylycz wiyśniŏka-umarlŏka", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.deflect_arrow.description": "Ôdbij szus po pōmocy tarczy", "advancements.story.deflect_arrow.title": "O <PERSON>y, panŏczku", "advancements.story.enchant_item.description": "Zaklnij rzecz na stole zaklinŏcza", "advancements.story.enchant_item.title": "Magik", "advancements.story.enter_the_end.description": "Prz<PERSON>ń<PERSON>ź bez portŏl ku Endowi", "advancements.story.enter_the_end.title": "Je to kōnie<PERSON>?", "advancements.story.enter_the_nether.description": "Postŏw portal, rozpŏl go a przyńdź ku Netherowi", "advancements.story.enter_the_nether.title": "Pod grub<PERSON>m", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.follow_ender_eye.title": "Filus", "advancements.story.form_obsidian.description": "Zdobydź blok ôbsydianu", "advancements.story.form_obsidian.title": "<PERSON><PERSON><PERSON><PERSON>, bo lawa stygnie", "advancements.story.iron_tools.description": "Ulepsz swojã hakã", "advancements.story.iron_tools.title": "Żelazne prŏwidła", "advancements.story.lava_bucket.description": "Nafoluj ajmer ze lawy", "advancements.story.lava_bucket.title": "Gork<PERSON> t<PERSON>", "advancements.story.mine_diamond.description": "Zdobydź dyjamynty", "advancements.story.mine_diamond.title": "Dyjamynty!", "advancements.story.mine_stone.description": "Wykopej kamiyń po twojij nowyj hace", "advancements.story.mine_stone.title": "<PERSON><PERSON><PERSON> kamiynia", "advancements.story.obtain_armor.description": "Ôblycz tajlã żelaznego pancra", "advancements.story.obtain_armor.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.root.description": "Syrce a historyjŏ gry", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Dyjamyntowy pancer retuje ży<PERSON>", "advancements.story.shiny_gear.title": "Zasyp mie d<PERSON>myntami", "advancements.story.smelt_iron.description": "Rozszmelcuj sztangelkã żelazła", "advancements.story.smelt_iron.title": "Ciynżkŏ sprawa", "advancements.story.upgrade_tools.description": "St<PERSON><PERSON><PERSON> le<PERSON> hakã", "advancements.story.upgrade_tools.title": "Ulepszynie", "advancements.toast.challenge": "Wyzwanie ukōńczōne!", "advancements.toast.goal": "<PERSON>l ô<PERSON>!", "advancements.toast.task": "Zrobiōno postymp!", "argument.anchor.invalid": "Felerne położynie zakotwiczyniŏ bytu: %s", "argument.angle.incomplete": "Niypołny (spodziywano jednego winkla)", "argument.angle.invalid": "<PERSON><PERSON>ny winkel", "argument.block.id.invalid": "Niyznanŏ zorta bloku: „%s”", "argument.block.property.duplicate": "Yno rŏz możno nasztalować włŏsność „%s” blokowi %s", "argument.block.property.invalid": "Blok %s niy śmi mieć „%s” za włŏsność „%s”", "argument.block.property.novalue": "Spodziywano wertu lŏ włŏsności „%s” na bloku %s", "argument.block.property.unclosed": "S<PERSON>d<PERSON><PERSON><PERSON><PERSON> włŏsności sztandu bloku", "argument.block.property.unknown": "Blok %s niy mŏ włŏsności „%s”", "argument.block.tag.disallowed": "Etykety niy sōm samkej prziz<PERSON>l<PERSON>ne, yno bloki", "argument.color.invalid": "Niyznanŏ farba: „%s”", "argument.component.invalid": "Felerny kōmpōnynt czatu: %s", "argument.criteria.invalid": "Niyznane kriteryjum: „%s”", "argument.dimension.invalid": "Niyznany wymiar: „%s”", "argument.double.big": "Nōmera zorty „double” niy śmi być wielgszŏ niż %s, a erbnyto %s", "argument.double.low": "Nōmera zorty „double” niy śmi być myńszŏ niż %s, a erbnyto %s", "argument.entity.invalid": "<PERSON><PERSON><PERSON> miano abo UUID", "argument.entity.notfound.entity": "Niy znŏjdzōno bytu", "argument.entity.notfound.player": "Niy znŏjdzōno grŏcza", "argument.entity.options.advancements.description": "Grŏcze ze postympami", "argument.entity.options.distance.description": "Ôdlygłość ôd bytu", "argument.entity.options.distance.negative": "Ôdlygłość niy może by<PERSON> uj<PERSON>", "argument.entity.options.dx.description": "<PERSON>ty pōmiyndzy X a X + dX", "argument.entity.options.dy.description": "<PERSON><PERSON> p<PERSON>dzy Y a Y + dY", "argument.entity.options.dz.description": "Byty pōmiyndzy Z a Z + dZ", "argument.entity.options.gamemode.description": "Grŏcze ze trybym gry", "argument.entity.options.inapplicable": "Niy śmisz sam użyć ôpcyje „%s”", "argument.entity.options.level.description": "Pozi<PERSON>m <PERSON>i<PERSON>", "argument.entity.options.level.negative": "Poziōm niy ś<PERSON> uj<PERSON>", "argument.entity.options.limit.description": "Maksymalnŏ wielość bytōw ku zwrōcyniu", "argument.entity.options.limit.toosmall": "Limit musi być aspōń 1", "argument.entity.options.mode.invalid": "Felerny abo niyznany tryb gry: „%s”", "argument.entity.options.name.description": "Miano bytu", "argument.entity.options.nbt.description": "Byty ze NBT", "argument.entity.options.predicate.description": "Niysztandardowy predykat", "argument.entity.options.scores.description": "Byty ze pōnktacyjōm", "argument.entity.options.sort.description": "Pozor<PERSON>j byty", "argument.entity.options.sort.irreversible": "Felerny abo niyznany rodzaj zortowaniŏ: „%s”", "argument.entity.options.tag.description": "Byty ze etyketōm", "argument.entity.options.team.description": "Byty we manszafcie", "argument.entity.options.type.description": "<PERSON><PERSON> z<PERSON>y", "argument.entity.options.type.invalid": "Felernŏ abo niyznanŏ zorta bytu: „%s”", "argument.entity.options.unknown": "Niyznane nasztalowanie: „%s”", "argument.entity.options.unterminated": "Spodziywano k<PERSON>", "argument.entity.options.valueless": "Spodziywano wertu lŏ ôpcyje: %s", "argument.entity.options.x.description": "położynie na ôśce X", "argument.entity.options.x_rotation.description": "Ôbrōt bytu we <PERSON>ce X", "argument.entity.options.y.description": "położynie na ôśce Y", "argument.entity.options.y_rotation.description": "Ôbrōt bytu we <PERSON>ce Y", "argument.entity.options.z.description": "położynie na ôśce Z", "argument.entity.selector.allEntities": "<PERSON><PERSON><PERSON><PERSON> byty", "argument.entity.selector.allPlayers": "Wszyscy grŏcze", "argument.entity.selector.missing": "Brakuje zorty ôbierŏcza", "argument.entity.selector.nearestEntity": "Nojbliższy byt", "argument.entity.selector.nearestPlayer": "Nojbliższy grŏcz", "argument.entity.selector.not_allowed": "Niyprzizwolōny ôbierŏcz", "argument.entity.selector.randomPlayer": "Cufalowy grŏcz", "argument.entity.selector.self": "Ôbecny byt", "argument.entity.selector.unknown": "Niyznanŏ zorta ôbierŏcza: „%s”", "argument.entity.toomany": "Przizwolōny je yno jedyn byt, a podany ôbierŏcz przizwŏlŏ na wiyncyj niż jedyn", "argument.enum.invalid": "Felerny wert: „%s”", "argument.float.big": "Nōmera zorty „float” niy śmi być wielgszŏ niż %s, a erbnyto %s", "argument.float.low": "Nōmera zorty „float” niy śmi być myńszŏ niż %s, a erbnyto %s", "argument.gamemode.invalid": "Niyznany tryb gry: „%s”", "argument.hexcolor.invalid": "Felerny szesnŏstkowy kod farby: „%s”", "argument.id.invalid": "Felerne ID", "argument.id.unknown": "Niyznane ID: „%s”", "argument.integer.big": "Nōmera zorty „integer” niy śmi być wielgszŏ niż %s, a erbnyto %s", "argument.integer.low": "Nōmera zorty „integer” niy śmi być myńszŏ niż %s, a erbnyto %s", "argument.item.id.invalid": "Niyznanŏ rzecz: „%s”", "argument.item.tag.disallowed": "Etykety niy sōm samkej przizwolōne, yno iste rzeczy", "argument.literal.incorrect": "Spodziywano dosłownego wertu: %s", "argument.long.big": "Nōmera zorty „long” niy śmi być wielgszŏ niż %s, a erbnyto %s", "argument.long.low": "Nōmera zorty „long” niy śmi być myńszŏ niż %s, a erbnyto %s", "argument.message.too_long": "Czatowŏ wiad<PERSON><PERSON><PERSON><PERSON> bōła za dugŏ (%s > maksimum %s znakōw)", "argument.nbt.array.invalid": "Felernŏ zorta tabule: „%s”", "argument.nbt.array.mixed": "<PERSON>y idzie wrazić %s ku %s", "argument.nbt.expected.compound": "Spodziywano złożōnyj etykety", "argument.nbt.expected.key": "Spodziywan<PERSON>", "argument.nbt.expected.value": "Spodziywano wertu", "argument.nbt.list.mixed": "Niy idzie wkludzić %s ku liście %s", "argument.nbt.trailing": "Niyspodziywane poślednie dane", "argument.player.entities": "Ta kōmynda fōnguje yno na grŏczy, a podany ôbierŏcz zawiyrŏ tyż inksze byty", "argument.player.toomany": "Przizwolōny je yno jedyn gr<PERSON>cz, a podany ôbierŏcz przizwŏlŏ na wiyncyj niż jedyn", "argument.player.unknown": "Niy ma żŏdnego takigo grŏcza", "argument.pos.missing.double": "Spodziywano spōłrzyndnyj", "argument.pos.missing.int": "Spodziywano położyniŏ bloku", "argument.pos.mixed": "<PERSON><PERSON> <PERSON><PERSON> mi<PERSON> zglyndnych a globalnych koôrdynatōw (^ musi być wszyńdzie abo nigdzie)", "argument.pos.outofbounds": "To położynie je poza przizwolōnymi granicami.", "argument.pos.outofworld": "To położynie je poza tym świŏtym!", "argument.pos.unloaded": "To położynie niyma z<PERSON>wanŏ", "argument.pos2d.incomplete": "Niypołny (spodziywano dwōch spōłrzyndnych)", "argument.pos3d.incomplete": "Niypołny (spodziywano trzech spōłrzyndnych)", "argument.range.empty": "Spodziywano wertu abo zakrysu wertōw", "argument.range.ints": "Przizwolōne sōm yno cołke nōmery", "argument.range.swapped": "Minimum niy śmi być wielgsze niż maksimum", "argument.resource.invalid_type": "Elymynt „%s” je niynŏleżnyj zorty: „%s” (spodziywano „%s”)", "argument.resource.not_found": "Niy idzie znŏjś elymyntu „%s” zorty „%s”", "argument.resource_or_id.failed_to_parse": "Niy podarziło sie przetworzić struktury: %s", "argument.resource_or_id.invalid": "Felerne ID abo tag", "argument.resource_or_id.no_such_element": "Niy idzie znŏjś elymyntu „%s” we registrze „%s”", "argument.resource_selector.not_found": "Brak wynikōw lŏ ôbierŏcza „%s” zorty „%s”", "argument.resource_tag.invalid_type": "Etyketa „%s” je niynŏleżnyj zorty „%s” (spodziywano „%s”)", "argument.resource_tag.not_found": "Niy idzie znŏjś etykety „%s” zorty „%s”", "argument.rotation.incomplete": "Niypołny (spodziywano dwōch spōłrzyndnych)", "argument.scoreHolder.empty": "Niy znŏjdzōno żŏdnych posiedzicieli pōnktacyje", "argument.scoreboardDisplaySlot.invalid": "Niyznane pole: „%s”", "argument.style.invalid": "Felerny wyglōnd: „%s”", "argument.time.invalid_tick_count": "Nōmera tikōw niy śmi być ujymn<PERSON>", "argument.time.invalid_unit": "Felernŏ jednŏstka", "argument.time.tick_count_too_low": "Liczba tikōw niy śmi być myńszŏ niż %s, a erbnyto %s", "argument.uuid.invalid": "Niyznane UUID", "argument.waypoint.invalid": "Ôbrany byt niyma we<PERSON>", "arguments.block.tag.unknown": "Niyznanŏ etyketa bloku: „%s”", "arguments.function.tag.unknown": "Niyznanŏ etyketa fōnkcyje: „%s”", "arguments.function.unknown": "Niyznanŏ fōnkcyjŏ: „%s”", "arguments.item.component.expected": "Spodziywano skłŏdnika ôd rzeczy", "arguments.item.component.malformed": "Felernie uformowany skłŏdnik ôd rzeczy „%s”: „%s”", "arguments.item.component.repeated": "<PERSON><PERSON> jedyn wert może by<PERSON> podany, a skłŏdnik ôd rzeczy „%s” sie powtŏrzŏ", "arguments.item.component.unknown": "Niyznany skłŏdnik ôd rzeczy: „%s”", "arguments.item.malformed": "Felernŏ rzecz: „%s”", "arguments.item.overstacked": "%s idzie skłŏdać maksymalnie po %s", "arguments.item.predicate.malformed": "Felerny predykat „%s”: „%s”", "arguments.item.predicate.unknown": "Niyznany predykat: „%s”", "arguments.item.tag.unknown": "Niyznanŏ etyketa rzeczy: „%s”", "arguments.nbtpath.node.invalid": "Fe<PERSON>ny elymynt cesty NBT", "arguments.nbtpath.nothing_found": "Niy znŏjd<PERSON><PERSON><PERSON> elymyntōw, kere pasujōm ku %s", "arguments.nbtpath.too_deep": "Wert NBT je za głymboko zagniyżdżōny", "arguments.nbtpath.too_large": "Wert NBT je za wielgi", "arguments.objective.notFound": "Niyznany cyl na tabuli wynikōw: „%s”", "arguments.objective.readonly": "Cyl „%s” na tabuli wynikōw je yno ku ôdczytowaniu", "arguments.operation.div0": "<PERSON><PERSON> <PERSON><PERSON> ta<PERSON> bez nulã", "arguments.operation.invalid": "Felerne d<PERSON>łanie", "arguments.swizzle.invalid": "<PERSON><PERSON>n<PERSON> kō<PERSON>ŏ, spodziywano X, Y a Z", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "pancra", "attribute.name.armor_toughness": "strzimałości pancra", "attribute.name.attack_damage": "zraniyń", "attribute.name.attack_knockback": "riksztosu przi napadzie", "attribute.name.attack_speed": "gibkości napadōw", "attribute.name.block_break_speed": "gibkości niszczyniŏ blokōw", "attribute.name.block_interaction_range": "zasiyngu interakcyje ze blokami", "attribute.name.burning_time": "c<PERSON><PERSON> go<PERSON>", "attribute.name.camera_distance": "ôdlygłości perspektywy", "attribute.name.entity_interaction_range": "zasiyngu interakcyje ze bytami", "attribute.name.explosion_knockback_resistance": "ôbstŏciŏ riksztosu eksplozyje", "attribute.name.fall_damage_multiplier": "mnożnika zraniyń ôd upŏdka", "attribute.name.flying_speed": "gibkości lŏtaniŏ", "attribute.name.follow_range": "zasiyngu szlakowaniŏ stworzyń", "attribute.name.generic.armor": "pancra", "attribute.name.generic.armor_toughness": "strzimałości pancra", "attribute.name.generic.attack_damage": "zraniyń", "attribute.name.generic.attack_knockback": "riksztosu przi napadzie", "attribute.name.generic.attack_speed": "gibkości napadōw", "attribute.name.generic.block_interaction_range": "zasiyngu interakcyji ze blokami", "attribute.name.generic.burning_time": "c<PERSON><PERSON> go<PERSON>", "attribute.name.generic.entity_interaction_range": "zasiyngu interakcyji ze bytami", "attribute.name.generic.explosion_knockback_resistance": "ôbstŏciŏ riksztosu eksplozyje", "attribute.name.generic.fall_damage_multiplier": "mnożnika zraniyń ôd upŏdka", "attribute.name.generic.flying_speed": "gibkości lŏtaniŏ", "attribute.name.generic.follow_range": "zasiyngu szlakowaniŏ stworzyń", "attribute.name.generic.gravity": "grawitacyje", "attribute.name.generic.jump_strength": "wysokości skoku", "attribute.name.generic.knockback_resistance": "ôbstŏciŏ riksztosu", "attribute.name.generic.luck": "szczy<PERSON><PERSON><PERSON>ŏ", "attribute.name.generic.max_absorption": "maksymalnego pochłanianiŏ", "attribute.name.generic.max_health": "maksymalnego zdrowiŏ", "attribute.name.generic.movement_efficiency": "grajfności ruchu", "attribute.name.generic.movement_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.oxygen_bonus": "tlynowyj prymije", "attribute.name.generic.safe_fall_distance": "wysokości bezpiycznego upŏdka", "attribute.name.generic.scale": "wielgości", "attribute.name.generic.step_height": "wysokości kroku", "attribute.name.generic.water_movement_efficiency": "grajfności ruchu we wodzie", "attribute.name.gravity": "grawitacyje", "attribute.name.horse.jump_strength": "Siyła skoku kōnia", "attribute.name.jump_strength": "wysokości skoku", "attribute.name.knockback_resistance": "ôbstŏciŏ riksztosu", "attribute.name.luck": "szczy<PERSON><PERSON><PERSON>ŏ", "attribute.name.max_absorption": "maksymalnego pochłanianiŏ", "attribute.name.max_health": "maksymalnego zdrowiŏ", "attribute.name.mining_efficiency": "skutności wydobyciŏ", "attribute.name.movement_efficiency": "grajfności ruchu", "attribute.name.movement_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.oxygen_bonus": "tlynowyj prymije", "attribute.name.player.block_break_speed": "gibkości niszczyniŏ blokōw", "attribute.name.player.block_interaction_range": "zasiyngu interakcyji ze blokami", "attribute.name.player.entity_interaction_range": "zasiyngu interakcyji ze bytami", "attribute.name.player.mining_efficiency": "skutności wydobyciŏ", "attribute.name.player.sneaking_speed": "gibkości zakrŏdaniŏ", "attribute.name.player.submerged_mining_speed": "gibkości wydobyciŏ pod wodōm", "attribute.name.player.sweeping_damage_ratio": "spōłczynnika zraniyń ôd s<PERSON><PERSON><PERSON>adōw", "attribute.name.safe_fall_distance": "wysokości bezpiycznego upŏdka", "attribute.name.scale": "wielgości", "attribute.name.sneaking_speed": "gibkości zakrŏdaniŏ", "attribute.name.spawn_reinforcements": "zmocniyń umarlŏkōw", "attribute.name.step_height": "wysokości kroku", "attribute.name.submerged_mining_speed": "gibkości wydobyciŏ pod wodōm", "attribute.name.sweeping_damage_ratio": "spōłczynnika zraniyń ôd s<PERSON><PERSON><PERSON>adōw", "attribute.name.tempt_range": "zasiyngu wabiyniŏ stworzyń", "attribute.name.water_movement_efficiency": "grajfności ruchu we wodzie", "attribute.name.waypoint_receive_range": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.waypoint_transmit_range": "Zasiyng nadŏwaniŏ wegwajzera", "attribute.name.zombie.spawn_reinforcements": "zmocniyń umarlŏkōw", "biome.minecraft.badlands": "Ustrōnie", "biome.minecraft.bamboo_jungle": "Bambusowy dżōngel", "biome.minecraft.basalt_deltas": "Bazaltowe delty", "biome.minecraft.beach": "Plaża", "biome.minecraft.birch_forest": "Brzizkowy las", "biome.minecraft.cherry_grove": "Trześniowy gŏj", "biome.minecraft.cold_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.crimson_forest": "Szarłatny las", "biome.minecraft.dark_forest": "Ć<PERSON><PERSON>", "biome.minecraft.deep_cold_ocean": "Głymboki zimny ôcean", "biome.minecraft.deep_dark": "Markotnŏ głymbia", "biome.minecraft.deep_frozen_ocean": "Głymboki zamarznyty ôcean", "biome.minecraft.deep_lukewarm_ocean": "Głymboki ciepły ôcean", "biome.minecraft.deep_ocean": "Głymboki ôcean", "biome.minecraft.desert": "<PERSON>ustyniŏ", "biome.minecraft.dripstone_caves": "Naciekowe jaskinie", "biome.minecraft.end_barrens": "Endowŏ pustacina", "biome.minecraft.end_highlands": "Endowe wyżyny", "biome.minecraft.end_midlands": "<PERSON><PERSON><PERSON> strz<PERSON><PERSON><PERSON>", "biome.minecraft.eroded_badlands": "Zwietrzałe ustrōnia", "biome.minecraft.flower_forest": "Kwiŏtkowy las", "biome.minecraft.forest": "Las", "biome.minecraft.frozen_ocean": "Zamarznyty <PERSON>", "biome.minecraft.frozen_peaks": "Zamarznyte gipfle", "biome.minecraft.frozen_river": "Zamarznytŏ rzyka", "biome.minecraft.grove": "<PERSON><PERSON><PERSON>", "biome.minecraft.ice_spikes": "Lodowe szpice", "biome.minecraft.jagged_peaks": "Ôstre gipfle", "biome.minecraft.jungle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.lush_caves": "Ôkwite jaskinie", "biome.minecraft.mangrove_swamp": "Namorzinowe barzoły", "biome.minecraft.meadow": "Gōrskŏ łōnka", "biome.minecraft.mushroom_fields": "Grzibowe pola", "biome.minecraft.nether_wastes": "Netherowe ustrōnia", "biome.minecraft.ocean": "Ôcean", "biome.minecraft.old_growth_birch_forest": "Wiekowy brzizkowy las", "biome.minecraft.old_growth_pine_taiga": "Wiekowŏ chojŏkowŏ tajga", "biome.minecraft.old_growth_spruce_taiga": "Wiekowŏ świyrkowŏ tajga", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON>", "biome.minecraft.plains": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.river": "Rzyka", "biome.minecraft.savanna": "<PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Sawannowe płaskowiyrchy", "biome.minecraft.small_end_islands": "<PERSON><PERSON><PERSON> wysepki", "biome.minecraft.snowy_beach": "Śniyżnŏ plaża", "biome.minecraft.snowy_plains": "Śniyżne rōwnie", "biome.minecraft.snowy_slopes": "Śniyżne sz<PERSON>py", "biome.minecraft.snowy_taiga": "Śniyżnŏ tajga", "biome.minecraft.soul_sand_valley": "Duszyczkowŏ dukla", "biome.minecraft.sparse_jungle": "Rzŏdki <PERSON>", "biome.minecraft.stony_peaks": "Skŏlite gipfle", "biome.minecraft.stony_shore": "Skŏlite wybrzeże", "biome.minecraft.sunflower_plains": "Zōnynblumowe rōwnie", "biome.minecraft.swamp": "Barzoły", "biome.minecraft.taiga": "<PERSON><PERSON><PERSON>", "biome.minecraft.the_end": "End", "biome.minecraft.the_void": "Prōżnia", "biome.minecraft.warm_ocean": "Ciepły ôcean", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.windswept_forest": "Wietrzny las", "biome.minecraft.windswept_gravelly_hills": "Wietrzne kisowe zgōrza", "biome.minecraft.windswept_hills": "Zwietrzałe zgōrza", "biome.minecraft.windswept_savanna": "<PERSON><PERSON>rz<PERSON><PERSON> sawanna", "biome.minecraft.wooded_badlands": "<PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON>", "block.minecraft.acacia_button": "Agacowy knefel", "block.minecraft.acacia_door": "Agacowe dźwiyrze", "block.minecraft.acacia_fence": "Agacowy płot", "block.minecraft.acacia_fence_gate": "Agacowŏ wrōtka", "block.minecraft.acacia_hanging_sign": "Agacowy szild", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON> agacu", "block.minecraft.acacia_log": "<PERSON><PERSON><PERSON> a<PERSON>", "block.minecraft.acacia_planks": "Agacowe brety", "block.minecraft.acacia_pressure_plate": "Agacowŏ drukplata", "block.minecraft.acacia_sapling": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "block.minecraft.acacia_sign": "Agacowy znak", "block.minecraft.acacia_slab": "Agacowŏ fliza", "block.minecraft.acacia_stairs": "Agacowe słody", "block.minecraft.acacia_trapdoor": "Agacowŏ szłapa", "block.minecraft.acacia_wall_hanging_sign": "Agacowy ściynny szild", "block.minecraft.acacia_wall_sign": "Agacowy ściynny znak", "block.minecraft.acacia_wood": "Agacowe drzewo", "block.minecraft.activator_rail": "Aktywacyjne glajzy", "block.minecraft.air": "Luft", "block.minecraft.allium": "Knobloch", "block.minecraft.amethyst_block": "Blok ametystu", "block.minecraft.amethyst_cluster": "Klōmp ametystu", "block.minecraft.ancient_debris": "Chańdŏwne wygorzelisko", "block.minecraft.andesite": "Andezit", "block.minecraft.andesite_slab": "Andezitowŏ fliza", "block.minecraft.andesite_stairs": "Andezitowe słody", "block.minecraft.andesite_wall": "Andezitowy murek", "block.minecraft.anvil": "Ambos", "block.minecraft.attached_melon_stem": "Prziłōnczōny sztyngel wasermelōna", "block.minecraft.attached_pumpkin_stem": "Prziłōnczōny sztyngel banie", "block.minecraft.azalea": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azalea_leaves": "Liście azalije", "block.minecraft.azure_bluet": "<PERSON><PERSON><PERSON><PERSON> hiustōnijŏ", "block.minecraft.bamboo": "Bambus", "block.minecraft.bamboo_block": "Blok bambusa", "block.minecraft.bamboo_button": "Bambusowy knefel", "block.minecraft.bamboo_door": "Bambusowe dźwiyrze", "block.minecraft.bamboo_fence": "Bambusowy płot", "block.minecraft.bamboo_fence_gate": "Bambusowŏ wrōtka", "block.minecraft.bamboo_hanging_sign": "Bambusowy szild", "block.minecraft.bamboo_mosaic": "Bambusowy sztukwerk", "block.minecraft.bamboo_mosaic_slab": "Bambusowŏ sztukwerkowŏ fliza", "block.minecraft.bamboo_mosaic_stairs": "Bambusowe sztukwerkowe słody", "block.minecraft.bamboo_planks": "Bambusowe brety", "block.minecraft.bamboo_pressure_plate": "Bambusowŏ drukplata", "block.minecraft.bamboo_sapling": "<PERSON><PERSON><PERSON> bam<PERSON>a", "block.minecraft.bamboo_sign": "Bambusowy znak", "block.minecraft.bamboo_slab": "Bambusowŏ fliza", "block.minecraft.bamboo_stairs": "Bambusowe słody", "block.minecraft.bamboo_trapdoor": "Bambusowŏ szłapa", "block.minecraft.bamboo_wall_hanging_sign": "Bambusowy ściynny szild", "block.minecraft.bamboo_wall_sign": "Bambusowy ściynny znak", "block.minecraft.banner.base.black": "Czŏrne tło", "block.minecraft.banner.base.blue": "<PERSON><PERSON> tło", "block.minecraft.banner.base.brown": "Brōnŏtne tło", "block.minecraft.banner.base.cyan": "Cyjanowe tło", "block.minecraft.banner.base.gray": "<PERSON><PERSON> tło", "block.minecraft.banner.base.green": "<PERSON>iel<PERSON><PERSON> tło", "block.minecraft.banner.base.light_blue": "Światłomodre tło", "block.minecraft.banner.base.light_gray": "Światłosiwe tło", "block.minecraft.banner.base.lime": "Limōnkowe tło", "block.minecraft.banner.base.magenta": "Purpurowe tło", "block.minecraft.banner.base.orange": "Ôranżowe tło", "block.minecraft.banner.base.pink": "Rōżowe tło", "block.minecraft.banner.base.purple": "Flidrowe tło", "block.minecraft.banner.base.red": "Czyrwōne tło", "block.minecraft.banner.base.white": "Biołe tło", "block.minecraft.banner.base.yellow": "Żōłte tło", "block.minecraft.banner.border.black": "Czŏrnŏ rōma", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.brown": "Brōnŏtnŏ rōma", "block.minecraft.banner.border.cyan": "Cyjanow<PERSON> rōma", "block.minecraft.banner.border.gray": "Siwŏ rōma", "block.minecraft.banner.border.green": "Zielōn<PERSON> rōma", "block.minecraft.banner.border.light_blue": "Światłomodrŏ rōma", "block.minecraft.banner.border.light_gray": "Światłosiwŏ rōma", "block.minecraft.banner.border.lime": "Limōnkowŏ rōma", "block.minecraft.banner.border.magenta": "Purpurowŏ rōma", "block.minecraft.banner.border.orange": "Ôranżowŏ rōma", "block.minecraft.banner.border.pink": "Rōżowŏ rōma", "block.minecraft.banner.border.purple": "Flidrowŏ rōma", "block.minecraft.banner.border.red": "Czyrwōnŏ rōma", "block.minecraft.banner.border.white": "Biołŏ rōma", "block.minecraft.banner.border.yellow": "Żōłtŏ rōma", "block.minecraft.banner.bricks.black": "<PERSON><PERSON><PERSON><PERSON><PERSON> mur", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON><PERSON> mur", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mur", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON><PERSON> mur", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON> mur", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON><PERSON> mur", "block.minecraft.banner.bricks.light_blue": "Światłomodry mur", "block.minecraft.banner.bricks.light_gray": "Światłos<PERSON><PERSON> mur", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON><PERSON> mur", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON> mur", "block.minecraft.banner.bricks.orange": "Ôranżowy mur", "block.minecraft.banner.bricks.pink": "Rōżowy mur", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON><PERSON><PERSON> mur", "block.minecraft.banner.bricks.red": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mur", "block.minecraft.banner.bricks.white": "Bioły mur", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON><PERSON> mur", "block.minecraft.banner.circle.black": "Cz<PERSON>rne rōnto", "block.minecraft.banner.circle.blue": "<PERSON><PERSON>", "block.minecraft.banner.circle.brown": "Brōnŏtne rōnto", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON> r<PERSON>", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "Światłomodre rōnto", "block.minecraft.banner.circle.light_gray": "Światłosiwe rōnto", "block.minecraft.banner.circle.lime": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.circle.magenta": "Purpurowe rōnto", "block.minecraft.banner.circle.orange": "Ôranżowe rōnto", "block.minecraft.banner.circle.pink": "Rōżowe rōnto", "block.minecraft.banner.circle.purple": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.circle.red": "Czyrw<PERSON><PERSON> r<PERSON>", "block.minecraft.banner.circle.white": "Biołe rōnto", "block.minecraft.banner.circle.yellow": "Żōłte rōnto", "block.minecraft.banner.creeper.black": "Czŏrny creeper", "block.minecraft.banner.creeper.blue": "Modry creeper", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.cyan": "Cyjanowy creeper", "block.minecraft.banner.creeper.gray": "<PERSON>wy creeper", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.light_blue": "Światłomodry creeper", "block.minecraft.banner.creeper.light_gray": "Światłos<PERSON><PERSON> creeper", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.magenta": "Purpurowy creeper", "block.minecraft.banner.creeper.orange": "Ôranżowy creeper", "block.minecraft.banner.creeper.pink": "Rōżowy creeper", "block.minecraft.banner.creeper.purple": "Flidrowy creeper", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.white": "Bioły creeper", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.cross.black": "Czŏrny kośny krziż", "block.minecraft.banner.cross.blue": "Modry kośny krziż", "block.minecraft.banner.cross.brown": "Brōnŏtny kośny krziż", "block.minecraft.banner.cross.cyan": "Cyjanowy kośny krziż", "block.minecraft.banner.cross.gray": "Siwy kośny krziż", "block.minecraft.banner.cross.green": "Zielōny kośny krziż", "block.minecraft.banner.cross.light_blue": "Światłomodry kośny krziż", "block.minecraft.banner.cross.light_gray": "Światłosiwy kośny krziż", "block.minecraft.banner.cross.lime": "Limōnkowy kośny krziż", "block.minecraft.banner.cross.magenta": "Purpurowy kośny krziż", "block.minecraft.banner.cross.orange": "Ôranżowy kośny krziż", "block.minecraft.banner.cross.pink": "Rōżowy kośny krziż", "block.minecraft.banner.cross.purple": "Flidrowy kośny krziż", "block.minecraft.banner.cross.red": "Czyrwōny kośny krziż", "block.minecraft.banner.cross.white": "Bioły kośny krziż", "block.minecraft.banner.cross.yellow": "Żōłty kośny krziż", "block.minecraft.banner.curly_border.black": "Czŏrnŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.blue": "Modrŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.brown": "Brōnŏtnŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.cyan": "Cyjanowŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.gray": "Siwŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.green": "Zielōnŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.light_blue": "Światłomodrŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.light_gray": "Światłosiwŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.lime": "Limōnkowŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.magenta": "Purpurowŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.orange": "Ôranżowŏ faltowan<PERSON> rōma", "block.minecraft.banner.curly_border.pink": "Rōżowŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.purple": "Flidrowŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.red": "Czyrwōnŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.white": "Biołŏ faltowanŏ rōma", "block.minecraft.banner.curly_border.yellow": "Żōłtŏ faltowanŏ rōma", "block.minecraft.banner.diagonal_left.black": "Czŏrny kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.blue": "Modry kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.brown": "Brōnŏtny kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.cyan": "Cyjanowy kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.gray": "Siwy kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.green": "Zielōny kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.light_blue": "Światłomŏdry kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.light_gray": "Światłosiwy kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.lime": "Limōnkowy kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.magenta": "Purpurowy kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.orange": "Ôranżowy kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.pink": "Rōżowy kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.purple": "Flidrowy kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.red": "Czyrwōny kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.white": "Bioły kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_left.yellow": "Żōłty kośny lewy wiyrchni połek", "block.minecraft.banner.diagonal_right.black": "Czŏrny kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.blue": "Modry kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.brown": "Brōnŏtny kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.cyan": "Cyjanowy kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.gray": "Siwy kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.green": "Zielōny kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.light_blue": "Światłomodry kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.light_gray": "Światłosiwy kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.lime": "Limōnkowy kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.magenta": "Purpurowy kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.orange": "Ôranżowy kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.pink": "Rōżowy kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.purple": "Flidrowy kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.red": "Czyrwōny kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.white": "Bioły kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_right.yellow": "Żōłty kośny prawy wiyrchni połek", "block.minecraft.banner.diagonal_up_left.black": "Czŏrny kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.blue": "Mŏdry kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.brown": "Brōnŏtny kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.cyan": "Cyjanowy kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.gray": "Siwy kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.green": "Zielōny kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.light_blue": "Światłomŏdry kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.light_gray": "Światłosiwy kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.lime": "Limōnkowy kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.magenta": "Purpurowy kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.orange": "Ôranżowy kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.pink": "Rōżowy kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.purple": "Flidrowy kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.red": "Czyrwōny kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.white": "Bioły kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_left.yellow": "Żōłty kośny lewy spodni połek", "block.minecraft.banner.diagonal_up_right.black": "Czŏrny kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.blue": "Mŏdry kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.brown": "Brōnŏtny kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.cyan": "Cyjanowy kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.gray": "Siwy kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.green": "Zielōny kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.light_blue": "Światłomŏdry kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.light_gray": "Światłosiwy kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.lime": "Limōnkowy kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.magenta": "Purpurowy kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.orange": "Ôranżowy kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.pink": "Rōżowy kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.purple": "Lilowy kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.red": "Czyrwōny kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.white": "Bioły kośny prawy spodni połek", "block.minecraft.banner.diagonal_up_right.yellow": "Żōłty kośny prawy spodni połek", "block.minecraft.banner.flow.black": "Czŏrny wir", "block.minecraft.banner.flow.blue": "Modry wir", "block.minecraft.banner.flow.brown": "Brōnŏtny wir", "block.minecraft.banner.flow.cyan": "Cyjanowy wir", "block.minecraft.banner.flow.gray": "Siwy wir", "block.minecraft.banner.flow.green": "<PERSON><PERSON><PERSON><PERSON> wir", "block.minecraft.banner.flow.light_blue": "Światłomodry wir", "block.minecraft.banner.flow.light_gray": "Światłosiwy wir", "block.minecraft.banner.flow.lime": "Limōnkowy wir", "block.minecraft.banner.flow.magenta": "Purpurowy wir", "block.minecraft.banner.flow.orange": "Ôranżowy wir", "block.minecraft.banner.flow.pink": "Rōżowy wir", "block.minecraft.banner.flow.purple": "Flid<PERSON>y wir", "block.minecraft.banner.flow.red": "Czyrwō<PERSON> wir", "block.minecraft.banner.flow.white": "Bioły wir", "block.minecraft.banner.flow.yellow": "Żōłty wir", "block.minecraft.banner.flower.black": "Czŏrny kwiŏtek", "block.minecraft.banner.flower.blue": "<PERSON><PERSON>y kwiŏtek", "block.minecraft.banner.flower.brown": "Brōnŏtny kwiŏtek", "block.minecraft.banner.flower.cyan": "Cyjanowy kwiŏtek", "block.minecraft.banner.flower.gray": "<PERSON>wy k<PERSON>", "block.minecraft.banner.flower.green": "Zielōny kwiŏtek", "block.minecraft.banner.flower.light_blue": "Światłomodry kwiŏtek", "block.minecraft.banner.flower.light_gray": "Światłosiwy kwiŏtek", "block.minecraft.banner.flower.lime": "Limōnkowy kwiŏtek", "block.minecraft.banner.flower.magenta": "Purpurowy kwiŏtek", "block.minecraft.banner.flower.orange": "Ôranżowy kwiŏtek", "block.minecraft.banner.flower.pink": "Rōżowy kwiŏtek", "block.minecraft.banner.flower.purple": "Flidrowy kwiŏtek", "block.minecraft.banner.flower.red": "Czyrwōny kwiŏtek", "block.minecraft.banner.flower.white": "Bioły kwiŏtek", "block.minecraft.banner.flower.yellow": "Żōłty kwiŏtek", "block.minecraft.banner.globe.black": "Czŏrnŏ ziymiokostka", "block.minecraft.banner.globe.blue": "Modrŏ z<PERSON>ym<PERSON>tka", "block.minecraft.banner.globe.brown": "Brōnŏtnŏ ziymiokostka", "block.minecraft.banner.globe.cyan": "Cyjanowŏ ziymiokostka", "block.minecraft.banner.globe.gray": "Siwŏ ziymiokostka", "block.minecraft.banner.globe.green": "Zielōnŏ ziymiokostka", "block.minecraft.banner.globe.light_blue": "Światłomodrŏ ziymiokostka", "block.minecraft.banner.globe.light_gray": "Światłosiwŏ ziymiokostka", "block.minecraft.banner.globe.lime": "Limōnkowŏ ziymiokostka", "block.minecraft.banner.globe.magenta": "Purpurowŏ ziymiokostka", "block.minecraft.banner.globe.orange": "Ôranżowŏ ziymiokostka", "block.minecraft.banner.globe.pink": "Rōżowŏ ziymiokostka", "block.minecraft.banner.globe.purple": "Flidrowŏ ziymokostka", "block.minecraft.banner.globe.red": "Czyrwōnŏ ziymiokostka", "block.minecraft.banner.globe.white": "Biołŏ ziymiokostka", "block.minecraft.banner.globe.yellow": "Żōłtŏ ziymiokostka", "block.minecraft.banner.gradient.black": "Czŏrny gradiynt", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.brown": "Brōnŏtny gradiynt", "block.minecraft.banner.gradient.cyan": "Cyjanowy gradiynt", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON> gradi<PERSON>", "block.minecraft.banner.gradient.green": "<PERSON>iel<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.gradient.light_blue": "Światłomodry gradiynt", "block.minecraft.banner.gradient.light_gray": "Światłosiwy gradiynt", "block.minecraft.banner.gradient.lime": "Limōnkowy gradiynt", "block.minecraft.banner.gradient.magenta": "Purpurowy gradiynt", "block.minecraft.banner.gradient.orange": "Ôranżowy gradi<PERSON>t", "block.minecraft.banner.gradient.pink": "Rōżowy gradiynt", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON><PERSON><PERSON> grad<PERSON>", "block.minecraft.banner.gradient.red": "Czyrwōny gradiynt", "block.minecraft.banner.gradient.white": "Bioły gradiynt", "block.minecraft.banner.gradient.yellow": "Żōłty gradiynt", "block.minecraft.banner.gradient_up.black": "Czŏrny ôbrōcōny g<PERSON>i<PERSON>", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.brown": "Brōnŏtny ôbrōcōny gradiynt", "block.minecraft.banner.gradient_up.cyan": "Cyjanowy ôbrōcōny gradiynt", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.gradient_up.green": "Zielōny ôbrōcōny gradi<PERSON>t", "block.minecraft.banner.gradient_up.light_blue": "Świałomodry ôbrōcōny gradiynt", "block.minecraft.banner.gradient_up.light_gray": "Światłosiwy ôbrōcōny gradiynt", "block.minecraft.banner.gradient_up.lime": "<PERSON><PERSON><PERSON><PERSON>rōcō<PERSON> gradi<PERSON>", "block.minecraft.banner.gradient_up.magenta": "Purpurowy ôbrōcōny gradiynt", "block.minecraft.banner.gradient_up.orange": "Ôranżowy ô<PERSON><PERSON><PERSON><PERSON><PERSON> grad<PERSON>t", "block.minecraft.banner.gradient_up.pink": "Rōżowy ôbrōcōny gradiynt", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.gradient_up.red": "Czyrwōny ôbrōcōny gradiynt", "block.minecraft.banner.gradient_up.white": "Bioły ôbrōcōny gradiynt", "block.minecraft.banner.gradient_up.yellow": "Żōłty ôbrōcōny gradiynt", "block.minecraft.banner.guster.black": "Czŏrny wiytrzik", "block.minecraft.banner.guster.blue": "Modry wiytrzik", "block.minecraft.banner.guster.brown": "Brōnŏtny wiytrzik", "block.minecraft.banner.guster.cyan": " Cyjanowy wiytrzik", "block.minecraft.banner.guster.gray": "Siwy wiytrzik", "block.minecraft.banner.guster.green": "Zielōny wiytrzik", "block.minecraft.banner.guster.light_blue": "Światłomodry wiytrzik", "block.minecraft.banner.guster.light_gray": "Światłosiwy wiytrzik", "block.minecraft.banner.guster.lime": "Limōnkowy wiytrzik", "block.minecraft.banner.guster.magenta": "Purpurowy wiytrzik", "block.minecraft.banner.guster.orange": "Ôranżowy wiytrzik", "block.minecraft.banner.guster.pink": "Rōżowy wiytrzik", "block.minecraft.banner.guster.purple": "Flidrowy wiytrzik", "block.minecraft.banner.guster.red": "Czyrwōny wiytrzik", "block.minecraft.banner.guster.white": "Bioły wiytrzik", "block.minecraft.banner.guster.yellow": "Żōłty wiytrzik", "block.minecraft.banner.half_horizontal.black": "Czŏrny wiyrchni połek", "block.minecraft.banner.half_horizontal.blue": "Modry wiyrchni połek", "block.minecraft.banner.half_horizontal.brown": "Brōnŏtny wiyrchni połek", "block.minecraft.banner.half_horizontal.cyan": "Cyjanowy wiyrchni połek", "block.minecraft.banner.half_horizontal.gray": "Siwy wiyrchni połek", "block.minecraft.banner.half_horizontal.green": "Zielōny wiyrchni połek", "block.minecraft.banner.half_horizontal.light_blue": "Światłomodry wiyrchni połek", "block.minecraft.banner.half_horizontal.light_gray": "Światłosiwy wiyrchni połek", "block.minecraft.banner.half_horizontal.lime": "Limōnkowy wiyrchni połek", "block.minecraft.banner.half_horizontal.magenta": "Purpurowy wiyrchni połek", "block.minecraft.banner.half_horizontal.orange": "Ôranżowy wiyrchni połek", "block.minecraft.banner.half_horizontal.pink": "Rōżowy wiyrchni połek", "block.minecraft.banner.half_horizontal.purple": "Flidrowy wiyrchni połek", "block.minecraft.banner.half_horizontal.red": "Czyrwōny wiyrchni połek", "block.minecraft.banner.half_horizontal.white": "Bioły wiyrchni połek", "block.minecraft.banner.half_horizontal.yellow": "Żōłty wiyrchni połek", "block.minecraft.banner.half_horizontal_bottom.black": "Czŏrny spodni połek", "block.minecraft.banner.half_horizontal_bottom.blue": "Modry spodni połek", "block.minecraft.banner.half_horizontal_bottom.brown": "Brōnŏtny spodni połek", "block.minecraft.banner.half_horizontal_bottom.cyan": "Cyjanowy spodni połek", "block.minecraft.banner.half_horizontal_bottom.gray": "Siwy spodni połek", "block.minecraft.banner.half_horizontal_bottom.green": "Zielōny spodni połek", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Światłomodry spodni połek", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Światłosiwy spodni połek", "block.minecraft.banner.half_horizontal_bottom.lime": "Limōnkowy spodni połek", "block.minecraft.banner.half_horizontal_bottom.magenta": "Purpurowy spodni połek", "block.minecraft.banner.half_horizontal_bottom.orange": "Ôranżowy spodni połek", "block.minecraft.banner.half_horizontal_bottom.pink": "Rōżowy spodni połek", "block.minecraft.banner.half_horizontal_bottom.purple": "Flidrowy spodni połek", "block.minecraft.banner.half_horizontal_bottom.red": "Czyrwōny spodni połek", "block.minecraft.banner.half_horizontal_bottom.white": "Bioły spodni połek", "block.minecraft.banner.half_horizontal_bottom.yellow": "Żōłty spodni połek", "block.minecraft.banner.half_vertical.black": "Czŏrny lewy połek", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON><PERSON> lewy połek", "block.minecraft.banner.half_vertical.brown": "Brōnŏtny lewy połek", "block.minecraft.banner.half_vertical.cyan": "Cyjanowy lewy połek", "block.minecraft.banner.half_vertical.gray": "<PERSON><PERSON> lewy połek", "block.minecraft.banner.half_vertical.green": "Zielōny lewy połek", "block.minecraft.banner.half_vertical.light_blue": "Światłomodry lewy połek", "block.minecraft.banner.half_vertical.light_gray": "Światłosiwy lewy połek", "block.minecraft.banner.half_vertical.lime": "Limōnkowy lewy połek", "block.minecraft.banner.half_vertical.magenta": "Purpurowy lewy połek", "block.minecraft.banner.half_vertical.orange": "Ôranżowy lewy połek", "block.minecraft.banner.half_vertical.pink": "Rōżowy lewy połek", "block.minecraft.banner.half_vertical.purple": "Flidrowy lewy połek", "block.minecraft.banner.half_vertical.red": "Czyrwōny lewy połek", "block.minecraft.banner.half_vertical.white": "Bioły lewy połek", "block.minecraft.banner.half_vertical.yellow": "Żōłty lewy połek", "block.minecraft.banner.half_vertical_right.black": "Czŏrny prawy połek", "block.minecraft.banner.half_vertical_right.blue": "Modry prawy połek", "block.minecraft.banner.half_vertical_right.brown": "Brōnŏtny prawy połek", "block.minecraft.banner.half_vertical_right.cyan": "Cyjanowy prawy połek", "block.minecraft.banner.half_vertical_right.gray": "Siwy prawy połek", "block.minecraft.banner.half_vertical_right.green": "Zielōny prawy połek", "block.minecraft.banner.half_vertical_right.light_blue": "Światłomodry prawy połek", "block.minecraft.banner.half_vertical_right.light_gray": "Światłosiwy prawy połek", "block.minecraft.banner.half_vertical_right.lime": "Limōnkowy prawy połek", "block.minecraft.banner.half_vertical_right.magenta": "Purpurowy prawy połek", "block.minecraft.banner.half_vertical_right.orange": "Ôranżowy prawy połek", "block.minecraft.banner.half_vertical_right.pink": "Rōżowy prawy połek", "block.minecraft.banner.half_vertical_right.purple": "Flidrowy prawy połek", "block.minecraft.banner.half_vertical_right.red": "Czyrwōny prawy połek", "block.minecraft.banner.half_vertical_right.white": "Bioły prawy połek", "block.minecraft.banner.half_vertical_right.yellow": "Żōłty prawy połek", "block.minecraft.banner.mojang.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON>", "block.minecraft.banner.mojang.brown": "B<PERSON>n<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON>", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "Światłomo<PERSON>ś", "block.minecraft.banner.mojang.light_gray": "Światłosiwe coś", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.magenta": "Purpurowe coś", "block.minecraft.banner.mojang.orange": "Ôranżowe coś", "block.minecraft.banner.mojang.pink": "Rōżowe coś", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON><PERSON><PERSON> co<PERSON>", "block.minecraft.banner.mojang.red": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.white": "Biołe coś", "block.minecraft.banner.mojang.yellow": "Żōł<PERSON>", "block.minecraft.banner.piglin.black": "Czŏrny niurŏk", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.brown": "Brōnŏtny niurŏk", "block.minecraft.banner.piglin.cyan": "Cyjanowy niurŏk", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON>", "block.minecraft.banner.piglin.green": "Zielōny niurŏk", "block.minecraft.banner.piglin.light_blue": "Światłomodry niurŏk", "block.minecraft.banner.piglin.light_gray": "Światłosiwy niurŏk", "block.minecraft.banner.piglin.lime": "Limōnkowy niurŏk", "block.minecraft.banner.piglin.magenta": "Purpurowy niurŏk", "block.minecraft.banner.piglin.orange": "Ôranżowy niurŏk", "block.minecraft.banner.piglin.pink": "Rōżowy niurŏk", "block.minecraft.banner.piglin.purple": "Flidrowy niurŏk", "block.minecraft.banner.piglin.red": "Czyrwōny niurŏk", "block.minecraft.banner.piglin.white": "Bioły niurŏk", "block.minecraft.banner.piglin.yellow": "Żōłty niurŏk", "block.minecraft.banner.rhombus.black": "Czŏrny rōmb", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.brown": "Brōnŏtny rōmb", "block.minecraft.banner.rhombus.cyan": "Cyjanowy rōmb", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON> r<PERSON>", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.rhombus.light_blue": "Światłomodry rōmb", "block.minecraft.banner.rhombus.light_gray": "Światłosiwy rōmb", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON><PERSON> rō<PERSON>", "block.minecraft.banner.rhombus.magenta": "Purpurowy rōmb", "block.minecraft.banner.rhombus.orange": "Ôranżowy rōmb", "block.minecraft.banner.rhombus.pink": "Rōżowy rōmb", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>mb", "block.minecraft.banner.rhombus.red": "Czyrwō<PERSON> rōmb", "block.minecraft.banner.rhombus.white": "Bioły rōmb", "block.minecraft.banner.rhombus.yellow": "Żōłty rōmb", "block.minecraft.banner.skull.black": "Czŏrnŏ trōpiŏ szala", "block.minecraft.banner.skull.blue": "<PERSON><PERSON><PERSON> t<PERSON> szala", "block.minecraft.banner.skull.brown": "Brōnŏtnŏ trō<PERSON> szala", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON><PERSON> trō<PERSON> szala", "block.minecraft.banner.skull.gray": "<PERSON>w<PERSON> trōpi<PERSON> szala", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> trō<PERSON> szala", "block.minecraft.banner.skull.light_blue": "Światłomodrŏ trō<PERSON> szala", "block.minecraft.banner.skull.light_gray": "Światłosiwŏ trōpi<PERSON> szala", "block.minecraft.banner.skull.lime": "Limōnkowŏ trōpi<PERSON> szala", "block.minecraft.banner.skull.magenta": "Purpurow<PERSON> trōpi<PERSON> szala", "block.minecraft.banner.skull.orange": "Ôranżowŏ t<PERSON><PERSON><PERSON> szala", "block.minecraft.banner.skull.pink": "Rōżowŏ trōpi<PERSON> szala", "block.minecraft.banner.skull.purple": "<PERSON><PERSON><PERSON><PERSON> trōpi<PERSON> szala", "block.minecraft.banner.skull.red": "Czyrwōn<PERSON> trō<PERSON> szala", "block.minecraft.banner.skull.white": "Biołŏ t<PERSON> szala", "block.minecraft.banner.skull.yellow": "Żōłtŏ trō<PERSON> szala", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.brown": "B<PERSON>n<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.light_blue": "Światłomodre <PERSON>", "block.minecraft.banner.small_stripes.light_gray": "Światłosiwe pŏski", "block.minecraft.banner.small_stripes.lime": "Lim<PERSON>nko<PERSON> p<PERSON>ski", "block.minecraft.banner.small_stripes.magenta": "Purpurowe pŏski", "block.minecraft.banner.small_stripes.orange": "Ôranżowe pŏski", "block.minecraft.banner.small_stripes.pink": "Rōżowe pŏski", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.red": "<PERSON>zyr<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.white": "Biołe pŏski", "block.minecraft.banner.small_stripes.yellow": "Żōłte pŏski", "block.minecraft.banner.square_bottom_left.black": "Czŏrnŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.blue": "Modrŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.brown": "Brōnŏtnŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.cyan": "Cyjanowŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.gray": "Siwŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.green": "Zielōnŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.light_blue": "Światłomodrŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.light_gray": "Światłosiwŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.lime": "Limōnkowŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.magenta": "Purpurowŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.orange": "Ôranżowŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.pink": "Rōżowŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.purple": "Flidrowŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.red": "Czyrwōnŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.white": "Biołŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_left.yellow": "Żōłtŏ spodniŏ lewŏ eka", "block.minecraft.banner.square_bottom_right.black": "Czornŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.blue": "Modrŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.brown": "Brōnŏtnŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.cyan": "Cyjanowŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.gray": "Siwŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.green": "Zielōnŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.light_blue": "Światłomodrŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.light_gray": "Światłosiwŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.lime": "Limōnkowŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.magenta": "Purpurowŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.orange": "Ôranżowŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.pink": "Rōżowŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.purple": "Flidrowŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.red": "Czyrwōnŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.white": "Biołŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_bottom_right.yellow": "Żōłtŏ spodniŏ prawŏ eka", "block.minecraft.banner.square_top_left.black": "Czŏrnŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.blue": "Modrŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.brown": "Brōnŏtnŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.cyan": "Cyjanowŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.gray": "Siwŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.green": "Zielōnŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.light_blue": "Światłomodrŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.light_gray": "Światłosiwŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.lime": "Limōnkowŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.magenta": "Purpurowŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.orange": "Ôranżowŏ wi<PERSON><PERSON><PERSON><PERSON> lewŏ eka", "block.minecraft.banner.square_top_left.pink": "Rōżowŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.purple": "Flidrowŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.red": "Czyrwōnŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.white": "Biołŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_left.yellow": "Żōłtŏ wiyrchniŏ lewŏ eka", "block.minecraft.banner.square_top_right.black": "Czŏrnŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.blue": "Modrŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.brown": "Brōnŏtnŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.cyan": "Cyjanowŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.gray": "Siwŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.green": "Zielōnŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.light_blue": "Światłomodrŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.light_gray": "Światłosiwŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.lime": "Limōnkowŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.magenta": "Purpurowŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.orange": "Ôranżowŏ wiyr<PERSON><PERSON><PERSON> prawŏ eka", "block.minecraft.banner.square_top_right.pink": "Rōżowŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.purple": "Flidrowŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.red": "Czyrwōnŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.white": "Biołŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.square_top_right.yellow": "Żōłtŏ wiyrchniŏ prawŏ eka", "block.minecraft.banner.straight_cross.black": "Czŏrny krziż", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON><PERSON>ż", "block.minecraft.banner.straight_cross.brown": "Brōnŏtny krziż", "block.minecraft.banner.straight_cross.cyan": "Cyjanowy krziż", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.green": "Zielōny krziż", "block.minecraft.banner.straight_cross.light_blue": "Światłomodry krziż", "block.minecraft.banner.straight_cross.light_gray": "Światłosiwy krziż", "block.minecraft.banner.straight_cross.lime": "Limōnkowy krziż", "block.minecraft.banner.straight_cross.magenta": "Purpurowy krziż", "block.minecraft.banner.straight_cross.orange": "Ôranżowy krziż", "block.minecraft.banner.straight_cross.pink": "Rōżowy krziż", "block.minecraft.banner.straight_cross.purple": "Flidrowy krziż", "block.minecraft.banner.straight_cross.red": "Czyrwōny krziż", "block.minecraft.banner.straight_cross.white": "Bioły krziż", "block.minecraft.banner.straight_cross.yellow": "Żōłty krziż", "block.minecraft.banner.stripe_bottom.black": "Czŏrny spodni pŏs", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON><PERSON> spodni pŏs", "block.minecraft.banner.stripe_bottom.brown": "Brōnŏtny spodni pŏs", "block.minecraft.banner.stripe_bottom.cyan": "Cyjanowy spodni pŏs", "block.minecraft.banner.stripe_bottom.gray": "<PERSON>wy spodni pŏs", "block.minecraft.banner.stripe_bottom.green": "Zielōny spodni pŏs", "block.minecraft.banner.stripe_bottom.light_blue": "Światłomodry spodni pŏs", "block.minecraft.banner.stripe_bottom.light_gray": "Światłosiwy spodni pŏs", "block.minecraft.banner.stripe_bottom.lime": "Limōnkowy spodni pŏs", "block.minecraft.banner.stripe_bottom.magenta": "Purpurowy spodni pŏs", "block.minecraft.banner.stripe_bottom.orange": "Ôranżowy spodni pŏs", "block.minecraft.banner.stripe_bottom.pink": "Rōżowy spodni pŏs", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON><PERSON>y spodni pŏs", "block.minecraft.banner.stripe_bottom.red": "Czyrwōny spodni pŏs", "block.minecraft.banner.stripe_bottom.white": "Bioły spodni pŏs", "block.minecraft.banner.stripe_bottom.yellow": "Żōłty spodni pŏs", "block.minecraft.banner.stripe_center.black": "Czŏrny piōnowy pŏsek", "block.minecraft.banner.stripe_center.blue": "Modry piōnowy pŏsek", "block.minecraft.banner.stripe_center.brown": "Brōnŏtny piōnowy pŏsek", "block.minecraft.banner.stripe_center.cyan": "Cyjanowy piōnowy pŏsek", "block.minecraft.banner.stripe_center.gray": "Siwy piōnowy pŏsek", "block.minecraft.banner.stripe_center.green": "Zielōny piōnowy pŏsek", "block.minecraft.banner.stripe_center.light_blue": "Światłomodry piōnowy pŏsek", "block.minecraft.banner.stripe_center.light_gray": "Światłosiwy piōnowy pŏsek", "block.minecraft.banner.stripe_center.lime": "Limōnkowy piōnowy pŏsek", "block.minecraft.banner.stripe_center.magenta": "Purpurowy piōnowy pŏsek", "block.minecraft.banner.stripe_center.orange": "Ôranżowy piōnowy p<PERSON>k", "block.minecraft.banner.stripe_center.pink": "Rōżowy piōnowy pŏsek", "block.minecraft.banner.stripe_center.purple": "Flidrowy piōnowy pŏsek", "block.minecraft.banner.stripe_center.red": "Czyrwōny piōnowy pŏsek", "block.minecraft.banner.stripe_center.white": "Bioły piōnowy pŏsek", "block.minecraft.banner.stripe_center.yellow": "Żōłty piōnowy pŏsek", "block.minecraft.banner.stripe_downleft.black": "Czŏrny kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.blue": "Modry kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.brown": "Brōnŏtny kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.cyan": "Cyjanowy kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.gray": "Siwy kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.green": "Zielōny kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.light_blue": "Światłomodry kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.light_gray": "Światłosiwy kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.lime": "Limōnkowy kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.magenta": "Purpurowy kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.orange": "Ôranżowy kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.pink": "Rōżowy kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.purple": "Flidrowy kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.red": "Czyrwōny kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.white": "Bioły kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downleft.yellow": "Żōłty kośny lewy spodni pŏsek", "block.minecraft.banner.stripe_downright.black": "Czŏrny kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.blue": "Modry kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.brown": "Brōnŏtny kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.cyan": "Cyjanowy kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.gray": "Siwy kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.green": "Zielōny kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.light_blue": "Światłomodry kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.light_gray": "Światłosiwy kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.lime": "Limōnkowy kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.magenta": "Purpurowy kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.orange": "Ôranżowy kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.pink": "Rōżowy kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.purple": "Lilowy kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.red": "Czyrwōny kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.white": "Bioły kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_downright.yellow": "Żōłty kośny prawy spodni pŏsek", "block.minecraft.banner.stripe_left.black": "Czŏrny lewy pŏsek", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON><PERSON> lewy p<PERSON>", "block.minecraft.banner.stripe_left.brown": "Brōnŏtny lewy pŏ<PERSON>k", "block.minecraft.banner.stripe_left.cyan": "Cyjanowy lewy pŏsek", "block.minecraft.banner.stripe_left.gray": "<PERSON>wy lewy p<PERSON>k", "block.minecraft.banner.stripe_left.green": "Zielōny lewy p<PERSON>", "block.minecraft.banner.stripe_left.light_blue": "Światłomodry lewy pŏsek", "block.minecraft.banner.stripe_left.light_gray": "Światłosiwy lewy pŏsek", "block.minecraft.banner.stripe_left.lime": "Limōnkowy lewy p<PERSON>k", "block.minecraft.banner.stripe_left.magenta": "Purpurowy lewy pŏsek", "block.minecraft.banner.stripe_left.orange": "Ôranżowy lewy pŏ<PERSON>k", "block.minecraft.banner.stripe_left.pink": "Rōżowy lewy pŏsek", "block.minecraft.banner.stripe_left.purple": "Flidrowy lewy p<PERSON>k", "block.minecraft.banner.stripe_left.red": "Czyrwōny lewy pŏ<PERSON>k", "block.minecraft.banner.stripe_left.white": "Bioły lewy pŏsek", "block.minecraft.banner.stripe_left.yellow": "Żōłty lewy pŏsek", "block.minecraft.banner.stripe_middle.black": "Czŏrny poziōmy pŏsek", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON> poz<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "Brōnŏtny poziōmy pŏsek", "block.minecraft.banner.stripe_middle.cyan": "Cyjanowy poziōmy pŏsek", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON> poz<PERSON>", "block.minecraft.banner.stripe_middle.green": "Zielōny poziōmy p<PERSON>k", "block.minecraft.banner.stripe_middle.light_blue": "Światłomodry poziōmy pŏsek", "block.minecraft.banner.stripe_middle.light_gray": "Światłosiwy poziōmy pŏsek", "block.minecraft.banner.stripe_middle.lime": "Limōnkowy poziōmy p<PERSON>k", "block.minecraft.banner.stripe_middle.magenta": "Purpurowy poziōmy pŏsek", "block.minecraft.banner.stripe_middle.orange": "Ôranżowy poziōmy pŏsek", "block.minecraft.banner.stripe_middle.pink": "Rōżowy poziōmy pŏsek", "block.minecraft.banner.stripe_middle.purple": "Flidrowy poziōmy p<PERSON>k", "block.minecraft.banner.stripe_middle.red": "Czyrwōny poziōmy pŏsek", "block.minecraft.banner.stripe_middle.white": "Bioły poziōmy pŏsek", "block.minecraft.banner.stripe_middle.yellow": "Żōłty poziōmy pŏsek", "block.minecraft.banner.stripe_right.black": "Czŏrny prawy pŏsek", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON><PERSON> prawy p<PERSON>k", "block.minecraft.banner.stripe_right.brown": "Brōnŏtny prawy pŏsek", "block.minecraft.banner.stripe_right.cyan": "Cyjanowy prawy pŏsek", "block.minecraft.banner.stripe_right.gray": "<PERSON>wy prawy pŏ<PERSON>k", "block.minecraft.banner.stripe_right.green": "Zielōny prawy pŏ<PERSON>k", "block.minecraft.banner.stripe_right.light_blue": "Światłomodry prawy pŏsek", "block.minecraft.banner.stripe_right.light_gray": "Światłosiwy prawy pŏsek", "block.minecraft.banner.stripe_right.lime": "Limōnkowy prawy pŏsek", "block.minecraft.banner.stripe_right.magenta": "Purpurowy prawy pŏsek", "block.minecraft.banner.stripe_right.orange": "Ôranżowy prawy pŏsek", "block.minecraft.banner.stripe_right.pink": "Rōżowy prawy pŏsek", "block.minecraft.banner.stripe_right.purple": "Flidrowy prawy pŏ<PERSON>k", "block.minecraft.banner.stripe_right.red": "Czyrwōny prawy pŏ<PERSON>k", "block.minecraft.banner.stripe_right.white": "Bioły prawy pŏsek", "block.minecraft.banner.stripe_right.yellow": "Żōłty prawy pŏsek", "block.minecraft.banner.stripe_top.black": "Czŏrny wiyrchni pŏs", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON><PERSON> wi<PERSON><PERSON><PERSON> pŏs", "block.minecraft.banner.stripe_top.brown": "Brōnŏtny wiyrchni pŏs", "block.minecraft.banner.stripe_top.cyan": "Cyjanowy wiyrchni pŏs", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON> wiyrchni pŏs", "block.minecraft.banner.stripe_top.green": "Zielōny wiyrchni pŏs", "block.minecraft.banner.stripe_top.light_blue": "Światłomodry wiyrchni pŏs", "block.minecraft.banner.stripe_top.light_gray": "Światłosiwy wiyrchni pŏs", "block.minecraft.banner.stripe_top.lime": "Limōnkowy wiyrchni pŏs", "block.minecraft.banner.stripe_top.magenta": "Purpurowy wiyrchni pŏs", "block.minecraft.banner.stripe_top.orange": "Ôranżowy wiyrchni pŏs", "block.minecraft.banner.stripe_top.pink": "Rōżowy wiyrchni pŏs", "block.minecraft.banner.stripe_top.purple": "Flidrowy wiyrchni pŏs", "block.minecraft.banner.stripe_top.red": "Czyrwōny wiyrchni pŏs", "block.minecraft.banner.stripe_top.white": "Bioły wiyrchni pŏs", "block.minecraft.banner.stripe_top.yellow": "Żōłty wiyrch", "block.minecraft.banner.triangle_bottom.black": "Czŏrny spodni sztachel", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON><PERSON> spodni sztachel", "block.minecraft.banner.triangle_bottom.brown": "Brōnŏtny spodni sztachel", "block.minecraft.banner.triangle_bottom.cyan": "Cyjanowy spodni sztachel", "block.minecraft.banner.triangle_bottom.gray": "Siwy spodni sztachel", "block.minecraft.banner.triangle_bottom.green": "Zielōny spodni sztachel", "block.minecraft.banner.triangle_bottom.light_blue": "Światłomodry spodni sztachel", "block.minecraft.banner.triangle_bottom.light_gray": "Światłosiwy spodni sztachel", "block.minecraft.banner.triangle_bottom.lime": "Limōnkowy spodni sztachel", "block.minecraft.banner.triangle_bottom.magenta": "Purpurowy spodni sztachel", "block.minecraft.banner.triangle_bottom.orange": "Ôranżowy spodni sztachel", "block.minecraft.banner.triangle_bottom.pink": "Rōżowy spodni sztachel", "block.minecraft.banner.triangle_bottom.purple": "<PERSON>lid<PERSON>y spodni sztachel", "block.minecraft.banner.triangle_bottom.red": "Czyrwōny spodni sztachel", "block.minecraft.banner.triangle_bottom.white": "Bioły spodni sztachel", "block.minecraft.banner.triangle_bottom.yellow": "Żōłty spodni sztachel", "block.minecraft.banner.triangle_top.black": "Czŏrny wiyrchni sztachel", "block.minecraft.banner.triangle_top.blue": "<PERSON>dry wiyr<PERSON>ni sztachel", "block.minecraft.banner.triangle_top.brown": "Brōnŏtny wiyrchni sztachel", "block.minecraft.banner.triangle_top.cyan": "Cyjanowy wiyrchni sztachel", "block.minecraft.banner.triangle_top.gray": "Siwy wiyrchni sztachel", "block.minecraft.banner.triangle_top.green": "Zielōny wiyrchni sztachel", "block.minecraft.banner.triangle_top.light_blue": "Światłomodry wiyrchni sztachel", "block.minecraft.banner.triangle_top.light_gray": "Światłosiwy wiyrchni sztachel", "block.minecraft.banner.triangle_top.lime": "Limōnkowy wiyrchni sztachel", "block.minecraft.banner.triangle_top.magenta": "Purpurowy wiyrchni sztachel", "block.minecraft.banner.triangle_top.orange": "Ôranżowy wi<PERSON><PERSON>ni s<PERSON>tachel", "block.minecraft.banner.triangle_top.pink": "Rōżowy wiyrchni sztachel", "block.minecraft.banner.triangle_top.purple": "Flidrowy wiyrchni sztachel", "block.minecraft.banner.triangle_top.red": "Czyrwōny wiyrchni sztachel", "block.minecraft.banner.triangle_top.white": "Bioły wiyrchni sztachel", "block.minecraft.banner.triangle_top.yellow": "Żōłty wiyrchni sztachel", "block.minecraft.banner.triangles_bottom.black": "Czŏrne spodnie sztachle", "block.minecraft.banner.triangles_bottom.blue": "Modre spodnie sztachle", "block.minecraft.banner.triangles_bottom.brown": "Brōnŏtne spodnie sztachle", "block.minecraft.banner.triangles_bottom.cyan": "Cyjanowe spodnie sztachle", "block.minecraft.banner.triangles_bottom.gray": "Siwe spodnie sztachle", "block.minecraft.banner.triangles_bottom.green": "Zielōne spodnie sztachle", "block.minecraft.banner.triangles_bottom.light_blue": "Światłomodre spodnie sztachle", "block.minecraft.banner.triangles_bottom.light_gray": "Światłosiwe spodnie sztachle", "block.minecraft.banner.triangles_bottom.lime": "Limōnkowe spodnie sztachle", "block.minecraft.banner.triangles_bottom.magenta": "Purpurowe spodnie sztachle", "block.minecraft.banner.triangles_bottom.orange": "Ôranżowe spodnie sztachle", "block.minecraft.banner.triangles_bottom.pink": "Rōżowe spodnie sztachle", "block.minecraft.banner.triangles_bottom.purple": "Flidrowe spodnie sztachle", "block.minecraft.banner.triangles_bottom.red": "Czyrwōne spodnie sztachle", "block.minecraft.banner.triangles_bottom.white": "Biołe spodnie sztachle", "block.minecraft.banner.triangles_bottom.yellow": "Żōłte spodnie sztachle", "block.minecraft.banner.triangles_top.black": "Czŏrne wiyrchnie sztachle", "block.minecraft.banner.triangles_top.blue": "Modre wiyrchnie sztachle", "block.minecraft.banner.triangles_top.brown": "Brōnŏtne wiyrchnie sztachle", "block.minecraft.banner.triangles_top.cyan": "Cyjanowe wiyrchnie sztachle", "block.minecraft.banner.triangles_top.gray": "Siwe wiyrchnie sztachle", "block.minecraft.banner.triangles_top.green": "Zielōne wiyrchnie sztachle", "block.minecraft.banner.triangles_top.light_blue": "Światłomodre wiyrchnie sztachle", "block.minecraft.banner.triangles_top.light_gray": "Światłosiwe wiyrchnie sztachle", "block.minecraft.banner.triangles_top.lime": "Limōnkowe wiyrchnie sztachle", "block.minecraft.banner.triangles_top.magenta": "Purpurowe wiyrchnie sztachle", "block.minecraft.banner.triangles_top.orange": "Ôranżowe wi<PERSON>chnie sztachle", "block.minecraft.banner.triangles_top.pink": "Rōżowe wiyrchnie sztachle", "block.minecraft.banner.triangles_top.purple": "Flidrowe wiyrchnie sztachle", "block.minecraft.banner.triangles_top.red": "Czyrwōne wiyrchnie sztachle", "block.minecraft.banner.triangles_top.white": "Biołe wiyrchnie sztachle", "block.minecraft.banner.triangles_top.yellow": "Żōłte wiyrchnie sztachle", "block.minecraft.barrel": "Faska", "block.minecraft.barrier": "Bariyra", "block.minecraft.basalt": "Bazalt", "block.minecraft.beacon": "<PERSON><PERSON>", "block.minecraft.beacon.primary": "Grōntn<PERSON>", "block.minecraft.beacon.secondary": "Hilfowŏ siyła", "block.minecraft.bed.no_sleep": "Ś<PERSON><PERSON> spać yno bez noc a bez burzã", "block.minecraft.bed.not_safe": "<PERSON><PERSON> terŏ<PERSON> spa<PERSON>; wele ciebie sōm be<PERSON>", "block.minecraft.bed.obstructed": "To łōżko je zagrodzōne", "block.minecraft.bed.occupied": "To łōżko je zajmnyte", "block.minecraft.bed.too_far_away": "<PERSON>y śmisz terŏzki spać: ł<PERSON><PERSON><PERSON> je za daleko", "block.minecraft.bedrock": "Zŏl", "block.minecraft.bee_nest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beehive": "<PERSON><PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON>", "block.minecraft.bell": "Zwōn", "block.minecraft.big_dripleaf": "<PERSON><PERSON><PERSON>zam<PERSON>k", "block.minecraft.big_dripleaf_stem": "Sztyngel wielgigo czampiōnka", "block.minecraft.birch_button": "Brzizkowy knefel", "block.minecraft.birch_door": "Brzizkowe dźwiyrze", "block.minecraft.birch_fence": "Brzizkowy płot", "block.minecraft.birch_fence_gate": "Brzizkowŏ wrōtka", "block.minecraft.birch_hanging_sign": "Brzizkowy szild", "block.minecraft.birch_leaves": "Liście brzizki", "block.minecraft.birch_log": "Sztam brzizki", "block.minecraft.birch_planks": "Brzizkowe brety", "block.minecraft.birch_pressure_plate": "Brzizkowŏ drukplata", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON>a br<PERSON>zki", "block.minecraft.birch_sign": "Brzizkowy znak", "block.minecraft.birch_slab": "Brzizkowŏ fliza", "block.minecraft.birch_stairs": "Brzizkowe słody", "block.minecraft.birch_trapdoor": "Brzizkowŏ szłapa", "block.minecraft.birch_wall_hanging_sign": "Brzizkowy ściynny szild", "block.minecraft.birch_wall_sign": "Brzizkowy ściynny znak", "block.minecraft.birch_wood": "Brzizkowe drzewo", "block.minecraft.black_banner": "Czŏrnŏ fana", "block.minecraft.black_bed": "Cz<PERSON>rne <PERSON>", "block.minecraft.black_candle": "Czŏrnŏ świyczka", "block.minecraft.black_candle_cake": "Torta ze czŏrnōm świyczkōm", "block.minecraft.black_carpet": "Czŏrny tepich", "block.minecraft.black_concrete": "Czŏrny betōn", "block.minecraft.black_concrete_powder": "Czŏrny cymynt", "block.minecraft.black_glazed_terracotta": "Czŏrnŏ glaziyrowanŏ kamiōnka", "block.minecraft.black_shulker_box": "Czŏrnŏ shulkerowŏ kastla", "block.minecraft.black_stained_glass": "Czŏrne szkło", "block.minecraft.black_stained_glass_pane": "Czŏrnŏ szklannŏ tŏfla", "block.minecraft.black_terracotta": "Czŏrnŏ kamiōnka", "block.minecraft.black_wool": "Czŏrnŏ wołna", "block.minecraft.blackstone": "Czŏrny kamiyń", "block.minecraft.blackstone_slab": "Czŏrnokamiynnŏ fliza", "block.minecraft.blackstone_stairs": "Czŏrnokamiynne słody", "block.minecraft.blackstone_wall": "Czŏrnokamiynny murek", "block.minecraft.blast_furnace": "<PERSON><PERSON>", "block.minecraft.blue_banner": "<PERSON><PERSON><PERSON>a", "block.minecraft.blue_bed": "<PERSON><PERSON>", "block.minecraft.blue_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle_cake": "Torta ze modrōm świyczkōm", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON> te<PERSON>h", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_glazed_terracotta": "Modrŏ gla<PERSON>yrowanŏ kamiōnka", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_shulker_box": "Modr<PERSON> shulkerowŏ kastla", "block.minecraft.blue_stained_glass": "<PERSON><PERSON>", "block.minecraft.blue_stained_glass_pane": "Modr<PERSON> s<PERSON>klannŏ tŏfla", "block.minecraft.blue_terracotta": "<PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.blue_wool": "Modrŏ wołna", "block.minecraft.bone_block": "Blok kostyry", "block.minecraft.bookshelf": "Bicherszrank", "block.minecraft.brain_coral": "Mōzgowy korŏl", "block.minecraft.brain_coral_block": "Blok mōzgowego korŏlu", "block.minecraft.brain_coral_fan": "Mōzgowŏ gorgonia", "block.minecraft.brain_coral_wall_fan": "Ściynnŏ mōzgowŏ gorgonia", "block.minecraft.brewing_stand": "Sztynder ku warzyniu", "block.minecraft.brick_slab": "Ceglann<PERSON> fliza", "block.minecraft.brick_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_wall": "Ce<PERSON>lan<PERSON> murek", "block.minecraft.bricks": "Cegły", "block.minecraft.brown_banner": "Brōnŏtnŏ fana", "block.minecraft.brown_bed": "Brōnŏtne <PERSON>", "block.minecraft.brown_candle": "Brōnŏtnŏ świyczka", "block.minecraft.brown_candle_cake": "Torta ze brōnŏtnōm świyczkōm", "block.minecraft.brown_carpet": "Brōnŏtny te<PERSON>", "block.minecraft.brown_concrete": "Brōnŏ<PERSON>ny bet<PERSON>n", "block.minecraft.brown_concrete_powder": "Brōnŏ<PERSON>ny cymynt", "block.minecraft.brown_glazed_terracotta": "Brōnŏtnŏ glaziyrowanŏ kamiōnka", "block.minecraft.brown_mushroom": "Prawŏk", "block.minecraft.brown_mushroom_block": "Blok prawŏka", "block.minecraft.brown_shulker_box": "Brōnŏtnŏ shulkerowŏ kastla", "block.minecraft.brown_stained_glass": "Brōnŏtne szkło", "block.minecraft.brown_stained_glass_pane": "Brōnŏtnŏ szklannŏ tŏfla", "block.minecraft.brown_terracotta": "Brōnŏtnŏ kamiōnka", "block.minecraft.brown_wool": "Brōnŏtnŏ wołna", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "Bōmbelkowy korŏl", "block.minecraft.bubble_coral_block": "Blok bōmbelkowego korŏlu", "block.minecraft.bubble_coral_fan": "Bōmbelkowŏ gorgonia", "block.minecraft.bubble_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON> bōmbelkowŏ gorgonia", "block.minecraft.budding_amethyst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ametyst", "block.minecraft.bush": "Krzŏk", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Kwiŏt kaktusa", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Kalcyt", "block.minecraft.calibrated_sculk_sensor": "Skalibrowany sculkowy czujnik", "block.minecraft.campfire": "Fojera", "block.minecraft.candle": "Świyczka", "block.minecraft.candle_cake": "Torta ze świyczkōm", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "<PERSON><PERSON><PERSON> kartografa", "block.minecraft.carved_pumpkin": "Wystruganŏ bania", "block.minecraft.cauldron": "<PERSON><PERSON>", "block.minecraft.cave_air": "Jaskiniowy luft", "block.minecraft.cave_vines": "Jaskiniowe kleterflance", "block.minecraft.cave_vines_plant": "Jaskiniowe kleterflance", "block.minecraft.chain": "<PERSON><PERSON>", "block.minecraft.chain_command_block": "Ketowy k<PERSON>ndowy blok", "block.minecraft.cherry_button": "Trześniowy knefel", "block.minecraft.cherry_door": "Trześniowe dźwiyrze", "block.minecraft.cherry_fence": "Trześniowy płot", "block.minecraft.cherry_fence_gate": "Trześniowŏ wrōtka", "block.minecraft.cherry_hanging_sign": "Trześniowy szild", "block.minecraft.cherry_leaves": "Liście trześnie", "block.minecraft.cherry_log": "Sztam trześnie", "block.minecraft.cherry_planks": "Trześniowe brety", "block.minecraft.cherry_pressure_plate": "Trześniowŏ drukplata", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON>a trześnie", "block.minecraft.cherry_sign": "Trześniowy znak", "block.minecraft.cherry_slab": "Trześniowŏ fliza", "block.minecraft.cherry_stairs": "Trześniowe słody", "block.minecraft.cherry_trapdoor": "Trześniowŏ szłapa", "block.minecraft.cherry_wall_hanging_sign": "Trześniowy ściynny szild", "block.minecraft.cherry_wall_sign": "Trześniowy ściynny znak", "block.minecraft.cherry_wood": "Trześniowe drzewo", "block.minecraft.chest": "<PERSON><PERSON><PERSON>", "block.minecraft.chipped_anvil": "Uszczyrbiōny ambos", "block.minecraft.chiseled_bookshelf": "Wyrŏbiany bicherszrank", "block.minecraft.chiseled_copper": "Wyrŏbianŏ miydź", "block.minecraft.chiseled_deepslate": "<PERSON>yr<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_nether_bricks": "Wyrŏbiane netherowe cegły", "block.minecraft.chiseled_polished_blackstone": "Wyrŏbiany glancowany czŏrny kamiyń", "block.minecraft.chiseled_quartz_block": "Wyrŏbiany blok kwarcu", "block.minecraft.chiseled_red_sandstone": "Wyrŏbiany czyrwōny piŏskowiec", "block.minecraft.chiseled_resin_bricks": "Wyrŏbiane harcowe cegły", "block.minecraft.chiseled_sandstone": "Wyrŏbiany piŏskowiec", "block.minecraft.chiseled_stone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> c<PERSON>", "block.minecraft.chiseled_tuff": "Wyrŏbiany tuf", "block.minecraft.chiseled_tuff_bricks": "Wyrŏbiane tufowe cegły", "block.minecraft.chorus_flower": "Kwiŏt refrynice", "block.minecraft.chorus_plant": "Refrynica", "block.minecraft.clay": "Glina", "block.minecraft.closed_eyeblossom": "Zaw<PERSON><PERSON><PERSON> ślypi<PERSON>ka", "block.minecraft.coal_block": "Blok wōngla", "block.minecraft.coal_ore": "<PERSON><PERSON>", "block.minecraft.coarse_dirt": "Ka<PERSON><PERSON><PERSON><PERSON> ziymia", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON> flŏster", "block.minecraft.cobbled_deepslate_slab": "Szyfrowŏ flŏstrowŏ fliza", "block.minecraft.cobbled_deepslate_stairs": "Szyfrowe flŏstrowe słody", "block.minecraft.cobbled_deepslate_wall": "Szyfrowy flŏstrowy murek", "block.minecraft.cobblestone": "Flŏster", "block.minecraft.cobblestone_slab": "Flŏstrowŏ fliza", "block.minecraft.cobblestone_stairs": "Flŏstrowe słody", "block.minecraft.cobblestone_wall": "Flŏstrowy murek", "block.minecraft.cobweb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Kōmyndowy blok", "block.minecraft.comparator": "Redstone'owy kōmparatōr", "block.minecraft.composter": "Kōmposter", "block.minecraft.conduit": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_block": "Blok mi<PERSON>", "block.minecraft.copper_bulb": "Miyd<PERSON>nn<PERSON> byrna", "block.minecraft.copper_door": "Miydzianne dźwiyrze", "block.minecraft.copper_grate": "Miydzianne mrzeże", "block.minecraft.copper_ore": "<PERSON><PERSON>", "block.minecraft.copper_trapdoor": "Miydziann<PERSON> szłapa", "block.minecraft.cornflower": "<PERSON>", "block.minecraft.cracked_deepslate_bricks": "Pynknyte szyfrowe cegły", "block.minecraft.cracked_deepslate_tiles": "Pynknyte szyfrowe kachelki", "block.minecraft.cracked_nether_bricks": "Pynknyte netherowe cegły", "block.minecraft.cracked_polished_blackstone_bricks": "Pynknyte glancowane czŏrnokamiynne cegły", "block.minecraft.cracked_stone_bricks": "Pynknyte kamiynne c<PERSON>ły", "block.minecraft.crafter": "Wytwŏrzŏcz", "block.minecraft.crafting_table": "Fajlbank", "block.minecraft.creaking_heart": "Syrce grzip<PERSON>cza", "block.minecraft.creeper_head": "<PERSON><PERSON> creepera", "block.minecraft.creeper_wall_head": "Ściynnŏ gowa creepera", "block.minecraft.crimson_button": "Szarłatny knefel", "block.minecraft.crimson_door": "Szarłatne d<PERSON>ze", "block.minecraft.crimson_fence": "Szarłatny płot", "block.minecraft.crimson_fence_gate": "Szarłatnŏ wrōtka", "block.minecraft.crimson_fungus": "Szarłatny grzib", "block.minecraft.crimson_hanging_sign": "Szarłatny szild", "block.minecraft.crimson_hyphae": "Szarłatne strzympki", "block.minecraft.crimson_nylium": "Szarłatny mycel", "block.minecraft.crimson_planks": "S<PERSON><PERSON><PERSON>ne brety", "block.minecraft.crimson_pressure_plate": "Szarłatnŏ drukplata", "block.minecraft.crimson_roots": "Szarłatne wōrcle", "block.minecraft.crimson_sign": "Szarłatny znak", "block.minecraft.crimson_slab": "Szarłatnŏ fliza", "block.minecraft.crimson_stairs": "Szarłatne słody", "block.minecraft.crimson_stem": "Szarłatny sztil", "block.minecraft.crimson_trapdoor": "Szarłatnŏ szłapa", "block.minecraft.crimson_wall_hanging_sign": "Szarłatny ściynny szild", "block.minecraft.crimson_wall_sign": "Szarłatny ściynny znak", "block.minecraft.crying_obsidian": "Płaczliwy ôbsydiōn", "block.minecraft.cut_copper": "Żłobiōnŏ miydź", "block.minecraft.cut_copper_slab": "Żłobiōn<PERSON> miydzianno fliza", "block.minecraft.cut_copper_stairs": "Żłobiōne miydzianne słody", "block.minecraft.cut_red_sandstone": "Ciosany czyrwōny piŏskowiec", "block.minecraft.cut_red_sandstone_slab": "Ciosanŏ czyrwōnŏ piŏskowcowŏ fliza", "block.minecraft.cut_sandstone": "Ciosany piŏskowiec", "block.minecraft.cut_sandstone_slab": "Ciosanŏ piŏskowcowŏ fliza", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON><PERSON> fana", "block.minecraft.cyan_bed": "Cyjanowe łōżko", "block.minecraft.cyan_candle": "Cyjanowŏ świyczka", "block.minecraft.cyan_candle_cake": "Torta ze cyjanowōm świyczkōm", "block.minecraft.cyan_carpet": "Cyjanowy tepich", "block.minecraft.cyan_concrete": "Cyjanowy betōn", "block.minecraft.cyan_concrete_powder": "Cyjanowy cymynt", "block.minecraft.cyan_glazed_terracotta": "Cyjanowŏ glaziyrowanŏ kamiōnka", "block.minecraft.cyan_shulker_box": "Cyjanow<PERSON> shulkerowŏ kastla", "block.minecraft.cyan_stained_glass": "Cyjanowe szkło", "block.minecraft.cyan_stained_glass_pane": "Cyjanow<PERSON> szklannŏ tŏfla", "block.minecraft.cyan_terracotta": "<PERSON><PERSON><PERSON><PERSON> kamiōnka", "block.minecraft.cyan_wool": "Cyjanowŏ wołna", "block.minecraft.damaged_anvil": "Popsowany ambos", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "Ćmawodymbowy knefel", "block.minecraft.dark_oak_door": "Ćmawodymbowe dźwiyrze", "block.minecraft.dark_oak_fence": "Ćmawodymbowy płot", "block.minecraft.dark_oak_fence_gate": "Ćmawodymbowŏ wrōtka", "block.minecraft.dark_oak_hanging_sign": "Ćmawodymbowy szild", "block.minecraft.dark_oak_leaves": "Liście ćmawego dymbu", "block.minecraft.dark_oak_log": "Sztam ćmawego dymbu", "block.minecraft.dark_oak_planks": "Ćmawodymbowe brety", "block.minecraft.dark_oak_pressure_plate": "Ćmawodymbowŏ drukplata", "block.minecraft.dark_oak_sapling": "Flancka ćmawego dymbu", "block.minecraft.dark_oak_sign": "Ćmawodymbowy znak", "block.minecraft.dark_oak_slab": "Ćmawodymbowŏ fliza", "block.minecraft.dark_oak_stairs": "Ćmawodymbowe słody", "block.minecraft.dark_oak_trapdoor": "Ćmawodymbowŏ szłapa", "block.minecraft.dark_oak_wall_hanging_sign": "Ćmawodymbowy ściynny szild", "block.minecraft.dark_oak_wall_sign": "Ćmawodymbowy ściynny znak", "block.minecraft.dark_oak_wood": "Ćmawodymbowe drzewo", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "Ćmawoprizmarynowŏ fliza", "block.minecraft.dark_prismarine_stairs": "Ćmawoprizmarynowe słody", "block.minecraft.daylight_detector": "Czujnik dziynnego światła", "block.minecraft.dead_brain_coral": "Umrzity mōzgowy korŏl", "block.minecraft.dead_brain_coral_block": "Blok umrzitego mōzgowego korŏlu", "block.minecraft.dead_brain_coral_fan": "Umrzitŏ mōzgowŏ gorgonia", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON> mōzgowŏ gorgonia", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON><PERSON> bōmbelkowy korŏl", "block.minecraft.dead_bubble_coral_block": "Blok umrzitego bōmbelkowego korŏlu", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON><PERSON><PERSON> bōmbelkowŏ gorgonia", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON> bōmbelkowŏ gorgonia", "block.minecraft.dead_bush": "Uschnyty krzŏk", "block.minecraft.dead_fire_coral": "Umrzity ôgnisty korŏl", "block.minecraft.dead_fire_coral_block": "Blok umrzitego ôgnistego korŏlu", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON><PERSON><PERSON> gorgonia", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON> ôgni<PERSON>ŏ gorgonia", "block.minecraft.dead_horn_coral": "Umrzity rogaty korŏl", "block.minecraft.dead_horn_coral_block": "Blok umrzitego rogatego korŏlu", "block.minecraft.dead_horn_coral_fan": "Umrzitŏ rogatŏ gorgonia", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON>ŏ rogatŏ gorgonia", "block.minecraft.dead_tube_coral": "Umrzity rułkowy korŏl", "block.minecraft.dead_tube_coral_block": "Blok umrzitego rułkowego korŏlu", "block.minecraft.dead_tube_coral_fan": "Umrzitŏ rułkowŏ gorgonia", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON>ŏ rułkowŏ gorgonia", "block.minecraft.decorated_pot": "Prziôzdobiōny bōnclŏk", "block.minecraft.deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> ceglannŏ fliza", "block.minecraft.deepslate_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> ceglanne słody", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON> ceglanny murek", "block.minecraft.deepslate_bricks": "Szyfrowe cegły", "block.minecraft.deepslate_coal_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON> erc wō<PERSON>la", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON> erc <PERSON>", "block.minecraft.deepslate_diamond_ore": "Szyfrowy erc dyjamyntu", "block.minecraft.deepslate_emerald_ore": "Szyfrowy erc szmaragdu", "block.minecraft.deepslate_gold_ore": "Szyfrowy erc złōta", "block.minecraft.deepslate_iron_ore": "Szyfrowy erc żelazła", "block.minecraft.deepslate_lapis_ore": "S<PERSON><PERSON><PERSON><PERSON> erc lazurytu", "block.minecraft.deepslate_redstone_ore": "Szyfrowy erc redstone", "block.minecraft.deepslate_tile_slab": "Szyfrowŏ kachelkowŏ fliza", "block.minecraft.deepslate_tile_stairs": "Szyfrowe kachelkowe słody", "block.minecraft.deepslate_tile_wall": "Szyfrowy kachelkowy murek", "block.minecraft.deepslate_tiles": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.detector_rail": "G<PERSON><PERSON>zy ze synsorym", "block.minecraft.diamond_block": "Blok dyjamyntu", "block.minecraft.diamond_ore": "<PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "Dyjorytowŏ fliza", "block.minecraft.diorite_stairs": "Dyjorytowe słody", "block.minecraft.diorite_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>k", "block.minecraft.dirt": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dirt_path": "Sztajg", "block.minecraft.dispenser": "Strz<PERSON><PERSON><PERSON>", "block.minecraft.dragon_egg": "Drachowe jajco", "block.minecraft.dragon_head": "Gowa dracha", "block.minecraft.dragon_wall_head": "Ściynnŏ gowa dracha", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON><PERSON><PERSON> ghast", "block.minecraft.dried_kelp_block": "Blok wysusz<PERSON>nyj waserflance", "block.minecraft.dripstone_block": "Blok nacieku", "block.minecraft.dropper": "Podŏwŏcz", "block.minecraft.emerald_block": "Blok szmaragdu", "block.minecraft.emerald_ore": "<PERSON><PERSON>", "block.minecraft.enchanting_table": "<PERSON><PERSON><PERSON>", "block.minecraft.end_gateway": "<PERSON><PERSON><PERSON>", "block.minecraft.end_portal": "Portŏl Endu", "block.minecraft.end_portal_frame": "Rōma portŏlu Endu", "block.minecraft.end_rod": "Endowŏ ruta", "block.minecraft.end_stone": "Endowy kamiyń", "block.minecraft.end_stone_brick_slab": "Endowŏ kamiynn<PERSON> ceglannŏ fliza", "block.minecraft.end_stone_brick_stairs": "<PERSON>owe kamiynne ceglan<PERSON> słody", "block.minecraft.end_stone_brick_wall": "<PERSON>owy kamiynny ceglanny murek", "block.minecraft.end_stone_bricks": "<PERSON>owe kamiynne cegły", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "<PERSON><PERSON> wyrŏbianŏ miydź", "block.minecraft.exposed_copper": "<PERSON><PERSON> mi<PERSON>", "block.minecraft.exposed_copper_bulb": "<PERSON><PERSON> miydziannŏ byrna", "block.minecraft.exposed_copper_door": "<PERSON><PERSON> mi<PERSON> dźwiyrze", "block.minecraft.exposed_copper_grate": "<PERSON><PERSON>ne miydzianne mrzeże", "block.minecraft.exposed_copper_trapdoor": "<PERSON><PERSON> miydziannŏ szłapa", "block.minecraft.exposed_cut_copper": "<PERSON><PERSON> żłobiōnŏ miydź", "block.minecraft.exposed_cut_copper_slab": "<PERSON><PERSON> żłobiōnŏ miydziannŏ fliza", "block.minecraft.exposed_cut_copper_stairs": "<PERSON><PERSON> żłobiōne miydzianne słody", "block.minecraft.farmland": "Uprŏwnŏ ziymia", "block.minecraft.fern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire": "Ôgyń", "block.minecraft.fire_coral": "Ôgnisty korŏl", "block.minecraft.fire_coral_block": "Blok ôgnistego korŏlu", "block.minecraft.fire_coral_fan": "Ôgnistŏ gorgonia", "block.minecraft.fire_coral_wall_fan": "Ś<PERSON><PERSON><PERSON> gorgonia", "block.minecraft.firefly_bush": "Jōnikowy krzŏk", "block.minecraft.fletching_table": "<PERSON><PERSON><PERSON>", "block.minecraft.flower_pot": "Ôtka", "block.minecraft.flowering_azalea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea_leaves": "Liście kciōncyj azali<PERSON>", "block.minecraft.frogspawn": "Rapitołzowe jajca", "block.minecraft.frosted_ice": "Ôszadlōny lōd", "block.minecraft.furnace": "Żeleźniŏk", "block.minecraft.gilded_blackstone": "Złocōny czŏrny kamiyń", "block.minecraft.glass": "Szkło", "block.minecraft.glass_pane": "Szklannŏ tŏfla", "block.minecraft.glow_lichen": "Blyndorost", "block.minecraft.glowstone": "Blyndŏk", "block.minecraft.gold_block": "Blok złōta", "block.minecraft.gold_ore": "<PERSON><PERSON>", "block.minecraft.granite": "Granit", "block.minecraft.granite_slab": "Granitowŏ fliza", "block.minecraft.granite_stairs": "Granitowe słody", "block.minecraft.granite_wall": "Granitowy murek", "block.minecraft.grass": "Trŏwa", "block.minecraft.grass_block": "Blok trŏwy", "block.minecraft.gravel": "<PERSON><PERSON>", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON> fana", "block.minecraft.gray_bed": "<PERSON><PERSON>", "block.minecraft.gray_candle": "Siwŏ świyczka", "block.minecraft.gray_candle_cake": "Torta ze siwōm świyczkōm", "block.minecraft.gray_carpet": "<PERSON><PERSON> te<PERSON>h", "block.minecraft.gray_concrete": "<PERSON><PERSON> bet<PERSON>n", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON> cym<PERSON>t", "block.minecraft.gray_glazed_terracotta": "Siwŏ glaziyrowanŏ kamiōnka", "block.minecraft.gray_shulker_box": "Siwŏ shulkerowŏ kastla", "block.minecraft.gray_stained_glass": "Siwe szkło", "block.minecraft.gray_stained_glass_pane": "Siwŏ szklannŏ tŏfla", "block.minecraft.gray_terracotta": "Siwŏ kamiōnka", "block.minecraft.gray_wool": "Siwŏ wołna", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON> fana", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_candle": "Zielōnŏ świyczka", "block.minecraft.green_candle_cake": "Torta ze zielōnōm świyczkōm", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>t", "block.minecraft.green_glazed_terracotta": "Zielōnŏ glaziyrowanŏ kamiōnka", "block.minecraft.green_shulker_box": "Zielōnŏ shulkerowŏ kastla", "block.minecraft.green_stained_glass": "Zielōne szkło", "block.minecraft.green_stained_glass_pane": "Zielōnŏ szklannŏ tŏfla", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> kamiōnka", "block.minecraft.green_wool": "Zielōnŏ wołna", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON> wōrcle", "block.minecraft.hay_block": "<PERSON><PERSON><PERSON>", "block.minecraft.heavy_core": "Ciynżki drzyń", "block.minecraft.heavy_weighted_pressure_plate": "Ciynżkŏ wagowŏ drukplata", "block.minecraft.honey_block": "Blok miodu", "block.minecraft.honeycomb_block": "Blok ślyzykōw miodu", "block.minecraft.hopper": "Lyjŏk", "block.minecraft.horn_coral": "Rogaty korŏl", "block.minecraft.horn_coral_block": "Blok rogatego korŏlu", "block.minecraft.horn_coral_fan": "Rogatŏ gorgonia", "block.minecraft.horn_coral_wall_fan": "Ściynnŏ rogatŏ gorgonia", "block.minecraft.ice": "<PERSON><PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "Chrobŏczywe wyrŏbiane kamiynne cegły", "block.minecraft.infested_cobblestone": "Chrobŏczywy flŏster", "block.minecraft.infested_cracked_stone_bricks": "Chrobŏczywe pynknyte kamiynne cegły", "block.minecraft.infested_deepslate": "Chrobŏczywy szyfer", "block.minecraft.infested_mossy_stone_bricks": "Chrobŏczywe zamszōne kamiynne cegły", "block.minecraft.infested_stone": "Chrobŏczywy kamiyń", "block.minecraft.infested_stone_bricks": "Chrobŏczywe kamiynne c<PERSON>ły", "block.minecraft.iron_bars": "Żelazne mrzeże", "block.minecraft.iron_block": "Blok żelazła", "block.minecraft.iron_door": "Żelazne dźwiyrze", "block.minecraft.iron_ore": "<PERSON><PERSON>", "block.minecraft.iron_trapdoor": "Żelaznŏ szłapa", "block.minecraft.jack_o_lantern": "Ś<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bania", "block.minecraft.jigsaw": "Kōnstrukcyjny blok", "block.minecraft.jukebox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_button": "Dżōnglowy knefel", "block.minecraft.jungle_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_fence": "Dżōnglowy płot", "block.minecraft.jungle_fence_gate": "Dżōnglowŏ wrōtka", "block.minecraft.jungle_hanging_sign": "Dżōnglowy szild", "block.minecraft.jungle_leaves": "Liście dżōnglowego strōmu", "block.minecraft.jungle_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> strōmu", "block.minecraft.jungle_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> brety", "block.minecraft.jungle_pressure_plate": "Dżōnglowŏ drukplata", "block.minecraft.jungle_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> strōmu", "block.minecraft.jungle_sign": "Dżōnglowy znak", "block.minecraft.jungle_slab": "Dżōnglowŏ fliza", "block.minecraft.jungle_stairs": "Dżōnglowe słody", "block.minecraft.jungle_trapdoor": "Dżōng<PERSON><PERSON> szłapa", "block.minecraft.jungle_wall_hanging_sign": "Dżōnglowy ściynny szild", "block.minecraft.jungle_wall_sign": "Dżōnglowy ściynny znak", "block.minecraft.jungle_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>o", "block.minecraft.kelp": "Waserflanca", "block.minecraft.kelp_plant": "Waserflanca", "block.minecraft.ladder": "<PERSON><PERSON><PERSON>", "block.minecraft.lantern": "Blyndka", "block.minecraft.lapis_block": "Blok lazurytu", "block.minecraft.lapis_ore": "<PERSON><PERSON>", "block.minecraft.large_amethyst_bud": "<PERSON><PERSON><PERSON> p<PERSON>nc<PERSON>k ametystu", "block.minecraft.large_fern": "<PERSON><PERSON><PERSON>", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "<PERSON><PERSON> lawy", "block.minecraft.leaf_litter": "Liściowy ściyl", "block.minecraft.lectern": "<PERSON><PERSON>", "block.minecraft.lever": "<PERSON><PERSON><PERSON>", "block.minecraft.light": "Światło", "block.minecraft.light_blue_banner": "Światłomodrŏ fana", "block.minecraft.light_blue_bed": "Światłomodre <PERSON>", "block.minecraft.light_blue_candle": "Światłomodrŏ świyczka", "block.minecraft.light_blue_candle_cake": "Torta ze światłomodrōm świyczkōm", "block.minecraft.light_blue_carpet": "Światłomodry tepich", "block.minecraft.light_blue_concrete": "Światłomodry betōn", "block.minecraft.light_blue_concrete_powder": "Światłomodry cymynt", "block.minecraft.light_blue_glazed_terracotta": "Światłomodrŏ glaziyrowanŏ kamiōnka", "block.minecraft.light_blue_shulker_box": "Światłomodrŏ shulkerowŏ kastla", "block.minecraft.light_blue_stained_glass": "Światłomodre szkło", "block.minecraft.light_blue_stained_glass_pane": "Światłomodrŏ szklannŏ tŏfla", "block.minecraft.light_blue_terracotta": "Światłomodrŏ kamiōnka", "block.minecraft.light_blue_wool": "Światłomodrŏ wołna", "block.minecraft.light_gray_banner": "Światłosiwŏ fana", "block.minecraft.light_gray_bed": "Światłosiwe <PERSON>żko", "block.minecraft.light_gray_candle": "Światłosiwŏ świyczka", "block.minecraft.light_gray_candle_cake": "Torta ze światłosiwōm świyczkōm", "block.minecraft.light_gray_carpet": "Światłosiwy tepich", "block.minecraft.light_gray_concrete": "Światłosiwy betōn", "block.minecraft.light_gray_concrete_powder": "Światłosiwy cymynt", "block.minecraft.light_gray_glazed_terracotta": "Światłosiwŏ glaziyrowanŏ kamiōnka", "block.minecraft.light_gray_shulker_box": "Światłosiwŏ shulkerowŏ kastla", "block.minecraft.light_gray_stained_glass": "Światłosiwe szkło", "block.minecraft.light_gray_stained_glass_pane": "Światłosiwŏ szklannŏ tŏfla", "block.minecraft.light_gray_terracotta": "Światłosiwŏ kamiōnka", "block.minecraft.light_gray_wool": "Światłosiwŏ wołna", "block.minecraft.light_weighted_pressure_plate": "Lekŏ wagowŏ drukplata", "block.minecraft.lightning_rod": "Blicablajter", "block.minecraft.lilac": "Bez", "block.minecraft.lily_of_the_valley": "Majglokla", "block.minecraft.lily_pad": "Wodnŏ lilijŏ", "block.minecraft.lime_banner": "Limōnkow<PERSON> fana", "block.minecraft.lime_bed": "Limōnkowe łōżko", "block.minecraft.lime_candle": "Limōnkowŏ świyczka", "block.minecraft.lime_candle_cake": "Torta ze limōnkowōm świyczkōm", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON> bet<PERSON>", "block.minecraft.lime_concrete_powder": "Limōnkowy cymynt", "block.minecraft.lime_glazed_terracotta": "Limōnkowŏ glaziyrowanŏ kamiōnka", "block.minecraft.lime_shulker_box": "Limōnkowŏ shulkerowŏ kastla", "block.minecraft.lime_stained_glass": "Limōnkowe szkło", "block.minecraft.lime_stained_glass_pane": "Limōnkowŏ szklannŏ tŏfla", "block.minecraft.lime_terracotta": "Limōnkowŏ kamiōnka", "block.minecraft.lime_wool": "Limōnkowŏ wołna", "block.minecraft.lodestone": "Magnetyt", "block.minecraft.loom": "Krosna", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON> fana", "block.minecraft.magenta_bed": "Purpurowe łōżko", "block.minecraft.magenta_candle": "Purpurowŏ świyczka", "block.minecraft.magenta_candle_cake": "Torta ze purpurowōm świyczkōm", "block.minecraft.magenta_carpet": "Purpurowy tepich", "block.minecraft.magenta_concrete": "Purpurowy betōn", "block.minecraft.magenta_concrete_powder": "Purpurowy cymynt", "block.minecraft.magenta_glazed_terracotta": "Purpurowŏ glaziyrowanŏ kamiōnka", "block.minecraft.magenta_shulker_box": "Purpurow<PERSON> shulkerowŏ kastla", "block.minecraft.magenta_stained_glass": "Purpurowe szkło", "block.minecraft.magenta_stained_glass_pane": "Purpurowŏ szklannŏ tŏfla", "block.minecraft.magenta_terracotta": "Purpurow<PERSON> kamiōnka", "block.minecraft.magenta_wool": "Purpurowŏ wołna", "block.minecraft.magma_block": "Blok magmy", "block.minecraft.mangrove_button": "Namorzinowy knefel", "block.minecraft.mangrove_door": "Namorzinowe dźwiyrze", "block.minecraft.mangrove_fence": "Namorzinowy płot", "block.minecraft.mangrove_fence_gate": "Namorzinowŏ wrōtka", "block.minecraft.mangrove_hanging_sign": "Namorzinowy szild", "block.minecraft.mangrove_leaves": "Liście namorzinōw", "block.minecraft.mangrove_log": "<PERSON><PERSON><PERSON>morzinu", "block.minecraft.mangrove_planks": "Namorzinowe brety", "block.minecraft.mangrove_pressure_plate": "<PERSON><PERSON><PERSON>owŏ drukplata", "block.minecraft.mangrove_propagule": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_roots": "Namorzinowe wōrcle", "block.minecraft.mangrove_sign": "Namorzinowy znak", "block.minecraft.mangrove_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> fliza", "block.minecraft.mangrove_stairs": "Namorzinowe słody", "block.minecraft.mangrove_trapdoor": "Namorzin<PERSON><PERSON> szłapa", "block.minecraft.mangrove_wall_hanging_sign": "Namorzinowy ściynny szild", "block.minecraft.mangrove_wall_sign": "Namorzinowy ściynny znak", "block.minecraft.mangrove_wood": "Namorzinowe drzewo", "block.minecraft.medium_amethyst_bud": "Strzedni pōnczek ametystu", "block.minecraft.melon": "Wasermelōn", "block.minecraft.melon_stem": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.moss_block": "Blok mchu", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON><PERSON> mchu", "block.minecraft.mossy_cobblestone": "Zamszōny flŏster", "block.minecraft.mossy_cobblestone_slab": "Zamszōnŏ flŏstrowŏ fliza", "block.minecraft.mossy_cobblestone_stairs": "Zamszōne flŏstrowe słody", "block.minecraft.mossy_cobblestone_wall": "Zamszōny flŏstrowy murek", "block.minecraft.mossy_stone_brick_slab": "Zamszōnŏ kamiynnŏ ceglannŏ fliza ", "block.minecraft.mossy_stone_brick_stairs": "Zamszōne kami<PERSON>e c<PERSON>lanne słody", "block.minecraft.mossy_stone_brick_wall": "Zamszōny kamiynny ceglanny murek", "block.minecraft.mossy_stone_bricks": "<PERSON>ams<PERSON><PERSON><PERSON> kami<PERSON> c<PERSON>y", "block.minecraft.moving_piston": "Tłok we gangu", "block.minecraft.mud": "Ciaply<PERSON>", "block.minecraft.mud_brick_slab": "Ciaplatŏ ceglannŏ fliza", "block.minecraft.mud_brick_stairs": "Ciaplate ceglanne słody", "block.minecraft.mud_brick_wall": "Ciaplaty ceglanny murek", "block.minecraft.mud_bricks": "Ciaplate cegły", "block.minecraft.muddy_mangrove_roots": "Ciaplate wōrcle namorzinu", "block.minecraft.mushroom_stem": "<PERSON><PERSON><PERSON> grz<PERSON>", "block.minecraft.mycelium": "Mycel", "block.minecraft.nether_brick_fence": "Netherowy ceglanny płot", "block.minecraft.nether_brick_slab": "Netherowŏ ceglannŏ fliza", "block.minecraft.nether_brick_stairs": "Netherowe ceglanne słody", "block.minecraft.nether_brick_wall": "Netherowy ceglanny murek", "block.minecraft.nether_bricks": "Netherowe cegły", "block.minecraft.nether_gold_ore": "Netherowy erc złōta", "block.minecraft.nether_portal": "Netherowy portŏl", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON> k<PERSON>", "block.minecraft.nether_sprouts": "Netherowe rywiki", "block.minecraft.nether_wart": "Netherowŏ kurzŏwka", "block.minecraft.nether_wart_block": "Blok netherowyj kurzŏwki", "block.minecraft.netherite_block": "Blok netherytu", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Klangblok", "block.minecraft.oak_button": "Dymbowy knefel", "block.minecraft.oak_door": "Dymbowe dźwiyrze", "block.minecraft.oak_fence": "Dymbowy płot", "block.minecraft.oak_fence_gate": "Dymbowŏ wrōtka", "block.minecraft.oak_hanging_sign": "Dymbowy szild", "block.minecraft.oak_leaves": "Liście dymbu", "block.minecraft.oak_log": "Sztam dymbu", "block.minecraft.oak_planks": "Dymbowe brety", "block.minecraft.oak_pressure_plate": "Dymbowŏ drukplata", "block.minecraft.oak_sapling": "<PERSON><PERSON><PERSON><PERSON> dymbu", "block.minecraft.oak_sign": "Dymbowy znak", "block.minecraft.oak_slab": "Dymbowŏ fliza", "block.minecraft.oak_stairs": "Dymbowe słody", "block.minecraft.oak_trapdoor": "Dymbowŏ szłapa", "block.minecraft.oak_wall_hanging_sign": "Dymbowy ściynny szild", "block.minecraft.oak_wall_sign": "Dymbowy ściynny znak", "block.minecraft.oak_wood": "Dymbowe drzewo", "block.minecraft.observer": "Spoziyr<PERSON>cz", "block.minecraft.obsidian": "Ôbsydiōn", "block.minecraft.ochre_froglight": "Ôkrowŏ blyn<PERSON>łza", "block.minecraft.ominous_banner": "Lagramynckŏ fana", "block.minecraft.open_eyeblossom": "Ôdewrzitŏ ś<PERSON><PERSON>ka", "block.minecraft.orange_banner": "Ôranżowŏ fana", "block.minecraft.orange_bed": "Ôranżowe łōżko", "block.minecraft.orange_candle": "Ôranżowŏ świyczka", "block.minecraft.orange_candle_cake": "Torta ze ôranżowōm świyczkōm", "block.minecraft.orange_carpet": "Ôranżowy tepich", "block.minecraft.orange_concrete": "Ôranżowy betōn", "block.minecraft.orange_concrete_powder": "Ôranżowy cymynt", "block.minecraft.orange_glazed_terracotta": "Ôranżowŏ glaziyrowanŏ kamiōnka", "block.minecraft.orange_shulker_box": "Ôranżowŏ shulker<PERSON>ŏ kastla", "block.minecraft.orange_stained_glass": "Ôranżowe szkło", "block.minecraft.orange_stained_glass_pane": "Ôranżowŏ szkla<PERSON>ŏ tŏfla", "block.minecraft.orange_terracotta": "Ôranżowŏ kamiōnka", "block.minecraft.orange_tulip": "Ôranżowŏ tulpa", "block.minecraft.orange_wool": "Ôranżowŏ wołna", "block.minecraft.oxeye_daisy": "Gyńsipympek", "block.minecraft.oxidized_chiseled_copper": "Utlyniōnŏ wyrŏbianŏ miydź", "block.minecraft.oxidized_copper": "Utlyniōn<PERSON> miydź", "block.minecraft.oxidized_copper_bulb": "Utlyniōnŏ miydziannŏ byrna", "block.minecraft.oxidized_copper_door": "Utlyniōne mi<PERSON> d<PERSON>ze", "block.minecraft.oxidized_copper_grate": "Utlyniōne miydzianne mrzeże", "block.minecraft.oxidized_copper_trapdoor": "Utlyniōnŏ miydziannŏ szłapa", "block.minecraft.oxidized_cut_copper": "Utlyniōnŏ żłobiōnŏ miydź", "block.minecraft.oxidized_cut_copper_slab": "Utlyniōnŏ żłobiōnŏ miydziannŏ fliza", "block.minecraft.oxidized_cut_copper_stairs": "Utlyniōne żłobiōne miydzianne słody", "block.minecraft.packed_ice": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>d", "block.minecraft.packed_mud": "<PERSON><PERSON><PERSON><PERSON> cia<PERSON>lyta", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON><PERSON><PERSON> blady mech", "block.minecraft.pale_moss_block": "Blok bladego mchu", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON><PERSON><PERSON> bladego mchu", "block.minecraft.pale_oak_button": "Bladodymbowy knefel", "block.minecraft.pale_oak_door": "Bladodymbowe dźwiyrze", "block.minecraft.pale_oak_fence": "Bladodymbowy płot", "block.minecraft.pale_oak_fence_gate": "Bladodymbowŏ wrōtka", "block.minecraft.pale_oak_hanging_sign": "Bladodymbowy szild", "block.minecraft.pale_oak_leaves": "Liście bladego dymbu", "block.minecraft.pale_oak_log": "Sztam bladego dymbu", "block.minecraft.pale_oak_planks": "Bladodymbowe brety", "block.minecraft.pale_oak_pressure_plate": "Bladodymbowŏ drukplata", "block.minecraft.pale_oak_sapling": "<PERSON><PERSON>cka bladego dymbu", "block.minecraft.pale_oak_sign": "Bladodymbowy znak", "block.minecraft.pale_oak_slab": "Bladodymbowŏ fliza", "block.minecraft.pale_oak_stairs": "Bladodymbowe słody", "block.minecraft.pale_oak_trapdoor": "Bladodymbowŏ szłapa", "block.minecraft.pale_oak_wall_hanging_sign": "Bladodymbowy ściynny szild", "block.minecraft.pale_oak_wall_sign": "Bladodymbowy ściynny znak", "block.minecraft.pale_oak_wood": "Bladodymbowe drzewo", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON> blyn<PERSON><PERSON><PERSON>", "block.minecraft.peony": "Piwowōnijŏ", "block.minecraft.petrified_oak_slab": "Skamiyniałŏ dymbowŏ fliza", "block.minecraft.piglin_head": "<PERSON><PERSON>", "block.minecraft.piglin_wall_head": "Ściynnŏ gowa piglina", "block.minecraft.pink_banner": "Rōżow<PERSON> fana", "block.minecraft.pink_bed": "Rōżowe łōżko", "block.minecraft.pink_candle": "Rōżowŏ świyczka", "block.minecraft.pink_candle_cake": "Torta ze rōżowōm świyczkōm", "block.minecraft.pink_carpet": "Rōżowy tepich", "block.minecraft.pink_concrete": "Rōżowy betōn", "block.minecraft.pink_concrete_powder": "Rōżowy cymynt", "block.minecraft.pink_glazed_terracotta": "Rōżowŏ glaziyrowanŏ kamiōnka", "block.minecraft.pink_petals": "Rōżowe krōnblaty", "block.minecraft.pink_shulker_box": "Rōżowŏ shulkerowŏ kastla", "block.minecraft.pink_stained_glass": "Rōżowe szkło", "block.minecraft.pink_stained_glass_pane": "Rōżowŏ szklannŏ tŏfla", "block.minecraft.pink_terracotta": "Rōżowŏ kamiōnka", "block.minecraft.pink_tulip": "Rōżowŏ tulpa", "block.minecraft.pink_wool": "Rōżowŏ wołna", "block.minecraft.piston": "Tłok", "block.minecraft.piston_head": "Gōwica tłoka", "block.minecraft.pitcher_crop": "Zbōnecznik", "block.minecraft.pitcher_plant": "Zbōnecznik", "block.minecraft.player_head": "Gowa grŏcza", "block.minecraft.player_head.named": "Gowa grŏcza „%s”", "block.minecraft.player_wall_head": "Ściynnŏ gowa grŏcza", "block.minecraft.podzol": "Biylica", "block.minecraft.pointed_dripstone": "Szpicaty naciek", "block.minecraft.polished_andesite": "Glancowany andezit", "block.minecraft.polished_andesite_slab": "Glancowanŏ andezitowŏ fliza", "block.minecraft.polished_andesite_stairs": "Glancowane andezitowe słody", "block.minecraft.polished_basalt": "Glancowany bazalt", "block.minecraft.polished_blackstone": "Glancowany czŏrny kamiyń", "block.minecraft.polished_blackstone_brick_slab": "Glancowanŏ czŏrnokamiynnŏ ceglannŏ fliza", "block.minecraft.polished_blackstone_brick_stairs": "Glancowane czŏrnokamiynne ceglanne słody", "block.minecraft.polished_blackstone_brick_wall": "Glancowany czŏrnokamiynny ceglanny murek", "block.minecraft.polished_blackstone_bricks": "Glancowane czŏrnokamiynne c<PERSON>ły", "block.minecraft.polished_blackstone_button": "Glancowany czŏrnokamiynny knefel", "block.minecraft.polished_blackstone_pressure_plate": "Glancowanŏ czŏrnokamiynnŏ drukplata", "block.minecraft.polished_blackstone_slab": "Glancowanŏ czŏrnokamiynnŏ fliza", "block.minecraft.polished_blackstone_stairs": "Glancowane czŏrnokamiynne słody", "block.minecraft.polished_blackstone_wall": "Glancowany czŏrnokamiynny murek", "block.minecraft.polished_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate_slab": "Glancowanŏ szyfrowŏ fliza", "block.minecraft.polished_deepslate_stairs": "Glancowane szyfrowe słody", "block.minecraft.polished_deepslate_wall": "<PERSON>lan<PERSON><PERSON><PERSON> s<PERSON>rowy murek", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite_slab": "Glancowanŏ dyjorytowŏ fliza", "block.minecraft.polished_diorite_stairs": "Glancowane dyjorytowe słody", "block.minecraft.polished_granite": "Glancowan<PERSON> granit", "block.minecraft.polished_granite_slab": "Glancowanŏ granitowŏ fliza", "block.minecraft.polished_granite_stairs": "Glancowane granitowe słody", "block.minecraft.polished_tuff": "<PERSON><PERSON><PERSON><PERSON><PERSON> tuf", "block.minecraft.polished_tuff_slab": "Glancowanŏ tufowŏ fliza", "block.minecraft.polished_tuff_stairs": "Glancowane tufowe słody", "block.minecraft.polished_tuff_wall": "Glancowany tufowy murek", "block.minecraft.poppy": "Mak", "block.minecraft.potatoes": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> we <PERSON>", "block.minecraft.potted_allium": "Knobloch we ôtce", "block.minecraft.potted_azalea_bush": "<PERSON><PERSON><PERSON><PERSON><PERSON> we ô<PERSON>ce", "block.minecraft.potted_azure_bluet": "<PERSON><PERSON><PERSON><PERSON> hiustō<PERSON><PERSON><PERSON> we ôtce", "block.minecraft.potted_bamboo": "Bambus we ôtce", "block.minecraft.potted_birch_sapling": "Flancka brzizki we <PERSON>ce", "block.minecraft.potted_blue_orchid": "Modry storczyk we ôtce", "block.minecraft.potted_brown_mushroom": "Prawŏk we ôtce", "block.minecraft.potted_cactus": "<PERSON><PERSON><PERSON> we <PERSON>", "block.minecraft.potted_cherry_sapling": "Flancka trześnie we ô<PERSON>ce", "block.minecraft.potted_closed_eyeblossom": "Zawrzitŏ ślypiŏczka we ôtce", "block.minecraft.potted_cornflower": "<PERSON> we ôtce", "block.minecraft.potted_crimson_fungus": "Szarłatny grzib <PERSON>", "block.minecraft.potted_crimson_roots": "Szarłatne wōrcle we ôtce", "block.minecraft.potted_dandelion": "M<PERSON><PERSON> we ô<PERSON>ce", "block.minecraft.potted_dark_oak_sapling": "Flancka ćmawego dymbu we ôtce", "block.minecraft.potted_dead_bush": "Uschnyty krzŏk we ôtce", "block.minecraft.potted_fern": "<PERSON><PERSON><PERSON><PERSON> we <PERSON>", "block.minecraft.potted_flowering_azalea_bush": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> we ôtce", "block.minecraft.potted_jungle_sapling": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON> strōmu we <PERSON>", "block.minecraft.potted_lily_of_the_valley": "Majglokla we ôtce", "block.minecraft.potted_mangrove_propagule": "<PERSON><PERSON><PERSON> we <PERSON>", "block.minecraft.potted_oak_sapling": "<PERSON><PERSON>cka dymbu we <PERSON>tce", "block.minecraft.potted_open_eyeblossom": "Ôdewrzitŏ ślypi<PERSON><PERSON>ka we ôtce", "block.minecraft.potted_orange_tulip": "Ôranżowŏ tulpa we ôtce", "block.minecraft.potted_oxeye_daisy": "Gyńsipympek we ôtce", "block.minecraft.potted_pale_oak_sapling": "<PERSON><PERSON>cka bladego dymbu we ôtce", "block.minecraft.potted_pink_tulip": "Rōżowŏ tulpa we ôtce", "block.minecraft.potted_poppy": "<PERSON><PERSON> we <PERSON>", "block.minecraft.potted_red_mushroom": "Muszŏk we ôtce", "block.minecraft.potted_red_tulip": "Czyrwōnŏ tulpa we ôtce", "block.minecraft.potted_spruce_sapling": "Flancka świyrku we ôtce", "block.minecraft.potted_torchflower": "Fakliczka we ôtce", "block.minecraft.potted_warped_fungus": "<PERSON><PERSON><PERSON> grzib we <PERSON>", "block.minecraft.potted_warped_roots": "<PERSON><PERSON><PERSON> wō<PERSON> we ôtce", "block.minecraft.potted_white_tulip": "Biołŏ tulpa we ôtce", "block.minecraft.potted_wither_rose": "With<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.powder_snow": "Sypki śniyg", "block.minecraft.powder_snow_cauldron": "Kesel sypkigo śniyga", "block.minecraft.powered_rail": "Napyndzane gla<PERSON>", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Prizmarinowŏ ceglannŏ fliza", "block.minecraft.prismarine_brick_stairs": "Prizmarynowe ceglanne słody", "block.minecraft.prismarine_bricks": "Prizmarynowe cegły", "block.minecraft.prismarine_slab": "Prizmarinowŏ fliza", "block.minecraft.prismarine_stairs": "Prizmarynowe słody", "block.minecraft.prismarine_wall": "Prizmarynowy murek", "block.minecraft.pumpkin": "Ban<PERSON>", "block.minecraft.pumpkin_stem": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_banner": "<PERSON><PERSON><PERSON><PERSON> fana", "block.minecraft.purple_bed": "Flidrowe łōżko", "block.minecraft.purple_candle": "Flidrowŏ świyczka", "block.minecraft.purple_candle_cake": "Torta ze flidrowōm świyczkōm", "block.minecraft.purple_carpet": "<PERSON><PERSON><PERSON><PERSON> tepich", "block.minecraft.purple_concrete": "<PERSON><PERSON><PERSON><PERSON> bet<PERSON>n", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> cym<PERSON>t", "block.minecraft.purple_glazed_terracotta": "Flidrowŏ glaziyrowanŏ kamiōnka", "block.minecraft.purple_shulker_box": "Flidrowŏ shulkerowŏ kastla", "block.minecraft.purple_stained_glass": "Flidrowe szkło", "block.minecraft.purple_stained_glass_pane": "Flidrowŏ szklannŏ tŏfla", "block.minecraft.purple_terracotta": "Flidrowŏ kamiōnka", "block.minecraft.purple_wool": "Flidrowŏ wołna", "block.minecraft.purpur_block": "Blok purpury", "block.minecraft.purpur_pillar": "Purpurowy filŏr", "block.minecraft.purpur_slab": "Purpurowŏ fliza", "block.minecraft.purpur_stairs": "Purpurowe słody", "block.minecraft.quartz_block": "Blok kwarcu", "block.minecraft.quartz_bricks": "Kwarcowe cegły", "block.minecraft.quartz_pillar": "<PERSON>war<PERSON><PERSON>", "block.minecraft.quartz_slab": "Kwarcowŏ fliza", "block.minecraft.quartz_stairs": "Kwarcowe słody", "block.minecraft.rail": "G<PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "Blok surowyj miydzi", "block.minecraft.raw_gold_block": "Blok surowegŏ złōta", "block.minecraft.raw_iron_block": "Blok surowego żelazła", "block.minecraft.red_banner": "Czyrwōnŏ fana", "block.minecraft.red_bed": "Czyrw<PERSON><PERSON>", "block.minecraft.red_candle": "Czyrwōnŏ świyczka", "block.minecraft.red_candle_cake": "Torta ze czyrwōnōm świyczkōm", "block.minecraft.red_carpet": "Czyrwō<PERSON> te<PERSON>", "block.minecraft.red_concrete": "Czyrw<PERSON><PERSON> bet<PERSON>n", "block.minecraft.red_concrete_powder": "Czyrwō<PERSON> cymynt", "block.minecraft.red_glazed_terracotta": "Czyrwōnŏ glaziyrowanŏ kamiōnka", "block.minecraft.red_mushroom": "Muszŏk", "block.minecraft.red_mushroom_block": "Blok muszŏka", "block.minecraft.red_nether_brick_slab": "Czyrwōnŏ netherowŏ ceglannŏ fliza", "block.minecraft.red_nether_brick_stairs": "Czyrwōne netherowe ceglanne słody", "block.minecraft.red_nether_brick_wall": "Czyrwōny netherowy ceglanny murek", "block.minecraft.red_nether_bricks": "Czyrwōne netherowe cegły", "block.minecraft.red_sand": "Czyrwōny piŏsek", "block.minecraft.red_sandstone": "Czyrwōny piŏskowiec", "block.minecraft.red_sandstone_slab": "Czyrwōnŏ piŏskowcowŏ fliza", "block.minecraft.red_sandstone_stairs": "Czyrwōne piŏskowcowe słody", "block.minecraft.red_sandstone_wall": "Czyrwōny piŏskowcowy murek", "block.minecraft.red_shulker_box": "Czyrwōnŏ shulkerowŏ kastla", "block.minecraft.red_stained_glass": "Czyrwōne szkło", "block.minecraft.red_stained_glass_pane": "Czyrwōnŏ szklannŏ tŏfla", "block.minecraft.red_terracotta": "Czyrwōnŏ kamiōnka", "block.minecraft.red_tulip": "Czyrwōn<PERSON> tulpa", "block.minecraft.red_wool": "Czyrwōnŏ wołna", "block.minecraft.redstone_block": "Blok redstone", "block.minecraft.redstone_lamp": "Redstone'ow<PERSON> lampa", "block.minecraft.redstone_ore": "Erc redstone", "block.minecraft.redstone_torch": "Redstone'owŏ fakla", "block.minecraft.redstone_wall_torch": "Ściynnŏ redstone'owŏ fakla", "block.minecraft.redstone_wire": "Redstone'owy przewōd", "block.minecraft.reinforced_deepslate": "Zbrojō<PERSON> s<PERSON>fer", "block.minecraft.repeater": "Redstone'owy przekŏźnik", "block.minecraft.repeating_command_block": "Powtŏrzaj<PERSON><PERSON> k<PERSON>myndowy blok", "block.minecraft.resin_block": "Blok harcu", "block.minecraft.resin_brick_slab": "<PERSON><PERSON><PERSON><PERSON> ceglann<PERSON> fliza", "block.minecraft.resin_brick_stairs": "Harcowe ceglanne słody", "block.minecraft.resin_brick_wall": "<PERSON><PERSON><PERSON><PERSON> ceglanny murek", "block.minecraft.resin_bricks": "Harcowe cegły", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON> harcu", "block.minecraft.respawn_anchor": "<PERSON><PERSON>", "block.minecraft.rooted_dirt": "W<PERSON><PERSON><PERSON><PERSON> ziymia", "block.minecraft.rose_bush": "Rōłzowy krzŏk", "block.minecraft.sand": "Piŏsek", "block.minecraft.sandstone": "Piŏskowiec", "block.minecraft.sandstone_slab": "Piŏskowcowŏ fliza", "block.minecraft.sandstone_stairs": "Piŏskowcowe słody", "block.minecraft.sandstone_wall": "Piŏskowcowy murek", "block.minecraft.scaffolding": "Rysztōnek", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Scul<PERSON><PERSON> ka<PERSON>", "block.minecraft.sculk_sensor": "Sculkowy synsōr", "block.minecraft.sculk_shrieker": "Sculkowy krzykŏcz", "block.minecraft.sculk_vein": "Sculkowŏ żyła", "block.minecraft.sea_lantern": "Morskŏ laterna", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "Morskŏ trŏwa", "block.minecraft.set_spawn": "Nasztalowano pōnkt ôdrŏdzaniŏ", "block.minecraft.short_dry_grass": "Niskŏ suchŏ trŏwa", "block.minecraft.short_grass": "Niskŏ trŏwa", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "Shulkerowŏ kastla", "block.minecraft.skeleton_skull": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.skeleton_wall_skull": "Ściynnŏ szala szkeleta", "block.minecraft.slime_block": "Blok szlōmu", "block.minecraft.small_amethyst_bud": "Ma<PERSON>y pōnczek ametystu", "block.minecraft.small_dripleaf": "<PERSON><PERSON><PERSON> czampiōnek", "block.minecraft.smithing_table": "<PERSON><PERSON><PERSON>", "block.minecraft.smoker": "Wyndzŏk", "block.minecraft.smooth_basalt": "Glajchowany bazalt", "block.minecraft.smooth_quartz": "Glajchowany blok kwarcu", "block.minecraft.smooth_quartz_slab": "Glajchowanŏ kwarcowŏ fliza", "block.minecraft.smooth_quartz_stairs": "Glajchowane kwarcowe słody", "block.minecraft.smooth_red_sandstone": "Glajchowany czyrwōny piŏskowiec", "block.minecraft.smooth_red_sandstone_slab": "Glajchowanŏ czyrwōnŏ piŏskowcowŏ fliza", "block.minecraft.smooth_red_sandstone_stairs": "Glajchowane czyrwōne piŏskowcowe słody", "block.minecraft.smooth_sandstone": "Glajchowany piŏskowiec", "block.minecraft.smooth_sandstone_slab": "Glajchowanŏ piŏskowcowŏ fliza", "block.minecraft.smooth_sandstone_stairs": "Glajchowane piŏskowcowe słody", "block.minecraft.smooth_stone": "Glajchowany kamiyń", "block.minecraft.smooth_stone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kamiynn<PERSON> fliza", "block.minecraft.sniffer_egg": "Jajco sznupŏcza", "block.minecraft.snow": "Śniyg", "block.minecraft.snow_block": "Blok śniyga", "block.minecraft.soul_campfire": "Duszyczkowŏ fojera", "block.minecraft.soul_fire": "Duszyczkowy ôgyń", "block.minecraft.soul_lantern": "Duszyczkowŏ blyndka", "block.minecraft.soul_sand": "Duszyczkowy piŏsek", "block.minecraft.soul_soil": "Duszyczkowŏ ziymia", "block.minecraft.soul_torch": "Duszyczkowŏ fakla", "block.minecraft.soul_wall_torch": "<PERSON><PERSON><PERSON><PERSON>kowŏ fakla", "block.minecraft.spawn.not_valid": "<PERSON><PERSON> m<PERSON> przidzielōnego łōżka ani an<PERSON><PERSON>, abo bōły zagrodzōne", "block.minecraft.spawner": "Swoływŏcz bebŏkōw", "block.minecraft.spawner.desc1": "Interakcyjŏ ze jajcym:", "block.minecraft.spawner.desc2": "Ustŏwiŏ zortã stworzyniŏ", "block.minecraft.sponge": "Gōmbka", "block.minecraft.spore_blossom": "Fukōwka", "block.minecraft.spruce_button": "Świyrkowy knefel", "block.minecraft.spruce_door": "Świyrkowe dźwiyrze", "block.minecraft.spruce_fence": "Świyrkowy płot", "block.minecraft.spruce_fence_gate": "Świyrkowŏ wōrtka", "block.minecraft.spruce_hanging_sign": "Świyrkowy szild", "block.minecraft.spruce_leaves": "Liście świyrku", "block.minecraft.spruce_log": "Sztam świyrku", "block.minecraft.spruce_planks": "Świyrkowe brety", "block.minecraft.spruce_pressure_plate": "Świyrkowŏ drukplata", "block.minecraft.spruce_sapling": "Flancka świyrku", "block.minecraft.spruce_sign": "Świyrkowy znak", "block.minecraft.spruce_slab": "Świyrkowŏ fliza", "block.minecraft.spruce_stairs": "Świyrkowe słody", "block.minecraft.spruce_trapdoor": "Świyrkowŏ szłapa", "block.minecraft.spruce_wall_hanging_sign": "Świyrkowy ściynny szild", "block.minecraft.spruce_wall_sign": "Świyrkowy ściynny znak", "block.minecraft.spruce_wood": "Świyrkowe drzewo", "block.minecraft.sticky_piston": "Lepki tłok", "block.minecraft.stone": "Kamiyń", "block.minecraft.stone_brick_slab": "<PERSON><PERSON><PERSON><PERSON> ceglann<PERSON> fliza", "block.minecraft.stone_brick_stairs": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> s<PERSON>ody", "block.minecraft.stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>lan<PERSON> murek", "block.minecraft.stone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_button": "<PERSON><PERSON><PERSON><PERSON> knefel", "block.minecraft.stone_pressure_plate": "<PERSON><PERSON><PERSON><PERSON> druk<PERSON>a", "block.minecraft.stone_slab": "<PERSON><PERSON><PERSON><PERSON> fliza", "block.minecraft.stone_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stonecutter": "Krajzyga", "block.minecraft.stripped_acacia_log": "Ôkorowany sztam agacu", "block.minecraft.stripped_acacia_wood": "Ôkorowane agacowe drzewo", "block.minecraft.stripped_bamboo_block": "Blok ôkorowanego bambusa", "block.minecraft.stripped_birch_log": "Ôkorowany sztam brzizki", "block.minecraft.stripped_birch_wood": "Ôkorowane brzizkowe drzewo", "block.minecraft.stripped_cherry_log": "Ôkorowany sztam trześnie", "block.minecraft.stripped_cherry_wood": "Ôkorowane trześniowe drzewo", "block.minecraft.stripped_crimson_hyphae": "Ôkorowane szarłatne strzympki", "block.minecraft.stripped_crimson_stem": "Ôkorowany szarłatny sztil", "block.minecraft.stripped_dark_oak_log": "Ôkorowany sztam ćmawego dymbu", "block.minecraft.stripped_dark_oak_wood": "Ôkorowane ćmawodymbowe drzewo", "block.minecraft.stripped_jungle_log": "Ôkorowany sztam d<PERSON><PERSON><PERSON><PERSON><PERSON> strōmu", "block.minecraft.stripped_jungle_wood": "Ôkorowane d<PERSON><PERSON><PERSON><PERSON><PERSON> drzewo", "block.minecraft.stripped_mangrove_log": "Ôkorowany sztam namorzinu", "block.minecraft.stripped_mangrove_wood": "Ôkorowane namorzinowe drzewo", "block.minecraft.stripped_oak_log": "Ôkorowany sztam dymbu", "block.minecraft.stripped_oak_wood": "Ôkorowane dymbowe drzewo", "block.minecraft.stripped_pale_oak_log": "Ôkorowany sztam bladego dymbu", "block.minecraft.stripped_pale_oak_wood": "Ôkorowane bladodymbowe drzewo", "block.minecraft.stripped_spruce_log": "Ôkorowany sztam świyrku", "block.minecraft.stripped_spruce_wood": "Ôkorowane świyrkowe drzewo", "block.minecraft.stripped_warped_hyphae": "Ôkorowane szage strzy<PERSON>ki", "block.minecraft.stripped_warped_stem": "Ôkorowanŏ szagi sztil", "block.minecraft.structure_block": "Strukturowy blok", "block.minecraft.structure_void": "Strukturalnŏ prōżnia", "block.minecraft.sugar_cane": "<PERSON><PERSON><PERSON><PERSON> trzcina", "block.minecraft.sunflower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.suspicious_gravel": "Podyjzdrzany kis", "block.minecraft.suspicious_sand": "Podyjzdrzany piŏsek", "block.minecraft.sweet_berry_bush": "Borōwczywie", "block.minecraft.tall_dry_grass": "Suchŏ wysokŏ trŏwa", "block.minecraft.tall_grass": "Wysokŏ trŏwa", "block.minecraft.tall_seagrass": "Wysokŏ morskŏ trŏwa", "block.minecraft.target": "Szisszajba", "block.minecraft.terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.test_block": "Testowy blok", "block.minecraft.test_instance_block": "Blok wystōmpiyniŏ testu", "block.minecraft.tinted_glass": "Ćmawe szkło", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "Abszusy TNT sōm wyłōnczōne", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "Fakliczka", "block.minecraft.torchflower_crop": "Fakliczka", "block.minecraft.trapped_chest": "<PERSON><PERSON><PERSON>-kast<PERSON>", "block.minecraft.trial_spawner": "Swoływŏcz prōb", "block.minecraft.tripwire": "<PERSON><PERSON>", "block.minecraft.tripwire_hook": "Hŏk na linkã", "block.minecraft.tube_coral": "Rułkowy korŏl", "block.minecraft.tube_coral_block": "Blok rułkowego korŏlu", "block.minecraft.tube_coral_fan": "Rułkowŏ gorgonia", "block.minecraft.tube_coral_wall_fan": "Ściynnŏ rułkowŏ gorgonia", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Tufowŏ ceglannŏ fliza", "block.minecraft.tuff_brick_stairs": "Tufowe ceglan<PERSON> słody", "block.minecraft.tuff_brick_wall": "Tu<PERSON>wy ceglanny murek", "block.minecraft.tuff_bricks": "Tufowe c<PERSON>", "block.minecraft.tuff_slab": "Tufowŏ fliza", "block.minecraft.tuff_stairs": "Tufowe słody", "block.minecraft.tuff_wall": "<PERSON><PERSON><PERSON> murek", "block.minecraft.turtle_egg": "Żōłwie jajco", "block.minecraft.twisting_vines": "<PERSON><PERSON><PERSON> kleterflance", "block.minecraft.twisting_vines_plant": "<PERSON><PERSON><PERSON> kleterflance", "block.minecraft.vault": "Szac", "block.minecraft.verdant_froglight": "Zielenn<PERSON> blyndołza", "block.minecraft.vine": "<PERSON>let<PERSON><PERSON><PERSON>", "block.minecraft.void_air": "Prōżniowy luft", "block.minecraft.wall_torch": "<PERSON><PERSON><PERSON><PERSON> fakla", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON> knefel", "block.minecraft.warped_door": "Szage d<PERSON>wiyrze", "block.minecraft.warped_fence": "Szagi płot", "block.minecraft.warped_fence_gate": "Szagŏ wrōtka", "block.minecraft.warped_fungus": "<PERSON><PERSON><PERSON> grzib", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON> szild", "block.minecraft.warped_hyphae": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON> mycel", "block.minecraft.warped_planks": "Szage brety", "block.minecraft.warped_pressure_plate": "Szagŏ drukplata", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON> w<PERSON>", "block.minecraft.warped_sign": "Szagi znak", "block.minecraft.warped_slab": "Szagŏ fliza", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON> słody", "block.minecraft.warped_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_trapdoor": "Szag<PERSON> szłapa", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON> s<PERSON>ld", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON>ynn<PERSON> znak", "block.minecraft.warped_wart_block": "Blok szagij kurzŏwki", "block.minecraft.water": "W<PERSON>", "block.minecraft.water_cauldron": "<PERSON><PERSON> wody", "block.minecraft.waxed_chiseled_copper": "Waksowanŏ wyrobianŏ miydź", "block.minecraft.waxed_copper_block": "Waksowany blok miydzi", "block.minecraft.waxed_copper_bulb": "Waksowanŏ miydziannŏ byrna", "block.minecraft.waxed_copper_door": "Waksowane miydzianne dźwiyrze", "block.minecraft.waxed_copper_grate": "Waksowane miydzianne mrzeże", "block.minecraft.waxed_copper_trapdoor": "Waksowanŏ miydziannŏ szłapa", "block.minecraft.waxed_cut_copper": "Waksowanŏ żłobiōnŏ miydź", "block.minecraft.waxed_cut_copper_slab": "Waksowanŏ żłobiōnŏ miydziannŏ fliza", "block.minecraft.waxed_cut_copper_stairs": "Waksowane żłobiōne miydzianne słody", "block.minecraft.waxed_exposed_chiseled_copper": "Waksowanŏ leko u<PERSON>ō<PERSON>ŏ wyrŏbianŏ miydź", "block.minecraft.waxed_exposed_copper": "Waksowanŏ leko u<PERSON> mi<PERSON>ź", "block.minecraft.waxed_exposed_copper_bulb": "Waksowanŏ leko u<PERSON> miydziannŏ byrna", "block.minecraft.waxed_exposed_copper_door": "Waksowane leko utlyniōne miydzianne dźwiyrze", "block.minecraft.waxed_exposed_copper_grate": "Waksowane leko utlyniōne miydzianne mrzeże", "block.minecraft.waxed_exposed_copper_trapdoor": "Waksowanŏ leko u<PERSON> miydziannŏ szłapa", "block.minecraft.waxed_exposed_cut_copper": "Waksowanŏ leko utly<PERSON>ō<PERSON>ŏ żłobiōnŏ miydź", "block.minecraft.waxed_exposed_cut_copper_slab": "Waksowanŏ leko u<PERSON> żłobiōnŏ miydziannŏ fliza", "block.minecraft.waxed_exposed_cut_copper_stairs": "Waksowane leko utlyniōne żłobiōne miydzianne słody", "block.minecraft.waxed_oxidized_chiseled_copper": "Waksowanŏ utlyniōnŏ wyrŏbianŏ miydź", "block.minecraft.waxed_oxidized_copper": "Waksowanŏ utlyniōnŏ miydź", "block.minecraft.waxed_oxidized_copper_bulb": "Waksowanŏ utlyniōnŏ miydziannŏ byrna", "block.minecraft.waxed_oxidized_copper_door": "Waksowane utlyniōne miydzianne dźwiyrze", "block.minecraft.waxed_oxidized_copper_grate": "Waksowane utlyniōne miydzianne mrzeże", "block.minecraft.waxed_oxidized_copper_trapdoor": "Waksowanŏ utlyniōnŏ miydziannŏ szłapa", "block.minecraft.waxed_oxidized_cut_copper": "Waksowanŏ utlyniōnŏ żłobiōnŏ miydź", "block.minecraft.waxed_oxidized_cut_copper_slab": "Waksowanŏ utlyniōnŏ żłobiōnŏ miydziannŏ fliza", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Waksowane utlyniōne żłobiōne miydzianne słody", "block.minecraft.waxed_weathered_chiseled_copper": "Waksowanŏ strzednio utlyniōnŏ wyrŏbianŏ miydź", "block.minecraft.waxed_weathered_copper": "Waksowanŏ strzednio utlyniōnŏ miydź", "block.minecraft.waxed_weathered_copper_bulb": "Waksowanŏ strzednio utlyniōnŏ miydziannŏ byrna", "block.minecraft.waxed_weathered_copper_door": "Waksowane strzednio utlyniōne miydzianne dźwiyrze", "block.minecraft.waxed_weathered_copper_grate": "Waksowanŏ strzednio utlyniōnŏ miydziannŏ mrzeże", "block.minecraft.waxed_weathered_copper_trapdoor": "Waksowanŏ strzednio utlyniōnŏ miydziannŏ szłapa", "block.minecraft.waxed_weathered_cut_copper": "Waksowanŏ strzednio utlyniōnŏ żłobiōnŏ miydź", "block.minecraft.waxed_weathered_cut_copper_slab": "Waksowanŏ strzednio utlyniōnŏ żłobiōnŏ miydziannŏ fliza", "block.minecraft.waxed_weathered_cut_copper_stairs": "Waksowane strzednio utlyniōne żłobiōne miydzianne słody", "block.minecraft.weathered_chiseled_copper": "St<PERSON><PERSON>nio <PERSON>niō<PERSON>ŏ wyrŏbianŏ miydź", "block.minecraft.weathered_copper": "<PERSON><PERSON><PERSON><PERSON> mi<PERSON>ź", "block.minecraft.weathered_copper_bulb": "<PERSON><PERSON><PERSON><PERSON>ŏ miydziannŏ byrna", "block.minecraft.weathered_copper_door": "Strzednio utlyniōne miydzianne dźwiyrze", "block.minecraft.weathered_copper_grate": "Strzednio utlyniōne miydzianne mrzeże", "block.minecraft.weathered_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON>ŏ miydziannŏ szłapa", "block.minecraft.weathered_cut_copper": "<PERSON><PERSON><PERSON><PERSON>niō<PERSON>ŏ żłobiōnŏ miydź", "block.minecraft.weathered_cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON> żłobiōnŏ miydziannŏ fliza", "block.minecraft.weathered_cut_copper_stairs": "Strzednio utlyniōne żłobiōne miydzianne słody", "block.minecraft.weeping_vines": "Szarłatne kleterflance", "block.minecraft.weeping_vines_plant": "Szarłatne kleterflance", "block.minecraft.wet_sponge": "Mokrŏ gōmbka", "block.minecraft.wheat": "Pszynica", "block.minecraft.white_banner": "Biołŏ fana", "block.minecraft.white_bed": "Biołe łōżko", "block.minecraft.white_candle": "Biołŏ świyczka", "block.minecraft.white_candle_cake": "Torta ze biołōm świyczkōm", "block.minecraft.white_carpet": "Bioły tepich", "block.minecraft.white_concrete": "Bioły betōn", "block.minecraft.white_concrete_powder": "Bioły cymynt", "block.minecraft.white_glazed_terracotta": "Biołŏ glaziyrowanŏ kamiōnka", "block.minecraft.white_shulker_box": "Biołŏ shulkerowŏ kastla", "block.minecraft.white_stained_glass": "Biołe szkło", "block.minecraft.white_stained_glass_pane": "Biołŏ szklannŏ tŏfla", "block.minecraft.white_terracotta": "Biołŏ kamiōnka", "block.minecraft.white_tulip": "Biołŏ tulpa", "block.minecraft.white_wool": "Biołŏ wołna", "block.minecraft.wildflowers": "Dzike kwiŏty", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Szala witherowego szkeleta", "block.minecraft.wither_skeleton_wall_skull": "Ściynnŏ szala witherowego szkeleta", "block.minecraft.yellow_banner": "Żōłt<PERSON> fana", "block.minecraft.yellow_bed": "Żōłte łōżko", "block.minecraft.yellow_candle": "Żōłtŏ świyczka", "block.minecraft.yellow_candle_cake": "Torta ze żōłtōm świyczkōm", "block.minecraft.yellow_carpet": "Żōłty tepich", "block.minecraft.yellow_concrete": "Żōłty betōn", "block.minecraft.yellow_concrete_powder": "Żōłty cymynt", "block.minecraft.yellow_glazed_terracotta": "Żōłtŏ glaziyrowanŏ kamiōnka", "block.minecraft.yellow_shulker_box": "Żōłtŏ shulkerowŏ kastla", "block.minecraft.yellow_stained_glass": "Żōłte szkło", "block.minecraft.yellow_stained_glass_pane": "Żōłtŏ szklannŏ tŏfla", "block.minecraft.yellow_terracotta": "Żōłtŏ kamiōnka", "block.minecraft.yellow_wool": "Żōłtŏ wołna", "block.minecraft.zombie_head": "<PERSON><PERSON> umarlŏ<PERSON>", "block.minecraft.zombie_wall_head": "Ściynnŏ gowa umarlŏka", "book.byAuthor": "ôd %1$s", "book.edit.title": "Ykrōn <PERSON> ksiōnżki", "book.editTitle": "Wkludź miano ksiōnżki:", "book.finalizeButton": "Podszriftuj a zawrzij", "book.finalizeWarning": "Pozōr! Po podpisaniu ksiōnżki, niy bydziesz już śmioł jij edytować.", "book.generation.0": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "book.generation.1": "<PERSON><PERSON><PERSON><PERSON>", "book.generation.2": "<PERSON><PERSON><PERSON><PERSON> kopije", "book.generation.3": "Starganŏ kopijŏ", "book.invalid.tag": "* Felernŏ etyketa k<PERSON>żki *", "book.pageIndicator": "Zajta %1$s ze %2$s", "book.page_button.next": "Nŏstympnŏ zajta", "book.page_button.previous": "<PERSON>rz<PERSON><PERSON><PERSON> zajta", "book.sign.title": "Ykrōn pod<PERSON>ŏ ksiōnżki", "book.sign.titlebox": "Titel", "book.signButton": "Podszriftuj", "book.view.title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ōnżki", "build.tooHigh": "Limit wysokości budowaniŏ je %s blokōw", "chat.cannotSend": "<PERSON><PERSON> <PERSON><PERSON> p<PERSON> w<PERSON>", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "<PERSON><PERSON><PERSON>, coby sie prz<PERSON>", "chat.copy": "Skopiyruj ku schowku", "chat.copy.click": "<PERSON><PERSON><PERSON>, coby sko<PERSON><PERSON> ku schowku", "chat.deleted_marker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> usunytŏ ôd serwera.", "chat.disabled.chain_broken": "Czat wyłōnczōny skuli przerwanyj kety. Proszymy sprōbować na zaś sie połōnczyć.", "chat.disabled.expiredProfileKey": "Czat ôstoł wyłōnczōny skuli przedŏwniōnego klucza publicznego profilu. Sprōbuj na zaś sie połōnczyć.", "chat.disabled.invalid_command_signature": "Kōmynda zawiyrała niyspodziywane abo brakujōnce podpisy argumyntōw.", "chat.disabled.invalid_signature": "Czat mioł felerny podpis. Sprōbuj na zaś sie połōnczyć.", "chat.disabled.launcher": "<PERSON><PERSON>t ôstoł wyłōnczōny we nasztalowaniach lōnczera. Niy idzie posłać wiadō<PERSON>ci.", "chat.disabled.missingProfileKey": "Czat ôstoł wyłōnczōny skuli braku klucza publicznego profilu. Sprōbuj sie połōnczyć na zaś.", "chat.disabled.options": "<PERSON><PERSON><PERSON> ôstoł wyłōnczōny we nasztalowaniach klijynta.", "chat.disabled.out_of_order_chat": "Erbnyto czat we niynŏleżnym porzōndku. Zmiyniōł sie u ciebie systymowy czas?", "chat.disabled.profile": "Nasztalowania kōnta niy przizwŏlajōm na używanie czatu. Naciś zaś „%s”, coby u<PERSON><PERSON> wiyncyj informacyji.", "chat.disabled.profile.moreInfo": "Nasztalowania kōnta niy przizwŏlajōm na używanie czatu. Niy śmis<PERSON> posyła<PERSON> ani ô<PERSON> w<PERSON>dō<PERSON>ści.", "chat.editBox": "czat", "chat.filtered": "Ôdfiltrowano ôd serwera.", "chat.filtered_full": "<PERSON>wer skrył <PERSON>j<PERSON> wiadōmość niykerym grŏczōm.", "chat.link.confirm": "Na isto ch<PERSON><PERSON>wrz<PERSON> tã neczajtã?", "chat.link.confirmTrusted": "Ch<PERSON>z ôdewrzić tyn link, abo go sko<PERSON>yrować ku schowku?", "chat.link.open": "Ôdewrzij we przeziyrŏczce", "chat.link.warning": "<PERSON>g<PERSON> niy <PERSON><PERSON><PERSON><PERSON><PERSON>w ôd <PERSON>, kerym niy uf<PERSON>!", "chat.queue": "[+%s rzy<PERSON><PERSON>w, kere czka<PERSON>m]", "chat.square_brackets": "[%s]", "chat.tag.error": "<PERSON><PERSON> posłoł felernõ wiad<PERSON>.", "chat.tag.modified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON> ôd serwera. Oryginoł:", "chat.tag.not_secure": "Niyuwiyrzitelnionŏ wiadōmość. Niy śmisz jij zgł<PERSON>.", "chat.tag.system": "Serwerowŏ wiadōmość. Niy idzie jij zgł<PERSON>ić.", "chat.tag.system_single_player": "Serwerowŏ wiad<PERSON>.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "Grŏcz %s ukōńczōł wyzwanie %s", "chat.type.advancement.goal": "Grŏcz %s ôsiōngnył cyl %s", "chat.type.advancement.task": "Grŏcz %s zrobiōł postymp %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Napisz ku manszaftowi", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s gŏdŏ %s", "chat.validation_error": "Feler przi weryfikacyji c<PERSON>", "chat_screen.message": "Wiadōmość ku posłaniu: %s", "chat_screen.title": "Ôkno czatu", "chat_screen.usage": "<PERSON><PERSON><PERSON><PERSON><PERSON> wia<PERSON> a naciś Enter, coby pos<PERSON>", "chunk.toast.checkLog": "Badnij tagebuch po wiyncyj informacyji", "chunk.toast.loadFailure": "Niy podarziło sie zaladować czanku przi %s", "chunk.toast.lowDiskSpace": "Mało placu na dysku!", "chunk.toast.lowDiskSpace.description": "<PERSON> m<PERSON>, iże niy bydziesz mōg zachowa<PERSON> świŏta.", "chunk.toast.saveFailure": "Niy podarziło sie zachować czanku na %s", "clear.failed.multiple": "Niy znŏjdzōno żŏdnych rzeczy u %s grŏczy", "clear.failed.single": "Niy znŏjdzōno żŏdnych rzeczy u grŏcza %s", "color.minecraft.black": "Czŏrnŏ", "color.minecraft.blue": "Modrŏ", "color.minecraft.brown": "Brōnŏtnŏ", "color.minecraft.cyan": "<PERSON>janowŏ", "color.minecraft.gray": "Siwŏ", "color.minecraft.green": "Zielōn<PERSON>", "color.minecraft.light_blue": "Światłomodrŏ", "color.minecraft.light_gray": "Światłosiwŏ", "color.minecraft.lime": "Limōnkowŏ", "color.minecraft.magenta": "Purpurowŏ", "color.minecraft.orange": "Ôranżowŏ", "color.minecraft.pink": "Rōżowŏ", "color.minecraft.purple": "<PERSON><PERSON><PERSON>", "color.minecraft.red": "Czyrwōnŏ", "color.minecraft.white": "Biołŏ", "color.minecraft.yellow": "Żōłtŏ", "command.context.here": "<--[SAMKEJ]", "command.context.parse_error": "%s na położyniu %s: %s", "command.exception": "Niy podarziło sie przetworzić kōmyndy: %s", "command.expected.separator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> spa<PERSON>, ker<PERSON> by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ale znŏjdzōno dals<PERSON> dane", "command.failed": "Feler przi prōbie zregiyrowaniŏ tyj kōmyndy", "command.forkLimit": "Ôsiōngnyto maksym<PERSON>nõ liczbã kōntekstōw (%s)", "command.unknown.argument": "<PERSON><PERSON><PERSON> argum<PERSON>t l<PERSON> tyj kōmy<PERSON>", "command.unknown.command": "Niyznanŏ abo niykōmpletnŏ kōmynda; pōniż<PERSON>j je feler", "commands.advancement.criterionNotFound": "Postymp „%1$s” niy ôbejmuje kriteryjum „%2$s”", "commands.advancement.grant.criterion.to.many.failure": "Niy idzie prziznać kriteryjum „%s” postympu „%s” %s grŏczōm, pōniywŏż już go majōm", "commands.advancement.grant.criterion.to.many.success": "Prziznano kriteryjum „%s” postympowi „%s” %s grŏczōm", "commands.advancement.grant.criterion.to.one.failure": "Niy idzie prziznać kriteryjum „%s” postympu „%s” grŏczowi %s, pōniywŏż już go mŏ", "commands.advancement.grant.criterion.to.one.success": "Prziznano kriteryjum „%s” postympowi „%s” grŏczowi %s", "commands.advancement.grant.many.to.many.failure": "Niy idzie prziznać %s postympōw %s grŏczom, pōniywŏż już je majōm", "commands.advancement.grant.many.to.many.success": "Prziznano %s postympōw %s grŏczōm", "commands.advancement.grant.many.to.one.failure": "Niy idzie prziznać %s postympōw grŏczowi %s, pōniywŏż już go mŏ", "commands.advancement.grant.many.to.one.success": "Prziznano %s postympōw grŏczowi %s", "commands.advancement.grant.one.to.many.failure": "Niy idzie prziznać postympu „%s” %s grŏczōm, pōniywŏż już go majōm", "commands.advancement.grant.one.to.many.success": "Prziznano postymp „%s” %s grŏczōm", "commands.advancement.grant.one.to.one.failure": "Niy idzie prziznać postympu „%s” grŏczowi %s, pōniywŏż już go majōm", "commands.advancement.grant.one.to.one.success": "Prziznano postymp „%s” grŏczowi %s", "commands.advancement.revoke.criterion.to.many.failure": "Niy idzie ôdkŏzać kriteryjum „%s” postympu „%s” %s grŏczōm, pōniywŏż go niy majōm", "commands.advancement.revoke.criterion.to.many.success": "Ôdkŏzano kriteryjum „%s” postympu „%s” %s grŏczōm", "commands.advancement.revoke.criterion.to.one.failure": "Niy idzie ôdkŏzać kriteryjum „%s” postympu „%s” grŏczowi %s, pōniywŏż go niy mŏ", "commands.advancement.revoke.criterion.to.one.success": "Ôdkŏzano kriteryjum „%s” postympu „%s” grŏczŏwi %s", "commands.advancement.revoke.many.to.many.failure": "Niy idzie ôdkŏzać %s postympōw %s grŏczōm, pōniywŏż go niy majōm", "commands.advancement.revoke.many.to.many.success": "Ôdkŏzano %s postympōw %s grŏczōm", "commands.advancement.revoke.many.to.one.failure": "Niy idzie ôdkŏzać %s postympōw grŏczowi %s, pōniywŏż go niy mŏ", "commands.advancement.revoke.many.to.one.success": "Ôdkŏzano %s postympōw grŏczowi %s", "commands.advancement.revoke.one.to.many.failure": "Niy idzie <PERSON> postympu „%s” %s gr<PERSON>czōm, pōniywŏż go niy majōm", "commands.advancement.revoke.one.to.many.success": "Ôdkŏzano postymp „%s” %s grŏczōm", "commands.advancement.revoke.one.to.one.failure": "Niy idzie ô<PERSON>ć postympu „%s” grŏczowi %s, pōniywŏż go niy mŏ", "commands.advancement.revoke.one.to.one.success": "Ôdkŏzano postymp „%s” grŏczowi %s", "commands.attribute.base_value.get.success": "G<PERSON><PERSON><PERSON> wert ôd atrybutu „%s” u bytu %s je %s", "commands.attribute.base_value.reset.success": "Zresetowano grōntny wert atrybutowi „%s” u bytu %s na sztandardowe %s", "commands.attribute.base_value.set.success": "Nasztalowano grōntny wert atrybutowi „%s” u bytu %s na %s", "commands.attribute.failed.entity": "%s niyma isty byt lŏ tyj kōmyndy", "commands.attribute.failed.modifier_already_present": "Modyfikatōr „%s” już je ôbecny przi atrybucie „%s” u bytu %s", "commands.attribute.failed.no_attribute": "Byt %s niy mŏ atrybutu %s", "commands.attribute.failed.no_modifier": "Atrybut „%s” u bytu %s niy mŏ modyfikatora „%s”", "commands.attribute.modifier.add.success": "Przidano modyfikatōr „%s” atrybutowi „%s” u bytu %s", "commands.attribute.modifier.remove.success": "Usunyto modyfikatōr „%s” ze atrybutu „%s” u bytu %s", "commands.attribute.modifier.value.get.success": "<PERSON>rt ôd mody<PERSON> „%s” przi atrybucie „%s” u bytu %s je %s", "commands.attribute.value.get.success": "Wert ôd atrybutu „%s” u bytu „%s” je %s", "commands.ban.failed": "Nic sie niy zmiyniło. Tyn grŏcz już je bloknyty", "commands.ban.success": "Bloknyto grŏcza %s za: %s", "commands.banip.failed": "Nic sie niy zmiyniło. Ta adresa IP już je bloknytŏ", "commands.banip.info": "Tyn ban sie tyczy %s grŏczy: %s", "commands.banip.invalid": "Felernŏ adresa IP abo niyznany grŏcz", "commands.banip.success": "Bloknyto IP %s za: %s", "commands.banlist.entry": "Grŏcz %s ôstoł bloknyty ôd %s za: %s", "commands.banlist.entry.unknown": "(Niyznany)", "commands.banlist.list": "Je bloknytych %s ôsōb:", "commands.banlist.none": "<PERSON><PERSON> <PERSON>ŏdn<PERSON>", "commands.bossbar.create.failed": "Już istnieje pŏsek zdrowiŏ ze ID „%s”", "commands.bossbar.create.success": "Stworzōno niysztandardowy pŏsek zdrowiŏ %s", "commands.bossbar.get.max": "Niysztandardowy pŏsek zdrowiŏ %s mŏ maksymalny wert %s", "commands.bossbar.get.players.none": "Żŏdyn ze grŏczy przipisanych ku niysztandardowymu pŏsku zdrowiŏ %s niyma terŏzki onlajn", "commands.bossbar.get.players.some": "Niysztandardowy pŏsek zdrowiŏ %s mŏ %s przipisanych grŏczy, kerzy sōm on<PERSON>jn: %s", "commands.bossbar.get.value": "Niysztandardowy pŏsek zdrowiŏ %s mŏ wert %s", "commands.bossbar.get.visible.hidden": "Niysztandardowy pŏsek zdrowiŏ %s je skryty", "commands.bossbar.get.visible.visible": "Niysztandardowy pŏsek zdrowiŏ %s je widoczny", "commands.bossbar.list.bars.none": "Niy ma żŏdnych aktywnych niysztandardowych pŏskōw zdrowiŏ", "commands.bossbar.list.bars.some": "Je %s aktywnych niysztandardowych pŏskōw zdrowiŏ: %s", "commands.bossbar.remove.success": "Usunyto niysztandardowy pŏsek zdrowiŏ %s", "commands.bossbar.set.color.success": "Zmiyniōno farbã niysztandardowego pŏska zdrowiŏ %s", "commands.bossbar.set.color.unchanged": "Nic sie niy zmiyniło. Pŏsek zdrowiŏ już je tyj farby", "commands.bossbar.set.max.success": "Zmiyniōno maksymalny wert niysztandardowego pŏska zdrowiŏ %s na %s", "commands.bossbar.set.max.unchanged": "Nic sie niy zmiyniło. Pŏsek zdrowiŏ już mŏ taki maksymalny wert", "commands.bossbar.set.name.success": "Zmiyniōno miano niysztandardowego pŏska zdrowiŏ %s", "commands.bossbar.set.name.unchanged": "Nic sie niy zmiyniło. Pŏsek zdrowiŏ już mŏ take miano", "commands.bossbar.set.players.success.none": "Niysztandardowy pŏsek zdrowiŏ %s już niy mŏ żŏdnych grŏczy", "commands.bossbar.set.players.success.some": "Niysztandardowy pŏsek zdrowiŏ %s terŏzki mŏ %s grŏczy: %s", "commands.bossbar.set.players.unchanged": "Nic sie niy zmiyniło. Ci grŏcze już sōm na pŏsku zdrowiŏ a niy ma żŏdnego ku przidaniu ani usunyciu", "commands.bossbar.set.style.success": "Zmiyniōno wyglōnd niysztandardowego pŏska zdrowiŏ %s", "commands.bossbar.set.style.unchanged": "Nic sie niy zmiyniło. Pŏsek zdrowiŏ już mŏ taki wyglōnd", "commands.bossbar.set.value.success": "Zmiyniōno wert niysztandardowego pŏska zdrowiŏ %s na %s", "commands.bossbar.set.value.unchanged": "Nic sie niy zmiyniło. Pŏsek zdrowiŏ już mŏ tyn wert", "commands.bossbar.set.visibility.unchanged.hidden": "Nic sie niy zmiyniło. Pŏsek zdrowiŏ już je skryty", "commands.bossbar.set.visibility.unchanged.visible": "Nic sie niy zmiyniło. Pŏsek zdrowiŏ już je pokŏzany", "commands.bossbar.set.visible.success.hidden": "Skryto niysztandardowy pŏsek zdrowiŏ %s", "commands.bossbar.set.visible.success.visible": "Pokŏzano niysztandardowy pŏsek zdrowiŏ %s", "commands.bossbar.unknown": "Niy ma żŏdnego pŏska zdrowiŏ ze ID „%s”", "commands.clear.success.multiple": "Usunyto %s rzeczy ze inwyntŏrzōw %s grŏczōw", "commands.clear.success.single": "Usunyto %s rzeczy ze inwyntŏrza grŏcza %s", "commands.clear.test.multiple": "Znŏjdzōno %s rzeczy u %s grŏczy, kere pasujōm", "commands.clear.test.single": "Znŏjdzōno %s rzeczy u grŏcza %s, kere pasujōm", "commands.clone.failed": "Żŏdne bloki niy ôstały powielōne", "commands.clone.overlap": "Zdrzōdłowe a adresowe pola niy śmiōm sie przekrywać", "commands.clone.success": "Sklōnowano %s blokōw", "commands.clone.toobig": "Za moc blokōw we wyznaczōnyj zōnie — maksymalnie je %s, a zaznaczōno %s", "commands.damage.invulnerable": "Byt je strzimały na zraniynia tyj zorty", "commands.damage.success": "Wyrzōndzōno %s zraniyń bytowi %s", "commands.data.block.get": "Wert ôd włŏsności „%s” u bloku na %s, %s, %s pomnożōny ô %s je %s", "commands.data.block.invalid": "Ôbrany blok niyma byt", "commands.data.block.modified": "Zmiyniōno dane bloku na %s, %s, %s", "commands.data.block.query": "Blok na %s, %s, %s mŏ dalsze dane: %s", "commands.data.entity.get": "Wert ôd włŏsności „%s” u bytu %s pomnożōny ô %s je %s", "commands.data.entity.invalid": "<PERSON><PERSON> <PERSON><PERSON> z<PERSON> danych grŏcza", "commands.data.entity.modified": "Zmiyniōno dane bytowi %s", "commands.data.entity.query": "Byt %s mŏ dalsze dane: %s", "commands.data.get.invalid": "Niy idzie erbnyć %s — przizwolōne sōm yno nōmerowe etykety", "commands.data.get.multiple": "Tyn argumynt przijmuje yno jedyn wert NBT", "commands.data.get.unknown": "<PERSON>y idzie erbnyć wertu ôd włŏsności „%s” — etyketa niy istnieje", "commands.data.merge.failed": "<PERSON>c sie niy zmiyniło. Podane włŏsności już majōm te werty", "commands.data.modify.expected_list": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> listy, a ôtrzimano: %s", "commands.data.modify.expected_object": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a ôtrzimano: %s", "commands.data.modify.expected_value": "Spod<PERSON><PERSON><PERSON><PERSON> wertu, a ôtrzimano: %s", "commands.data.modify.invalid_index": "Felerny indeks listy: %s", "commands.data.modify.invalid_substring": "Felerne indeksy podciōngōw: ôd %s ku %s", "commands.data.storage.get": "Wert ôd wł<PERSON>sności „%s” we skrytce %s pomnożōny ô %s je %s", "commands.data.storage.modified": "Zmiyniōno skrytkã %s", "commands.data.storage.query": "Skrytka %s mŏ dalszy inhalt: %s", "commands.datapack.create.already_exists": "Już istnieje paket ô mianie „%s”", "commands.datapack.create.invalid_full_name": "<PERSON><PERSON><PERSON> miano nowego paketu: „%s”", "commands.datapack.create.invalid_name": "Felerne znaki we nowym mianie paketu: „%s”", "commands.datapack.create.io_failure": "Niy id<PERSON> st<PERSON><PERSON> paketu ô mianie „%s”, sprŏwdź register", "commands.datapack.create.metadata_encode_failure": "Niy podarziło sie zakodować metadanych paketowi ô mianie „%s”: %s", "commands.datapack.create.success": "Stworzōno nowy prōżny paket ô mianie „%s”", "commands.datapack.disable.failed": "Paket „%s” niyma załōnczōny!", "commands.datapack.disable.failed.feature": "Paket „%s” niy może ôstać wyłōncz<PERSON>ny, pōniywŏż je tajla załōnczōnyj fany!", "commands.datapack.enable.failed": "Paket „%s” już je załōnczōny!", "commands.datapack.enable.failed.no_flags": "Paket „%s” niy może ôstać załōnczōny, pōniywŏż iste fany niy sōm załōnczōne na tym świecie: %s", "commands.datapack.list.available.none": "Niy ma już żŏdnego dostympnego paketu danych", "commands.datapack.list.available.success": "Je dostympnych %s paketōw danych: %s", "commands.datapack.list.enabled.none": "Niy ma żŏdnych załōnczōnych paketōw danych", "commands.datapack.list.enabled.success": "Je %s pake<PERSON>w danych: %s", "commands.datapack.modify.disable": "Wyłōnczanie paketu danych %s…", "commands.datapack.modify.enable": "Załōnczanie paketu danych %s…", "commands.datapack.unknown": "Niyznany paket danych: „%s”", "commands.debug.alreadyRunning": "Profiler t<PERSON><PERSON><PERSON> ju<PERSON> s<PERSON>ł", "commands.debug.function.noRecursion": "Niy idzie prześledzić ze postrzodka fōnkcyje", "commands.debug.function.noReturnRun": "<PERSON>y idzie prześledzić ze kōmyndōm „return run”", "commands.debug.function.success.multiple": "Prześledzōno %s k<PERSON>mynd ze %s fōnkcyji a je zachowano we %s", "commands.debug.function.success.single": "Prześledzōno %s kōmynd ze fōnkcyje „%s” a je zachowano we %s", "commands.debug.function.traceFailed": "<PERSON><PERSON> podarziło sie prześledzić fōnkcyje", "commands.debug.notRunning": "Profilowanie tikōw eszcze sie niy zaczło", "commands.debug.started": "Sztartnyto profilowanie tikōw", "commands.debug.stopped": "Sztopnyto profilowanie tikōw po %s zykōndach a %s tikach (%s tikōw na zykōndã)", "commands.defaultgamemode.success": "Sztandardowy tryb gry terŏzki je %s", "commands.deop.failed": "Nic sie niy zmiyniło. Grŏcz niyma ôperatōr", "commands.deop.success": "Grŏcz %s terŏzki już niyma ôperatōr serwera", "commands.dialog.clear.multiple": "Wysnŏżōno ôkiynko %s grŏczōm", "commands.dialog.clear.single": "Wysnŏżōno ôkiynko grŏczowi %s", "commands.dialog.show.multiple": "Pokŏzano ôkiynko %s grŏczōm", "commands.dialog.show.single": "Pokŏzano ôkiynko grŏczowi %s", "commands.difficulty.failure": "Nic sie niy zmiyniło. Ciynżkość już je nasztalowanŏ na: %s", "commands.difficulty.query": "Ciynż<PERSON>ść je %s", "commands.difficulty.success": "Nasztalowano ciynżkość na: %s", "commands.drop.no_held_items": "Byt niy ś<PERSON> trzimać żŏdnych rzeczy", "commands.drop.no_loot_table": "Byt „%s” niy mŏ tabule łupōw", "commands.drop.no_loot_table.block": "Blok „%s” niy mŏ tabule łupōw", "commands.drop.success.multiple": "Wyciepano %s rzeczy", "commands.drop.success.multiple_with_table": "Upuszczōno %s rzeczy ze tabule łupōw: %s", "commands.drop.success.single": "Upuszczōno %s %s", "commands.drop.success.single_with_table": "Upuszczōno %s %s ze tabule łupōw: %s", "commands.effect.clear.everything.failed": "Byt niy mŏ żŏdnych efektōw, kere by s<PERSON><PERSON><PERSON>", "commands.effect.clear.everything.success.multiple": "Usunyto wszycke efekty %s bytōm", "commands.effect.clear.everything.success.single": "Usunyto wszycke efekty bytowi %s", "commands.effect.clear.specific.failed": "Byt niy mŏ tego efektu", "commands.effect.clear.specific.success.multiple": "Usunyto efekt „%s” %s bytōm", "commands.effect.clear.specific.success.single": "Usunyto efekt „%s” bytowi %s", "commands.effect.give.failed": "Niy idzie nałożyć tego efektu (docelowy byt abo mŏ ôbstŏcie na efekty, abo już na niego działo silniyjszy efekt)", "commands.effect.give.success.multiple": "Nałożōno efekt „%s” na %s bytōw", "commands.effect.give.success.single": "Nałożōno efekt „%s” na byt %s", "commands.enchant.failed": "Nic sie niy zmiyniło. Abo byt niy mŏ rzeczy we rynce, abo niy szło nałoż<PERSON>ć z<PERSON>", "commands.enchant.failed.entity": "%s niyma isty byt lŏ tyj kōmyndy", "commands.enchant.failed.incompatible": "Niy idzie użyć tego zaklyńciŏ na %s", "commands.enchant.failed.itemless": "Byt %s niy trzimie żŏdnyj rzeczy", "commands.enchant.failed.level": "Wert %s je wielgszy niż nojwyższy poziōm spiyrany ôd tego z<PERSON>ŏ (%s)", "commands.enchant.success.multiple": "Nałożōno zaklyńcie „%s” na %s bytōw", "commands.enchant.success.single": "Nałożōno zaklyńcie „%s” na rzecz ôd grŏcza %s", "commands.execute.blocks.toobig": "Za moc blokōw we wyznaczōnyj zōnie — maksymalnie je %s, a zaznaczōno %s", "commands.execute.conditional.fail": "Test niyzdany", "commands.execute.conditional.fail_count": "Test niyzdany, liczba: %s", "commands.execute.conditional.pass": "Test zdany", "commands.execute.conditional.pass_count": "Test zdan<PERSON>, liczba: %s", "commands.execute.function.instantiationFailure": "Niy podarziło sie stworz<PERSON> wystōmpiyniŏ fōnkcyje %s: %s", "commands.experience.add.levels.success.multiple": "Przidzielōno %s poziomōw doświŏdczyniŏ %s grŏczōm", "commands.experience.add.levels.success.single": "Przidzielōno %s poziomōw doświŏdczyniŏ grŏczowi %s", "commands.experience.add.points.success.multiple": "Dano %s pōnktōw doświŏdczyniŏ %s grŏczōm", "commands.experience.add.points.success.single": "Dano %s pōnktōw doświŏdczyniŏ grŏczowi %s", "commands.experience.query.levels": "Grŏcz %s mŏ %s poziōmōw doświŏdczyniŏ", "commands.experience.query.points": "Grŏcz %s mŏ %s pōnktōw doświŏdczyniŏ", "commands.experience.set.levels.success.multiple": "Nasztalowano poziōm doświŏdczyniŏ na %s %s grŏczōm", "commands.experience.set.levels.success.single": "Nasztalowano poziōm doświŏdczyniŏ na %s grŏczowi %s", "commands.experience.set.points.invalid": "Niy idzie nasztalowa<PERSON> pōnktōw doświŏdczyniŏ na wiyncyj niż maksimum ôd ôbecnego poziōmu tego grŏcza", "commands.experience.set.points.success.multiple": "Nasztalowano pōnkty doświŏdczyniŏ na %s %s grŏczōm", "commands.experience.set.points.success.single": "Nasztalowano pōnkty doświŏdczyniŏ na %s grŏczowi %s", "commands.fill.failed": "<PERSON><PERSON> wyfolowano ze żŏdnych blokōw", "commands.fill.success": "Wyfolowano ze %s blokōw", "commands.fill.toobig": "Za moc blokōw we wyznaczōnyj zōnie — maksymalnie je %s, a zaznaczōno %s", "commands.fillbiome.success": "Nasztalowano biōmy pōmiyndzy %s, %s, %s a %s, %s, %s", "commands.fillbiome.success.count": "Nasztalowano %s biōmōw pōmiyndzy %s, %s, %s a %s, %s, %s", "commands.fillbiome.toobig": "Za moc blokōw we wyznaczōnyj zōnie — maksymalnie je %s, a zaznaczōno %s", "commands.forceload.added.failure": "Niy zaznaczōno żŏdnych czankōw ku wymuszōnym ladowaniu", "commands.forceload.added.multiple": "Zaznaczōno %s czankōw we %s ôd %s do %s ku wymuszōnym ladowaniu", "commands.forceload.added.none": "Niy znŏjdzono żŏdnych czankōw ze wymuszōnym ladowaniym we %s", "commands.forceload.added.single": "Zaznaczōno czank %s we %s ku wymuszōnym ladowaniu", "commands.forceload.list.multiple": "Znŏjdzōno %s czankōw ze wymuszōnym ladowaniym we %s na %s", "commands.forceload.list.single": "Znŏjdzono jedyn czank ze wymuszōnym ladowaniym we %s na %s", "commands.forceload.query.failure": "Czank na %s we %s niyma zaznaczōny ku wymuszōnym ladowaniu", "commands.forceload.query.success": "Czank na %s we %s je zaznaczōny ku wymuszōnym ladowaniu", "commands.forceload.removed.all": "Ôdznaczōno wszycke czanki ze wymuszōnym ladowaniym we %s", "commands.forceload.removed.failure": "Żŏdyn czank niy ôstoł usunyty przi wymuszōnym ladowaniu", "commands.forceload.removed.multiple": "Ôdznaczōno %s czankōw we %s ôd %s do %s ôd wymuszōnego ladowaniŏ", "commands.forceload.removed.single": "Ôdznaczōno czank %s we %s ôd wym<PERSON><PERSON><PERSON><PERSON><PERSON> ladowaniŏ", "commands.forceload.toobig": "Za moc czankōw we wyznaczōnyj zōnie — maksymalnie je %s, a zaznaczōno %s", "commands.function.error.argument_not_compound": "Felernŏ zorta argumyntu: „%s” — spodziywano słożōnyj etykety", "commands.function.error.missing_argument": "Brakuje argumyntu %2$s lŏ fōnkcyje „%1$s”", "commands.function.error.missing_arguments": "Brakuje argumyntōw lŏ fōnkcyje „%s”", "commands.function.error.parse": "<PERSON><PERSON> podarziło sie st<PERSON><PERSON><PERSON> wystōmpiyniŏ makra „%s” — feler przi regiyrowaniu kōmyndy „%s”: %s", "commands.function.instantiationFailure": "<PERSON>y podarziło sie stw<PERSON><PERSON><PERSON> wystōmpiyniŏ fōnkcyje „%s”: %s", "commands.function.result": "Fōnkcyjŏ „%s” zwrōciyła wert %s", "commands.function.scheduled.multiple": "Regiyrowanie fōnkcyji: %s", "commands.function.scheduled.no_functions": "Niy znŏjdzōno żŏdnyj funkcyje ô mianie: „%s”", "commands.function.scheduled.single": "Regiyrowanie fōnkcyje „%s”…", "commands.function.success.multiple": "Zregiyrowano %s kōmynd ze %s fōnkcyji", "commands.function.success.multiple.result": "Zregiyrowano %s fōnkcyji", "commands.function.success.single": "Zregiyrowano %s kōmynd ze fōnkcyje „%s”", "commands.function.success.single.result": "Fōnkcyjŏ „%2$s” zwrōciyła wert %1$s", "commands.gamemode.success.other": "Tryb gry ôd grŏcza %s ôstoł zmiyniōny na %s", "commands.gamemode.success.self": "Zmiyniōno tryb gry na %s", "commands.gamerule.query": "Regla gry %s ôbecnie je nasztalowanŏ na: %s", "commands.gamerule.set": "Nasztalowano reglã gry %s na: %s", "commands.give.failed.toomanyitems": "<PERSON><PERSON> <PERSON><PERSON> wiyncyj jak %s %s", "commands.give.success.multiple": "Dano %s × %s %s grŏczōm", "commands.give.success.single": "Dano %s × %s grŏczowi %s", "commands.help.failed": "Niyznanŏ kōmynda abo brakuje uprŏwniyń", "commands.item.block.set.success": "Zastōmpiōno pole %s, %s, %s rzeczōm %s", "commands.item.entity.set.success.multiple": "Zastōmpiōno pole %s bytōm rzeczōm %s", "commands.item.entity.set.success.single": "Zastōmpiōno pole %s rzeczōm %s", "commands.item.source.no_such_slot": "Zdrzōdło niy mŏ pola %s", "commands.item.source.not_a_container": "Byt na zdrzōdłowym położyniu %s, %s, %s niyma pojymnik", "commands.item.target.no_changed.known_item": "Żŏdyn ze bytōw niy przijmnył rzeczy %s we polu %s", "commands.item.target.no_changes": "Żŏdyn ze bytōw niy przijmnył rzeczy we polu %s", "commands.item.target.no_such_slot": "Byt niy mŏ pola %s", "commands.item.target.not_a_container": "Byt na docylowym położyniu %s, %s, %s niyma pojymnik", "commands.jfr.dump.failed": "Feler przi ściepowaniu wyniku profilowaniŏ JFR: %s", "commands.jfr.start.failed": "<PERSON>y podarziło sie sztartnyć profilowaniŏ JFR", "commands.jfr.started": "Sztartnyto profilowanie JFR", "commands.jfr.stopped": "Sztopnyto profilowanie JFR; ściepano wynik ku dataji %s", "commands.kick.owner.failed": "Niy idzie wyciepać włościciela serwera we grze LAN", "commands.kick.singleplayer.failed": "Niy idzie wyciepać grŏcza we jednoperzōnowyj grze", "commands.kick.success": "Wyciepano grŏcza %s za „%s”", "commands.kill.success.multiple": "Ubito %s bytōw", "commands.kill.success.single": "Ubito %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Połōnczōnych je %s ze maksimum %s grŏczy: %s", "commands.locate.biome.not_found": "Niy znŏjdzono żŏdnego biōmu zorty „%s” na ôsiōngalnyj ôdlygłości", "commands.locate.biome.success": "Nojbliższy biōm zorty „%s” sie znŏjduje na %s (%s blokōw stōnd)", "commands.locate.poi.not_found": "Niy znŏjdzōno żŏdnych pōnktōw uwŏgi zorty „%s” na ôsiōngalnyj ôdlygłości", "commands.locate.poi.success": "Nojbliższy pōnkt uwŏgi zorty „%s” sie znŏjduje na %s (%s blokōw stōnd)", "commands.locate.structure.invalid": "Niy ma żŏdnyj struktury zorty „%s”", "commands.locate.structure.not_found": "Niy podarziło sie znŏjś żŏdnyj struktury zorty „%s” po blisku", "commands.locate.structure.success": "Nojbliższŏ struktura zorty „%s” sie znŏjduje na %s (%s blokōw stōnd)", "commands.message.display.incoming": "%s szeptŏ ku Ciã: %s", "commands.message.display.outgoing": "Szeptŏsz ku %s: %s", "commands.op.failed": "Nic sie niy zmiyniło. Grŏcz już je ôperatōr", "commands.op.success": "Grŏcz %s terŏzki je ôperatōr serwera", "commands.pardon.failed": "Nic nie niy zmiyniło. Grŏcz niyma bloknyty", "commands.pardon.success": "Ôdblokowano grŏcza %s", "commands.pardonip.failed": "Nic sie niy zmiyniło. Ta adresa IP niyma bloknytŏ", "commands.pardonip.invalid": "Felernŏ adresa IP", "commands.pardonip.success": "Ôdblokowano IP %s", "commands.particle.failed": "Ta czōnstka niy bōła żŏdnymu widzialnŏ", "commands.particle.success": "Czōnstka %s terŏzki sie pokŏzuje", "commands.perf.alreadyRunning": "Profilowanie skutności już je we gangu", "commands.perf.notRunning": "Profilowanie skutności jeszcze sie niy zaczło", "commands.perf.reportFailed": "<PERSON><PERSON> podarziło sie stworz<PERSON> raportu debugowaniŏ", "commands.perf.reportSaved": "Zachowano raport debugowaniŏ we %s", "commands.perf.started": "Sztarnyto 10-sekundowe profilowanie lajstōngu (użyj kōmyndy „/perf sztop”, coby sz<PERSON> zaw<PERSON>)", "commands.perf.stopped": "Sztopnyto profilowanie lajstōngu po %s zykōndach a %s tikach (%s tikōw na zykōndã)", "commands.place.feature.failed": "<PERSON><PERSON> podarziło sie umiyścić tworu", "commands.place.feature.invalid": "Niy ma żŏdnego tworu zorty „%s”", "commands.place.feature.success": "Umiyszczōno twōr „%s” na %s, %s, %s", "commands.place.jigsaw.failed": "<PERSON><PERSON> podarziło sie wygynerować kōnstrukcyje", "commands.place.jigsaw.invalid": "<PERSON><PERSON> ma żŏdnyj pule szablōnōw zorty „%s”", "commands.place.jigsaw.success": "Umiyszczōno kōnstrukcyjõ na %s, %s, %s", "commands.place.structure.failed": "<PERSON><PERSON> podarziło sie <PERSON> struktury", "commands.place.structure.invalid": "Niy ma żŏdnyj struktury zorty „%s”", "commands.place.structure.success": "Umiyszczōno strukturã „%s” na %s, %s, %s", "commands.place.template.failed": "<PERSON><PERSON> podarziło sie <PERSON> s<PERSON>", "commands.place.template.invalid": "Niy ma żŏdnego szablōnu ze ID „%s”", "commands.place.template.success": "Zaladowano szablōn „%s” na %s, %s, %s", "commands.playsound.failed": "<PERSON>n klang je za fest weg, coby go usł<PERSON><PERSON><PERSON>", "commands.playsound.success.multiple": "Zagrŏto klang „%s” %s grŏczōm", "commands.playsound.success.single": "Zagrŏto klang „%s” grŏczowi %s", "commands.publish.alreadyPublished": "Mocperzōnowŏ gra już dzioło na porcie %s", "commands.publish.failed": "Niy idzie założyć lokalnyj gry", "commands.publish.started": "Założōno grã na porcie %s", "commands.publish.success": "Mocperzōnowŏ gra terŏzki dzioło na porcie %s", "commands.random.error.range_too_large": "<PERSON><PERSON><PERSON><PERSON> ôd zŏkrysu lŏ losowego wertu niy śmi być wielgszy niż 2147483646", "commands.random.error.range_too_small": "Zaczōntek ôd zŏkrysu losowego wertu musi być przinojmnij 2", "commands.random.reset.all.success": "Zresetowano %s losowych sekwyncyji", "commands.random.reset.success": "Zresetowano losowõ sekwyncyjõ %s", "commands.random.roll": "Grŏcz %s wylosowoł %s (spōmiyndzy %s a %s)", "commands.random.sample.success": "Wylosowany wert: %s", "commands.recipe.give.failed": "<PERSON><PERSON> podarziło sie nauczyć żŏdnego nowego receptu", "commands.recipe.give.success.multiple": "Ôdbloknyto %s receptōw %s grŏczōm", "commands.recipe.give.success.single": "Ôdbloknyto %s receptōw grŏczowi %s", "commands.recipe.take.failed": "<PERSON>y podarziło sie zapōmnieć żŏdnego receptu", "commands.recipe.take.success.multiple": "Ôdebrano %s receptōw %s grŏczom", "commands.recipe.take.success.single": "Ôdebrano %s receptōw grŏczowi %s", "commands.reload.failure": "Niy podarziło sie przeladować danych; zatrzimano stare dane", "commands.reload.success": "Przeladowano!", "commands.ride.already_riding": "Byt %s ju<PERSON>d<PERSON> bytu %s", "commands.ride.dismount.success": "Byt %s us<PERSON><PERSON> bytu %s", "commands.ride.mount.failure.cant_ride_players": "Grŏcze niy śmiōm <PERSON><PERSON>", "commands.ride.mount.failure.generic": "Byt %s niy mōg dosiōnś bytu %s", "commands.ride.mount.failure.loop": "Byt niy ś<PERSON> samego siebie ani żŏdnego ze swojich pasażyrōw", "commands.ride.mount.failure.wrong_dimension": "<PERSON><PERSON> <PERSON> bytu we <PERSON>szym wym<PERSON>ze", "commands.ride.mount.success": "Byt %s dosiŏd bytu %s", "commands.ride.not_riding": "%s niy siedzi we żŏdnym pojeździe", "commands.rotate.success": "Ôbrōcōno %s", "commands.save.alreadyOff": "Zachowywanie już je wyłōnczōne", "commands.save.alreadyOn": "Zachowywanie już je załōnczōne", "commands.save.disabled": "Samozachowywanie terŏzki je wyłōnczōne", "commands.save.enabled": "Samozachowywanie terŏzki je załōnczōne", "commands.save.failed": "<PERSON><PERSON> id<PERSON> z<PERSON> gry (mŏsz zadość placu na dysku?)", "commands.save.saving": "Zachowywanie gry (to może chwilã potwać!)", "commands.save.success": "Zachowano grã", "commands.schedule.cleared.failure": "Niy ma żŏdnych ôbsztalowań ze ID %s", "commands.schedule.cleared.success": "Usunyto %s ôbsztalowań ze ID %s", "commands.schedule.created.function": "Ôbsztalōwano fōnkcyjõ „%s” za %s tikōw ô %s czasu gry", "commands.schedule.created.tag": "Ôbsztalōwano etyketã „%s” za %s tikōw ô %s czasu gry", "commands.schedule.macro": "<PERSON><PERSON> <PERSON> makra", "commands.schedule.same_tick": "<PERSON><PERSON> <PERSON> ôbecnego tiku", "commands.scoreboard.objectives.add.duplicate": "<PERSON><PERSON> istnieje cyl ô tym mianie", "commands.scoreboard.objectives.add.success": "Stworzōno nowy cyl: %s", "commands.scoreboard.objectives.display.alreadyEmpty": "<PERSON>c sie niy zmiyniło. To pole już je prōżne", "commands.scoreboard.objectives.display.alreadySet": "<PERSON>c sie niy zmiyniło. To pole już pokazuje tyn cyl", "commands.scoreboard.objectives.display.cleared": "Wykasiyrowano wszycke cyle we polu %s", "commands.scoreboard.objectives.display.set": "Nasztalowano pole %s ku pokazowaniu cylu %s", "commands.scoreboard.objectives.list.empty": "<PERSON><PERSON> <PERSON>ŏdnych cylōw", "commands.scoreboard.objectives.list.success": "Je %s cylōw: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Wyłōnczōno samoaktualizacyjõ wyświytlanego cylu %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Załōnczōno samoaktualizacyjõ wyświytlanego cylu %s", "commands.scoreboard.objectives.modify.displayname": "Zmiyniōno pokazowane miano %s na %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Wykasiyrowano sztandardowy nōmerowy format ôd cylu %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Zmiyniōno sztandardowy nōmerowy format ôd cylu %s", "commands.scoreboard.objectives.modify.rendertype": "Zmiyniōno zortã renderowaniŏ cylu %s", "commands.scoreboard.objectives.remove.success": "Usunyto cyl „%s”", "commands.scoreboard.players.add.success.multiple": "Dodano %s ku %s %s bytōm", "commands.scoreboard.players.add.success.single": "Przidano %s ku %s grŏczowi %s (terŏzki je %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Prziwrōcōno pokazowane miano %s bytōm we %s", "commands.scoreboard.players.display.name.clear.success.single": "Prziwrōcōno pokazowane miano bytowi %s we %s", "commands.scoreboard.players.display.name.set.success.multiple": "Zmiyniōno pokazowane miano na „%s” %s bytōm we %s", "commands.scoreboard.players.display.name.set.success.single": "Zmiyniōno pokazowane miano na „%s” bytowi %s we %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Wykasiyrowano nōmerowy format u %s bytōw we %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Wykasiyrowano nōmerowy format u bytu %s we %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Zmiyniōno nōmerowy format u %s bytōw we %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Zmiyniōno nōmerowy format u bytu %s we %s", "commands.scoreboard.players.enable.failed": "Nic sie niy zmiyniło. Tyn aktywatōr już je załōnczōny", "commands.scoreboard.players.enable.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> idzie yno cyle, kere ś<PERSON><PERSON><PERSON> by<PERSON> a<PERSON>e", "commands.scoreboard.players.enable.success.multiple": "Załōnczōno aktywatōr %s %s bytōm", "commands.scoreboard.players.enable.success.single": "Załōnczōno aktywatōr %s bytowi %s", "commands.scoreboard.players.get.null": "Niy idzie erbnyć wertu %s ôd grŏcza %s — niy mŏ żŏdnego wertu", "commands.scoreboard.players.get.success": "Grŏcz %s mŏ %s %s", "commands.scoreboard.players.list.empty": "Niy ma żŏdnych śledzōnych bytōw", "commands.scoreboard.players.list.entity.empty": "Grŏcz %s niy mŏ żŏdnych wynikōw ku pokŏzaniu", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "Grŏcz %s mŏ %s wynikōw:", "commands.scoreboard.players.list.success": "Je %s śledz<PERSON>nych bytōw: %s", "commands.scoreboard.players.operation.success.multiple": "Zaktualiziyrowano %s %s bytōm", "commands.scoreboard.players.operation.success.single": "Nasztalowano %s bytowi %s na %s", "commands.scoreboard.players.remove.success.multiple": "Usunyto %s ze %s %s bytōm", "commands.scoreboard.players.remove.success.single": "Usunyto %s ze %s bytowi %s (terŏzki je %s)", "commands.scoreboard.players.reset.all.multiple": "Zresetowano pōnktacyjõ %s bytōm", "commands.scoreboard.players.reset.all.single": "Zresetowano pōnktacyjõ bytowi %s", "commands.scoreboard.players.reset.specific.multiple": "Zresetowano %s %s bytōm", "commands.scoreboard.players.reset.specific.single": "Zresetowano %s bytowi %s", "commands.scoreboard.players.set.success.multiple": "Nasztalowano %s %s bytōm na %s", "commands.scoreboard.players.set.success.single": "Nasztalowano %s bytowi %s na %s", "commands.seed.success": "Ziŏrno: %s", "commands.setblock.failed": "<PERSON><PERSON> <PERSON> na<PERSON> bloku", "commands.setblock.success": "Zamiyniōno blok, kery stoł na %s, %s, %s", "commands.setidletimeout.success": "Limit czasu bezczynności grŏcza terŏzki je %s minut", "commands.setidletimeout.success.disabled": "Limit czasu bezczynności grŏcza terŏzki je wyłōnczōny", "commands.setworldspawn.failure.not_overworld": "<PERSON><PERSON><PERSON> pōnkt ôdrŏdzaniŏ yno na wiyrchu", "commands.setworldspawn.success": "Nasztalowano świŏtowy pōnkt ôdrŏdzaniŏ na %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Nasztalowano pōnkt ôdrŏdzaniŏ na %s, %s, %s [%s] we wymiarze %s %s grŏczōm", "commands.spawnpoint.success.single": "Nasztalowano pōnkt ôdrŏdzaniŏ na %s, %s, %s [%s] we wymiarze %s grŏczowi %s", "commands.spectate.not_spectator": "Grŏcz %s niyma we trybie cuszauera", "commands.spectate.self": "<PERSON><PERSON> <PERSON><PERSON> s<PERSON>", "commands.spectate.success.started": "Terŏz śledzisz: %s", "commands.spectate.success.stopped": "Zakōńczōno ôglōndanie bytu", "commands.spreadplayers.failed.entities": "Niy idzie rozmiyścić %s grŏczy wele %s, %s (za moc bytōw ku placu — sprōbuj zmyńszyć liczbã na co nojwyżyj %s)", "commands.spreadplayers.failed.invalid.height": "Wert niy śmi być myńszy niż świŏtowe minimum: %s", "commands.spreadplayers.failed.teams": "Niy idzie rozmiyścić %s manszaftōw wele %s, %s (za moc bytōw ku placu — sprōbuj zmyńszyć liczbã na co nojwyżyj %s)", "commands.spreadplayers.success.entities": "Rozmiyszczōno %s bytōw wele %s, %s przi strzed<PERSON>j <PERSON>dlygłości %s blokōw jedyn ôd drugigo", "commands.spreadplayers.success.teams": "Rozmiyszczōno %s manszaftōw wele %s, %s przi strzednij ôdlygłości %s blokōw jedna ôd drugij", "commands.stop.stopping": "Sztopowanie serwera", "commands.stopsound.success.source.any": "Sztopnyto wszycke klangi zorty „%s”", "commands.stopsound.success.source.sound": "Sztopnyto klang „%s” ze zdrzōdła „%s”", "commands.stopsound.success.sourceless.any": "Sztopnyto wszycke klangi", "commands.stopsound.success.sourceless.sound": "Sztopnyto klang „%s”", "commands.summon.failed": "<PERSON><PERSON> <PERSON><PERSON> swo<PERSON> bytu", "commands.summon.failed.uuid": "<PERSON>y podarziło sie swołać bytu — UUID ôstało powtōrzōne", "commands.summon.invalidPosition": "Felerne położynie ku swołaniu", "commands.summon.success": "Swołano nowy byt zorty „%s”", "commands.tag.add.failed": "Byt abo już mŏ tã etyketã, abo ich mŏ za moc ", "commands.tag.add.success.multiple": "Przidano etyketã „%s” %s bytōm", "commands.tag.add.success.single": "Przidano etyketã „%s” bytowi %s", "commands.tag.list.multiple.empty": "Niy ma żŏdnych tagōw u tych %s bytōw", "commands.tag.list.multiple.success": "Te %s bytōw mŏ społym %s etyket: %s", "commands.tag.list.single.empty": "Grŏcz %s niy mŏ żŏdnych etyket", "commands.tag.list.single.success": "Byt %s mŏ %s etyket: %s", "commands.tag.remove.failed": "Byt niy mŏ tyj etykety", "commands.tag.remove.success.multiple": "Usunyto etyketã „%s” %s bytōm", "commands.tag.remove.success.single": "Usunyto etyketã „%s” bytowi %s", "commands.team.add.duplicate": "<PERSON><PERSON> istnieje manszaft ô tym mianie", "commands.team.add.success": "Stworzōno manszaft: %s", "commands.team.empty.success": "Usunyto %s człōnkōw ze manszaftu %s", "commands.team.empty.unchanged": "Nic sie niy zmiyniło. Tyn manszaft już je prōżny", "commands.team.join.success.multiple": "Wkludzōno %s człōnkōw ku manszaftowi %s", "commands.team.join.success.single": "Wkludzōno grŏcza %s ku manszaftowi %s", "commands.team.leave.success.multiple": "Usunyto społym %s człōnkōw ze manszaftōw", "commands.team.leave.success.single": "Usunyto grŏcza %s ze lecy jakigo manszaftu", "commands.team.list.members.empty": "Niy ma żŏdnych człōnkōw we manszafcie %s", "commands.team.list.members.success": "Manszaft %s mŏ %s człōnkōw: %s", "commands.team.list.teams.empty": "<PERSON>y ma żŏdnych manszaftōw", "commands.team.list.teams.success": "Je %s manszaftōw: %s", "commands.team.option.collisionRule.success": "Prŏwidło kolizyje u manszaftu %s terŏzki je „%s”", "commands.team.option.collisionRule.unchanged": "<PERSON>c sie niy <PERSON>, prŏwidło kolizyje już mŏ taki wert", "commands.team.option.color.success": "Zmiyniōno farbã manszaftu %s na %s", "commands.team.option.color.unchanged": "<PERSON>c sie niy zmiyniło. Tyn manszaft już je we tyj farbie", "commands.team.option.deathMessageVisibility.success": "Widoczność kōmunikatu ô śmiyrci lŏ manszaftu %s terŏzki je „%s”", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON>c sie niy <PERSON>, w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kōmunikatu ô zgōnach już takŏ je", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON>c sie niy zmiyniło, bratobōjczy ôgyń już je przizwolōny u tego manszaftu", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON>c sie niy zmiyniło, bratobōjczy ôgyń już je zakŏzany u tego manszaftu", "commands.team.option.friendlyfire.disabled": "Zakŏzano bratobōjczego ôgnia u manszaftu %s", "commands.team.option.friendlyfire.enabled": "Przizwolōno na bratobōjczy ôgyń u manszaftu %s", "commands.team.option.name.success": "Zaktualiziyrowano miano manszaftu %s", "commands.team.option.name.unchanged": "<PERSON>c sie niy zmiyniło. Tyn manszaft już mŏ take miano", "commands.team.option.nametagVisibility.success": "Widzialność znacznikōw u manszaftu %s terŏzki je „%s”", "commands.team.option.nametagVisibility.unchanged": "<PERSON>c sie niy z<PERSON>, w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> znacznikōw już tak<PERSON> je", "commands.team.option.prefix.success": "Nasztalowano prefiks manszaftu na %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Nic sie niy z<PERSON>yn<PERSON>ł<PERSON>, we tym manszafcie już sie niy doziyrŏ niywidzialnych kamratōw", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Nic sie niy zmiynił<PERSON>, we tym manszafcie już sie doziyrŏ niywidzialnych kamratōw", "commands.team.option.seeFriendlyInvisibles.disabled": " We manszafcie %s już sie niy doziyrŏ niywidzialnych kamratōw", "commands.team.option.seeFriendlyInvisibles.enabled": " We manszafcie %s terŏzki sie doziyrŏ niywidzialnych kamratōw", "commands.team.option.suffix.success": "Nasztalowano sufiks manszaftu na %s", "commands.team.remove.success": "Usunyto manszaft %s", "commands.teammsg.failed.noteam": "<PERSON><PERSON><PERSON><PERSON> we ż<PERSON><PERSON><PERSON>, coby ku niymu mōc posła<PERSON> w<PERSON>", "commands.teleport.invalidPosition": "Felerne położynie ku teleportacyji", "commands.teleport.success.entity.multiple": "Przeteleportowano %s bytōw ku %s", "commands.teleport.success.entity.single": "Przeteleportowano %s ku %s", "commands.teleport.success.location.multiple": "Przeteleportowano %s bytōw na %s, %s, %s", "commands.teleport.success.location.single": "Przeteleportowano %s na %s, %s, %s", "commands.test.batch.starting": "Sztartowanie strzodowiska %s, partyjŏ %s", "commands.test.clear.error.no_tests": "Niy ma żŏdnych testōw ku wysnŏżyniu", "commands.test.clear.success": "Wysnŏżōno %s struktur", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "<PERSON><PERSON><PERSON>, coby sko<PERSON><PERSON> ku schowku", "commands.test.create.success": "Stworzōno nasztalowanie lŏ testu %s", "commands.test.error.no_test_containing_pos": "<PERSON>y idzie znŏjś wystōmpiyniŏ testu, kery p<PERSON> %s, %s, %s", "commands.test.error.no_test_instances": "Niy ma żŏdnych wystōmpiyń testu", "commands.test.error.non_existant_test": "<PERSON>y idzie znŏjś testu: „%s”", "commands.test.error.structure_not_found": "Niy idzie znŏjś testowej struktury: „%s”", "commands.test.error.test_instance_not_found": "<PERSON>y idzie znŏjś bloku wystōmpiyniŏ testu", "commands.test.error.test_instance_not_found.position": "Niy idzie znŏjś bloku wystōmpiyniŏ testu na %s, %s, %s", "commands.test.error.too_large": "<PERSON>iel<PERSON><PERSON>ć struktury musi być myńszŏ niż %s blokōw po kŏżdyj ôśce", "commands.test.locate.done": "Zakōńczōno lokaliziyrowanie — znŏjdzono %s struktur", "commands.test.locate.found": "Znŏjdzōno strukturã na: %s (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: %s)", "commands.test.locate.started": "Sztartnyto lokal<PERSON> testowe struktury. To może chwilã zajmnyć…", "commands.test.no_tests": "<PERSON><PERSON> ma żŏdnych testōw ku zregiyrowaniu", "commands.test.relative_position": "Położynie zglydne %s: %s", "commands.test.reset.error.no_tests": "Niy znŏjdzōno żŏdnych testōw do zresetowaniŏ", "commands.test.reset.success": "Zresetowano %s struktur", "commands.test.run.no_tests": "Niy znŏjdzono testōw", "commands.test.run.running": "Regiyrowanie %s testōw…", "commands.test.summary": "Test gry je fertich! Zregiyrowano %s testōw", "commands.test.summary.all_required_passed": "<PERSON><PERSON><PERSON><PERSON> wymŏgane testy sie powiodły :)", "commands.test.summary.failed": "%s wymŏganych testōw sie niy powiodło :(", "commands.test.summary.optional_failed": "%s warōnkowych testōw sie niy powiodło", "commands.tick.query.percentiles": "Procyntyle — P50: %sms / P95: %sms / P99: %sms / prōbka: %s", "commands.tick.query.rate.running": "Docylowŏ frekwyncyjŏ tikōw: %s na zykōndã\nStrzedni czas na tik: %sms (docylowy: %sms)", "commands.tick.query.rate.sprinting": "Docylowŏ frekwyncyjŏ tikōw: %s na zykōndã (i<PERSON>rowane, yno za ôdniysiynie).\nStrzedni czas na tik: %sms", "commands.tick.rate.success": "Nasztalowano docylowõ frekwyncyjõ tikōw na %s na zykōndã", "commands.tick.sprint.report": "Zakōńczōno prziśpiyszynie ze %s tikami na zykōndã abo %s milizykōndami na tik", "commands.tick.sprint.stop.fail": "Niy ma żŏdnych prziśpiyszyń tikōw we gangu", "commands.tick.sprint.stop.success": "Przerwano prziśpiyszynie tikōw", "commands.tick.status.frozen": "Gra je zamrożōnŏ", "commands.tick.status.lagging": "<PERSON><PERSON>, ale niy dołazi ku docylowyj frekwyncyji tikōw", "commands.tick.status.running": "Gra fōnguje normalnie", "commands.tick.status.sprinting": "Gra je prziśpieszōnŏ", "commands.tick.step.fail": "Niy idzie zregiyrować przeskoku gry — nojprzōd przinŏleży sie jã zamro<PERSON>ć", "commands.tick.step.stop.fail": "<PERSON><PERSON><PERSON><PERSON> sie żŏdyn tik", "commands.tick.step.stop.success": "Przerwano ôbecny przeskok tikōw", "commands.tick.step.success": "Ôdmierzanie %s tikōw…", "commands.time.query": "Czas je %s", "commands.time.set": "Nasztalowano czas na %s", "commands.title.cleared.multiple": "Usunyto title %s grŏczōm", "commands.title.cleared.single": "Usunyto titel grŏczowi %s", "commands.title.reset.multiple": "Zresetowano nasztalowania titlōw %s grŏczōm", "commands.title.reset.single": "Zresetowano nasztalowania titlu grŏczowi %s", "commands.title.show.actionbar.multiple": "Pokazowanie nowego titla ôd akcyjowego pŏska %s grŏczōm…", "commands.title.show.actionbar.single": "Pokazowanie nowego titla ôd akcyjowego pŏska grŏczowi %s…", "commands.title.show.subtitle.multiple": "Pokazowanie nowego podtitla %s grŏczōm", "commands.title.show.subtitle.single": "Pokazowanie nowego podtitla grŏczowi %s", "commands.title.show.title.multiple": "Pokazowanie nowego titla %s grŏczōm", "commands.title.show.title.single": "Pokazowanie nowego titla grŏczowi %s", "commands.title.times.multiple": "Zmiyniōno ôkrys w<PERSON>wiytlaniŏ titlōw %s grŏczōm", "commands.title.times.single": "Zmiyniōno ôkrys w<PERSON>wi<PERSON>laniŏ titla grŏczowi %s", "commands.transfer.error.no_players": "<PERSON><PERSON>z podać przinojmnij jednego grŏcza ku przeniysiyniu", "commands.transfer.success.multiple": "Przeniysiōno %s grŏczy na %s:%s", "commands.transfer.success.single": "Przeniysiōno grŏcza %s na %s:%s", "commands.trigger.add.success": "Aktywowano %s (przidano %s ku wercie)", "commands.trigger.failed.invalid": "Śmisz aktywować yno cyle zorty „aktywatōr”", "commands.trigger.failed.unprimed": "Jeszcze niy śmisz aktywować tego cylu", "commands.trigger.set.success": "Aktywowano %s (nasztalowano wert na %s)", "commands.trigger.simple.success": "Aktywowano %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "Niy ma żŏdnych wegwajzerōw we %s", "commands.waypoint.list.success": "Je %s wegwajzerōw we %s: %s", "commands.waypoint.modify.color": "Farba wegwajzera terŏzki je %s", "commands.waypoint.modify.color.reset": "Zresetuj farbã wegwajzera", "commands.waypoint.modify.style": "Zmiyniōno wyglōnd wegwajzera", "commands.weather.set.clear": "Nasztalowano pogodã na klarownõ", "commands.weather.set.rain": "Nasztalowano pogodã na dyszcz", "commands.weather.set.thunder": "Nasztalowano pogodã na grōm", "commands.whitelist.add.failed": "Grŏcz już je na biołyj spisce", "commands.whitelist.add.success": "Wkludzono %s ku biołyj spisce", "commands.whitelist.alreadyOff": "Biołŏ spiska już je wyłōnczōnŏ", "commands.whitelist.alreadyOn": "Biołŏ spiska już je załōnczōnŏ", "commands.whitelist.disabled": "Biołŏ spiska je wyłōnczōnŏ", "commands.whitelist.enabled": "Biołŏ spiska je załōnczōnŏ", "commands.whitelist.list": "Na biołyj spisce je %s grŏczy: %s", "commands.whitelist.none": "Niy ma żŏdnego grŏcza na biołyj spisce", "commands.whitelist.reloaded": "Przeladowano biołõ spiskã", "commands.whitelist.remove.failed": "Grŏcz niyma na biołyj spisce", "commands.whitelist.remove.success": "Usunyto grŏcza %s ze biołyj spiski", "commands.worldborder.center.failed": "Nic sie niy zmiyniło. Granica już je samkej wypojstrzodkowanŏ", "commands.worldborder.center.success": "Nasztalowano postrzodek granicy świŏta na %s, %s", "commands.worldborder.damage.amount.failed": "<PERSON>c sie niy zmiyniło. Pufer zraniyń za granicōm świata już mŏ taki wert", "commands.worldborder.damage.amount.success": "Nasztalowano zraniynia za granicōm świŏta na %s co kŏżdõ zykōndã", "commands.worldborder.damage.buffer.failed": "<PERSON>c sie niy zmiyniło. Pufer zraniyń za granicōm świata już mŏ takõ długość", "commands.worldborder.damage.buffer.success": "Nasztalowano pufer zraniyń za granicōm świŏta na %s blokōw", "commands.worldborder.get": "Granica świŏta ôbecnie mŏ szyrokość %s blokōw", "commands.worldborder.set.failed.big": "Granica świŏta niy może być wielgszŏ niż %s blokōw", "commands.worldborder.set.failed.far": "Granica świŏta niy może siōngać dalij niźli %s blokōw", "commands.worldborder.set.failed.nochange": "<PERSON>c sie niy zmiyniło. Granica świŏta już mŏ tã wielgość", "commands.worldborder.set.failed.small": "Granica świŏta niy może by<PERSON> jak jedyn blok", "commands.worldborder.set.grow": "Zwiynkszanie szyrokości granicy świŏta na %s blokōw we %s zykōnd…", "commands.worldborder.set.immediate": "Nasztalowano szyrokość granicy na %s blokōw", "commands.worldborder.set.shrink": "Zmyńszanie szyrokości granicy świŏta na %s blokōw we %s zykōnd…", "commands.worldborder.warning.distance.failed": "Nic sie niy zmiyniło. Upozorniynie granicy świŏta już mŏ takõ <PERSON>dly<PERSON>ł<PERSON>ś<PERSON>", "commands.worldborder.warning.distance.success": "Nasztalowano ôdlygłość ôstrzeżyniŏ ô granicy świŏta na %s blokōw", "commands.worldborder.warning.time.failed": "Nic sie niy zmiyniło. Upozorniynie granicy świŏta już tela twŏ", "commands.worldborder.warning.time.success": "Nasztalowano czas ôstrzeżyniŏ ô granicy świŏta na %s blokōw", "compliance.playtime.greaterThan24Hours": "Grŏsz już cołkõ dobã", "compliance.playtime.hours": "Grŏsz już ôd %s godzin", "compliance.playtime.message": "Nadmiar g<PERSON> może zaburzić raj<PERSON> d<PERSON><PERSON>", "connect.aborted": "Sztopnyto", "connect.authorizing": "Logowanie…", "connect.connecting": "Łōnczynie ze serwerym…", "connect.encrypting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "connect.failed": "<PERSON><PERSON> podarziło sie połōnczyć ze serwerym", "connect.failed.transfer": "Połōnczynie sie niy podarziło, kej przenoszōno na serwer", "connect.joining": "Dołōnczanie ku świŏtu…", "connect.negotiating": "<PERSON><PERSON><PERSON><PERSON>…", "connect.reconfiging": "Przestŏwianie…", "connect.reconfiguring": "Przestŏwianie…", "connect.transferring": "Przenoszynie na nowy serwer…", "container.barrel": "Faska", "container.beacon": "<PERSON><PERSON>", "container.beehive.bees": "Biny: %s/%s", "container.beehive.honey": "Miōd: %s/%s", "container.blast_furnace": "<PERSON><PERSON>", "container.brewing": "Sztynder ku warzyniu", "container.cartography_table": "<PERSON><PERSON><PERSON> kartografa", "container.chest": "<PERSON><PERSON><PERSON>", "container.chestDouble": "Wielgŏ kastla", "container.crafter": "Wytwŏrzŏcz", "container.crafting": "Wyrŏbianie", "container.creative": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.dispenser": "Strz<PERSON><PERSON><PERSON>", "container.dropper": "Podowŏcz", "container.enchant": "<PERSON><PERSON><PERSON><PERSON>", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s sztikōw lazurytu", "container.enchant.lapis.one": "1 lazuryt", "container.enchant.level.many": "%s poziōmōw zaklinaniŏ", "container.enchant.level.one": "1 poziōm zaklinaniŏ", "container.enchant.level.requirement": "Wymŏgany poziōm: %s.", "container.enderchest": "<PERSON><PERSON><PERSON>", "container.furnace": "Żeleźniŏk", "container.grindstone_title": "Sprŏw a ôdeklnij", "container.hopper": "Lyjŏk", "container.inventory": "Inwyntŏrz", "container.isLocked": "Pojymnik %s je zawrzity!", "container.lectern": "<PERSON><PERSON>", "container.loom": "Krosna", "container.repair": "Sprŏw a zmianuj", "container.repair.cost": "Koszt zaklyńciŏ: %1$s", "container.repair.expensive": "Za droge!", "container.shulkerBox": "Shulkerowŏ kastla", "container.shulkerBox.itemCount": "%s × %s", "container.shulkerBox.more": "a %s wiyncyj…", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Wyndzŏk", "container.spectatorCantOpen": "<PERSON><PERSON> idzie ô<PERSON>wrzić: <PERSON>up jeszcze niy ôstoł wytworzōny.", "container.stonecutter": "Krajzyga", "container.upgrade": "Ulepsz rzecz", "container.upgrade.error_tooltip": "<PERSON><PERSON><PERSON> rzeczy we tyn spusōb", "container.upgrade.missing_template_tooltip": "Przidej kowŏlski szablōn", "controls.keybinds": "<PERSON><PERSON><PERSON><PERSON><PERSON> knefle…", "controls.keybinds.duplicateKeybinds": "Tyn knefel tyż mŏ dalij pokŏzanõ fōnkcyjõ:\n%s", "controls.keybinds.title": "Prz<PERSON><PERSON>e knefle", "controls.reset": "Zresetuj", "controls.resetAll": "Zresetuj wsz<PERSON>cke", "controls.title": "Sz<PERSON>urowan<PERSON>", "createWorld.customize.buffet.biome": "Ôbier biōm", "createWorld.customize.buffet.title": "Stosowanie jednego bi<PERSON>mu", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Dno — %s", "createWorld.customize.flat.layer.top": "Szczyt — %s", "createWorld.customize.flat.removeLayer": "Us<PERSON>ń szicht", "createWorld.customize.flat.tile": "<PERSON><PERSON><PERSON><PERSON> lagi", "createWorld.customize.flat.title": "Stosowanie płaskigo świŏta", "createWorld.customize.presets": "Szablōny", "createWorld.customize.presets.list": "Abo ôbier keryś ze dostympnych szablōnōw!", "createWorld.customize.presets.select": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.presets.share": "Chcesz sie podzielić ze drugimi ze swoich szablōnōw? Użyj pola pōniżyj!", "createWorld.customize.presets.title": "Ôbier szablōn", "createWorld.preparing": "Rychtowanie ku tworzyniu <PERSON>…", "createWorld.tab.game.title": "Gra", "createWorld.tab.more.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createWorld.tab.world.title": "Świŏt", "credits_and_attribution.button.attribution": "Atrybucyjŏ", "credits_and_attribution.button.credits": "Autorzi", "credits_and_attribution.button.licenses": "Licyncyje", "credits_and_attribution.screen.title": "Autorzi a atrybucyjŏ", "dataPack.bundle.description": "Załōnczŏ ekszperymyntalnõ rzecz — Miyszek", "dataPack.bundle.name": "<PERSON><PERSON><PERSON><PERSON>", "dataPack.locator_bar.description": "Pokazuj kerōnek ku inkszym grŏczōm we mocperzōnowym trybie", "dataPack.locator_bar.name": "Ôbiyżny pŏsek", "dataPack.minecart_improvements.description": "Usprawniōny ruch wagōn<PERSON>w", "dataPack.minecart_improvements.name": "Ulepszynia wagōnōw", "dataPack.redstone_experiments.description": "Ekszperymyntalne zmiany redstone", "dataPack.redstone_experiments.name": "Ekszperymynty redstone", "dataPack.title": "Ôbier pakety danych", "dataPack.trade_rebalance.description": "Zaktualiziyrowany gyszeft ze wiyśniŏkami", "dataPack.trade_rebalance.name": "Zrōwnowŏżynie gyszeftu ze wiyśniŏkami", "dataPack.update_1_20.description": "Nowŏ zawartość a fōnkcyje lŏ Minecrafta 1.20", "dataPack.update_1_20.name": "Aktualizacyjŏ 1.20", "dataPack.update_1_21.description": "Nowŏ zawartość a fōnkcyje lŏ Minecrafta 1.21", "dataPack.update_1_21.name": "Aktualizacyjŏ 1.21", "dataPack.validation.back": "Nazŏd", "dataPack.validation.failed": "Weryfikacyjŏ paketōw danych sie niy podarziła!", "dataPack.validation.reset": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sztandardowe", "dataPack.validation.working": "Weryficiyrowanie ôbranych paketōw danych…", "dataPack.vanilla.description": "Sztandardowe dane <PERSON>", "dataPack.vanilla.name": "Sztandardowy", "dataPack.winter_drop.description": "Nowŏ zawartość a fōnkcyje lŏ zimowyj aktualizacyje", "dataPack.winter_drop.name": "Zimowŏ aktualizacyjŏ", "datapackFailure.safeMode": "<PERSON><PERSON><PERSON> tryb", "datapackFailure.safeMode.failed.description": "<PERSON>n <PERSON> zawiyrŏ felerne abo popsowane dane zapisu.", "datapackFailure.safeMode.failed.title": "<PERSON>y podarziło sie zaladować świŏta we bedliwym trybie.", "datapackFailure.title": "Felery w ôbranych paketach danych niy przizwolyły na zaladowanie świŏta.\nMogesz abo sprōbować zaladować yno sztandardowy paket danych (w bedliwym trybie), abo w<PERSON><PERSON><PERSON> ku titlowym ykrōnie a naprawić felery ôd rynki.", "death.attack.anvil": "Grŏcz %1$s ôstoł zmiŏżdżōny ôd ambosa, kery na niego ślecioł", "death.attack.anvil.player": "Grŏcz %1$s ôstoł zmiŏżdżōny ôd ambosa, kery na niego ślecioł podczas gyfechtu ze %2$s", "death.attack.arrow": "Grŏcz %1$s ôstoł zastrzelōny ôd %2$s", "death.attack.arrow.item": "Grŏcz %1$s ôstoł zastrzelōny ôd %2$s po pōmocy %3$s", "death.attack.badRespawnPoint.link": "schwŏlnyj fōnkcyje we grze", "death.attack.badRespawnPoint.message": "Grŏcz %1$s ôstoł ubity ôd %2$s", "death.attack.cactus": "Grŏcz %1$s ôstoł zasztichowany na amynt", "death.attack.cactus.player": "Grŏcz %1$s wlŏz na kaktus, kej <PERSON><PERSON>ł umknyć %2$s", "death.attack.cramming": "Grŏcz %1$s ôstŏł za fest ścisnyty", "death.attack.cramming.player": "Grŏcz %1$s ôstoł zmiŏżdżōny ôd %2$s", "death.attack.dragonBreath": "Grŏcz %1$s upiyk sie na dychu dracha", "death.attack.dragonBreath.player": "Grŏcz %1$s upiyk sie na dychu dracha rozprzestrzyniōnego ôd %2$s", "death.attack.drown": "Grŏcz %1$s utynył", "death.attack.drown.player": "Grŏcz %1$s utynył, kej ch<PERSON>ł umknyć %2$s", "death.attack.dryout": "Grŏcz %1$s umrzōł ze ôdwodniyniŏ", "death.attack.dryout.player": "Grŏcz %1$s umrzōł ze ôdwōdniyniŏ, kej <PERSON>ł umknyć %2$s", "death.attack.even_more_magic": "Grŏcz %1$s ôstoł ubity ôd eszcze wiyncyj magije", "death.attack.explosion": "Grŏcz %1$s wyfiurgnył we luft", "death.attack.explosion.player": "Grŏcz %1$s ôstoł wysadzōny ôd %2$s", "death.attack.explosion.player.item": "Grŏcz %1$s ôstoł wysadzōny ôd %2$s po pōmocy %3$s", "death.attack.fall": "Grŏcz %1$s zaliczōł twarde lōndowanie", "death.attack.fall.player": "Grŏcz %1$s prasknōł festelnie na ziymiã, kej <PERSON>ł umkny<PERSON> %2$s", "death.attack.fallingBlock": "Grŏcz %1$s ôstoł zmiŏżdżōny ôd bloku, kery na niego ślecioł", "death.attack.fallingBlock.player": "Grŏcz %1$s ôstoł zmiŏżdżōny ôd bloku, kery na niego ślecioł podczas gyfechtu ze %2$s", "death.attack.fallingStalactite": "Grŏcz %1$s ôstoł zmiŏżdżōny ôd stalaktytu, kery na niego ślecioł", "death.attack.fallingStalactite.player": "Grŏcz %1$s ôstoł zmiŏżdżōny ôd stalaktytu, kery na niego ślecioł podczas gyfechtu ze %2$s", "death.attack.fireball": "Grŏcz %1$s ôstoł cylnyty ôd fojerbala ôd %2$s", "death.attack.fireball.item": "Grŏcz %1$s ôstoł cylnyty ôd fojerbala ôd %2$s po pōmocy %3$s", "death.attack.fireworks": "Grŏcz %1$s wyfiurgnył ze larmym", "death.attack.fireworks.item": "Grŏcz %1$s wyfiurgnył ze larmym skuli fojerwerku ôdpŏlōnego ze %3$s ôd %2$s", "death.attack.fireworks.player": "Grŏcz %1$s wyfiurgnył ze larmym podczas gyfechtu ze %2$s", "death.attack.flyIntoWall": "Grŏcz %1$s doświŏdczōł kinetycznyj ynergije", "death.attack.flyIntoWall.player": "Grŏcz %1$s skosztowoł energije kinetycznyj, kej <PERSON>ł umk<PERSON> %2$s", "death.attack.freeze": "Grŏcz %1$s zmarznył na amynt", "death.attack.freeze.player": "Grŏcz %1$s zmarznył na amyn ôd %2$s", "death.attack.generic": "Grŏcz %1$s umrzōł", "death.attack.generic.player": "Grŏcz %1$s umrzōł skuli %2$s", "death.attack.genericKill": "Grŏcz %1$s ôstoł ubity", "death.attack.genericKill.player": "Grŏcz %1$s ôstoł ubity podczas gyfechtu ze %2$s", "death.attack.hotFloor": "Grŏcz %1$s <PERSON><PERSON><PERSON><PERSON>, iże lezie po lawie", "death.attack.hotFloor.player": "Grŏcz %1$s wlŏz ku niybezpiycznyj zōnie skuli %2$s", "death.attack.inFire": "Grŏcz %1$s zgoroł", "death.attack.inFire.player": "Grŏcz %1$s wlŏz we ôgyń podczas gyfechtu ze %2$s", "death.attack.inWall": "Grŏcz %1$s udusiōł sie we ścianie", "death.attack.inWall.player": "Grŏcz %1$s udusiōł sie we ścianie podczas gyfechtu ze %2$s", "death.attack.indirectMagic": "Grŏcz %1$s ôstoł ubity ôd %2$s po pōmocy magije", "death.attack.indirectMagic.item": "Grŏcz %1$s ôstoł ubity ôd %2$s po pōmocy %3$s", "death.attack.lava": "Grŏcz %1$s sprōbowoł pływać we lawie", "death.attack.lava.player": "Grŏcz %1$s sprōbowoł pływać w lawie, kej <PERSON>ł um<PERSON> %2$s", "death.attack.lightningBolt": "Grŏcz %1$s ôstoł trefiōny ôd blicu", "death.attack.lightningBolt.player": "Grŏcz %1$s ôstoł trefiōny ôd blicu podczas gyfechtu ze %2$s", "death.attack.mace_smash": "Grŏcz %1$s ôstoł zmiŏżdżōny ôd %2$s", "death.attack.mace_smash.item": "Grŏcz %1$s ôstoł zmiŏżdżōny ôd %2$s po pōmocy %3$s", "death.attack.magic": "Grŏcz %1$s ôstoł ubity ôd magije", "death.attack.magic.player": "Grŏcz %1$s ôstoł ubity ôd magije, kej <PERSON><PERSON>ł umk<PERSON> %2$s", "death.attack.message_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bōła za <PERSON>, coby szło jã dolifrować we cołkości. Ôto jeji <PERSON> wersyjŏ: „%s”", "death.attack.mob": "Grŏcz %1$s ôstoł zaszlachtowany ôd %2$s", "death.attack.mob.item": "Grŏcz %1$s ôstoł zaszlachtowany ôd %2$s po pōmocy %3$s", "death.attack.onFire": "Grŏcz %1$s zgoroł", "death.attack.onFire.item": "Grŏcz %1$s zgoroł podczas gyfechtu ze %2$s, kery trzimoł %3$s", "death.attack.onFire.player": "Grŏcz %1$s zgoroł podczas gyfechtu ze %2$s", "death.attack.outOfWorld": "Grŏcz %1$s wypŏd ze świŏta", "death.attack.outOfWorld.player": "Grŏcz %1$s drek niy chcioł żyć na tym samym świecie co %2$s", "death.attack.outsideBorder": "Grŏcz %1$s wykludziōł sie ze tego świŏta", "death.attack.outsideBorder.player": "Grŏcz %1$s wykludziōł sie ze tego świŏta podczas gyfechtu ze %2$s", "death.attack.player": "Grŏcz %1$s ôstoł zaszlachtowany ôd %2$s", "death.attack.player.item": "Grŏcz %1$s ôstoł zaszlachtowany ôd %2$s po pōmocy %3$s", "death.attack.sonic_boom": "Grŏcz %1$s ôstoł śmieciōny ôd szrekliwyj klangowyj wele", "death.attack.sonic_boom.item": "Grŏcz %1$s ôstoł śmieciōny ôd szrekliwyj klangowyj wele, kej ch<PERSON>ł um<PERSON> %2$s, kery trzimoł %3$s", "death.attack.sonic_boom.player": "Grŏcz %1$s ôstoł śmieciōny ôd szrekliwyj klangowyj wele, kej ch<PERSON>ł umk<PERSON> %2$s", "death.attack.stalagmite": "Grŏcz %1$s sie nadzioł na stalagmit", "death.attack.stalagmite.player": "Grŏcz %1$s sie nadzioł na stalagmit podczas gyfechtu ze %2$s", "death.attack.starve": "Grŏcz %1$s umrzōł ze głodu", "death.attack.starve.player": "Grŏcz %1$s umrzōł ze głodu podczas gyfechtu ze %2$s", "death.attack.sting": "Grŏcz %1$s ôstoł na amynt zaszczypiōny", "death.attack.sting.item": "Grŏcz %1$s ôstoł na amynt zaszczypiōny ôd %2$s po pōmocy %3$s", "death.attack.sting.player": "Grŏcz %1$s ôstoł na amynt zaszczypiōny ôd %2$s", "death.attack.sweetBerryBush": "Grŏcz %1$s ôstoł na amynt zasztichowany ôd borōwczywiŏ", "death.attack.sweetBerryBush.player": "Grŏcz %1$s ôstoł na amynt zasztichowany ôd borōwczywiŏ, kej <PERSON>ł um<PERSON> %2$s", "death.attack.thorns": "Grŏcz %1$s ôstoł ubity, kej chcioł dopaś %2$s", "death.attack.thorns.item": "Grŏcz %1$s ôstoł ubity ôd %3$s, kej <PERSON>ł do<PERSON> %2$s", "death.attack.thrown": "Grŏcz %1$s ôstoł sprŏty ôd %2$s", "death.attack.thrown.item": "Grŏcz %1$s ôstoł sprŏty ôd %2$s po pōmocy %3$s", "death.attack.trident": "Grŏcz %1$s ôstoł przebity ôd %2$s", "death.attack.trident.item": "Grŏcz %1$s ôstoł przebity ôd %2$s po pōmocy %3$s", "death.attack.wither": "Grŏcz %1$s ôbumrził", "death.attack.wither.player": "Grŏcz %1$s umrzōł podczas gyfechtu ze %2$s", "death.attack.witherSkull": "Grŏcz %1$s ôstoł cylnyty ôd szale ôd %2$s", "death.attack.witherSkull.item": "Grŏcz %1$s ôstoł cylnyty ôd szale ôd %2$s po pōmocy %3$s", "death.fell.accident.generic": "Grŏcz %1$s spŏd ze fest wysokigo placu", "death.fell.accident.ladder": "Grŏcz %1$s spŏd ze drabiny", "death.fell.accident.other_climbable": "Grŏcz %1$s spŏd podczas kletrowaniŏ", "death.fell.accident.scaffolding": "Grŏcz %1$s ślecioł ze rysztōnku", "death.fell.accident.twisting_vines": "Grŏcz %1$s ślecioł ze szagich kleterflancōw", "death.fell.accident.vines": "Grŏcz %1$s spŏd ze kleterflancōw", "death.fell.accident.weeping_vines": "Grŏcz %1$s spŏd ze szarłatnych kleterflancōw", "death.fell.assist": "Grŏcz %1$s ôstoł skŏzany na fal ôd %2$s", "death.fell.assist.item": "Grŏcz %1$s ôstoł skŏzany na fal ôd %2$s po pōmocy %3$s", "death.fell.finish": "Grŏcz %1$s spŏd a ôstoł dobity ôd %2$s", "death.fell.finish.item": "Grŏcz %1$s spŏd a ôstoł dobity ôd grŏcza %2$s po pōmocy %3$s", "death.fell.killer": "Grŏcz %1$s ôstoł skŏzany na fal", "deathScreen.quit.confirm": "Na isto ch<PERSON>z wylyź?", "deathScreen.respawn": "Ôdrōdź sie", "deathScreen.score": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathScreen.score.value": "Wynik: %s", "deathScreen.spectate": "Ôglōndej świŏt", "deathScreen.title": "Niy żyjesz!", "deathScreen.title.hardcore": "Kōniec gry!", "deathScreen.titleScreen": "Gōwny ykrōn", "debug.advanced_tooltips.help": "F3 + H = <PERSON>rz<PERSON><PERSON><PERSON><PERSON><PERSON> zaawansowane etykety", "debug.advanced_tooltips.off": "Zaawansowane etykety: skryte", "debug.advanced_tooltips.on": "Zaawansowane etykety: widoczne", "debug.chunk_boundaries.help": "F3 + G = Pok<PERSON><PERSON> granice czankōw", "debug.chunk_boundaries.off": "Granice czankōw: skryte", "debug.chunk_boundaries.on": "Granice czankōw: widoczne", "debug.clear_chat.help": "F3 + D = Wycz<PERSON>ś czat", "debug.copy_location.help": "F3 + C = Skopiyruj położynie pod kōmyndã /tp. Przitrzimej F3 + C, coby s<PERSON> grã", "debug.copy_location.message": "Skopiyrowano położynie ku schowku", "debug.crash.message": "Kōmbinacyjŏ F3 + <PERSON> je wcisnytŏ. <PERSON>ra ô<PERSON>ie <PERSON>, e<PERSON><PERSON> durch by<PERSON><PERSON><PERSON> trzimoł.", "debug.crash.warning": "Kraszowanie za %s…", "debug.creative_spectator.error": "Niy idzie przeszaltrować trybu gry — brakuje uprawniyń", "debug.creative_spectator.help": "F3 + N = Przeszaltruj poprzydni tryb ze trybym cuszauera", "debug.dump_dynamic_textures": "Zachowano dynamiczne tekstury we %s", "debug.dump_dynamic_textures.help": "F3 + S = Ściep dynamiczne tekstury", "debug.gamemodes.error": "<PERSON><PERSON> <PERSON><PERSON> interfejsu zmiany trybu gry skuli braku uprawniyń", "debug.gamemodes.help": "F3 + F4 = Ôdewrzij interfejs zmiany trybu gry", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s — nŏstympny", "debug.help.help": "F3 + Q = Pokŏż tã spiskã", "debug.help.message": "Przipisane knefle:", "debug.inspect.client.block": "Skopiyrowano dane blokōw ze strōny klijynta ku schowku", "debug.inspect.client.entity": "Skopiyrowano dane bytōw ze strōny klijynta ku schowku", "debug.inspect.help": "F3 + I = Skopiyruj ku schowku dane bytōw abo blokōw", "debug.inspect.server.block": "Skopiyrowano serwerowe dane blokōw ku schowku", "debug.inspect.server.entity": "Skopiyrowano serwerowe dane bytōw ku schowku", "debug.pause.help": "F3 + Esc = Zapauzuj bez pokazowaniŏ meni pauzy (e<PERSON><PERSON> je moż<PERSON>ść zapauzowaniŏ)", "debug.pause_focus.help": "F3 + P = Przeszaltruj pauzã po ôpuszczyniu ôkna gry", "debug.pause_focus.off": "Pauza po ôpuszczyniu ôkna gry: wył<PERSON>nc<PERSON>ōnŏ", "debug.pause_focus.on": "Pauza po ôpuszczyniu ôkna gry: zał<PERSON>nc<PERSON>ōnŏ", "debug.prefix": "[Debugowanie]:", "debug.profiling.help": "F3 + L = Sztartnij/sztopnij profilowanie", "debug.profiling.start": "Sztartnyto profilowanie na %s zykōnd. Użyj kōmbinacyje F3 + L, coby sz<PERSON> z<PERSON>u", "debug.profiling.stop": "Ukōńczōno profilowanie. Raport zachowano we %s", "debug.reload_chunks.help": "F3 + A = Prz<PERSON><PERSON>j czanki", "debug.reload_chunks.message": "Przeladowywanie wszyckich czankōw…", "debug.reload_resourcepacks.help": "F3 + T = Przeladuj pakety zasobōw", "debug.reload_resourcepacks.message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pakety zasobōw", "debug.show_hitboxes.help": "F3 + B = Pokŏż hitboxy", "debug.show_hitboxes.off": "Kolizyjne kisty: skryte", "debug.show_hitboxes.on": "Kolizyjne kisty: widoczne", "debug.version.header": "Informacyje ô wersyji klijynta:", "debug.version.help": "F3 + V = Pokŏż informacyje ô wersyji klijynta", "demo.day.1": "Ta prōba potwŏ piy<PERSON>ć dōb czasu gry. Dej pozōr na czas!", "demo.day.2": "Drugi dziyń", "demo.day.3": "Trzeci dziyń", "demo.day.4": "Czwŏrty dziyń", "demo.day.5": "To je twōj pośledni dziyń!", "demo.day.6": "<PERSON><PERSON><PERSON><PERSON> dōb już je forbaj. Użyj %s, coby zach<PERSON> schyt ykranu swojij gryfnyj roboty.", "demo.day.warning": "Twōj czas sie kōńczy!", "demo.demoExpired": "<PERSON>zas prōby minył!", "demo.help.buy": "Kup terŏz!", "demo.help.fullWrapped": "Prōba potwŏ 5 dōb czasu gry (ōngyfer gō<PERSON><PERSON> a 40 minut zachliwego czasu). We postympach znŏjdziesz skŏzōwki! Miyj sie!", "demo.help.inventory": "Naciś knefel %1$s, coby <PERSON><PERSON><PERSON><PERSON><PERSON> inwyntŏrz", "demo.help.jump": "Naciś %1$s, coby s<PERSON>", "demo.help.later": "<PERSON><PERSON>j dalij!", "demo.help.movement": "Używej myszy a kneflōw %1$s, %2$s, %3$s, %4$s ku poruszaniu", "demo.help.movementMouse": "Rozglōndej sie po pōmocy myszki", "demo.help.movementShort": "<PERSON><PERSON><PERSON><PERSON> sie po pōmocy kneflōw %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Prōba Minecrafta", "demo.remainingTime": "Ôstoły czas: %s", "demo.reminder": "Prō<PERSON> je forbaj. <PERSON><PERSON>r<PERSON>, coby prz<PERSON>, abo wyprōbuj na zaś grã na nowym świecie!", "difficulty.lock.question": "Chcesz zaszperować ciynżkość tego świŏta? Świŏt bydzie na sztyjc nasztalowany na %1$s, a już nigdy niy byd<PERSON>sz śmioł ônego zmiynić.", "difficulty.lock.title": "Zaszperuj ciynżkość świŏta", "disconnect.endOfStream": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "disconnect.exceeded_packet_rate": "Wyciepano za przekrŏczynie limitu paketōw", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignorowanie zapytaniŏ ô status…", "disconnect.loginFailedInfo": "Logowanie sie niy podarziło: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Mocperzōnowŏ gra je wyłōnczōnŏ. Proszymy badnyć nasztalowania swojigo kōnta Microsoft.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>j<PERSON> (sprōbuj sztartnyć na zaś grã a lōnczer)", "disconnect.loginFailedInfo.serversUnavailable": "Serwery uwiyrzitylnianiŏ sōm terŏzki niydostympne. Proszymy sprōbować na zaś troszkã niyskorzij.", "disconnect.loginFailedInfo.userBanned": "<PERSON><PERSON><PERSON> bana ôd grani<PERSON> on<PERSON>jn", "disconnect.lost": "Stracōno połōnczynie", "disconnect.packetError": "<PERSON><PERSON>cowego <PERSON>", "disconnect.spam": "Wyciepano za spamowanie", "disconnect.timeout": "<PERSON>it c<PERSON>u <PERSON>", "disconnect.transfer": "Przeniysiōno na inkszy serwer", "disconnect.unknownHost": "Niyznany host", "download.pack.failed": "Niy podarziło sie pobrać %s ze %s paketōw", "download.pack.progress.bytes": "Postymp: %s (cołkowitŏ wielgość niyznanŏ)", "download.pack.progress.percent": "Postymp: %s%%", "download.pack.title": "Pobiyranie paketu zas<PERSON>w (%s/%s)", "editGamerule.default": "Sztandardowo: %s", "editGamerule.title": "Edytuj prŏwidła gry", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Pochłanianie", "effect.minecraft.bad_omen": "Lagramynckŏ przepowiydź", "effect.minecraft.blindness": "Ślepota", "effect.minecraft.conduit_power": "<PERSON><PERSON>", "effect.minecraft.darkness": "Cima", "effect.minecraft.dolphins_grace": "Delfiniŏ przisługa", "effect.minecraft.fire_resistance": "Ôbstŏcie ôgnia", "effect.minecraft.glowing": "Blask", "effect.minecraft.haste": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.health_boost": "Zwiynkszōne zdrowie", "effect.minecraft.hero_of_the_village": "Bohatyr wiŏski", "effect.minecraft.hunger": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.infested": "Chrobajstwo", "effect.minecraft.instant_damage": "Ôrŏzkowe zraniynia", "effect.minecraft.instant_health": "Ôrŏzkowe lyczynie", "effect.minecraft.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.jump_boost": "Wysoki skok", "effect.minecraft.levitation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.luck": "Szczyńście", "effect.minecraft.mining_fatigue": "Przerobiynie", "effect.minecraft.nausea": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.night_vision": "Widoczność we cimie", "effect.minecraft.oozing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.poison": "Zagidzynie", "effect.minecraft.raid_omen": "Przepowiydź anfalu", "effect.minecraft.regeneration": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.resistance": "Ôbstŏcie", "effect.minecraft.saturation": "Nasycynie", "effect.minecraft.slow_falling": "Powolne spadowanie", "effect.minecraft.slowness": "Uniyskorzynie", "effect.minecraft.speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.strength": "Siyła", "effect.minecraft.trial_omen": "Przepowiydź prōby", "effect.minecraft.unluck": "Pech", "effect.minecraft.water_breathing": "<PERSON><PERSON><PERSON><PERSON> pod wodōm", "effect.minecraft.weakness": "Lichota", "effect.minecraft.weaving": "<PERSON>", "effect.minecraft.wind_charged": "Naluftowanie", "effect.minecraft.wither": "Ôbumrzicie", "effect.none": "Bez efektōw", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pod wodōm", "enchantment.minecraft.bane_of_arthropods": "<PERSON><PERSON>", "enchantment.minecraft.binding_curse": "Fluch prziwiōnzaniŏ", "enchantment.minecraft.blast_protection": "Ôchrōna przed wybuchym", "enchantment.minecraft.breach": "Przedrzynie", "enchantment.minecraft.channeling": "Sztrōm", "enchantment.minecraft.density": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "Głymbinowy wandrus", "enchantment.minecraft.efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.feather_falling": "Łagodne spadowanie", "enchantment.minecraft.fire_aspect": "Zaklynty ôgyń", "enchantment.minecraft.fire_protection": "Ôchrōna przed <PERSON>", "enchantment.minecraft.flame": "Flama", "enchantment.minecraft.fortune": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.frost_walker": "Lodowe s<PERSON>zewi<PERSON>", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.knockback": "Riksztos", "enchantment.minecraft.looting": "<PERSON><PERSON>", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "Morske szczyńście", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "Sprŏwōnek", "enchantment.minecraft.multishot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.piercing": "Przeżganie", "enchantment.minecraft.power": "Siyła", "enchantment.minecraft.projectile_protection": "Ôchrōna przed szusami", "enchantment.minecraft.protection": "Ôchrōna", "enchantment.minecraft.punch": "Szlag", "enchantment.minecraft.quick_charge": "<PERSON><PERSON><PERSON> lad<PERSON>nie", "enchantment.minecraft.respiration": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.riptide": "Przipływ", "enchantment.minecraft.sharpness": "Ôstrość", "enchantment.minecraft.silk_touch": "Jedw<PERSON><PERSON><PERSON> dotyk", "enchantment.minecraft.smite": "<PERSON><PERSON>", "enchantment.minecraft.soul_speed": "Duszyczkowŏ gibkość", "enchantment.minecraft.sweeping": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.sweeping_edge": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.swift_sneak": "Gibke skrŏdanie", "enchantment.minecraft.thorns": "<PERSON>z<PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "<PERSON><PERSON><PERSON> podzianiŏ", "enchantment.minecraft.wind_burst": "<PERSON><PERSON><PERSON>", "entity.minecraft.acacia_boat": "Agacowŏ łōdka", "entity.minecraft.acacia_chest_boat": "Agacowŏ łōdka ze kastlōm", "entity.minecraft.allay": "<PERSON>rz<PERSON><PERSON><PERSON>", "entity.minecraft.area_effect_cloud": "Ôbłok wywaru", "entity.minecraft.armadillo": "Ôbryncznik", "entity.minecraft.armor_stand": "Sztynder na pancer", "entity.minecraft.arrow": "Strzała", "entity.minecraft.axolotl": "Aks<PERSON>tl", "entity.minecraft.bamboo_chest_raft": "Bambusowŏ mataczka ze kastlōm", "entity.minecraft.bamboo_raft": "Bambusowŏ mataczka", "entity.minecraft.bat": "<PERSON>j<PERSON><PERSON><PERSON>", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "Brzizkowŏ łōdka", "entity.minecraft.birch_chest_boat": "Brzizkowŏ łōdka ze kastlōm", "entity.minecraft.blaze": "Flamŏk", "entity.minecraft.block_display": "Wyświytlŏcz bloku", "entity.minecraft.boat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bogged": "Bagyniŏk", "entity.minecraft.breeze": "Wiytrzik", "entity.minecraft.breeze_wind_charge": "Wietrzny ladōnek", "entity.minecraft.camel": "<PERSON><PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "Jaskiniowy pajōnk", "entity.minecraft.cherry_boat": "Trześniowŏ łōdka", "entity.minecraft.cherry_chest_boat": "Trześniowŏ łōdka ze kastlōm", "entity.minecraft.chest_boat": "Łōdka ze kastlōm", "entity.minecraft.chest_minecart": "Wagōn ze kastlōm", "entity.minecraft.chicken": "<PERSON><PERSON>", "entity.minecraft.cod": "<PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Wagōn ze blokym kōmynd", "entity.minecraft.cow": "Krŏwa", "entity.minecraft.creaking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creaking_transient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Ćmawodymbowŏ łōdka", "entity.minecraft.dark_oak_chest_boat": "Ćmawodymbowŏ łōdka ze kastlōm", "entity.minecraft.dolphin": "Delfin", "entity.minecraft.donkey": "<PERSON><PERSON><PERSON>", "entity.minecraft.dragon_fireball": "<PERSON><PERSON><PERSON><PERSON> dracha", "entity.minecraft.drowned": "Utopek", "entity.minecraft.egg": "Ciepnyte jajco", "entity.minecraft.elder_guardian": "Chańdŏwny wachtyrz", "entity.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "Ciepnytŏ perla Endu", "entity.minecraft.enderman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.endermite": "Ender<PERSON>", "entity.minecraft.evoker": "Gu<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.evoker_fangs": "Gryzŏk guślŏrza", "entity.minecraft.experience_bottle": "Ciepanie flaszki doświŏdczyniŏ", "entity.minecraft.experience_orb": "<PERSON><PERSON>", "entity.minecraft.eye_of_ender": "<PERSON><PERSON><PERSON>", "entity.minecraft.falling_block": "Blok, co ślatuje", "entity.minecraft.falling_block_type": "%s, co ślatuje", "entity.minecraft.fireball": "Fojerbal", "entity.minecraft.firework_rocket": "Fojerwerk", "entity.minecraft.fishing_bobber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.fox": "Liszka", "entity.minecraft.frog": "Rapitołza", "entity.minecraft.furnace_minecart": "Wagōn ze żeleźniŏkym", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Rizŏk", "entity.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.goat": "Ciga", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "Ucieszny Ghast", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Wagōn ze lyjŏkym", "entity.minecraft.horse": "Kōń", "entity.minecraft.husk": "Suchlŏk", "entity.minecraft.illusioner": "Kugl<PERSON>rz", "entity.minecraft.interaction": "Interakcyjŏ", "entity.minecraft.iron_golem": "Żelazny golym", "entity.minecraft.item": "Rzecz", "entity.minecraft.item_display": "Wyświytlŏcz rzeczy", "entity.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "entity.minecraft.jungle_boat": "Dżōng<PERSON><PERSON> łōdka", "entity.minecraft.jungle_chest_boat": "Dżōnglowŏ łōdka ze kastlōm", "entity.minecraft.killer_bunny": "Mycŏk-ubijŏk", "entity.minecraft.leash_knot": "Knołtyl ôd la<PERSON>y", "entity.minecraft.lightning_bolt": "Blic", "entity.minecraft.lingering_potion": "Ôbłoczny wywar", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "Chark lamy", "entity.minecraft.magma_cube": "Magmŏk", "entity.minecraft.mangrove_boat": "Namorzinowŏ łōdka", "entity.minecraft.mangrove_chest_boat": "Namorzinowŏ łōdka ze kastlōm", "entity.minecraft.marker": "Znacznik", "entity.minecraft.minecart": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.oak_boat": "Dymbowŏ łōdka", "entity.minecraft.oak_chest_boat": "Dymbowŏ łōdka ze kastlōm", "entity.minecraft.ocelot": "Ôcylot", "entity.minecraft.ominous_item_spawner": "Lagramyncki swoływŏcz", "entity.minecraft.painting": "Ôbrŏz", "entity.minecraft.pale_oak_boat": "Bladodymbowŏ łōdka", "entity.minecraft.pale_oak_chest_boat": "Bladodymbowŏ łōdka ze kastlōm", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "<PERSON><PERSON>", "entity.minecraft.pig": "Świnia", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "Piglin-doskwiyrnik", "entity.minecraft.pillager": "Raubiyrz", "entity.minecraft.player": "G<PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "Polarny niydźwiydź", "entity.minecraft.potion": "<PERSON><PERSON><PERSON>", "entity.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.rabbit": "Mycŏk", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "Laks", "entity.minecraft.sheep": "Ôwca", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "Rybik", "entity.minecraft.skeleton": "Szkelet", "entity.minecraft.skeleton_horse": "Kōń-szkelet", "entity.minecraft.slime": "Szlōm", "entity.minecraft.small_fireball": "<PERSON><PERSON><PERSON> fojerbal", "entity.minecraft.sniffer": "Sznupŏcz", "entity.minecraft.snow_golem": "Śniyżny golym", "entity.minecraft.snowball": "Śniyżka", "entity.minecraft.spawner_minecart": "Wagōn ze swoływŏczym bebŏkōw", "entity.minecraft.spectral_arrow": "Upiorn<PERSON> strzała", "entity.minecraft.spider": "<PERSON><PERSON><PERSON>", "entity.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON> wywar", "entity.minecraft.spruce_boat": "Świyrkowŏ łōdka", "entity.minecraft.spruce_chest_boat": "Świyrkowŏ łōdka ze kastlōm", "entity.minecraft.squid": "Tintynfisza", "entity.minecraft.stray": "Pōniywiyrŏk", "entity.minecraft.strider": "Smyk<PERSON>cz", "entity.minecraft.tadpole": "Go<PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Wyświytlŏcz tekstu", "entity.minecraft.tnt": "Ôdpalōne TNT", "entity.minecraft.tnt_minecart": "Wagōn ze TNT", "entity.minecraft.trader_llama": "Handlyrzowŏ lama", "entity.minecraft.trident": "Trzizōmb", "entity.minecraft.tropical_fish": "Tropikalnŏ ryba", "entity.minecraft.tropical_fish.predefined.0": "Bŏzynek", "entity.minecraft.tropical_fish.predefined.1": "Czŏrny bodlŏk", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.11": "Ôzdobnŏ ryba-szmaterlŏk", "entity.minecraft.tropical_fish.predefined.12": "Ryba-papagaj", "entity.minecraft.tropical_fish.predefined.13": "Rzynsatŏ krōlōwka", "entity.minecraft.tropical_fish.predefined.14": "Czyrwōnŏ piastnica", "entity.minecraft.tropical_fish.predefined.15": "Czyrwōnogymbŏ kiōłzka", "entity.minecraft.tropical_fish.predefined.16": "Czyrwōny lucyjōnek", "entity.minecraft.tropical_fish.predefined.17": "Lanŏk", "entity.minecraft.tropical_fish.predefined.18": "Tōmatowy bŏzynek", "entity.minecraft.tropical_fish.predefined.19": "Kostyrka", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.20": "Żōłtopłetwŏ papagajoryba", "entity.minecraft.tropical_fish.predefined.21": "Żōłty bodlŏk", "entity.minecraft.tropical_fish.predefined.3": "Ryba-szmaterlŏk", "entity.minecraft.tropical_fish.predefined.4": "Piastnica", "entity.minecraft.tropical_fish.predefined.5": "Bŏzynek", "entity.minecraft.tropical_fish.predefined.6": "Herski wojŏk", "entity.minecraft.tropical_fish.predefined.7": "Diŏdymek", "entity.minecraft.tropical_fish.predefined.8": "Cysŏrski kłapŏcz", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "Wojŏk", "entity.minecraft.tropical_fish.type.blockfish": "Szperka", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON>link<PERSON>", "entity.minecraft.tropical_fish.type.dasher": "L<PERSON>t<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "Tŏplŏcz", "entity.minecraft.tropical_fish.type.glitter": "Blyndka", "entity.minecraft.tropical_fish.type.kob": "Kulbŏk", "entity.minecraft.tropical_fish.type.snooper": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.spotty": "Flekownica", "entity.minecraft.tropical_fish.type.stripey": "Dynga", "entity.minecraft.tropical_fish.type.sunstreak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.turtle": "Żōłw", "entity.minecraft.vex": "Szerga", "entity.minecraft.villager": "Wiyśniŏk", "entity.minecraft.villager.armorer": "Pła<PERSON><PERSON>rz", "entity.minecraft.villager.butcher": "Mas<PERSON><PERSON>", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON>", "entity.minecraft.villager.farmer": "<PERSON>", "entity.minecraft.villager.fisherman": "Rybiŏrz", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "Rzymiyniŏrz", "entity.minecraft.villager.librarian": "Biblijotykŏrz", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "Wiyśniŏk", "entity.minecraft.villager.shepherd": "Ôwcŏrz", "entity.minecraft.villager.toolsmith": "Ko<PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "Wafynszmid", "entity.minecraft.vindicator": "Zymstliwiec", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON><PERSON> handlyrz", "entity.minecraft.warden": "Wachmajster", "entity.minecraft.wind_charge": "Wietrzny ladōnek", "entity.minecraft.witch": "<PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Witherowy szkelet", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON> withera", "entity.minecraft.wolf": "Wilk", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Umarlŏk", "entity.minecraft.zombie_horse": "Kōń-umarlŏk", "entity.minecraft.zombie_villager": "Wiyśniŏk-umarlŏk", "entity.minecraft.zombified_piglin": "Piglin-umarlŏk", "entity.not_summonable": "Niy idzie swołać bytu zorty „%s”", "event.minecraft.raid": "An<PERSON><PERSON>", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Anfal — Klynska", "event.minecraft.raid.raiders_remaining": "Ôstoli raubiyrze: %s", "event.minecraft.raid.victory": "Zwyciynstwo", "event.minecraft.raid.victory.full": "Anfal — Zwyciynstwo", "filled_map.buried_treasure": "Karta ku skry<PERSON><PERSON> szacu", "filled_map.explorer_jungle": "<PERSON>rta forszera dżō<PERSON>", "filled_map.explorer_swamp": "Karta forszera barzołōw", "filled_map.id": "Nr. %s", "filled_map.level": "(Poziōm %s/%s)", "filled_map.locked": "Zaszperowanŏ", "filled_map.mansion": "Karta forsz<PERSON>", "filled_map.monument": "<PERSON>rta forszera ôceanōw", "filled_map.scale": "Skala – 1:%s", "filled_map.trial_chambers": "Karta forszera kōmnat prōb", "filled_map.unknown": "Niyznanŏ karta", "filled_map.village_desert": "Karta ku wiŏsce na pustyni", "filled_map.village_plains": "Karta ku wiŏsce na rōwniach", "filled_map.village_savanna": "Karta ku wiŏsce na sawannie", "filled_map.village_snowy": "Karta ku śniyżnyj wiŏsce", "filled_map.village_taiga": "Karta ku wiŏsce we tajdze", "flat_world_preset.minecraft.bottomless_pit": "Bezdynnŏ głymbia", "flat_world_preset.minecraft.classic_flat": "Klasyczny płaski", "flat_world_preset.minecraft.desert": "<PERSON>ustyniŏ", "flat_world_preset.minecraft.overworld": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.redstone_ready": "Idealny ku redstone", "flat_world_preset.minecraft.snowy_kingdom": "Śniyżne krōlewstwo", "flat_world_preset.minecraft.the_void": "Prōżnia", "flat_world_preset.minecraft.tunnelers_dream": "Tōnelŏrzowe marzynie", "flat_world_preset.minecraft.water_world": "W<PERSON><PERSON> świ<PERSON>t", "flat_world_preset.unknown": "???", "gameMode.adventure": "Przigoda", "gameMode.changed": "Tryb gry ôstoł zmiyniōny na %s", "gameMode.creative": "Kreatywny", "gameMode.hardcore": "Ôbmierzły", "gameMode.spectator": "<PERSON><PERSON>", "gameMode.survival": "Przeżycie", "gamerule.allowFireTicksAwayFromPlayer": "<PERSON><PERSON> ôdlygłego ôgnia ôd <PERSON>", "gamerule.allowFireTicksAwayFromPlayer.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eli tiki lŏ ôgnia a lawy śmiōm by<PERSON> na ôdlygłości wielgszyj niż 8 czankōw ôd lecy jakigo <PERSON>a", "gamerule.announceAdvancements": "Ôgłŏszanie postympōw", "gamerule.blockExplosionDropDecay": "Znikanie tajle blokōw wysadzōnych ôd inkszych blokōw", "gamerule.blockExplosionDropDecay.description": "Traci sie tajla rzeczy wypadnytych ze blokōw wysadzōnych ôd inkszych blokōw.", "gamerule.category.chat": "<PERSON><PERSON><PERSON>", "gamerule.category.drops": "Ł<PERSON><PERSON>", "gamerule.category.misc": "Roztōmańte", "gamerule.category.mobs": "Stworzynia", "gamerule.category.player": "G<PERSON><PERSON><PERSON>", "gamerule.category.spawning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.category.updates": "Aktualizacyj<PERSON>wiŏ<PERSON>", "gamerule.commandBlockOutput": "Wyświytlanie danych wydŏwanych ôd kōmyndowych blokōw", "gamerule.commandModificationBlockLimit": "<PERSON>it <PERSON><PERSON> blok<PERSON>w ôd k<PERSON>", "gamerule.commandModificationBlockLimit.description": "<PERSON>it b<PERSON>, kere ś<PERSON><PERSON>m by<PERSON> <PERSON><PERSON> ôd jednyj k<PERSON>, bez przikłŏd `fill` abo `clone`.", "gamerule.disableElytraMovementCheck": "Bez sprŏwdzaniŏ ruchu po ôkrywach", "gamerule.disablePlayerMovementCheck": "Bez sprŏwdzaniŏ ruchu grŏczy", "gamerule.disableRaids": "<PERSON><PERSON>", "gamerule.doDaylightCycle": "Dobowy cykel", "gamerule.doEntityDrops": "Upuszczanie inhaltu inwyntŏrzōw ôd by<PERSON>w", "gamerule.doEntityDrops.description": "Kōnt<PERSON>uje wypŏdanie rzeczy ze wagōnōw (w tym inwyntŏrzōw), r<PERSON><PERSON><PERSON><PERSON>, ł<PERSON><PERSON> atd.", "gamerule.doFireTick": "S<PERSON>rz<PERSON><PERSON>", "gamerule.doImmediateRespawn": "Ôrŏzkowe ô<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.doInsomnia": "<PERSON><PERSON><PERSON><PERSON><PERSON> mor", "gamerule.doLimitedCrafting": "Wymŏgej receptu ku stworzyniu rzeczy", "gamerule.doLimitedCrafting.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eli <PERSON><PERSON>e byd<PERSON><PERSON> wytwŏrzać rzeczy yno po tych receptach, kere sōm naucz<PERSON>.", "gamerule.doMobLoot": "Upuszczanie łupōw ôd stworzyń", "gamerule.doMobLoot.description": "Kōntroluje wypŏdanie rzeczy ze stworzyń, wr<PERSON>z ze sferami doświŏdczyniŏ", "gamerule.doMobSpawning": "Pojawianie stworzyń", "gamerule.doMobSpawning.description": "<PERSON><PERSON><PERSON> byty mogōm sie kero<PERSON> po ôsobnych prŏwidłach.", "gamerule.doPatrolSpawning": "Raubiyrzowe patrole", "gamerule.doTileDrops": "<PERSON>y<PERSON><PERSON><PERSON><PERSON> b<PERSON>w", "gamerule.doTileDrops.description": "Kōntroluje wypŏdanie rzeczy ze blokōw, wr<PERSON>z ze sferami doświŏdczyniŏ", "gamerule.doTraderSpawning": "Pojawianie wandrownych handlyrzōw", "gamerule.doVinesSpread": "Szyrzynie kleterflancōw", "gamerule.doVinesSpread.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eli kleterflance sie szyrzōm na sōmsiednie bloki. <PERSON><PERSON> wpływŏ to na inksze zorty kleterflancōw jak choby kleterflance jaskiniowe, s<PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON> atd.", "gamerule.doWardenSpawning": "<PERSON><PERSON><PERSON><PERSON><PERSON> wa<PERSON>", "gamerule.doWeatherCycle": "Pogodowy cykel", "gamerule.drowningDamage": "<PERSON><PERSON><PERSON><PERSON> ôd <PERSON>", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON> po <PERSON>", "gamerule.enderPearlsVanishOnDeath.description": "Ciepnyte ôd grŏcza perle <PERSON>u przepadujōm, kedy tyn grŏcz umiyrŏ.", "gamerule.entitiesWithPassengersCanUsePortals": "Byty ze pasażerami śmiōm używać portali", "gamerule.entitiesWithPassengersCanUsePortals.description": "Przizwŏlŏ bytōm ze pasażerami durchgang bez portŏle Netheru, portŏle Endu a fōrty Endu.", "gamerule.fallDamage": "<PERSON>rani<PERSON><PERSON> ôd up<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.fireDamage": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.forgiveDeadPlayers": "Przebŏczanie grŏczōm po śmiyrci", "gamerule.forgiveDeadPlayers.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eli zgorszōne ôbojyntnie nastŏwiōne stworzynia sie łagodzōm po śmiyrci gorszyciyla.", "gamerule.freezeDamage": "<PERSON><PERSON><PERSON><PERSON> ôd m<PERSON>zu", "gamerule.globalSoundEvents": "Świŏtowe klangowe zdarzynia", "gamerule.globalSoundEvents.description": "Czasu niykerych wydarzyń we grze, bez przikłŏd przi swoływaniu bosa, klang wszyndy idzie usłyszeć.", "gamerule.keepInventory": "Zachowywanie inwyntŏrza po śmiyrci", "gamerule.lavaSourceConversion": "Przeksztołcanie sie lawy we zdrzōdło", "gamerule.lavaSourceConversion.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, co płynie sama sie z<PERSON>yniŏ we zdrzōdło, kej je ôbtoczōn<PERSON> ôd zdrzōdeł lawy ze ôbōch zajtōw.", "gamerule.locatorBar": "Z<PERSON>ł<PERSON><PERSON>z ôbiyżny pŏsek", "gamerule.locatorBar.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eli je pokŏzany ôbiyżny pŏsek na ykrōnie, coby sk<PERSON><PERSON><PERSON> kerōnek ku inkszym grŏczōm.", "gamerule.logAdminCommands": "Rozgłŏszanie kōmynd", "gamerule.maxCommandChainLength": "Limit dug<PERSON> kety kōmynd", "gamerule.maxCommandChainLength.description": "Tyczy sie fōnkcyji a ketōw kōmyndowych blokōw.", "gamerule.maxCommandForkCount": "Limit kōntekstōw kōmynd", "gamerule.maxCommandForkCount.description": "Maks<PERSON>alnŏ liczba kōntekstōw, kere ś<PERSON><PERSON><PERSON> p<PERSON> we gangu kōmynd zorty „execute as”.", "gamerule.maxEntityCramming": "<PERSON><PERSON><PERSON>żyniŏ bytōw", "gamerule.minecartMaxSpeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gib<PERSON> wagōnu", "gamerule.minecartMaxSpeed.description": "Maksymalnŏ sztandardowŏ gib<PERSON> wagōnu, kery sie rusz<PERSON> na ziymi.", "gamerule.mobExplosionDropDecay": "Znikanie tajle blokōw wysadzōnych ôd stworzyń", "gamerule.mobExplosionDropDecay.description": "Traci sie tajla rzeczy wypadnytych ze blokōw wysadzōnych ôd stworzyń.", "gamerule.mobGriefing": "Stworzynia śmiōm niszcz<PERSON>ć ô<PERSON>czynie", "gamerule.naturalRegeneration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.playersNetherPortalCreativeDelay": "Ôpōźniynie przełażyniŏ bez portale Netheru we kreatywnym trybie", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON>iela czasu (we tikach) grŏcz musi wystŏć we portalu ku Netherze kedy je we kreatywnym trybie, niż sie przeniysie ku innymu wymiarze.", "gamerule.playersNetherPortalDefaultDelay": "Ôpōźniynie przełażyniŏ bez portale Netheru we niykreatywnych trybach", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON>iela czasu (we tikach) grŏcz musi wystŏć we portalu ku Netherze kedy niy ma we kreatywnym trybie, niż sie przeniysie ku innymu wymiarze.", "gamerule.playersSleepingPercentage": "Procynt <PERSON><PERSON><PERSON><PERSON><PERSON>, kerzy musz<PERSON><PERSON> spać", "gamerule.playersSleepingPercentage.description": "<PERSON><PERSON>nt <PERSON><PERSON><PERSON><PERSON><PERSON>, ker<PERSON> mus<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, coby p<PERSON><PERSON> noc.", "gamerule.projectilesCanBreakBlocks": "<PERSON><PERSON><PERSON><PERSON> bloki", "gamerule.projectilesCanBreakBlocks.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eli impaktowe szusy śmiōm niszczyć niyôbstołe bloki.", "gamerule.randomTickSpeed": "Tympo losowych tikōw", "gamerule.reducedDebugInfo": "<PERSON><PERSON>j informacyji <PERSON>", "gamerule.reducedDebugInfo.description": "Ukrŏcŏ informacyje na ykrōnie debugowaniŏ", "gamerule.sendCommandFeedback": "Posyłanie zwrotnych informacyji ôd kōmynd", "gamerule.showDeathMessages": "Kōmunikaty ô zgōnach", "gamerule.snowAccumulationHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.snowAccumulationHeight.description": "Podczas <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> wŏrsztwy bydōm sie <PERSON>, aż niy ôsiōngnōm podanyj liczby wŏrsztw.", "gamerule.spawnChunkRadius": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.spawnChunkRadius.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kere ôstanōm zaladowane naôbkoło pōnk<PERSON>drodzyni<PERSON>.", "gamerule.spawnRadius": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.spawnRadius.description": "Kōntrolu<PERSON> wielgo<PERSON>ć przestrzyństwa naôbkoło pōnktu, we kerym grŏcze śmiōm sie ôdr<PERSON>.", "gamerule.spectatorsGenerateChunks": "<PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>u ôd <PERSON>", "gamerule.tntExplodes": "Ôdpŏlanie a abszusy TNT", "gamerule.tntExplosionDropDecay": "Znikanie tajle blokōw wysadzōnych ôd TNT", "gamerule.tntExplosionDropDecay.description": "Traci sie tajla rzeczy wypadnytych ze blokōw wysadzōnych ôd TNT.", "gamerule.universalAnger": "Ôgōlny gniyw", "gamerule.universalAnger.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eli zgorsz<PERSON>ne ôbojyntnie nastŏwiōne stworzynia napŏdajōm wszyckich pobliskich grŏczy, niy yno gorszyciyli. <PERSON><PERSON><PERSON><PERSON>jlepij, kej <PERSON> `forgiveDeadPlayers` je wyłōncz<PERSON>ne.", "gamerule.waterSourceConversion": "Przeksztołcanie sie wody we zdrzōdło", "gamerule.waterSourceConversion.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> w<PERSON>, co płynie sama sie z<PERSON>yniŏ we zdrzōdło, kej je ôbtoczōn<PERSON> ôd zdrzōdeł wody ze ôbōch zajtōw.", "generator.custom": "Niysz<PERSON><PERSON>dowy", "generator.customized": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (stary)", "generator.minecraft.amplified": "WIELGACHNY", "generator.minecraft.amplified.info": "Pozōr: Yno ku szpasu! Potrzebny fest dobry kōmputer.", "generator.minecraft.debug_all_block_states": "<PERSON>b debugowaniŏ", "generator.minecraft.flat": "Blank płaski", "generator.minecraft.large_biomes": "<PERSON><PERSON><PERSON>", "generator.minecraft.normal": "Sztandardowy", "generator.minecraft.single_biome_surface": "Pojedync<PERSON>", "generator.single_biome_caves": "<PERSON><PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "Lata<PERSON><PERSON> wyspy", "gui.abuseReport.attestation": "Bez posyłanie zgłoszy<PERSON>ŏ <PERSON>, iże informacyje, kere sōm we nim zawarte sōm zgodliwe ze prŏwdōm a dokumyntne podug twojij wiedzy.", "gui.abuseReport.comments": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.describe": "Udostympniynie ôsobliwości pōmoże nōm podyjmnyć trefnyj decyzyje.", "gui.abuseReport.discard.content": "<PERSON><PERSON><PERSON>ś<PERSON> tã zajtã, zgłoszynie a kōmyntŏrze ôstanōm stracōne.\nNa isto ch<PERSON>z wylyź?", "gui.abuseReport.discard.discard": "Wylyź a pociep zgłoszynie", "gui.abuseReport.discard.draft": "Zachowej za szkic", "gui.abuseReport.discard.return": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "gui.abuseReport.discard.title": "Pociepać zgłoszynie a kōmyntŏrze?", "gui.abuseReport.draft.content": "Chcesz dalij <PERSON> zgł<PERSON>zynie, co już is<PERSON>, abo je pociepać a stwo<PERSON><PERSON> nowe?", "gui.abuseReport.draft.discard": "Pociep", "gui.abuseReport.draft.edit": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "gui.abuseReport.draft.quittotitle.content": "Chcesz dalij <PERSON> zgłoszynie, co już is<PERSON>, abo je pocie<PERSON>?", "gui.abuseReport.draft.quittotitle.title": "Mŏsz szkic zgłoszyniŏ czatu, kery sie straci, e<PERSON><PERSON> ter<PERSON>z chanysz tworzyniŏ", "gui.abuseReport.draft.title": "Chcesz edytować szkic zgłoszyniŏ?", "gui.abuseReport.error.title": "Feler przi posyłaniu twojigo zgłoszyniŏ", "gui.abuseReport.message": "Kaj żeś ujzdrzoł niynŏleżne zachowanie?\nTwōj ôpis pōmoże nōm we zbadaniu sprawy.", "gui.abuseReport.more_comments": "Ôpisz, co zaszło:", "gui.abuseReport.name.comment_box_label": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> to mi<PERSON>:", "gui.abuseReport.name.reporting": "Zgłŏszŏsz „%s”.", "gui.abuseReport.name.title": "Zgłoś miano grŏcza", "gui.abuseReport.observed_what": "Jaki je powōd ku zgłoszyniu?", "gui.abuseReport.read_info": "<PERSON><PERSON><PERSON><PERSON><PERSON> ô zgłŏszaniu", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drogi abo alko<PERSON>", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "<PERSON><PERSON><PERSON> zachyncŏ ku dzio<PERSON><PERSON>, kere sōm zwiōnzane ze niyprawnymi zubstancyjami abo namŏwiŏ smarkŏczy ku używaniu alkoholu.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Gwołt abo seksualne wykorzystywanie bajtli", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON><PERSON><PERSON>gŏduje niynŏleżne postympowanie wobec dzieci abo je zachwŏlŏ.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Ôszeredzanie", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Gdoś szargŏ czyjeś dobre miano, bez przikłŏd po szyrzyniu fałszywych informacyji we cylu ôszydzaniŏ abo wkludzaniŏ drugich we błōnd.", "gui.abuseReport.reason.description": "Ôpis:", "gui.abuseReport.reason.false_reporting": "Niyprawe zgłŏszanie", "gui.abuseReport.reason.generic": "Na isto chcã posłać zgłoszynie", "gui.abuseReport.reason.generic.description": "Tyn grŏcz mie szteruje abo jego postympowanie mi uprzikrzŏ", "gui.abuseReport.reason.harassment_or_bullying": "Drynczynie abo nōnkanie", "gui.abuseReport.reason.harassment_or_bullying.description": "Gdoś cie pōniżŏ, <PERSON><PERSON><PERSON><PERSON> abo nynkŏ abo kogoś inkszego. To tykŏ tyż sytuacyje, kej gdoś bez twojij zgody prōbuje sie wielokrotnie skōntaktować ze tobōm abo ze inkszōm ôsobōm abo publikuje prywatne ôsobiste informacyje ô ciebie abo ô kimś inkszym („doxing”).", "gui.abuseReport.reason.hate_speech": "Niynŏwistnŏ gŏdka", "gui.abuseReport.reason.hate_speech.description": "Gdoś cie napŏdŏ abo inkszego grŏcza skuli <PERSON>sobliwych znakōw takich jak religijŏ, rasa abo seks<PERSON><PERSON><PERSON> z<PERSON>.", "gui.abuseReport.reason.imminent_harm": "Groźba skrziwdzyniŏ drugich", "gui.abuseReport.reason.imminent_harm.description": "<PERSON><PERSON> g<PERSON>, i<PERSON>e chce skrzywdzić ciebie abo drugõ <PERSON> we prŏwdziwym życiu.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Intymne bildy bez zgody drugij <PERSON>", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON>ś gŏdŏ ô intymnych a prywatnych bildach, je udostympniŏ abo we inkszy spusōb je s<PERSON>rzi.", "gui.abuseReport.reason.self_harm_or_suicide": "Samozraniynie abo samobōjstwo", "gui.abuseReport.reason.self_harm_or_suicide.description": "<PERSON><PERSON>ś <PERSON> ôd wyrzōndzyniŏ siebie krziwdy we prŏwdziwym życiu abo ô tym gŏd<PERSON>.", "gui.abuseReport.reason.sexually_inappropriate": "<PERSON><PERSON><PERSON> g<PERSON>dki", "gui.abuseReport.reason.sexually_inappropriate.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kere zawiyrajōm graficzne treści ze ôdniesiyniym ku seksualnym dziołaniōm, płciowym ôrganōm abo seksualnyj przemocy.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terroryzm abo agresywny ekstrymizm", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON>ś <PERSON>sprawiŏ ô terroryźmie abo krajnym ekstrymiźmie, je prōmuje abo zachyncŏ ku ich wykōnywaniu abo strŏszŏ ôd popołniyniŏ ich skuli politycznych, wyz<PERSON>iowych, idyjologicznych abo inkszych zglyndōw.", "gui.abuseReport.reason.title": "Ôbier kategory<PERSON><PERSON> zgłoszyniŏ", "gui.abuseReport.report_sent_msg": "Przijmnyliśmy twoje zgłoszynie. Dziynkujymy!\n\nNŏsz zespōł je przejzdrzi tak rychle, jak yno idzie.", "gui.abuseReport.select_reason": "Ôbier kategory<PERSON><PERSON> zgłoszyniŏ", "gui.abuseReport.send": "Posłej zgłoszynie", "gui.abuseReport.send.comment_too_long": "T<PERSON><PERSON><PERSON> je za dugi", "gui.abuseReport.send.error_message": "Feler przi posyłaniu twojigo zgłoszyniŏ:\n„%s”", "gui.abuseReport.send.generic_error": "Feler przi posyłaniu twojigo zgłoszyniŏ.", "gui.abuseReport.send.http_error": "Feler HTTP podczas posyłaniŏ zgłoszyniŏ.", "gui.abuseReport.send.json_error": "Natrefiōno popsowane dane podczas posyłaniŏ zgłoszyniŏ.", "gui.abuseReport.send.no_reason": "Ôbier kategory<PERSON><PERSON> zgłoszyniŏ", "gui.abuseReport.send.not_attested": "Przeczytej powyższy tekst a zaznacz pole ôbioru, coby posła<PERSON> zgłoszynie", "gui.abuseReport.send.service_unavailable": "Niy idzie połōnczyć ze systymym zgłoszaniŏ nadużyciŏ. Proszymy spr<PERSON>, iżeś mŏsz necowe połōnczynie, a zatym sprōbuj na zaś.", "gui.abuseReport.sending.title": "Posyłanie zgłoszyniŏ…", "gui.abuseReport.sent.title": "Posłano zgłoszynie", "gui.abuseReport.skin.title": "Zgłoś skōrkã grŏcza", "gui.abuseReport.title": "Zgłoś grŏcza", "gui.abuseReport.type.chat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.type.name": "Miano", "gui.abuseReport.type.skin": "Skōrka", "gui.acknowledge": "Rozumiã", "gui.advancements": "Postympy", "gui.all": "Wszycko", "gui.back": "Nazŏd", "gui.banned.description": "%s\n\n%s\n\nPrzewiydz sie wiyncyj pod linkym: %s", "gui.banned.description.permanent": "Twoje kōnto ôstało na sztyjc zaszperowane, co znaczy, iże niy śmisz już grać onlajn ani dołōnczać ku Realms.", "gui.banned.description.reason": "Niydŏwno nŏs powiadōmiōno ô niynŏleżnym postympowaniu na twojim kōncie. Nasi autorzi wybadali twoje dziołania a je spoznali jako %s, co naruszŏ nasze Nakŏzania Społeczności.", "gui.banned.description.reason_id": "Kod: %s", "gui.banned.description.reason_id_message": "Kod: %s — %s", "gui.banned.description.temporary": "%s <PERSON><PERSON><PERSON> tym czasym niy śmisz grać we mocperzōnowym trybie ani dołōnczać ku świŏtōm Realms.", "gui.banned.description.temporary.duration": "Twoje kōnto ôstało tymczasowo bloknyte a ôstanie na zaś aktywowane za %s.", "gui.banned.description.unknownreason": "Niydŏwno nŏs powiadōmiōno ô niynŏleżnym postympowaniu na twojim kōncie. Nasi autorzi wybadali twoje dziołania a stwiyrdziyli, iże naruszajōm ône nasze Nakŏzania Społeczności.", "gui.banned.name.description": "<PERSON><PERSON> — „%s” — naruszŏ nasze Nakŏzania Społeczności. <PERSON><PERSON><PERSON> grać we jednoperzōnowym trybie, ale niż bydziesz mōg zagrać onlajn, k<PERSON><PERSON>czn<PERSON> bydzie zmiana twojigo miana używŏcza.\n\nUzyskej wiyncyj informacyji abo ôdwołej sie pod dalszym linkym: %s", "gui.banned.name.title": "Miano niyprzizwolōne we trybie mocperzōnowyj gry", "gui.banned.reason.defamation_impersonation_false_information": "Udŏwanie inkszyj ôsoby abo szyrzynie informacyji, kere majōm na cylu zbałamōńcić abo zausbojtować drugich", "gui.banned.reason.drugs": "Ôdniesiynia ku narkotykōm", "gui.banned.reason.extreme_violence_or_gore": "Przedstŏwianie nadbytnij siyły abo ôkruciyństwa ze prŏwdziwego życiŏ", "gui.banned.reason.false_reporting": "Posyłanie nadbytnij wielości niyprŏwdziwych abo niydokumyntnych zgoszyń", "gui.banned.reason.fraud": "Pozyskiwanie abo używanie treści we ôszydliwy spusōb", "gui.banned.reason.generic_violation": "Naruszynie Nakŏzań Społeczności", "gui.banned.reason.harassment_or_bullying": "Ôbraźliwŏ gŏdka używanŏ we skerowany, szkodliwy spusōb", "gui.banned.reason.hate_speech": "Niynŏwistnŏ gŏdka abo dyskryminacyjŏ", "gui.banned.reason.hate_terrorism_notorious_figure": "Ôdniesiyniŏ ku grupōm, kere szyrzō<PERSON>, terrorystycznym ôrganizacyjōm abo perzōnōm ô złyj sławie", "gui.banned.reason.imminent_harm_to_person_or_property": "Forzac ku wyrzōnd<PERSON>niu istyj szkody ôsobōm abo statkowi", "gui.banned.reason.nudity_or_pornography": "Pokazowanie zmysłowych abo pornograficznych materyji", "gui.banned.reason.sexually_inappropriate": "<PERSON><PERSON> abo inhalt ô seksualnyj istocie", "gui.banned.reason.spam_or_advertising": "Spam abo reklama", "gui.banned.skin.description": "Twoja ôbecnŏ skōrka naruszŏ nasze Nakŏzania Społeczności. Durch śmisz grać ze sztandardowōm skōrkōm abo możesz tyż <PERSON> nowõ skōrkã.\n\nPrzewiydz sie wiyncyj abo posłej sprŏwdzynie sprawy pod dalszym linkym: %s", "gui.banned.skin.title": "Niyprzizwolōnŏ skōrka", "gui.banned.title.permanent": "Kōnto ôstało na sztyjc bloknyte", "gui.banned.title.temporary": "Kōnto tymczasowo bloknyte", "gui.cancel": "Pociep", "gui.chatReport.comments": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.describe": "Udostympniynie ôsobliwości pōmoże nōm podyjmnyć trefnyj decyzyje.", "gui.chatReport.discard.content": "<PERSON><PERSON><PERSON>ś<PERSON> tã zajtã, zgłoszynie a kōmyntŏrze ôstanōm stracōne.\nNa isto ch<PERSON>z wylyź?", "gui.chatReport.discard.discard": "Wylyź a pociep zgłoszynie", "gui.chatReport.discard.draft": "Zachowej za szkic", "gui.chatReport.discard.return": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "gui.chatReport.discard.title": "Pociepać zgłoszynie a kōmyntŏrze?", "gui.chatReport.draft.content": "Chcesz dalij <PERSON> zgł<PERSON>zynie, co już is<PERSON>, abo je pociepać a stwo<PERSON><PERSON> nowe?", "gui.chatReport.draft.discard": "Pociep", "gui.chatReport.draft.edit": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "gui.chatReport.draft.quittotitle.content": "Chcesz dalij <PERSON> zgłoszynie, co już is<PERSON>, abo je pocie<PERSON>?", "gui.chatReport.draft.quittotitle.title": "Mŏsz szkic zgłoszyniŏ czatu, kery sie straci, e<PERSON><PERSON> ter<PERSON>z chanysz tworzyniŏ", "gui.chatReport.draft.title": "Chcesz edytować szkic zgłoszyniŏ?", "gui.chatReport.more_comments": "Ôpisz, co zaszło:", "gui.chatReport.observed_what": "<PERSON>aki je powōd zgoszyniŏ?", "gui.chatReport.read_info": "W<PERSON>ync<PERSON>j ô posyłaniu zgłoszyń", "gui.chatReport.report_sent_msg": "Przijmnyliśmy twoje zgłoszynie. Dziynkujymy!\n\nNŏsz zespōł przejzdrzi je tak rychle, jak yno idzie.", "gui.chatReport.select_chat": "Ôbier czatowe wiadōmości ku zgłoszyniu", "gui.chatReport.select_reason": "Ôbier kategory<PERSON><PERSON> zgłoszyniŏ", "gui.chatReport.selected_chat": "Liczba ôbranych wiadōmości ku zgłoszyniu: %s", "gui.chatReport.send": "Posłej zgłoszynie", "gui.chatReport.send.comments_too_long": "T<PERSON><PERSON><PERSON> je za dugi", "gui.chatReport.send.no_reason": "Ôbier kategory<PERSON><PERSON> zgłoszyniŏ", "gui.chatReport.send.no_reported_messages": "Ôbier aby jedn<PERSON> wiadōmość ku zgłoszyniu", "gui.chatReport.send.too_many_messages": "Za moc wiadōmości we zgłoszyniu", "gui.chatReport.title": "Zgłoś grŏcza", "gui.chatSelection.context": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kere <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tyn <PERSON>r byd<PERSON><PERSON>, coby z<PERSON><PERSON><PERSON><PERSON> hilfowego kō<PERSON>", "gui.chatSelection.fold": "Skryto %s wiadōmości", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "Grŏcz %s dołōnczōł ku czatu", "gui.chatSelection.message.narrate": "Grŏcz %s napisoł „%s” ô %s", "gui.chatSelection.selected": "Liczba ôbranych wiadōmości: %s/%s", "gui.chatSelection.title": "Ôbier czatowe wiadōmości ku zgłoszyniu", "gui.continue": "<PERSON><PERSON><PERSON>", "gui.copy_link_to_clipboard": "Skopiyruj link ku schowku", "gui.days": "%s dni", "gui.done": "<PERSON><PERSON><PERSON>", "gui.down": "<PERSON><PERSON><PERSON>", "gui.entity_tooltip.type": "Zorta: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Liczba ôdciepanych datajōw: %s", "gui.fileDropFailure.title": "<PERSON><PERSON> podarziło sie przidać datajōw", "gui.hours": "%s godzin", "gui.loadingMinecraft": "<PERSON><PERSON><PERSON><PERSON>", "gui.minutes": "%s minut", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "knefel %s", "gui.narrate.editBox": "tekstowe pole %s: %s", "gui.narrate.slider": "szalter %s", "gui.narrate.tab": "Zŏgibka %s", "gui.no": "<PERSON><PERSON>", "gui.none": "Bez", "gui.ok": "<PERSON><PERSON>", "gui.open_report_dir": "Ôdewrzij katalŏk raportōw", "gui.proceed": "<PERSON><PERSON><PERSON>", "gui.recipebook.moreRecipes": "<PERSON><PERSON>ś prawy knefel myszy po wiyncyj", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "<PERSON><PERSON><PERSON><PERSON>…", "gui.recipebook.toggleRecipes.all": "Poka<PERSON>je w<PERSON>cke", "gui.recipebook.toggleRecipes.blastable": "Pokazuje szmelcowalne we hamerze", "gui.recipebook.toggleRecipes.craftable": "Pokazuje wytwŏrzalne", "gui.recipebook.toggleRecipes.smeltable": "Pokazuje szmelcowalne", "gui.recipebook.toggleRecipes.smokable": "Po<PERSON><PERSON><PERSON> w<PERSON>d<PERSON>", "gui.report_to_server": "Zgłoś serwerowi", "gui.socialInteractions.blocking_hint": "Zarzōndzej po użyciu kōnta Microsoft", "gui.socialInteractions.empty_blocked": "Niy ma żŏdnych bloknytych grŏczy na czacie", "gui.socialInteractions.empty_hidden": "Niy ma żŏdnych skrytych grŏczy na czacie", "gui.socialInteractions.hidden_in_chat": "Wiadōmości posyłane ôd grŏcza %s byd<PERSON>m s<PERSON>e", "gui.socialInteractions.hide": "Skryj na czacie", "gui.socialInteractions.narration.hide": "Skrywej wiadōmości ôd grŏcza %s", "gui.socialInteractions.narration.report": "Zgłoś grŏcza %s", "gui.socialInteractions.narration.show": "Pokazuj wiadōmości ôd grŏcza %s", "gui.socialInteractions.report": "Zgł<PERSON>ś", "gui.socialInteractions.search_empty": "Niy znŏjdzōno grŏczy ô takim mianie", "gui.socialInteractions.search_hint": "<PERSON><PERSON><PERSON><PERSON>…", "gui.socialInteractions.server_label.multiple": "%s — %s grŏczy", "gui.socialInteractions.server_label.single": "%s — %s grŏcz", "gui.socialInteractions.show": "Pokŏż na czacie", "gui.socialInteractions.shown_in_chat": "Wiadōmości posyłane ôd grŏcza %s bydōm widoczne", "gui.socialInteractions.status_blocked": "Grŏcz je bloknyty", "gui.socialInteractions.status_blocked_offline": "Bloknyty — Oflajn", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Skryty — Oflajn", "gui.socialInteractions.status_offline": "Gr<PERSON><PERSON> je offline", "gui.socialInteractions.tab_all": "Wsz<PERSON><PERSON>", "gui.socialInteractions.tab_blocked": "Bloknyci", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "Społeczne interakcyje", "gui.socialInteractions.tooltip.hide": "Skrywej w<PERSON>", "gui.socialInteractions.tooltip.report": "Zgłoś grŏcza", "gui.socialInteractions.tooltip.report.disabled": "Zgłoszyniowy systym niyma dostympny", "gui.socialInteractions.tooltip.report.no_messages": "Niy ma żŏdnych wiadōmości ôd grŏcza %s, kere <PERSON><PERSON> zgł<PERSON>", "gui.socialInteractions.tooltip.report.not_reportable": "<PERSON><PERSON> ś<PERSON>gł<PERSON> tego g<PERSON>, pōniywŏż niy idzie zweryficiyrować jego wiadōmości na tym serwerze", "gui.socialInteractions.tooltip.show": "Poka<PERSON>j w<PERSON>", "gui.stats": "Sztatystyki", "gui.toMenu": "Nazŏd ku spisce serwerōw", "gui.toRealms": "Na<PERSON>ŏd ku spisce <PERSON>w", "gui.toTitle": "Na<PERSON>ŏd ku titlowymu <PERSON>", "gui.toWorld": "Nazŏd ku spisce świŏtōw", "gui.togglable_slot": "<PERSON><PERSON><PERSON>, coby w<PERSON><PERSON><PERSON><PERSON><PERSON> pole", "gui.up": "<PERSON><PERSON><PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "Nazŏd (%ss)", "gui.waitingForResponse.title": "Ôczekowanie na serwer", "gui.yes": "<PERSON>a", "hanging_sign.edit": "<PERSON><PERSON><PERSON><PERSON> inhalt szildu", "instrument.minecraft.admire_goat_horn": "Podziw", "instrument.minecraft.call_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.dream_goat_horn": "Śnik", "instrument.minecraft.feel_goat_horn": "Po<PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Rozwŏżanie", "instrument.minecraft.seek_goat_horn": "Poszukowanie", "instrument.minecraft.sing_goat_horn": "Śpiyw", "instrument.minecraft.yearn_goat_horn": "Tyschnica", "inventory.binSlot": "Zniszcz rzecz", "inventory.hotbarInfo": "Zachowej pole po kōmbinacyji %1$s + %2$s", "inventory.hotbarSaved": "Zachowano pole (p<PERSON><PERSON><PERSON><PERSON><PERSON> po kōmbinacyji %1$s + %2$s)", "item.canBreak": "Poradzi zniszczyć:", "item.canPlace": "Poradzi stŏć na:", "item.canUse.unknown": "Niyznany", "item.color": "Farba: %s", "item.components": "%s skłŏdnikōw", "item.disabled": "Wyłōnczōnŏ rzecz", "item.durability": "Strzimałość: %s / %s", "item.dyed": "Farbiōny", "item.minecraft.acacia_boat": "Agacowŏ łōdka", "item.minecraft.acacia_chest_boat": "Agacowŏ łōdka ze kastlōm", "item.minecraft.allay_spawn_egg": "Jajco ze przŏciuchym", "item.minecraft.amethyst_shard": "Szpliter ametystu", "item.minecraft.angler_pottery_shard": "Szpliter ze wyndkōm", "item.minecraft.angler_pottery_sherd": "Szpliter ze wyndkōm", "item.minecraft.apple": "<PERSON><PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Szpliter ze łukym", "item.minecraft.archer_pottery_sherd": "Szpliter ze łukym", "item.minecraft.armadillo_scute": "Ôbryncznikowŏ szupa", "item.minecraft.armadillo_spawn_egg": "Jajco ze ôbryncznikym", "item.minecraft.armor_stand": "Sztynder na pancer", "item.minecraft.arms_up_pottery_shard": "Szpliter ze zniysiōnymi rynkami", "item.minecraft.arms_up_pottery_sherd": "Szpliter ze zniysiōnymi rynkami", "item.minecraft.arrow": "Strzała", "item.minecraft.axolotl_bucket": "Ajmer ze aksolotlym", "item.minecraft.axolotl_spawn_egg": "Jajco ze aksolotlym", "item.minecraft.baked_potato": "Piyczŏk", "item.minecraft.bamboo_chest_raft": "Bambusowŏ mataczka ze kastlōm", "item.minecraft.bamboo_raft": "Bambusowŏ mataczka", "item.minecraft.bat_spawn_egg": "Jajco ze flyjdermausym", "item.minecraft.bee_spawn_egg": "Jajco ze binōm", "item.minecraft.beef": "Surowŏ ôwiynzina", "item.minecraft.beetroot": "Ćwikla", "item.minecraft.beetroot_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_soup": "Bŏrszcz", "item.minecraft.birch_boat": "Brzizkowŏ łōdka", "item.minecraft.birch_chest_boat": "Brzizkowŏ łōdka ze kastlōm", "item.minecraft.black_bundle": "Czŏrny miyszek", "item.minecraft.black_dye": "Czŏrn<PERSON> farbsztof", "item.minecraft.black_harness": "Czŏrny gyszir", "item.minecraft.blade_pottery_shard": "Szpliter ze sznajdōm", "item.minecraft.blade_pottery_sherd": "Szpliter ze sznajdōm", "item.minecraft.blaze_powder": "Flamŏkowy pulwer", "item.minecraft.blaze_rod": "Flamŏkowŏ ruta", "item.minecraft.blaze_spawn_egg": "Jajco ze flamŏkym", "item.minecraft.blue_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blue_egg": "<PERSON><PERSON>", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "Jajco ze bagyniŏkym", "item.minecraft.bolt_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.bolt_armor_trim_smithing_template.new": "Ôrnamynt — Szrauba", "item.minecraft.bone": "Kostyra", "item.minecraft.bone_meal": "Knochynmyjl", "item.minecraft.book": "Ksiōnżka", "item.minecraft.bordure_indented_banner_pattern": "Muster fany — Faltowanŏ rōma", "item.minecraft.bow": "<PERSON><PERSON>", "item.minecraft.bowl": "Sziselka", "item.minecraft.bread": "<PERSON><PERSON><PERSON>", "item.minecraft.breeze_rod": "Wiytrzikowŏ ruta", "item.minecraft.breeze_spawn_egg": "Jajco ze wiytrzikym", "item.minecraft.brewer_pottery_shard": "Szpliter ze wywarym", "item.minecraft.brewer_pottery_sherd": "Szpliter ze wywarym", "item.minecraft.brewing_stand": "Sztynder ku warzyniu", "item.minecraft.brick": "Cegła", "item.minecraft.brown_bundle": "Brōnŏtny miyszek", "item.minecraft.brown_dye": "Brōnŏtny farbsztof", "item.minecraft.brown_egg": "Brōnŏtne jajco", "item.minecraft.brown_harness": "Brōnŏtny gyszir", "item.minecraft.brush": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bucket": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle": "Miyszek", "item.minecraft.bundle.empty": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty.description": "<PERSON><PERSON> ch<PERSON> sztapel roztōmańtych rzeczy", "item.minecraft.bundle.full": "Połny", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Szpliter ze ôgniym", "item.minecraft.burn_pottery_sherd": "Szpliter ze ôgniym", "item.minecraft.camel_spawn_egg": "Jajco ze kamelōm", "item.minecraft.carrot": "<PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "Markew na kijŏku", "item.minecraft.cat_spawn_egg": "Jajco ze kotym", "item.minecraft.cauldron": "<PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Jajco ze jaskiniowym pajōnkym", "item.minecraft.chainmail_boots": "<PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.chainmail_chestplate": "<PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.chainmail_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.chainmail_leggings": "<PERSON><PERSON>we <PERSON>", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>l", "item.minecraft.cherry_boat": "Trześniowŏ łōdka", "item.minecraft.cherry_chest_boat": "Trześniowŏ łōdka ze kastlōm", "item.minecraft.chest_minecart": "Wagōn ze kastlōm", "item.minecraft.chicken": "<PERSON><PERSON><PERSON> kura", "item.minecraft.chicken_spawn_egg": "Jajco ze kurōm", "item.minecraft.chorus_fruit": "Ôwoc refrynice", "item.minecraft.clay_ball": "<PERSON><PERSON> gliny", "item.minecraft.clock": "Zygŏr", "item.minecraft.coal": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.coast_armor_trim_smithing_template.new": "Ôrnamynt — Wybrzeże", "item.minecraft.cocoa_beans": "Ziŏrna kakau", "item.minecraft.cod": "Surowy dorsz", "item.minecraft.cod_bucket": "Ajmer ze dorszym", "item.minecraft.cod_spawn_egg": "Jajco ze dorszym", "item.minecraft.command_block_minecart": "Wagōn ze blokym kōmynd", "item.minecraft.compass": "Kōmpas", "item.minecraft.cooked_beef": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_chicken": "Piecz<PERSON>n<PERSON> kura", "item.minecraft.cooked_cod": "Pieczōny dorsz", "item.minecraft.cooked_mutton": "Pie<PERSON><PERSON>n<PERSON> szkopina", "item.minecraft.cooked_porkchop": "Pieczōnŏ wieprzowina", "item.minecraft.cooked_rabbit": "Piyczōny mycŏk", "item.minecraft.cooked_salmon": "Pieczōny laks", "item.minecraft.cookie": "Kyjks", "item.minecraft.copper_ingot": "Sztangelka miydzi", "item.minecraft.cow_spawn_egg": "Jajco ze krŏwōm", "item.minecraft.creaking_spawn_egg": "Jajco ze grzipŏczym", "item.minecraft.creeper_banner_pattern": "Muster fany", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per", "item.minecraft.creeper_banner_pattern.new": "Muster fany — <PERSON><PERSON><PERSON>", "item.minecraft.creeper_spawn_egg": "Jajco ze creeperym", "item.minecraft.crossbow": "<PERSON><PERSON><PERSON>", "item.minecraft.crossbow.projectile": "Szus:", "item.minecraft.crossbow.projectile.multiple": "Szus: %s × %s", "item.minecraft.crossbow.projectile.single": "Szus: %s", "item.minecraft.cyan_bundle": "Cyjanowy miyszek", "item.minecraft.cyan_dye": "Cyjanow<PERSON> farbsztof", "item.minecraft.cyan_harness": "Cyjanowy gyszir", "item.minecraft.danger_pottery_shard": "Szpliter ze creeperym", "item.minecraft.danger_pottery_sherd": "Szpliter ze creeperym", "item.minecraft.dark_oak_boat": "Ćmawodymbowŏ łōdka", "item.minecraft.dark_oak_chest_boat": "Ćmawodymbowŏ łōdka ze kastlōm", "item.minecraft.debug_stick": "Kijŏk ku debugowaniu", "item.minecraft.debug_stick.empty": "%s niy m<PERSON> włŏsności", "item.minecraft.debug_stick.select": "ôbrano „%s” (%s)", "item.minecraft.debug_stick.update": "nasztalōwano „%s” na: %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "Dyjamyntowŏ cieślica", "item.minecraft.diamond_boots": "Dyjamyntowe strzewiki", "item.minecraft.diamond_chestplate": "Dyjamyntowy kyras", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> helm", "item.minecraft.diamond_hoe": "Dyjamyntowy kopŏcz", "item.minecraft.diamond_horse_armor": "Dyjamynto<PERSON> k<PERSON> pancer", "item.minecraft.diamond_leggings": "Dyjamyntowe galŏty", "item.minecraft.diamond_pickaxe": "Dyjamyntowŏ haka", "item.minecraft.diamond_shovel": "Dyjamyntowy ryl", "item.minecraft.diamond_sword": "Dyjamyntowy miycz", "item.minecraft.disc_fragment_5": "Skrŏwek dysketki", "item.minecraft.disc_fragment_5.desc": "Dysketka — 5", "item.minecraft.dolphin_spawn_egg": "Jajco ze delfinym", "item.minecraft.donkey_spawn_egg": "Jajco ze yjzlym", "item.minecraft.dragon_breath": "Drachowy dych", "item.minecraft.dried_kelp": "Wysuszōnŏ waserflanca", "item.minecraft.drowned_spawn_egg": "Jajco ze utopkym", "item.minecraft.dune_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.dune_armor_trim_smithing_template.new": "Ôrnamynt — Wyd<PERSON>", "item.minecraft.echo_shard": "Szpliter pogłosu", "item.minecraft.egg": "Jajco", "item.minecraft.elder_guardian_spawn_egg": "Jajco ze chańdŏwnym wachtyrzym", "item.minecraft.elytra": "Ôkrywy", "item.minecraft.emerald": "Szmaragd", "item.minecraft.enchanted_book": "Zaklyntŏ ksiōnżka", "item.minecraft.enchanted_golden_apple": "Zaklyntŏ złōtŏ pōnka", "item.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_dragon_spawn_egg": "Jajco ze drachym Endu", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Jajco ze endermanym", "item.minecraft.endermite_spawn_egg": "Jajco ze endermitym", "item.minecraft.evoker_spawn_egg": "Jajco ze guślŏrzym", "item.minecraft.experience_bottle": "Flaszka doświŏdczyniŏ", "item.minecraft.explorer_pottery_shard": "Szpliter ze kartōm", "item.minecraft.explorer_pottery_sherd": "Szpliter ze kartōm", "item.minecraft.eye_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.eye_armor_trim_smithing_template.new": "Ôrnamynt — <PERSON><PERSON><PERSON>", "item.minecraft.feather": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "Zger<PERSON>ne <PERSON> p<PERSON>", "item.minecraft.field_masoned_banner_pattern": "<PERSON>er fany — <PERSON><PERSON>", "item.minecraft.filled_map": "Karta", "item.minecraft.fire_charge": "Ôgnistŏ kula", "item.minecraft.firework_rocket": "Fojerwerk", "item.minecraft.firework_rocket.flight": "Czas lotu:", "item.minecraft.firework_rocket.multiple_stars": "%s × %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Fojerwerkowŏ gwiŏzda", "item.minecraft.firework_star.black": "Czŏrnŏ", "item.minecraft.firework_star.blue": "Modrŏ", "item.minecraft.firework_star.brown": "Brōnŏtnŏ", "item.minecraft.firework_star.custom_color": "Niysztandardowŏ", "item.minecraft.firework_star.cyan": "<PERSON>janowŏ", "item.minecraft.firework_star.fade_to": "Przechodzynie w", "item.minecraft.firework_star.flicker": "Iskrzynie", "item.minecraft.firework_star.gray": "Siwŏ", "item.minecraft.firework_star.green": "Zielōn<PERSON>", "item.minecraft.firework_star.light_blue": "Światłomodrŏ", "item.minecraft.firework_star.light_gray": "Światłosiwŏ", "item.minecraft.firework_star.lime": "Limōnkowŏ", "item.minecraft.firework_star.magenta": "Purpurowŏ", "item.minecraft.firework_star.orange": "Ôranżowŏ", "item.minecraft.firework_star.pink": "Rōżowŏ", "item.minecraft.firework_star.purple": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.red": "Czyrwōnŏ", "item.minecraft.firework_star.shape": "Niyznany ksztołt", "item.minecraft.firework_star.shape.burst": "Rozproszynie", "item.minecraft.firework_star.shape.creeper": "Ksztołt: C<PERSON>per", "item.minecraft.firework_star.shape.large_ball": "Ksztołt: <PERSON><PERSON><PERSON><PERSON> kula", "item.minecraft.firework_star.shape.small_ball": "Ksztołt: <PERSON><PERSON><PERSON> kula", "item.minecraft.firework_star.shape.star": "Ksztołt: Gwiŏzda", "item.minecraft.firework_star.trail": "Sztreka", "item.minecraft.firework_star.white": "Biołŏ", "item.minecraft.firework_star.yellow": "Żōłtŏ", "item.minecraft.fishing_rod": "Wyndka", "item.minecraft.flint": "Flint", "item.minecraft.flint_and_steel": "Krzosadło", "item.minecraft.flow_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.flow_armor_trim_smithing_template.new": "Ôrnamynt — Przepływ", "item.minecraft.flow_banner_pattern": "Muster fany", "item.minecraft.flow_banner_pattern.desc": "Wir", "item.minecraft.flow_banner_pattern.new": "Muster fany — Wir", "item.minecraft.flow_pottery_sherd": "Szpliter ze wirym", "item.minecraft.flower_banner_pattern": "Muster fany", "item.minecraft.flower_banner_pattern.desc": "Kwiŏtek", "item.minecraft.flower_banner_pattern.new": "Muster fany — Kwiŏ<PERSON>k", "item.minecraft.flower_pot": "Ôtka", "item.minecraft.fox_spawn_egg": "Jajco z liszkōm", "item.minecraft.friend_pottery_shard": "Szkyrtek ze przŏciuchym", "item.minecraft.friend_pottery_sherd": "Szpliter ze przŏciuchym", "item.minecraft.frog_spawn_egg": "Jajco ze rapitołzōm", "item.minecraft.furnace_minecart": "Wagōn ze żeleźniŏkym", "item.minecraft.ghast_spawn_egg": "Jajco ze ghastym", "item.minecraft.ghast_tear": "Ghastowŏ płaczka", "item.minecraft.glass_bottle": "Flaszka", "item.minecraft.glistering_melon_slice": "Złōtawy kōnsek wasermelōna", "item.minecraft.globe_banner_pattern": "Muster fany", "item.minecraft.globe_banner_pattern.desc": "Ziymiokostka", "item.minecraft.globe_banner_pattern.new": "Muster fany — <PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_berries": "Świycōnce jagody", "item.minecraft.glow_ink_sac": "Bojtlik ze blyndtintōm", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_squid_spawn_egg": "Jajco ze świycōncōm tintynfiszōm", "item.minecraft.glowstone_dust": "Blyndŏkowy pulwer", "item.minecraft.goat_horn": "Cigowy rōg", "item.minecraft.goat_spawn_egg": "Jajco ze cigōm", "item.minecraft.gold_ingot": "Sztangelka złōta", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_apple": "Złōtŏ pōnka", "item.minecraft.golden_axe": "Złōtŏ cieślica", "item.minecraft.golden_boots": "<PERSON>ł<PERSON><PERSON> strzewiki", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON><PERSON><PERSON> markew", "item.minecraft.golden_chestplate": "Złō<PERSON> kyras", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "Złōty kopŏcz", "item.minecraft.golden_horse_armor": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ński pancer", "item.minecraft.golden_leggings": "Złōte galŏty", "item.minecraft.golden_pickaxe": "Złōtŏ haka", "item.minecraft.golden_shovel": "Zł<PERSON><PERSON> ryl", "item.minecraft.golden_sword": "Złōty miycz", "item.minecraft.gray_bundle": "<PERSON><PERSON>", "item.minecraft.gray_dye": "<PERSON><PERSON>", "item.minecraft.gray_harness": "<PERSON><PERSON>", "item.minecraft.green_bundle": "Zielōny miyszek", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON><PERSON>z<PERSON>f", "item.minecraft.green_harness": "Zielōny g<PERSON>zir", "item.minecraft.guardian_spawn_egg": "Jajco ze wachtyrzym", "item.minecraft.gunpowder": "Proch", "item.minecraft.guster_banner_pattern": "Muster fany", "item.minecraft.guster_banner_pattern.desc": "Wiytrzik", "item.minecraft.guster_banner_pattern.new": "<PERSON>er fany — <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_pottery_sherd": "Szpliter ze wiytrzykym", "item.minecraft.happy_ghast_spawn_egg": "Jajco ze uciesznym ghastym", "item.minecraft.harness": "Gyszir", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_pottery_shard": "Szpliter ze syrcym", "item.minecraft.heart_pottery_sherd": "Szpliter ze syrcym", "item.minecraft.heartbreak_pottery_shard": "Szpliter ze złōmanym syrcym", "item.minecraft.heartbreak_pottery_sherd": "Szpliter ze złōmanym syrcym", "item.minecraft.hoglin_spawn_egg": "Jajco ze hoglinym", "item.minecraft.honey_bottle": "Flaszka miodu", "item.minecraft.honeycomb": "Ślyzyk miodu", "item.minecraft.hopper_minecart": "Wagōn ze lyjŏkym", "item.minecraft.horse_spawn_egg": "Jajco ze kōniym", "item.minecraft.host_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.host_armor_trim_smithing_template.new": "Ôrnamynt — Gospodŏrz", "item.minecraft.howl_pottery_shard": "Szpliter ze wilkym", "item.minecraft.howl_pottery_sherd": "Szpliter ze wilkym", "item.minecraft.husk_spawn_egg": "Jajco ze suchlŏkym", "item.minecraft.ink_sac": "Bojtlik ze tintōm", "item.minecraft.iron_axe": "Żelaznŏ cieślica", "item.minecraft.iron_boots": "Żelazne strzewiki", "item.minecraft.iron_chestplate": "Żelazny kyras", "item.minecraft.iron_golem_spawn_egg": "Jajco ze żelaznym golymym", "item.minecraft.iron_helmet": "Żelazny helm", "item.minecraft.iron_hoe": "Żelazny kopŏcz", "item.minecraft.iron_horse_armor": "Żelazny kōński pancer", "item.minecraft.iron_ingot": "Sztangelka żelazła", "item.minecraft.iron_leggings": "Żelazne galŏty", "item.minecraft.iron_nugget": "Łupa <PERSON>", "item.minecraft.iron_pickaxe": "Żelaznŏ haka", "item.minecraft.iron_shovel": "Żelazny ryl", "item.minecraft.iron_sword": "Żelazny miycz", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "Dżōng<PERSON><PERSON> łōdka", "item.minecraft.jungle_chest_boat": "Dżōnglowŏ łōdka ze kastlōm", "item.minecraft.knowledge_book": "Encyklopedyjŏ", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON><PERSON><PERSON> lawy", "item.minecraft.lead": "<PERSON><PERSON><PERSON>", "item.minecraft.leather": "S<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jakla", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mycka", "item.minecraft.leather_horse_armor": "<PERSON>k<PERSON><PERSON><PERSON><PERSON> k<PERSON> pancer", "item.minecraft.leather_leggings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_blue_bundle": "Światłomodry miyszek", "item.minecraft.light_blue_dye": "Światłomodry farbsztof", "item.minecraft.light_blue_harness": "Światłomodry gyszir", "item.minecraft.light_gray_bundle": "Światłosiwy miyszek", "item.minecraft.light_gray_dye": "Światłosiwy farbsztof", "item.minecraft.light_gray_harness": "Światłosiwy gyszir", "item.minecraft.lime_bundle": "<PERSON><PERSON>nko<PERSON> miyszek", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON>zto<PERSON>", "item.minecraft.lime_harness": "Limōnkowy gyszir", "item.minecraft.lingering_potion": "Ôbłoczny wywar", "item.minecraft.lingering_potion.effect.awkward": "Ciyrpki ôbłoczny wywar", "item.minecraft.lingering_potion.effect.empty": "Niystwarzalny ôbłoczny wywar", "item.minecraft.lingering_potion.effect.fire_resistance": "Ôbłoczny wywar ôbstŏ<PERSON><PERSON> ôgnia", "item.minecraft.lingering_potion.effect.harming": "Ôbłoczny wywar krziwdy", "item.minecraft.lingering_potion.effect.healing": "Ôbłoczny wywar lyczyniŏ", "item.minecraft.lingering_potion.effect.infested": "Ôbłoczny wywar chrobajstwa", "item.minecraft.lingering_potion.effect.invisibility": "Ôbłoczny wywar <PERSON>", "item.minecraft.lingering_potion.effect.leaping": "Ôbłoczny wywar wysokigo skŏkaniŏ", "item.minecraft.lingering_potion.effect.levitation": "Ôbłoczny wywar lewi<PERSON>je", "item.minecraft.lingering_potion.effect.luck": "Ôbłoczny wywar szczyńściŏ", "item.minecraft.lingering_potion.effect.mundane": "Mgły ôbłoczny wywar", "item.minecraft.lingering_potion.effect.night_vision": "Ôbłoczny wywar widoczności we cimie", "item.minecraft.lingering_potion.effect.oozing": "Ôbłoczny wywar wy<PERSON>", "item.minecraft.lingering_potion.effect.poison": "Ôbłoczny wywar zagid<PERSON>", "item.minecraft.lingering_potion.effect.regeneration": "Ôbłoczny wywar regyneracyje", "item.minecraft.lingering_potion.effect.slow_falling": "Ôbłoczny wywar powolnego spadowaniŏ", "item.minecraft.lingering_potion.effect.slowness": "Ôbłoczny wywar uniyskorzyniŏ", "item.minecraft.lingering_potion.effect.strength": "Ôbłoczny wywar siyły", "item.minecraft.lingering_potion.effect.swiftness": "Ôbłoczny wywar gib<PERSON>ci", "item.minecraft.lingering_potion.effect.thick": "Gynsty ôbłoczny wywar", "item.minecraft.lingering_potion.effect.turtle_master": "Ôbłoczny wywar <PERSON><PERSON> majstra", "item.minecraft.lingering_potion.effect.water": "Ôbłocznŏ flaszka wody", "item.minecraft.lingering_potion.effect.water_breathing": "Ôbłoczny wywar dychaniŏ pod wodōm", "item.minecraft.lingering_potion.effect.weakness": "Ôbłoczny wywar lichoty", "item.minecraft.lingering_potion.effect.weaving": "Ôbłoczny wywar webera", "item.minecraft.lingering_potion.effect.wind_charged": "Ôbłoczny wywar naluftowaniŏ", "item.minecraft.llama_spawn_egg": "Jajco ze lamōm", "item.minecraft.lodestone_compass": "Magnety<PERSON><PERSON>", "item.minecraft.mace": "Buzdygōn", "item.minecraft.magenta_bundle": "Purpurowy miyszek", "item.minecraft.magenta_dye": "Purpurowy farbsztof", "item.minecraft.magenta_harness": "Purpurowy gyszir", "item.minecraft.magma_cream": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>m", "item.minecraft.magma_cube_spawn_egg": "Jajco ze magmŏkym", "item.minecraft.mangrove_boat": "Namorzinowŏ łōdka", "item.minecraft.mangrove_chest_boat": "Namorzinowŏ łōdka ze kastlōm", "item.minecraft.map": "Prōżnŏ karta", "item.minecraft.melon_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.melon_slice": "Kōnsek wasermelōna", "item.minecraft.milk_bucket": "<PERSON><PERSON><PERSON>", "item.minecraft.minecart": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "Szpliter ze hakōm", "item.minecraft.miner_pottery_sherd": "Szpliter ze hakōm", "item.minecraft.mojang_banner_pattern": "Muster fany", "item.minecraft.mojang_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.new": "<PERSON>er fany — <PERSON><PERSON>", "item.minecraft.mooshroom_spawn_egg": "Jajco ze mooshroomym", "item.minecraft.mourner_pottery_shard": "Szpliter ze wachmajstrym", "item.minecraft.mourner_pottery_sherd": "Szpliter ze wachmajstrym", "item.minecraft.mule_spawn_egg": "Jajco ze maultiyrym", "item.minecraft.mushroom_stew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11": "Dysketka", "item.minecraft.music_disc_11.desc": "C418 — 11", "item.minecraft.music_disc_13": "Dysketka", "item.minecraft.music_disc_13.desc": "C418 — 13", "item.minecraft.music_disc_5": "Dysketka", "item.minecraft.music_disc_5.desc": "<PERSON> — 5", "item.minecraft.music_disc_blocks": "Dysketka", "item.minecraft.music_disc_blocks.desc": "C418 — blocks", "item.minecraft.music_disc_cat": "Dysketka", "item.minecraft.music_disc_cat.desc": "C418 — cat", "item.minecraft.music_disc_chirp": "Dysketka", "item.minecraft.music_disc_chirp.desc": "C418 — chirp", "item.minecraft.music_disc_creator": "Dysketka", "item.minecraft.music_disc_creator.desc": "<PERSON> — C<PERSON>", "item.minecraft.music_disc_creator_music_box": "Dysketka", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> Creator (Lajerka)", "item.minecraft.music_disc_far": "Dysketka", "item.minecraft.music_disc_far.desc": "C418 — far", "item.minecraft.music_disc_lava_chicken": "Dysketka", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions — Lava Chicken", "item.minecraft.music_disc_mall": "Dysketka", "item.minecraft.music_disc_mall.desc": "C418 — mall", "item.minecraft.music_disc_mellohi": "Dysketka", "item.minecraft.music_disc_mellohi.desc": "C418 — mellohi", "item.minecraft.music_disc_otherside": "Dysketka", "item.minecraft.music_disc_otherside.desc": "<PERSON> — otherside", "item.minecraft.music_disc_pigstep": "Dysketka", "item.minecraft.music_disc_pigstep.desc": "<PERSON> Pigstep", "item.minecraft.music_disc_precipice": "Dysketka", "item.minecraft.music_disc_precipice.desc": "<PERSON> — Precipice", "item.minecraft.music_disc_relic": "Dysketka", "item.minecraft.music_disc_relic.desc": "<PERSON> <PERSON><PERSON>", "item.minecraft.music_disc_stal": "Dysketka", "item.minecraft.music_disc_stal.desc": "C418 — stal", "item.minecraft.music_disc_strad": "Dysketka", "item.minecraft.music_disc_strad.desc": "C418 — strad", "item.minecraft.music_disc_tears": "Muzycznŏ dysketka", "item.minecraft.music_disc_tears.desc": "<PERSON> — Tears", "item.minecraft.music_disc_wait": "Dysketka", "item.minecraft.music_disc_wait.desc": "C418 — wait", "item.minecraft.music_disc_ward": "Dysketka", "item.minecraft.music_disc_ward.desc": "C418 — ward", "item.minecraft.mutton": "Surowŏ szkopina", "item.minecraft.name_tag": "Znacznik", "item.minecraft.nautilus_shell": "Łodzikowŏ muszla", "item.minecraft.nether_brick": "Netherowŏ cegła", "item.minecraft.nether_star": "Netherowŏ gwiŏzda", "item.minecraft.nether_wart": "Netherowŏ kurzŏwka", "item.minecraft.netherite_axe": "Netherytowŏ cieślica", "item.minecraft.netherite_boots": "Netherytowe strzewiki", "item.minecraft.netherite_chestplate": "Netherytowy kyras", "item.minecraft.netherite_helmet": "Netherytowy helm", "item.minecraft.netherite_hoe": "Netherytowy kopŏcz", "item.minecraft.netherite_ingot": "Sztangelka netherytu", "item.minecraft.netherite_leggings": "Netherytowe galŏty", "item.minecraft.netherite_pickaxe": "Netherytowŏ haka", "item.minecraft.netherite_scrap": "Netherytowy szrōt", "item.minecraft.netherite_shovel": "Netherytowy ryl", "item.minecraft.netherite_sword": "Netherytowy miycz", "item.minecraft.netherite_upgrade_smithing_template": "Kowŏlski szablōn", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherytowe ulepszynie", "item.minecraft.oak_boat": "Dymbowŏ łōdka", "item.minecraft.oak_chest_boat": "Dymbowŏ łōdka ze kastlōm", "item.minecraft.ocelot_spawn_egg": "Jajco ze ôcelotym", "item.minecraft.ominous_bottle": "Lagramynckŏ flaszka", "item.minecraft.ominous_trial_key": "Lagramyncki klucz prōby", "item.minecraft.orange_bundle": "Ôranżowy miyszek", "item.minecraft.orange_dye": "Ôranżowy far<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.orange_harness": "Ôranżowy gyszir", "item.minecraft.painting": "Ôbrŏz", "item.minecraft.pale_oak_boat": "Bladodymbowŏ łōdka", "item.minecraft.pale_oak_chest_boat": "Bladodymbowŏ łōdka ze kastlōm", "item.minecraft.panda_spawn_egg": "Jajco ze pandōm", "item.minecraft.paper": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Jajco ze papagajym", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON><PERSON> mory", "item.minecraft.phantom_spawn_egg": "Jajco ze morōm", "item.minecraft.pig_spawn_egg": "Jajco ze świniōm", "item.minecraft.piglin_banner_pattern": "Muster fany", "item.minecraft.piglin_banner_pattern.desc": "Niurŏk", "item.minecraft.piglin_banner_pattern.new": "Muster fany — Niurŏk", "item.minecraft.piglin_brute_spawn_egg": "Jajco ze piglinym-doskwiyrnikym", "item.minecraft.piglin_spawn_egg": "Jajco ze piglinym", "item.minecraft.pillager_spawn_egg": "Jajco ze raubiyrzym", "item.minecraft.pink_bundle": "Rōżowy miyszek", "item.minecraft.pink_dye": "Rōżowy farbsztof", "item.minecraft.pink_harness": "Rōżowy gyszir", "item.minecraft.pitcher_plant": "Zbōnecznik", "item.minecraft.pitcher_pod": "<PERSON><PERSON><PERSON> zbōnecz<PERSON>a", "item.minecraft.plenty_pottery_shard": "Szpliter ze kastlōm", "item.minecraft.plenty_pottery_sherd": "Szpliter ze kastlōm", "item.minecraft.poisonous_potato": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.polar_bear_spawn_egg": "Jajco ze polarnym niydźwiydziym", "item.minecraft.popped_chorus_fruit": "Piyczka refrynice", "item.minecraft.porkchop": "Surowŏ wieprzowina", "item.minecraft.potato": "Kart<PERSON>fel", "item.minecraft.potion": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.awkward": "Ciyrpki wywar", "item.minecraft.potion.effect.empty": "Niystwar<PERSON><PERSON> wywar", "item.minecraft.potion.effect.fire_resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON> ô<PERSON>", "item.minecraft.potion.effect.harming": "<PERSON><PERSON><PERSON> krziwdy", "item.minecraft.potion.effect.healing": "Wywar lyczyniŏ", "item.minecraft.potion.effect.infested": "<PERSON><PERSON>war chrobajstwa", "item.minecraft.potion.effect.invisibility": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.leaping": "Wywar wysokigo skŏkaniŏ", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.luck": "Wywar szczyńściŏ", "item.minecraft.potion.effect.mundane": "Mdły wywar", "item.minecraft.potion.effect.night_vision": "Wywar widoczności we cimie", "item.minecraft.potion.effect.oozing": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.poison": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.regeneration": "<PERSON><PERSON><PERSON> regyner<PERSON>", "item.minecraft.potion.effect.slow_falling": "<PERSON><PERSON>war powolnego spadowaniŏ", "item.minecraft.potion.effect.slowness": "Wywar uniyskorzyniŏ", "item.minecraft.potion.effect.strength": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.swiftness": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.thick": "<PERSON><PERSON><PERSON> wywar", "item.minecraft.potion.effect.turtle_master": "<PERSON><PERSON><PERSON> ma<PERSON>", "item.minecraft.potion.effect.water": "Flaszka wody", "item.minecraft.potion.effect.water_breathing": "Wywar dychaniŏ pod wodōm", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON><PERSON> lichoty", "item.minecraft.potion.effect.weaving": "<PERSON><PERSON><PERSON> webera", "item.minecraft.potion.effect.wind_charged": "<PERSON><PERSON><PERSON>", "item.minecraft.pottery_shard_archer": "Szpliter ze łukym", "item.minecraft.pottery_shard_arms_up": "Szpliter ze zniysiōnymi rynkami", "item.minecraft.pottery_shard_prize": "Szpliter ze nadgrōdōm", "item.minecraft.pottery_shard_skull": "Szpliter ze szalōm", "item.minecraft.powder_snow_bucket": "Ajmer sypkigo śniega", "item.minecraft.prismarine_crystals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_shard": "Szpliter prizmarynu", "item.minecraft.prize_pottery_shard": "Szpliter ze nadgrōdōm", "item.minecraft.prize_pottery_sherd": "Szpliter ze nadgrōdōm", "item.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pufferfish_bucket": "Ajmer ze kugelfiszōm", "item.minecraft.pufferfish_spawn_egg": "Jajco ze kugelfiszōm", "item.minecraft.pumpkin_pie": "Kołŏcz ze banie", "item.minecraft.pumpkin_seeds": "Ziŏrna banie", "item.minecraft.purple_bundle": "Flidrowy miyszek", "item.minecraft.purple_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>zi<PERSON>", "item.minecraft.quartz": "Netherowy kwarc", "item.minecraft.rabbit": "Surowy mycŏk", "item.minecraft.rabbit_foot": "Szłapka mycŏka", "item.minecraft.rabbit_hide": "Mycŏkowŏ skōra", "item.minecraft.rabbit_spawn_egg": "Jajco ze mycŏkym", "item.minecraft.rabbit_stew": "Ajntop ze mycŏka", "item.minecraft.raiser_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.raiser_armor_trim_smithing_template.new": "Ôrnamynt — Formut", "item.minecraft.ravager_spawn_egg": "Jajco ze niszczŏrzym", "item.minecraft.raw_copper": "Surowŏ miydź", "item.minecraft.raw_gold": "Surowe złōto", "item.minecraft.raw_iron": "Surowe żelazło", "item.minecraft.recovery_compass": "Powrotny k<PERSON>", "item.minecraft.red_bundle": "Czyrwōny miyszek", "item.minecraft.red_dye": "Czyrwō<PERSON> farbsztof", "item.minecraft.red_harness": "Czyrwōny g<PERSON>zir", "item.minecraft.redstone": "Redstone'owy pulwer", "item.minecraft.resin_brick": "Harcow<PERSON> cegła", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON> harcu", "item.minecraft.rib_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.rib_armor_trim_smithing_template.new": "Ôrnamynt — Ziobro", "item.minecraft.rotten_flesh": "<PERSON><PERSON><PERSON> mi<PERSON>", "item.minecraft.saddle": "Zatel", "item.minecraft.salmon": "Surowy laks", "item.minecraft.salmon_bucket": "Ajmer ze laksym", "item.minecraft.salmon_spawn_egg": "Jajco ze laksym", "item.minecraft.scrape_pottery_sherd": "Szpliter ze cieślicōm", "item.minecraft.scute": "<PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.sentry_armor_trim_smithing_template.new": "Ôrnamynt — <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shaper_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.shaper_armor_trim_smithing_template.new": "Ôrnamynt — Kształtownik", "item.minecraft.sheaf_pottery_shard": "Szpliter ze pszynicōm", "item.minecraft.sheaf_pottery_sherd": "Szpliter ze pszynicōm", "item.minecraft.shears": "Szyry", "item.minecraft.sheep_spawn_egg": "Jajco ze ôwcōm", "item.minecraft.shelter_pottery_shard": "Szpliter ze agacym", "item.minecraft.shelter_pottery_sherd": "Szpliter ze agacym", "item.minecraft.shield": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "Czŏrnŏ tarcza", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON> tarcza", "item.minecraft.shield.brown": "Brōnŏtnŏ tarcza", "item.minecraft.shield.cyan": "Cyjanow<PERSON> tarcza", "item.minecraft.shield.gray": "Siwŏ tarcza", "item.minecraft.shield.green": "Zielōn<PERSON> tarcza", "item.minecraft.shield.light_blue": "Światłomodrŏ tarcza", "item.minecraft.shield.light_gray": "Światłosiwŏ tarcza", "item.minecraft.shield.lime": "Limōnkowŏ tarcza", "item.minecraft.shield.magenta": "Purpurowŏ tarcza", "item.minecraft.shield.orange": "Ôranżowŏ tarcza", "item.minecraft.shield.pink": "Rōżowŏ tarcza", "item.minecraft.shield.purple": "Flidrowŏ tarcza", "item.minecraft.shield.red": "Czyrwōnŏ tarcza", "item.minecraft.shield.white": "Biołŏ tarcza", "item.minecraft.shield.yellow": "Żōłtŏ tarcza", "item.minecraft.shulker_shell": "Shulkerowŏ szkorupa", "item.minecraft.shulker_spawn_egg": "Jajco ze shulkerym", "item.minecraft.sign": "Znak", "item.minecraft.silence_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.silence_armor_trim_smithing_template.new": "Ôrnamynt — Cisza", "item.minecraft.silverfish_spawn_egg": "Jajco ze rybikym", "item.minecraft.skeleton_horse_spawn_egg": "Jajco ze kōniym-szkeletym", "item.minecraft.skeleton_spawn_egg": "Jajco ze szkeletym", "item.minecraft.skull_banner_pattern": "Muster fany", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.skull_banner_pattern.new": "Muster fany — <PERSON><PERSON><PERSON><PERSON> szala", "item.minecraft.skull_pottery_shard": "Szpliter ze szalōm", "item.minecraft.skull_pottery_sherd": "Szpliter ze szalōm", "item.minecraft.slime_ball": "<PERSON><PERSON>", "item.minecraft.slime_spawn_egg": "Jajco ze szlōmym", "item.minecraft.smithing_template": "Kowŏlski szablōn", "item.minecraft.smithing_template.applies_to": "Nakłŏdŏ sie na:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Przidej sztangelkã abo krysztoł", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Przidej tajlã pancra", "item.minecraft.smithing_template.armor_trim.ingredients": "Sztangelki a krysztoły", "item.minecraft.smithing_template.ingredients": "Skłŏdniki:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Przidej sztangelkã netherytu", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Dyjamyntowy sprzynt", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "<PERSON><PERSON><PERSON><PERSON>, br<PERSON><PERSON> abo werkcojg", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Sztangelka netherytu", "item.minecraft.smithing_template.upgrade": "Ulepszynie:", "item.minecraft.sniffer_spawn_egg": "Jajco ze sznupŏczym", "item.minecraft.snort_pottery_shard": "Szpliter ze sznupŏczym", "item.minecraft.snort_pottery_sherd": "Szpliter ze sznupŏczym", "item.minecraft.snout_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.snout_armor_trim_smithing_template.new": "Ôrnamynt — Niurŏk", "item.minecraft.snow_golem_spawn_egg": "Jajco ze śniyżnym golymym", "item.minecraft.snowball": "Śniyżka", "item.minecraft.spectral_arrow": "Upiorn<PERSON> strzała", "item.minecraft.spider_eye": "<PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.spider_spawn_egg": "Jajco ze pajōnkym", "item.minecraft.spire_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.spire_armor_trim_smithing_template.new": "Ôrnamynt — Szpica", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON> wywar", "item.minecraft.splash_potion.effect.awkward": "Ciepany ciyrpki wywar", "item.minecraft.splash_potion.effect.empty": "<PERSON>ystwar<PERSON><PERSON> ciepany wywar", "item.minecraft.splash_potion.effect.fire_resistance": "Ciepany wywar ôbstōniŏ ôgnia", "item.minecraft.splash_potion.effect.harming": "Ciepany wywar krziwdy", "item.minecraft.splash_potion.effect.healing": "Ciepany wywar lyczyniŏ", "item.minecraft.splash_potion.effect.infested": "Ciepany wywar chrobajstwa", "item.minecraft.splash_potion.effect.invisibility": "Ciepany wywar niywidzialności", "item.minecraft.splash_potion.effect.leaping": "Ciepany wywar wysokigo skŏkaniŏ", "item.minecraft.splash_potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON> wywar le<PERSON>", "item.minecraft.splash_potion.effect.luck": "Ciepany wywar szczyńściŏ", "item.minecraft.splash_potion.effect.mundane": "Mgły ciepany wywar", "item.minecraft.splash_potion.effect.night_vision": "Ciepany wywar widoczności we cimie", "item.minecraft.splash_potion.effect.oozing": "<PERSON>ie<PERSON><PERSON> wywar w<PERSON>", "item.minecraft.splash_potion.effect.poison": "Ciepany wywar zagidzyniŏ", "item.minecraft.splash_potion.effect.regeneration": "<PERSON><PERSON><PERSON><PERSON> wywar regyneracyje", "item.minecraft.splash_potion.effect.slow_falling": "Ciepany wywar powolnego spadowaniŏ", "item.minecraft.splash_potion.effect.slowness": "Ciepany wywar uniyskorzyniŏ", "item.minecraft.splash_potion.effect.strength": "Ciepany wywar siyły", "item.minecraft.splash_potion.effect.swiftness": "Ciepany wywar gib<PERSON>", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON> ciepany wywar", "item.minecraft.splash_potion.effect.turtle_master": "Ciepany wywar żōłwigo majstra", "item.minecraft.splash_potion.effect.water": "Ciepanŏ flaszka wody", "item.minecraft.splash_potion.effect.water_breathing": "Ciepany wywar dychaniŏ pod wodōm", "item.minecraft.splash_potion.effect.weakness": "<PERSON>ie<PERSON><PERSON> wywar lichoty", "item.minecraft.splash_potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON> wywar webera", "item.minecraft.splash_potion.effect.wind_charged": "Ciepany wywar naluftowaniŏ", "item.minecraft.spruce_boat": "Świyrkowŏ łōdka", "item.minecraft.spruce_chest_boat": "Świyrkowŏ łōdka ze kastlōm", "item.minecraft.spyglass": "Ôptikruła", "item.minecraft.squid_spawn_egg": "Jajco ze tintynfiszōm", "item.minecraft.stick": "Kijŏk", "item.minecraft.stone_axe": "Kamiynnŏ cieślica", "item.minecraft.stone_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_pickaxe": "<PERSON><PERSON><PERSON><PERSON> haka", "item.minecraft.stone_shovel": "<PERSON><PERSON><PERSON><PERSON> ryl", "item.minecraft.stone_sword": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stray_spawn_egg": "Jajco ze pōniywiyrŏkym", "item.minecraft.strider_spawn_egg": "Jajco ze smykŏczym", "item.minecraft.string": "<PERSON><PERSON><PERSON>", "item.minecraft.sugar": "Cuker", "item.minecraft.suspicious_stew": "Podyjzdrzany ajntop", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON><PERSON> borōw<PERSON>", "item.minecraft.tadpole_bucket": "Ajmer ze gowŏczym", "item.minecraft.tadpole_spawn_egg": "Jajco ze gowŏczym", "item.minecraft.tide_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.tide_armor_trim_smithing_template.new": "Ôrnamynt — <PERSON><PERSON>", "item.minecraft.tipped_arrow": "Tōnknytŏ strzała", "item.minecraft.tipped_arrow.effect.awkward": "Tōnknytŏ strzała", "item.minecraft.tipped_arrow.effect.empty": "Niystwarzalnŏ tōnknytŏ strzała", "item.minecraft.tipped_arrow.effect.fire_resistance": "Strzała ôbstŏciŏ ôgnia", "item.minecraft.tipped_arrow.effect.harming": "Strzała krziwdy", "item.minecraft.tipped_arrow.effect.healing": "Strzała lyczyniŏ", "item.minecraft.tipped_arrow.effect.infested": "Strzała chrobajstwa", "item.minecraft.tipped_arrow.effect.invisibility": "Strzała niywidzialności", "item.minecraft.tipped_arrow.effect.leaping": "Strzała wysokigo skŏkaniŏ", "item.minecraft.tipped_arrow.effect.levitation": "Strz<PERSON><PERSON> lewitacyje", "item.minecraft.tipped_arrow.effect.luck": "Strzała szczyńściŏ", "item.minecraft.tipped_arrow.effect.mundane": "Tōnknytŏ strzała", "item.minecraft.tipped_arrow.effect.night_vision": "Strzała widoczności we cimie", "item.minecraft.tipped_arrow.effect.oozing": "Strzała wyciykaniŏ", "item.minecraft.tipped_arrow.effect.poison": "Strzała zagidzyniŏ", "item.minecraft.tipped_arrow.effect.regeneration": "Strzała regyneracyje", "item.minecraft.tipped_arrow.effect.slow_falling": "Strzała powolnego spadowaniŏ", "item.minecraft.tipped_arrow.effect.slowness": "Strzała uniyskorzyniŏ", "item.minecraft.tipped_arrow.effect.strength": "Strzała siyły", "item.minecraft.tipped_arrow.effect.swiftness": "Strzała gibkości", "item.minecraft.tipped_arrow.effect.thick": "Tōnknytŏ strzała", "item.minecraft.tipped_arrow.effect.turtle_master": "<PERSON>rz<PERSON><PERSON> żōłwigo majstra", "item.minecraft.tipped_arrow.effect.water": "Strzała ciŏplaniŏ", "item.minecraft.tipped_arrow.effect.water_breathing": "Strzała dychaniŏ pod wodōm", "item.minecraft.tipped_arrow.effect.weakness": "Strzała lichoty", "item.minecraft.tipped_arrow.effect.weaving": "Strzała webera", "item.minecraft.tipped_arrow.effect.wind_charged": "Strzała naluftowaniŏ", "item.minecraft.tnt_minecart": "Wagōn ze TNT", "item.minecraft.torchflower_seeds": "Ziŏrna faklicz<PERSON>", "item.minecraft.totem_of_undying": "<PERSON><PERSON><PERSON>", "item.minecraft.trader_llama_spawn_egg": "Jajco z handlyrzowōm lamōm", "item.minecraft.trial_key": "<PERSON><PERSON>cz prōby", "item.minecraft.trident": "Trzizōmb", "item.minecraft.tropical_fish": "Trōpikalŏ ryba", "item.minecraft.tropical_fish_bucket": "Ajmer ze trōpikalnōm rybōm", "item.minecraft.tropical_fish_spawn_egg": "Jajco ze trōpikalnōm rybōm", "item.minecraft.turtle_helmet": "Żōł<PERSON><PERSON> szkorupa", "item.minecraft.turtle_scute": "<PERSON><PERSON><PERSON><PERSON><PERSON> szupa", "item.minecraft.turtle_spawn_egg": "Jajco ze żōłwiym", "item.minecraft.vex_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.vex_armor_trim_smithing_template.new": "Ôrnamynt — Szerga", "item.minecraft.vex_spawn_egg": "Jajco ze szergōm", "item.minecraft.villager_spawn_egg": "Jajco ze wiyśniŏkym", "item.minecraft.vindicator_spawn_egg": "Jajco ze zymstliwcym", "item.minecraft.wandering_trader_spawn_egg": "Jajco z wandrownym handlyrzym", "item.minecraft.ward_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.ward_armor_trim_smithing_template.new": "Ôrnamynt — Wacha", "item.minecraft.warden_spawn_egg": "Jajco ze wachmajstrym", "item.minecraft.warped_fungus_on_a_stick": "Szagi grzib na kijŏku", "item.minecraft.water_bucket": "<PERSON><PERSON><PERSON> wody", "item.minecraft.wayfinder_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Ôrnamynt <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wheat": "Pszynica", "item.minecraft.wheat_seeds": "Ziŏrna pszynicy", "item.minecraft.white_bundle": "Bioły miyszek", "item.minecraft.white_dye": "Bioły farbsztof", "item.minecraft.white_harness": "Bioły gyszir", "item.minecraft.wild_armor_trim_smithing_template": "Kowŏlski szablōn", "item.minecraft.wild_armor_trim_smithing_template.new": "Ôrnamynt — Dzicz", "item.minecraft.wind_charge": "Wietrzny ladōnek", "item.minecraft.witch_spawn_egg": "Jajco ze heksōm", "item.minecraft.wither_skeleton_spawn_egg": "Jajco ze witherowym szkeletym", "item.minecraft.wither_spawn_egg": "Jajco ze witherym", "item.minecraft.wolf_armor": "<PERSON><PERSON><PERSON><PERSON> pancer", "item.minecraft.wolf_spawn_egg": "Jajco ze wilkym", "item.minecraft.wooden_axe": "Drzewniannŏ cieślica", "item.minecraft.wooden_hoe": "Drzewnianny kopŏcz", "item.minecraft.wooden_pickaxe": "Drzewniannŏ haka", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON> ryl", "item.minecraft.wooden_sword": "Drzewnianny miycz", "item.minecraft.writable_book": "Ksiōnżka ze fyjdrym", "item.minecraft.written_book": "Naszkryftano ksiōnżka", "item.minecraft.yellow_bundle": "Żōłty miyszek", "item.minecraft.yellow_dye": "Żōł<PERSON> farbsztof", "item.minecraft.yellow_harness": "Żōłty gyszir", "item.minecraft.zoglin_spawn_egg": "Jajco ze zoglinym", "item.minecraft.zombie_horse_spawn_egg": "Jajco ze kōniym-umarlŏkym", "item.minecraft.zombie_spawn_egg": "Jajco ze umarlŏkym", "item.minecraft.zombie_villager_spawn_egg": "Jajco ze wiyśniŏkym-umarlŏkym", "item.minecraft.zombified_piglin_spawn_egg": "Jajco ze piglinym-umarlŏkiym", "item.modifiers.any": "Przi wyposażyniu:", "item.modifiers.armor": "<PERSON><PERSON><PERSON>yn<PERSON>:", "item.modifiers.body": "Przi wyposażyniu:", "item.modifiers.chest": "<PERSON>j na bruście:", "item.modifiers.feet": "<PERSON><PERSON> na stopach:", "item.modifiers.hand": "<PERSON><PERSON><PERSON>:", "item.modifiers.head": "<PERSON><PERSON> na gowie:", "item.modifiers.legs": "<PERSON>j na nogach:", "item.modifiers.mainhand": "Kej we gōwnyj rynce:", "item.modifiers.offhand": "<PERSON>j we drugij r<PERSON>e:", "item.modifiers.saddle": "<PERSON><PERSON><PERSON>:", "item.nbt_tags": "NBT: %s etyket", "item.op_block_warning.line1": "Pozōr:", "item.op_block_warning.line2": "Użycie tyj rzeczy śmi dokludzić ku zregiyrowaniu kōmyndy", "item.op_block_warning.line3": "<PERSON><PERSON>, podwiela niy znŏsz akuratnego inhaltu!", "item.unbreakable": "Niyzniszczalne", "itemGroup.buildingBlocks": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.coloredBlocks": "Farbiste", "itemGroup.combat": "<PERSON><PERSON><PERSON>", "itemGroup.consumables": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.crafting": "Wytwŏrzanie", "itemGroup.foodAndDrink": "Pożywiynie a napoje", "itemGroup.functional": "Użytkowe", "itemGroup.hotbar": "<PERSON><PERSON><PERSON>", "itemGroup.ingredients": "Skłŏdniki", "itemGroup.inventory": "Inwyntŏrz", "itemGroup.natural": "Naturalne", "itemGroup.op": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.redstone": "Mechanizmy redstone", "itemGroup.search": "Szukej", "itemGroup.spawnEggs": "Jajca", "itemGroup.tools": "Werkcojgi a narychty", "item_modifier.unknown": "Niyznany rzeczowy modyfikatōr: %s", "jigsaw_block.final_state": "Przemiyni sie we:", "jigsaw_block.generate": "G<PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "Wyrōwnany", "jigsaw_block.joint.rollable": "Ôbrŏcalny", "jigsaw_block.joint_label": "Zorta łōncznika:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>nstruk<PERSON>", "jigsaw_block.levels": "Poziōmy: %s", "jigsaw_block.name": "Miano:", "jigsaw_block.placement_priority": "Priorytet wrażaniŏ:", "jigsaw_block.placement_priority.tooltip": "Kej tyn kōnstrukcyjny blok sie połōnczy ze elymyntym, bydzie poszukowoł połōnczyń na przestrzyństwie szyrszyj struktury po podanym sam porzōndku.\n\nKōnstrukcyjne bloki bydōm przetwŏrzane po tych ze nojwyższym priorytetym, a we przipŏdku, kej pŏra wertōw sōm egal, ôstanōm przetworzōne po porzōndku jejich wrażyniŏ.", "jigsaw_block.pool": "Zaczōntkowy bytrag:", "jigsaw_block.selection_priority": "Priorytet ôbioru:", "jigsaw_block.selection_priority.tooltip": "Kej tyn nadrzyndny elymynt bydzie poszukowoł połō<PERSON>zyń, poł<PERSON><PERSON><PERSON> sie ze docylowym elymyntym po podanym sam porzōndku.\n\nKōnstrukcyjne bloki bydōm przetwŏrzane po tych ze nojwyższym priorytetym, a we przipŏdku, kej pŏra wertōw bydōm egal, ôstanōm losowo przetworzōne.", "jigsaw_block.target": "Miano cylu:", "jukebox_song.minecraft.11": "C418 — 11", "jukebox_song.minecraft.13": "C418 — 13", "jukebox_song.minecraft.5": "<PERSON> — 5", "jukebox_song.minecraft.blocks": "C418 — blocks", "jukebox_song.minecraft.cat": "C418 — cat", "jukebox_song.minecraft.chirp": "C418 — chirp", "jukebox_song.minecraft.creator": "<PERSON> — C<PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> Creator (Lajerka)", "jukebox_song.minecraft.far": "C418 — far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions — Lava Chicken", "jukebox_song.minecraft.mall": "C418 — mall", "jukebox_song.minecraft.mellohi": "C418 — mellohi", "jukebox_song.minecraft.otherside": "<PERSON> — otherside", "jukebox_song.minecraft.pigstep": "<PERSON> Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> — Precipice", "jukebox_song.minecraft.relic": "<PERSON> <PERSON><PERSON>", "jukebox_song.minecraft.stal": "C418 — stal", "jukebox_song.minecraft.strad": "C418 — strad", "jukebox_song.minecraft.tears": "<PERSON> — Tears", "jukebox_song.minecraft.wait": "C418 — wait", "jukebox_song.minecraft.ward": "C418 — ward", "key.advancements": "Pokŏ<PERSON> postympy", "key.attack": "Napŏdej / niszcz", "key.back": "Idź ku zadku", "key.categories.creative": "<PERSON><PERSON><PERSON><PERSON><PERSON> tryb", "key.categories.gameplay": "Gra", "key.categories.inventory": "Inwyntŏrz", "key.categories.misc": "Roztōmańte", "key.categories.movement": "Poruszanie", "key.categories.multiplayer": "Mocperzōnowŏ gra", "key.categories.ui": "Interfejs gry", "key.chat": "Ôdewrzij czat", "key.command": "Wkludź k<PERSON>ndã", "key.drop": "Wyciep ôbranõ rzecz", "key.forward": "Idź ku przodku", "key.fullscreen": "Przeszaltruj połny ykrōn", "key.hotbar.1": "Pole 1", "key.hotbar.2": "Pole 2", "key.hotbar.3": "Pole 3", "key.hotbar.4": "Pole 4", "key.hotbar.5": "Pole 5", "key.hotbar.6": "Pole 6", "key.hotbar.7": "Pole 7", "key.hotbar.8": "Pole 8", "key.hotbar.9": "Pole 9", "key.inventory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>wyntŏrz", "key.jump": "Skŏkej", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "<PERSON><PERSON><PERSON>", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Numeryczne 0", "key.keyboard.keypad.1": "Numeryczne 1", "key.keyboard.keypad.2": "Numeryczne 2", "key.keyboard.keypad.3": "Numeryczne 3", "key.keyboard.keypad.4": "Numeryczne 4", "key.keyboard.keypad.5": "Numeryczne 5", "key.keyboard.keypad.6": "Numeryczne 6", "key.keyboard.keypad.7": "Numeryczne 7", "key.keyboard.keypad.8": "Numeryczne 8", "key.keyboard.keypad.9": "Numeryczne 9", "key.keyboard.keypad.add": "Numeryczne +", "key.keyboard.keypad.decimal": "Numerycznŏ .", "key.keyboard.keypad.divide": "Numeryczny /", "key.keyboard.keypad.enter": "Numeryczny Enter", "key.keyboard.keypad.equal": "Numeryczny =", "key.keyboard.keypad.multiply": "Numeryczny *", "key.keyboard.keypad.subtract": "Numeryczny -", "key.keyboard.left": "<PERSON><PERSON>", "key.keyboard.left.alt": "Lewy Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Lewy C<PERSON>l", "key.keyboard.left.shift": "Le<PERSON>", "key.keyboard.left.win": "Lewy Windows", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "Prawo", "key.keyboard.right.alt": "Prawy Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Prawy Ctrl", "key.keyboard.right.shift": "Prawy Shift", "key.keyboard.right.win": "Prawy Windows", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Spa<PERSON>j<PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Niyprzipisany", "key.keyboard.up": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.world.1": "Świŏt 1", "key.keyboard.world.2": "Świŏt 2", "key.left": "Idź na lewo", "key.loadToolbarActivator": "<PERSON><PERSON><PERSON><PERSON> pole", "key.mouse": "Knefel %1$s", "key.mouse.left": "<PERSON><PERSON> knefel", "key.mouse.middle": "Postrzedni knefel", "key.mouse.right": "<PERSON><PERSON><PERSON> knefel", "key.pickItem": "Ôbier blok", "key.playerlist": "Pokŏż zestawiynie grŏczy", "key.quickActions": "Gibke dziołania", "key.right": "Idź na prawo", "key.saveToolbarActivator": "<PERSON><PERSON><PERSON> pole", "key.screenshot": "<PERSON><PERSON><PERSON><PERSON>", "key.smoothCamera": "Przeszaltruj filmowõ kamerã", "key.sneak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e", "key.socialInteractions": "Pokŏż ykrōn społecznych interakcyji", "key.spectatorOutlines": "Podświyt<PERSON> (cuszauerōw)", "key.sprint": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.swapOffhand": "Przeciep ku drugij rynce", "key.togglePerspective": "Przeszal<PERSON><PERSON><PERSON>", "key.use": "Używej / stŏwiej", "known_server_link.announcements": "Anōnse", "known_server_link.community": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.community_guidelines": "Nakŏzania Społeczności", "known_server_link.feedback": "Ôpinije", "known_server_link.forums": "Fora", "known_server_link.news": "Nowiny", "known_server_link.report_bug": "Zgłoś serwerowy feler", "known_server_link.status": "St<PERSON><PERSON>", "known_server_link.support": "<PERSON><PERSON><PERSON>", "known_server_link.website": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lanServer.otherPlayers": "Nasztalowania lŏ inkszych grŏczy", "lanServer.port": "Port", "lanServer.port.invalid": "Niynŏleżny port.\nÔstŏw tekstowe pole prōżne abo podej nōmerã pōmiyndzy 1024 a 65535.", "lanServer.port.invalid.new": "Niynŏleżny port.\nÔstŏw tekstowe pole prōżne abo podej nōmerã pōmiyndzy %s a %s.", "lanServer.port.unavailable": "Port niydostympny.\nÔstŏw tekstowe pole prōżne abo podej inkszõ liczbã pōmiyndzy 1024 a 65535.", "lanServer.port.unavailable.new": "Port niydostympny.\nÔstŏw tekstowe pole prōżne abo podej inkszõ liczbã pōmiyndzy %s a %s.", "lanServer.scanning": "Szukanie ger we lokalnym necu…", "lanServer.start": "Załōncz świŏt we LAN", "lanServer.title": "Świŏt we LAN", "language.code": "szl_PL", "language.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "language.region": "Gōrny Ślōnsk", "lectern.take_book": "<PERSON>ź ksiōnżkã", "loading.progress": "%s%%", "mco.account.privacy.info": "Przeczytej wiyncyj ô Mojang a ô prŏwidłach chrōniyniŏ p<PERSON>watności", "mco.account.privacy.info.button": "Przeczytej wiyncyj ô GDPR", "mco.account.privacy.information": "Mojang wkludzŏ dokumyntny porzōndek we cylu <PERSON> bajtli a jejich p<PERSON>, we tym upozōrnianiŏ Children's Online Privacy Protection Act (COPPA) a General Data Protection Regulation (GDPR).\n\nPrzed dostaniym dostympu do kōnta Realms, możno być kōnieczne erbnycie zgody ôjcōw.", "mco.account.privacyinfo": "Mojang wkludz<PERSON> dokumyntny porzōndek we cylu ôchrōny bajtli a jejich p<PERSON>, we tym upozōrnianiŏ Children's Online Privacy Protection Act (COPPA) a General Data Protection Regulation (GDPR).\n\nPrzed dostaniym dostympu do kōnta Realms, możno być kōnieczne erbnycie zgody ôjcōw.\n\n<PERSON><PERSON><PERSON> mŏsz starsze kōnto Minecraft (logujesz sie po pōmocy miana używŏcza), by<PERSON><PERSON><PERSON> musioł je przeniyś na serwis Mo<PERSON>g, coby dostać dostymp ku usłudze Realms.", "mco.account.update": "Zaktualizi<PERSON><PERSON><PERSON> k<PERSON>", "mco.activity.noactivity": "Bez żŏdnyj aktywności bez łōńske %s dni", "mco.activity.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> grŏczy", "mco.backup.button.download": "<PERSON><PERSON><PERSON>", "mco.backup.button.reset": "Zresetuj <PERSON>", "mco.backup.button.restore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.button.upload": "Posł<PERSON>", "mco.backup.changes.tooltip": "Zmiany", "mco.backup.entry": "Ibrycznŏ kopijŏ (%s)", "mco.backup.entry.description": "Ôpis", "mco.backup.entry.enabledPack": "Ôbrane pakety", "mco.backup.entry.gameDifficulty": "Ciynżkość gry", "mco.backup.entry.gameMode": "<PERSON><PERSON> gry", "mco.backup.entry.gameServerVersion": "<PERSON><PERSON><PERSON><PERSON><PERSON> serwera", "mco.backup.entry.name": "Miano", "mco.backup.entry.seed": "Ziŏrno", "mco.backup.entry.templateName": "<PERSON><PERSON>", "mco.backup.entry.undefined": "Niyôkryś<PERSON>ōn<PERSON> zmiana", "mco.backup.entry.uploaded": "Posłano", "mco.backup.entry.worldType": "<PERSON>orta <PERSON>", "mco.backup.generate.world": "St<PERSON><PERSON><PERSON>", "mco.backup.info.title": "Zmiany ôd poślednij ibrycznyj kopije", "mco.backup.narration": "Ibrycznŏ kopijŏ %s", "mco.backup.nobackups": "Tyn Realm ôbecnie niy mŏ żŏdnych ibrycznych kopiji.", "mco.backup.restoring": "Prziwracanie Realmu…", "mco.backup.unknown": "NIYZNANŎ", "mco.brokenworld.download": "Pobier", "mco.brokenworld.downloaded": "Pobrano", "mco.brokenworld.message.line1": "Zresetuj abo ôbier inkszy świŏt.", "mco.brokenworld.message.line2": "Mŏsz tyż możeb<PERSON> pobraniŏ świata, co<PERSON> na nim zagrać we jednoperzōnowym trybie.", "mco.brokenworld.minigame.title": "Ta minigra już niy ma spōmogany", "mco.brokenworld.nonowner.error": "<PERSON><PERSON><PERSON><PERSON>, aż włŏściciel Realmu zresetuje świŏt", "mco.brokenworld.nonowner.title": "Ś<PERSON><PERSON><PERSON> je forbaj", "mco.brokenworld.play": "<PERSON><PERSON><PERSON>", "mco.brokenworld.reset": "Zresetuj", "mco.brokenworld.title": "Twōj ôbecny świŏt już niyma spōmogany", "mco.client.incompatible.msg.line1": "Tw<PERSON>j k<PERSON>j<PERSON>t je spōrny ze Realms.", "mco.client.incompatible.msg.line2": "Proszymy użyć nojnowszyj wersyje Minecrafta.", "mco.client.incompatible.msg.line3": "Realms niy fōngujōm we <PERSON>zotach.", "mco.client.incompatible.title": "Sp<PERSON>rny klijynt!", "mco.client.outdated.stable.version": "Twoja wers<PERSON><PERSON> kli<PERSON> (%s) je spōrnŏ ze Realms.\n\nProszymy użyć nojnowszyj wersyje Minecrafta.", "mco.client.unsupported.snapshot.version": "<PERSON>ja we<PERSON><PERSON><PERSON> k<PERSON> (%s) je spōrnŏ ze Realms.\n\nRealms niy sōm dostympne we tym snapszocie.", "mco.compatibility.downgrade": "Ôbniż wersyjõ", "mco.compatibility.downgrade.description": "Tyn świ<PERSON>t bōł skorzij ôdewiyrany na wersyji %s, a terŏzki grŏsz na wersyji %s. Ôbniżynie wersyje gry może spowodować jego uszkodzynie — niy m<PERSON><PERSON>, i<PERSON><PERSON>tar<PERSON>, ani iże bydzie nŏleżnie fōngowoł.\n\nIbrycznŏ kopijŏ świŏta ôstanie zachowanŏ we „World Backups”. Proszymy prziwrōcić swōj świ<PERSON>t, eźli bydzie takŏ potrzeba.", "mco.compatibility.incompatible.popup.title": "Spōrnŏ wersyjŏ", "mco.compatibility.incompatible.releaseType.popup.message": "<PERSON><PERSON><PERSON><PERSON>, co ku niymu prōbujesz wlyź, je niyzgodliwy ze wersyjōm, co jij używŏsz.", "mco.compatibility.incompatible.series.popup.message": "Tyn świŏt bōł skorzij ôdewiyrany na wersyji %s, a terŏzki grŏsz na wersyji %s.\n\nTe wersyje gry sōm ze sobōm spōrne. Coby zagrać na tyj wersyji, bydzie potrzebny nowy świŏt.", "mco.compatibility.unverifiable.message": "Niy idzie zweryficiyrować wersyje świŏta, na keryj poślednio grano. Eźli wersyjŏ świŏta ôstanie dźwignytŏ abo ôbniżōnŏ, ibrycznŏ kopijŏ świŏta sama ôstanie stworzōnŏ a zachowanŏ we „World Backups”.", "mco.compatibility.unverifiable.title": "Zgodliwość niy ku sprŏwdzyniu", "mco.compatibility.upgrade": "Zaktualiziyruj", "mco.compatibility.upgrade.description": "Tyn świŏt bōł skorzij ôdewiyrany na wersyji %s, a terŏzki grŏsz na wersyji %s.\n\nIbrycznŏ kopijŏ świŏta ôstanie zachowanŏ we „World Backups”.\n\n<PERSON><PERSON><PERSON> by<PERSON><PERSON>, proszymy prziwrōcić swōj świŏt po pōmocy istej kopije.", "mco.compatibility.upgrade.friend.description": "Tyn świŏt bōł skorzij ôdewiyrany na wersyji %s, a terŏzki grŏsz na wersyji %s.\n\nIbrycznŏ kopijŏ świŏta ôstanie zachowanŏ we „World Backups”.\n\nEźli bydzie ta<PERSON>rz<PERSON>, włŏściciel Realmu bydzie mōg prziwrōcić świŏt po pōmocy istej kopije.", "mco.compatibility.upgrade.title": "Na isto ch<PERSON>z z<PERSON>ali<PERSON>ować tyn świŏt?", "mco.configure.current.minigame": "Terŏźnŏ", "mco.configure.world.activityfeed.disabled": "<PERSON><PERSON> <PERSON>d gr<PERSON><PERSON>a ôstoł tymczasowo wyłōnczōny", "mco.configure.world.backup": "Ibryczne kopije świŏta", "mco.configure.world.buttons.activity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> grŏcza", "mco.configure.world.buttons.close": "Zawrzij <PERSON>", "mco.configure.world.buttons.delete": "Usuń", "mco.configure.world.buttons.done": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.edit": "Nasztalowania", "mco.configure.world.buttons.invite": "Pozwej grŏcza", "mco.configure.world.buttons.moreoptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.newworld": "Nowy świŏt", "mco.configure.world.buttons.open": "Ôdewrzij Realm", "mco.configure.world.buttons.options": "Nasztalowania świŏta", "mco.configure.world.buttons.players": "Grŏcze", "mco.configure.world.buttons.region_preference": "Ôbier regiōn…", "mco.configure.world.buttons.resetworld": "Zresetuj <PERSON>", "mco.configure.world.buttons.save": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.settings": "Nasztalowania", "mco.configure.world.buttons.subscription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.switchminigame": "Przeszal<PERSON><PERSON>j minigrã", "mco.configure.world.close.question.line1": "Mogesz na chwilã zatrzimać swōj Realm, co przebrodzi graniu na czas wkludzaniŏ zmian. Ôdewrzij Realm na zaś kedy skōńczysz.\n\nTo dziołanie niy mŏ wpływu na twōj abōnamynt Realms.", "mco.configure.world.close.question.line2": "Na isto ch<PERSON>z przelyź dalij?", "mco.configure.world.close.question.title": "Potrzebujesz wkludzić zmiany bez zakłōcyń?", "mco.configure.world.closing": "Zawi<PERSON><PERSON>…", "mco.configure.world.commandBlocks": "<PERSON><PERSON><PERSON>", "mco.configure.world.delete.button": "Usuń Realm", "mco.configure.world.delete.question.line1": "Twōj Realm ôstanie usunyty na zŏwdy", "mco.configure.world.delete.question.line2": "Na isto ch<PERSON>z przelyź dalij?", "mco.configure.world.description": "Ôpis Realmu", "mco.configure.world.edit.slot.name": "Miano świ<PERSON>", "mco.configure.world.edit.subscreen.adventuremap": "<PERSON>jla nasztalowań je wyłōnczōnŏ skirz tego, iże twōj ôbecny świŏt je przigoda", "mco.configure.world.edit.subscreen.experience": "<PERSON>jla nasztal<PERSON>ń je wyłōnczōnŏ skirz tego, iże twōj ôbecny świŏt je świŏt wrażyń", "mco.configure.world.edit.subscreen.inspiration": "<PERSON><PERSON><PERSON> nasztal<PERSON> je wyłōnczōnŏ skirz tego, iże twōj ôbecny świŏt je yno inszpiracyjŏ", "mco.configure.world.forceGameMode": "<PERSON><PERSON><PERSON><PERSON> tryb gry", "mco.configure.world.invite.narration": "Mŏsz %s nowych zaproszyń", "mco.configure.world.invite.profile.name": "Miano", "mco.configure.world.invited": "Pozwany", "mco.configure.world.invited.number": "Zaproszyni (%s)", "mco.configure.world.invites.normal.tooltip": "Ajnfachowy używŏcz", "mco.configure.world.invites.ops.tooltip": "Ôperatōr", "mco.configure.world.invites.remove.tooltip": "Pociep", "mco.configure.world.leave.question.line1": "Eźli wyleziesz ze tego Realmu, już niy byd<PERSON>sz <PERSON>mioł na niego wlyź, niż niy ôstaniesz zaproszōny na zaś", "mco.configure.world.leave.question.line2": "Na isto ch<PERSON>z przelyź dalij?", "mco.configure.world.loading": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.location": "Położynie", "mco.configure.world.minigame": "Terŏźny: %s", "mco.configure.world.name": "<PERSON><PERSON>", "mco.configure.world.opening": "Ôdewiyranie Realmu…", "mco.configure.world.players.error": "<PERSON>y ma żŏdnego używŏcza ô takim mianie", "mco.configure.world.players.inviting": "Zaprŏszanie grŏcza…", "mco.configure.world.players.title": "Grŏcze", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Preferowany regiōn", "mco.configure.world.region_preference.title": "Ôbiōr preferowanego regiōnu", "mco.configure.world.reset.question.line1": "Twōj Realm ôstanie ôdtworzōny a ôbecny świŏt sie straci", "mco.configure.world.reset.question.line2": "Na isto ch<PERSON>z przelyź dalij?", "mco.configure.world.resourcepack.question": "Serwer wymŏgŏ niysztandardowego paketu zasobōw\n\nChcesz go pobrać a zagrać?", "mco.configure.world.resourcepack.question.line1": "Tyn Realm wymŏgŏ niysztandardowego paketu zasobōw", "mco.configure.world.resourcepack.question.line2": "Chcesz go pobrać a zagrać?", "mco.configure.world.restore.download.question.line1": "Świŏt ôstanie pobrany a wkludzōny ku twojich jednoperzōnowych świŏtach.", "mco.configure.world.restore.download.question.line2": "Na isto ch<PERSON>z przelyź dalij?", "mco.configure.world.restore.question.line1": "Twōj świŏt ôstanie prziwrōcōny ku stanowi ze dnia %s (%s)", "mco.configure.world.restore.question.line2": "Na isto ch<PERSON>z przelyź dalij?", "mco.configure.world.settings.expired": "<PERSON><PERSON><PERSON> nasztalowań przedŏwniōnego Realmu", "mco.configure.world.settings.title": "Nasztalowania", "mco.configure.world.slot": "Świŏt %s", "mco.configure.world.slot.empty": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "Realm ôstanie przeszaltrowany na inkszy świŏt", "mco.configure.world.slot.switch.question.line2": "Na isto ch<PERSON>z przelyź dalij?", "mco.configure.world.slot.tooltip": "Przeszaltruj na świŏt", "mco.configure.world.slot.tooltip.active": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot.tooltip.minigame": "Przeszaltruj na minigrã", "mco.configure.world.spawnAnimals": "Swołuj zwiyrzyńcia", "mco.configure.world.spawnMonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.spawnNPCs": "Swołuj NPC", "mco.configure.world.spawnProtection": "Ôchrōna przi pōnkcie ôdrodzyniŏ", "mco.configure.world.spawn_toggle.message": "Wyłōnczynie tego nasztalowaniŏ usunie wszycke ôbecne byty tyj zorty", "mco.configure.world.spawn_toggle.message.npc": "Wyłōnczynie tego nasztalowaniŏ usunie wszycke ôbecne byty tyj zorty, jak choby wiy<PERSON>", "mco.configure.world.spawn_toggle.title": "Pozōr!", "mco.configure.world.status": "St<PERSON><PERSON>", "mco.configure.world.subscription.day": "dziyń", "mco.configure.world.subscription.days": "dni", "mco.configure.world.subscription.expired": "Forbaj", "mco.configure.world.subscription.extend": "<PERSON><PERSON><PERSON><PERSON><PERSON> abōnamynt", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON><PERSON> jak jedyn <PERSON>", "mco.configure.world.subscription.month": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.months": "mies.", "mco.configure.world.subscription.recurring.daysleft": "Samoôdnŏwianie za", "mco.configure.world.subscription.recurring.info": "<PERSON><PERSON><PERSON>, kere by<PERSON><PERSON><PERSON> ch<PERSON>ł wkludzić ku swojimu abōnamyntowi Realms, take jak przidŏwanie czasu abo wyłōnczanie cyklicznych rachōnkōw, byd<PERSON><PERSON> wkludzōne dopiyro przi nŏstympnyj zŏpłacie.", "mco.configure.world.subscription.remaining.days": "%1$s dni", "mco.configure.world.subscription.remaining.months": "%1$s miesiōnce", "mco.configure.world.subscription.remaining.months.days": "%1$s miesiōnce, %2$s dni", "mco.configure.world.subscription.start": "Data nap<PERSON>znyciŏ", "mco.configure.world.subscription.tab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.timeleft": "Ôstoły czas", "mco.configure.world.subscription.title": "Informacyje ô abōnamyncie", "mco.configure.world.subscription.unknown": "Niyznane", "mco.configure.world.switch.slot": "St<PERSON><PERSON><PERSON>", "mco.configure.world.switch.slot.subtitle": "<PERSON><PERSON> je prō<PERSON>, <PERSON><PERSON><PERSON> jako by<PERSON>ł go stwo<PERSON>", "mco.configure.world.title": "Naszteluj Realm:", "mco.configure.world.uninvite.player": "Na isto chcesz pociepać zaproszynie „%s”?", "mco.configure.world.uninvite.question": "Na isto ch<PERSON>z pociepać zaproszynie", "mco.configure.worlds.title": "Świŏty", "mco.connect.authorizing": "Logowanie…", "mco.connect.connecting": "Łōnczynie ze Realmym…", "mco.connect.failed": "<PERSON><PERSON> podarziło sie połōnczyć ze Realmym", "mco.connect.region": "Regiōn serwera: %s", "mco.connect.success": "<PERSON><PERSON><PERSON>", "mco.create.world": "Stwōrz", "mco.create.world.error": "Musisz wklud<PERSON><PERSON> miano!", "mco.create.world.failed": "Niy szło stworzić świŏta!", "mco.create.world.reset.title": "Tworzynie świŏta…", "mco.create.world.skip": "Pōmi<PERSON>", "mco.create.world.subtitle": "Warōnkowo mogecie <PERSON>, kere<PERSON> by <PERSON><PERSON><PERSON><PERSON> za swōj <PERSON>", "mco.create.world.wait": "<PERSON><PERSON><PERSON><PERSON>…", "mco.download.cancelled": "Pociepano pobieranie", "mco.download.confirmation.line1": "<PERSON><PERSON><PERSON><PERSON>, kery <PERSON><PERSON>, je wielgszy jak %s", "mco.download.confirmation.line2": "<PERSON><PERSON> niy by<PERSON><PERSON><PERSON> p<PERSON> tego świ<PERSON>, coby go użyć za swōj Realm", "mco.download.confirmation.oversized": "<PERSON><PERSON><PERSON><PERSON>, kery m<PERSON><PERSON>, mŏ srogszŏ wielgość niż %s\n\n<PERSON><PERSON> niy byd<PERSON>sz śmioł posłać tego świ<PERSON>ta, coby go użyć za swōj Realm", "mco.download.done": "Pobiyranie je fertich", "mco.download.downloading": "Pobiyranie", "mco.download.extracting": "Wyôsobnianie…", "mco.download.failed": "Pobieranie sie niy podarziło", "mco.download.percent": "%s %%", "mco.download.preparing": "Rychtowan<PERSON>", "mco.download.resourcePack.fail": "<PERSON>y podarziło sie pobrać paketu zasobōw!", "mco.download.speed": "(%s/z)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Pobiyranie nojnowszego świŏta", "mco.error.invalid.session.message": "Sprōbuj na zaś sztartnyć grã", "mco.error.invalid.session.title": "Se<PERSON>j<PERSON> wygasła", "mco.errorMessage.6001": "Przedŏwniōny klijynt", "mco.errorMessage.6002": "Warōnki używaniŏ niy sōm przijmnyte", "mco.errorMessage.6003": "Ôsiōngnyto limit pobiyraniŏ", "mco.errorMessage.6004": "Ôsiōngnyto limit posyłaniŏ", "mco.errorMessage.6005": "Zaszperowany świŏt", "mco.errorMessage.6006": "Ś<PERSON><PERSON><PERSON> je forbaj", "mco.errorMessage.6007": "Grŏcz je we zazbyt wielōch Realmach", "mco.errorMessage.6008": "<PERSON><PERSON><PERSON>", "mco.errorMessage.6009": "<PERSON><PERSON><PERSON>", "mco.errorMessage.connectionFailure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sprōbuj na zaś troszkã niskorzij.", "mco.errorMessage.generic": "Doszło ku felerowi:", "mco.errorMessage.initialize.failed": "<PERSON><PERSON> podarziło sie zainicjować Realmu", "mco.errorMessage.noDetails": "<PERSON><PERSON> <PERSON><PERSON>liwości feleru", "mco.errorMessage.realmsService": "Doszło ku felerowi (%s):", "mco.errorMessage.realmsService.configurationError": "Feler przi edytowaniu nasztalowań świŏta", "mco.errorMessage.realmsService.connectivity": "Niy idzie połōnczyć ze Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "<PERSON><PERSON> podarziło sie spr<PERSON><PERSON><PERSON><PERSON><PERSON> zgodliwyj wersyje, erb<PERSON><PERSON> ôdpedzynie: %s", "mco.errorMessage.retry": "Pow<PERSON><PERSON>", "mco.errorMessage.serviceBusy": "Realms sōm zajmnyte na tã chwilã.\nProszymy sprōbować sie połōnczyć na zaś za porã minut.", "mco.gui.button": "Knefel", "mco.gui.ok": "<PERSON><PERSON>", "mco.info": "Informacyje!", "mco.invited.player.narration": "Zaproszōno grŏcza %s", "mco.invites.button.accept": "Przijmij", "mco.invites.button.reject": "Pociep", "mco.invites.nopending": "Żŏdnych czkajōncych!", "mco.invites.pending": "Nowe zaproszynia!", "mco.invites.title": "Czkajōnce zaproszynia", "mco.minigame.world.changeButton": "Ôbier inkszõ minigrã", "mco.minigame.world.info.line1": "Ôbecny świŏt ôstanie zastōmp<PERSON>ōny ôd minigry!", "mco.minigame.world.info.line2": "Śmisz wrōcić na swōj pierwotny świŏt bez tracyniŏ niczego.", "mco.minigame.world.noSelection": "Ôbier minigrã", "mco.minigame.world.restore": "Kōńczynie minigry…", "mco.minigame.world.restore.question.line1": "Minigra sie skōńczy a twōj Realm ôstanie prziwrōcōny.", "mco.minigame.world.restore.question.line2": "Na isto ch<PERSON>z przelyź dalij?", "mco.minigame.world.selected": "Ôbranŏ minigra:", "mco.minigame.world.slot.screen.title": "Przeszaltrowywanie świŏta…", "mco.minigame.world.startButton": "<PERSON>rz<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.minigame.world.starting.screen.title": "Sztartowanie minigry…", "mco.minigame.world.stopButton": "Zakōńcz minigrã", "mco.minigame.world.switch.new": "Ôbrać inkszõ minigrã?", "mco.minigame.world.switch.title": "Przeszal<PERSON><PERSON>j minigrã", "mco.minigame.world.title": "Przeszaltruj Realm na minigrã", "mco.news": "Nowiny Realms", "mco.notification.dismiss": "Ôdciep", "mco.notification.transferSubscription.buttonText": "Przeniyś terŏz", "mco.notification.transferSubscription.message": "Abōnamynty Java Realms sōm przenoszōne ku sklepu Microsoft Store. <PERSON>y przizwōl, coby twōj abōnamynt sie przedŏwniōł!\nPrzeniyś go terŏz a zdobydź fraj 30 dni Realms.\nWlyź na swōj profil na zajcie minecraft.net, coby przeniyś swōj abōnamynt.", "mco.notification.visitUrl.buttonText.default": "Ôdewrzij link", "mco.notification.visitUrl.message.default": "Proszymy badnyć pōniższy link", "mco.onlinePlayers": "Aktywni grŏcze", "mco.play.button.realm.closed": "<PERSON> je zawrzity", "mco.question": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "P<PERSON><PERSON><PERSON>", "mco.reset.world.experience": "Wrażynia", "mco.reset.world.generate": "Nowy świŏt", "mco.reset.world.inspiration": "Inszpiracyjŏ", "mco.reset.world.resetting.screen.title": "Resetowanie świŏta…", "mco.reset.world.seed": "<PERSON><PERSON><PERSON><PERSON> (ôpcyjōnalnie)", "mco.reset.world.template": "Szablōny świŏtōw", "mco.reset.world.title": "Zresetuj <PERSON>", "mco.reset.world.upload": "Posł<PERSON>", "mco.reset.world.warning": "Ôbecny świŏt ôstanie na zŏwdy usunyty ze Realmu", "mco.selectServer.buy": "<PERSON><PERSON>!", "mco.selectServer.close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.closed": "Realm zawrzity", "mco.selectServer.closeserver": "Zawrzij realm", "mco.selectServer.configure": "Nasztalowania", "mco.selectServer.configureRealm": "Nasztalowania", "mco.selectServer.create": "Stwōrz Realm", "mco.selectServer.create.subtitle": "Ôbier, kerego świŏta użyć za Realm", "mco.selectServer.expired": "<PERSON> je forbaj", "mco.selectServer.expiredList": "Twōj abōnamynt je forbaj", "mco.selectServer.expiredRenew": "Ôdnōw", "mco.selectServer.expiredSubscribe": "Zaabōniyr<PERSON>j", "mco.selectServer.expiredTrial": "<PERSON><PERSON> p<PERSON>ba je forbaj", "mco.selectServer.expires.day": "<PERSON><PERSON><PERSON> forbaj za jedyn dzi<PERSON>ń", "mco.selectServer.expires.days": "<PERSON><PERSON>zie forbaj za %s dni", "mco.selectServer.expires.soon": "H<PERSON> by<PERSON><PERSON> forbaj", "mco.selectServer.leave": "W<PERSON><PERSON>ź ze Realmu", "mco.selectServer.loading": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "mco.selectServer.mapOnlySupportedForVersion": "Ta karta niy mŏ sparciŏ we %s", "mco.selectServer.minigame": "Minigra:", "mco.selectServer.minigameName": "Minigra: %s", "mco.selectServer.minigameNotSupportedInVersion": "Niy śmisz grać we tã minigrã na wersyji %s", "mco.selectServer.noRealms": "Wygl<PERSON><PERSON><PERSON> na to, iże niy mŏsz żŏdnego Realmu. Przidej Realm, coby zagrać społym ze kamratami.", "mco.selectServer.note": "Spōminka:", "mco.selectServer.open": "Realm ôdewrzity", "mco.selectServer.openserver": "Ôdewrzij Realm", "mco.selectServer.play": "<PERSON><PERSON><PERSON>", "mco.selectServer.popup": "Realms je ajn<PERSON><PERSON>, bezpiyczny spusōb na szpas onlajn aji ze dziesiyńciōma kamratami ôrŏz. Spōmŏgŏ moc miniger a niyôbyczajnych świŏtōw! Yno posiedziciel Realmu musi zapłacić.", "mco.selectServer.purchase": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.trial": "Wyprōbuj!", "mco.selectServer.uninitialized": "<PERSON><PERSON><PERSON>, coby s<PERSON><PERSON><PERSON><PERSON> swōj włŏsny Realm!", "mco.snapshot.createSnapshotPopup.text": "Za mōmynt stwo<PERSON><PERSON>z bezpłatny snapszotowy Realm, kery ôstanie połōnczōny ze twojim płatnym abōnamyntym Realms. Realm bydzie dostympny podwiela abōnamynt dalij bydzie aktywny. Niy wpłynie to na twōj płatny Realm.", "mco.snapshot.createSnapshotPopup.title": "<PERSON><PERSON><PERSON><PERSON> snapszotowy Realm?", "mco.snapshot.creating": "<PERSON><PERSON><PERSON><PERSON>…", "mco.snapshot.description": "Sparowano ze „%s”", "mco.snapshot.friendsRealm.downgrade": "Musisz użyć wersyje %s, coby do<PERSON><PERSON><PERSON><PERSON> ku tymu Realmowi", "mco.snapshot.friendsRealm.upgrade": "Niż zagrŏsz na tym snapszocie, %s bydzie musioł zaktualiziyrować wersyjõ swojigo Realmu", "mco.snapshot.paired": "<PERSON><PERSON> je połōnczōny ze Realmym „%s”", "mco.snapshot.parent.tooltip": "Proszymy użyć nojnowszyj wersyje Minecrafta, coby zagrać na tym Realmie", "mco.snapshot.start": "Sztartnij darmowy snapszotowy Realm", "mco.snapshot.subscription.info": "To je snapszotowy Realm, kery je połōnczōny ze abōnamyntym ôd Realmu „%s”. Bydzie ôn dostympny podwiela bydzie aktywny gōwny Realm.", "mco.snapshot.tooltip": "Snapszotowe Realmy przizwŏlajōm ci ôbadać prziszłe wersyje Minecrafta, kere śmiōm zawiyrać nowości a inksze zmiany.\n\nNormalne świŏty Realms durch sōm dostympne we sztabilnyj wersyje gry.", "mco.snapshotRealmsPopup.message": "Ôd wersyje 23w41a, Realms terŏzki sōm dostympne we snapszotach. <PERSON><PERSON> m<PERSON>sz abōnamynt Realms, ôk<PERSON>m ajnfachowego Realmu lŏ sztandardowyj wersyje Java, tyż ś<PERSON>z zał<PERSON>ż<PERSON>ć włŏsny Realm lŏ snapszotowyj wersyje gratis!", "mco.snapshotRealmsPopup.title": "Realms terŏzki sōm dostympne we snapszotach", "mco.snapshotRealmsPopup.urlText": "Przewiyd<PERSON> sie wiyncyj", "mco.template.button.publisher": "Wydŏwca", "mco.template.button.select": "Ôbier", "mco.template.button.trailer": "Zŏpodanie", "mco.template.default.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.info.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.name": "Szablōn", "mco.template.select.failure": "Niy podarziło sie erbnyć spiski inhaltu ôd tyj kategoryje.\nProszymy sprŏwdzić necowe połōnczynie abo sprōbować na zaś troszkã niyskorzij.", "mco.template.select.narrate.authors": "Autorzi: %s", "mco.template.select.narrate.version": "wersyjŏ %s", "mco.template.select.none": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> sie, iże ta kategoryj<PERSON> je prōżnŏ.\nProszymy sprŏwdzić na zaś ni<PERSON>korzij, coby erb<PERSON> nowy inhalt ôd tyj kategoryje, abo <PERSON> je twōrca,\n%s.", "mco.template.select.none.linkTitle": "rozwŏż posłanie czegoś swojigo", "mco.template.title": "Szablōny świŏta", "mco.template.title.minigame": "Minigry", "mco.template.trailer.tooltip": "Zŏpodanie karty", "mco.terms.buttons.agree": "Uważōm", "mco.terms.buttons.disagree": "<PERSON><PERSON>", "mco.terms.sentence.1": "Uważōm prŏwidła", "mco.terms.sentence.2": "Minecraft Realms", "mco.terms.title": "Prŏwidła Realms", "mco.time.daysAgo": "%1$s dni tymu", "mco.time.hoursAgo": "%1$s godzin tymu", "mco.time.minutesAgo": "%1$s minut tymu", "mco.time.now": "ter<PERSON>zki", "mco.time.secondsAgo": "%1$s zykōnd temu", "mco.trial.message.line1": "<PERSON><PERSON><PERSON> kupić swōj włŏsny Realm?", "mco.trial.message.line2": "<PERSON><PERSON><PERSON>, coby erb<PERSON><PERSON> wiyncyj informacyji!", "mco.upload.button.name": "Posłej", "mco.upload.cancelled": "Pociepano posyłanie", "mco.upload.close.failure": "<PERSON><PERSON> <PERSON><PERSON>, proszymy sprōbować na zaś niyskorzij", "mco.upload.done": "Posyłanie je fertich", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Posyłanie sie niy podarziło! (%s)", "mco.upload.failed.too_big.description": "Ôbrany świŏt je za wielgi. Maksymalnŏ przizwolōnŏ wielgość je %s.", "mco.upload.failed.too_big.title": "Świŏt je za srogi", "mco.upload.hardcore": "Niy możno posyłać ôbmierzłych świŏtōw!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Rychtowanie <PERSON>…", "mco.upload.select.world.none": "Niy znŏjdzōno żŏdnego jednoperzōnowego świŏta!", "mco.upload.select.world.subtitle": "Ôbier jednoperzōnowy świŏt ku przesłaniu", "mco.upload.select.world.title": "Posł<PERSON>", "mco.upload.size.failure.line1": "Świŏt „%s” je za srogi!", "mco.upload.size.failure.line2": "Mŏ %s. <PERSON><PERSON><PERSON> przizwolōnŏ wielgość je %s.", "mco.upload.uploading": "Posyłanie świŏta „%s”…", "mco.upload.verifying": "Sprŏwdzanie świŏta", "mco.version": "wersyjŏ: %s", "mco.warning": "Pozōr!", "mco.worldSlot.minigame": "Minigra", "menu.custom_options": "Niysztandardowe nasztalowania…", "menu.custom_options.title": "Niysztandardowe nasztalowania", "menu.custom_options.tooltip": "Uwŏga: Niysztandardowe nasztalowania sōm urynczane ôd serwerōw abo inhaltu ôd trz<PERSON><PERSON> ôs<PERSON>b.\nDŏwej sie baczynie!", "menu.custom_screen_info.button_narration": "To je niysztandardowy ykrōn. Przewiydz sie wiyncyj.", "menu.custom_screen_info.contents": "Te fōnkcyje sōm anbitowane ôd serwerōw a kart, kere niy sōm wł<PERSON><PERSON><PERSON>ć ôd Mojang Studios ani Microsoft, a tyż niy sōm ôbsługowane ani nadzorowane ôd istych firm.\n\nDŏwej sie baczynie! Zŏwdy dŏwej pozōr przi sprŏwdzaniu linkōw a nigdy niy ôddŏwej swojich ôsobowych danych, cuzamyn ze danymi logowaniŏ.\n\nEźli tyn ykrōn ci niymożliwiŏ granie, mogesz tyż rozłōnczyć sie ze serwerym po pōmocy pōniższego knefla.", "menu.custom_screen_info.disconnect": "Niysztandardowy ykrōn ôstał ôdciepany", "menu.custom_screen_info.title": "Uwŏga ô niysztandardowych ykranach", "menu.custom_screen_info.tooltip": "To je ni<PERSON>z<PERSON><PERSON><PERSON>wy ykrōn. <PERSON><PERSON><PERSON><PERSON>, coby sie przew<PERSON><PERSON> wiyncyj.", "menu.disconnect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.feedback": "Ôpinije…", "menu.feedback.title": "Ôpinije", "menu.game": "Meni gry", "menu.modded": " (zmodyfikowanŏ)", "menu.multiplayer": "Mocperzōnowŏ gra", "menu.online": "Minecraft Realms", "menu.options": "Nasztalowania…", "menu.paused": "<PERSON>ra <PERSON>", "menu.playdemo": "Grej na świecie demo", "menu.playerReporting": "Zgłoś grŏcza", "menu.preparingSpawn": "Rychtowanie zōny ôdrodzyniŏ: %s%%", "menu.quick_actions": "G<PERSON>ke dzioła<PERSON>…", "menu.quick_actions.title": "Gibke dziołania", "menu.quit": "Wylyź ze gry", "menu.reportBugs": "<PERSON><PERSON><PERSON><PERSON><PERSON> feler", "menu.resetdemo": "Zresetuj <PERSON> demo", "menu.returnToGame": "Nazŏd ku grze", "menu.returnToMenu": "Zachowej a wylyź ku titlowymu ykrōnowi", "menu.savingChunks": "Zachowywanie c<PERSON>ōw", "menu.savingLevel": "Zachowywanie świŏta", "menu.sendFeedback": "<PERSON><PERSON><PERSON><PERSON>", "menu.server_links": "Serwerowe linki…", "menu.server_links.title": "Serwerowe linki", "menu.shareToLan": "Ôdewrzij na LAN", "menu.singleplayer": "Jednoperzōnowŏ gra", "menu.working": "Ônaczynie…", "merchant.deprecated": "Wiyśniŏki uzupołniajōm foranty po dwa razy na dziyń.", "merchant.level.1": "<PERSON><PERSON><PERSON>", "merchant.level.2": "Szkolŏrz", "merchant.level.3": "Pōmager", "merchant.level.4": "Fachmōn", "merchant.level.5": "<PERSON><PERSON>", "merchant.title": "%s — %s", "merchant.trades": "Ôferty", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Naciś %1$s, coby wys<PERSON>", "multiplayer.applyingPack": "Pobiyra<PERSON> pake<PERSON> z<PERSON>w", "multiplayer.confirm_command.parse_errors": "Prōbujesz zregiyrować niyznanõ abo felernõ kōmyndã.\nNa isto ch<PERSON>z to zrobić?\nKōmynda: %s", "multiplayer.confirm_command.permissions_required": "Prōbujesz zregiyrować kōmyndã, kerŏ wymŏgŏ dźwignytych uprŏwniyń.\nTo śmi źle wpłynyć na grã.\nNa isto chcesz zregiyrować tã kōmyndã?\nKōmynda: %s", "multiplayer.confirm_command.title": "Potwiyrdź zregiyrowanie kōmyndy", "multiplayer.disconnect.authservers_down": "Przepraszōmy! Serwery uwiyrzitelnianiŏ niy fōngujōm we tyj chwili. Sprōbuj zaś niyskorzij.", "multiplayer.disconnect.bad_chat_index": "Znŏjdzō<PERSON> w<PERSON> ôd serwer<PERSON>, k<PERSON><PERSON> przeminytŏ abo je inaks<PERSON>j uplacowanõ", "multiplayer.disconnect.banned": "Mŏsz bana na tym serwerze", "multiplayer.disconnect.banned.expiration": "\nTwōj ban wygaśnie %s", "multiplayer.disconnect.banned.reason": "Mŏsz bana na tym serwerze.\nPrziczyna: %s", "multiplayer.disconnect.banned_ip.expiration": "\nTwōj ban wygaśnie %s", "multiplayer.disconnect.banned_ip.reason": "Twoja adresa IP je bloknytŏ na tym serwerze.\nPrziczyna: %s", "multiplayer.disconnect.chat_validation_failed": "<PERSON><PERSON> podarziło sie zweryficiyrować czatowyj wiadō<PERSON>ci", "multiplayer.disconnect.duplicate_login": "Zalogowano ze inkszego placu", "multiplayer.disconnect.expired_public_key": "Klucz publicznego profilu je forbaj. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> m<PERSON>sz zegrany systymowy czas a sprōbuj na zaś sztartnyć swojã grã.", "multiplayer.disconnect.flying": "Lŏtanie je wyłōnczōne na tym serwerze", "multiplayer.disconnect.generic": "Rozłōnczō<PERSON>", "multiplayer.disconnect.idling": "Za dugo nic eś niy robiōł!", "multiplayer.disconnect.illegal_characters": "Niyprzizwolōne znaki na czacie", "multiplayer.disconnect.incompatible": "Spōrny klijynt! Proszymy użyć %s", "multiplayer.disconnect.invalid_entity_attacked": "Sprōbowołeś napaś niynŏleżny byt", "multiplayer.disconnect.invalid_packet": "<PERSON><PERSON> posłoł felerny paket", "multiplayer.disconnect.invalid_player_data": "<PERSON><PERSON><PERSON> dane <PERSON>", "multiplayer.disconnect.invalid_player_movement": "Erbnyto felerny paket ruchu grŏcza", "multiplayer.disconnect.invalid_public_key_signature": "Felerny podpis klucza publicznego profilu.\nSprōbuj sztartnyć na zaś grã.", "multiplayer.disconnect.invalid_public_key_signature.new": "Felerny podpis klucza publicznego profilu.\nSprōbuj sztartnyć na zaś grã.", "multiplayer.disconnect.invalid_vehicle_movement": "<PERSON>rb<PERSON><PERSON> felerny paket ruchu pojazdu", "multiplayer.disconnect.ip_banned": "Twoja adresa IP je bloknytŏ na tym serwerze", "multiplayer.disconnect.kicked": "<PERSON><PERSON><PERSON><PERSON><PERSON> ôd <PERSON>", "multiplayer.disconnect.missing_tags": "Erbnyto niypołny zestŏw etyket ôd serwera.\nProszymy zgŏdać sie ze ôperatorym serwera.", "multiplayer.disconnect.name_taken": "To miano już je zajmnyte", "multiplayer.disconnect.not_whitelisted": "Niy ma cie na biołyj spisce na tym serwerze!", "multiplayer.disconnect.out_of_order_chat": "Erbny<PERSON> paket czatu poza porzōndkym. Zmiyniōł sie u ciebie systymowy czas?", "multiplayer.disconnect.outdated_client": "Spōrny klijynt! Proszymy użyć %s", "multiplayer.disconnect.outdated_server": "Spōrny klijynt! Proszymy użyć %s", "multiplayer.disconnect.server_full": "Serwer je połny!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON> z<PERSON>", "multiplayer.disconnect.slow_login": "Logowanie twało za dugo", "multiplayer.disconnect.too_many_pending_chats": "Za moc niyprzetworzōnych czatowych wiadōmości", "multiplayer.disconnect.transfers_disabled": "Serwer niy przijmuje przeniesiyń", "multiplayer.disconnect.unexpected_query_response": "Niyspodziywane niysztandardowe dane klij<PERSON>ta", "multiplayer.disconnect.unsigned_chat": "Erb<PERSON><PERSON> paket czatu bez podpisu abo ze felernym podpisym.", "multiplayer.disconnect.unverified_username": "<PERSON>y podarziło sie sprŏwdzić miana używŏcza!", "multiplayer.downloadingStats": "Ôdebiyranie sztatistik…", "multiplayer.downloadingTerrain": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>…", "multiplayer.lan.server_found": "Znŏjdzōno nowy serwer: %s", "multiplayer.message_not_delivered": "<PERSON><PERSON> <PERSON><PERSON> w<PERSON>, sprŏwdź register serwera: %s", "multiplayer.player.joined": "Grŏcz %s dołōnczōł ku grze", "multiplayer.player.joined.renamed": "Grŏcz %s (dŏwnij znany jako %s) dołōnczōł ku grze", "multiplayer.player.left": "Grŏcz %s ôpuściōł grã", "multiplayer.player.list.hp": "%s PŻ", "multiplayer.player.list.narration": "Aktywnych grŏczy: %s", "multiplayer.requiredTexturePrompt.disconnect": "Serwer wymŏgŏ niysztandardowego paketu zasobōw", "multiplayer.requiredTexturePrompt.line1": "Tyn serwer wymŏgŏ niysztandardowego paketu zasobōw.", "multiplayer.requiredTexturePrompt.line2": "<PERSON><PERSON><PERSON>dciepiesz tyn niysztandardowy paket zasobōw, ôstaniesz rozłōnczōny ze serwerym.", "multiplayer.socialInteractions.not_available": "Społeczne interakcyje sōm dostympne yno we mocperzōnowych świŏtach", "multiplayer.status.and_more": "…a jeszcze %s…", "multiplayer.status.cancelled": "Pociepano", "multiplayer.status.cannot_connect": "Niy idzie połōnczyć ze serwerym", "multiplayer.status.cannot_resolve": "<PERSON><PERSON> <PERSON> spoznać miana tego hosta", "multiplayer.status.finished": "<PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Spōrnŏ wersyjŏ!", "multiplayer.status.motd.narration": "Kōmunikat dnia: %s", "multiplayer.status.no_connection": "(brak połōnczyniŏ)", "multiplayer.status.old": "Stary", "multiplayer.status.online": "Aktywny", "multiplayer.status.ping": "%s mz", "multiplayer.status.ping.narration": "Ôpōźniynie: %s milizykōnd", "multiplayer.status.pinging": "Sprŏwdzanie połōnczyniŏ…", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "Aktywnych grŏczy: %s ze %s", "multiplayer.status.quitting": "Wyłażynie", "multiplayer.status.request_handled": "Żōndanie ô sztand ôstało ôbsłużōne", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>n", "multiplayer.status.version.narration": "Wersyjŏ serwera: %s", "multiplayer.stopSleeping": "Stōń ze łōżka", "multiplayer.texturePrompt.failure.line1": "<PERSON><PERSON> podarziło sie zastosować serwerowego paketu zasobōw", "multiplayer.texturePrompt.failure.line2": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kere wymŏgajōm niysztandardowych zasobōw śmiōm niy fōngować nŏleżnie", "multiplayer.texturePrompt.line1": "<PERSON>n serwer polecŏ używanie niysztandardowego paketu zas<PERSON>w.", "multiplayer.texturePrompt.line2": "Chcesz go autōmagicznie pobrać a zainsztalować?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nSerwerowŏ wiadōmość:\n%s", "multiplayer.title": "Mocperzōnowŏ gra", "multiplayer.unsecureserver.toast": "Wiadōmości posyłane na tym serwerze mogōm być zmiyniōne a mogōm niy ôdpadać swojigo pierwotnego inhaltu", "multiplayer.unsecureserver.toast.title": "<PERSON><PERSON> <PERSON><PERSON> czatowych wiadōmości", "multiplayerWarning.check": "<PERSON>y pokazuj już tego ykranu", "multiplayerWarning.header": "Pozōr: zewnōntrznŏ gra onlajn", "multiplayerWarning.message": "Pozōr: <PERSON><PERSON>perzōnowŏ gra je anbitowanŏ ôd serwer<PERSON>w ôd trzecich ôsōb. Te serwery niy sōm wł<PERSON><PERSON><PERSON> ôd Mojang Studios ani Microsoft, a niy sōm ôbsługowane ani nadzorowane ôd istych firm. <PERSON><PERSON>i grani<PERSON> on<PERSON>, naraż<PERSON>sz sie na niysprŏwdzane wiadōmości a inksze zorty inhaltu tworzōnego ôd używŏczy, kery śmi niy być pasowny lŏ wszyckich.", "music.game.a_familiar_room": "<PERSON> — A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> — An Ordinary Day", "music.game.ancestry": "<PERSON> — Ancestry", "music.game.below_and_above": "<PERSON> — Below and Above", "music.game.broken_clocks": "<PERSON> — Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 <PERSON> <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> — Comforting Memories", "music.game.creative.aria_math": "C418 — Aria Math", "music.game.creative.biome_fest": "C418 — Biome Fest", "music.game.creative.blind_spots": "C418 — Blind Spots", "music.game.creative.dreiton": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 <PERSON> <PERSON><PERSON>", "music.game.creative.taswell": "C418 — Taswell", "music.game.crescent_dunes": "<PERSON> — Crescent Dunes", "music.game.danny": "C418 <PERSON> <PERSON>", "music.game.deeper": "<PERSON> — <PERSON>er", "music.game.dry_hands": "C418 — Dry Hands", "music.game.echo_in_the_wind": "<PERSON> — Echo in the Wind", "music.game.eld_unknown": "<PERSON> — <PERSON><PERSON>", "music.game.end.alpha": "C418 — Alpha", "music.game.end.boss": "C418 <PERSON> <PERSON>", "music.game.end.the_end": "C418 — The End", "music.game.endless": "<PERSON> — <PERSON><PERSON>", "music.game.featherfall": "<PERSON> — Featherfall", "music.game.fireflies": "<PERSON> Fireflies", "music.game.floating_dream": "<PERSON><PERSON> — Floating Dream", "music.game.haggstrom": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> — Infinite Amethyst", "music.game.key": "C418 — <PERSON>", "music.game.komorebi": "<PERSON><PERSON> komorebi", "music.game.left_to_bloom": "<PERSON> — Left to Bloom", "music.game.lilypad": "<PERSON> — Lilypad", "music.game.living_mice": "C418 — Living Mice", "music.game.mice_on_venus": "C418 — <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 — Minecraft", "music.game.nether.ballad_of_the_cats": "C418 — Ballad of the Cats", "music.game.nether.concrete_halls": "C418 — Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 — <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> — <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> — So Below", "music.game.nether.warmth": "C418 — Warmth", "music.game.one_more_day": "<PERSON> — One More Day", "music.game.os_piano": "<PERSON> — O's Piano", "music.game.oxygene": "C418 — Oxygène", "music.game.pokopoko": "<PERSON><PERSON> poko<PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> — Puzzlebox", "music.game.stand_tall": "<PERSON> — <PERSON> Tail", "music.game.subwoofer_lullaby": "C418 — Subwoofer Lullaby", "music.game.swamp.aerie": "<PERSON> <PERSON><PERSON>", "music.game.swamp.firebugs": "<PERSON> — Firebugs", "music.game.swamp.labyrinthine": "<PERSON> — Labyrinthine", "music.game.sweden": "C418 — Sweden", "music.game.watcher": "<PERSON> — Watcher", "music.game.water.axolotl": "C418 — Axolotl", "music.game.water.dragon_fish": "C418 — Dragon Fish", "music.game.water.shuniji": "C418 — Shuni<PERSON>", "music.game.wending": "<PERSON> — Wending", "music.game.wet_hands": "C418 — <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON> ya<PERSON><PERSON>", "music.menu.beginning_2": "C418 — Beginning 2", "music.menu.floating_trees": "C418 — Floating Trees", "music.menu.moog_city_2": "C418 — Moog City 2", "music.menu.mutation": "C418 — Mutation", "narration.button": "Knefel: %s", "narration.button.usage.focused": "<PERSON><PERSON><PERSON>, co<PERSON> a<PERSON><PERSON><PERSON><PERSON>", "narration.button.usage.hovered": "<PERSON><PERSON><PERSON> lewy knefel <PERSON>, coby a<PERSON><PERSON><PERSON><PERSON>", "narration.checkbox": "Pole ôbioru: %s", "narration.checkbox.usage.focused": "<PERSON><PERSON><PERSON>, co<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "narration.checkbox.usage.hovered": "<PERSON><PERSON><PERSON> lewy knefel <PERSON>, co<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "narration.component_list.usage": "<PERSON><PERSON><PERSON>, coby przely<PERSON> ku nŏstympnymu <PERSON>wi", "narration.cycle_button.usage.focused": "<PERSON><PERSON><PERSON>, co<PERSON> <PERSON><PERSON> na: %s", "narration.cycle_button.usage.hovered": "<PERSON><PERSON><PERSON> lewy knefel <PERSON>, co<PERSON> <PERSON><PERSON> na: %s", "narration.edit_box": "Tekstowe pole: %s", "narration.item": "Rzecz: %s", "narration.recipe": "Recept na: %s", "narration.recipe.usage": "<PERSON><PERSON><PERSON> lewy knefel <PERSON>, co<PERSON>", "narration.recipe.usage.more": "<PERSON><PERSON><PERSON> prawy knefel my<PERSON>, coby p<PERSON><PERSON> wi<PERSON>yj receptōw", "narration.selection.usage": "Naciś fajlik we gōrã abo we dōł, coby prz<PERSON><PERSON><PERSON> na inkszy wpis", "narration.slider.usage.focused": "<PERSON><PERSON><PERSON> fajlik na lewo abo na prawo, co<PERSON> <PERSON><PERSON><PERSON><PERSON> wert", "narration.slider.usage.hovered": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, coby <PERSON><PERSON><PERSON><PERSON> wert", "narration.suggestion": "Ôbrano sugestyjõ nr. %s ze %s: %s", "narration.suggestion.tooltip": "Ôbrano sugestyjõ nr. %s ze %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "<PERSON><PERSON><PERSON>, coby prz<PERSON> ku nŏstympnyj sugestyji", "narration.suggestion.usage.cycle.hidable": "<PERSON><PERSON><PERSON>, coby prz<PERSON> ku nŏstympnyj sugestyji, a<PERSON> <PERSON><PERSON>, coby wyly<PERSON>", "narration.suggestion.usage.fill.fixed": "<PERSON><PERSON><PERSON>, co<PERSON> u<PERSON> sugestyje", "narration.suggestion.usage.fill.hidable": "<PERSON><PERSON><PERSON>, coby u<PERSON><PERSON><PERSON> sugestyje abo <PERSON>, coby wyly<PERSON>", "narration.tab_navigation.usage": "<PERSON><PERSON><PERSON> C<PERSON>l a <PERSON>, co<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ŏ<PERSON>", "narrator.button.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock": "Szpera ciynżkości", "narrator.button.difficulty_lock.locked": "Zaszperowanŏ", "narrator.button.difficulty_lock.unlocked": "Ôdszperowanŏ", "narrator.button.language": "Gŏdka", "narrator.controls.bound": "Przipisano knefel na %s", "narrator.controls.reset": "Zresetuj knefel: %s", "narrator.controls.unbound": "Knefel %s niyma przipisany", "narrator.joining": "Dołōnczanie", "narrator.loading": "Ladowanie: %s", "narrator.loading.done": "<PERSON><PERSON><PERSON>", "narrator.position.list": "Ôbrano %s rzōnd listy ze %s", "narrator.position.object_list": "Ôbrano %s elymynt rzyndu ze %s", "narrator.position.screen": "Ôbrano %s elymynt ykranu ze %s", "narrator.position.tab": "Ôbrano zŏgibkã %s ze %s", "narrator.ready_to_play": "<PERSON><PERSON><PERSON> ku graniu", "narrator.screen.title": "Gōwny ykrōn", "narrator.screen.usage": "<PERSON>ż<PERSON>j kursora abo kn<PERSON>, co<PERSON> <PERSON><PERSON> el<PERSON>", "narrator.select": "Ôbrano: %s", "narrator.select.world": "Ôbrano: %s, pośledniŏ gra: %s, %s, %s, wersyjŏ: %s", "narrator.select.world_info": "Ôbrano: %s, pośledniŏ gra: %s, %s", "narrator.toast.disabled": "Ercyjler wyłōnczōny", "narrator.toast.enabled": "Ercyjler załōnczōny", "optimizeWorld.confirm.description": "Twōj świŏt ôstanie poddany ôptymalizacyji, kerŏ przetworzi wszycke dane świŏta na nojnowszy format. To może zajmnyć moc czasu we znŏlyżności ôd twojigo świŏta. Po zakōńczyniu świŏt już niy bydzie kōmpatybilny ze starszymi wersyjami gry, ale bydzie warcyj fōngowoł. Na isto chcesz kōntynuować?", "optimizeWorld.confirm.proceed": "Stwōrz ibrycznõ kopijõ a zôptymalizuj", "optimizeWorld.confirm.title": "Zôptymali<PERSON><PERSON>", "optimizeWorld.info.converted": "Przetworzōnych czankōw: %s", "optimizeWorld.info.skipped": "Pōminytych czankōw: %s", "optimizeWorld.info.total": "Czankōw społym: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>…", "optimizeWorld.stage.failed": "<PERSON>y podarziło sie! :(", "optimizeWorld.stage.finished": "K<PERSON><PERSON><PERSON><PERSON><PERSON>…", "optimizeWorld.stage.finished.chunks": "Kōńczynie przetwŏrzaniŏ wszyckich czankōw…", "optimizeWorld.stage.finished.entities": "Kōńczynie przetwŏrzaniŏ wszyckich bytōw…", "optimizeWorld.stage.finished.poi": "Kōńczynie przetwŏrzaniŏ wszyckich pōnktōw uwŏgi…", "optimizeWorld.stage.upgrading": "Przetwŏrzanie wszyckich czankōw…", "optimizeWorld.stage.upgrading.chunks": "Przetwŏrzanie wszyckich czankōw…", "optimizeWorld.stage.upgrading.entities": "Przetwŏrzanie wszyckich bytōw…", "optimizeWorld.stage.upgrading.poi": "Przetwŏrzanie wszyckich pōnktōw uwŏgi…", "optimizeWorld.title": "Ôptymalizowanie świŏta „%s”", "options.accessibility": "Ulekcynia dostympu…", "options.accessibility.high_contrast": "<PERSON><PERSON><PERSON>", "options.accessibility.high_contrast.error.tooltip": "<PERSON><PERSON> zas<PERSON>ōw ze srogim kōntrastym niyma dostympny.", "options.accessibility.high_contrast.tooltip": "Zmŏcniŏ kōntrast elymyntōw we interfejsie.", "options.accessibility.high_contrast_block_outline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.high_contrast_block_outline.tooltip": "Zmŏcniŏ kōntrast ôbry<PERSON> bloku za kursorym.", "options.accessibility.link": "Skŏzōwki ulekcyń dostympu", "options.accessibility.menu_background_blurriness": "Rozmŏzanie hintergrōndu", "options.accessibility.menu_background_blurriness.tooltip": "Kōntroluje <PERSON> rozmŏzaniŏ hintergrōndu we meni.", "options.accessibility.narrator_hotkey": "Skrōt ku ercyjlerowi", "options.accessibility.narrator_hotkey.mac.tooltip": "Przizwŏlŏ na przeszaltrowywanie ercyjlera po pōmocy ⌘B.", "options.accessibility.narrator_hotkey.tooltip": "Przizwŏlŏ na przeszaltrowywanie ercyjlera po pōmocy „Ctrl + B”.", "options.accessibility.panorama_speed": "Gib<PERSON><PERSON>ć przewijaniŏ panoramy", "options.accessibility.text_background": "Hintergrōnd tekstu", "options.accessibility.text_background.chat": "<PERSON><PERSON><PERSON>", "options.accessibility.text_background.everywhere": "Wszyńdzie", "options.accessibility.text_background_opacity": "Rozmŏzanie hintergrōndu tekstu", "options.accessibility.title": "Ulekcynia dostympu", "options.allowServerListing": "Pokazuj na spiskach", "options.allowServerListing.tooltip": "Serwery śmiōm udostympniać zestawiynie grŏczy onlajn jako tajlã jejich sztandu publicznego.\nPo wyłōnczyniu tego nasztalowania, twoje miano niy bydzie sie pokazować na takich listach.", "options.ao": "Gł<PERSON><PERSON><PERSON>", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "Minimum", "options.ao.off": "W<PERSON>ł.", "options.attack.crosshair": "<PERSON><PERSON><PERSON>", "options.attack.hotbar": "Pŏsek ze werkcojgami", "options.attackIndicator": "Blyndka napadu", "options.audioDevice": "Urzōndzynie", "options.audioDevice.default": "Sztandardowe", "options.autoJump": "Samoskŏkanie", "options.autoSuggestCommands": "Sugestyje kōmynd", "options.autosaveIndicator": "Skŏźnik samozachowaniŏ", "options.biomeBlendRadius": "Ślywanie biomōw", "options.biomeBlendRadius.1": "Bez (nojgibsze)", "options.biomeBlendRadius.11": "11 × 11 (ekstrymalne)", "options.biomeBlendRadius.13": "13 × 13 (s<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.15": "15 × 15 (ma<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.3": "3 × 3 (gibke)", "options.biomeBlendRadius.5": "5 × 5 (s<PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.7": "7 × 7 (sroge)", "options.biomeBlendRadius.9": "9 × 9 (fest sroge)", "options.chat": "Nasztalowania czatu…", "options.chat.color": "Farby", "options.chat.delay": "Ôpōźniynie czatu: %s zykōnd", "options.chat.delay_none": "Ôpōźniynie czatu: <PERSON><PERSON><PERSON>e", "options.chat.height.focused": "Wysokość akt. czatu", "options.chat.height.unfocused": "Wysokość niyakt. czatu", "options.chat.line_spacing": "<PERSON><PERSON><PERSON> <PERSON> l<PERSON>", "options.chat.links": "Linki ku neczajtōm", "options.chat.links.prompt": "Ôstrzygej przi <PERSON>w", "options.chat.opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "options.chat.scale": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "options.chat.title": "Nasztalowania czatu", "options.chat.visibility": "<PERSON><PERSON><PERSON>", "options.chat.visibility.full": "Widoczny", "options.chat.visibility.hidden": "Skry<PERSON>", "options.chat.visibility.system": "<PERSON><PERSON>", "options.chat.width": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.chunks": "%s czankōw", "options.clouds.fancy": "Urodne", "options.clouds.fast": "<PERSON><PERSON><PERSON>", "options.controls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "options.credits_and_attribution": "<PERSON>rzi a atrybucyjŏ…", "options.damageTiltStrength": "Strzōns ôd zrani<PERSON>ń", "options.damageTiltStrength.tooltip": "K<PERSON>nt<PERSON><PERSON><PERSON> intynziwność strzōnsu kamery ôd zrani<PERSON>ń.", "options.darkMojangStudiosBackgroundColor": "Czŏrno-biołe logo", "options.darkMojangStudiosBackgroundColor.tooltip": "Zmiyniŏ farbã hintergrōndu ykranu ladowaniŏ Mojang Studios na czŏrnõ.", "options.darknessEffectScale": "<PERSON><PERSON><PERSON><PERSON> cimy", "options.darknessEffectScale.tooltip": "Kōntroluje si<PERSON> miechtaniŏ efektu cimy przijmnytego ôd wachmajstra abo ôd sculkowego krzykŏcza.", "options.difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.easy": "Snadnŏ", "options.difficulty.easy.info": "Pojawiajōm sie agresywne stworzynia, ale zadŏwajōm mynij zraniyń. Głōd rośnie a śmi spotrzebować połowã twojigo zdrowiŏ.", "options.difficulty.hard": "Ciynżkŏ", "options.difficulty.hard.info": "Pojawiajōm sie agresywne stworzynia a zadŏwajōm wiyncyj zraniyń. Głōd rośnie a śmi spotrzebować cołke zdrowie.", "options.difficulty.hardcore": "Ôbmierzłŏ", "options.difficulty.normal": "Normalnŏ", "options.difficulty.normal.info": "Pojawiajōm sie agresywne stworzynia a zadŏwajōm sztandardowe zraniynia. Głōd rośnie a spotrzebowuje życie, choci<PERSON>ż ôd samego głodu niy <PERSON>.", "options.difficulty.online": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> serwera", "options.difficulty.peaceful": "Pokojowŏ", "options.difficulty.peaceful.info": "<PERSON>y poja<PERSON>m sie żŏdne agresywne stworzynia, a yno niykere te neutralne. Skŏźnik głodu niy spŏdŏ, a zdrowie samo sie ôdnŏwiŏ.", "options.directionalAudio": "Kerōnkowy klang", "options.directionalAudio.off.tooltip": "Klasyczny klang stereo", "options.directionalAudio.on.tooltip": "Używŏ przestrzynnego klangu ôprzitego na HRTF, coby <PERSON><PERSON> symulacyjõ klangu 3D. Wymŏgŏ sprzyntu audio, kery je zgodliwy ze HRTF, a nojlepij sie go doświŏdczŏ we słuchŏwkach.", "options.discrete_mouse_scroll": "Wyważōne przewijanie", "options.entityDistanceScaling": "Ôdlygłość bytu", "options.entityShadows": "<PERSON><PERSON><PERSON><PERSON> by<PERSON>w", "options.font": "Nasztalowania buchsztaby…", "options.font.title": "Nasztalowania buchsztaby", "options.forceUnicodeFont": "Unikodowŏ buchsztaba", "options.fov": "<PERSON> widzyniŏ", "options.fov.max": "Quake Pro", "options.fov.min": "Normalne", "options.fovEffectScale": "Efekty pola widzyniŏ", "options.fovEffectScale.tooltip": "Kōntroluje si<PERSON>łã zmiany pola widzyniŏ skuli efektōw.", "options.framerate": "%s FPS", "options.framerateLimit": "Maksymalne FPS", "options.framerateLimit.max": "<PERSON><PERSON> limitu", "options.fullscreen": "Połny ykrōn", "options.fullscreen.current": "Terŏźnŏ", "options.fullscreen.entry": "%s × %s przi %s Hz, %s Bit", "options.fullscreen.resolution": "Rozdziylszczość na fol ykrōnie", "options.fullscreen.unavailable": "Nasztalowanie niydostympne", "options.gamma": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.gamma.default": "Sztandardowŏ", "options.gamma.max": "Jasnŏ", "options.gamma.min": "Ćmawŏ", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON><PERSON><PERSON><PERSON> lyskaniŏ", "options.glintSpeed.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON> animacyje szajnu ôd zaklyntych rzeczy.", "options.glintStrength": "Natynżynie lyskaniŏ", "options.glintStrength.tooltip": "Kōntrol<PERSON><PERSON> prz<PERSON>z<PERSON> szajnu ôd zaklyntych rzeczy.", "options.graphics": "Ôbrŏz", "options.graphics.fabulous": "Bŏjkowy!", "options.graphics.fabulous.tooltip": "%s ôbr<PERSON>z używŏ nakłŏdek ôd ciyniowaniŏ ku rysowaniu pogody, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aji czōnsteczek za przejzdrzistymi blokami a wodōm.\n<PERSON><PERSON> to uzdanie wpłynyć na skutność na przenośnych urzōndzyniach a wyświytlŏczach 4K.", "options.graphics.fancy": "Urodny", "options.graphics.fancy.tooltip": "Nasztalowanie urodnego ôbrazu rōwnowŏży skutność a jakość na wiynkszości urzōndzyń.\nEfekty pogoda, ôbłokōw a czōnsteczki śmiōm sie niy pokazować za przejzdrzistymi blokami abo za wodōm.", "options.graphics.fast": "<PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "Wartkŏ jakość grafiki zmyńszŏ widzialność dyszcza a śniega.\nEfekt przejzdrzistości niykerych blokōw, takich jak <PERSON>, je wy<PERSON><PERSON><PERSON><PERSON>.", "options.graphics.warning.accept": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bez sparc<PERSON>", "options.graphics.warning.cancel": "Nazŏd", "options.graphics.warning.message": "%s ôbr<PERSON>z niyma spiyrany ôd wykrytego graficznego urzōndzyniŏ.\n\n<PERSON><PERSON><PERSON> to ôstrzeżynie a prz<PERSON> dalij, ale niy bydymy śmieli z<PERSON>ć sparciŏ po ôbraniu ôbrazu „%s”.", "options.graphics.warning.renderer": "Znŏjdzono renderer: [%s]", "options.graphics.warning.title": "Graficznŏ szkarta niyma s<PERSON>", "options.graphics.warning.vendor": "Znŏjdzōno ercojgnyra: [%s]", "options.graphics.warning.version": "Znŏjdzono wersyjõ OpenGL: [%s]", "options.guiScale": "Wielgość GUI", "options.guiScale.auto": "Autōmatycznŏ", "options.hidden": "Skry<PERSON>", "options.hideLightningFlashes": "<PERSON><PERSON><PERSON><PERSON><PERSON> ôd b<PERSON>w", "options.hideLightningFlashes.tooltip": "Skrywŏ blyski na niebie, kere towarziszōm szlagōm ôd blicōw. Same blice dalij byd<PERSON><PERSON> wid<PERSON>.", "options.hideMatchedNames": "<PERSON><PERSON><PERSON><PERSON> pod<PERSON>ana", "options.hideMatchedNames.tooltip": "Zewnōntrzne serwery śmiōm posyłać wiadōmości na czacie we niysztandardowych formatach.\nPo załōnczyniu tyj <PERSON>, skryci grŏcze ôstanōm przipasowani podle miana abzyndera.", "options.hideSplashTexts": "Sk<PERSON>j prziti<PERSON>", "options.hideSplashTexts.tooltip": "Skrywŏ żōłte przititle ze titlowego ykranu.", "options.inactivityFpsLimit": "Pōmyńsz FPS przi", "options.inactivityFpsLimit.afk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.inactivityFpsLimit.afk.tooltip": "Ôgraniczŏ liczbã klŏtek na zykōndã na 30, kej gra niy dostŏwŏ żŏdnych wejściowych danych ôd grŏcza minutã czasu. Ôgraniczŏ jã dalij na 10 klŏtek po dalszych 9 minutach.", "options.inactivityFpsLimit.minimized": "Zminimalizowaniu", "options.inactivityFpsLimit.minimized.tooltip": "Ôgraniczŏ liczbã klŏtek na zykōndã yno tedy, kedy ôkno ôd gry je zminimaliz<PERSON>ne.", "options.invertMouse": "Ôdwrōć ôśk<PERSON> my<PERSON>ki", "options.japaneseGlyphVariants": "Japanerske <PERSON> g<PERSON>w", "options.japaneseGlyphVariants.tooltip": "Używŏ japanerskich waryjantōw znakōw CJK we sztandardowyj buchsztabie.", "options.key.hold": "<PERSON>rz<PERSON><PERSON><PERSON><PERSON>", "options.key.toggle": "<PERSON>rz<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.language": "Gŏdka…", "options.language.title": "Gŏdka", "options.languageAccuracyWarning": "(Przekłŏdy śmiōm niy być we 100%% akuratne)", "options.languageWarning": "Przekłŏdy śmiōm niy być we 100%% akuratne", "options.mainHand": "Gōwnŏ rynka", "options.mainHand.left": "<PERSON><PERSON>", "options.mainHand.right": "<PERSON><PERSON>", "options.mipmapLevels": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.cape": "<PERSON><PERSON>", "options.modelPart.hat": "Hu<PERSON>", "options.modelPart.jacket": "<PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "Lewŏ nogawica", "options.modelPart.left_sleeve": "<PERSON><PERSON>", "options.modelPart.right_pants_leg": "Prawŏ nogawica", "options.modelPart.right_sleeve": "<PERSON><PERSON><PERSON> rynk<PERSON>w", "options.mouseWheelSensitivity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> przewijaniŏ", "options.mouse_settings": "Nasztalowania myszki…", "options.mouse_settings.title": "Nasztalowania myszki", "options.multiplayer.title": "Nasztalowania mocperzōnowyj gry…", "options.multiplier": "%sx", "options.music_frequency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> muzyki", "options.music_frequency.constant": "St<PERSON>ł<PERSON>", "options.music_frequency.default": "Normalnŏ", "options.music_frequency.frequent": "<PERSON><PERSON><PERSON>", "options.music_frequency.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jak czynsto muzyka bydzie gran<PERSON> we świecie.", "options.narrator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.narrator.all": "Wyczytuje wszycko", "options.narrator.chat": "<PERSON>y<PERSON><PERSON><PERSON><PERSON> c<PERSON>t", "options.narrator.notavailable": "Niydostympny", "options.narrator.off": "W<PERSON>ł.", "options.narrator.system": "Wyczytuje systymowe wiadōmości", "options.notifications.display_time": "Czas wyświytlaniŏ powiadōmiyń", "options.notifications.display_time.tooltip": "Kōntroluje wiela czasu powiadōmiynia ôstŏwajōm widzialne na ykrōnie.", "options.off": "W<PERSON>ł.", "options.off.composed": "%s: <PERSON><PERSON><PERSON>.", "options.on": "<PERSON><PERSON><PERSON>.", "options.on.composed": "%s: <PERSON><PERSON><PERSON>.", "options.online": "Nec…", "options.online.title": "Nasztalowania necu", "options.onlyShowSecureChat": "Pokazuj yno bezpiyczny czat", "options.onlyShowSecureChat.tooltip": "<PERSON><PERSON><PERSON><PERSON> ôd gr<PERSON>ōw yno te wiadōmo<PERSON>ci, u kerych idzie przituplikować, iże ôd istych ôstały posłane, a yno te, kere niy sōm fałszywe.", "options.operatorItemsTab": "<PERSON><PERSON><PERSON><PERSON>", "options.particles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.particles.all": "<PERSON><PERSON><PERSON><PERSON>", "options.particles.decreased": "Zmyńszōne", "options.particles.minimal": "Minimalne", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "<PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer": "Czyńściowŏ blokujōnce", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Niykere dzio<PERSON>nia we ôbrymbie czanku sprŏwiōm, iże czank ôstanie ôrŏz przeladowany. Stŏwianie a niszczynie blokōw sōm przikłady takich dziołań.", "options.prioritizeChunkUpdates.nearby": "We połni blokujōnce", "options.prioritizeChunkUpdates.nearby.tooltip": "Pobliske czanki sōm dycki ôrŏz kōmpilowane. <PERSON><PERSON> to wpłynyć na skutność gry, kedy bloki sōm stŏwiane abo niszczōne.", "options.prioritizeChunkUpdates.none": "W<PERSON>ntkowe", "options.prioritizeChunkUpdates.none.tooltip": "Pobliske chunki sōm kōmpilowane na rōwnolygłych wōntkach. <PERSON><PERSON> to skutkować we krōtkotwałych pauzach we wyświytlaniu, kej bloki sōm niszczone.", "options.rawMouseInput": "Bezpostrzedni ôdczyt", "options.realmsNotifications": "Powiadomiynia Realms", "options.realmsNotifications.tooltip": "Pobiyrŏ wiadōmości a zaproszynia ze Realms ze titlowego ykranu a wyświytlŏ ôdpedniõ ikōnã na kneflu „Realms”.", "options.reducedDebugInfo": "<PERSON><PERSON>j informacyji <PERSON>", "options.renderClouds": "Ôbłoki", "options.renderCloudsDistance": "Ôdlygłość ôbłokōw", "options.renderDistance": "Ôdlygłość <PERSON><PERSON><PERSON>", "options.resourcepack": "<PERSON><PERSON> z<PERSON>…", "options.rotateWithMinecart": "Ôbrŏcanie ze wagōnym", "options.rotateWithMinecart.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eli widok grŏcza winiyn sie ôbrŏcać społym ze wagōnym, kej ôn sie ôbrŏcŏ. Ta regla je dostympno yno we świŏtach, kere majōm załōnczōnõ ekszperymyntalne nasztalowanie „UIepszynia wagōnōw”.", "options.screenEffectScale": "Efekt zniyksztołcyniŏ", "options.screenEffectScale.tooltip": "Siyła efektu zniyksztołcyniŏ ykranu sprŏwiōnego ôd morzyska a portali ku Netherze.\nPrzi ni<PERSON> werta<PERSON>, efekt morzyska ôstŏwŏ zastōmpiōny ôd zielōnyj, przejzdrzisty<PERSON> wŏrstwy.", "options.sensitivity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.sensitivity.max": "PRZEDRABKOŚĆ!!!", "options.sensitivity.min": "*ziyw*", "options.showNowPlayingToast": "Dymek ze utworym", "options.showNowPlayingToast.tooltip": "<PERSON><PERSON><PERSON><PERSON>, kej <PERSON> grać utwōr. Dymek tyż je widoczny we meni pauzy gry podczas odtwŏrzaniŏ.", "options.showSubtitles": "Podtitle", "options.simulationDistance": "<PERSON><PERSON><PERSON><PERSON> symula<PERSON>", "options.skinCustomisation": "Stosowanie s<PERSON>i…", "options.skinCustomisation.title": "Stosowanie s<PERSON>rki", "options.sounds": "Muzyka a klang…", "options.sounds.title": "Nasztalowania muzyki a klangu", "options.telemetry": "Telemetryczne dane…", "options.telemetry.button": "<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>ch", "options.telemetry.button.tooltip": "„%s” zawiyrŏ ino wymŏgane dane.\n„%s” zawiyrŏ tak niywymŏgane jak tyż wymŏgane dane.", "options.telemetry.disabled": "Telemetryjŏ je wyłōnczōnŏ.", "options.telemetry.state.all": "<PERSON><PERSON><PERSON><PERSON>", "options.telemetry.state.minimal": "Minimalne", "options.telemetry.state.none": "Żŏdne", "options.title": "Nasztalowania", "options.touchscreen": "<PERSON><PERSON> tykowego ykranu", "options.video": "Nasztalowania ôbrazu…", "options.videoTitle": "Nasztalowania ôbrazu", "options.viewBobbing": "Animacyj<PERSON> chodzyniŏ", "options.visible": "W<PERSON>czne", "options.vsync": "VSync", "outOfMemory.message": "Minecraftowi skōńczyła sie pamiyńć.\n\n<PERSON><PERSON><PERSON> to być sprawiōne ôd feleru we grze abo ôd niyprzidzielyniŏ wielości pamiyńci JVM, kerŏ by stykała ku graniu.\n\nCoby zapobi<PERSON><PERSON>, gra ôstała sztopnytŏ. Prōbowali my zwolnić za tela pamiyńci, coby sie doło wrō<PERSON> ku gōwnym ykrō<PERSON>, nale dalij je mo<PERSON>, iże to sie niy podarziło.\n\nEźli zaś ôbŏczysz tã wiad<PERSON>, sztartnij na zaś grã.", "outOfMemory.title": "Brak pami<PERSON>ńci!", "pack.available.title": "Dostympne", "pack.copyFailure": "Kopiyrowanie paketōw sie niy podarziło", "pack.dropConfirm": "Na isto ch<PERSON>z przidać dalsze pakety ku Minecraftowi?", "pack.dropInfo": "Przeciōng a spuś dataje na to ôkno, coby prz<PERSON><PERSON> pakety", "pack.dropRejected.message": "Dalsze wpisy bōły niynŏleżne a niy ôstały przekopiyrowane:\n %s", "pack.dropRejected.title": "<PERSON><PERSON><PERSON><PERSON> spoza paketu", "pack.folderInfo": "(Samkej wraź paketowe dataje)", "pack.incompatible": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.incompatible.confirm.new": "Tyn paket bōł stworzōny lō nowszyj wersyje Minecrafta a je możeb<PERSON>ść, iże niy bydzie nŏleżnie fōngowoł.", "pack.incompatible.confirm.old": "Tyn paket bōł stworzōny lŏ starszyj wersyje Minecrafta a je możeb<PERSON>ść, iże niy bydzie nŏleżnie fōngowoł.", "pack.incompatible.confirm.title": "Na isto ch<PERSON> tyn paket?", "pack.incompatible.new": "(Stworzōny lō nowszyj wersyje Minecrafta)", "pack.incompatible.old": "(Stworzōny lŏ starszyj wersyje <PERSON>crafta)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Ôdewrzij folder ze paketami", "pack.selected.title": "Ôbrane", "pack.source.builtin": "wbudowany", "pack.source.feature": "funkcyjŏ", "pack.source.local": "lokalny", "pack.source.server": "serwerowy", "pack.source.world": "świŏt", "painting.dimensions": "%s × %s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "<PERSON><PERSON>", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Barŏk", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON><PERSON> wysadz<PERSON>ny ku myśli", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Buket", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Jaskiniowy ptŏk", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Zmian<PERSON>", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Kotan", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>, panie Courbet", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Ôstateczny wrōg", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Ôdekrycie", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Ôgyń", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab ze trzema papryczkami", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Niskŏ mgła", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Sztrachecla", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Medytacyjŏ", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "Sfera", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Cytru-sowy", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Przechōd", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Dziyweczka ze świnkōm", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "<PERSON><PERSON> str<PERSON>", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pokŏzowŏcz", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Stŏw", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "Basyn", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Preriowŏ rajza", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Wybrzeże", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Śmiyrtelny zwōj", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "S<PERSON>jdel a rōłzy", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "<PERSON><PERSON> fertich", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Przipływy", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Wypakowane", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "Prōżnia", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "<PERSON><PERSON><PERSON><PERSON>", "parsing.bool.expected": "Spodziywano wertu zorty boolean", "parsing.bool.invalid": "<PERSON><PERSON>ny wert zorty boolean, s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „true” abo „false”, a znŏjdzono: „%s”", "parsing.double.expected": "Spodziywano wertu zorty double", "parsing.double.invalid": "Felerny wert zorty double: „%s”", "parsing.expected": "Spodziywano: „%s”", "parsing.float.expected": "Spodziywano wertu zorty float", "parsing.float.invalid": "Felerny wert zorty float: „%s”", "parsing.int.expected": "Spodziywano wertu zorty integer", "parsing.int.invalid": "Felerny wert zorty integer: „%s”", "parsing.long.expected": "S<PERSON>d<PERSON>ywano wertu zorty long", "parsing.long.invalid": "Felerny wert zorty long: „%s”", "parsing.quote.escape": "Felernŏ sekwyncyjŏ uwŏlnianiŏ we anfirowanym ciōngu: „\\%s”", "parsing.quote.expected.end": "Niyzawrzity anfirowany ciōng", "parsing.quote.expected.start": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, co<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> ciōng", "particle.invalidOptions": "Niy idzie sparsować nasztalowań czōnstek: %s", "particle.notFound": "Niyznany czōnstka: „%s”", "permissions.requires.entity": "<PERSON><PERSON> k<PERSON> musi samkej użyć byt", "permissions.requires.player": "<PERSON><PERSON> k<PERSON> musi samkej użyć grŏcz", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Przi użyciu:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Niyznany predykat: „%s”", "quickplay.error.invalid_identifier": "Niy idzie znŏjś świŏta ze danym ID", "quickplay.error.realm_connect": "Niy idzie połōnc<PERSON>ć ze Realmym", "quickplay.error.realm_permission": "Brak uprawniyń ku połōnczyniu ze tym Realmym", "quickplay.error.title": "<PERSON><PERSON> podarziło sie sztartnyć gibkij gry", "realms.configuration.region.australia_east": "Nowŏ Połedniowŏ Walijŏ, Australijŏ", "realms.configuration.region.australia_southeast": "Wiktoryjŏ, Australijŏ", "realms.configuration.region.brazil_south": "<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.central_india": "<PERSON><PERSON>", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "Pōłnoc<PERSON>, USA", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "Schodniŏ Japōnijŏ", "realms.configuration.region.japan_west": "Zach<PERSON>ni<PERSON> Japōnij<PERSON>", "realms.configuration.region.korea_central": "Połedniowŏ Koryjŏ", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Irlandyjŏ", "realms.configuration.region.south_central_us": "Teksas, USA", "realms.configuration.region.southeast_asia": "Singapur", "realms.configuration.region.sweden_central": "Szwe<PERSON>jŏ", "realms.configuration.region.uae_north": "Zjednoczōne Arabske Emiraty (UAE)", "realms.configuration.region.uk_south": "Połedniowŏ Anglijŏ", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Niderlandy", "realms.configuration.region.west_us": "Kalifornijŏ, USA", "realms.configuration.region.west_us_2": "Waszyngtōn, USA", "realms.configuration.region_preference.automatic_owner": "Autōmatyczny (ping włŏściciela Realmu)", "realms.configuration.region_preference.automatic_player": "Autōmatyczny (ôd pi<PERSON><PERSON>ego grŏcza dołōnc<PERSON>)", "realms.missing.snapshot.error.text": "Realms ôbecnie niy majōm sparci<PERSON> we snapszotach", "recipe.notFound": "Niyznany recept: „%s”", "recipe.toast.description": "Badnij ksiōnżkã receptōw", "recipe.toast.title": "Ôdbloknyto nowe recepty!", "record.nowPlaying": "Terŏzki grŏ: %s", "recover_world.bug_tracker": "<PERSON><PERSON><PERSON><PERSON><PERSON> feler", "recover_world.button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recover_world.done.failed": "Niy podarziło sie ôdzyskać świŏta ze skorszego sztandu.", "recover_world.done.success": "Ôdzyskowanie sie podarziło!", "recover_world.done.title": "Zakōńczōno ôdzyskowanie", "recover_world.issue.missing_file": "Brak dataje", "recover_world.issue.none": "Bez niyprzileż<PERSON>ści", "recover_world.message": "Natrefiōno na dalsze niyprzileżytości podczas prōby ôdczytaniŏ folderu ze świŏtym „%s”.\nSprōbuj prziwrōcić świŏt ze skorszego sztandu abo zgłoś feler.", "recover_world.no_fallback": "Niy ma żŏdnego dostympnego sztandu ku ôdzyskaniu świŏta", "recover_world.restore": "<PERSON>prō<PERSON><PERSON>", "recover_world.restoring": "Prōbowanie prziwrōcyniŏ świŏta…", "recover_world.state_entry": "Stan ze %s: ", "recover_world.state_entry.unknown": "niyznany", "recover_world.title": "<PERSON><PERSON> podarziło sie zaladować świŏta", "recover_world.warning": "<PERSON><PERSON> podarziło sie zaladować podsumowaniŏ świŏta", "resourcePack.broken_assets": "ZNŎJDZONO FELERNE ZASOBY", "resourcePack.high_contrast.name": "<PERSON><PERSON><PERSON>", "resourcePack.load_fail": "<PERSON><PERSON> podarziło sie przeladować zasobōw", "resourcePack.programmer_art.name": "Zasoby programistōw", "resourcePack.runtime_failure": "<PERSON><PERSON><PERSON><PERSON> feler we pakecie zas<PERSON>ōw", "resourcePack.server.name": "Szpecyjalne zasoby świŏta", "resourcePack.title": "Ôbier pakety zasob<PERSON>w", "resourcePack.vanilla.description": "Wygodny wyglōnd a styl Minecrafta", "resourcePack.vanilla.name": "Vanilla", "resourcepack.downloading": "Pobiyra<PERSON> pake<PERSON> z<PERSON>w", "resourcepack.progress": "Pobiyranie dataje (%s MB)…", "resourcepack.requesting": "<PERSON><PERSON><PERSON><PERSON>…", "screenshot.failure": "<PERSON><PERSON> <PERSON><PERSON> z<PERSON> schytu ykranu: %s", "screenshot.success": "Zachowano schyt ykranu we %s", "selectServer.add": "<PERSON><PERSON><PERSON><PERSON> serwer", "selectServer.defaultName": "Serwer <PERSON>", "selectServer.delete": "Usuń", "selectServer.deleteButton": "Usuń", "selectServer.deleteQuestion": "Na isto ch<PERSON>z usuny<PERSON> tyn serwer?", "selectServer.deleteWarning": "Serwer „%s” ôstanie stracōny na zŏwdy! (To je naprŏwdã dugo!)", "selectServer.direct": "Bezpostrzydnie połōnczynie", "selectServer.edit": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.hiddenAddress": "(Skryte)", "selectServer.refresh": "Ôdświyż", "selectServer.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.access_failure": "<PERSON><PERSON> podarziło sie wlyź na świŏt", "selectWorld.allowCommands": "Przizwŏlej na kōmyndy", "selectWorld.allowCommands.info": "<PERSON><PERSON><PERSON><PERSON> jak choby /gamemode a /experience", "selectWorld.allowCommands.new": "Przizwŏlej na kōmyndy", "selectWorld.backupEraseCache": "Usuń pufer danych", "selectWorld.backupJoinConfirmButton": "Stwōrz ibrycznõ kopijõ a zaladuj", "selectWorld.backupJoinSkipButton": "<PERSON><PERSON><PERSON>, co robiã!", "selectWorld.backupQuestion.customized": "Niysztandardowe świŏty już niy sōm ôbsługowane", "selectWorld.backupQuestion.downgrade": "Ôbniżanie wersyje niyma <PERSON>bsługowane lŏ tego świŏta", "selectWorld.backupQuestion.experimental": "<PERSON><PERSON><PERSON><PERSON>, kere używajōm ekszperymyntalnych nasztalowań, niy majōm sparci<PERSON>", "selectWorld.backupQuestion.snapshot": "Na isto ch<PERSON> tyn świŏt?", "selectWorld.backupWarning.customized": "<PERSON><PERSON><PERSON><PERSON>, niy ôbsługujymy niysztandardowych świŏtōw na tyj wersyji Minecrafta. Durch mogymy zaladować tyn świŏt a zachować wszycko tako, jako s<PERSON><PERSON><PERSON> b<PERSON>, ale wszeli niydŏwno wygynerowany teryn sie zresetuje. Przeprŏszōmy za starość!", "selectWorld.backupWarning.downgrade": "Tyn świ<PERSON>t bōł skorzij ôdewiyrany na wersyji %s, a terŏzki grŏsz na wersyji %s. Ôbniżynie wersyje gry może spowodować jego uszkodzynie — niy m<PERSON><PERSON>, i<PERSON><PERSON>tar<PERSON>, ani iże bydzie nŏleżnie fōngowoł. Eźli chcesz przelyź dalij, <PERSON><PERSON><PERSON><PERSON>, co<PERSON>ś stworzōł ibrycznõ kopijõ.", "selectWorld.backupWarning.experimental": "Tyn świ<PERSON>t używŏ ekszperymyntalnych nasztalowań, kere kedy yno śmiōm ustŏć fōngować. <PERSON><PERSON> m<PERSON><PERSON>, i<PERSON><PERSON> s<PERSON>tar<PERSON>, ani iże bydzie nŏleżnie fōngowoł. Dŏwej sie baczynie!", "selectWorld.backupWarning.snapshot": "Tyn świŏt bōł skorzij ôdewiyrany na wersyji %s, a terŏzki grŏsz na wersyji %s. Proszymy stworzić ibrycznõ kopijõ we razie uszkodzyniŏ świŏta.", "selectWorld.bonusItems": "Geszynkowŏ kastla", "selectWorld.cheats": "Szybalstwa", "selectWorld.commands": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.conversion": "Wymŏgŏ przetworzyniŏ!", "selectWorld.conversion.tooltip": "<PERSON><PERSON> nŏleży ôdewrzić na starszyj wersyji, (cum bajszpil 1.6.4), coby p<PERSON><PERSON><PERSON> b<PERSON> bezpi<PERSON>", "selectWorld.create": "Stwōrz nowy świŏt", "selectWorld.customizeType": "Nasztalowania", "selectWorld.dataPacks": "<PERSON><PERSON> danych", "selectWorld.data_read": "Ôdczytowanie danych <PERSON>wj<PERSON>...", "selectWorld.delete": "Usuń", "selectWorld.deleteButton": "Usuń", "selectWorld.deleteQuestion": "Na isto ch<PERSON>z us<PERSON> tyn świ<PERSON>?", "selectWorld.deleteWarning": "Świŏt „%s” ôstanie stracōny na zŏwdy! (To je naprŏwdã dugo!)", "selectWorld.delete_failure": "<PERSON><PERSON> podarziło sie usunyć świŏta", "selectWorld.edit": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.backup": "Stwōrz i<PERSON>õ kopijõ", "selectWorld.edit.backupCreated": "Stworzōno ibrycznõ kopijõ: %s", "selectWorld.edit.backupFailed": "<PERSON><PERSON> podarziło sie stworzić ibrycznej kopije", "selectWorld.edit.backupFolder": "Ôdewrzij folder ze ibrycznymi kopijami", "selectWorld.edit.backupSize": "wielgość: %s MB", "selectWorld.edit.export_worldgen_settings": "Ekszportuj nasztalowania gyneracyje świŏta", "selectWorld.edit.export_worldgen_settings.failure": "<PERSON>y podarziło sie wyekszportować świŏta", "selectWorld.edit.export_worldgen_settings.success": "Wyekszportowano", "selectWorld.edit.openFolder": "Ôdewrzij folder świŏta", "selectWorld.edit.optimize": "Zôptymali<PERSON><PERSON>", "selectWorld.edit.resetIcon": "Zresetuj ikōnã", "selectWorld.edit.save": "<PERSON><PERSON><PERSON>", "selectWorld.edit.title": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.enterName": "Miano świ<PERSON>", "selectWorld.enterSeed": "Ziŏrno lŏ gyneratora świŏta", "selectWorld.experimental": "Ekszperymyntalny", "selectWorld.experimental.details": "Ôsobliwości", "selectWorld.experimental.details.entry": "Wymŏgane ekszperymyntalne fōnkcyje: %s", "selectWorld.experimental.details.title": "Wymŏgania ekszperymyntalnych fōnkcyji", "selectWorld.experimental.message": "Dej pozōr!\nNiykere ze ôbranych paketōw wymŏgajōm fōnkcyji, co sie jeszcze rozwijajōm. Twōj świŏt śmi sie <PERSON>, <PERSON><PERSON><PERSON><PERSON>, abo niy fō<PERSON> nŏleżnie przi nowych aktualizacyjach.", "selectWorld.experimental.title": "Pozōr o ekszperymyntalnych fōnkcyjach", "selectWorld.experiments": "Ekszper<PERSON>ynty", "selectWorld.experiments.info": "Ekszperymynty sōm potyncjalne nowości. <PERSON><PERSON> p<PERSON> — coś sie może popsować. Po stworz<PERSON><PERSON>wi<PERSON>, ekszperymyntōw już niy idzie wyłōnczyć.", "selectWorld.futureworld.error.text": "Feler przi prōbie zaladowaniŏ świŏta, k<PERSON> bōł stworzōny na nowszyj wersyje. Ta ôperacyjŏ bōła ryzykownŏ — prz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, iże sie niy podarziła.", "selectWorld.futureworld.error.title": "Doszło ku felerowi!", "selectWorld.gameMode": "<PERSON><PERSON> gry", "selectWorld.gameMode.adventure": "Przigoda", "selectWorld.gameMode.adventure.info": "Jednakowe prŏwidła jak we trybie przetwaniŏ, ale niy ś<PERSON> s<PERSON><PERSON> ani usuwać blokōw.", "selectWorld.gameMode.adventure.line1": "Prŏwidła jako we trybie przeżyciŏ, ale niy ś<PERSON>z", "selectWorld.gameMode.adventure.line2": "s<PERSON><PERSON><PERSON><PERSON><PERSON> ani <PERSON> blokōw", "selectWorld.gameMode.creative": "Kreatywny", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON><PERSON>, buduj a rajzuj bez limitu. <PERSON><PERSON><PERSON>, m<PERSON><PERSON> niyôgranic<PERSON><PERSON>, a je żeś niyśmiyrtelny.", "selectWorld.gameMode.creative.line1": "Niyskō<PERSON><PERSON><PERSON>ne mater<PERSON>je, swobodne lŏtanie a", "selectWorld.gameMode.creative.line2": "ôrŏzkowe niszczynie blokōw", "selectWorld.gameMode.hardcore": "Ôbmierzły", "selectWorld.gameMode.hardcore.info": "Tryb przetwaniŏ je zaszperowany na ciynżkim poziōmie, a eźli zginiesz, już niy byd<PERSON>sz śmioł sie ôdrodzić.", "selectWorld.gameMode.hardcore.line1": "Prŏwidła jako we trybie przeżyciŏ ôkrōm c<PERSON>, ker<PERSON> je zaszperowanŏ za trudnŏ,", "selectWorld.gameMode.hardcore.line2": "a <PERSON><PERSON><PERSON><PERSON>, kere we tym trybie mŏsz tylko jedno", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "<PERSON><PERSON><PERSON><PERSON>, ale <PERSON>y tyk<PERSON>.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON><PERSON><PERSON>, ale <PERSON>y tyka<PERSON>", "selectWorld.gameMode.survival": "Przeżycie", "selectWorld.gameMode.survival.info": "Zwiyd<PERSON>j <PERSON>wiŏt, we k<PERSON><PERSON> bud<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, tworzisz a fechtujesz ze roztōmańtymi bebŏkami.", "selectWorld.gameMode.survival.line1": "Szukej surowcōw, rychtuj, zdobywej", "selectWorld.gameMode.survival.line2": "p<PERSON><PERSON><PERSON><PERSON>, jydz a dbej ô zdrowie", "selectWorld.gameRules": "Prŏwidła gry", "selectWorld.import_worldgen_settings": "Importuj nasztalowania", "selectWorld.import_worldgen_settings.failure": "<PERSON><PERSON> podarziło sie zaimportować nasztalowań", "selectWorld.import_worldgen_settings.select_file": "Ôbier dataj<PERSON> nasz<PERSON> (.json)", "selectWorld.incompatible.description": "Niy id<PERSON> ô<PERSON>wrzić tego świŏta na tyj wersyji.\nPoślednio grano na nim na wersyji %s.", "selectWorld.incompatible.info": "Spōrnŏ wersyjŏ: %s", "selectWorld.incompatible.title": "Spōrnŏ wersyjŏ", "selectWorld.incompatible.tooltip": "<PERSON><PERSON> <PERSON><PERSON> tego świ<PERSON>, pōniyw<PERSON><PERSON> bōł ôn stworzōny na spōrnyj wersyji.", "selectWorld.incompatible_series": "Stworzōny na spōrnyj wersyji", "selectWorld.load_folder_access": "Ni<PERSON> idzie <PERSON>ć abo zyskać dostympu ku folderowi ze zachowanymi świŏtami!", "selectWorld.loading_list": "Ladowanie spiski świŏtōw", "selectWorld.locked": "Bloknyte ôd inkszyj załōnczōnego wystōmpiyniŏ Minecrafta", "selectWorld.mapFeatures": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON><PERSON>, wraki szifōw atd.", "selectWorld.mapType": "<PERSON>orta <PERSON>", "selectWorld.mapType.normal": "Normalny", "selectWorld.moreWorldOptions": "Wiyncyj nasztalowań świŏta…", "selectWorld.newWorld": "Nowy świŏt", "selectWorld.recreate": "Ôdetwōrz", "selectWorld.recreate.customized.text": "Niysztandardowe świŏty już niy majōm sparciŏ na tyj wersyji Minecrafta. Mogymy sprōbow<PERSON><PERSON> ôdtworz<PERSON> świŏt ze jednakowym ziŏrnym a włŏs<PERSON>ściami, ale stracōm sie wszelke stosowania terynu. Przeprŏszōmy za starość!", "selectWorld.recreate.customized.title": "Niysztandardowe świŏty już niy sōm ôbsługowane", "selectWorld.recreate.error.text": "<PERSON><PERSON> podarziło sie ôdtworzić świŏta.", "selectWorld.recreate.error.title": "Doszło ku felerowi!", "selectWorld.resource_load": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>…", "selectWorld.resultFolder": "Bydzie zachowany we:", "selectWorld.search": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.seedInfo": "Ôstŏw p<PERSON><PERSON><PERSON>, coby wyl<PERSON> zi<PERSON>rno", "selectWorld.select": "Grej na ôbranym świecie", "selectWorld.targetFolder": "Folder zachowaniŏ: %s", "selectWorld.title": "Ôbier świŏt", "selectWorld.tooltip.fromNewerVersion1": "Świŏt ôstoł zachowany na nowszyj wersyji.", "selectWorld.tooltip.fromNewerVersion2": "Zaladowanie tego świŏta może sprawić niyprzileż<PERSON>ści!", "selectWorld.tooltip.snapshot1": "Niy zapōmnij stworzić ibrycznej kopije swojigo świŏta", "selectWorld.tooltip.snapshot2": "niż go zaladujesz na tym snapszocie.", "selectWorld.unable_to_load": "<PERSON><PERSON> <PERSON><PERSON>", "selectWorld.version": "wersyjŏ:", "selectWorld.versionJoinButton": "Zaladuj <PERSON>z<PERSON>", "selectWorld.versionQuestion": "Na isto ch<PERSON>z z<PERSON>dować tyń świŏt?", "selectWorld.versionUnknown": "niyznanŏ", "selectWorld.versionWarning": "Tyn świŏt bōł skorzij ôdewiyrany na wersyji %s. Zaladowanie go na tyj wersyji może uszkodzić twōj świŏt!", "selectWorld.warning.deprecated.question": "<PERSON><PERSON><PERSON> fōnkcyje sōm przedŏwniōne a we prziszłości ustanōm fōngować. Na isto chcesz przelyź dalij?", "selectWorld.warning.deprecated.title": "Pozōr! To nasztalowanie używŏ niyaktualnych fōnkcyji", "selectWorld.warning.experimental.question": "To nasztalowanie je ekszperymyntalne a śmi ustŏć fōngować we prziszłości. Chcesz przelyź dalij?", "selectWorld.warning.experimental.title": "Pozōr! To nasztalowanie używŏ ekszperymyntalnych fōnkcyji", "selectWorld.warning.lowDiskSpace.description": "Na maszynie ôstało małowiela swobodnego placu.\nWyczyrpanie placu na dysku czasu gry śmi dokludzić ku popsowaniu świŏta.", "selectWorld.warning.lowDiskSpace.title": "Pozōr! Mało placu na dysku!", "selectWorld.world": "Świŏt", "sign.edit": "Edyt<PERSON>j inhalt znaku", "sleep.not_possible": "Przesypianie nocy niyma możebne", "sleep.players_sleeping": "%s/%s gr<PERSON><PERSON>y śpi", "sleep.skipping_night": "Przesypianie nocy", "slot.only_single_allowed": "Przizwolōne sōm ino pojedyncze pola, a erbnyto „%s”", "slot.unknown": "Niyznane pole: „%s”", "snbt.parser.empty_key": "Klucz niy może być prōżny", "snbt.parser.expected_binary_numeral": "Spod<PERSON>y<PERSON><PERSON> dwōjkowyj nōmery", "snbt.parser.expected_decimal_numeral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>wyj nōmery", "snbt.parser.expected_float_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> zorty „float”", "snbt.parser.expected_hex_escape": "Spodziywano dosłownego znaku ô dugości %s", "snbt.parser.expected_hex_numeral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>kowyj nōmery", "snbt.parser.expected_integer_type": "Spodziywano nōmery zorty „integer”", "snbt.parser.expected_non_negative_number": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ō<PERSON>", "snbt.parser.expected_number_or_boolean": "Spod<PERSON>ywano nōmery abo logicznego wertu", "snbt.parser.expected_string_uuid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>, k<PERSON> <PERSON><PERSON><PERSON><PERSON> wyrŏz nŏleżnymu UUID", "snbt.parser.expected_unquoted_string": "Spodziywano nŏleżnego niyanfirowanego ciōngu", "snbt.parser.infinity_not_allowed": "Niyskōńczōne nōmery sōm niyprzizwolōne", "snbt.parser.invalid_array_element_type": "Felernŏ zorta elymyntu tabule", "snbt.parser.invalid_character_name": "<PERSON><PERSON>ne miano unikodowego znaku", "snbt.parser.invalid_codepoint": "Felerny wert unikodowego znaku: %s", "snbt.parser.invalid_string_contents": "Felerny inhalt ciōngu", "snbt.parser.invalid_unquoted_start": "Niyanfirowane ciōngi niy śmiōm sie nap<PERSON> ôd cyfrōw 0–9, plusa (+) ani minusa (-)", "snbt.parser.leading_zero_not_allowed": "Dziesiōntkowe nōmery niy śmiōm sie z<PERSON> ôd nule", "snbt.parser.no_such_operation": "Niy ma żŏdnyj takij ôperacyje: %s", "snbt.parser.number_parse_failure": "Feler przi przetworzyniu nōmery: „%s”", "snbt.parser.undescore_not_allowed": "Znaki podkreślyniŏ niy sōm przizwolōne na poczōntku ani na kōńcu nōmery", "soundCategory.ambient": "Strzodowisko", "soundCategory.block": "Bloki", "soundCategory.hostile": "Niyprzŏciele", "soundCategory.master": "Ôgōlny poziōm klangu", "soundCategory.music": "Muzyka", "soundCategory.neutral": "Przŏciele", "soundCategory.player": "Grŏcze", "soundCategory.record": "Klangbloki", "soundCategory.ui": "Interfejs", "soundCategory.voice": "<PERSON><PERSON><PERSON>", "soundCategory.weather": "Pogoda", "spectatorMenu.close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spectatorMenu.next_page": "Nŏstympnŏ zajta", "spectatorMenu.previous_page": "<PERSON>rz<PERSON><PERSON><PERSON> zajta", "spectatorMenu.root.prompt": "<PERSON><PERSON><PERSON> knefel, coby <PERSON><PERSON>, a drugi r<PERSON><PERSON>, coby jij <PERSON>.", "spectatorMenu.team_teleport": "Przeteleportuj ku człōnkowi manszaftu", "spectatorMenu.team_teleport.prompt": "Ôbier mansz<PERSON>, coby sie ku niymu prz<PERSON>", "spectatorMenu.teleport": "Teleportuj sie ku g<PERSON>ŏczowi", "spectatorMenu.teleport.prompt": "Ôbier gr<PERSON><PERSON><PERSON>, coby sie ku niymu przete<PERSON>", "stat.generalButton": "Ôgōlne", "stat.itemsButton": "Rzeczy", "stat.minecraft.animals_bred": "Rozmnożōnych zwiyrzyńć", "stat.minecraft.aviate_one_cm": "Przelecōno po ôkrywach", "stat.minecraft.bell_ring": "Uderzōnych zwōnōw", "stat.minecraft.boat_one_cm": "Przepłynyto we łōdce", "stat.minecraft.clean_armor": "Wyczyszczōnych tajli pancrōw", "stat.minecraft.clean_banner": "<PERSON>ypucowan<PERSON> fanōw", "stat.minecraft.clean_shulker_box": "Ôprōżniōnych shulkerowych kastli", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.damage_absorbed": "Przijmnytych zraniyń", "stat.minecraft.damage_blocked_by_shield": "Zraniynia bloknyte po pōmocy tarczy", "stat.minecraft.damage_dealt": "Wyrzōndzōnych zraniyń", "stat.minecraft.damage_dealt_absorbed": "Wyrzōndzōnych zraniyń (przijmnytych)", "stat.minecraft.damage_dealt_resisted": "Wyrzōndzō<PERSON><PERSON> zrani<PERSON>ń (ôdeprzitych)", "stat.minecraft.damage_resisted": "Ôdeprzitych zrani<PERSON>ń", "stat.minecraft.damage_taken": "Przijmnytych zraniyń", "stat.minecraft.deaths": "Liczba śmiyrci", "stat.minecraft.drop": "Wyciepanych rzeczy", "stat.minecraft.eat_cake_slice": "Zjedzōnych kōnskōw torty", "stat.minecraft.enchant_item": "Zaklyntych rzeczy", "stat.minecraft.fall_one_cm": "Śleciano", "stat.minecraft.fill_cauldron": "Nafolowanych keslōw", "stat.minecraft.fish_caught": "Chycōnych ryb", "stat.minecraft.fly_one_cm": "Prz<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.happy_ghast_one_cm": "Przerajtowano na uciesznym ghaście", "stat.minecraft.horse_one_cm": "Przerajtowano na kōniu", "stat.minecraft.inspect_dispenser": "Przeszukanych strzylŏczōw", "stat.minecraft.inspect_dropper": "Przeszukanych podŏwŏczōw", "stat.minecraft.inspect_hopper": "Przeszukanych lyjŏkōw", "stat.minecraft.interact_with_anvil": "Interakcyji ze ambosym", "stat.minecraft.interact_with_beacon": "Interakcyji ze laternōm", "stat.minecraft.interact_with_blast_furnace": "Interakcyji ze hamrym", "stat.minecraft.interact_with_brewingstand": "Interakcyji ze sztyndrym ku warzyniu", "stat.minecraft.interact_with_campfire": "Interakcyji ze fojerōm", "stat.minecraft.interact_with_cartography_table": "Intyrakcyji ze stołym kartografa", "stat.minecraft.interact_with_crafting_table": "Interakcyji ze fajlbankym", "stat.minecraft.interact_with_furnace": "Interakcyji ze żeleźniŏkym", "stat.minecraft.interact_with_grindstone": "Interakcyji ze bruskym", "stat.minecraft.interact_with_lectern": "Interakcyji ze pultym", "stat.minecraft.interact_with_loom": "Interakcyji ze krosnami", "stat.minecraft.interact_with_smithing_table": "Interakcyji ze stołym kowŏla", "stat.minecraft.interact_with_smoker": "Intyrakcyji ze wyndzŏkym", "stat.minecraft.interact_with_stonecutter": "Interakcyji ze krajzygōm", "stat.minecraft.jump": "Skokōw", "stat.minecraft.leave_game": "Wylezyń ze gry", "stat.minecraft.minecart_one_cm": "Przejechano we wagōnie", "stat.minecraft.mob_kills": "Ubitych stworzyń", "stat.minecraft.open_barrel": "Ôdewrzitych fasek", "stat.minecraft.open_chest": "Ôdewrzitych kastli", "stat.minecraft.open_enderchest": "Ôdewrzitych kastli <PERSON>u", "stat.minecraft.open_shulker_box": "Ôdewrzitych shulkerowych kastli", "stat.minecraft.pig_one_cm": "Przerajtowano na świni", "stat.minecraft.play_noteblock": "Zagranych <PERSON>lokōw", "stat.minecraft.play_record": "Zagranych dys<PERSON>ek", "stat.minecraft.play_time": "<PERSON>zas gry", "stat.minecraft.player_kills": "Ubitych grŏczy", "stat.minecraft.pot_flower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> flanc", "stat.minecraft.raid_trigger": "Rozfechtowanych anfali", "stat.minecraft.raid_win": "Ôdeprzitych anfali", "stat.minecraft.sleep_in_bed": "Przespanych nocy", "stat.minecraft.sneak_time": "Zakrŏdzōno", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.strider_one_cm": "Przerajtowano na smykŏczu", "stat.minecraft.swim_one_cm": "Przepłynyto", "stat.minecraft.talked_to_villager": "Przegŏdano ze wiyśniŏkami", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "stat.minecraft.time_since_death": "<PERSON><PERSON> p<PERSON><PERSON><PERSON>", "stat.minecraft.time_since_rest": "<PERSON><PERSON> ôd po<PERSON><PERSON><PERSON><PERSON> snu", "stat.minecraft.total_world_time": "Czas ze ôdewrzitym świŏtym", "stat.minecraft.traded_with_villager": "Tŏrgōw ze wiyśniŏkami", "stat.minecraft.trigger_trapped_chest": "Ôdewrzitych paść-kastli", "stat.minecraft.tune_noteblock": "Nastrojōnych klangblokōw", "stat.minecraft.use_cauldron": "Pobranyj wody ze kesla", "stat.minecraft.walk_on_water_one_cm": "Przeleziōno po wodzie", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.walk_under_water_one_cm": "Przeleziōno pod wodōm", "stat.mobsButton": "Stworzynia", "stat_type.minecraft.broken": "Zniszczō<PERSON><PERSON>", "stat_type.minecraft.crafted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.dropped": "Wyciepanych", "stat_type.minecraft.killed": "Ubiōł żeś %s %s", "stat_type.minecraft.killed.none": "Nigdyś niy ubiōł żŏdnego %s", "stat_type.minecraft.killed_by": "Byt %s ubiōł cie %s razōw", "stat_type.minecraft.killed_by.none": "Nigdyś niy ôstoł ubity ôd %s", "stat_type.minecraft.mined": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.picked_up": "Podniesiōnych", "stat_type.minecraft.used": "<PERSON><PERSON><PERSON>ych", "stats.none": "-", "structure_block.button.detect_size": "WYKRYJ", "structure_block.button.load": "ZALADUJ", "structure_block.button.save": "ZACHOWEJ", "structure_block.custom_data": "Miano niysztandardowyj etykety ze danymi", "structure_block.detect_size": "Wykryj wielgość a położynie struktury:", "structure_block.hover.corner": "Eka: %s", "structure_block.hover.data": "Dane: %s", "structure_block.hover.load": "Zaladuj: %s", "structure_block.hover.save": "Zachowej: %s", "structure_block.include_entities": "Zarachuj byty:", "structure_block.integrity": "Integralność a ziŏrno struktury", "structure_block.integrity.integrity": "<PERSON>te<PERSON><PERSON><PERSON><PERSON><PERSON> struktury", "structure_block.integrity.seed": "Ziŏrno struktury", "structure_block.invalid_structure_name": "<PERSON><PERSON>ne miano struktury: „%s”", "structure_block.load_not_found": "Struktura „%s” niyma dos<PERSON>ŏ", "structure_block.load_prepare": "Narychtowano położynie struktury „%s”", "structure_block.load_success": "Wczytano strukturã ze „%s”", "structure_block.mode.corner": "<PERSON><PERSON>", "structure_block.mode.data": "<PERSON>", "structure_block.mode.load": "Zaladuj", "structure_block.mode.save": "<PERSON><PERSON><PERSON>", "structure_block.mode_info.corner": "Tryb ek — znacznik położyniŏ a wielgości", "structure_block.mode_info.data": "<PERSON><PERSON> da<PERSON> — znacznik ô logice gry", "structure_block.mode_info.load": "Tryb ladowaniŏ — ladowanie ze dataje", "structure_block.mode_info.save": "Tryb zachowaniŏ — szkryft ku dataji", "structure_block.position": "Zglyndne położynie", "structure_block.position.x": "położynie zglyndne X", "structure_block.position.y": "położynie zglyndne Y", "structure_block.position.z": "położynie zglyndne Z", "structure_block.save_failure": "Niy idzie zach<PERSON>ć struktury „%s”", "structure_block.save_success": "Zachowano strukturã jako „%s”", "structure_block.show_air": "Pokazuj niywidzialne bloki:", "structure_block.show_boundingbox": "<PERSON><PERSON><PERSON><PERSON> struktur<PERSON>:", "structure_block.size": "<PERSON><PERSON><PERSON><PERSON><PERSON> struktury", "structure_block.size.x": "<PERSON><PERSON><PERSON><PERSON><PERSON> struktury (ôśka X)", "structure_block.size.y": "<PERSON><PERSON><PERSON><PERSON><PERSON> struktury (ôśka Y)", "structure_block.size.z": "<PERSON><PERSON><PERSON><PERSON><PERSON> struktury (ôśka Z)", "structure_block.size_failure": "Niy idzie znŏjś wielgość struktury. Przidej eki ze mianami struktury, kere pasujōm", "structure_block.size_success": "Znŏjdzono wielgość lŏ „%s”", "structure_block.strict": "Sztryng umiyszczynie:", "structure_block.structure_name": "<PERSON><PERSON> struktur<PERSON>", "subtitles.ambient.cave": "Niyprzijymny klang", "subtitles.ambient.sound": "Niyprzijymny klang", "subtitles.block.amethyst_block.chime": "Ametyst zwōni", "subtitles.block.amethyst_block.resonate": "Ametyst rozbrzmiywŏ", "subtitles.block.anvil.destroy": "<PERSON><PERSON> sie niszczy", "subtitles.block.anvil.land": "Ambos upŏdŏ", "subtitles.block.anvil.use": "<PERSON><PERSON> sie zu<PERSON>", "subtitles.block.barrel.close": "<PERSON>aska sie zaw<PERSON>", "subtitles.block.barrel.open": "<PERSON><PERSON><PERSON> sie <PERSON>", "subtitles.block.beacon.activate": "<PERSON>na sie zał<PERSON>", "subtitles.block.beacon.ambient": "<PERSON><PERSON> buczy", "subtitles.block.beacon.deactivate": "<PERSON>na sie w<PERSON>ł<PERSON>", "subtitles.block.beacon.power_select": "Ôbiyranie siyły we laternie", "subtitles.block.beehive.drip": "<PERSON><PERSON><PERSON> kid<PERSON>", "subtitles.block.beehive.enter": "<PERSON>a w<PERSON>uje ku hulowi", "subtitles.block.beehive.exit": "Bina wylatuje ze hula", "subtitles.block.beehive.shear": "S<PERSON>ry zeszkrobujōm", "subtitles.block.beehive.work": "<PERSON><PERSON> robi<PERSON>", "subtitles.block.bell.resonate": "Zwōn rozbrzmiywŏ", "subtitles.block.bell.use": "Zwōn zwōni", "subtitles.block.big_dripleaf.tilt_down": "Czampiōnek ôpadŏ", "subtitles.block.big_dripleaf.tilt_up": "Czampiōnek sie d<PERSON>", "subtitles.block.blastfurnace.fire_crackle": "<PERSON><PERSON>", "subtitles.block.brewing_stand.brew": "Sztynder ku warzyniu blamborzi", "subtitles.block.bubble_column.bubble_pop": "Bōmbelki p<PERSON>", "subtitles.block.bubble_column.upwards_ambient": "Bōmbelki płynōm", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.whirlpool_ambient": "Bōmbelki fyr<PERSON>", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.button.click": "Knefel k<PERSON>", "subtitles.block.cake.add_candle": "Torta plaskŏ", "subtitles.block.campfire.crackle": "Fojera praskŏ", "subtitles.block.candle.crackle": "Świyczka praskŏ", "subtitles.block.candle.extinguish": "Świyczka gaśnie", "subtitles.block.chest.close": "<PERSON><PERSON><PERSON> sie zaw<PERSON>", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.open": "<PERSON><PERSON><PERSON> sie <PERSON>", "subtitles.block.chorus_flower.death": "Kwiŏt refrynice ôbumiyrŏ", "subtitles.block.chorus_flower.grow": "Kwiŏt refrynice rośnie", "subtitles.block.comparator.click": "Kōmparatōr <PERSON>", "subtitles.block.composter.empty": "Ôprōżnianie kōmpostera", "subtitles.block.composter.fill": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.composter.ready": "Kōmposter kōmpostuje", "subtitles.block.conduit.activate": "Lajtōng sie zał<PERSON>ncz<PERSON>", "subtitles.block.conduit.ambient": "Lajtōng puls<PERSON>yruje", "subtitles.block.conduit.attack.target": "Lajtōng napŏdŏ", "subtitles.block.conduit.deactivate": "Lajtōng sie wył<PERSON>", "subtitles.block.copper_bulb.turn_off": "Miedziannŏ byrna sie w<PERSON>", "subtitles.block.copper_bulb.turn_on": "Miedziannŏ byrna sie zał<PERSON>", "subtitles.block.copper_trapdoor.close": "Szłapa sie zawiyrŏ", "subtitles.block.copper_trapdoor.open": "Szłapa sie ô<PERSON>wiyr<PERSON>", "subtitles.block.crafter.craft": "Wytwŏrzŏcz wytwŏrzŏ", "subtitles.block.crafter.fail": "Wytwŏrzŏcz niy zafōngowoł", "subtitles.block.creaking_heart.hurt": "<PERSON><PERSON><PERSON> grzip<PERSON> br<PERSON>", "subtitles.block.creaking_heart.idle": "Niyprzijymny klang", "subtitles.block.creaking_heart.spawn": "<PERSON><PERSON><PERSON> grz<PERSON>a sie budzi", "subtitles.block.deadbush.idle": "<PERSON><PERSON>", "subtitles.block.decorated_pot.insert": "Wypołnianie prziôzdobiōnego bōnclŏka", "subtitles.block.decorated_pot.insert_fail": "Prziôzdobiōny bōnclŏk sie kiwie", "subtitles.block.decorated_pot.shatter": "Prziôzdobiōny bōnclŏk sie roztrzaskuje", "subtitles.block.dispenser.dispense": "Strzylŏcz wyciepuje rzecz", "subtitles.block.dispenser.fail": "Strzylŏcz niy zafōngowoł", "subtitles.block.door.toggle": "Dźwiyrze grzipiōm", "subtitles.block.dried_ghast.ambient": "Wyschnyty ghast ciynżko dych<PERSON>", "subtitles.block.dried_ghast.ambient_water": "W<PERSON><PERSON><PERSON><PERSON> ghast sie nawadniŏ", "subtitles.block.dried_ghast.place_in_water": "Wyschnyty ghast namak<PERSON>", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON><PERSON><PERSON> ghast sie czuje lepij", "subtitles.block.dry_grass.ambient": "<PERSON><PERSON><PERSON><PERSON> klangi", "subtitles.block.enchantment_table.use": "Użyto stołu z<PERSON>ŏcza", "subtitles.block.end_portal.spawn": "Portŏl End<PERSON> sie <PERSON>", "subtitles.block.end_portal_frame.fill": "Wrażanie ślypia Endu", "subtitles.block.eyeblossom.close": "Ślypiŏczka sie zawiyrŏ", "subtitles.block.eyeblossom.idle": "Ślypioczka szkamrŏ", "subtitles.block.eyeblossom.open": "Ślypiŏczka sie ô<PERSON>wiyrŏ", "subtitles.block.fence_gate.toggle": "<PERSON>rō<PERSON><PERSON> grz<PERSON>", "subtitles.block.fire.ambient": "Ôgyń praskŏ", "subtitles.block.fire.extinguish": "Ôgyń gaśnie", "subtitles.block.firefly_bush.idle": "Jōniki ciyrczōm", "subtitles.block.frogspawn.hatch": "Gowŏcz sie wylyng<PERSON>", "subtitles.block.furnace.fire_crackle": "Żeleźniŏk praskŏ", "subtitles.block.generic.break": "Zniszczōno blok", "subtitles.block.generic.fall": "Coś ślatuje na blok", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON>", "subtitles.block.generic.hit": "Niszczynie bloku", "subtitles.block.generic.place": "Stŏwianie bloku", "subtitles.block.grindstone.use": "Używanie bruska", "subtitles.block.growing_plant.crop": "Podcinanie flance", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> sie kolybie", "subtitles.block.honey_block.slide": "Sjyżdżanie po bloku miodu", "subtitles.block.iron_trapdoor.close": "Szłapa sie zawiyrŏ", "subtitles.block.iron_trapdoor.open": "Szłapa sie ô<PERSON>wiyr<PERSON>", "subtitles.block.lava.ambient": "<PERSON><PERSON>", "subtitles.block.lava.extinguish": "<PERSON><PERSON>", "subtitles.block.lever.click": "<PERSON><PERSON><PERSON>", "subtitles.block.note_block.note": "Klangblok grŏ", "subtitles.block.pale_hanging_moss.idle": "Niyprzijymny klang", "subtitles.block.piston.move": "Tłok sie ruszŏ", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> kid<PERSON>", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON><PERSON> kid<PERSON> ku keslowi", "subtitles.block.pointed_dripstone.drip_water": "<PERSON>oda kid<PERSON>", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Woda kidŏ ku keslowi", "subtitles.block.pointed_dripstone.land": "Stalaktyt ślatuje", "subtitles.block.portal.ambient": "Port<PERSON><PERSON> huczy", "subtitles.block.portal.travel": "Klang portŏlu słabnie", "subtitles.block.portal.trigger": "Klang portŏlu sie zmŏcniŏ", "subtitles.block.pressure_plate.click": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.pumpkin.carve": "<PERSON><PERSON><PERSON> wykrŏwajōm", "subtitles.block.redstone_torch.burnout": "Redstone'owŏ fakla s<PERSON>y", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON> h<PERSON>zy", "subtitles.block.respawn_anchor.charge": "<PERSON><PERSON> sie laduje", "subtitles.block.respawn_anchor.deplete": "An<PERSON> sie wyczyrpuje", "subtitles.block.respawn_anchor.set_spawn": "<PERSON><PERSON> pōnkt ôdrŏdzaniŏ", "subtitles.block.sand.idle": "Piŏskowe klangi", "subtitles.block.sand.wind": "<PERSON><PERSON><PERSON><PERSON> klangi", "subtitles.block.sculk.charge": "Sculk blamborzi", "subtitles.block.sculk.spread": "Sculk sie szyrzi", "subtitles.block.sculk_catalyst.bloom": "Sculkowy katalizatōr rozkwitŏ", "subtitles.block.sculk_sensor.clicking": "Sculkowy synsōr napoczynŏ klikać", "subtitles.block.sculk_sensor.clicking_stop": "Sculkowy synsōr ustŏwŏ klikać", "subtitles.block.sculk_shrieker.shriek": "Sculkowy krzykŏcz krziczy", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> sie zaw<PERSON>", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> sie <PERSON>", "subtitles.block.sign.waxed_interact_fail": "Znak sie kolybie", "subtitles.block.smithing_table.use": "Używanie stołu kowŏla", "subtitles.block.smoker.smoke": "Wyndzŏk wyńdzi", "subtitles.block.sniffer_egg.crack": "Jajco sznupŏcza pynkŏ", "subtitles.block.sniffer_egg.hatch": "Jajco sznupŏcza sie wykluwŏ", "subtitles.block.sniffer_egg.plop": "Sznupŏcz niysie jajco", "subtitles.block.sponge.absorb": "Gōmbka wsiōnkŏ", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON><PERSON>", "subtitles.block.trapdoor.close": "Szłapa sie zawiyrŏ", "subtitles.block.trapdoor.open": "Szłapa sie ô<PERSON>wiyr<PERSON>", "subtitles.block.trapdoor.toggle": "Szłapa grzipi", "subtitles.block.trial_spawner.about_to_spawn_item": "Swoływŏcz ôbierŏ rzecz", "subtitles.block.trial_spawner.ambient": "Swoływŏcz prōb praskŏ", "subtitles.block.trial_spawner.ambient_charged": "Swoływŏcz praskŏ", "subtitles.block.trial_spawner.ambient_ominous": "Lagramynckie praskanie", "subtitles.block.trial_spawner.charge_activate": "Przepowiydź ôbjimuje swoływŏcz", "subtitles.block.trial_spawner.close_shutter": "Swoływŏcz prōb sie zawiyrŏ", "subtitles.block.trial_spawner.detect_player": "Swoływŏcz prōb sie laduje", "subtitles.block.trial_spawner.eject_item": "Swoływŏcz prōb wyciepuje rzeczy", "subtitles.block.trial_spawner.ominous_activate": "Wołŏcz prōb pochłōniŏ przepowiydź", "subtitles.block.trial_spawner.open_shutter": "Swoływŏcz prōb sie ô<PERSON>wiyr<PERSON>", "subtitles.block.trial_spawner.spawn_item": "Lagramynckŏ rzecz wypŏdŏ", "subtitles.block.trial_spawner.spawn_item_begin": "Lagramynckŏ rzecz sie pokŏzuje", "subtitles.block.trial_spawner.spawn_mob": "Swoływŏcz swołuje stworzynie", "subtitles.block.tripwire.attach": "<PERSON><PERSON> sie z<PERSON>", "subtitles.block.tripwire.click": "<PERSON><PERSON>", "subtitles.block.tripwire.detach": "<PERSON><PERSON> sie <PERSON>", "subtitles.block.vault.activate": "Szac sie zapŏlŏ", "subtitles.block.vault.ambient": "Szac praskŏ", "subtitles.block.vault.close_shutter": "Szac sie zawiyr<PERSON>", "subtitles.block.vault.deactivate": "Szac gaśnie", "subtitles.block.vault.eject_item": "Szac wyciepuje rzecz", "subtitles.block.vault.insert_item": "Szac sie ôdszperowuje", "subtitles.block.vault.insert_item_fail": "Szac ôdciepuje rzecz", "subtitles.block.vault.open_shutter": "Szac sie ô<PERSON>wi<PERSON>", "subtitles.block.vault.reject_rewarded_player": "Szac ôdciepuje grŏcza", "subtitles.block.water.ambient": "Woda płynie", "subtitles.block.wet_sponge.dries": "Gōmbka wysychŏ", "subtitles.chiseled_bookshelf.insert": "Wrażanie ksiōnżki", "subtitles.chiseled_bookshelf.insert_enchanted": "Wrażanie zaklyntej ksiōnżki", "subtitles.chiseled_bookshelf.take": "Wyrażynie ksiōnżki", "subtitles.chiseled_bookshelf.take_enchanted": "Wyrażynie zaklyntej ksiōnżki", "subtitles.enchant.thorns.hit": "Sztachle żgajōm", "subtitles.entity.allay.ambient_with_item": "Przŏciuch szukŏ", "subtitles.entity.allay.ambient_without_item": "Przŏciuch pragli", "subtitles.entity.allay.death": "Przŏciuch umiyrŏ", "subtitles.entity.allay.hurt": "Przŏciuch sie utrŏpiŏ", "subtitles.entity.allay.item_given": "Przŏciuch chichrŏ", "subtitles.entity.allay.item_taken": "Przŏciuch przaje", "subtitles.entity.allay.item_thrown": "Przŏciuch ciepie rzecz", "subtitles.entity.armadillo.ambient": "Ôbryncznik brōnczy", "subtitles.entity.armadillo.brush": "Pucowanie ôbryncznika", "subtitles.entity.armadillo.death": "Ôbryncznik umiyrŏ", "subtitles.entity.armadillo.eat": "Ôbryncznik jy", "subtitles.entity.armadillo.hurt": "Ôbryncznik sie u<PERSON>", "subtitles.entity.armadillo.hurt_reduced": "Ôbryncznik sie brōni", "subtitles.entity.armadillo.land": "Ôbryncznik upŏdŏ", "subtitles.entity.armadillo.peek": "Ôbryncznik spoziyrŏ", "subtitles.entity.armadillo.roll": "Ôbryncznik sie swijŏ", "subtitles.entity.armadillo.scute_drop": "Ôbryncznik ście<PERSON>je szupã", "subtitles.entity.armadillo.unroll_finish": "Ôbryncznik sie rozwij<PERSON>", "subtitles.entity.armadillo.unroll_start": "Ôbryncznik spoziyrŏ", "subtitles.entity.armor_stand.fall": "<PERSON><PERSON>", "subtitles.entity.arrow.hit": "Strzała sie wbijŏ", "subtitles.entity.arrow.hit_player": "Strzała uderzŏ grŏcza", "subtitles.entity.arrow.shoot": "<PERSON><PERSON><PERSON> ze łuku", "subtitles.entity.axolotl.attack": "Aksolotl <PERSON>", "subtitles.entity.axolotl.death": "Aksolotl um<PERSON>", "subtitles.entity.axolotl.hurt": "<PERSON>ks<PERSON>tl sie utr<PERSON>", "subtitles.entity.axolotl.idle_air": "Aksolotl d<PERSON>yrgoli", "subtitles.entity.axolotl.idle_water": "Aksolotl d<PERSON>yrgoli", "subtitles.entity.axolotl.splash": "Aksolotl ciŏplŏ", "subtitles.entity.axolotl.swim": "Aksolotl pływŏ", "subtitles.entity.bat.ambient": "<PERSON>jder<PERSON><PERSON>", "subtitles.entity.bat.death": "Flyjdermaus um<PERSON>", "subtitles.entity.bat.hurt": "Flyjdermaus sie u<PERSON>", "subtitles.entity.bat.takeoff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bee.ambient": "<PERSON><PERSON>", "subtitles.entity.bee.death": "<PERSON><PERSON>", "subtitles.entity.bee.hurt": "<PERSON><PERSON> sie <PERSON>", "subtitles.entity.bee.loop": "<PERSON><PERSON>", "subtitles.entity.bee.loop_aggressive": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bee.pollinate": "<PERSON>a bzyczy szpasownie", "subtitles.entity.bee.sting": "<PERSON><PERSON>", "subtitles.entity.blaze.ambient": "Flamŏk dychŏ", "subtitles.entity.blaze.burn": "Flamŏk praskŏ", "subtitles.entity.blaze.death": "Flamŏk umiyrŏ", "subtitles.entity.blaze.hurt": "Flamŏk sie utrŏpiŏ", "subtitles.entity.blaze.shoot": "Flamŏk strzylŏ", "subtitles.entity.boat.paddle_land": "Wiosłowanie", "subtitles.entity.boat.paddle_water": "Wiosłowanie", "subtitles.entity.bogged.ambient": "Bagyniŏk klekotŏ", "subtitles.entity.bogged.death": "Bagyniŏk umiyrŏ", "subtitles.entity.bogged.hurt": "Bagyniŏk sie utrŏpiŏ", "subtitles.entity.breeze.charge": "Wiytrzik sie rychtuje", "subtitles.entity.breeze.death": "Wiytrzik umiyrŏ", "subtitles.entity.breeze.deflect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.hurt": "Wiytrzik sie utrŏpiŏ", "subtitles.entity.breeze.idle_air": "Wiytrzik leci", "subtitles.entity.breeze.idle_ground": "Wiytrzik wyrczy", "subtitles.entity.breeze.inhale": "Wiytrzik wdychŏ", "subtitles.entity.breeze.jump": "Wiytrzik skŏkŏ", "subtitles.entity.breeze.land": "Wiytrzik upŏdŏ", "subtitles.entity.breeze.shoot": "Wiytrzik strzylŏ", "subtitles.entity.breeze.slide": "Wiytrzik kiełzŏ", "subtitles.entity.breeze.whirl": "Wiytrzik fyrtŏ", "subtitles.entity.breeze.wind_burst": "Wietrzny ladōnek wybuchŏ", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON> jy", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON> si<PERSON>", "subtitles.entity.camel.saddle": "Zakłŏdanie zatla", "subtitles.entity.camel.sit": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.stand": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.step_sand": "<PERSON><PERSON><PERSON> łazi po piŏsku", "subtitles.entity.cat.ambient": "<PERSON><PERSON>", "subtitles.entity.cat.beg_for_food": "Kot prŏsi", "subtitles.entity.cat.death": "Kot umiyr<PERSON>", "subtitles.entity.cat.eat": "<PERSON><PERSON> jy", "subtitles.entity.cat.hiss": "Ko<PERSON>y", "subtitles.entity.cat.hurt": "<PERSON><PERSON> sie u<PERSON>", "subtitles.entity.cat.purr": "<PERSON><PERSON>", "subtitles.entity.chicken.ambient": "<PERSON><PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON>", "subtitles.entity.chicken.egg": "<PERSON><PERSON> niys<PERSON> j<PERSON>", "subtitles.entity.chicken.hurt": "<PERSON><PERSON> sie <PERSON>", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON> umi<PERSON>", "subtitles.entity.cod.flop": "Dorsz ciŏplŏ", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON> sie <PERSON>", "subtitles.entity.cow.ambient": "Krŏ<PERSON> muczy", "subtitles.entity.cow.death": "Krŏwa umiyrŏ", "subtitles.entity.cow.hurt": "Krŏwa sie utrŏpiŏ", "subtitles.entity.cow.milk": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.activate": "<PERSON>rz<PERSON><PERSON><PERSON> wachuje", "subtitles.entity.creaking.ambient": "Grz<PERSON><PERSON><PERSON> grzipi", "subtitles.entity.creaking.attack": "Grzipŏcz napŏdŏ", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON> sie uspokajŏ", "subtitles.entity.creaking.death": "Grz<PERSON><PERSON><PERSON> umiyrŏ", "subtitles.entity.creaking.freeze": "Grzipŏcz stŏwŏ", "subtitles.entity.creaking.spawn": "Grz<PERSON><PERSON>cz ôżywŏ", "subtitles.entity.creaking.sway": "<PERSON><PERSON><PERSON><PERSON><PERSON> sie chrōni", "subtitles.entity.creaking.twitch": "Grzipŏcz dyrgotŏ", "subtitles.entity.creaking.unfreeze": "Grzipŏcz idzie", "subtitles.entity.creeper.death": "Creeper umiyrŏ", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> sie u<PERSON>", "subtitles.entity.creeper.primed": "Creeper syczy", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON> d<PERSON>goli", "subtitles.entity.dolphin.ambient_water": "Delfin gwizdŏ", "subtitles.entity.dolphin.attack": "Delfin napŏdŏ", "subtitles.entity.dolphin.death": "<PERSON><PERSON>", "subtitles.entity.dolphin.eat": "<PERSON><PERSON> jy", "subtitles.entity.dolphin.hurt": "Delfin sie utr<PERSON>", "subtitles.entity.dolphin.jump": "Delfin skŏkŏ", "subtitles.entity.dolphin.play": "<PERSON><PERSON> sie bawi", "subtitles.entity.dolphin.splash": "Delfin ciŏplŏ", "subtitles.entity.dolphin.swim": "Delfin pływŏ", "subtitles.entity.donkey.ambient": "<PERSON><PERSON><PERSON> rż<PERSON>", "subtitles.entity.donkey.angry": "<PERSON><PERSON><PERSON> rż<PERSON>", "subtitles.entity.donkey.chest": "Zakłŏdanie kastle na yjzla", "subtitles.entity.donkey.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.eat": "<PERSON><PERSON><PERSON> jy", "subtitles.entity.donkey.hurt": "<PERSON><PERSON><PERSON> sie <PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient": "Utopek chraplŏ", "subtitles.entity.drowned.ambient_water": "Utopek chraplŏ", "subtitles.entity.drowned.death": "Utopek umiyrŏ", "subtitles.entity.drowned.hurt": "Utopek sie utr<PERSON>ŏ", "subtitles.entity.drowned.shoot": "Utopek ciepie trzizymbym", "subtitles.entity.drowned.step": "Utopek kroczy", "subtitles.entity.drowned.swim": "Utopek pływŏ", "subtitles.entity.egg.throw": "Jajco leci", "subtitles.entity.elder_guardian.ambient": "Chańdŏwny wachtyrz stynkŏ", "subtitles.entity.elder_guardian.ambient_land": "Chańdŏwny wachtyrz sie trzep<PERSON>ze", "subtitles.entity.elder_guardian.curse": "Chańdŏwny wachtyrz przeklinŏ", "subtitles.entity.elder_guardian.death": "Chańdŏwny wachtyrz umiyrŏ", "subtitles.entity.elder_guardian.flop": "Chańdŏwny wachtyrz ciŏplŏ", "subtitles.entity.elder_guardian.hurt": "Chańdŏwny wachtyrz sie utrŏpiŏ", "subtitles.entity.ender_dragon.ambient": "<PERSON>ach <PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON>", "subtitles.entity.ender_dragon.flap": "Drach trzepocze ze krzideł", "subtitles.entity.ender_dragon.growl": "Drach wyrczy", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON> sie <PERSON>", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.ender_eye.death": "<PERSON><PERSON><PERSON> Endu <PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> leci", "subtitles.entity.enderman.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> sie u<PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON><PERSON><PERSON> sie roz<PERSON>", "subtitles.entity.enderman.teleport": "<PERSON>ermōn sie teleportuje", "subtitles.entity.endermite.ambient": "<PERSON><PERSON><PERSON> drept<PERSON>", "subtitles.entity.endermite.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON> sie <PERSON>", "subtitles.entity.evoker.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fajeruje", "subtitles.entity.evoker.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sie u<PERSON>", "subtitles.entity.evoker.prepare_attack": "Gu<PERSON><PERSON>ŏrz rychtuje cauber napŏdu", "subtitles.entity.evoker.prepare_summon": "Guślŏz rychtuje cauber swoływaniŏ", "subtitles.entity.evoker.prepare_wololo": "Guślŏrz rychtuje cauber urzeczyniŏ", "subtitles.entity.evoker_fangs.attack": "Gryzŏk sie zatrzaskuje", "subtitles.entity.experience_orb.pickup": "Zdobywanie doświŏdczyniŏ", "subtitles.entity.firework_rocket.blast": "Fojerwerk wybuchŏ", "subtitles.entity.firework_rocket.launch": "Ôdpŏlanie fojerwerka", "subtitles.entity.firework_rocket.twinkle": "Fojerwerk skrzy", "subtitles.entity.fish.swim": "Ciŏplanie", "subtitles.entity.fishing_bobber.retrieve": "Wyciōnganie szpōnta", "subtitles.entity.fishing_bobber.splash": "Szpōnt ciŏplŏ", "subtitles.entity.fishing_bobber.throw": "<PERSON>ie<PERSON><PERSON> s<PERSON>", "subtitles.entity.fox.aggro": "Liszka sie gorszy", "subtitles.entity.fox.ambient": "Liszka piskŏ", "subtitles.entity.fox.bite": "Liszka gryzie", "subtitles.entity.fox.death": "Liszka umiyrŏ", "subtitles.entity.fox.eat": "Liszka jy", "subtitles.entity.fox.hurt": "Liszka sie utr<PERSON>", "subtitles.entity.fox.screech": "Liszka piskŏ", "subtitles.entity.fox.sleep": "Liszka chyrczy", "subtitles.entity.fox.sniff": "Liszka wōniŏ", "subtitles.entity.fox.spit": "Liszka pluje", "subtitles.entity.fox.teleport": "Liszka sie teleportuje", "subtitles.entity.frog.ambient": "Rapitołza żegocze", "subtitles.entity.frog.death": "Rapitołza umiyrŏ", "subtitles.entity.frog.eat": "Rapitołza jy", "subtitles.entity.frog.hurt": "Rapitołza sie utrŏpiŏ", "subtitles.entity.frog.lay_spawn": "Rapitołza skłŏdŏ jajca", "subtitles.entity.frog.long_jump": "Rapitołza skŏkŏ", "subtitles.entity.generic.big_fall": "<PERSON><PERSON>", "subtitles.entity.generic.burn": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.drink": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.eat": "Jedzynie", "subtitles.entity.generic.explode": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.extinguish_fire": "Ôgyń gaśnie", "subtitles.entity.generic.hurt": "Coś sie utr<PERSON>", "subtitles.entity.generic.small_fall": "<PERSON><PERSON> sie potyk<PERSON>", "subtitles.entity.generic.splash": "Ciŏplanie", "subtitles.entity.generic.swim": "Pływanie", "subtitles.entity.generic.wind_burst": "Wietrzny ladōnek wybuchŏ", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> umi<PERSON>", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> sie <PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.ghastling.ambient": "Ghastlik gwŏrzy", "subtitles.entity.ghastling.death": "Ghastlik umiyrŏ", "subtitles.entity.ghastling.hurt": "Ghastlik sie utr<PERSON>ŏ", "subtitles.entity.ghastling.spawn": "Ghastlik sie pokazuje", "subtitles.entity.glow_item_frame.add_item": "Wypołnianie rōmy", "subtitles.entity.glow_item_frame.break": "Nisz<PERSON><PERSON><PERSON> b<PERSON>dr<PERSON>my", "subtitles.entity.glow_item_frame.place": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.glow_item_frame.remove_item": "Ôprōżnianie blyn<PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.rotate_item": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_squid.ambient": "Świycōnŏ tintynfisza pływŏ", "subtitles.entity.glow_squid.death": "Ś<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tintynfisza umiyrŏ", "subtitles.entity.glow_squid.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tintynfisza sie utr<PERSON>", "subtitles.entity.glow_squid.squirt": "Ś<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tintynfisza strzylŏ ze tinty", "subtitles.entity.goat.ambient": "Ciga beczy", "subtitles.entity.goat.death": "Ciga umiyrŏ", "subtitles.entity.goat.eat": "Ciga jy", "subtitles.entity.goat.horn_break": "Cigowy rōg sie ułōmuje", "subtitles.entity.goat.hurt": "Ciga sie utrŏpiŏ", "subtitles.entity.goat.long_jump": "Ciga skŏkŏ", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON><PERSON> cigi", "subtitles.entity.goat.prepare_ram": "Ciga stōmpŏ", "subtitles.entity.goat.ram_impact": "Ciga angrajfuje", "subtitles.entity.goat.screaming.ambient": "Ciga ryczy", "subtitles.entity.goat.step": "Ciga kroczy", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.guardian.ambient_land": "<PERSON>ach<PERSON>rz sie trzep<PERSON>ze", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON><PERSON>zy<PERSON>", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.guardian.flop": "Wachtyrz ciŏplŏ", "subtitles.entity.guardian.hurt": "<PERSON>ach<PERSON>rz sie utr<PERSON>", "subtitles.entity.happy_ghast.ambient": "Uciesz<PERSON> ghast dudli", "subtitles.entity.happy_ghast.death": "Ucieszny ghast umiyr<PERSON>", "subtitles.entity.happy_ghast.equip": "Zakłŏdanie gyszira", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON><PERSON> ghast je fertich", "subtitles.entity.happy_ghast.harness_goggles_up": "Ucieszny ghast sztopuje", "subtitles.entity.happy_ghast.hurt": "<PERSON>cies<PERSON><PERSON> ghast sie u<PERSON>", "subtitles.entity.happy_ghast.unequip": "Syjmowanie gysziru", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> w<PERSON>", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "Hoglin przemiyniŏ sie we zoglina", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> sie <PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> sie c<PERSON>fie", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.ambient": "Kōń rży", "subtitles.entity.horse.angry": "Kōń rży", "subtitles.entity.horse.armor": "Zakłŏdanie kōńskigo pancra", "subtitles.entity.horse.breathe": "<PERSON>ō<PERSON> d<PERSON>", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.eat": "Kōń jy", "subtitles.entity.horse.gallop": "<PERSON><PERSON><PERSON> ka<PERSON>", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON> sie utr<PERSON>", "subtitles.entity.horse.jump": "Kōń skŏkŏ", "subtitles.entity.horse.saddle": "Zakłŏdanie zatla", "subtitles.entity.husk.ambient": "Suchlŏk stynkŏ", "subtitles.entity.husk.converted_to_zombie": "Suchlŏk przemiyniŏ sie we umarlŏka", "subtitles.entity.husk.death": "Suchlŏk umiyrŏ", "subtitles.entity.husk.hurt": "Suchlŏk sie utrŏpiŏ", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON><PERSON><PERSON> caub<PERSON>je", "subtitles.entity.illusioner.death": "Ku<PERSON><PERSON><PERSON><PERSON> um<PERSON>", "subtitles.entity.illusioner.hurt": "Ku<PERSON><PERSON><PERSON><PERSON> sie u<PERSON>", "subtitles.entity.illusioner.mirror_move": "Kugl<PERSON>rz sie przemiyszczŏ", "subtitles.entity.illusioner.prepare_blindness": "Kuglŏrz rychtuje cauber ôślypiyniŏ", "subtitles.entity.illusioner.prepare_mirror": "Kuglŏrz rychtuje cauber ôdzwiyrciydleniŏ", "subtitles.entity.iron_golem.attack": "Żelazny golym nap<PERSON>dŏ", "subtitles.entity.iron_golem.damage": "Żelazny golym trzaskŏ", "subtitles.entity.iron_golem.death": "Żelazny golym umiyrŏ", "subtitles.entity.iron_golem.hurt": "Żelazny golym sie u<PERSON>", "subtitles.entity.iron_golem.repair": "Sprŏwianie żelaznego golyma", "subtitles.entity.item.break": "Rzecz sie niszczy", "subtitles.entity.item.pickup": "Zebiyranie rzeczy", "subtitles.entity.item_frame.add_item": "Wypołnianie rōmy", "subtitles.entity.item_frame.break": "Niszczynie rōmy", "subtitles.entity.item_frame.place": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.remove_item": "Ôprōżnianie rōmy", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.leash_knot.break": "Knołtyl ôd lajny sie urywŏ", "subtitles.entity.leash_knot.place": "Ôbwiōnzywanie knoł<PERSON>la ôd lajny", "subtitles.entity.lightning_bolt.impact": "Blic uderzŏ", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON><PERSON>", "subtitles.entity.llama.ambient": "<PERSON>", "subtitles.entity.llama.angry": "<PERSON>", "subtitles.entity.llama.chest": "Zakłŏdanie kastle na lamã", "subtitles.entity.llama.death": "<PERSON>", "subtitles.entity.llama.eat": "<PERSON> jy", "subtitles.entity.llama.hurt": "<PERSON> si<PERSON>", "subtitles.entity.llama.spit": "<PERSON>", "subtitles.entity.llama.step": "<PERSON>", "subtitles.entity.llama.swag": "Rychtowanie lamy ku szpanowi", "subtitles.entity.magma_cube.death": "Magmŏk umiyrŏ", "subtitles.entity.magma_cube.hurt": "Magmŏk sie utr<PERSON>piŏ", "subtitles.entity.magma_cube.squish": "Magmŏk plaskŏ", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON><PERSON> zw<PERSON>ni", "subtitles.entity.minecart.inside_underwater": "Wagōn zwōni pod wodōm", "subtitles.entity.minecart.riding": "<PERSON><PERSON><PERSON><PERSON> sie kul<PERSON>", "subtitles.entity.mooshroom.convert": "Mooshroom sie przemiyniŏ", "subtitles.entity.mooshroom.eat": "Mooshroom jy", "subtitles.entity.mooshroom.milk": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mooshroom.suspicious_milk": "Podyj<PERSON><PERSON><PERSON><PERSON> do<PERSON> m<PERSON>", "subtitles.entity.mule.ambient": "Maultiyr rży", "subtitles.entity.mule.angry": "Maultiyr rży", "subtitles.entity.mule.chest": "Zakłŏdanie kastle na maultiyra", "subtitles.entity.mule.death": "Maultiyr um<PERSON>yr<PERSON>", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON><PERSON> jy", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON><PERSON> sie u<PERSON>", "subtitles.entity.mule.jump": "Maultiyr skŏkŏ", "subtitles.entity.painting.break": "Ôbrŏz sie niszczy", "subtitles.entity.painting.place": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.panda.aggressive_ambient": "Panda fukŏ", "subtitles.entity.panda.ambient": "Panda z<PERSON>", "subtitles.entity.panda.bite": "Panda gryzie", "subtitles.entity.panda.cant_breed": "Panda kwikŏ", "subtitles.entity.panda.death": "Panda umiyrŏ", "subtitles.entity.panda.eat": "Panda jy", "subtitles.entity.panda.hurt": "Panda sie u<PERSON>", "subtitles.entity.panda.pre_sneeze": "Pandã świyrzbi nos", "subtitles.entity.panda.sneeze": "Panda kichŏ", "subtitles.entity.panda.step": "Panda kroczy", "subtitles.entity.panda.worried_ambient": "Panda szkōmlŏ", "subtitles.entity.parrot.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON> jy", "subtitles.entity.parrot.fly": "Papagaj trzepocze ze krzideł", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON> sie <PERSON>", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON>j <PERSON>", "subtitles.entity.parrot.imitate.creaking": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.creeper": "Papagaj <PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.ender_dragon": "Papagaj ryczy", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON><PERSON> dre<PERSON>", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.hoglin": "Papagaj w<PERSON>czy", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.magma_cube": "Papagaj p<PERSON>k<PERSON>", "subtitles.entity.parrot.imitate.phantom": "Papagaj ciyrczy", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.silverfish": "Papagaj <PERSON>", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.slime": "Papagaj p<PERSON>k<PERSON>", "subtitles.entity.parrot.imitate.spider": "Papagaj <PERSON>", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON>j <PERSON>", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON> sie gorszy", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zoglin": "Papagaj w<PERSON>czy", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON>", "subtitles.entity.phantom.ambient": "<PERSON><PERSON>", "subtitles.entity.phantom.bite": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.phantom.death": "<PERSON><PERSON>", "subtitles.entity.phantom.flap": "Mora trzepocze ze krzideł", "subtitles.entity.phantom.hurt": "<PERSON><PERSON> sie <PERSON>", "subtitles.entity.phantom.swoop": "<PERSON><PERSON>", "subtitles.entity.pig.ambient": "Ś<PERSON><PERSON> ch<PERSON>", "subtitles.entity.pig.death": "Ś<PERSON><PERSON> um<PERSON>", "subtitles.entity.pig.hurt": "Świnia sie u<PERSON>", "subtitles.entity.pig.saddle": "Zakłŏdanie zatla", "subtitles.entity.piglin.admiring_item": "<PERSON>lin podziwiŏ rzecz", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> chrumk<PERSON>", "subtitles.entity.piglin.angry": "<PERSON><PERSON> chrumk<PERSON> ściykle", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> fajer<PERSON>je", "subtitles.entity.piglin.converted_to_zombified": "Piglin przemiyniŏ sie we piglina-umarlŏka", "subtitles.entity.piglin.death": "<PERSON><PERSON>", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> sie u<PERSON>", "subtitles.entity.piglin.jealous": "Piglin chrumkŏ ze zŏwiściōm", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> sie cŏfie", "subtitles.entity.piglin.step": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.piglin_brute.ambient": "Piglin-doskwiyrnik chrumkŏ", "subtitles.entity.piglin_brute.angry": "Piglin-doskwiyrnik chrumkŏ ściykle", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin-doskwiyrnik przemiyniŏ sie we piglina-umarlŏka", "subtitles.entity.piglin_brute.death": "Piglin-doskwiyrnik umiyrŏ", "subtitles.entity.piglin_brute.hurt": "Piglin-doskwiyrnik sie utrŏpiŏ", "subtitles.entity.piglin_brute.step": "Piglin-doskwiyrnik kroczy", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.celebrate": "Raubiyrz fajeruje", "subtitles.entity.pillager.death": "Raubiyrz umiyr<PERSON>", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON><PERSON> sie u<PERSON>", "subtitles.entity.player.attack.crit": "Krytyczny szlag", "subtitles.entity.player.attack.knockback": "Napad ze riksztosym", "subtitles.entity.player.attack.strong": "Festelny napad", "subtitles.entity.player.attack.sweep": "Szyroki szlag", "subtitles.entity.player.attack.weak": "<PERSON><PERSON><PERSON>ad", "subtitles.entity.player.burp": "Gyrtnycie", "subtitles.entity.player.death": "Grŏcz umiyrŏ", "subtitles.entity.player.freeze_hurt": "Grŏcz zamarzŏ", "subtitles.entity.player.hurt": "Gr<PERSON><PERSON> sie utr<PERSON>", "subtitles.entity.player.hurt_drown": "Grŏcz tōnie", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON><PERSON> sie pŏli", "subtitles.entity.player.levelup": "Grŏcz zwōni", "subtitles.entity.player.teleport": "Grŏcz sie teleportuje", "subtitles.entity.polar_bear.ambient": "Polarny niydźwiydź sapie", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON> niydźwiŏdek dudli", "subtitles.entity.polar_bear.death": "Polarny niydźwiydź umiyrŏ", "subtitles.entity.polar_bear.hurt": "Polarny niydźwiydź sie utrŏpiŏ", "subtitles.entity.polar_bear.warning": "Polarny niydźwiydź ryczy", "subtitles.entity.potion.splash": "Flaszka pynkŏ", "subtitles.entity.potion.throw": "Flaszka leci", "subtitles.entity.puffer_fish.blow_out": "<PERSON><PERSON><PERSON><PERSON><PERSON> flaczy", "subtitles.entity.puffer_fish.blow_up": "Kugelfisza sie nadmuchuje", "subtitles.entity.puffer_fish.death": "Kugelfisza um<PERSON>", "subtitles.entity.puffer_fish.flop": "Kugelfisza ciŏplŏ", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON><PERSON>sz<PERSON> sie u<PERSON>", "subtitles.entity.puffer_fish.sting": "Kugelfisza żgŏ", "subtitles.entity.rabbit.ambient": "Mycŏk piskŏ", "subtitles.entity.rabbit.attack": "Mycŏk napŏdŏ", "subtitles.entity.rabbit.death": "Mycŏk umiyrŏ", "subtitles.entity.rabbit.hurt": "Mycŏk sie utrŏpiŏ", "subtitles.entity.rabbit.jump": "Mycŏk hicŏ", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON> fajeruje", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> um<PERSON>", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> sie utr<PERSON>", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON><PERSON>rz ryczy", "subtitles.entity.ravager.step": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>y", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON><PERSON>rz je ôsłupiony", "subtitles.entity.salmon.death": "Laks umiyrŏ", "subtitles.entity.salmon.flop": "Laks ciŏplŏ", "subtitles.entity.salmon.hurt": "Laks sie utr<PERSON>", "subtitles.entity.sheep.ambient": "Ôwca beczy", "subtitles.entity.sheep.death": "Ôwca umiyrŏ", "subtitles.entity.sheep.hurt": "Ôwca sie u<PERSON>", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> sie zaw<PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> sie <PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> sie <PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> sie teleportuje", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON> shul<PERSON>a wy<PERSON>", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON> shul<PERSON>a sie roz<PERSON>je", "subtitles.entity.silverfish.ambient": "Rybik syczy", "subtitles.entity.silverfish.death": "Rybik umiyrŏ", "subtitles.entity.silverfish.hurt": "Rybik sie utrŏpiŏ", "subtitles.entity.skeleton.ambient": "Szkelet klekotŏ", "subtitles.entity.skeleton.converted_to_stray": "Szkelet przemiyniŏ sie we pōniywiyrŏka", "subtitles.entity.skeleton.death": "Szkelet umiyrŏ", "subtitles.entity.skeleton.hurt": "Szkelet sie utrŏpiŏ", "subtitles.entity.skeleton.shoot": "Szkelet strzylŏ", "subtitles.entity.skeleton_horse.ambient": "Kōń-szkelet sie rozdziyrŏ", "subtitles.entity.skeleton_horse.death": "Kōń-szkelet umiyrŏ", "subtitles.entity.skeleton_horse.hurt": "Kōń-szkelet sie utrŏpiŏ", "subtitles.entity.skeleton_horse.jump_water": "Kōń-szkelet skŏkŏ", "subtitles.entity.skeleton_horse.swim": "Kōń-szkelet pływŏ", "subtitles.entity.slime.attack": "<PERSON>z<PERSON><PERSON>m <PERSON>", "subtitles.entity.slime.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> um<PERSON>", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> sie u<PERSON>", "subtitles.entity.slime.squish": "Szlōm plaskŏ", "subtitles.entity.sniffer.death": "Sznupŏcz umiyrŏ", "subtitles.entity.sniffer.digging": "Sznupŏcz bagruje", "subtitles.entity.sniffer.digging_stop": "Sznupŏcz stŏwŏ", "subtitles.entity.sniffer.drop_seed": "Sznupŏcz upuszczŏ ziŏrno", "subtitles.entity.sniffer.eat": "Sznupŏcz jy", "subtitles.entity.sniffer.egg_crack": "Jajco sznupŏcza pynkŏ", "subtitles.entity.sniffer.egg_hatch": "Jajco sznupŏcza sie wykluwŏ", "subtitles.entity.sniffer.happy": "Sznupŏcz sie podziwiŏ", "subtitles.entity.sniffer.hurt": "Sznupŏcz sie utrŏ<PERSON>ŏ", "subtitles.entity.sniffer.idle": "Sznupŏ<PERSON> brōnczy", "subtitles.entity.sniffer.scenting": "Sznupŏcz wōniŏ", "subtitles.entity.sniffer.searching": "Sznupŏcz sznupie", "subtitles.entity.sniffer.sniffing": "Sznupŏcz wōniŏ", "subtitles.entity.sniffer.step": "Sznupŏcz kroczy", "subtitles.entity.snow_golem.death": "Śniyżny golym umiyrŏ", "subtitles.entity.snow_golem.hurt": "Śniyżny golym sie utrŏ<PERSON>ŏ", "subtitles.entity.snowball.throw": "Śniyżka leci", "subtitles.entity.spider.ambient": "Pajōnk syczy", "subtitles.entity.spider.death": "Pajōnk um<PERSON>yr<PERSON>", "subtitles.entity.spider.hurt": "Pajōnk sie utr<PERSON>", "subtitles.entity.squid.ambient": "Tintynfisza pływŏ", "subtitles.entity.squid.death": "Tintynfisza <PERSON>", "subtitles.entity.squid.hurt": "Tintyn<PERSON>sza sie u<PERSON>", "subtitles.entity.squid.squirt": "Tintynfisza strzy<PERSON>ŏ tintōm", "subtitles.entity.stray.ambient": "Pōniywiyrŏk klekotŏ", "subtitles.entity.stray.death": "Pōniywiyrŏk umiyrŏ", "subtitles.entity.stray.hurt": "Pōniywiyrŏk sie utrŏpiŏ", "subtitles.entity.strider.death": "Smykŏcz umiyrŏ", "subtitles.entity.strider.eat": "Smykŏcz jy", "subtitles.entity.strider.happy": "Smykŏcz bajutrzy", "subtitles.entity.strider.hurt": "Smykŏcz sie utrŏ<PERSON>ŏ", "subtitles.entity.strider.idle": "Smykŏcz dziyrgoli", "subtitles.entity.strider.retreat": "Smykŏ<PERSON> sie cŏfie", "subtitles.entity.tadpole.death": "Gowŏcz umiyrŏ", "subtitles.entity.tadpole.flop": "Gowŏcz ciŏplŏ", "subtitles.entity.tadpole.grow_up": "Gowŏcz <PERSON>", "subtitles.entity.tadpole.hurt": "Gowŏcz sie utr<PERSON>", "subtitles.entity.tnt.primed": "Dynamit syczy", "subtitles.entity.tropical_fish.death": "Trōpikalnŏ ryba umiyrŏ", "subtitles.entity.tropical_fish.flop": "Trōpikalnŏ ryba ciŏplŏ", "subtitles.entity.tropical_fish.hurt": "Trōpikalnŏ ryba sie utr<PERSON>ŏ", "subtitles.entity.turtle.ambient_land": "Żōłw dziyrgoli", "subtitles.entity.turtle.death": "Żōłw umiyrŏ", "subtitles.entity.turtle.death_baby": "Ż<PERSON>ł<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.egg_break": "Żōłwie jajco pynkŏ", "subtitles.entity.turtle.egg_crack": "Żōłwie jajco trz<PERSON>ŏ", "subtitles.entity.turtle.egg_hatch": "Żōłwiōntko sie wykluwŏ", "subtitles.entity.turtle.hurt": "Żōłw sie utrŏpiŏ", "subtitles.entity.turtle.hurt_baby": "Żōłwiōntko sie utrŏpiŏ", "subtitles.entity.turtle.lay_egg": "Żōłw skłŏdŏ jajco", "subtitles.entity.turtle.shamble": "Żōłw połzŏ", "subtitles.entity.turtle.shamble_baby": "Żōłwiōntko połzŏ", "subtitles.entity.turtle.swim": "Żōłw pływŏ", "subtitles.entity.vex.ambient": "Szerga szergŏ", "subtitles.entity.vex.charge": "Szerga krziczy", "subtitles.entity.vex.death": "Szerga umiyrŏ", "subtitles.entity.vex.hurt": "Szerga sie utrŏpiŏ", "subtitles.entity.villager.ambient": "Wiyśniŏk mamrze", "subtitles.entity.villager.celebrate": "Wiyśniŏk fajeruje", "subtitles.entity.villager.death": "Wiyśniŏk umiyrŏ", "subtitles.entity.villager.hurt": "Wiyśniŏk sie utrŏpiŏ", "subtitles.entity.villager.no": "Wiyśniŏk sie niy zgŏdzŏ", "subtitles.entity.villager.trade": "Wiyśniŏk handlyrzy", "subtitles.entity.villager.work_armorer": "Pła<PERSON><PERSON>rz robi", "subtitles.entity.villager.work_butcher": "Mas<PERSON>rz robi", "subtitles.entity.villager.work_cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON> robi", "subtitles.entity.villager.work_cleric": "Kaplan robi", "subtitles.entity.villager.work_farmer": "Bauer robi", "subtitles.entity.villager.work_fisherman": "Rybi<PERSON>rz robi", "subtitles.entity.villager.work_fletcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> robi", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> robi", "subtitles.entity.villager.work_librarian": "Biblijotyk<PERSON>rz robi", "subtitles.entity.villager.work_mason": "<PERSON><PERSON><PERSON><PERSON> robi", "subtitles.entity.villager.work_shepherd": "Ôwcŏrz robi", "subtitles.entity.villager.work_toolsmith": "Kowŏl robi", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> robi", "subtitles.entity.villager.yes": "Wiyśniŏk sie zgŏdzŏ", "subtitles.entity.vindicator.ambient": "Zymstliwiec mamrotŏ", "subtitles.entity.vindicator.celebrate": "Zymstliwiec fajeruje", "subtitles.entity.vindicator.death": "Zymstliwiec umiyrŏ", "subtitles.entity.vindicator.hurt": "Zymstliwiec sie utrŏpiŏ", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON><PERSON> hand<PERSON>rz ma<PERSON>e", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON><PERSON><PERSON> hand<PERSON>rz um<PERSON>", "subtitles.entity.wandering_trader.disappeared": "Wandrown<PERSON> hand<PERSON>rz z<PERSON>", "subtitles.entity.wandering_trader.drink_milk": "<PERSON><PERSON><PERSON><PERSON> handlyrz pijy mlyko", "subtitles.entity.wandering_trader.drink_potion": "<PERSON><PERSON><PERSON><PERSON> handlyrz pijy wywar", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON><PERSON> handlyrz sie utr<PERSON>", "subtitles.entity.wandering_trader.no": "Wan<PERSON><PERSON><PERSON> handlyrz sie niy zgŏdzŏ", "subtitles.entity.wandering_trader.reappeared": "Wandr<PERSON><PERSON> handlyrz sie pokazuje", "subtitles.entity.wandering_trader.trade": "<PERSON><PERSON><PERSON><PERSON> handlyrz handly<PERSON>y", "subtitles.entity.wandering_trader.yes": "Wandr<PERSON><PERSON> handlyrz sie zgŏdz<PERSON>", "subtitles.entity.warden.agitated": "Wachmajster stynkŏ ściykle", "subtitles.entity.warden.ambient": "Wachmaj<PERSON> j<PERSON>", "subtitles.entity.warden.angry": "Wachmajster sie gni<PERSON>w<PERSON>", "subtitles.entity.warden.attack_impact": "Wachmajster uderzŏ", "subtitles.entity.warden.death": "Wachmajster umiyrŏ", "subtitles.entity.warden.dig": "Wachmajster kopie", "subtitles.entity.warden.emerge": "Wachmajster sie wyłōniŏ", "subtitles.entity.warden.heartbeat": "S<PERSON>ce wachmajstra bije", "subtitles.entity.warden.hurt": "Wachmajster sie u<PERSON>", "subtitles.entity.warden.listening": "Wachmajster zwracŏ uwŏgã", "subtitles.entity.warden.listening_angry": "Wachmajster ściykle zwracŏ uwŏgã", "subtitles.entity.warden.nearby_close": "Wachmajster przibywŏ", "subtitles.entity.warden.nearby_closer": "Wachmajster przibywŏ", "subtitles.entity.warden.nearby_closest": "Wachmajster sie zbliżŏ", "subtitles.entity.warden.roar": "Wachmajster ryczy", "subtitles.entity.warden.sniff": "Wachmajster wōniŏ", "subtitles.entity.warden.sonic_boom": "Wachmajster grzmi", "subtitles.entity.warden.sonic_charge": "Wachmajster sie rychtuje", "subtitles.entity.warden.step": "Wachmajster kroczy", "subtitles.entity.warden.tendril_clicks": "<PERSON><PERSON><PERSON> ôd wachmajstra dyrgajōm", "subtitles.entity.wind_charge.throw": "Wietrzny ladōnek leci", "subtitles.entity.wind_charge.wind_burst": "Wietrzny ladōnek wybuchŏ", "subtitles.entity.witch.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.celebrate": "<PERSON><PERSON><PERSON> faj<PERSON>", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON> pije", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON> si<PERSON>", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.wither.ambient": "Wither sie gorszy", "subtitles.entity.wither.death": "<PERSON><PERSON>", "subtitles.entity.wither.hurt": "<PERSON>er sie <PERSON>", "subtitles.entity.wither.shoot": "<PERSON><PERSON>", "subtitles.entity.wither.spawn": "Uwŏ<PERSON><PERSON><PERSON>", "subtitles.entity.wither_skeleton.ambient": "Witherowy szkelet klekotŏ", "subtitles.entity.wither_skeleton.death": "Witherowy szkelet umiyrŏ", "subtitles.entity.wither_skeleton.hurt": "Witherowy szkelet sie utrŏpiŏ", "subtitles.entity.wolf.ambient": "Wilk ziopie", "subtitles.entity.wolf.bark": "Wilk szczekŏ", "subtitles.entity.wolf.death": "Wilk umiyrŏ", "subtitles.entity.wolf.growl": "Wilk wyrczy", "subtitles.entity.wolf.hurt": "Wilk sie utr<PERSON>", "subtitles.entity.wolf.pant": "Wilk ziopie", "subtitles.entity.wolf.shake": "Wilk sie <PERSON>", "subtitles.entity.wolf.whine": "<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.zoglin.ambient": "Zoglin wyrczy", "subtitles.entity.zoglin.angry": "Zoglin wyrczy ściykle", "subtitles.entity.zoglin.attack": "Zoglin napŏdŏ", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> sie u<PERSON>", "subtitles.entity.zoglin.step": "Zoglin kroczy", "subtitles.entity.zombie.ambient": "Umarlŏk stynkŏ", "subtitles.entity.zombie.attack_wooden_door": "Dźwiyrze trzaskajōm", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON><PERSON><PERSON> sie rozeprywajōm", "subtitles.entity.zombie.converted_to_drowned": "Umarlŏk przemiyniŏ sie we utopka", "subtitles.entity.zombie.death": "Umarlŏk umiyrŏ", "subtitles.entity.zombie.destroy_egg": "Miŏżdżynie żōłwich jajec", "subtitles.entity.zombie.hurt": "Umarlŏk sie utrŏpiŏ", "subtitles.entity.zombie.infect": "Umarlŏk zakażŏ", "subtitles.entity.zombie_horse.ambient": "Kōń-umarlŏk sie rozdziyrŏ", "subtitles.entity.zombie_horse.death": "Kōń-umarlŏk umiyrŏ", "subtitles.entity.zombie_horse.hurt": "Kōń-umarlŏk sie utrŏpiŏ", "subtitles.entity.zombie_villager.ambient": "Wiyśniŏk-umarlŏk stynkŏ", "subtitles.entity.zombie_villager.converted": "Wiyśniŏk-umarlŏk krziczy", "subtitles.entity.zombie_villager.cure": "Wiyśniŏk-umarlŏk ziopie", "subtitles.entity.zombie_villager.death": "Wiyśniŏk-umarlŏk umiyrŏ", "subtitles.entity.zombie_villager.hurt": "Wiyśniŏk-umarlŏk sie utrŏpiŏ", "subtitles.entity.zombified_piglin.ambient": "Piglin-umarlŏ<PERSON> brōnc<PERSON>", "subtitles.entity.zombified_piglin.angry": "Piglin-umarlŏk br<PERSON><PERSON><PERSON>", "subtitles.entity.zombified_piglin.death": "Piglin-umarlŏk umiyrŏ", "subtitles.entity.zombified_piglin.hurt": "Piglin-umarlŏk sie utrŏpiŏ", "subtitles.event.mob_effect.bad_omen": "Przepowiyd<PERSON>niŏ grŏcza", "subtitles.event.mob_effect.raid_omen": "Po blisku sztyftuje sie anfal", "subtitles.event.mob_effect.trial_omen": "Po blisku sztyftuje sie lagramynckŏ prōba", "subtitles.event.raid.horn": "La<PERSON><PERSON><PERSON> trōmbi<PERSON> rogu", "subtitles.item.armor.equip": "Ôblykanie z<PERSON>ji", "subtitles.item.armor.equip_chain": "Ketowŏ zbroja zwōni", "subtitles.item.armor.equip_diamond": "Dyjamynto<PERSON> pancer br<PERSON>k<PERSON>", "subtitles.item.armor.equip_elytra": "Ôkrywy szuszczōm", "subtitles.item.armor.equip_gold": "<PERSON><PERSON><PERSON><PERSON> pancer br<PERSON>", "subtitles.item.armor.equip_iron": "Żelazny pancer brzink<PERSON>", "subtitles.item.armor.equip_leather": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pancer s<PERSON>", "subtitles.item.armor.equip_netherite": "Netherytowy pancer brzinkŏ", "subtitles.item.armor.equip_turtle": "Żōłwi<PERSON> szupa stukŏ", "subtitles.item.armor.equip_wolf": "Prziwiōnzowanie wilczego pancra", "subtitles.item.armor.unequip_wolf": "<PERSON><PERSON><PERSON><PERSON> pancer sie ssuwŏ", "subtitles.item.axe.scrape": "Cieślica szkrobie", "subtitles.item.axe.strip": "Cieślica zeszkrobuje", "subtitles.item.axe.wax_off": "Zeszkrobowanie waksu", "subtitles.item.bone_meal.use": "Knochynmyj<PERSON> ch<PERSON>", "subtitles.item.book.page_turn": "Szkartka s<PERSON>ści", "subtitles.item.book.put": "Ksiōnżka stukŏ", "subtitles.item.bottle.empty": "Flaszka sie ôprō<PERSON>ŏ", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON><PERSON> flas<PERSON>ki", "subtitles.item.brush.brushing.generic": "Śmiatanie", "subtitles.item.brush.brushing.gravel": "Śmiatanie kisu", "subtitles.item.brush.brushing.gravel.complete": "Skōńczōno śmiatanie kisu", "subtitles.item.brush.brushing.sand": "Śmiatanie piŏsku", "subtitles.item.brush.brushing.sand.complete": "Skōńczōno śmiatanie piŏsku", "subtitles.item.bucket.empty": "<PERSON><PERSON><PERSON> sie <PERSON>", "subtitles.item.bucket.fill": "Folowan<PERSON> a<PERSON>", "subtitles.item.bucket.fill_axolotl": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>by", "subtitles.item.bucket.fill_tadpole": "Gowŏcz ôstŏwŏ chycōny", "subtitles.item.bundle.drop_contents": "Miyszek sie ôprōż<PERSON>ŏ", "subtitles.item.bundle.insert": "Wrażanie rzeczy", "subtitles.item.bundle.insert_fail": "Miyszek je połny", "subtitles.item.bundle.remove_one": "Wyrażowanie rzeczy", "subtitles.item.chorus_fruit.teleport": "Grŏcz sie teleportuje", "subtitles.item.crop.plant": "Sadzowanie plonu", "subtitles.item.crossbow.charge": "Sprynżanie kuszy", "subtitles.item.crossbow.hit": "Strzała sie wbijŏ", "subtitles.item.crossbow.load": "Ladowanie kuszy", "subtitles.item.crossbow.shoot": "<PERSON><PERSON><PERSON> ze kuszy", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.elytra.flying": "Ôkrywy szuszczōm", "subtitles.item.firecharge.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.flintandsteel.use": "Krzosadło zgrzitŏ", "subtitles.item.glow_ink_sac.use": "Bojtlik ze blyndtintōm kidŏ", "subtitles.item.goat_horn.play": "Cigowy rōg trōmbi", "subtitles.item.hoe.till": "Kopŏcz ôrŏ", "subtitles.item.honey_bottle.drink": "Połykanie", "subtitles.item.honeycomb.wax_on": "Waksowanie", "subtitles.item.horse_armor.unequip": "<PERSON><PERSON><PERSON> pancer sie ssuw<PERSON>", "subtitles.item.ink_sac.use": "Bojtlik ze tintōm kidŏ", "subtitles.item.lead.break": "<PERSON><PERSON><PERSON>", "subtitles.item.lead.tied": "Lajna sie zawiōnzuje", "subtitles.item.lead.untied": "Lajna sie ôdwiō<PERSON>je", "subtitles.item.llama_carpet.unequip": "<PERSON><PERSON><PERSON> sie ssuw<PERSON>", "subtitles.item.lodestone_compass.lock": "Magnetytowy kōmpas skazuje magnetyt", "subtitles.item.mace.smash_air": "<PERSON><PERSON><PERSON>g<PERSON><PERSON>", "subtitles.item.mace.smash_ground": "<PERSON><PERSON><PERSON>g<PERSON><PERSON>", "subtitles.item.nether_wart.plant": "<PERSON><PERSON>nie kurzŏwki", "subtitles.item.ominous_bottle.dispose": "Flaszka sie trzask<PERSON>", "subtitles.item.saddle.unequip": "Zatel sie ssuwŏ", "subtitles.item.shears.shear": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.shears.snip": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON> blo<PERSON>", "subtitles.item.shovel.flatten": "<PERSON><PERSON>", "subtitles.item.spyglass.stop_using": "Ôptikruła sie zasuwŏ", "subtitles.item.spyglass.use": "Ôptikruła sie rozsuwŏ", "subtitles.item.totem.use": "To<PERSON>m sie pobudz<PERSON>", "subtitles.item.trident.hit": "Trzizōmb żgŏ", "subtitles.item.trident.hit_ground": "Trzizōmb wibruje", "subtitles.item.trident.return": "Trzizōmb powracŏ", "subtitles.item.trident.riptide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.trident.throw": "Trzizōmb zwōni", "subtitles.item.trident.thunder": "Trzizymbi grōm t<PERSON>", "subtitles.item.wolf_armor.break": "<PERSON><PERSON><PERSON><PERSON> pancer sie niszczy", "subtitles.item.wolf_armor.crack": "<PERSON><PERSON><PERSON><PERSON> pancer trzask<PERSON>", "subtitles.item.wolf_armor.damage": "Wilczŏ zbroja przijmuje ôbrażynia", "subtitles.item.wolf_armor.repair": "Sprŏwianie wilczego pancra", "subtitles.particle.soul_escape": "Duszyczka uciykŏ", "subtitles.ui.cartography_table.take_result": "Kreślynie karty", "subtitles.ui.hud.bubble_pop": "Skŏźnik dychu ô<PERSON>ŏ", "subtitles.ui.loom.take_result": "Używanie krosnōw", "subtitles.ui.stonecutter.take_result": "Użytŏ krajzygi", "subtitles.weather.rain": "Padŏ dyszcz", "symlink_warning.message": "Ladowanie świŏtōw ze folderōw, kere zawiyrajōm symboliczne znaki, może by<PERSON>, e<PERSON><PERSON> niy wi<PERSON>z, co akuratnie robisz. Przewiydz sie wiyncyj na %s.", "symlink_warning.message.pack": "Ladowanie paketōw po symbolicznych linkach może być <PERSON>, e<PERSON><PERSON> niy wiysz dyre<PERSON>, co robisz. Sprŏwdź %s, coby dowi<PERSON><PERSON> sie wiyncyj.", "symlink_warning.message.world": "Ladowanie świŏtōw ze folderōw, kere zawiyrajōm symboliczne znaki, może by<PERSON>, e<PERSON><PERSON> niy wi<PERSON>z, co akuratnie robisz. Przewiydz sie wiyncyj na %s.", "symlink_warning.more_info": "Wiyncyj informacyji", "symlink_warning.title": "Folder świŏta zawiyrŏ symboliczne linki", "symlink_warning.title.pack": "Przidane pakety zawiyrajōm symboliczne linki", "symlink_warning.title.world": "Folder świŏta zawiyrŏ symboliczne linki", "team.collision.always": "<PERSON><PERSON><PERSON>", "team.collision.never": "<PERSON><PERSON><PERSON>", "team.collision.pushOtherTeams": "Odpychanie człōnkōw inkszych manszaftōw", "team.collision.pushOwnTeam": "Odpychanie człōnkōw swojigo manszaftu", "team.notFound": "Niyznany manszaft: „%s”", "team.visibility.always": "<PERSON><PERSON><PERSON>", "team.visibility.hideForOtherTeams": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "team.visibility.hideForOwnTeam": "Skryj swo<PERSON><PERSON>aftowi", "team.visibility.never": "<PERSON><PERSON><PERSON>", "telemetry.event.advancement_made.description": "Zrozu<PERSON><PERSON><PERSON>, kery stoji za zdobyciym postympōw przizwŏlŏ nōm lepij rozumieć a rozwijać grã.", "telemetry.event.advancement_made.title": "Postymp <PERSON>", "telemetry.event.game_load_times.description": "To zdarzynie nōm pōmŏ<PERSON><PERSON> do<PERSON>, kaj na<PERSON><PERSON><PERSON> sku<PERSON> czasu ladowani<PERSON> gry po mierzyniu czasōw regiyrowaniŏ sztartowych sztufōw.", "telemetry.event.game_load_times.title": "<PERSON>zas ladowania gry", "telemetry.event.optional": "%s (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "telemetry.event.optional.disabled": "%s (ôp<PERSON><PERSON><PERSON><PERSON>) — wyłōnczōne", "telemetry.event.performance_metrics.description": "Kedy bydy<PERSON> zna<PERSON> ô<PERSON> profil wydajności Minecrafta, bydymy mogli lepij usprŏwniać a ôptymalizować grã pod kōntym fest roztōmańcie skōnfigurowanych urzōndzyń a ôperacyjnych systymōw. \nKu raporcie tyż je wklud<PERSON>ŏ wersyjŏ gry, co pōmŏgŏ nōm porōwnować profile skutności na nowych wersyjach Minecrafta.", "telemetry.event.performance_metrics.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.event.required": "%s (wymŏgane)", "telemetry.event.world_load_times.description": "W<PERSON><PERSON><PERSON><PERSON> sie informacyje, kere tykajōm czasu dołōnczyniu ku światu a tego, jako tyn czas ulygŏ zmianie. Bez tyn przikłŏd, kej co przidŏwōmy ku grze abo wkludzōmy wielgsze techniczne zmiany, mus<PERSON><PERSON>, jako to wpływŏ na czas ladowaniŏ.", "telemetry.event.world_load_times.title": "<PERSON>zas ladowaniŏ świŏtōw", "telemetry.event.world_loaded.description": "<PERSON><PERSON><PERSON> ô tym, jak grŏ<PERSON>e grajōm we Minecrafta (cum bajszpil tryb gry, modyfika<PERSON><PERSON> klijynta abo serwera, eli we<PERSON><PERSON><PERSON><PERSON> gry) przizwŏlŏ nōm skupić sie na aktualizacyjach, kere poprŏwiajōm tajle gry, co sōm lŏ grŏczy nojwŏżniyjsze.\n\nZdarzynie zaladowaniŏ świŏta je kuplowane ze zdarzyniym wyladowaniŏ świŏta, coby poracho<PERSON> czas twaniŏ jednyj sesyje na istym świecie.", "telemetry.event.world_loaded.title": "Zaladowan<PERSON>", "telemetry.event.world_unloaded.description": "Zdarzynie zaladowaniŏ świŏta je kuplowane ze zdarzyniym wyladowaniŏ świŏta, coby pora<PERSON><PERSON>ć czas twaniŏ jednyj sesyje na istym świecie.\n\n<PERSON>zas twaniŏ (we zykōndach a tikach) ôstŏwŏ zmierzōny przi zakōńczyniu sesyje (tj. przi wyleziyniu ku meni abo rozłōnczyniu sie ze serwerym).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON> gry (we tikach)", "telemetry.property.advancement_id.title": "ID postympu", "telemetry.property.client_id.title": "ID klijynta", "telemetry.property.client_modded.title": "Modyfikacyje klijynta", "telemetry.property.dedicated_memory_kb.title": "Przeznaczōn<PERSON> p<PERSON>ć (kB)", "telemetry.property.event_timestamp_utc.title": "<PERSON><PERSON> zdarzyniŏ (UTC)", "telemetry.property.frame_rate_samples.title": "Frekwyn<PERSON><PERSON><PERSON>świ<PERSON><PERSON><PERSON> (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON> gry", "telemetry.property.game_version.title": "Wersyjŏ gry", "telemetry.property.launcher_name.title": "<PERSON><PERSON> l<PERSON>", "telemetry.property.load_time_bootstrap_ms.title": "<PERSON><PERSON> ladowaniŏ (we milizykōndach)", "telemetry.property.load_time_loading_overlay_ms.title": "Czas na ladōnkowym ykrōnie (we milizykōndach)", "telemetry.property.load_time_pre_window_ms.title": "<PERSON>zas przed ôdewrziciym <PERSON> (we milizykōndach)", "telemetry.property.load_time_total_time_ms.title": "Cołkowity czas ladowaniŏ (we milizykōndach)", "telemetry.property.minecraft_session_id.title": "ID sesyje <PERSON>a", "telemetry.property.new_world.title": "Nowy świŏt", "telemetry.property.number_of_samples.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "telemetry.property.operating_system.title": "Ôperacyjny systym", "telemetry.property.opt_in.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>ch", "telemetry.property.platform.title": "Platforma", "telemetry.property.realms_map_content.title": "Inhalt kart Realms (miano minigry)", "telemetry.property.render_distance.title": "Ôdlygłość <PERSON><PERSON><PERSON>", "telemetry.property.render_time_samples.title": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "telemetry.property.seconds_since_load.title": "<PERSON><PERSON> ôd <PERSON> (we zykōndach)", "telemetry.property.server_modded.title": "Modyfikacyje ser<PERSON>", "telemetry.property.server_type.title": "<PERSON><PERSON><PERSON> serwera", "telemetry.property.ticks_since_load.title": "<PERSON><PERSON> ôd <PERSON> (we tikach)", "telemetry.property.used_memory_samples.title": "Używanŏ pami<PERSON>ńć RAM", "telemetry.property.user_id.title": "ID używŏcza", "telemetry.property.world_load_time_ms.title": "<PERSON>zas ladowaniŏ świŏta (we milizykōndach)", "telemetry.property.world_session_id.title": "ID sesyje świŏta", "telemetry_info.button.give_feedback": "<PERSON><PERSON><PERSON><PERSON>", "telemetry_info.button.privacy_statement": "Poświŏdczynie ô ôchrōnie p<PERSON>watności", "telemetry_info.button.show_data": "Pok<PERSON>ż moje dane", "telemetry_info.opt_in.description": "Wyrażōm zgoda na posyłanie warōnkowych telemetrycznych danych", "telemetry_info.property_title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> danych", "telemetry_info.screen.description": "<PERSON><PERSON> te dane, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to nōm usprŏwniać Minecrafta dziynki kludzyniu nŏs ku kerōnkōm, co sōm urzōndne naszym grŏczōm.\n<PERSON><PERSON><PERSON> tyż posyła<PERSON> e<PERSON>tra <PERSON>, coby nōm pōm<PERSON><PERSON> ul<PERSON>crafta.", "telemetry_info.screen.title": "Zbiyr telemetrycznych danych", "test.error.block_property_mismatch": "Spodziywano, by włŏsność %s miała wert %s, a miała %s", "test.error.block_property_missing": "Spod<PERSON>ywan<PERSON>, by włŏsność %s miała wert %s, a jij brakuje", "test.error.entity_property": "Byt %s zawiōd test: %s", "test.error.entity_property_details": "Byt %s zawiōd test: %s, spodziywano: %s, zaregistiyrowano: %s", "test.error.expected_block": "Spodziywano bloku %s, a ôtrzimano: %s", "test.error.expected_block_tag": "Spodziywano bloku we #%s, a ôtrzimano: %s", "test.error.expected_container_contents": "Pojymnik winien zaw<PERSON>ć: %s", "test.error.expected_container_contents_single": "Pojymnik winien zawiyrać yno jednõ rzecz zorty %s", "test.error.expected_empty_container": "Pojymnik winien być prōżny", "test.error.expected_entity": "Spodziywano: %s", "test.error.expected_entity_around": "Spodziywano istnieniŏ bytu %s wele %s, %s, %s ", "test.error.expected_entity_count": "Spodziywano %s bytōw zorty %s, a znŏjdzono: %s", "test.error.expected_entity_data": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, iże dane bytu bydōm %s, a były %s ", "test.error.expected_entity_data_predicate": "Niyzgodliwość danych bytu lŏ predykatu %s", "test.error.expected_entity_effect": "Spodziywano, iże byt %s bydzie mioł efekt %s %s", "test.error.expected_entity_having": "Inwyntŏrz bytu winien zaw<PERSON>yrać rzecz: %s", "test.error.expected_entity_holding": "Byt winien trzimać rzecz: %s", "test.error.expected_entity_in_test": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, iże byt zorty %s bydzie istnioł we teście", "test.error.expected_entity_not_touching": "<PERSON><PERSON>, iże byt zorty %s bydzie sie znŏjdowoł na %s, %s, %s (zglyndnie: %s, %s, %s)", "test.error.expected_entity_touching": "Spodziywano, iże byt zorty %s bydzie sie znŏjdowoł na %s, %s, %s (zglyndnie: %s, %s, %s)", "test.error.expected_item": "Spodziywano rzeczy zorty „%s”", "test.error.expected_items_count": "Spodziywano %s rzeczy zorty %s, a znŏjdzōno %s", "test.error.fail": "Ziszczōno warōnki niypowodzyniŏ", "test.error.invalid_block_type": "Znŏjdzōno felernõ zortã bloku: „%s”", "test.error.missing_block_entity": "Brak<PERSON>je bytu we fasōnku bloku", "test.error.position": "%s na położyniu %s, %s, %s (zglyndnie: %s, %s, %s) przi tiku %s", "test.error.sequence.condition_already_triggered": "Warōnek już ôstoł ziszczōny ô %s", "test.error.sequence.condition_not_triggered": "Warōnek niy ôstoł ziszczōny", "test.error.sequence.invalid_tick": "Spodziywano powodzyniŏ testu akuratnie przi %s tiku, a test sie powiōd kedy indzij", "test.error.sequence.not_completed": "Test wygŏs przed zakōńczyniym ciōngu dziołań", "test.error.set_biome": "<PERSON><PERSON> podarziło sie nasztalować biōmu lŏ testu", "test.error.spawn_failure": "Niy podarziło sie stworzić bytu %s", "test.error.state_not_equal": "Felerny stōn. Spodziywano %s, a bōło %s", "test.error.structure.failure": "Niy podarziło sie postawić testowyj struktury lŏ %s", "test.error.tick": "%s przi tiku %s", "test.error.ticking_without_structure": "Regiyrowanie testu przed stŏwianiem struktury", "test.error.timeout.no_result": "Ani sie niy powiōd ani sie niy zawiōd we gangu %s tikōw", "test.error.timeout.no_sequences_finished": "Żŏdnŏ ze sekwencyji sie niy zakōńczyła we gangu %s tikōw", "test.error.too_many_entities": "Spodziywano istnieniŏ yno jednego bytu zorty %s wele %s, %s, %s, a znŏjdzōno %s", "test.error.unexpected_block": "<PERSON><PERSON> spodziywano bloku zorty „%s”", "test.error.unexpected_entity": "Niy spodziywano %s", "test.error.unexpected_item": "Niy spodziywano rzeczy zorty %s", "test.error.unknown": "Niyznany wnōntrzny feler: „%s”", "test.error.value_not_equal": "Spodziywano, by włŏśność %s miała wert %s, a miała %s", "test.error.wrong_block_entity": "Felernŏ zorta bytu we fasōnku bloku: %s", "test_block.error.missing": "Testowyj strukturze brakuje bloku %s ", "test_block.error.too_many": "Za moc blokōw zorty: %s", "test_block.invalid_timeout": "Felernŏ nōmera (%s) — Limit czasu musi być dodatniŏ liczba tikōw", "test_block.message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:", "test_block.mode.accept": "Przijmnycie", "test_block.mode.fail": "Niypowodzynie", "test_block.mode.log": "Register", "test_block.mode.start": "Sztart", "test_block.mode_info.accept": "Tryb przijmnyciŏ — przyjmnycie powodzyniŏ lŏ cołkigo testu abo jego tajle", "test_block.mode_info.fail": "Tryb niypowodzyniŏ — zawiedzynie testu", "test_block.mode_info.log": "<PERSON>b registru — registro<PERSON>ie w<PERSON>", "test_block.mode_info.start": "Tryb sztartu — plac nap<PERSON>z<PERSON>ciŏ testu", "test_instance.action.reset": "Zresetuj a zaladuj", "test_instance.action.run": "Zaladuj a sztartnij", "test_instance.action.save": "<PERSON><PERSON><PERSON> struk<PERSON>", "test_instance.description.batch": "Seryjŏ: %s", "test_instance.description.failed": "Feler: %s", "test_instance.description.function": "Fōnkcyjŏ: %s", "test_instance.description.invalid_id": "Felerne <PERSON> testu", "test_instance.description.no_test": "<PERSON><PERSON> ma żŏdnego takigo testu", "test_instance.description.structure": "Struktura: %s", "test_instance.description.type": "Zorta: %s", "test_instance.type.block_based": "Test ôprzity na blokach", "test_instance.type.function": "Wbudowany test fōnkcyje", "test_instance_block.entities": "Byty:", "test_instance_block.error.no_test": "Niy id<PERSON> s<PERSON>ć wystōmpiyniŏ testu na %s, %s, %s, pōniywŏż brakuje we nim samego testu ", "test_instance_block.error.no_test_structure": "Niy idzie sztar<PERSON>ć wystōmpiyniŏ testu na %s, %s, %s, pōniywŏż brakuje we nim testowyj struktury", "test_instance_block.error.unable_to_save": "<PERSON><PERSON> szabl<PERSON>nu struktury ôd wystōmpiyniŏ testu na %s, %s, %s", "test_instance_block.invalid": "[felerny]", "test_instance_block.reset_success": "Zresetowano test „%s”", "test_instance_block.rotation": "Ôbrōcynie", "test_instance_block.size": "Wielgość testowyj struktury", "test_instance_block.starting": "Sztartowanie testu %s", "test_instance_block.test_id": "ID wystōmpiyniŏ testu", "title.32bit.deprecation": "Znŏjdzono 32-bitowy systym: je <PERSON><PERSON><PERSON><PERSON>, i<PERSON>e cie to postrzimie grać we prziszł<PERSON>ci, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bydzie wymŏgany 64-bitowy systym!", "title.32bit.deprecation.realms": "Minecraft hnedko bydzie wymŏgoł 64-bitowego systymu, co cie postrzimie korzystaniŏ ze posługi Realms na tym urzōndzyniu. Eźli ôbecnie mŏsz abōnamynt, byd<PERSON><PERSON> musioł go zakōńczyć.", "title.32bit.deprecation.realms.check": "<PERSON><PERSON> pokazuj tego już", "title.32bit.deprecation.realms.header": "Znŏjdzono 32-bitowy systym", "title.credits": "Autorske prawa Mojang AB. Niy szyrz<PERSON>!", "title.multiplayer.disabled": "Mocperzōnowŏ gra je wyłōnc<PERSON>ŏ — Proszymy badnyć nasztalowania swojigo kōnta Microsoft.", "title.multiplayer.disabled.banned.name": "<PERSON><PERSON> byd<PERSON> gra<PERSON>, byd<PERSON><PERSON> musioł z<PERSON> swoji miano używŏcza", "title.multiplayer.disabled.banned.permanent": "Twoje kōnto mŏ na sztyjc bloknyty dostymp ku necowyj grze", "title.multiplayer.disabled.banned.temporary": "Twoje kōnto mŏ tymczasowo bloknyty dostymp ku necowyj grze", "title.multiplayer.lan": "Mocperzōnowŏ gra (LAN)", "title.multiplayer.other": "Mocperzōnowŏ gra (zewnōntrzny serwer)", "title.multiplayer.realms": "Mocperzōnowŏ gra (Realms)", "title.singleplayer": "Jednoperzōnowŏ gra", "translation.test.args": "%s %s", "translation.test.complex": "Prefiks, %s%2$s zaś %s a %1$s yntlich %s a tyż %1$s juzaś!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "serwus %", "translation.test.invalid2": "serwus %s", "translation.test.none": "Witej, <PERSON>wi<PERSON>ie!", "translation.test.world": "świŏt", "trim_material.minecraft.amethyst": "Ametystowy sztof", "trim_material.minecraft.copper": "<PERSON><PERSON><PERSON><PERSON> sztof", "trim_material.minecraft.diamond": "Dyjamynto<PERSON> sztof", "trim_material.minecraft.emerald": "Szmaragdowy sztof", "trim_material.minecraft.gold": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>tof", "trim_material.minecraft.iron": "Żelazny sztof", "trim_material.minecraft.lapis": "<PERSON><PERSON><PERSON><PERSON><PERSON>tof", "trim_material.minecraft.netherite": "Netherytowy sztof", "trim_material.minecraft.quartz": "Kwar<PERSON><PERSON>ztof", "trim_material.minecraft.redstone": "Redstone'owy sztof", "trim_material.minecraft.resin": "Harc<PERSON><PERSON> sztof", "trim_pattern.minecraft.bolt": "Ôrnamynt — Szrauba", "trim_pattern.minecraft.coast": "Ôrnamynt — Wybrzeże", "trim_pattern.minecraft.dune": "Ôrnamynt — Wyd<PERSON>", "trim_pattern.minecraft.eye": "Ôrnamynt — <PERSON><PERSON><PERSON>", "trim_pattern.minecraft.flow": "Ôrnamynt — Przepływ", "trim_pattern.minecraft.host": "Ôrnamynt — Gospodŏrz", "trim_pattern.minecraft.raiser": "Ôrnamynt — Formut", "trim_pattern.minecraft.rib": "Ôrnamynt — Ziobro", "trim_pattern.minecraft.sentry": "Ôrnamynt — <PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.shaper": "Ôrnamynt — Kształtownik", "trim_pattern.minecraft.silence": "Ôrnamynt — Cisza", "trim_pattern.minecraft.snout": "Ôrnamynt — Niurŏk", "trim_pattern.minecraft.spire": "Ôrnamynt — Szpica", "trim_pattern.minecraft.tide": "Ôrnamynt — <PERSON><PERSON>", "trim_pattern.minecraft.vex": "Ôrnamynt — Szerga", "trim_pattern.minecraft.ward": "Ôrnamynt — Wacha", "trim_pattern.minecraft.wayfinder": "Ôrnamynt <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.wild": "Ôrnamynt — Dzicz", "tutorial.bundleInsert.description": "Kliknij po prawym <PERSON>ef<PERSON>, coby prz<PERSON> rzeczy", "tutorial.bundleInsert.title": "Użyj miyszka", "tutorial.craft_planks.description": "Ksiōnżka receptōw Ci pōmoże", "tutorial.craft_planks.title": "<PERSON>yt<PERSON><PERSON><PERSON> drzew<PERSON> brety", "tutorial.find_tree.description": "Klupnij a zebier drzewo", "tutorial.find_tree.title": "Znŏjdź strōm", "tutorial.look.description": "Zwyrtej sie myszkōm", "tutorial.look.title": "Rozejzdr<PERSON>j sie", "tutorial.move.description": "Skŏkej po pōmocy %s", "tutorial.move.title": "<PERSON><PERSON><PERSON><PERSON> sie po pōmocy %s, %s, %s a %s", "tutorial.open_inventory.description": "Naciś %s", "tutorial.open_inventory.title": "Ôdewrzij inwyntŏrz", "tutorial.punch_tree.description": "Przitrzimej %s", "tutorial.punch_tree.title": "<PERSON>et<PERSON><PERSON> str<PERSON>", "tutorial.socialInteractions.description": "Naciś %s, coby <PERSON>", "tutorial.socialInteractions.title": "Społeczne interakcyje", "upgrade.minecraft.netherite_upgrade": "Netherytowe ulepszynie"}