{"accessibility.onboarding.accessibility.button": "Beasatlašvuođa heivehusat...", "accessibility.onboarding.screen.narrator": "<PERSON><PERSON><PERSON><PERSON>ah<PERSON> \"Enter\"-b<PERSON><PERSON>", "accessibility.onboarding.screen.title": "Bures boahtin Minecraftii!\n\nHáliidivččet go geavahit lohkanveahkkeprográmma dahje fitnat beasatlašvuođa heivehusaid rievdadeame?", "addServer.add": "<PERSON><PERSON>", "addServer.enterIp": "<PERSON>", "addServer.enterName": "<PERSON><PERSON> namma", "addServer.resourcePack": "<PERSON><PERSON> resursa<PERSON>", "addServer.resourcePack.disabled": "Ii geavahuvvo", "addServer.resourcePack.enabled": "Alde", "addServer.resourcePack.prompt": "<PERSON><PERSON>", "addServer.title": "Rievdat serverdieđuid", "advMode.command": "<PERSON><PERSON><PERSON>", "advMode.mode": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.auto": "<PERSON><PERSON><PERSON>ea<PERSON><PERSON>", "advMode.mode.autoexec.bat": "<PERSON><PERSON>", "advMode.mode.conditional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.mode.redstone": "Impulsiiva", "advMode.mode.redstoneTriggered": "Dárbbaša redstone", "advMode.mode.sequence": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.unconditional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.notAllowed": "Ferte leat spealli gii lea OP ja hutkás speallanhámis", "advMode.notEnabled": "Kommándoblohkat eai leat dohkkehuvvon dán <PERSON>is", "advMode.previousOutput": "<PERSON><PERSON><PERSON><PERSON>", "advMode.setCommand": "Bija konsolla kommándoblohkkii", "advMode.setCommand.success": "Kommándo bidjon: %s", "advMode.trackOutput": "Čuovo bohtosa", "advMode.triggering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.type": "<PERSON><PERSON><PERSON>", "advancement.advancementNotFound": "Amas oláhus: %s", "advancements.adventure.adventuring_time.description": "Gáv<PERSON> juohke luonddu", "advancements.adventure.adventuring_time.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.arbalistic.description": "Gotte 5 iešguđegelágan elliid ovtta dávgebissu njuolain", "advancements.adventure.arbalistic.title": "Dávgebissobá<PERSON>i", "advancements.adventure.avoid_vibration.description": "<PERSON><PERSON><PERSON><PERSON> sculk-do<PERSON><PERSON><PERSON>, sculk-r<PERSON><PERSON><PERSON>i dahje goh<PERSON> lahka vai sii eai gula du", "advancements.adventure.avoid_vibration.title": "Njáhkan 100", "advancements.adventure.blowback.description": "Gotte ruvašstálu hoigadeamis bieggadoarohasa ruovttoluotta", "advancements.adventure.blowback.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.brush_armadillo.description": "Gušte armadilloskálžžu luovos geavaheamis gušta", "advancements.adventure.brush_armadillo.title": "Oažžu skálžžu", "advancements.adventure.bullseye.description": "Deaivva guovdu deaivvusblohka unnimusat 30 mehtar gaskain", "advancements.adventure.bullseye.title": "Deaivil", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Daga hervejuv<PERSON> kruhka n<PERSON>l<PERSON> k<PERSON>", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON><PERSON><PERSON><PERSON> divvun", "advancements.adventure.crafters_crafting_crafters.description": "Go leat duoj<PERSON>ra lahka mii duddjo duojára", "advancements.adventure.crafters_crafting_crafters.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.fall_from_world_height.description": "<PERSON><PERSON><PERSON><PERSON> (m<PERSON><PERSON><PERSON>) gitta máil<PERSON>i bod<PERSON>i ja eale", "advancements.adventure.fall_from_world_height.title": "Rokkit & bávttit", "advancements.adventure.heart_transplanter.description": "Bija G<PERSON>či váimmu rievttes ládje guovtti šovkes eaikačoska gaskii", "advancements.adventure.heart_transplanter.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.hero_of_the_village.description": "Lihkostuvva doarjut ovtta gili rievideamis", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.honey_block_slide.description": "<PERSON><PERSON><PERSON> honnet<PERSON><PERSON><PERSON><PERSON> njeaiga go njuiket", "advancements.adventure.honey_block_slide.title": "Njivlagoardnji", "advancements.adventure.kill_a_mob.description": "Gotte vaikko ma<PERSON>", "advancements.adventure.kill_a_mob.title": "Návdebivdi", "advancements.adventure.kill_all_mobs.description": "Gotte ovtta stálu juohke stállošlájas", "advancements.adventure.kill_all_mobs.title": "<PERSON>áv<PERSON><PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "<PERSON>te e<PERSON>i sculk-ka<PERSON><PERSON><PERSON><PERSON><PERSON>ka", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "<PERSON><PERSON> <PERSON>", "advancements.adventure.lighten_up.description": "Fasko veaikečuovgga ákšun vai čuovgagoahtá", "advancements.adventure.lighten_up.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "<PERSON><PERSON><PERSON><PERSON>, muhto v<PERSON><PERSON>t ammas dola c<PERSON>", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "advancements.adventure.minecraft_trials_edition.description": "Lávke gea<PERSON>ččalusaid g<PERSON>mmirii", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(aid) veršuvdna", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON>ž<PERSON>", "advancements.adventure.ol_betsy.title": "Dávgealmm<PERSON><PERSON>", "advancements.adventure.overoverkill.description": "Bávččat veahčiriin 50 váibmoheakka ovtta dearpamis", "advancements.adventure.overoverkill.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> goddin", "advancements.adventure.play_jukebox_in_meadows.description": "Ealáskahtte gietti skearročuojanasa musihkain", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON><PERSON><PERSON> jietna", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Iska luokčajuvvon girjehildu redstonesignála iskkadeddjiin", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.revaulting.description": "<PERSON><PERSON> bahávuoiŋŋalaš lohkkadaga bahávuoiŋŋalaš g<PERSON>", "advancements.adventure.revaulting.title": "Čovdojuvvon lohkka", "advancements.adventure.root.description": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON>, ja do<PERSON><PERSON>", "advancements.adventure.root.title": "Fear<PERSON>", "advancements.adventure.salvage_sherd.description": "<PERSON><PERSON> ima<PERSON><PERSON><PERSON> blohka ja gávnna das kruhkka<PERSON>alu", "advancements.adventure.salvage_sherd.title": "<PERSON><PERSON><PERSON> do<PERSON> dá<PERSON>d", "advancements.adventure.shoot_arrow.description": "<PERSON><PERSON><PERSON><PERSON> juo<PERSON> n<PERSON>", "advancements.adventure.shoot_arrow.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.sleep_in_bed.description": "<PERSON><PERSON><PERSON><PERSON> seaŋggas nu ahte rievdadat ealáskanbáikki", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.sniper_duel.description": "<PERSON><PERSON><PERSON><PERSON> ja gotte dákterikki unnimus 50 mehtar gaskkain", "advancements.adventure.sniper_duel.title": "Galge go dávgiin gilvo<PERSON>at?", "advancements.adventure.spyglass_at_dragon.description": "Giikanastte endordrága", "advancements.adventure.spyglass_at_dragon.title": "Lea go girdi?", "advancements.adventure.spyglass_at_ghast.description": "Giikanastte ghast giikaniin", "advancements.adventure.spyglass_at_ghast.title": "Lea go baloŋŋa?", "advancements.adventure.spyglass_at_parrot.description": "Giikanastte papegoia", "advancements.adventure.spyglass_at_parrot.title": "Lea go loddi?", "advancements.adventure.summon_iron_golem.description": "Ealáskahtte ruovdejiehtanasa várjalanveahkkin márkanii", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON> bođii guo<PERSON>i", "advancements.adventure.throw_trident.description": "<PERSON><PERSON><PERSON><PERSON> háŋggu juoga man njeaiga.\nFuom: <PERSON><PERSON>lkestit duš<PERSON>i á<PERSON>na vearjju ii leat buorre jurdda.", "advancements.adventure.throw_trident.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.totem_of_undying.description": "Geavat jápm<PERSON>eahttunvuođa árnna nu ahte eall<PERSON>t jápmindilis", "advancements.adventure.totem_of_undying.title": "Don leat jámeš!", "advancements.adventure.trade.description": "Buoremielat gávppoš gill<PERSON>ččain", "advancements.adventure.trade.title": "Dat gusto g<PERSON>!", "advancements.adventure.trade_at_world_height.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON> m<PERSON>mmi alimus huk<PERSON>", "advancements.adventure.trade_at_world_height.title": "Nástegávpi", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Geavat ču<PERSON><PERSON><PERSON><PERSON><PERSON>id unnimusat oktii: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> dego rá<PERSON> b<PERSON><PERSON>", "advancements.adventure.trim_with_any_armor_pattern.description": "<PERSON><PERSON><PERSON> hervejuvvon suodjebiktasa rávdebadjebeavddis", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON>", "advancements.adventure.two_birds_one_arrow.description": "Gotte guokte idjagopmi sákkastahtti n<PERSON>olain", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON><PERSON>, okta njuolla", "advancements.adventure.under_lock_and_key.description": "Raba lohkkadaga gea<PERSON>ččal<PERSON>oa<PERSON>gi<PERSON>", "advancements.adventure.under_lock_and_key.title": "Loh<PERSON>du<PERSON><PERSON>", "advancements.adventure.use_lodestone.description": "Deaddil láidengeađggi kompássain", "advancements.adventure.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON> mu ruk<PERSON>an", "advancements.adventure.very_very_frightening.description": "Časke gillilačča áltagasain", "advancements.adventure.very_very_frightening.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.voluntary_exile.description": "<PERSON>te rievvár hearrá. So<PERSON><PERSON> leat buorre jurdda garvit gilliid ve<PERSON><PERSON>...", "advancements.adventure.voluntary_exile.title": "Iešmielalaš <PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Vácce obbasa alde... dego liv<PERSON><PERSON>i cuoŋu", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "It čalgga buori g<PERSON>!", "advancements.adventure.who_needs_rockets.description": "Báže iežat 8 blohka áibmui bieggadoarohasain", "advancements.adventure.who_needs_rockets.title": "<PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON> bá<PERSON><PERSON>?", "advancements.adventure.whos_the_pillager_now.description": "Čájet rievvárii movt lea rivvejuvvot", "advancements.adventure.whos_the_pillager_now.title": "Gii bat dat rievv<PERSON>r lea dál?", "advancements.empty": "<PERSON><PERSON><PERSON> ii oro oidnomin mihkkege...", "advancements.end.dragon_breath.description": "<PERSON><PERSON><PERSON><PERSON> drága vuoiŋŋ<PERSON><PERSON> l<PERSON>ii", "advancements.end.dragon_breath.title": "Berre<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "advancements.end.dragon_egg.description": "Doala drá<PERSON>oni", "advancements.end.dragon_egg.title": "<PERSON><PERSON><PERSON> buolva", "advancements.end.elytra.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.end.elytra.title": "<PERSON><PERSON><PERSON> lea mearri", "advancements.end.enter_end_gateway.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON><PERSON> eret verr<PERSON>", "advancements.end.find_end_city.description": "Finat siste, sáhttá bat nu vearrái leat?", "advancements.end.find_end_city.title": "<PERSON><PERSON><PERSON><PERSON> gitta <PERSON>", "advancements.end.kill_dragon.description": "Buorre lihkku", "advancements.end.kill_dragon.title": "<PERSON><PERSON>", "advancements.end.levitate.description": "<PERSON><PERSON><PERSON><PERSON> 50 blohka eret shul<PERSON>", "advancements.end.levitate.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lei buorre oainnus", "advancements.end.respawn_dragon.description": "Ealás fas Endordrága", "advancements.end.respawn_dragon.title": "Vuohon... <PERSON>...", "advancements.end.root.description": "Vai bures bat manai ge?", "advancements.end.root.title": "<PERSON><PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "<PERSON><PERSON><PERSON> h<PERSON> bá<PERSON>ut gáhku n<PERSON>httablohka n<PERSON>aiga", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "advancements.husbandry.allay_deliver_item_to_player.description": "<PERSON><PERSON><PERSON><PERSON> hálddi buktit dutnje dávvira", "advancements.husbandry.allay_deliver_item_to_player.title": "Dutnje aivve ustit lean", "advancements.husbandry.axolotl_in_a_bucket.description": "<PERSON><PERSON> a<PERSON> s<PERSON>", "advancements.husbandry.axolotl_in_a_bucket.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON> buot máid s<PERSON>ht<PERSON> borrat, vaik<PERSON> ii leat boráhahtii dahje <PERSON>š", "advancements.husbandry.balanced_diet.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> bi<PERSON>", "advancements.husbandry.breed_all_animals.description": "<PERSON><PERSON><PERSON><PERSON> buot elliid!", "advancements.husbandry.breed_all_animals.title": "<PERSON><PERSON><PERSON> lea buoret", "advancements.husbandry.breed_an_animal.description": "Eat<PERSON><PERSON><PERSON> ealli nuppi elliin", "advancements.husbandry.breed_an_animal.title": "<PERSON><PERSON> go doarrumin??", "advancements.husbandry.complete_catalogue.description": "Dáma lodjin buot bussábuolvvaid!", "advancements.husbandry.complete_catalogue.title": "<PERSON><PERSON>", "advancements.husbandry.feed_snifflet.description": "<PERSON><PERSON><PERSON><PERSON> susti", "advancements.husbandry.feed_snifflet.title": "Susttašeamit", "advancements.husbandry.fishy_business.description": "<PERSON><PERSON> guoli", "advancements.husbandry.fishy_business.title": "<PERSON><PERSON><PERSON>h<PERSON>ku vai?", "advancements.husbandry.froglights.description": "<PERSON><PERSON><PERSON><PERSON> buot rihccecuoppočuovggaid dávvirvuorkái", "advancements.husbandry.froglights.title": "Go mii váldit gitt<PERSON>gaid, de leat giev<PERSON>t!", "advancements.husbandry.kill_axolotl_target.description": "Do<PERSON> ja vuoitte návddiid vuost<PERSON> aksolo<PERSON>in fárrolaga", "advancements.husbandry.kill_axolotl_target.title": "Ustitlaš fápmu!", "advancements.husbandry.leash_all_frog_variants.description": "Darvet visot rihccecuoppošlájaid seamma lávžái", "advancements.husbandry.leash_all_frog_variants.title": "Go valvi dolle márkanii", "advancements.husbandry.make_a_sign_glow.description": "Vuoidda áhcagas ivdnečoali galbii nu ahte čuovgagoahtá", "advancements.husbandry.make_a_sign_glow.title": "Áicca movt áhcagastá!", "advancements.husbandry.netherite_hoe.description": "Geavat netheriita bihttá buoridit guo<PERSON>, ja de jurddašahtte iežat eallinmearrádusaid birra", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.obtain_sniffer_egg.description": "<PERSON><PERSON><PERSON>i", "advancements.husbandry.obtain_sniffer_egg.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.place_dried_ghast_in_water.description": "<PERSON><PERSON> goikan ghast <PERSON>", "advancements.husbandry.place_dried_ghast_in_water.title": "Juga čázi!", "advancements.husbandry.plant_any_sniffer_seed.description": "<PERSON><PERSON><PERSON> vaikko mak<PERSON> gil<PERSON> man <PERSON><PERSON><PERSON> lea su<PERSON>n", "advancements.husbandry.plant_any_sniffer_seed.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.plant_seed.description": "<PERSON><PERSON><PERSON> ja geahča go šaddá", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON> leat boanda", "advancements.husbandry.remove_wolf_armor.description": "Čoavdde skárriiguin luovus suodjalusbiktasa gumpes", "advancements.husbandry.remove_wolf_armor.title": "Čuohpa biktasiid", "advancements.husbandry.repair_wolf_armor.description": "Divo gumppe suodjalusbiktasa armadilloskálžžuin", "advancements.husbandry.repair_wolf_armor.title": "Dego livččii ođas", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Hoigat gáicca fatnasii ja gorgŋes ieš vel fatnasii", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Go<PERSON><PERSON><PERSON><PERSON><PERSON> go gáica?", "advancements.husbandry.root.description": "Eatnan alde leat dievva olbm<PERSON>t ja biebmu", "advancements.husbandry.root.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.safely_harvest_honey.description": "Čoakke honnega uvlobeasis láseb<PERSON>taliin, geavaheamis <PERSON> vai uvllut eai suhta", "advancements.husbandry.safely_harvest_honey.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.silk_touch_nest.description": "<PERSON><PERSON><PERSON> uvlobeasi mas leat 3 uvllu, g<PERSON><PERSON><PERSON><PERSON> silkeguosk<PERSON><PERSON> no<PERSON>uma", "advancements.husbandry.silk_touch_nest.title": "Áibbas giehtanjuovžil", "advancements.husbandry.tactical_fishing.description": "Gotte guoli... oaggunst<PERSON><PERSON><PERSON> haga!", "advancements.husbandry.tactical_fishing.title": "<PERSON><PERSON><PERSON> g<PERSON>", "advancements.husbandry.tadpole_in_a_bucket.description": "Goive šlubboaivvi skállui", "advancements.husbandry.tadpole_in_a_bucket.title": "Bukkit Bukkit", "advancements.husbandry.tame_an_animal.description": "<PERSON><PERSON><PERSON> lo<PERSON>", "advancements.husbandry.tame_an_animal.title": "Agálaš olbmážat", "advancements.husbandry.wax_off.description": "Sihko v<PERSON> ve<PERSON>lo<PERSON>!", "advancements.husbandry.wax_off.title": "Váksasihkku", "advancements.husbandry.wax_on.description": "Vuoidda honnetbihttá veaikeblohkkii!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.whole_pack.description": "<PERSON><PERSON><PERSON> j<PERSON>", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON> valvi", "advancements.nether.all_effects.description": "Ane buot váikkuhusaid mat gávdnojit spealus seammás", "advancements.nether.all_effects.title": "Movt máilmmis deike beasaimet?", "advancements.nether.all_potions.description": "Váikkuhuvvo buot jugusváikkuhusain mat gávdnojit seammás", "advancements.nether.all_potions.title": "G<PERSON><PERSON><PERSON> jugus", "advancements.nether.brew_potion.description": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "advancements.nether.brew_potion.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> bat don leat ge", "advancements.nether.charge_respawn_anchor.description": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>ŋ<PERSON>a <PERSON> dievvan", "advancements.nether.charge_respawn_anchor.title": "<PERSON><PERSON> hal juste \"ov<PERSON>\" heakka", "advancements.nether.create_beacon.description": "<PERSON><PERSON><PERSON> ja bija vá<PERSON><PERSON> muh<PERSON> sad<PERSON>i", "advancements.nether.create_beacon.title": "Buvtte ruoktot várddu", "advancements.nether.create_full_beacon.description": "<PERSON><PERSON><PERSON> v<PERSON>", "advancements.nether.create_full_beacon.title": "Albma várdodahkki", "advancements.nether.distract_piglin.description": "<PERSON><PERSON> golli<PERSON>", "advancements.nether.distract_piglin.title": "<PERSON><PERSON><PERSON> man <PERSON>", "advancements.nether.explore_nether.description": "<PERSON><PERSON><PERSON> buot net<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.explore_nether.title": "Báhkka turisttabáikkit", "advancements.nether.fast_travel.description": "Geavat Nethera mátkoš<PERSON>t 7 km dábálaš máilmmis", "advancements.nether.fast_travel.title": "Girddihit ilmmiid gaska", "advancements.nether.find_bastion.description": "Beasa Ladneb<PERSON>i", "advancements.nether.find_bastion.title": "<PERSON>ai leat go boares muittut", "advancements.nether.find_fortress.description": "Beasa nether ladne<PERSON><PERSON>i", "advancements.nether.find_fortress.title": "<PERSON><PERSON><PERSON>", "advancements.nether.get_wither_skull.description": "Fidne witherdákterikki oaiveskálžžu", "advancements.nether.get_wither_skull.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.loot_bastion.description": "Suolát giisá dávviriid ladnebáz<PERSON>", "advancements.nether.loot_bastion.title": "Soahtespiinnit", "advancements.nether.netherite_armor.description": "Oaččo buot netheriita suodjebiktasiid", "advancements.nether.netherite_armor.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.obtain_ancient_debris.description": "<PERSON><PERSON><PERSON> b<PERSON>", "advancements.nether.obtain_ancient_debris.title": "Čihkkon čiekŋalasas", "advancements.nether.obtain_blaze_rod.description": "<PERSON><PERSON><PERSON> buo<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.obtain_blaze_rod.title": "<PERSON><PERSON> dollii", "advancements.nether.obtain_crying_obsidian.description": "<PERSON>dne čierru obsidi<PERSON>a", "advancements.nether.obtain_crying_obsidian.title": "Gii lea cáhpamin lávkkiid?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON> ghast buo<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.return_to_sender.title": "<PERSON><PERSON><PERSON>", "advancements.nether.ride_strider.description": "Riide Gálašeaddji oaggunstávrrain mas skuibeguoppar", "advancements.nether.ride_strider.title": "<PERSON>án fatnasis han leat juolggit", "advancements.nether.ride_strider_in_overworld_lava.description": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> hiiiiirb<PERSON> guhk<PERSON> dá<PERSON><PERSON><PERSON><PERSON> m<PERSON><PERSON>", "advancements.nether.ride_strider_in_overworld_lava.title": "Juste nu movt ruovttus", "advancements.nether.root.description": "<PERSON><PERSON>", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "Ealáskahtte Withera", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "<PERSON><PERSON><PERSON>, bu<PERSON>tte oadjebasat Máilbmái... jađe lo<PERSON> gotte dan", "advancements.nether.uneasy_alliance.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.use_lodestone.description": "Guoska láidengeađggi kompássain", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Hea<PERSON><PERSON> ja ges div<PERSON><PERSON><PERSON>", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.deflect_arrow.description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.deflect_arrow.title": "<PERSON><PERSON><PERSON>, muhto ii fal odne", "advancements.story.enchant_item.description": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON>", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "<PERSON><PERSON>", "advancements.story.enter_the_end.title": "<PERSON><PERSON>?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ja mana <PERSON>", "advancements.story.enter_the_nether.title": "Mis lea dárbu mannat č<PERSON>", "advancements.story.follow_ender_eye.description": "Čuovo Endorčalmmi", "advancements.story.follow_ender_eye.title": "Čalbmái diet almmái", "advancements.story.form_obsidian.description": "<PERSON><PERSON><PERSON> obsid<PERSON>", "advancements.story.form_obsidian.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.iron_tools.description": "Buoridahtte iežat biikaákšu", "advancements.story.iron_tools.title": "Ii bat leat ruovdeguohkki", "advancements.story.lava_bucket.description": "<PERSON><PERSON><PERSON><PERSON> gievnni lavain", "advancements.story.lava_bucket.title": "<PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.title": "Diamánttat!", "advancements.story.mine_stone.description": "Rokka alccet geađggi iežat ođđa biikaákšuin", "advancements.story.mine_stone.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.obtain_armor.description": "Sudje iežat ruovdesuodjalusain", "advancements.story.obtain_armor.title": "Gárvot", "advancements.story.root.description": "<PERSON>án speala váibmu ja má<PERSON>as", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Diamántasuod<PERSON><PERSON> g<PERSON>", "advancements.story.shiny_gear.title": "<PERSON>am<PERSON><PERSON> hearr<PERSON>", "advancements.story.smelt_iron.description": "<PERSON>ddat ruovddi", "advancements.story.smelt_iron.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.upgrade_tools.description": "<PERSON><PERSON><PERSON> buoret <PERSON><PERSON>", "advancements.story.upgrade_tools.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.toast.challenge": "Hástalus g<PERSON>ju<PERSON>!", "advancements.toast.goal": "Ulbmil Oláhuvvon!", "advancements.toast.task": "Ovdáneapmi oláhuvvon!", "argument.anchor.invalid": "Boastu entitiaáŋkkor sajádat %s", "argument.angle.incomplete": "Ii leat gearggus (meroštallai 1 viŋkilčiega)", "argument.angle.invalid": "<PERSON>ast<PERSON>", "argument.block.id.invalid": "<PERSON>as blohkka '%s'", "argument.block.property.duplicate": "Heivehus '%s' s<PERSON><PERSON><PERSON><PERSON> duš<PERSON>e bidjot okte %s blohkkii", "argument.block.property.invalid": "%s bloh<PERSON> ii dohkket '%s' %s mearr<PERSON>dus<PERSON>i", "argument.block.property.novalue": "Meroštii mearrádusa '%s' mearrádusaide %s blohkas", "argument.block.property.unclosed": "<PERSON><PERSON><PERSON><PERSON><PERSON> ] loahpahusdihtii blohka mearrádusas", "argument.block.property.unknown": "%s bloh<PERSON> ii leat heivehus '%s'", "argument.block.tag.disallowed": "Mearkkat eai leat <PERSON>, du<PERSON><PERSON><PERSON> alb<PERSON> blohkat", "argument.color.invalid": "Amas ivndi '%s'", "argument.component.invalid": "Boasto chátta oassi: %s", "argument.criteria.invalid": "Amas kriteria '%s'", "argument.dimension.invalid": "<PERSON><PERSON> ilbmi '%s'", "argument.double.big": "Desimálanummar ii sáhte leat eanet go %s, gávnnai %s", "argument.double.low": "Desimálanummar ii sáhte leat unnánet go %s, gávnnai %s", "argument.entity.invalid": "<PERSON><PERSON><PERSON> namma dahje UUID", "argument.entity.notfound.entity": "Entitia ii gávdnon", "argument.entity.notfound.player": "Spealli ii gávdnon", "argument.entity.options.advancements.description": "Speallit geain ovdáneamit", "argument.entity.options.distance.description": "Gaska entitiai", "argument.entity.options.distance.negative": "Gaskka ii sáhte leat negatiiva", "argument.entity.options.dx.description": "Entitiat gaskkal x ja x + dx", "argument.entity.options.dy.description": "Entitiat gaskkal y ja y + dy", "argument.entity.options.dz.description": "Entitiat gaskkal z ja z + dz", "argument.entity.options.gamemode.description": "Speallit geain lea spe<PERSON>", "argument.entity.options.inapplicable": "'%s' he<PERSON><PERSON> ii sáhte adnot dás", "argument.entity.options.level.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.level.negative": "Dássi ii galggašii leat negatiiva", "argument.entity.options.limit.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON> entitiain čuvvot", "argument.entity.options.limit.toosmall": "<PERSON><PERSON><PERSON><PERSON> ferte leat eanet go 1", "argument.entity.options.mode.invalid": "Boastu dahje amas speallanhábmi '%s'", "argument.entity.options.name.description": "<PERSON><PERSON><PERSON> namma", "argument.entity.options.nbt.description": "Entetiat main NBT", "argument.entity.options.predicate.description": "Heivehallan ov<PERSON>", "argument.entity.options.scores.description": "Entetiat main čuoggát", "argument.entity.options.sort.description": "<PERSON><PERSON> entititaid", "argument.entity.options.sort.irreversible": "Boastu dahje amas oassi '%s'", "argument.entity.options.tag.description": "Entetiat main mearka", "argument.entity.options.team.description": "Entitiat joavkkus", "argument.entity.options.type.description": "Entitiat mat leat dán l<PERSON>gan", "argument.entity.options.type.invalid": "Boastu dahje amas entitia '%s'", "argument.entity.options.unknown": "<PERSON><PERSON> he<PERSON> '%s'", "argument.entity.options.unterminated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.valueless": "Meroštii mearrádusa '%s' mearrádussii", "argument.entity.options.x.description": "x sajádat", "argument.entity.options.x_rotation.description": "Entetia x jorran", "argument.entity.options.y.description": "y sajádat", "argument.entity.options.y_rotation.description": "Entetia y jorran", "argument.entity.options.z.description": "z sajádat", "argument.entity.selector.allEntities": "Buot entitiat", "argument.entity.selector.allPlayers": "<PERSON>uot speallit", "argument.entity.selector.missing": "<PERSON><PERSON><PERSON>š v<PERSON>", "argument.entity.selector.nearestEntity": "Lagamus entitiat", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON><PERSON>i", "argument.entity.selector.not_allowed": "Válljejeaddji ii leat dohkkehuvvon", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON><PERSON>i", "argument.entity.selector.self": "Dálá entitiat", "argument.entity.selector.unknown": "Amas v<PERSON>ll<PERSON> '%s'", "argument.entity.toomany": "<PERSON><PERSON><PERSON><PERSON> okta entitia lea do<PERSON><PERSON><PERSON>, muhto gea<PERSON>hu<PERSON><PERSON> válljejeaddji diktá eambbo go ovtta", "argument.enum.invalid": "Boastu árvu \"%s\"", "argument.float.big": "Govddodat ii ábut leat eanet go %s, čállui %s", "argument.float.low": "Govddodat ii ábut leat unnánet go %s, čállui %s", "argument.gamemode.invalid": "Amas speallanhápmi: %s", "argument.hexcolor.invalid": "Invalid hex color code '%s'", "argument.id.invalid": "Boastu ID", "argument.id.unknown": "Amas ID: %s", "argument.integer.big": "Ollesnummar ii sáhte leat eanet go %s, gávnnai %s", "argument.integer.low": "Ollesnummar ii sáhte leat unnit go %s, gávnnai %s", "argument.item.id.invalid": "<PERSON>as dá<PERSON> '%s'", "argument.item.tag.disallowed": "Mearkkat eai leat do<PERSON><PERSON>, du<PERSON><PERSON><PERSON> albma dávvirat", "argument.literal.incorrect": "Meroštii njuolgut %s", "argument.long.big": "Guhkku ii sáhte leat eanet go %s, oažžui %s", "argument.long.low": "Guhkku ii sáhte leat unnánet go %s, oažžui %s", "argument.message.too_long": "<PERSON><PERSON><PERSON> diehtu lei menddo guh<PERSON> (%s > eanemus %s bust<PERSON>va)", "argument.nbt.array.invalid": "Boastu array oassi '%s'", "argument.nbt.array.mixed": "Ii sáhte bidjat %s áđa %s áhtii", "argument.nbt.expected.compound": "Expected compound tag", "argument.nbt.expected.key": "<PERSON><PERSON><PERSON><PERSON><PERSON> boalu", "argument.nbt.expected.value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.nbt.list.mixed": "Ii sáhte bidjat %s áđa %s listui", "argument.nbt.trailing": "Ii me<PERSON>š<PERSON>u", "argument.player.entities": "<PERSON><PERSON><PERSON><PERSON> speallá<PERSON> sáhttet váikkuhuvvot dán kommán<PERSON>, muhto geavahuvvon válljejeaddji diktá entitiaid maid", "argument.player.toomany": "<PERSON><PERSON><PERSON><PERSON> okta spealli do<PERSON>huvvo, muhto geavahuvvon válljejeaddji diktá eambbo go ovtta", "argument.player.unknown": "Diet spealli ii gávdno", "argument.pos.missing.double": "Meroštii <PERSON>", "argument.pos.missing.int": "Meroštii blohka sajádasa", "argument.pos.mixed": "<PERSON>i sáhte seaguhit máilmmi ja b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> (buot ferte leat ^ dahje ii)", "argument.pos.outofbounds": "Saj<PERSON><PERSON>t lea olggobealde dohkkehuvvon rá<PERSON>id.", "argument.pos.outofworld": "Diet sajádat lea olggobealde dán máilmmi!", "argument.pos.unloaded": "Diet báiki ii leat láddan", "argument.pos2d.incomplete": "<PERSON>i <PERSON> g<PERSON> (vurdii 2 sajádaga)", "argument.pos3d.incomplete": "<PERSON>i <PERSON> g<PERSON> (vurdii 3 sajádaga)", "argument.range.empty": "Meroštii árvvu dahje gaskka árvvuin", "argument.range.ints": "<PERSON><PERSON><PERSON><PERSON> leat do<PERSON>, eai desimálal<PERSON>", "argument.range.swapped": "Minimumma ii sáhte leat stuor<PERSON>t go <PERSON>ma", "argument.resource.invalid_type": "\"%s\" oasis lea vearu tiipa \"%s\" (vurdiimet \"%s\")", "argument.resource.not_found": "Ii sáhte gávdnat \"%s\" oasi mii lea \"%s\" šlád<PERSON>", "argument.resource_or_id.failed_to_parse": "Huksehusa dulkon ii lihkostuvvan: %s", "argument.resource_or_id.invalid": "Boastu id dahje me<PERSON>a", "argument.resource_or_id.no_such_element": "Can't find element '%s' in registry '%s'", "argument.resource_selector.not_found": "No matches for selector '%s' of type '%s'", "argument.resource_tag.invalid_type": "\"%s\" mearkkas lea vearu tiipa \"%s\" (vurdiimet \"%s\")", "argument.resource_tag.not_found": "Ii sáhte gávdnat \"%s\" oasi mii lea \"%s\" šlád<PERSON>", "argument.rotation.incomplete": "<PERSON>i <PERSON> g<PERSON> (vurdii 2 sajádaga)", "argument.scoreHolder.empty": "Ii gávdnon makk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.scoreboardDisplaySlot.invalid": "Amas <PERSON>ádat '%s'", "argument.style.invalid": "Boastu hápmi: %s", "argument.time.invalid_tick_count": "Coahkkinlohku ferte leat eahpenegatiiva", "argument.time.invalid_unit": "<PERSON><PERSON><PERSON>", "argument.time.tick_count_too_low": "Coahkkinlohku ii ábut leat unnit go %s, gávnnai %s", "argument.uuid.invalid": "Boastu UUID", "argument.waypoint.invalid": "Selected entity is not a waypoint", "arguments.block.tag.unknown": "<PERSON>as blohkka mearka '%s'", "arguments.function.tag.unknown": "<PERSON><PERSON> '%s'", "arguments.function.unknown": "Amas doaibma %s", "arguments.item.component.expected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments.item.component.malformed": "Billašuvvan '%s' oassi: '%s'", "arguments.item.component.repeated": "<PERSON><PERSON><PERSON><PERSON><PERSON> '%s' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, muhto du<PERSON> okta árvu sáhttá dárkilit čilgejuvvot", "arguments.item.component.unknown": "<PERSON>as <PERSON> '%s'", "arguments.item.malformed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>: '%s'", "arguments.item.overstacked": "%s sáhttá dušše čoggot gittálaga %s radjai", "arguments.item.predicate.malformed": "Billašuvvan '%s' oassi: '%s'", "arguments.item.predicate.unknown": "Amas dávvirpredikáhtta '%s'", "arguments.item.tag.unknown": "Amas dávvirmearka '%s'", "arguments.nbtpath.node.invalid": "Boastu NBT elementtaluodda", "arguments.nbtpath.nothing_found": "Ii gávdnan elemeanta mii heivii %s", "arguments.nbtpath.too_deep": "NBT boađus lea juo garrasit čatnašuvvan", "arguments.nbtpath.too_large": "NBT boa<PERSON>us lea menddo stuoris", "arguments.objective.notFound": "Amas tabealla objektiiva '%s'", "arguments.objective.readonly": "Tabealla objektiiva '%s' lea du<PERSON><PERSON> lohkan modusis", "arguments.operation.div0": "Ii s<PERSON>hte juohkit nullain", "arguments.operation.invalid": "<PERSON><PERSON><PERSON><PERSON>", "arguments.swizzle.invalid": "<PERSON><PERSON><PERSON>, vur<PERSON><PERSON> dán l<PERSON> 'x', 'y' ja 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "Su<PERSON><PERSON>lus<PERSON><PERSON><PERSON> na<PERSON>", "attribute.name.attack_damage": "Časkinvahá", "attribute.name.attack_knockback": "Hoiganvahá", "attribute.name.attack_speed": "Časkinleaktu", "attribute.name.block_break_speed": "Blohkkacuvkenleaktu", "attribute.name.block_interaction_range": "Blohka guoskkahangaska", "attribute.name.burning_time": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.camera_distance": "Kámer<PERSON>", "attribute.name.entity_interaction_range": "Blohka guoskkahangaska", "attribute.name.explosion_knockback_resistance": "Bávkkanasa bálkestangierde<PERSON>š<PERSON>", "attribute.name.fall_damage_multiplier": "Gahččanbávččagahttima geardduheaddji", "attribute.name.flying_speed": "Girdinleaktu", "attribute.name.follow_range": "<PERSON><PERSON><PERSON>", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_damage": "Čask<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_knockback": "Hoigadahttinhuškunvuohki", "attribute.name.generic.attack_speed": "Časkinleaktu", "attribute.name.generic.block_interaction_range": "Blohka guoskkahangaska", "attribute.name.generic.burning_time": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.entity_interaction_range": "Entitiija g<PERSON>skkahangaska", "attribute.name.generic.explosion_knockback_resistance": "Bávkkanasa bálkestangierde<PERSON>š<PERSON>", "attribute.name.generic.fall_damage_multiplier": "Gahččanbávččagahttima geardduheaddji", "attribute.name.generic.flying_speed": "Girdinleaktu", "attribute.name.generic.follow_range": "Návddiid Čuovvolangaska", "attribute.name.generic.gravity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.jump_strength": "Njuikengivrodat", "attribute.name.generic.knockback_resistance": "Hoigadan Gierdevašvuohta", "attribute.name.generic.luck": "Lihkku", "attribute.name.generic.max_absorption": "<PERSON><PERSON><PERSON>", "attribute.name.generic.max_health": "<PERSON>", "attribute.name.generic.movement_efficiency": "Lihkadusleaktu", "attribute.name.generic.movement_speed": "Leaktu", "attribute.name.generic.oxygen_bonus": "Liigeáibmu", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON><PERSON><PERSON><PERSON>š <PERSON>", "attribute.name.generic.scale": "Sturrodat", "attribute.name.generic.step_height": "Lávkki allodat", "attribute.name.generic.water_movement_efficiency": "Vuojadanleaktu", "attribute.name.gravity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.horse.jump_strength": "<PERSON><PERSON><PERSON>", "attribute.name.jump_strength": "Njuikengivrodat", "attribute.name.knockback_resistance": "Hoigadangierdevašvuohta", "attribute.name.luck": "Lihkku", "attribute.name.max_absorption": "<PERSON><PERSON><PERSON>", "attribute.name.max_health": "<PERSON><PERSON> heagga", "attribute.name.mining_efficiency": "Rogganleaktu", "attribute.name.movement_efficiency": "Lihkadanleaktu", "attribute.name.movement_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.oxygen_bonus": "Liigeáibmu", "attribute.name.player.block_break_speed": "Blohkkacuvkenleaktu", "attribute.name.player.block_interaction_range": "Blohka guoskkahangaska", "attribute.name.player.entity_interaction_range": "Entitiija g<PERSON>skkahangaska", "attribute.name.player.mining_efficiency": "Rogganleaktu", "attribute.name.player.sneaking_speed": "Njáhkanleaktu", "attribute.name.player.submerged_mining_speed": "Čázevuoli rogganleaktu", "attribute.name.player.sweeping_damage_ratio": "Fuoikuma bá<PERSON>ččagahttinmearri", "attribute.name.safe_fall_distance": "<PERSON><PERSON><PERSON><PERSON><PERSON>š <PERSON>", "attribute.name.scale": "Sturrodat", "attribute.name.sneaking_speed": "Njáhkanleaktu", "attribute.name.spawn_reinforcements": "Jámeža <PERSON>", "attribute.name.step_height": "Lávkenallodat", "attribute.name.submerged_mining_speed": "Čázevuoli rogganleaktu", "attribute.name.sweeping_damage_ratio": "Fuoikuma bá<PERSON>ččagahttinmearri", "attribute.name.tempt_range": "<PERSON><PERSON><PERSON>", "attribute.name.water_movement_efficiency": "Vuojadanleaktu", "attribute.name.waypoint_receive_range": "Waypoint Receive Range", "attribute.name.waypoint_transmit_range": "Waypoint Transmit Range", "attribute.name.zombie.spawn_reinforcements": "Jámeža liigeveahkit", "biome.minecraft.badlands": "Ávdineana", "biome.minecraft.bamboo_jungle": "Suhkkes Bamboovuovdi", "biome.minecraft.basalt_deltas": "Basaltaoaivvuš", "biome.minecraft.beach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.birch_forest": "Soahkevuovdi", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cold_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.crimson_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dark_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ovdi", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.deep_lukewarm_ocean": "Čiek<PERSON><PERSON>", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.desert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "Goaikogeađgg<PERSON>", "biome.minecraft.end_barrens": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.end_highlands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.end_midlands": "Endora Gaskkaeatnamat", "biome.minecraft.eroded_badlands": "<PERSON><PERSON><PERSON>", "biome.minecraft.flower_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.forest": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON><PERSON> várit", "biome.minecraft.frozen_river": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.grove": "<PERSON><PERSON><PERSON>", "biome.minecraft.ice_spikes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.jagged_peaks": "Juovvav<PERSON><PERSON>", "biome.minecraft.jungle": "Suhkkesvuovdi", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON>", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.mangrove_swamp": "Mangrovesuotnju", "biome.minecraft.meadow": "<PERSON><PERSON><PERSON>", "biome.minecraft.mushroom_fields": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.nether_wastes": "<PERSON><PERSON>", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_pine_taiga": "<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_spruce_taiga": "<PERSON><PERSON><PERSON>", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON>", "biome.minecraft.plains": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.river": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.small_end_islands": "<PERSON><PERSON>", "biome.minecraft.snowy_beach": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.snowy_plains": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.snowy_slopes": "Muohtavielttit", "biome.minecraft.snowy_taiga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.soul_sand_valley": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.sparse_jungle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.stony_peaks": "Báktevárit", "biome.minecraft.stony_shore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.sunflower_plains": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.taiga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.the_end": "<PERSON><PERSON>", "biome.minecraft.the_void": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.warm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_gravelly_hills": "Roaiskočievrábolnnit", "biome.minecraft.windswept_hills": "Roaiskobolnnit", "biome.minecraft.windswept_savanna": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.wooded_badlands": "<PERSON><PERSON><PERSON><PERSON>vuovdi", "block.minecraft.acacia_button": "Acaciaboallu", "block.minecraft.acacia_door": "A<PERSON><PERSON><PERSON>", "block.minecraft.acacia_fence": "<PERSON><PERSON>", "block.minecraft.acacia_fence_gate": "A<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> acaciagalba", "block.minecraft.acacia_leaves": "Acacialasttat", "block.minecraft.acacia_log": "Acaciabelko", "block.minecraft.acacia_planks": "Acaciafiellut", "block.minecraft.acacia_pressure_plate": "Acaciadeattopl<PERSON>ht<PERSON>", "block.minecraft.acacia_sapling": "Acaciamuoraš", "block.minecraft.acacia_sign": "Acaciagalba", "block.minecraft.acacia_slab": "Acaciapl<PERSON><PERSON><PERSON>", "block.minecraft.acacia_stairs": "Acaciatráhppá", "block.minecraft.acacia_trapdoor": "Acacialuŋká", "block.minecraft.acacia_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>is", "block.minecraft.acacia_wall_sign": "Heŋgojuvvon Acaciagalba", "block.minecraft.acacia_wood": "Acaciamuorra", "block.minecraft.activator_rail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.air": "Áibmu", "block.minecraft.allium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.amethyst_block": "Ametistablohkka", "block.minecraft.amethyst_cluster": "Ametistačoaltu", "block.minecraft.ancient_debris": "<PERSON><PERSON><PERSON>", "block.minecraft.andesite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.andesite_slab": "Ándasihttapláhtta", "block.minecraft.andesite_stairs": "Ándasihttatráhppá", "block.minecraft.andesite_wall": "Ándasihtta <PERSON>", "block.minecraft.anvil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Čatnasan Meluvdna Máddu", "block.minecraft.attached_pumpkin_stem": "Čatnas<PERSON>", "block.minecraft.azalea": "Asalea", "block.minecraft.azalea_leaves": "Asalealasttat", "block.minecraft.azure_bluet": "Nuorsá", "block.minecraft.bamboo": "Bamboa", "block.minecraft.bamboo_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_button": "Bámbub<PERSON>ll<PERSON>", "block.minecraft.bamboo_door": "Bámbuuksa", "block.minecraft.bamboo_fence": "Bámb<PERSON><PERSON>idi", "block.minecraft.bamboo_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_mosaic": "Bámbumosaihkka", "block.minecraft.bamboo_mosaic_slab": "Bámbumosaih<PERSON>alli", "block.minecraft.bamboo_mosaic_stairs": "Bámbumosaih<PERSON>", "block.minecraft.bamboo_planks": "Bámbufiellut", "block.minecraft.bamboo_pressure_plate": "Bámbudeaddopláht<PERSON>", "block.minecraft.bamboo_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_sign": "Bámbugalba", "block.minecraft.bamboo_slab": "Bámbubealli", "block.minecraft.bamboo_stairs": "Bámbutr<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_trapdoor": "Bámbuluŋká", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.black": "Áibbas čáhppes vuolimus <PERSON>i", "block.minecraft.banner.base.blue": "Áibbas alit vuolimus <PERSON>i", "block.minecraft.banner.base.brown": "Áibbas ru<PERSON><PERSON> vuolimus <PERSON>i", "block.minecraft.banner.base.cyan": "Áibbas čuv<PERSON><PERSON> vuolimus <PERSON>i", "block.minecraft.banner.base.gray": "Áibbas r<PERSON><PERSON> vuo<PERSON>i", "block.minecraft.banner.base.green": "Áibbas ruoná vuolimus o<PERSON>i", "block.minecraft.banner.base.light_blue": "Áibbas čuv<PERSON><PERSON> vuolimus <PERSON>i", "block.minecraft.banner.base.light_gray": "Áibbas čuvges<PERSON><PERSON><PERSON> vuolim<PERSON>i", "block.minecraft.banner.base.lime": "Áibbas čugesruoná vuolimus oassi", "block.minecraft.banner.base.magenta": "Áibbas čuvges<PERSON>á<PERSON><PERSON> vuolimus o<PERSON>i", "block.minecraft.banner.base.orange": "Áibbas runta vuolimus <PERSON>i", "block.minecraft.banner.base.pink": "Áibbas čuv<PERSON><PERSON><PERSON> vuolimus <PERSON>i", "block.minecraft.banner.base.purple": "Áibbas sá<PERSON><PERSON> vuolimus <PERSON>i", "block.minecraft.banner.base.red": "Áibbas ruk<PERSON> vuo<PERSON>i", "block.minecraft.banner.base.white": "Áibbas vilges vuolimus o<PERSON>i", "block.minecraft.banner.base.yellow": "Áibbas fiskes vuolimus <PERSON>i", "block.minecraft.banner.border.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.blue": "<PERSON><PERSON>", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.orange": "<PERSON><PERSON>", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.black": "Čáhppes Muvraminsttar", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON>", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.cyan": "Turkii<PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.light_blue": "Čuvges<PERSON>t Mu<PERSON>ttar", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.magenta": "Čuvgessáhppes Muvraminsttar", "block.minecraft.banner.bricks.orange": "<PERSON><PERSON>", "block.minecraft.banner.bricks.pink": "Čuvgesrukses <PERSON>", "block.minecraft.banner.bricks.purple": "Sá<PERSON><PERSON>", "block.minecraft.banner.bricks.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON>s <PERSON>", "block.minecraft.banner.circle.black": "Čáhppes Guovdogierdu", "block.minecraft.banner.circle.blue": "<PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "Turkii<PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "Čuvges<PERSON><PERSON>", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.lime": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "Čuvgessáhppes Guovdogierdu", "block.minecraft.banner.circle.orange": "<PERSON><PERSON>", "block.minecraft.banner.circle.pink": "Čuvges<PERSON><PERSON>", "block.minecraft.banner.circle.purple": "Sáhppes Guo<PERSON>", "block.minecraft.banner.circle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.black": "Čáhppes Creeperminsttar", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON>", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.cyan": "Turkii<PERSON>", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>minsttar", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.lime": "Čuvges<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.magenta": "Čuvgessáhppes Creeperminsttar", "block.minecraft.banner.creeper.orange": "Runta Creeperminsttar", "block.minecraft.banner.creeper.pink": "Čuvgesrukses Creeperminsttar", "block.minecraft.banner.creeper.purple": "Sáhppes Creeperminsttar", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON>s Creeperminsttar", "block.minecraft.banner.cross.black": "Čáhppes Ruossa", "block.minecraft.banner.cross.blue": "<PERSON><PERSON>", "block.minecraft.banner.cross.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "Turkiisa Rista", "block.minecraft.banner.cross.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.magenta": "Čuvgessáhppes Ruossa", "block.minecraft.banner.cross.orange": "<PERSON><PERSON>", "block.minecraft.banner.cross.pink": "Čuvges<PERSON><PERSON>", "block.minecraft.banner.cross.purple": "<PERSON>á<PERSON><PERSON>", "block.minecraft.banner.cross.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.black": "Čáhppes Barvvasravda", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.brown": "Ruš<PERSON>vvasra<PERSON>", "block.minecraft.banner.curly_border.cyan": "Turkiisa Barvvasravda", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.light_blue": "Čuvgesalit Barvvasravda", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.lime": "Čuvgesruoná <PERSON>", "block.minecraft.banner.curly_border.magenta": "Čuvgessáhppes Barvvasravda", "block.minecraft.banner.curly_border.orange": "Runta Barvvasravda", "block.minecraft.banner.curly_border.pink": "Čuvgesrukses Bar<PERSON>vasrav<PERSON>", "block.minecraft.banner.curly_border.purple": "Sáhppes Barvvasravda", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.yellow": "Fiskes Barvvasravda", "block.minecraft.banner.diagonal_left.black": "Čáhppes Bajit <PERSON>", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.cyan": "Turkiisa <PERSON>", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.light_blue": "Čuvgesalit Ba<PERSON>", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.lime": "Čuvgesruoná Ba<PERSON>", "block.minecraft.banner.diagonal_left.magenta": "Čuvgessáhppes Bajit <PERSON>", "block.minecraft.banner.diagonal_left.orange": "Runta Bajit <PERSON>", "block.minecraft.banner.diagonal_left.pink": "Čuvgesrukses Ba<PERSON>", "block.minecraft.banner.diagonal_left.purple": "Sáhppes Bajit <PERSON>", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> Bajit <PERSON>", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON> Ba<PERSON>", "block.minecraft.banner.diagonal_right.black": "Čáhppes Bajit <PERSON>", "block.minecraft.banner.diagonal_right.blue": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.cyan": "Turkiisa Ba<PERSON>", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON> Ba<PERSON>", "block.minecraft.banner.diagonal_right.light_blue": "Čuvgesalit <PERSON>", "block.minecraft.banner.diagonal_right.light_gray": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.lime": "Čuvgesruoná Ba<PERSON>", "block.minecraft.banner.diagonal_right.magenta": "Čuvgessáhppes Bajit <PERSON>", "block.minecraft.banner.diagonal_right.orange": "Runta Bajit <PERSON>", "block.minecraft.banner.diagonal_right.pink": "Čuvgesrukses Ba<PERSON>", "block.minecraft.banner.diagonal_right.purple": "Sáhppes Bajit <PERSON>", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.white": "Vilges Bajit <PERSON>", "block.minecraft.banner.diagonal_right.yellow": "Fiskes Bajit <PERSON>", "block.minecraft.banner.diagonal_up_left.black": "Čáhppes Vuolit Gurutbealečiehka", "block.minecraft.banner.diagonal_up_left.blue": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.brown": "Ruškes Vuolit Gurutbealeč<PERSON>hka", "block.minecraft.banner.diagonal_up_left.cyan": "Turkiisa Vuolit Gurutbealeč<PERSON>hka", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON> V<PERSON>", "block.minecraft.banner.diagonal_up_left.light_blue": "Čuvgesalit Vuolit Gurutbealečiehka", "block.minecraft.banner.diagonal_up_left.light_gray": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.lime": "Čuvgesruoná Vuolit Gurutbealečiehka", "block.minecraft.banner.diagonal_up_left.magenta": "Čuvgessáhppes Vuolit Gurutbealečiehka", "block.minecraft.banner.diagonal_up_left.orange": "Runta Vuolit Gurutbealeč<PERSON>hka", "block.minecraft.banner.diagonal_up_left.pink": "Čuvgesrukses Vuolit Gurutbealečiehka", "block.minecraft.banner.diagonal_up_left.purple": "Sáhppes Vuolit Gurutbealečiehka", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.white": "Vilges Vuolit <PERSON>č<PERSON>ka", "block.minecraft.banner.diagonal_up_left.yellow": "Fiskes Vuolit <PERSON>aleč<PERSON>hka", "block.minecraft.banner.diagonal_up_right.black": "Čáhppes Vuolit <PERSON>ka", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.brown": "Ruš<PERSON> V<PERSON>", "block.minecraft.banner.diagonal_up_right.cyan": "Turkiisa Vuolit <PERSON>", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.green": "<PERSON>uo<PERSON> V<PERSON>", "block.minecraft.banner.diagonal_up_right.light_blue": "Čuvgesalit Vuolit O<PERSON>šbealečiehka", "block.minecraft.banner.diagonal_up_right.light_gray": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.lime": "Čuvgesruoná V<PERSON>č<PERSON>hka", "block.minecraft.banner.diagonal_up_right.magenta": "Čuvgessáhppes Vuolit Olgešbealečiehka", "block.minecraft.banner.diagonal_up_right.orange": "Runta Vuolit <PERSON>č<PERSON>hka", "block.minecraft.banner.diagonal_up_right.pink": "Čuvgesrukses Vuolit O<PERSON>šbealečiehka", "block.minecraft.banner.diagonal_up_right.purple": "Sáhppes Vuolit <PERSON>", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.white": "Runta Vuolit <PERSON>č<PERSON>hka", "block.minecraft.banner.diagonal_up_right.yellow": "Fiskes Vuolit <PERSON>", "block.minecraft.banner.flow.black": "Čáhppes golgan", "block.minecraft.banner.flow.blue": "<PERSON><PERSON> go<PERSON>gan", "block.minecraft.banner.flow.brown": "<PERSON><PERSON><PERSON><PERSON> golgan", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON><PERSON><PERSON> golgan", "block.minecraft.banner.flow.gray": "<PERSON><PERSON><PERSON> go<PERSON>", "block.minecraft.banner.flow.green": "<PERSON><PERSON><PERSON> golgan", "block.minecraft.banner.flow.light_blue": "Čuvgesalit golgan", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON>v<PERSON><PERSON><PERSON><PERSON> go<PERSON>gan", "block.minecraft.banner.flow.lime": "Čuvgesru<PERSON><PERSON> golgan", "block.minecraft.banner.flow.magenta": "Čuvgessáhppes golgan", "block.minecraft.banner.flow.orange": "<PERSON><PERSON> golgan", "block.minecraft.banner.flow.pink": "Čuvgesrukses golgan", "block.minecraft.banner.flow.purple": "Sáhp<PERSON> golgan", "block.minecraft.banner.flow.red": "<PERSON><PERSON><PERSON> golgan", "block.minecraft.banner.flow.white": "Vil<PERSON> golgan", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON><PERSON> golgan", "block.minecraft.banner.flower.black": "Čáhppes Lieđđeminsttar", "block.minecraft.banner.flower.blue": "<PERSON><PERSON>", "block.minecraft.banner.flower.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.cyan": "Turkiisa <PERSON>", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.light_gray": "<PERSON><PERSON>v<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.lime": "Čuvgesruon<PERSON>", "block.minecraft.banner.flower.magenta": "Čuvgessáhppes Lieđđeminsttar", "block.minecraft.banner.flower.orange": "<PERSON><PERSON>", "block.minecraft.banner.flower.pink": "Čuvgesrukses <PERSON>", "block.minecraft.banner.flower.purple": "Sáhppes Lieđđeminsttar", "block.minecraft.banner.flower.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.black": "Čáhppes Máilmmespábba", "block.minecraft.banner.globe.blue": "<PERSON><PERSON>", "block.minecraft.banner.globe.brown": "<PERSON><PERSON><PERSON><PERSON> Máilmmespá<PERSON>", "block.minecraft.banner.globe.cyan": "Turkiisa Máilmmespábba", "block.minecraft.banner.globe.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.light_blue": "Čuvgesalit Máilmmespábba", "block.minecraft.banner.globe.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.lime": "Čuvgesruoná <PERSON>áilmmespábba", "block.minecraft.banner.globe.magenta": "Čuvgessáhppes Máilmmespábba", "block.minecraft.banner.globe.orange": "Runta Máilmmespábba", "block.minecraft.banner.globe.pink": "Čuvgesrukses Máilmmespábba", "block.minecraft.banner.globe.purple": "Sáhppes Máilmmespábba", "block.minecraft.banner.globe.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.white": "<PERSON><PERSON>ges Máilmmespábba", "block.minecraft.banner.globe.yellow": "<PERSON><PERSON>s Máilmmespábba", "block.minecraft.banner.gradient.black": "Čáhppes Bajitguovsomolsun", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON>", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.lime": "Čuv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.magenta": "Čuvgessáhppes Bajitguovsomolsun", "block.minecraft.banner.gradient.orange": "<PERSON><PERSON>", "block.minecraft.banner.gradient.pink": "Čuvges<PERSON><PERSON>", "block.minecraft.banner.gradient.purple": "Sáhppes Bajitguovsomolsun", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.white": "<PERSON><PERSON>", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.black": "Čáhppes Vuođđoguovsomolsun", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.cyan": "Turkiisa Vuođđog<PERSON>v<PERSON>lsun", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.light_blue": "Čuvges<PERSON><PERSON>", "block.minecraft.banner.gradient_up.light_gray": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.lime": "Čuvgesruon<PERSON><PERSON>", "block.minecraft.banner.gradient_up.magenta": "Čuvgessáhppes Vuođđoguovsomolsun", "block.minecraft.banner.gradient_up.orange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.pink": "Čuvgesrukses <PERSON>", "block.minecraft.banner.gradient_up.purple": "Sáhppes Vuođđ<PERSON><PERSON>un", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.black": "Čáhp<PERSON>", "block.minecraft.banner.guster.blue": "<PERSON><PERSON>", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ji<PERSON>", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON>vges<PERSON><PERSON><PERSON><PERSON> jiella", "block.minecraft.banner.guster.orange": "<PERSON><PERSON>", "block.minecraft.banner.guster.pink": "<PERSON>uv<PERSON><PERSON><PERSON> ji<PERSON>", "block.minecraft.banner.guster.purple": "<PERSON><PERSON><PERSON><PERSON> jiella", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON>il<PERSON>", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON> jiella", "block.minecraft.banner.half_horizontal.black": "Čáhppes Badjebealli", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.brown": "Ruškes <PERSON>all<PERSON>", "block.minecraft.banner.half_horizontal.cyan": "Turk<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.light_blue": "Čuvgesalit Badjebealli", "block.minecraft.banner.half_horizontal.light_gray": "<PERSON><PERSON>v<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.lime": "Čuvgesruoná <PERSON>", "block.minecraft.banner.half_horizontal.magenta": "Čuvgessáhppes Badjebealli", "block.minecraft.banner.half_horizontal.orange": "Runta Badjebealli", "block.minecraft.banner.half_horizontal.pink": "Čuvgesrukses Badjebealli", "block.minecraft.banner.half_horizontal.purple": "Sáhppes Badjebealli", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.white": "Vilges Badjebealli", "block.minecraft.banner.half_horizontal.yellow": "Fiskes Badjebealli", "block.minecraft.banner.half_horizontal_bottom.black": "Čáhppes Vuollebealli", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Čuvgesalit V<PERSON>llebealli", "block.minecraft.banner.half_horizontal_bottom.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.lime": "Čuvgesru<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.magenta": "Čuvgessáhppes Vuollebealli", "block.minecraft.banner.half_horizontal_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.pink": "Čuvgesrukses <PERSON>alli", "block.minecraft.banner.half_horizontal_bottom.purple": "Sáhp<PERSON>", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.black": "Čáhppes Gurutbealli", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.brown": "Ruškes <PERSON>bealli", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.light_blue": "Čuvgesalit Gurutbealli", "block.minecraft.banner.half_vertical.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.lime": "Čuvgesruon<PERSON>", "block.minecraft.banner.half_vertical.magenta": "Čuvgessáhppes Gurutbealli", "block.minecraft.banner.half_vertical.orange": "Runta Gurutbealli", "block.minecraft.banner.half_vertical.pink": "Čuvgesrukses <PERSON>alli", "block.minecraft.banner.half_vertical.purple": "Sáhppes Gurutbealli", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON>s Gurutbealli", "block.minecraft.banner.half_vertical_right.black": "Čáhppes Olgešbealli", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.cyan": "Turkii<PERSON>", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "Čuvgesalit <PERSON>alli", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.lime": "Čuvgesru<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.magenta": "Čuvgessáhppes Olgešbealli", "block.minecraft.banner.half_vertical_right.orange": "Runta Olgešbealli", "block.minecraft.banner.half_vertical_right.pink": "Čuvgesrukses <PERSON>all<PERSON>", "block.minecraft.banner.half_vertical_right.purple": "Sáhppes Olgešbealli", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.yellow": "Fiskes Olgešbealli", "block.minecraft.banner.mojang.black": "Čáhppes <PERSON>ttar", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON>", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.magenta": "Čuvgessáhppes Mojangminsttar", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON>", "block.minecraft.banner.mojang.pink": "Čuvges<PERSON><PERSON>", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.black": "Čáhppes njunni", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON>", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON>uv<PERSON><PERSON><PERSON> n<PERSON>", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.light_blue": "<PERSON>uv<PERSON><PERSON><PERSON> n<PERSON>", "block.minecraft.banner.piglin.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>", "block.minecraft.banner.piglin.magenta": "Čuvgessáhppes njunni", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON>", "block.minecraft.banner.piglin.pink": "Čuvges<PERSON><PERSON> n<PERSON>", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON><PERSON><PERSON> njun<PERSON>", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON> n<PERSON>", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON><PERSON> n<PERSON>", "block.minecraft.banner.rhombus.black": "Čáhppes Dikkel", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_blue": "<PERSON>uv<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.lime": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.magenta": "Čuvgessáhppes Dikkel", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.pink": "Čuvgesrukses <PERSON>", "block.minecraft.banner.rhombus.purple": "Sáhppes Di<PERSON>", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.black": "Čáhppes Oaiveskálžominsttar", "block.minecraft.banner.skull.blue": "<PERSON><PERSON>ž<PERSON>nsttar", "block.minecraft.banner.skull.brown": "Ruškes Oaiveskálžominsttar", "block.minecraft.banner.skull.cyan": "Turkiisa Oaiveskálžominsttar", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON>ž<PERSON>nsttar", "block.minecraft.banner.skull.light_blue": "Čuvgesalit Oaiveskálžominsttar", "block.minecraft.banner.skull.light_gray": "Čuvges<PERSON><PERSON><PERSON>ž<PERSON>nsttar", "block.minecraft.banner.skull.lime": "Čuvgesruoná <PERSON>álžominsttar", "block.minecraft.banner.skull.magenta": "Čuvgessáhppes Oaiveskálžominsttar", "block.minecraft.banner.skull.orange": "Runta Oaiveskálžominsttar", "block.minecraft.banner.skull.pink": "Čuvgesrukses Oaiveskálžominsttar", "block.minecraft.banner.skull.purple": "Sáhppes Oaiveskálžominsttar", "block.minecraft.banner.skull.red": "<PERSON><PERSON><PERSON>ž<PERSON>tar", "block.minecraft.banner.skull.white": "Vilges Oaiveskálžominsttar", "block.minecraft.banner.skull.yellow": "Fiskes Oaiveskálžominsttar", "block.minecraft.banner.small_stripes.black": "Čáhppes ráidá", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON><PERSON> \nráidá", "block.minecraft.banner.small_stripes.cyan": "Turkiisa <PERSON>", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.light_blue": "Čuvgesalit Ráida", "block.minecraft.banner.small_stripes.light_gray": "Čuovges<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.lime": "Čuvgesruoná <PERSON>", "block.minecraft.banner.small_stripes.magenta": "Čuvgessáhppes Ráida", "block.minecraft.banner.small_stripes.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.pink": "Guvgeruoksat ráidá", "block.minecraft.banner.small_stripes.purple": "Sáhppat ráidá", "block.minecraft.banner.small_stripes.red": "Ruoksat ráidá", "block.minecraft.banner.small_stripes.white": "Vielgat ráidá", "block.minecraft.banner.small_stripes.yellow": "Fiskat ráidá", "block.minecraft.banner.square_bottom_left.black": "Čáhppes Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.blue": "Alit Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.brown": "Ruškes Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.cyan": "Turkiisa Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.gray": "<PERSON><PERSON><PERSON>lle<PERSON>č<PERSON>", "block.minecraft.banner.square_bottom_left.green": "Ruoná Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.light_blue": "Čuvgesalit Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.light_gray": "Čuvgesránes Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.lime": "Čuvgesruoná Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.magenta": "Čuvgessáhppes Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.orange": "Runta Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.pink": "Čuvgesrukses Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.purple": "Sáhppes Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.red": "Rukses Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.white": "Vilges Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_left.yellow": "Fiskes Gurutbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.black": "Čáhppes Olgešbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.blue": "Alit Olgešbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.brown": "Ruškes Olgešbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.cyan": "Turkiisa Olgešbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.gray": "<PERSON><PERSON><PERSON> Vuollegeahči", "block.minecraft.banner.square_bottom_right.green": "Ruoná Olgešbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.light_blue": "Čuvgesalit Olgešbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.light_gray": "Čuvgesrán<PERSON> Vuollegeahči", "block.minecraft.banner.square_bottom_right.lime": "Čuvgesruoná Olgešbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.magenta": "Čuvgessáhppes Olgešbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.orange": "Runta Olgešbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.pink": "Čuvgesrukses Olgešbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.purple": "Sáhppes Olgešbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.red": "Rukses <PERSON>ale Vuollegeahči", "block.minecraft.banner.square_bottom_right.white": "Vilges Olgešbeale Vuollegeahči", "block.minecraft.banner.square_bottom_right.yellow": "Fiskes Olgešbeale Vuollegeahči", "block.minecraft.banner.square_top_left.black": "Čáhppes Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_left.blue": "Alit Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_left.brown": "Ruškes Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_left.cyan": "Turkiisa Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_left.gray": "<PERSON><PERSON><PERSON> Badjegeahči", "block.minecraft.banner.square_top_left.green": "Ruoná Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_left.light_blue": "Čuvgesalit Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_left.light_gray": "Čuvgesránes <PERSON>tbe<PERSON> Badjegeahči", "block.minecraft.banner.square_top_left.lime": "Čuvgesruoná Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_left.magenta": "Čuvgessáhppes Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_left.orange": "Runta Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_left.pink": "Čuvgesrukses Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_left.purple": "Sáhppes Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_left.red": "Rukses <PERSON>tbeale Badjegeahči", "block.minecraft.banner.square_top_left.white": "Vilges Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_left.yellow": "Fiskes Gurutbeale Badjegeahči", "block.minecraft.banner.square_top_right.black": "Čáhppes Olgešbeale Badjegeahči", "block.minecraft.banner.square_top_right.blue": "<PERSON><PERSON>ale Badjegeahči", "block.minecraft.banner.square_top_right.brown": "Ruškes Olgešbeale Badjegeahči", "block.minecraft.banner.square_top_right.cyan": "Turkiisa Olgešbeale Badjegeahči", "block.minecraft.banner.square_top_right.gray": "<PERSON><PERSON><PERSON> Badjegeahči", "block.minecraft.banner.square_top_right.green": "Ruoná Olgešbeale Badjegeahči", "block.minecraft.banner.square_top_right.light_blue": "Čuvgesalit Olgešbeale Badjegeahči", "block.minecraft.banner.square_top_right.light_gray": "Čuvges<PERSON><PERSON><PERSON> Badjegeahči", "block.minecraft.banner.square_top_right.lime": "Čuvgesruoná <PERSON>šbeale Badjegeahči", "block.minecraft.banner.square_top_right.magenta": "Čuvgessáhppes Olgešbeale Badjegeahči", "block.minecraft.banner.square_top_right.orange": "Runta Olgešbeale Badjegeahči", "block.minecraft.banner.square_top_right.pink": "Čuvgesrukses Olgešbeale Badjegeahči", "block.minecraft.banner.square_top_right.purple": "Sáhppes Olgešbeale Badjegeahči", "block.minecraft.banner.square_top_right.red": "<PERSON><PERSON><PERSON>ale Badjegeahči", "block.minecraft.banner.square_top_right.white": "Vilges Olgešbeale Badjegeahči", "block.minecraft.banner.square_top_right.yellow": "Fiskes Olgešbeale Badjegeahči", "block.minecraft.banner.straight_cross.black": "Čáhppes Ruossa", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.magenta": "Čuvgessáhppes Ruossa", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.pink": "Čuvges<PERSON><PERSON>", "block.minecraft.banner.straight_cross.purple": "<PERSON>á<PERSON><PERSON>", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.black": "Čáhppes Vuođđu", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "Turkii<PERSON>", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.lime": "<PERSON>uvges<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.magenta": "Čuvgessáhppes Vuođđu", "block.minecraft.banner.stripe_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.pink": "Guvgesruokses Vuođđu", "block.minecraft.banner.stripe_bottom.purple": "Sáhp<PERSON>đ<PERSON>", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.black": "Čáhppes Guo<PERSON>rg<PERSON>", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "Turk<PERSON><PERSON>", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_blue": "<PERSON>uv<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.magenta": "Čuvgessáhppes Guovdosárggis", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.pink": "Guvgesruokses <PERSON>", "block.minecraft.banner.stripe_center.purple": "Sáhppes <PERSON>", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.black": "Čáhppes Vinjusárg<PERSON>", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON>š<PERSON>", "block.minecraft.banner.stripe_downleft.cyan": "Turk<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_blue": "Čuvgesalit <PERSON>all<PERSON>", "block.minecraft.banner.stripe_downleft.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.lime": "Čuvges<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.magenta": "Čuvgessáhppes Vinjusárggis <PERSON>", "block.minecraft.banner.stripe_downleft.orange": "Runta Vinju<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.pink": "Čuvgesrukses <PERSON>all<PERSON>", "block.minecraft.banner.stripe_downleft.purple": "Sáhppes Vinjusá<PERSON>", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.black": "Čáhppes Vinjusárggis <PERSON>", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.brown": "Ruš<PERSON>", "block.minecraft.banner.stripe_downright.cyan": "Turkiisa <PERSON>", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.light_blue": "Čuvgesalit Vin<PERSON>", "block.minecraft.banner.stripe_downright.light_gray": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.lime": "Čuvgesruon<PERSON>", "block.minecraft.banner.stripe_downright.magenta": "Čuvgessáhppes Vinjusárggis <PERSON>", "block.minecraft.banner.stripe_downright.orange": "Runta Vinjusá<PERSON>", "block.minecraft.banner.stripe_downright.pink": "Čuvgesrukses <PERSON>rg<PERSON>", "block.minecraft.banner.stripe_downright.purple": "Sáhppes Vinjusá<PERSON>", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.yellow": "<PERSON><PERSON>s <PERSON><PERSON>", "block.minecraft.banner.stripe_left.black": "Čáhppes Gurutsárggis", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.brown": "R<PERSON>š<PERSON>rg<PERSON>", "block.minecraft.banner.stripe_left.cyan": "Turkii<PERSON>", "block.minecraft.banner.stripe_left.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.light_blue": "Čuvgesalit Gurutsárggis", "block.minecraft.banner.stripe_left.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.lime": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.magenta": "Čuvgessáhppes Gurutsárggis", "block.minecraft.banner.stripe_left.orange": "Run<PERSON> Gurutsárggis", "block.minecraft.banner.stripe_left.pink": "Čuvgesruk<PERSON>", "block.minecraft.banner.stripe_left.purple": "Sáhppes Gurutsárggis", "block.minecraft.banner.stripe_left.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON><PERSON>rg<PERSON>", "block.minecraft.banner.stripe_middle.black": "Čáhppes Veallut Guovdos<PERSON>rggis", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "Turkiisa <PERSON>lut <PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "Čuvgesalit V<PERSON>lut Guovdosárggis", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON>v<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.lime": "Čuvges<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.magenta": "Čuvgessáhppes Veallut Guovdosárggis", "block.minecraft.banner.stripe_middle.orange": "Runta Veallut <PERSON>", "block.minecraft.banner.stripe_middle.pink": "Čuvgesrukses <PERSON> Guovdosárggis", "block.minecraft.banner.stripe_middle.purple": "Sáhppes Veallut Guov<PERSON>gis", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.black": "Čáhppes Olgešsárggis", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.cyan": "Turkiisa <PERSON>", "block.minecraft.banner.stripe_right.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.light_blue": "Čuvges<PERSON><PERSON>", "block.minecraft.banner.stripe_right.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.lime": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.magenta": "Čuvgessáhppes Olgešsárggis", "block.minecraft.banner.stripe_right.orange": "Runta O<PERSON>š<PERSON>", "block.minecraft.banner.stripe_right.pink": "Čuvgesruskes Olgešsárggis", "block.minecraft.banner.stripe_right.purple": "Sáhppes Olgešsárggis", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON>s O<PERSON>š<PERSON>", "block.minecraft.banner.stripe_top.black": "Čáhppes Giera", "block.minecraft.banner.stripe_top.blue": "Alit G<PERSON>", "block.minecraft.banner.stripe_top.brown": "Ruškes Giera", "block.minecraft.banner.stripe_top.cyan": "Turkiisa <PERSON>iera", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.green": "Ruoná <PERSON>", "block.minecraft.banner.stripe_top.light_blue": "Čuvgesalit Giera", "block.minecraft.banner.stripe_top.light_gray": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.lime": "Čuvgesruoná Giera", "block.minecraft.banner.stripe_top.magenta": "Čuvgessáhppes Giera", "block.minecraft.banner.stripe_top.orange": "Runta Giera", "block.minecraft.banner.stripe_top.pink": "Čuvgesrukses Giera", "block.minecraft.banner.stripe_top.purple": "Sáhppes Giera", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.white": "Vilges Giera", "block.minecraft.banner.stripe_top.yellow": "Fiskes Giera", "block.minecraft.banner.triangle_bottom.black": "Čáhppes Vuollegolmmačiegat", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.brown": "Ruškes Vuollegolmmačiegat", "block.minecraft.banner.triangle_bottom.cyan": "Turkiisa Vuollegolmmačiegat", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON>mmač<PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "Čuvgesalit Vuollegolmmačiegat", "block.minecraft.banner.triangle_bottom.light_gray": "Čuv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.lime": "Čuvgesruoná Vuollegolmmačiegat", "block.minecraft.banner.triangle_bottom.magenta": "Čuvgessáhppes Vuollegolmmačiegat", "block.minecraft.banner.triangle_bottom.orange": "Runta Vuollegolmmačiegat", "block.minecraft.banner.triangle_bottom.pink": "Fiskes Vuollegolmmačiegat", "block.minecraft.banner.triangle_bottom.purple": "Sáhppes Vuollegolmmačiegat", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.white": "Vilges Vuollegolmmačiegat", "block.minecraft.banner.triangle_bottom.yellow": "Fiskes Vuollegolmmačiegat", "block.minecraft.banner.triangle_top.black": "Čáhppes Badjegolmmačiegat", "block.minecraft.banner.triangle_top.blue": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.brown": "Ruškes Badjegolmmačiegat", "block.minecraft.banner.triangle_top.cyan": "Turkiisa Badjegolmmačiegat", "block.minecraft.banner.triangle_top.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.green": "Ruo<PERSON>č<PERSON>", "block.minecraft.banner.triangle_top.light_blue": "Čuvgesalit Badjegolmmačiegat", "block.minecraft.banner.triangle_top.light_gray": "Čuvges<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.lime": "Čuvgesruoná Badjegolmmačiegat", "block.minecraft.banner.triangle_top.magenta": "Čuvgessáhppes Badjegolmmačiegat", "block.minecraft.banner.triangle_top.orange": "Runta Badjegolmmačiegat", "block.minecraft.banner.triangle_top.pink": "Čuvgesrukses Badjegolmmačiegat", "block.minecraft.banner.triangle_top.purple": "Sáhppes Badjegolmmačiegat", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.white": "Vilges Badjegolmmačiegat", "block.minecraft.banner.triangle_top.yellow": "Fiskes Badjegolmmačiegat", "block.minecraft.banner.triangles_bottom.black": "Čáhppes Vuollegolmmabánát", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.cyan": "Turk<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.light_blue": "Čuvges<PERSON><PERSON>án<PERSON>", "block.minecraft.banner.triangles_bottom.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.lime": "Čuvges<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.magenta": "Čuvgessáhppes Vuollegolmmabánát", "block.minecraft.banner.triangles_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.pink": "Čuvgesrukses <PERSON>", "block.minecraft.banner.triangles_bottom.purple": "Sáhppes V<PERSON>", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.black": "Čáhppes Badjegolmmabánát", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON>", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.cyan": "Turkii<PERSON>", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.light_blue": "Čuvges<PERSON><PERSON>", "block.minecraft.banner.triangles_top.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.lime": "Čuvgesruon<PERSON>", "block.minecraft.banner.triangles_top.magenta": "Čuvgessáhppes Badjegolmmabánát", "block.minecraft.banner.triangles_top.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangles_top.pink": "Čuvgesrukses <PERSON>", "block.minecraft.banner.triangles_top.purple": "Sáhppes Badjegol<PERSON>", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.barrel": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.barrier": "<PERSON><PERSON><PERSON>", "block.minecraft.basalt": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beacon.primary": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beacon.secondary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bed.no_sleep": "<PERSON><PERSON><PERSON><PERSON><PERSON> duš<PERSON>e oađđit ihkku dahje garra dálk<PERSON>", "block.minecraft.bed.not_safe": "Don it sahte nohkkat dál; leat návddit lahka", "block.minecraft.bed.obstructed": "<PERSON><PERSON> seaŋgii ii sáhte velledit", "block.minecraft.bed.occupied": "<PERSON><PERSON><PERSON>nga lea v<PERSON><PERSON>", "block.minecraft.bed.too_far_away": "Don it sáhte nohkkat dál; seanga lea menddo guhkkin eret", "block.minecraft.bedrock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bee_nest": "Uvlubeassi", "block.minecraft.beehive": "Uvluviessu", "block.minecraft.beetroots": "Ruksesruohttasat", "block.minecraft.bell": "Biellu", "block.minecraft.big_dripleaf": "<PERSON><PERSON><PERSON> go<PERSON>", "block.minecraft.big_dripleaf_stem": "<PERSON><PERSON><PERSON> go<PERSON> m<PERSON>tta", "block.minecraft.birch_button": "Soahkeboallu", "block.minecraft.birch_door": "Soahkeuksa", "block.minecraft.birch_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_leaves": "Soahkelasttat", "block.minecraft.birch_log": "Soahkebelko", "block.minecraft.birch_planks": "Soahkefiellut", "block.minecraft.birch_pressure_plate": "Soahkedeattopláht<PERSON>", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_sign": "Soahkegalba", "block.minecraft.birch_slab": "Soahkepláhtta", "block.minecraft.birch_stairs": "Soahketráhppá", "block.minecraft.birch_trapdoor": "Soahkeluŋká", "block.minecraft.birch_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_wood": "Soahkemuorra", "block.minecraft.black_banner": "Čáhppes Leavga", "block.minecraft.black_bed": "Čáhppes Seaŋga", "block.minecraft.black_candle": "Čáhppes ginttal", "block.minecraft.black_candle_cake": "Gáhkku čáhppes gintaliin", "block.minecraft.black_carpet": "Čáhppes Láhtterátnu", "block.minecraft.black_concrete": "Čáhppes betoŋŋa", "block.minecraft.black_concrete_powder": "Čáhppes betoŋŋabulvvar", "block.minecraft.black_glazed_terracotta": "Čáhppes šelges terrakotta", "block.minecraft.black_shulker_box": "Čáhppes Shulkerboksa", "block.minecraft.black_stained_glass": "Čáhppes Glássa", "block.minecraft.black_stained_glass_pane": "Čáhppes Glás<PERSON>u<PERSON>", "block.minecraft.black_terracotta": "Čáhppes terrakotta", "block.minecraft.black_wool": "Čáhppes Ullu", "block.minecraft.blackstone": "Čáhppesgeađgi", "block.minecraft.blackstone_slab": "Čáhppesgeađgepláhtta", "block.minecraft.blackstone_stairs": "Čáhppesgeađgetráhppá", "block.minecraft.blackstone_wall": "Čáhppesgeađge Seaidni", "block.minecraft.blast_furnace": "Su<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_banner": "<PERSON><PERSON>", "block.minecraft.blue_bed": "<PERSON><PERSON>", "block.minecraft.blue_candle": "<PERSON><PERSON> ginttal", "block.minecraft.blue_candle_cake": "Gáhkku alit gintaliin", "block.minecraft.blue_carpet": "<PERSON><PERSON>", "block.minecraft.blue_concrete": "<PERSON>t beto<PERSON>a", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON> bet<PERSON>", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON> terrakotta", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON>", "block.minecraft.blue_shulker_box": "<PERSON><PERSON>", "block.minecraft.blue_stained_glass": "<PERSON><PERSON>", "block.minecraft.blue_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.blue_terracotta": "<PERSON><PERSON>", "block.minecraft.blue_wool": "<PERSON><PERSON>", "block.minecraft.bone_block": "Dák<PERSON><PERSON><PERSON>h<PERSON>", "block.minecraft.bookshelf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_slab": "Muv<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_stairs": "Muvratráhppá", "block.minecraft.brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON><PERSON> ginttal", "block.minecraft.brown_candle_cake": "Gáhkku ruš<PERSON> gintaliin", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON><PERSON> betoŋŋa", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> beto<PERSON>", "block.minecraft.brown_glazed_terracotta": "Ruškes šelges terrakotta", "block.minecraft.brown_mushroom": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass": "Ruškes Glássa", "block.minecraft.brown_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_terracotta": "Ruškes terrakotta", "block.minecraft.brown_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_block": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_wall_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.budding_amethyst": "<PERSON><PERSON><PERSON><PERSON><PERSON> ametista", "block.minecraft.bush": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cake": "Gáhkku", "block.minecraft.calcite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "Kalibrerejuv<PERSON> skulk-dov<PERSON>an", "block.minecraft.campfire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle": "Ginttal", "block.minecraft.candle_cake": "Ginttalgáhkku", "block.minecraft.carrots": "Rušppit", "block.minecraft.cartography_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.carved_pumpkin": "Čuhpp<PERSON> Gurbbet", "block.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_air": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_vines": "Bákteráigegoargŋ<PERSON>", "block.minecraft.cave_vines_plant": "Bákteráigegoargŋ<PERSON><PERSON><PERSON>", "block.minecraft.chain": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chain_command_block": "Ráidokommándo<PERSON>kka", "block.minecraft.cherry_button": "<PERSON><PERSON>muorjemuorraboallu", "block.minecraft.cherry_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>jemuorragalba", "block.minecraft.cherry_leaves": "Kirsamuorjemuorralasttat", "block.minecraft.cherry_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_planks": "Kirsamuorjemuorrafiellut", "block.minecraft.cherry_pressure_plate": "Kirsamuorjemuorradeattopláhtta", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_sign": "Kirsamuorjemuorragalba", "block.minecraft.cherry_slab": "Ki<PERSON>muorjemuorrapl<PERSON><PERSON><PERSON>", "block.minecraft.cherry_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_trapdoor": "Kirsamuorjemuorraluŋká", "block.minecraft.cherry_wall_hanging_sign": "Kirsamuorjemuorraseaidnegalba", "block.minecraft.cherry_wall_sign": "Kirsamuorjemuorraseaidnegalba", "block.minecraft.cherry_wood": "Kirsamuorjemuorra", "block.minecraft.chest": "Giisá", "block.minecraft.chipped_anvil": "Reakčana<PERSON>", "block.minecraft.chiseled_bookshelf": "Luokčajuvvon girjehildu", "block.minecraft.chiseled_copper": "Luokč<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_nether_bricks": "Luokč<PERSON><PERSON><PERSON><PERSON>hergeađgemuvra", "block.minecraft.chiseled_polished_blackstone": "Luokčajuvvon Bussejuvvon Čáhppesgeađgi", "block.minecraft.chiseled_quartz_block": "Luokčajuvvon Kvárcablohkka", "block.minecraft.chiseled_red_sandstone": "Luokčajuvvon rukses s<PERSON>tto<PERSON>đgi", "block.minecraft.chiseled_resin_bricks": "Luokčajuvvon gáhččemuvra", "block.minecraft.chiseled_sandstone": "Luokčajuvvon sáttogeađgi", "block.minecraft.chiseled_stone_bricks": "Luokč<PERSON><PERSON><PERSON><PERSON>đgemuvra", "block.minecraft.chiseled_tuff": "Luok<PERSON><PERSON><PERSON><PERSON><PERSON> tuffa", "block.minecraft.chiseled_tuff_bricks": "Luok<PERSON><PERSON><PERSON><PERSON><PERSON> tuffamuvra", "block.minecraft.chorus_flower": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chorus_plant": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.clay": "<PERSON><PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coal_block": "Koallablohkka", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "Roattoseana", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobbled_deepslate_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>erák<PERSON>áht<PERSON>", "block.minecraft.cobbled_deepslate_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobbled_deepslate_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone": "Luoddageađ<PERSON>", "block.minecraft.cobblestone_slab": "Luoddageađgepláhtta", "block.minecraft.cobblestone_stairs": "Luoddageađgetráhppá", "block.minecraft.cobblestone_wall": "Luoddageađgg<PERSON>", "block.minecraft.cobweb": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cocoa": "Kakáo", "block.minecraft.command_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.comparator": "Redstone iskkadeaddji", "block.minecraft.composter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.conduit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_bulb": "Veaikelámpá", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "Veaikeráspa", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "Veaikeluŋká", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_tiles": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_polished_blackstone_bricks": "Luoddan<PERSON>sejuv<PERSON>áhppesgeađgemuvra", "block.minecraft.cracked_stone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crafter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crafting_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.creaking_heart": "Gihčama váibmu", "block.minecraft.creeper_head": "Creeper <PERSON>aivi", "block.minecraft.creeper_wall_head": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_button": "Skárllatboallu", "block.minecraft.crimson_door": "Skárllatuksa", "block.minecraft.crimson_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence_gate": "Skárllat<PERSON><PERSON><PERSON>", "block.minecraft.crimson_fungus": "Skárllatguoppar", "block.minecraft.crimson_hanging_sign": "Heŋgeju<PERSON><PERSON> sk<PERSON>", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_nylium": "Ská<PERSON>lat<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_planks": "Skárllatfiellut", "block.minecraft.crimson_pressure_plate": "Skárllatdeattopláhtta", "block.minecraft.crimson_roots": "Skárllatruohttasat", "block.minecraft.crimson_sign": "Skárllatgalba", "block.minecraft.crimson_slab": "Skárllatbealli", "block.minecraft.crimson_stairs": "Skárl<PERSON><PERSON>", "block.minecraft.crimson_stem": "Skárllatmátta", "block.minecraft.crimson_trapdoor": "Skárllatluŋká", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sk<PERSON>", "block.minecraft.crimson_wall_sign": "Heŋgojuvvon Skárllatgalba", "block.minecraft.crying_obsidian": "<PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.cut_copper": "Čuhppon veaiki", "block.minecraft.cut_copper_slab": "Čuhppon Veaikepláhtta", "block.minecraft.cut_copper_stairs": "Čuhppon veaiketráhppá", "block.minecraft.cut_red_sandstone": "Njuolgejuvvon rukses s<PERSON>tto<PERSON>đgi", "block.minecraft.cut_red_sandstone_slab": "Čuhppon Rukses Sáttogeađgepláhtta", "block.minecraft.cut_sandstone": "Njulgejuvvon sátto<PERSON>ađgi", "block.minecraft.cut_sandstone_slab": "Čuhppon Sáttogeađgepláhtta", "block.minecraft.cyan_banner": "Turkii<PERSON>", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_candle": "Turk<PERSON>sa ginttal", "block.minecraft.cyan_candle_cake": "Gáhkku čuvgesalit gintaliin", "block.minecraft.cyan_carpet": "Turkii<PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON><PERSON> betoŋ<PERSON>a", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> beto<PERSON>", "block.minecraft.cyan_glazed_terracotta": "Turkiisa š<PERSON>ges terrakotta", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "Turkiisa Glássa", "block.minecraft.cyan_stained_glass_pane": "Turkiisa <PERSON>", "block.minecraft.cyan_terracotta": "<PERSON><PERSON><PERSON><PERSON> terrakotta", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.damaged_anvil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sevdnjeseaikagalba", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sevdn<PERSON>seaika<PERSON>ba <PERSON>innis", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>ráhpp<PERSON>", "block.minecraft.daylight_detector": "Beaivečuovgga iski", "block.minecraft.dead_brain_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_bubble_coral_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_bush": "Goikemiestta", "block.minecraft.dead_fire_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_fire_coral_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_horn_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON><PERSON><PERSON>lo<PERSON>", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON>v<PERSON>koralla Seaidnelibardahtti", "block.minecraft.dead_tube_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_tube_coral_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON>all<PERSON>idnel<PERSON>", "block.minecraft.decorated_pot": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_brick_slab": "Bodneráktomuvrap<PERSON>á<PERSON>", "block.minecraft.deepslate_brick_stairs": "Bodneráktomuvratráhppá", "block.minecraft.deepslate_brick_wall": "Bodner<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_bricks": "Bo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_coal_ore": "Bodneráktok<PERSON><PERSON>", "block.minecraft.deepslate_copper_ore": "Bodner<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_diamond_ore": "Bodnerákto<PERSON>", "block.minecraft.deepslate_emerald_ore": "Bodneráktoemeraldamálbma", "block.minecraft.deepslate_gold_ore": "Bodneráktogol<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_iron_ore": "Bodneráktoruov<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_lapis_ore": "Bo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_redstone_ore": "Bodneráktoredstonemálbma", "block.minecraft.deepslate_tile_slab": "Bodneráktotiilapláht<PERSON>", "block.minecraft.deepslate_tile_stairs": "Bodneráktotiilatráhppá", "block.minecraft.deepslate_tile_wall": "Bodner<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_tiles": "Bodneráktotiila", "block.minecraft.detector_rail": "<PERSON><PERSON><PERSON>", "block.minecraft.diamond_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "Diorihttapláhtta", "block.minecraft.diorite_stairs": "Diorihttatráhppá", "block.minecraft.diorite_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dispenser": "Bážán", "block.minecraft.dragon_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "<PERSON><PERSON><PERSON>", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON>", "block.minecraft.dried_kelp_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dripstone_block": "Goaikogeađgeblohkka", "block.minecraft.dropper": "<PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "Emeraldablohkka", "block.minecraft.emerald_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.enchanting_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_gateway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_portal": "Endorportála", "block.minecraft.end_portal_frame": "Endorportálarámma", "block.minecraft.end_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_stone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_stone_brick_stairs": "<PERSON><PERSON>", "block.minecraft.end_stone_brick_wall": "<PERSON><PERSON>", "block.minecraft.end_stone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_bulb": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_grate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_cut_copper": "<PERSON><PERSON><PERSON><PERSON>ki", "block.minecraft.exposed_cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON>all<PERSON>", "block.minecraft.exposed_cut_copper_stairs": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.farmland": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire": "<PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON>", "block.minecraft.fire_coral_block": "<PERSON><PERSON>", "block.minecraft.fire_coral_fan": "<PERSON><PERSON>", "block.minecraft.fire_coral_wall_fan": "<PERSON><PERSON>", "block.minecraft.firefly_bush": "Dollačurotmiestta", "block.minecraft.fletching_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.flower_pot": "Lieđđekruhkku", "block.minecraft.flowering_azalea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ala<PERSON>tat", "block.minecraft.frogspawn": "Šlubboaivemonit", "block.minecraft.frosted_ice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.furnace": "Uvdna", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.glass": "Glássa", "block.minecraft.glass_pane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glow_lichen": "Áhcagas jeagil", "block.minecraft.glowstone": "Čuovgageađgi", "block.minecraft.gold_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gold_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.granite": "<PERSON><PERSON><PERSON>", "block.minecraft.granite_slab": "Granihttapláhtta", "block.minecraft.granite_stairs": "Granihttatráhppá", "block.minecraft.granite_wall": "Gran<PERSON><PERSON>", "block.minecraft.grass": "<PERSON><PERSON><PERSON>", "block.minecraft.grass_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gravel": "Čievra", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.gray_candle_cake": "Gáhk<PERSON> r<PERSON><PERSON> g<PERSON>", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON> bet<PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON><PERSON> terra<PERSON>", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.green_candle": "Ruo<PERSON> ginttal", "block.minecraft.green_candle_cake": "Gáhkku ruoná gintaliin", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON> betoŋŋa", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON> beto<PERSON>ab<PERSON>", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON><PERSON> šelges terrakotta", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "Ruoná Glás<PERSON>", "block.minecraft.green_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.green_terracotta": "Ruoná terrakotta", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "Sluŋkeruohttasat", "block.minecraft.hay_block": "Suoidnegubá", "block.minecraft.heavy_core": "<PERSON><PERSON>", "block.minecraft.heavy_weighted_pressure_plate": "Lossadeattupláhtta", "block.minecraft.honey_block": "Honnetblohkka", "block.minecraft.honeycomb_block": "Honnetbihttáblohkka", "block.minecraft.hopper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "Čoarve <PERSON>", "block.minecraft.horn_coral_block": "Čoarvvás <PERSON>lo<PERSON>", "block.minecraft.horn_coral_fan": "Čoarvvás Koralla Fan", "block.minecraft.horn_coral_wall_fan": "Čoarvvás <PERSON>", "block.minecraft.ice": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "<PERSON><PERSON><PERSON><PERSON>mu<PERSON>", "block.minecraft.infested_cobblestone": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.infested_cracked_stone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.infested_deepslate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.infested_mossy_stone_bricks": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON> gea<PERSON>", "block.minecraft.infested_stone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.infested_stone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_door": "Ruovdeuksa", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_trapdoor": "Ruovdeluŋká", "block.minecraft.jack_o_lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jigsaw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jukebox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_button": "Suhkkesvuovdeboallu", "block.minecraft.jungle_door": "Suhkkesvuvdemuorrauksa", "block.minecraft.jungle_fence": "Su<PERSON>k<PERSON><PERSON>ovde<PERSON>i", "block.minecraft.jungle_fence_gate": "Suhkkesvuovdeverrát", "block.minecraft.jungle_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ovdegalba", "block.minecraft.jungle_leaves": "Suhkkesvuovdelasttat", "block.minecraft.jungle_log": "Suhkkesvuovdebelko", "block.minecraft.jungle_planks": "Suhkkesvuovdefiellut", "block.minecraft.jungle_pressure_plate": "Suhkkesvuovdedeattopláhtta", "block.minecraft.jungle_sapling": "Suhkkesvuovdemuoraš", "block.minecraft.jungle_sign": "Suhkkesvuovdegalba", "block.minecraft.jungle_slab": "Suhkkesvuovdepláhtta", "block.minecraft.jungle_stairs": "Suhkkesvuovdetráhppá", "block.minecraft.jungle_trapdoor": "Suhkkesvuovdeluŋká", "block.minecraft.jungle_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>galba <PERSON>", "block.minecraft.jungle_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Suhkkesvuovdegalba", "block.minecraft.jungle_wood": "Suhkkesvuovdemuorra", "block.minecraft.kelp": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp_plant": "<PERSON><PERSON><PERSON>", "block.minecraft.ladder": "R<PERSON><PERSON><PERSON>", "block.minecraft.lantern": "Lámpá", "block.minecraft.lapis_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lapis_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.large_amethyst_bud": "Stuora ametistaurbi", "block.minecraft.large_fern": "<PERSON><PERSON><PERSON>", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lectern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lever": "Geavja", "block.minecraft.light": "Čuovga", "block.minecraft.light_blue_banner": "Čuvgesali<PERSON>", "block.minecraft.light_blue_bed": "<PERSON><PERSON>v<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_candle": "Čuvgesalit ginttal", "block.minecraft.light_blue_candle_cake": "Gáhkku čuvgesalit gintaliin", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>t betoŋŋa", "block.minecraft.light_blue_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>t betoŋŋabul<PERSON>var", "block.minecraft.light_blue_glazed_terracotta": "Čuvgesalit šelges terrakotta", "block.minecraft.light_blue_shulker_box": "Čuvges<PERSON><PERSON> Shulkerboksa", "block.minecraft.light_blue_stained_glass": "Čuvgesalit Glássa", "block.minecraft.light_blue_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_blue_terracotta": "Čuvgesalit terrakotta", "block.minecraft.light_blue_wool": "Čuvgesali<PERSON>u", "block.minecraft.light_gray_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_candle": "<PERSON>uovges<PERSON><PERSON><PERSON> ginttal", "block.minecraft.light_gray_candle_cake": "Gáhkku <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete": "<PERSON><PERSON>v<PERSON><PERSON><PERSON><PERSON> bet<PERSON>a", "block.minecraft.light_gray_concrete_powder": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON> bet<PERSON>", "block.minecraft.light_gray_glazed_terracotta": "Čuvgesr<PERSON><PERSON> terrakotta", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_stained_glass": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_terracotta": "<PERSON>uvges<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_weighted_pressure_plate": "Geahpadeattupláhtta", "block.minecraft.lightning_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lilac": "Sir<PERSON><PERSON><PERSON>", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lily_pad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_bed": "Čuvgesru<PERSON><PERSON>", "block.minecraft.lime_candle": "Čuvgesruoná ginttal", "block.minecraft.lime_candle_cake": "Gáhkku čuvgesru<PERSON><PERSON> gintaliin", "block.minecraft.lime_carpet": "<PERSON>e <PERSON>", "block.minecraft.lime_concrete": "<PERSON>uv<PERSON><PERSON><PERSON><PERSON> betoŋŋa", "block.minecraft.lime_concrete_powder": "Čuvges<PERSON><PERSON>á betoŋŋabulvvar", "block.minecraft.lime_glazed_terracotta": "Čuvgesruoná šelges terrakotta", "block.minecraft.lime_shulker_box": "Čuvges<PERSON><PERSON><PERSON>", "block.minecraft.lime_stained_glass": "Čuvgesruoná Glássa", "block.minecraft.lime_stained_glass_pane": "<PERSON>uvges<PERSON><PERSON><PERSON>", "block.minecraft.lime_terracotta": "Čuvgesruoná terrakotta", "block.minecraft.lime_wool": "<PERSON>uvges<PERSON><PERSON><PERSON>", "block.minecraft.lodestone": "Láidengeađgi", "block.minecraft.loom": "Gákkesmuorat", "block.minecraft.magenta_banner": "Čuvgessáhppes Leavga", "block.minecraft.magenta_bed": "Čuvgessáhppes Seaŋga", "block.minecraft.magenta_candle": "Čuvgessáhppes ginttal", "block.minecraft.magenta_candle_cake": "Gáhkku čuvgessáhppes gintaliin", "block.minecraft.magenta_carpet": "Čuvgessáhppes Láhtterátnu", "block.minecraft.magenta_concrete": "Čuvgessáhppes betoŋŋa", "block.minecraft.magenta_concrete_powder": "Čuvgessáhppes betoŋŋabulvvar", "block.minecraft.magenta_glazed_terracotta": "Čuvgessáhppes šelges terrakotta", "block.minecraft.magenta_shulker_box": "Čuvgessáhppes Shulkerboksa", "block.minecraft.magenta_stained_glass": "Magenta Glássa", "block.minecraft.magenta_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_terracotta": "Čuvgessáhppes terrakotta", "block.minecraft.magenta_wool": "Čuvgessáhppes Ullu", "block.minecraft.magma_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_button": "Mangroveboallu", "block.minecraft.mangrove_door": "Mangroveuksa", "block.minecraft.mangrove_fence": "Mangroveáidi", "block.minecraft.mangrove_fence_gate": "Mangroveverr<PERSON><PERSON>", "block.minecraft.mangrove_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mangrovegalba", "block.minecraft.mangrove_leaves": "Mangrovelasttat", "block.minecraft.mangrove_log": "Mangrovebelko", "block.minecraft.mangrove_planks": "Mangrovefiellut", "block.minecraft.mangrove_pressure_plate": "Mangrovedeattopláhtta", "block.minecraft.mangrove_propagule": "Mangrovegilvva", "block.minecraft.mangrove_roots": "Mangroveruohttasat", "block.minecraft.mangrove_sign": "Mangrovegalba", "block.minecraft.mangrove_slab": "Mangrovepláhtta", "block.minecraft.mangrove_stairs": "Mangrovetr<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_trapdoor": "Mangroveluŋká", "block.minecraft.mangrove_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mangrovegalba seainnis", "block.minecraft.mangrove_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Mangrovegalba", "block.minecraft.mangrove_wood": "Mangrovemuorra", "block.minecraft.medium_amethyst_bud": "Gaskasturrosaš ametistaurbi", "block.minecraft.melon": "Melovdna", "block.minecraft.melon_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.moss_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.moss_carpet": "Darfegovččas", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> luo<PERSON>", "block.minecraft.mossy_cobblestone_slab": "Jeagild<PERSON><PERSON><PERSON> luo<PERSON>đgebealli", "block.minecraft.mossy_cobblestone_stairs": "Jeagild<PERSON><PERSON><PERSON> luo<PERSON>đgetráhppá", "block.minecraft.mossy_cobblestone_wall": "Je<PERSON>ld<PERSON><PERSON><PERSON> luo<PERSON>", "block.minecraft.mossy_stone_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_stone_brick_stairs": "Jeagild<PERSON><PERSON><PERSON> geađgemuvratráhppá", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.moving_piston": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Garraláiramuvrap<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_brick_stairs": "Garraláiramuvratráhppá", "block.minecraft.mud_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_bricks": "G<PERSON><PERSON><PERSON><PERSON>mu<PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "Mohttás Mangroveruohttasat", "block.minecraft.mushroom_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_stairs": "Nethermuvratráhppá", "block.minecraft.nether_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_bricks": "Nether<PERSON>v<PERSON>", "block.minecraft.nether_gold_ore": "<PERSON><PERSON>", "block.minecraft.nether_portal": "<PERSON><PERSON>", "block.minecraft.nether_quartz_ore": "Netherkvárcam<PERSON><PERSON>bma", "block.minecraft.nether_sprouts": "Netherrahttá", "block.minecraft.nether_wart": "<PERSON><PERSON>", "block.minecraft.nether_wart_block": "<PERSON><PERSON>", "block.minecraft.netherite_block": "Netheriitablohkka", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "<PERSON><PERSON>ht<PERSON><PERSON><PERSON>hkka", "block.minecraft.oak_button": "Eaikaboallu", "block.minecraft.oak_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_leaves": "Eaikalasttat", "block.minecraft.oak_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_planks": "Eaikafiellut", "block.minecraft.oak_pressure_plate": "Eaikadeattopláhtta", "block.minecraft.oak_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_sign": "Eaikagalba", "block.minecraft.oak_slab": "Eaikapláhtta", "block.minecraft.oak_stairs": "Eaikatráhppá", "block.minecraft.oak_trapdoor": "Eaikaluŋká", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_wood": "Eaikamuorra", "block.minecraft.observer": "<PERSON><PERSON>", "block.minecraft.obsidian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON>š r<PERSON>ec<PERSON>pp<PERSON>č<PERSON>vga", "block.minecraft.ominous_banner": "<PERSON><PERSON>oiŋ<PERSON>a leavga", "block.minecraft.open_eyeblossom": "<PERSON><PERSON>", "block.minecraft.orange_banner": "<PERSON><PERSON>", "block.minecraft.orange_bed": "<PERSON><PERSON>ŋ<PERSON>", "block.minecraft.orange_candle": "Runta ginttal", "block.minecraft.orange_candle_cake": "G<PERSON><PERSON>k<PERSON> runta gintaliin", "block.minecraft.orange_carpet": "<PERSON><PERSON>", "block.minecraft.orange_concrete": "Runta betoŋŋa", "block.minecraft.orange_concrete_powder": "<PERSON>ta betoŋ<PERSON>ab<PERSON>var", "block.minecraft.orange_glazed_terracotta": "Runta šelges terrakotta", "block.minecraft.orange_shulker_box": "<PERSON><PERSON>", "block.minecraft.orange_stained_glass": "Runta Glássa", "block.minecraft.orange_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.orange_terracotta": "Runta terrakotta", "block.minecraft.orange_tulip": "<PERSON><PERSON>", "block.minecraft.orange_wool": "<PERSON><PERSON>", "block.minecraft.oxeye_daisy": "Báhpačehporas", "block.minecraft.oxidized_chiseled_copper": "Giimman luokčajuvvon <PERSON>aiki", "block.minecraft.oxidized_copper": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>ki", "block.minecraft.oxidized_copper_bulb": "Giimman <PERSON>aikelámp<PERSON>", "block.minecraft.oxidized_copper_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_grate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_trapdoor": "<PERSON><PERSON><PERSON>n ve<PERSON>", "block.minecraft.oxidized_cut_copper": "<PERSON><PERSON><PERSON><PERSON>ki", "block.minecraft.oxidized_cut_copper_slab": "G<PERSON>mman <PERSON>alli", "block.minecraft.oxidized_cut_copper_stairs": "<PERSON><PERSON>mman <PERSON> ve<PERSON>", "block.minecraft.packed_ice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.packed_mud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_hanging_moss": "Šovkes slu<PERSON>", "block.minecraft.pale_moss_block": "Šovkes darfeblohkka", "block.minecraft.pale_moss_carpet": "Šovkes darfego<PERSON>", "block.minecraft.pale_oak_button": "Šovkes eaikaboallu", "block.minecraft.pale_oak_door": "Šovkes eai<PERSON>uksa", "block.minecraft.pale_oak_fence": "Šovkes <PERSON>", "block.minecraft.pale_oak_fence_gate": "Šovkes eaikaverr<PERSON>", "block.minecraft.pale_oak_hanging_sign": "Šovkes eai<PERSON>gal<PERSON>is", "block.minecraft.pale_oak_leaves": "Šovkes eaikalasttat", "block.minecraft.pale_oak_log": "Šovkes eaikabelko", "block.minecraft.pale_oak_planks": "Šovkes eaikafiellut", "block.minecraft.pale_oak_pressure_plate": "Šovkes eaikadeattopláhtta", "block.minecraft.pale_oak_sapling": "Šovkes <PERSON>", "block.minecraft.pale_oak_sign": "Šovkes eaikagalba", "block.minecraft.pale_oak_slab": "Šovkes eaikapláhtta", "block.minecraft.pale_oak_stairs": "Šovkes eaikatráhppá", "block.minecraft.pale_oak_trapdoor": "Šovkes eaikaluŋká", "block.minecraft.pale_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>", "block.minecraft.pale_oak_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> šov<PERSON> eaikagalba", "block.minecraft.pale_oak_wood": "Šovkes eaika", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rih<PERSON>ec<PERSON>pp<PERSON>č<PERSON>", "block.minecraft.peony": "Piovdna", "block.minecraft.petrified_oak_slab": "G<PERSON>đgás <PERSON>", "block.minecraft.piglin_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.piglin_wall_head": "<PERSON><PERSON>", "block.minecraft.pink_banner": "Čuvgesrukses <PERSON>", "block.minecraft.pink_bed": "Čuvges<PERSON><PERSON>", "block.minecraft.pink_candle": "Guvgesruokses ginttal", "block.minecraft.pink_candle_cake": "Gáhkku čuvgesruk<PERSON> gintaliin", "block.minecraft.pink_carpet": "Vilgesruokses Láhtterátnu", "block.minecraft.pink_concrete": "<PERSON>uvges<PERSON><PERSON> betoŋ<PERSON>a", "block.minecraft.pink_concrete_powder": "Čuvges<PERSON><PERSON> beto<PERSON>", "block.minecraft.pink_glazed_terracotta": "Vilgesrukses šelges terrakotta", "block.minecraft.pink_petals": "<PERSON>", "block.minecraft.pink_shulker_box": "Čuvgesrukses <PERSON>lker<PERSON>a", "block.minecraft.pink_stained_glass": "Čuvgesruoksat Glássa", "block.minecraft.pink_stained_glass_pane": "Čuvgesruk<PERSON>", "block.minecraft.pink_terracotta": "<PERSON> terrakot<PERSON>", "block.minecraft.pink_tulip": "Čuvges<PERSON><PERSON>", "block.minecraft.pink_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ullu", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "Hoigadeadd<PERSON>", "block.minecraft.pitcher_crop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head": "S<PERSON><PERSON>oaiv<PERSON>", "block.minecraft.player_head.named": "%s <PERSON><PERSON><PERSON>", "block.minecraft.player_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.podzol": "Podsola", "block.minecraft.pointed_dripstone": "Goaikogeađgecuipi", "block.minecraft.polished_andesite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite_slab": "Busse<PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>hp<PERSON>", "block.minecraft.polished_basalt": "Bussejuv<PERSON>", "block.minecraft.polished_blackstone": "Bussejuvvon <PERSON>áhppesgeađgi", "block.minecraft.polished_blackstone_brick_slab": "Bussejuvvon <PERSON>hppesgeađgemuvrapláhtta", "block.minecraft.polished_blackstone_brick_stairs": "Bussejuvvon <PERSON>hppesgeađgemuvratráhppá", "block.minecraft.polished_blackstone_brick_wall": "Bussejuvvon <PERSON>áhppesgeađgemuvraseaidni", "block.minecraft.polished_blackstone_bricks": "Bussejuvvon <PERSON>hppesgeađgemuvra", "block.minecraft.polished_blackstone_button": "Bussejuvvon <PERSON>áhppesgeađgeboallu", "block.minecraft.polished_blackstone_pressure_plate": "Bussejuvvon <PERSON>hppesgeađgedeattopláhtta", "block.minecraft.polished_blackstone_slab": "Bussejuvvon <PERSON>hppesgeađgepláhtta", "block.minecraft.polished_blackstone_stairs": "Bussejuvvon <PERSON>hppesgeađgetráhppá", "block.minecraft.polished_blackstone_wall": "Bussejuvvon Čáhppesgeađge Seaidni", "block.minecraft.polished_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate_slab": "Bussejuv<PERSON>ht<PERSON>", "block.minecraft.polished_deepslate_stairs": "Busse<PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate_wall": "Busse<PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite": "Busseju<PERSON><PERSON>", "block.minecraft.polished_diorite_slab": "Bussejuv<PERSON>láhtta", "block.minecraft.polished_diorite_stairs": "Bussejuv<PERSON>tatráhppá", "block.minecraft.polished_granite": "Busse<PERSON><PERSON><PERSON>", "block.minecraft.polished_granite_slab": "Bussejuvvon <PERSON>pláhtta", "block.minecraft.polished_granite_stairs": "Busseju<PERSON><PERSON>hp<PERSON>", "block.minecraft.polished_tuff": "<PERSON><PERSON><PERSON><PERSON><PERSON> tuffa", "block.minecraft.polished_tuff_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff_stairs": "Bussejuvvon tuffatráhpp<PERSON>", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.poppy": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "Buđehat", "block.minecraft.potted_acacia_sapling": "Gilvojuvvon acaciamuoraš", "block.minecraft.potted_allium": "Gilvojuvvon ruksesluovvar", "block.minecraft.potted_azalea_bush": "Gilvojuvvon asalea", "block.minecraft.potted_azure_bluet": "Gilvojuvvon nuorsá", "block.minecraft.potted_bamboo": "Gilvojuvvon bamboa", "block.minecraft.potted_birch_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_blue_orchid": "Gilvojuvvon alit orkidéa", "block.minecraft.potted_brown_mushroom": "Gilvojuvvon ruškesguoppar", "block.minecraft.potted_cactus": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.potted_cherry_sapling": "Gilvoju<PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_closed_eyeblossom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_cornflower": "Gilvoju<PERSON><PERSON>", "block.minecraft.potted_crimson_fungus": "Gilvojuvvon skárllatguoppar", "block.minecraft.potted_crimson_roots": "Gilvojuvvon skárllatruohttasat", "block.minecraft.potted_dandelion": "Gilvojuvvon uvlorássi", "block.minecraft.potted_dark_oak_sapling": "Gilvoju<PERSON><PERSON>", "block.minecraft.potted_dead_bush": "Gilvoju<PERSON><PERSON> go<PERSON>", "block.minecraft.potted_fern": "Gilvojuvvon gáiska", "block.minecraft.potted_flowering_azalea_bush": "Gilvojuvvon lieđđudeaddji asalea", "block.minecraft.potted_jungle_sapling": "Gilvos Suhkesvuovdemuoraš", "block.minecraft.potted_lily_of_the_valley": "Gilvojuv<PERSON>", "block.minecraft.potted_mangrove_propagule": "Gilvojuvvon mangrovemuoraš", "block.minecraft.potted_oak_sapling": "Gil<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_open_eyeblossom": "Rahpasan <PERSON><PERSON>đ<PERSON>", "block.minecraft.potted_orange_tulip": "Gilvojuvvon runta tulipána", "block.minecraft.potted_oxeye_daisy": "Gilvojuvvon báhpačehporas", "block.minecraft.potted_pale_oak_sapling": "Gilvojuvvon šovkes eaikamuoraš", "block.minecraft.potted_pink_tulip": "Gilvojuvvon čuvgesrukses tulipána", "block.minecraft.potted_poppy": "Gil<PERSON>ju<PERSON><PERSON>", "block.minecraft.potted_red_mushroom": "Gilvojuv<PERSON> Ruksesguoppar", "block.minecraft.potted_red_tulip": "Gilvojuvvon rukses tulipána", "block.minecraft.potted_spruce_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.potted_torchflower": "Gilvojuvvon spáddárlieđđi", "block.minecraft.potted_warped_fungus": "Gil<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_warped_roots": "Gilvoju<PERSON><PERSON>", "block.minecraft.potted_white_tulip": "Gilvojuvvon vilges tulipána", "block.minecraft.potted_wither_rose": "<PERSON><PERSON><PERSON><PERSON><PERSON> with<PERSON><PERSON>", "block.minecraft.powder_snow": "Oppas", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.powered_rail": "Rávdn<PERSON><PERSON> ruov<PERSON>đi", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Prismariidnamuvraplá<PERSON><PERSON>", "block.minecraft.prismarine_brick_stairs": "Prismariidnamuvratráhppá", "block.minecraft.prismarine_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_slab": "Prismariidnapláhtta", "block.minecraft.prismarine_stairs": "Prismariidnatráhppá", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.purple_banner": "Sá<PERSON><PERSON>", "block.minecraft.purple_bed": "Sáhppes Seaŋga", "block.minecraft.purple_candle": "Sáhppes ginttal", "block.minecraft.purple_candle_cake": "Gáhkku sáhppes gintaliin", "block.minecraft.purple_carpet": "Sáhp<PERSON>", "block.minecraft.purple_concrete": "<PERSON><PERSON><PERSON><PERSON> betoŋŋa", "block.minecraft.purple_concrete_powder": "Sáhp<PERSON> betoŋŋabulvvar", "block.minecraft.purple_glazed_terracotta": "Sáhppes šelges terrakotta", "block.minecraft.purple_shulker_box": "Sáhppes Shulkerboksa", "block.minecraft.purple_stained_glass": "Sáhppes Glássa", "block.minecraft.purple_stained_glass_pane": "Sáhp<PERSON>", "block.minecraft.purple_terracotta": "Sáhppes terrakotta", "block.minecraft.purple_wool": "Sá<PERSON><PERSON>ll<PERSON>", "block.minecraft.purpur_block": "Pur<PERSON>blohkka", "block.minecraft.purpur_pillar": "Pur<PERSON>stoalpu", "block.minecraft.purpur_slab": "Purpurpláht<PERSON>", "block.minecraft.purpur_stairs": "Purpurtráhppá", "block.minecraft.quartz_block": "Kvárcablohkka", "block.minecraft.quartz_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_pillar": "Kvárcastoalpu", "block.minecraft.quartz_slab": "Kvárcapláhtta", "block.minecraft.quartz_stairs": "Kvárcatráhppá", "block.minecraft.rail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "Roavvaveaikeblohkka", "block.minecraft.raw_gold_block": "Roavvagol<PERSON><PERSON>kka", "block.minecraft.raw_iron_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON> ginttal", "block.minecraft.red_candle_cake": "Gáhkku rukses gintaliin", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON> beto<PERSON>a", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.red_glazed_terracotta": "<PERSON><PERSON><PERSON> terrakotta", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.red_mushroom_block": "<PERSON><PERSON><PERSON>", "block.minecraft.red_nether_brick_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.red_nether_brick_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sand": "Ruksessáttu", "block.minecraft.red_sandstone": "﻿<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_stairs": "<PERSON><PERSON><PERSON>đgetráhppá", "block.minecraft.red_sandstone_wall": "Ruksessáttogea<PERSON><PERSON>", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.red_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.red_terracotta": "<PERSON><PERSON><PERSON> terra<PERSON>", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.red_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.redstone_block": "Redstoneblohkka", "block.minecraft.redstone_lamp": "Redstonečuovga", "block.minecraft.redstone_ore": "Redstonemálbma", "block.minecraft.redstone_torch": "Redstonespáiddar", "block.minecraft.redstone_wall_torch": "Redstone seaidnespáiddar", "block.minecraft.redstone_wire": "Redstone<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.reinforced_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.repeater": "Redstone geardduheaddji", "block.minecraft.repeating_command_block": "Geardduheaddji kommándoblohkka", "block.minecraft.resin_block": "Gáhččeblohkka", "block.minecraft.resin_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_brick_stairs": "Gáhččemuvratráhppá", "block.minecraft.resin_brick_wall": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_bricks": "Gáhččemuvra", "block.minecraft.resin_clump": "Gáhččečoaltu", "block.minecraft.respawn_anchor": "Ealáskanáŋkor", "block.minecraft.rooted_dirt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.rose_bush": "Rosa<PERSON><PERSON><PERSON>", "block.minecraft.sand": "Sáttu", "block.minecraft.sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>đ<PERSON>", "block.minecraft.sandstone_slab": "Sáttogeađgepláhtta", "block.minecraft.sandstone_stairs": "Sáttogeađgetráhppá", "block.minecraft.sandstone_wall": "Sáttoge<PERSON><PERSON><PERSON>", "block.minecraft.scaffolding": "<PERSON><PERSON><PERSON>", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk-katalysá<PERSON>or", "block.minecraft.sculk_sensor": "Sculk-dov<PERSON><PERSON>", "block.minecraft.sculk_shrieker": "Sculk-riehči", "block.minecraft.sculk_vein": "Sculk-suotna", "block.minecraft.sea_lantern": "Mearračuovga", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Ealáskanbáiki bidjon", "block.minecraft.short_dry_grass": "<PERSON><PERSON><PERSON> go<PERSON>", "block.minecraft.short_grass": "Oanehis rásit", "block.minecraft.shroomlight": "Guopparčuovga", "block.minecraft.shulker_box": "Shulkerboksa", "block.minecraft.skeleton_skull": "Dákterik<PERSON>", "block.minecraft.skeleton_wall_skull": "Dákterikki <PERSON>indeoaiveskálžu", "block.minecraft.slime_block": "Njivleblohkka", "block.minecraft.small_amethyst_bud": "<PERSON>na ameti<PERSON>", "block.minecraft.small_dripleaf": "<PERSON><PERSON>", "block.minecraft.smithing_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smoker": "Suovastanuvdna", "block.minecraft.smooth_basalt": "<PERSON>inna basalta", "block.minecraft.smooth_quartz": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_quartz_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_quartz_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_red_sandstone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_red_sandstone_stairs": "<PERSON><PERSON><PERSON>ráhp<PERSON>", "block.minecraft.smooth_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_sandstone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_sandstone_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_stone": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_stone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.sniffer_egg": "<PERSON><PERSON><PERSON>", "block.minecraft.snow": "<PERSON><PERSON><PERSON>", "block.minecraft.snow_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_campfire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_lantern": "Siellolámpá", "block.minecraft.soul_sand": "<PERSON><PERSON>s<PERSON><PERSON><PERSON>", "block.minecraft.soul_soil": "<PERSON><PERSON>ean<PERSON>", "block.minecraft.soul_torch": "<PERSON>ellosp<PERSON>id<PERSON>", "block.minecraft.soul_wall_torch": "<PERSON><PERSON>", "block.minecraft.spawn.not_valid": "Dus ii leat ruovttuseaŋga dahje devdon e<PERSON>, dahje juobbá lei caggon", "block.minecraft.spawner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spawner.desc1": "Geavat ealáskanmoni:", "block.minecraft.spawner.desc2": "<PERSON><PERSON><PERSON><PERSON><PERSON> guđe ealli <PERSON>", "block.minecraft.sponge": "Guopparas", "block.minecraft.spore_blossom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_button": "Guossaboallu", "block.minecraft.spruce_door": "Guossauksa", "block.minecraft.spruce_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence_gate": "Guo<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> guo<PERSON>", "block.minecraft.spruce_leaves": "Guossalasttat", "block.minecraft.spruce_log": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_planks": "Guossafiellut", "block.minecraft.spruce_pressure_plate": "Guossadeattopláhtta", "block.minecraft.spruce_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_sign": "Guossagalba", "block.minecraft.spruce_slab": "Guossapláhtta", "block.minecraft.spruce_stairs": "Guossatráhppá", "block.minecraft.spruce_trapdoor": "Guossaluŋká", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> guo<PERSON>", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_wood": "Guossamuorra", "block.minecraft.sticky_piston": "Njiv<PERSON> hoigi", "block.minecraft.stone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Geađgemuvrap<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_brick_stairs": "Geađgemuvratráhppá", "block.minecraft.stone_brick_wall": "Geađgemuvra <PERSON>", "block.minecraft.stone_bricks": "Geađ<PERSON>mu<PERSON><PERSON>", "block.minecraft.stone_button": "Geađgeboallu", "block.minecraft.stone_pressure_plate": "Geađgedeat<PERSON><PERSON><PERSON>", "block.minecraft.stone_slab": "Geađgepláht<PERSON>", "block.minecraft.stone_stairs": "Geađgetráhppá", "block.minecraft.stonecutter": "Geađgečuo<PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_wood": "Vullojuvvon Acaciamuorra", "block.minecraft.stripped_bamboo_block": "Ferk<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_birch_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_birch_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_cherry_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_cherry_wood": "<PERSON>ull<PERSON><PERSON><PERSON><PERSON> k<PERSON>uorjemuorra", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_crimson_stem": "Vullojuvvon Skárllatađđa", "block.minecraft.stripped_dark_oak_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_dark_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_jungle_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>kkesvuovdebelko", "block.minecraft.stripped_jungle_wood": "Vull<PERSON>ju<PERSON><PERSON> Suhkkesvuovdemuorra", "block.minecraft.stripped_mangrove_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>grovemuorra", "block.minecraft.stripped_oak_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_pale_oak_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> šov<PERSON>ai<PERSON>belko", "block.minecraft.stripped_pale_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> šov<PERSON> eaika", "block.minecraft.stripped_spruce_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_warped_hyphae": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.structure_block": "Huskehusblohkka", "block.minecraft.structure_void": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sugar_cane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sunflower": "Beaivv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.suspicious_gravel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.suspicious_sand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sweet_berry_bush": "Váranmiestta", "block.minecraft.tall_dry_grass": "<PERSON>a <PERSON>", "block.minecraft.tall_grass": "<PERSON><PERSON>", "block.minecraft.tall_seagrass": "<PERSON><PERSON>", "block.minecraft.target": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.terracotta": "Terrakotta", "block.minecraft.test_block": "Iskkadanblohkka", "block.minecraft.test_instance_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT-bávkaleamit leat jáddaduvvon", "block.minecraft.torch": "Spáiddar", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.trapped_chest": "Darvvihahttingiisá", "block.minecraft.trial_spawner": "Geahččalusealáskahtti", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tube_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.tube_coral_block": "<PERSON><PERSON><PERSON>", "block.minecraft.tube_coral_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.tube_coral_wall_fan": "<PERSON><PERSON><PERSON>", "block.minecraft.tuff": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.turtle_egg": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.twisting_vines": "<PERSON><PERSON><PERSON><PERSON> go<PERSON>", "block.minecraft.twisting_vines_plant": "<PERSON><PERSON><PERSON><PERSON> go<PERSON> r<PERSON>", "block.minecraft.vault": "Lohkkadat", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON> rih<PERSON><PERSON><PERSON>", "block.minecraft.vine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.void_air": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wall_torch": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fungus": "Bonjoguoppar", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bon<PERSON><PERSON>", "block.minecraft.warped_hyphae": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_pressure_plate": "Bonjodeattopláhtta", "block.minecraft.warped_roots": "Bon<PERSON><PERSON><PERSON>tta<PERSON>", "block.minecraft.warped_sign": "Bonjogalba", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_trapdoor": "Bonjoluŋká", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bon<PERSON><PERSON><PERSON>", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wart_block": "Bonjospártoblohkka", "block.minecraft.water": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.water_cauldron": "Č<PERSON><PERSON>cer<PERSON><PERSON>", "block.minecraft.waxed_chiseled_copper": "Váksajuvvon luokčajuvvon veaiki", "block.minecraft.waxed_copper_block": "Váksaju<PERSON><PERSON>", "block.minecraft.waxed_copper_bulb": "Váksajuv<PERSON>aikelámp<PERSON>", "block.minecraft.waxed_copper_door": "Váksaju<PERSON><PERSON>", "block.minecraft.waxed_copper_grate": "Váksa<PERSON><PERSON><PERSON>", "block.minecraft.waxed_copper_trapdoor": "Váksajuvvon <PERSON>ŋ<PERSON>", "block.minecraft.waxed_cut_copper": "Váksajuv<PERSON> ve<PERSON>ki", "block.minecraft.waxed_cut_copper_slab": "Váksajuvvon <PERSON>aikebealli", "block.minecraft.waxed_cut_copper_stairs": "Váksajuv<PERSON> ve<PERSON>ket<PERSON>á<PERSON>", "block.minecraft.waxed_exposed_chiseled_copper": "Váksajuvvon suojehis luokčajuvvon veaiki", "block.minecraft.waxed_exposed_copper": "Váksajuvvon suoje<PERSON> ve<PERSON>ki", "block.minecraft.waxed_exposed_copper_bulb": "Váksajuvvon suojehis <PERSON>aikelámp<PERSON>", "block.minecraft.waxed_exposed_copper_door": "Váksajuvvon suo<PERSON><PERSON>", "block.minecraft.waxed_exposed_copper_grate": "Váksajuvvon suo<PERSON><PERSON>", "block.minecraft.waxed_exposed_copper_trapdoor": "Váksajuvvon suojehis ve<PERSON>", "block.minecraft.waxed_exposed_cut_copper": "Váksajuvvon suojehis <PERSON> veaiki", "block.minecraft.waxed_exposed_cut_copper_slab": "Váksajuvvon suo<PERSON> veaikebealli", "block.minecraft.waxed_exposed_cut_copper_stairs": "Váksajuvvon suoje<PERSON> veaiketráhppá<PERSON>", "block.minecraft.waxed_oxidized_chiseled_copper": "Váksajuvvon giimman luokčajuvvon veaiki", "block.minecraft.waxed_oxidized_copper": "Váksajuvvon giimman veaiki", "block.minecraft.waxed_oxidized_copper_bulb": "Váksajuvvon giimman veaikelámpá", "block.minecraft.waxed_oxidized_copper_door": "Váksajuvvon giimman ve<PERSON>", "block.minecraft.waxed_oxidized_copper_grate": "Váskajuvvon giimman ve<PERSON>", "block.minecraft.waxed_oxidized_copper_trapdoor": "Váskajuvvon giimman veai<PERSON>ŋ<PERSON>", "block.minecraft.waxed_oxidized_cut_copper": "Váksajuvvon giimman č<PERSON> veaiki", "block.minecraft.waxed_oxidized_cut_copper_slab": "Váksajuvvon giimman č<PERSON> veaikebealli", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Váksajuvvon giimman č<PERSON> veaiketráhppát", "block.minecraft.waxed_weathered_chiseled_copper": "Váksajuvvon giimmagoahtan luokčajuvvon veaiki", "block.minecraft.waxed_weathered_copper": "Váksajuvvon giimmagoahtan veaiki", "block.minecraft.waxed_weathered_copper_bulb": "Váksajuvvon giimmagoahtan veaikelámpá", "block.minecraft.waxed_weathered_copper_door": "Váksajuvvon giimmagoahtan veaikeuksa", "block.minecraft.waxed_weathered_copper_grate": "Váksajuvvon giimmagoahtan veai<PERSON>", "block.minecraft.waxed_weathered_copper_trapdoor": "Váksajuvvon giimmagoahtan veai<PERSON>uŋ<PERSON>á", "block.minecraft.waxed_weathered_cut_copper": "Váksajuvvon giimmagoahtan čuhppon veaiki", "block.minecraft.waxed_weathered_cut_copper_slab": "Váksajuvvon giimmagoahtan č<PERSON> veaikebealli", "block.minecraft.waxed_weathered_cut_copper_stairs": "Váksajuvvon giimmagoahtan č<PERSON>ppon veaiketráhppá<PERSON>", "block.minecraft.weathered_chiseled_copper": "Giimmagoahtan luokčajuvvon veaiki", "block.minecraft.weathered_copper": "Giimmagoah<PERSON> ve<PERSON>ki", "block.minecraft.weathered_copper_bulb": "Giimmagoahtan veaikelámpá", "block.minecraft.weathered_copper_door": "Giimmagoahtan ve<PERSON>uk<PERSON>", "block.minecraft.weathered_copper_grate": "Giimmagoahtan <PERSON>", "block.minecraft.weathered_copper_trapdoor": "Giimmagoahtan veai<PERSON>ŋ<PERSON>", "block.minecraft.weathered_cut_copper": "Giimmagoahtan č<PERSON> ve<PERSON>ki", "block.minecraft.weathered_cut_copper_slab": "Giimmagoahtan <PERSON>i", "block.minecraft.weathered_cut_copper_stairs": "Giimmagoahtan č<PERSON> ve<PERSON>ket<PERSON>", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON><PERSON><PERSON> goarg<PERSON>u", "block.minecraft.weeping_vines_plant": "<PERSON><PERSON><PERSON><PERSON><PERSON> goarg<PERSON>u r<PERSON>si", "block.minecraft.wet_sponge": "Njuoska <PERSON>", "block.minecraft.wheat": "Nisugor<PERSON><PERSON>", "block.minecraft.white_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.white_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.white_candle": "Vilges ginttal", "block.minecraft.white_candle_cake": "Gáhkku vilges gintaliin", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.white_concrete": "Vilges betoŋ<PERSON>a", "block.minecraft.white_concrete_powder": "Vilges beto<PERSON>", "block.minecraft.white_glazed_terracotta": "Vilges šelges terrakotta", "block.minecraft.white_shulker_box": "<PERSON><PERSON>ges Shulkerboksa", "block.minecraft.white_stained_glass": "Vilges Glássa", "block.minecraft.white_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.white_terracotta": "Vilges terrakotta", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.white_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.wildflowers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "<PERSON>er <PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_wall_skull": "<PERSON>er <PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.yellow_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_candle": "Fiskes ginttal", "block.minecraft.yellow_candle_cake": "Gáhkku fiskes gintaliin", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON> betoŋ<PERSON>a", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON><PERSON> beto<PERSON>", "block.minecraft.yellow_glazed_terracotta": "Fiskes šelges terrakotta", "block.minecraft.yellow_shulker_box": "Fiskes Shulkerboksa", "block.minecraft.yellow_stained_glass": "Fiskes Glássa", "block.minecraft.yellow_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_terracotta": "Fiskes terrakotta", "block.minecraft.yellow_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.zombie_head": "Jámeža oaivi", "block.minecraft.zombie_wall_head": "Jámež<PERSON>", "book.byAuthor": "%1$s čállán", "book.edit.title": "<PERSON><PERSON><PERSON><PERSON>", "book.editTitle": "<PERSON><PERSON><PERSON> gir<PERSON> na<PERSON>:", "book.finalizeButton": "<PERSON><PERSON><PERSON> ja gidde", "book.finalizeWarning": "Fuomáš! Go čálát vuoll<PERSON>i girji, de it sáhte šat lasihit maidege.", "book.generation.0": "<PERSON><PERSON><PERSON>", "book.generation.1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "book.generation.2": "<PERSON><PERSON><PERSON><PERSON><PERSON> máŋggus", "book.generation.3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "book.invalid.tag": "* Boastu gir<PERSON>ark<PERSON> *", "book.pageIndicator": "Siidu %1$s/%2$s", "book.page_button.next": "<PERSON><PERSON><PERSON>", "book.page_button.previous": "<PERSON><PERSON><PERSON><PERSON>", "book.sign.title": "<PERSON><PERSON><PERSON><PERSON>", "book.sign.titlebox": "<PERSON><PERSON>", "book.signButton": "<PERSON><PERSON><PERSON>", "book.view.title": "<PERSON><PERSON><PERSON><PERSON>", "build.tooHigh": "Huksenmeari allodat lea %s", "chat.cannotSend": "Ii s<PERSON>te s<PERSON>", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "<PERSON><PERSON><PERSON>", "chat.copy": "<PERSON><PERSON><PERSON><PERSON>", "chat.copy.click": "<PERSON><PERSON><PERSON><PERSON>", "chat.deleted_marker": "Server <PERSON><PERSON><PERSON><PERSON> d<PERSON>.", "chat.disabled.chain_broken": "Č<PERSON>tta lea jádd<PERSON>u<PERSON><PERSON> danne go ráidu lea boatkanan. Geahččal ođđasit searvat serverii.", "chat.disabled.expiredProfileKey": "Čátta lea jáddaduv<PERSON> danne go dán geava<PERSON><PERSON><PERSON> al<PERSON><PERSON> čoavdda lea nohkan. Geahččal ođđasit searvat serverii.", "chat.disabled.invalid_command_signature": "<PERSON><PERSON><PERSON><PERSON> ledje vuordemeahttun dahje váilevaš kommánduargumeantta vuolláičállosat.", "chat.disabled.invalid_signature": "Čáttas lei dohkketmeahttun vuolláičála. Geahččal ođđasit searvat.", "chat.disabled.launcher": "Čálašeapmi lea j<PERSON>dd<PERSON><PERSON><PERSON><PERSON> (launcheris). Ii sáhte sáddet <PERSON>.", "chat.disabled.missingProfileKey": "Čátta lea jádd<PERSON>u<PERSON><PERSON> danne go almm<PERSON>š čoavdda lea nohkan. Geahččal ođđasit searvat serverii.", "chat.disabled.options": "Čállosat leat jáddaduvvon k<PERSON>anta he<PERSON>.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON><PERSON><PERSON><PERSON> boastu vuoru mielde. Lea go du vuogádaga á<PERSON> r<PERSON>?", "chat.disabled.profile": "Čátta ii dohkkehuvvo kontoheivehusaiguin. Deaddil fas \"%s\" oa<PERSON><PERSON><PERSON><PERSON> eambbo dieđ<PERSON>.", "chat.disabled.profile.moreInfo": "Čálašeapmi ii leat dohkkhuvvon geavaheaddji heivehusain. Ii s<PERSON>hte <PERSON>, iige o<PERSON>t <PERSON>.", "chat.editBox": "<PERSON><PERSON><PERSON>", "chat.filtered": "Server sille.", "chat.filtered_full": "Server lea <PERSON> du čállosa soames spelliide.", "chat.link.confirm": "Leat go sihkar ahte áiggut rahpat čuovvovas neahtasiiddu?", "chat.link.confirmTrusted": "Áiggut go rahpat liŋkka dahje máŋget čuohp<PERSON>beavdái?", "chat.link.open": "<PERSON><PERSON>", "chat.link.warning": "Ale goassege raba linkkaid olb<PERSON>in geaid it luohte!", "chat.queue": "[+%s linn<PERSON><PERSON><PERSON> vurdet]", "chat.square_brackets": "[%s]", "chat.tag.error": "Server s<PERSON><PERSON><PERSON> boast<PERSON><PERSON><PERSON><PERSON>.", "chat.tag.modified": "Server rievdadii čállosa. Ná lei álgg<PERSON>:", "chat.tag.not_secure": "Duođaš<PERSON>ahttun čálus. Dan ii sáhte váidit.", "chat.tag.system": "Serverčálus. Dan ii sáhte váidit.", "chat.tag.system_single_player": "Server diehtu.", "chat.type.admin": "[%s %s]", "chat.type.advancement.challenge": "%s lea gárven %s hástalusa", "chat.type.advancement.goal": "%s lea olahan %s olahusa", "chat.type.advancement.task": "%s lea o<PERSON>an ovdáneami %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "<PERSON><PERSON><PERSON>", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s cealká %s", "chat.validation_error": "Čátta duođašteapmi ii lihkostuvvan", "chat_screen.message": "<PERSON><PERSON><PERSON><PERSON>: %s", "chat_screen.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chat_screen.usage": "<PERSON><PERSON><PERSON>, ja<PERSON><PERSON><PERSON><PERSON> enter-b<PERSON><PERSON><PERSON>", "chunk.toast.checkLog": "Geahča loggas dárkileappot dieđuid", "chunk.toast.loadFailure": "Laigosiid viežžan ii lihkostuvvan dás: %s", "chunk.toast.lowDiskSpace": "Unnán sadji vuo<PERSON>!", "chunk.toast.lowDiskSpace.description": "Ii soaitte s<PERSON>ht<PERSON>t vurket máilmmi.", "chunk.toast.saveFailure": "Laigosiid vurken ii lihkostuvvan dás: %s", "clear.failed.multiple": "Dávvirat eai gávdnon %s olbmuin", "clear.failed.single": "Dávvirat eai gávdnon %s spealláris", "color.minecraft.black": "Čáhppat", "color.minecraft.blue": "<PERSON><PERSON>", "color.minecraft.brown": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.cyan": "Turkiisa", "color.minecraft.gray": "<PERSON><PERSON><PERSON>", "color.minecraft.green": "Ruoná", "color.minecraft.light_blue": "Čuvgesalit", "color.minecraft.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.lime": "Čuvgesruoná", "color.minecraft.magenta": "<PERSON><PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.orange": "<PERSON><PERSON>", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "Sáhppat", "color.minecraft.red": "Ruoksat", "color.minecraft.white": "Vielgat", "color.minecraft.yellow": "Fiskat", "command.context.here": "<--[D<PERSON><PERSON><PERSON>]", "command.context.parse_error": "%s %s sajádagas: %s", "command.exception": "Kommándo %s ii sáhttán lohkot", "command.expected.separator": "<PERSON><PERSON><PERSON><PERSON>i guoros saji go loahpaha argume<PERSON>, muhto gáv<PERSON>i e<PERSON>t <PERSON>", "command.failed": "Meroškeahtta boasttuvuohta iđii go geahččalii jođihit kommándo", "command.forkLimit": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON> (%s)", "command.unknown.argument": "Ii lean rievttes argumeanta kommándos", "command.unknown.command": "Amas dahje be<PERSON> kommando, g<PERSON><PERSON><PERSON><PERSON> vuolil boasttuvuođa", "commands.advancement.criterionNotFound": "%1$s ovdáneamis ii leat '%2$s' kriteria", "commands.advancement.grant.criterion.to.many.failure": "Ii sáhttán skeŋket '%s' kriteria %s ovdáneamis %s olbmuide go sis juo ledje dat", "commands.advancement.grant.criterion.to.many.success": "Skeŋken '%s' kriteria %s ovdáneamis %s olbmuide", "commands.advancement.grant.criterion.to.one.failure": "Ii sáhttán skeŋket '%s' kriteria %s ovdáneamis %s olbmuide go sis juo ledje dat", "commands.advancement.grant.criterion.to.one.success": "Skeŋken '%s' kriteria %s ovdáneamis %s olbmui", "commands.advancement.grant.many.to.many.failure": "Ii sáhttán skeŋket %s ovdánemiid %s olbmuide go sis juo ledje dat", "commands.advancement.grant.many.to.many.success": "Skeŋken %s ovdánemiid %s olbmuide", "commands.advancement.grant.many.to.one.failure": "Ii sáhttán skeŋket %s ovdánemiid %s olbmui go sus juo ledje dat", "commands.advancement.grant.many.to.one.success": "Skeŋken %s ovdánemiid %s olbmui", "commands.advancement.grant.one.to.many.failure": "Ii sáhttán skeŋket %s ovdáneami %s olbmuide go sis juo ledje dat", "commands.advancement.grant.one.to.many.success": "Skeŋken %s ovdáneami %s olbmuide", "commands.advancement.grant.one.to.one.failure": "Ii sáhttán skeŋket %s ovdáneami %s olbmui go sus juo lei dat", "commands.advancement.grant.one.to.one.success": "Skeŋken %s ovdáneami %s olbmui", "commands.advancement.revoke.criterion.to.many.failure": "Ii sáhttán geassit '%s' kriteria %s ovdáneamis %s olb<PERSON>in go sis ii leat dat", "commands.advancement.revoke.criterion.to.many.success": "Geassán '%s' kriteria %s ovdáneamis %s olbmuin", "commands.advancement.revoke.criterion.to.one.failure": "Ii sáhttán geassit '%s' kriteria %s ovdáneamis %s olbmuin go sis eai leat dat", "commands.advancement.revoke.criterion.to.one.success": "Geassán '%s' kriteria %s ovdáneamis %s speallis", "commands.advancement.revoke.many.to.many.failure": "Ii sáhttán geassit %s ovdánemiid %s olb<PERSON>in go sis eai leat dat", "commands.advancement.revoke.many.to.many.success": "Geassán %s ovdánemiid %s olbmuin", "commands.advancement.revoke.many.to.one.failure": "Ii sáhttán geassit %s ovdánemiid %s olb<PERSON>in go sis eai leat dat", "commands.advancement.revoke.many.to.one.success": "Geassán %s ovdánemiid %s speallis", "commands.advancement.revoke.one.to.many.failure": "Ii sáhttán geassit %s ovdáneami %s olb<PERSON>in go sis ii leat dat", "commands.advancement.revoke.one.to.many.success": "Geassán %s ovdáneami %s olbmuin", "commands.advancement.revoke.one.to.one.failure": "Ii sáhttán geassit %s ovdáneami %s olb<PERSON>in go sis ii leat dat", "commands.advancement.revoke.one.to.one.success": "Geassán %s ovdáneami %s speallis", "commands.attribute.base_value.get.success": "Vuođđoárvu %s attribuhtas %s entitas lea %s", "commands.attribute.base_value.reset.success": "Vuođđoárvu %s attribuhtas %s entitas lea máhcahuvvon dása: %s", "commands.attribute.base_value.set.success": "Vuođđoárvu %s attribuhtas %s entitas lea bidjon %s", "commands.attribute.failed.entity": "%s ii leat dohkkehuvvon entitia dán kommándos", "commands.attribute.failed.modifier_already_present": "%s he<PERSON>hus gávdno juo %s attribuhtas %s entitias", "commands.attribute.failed.no_attribute": "%s entitias ii leat %s attribuhtta", "commands.attribute.failed.no_modifier": "%s attribuhtas %s entitias ii lea heivehus %s", "commands.attribute.modifier.add.success": "Lasihan %s heivehusa %s attribuhttii %s entitias", "commands.attribute.modifier.remove.success": "Sihkkon %s heivehusa %s attribuhttii %s entitias", "commands.attribute.modifier.value.get.success": "Árvu %s heivehusas %s attribuhttas %s entitias lea %s", "commands.attribute.value.get.success": "%s entitia %s attribuhta árvu lea %s", "commands.ban.failed": "Ii mihkkege rievdan. Diet spealli lea juo gildojuvvon", "commands.ban.success": "Gilddii %s: %s", "commands.banip.failed": "Ii mihkkege rievdan. Diet IP lea juo gildojuvvon", "commands.banip.info": "<PERSON><PERSON><PERSON> gieldin váikkuha %s olbmo/olbmuid: %s", "commands.banip.invalid": "Boastu IP adreassa dahje amas spealli", "commands.banip.success": "Gildojuvvon IP %s: %s", "commands.banlist.entry": "%s gildojuvvoi %s spealli mearrádusain. Sivva lei: %s", "commands.banlist.entry.unknown": "(Amas)", "commands.banlist.list": "Leat %s g<PERSON><PERSON><PERSON><PERSON><PERSON>:", "commands.banlist.none": "<PERSON><PERSON> leat g<PERSON>", "commands.bossbar.create.failed": "Gávdno juo stálloheaggasáhcu mas lea '%s' ID:a", "commands.bossbar.create.success": "R<PERSON><PERSON><PERSON><PERSON> he<PERSON>huvvon stálloheaggasázu %s", "commands.bossbar.get.max": "<PERSON><PERSON><PERSON><PERSON><PERSON> st<PERSON><PERSON><PERSON>gg<PERSON><PERSON>, %s, lea %s maks<PERSON><PERSON><PERSON>", "commands.bossbar.get.players.none": "Heivehu<PERSON><PERSON> st<PERSON><PERSON>gg<PERSON>, %s, eai leat speallit online", "commands.bossbar.get.players.some": "Heivehu<PERSON><PERSON> st<PERSON><PERSON>gg<PERSON>, %s, leat dál %s spealli online: %s", "commands.bossbar.get.value": "<PERSON><PERSON><PERSON><PERSON><PERSON> st<PERSON><PERSON>gg<PERSON>, %s, lea %s mearr<PERSON><PERSON>", "commands.bossbar.get.visible.hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON>, %s, lea <PERSON>", "commands.bossbar.get.visible.visible": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON>, %s, lea oidnosis", "commands.bossbar.list.bars.none": "<PERSON>ai leat he<PERSON>hu<PERSON><PERSON> st<PERSON>lloheaggasázut do<PERSON>s", "commands.bossbar.list.bars.some": "Leat %s he<PERSON><PERSON><PERSON><PERSON> st<PERSON><PERSON>heaggasá<PERSON><PERSON> do<PERSON>mmas: %s", "commands.bossbar.remove.success": "Sihkkon heivehuvvon stálloheaggasázu %s", "commands.bossbar.set.color.success": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON>, %s, lea ivdni rievdaduvvon", "commands.bossbar.set.color.unchanged": "Ii mihkkege rievdan. Diet ivdni lea juo dán stálloheaggasázus", "commands.bossbar.set.max.success": "<PERSON><PERSON><PERSON><PERSON><PERSON> st<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, %s, lea maks<PERSON><PERSON><PERSON> rievdaduvvon %s maks<PERSON><PERSON><PERSON>", "commands.bossbar.set.max.unchanged": "Ii mihkkege rievdan. Diet lea juo maksim<PERSON>la dán st<PERSON>lloheaggasázus", "commands.bossbar.set.name.success": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, %s, lea namma rievdaduvvon", "commands.bossbar.set.name.unchanged": "Ii mihkkege rievdan. Diet namma lea juo dán st<PERSON>lloheaggasá<PERSON>s", "commands.bossbar.set.players.success.none": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON>gg<PERSON>, %s, eai leat šat speallit", "commands.bossbar.set.players.success.some": "<PERSON><PERSON><PERSON><PERSON><PERSON> st<PERSON><PERSON>gg<PERSON>, %s, leat dál %s spealli: %s", "commands.bossbar.set.players.unchanged": "Ii mihkkege rievdan. Diet speallit leat juo dán stálloheaggasázus. Ii sáhte lasihit dahje geassit ovttage", "commands.bossbar.set.style.success": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON>, %s, lea h<PERSON><PERSON><PERSON> riev<PERSON>uv<PERSON>", "commands.bossbar.set.style.unchanged": "Ii mihkkege rievdan. Diet hábmi lea juo dán stálloheaggasázus", "commands.bossbar.set.value.success": "Heivehu<PERSON><PERSON> st<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, %s, lea mearr<PERSON>dus rievdaduvvon %s mearr<PERSON><PERSON><PERSON>i", "commands.bossbar.set.value.unchanged": "Ii mihkkege rievdan. Diet lea juo mearrádus dán st<PERSON>lloheaggasázus", "commands.bossbar.set.visibility.unchanged.hidden": "Ii mihkkege rievdan. Stálloheaggasáhcu lea č<PERSON>kkon", "commands.bossbar.set.visibility.unchanged.visible": "Ii mihkkege rievdan. Stálloheaggasáhcu lea juo oidnosis", "commands.bossbar.set.visible.success.hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON>, %s, lea d<PERSON><PERSON>", "commands.bossbar.set.visible.success.visible": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON>, %s, lea dál oidnosis", "commands.bossbar.unknown": "Ii gávdno stálloheaggasáhcu mas lea '%s' ID:a", "commands.clear.success.multiple": "Gurren %s dávvira %s olbmuin", "commands.clear.success.single": "Gurren %s dávvira %s speallis", "commands.clear.test.multiple": "Gávdnoje %s seammalágan dávvira %s spelliin", "commands.clear.test.single": "Gávdnoje %s seammalágan dávviriid %s speallis", "commands.clone.failed": "Blohkat eai máŋ<PERSON>", "commands.clone.overlap": "Vuolggabáikkit ja loahppabáikkit eai sáhte bidjot bad<PERSON>a", "commands.clone.success": "Máŋgii %s blohkaid", "commands.clone.toobig": "Menddo olu blohkat vuoruhuvvon guovllus (eanemus %s, vuoruhuvvon %s)", "commands.damage.invulnerable": "Miht<PERSON><PERSON><PERSON><PERSON><PERSON> ii sáhte bávččagahttet diekkár sorttat vahágiin", "commands.damage.success": "Bidjan %s bákčasiid spellii %s", "commands.data.block.get": "%s diehtu %s, %s, %s blohkas %s sturrodatfaktora geažil lea %s", "commands.data.block.invalid": "Válljejuvvon blohkka ii leat blohkka entitia", "commands.data.block.modified": "<PERSON><PERSON><PERSON><PERSON> bloh<PERSON><PERSON>đu buot dáin %s, %s, %s", "commands.data.block.query": "%s, %s, ja %s sajádasas leat čuovvovaš blohkkadieđut: %s", "commands.data.entity.get": "%s vurkenbáikis %s maŋŋá sturrodatfaktora %s %s sturrodatfaktorii", "commands.data.entity.invalid": "<PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON> he<PERSON> s<PERSON>", "commands.data.entity.modified": "Ođasmahtten %s entitia dieđu", "commands.data.entity.query": "%s lea č<PERSON><PERSON>vas entitia dieđu: %s", "commands.data.get.invalid": "Ii sáhte oažžut %s; dušše numeriskas mearkkat leat dohkkehuvvon", "commands.data.get.multiple": "<PERSON><PERSON><PERSON> paramehtar doh<PERSON> du<PERSON> ovtta NBT dieđu", "commands.data.get.unknown": "Ii sáhte oažžut %s; mearka ii gávdno", "commands.data.merge.failed": "<PERSON><PERSON> mi<PERSON><PERSON><PERSON><PERSON> r<PERSON>, vuo<PERSON><PERSON><PERSON><PERSON> heivehusain leat juo dát dieđut", "commands.data.modify.expected_list": "Meroštii listu, oažžui: %s", "commands.data.modify.expected_object": "Meroštii o<PERSON>avtta, oažžui: %s", "commands.data.modify.expected_value": "Meroštii <PERSON>vu, oaččui: %s", "commands.data.modify.invalid_index": "Boastu indeksalistu: %s", "commands.data.modify.invalid_substring": "Ogiltiga delsträngsindex: %s till %s", "commands.data.storage.get": "%s vurkenbáikis %s maŋŋá sturrodatfaktora %s %s: as", "commands.data.storage.modified": "Heivehu<PERSON><PERSON> vurken %s", "commands.data.storage.query": "Vurkenbáiki %s sisttisdoallá: %s", "commands.datapack.create.already_exists": "Pack with name '%s' already exists", "commands.datapack.create.invalid_full_name": "Invalid new pack name '%s'", "commands.datapack.create.invalid_name": "Invalid characters in new pack name '%s'", "commands.datapack.create.io_failure": "Can't create pack with name '%s', check logs", "commands.datapack.create.metadata_encode_failure": "Failed to encode metadata for pack with name '%s': %s", "commands.datapack.create.success": "Created new empty pack with name '%s'", "commands.datapack.disable.failed": "'%s' p<PERSON>h<PERSON> ii leat bidjon ala!", "commands.datapack.disable.failed.feature": "<PERSON>i s<PERSON>hte gea<PERSON> \"%s\"-<PERSON><PERSON><PERSON><PERSON>, danne go dat lea oassin aktiverejuvvon le<PERSON>ggas!", "commands.datapack.enable.failed": "'%s' páhkka lea juo bidjon ala!", "commands.datapack.enable.failed.no_flags": "\"%s\" namma<PERSON><PERSON> p<PERSON> ii sáhte váldit atnui, danne go gáibiduvvon sisdoalut eai leat bidjon ala dán máilmmis %s!", "commands.datapack.list.available.none": "<PERSON>ai gávdno šat eanet diehtopá<PERSON>kat olámuttos", "commands.datapack.list.available.success": "Leat %s diehtopáhka olámuttos: %s", "commands.datapack.list.enabled.none": "<PERSON><PERSON> makk<PERSON>rge die<PERSON>kat leat bidjon ala", "commands.datapack.list.enabled.success": "Leat %s diehtopáhka mat leat biddjojuvvon ala: %s", "commands.datapack.modify.disable": "Geassádeamin diehtopáhka %s", "commands.datapack.modify.enable": "Juolludeamin diehtopáhka %s", "commands.datapack.unknown": "Amas die<PERSON>kka %s", "commands.debug.alreadyRunning": "Coahkkinguorahallan lea juo <PERSON>", "commands.debug.function.noRecursion": "Ii sáht<PERSON>án guorahallat doaimma siste", "commands.debug.function.noReturnRun": "<PERSON>i s<PERSON>hte guo<PERSON> \"return run\"", "commands.debug.function.success.multiple": "Guorran %s kommándu %s doaimma gitta bohtosfiilii %s", "commands.debug.function.success.single": "Guorran %s kommándu \"%s\" doaimmas gitta boh<PERSON>fiilii %s", "commands.debug.function.traceFailed": "<PERSON><PERSON><PERSON> guo<PERSON> ii lih<PERSON>vvan", "commands.debug.notRunning": "Coahkkinguorahallan ii leat <PERSON>ggahuvvan", "commands.debug.started": "<PERSON><PERSON>gg<PERSON><PERSON> co<PERSON>kinguorahall<PERSON>", "commands.debug.stopped": "Heitihii coahkkin-guorahallama go lei vássán %s sekundda ja %s coahkkima (%s coahkkima juohke sekundas)", "commands.defaultgamemode.success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> speallanhábmi lea dál %s hábmi", "commands.deop.failed": "Ii mihkkege rievdan. Diet spealli ii leat operatevra", "commands.deop.success": "%s ii leat šat operator", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "Váttisvuohta ii rievdaduvvon; %s lei juo bidjon", "commands.difficulty.query": "Váttisvuođa dássi lea dál %s", "commands.difficulty.success": "Váttisvuohta lea bidjon %s dássái", "commands.drop.no_held_items": "Entitiai ii sáhte deavdit dávviriid", "commands.drop.no_loot_table": "%s entitias ii leat dávvirtabealla", "commands.drop.no_loot_table.block": "%s blohkas ii leat mearriduvvon dávvirluoitin", "commands.drop.success.multiple": "Luoitán %s dávviriid", "commands.drop.success.multiple_with_table": "Luoitán %s dávviriid %s dávvirtabeallas", "commands.drop.success.single": "Luoitán %s %s", "commands.drop.success.single_with_table": "Luoitán %s %s dávviriid %s dávvirtabeallas", "commands.effect.clear.everything.failed": "<PERSON><PERSON><PERSON> ii leat váikkuhus máid sáhttá geassit", "commands.effect.clear.everything.success.multiple": "Geassá buot váikkuhusaid %s <PERSON>đain", "commands.effect.clear.everything.success.single": "Geassán buot váikkuhusaid %s <PERSON>đas", "commands.effect.clear.specific.failed": "Áđ<PERSON> ii leat válljejuvvon váikkuhus", "commands.effect.clear.specific.success.multiple": "Geassán %s váikkuhusa %s áđain", "commands.effect.clear.specific.success.single": "Geassán %s váikkuhusa %s áđas", "commands.effect.give.failed": "<PERSON>i sáhte bostit d<PERSON><PERSON><PERSON> v<PERSON> (<PERSON><PERSON> ii sáhte bostit, dahje lea juo bost<PERSON><PERSON><PERSON><PERSON> garra<PERSON>bbot)", "commands.effect.give.success.multiple": "Váikkuhan %s váikkuhusa %s <PERSON>đaide", "commands.effect.give.success.single": "%2$s váikkuhuvvui %1$s váikkuhusain", "commands.enchant.failed": "Ii mihkkege rievdan. Spealláris ii soaite dávvir gieđas dahje noaidun ii heiven dávvirii", "commands.enchant.failed.entity": "%s ii leat dohkkehu<PERSON>von áhta dán kommándos", "commands.enchant.failed.incompatible": "%s ii sáhte doalahit dien noaiddu", "commands.enchant.failed.itemless": "%s ii leat doallamin dávviriid", "commands.enchant.failed.level": "%s lea bajimus dássi %s dásiin mat leat dohkke<PERSON><PERSON><PERSON> no<PERSON>s", "commands.enchant.success.multiple": "Bidjan %s noaiduma %s entitiaide", "commands.enchant.success.single": "Bidjan %s noaiduma %s spealli dávvirii", "commands.execute.blocks.toobig": "<PERSON><PERSON><PERSON> olu blohkat oaivvilduv<PERSON> guov<PERSON> (eanemustá %s, ledje %s)", "commands.execute.conditional.fail": "Iskkus ii čađahuvvon", "commands.execute.conditional.fail_count": "Iskkus ii čađahuvvon: %s geardde", "commands.execute.conditional.pass": "Iskkus dohkkehuvvoi", "commands.execute.conditional.pass_count": "Iskkus dohkkehuvvoi: %s geardde", "commands.execute.function.instantiationFailure": "%s do<PERSON>mma álggaheapmi ii lihkostuvvan: %s", "commands.experience.add.levels.success.multiple": "Addán %s noaidundási %s olbmuide", "commands.experience.add.levels.success.single": "Addan %s noaidundási %s olbmui", "commands.experience.add.points.success.multiple": "%s noaidunčuoggá addon %s olbmuide", "commands.experience.add.points.success.single": "Addán %s noaidunčuoggá %s olbmui", "commands.experience.query.levels": "%s leat %s noaidundási", "commands.experience.query.points": "%s leat %s noaidunčoggá", "commands.experience.set.levels.success.multiple": "Bidjan %s noaidundási %s olbmuide", "commands.experience.set.levels.success.single": "Bidjan %s noaidundási %s olbmui", "commands.experience.set.points.invalid": "Ii sáhte bidjat no<PERSON>un<PERSON>uo<PERSON> badjel speallára dálá maximum čuoggaid", "commands.experience.set.points.success.multiple": "Bidjan %s noaidunčuoggá %s olbmuide", "commands.experience.set.points.success.single": "Bidjan %s noaidunčuoggá %s olbmui", "commands.fill.failed": "Blohkat eai bidjon", "commands.fill.success": "Deavdán buot %s blohka", "commands.fill.toobig": "<PERSON><PERSON><PERSON> olu blohkat oaivvilduv<PERSON> guov<PERSON> (eanemustá %s, ledje %s)", "commands.fillbiome.success": "Luonddut bidjon gaskal %s, %s, %s ja %s, %s, %s", "commands.fillbiome.success.count": "%s luonddu biddjojuvve gaskal %s, %s, %s ja %s, %s, %s", "commands.fillbiome.toobig": "<PERSON><PERSON><PERSON> olu blohkat oaivvilduv<PERSON> guov<PERSON> (eanemustá %s, ledje %s)", "commands.forceload.added.failure": "<PERSON><PERSON> gavdnon laigosat maid liv<PERSON><PERSON><PERSON> d<PERSON>n veagal rahpat", "commands.forceload.added.multiple": "%s laigosa lei dárbu veagal ráhpat %s máilmmis %s:s gitta %s:ái", "commands.forceload.added.none": "Eai gávdnon veagal ráhpon laigosat %s máilmmis", "commands.forceload.added.single": "%s laigosa lei dárbu veagal ráhpat %s:s", "commands.forceload.list.multiple": "%s veagal rahpon laigosa gádnui %s m<PERSON><PERSON><PERSON>: %s", "commands.forceload.list.single": "Veagal rahpon laigos gádnui %s m<PERSON><PERSON><PERSON> sa<PERSON>: %s", "commands.forceload.query.failure": "Laigosa sajis %s %s m<PERSON><PERSON><PERSON> ii lean dárbu veagal ráhpat", "commands.forceload.query.success": "Laigosa sajis %s %s máilmmis lea dárbu veagal ráhpat", "commands.forceload.removed.all": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>in doallat rabas buot máilmmebihttážiid %s ilmmis", "commands.forceload.removed.failure": "<PERSON>i mak<PERSON><PERSON><PERSON> m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> heitán dollot rabasiin", "commands.forceload.removed.multiple": "Heittii bákkuin doallat rabas %s máilmmebihttážiid %s ilmmis %s sajádagas gitta %s sajádahkii", "commands.forceload.removed.single": "Heittii b<PERSON><PERSON>in do<PERSON>at rabas %s máilmmebihttáža %s ilmmis", "commands.forceload.toobig": "Menddo olu laigosat vuoruhuvvon guovllus (eanemustá %s, vuoruhuvvon %s)", "commands.function.error.argument_not_compound": "Vearru argumeantasorta: %s, me<PERSON><PERSON><PERSON><PERSON>a", "commands.function.error.missing_argument": "%1$s doaimma \"%2$s\" argumeanta váilu", "commands.function.error.missing_arguments": "%s doaimma argumeanttat váilot", "commands.function.error.parse": "Go lei <PERSON>ggaheamin %s makro, de '%s' kommándu dagahii meattáhusa: %s", "commands.function.instantiationFailure": "%s do<PERSON>mma álggaheapmi ii lihkostuvvan: %s", "commands.function.result": "%s doaibma má<PERSON>cahii %s", "commands.function.scheduled.multiple": "Čađaheamin %s do<PERSON><PERSON>id", "commands.function.scheduled.no_functions": "Ii g<PERSON><PERSON><PERSON> makk<PERSON>rge doaimma mas lea dát namma: %s", "commands.function.scheduled.single": "Čađaheamin %s doaimma", "commands.function.success.multiple": "Čađáhii %s kommándu %s doaimmain", "commands.function.success.multiple.result": "Čađahii %s doaimmaid", "commands.function.success.single": "Čađahii %s kommándu '%s' doaimmas", "commands.function.success.single.result": "Doaibma '%2$s' máhcahii %1$s", "commands.gamemode.success.other": "Bidjan %s spealli %s speallanhábmái", "commands.gamemode.success.self": "Bidjan iežas %s speallanhábmái", "commands.gamerule.query": "Speallannjuolggadus %s lea juste dál: %s", "commands.gamerule.set": "Speallannjuolggadus %s lea dál bidjon ná: %s", "commands.give.failed.toomanyitems": "Ii sáhte addit eambbo go %s bihttá %s dávviris", "commands.give.success.multiple": "Attii %s %s %s olbmuide", "commands.give.success.single": "Attii %s %s %s olbmui", "commands.help.failed": "Amas kommándo dahje it oaččo lobi atnit dan", "commands.item.block.set.success": "<PERSON>lson sajádaga %s, %s, %s %s áđ<PERSON>", "commands.item.entity.set.success.multiple": "<PERSON><PERSON> ovtta sajádaga %s entitias %s áđ<PERSON>", "commands.item.entity.set.success.single": "<PERSON><PERSON> ovtta sajádaga %s entitias %s dávviriin", "commands.item.source.no_such_slot": "Gálddus ii leat %s sajádat", "commands.item.source.not_a_container": "Gálddu sajádat %s, %s, %s ii leat vurkenbáiki", "commands.item.target.no_changed.known_item": "Eai makká<PERSON> do<PERSON>kkehan %s dávvira %s sajádahkii", "commands.item.target.no_changes": "Eai mak<PERSON>t dohkkehan dávvira %s sajádahkii", "commands.item.target.no_such_slot": "Gálddus ii leat %s sajádat", "commands.item.target.not_a_container": "Čuozáhasa sajádat %s, %s, %s ii leat rádju", "commands.jfr.dump.failed": "Ii sáhtán vurket JFR báddejumi: %s", "commands.jfr.start.failed": "Ii lihkostuvvan álggahit JFR guorahallama", "commands.jfr.started": "JFR g<PERSON><PERSON><PERSON>", "commands.jfr.stopped": "JFR guorahallan bisánii ja bidjui %s vuorkái", "commands.kick.owner.failed": "<PERSON>i s<PERSON>hte gevret <PERSON>eaiggáda LANas", "commands.kick.singleplayer.failed": "Ii s<PERSON>hte gevret offline iežainisspealus", "commands.kick.success": "%s gevrejuvvoi: %s", "commands.kill.success.multiple": "%s entitiat goddo", "commands.kill.success.single": "%s god<PERSON>i", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Leat %s maximumma %s spealláriin online: %s", "commands.locate.biome.not_found": "Ii nagodan gávdnat \"%s\" nammasaš luonddu mii lea doarvái lahka", "commands.locate.biome.success": "Lagamus %s gávdno %s sajádagas. (%s blohka dás)", "commands.locate.poi.not_found": "\"%s\" na<PERSON><PERSON><PERSON> bá<PERSON> ii gávdnon lahkosis", "commands.locate.poi.success": "Lagamus %s gávdno %s sajádagas. (%s blohka dás)", "commands.locate.structure.invalid": "Ii gávdno huk<PERSON>hus mas lea \"%s\" namahus", "commands.locate.structure.not_found": "Ii gávdnan \"%s\" namma<PERSON><PERSON> huk<PERSON><PERSON>", "commands.locate.structure.success": "Lagamus %s gávdno %s sajádagas. (%s blohka dás)", "commands.message.display.incoming": "%s﻿ savkala dutnje: %s", "commands.message.display.outgoing": "Don savkalat %s speallái: %s", "commands.op.failed": "Ii mihkkege rievdan. Diet spealli lea juo operatevra", "commands.op.success": "%s šattai operator", "commands.pardon.failed": "Ii mihkkege rievdan. Diet spealli ii leat gildojuvvon", "commands.pardon.success": "Gieldin gesson %s speallis", "commands.pardonip.failed": "Ii mihkkege rievdan. Diet IP ii leat gildojuvvon", "commands.pardonip.invalid": "Boastu IP adreassa", "commands.pardonip.success": "Gieldin gesson %s IPas", "commands.particle.failed": "Partihkkal ii lean oidnosis oktiige", "commands.particle.success": "Čáhjeheamin %s partihkkala", "commands.perf.alreadyRunning": "Doaibmabuv<PERSON>u guorahallan lea juo <PERSON><PERSON><PERSON>", "commands.perf.notRunning": "Doaibmabuvttu guorah<PERSON> ii leat álgg<PERSON>vvan", "commands.perf.reportFailed": "Ii sáhttán ráhkadit sivvaohcanrapporta", "commands.perf.reportSaved": "Ráhkadii sivvaohcanrapporta %s gihppagii", "commands.perf.started": "Álggahii 10 sekundasaš doaibmabuktoguorahallama (čále \"/debug stop\" jus áiggut bissehit á<PERSON>)", "commands.perf.stopped": "Heitihii doaibmabuvttu guorahallama go lei vássán %s sekundda ja %s coahkkima (%s coahkkima guđege sekundas)", "commands.place.feature.failed": "Iešvuođa bidjan ii lihkostuvvan", "commands.place.feature.invalid": "Ii gávdno iešvuohta mas lea \"%s\" namahus", "commands.place.feature.success": "Bidjan \"%s\" %s, %s, %s sajádahkii", "commands.place.jigsaw.failed": "Ásahanblohka ráhkadeapmi ii lihkostuvvan", "commands.place.jigsaw.invalid": "Ii gávdno m<PERSON>lle<PERSON>t mas lea \"%s\"", "commands.place.jigsaw.success": "Ásahanblohkka ráhkaduvvui %s, %s, %s sajádagas", "commands.place.structure.failed": "Huksehusa ráhkadeapmi ii lihkostuvvan", "commands.place.structure.invalid": "Ii gávdno huk<PERSON>hus mas lea \"%s\" namahus", "commands.place.structure.success": "\"%s\" na<PERSON><PERSON><PERSON> huk<PERSON> rá<PERSON>kaduvvui %s, %s, %s sajádagas", "commands.place.template.failed": "M<PERSON>lle <PERSON> ii lihkostuvvan", "commands.place.template.invalid": "Ii gávdno málle mas lea namma \"%s\"", "commands.place.template.success": "<PERSON><PERSON><PERSON> \"%s\" málle %s, %s, %s sajádagas", "commands.playsound.failed": "<PERSON><PERSON><PERSON> lea menddo guhkkin eret gullot", "commands.playsound.success.multiple": "Čuojahii %s jiena %s olbmuide", "commands.playsound.success.single": "Čuojahii %s jiena %s olbmui", "commands.publish.alreadyPublished": "Máŋggaidspealli lea juo álggahuvvon %s porttas", "commands.publish.failed": "<PERSON>i sáhte rahpat b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> spealu", "commands.publish.started": "Báikkálaš speallu rahpasii dás %s", "commands.publish.success": "Máŋggaidspealli lea dál álggahuvvon %s porttas", "commands.random.error.range_too_large": "Summalis árvu ii sáhte eanet go 2147483646", "commands.random.error.range_too_small": "Summalis árvu ferte leahkit stu<PERSON> go 2", "commands.random.reset.all.success": "Máhcat %s summalis vuoruid", "commands.random.reset.success": "Máhcat summalis vuoruid %s", "commands.random.roll": "%s oaččui %s (gaskkal %s ja %s)", "commands.random.sample.success": "Summalis árvolohkku: %s", "commands.recipe.give.failed": "<PERSON><PERSON> makk<PERSON> duodjeneavvagat rahppon", "commands.recipe.give.success.multiple": "Rahpan %s duodjeneavvagiid %s speallái", "commands.recipe.give.success.single": "Rahpan %s duodjeneavvagiid %s speallárii", "commands.recipe.take.failed": "<PERSON><PERSON> makk<PERSON>rge duodjeneavvagat vajálduvvon", "commands.recipe.take.success.multiple": "Geassán %s duodjeneavvagiid %s olbmuin", "commands.recipe.take.success.single": "Geassán %s duodjeneavvagiid %s spealláris", "commands.reload.failure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, do<PERSON><PERSON> ain boares data", "commands.reload.success": "<PERSON>đas<PERSON>tti<PERSON>!", "commands.ride.already_riding": "%s lea juo riidemin %s", "commands.ride.dismount.success": "%s heitii riidemis %s", "commands.ride.mount.failure.cant_ride_players": "Spelli<PERSON> ii sáhte riidet", "commands.ride.mount.failure.generic": "%s ii sáhttán álgit riidet %s", "commands.ride.mount.failure.loop": "Seamma ealli dahje <PERSON> ii sáhte riidet iežasa dahjege iežas riidejeddjiid", "commands.ride.mount.failure.wrong_dimension": "Ii sáhte riidet áđa mii lea ear<PERSON> ilmmis", "commands.ride.mount.success": "%s álggii riidet %s", "commands.ride.not_riding": "%s ii leat riidemin", "commands.rotate.success": "Jorahuvvon %s", "commands.save.alreadyOff": "<PERSON>urken lea juo jáddaduvvon", "commands.save.alreadyOn": "Vurken lea juo bidjon ala", "commands.save.disabled": "Automáhtalaš vurken lea dál j<PERSON>ddaduvvon", "commands.save.enabled": "Automáhtalaš vurken lea dál alde", "commands.save.failed": "Ii sáhte vurket máilmmi (lea go doarv<PERSON>i sadji <PERSON>?)", "commands.save.saving": "<PERSON><PERSON><PERSON><PERSON><PERSON> (sáhttá bistit unna bottu!)", "commands.save.success": "<PERSON><PERSON><PERSON> m<PERSON>", "commands.schedule.cleared.failure": "Dagut mas id %s: a", "commands.schedule.cleared.success": "Sihkon eret %s dagu main lei id %s", "commands.schedule.created.function": "Plánen '%s' doaimma %s coahkkimis %s speallaáiggis", "commands.schedule.created.tag": "Plánen '%s' mearkka %s coahkkimis %s spealloáiggis", "commands.schedule.macro": "<PERSON>i s<PERSON>hte bidjat johtui macro", "commands.schedule.same_tick": "<PERSON><PERSON> ii sáhte plánet", "commands.scoreboard.objectives.add.duplicate": "Obje<PERSON><PERSON><PERSON>va mas seamma namma juo gávdno", "commands.scoreboard.objectives.add.success": "Ráh<PERSON>dan ođđa objektiivva mas namma %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Ii mihkkege rievdan. Diet čájehansajádat lea juo guoros", "commands.scoreboard.objectives.display.alreadySet": "Ii mihkkege rievdan. Diet čájehansajádat č<PERSON> juo dien objektiivva", "commands.scoreboard.objectives.display.cleared": "Sihkkon buot objektiivvaid %s čájehansajádagas", "commands.scoreboard.objectives.display.set": "Bidjan <PERSON>hansajádaga %s nu ahte čájeha %s objektiivva", "commands.scoreboard.objectives.list.empty": "Eai leat objektiivvat", "commands.scoreboard.objectives.list.success": "Leat %s objektiivva: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Jáddádan %s objektiivva auto-ođasmahttima šearmma", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Bidjan ala %s objektiivva auto-ođasmahttima šearmma", "commands.scoreboard.objectives.modify.displayname": "Molson %s oidnosis nama %s nammii", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "G<PERSON><PERSON><PERSON>š lohkomálle %s objektiivvas", "commands.scoreboard.objectives.modify.objectiveFormat.set": "<PERSON><PERSON> d<PERSON><PERSON>š lohkomálle %s objektiivvas", "commands.scoreboard.objectives.modify.rendertype": "Rievdadan %s objektiiva rendermáli", "commands.scoreboard.objectives.remove.success": "Sihkkon áibbas %s objektiivva", "commands.scoreboard.players.add.success.multiple": "Lasihan %s čuoggá %s tabellii %s entitiaide", "commands.scoreboard.players.add.success.single": "Lasihan %s čuoggá %s tabellii %s olbmui (dál lea %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Gurren %s áđa čájehannama %s távvalis", "commands.scoreboard.players.display.name.clear.success.single": "Gurren %s čájehannama %s távvalis", "commands.scoreboard.players.display.name.set.success.multiple": "Molson %s čájehannammii %s áđas %s távvalis", "commands.scoreboard.players.display.name.set.success.single": "Molson %s čájehannammii %s ovddas %s távvalis", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Gurren %s <PERSON>đa <PERSON> %s távvalis", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Gurren %s lohkomálle %s távvalis", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "<PERSON><PERSON> lohkomálle %s áđas %s távvalis", "commands.scoreboard.players.display.numberFormat.set.success.single": "<PERSON>lson lohkomálle %s ovddas %s távvalis", "commands.scoreboard.players.enable.failed": "Ii mihkkege rievdan. Diet álggahus lea juo bidjon ala", "commands.scoreboard.players.enable.invalid": "<PERSON><PERSON><PERSON><PERSON> dohkke álggahan objektiivaidguin", "commands.scoreboard.players.enable.success.multiple": "Bidjan ala %s álggahusa %s entitiaide", "commands.scoreboard.players.enable.success.single": "Bidjan ala %s álggahusa %s áhtii", "commands.scoreboard.players.get.null": "Ii sáhte viežžat %s dieđuid %s áđain; eai leat bidjon", "commands.scoreboard.players.get.success": "%s leat %s %s", "commands.scoreboard.players.list.empty": "Eai leat makkárge me<PERSON>juvvon entitiat", "commands.scoreboard.players.list.entity.empty": "%s listtus eai leat <PERSON>", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "Spealláris %s leat %s čuoggá:", "commands.scoreboard.players.list.success": "Leat %s merkejuvvon entita: %s", "commands.scoreboard.players.operation.success.multiple": "Ođásmahtten %s %s entitiain", "commands.scoreboard.players.operation.success.single": "Bidjan %s listui %s olbmui %s čuogg<PERSON>id", "commands.scoreboard.players.remove.success.multiple": "Sihkkon %s čuoggá %s tabellas %s entitiain", "commands.scoreboard.players.remove.success.single": "Sihkkon %s čuoggá %s tabellas %s spealláris (dál lea %s)", "commands.scoreboard.players.reset.all.multiple": "Álggahan ođđ<PERSON>t buot čuoggáid %s entitiain", "commands.scoreboard.players.reset.all.single": "Álggahan ođđasit buot %s entitia čuoggáid", "commands.scoreboard.players.reset.specific.multiple": "Álggahan ođđasit %s %s entitiain", "commands.scoreboard.players.reset.specific.single": "Álggahan ođđasit %s %s entitia ovddas", "commands.scoreboard.players.set.success.multiple": "Bidjan %s čuoggá %s entitiaide %s", "commands.scoreboard.players.set.success.single": "Bidjan %s listui %s olbmui %s čuogg<PERSON>id", "commands.seed.success": "Vuođđudanlohku: %s", "commands.setblock.failed": "<PERSON><PERSON> s<PERSON><PERSON> bid<PERSON> blo<PERSON>ka", "commands.setblock.success": "Rievdadan blohka %s, %s, %s sajádagas", "commands.setidletimeout.success": "Speallára boddu lea dál %s minuvtta guhkki", "commands.setidletimeout.success.disabled": "<PERSON><PERSON><PERSON><PERSON> bod<PERSON><PERSON> lea d<PERSON>l j<PERSON>", "commands.setworldspawn.failure.not_overworld": "<PERSON><PERSON><PERSON><PERSON><PERSON> duš<PERSON>e bidjat máilmmi ealáskanbáikki dá<PERSON><PERSON><PERSON><PERSON> máilmmis", "commands.setworldspawn.success": "<PERSON><PERSON><PERSON> m<PERSON> e<PERSON>kanbáikki %s, %s, %s [%s] báikái", "commands.spawnpoint.success.multiple": "Bidjan ealáskanbáikki %s, %s, %s [%s] sajádahkii %s ilmmis %s spealliide", "commands.spawnpoint.success.single": "Bidjan ealáskanbáikki %s, %s, %s [%s] sajádahkii %s ilmmis %s speallárii", "commands.spectate.not_spectator": "%s ii leat geahčči modusis", "commands.spectate.self": "It sáhte geahččat iežat", "commands.spectate.success.started": "Dál geahččamin %s", "commands.spectate.success.stopped": "It leat šat geahččamin entitya", "commands.spreadplayers.failed.entities": "Ii sáhttán biđget %s entitiaid birra %s, %s (menddo olu entitiat dán gaskas - geahččal geavahit juohkinsturrodatmeari mii lea unnit go %s)", "commands.spreadplayers.failed.invalid.height": "Dohkketmeahttun maxHeight %s; me<PERSON><PERSON><PERSON><PERSON> eambbo go máilmmi unnimusa %s", "commands.spreadplayers.failed.teams": "Ii sáhttán biđget %s joavkkuid birra %s, %s (menddo olu entitiat dán gaskas - geahččal geavahit juohkinsturrodatmeari unnit go %s)", "commands.spreadplayers.success.entities": "Biđge %s spealli %s, %s sajádagaid birra, gos gaskamearalaš gaska lea %s blohka", "commands.spreadplayers.success.teams": "Biđgen %s joavkku %s, %s sajádaga birra, gos gaskamearalaš gaska lea %s blohka", "commands.stop.stopping": "Bisseheamin servera", "commands.stopsound.success.source.any": "<PERSON><PERSON><PERSON> buot '%s' jienaid", "commands.stopsound.success.source.sound": "Bissehan '%s' jiena '%s' vuo<PERSON><PERSON><PERSON><PERSON><PERSON>", "commands.stopsound.success.sourceless.any": "<PERSON><PERSON><PERSON> buot jienaid", "commands.stopsound.success.sourceless.sound": "<PERSON><PERSON><PERSON> '%s' jiena", "commands.summon.failed": "Ii sáhte ealáskahttet entitia", "commands.summon.failed.uuid": "Ii sáhttán ealáskit entitia go gávdnui guovttegeardásaš UUIDs", "commands.summon.invalidPosition": "Dohkkemeahttun sajádat ealáskahttemii", "commands.summon.success": "Ealáskahtten ođđa %s", "commands.tag.add.failed": "<PERSON><PERSON><PERSON> lea juo d<PERSON>t me<PERSON>, dahje leat menddo olu me<PERSON>", "commands.tag.add.success.multiple": "Lasihan %s' mearkka %s entitiaide", "commands.tag.add.success.single": "Lasihan %s' mearkka %s áhtii", "commands.tag.list.multiple.empty": "Eai leat makkárge mearkkat %s entitias", "commands.tag.list.multiple.success": "%s entitiain leat %s mearkka oktiibuot: %s", "commands.tag.list.single.empty": "Eai leat mearkkat %s", "commands.tag.list.single.success": "%s leat %s mearkkat: %s", "commands.tag.remove.failed": "<PERSON><PERSON><PERSON> ii leat dát me<PERSON>a", "commands.tag.remove.success.multiple": "Geassán %s' mearkka %s entitiain", "commands.tag.remove.success.single": "Geassán %s' mearkka %s áđas", "commands.team.add.duplicate": "<PERSON><PERSON><PERSON> mas seamma namma juo gávdno", "commands.team.add.success": "Ráhkadii joavkku %s", "commands.team.empty.success": "Válddii eret %s oasseváldi %s joavkkus", "commands.team.empty.unchanged": "Ii mihkkege rievdan. Diet joavku lea juo guoros", "commands.team.join.success.multiple": "Lasihii %s oasálasti %s joavkkui", "commands.team.join.success.single": "Lasihii %s %s joavkkui", "commands.team.leave.success.multiple": "Válddii eret %s oasseváldiid buot joavkkuin", "commands.team.leave.success.single": "Válddii eret %s buot joavkkuin", "commands.team.list.members.empty": "Eai leat oasseváldit %s joavkkus", "commands.team.list.members.success": "%s joavkkus leat %s oasseváldit: %s", "commands.team.list.teams.empty": "<PERSON>ai leat makk<PERSON>rge joa<PERSON>t", "commands.team.list.teams.success": "Leat %s joavkku: %s", "commands.team.option.collisionRule.success": "Oktiibeaškkiheami mearri %s jovkkui lea dál \"%s\"", "commands.team.option.collisionRule.unchanged": "Ii mihkkege rievdan. Oktiibeaškkiheami mearri lea juo dien mearr<PERSON>as", "commands.team.option.color.success": "Ođastahtten %s joavkku ivnni %s ivdnái", "commands.team.option.color.unchanged": "Ii mihkkege rievdan. Dien joavkkus lea juo diet ivdni", "commands.team.option.deathMessageVisibility.success": "Jápmasága oinnolašvuohta %s jovkkui lea dál \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Ii mihkkege rievdan. J<PERSON><PERSON>as<PERSON>ga o<PERSON>ta lea juo dien mearr<PERSON>as", "commands.team.option.friendlyfire.alreadyDisabled": "Ii mihkkege rievdan. <PERSON><PERSON><PERSON><PERSON><PERSON> lea juo jáddaduvvon dien joavkkus", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON>i mihkkege rievdan. <PERSON><PERSON><PERSON><PERSON><PERSON> lea juo alde dien joav<PERSON>", "commands.team.option.friendlyfire.disabled": "Jáddadan ustitlaš časkima %s joavkkus", "commands.team.option.friendlyfire.enabled": "Bidjan ala ustitlaš časkima %s joavkkus", "commands.team.option.name.success": "Ođásmahtten nama %s joavkkus", "commands.team.option.name.unchanged": "Ii mihkkege rievdan. Dien joavkkus lea juo diet namma", "commands.team.option.nametagVisibility.success": "Nammamlihpu oinnolašvuohta %s jovkkui lea dál \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Ii mihkkege rievdan. Nammamearkka o<PERSON>š<PERSON>ohta lea juo dien mearr<PERSON>", "commands.team.option.prefix.success": "Joavkku álgonamma lea bidjon %s nammii", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Ii mihkkege rievdan. Joavku juo ii sáhte oaidnit geidojuvvon oasseváldiid", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Ii mihkkege rievdan. Joavku juo sáhttá oaidnit geidojuvvon oasseváldiid", "commands.team.option.seeFriendlyInvisibles.disabled": "%s joavku ii šat sáhte oaidnit geidojuvvon joavkooasseváldiid", "commands.team.option.seeFriendlyInvisibles.enabled": "%s joavku sáhttá oaidnit geidojuvvon joavkooasseváldiid", "commands.team.option.suffix.success": "Joavkku loahppanamma lea bidjon %s nammii", "commands.team.remove.success": "Joavku %s lea sihkkon", "commands.teammsg.failed.noteam": "Don fertet leat mielde joavkkus ovdal go sáht<PERSON>t sáddet dieđu jovkkui", "commands.teleport.invalidPosition": "<PERSON>i s<PERSON>hte sirdi<PERSON> dien b<PERSON>i", "commands.teleport.success.entity.multiple": "Sirdilan %s entitiaid %s lusa", "commands.teleport.success.entity.single": "%s sirdilii %s lusa", "commands.teleport.success.location.multiple": "Sirdilii %s entitiaid dán báikái %s, %s, %s", "commands.teleport.success.location.single": "%s sirdilii %s, %s, %s sajádahkii", "commands.test.batch.starting": "Starting environment %s batch %s", "commands.test.clear.error.no_tests": "Could not find any tests to clear", "commands.test.clear.success": "Cleared %s structure(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "<PERSON><PERSON>l nu ahte máŋge <PERSON>i", "commands.test.create.success": "Created test setup for test %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Found no test instances", "commands.test.error.non_existant_test": "Test %s could not be found", "commands.test.error.structure_not_found": "Test structure %s could not be found", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "The structure size must be less than %s blocks along each axis", "commands.test.locate.done": "Finished locating, found %s structure(s)", "commands.test.locate.found": "Found structure at: %s (distance: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "No tests to run", "commands.test.relative_position": "Position relative to %s: %s", "commands.test.reset.error.no_tests": "Could not find any tests to reset", "commands.test.reset.success": "Reset %s structure(s)", "commands.test.run.no_tests": "No tests found", "commands.test.run.running": "Running %s test(s)...", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "All required tests passed :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "Proseanttat: P50: %sms P95: %sms P99: %sms, čoakkus: %s", "commands.tick.query.rate.running": "Válljejuvvon coahkkinleaktu: %s guđege sekundda\nGaskamearalaš áigi guđege coahkkimis: %sms (Válljejumis: %sms)", "commands.tick.query.rate.sprinting": "<PERSON><PERSON><PERSON>i coahkkinleaktu: %s guđege sekundda (ii čuovvoluvvo, duš<PERSON><PERSON> diehtun).\nGaskamearalaš áigi juohke tickas: %sms", "commands.tick.rate.success": "Bidjan válljejuvvon coahkkinleavttu %s guđ<PERSON> sekunddii", "commands.tick.sprint.report": "Viehkan čađahuvvui %s coahkkimiiguin guđege sekunddas, dahjege %s ms guđege coahkkimis", "commands.tick.sprint.stop.fail": "<PERSON><PERSON> leat coahkkinjohttima jođus", "commands.tick.sprint.stop.success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dá<PERSON><PERSON>", "commands.tick.status.frozen": "<PERSON><PERSON><PERSON><PERSON> lea jik<PERSON>on", "commands.tick.status.lagging": "<PERSON><PERSON><PERSON><PERSON>, muhto ii nagot doalahit válljejuv<PERSON> coahkkinleavttu", "commands.tick.status.running": "Speallu doaibmá nugo galgá", "commands.tick.status.sprinting": "<PERSON><PERSON><PERSON><PERSON> joh<PERSON> j<PERSON>", "commands.tick.step.fail": "Spealu njuiken ii lihkostuvvan - speallu ferte álggos jiekŋut", "commands.tick.step.stop.fail": "<PERSON>ai leat coahkkin<PERSON><PERSON><PERSON><PERSON> doaimmas", "commands.tick.step.stop.success": "Gaskkalduhtt<PERSON> dá<PERSON><PERSON> co<PERSON>", "commands.tick.step.success": "Njuikumin %s coahkkima", "commands.time.query": "<PERSON><PERSON> lea dál %s", "commands.time.set": "<PERSON><PERSON><PERSON> %s", "commands.title.cleared.multiple": "Jávkadan bajilčállaga %s olbmuin", "commands.title.cleared.single": "Jávkadan bajilčállaga %s spealláris", "commands.title.reset.multiple": "Máhcat bajilčállaga heivehusaid %s olbmuin", "commands.title.reset.single": "Máhcat bajilčállaga heivehusaid %s spealláris", "commands.title.show.actionbar.multiple": "Čájeheamin ođ<PERSON><PERSON>jáčállaga %s olbmuide", "commands.title.show.actionbar.single": "Čájeheamin ođ<PERSON><PERSON>jáčállaga %s olbmui", "commands.title.show.subtitle.multiple": "Čájeheamin ođ<PERSON>a vuolilčállaga %s olbmuide", "commands.title.show.subtitle.single": "Čájeheamin ođđa vuolilčállaga %s olbmui", "commands.title.show.title.multiple": "Čájeheamin ođ<PERSON><PERSON> bajilčállaga %s olbmuide", "commands.title.show.title.single": "Čájeheamin ođ<PERSON>a bajilčállaga %s olbmui", "commands.title.times.multiple": "<PERSON><PERSON> b<PERSON>čállaga čájehangerddiid %s olbmuin", "commands.title.times.single": "<PERSON><PERSON> b<PERSON>ilčállaga čájehangerddiid %s spealláriin", "commands.transfer.error.no_players": "<PERSON>rte d<PERSON> unnimusat ovtta spealli man galgá sirdit", "commands.transfer.success.multiple": "Sirdimin %s spelliid deike %s:%s", "commands.transfer.success.single": "Sirdimin %s deike %s:%s", "commands.trigger.add.success": "Álggahan %s (lasihan %s mearrádussii)", "commands.trigger.failed.invalid": "<PERSON><PERSON> du<PERSON>gg<PERSON> objektiv<PERSON>id mat lea \"álggah<PERSON><PERSON>\"", "commands.trigger.failed.unprimed": "Don it sáhte álgg<PERSON>t dán objektiivva vuos", "commands.trigger.set.success": "Álggahan %s (bidjan meari dása %s)", "commands.trigger.simple.success": "Álggahan %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Bálvvá veršuvdnadieđut:", "commands.version.id": "id = %s", "commands.version.name": "namma = %s", "commands.version.pack.data": "páhka_diehtu = %s", "commands.version.pack.resource": "páhka_resursa = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "ráidu = %s", "commands.version.stable.no": "stáđis = no", "commands.version.stable.yes": "stáđis = juo", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "<PERSON><PERSON><PERSON><PERSON><PERSON>kin", "commands.weather.set.rain": "Riev<PERSON><PERSON> dá<PERSON> nu ahte arvá", "commands.weather.set.thunder": "Riev<PERSON><PERSON> dá<PERSON> nu ahte arvá ja lea rihttu", "commands.whitelist.add.failed": "Spealli lea juo vuoruhuvvon", "commands.whitelist.add.success": "Lasihan %s vuo<PERSON><PERSON><PERSON><PERSON>", "commands.whitelist.alreadyOff": "<PERSON><PERSON><PERSON><PERSON>listu lea juo jáddaduvvon", "commands.whitelist.alreadyOn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lea juo alde", "commands.whitelist.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lea dál j<PERSON><PERSON>", "commands.whitelist.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lea dál alde", "commands.whitelist.list": "Leat %s vuoruhuvvon spealli: %s", "commands.whitelist.none": "Eai leat makkárge vuoruhuvvon speallárat", "commands.whitelist.reloaded": "Ođasmahtii vuoruhanlisttu", "commands.whitelist.remove.failed": "Spealli ii leat vuoruhuvvon", "commands.whitelist.remove.success": "Geassán %s vuoruhanlisttus", "commands.worldborder.center.failed": "Ii mihkkege rievdan. Máilmmiravda lea juo bidjon diekko", "commands.worldborder.center.success": "<PERSON><PERSON><PERSON>guovddu %s, %s sajádahkii", "commands.worldborder.damage.amount.failed": "Ii mihkkege rievdan. Máilmmiravdda sorbmen lea juo nie olu", "commands.worldborder.damage.amount.success": "<PERSON><PERSON> m<PERSON>mmiravdda soarbmináiggi %s juohke blohkkii juohke sekundii", "commands.worldborder.damage.buffer.failed": "Ii mihkkege rievdan. Máilmmiravdda sorbmenskuolja lea juo seamma gaskka", "commands.worldborder.damage.buffer.success": "<PERSON><PERSON><PERSON> m<PERSON> so<PERSON>bminskuolja %s blohkkii", "commands.worldborder.get": "Dálá máilmmeravda lea %s blohka viiddis", "commands.worldborder.set.failed.big": "Mailmmiravda ii sáhte leat govdabut go %s blohka", "commands.worldborder.set.failed.far": "Máilbmirádji ii sáhte leat guhkit go %s birccu", "commands.worldborder.set.failed.nochange": "Ii mihkkege rievdan. Máilmmiravda lea juo dien stuorrod<PERSON>s", "commands.worldborder.set.failed.small": "Máilmmiravda ii sáhte leat gáržžit go ovtta blohkka", "commands.worldborder.set.grow": "Stuorideamin máilmmiravdda %s blohkka viidodahkii %s sekundda áiggis", "commands.worldborder.set.immediate": "<PERSON><PERSON><PERSON>avdda %s blohkka viidodahkii", "commands.worldborder.set.shrink": "Unnideamin máilmmiravdda %s blohka viidodahkii %s sekundda áiggis", "commands.worldborder.warning.distance.failed": "Ii mihkkege rievdan. Máilmmiravdda sorbmenskuoljadiehtu lea juo seamma gaskka", "commands.worldborder.warning.distance.success": "<PERSON><PERSON><PERSON> m<PERSON>mmeravdda soarbmingaska váruhusa %s blohka viidodahkii", "commands.worldborder.warning.time.failed": "Ii mihkkege rievdan. Máilmmiravdda sorbmenskuoljadiehtu lea juo nie olu", "commands.worldborder.warning.time.success": "<PERSON><PERSON><PERSON> m<PERSON>mmerav<PERSON>a soarbminmuittuhusáiggi %s sekundii", "compliance.playtime.greaterThan24Hours": "<PERSON><PERSON><PERSON> leat speallan eambbo go 24 diimmu", "compliance.playtime.hours": "Dál leat speallan %s diimmu", "compliance.playtime.message": "<PERSON><PERSON><PERSON><PERSON><PERSON> speallan sáhttá hehttet beaivválaš eallima", "connect.aborted": "Boaŧkanii", "connect.authorizing": "Ra<PERSON><PERSON><PERSON>...", "connect.connecting": "Čatnaseamin serverii...", "connect.encrypting": "Krypteremin...", "connect.failed": "Oktavuohta serveriin ii ollašuvvon", "connect.failed.transfer": "Oktavuohta boatkanii go lei sirdimin serverii", "connect.joining": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>...", "connect.negotiating": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "connect.reconfiging": "<PERSON><PERSON><PERSON><PERSON><PERSON> bar<PERSON>...", "connect.reconfiguring": "<PERSON><PERSON><PERSON><PERSON><PERSON> bar<PERSON>...", "connect.transferring": "<PERSON><PERSON><PERSON> o<PERSON> serverii...", "container.barrel": "<PERSON><PERSON><PERSON><PERSON>", "container.beacon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.beehive.bees": "Uvllut: %s / %s", "container.beehive.honey": "Honnet: %s / %s", "container.blast_furnace": "Su<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.brewing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.cartography_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.chest": "Giisá", "container.chestDouble": "Stuora giis<PERSON>", "container.crafter": "<PERSON><PERSON><PERSON><PERSON>", "container.crafting": "<PERSON><PERSON><PERSON>", "container.creative": "<PERSON><PERSON><PERSON><PERSON>", "container.dispenser": "Báhčči", "container.dropper": "<PERSON><PERSON><PERSON>", "container.enchant": "Noido", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s lasura", "container.enchant.lapis.one": "1 lasura", "container.enchant.level.many": "%s no<PERSON><PERSON><PERSON><PERSON>", "container.enchant.level.one": "1 noaidundássi", "container.enchant.level.requirement": "Dárbbašuvvon dássi: %s", "container.enderchest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.furnace": "Uvdna", "container.grindstone_title": "Divo & sihko no<PERSON>sa", "container.hopper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.inventory": "Dávvirvuorká", "container.isLocked": "%s lea lohkkaduvvon!", "container.lectern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.loom": "Gákkesmuorat", "container.repair": "Divo & atte nama", "container.repair.cost": "<PERSON><PERSON><PERSON> haddi: %1$s", "container.repair.expensive": "Menddo <PERSON>!", "container.shulkerBox": "Skulkerboksa", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "ja %s eanet...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Suovastanuvdna", "container.spectatorCantOpen": "Ii sáhte rahpat. Dávvirat eai leat vuos ráhkaduvvan.", "container.stonecutter": "Geađgečuo<PERSON><PERSON>", "container.upgrade": "<PERSON>đas<PERSON>tte d<PERSON>vvir<PERSON>", "container.upgrade.error_tooltip": "Dávvira ii sáhte ođasmahttet dieinna lágiin", "container.upgrade.missing_template_tooltip": "<PERSON><PERSON>", "controls.keybinds": "Boalločadnosat...", "controls.keybinds.duplicateKeybinds": "<PERSON><PERSON><PERSON> boallu maid lea gea<PERSON> dá<PERSON>: %s", "controls.keybinds.title": "Boalločadnosat", "controls.reset": "Máhcat", "controls.resetAll": "<PERSON><PERSON><PERSON><PERSON>", "controls.title": "Boalloheivehusat", "createWorld.customize.buffet.biome": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "createWorld.customize.buffet.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON><PERSON> h<PERSON>men", "createWorld.customize.flat.height": "Allodat", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Vuolemus - %s", "createWorld.customize.flat.layer.top": "Bajemuš - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON>", "createWorld.customize.flat.tile": "Materiálagearddit", "createWorld.customize.flat.title": "Superduolbba hábmen", "createWorld.customize.presets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.presets.list": "<PERSON><PERSON><PERSON>, dá leat moadde málle mat leat ovdagihtii ráhkaduvvon!", "createWorld.customize.presets.select": "<PERSON><PERSON><PERSON> d<PERSON> m<PERSON>", "createWorld.customize.presets.share": "<PERSON>ig<PERSON>t go juohkit iežat málle ear<PERSON>? Máŋges čállosa mii čuožžu bovssas ja sádde!", "createWorld.customize.presets.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.preparing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>mmi h<PERSON>...", "createWorld.tab.game.title": "Speallu", "createWorld.tab.more.title": "<PERSON><PERSON><PERSON>", "createWorld.tab.world.title": "<PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.button.attribution": "Lasihusat", "credits_and_attribution.button.credits": "Dahkkit", "credits_and_attribution.button.licenses": "Liseanssat", "credits_and_attribution.screen.title": "Dahkkit ja lasihusat", "dataPack.bundle.description": "Váldá atnui eksperimentála dávvirbur<PERSON>", "dataPack.bundle.name": "Dávvirbursa", "dataPack.locator_bar.description": "Show the direction of other players in multiplayer", "dataPack.locator_bar.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataPack.minecart_improvements.description": "Buoriduvvon roggangielkálihkadeapmi", "dataPack.minecart_improvements.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> buo<PERSON><PERSON><PERSON>", "dataPack.redstone_experiments.description": "Geahččaladdi redstone-rievdadusat", "dataPack.redstone_experiments.name": "Redstone-geahččaleamit", "dataPack.title": "<PERSON><PERSON><PERSON><PERSON>", "dataPack.trade_rebalance.description": "Ođasmuvvan gávppašeapmi gililaččaiguin", "dataPack.trade_rebalance.name": "Gil<PERSON><PERSON><PERSON><PERSON> gá<PERSON>ppašeami mudden", "dataPack.update_1_20.description": "Minecraft 1.20 ođđa iešvuođat ja sisdoalut", "dataPack.update_1_20.name": "Ođasmahttin 1.20", "dataPack.update_1_21.description": "Minecraft 1.21 o<PERSON><PERSON><PERSON> ja sisdo<PERSON>ut", "dataPack.update_1_21.name": "1.21 be<PERSON><PERSON><PERSON><PERSON>", "dataPack.validation.back": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataPack.validation.failed": "Diehtopáhka duođašteapmi ii oláhuvvon!", "dataPack.validation.reset": "Máhcat d<PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "dataPack.validation.working": "Duođašteamin válljejuvvon diehtopáhkaid...", "dataPack.vanilla.description": "Minecraft:a d<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataPack.vanilla.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataPack.winter_drop.description": "<PERSON><PERSON><PERSON><PERSON> ja sisdo<PERSON> d<PERSON>lu<PERSON>", "dataPack.winter_drop.name": "Dálveluoitin", "datapackFailure.safeMode": "<PERSON><PERSON><PERSON><PERSON> modus", "datapackFailure.safeMode.failed.description": "Máil<PERSON>is leat vearu dahje billašuvvan vurkendieđut.", "datapackFailure.safeMode.failed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ii lihkostuv<PERSON>.", "datapackFailure.title": "Dálá válljeju<PERSON><PERSON> diehtopáhkaid meattáhusat bissehedje máilmmi rahpamis.\nSáht<PERSON>t geahč<PERSON>alit rahpat dan dušše dá<PERSON><PERSON><PERSON><PERSON> diehtop<PERSON>hkain (\"sihkkaris modus\") dahje máhccat fas váldosiidui ja ieš divvut dan.", "death.attack.anvil": "%1$s njuvdui eatnamii go stáđđi gahčai ala", "death.attack.anvil.player": "%1$s njuvdui eatnamii go stáđđi gahčai ala seammás go doaruhi %2$s", "death.attack.arrow": "%2$s bážii %1$s", "death.attack.arrow.item": "%2$s geavahii %3$s báhčit %1$s", "death.attack.badRespawnPoint.link": "Die<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "death.attack.badRespawnPoint.message": "%2$s gottii %1$s spealli", "death.attack.cactus": "%1$s čuggui jámas", "death.attack.cactus.player": "%1$s v<PERSON><PERSON>i káktusa njeaiga go geahččalii viehkat eret %2$s", "death.attack.cramming": "%1$s čárvojuvvoi menddo eatnat", "death.attack.cramming.player": "%2$s njuvdii eatnamii %1$s", "death.attack.dragonBreath": "%1$s bulii dr<PERSON>ga b<PERSON>", "death.attack.dragonBreath.player": "%1$s bulii dr<PERSON>ga bosáldagas %2$s dihte", "death.attack.drown": "%1$s heavvanii", "death.attack.drown.player": "%1$s heavvanii go geahččalii viehkat eret %2$s", "death.attack.dryout": "%1$s goikkai jámas", "death.attack.dryout.player": "%1$s goikkai jámas go geahččalii báhtarit %2$s spealli", "death.attack.even_more_magic": "%1$s god<PERSON>i garr<PERSON> noaidumiin", "death.attack.explosion": "%1$s bávkkehii", "death.attack.explosion.player": "%2$s bávkkehii %1$s spealli", "death.attack.explosion.player.item": "%2$s geavahii %3$s bávkkehit %1$s spealli", "death.attack.fall": "%1$s gahčai menddo garrasit", "death.attack.fall.player": "%1$s gahčai menddo garrasit dan bottu go geahččalii viehkat earet %2$s:as", "death.attack.fallingBlock": "%1$s njuvdui eatnamii go blohkka gahčai ala", "death.attack.fallingBlock.player": "%1$s njuvduii eatnamii go geahččalii doaruhit %2$s", "death.attack.fallingStalactite": "Stalakihtta mii gahčai čuggestii %1$s spealli", "death.attack.fallingStalactite.player": "Stalakihtta mii gahčai čuggestii %1$s spealli dan bottu go %2$s doaruhii", "death.attack.fireball": "%2$s bážii dollageđgiin %1$s", "death.attack.fireball.item": "%2$s geavahii %3$s báhčit dollageađgiin %1$s", "death.attack.fireworks": "%1$s bávkaluvvui", "death.attack.fireworks.item": "%1$s bávkkehii albmái rahkeahta geažil máid %3$s bážii %2$s dávviriin", "death.attack.fireworks.player": "%1$s bávkaluvvui dan bottu go vikkai doaruhit %2$s", "death.attack.flyIntoWall": "%1$s vásihii movt kinetiskas energia doaibmá", "death.attack.flyIntoWall.player": "%1$s vásihii movt kinetiskas energia doaibmá dan bottu go geahččalii viehkat earet %2$s:as", "death.attack.freeze": "%1$s galbmui jámas", "death.attack.freeze.player": "%1$s galbmui jámas %2$s spealli dihte", "death.attack.generic": "%1$s jámii", "death.attack.generic.player": "%2$s gottii %1$s", "death.attack.genericKill": "%1$s spealli jámii", "death.attack.genericKill.player": "%1$s god<PERSON>i go %2$s do<PERSON><PERSON>i su", "death.attack.hotFloor": "%1$s fuobmái ahte lei lava láhttis", "death.attack.hotFloor.player": "%1$s váccii soarbminguovlui %2$s dihte", "death.attack.inFire": "%1$s bulii", "death.attack.inFire.player": "%1$s váccii dollii go doaruhii %2$s", "death.attack.inWall": "%1$s hávkkai seainni siste", "death.attack.inWall.player": "%1$s hávkkai seainni siste dan bottu go doaruhii %2$s", "death.attack.indirectMagic": "%2$s gottii %1$s noaidumiin", "death.attack.indirectMagic.item": "%2$s geavahii %3$s goddit %1$s", "death.attack.lava": "%1$s vikkai lavas vuojadit", "death.attack.lava.player": "%1$s vikkai lavas vuojadit dan bottu go viegai eret %2$s", "death.attack.lightningBolt": "Álddagas deaivvai %1$s", "death.attack.lightningBolt.player": "Álddagas deaivvai %1$s dan bottu go doaruhii %2$s", "death.attack.mace_smash": "%2$s njuvdii eatnamii %1$s", "death.attack.mace_smash.item": "%2$s geavahii %3$s njuvdit %1$s eatnamii", "death.attack.magic": "%1$s god<PERSON><PERSON> no<PERSON>in", "death.attack.magic.player": "%1$s god<PERSON>i noadungoansttaiguin dan bottu go geahččalii viehkat eret %2$s spealláris", "death.attack.message_too_long": "<PERSON><PERSON><PERSON>, diehtu lei menddo guhkki sáddejuvvot ollásit. Šállošumit! Dá lea oaniduvvon veršuvdna: %s", "death.attack.mob": "%2$s gottii %1$s spealli", "death.attack.mob.item": "%2$s geavahii %3$s goddit %1$s", "death.attack.onFire": "%1$s bulii jámas", "death.attack.onFire.item": "%1$s bulii gutnan go doaruhii %2$s ja doalai %3$s", "death.attack.onFire.player": "%1$s bulii jámas dan bottu go doarui %2$s", "death.attack.outOfWorld": "%1$s gahčai gomuvuhtii", "death.attack.outOfWorld.player": "%1$s ii šat fuollan eallit seamma máilmmis go %2$s", "death.attack.outsideBorder": "%1$s vád<PERSON><PERSON>i eret dán <PERSON>is", "death.attack.outsideBorder.player": "%1$s vád<PERSON><PERSON>i eret dán ilmmis go %2$s do<PERSON><PERSON>i su", "death.attack.player": "%2$s gottii %1$s spealli", "death.attack.player.item": "%2$s geavahii %3$s goddit %1$s", "death.attack.sonic_boom": "%1$s bávkkehii duhát moallun sonihkal<PERSON>š-gealdojuvvon riežádeamis", "death.attack.sonic_boom.item": "%1$s bávkkehii duhát moallun sonihkal<PERSON>š-gealdojuvvon riežádeamis dan bottu go báhtarii %2$s speallis geas lei %3$s gieđas", "death.attack.sonic_boom.player": "%1$s bávkkehii duhát moallun sonihkal<PERSON>š-gealdojuvvon riežádeamis dan bottu go báhtarii %2$s speallis", "death.attack.stalagmite": "Stalagmihtta čuggestii %1$s spealli", "death.attack.stalagmite.player": "Stalagmihtta čuggestii %1$s spealli dan bottu go %2$s doaruhii", "death.attack.starve": "%1$s nelggui jámas", "death.attack.starve.player": "%1$s nelggui jámas dan bottu go doarui %2$s", "death.attack.sting": "%1$s čuggui jámas", "death.attack.sting.item": "%1$s čuggui j<PERSON> %2$s geavaheamis %3$s", "death.attack.sting.player": "%1$s čuggui j<PERSON>mas %2$s", "death.attack.sweetBerryBush": "%1$s du<PERSON><PERSON><PERSON><PERSON> nj<PERSON>lgga murj<PERSON><PERSON>i", "death.attack.sweetBerryBush.player": "%1$s du<PERSON><PERSON><PERSON> njálgga muorj<PERSON><PERSON> čuoggumii dan botta go báhtarii %2$s speallis", "death.attack.thorns": "%1$s gottohallai go geahččalii soarbmit %2$s", "death.attack.thorns.item": "%1$s gottohallai %3$s go geahččalii doaruhit %2$s", "death.attack.thrown": "%2$s bálkestii %1$s", "death.attack.thrown.item": "%2$s geavahii %3$s bálkestit %1$s", "death.attack.trident": "%2$s čuggii %1$s", "death.attack.trident.item": "%2$s geavahii %3$s čugget %1$s", "death.attack.wither": "%1$s witheruvvui eret", "death.attack.wither.player": "%1$s witheruvvui eret dan bottu go doaruhii %2$s", "death.attack.witherSkull": "%2$s geavahii oaiveskálžžu báhčit %1$s spealli", "death.attack.witherSkull.item": "%2$s geavahii oaiveskálžžu báhčit %1$s geavaheamis %3$s", "death.fell.accident.generic": "%1$s gahčai allasis", "death.fell.accident.ladder": "%1$s gahčai ráidalasas", "death.fell.accident.other_climbable": "%1$s gáhč<PERSON>i goarg<PERSON><PERSON><PERSON>in", "death.fell.accident.scaffolding": "%1$s gáhčái luovis", "death.fell.accident.twisting_vines": "%1$s gáhč<PERSON>i gierrinan goargŋun<PERSON>ttus", "death.fell.accident.vines": "%1$s gáhč<PERSON>i goarg<PERSON>", "death.fell.accident.weeping_vines": "%1$s gáhčái ganjaldan goargŋun<PERSON>ttus", "death.fell.assist": "%2$s dubmii %1$s gahččat", "death.fell.assist.item": "%2$s dubmii %1$s gahččat geavaheamis %3$s", "death.fell.finish": "%1$s gahčai menddo guhkás ja %2$s spealli heaittihii su", "death.fell.finish.item": "%1$s gahčai menddo guhkás ja %2$s spealli heaittihii su go geavahii %3$s", "death.fell.killer": "%1$s dubmejuvvui gahččat", "deathScreen.quit.confirm": "Leat go sihkar ahte <PERSON>t guođ<PERSON>it?", "deathScreen.respawn": "Ealás", "deathScreen.score": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deathScreen.score.value": "Čuoggát: %s", "deathScreen.spectate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deathScreen.title": "Don j<PERSON>et!", "deathScreen.title.hardcore": "Vuoittáhallet!", "deathScreen.titleScreen": "Álgosiidui", "debug.advanced_tooltips.help": "F3 + H = Dárk<PERSON><PERSON><PERSON><PERSON> reaid<PERSON>ieđut", "debug.advanced_tooltips.off": "Dárkkileappot reaidodieđut: čihkkon", "debug.advanced_tooltips.on": "Dárkkileappot reaidodieđut: oidnosis", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON><PERSON>", "debug.chunk_boundaries.off": "Laiggusráját: č<PERSON><PERSON>", "debug.chunk_boundaries.on": "Laiggusráj<PERSON><PERSON>: oidnosis", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON><PERSON>", "debug.copy_location.help": "F3 + C = Máŋge iežat sajádaga /tp kommandoi, dahje doala guhkit áiggi F3 + C jus áiggut botket spealu", "debug.copy_location.message": "M<PERSON>ŋgen sajádaga č<PERSON>beavdái", "debug.crash.message": "F3 + C deaddojuvvoba. Jus it luo<PERSON> boa<PERSON>, de kras<PERSON> s<PERSON>.", "debug.crash.warning": "Krasje fargga %s...", "debug.creative_spectator.error": "Ii sáhte lonuhit speall<PERSON>; dus ii leat lohpi", "debug.creative_spectator.help": "F3 + N = Molsso ovddit speallanhámi ja geahččihámi gaska", "debug.dump_dynamic_textures": "Vurken dynamalaš tekstuvrraid %s sajádahkii", "debug.dump_dynamic_textures.help": "F3 + S = Vurke dynamal<PERSON> tekstuvrraid", "debug.gamemodes.error": "Ii s<PERSON>hte rahpat speallanh<PERSON>mi r<PERSON>ji, dus ii leat dohkkehus", "debug.gamemodes.help": "F3 + F4 = <PERSON><PERSON><PERSON>", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s boahtte", "debug.help.help": "F3 + Q = <PERSON><PERSON><PERSON><PERSON> d<PERSON> listu", "debug.help.message": "Boalločadnosat:", "debug.inspect.client.block": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> blohkadie<PERSON>u <PERSON>", "debug.inspect.client.entity": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>a-oasásaš entitiadieđu <PERSON>avdái", "debug.inspect.help": "F3 + I = Máŋge entitiadieđuid dahje blohkadieđuid č<PERSON>avdái", "debug.inspect.server.block": "Máŋgen server-<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "debug.inspect.server.entity": "Máŋgen server-<PERSON><PERSON>ásaš entitiadieđu <PERSON>avdái", "debug.pause.help": "F3 + Esc = <PERSON><PERSON><PERSON> s<PERSON>, muhto he<PERSON> eai iđe (jus lea vejo<PERSON>)", "debug.pause_focus.help": "F3 + P = <PERSON><PERSON>t spealu go dasa it geahča", "debug.pause_focus.off": "Bisset go it geah<PERSON>a speala: ii alde", "debug.pause_focus.on": "Bisset go it geahča speala: alde", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Álggat/heaittit guorah<PERSON>ama", "debug.profiling.start": "%s seku<PERSON><PERSON><PERSON> guo<PERSON><PERSON>an <PERSON>lggáhuv<PERSON>i. Bisset árábut F3 + L boaluin", "debug.profiling.stop": "Guorahallan gárvánii. Vurken boađuid %s vuorkábáikái", "debug.reload_chunks.help": "F3 + A = Ođasmah<PERSON> laigo<PERSON>id", "debug.reload_chunks.message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> buot la<PERSON><PERSON><PERSON>", "debug.reload_resourcepacks.help": "F3 + T = Ođasmahte resursapáhkaid", "debug.reload_resourcepacks.message": "<PERSON>đas<PERSON><PERSON><PERSON> resursa<PERSON>d", "debug.show_hitboxes.help": "F3 + B = <PERSON><PERSON>jet deaivanbovssa", "debug.show_hitboxes.off": "Hitboksat: Čihkosis", "debug.show_hitboxes.on": "Hitboksat: Oidnosis", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Geavaheaddji veršuvnna dieđuid", "demo.day.1": "Demo bistá vihtta speallabeaivvi, olu lihkku!", "demo.day.2": "<PERSON><PERSON><PERSON>", "demo.day.3": "﻿<PERSON><PERSON><PERSON><PERSON>", "demo.day.4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "demo.day.5": "<PERSON><PERSON>t lea du ﻿maŋimus beaivi!", "demo.day.6": "<PERSON><PERSON><PERSON> leat badjel vi<PERSON><PERSON><PERSON>, geavat %s vurket šearbmagová du huksemušaidguin.", "demo.day.warning": "<PERSON><PERSON> lea measta nohkan!", "demo.demoExpired": "Demo á<PERSON> nog<PERSON>i!", "demo.help.buy": "Oaste dál!", "demo.help.fullWrapped": "Dát demo bistá 5 spillabeiviid (birrasii diimmu ja 40 minuvta). Iskka ovdánemiid hintaid ovddas! Olu lihkku!", "demo.help.inventory": "Raba dávvirvuorkká %1$s boaluin", "demo.help.jump": "Njuikes %1$s boaluin", "demo.help.later": "Joaŧke spillet!", "demo.help.movement": "%1$s, %2$s, %3$s, %4$s boaluiguin ja sáhpániin lihkadat", "demo.help.movementMouse": "<PERSON><PERSON><PERSON>", "demo.help.movementShort": "Vázze %1$s, %2$s, %3$s, %4$s boaluiguin", "demo.help.title": "<PERSON><PERSON>", "demo.remainingTime": "<PERSON><PERSON> vel: %s", "demo.reminder": "Demo á<PERSON> le<PERSON>, oaste spealu jus áig<PERSON>t joat<PERSON>t, dah<PERSON>ggat ođ<PERSON><PERSON> m<PERSON>!", "difficulty.lock.question": "Áiggotgo sihkkarit lohkadit dán máilmmi váttisvuođa dási? Máilmmi váttisvuohta lea %1$s dás ovddosguvlui, iige dan sáhte šat rievdadit goassege.", "difficulty.lock.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disconnect.endOfStream": "<PERSON><PERSON><PERSON><PERSON><PERSON> nogai", "disconnect.exceeded_packet_rate": "Gevrehu<PERSON><PERSON> go páhkameari meattildii", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignoreer tans statusversoek", "disconnect.loginFailedInfo": "Beassansivva: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lea <PERSON>, iska s<PERSON>/la<PERSON><PERSON> he<PERSON>.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON> (Geahččal o<PERSON><PERSON><PERSON><PERSON> ja <PERSON>)", "disconnect.loginFailedInfo.serversUnavailable": "Duođaštanb<PERSON>lval<PERSON>t eai leat jur dál o<PERSON>. Geahččal áinn<PERSON> ođđasit.", "disconnect.loginFailedInfo.userBanned": "Don leat gildoju<PERSON><PERSON> online-speallamis", "disconnect.lost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disconnect.packetError": "Neahttaprotokollameatt<PERSON>", "disconnect.spam": "Gevrejuvvoi duššiid <PERSON>", "disconnect.timeout": "<PERSON><PERSON>", "disconnect.transfer": "<PERSON><PERSON><PERSON>", "disconnect.unknownHost": "<PERSON><PERSON>", "download.pack.failed": "%s/%s páhkaid viežžamus ii lihkostuvvan", "download.pack.progress.bytes": "Ovdáneapmi: %s (ii dieđe o<PERSON> stur<PERSON>)", "download.pack.progress.percent": "Ovdáneapmi: %s%%", "download.pack.title": "Viežžamin resursapáhka %s/%s", "editGamerule.default": "Dábálaš: %s", "editGamerule.title": "<PERSON><PERSON><PERSON> speallann<PERSON>id", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "<PERSON><PERSON>or<PERSON><PERSON>", "effect.minecraft.bad_omen": "<PERSON><PERSON> <PERSON><PERSON>", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Mearrameannudeaddji fápmu", "effect.minecraft.darkness": "Seav<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.fire_resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.glowing": "Áhcagastin", "effect.minecraft.haste": "Rogganleaktu", "effect.minecraft.health_boost": "<PERSON><PERSON>", "effect.minecraft.hero_of_the_village": "<PERSON>", "effect.minecraft.hunger": "<PERSON><PERSON>", "effect.minecraft.infested": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.instant_damage": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.instant_health": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.jump_boost": "Njuikenveahkki", "effect.minecraft.levitation": "<PERSON><PERSON><PERSON>", "effect.minecraft.luck": "Lihkku", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "Váibmomoiddodat", "effect.minecraft.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.oozing": "Njivllas", "effect.minecraft.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.raid_omen": "Rievideami diida", "effect.minecraft.regeneration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.saturation": "Nealgečoavji", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.speed": "Leaktu", "effect.minecraft.strength": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.trial_omen": "Geahččalusa diida", "effect.minecraft.unluck": "Li<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.water_breathing": "Čáhce Vuoiŋŋahat", "effect.minecraft.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "Biegg<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.wither": "<PERSON><PERSON><PERSON><PERSON>", "effect.none": "Ii leat váikkuhuvvon", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.bane_of_arthropods": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON>", "enchantment.minecraft.binding_curse": "Čat<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.blast_protection": "Bávkalahttinsuojus", "enchantment.minecraft.breach": "Cuvken", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.density": "Č<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "Čáhceleaktu", "enchantment.minecraft.efficiency": "Leah<PERSON>roggan", "enchantment.minecraft.feather_falling": "Dolgeseaivumuš", "enchantment.minecraft.fire_aspect": "Cahkkehallan", "enchantment.minecraft.fire_protection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.flame": "<PERSON><PERSON>", "enchantment.minecraft.fortune": "Rogganlihkku", "enchantment.minecraft.frost_walker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.impaling": "Čuggen", "enchantment.minecraft.infinity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.knockback": "Hoigadahttin", "enchantment.minecraft.looting": "Oažžun", "enchantment.minecraft.loyalty": "Oskk<PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "Ábilihkku", "enchantment.minecraft.lure": "Seakti", "enchantment.minecraft.mending": "Div<PERSON><PERSON>", "enchantment.minecraft.multishot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.piercing": "Sákkastahttin", "enchantment.minecraft.power": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.protection": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.punch": "Časkkástat", "enchantment.minecraft.quick_charge": "Gealdinleaktu", "enchantment.minecraft.respiration": "Čáhcevuoidnan", "enchantment.minecraft.riptide": "Miehterávdnjái", "enchantment.minecraft.sharpness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "Silkeguoskkahus", "enchantment.minecraft.smite": "Jámežiid <PERSON>", "enchantment.minecraft.soul_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sweeping": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sweeping_edge": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.wind_burst": "Bieggabosa<PERSON><PERSON>", "entity.minecraft.acacia_boat": "Acaciafanas", "entity.minecraft.acacia_chest_boat": "Acaciafanas mas lea giis<PERSON>", "entity.minecraft.allay": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.area_effect_cloud": "Báikkálaš váikku<PERSON>va", "entity.minecraft.armadillo": "Armadillo", "entity.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.arrow": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.axolotl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bamboo_chest_raft": "Bámbufanas mas lea giis<PERSON>", "entity.minecraft.bamboo_raft": "Bámbufanas", "entity.minecraft.bat": "Girdisá<PERSON><PERSON>", "entity.minecraft.bee": "Ulvu", "entity.minecraft.birch_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.birch_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> mas lea giis<PERSON>", "entity.minecraft.blaze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.block_display": "Blohkkačájáhus", "entity.minecraft.boat": "<PERSON><PERSON>", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Bieggadoarohas", "entity.minecraft.camel": "<PERSON><PERSON><PERSON>", "entity.minecraft.cat": "Bussá", "entity.minecraft.cave_spider": "<PERSON><PERSON>uheav<PERSON>i", "entity.minecraft.cherry_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cherry_chest_boat": "Ki<PERSON>mu<PERSON><PERSON>muorrafan<PERSON> mas lea giisá", "entity.minecraft.chest_boat": "Fanas mas lea giis<PERSON>", "entity.minecraft.chest_minecart": "Roggangielká mas giisá", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cod": "<PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Roggangielká mas kommándoblohkka", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creaking_transient": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.dark_oak_chest_boat": "Sevdnjeseaikafanas mas lea giisá", "entity.minecraft.dolphin": "Dolfiidn<PERSON>", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "<PERSON><PERSON><PERSON>", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON>", "entity.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Gonstošeaddji", "entity.minecraft.evoker_fangs": "Čiehkabánit", "entity.minecraft.experience_bottle": "Balkest<PERSON><PERSON><PERSON>", "entity.minecraft.experience_orb": "Noaidundássespábba", "entity.minecraft.eye_of_ender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.falling_block": "Gahčči blohkka", "entity.minecraft.falling_block_type": "%s mii gahčč<PERSON>", "entity.minecraft.fireball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.firework_rocket": "Ra<PERSON>ahtta", "entity.minecraft.fishing_bobber": "Goarkagoarvvet vuogga", "entity.minecraft.fox": "<PERSON><PERSON><PERSON>", "entity.minecraft.frog": "Rihccecuoppu", "entity.minecraft.furnace_minecart": "Roggangielká mas uvdna", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "Áhcagas dávvirrámma", "entity.minecraft.glow_squid": "Áhcagas <PERSON>", "entity.minecraft.goat": "Gáica", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Roggangielk<PERSON> mas njammi", "entity.minecraft.horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.husk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.illusioner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.interaction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.iron_golem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.item": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.item_display": "Dáv<PERSON><PERSON><PERSON>", "entity.minecraft.item_frame": "Dávvirrámma", "entity.minecraft.jungle_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.jungle_chest_boat": "Suhkesvuovdefanas mas lea giis<PERSON>", "entity.minecraft.killer_bunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.leash_knot": "Lávžečuolbma", "entity.minecraft.lightning_bolt": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.lingering_potion": "<PERSON>issovaš jugus", "entity.minecraft.llama": "Llama", "entity.minecraft.llama_spit": "Llama <PERSON>", "entity.minecraft.magma_cube": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.mangrove_boat": "Mangrovefanas", "entity.minecraft.mangrove_chest_boat": "Mangrovefanas mas lea giis<PERSON>", "entity.minecraft.marker": "Mearka", "entity.minecraft.minecart": "Roggangielká", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON><PERSON>", "entity.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.oak_chest_boat": "Eaika<PERSON><PERSON> mas lea giis<PERSON>", "entity.minecraft.ocelot": "Meahccebussá", "entity.minecraft.ominous_item_spawner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dá<PERSON>", "entity.minecraft.painting": "<PERSON><PERSON><PERSON>", "entity.minecraft.pale_oak_boat": "Šovkes <PERSON>", "entity.minecraft.pale_oak_chest_boat": "Šovkes eaika<PERSON>as mas lea fanas", "entity.minecraft.panda": "Pandaguovža", "entity.minecraft.parrot": "Papegoia", "entity.minecraft.phantom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.pig": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "Piglinbahálaš", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.player": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.potion": "<PERSON>aid<PERSON><PERSON><PERSON>", "entity.minecraft.pufferfish": "Mir<PERSON>gu<PERSON>i", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "Lu<PERSON>", "entity.minecraft.sheep": "Sávza", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "Silbasáhpán", "entity.minecraft.skeleton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.slime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.small_fireball": "<PERSON><PERSON> buo<PERSON>", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snowball": "Muohtačah<PERSON>r", "entity.minecraft.spawner_minecart": "Roggangielká mas st<PERSON><PERSON>", "entity.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spider": "He<PERSON><PERSON><PERSON>", "entity.minecraft.splash_potion": "Diškalanjugus", "entity.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spruce_chest_boat": "<PERSON><PERSON><PERSON><PERSON> mas lea giis<PERSON>", "entity.minecraft.squid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.stray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.strider": "G<PERSON><PERSON>šeaddji", "entity.minecraft.tadpole": "Šlubboaivi", "entity.minecraft.text_display": "Teakstačájáhus", "entity.minecraft.tnt": "Cahkkehallan TNT", "entity.minecraft.tnt_minecart": "Roggangielká mas TNT", "entity.minecraft.trader_llama": "Gill<PERSON>č<PERSON><PERSON> llama", "entity.minecraft.trident": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.0": "Ánemoná", "entity.minecraft.tropical_fish.predefined.1": "Čáhppes <PERSON>rj<PERSON>", "entity.minecraft.tropical_fish.predefined.10": "Čoarvegu<PERSON>i", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.14": "Sihklidavuskkon", "entity.minecraft.tropical_fish.predefined.15": "Ruksesbavssat Njuovoguolli", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.20": "Fiskesbeahcet Papegoiaguolli", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "Vusk<PERSON>", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "<PERSON><PERSON><PERSON><PERSON>i", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "Duškii", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "Gáddenjivlás<PERSON>olli", "entity.minecraft.tropical_fish.type.snooper": "Snuoggá", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Beaive<PERSON><PERSON><PERSON><PERSON><PERSON> guo<PERSON>", "entity.minecraft.turtle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.vex": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "<PERSON><PERSON>", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "Nju<PERSON>adahk<PERSON>", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "G<PERSON>ječea<PERSON><PERSON>", "entity.minecraft.villager.mason": "G<PERSON><PERSON>gel<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "Buoskabahta", "entity.minecraft.villager.none": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.vindicator": "<PERSON><PERSON><PERSON>", "entity.minecraft.wandering_trader": "Johttigávppašeaddji", "entity.minecraft.warden": "<PERSON><PERSON><PERSON>", "entity.minecraft.wind_charge": "Bieggadoarohas", "entity.minecraft.witch": "<PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON><PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombified_piglin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.not_summonable": "Ii sáhte ealáskit %s namma<PERSON>š <PERSON>", "event.minecraft.raid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "<PERSON><PERSON>videap<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event.minecraft.raid.raiders_remaining": "Rievvárat vel: %s", "event.minecraft.raid.victory": "Vuoitu", "event.minecraft.raid.victory.full": "Rievideapmi - Vuoitu", "filled_map.buried_treasure": "Árdnahávdekárta", "filled_map.explorer_jungle": "<PERSON><PERSON><PERSON><PERSON>", "filled_map.explorer_swamp": "<PERSON><PERSON><PERSON>", "filled_map.id": "ID #%s", "filled_map.level": "(Level %s/%s)", "filled_map.locked": "Lohkaduvvon", "filled_map.mansion": "<PERSON><PERSON><PERSON>", "filled_map.monument": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.scale": "Sturrodat lea 1:%s", "filled_map.trial_chambers": "Geahččaluslanjaid kárta", "filled_map.unknown": "<PERSON><PERSON>", "filled_map.village_desert": "Sáttomeara giláškárta", "filled_map.village_plains": "<PERSON><PERSON><PERSON>", "filled_map.village_savanna": "Áđgga giláškárta", "filled_map.village_snowy": "<PERSON><PERSON>ttás giláškárta", "filled_map.village_taiga": "Guovssovuovddi giláškárta", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.classic_flat": "Dábá<PERSON>š <PERSON>lba", "flat_world_preset.minecraft.desert": "Sáttomearra", "flat_world_preset.minecraft.overworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.redstone_ready": "Redstone gearggus", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.the_void": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "Rog<PERSON>lb<PERSON>", "flat_world_preset.minecraft.water_world": "Čáhcem<PERSON>bmi", "flat_world_preset.unknown": "???", "gameMode.adventure": "Fear<PERSON>", "gameMode.changed": "Du speallanvuohki lea rievdaduvvon %s speallanhábmái", "gameMode.creative": "Hutkás", "gameMode.hardcore": "Hardcore!", "gameMode.spectator": "Geahčči", "gameMode.survival": "<PERSON><PERSON><PERSON>", "gamerule.allowFireTicksAwayFromPlayer": "<PERSON><PERSON> buo<PERSON><PERSON> guhkkin eret speallis", "gamerule.allowFireTicksAwayFromPlayer.description": "<PERSON><PERSON><PERSON> galg<PERSON> go dolla dahje lava sáhttit cáhkke<PERSON> diŋggaid eanet go 8 laigosa eret speallis", "gamerule.announceAdvancements": "<PERSON><PERSON><PERSON>", "gamerule.blockExplosionDropDecay": "<PERSON><PERSON> blo<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, de eai luitojuvvo dávvirat buot blohkain", "gamerule.blockExplosionDropDecay.description": "<PERSON><PERSON> blo<PERSON> bá<PERSON><PERSON><PERSON><PERSON>, de jávket soames dávvirat.", "gamerule.category.chat": "Čátta", "gamerule.category.drops": "Lu<PERSON><PERSON><PERSON>", "gamerule.category.misc": "Eará", "gamerule.category.mobs": "Eallit", "gamerule.category.player": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Ealáskeapmi", "gamerule.category.updates": "Máilbmálaš rievdadeamit", "gamerule.commandBlockOutput": "<PERSON><PERSON>ut kom<PERSON>do blo<PERSON> č<PERSON>id", "gamerule.commandModificationBlockLimit": "Blohkameari kommán<PERSON>", "gamerule.commandModificationBlockLimit.description": "Galle blohkaid ov<PERSON>in kommándoin sáhttá rievdadit, ovdamear<PERSON> dihte de<PERSON>din- dahje máŋgenkommándoin.", "gamerule.disableElytraMovementCheck": "J<PERSON>ddat sojiid lihkasteami dárkkástusa", "gamerule.disablePlayerMovementCheck": "Jáddat spelliid lihkasteami dárkkisteami", "gamerule.disableRaids": "Jáddat rievideamiid", "gamerule.doDaylightCycle": "<PERSON><PERSON><PERSON> go<PERSON>t", "gamerule.doEntityDrops": "Luoitte entitiain d<PERSON>d", "gamerule.doEntityDrops.description": "<PERSON><PERSON><PERSON> jus dávvirat luo<PERSON><PERSON><PERSON><PERSON><PERSON> (maiddái inventárain), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, jna.", "gamerule.doFireTick": "<PERSON><PERSON>", "gamerule.doImmediateRespawn": "<PERSON><PERSON><PERSON>", "gamerule.doInsomnia": "Idjagopmit ihtet ihkku", "gamerule.doLimitedCrafting": "G<PERSON><PERSON><PERSON> duod<PERSON>neav<PERSON>giid", "gamerule.doLimitedCrafting.description": "<PERSON><PERSON> lea al<PERSON>, de eai sáhte speallit duddjot eará dávviriid go daid mat leat rahpasan duodjegirjji<PERSON>.", "gamerule.doMobLoot": "Návddiid ja ealliid luoitin", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON> jus resurssat ihtet go gott<PERSON><PERSON>, f<PERSON><PERSON><PERSON><PERSON> maiddái XP.", "gamerule.doMobSpawning": "Návddit ja eallit ealás<PERSON>t", "gamerule.doMobSpawning.description": "<PERSON><PERSON><PERSON> elliin ja návddiin leat iešguđegelágan ealáskannjuolggádusat.", "gamerule.doPatrolSpawning": "Rievvárat ihtet", "gamerule.doTileDrops": "Blohkat luitojuvvot", "gamerule.doTileDrops.description": "<PERSON><PERSON><PERSON> resurssaid ihttima go cuvket blohka, f<PERSON><PERSON><PERSON><PERSON> maiddái XP.", "gamerule.doTraderSpawning": "Johttigávppašeaddjit i<PERSON>", "gamerule.doVinesSpread": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.doVinesSpread.description": "Gieđahallá goargŋunšattuid summális šaddama ja leavvama eará blohkaide. Dát ii váikkut ganjaldan goarg<PERSON><PERSON>, bon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jna.", "gamerule.doWardenSpawning": "Gohccit ealáskit", "gamerule.doWeatherCycle": "<PERSON><PERSON><PERSON>erievdadeapmi", "gamerule.drowningDamage": "So<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> go he<PERSON><PERSON>t", "gamerule.enderPearlsVanishOnDeath": "B<PERSON>lkestuvvon enderhelbmot jávket jápmima maŋŋel", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON><PERSON><PERSON> jáv<PERSON>go bálkestuvvon endorhelbmot maŋŋel go spealli jápm<PERSON>.", "gamerule.entitiesWithPassengersCanUsePortals": "Entitiait geain leat mátkkálaččat sáhttet sirddášit portálain", "gamerule.entitiesWithPassengersCanUsePortals.description": "Divtte entitiaid main leat mátkkálaččat sirddášit nehterportálaid, Endorportálaid ja Endorverráhiid čađa.", "gamerule.fallDamage": "Soarddašuvva go gahčat", "gamerule.fireDamage": "Soarddašu<PERSON>va go buol<PERSON>t", "gamerule.forgiveDeadPlayers": "Atte ándagassii jápmán spealliide", "gamerule.forgiveDeadPlayers.description": "<PERSON><PERSON><PERSON> neutr<PERSON><PERSON> eallit heitet rohkkáhit go sivalaš speall<PERSON>r j<PERSON><PERSON>.", "gamerule.freezeDamage": "Soard<PERSON><PERSON><PERSON><PERSON><PERSON> go galbmot", "gamerule.globalSoundEvents": "Máilmmeviidosaš jietnadáhpáhusat", "gamerule.globalSoundEvents.description": "<PERSON> j<PERSON><PERSON><PERSON><PERSON> ear<PERSON> d<PERSON> geavv<PERSON>, ov<PERSON><PERSON><PERSON> ho<PERSON><PERSON>, de gullo issoras jietna mi<PERSON>t<PERSON> m<PERSON>.", "gamerule.keepInventory": "<PERSON><PERSON><PERSON> dáv<PERSON>kk<PERSON> maŋŋel go jápmá", "gamerule.lavaSourceConversion": "<PERSON><PERSON> gáldun", "gamerule.lavaSourceConversion.description": "Jus leat olles lavagáldoblohkat lava bálddas mii golg<PERSON>, de šaddet golgi blohkat maidd<PERSON>i olles lavagáldobloh<PERSON>n.", "gamerule.locatorBar": "Geavat spelliid gávdnansázu", "gamerule.locatorBar.description": "Go gea<PERSON><PERSON><PERSON><PERSON>, de iht<PERSON> s<PERSON>hcu š<PERSON>as mii č<PERSON>ha gos eará speallit leat.", "gamerule.logAdminCommands": "<PERSON><PERSON><PERSON> h<PERSON>dašeaddji kommándoid", "gamerule.maxCommandChainLength": "Guhkodatmearri kommán<PERSON>", "gamerule.maxCommandChainLength.description": "Guoská kommándoblohkaide mat leat ráiddus ja daid doaimmaide.", "gamerule.maxCommandForkCount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.maxCommandForkCount.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uođa lohku máid kommándot nugo \"execute as\" sáht<PERSON>t geavahit.", "gamerule.maxEntityCramming": "<PERSON><PERSON><PERSON>", "gamerule.minecartMaxSpeed": "Roggangielkká jođáneamos <PERSON>", "gamerule.minecartMaxSpeed.description": "Roggangielkkáid jođáneamos vuođđoleaktu eatnama alde.", "gamerule.mobExplosionDropDecay": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, de eai luitojuvvo dávvirat buot blohkain", "gamerule.mobExplosionDropDecay.description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, de jávket soames dávvirat.", "gamerule.mobGriefing": "<PERSON>vtte návddiid billistit blohkaid", "gamerule.naturalRegeneration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.playersNetherPortalCreativeDelay": "Spealli Netherportála <PERSON> go lea hutkái speallanvuogis", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON><PERSON> (coahkkimiiguin), mas spealli geas lea alde hutk<PERSON> spe<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>portálas ov<PERSON> sir<PERSON>uvvá nuppi ilbmái.", "gamerule.playersNetherPortalDefaultDelay": "Spealli Netherportála <PERSON> go ii leat hutkái speallanvuogis", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON><PERSON> (coahkkimiiguin), mas spealli geas ii leat alde hutk<PERSON> speall<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>herportálas ov<PERSON> sirdašuvvá nuppi ilbmái.", "gamerule.playersSleepingPercentage": "Nagirproseant<PERSON>", "gamerule.playersSleepingPercentage.description": "Proseantaoassi spelliin geat fertejit oa<PERSON><PERSON><PERSON>in nu ahte idja golla beaivái.", "gamerule.projectilesCanBreakBlocks": "Bážalmasat sáhttet billistit blohkaid", "gamerule.projectilesCanBreakBlocks.description": "<PERSON><PERSON><PERSON> cuvkejit go bážalmasat blohkaid mat dábálaččat cuvkejuvvet go báhččojuvvet.", "gamerule.randomTickSpeed": "<PERSON><PERSON><PERSON> co<PERSON>u", "gamerule.reducedDebugInfo": "<PERSON>nit sivva<PERSON><PERSON> dieđ<PERSON>", "gamerule.reducedDebugInfo.description": "<PERSON><PERSON><PERSON> die<PERSON>.", "gamerule.sendCommandFeedback": "Čájet kommándoid d<PERSON>", "gamerule.showDeathMessages": "Čájet jápmasága", "gamerule.snowAccumulationHeight": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "gamerule.snowAccumulationHeight.description": "<PERSON> b<PERSON>, de s<PERSON>htt<PERSON> jovgot gerddiid mielde eanemustá dán logu radj<PERSON>i.", "gamerule.spawnChunkRadius": "Ealáskan<PERSON><PERSON><PERSON><PERSON>", "gamerule.spawnChunkRadius.description": "Galle laigosat dá<PERSON><PERSON><PERSON><PERSON> m<PERSON>mmi <PERSON>áik<PERSON> bissot r<PERSON>.", "gamerule.spawnRadius": "Ealáskanbáik<PERSON> radius", "gamerule.spawnRadius.description": "Mearrida man stuora biras<PERSON>us ealáskanbáik<PERSON> birra spealli sáhttá ealáskit.", "gamerule.spectatorsGenerateChunks": "Diktá geahččiid genereret duovdagiid", "gamerule.tntExplodes": "Dohkket TNT cáhkkeheami ja bávkkeheami", "gamerule.tntExplosionDropDecay": "Jus TNT bávkke<PERSON> blo<PERSON>, de eai luitojuvvo dávvirat buot blohkain", "gamerule.tntExplosionDropDecay.description": "Jus TNT bávkkeha blo<PERSON>d, de jávket soames dávvirat.", "gamerule.universalAnger": "Universálas suhttu", "gamerule.universalAnger.description": "<PERSON><PERSON><PERSON> neutr<PERSON><PERSON> eallit rohkk<PERSON><PERSON> vaikko ma<PERSON>, ii duš<PERSON>e dan guhte suhttadii ealli. He<PERSON> buoremusat jus \"Atte ándagassii jápmán spealliide\" lea jáddaduvvon.", "gamerule.waterSourceConversion": "Čáhci šaddá gáldun", "gamerule.waterSourceConversion.description": "<PERSON>s leat olles čá<PERSON>cegáldoblohkat č<PERSON>zi bálddas mii golg<PERSON>, de šaddet golgi blohkat maiddái olles čáhcegáldoblohkkan.", "generator.custom": "Iežat", "generator.customized": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generator.minecraft.amplified": "GÁISÁT", "generator.minecraft.amplified.info": "Fuomáš: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON> buorre dihtor.", "generator.minecraft.debug_all_block_states": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generator.minecraft.flat": "Superduolbbas", "generator.minecraft.large_biomes": "<PERSON><PERSON><PERSON>", "generator.minecraft.normal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generator.minecraft.single_biome_surface": "Okta Bioma", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.attestation": "Go sádd<PERSON> dán <PERSON>, de duođ<PERSON><PERSON><PERSON> ahte dieđut maid leat addán leat riekta ja ollislaččat buoremus lági mielde.", "gui.abuseReport.comments": "Mearkkašumit", "gui.abuseReport.describe": "<PERSON><PERSON><PERSON><PERSON> veahkeha min dahkat buori mearr<PERSON><PERSON>a.", "gui.abuseReport.discard.content": "<PERSON><PERSON>, de <PERSON><PERSON><PERSON><PERSON><PERSON> váid<PERSON> ja buot lasihuvvon mearkkašumit.\nH<PERSON>liidat go duođaid guođđit?", "gui.abuseReport.discard.discard": "<PERSON><PERSON><PERSON> ja si<PERSON>ko v<PERSON>", "gui.abuseReport.discard.draft": "<PERSON><PERSON>", "gui.abuseReport.discard.return": "Joatkke rievdadeamis", "gui.abuseReport.discard.title": "<PERSON><PERSON><PERSON><PERSON>t go sihkkut váid<PERSON>š<PERSON> ja meark<PERSON>?", "gui.abuseReport.draft.content": "Háliidatgo joatkkit čállimis ovddeš váidinášši vai haliidatgo sihkkut dan ja čállit ođđa?", "gui.abuseReport.draft.discard": "Sihko", "gui.abuseReport.draft.edit": "Joatkke rievdadeamis", "gui.abuseReport.draft.quittotitle.content": "Háliidatgo joatkkit čállimis vai háliidatgo sihkkut ášš<PERSON>?", "gui.abuseReport.draft.quittotitle.title": "Dus lea sest<PERSON><PERSON><PERSON><PERSON>ttaváidin mii jávká jus loahpahat", "gui.abuseReport.draft.title": "Rievdadit sesttojuvvon čáttaváidima?", "gui.abuseReport.error.title": "Váidin<PERSON><PERSON><PERSON><PERSON> sádden ii lihkostuvvan", "gui.abuseReport.message": "Gos áicet fuones lá<PERSON>?\nDát diehtu veahkkehivččii min guorahallamis du ášš<PERSON>.", "gui.abuseReport.more_comments": "Čilges mat mii d<PERSON>hu<PERSON>i:", "gui.abuseReport.name.comment_box_label": "<PERSON><PERSON>ge manne h<PERSON>t vá<PERSON>it dán nama:", "gui.abuseReport.name.reporting": "Leat váidime \"%s\".", "gui.abuseReport.name.title": "<PERSON><PERSON><PERSON><PERSON> spealli nama", "gui.abuseReport.observed_what": "<PERSON><PERSON> v<PERSON> dán?", "gui.abuseReport.read_info": "Oahpa váidinbálval<PERSON> birra", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Gárrenmirkot dahje alkohola", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "<PERSON><PERSON><PERSON> h<PERSON><PERSON><PERSON><PERSON> váld<PERSON> g<PERSON><PERSON><PERSON><PERSON>, oassálastet lobiheames gárren<PERSON><PERSON> do<PERSON>ide dahje juhkat alkohola ovdal go lea lohp<PERSON>.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> illásteapmi dahje doaruheapmi mánáid vuostá", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "So<PERSON>s <PERSON> dahje ovddida illásteami dahje doaruheami mán<PERSON>id vuostá.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON>h<PERSON>", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "<PERSON><PERSON>s bi<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON><PERSON> vearredie<PERSON> mas lea ulbmil dáju<PERSON>.", "gui.abuseReport.reason.description": "Govvádus:", "gui.abuseReport.reason.false_reporting": "<PERSON><PERSON><PERSON><PERSON><PERSON> di<PERSON>e v<PERSON>", "gui.abuseReport.reason.generic": "Háliidan sin váidit", "gui.abuseReport.reason.generic.description": "<PERSON>n in liiko sid<PERSON> / sii dahke juoidá masa in liikon.", "gui.abuseReport.reason.harassment_or_bullying": "Loavkideapmi dahje givssideapmi", "gui.abuseReport.reason.harassment_or_bullying.description": "Soames lea bilk<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dah<PERSON> g<PERSON><PERSON><PERSON><PERSON> du dahje eará olbmo. Jus soames viggá duinna dahje earáin ságastallat vaikko dus ii leat miella, dahje soames viggá bidjat govaid ja dieđuid du birra nehttii/online-fierbmái vaikko it leat addán lobi, de lea dat maiddái loavkideapmi ja givssideapmi (\"doxing\").", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.hate_speech.description": "Soames cielaha du dahje eará spealli identiteha, releg<PERSON><PERSON><PERSON>, liikeivnni dahje seksualitehta dihte.", "gui.abuseReport.reason.imminent_harm": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> ahte <PERSON>u báv<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.imminent_harm.description": "<PERSON><PERSON><PERSON> baldala soarbmit du dahje ear<PERSON>id albma m<PERSON>mm<PERSON>.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Govahallan vuolledábiin masa it leat miehttán", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ládje ovddida priváhta ja seksuálal<PERSON>š govaid.", "gui.abuseReport.reason.self_harm_or_suicide": "<PERSON><PERSON><PERSON> - Iešsorbmen dahje ieš<PERSON>ddin", "gui.abuseReport.reason.self_harm_or_suicide.description": "Soames muitala ahte áigu bávččagahttit iežas albma máilmm<PERSON>, dah<PERSON> á<PERSON> ahte hálidivččii bávččagahttit iežas.", "gui.abuseReport.reason.sexually_inappropriate": "Čuohcá unohasat seksuálalaččat", "gui.abuseReport.reason.sexually_inappropriate.description": "Fárddat mat roavvásit govvidit seksu<PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON><PERSON><PERSON> ja illásteami.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorisma dahje várálaš ekstremisma", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ain dahje ekstre<PERSON><PERSON> vearred<PERSON> ol<PERSON><PERSON> poli<PERSON>, re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ideolog<PERSON><PERSON> miela dahje <PERSON> miela<PERSON>.", "gui.abuseReport.reason.title": "Vállje váidinšlája", "gui.abuseReport.report_sent_msg": "Mii leat váldán vuhtii váidin<PERSON><PERSON>š<PERSON>. <PERSON>lu giitu!\n\nMin joavku guorahallá váidin<PERSON><PERSON><PERSON>i farggamusat.", "gui.abuseReport.select_reason": "<PERSON><PERSON><PERSON><PERSON> guđe <PERSON> vá<PERSON><PERSON> gull<PERSON>", "gui.abuseReport.send": "Sádde váidaga", "gui.abuseReport.send.comment_too_long": "<PERSON><PERSON><PERSON><PERSON> mat dieđu", "gui.abuseReport.send.error_message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> iđii boasttuvuohta: \n\"%s\"", "gui.abuseReport.send.generic_error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON> iđii boasttuvuohta man eat meroštan.", "gui.abuseReport.send.http_error": "V<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>in iđii HTTP-boasttuvuohta.", "gui.abuseReport.send.json_error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON> g<PERSON> boast<PERSON><PERSON><PERSON><PERSON><PERSON>.", "gui.abuseReport.send.no_reason": "<PERSON><PERSON><PERSON><PERSON><PERSON> mat guđe <PERSON> vá<PERSON>in gullá", "gui.abuseReport.send.not_attested": "Loga teavstta dás bajábealde ja deaddil dárkkistanbovssa vai raporta sáddejuvvo", "gui.abuseReport.send.service_unavailable": "Ii sáhte o<PERSON><PERSON> veagalváldimiid váidinbálvalusa. Iska mat iežat interneahttaoktavuođa ja geahččal fas ođđasit.", "gui.abuseReport.sending.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>...", "gui.abuseReport.sent.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.skin.title": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>all<PERSON> fá<PERSON>", "gui.abuseReport.title": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.type.chat": "Čátta <PERSON>", "gui.abuseReport.type.name": "<PERSON><PERSON><PERSON><PERSON> namma", "gui.abuseReport.type.skin": "Spealli fárda", "gui.acknowledge": "<PERSON><PERSON> / <PERSON><PERSON><PERSON>", "gui.advancements": "Ovdáneamit", "gui.all": "<PERSON><PERSON><PERSON>", "gui.back": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\n<PERSON><PERSON><PERSON> e<PERSON><PERSON><PERSON> liŋkka bokte: %s", "gui.banned.description.permanent": "Du geavaheaddji lea bistevaččat gildojuvvon ja danne it beasa speallat online itge searvat Realmsii.", "gui.banned.description.reason": "Mii leat aitto du geavaheaddjis ožžon váidinášši eará spealli fuones láhttestallama birra. Min bearráigeahččit leat dál iskan du ášši ja dulkon ahte dat lei %s, mii ii čuovo Minecraft Community Standard njuolggadusaid.", "gui.banned.description.reason_id": "Dovddanummir: %s", "gui.banned.description.reason_id_message": "Dovddanummir: %s - %s", "gui.banned.description.temporary": "%s Dassážii it sáhte speallat online dahjege searvat Realmsii.", "gui.banned.description.temporary.duration": "Du geavaheaddji lea gaskaboddosaččat lohkkaduvvon %s.", "gui.banned.description.unknownreason": "Mii leat aitto ož<PERSON> váidinášš<PERSON> eará spealli fuones láhttestallama birra. Min bearráigeahččit leat dál iskan du ášši ja dulkon ahte dat ii čuovo Minecraft Community Standard njuolggadusaid.", "gui.banned.name.description": "<PERSON> dála namma - \"%s\" - rihkku min Servodat Standara. <PERSON> sáhtat ain speallat iežainat, muhto fertet rievdadit jus háliidat speallat online.\n\nOahpa eanet dahje sáddes <PERSON>ššemanahaga čuvvovaš liŋkas: %s", "gui.banned.name.title": "Nam<PERSON> ii dohkkehuvvo máŋggaidspealus", "gui.banned.reason.defamation_impersonation_false_information": "Dahkaladdá eará olm<PERSON>in dahje juohká dieđuid mat fillejit ja behttet earáid", "gui.banned.reason.drugs": "<PERSON><PERSON><PERSON><PERSON> g<PERSON> g<PERSON>", "gui.banned.reason.extreme_violence_or_gore": "Čájehallá duohta máilmmi doarrumiid dahje goddi<PERSON>", "gui.banned.reason.false_reporting": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> g<PERSON>- <PERSON><PERSON><PERSON> boasttuváidagiid", "gui.banned.reason.fraud": "<PERSON><PERSON><PERSON>š g<PERSON> earáid opmodagas", "gui.banned.reason.generic_violation": "Rihkku servvodaga noarpmaid", "gui.banned.reason.harassment_or_bullying": "Fasttes ságat mas lea čielga bahávuoŋŋ<PERSON><PERSON> ul<PERSON>l", "gui.banned.reason.hate_speech": "Vaššiságat dahje diskrimineren", "gui.banned.reason.hate_terrorism_notorious_figure": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "gui.banned.reason.imminent_harm_to_person_or_property": "Oaivvilda bávččagahttet olbmuid dahje dávviiriid duohta máilmmis", "gui.banned.reason.nudity_or_pornography": "Čájehallá seksuála dahje pornog<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.banned.reason.sexually_inappropriate": "Snoallan ja eará unohas seksu<PERSON> s<PERSON>gat", "gui.banned.reason.spam_or_advertising": "<PERSON><PERSON><PERSON><PERSON><PERSON> dahje m<PERSON>", "gui.banned.skin.description": "Du dála fárda rihkku min Servodat Standara. <PERSON> s<PERSON><PERSON>t ain speallut d<PERSON><PERSON><PERSON><PERSON> fárddain, dah<PERSON> válje ođ<PERSON>a.\n\nOahpa eambbo dahje sáddes <PERSON>šemanahaga čuvvovaš liŋkas: %s", "gui.banned.skin.title": "Fárda ii dohkkehuvvo", "gui.banned.title.permanent": "Geavaheaddji lea bistevaččat gildojuvvon", "gui.banned.title.temporary": "Geavaheaddji lea gaskaboddosaččat gildojuvvon", "gui.cancel": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.comments": "Mearkkašeamit", "gui.chatReport.describe": "<PERSON><PERSON><PERSON> fal <PERSON>, vai mii s<PERSON>t buori dieđ<PERSON>guin mearridit mearrá<PERSON>.", "gui.chatReport.discard.content": "<PERSON><PERSON>, de j<PERSON><PERSON><PERSON><PERSON> váidin ja buot lasihuvvon dieđut.\nH<PERSON>liidat go duođaid guođđit?", "gui.chatReport.discard.discard": "<PERSON><PERSON><PERSON> ja b<PERSON><PERSON> v<PERSON>", "gui.chatReport.discard.draft": "<PERSON><PERSON>", "gui.chatReport.discard.return": "Joatkke rievdadeamis", "gui.chatReport.discard.title": "<PERSON><PERSON><PERSON><PERSON>t go sihkkut váidin<PERSON>šš<PERSON> ja dieđuid?", "gui.chatReport.draft.content": "Háliidatgo joatkkit čállimis ovddeš váidinášši vai haliidatgo bálkestit dan ja čállit ođđa?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "Joatkke rievdadeamis", "gui.chatReport.draft.quittotitle.content": "Háliidatgo joatkkit čállimis vai háliidatgo bálkestit ášši?", "gui.chatReport.draft.quittotitle.title": "Dus lea sesttojuv<PERSON> váidinášši mii jávká jus loahpahat", "gui.chatReport.draft.title": "Rievdadit sesttojuvvon váidinášši?", "gui.chatReport.more_comments": "Čilges mat mii d<PERSON>hu<PERSON>i:", "gui.chatReport.observed_what": "<PERSON>e h<PERSON>t vá<PERSON>it dán?", "gui.chatReport.read_info": "Oahpa váidinbálval<PERSON> birra", "gui.chatReport.report_sent_msg": "Mii leat váldán vuhtii váidin<PERSON><PERSON>š<PERSON>. <PERSON>lu giitu!\n\nMin joavku guorahallá váidin<PERSON><PERSON><PERSON>i farggamusat.", "gui.chatReport.select_chat": "<PERSON><PERSON><PERSON><PERSON> jus áiggut váidit", "gui.chatReport.select_reason": "Vállje váidinšlája", "gui.chatReport.selected_chat": "%s č<PERSON>lus dahje čállosat leat válljejuvvon váidináššái", "gui.chatReport.send": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.send.no_reason": "<PERSON><PERSON><PERSON><PERSON><PERSON> mat vá<PERSON>", "gui.chatReport.send.no_reported_messages": "<PERSON><PERSON><PERSON><PERSON>s mat unnimusat ovtta čállosa man hálidivččet váidit", "gui.chatReport.send.too_many_messages": "<PERSON><PERSON> las<PERSON> menddo o<PERSON> v<PERSON><PERSON><PERSON>", "gui.chatReport.title": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatSelection.context": "Čállosat mat leat dán saji siskkobealde lasihuvvojit <PERSON> vai mii buorebut ipmirdit <PERSON>llosiid oktavuođa", "gui.chatSelection.fold": "%s dieđut leat č<PERSON>kon", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s searvvai s<PERSON>ii", "gui.chatSelection.message.narrate": "%s muitalii: %s %s", "gui.chatSelection.selected": "%s/%s válljejuvvon čállosa", "gui.chatSelection.title": "<PERSON><PERSON><PERSON><PERSON> man <PERSON> vá<PERSON>", "gui.continue": "Joatkke", "gui.copy_link_to_clipboard": "<PERSON><PERSON><PERSON><PERSON> liŋ<PERSON>", "gui.days": "%s beaivái", "gui.done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.down": "<PERSON><PERSON>", "gui.entity_tooltip.type": "Málle: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Ii dohkkehan %s fiillaid", "gui.fileDropFailure.title": "Ii me<PERSON>est<PERSON><PERSON><PERSON> lasihit fi<PERSON>id", "gui.hours": "%s diibmui", "gui.loadingMinecraft": "<PERSON><PERSON><PERSON><PERSON>", "gui.minutes": "%s minuhttii", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s boallu", "gui.narrate.editBox": "%s rievdat: %s", "gui.narrate.slider": "%s geassi", "gui.narrate.tab": "%s tab-boallu", "gui.no": "Ii", "gui.none": "Ii mihkkege", "gui.ok": "<PERSON><PERSON>", "gui.open_report_dir": "<PERSON><PERSON><PERSON>", "gui.proceed": "Joatkke", "gui.recipebook.moreRecipes": "<PERSON><PERSON><PERSON> o<PERSON>áhpánboalu eanet oaidnit", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Oza...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON><PERSON><PERSON><PERSON> visot", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON><PERSON><PERSON><PERSON> sudd<PERSON> dávviriid", "gui.recipebook.toggleRecipes.craftable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON><PERSON><PERSON>ii", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.report_to_server": "<PERSON><PERSON><PERSON> serverii", "gui.socialInteractions.blocking_hint": "Heivet Microsoft geavaheaddjin", "gui.socialInteractions.empty_blocked": "Eai leat gildojuvvon speallit čáttas", "gui.socialInteractions.empty_hidden": "Eai leat čihkkon speallit cháttas", "gui.socialInteractions.hidden_in_chat": "<PERSON><PERSON><PERSON> %s:s <PERSON><PERSON><PERSON>juv<PERSON><PERSON>", "gui.socialInteractions.hide": "Čiega Cháttas", "gui.socialInteractions.narration.hide": "Čiega %s čállosiid", "gui.socialInteractions.narration.report": "Váidde spealli %s", "gui.socialInteractions.narration.show": "Čájet %s čállosiid", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "Ii gávdnon spealli guđ<PERSON>as lea diet namma", "gui.socialInteractions.search_hint": "Oza...", "gui.socialInteractions.server_label.multiple": "%s - %s speallit", "gui.socialInteractions.server_label.single": "%s - %s spealli", "gui.socialInteractions.show": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.shown_in_chat": "<PERSON><PERSON><PERSON> %s:s <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_blocked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_blocked_offline": "Gildojuvvon - Offline", "gui.socialInteractions.status_hidden": "Čih<PERSON>", "gui.socialInteractions.status_hidden_offline": "Čihkon - Offline", "gui.socialInteractions.status_offline": "Offline", "gui.socialInteractions.tab_all": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_blocked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_hidden": "Čih<PERSON>", "gui.socialInteractions.title": "Sosiála <PERSON>", "gui.socialInteractions.tooltip.hide": "Čiega č<PERSON>llosiid", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ii leat olámuttos", "gui.socialInteractions.tooltip.report.no_messages": "%s speallis eai leat čállosat maid sáhttá váidit", "gui.socialInteractions.tooltip.report.not_reportable": "Dán spealli it sáhte váidit, dannego su čállosat eai sáhte duođaštuvvot serverbealde", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON><PERSON>", "gui.stats": "Statistihkat", "gui.toMenu": "Ruovttoluotta server listui", "gui.toRealms": "Ruovttoluotta Realms listui", "gui.toTitle": "Ruov<PERSON><PERSON>ott<PERSON> v<PERSON>ldo<PERSON>ui", "gui.toWorld": "<PERSON><PERSON><PERSON> m<PERSON>ui", "gui.togglable_slot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "gui.up": "Bajás", "gui.waitingForResponse.button.inactive": "Back (%ss)", "gui.waitingForResponse.title": "Waiting for Server", "gui.yes": "<PERSON><PERSON>", "hanging_sign.edit": "<PERSON><PERSON><PERSON><PERSON><PERSON> heŋ<PERSON><PERSON><PERSON><PERSON> gal<PERSON> dieđu", "instrument.minecraft.admire_goat_horn": "Áv<PERSON>", "instrument.minecraft.call_goat_horn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Ohcan", "instrument.minecraft.sing_goat_horn": "Lávlun", "instrument.minecraft.yearn_goat_horn": "Ohcaleapmi", "inventory.binSlot": "<PERSON><PERSON>", "inventory.hotbarInfo": "<PERSON><PERSON><PERSON> reaidolinnjá %1$s+%2$s boaluiguin", "inventory.hotbarSaved": "Dávviriid reaidolinnjá lea vurkejuvvon (viečča fas %1$s+%2$s boaluiguin)", "item.canBreak": "Sáhttá cuvket:", "item.canPlace": "Sáhttá dán ala bidjot:", "item.canUse.unknown": "<PERSON>as", "item.color": "Ivdni: %s", "item.components": "%s liigedieđu", "item.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dávvir", "item.durability": "Bistevašvuohta: %s / %s", "item.dyed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.acacia_boat": "Acaciafanas", "item.minecraft.acacia_chest_boat": "Acaciafanas mas giis<PERSON>", "item.minecraft.allay_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.amethyst_shard": "Ametistamoallu", "item.minecraft.angler_pottery_shard": "<PERSON><PERSON><PERSON> k<PERSON>u", "item.minecraft.angler_pottery_sherd": "<PERSON><PERSON><PERSON> k<PERSON>u", "item.minecraft.apple": "<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Dávgebáhčči kruhkkamoallu", "item.minecraft.archer_pottery_sherd": "Dávgebáhčči kruhkkamoallu", "item.minecraft.armadillo_scute": "Armadillo skálžu", "item.minecraft.armadillo_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.arms_up_pottery_shard": "Geigejuvvon g<PERSON><PERSON><PERSON>", "item.minecraft.arms_up_pottery_sherd": "Geigejuvvon g<PERSON><PERSON><PERSON>", "item.minecraft.arrow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.axolotl_bucket": "Aksolohtalskállu", "item.minecraft.axolotl_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.baked_potato": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> buđ<PERSON>", "item.minecraft.bamboo_chest_raft": "Bámbufanas mas gii<PERSON>", "item.minecraft.bamboo_raft": "Bámbufanas", "item.minecraft.bat_spawn_egg": "Girdisá<PERSON><PERSON><PERSON>", "item.minecraft.bee_spawn_egg": "Uvll<PERSON>", "item.minecraft.beef": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> mas gii<PERSON>", "item.minecraft.black_bundle": "Čáhppes dávvirbursa", "item.minecraft.black_dye": "Čáhppes Ivdni", "item.minecraft.black_harness": "Čáhppes leaŋggat", "item.minecraft.blade_pottery_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blade_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bulvvar", "item.minecraft.blaze_rod": "<PERSON><PERSON>llist<PERSON><PERSON>", "item.minecraft.blaze_spawn_egg": "Buollist<PERSON><PERSON>", "item.minecraft.blue_bundle": "<PERSON><PERSON>", "item.minecraft.blue_dye": "Alit Ivdni", "item.minecraft.blue_egg": "<PERSON><PERSON>", "item.minecraft.blue_harness": "<PERSON><PERSON> le<PERSON>", "item.minecraft.bogged_spawn_egg": "Jeakkeža ealáskanmonni", "item.minecraft.bolt_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bolt_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bone": "<PERSON><PERSON><PERSON>", "item.minecraft.bone_meal": "D<PERSON><PERSON>j<PERSON><PERSON><PERSON>", "item.minecraft.book": "<PERSON><PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bowl": "Boll<PERSON>", "item.minecraft.bread": "Láibi", "item.minecraft.breeze_rod": "Ru<PERSON>ž<PERSON> s<PERSON>i", "item.minecraft.breeze_spawn_egg": "Ruvaža ealáskanmonni", "item.minecraft.brewer_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brewer_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brick": "Muv<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_bundle": "Ruškes dávvirbur<PERSON>", "item.minecraft.brown_dye": "Ruškes Ivdni", "item.minecraft.brown_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON><PERSON> leaŋggat", "item.minecraft.brush": "<PERSON><PERSON>", "item.minecraft.bucket": "Gievdni", "item.minecraft.bundle": "Dávvirbursa", "item.minecraft.bundle.empty": "<PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Sáhttá vurkkodit ovtta lána iešguđet dávviriid", "item.minecraft.bundle.full": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.burn_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cat_spawn_egg": "Bussá Ealáskanmonni", "item.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_boots": "Viđjegápmagat", "item.minecraft.chainmail_chestplate": "Viđjeraddepánsár", "item.minecraft.chainmail_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_leggings": "Viđjebuvssat", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_chest_boat": "Ki<PERSON>muor<PERSON>muorrafan<PERSON> mas giisá", "item.minecraft.chest_minecart": "Roggangielká mas giisá", "item.minecraft.chicken": "Njuoska Vuoncábiergu", "item.minecraft.chicken_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chorus_fruit": "Chorusšatt<PERSON>", "item.minecraft.clay_ball": "Láiraspábba", "item.minecraft.clock": "Diib<PERSON>", "item.minecraft.coal": "<PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod": "Njuoska Dorski", "item.minecraft.cod_bucket": "Dorskeskállu", "item.minecraft.cod_spawn_egg": "Dorsk<PERSON> Ealáskanmonni", "item.minecraft.command_block_minecart": "Roggangielká mas kommándoblohkka", "item.minecraft.compass": "Kompássa", "item.minecraft.cooked_beef": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_chicken": "<PERSON><PERSON>", "item.minecraft.cooked_cod": "<PERSON><PERSON>", "item.minecraft.cooked_mutton": "<PERSON><PERSON>", "item.minecraft.cooked_porkchop": "<PERSON><PERSON>", "item.minecraft.cooked_rabbit": "<PERSON><PERSON>", "item.minecraft.cooked_salmon": "Basson Luossa", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "Veaiki", "item.minecraft.cow_spawn_egg": "Gusa Ealáskanmonni", "item.minecraft.creaking_spawn_egg": "Gihčama ealáskanmonni", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON><PERSON> min<PERSON>", "item.minecraft.creeper_banner_pattern.desc": "Creeperminsttar", "item.minecraft.creeper_banner_pattern.new": "<PERSON><PERSON><PERSON>", "item.minecraft.creeper_spawn_egg": "Creepera <PERSON>ás<PERSON>monni", "item.minecraft.crossbow": "Dávgebissu", "item.minecraft.crossbow.projectile": "Luođ<PERSON>a:", "item.minecraft.crossbow.projectile.multiple": "Luođđa: %s x %s", "item.minecraft.crossbow.projectile.single": "Luođđa: %s", "item.minecraft.cyan_bundle": "Turkiisa dávvirbursa", "item.minecraft.cyan_dye": "Turkiisa Idvni", "item.minecraft.cyan_harness": "<PERSON>uvgesalit leaŋggat", "item.minecraft.danger_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.danger_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dark_oak_chest_boat": "Sevdnjeseaikafanas mas giisá", "item.minecraft.debug_stick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.debug_stick.empty": "%s eai leat mearrádusat", "item.minecraft.debug_stick.select": "válljejuvvon \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" šadda %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_boots": "Diamántagápm<PERSON>t", "item.minecraft.diamond_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "Diamán<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_leggings": "Diamántabuvssat", "item.minecraft.diamond_pickaxe": "Diamán<PERSON><PERSON><PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "<PERSON>am<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.disc_fragment_5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.disc_fragment_5.desc": "Musihkkaskearru - 5", "item.minecraft.dolphin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON>", "item.minecraft.dragon_breath": "Drágavuoiŋŋastat", "item.minecraft.dried_kelp": "Goikaduvvon <PERSON>", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dune_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.echo_shard": "Sk<PERSON>d<PERSON><PERSON>all<PERSON>", "item.minecraft.egg": "<PERSON><PERSON>", "item.minecraft.elder_guardian_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.elytra": "<PERSON><PERSON><PERSON>", "item.minecraft.emerald": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "Noidojuv<PERSON> girji", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_dragon_spawn_egg": "Endordrága <PERSON>ni", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Endorolbmo <PERSON>ás<PERSON>", "item.minecraft.endermite_spawn_egg": "Endermite Ealáskanmonni", "item.minecraft.evoker_spawn_egg": "Gonstošeaddji Ealáskanmonni", "item.minecraft.experience_bottle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.explorer_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.explorer_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.eye_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.feather": "<PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.field_masoned_banner_pattern": "Muvrra le<PERSON>gami<PERSON>tar", "item.minecraft.filled_map": "Kárta", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_rocket": "Ra<PERSON>ahtta", "item.minecraft.firework_rocket.flight": "<PERSON><PERSON><PERSON><PERSON>igi:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Rahkeahttanásti", "item.minecraft.firework_star.black": "Čáhppat", "item.minecraft.firework_star.blue": "<PERSON><PERSON>", "item.minecraft.firework_star.brown": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.cyan": "Turkiisa", "item.minecraft.firework_star.fade_to": "Guovssut", "item.minecraft.firework_star.flicker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.gray": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.green": "Ruoná", "item.minecraft.firework_star.light_blue": "Čuvgesalit", "item.minecraft.firework_star.light_gray": "Čuovgesr<PERSON><PERSON>", "item.minecraft.firework_star.lime": "Čuvesruoná", "item.minecraft.firework_star.magenta": "<PERSON><PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.orange": "<PERSON><PERSON>", "item.minecraft.firework_star.pink": "Čuvgesrukses", "item.minecraft.firework_star.purple": "Sáhppat", "item.minecraft.firework_star.red": "Ruoksat", "item.minecraft.firework_star.shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.burst": "Reatká miehtá", "item.minecraft.firework_star.shape.creeper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON><PERSON> luo<PERSON>a", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON>", "item.minecraft.firework_star.shape.star": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "Vielgat", "item.minecraft.firework_star.yellow": "Fiskat", "item.minecraft.fishing_rod": "Oaggunstávrá", "item.minecraft.flint": "<PERSON><PERSON>", "item.minecraft.flint_and_steel": "Cahkkehanruovdi", "item.minecraft.flow_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.desc": "Go<PERSON>gan", "item.minecraft.flow_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>u", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "<PERSON><PERSON><PERSON>", "item.minecraft.flower_pot": "Lieđđekruhkku", "item.minecraft.fox_spawn_egg": "Riebana Ealáskanmonni", "item.minecraft.friend_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.friend_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.frog_spawn_egg": "Rihccecubbo ealáskanmonni", "item.minecraft.furnace_minecart": "Roggangielká mas uvdna", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.ghast_tear": "<PERSON><PERSON><PERSON> ga<PERSON><PERSON>at", "item.minecraft.glass_bottle": "L<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "Šelges Meluvdnabihttáš", "item.minecraft.globe_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON><PERSON> muorj<PERSON>", "item.minecraft.glow_ink_sac": "Áhcagas ivdnečoalli", "item.minecraft.glow_item_frame": "Áhcagas dávvirrámma", "item.minecraft.glow_squid_spawn_egg": "Áhcagas áhkárguoli ealáskanmonni", "item.minecraft.glowstone_dust": "Čuovgageađggedopmu", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Gáicca Ealáskanmonni", "item.minecraft.gold_ingot": "<PERSON><PERSON>", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_boots": "Gollegápmagat", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "Golleraddepán<PERSON>á<PERSON>", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_horse_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_leggings": "Gollebuvssat", "item.minecraft.golden_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_shovel": "Gollegoaivu", "item.minecraft.golden_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gray_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.green_bundle": "Ruoná dávvirbur<PERSON>", "item.minecraft.green_dye": "Ruoná Ivdni", "item.minecraft.green_harness": "Ruo<PERSON> leaŋggat", "item.minecraft.guardian_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gunpowder": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "<PERSON><PERSON>", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.happy_ghast_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.harness": "Leaŋggat", "item.minecraft.heart_of_the_sea": "Mearaváibmu", "item.minecraft.heart_pottery_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.heart_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.heartbreak_pottery_shard": "V<PERSON>ib<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.heartbreak_pottery_sherd": "V<PERSON>ib<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.honey_bottle": "Honnetbohtal", "item.minecraft.honeycomb": "Honnetbihttá", "item.minecraft.hopper_minecart": "Roggangielk<PERSON> mas njammi", "item.minecraft.horse_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.host_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.host_armor_trim_smithing_template.new": "B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.howl_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.husk_spawn_egg": "Goikejámeža Ealáskanmonni", "item.minecraft.ink_sac": "Čáhppesivdneč<PERSON>lli", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_boots": "Ruovdegápmagat", "item.minecraft.iron_chestplate": "Ruovderaddepánsár", "item.minecraft.iron_golem_spawn_egg": "Ruovdejiehtanasa <PERSON>ás<PERSON>monni", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "<PERSON><PERSON><PERSON> ruo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_ingot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_leggings": "Ruovdebuvssat", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "Ruovdebiikaákšu", "item.minecraft.iron_shovel": "Ruovdegoaivu", "item.minecraft.iron_sword": "Ruovdemiehkki", "item.minecraft.item_frame": "Dávvirrámma", "item.minecraft.jungle_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.jungle_chest_boat": "Suhkesvuovdefanas mas giis<PERSON>", "item.minecraft.knowledge_book": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lapis_lazuli": "<PERSON><PERSON>", "item.minecraft.lava_bucket": "Lavagievdni", "item.minecraft.lead": "Lávži", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "Sistegápmagat", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_helmet": "Sistegahpir", "item.minecraft.leather_horse_armor": "He<PERSON><PERSON>", "item.minecraft.leather_leggings": "Sistebuvssat", "item.minecraft.light_blue_bundle": "Čuvgesalit dávvirbursa", "item.minecraft.light_blue_dye": "Čuvgesalit Ivdni", "item.minecraft.light_blue_harness": "<PERSON>uvgesalit leaŋggat", "item.minecraft.light_gray_bundle": "Čuovgesrán<PERSON> d<PERSON>", "item.minecraft.light_gray_dye": "Čuvges<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_harness": "<PERSON>uvges<PERSON><PERSON><PERSON> le<PERSON>", "item.minecraft.lime_bundle": "Čuvgesruoná dávvirbursa", "item.minecraft.lime_dye": "Čuvgesruoná Ivdni", "item.minecraft.lime_harness": "Čuvgesruoná leaŋggat", "item.minecraft.lingering_potion": "<PERSON>issovaš jugus", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON><PERSON> buo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON> sorb<PERSON>", "item.minecraft.lingering_potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON> f<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.lingering_potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.leaping": "<PERSON><PERSON><PERSON>š liigenjuikema no<PERSON>ejugus", "item.minecraft.lingering_potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON> girdi<PERSON>", "item.minecraft.lingering_potion.effect.luck": "Bissovaš lihku noaidejugus", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bi<PERSON> jugus", "item.minecraft.lingering_potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON> nji<PERSON> no<PERSON>", "item.minecraft.lingering_potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.regeneration": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>az<PERSON>gahčči noaidejugus", "item.minecraft.lingering_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON> vuo<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.thick": "Suhkkes bisso<PERSON>š jugus", "item.minecraft.lingering_potion.effect.turtle_master": "<PERSON><PERSON><PERSON><PERSON>eipmila <PERSON>", "item.minecraft.lingering_potion.effect.water": "<PERSON><PERSON><PERSON>š <PERSON>", "item.minecraft.lingering_potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON>oiŋŋama no<PERSON>ugus", "item.minecraft.lingering_potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON> go<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.wind_charged": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>us", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.lodestone_compass": "Láidengeađgekompássa", "item.minecraft.mace": "Veažir", "item.minecraft.magenta_bundle": "Čuvgessáhppes dávvirbursa", "item.minecraft.magenta_dye": "Čuvgessáhppes Ivdni", "item.minecraft.magenta_harness": "Čuvgessáhppes leaŋggat", "item.minecraft.magma_cream": "Magmak<PERSON><PERSON>", "item.minecraft.magma_cube_spawn_egg": "Ma<PERSON><PERSON><PERSON>", "item.minecraft.mangrove_boat": "Mangrovefanas", "item.minecraft.mangrove_chest_boat": "Mangrovefanas mas giis<PERSON>", "item.minecraft.map": "<PERSON><PERSON>", "item.minecraft.melon_seeds": "Mel<PERSON>v<PERSON><PERSON>l<PERSON><PERSON>", "item.minecraft.melon_slice": "Meluvdna Bihttá", "item.minecraft.milk_bucket": "Mielkegievdni", "item.minecraft.minecart": "Roggangielká", "item.minecraft.miner_pottery_shard": "R<PERSON>vker<PERSON><PERSON> k<PERSON>", "item.minecraft.miner_pottery_sherd": "R<PERSON>vker<PERSON><PERSON> k<PERSON>", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mooshroom_spawn_egg": "Mooshrooma Ealáskanmonni", "item.minecraft.mourner_pottery_shard": "Morašead<PERSON><PERSON>", "item.minecraft.mourner_pottery_sherd": "Morašead<PERSON><PERSON>", "item.minecraft.mule_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.mushroom_stew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - Creator (Musihkkaboksa)", "item.minecraft.music_disc_far": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Music Disc", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Njuoska Lábbabiergu", "item.minecraft.name_tag": "Nammalihppu", "item.minecraft.nautilus_shell": "Nautilus Skálžu", "item.minecraft.nether_brick": "Nether<PERSON>v<PERSON>", "item.minecraft.nether_star": "<PERSON><PERSON>", "item.minecraft.nether_wart": "Netherspártu", "item.minecraft.netherite_axe": "Netheriitaákšu", "item.minecraft.netherite_boots": "Netheriitagapmagat", "item.minecraft.netherite_chestplate": "Netheriitaraddep<PERSON>", "item.minecraft.netherite_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_hoe": "Netheriitaguohkki", "item.minecraft.netherite_ingot": "Netheriita", "item.minecraft.netherite_leggings": "Netheriitabuvssat", "item.minecraft.netherite_pickaxe": "Netheriitabiikaákšu", "item.minecraft.netherite_scrap": "Netheriitabihttážat", "item.minecraft.netherite_shovel": "Netheriitagoaivu", "item.minecraft.netherite_sword": "Netheriitamiehkki", "item.minecraft.netherite_upgrade_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON> buoridus", "item.minecraft.oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.oak_chest_boat": "<PERSON>ai<PERSON><PERSON><PERSON> mas gii<PERSON>", "item.minecraft.ocelot_spawn_egg": "Meahccebussá Ealáskanmonni", "item.minecraft.ominous_bottle": "<PERSON><PERSON><PERSON><PERSON><PERSON>a bohtal", "item.minecraft.ominous_trial_key": "Bahávuoiŋ<PERSON>a g<PERSON>", "item.minecraft.orange_bundle": "Runta dávvirbursa", "item.minecraft.orange_dye": "Runta Ivdni", "item.minecraft.orange_harness": "<PERSON>ta lea<PERSON>ggat", "item.minecraft.painting": "<PERSON><PERSON><PERSON>", "item.minecraft.pale_oak_boat": "Šovkes <PERSON>", "item.minecraft.pale_oak_chest_boat": "Šovkes eaika<PERSON>as mas lea fanas", "item.minecraft.panda_spawn_egg": "Panda Ealáskanmonni", "item.minecraft.paper": "<PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Papegoia Ealáskanmonni", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.phantom_spawn_egg": "Idjagopmi Ealáskanmonni", "item.minecraft.pig_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_brute_spawn_egg": "<PERSON><PERSON> b<PERSON>á<PERSON><PERSON><PERSON><PERSON> Ealáskanmonni", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON>", "item.minecraft.pillager_spawn_egg": "<PERSON>iev<PERSON><PERSON><PERSON>", "item.minecraft.pink_bundle": "Guvgesrukses dávvirbursa", "item.minecraft.pink_dye": "Čuvgesrukses Ivdni", "item.minecraft.pink_harness": "Čuvgesrukses leaŋggat", "item.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pitcher_pod": "Muggarássekru<PERSON><PERSON>", "item.minecraft.plenty_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.plenty_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.poisonous_potato": "<PERSON><PERSON><PERSON><PERSON><PERSON> buđ<PERSON>", "item.minecraft.polar_bear_spawn_egg": "Jiekŋaguovžža Ealáskanmonni", "item.minecraft.popped_chorus_fruit": "Rahpasan chorusšattus", "item.minecraft.porkchop": "Njuoska spiidnečoarbealli", "item.minecraft.potato": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion": "<PERSON>aid<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.fire_resistance": "<PERSON><PERSON><PERSON>ier<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.leaping": "Liigen<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.luck": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.mundane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jugus", "item.minecraft.potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.regeneration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.slow_falling": "Njoazesgahčči noaidejugus", "item.minecraft.potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.thick": "Suhkkes jugus", "item.minecraft.potion.effect.turtle_master": "Galbarihcceipm<PERSON>", "item.minecraft.potion.effect.water": "Čáhcebohtal", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON>vuoiŋ<PERSON><PERSON> no<PERSON>us", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.wind_charged": "Bieggadoarohasa no<PERSON>us", "item.minecraft.pottery_shard_archer": "Dávgebáhčči kruhkkamoallu", "item.minecraft.pottery_shard_arms_up": "Geigejuvvon g<PERSON><PERSON><PERSON>", "item.minecraft.pottery_shard_prize": "Bálkk<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.pottery_shard_skull": "Oaiveskálžžu ruh<PERSON>", "item.minecraft.powder_snow_bucket": "Oppasgievdni", "item.minecraft.prismarine_crystals": "<PERSON>rism<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_shard": "Prismariid<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_shard": "Bálkk<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.prize_pottery_sherd": "Bálkkáš<PERSON> k<PERSON>all<PERSON>", "item.minecraft.pufferfish": "Mir<PERSON>gu<PERSON>i", "item.minecraft.pufferfish_bucket": "Mirkoguolleskállu", "item.minecraft.pufferfish_spawn_egg": "Mirkoguoli Ealáskanmonni", "item.minecraft.pumpkin_pie": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pumpkin_seeds": "Murbbetgilvagat", "item.minecraft.purple_bundle": "Sáhppes dávvirbursa", "item.minecraft.purple_dye": "Sáhppes Ivdni", "item.minecraft.purple_harness": "Sáhppes leaŋggat", "item.minecraft.quartz": "Netherkvártsa", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_foot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_copper": "Roav<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_gold": "R<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "Máhcahankompássa", "item.minecraft.red_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON> le<PERSON>", "item.minecraft.redstone": "Redstonedopmu", "item.minecraft.resin_brick": "Gáhččemuvra", "item.minecraft.resin_clump": "Gáhččečoaltu", "item.minecraft.rib_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template.new": "Erttetd<PERSON><PERSON><PERSON>", "item.minecraft.rotten_flesh": "Guohcagan Biergu", "item.minecraft.saddle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.salmon": "Njuoska <PERSON>", "item.minecraft.salmon_bucket": "Luossaskállu", "item.minecraft.salmon_spawn_egg": "<PERSON><PERSON>", "item.minecraft.scrape_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.scute": "Skálžu", "item.minecraft.sentry_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shaper_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>u", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>u", "item.minecraft.shears": "Sk<PERSON><PERSON>t", "item.minecraft.sheep_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shelter_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.shelter_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.shield": "Galba", "item.minecraft.shield.black": "Čáhppes Galba", "item.minecraft.shield.blue": "<PERSON><PERSON>", "item.minecraft.shield.brown": "Ruškes Galba", "item.minecraft.shield.cyan": "Turkiisa <PERSON>", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.green": "Ruoná Galba", "item.minecraft.shield.light_blue": "Čuvgesalit Galba", "item.minecraft.shield.light_gray": "<PERSON><PERSON>v<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.lime": "Čuvgesruoná Galba", "item.minecraft.shield.magenta": "Čuvgessáhppes Galba", "item.minecraft.shield.orange": "Runta Galba", "item.minecraft.shield.pink": "Čuvgesrukses Galba", "item.minecraft.shield.purple": "Sáhppes Galba", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.white": "<PERSON>ilges Galba", "item.minecraft.shield.yellow": "Fiskes Galba", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "Shulkera Ealáskanmonni", "item.minecraft.sign": "Galba", "item.minecraft.silence_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.silverfish_spawn_egg": "Silbasáhpán<PERSON>", "item.minecraft.skeleton_horse_spawn_egg": "Dákteriggeheasta Ealáskanmonni", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.desc": "Oaiveskálžominsttar", "item.minecraft.skull_banner_pattern.new": "Oaivesk<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_pottery_shard": "Oaiveskálžžu ruh<PERSON>", "item.minecraft.skull_pottery_sherd": "Oaiveskálžžu k<PERSON>allu", "item.minecraft.slime_ball": "Njivlespábba", "item.minecraft.slime_spawn_egg": "Njivleealli Ealáskanmonni", "item.minecraft.smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.applies_to": "<PERSON><PERSON><PERSON> dása:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "<PERSON><PERSON> b<PERSON><PERSON> k<PERSON>", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Lasit <PERSON>", "item.minecraft.smithing_template.armor_trim.ingredients": "Materiálat & Kristállat", "item.minecraft.smithing_template.ingredients": "Ávnna<PERSON>asit:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "<PERSON><PERSON> netheriita", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, da<PERSON><PERSON>du", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netheriita", "item.minecraft.smithing_template.upgrade": "Ođasmahtte: ", "item.minecraft.sniffer_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.snort_pottery_shard": "Sustašeami <PERSON>", "item.minecraft.snort_pottery_sherd": "Sustašeami <PERSON>", "item.minecraft.snout_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.snout_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.snow_golem_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.snowball": "Muohtačah<PERSON>r", "item.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spider_eye": "He<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spider_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion": "Diškalanjugus", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>škalanjugus", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.fire_resistance": "Buollingierdevašvuođa diš<PERSON>anju<PERSON>", "item.minecraft.splash_potion.effect.harming": "So<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.invisibility": "<PERSON>ain<PERSON>ahttun<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.luck": "Lihku diškalanjugus", "item.minecraft.splash_potion.effect.mundane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.oozing": "Njivlli diškalanjugus", "item.minecraft.splash_potion.effect.poison": "Mirkku diškalanjugus", "item.minecraft.splash_potion.effect.regeneration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.slow_falling": "Njoazesgahččama diškalanjugus", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.swiftness": "Leav<PERSON><PERSON> diš<PERSON>anju<PERSON>", "item.minecraft.splash_potion.effect.thick": "Suhkkes diškalanjugus", "item.minecraft.splash_potion.effect.turtle_master": "Galbarihcceipmila diškalanjugus", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.water_breathing": "Čáhcevuoigŋama diškalanjugus", "item.minecraft.splash_potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.wind_charged": "Bieggadoarohasa diškalanjugus", "item.minecraft.spruce_boat": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spruce_chest_boat": "<PERSON><PERSON><PERSON><PERSON> mas gii<PERSON>", "item.minecraft.spyglass": "Giikan", "item.minecraft.squid_spawn_egg": "Áhkárg<PERSON><PERSON>", "item.minecraft.stick": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_axe": "Geađge<PERSON><PERSON><PERSON>", "item.minecraft.stone_hoe": "Geađ<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_pickaxe": "Geađgebiikaákšu", "item.minecraft.stone_shovel": "Geađgegoaivu", "item.minecraft.stone_sword": "Geađgemiehkki", "item.minecraft.stray_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.strider_spawn_egg": "Gálašeaddji Ealáskanmonni", "item.minecraft.string": "<PERSON><PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tadpole_bucket": "Šlubboaiveskállu", "item.minecraft.tadpole_spawn_egg": "Šlubboaivvi ealáskanmonni", "item.minecraft.tide_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.awkward": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.empty": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.fire_resistance": "Buollingierdevašvu<PERSON><PERSON><PERSON> n<PERSON>a", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>a", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>a", "item.minecraft.tipped_arrow.effect.infested": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>a", "item.minecraft.tipped_arrow.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>a", "item.minecraft.tipped_arrow.effect.leaping": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>a", "item.minecraft.tipped_arrow.effect.levitation": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON><PERSON> n<PERSON>a", "item.minecraft.tipped_arrow.effect.mundane": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.tipped_arrow.effect.oozing": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.tipped_arrow.effect.regeneration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.slow_falling": "Njoazesgahččama njuolla", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.tipped_arrow.effect.strength": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>a", "item.minecraft.tipped_arrow.effect.thick": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.turtle_master": "Galbarihcceipmila n<PERSON>a", "item.minecraft.tipped_arrow.effect.water": "Diš<PERSON>ann<PERSON>olla", "item.minecraft.tipped_arrow.effect.water_breathing": "Č<PERSON><PERSON>cevuoigŋama nju<PERSON>a", "item.minecraft.tipped_arrow.effect.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.tipped_arrow.effect.weaving": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.tipped_arrow.effect.wind_charged": "Bieggadoarohasa njuolla", "item.minecraft.tnt_minecart": "Roggangielká mas TNT", "item.minecraft.torchflower_seeds": "Spáiddárlieđđegilvagat", "item.minecraft.totem_of_undying": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.trader_llama_spawn_egg": "Vuovdaleaddji Llama Ealáskanmonni", "item.minecraft.trial_key": "Geahččalusčoavdda", "item.minecraft.trident": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tropical_fish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tropical_fish_bucket": "Trohpalašguolleskállu", "item.minecraft.tropical_fish_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>š guoli <PERSON>", "item.minecraft.turtle_helmet": "Skálžogahpir", "item.minecraft.turtle_scute": "Galbarihcci skálžu", "item.minecraft.turtle_spawn_egg": "Galbarihcci Ealáskanmonni", "item.minecraft.vex_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.vex_armor_trim_smithing_template.new": "Biidnogopmi suodjalushearva", "item.minecraft.vex_spawn_egg": "Biidnogopmi Ealáskanmonni", "item.minecraft.villager_spawn_egg": "Gillilačča Ealáskanmonni", "item.minecraft.vindicator_spawn_egg": "Vávtta Ealáskanmonni", "item.minecraft.wandering_trader_spawn_egg": "Johttigávppašeaddji Ealáskanmonni", "item.minecraft.ward_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.warden_spawn_egg": "<PERSON><PERSON>", "item.minecraft.warped_fungus_on_a_stick": "Bonjoguoppar <PERSON>", "item.minecraft.water_bucket": "Čáhcegievdni", "item.minecraft.wayfinder_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wheat": "Nisugordni", "item.minecraft.wheat_seeds": "Ni<PERSON>gil<PERSON><PERSON>", "item.minecraft.white_bundle": "Vilges d<PERSON>", "item.minecraft.white_dye": "Vilges Ivdni", "item.minecraft.white_harness": "Vilges leaŋggat", "item.minecraft.wild_armor_trim_smithing_template": "Ráv<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.wind_charge": "Bieggadoarohas", "item.minecraft.witch_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.wither_skeleton_spawn_egg": "Witherdák<PERSON><PERSON><PERSON>", "item.minecraft.wither_spawn_egg": "Withera ealáskanmonni", "item.minecraft.wolf_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.wolf_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_axe": "Muorraákš<PERSON>", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "Muorrabiika<PERSON>š<PERSON>", "item.minecraft.wooden_shovel": "Muorragoaivu", "item.minecraft.wooden_sword": "Mu<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.writable_book": "<PERSON><PERSON><PERSON> ja <PERSON>", "item.minecraft.written_book": "<PERSON><PERSON><PERSON>", "item.minecraft.yellow_bundle": "Fiskes dávvirbursa", "item.minecraft.yellow_dye": "Fiskes Ivdni", "item.minecraft.yellow_harness": "<PERSON><PERSON>s lea<PERSON>ggat", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.zombie_horse_spawn_egg": "Jámešheasta Ealáskanmonni", "item.minecraft.zombie_spawn_egg": "Jámeža Ealáskanmonni", "item.minecraft.zombie_villager_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> Ealáskanmonni", "item.minecraft.zombified_piglin_spawn_egg": "Piglinjábmi Ealáskanmonni", "item.modifiers.any": "Go lea geava<PERSON>as:", "item.modifiers.armor": "Go lea bad<PERSON><PERSON>:", "item.modifiers.body": "Go geavahusas:", "item.modifiers.chest": "Go go<PERSON>a alde:", "item.modifiers.feet": "Go julgiin:", "item.modifiers.hand": "Go lea gieđ<PERSON>:", "item.modifiers.head": "Go oaivvi alde:", "item.modifiers.legs": "Go julgiin:", "item.modifiers.mainhand": "Go váldogieđas:", "item.modifiers.offhand": "Go nup<PERSON> gie<PERSON>:", "item.modifiers.saddle": "Go riidensále lea alde:", "item.nbt_tags": "NBT: %s Ča(t)nus(sat)", "item.op_block_warning.line1": "Várrehus:", "item.op_block_warning.line2": "<PERSON><PERSON> g<PERSON> dán <PERSON>, de d<PERSON><PERSON><PERSON> kommándo <PERSON>t", "item.op_block_warning.line3": "Ale geavat dán dá<PERSON>, ear<PERSON>go jus dieđát dan sisdo<PERSON>!", "item.unbreakable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.buildingBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.coloredBlocks": "<PERSON><PERSON><PERSON><PERSON> bloh<PERSON>", "itemGroup.combat": "<PERSON><PERSON><PERSON>", "itemGroup.consumables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.crafting": "Duodjedávvirat", "itemGroup.foodAndDrink": "<PERSON><PERSON><PERSON><PERSON><PERSON> ja juh<PERSON>", "itemGroup.functional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.hotbar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.ingredients": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.inventory": "<PERSON><PERSON><PERSON>", "itemGroup.natural": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.op": "Hálddašeaddji dávvirat", "itemGroup.redstone": "Redstoneblohkat", "itemGroup.search": "Oza dávviriid", "itemGroup.spawnEggs": "Ealáskanmonit", "itemGroup.tools": "Reaiddut & atnodávvirat", "item_modifier.unknown": "Amas dá<PERSON>vir<PERSON>: %s", "jigsaw_block.final_state": "Šaddá:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "<PERSON>i s<PERSON>hte j<PERSON>", "jigsaw_block.joint.rollable": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "jigsaw_block.joint_label": "Sirdinmálle:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON><PERSON>", "jigsaw_block.levels": "Dásit: %s", "jigsaw_block.name": "Namma:", "jigsaw_block.placement_priority": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>:", "jigsaw_block.placement_priority.tooltip": "Go Jigsaw blokka čatná oktii biht<PERSON>i, de lea dát dat ortnet man mielde dat bihttá gieđahallojuvvo go čatná čanastagaid stuorát ráhkadussii.\n\nBihttát vuoruhuvvojit vulosguvlui oktan summalis ordnegiin mii boatkkuha ráiggiid.", "jigsaw_block.pool": "Čuoz<PERSON><PERSON>id <PERSON>lda<PERSON>:", "jigsaw_block.selection_priority": "V<PERSON>ll<PERSON><PERSON><PERSON> v<PERSON>:", "jigsaw_block.selection_priority.tooltip": "Go váldobihttá gieđahallojuvvo oktavuođaide, de lea dát ortnet mas dát Jigsaw-bihttá geahččala čatnat iežas váldobihttái.\n\nJigsaws vuoruhuvvojit vulosguvlui oktan summalis ordnegiin mii boatkkuha ráiggiid.", "jigsaw_block.target": "<PERSON>uo<PERSON><PERSON><PERSON> namma:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - Creator (Musihkkaboksa)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Ovdáneamit", "key.attack": "Časkit/Bilidit", "key.back": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.creative": "Hutkás speallanhápmi", "key.categories.gameplay": "<PERSON><PERSON><PERSON>", "key.categories.inventory": "Dávvirvuorká", "key.categories.misc": "Eará", "key.categories.movement": "<PERSON><PERSON><PERSON>", "key.categories.multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.ui": "Spilla Interface", "key.chat": "<PERSON><PERSON>", "key.command": "Rahpat kommándo", "key.drop": "Luoitit dávviriid", "key.forward": "Vázzit ovddas guvlui", "key.fullscreen": "Molssohallat ollesšearpma", "key.hotbar.1": "Reaidolinnjásadji 1", "key.hotbar.2": "Reaidolinnjásadji 2", "key.hotbar.3": "Reaidolinnjásadji 3", "key.hotbar.4": "Reaidolinnjásadji 4", "key.hotbar.5": "Reaidolinnjásadji 5", "key.hotbar.6": "Reaidolinnjásadji 6", "key.hotbar.7": "Reaidolinnjásadji 7", "key.hotbar.8": "Reaidolinnjásadji 8", "key.hotbar.9": "Reaidolinnjásadji 9", "key.inventory": "Rahpat/giddet dá<PERSON>kká", "key.jump": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Sihkkun", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Sihko", "key.keyboard.down": "↓", "key.keyboard.end": "<PERSON><PERSON>", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "'", "key.keyboard.home": "Ruoktu", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Boallu 0", "key.keyboard.keypad.1": "Boallu 1", "key.keyboard.keypad.2": "Boallu 2", "key.keyboard.keypad.3": "Boallu 3", "key.keyboard.keypad.4": "Boallu 4", "key.keyboard.keypad.5": "Boallu 5", "key.keyboard.keypad.6": "Boallu 6", "key.keyboard.keypad.7": "Boallu 7", "key.keyboard.keypad.8": "Boallu8", "key.keyboard.keypad.9": "Boallu 9", "key.keyboard.keypad.add": "Boallu +", "key.keyboard.keypad.decimal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.keypad.divide": "Boallu /", "key.keyboard.keypad.enter": "<PERSON><PERSON>", "key.keyboard.keypad.equal": "Boallu =", "key.keyboard.keypad.multiply": "Boallu *", "key.keyboard.keypad.subtract": "Boallu -", "key.keyboard.left": "←", "key.keyboard.left.alt": "Gurut Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Gurut Control", "key.keyboard.left.shift": "Gurut shift", "key.keyboard.left.win": "<PERSON><PERSON> Win", "key.keyboard.menu": "V<PERSON><PERSON><PERSON>mat", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "<PERSON><PERSON><PERSON>", "key.keyboard.page.up": "Siidu Bajas", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "PrintScreen", "key.keyboard.right": "→", "key.keyboard.right.alt": "<PERSON><PERSON><PERSON>", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "<PERSON>lgeš Control", "key.keyboard.right.shift": "<PERSON><PERSON><PERSON>", "key.keyboard.right.win": "<PERSON><PERSON><PERSON>", "key.keyboard.scroll.lock": "Scrollenlohkka", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Gaskkaboallu", "key.keyboard.tab": "Tab ⇄", "key.keyboard.unknown": "Ii leat <PERSON>", "key.keyboard.up": "↑", "key.keyboard.world.1": "1. <PERSON><PERSON><PERSON><PERSON>", "key.keyboard.world.2": "2. <PERSON><PERSON><PERSON><PERSON>", "key.left": "<PERSON><PERSON><PERSON><PERSON> gurut guvlui", "key.loadToolbarActivator": "<PERSON><PERSON> reaidolinnjá aktivatora", "key.mouse": "Boallu %1$s", "key.mouse.left": "Gurutboallu", "key.mouse.middle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.mouse.right": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.pickItem": "Válljet blohka", "key.playerlist": "Spealliid listu", "key.quickActions": "Quick Actions", "key.right": "<PERSON><PERSON><PERSON><PERSON>", "key.saveToolbarActivator": "<PERSON><PERSON><PERSON> reaidolinnjá aktivatora", "key.screenshot": "<PERSON><PERSON><PERSON>", "key.smoothCamera": "Molssohallat cinematiskas kamera", "key.sneak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.socialInteractions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.spectatorOutlines": "<PERSON><PERSON><PERSON><PERSON> (Gehččiid)", "key.sprint": "<PERSON><PERSON><PERSON><PERSON>", "key.swapOffhand": "<PERSON><PERSON><PERSON> dávvira nup<PERSON> g<PERSON>i", "key.togglePerspective": "Molssohallat perspektiivva", "key.use": "Geavat dávviriid/<PERSON><PERSON> blo<PERSON>ka", "known_server_link.announcements": "Almmuhusat", "known_server_link.community": "Servvodat", "known_server_link.community_guidelines": "Servvodaga njuolggádusat", "known_server_link.feedback": "Máhcahagat", "known_server_link.forums": "Forumat", "known_server_link.news": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.report_bug": "<PERSON><PERSON><PERSON>", "known_server_link.status": "<PERSON><PERSON>", "known_server_link.support": "Veahkki", "known_server_link.website": "Neahttasiidu", "lanServer.otherPlayers": "Heivehusat Eará <PERSON>", "lanServer.port": "V<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lanServer.port.invalid": "Verráha ii sáhte duođaštit.\nDivtte teakstaruvtto guo<PERSON>in dahje čále logu gaskal 1024 ja 65535.", "lanServer.port.invalid.new": "Verrát ii leat dohkk<PERSON><PERSON>š\n<PERSON>đe rievdadansaji guorosin dahje lasit logu gaskal %s ja %s.", "lanServer.port.unavailable": "Verrá<PERSON> ii sáhte geavahit.\nDivtte teakstaruvtto guo<PERSON>in dahje čále logu gaskal 1024 ja 65535.", "lanServer.port.unavailable.new": "Verrát ii leat olámuttos.\nGuođe rievdadansaji guo<PERSON>in dahje lasit logu gaskal %s ja %s.", "lanServer.scanning": "<PERSON><PERSON><PERSON> ear<PERSON> m<PERSON>mmiid dán <PERSON>", "lanServer.start": "Álggat LAN Máilmmi", "lanServer.title": "LAN Máilbmi", "language.code": "sme_NO", "language.name": "Davvisámegiella", "language.region": "<PERSON><PERSON><PERSON><PERSON>", "lectern.take_book": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "loading.progress": "%s%%", "mco.account.privacy.info": "Loga e<PERSON>t <PERSON> ja diehtosuodjalus<PERSON><PERSON> birra", "mco.account.privacy.info.button": "Loga eanet o<PERSON><PERSON><PERSON> (GDPR)", "mco.account.privacy.information": "<PERSON>jang čađaha vissis doaimmaid mat sudd<PERSON><PERSON> mánáid ja sin priváhtavuođa, danne go čuvvot Children's Online Privacy Protetion Act (COPPA) dahje Mánáid neahttapriváhtavuođaeallima suodjalanlága ja General Data Protection Regulation (GDPR) dah<PERSON> dieđuid suodjalanlága.\n\nSoaittát dárbbašit váhnemiid dohkkehusa ovdal go beasat iežat Realms kontoi.", "mco.account.privacyinfo": "Mojanga doalaha lágaid mat suod<PERSON><PERSON> mánáid ja sin privataeallima danne go čuvvot Children's Online Privacy Protetion Act (COPPA) dahje Mánáid Neahttaprivatavuođaeallima Suodjalanlága ja General Data Protection Regulation (GDPR) dah<PERSON> Suodjalanlága.\n\nDon soaitat dárbbašit váhnemiid dohkkehusa ovdal go beasat iežat Realms konto sisa.\n\nJus dus lea boarráset Minecraft konto (ahte logget sisa geavaheaddjinamain), dalle fertet ođásmahttet konto ovtta Mojang kontoii.", "mco.account.update": "Ođásmahtte konto", "mco.activity.noactivity": "Ii makk<PERSON>rge aktivitehta maŋemus %s beaivvi", "mco.activity.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.button.download": "Viečča ođđ<PERSON><PERSON>s", "mco.backup.button.reset": "<PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "mco.backup.button.restore": "Ovddeštat", "mco.backup.button.upload": "<PERSON><PERSON>", "mco.backup.changes.tooltip": "Rievdamat", "mco.backup.entry": "Sihkkarastinmáŋgosat (%s)", "mco.backup.entry.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.enabledPack": "<PERSON><PERSON><PERSON><PERSON> anus", "mco.backup.entry.gameDifficulty": "Spealu v<PERSON>", "mco.backup.entry.gameMode": "Speallanhápmi", "mco.backup.entry.gameServerVersion": "Spealu serververšuvdna", "mco.backup.entry.name": "<PERSON><PERSON>", "mco.backup.entry.seed": "<PERSON><PERSON><PERSON>", "mco.backup.entry.templateName": "<PERSON><PERSON><PERSON>", "mco.backup.entry.undefined": "<PERSON><PERSON> r<PERSON>", "mco.backup.entry.uploaded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.worldType": "Máilbmemá<PERSON>", "mco.backup.generate.world": "<PERSON><PERSON><PERSON>", "mco.backup.info.title": "Rievdadusat maŋemuš liigevurkemis", "mco.backup.narration": "%s liigevuorká", "mco.backup.nobackups": "Dát Realmsa ii leat vuos vurkejuvvon liige.", "mco.backup.restoring": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.unknown": "AMAS", "mco.brokenworld.download": "Viečča", "mco.brokenworld.downloaded": "Vižžon", "mco.brokenworld.message.line1": "Álggat ođđásit dahje vállje eará máilmmi.", "mco.brokenworld.message.line2": "Don maid s<PERSON><PERSON><PERSON><PERSON> viež<PERSON> máilmmi iežainisspeallui.", "mco.brokenworld.minigame.title": "Dát minispeallu ii leat šat dohkkehuvvon", "mco.brokenworld.nonowner.error": "Fertet vuordit dassái Realmsa eaiggát lea ođásmahtten máilmmi", "mco.brokenworld.nonowner.title": "Máilb<PERSON> lea boar<PERSON>", "mco.brokenworld.play": "Speala", "mco.brokenworld.reset": "Álggat ođđásit", "mco.brokenworld.title": "Du m<PERSON> ii šat doalahuvvo", "mco.client.incompatible.msg.line1": "Du speallu ii ovttastuvvo Realms fálaldagain.", "mco.client.incompatible.msg.line2": "<PERSON><PERSON>vat ođ<PERSON><PERSON><PERSON>s Minecraft veršuvnna.", "mco.client.incompatible.msg.line3": "Realms ii leat dohkkehu<PERSON>von snapshottaiguin.", "mco.client.incompatible.title": "<PERSON><PERSON><PERSON> ii heive!", "mco.client.outdated.stable.version": "Du veršuvdna (%s) ii heibe Realms:ain.\n\nGeavat Minecrafta ođđaseamos veršuvnna.", "mco.client.unsupported.snapshot.version": "Du veršuvdna (%s) ii heive Realms:ain.\n\nRealms ii leat olamuttus dán snapshot ver<PERSON><PERSON><PERSON><PERSON><PERSON>.", "mco.compatibility.downgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>i", "mco.compatibility.downgrade.description": "<PERSON><PERSON><PERSON> máil<PERSON>mi spellojuvvui maŋemuš %s ve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; don leat dál %s ver<PERSON><PERSON><PERSON><PERSON><PERSON>. Máilmmi dási vuolideapmi sáhttá billistit juoidá ja mii eat sáhte sihkarastet ahte dat rahpasa dahjege doaibmá.\n\nLiige vuorká du máilmmis vurkejuvvo \"Máilmmevuorkkáide\". Máhcat fas máilmmi jos lea dárbu.", "mco.compatibility.incompatible.popup.title": "Veršuvdna ii ovttastuvo", "mco.compatibility.incompatible.releaseType.popup.message": "Máilbmi masa geah<PERSON>t searvat ii ovttastuvo dan ver<PERSON>uv<PERSON>in mas don leat.", "mco.compatibility.incompatible.series.popup.message": "<PERSON><PERSON><PERSON> máil<PERSON>mi spellojuvvui maŋemuš %s ver<PERSON><PERSON><PERSON><PERSON><PERSON>; don leat %s ver<PERSON><PERSON><PERSON><PERSON><PERSON>.\n\n<PERSON><PERSON>t veršuvnnat eai gulahala. <PERSON><PERSON><PERSON><PERSON> máilbmi dárbbašuvvo jus áiggut speallat dán veršuvnna.", "mco.compatibility.unverifiable.message": "Ii sáhttán duođaštit maŋemus veršuvnna mas dát máilbmi spellojuvvui. Jus máilmmi iešvuođat ođasmahttojuvvet dahje vuoliduvvet, de automáhtalaččat ráhkaduvvo ja vurkejuvvo máilbmevuorká \"Máilmmevuorkkáide\".", "mco.compatibility.unverifiable.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ii sáhte duođaštuvvot", "mco.compatibility.upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.compatibility.upgrade.description": "<PERSON><PERSON><PERSON> máil<PERSON><PERSON><PERSON> spellojuvvui maŋemuš %s ver<PERSON><PERSON><PERSON><PERSON><PERSON>; don leat dál %s ver<PERSON><PERSON><PERSON><PERSON><PERSON>. \n\nMáilmmi liigevuorká vurkejuvvo \"Máilmmivuorkkáide\".\n\nMáhcat deike iežat máilmmi jus dan d<PERSON>.", "mco.compatibility.upgrade.friend.description": "<PERSON><PERSON><PERSON> máil<PERSON><PERSON><PERSON> spellojuv<PERSON>i maŋemuš veršuvnnas %s; don leat dál veršuvnnas %s.\n\nMáilmmi liigevuorká vurkejuvvo \"Máilmmivuorkkáide\".\n\nRealm oamasteaddji sáhttá bidjat ruo<PERSON><PERSON> m<PERSON>, juos d<PERSON>.", "mco.compatibility.upgrade.title": "Háliidatgo duođas ođasmahttet dán máilmmi?", "mco.configure.current.minigame": "﻿ Dálá", "mco.configure.world.activityfeed.disabled": "Spealli feed lea jávkan gaskkamearalaččat", "mco.configure.world.backup": "Máilmmi liige vuorkkát", "mco.configure.world.buttons.activity": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.close": "<PERSON><PERSON><PERSON> <PERSON>a", "mco.configure.world.buttons.delete": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.edit": "Heivehusat", "mco.configure.world.buttons.invite": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.moreoptions": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.newworld": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.open": "Raba realma", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.players": "Speallit", "mco.configure.world.buttons.region_preference": "<PERSON><PERSON><PERSON><PERSON> guo<PERSON>...", "mco.configure.world.buttons.resetworld": "<PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "mco.configure.world.buttons.save": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.settings": "Heivehusat", "mco.configure.world.buttons.subscription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.switchminigame": "Lonut minispilla", "mco.configure.world.close.question.line1": "Du realm ii boađe leat vejola<PERSON> rahpat.", "mco.configure.world.close.question.line2": "Háliidatgo duođ<PERSON> j<PERSON>?", "mco.configure.world.close.question.title": "Háliidatgo dahkat rievdadusaid bissetkeahttá?", "mco.configure.world.closing": "Giddemin realma...", "mco.configure.world.commandBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.delete.button": "<PERSON><PERSON><PERSON>", "mco.configure.world.delete.question.line1": "Du realm sihkkojuvvo ágibeaivái", "mco.configure.world.delete.question.line2": "Háliidatgo duođ<PERSON> j<PERSON>?", "mco.configure.world.description": "<PERSON><PERSON>", "mco.configure.world.edit.slot.name": "<PERSON><PERSON><PERSON><PERSON> namma", "mco.configure.world.edit.subscreen.adventuremap": "<PERSON><PERSON><PERSON> he<PERSON>husat leat jáddaduvvon danne go du máilbmi lea fearán", "mco.configure.world.edit.subscreen.experience": "<PERSON><PERSON><PERSON> heivehusat leat jáddaduvvon danne go du máilbmi lea fuoma<PERSON>", "mco.configure.world.edit.subscreen.inspiration": "<PERSON><PERSON><PERSON> heivehusat leat jáddaduvvon danne go du máilbmi lea inspirášuvdna", "mco.configure.world.forceGameMode": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invite.narration": "Dus leat %s ođ<PERSON>a bovdejumit", "mco.configure.world.invite.profile.name": "<PERSON><PERSON>", "mco.configure.world.invited": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invites.ops.tooltip": "Operatevra", "mco.configure.world.invites.remove.tooltip": "Sihko", "mco.configure.world.leave.question.line1": "<PERSON><PERSON> guo<PERSON> d<PERSON>, de it beasa sisa ovdal go muhtin fas bovde du", "mco.configure.world.leave.question.line2": "Háliidatgo duođ<PERSON> j<PERSON>?", "mco.configure.world.loading": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.location": "Báiki", "mco.configure.world.minigame": "Dálá: %s", "mco.configure.world.name": "<PERSON><PERSON> namma", "mco.configure.world.opening": "Álggaheamin realm...", "mco.configure.world.players.error": "<PERSON><PERSON><PERSON>i mas diet namma ii gávdno", "mco.configure.world.players.inviting": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>i...", "mco.configure.world.players.title": "Speallit", "mco.configure.world.pvp": "ČvČ", "mco.configure.world.region_preference": "Miellaseamos guovlu", "mco.configure.world.region_preference.title": "Guovllu válljen", "mco.configure.world.reset.question.line1": "<PERSON> málbmi ođ<PERSON><PERSON>ttejuv<PERSON>, ja dál<PERSON> máilbmi si<PERSON>kojuv<PERSON>", "mco.configure.world.reset.question.line2": "Háliidatgo duođ<PERSON> j<PERSON>?", "mco.configure.world.resourcepack.question": "Don d<PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON><PERSON><PERSON><PERSON> resursapáhka speallat dán realmmas \n\n<PERSON>t go viežžat dan ja speallat?", "mco.configure.world.resourcepack.question.line1": "<PERSON><PERSON><PERSON><PERSON> he<PERSON><PERSON><PERSON> resursapáhka jus áiggut rahpat dán <PERSON>a", "mco.configure.world.resourcepack.question.line2": "Áiggut go viežžat neahtas ja spillet?", "mco.configure.world.restore.download.question.line1": "Du máilbmi lea vižžojuvvon ja lasihuvvon iežainis máilmmiide.", "mco.configure.world.restore.download.question.line2": "<PERSON><PERSON><PERSON><PERSON><PERSON> go joaŧkit?", "mco.configure.world.restore.question.line1": "<PERSON> m<PERSON><PERSON>mi ođásmahttejuvvo '%s' (%s)", "mco.configure.world.restore.question.line2": "Háliidatgo duođ<PERSON> j<PERSON>?", "mco.configure.world.settings.expired": "It sáhte heivehit nohkan <PERSON>a heivehusaid", "mco.configure.world.settings.title": "Heivehusat", "mco.configure.world.slot": "Máilbmi %s", "mco.configure.world.slot.empty": "Guo<PERSON>", "mco.configure.world.slot.switch.question.line1": "Du realm lonuhuvvo eará m<PERSON>mmiin", "mco.configure.world.slot.switch.question.line2": "Háliidatgo duođ<PERSON> j<PERSON>?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON>", "mco.configure.world.slot.tooltip.active": "Ra<PERSON>", "mco.configure.world.slot.tooltip.minigame": "<PERSON><PERSON><PERSON> minispellui", "mco.configure.world.spawnAnimals": "<PERSON><PERSON> el<PERSON>", "mco.configure.world.spawnMonsters": "<PERSON><PERSON> n<PERSON>", "mco.configure.world.spawnNPCs": "Lasit NPC-aid", "mco.configure.world.spawnProtection": "Sudje <PERSON>báik<PERSON>", "mco.configure.world.spawn_toggle.message": "Buot sullasaš eallit JÁVKET OALÁT jus jáddadat dán he<PERSON>a", "mco.configure.world.spawn_toggle.message.npc": "<PERSON><PERSON>t <PERSON>, <PERSON><PERSON>, JÁVKET OALÁT jus jáddadat dán he<PERSON>a", "mco.configure.world.spawn_toggle.title": "Váruhus!", "mco.configure.world.status": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.day": "beaivi", "mco.configure.world.subscription.days": "beaivvit", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.less_than_a_day": "Unnánet go beaivvi vel", "mco.configure.world.subscription.month": "m<PERSON><PERSON>", "mco.configure.world.subscription.months": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.recurring.daysleft": "Ođásmahtte diŋgojumi automáhtalaččat", "mco.configure.world.subscription.recurring.info": "Rievdadusat mat dahkkojuvvet du Realms diŋgojupm<PERSON>i, nugo r<PERSON><PERSON><PERSON><PERSON> dahje jádd<PERSON>t máksima ođasmahttima ii boađe oidnot ovdal go boahtte háve fertet máksit.", "mco.configure.world.subscription.remaining.days": "%1$s beaivvi", "mco.configure.world.subscription.remaining.months": "%1$s mánu", "mco.configure.world.subscription.remaining.months.days": "%1$s mánu, %2$s beaivvi", "mco.configure.world.subscription.start": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.tab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.timeleft": "<PERSON><PERSON> vel", "mco.configure.world.subscription.title": "<PERSON>", "mco.configure.world.subscription.unknown": "<PERSON>as", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "mco.configure.world.switch.slot.subtitle": "<PERSON><PERSON><PERSON> m<PERSON><PERSON> lea guo<PERSON>, vá<PERSON><PERSON> movt ráhkadat iežat máilmmi", "mco.configure.world.title": "Heivet realma:", "mco.configure.world.uninvite.player": "H<PERSON>liidat go geassit \"%s\" bovdejumi?", "mco.configure.world.uninvite.question": "Leat go sihk<PERSON>r ahte <PERSON>gut geassit eret bovdejumi", "mco.configure.worlds.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.connect.authorizing": "<PERSON><PERSON><PERSON><PERSON><PERSON> sisa...", "mco.connect.connecting": "Čatnaseamin <PERSON>ii...", "mco.connect.failed": "Ii ožžon oktavuođa realmain", "mco.connect.region": "Bálvvá báiki: %s", "mco.connect.success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON><PERSON><PERSON>", "mco.create.world.error": "Don fertet nama lasihit!", "mco.create.world.failed": "Ii s<PERSON>án r<PERSON> m<PERSON>!", "mco.create.world.reset.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>...", "mco.create.world.skip": "<PERSON><PERSON><PERSON>", "mco.create.world.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> maiddái válljet makkár m<PERSON>mmi biijat ođ<PERSON>a <PERSON>ii", "mco.create.world.wait": "Ráhkadeamin realma...", "mco.download.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.download.confirmation.line1": "Máilbmi maid <PERSON><PERSON><PERSON><PERSON> lasihit lea stuorat go %s", "mco.download.confirmation.line2": "Don it sáhte dán máilmmi goassege šat lasihit Realmsai", "mco.download.confirmation.oversized": "Máilbmi maid <PERSON><PERSON><PERSON><PERSON> viežžat lea stuorát go %s \n\nDon it beasa fas bidjat dán máilmmi iežat realmii", "mco.download.done": "Viežžan v<PERSON>", "mco.download.downloading": "Viežžamin", "mco.download.extracting": "Boltu", "mco.download.failed": "Viežžu ii leat onnestuvvan", "mco.download.percent": "%s %%", "mco.download.preparing": "Gárveme viežžama", "mco.download.resourcePack.fail": "Resursapáhka viežžamus ii lihkostuvvan!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Viečča ođđ<PERSON><PERSON> m<PERSON>", "mco.error.invalid.session.message": "Geahččal álggahit Minecrafta ođđásit", "mco.error.invalid.session.title": "Dohkketmeahttun speallanvuorru", "mco.errorMessage.6001": "Boarás<PERSON><PERSON><PERSON>", "mco.errorMessage.6002": "Geavahuseaktu eai leat dohk<PERSON>han", "mco.errorMessage.6003": "Viežárá<PERSON><PERSON><PERSON> lea joksan", "mco.errorMessage.6004": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lea joksan", "mco.errorMessage.6005": "M<PERSON><PERSON><PERSON> lea lo<PERSON>", "mco.errorMessage.6006": "Máilb<PERSON> lea boar<PERSON>", "mco.errorMessage.6007": "S<PERSON>alli oass<PERSON><PERSON>a menddo o<PERSON>", "mco.errorMessage.6008": "<PERSON><PERSON><PERSON>-namma", "mco.errorMessage.6009": "Vearu Realm-čilgehus", "mco.errorMessage.connectionFailure": "Juoga geavái boasttut, gea<PERSON><PERSON><PERSON><PERSON> fas maŋela<PERSON>.", "mco.errorMessage.generic": "Boast<PERSON><PERSON><PERSON><PERSON> i<PERSON>ii: ", "mco.errorMessage.initialize.failed": "<PERSON><PERSON> s<PERSON><PERSON>", "mco.errorMessage.noDetails": "Meattáhusdieđut eai addojuvvon", "mco.errorMessage.realmsService": "Boasttuvuohta i<PERSON> (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Ii sáhttán čatnašuvvot realmii: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "<PERSON><PERSON><PERSON><PERSON>š verš<PERSON>v<PERSON> iskamuš ii lihkostuvvon: %s", "mco.errorMessage.retry": "Geahččal mat ođđ<PERSON>", "mco.errorMessage.serviceBusy": "Realm-fálaldat lea badjelm<PERSON>e bivnnut dál.\nGeahčcal mat ođđasit čatnasit realmii moatti minuvtta geažin.", "mco.gui.button": "Boall<PERSON>", "mco.gui.ok": "Ok", "mco.info": "Diehtu!", "mco.invited.player.narration": "Bovdejuvvon spealli %s", "mco.invites.button.accept": "Dohkkehat", "mco.invites.button.reject": "<PERSON><PERSON><PERSON><PERSON>", "mco.invites.nopending": "Eai leat bovdejumit!", "mco.invites.pending": "<PERSON><PERSON><PERSON><PERSON> bovdejumit!", "mco.invites.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.minigame.world.changeButton": "Válljet eará minispealu", "mco.minigame.world.info.line1": "<PERSON><PERSON>t lonuha gaskkamearalaččat du máilmmi minispillain!", "mco.minigame.world.info.line2": "<PERSON> s<PERSON> má<PERSON><PERSON> boahtit ruov<PERSON><PERSON>otta alcc<PERSON>t máilbmái haga massimis maidege.", "mco.minigame.world.noSelection": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "mco.minigame.world.restore": "<PERSON><PERSON><PERSON><PERSON><PERSON> minispealu...", "mco.minigame.world.restore.question.line1": "Minispeallu loahpahuvva ja du <PERSON>a biddjo ruov<PERSON>a.", "mco.minigame.world.restore.question.line2": "Háliidatgo duođ<PERSON> j<PERSON>?", "mco.minigame.world.selected": "Vállje minispilla:", "mco.minigame.world.slot.screen.title": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>i...", "mco.minigame.world.startButton": "<PERSON><PERSON><PERSON>", "mco.minigame.world.starting.screen.title": "Minispeallu álgá...", "mco.minigame.world.stopButton": "Loahpat minispealu", "mco.minigame.world.switch.new": "<PERSON><PERSON><PERSON><PERSON> minispealu?", "mco.minigame.world.switch.title": "<PERSON><PERSON><PERSON> minispealu", "mco.minigame.world.title": "Molssut realm minispellui", "mco.news": "Realms ođđasat", "mco.notification.dismiss": "Gidde", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.message": "Java Realms diŋgojumit sirdojuvvet Microsoft Storii. Ale divtte iežat diŋgojumi boarásmuvvat!\nMolsso dál ja oaččo 30 beaivásaš Realms nuvttá.\n<PERSON><PERSON> \"Profilii\" minecraft.net siiddus ja molsso iežat diŋgojumi.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON> liŋkka", "mco.notification.visitUrl.message.default": "Iska liŋkka dás vuollelis", "mco.onlinePlayers": "Online speallit", "mco.play.button.realm.closed": "Realm lea gitta", "mco.question": "Gažaldat", "mco.reset.world.adventure": "Fearánat", "mco.reset.world.experience": "Vásáhusat", "mco.reset.world.generate": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.inspiration": "Inspirášuvdna", "mco.reset.world.resetting.screen.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON> m<PERSON>...", "mco.reset.world.seed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (eaktodáhtolaš)", "mco.reset.world.template": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.title": "<PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "mco.reset.world.upload": "<PERSON><PERSON>", "mco.reset.world.warning": "<PERSON><PERSON><PERSON> lo<PERSON>ha saji dál<PERSON>-máilmmiin", "mco.selectServer.buy": "Oaste realma!", "mco.selectServer.close": "Gidde", "mco.selectServer.closed": "Giddejuvvon realm", "mco.selectServer.closeserver": "Gidde realm", "mco.selectServer.configure": "He<PERSON>t realma", "mco.selectServer.configureRealm": "He<PERSON>t realma", "mco.selectServer.create": "Ráhkat realm", "mco.selectServer.create.subtitle": "Vállje ma<PERSON>á<PERSON> máilmmi bijat iežat ođđa realmii", "mco.selectServer.expired": "Nohkan realm", "mco.selectServer.expiredList": "<PERSON>pmi lea nohkan", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": "<PERSON> g<PERSON>ddenáigi lea nohkan", "mco.selectServer.expires.day": "<PERSON><PERSON><PERSON><PERSON> beav<PERSON> g<PERSON>", "mco.selectServer.expires.days": "Nohká %s be<PERSON><PERSON> g<PERSON>", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON><PERSON> fargga", "mco.selectServer.leave": "<PERSON><PERSON><PERSON>", "mco.selectServer.loading": "Viežžamin realmlisttu", "mco.selectServer.mapOnlySupportedForVersion": "<PERSON><PERSON><PERSON> ii doalahuvvo %s ver<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.minigame": "Minispillat:", "mco.selectServer.minigameName": "Minispeallu: %s", "mco.selectServer.minigameNotSupportedInVersion": "Ii sáhte dán minispilla spillet %s veršuvnnas", "mco.selectServer.noRealms": "Dus vissa ii leat Realm. Lasit Realma ja speala iežat ustibiiguin.", "mco.selectServer.note": "Fu<PERSON><PERSON>š:", "mco.selectServer.open": "Rabas realm", "mco.selectServer.openserver": "Rabas realm", "mco.selectServer.play": "Speala", "mco.selectServer.popup": "Realms lea <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vuo<PERSON>ki ne<PERSON>ta bokte návddašit Minecraft máilmmi gitta logi olbmáiguin. Das leat dievva minispillat ja dievva heivehuvvon máilmmit! Du<PERSON>še eaiggát ferte máksit.", "mco.selectServer.purchase": "Lasit realma", "mco.selectServer.trial": "Ánssaš geahččalanáiggi!", "mco.selectServer.uninitialized": "<PERSON><PERSON>l <PERSON>gg<PERSON>t ođđa realm!", "mco.snapshot.createSnapshotPopup.text": "Don leat r<PERSON><PERSON><PERSON><PERSON>min nuvttá Snapshot Realm:a mii biddjojuvvo oktii máksojuvvon Realms-diŋgojumiin. <PERSON><PERSON><PERSON> ođ<PERSON>a Snapshot Realm doaibmá nu guhká go máksojuvvon diŋgojupmi lea doaimmas. Du máksojuvvon Realm ii váikkuhuvvo.", "mco.snapshot.createSnapshotPopup.title": "<PERSON><PERSON><PERSON><PERSON>?", "mco.snapshot.creating": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Realm<PERSON>...", "mco.snapshot.description": "Ovttastuvvon \"%s\" diŋgo<PERSON><PERSON>in", "mco.snapshot.friendsRealm.downgrade": "Don fertet leat %s ver<PERSON><PERSON><PERSON><PERSON>s jus áiggut searvat dán Realm:ii", "mco.snapshot.friendsRealm.upgrade": "%s ferte ođasmahttit iežas Realm:a ovdal go sáhttá speallat dán veršuvnnas", "mco.snapshot.paired": "<PERSON><PERSON><PERSON> snapshot Realm lea ovttasta<PERSON> \"%s\" diŋgo<PERSON><PERSON>in", "mco.snapshot.parent.tooltip": "Geavat Minecraft:a ođđaseamos veršuvnna jus áiggut speallat dán Realm:as", "mco.snapshot.start": "Álggat nuvttás Snapshot Realma", "mco.snapshot.subscription.info": "<PERSON><PERSON>t snapshot Realm lea ovttastahtton iežat diŋgojuvvon Realmain mas lea namma '%s'. Dat doaibmá nu guhká go dát Realma lea ovttastahtton.", "mco.snapshot.tooltip": "Geavat Snapshot Realms oaidnit mo Minecraft boahttevaš veršuvnnat šaddet, main sáhttet leat ođđa doaimmat ja eará rievdadusat.\n\nDon gávnnat iežat dábálaš Realms spealu dábálaš veršuvnnas.", "mco.snapshotRealmsPopup.message": "Realms lea dál olámuttus Snapshots-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ja <PERSON><PERSON><PERSON><PERSON> Snapshot:ain 23w41a. <PERSON><PERSON><PERSON>s-di<PERSON><PERSON><PERSON><PERSON><PERSON> boahtá nuvttá Snapshot Realm mii lea sierra dábálaš <PERSON> Realm:as!", "mco.snapshotRealmsPopup.title": "Realms lea dál o<PERSON><PERSON><PERSON><PERSON>us Snapshot:as", "mco.snapshotRealmsPopup.urlText": "Loga eanet", "mco.template.button.publisher": "R<PERSON><PERSON>kadeadji", "mco.template.button.select": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.button.trailer": "Trailer", "mco.template.default.name": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.info.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.select.failure": "Mii eat sáhttán viežžat dán kategoriijas maidege. Iska iežat ne<PERSON>tta<PERSON>vuođa, dahje geahč<PERSON>al maŋŋelaš.", "mco.template.select.narrate.authors": "Čállit: %s", "mco.template.select.narrate.version": "veršuvdna %s", "mco.template.select.none": "<PERSON><PERSON><PERSON> kategoriija gusto lea guoros. <PERSON><PERSON>, dahje jus leat r<PERSON>dd<PERSON>, %s.", "mco.template.select.none.linkTitle": "ráh<PERSON> juoidá ieš", "mco.template.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.title.minigame": "Minispealut", "mco.template.trailer.tooltip": "Máilmme trailer", "mco.terms.buttons.agree": "<PERSON>v<PERSON><PERSON><PERSON><PERSON>", "mco.terms.buttons.disagree": "In Ovttaoaivilis", "mco.terms.sentence.1": "Mun dohk<PERSON>han Minecraft Realms", "mco.terms.sentence.2": "Geavahuseavttut", "mco.terms.title": "Realms Geavahuseaktu", "mco.time.daysAgo": "%1$s beaivvi <PERSON><PERSON>", "mco.time.hoursAgo": "%1$s diimmu áigi", "mco.time.minutesAgo": "%1$s minuvtta áigi", "mco.time.now": "a<PERSON>o d<PERSON>l", "mco.time.secondsAgo": "%1$s sekundda áigi", "mco.trial.message.line1": "<PERSON><PERSON><PERSON><PERSON> go alcceset Realmsa?", "mco.trial.message.line2": "Deaddil dása oažžut eanet dieđ<PERSON>!", "mco.upload.button.name": "<PERSON><PERSON><PERSON><PERSON>", "mco.upload.cancelled": "Lasih<PERSON><PERSON><PERSON> he<PERSON>i", "mco.upload.close.failure": "<PERSON>i máhte ﻿giddet du <PERSON>, geahčč<PERSON> fas maŋŋil", "mco.upload.done": "<PERSON><PERSON> ja g<PERSON>", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "<PERSON><PERSON><PERSON><PERSON> ii do<PERSON>! (%s)", "mco.upload.failed.too_big.description": "Válljejuvvon máilbmi lea menddo stuoris. Stuorámus sturradat mii dohkkehuvvo lea %s.", "mco.upload.failed.too_big.title": "<PERSON><PERSON><PERSON><PERSON> lea menddo stuoris", "mco.upload.hardcore": "Máŧoheami máilmmit eai sáhte lasihuvvot!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>mm<PERSON>", "mco.upload.select.world.none": "Iežainisspealli máilbmi ii gávdnon!", "mco.upload.select.world.subtitle": "Vállje iežainisspealli máilmmi máid <PERSON> lasihit", "mco.upload.select.world.title": "<PERSON><PERSON>", "mco.upload.size.failure.line1": "'%s' lea menddo stuoris!", "mco.upload.size.failure.line2": "Dat lea olles %s. <PERSON><PERSON><PERSON><PERSON><PERSON> sturrodat lea %s.", "mco.upload.uploading": "Lasiheamin '%s'", "mco.upload.verifying": "Dárkkis<PERSON> máilmmi", "mco.version": "Veršuvdna: %s", "mco.warning": "Váruhus!", "mco.worldSlot.minigame": "Minispeallu", "menu.custom_options": "Sierra heivehusat...", "menu.custom_options.title": "Sierra heivehusat", "menu.custom_options.tooltip": "Fuomáš: Sierra heivehusat leat olgguldas bálvváid ja/dahje sisdoaluid bokte boaht<PERSON>.\nGieđahala várrogas!", "menu.custom_screen_info.button_narration": "<PERSON><PERSON><PERSON> lea sierra <PERSON>. Loga eanet.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Sierra šearbma hi<PERSON>i", "menu.custom_screen_info.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "<PERSON><PERSON><PERSON>", "menu.feedback": "Máhcahagat...", "menu.feedback.title": "Máhcahagat", "menu.game": "Spealu válljemat", "menu.modded": " (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "menu.multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "Heivehusat...", "menu.paused": "<PERSON><PERSON><PERSON><PERSON>", "menu.playdemo": "<PERSON><PERSON>", "menu.playerReporting": "<PERSON><PERSON><PERSON><PERSON>", "menu.preparingSpawn": "Gárveme ealáskanbáikki: %s%%", "menu.quick_actions": "Quick Actions...", "menu.quick_actions.title": "Quick Actions", "menu.quit": "<PERSON><PERSON><PERSON>", "menu.reportBugs": "<PERSON><PERSON><PERSON>", "menu.resetdemo": "<PERSON><PERSON><PERSON>", "menu.returnToGame": "<PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON>ui", "menu.returnToMenu": "<PERSON><PERSON>ke ja mana ovdasiidui", "menu.savingChunks": "<PERSON><PERSON><PERSON><PERSON>", "menu.savingLevel": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "menu.sendFeedback": "Dávisstahte", "menu.server_links": "Serverčujuhusat...", "menu.server_links.title": "Serverčujuhusat", "menu.shareToLan": "Raba LAN", "menu.singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.working": "Bargame...", "merchant.deprecated": "Gillilaččat lasihit gálvvuid gukte beaivái.", "merchant.level.1": "Easkaálgi", "merchant.level.2": "Oahpahall<PERSON>", "merchant.level.3": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.4": "Čeahppi", "merchant.level.5": "<PERSON><PERSON><PERSON><PERSON>", "merchant.title": "%s - %s", "merchant.trades": "Gálvvut", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Njuike eret deaddimis %1$s boalu", "multiplayer.applyingPack": "<PERSON><PERSON><PERSON><PERSON>su<PERSON>", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>serverat leat j<PERSON>an. <PERSON><PERSON><PERSON> g<PERSON> fas maŋŋelaš!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "Don leat gildon boa<PERSON><PERSON><PERSON>ii", "multiplayer.disconnect.banned.expiration": "\n Du gieldun jávká %s", "multiplayer.disconnect.banned.reason": "Don leat gildon boahtimis serverii. Dá lea manne: %s", "multiplayer.disconnect.banned_ip.expiration": "\n Du gielddus jávká %s", "multiplayer.disconnect.banned_ip.reason": "Don leat gildon boahtimis serveris.\nDá lea manne: %s", "multiplayer.disconnect.chat_validation_failed": "Čátta čállosa duođašteapmi ii lihkostuvvan", "multiplayer.disconnect.duplicate_login": "<PERSON> rah<PERSON> konto <PERSON> b<PERSON>", "multiplayer.disconnect.expired_public_key": "Almm<PERSON>š <PERSON>oavdda lea nohkan. Iska leago iežat systemaáigi synkroniserejuvvon ja geahččal álggahit spealu ođđasit.", "multiplayer.disconnect.flying": "Girdit ii leat lohpi dán serveris", "multiplayer.disconnect.generic": "Boŧkejuvvon okta<PERSON><PERSON>ta", "multiplayer.disconnect.idling": "Don leat menddo guhká čužžon jaska!", "multiplayer.disconnect.illegal_characters": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>s", "multiplayer.disconnect.incompatible": "Veršuvdna ii heive! Ane baicce %s", "multiplayer.disconnect.invalid_entity_attacked": "Geahččaleamin soardit vearu entitia", "multiplayer.disconnect.invalid_packet": "Server sáddii páhka mii ii dohkkehuvvon", "multiplayer.disconnect.invalid_player_data": "Dohkketmeahttun speallidieđut", "multiplayer.disconnect.invalid_player_movement": "Lobiheames speallárlihkáduspáhkka bođii", "multiplayer.disconnect.invalid_public_key_signature": "<PERSON><PERSON><PERSON><PERSON><PERSON>š <PERSON>vdaga vuolláičálus lei boasttut.\nGeahččal ođđasit álggahit spealu.", "multiplayer.disconnect.invalid_public_key_signature.new": "<PERSON><PERSON><PERSON><PERSON><PERSON>š <PERSON>vdaga vuolláičálus lei boasttut.\nGeahččal ođđasit álggahit spealu.", "multiplayer.disconnect.invalid_vehicle_movement": "Lobiheames speall<PERSON> bođii", "multiplayer.disconnect.ip_banned": "Du IP lea gildojuvvon d<PERSON>is", "multiplayer.disconnect.kicked": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer.disconnect.missing_tags": "Váilevaš čoahkku mearkkain vuostáiváldui serveris.\nVáldde oktavuođa server operatevrrain.", "multiplayer.disconnect.name_taken": "<PERSON><PERSON><PERSON> namma lea juo anus", "multiplayer.disconnect.not_whitelisted": "Dus ii leat oassi dán server<PERSON>!", "multiplayer.disconnect.out_of_order_chat": "<PERSON><PERSON>tta páhkka mii ii doaimma bođii. Lea go systema <PERSON><PERSON> r<PERSON>?", "multiplayer.disconnect.outdated_client": "Veršuvdna ii heive! Ane baicce %s", "multiplayer.disconnect.outdated_server": "Veršuvdna ii heive! Ane baicce %s", "multiplayer.disconnect.server_full": "Server lea dievva!", "multiplayer.disconnect.server_shutdown": "Server gidde<PERSON><PERSON><PERSON><PERSON>", "multiplayer.disconnect.slow_login": "G<PERSON>va<PERSON><PERSON> menddo guhkes <PERSON> rahpat", "multiplayer.disconnect.too_many_pending_chats": "<PERSON><PERSON><PERSON> mat vurdet duo<PERSON><PERSON>", "multiplayer.disconnect.transfers_disabled": "Server ii dohkket sirdašumiid", "multiplayer.disconnect.unexpected_query_response": "Ii meroš<PERSON> he<PERSON>hu<PERSON>von dieđu k<PERSON>", "multiplayer.disconnect.unsigned_chat": "Oaččuimet čáttapáhkka mii váilu dahje ii leat rievttes ládje vuolláič<PERSON>llon.", "multiplayer.disconnect.unverified_username": "Ii sáhttán duodaštit geavaheaddjinama!", "multiplayer.downloadingStats": "Viežžamin s<PERSON>d ...", "multiplayer.downloadingTerrain": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "multiplayer.lan.server_found": "O<PERSON><PERSON>a báikkálaš server gávdnui: %s", "multiplayer.message_not_delivered": "<PERSON>i s<PERSON>hte s<PERSON> die<PERSON>, iska server logga: %s", "multiplayer.player.joined": "%s searvvai spellui", "multiplayer.player.joined.renamed": "%s (ovdal gohčoduvvon %s) bođii sisa", "multiplayer.player.left": "%s guđii spealu", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Online speallit: %s", "multiplayer.requiredTexturePrompt.disconnect": "Server g<PERSON><PERSON><PERSON> he<PERSON><PERSON><PERSON><PERSON> resurs<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer.requiredTexturePrompt.line1": "Dát server g<PERSON><PERSON><PERSON> he<PERSON>hu<PERSON><PERSON> resursapáhka.", "multiplayer.requiredTexturePrompt.line2": "Jus it dohkket he<PERSON><PERSON><PERSON><PERSON> re<PERSON>, de botkejuvvo oktavuohta serveriin.", "multiplayer.socialInteractions.not_available": "Sosiála oktavuođa oa<PERSON><PERSON> du<PERSON> máŋggaidspealli máilmmiin", "multiplayer.status.and_more": "... ja %s eanet ...", "multiplayer.status.cancelled": "Boŧkejuvvui", "multiplayer.status.cannot_connect": "Ii sáhte mannat serverii", "multiplayer.status.cannot_resolve": "IP ii gávdnon", "multiplayer.status.finished": "G<PERSON>rven", "multiplayer.status.incompatible": "Veršuvdn<PERSON> ii heive!", "multiplayer.status.motd.narration": "Otná diehtu: %s", "multiplayer.status.no_connection": "(v<PERSON><PERSON>)", "multiplayer.status.old": "<PERSON><PERSON>", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s millisekunddat", "multiplayer.status.pinging": "Pingemin...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s / %s spelliin leat speallamin", "multiplayer.status.quitting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.request_handled": "Statusjearaldat lea ordnej<PERSON>", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>a", "multiplayer.status.version.narration": "Servera veršuvdna: %s", "multiplayer.stopSleeping": "<PERSON><PERSON><PERSON>", "multiplayer.texturePrompt.failure.line1": "Server resurssapáhka bidjan ii lihkostuvvon", "multiplayer.texturePrompt.failure.line2": "<PERSON><PERSON><PERSON><PERSON> mat g<PERSON><PERSON><PERSON> he<PERSON><PERSON><PERSON> resurssaid eai soaitte doaibmat", "multiplayer.texturePrompt.line1": "Dát server g<PERSON><PERSON><PERSON> he<PERSON>hu<PERSON><PERSON> resursapáhka.", "multiplayer.texturePrompt.line2": "Áiggut go viežžat neahtas ja installeret automáhtalaččat?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nDiehtu serveris:\n%s", "multiplayer.title": "Speala m<PERSON>i", "multiplayer.unsecureserver.toast": "Čállosat mat sáddejuvvojit dán serveris soitet rievdaduvvon ja eai čájehuvvo nu movt ledje álggos č<PERSON>llon", "multiplayer.unsecureserver.toast.title": "Čátta čállosat eai sáhte duođaštuvvot", "multiplayerWarning.check": "Ale šat čájet dán dieđu", "multiplayerWarning.header": "Fuomáš: Sierra buvttadeaddji online speallu", "multiplayerWarning.message": "Váruhus: Mojang Studios ja Microsoft eaba fála, oamas, eabage čuovo online serveriid. Nuba lea vejolaš ahte soaittát oaidnit dahje vásihit unohas áššiid. Eará speallit soitet duljjet dahje čálážit fasttiid.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> On <PERSON>", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygene", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Lab<PERSON><PERSON><PERSON>", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Boallu: %s", "narration.button.usage.focused": "<PERSON><PERSON> ala <PERSON> boa<PERSON>", "narration.button.usage.hovered": "<PERSON>ija ala gurut s<PERSON>luin", "narration.checkbox": "Sárggestanruvtu: %s", "narration.checkbox.usage.focused": "<PERSON><PERSON><PERSON><PERSON> En<PERSON> boa<PERSON>", "narration.checkbox.usage.hovered": "<PERSON><PERSON><PERSON><PERSON> gurut s<PERSON>hpán<PERSON>luin", "narration.component_list.usage": "<PERSON><PERSON><PERSON> boa<PERSON> g<PERSON>gas<PERSON>", "narration.cycle_button.usage.focused": "Lonut %s modusii Enter boaluin", "narration.cycle_button.usage.hovered": "Lonut %s modusii gurut sáhpánboaluin", "narration.edit_box": "Reivdadanruvtu: %s", "narration.item": "Dávvir: %s", "narration.recipe": "Duoddjemálle %s dávvirii", "narration.recipe.usage": "<PERSON><PERSON><PERSON><PERSON> guru<PERSON> s<PERSON>in", "narration.recipe.usage.more": "<PERSON><PERSON><PERSON> e<PERSON><PERSON><PERSON><PERSON>", "narration.selection.usage": "<PERSON><PERSON><PERSON> <PERSON> baja<PERSON>/vulos boa<PERSON>uin", "narration.slider.usage.focused": "Rievdat meari olgeš dahje gurut sárg<PERSON>uin", "narration.slider.usage.hovered": "Rievdat meari boalu g<PERSON><PERSON><PERSON><PERSON>/gurut guvlui", "narration.suggestion": "Válljen %s evttohusaid %s evttohusain: %s", "narration.suggestion.tooltip": "Válljen %s evttohusaid %s evttohusain: %s (%s)", "narration.suggestion.usage.cycle.fixed": "<PERSON><PERSON><PERSON> vai lonuha boa<PERSON><PERSON> e<PERSON><PERSON><PERSON><PERSON>i", "narration.suggestion.usage.cycle.hidable": "<PERSON><PERSON><PERSON> vai lonuha boa<PERSON><PERSON> e<PERSON><PERSON><PERSON><PERSON><PERSON>, da<PERSON><PERSON> <PERSON> vai guođ<PERSON> ev<PERSON>", "narration.suggestion.usage.fill.fixed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "narration.suggestion.usage.fill.hidable": "<PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON> guo<PERSON> ev<PERSON><PERSON>", "narration.tab_navigation.usage": "Lonut lásiid Ctrl ja Tab boa<PERSON>id <PERSON>in", "narrator.button.accessibility": "Beasatl<PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "Ra<PERSON><PERSON><PERSON>", "narrator.button.language": "<PERSON><PERSON><PERSON>", "narrator.controls.bound": "%2$s boaluin s<PERSON>t %1$s", "narrator.controls.reset": "Máhcat %s boalu", "narrator.controls.unbound": "%s ii leat čadnon masage", "narrator.joining": "<PERSON><PERSON><PERSON>", "narrator.loading": "Viežžamin: %s", "narrator.loading.done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.position.list": "Válljen %s listoruvttu %s listoruvttuin", "narrator.position.object_list": "Válljejuvvan listoruvttu %s/%s osiin", "narrator.position.screen": "Šearpmabihttáš %s/%s bihttáin", "narrator.position.tab": "Válljen %s listoruvttu %s listoruvttuin", "narrator.ready_to_play": "<PERSON><PERSON><PERSON> speallat", "narrator.screen.title": "Álgosiidui", "narrator.screen.usage": "<PERSON><PERSON><PERSON><PERSON>iggoniin dahje <PERSON> boa<PERSON>", "narrator.select": "Válljejuvvon: %s", "narrator.select.world": "Válljejuvvon %s, maŋŋemus spillen: %s, %s, %s, veršuvdna: %s", "narrator.select.world_info": "Válljen %s, ma<PERSON><PERSON><PERSON><PERSON> speallan: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.toast.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optimizeWorld.confirm.description": "Dát viggá optimaliseret dán máilmmi nu ahte buot dieđut leat vurkejuvvon ođđase<PERSON>s spealloformáhta<PERSON>. <PERSON><PERSON> ádjána dađi guhkit mađi stuorát dát máilbmi lea. Dás ovddos dat soaitá doaibmat n<PERSON>, muhto ii šat doaimma boarrasut veršuvnnaiguin. Leatgo <PERSON> sihkkar ahte áiggut joatkit?", "optimizeWorld.confirm.proceed": "Ráhkat liigevuorkka ja buoridahtte", "optimizeWorld.confirm.title": "Optimalisere Máilmmi", "optimizeWorld.info.converted": "Ođasmahttojuvvon laigosat: %s", "optimizeWorld.info.skipped": "Hilgojuvvon laigosat: %s", "optimizeWorld.info.total": "Laigosat oktiibuot: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "optimizeWorld.stage.failed": "Ii lihkostuvvan! :(", "optimizeWorld.stage.finished": "<PERSON><PERSON><PERSON><PERSON>...", "optimizeWorld.stage.finished.chunks": "<PERSON><PERSON><PERSON><PERSON>...", "optimizeWorld.stage.finished.entities": "<PERSON><PERSON><PERSON><PERSON> entiti<PERSON> o<PERSON>...", "optimizeWorld.stage.finished.poi": "<PERSON><PERSON><PERSON><PERSON>...", "optimizeWorld.stage.upgrading": "<PERSON>nemin buot laigosiid...", "optimizeWorld.stage.upgrading.chunks": "<PERSON>nemin buot laigosiid...", "optimizeWorld.stage.upgrading.entities": "<PERSON><PERSON><PERSON> buot entitiiaid...", "optimizeWorld.stage.upgrading.poi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> buot mi<PERSON><PERSON><PERSON><PERSON>...", "optimizeWorld.title": "Optimal<PERSON><PERSON><PERSON> '%s'", "options.accessibility": "Beasatlašvuođa Heivehusat...", "options.accessibility.high_contrast": "<PERSON><PERSON> k<PERSON>", "options.accessibility.high_contrast.error.tooltip": "<PERSON><PERSON> kontrásta resursapáhkka ii leat olámuttos", "options.accessibility.high_contrast.tooltip": "Čielggudahtte boaluid garra kontrástai<PERSON>nniiguin", "options.accessibility.high_contrast_block_outline": "<PERSON>a k<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>d <PERSON>", "options.accessibility.high_contrast_block_outline.tooltip": "Čuvggodahtte válljejuvvon blohkaid o<PERSON><PERSON><PERSON><PERSON>.", "options.accessibility.link": "Beasa<PERSON>š<PERSON><PERSON><PERSON><PERSON>", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.menu_background_blurriness.tooltip": "Rievdada váldošearpma duogáža seađasvuođa.", "options.accessibility.narrator_hotkey": "<PERSON><PERSON><PERSON><PERSON><PERSON> boa<PERSON>u", "options.accessibility.narrator_hotkey.mac.tooltip": "<PERSON><PERSON><PERSON> muit<PERSON>addji bidjat ala ja eret 'Cmd+B' boa<PERSON>in.", "options.accessibility.narrator_hotkey.tooltip": "Dikta muitaleaddji časkit ala ja eret 'Ctrl+B' boa<PERSON>in", "options.accessibility.panorama_speed": "Panorama jorranleaktu", "options.accessibility.text_background": "Teaks<PERSON><PERSON>g<PERSON><PERSON>", "options.accessibility.text_background.chat": "Čátta", "options.accessibility.text_background.everywhere": "Miehtá", "options.accessibility.text_background_opacity": "Čađačuovgi teakstaduogáš", "options.accessibility.title": "Beasatlašvuođa Heivehusat...", "options.allowServerListing": "<PERSON>ova serverlisttuid", "options.allowServerListing.tooltip": "Serverat sáhttet čájehit online-spelliid o<PERSON>in almmus stáhtusisttiset. Go dát heivehus lea j<PERSON>, de eai čájehuvvo listtuin.", "options.ao": "<PERSON><PERSON><PERSON>", "options.ao.max": "<PERSON><PERSON><PERSON>", "options.ao.min": "<PERSON><PERSON><PERSON>", "options.ao.off": "Ii alde", "options.attack.crosshair": "<PERSON><PERSON><PERSON>", "options.attack.hotbar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.attackIndicator": "Časkinmearka", "options.audioDevice": "Speallanrustte<PERSON>", "options.audioDevice.default": "Systema dábálaš", "options.autoJump": "Auto-njuikun", "options.autoSuggestCommands": "<PERSON><PERSON><PERSON><PERSON>", "options.autosaveIndicator": "Autovurkenindikáhtor", "options.biomeBlendRadius": "<PERSON><PERSON><PERSON>", "options.biomeBlendRadius.1": "II ALDE (Jođáneamos)", "options.biomeBlendRadius.11": "11x11 (Ekstrema)", "options.biomeBlendRadius.13": "13x13 (<PERSON><PERSON>)", "options.biomeBlendRadius.15": "15x15 (Max)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.7": "7x7 (<PERSON><PERSON>)", "options.biomeBlendRadius.9": "9x9 (Eatnat)", "options.chat": "Č<PERSON>tta he<PERSON>husat...", "options.chat.color": "Ivnnit", "options.chat.delay": "<PERSON><PERSON><PERSON>: %s sekundda", "options.chat.delay_none": "<PERSON><PERSON><PERSON>: <PERSON><PERSON>", "options.chat.height.focused": "Čielgasa Allodat", "options.chat.height.unfocused": "Seađđ<PERSON> Allodat", "options.chat.line_spacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.chat.links": "Web liŋkkat", "options.chat.links.prompt": "Dohkkehus liŋkkain", "options.chat.opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.chat.scale": "Čállagiid Sturrodat", "options.chat.title": "<PERSON><PERSON><PERSON>...", "options.chat.visibility": "<PERSON><PERSON><PERSON>", "options.chat.visibility.full": "Oidnosis", "options.chat.visibility.hidden": "Čih<PERSON>", "options.chat.visibility.system": "Dušše kommándot", "options.chat.width": "Viidodat", "options.chunks": "%s laigosa", "options.clouds.fancy": "Čáppa", "options.clouds.fast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.controls": "Boalut...", "options.credits_and_attribution": "Dahkkit & lasihusat...", "options.damageTiltStrength": "Bávččaslihkasteapmi", "options.damageTiltStrength.tooltip": "Man guhká kamera lihkasta go bávččagat.", "options.darkMojangStudiosBackgroundColor": "Ivdnehis logo", "options.darkMojangStudiosBackgroundColor.tooltip": "Molsu Mojang Studios logošearpmaduogáža čáhppadin.", "options.darknessEffectScale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "options.darknessEffectScale.tooltip": "<PERSON><PERSON><PERSON> man olu sea<PERSON>vuođa váikkuhus coahkká go gohcci dahje sculk-rieh<PERSON>i fuo<PERSON><PERSON><PERSON> du.", "options.difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.easy": "<PERSON><PERSON><PERSON>", "options.difficulty.easy.info": "<PERSON>s e<PERSON><PERSON><PERSON><PERSON>, m<PERSON><PERSON> bá<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> unnán. Don neal<PERSON>ut ja báv<PERSON><PERSON><PERSON>httojuvvot dassáigo báhcet vihtta váimmu.", "options.difficulty.hard": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.hard.info": "<PERSON>s e<PERSON>it ealáskit ja bá<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> garraseappot. <PERSON> nealggut jođánea<PERSON>ot ja sáhtát das jápmit.", "options.difficulty.hardcore": "Máŧoheapme", "options.difficulty.normal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.normal.info": "<PERSON>s eallit ealáskit ja báv<PERSON><PERSON><PERSON>htte<PERSON> dá<PERSON><PERSON><PERSON><PERSON> fá<PERSON>. Don nealggut ja báv<PERSON>čagahttojuvvot dassáigo báhcá okta váibmobealli.", "options.difficulty.online": "Server<PERSON>", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "Bahás eallit eai ealás ja dušše muhtun nevtrála eallit ealáskit. Don it nealggo ja dus dađistaga lassána heagga.", "options.directionalAudio": "3D jietna", "options.directionalAudio.off.tooltip": "Klássihkalaš stereojietna", "options.directionalAudio.on.tooltip": "Geavaha HRTF-vuo<PERSON><PERSON><PERSON>uvvon jietnadieđuid buoridit 3D-jiena simulerema. Gáibida HRTF-ovttatstahtti jietnarusttega ja doaibmá buoremusat belljosiiguin.", "options.discrete_mouse_scroll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>en", "options.entityDistanceScaling": "<PERSON><PERSON><PERSON><PERSON>ka", "options.entityShadows": "<PERSON><PERSON><PERSON> su<PERSON>", "options.font": "Fonta he<PERSON>...", "options.font.title": "<PERSON><PERSON><PERSON>", "options.forceUnicodeFont": "Bágge Unicode Fontta", "options.fov": "FOV", "options.fov.max": "<PERSON>hana <PERSON>", "options.fov.min": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.fovEffectScale": "FOV váikkuhus", "options.fovEffectScale.tooltip": "Mearrida man olu FOV rievdá go spealli váikkuhuvva iešguđetge váikkuhusain.", "options.framerate": "%s fps", "options.framerateLimit": "Max FPS", "options.framerateLimit.max": "Noh<PERSON><PERSON>ahttun", "options.fullscreen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.fullscreen.current": "Dálá", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "Ollesšearpma čuokkisčoahkku", "options.fullscreen.unavailable": "Heivehusat eai leat ol<PERSON>s", "options.gamma": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.gamma.default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.gamma.max": "<PERSON><PERSON>v<PERSON>", "options.gamma.min": "Seavdnjat", "options.generic_value": "%s: %s", "options.glintSpeed": "Šealgunleaktu", "options.glintSpeed.tooltip": "<PERSON><PERSON><PERSON> no<PERSON> dávviriid <PERSON><PERSON><PERSON> le<PERSON>.", "options.glintStrength": "Š<PERSON>guma f<PERSON>", "options.glintStrength.tooltip": "<PERSON><PERSON><PERSON> no<PERSON> dávviriid <PERSON>a <PERSON>.", "options.graphics": "<PERSON><PERSON><PERSON>", "options.graphics.fabulous": "Rámálmas!", "options.graphics.fabulous.tooltip": "%s grafihkat geavahit šearpmabuvttu čáje<PERSON>, balvvaid ja partihkkaliid čađačuovgi blohkaid ja čá<PERSON>i <PERSON>. \nDát sáhttá garrasit váikkuhit doaibmabuvttu mátkedihtorin ja 4K šearpmain.", "options.graphics.fancy": "Čáppa", "options.graphics.fancy.tooltip": "Čáppa grafihkat dássádallet doaibmabuvttu ja kvalitehta eanas diht<PERSON>in. \n<PERSON><PERSON><PERSON><PERSON>, balvvat ja partihkkalat eai oidno čađačuovgi blohkaid ja čá<PERSON>i <PERSON>.", "options.graphics.fast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "Dohk<PERSON><PERSON>š grafihkat unnidit man olu muohta- ja arve-partihkkalat oidnojit.\nČađačuovgi funk<PERSON>uvnnat jáddaduvvojit iešguđegelágan blohkain dego lastain.", "options.graphics.warning.accept": "Joatk<PERSON> doarjaga haga", "options.graphics.warning.cancel": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.warning.message": "Du grafihkkabierggas ii dorjojuvvo %s grafihka heivehusain.\n\n<PERSON><PERSON><PERSON><PERSON><PERSON> hilgut dán dieđu ja joatkit, muhto du speallanbierggas ii šat dorjojuvvo jus geavahat %s grafihka.", "options.graphics.warning.renderer": "Rendereaddji gávdnon: [%s]", "options.graphics.warning.title": "Grafihkka apparáhta ii leat dohkehuvvon", "options.graphics.warning.vendor": "Vendor gávdnon: [%s]", "options.graphics.warning.version": "OpenGL veršuvdna gávdnon: [%s]", "options.guiScale": "GUI sturrodat", "options.guiScale.auto": "﻿Auto", "options.hidden": "Čih<PERSON>", "options.hideLightningFlashes": "J<PERSON>dd<PERSON>lddagasčuovgga", "options.hideLightningFlashes.tooltip": "Álddagas ii čuovggat almmis. Álddagasa časkkastat gal ain oidno.", "options.hideMatchedNames": "Čiega sullas<PERSON>š namaid", "options.hideMatchedNames.tooltip": "Sierra buvttadeaddjid serverat sáhttet sáddet čállo<PERSON>id ja dieđuid formáhtaid bokte maid mii eat leat heivehan.\nGo dát heivehus lea alde, dalle viggá speallu č<PERSON>hkat namaid máid don leat gieldán dák<PERSON><PERSON>r serveriin maiddái.", "options.hideSplashTexts": "Čiega fuomáščállosa", "options.hideSplashTexts.tooltip": "Čiehká fiskes diškalanteavstta váldošearpmas.", "options.inactivityFpsLimit": "Vuolit FPS:a go", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Vuolida govvadávjodaga 30 govvii go spealli ii daga maidege minuvttas. Dat vuolida ges 10 govvii maŋŋel vel ovcci minuvtta.", "options.inactivityFpsLimit.minimized": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.inactivityFpsLimit.minimized.tooltip": "Vuolida govvadávjodaga dušše go láse lea unniduvvon.", "options.invertMouse": "<PERSON><PERSON>ggon vulos oivviid", "options.japaneseGlyphVariants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.japaneseGlyphVariants.tooltip": "Geavaha <PERSON> sorttat CJK-bustávaid dá<PERSON><PERSON><PERSON><PERSON>.", "options.key.hold": "<PERSON><PERSON><PERSON> boalu", "options.key.toggle": "Lotnahuvvá", "options.language": "G<PERSON><PERSON>...", "options.language.title": "<PERSON><PERSON><PERSON>", "options.languageAccuracyWarning": "(Jorgalusat eai soaitte 100%% riekta)", "options.languageWarning": "Jorgalusat eai soaitte 100%% riekta", "options.mainHand": "V<PERSON>ldogiehta", "options.mainHand.left": "Gurut", "options.mainHand.right": "<PERSON><PERSON><PERSON>", "options.mipmapLevels": "Mipmap dásit", "options.modelPart.cape": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.hat": "Gáhpir", "options.modelPart.jacket": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "<PERSON><PERSON> b<PERSON>", "options.modelPart.left_sleeve": "<PERSON><PERSON><PERSON>", "options.modelPart.right_pants_leg": "<PERSON><PERSON><PERSON>", "options.modelPart.right_sleeve": "<PERSON><PERSON><PERSON>", "options.mouseWheelSensitivity": "<PERSON><PERSON><PERSON><PERSON>", "options.mouse_settings": "Dihtorsáhpána Heivehusat...", "options.mouse_settings.title": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>at", "options.multiplayer.title": "M<PERSON>ŋggaidspeali heivehusat...", "options.multiplier": "%sx", "options.music_frequency": "Musihka d<PERSON>", "options.music_frequency.constant": "<PERSON><PERSON>", "options.music_frequency.default": "Dábálaččat", "options.music_frequency.frequent": "Dávjá", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.narrator.all": "<PERSON><PERSON><PERSON><PERSON> buot", "options.narrator.chat": "Loga Chátta Jitnosit", "options.narrator.notavailable": "Ii leat ol<PERSON><PERSON><PERSON>s", "options.narrator.off": "Ii alde", "options.narrator.system": "Lohká systema", "options.notifications.display_time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.notifications.display_time.tooltip": "Váikkuha man guhkes <PERSON>gi fuom<PERSON><PERSON><PERSON> o<PERSON>.", "options.off": "II ALDE", "options.off.composed": "%s: II ALDE", "options.on": "ALDE", "options.on.composed": "%s: ALDE", "options.online": "Online...", "options.online.title": "Online heivehusat", "options.onlyShowSecureChat": "<PERSON><PERSON><PERSON><PERSON>", "options.onlyShowSecureChat.tooltip": "<PERSON><PERSON><PERSON><PERSON> du<PERSON> máid mii sáhttit duođaštit ahte muhtun lea duođaid s<PERSON>, ja ahte dat ii leat heivehuvvon makk<PERSON>rge ládje.", "options.operatorItemsTab": "Hálddašeaddji dávvirat", "options.particles": "<PERSON><PERSON><PERSON><PERSON>", "options.particles.all": "<PERSON><PERSON><PERSON>", "options.particles.decreased": "Unnánet", "options.particles.minimal": "<PERSON><PERSON><PERSON>", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer": "Beallecaggi", "options.prioritizeChunkUpdates.byPlayer.tooltip": "<PERSON><PERSON><PERSON> dagut laigosis šaddet ođđasit kompileret laigosa o<PERSON>. <PERSON><PERSON><PERSON> fá<PERSON><PERSON><PERSON> blohkkabidjama ja -biđgema.", "options.prioritizeChunkUpdates.nearby": "Áibbas caggi", "options.prioritizeChunkUpdates.nearby.tooltip": "Lagas laigosat kompilerejuvvojit <PERSON> o<PERSON>. <PERSON><PERSON><PERSON> s<PERSON>t<PERSON> č<PERSON>hcit speallodoibmii go blohkat biddjojit dahje cuvkejuvvojit.", "options.prioritizeChunkUpdates.none": "Á<PERSON><PERSON>ig<PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Lagas laigosat čoggojuvvet bálddalas árppuide. D<PERSON>t s<PERSON>tá mielddisbuktit veaháš visu<PERSON><PERSON>, go blohkat cuvkejuvvet.", "options.rawMouseInput": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.realmsNotifications": "Realms ođđasat ja bovdehusat", "options.realmsNotifications.tooltip": "Almmuha Realms ođđasiid ja bovdehusaid váldošearpmas ja čájeha sin gullevaš govaža Realms-boaluin.", "options.reducedDebugInfo": "Unnit sivva<PERSON><PERSON> dieđut", "options.renderClouds": "Balvvat", "options.renderCloudsDistance": "Balvvaid gaska", "options.renderDistance": "Oaidninguhkodat", "options.resourcepack": "Resursapáhkat...", "options.rotateWithMinecart": "Jorahuvvo roggangielkkáin", "options.rotateWithMinecart.tooltip": "<PERSON><PERSON><PERSON> jus roggangie<PERSON><PERSON> moh<PERSON>ta dahje jorahu<PERSON>vo, de spealli oaidnu maid jorahuvvo seamma guvlui. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> duš<PERSON> go \"roggangielkk<PERSON>id buorida<PERSON>tin\"-geahččaleapmi geavahuvvo.", "options.screenEffectScale": "Botnjan váikkuhus", "options.screenEffectScale.tooltip": "Váibmomoiddodaga ja netherportála <PERSON>botnjasaddan effeavtta givrrodat.\nBuot vuolimus dásis lotnahuvvá váibmomoiddodat effeakta ruoná čuozahusain.", "options.sensitivity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.sensitivity.max": "HYPERJOHTILIT!!!", "options.sensitivity.min": "*gávvasat*", "options.showNowPlayingToast": "<PERSON><PERSON><PERSON>", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "Čájet tekstema", "options.simulationDistance": "Simulašuvdnagaska", "options.skinCustomisation": "<PERSON><PERSON><PERSON><PERSON>...", "options.skinCustomisation.title": "<PERSON><PERSON><PERSON><PERSON>", "options.sounds": "<PERSON><PERSON><PERSON><PERSON> ja <PERSON>...", "options.sounds.title": "Musih<PERSON> & Ji<PERSON>id <PERSON>at", "options.telemetry": "Telemetriijadieđut...", "options.telemetry.button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.telemetry.button.tooltip": "\"%s\" sisttis<PERSON><PERSON><PERSON> dušše gáibiduvvon dieđuid.\n\"%s\" sisttisdoallá sihke eak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dieđuid ja gáibiduvvon dieđuid.", "options.telemetry.disabled": "Telemetriija lea j<PERSON>dd<PERSON>duvvon.", "options.telemetry.state.all": "<PERSON><PERSON><PERSON>", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON>", "options.telemetry.state.none": "Ii mihkkege", "options.title": "Heivehusat", "options.touchscreen": "Guoskkašearbma-modus", "options.video": "Video Heivehusat...", "options.videoTitle": "Video Heivehusat", "options.viewBobbing": "<PERSON><PERSON><PERSON>", "options.visible": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.vsync": "VSync", "outOfMemory.message": "Minecraftas ii leat šat sadji muittus.\n\n<PERSON><PERSON> juogalágan speallosi<PERSON>, dahje ii leat juolluduvvon doarvái muitu Java Virtual Machine:ii.\n\nSpeallu giddejuvvui máilmmi gádjuma dihte. <PERSON><PERSON> g<PERSON> gurret muittu dan muddui ahte besset speallat fas, muhto dát ii lihkosmuvvan.\n\nÁinnas álggat spealu ođđasit jus oainnát dán dieđu.", "outOfMemory.title": "Ii leat šat sadji muit<PERSON>!", "pack.available.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.copyFailure": "<PERSON>i sá<PERSON> máŋget páhkaid", "pack.dropConfirm": "Háliidat go lasihit čuovvovaš páhkaid Minecraftii?", "pack.dropInfo": "Gease ja luoitte fiillaid dán láse sisa nu ahte lasihat páhkaid", "pack.dropRejected.message": "<PERSON>uov<PERSON><PERSON>š fiillat eai lean rievtteslágan páhkat ja dat eai máŋgejuvvon:\n %s", "pack.dropRejected.title": "<PERSON><PERSON><PERSON>", "pack.folderInfo": "(<PERSON><PERSON> p<PERSON> fi<PERSON> dása)", "pack.incompatible": "Ii heive", "pack.incompatible.confirm.new": "Páhkka lea ráhkaduvvon ođđaseabbo Minecraft veršuvdnii ja ii soaitte šat doaibmat albma ládje.", "pack.incompatible.confirm.old": "Páhkka lea ráhkaduvvon boarráset Minecraft veršuvdnii ja ii soaitte šat doaibmat albma ládje.", "pack.incompatible.confirm.title": "Leat go sihkar ahte áiggut rahpat dán páhka?", "pack.incompatible.new": "(Ráhkaduvvon ođđaseabbo Minecraft veršuvdnii)", "pack.incompatible.old": "(Ráhkaduvvon boarráset Minecraft veršuvdnii)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON> p<PERSON>d g<PERSON>", "pack.selected.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.builtin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.feature": "ie<PERSON><PERSON><PERSON><PERSON>", "pack.source.local": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.server": "server", "pack.source.world": "<PERSON><PERSON><PERSON><PERSON>", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Šilju", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ji", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Mu<PERSON><PERSON>", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "<PERSON>uo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Skoalfesitruvdna", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Beaivvášlieđit", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Riddu", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Páhkkejuv<PERSON>", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Summalis sorta", "parsing.bool.expected": "Meroštii duohta dahje boastu du<PERSON>e", "parsing.bool.invalid": "Boolean-die<PERSON><PERSON> ledje boastut, mero<PERSON><PERSON><PERSON> 'true' dahje 'false' muhto lei '%s'", "parsing.double.expected": "Me<PERSON>štii desimálanummara", "parsing.double.invalid": "Boastu desimálanummar '%s'", "parsing.expected": "Meroštii '%s'", "parsing.float.expected": "<PERSON><PERSON><PERSON><PERSON><PERSON>i", "parsing.float.invalid": "Boasttusátni '%s'", "parsing.int.expected": "Meroštii ollesnummara", "parsing.int.invalid": "Boastu ollesnummar '%s'", "parsing.long.expected": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "parsing.long.invalid": "Boastu guhkku '%s'", "parsing.quote.escape": "'\\%s' lea boastu ordnet cealkagis", "parsing.quote.expected.end": "Loahppamearka celkagis váilu", "parsing.quote.expected.start": "Meroštii álgomearkka álggahit cealkaga", "particle.invalidOptions": "Ii máhte dulkot partihkkalválljejumi: %s", "particle.notFound": "Amas partihkkal: %s", "permissions.requires.entity": "Entitia adno <PERSON> dán kommándo dás", "permissions.requires.player": "<PERSON><PERSON><PERSON><PERSON><PERSON> adno <PERSON> dán kommándo dás", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Go adno:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Amas mearr<PERSON>dus:%s", "quickplay.error.invalid_identifier": "Ii gávdnan máilmmi identifikašuvnnain mii lea addon", "quickplay.error.realm_connect": "Ii sáhttán čatnašuvvot realmii", "quickplay.error.realm_permission": "Ii leat dohkkehus searvat dán <PERSON>", "quickplay.error.title": "<PERSON><PERSON> s<PERSON> ii lihkustuvvon", "realms.configuration.region.australia_east": "New South Wales, Austrália", "realms.configuration.region.australia_southeast": "Victoria, Austrália", "realms.configuration.region.brazil_south": "Brasilia", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "Davvi Carolina, USA", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "Nuorta Japan", "realms.configuration.region.japan_west": "<PERSON>arje <PERSON>", "realms.configuration.region.korea_central": "Mátta Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Ruoŧŧa", "realms.configuration.region.uae_north": "<PERSON>vtta<PERSON><PERSON><PERSON><PERSON> (UAE)", "realms.configuration.region.uk_south": "<PERSON><PERSON><PERSON>", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Realm eaiggáda báiki)", "realms.configuration.region_preference.automatic_player": "Automáhtalaš (vuosttaš gii searvá)", "realms.missing.snapshot.error.text": "Realms ii doalahuvvo snapshottaiguin vuos", "recipe.notFound": "<PERSON><PERSON>: %s", "recipe.toast.description": "<PERSON><PERSON>", "recipe.toast.title": "Ođđa duodjeneavvagat!", "record.nowPlaying": "<PERSON><PERSON><PERSON>: %s", "recover_world.bug_tracker": "<PERSON><PERSON><PERSON>va", "recover_world.button": "Geahččal divvut", "recover_world.done.failed": "Ovddit dási divvun ii lihkostuvvan.", "recover_world.done.success": "Divvun lihkostuvai!", "recover_world.done.title": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>i", "recover_world.issue.missing_file": "<PERSON><PERSON> v<PERSON>", "recover_world.issue.none": "<PERSON><PERSON>", "recover_world.message": "Čuov<PERSON>vaš váttisvuođat dáhpáhuvve go geahččalii lohkat máilmmemáhpa \"%s\".\nSáhttá leat vejolaš máhcahit máilmmi boarráset dilis dahje sáhtát dieđihit dán váttisvuođa meattáhusguorahallamii.", "recover_world.no_fallback": "Dilli mas vižžojuvvošii ii leat olámuttos", "recover_world.restore": "Geahččal divvut", "recover_world.restoring": "Geahččaleamin divvut máilmmi...", "recover_world.state_entry": "Dilálašvuohta %s: ", "recover_world.state_entry.unknown": "do<PERSON><PERSON><PERSON><PERSON><PERSON>", "recover_world.title": "<PERSON>i s<PERSON> rahpat m<PERSON>mmi", "recover_world.warning": "Ii sáht<PERSON>án rahpat máilmmi <PERSON>kkáigeasu", "resourcePack.broken_assets": "BILLAŠUVVAN RESURSADIEHTOINDEKSA GÁVDNON", "resourcePack.high_contrast.name": "<PERSON><PERSON> k<PERSON>", "resourcePack.load_fail": "Resursa o<PERSON>mahtti<PERSON> ii lihkostuvvan", "resourcePack.programmer_art.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resourcePack.runtime_failure": "Resursapáhkkameattáhusa fuo<PERSON>vvui", "resourcePack.server.name": "<PERSON><PERSON>", "resourcePack.title": "<PERSON><PERSON><PERSON><PERSON>", "resourcePack.vanilla.description": "Minecrafta vuđ<PERSON>š hápmi ja dovdu", "resourcePack.vanilla.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resourcepack.downloading": "Viežžamin resursapáhka", "resourcepack.progress": "Viežžamin fiilla (%s MB)...", "resourcepack.requesting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "screenshot.failure": "Ii sáhttán vurket šearbmagova namain: %s", "screenshot.success": "Vurken šearbmagova namain %s", "selectServer.add": "<PERSON><PERSON>", "selectServer.defaultName": "Minecraft Server", "selectServer.delete": "Sihko", "selectServer.deleteButton": "Sihko", "selectServer.deleteQuestion": "Leat go sihkar ahte áiggut sihkkut á<PERSON>bas eret servera?", "selectServer.deleteWarning": "'%s' jávk<PERSON> agibeaivái! (Oba guhk<PERSON>!)", "selectServer.direct": "<PERSON><PERSON><PERSON>", "selectServer.edit": "Rievdat", "selectServer.hiddenAddress": "(<PERSON>ih<PERSON>)", "selectServer.refresh": "Ođasmah<PERSON>", "selectServer.select": "Mana serverii", "selectWorld.access_failure": "<PERSON>i s<PERSON> rahpat m<PERSON>mmi", "selectWorld.allowCommands": "Verrošankodat", "selectWorld.allowCommands.info": "Kommándot nu movt /gamemode, /experience", "selectWorld.allowCommands.new": "Geavat kommándoid", "selectWorld.backupEraseCache": "<PERSON><PERSON><PERSON> cache<PERSON> data", "selectWorld.backupJoinConfirmButton": "Ráhkat Liige Vuorkká ja Raba", "selectWorld.backupJoinSkipButton": "Dieđan maid lean bargamin!", "selectWorld.backupQuestion.customized": "Heivehuvvon máilmmit eai šat doalahuvvo", "selectWorld.backupQuestion.downgrade": "<PERSON><PERSON><PERSON> máilmmemálle vuolit mállii ii dorjojuvvo", "selectWorld.backupQuestion.experimental": "Máilmmit mat geavahit eksper<PERSON><PERSON><PERSON> he<PERSON> eai do<PERSON>huvvo", "selectWorld.backupQuestion.snapshot": "Háliidatgo duođas rahpat dán máilmmi?", "selectWorld.backupWarning.customized": "<PERSON><PERSON> eat doalat heivehuvvon máilmmiid dán <PERSON>craft veršuvnnas. Mii sáhttit ain rahpat dán máilmmi ja doallat buot nu movt lei, muhto buot ođđa eana mii hábmejuvvo ii leat šat heivehuvvon. Ándagassi go ferte leat dán láhkai!", "selectWorld.backupWarning.downgrade": "<PERSON><PERSON><PERSON> máil<PERSON>mi spellojuvvui gieskat %s ver<PERSON><PERSON><PERSON><PERSON><PERSON>; don leat d<PERSON>l %s ver<PERSON><PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON> vuolid<PERSON> máilmm<PERSON>, de soaitá máilbmi billašuvvat ja mii eat sáhte lohpidit ahte dat šat rahpasa dahje obage doaibmá. <PERSON><PERSON> h<PERSON> speall<PERSON>, de ráhkat fal liige vuorkká!", "selectWorld.backupWarning.experimental": "<PERSON><PERSON><PERSON> máil<PERSON>mi geavaha eksper<PERSON><PERSON><PERSON> he<PERSON> mat soitet heaitit doaibmamis vaikko goas. <PERSON><PERSON> eat sáhte duođaštit ahte máilbmi rahpasa dahje doaibmá. Sáhttá issoras!", "selectWorld.backupWarning.snapshot": "<PERSON><PERSON><PERSON> máilb<PERSON> ii leat rahppon %s veršuvdna ráje<PERSON>; dál<PERSON> veršuvdna lea %s. Gánneha ráhkadit liige vuorkká sihkarvuođa dihte!", "selectWorld.bonusItems": "Liige gii<PERSON>", "selectWorld.cheats": "Verrošankodat", "selectWorld.commands": "Kommándot", "selectWorld.conversion": "<PERSON>rte heivehuvvot!", "selectWorld.conversion.tooltip": "<PERSON><PERSON><PERSON> m<PERSON><PERSON>mi ferte rahppot boarr<PERSON><PERSON> ver<PERSON><PERSON><PERSON><PERSON><PERSON> (omd. 1.6.4) jus galg<PERSON> sih<PERSON><PERSON><PERSON><PERSON><PERSON> ođasmuvvat", "selectWorld.create": "<PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON>", "selectWorld.customizeType": "<PERSON><PERSON><PERSON>", "selectWorld.dataPacks": "Diehtopáhkat", "selectWorld.data_read": "<PERSON><PERSON><PERSON><PERSON> m<PERSON><PERSON> die<PERSON>...", "selectWorld.delete": "<PERSON><PERSON><PERSON>", "selectWorld.deleteButton": "<PERSON><PERSON><PERSON>", "selectWorld.deleteQuestion": "Leat go sihkar ahte á<PERSON>gut sihkkut <PERSON>?", "selectWorld.deleteWarning": "'%s' jávk<PERSON> agibeaivái! (Oba guhk<PERSON>!)", "selectWorld.delete_failure": "<PERSON>i s<PERSON> si<PERSON>t m<PERSON>i", "selectWorld.edit": "Rievdat", "selectWorld.edit.backup": "Ráhkat liige vuorkká", "selectWorld.edit.backupCreated": "Vurkejuvvon: %s", "selectWorld.edit.backupFailed": "Liige vurkemuš ii čađahuvvon", "selectWorld.edit.backupFolder": "Raba liige vuorkkáid gihppaga", "selectWorld.edit.backupSize": "sturrodat: %s MB", "selectWorld.edit.export_worldgen_settings": "Eksportere Máilmmehábmen <PERSON>", "selectWorld.edit.export_worldgen_settings.failure": "Eksporteren ii lihkostuvvan", "selectWorld.edit.export_worldgen_settings.success": "Eksporterejuvvon", "selectWorld.edit.openFolder": "<PERSON><PERSON> m<PERSON><PERSON> g<PERSON>", "selectWorld.edit.optimize": "Optimalisere máilmmi", "selectWorld.edit.resetIcon": "Máhcat <PERSON>kona", "selectWorld.edit.save": "<PERSON><PERSON><PERSON>", "selectWorld.edit.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.enterName": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.enterSeed": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>addji <PERSON>", "selectWorld.experimental": "Eksperimentála", "selectWorld.experimental.details": "<PERSON><PERSON><PERSON>", "selectWorld.experimental.details.entry": "Gáibádusat eksperimentála iešvuođaide: %s", "selectWorld.experimental.details.title": "Eksperimentála iešvuođaid gáibádusat", "selectWorld.experimental.message": "Várut fal!\nDát válljejupmi gáibida iešvuođaid maiguin mii leat ain bargamin. Máilbmi sáhttá giddejuv<PERSON>t, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da<PERSON><PERSON> oal<PERSON>t heaitit do<PERSON> boahtteá<PERSON>.", "selectWorld.experimental.title": "Eksperimentála iešvuođaid váruhus", "selectWorld.experiments": "Eksperimeanttat", "selectWorld.experiments.info": "Eksperimeanttat leat ođđa sisdoalut ja iešvuođat mat sáhttet boahtit speallui. <PERSON><PERSON><PERSON><PERSON> fal, danne go diŋggat sáhttet billašuvvat. Eksperimeanttaid ii sáhte šat jáddadit go máilbmi lea ráhkaduvvan.", "selectWorld.futureworld.error.text": "Boasttuvuohta iđii rahpad<PERSON>in máilmmi ođđ<PERSON><PERSON> ver<PERSON>. Dát lei juo eahpesihkaris dahkku <PERSON>go<PERSON>, <PERSON><PERSON><PERSON><PERSON> go ii do<PERSON><PERSON><PERSON>.", "selectWorld.futureworld.error.title": "Boasttuvuohta iđii!", "selectWorld.gameMode": "Speallanhápmi", "selectWorld.gameMode.adventure": "Fear<PERSON>", "selectWorld.gameMode.adventure.info": "<PERSON><PERSON><PERSON><PERSON>, muh<PERSON> blo<PERSON> ii sáhte bidjat dahje cuvket.", "selectWorld.gameMode.adventure.line1": "Seamma go ceavzin, muhto it sáhte", "selectWorld.gameMode.adventure.line2": "<PERSON><PERSON><PERSON> dahje sir<PERSON> b<PERSON>d", "selectWorld.gameMode.creative": "Hutkás", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ja su<PERSON><PERSON><PERSON>a rá<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON> girdit, gea<PERSON><PERSON> nohkameahttun materi<PERSON>laid ja stálut eai báv<PERSON>čaga<PERSON>te du.", "selectWorld.gameMode.creative.line1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> resurssat, girdde mi<PERSON> ja", "selectWorld.gameMode.creative.line2": "rokka o<PERSON><PERSON><PERSON><PERSON> blo<PERSON>d", "selectWorld.gameMode.hardcore": "Hardcore", "selectWorld.gameMode.hardcore.info": "Ceavzinspeallanhápmi mii lea lohkkaduvvon \"váttes\" váttisvuhtii. Don it fas ealás jus já<PERSON>.", "selectWorld.gameMode.hardcore.line1": "Seammalágan go ceav<PERSON> vuohki, muhto lohkadu<PERSON>von", "selectWorld.gameMode.hardcore.line2": "v<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>, ja du<PERSON><PERSON><PERSON> oktii <PERSON>t", "selectWorld.gameMode.spectator": "Geahčči", "selectWorld.gameMode.spectator.info": "Sáht<PERSON>t geahččat, muhto it guoskat.", "selectWorld.gameMode.spectator.line1": "Sáhtát geahččat muhto it guoskat", "selectWorld.gameMode.survival": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.survival.info": "Suokkar<PERSON>a ima<PERSON><PERSON><PERSON> m<PERSON>mmi gos s<PERSON><PERSON> h<PERSON>, <PERSON><PERSON><PERSON><PERSON>, dudd<PERSON><PERSON> ja vel d<PERSON><PERSON> st<PERSON><PERSON>.", "selectWorld.gameMode.survival.line1": "<PERSON><PERSON> resurs<PERSON>d, du<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.survival.line2": "<PERSON><PERSON><PERSON><PERSON>, heagga ja n<PERSON>gi", "selectWorld.gameRules": "Speallannjuolggadusat", "selectWorld.import_worldgen_settings": "Vieč<PERSON><PERSON>", "selectWorld.import_worldgen_settings.failure": "Boasttuvuoh<PERSON> he<PERSON> v<PERSON>", "selectWorld.import_worldgen_settings.select_file": "<PERSON><PERSON><PERSON><PERSON> (.json)", "selectWorld.incompatible.description": "<PERSON><PERSON><PERSON> ii sáhte rahpasit dán veršuvnnas\nDat spellojuvvui maŋemušat dán veršuvnnas: %s.", "selectWorld.incompatible.info": "Veršuvdna ii ovttastuvo: %s", "selectWorld.incompatible.title": "Veršuvdna ii ovttastuvo", "selectWorld.incompatible.tooltip": "<PERSON><PERSON><PERSON> m<PERSON><PERSON><PERSON> ii sáhte rahpasit go dat ráhkaduvvui veršuvnnas mii ii ovttastuvo d<PERSON>inn<PERSON>.", "selectWorld.incompatible_series": "Ráhkaduvvon veršuvnnas mii ii ovttastuvvo", "selectWorld.load_folder_access": "Ii sáhte lohkat dahje rahpat gihppaga gos spillá máilmmit leat vurkejuvvon!", "selectWorld.loading_list": "Viežžamin m<PERSON>tu", "selectWorld.locked": "Lohkaduvvon eará rahppon Minecraft-spealu geažil", "selectWorld.mapFeatures": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON><PERSON>, fanasbázahusat jna.", "selectWorld.mapType": "Máilbmemá<PERSON>", "selectWorld.mapType.normal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.moreWorldOptions": "<PERSON><PERSON><PERSON> m<PERSON>...", "selectWorld.newWorld": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.recreate": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.recreate.customized.text": "Heivehuvvon máilmmit eai šat dorjojuvvo dán Minecraft veršuvnnas. <PERSON>i sáhttit geahč<PERSON>alit ođđasit ráhkadit seammalágan vuođđudanloguin ja heivehus<PERSON>, muhto buot ovddit heivehusat eai šat leat heivehuvvon. Ándagassi go ferte leat dán láhkai!", "selectWorld.recreate.customized.title": "Heivehuvvon máilmmit eai šat doalahuvvo", "selectWorld.recreate.error.text": "Boasttuvuohta iđii go geahččalii ođđa m<PERSON>mmi r<PERSON>.", "selectWorld.recreate.error.title": "Boasttuvuohta iđii!", "selectWorld.resource_load": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> resurssaid...", "selectWorld.resultFolder": "Vurkejuvvo:", "selectWorld.search": "oza m<PERSON>d", "selectWorld.seedInfo": "<PERSON><PERSON>tte guorosin jus siđat summalis vuođđudanlogu", "selectWorld.select": "Speala Válljejuvvon M<PERSON>", "selectWorld.targetFolder": "Vurkenbáiki: %s", "selectWorld.title": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.tooltip.fromNewerVersion1": "Máilbmi lei vurkejuvvon ođđ<PERSON>ut veršuvdnii,", "selectWorld.tooltip.fromNewerVersion2": "jus rabat de sáhttet ihttit váttisvuodat!", "selectWorld.tooltip.snapshot1": "Ale vajálduhtte ráhkadit liige vuorkká dán m<PERSON>mmis", "selectWorld.tooltip.snapshot2": "ovdal go rabat snapshottas.", "selectWorld.unable_to_load": "Ii sáhte viežžat máilmmi", "selectWorld.version": "Veršuvdna:", "selectWorld.versionJoinButton": "Raba Li<PERSON>kk<PERSON>", "selectWorld.versionQuestion": "Áiggutgo duo<PERSON><PERSON> rah<PERSON>t dán m<PERSON>?", "selectWorld.versionUnknown": "do<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.versionWarning": "<PERSON><PERSON><PERSON> m<PERSON> ii leat rahppon %s veršuvdna rájes. <PERSON>s rabat dan dálá veršuvdn<PERSON>, dat soaitá mannat bill<PERSON>!", "selectWorld.warning.deprecated.question": "So<PERSON>s ieš<PERSON>o<PERSON>at leat he<PERSON>, ja dan gea<PERSON>il heitet doaibma<PERSON> boahtteáiggis. Háliidatgo joatkit?", "selectWorld.warning.deprecated.title": "Váruhus! Heivehusat geavahit heaittihuvvon iešvuođaid", "selectWorld.warning.experimental.question": "<PERSON><PERSON>t heivehusat leat eksperimentála ja soitet heaitit do<PERSON>b<PERSON> boahtteáiggis. Háliidatgo joatkit?", "selectWorld.warning.experimental.title": "Váruhus! Heivehusat geavahit eksperimentála iešvuođaid", "selectWorld.warning.lowDiskSpace.description": "<PERSON> s<PERSON>all<PERSON>rusttegis lea unnán vurkensadji.\n<PERSON><PERSON> sad<PERSON>, de s<PERSON><PERSON><PERSON><PERSON> máilbmi bullašuvvat.", "selectWorld.warning.lowDiskSpace.title": "Várut! Unnán vurkensadji!", "selectWorld.world": "<PERSON><PERSON><PERSON><PERSON>", "sign.edit": "<PERSON>iev<PERSON><PERSON> die<PERSON>u", "sleep.not_possible": "Dát idja ii gola vaikko man olu vuoinnastivččet", "sleep.players_sleeping": "%s/%s <PERSON>iin o<PERSON>", "sleep.skipping_night": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slot.only_single_allowed": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> sajit do<PERSON>v<PERSON>, oa<PERSON><PERSON>ui '%s'", "slot.unknown": "Amas sajádat '%s'", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "Biras", "soundCategory.block": "Blohkat", "soundCategory.hostile": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "soundCategory.master": "Váldojietna", "soundCategory.music": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.neutral": "Siivos eallit", "soundCategory.player": "Speallit", "soundCategory.record": "Čuojanas/Nuoht<PERSON>lohkka", "soundCategory.ui": "UI", "soundCategory.voice": "Hupman/H<PERSON>llan", "soundCategory.weather": "<PERSON><PERSON><PERSON><PERSON>", "spectatorMenu.close": "Gidde válljema", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON>", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON><PERSON> siidui", "spectatorMenu.root.prompt": "<PERSON><PERSON><PERSON> boalu vuoi kom<PERSON> v<PERSON>, ja geavat vuohon.", "spectatorMenu.team_teleport": "<PERSON><PERSON><PERSON>", "spectatorMenu.team_teleport.prompt": "<PERSON><PERSON><PERSON><PERSON> joa<PERSON>ku masa sir<PERSON>", "spectatorMenu.teleport": "<PERSON><PERSON><PERSON> spealli lusa", "spectatorMenu.teleport.prompt": "<PERSON><PERSON><PERSON><PERSON> spealli geasa sirdi<PERSON>", "stat.generalButton": "<PERSON><PERSON><PERSON><PERSON>", "stat.itemsButton": "Dávvirat", "stat.minecraft.animals_bred": "<PERSON><PERSON><PERSON>", "stat.minecraft.aviate_one_cm": "Gaska girdán <PERSON>", "stat.minecraft.bell_ring": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.boat_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.clean_armor": "<PERSON><PERSON><PERSON><PERSON><PERSON> bassan", "stat.minecraft.clean_banner": "<PERSON><PERSON><PERSON><PERSON> doid<PERSON>", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> doid<PERSON>", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON> go<PERSON>", "stat.minecraft.crouch_one_cm": "Gaska guvdŋán", "stat.minecraft.damage_absorbed": "Vahága gillán", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON><PERSON><PERSON> vuo<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.damage_dealt": "Vahágahttán", "stat.minecraft.damage_dealt_absorbed": "Vah<PERSON><PERSON><PERSON> (gillán)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (gierddahan)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.damage_taken": "Vahága gillán", "stat.minecraft.deaths": "<PERSON><PERSON> <PERSON><PERSON>", "stat.minecraft.drop": "Dávviriid luo<PERSON>", "stat.minecraft.eat_cake_slice": "Gáhkkobihttáid borran", "stat.minecraft.enchant_item": "Dávviriid noidon", "stat.minecraft.fall_one_cm": "Gaska gahččan", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.fish_caught": "<PERSON><PERSON><PERSON>", "stat.minecraft.fly_one_cm": "Gaska girdán", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "Gaska heasttain", "stat.minecraft.inspect_dispenser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> iskan", "stat.minecraft.inspect_dropper": "<PERSON><PERSON><PERSON><PERSON> iskan", "stat.minecraft.inspect_hopper": "<PERSON><PERSON><PERSON><PERSON><PERSON> iskan", "stat.minecraft.interact_with_anvil": "Ávkkástallan stáđi", "stat.minecraft.interact_with_beacon": "Ávkkástallan várddočuovgga", "stat.minecraft.interact_with_blast_furnace": "Ávkkástallan suddadanu<PERSON>", "stat.minecraft.interact_with_brewingstand": "Ávkkástallan vuoššahaga", "stat.minecraft.interact_with_campfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_cartography_table": "Ávkkástallan k<PERSON>rtenbeavddi", "stat.minecraft.interact_with_crafting_table": "Ávkkástallan duodjebeavddi", "stat.minecraft.interact_with_furnace": "Ávkkástallan ovnna", "stat.minecraft.interact_with_grindstone": "Ávkkástallan geađgeluddejeaddji", "stat.minecraft.interact_with_lectern": "Ávkkástallan gir<PERSON>avddi", "stat.minecraft.interact_with_loom": "Ávkkástallan gákkesmuoraid", "stat.minecraft.interact_with_smithing_table": "Ávkkástallan rávdebádjebeavddi", "stat.minecraft.interact_with_smoker": "Ávkkás<PERSON><PERSON> su<PERSON>", "stat.minecraft.interact_with_stonecutter": "Ávkkástallan geađgečuohppi", "stat.minecraft.jump": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.leave_game": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "stat.minecraft.minecart_one_cm": "Gaska roggangielk<PERSON>in", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_barrel": "Fárppaliid rahpan", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rahpan", "stat.minecraft.pig_one_cm": "Gaska s<PERSON>n", "stat.minecraft.play_noteblock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.play_record": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.play_time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.pot_flower": "Ruotnasiid g<PERSON>", "stat.minecraft.raid_trigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.raid_win": "Rievidemiid vuoit<PERSON>", "stat.minecraft.sleep_in_bed": "<PERSON><PERSON> <PERSON><PERSON>e oa<PERSON><PERSON><PERSON> sea<PERSON>", "stat.minecraft.sneak_time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.sprint_one_cm": "Gaska viehkan", "stat.minecraft.strider_one_cm": "Gaska gálašeaddji<PERSON>", "stat.minecraft.swim_one_cm": "Gaska vuodjan", "stat.minecraft.talked_to_villager": "<PERSON><PERSON><PERSON>", "stat.minecraft.target_hit": "Deivvosat deivon", "stat.minecraft.time_since_death": "<PERSON><PERSON> o<PERSON> du<PERSON> rajes", "stat.minecraft.time_since_rest": "<PERSON><PERSON> ovddit vuo<PERSON><PERSON><PERSON><PERSON> rajes", "stat.minecraft.total_world_time": "<PERSON><PERSON>", "stat.minecraft.traded_with_villager": "Gávppa<PERSON><PERSON> g<PERSON>", "stat.minecraft.trigger_trapped_chest": "Darvvihahttingiis<PERSON>id g<PERSON>", "stat.minecraft.tune_noteblock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.use_cauldron": "<PERSON><PERSON><PERSON> go<PERSON> ruittus", "stat.minecraft.walk_on_water_one_cm": "Gaska vázzán čázi alde", "stat.minecraft.walk_one_cm": "Gaska vázzán", "stat.minecraft.walk_under_water_one_cm": "Gaska vázzán čázevuolde", "stat.mobsButton": "Eallit", "stat_type.minecraft.broken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.crafted": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.dropped": "Luoitán", "stat_type.minecraft.killed": "Leat goddán %s %s ealli", "stat_type.minecraft.killed.none": "Don it leat goassege goddán %s", "stat_type.minecraft.killed_by": "%s lea godd<PERSON> du %s <PERSON>dde", "stat_type.minecraft.killed_by.none": "It leat goassege goddojuvvon %s eallis", "stat_type.minecraft.mined": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.picked_up": "Čoaggán", "stat_type.minecraft.used": "<PERSON><PERSON><PERSON><PERSON>", "stats.none": "-", "structure_block.button.detect_size": "ISKKA", "structure_block.button.load": "VIEČČA", "structure_block.button.save": "VURKE", "structure_block.custom_data": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "structure_block.detect_size": "<PERSON><PERSON> h<PERSON>a stu<PERSON> ja saj<PERSON>daga:", "structure_block.hover.corner": "Čiehka: %s", "structure_block.hover.data": "Diehtu: %s", "structure_block.hover.load": "Viečča: %s", "structure_block.hover.save": "Vurke: %s", "structure_block.include_entities": "<PERSON><PERSON><PERSON><PERSON>s entitiaid:", "structure_block.integrity": "Huksehusa integritehta ja vuođđudanlohku", "structure_block.integrity.integrity": "Huksehusa Integritehta", "structure_block.integrity.seed": "Huksehusa vuođđudanlohku", "structure_block.invalid_structure_name": "<PERSON><PERSON><PERSON> h<PERSON> '%s'", "structure_block.load_not_found": "<PERSON><PERSON><PERSON>us '%s' ii leat olámuttos", "structure_block.load_prepare": "%s huk<PERSON><PERSON><PERSON> lea r<PERSON>kan<PERSON>vvon", "structure_block.load_success": "Huksehus vižžon '%s'", "structure_block.mode.corner": "Čiehka", "structure_block.mode.data": "Die<PERSON><PERSON>", "structure_block.mode.load": "Viečča", "structure_block.mode.save": "<PERSON><PERSON><PERSON>", "structure_block.mode_info.corner": "Čiehkamodus - á<PERSON><PERSON>mi ja sturrodat merkejeaddji", "structure_block.mode_info.data": "Diehtomodus - speala logihka merkejeaddji", "structure_block.mode_info.load": "Viežžanmodus - viežž<PERSON> fiillas", "structure_block.mode_info.save": "Vurkenmodus - čállin filii", "structure_block.position": "<PERSON><PERSON><PERSON><PERSON>", "structure_block.position.x": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>t x", "structure_block.position.y": "<PERSON><PERSON><PERSON><PERSON><PERSON> y", "structure_block.position.z": "go<PERSON><PERSON><PERSON>š sajádat z", "structure_block.save_failure": "Ii sáhte vurket '%s' huk<PERSON>husa", "structure_block.save_success": "<PERSON><PERSON><PERSON>us vurkejuvvon '%s'", "structure_block.show_air": "Čájet čađačuovgi blohkaid:", "structure_block.show_boundingbox": "Čájet birasčuvgga:", "structure_block.size": "Huksehusa Sturrodat", "structure_block.size.x": "huk<PERSON><PERSON><PERSON> sturrod<PERSON> x", "structure_block.size.y": "huk<PERSON><PERSON><PERSON> sturrod<PERSON> y", "structure_block.size.z": "huk<PERSON><PERSON>a sturrodat z", "structure_block.size_failure": "<PERSON>i sahte iskkat hukse<PERSON>a sturrodat. <PERSON><PERSON> mas seammalagan hukse<PERSON>", "structure_block.size_success": "Sturrodatiskkus lihkustuvvui '%s' huksehussii", "structure_block.strict": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:", "structure_block.structure_name": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.ambient.cave": "<PERSON><PERSON> jietna", "subtitles.ambient.sound": "<PERSON><PERSON><PERSON> jietna", "subtitles.block.amethyst_block.chime": "Ametista skillá", "subtitles.block.amethyst_block.resonate": "Ámetista doarggista", "subtitles.block.anvil.destroy": "St<PERSON><PERSON><PERSON>d cuvken", "subtitles.block.anvil.land": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.anvil.use": "<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>i", "subtitles.block.barrel.close": "<PERSON><PERSON><PERSON><PERSON> giddejuvvo", "subtitles.block.barrel.open": "<PERSON><PERSON><PERSON><PERSON> rahppo", "subtitles.block.beacon.activate": "Várdočuovga álggahuvvo", "subtitles.block.beacon.ambient": "Várdočuovga šurr<PERSON>", "subtitles.block.beacon.deactivate": "Várdočuovga jáddaduvvo", "subtitles.block.beacon.power_select": "Várdočuovgga fápmu válljejuvvo", "subtitles.block.beehive.drip": "Honnet golgá", "subtitles.block.beehive.enter": "<PERSON><PERSON><PERSON> manna <PERSON>", "subtitles.block.beehive.exit": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.beehive.shear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.beehive.work": "Uvllut barget", "subtitles.block.bell.resonate": "Biellu čuojahasta", "subtitles.block.bell.use": "Biellu čuodjá", "subtitles.block.big_dripleaf.tilt_down": "Goaikolasta loažžá", "subtitles.block.big_dripleaf.tilt_up": "Goaikolasta njuolgá", "subtitles.block.blastfurnace.fire_crackle": "Suddadanuvdna ruohčá", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON><PERSON><PERSON> vuošš<PERSON>", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON> golg<PERSON>", "subtitles.block.bubble_column.upwards_inside": "Bulljarasat šuvket", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON><PERSON><PERSON> jorret", "subtitles.block.bubble_column.whirlpool_inside": "Bulljarasat š<PERSON>it", "subtitles.block.button.click": "<PERSON><PERSON><PERSON>", "subtitles.block.cake.add_candle": "Gáhkku steažžasa", "subtitles.block.campfire.crackle": "<PERSON><PERSON><PERSON><PERSON><PERSON> buoll<PERSON>", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON> buoll<PERSON>", "subtitles.block.candle.extinguish": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.close": "Giisá giddejuvvo", "subtitles.block.chest.locked": "Giisá lohkaduvvo", "subtitles.block.chest.open": "G<PERSON>sá rahpasa", "subtitles.block.chorus_flower.death": "Chorusšattus goldná", "subtitles.block.chorus_flower.grow": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.comparator.click": "Iskkadeaddji is<PERSON>á", "subtitles.block.composter.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.composter.fill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.composter.ready": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.conduit.activate": "Mearrameannudeaddji <PERSON>", "subtitles.block.conduit.ambient": "Mearrameannudeaddji <PERSON>ahkká", "subtitles.block.conduit.attack.target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.conduit.deactivate": "Mearrameannude<PERSON><PERSON><PERSON>", "subtitles.block.copper_bulb.turn_off": "Veaikečuovga j<PERSON>ddá", "subtitles.block.copper_bulb.turn_on": "Veaikečuovga čuovgagoahtá", "subtitles.block.copper_trapdoor.close": "Luŋká giddana", "subtitles.block.copper_trapdoor.open": "Luŋká rahpasa", "subtitles.block.crafter.craft": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.crafter.fail": "Duojár ii lihkostuvvan", "subtitles.block.creaking_heart.hurt": "Gihčama váibmu čierru", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON> jietna", "subtitles.block.creaking_heart.spawn": "Gihčama váibmu lihká", "subtitles.block.deadbush.idle": "Goikejienat", "subtitles.block.decorated_pot.insert": "<PERSON><PERSON><PERSON><PERSON><PERSON> ruhkka dievv<PERSON>", "subtitles.block.decorated_pot.insert_fail": "<PERSON><PERSON><PERSON><PERSON><PERSON> ruhkka he<PERSON>u", "subtitles.block.decorated_pot.shatter": "Kruhkku cuov<PERSON>a", "subtitles.block.dispenser.dispense": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON><PERSON>tead<PERSON><PERSON> ii b<PERSON><PERSON><PERSON>n", "subtitles.block.door.toggle": "<PERSON>ks<PERSON>", "subtitles.block.dried_ghast.ambient": "<PERSON><PERSON><PERSON> ji<PERSON>t", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON><PERSON> luvva<PERSON>", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON> buori millii", "subtitles.block.dry_grass.ambient": "Biekkat biekkastit", "subtitles.block.enchantment_table.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> adnon", "subtitles.block.end_portal.spawn": "Endorportála rah<PERSON>a", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.eyeblossom.close": "Čalbmelieđ<PERSON><PERSON> giddan<PERSON>", "subtitles.block.eyeblossom.idle": "Čalbmelieđđi sav<PERSON>a", "subtitles.block.eyeblossom.open": "Čalbmelieđ<PERSON><PERSON> rah<PERSON>", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.fire.ambient": "Dolla čuotnamastá", "subtitles.block.fire.extinguish": "Dolla čáskadii", "subtitles.block.firefly_bush.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.frogspawn.hatch": "Šlubboaivi riegáda", "subtitles.block.furnace.fire_crackle": "Uvdna čuotnamastá", "subtitles.block.generic.break": "<PERSON><PERSON><PERSON><PERSON>a", "subtitles.block.generic.fall": "Juoga gahča blohka ala", "subtitles.block.generic.footsteps": "Lihkastat", "subtitles.block.generic.hit": "Blohkka cuvkejuvvo", "subtitles.block.generic.place": "<PERSON><PERSON><PERSON><PERSON> bidjo", "subtitles.block.grindstone.use": "Sadjingeđ<PERSON><PERSON><PERSON> sadj<PERSON>", "subtitles.block.growing_plant.crop": "<PERSON><PERSON><PERSON>", "subtitles.block.hanging_sign.waxed_interact_fail": "Galba sparaida", "subtitles.block.honey_block.slide": "Čierasteamin vulos honnetblohka", "subtitles.block.iron_trapdoor.close": "Luŋká giddana", "subtitles.block.iron_trapdoor.open": "Luŋká rahpasa", "subtitles.block.lava.ambient": "<PERSON>va <PERSON>", "subtitles.block.lava.extinguish": "<PERSON><PERSON> ru<PERSON>", "subtitles.block.lever.click": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.block.note_block.note": "Nuohttablohkka <PERSON>dj<PERSON>", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON> ji<PERSON>", "subtitles.block.piston.move": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON>va goaiku", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON>va goaiku ruittu sisa", "subtitles.block.pointed_dripstone.drip_water": "Čáhci goaiku", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Čáhci goaiku ruittu sisa", "subtitles.block.pointed_dripstone.land": "Stalakihtta gahččá", "subtitles.block.portal.ambient": "Port<PERSON>la <PERSON>", "subtitles.block.portal.travel": "<PERSON><PERSON><PERSON> jáv<PERSON>", "subtitles.block.portal.trigger": "<PERSON><PERSON><PERSON> allu", "subtitles.block.pressure_plate.click": "Deatt<PERSON><PERSON><PERSON><PERSON><PERSON> co<PERSON>", "subtitles.block.pumpkin.carve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON><PERSON><PERSON> buoll<PERSON>", "subtitles.block.respawn_anchor.ambient": "Port<PERSON>la <PERSON>", "subtitles.block.respawn_anchor.charge": "Ealáskan<PERSON><PERSON><PERSON> devdo", "subtitles.block.respawn_anchor.deplete": "Ealáskanáŋkor luoitá", "subtitles.block.respawn_anchor.set_spawn": "Ealáskanáŋkor bidjá e<PERSON>áik<PERSON>", "subtitles.block.sand.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.sand.wind": "<PERSON><PERSON><PERSON><PERSON> bossu", "subtitles.block.sculk.charge": "Sculk bulljarastá", "subtitles.block.sculk.spread": "Sculk leavvá", "subtitles.block.sculk_catalyst.bloom": "Sculk-ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.sculk_sensor.clicking": "Sculk-do<PERSON><PERSON><PERSON>", "subtitles.block.sculk_sensor.clicking_stop": "Sculk-do<PERSON><PERSON><PERSON> he<PERSON> co<PERSON>k<PERSON>t", "subtitles.block.sculk_shrieker.shriek": "Sculk-riehči riežáda", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> rah<PERSON>o", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON><PERSON>", "subtitles.block.smithing_table.use": "R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "subtitles.block.smoker.smoke": "Suova<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.sniffer_egg.crack": "Suostti monni luoddanišgoahtá", "subtitles.block.sniffer_egg.hatch": "<PERSON><PERSON><PERSON> monni rah<PERSON>a", "subtitles.block.sniffer_egg.plop": "<PERSON><PERSON><PERSON> bid<PERSON>i", "subtitles.block.sponge.absorb": "Guopparas njamista", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.trapdoor.close": "Luŋká gokčojuvvo", "subtitles.block.trapdoor.open": "Luŋká rahpasa", "subtitles.block.trapdoor.toggle": "Luŋká gihččá", "subtitles.block.trial_spawner.about_to_spawn_item": "Bahávu<PERSON>ŋ<PERSON><PERSON><PERSON> dávvir iht<PERSON>", "subtitles.block.trial_spawner.ambient": "Geahččalusealáskahtti čuotnamastá", "subtitles.block.trial_spawner.ambient_charged": "Bahá<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jietna", "subtitles.block.trial_spawner.ambient_ominous": "Bahá<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jietna", "subtitles.block.trial_spawner.charge_activate": "Diida go<PERSON>č<PERSON> geahččalusealáskahtti", "subtitles.block.trial_spawner.close_shutter": "Geahččalusealáskahtti giddana", "subtitles.block.trial_spawner.detect_player": "Geahččalusealáskahtti láddá", "subtitles.block.trial_spawner.eject_item": "Geahččalusealáskahtti bálkesta dávvira", "subtitles.block.trial_spawner.ominous_activate": "Diida deavdá geahččalusealáskahtti", "subtitles.block.trial_spawner.open_shutter": "Geahččalusealáskahtti rahpasa", "subtitles.block.trial_spawner.spawn_item": "Bahá<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dávvir luitojuvvo", "subtitles.block.trial_spawner.spawn_item_begin": "<PERSON><PERSON><PERSON>ŋ<PERSON><PERSON><PERSON> dávvir ihtá", "subtitles.block.trial_spawner.spawn_mob": "<PERSON><PERSON><PERSON>", "subtitles.block.tripwire.attach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.tripwire.click": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> luo<PERSON>", "subtitles.block.vault.activate": "Lohkkadat cáhkkehuvvo", "subtitles.block.vault.ambient": "Lohkkadat čuotnamastá", "subtitles.block.vault.close_shutter": "Lohkka<PERSON>t giddana", "subtitles.block.vault.deactivate": "Lohkkadat čáskada", "subtitles.block.vault.eject_item": "Lohkkadat bálkesta dávvira", "subtitles.block.vault.insert_item": "Lohkkadat čovdojuvvo", "subtitles.block.vault.insert_item_fail": "<PERSON><PERSON><PERSON><PERSON><PERSON> hilgu dávvira", "subtitles.block.vault.open_shutter": "Lohkka<PERSON>t rahpasa", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON><PERSON><PERSON><PERSON> hilgu spealli", "subtitles.block.water.ambient": "Čáhci golgá", "subtitles.block.wet_sponge.dries": "Guopparas goiká", "subtitles.chiseled_bookshelf.insert": "<PERSON><PERSON><PERSON>", "subtitles.chiseled_bookshelf.insert_enchanted": "Noidojuvvon girji <PERSON>", "subtitles.chiseled_bookshelf.take": "<PERSON><PERSON><PERSON>", "subtitles.chiseled_bookshelf.take_enchanted": "Noidojuvvon girji v<PERSON>", "subtitles.enchant.thorns.hit": "<PERSON><PERSON><PERSON> cuoggu", "subtitles.entity.allay.ambient_with_item": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.allay.ambient_without_item": "<PERSON><PERSON><PERSON><PERSON> dá<PERSON>", "subtitles.entity.allay.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.allay.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.allay.item_given": "Háldi giit<PERSON>", "subtitles.entity.allay.item_taken": "<PERSON><PERSON><PERSON><PERSON> add<PERSON> dávvira", "subtitles.entity.allay.item_thrown": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.ambient": "<PERSON>adillo ruovg<PERSON>", "subtitles.entity.armadillo.brush": "Armadilla skálžu guštejuvvo luovos", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.armadillo.hurt": "Armadillo bávččaga", "subtitles.entity.armadillo.hurt_reduced": "Armadillo suodjala <PERSON>", "subtitles.entity.armadillo.land": "Arm<PERSON>llo <PERSON>iv<PERSON>", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.roll": "<PERSON><PERSON><PERSON> gid<PERSON>", "subtitles.entity.armadillo.scute_drop": "Armadillo luoitá skálžžu", "subtitles.entity.armadillo.unroll_finish": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON>", "subtitles.entity.armor_stand.fall": "Juoga gahčai", "subtitles.entity.arrow.hit": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.arrow.hit_player": "Spealli deivui", "subtitles.entity.arrow.shoot": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.axolotl.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON> fall<PERSON>a", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.axolotl.hurt": "Aks<PERSON><PERSON>l bávččaga", "subtitles.entity.axolotl.idle_air": "Aks<PERSON>htal njivká", "subtitles.entity.axolotl.idle_water": "Aks<PERSON>htal njivká", "subtitles.entity.axolotl.splash": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.axolotl.swim": "<PERSON>ks<PERSON><PERSON><PERSON> vuojada", "subtitles.entity.bat.ambient": "G<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bá<PERSON>", "subtitles.entity.bat.takeoff": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> girdila", "subtitles.entity.bee.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.bee.death": "<PERSON><PERSON><PERSON>šš<PERSON>", "subtitles.entity.bee.hurt": "Uvlu bávččaga", "subtitles.entity.bee.loop": "<PERSON><PERSON><PERSON>", "subtitles.entity.bee.loop_aggressive": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bee.pollinate": "Uvlu šurrá buoremielalaččat", "subtitles.entity.bee.sting": "U<PERSON>lu čugge", "subtitles.entity.blaze.ambient": "Buollistállu vuo<PERSON>ná", "subtitles.entity.blaze.burn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gullo", "subtitles.entity.blaze.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.blaze.hurt": "Buollistáll<PERSON> b<PERSON>č<PERSON>", "subtitles.entity.blaze.shoot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bogged.hurt": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.breeze.charge": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.deflect": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.idle_air": "<PERSON><PERSON><PERSON><PERSON> girdá", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.inhale": "<PERSON><PERSON><PERSON><PERSON> vuoigŋ<PERSON>", "subtitles.entity.breeze.jump": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.land": "<PERSON><PERSON><PERSON><PERSON>u", "subtitles.entity.breeze.shoot": "<PERSON><PERSON><PERSON><PERSON> bos<PERSON>a", "subtitles.entity.breeze.slide": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.breeze.whirl": "<PERSON><PERSON><PERSON><PERSON>ke", "subtitles.entity.breeze.wind_burst": "<PERSON><PERSON>gg<PERSON><PERSON><PERSON> bosuha", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON> n<PERSON> g<PERSON>", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.saddle": "Riidens<PERSON><PERSON>", "subtitles.entity.camel.sit": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.stand": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.step_sand": "<PERSON><PERSON><PERSON>", "subtitles.entity.cat.ambient": "Bussá mjávvu", "subtitles.entity.cat.beg_for_food": "Bussá <PERSON>a", "subtitles.entity.cat.death": "Bussá jápmá", "subtitles.entity.cat.eat": "Bussá borrá", "subtitles.entity.cat.hiss": "Buss<PERSON>", "subtitles.entity.cat.hurt": "Bussá bávččaga", "subtitles.entity.cat.purr": "Bussá botná", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.chicken.egg": "<PERSON><PERSON><PERSON><PERSON> luo<PERSON> moni", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.cod.flop": "<PERSON><PERSON><PERSON>", "subtitles.entity.cod.hurt": "Dorski bávččaga", "subtitles.entity.cow.ambient": "<PERSON><PERSON> m<PERSON>", "subtitles.entity.cow.death": "<PERSON><PERSON>", "subtitles.entity.cow.hurt": "<PERSON><PERSON>", "subtitles.entity.cow.milk": "<PERSON><PERSON>", "subtitles.entity.creaking.activate": "G<PERSON>čan g<PERSON>agoahtá", "subtitles.entity.creaking.ambient": "G<PERSON>č<PERSON> gih<PERSON>", "subtitles.entity.creaking.attack": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON><PERSON> he<PERSON> gih<PERSON>", "subtitles.entity.creaking.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.freeze": "Gihčan já<PERSON>k<PERSON>", "subtitles.entity.creaking.spawn": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.sway": "<PERSON><PERSON><PERSON><PERSON> lea sudd<PERSON>", "subtitles.entity.creaking.twitch": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.creaking.unfreeze": "<PERSON><PERSON><PERSON><PERSON> lih<PERSON>", "subtitles.entity.creeper.death": "Creeper j<PERSON>", "subtitles.entity.creeper.hurt": "Creeper bávččaga", "subtitles.entity.creeper.primed": "Creeper šŋjirrá", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.dolphin.hurt": "Del<PERSON><PERSON><PERSON> b<PERSON>ččaga", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.dolphin.swim": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.entity.donkey.ambient": "<PERSON><PERSON>", "subtitles.entity.donkey.angry": "<PERSON><PERSON>", "subtitles.entity.donkey.chest": "Giisá bidjojuvvo ásii", "subtitles.entity.donkey.death": "<PERSON><PERSON>", "subtitles.entity.donkey.eat": "<PERSON><PERSON> b<PERSON>", "subtitles.entity.donkey.hurt": "<PERSON><PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON>", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON><PERSON> bohrai<PERSON>", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON><PERSON> bohrai<PERSON>", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON><PERSON> bálkesta Háŋkku", "subtitles.entity.drowned.step": "Heavvanan vázzá", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON><PERSON> vuodj<PERSON>", "subtitles.entity.egg.throw": "<PERSON><PERSON>", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON><PERSON> fuoiku", "subtitles.entity.elder_guardian.ambient_land": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.elder_guardian.curse": "<PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.flop": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.flap": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON> harra", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_eye.death": "Endorčalbmi gahččá", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.teleport": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.endermite.ambient": "Endormite ciehppá", "subtitles.entity.endermite.death": "Endormite j<PERSON>", "subtitles.entity.endermite.hurt": "Endormite báv<PERSON>aga", "subtitles.entity.evoker.ambient": "Gonstošeaddji buljarda", "subtitles.entity.evoker.cast_spell": "Gonstoš<PERSON><PERSON><PERSON>", "subtitles.entity.evoker.celebrate": "Gonstošeaddji <PERSON>", "subtitles.entity.evoker.death": "Gonstošeaddji jápmá", "subtitles.entity.evoker.hurt": "Gonstošeaddji bávččaga", "subtitles.entity.evoker.prepare_attack": "Gonstošeaddji fallehišgoahta", "subtitles.entity.evoker.prepare_summon": "Gonstošeaddji ealáskahttegoahta", "subtitles.entity.evoker.prepare_wololo": "Gonstošeaddji gonstošitgoahta", "subtitles.entity.evoker_fangs.attack": "Čiehkabániidguin gáskkesta", "subtitles.entity.experience_orb.pickup": "Noaidundássi ánssášu<PERSON>van", "subtitles.entity.firework_rocket.blast": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.firework_rocket.launch": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.firework_rocket.twinkle": "Rahkeahtta ravkkuha", "subtitles.entity.fish.swim": "B<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.fishing_bobber.retrieve": "Njuohpat vuokka", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON>", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>uv<PERSON>", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.ambient": "<PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.fox.bite": "<PERSON><PERSON><PERSON> snelke", "subtitles.entity.fox.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.eat": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.fox.hurt": "<PERSON><PERSON><PERSON> b<PERSON>č<PERSON>", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.sleep": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.sniff": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.spit": "<PERSON><PERSON><PERSON> luoitila dávvira", "subtitles.entity.fox.teleport": "<PERSON><PERSON><PERSON>", "subtitles.entity.frog.ambient": "Rihccecuo<PERSON><PERSON> bunjaida", "subtitles.entity.frog.death": "Rihccecuoppu j<PERSON>", "subtitles.entity.frog.eat": "<PERSON><PERSON><PERSON>ec<PERSON><PERSON><PERSON> borr<PERSON>", "subtitles.entity.frog.hurt": "Rihccecuoppu bávččaga", "subtitles.entity.frog.lay_spawn": "Rihccecuoppu bidj<PERSON> moniid", "subtitles.entity.frog.long_jump": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.generic.big_fall": "Juoga gahčai", "subtitles.entity.generic.burn": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.drink": "Jugesta", "subtitles.entity.generic.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.explode": "Bávkkiheapmi", "subtitles.entity.generic.extinguish_fire": "Dolla čáskada", "subtitles.entity.generic.hurt": "Juoga bávččaga", "subtitles.entity.generic.small_fall": "Juoga mulččiha", "subtitles.entity.generic.splash": "Stunžesta", "subtitles.entity.generic.swim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Bieggadoaro<PERSON>", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.add_item": "Áhcagas dávvirr<PERSON>mma dievvá", "subtitles.entity.glow_item_frame.break": "Áhcagas dávvir<PERSON><PERSON><PERSON> bill<PERSON>", "subtitles.entity.glow_item_frame.place": "Áhcagas dávvir<PERSON><PERSON><PERSON> bidjo", "subtitles.entity.glow_item_frame.remove_item": "Áhcagas dávvirr<PERSON>mma guorrana", "subtitles.entity.glow_item_frame.rotate_item": "Áhcagas dávvirr<PERSON><PERSON> co<PERSON>la", "subtitles.entity.glow_squid.ambient": "Áhcagas áhkárguolli vuojada", "subtitles.entity.glow_squid.death": "Áhcagas á<PERSON>ká<PERSON>lli j<PERSON>", "subtitles.entity.glow_squid.hurt": "Áhcagas áhkárg<PERSON>lli bávččaga", "subtitles.entity.glow_squid.squirt": "Áhcagas áhkárguolli luoitá ivnni", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> me<PERSON>", "subtitles.entity.goat.death": "Gáica <PERSON>", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.goat.horn_break": "Gáicca čoarvi luovvanii", "subtitles.entity.goat.hurt": "Gáica b<PERSON>ččaga", "subtitles.entity.goat.long_jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.goat.prepare_ram": "Gáica steampu", "subtitles.entity.goat.ram_impact": "Gáica <PERSON>", "subtitles.entity.goat.screaming.ambient": "Gáica r<PERSON>č<PERSON>", "subtitles.entity.goat.step": "Gáica duolbmu", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fuoiku", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.hurt": "Várjaleaddji bávččaga", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.equip": "Leaŋggat čadnojuvvet", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON><PERSON><PERSON> lea <PERSON>", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.unequip": "Leaŋggat gálgejuvvet", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> ha<PERSON>", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> harr<PERSON>", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON>hu<PERSON> Zoglinin", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.horse.ambient": "<PERSON>asta niv<PERSON>", "subtitles.entity.horse.angry": "<PERSON>asta niv<PERSON>", "subtitles.entity.horse.armor": "He<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.horse.gallop": "<PERSON><PERSON><PERSON> ruo<PERSON>", "subtitles.entity.horse.hurt": "Heasta bá<PERSON>ččaga", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.saddle": "Sale bidjo badjelii", "subtitles.entity.husk.ambient": "Go<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.husk.converted_to_zombie": "Goikej<PERSON><PERSON>š earáhuvvo jámežin", "subtitles.entity.husk.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.husk.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> buljar<PERSON>", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gea<PERSON>", "subtitles.entity.illusioner.death": "Geaidojeaddji j<PERSON>pm<PERSON>", "subtitles.entity.illusioner.hurt": "Geaidojeaddji bávččaga", "subtitles.entity.illusioner.mirror_move": "Geaidoje<PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.prepare_blindness": "Geaidojeaddji ráhkkanahttá čálmmehisvuođa", "subtitles.entity.illusioner.prepare_mirror": "Geaidoje<PERSON><PERSON><PERSON> r<PERSON> máŋget", "subtitles.entity.iron_golem.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "subtitles.entity.iron_golem.damage": "<PERSON><PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.iron_golem.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.iron_golem.hurt": "Ruovdejiehtanas b<PERSON>", "subtitles.entity.iron_golem.repair": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.item.break": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item.pickup": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.add_item": "Dáv<PERSON><PERSON><PERSON><PERSON> dievvá", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON>v<PERSON><PERSON><PERSON><PERSON> bidjo", "subtitles.entity.item_frame.remove_item": "Dáv<PERSON><PERSON><PERSON><PERSON> guorrana", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> co<PERSON>la", "subtitles.entity.leash_knot.break": "Lávži čoavdása", "subtitles.entity.leash_knot.place": "Lávži čuolbmaduvvo", "subtitles.entity.lightning_bolt.impact": "Álddágas čask<PERSON>", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON><PERSON>", "subtitles.entity.llama.ambient": "<PERSON><PERSON><PERSON> me<PERSON>ku", "subtitles.entity.llama.angry": "<PERSON><PERSON><PERSON> meahku suhtun", "subtitles.entity.llama.chest": "Giisá bidjojuvvo lamai", "subtitles.entity.llama.death": "Llama <PERSON>", "subtitles.entity.llama.eat": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.llama.hurt": "Llama bávččaga", "subtitles.entity.llama.spit": "Llama čolgada", "subtitles.entity.llama.step": "Llama vázzá", "subtitles.entity.llama.swag": "<PERSON><PERSON><PERSON>", "subtitles.entity.magma_cube.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.magma_cube.hurt": "Magmaealli b<PERSON>ččaga", "subtitles.entity.magma_cube.squish": "Magmaealli šnjivvá", "subtitles.entity.minecart.inside": "Roggangielká gihččá", "subtitles.entity.minecart.inside_underwater": "Roggangielká gihččá", "subtitles.entity.minecart.riding": "Roggangie<PERSON><PERSON> fierr<PERSON>", "subtitles.entity.mooshroom.convert": "Mooshroom earáhuvvo", "subtitles.entity.mooshroom.eat": "Mooshroom borrá", "subtitles.entity.mooshroom.milk": "Mooshroom bohččo", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom b<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.mule.angry": "Muvla niv<PERSON>", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON>jo", "subtitles.entity.mule.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.mule.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.painting.break": "<PERSON><PERSON><PERSON>", "subtitles.entity.painting.place": "<PERSON><PERSON><PERSON>", "subtitles.entity.panda.aggressive_ambient": "<PERSON>da bosiha su<PERSON>un", "subtitles.entity.panda.ambient": "Panda sahká", "subtitles.entity.panda.bite": "Panda gáskesta", "subtitles.entity.panda.cant_breed": "Panda váidá", "subtitles.entity.panda.death": "Panda jápmá", "subtitles.entity.panda.eat": "Panda borr<PERSON>", "subtitles.entity.panda.hurt": "Panda bávččaga", "subtitles.entity.panda.pre_sneeze": "<PERSON><PERSON> n<PERSON>", "subtitles.entity.panda.sneeze": "Panda gastila", "subtitles.entity.panda.step": "Panda vázzá", "subtitles.entity.panda.worried_ambient": "Panda n<PERSON>hku", "subtitles.entity.parrot.ambient": "Pa<PERSON><PERSON><PERSON> hupma", "subtitles.entity.parrot.death": "Papegoia j<PERSON>pmá", "subtitles.entity.parrot.eats": "Papegoia borrá", "subtitles.entity.parrot.fly": "Papegoia rámšku", "subtitles.entity.parrot.hurts": "Papegoia bávččaga", "subtitles.entity.parrot.imitate.blaze": "Papegoia vuoidná", "subtitles.entity.parrot.imitate.bogged": "Papegoia skillá", "subtitles.entity.parrot.imitate.breeze": "Papegoia <PERSON>ohku", "subtitles.entity.parrot.imitate.creaking": "Papegoia gihčá", "subtitles.entity.parrot.imitate.creeper": "Papegoia šŋjirrá", "subtitles.entity.parrot.imitate.drowned": "Pa<PERSON><PERSON><PERSON> bohraida", "subtitles.entity.parrot.imitate.elder_guardian": "Papegoia fuoiku", "subtitles.entity.parrot.imitate.ender_dragon": "Papegoia šuohkká", "subtitles.entity.parrot.imitate.endermite": "Papegoia ciehppá", "subtitles.entity.parrot.imitate.evoker": "Papego<PERSON> buljarda", "subtitles.entity.parrot.imitate.ghast": "Papegoia čierru", "subtitles.entity.parrot.imitate.guardian": "Papegoia fuoiku", "subtitles.entity.parrot.imitate.hoglin": "Papego<PERSON> harrá", "subtitles.entity.parrot.imitate.husk": "Papegoia šuohkká", "subtitles.entity.parrot.imitate.illusioner": "Papego<PERSON> buljarda", "subtitles.entity.parrot.imitate.magma_cube": "Papegoia šŋjivvá", "subtitles.entity.parrot.imitate.phantom": "Papegoia šnjivvá", "subtitles.entity.parrot.imitate.piglin": "Papegoia ruovgá", "subtitles.entity.parrot.imitate.piglin_brute": "Papegoia ruovgá", "subtitles.entity.parrot.imitate.pillager": "Papego<PERSON> buljarda", "subtitles.entity.parrot.imitate.ravager": "Papego<PERSON> harrá", "subtitles.entity.parrot.imitate.shulker": "Papegoia njáhká", "subtitles.entity.parrot.imitate.silverfish": "Papegoia šŋjirrá", "subtitles.entity.parrot.imitate.skeleton": "Papegoia skállá", "subtitles.entity.parrot.imitate.slime": "Papegoia šŋjivvá", "subtitles.entity.parrot.imitate.spider": "Papegoia šŋjirrá", "subtitles.entity.parrot.imitate.stray": "Papegoia skállá", "subtitles.entity.parrot.imitate.vex": "Papego<PERSON> h<PERSON>ha", "subtitles.entity.parrot.imitate.vindicator": "Papegoia humešta", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.parrot.imitate.witch": "Papegoia boagus<PERSON>á", "subtitles.entity.parrot.imitate.wither": "Papegoia suhttá", "subtitles.entity.parrot.imitate.wither_skeleton": "Papegoia skállá", "subtitles.entity.parrot.imitate.zoglin": "Papego<PERSON> harrá", "subtitles.entity.parrot.imitate.zombie": "Papegoia šuohkká", "subtitles.entity.parrot.imitate.zombie_villager": "Papegoia šuohkká", "subtitles.entity.phantom.ambient": "Idjagobmi šnjivvá", "subtitles.entity.phantom.bite": "Idjagobmi gáskesta", "subtitles.entity.phantom.death": "Idjagobmi j<PERSON>", "subtitles.entity.phantom.flap": "Idjagobmi rámšku", "subtitles.entity.phantom.hurt": "Idjagobmi bávččaga", "subtitles.entity.phantom.swoop": "Idjagobmi rámškkui", "subtitles.entity.pig.ambient": "Spiidni ruovgá", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pig.hurt": "Spiidni bávččaga", "subtitles.entity.pig.saddle": "Riidensálat darvvihuvvo", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> dávvir<PERSON>", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> ruov<PERSON>", "subtitles.entity.piglin.angry": "<PERSON><PERSON> ruov<PERSON><PERSON>uin", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON>", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> eará<PERSON><PERSON><PERSON>", "subtitles.entity.piglin.death": "<PERSON><PERSON>", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> bá<PERSON>ččaga", "subtitles.entity.piglin.jealous": "<PERSON>lin ruovgá gáđašvu<PERSON>đ<PERSON>", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.piglin.step": "<PERSON><PERSON> l<PERSON>", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> ruovg<PERSON>", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> ruovg<PERSON>uin", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Piglinjábmái", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>š v<PERSON>", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.crit": "Kritihkalaš huškkastat", "subtitles.entity.player.attack.knockback": "Hoigadahttin huškkastat", "subtitles.entity.player.attack.strong": "Veagalaš huškkastat", "subtitles.entity.player.attack.sweep": "Fuoikun huškka<PERSON>t", "subtitles.entity.player.attack.weak": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "Spealli j<PERSON>", "subtitles.entity.player.freeze_hurt": "Spealli galbmo", "subtitles.entity.player.hurt": "Spealli bávččaga", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON><PERSON> buoll<PERSON>", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.teleport": "Spealli sir<PERSON>šuvvá", "subtitles.entity.polar_bear.ambient": "Jiek<PERSON><PERSON><PERSON><PERSON>a rohku", "subtitles.entity.polar_bear.ambient_baby": "Jiek<PERSON><PERSON><PERSON><PERSON>a muoll<PERSON>", "subtitles.entity.polar_bear.death": "Jiekŋagu<PERSON><PERSON> j<PERSON>", "subtitles.entity.polar_bear.hurt": "Jiekŋaguovža bávččaga", "subtitles.entity.polar_bear.warning": "Jiek<PERSON><PERSON><PERSON><PERSON>a muoll<PERSON>", "subtitles.entity.potion.splash": "Bohtal cuvkana", "subtitles.entity.potion.throw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_out": "Mirkoguolli stuorru", "subtitles.entity.puffer_fish.blow_up": "Mir<PERSON><PERSON><PERSON><PERSON> unnu", "subtitles.entity.puffer_fish.death": "Mirkoguolli jápmá", "subtitles.entity.puffer_fish.flop": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.hurt": "Mirkoguolli bávččaga", "subtitles.entity.puffer_fish.sting": "Mirkoguolli čugge", "subtitles.entity.rabbit.ambient": "Njoammil njihkku", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.death": "Njoammil <PERSON>", "subtitles.entity.rabbit.hurt": "Njoammil b<PERSON>ččaga", "subtitles.entity.rabbit.jump": "Njoammil simpu", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.attack": "Mollenstá<PERSON><PERSON>", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.hurt": "Mollenstállu b<PERSON>č<PERSON>", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> muoll<PERSON>", "subtitles.entity.ravager.step": "<PERSON>llenst<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.ravager.stunned": "Mollenstállu vuoimmehuvvo", "subtitles.entity.salmon.death": "Luossa jápmá", "subtitles.entity.salmon.flop": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>", "subtitles.entity.salmon.hurt": "Luossa bávččaga", "subtitles.entity.sheep.ambient": "<PERSON><PERSON><PERSON>za me<PERSON>", "subtitles.entity.sheep.death": "Sávza jápm<PERSON>", "subtitles.entity.sheep.hurt": "Sávza bávččaga", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bá<PERSON><PERSON>", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.silverfish.ambient": "Silbas<PERSON><PERSON><PERSON>", "subtitles.entity.silverfish.death": "Sil<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.silverfish.hurt": "Sil<PERSON><PERSON><PERSON><PERSON> bá<PERSON>", "subtitles.entity.skeleton.ambient": "Dák<PERSON>iggi s<PERSON>", "subtitles.entity.skeleton.converted_to_stray": "Dákteriggi earáhuvvo vádjoleaddjin", "subtitles.entity.skeleton.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.skeleton.hurt": "Dákteriggi b<PERSON>ččaga", "subtitles.entity.skeleton.shoot": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.skeleton_horse.ambient": "Dákteriggi<PERSON><PERSON>", "subtitles.entity.skeleton_horse.death": "Dákteriggihea<PERSON> j<PERSON>", "subtitles.entity.skeleton_horse.hurt": "Dákteriggeheasta bávččaga", "subtitles.entity.skeleton_horse.jump_water": "<PERSON>ák<PERSON>ig<PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.skeleton_horse.swim": "Dákteriggiheasta vuodj<PERSON>", "subtitles.entity.slime.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> falleha", "subtitles.entity.slime.death": "Njivleealli <PERSON>", "subtitles.entity.slime.hurt": "Njivleealli bávččaga", "subtitles.entity.slime.squish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>u", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.digging": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.sniffer.digging_stop": "Sustti čuožžila", "subtitles.entity.sniffer.drop_seed": "Sustti luoitá gilvaga", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.sniffer.egg_crack": "Suostti monni luoddanišgoahtá", "subtitles.entity.sniffer.egg_hatch": "<PERSON><PERSON><PERSON> monni rah<PERSON>a", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.sniffer.hurt": "Sustti bávččaga", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.sniffer.scenting": "Sustti sustila", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.snow_golem.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.snow_golem.hurt": "Ruovdejiehtanas b<PERSON>", "subtitles.entity.snowball.throw": "Muoh<PERSON>č<PERSON><PERSON><PERSON>", "subtitles.entity.spider.ambient": "Heavdni <PERSON>ŋ<PERSON>", "subtitles.entity.spider.death": "Heavdni j<PERSON>", "subtitles.entity.spider.hurt": "Heavdni bávččaga", "subtitles.entity.squid.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vuodj<PERSON>", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>č<PERSON>", "subtitles.entity.squid.squirt": "Áhk<PERSON><PERSON>uolli luoitá ivnni", "subtitles.entity.stray.ambient": "Vádjoleaddji skállá", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.hurt": "Vádjoleaddji bávččaga", "subtitles.entity.strider.death": "Gálašeaddji jápmá", "subtitles.entity.strider.eat": "G<PERSON><PERSON>š<PERSON><PERSON><PERSON> borrá", "subtitles.entity.strider.happy": "Gálašeaddji reavgu", "subtitles.entity.strider.hurt": "Gálašeaddji bávččaga", "subtitles.entity.strider.idle": "Gálašeaddji civká", "subtitles.entity.strider.retreat": "Gálašeaddji geasáša", "subtitles.entity.tadpole.death": "Šlubboaivi jápmá", "subtitles.entity.tadpole.flop": "Šlubboaivi bánccarda", "subtitles.entity.tadpole.grow_up": "Šlubboaivi stuorrola", "subtitles.entity.tadpole.hurt": "Šlubboaivi bávččaga", "subtitles.entity.tnt.primed": "TNT šŋirásta", "subtitles.entity.tropical_fish.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> guo<PERSON> j<PERSON>", "subtitles.entity.tropical_fish.flop": "<PERSON><PERSON><PERSON><PERSON><PERSON> guo<PERSON> b<PERSON><PERSON>", "subtitles.entity.tropical_fish.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> guo<PERSON> bá<PERSON>", "subtitles.entity.turtle.ambient_land": "G<PERSON>barihcci civkala", "subtitles.entity.turtle.death": "G<PERSON>barihcci j<PERSON>pm<PERSON>", "subtitles.entity.turtle.death_baby": "G<PERSON><PERSON><PERSON><PERSON>š j<PERSON>", "subtitles.entity.turtle.egg_break": "Galbarihci monni cu<PERSON>a", "subtitles.entity.turtle.egg_crack": "G<PERSON>barihci monni luoddana", "subtitles.entity.turtle.egg_hatch": "Galbarihci monni cu<PERSON>a", "subtitles.entity.turtle.hurt": "Galbarihcci bávččaga", "subtitles.entity.turtle.hurt_baby": "G<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.turtle.lay_egg": "Galbarihcci bidja moni", "subtitles.entity.turtle.shamble": "<PERSON><PERSON><PERSON><PERSON><PERSON> suonjada", "subtitles.entity.turtle.shamble_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> su<PERSON>", "subtitles.entity.turtle.swim": "G<PERSON><PERSON><PERSON><PERSON> vuodjá", "subtitles.entity.vex.ambient": "Biidnogo<PERSON><PERSON>", "subtitles.entity.vex.charge": "Biidnogobmi riežiha", "subtitles.entity.vex.death": "Biidnogobmi jápmá", "subtitles.entity.vex.hurt": "Biidnogobmi bávččaga", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.hurt": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.villager.no": "<PERSON><PERSON><PERSON> ii leat mielas", "subtitles.entity.villager.trade": "<PERSON><PERSON>š g<PERSON>lvvu<PERSON>", "subtitles.entity.villager.work_armorer": "Suod<PERSON>lusdahk<PERSON> bar<PERSON>", "subtitles.entity.villager.work_butcher": "Njuovvi bargá", "subtitles.entity.villager.work_cartographer": "K<PERSON>rtadahk<PERSON> barg<PERSON>", "subtitles.entity.villager.work_cleric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_farmer": "<PERSON><PERSON> bar<PERSON>", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>gu", "subtitles.entity.villager.work_fletcher": "Njuolladahkki barg<PERSON>", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_librarian": "Girječeahppi bargá", "subtitles.entity.villager.work_mason": "Geađeluddejeaddji bargá", "subtitles.entity.villager.work_shepherd": "Guođ<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_toolsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_weaponsmith": "Vearjor<PERSON><PERSON><PERSON> bar<PERSON>", "subtitles.entity.villager.yes": "<PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.hurt": "Vákta b<PERSON>čč<PERSON>", "subtitles.entity.wandering_trader.ambient": "Johttigávppašeaddji humešta", "subtitles.entity.wandering_trader.death": "Johttigávppašeaddji jápmá", "subtitles.entity.wandering_trader.disappeared": "Johttigávppašeaddji jávká", "subtitles.entity.wandering_trader.drink_milk": "Johttigávppašeaddji juhká mielkki", "subtitles.entity.wandering_trader.drink_potion": "Johttigávppašeaddji juhká <PERSON>uh<PERSON>", "subtitles.entity.wandering_trader.hurt": "Johttigávppašeaddji bávččaga", "subtitles.entity.wandering_trader.no": "Johttigávppašeaddji eahpida", "subtitles.entity.wandering_trader.reappeared": "Johttigávppašeaddji ihttá", "subtitles.entity.wandering_trader.trade": "Johttigávppašeaddji vuovdala", "subtitles.entity.wandering_trader.yes": "Johttigávppašeaddji modje", "subtitles.entity.warden.agitated": "<PERSON><PERSON><PERSON> č<PERSON>ge su<PERSON>", "subtitles.entity.warden.ambient": "<PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.warden.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.attack_impact": "<PERSON><PERSON><PERSON> hu<PERSON>", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.dig": "<PERSON><PERSON><PERSON> rogg<PERSON>", "subtitles.entity.warden.emerge": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.heartbeat": "Gozi váibmu julká", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON> b<PERSON>čč<PERSON>", "subtitles.entity.warden.listening": "<PERSON><PERSON><PERSON> guldala", "subtitles.entity.warden.listening_angry": "<PERSON><PERSON><PERSON> fuo<PERSON><PERSON><PERSON>", "subtitles.entity.warden.nearby_close": "<PERSON><PERSON><PERSON> lea lahka", "subtitles.entity.warden.nearby_closer": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.nearby_closest": "<PERSON><PERSON><PERSON> lea la<PERSON>", "subtitles.entity.warden.roar": "<PERSON><PERSON><PERSON> čerge", "subtitles.entity.warden.sniff": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.sonic_boom": "<PERSON><PERSON><PERSON> bo<PERSON>a", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON> roh<PERSON>", "subtitles.entity.warden.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.tendril_clicks": "<PERSON><PERSON><PERSON> co<PERSON>", "subtitles.entity.wind_charge.throw": "<PERSON><PERSON>gg<PERSON><PERSON><PERSON> bossu", "subtitles.entity.wind_charge.wind_burst": "Bieggadoarohas <PERSON>tt<PERSON>", "subtitles.entity.witch.ambient": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.witch.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON> jugista", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.wither.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.wither.death": "<PERSON><PERSON>", "subtitles.entity.wither.hurt": "<PERSON><PERSON>", "subtitles.entity.wither.shoot": "Wither fall<PERSON>a", "subtitles.entity.wither.spawn": "<PERSON><PERSON>", "subtitles.entity.wither_skeleton.ambient": "Witherdák<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wither_skeleton.hurt": "Witherdákteriggi b<PERSON>ččaga", "subtitles.entity.wolf.ambient": "<PERSON><PERSON><PERSON> c<PERSON>", "subtitles.entity.wolf.bark": "<PERSON><PERSON><PERSON> c<PERSON>", "subtitles.entity.wolf.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.growl": "<PERSON><PERSON>e harra", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.wolf.pant": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.shake": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.whine": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> harr<PERSON>", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> bávččaga", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.converted_to_drowned": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie.destroy_egg": "Galbarihcci monit dulbmo", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie.infect": "<PERSON><PERSON><PERSON><PERSON> bost<PERSON>", "subtitles.entity.zombie_horse.ambient": "Jámešhea<PERSON> čierru", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON>šhea<PERSON> j<PERSON>", "subtitles.entity.zombie_horse.hurt": "Jámešheasta bávččaga", "subtitles.entity.zombie_villager.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie_villager.converted": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie_villager.cure": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.zombie_villager.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie_villager.hurt": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ha<PERSON>", "subtitles.entity.zombified_piglin.death": "Piglinjá<PERSON><PERSON>", "subtitles.entity.zombified_piglin.hurt": "Piglinjábmi bávččaga", "subtitles.event.mob_effect.bad_omen": "<PERSON><PERSON>", "subtitles.event.mob_effect.raid_omen": "<PERSON>ie<PERSON><PERSON><PERSON>", "subtitles.event.mob_effect.trial_omen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON>", "subtitles.event.raid.horn": "<PERSON><PERSON><PERSON> ji<PERSON> r<PERSON>", "subtitles.item.armor.equip": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_chain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_diamond": "Diam<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_elytra": "Soajit s<PERSON>", "subtitles.item.armor.equip_gold": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_iron": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_leather": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_netherite": "Netheriita suod<PERSON>", "subtitles.item.armor.equip_turtle": "Skálžogahpir skállá", "subtitles.item.armor.equip_wolf": "Gumppe suod<PERSON>lusbivttas čadnojuvvo gitta", "subtitles.item.armor.unequip_wolf": "Gumppe su<PERSON>bivttas luvvejuvvo", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON><PERSON> fasku", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON><PERSON> fasku", "subtitles.item.axe.wax_off": "<PERSON><PERSON><PERSON>", "subtitles.item.bone_meal.use": "Dáktejáffut ruoškkehit", "subtitles.item.book.page_turn": "<PERSON><PERSON><PERSON>", "subtitles.item.book.put": "<PERSON><PERSON><PERSON>", "subtitles.item.bottle.empty": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel.complete": "Geargan č<PERSON>vrra kustet", "subtitles.item.brush.brushing.sand": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.sand.complete": "<PERSON>gan s<PERSON>dduid kustet", "subtitles.item.bucket.empty": "Gievdni guo<PERSON>na", "subtitles.item.bucket.fill": "Gievdni dievv<PERSON>", "subtitles.item.bucket.fill_axolotl": "Aks<PERSON><PERSON>l goivestuvvo", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON><PERSON> guoli", "subtitles.item.bucket.fill_tadpole": "Darvvihii šlubboaivvi", "subtitles.item.bundle.drop_contents": "Dávvirbursa guorrana", "subtitles.item.bundle.insert": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bundle.insert_fail": "Dávvirbursa lea dievva", "subtitles.item.bundle.remove_one": "<PERSON>áv<PERSON> v<PERSON><PERSON><PERSON>", "subtitles.item.chorus_fruit.teleport": "<PERSON><PERSON><PERSON><PERSON> sirdila", "subtitles.item.crop.plant": "<PERSON><PERSON><PERSON> gilvo", "subtitles.item.crossbow.charge": "Dávge<PERSON><PERSON> geald<PERSON>", "subtitles.item.crossbow.hit": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.crossbow.load": "Dávgebissui bidjo n<PERSON>a", "subtitles.item.crossbow.shoot": "Dávgebissu bá<PERSON>", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>", "subtitles.item.elytra.flying": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "Buolligea<PERSON><PERSON>", "subtitles.item.flintandsteel.use": "Cahkkehanruovdi cahkkeha", "subtitles.item.glow_ink_sac.use": "Áhcagas ivdnečoalli duškida", "subtitles.item.goat_horn.play": "Gáicačoarvi č<PERSON>", "subtitles.item.hoe.till": "<PERSON><PERSON><PERSON><PERSON> guohke <PERSON>a", "subtitles.item.honey_bottle.drink": "Šlubista", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON><PERSON> vuido", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "Čáhppesivdnečoalli duškida", "subtitles.item.lead.break": "Lávži boatkana", "subtitles.item.lead.tied": "Lávži čadnojuvvo", "subtitles.item.lead.untied": "Lávži gálgojuvvo", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "Láidengeađgekompássa lohkkaduvvo láidengeađgái", "subtitles.item.mace.smash_air": "<PERSON><PERSON><PERSON><PERSON> bo<PERSON>", "subtitles.item.mace.smash_ground": "Veažir <PERSON>", "subtitles.item.nether_wart.plant": "Gilvagat gilvojuvve", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON>a", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.shears.snip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.shield.block": "Galba gillá", "subtitles.item.shovel.flatten": "Goaivu dulbe", "subtitles.item.spyglass.stop_using": "Giikan bidjo eret", "subtitles.item.spyglass.use": "Giikan giikanastá", "subtitles.item.totem.use": "<PERSON><PERSON><PERSON>", "subtitles.item.trident.hit": "<PERSON><PERSON><PERSON><PERSON>ugge", "subtitles.item.trident.hit_ground": "<PERSON><PERSON><PERSON><PERSON> sparaida", "subtitles.item.trident.return": "<PERSON><PERSON><PERSON><PERSON> má<PERSON>vo", "subtitles.item.trident.riptide": "Háŋgu zoome", "subtitles.item.trident.throw": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.trident.thunder": "Háŋkku áltagas beašku", "subtitles.item.wolf_armor.break": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.wolf_armor.crack": "<PERSON><PERSON><PERSON> luo<PERSON>", "subtitles.item.wolf_armor.damage": "Gump<PERSON> cakk<PERSON> v<PERSON>", "subtitles.item.wolf_armor.repair": "Gumppe su<PERSON>lusbivttas divvojuvvo", "subtitles.particle.soul_escape": "<PERSON><PERSON><PERSON>", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "subtitles.ui.hud.bubble_pop": "Vuoidnanbulljar<PERSON> guo<PERSON>na", "subtitles.ui.loom.take_result": "Gákkes<PERSON><PERSON><PERSON> adno<PERSON>", "subtitles.ui.stonecutter.take_result": "Geađgečuohppi adno", "subtitles.weather.rain": "<PERSON><PERSON>v<PERSON>", "symlink_warning.message": "Sáhttá vár<PERSON><PERSON>š rahpat máilmmiid gihpagis mas leat symbolalaš liŋkkat jus it dieđe juste man dagat. Loga eambbo d<PERSON>: %s.", "symlink_warning.message.pack": "Sáhttá vár<PERSON><PERSON>š rahpat gihppagiid mas leat symbolalaš liŋkkat jus it dieđe juste man dagat. Loga eanet dáppe: %s.", "symlink_warning.message.world": "Sáhttá vár<PERSON><PERSON>š rahpat máilmmiid gihpagis mas leat symbolalaš liŋkkat jus it dieđe juste man dagat. Loga eanet dáppe: %s.", "symlink_warning.more_info": "<PERSON><PERSON><PERSON>", "symlink_warning.title": "Máilbmegihppa sisttisdoallá symbolalaš liŋkkaid", "symlink_warning.title.pack": "Lasihu<PERSON><PERSON> páhkain lea(t) symbolalaš liŋkat", "symlink_warning.title.world": "Máilbmegihppa sisttisdoallá symbolalaš liŋkkaid", "team.collision.always": "<PERSON><PERSON>", "team.collision.never": "<PERSON>i go<PERSON>ige", "team.collision.pushOtherTeams": "<PERSON><PERSON><PERSON> joa<PERSON>", "team.collision.pushOwnTeam": "Hoigga iežat joavkku", "team.notFound": "Amas joavku '%s'", "team.visibility.always": "<PERSON><PERSON>", "team.visibility.hideForOtherTeams": "Čiega eará joav<PERSON>ide", "team.visibility.hideForOwnTeam": "Čiega iežat joavkkui", "team.visibility.never": "<PERSON>i go<PERSON>ige", "telemetry.event.advancement_made.description": "Go ipmirdat makk<PERSON><PERSON> k<PERSON>av<PERSON>tas leat ožžon ovdáneami, de dat veahkeha min buorebut ipmirdit ja buoridit spealu ovdánemiid.", "telemetry.event.advancement_made.title": "Ovdáneapmi oláhuvvon", "telemetry.event.game_load_times.description": "<PERSON><PERSON><PERSON> d<PERSON> sáhttá veahkehit min gávnnahit gokko lea dá<PERSON><PERSON><PERSON><PERSON><PERSON> buoridit <PERSON>ggahanproseass<PERSON>, danne go dat mihtida álggahandásiid čađahanáiggiid.", "telemetry.event.game_load_times.title": "Spealu r<PERSON>", "telemetry.event.optional": "%s (Eak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "telemetry.event.optional.disabled": "%s (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) - J<PERSON>dd<PERSON>uv<PERSON>", "telemetry.event.performance_metrics.description": "Go diehtit Minecrafta o<PERSON><PERSON><PERSON>, de sáhttit divodit ja buoridit spealu, vai heive máŋga iešguđegelágan dihtoriidda ja doaibmasystemaide.\nSpealu veršuvdna čoggojuvvo vai sáhttit buohtastahttet doaibmabuktaga ođđaseabbot Minecraft-veršuvnnaiguin.", "telemetry.event.performance_metrics.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> logut", "telemetry.event.required": "%s (Gáibiduvvon)", "telemetry.event.world_load_times.description": "<PERSON> de<PERSON><PERSON> die<PERSON>, man guh<PERSON><PERSON> ádj<PERSON>a rahpat máilmmi ja movt dat rievdá áiggis <PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON> dihte, go mii lasihat ođđa sisdoalu dahje rievdadat teknih<PERSON><PERSON>, de lea dárbu diehtit makk<PERSON>r vá<PERSON><PERSON> das lea rahpanáigái.", "telemetry.event.world_load_times.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.event.world_loaded.description": "Go diehtit movt spealli spellet Minecrafta (earret eará sin speallanhámi, leatgo modden spealli- <PERSON><PERSON><PERSON>, dahje guđe speallover<PERSON>uvnna geavahit), de beassat buoridit spealu dain beliin main speallit duo<PERSON><PERSON> bero<PERSON>t.\nMáilbmi rahpasii- dáhpáhus čatnašuvvá Máilbmi giddejuvvui- dáhpáhusain vai sáhttit rehkenastit speallanvuoru áiggi.", "telemetry.event.world_loaded.title": "M<PERSON><PERSON><PERSON>", "telemetry.event.world_unloaded.description": "<PERSON><PERSON><PERSON> dáhp<PERSON><PERSON> čatnašuvvá Máilbmi rahpasii- dáhpáhussii vai sáhttit rehkenastit man guhká máilbmi lei rabas.\n<PERSON><PERSON> (sekunddain ja coahkkimiin) mihtiduvvá go speallanvuorru nohká (earret eará go manat fas ovdasiidui dahje guođát servera).", "telemetry.event.world_unloaded.title": "M<PERSON><PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "S<PERSON>al<PERSON> (Coahkkimat)", "telemetry.property.advancement_id.title": "Ovdáneami ID", "telemetry.property.client_id.title": "Spealli ID", "telemetry.property.client_modded.title": "Moddejuvvon speallibealde", "telemetry.property.dedicated_memory_kb.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> muitu (kB)", "telemetry.property.event_timestamp_utc.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (UTC)", "telemetry.property.frame_rate_samples.title": "Govat juo<PERSON>ke seku<PERSON> (FPS)", "telemetry.property.game_mode.title": "Speallanhápmi", "telemetry.property.game_version.title": "Spealloveršuvdna", "telemetry.property.launcher_name.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> namma", "telemetry.property.load_time_bootstrap_ms.title": "<PERSON><PERSON><PERSON><PERSON> (Millisekunddain)", "telemetry.property.load_time_loading_overlay_ms.title": "Ra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Millisekundda)", "telemetry.property.load_time_pre_window_ms.title": "Láse rahpasii go lei vás<PERSON> (millisekundda)", "telemetry.property.load_time_total_time_ms.title": "<PERSON><PERSON><PERSON><PERSON> (Millisekunddain)", "telemetry.property.minecraft_session_id.title": "Minecraft speallanvuoru ID", "telemetry.property.new_world.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.number_of_samples.title": "Iskuslohku", "telemetry.property.operating_system.title": "Doaibmasaš systema", "telemetry.property.opt_in.title": "Searvanvejo<PERSON><PERSON><PERSON>", "telemetry.property.platform.title": "Speallanrusttet", "telemetry.property.realms_map_content.title": "Realms m<PERSON><PERSON><PERSON> (Minispealu namma)", "telemetry.property.render_distance.title": "Oaidninguhkodat", "telemetry.property.render_time_samples.title": "Renderaste áigeiskosiid", "telemetry.property.seconds_since_load.title": "<PERSON><PERSON><PERSON> (Sekunddain)", "telemetry.property.server_modded.title": "Moddejuvvon serverbealde", "telemetry.property.server_type.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.ticks_since_load.title": "<PERSON><PERSON><PERSON> (Coahkkimat)", "telemetry.property.used_memory_samples.title": "Geavahuv<PERSON> summá<PERSON> gea<PERSON>u", "telemetry.property.user_id.title": "Geavaheaddji ID", "telemetry.property.world_load_time_ms.title": "<PERSON><PERSON><PERSON><PERSON> (Millisekunddain)", "telemetry.property.world_session_id.title": "Máilbmevuoru ID", "telemetry_info.button.give_feedback": "Dávisstahte", "telemetry_info.button.privacy_statement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry_info.button.show_data": "Čájet mu dieđuid", "telemetry_info.opt_in.description": "Dohkkehan ahte válljejuvvon telemetriijadieđut s<PERSON>dd<PERSON>uvvojit", "telemetry_info.property_title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "telemetry_info.screen.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> dieđ<PERSON> láidesta min divodit daid osiid <PERSON>as mat leat dehálaččat spelliide.\nSáhtát vel midjiide sáddet lii<PERSON>, jus lea miella veah<PERSON> buo<PERSON>amis <PERSON>crafta.", "telemetry_info.screen.title": "Telemetriijadie<PERSON><PERSON>", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s %s tikkas", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Diehtu:", "test_block.mode.accept": "Dohkket", "test_block.mode.fail": "<PERSON><PERSON><PERSON>", "test_block.mode.log": "<PERSON><PERSON><PERSON>", "test_block.mode.start": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Viečča ja čađat", "test_instance.action.save": "<PERSON><PERSON><PERSON>", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Failed: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Boasttu iskkadan ID", "test_instance.description.no_test": "Iskkadeapmi ii gávdno", "test_instance.description.structure": "Huksehus: %s", "test_instance.description.type": "Málle: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Áđat:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[dohk<PERSON><PERSON><PERSON><PERSON><PERSON>]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Jorran:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "Fuomášeimmet 32-bit systema: sáhtt<PERSON> bissehit du speallamis boa<PERSON><PERSON><PERSON><PERSON><PERSON>, dan dihte go mii fargga gáibidat 64-bit systema!", "title.32bit.deprecation.realms": "Minecraft gáibida fargga 64-bit systema, juoga mii bisseha du speallamis dahje geavaheamis Realms fálaldaga dán speallanrusttegis. Fertet ieš alddis heaittihit Realms diŋgojumi.", "title.32bit.deprecation.realms.check": "Ale šat čájet dán dieđu", "title.32bit.deprecation.realms.header": "Fuomášeimmet 32-bit systema", "title.credits": "Dahkkivuoigatvuohta Mojang AB. Ale juogat!", "title.multiplayer.disabled": "Mánggaidspealli lea jáddaduvvon. Iska iežat Microsoft-geavaheaddji <PERSON>.", "title.multiplayer.disabled.banned.name": "Fertet rievdadit nama ovdal go beasat speallat online", "title.multiplayer.disabled.banned.permanent": "Du geavaheaddji lea bistevaččat gildojuvvon online-speallamis", "title.multiplayer.disabled.banned.temporary": "Du geavaheaddji lea gaskaboddosaččat gildojuvvon online-speallamis", "title.multiplayer.lan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "M<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>li (olgguldas buvttadeaddji server)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "translation.test.args": "%s %s", "translation.test.complex": "Prefixa, %s%2$s fas %s ja %1$s loahpas %s ja de maid %1$s vuohon!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "bures %", "translation.test.invalid2": "bures %s", "translation.test.none": "Bures ain Sápmi!", "translation.test.world": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.amethyst": "Ametistamateri<PERSON>la", "trim_material.minecraft.copper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.diamond": "Diam<PERSON><PERSON><PERSON>", "trim_material.minecraft.emerald": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.gold": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.iron": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.lapis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.netherite": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.quartz": "Kvárcamateriála", "trim_material.minecraft.redstone": "Redstonemateriála", "trim_material.minecraft.resin": "G<PERSON><PERSON>ččemateri<PERSON><PERSON>", "trim_pattern.minecraft.bolt": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.coast": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.dune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.eye": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.flow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.host": "B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.raiser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.rib": "Erttetd<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.sentry": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.shaper": "<PERSON><PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.silence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.snout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.spire": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.tide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.vex": "Biidnogopmi suodjalushearva", "trim_pattern.minecraft.ward": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.wayfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.wild": "<PERSON><PERSON><PERSON>", "tutorial.bundleInsert.description": "Lasit dá<PERSON><PERSON><PERSON><PERSON><PERSON>", "tutorial.bundleInsert.title": "<PERSON><PERSON>", "tutorial.craft_planks.description": "Duod<PERSON><PERSON><PERSON><PERSON> lea <PERSON>", "tutorial.craft_planks.title": "<PERSON><PERSON><PERSON> muo<PERSON>", "tutorial.find_tree.description": "Muorračoskaid oa<PERSON><PERSON><PERSON> jus dearppat muoraid", "tutorial.find_tree.title": "<PERSON><PERSON><PERSON><PERSON> muora", "tutorial.look.description": "<PERSON><PERSON><PERSON><PERSON>", "tutorial.look.title": "<PERSON><PERSON><PERSON>", "tutorial.move.description": "Njuike %s boaluin", "tutorial.move.title": "Jođe %s, %s, %s ja %s boaluiguin", "tutorial.open_inventory.description": "Deaddil %s", "tutorial.open_inventory.title": "Raba d<PERSON>vvir<PERSON>kk<PERSON>", "tutorial.punch_tree.description": "Deatte %s boalu", "tutorial.punch_tree.title": "<PERSON><PERSON> muora", "tutorial.socialInteractions.description": "Deaddil %s rahpat", "tutorial.socialInteractions.title": "Sosiála <PERSON>", "upgrade.minecraft.netherite_upgrade": "<PERSON><PERSON><PERSON><PERSON>"}