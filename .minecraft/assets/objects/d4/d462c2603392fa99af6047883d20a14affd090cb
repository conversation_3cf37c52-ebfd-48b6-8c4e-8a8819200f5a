{"accessibility.onboarding.accessibility.button": "Accesibilidad...", "accessibility.onboarding.screen.narrator": "Apretá Enter para habilitar el narrador", "accessibility.onboarding.screen.title": "¡Bienvenido a Minecraft!\n\n¿Querés activar el narrador o ver los ajustes de accesibilidad?", "addServer.add": "Aceptar", "addServer.enterIp": "Dirección IP", "addServer.enterName": "Nombre", "addServer.resourcePack": "Recursos del servidor", "addServer.resourcePack.disabled": "NO", "addServer.resourcePack.enabled": "SÍ", "addServer.resourcePack.prompt": "Preguntar", "addServer.title": "Información del servidor", "advMode.command": "Comando de consola", "advMode.mode": "Modo", "advMode.mode.auto": "Repetición", "advMode.mode.autoexec.bat": "Siempre activo", "advMode.mode.conditional": "Condicional", "advMode.mode.redstone": "<PERSON>mpul<PERSON>", "advMode.mode.redstoneTriggered": "Necesita redstone", "advMode.mode.sequence": "En cadena", "advMode.mode.unconditional": "Incondicional", "advMode.notAllowed": "Tenés que ser administrador en modo creativo", "advMode.notEnabled": "Los bloques de comandos no están activados en este servidor", "advMode.previousOutput": "Comando anterior", "advMode.setCommand": "<PERSON><PERSON><PERSON> comando de consola", "advMode.setCommand.success": "Comando establecido: %s", "advMode.trackOutput": "Ver el resultado", "advMode.triggering": "Activando", "advMode.type": "Tipo", "advancement.advancementNotFound": "Progreso desconocido: %s", "advancements.adventure.adventuring_time.description": "Descubrí todos los biomas.", "advancements.adventure.adventuring_time.title": "Hora de aventura", "advancements.adventure.arbalistic.description": "Matá a cinco criaturas de diferente especie con un tiro de ballesta.", "advancements.adventure.arbalistic.title": "Ballestería avanzada", "advancements.adventure.avoid_vibration.description": "Caminá agachado cerca de un sensor de sculk o de un warden para evitar que te detecten.", "advancements.adventure.avoid_vibration.title": "Discreción 100", "advancements.adventure.blowback.description": "Matá un breeze devolviéndole una carga ventosa.", "advancements.adventure.blowback.title": "Retroceso", "advancements.adventure.brush_armadillo.description": "Conseguí una escama de armadillo usando un cepillo.", "advancements.adventure.brush_armadillo.title": "Escama-ravi<PERSON><PERSON>", "advancements.adventure.bullseye.description": "Dispará al centro de una diana desde una distancia de 30 metros como mínimo.", "advancements.adventure.bullseye.title": "En el blanco", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Fabricá un jarrón decorado con 4 fragmentos de cerámica.", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Restauración meticulosa", "advancements.adventure.crafters_crafting_crafters.description": "Presenciá un fabricador fabricar un fabricador.", "advancements.adventure.crafters_crafting_crafters.title": "Fabricadores fabricando fabricadores", "advancements.adventure.fall_from_world_height.description": "Lanzate en caída libre desde lo más alto del mundo (límite de altura) hasta lo más profundo y sobreviví.", "advancements.adventure.fall_from_world_height.title": "Cuevas y acantilados", "advancements.adventure.heart_transplanter.description": "Colocá un corazón de crepitante con la alineación correcta entre dos troncos de roble pálido.", "advancements.adventure.heart_transplanter.title": "Cardiólogo", "advancements.adventure.hero_of_the_village.description": "Defendé a una aldea de una invasión.", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON><PERSON> aldea", "advancements.adventure.honey_block_slide.description": "Deslizate por bloques de miel para parar una caída.", "advancements.adventure.honey_block_slide.title": "Situación pegajosa", "advancements.adventure.kill_a_mob.description": "Matá a una criatura hostil.", "advancements.adventure.kill_a_mob.title": "Cazamonstruos", "advancements.adventure.kill_all_mobs.description": "Matá a una criatura hostil de cada tipo.", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Matá a una criatura cerca de un catalizador de sculk.", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "¡Se contagia!", "advancements.adventure.lighten_up.description": "Raspá una lámpara de cobre con un hacha para hacerla brillar más.", "advancements.adventure.lighten_up.title": "Brillo a la vista", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Protegé a un aldeano de un inesperado impacto eléctrico sin iniciar un incendio.", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Protector de sobretensión", "advancements.adventure.minecraft_trials_edition.description": "Entrá a una cámara de desafío.", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Desafío Edition", "advancements.adventure.ol_betsy.description": "Dispará una ballesta.", "advancements.adventure.ol_betsy.title": "La vieja confiable", "advancements.adventure.overoverkill.description": "Infligí 50 corazones de daño de un solo golpe usando una maza.", "advancements.adventure.overoverkill.title": "¡Sobremazazo!", "advancements.adventure.play_jukebox_in_meadows.description": "Hacé que los prados cobren vida con la música de un tocadiscos.", "advancements.adventure.play_jukebox_in_meadows.title": "El dulce cantar", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Leé la señal de energía de una biblioteca tallada usando un comparador.", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "¡El conocimiento es poder!", "advancements.adventure.revaulting.description": "Desbloqueá un arca siniestra con una llave de desafío siniestra.", "advancements.adventure.revaulting.title": "Caja de pandora", "advancements.adventure.root.description": "Aventura, exploración y combate.", "advancements.adventure.root.title": "Aventura", "advancements.adventure.salvage_sherd.description": "Cepillá un bloque sospechoso para obtener un fragmento de cerámica.", "advancements.adventure.salvage_sherd.title": "Admirando el pasado", "advancements.adventure.shoot_arrow.description": "Disparale a algo con una flecha.", "advancements.adventure.shoot_arrow.title": "En el punto de mira", "advancements.adventure.sleep_in_bed.description": "Dormí en una cama para cambiar tu punto de reaparición.", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON> sue<PERSON>", "advancements.adventure.sniper_duel.description": "Matá a un esqueleto desde una distancia de 50 metros como mínimo.", "advancements.adventure.sniper_duel.title": "Duelo de francocazadores", "advancements.adventure.spyglass_at_dragon.description": "<PERSON><PERSON> al Enderdragón con un catalejo.", "advancements.adventure.spyglass_at_dragon.title": "¿Es un avión?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON><PERSON> a un ghast con un catalejo.", "advancements.adventure.spyglass_at_ghast.title": "¿Es un globo?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON><PERSON> a un loro con un catalejo.", "advancements.adventure.spyglass_at_parrot.title": "¿Es un pájaro?", "advancements.adventure.summon_iron_golem.description": "Construí un gólem de hierro para ayudar a defender una aldea.", "advancements.adventure.summon_iron_golem.title": "El guardaespaldas", "advancements.adventure.throw_trident.description": "Tirá un tridente contra algo.\nNota: tirar tu única arma no es buena idea.", "advancements.adventure.throw_trident.title": "Maravillosa jugada", "advancements.adventure.totem_of_undying.description": "Usá un tótem de inmortalidad para engañar a la muerte.", "advancements.adventure.totem_of_undying.title": "Post mortem", "advancements.adventure.trade.description": "Hacé un intercambio con un aldeano.", "advancements.adventure.trade.title": "¡Qué buen trato!", "advancements.adventure.trade_at_world_height.description": "Comerciá con un aldeano en el límite de altura del mundo.", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON> est<PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Aplicá estas plantillas de herrería al menos una vez: aguja, hocico, costillas, guardián, silencio, vex, marea, guía.", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON> con estilo", "advancements.adventure.trim_with_any_armor_pattern.description": "Decorá una armadura en una mesa de herrería.", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.two_birds_one_arrow.description": "<PERSON><PERSON> a dos phantoms con una flecha perforante.", "advancements.adventure.two_birds_one_arrow.title": "Dos pájaros de un tiro", "advancements.adventure.under_lock_and_key.description": "Abrí un arca usando una llave de desafío.", "advancements.adventure.under_lock_and_key.title": "Bajo llave", "advancements.adventure.use_lodestone.description": "Imantá una brújula a una magnetita.", "advancements.adventure.use_lodestone.title": "Magne<PERSON>ta, llévame a casita", "advancements.adventure.very_very_frightening.description": "Atacá a un aldeano utilizando un rayo.", "advancements.adventure.very_very_frightening.title": "Impactrueno", "advancements.adventure.voluntary_exile.description": "Matá al capitán de una invasión.\nPodés pensar en alejarte de las aldeas por ahora...", "advancements.adventure.voluntary_exile.title": "<PERSON><PERSON>o volunta<PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Caminá sobre nieve polvo... sin hundirte.", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Ligero como conejo", "advancements.adventure.who_needs_rockets.description": "Usá una carga ventosa para lanzarte 8 bloques hacia arriba.", "advancements.adventure.who_needs_rockets.title": "¿Quién necesita cohetes?", "advancements.adventure.whos_the_pillager_now.description": "<PERSON> a un saqueador el sabor de su propia medicina.", "advancements.adventure.whos_the_pillager_now.title": "El saqueador saqueado", "advancements.empty": "Parece que no hay nada por acá...", "advancements.end.dragon_breath.description": "Recolectá aliento de dragón en una botella.", "advancements.end.dragon_breath.title": "¡Qué mal aliento!", "advancements.end.dragon_egg.description": "Agarrá el huevo de dragón.", "advancements.end.dragon_egg.title": "La nueva generación", "advancements.end.elytra.description": "Encontrá una elytra.", "advancements.end.elytra.title": "Al infinito... ¡y más allá!", "advancements.end.enter_end_gateway.description": "Escapá de la isla principal.", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON> remota", "advancements.end.find_end_city.description": "¡Vamos!, ¿qué es lo peor que puede pasar?", "advancements.end.find_end_city.title": "La ciudad al final del juego", "advancements.end.kill_dragon.description": "Mat<PERSON> al Ender Dragon para liberar el End. ¡Buena suerte!", "advancements.end.kill_dragon.title": "La libertad del End", "advancements.end.levitate.description": "Levitá a una altura de 50 bloques luego de recibir ataques de un shulker.", "advancements.end.levitate.title": "Buenas vistas desde lo alto", "advancements.end.respawn_dragon.description": "Devolvele la vida al Enderdragón", "advancements.end.respawn_dragon.title": "¿Es un déjà vu?", "advancements.end.root.description": "¿Se acabó? O... ¿sólo acaba de empezar?", "advancements.end.root.title": "El End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Ha<PERSON> que un allay suelte una torta en un bloque musical.", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "¡Que los cumplas feliz!", "advancements.husbandry.allay_deliver_item_to_player.description": "Ha<PERSON> que un allay te traiga un objeto.", "advancements.husbandry.allay_deliver_item_to_player.title": "Yo soy tu amigo fiel", "advancements.husbandry.axolotl_in_a_bucket.description": "Atrapá un ajolote en un balde.", "advancements.husbandry.axolotl_in_a_bucket.title": "El depredador más tierno", "advancements.husbandry.balanced_diet.description": "Comé todo lo que sea comestible, aunque no sea saludable.", "advancements.husbandry.balanced_diet.title": "Una dieta equilibrada", "advancements.husbandry.breed_all_animals.description": "Juntá a dos animales de cada tipo para reproducirlos.", "advancements.husbandry.breed_all_animals.title": "De dos en dos", "advancements.husbandry.breed_an_animal.description": "Juntá a dos animales para reproducirlos.", "advancements.husbandry.breed_an_animal.title": "Tor<PERSON>lit<PERSON>", "advancements.husbandry.complete_catalogue.description": "¡Domesticá todas las especies de gatos!", "advancements.husbandry.complete_catalogue.title": "Un gatálogo completo", "advancements.husbandry.feed_snifflet.description": "Alimentá a un snifflet.", "advancements.husbandry.feed_snifflet.title": "Pequeños olfateos", "advancements.husbandry.fishy_business.description": "Agarrá un pez.", "advancements.husbandry.fishy_business.title": "Un asunto escamoso", "advancements.husbandry.froglights.description": "<PERSON>é todas las ranaluces en tu inventario.", "advancements.husbandry.froglights.title": "¡Con nuestros poderes reunidos!", "advancements.husbandry.kill_axolotl_target.description": "Formá equipo con un ajolote y ganá una pelea.", "advancements.husbandry.kill_axolotl_target.title": "¡El poder curativo de la amistad!", "advancements.husbandry.leash_all_frog_variants.description": "Atá una rienda a todas las variantes de ranas.", "advancements.husbandry.leash_all_frog_variants.title": "Los tres mosqueteros", "advancements.husbandry.make_a_sign_glow.description": "<PERSON><PERSON> brillar el texto de un cartel.", "advancements.husbandry.make_a_sign_glow.title": "¡Brilla y deslumbra!", "advancements.husbandry.netherite_hoe.description": "Usá un lingote de netherita para mejorar una azada, y luego replanteá tus elecciones de vida.", "advancements.husbandry.netherite_hoe.title": "Dedicación seria", "advancements.husbandry.obtain_sniffer_egg.description": "Obtené un huevo de sniffer.", "advancements.husbandry.obtain_sniffer_egg.title": "Huele interesante", "advancements.husbandry.place_dried_ghast_in_water.description": "Colocá un bloque de ghast deshidratado en agua.", "advancements.husbandry.place_dried_ghast_in_water.title": "¡Siempre hidratado!", "advancements.husbandry.plant_any_sniffer_seed.description": "<PERSON><PERSON> cualquier semilla de un sniffer.", "advancements.husbandry.plant_any_sniffer_seed.title": "Sembrando el pasado", "advancements.husbandry.plant_seed.description": "Plantá una semilla y mirá como crece.", "advancements.husbandry.plant_seed.title": "Crecen tan rápido...", "advancements.husbandry.remove_wolf_armor.description": "Sacale la armadura a un lobo usando tijeras.", "advancements.husbandry.remove_wolf_armor.title": "Corte limpio", "advancements.husbandry.repair_wolf_armor.description": "Repará una armadura de lobo dañada utilizando escamas de armadillo.", "advancements.husbandry.repair_wolf_armor.title": "Como nuevo", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Subite a un bote y flotá con una cabra.", "advancements.husbandry.ride_a_boat_with_a_goat.title": "¡Cabra a la vista!", "advancements.husbandry.root.description": "El mundo está repleto de amigos y de comida.", "advancements.husbandry.root.title": "Prosperidad", "advancements.husbandry.safely_harvest_honey.description": "Usá una fogata para conseguir miel de una colmena con un frasco sin enojar a las abejas.", "advancements.husbandry.safely_harvest_honey.title": "Abelante, esta es tu casa", "advancements.husbandry.silk_touch_nest.description": "Mové una colmena con 3 abejas dentro usando toque de seda.", "advancements.husbandry.silk_touch_nest.title": "Abejémonos de acá", "advancements.husbandry.tactical_fishing.description": "Atrapá a un pez... ¡sin una caña de pescar!", "advancements.husbandry.tactical_fishing.title": "Pesca táctica", "advancements.husbandry.tadpole_in_a_bucket.description": "Atrapá un renacuajo en un balde.", "advancements.husbandry.tadpole_in_a_bucket.title": "¿Un baldecuajo?", "advancements.husbandry.tame_an_animal.description": "Domesticá a un animal.", "advancements.husbandry.tame_an_animal.title": "Amigos para siempre", "advancements.husbandry.wax_off.description": "¡Deslustrá un bloque de cobre!", "advancements.husbandry.wax_off.title": "Deslustrando ando", "advancements.husbandry.wax_on.description": "¡Encerá un bloque de cobre con un panal!", "advancements.husbandry.wax_on.title": "Encerando ando", "advancements.husbandry.whole_pack.description": "Domesticá a todas las especies de lobo.", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON><PERSON> manada", "advancements.nether.all_effects.description": "Conseguí todos los efectos posibles al mismo tiempo.", "advancements.nether.all_effects.title": "¿Cómo llegamos hasta acá?", "advancements.nether.all_potions.description": "Conseguí todos los efectos de pociones al mismo tiempo.", "advancements.nether.all_potions.title": "Mezcla explosiva", "advancements.nether.brew_potion.description": "Prepará una poción.", "advancements.nether.brew_potion.title": "Destilería local", "advancements.nether.charge_respawn_anchor.description": "Cargá un nexo de reaparición al máximo.", "advancements.nether.charge_respawn_anchor.title": "No «siete» vidas exactamente", "advancements.nether.create_beacon.description": "Fabricá y colocá un faro.", "advancements.nether.create_beacon.title": "Hágase la luz", "advancements.nether.create_full_beacon.description": "Ha<PERSON> que un faro funcione a máxima potencia.", "advancements.nether.create_full_beacon.title": "Faroneitor", "advancements.nether.distract_piglin.description": "Distraé a un piglin con oro.", "advancements.nether.distract_piglin.title": "Mi precioso...", "advancements.nether.explore_nether.description": "Descubrí todos los biomas del Nether.", "advancements.nether.explore_nether.title": "Turismo infernal", "advancements.nether.fast_travel.description": "Usá el Nether para viajar 7 kilómetros en la superficie.", "advancements.nether.fast_travel.title": "Burbuja subespacial", "advancements.nether.find_bastion.description": "Entrá en las ruinas de un bastión.", "advancements.nether.find_bastion.title": "Qué tiempos aquellos", "advancements.nether.find_fortress.description": "Encontrá y explorá una fortaleza en el Nether.", "advancements.nether.find_fortress.title": "Una terrible fortaleza", "advancements.nether.get_wither_skull.description": "Sacale el cráneo a un esqueleto del Wither.", "advancements.nether.get_wither_skull.title": "Por una cabeza", "advancements.nether.loot_bastion.description": "Saqueá un cofre en las ruinas de un bastión.", "advancements.nether.loot_bastion.title": "Guerra de chanchos", "advancements.nether.netherite_armor.description": "Conseguí una armadura completa de netherita.", "advancements.nether.netherite_armor.title": "Cubrime con escombros", "advancements.nether.obtain_ancient_debris.description": "Encontrá escombros ancestrales.", "advancements.nether.obtain_ancient_debris.title": "Oculto en las profundidades", "advancements.nether.obtain_blaze_rod.description": "Sacale a un blaze su vara.", "advancements.nether.obtain_blaze_rod.title": "Jugando con fuego", "advancements.nether.obtain_crying_obsidian.description": "Conseguí obsidiana llorosa.", "advancements.nether.obtain_crying_obsidian.title": "¿Quién está cortando cebollas?", "advancements.nether.return_to_sender.description": "Acabá con un ghast usando su bola de fuego.", "advancements.nether.return_to_sender.title": "Devolver al remitente", "advancements.nether.ride_strider.description": "Manejá un strider con una caña con hongo distorsionado.", "advancements.nether.ride_strider.title": "¡Un bote con patas!", "advancements.nether.ride_strider_in_overworld_lava.description": "<PERSON><PERSON> un laaaargo paseo con un Strider por un lago de lava en la superficie.", "advancements.nether.ride_strider_in_overworld_lava.title": "Como en casa", "advancements.nether.root.description": "¡Estar acá es infernal!", "advancements.nether.root.title": "<PERSON>", "advancements.nether.summon_wither.description": "Invocá al Wither.", "advancements.nether.summon_wither.title": "Dr<PERSON>", "advancements.nether.uneasy_alliance.description": "Llevá a un ghast desde el Nether hasta la superficie sin que reciba daño y... luego... acabá con su vida.", "advancements.nether.uneasy_alliance.title": "Falsa al<PERSON>za", "advancements.nether.use_lodestone.description": "Imantá una brújula a una magnetita.", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON>, llevame a casita", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Debilitá y curá a un aldeano zombi.", "advancements.story.cure_zombie_villager.title": "Zombiólogo", "advancements.story.deflect_arrow.description": "Desviá un proyectil con un escudo.", "advancements.story.deflect_arrow.title": "Hoy no, gracias", "advancements.story.enchant_item.description": "Encantá un ítem en una mesa de encantamientos.", "advancements.story.enchant_item.title": "Aprendiz de mago", "advancements.story.enter_the_end.description": "Localizá, activá y atravesá un portal al End.", "advancements.story.enter_the_end.title": "¿Se acabó?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON><PERSON><PERSON>, activá y atravesá un portal al Nether.", "advancements.story.enter_the_nether.title": "Tenemos que ir más profundo", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON> ojos de ender.", "advancements.story.follow_ender_eye.title": "Ojo espía", "advancements.story.form_obsidian.description": "Conseguí un bloque de obsidiana.", "advancements.story.form_obsidian.title": "Mente fría", "advancements.story.iron_tools.description": "Mejorá tu pico.", "advancements.story.iron_tools.title": "¿No es hierrónico?", "advancements.story.lava_bucket.description": "Llená un balde con lava.", "advancements.story.lava_bucket.title": "¡La cosa está que arde!", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>.", "advancements.story.mine_diamond.title": "¡DIAMANTES!", "advancements.story.mine_stone.description": "Miná piedra con tu nuevo pico.", "advancements.story.mine_stone.title": "La Edad de Piedra", "advancements.story.obtain_armor.description": "Usá una pieza de la armadura de hierro para protegerte.", "advancements.story.obtain_armor.title": "<PERSON><PERSON> de vestirte", "advancements.story.root.description": "El corazón y la historia del juego.", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Una armadura de diamante te puede salvar la vida.", "advancements.story.shiny_gear.title": "Cúbreme de diamantes", "advancements.story.smelt_iron.description": "Fundí un lingote de hierro.", "advancements.story.smelt_iron.title": "La Edad de Hierro", "advancements.story.upgrade_tools.description": "Fabricá un pico mejor.", "advancements.story.upgrade_tools.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.toast.challenge": "¡Desafío completado!", "advancements.toast.goal": "¡Objetivo alcanzado!", "advancements.toast.task": "¡Progreso realizado!", "argument.anchor.invalid": "La posición de anclaje de la entidad no es válida: %s", "argument.angle.incomplete": "Incompleto (se requiere 1 ángulo)", "argument.angle.invalid": "Ángulo no válido", "argument.block.id.invalid": "Tipo de bloque desconocido: %s", "argument.block.property.duplicate": "La propiedad «%s» del bloque %s sólo puede establecerse una vez", "argument.block.property.invalid": "El bloque %s no acepta «%s» para la propiedad «%s»", "argument.block.property.novalue": "Se requiere un valor para la propiedad «%s» del bloque %s", "argument.block.property.unclosed": "Se requiere «]» para cerrar las propiedades del bloque", "argument.block.property.unknown": "El bloque %s no posee la propiedad «%s»", "argument.block.tag.disallowed": "Los # no están permitidos acá, s<PERSON>lo bloques actuales", "argument.color.invalid": "Color desconocido: «%s»", "argument.component.invalid": "Componente de chat no válido: %s", "argument.criteria.invalid": "Criterio desconocido: «%s»", "argument.dimension.invalid": "La dimensión «%s» no existe", "argument.double.big": "El valor doble no puede ser mayor que %s (encontrado: %s)", "argument.double.low": "El valor doble no puede ser menor que %s (encontrado: %s)", "argument.entity.invalid": "Nombre o UUID no válidos", "argument.entity.notfound.entity": "No se encontraron entidades", "argument.entity.notfound.player": "No se encontraron jugadores", "argument.entity.options.advancements.description": "Jugadores con un determinado progreso", "argument.entity.options.distance.description": "Distancia de entidad", "argument.entity.options.distance.negative": "La distancia no puede ser negativa", "argument.entity.options.dx.description": "Entidades entre X y X+dX", "argument.entity.options.dy.description": "Entidades entre Y e Y+dY", "argument.entity.options.dz.description": "Entidades entre Z y Z+dZ", "argument.entity.options.gamemode.description": "Jugadores con el modo de juego", "argument.entity.options.inapplicable": "La opción «%s» no puede aplicarse acá", "argument.entity.options.level.description": "Nivel de experiencia", "argument.entity.options.level.negative": "El nivel no puede ser negativo", "argument.entity.options.limit.description": "Número máximo de entidades a devolver", "argument.entity.options.limit.toosmall": "El límite debe ser de al menos 1", "argument.entity.options.mode.invalid": "Modo de juego no válido o desconocido: %s", "argument.entity.options.name.description": "Nombre de entidad", "argument.entity.options.nbt.description": "Entidades con un determinado NBT", "argument.entity.options.predicate.description": "Predicado personalizado", "argument.entity.options.scores.description": "Entidades con un determinado puntaje", "argument.entity.options.sort.description": "Clasificar entidades", "argument.entity.options.sort.irreversible": "El tipo de clasificación «%s» es desconocido o no es válido", "argument.entity.options.tag.description": "Entidades con un determinado tag", "argument.entity.options.team.description": "Entidades en un determinado equipo", "argument.entity.options.type.description": "Entidades de un determinado tipo", "argument.entity.options.type.invalid": "Tipo de entidad desconocida o no válida: %s", "argument.entity.options.unknown": "Opción desconocida: %s", "argument.entity.options.unterminated": "Se requiere «]» para cerrar las opciones", "argument.entity.options.valueless": "Se requiere un valor para la opción «%s»", "argument.entity.options.x.description": "posición x", "argument.entity.options.x_rotation.description": "Rotación x de la entidad", "argument.entity.options.y.description": "posición y", "argument.entity.options.y_rotation.description": "Rotación y de la entidad", "argument.entity.options.z.description": "posición z", "argument.entity.selector.allEntities": "Todas las entidades", "argument.entity.selector.allPlayers": "Todos los jugadores", "argument.entity.selector.missing": "Falta el tipo de selector", "argument.entity.selector.nearestEntity": "Entidad más cercana", "argument.entity.selector.nearestPlayer": "Jugador más cercano", "argument.entity.selector.not_allowed": "Selector no permitido", "argument.entity.selector.randomPlayer": "Jugador al azar", "argument.entity.selector.self": "Entidad actual", "argument.entity.selector.unknown": "Tipo de selector desconocido: %s", "argument.entity.toomany": "<PERSON><PERSON>lo se permite una entidad, sin embargo el selector utilizado permite más de una", "argument.enum.invalid": "Valor «%s» no válido", "argument.float.big": "El valor float no puede ser mayor que %s (encontrado: %s)", "argument.float.low": "El valor float no puede ser menor que %s (encontrado: %s)", "argument.gamemode.invalid": "Modo de juego desconocido: %s", "argument.hexcolor.invalid": "Código de color hex. no válido: %s", "argument.id.invalid": "ID no válido", "argument.id.unknown": "ID desconocido: %s", "argument.integer.big": "El número entero no puede ser mayor que %s (encontrado: %s)", "argument.integer.low": "El número entero no puede ser menor que %s (encontrado: %s)", "argument.item.id.invalid": "Ítem desconocido: %s", "argument.item.tag.disallowed": "Los # no están permitidos acá, s<PERSON>lo objetos actuales", "argument.literal.incorrect": "Se requiere el valor literal «%s»", "argument.long.big": "El valor long no puede ser mayor que %s (encontrado: %s)", "argument.long.low": "El valor long no puede ser menor que %s (encontrado: %s)", "argument.message.too_long": "El mensaje es demasiado largo (%s > máximo %s caracteres)", "argument.nbt.array.invalid": "Tipo de array no válido: %s", "argument.nbt.array.mixed": "No se puede insertar %s en %s", "argument.nbt.expected.compound": "Se requiere etiqueta compuesta", "argument.nbt.expected.key": "Se requiere clave", "argument.nbt.expected.value": "Se requiere valor", "argument.nbt.list.mixed": "No se puede insertar %s en la lista de %s", "argument.nbt.trailing": "Hay datos de más", "argument.player.entities": "Este comando sólo afecta a jugadores, sin embargo el selector utilizado incluye entidades", "argument.player.toomany": "<PERSON><PERSON><PERSON> se permite un jugador, sin embargo el selector utilizado permite más de uno", "argument.player.unknown": "El jugador no existe", "argument.pos.missing.double": "Se requiere una coordenada", "argument.pos.missing.int": "Se requiere la ubicación de un bloque", "argument.pos.mixed": "No se puede usar una mezcla de coordenadas globales y locales (usá ^ con todas o con ninguna)", "argument.pos.outofbounds": "Esta posición está afuera de los límites permitidos.", "argument.pos.outofworld": "¡Esa ubicación está fuera del mundo!", "argument.pos.unloaded": "Esa ubicación no está cargada", "argument.pos2d.incomplete": "Incompleto (se requieren 2 coordenadas)", "argument.pos3d.incomplete": "Incompleto (se requieren 3 coordenadas)", "argument.range.empty": "Se requiere un valor o un intervalo de valores", "argument.range.ints": "<PERSON><PERSON><PERSON> se permiten números enteros, números decimales no", "argument.range.swapped": "El mínimo no puede ser mayor que el máximo", "argument.resource.invalid_type": "El elemento «%s» es del tipo incorrecto «%s» (se esperaba «%s»)", "argument.resource.not_found": "No se encontró el elemento «%s» de tipo «%s»", "argument.resource_or_id.failed_to_parse": "Error al analizar estructura: %s", "argument.resource_or_id.invalid": "Id. o etiqueta no válida", "argument.resource_or_id.no_such_element": "No se pudo encontrar el elemento «%s» en el registro «%s»", "argument.resource_selector.not_found": "Sin coincidencias para el selector «%s» del tipo «%s»", "argument.resource_tag.invalid_type": "La etiqueta «%s» es del tipo incorrecto «%s» (se esperaba «%s»)", "argument.resource_tag.not_found": "No se encontró la etiqueta «%s» de tipo «%s»", "argument.rotation.incomplete": "Incompleto (se requieren 2 coordenadas)", "argument.scoreHolder.empty": "No se encontraron marcadores de puntaje relevantes", "argument.scoreboardDisplaySlot.invalid": "Espacio de muestra desconocido: «%s»", "argument.style.invalid": "Estilo no válido: %s", "argument.time.invalid_tick_count": "El número de ticks no puede ser negativo", "argument.time.invalid_unit": "Unidad no válida", "argument.time.tick_count_too_low": "El número de ticks no puede ser menor a %s (encontrado: %s)", "argument.uuid.invalid": "UUID no válido", "argument.waypoint.invalid": "La entidad seleccionada no es un punto de ruta", "arguments.block.tag.unknown": "Tag de bloque desconocido: %s", "arguments.function.tag.unknown": "Tag de función desconocido: %s", "arguments.function.unknown": "Función desconocida: %s", "arguments.item.component.expected": "Se requiere componente de objeto", "arguments.item.component.malformed": "Componente «%s» mal formado: %s", "arguments.item.component.repeated": "Componente «%s» está repetido, pero solo se puede especificar un valor", "arguments.item.component.unknown": "Componente de objeto desconocido: %s", "arguments.item.malformed": "Objeto mal formado: %s", "arguments.item.overstacked": "%s sólo puede apilarse con hasta %s del mismo", "arguments.item.predicate.malformed": "Predicado «%s» mal formado: %s", "arguments.item.predicate.unknown": "Predicado de objeto desconocido: %s", "arguments.item.tag.unknown": "Tag de ítem desconocido: %s", "arguments.nbtpath.node.invalid": "Ruta NBT no válida", "arguments.nbtpath.nothing_found": "No se encontraron elementos que coincidan con %s", "arguments.nbtpath.too_deep": "La información NBT resultante está anidada muy profundamente", "arguments.nbtpath.too_large": "La información NBT resultante es muy grande", "arguments.objective.notFound": "Objetivo de tabla de puntaje desconocido: «%s»", "arguments.objective.readonly": "El objetivo «%s» de la tabla de puntaje sólo es de lectura", "arguments.operation.div0": "No se puede dividir entre cero", "arguments.operation.invalid": "Operación no válida", "arguments.swizzle.invalid": "Swizzle no válido: se requiere la combinación de «X», «Y» y «Z»", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%2$s: %1$s%%", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "%2$s: +%1$s", "attribute.modifier.plus.1": "%2$s: +%1$s%%", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "%2$s: -%1$s", "attribute.modifier.take.1": "%2$s: -%1$s%%", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "Dureza de armadura", "attribute.name.attack_damage": "Daño de ataque", "attribute.name.attack_knockback": "Empuje de ataque", "attribute.name.attack_speed": "Velocidad de ataque", "attribute.name.block_break_speed": "Velocidad al romper bloques", "attribute.name.block_interaction_range": "Rango de interacción con bloques", "attribute.name.burning_time": "Duración de quemadura", "attribute.name.camera_distance": "Distancia de la cámara", "attribute.name.entity_interaction_range": "Rango de interacción con entidades", "attribute.name.explosion_knockback_resistance": "Resistencia al empuje de explosiones", "attribute.name.fall_damage_multiplier": "Multiplicador de daño por caída", "attribute.name.flying_speed": "Velocidad de vuelo", "attribute.name.follow_range": "<PERSON>ngo de seguimiento de criaturas", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Dureza de armadura", "attribute.name.generic.attack_damage": "<PERSON><PERSON> por golpe", "attribute.name.generic.attack_knockback": "Empuje de ataque", "attribute.name.generic.attack_speed": "Velocidad de ataque", "attribute.name.generic.block_interaction_range": "Rango de interacción con bloques", "attribute.name.generic.burning_time": "Duración de quemadura", "attribute.name.generic.entity_interaction_range": "Rango de interacción con entidades", "attribute.name.generic.explosion_knockback_resistance": "Resistencia al empuje de las explosiones", "attribute.name.generic.fall_damage_multiplier": "Multiplicador de daño por caída", "attribute.name.generic.flying_speed": "Velocidad de vuelo", "attribute.name.generic.follow_range": "<PERSON><PERSON>", "attribute.name.generic.gravity": "Gravedad", "attribute.name.generic.jump_strength": "Potencia de salto", "attribute.name.generic.knockback_resistance": "Resistencia al empuje", "attribute.name.generic.luck": "<PERSON><PERSON>", "attribute.name.generic.max_absorption": "Absorción máxima", "attribute.name.generic.max_health": "<PERSON><PERSON> m<PERSON>xi<PERSON>", "attribute.name.generic.movement_efficiency": "Eficiencia de movimiento", "attribute.name.generic.movement_speed": "Velocidad", "attribute.name.generic.oxygen_bonus": "Oxígeno extra", "attribute.name.generic.safe_fall_distance": "Distancia de caída segura", "attribute.name.generic.scale": "Escala", "attribute.name.generic.step_height": "Altura de paso", "attribute.name.generic.water_movement_efficiency": "Eficiencia de movimiento acuático", "attribute.name.gravity": "Gravedad", "attribute.name.horse.jump_strength": "Fuerza de salto", "attribute.name.jump_strength": "Potencia de salto", "attribute.name.knockback_resistance": "Resistencia al empuje", "attribute.name.luck": "<PERSON><PERSON>", "attribute.name.max_absorption": "Absorción máxima", "attribute.name.max_health": "<PERSON><PERSON> m<PERSON>xi<PERSON>", "attribute.name.mining_efficiency": "Eficiencia al romper bloques", "attribute.name.movement_efficiency": "Eficiencia de movimiento", "attribute.name.movement_speed": "Velocidad", "attribute.name.oxygen_bonus": "Oxígeno extra", "attribute.name.player.block_break_speed": "Velocidad al romper bloques", "attribute.name.player.block_interaction_range": "Rango de interacción con bloques", "attribute.name.player.entity_interaction_range": "Rango de interacción con entidades", "attribute.name.player.mining_efficiency": "Eficiencia al romper bloques", "attribute.name.player.sneaking_speed": "Velocidad al agacharse", "attribute.name.player.submerged_mining_speed": "Velocidad al romper bloques sumergido", "attribute.name.player.sweeping_damage_ratio": "Proporción del daño de barrido", "attribute.name.safe_fall_distance": "Distancia de caída segura", "attribute.name.scale": "Escala", "attribute.name.sneaking_speed": "Velocidad al agacharse", "attribute.name.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>i", "attribute.name.step_height": "Altura de paso", "attribute.name.submerged_mining_speed": "Velocidad al romper bloques sumergido", "attribute.name.sweeping_damage_ratio": "Proporción del daño de barrido", "attribute.name.tempt_range": "Radio de atracción de criaturas", "attribute.name.water_movement_efficiency": "Eficiencia de movimiento acuático", "attribute.name.waypoint_receive_range": "Alcance de recepción de punto de ruta", "attribute.name.waypoint_transmit_range": "Alcance de transmisión de punto de ruta", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>i", "biome.minecraft.badlands": "Badlands", "biome.minecraft.bamboo_jungle": "<PERSON><PERSON>", "biome.minecraft.basalt_deltas": "Deltas de basalto", "biome.minecraft.beach": "Playa", "biome.minecraft.birch_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.cherry_grove": "Cerezal", "biome.minecraft.cold_ocean": "Océano frío", "biome.minecraft.crimson_forest": "<PERSON><PERSON>", "biome.minecraft.dark_forest": "Bosque oscuro", "biome.minecraft.deep_cold_ocean": "Océano frío profundo", "biome.minecraft.deep_dark": "Oscuridad profunda", "biome.minecraft.deep_frozen_ocean": "Océano congelado profundo", "biome.minecraft.deep_lukewarm_ocean": "Océano tibio profundo", "biome.minecraft.deep_ocean": "Océano profundo", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "<PERSON><PERSON><PERSON>", "biome.minecraft.end_barrens": "El End - Zona árida", "biome.minecraft.end_highlands": "El End - Zona alta", "biome.minecraft.end_midlands": "El End - Zona central", "biome.minecraft.eroded_badlands": "Badlands erosionadas", "biome.minecraft.flower_forest": "Bosque floral", "biome.minecraft.forest": "Bosque", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON> congelado", "biome.minecraft.frozen_peaks": "Cumbres heladas", "biome.minecraft.frozen_river": "Río congelado", "biome.minecraft.grove": "Arboleda", "biome.minecraft.ice_spikes": "Picos de hielo", "biome.minecraft.jagged_peaks": "Cumbres escarpadas", "biome.minecraft.jungle": "<PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "Océano tibio", "biome.minecraft.lush_caves": "<PERSON><PERSON>s fron<PERSON>", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.meadow": "Prados", "biome.minecraft.mushroom_fields": "Campo fúngico", "biome.minecraft.nether_wastes": "Desiertos del Nether", "biome.minecraft.ocean": "<PERSON><PERSON>ano", "biome.minecraft.old_growth_birch_forest": "Abedular ancestral", "biome.minecraft.old_growth_pine_taiga": "<PERSON><PERSON> de pinos ancestral", "biome.minecraft.old_growth_spruce_taiga": "<PERSON><PERSON> de abetos ancestral", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON>", "biome.minecraft.plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.river": "Río", "biome.minecraft.savanna": "Sabana", "biome.minecraft.savanna_plateau": "Meseta de sabana", "biome.minecraft.small_end_islands": "El End - Islas chicas", "biome.minecraft.snowy_beach": "Playa nevada", "biome.minecraft.snowy_plains": "<PERSON><PERSON><PERSON> nevada", "biome.minecraft.snowy_slopes": "Pendientes nevadas", "biome.minecraft.snowy_taiga": "Taiga nevada", "biome.minecraft.soul_sand_valley": "Valle de almas", "biome.minecraft.sparse_jungle": "Jungla dispersa", "biome.minecraft.stony_peaks": "Cumbres rocosas", "biome.minecraft.stony_shore": "Costa rocosa", "biome.minecraft.sunflower_plains": "Llanura de girasoles", "biome.minecraft.swamp": "Pantano", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "El End", "biome.minecraft.the_void": "El vacío", "biome.minecraft.warm_ocean": "Océano <PERSON>", "biome.minecraft.warped_forest": "Bosque distorsionado", "biome.minecraft.windswept_forest": "<PERSON><PERSON> ventiscoso", "biome.minecraft.windswept_gravelly_hills": "Colinas de grava ventiscosas", "biome.minecraft.windswept_hills": "Colinas ventiscosas", "biome.minecraft.windswept_savanna": "Sabana ventiscosa", "biome.minecraft.wooded_badlands": "Páramo frondoso", "block.minecraft.acacia_button": "Botón de acacia", "block.minecraft.acacia_door": "Puerta de acacia", "block.minecraft.acacia_fence": "Reja de acacia", "block.minecraft.acacia_fence_gate": "Portón de acacia", "block.minecraft.acacia_hanging_sign": "Cartel colgante de acacia", "block.minecraft.acacia_leaves": "Hojas de acacia", "block.minecraft.acacia_log": "Tronco de acacia", "block.minecraft.acacia_planks": "Madera de acacia", "block.minecraft.acacia_pressure_plate": "Placa de presión de acacia", "block.minecraft.acacia_sapling": "Brote de acacia", "block.minecraft.acacia_sign": "Cartel de acacia", "block.minecraft.acacia_slab": "Baldosa de acacia", "block.minecraft.acacia_stairs": "Escaleras de acacia", "block.minecraft.acacia_trapdoor": "Escotilla de acacia", "block.minecraft.acacia_wall_hanging_sign": "Cartel colgante de acacia en pared", "block.minecraft.acacia_wall_sign": "Cartel de acacia en pared", "block.minecraft.acacia_wood": "Leño de acacia", "block.minecraft.activator_rail": "Vía activadora", "block.minecraft.air": "Aire", "block.minecraft.allium": "Allium", "block.minecraft.amethyst_block": "Bloque de amatista", "block.minecraft.amethyst_cluster": "Clúster de amatista", "block.minecraft.ancient_debris": "Escombros ancestrales", "block.minecraft.andesite": "Andesita", "block.minecraft.andesite_slab": "Baldosa de andesita", "block.minecraft.andesite_stairs": "Escaleras de andesita", "block.minecraft.andesite_wall": "<PERSON><PERSON> de and<PERSON>", "block.minecraft.anvil": "Yun<PERSON>", "block.minecraft.attached_melon_stem": "Tallo de sandía unido", "block.minecraft.attached_pumpkin_stem": "Tallo de zapallo unido", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Hojas de azalea", "block.minecraft.azure_bluet": "Rubiácea", "block.minecraft.bamboo": "Bambú", "block.minecraft.bamboo_block": "Bloque de bam<PERSON>ú", "block.minecraft.bamboo_button": "Botón de bam<PERSON>ú", "block.minecraft.bamboo_door": "<PERSON><PERSON><PERSON> de b<PERSON>ú", "block.minecraft.bamboo_fence": "<PERSON><PERSON>", "block.minecraft.bamboo_fence_gate": "Portón de bambú", "block.minecraft.bamboo_hanging_sign": "Cartel colgante de bambú", "block.minecraft.bamboo_mosaic": "Mosaico de bam<PERSON>ú", "block.minecraft.bamboo_mosaic_slab": "Baldosa de mosaico de bam<PERSON>ú", "block.minecraft.bamboo_mosaic_stairs": "Escaleras de mosaico de bambú", "block.minecraft.bamboo_planks": "<PERSON><PERSON>", "block.minecraft.bamboo_pressure_plate": "Placa de presión de bambú", "block.minecraft.bamboo_sapling": "<PERSON><PERSON>", "block.minecraft.bamboo_sign": "Cartel de bambú", "block.minecraft.bamboo_slab": "<PERSON>ldo<PERSON> de bam<PERSON>", "block.minecraft.bamboo_stairs": "Escaleras de bambú", "block.minecraft.bamboo_trapdoor": "Escotilla de bam<PERSON>ú", "block.minecraft.bamboo_wall_hanging_sign": "Cartel colgante de bambú en pared", "block.minecraft.bamboo_wall_sign": "Cartel de bambú en pared", "block.minecraft.banner.base.black": "Paño completamente negro", "block.minecraft.banner.base.blue": "Paño completamente azul", "block.minecraft.banner.base.brown": "<PERSON><PERSON> completamente marr<PERSON>", "block.minecraft.banner.base.cyan": "<PERSON><PERSON> completamente cian", "block.minecraft.banner.base.gray": "Pa<PERSON> completamente gris", "block.minecraft.banner.base.green": "Paño completamente verde", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON> completamente celeste", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON> completamente gris claro", "block.minecraft.banner.base.lime": "Paño completamente verde lima", "block.minecraft.banner.base.magenta": "Paño completamente magenta", "block.minecraft.banner.base.orange": "Paño completamente naranja", "block.minecraft.banner.base.pink": "<PERSON><PERSON> completamente rosa", "block.minecraft.banner.base.purple": "<PERSON><PERSON> completamente violeta", "block.minecraft.banner.base.red": "Paño completamente rojo", "block.minecraft.banner.base.white": "Paño completamente blanco", "block.minecraft.banner.base.yellow": "<PERSON><PERSON> completamente amarillo", "block.minecraft.banner.border.black": "Bordura negra", "block.minecraft.banner.border.blue": "Bordura azul", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.border.green": "Bordura verde", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON> celeste", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.banner.border.lime": "Bordura verde lima", "block.minecraft.banner.border.magenta": "Bordura magenta", "block.minecraft.banner.border.orange": "<PERSON><PERSON><PERSON> naranja", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.border.purple": "<PERSON><PERSON><PERSON> violeta", "block.minecraft.banner.border.red": "Bord<PERSON> roja", "block.minecraft.banner.border.white": "<PERSON>rd<PERSON> blanca", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON> amarilla", "block.minecraft.banner.bricks.black": "Mazonado negro", "block.minecraft.banner.bricks.blue": "Mazonado azul", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.bricks.cyan": "Ma<PERSON><PERSON> cian", "block.minecraft.banner.bricks.gray": "Mazonado gris", "block.minecraft.banner.bricks.green": "Mazonado verde", "block.minecraft.banner.bricks.light_blue": "Mazonado celeste", "block.minecraft.banner.bricks.light_gray": "Mazonado gris claro", "block.minecraft.banner.bricks.lime": "Mazonado verde lima", "block.minecraft.banner.bricks.magenta": "Mazonado magenta", "block.minecraft.banner.bricks.orange": "Ma<PERSON>ado naranja", "block.minecraft.banner.bricks.pink": "Mazonado rosa", "block.minecraft.banner.bricks.purple": "Ma<PERSON><PERSON> violeta", "block.minecraft.banner.bricks.red": "Mazonado rojo", "block.minecraft.banner.bricks.white": "Mazonado blanco", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON> am<PERSON>", "block.minecraft.banner.circle.black": "<PERSON><PERSON> negro", "block.minecraft.banner.circle.blue": "<PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.circle.green": "<PERSON><PERSON> verde", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON> cele<PERSON>", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON> gris claro", "block.minecraft.banner.circle.lime": "<PERSON>l verde lima", "block.minecraft.banner.circle.magenta": "Roel magenta", "block.minecraft.banner.circle.orange": "<PERSON><PERSON>", "block.minecraft.banner.circle.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.circle.purple": "<PERSON><PERSON>a", "block.minecraft.banner.circle.red": "<PERSON><PERSON> rojo", "block.minecraft.banner.circle.white": "<PERSON><PERSON> blan<PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON>", "block.minecraft.banner.creeper.black": "Estampado negro de creeper", "block.minecraft.banner.creeper.blue": "Estampado azul de creeper", "block.minecraft.banner.creeper.brown": "Estampado marr<PERSON> de creeper", "block.minecraft.banner.creeper.cyan": "Estampado cian <PERSON> creeper", "block.minecraft.banner.creeper.gray": "Estampado gris de creeper", "block.minecraft.banner.creeper.green": "Estampado verde de creeper", "block.minecraft.banner.creeper.light_blue": "Estampado celeste de creeper", "block.minecraft.banner.creeper.light_gray": "Estampado gris claro de creeper", "block.minecraft.banner.creeper.lime": "Estampado verde lima de creeper", "block.minecraft.banner.creeper.magenta": "Estampado magenta de creeper", "block.minecraft.banner.creeper.orange": "Estampado naranja de creeper", "block.minecraft.banner.creeper.pink": "Estampado rosa de creeper", "block.minecraft.banner.creeper.purple": "Estampado violeta de creeper", "block.minecraft.banner.creeper.red": "Estampado rojo de creeper", "block.minecraft.banner.creeper.white": "Estampado blanco de creeper", "block.minecraft.banner.creeper.yellow": "Estampado amariillo de creeper", "block.minecraft.banner.cross.black": "<PERSON><PERSON> negra", "block.minecraft.banner.cross.blue": "<PERSON><PERSON> azul", "block.minecraft.banner.cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.cross.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.cross.green": "<PERSON>pa verde", "block.minecraft.banner.cross.light_blue": "<PERSON><PERSON> celeste", "block.minecraft.banner.cross.light_gray": "<PERSON><PERSON> gris claro", "block.minecraft.banner.cross.lime": "Aspa verde lima", "block.minecraft.banner.cross.magenta": "Aspa magenta", "block.minecraft.banner.cross.orange": "<PERSON><PERSON> naranja", "block.minecraft.banner.cross.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.cross.purple": "<PERSON><PERSON> violeta", "block.minecraft.banner.cross.red": "<PERSON><PERSON> roja", "block.minecraft.banner.cross.white": "As<PERSON> blanca", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON> amarilla", "block.minecraft.banner.curly_border.black": "Bordura negra dentada", "block.minecraft.banner.curly_border.blue": "Bordura azul dentada", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> marr<PERSON> dentada", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON> cian dentada", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON> gris dentada", "block.minecraft.banner.curly_border.green": "<PERSON>rd<PERSON> verde dentada", "block.minecraft.banner.curly_border.light_blue": "Bordura celeste dentada", "block.minecraft.banner.curly_border.light_gray": "<PERSON>rdura gris claro dentada", "block.minecraft.banner.curly_border.lime": "Bordura verde lima dentada", "block.minecraft.banner.curly_border.magenta": "Bordura magenta dentada", "block.minecraft.banner.curly_border.orange": "<PERSON>rd<PERSON> naranja dentada", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON> rosa dentada", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON><PERSON> violeta dentada", "block.minecraft.banner.curly_border.red": "<PERSON>rd<PERSON> roja dentada", "block.minecraft.banner.curly_border.white": "Bordura blanca dentada", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON><PERSON> amarilla dentada", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON><PERSON> negro", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON> verde", "block.minecraft.banner.diagonal_left.light_blue": "<PERSON><PERSON><PERSON> cele<PERSON>", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.banner.diagonal_left.lime": "<PERSON><PERSON>do verde lima", "block.minecraft.banner.diagonal_left.magenta": "Tajado magenta", "block.minecraft.banner.diagonal_left.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.black": "Tronchado negro", "block.minecraft.banner.diagonal_right.blue": "Tron<PERSON>do <PERSON>l", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON><PERSON>ian", "block.minecraft.banner.diagonal_right.gray": "Tronchado gris", "block.minecraft.banner.diagonal_right.green": "Tronchado verde", "block.minecraft.banner.diagonal_right.light_blue": "<PERSON><PERSON><PERSON><PERSON> celeste", "block.minecraft.banner.diagonal_right.light_gray": "Tronchado gris claro", "block.minecraft.banner.diagonal_right.lime": "Tronchado verde lima", "block.minecraft.banner.diagonal_right.magenta": "Tronchado magenta", "block.minecraft.banner.diagonal_right.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.diagonal_right.purple": "T<PERSON><PERSON><PERSON> violeta", "block.minecraft.banner.diagonal_right.red": "Tron<PERSON>do rojo", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.black": "Tronchado negro invertido", "block.minecraft.banner.diagonal_up_left.blue": "Tronchado azul invertido", "block.minecraft.banner.diagonal_up_left.brown": "Tronchado marrón invertido", "block.minecraft.banner.diagonal_up_left.cyan": "Tronchado cian invertido", "block.minecraft.banner.diagonal_up_left.gray": "Tronchado gris invertido", "block.minecraft.banner.diagonal_up_left.green": "Tronchado verde invertido", "block.minecraft.banner.diagonal_up_left.light_blue": "Tronchado celeste invertido", "block.minecraft.banner.diagonal_up_left.light_gray": "Tronchado gris claro invertido", "block.minecraft.banner.diagonal_up_left.lime": "Tronchado verde lima invertido", "block.minecraft.banner.diagonal_up_left.magenta": "Tronchado magenta invertido", "block.minecraft.banner.diagonal_up_left.orange": "Tronchado naranja invertido", "block.minecraft.banner.diagonal_up_left.pink": "Tronchado rosa invertido", "block.minecraft.banner.diagonal_up_left.purple": "Tronchado violeta invertido", "block.minecraft.banner.diagonal_up_left.red": "Tronchado rojo invertido", "block.minecraft.banner.diagonal_up_left.white": "Tronchado blanco invertido", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON><PERSON><PERSON> amarillo invertido", "block.minecraft.banner.diagonal_up_right.black": "Tajado negro invertido", "block.minecraft.banner.diagonal_up_right.blue": "Tajado a<PERSON>l invertido", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON><PERSON> ma<PERSON> invertido", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON><PERSON> cian invertido", "block.minecraft.banner.diagonal_up_right.gray": "Tajado gris invertido", "block.minecraft.banner.diagonal_up_right.green": "Tajado verde invertido", "block.minecraft.banner.diagonal_up_right.light_blue": "Tajado celeste invertido", "block.minecraft.banner.diagonal_up_right.light_gray": "<PERSON><PERSON>do gris claro invertido", "block.minecraft.banner.diagonal_up_right.lime": "Tajado verde lima invertido", "block.minecraft.banner.diagonal_up_right.magenta": "Tajado magenta invertido", "block.minecraft.banner.diagonal_up_right.orange": "<PERSON><PERSON><PERSON> na<PERSON>ja invertido", "block.minecraft.banner.diagonal_up_right.pink": "Ta<PERSON>do rosa invertido", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON>do violeta invertido", "block.minecraft.banner.diagonal_up_right.red": "Tajado rojo invertido", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON> blanco invertido", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON> am<PERSON>llo invertido", "block.minecraft.banner.flow.black": "Estampado negro de espiral", "block.minecraft.banner.flow.blue": "Estampado azul de espiral", "block.minecraft.banner.flow.brown": "Estampado marrón de espiral", "block.minecraft.banner.flow.cyan": "Estampado cian de es<PERSON>ral", "block.minecraft.banner.flow.gray": "Estampado gris de espiral", "block.minecraft.banner.flow.green": "Estampado verde de espiral", "block.minecraft.banner.flow.light_blue": "Estampado celeste de espiral", "block.minecraft.banner.flow.light_gray": "Estampado gris claro de es<PERSON>ral", "block.minecraft.banner.flow.lime": "Estampado verde lima de espiral", "block.minecraft.banner.flow.magenta": "Estampado magenta de espiral", "block.minecraft.banner.flow.orange": "Estampado naranja de espiral", "block.minecraft.banner.flow.pink": "Estampado rosa de espiral", "block.minecraft.banner.flow.purple": "Estampado violeta de espiral", "block.minecraft.banner.flow.red": "Estampado rojo de espiral", "block.minecraft.banner.flow.white": "Estampado blanco de espiral", "block.minecraft.banner.flow.yellow": "Estampado amarillo de espiral", "block.minecraft.banner.flower.black": "Estampado negro de flor", "block.minecraft.banner.flower.blue": "Estampado azul de flor", "block.minecraft.banner.flower.brown": "Estampado marrón de flor", "block.minecraft.banner.flower.cyan": "Estampado cian de flor", "block.minecraft.banner.flower.gray": "Estampado gris de flor", "block.minecraft.banner.flower.green": "Estampado verde de flor", "block.minecraft.banner.flower.light_blue": "Estampado celeste de flor", "block.minecraft.banner.flower.light_gray": "Estampado gris claro de flor", "block.minecraft.banner.flower.lime": "Estampado verde lima de flor", "block.minecraft.banner.flower.magenta": "Estampado magenta de flor", "block.minecraft.banner.flower.orange": "Estampado naranja de flor", "block.minecraft.banner.flower.pink": "Estampado rosa de flor", "block.minecraft.banner.flower.purple": "Estampado violeta de flor", "block.minecraft.banner.flower.red": "Estampado rojo de flor", "block.minecraft.banner.flower.white": "Estampado blanco de flor", "block.minecraft.banner.flower.yellow": "Estampado amarillo de flor", "block.minecraft.banner.globe.black": "Planeta negro", "block.minecraft.banner.globe.blue": "Planeta azul", "block.minecraft.banner.globe.brown": "Planeta marrón", "block.minecraft.banner.globe.cyan": "Planeta cian", "block.minecraft.banner.globe.gray": "Planeta gris", "block.minecraft.banner.globe.green": "Planeta verde", "block.minecraft.banner.globe.light_blue": "Planeta celeste", "block.minecraft.banner.globe.light_gray": "Planeta gris claro", "block.minecraft.banner.globe.lime": "Planeta verde lima", "block.minecraft.banner.globe.magenta": "Planeta magenta", "block.minecraft.banner.globe.orange": "Planeta naranja", "block.minecraft.banner.globe.pink": "Planeta rosa", "block.minecraft.banner.globe.purple": "Planeta violeta", "block.minecraft.banner.globe.red": "Planeta rojo", "block.minecraft.banner.globe.white": "Planeta blanco", "block.minecraft.banner.globe.yellow": "Planeta amarillo", "block.minecraft.banner.gradient.black": "Gradiente negro", "block.minecraft.banner.gradient.blue": "Gradiente azul", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "Gradiente cian", "block.minecraft.banner.gradient.gray": "Gradiente gris", "block.minecraft.banner.gradient.green": "Gradiente verde", "block.minecraft.banner.gradient.light_blue": "Gradiente celeste", "block.minecraft.banner.gradient.light_gray": "Grad<PERSON>e gris claro", "block.minecraft.banner.gradient.lime": "Gradiente verde lima", "block.minecraft.banner.gradient.magenta": "Gradiente magenta", "block.minecraft.banner.gradient.orange": "Gradiente naranja", "block.minecraft.banner.gradient.pink": "Gradiente rosa", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON><PERSON><PERSON> violeta", "block.minecraft.banner.gradient.red": "Gradiente rojo", "block.minecraft.banner.gradient.white": "Gradiente blanco", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON><PERSON> am<PERSON>llo", "block.minecraft.banner.gradient_up.black": "Gradiente negro en base", "block.minecraft.banner.gradient_up.blue": "Gradiente azul en base", "block.minecraft.banner.gradient_up.brown": "Gradiente marrón en base", "block.minecraft.banner.gradient_up.cyan": "Gradiente cian en base", "block.minecraft.banner.gradient_up.gray": "Gradiente gris en base", "block.minecraft.banner.gradient_up.green": "Gradiente verde en base", "block.minecraft.banner.gradient_up.light_blue": "Gradiente celeste en base", "block.minecraft.banner.gradient_up.light_gray": "Gradiente gris claro en base", "block.minecraft.banner.gradient_up.lime": "Gradiente verde lima en base", "block.minecraft.banner.gradient_up.magenta": "Gradiente magenta en base", "block.minecraft.banner.gradient_up.orange": "Gradiente naranja en base", "block.minecraft.banner.gradient_up.pink": "Gradiente rosa en base", "block.minecraft.banner.gradient_up.purple": "Gradiente violeta en base", "block.minecraft.banner.gradient_up.red": "Gradiente rojo en base", "block.minecraft.banner.gradient_up.white": "Gradiente blanco en base", "block.minecraft.banner.gradient_up.yellow": "Gradiente amarillo en base", "block.minecraft.banner.guster.black": "Estampado negro de breeze", "block.minecraft.banner.guster.blue": "Estampado azul de breeze", "block.minecraft.banner.guster.brown": "Estampado marrón de breeze", "block.minecraft.banner.guster.cyan": "Estampado cian de <PERSON>", "block.minecraft.banner.guster.gray": "Estampado gris de breeze", "block.minecraft.banner.guster.green": "Estampado verde de breeze", "block.minecraft.banner.guster.light_blue": "Estampado celeste de breeze", "block.minecraft.banner.guster.light_gray": "Estampado gris claro de breeze", "block.minecraft.banner.guster.lime": "Estampado verde lima de breeze", "block.minecraft.banner.guster.magenta": "Estampado magenta de breeze", "block.minecraft.banner.guster.orange": "Estampado naranja de breeze", "block.minecraft.banner.guster.pink": "Estampado rosa de breeze", "block.minecraft.banner.guster.purple": "Estampado violeta de breeze", "block.minecraft.banner.guster.red": "Estampado rojo de breeze", "block.minecraft.banner.guster.white": "Estampado blanco de breeze", "block.minecraft.banner.guster.yellow": "Estampado amarillo de <PERSON>", "block.minecraft.banner.half_horizontal.black": "Cortado negro", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON> a<PERSON>l", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.half_horizontal.gray": "Cortado gris", "block.minecraft.banner.half_horizontal.green": "Cortado verde", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON><PERSON> celeste", "block.minecraft.banner.half_horizontal.light_gray": "Cortado gris claro", "block.minecraft.banner.half_horizontal.lime": "Cortado verde lima", "block.minecraft.banner.half_horizontal.magenta": "Cortado magenta", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON><PERSON> violeta", "block.minecraft.banner.half_horizontal.red": "<PERSON>rtado rojo", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.black": "Cortado negro invertido", "block.minecraft.banner.half_horizontal_bottom.blue": "Cortado azul invertido", "block.minecraft.banner.half_horizontal_bottom.brown": "Cortado marrón invertido", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON>rtado cian invertido", "block.minecraft.banner.half_horizontal_bottom.gray": "Cortado gris invertido", "block.minecraft.banner.half_horizontal_bottom.green": "Cortado verde invertido", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Cortado celeste invertido", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Cortado gris claro invertido", "block.minecraft.banner.half_horizontal_bottom.lime": "Cortado verde lima invertido", "block.minecraft.banner.half_horizontal_bottom.magenta": "Cortado magenta invertido", "block.minecraft.banner.half_horizontal_bottom.orange": "Cortado naranja invertido", "block.minecraft.banner.half_horizontal_bottom.pink": "Cortado rosa invertido", "block.minecraft.banner.half_horizontal_bottom.purple": "Cortado violeta invertido", "block.minecraft.banner.half_horizontal_bottom.red": "Cortado rojo invertido", "block.minecraft.banner.half_horizontal_bottom.white": "Cortado blanco invertido", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON><PERSON> amarillo invertido", "block.minecraft.banner.half_vertical.black": "Flanco negro a diestra", "block.minecraft.banner.half_vertical.blue": "Flanco azul a diestra", "block.minecraft.banner.half_vertical.brown": "<PERSON>lanco marr<PERSON> a diestra", "block.minecraft.banner.half_vertical.cyan": "<PERSON>lan<PERSON> cian a diestra", "block.minecraft.banner.half_vertical.gray": "Flanco gris a diestra", "block.minecraft.banner.half_vertical.green": "Flanco verde a diestra", "block.minecraft.banner.half_vertical.light_blue": "Flanco celeste a diestra", "block.minecraft.banner.half_vertical.light_gray": "Flanco gris claro a diestra", "block.minecraft.banner.half_vertical.lime": "Flanco verde lima a diestra", "block.minecraft.banner.half_vertical.magenta": "Flanco magenta a diestra", "block.minecraft.banner.half_vertical.orange": "Flanco naranja a diestra", "block.minecraft.banner.half_vertical.pink": "<PERSON>lanco rosa a diestra", "block.minecraft.banner.half_vertical.purple": "Flanco violeta a diestra", "block.minecraft.banner.half_vertical.red": "<PERSON>lanco rojo a diestra", "block.minecraft.banner.half_vertical.white": "Flanco blanco a diestra", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON><PERSON> amarillo a diestra", "block.minecraft.banner.half_vertical_right.black": "Flanco negro a siniestra", "block.minecraft.banner.half_vertical_right.blue": "Flanco azul a siniestra", "block.minecraft.banner.half_vertical_right.brown": "Flanco marrón a siniestra", "block.minecraft.banner.half_vertical_right.cyan": "Flanco cian a siniestra", "block.minecraft.banner.half_vertical_right.gray": "Flanco gris a siniestra", "block.minecraft.banner.half_vertical_right.green": "Flanco verde a siniestra", "block.minecraft.banner.half_vertical_right.light_blue": "Flanco celeste a siniestra", "block.minecraft.banner.half_vertical_right.light_gray": "Flanco gris claro a siniestra", "block.minecraft.banner.half_vertical_right.lime": "Flanco verde lima a siniestra", "block.minecraft.banner.half_vertical_right.magenta": "Flanco magenta a siniestra", "block.minecraft.banner.half_vertical_right.orange": "Flanco naranja a siniestra", "block.minecraft.banner.half_vertical_right.pink": "Flanco rosa a siniestra", "block.minecraft.banner.half_vertical_right.purple": "Flanco violeta a siniestra", "block.minecraft.banner.half_vertical_right.red": "Flanco rojo a siniestra", "block.minecraft.banner.half_vertical_right.white": "Flanco blanco a siniestra", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON>lanco amarillo a siniestra", "block.minecraft.banner.mojang.black": "Cosa negra", "block.minecraft.banner.mojang.blue": "Cosa a<PERSON>l", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.mojang.gray": "<PERSON>sa gris", "block.minecraft.banner.mojang.green": "Cosa verde", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON> celeste", "block.minecraft.banner.mojang.light_gray": "<PERSON>sa gris claro", "block.minecraft.banner.mojang.lime": "Cosa verde lima", "block.minecraft.banner.mojang.magenta": "Cosa magenta", "block.minecraft.banner.mojang.orange": "Cosa naranja", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON> violeta", "block.minecraft.banner.mojang.red": "Cosa roja", "block.minecraft.banner.mojang.white": "Cosa blanca", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON> amarilla", "block.minecraft.banner.piglin.black": "Hocico negro", "block.minecraft.banner.piglin.blue": "Hocico azul", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.piglin.gray": "Hocico gris", "block.minecraft.banner.piglin.green": "Hocico verde", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON><PERSON> cele<PERSON>", "block.minecraft.banner.piglin.light_gray": "<PERSON>cico gris claro", "block.minecraft.banner.piglin.lime": "Hocico verde lima", "block.minecraft.banner.piglin.magenta": "Hocico magenta", "block.minecraft.banner.piglin.orange": "Hocico na<PERSON>", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.piglin.purple": "Hocico violeta", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.black": "Rombo negro", "block.minecraft.banner.rhombus.blue": "Rombo azul", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.rhombus.gray": "Rombo gris", "block.minecraft.banner.rhombus.green": "Rombo verde", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON> celeste", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON>o gris claro", "block.minecraft.banner.rhombus.lime": "Rombo verde lima", "block.minecraft.banner.rhombus.magenta": "Rombo magenta", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON><PERSON> violeta", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON>o rojo", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON>o blanco", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON> am<PERSON>", "block.minecraft.banner.skull.black": "Estampado negro de cráneo", "block.minecraft.banner.skull.blue": "Estampado azul de cráneo", "block.minecraft.banner.skull.brown": "Estampado marrón de cráneo", "block.minecraft.banner.skull.cyan": "Estampado cian de cr<PERSON>eo", "block.minecraft.banner.skull.gray": "Estampado gris de cráneo", "block.minecraft.banner.skull.green": "Estampado verde de cráneo", "block.minecraft.banner.skull.light_blue": "Estampado celeste de cráneo", "block.minecraft.banner.skull.light_gray": "Estampado gris claro de cráneo", "block.minecraft.banner.skull.lime": "Estampado verde lima de cráneo", "block.minecraft.banner.skull.magenta": "Estampado magenta de cráneo", "block.minecraft.banner.skull.orange": "Estampado naranja de cráneo", "block.minecraft.banner.skull.pink": "Estampado rosa de cráneo", "block.minecraft.banner.skull.purple": "Estampado violeta de cráneo", "block.minecraft.banner.skull.red": "Estampado rojo de cráneo", "block.minecraft.banner.skull.white": "Estampado blanco de cráneo", "block.minecraft.banner.skull.yellow": "Estampado amarillo de cráneo", "block.minecraft.banner.small_stripes.black": "Bastonado negro", "block.minecraft.banner.small_stripes.blue": "Bastonado azul", "block.minecraft.banner.small_stripes.brown": "Bastonado marrón", "block.minecraft.banner.small_stripes.cyan": "Ba<PERSON><PERSON> cian", "block.minecraft.banner.small_stripes.gray": "Bastonado gris", "block.minecraft.banner.small_stripes.green": "Bastonado verde", "block.minecraft.banner.small_stripes.light_blue": "Bastonado celeste", "block.minecraft.banner.small_stripes.light_gray": "Bastonado gris claro", "block.minecraft.banner.small_stripes.lime": "Bastonado verde lima", "block.minecraft.banner.small_stripes.magenta": "Bastonado magenta", "block.minecraft.banner.small_stripes.orange": "Bastonado naranja", "block.minecraft.banner.small_stripes.pink": "Bastonado rosa", "block.minecraft.banner.small_stripes.purple": "Bastonado violeta", "block.minecraft.banner.small_stripes.red": "Bastonado rojo", "block.minecraft.banner.small_stripes.white": "Bastonado blanco", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON><PERSON> amarillo", "block.minecraft.banner.square_bottom_left.black": "Cantón negro en base a diestra", "block.minecraft.banner.square_bottom_left.blue": "Cantón azul en base a diestra", "block.minecraft.banner.square_bottom_left.brown": "Cantón marrón en base a diestra", "block.minecraft.banner.square_bottom_left.cyan": "Cant<PERSON> cian en base a diestra", "block.minecraft.banner.square_bottom_left.gray": "Cantón gris en base a diestra", "block.minecraft.banner.square_bottom_left.green": "Cantón verde en base a diestra", "block.minecraft.banner.square_bottom_left.light_blue": "Cantón celeste en base a diestra", "block.minecraft.banner.square_bottom_left.light_gray": "Cantón gris claro en base a diestra", "block.minecraft.banner.square_bottom_left.lime": "Cantón verde lima en base a diestra", "block.minecraft.banner.square_bottom_left.magenta": "Cantón magenta en base a diestra", "block.minecraft.banner.square_bottom_left.orange": "Cantón naranja en base a diestra", "block.minecraft.banner.square_bottom_left.pink": "Cantón rosa en base a diestra", "block.minecraft.banner.square_bottom_left.purple": "Cantón violeta en base a diestra", "block.minecraft.banner.square_bottom_left.red": "Cantón rojo en base a diestra", "block.minecraft.banner.square_bottom_left.white": "Cantón blanco en base a diestra", "block.minecraft.banner.square_bottom_left.yellow": "<PERSON>t<PERSON> amarillo en base a diestra", "block.minecraft.banner.square_bottom_right.black": "Cantón negro en base a siniestra", "block.minecraft.banner.square_bottom_right.blue": "Cantón azul en base a siniestra", "block.minecraft.banner.square_bottom_right.brown": "Cantón marrón en base a siniestra", "block.minecraft.banner.square_bottom_right.cyan": "Cantón cian en base a siniestra", "block.minecraft.banner.square_bottom_right.gray": "Cantón gris en base a siniestra", "block.minecraft.banner.square_bottom_right.green": "Cantón verde en base a siniestra", "block.minecraft.banner.square_bottom_right.light_blue": "Cantón celeste en base a siniestra", "block.minecraft.banner.square_bottom_right.light_gray": "Cantón gris claro en base a siniestra", "block.minecraft.banner.square_bottom_right.lime": "Cantón verde lima en base a siniestra", "block.minecraft.banner.square_bottom_right.magenta": "Cantón magenta en base a siniestra", "block.minecraft.banner.square_bottom_right.orange": "Cantón naranja en base a siniestra", "block.minecraft.banner.square_bottom_right.pink": "Cantón rosa en base a siniestra", "block.minecraft.banner.square_bottom_right.purple": "Cantón violeta en base a siniestra", "block.minecraft.banner.square_bottom_right.red": "Cantón rojo en base a siniestra", "block.minecraft.banner.square_bottom_right.white": "Cantón blanco en base a siniestra", "block.minecraft.banner.square_bottom_right.yellow": "Cantón amarillo en base a siniestra", "block.minecraft.banner.square_top_left.black": "Cantón negro en jefe a diestra", "block.minecraft.banner.square_top_left.blue": "Cantón azul en jefe a diestra", "block.minecraft.banner.square_top_left.brown": "Cantón marrón en jefe a diestra", "block.minecraft.banner.square_top_left.cyan": "Cantón cian en jefe a diestra", "block.minecraft.banner.square_top_left.gray": "Cantón gris en jefe a diestra", "block.minecraft.banner.square_top_left.green": "Cantón verde en jefe a diestra", "block.minecraft.banner.square_top_left.light_blue": "Cantón celeste en jefe a diestra", "block.minecraft.banner.square_top_left.light_gray": "Cantón gris claro en jefe a diestra", "block.minecraft.banner.square_top_left.lime": "Cantón verde lima en jefe a diestra", "block.minecraft.banner.square_top_left.magenta": "Cantón magenta en jefe a diestra", "block.minecraft.banner.square_top_left.orange": "Cantón naranja en jefe a diestra", "block.minecraft.banner.square_top_left.pink": "Cantón rosa en jefe a diestra", "block.minecraft.banner.square_top_left.purple": "Cantón violeta en jefe a diestra", "block.minecraft.banner.square_top_left.red": "Cantón rojo en jefe a diestra", "block.minecraft.banner.square_top_left.white": "Cantón blanco en jefe a diestra", "block.minecraft.banner.square_top_left.yellow": "Cantón amarillo en jefe a diestra", "block.minecraft.banner.square_top_right.black": "Cantón negro en jefe a siniestra", "block.minecraft.banner.square_top_right.blue": "Cantón azul en jefe a siniestra", "block.minecraft.banner.square_top_right.brown": "Cantón marrón en jefe a siniestra", "block.minecraft.banner.square_top_right.cyan": "Cantón cian en jefe a siniestra", "block.minecraft.banner.square_top_right.gray": "Cantón gris en jefe a siniestra", "block.minecraft.banner.square_top_right.green": "Cantón verde en jefe a siniestra", "block.minecraft.banner.square_top_right.light_blue": "Cantón celeste en jefe a siniestra", "block.minecraft.banner.square_top_right.light_gray": "Cantón gris claro en jefe a siniestra", "block.minecraft.banner.square_top_right.lime": "Cantón verde lima en jefe a siniestra", "block.minecraft.banner.square_top_right.magenta": "Cantón magenta en jefe a siniestra", "block.minecraft.banner.square_top_right.orange": "Cantón naranja en jefe a siniestra", "block.minecraft.banner.square_top_right.pink": "Cantón rosa en jefe a siniestra", "block.minecraft.banner.square_top_right.purple": "Cantón violeta en jefe a siniestra", "block.minecraft.banner.square_top_right.red": "Cantón rojo en jefe a siniestra", "block.minecraft.banner.square_top_right.white": "Cantón blanco en jefe a siniestra", "block.minecraft.banner.square_top_right.yellow": "Cantón amarillo en jefe a siniestra", "block.minecraft.banner.straight_cross.black": "Cruz negra", "block.minecraft.banner.straight_cross.blue": "Cruz azul", "block.minecraft.banner.straight_cross.brown": "<PERSON>", "block.minecraft.banner.straight_cross.cyan": "<PERSON> cian", "block.minecraft.banner.straight_cross.gray": "<PERSON> gris", "block.minecraft.banner.straight_cross.green": "Cruz verde", "block.minecraft.banner.straight_cross.light_blue": "<PERSON> celeste", "block.minecraft.banner.straight_cross.light_gray": "<PERSON> gris claro", "block.minecraft.banner.straight_cross.lime": "Cruz verde lima", "block.minecraft.banner.straight_cross.magenta": "Cruz magenta", "block.minecraft.banner.straight_cross.orange": "<PERSON> naranja", "block.minecraft.banner.straight_cross.pink": "<PERSON> rosa", "block.minecraft.banner.straight_cross.purple": "<PERSON> violeta", "block.minecraft.banner.straight_cross.red": "Cruz roja", "block.minecraft.banner.straight_cross.white": "Cruz blanca", "block.minecraft.banner.straight_cross.yellow": "<PERSON> amari<PERSON>", "block.minecraft.banner.stripe_bottom.black": "Campaña negra", "block.minecraft.banner.stripe_bottom.blue": "Campaña azul", "block.minecraft.banner.stripe_bottom.brown": "Campaña <PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "Campaña cian", "block.minecraft.banner.stripe_bottom.gray": "Campaña gris", "block.minecraft.banner.stripe_bottom.green": "Campaña verde", "block.minecraft.banner.stripe_bottom.light_blue": "Campaña celeste", "block.minecraft.banner.stripe_bottom.light_gray": "Campaña gris claro", "block.minecraft.banner.stripe_bottom.lime": "Campaña verde lima", "block.minecraft.banner.stripe_bottom.magenta": "Campaña magenta", "block.minecraft.banner.stripe_bottom.orange": "Campaña naranja", "block.minecraft.banner.stripe_bottom.pink": "Campaña rosa", "block.minecraft.banner.stripe_bottom.purple": "Campaña violeta", "block.minecraft.banner.stripe_bottom.red": "Campaña roja", "block.minecraft.banner.stripe_bottom.white": "Campaña blanca", "block.minecraft.banner.stripe_bottom.yellow": "Campaña amarilla", "block.minecraft.banner.stripe_center.black": "<PERSON>lo negro", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON>l", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON>ian", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.stripe_center.green": "Palo verde", "block.minecraft.banner.stripe_center.light_blue": "<PERSON><PERSON> celeste", "block.minecraft.banner.stripe_center.light_gray": "<PERSON>lo gris claro", "block.minecraft.banner.stripe_center.lime": "Palo verde lima", "block.minecraft.banner.stripe_center.magenta": "Palo magenta", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON> violeta", "block.minecraft.banner.stripe_center.red": "<PERSON>lo rojo", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON> blanco", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.black": "Banda negra a siniestra", "block.minecraft.banner.stripe_downleft.blue": "Banda azul a siniestra", "block.minecraft.banner.stripe_downleft.brown": "Banda marrón a siniestra", "block.minecraft.banner.stripe_downleft.cyan": "Banda cian a siniestra", "block.minecraft.banner.stripe_downleft.gray": "Banda gris a siniestra", "block.minecraft.banner.stripe_downleft.green": "Banda verde a siniestra", "block.minecraft.banner.stripe_downleft.light_blue": "Banda celeste a siniestra", "block.minecraft.banner.stripe_downleft.light_gray": "Banda gris claro a siniestra", "block.minecraft.banner.stripe_downleft.lime": "Banda verde lima a siniestra", "block.minecraft.banner.stripe_downleft.magenta": "Banda magenta a siniestra", "block.minecraft.banner.stripe_downleft.orange": "Banda naranja a siniestra", "block.minecraft.banner.stripe_downleft.pink": "Banda rosa a siniestra", "block.minecraft.banner.stripe_downleft.purple": "Banda violeta a siniestra", "block.minecraft.banner.stripe_downleft.red": "Banda roja a siniestra", "block.minecraft.banner.stripe_downleft.white": "Banda blanca a siniestra", "block.minecraft.banner.stripe_downleft.yellow": "Banda amarilla a siniestra", "block.minecraft.banner.stripe_downright.black": "Banda negra", "block.minecraft.banner.stripe_downright.blue": "Banda azul", "block.minecraft.banner.stripe_downright.brown": "Banda marrón", "block.minecraft.banner.stripe_downright.cyan": "Banda cian", "block.minecraft.banner.stripe_downright.gray": "Banda gris", "block.minecraft.banner.stripe_downright.green": "Banda verde", "block.minecraft.banner.stripe_downright.light_blue": "Banda celeste", "block.minecraft.banner.stripe_downright.light_gray": "Banda gris claro", "block.minecraft.banner.stripe_downright.lime": "Banda verde lima", "block.minecraft.banner.stripe_downright.magenta": "Banda magenta", "block.minecraft.banner.stripe_downright.orange": "Banda naranja", "block.minecraft.banner.stripe_downright.pink": "Banda rosa", "block.minecraft.banner.stripe_downright.purple": "Banda violeta", "block.minecraft.banner.stripe_downright.red": "Banda roja", "block.minecraft.banner.stripe_downright.white": "Banda blanca", "block.minecraft.banner.stripe_downright.yellow": "Banda amarilla", "block.minecraft.banner.stripe_left.black": "Palo negro a diestra", "block.minecraft.banner.stripe_left.blue": "<PERSON>lo azul a diestra", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON> marr<PERSON> a diestra", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON> cian a diestra", "block.minecraft.banner.stripe_left.gray": "<PERSON>lo gris a diestra", "block.minecraft.banner.stripe_left.green": "Palo verde a diestra", "block.minecraft.banner.stripe_left.light_blue": "<PERSON>lo celeste a diestra", "block.minecraft.banner.stripe_left.light_gray": "Palo gris claro a diestra", "block.minecraft.banner.stripe_left.lime": "Palo verde lima a diestra", "block.minecraft.banner.stripe_left.magenta": "Palo magenta a diestra", "block.minecraft.banner.stripe_left.orange": "<PERSON><PERSON> naranja a diestra", "block.minecraft.banner.stripe_left.pink": "<PERSON>lo rosa a diestra", "block.minecraft.banner.stripe_left.purple": "<PERSON>lo violeta a diestra", "block.minecraft.banner.stripe_left.red": "<PERSON>lo rojo a diestra", "block.minecraft.banner.stripe_left.white": "Palo blanco a diestra", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON> amarillo a diestra", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON><PERSON> negra", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON> verde", "block.minecraft.banner.stripe_middle.light_blue": "<PERSON><PERSON><PERSON> cele<PERSON>", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.banner.stripe_middle.lime": "Franja verde lima", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON> magenta", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON> blanca", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON> am<PERSON>", "block.minecraft.banner.stripe_right.black": "Palo negro a siniestra", "block.minecraft.banner.stripe_right.blue": "Palo azul a siniestra", "block.minecraft.banner.stripe_right.brown": "<PERSON>lo marrón a siniestra", "block.minecraft.banner.stripe_right.cyan": "<PERSON>lo cian a siniestra", "block.minecraft.banner.stripe_right.gray": "Palo gris a siniestra", "block.minecraft.banner.stripe_right.green": "Palo verde a siniestra", "block.minecraft.banner.stripe_right.light_blue": "Palo celeste a siniestra", "block.minecraft.banner.stripe_right.light_gray": "Palo gris claro a siniestra", "block.minecraft.banner.stripe_right.lime": "Palo verde lima a siniestra", "block.minecraft.banner.stripe_right.magenta": "Palo magenta a siniestra", "block.minecraft.banner.stripe_right.orange": "<PERSON>lo naranja a siniestra", "block.minecraft.banner.stripe_right.pink": "Palo rosa a siniestra", "block.minecraft.banner.stripe_right.purple": "Palo violeta a siniestra", "block.minecraft.banner.stripe_right.red": "Palo rojo a siniestra", "block.minecraft.banner.stripe_right.white": "Palo blanco a siniestra", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON> amarillo a siniestra", "block.minecraft.banner.stripe_top.black": "Cantón negro", "block.minecraft.banner.stripe_top.blue": "Cantón azul", "block.minecraft.banner.stripe_top.brown": "Cantón marrón", "block.minecraft.banner.stripe_top.cyan": "Cantón cian", "block.minecraft.banner.stripe_top.gray": "Cantón gris", "block.minecraft.banner.stripe_top.green": "Cantón verde", "block.minecraft.banner.stripe_top.light_blue": "Cantón celeste", "block.minecraft.banner.stripe_top.light_gray": "Cantón gris claro", "block.minecraft.banner.stripe_top.lime": "Cantón verde lima", "block.minecraft.banner.stripe_top.magenta": "Cantón magenta", "block.minecraft.banner.stripe_top.orange": "Cantón naranja", "block.minecraft.banner.stripe_top.pink": "Cantón rosa", "block.minecraft.banner.stripe_top.purple": "<PERSON>t<PERSON> violeta", "block.minecraft.banner.stripe_top.red": "Cantón rojo", "block.minecraft.banner.stripe_top.white": "Cantón blanco", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON><PERSON> amarillo", "block.minecraft.banner.triangle_bottom.black": "Chevrón negro", "block.minecraft.banner.triangle_bottom.blue": "Chevrón azul", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "Chevr<PERSON> cian", "block.minecraft.banner.triangle_bottom.gray": "Chevrón gris", "block.minecraft.banner.triangle_bottom.green": "Chevrón verde", "block.minecraft.banner.triangle_bottom.light_blue": "Chevr<PERSON> celeste", "block.minecraft.banner.triangle_bottom.light_gray": "Chevrón gris claro", "block.minecraft.banner.triangle_bottom.lime": "Chevrón verde lima", "block.minecraft.banner.triangle_bottom.magenta": "Chevrón magenta", "block.minecraft.banner.triangle_bottom.orange": "Chevrón naranja", "block.minecraft.banner.triangle_bottom.pink": "Chevr<PERSON> rosa", "block.minecraft.banner.triangle_bottom.purple": "Chevrón violeta", "block.minecraft.banner.triangle_bottom.red": "Chevrón rojo", "block.minecraft.banner.triangle_bottom.white": "Chevrón blanco", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON><PERSON><PERSON> amarillo", "block.minecraft.banner.triangle_top.black": "Chevrón negro invertido", "block.minecraft.banner.triangle_top.blue": "Chevrón azul invertido", "block.minecraft.banner.triangle_top.brown": "Chevrón marrón invertido", "block.minecraft.banner.triangle_top.cyan": "Chevrón cian invertido", "block.minecraft.banner.triangle_top.gray": "Chevrón gris invertido", "block.minecraft.banner.triangle_top.green": "Chevrón verde invertido", "block.minecraft.banner.triangle_top.light_blue": "Chevrón celeste invertido", "block.minecraft.banner.triangle_top.light_gray": "Chevrón gris claro invertido", "block.minecraft.banner.triangle_top.lime": "Chevrón verde lima invertido", "block.minecraft.banner.triangle_top.magenta": "Chevrón magenta invertido", "block.minecraft.banner.triangle_top.orange": "Chevrón naranja invertido", "block.minecraft.banner.triangle_top.pink": "Chevrón rosa invertido", "block.minecraft.banner.triangle_top.purple": "Chevrón violeta invertido", "block.minecraft.banner.triangle_top.red": "Chevrón rojo invertido", "block.minecraft.banner.triangle_top.white": "Chevrón blanco invertido", "block.minecraft.banner.triangle_top.yellow": "Chevrón amarillo invertido", "block.minecraft.banner.triangles_bottom.black": "Base negra dentada", "block.minecraft.banner.triangles_bottom.blue": "Base azul dentada", "block.minecraft.banner.triangles_bottom.brown": "Base marrón dentada", "block.minecraft.banner.triangles_bottom.cyan": "Base cian dentada", "block.minecraft.banner.triangles_bottom.gray": "Base gris dentada", "block.minecraft.banner.triangles_bottom.green": "Base verde dentada", "block.minecraft.banner.triangles_bottom.light_blue": "Base celeste dentada", "block.minecraft.banner.triangles_bottom.light_gray": "Base gris claro dentada", "block.minecraft.banner.triangles_bottom.lime": "Base verde lima dentada", "block.minecraft.banner.triangles_bottom.magenta": "Base magenta dentada", "block.minecraft.banner.triangles_bottom.orange": "Base naranja dentada", "block.minecraft.banner.triangles_bottom.pink": "Base rosa dentada", "block.minecraft.banner.triangles_bottom.purple": "Base violeta dentada", "block.minecraft.banner.triangles_bottom.red": "Base roja dentada", "block.minecraft.banner.triangles_bottom.white": "Base blanca dentada", "block.minecraft.banner.triangles_bottom.yellow": "Base amarilla dentada", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON> negro dentado", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON> a<PERSON>l dentado", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON> ma<PERSON> den<PERSON>o", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON> c<PERSON>o", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON> gris dentado", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON> verde dentado", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON> celeste dentado", "block.minecraft.banner.triangles_top.light_gray": "Chevrón azul", "block.minecraft.banner.triangles_top.lime": "<PERSON><PERSON> verde lima dentado", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON> magenta dentado", "block.minecraft.banner.triangles_top.orange": "<PERSON><PERSON> na<PERSON>ja dentado", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON> rosa dentado", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON> violet<PERSON> dentado", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON> rojo dentado", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON> blanco dentado", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON> am<PERSON> den<PERSON>o", "block.minecraft.barrel": "Barril", "block.minecraft.barrier": "Barr<PERSON>", "block.minecraft.basalt": "Basalto", "block.minecraft.beacon": "Faro", "block.minecraft.beacon.primary": "Poder primario", "block.minecraft.beacon.secondary": "Poder secundario", "block.minecraft.bed.no_sleep": "<PERSON><PERSON>lo podés dormir de noche o durante una tormenta", "block.minecraft.bed.not_safe": "Ahora no podés dormir: hay monstruos cerca", "block.minecraft.bed.obstructed": "Esta cama está obstruida", "block.minecraft.bed.occupied": "La cama está ocupada", "block.minecraft.bed.too_far_away": "Ahora no podés dormir: la cama está lejos", "block.minecraft.bedrock": "<PERSON>dra madre", "block.minecraft.bee_nest": "Colmena <PERSON>", "block.minecraft.beehive": "Apiario de abejas", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bell": "Campana", "block.minecraft.big_dripleaf": "Plantaforma", "block.minecraft.big_dripleaf_stem": "Tallo de plantaforma grande", "block.minecraft.birch_button": "Botón de abedul", "block.minecraft.birch_door": "Puer<PERSON> de a<PERSON>ul", "block.minecraft.birch_fence": "<PERSON><PERSON>", "block.minecraft.birch_fence_gate": "Portón de abedul", "block.minecraft.birch_hanging_sign": "Cartel colgante de abedul", "block.minecraft.birch_leaves": "Hojas de abedul", "block.minecraft.birch_log": "Tronco de abedul", "block.minecraft.birch_planks": "<PERSON><PERSON>", "block.minecraft.birch_pressure_plate": "Placa de presión de abedul", "block.minecraft.birch_sapling": "<PERSON><PERSON>", "block.minecraft.birch_sign": "Cartel de abedul", "block.minecraft.birch_slab": "Baldo<PERSON> a<PERSON>", "block.minecraft.birch_stairs": "Escaleras de abedul", "block.minecraft.birch_trapdoor": "Escotilla de abedul", "block.minecraft.birch_wall_hanging_sign": "Cartel colgante de abedul en pared", "block.minecraft.birch_wall_sign": "Cartel de abedul en pared", "block.minecraft.birch_wood": "<PERSON><PERSON> de abedul", "block.minecraft.black_banner": "Estandarte negro", "block.minecraft.black_bed": "Cama negra", "block.minecraft.black_candle": "Vela negra", "block.minecraft.black_candle_cake": "Torta con vela negra", "block.minecraft.black_carpet": "Alfombra negra", "block.minecraft.black_concrete": "Concreto negro", "block.minecraft.black_concrete_powder": "Cemento negro", "block.minecraft.black_glazed_terracotta": "Azulejo negro", "block.minecraft.black_shulker_box": "Caja de shulker negra", "block.minecraft.black_stained_glass": "<PERSON><PERSON><PERSON> negro", "block.minecraft.black_stained_glass_pane": "Panel de vidrio negro", "block.minecraft.black_terracotta": "Terracota negra", "block.minecraft.black_wool": "<PERSON> negra", "block.minecraft.blackstone": "Piedra negra", "block.minecraft.blackstone_slab": "Baldosa de piedra negra", "block.minecraft.blackstone_stairs": "Escaleras de piedra negra", "block.minecraft.blackstone_wall": "<PERSON><PERSON> de piedra negra", "block.minecraft.blast_furnace": "Alto horno", "block.minecraft.blue_banner": "Estandarte azul", "block.minecraft.blue_bed": "Cama a<PERSON>l", "block.minecraft.blue_candle": "<PERSON><PERSON> azul", "block.minecraft.blue_candle_cake": "Torta con vela azul", "block.minecraft.blue_carpet": "Alfombra azul", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON> a<PERSON>l", "block.minecraft.blue_concrete_powder": "Cemento azul", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> azul", "block.minecraft.blue_ice": "<PERSON><PERSON>", "block.minecraft.blue_orchid": "Orquídea a<PERSON>l", "block.minecraft.blue_shulker_box": "Caja de shulker azul", "block.minecraft.blue_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass_pane": "Panel de vidrio azul", "block.minecraft.blue_terracotta": "Terracota azul", "block.minecraft.blue_wool": "<PERSON>", "block.minecraft.bone_block": "Bloque de huesos", "block.minecraft.bookshelf": "Biblioteca", "block.minecraft.brain_coral": "Coral de cerebro", "block.minecraft.brain_coral_block": "Bloque de coral de cerebro", "block.minecraft.brain_coral_fan": "Gorgon<PERSON> de cerebro", "block.minecraft.brain_coral_wall_fan": "<PERSON><PERSON><PERSON> de cerebro en pared", "block.minecraft.brewing_stand": "Destiladora", "block.minecraft.brick_slab": "<PERSON>ldo<PERSON> de <PERSON>", "block.minecraft.brick_stairs": "Escalera de ladrillos", "block.minecraft.brick_wall": "<PERSON><PERSON>", "block.minecraft.bricks": "Ladrillos", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_bed": "<PERSON><PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON>", "block.minecraft.brown_candle_cake": "Torta con vela marrón", "block.minecraft.brown_carpet": "Alfombra marrón", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "Cemento <PERSON>", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_mushroom": "<PERSON><PERSON>", "block.minecraft.brown_mushroom_block": "Bloque de hongo marrón", "block.minecraft.brown_shulker_box": "Caja de shulker marr<PERSON>", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass_pane": "Panel de vidrio marr<PERSON>", "block.minecraft.brown_terracotta": "Terracota marrón", "block.minecraft.brown_wool": "<PERSON>", "block.minecraft.bubble_column": "Columna de burbujas", "block.minecraft.bubble_coral": "Coral de burbuja", "block.minecraft.bubble_coral_block": "Bloque de coral de burbuja", "block.minecraft.bubble_coral_fan": "Gorgonia de burbuja", "block.minecraft.bubble_coral_wall_fan": "<PERSON><PERSON><PERSON> de burbuja en pared", "block.minecraft.budding_amethyst": "Brotador de amatista", "block.minecraft.bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cactus": "Cactus", "block.minecraft.cactus_flower": "Flor de cactus", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Calcita", "block.minecraft.calibrated_sculk_sensor": "Sensor de sculk calibrado", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>", "block.minecraft.candle": "Vela", "block.minecraft.candle_cake": "Torta con vela", "block.minecraft.carrots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Mesa de cartografía", "block.minecraft.carved_pumpkin": "<PERSON><PERSON><PERSON> tallado", "block.minecraft.cauldron": "Calder<PERSON>", "block.minecraft.cave_air": "Aire de cueva", "block.minecraft.cave_vines": "Enredaderas de cueva", "block.minecraft.cave_vines_plant": "Planta de enredaderas de cueva", "block.minecraft.chain": "Cadena", "block.minecraft.chain_command_block": "Bloque de comandos en cadena", "block.minecraft.cherry_button": "Botón de cerezo", "block.minecraft.cherry_door": "<PERSON>uer<PERSON> de cerezo", "block.minecraft.cherry_fence": "<PERSON><PERSON> c<PERSON>", "block.minecraft.cherry_fence_gate": "Portón de cerezo", "block.minecraft.cherry_hanging_sign": "Cartel colgante de cerezo", "block.minecraft.cherry_leaves": "Hojas de cerezo", "block.minecraft.cherry_log": "Tronco de cerezo", "block.minecraft.cherry_planks": "<PERSON><PERSON> c<PERSON>", "block.minecraft.cherry_pressure_plate": "Placa de presión de cerezo", "block.minecraft.cherry_sapling": "<PERSON><PERSON> cere<PERSON>", "block.minecraft.cherry_sign": "Cartel de cerezo", "block.minecraft.cherry_slab": "Baldo<PERSON> de cere<PERSON>", "block.minecraft.cherry_stairs": "Escaleras de cerezo", "block.minecraft.cherry_trapdoor": "Escotilla de cerezo", "block.minecraft.cherry_wall_hanging_sign": "Cartel colgante de cerezo en pared", "block.minecraft.cherry_wall_sign": "Cartel colgante de cerezo", "block.minecraft.cherry_wood": "Le<PERSON> c<PERSON>zo", "block.minecraft.chest": "Baúl", "block.minecraft.chipped_anvil": "<PERSON><PERSON>", "block.minecraft.chiseled_bookshelf": "Biblioteca tallada", "block.minecraft.chiseled_copper": "Cobre cincelado", "block.minecraft.chiseled_deepslate": "Pizarra profunda cincelada", "block.minecraft.chiseled_nether_bricks": "Ladrillos del Nether cincelados", "block.minecraft.chiseled_polished_blackstone": "Piedra negra pulida cincelada", "block.minecraft.chiseled_quartz_block": "<PERSON><PERSON>rzo cin<PERSON>", "block.minecraft.chiseled_red_sandstone": "Arenisca roja cincelada", "block.minecraft.chiseled_resin_bricks": "Ladrillos de resina cincelados", "block.minecraft.chiseled_sandstone": "Arenisca cincelada", "block.minecraft.chiseled_stone_bricks": "Ladrillos de piedra cincelados", "block.minecraft.chiseled_tuff": "Toba cincelada", "block.minecraft.chiseled_tuff_bricks": "Ladrillos de toba cincelados", "block.minecraft.chorus_flower": "Flor de chorus", "block.minecraft.chorus_plant": "Chorus", "block.minecraft.clay": "Arcilla", "block.minecraft.closed_eyeblossom": "Miraflor cerrada", "block.minecraft.coal_block": "Bloque de car<PERSON>ón", "block.minecraft.coal_ore": "Mineral de carbón", "block.minecraft.coarse_dirt": "Tierra infértil", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON> de pizarra profunda", "block.minecraft.cobbled_deepslate_slab": "Baldosa de piedra de pizarra profunda", "block.minecraft.cobbled_deepslate_stairs": "Escaleras de piedra de pizarra profunda", "block.minecraft.cobbled_deepslate_wall": "<PERSON><PERSON> de piedra de pizarra profunda", "block.minecraft.cobblestone": "Roca", "block.minecraft.cobblestone_slab": "Baldosa de roca", "block.minecraft.cobblestone_stairs": "Escaleras de roca", "block.minecraft.cobblestone_wall": "<PERSON><PERSON> de roca", "block.minecraft.cobweb": "Telaraña", "block.minecraft.cocoa": "Cacao", "block.minecraft.command_block": "Bloque de comandos", "block.minecraft.comparator": "Comparador de redstone", "block.minecraft.composter": "Compostera", "block.minecraft.conduit": "Canalizador", "block.minecraft.copper_block": "Bloque de cobre", "block.minecraft.copper_bulb": "Lámpara de cobre", "block.minecraft.copper_door": "Puerta de cobre", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "Mineral de cobre", "block.minecraft.copper_trapdoor": "Escotilla de cobre", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Ladrillos de pizarra profunda quebrados", "block.minecraft.cracked_deepslate_tiles": "Baldosas de pizarra abismal quebradas", "block.minecraft.cracked_nether_bricks": "Ladrillos del Nether rajados", "block.minecraft.cracked_polished_blackstone_bricks": "Ladrillos de piedra negra pulida rajada", "block.minecraft.cracked_stone_bricks": "Ladrillos de piedra rajados", "block.minecraft.crafter": "Fabricador", "block.minecraft.crafting_table": "Mesa de crafteo", "block.minecraft.creaking_heart": "Corazón de crujidor", "block.minecraft.creeper_head": "C<PERSON>za de creeper", "block.minecraft.creeper_wall_head": "Cabeza de creeper en pared", "block.minecraft.crimson_button": "Bo<PERSON><PERSON>", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence": "<PERSON><PERSON>", "block.minecraft.crimson_fence_gate": "Portón carmesí", "block.minecraft.crimson_fungus": "<PERSON><PERSON>", "block.minecraft.crimson_hanging_sign": "Cartel colgante carmesí", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_nylium": "<PERSON><PERSON>", "block.minecraft.crimson_planks": "<PERSON><PERSON>", "block.minecraft.crimson_pressure_plate": "Placa de presión carmesí", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_sign": "Cartel carmesí", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_stairs": "Escaleras carmesí", "block.minecraft.crimson_stem": "Tallo car<PERSON>", "block.minecraft.crimson_trapdoor": "Escotilla <PERSON>", "block.minecraft.crimson_wall_hanging_sign": "Cartel colgante carmesí en pared", "block.minecraft.crimson_wall_sign": "Cartel carmesí en pared", "block.minecraft.crying_obsidian": "Obs<PERSON>a lloro<PERSON>", "block.minecraft.cut_copper": "Cobre cortado", "block.minecraft.cut_copper_slab": "Baldosa de cobre cortado", "block.minecraft.cut_copper_stairs": "Escaleras de cobre cortado", "block.minecraft.cut_red_sandstone": "Arenisca roja cortada", "block.minecraft.cut_red_sandstone_slab": "Baldosa de arenisca roja cortada", "block.minecraft.cut_sandstone": "Arenisca cortada", "block.minecraft.cut_sandstone_slab": "Baldosa de arenisca cortada", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_bed": "<PERSON><PERSON> cian", "block.minecraft.cyan_candle": "<PERSON><PERSON> cian", "block.minecraft.cyan_candle_cake": "Torta con vela cian", "block.minecraft.cyan_carpet": "Alfombra cian", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_concrete_powder": "Cemento cian", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_shulker_box": "Caja de shulker cian", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass_pane": "Panel de vidrio cian", "block.minecraft.cyan_terracotta": "Terracota cian", "block.minecraft.cyan_wool": "<PERSON> c<PERSON>", "block.minecraft.damaged_anvil": "<PERSON><PERSON> m<PERSON>", "block.minecraft.dandelion": "<PERSON><PERSON>", "block.minecraft.dark_oak_button": "Botón de roble oscuro", "block.minecraft.dark_oak_door": "<PERSON><PERSON><PERSON> de roble oscuro", "block.minecraft.dark_oak_fence": "<PERSON><PERSON> de roble oscuro", "block.minecraft.dark_oak_fence_gate": "Portón de roble oscuro", "block.minecraft.dark_oak_hanging_sign": "Cartel colgante de roble oscuro", "block.minecraft.dark_oak_leaves": "Hojas de roble oscuro", "block.minecraft.dark_oak_log": "Tronco de roble oscuro", "block.minecraft.dark_oak_planks": "<PERSON><PERSON> de roble oscuro", "block.minecraft.dark_oak_pressure_plate": "Placa de presión de roble oscuro", "block.minecraft.dark_oak_sapling": "<PERSON><PERSON> de roble oscuro", "block.minecraft.dark_oak_sign": "Cartel de roble oscuro", "block.minecraft.dark_oak_slab": "Baldosa de roble oscuro", "block.minecraft.dark_oak_stairs": "Escaleras de roble oscuro", "block.minecraft.dark_oak_trapdoor": "Escotilla de roble oscuro", "block.minecraft.dark_oak_wall_hanging_sign": "Cartel colgante de roble oscuro en pared", "block.minecraft.dark_oak_wall_sign": "Cartel de roble oscuro en pared", "block.minecraft.dark_oak_wood": "Leño de roble oscuro", "block.minecraft.dark_prismarine": "Prismarina oscura", "block.minecraft.dark_prismarine_slab": "Baldosa de prismarina oscura", "block.minecraft.dark_prismarine_stairs": "Escaleras de prismarina oscura", "block.minecraft.daylight_detector": "Sensor de luz solar", "block.minecraft.dead_brain_coral": "Coral de cerebro muerto", "block.minecraft.dead_brain_coral_block": "Bloque de coral de cerebro muerto", "block.minecraft.dead_brain_coral_fan": "Gorgonia de cerebro muerta", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON> de cerebro muerta en pared", "block.minecraft.dead_bubble_coral": "Coral de burbuja muerto", "block.minecraft.dead_bubble_coral_block": "Bloque de coral de burbuja muerto", "block.minecraft.dead_bubble_coral_fan": "Gorgonia de burbuja muerta", "block.minecraft.dead_bubble_coral_wall_fan": "Gorgonia de burbuja muerta en pared", "block.minecraft.dead_bush": "<PERSON><PERSON><PERSON><PERSON> muerto", "block.minecraft.dead_fire_coral": "Coral de fuego muerto", "block.minecraft.dead_fire_coral_block": "Bloque de coral de fuego muerto", "block.minecraft.dead_fire_coral_fan": "Gorgonia de fuego muerta", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON><PERSON> de fuego muerta en pared", "block.minecraft.dead_horn_coral": "Coral de cuerno muerto", "block.minecraft.dead_horn_coral_block": "Bloque de coral de cuerno muerto", "block.minecraft.dead_horn_coral_fan": "Gorgonia de cuerno muerta", "block.minecraft.dead_horn_coral_wall_fan": "Gorgonia de cuerno muerta en pared", "block.minecraft.dead_tube_coral": "Coral de tubo muerto", "block.minecraft.dead_tube_coral_block": "Bloque de coral de tubo muerto", "block.minecraft.dead_tube_coral_fan": "Gorgonia de tubo muerta", "block.minecraft.dead_tube_coral_wall_fan": "Gorgonia de tubo muerta en pared", "block.minecraft.decorated_pot": "J<PERSON><PERSON><PERSON> decorado", "block.minecraft.deepslate": "Pizarra profunda", "block.minecraft.deepslate_brick_slab": "Baldosa de ladrillos de pizarra profunda", "block.minecraft.deepslate_brick_stairs": "Escaleras de ladrillos de pizarra profunda", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON> de ladrillos de pizarra profunda", "block.minecraft.deepslate_bricks": "Ladrillos de pizarra profunda", "block.minecraft.deepslate_coal_ore": "Mineral de carbón de pizarra profunda", "block.minecraft.deepslate_copper_ore": "Mineral de cobre de pizarra profunda", "block.minecraft.deepslate_diamond_ore": "Mineral de diamante de pizarra profunda", "block.minecraft.deepslate_emerald_ore": "Mineral de esmeralda de pizarra profunda", "block.minecraft.deepslate_gold_ore": "Mineral de oro de pizarra profunda", "block.minecraft.deepslate_iron_ore": "Mineral de hierro de pizarra profunda", "block.minecraft.deepslate_lapis_ore": "Mineral de lapislázuli de pizarra profunda", "block.minecraft.deepslate_redstone_ore": "Mineral de redstone de pizarra profunda", "block.minecraft.deepslate_tile_slab": "Baldosa de losas de pizarra profunda", "block.minecraft.deepslate_tile_stairs": "Escaleras de losas de pizarra profunda", "block.minecraft.deepslate_tile_wall": "<PERSON><PERSON> de losas de pizarra profunda", "block.minecraft.deepslate_tiles": "Losas de pizarra profunda", "block.minecraft.detector_rail": "<PERSON><PERSON> detector<PERSON>", "block.minecraft.diamond_block": "Bloque de diamante", "block.minecraft.diamond_ore": "Mineral de diamante", "block.minecraft.diorite": "Di<PERSON>ta", "block.minecraft.diorite_slab": "Baldosa de diorita", "block.minecraft.diorite_stairs": "Escaleras de diorita", "block.minecraft.diorite_wall": "<PERSON><PERSON>", "block.minecraft.dirt": "Tierra", "block.minecraft.dirt_path": "Camino de tierra", "block.minecraft.dispenser": "Dispensador", "block.minecraft.dragon_egg": "Huevo de dragón", "block.minecraft.dragon_head": "Cabeza de dragón", "block.minecraft.dragon_wall_head": "Cabeza de dragón en pared", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Bloque de algas secas", "block.minecraft.dripstone_block": "Bloque de espeleotema", "block.minecraft.dropper": "Soltador", "block.minecraft.emerald_block": "Bloque de esmeralda", "block.minecraft.emerald_ore": "Mineral de esmeralda", "block.minecraft.enchanting_table": "Mesa de encantamientos", "block.minecraft.end_gateway": "Acceso al End", "block.minecraft.end_portal": "Marco <PERSON> al End", "block.minecraft.end_portal_frame": "Marco <PERSON> portal del End", "block.minecraft.end_rod": "Vara del End", "block.minecraft.end_stone": "Piedra del End", "block.minecraft.end_stone_brick_slab": "Baldosa de ladrillos de piedra del End", "block.minecraft.end_stone_brick_stairs": "Escaleras de ladrillos de piedra del End", "block.minecraft.end_stone_brick_wall": "<PERSON><PERSON> de ladrillos de piedra del End", "block.minecraft.end_stone_bricks": "Ladrillos de piedra del End", "block.minecraft.ender_chest": "<PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Cobre cincelado expuesto", "block.minecraft.exposed_copper": "Cobre expuesto", "block.minecraft.exposed_copper_bulb": "Lámpara de cobre expuesto", "block.minecraft.exposed_copper_door": "Puerta de cobre expuesto", "block.minecraft.exposed_copper_grate": "Rejilla de cobre expuesto", "block.minecraft.exposed_copper_trapdoor": "Escotilla de cobre expuesto", "block.minecraft.exposed_cut_copper": "Cobre cortado expuesto", "block.minecraft.exposed_cut_copper_slab": "Baldosa de cobre cortado expuesto", "block.minecraft.exposed_cut_copper_stairs": "Escaleras de cobre cortado expuesto", "block.minecraft.farmland": "Tierra de cultivo", "block.minecraft.fern": "<PERSON><PERSON><PERSON>", "block.minecraft.fire": "Fuego", "block.minecraft.fire_coral": "Coral de fuego", "block.minecraft.fire_coral_block": "Bloque de coral de fuego", "block.minecraft.fire_coral_fan": "Gorgonia de fuego", "block.minecraft.fire_coral_wall_fan": "<PERSON><PERSON><PERSON> de fuego en pared", "block.minecraft.firefly_bush": "Arbusto con luciérnagas", "block.minecraft.fletching_table": "Mesa de arquería", "block.minecraft.flower_pot": "Mace<PERSON>", "block.minecraft.flowering_azalea": "Azalea florida", "block.minecraft.flowering_azalea_leaves": "Hojas de azalea floridas", "block.minecraft.frogspawn": "<PERSON><PERSON> de rana", "block.minecraft.frosted_ice": "<PERSON><PERSON> es<PERSON>", "block.minecraft.furnace": "<PERSON><PERSON>", "block.minecraft.gilded_blackstone": "Piedra negra dorada", "block.minecraft.glass": "<PERSON><PERSON><PERSON>", "block.minecraft.glass_pane": "Panel de vidrio", "block.minecraft.glow_lichen": "Liquen luminoso", "block.minecraft.glowstone": "Piedra luminosa", "block.minecraft.gold_block": "Bloque de oro", "block.minecraft.gold_ore": "Mineral de oro", "block.minecraft.granite": "<PERSON><PERSON>", "block.minecraft.granite_slab": "Baldosa de granito", "block.minecraft.granite_stairs": "Escaleras de granito", "block.minecraft.granite_wall": "<PERSON><PERSON>", "block.minecraft.grass": "Pasto", "block.minecraft.grass_block": "Bloque de pasto", "block.minecraft.gravel": "Grava", "block.minecraft.gray_banner": "Estandarte gris", "block.minecraft.gray_bed": "<PERSON>a gris", "block.minecraft.gray_candle": "<PERSON><PERSON> gris", "block.minecraft.gray_candle_cake": "Torta con vela gris", "block.minecraft.gray_carpet": "Alfombra gris", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON> gris", "block.minecraft.gray_concrete_powder": "Cemento gris", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> gris", "block.minecraft.gray_shulker_box": "Caja de shulker gris", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON> gris", "block.minecraft.gray_stained_glass_pane": "Panel de vidrio gris", "block.minecraft.gray_terracotta": "Terracota gris", "block.minecraft.gray_wool": "<PERSON> gris", "block.minecraft.green_banner": "Estandarte verde", "block.minecraft.green_bed": "Cama verde", "block.minecraft.green_candle": "Vela verde", "block.minecraft.green_candle_cake": "Torta con vela verde", "block.minecraft.green_carpet": "Alfombra verde", "block.minecraft.green_concrete": "<PERSON><PERSON>to verde", "block.minecraft.green_concrete_powder": "Cemento verde", "block.minecraft.green_glazed_terracotta": "A<PERSON>lejo verde", "block.minecraft.green_shulker_box": "Caja de shulker verde", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON> verde", "block.minecraft.green_stained_glass_pane": "Panel de vidrio verde", "block.minecraft.green_terracotta": "Terracota verde", "block.minecraft.green_wool": "Lana verde", "block.minecraft.grindstone": "A<PERSON>lad<PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON> co<PERSON>", "block.minecraft.hay_block": "<PERSON><PERSON>", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON><PERSON> pesado", "block.minecraft.heavy_weighted_pressure_plate": "Placa de presión para peso elevado", "block.minecraft.honey_block": "Bloque de miel", "block.minecraft.honeycomb_block": "Bloque de panal", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "Coral de cuerno", "block.minecraft.horn_coral_block": "Bloque de coral de cuerno", "block.minecraft.horn_coral_fan": "Gorgonia de cuerno", "block.minecraft.horn_coral_wall_fan": "<PERSON><PERSON><PERSON> de cuerno en pared", "block.minecraft.ice": "<PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "Ladrillos de piedra cincelados infestados", "block.minecraft.infested_cobblestone": "Roca infestada", "block.minecraft.infested_cracked_stone_bricks": "Ladrillos de piedra rajados infestados", "block.minecraft.infested_deepslate": "Pizarra profunda infestada", "block.minecraft.infested_mossy_stone_bricks": "Ladrillos de piedra musgosos infestados", "block.minecraft.infested_stone": "Piedra infestada", "block.minecraft.infested_stone_bricks": "Ladrillos de piedra infestados", "block.minecraft.iron_bars": "Barras de hi<PERSON>ro", "block.minecraft.iron_block": "Bloque de hi<PERSON>ro", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "<PERSON><PERSON> de <PERSON>ro", "block.minecraft.iron_trapdoor": "Escotilla de hi<PERSON>ro", "block.minecraft.jack_o_lantern": "Zapallo de Halloween", "block.minecraft.jigsaw": "Bloque de rompecabezas", "block.minecraft.jukebox": "Tocadiscos", "block.minecraft.jungle_button": "Botón de j<PERSON>", "block.minecraft.jungle_door": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_fence": "<PERSON><PERSON>", "block.minecraft.jungle_fence_gate": "Portón de jungla", "block.minecraft.jungle_hanging_sign": "Cartel colgante de jungla", "block.minecraft.jungle_leaves": "Hojas de jungla", "block.minecraft.jungle_log": "Tronco de jungla", "block.minecraft.jungle_planks": "<PERSON><PERSON>", "block.minecraft.jungle_pressure_plate": "Placa de presión de jungla", "block.minecraft.jungle_sapling": "<PERSON><PERSON>", "block.minecraft.jungle_sign": "Cartel de jungla", "block.minecraft.jungle_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_stairs": "Escaleras de jungla", "block.minecraft.jungle_trapdoor": "Escotilla de jungla", "block.minecraft.jungle_wall_hanging_sign": "Cartel colgante de jungla en pared", "block.minecraft.jungle_wall_sign": "Cartel de jungla en pared", "block.minecraft.jungle_wood": "<PERSON><PERSON>", "block.minecraft.kelp": "Alga", "block.minecraft.kelp_plant": "Tallo de alga", "block.minecraft.ladder": "Escalera", "block.minecraft.lantern": "Farol", "block.minecraft.lapis_block": "Bloque de Lapislázuli", "block.minecraft.lapis_ore": "Mineral de lapislázuli", "block.minecraft.large_amethyst_bud": "Brote de amatista grande", "block.minecraft.large_fern": "<PERSON><PERSON><PERSON> alto", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Caldero con lava", "block.minecraft.leaf_litter": "Hojas secas", "block.minecraft.lectern": "Atril", "block.minecraft.lever": "Palanca", "block.minecraft.light": "Luz", "block.minecraft.light_blue_banner": "Estandarte celeste", "block.minecraft.light_blue_bed": "<PERSON><PERSON> celeste", "block.minecraft.light_blue_candle": "<PERSON><PERSON> celeste", "block.minecraft.light_blue_candle_cake": "Torta con vela celeste", "block.minecraft.light_blue_carpet": "Alfombra celeste", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON> celeste", "block.minecraft.light_blue_concrete_powder": "Cemento celeste", "block.minecraft.light_blue_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> celeste", "block.minecraft.light_blue_shulker_box": "Caja de shulker celeste", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON> celeste", "block.minecraft.light_blue_stained_glass_pane": "Panel de vidrio celeste", "block.minecraft.light_blue_terracotta": "Terracota celeste", "block.minecraft.light_blue_wool": "<PERSON> celeste", "block.minecraft.light_gray_banner": "Estandarte gris claro", "block.minecraft.light_gray_bed": "<PERSON>a gris claro", "block.minecraft.light_gray_candle": "<PERSON><PERSON> gris claro", "block.minecraft.light_gray_candle_cake": "<PERSON>ta con vela gris claro", "block.minecraft.light_gray_carpet": "Alfombra gris claro", "block.minecraft.light_gray_concrete": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.light_gray_concrete_powder": "Cemento gris claro", "block.minecraft.light_gray_glazed_terracotta": "<PERSON><PERSON><PERSON>jo gris claro", "block.minecraft.light_gray_shulker_box": "Caja de shulker gris claro", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.light_gray_stained_glass_pane": "Panel de vidrio gris claro", "block.minecraft.light_gray_terracotta": "Terracota gris claro", "block.minecraft.light_gray_wool": "<PERSON> gris claro", "block.minecraft.light_weighted_pressure_plate": "Placa de presión para peso liviano", "block.minecraft.lightning_rod": "Pararrayos", "block.minecraft.lilac": "<PERSON>", "block.minecraft.lily_of_the_valley": "Lirio del valle", "block.minecraft.lily_pad": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_banner": "Estandarte verde lima", "block.minecraft.lime_bed": "Cama verde lima", "block.minecraft.lime_candle": "Vela verde lima", "block.minecraft.lime_candle_cake": "Torta con vela verde lima", "block.minecraft.lime_carpet": "Alfombra verde lima", "block.minecraft.lime_concrete": "Concreto verde lima", "block.minecraft.lime_concrete_powder": "Cemento verde lima", "block.minecraft.lime_glazed_terracotta": "Azulejo verde lima", "block.minecraft.lime_shulker_box": "Caja de shulker verde lima", "block.minecraft.lime_stained_glass": "V<PERSON>rio verde lima", "block.minecraft.lime_stained_glass_pane": "Panel de vidrio verde lima", "block.minecraft.lime_terracotta": "Terracota verde lima", "block.minecraft.lime_wool": "Lana verde lima", "block.minecraft.lodestone": "Ma<PERSON><PERSON><PERSON>", "block.minecraft.loom": "Máquina de telar", "block.minecraft.magenta_banner": "Estandarte magenta", "block.minecraft.magenta_bed": "Cama magenta", "block.minecraft.magenta_candle": "Vela magenta", "block.minecraft.magenta_candle_cake": "Torta con vela magenta", "block.minecraft.magenta_carpet": "Alfombra magenta", "block.minecraft.magenta_concrete": "Concreto magenta", "block.minecraft.magenta_concrete_powder": "Cemento magenta", "block.minecraft.magenta_glazed_terracotta": "Azulejo magenta", "block.minecraft.magenta_shulker_box": "Caja de shulker magenta", "block.minecraft.magenta_stained_glass": "V<PERSON><PERSON> magenta", "block.minecraft.magenta_stained_glass_pane": "Panel de vidrio magenta", "block.minecraft.magenta_terracotta": "Terracota magenta", "block.minecraft.magenta_wool": "Lana magenta", "block.minecraft.magma_block": "Bloque de magma", "block.minecraft.mangrove_button": "Botón de mangle", "block.minecraft.mangrove_door": "<PERSON>uerta de mangle", "block.minecraft.mangrove_fence": "<PERSON><PERSON> de mangle", "block.minecraft.mangrove_fence_gate": "Portón de mangle", "block.minecraft.mangrove_hanging_sign": "Cartel colgante de mangle", "block.minecraft.mangrove_leaves": "Hojas de mangle", "block.minecraft.mangrove_log": "Tronco de mangle", "block.minecraft.mangrove_planks": "<PERSON><PERSON> de mangle", "block.minecraft.mangrove_pressure_plate": "Placa de presión de mangle", "block.minecraft.mangrove_propagule": "Propágulo de mangle", "block.minecraft.mangrove_roots": "<PERSON><PERSON><PERSON> de mangle", "block.minecraft.mangrove_sign": "Cartel de mangle", "block.minecraft.mangrove_slab": "<PERSON><PERSON><PERSON> de mangle", "block.minecraft.mangrove_stairs": "Escaleras de mangle", "block.minecraft.mangrove_trapdoor": "Escotilla de mangle", "block.minecraft.mangrove_wall_hanging_sign": "Cartel colgante de mangle en pared", "block.minecraft.mangrove_wall_sign": "Cartel de mangle en pared", "block.minecraft.mangrove_wood": "Leño de mangle", "block.minecraft.medium_amethyst_bud": "Brote de amatista mediano", "block.minecraft.melon": "Sandía", "block.minecraft.melon_stem": "Tallo de sandía", "block.minecraft.moss_block": "Bloque de musgo", "block.minecraft.moss_carpet": "Alfombra de musgo", "block.minecraft.mossy_cobblestone": "Roca musgosa", "block.minecraft.mossy_cobblestone_slab": "Baldosa de roca musgosa", "block.minecraft.mossy_cobblestone_stairs": "Escaleras de roca musgosa", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> de roca musgosa", "block.minecraft.mossy_stone_brick_slab": "Baldosa de ladrillos de piedra musgosos", "block.minecraft.mossy_stone_brick_stairs": "Escaleras de ladrillos de piedra musgosos", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON> de ladrillos de piedra musgosa", "block.minecraft.mossy_stone_bricks": "Ladrillos de piedra musgosos", "block.minecraft.moving_piston": "Pistón en movimiento", "block.minecraft.mud": "Barro", "block.minecraft.mud_brick_slab": "Baldosa de ladrillos de adobe", "block.minecraft.mud_brick_stairs": "Escaleras de ladrillos de adobe", "block.minecraft.mud_brick_wall": "<PERSON><PERSON> de ladrillos de adobe", "block.minecraft.mud_bricks": "Ladrillos de adobe", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON><PERSON> de mangle con barro", "block.minecraft.mushroom_stem": "Tallo de hongo", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "<PERSON><PERSON> de ladrillos del Nether", "block.minecraft.nether_brick_slab": "Baldosa de ladrillos del Nether", "block.minecraft.nether_brick_stairs": "Escaleras de ladrillos del Nether", "block.minecraft.nether_brick_wall": "<PERSON><PERSON> del Nether", "block.minecraft.nether_bricks": "Ladrillos del Nether", "block.minecraft.nether_gold_ore": "Mineral de oro del Nether", "block.minecraft.nether_portal": "Portal del Nether", "block.minecraft.nether_quartz_ore": "Mineral de cuarzo del Nether", "block.minecraft.nether_sprouts": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_wart": "Verruga del Nether", "block.minecraft.nether_wart_block": "Bloque de verrugas del Nether", "block.minecraft.netherite_block": "Bloque de netherita", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Bloque musical", "block.minecraft.oak_button": "Botón de roble", "block.minecraft.oak_door": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.oak_fence": "<PERSON><PERSON>", "block.minecraft.oak_fence_gate": "Portón de roble", "block.minecraft.oak_hanging_sign": "Cartel colgante de roble", "block.minecraft.oak_leaves": "Hojas de roble", "block.minecraft.oak_log": "Tronco de roble", "block.minecraft.oak_planks": "<PERSON><PERSON>", "block.minecraft.oak_pressure_plate": "Placa de presión de roble", "block.minecraft.oak_sapling": "<PERSON><PERSON>", "block.minecraft.oak_sign": "Cartel de roble", "block.minecraft.oak_slab": "<PERSON><PERSON><PERSON> de r<PERSON>", "block.minecraft.oak_stairs": "Escaleras de roble", "block.minecraft.oak_trapdoor": "Escotilla de roble", "block.minecraft.oak_wall_hanging_sign": "Cartel colgante de roble en pared", "block.minecraft.oak_wall_sign": "Cartel de roble en pared", "block.minecraft.oak_wood": "<PERSON><PERSON> r<PERSON>", "block.minecraft.observer": "Observador", "block.minecraft.obsidian": "Obsidiana", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON> ocre", "block.minecraft.ominous_banner": "Estandarte siniestro", "block.minecraft.open_eyeblossom": "Miraflor abierta", "block.minecraft.orange_banner": "Estandarte naranja", "block.minecraft.orange_bed": "<PERSON><PERSON> naranja", "block.minecraft.orange_candle": "<PERSON><PERSON> naranja", "block.minecraft.orange_candle_cake": "Torta con vela naranja", "block.minecraft.orange_carpet": "Alfombra naranja", "block.minecraft.orange_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "Cemento naranja", "block.minecraft.orange_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_shulker_box": "<PERSON>aja de shulker naranja", "block.minecraft.orange_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "Panel de vidrio naranja", "block.minecraft.orange_terracotta": "Terracota naranja", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON> na<PERSON>ja", "block.minecraft.orange_wool": "<PERSON>", "block.minecraft.oxeye_daisy": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "Cobre cincelado oxidado", "block.minecraft.oxidized_copper": "Cobre oxidado", "block.minecraft.oxidized_copper_bulb": "Lámpara de cobre oxidado", "block.minecraft.oxidized_copper_door": "Puerta de cobre oxidado", "block.minecraft.oxidized_copper_grate": "Rejilla de cobre oxidado", "block.minecraft.oxidized_copper_trapdoor": "Escotilla de cobre oxidado", "block.minecraft.oxidized_cut_copper": "Cobre cortado oxidado", "block.minecraft.oxidized_cut_copper_slab": "Baldosa de cobre cortado oxidado", "block.minecraft.oxidized_cut_copper_stairs": "Escaleras de cobre cortado oxidado", "block.minecraft.packed_ice": "<PERSON><PERSON> comprimido", "block.minecraft.packed_mud": "Adobe", "block.minecraft.pale_hanging_moss": "Musgo p<PERSON><PERSON>o co<PERSON>", "block.minecraft.pale_moss_block": "Bloque de musgo pálido", "block.minecraft.pale_moss_carpet": "Alfombra de musgo pálido", "block.minecraft.pale_oak_button": "Botón de roble pálido", "block.minecraft.pale_oak_door": "<PERSON><PERSON><PERSON> de roble p<PERSON>", "block.minecraft.pale_oak_fence": "<PERSON><PERSON> de roble p<PERSON>", "block.minecraft.pale_oak_fence_gate": "Portón de roble pálido", "block.minecraft.pale_oak_hanging_sign": "Cartel colgante de roble pálido", "block.minecraft.pale_oak_leaves": "Hojas de roble pálido", "block.minecraft.pale_oak_log": "Tronco de roble pálido", "block.minecraft.pale_oak_planks": "<PERSON><PERSON> de roble p<PERSON>", "block.minecraft.pale_oak_pressure_plate": "Placa de presión de roble pálido", "block.minecraft.pale_oak_sapling": "<PERSON><PERSON> de roble <PERSON>", "block.minecraft.pale_oak_sign": "Cartel de roble pálido", "block.minecraft.pale_oak_slab": "Baldosa de roble p<PERSON>", "block.minecraft.pale_oak_stairs": "Escaleras de roble pálido", "block.minecraft.pale_oak_trapdoor": "Escotilla de roble pálido", "block.minecraft.pale_oak_wall_hanging_sign": "Cartel colgante de roble pálido en pared", "block.minecraft.pale_oak_wall_sign": "Cartel de roble pálido en pared", "block.minecraft.pale_oak_wood": "Leño de roble p<PERSON>", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON>", "block.minecraft.peony": "Peonía", "block.minecraft.petrified_oak_slab": "Baldosa de roble petrificada", "block.minecraft.piglin_head": "Cabeza de piglin", "block.minecraft.piglin_wall_head": "Cabeza de piglin en pared", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_bed": "<PERSON><PERSON> rosa", "block.minecraft.pink_candle": "<PERSON><PERSON> rosa", "block.minecraft.pink_candle_cake": "Torta con vela rosa", "block.minecraft.pink_carpet": "Alfombra rosa", "block.minecraft.pink_concrete": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_concrete_powder": "Cemento rosa", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_petals": "<PERSON><PERSON><PERSON><PERSON> rosas", "block.minecraft.pink_shulker_box": "Caja de shulker rosa", "block.minecraft.pink_stained_glass": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_stained_glass_pane": "Panel de vidrio rosa", "block.minecraft.pink_terracotta": "Terracota rosa", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_wool": "<PERSON> rosa", "block.minecraft.piston": "Pistón", "block.minecraft.piston_head": "Cabeza de pistón", "block.minecraft.pitcher_crop": "Cultivo de planta jarra", "block.minecraft.pitcher_plant": "<PERSON>a jarra", "block.minecraft.player_head": "Cabeza de jugador", "block.minecraft.player_head.named": "Cabeza de %s", "block.minecraft.player_wall_head": "Cabeza de jugador en pared", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "Espeleotema puntiagudo", "block.minecraft.polished_andesite": "<PERSON><PERSON> pulida", "block.minecraft.polished_andesite_slab": "Baldosa de andesita pulida", "block.minecraft.polished_andesite_stairs": "Escaleras de andesita pulida", "block.minecraft.polished_basalt": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone": "Piedra negra pulida", "block.minecraft.polished_blackstone_brick_slab": "Baldosa de ladrillos de piedra negra pulida", "block.minecraft.polished_blackstone_brick_stairs": "Escaleras de ladrillos de piedra negra pulida", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON> de ladrillos de piedra negra pulida", "block.minecraft.polished_blackstone_bricks": "Ladrillos de piedra negra pulida", "block.minecraft.polished_blackstone_button": "Botón de piedra negra pulida", "block.minecraft.polished_blackstone_pressure_plate": "Placa de presión de piedra negra pulida", "block.minecraft.polished_blackstone_slab": "Baldosa de piedra negra pulida", "block.minecraft.polished_blackstone_stairs": "Escaleras de piedra negra pulida", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON> de piedra negra pulida", "block.minecraft.polished_deepslate": "Pizarra profunda pulida", "block.minecraft.polished_deepslate_slab": "Baldosa de pizarra profunda pulida", "block.minecraft.polished_deepslate_stairs": "Escaleras de pizarra profunda pulida", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON> de pizarra profunda pulida", "block.minecraft.polished_diorite": "<PERSON><PERSON>ta pulida", "block.minecraft.polished_diorite_slab": "Baldosa de diorita pulida", "block.minecraft.polished_diorite_stairs": "Escaleras de diorita pulida", "block.minecraft.polished_granite": "<PERSON><PERSON>", "block.minecraft.polished_granite_slab": "Baldosa de granito pulido", "block.minecraft.polished_granite_stairs": "Escaleras de granito pulido", "block.minecraft.polished_tuff": "<PERSON><PERSON> pulida", "block.minecraft.polished_tuff_slab": "Baldosa de toba pulida", "block.minecraft.polished_tuff_stairs": "Escaleras de toba pulida", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON> de toba pulida", "block.minecraft.poppy": "Amapola", "block.minecraft.potatoes": "<PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Maceta con brote de acacia", "block.minecraft.potted_allium": "Maceta con allium", "block.minecraft.potted_azalea_bush": "Maceta con azalea", "block.minecraft.potted_azure_bluet": "Maceta con rubi<PERSON>", "block.minecraft.potted_bamboo": "Maceta con bambú", "block.minecraft.potted_birch_sapling": "Maceta con brote de abedul", "block.minecraft.potted_blue_orchid": "Maceta con orquídea azul", "block.minecraft.potted_brown_mushroom": "Maceta con hongo marrón", "block.minecraft.potted_cactus": "Maceta con cactus", "block.minecraft.potted_cherry_sapling": "Maceta con brote de cerezo", "block.minecraft.potted_closed_eyeblossom": "Maceta con miraflor cerrada", "block.minecraft.potted_cornflower": "Maceta con aciano", "block.minecraft.potted_crimson_fungus": "Maceta con hongo carmesí", "block.minecraft.potted_crimson_roots": "Maceta con ra<PERSON>ces carmesí", "block.minecraft.potted_dandelion": "Maceta con diente de león", "block.minecraft.potted_dark_oak_sapling": "Maceta con brote de roble oscuro", "block.minecraft.potted_dead_bush": "Maceta con arbusto muerto", "block.minecraft.potted_fern": "Maceta con helecho", "block.minecraft.potted_flowering_azalea_bush": "Maceta con azalea florida", "block.minecraft.potted_jungle_sapling": "Maceta con brote de jungla", "block.minecraft.potted_lily_of_the_valley": "Maceta con lirio del valle", "block.minecraft.potted_mangrove_propagule": "Maceta con propágulo de mangle", "block.minecraft.potted_oak_sapling": "Maceta con brote de roble", "block.minecraft.potted_open_eyeblossom": "Maceta con miraflor abierta", "block.minecraft.potted_orange_tulip": "Maceta con tulipán naranja", "block.minecraft.potted_oxeye_daisy": "<PERSON>ta con margarita", "block.minecraft.potted_pale_oak_sapling": "Maceta con brote de roble pálido", "block.minecraft.potted_pink_tulip": "Maceta con tulipán rosa", "block.minecraft.potted_poppy": "Maceta con amapola", "block.minecraft.potted_red_mushroom": "Maceta con hongo rojo", "block.minecraft.potted_red_tulip": "Maceta con tulipán rojo", "block.minecraft.potted_spruce_sapling": "Maceta con brote de pino", "block.minecraft.potted_torchflower": "Maceta con anflorcha", "block.minecraft.potted_warped_fungus": "Maceta con hongo distorsionado", "block.minecraft.potted_warped_roots": "Maceta con raíces distorsionadas", "block.minecraft.potted_white_tulip": "Maceta con tulipán blanco", "block.minecraft.potted_wither_rose": "Maceta con rosa del Wither", "block.minecraft.powder_snow": "Nieve polvo", "block.minecraft.powder_snow_cauldron": "Caldero con nieve polvo", "block.minecraft.powered_rail": "Vía propulsora", "block.minecraft.prismarine": "Prismarina", "block.minecraft.prismarine_brick_slab": "Baldosa de ladrillos de prismarina", "block.minecraft.prismarine_brick_stairs": "Escaleras de ladrillos de prismarina", "block.minecraft.prismarine_bricks": "Ladrillos de prismarina", "block.minecraft.prismarine_slab": "Baldosa de prismarina", "block.minecraft.prismarine_stairs": "Escaleras de prismarina", "block.minecraft.prismarine_wall": "<PERSON><PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Tallo de <PERSON>llo", "block.minecraft.purple_banner": "Estandarte violeta", "block.minecraft.purple_bed": "<PERSON><PERSON> violeta", "block.minecraft.purple_candle": "<PERSON>ela violeta", "block.minecraft.purple_candle_cake": "Torta con vela violeta", "block.minecraft.purple_carpet": "Alfomb<PERSON> violeta", "block.minecraft.purple_concrete": "<PERSON><PERSON><PERSON> violeta", "block.minecraft.purple_concrete_powder": "Cemento violeta", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>a", "block.minecraft.purple_shulker_box": "<PERSON>aja de shulker violeta", "block.minecraft.purple_stained_glass": "<PERSON><PERSON><PERSON> violeta", "block.minecraft.purple_stained_glass_pane": "Panel de vidrio violeta", "block.minecraft.purple_terracotta": "Terracota violeta", "block.minecraft.purple_wool": "<PERSON>", "block.minecraft.purpur_block": "Purpur", "block.minecraft.purpur_pillar": "<PERSON><PERSON> de purpur", "block.minecraft.purpur_slab": "Baldosa de purpur", "block.minecraft.purpur_stairs": "Escaleras de purpur", "block.minecraft.quartz_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_bricks": "Ladrillos de cuarzo", "block.minecraft.quartz_pillar": "<PERSON><PERSON>", "block.minecraft.quartz_slab": "Baldo<PERSON> de cu<PERSON>zo", "block.minecraft.quartz_stairs": "Escaleras de cuarzo", "block.minecraft.rail": "Vía", "block.minecraft.raw_copper_block": "Bloque de cobre crudo", "block.minecraft.raw_gold_block": "Bloque de oro crudo", "block.minecraft.raw_iron_block": "Bloque de hierro crudo", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.red_bed": "Cama roja", "block.minecraft.red_candle": "<PERSON><PERSON> roja", "block.minecraft.red_candle_cake": "Torta con vela roja", "block.minecraft.red_carpet": "Alfombra roja", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.red_concrete_powder": "Cemento rojo", "block.minecraft.red_glazed_terracotta": "A<PERSON><PERSON>jo rojo", "block.minecraft.red_mushroom": "<PERSON><PERSON> rojo", "block.minecraft.red_mushroom_block": "Bloque de hongo rojo", "block.minecraft.red_nether_brick_slab": "Baldosa de ladrillos del Nether rojos", "block.minecraft.red_nether_brick_stairs": "Escaleras de ladrillos del Nether rojos", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON> de ladrillos del Nether rojos", "block.minecraft.red_nether_bricks": "Ladrillos del Nether rojos", "block.minecraft.red_sand": "Arena roja", "block.minecraft.red_sandstone": "Arenisca roja", "block.minecraft.red_sandstone_slab": "Baldosa de arenisca roja", "block.minecraft.red_sandstone_stairs": "Escaleras de arenisca roja", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON> de arenisca roja", "block.minecraft.red_shulker_box": "Caja de shulker roja", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.red_stained_glass_pane": "Panel de vidrio rojo", "block.minecraft.red_terracotta": "Terracota roja", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.red_wool": "<PERSON>", "block.minecraft.redstone_block": "Bloque de redstone", "block.minecraft.redstone_lamp": "Lámpara de redstone", "block.minecraft.redstone_ore": "Mineral de redstone", "block.minecraft.redstone_torch": "Antorcha de redstone", "block.minecraft.redstone_wall_torch": "<PERSON><PERSON><PERSON> de redstone en pared", "block.minecraft.redstone_wire": "Cable de redstone", "block.minecraft.reinforced_deepslate": "Pizarra profunda reforzada", "block.minecraft.repeater": "Repetidor de redstone", "block.minecraft.repeating_command_block": "Bloque de comandos de repetición", "block.minecraft.resin_block": "Bloque de resina", "block.minecraft.resin_brick_slab": "Baldosa de ladrillos de resina", "block.minecraft.resin_brick_stairs": "Escalera de ladrillos de resina", "block.minecraft.resin_brick_wall": "<PERSON><PERSON> de ladrillos de resina", "block.minecraft.resin_bricks": "<PERSON><PERSON><PERSON> resin<PERSON>", "block.minecraft.resin_clump": "Bloque de resina", "block.minecraft.respawn_anchor": "Nexo de reaparición", "block.minecraft.rooted_dirt": "<PERSON>ra enraizada", "block.minecraft.rose_bush": "<PERSON><PERSON>", "block.minecraft.sand": "Arena", "block.minecraft.sandstone": "Arenisca", "block.minecraft.sandstone_slab": "Baldosa de arenisca", "block.minecraft.sandstone_stairs": "Escaleras de arenisca", "block.minecraft.sandstone_wall": "<PERSON><PERSON>", "block.minecraft.scaffolding": "Andamio", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Catalizador de sculk", "block.minecraft.sculk_sensor": "Sensor de sculk", "block.minecraft.sculk_shrieker": "<PERSON><PERSON><PERSON> de sculk", "block.minecraft.sculk_vein": "<PERSON><PERSON> de sculk", "block.minecraft.sea_lantern": "Linterna del mar", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "Planta marina", "block.minecraft.set_spawn": "Se estableció el punto de reaparición", "block.minecraft.short_dry_grass": "Pasto seco corto", "block.minecraft.short_grass": "Pasto corto", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "<PERSON><PERSON> de <PERSON>ker", "block.minecraft.skeleton_skull": "<PERSON>r<PERSON><PERSON>", "block.minecraft.skeleton_wall_skull": "<PERSON>r<PERSON><PERSON> de esqueleto en pared", "block.minecraft.slime_block": "Bloque de slime", "block.minecraft.small_amethyst_bud": "Brote de amatista pequeño", "block.minecraft.small_dripleaf": "Plantaforma pequeña", "block.minecraft.smithing_table": "Mesa de herrería", "block.minecraft.smoker": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Ba<PERSON>to liso", "block.minecraft.smooth_quartz": "Cuarzo liso", "block.minecraft.smooth_quartz_slab": "Baldosa de cuarzo liso", "block.minecraft.smooth_quartz_stairs": "Escaleras de cuarzo liso", "block.minecraft.smooth_red_sandstone": "Arenisca roja lisa", "block.minecraft.smooth_red_sandstone_slab": "Baldosa de arenisca roja lisa", "block.minecraft.smooth_red_sandstone_stairs": "Escaleras de arenisca roja lisa", "block.minecraft.smooth_sandstone": "Arenisca lisa", "block.minecraft.smooth_sandstone_slab": "Baldosa de arenisca lisa", "block.minecraft.smooth_sandstone_stairs": "Escaleras de arenisca lisa", "block.minecraft.smooth_stone": "<PERSON><PERSON> lisa", "block.minecraft.smooth_stone_slab": "Baldosa de piedra lisa", "block.minecraft.sniffer_egg": "<PERSON><PERSON> de sniffer", "block.minecraft.snow": "<PERSON><PERSON>", "block.minecraft.snow_block": "Bloque de nieve", "block.minecraft.soul_campfire": "Fogata de almas", "block.minecraft.soul_fire": "Fuego de almas", "block.minecraft.soul_lantern": "Farol de almas", "block.minecraft.soul_sand": "Arena de almas", "block.minecraft.soul_soil": "Tierra de almas", "block.minecraft.soul_torch": "An<PERSON><PERSON> de almas", "block.minecraft.soul_wall_torch": "<PERSON><PERSON><PERSON> de almas en pared", "block.minecraft.spawn.not_valid": "No tenés cama ni nexo de reaparición cargado, o están obstruidos", "block.minecraft.spawner": "Generador de criaturas", "block.minecraft.spawner.desc1": "Interactuá con un huevo generador:", "block.minecraft.spawner.desc2": "Establece el tipo de criatura", "block.minecraft.sponge": "Esponja", "block.minecraft.spore_blossom": "Flor de esporas", "block.minecraft.spruce_button": "Botón de pino", "block.minecraft.spruce_door": "Puerta de pino", "block.minecraft.spruce_fence": "<PERSON><PERSON> de pino", "block.minecraft.spruce_fence_gate": "Portón de pino", "block.minecraft.spruce_hanging_sign": "Cartel colgante de abeto", "block.minecraft.spruce_leaves": "Hojas de pino", "block.minecraft.spruce_log": "Tronco de pino", "block.minecraft.spruce_planks": "<PERSON><PERSON> de pino", "block.minecraft.spruce_pressure_plate": "Placa de presión de pino", "block.minecraft.spruce_sapling": "<PERSON><PERSON> de pino", "block.minecraft.spruce_sign": "Cartel de pino", "block.minecraft.spruce_slab": "Baldosa de pino", "block.minecraft.spruce_stairs": "Escaleras de pino", "block.minecraft.spruce_trapdoor": "Escotilla de pino", "block.minecraft.spruce_wall_hanging_sign": "Cartel colgante de abeto en pared", "block.minecraft.spruce_wall_sign": "Cartel de pino en pared", "block.minecraft.spruce_wood": "Leño de pino", "block.minecraft.sticky_piston": "<PERSON><PERSON><PERSON> pegajoso", "block.minecraft.stone": "Piedra", "block.minecraft.stone_brick_slab": "Baldosa de ladrillos de piedra", "block.minecraft.stone_brick_stairs": "Escaleras de ladrillos de piedra", "block.minecraft.stone_brick_wall": "<PERSON><PERSON> de ladrillos de piedra", "block.minecraft.stone_bricks": "Ladrillos de piedra", "block.minecraft.stone_button": "Botón de piedra", "block.minecraft.stone_pressure_plate": "Placa de presión de piedra", "block.minecraft.stone_slab": "Baldosa de piedra", "block.minecraft.stone_stairs": "Escaleras de piedra", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Tronco de acacia sin corteza", "block.minecraft.stripped_acacia_wood": "Leño de acacia sin corteza", "block.minecraft.stripped_bamboo_block": "Bloque de bambú sin corteza", "block.minecraft.stripped_birch_log": "Tronco de abedul sin corteza", "block.minecraft.stripped_birch_wood": "Leño de abedul sin corteza", "block.minecraft.stripped_cherry_log": "Tronco de cerezo sin corteza", "block.minecraft.stripped_cherry_wood": "Leño de cerezo sin corteza", "block.minecraft.stripped_crimson_hyphae": "Hifas carmesí sin corteza", "block.minecraft.stripped_crimson_stem": "Tallo carmesí sin corteza", "block.minecraft.stripped_dark_oak_log": "Tronco de roble oscuro sin corteza", "block.minecraft.stripped_dark_oak_wood": "Leño de roble oscuro sin corteza", "block.minecraft.stripped_jungle_log": "Tronco de jungla sin corteza", "block.minecraft.stripped_jungle_wood": "Leño de jungla sin corteza", "block.minecraft.stripped_mangrove_log": "Tronco de mangle sin corteza", "block.minecraft.stripped_mangrove_wood": "Leño de mangle sin corteza", "block.minecraft.stripped_oak_log": "Tronco de roble sin corteza", "block.minecraft.stripped_oak_wood": "Leño de roble sin corteza", "block.minecraft.stripped_pale_oak_log": "Tronco de roble pálido sin corteza", "block.minecraft.stripped_pale_oak_wood": "Leño de roble pálido sin corteza", "block.minecraft.stripped_spruce_log": "Tronco de pino sin corteza", "block.minecraft.stripped_spruce_wood": "Leño de pino sin corteza", "block.minecraft.stripped_warped_hyphae": "Hifas distorsionadas sin corteza", "block.minecraft.stripped_warped_stem": "Tallo distorsionado sin corteza", "block.minecraft.structure_block": "Bloque de estructuras", "block.minecraft.structure_void": "Vacío de estructuras", "block.minecraft.sugar_cane": "Caña de azúcar", "block.minecraft.sunflower": "Girasol", "block.minecraft.suspicious_gravel": "<PERSON><PERSON><PERSON> sospechosa", "block.minecraft.suspicious_sand": "Arena sospechosa", "block.minecraft.sweet_berry_bush": "Arbusto de bayas dulces", "block.minecraft.tall_dry_grass": "Pasto seco alto", "block.minecraft.tall_grass": "Pasto alto", "block.minecraft.tall_seagrass": "Planta marina alta", "block.minecraft.target": "<PERSON>", "block.minecraft.terracotta": "Terracota", "block.minecraft.test_block": "Bloque de pruebas", "block.minecraft.test_instance_block": "Bloque de instancia de prueba", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON> opaco", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "Las explosiones de TNT están desactivadas", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "An<PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "Cultivo de anflorcha", "block.minecraft.trapped_chest": "<PERSON><PERSON> trampa", "block.minecraft.trial_spawner": "Generador de desafío", "block.minecraft.tripwire": "<PERSON><PERSON> trampa", "block.minecraft.tripwire_hook": "<PERSON><PERSON><PERSON>", "block.minecraft.tube_coral": "Coral de tubo", "block.minecraft.tube_coral_block": "Bloque de coral de tubo", "block.minecraft.tube_coral_fan": "Gorgonia de tubo", "block.minecraft.tube_coral_wall_fan": "<PERSON><PERSON><PERSON> de tubo en pared", "block.minecraft.tuff": "Toba", "block.minecraft.tuff_brick_slab": "Baldosa de ladrillos de toba", "block.minecraft.tuff_brick_stairs": "Escaleras de ladrillos de toba", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON> de ladrill<PERSON> de toba", "block.minecraft.tuff_bricks": "Ladrillos de toba", "block.minecraft.tuff_slab": "Baldosa de to<PERSON>", "block.minecraft.tuff_stairs": "Escaleras de toba", "block.minecraft.tuff_wall": "<PERSON><PERSON>", "block.minecraft.turtle_egg": "Huevo de tortuga", "block.minecraft.twisting_vines": "Enredaderas retorcidas", "block.minecraft.twisting_vines_plant": "Enredaderas retorcidas", "block.minecraft.vault": "Arca", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON> verde", "block.minecraft.vine": "Enredaderas", "block.minecraft.void_air": "Aire del vacío", "block.minecraft.wall_torch": "An<PERSON><PERSON> en pared", "block.minecraft.warped_button": "Botón distorsionado", "block.minecraft.warped_door": "Puerta distorsionada", "block.minecraft.warped_fence": "Reja distorsionada", "block.minecraft.warped_fence_gate": "Portón distorsionado", "block.minecraft.warped_fungus": "Hongo distorsionado", "block.minecraft.warped_hanging_sign": "Cartel colgante distorsionado", "block.minecraft.warped_hyphae": "Hifas distorsionadas", "block.minecraft.warped_nylium": "<PERSON><PERSON> distorsion<PERSON>", "block.minecraft.warped_planks": "Madera distorsionada", "block.minecraft.warped_pressure_plate": "Placa de presión distorsionada", "block.minecraft.warped_roots": "Raíces distorsionadas", "block.minecraft.warped_sign": "Cartel distorsionado", "block.minecraft.warped_slab": "Baldosa distorsionada", "block.minecraft.warped_stairs": "Escaleras distorsionadas", "block.minecraft.warped_stem": "Tallo distorsionado", "block.minecraft.warped_trapdoor": "Escotilla distorsionada", "block.minecraft.warped_wall_hanging_sign": "Cartel colgante distorsionado en pared", "block.minecraft.warped_wall_sign": "Cartel distorsionado de pared", "block.minecraft.warped_wart_block": "Bloque de verrugas distorsionadas", "block.minecraft.water": "Agua", "block.minecraft.water_cauldron": "Caldero con agua", "block.minecraft.waxed_chiseled_copper": "Cobre cincelado encerado", "block.minecraft.waxed_copper_block": "Bloque de cobre encerado", "block.minecraft.waxed_copper_bulb": "Lámpara de cobre encerado", "block.minecraft.waxed_copper_door": "Puerta de cobre encerado", "block.minecraft.waxed_copper_grate": "<PERSON><PERSON><PERSON> de cobre encerado", "block.minecraft.waxed_copper_trapdoor": "Escotilla de cobre encerado", "block.minecraft.waxed_cut_copper": "Cobre cortado encerado", "block.minecraft.waxed_cut_copper_slab": "Baldosa de cobre cortado encerado", "block.minecraft.waxed_cut_copper_stairs": "Escaleras de cobre cortado encerado", "block.minecraft.waxed_exposed_chiseled_copper": "Cobre cincelado expuesto encerado", "block.minecraft.waxed_exposed_copper": "Cobre expuesto encerado", "block.minecraft.waxed_exposed_copper_bulb": "Lámpara de cobre expuesto encerado", "block.minecraft.waxed_exposed_copper_door": "Puerta de cobre expuesto encerado", "block.minecraft.waxed_exposed_copper_grate": "Rejilla de cobre expuesto encerado", "block.minecraft.waxed_exposed_copper_trapdoor": "Escotilla de cobre expuesto encerado", "block.minecraft.waxed_exposed_cut_copper": "Cobre cortado expuesto encerado", "block.minecraft.waxed_exposed_cut_copper_slab": "Baldosa de cobre cortado expuesto encerado", "block.minecraft.waxed_exposed_cut_copper_stairs": "Escaleras de cobre cortado expuesto encerado", "block.minecraft.waxed_oxidized_chiseled_copper": "Cobre cincelado oxidado encerado", "block.minecraft.waxed_oxidized_copper": "Cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_bulb": "Lámpara de cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_door": "Puerta de cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_grate": "Rejilla de cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_trapdoor": "Escotilla de cobre oxidado encerado", "block.minecraft.waxed_oxidized_cut_copper": "Cobre cortado oxidado encerado", "block.minecraft.waxed_oxidized_cut_copper_slab": "Baldosa de cobre cortado oxidado encerado", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Escaleras de cobre cortado oxidado encerado", "block.minecraft.waxed_weathered_chiseled_copper": "Cobre cincelado degradado encerado", "block.minecraft.waxed_weathered_copper": "Cobre degradado encerado", "block.minecraft.waxed_weathered_copper_bulb": "Lámpara de cobre degradado encerado", "block.minecraft.waxed_weathered_copper_door": "Puerta de cobre degradado encerado", "block.minecraft.waxed_weathered_copper_grate": "Rejilla de cobre degradado encerado", "block.minecraft.waxed_weathered_copper_trapdoor": "Escotilla de cobre degradado encerado", "block.minecraft.waxed_weathered_cut_copper": "Cobre cortado degradado encerado", "block.minecraft.waxed_weathered_cut_copper_slab": "Baldosa de cobre cortado degradado encerado", "block.minecraft.waxed_weathered_cut_copper_stairs": "Escaleras de cobre cortado degradado encerado", "block.minecraft.weathered_chiseled_copper": "Cobre cincelado degradado", "block.minecraft.weathered_copper": "Cobre degradado", "block.minecraft.weathered_copper_bulb": "Lámpara de cobre degradado", "block.minecraft.weathered_copper_door": "Puerta de cobre degradado", "block.minecraft.weathered_copper_grate": "Rejilla de cobre degradado", "block.minecraft.weathered_copper_trapdoor": "Escotilla de cobre degradado", "block.minecraft.weathered_cut_copper": "Cobre cortado oxidado", "block.minecraft.weathered_cut_copper_slab": "Baldosa de cobre cortado oxidado", "block.minecraft.weathered_cut_copper_stairs": "Escaleras de cobre cortado oxidado", "block.minecraft.weeping_vines": "Enredaderas lloronas", "block.minecraft.weeping_vines_plant": "Planta de enredaderas lloronas", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wheat": "Cultivo de trigo", "block.minecraft.white_banner": "Estandarte blanco", "block.minecraft.white_bed": "Cama blanca", "block.minecraft.white_candle": "<PERSON>ela blanca", "block.minecraft.white_candle_cake": "Torta con vela blanca", "block.minecraft.white_carpet": "Alfombra blanca", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.white_concrete_powder": "Cemento blanco", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> blanco", "block.minecraft.white_shulker_box": "Caja de shulker blanca", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.white_stained_glass_pane": "Panel de vidrio blanco", "block.minecraft.white_terracotta": "Terracota blanca", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.white_wool": "<PERSON> blan<PERSON>", "block.minecraft.wildflowers": "<PERSON> silvestres", "block.minecraft.wither_rose": "<PERSON>", "block.minecraft.wither_skeleton_skull": "Cráneo de esqueleto <PERSON>er", "block.minecraft.wither_skeleton_wall_skull": "Cráneo de esqueleto del Wither en pared", "block.minecraft.yellow_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_bed": "<PERSON><PERSON> amarilla", "block.minecraft.yellow_candle": "<PERSON><PERSON> amarilla", "block.minecraft.yellow_candle_cake": "Torta con vela amarilla", "block.minecraft.yellow_carpet": "Alfombra amarilla", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON> de shulker amarilla", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass_pane": "Panel de vidrio amarillo", "block.minecraft.yellow_terracotta": "Terracota amarilla", "block.minecraft.yellow_wool": "<PERSON>", "block.minecraft.zombie_head": "Cabeza de zombi", "block.minecraft.zombie_wall_head": "Cabeza de zombi en pared", "book.byAuthor": "por %1$s", "book.edit.title": "Pantalla de edición del libro", "book.editTitle": "Título del libro:", "book.finalizeButton": "<PERSON><PERSON><PERSON> y cerrar", "book.finalizeWarning": "¡Ojo! Si se firma deja de ser editable.", "book.generation.0": "Original", "book.generation.1": "Copia del original", "book.generation.2": "Copia de una copia", "book.generation.3": "<PERSON><PERSON><PERSON>", "book.invalid.tag": "* Tag de libro inválido *", "book.pageIndicator": "Página %1$s de %2$s", "book.page_button.next": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "book.page_button.previous": "Página anterior", "book.sign.title": "Pantalla de firma del libro", "book.sign.titlebox": "<PERSON><PERSON><PERSON><PERSON>", "book.signButton": "<PERSON><PERSON><PERSON>", "book.view.title": "Pantalla de vista del libro", "build.tooHigh": "El límite de altura para la construcción es %s", "chat.cannotSend": "No se pudo enviar el mensaje, revisá las opciones de chat", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Hacé click para teletransportarte", "chat.copy": "<PERSON><PERSON><PERSON> enlace", "chat.copy.click": "Hacé click para copiar", "chat.deleted_marker": "Este mensaje fue eliminado por el servidor.", "chat.disabled.chain_broken": "El chat se deshabilitó debido a una cadena rota. Por favor, intentá reconectar.", "chat.disabled.expiredProfileKey": "Se desactivó el chat debido a que la clave pública del perfil caducó. Por favor, intentá reconectarte.", "chat.disabled.invalid_command_signature": "El comando tuvo firmas de argumento de comando inesperadas o faltantes.", "chat.disabled.invalid_signature": "El chat tuvo una firma inválida. Por favor, intentá reconectarte.", "chat.disabled.launcher": "Chat desactivado en las opciones del lanzador. No se puede mandar el mensaje.", "chat.disabled.missingProfileKey": "Se desactivó el chat debido a que no se encontró la clave pública del perfil. Por favor, intentá reconectarte.", "chat.disabled.options": "Chat desactivado en las opciones del cliente.", "chat.disabled.out_of_order_chat": "El chat se recibió desordenado. ¿Se cambió la hora del sistema?", "chat.disabled.profile": "Chat no permitido por los ajustes de la cuenta. Apretá «%s» otra vez para más información.", "chat.disabled.profile.moreInfo": "Chat desactivado en los ajustes de la cuenta. No se pueden ver ni enviar mensajes.", "chat.editBox": "chat", "chat.filtered": "Filtrado por el servidor.", "chat.filtered_full": "El servidor ocultó tu mensaje para algunos jugadores.", "chat.link.confirm": "¿Seguro que querés abrir esta página web?", "chat.link.confirmTrusted": "¿Querés abrir esta web o copiar su enlace al portapapeles?", "chat.link.open": "<PERSON><PERSON><PERSON> enlace", "chat.link.warning": "¡Nunca abras enlaces de gente desconocida!", "chat.queue": "[+%s mensajes en espera]", "chat.square_brackets": "[%s]", "chat.tag.error": "El servidor mandó un mensaje inválido.", "chat.tag.modified": "Mensaje modificado por el servidor. Original:", "chat.tag.not_secure": "Mensaje no verificado. No se puede reportar.", "chat.tag.system": "Mensaje del servidor. No se puede reportar.", "chat.tag.system_single_player": "Mensaje del servidor.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s completó el desafío %s", "chat.type.advancement.goal": "%s alcanzó el objetivo %s", "chat.type.advancement.task": "%s consiguió el progreso %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Enviar mensaje al equipo", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s ha escrito: %s", "chat.validation_error": "Error de validación del chat", "chat_screen.message": "Mensaje a mandar: %s", "chat_screen.title": "Pantalla del chat", "chat_screen.usage": "Escribí un mensaje y presioná Enter para enviar", "chunk.toast.checkLog": "Ver registro para más detalles", "chunk.toast.loadFailure": "Error al cargar el chunk en %s", "chunk.toast.lowDiskSpace": "¡Queda poco espacio en el disco!", "chunk.toast.lowDiskSpace.description": "Es posible que no se pueda guardar el mundo.", "chunk.toast.saveFailure": "Error al guardar el chunk en %s", "clear.failed.multiple": "No se encontraron ítems en el inventario de %s jugadores", "clear.failed.single": "No se encontraron ítems en el inventario de %s", "color.minecraft.black": "Negro", "color.minecraft.blue": "Azul", "color.minecraft.brown": "<PERSON>r<PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON>", "color.minecraft.green": "Verde", "color.minecraft.light_blue": "Celeste", "color.minecraft.light_gray": "<PERSON><PERSON> claro", "color.minecraft.lime": "Verde lima", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "<PERSON><PERSON><PERSON>", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "<PERSON><PERSON>", "color.minecraft.red": "<PERSON><PERSON><PERSON>", "color.minecraft.white": "<PERSON>", "color.minecraft.yellow": "Amarillo", "command.context.here": "<--[<PERSON>Á]", "command.context.parse_error": "%s en la posición %s: %s", "command.exception": "No se puede analizar el comando: %s", "command.expected.separator": "Se requiere un espacio en blanco para concluir un argumento. Algunos datos están escritos sin separar.", "command.failed": "Se produjo un error inesperado al intentar ejecutar ese comando", "command.forkLimit": "Se alcanzó el número máximo de contextos (%s)", "command.unknown.argument": "<PERSON><PERSON><PERSON><PERSON>", "command.unknown.command": "Comando desconocido o incompleto, mirá el error abajo", "commands.advancement.criterionNotFound": "El progreso %1$s no contiene el criterio «%2$s»", "commands.advancement.grant.criterion.to.many.failure": "No se pudo dar el criterio «%s» del progreso %s a %s jugadores porque ya lo tienen", "commands.advancement.grant.criterion.to.many.success": "Se dio el criterio «%s» del progreso %s a %s jugadores", "commands.advancement.grant.criterion.to.one.failure": "No se pudo dar el criterio «%s» del progreso %s a %s porque ya lo tiene", "commands.advancement.grant.criterion.to.one.success": "Se dio el criterio «%s» del progreso %s a %s", "commands.advancement.grant.many.to.many.failure": "No se pudo dar %s progresos a %s jugadores porque ya los tienen", "commands.advancement.grant.many.to.many.success": "Se dieron %s progresos a %s jugadores", "commands.advancement.grant.many.to.one.failure": "No se pudo dar %s progresos a %s porque ya los tiene", "commands.advancement.grant.many.to.one.success": "Se dieron %s progresos a %s", "commands.advancement.grant.one.to.many.failure": "No se pudo dar el progreso %s a %s jugadores porque ya lo tienen", "commands.advancement.grant.one.to.many.success": "Se dio el progreso %s a %s jugadores", "commands.advancement.grant.one.to.one.failure": "No se pudo dar el progreso %s a %s porque ya lo tiene", "commands.advancement.grant.one.to.one.success": "Se dio el progreso %s a %s", "commands.advancement.revoke.criterion.to.many.failure": "No se les pudo borrar el criterio «%s» del progreso %s a %s jugadores porque no lo tienen", "commands.advancement.revoke.criterion.to.many.success": "Se borró el criterio «%s» del progreso %s a %s jugadores", "commands.advancement.revoke.criterion.to.one.failure": "No se pudo borrar el criterio «%s» del progreso %s a %s porque no lo tiene", "commands.advancement.revoke.criterion.to.one.success": "Se borró el criterio «%s» del progreso %s a %s", "commands.advancement.revoke.many.to.many.failure": "No se les pudo borrar %s progresos a %s jugadores porque no los tienen", "commands.advancement.revoke.many.to.many.success": "Se borraron %s progresos de %s jugadores", "commands.advancement.revoke.many.to.one.failure": "No se le pudo borrar %s progresos a %s porque no los tiene", "commands.advancement.revoke.many.to.one.success": "Se borraron %s progresos de %s", "commands.advancement.revoke.one.to.many.failure": "No se pudo borrar el progreso %s a %s jugadores porque no lo tienen", "commands.advancement.revoke.one.to.many.success": "Se borró el progreso %s a %s jugadores", "commands.advancement.revoke.one.to.one.failure": "No se le pudo borrar el progreso %s a %s porque no lo tiene", "commands.advancement.revoke.one.to.one.success": "Se borró el progreso %s de %s", "commands.attribute.base_value.get.success": "El valor base del atributo %s de la entidad %s es %s", "commands.attribute.base_value.reset.success": "El valor base del atributo %s para la entidad %s se restablecio en %s", "commands.attribute.base_value.set.success": "Se estableció el valor base del atributo %s de la entidad %s en %s", "commands.attribute.failed.entity": "%s no es una entidad válida para este comando", "commands.attribute.failed.modifier_already_present": "El modificador %s ya está aplicado al atributo %s de la entidad %s", "commands.attribute.failed.no_attribute": "La entidad %s no tiene el atributo %s", "commands.attribute.failed.no_modifier": "El atributo %s de la entidad %s no tiene el modificador %s", "commands.attribute.modifier.add.success": "Se agregó el modificador %s al atributo %s de la entidad %s", "commands.attribute.modifier.remove.success": "Se borró el modificador %s del atributo %s de la entidad %s", "commands.attribute.modifier.value.get.success": "El valor del modificador %s del atributo %s de la entidad %s es %s", "commands.attribute.value.get.success": "El valor del atributo %s de la entidad %s es %s", "commands.ban.failed": "Nada cambió: el jugador ya estaba bloqueado", "commands.ban.success": "Se bloqueó a %s: %s", "commands.banip.failed": "Nada cambió: la dirección IP ya estaba bloqueada", "commands.banip.info": "Este ban afecta a %s jugador(es): %s", "commands.banip.invalid": "Dirección de IP no válida o jugador desconocido", "commands.banip.success": "Se bloqueó la dirección IP %s: %s", "commands.banlist.entry": "%s fue bloqueado/a por %s: %s", "commands.banlist.entry.unknown": "(Desconocida)", "commands.banlist.list": "Hay %s cuenta(s) bloqueada(s):", "commands.banlist.none": "No hay cuentas bloqueadas", "commands.bossbar.create.failed": "Ya existe una barra de boss con ID «%s»", "commands.bossbar.create.success": "Se creó la barra de boss «%s»", "commands.bossbar.get.max": "La barra de boss «%s» tiene un valor máximo de %s", "commands.bossbar.get.players.none": "La barra de boss «%s» no tiene jugadores conectados", "commands.bossbar.get.players.some": "La barra de jefe %s tiene %s jugador(es) en línea: %s", "commands.bossbar.get.value": "La barra de boss «%s» tiene un valor de %s", "commands.bossbar.get.visible.hidden": "La barra de boss «%s» está oculta", "commands.bossbar.get.visible.visible": "La barra de boss «%s» es visible", "commands.bossbar.list.bars.none": "No hay ninguna barra de boss activa", "commands.bossbar.list.bars.some": "Hay %s barra(s) de jefe activa(s): %s", "commands.bossbar.remove.success": "Se eliminó la barra de boss «%s»", "commands.bossbar.set.color.success": "Se cambió el color de la barra de boss «%s»", "commands.bossbar.set.color.unchanged": "Nada cambió: ese ya era el color de esta barra de boss", "commands.bossbar.set.max.success": "Se cambió el máximo de la barra de boss «%s» a %s", "commands.bossbar.set.max.unchanged": "Nada cambió: ese ya era el máximo de esta barra de boss", "commands.bossbar.set.name.success": "Se renombró la barra de boss «%s»", "commands.bossbar.set.name.unchanged": "Nada cambió: ese ya era el nombre de esta barra de boss", "commands.bossbar.set.players.success.none": "La barra de boss «%s» no tiene jugadores asociados", "commands.bossbar.set.players.success.some": "La barra de jefe «%s» ahora tiene %s jugador(es): %s", "commands.bossbar.set.players.unchanged": "Nada cambió: esos jugadores ya estaban en la barra de boss sin nadie al que agregar o quitar", "commands.bossbar.set.style.success": "Se cambió el estilo de la barra de boss «%s»", "commands.bossbar.set.style.unchanged": "Nada cambió: ese ya era el estilo de esta barra de boss", "commands.bossbar.set.value.success": "Se cambió el valor de la barra de boss «%s» a %s", "commands.bossbar.set.value.unchanged": "Nada cambió: ese ya era el valor de esta barra de boss", "commands.bossbar.set.visibility.unchanged.hidden": "Nada cambió: la barra de boss ya estaba oculta", "commands.bossbar.set.visibility.unchanged.visible": "Nada cambió: la barra de boss ya estaba mostrándose", "commands.bossbar.set.visible.success.hidden": "La barra de boss «%s» ahora está oculta", "commands.bossbar.set.visible.success.visible": "La barra de boss «%s» ahora es visible", "commands.bossbar.unknown": "No existe ninguna barra de boss con ID «%s»", "commands.clear.success.multiple": "Se eliminaron %s objeto(s) de %s jugadores", "commands.clear.success.single": "Se eliminaron %s objeto(s) del jugador %s", "commands.clear.test.multiple": "Hay %s objeto(s) que coinciden en el inventario de %s jugadores", "commands.clear.test.single": "Hay %s objeto(s) que coinciden en el inventario de %s", "commands.clone.failed": "No se clonaron bloques", "commands.clone.overlap": "El área de origen y el área de destino no pueden ser iguales", "commands.clone.success": "Bloques clonados con éxito: %s", "commands.clone.toobig": "Hay demasiados bloques en el área especificada (el máximo es %s, se especificaron %s)", "commands.damage.invulnerable": "El objetivo es invulnerable al tipo de daño infligido", "commands.damage.success": "Se aplicó %s de daño a %s", "commands.data.block.get": "%s en el bloque en %s, %s, %s después de la escala de %s cambió a %s", "commands.data.block.invalid": "El bloque especificado no es una entidad-bloque", "commands.data.block.modified": "Se modificaron los datos del bloque en %s, %s, %s", "commands.data.block.query": "El bloque en %s, %s, %s contiene los siguientes datos: %s", "commands.data.entity.get": "%s de %s, después de un factor escala de %s, cambió a %s", "commands.data.entity.invalid": "No se pudieron modificar los datos del jugador", "commands.data.entity.modified": "Se modificaron los datos de la entidad %s", "commands.data.entity.query": "La entidad %s tiene los siguientes datos: %s", "commands.data.get.invalid": "No se pudo obtener %s, sólo se permite tag numérico", "commands.data.get.multiple": "Este argumento sólo acepta un valor NBT", "commands.data.get.unknown": "No se pudo obtener %s, el tag no existe", "commands.data.merge.failed": "Nada cambió: las propiedades especificadas ya tenían esos valores", "commands.data.modify.expected_list": "Se requiere una lista (recibido: %s)", "commands.data.modify.expected_object": "Se requiere un objeto (recibido: %s)", "commands.data.modify.expected_value": "Se requiere un valor (recibido: %s)", "commands.data.modify.invalid_index": "Índice de lista no válido: %s", "commands.data.modify.invalid_substring": "Índices de subcadena no válidos: de %s a %s", "commands.data.storage.get": "%s en el contenedor %s, luego de un factor escala de %s, es %s", "commands.data.storage.modified": "Se modificó el contenedor %s", "commands.data.storage.query": "El contenedor %s tiene: %s", "commands.datapack.create.already_exists": "Ya existe un paquete con ese nombre: %s", "commands.datapack.create.invalid_full_name": "«%s» no es un nombre de paquete válido", "commands.datapack.create.invalid_name": "Caracteres no válidos en el nuevo nombre del paquete: %s", "commands.datapack.create.io_failure": "No se pudo crear un paquete con el nombre «%s», revisá los registros", "commands.datapack.create.metadata_encode_failure": "Error al codificar los metadatos del paquete con el nombre «%s»: %s", "commands.datapack.create.success": "Se creó un nuevo paquete vacío con el nombre «%s»", "commands.datapack.disable.failed": "¡El paquete de datos «%s» no estaba activado!", "commands.datapack.disable.failed.feature": "¡El paquete de datos «%s» no se puede desactivar porque es parte de una variable activa!", "commands.datapack.enable.failed": "¡El paquete de datos «%s» ya estaba activado!", "commands.datapack.enable.failed.no_flags": "El paquete de datos «%s» no puede activarse porque las variables necesarias no fueron activadas en este mundo: «%s»", "commands.datapack.list.available.none": "No hay más paquetes de datos disponibles", "commands.datapack.list.available.success": "Hay %s paquete(s) de datos disponible(s): %s", "commands.datapack.list.enabled.none": "No hay paquetes de datos habilitados", "commands.datapack.list.enabled.success": "Hay %s paquete(s) de datos activo(s): %s", "commands.datapack.modify.disable": "Desactivando el paquete de datos %s", "commands.datapack.modify.enable": "Activando el paquete de datos %s", "commands.datapack.unknown": "Paquete de datos desconocido: %s", "commands.debug.alreadyRunning": "El perfilador de ciclos ya se ha iniciado", "commands.debug.function.noRecursion": "No se puede rastrear dentro de una función", "commands.debug.function.noReturnRun": "No se puede rastrear con «/return run»", "commands.debug.function.success.multiple": "Se rastreó %s comando(s) de %s funciones al archivo de salida %s", "commands.debug.function.success.single": "Se rastreó %s comando(s) de la función «%s» al archivo de salida %s", "commands.debug.function.traceFailed": "Error al rastrear función", "commands.debug.notRunning": "El perfilador de ciclos no se ha iniciado", "commands.debug.started": "Se ha iniciado el perfilado de ciclos", "commands.debug.stopped": "Se detuvo el perfilador de tics tras %s segundo(s) y %s tic(s) (%s tic(s) por segundo)", "commands.defaultgamemode.success": "El modo de juego por defecto ahora es %s", "commands.deop.failed": "Nada cambió: el jugador no era administrador(a)", "commands.deop.success": "%s ya no es administrador(a) del servidor", "commands.dialog.clear.multiple": "Diálogo cerrado para %s jugadores", "commands.dialog.clear.single": "Diálogo cerrado para %s", "commands.dialog.show.multiple": "Mostrando diálogo a %s jugadores", "commands.dialog.show.single": "Mostrando diálogo a %s", "commands.difficulty.failure": "La dificultad no cambió, ya estaba en %s", "commands.difficulty.query": "La dificultad está configurada en %s", "commands.difficulty.success": "La dificultad se estableció en %s", "commands.drop.no_held_items": "La entidad no puede agarrar ítems", "commands.drop.no_loot_table": "La entidad %s no tiene tabla de botín", "commands.drop.no_loot_table.block": "El bloque %s no tiene tabla de botín", "commands.drop.success.multiple": "Se tiraron %s ítems", "commands.drop.success.multiple_with_table": "Se tiraron %s ítems de la tabla de botín %s", "commands.drop.success.single": "ítems soltados: %s de %s", "commands.drop.success.single_with_table": "Ítems soltados de la tabla de botín %3$s: %1$s de %2$s", "commands.effect.clear.everything.failed": "El objetivo no tiene efectos para borrar", "commands.effect.clear.everything.success.multiple": "Se borraron todos los efectos de %s objetivos", "commands.effect.clear.everything.success.single": "Se borraron todos los efectos de %s", "commands.effect.clear.specific.failed": "El objetivo no tiene el efecto especificado", "commands.effect.clear.specific.success.multiple": "Se borró el efecto %s de %s objetivos", "commands.effect.clear.specific.success.single": "Se borró el efecto %s de %s", "commands.effect.give.failed": "No se pudo aplicar este efecto (el objetivo es inmune a los efectos o tiene algo de mayor fuerza)", "commands.effect.give.success.multiple": "Se aplicó el efecto %s a %s objetivos", "commands.effect.give.success.single": "Se aplicó el efecto %s a %s", "commands.enchant.failed": "Nada cambió: el objetivo no tenía un ítem en la mano o el ítem no es compatible con el encantamiento", "commands.enchant.failed.entity": "%s no es una entidad válida para este comando", "commands.enchant.failed.incompatible": "%s no admite este encantamiento", "commands.enchant.failed.itemless": "%s no tiene ningún ítem en sus manos", "commands.enchant.failed.level": "%s es superior al nivel máximo del encantamiento (%s)", "commands.enchant.success.multiple": "Se aplicó %s a %s entidades", "commands.enchant.success.single": "Se aplicó %s al ítem de %s", "commands.execute.blocks.toobig": "Hay demasiados bloques en el área especificada (el máximo es %s, se especificaron %s)", "commands.execute.conditional.fail": "El test no se superó", "commands.execute.conditional.fail_count": "El test no se superó, cantidad: %s", "commands.execute.conditional.pass": "El test se superó", "commands.execute.conditional.pass_count": "El test se superó, cantidad: %s", "commands.execute.function.instantiationFailure": "Error al realizar la función %s: %s", "commands.experience.add.levels.success.multiple": "Se agregaron %s niveles de experiencia a %s jugadores", "commands.experience.add.levels.success.single": "Se agregaron %s niveles de experiencia a %s", "commands.experience.add.points.success.multiple": "Se agregaron %s puntos de experiencia a %s jugadores", "commands.experience.add.points.success.single": "Se agregaron %s puntos de experiencia a %s", "commands.experience.query.levels": "%s tiene %s niveles de experiencia", "commands.experience.query.points": "%s tiene %s puntos de experiencia", "commands.experience.set.levels.success.multiple": "Se estableció el nivel de experiencia de %2$s jugadores en %1$s", "commands.experience.set.levels.success.single": "Se estableció el nivel de experiencia de %2$s en %1$s", "commands.experience.set.points.invalid": "No se pueden establecer puntos de experiencia por encima del máximo de puntos actual", "commands.experience.set.points.success.multiple": "Se establecieron los puntos de experiencia de %2$s jugadores en %1$s", "commands.experience.set.points.success.single": "Se establecieron los puntos de experiencia de %2$s en %1$s", "commands.fill.failed": "No se rellenaron bloques", "commands.fill.success": "Bloques rellenados con éxito: %s", "commands.fill.toobig": "Hay demasiados bloques en el área especificada (el máximo es %s, se especificaron %s)", "commands.fillbiome.success": "Biomas colocados entre %s, %s, %s y %s, %s, %s", "commands.fillbiome.success.count": "Hay %s entrada(s) de biomas entre %s, %s, %s y %s, %s, %s", "commands.fillbiome.toobig": "Demasiados bloques en el volumen especificado (el máximo es %s, se especificaron %s)", "commands.forceload.added.failure": "La carga forzosa no se activó en ningún chunk", "commands.forceload.added.multiple": "Carga forzosa activada para %s chunks de %s (de %s a %s)", "commands.forceload.added.none": "No hay chunks con carga forzosa en %s", "commands.forceload.added.single": "Carga forzosa activada para el chunk %s de %s", "commands.forceload.list.multiple": "Hay %s chunks con carga forzosa en %s: %s", "commands.forceload.list.single": "Hay 1 chunk con carga forzosa en %s: %s", "commands.forceload.query.failure": "El chunk %s de %s tiene la carga forzosa desactivada", "commands.forceload.query.success": "El chunk %s de %s tiene la carga forzosa activada", "commands.forceload.removed.all": "Se desactivó la carga forzosa en todos los chunks de %s", "commands.forceload.removed.failure": "La carga forzosa no se desactivó de ningún chunk", "commands.forceload.removed.multiple": "Carga forzosa desactivada para %s chunks de %s (de %s a %)", "commands.forceload.removed.single": "Carga forzosa desactivada para el chunk %s de %s", "commands.forceload.toobig": "Hay demasiados chunks en la zona especificada (el máximo es %s, se especificaron %s)", "commands.function.error.argument_not_compound": "Tipo de argumento no válido: %s, se esperaba Compund", "commands.function.error.missing_argument": "Falta el argumento %2$s en la función %1$s", "commands.function.error.missing_arguments": "Faltan argumentos en la función %s", "commands.function.error.parse": "Al ejecutar la macro %s: el comando «%s» provocó el error: %s", "commands.function.instantiationFailure": "Error al realizar la función %s: %s", "commands.function.result": "La función «%s» devolvió %s", "commands.function.scheduled.multiple": "Funciones en ejecución %s", "commands.function.scheduled.no_functions": "No se pudo encontrar funciones de nombre %s", "commands.function.scheduled.single": "Función en ejecución %s", "commands.function.success.multiple": "Se ejecutó %s comando(s) de %s funciones", "commands.function.success.multiple.result": "Se ejecutaron %s funciones", "commands.function.success.single": "Se ejecutó %s comando(s) de la función «%s»", "commands.function.success.single.result": "La función «%2$s» devolvió el valor %1$s", "commands.gamemode.success.other": "Modo de juego de %s cambiado a %s", "commands.gamemode.success.self": "Tu modo de juego cambió a %s", "commands.gamerule.query": "La regla «%s» está establecida como «%s»", "commands.gamerule.set": "La regla «%s» cambió a «%s»", "commands.give.failed.toomanyitems": "No se puede dar más que %s de %s", "commands.give.success.multiple": "%3$s jugadores recibieron %1$s de %2$s", "commands.give.success.single": "%3$s recibió %1$s de %2$s", "commands.help.failed": "Comando desconocido o permiso insuficiente", "commands.item.block.set.success": "Se reemplazó un espacio en %s, %s, %s con %s", "commands.item.entity.set.success.multiple": "Se reemplazó un espacio en %s entidades, con %s", "commands.item.entity.set.success.single": "Se ha reemplazado un espacio en %s con %s", "commands.item.source.no_such_slot": "La fuente no tiene el espacio %s", "commands.item.source.not_a_container": "La posición fuente %s, %s, %s no es un contenedor", "commands.item.target.no_changed.known_item": "Ningún objetivo aceptó el ítem %s en el slot %s", "commands.item.target.no_changes": "Ningún objetivo aceptó el ítem en el slot %s", "commands.item.target.no_such_slot": "El objetivo no tiene el espacio %s", "commands.item.target.not_a_container": "La posición del objetivo %s, %s, %s no es un contenedor", "commands.jfr.dump.failed": "Error al copiar el registro del JFR: %s", "commands.jfr.start.failed": "Error al iniciar el perfilado JFR", "commands.jfr.started": "Perfilado JFR iniciado", "commands.jfr.stopped": "Se detuvo el perfilado JFR y se copió a %s", "commands.kick.owner.failed": "No se puede expulsar al dueño de un mundo LAN", "commands.kick.singleplayer.failed": "No se puede expulsar en un mundo para un jugador", "commands.kick.success": "Se expulsó a %s: %s", "commands.kill.success.multiple": "Se borraron %s entidades", "commands.kill.success.single": "Mataste a %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Hay %s de %s jugadores en línea: %s", "commands.locate.biome.not_found": "No se pudo encontrar un bioma «%s» a una distancia razonable", "commands.locate.biome.success": "La estructura de tipo %s más cercana está en %s. (A %s bloques de distancia)", "commands.locate.poi.not_found": "No se ha encontrado ningún punto de interés de tipo «%s» a una distancia razonable", "commands.locate.poi.success": "La estructura de tipo %s más cercana está en %s. (A %s bloques de distancia)", "commands.locate.structure.invalid": "No hay ninguna estructura con el tipo «%s»", "commands.locate.structure.not_found": "No se ha encontrado una estructura del tipo «%s» cerca", "commands.locate.structure.success": "La estructura de tipo %s más cercana está en %s. (A %s bloques de distancia)", "commands.message.display.incoming": "%s te susurró: %s", "commands.message.display.outgoing": "Le susurraste a %s: %s", "commands.op.failed": "Nada cambió: el jugador ya era administrador(a)", "commands.op.success": "%s se convirtió en administrador(a) del servidor", "commands.pardon.failed": "Nada cambió: el jugador no estaba bloqueado", "commands.pardon.success": "Se desbloqueó a %s", "commands.pardonip.failed": "Nada cambió: la dirección IP no estaba bloqueada", "commands.pardonip.invalid": "Dirección IP no válida", "commands.pardonip.success": "Se desbloqueó la dirección IP %s", "commands.particle.failed": "La partícula no era visible para nadie", "commands.particle.success": "Mostrando la partícula %s", "commands.perf.alreadyRunning": "El perfilador de rendimiento ya se ha iniciado", "commands.perf.notRunning": "El perfilador de rendimiento no se ha iniciado", "commands.perf.reportFailed": "Error al crear informe debug", "commands.perf.reportSaved": "Se creó un informe debug en %s", "commands.perf.started": "Se ha iniciado el perfilado de rendimiento de 10 segundos (usa «/perf stop» para detenerlo antes)", "commands.perf.stopped": "Se detuvo el análisis de rendimiento después de %s segundo(s) y %s tick(s) (%s tick(s) por segundo)", "commands.place.feature.failed": "Error al colocar característica", "commands.place.feature.invalid": "No hay ninguna característica con el tipo «%s»", "commands.place.feature.success": "Se ha colocado «%s» en %s, %s, %s", "commands.place.jigsaw.failed": "Error al generar bloque rompecabezas", "commands.place.jigsaw.invalid": "No hay ninguna plantilla del tipo «%s»", "commands.place.jigsaw.success": "Bloque rompecabezas generado en %s, %s, %s", "commands.place.structure.failed": "Error al colocar estructura", "commands.place.structure.invalid": "No hay ninguna estructura con el tipo «%s»", "commands.place.structure.success": "Estructura «%s» generada en %s, %s, %s", "commands.place.template.failed": "Error al colocar la plantilla", "commands.place.template.invalid": "No hay ninguna plantilla con el ID «%s»", "commands.place.template.success": "Plantilla «%s» cargada en %s, %s, %s", "commands.playsound.failed": "El sonido está demasiado lejos para ser escuchado", "commands.playsound.success.multiple": "Se reprodujo %s a %s jugadores", "commands.playsound.success.single": "Se reprodujo %s a %s", "commands.publish.alreadyPublished": "La partida multijugador ya está hospedada en el puerto %s", "commands.publish.failed": "No se pudo alojar la partida en LAN", "commands.publish.started": "Mundo en LAN alojado en el puerto %s", "commands.publish.success": "La partida multijugador está siendo hospedada en el puerto %s", "commands.random.error.range_too_large": "El rango del valor aleatorio debe ser, como máximo, 2147483646", "commands.random.error.range_too_small": "El rango del valor aleatorio debe ser, como mínimo, 2", "commands.random.reset.all.success": "%s secuencia(s) aleatoria(s) reiniciada(s)", "commands.random.reset.success": "Secuencia aleatoria %s reiniciada", "commands.random.roll": "%s sacó %s (entre %s y %s)", "commands.random.sample.success": "Valor aleatorio: %s", "commands.recipe.give.failed": "No se aprendieron nuevas recetas", "commands.recipe.give.success.multiple": "Se desbloquearon %s recetas para %s jugadores", "commands.recipe.give.success.single": "Se desbloquearon %s recetas para %s", "commands.recipe.take.failed": "No hay recetas olvidables", "commands.recipe.take.success.multiple": "Se borraron %s recetas a %s jugadores", "commands.recipe.take.success.single": "Se borraron %s recetas a %s", "commands.reload.failure": "<PERSON><PERSON>r al recargar, se van a mantener los datos antiguos", "commands.reload.success": "¡Recargando!", "commands.ride.already_riding": "%s ya está montado en %s", "commands.ride.dismount.success": "%s dej<PERSON> de <PERSON> a %s", "commands.ride.mount.failure.cant_ride_players": "No se puede montar en otro jugador", "commands.ride.mount.failure.generic": "%s no pudo montar en %s", "commands.ride.mount.failure.loop": "No se puede montar una entidad sobre sí misma ni a cualquiera de sus pasajeros", "commands.ride.mount.failure.wrong_dimension": "No se puede montar una entidad que está en otra dimensión", "commands.ride.mount.success": "%s comenzó a montar en %s", "commands.ride.not_riding": "%s no está montando ningún vehículo", "commands.rotate.success": "Se rotó a %s", "commands.save.alreadyOff": "El guardado ya estaba desactivado", "commands.save.alreadyOn": "El guardado ya estaba activado", "commands.save.disabled": "Autoguardado desactivado", "commands.save.enabled": "Autoguardado activado", "commands.save.failed": "Error al guardar la partida (¿tenés espacio en el disco duro?)", "commands.save.saving": "Guardando la partida... (<PERSON><PERSON>a, por favor)", "commands.save.success": "Partida guardada", "commands.schedule.cleared.failure": "No hay programaciones con ID %s", "commands.schedule.cleared.success": "Se eliminó %s tarea(s) con el id %s", "commands.schedule.created.function": "Se programó la función «%s» en %s tick(s) en el tiempo de juego %s", "commands.schedule.created.tag": "Se programó el tag «%s» en %s ticks (hora de juego: %s)", "commands.schedule.macro": "No sé pudo programar una macro", "commands.schedule.same_tick": "La ejecución no se puede programar en el tick actual", "commands.scoreboard.objectives.add.duplicate": "Ya existe un objetivo con este nombre", "commands.scoreboard.objectives.add.success": "Se creó un nuevo objetivo: %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Nada cambió: este espacio de muestra ya estaba vacío", "commands.scoreboard.objectives.display.alreadySet": "Nada cambió: este espacio de muestra ya estaba mostrando este objetivo", "commands.scoreboard.objectives.display.cleared": "Se quitaron los objetivos que se mostraban en la ranura %s", "commands.scoreboard.objectives.display.set": "Se estableció que la ranura %s muestre el objetivo %s", "commands.scoreboard.objectives.list.empty": "No hay objetivos", "commands.scoreboard.objectives.list.success": "Hay %s objetivo(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Se desactivó la actualización automática del nombre del objetivo %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Se activó la actualización automática del nombre del objetivo %s", "commands.scoreboard.objectives.modify.displayname": "Se cambió el nombre a mostrar del objetivo %s a %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Se eliminó el formato numérico predefinido del objetivo %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Se cambió el formato numérico predefinido del objetivo %s", "commands.scoreboard.objectives.modify.rendertype": "Se cambió el tipo de renderizado del objetivo %s", "commands.scoreboard.objectives.remove.success": "Se borró un objetivo: %s", "commands.scoreboard.players.add.success.multiple": "Se agregaron %s puntos al objetivo %s para %s entidades", "commands.scoreboard.players.add.success.single": "Se agregaron %s puntos al objetivo %s para %s (ahora tiene %s puntos)", "commands.scoreboard.players.display.name.clear.success.multiple": "Se eliminó el nombre de %s entidades en %s", "commands.scoreboard.players.display.name.clear.success.single": "Se eliminó el nombre de %s en %s", "commands.scoreboard.players.display.name.set.success.multiple": "Se cambió el nombre a %s a %s entidades en %s", "commands.scoreboard.players.display.name.set.success.single": "Se cambió el nombre de %2$s a %1$s en %3$s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Se eliminó el formato numérico de %s entidades en %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Se eliminó el formato numérico de %s en %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Se cambió el formato numérico de %s entidades en %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Se cambió el formato numérico de %s en %s", "commands.scoreboard.players.enable.failed": "Nada cambió: el trigger ya estaba activado", "commands.scoreboard.players.enable.invalid": "La función «enable» sólo sirve con objetivos de tipo «trigger»", "commands.scoreboard.players.enable.success.multiple": "Se activó el trigger %s para %s entidades", "commands.scoreboard.players.enable.success.single": "Trigger %s activado para %s", "commands.scoreboard.players.get.null": "No se pudo obtener el puntaje del objetivo %s para %s, no hay registro", "commands.scoreboard.players.get.success": "%s tiene un puntaje de %s en %s", "commands.scoreboard.players.list.empty": "No hay entidades registradas", "commands.scoreboard.players.list.entity.empty": "%s no tiene puntajes para mostrar", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s tiene %s punto(s):", "commands.scoreboard.players.list.success": "Hay %s entidad(es) registrada(s): %s", "commands.scoreboard.players.operation.success.multiple": "Se actualizó el puntaje del objetivo %s para %s entidades", "commands.scoreboard.players.operation.success.single": "Se estableció el puntaje del objetivo %s para %s a %s", "commands.scoreboard.players.remove.success.multiple": "Se borró el puntaje %s del objetivo %s para %s entidades", "commands.scoreboard.players.remove.success.single": "Se redujo el puntaje en %s puntos del objetivo %s a %s (ahora tiene %s)", "commands.scoreboard.players.reset.all.multiple": "Se reiniciaron todos los puntajes de %s entidades", "commands.scoreboard.players.reset.all.single": "Se reiniciaron todas las puntuaciones de %s", "commands.scoreboard.players.reset.specific.multiple": "Se reinició el puntaje de %s para %s entidades", "commands.scoreboard.players.reset.specific.single": "Se reinició el puntaje del objetivo %s para %s", "commands.scoreboard.players.set.success.multiple": "Se cambió el puntaje del objetivo %s para %s entidades a %s", "commands.scoreboard.players.set.success.single": "Se estableció el puntaje del objetivo %s de %s a %s", "commands.seed.success": "Semilla: %s", "commands.setblock.failed": "No se pudo colocar el bloque", "commands.setblock.success": "Se cambió el bloque en %s, %s, %s", "commands.setidletimeout.success": "Se cambió el tiempo de espera por inactividad a %s minuto(s)", "commands.setidletimeout.success.disabled": "Se desactivó el tiempo máximo de inactividad de jugadores", "commands.setworldspawn.failure.not_overworld": "El punto de reaparición del mundo solo puede estar en la superficie", "commands.setworldspawn.success": "Se cambió el punto de aparición del mundo a %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Se cambió el punto de aparición de %6$s jugadores en la dimensión %5$s a %1$s, %2$s, %3$s [%4$s]", "commands.spawnpoint.success.single": "Se cambió el punto de aparición de %6$s en la dimensión %5$s a %1$s, %2$s, %3$s [%4$s]", "commands.spectate.not_spectator": "%s no está en modo espectador", "commands.spectate.self": "No podés observarte a vos mismo", "commands.spectate.success.started": "El jugador observa la entidad %s", "commands.spectate.success.stopped": "El jugador dejó de observar una entidad", "commands.spreadplayers.failed.entities": "No se pudo repartir %s entidad(es) entre %s y %s (demasiadas entidades para ese espacio, probá repartiéndolas al menos en %s)", "commands.spreadplayers.failed.invalid.height": "maxHeight %s no válido, se requiere un valor mayor al mínimo %s del mundo", "commands.spreadplayers.failed.teams": "No se pudo repartir %s equipo(s) entre %s y %s (demasiadas entidades para ese espacio, probá repartiéndolas al menos en %s)", "commands.spreadplayers.success.entities": "Se repartieron %s jugador(es) alrededor %s, %s con una distancia media de %s bloques", "commands.spreadplayers.success.teams": "Se repartieron %s equipo(s) alrededor de %s, %s con una distancia media de %s bloques", "commands.stop.stopping": "Deteniendo el servidor", "commands.stopsound.success.source.any": "Se detuvieron todos los sonidos «%s»", "commands.stopsound.success.source.sound": "Se detuvo el sonido «%s» de la fuente «%s»", "commands.stopsound.success.sourceless.any": "Se detuvieron todos los sonidos", "commands.stopsound.success.sourceless.sound": "Se detuvo el sonido «%s»", "commands.summon.failed": "No se pudo generar la entidad", "commands.summon.failed.uuid": "No se pudo generar la entidad porque hay UUIDs duplicados", "commands.summon.invalidPosition": "Posición no válida para invocar", "commands.summon.success": "Se generó la entidad «%s»", "commands.tag.add.failed": "El objetivo ya tenía el tag o tiene demasiados", "commands.tag.add.success.multiple": "Se agregó la etiqueta «%s» a %s entidades", "commands.tag.add.success.single": "Se agregó la etiqueta «%s» a %s", "commands.tag.list.multiple.empty": "Las %s entidades no tienen etiquetas", "commands.tag.list.multiple.success": "Las %s entidades tienen un total de %s etiquetas: %s", "commands.tag.list.single.empty": "%s no tiene etiquetas", "commands.tag.list.single.success": "%s tiene %s etiquetas: %s", "commands.tag.remove.failed": "El objetivo no tiene este tag", "commands.tag.remove.success.multiple": "Se borró la etiqueta «%s» de %s entidades", "commands.tag.remove.success.single": "Se borró la etiqueta «%s» de %s", "commands.team.add.duplicate": "Ya existe un equipo con este nombre", "commands.team.add.success": "Se creó el equipo %s", "commands.team.empty.success": "Se expulsó %s miembro(s) del equipo %s", "commands.team.empty.unchanged": "Nada cambió: el equipo ya estaba vacío", "commands.team.join.success.multiple": "Se agregaron %s participantes al equipo %s", "commands.team.join.success.single": "Se agregó a %s al equipo %s", "commands.team.leave.success.multiple": "Se sacaron %s participantes de sus equipos", "commands.team.leave.success.single": "Se sacó a %s de su equipo", "commands.team.list.members.empty": "No hay participantes en el equipo %s", "commands.team.list.members.success": "El equipo %s tiene %s miembro(s): %s", "commands.team.list.teams.empty": "No hay equipos", "commands.team.list.teams.success": "Hay %s equipo(s): %s", "commands.team.option.collisionRule.success": "Se cambió la regla de colisión del equipo %s a «%s»", "commands.team.option.collisionRule.unchanged": "Nada cambió: la regla de colisión ya tenía este valor", "commands.team.option.color.success": "Se actualizó el color de equipo %s a %s", "commands.team.option.color.unchanged": "Nada cambió: el equipo ya tenía este color", "commands.team.option.deathMessageVisibility.success": "Se cambió la visibilidad de las alertas de muertes para el equipo %s a «%s»", "commands.team.option.deathMessageVisibility.unchanged": "Nada cambió: la visibilidad de avisos de muertes ya tenía este valor", "commands.team.option.friendlyfire.alreadyDisabled": "Nada cambió: el fuego amigo ya estaba desactivado en el equipo", "commands.team.option.friendlyfire.alreadyEnabled": "Nada cambió: el fuego amigo ya estaba activado en el equipo", "commands.team.option.friendlyfire.disabled": "Se desactivó el fuego amigo en el equipo %s", "commands.team.option.friendlyfire.enabled": "Se activó el fuego amigo en el equipo %s", "commands.team.option.name.success": "Se cambió el nombre del equipo %s", "commands.team.option.name.unchanged": "Nada cambió: el equipo ya tenía ese nombre", "commands.team.option.nametagVisibility.success": "Se cambió la visibilidad de los nombres de jugadores para el equipo %s a «%s»", "commands.team.option.nametagVisibility.unchanged": "Nada cambió: la visibilidad de nombres ya tenía este valor", "commands.team.option.prefix.success": "Se cambió el prefijo del equipo a %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Nada cambió: los miembros del equipo ya no podían ver a sus compañeros invisibles", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Nada cambió: los miembros ya podían ver a sus compañeros invisibles", "commands.team.option.seeFriendlyInvisibles.disabled": "Ahora los miembros del equipo %s no pueden ver a sus compañeros invisibles", "commands.team.option.seeFriendlyInvisibles.enabled": "Ahora los miembros del equipo %s pueden ver a sus compañeros invisibles", "commands.team.option.suffix.success": "Se cambió el sufijo del equipo a %s", "commands.team.remove.success": "Equipo %s borrado", "commands.teammsg.failed.noteam": "Para enviar un mensaje a tu equipo tenés que pertenecer a uno", "commands.teleport.invalidPosition": "Posición no válida para teletransportarse", "commands.teleport.success.entity.multiple": "Se teletransportaron %s entidades a %s", "commands.teleport.success.entity.single": "%s teletransportado hacia %s", "commands.teleport.success.location.multiple": "Se teletransportaron %s entidades a %s, %s, %s", "commands.teleport.success.location.single": "%s teletransportado a %s, %s, %s", "commands.test.batch.starting": "Iniciando entorno %s del lote %s", "commands.test.clear.error.no_tests": "No se pudo encontrar ninguna prueba para eliminar", "commands.test.clear.success": "Se eliminó %s estructura(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Hacé click para copiar al portapapeles", "commands.test.create.success": "Configuración creada para la prueba %s", "commands.test.error.no_test_containing_pos": "No se pudo encontrar una instancia de prueba que contenga %s, %s, %s", "commands.test.error.no_test_instances": "No se encontraron instancias de prueba", "commands.test.error.non_existant_test": "No se pudo encontrar la prueba %s", "commands.test.error.structure_not_found": "No se pudo encontrar la estructura de prueba %s", "commands.test.error.test_instance_not_found": "No se pudo encontrar la entidad de bloque de instancia de prueba", "commands.test.error.test_instance_not_found.position": "No se pudo encontrar la entidad de bloque de instancia de prueba en %s, %s, %s", "commands.test.error.too_large": "El tamaño de la estructura tiene que ser menor a %s en cada eje", "commands.test.locate.done": "Búsqueda terminada, se encontró %s estructura(s)", "commands.test.locate.found": "Se encontró una estructura en: %s (distancia: %s)", "commands.test.locate.started": "Se inició la búsqueda de estructuras de prueba, esto puede tardar un momento...", "commands.test.no_tests": "No hay pruebas para ejecutar", "commands.test.relative_position": "Posición relativa a %s: %s", "commands.test.reset.error.no_tests": "No se pudo encontrar ninguna prueba para reiniciar", "commands.test.reset.success": "Se reinició %s estructura(s)", "commands.test.run.no_tests": "No se encontraron pruebas", "commands.test.run.running": "Realizando %s prueba(s)...", "commands.test.summary": "¡Se completó la prueba del juego! Se realizó %s prueba(s)", "commands.test.summary.all_required_passed": "Se superaron todas las pruebas requeridas :)", "commands.test.summary.failed": "No se superó %s de las pruebas requeridas :(", "commands.test.summary.optional_failed": "No se superó %s de las pruebas opcionales :(", "commands.tick.query.percentiles": "Percentiles: P50: %s ms, P95: %s ms, P99: %s ms; muestra: %s", "commands.tick.query.rate.running": "Tasa de ticks: %s por segundo.\nMedia de tiempo por tick: %% ms (destino: %% ms)", "commands.tick.query.rate.sprinting": "Tasa de ticks del objetivo: %s por segundo (ignorado, solo como referencia).\nPromedio de tiempo por tick: %s ms", "commands.tick.rate.success": "Se cambió la tasa de ticks del objetivo a %s por segundo", "commands.tick.sprint.report": "Alta velocidad con %s ticks por segundo o %s ms por tick", "commands.tick.sprint.stop.fail": "No hay ticks a alta velocidad en curso", "commands.tick.sprint.stop.success": "Aceleración de ticks interrumpida", "commands.tick.status.frozen": "El juego está congelado", "commands.tick.status.lagging": "El juego se está ejecutando, pero no puede mantenerse a la tasa de ticks objetivo", "commands.tick.status.running": "El juego funciona con normalidad", "commands.tick.status.sprinting": "El juego está a alta velocidad", "commands.tick.step.fail": "Error al volver a la velocidad normal; el juego debe estar congelado primero.", "commands.tick.step.stop.fail": "No hay ticks a velocidad normal en curso", "commands.tick.step.stop.success": "Se interrumpió un tick a velocidad normal", "commands.tick.step.success": "Regulando %s tick(s)", "commands.time.query": "Hora: %s ticks", "commands.time.set": "Tiempo ajustado a %s ticks", "commands.title.cleared.multiple": "Se borraron los títulos de %s jugadores", "commands.title.cleared.single": "Se borraron los títulos para %s", "commands.title.reset.multiple": "Se reiniciaron las opciones de título para %s jugadores", "commands.title.reset.single": "Se reiniciaron las opciones de título para %s", "commands.title.show.actionbar.multiple": "Mostrando un nuevo título en la barra de acción de %s jugadores", "commands.title.show.actionbar.single": "Mostrando nuevo título en la barra de acción de %s", "commands.title.show.subtitle.multiple": "Mostrando el nuevo subtítulo a %s jugadores", "commands.title.show.subtitle.single": "Mostrando el nuevo subtítulo para %s", "commands.title.show.title.multiple": "Mostrando el nuevo título a %s jugadores", "commands.title.show.title.single": "Mostrando el nuevo título para %s", "commands.title.times.multiple": "Se cambió el tiempo de visualización del título a %s jugadores", "commands.title.times.single": "Se cambió el tiempo de visualización del título %s", "commands.transfer.error.no_players": "Especificá al menos un jugador para transferir", "commands.transfer.success.multiple": "Transfiriendo %s jugadores a %s:%s", "commands.transfer.success.single": "Transfiriendo a %s a %s:%s", "commands.trigger.add.success": "Se activó el trigger «%s» (se agregó %s al valor)", "commands.trigger.failed.invalid": "<PERSON><PERSON>lo puedes activar objetivos de tipo «trigger»", "commands.trigger.failed.unprimed": "Aún no puedes activar este objetivo trigger", "commands.trigger.set.success": "Se activó el trigger «%s» (se estableció el valor en %s)", "commands.trigger.simple.success": "Se activó el trigger «%s»", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No hay puntos de ruta en %s", "commands.waypoint.list.success": "Hay %s punto(s) de ruta en %s: %s", "commands.waypoint.modify.color": "El color del punto de ruta ahora es %s", "commands.waypoint.modify.color.reset": "Se restableció el color del punto de ruta", "commands.waypoint.modify.style": "Se cambió el estilo del punto de referencia", "commands.weather.set.clear": "Cambiando el tiempo atmosférico a despejado...", "commands.weather.set.rain": "Cambiando el tiempo atmosférico a lluvia...", "commands.weather.set.thunder": "Cambiando el tiempo atmosférico a tormenta...", "commands.whitelist.add.failed": "El jugador ya estaba en la lista blanca", "commands.whitelist.add.success": "%s fue añadido a la lista blanca", "commands.whitelist.alreadyOff": "La lista blanca ya estaba desactivada", "commands.whitelist.alreadyOn": "La lista blanca ya estaba activada", "commands.whitelist.disabled": "Se desactivó la lista blanca", "commands.whitelist.enabled": "Se activó la lista blanca", "commands.whitelist.list": "Hay %s jugador(es) en la lista blanca: %s", "commands.whitelist.none": "La lista blanca está vacía", "commands.whitelist.reloaded": "Lista blanca recargada", "commands.whitelist.remove.failed": "El jugador no estaba en la lista blanca", "commands.whitelist.remove.success": "%s fue borrado de la lista blanca", "commands.worldborder.center.failed": "Nada cambió: el centro del borde del mundo ya estaba en esa ubicación", "commands.worldborder.center.success": "Se cambió el centro del borde del mundo en las coordenadas %s, %s", "commands.worldborder.damage.amount.failed": "Nada cambió: el borde del mundo ya inflige esta cantidad de daño", "commands.worldborder.damage.amount.success": "Se estableció la cantidad de daño fuera del borde del mundo a %s por bloque cada segundo", "commands.worldborder.damage.buffer.failed": "Nada cambió: la zona segura fuera del mundo ya estaba a esta distancia", "commands.worldborder.damage.buffer.success": "Se estableció la zona segura fuera del borde el mundo a %s bloque(s)", "commands.worldborder.get": "El borde del mundo tiene %s bloque(s) de largo", "commands.worldborder.set.failed.big": "El borde del mundo no puede tener una extensión mayor a %s bloques", "commands.worldborder.set.failed.far": "El borde del mundo no puede tener una extensión mayor a %s bloques", "commands.worldborder.set.failed.nochange": "Nada cambió: el borde del mundo ya tenía este tamaño", "commands.worldborder.set.failed.small": "El borde del mundo no puede tener una extensión inferior a 1 bloque", "commands.worldborder.set.grow": "Se está aumentando la extensión del borde del mundo en %s bloques durante %s segundos", "commands.worldborder.set.immediate": "Se estableció el borde del mundo a %s bloque(s) de distancia", "commands.worldborder.set.shrink": "Reduciendo el borde del mundo en %s bloque(s) durante %s segundo(s)", "commands.worldborder.warning.distance.failed": "Nada cambió: el aviso del borde del mundo ya tenía esta distancia", "commands.worldborder.warning.distance.success": "Se estableció la distancia del aviso del borde del mundo a %s bloque(s)", "commands.worldborder.warning.time.failed": "Nada cambió: el aviso del borde del mundo ya duraba este tiempo", "commands.worldborder.warning.time.success": "Se estableció el tiempo de aviso del borde del mundo en %s segundo(s)", "compliance.playtime.greaterThan24Hours": "Llevás jugando más de 24 horas", "compliance.playtime.hours": "Llevás jugando %s hora(s)", "compliance.playtime.message": "Jugar excesivamente podría interferir con tu vida diaria", "connect.aborted": "Conexión cancelada", "connect.authorizing": "Iniciando se<PERSON>...", "connect.connecting": "Conectándose al servidor...", "connect.encrypting": "<PERSON><PERSON><PERSON>o cone<PERSON>...", "connect.failed": "Error al conectar con el servidor", "connect.failed.transfer": "Error de conexión al transferir al servidor", "connect.joining": "Entrando al mundo...", "connect.negotiating": "Conectando...", "connect.reconfiging": "Reconfigurando...", "connect.reconfiguring": "Reconfigurando...", "connect.transferring": "Transfiriendo al nuevo servidor...", "container.barrel": "Barril", "container.beacon": "Faro", "container.beehive.bees": "Abejas: %s/%s", "container.beehive.honey": "Miel:%s/%s", "container.blast_furnace": "Alto horno", "container.brewing": "Destiladora", "container.cartography_table": "Mesa de cartografía", "container.chest": "Baúl", "container.chestDouble": "Baúl grande", "container.crafter": "Fabricador", "container.crafting": "Fabricación", "container.creative": "Selección de objetos", "container.dispenser": "Dispensador", "container.dropper": "Soltador", "container.enchant": "Encantar", "container.enchant.clue": "¿ %s . . . ?", "container.enchant.lapis.many": "%s de lapislázuli", "container.enchant.lapis.one": "1 de lapislázuli", "container.enchant.level.many": "%s niveles de experiencia", "container.enchant.level.one": "1 nivel de experiencia", "container.enchant.level.requirement": "Nivel necesario: %s", "container.enderchest": "<PERSON><PERSON>", "container.furnace": "<PERSON><PERSON>", "container.grindstone_title": "Reparar y desencantar", "container.hopper": "<PERSON><PERSON><PERSON>", "container.inventory": "Inventario", "container.isLocked": "¡%s bloqueado/a!", "container.lectern": "Atril", "container.loom": "Máquina de telar", "container.repair": "Mejorar y renombrar", "container.repair.cost": "Costo: %1$s", "container.repair.expensive": "¡Es demasiado caro!", "container.shulkerBox": "<PERSON><PERSON> de <PERSON>ker", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "y %s más...", "container.shulkerBox.unknownContents": "¿¿¿???", "container.smoker": "<PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "No se puede abrir. El botín no se ha generado aún.", "container.stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.upgrade": "<PERSON><PERSON>rar equipamiento", "container.upgrade.error_tooltip": "Este objeto no puede mejorarse de esta manera", "container.upgrade.missing_template_tooltip": "Colocá un molde de herrería", "controls.keybinds": "Atajos...", "controls.keybinds.duplicateKeybinds": "Esta tecla también se usa para:\n%s", "controls.keybinds.title": "<PERSON><PERSON><PERSON>", "controls.reset": "Reiniciar", "controls.resetAll": "Reiniciar teclas", "controls.title": "Controles", "createWorld.customize.buffet.biome": "Elegí el bioma", "createWorld.customize.buffet.title": "Personalizar mundo de bioma único", "createWorld.customize.flat.height": "Altura", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "(Fondo)  %s", "createWorld.customize.flat.layer.top": "(Superficie)  %s", "createWorld.customize.flat.removeLayer": "Eliminar capa", "createWorld.customize.flat.tile": "Material de la capa", "createWorld.customize.flat.title": "Personalización del mundo plano", "createWorld.customize.presets": "Plantillas", "createWorld.customize.presets.list": "Como otra opción, ¡acá tenés algunas que hicimos!", "createWorld.customize.presets.select": "Usar plantilla", "createWorld.customize.presets.share": "¿Querés compartir tu plantilla con alguien? ¡Usá el cuadro de abajo!", "createWorld.customize.presets.title": "Seleccionar una plantilla", "createWorld.preparing": "Preparando la generación del mundo...", "createWorld.tab.game.title": "Ju<PERSON>", "createWorld.tab.more.title": "Más", "createWorld.tab.world.title": "Mundo", "credits_and_attribution.button.attribution": "Atribuciones", "credits_and_attribution.button.credits": "C<PERSON>dit<PERSON>", "credits_and_attribution.button.licenses": "Licencias", "credits_and_attribution.screen.title": "Créditos y atribuciones", "dataPack.bundle.description": "Activa la bolsa como objeto experimental", "dataPack.bundle.name": "Bolsas", "dataPack.locator_bar.description": "Mostrar la dirección de otros jugadores en multijugador", "dataPack.locator_bar.name": "Barra localizadora", "dataPack.minecart_improvements.description": "Mejoras en el movimiento de los carritos", "dataPack.minecart_improvements.name": "Mejoras del carrito", "dataPack.redstone_experiments.description": "Cambios experimentales de redstone", "dataPack.redstone_experiments.name": "Experimentos de redstone", "dataPack.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> de <PERSON>", "dataPack.trade_rebalance.description": "Actualización de los intercambios con aldeanos", "dataPack.trade_rebalance.name": "Balanceo a los intercambios con aldeanos", "dataPack.update_1_20.description": "Nuevas características y contenido para Minecraft 1.20", "dataPack.update_1_20.name": "Actualización 1.20", "dataPack.update_1_21.description": "Nuevas características y contenido para Minecraft 1.21", "dataPack.update_1_21.name": "Actualización 1.21", "dataPack.validation.back": "Volver", "dataPack.validation.failed": "¡Error al validar el paquete de datos!", "dataPack.validation.reset": "Restablecer por defecto", "dataPack.validation.working": "Validando paquetes de datos seleccionados...", "dataPack.vanilla.description": "Los datos por defecto de Minecraft", "dataPack.vanilla.name": "Por defecto", "dataPack.winter_drop.description": "Nuevo contenido de la actualización de invierno", "dataPack.winter_drop.name": "Actualización de invierno", "datapackFailure.safeMode": "<PERSON><PERSON> se<PERSON>", "datapackFailure.safeMode.failed.description": "Este mundo tiene datos de guardado inválidos o corruptos.", "datapackFailure.safeMode.failed.title": "Error al cargar el mundo en modo seguro.", "datapackFailure.title": "No se pudo cargar el mundo porque hay errores en los paquetes de datos seleccionados.\nPodes intentar cargarlo únicamente con el paquete de datos predeterminado («modo seguro») o volver al menú principal y buscar una solución manualmente.", "death.attack.anvil": "%1$s fue aplastado por un yunque", "death.attack.anvil.player": "%1$s fue aplastado por un yunque mientras peleaba contra %2$s", "death.attack.arrow": "%1$s fue disparado por %2$s", "death.attack.arrow.item": "%1$s fue disparado por %2$s usando %3$s", "death.attack.badRespawnPoint.link": "un secreto del juego", "death.attack.badRespawnPoint.message": "%1$s fue víctima de %2$s", "death.attack.cactus": "%1$s fue pinchado hasta la muerte", "death.attack.cactus.player": "%1$s se pinchó con un cactus tratando de escapar de %2$s", "death.attack.cramming": "%1$s fue aplastado con demasiada fuerza", "death.attack.cramming.player": "%1$s fue aplastado/a por %2$s", "death.attack.dragonBreath": "%1$s fue calcinado en aliento de dragón", "death.attack.dragonBreath.player": "%1$s fue calcinado en aliento del dragón por culpa de %2$s", "death.attack.drown": "%1$s se ahogó", "death.attack.drown.player": "%1$s se ahogó tratando de escapar de %2$s", "death.attack.dryout": "%1$s ha muerto por deshidratación", "death.attack.dryout.player": "%1$s murió por deshidratación mientras intentaba huir de %2$s", "death.attack.even_more_magic": "%1$s murió por mucho más que arte de magia", "death.attack.explosion": "%1$s explotó", "death.attack.explosion.player": "%1$s fue explotado por %2$s", "death.attack.explosion.player.item": "%1$s fue reventado/a por %2$s y su %3$s", "death.attack.fall": "%1$s cayó demasiado fuerte", "death.attack.fall.player": "%1$s chocó contra el piso tratando de escapar de %2$s", "death.attack.fallingBlock": "%1$s fue aplastado por un bloque", "death.attack.fallingBlock.player": "%1$s fue aplastado por un bloque mientras peleaba contra %2$s", "death.attack.fallingStalactite": "%1$s fue atravesado por una estalactita", "death.attack.fallingStalactite.player": "%1$s fue atravesado por una estalactita mientras peleaba contra %2$s", "death.attack.fireball": "%1$s fue alcanzado por una bola de fuego de %2$s", "death.attack.fireball.item": "%1$s fue quemado por %2$s con una bola de fuego usando %3$s", "death.attack.fireworks": "%1$s se convirtió en un fuego artificial", "death.attack.fireworks.item": "%1$s explotó por un cohete lanzado por %2$s con %3$s", "death.attack.fireworks.player": "%1$s se convirtió en un fuego artificial mientras peleaba contra %2$s", "death.attack.flyIntoWall": "%1$s experimentó la energía cinética", "death.attack.flyIntoWall.player": "%1$s experimentó la energía cinética tratando de escapar de %2$s", "death.attack.freeze": "%1$s se congeló hasta la muerte", "death.attack.freeze.player": "%1$s fue congelado hasta la muerte por %2$s", "death.attack.generic": "%1$s murió", "death.attack.generic.player": "%1$s murió por culpa de %2$s", "death.attack.genericKill": "%1$s fue asesinado", "death.attack.genericKill.player": "%1$s fue asesinado mientras peleaba contra %2$s", "death.attack.hotFloor": "%1$s descubrió que el piso era lava", "death.attack.hotFloor.player": "%1$s caminó sobre una zona peligrosa por culpa de %2$s", "death.attack.inFire": "%1$s se prendió fuego", "death.attack.inFire.player": "%1$s caminó hacia el fuego luchando contra %2$s", "death.attack.inWall": "%1$s se sofocó en una pared", "death.attack.inWall.player": "%1$s se asfixió en una pared mientras peleaba contra %2$s", "death.attack.indirectMagic": "%1$s fue asesinado por %2$s usando magia", "death.attack.indirectMagic.item": "%1$s fue asesinado por %2$s usando %3$s", "death.attack.lava": "%1$s trató de nadar en lava", "death.attack.lava.player": "%1$s trató de nadar en la lava para escapar de %2$s", "death.attack.lightningBolt": "%1$s fue alcanzado por un rayo", "death.attack.lightningBolt.player": "%1$s recibió la ira de Thor mientras peleaba contra %2$s", "death.attack.mace_smash": "%1$s fue aplastado por %2$s", "death.attack.mace_smash.item": "%1$s fue aplastado por %2$s con %3$s", "death.attack.magic": "%1$s murió por arte de magia", "death.attack.magic.player": "%1$s murió por arte de magia tratando de escapar de %2$s", "death.attack.message_too_long": "El mensaje era demasiado largo para ser enviado. ¡Lo sentimos! Versión abreviada: %s", "death.attack.mob": "%1$s fue asesinado/a por %2$s", "death.attack.mob.item": "%1$s fue víctima de %2$ss usando %3$s", "death.attack.onFire": "%1$s se quemó hasta la muerte", "death.attack.onFire.item": "%1$s fue reducido a cenizas mientras luchaba contra %2$s empuñando %3$s", "death.attack.onFire.player": "%1$s fue reducido a cenizas luchando contra %2$s", "death.attack.outOfWorld": "%1$s se cayó al vacío", "death.attack.outOfWorld.player": "%1$s no volverá a vivir en el mismo mundo que %2$s", "death.attack.outsideBorder": "%1$s abandonó los confines del mundo", "death.attack.outsideBorder.player": "%1$s abandonó los confines de este mundo mientras peleaba contra %2$s", "death.attack.player": "%1$s fue asesinado/a por %2$s", "death.attack.player.item": "%1$s fue víctima de %2$s usando %3$s", "death.attack.sonic_boom": "%1$s fue borrado/a por un chillido supersónico", "death.attack.sonic_boom.item": "%1$s fue borrado por un chillido supersónico tratando de escapar de %2$s empuñando %3$s", "death.attack.sonic_boom.player": "%1$s fue borrado por un chillido supersónico tratando de escapar de %2$s", "death.attack.stalagmite": "%1$s fue empalado por una estalagmita", "death.attack.stalagmite.player": "%1$s fue empalado por una estalagmita mientras luchaba contra %2$s", "death.attack.starve": "%1$s se murió de hambre", "death.attack.starve.player": "%1$s murió de hambre mientras peleaba contra %2$s", "death.attack.sting": "%1$s fue picado hasta la muerte", "death.attack.sting.item": "%1$s fue picado hasta la muerte por %2$s usando %3$s", "death.attack.sting.player": "%1$s fue picado hasta la muerte por %2$s", "death.attack.sweetBerryBush": "%1$s se pinchó con un arbusto de bayas dulces", "death.attack.sweetBerryBush.player": "%1$s se pinchó con un arbusto de bayas dulces tratando de escapar de %2$s", "death.attack.thorns": "%1$s murió tratando de golpear a %2$s", "death.attack.thorns.item": "%1$s fue asesinado por %3$s mientras intentaba golpear a %2$s", "death.attack.thrown": "%1$s fue golpeado por %2$s", "death.attack.thrown.item": "%1$s fue golpeado por %2$s usando %3$s", "death.attack.trident": "%1$s fue empalado/a por %2$s", "death.attack.trident.item": "%1$s fue empalado/a por %2$s y su %3$s", "death.attack.wither": "%1$s murió tras descomponerse", "death.attack.wither.player": "%1$s se descompuso mientras peleaba contra %2$s", "death.attack.witherSkull": "%1$s recibió un proyectil del Wither de %2$s", "death.attack.witherSkull.item": "%1$s fue golpeado/a por un cráneo de %2$s usando %3$s", "death.fell.accident.generic": "%1$s cayó desde muy alto", "death.fell.accident.ladder": "%1$s se cayó de una escalera", "death.fell.accident.other_climbable": "%1$s se cayó mientras trepaba", "death.fell.accident.scaffolding": "%1$s se cayó de un andamio", "death.fell.accident.twisting_vines": "%1$s se cayó de unas enredaderas retorcidas", "death.fell.accident.vines": "%1$s se cayó de unas enredaderas", "death.fell.accident.weeping_vines": "%1$s se cayó de unas enredaderas lloronas", "death.fell.assist": "%1$s fue condenado a caer desde muy alto por %2$s", "death.fell.assist.item": "%1$s fue condenado a caer desde muy alto por %2$s usando %3$s", "death.fell.finish": "%1$s cayó desde muy alto y fue asesinado/a por %2$s", "death.fell.finish.item": "%1$s cayó desde muy alto y %2$s acabó con él usando %3$s", "death.fell.killer": "%1$s fue condenado a caer desde muy alto", "deathScreen.quit.confirm": "¿Seguro que querés salir?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON>", "deathScreen.score": "Puntuación", "deathScreen.score.value": "Puntuación: %s", "deathScreen.spectate": "Observar mundo", "deathScreen.title": "¡Has muerto!", "deathScreen.title.hardcore": "Game Over", "deathScreen.titleScreen": "<PERSON><PERSON> principal", "debug.advanced_tooltips.help": "F3 + H = Descripción de ítems detallada", "debug.advanced_tooltips.off": "Descripción de ítems detallada: no", "debug.advanced_tooltips.on": "Descripción de ítems detallada: sí", "debug.chunk_boundaries.help": "F3 + G = Mostrar bordes de chunks", "debug.chunk_boundaries.off": "Bordes de chunks: no", "debug.chunk_boundaries.on": "Bordes de chunks: sí", "debug.clear_chat.help": "F3 + D = Limpiar chat", "debug.copy_location.help": "F3 + C = Copiar tu ubicación a un /tp; mantené para cerrar el juego", "debug.copy_location.message": "Ubicación copiada en el portapapeles", "debug.crash.message": "Estás apretando F3 + C. El juego se va a cerrar si seguís.", "debug.crash.warning": "El juego va a dejar de funcionar en %s...", "debug.creative_spectator.error": "No tenés permisos para cambiar el modo de juego", "debug.creative_spectator.help": "F3 + N = Cambiar al modo de juego anterior <-> espectador", "debug.dump_dynamic_textures": "Texturas dinámicas guardadas en %s", "debug.dump_dynamic_textures.help": "F3 + S = Exportar texturas dinámicas", "debug.gamemodes.error": "No tenés permiso para abrir el menú de modos de juego", "debug.gamemodes.help": "F3 + F4 = <PERSON>brir menú de modos de juego", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s siguiente", "debug.help.help": "F3 + Q = Mostrar esta lista", "debug.help.message": "Teclas:", "debug.inspect.client.block": "Se copiaron los datos del bloque al portapapeles (cliente)", "debug.inspect.client.entity": "Se copiaron los datos de la entidad al portapapeles (cliente)", "debug.inspect.help": "F3 + I = Copiar datos de entidad o bloque al portapapeles", "debug.inspect.server.block": "Se copiaron los datos del bloque al portapapeles (servidor)", "debug.inspect.server.entity": "Se copiaron los datos de la entidad al portapapeles (servidor)", "debug.pause.help": "F3 + Esc = Pausar juego sin el menú de pausa (si es posible)", "debug.pause_focus.help": "F3 + P = Pausar juego al cambiar de ventana", "debug.pause_focus.off": "Pausar juego al cambiar de ventana: no", "debug.pause_focus.on": "Pausar juego al cambiar de ventana: sí", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Empezar/terminar perfilado", "debug.profiling.start": "Iniciado perfilado durante %s segundos. Usa F3 + L para detenerlo antes", "debug.profiling.stop": "Perfilado terminado. Resultados guardados en %s", "debug.reload_chunks.help": "F3 + A = Recargar chunks", "debug.reload_chunks.message": "Recargando todos los chunks", "debug.reload_resourcepacks.help": "F3 + T = <PERSON><PERSON><PERSON> paquetes de recursos", "debug.reload_resourcepacks.message": "Paquetes de recursos recargados", "debug.show_hitboxes.help": "F3 + B = Mostrar hitbox (cajas de colisión)", "debug.show_hitboxes.off": "Mostrar hitbox: no", "debug.show_hitboxes.on": "Mostrar hitbox: sí", "debug.version.header": "Información de la versión del cliente:", "debug.version.help": "F3 + V = Información de la versión del cliente", "demo.day.1": "Esta demo dura 5 días en el juego. ¡Aprovechá!", "demo.day.2": "<PERSON><PERSON><PERSON> d<PERSON>", "demo.day.3": "<PERSON><PERSON><PERSON> d<PERSON>", "demo.day.4": "Cuarto día", "demo.day.5": "¡Último día!", "demo.day.6": "Se acabaron los 5 días de juego. Usá %s para guardar una captura de tu creación.", "demo.day.warning": "¡El tiempo se está agotando!", "demo.demoExpired": "¡La demostración terminó!", "demo.help.buy": "¡<PERSON>mprar ahora!", "demo.help.fullWrapped": "Esta demo dura 5 días de juego (aprox. 1 hora y 40 minutos reales). ¡Revisá la sección «Progresoss» para guiarte! ¡Divertite!", "demo.help.inventory": "Usá %1$s para abrir tu inventario", "demo.help.jump": "Usá %1$s para saltar", "demo.help.later": "¡Seguir jugando!", "demo.help.movement": "Usá %1$s, %2$s, %3$s, %4$s para moverte y el mouse para mirar", "demo.help.movementMouse": "<PERSON><PERSON> el mouse para mirar a tu alrededor", "demo.help.movementShort": "Podés caminar con %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Demostración de Minecraft", "demo.remainingTime": "Tiempo restante: %s", "demo.reminder": "Se acabó la demo. ¡Comprá el juego para continuar o volvé a empezar con otro mundo!", "difficulty.lock.question": "¿Seguro que querés bloquear la dificultad del mundo? Esto lo va a dejar para siempre en %1$s, y no vas a poder cambiarlo.", "difficulty.lock.title": "Bloquear la dificultad del mundo", "disconnect.endOfStream": "Fin de la conexión", "disconnect.exceeded_packet_rate": "Expulsado/a por superar el límite de paquetes", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignorando solicitud de estado", "disconnect.loginFailedInfo": "Error al iniciar sesión: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "El modo multijugador está desactivado. Por favor, revisá las configuraciones de tu cuenta de Microsoft.", "disconnect.loginFailedInfo.invalidSession": "La sesión no es válida (reiniciá el juego y el launcher)", "disconnect.loginFailedInfo.serversUnavailable": "No se tiene acceso a los servidores de autenticación. Por favor, intentá de nuevo.", "disconnect.loginFailedInfo.userBanned": "Se te ha prohibido jugar en línea", "disconnect.lost": "Conexión perdida", "disconnect.packetError": "Error de protocolo de red", "disconnect.spam": "Expulsado por hacer spam", "disconnect.timeout": "Tiempo de espera agotado", "disconnect.transfer": "Transferido a otro servidor", "disconnect.unknownHost": "Ser<PERSON><PERSON>", "download.pack.failed": "No se pudo descargar %s de %s paquetes", "download.pack.progress.bytes": "Progreso: %s (tamaño total desconocido)", "download.pack.progress.percent": "Progreso: %s %%", "download.pack.title": "Descargando paquete de recursos %s/%s", "editGamerule.default": "Por defecto: %s", "editGamerule.title": "Editar las reglas del juego", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorción", "effect.minecraft.bad_omen": "Mal presagio", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Poder canalizado", "effect.minecraft.darkness": "Oscuridad", "effect.minecraft.dolphins_grace": "<PERSON><PERSON><PERSON> del<PERSON>a", "effect.minecraft.fire_resistance": "Resis. al fuego", "effect.minecraft.glowing": "Luminiscencia", "effect.minecraft.haste": "Apuro", "effect.minecraft.health_boost": "<PERSON><PERSON> mejorada", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON><PERSON> aldea", "effect.minecraft.hunger": "Hambre", "effect.minecraft.infested": "Infestación", "effect.minecraft.instant_damage": "<PERSON><PERSON>", "effect.minecraft.instant_health": "Curación instantánea", "effect.minecraft.invisibility": "Invisibilidad", "effect.minecraft.jump_boost": "Supersalto", "effect.minecraft.levitation": "Levitación", "effect.minecraft.luck": "<PERSON><PERSON>", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "Náuseas", "effect.minecraft.night_vision": "Visión nocturna", "effect.minecraft.oozing": "Mucosidad", "effect.minecraft.poison": "Veneno", "effect.minecraft.raid_omen": "Presagio de invasión", "effect.minecraft.regeneration": "Regeneración", "effect.minecraft.resistance": "Resistencia", "effect.minecraft.saturation": "Saturación", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON><PERSON> lenta", "effect.minecraft.slowness": "Lentitud", "effect.minecraft.speed": "Velocidad", "effect.minecraft.strength": "Fuerza", "effect.minecraft.trial_omen": "Presagio de desafío", "effect.minecraft.unluck": "<PERSON><PERSON> suerte", "effect.minecraft.water_breathing": "Respiración", "effect.minecraft.weakness": "Debilidad", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "Carga ventosa", "effect.minecraft.wither": "Descomposición", "effect.none": "Sin efectos", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Afinidad acuática", "enchantment.minecraft.bane_of_arthropods": "Pesadilla de los artrópodos", "enchantment.minecraft.binding_curse": "Maldición de ligamiento", "enchantment.minecraft.blast_protection": "Protección contra explosiones", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "Conductividad", "enchantment.minecraft.density": "Densidad", "enchantment.minecraft.depth_strider": "Agilidad acuática", "enchantment.minecraft.efficiency": "Eficiencia", "enchantment.minecraft.feather_falling": "Caída de pluma", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON><PERSON> de fuego", "enchantment.minecraft.fire_protection": "Protección contra el fuego", "enchantment.minecraft.flame": "Fuego", "enchantment.minecraft.fortune": "Fortuna", "enchantment.minecraft.frost_walker": "Paso helado", "enchantment.minecraft.impaling": "Empalamiento", "enchantment.minecraft.infinity": "Infinidad", "enchantment.minecraft.knockback": "Empuje", "enchantment.minecraft.looting": "Botín", "enchantment.minecraft.loyalty": "Lealtad", "enchantment.minecraft.luck_of_the_sea": "Suerte marina", "enchantment.minecraft.lure": "Atracción", "enchantment.minecraft.mending": "Reparación", "enchantment.minecraft.multishot": "Multidisparo", "enchantment.minecraft.piercing": "Perforación", "enchantment.minecraft.power": "<PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "Protección contra proyectiles", "enchantment.minecraft.protection": "Protección", "enchantment.minecraft.punch": "Retroceso", "enchantment.minecraft.quick_charge": "Carga rápida", "enchantment.minecraft.respiration": "Respiración", "enchantment.minecraft.riptide": "Propulsión acuática", "enchantment.minecraft.sharpness": "<PERSON><PERSON>", "enchantment.minecraft.silk_touch": "Toque de seda", "enchantment.minecraft.smite": "Golpeo", "enchantment.minecraft.soul_speed": "Velocidad de alma", "enchantment.minecraft.sweeping": "Barrido", "enchantment.minecraft.sweeping_edge": "Barrido", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.thorns": "Espinas", "enchantment.minecraft.unbreaking": "Irrompibilidad", "enchantment.minecraft.vanishing_curse": "Maldición de desaparición", "enchantment.minecraft.wind_burst": "Aeroimpulso", "entity.minecraft.acacia_boat": "Bote de acacia", "entity.minecraft.acacia_chest_boat": "Bote de acacia con baúl", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Nube de efecto persistente", "entity.minecraft.armadillo": "Armadillo", "entity.minecraft.armor_stand": "Soporte para armadura", "entity.minecraft.arrow": "Fle<PERSON>", "entity.minecraft.axolotl": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bamboo_chest_raft": "Balsa de bambú con baúl", "entity.minecraft.bamboo_raft": "Balsa de bambú", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "<PERSON><PERSON>", "entity.minecraft.birch_chest_boat": "Bote de abedul con baúl", "entity.minecraft.blaze": "Blaze", "entity.minecraft.block_display": "Bloque holográfico", "entity.minecraft.boat": "Bo<PERSON>", "entity.minecraft.bogged": "Esqueleto pantanoso", "entity.minecraft.breeze": "<PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Carga ventosa", "entity.minecraft.camel": "<PERSON>llo", "entity.minecraft.cat": "Gato", "entity.minecraft.cave_spider": "<PERSON><PERSON>", "entity.minecraft.cherry_boat": "<PERSON><PERSON> c<PERSON>", "entity.minecraft.cherry_chest_boat": "Bote de cerezo con baúl", "entity.minecraft.chest_boat": "<PERSON>te con baúl", "entity.minecraft.chest_minecart": "Carrito con baúl", "entity.minecraft.chicken": "Gallinas", "entity.minecraft.cod": "Bacalao", "entity.minecraft.command_block_minecart": "Carrito con bloque de comandos", "entity.minecraft.cow": "Vaca", "entity.minecraft.creaking": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creaking_transient": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Bote de roble oscuro", "entity.minecraft.dark_oak_chest_boat": "Bote de roble oscuro con baúl", "entity.minecraft.dolphin": "Delfín", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Bola de fuego de dragón", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON>", "entity.minecraft.egg": "<PERSON><PERSON> lanza<PERSON>", "entity.minecraft.elder_guardian": "Guardianes ancianos", "entity.minecraft.end_crystal": "Cristal del End", "entity.minecraft.ender_dragon": "Enderdragón", "entity.minecraft.ender_pearl": "<PERSON><PERSON> de ender la<PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Evocador", "entity.minecraft.evoker_fangs": "Colmillos de evocador", "entity.minecraft.experience_bottle": "Botella de experiencia lanzada", "entity.minecraft.experience_orb": "Orbe de experiencia", "entity.minecraft.eye_of_ender": "<PERSON><PERSON> de ender", "entity.minecraft.falling_block": "Bloque cayendo", "entity.minecraft.falling_block_type": "%s cayendo", "entity.minecraft.fireball": "Bola de fuego", "entity.minecraft.firework_rocket": "Cohete de fuegos artificiales", "entity.minecraft.fishing_bobber": "Carnada (caña de pescar)", "entity.minecraft.fox": "<PERSON><PERSON><PERSON>", "entity.minecraft.frog": "<PERSON>", "entity.minecraft.furnace_minecart": "<PERSON><PERSON> con horno", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Zombi gigante", "entity.minecraft.glow_item_frame": "<PERSON> l<PERSON>", "entity.minecraft.glow_squid": "Calamar luminoso", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Guardianes", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON> feliz", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Carrito con tolva", "entity.minecraft.horse": "Caballos", "entity.minecraft.husk": "<PERSON><PERSON><PERSON> mom<PERSON>", "entity.minecraft.illusioner": "Ilusionista", "entity.minecraft.interaction": "Interacción", "entity.minecraft.iron_golem": "Gólem de <PERSON>ro", "entity.minecraft.item": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.item_display": "Objeto holográfico", "entity.minecraft.item_frame": "<PERSON>", "entity.minecraft.jungle_boat": "<PERSON><PERSON>", "entity.minecraft.jungle_chest_boat": "<PERSON><PERSON> de jungla con baúl", "entity.minecraft.killer_bunny": "El conejo asesino", "entity.minecraft.leash_knot": "<PERSON>udo de rienda", "entity.minecraft.lightning_bolt": "Rayo", "entity.minecraft.lingering_potion": "Poción persistente", "entity.minecraft.llama": "Llamas", "entity.minecraft.llama_spit": "Escupitajo de llama", "entity.minecraft.magma_cube": "Cubo de magma", "entity.minecraft.mangrove_boat": "<PERSON><PERSON> de mangle", "entity.minecraft.mangrove_chest_boat": "Bote de mangle con baúl", "entity.minecraft.marker": "Marcador", "entity.minecraft.minecart": "<PERSON><PERSON>", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "<PERSON><PERSON>", "entity.minecraft.oak_chest_boat": "<PERSON>te de roble con baúl", "entity.minecraft.ocelot": "Ocelotes", "entity.minecraft.ominous_item_spawner": "Generador de objetos siniestro", "entity.minecraft.painting": "Cuadro", "entity.minecraft.pale_oak_boat": "<PERSON><PERSON> de roble p<PERSON>", "entity.minecraft.pale_oak_chest_boat": "Bote de roble pálido con baúl", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON>", "entity.minecraft.phantom": "Phantom", "entity.minecraft.pig": "<PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON> bruto", "entity.minecraft.pillager": "Saqueador", "entity.minecraft.player": "Jugador(a)", "entity.minecraft.polar_bear": "Osos polares", "entity.minecraft.potion": "Poción", "entity.minecraft.pufferfish": "Pez globo", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Devastador", "entity.minecraft.salmon": "Salmón", "entity.minecraft.sheep": "Oveja", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Proyectil de <PERSON>ker", "entity.minecraft.silverfish": "Pez de plata", "entity.minecraft.skeleton": "Esqueleto", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON> esqueleto", "entity.minecraft.slime": "Slime", "entity.minecraft.small_fireball": "Bola de fuego chica", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Gólem de nieve", "entity.minecraft.snowball": "<PERSON><PERSON> de <PERSON>eve", "entity.minecraft.spawner_minecart": "Carrito con generador de criaturas", "entity.minecraft.spectral_arrow": "Flecha espectral", "entity.minecraft.spider": "<PERSON><PERSON>", "entity.minecraft.splash_potion": "Poción arrojadiza", "entity.minecraft.spruce_boat": "Bo<PERSON> de a<PERSON>", "entity.minecraft.spruce_chest_boat": "Bote de abeto con baúl", "entity.minecraft.squid": "Calamar", "entity.minecraft.stray": "Esqueleto glacial", "entity.minecraft.strider": "Strider", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Texto holográfico", "entity.minecraft.tnt": "TNT activa", "entity.minecraft.tnt_minecart": "Carrito con TNT", "entity.minecraft.trader_llama": "Llama de comerciante", "entity.minecraft.trident": "Tridente", "entity.minecraft.tropical_fish": "Pez tropical", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "Pez cirujano negro", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON> moro", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON> mariposa adornado", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON> loro", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON> real", "entity.minecraft.tropical_fish.predefined.14": "Cíclido rojo", "entity.minecraft.tropical_fish.predefined.15": "Ophioblen<PERSON> atlanticus", "entity.minecraft.tropical_fish.predefined.16": "Pargo rojo", "entity.minecraft.tropical_fish.predefined.17": "Barbudo", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON> payaso tomate", "entity.minecraft.tropical_fish.predefined.19": "Pez ballesta", "entity.minecraft.tropical_fish.predefined.2": "Pez cirujano azul", "entity.minecraft.tropical_fish.predefined.20": "<PERSON>ez loro de aleta amarilla", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON> cirujano amarillo", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON> mariposa", "entity.minecraft.tropical_fish.predefined.4": "Cíclido", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON> payaso", "entity.minecraft.tropical_fish.predefined.6": "Betta algodón de azúcar", "entity.minecraft.tropical_fish.predefined.7": "Pseudochromidae", "entity.minecraft.tropical_fish.predefined.8": "Pargo rojo emperador", "entity.minecraft.tropical_fish.predefined.9": "Salmonete", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON> betta", "entity.minecraft.tropical_fish.type.blockfish": "Pez bloque", "entity.minecraft.tropical_fish.type.brinely": "De agua salada", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON> arcilla", "entity.minecraft.tropical_fish.type.dasher": "A rayas", "entity.minecraft.tropical_fish.type.flopper": "Saltarín", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "De arrecife", "entity.minecraft.tropical_fish.type.spotty": "A puntos", "entity.minecraft.tropical_fish.type.stripey": "A tiras", "entity.minecraft.tropical_fish.type.sunstreak": "Pez rayo de sol", "entity.minecraft.turtle": "Tortugas", "entity.minecraft.vex": "Vex", "entity.minecraft.villager": "Aldeano", "entity.minecraft.villager.armorer": "<PERSON><PERSON> de armaduras", "entity.minecraft.villager.butcher": "Carnicero", "entity.minecraft.villager.cartographer": "Cartógrafo", "entity.minecraft.villager.cleric": "Sacerdote", "entity.minecraft.villager.farmer": "Granjero", "entity.minecraft.villager.fisherman": "Pescador", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliotecario", "entity.minecraft.villager.mason": "Escultor", "entity.minecraft.villager.nitwit": "Idiota", "entity.minecraft.villager.none": "Aldeanos", "entity.minecraft.villager.shepherd": "Pastor", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON> de armas", "entity.minecraft.vindicator": "Vindicador", "entity.minecraft.wandering_trader": "Comerciante nómada", "entity.minecraft.warden": "Warden", "entity.minecraft.wind_charge": "Carga ventosa", "entity.minecraft.witch": "Bruja", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Esqueleto del Wither", "entity.minecraft.wither_skull": "Proyectil del Wither", "entity.minecraft.wolf": "Lobos", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON> zombi", "entity.minecraft.zombie_villager": "Aldeano zombi", "entity.minecraft.zombified_piglin": "<PERSON><PERSON> zomb<PERSON>ado", "entity.not_summonable": "No se pudo generar la entidad de tipo %s", "event.minecraft.raid": "Invasión", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Invasión - Derrota", "event.minecraft.raid.raiders_remaining": "Invasores restantes: %s", "event.minecraft.raid.victory": "Victoria", "event.minecraft.raid.victory.full": "Invasión - Victoria", "filled_map.buried_treasure": "Mapa del tesoro", "filled_map.explorer_jungle": "Mapa de exploración de jungla", "filled_map.explorer_swamp": "Mapa de exploración de pantano", "filled_map.id": "ID nº %s", "filled_map.level": "(Nivel %s/%s)", "filled_map.locked": "Bloqueado", "filled_map.mansion": "Mapa de exploración de bosques", "filled_map.monument": "Mapa de exploración del océano", "filled_map.scale": "Escala 1:%s", "filled_map.trial_chambers": "Mapa de exploración del desafío", "filled_map.unknown": "Mapa desconocido", "filled_map.village_desert": "Mapa de aldea de desierto", "filled_map.village_plains": "Mapa de aldea de llanura", "filled_map.village_savanna": "Mapa de aldea de sabana", "filled_map.village_snowy": "Mapa de aldea de llanura nevada", "filled_map.village_taiga": "Mapa de aldea de taiga", "flat_world_preset.minecraft.bottomless_pit": "Pozo sin fondo", "flat_world_preset.minecraft.classic_flat": "Clásico", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Superficie", "flat_world_preset.minecraft.redstone_ready": "Listo para redstone", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON> ne<PERSON>do", "flat_world_preset.minecraft.the_void": "El vacío", "flat_world_preset.minecraft.tunnelers_dream": "Sueño de tuneladoras", "flat_world_preset.minecraft.water_world": "Mundo acuático", "flat_world_preset.unknown": "¿¿??", "gameMode.adventure": "Aventura", "gameMode.changed": "Modo de juego cambiado a %s", "gameMode.creative": "Creativo", "gameMode.hardcore": "Hardcore", "gameMode.spectator": "Espectador", "gameMode.survival": "Supervivencia", "gamerule.allowFireTicksAwayFromPlayer": "Cargar fuego lejos de los jugadores", "gamerule.allowFireTicksAwayFromPlayer.description": "Controla si el fuego o la lava deberían estar cargados al estar a más de 8 chunks de distancia de cualquier jugador", "gamerule.announceAdvancements": "Anunciar progresos", "gamerule.blockExplosionDropDecay": "En explosiones por interacción con bloques, algunos bloques no soltarán objetos", "gamerule.blockExplosionDropDecay.description": "Algunos de los objetos soltados por bloques destruidos a causa de explosiones derivadas de interacciones con bloques desaparecen con la explosión.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Obtención de ítems", "gamerule.category.misc": "Varios", "gamerule.category.mobs": "Criaturas", "gamerule.category.player": "Jugador", "gamerule.category.spawning": "Generación de criaturas", "gamerule.category.updates": "Actualizaciones del mundo", "gamerule.commandBlockOutput": "Notificar uso de bloques de comandos", "gamerule.commandModificationBlockLimit": "Límite de bloques modificables por comandos", "gamerule.commandModificationBlockLimit.description": "Número de bloques que se pueden cambiar de una vez con un comando, como «/fill» o «/clone».", "gamerule.disableElytraMovementCheck": "Desactivar verificación de vuelo de elytra", "gamerule.disablePlayerMovementCheck": "Desactivar control de movimiento del jugador", "gamerule.disableRaids": "Desactivar invasiones", "gamerule.doDaylightCycle": "Ciclo de día y noche", "gamerule.doEntityDrops": "Soltar ítems de entidades", "gamerule.doEntityDrops.description": "Permite que las entidades suelten ítems al romperlas. Afecta a carritos (incluyendo sus inventarios), marcos, botes, etc.", "gamerule.doFireTick": "Propagación del fuego", "gamerule.doImmediateRespawn": "Reaparición instantánea", "gamerule.doInsomnia": "<PERSON>rar phantoms", "gamerule.doLimitedCrafting": "Exigir recetas para poder fabricar", "gamerule.doLimitedCrafting.description": "Si se activa, solo se podrán fabricar recetas aprendidas.", "gamerule.doMobLoot": "Soltar ítems de criaturas", "gamerule.doMobLoot.description": "Controla que las criaturas puedan soltar recursos, incluyendo orbes de experiencia.", "gamerule.doMobSpawning": "Generar criaturas", "gamerule.doMobSpawning.description": "Algunas entidades pueden tener sus propias reglas.", "gamerule.doPatrolSpawning": "Generar bandas de saqueadores", "gamerule.doTileDrops": "Soltar ítems de bloques", "gamerule.doTileDrops.description": "Controla que los bloques puedan soltar recursos, incluyendo orbes de experiencia.", "gamerule.doTraderSpawning": "Generar comerciantes nómadas", "gamerule.doVinesSpread": "Propagación de enredaderas", "gamerule.doVinesSpread.description": "Controla si las enredaderas se dispersan a bloques adyacentes. No afecta otros tipos de enredaderas como enredaderas lloronas o enredaderas retorcidas, entre otras.", "gamerule.doWardenSpawning": "Generar wardens", "gamerule.doWeatherCycle": "Ciclo meteorológico", "gamerule.drowningDamage": "<PERSON>ño por ahogamiento", "gamerule.enderPearlsVanishOnDeath": "Las perlas de ender tiradas desaparecen al morir", "gamerule.enderPearlsVanishOnDeath.description": "Las perlas de ender tiradas por un jugador desaparecen cuando este muere.", "gamerule.entitiesWithPassengersCanUsePortals": "Las entidades con pasajeros pueden usar portales", "gamerule.entitiesWithPassengersCanUsePortals.description": "Permite que las entidades con pasajeros puedan viajar a través de portales del Nether, del End, y accesos al End.", "gamerule.fallDamage": "Daño por caída", "gamerule.fireDamage": "Daño por fuego", "gamerule.forgiveDeadPlayers": "Perdonar jugadores muertos", "gamerule.forgiveDeadPlayers.description": "Las criaturas neutrales que estén enojadas se relajan cuando el jugador al que persiguen muere cerca.", "gamerule.freezeDamage": "Daño por congelamiento", "gamerule.globalSoundEvents": "Eventos de sonido globales", "gamerule.globalSoundEvents.description": "<PERSON>uando se producen ciertos eventos durante la partida, como que aparezca un jefe, el sonido se escucha en todas partes.", "gamerule.keepInventory": "Mantener inventario al morir", "gamerule.lavaSourceConversion": "Convertir lava en fuente", "gamerule.lavaSourceConversion.description": "Cuando una corriente de lava está rodeada de más lava por dos de sus lados, se convierte en una fuente.", "gamerule.locatorBar": "Activar la barra localizadora de jugadores", "gamerule.locatorBar.description": "Al activarse, se muestra una barra en la pantalla que indica la dirección de los jugadores.", "gamerule.logAdminCommands": "Notificar uso de comandos de admin", "gamerule.maxCommandChainLength": "Límite de comandos encadenados", "gamerule.maxCommandChainLength.description": "Se aplica a las cadenas de bloques de comandos y funciones.", "gamerule.maxCommandForkCount": "Límite de contextos del comando", "gamerule.maxCommandForkCount.description": "El número máximo de contextos que se pueden utilizar en comandos como «execute as».", "gamerule.maxEntityCramming": "Límite de entidades por bloque", "gamerule.minecartMaxSpeed": "Velocidad máxima de los carritos", "gamerule.minecartMaxSpeed.description": "Velocidad máxima por defecto de un carrito por tierra.", "gamerule.mobExplosionDropDecay": "En explosiones por criaturas, algunos bloques no soltarán objetos", "gamerule.mobExplosionDropDecay.description": "Algunos de los objetos soltados por bloques destruidos a causa de explosiones derivadas de criaturas desaparecen con la explosión.", "gamerule.mobGriefing": "Permitir que las criaturas destruyan", "gamerule.naturalRegeneration": "<PERSON><PERSON>ar salud", "gamerule.playersNetherPortalCreativeDelay": "Tiempo de espera del portal al Nether en modo creativo", "gamerule.playersNetherPortalCreativeDelay.description": "Tiempo (en ticks) que necesita estar un jugador sobre el portal en modo creativo para cambiar de dimensión.", "gamerule.playersNetherPortalDefaultDelay": "Tiempo de espera del portal al Nether en modos que no sean creativo", "gamerule.playersNetherPortalDefaultDelay.description": "Tiempo (en ticks) que necesita pasar un jugador sobre el portal en modos que no sean creativo para cambiar de dimensión.", "gamerule.playersSleepingPercentage": "Porcentaje de jugadores para dormir", "gamerule.playersSleepingPercentage.description": "El porcentaje de jugadores que deben dormir para pasar la noche.", "gamerule.projectilesCanBreakBlocks": "Los proyectiles pueden romper bloques", "gamerule.projectilesCanBreakBlocks.description": "Controla si los proyectiles pueden romper ciertos bloques al impactar en ellos.", "gamerule.randomTickSpeed": "Frecuencia de ticks aleatorios", "gamerule.reducedDebugInfo": "Reducir datos de F3", "gamerule.reducedDebugInfo.description": "Limita el contenido de la pantalla debug al apretar F3.", "gamerule.sendCommandFeedback": "Mostrar respuestas de comandos", "gamerule.showDeathMessages": "<PERSON>rar mensajes de muertes", "gamerule.snowAccumulationHeight": "Altura de acumulación de nieve", "gamerule.snowAccumulationHeight.description": "<PERSON><PERSON><PERSON> nieva, se forma este número máximo de capas de nieve en el suelo.", "gamerule.spawnChunkRadius": "Radio del punto de reaparición", "gamerule.spawnChunkRadius.description": "Cantidad de chunks que se mantienen cargados alrededor del punto de aparición.", "gamerule.spawnRadius": "Radio del punto de reaparición", "gamerule.spawnRadius.description": "Controla el tamaño de la zona alrededor del punto de reaparición donde pueden aparecer los jugadores.", "gamerule.spectatorsGenerateChunks": "Generar terreno en modo espectador", "gamerule.tntExplodes": "Permitir explosiones de TNT", "gamerule.tntExplosionDropDecay": "En explosiones por TNT, algunos bloques no soltarán objetos", "gamerule.tntExplosionDropDecay.description": "Algunos de los objetos soltados por bloques destruidos a causa de explosiones derivadas de la dinamita desaparecen con la explosión.", "gamerule.universalAnger": "Ira universal", "gamerule.universalAnger.description": "Si está activa, las criaturas neutrales que estén enojadas atacan a cualquier jugador cercano, no sólo al que las hizo enojar.\nFunciona mejor si la regla «Perdonar jugadores muertos» se desactiva.", "gamerule.waterSourceConversion": "Convertir agua en fuente", "gamerule.waterSourceConversion.description": "Cuando una corriente de agua está rodeada de más agua por dos de sus lados, se convierte en una fuente.", "generator.custom": "personalizado", "generator.customized": "Personalizado (antiguo)", "generator.minecraft.amplified": "AMPLIFICADO", "generator.minecraft.amplified.info": "Aviso: ¡solo por diversión! \nRequiere una computadora potente.", "generator.minecraft.debug_all_block_states": "Debug", "generator.minecraft.flat": "Plano", "generator.minecraft.large_biomes": "Superbiomas", "generator.minecraft.normal": "Por defecto", "generator.minecraft.single_biome_surface": "Bioma único ", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON><PERSON> flotantes", "gui.abuseReport.attestation": "Al enviar este reporte, confirmás que la información proporcionada es correcta y está completa.", "gui.abuseReport.comments": "Comentarios", "gui.abuseReport.describe": "Compartir detalles nos ayuda a tomar una decisión apropiada.", "gui.abuseReport.discard.content": "<PERSON> salís, este reporte y tus comentarios se descartarán.\n¿Estás seguro de que querés salir?", "gui.abuseReport.discard.discard": "Salir y descartar reporte", "gui.abuseReport.discard.draft": "Guardar borrador", "gui.abuseReport.discard.return": "<PERSON><PERSON><PERSON>", "gui.abuseReport.discard.title": "¿Descartar reporte y comentarios?", "gui.abuseReport.draft.content": "¿Te gustaría seguir editando este reporte o descartarla y crear una nueva?", "gui.abuseReport.draft.discard": "Descar<PERSON>", "gui.abuseReport.draft.edit": "<PERSON><PERSON><PERSON>", "gui.abuseReport.draft.quittotitle.content": "¿Te gustaría seguir editán<PERSON>lo o descartarlo?", "gui.abuseReport.draft.quittotitle.title": "Tenés un borrador de reporte que se perderá si salís", "gui.abuseReport.draft.title": "¿Editar borrador de reporte de chat?", "gui.abuseReport.error.title": "Error al enviar tu reporte", "gui.abuseReport.message": "¿Dónde observaste el mal comportamiento?\nEsto nos ayudará a investigar tu caso.", "gui.abuseReport.more_comments": "Describí qué sucedió:", "gui.abuseReport.name.comment_box_label": "Por favor describe por qué quieres reportar este nombre:", "gui.abuseReport.name.reporting": "Estás reportando a «%s».", "gui.abuseReport.name.title": "Reportar nombre de jugador", "gui.abuseReport.observed_what": "¿Por qué estás reportando esto?", "gui.abuseReport.read_info": "Más sobre los reportes", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drogas o alcohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Alguien está incitando a otros la participación en actividades ilegales relacionadas con drogas, o el consumo de alcohol en menores.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Explotación o abuso sexual de menores", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Alguien está incitando o hablando de un comportamiento indecente hacia menores.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Difamación, suplantación de identidad o información falsa", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Alguien está dañando la reputación de otra persona, suplantando la identidad de alguien o compartiendo información falsa, con el objetivo de aprovecharse o confundir a otros.", "gui.abuseReport.reason.description": "Descripción:", "gui.abuseReport.reason.false_reporting": "Reporte falso", "gui.abuseReport.reason.generic": "<PERSON><PERSON><PERSON><PERSON><PERSON>/a", "gui.abuseReport.reason.generic.description": "Me molestó / hizo algo que no me gusta.", "gui.abuseReport.reason.harassment_or_bullying": "Intimidación o acoso", "gui.abuseReport.reason.harassment_or_bullying.description": "Alguien está hostigando, atacando o acosándote a vos o a otra persona. Esto incluye haber intentado contactar repetidamente con vos o con otra persona o publicar información personal privada sobre vos u otra persona sin consentimiento («doxeo»).", "gui.abuseReport.reason.hate_speech": "Discurso de odio", "gui.abuseReport.reason.hate_speech.description": "Alguien está atacándote a vos o a otro jugador por características de su identidad, como religión, raza o sexualidad.", "gui.abuseReport.reason.imminent_harm": "Peligro inminente - Amenazas de daño a otros", "gui.abuseReport.reason.imminent_harm.description": "Alguien está amenazando con hacerte daño a vos o a otra persona en la vida real.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Imágenes íntimas sin consentimiento", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Alguien está pidiendo, compartiendo o incitando al tráfico de imágenes privadas e íntimas.", "gui.abuseReport.reason.self_harm_or_suicide": "Peligro inminente - Autolesión o suicidio", "gui.abuseReport.reason.self_harm_or_suicide.description": "Alguien está amenazando con autolesionarse o hablando sobre hacerlo en la vida real.", "gui.abuseReport.reason.sexually_inappropriate": "Contenido de naturaleza sexual inapropiado", "gui.abuseReport.reason.sexually_inappropriate.description": "Aspectos que muestran elementos de naturaleza sexual, órganos sexuales o violencia sexual.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorismo o extremismo violento", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Alguien está hablando, incitando o amenazando con cometer actos de terrorismo o extremismo violento por motivos políticos, religiosos, ideológicos o de cualquier otra índole.", "gui.abuseReport.reason.title": "Elegir categoría de reporte", "gui.abuseReport.report_sent_msg": "Recibimos tu reporte, ¡muchas gracias!\n\nNuestro equipo la revisará lo más pronto posible.", "gui.abuseReport.select_reason": "Elegir categoría de reporte", "gui.abuseReport.send": "Enviar reporte", "gui.abuseReport.send.comment_too_long": "Por favor, acortá el comentario", "gui.abuseReport.send.error_message": "Se produjo un error al enviar tu reporte:\n«%s»", "gui.abuseReport.send.generic_error": "Se ha producido un error inesperado al enviar tu reporte.", "gui.abuseReport.send.http_error": "Se ha producido un error HTTP inesperado al enviar tu reporte.", "gui.abuseReport.send.json_error": "Se ha detectado una carga útil mal estructurada al enviar tu reporte.", "gui.abuseReport.send.no_reason": "Elegí una categoría para el reporte", "gui.abuseReport.send.not_attested": "Por favor, leé el texto anterior y asegurate de marcar la casilla para poder enviar el reporte", "gui.abuseReport.send.service_unavailable": "No se pudo contactar con el servicio de reportes de abuso. Asegurate de estar conectado a internet e intentalo de nuevo.", "gui.abuseReport.sending.title": "Enviando reporte...", "gui.abuseReport.sent.title": "Reporte enviado", "gui.abuseReport.skin.title": "Reportar aspecto de jugador", "gui.abuseReport.title": "<PERSON>ar jugador", "gui.abuseReport.type.chat": "Men<PERSON><PERSON><PERSON>", "gui.abuseReport.type.name": "Nombre del jugador", "gui.abuseReport.type.skin": "<PERSON><PERSON><PERSON> de j<PERSON>dor", "gui.acknowledge": "Entendido", "gui.advancements": "Progresos", "gui.all": "<PERSON><PERSON>", "gui.back": "Atrás", "gui.banned.description": "%s\n\n%s\n\nMás información en el siguiente enlace: %s", "gui.banned.description.permanent": "Tu cuenta fue suspendida permanentemente, esto significa que no podrás jugar en línea y/o unirte a Realms.", "gui.banned.description.reason": "Recientemente recibimos una denuncia por mal comportamiento por parte de tu cuenta. Nuestros moderadores han revisado el caso y lo han identificado como %s, lo que va en contra de los estándares de la comunidad de Minecraft.", "gui.banned.description.reason_id": "Código: %s", "gui.banned.description.reason_id_message": "Código: %s - %s", "gui.banned.description.temporary": "%s <PERSON><PERSON> entonces, no podrás jugar en línea o unirte a Realms.", "gui.banned.description.temporary.duration": "Tu cuenta fue suspendida temporalmente y será reactivada en %s.", "gui.banned.description.unknownreason": "Recientemente recibimos una denuncia por mal comportamiento por parte de tu cuenta. Nuestros moderadores han revisado el caso y han identificado que va en contra de los estándares de la comunidad de Minecraft.", "gui.banned.name.description": "Tu nombre actual, \"%s\", incumple nuestros estándares comunitarios. Podés jugar en mundos de un jugador, pero debeé cambiar tu nombre para jugar en multijugador.\n\nAprendé más o solicité una revisión del caso en el siguiente enlace: %s", "gui.banned.name.title": "Nombre no permitido en multijugador", "gui.banned.reason.defamation_impersonation_false_information": "Suplantación o intercambio de información para engañar o aprovecharse de otros", "gui.banned.reason.drugs": "Referencias a drogas ilegales", "gui.banned.reason.extreme_violence_or_gore": "Representaciones de violencia excesiva o gore en la vida real", "gui.banned.reason.false_reporting": "Exceso de reportes falsos o inexactos", "gui.banned.reason.fraud": "Adquisición o uso fraudulento de contenido", "gui.banned.reason.generic_violation": "Violación de las normas de la comunidad", "gui.banned.reason.harassment_or_bullying": "Lenguaje abusivo usado de manera dirigida y dañina", "gui.banned.reason.hate_speech": "Discurso de odio o discriminación", "gui.banned.reason.hate_terrorism_notorious_figure": "Referencias a grupos de odio, organizaciones terroristas o figuras notorias", "gui.banned.reason.imminent_harm_to_person_or_property": "Intención de causar daños a personas o bienes en la vida real", "gui.banned.reason.nudity_or_pornography": "Mostrar material lascivo o pornográfico", "gui.banned.reason.sexually_inappropriate": "Temas o contenido de naturaleza sexual", "gui.banned.reason.spam_or_advertising": "Spam o publicidad", "gui.banned.skin.description": "Tu aspecto actual incumple nuestros estándares comunitarios. Podés jugar con un aspecto predeterminado, o seleccionar uno nuevo.\n\nAprendé más o solicitá una revisión del caso en el siguiente enlace: %s", "gui.banned.skin.title": "Aspecto no permitido", "gui.banned.title.permanent": "Cuenta bloqueada permanentemente", "gui.banned.title.temporary": "Cuenta suspendida temporalmente", "gui.cancel": "<PERSON><PERSON><PERSON>", "gui.chatReport.comments": "Comentarios", "gui.chatReport.describe": "Compartir detalles nos ayuda a tomar una decisión apropiada.", "gui.chatReport.discard.content": "Si salís ahora, se borrará este reporte y tus comentarios.\n¿Seguro que querés salir?", "gui.chatReport.discard.discard": "Salir y descartar reporte", "gui.chatReport.discard.draft": "Guardar como borrador", "gui.chatReport.discard.return": "<PERSON><PERSON><PERSON>", "gui.chatReport.discard.title": "¿Descartar reporte y comentarios?", "gui.chatReport.draft.content": "¿Querés seguir editando el reporte existente o descartarlo y crear uno nuevo?", "gui.chatReport.draft.discard": "Descar<PERSON>", "gui.chatReport.draft.edit": "<PERSON><PERSON><PERSON>", "gui.chatReport.draft.quittotitle.content": "¿Querés seguir editán<PERSON>lo o descartarlo?", "gui.chatReport.draft.quittotitle.title": "Tenés un borrador de reporte que se perderá si salís", "gui.chatReport.draft.title": "¿Editar borrador del reporte?", "gui.chatReport.more_comments": "Describí qué sucedió:", "gui.chatReport.observed_what": "¿Por qué estás reportando esto?", "gui.chatReport.read_info": "Descubrí más sobre los reportes", "gui.chatReport.report_sent_msg": "Hemos recibido tu reporte. ¡Gracias!\n\nNuestro equipo lo revisará lo antes posible.", "gui.chatReport.select_chat": "Elegir mensa<PERSON> de chat a reportar", "gui.chatReport.select_reason": "Elegir categoría de reporte", "gui.chatReport.selected_chat": "Mensajes de chat marcados para reportar: %s", "gui.chatReport.send": "Enviar reporte", "gui.chatReport.send.comments_too_long": "Por favor, acortá el comentario", "gui.chatReport.send.no_reason": "Elegí una categoría para el reporte", "gui.chatReport.send.no_reported_messages": "Elegí al menos un mensaje del chat para reportar", "gui.chatReport.send.too_many_messages": "Estás incluyendo demasiados mensajes en el reporte", "gui.chatReport.title": "Reportar mensaje de jugador", "gui.chatSelection.context": "Los mensajes relacionados con esta selección serán incluidos para añadir un contexto adicional", "gui.chatSelection.fold": "%s mensajes ocultos", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s entró al chat", "gui.chatSelection.message.narrate": "%s dijo: %s a la(s) %s", "gui.chatSelection.selected": "%s/%s mensajes seleccionados", "gui.chatSelection.title": "Elegir mensa<PERSON> de chat a reportar", "gui.continue": "<PERSON><PERSON><PERSON><PERSON>", "gui.copy_link_to_clipboard": "Copiar al portapapeles", "gui.days": "%s día(s)", "gui.done": "Aceptar", "gui.down": "Abajo", "gui.entity_tooltip.type": "Tipo: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s archivos rechazados", "gui.fileDropFailure.title": "Error al añadir archivos", "gui.hours": "%s hora(s)", "gui.loadingMinecraft": "Cargando Minecraft", "gui.minutes": "%s minuto(s)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Botón de %s", "gui.narrate.editBox": "Barra de texto de %s: %s", "gui.narrate.slider": "Barra de %s", "gui.narrate.tab": "Pestaña de %s", "gui.no": "No", "gui.none": "NInguno", "gui.ok": "Aceptar", "gui.open_report_dir": "<PERSON><PERSON><PERSON><PERSON> de informes", "gui.proceed": "<PERSON><PERSON><PERSON><PERSON>", "gui.recipebook.moreRecipes": "Click derecho para ver más", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Buscar...", "gui.recipebook.toggleRecipes.all": "Mostrando todas las recetas", "gui.recipebook.toggleRecipes.blastable": "Mostrando recetas fundibles", "gui.recipebook.toggleRecipes.craftable": "Mostrando recetas fabricables", "gui.recipebook.toggleRecipes.smeltable": "Mostrando recetas horneables", "gui.recipebook.toggleRecipes.smokable": "Mostrando recetas ahumables", "gui.report_to_server": "Informar al servidor", "gui.socialInteractions.blocking_hint": "Administrar con cuenta de Microsoft", "gui.socialInteractions.empty_blocked": "No hay jugadores bloqueados en el chat", "gui.socialInteractions.empty_hidden": "No hay jugadores ocultos en chat", "gui.socialInteractions.hidden_in_chat": "Los mensajes de chat de %s van a ocultarse", "gui.socialInteractions.hide": "Ocultar en el chat", "gui.socialInteractions.narration.hide": "Ocultar mensajes de %s", "gui.socialInteractions.narration.report": "Reportar a %s", "gui.socialInteractions.narration.show": "Mostrar mensajes de %s", "gui.socialInteractions.report": "Reportar", "gui.socialInteractions.search_empty": "No se encontró a nadie con ese nombre", "gui.socialInteractions.search_hint": "Buscar...", "gui.socialInteractions.server_label.multiple": "%s - %s jugadores", "gui.socialInteractions.server_label.single": "%s - %s jugador", "gui.socialInteractions.show": "Mostrar en el chat", "gui.socialInteractions.shown_in_chat": "Los mensajes de chat de %s van a mostrarse", "gui.socialInteractions.status_blocked": "Bloqueado", "gui.socialInteractions.status_blocked_offline": "Bloqueado - Sin conexión", "gui.socialInteractions.status_hidden": "Oculto", "gui.socialInteractions.status_hidden_offline": "Oculto - Sin conexión", "gui.socialInteractions.status_offline": "Sin conexión", "gui.socialInteractions.tab_all": "Todo", "gui.socialInteractions.tab_blocked": "Bloqueado", "gui.socialInteractions.tab_hidden": "Oculto", "gui.socialInteractions.title": "Interacciones sociales", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report": "<PERSON>ar jugador", "gui.socialInteractions.tooltip.report.disabled": "El servicio de reportes no está disponible", "gui.socialInteractions.tooltip.report.no_messages": "No hay mensajes reportables de %s", "gui.socialInteractions.tooltip.report.not_reportable": "Este jugador no puede ser reportado porque sus mesajes no pudieron ser verificados por el servidor", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON> mensajes", "gui.stats": "Estadísticas", "gui.toMenu": "Volver a la lista de servidores", "gui.toRealms": "Volver a la lista de Realms", "gui.toTitle": "<PERSON>ver al menú principal", "gui.toWorld": "Volver a la lista de mundos", "gui.togglable_slot": "Hacé click para desactivar la ranura", "gui.up": "Arriba", "gui.waitingForResponse.button.inactive": "Volver (%ss)", "gui.waitingForResponse.title": "E<PERSON>and<PERSON> al servidor", "gui.yes": "Sí", "hanging_sign.edit": "Editar mensaje del cartel colgante", "instrument.minecraft.admire_goat_horn": "Admiración", "instrument.minecraft.call_goat_horn": "Llamada", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "Sen<PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Reflexión", "instrument.minecraft.seek_goat_horn": "Búsqueda", "instrument.minecraft.sing_goat_horn": "Canto", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "Bo<PERSON>r", "inventory.hotbarInfo": "Guardá la barra de ítems con %1$s+%2$s", "inventory.hotbarSaved": "Barra de ítems guardada (%1$s+%2$s para usarla)", "item.canBreak": "<PERSON><PERSON><PERSON> romper:", "item.canPlace": "Puede colocarse sobre:", "item.canUse.unknown": "Desconocido", "item.color": "Color: %s", "item.components": "%s componente(s)", "item.disabled": "Objeto desactivado", "item.durability": "Durabilidad: %s / %s", "item.dyed": "Teñido", "item.minecraft.acacia_boat": "Bote de acacia", "item.minecraft.acacia_chest_boat": "Bote de acacia con baúl", "item.minecraft.allay_spawn_egg": "<PERSON><PERSON> allay", "item.minecraft.amethyst_shard": "Fragmento de amatista", "item.minecraft.angler_pottery_shard": "Fragmento de cerámica de pescador", "item.minecraft.angler_pottery_sherd": "Fragmento de cerámica de pescador", "item.minecraft.apple": "Man<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Fragmento de cerámica de arquero", "item.minecraft.archer_pottery_sherd": "Fragmento de cerámica de arquero", "item.minecraft.armadillo_scute": "Escama de armadillo", "item.minecraft.armadillo_spawn_egg": "<PERSON><PERSON>", "item.minecraft.armor_stand": "Soporte para armadura", "item.minecraft.arms_up_pottery_shard": "Fragmento de cerámica de brazos arriba", "item.minecraft.arms_up_pottery_sherd": "Fragmento de cerámica de gurí", "item.minecraft.arrow": "Fle<PERSON>", "item.minecraft.axolotl_bucket": "<PERSON><PERSON> con ajolote", "item.minecraft.axolotl_spawn_egg": "<PERSON><PERSON>", "item.minecraft.baked_potato": "<PERSON>cida", "item.minecraft.bamboo_chest_raft": "Balsa de bambú con baúl", "item.minecraft.bamboo_raft": "Balsa de bambú", "item.minecraft.bat_spawn_egg": "<PERSON><PERSON>", "item.minecraft.bee_spawn_egg": "<PERSON><PERSON> abeja", "item.minecraft.beef": "Carne de vaca cruda", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "Semillas de remolacha", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.birch_boat": "<PERSON><PERSON>", "item.minecraft.birch_chest_boat": "Bote de abedul con baúl", "item.minecraft.black_bundle": "Bolsa negra", "item.minecraft.black_dye": "Tinte negro", "item.minecraft.black_harness": "Arnés negro", "item.minecraft.blade_pottery_shard": "Fragmento de cerámica de espada", "item.minecraft.blade_pottery_sherd": "Fragmento de cerámica de espada", "item.minecraft.blaze_powder": "Polvo de blaze", "item.minecraft.blaze_rod": "Vara de blaze", "item.minecraft.blaze_spawn_egg": "Generar blaze", "item.minecraft.blue_bundle": "Bolsa azul", "item.minecraft.blue_dye": "<PERSON>te a<PERSON>l", "item.minecraft.blue_egg": "Huevo azul", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "Generar esqueleto <PERSON>", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.bolt_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> de re<PERSON>", "item.minecraft.bone": "Hueso", "item.minecraft.bone_meal": "<PERSON><PERSON><PERSON> molido", "item.minecraft.book": "Libro", "item.minecraft.bordure_indented_banner_pattern": "Estampado de bordura dentada", "item.minecraft.bow": "Arco", "item.minecraft.bowl": "Cuenco", "item.minecraft.bread": "Pan", "item.minecraft.breeze_rod": "Vara de breeze", "item.minecraft.breeze_spawn_egg": "Generar breeze", "item.minecraft.brewer_pottery_shard": "Fragmento de cerámica de poción", "item.minecraft.brewer_pottery_sherd": "Fragmento de cerámica de poción", "item.minecraft.brewing_stand": "Destiladora", "item.minecraft.brick": "Ladrillo", "item.minecraft.brown_bundle": "<PERSON><PERSON><PERSON> marr<PERSON>", "item.minecraft.brown_dye": "<PERSON><PERSON>", "item.minecraft.brown_egg": "<PERSON><PERSON> ma<PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.brush": "<PERSON><PERSON><PERSON>", "item.minecraft.bucket": "Balde", "item.minecraft.bundle": "Bolsa", "item.minecraft.bundle.empty": "Vacío", "item.minecraft.bundle.empty.description": "<PERSON><PERSON><PERSON> contener varios objetos", "item.minecraft.bundle.full": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Fragmento de cerámica de fuego", "item.minecraft.burn_pottery_sherd": "Fragmento de cerámica de fogata", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON> camello", "item.minecraft.carrot": "Zanahoria", "item.minecraft.carrot_on_a_stick": "Caña con zanahoria", "item.minecraft.cat_spawn_egg": "Generar gato", "item.minecraft.cauldron": "Calder<PERSON>", "item.minecraft.cave_spider_spawn_egg": "Generar a<PERSON> cue<PERSON>", "item.minecraft.chainmail_boots": "Botas de cota de malla", "item.minecraft.chainmail_chestplate": "Pechera de cota de malla", "item.minecraft.chainmail_helmet": "Casco de cota de malla", "item.minecraft.chainmail_leggings": "Pantalones de cota de malla", "item.minecraft.charcoal": "Carbón vegetal", "item.minecraft.cherry_boat": "<PERSON><PERSON> c<PERSON>", "item.minecraft.cherry_chest_boat": "Bote de cerezo con baúl", "item.minecraft.chest_minecart": "Carrito con baúl", "item.minecraft.chicken": "<PERSON><PERSON> crudo", "item.minecraft.chicken_spawn_egg": "<PERSON><PERSON> gallina", "item.minecraft.chorus_fruit": "Fruta de chorus", "item.minecraft.clay_ball": "<PERSON><PERSON>", "item.minecraft.clock": "<PERSON><PERSON><PERSON>", "item.minecraft.coal": "Carbón", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.coast_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.cocoa_beans": "Granos de cacao", "item.minecraft.cod": "Bacalao crudo", "item.minecraft.cod_bucket": "Balde con bacalao", "item.minecraft.cod_spawn_egg": "<PERSON><PERSON> b<PERSON>", "item.minecraft.command_block_minecart": "Carrito con bloque de comandos", "item.minecraft.compass": "Brújula", "item.minecraft.cooked_beef": "Carne de vaca cocinada", "item.minecraft.cooked_chicken": "<PERSON><PERSON> cocinado", "item.minecraft.cooked_cod": "Bacalao cocinado", "item.minecraft.cooked_mutton": "Cordero cocinado", "item.minecraft.cooked_porkchop": "<PERSON><PERSON><PERSON> cocinada", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON> co<PERSON>ado", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON> cocinado", "item.minecraft.cookie": "Galleta", "item.minecraft.copper_ingot": "Lingote de cobre", "item.minecraft.cow_spawn_egg": "Generar vaca", "item.minecraft.creaking_spawn_egg": "<PERSON><PERSON> crujidor", "item.minecraft.creeper_banner_pattern": "Diseño de estandarte", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per", "item.minecraft.creeper_banner_pattern.new": "Estampado de creeper", "item.minecraft.creeper_spawn_egg": "Generar creeper", "item.minecraft.crossbow": "Ballesta", "item.minecraft.crossbow.projectile": "Proyectil:", "item.minecraft.crossbow.projectile.multiple": "Proyectil: %s x %s", "item.minecraft.crossbow.projectile.single": "Proyectil: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON><PERSON> cian", "item.minecraft.cyan_dye": "<PERSON><PERSON> cian", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Fragmento de cerámica de peligro", "item.minecraft.danger_pottery_sherd": "Fragmento de cerámica de creeper", "item.minecraft.dark_oak_boat": "Bote de roble oscuro", "item.minecraft.dark_oak_chest_boat": "Bote de roble oscuro con baúl", "item.minecraft.debug_stick": "<PERSON>lo debug", "item.minecraft.debug_stick.empty": "%s no tiene propiedades", "item.minecraft.debug_stick.select": "seleccionado «%s» (%s)", "item.minecraft.debug_stick.update": "«%s» cambió a %s", "item.minecraft.diamond": "Diamante", "item.minecraft.diamond_axe": "<PERSON><PERSON>", "item.minecraft.diamond_boots": "Botas de diamante", "item.minecraft.diamond_chestplate": "Pechera de diamante", "item.minecraft.diamond_helmet": "Casco de diamante", "item.minecraft.diamond_hoe": "Azada de diamante", "item.minecraft.diamond_horse_armor": "Armadura de diamante para caballo", "item.minecraft.diamond_leggings": "Pantalones de diamante", "item.minecraft.diamond_pickaxe": "Pico de di<PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON>", "item.minecraft.diamond_sword": "Espada de diamante", "item.minecraft.disc_fragment_5": "Fragmento de disco", "item.minecraft.disc_fragment_5.desc": "Disco - 5", "item.minecraft.dolphin_spawn_egg": "<PERSON><PERSON>", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON> burro", "item.minecraft.dragon_breath": "<PERSON><PERSON>", "item.minecraft.dried_kelp": "Algas secas", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON>", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.echo_shard": "Fragmento de eco", "item.minecraft.egg": "Huevo", "item.minecraft.elder_guardian_spawn_egg": "<PERSON><PERSON> <PERSON>", "item.minecraft.elytra": "Elytra", "item.minecraft.emerald": "Esm<PERSON><PERSON>", "item.minecraft.enchanted_book": "Libro encantado", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON><PERSON> encantada", "item.minecraft.end_crystal": "Cristal del End", "item.minecraft.ender_dragon_spawn_egg": "<PERSON><PERSON>", "item.minecraft.ender_eye": "<PERSON><PERSON> de ender", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON> enderman", "item.minecraft.endermite_spawn_egg": "Generar endermite", "item.minecraft.evoker_spawn_egg": "Generar evocador", "item.minecraft.experience_bottle": "Botella de experiencia", "item.minecraft.explorer_pottery_shard": "Fragmento de cerámica de explorador", "item.minecraft.explorer_pottery_sherd": "Fragmento de cerámica de explorador", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.feather": "<PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "Ojo de araña fermentado", "item.minecraft.field_masoned_banner_pattern": "Estampado de mazonado", "item.minecraft.filled_map": "Mapa", "item.minecraft.fire_charge": "Carga de fuego", "item.minecraft.firework_rocket": "Cohete de fuegos artificiales", "item.minecraft.firework_rocket.flight": "Duración del vuelo:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Pólvora de fuegos artificiales", "item.minecraft.firework_star.black": "negra", "item.minecraft.firework_star.blue": "azul", "item.minecraft.firework_star.brown": "marr<PERSON>", "item.minecraft.firework_star.custom_color": "Personalizado", "item.minecraft.firework_star.cyan": "cian", "item.minecraft.firework_star.fade_to": "Decolora a", "item.minecraft.firework_star.flicker": " con chisporroteo.", "item.minecraft.firework_star.gray": "gris", "item.minecraft.firework_star.green": "verde", "item.minecraft.firework_star.light_blue": "celeste", "item.minecraft.firework_star.light_gray": "gris claro", "item.minecraft.firework_star.lime": "verde lima", "item.minecraft.firework_star.magenta": "magenta", "item.minecraft.firework_star.orange": "naran<PERSON>", "item.minecraft.firework_star.pink": "rosa", "item.minecraft.firework_star.purple": "violeta", "item.minecraft.firework_star.red": "roja", "item.minecraft.firework_star.shape": "Forma desconocida", "item.minecraft.firework_star.shape.burst": "Explosión", "item.minecraft.firework_star.shape.creeper": "Con forma de creeper", "item.minecraft.firework_star.shape.large_ball": "Esfera grande", "item.minecraft.firework_star.shape.small_ball": "Esfera pequeña", "item.minecraft.firework_star.shape.star": "Con forma de estrella", "item.minecraft.firework_star.trail": " con rastro.", "item.minecraft.firework_star.white": "blanca", "item.minecraft.firework_star.yellow": "amarilla", "item.minecraft.fishing_rod": "Caña de pescar", "item.minecraft.flint": "Pedernal", "item.minecraft.flint_and_steel": "Encendedor", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.flow_armor_trim_smithing_template.new": "Adorno de es<PERSON>ral", "item.minecraft.flow_banner_pattern": "Diseño de estandarte", "item.minecraft.flow_banner_pattern.desc": "Estampado de espiral", "item.minecraft.flow_banner_pattern.new": "Estampado de espiral", "item.minecraft.flow_pottery_sherd": "Fragmento de cerámica de espiral", "item.minecraft.flower_banner_pattern": "Diseño de estandarte", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "Estampado de flor", "item.minecraft.flower_pot": "Mace<PERSON>", "item.minecraft.fox_spawn_egg": "<PERSON><PERSON>", "item.minecraft.friend_pottery_shard": "Fragmento de cerámica de amistad", "item.minecraft.friend_pottery_sherd": "Fragmento de cerámica de amistad", "item.minecraft.frog_spawn_egg": "<PERSON><PERSON> rana", "item.minecraft.furnace_minecart": "<PERSON><PERSON> con horno", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON> ghast", "item.minecraft.ghast_tear": "Lágrim<PERSON>", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "Pedazo de sandía brillante", "item.minecraft.globe_banner_pattern": "Diseño de estandarte", "item.minecraft.globe_banner_pattern.desc": "Planeta", "item.minecraft.globe_banner_pattern.new": "Estampado de planeta", "item.minecraft.glow_berries": "Bayas luminosas", "item.minecraft.glow_ink_sac": "Saco de tinta luminoso", "item.minecraft.glow_item_frame": "<PERSON> l<PERSON>", "item.minecraft.glow_squid_spawn_egg": "Generar calamar luminoso", "item.minecraft.glowstone_dust": "Polvo de piedra luminosa", "item.minecraft.goat_horn": "Cuerno de cabra", "item.minecraft.goat_spawn_egg": "Generar cabra", "item.minecraft.gold_ingot": "Lingote de oro", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON> de oro", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "Hacha de oro", "item.minecraft.golden_boots": "Botas de oro", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "Pechera de oro", "item.minecraft.golden_helmet": "Casco de oro", "item.minecraft.golden_hoe": "Azada de oro", "item.minecraft.golden_horse_armor": "Armadura de oro para caballo", "item.minecraft.golden_leggings": "Pantalones de oro", "item.minecraft.golden_pickaxe": "Pico de oro", "item.minecraft.golden_shovel": "Pala de oro", "item.minecraft.golden_sword": "Espada de oro", "item.minecraft.gray_bundle": "Bolsa gris", "item.minecraft.gray_dye": "Tinte gris", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.green_bundle": "Bolsa verde", "item.minecraft.green_dye": "Tinte verde", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON> verde", "item.minecraft.guardian_spawn_egg": "<PERSON><PERSON>", "item.minecraft.gunpowder": "Pólvora", "item.minecraft.guster_banner_pattern": "Diseño de estandarte", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "Estampado de breeze", "item.minecraft.guster_pottery_sherd": "Fragmento de cerámica de breeze", "item.minecraft.happy_ghast_spawn_egg": "<PERSON><PERSON> ghast feliz", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Corazón del mar", "item.minecraft.heart_pottery_shard": "Fragmento de cerámica de corazón", "item.minecraft.heart_pottery_sherd": "Fragmento de cerámica de corazón", "item.minecraft.heartbreak_pottery_shard": "Fragmento de cerámica de corazón roto", "item.minecraft.heartbreak_pottery_sherd": "Fragmento de cerámica de corazón roto", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON> ho<PERSON>n", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.honeycomb": "Panal", "item.minecraft.hopper_minecart": "Carrito con tolva", "item.minecraft.horse_spawn_egg": "<PERSON><PERSON> caballo", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.host_armor_trim_smithing_template.new": "Adorno de anfitrión", "item.minecraft.howl_pottery_shard": "Fragmento de cerámica de lobo", "item.minecraft.howl_pottery_sherd": "Fragmento de cerámica de lobo", "item.minecraft.husk_spawn_egg": "<PERSON><PERSON> (Husk)", "item.minecraft.ink_sac": "Saco de tinta", "item.minecraft.iron_axe": "<PERSON><PERSON> <PERSON>", "item.minecraft.iron_boots": "<PERSON><PERSON>ro", "item.minecraft.iron_chestplate": "Pechera de hierro", "item.minecraft.iron_golem_spawn_egg": "Generar gó<PERSON>", "item.minecraft.iron_helmet": "<PERSON><PERSON>", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "Armadura de hierro para caballo", "item.minecraft.iron_ingot": "Ling<PERSON> de hierro", "item.minecraft.iron_leggings": "Pantalones de hierro", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "<PERSON><PERSON>", "item.minecraft.iron_shovel": "<PERSON><PERSON>", "item.minecraft.iron_sword": "E<PERSON><PERSON> de <PERSON>", "item.minecraft.item_frame": "<PERSON>", "item.minecraft.jungle_boat": "<PERSON><PERSON>", "item.minecraft.jungle_chest_boat": "<PERSON><PERSON> de jungla con baúl", "item.minecraft.knowledge_book": "Enciclopedia", "item.minecraft.lapis_lazuli": "La<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Balde de lava", "item.minecraft.lead": "Rienda", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "Botas de cuero", "item.minecraft.leather_chestplate": "Túnica de cuero", "item.minecraft.leather_helmet": "<PERSON><PERSON>", "item.minecraft.leather_horse_armor": "Armadura de cuero para caballo", "item.minecraft.leather_leggings": "Pantalones de cuero", "item.minecraft.light_blue_bundle": "Bolsa celeste", "item.minecraft.light_blue_dye": "<PERSON><PERSON> celeste", "item.minecraft.light_blue_harness": "<PERSON><PERSON><PERSON> c<PERSON>", "item.minecraft.light_gray_bundle": "Bolsa gris claro", "item.minecraft.light_gray_dye": "<PERSON>te gris claro", "item.minecraft.light_gray_harness": "<PERSON><PERSON><PERSON> gris claro", "item.minecraft.lime_bundle": "Bolsa verde lima", "item.minecraft.lime_dye": "Tinte verde lima", "item.minecraft.lime_harness": "Arnés verde lima", "item.minecraft.lingering_potion": "Poción persistente", "item.minecraft.lingering_potion.effect.awkward": "Poción persistente rara", "item.minecraft.lingering_potion.effect.empty": "Poción persistente no fabricable", "item.minecraft.lingering_potion.effect.fire_resistance": "Poción persistente de resistencia al fuego", "item.minecraft.lingering_potion.effect.harming": "Poción persistente de daño", "item.minecraft.lingering_potion.effect.healing": "Poción persistente de curación", "item.minecraft.lingering_potion.effect.infested": "Poción persistente de infestación", "item.minecraft.lingering_potion.effect.invisibility": "Poción persistente de invisibilidad", "item.minecraft.lingering_potion.effect.leaping": "Poción persistente de salto", "item.minecraft.lingering_potion.effect.levitation": "Poción persistente de levitación", "item.minecraft.lingering_potion.effect.luck": "Poción persistente de suerte", "item.minecraft.lingering_potion.effect.mundane": "Poción persistente mundana", "item.minecraft.lingering_potion.effect.night_vision": "Poción persistente de visión nocturna", "item.minecraft.lingering_potion.effect.oozing": "Poción persistente de mucosidad", "item.minecraft.lingering_potion.effect.poison": "Poción persistente de veneno", "item.minecraft.lingering_potion.effect.regeneration": "Poción persistente de regeneración", "item.minecraft.lingering_potion.effect.slow_falling": "Poción persistente de caída lenta", "item.minecraft.lingering_potion.effect.slowness": "Poción persistente de lentitud", "item.minecraft.lingering_potion.effect.strength": "Poción persistente de fuerza", "item.minecraft.lingering_potion.effect.swiftness": "Poción persistente de velocidad", "item.minecraft.lingering_potion.effect.thick": "Poción persistente espesa", "item.minecraft.lingering_potion.effect.turtle_master": "Poción persistente del maestro tortuga", "item.minecraft.lingering_potion.effect.water": "Botella de agua persistente", "item.minecraft.lingering_potion.effect.water_breathing": "Poción persistente de respiración acuática", "item.minecraft.lingering_potion.effect.weakness": "Poción persistente de debilidad", "item.minecraft.lingering_potion.effect.weaving": "Poción persistente de tejedura", "item.minecraft.lingering_potion.effect.wind_charged": "Poción persistente de carga ventosa", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON> llama", "item.minecraft.lodestone_compass": "Brújula magnetizada", "item.minecraft.mace": "<PERSON><PERSON>", "item.minecraft.magenta_bundle": "Bolsa magenta", "item.minecraft.magenta_dye": "Tinte magenta", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON> magenta", "item.minecraft.magma_cream": "Crema de magma", "item.minecraft.magma_cube_spawn_egg": "Generar cubo de magma", "item.minecraft.mangrove_boat": "<PERSON><PERSON> de mangle", "item.minecraft.mangrove_chest_boat": "Bote de mangle con baúl", "item.minecraft.map": "Mapa en blanco", "item.minecraft.melon_seeds": "Semillas de sandía", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON>", "item.minecraft.milk_bucket": "Balde de leche", "item.minecraft.minecart": "<PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "Fragmento de cerámica de minero", "item.minecraft.miner_pottery_sherd": "Fragmento de cerámica de minero", "item.minecraft.mojang_banner_pattern": "Diseño de estandarte", "item.minecraft.mojang_banner_pattern.desc": "Logotipo", "item.minecraft.mojang_banner_pattern.new": "Estampado de Mojang", "item.minecraft.mooshroom_spawn_egg": "<PERSON><PERSON> mooshroom", "item.minecraft.mourner_pottery_shard": "Fragmento de cerámica de luto", "item.minecraft.mourner_pottery_sherd": "Fragmento de cerámica de doliente", "item.minecraft.mule_spawn_egg": "<PERSON>rar mula", "item.minecraft.mushroom_stew": "<PERSON><PERSON><PERSON> de hongo<PERSON>", "item.minecraft.music_disc_11": "Disco", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Disco", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Disco", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Disco", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Disco", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Disco", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Disco", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Disco", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON> (caja de música)", "item.minecraft.music_disc_far": "Disco", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "disco de musica", "item.minecraft.music_disc_lava_chicken.desc": "Super pociones - Lava Gallina", "item.minecraft.music_disc_mall": "Disco", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Disco", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Disco", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Disco", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Disco", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Disco", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Disco", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Disco", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Disco", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Disco", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Disco", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Cordero crudo", "item.minecraft.name_tag": "Etiqueta", "item.minecraft.nautilus_shell": "Caparazón de nautilo", "item.minecraft.nether_brick": "Ladrillo del Nether", "item.minecraft.nether_star": "Estrella del Nether", "item.minecraft.nether_wart": "Verruga del Nether", "item.minecraft.netherite_axe": "<PERSON><PERSON> de netherita", "item.minecraft.netherite_boots": "Botas de netherita", "item.minecraft.netherite_chestplate": "Pechera de netherita", "item.minecraft.netherite_helmet": "Casco de netherita", "item.minecraft.netherite_hoe": "Azada de netherita", "item.minecraft.netherite_ingot": "Lingote de netherita", "item.minecraft.netherite_leggings": "Pantalones de netherita", "item.minecraft.netherite_pickaxe": "Pico de netherita", "item.minecraft.netherite_scrap": "Fragmento de netherita", "item.minecraft.netherite_shovel": "<PERSON>la de netherita", "item.minecraft.netherite_sword": "Espada de netherita", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.netherite_upgrade_smithing_template.new": "Me<PERSON>ra de netherita", "item.minecraft.oak_boat": "<PERSON><PERSON>", "item.minecraft.oak_chest_boat": "<PERSON>te de roble con baúl", "item.minecraft.ocelot_spawn_egg": "<PERSON><PERSON> o<PERSON>", "item.minecraft.ominous_bottle": "<PERSON><PERSON><PERSON> sin<PERSON>", "item.minecraft.ominous_trial_key": "Llave de desafío siniestra", "item.minecraft.orange_bundle": "<PERSON><PERSON><PERSON> naranja", "item.minecraft.orange_dye": "<PERSON><PERSON>", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.painting": "Cuadro", "item.minecraft.pale_oak_boat": "<PERSON><PERSON> de roble p<PERSON>", "item.minecraft.pale_oak_chest_boat": "Bote de roble pálido con baúl", "item.minecraft.panda_spawn_egg": "<PERSON>rar panda", "item.minecraft.paper": "Papel", "item.minecraft.parrot_spawn_egg": "<PERSON><PERSON> loro", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON> de phantom", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON> phantom", "item.minecraft.pig_spawn_egg": "<PERSON><PERSON> chancho", "item.minecraft.piglin_banner_pattern": "Diseño de estandarte", "item.minecraft.piglin_banner_pattern.desc": "Hocico", "item.minecraft.piglin_banner_pattern.new": "Estampado de hocico", "item.minecraft.piglin_brute_spawn_egg": "<PERSON><PERSON> piglin bruto", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON> piglin", "item.minecraft.pillager_spawn_egg": "Generar saqueador", "item.minecraft.pink_bundle": "Bolsa rosa", "item.minecraft.pink_dye": "<PERSON>te rosa", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON> rosa", "item.minecraft.pitcher_plant": "<PERSON>a jarra", "item.minecraft.pitcher_pod": "Vaina de planta jarra", "item.minecraft.plenty_pottery_shard": "Fragmento de cerámica de baúl", "item.minecraft.plenty_pottery_sherd": "Fragmento de cerámica de riqueza", "item.minecraft.poisonous_potato": "<PERSON>", "item.minecraft.polar_bear_spawn_egg": "Generar oso polar", "item.minecraft.popped_chorus_fruit": "<PERSON>uta de chorus horneada", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON> cruda", "item.minecraft.potato": "<PERSON>", "item.minecraft.potion": "Poción", "item.minecraft.potion.effect.awkward": "Poción rara", "item.minecraft.potion.effect.empty": "Poción no fabricable", "item.minecraft.potion.effect.fire_resistance": "Poción de resistencia al fuego", "item.minecraft.potion.effect.harming": "Poción de daño", "item.minecraft.potion.effect.healing": "Poción de curación", "item.minecraft.potion.effect.infested": "Poción de infestación", "item.minecraft.potion.effect.invisibility": "Poción de invisibilidad", "item.minecraft.potion.effect.leaping": "Poción de salto", "item.minecraft.potion.effect.levitation": "Poción de levitación", "item.minecraft.potion.effect.luck": "Poción de suerte", "item.minecraft.potion.effect.mundane": "Poción mundana", "item.minecraft.potion.effect.night_vision": "Poción de visión nocturna", "item.minecraft.potion.effect.oozing": "Poción de mucosidad", "item.minecraft.potion.effect.poison": "Poción de veneno", "item.minecraft.potion.effect.regeneration": "Poción de regeneración", "item.minecraft.potion.effect.slow_falling": "Poción de caída lenta", "item.minecraft.potion.effect.slowness": "Poción de lentitud", "item.minecraft.potion.effect.strength": "Poción de fuerza", "item.minecraft.potion.effect.swiftness": "Poción de velocidad", "item.minecraft.potion.effect.thick": "Poción espesa", "item.minecraft.potion.effect.turtle_master": "Poción del maestro tortuga", "item.minecraft.potion.effect.water": "Botella de agua", "item.minecraft.potion.effect.water_breathing": "Poción de respiración acuática", "item.minecraft.potion.effect.weakness": "Poción de debilidad", "item.minecraft.potion.effect.weaving": "Poción de tejedura", "item.minecraft.potion.effect.wind_charged": "Poción de carga ventosa", "item.minecraft.pottery_shard_archer": "Fragmento de cerámica de arquero", "item.minecraft.pottery_shard_arms_up": "Fragmento de cerámica de brazos arriba", "item.minecraft.pottery_shard_prize": "Fragmento de cerámica de joya", "item.minecraft.pottery_shard_skull": "Fragmento de cerámica de calavera", "item.minecraft.powder_snow_bucket": "Balde de nieve polvo", "item.minecraft.prismarine_crystals": "Cristales de prismarina", "item.minecraft.prismarine_shard": "Fragmento de prismarina", "item.minecraft.prize_pottery_shard": "Fragmento de cerámica de joya", "item.minecraft.prize_pottery_sherd": "Fragmento de cerámica de tesoro", "item.minecraft.pufferfish": "Pez globo", "item.minecraft.pufferfish_bucket": "Balde con pez globo", "item.minecraft.pufferfish_spawn_egg": "Generar pez globo", "item.minecraft.pumpkin_pie": "Torta de z<PERSON>llo", "item.minecraft.pumpkin_seeds": "Semillas de zapallo", "item.minecraft.purple_bundle": "<PERSON><PERSON>a violeta", "item.minecraft.purple_dye": "<PERSON>te violeta", "item.minecraft.purple_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.quartz": "Cuarz<PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON> crudo", "item.minecraft.rabbit_foot": "Pata de conejo", "item.minecraft.rabbit_hide": "<PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON>", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.raiser_armor_trim_smithing_template.new": "Adorno de elevación", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON>", "item.minecraft.raw_copper": "<PERSON>bre crudo", "item.minecraft.raw_gold": "Oro crudo", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON> crudo", "item.minecraft.recovery_compass": "Brújula de recuperación", "item.minecraft.red_bundle": "Bolsa roja", "item.minecraft.red_dye": "<PERSON><PERSON> rojo", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON> rojo", "item.minecraft.redstone": "Polvo de redstone", "item.minecraft.resin_brick": "<PERSON><PERSON><PERSON> resin<PERSON>", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON> de resina", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.rib_armor_trim_smithing_template.new": "Adorno de costillas", "item.minecraft.rotten_flesh": "<PERSON><PERSON>", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON><PERSON> crudo", "item.minecraft.salmon_bucket": "Balde con salmón", "item.minecraft.salmon_spawn_egg": "<PERSON><PERSON>", "item.minecraft.scrape_pottery_sherd": "Fragmento de cerámica de hacha", "item.minecraft.scute": "Escama", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.sentry_armor_trim_smithing_template.new": "Adorno de centinela", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.shaper_armor_trim_smithing_template.new": "Adorno de modelador", "item.minecraft.sheaf_pottery_shard": "Fragmento de cerámica de trigo", "item.minecraft.sheaf_pottery_sherd": "Fragmento de cerámica de trigo", "item.minecraft.shears": "Tijeras", "item.minecraft.sheep_spawn_egg": "Generar oveja", "item.minecraft.shelter_pottery_shard": "Fragmento de cerámica de refugio", "item.minecraft.shelter_pottery_sherd": "Fragmento de cerámica de refugio", "item.minecraft.shield": "Escudo", "item.minecraft.shield.black": "Escudo negro", "item.minecraft.shield.blue": "Escudo a<PERSON>l", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON><PERSON> cian", "item.minecraft.shield.gray": "Escudo gris", "item.minecraft.shield.green": "Escudo verde", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON><PERSON> celeste", "item.minecraft.shield.light_gray": "Escudo gris claro", "item.minecraft.shield.lime": "Escudo verde lima", "item.minecraft.shield.magenta": "Escudo magenta", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON><PERSON> rosa", "item.minecraft.shield.purple": "<PERSON>scu<PERSON> violeta", "item.minecraft.shield.red": "Escudo rojo", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON><PERSON> blanco", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_shell": "Caparazón de <PERSON>ker", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON>", "item.minecraft.sign": "Cartel", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.silence_armor_trim_smithing_template.new": "Adorno de silencio", "item.minecraft.silverfish_spawn_egg": "Generar pez de plata", "item.minecraft.skeleton_horse_spawn_egg": "<PERSON><PERSON> caballo esqueleto", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON> es<PERSON>", "item.minecraft.skull_banner_pattern": "Diseño de estandarte", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "Estampado de calavera", "item.minecraft.skull_pottery_shard": "Fragmento de cerámica de calavera", "item.minecraft.skull_pottery_sherd": "Fragmento de cerámica de calavera", "item.minecraft.slime_ball": "Bola de slime", "item.minecraft.slime_spawn_egg": "Generar slime", "item.minecraft.smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.smithing_template.applies_to": "Se aplica a:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Colocá un lingote o cristal", "item.minecraft.smithing_template.armor_trim.applies_to": "Armad<PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Colocá una parte de armadura", "item.minecraft.smithing_template.armor_trim.ingredients": "Lingotes y cristales", "item.minecraft.smithing_template.ingredients": "Ingredientes:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Colocá un lingote de netherita", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Equipamiento de diamante", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Colocá una armadura, arma o herramienta de diamante", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Lingote de netherita", "item.minecraft.smithing_template.upgrade": "Mejora: ", "item.minecraft.sniffer_spawn_egg": "<PERSON><PERSON> sniffer", "item.minecraft.snort_pottery_shard": "Fragmento de cerámica de sniffer", "item.minecraft.snort_pottery_sherd": "Fragmento de cerámica de sniffer", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.snout_armor_trim_smithing_template.new": "Adorno de hocico", "item.minecraft.snow_golem_spawn_egg": "Generar gólem de nieve", "item.minecraft.snowball": "<PERSON><PERSON> de <PERSON>eve", "item.minecraft.spectral_arrow": "Flecha espectral", "item.minecraft.spider_eye": "<PERSON><PERSON> de a<PERSON>ña", "item.minecraft.spider_spawn_egg": "<PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.spire_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion": "Poción arrojadiza", "item.minecraft.splash_potion.effect.awkward": "Poción arrojadiza rara", "item.minecraft.splash_potion.effect.empty": "Poción arrojadiza no fabricable", "item.minecraft.splash_potion.effect.fire_resistance": "Poción arrojadiza de resistencia al fuego", "item.minecraft.splash_potion.effect.harming": "Poción arrojadiza de <PERSON>ño", "item.minecraft.splash_potion.effect.healing": "Poción arrojadiza de curación", "item.minecraft.splash_potion.effect.infested": "Poción arrojadiza de infestación", "item.minecraft.splash_potion.effect.invisibility": "Poción arrojadiza de invisibilidad", "item.minecraft.splash_potion.effect.leaping": "Poción arrojadiza de salto", "item.minecraft.splash_potion.effect.levitation": "Poción arrojadiza de levitación", "item.minecraft.splash_potion.effect.luck": "Poción arrojadiza de suerte", "item.minecraft.splash_potion.effect.mundane": "Poción arrojadiza mundana", "item.minecraft.splash_potion.effect.night_vision": "Poción arrojadiza de visión nocturna", "item.minecraft.splash_potion.effect.oozing": "Poción arrojadiza de mucosidad", "item.minecraft.splash_potion.effect.poison": "Poción arrojadiza de veneno", "item.minecraft.splash_potion.effect.regeneration": "Poción arrojadiza de regeneración", "item.minecraft.splash_potion.effect.slow_falling": "Poción arrojadiza de caída lenta", "item.minecraft.splash_potion.effect.slowness": "Poción arrojadiza de lentitud", "item.minecraft.splash_potion.effect.strength": "Poción arrojadiza de fuerza", "item.minecraft.splash_potion.effect.swiftness": "Poción arrojadiza de velocidad", "item.minecraft.splash_potion.effect.thick": "Poción arrojadiza espesa", "item.minecraft.splash_potion.effect.turtle_master": "Poción arrojadiza del maestro tortuga", "item.minecraft.splash_potion.effect.water": "Botella de agua arrojadiza", "item.minecraft.splash_potion.effect.water_breathing": "Poción arrojadiza de respiración acuática", "item.minecraft.splash_potion.effect.weakness": "Poción arrojadiza de debilidad", "item.minecraft.splash_potion.effect.weaving": "Poción arrojadiza de tejedura", "item.minecraft.splash_potion.effect.wind_charged": "Poción arrojadiza de carga ventosa", "item.minecraft.spruce_boat": "<PERSON><PERSON> de pino", "item.minecraft.spruce_chest_boat": "Bote de abeto con baúl", "item.minecraft.spyglass": "Catalejo", "item.minecraft.squid_spawn_egg": "<PERSON><PERSON> calamar", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON><PERSON> de piedra", "item.minecraft.stone_hoe": "Azada de piedra", "item.minecraft.stone_pickaxe": "Pico de piedra", "item.minecraft.stone_shovel": "Pala de piedra", "item.minecraft.stone_sword": "Espada de piedra", "item.minecraft.stray_spawn_egg": "Generar esqueleto glacial (Stray)", "item.minecraft.strider_spawn_egg": "Generar strider", "item.minecraft.string": "<PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON><PERSON><PERSON>", "item.minecraft.sweet_berries": "Bayas dulces", "item.minecraft.tadpole_bucket": "Balde con renacuajo", "item.minecraft.tadpole_spawn_egg": "<PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.tide_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> de <PERSON>", "item.minecraft.tipped_arrow": "Flecha con efecto", "item.minecraft.tipped_arrow.effect.awkward": "Flecha con efecto", "item.minecraft.tipped_arrow.effect.empty": "Flecha con efecto no fabricable", "item.minecraft.tipped_arrow.effect.fire_resistance": "Flecha de resistencia al fuego", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.healing": "Flecha de curación", "item.minecraft.tipped_arrow.effect.infested": "Flecha de infestación", "item.minecraft.tipped_arrow.effect.invisibility": "Flecha de invisibilidad", "item.minecraft.tipped_arrow.effect.leaping": "Flecha de salto", "item.minecraft.tipped_arrow.effect.levitation": "Flecha de levitación", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.mundane": "Flecha con efecto", "item.minecraft.tipped_arrow.effect.night_vision": "Flecha de visión nocturna", "item.minecraft.tipped_arrow.effect.oozing": "Flecha de mucosidad", "item.minecraft.tipped_arrow.effect.poison": "Flecha de veneno", "item.minecraft.tipped_arrow.effect.regeneration": "Flecha de regeneración", "item.minecraft.tipped_arrow.effect.slow_falling": "Flecha de caída lenta", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.strength": "Flecha de fuerza", "item.minecraft.tipped_arrow.effect.swiftness": "Flecha de velocidad", "item.minecraft.tipped_arrow.effect.thick": "Flecha con efecto", "item.minecraft.tipped_arrow.effect.turtle_master": "Flecha del maestro tortuga", "item.minecraft.tipped_arrow.effect.water": "Flecha con salpicadura", "item.minecraft.tipped_arrow.effect.water_breathing": "Flecha de respiración acuática", "item.minecraft.tipped_arrow.effect.weakness": "Flecha de debilidad", "item.minecraft.tipped_arrow.effect.weaving": "Flecha de tejedura", "item.minecraft.tipped_arrow.effect.wind_charged": "Flecha de carga ventosa", "item.minecraft.tnt_minecart": "Carrito con TNT", "item.minecraft.torchflower_seeds": "Semillas de anflorcha", "item.minecraft.totem_of_undying": "Tótem de inmortalidad", "item.minecraft.trader_llama_spawn_egg": "<PERSON><PERSON> llama de comerciante", "item.minecraft.trial_key": "Llave de desafío", "item.minecraft.trident": "Tridente", "item.minecraft.tropical_fish": "Pez tropical", "item.minecraft.tropical_fish_bucket": "Balde con pez tropical", "item.minecraft.tropical_fish_spawn_egg": "Generar pez tropical", "item.minecraft.turtle_helmet": "Caparazón de tortuga", "item.minecraft.turtle_scute": "Escama de tortuga", "item.minecraft.turtle_spawn_egg": "<PERSON><PERSON> tortuga", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.vex_armor_trim_smithing_template.new": "Adorno de vex", "item.minecraft.vex_spawn_egg": "Generar vex", "item.minecraft.villager_spawn_egg": "<PERSON><PERSON>", "item.minecraft.vindicator_spawn_egg": "Generar vindicador", "item.minecraft.wandering_trader_spawn_egg": "Generar comerciante nómada", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> de warden", "item.minecraft.warden_spawn_egg": "Generar warden", "item.minecraft.warped_fungus_on_a_stick": "Caña con hongo distorsionado", "item.minecraft.water_bucket": "Balde de agua", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Adorno de guía", "item.minecraft.wheat": "Trigo", "item.minecraft.wheat_seeds": "Semillas de trigo", "item.minecraft.white_bundle": "Bolsa blanca", "item.minecraft.white_dye": "<PERSON>te blanco", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON> blan<PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.wild_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.wind_charge": "Carga ventosa", "item.minecraft.witch_spawn_egg": "<PERSON><PERSON> bruja", "item.minecraft.wither_skeleton_spawn_egg": "Generar es<PERSON>", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON>", "item.minecraft.wolf_armor": "Armadura para lobo", "item.minecraft.wolf_spawn_egg": "Generar lobo", "item.minecraft.wooden_axe": "<PERSON><PERSON>", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "Pico <PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON>", "item.minecraft.wooden_sword": "Espada de madera", "item.minecraft.writable_book": "Libro y pluma", "item.minecraft.written_book": "Libro escrito", "item.minecraft.yellow_bundle": "<PERSON><PERSON><PERSON> amarilla", "item.minecraft.yellow_dye": "<PERSON><PERSON>", "item.minecraft.yellow_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON>lin", "item.minecraft.zombie_horse_spawn_egg": "<PERSON><PERSON> caballo zombi", "item.minecraft.zombie_spawn_egg": "<PERSON><PERSON> z<PERSON>", "item.minecraft.zombie_villager_spawn_egg": "<PERSON><PERSON> al<PERSON> z<PERSON>i", "item.minecraft.zombified_piglin_spawn_egg": "Generar piglin zombificado", "item.modifiers.any": "Al equipar:", "item.modifiers.armor": "En el cuerpo:", "item.modifiers.body": "Al equipar:", "item.modifiers.chest": "En el cuerpo:", "item.modifiers.feet": "En los pies:", "item.modifiers.hand": "En la mano:", "item.modifiers.head": "En la cabeza:", "item.modifiers.legs": "En las piernas:", "item.modifiers.mainhand": "En la mano principal:", "item.modifiers.offhand": "En la mano secundaria:", "item.modifiers.saddle": "Al tener montura:", "item.nbt_tags": "NBT: %s etiqueta(s)", "item.op_block_warning.line1": "Advertencia:", "item.op_block_warning.line2": "Se podrían ejecutar comandos al usar este bloque", "item.op_block_warning.line3": "¡No lo uses hasta saber el contenido exacto!", "item.unbreakable": "Irrompible", "itemGroup.buildingBlocks": "Construcción", "itemGroup.coloredBlocks": "Bloques de colores", "itemGroup.combat": "Combate", "itemGroup.consumables": "Consumibles", "itemGroup.crafting": "Fabricación", "itemGroup.foodAndDrink": "<PERSON><PERSON><PERSON> y bebidas", "itemGroup.functional": "Funcionalidad", "itemGroup.hotbar": "Barras de ítems guardadas", "itemGroup.ingredients": "Ingredientes", "itemGroup.inventory": "Inventario", "itemGroup.natural": "Naturaleza", "itemGroup.op": "Herramientas del administrador", "itemGroup.redstone": "Redstone", "itemGroup.search": "Buscar ítems", "itemGroup.spawnEggs": "Huevos generadores", "itemGroup.tools": "Herramientas", "item_modifier.unknown": "Modificador de objeto desconocido: %s", "jigsaw_block.final_state": "Se convierte en:", "jigsaw_block.generate": "Generar", "jigsaw_block.joint.aligned": "<PERSON><PERSON>", "jigsaw_block.joint.rollable": "Rotable", "jigsaw_block.joint_label": "Tipo de unión:", "jigsaw_block.keep_jigsaws": "Guardar rompecab.", "jigsaw_block.levels": "Niveles: %s", "jigsaw_block.name": "Nombre:", "jigsaw_block.placement_priority": "Prioridad de colocación:", "jigsaw_block.placement_priority.tooltip": "Cuando el bloque rompecabezas se conecta a una pieza, este es el orden en el que la pieza se procesa para las conexiones en estructuras más anchas.\n\nLas piezas se procesan en orden descendente, teniendo en cuenta el orden de inserción.", "jigsaw_block.pool": "Grupo objetivo:", "jigsaw_block.selection_priority": "Prioridad de selección:", "jigsaw_block.selection_priority.tooltip": "Al procesar la pieza asociada a las conexiones, este es el orden en el que el bloque rompecabezas intenta conectar con su pieza de destino.\n\nLos bloques rompecabezas se procesan en orden descendente, con un orden de inserción aleatorio.", "jigsaw_block.target": "Nombre del objetivo:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON> (caja de música)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Progresos", "key.attack": "Atacar/Destruir", "key.back": "Caminar hacia atrás", "key.categories.creative": "Modo creativo", "key.categories.gameplay": "Acciones del juego", "key.categories.inventory": "Inventario", "key.categories.misc": "Varios", "key.categories.movement": "Acciones de movimiento", "key.categories.multiplayer": "Multijugador", "key.categories.ui": "Interfaz del juego", "key.chat": "<PERSON><PERSON><PERSON> el chat", "key.command": "<PERSON><PERSON><PERSON> comando en el chat", "key.drop": "<PERSON><PERSON><PERSON> <PERSON><PERSON> se<PERSON>", "key.forward": "Caminar hacia delante", "key.fullscreen": "Pantalla completa", "key.hotbar.1": "Acceso 1 de la barra de ítems", "key.hotbar.2": "Acceso 2 de la barra de ítems", "key.hotbar.3": "Acceso 3 de la barra de ítems", "key.hotbar.4": "Acceso 4 de la barra de ítems", "key.hotbar.5": "Acceso 5 de la barra de ítems", "key.hotbar.6": "Acceso 6 de la barra de ítems", "key.hotbar.7": "Acceso 7 de la barra de ítems", "key.hotbar.8": "Acceso 8 de la barra de ítems", "key.hotbar.9": "Acceso 9 de la barra de ítems", "key.inventory": "Abrir/Cerrar inventario", "key.jump": "Saltar", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Retroceso", "key.keyboard.caps.lock": "<PERSON><PERSON><PERSON>", "key.keyboard.comma": ",", "key.keyboard.delete": "Bo<PERSON>r", "key.keyboard.down": "<PERSON><PERSON>cha a<PERSON>", "key.keyboard.end": "Fin", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F18", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "<PERSON><PERSON>o", "key.keyboard.insert": "Insertar", "key.keyboard.keypad.0": "0 (TN)", "key.keyboard.keypad.1": "1 (TN)", "key.keyboard.keypad.2": "2 (TN)", "key.keyboard.keypad.3": "3 (TN)", "key.keyboard.keypad.4": "4 (TN)", "key.keyboard.keypad.5": "5 (TN)", "key.keyboard.keypad.6": "6 (TN)", "key.keyboard.keypad.7": "7 (TN)", "key.keyboard.keypad.8": "8 (TN)", "key.keyboard.keypad.9": "9 (TN)", "key.keyboard.keypad.add": "+ (TN)", "key.keyboard.keypad.decimal": ". (TN)", "key.keyboard.keypad.divide": "/ (TN)", "key.keyboard.keypad.enter": "Intro", "key.keyboard.keypad.equal": "= (TN)", "key.keyboard.keypad.multiply": "* (TN)", "key.keyboard.keypad.subtract": "- (TN)", "key.keyboard.left": "Flecha izda", "key.keyboard.left.alt": "Alt izdo.", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Control izdo.", "key.keyboard.left.shift": "Shift izdo.", "key.keyboard.left.win": "Windows izdo.", "key.keyboard.menu": "Menú", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Blo<PERSON>", "key.keyboard.page.down": "Av P<PERSON>g", "key.keyboard.page.up": "<PERSON>", "key.keyboard.pause": "Pausa", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON><PERSON><PERSON>", "key.keyboard.right": "Flecha dcha", "key.keyboard.right.alt": "Alt dcho.", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Control dcho.", "key.keyboard.right.shift": "Shift dcho.", "key.keyboard.right.win": "Windows dcho.", "key.keyboard.scroll.lock": "Bloq Despl", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Espacio", "key.keyboard.tab": "Tabulador", "key.keyboard.unknown": "<PERSON>", "key.keyboard.up": "Flecha arriba", "key.keyboard.world.1": "Macro 1", "key.keyboard.world.2": "Macro 2", "key.left": "Caminar hacia la izquierda", "key.loadToolbarActivator": "<PERSON>gar barra de í<PERSON>", "key.mouse": "Botón %1$s", "key.mouse.left": "Botón izdo.", "key.mouse.middle": "Botón central", "key.mouse.right": "Botón dcho.", "key.pickItem": "Copiar bloque", "key.playerlist": "Lista de jugadores", "key.quickActions": "Acciones rápidas", "key.right": "Caminar hacia la derecha", "key.saveToolbarActivator": "Guardar barra de ítems", "key.screenshot": "Tomar captura de pantalla", "key.smoothCamera": "Cambiar a cámara cinemática", "key.sneak": "Agacharse", "key.socialInteractions": "Pantalla de interacciones sociales", "key.spectatorOutlines": "Destacar j<PERSON> (espectadores)", "key.sprint": "<PERSON><PERSON>", "key.swapOffhand": "Cambiar objeto a la mano secundaria", "key.togglePerspective": "Cambiar perspectiva", "key.use": "Usar ítem/Colocar bloque", "known_server_link.announcements": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.community": "Comunidad", "known_server_link.community_guidelines": "Normas de la comunidad", "known_server_link.feedback": "Opiniones", "known_server_link.forums": "For<PERSON>", "known_server_link.news": "Novedades", "known_server_link.report_bug": "Informar un error del servidor", "known_server_link.status": "Estado", "known_server_link.support": "Soporte", "known_server_link.website": "Sitio web", "lanServer.otherPlayers": "Configuración para los otros jugadores", "lanServer.port": "Número de puerto", "lanServer.port.invalid": "Puerto inválido.\nDejá la caja de edición vacía o ingresá un número entre 1024 y 65535.", "lanServer.port.invalid.new": "Puerto inválido.\nDejá la caja de edición vacía o ingresá un número diferente entre %s y %s.", "lanServer.port.unavailable": "Puerto no disponible.\nDejá la caja de edición vacía o ingresá un número diferente entre 1024 y 65535.", "lanServer.port.unavailable.new": "Puerto no disponible.\nDejá la caja de edición vacía o ingresá un número diferente entre %s y %s.", "lanServer.scanning": "Buscando mundos en LAN", "lanServer.start": "Abrir mundo al LAN", "lanServer.title": "Mundo en LAN", "language.code": "spa_UY", "language.name": "Español", "language.region": "Uruguay", "lectern.take_book": "Agarrar libro", "loading.progress": "%s%%", "mco.account.privacy.info": "Leer más sobre Mojang y leyes de privacidad", "mco.account.privacy.info.button": "Más información sobre el RGPD", "mco.account.privacy.information": "Mojang implementa ciertos procedimientos para ayudar a proteger a los menores y su privacidad, que consisten en cumplir con la Ley de Protección de Privacidad Infantil en Internet (COPPA) y el Reglamento General de Protección de Datos (RGPD).\n\nTenés que tener consentimiento parental antes de poder tener acceso a tu cuenta de Realms.", "mco.account.privacyinfo": "Mojang implementa ciertos procedimientos para ayudar a proteger a los menores y su privacidad, que consisten en cumplir con la Ley de Protección de Privacidad Infantil en Internet (COPPA) y el Reglamento General de Protección de Datos (RGPD).\n\nTenés que contar con consentimiento parental antes de poder acceder a tu cuenta de Realms.\n\nSi tu cuenta de Minecraft es vieja (usás tu usuario para iniciar sesión), tenés que migrarla a Mojang para poder acceder a Realms.", "mco.account.update": "<PERSON><PERSON><PERSON> cuenta", "mco.activity.noactivity": "Sin actividad desde hace %s día(s)", "mco.activity.title": "Actividad de jugadores", "mco.backup.button.download": "Descargar la última", "mco.backup.button.reset": "Reiniciar mundo", "mco.backup.button.restore": "Restaurar", "mco.backup.button.upload": "Subir mundo", "mco.backup.changes.tooltip": "Cambios", "mco.backup.entry": "Copia de seguridad (%s)", "mco.backup.entry.description": "Descripción", "mco.backup.entry.enabledPack": "Paquete activado", "mco.backup.entry.gameDifficulty": "Dificultad", "mco.backup.entry.gameMode": "<PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Versión del servidor de juego", "mco.backup.entry.name": "Nombre", "mco.backup.entry.seed": "<PERSON><PERSON>", "mco.backup.entry.templateName": "Nombre de plantilla", "mco.backup.entry.undefined": "Cambio sin definir", "mco.backup.entry.uploaded": "Subido", "mco.backup.entry.worldType": "Tipo de mundo", "mco.backup.generate.world": "Generar mundo", "mco.backup.info.title": "Cambios de la última copia", "mco.backup.narration": "Copia desde %s", "mco.backup.nobackups": "Este Realm no tiene copias de seguridad.", "mco.backup.restoring": "Restaurando Realm", "mco.backup.unknown": "DESCONOCIDA", "mco.brokenworld.download": "<PERSON><PERSON><PERSON>", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "Por favor, restablecé el mundo o elegí otro.", "mco.brokenworld.message.line2": "También podés descargar el mundo al modo un jugador.", "mco.brokenworld.minigame.title": "Este minijuego ya no es compatible", "mco.brokenworld.nonowner.error": "Tenés que esperar hasta que el dueño reinicie el mundo", "mco.brokenworld.nonowner.title": "El mundo está desactualizado", "mco.brokenworld.play": "<PERSON><PERSON>", "mco.brokenworld.reset": "Reiniciar", "mco.brokenworld.title": "Tu mundo actual ya no es soportado", "mco.client.incompatible.msg.line1": "Tu cliente no es compatible con Minecraft Realms.", "mco.client.incompatible.msg.line2": "Por favor, usá la versión más reciente de Minecraft.", "mco.client.incompatible.msg.line3": "Realms no es compatible con snapshots.", "mco.client.incompatible.title": "¡Cliente incompatible!", "mco.client.outdated.stable.version": "La versión de tu cliente (%s) no es compatible con Realms.\n\nPor favor, actualizá a la versión más reciente de Minecraft.", "mco.client.unsupported.snapshot.version": "La versión de tu cliente (%s) no es compatible con Realms.\n\nRealms no está disponible en esta versión de prueba.", "mco.compatibility.downgrade": "Desactualizar", "mco.compatibility.downgrade.description": "Este mundo se jugó por última vez en la versión %s y la tuya es %s. Desactualizar un mundo a una versión anterior podría dañarlo. No podemos garantizarte que cargue o funcione.\n\nSe guardará una copia de seguridad de tu mundo en «Copias del mundo». Por favor, restaurá tu mundo si es necesario.", "mco.compatibility.incompatible.popup.title": "Versión incompatible", "mco.compatibility.incompatible.releaseType.popup.message": "El mundo al que te estás intentando unir es incompatible con tu versión del juego.", "mco.compatibility.incompatible.series.popup.message": "Este mundo se jugó por última vez en la versión %s y la tuya es %s.\n\nEstas series no son compatibles. Necesitas un mundo nuevo para poder jugar en esta versión.", "mco.compatibility.unverifiable.message": "No se pudo verificar la versión en la que se jugó este mundo por última vez. Si el mundo se actualiza o desactualiza, se creará automáticamente una copia de seguridad que se guardará en «Copias del mundo».", "mco.compatibility.unverifiable.title": "No se puede verificar la compatibilidad", "mco.compatibility.upgrade": "Actualizar", "mco.compatibility.upgrade.description": "Este mundo se jugó por última vez en la versión %s y la tuya es %s.\n\nSe guardará una copia de seguridad de tu mundo en «Copias del mundo». \n\nRestaurá tu mundo si es necesario.", "mco.compatibility.upgrade.friend.description": "Este mundo se jugó por última vez en la versión %s. Estás en la versión %s.\n\nSe guardará una copia de seguridad del mundo en «Copias del mundo».\n\nEl dueño del realm puede restaurar el mundo si es necesario.", "mco.compatibility.upgrade.title": "¿Seguro que querés actualizar tu mundo?", "mco.configure.current.minigame": "Actual", "mco.configure.world.activityfeed.disabled": "Desactivado temporalmente", "mco.configure.world.backup": "Copias del mundo", "mco.configure.world.buttons.activity": "Actividad de jugadores", "mco.configure.world.buttons.close": "Cerrar Realm temporalmente", "mco.configure.world.buttons.delete": "Bo<PERSON>r", "mco.configure.world.buttons.done": "Aceptar", "mco.configure.world.buttons.edit": "<PERSON>ar mundo", "mco.configure.world.buttons.invite": "<PERSON>vi<PERSON> jugador", "mco.configure.world.buttons.moreoptions": "Más opciones", "mco.configure.world.buttons.newworld": "Mundo nuevo", "mco.configure.world.buttons.open": "<PERSON>abrir <PERSON>", "mco.configure.world.buttons.options": "Ajustes del mundo", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Seleccionar región...", "mco.configure.world.buttons.resetworld": "Reiniciar mundo", "mco.configure.world.buttons.save": "Guardar", "mco.configure.world.buttons.settings": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.subscription": "Suscripción", "mco.configure.world.buttons.switchminigame": "Cambiar minijuego", "mco.configure.world.close.question.line1": "Podés cerrar temporalmente tu Realm, evitando que se unan otros jugadores. Abrilo de nuevo cuando lo tengas todo listo.\n\nHacer esto no cancelará tu suscripción de realms.", "mco.configure.world.close.question.line2": "¿Seguro que querés continuar?", "mco.configure.world.close.question.title": "¿Necesitas hacer cambios sin interrupciones?", "mco.configure.world.closing": "Cerrando el Realm temporalmente...", "mco.configure.world.commandBlocks": "Bloques de comandos", "mco.configure.world.delete.button": "Borrar Realm", "mco.configure.world.delete.question.line1": "Tu Realm va a ser borrado permanentemente", "mco.configure.world.delete.question.line2": "¿Seguro que querés continuar?", "mco.configure.world.description": "Descripción del Realm", "mco.configure.world.edit.slot.name": "Nombre del mundo", "mco.configure.world.edit.subscreen.adventuremap": "Algunas opciones se encuentran desactivadas porque el mundo actual es de aventura", "mco.configure.world.edit.subscreen.experience": "Algunas opciones se encuentran desactivadas porque el mundo actual es de experiencia", "mco.configure.world.edit.subscreen.inspiration": "Algunos ajustes no están disponibles porque el mundo actual es sólo una fuente de inspiración", "mco.configure.world.forceGameMode": "<PERSON><PERSON> modo de juego", "mco.configure.world.invite.narration": "Tenés %s invitación(es) nueva(s)", "mco.configure.world.invite.profile.name": "Nombre", "mco.configure.world.invited": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "Invitado (%s)", "mco.configure.world.invites.normal.tooltip": "Usuario normal", "mco.configure.world.invites.ops.tooltip": "Administrador(a)", "mco.configure.world.invites.remove.tooltip": "Bo<PERSON>r", "mco.configure.world.leave.question.line1": "Si te vas de este Realm no vas a poder verlo hasta que te inviten de nuevo", "mco.configure.world.leave.question.line2": "¿Seguro que querés continuar?", "mco.configure.world.loading": "Cargando Realm", "mco.configure.world.location": "Ubicación", "mco.configure.world.minigame": "Actual: %s", "mco.configure.world.name": "Nombre del Realm", "mco.configure.world.opening": "Abriendo el Realm...", "mco.configure.world.players.error": "No existe ningún jugador con ese nombre", "mco.configure.world.players.inviting": "Invitando jugador...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP (JcJ)", "mco.configure.world.region_preference": "Preferencia de región", "mco.configure.world.region_preference.title": "Selección de región", "mco.configure.world.reset.question.line1": "Tu mundo será regenerado y el actual se perderá", "mco.configure.world.reset.question.line2": "¿Seguro que querés continuar?", "mco.configure.world.resourcepack.question": "Necesitas un paquete de recursos personalizado para jugar en este Realm\n\n¿Querés descargarlo y jugar?", "mco.configure.world.resourcepack.question.line1": "Necesitás un paquete de recursos personalizado para jugar en este Realm", "mco.configure.world.resourcepack.question.line2": "¿Querés descargarlo e instalarlo automáticamente para jugar?", "mco.configure.world.restore.download.question.line1": "El mundo se descargará y será agregado a tus mundos de un jugador.", "mco.configure.world.restore.download.question.line2": "¿Querés continuar?", "mco.configure.world.restore.question.line1": "Tu mundo será restaurado a la fecha «%s» (%s)", "mco.configure.world.restore.question.line2": "¿Seguro que querés continuar?", "mco.configure.world.settings.expired": "No se puede editar los ajustes de un Realm caducado", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot": "Mundo %s", "mco.configure.world.slot.empty": "Vacío", "mco.configure.world.slot.switch.question.line1": "Tu Realm cambiará a otro mundo", "mco.configure.world.slot.switch.question.line2": "¿Seguro que querés continuar?", "mco.configure.world.slot.tooltip": "Cambiar a este mundo", "mco.configure.world.slot.tooltip.active": "Entrar", "mco.configure.world.slot.tooltip.minigame": "Cambiar a minijuego", "mco.configure.world.spawnAnimals": "Generar animales", "mco.configure.world.spawnMonsters": "<PERSON><PERSON>", "mco.configure.world.spawnNPCs": "Generar <PERSON>", "mco.configure.world.spawnProtection": "Protección de área inicial", "mco.configure.world.spawn_toggle.message": "Desactivar esta opción BORRARÁ TODAS las entidades de ese tipo", "mco.configure.world.spawn_toggle.message.npc": "Desactivar esta opción BORRARÁ TODAS las entidades de ese tipo, por ejemplo, los aldeanos", "mco.configure.world.spawn_toggle.title": "¡Atención!", "mco.configure.world.status": "Estado", "mco.configure.world.subscription.day": "día", "mco.configure.world.subscription.days": "días", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "Extender suscrip<PERSON>", "mco.configure.world.subscription.less_than_a_day": "Menos de un día", "mco.configure.world.subscription.month": "mes", "mco.configure.world.subscription.months": "meses", "mco.configure.world.subscription.recurring.daysleft": "Se renueva automáticamente en", "mco.configure.world.subscription.recurring.info": "Los cambios que le hagas a tu suscripción de Realms, como añadir tiempo a tu suscripción o desactivar la facturación recurrente, no tendrán efecto hasta la próxima fecha de facturación.", "mco.configure.world.subscription.remaining.days": "%1$s día(s)", "mco.configure.world.subscription.remaining.months": "%1$s mes(es)", "mco.configure.world.subscription.remaining.months.days": "%1$s mes(es), %2$s día(s)", "mco.configure.world.subscription.start": "Fecha de inicio", "mco.configure.world.subscription.tab": "Suscripción", "mco.configure.world.subscription.timeleft": "Tiempo restante", "mco.configure.world.subscription.title": "Tu suscripción", "mco.configure.world.subscription.unknown": "Desconocido", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON> mundo", "mco.configure.world.switch.slot.subtitle": "Este mundo está vacío. <PERSON>r favor, elegí como crearlo", "mco.configure.world.title": "Configurar Realm:", "mco.configure.world.uninvite.player": "¿Seguro que querés anular la invitación a %s?", "mco.configure.world.uninvite.question": "¿Seguro que querés cancelar la invitación de", "mco.configure.worlds.title": "Mundos", "mco.connect.authorizing": "Iniciando se<PERSON>...", "mco.connect.connecting": "Conectando al Realm...", "mco.connect.failed": "Error al conectar con el Realm", "mco.connect.region": "Región del servidor: %s", "mco.connect.success": "Aceptar", "mco.create.world": "<PERSON><PERSON><PERSON>", "mco.create.world.error": "¡Tenés que escribir un nombre!", "mco.create.world.failed": "¡Error al crear mundo!", "mco.create.world.reset.title": "Creando mundo...", "mco.create.world.skip": "<PERSON><PERSON><PERSON>", "mco.create.world.subtitle": "Opcionalmente, seleccioná un mundo para poner en tu nuevo Realm", "mco.create.world.wait": "Creando el Realm...", "mco.download.cancelled": "Descarga cancelada", "mco.download.confirmation.line1": "El mundo que vas a descargar supera los %s", "mco.download.confirmation.line2": "No vas a poder subir este mundo a tu Realm de nuevo", "mco.download.confirmation.oversized": "El mundo que intentas descargar supera los %s\n\nNo podrás volver a subir este mundo a tu Realm", "mco.download.done": "<PERSON><PERSON><PERSON> completada", "mco.download.downloading": "Descargando", "mco.download.extracting": "Extrayendo", "mco.download.failed": "<PERSON><PERSON><PERSON> fallida", "mco.download.percent": "%s %%", "mco.download.preparing": "Preparando <PERSON>", "mco.download.resourcePack.fail": "¡Error al descargar el paquete de recursos!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Descargando el ultimo mundo", "mco.error.invalid.session.message": "Por favor, probá tras reiniciar Minecraft", "mco.error.invalid.session.title": "La sesión no es válida", "mco.errorMessage.6001": "Cliente desactualizado", "mco.errorMessage.6002": "Términos de servicio no aceptados", "mco.errorMessage.6003": "Límite de descarga alcanzado", "mco.errorMessage.6004": "Límite de subida alcanzado", "mco.errorMessage.6005": "Mundo bloqueado", "mco.errorMessage.6006": "Mundo desactualizado", "mco.errorMessage.6007": "El usuario está en demasiados Realms", "mco.errorMessage.6008": "Nombre de Realm no válido", "mco.errorMessage.6009": "Descripción del Realm no válida", "mco.errorMessage.connectionFailure": "Se produjo un error. Por favor, volvé a probar más tarde.", "mco.errorMessage.generic": "<PERSON><PERSON><PERSON><PERSON> un error: ", "mco.errorMessage.initialize.failed": "Error al inicializar el Realm", "mco.errorMessage.noDetails": "No se proporcionaron detalles del error", "mco.errorMessage.realmsService": "Ocurrió un error (%s):", "mco.errorMessage.realmsService.configurationError": "Se produjo un error inesperado al modificar la configuración del mundo", "mco.errorMessage.realmsService.connectivity": "No se pudo conectar a Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "No se pudo comprobar la versión compatible, se obtuvo la respuesta: %s", "mco.errorMessage.retry": "Reintentar operación", "mco.errorMessage.serviceBusy": "El servicio de Realms no está disponible en estos momentos.\nPor favor, intentá conectarte otra vez dentro de unos minutos.", "mco.gui.button": "Botón", "mco.gui.ok": "Aceptar", "mco.info": "¡Info.!", "mco.invited.player.narration": "Se invitó a %s", "mco.invites.button.accept": "Aceptar", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "¡No hay invitaciones pendientes!", "mco.invites.pending": "¡Nuevas invitaciones!", "mco.invites.title": "Invitaciones pendientes", "mco.minigame.world.changeButton": "Seleccionar o<PERSON> mini<PERSON>", "mco.minigame.world.info.line1": "¡Esto va a reemplazar temporalmente tu mundo por un minijuego!", "mco.minigame.world.info.line2": "Después podés volver a tu mundo original sin perder nada.", "mco.minigame.world.noSelection": "Elegí un minijuego", "mco.minigame.world.restore": "Terminando minijuego...", "mco.minigame.world.restore.question.line1": "Tu Realm será restaurado al finalizar el minijuego.", "mco.minigame.world.restore.question.line2": "¿Seguro que querés continuar?", "mco.minigame.world.selected": "Minijuego seleccionado:", "mco.minigame.world.slot.screen.title": "Cambiando mundo...", "mco.minigame.world.startButton": "Cambiar", "mco.minigame.world.starting.screen.title": "Iniciando minijuego...", "mco.minigame.world.stopButton": "Terminar minijuego", "mco.minigame.world.switch.new": "¿Seleccionar otro minijuego?", "mco.minigame.world.switch.title": "Cambiar minijuego", "mco.minigame.world.title": "Cambiar realm a minijuego", "mco.news": "Novedades de Minecraft Realms", "mco.notification.dismiss": "Descar<PERSON>", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON> ahora", "mco.notification.transferSubscription.message": "Las suscripciones de Realms para Java se mudarán a la Microsoft Store. ¡No esperes a que tu suscripción termine!\nTransferí ahora y obtené 30 días de Realms gratis.\nPodes transferir tu suscripción desde tu perfil en minecraft.net.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON><PERSON> enlace", "mco.notification.visitUrl.message.default": "Por favor, entrá al siguiente enlace", "mco.onlinePlayers": "Jugadores en línea", "mco.play.button.realm.closed": "Realm cerrado", "mco.question": "Pregunta", "mco.reset.world.adventure": "Aventuras", "mco.reset.world.experience": "Experiencias", "mco.reset.world.generate": "Nuevo mundo", "mco.reset.world.inspiration": "Inspiración", "mco.reset.world.resetting.screen.title": "Reiniciando mundo...", "mco.reset.world.seed": "<PERSON><PERSON> (opcional)", "mco.reset.world.template": "Plantillas de mundo", "mco.reset.world.title": "Reiniciar mundo", "mco.reset.world.upload": "Subir mundo", "mco.reset.world.warning": "Esto va a reemplazar el mundo actual de tu Realm", "mco.selectServer.buy": "¡Comprar un Realm!", "mco.selectServer.close": "<PERSON><PERSON><PERSON>", "mco.selectServer.closed": "Realm cerrado", "mco.selectServer.closeserver": "Cerrar Realm", "mco.selectServer.configure": "Configurar realm", "mco.selectServer.configureRealm": "Configurar Realm", "mco.selectServer.create": "Crear Realm", "mco.selectServer.create.subtitle": "Elegí qué mundo poner en tu nuevo Realm", "mco.selectServer.expired": "Realm caducado", "mco.selectServer.expiredList": "Tu suscripción caducó", "mco.selectServer.expiredRenew": "<PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Suscribirse", "mco.selectServer.expiredTrial": "Prueba terminada", "mco.selectServer.expires.day": "Caduca en 1 día", "mco.selectServer.expires.days": "Caduca en %s días", "mco.selectServer.expires.soon": "Caduca pronto", "mco.selectServer.leave": "Abandonar Realm", "mco.selectServer.loading": "Cargando lista de Realms", "mco.selectServer.mapOnlySupportedForVersion": "Este mapa no es compatible con la %s", "mco.selectServer.minigame": "Minijuego:", "mco.selectServer.minigameName": "Minijuego: %s", "mco.selectServer.minigameNotSupportedInVersion": "No podés jugar este minijuego en %s", "mco.selectServer.noRealms": "Parece que no tenés ningún Realm. Agregá un Realm para jugar con tus amigos.", "mco.selectServer.note": "Nota:", "mco.selectServer.open": "Realm abierto", "mco.selectServer.openserver": "Abrir Realm", "mco.selectServer.play": "<PERSON><PERSON>", "mco.selectServer.popup": "Realms es una manera segura y simple de jugar Minecraft con hasta diez amigos a la vez. ¡Soporta minijuegos y mundos personalizados! Sólo tiene que pagar el dueño.", "mco.selectServer.purchase": "<PERSON><PERSON><PERSON>", "mco.selectServer.trial": "¡Probalo!", "mco.selectServer.uninitialized": "¡Hacé click para crear tu nuevo Realm!", "mco.snapshot.createSnapshotPopup.text": "Estás a punto de crear un Realm para versiones de prueba gratuito que estará vinculado a tu suscripción de Realms de pago. Este Realm en particular será accesible mientras la suscripción de pago esté activa. Tu Realm de pago no se verá afectado.", "mco.snapshot.createSnapshotPopup.title": "¿Crear un Realm para versiones de prueba?", "mco.snapshot.creating": "Creando Realm para versiones de prueba...", "mco.snapshot.description": "Vinculado a %s", "mco.snapshot.friendsRealm.downgrade": "Necesitas usar la versión %s para unirte a este Realm", "mco.snapshot.friendsRealm.upgrade": "Para jugar desde la versión en la que estás, %s deberá actualizar su Realm primero", "mco.snapshot.paired": "Este Realm para versiones de prueba está vinculado a %s", "mco.snapshot.parent.tooltip": "Cambiá a la última versión oficial de Minecraft para jugar en este Realm", "mco.snapshot.start": "Abrir Realm para versiones de prueba gratuito", "mco.snapshot.subscription.info": "Este es un Realm para versiones de prueba vinculado a la suscripción de tu realm «%s». Permanecerá activo mientras el Realm vinculado esté en funcionamiento.", "mco.snapshot.tooltip": "Probá los Realms para versiones de prueba para echar un vistazo a próximas versiones, que podrían incluir nuevas características y otros cambios.\n\nTus Realms normales permanecerán en la versión estable del juego.", "mco.snapshotRealmsPopup.message": "Realms ahora es compatible con las versiones de prueba desde la 23w41a. ¡Todas las suscripciones a Realms vienen con una versión de prueba de Realms gratuita, independiente a tu Realm de Java normal!", "mco.snapshotRealmsPopup.title": "Realms ahora es compatible con las versiones de prueba", "mco.snapshotRealmsPopup.urlText": "Más información", "mco.template.button.publisher": "Autor(a)", "mco.template.button.select": "Elegir", "mco.template.button.trailer": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.default.name": "Plantilla de mundo", "mco.template.info.tooltip": "Página web del creador", "mco.template.name": "Plantilla", "mco.template.select.failure": "No se pudo recuperar la lista de contenido para esta categoría.\nPor favor, comprobá tu conexión o volvé a probar después.", "mco.template.select.narrate.authors": "Autores o autoras: %s", "mco.template.select.narrate.version": "versión %s", "mco.template.select.none": "¡Uups! Parece que la categoría está vacía ahora mismo.\nPor favor, mirá después. O... si sos creador(a), podés\n%s.", "mco.template.select.none.linkTitle": "pensar en mandarnos una idea tuya", "mco.template.title": "Plantillas de mundos", "mco.template.title.minigame": "Minijuegos", "mco.template.trailer.tooltip": "<PERSON><PERSON><PERSON><PERSON>a", "mco.terms.buttons.agree": "Acepto", "mco.terms.buttons.disagree": "No acepto", "mco.terms.sentence.1": "Acepto los términos de servicio", "mco.terms.sentence.2": "de Minecraft Realms", "mco.terms.title": "Términos de servicio de Minecraft Realms", "mco.time.daysAgo": "Hace %1$s día(s)", "mco.time.hoursAgo": "Hace %1$s hora(s)", "mco.time.minutesAgo": "Hace %1$s minuto(s)", "mco.time.now": "ahora mismo", "mco.time.secondsAgo": "Hace %1$s segundo(s)", "mco.trial.message.line1": "¿Querés tener tu propio Realm?", "mco.trial.message.line2": "¡Hacé clic acá para más información!", "mco.upload.button.name": "Subir", "mco.upload.cancelled": "Subida cancelada", "mco.upload.close.failure": "No se pudo cerrar tu Realm. Por favor, volvé a probar más tarde", "mco.upload.done": "Subida realizada", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "¡Subida fallida! (%s)", "mco.upload.failed.too_big.description": "El mundo seleccionado es demasiado grande. El tamaño máximo permitido es de %s.", "mco.upload.failed.too_big.title": "El mundo es demasiado grande", "mco.upload.hardcore": "¡No se pueden subir mundos extremos!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Preparando tu mundo", "mco.upload.select.world.none": "¡No se encontraron mundos de un jugador!", "mco.upload.select.world.subtitle": "Por favor, elegí un mundo de un jugador para subir", "mco.upload.select.world.title": "Subir mundo", "mco.upload.size.failure.line1": "¡«%s» es demasiado grande!", "mco.upload.size.failure.line2": "%s, de un máximo permitido de %s.", "mco.upload.uploading": "Subiendo «%s»...", "mco.upload.verifying": "Verificando tu mundo", "mco.version": "Versión: %s", "mco.warning": "¡Atención!", "mco.worldSlot.minigame": "Minijuego", "menu.custom_options": "Opciones personalizadas...", "menu.custom_options.title": "Opciones personalizadas", "menu.custom_options.tooltip": "Nota: Las opciones personalizadas las proporcionan servidores o contenido de terceros.\n¡Usalas con cuidado!", "menu.custom_screen_info.button_narration": "Esto es una pantalla personalizada. Más información.", "menu.custom_screen_info.contents": "El contenido de esta pantalla está controlado por servidores y mapas de terceros que no son propiedad de Mojang Studios ni de Microsoft, ni están operados ni supervisados ​​por ellos.\n\n¡Atención! Mucho cuidado al abrir enlaces y nunca compartas tu información personal, incluyendo tus datos de inicio de sesión.\n\nSi esta pantalla te impide jugar, puedes desconectarte del servidor usando el botón de abajo.", "menu.custom_screen_info.disconnect": "Pantalla personalizada rechazada", "menu.custom_screen_info.title": "Acerca de las pantallas personalizadas", "menu.custom_screen_info.tooltip": "Esto es una pantalla personalizada. Hacé click para más información.", "menu.disconnect": "Desconectarte", "menu.feedback": "Danos tu opinión...", "menu.feedback.title": "Danos tu opinión", "menu.game": "Menú", "menu.modded": " (Con mods)", "menu.multiplayer": "Multijugador", "menu.online": "Minecraft Realms", "menu.options": "Opciones...", "menu.paused": "Juego en pausa", "menu.playdemo": "Jugar mundo de demostración", "menu.playerReporting": "<PERSON>ar jugador", "menu.preparingSpawn": "Preparando área de aparición: %s%%", "menu.quick_actions": "Acciones rápidas...", "menu.quick_actions.title": "Acciones rápidas", "menu.quit": "Cerrar Minecraft", "menu.reportBugs": "Reportar error", "menu.resetdemo": "Reiniciar mundo de demostración", "menu.returnToGame": "Volver al juego", "menu.returnToMenu": "Guardar y salir al menú principal", "menu.savingChunks": "Guardando los chunks...", "menu.savingLevel": "Guardando...", "menu.sendFeedback": "Enviar opinión", "menu.server_links": "Enlaces del servidor...", "menu.server_links.title": "Enlaces del servidor", "menu.shareToLan": "Abrir en LAN", "menu.singleplayer": "Un jugador", "menu.working": "Laburando...", "merchant.deprecated": "Se reabastece dos veces al día.", "merchant.level.1": "Novato", "merchant.level.2": "Aprendiz", "merchant.level.3": "Cualificado", "merchant.level.4": "Experto", "merchant.level.5": "Maestro", "merchant.title": "%s - %s", "merchant.trades": "Intercambios", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "%1$s para bajarte", "multiplayer.applyingPack": "Aplicando paquete de recursos", "multiplayer.confirm_command.parse_errors": "Estás intentando ejecutar un comando desconocido o inválido.\n¿Estás seguro?\nComando: %s", "multiplayer.confirm_command.permissions_required": "Estás intentando ejecutar un comando que requiere permisos elevados.\nEsto podría afectar negativamente a tu juego.\n¿Estás seguro?\nComando: %s", "multiplayer.confirm_command.title": "Confirmar ejecución del comando", "multiplayer.disconnect.authservers_down": "¡Disculpá! Los servidores de autentificación no están disponibles. ¡Probá más tarde!", "multiplayer.disconnect.bad_chat_index": "Mensaje de chat perdido o reordenado recibido del servidor", "multiplayer.disconnect.banned": "Te bloquearon en este servidor", "multiplayer.disconnect.banned.expiration": "\nFecha de desbloqueo: %s", "multiplayer.disconnect.banned.reason": "Estás bloqueado/a en este servidor.\nMotivo: %s", "multiplayer.disconnect.banned_ip.expiration": "\nFecha de desbloqueo: %s", "multiplayer.disconnect.banned_ip.reason": "Tu IP está bloqueada en este servidor.\nMotivo: %s", "multiplayer.disconnect.chat_validation_failed": "Error al validar el mensaje de chat", "multiplayer.disconnect.duplicate_login": "Te conectaste desde otra ubicación", "multiplayer.disconnect.expired_public_key": "Clave pública de perfil caducada. Comprobá que la hora de tu sistema esté sincronizada y reiniciá el juego.", "multiplayer.disconnect.flying": "En el servidor no se permite volar", "multiplayer.disconnect.generic": "Desconectado/a", "multiplayer.disconnect.idling": "¡Estuviste ausente demasiado tiempo!", "multiplayer.disconnect.illegal_characters": "Caracteres no permitidos en el chat", "multiplayer.disconnect.incompatible": "¡Cliente incompatible! Por favor, usá la versión %s", "multiplayer.disconnect.invalid_entity_attacked": "Se <PERSON>ó atacar a una entidad no válida", "multiplayer.disconnect.invalid_packet": "El servidor envió un paquete inválido", "multiplayer.disconnect.invalid_player_data": "Datos del jugador inválidos", "multiplayer.disconnect.invalid_player_movement": "Se detectó un movimiento de jugador no válido", "multiplayer.disconnect.invalid_public_key_signature": "Firma inválida de la clave de perfil pública.\nProbá reiniciando el juego.", "multiplayer.disconnect.invalid_public_key_signature.new": "Firma inválida de la clave de perfil pública.\nProbá reiniciando el juego.", "multiplayer.disconnect.invalid_vehicle_movement": "Se detectó un movimiento de vehículo no válido", "multiplayer.disconnect.ip_banned": "Te bloquearon de IP en este servidor", "multiplayer.disconnect.kicked": "Expulsado/a por un administrador", "multiplayer.disconnect.missing_tags": "El servidor envió un conjunto de tags incompleto.\nPor favor, contactá con un admin del servidor.", "multiplayer.disconnect.name_taken": "Ya hay alguien conectado con tu nombre", "multiplayer.disconnect.not_whitelisted": "¡No estás en la lista blanca del servidor!", "multiplayer.disconnect.out_of_order_chat": "Se ha detectado un paquete de chat fuera de servicio. ¿Ha habido algún cambio en la hora de tu dispositivo?", "multiplayer.disconnect.outdated_client": "¡Cliente incompatible! Por favor, usá la versión %s", "multiplayer.disconnect.outdated_server": "¡Cliente incompatible! Por favor, usá la versión %s", "multiplayer.disconnect.server_full": "¡El servidor está lleno!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON><PERSON> cer<PERSON>", "multiplayer.disconnect.slow_login": "La conexión tomó demasiado tiempo", "multiplayer.disconnect.too_many_pending_chats": "Demasiados mensajes de chat sin confirmar", "multiplayer.disconnect.transfers_disabled": "El servidor no acepta transferencias", "multiplayer.disconnect.unexpected_query_response": "Se enviaron datos desconocidos desde el cliente", "multiplayer.disconnect.unsigned_chat": "Se ha recibido un paquete de chat sin firma o con una firma no válida.", "multiplayer.disconnect.unverified_username": "¡No se pudo verificar tu nombre de usuario!", "multiplayer.downloadingStats": "Obteniendo estadísticas...", "multiplayer.downloadingTerrain": "Cargando el terreno...", "multiplayer.lan.server_found": "Nuevo servidor encontrado: %s", "multiplayer.message_not_delivered": "No se pudo enviar el mensaje, revisá el registro (log) del servidor: %s", "multiplayer.player.joined": "%s se conectó", "multiplayer.player.joined.renamed": "%s (antes conocido como %s) se conectó", "multiplayer.player.left": "%s se desconectó", "multiplayer.player.list.hp": "%s PS", "multiplayer.player.list.narration": "Jugadores en línea: %s", "multiplayer.requiredTexturePrompt.disconnect": "El servidor requiere un paquete de recursos personalizado", "multiplayer.requiredTexturePrompt.line1": "Este servidor requiere el uso de un paquete de recursos personalizado.", "multiplayer.requiredTexturePrompt.line2": "<PERSON><PERSON><PERSON> este paquete de recursos te desconectará del servidor.", "multiplayer.socialInteractions.not_available": "Las interacciones sociales solo están disponibles en mundos multijugador", "multiplayer.status.and_more": "... y %s más ...", "multiplayer.status.cancelled": "Operación cancelada", "multiplayer.status.cannot_connect": "Error al conectar", "multiplayer.status.cannot_resolve": "Error al resolver el nombre del host", "multiplayer.status.finished": "Conexión finalizada", "multiplayer.status.incompatible": "¡Versión no compatible!", "multiplayer.status.motd.narration": "Mensaje del día: %s", "multiplayer.status.no_connection": "(sin conexión)", "multiplayer.status.old": "Desactualizado", "multiplayer.status.online": "En línea", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Latencia de %s milisegundos", "multiplayer.status.pinging": "Conectando...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s de %s jugadores en línea", "multiplayer.status.quitting": "Desconectando", "multiplayer.status.request_handled": "Se recibió la solicitud de estado", "multiplayer.status.unknown": "¿¿??", "multiplayer.status.unrequested": "Se recibió un estado no solicitado", "multiplayer.status.version.narration": "Versión del servidor: %s", "multiplayer.stopSleeping": "Levantarse", "multiplayer.texturePrompt.failure.line1": "No se pudo aplicar el paquete de recursos del servidor", "multiplayer.texturePrompt.failure.line2": "Cualquier funcionalidad que requiera recursos personalizados puede no funcionar como se espera", "multiplayer.texturePrompt.line1": "Este servidor recomienda el uso de un paquete de recursos personalizado.", "multiplayer.texturePrompt.line2": "¿Querés descargarlo e instalarlo automágicamente?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMensaje del servidor:\n%s", "multiplayer.title": "Multijugador", "multiplayer.unsecureserver.toast": "Los mensajes enviados en este servidor pueden ser modificados y podrían no reflejar el mensaje original", "multiplayer.unsecureserver.toast.title": "Mensajes de chat no verificables", "multiplayerWarning.check": "No mostrar este mensaje de nuevo", "multiplayerWarning.header": "Atención: Juego en linea de terceros", "multiplayerWarning.message": "Atención: El juego en línea está provisto por servidores de terceros los cuales no son propiedad de Mojang Studios o Microsoft, ni están administrados ni supervisados por ellos. Mientras juegas en línea puedes estar expuesto a mensajes de texto sin moderación u otro tipo de contenido generado por la comunidad que puede no ser apropiado para todas las edades.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Botón: %s", "narration.button.usage.focused": "Apretá Enter para activar", "narration.button.usage.hovered": "Click izquierdo para activar", "narration.checkbox": "Casilla: %s", "narration.checkbox.usage.focused": "Apretá Enter para alternar", "narration.checkbox.usage.hovered": "<PERSON>lick izquierdo para alternar", "narration.component_list.usage": "Apretá Tab para navegar al siguiente elemento", "narration.cycle_button.usage.focused": "Apretá Enter para cambiar a %s", "narration.cycle_button.usage.hovered": "Click izquierdo para cambiar a %s", "narration.edit_box": "Editar caja: %s", "narration.item": "Objeto: %s", "narration.recipe": "Receta para %s", "narration.recipe.usage": "Click izquierdo para seleccionar", "narration.recipe.usage.more": "Click derecho para mostrar más recetas", "narration.selection.usage": "Apretá los botones arriba y abajo para moverte a otra entrada", "narration.slider.usage.focused": "Apretá el botón izquierdo o derecho del teclado para cambiar el valor", "narration.slider.usage.hovered": "Arrastrá el deslizador para cambiar el valor", "narration.suggestion": "Se seleccionó la sugerencia %s de %s: %s", "narration.suggestion.tooltip": "Se seleccionó la sugerencia %s de %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Apretá Tab para pasar a la siguiente sugerencia", "narration.suggestion.usage.cycle.hidable": "Apretá Tab para pasar a la siguiente sugerencia, o Escape para salir de las sugerencias", "narration.suggestion.usage.fill.fixed": "Apretá Tab para usar la sugerencia", "narration.suggestion.usage.fill.hidable": "Apretá Tab para usar la sugerencia, o Escape para abandonar las sugerencias", "narration.tab_navigation.usage": "Apretá Ctrl y Tab para cambiar de pestaña", "narrator.button.accessibility": "Accesibilidad", "narrator.button.difficulty_lock": "Bloquear dificultad", "narrator.button.difficulty_lock.locked": "Bloqueada", "narrator.button.difficulty_lock.unlocked": "Sin bloquear", "narrator.button.language": "Idioma", "narrator.controls.bound": "La acción %s está asignada al %s", "narrator.controls.reset": "Reiniciar el botón de la acción %s", "narrator.controls.unbound": "La acción %s no está asignada", "narrator.joining": "Entrando", "narrator.loading": "Cargando: %s", "narrator.loading.done": "Aceptar", "narrator.position.list": "Se ha seleccionado la fila de la lista %s de %s", "narrator.position.object_list": "Se ha seleccionado el elemento de la fila %s de %s", "narrator.position.screen": "Elemento %s fuera de %s en la pantalla", "narrator.position.tab": "Se seleccionó la pestaña %s de %s", "narrator.ready_to_play": "Listo para jugar", "narrator.screen.title": "<PERSON><PERSON> principal", "narrator.screen.usage": "Usá el cursor o botón Tab para seleccionar un elemento", "narrator.select": "Seleccionado: %s", "narrator.select.world": "Seleccionaste el mundo «%s», jugado por última vez: %s, %s, %s. Con la versión: %s", "narrator.select.world_info": "%s seleccionado, última vez jugado: %s, %s", "narrator.toast.disabled": "Narración desactivada", "narrator.toast.enabled": "Narración activada", "optimizeWorld.confirm.description": "Trataremos de optimizar tu mundo asegurándonos de que todos los datos se guarden en el formato más reciente. Dependiendo del tamaño podría demorar mucho. Una vez terminado, tu mundo debería funcionar a mayor velocidad, pero va a dejar de ser compatible con versiones anteriores. ¿Querés continuar?", "optimizeWorld.confirm.proceed": "<PERSON><PERSON><PERSON> respaldo y optimizar", "optimizeWorld.confirm.title": "<PERSON>timi<PERSON> mundo", "optimizeWorld.info.converted": "Chunks actualizados: %s", "optimizeWorld.info.skipped": "Chunks omitidos: %s", "optimizeWorld.info.total": "Chunks en total: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Calculando chunks...", "optimizeWorld.stage.failed": "¡Error! :(", "optimizeWorld.stage.finished": "Finalizando...", "optimizeWorld.stage.finished.chunks": "Terminando de optimizar chunks...", "optimizeWorld.stage.finished.entities": "Terminando de optimizar entidades...", "optimizeWorld.stage.finished.poi": "Terminando de optimizar los puntos de interés...", "optimizeWorld.stage.upgrading": "Optimizando todos los chunks...", "optimizeWorld.stage.upgrading.chunks": "Optimizando todos los chunks...", "optimizeWorld.stage.upgrading.entities": "Optimizando todas las entidades...", "optimizeWorld.stage.upgrading.poi": "Optimizando todos los puntos de interés...", "optimizeWorld.title": "Optimizando el mundo «%s»", "options.accessibility": "Accesibilidad...", "options.accessibility.high_contrast": "Contraste alto", "options.accessibility.high_contrast.error.tooltip": "El paquete de recursos de contraste alto no está disponible.", "options.accessibility.high_contrast.tooltip": "Mejora el contraste de los elementos de la interfaz.", "options.accessibility.high_contrast_block_outline": "Bordes de bloques de alto contraste", "options.accessibility.high_contrast_block_outline.tooltip": "Aumenta el contraste del contorno del bloque al que estés apuntando.", "options.accessibility.link": "Guía de accesibilidad", "options.accessibility.menu_background_blurriness": "Desenfoque del fondo del menú", "options.accessibility.menu_background_blurriness.tooltip": "Controla el difuminado del fondo en los menús.", "options.accessibility.narrator_hotkey": "Atajo al narrador", "options.accessibility.narrator_hotkey.mac.tooltip": "Permite activar o desactivar el narrador con Ctrl+B", "options.accessibility.narrator_hotkey.tooltip": "Permite activar o desactivar el narrador con Ctrl+B", "options.accessibility.panorama_speed": "Rapidez del panorama", "options.accessibility.text_background": "Fondo de texto", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "En todo", "options.accessibility.text_background_opacity": "Opacidad de fondo", "options.accessibility.title": "Accesibilidad...", "options.allowServerListing": "Mostrar conectado", "options.allowServerListing.tooltip": "Los servidores pueden mostrar jugadores en línea como parte de su estado público.\nTu nombre no aparecerá en aquellas listas si desactivás esta opción.", "options.ao": "Iluminación suave", "options.ao.max": "Máxima", "options.ao.min": "<PERSON><PERSON><PERSON>", "options.ao.off": "NO", "options.attack.crosshair": "Mira", "options.attack.hotbar": "Barr<PERSON>", "options.attackIndicator": "Indicador de ataque", "options.audioDevice": "Dispositivo", "options.audioDevice.default": "Predeterminado del Sistema", "options.autoJump": "Salto automático", "options.autoSuggestCommands": "Sugerir comandos", "options.autosaveIndicator": "Indicar autoguardado", "options.biomeBlendRadius": "Transición de biomas", "options.biomeBlendRadius.1": "NO (más rápido)", "options.biomeBlendRadius.11": "11x11 (extremo)", "options.biomeBlendRadius.13": "13x13 (piola)", "options.biomeBlendRadius.15": "15x15 (máximo)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (normal)", "options.biomeBlendRadius.7": "7x7 (de<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.9": "9x9 (muy de<PERSON><PERSON><PERSON>)", "options.chat": "Chat...", "options.chat.color": "Colores", "options.chat.delay": "Ralentizar chat: %s segundos", "options.chat.delay_none": "Ra<PERSON><PERSON><PERSON> chat: NO", "options.chat.height.focused": "Altura máx. (abierto)", "options.chat.height.unfocused": "Altura máx. (cerrado)", "options.chat.line_spacing": "Interlineado", "options.chat.links": "Enlaces web", "options.chat.links.prompt": "Avisar al abrir enlaces", "options.chat.opacity": "Opacidad de chat", "options.chat.scale": "Tamaño <PERSON>", "options.chat.title": "Chat...", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "Visible", "options.chat.visibility.hidden": "Oculto", "options.chat.visibility.system": "<PERSON><PERSON><PERSON> comandos", "options.chat.width": "<PERSON><PERSON>", "options.chunks": "%s chunks", "options.clouds.fancy": "Detalladas", "options.clouds.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.controls": "Controles...", "options.credits_and_attribution": "Créditos y atribuciones...", "options.damageTiltStrength": "Tambal<PERSON>", "options.damageTiltStrength.tooltip": "La cantidad de tambaleo que sufre la cámara al recibir daño.", "options.darkMojangStudiosBackgroundColor": "Logo monocromá<PERSON>o", "options.darkMojangStudiosBackgroundColor.tooltip": "Cambia el fondo de la pantalla de carga de Mojang Studios a negro.", "options.darknessEffectScale": "Pulso de oscuridad", "options.darknessEffectScale.tooltip": "Controla la pulsación del efecto de oscuridad cuando un warden o chillador de sculk te lo provoca.", "options.difficulty": "Dificultad", "options.difficulty.easy": "F<PERSON><PERSON>l", "options.difficulty.easy.info": "Aparecen criaturas hostiles, pero infligen menos daño. La barra de hambre se agota, pudiendo reducir la salud hasta cinco corazones.", "options.difficulty.hard": "Dif<PERSON><PERSON>l", "options.difficulty.hard.info": "Aparecen criaturas hostiles y son más fuertes. La barra de hambre se agota, pudiendo reducir toda la salud.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Aparecen criaturas hostiles que infligen daño moderado. La barra de hambre se agota, pudiendo reducir la salud hasta medio corazón.", "options.difficulty.online": "Dificultad del servidor", "options.difficulty.peaceful": "Pacífico", "options.difficulty.peaceful.info": "No aparecen criaturas hostiles, solo algunas neutrales. La barra de hambre no baja y la salud se recupera con el tiempo.", "options.directionalAudio": "Audio direccional", "options.directionalAudio.off.tooltip": "Sonido estéreo clásico", "options.directionalAudio.on.tooltip": "Utiliza audio direccional basado en HRTF para mejorar la simulación de sonido 3D. Es necesario hardware de audio compatible con HRTF, y se experimenta mejor con auriculares.", "options.discrete_mouse_scroll": "<PERSON><PERSON> discreto", "options.entityDistanceScaling": "Distancia de entidades", "options.entityShadows": "Sombras de entidades", "options.font": "Ajustes de fuente...", "options.font.title": "Ajustes de fuente", "options.forceUnicodeFont": "Forzar fuente Unicode", "options.fov": "Campo de visión", "options.fov.max": "Quake Pro", "options.fov.min": "Normal", "options.fovEffectScale": "Efectos de FOV", "options.fovEffectScale.tooltip": "Controla cuánto puede cambiar el campo de visión con los efectos del juego.", "options.framerate": "%s", "options.framerateLimit": "FPS máximos", "options.framerateLimit.max": "Sin límite", "options.fullscreen": "Pantalla completa", "options.fullscreen.current": "Actual", "options.fullscreen.entry": "%sx%s (%s Hz, %s bits)", "options.fullscreen.resolution": "Resolución de pantalla completa", "options.fullscreen.unavailable": "No disponible", "options.gamma": "<PERSON><PERSON><PERSON>", "options.gamma.default": "Por defecto", "options.gamma.max": "<PERSON><PERSON><PERSON>", "options.gamma.min": "Oscuro", "options.generic_value": "%s: %s", "options.glintSpeed": "Velocidad del brillo", "options.glintSpeed.tooltip": "Controla la velocidad del brillo de los objetos encantados.", "options.glintStrength": "Intensidad del brillo", "options.glintStrength.tooltip": "Controla la transparencia del brillo de los objetos encantados.", "options.graphics": "Grá<PERSON><PERSON>", "options.graphics.fabulous": "¡Fabulosos!", "options.graphics.fabulous.tooltip": "Los gráficos %s usan sombreadores de pantalla para renderizar las precipitaciones, las nubes y las partículas detrás de bloques transparentes o de agua.\nEsto puede afectar gravemente al rendimiento en laptops y al utilizar monitores 4K.", "options.graphics.fancy": "Detallados", "options.graphics.fancy.tooltip": "Los gráficos detallados equilibran el rendimiento y la calidad en la mayoría de dispositivos.\nLas precipitaciones, las nubes y las partículas pueden no aparecer detrás de bloques transparentes o de agua.", "options.graphics.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "Los gráficos rápidos reducen la cantidad de lluvia y nieve visible.\nLos efectos de transparencia se desactivan para bloques como las hojas de árboles.", "options.graphics.warning.accept": "Continuar sin compatibilidad", "options.graphics.warning.cancel": "<PERSON><PERSON><PERSON>", "options.graphics.warning.message": "Tu dispositivo gráfico no tiene soporte para los gráficos %s.\n\nPodés ignorar este mensaje y continuar, pero tu dispositivo no va a tener soporte si elegís usar los gráficos %s.", "options.graphics.warning.renderer": "Motor de renderizado detectado: [%s]", "options.graphics.warning.title": "Dispositivo gráfico incompatible", "options.graphics.warning.vendor": "Fabricante detectado: [%s]", "options.graphics.warning.version": "Versión de OpenGL detectada: [%s]", "options.guiScale": "Escala de interfaz", "options.guiScale.auto": "Auto.", "options.hidden": "Oculto", "options.hideLightningFlashes": "Ocultar re<PERSON>", "options.hideLightningFlashes.tooltip": "Evita que los relámpagos iluminen el cielo. Los rayos seguirán siendo visibles.", "options.hideMatchedNames": "Ocultar nombres iguales", "options.hideMatchedNames.tooltip": "Es posible que los servidores de terceros envíen mensajes en formatos no estándares.\nSi esta opción está activada, los jugadores ocultos serán emparejados según su nombre de emisor de chat.", "options.hideSplashTexts": "Ocultar los textos de bienvenida", "options.hideSplashTexts.tooltip": "Oculta los textos en amarillo que aparecen en el menú principal.", "options.inactivityFpsLimit": "Reducir FPS", "options.inactivityFpsLimit.afk": "Por inactividad", "options.inactivityFpsLimit.afk.tooltip": "Limita los Fps a 30 al pasar un minuto sin actividad. El límite aumentará a 10 Fps tras pasar 10 minutos sin actividad.", "options.inactivityFpsLimit.minimized": "Al minimizar", "options.inactivityFpsLimit.minimized.tooltip": "Solo se limitarán los FPS cuando el juego esté minimizado.", "options.invertMouse": "Invertir mouse", "options.japaneseGlyphVariants": "Variantes de glifos japoneses", "options.japaneseGlyphVariants.tooltip": "Usa las variantes japonesas de los caracteres CJK en la fuente predeterminada", "options.key.hold": "<PERSON><PERSON><PERSON>", "options.key.toggle": "Alternar", "options.language": "Idioma...", "options.language.title": "Idioma", "options.languageAccuracyWarning": "(Algunas traducciones pueden contener errores)", "options.languageWarning": "Algunas traducciones pueden contener errores", "options.mainHand": "<PERSON><PERSON> principal", "options.mainHand.left": "Iz<PERSON>erda", "options.mainHand.right": "Derecha", "options.mipmapLevels": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.cape": "Capa", "options.modelPart.hat": "Sombrero", "options.modelPart.jacket": "<PERSON><PERSON>", "options.modelPart.left_pants_leg": "<PERSON><PERSON>", "options.modelPart.left_sleeve": "Manga izquierda", "options.modelPart.right_pants_leg": "<PERSON><PERSON> der<PERSON>a", "options.modelPart.right_sleeve": "Manga derecha", "options.mouseWheelSensitivity": "Sensibilidad de scroll", "options.mouse_settings": "Mouse...", "options.mouse_settings.title": "Opciones de mouse", "options.multiplayer.title": "Opciones de multijugador...", "options.multiplier": "x%s", "options.music_frequency": "Frecuencia de música", "options.music_frequency.constant": "<PERSON><PERSON><PERSON>", "options.music_frequency.default": "Por defecto", "options.music_frequency.frequent": "Frecuente", "options.music_frequency.tooltip": "Cambia la frecuencia con la que se reproduce música durante una partida.", "options.narrator": "Narración", "options.narrator.all": "<PERSON><PERSON><PERSON> todo", "options.narrator.chat": "<PERSON><PERSON><PERSON> chat", "options.narrator.notavailable": "No disponible", "options.narrator.off": "NO", "options.narrator.system": "<PERSON><PERSON><PERSON> sistema", "options.notifications.display_time": "Duración de las notificaciones", "options.notifications.display_time.tooltip": "Controla cuanto tiempo se mantienen las notificaciones visibles en la pantalla.", "options.off": "NO", "options.off.composed": "%s: NO", "options.on": "SÍ", "options.on.composed": "%s: SÍ", "options.online": "En línea...", "options.online.title": "Opciones en línea", "options.onlyShowSecureChat": "Solo mensajes seguros", "options.onlyShowSecureChat.tooltip": "Muestra solo mensajes de jugadores que se pueda verificar que fueron enviados por esa persona, y que no fueron modificados.", "options.operatorItemsTab": "Pestaña de administrador", "options.particles": "Partículas", "options.particles.all": "<PERSON><PERSON>", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "Mínimas", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Generar chunks", "options.prioritizeChunkUpdates.byPlayer": "Por acción", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Algunas acciones pueden hacer que el chunk se recompile inmediatamente. Esto incluye el colocar y destruir bloques.", "options.prioritizeChunkUpdates.nearby": "Siempre", "options.prioritizeChunkUpdates.nearby.tooltip": "Los chunks cercanos serán compilados de inmediato siempre. Esto podría afectar al rendimiento del juego al colocar o destruir bloques.", "options.prioritizeChunkUpdates.none": "En paralelo", "options.prioritizeChunkUpdates.none.tooltip": "Los chunks cercanos son compilados en hilos paralelos. Esto puede resultar en breves agujeros visuales cuando los bloques son destruidos.", "options.rawMouseInput": "Entrada directa", "options.realmsNotifications": "Noticias e invitaciones de Realms", "options.realmsNotifications.tooltip": "Buscá noticias e invitaciones de Realms en el menú principal y muestra sus respectivos íconos en el botón de Realms.", "options.reducedDebugInfo": "Reducir datos de F3", "options.renderClouds": "Nubes", "options.renderCloudsDistance": "Distancia de las nubes", "options.renderDistance": "Renderizado", "options.resourcepack": "Paquetes de recursos...", "options.rotateWithMinecart": "<PERSON><PERSON><PERSON> con carritos", "options.rotateWithMinecart.tooltip": "Controla si la cámara debería rotar al subirte en un carrito. Solo disponible en mundos con el ajuste experimental «Mejoras del carrito» activado.", "options.screenEffectScale": "Efectos de distorsión", "options.screenEffectScale.tooltip": "Modifica la intensidad de los efectos de distorsión generados por los portales del Nether y las náuseas.\nSi el valor es bajo, el efecto de las náuseas se sustituye por un overlay verde.", "options.sensitivity": "Sensibilidad", "options.sensitivity.max": "¡¡HIPERRÁPIDO!!!", "options.sensitivity.min": "*zzz*", "options.showNowPlayingToast": "Notificación de música", "options.showNowPlayingToast.tooltip": "Muestra una notificación cada vez que empieza a sonar una canción. La notificación permanecerá visible en el menú de pausa mientras haya una canción sonando.", "options.showSubtitles": "Mostrar subtítulos", "options.simulationDistance": "Simulación", "options.skinCustomisation": "Mi aspecto...", "options.skinCustomisation.title": "Mi aspecto", "options.sounds": "Música y sonidos...", "options.sounds.title": "Opciones de música y sonido", "options.telemetry": "Telemetría...", "options.telemetry.button": "Recolección de datos", "options.telemetry.button.tooltip": "«%s» incluye solo la información necesaria.\n«%s» incluye también información opcional.", "options.telemetry.disabled": "Telemetría desactivada.", "options.telemetry.state.all": "Toda", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON>", "options.telemetry.state.none": "<PERSON><PERSON>", "options.title": "Opciones", "options.touchscreen": "<PERSON><PERSON><PERSON> t<PERSON>ctil", "options.video": "Gráficos...", "options.videoTitle": "Opciones de gráficos", "options.viewBobbing": "Movimiento de la visión", "options.visible": "Visible", "options.vsync": "VSync", "outOfMemory.message": "Minecraft se quedó sin memoria.\n\nEsto podría deberse a un error en el juego o a que la máquina virtual de Java no tiene suficiente memoria asignada.\n\nPara evitar la corrupción de mundo, la partida actual se cerró. Hemos intentado liberar suficiente memoria para permitirte regresar al menú principal y volver a jugar, pero es posible que esto no haya funcionado.\n\nReiniciá el juego si volvés a ver este mensaje.", "outOfMemory.title": "¡Sin memoria!", "pack.available.title": "Disponibles", "pack.copyFailure": "Error al copiar los paquetes", "pack.dropConfirm": "¿Querés agregar estos paquetes a Minecraft?", "pack.dropInfo": "Arrastrá archivos a esta ventana para agregar paquetes", "pack.dropRejected.message": "Las siguientes entradas no eran paquetes válidos y no se copiaron:\n%s", "pack.dropRejected.title": "Entra<PERSON> que no son paquete", "pack.folderInfo": "(Meté los archivos de paquetes acá)", "pack.incompatible": "Incompatible", "pack.incompatible.confirm.new": "Este paquete está diseñado para una versión de Minecraft más reciente y puede que no funcione correctamente.", "pack.incompatible.confirm.old": "Este paquete está diseñado para una versión de Minecraft anterior y puede que no funcione correctamente.", "pack.incompatible.confirm.title": "¿Seguro que querés cargar este paquete?", "pack.incompatible.new": "(Diseñado para una versión posterior de Minecraft)", "pack.incompatible.old": "(Diseñado para una versión anterior de Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON><PERSON> la <PERSON>a", "pack.selected.title": "Seleccionados", "pack.source.builtin": "integrado", "pack.source.feature": "característica", "pack.source.local": "local", "pack.source.server": "servidor", "pack.source.world": "mundo", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "<PERSON><PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Bodegón", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Pájaro de las cuevas", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Cambiante", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Tierra", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Jefe final", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Hallazgo", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fuego", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Neblina", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditativo", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "Or<PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Búho con limones", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Pasadizo", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Apuntador", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Estanque", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "La pileta", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Paseo por la pradera", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Costa cálida", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Ataduras mortales", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Calavera y rosas", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "<PERSON><PERSON> preparado", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Girasoles", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "<PERSON>ardec<PERSON>", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Desempaque", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "El vacío", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "El caminante sobre el mar de nubes", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Agua", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Viento", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Variante aleatoria", "parsing.bool.expected": "Se requiere un valor booleano", "parsing.bool.invalid": "Valor booleano no válido: «%s» no es «true» ni «false»", "parsing.double.expected": "Se requiere un valor doble", "parsing.double.invalid": "Valor doble no válido: %s", "parsing.expected": "Se requiere «%s»", "parsing.float.expected": "Se requiere un valor float", "parsing.float.invalid": "Valor float no válido: %s", "parsing.int.expected": "Se requiere un número entero", "parsing.int.invalid": "Número entero no válido: %s", "parsing.long.expected": "Se requiere un valor long", "parsing.long.invalid": "Valor long no válido: %s", "parsing.quote.escape": "La secuencia de escape «\\%s» no es válida en una cadena con comillas", "parsing.quote.expected.end": "Falta el cierre de comillas de la cadena", "parsing.quote.expected.start": "Faltan comillas al inicio de la cadena", "particle.invalidOptions": "No se pudo analizar las opciones de partículas: %s", "particle.notFound": "Partícula desconocida: %s", "permissions.requires.entity": "Se requiere una entidad para ejecutar este comando desde acá", "permissions.requires.player": "Se requiere un jugador para ejecutar este comando desde acá", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Al aplicarse:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predicado desconocido: %s", "quickplay.error.invalid_identifier": "No se pudo encontrar el mundo con el identificador proporcionado", "quickplay.error.realm_connect": "No se pudo conectar al Realm", "quickplay.error.realm_permission": "No tenés permiso para entrar a este Realm", "quickplay.error.title": "Error en Juego Rapido", "realms.configuration.region.australia_east": "Nueva Gales del Sur, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, EE. UU.", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, EE. UU.", "realms.configuration.region.east_us_2": "Carolina del Norte, EE. UU.", "realms.configuration.region.france_central": "Francia", "realms.configuration.region.japan_east": "Este de Japón", "realms.configuration.region.japan_west": "Oeste de Japón", "realms.configuration.region.korea_central": "Corea del Sur", "realms.configuration.region.north_central_us": "Illinois, EE. UU.", "realms.configuration.region.north_europe": "Irlanda", "realms.configuration.region.south_central_us": "Texas, EE. UU.", "realms.configuration.region.southeast_asia": "Singapur", "realms.configuration.region.sweden_central": "<PERSON><PERSON>", "realms.configuration.region.uae_north": "Emiratos Árabes Unidos (EAU)", "realms.configuration.region.uk_south": "Sur de Inglaterra", "realms.configuration.region.west_central_us": "Utah, EE. UU.", "realms.configuration.region.west_europe": "País<PERSON>", "realms.configuration.region.west_us": "California, EE. UU.", "realms.configuration.region.west_us_2": "Washington, EE. UU.", "realms.configuration.region_preference.automatic_owner": "Automática (latencia del dueño del Realm)", "realms.configuration.region_preference.automatic_player": "Atomática (latencia del primero en unirse)", "realms.missing.snapshot.error.text": "Minecraft Realms no es compatible con versiones snapshot.", "recipe.notFound": "Receta desconocida: %s", "recipe.toast.description": "Mirá el libro de recetas.", "recipe.toast.title": "¡Nuevas recetas!", "record.nowPlaying": "Estás escuchando: %s", "recover_world.bug_tracker": "Reportar un error", "recover_world.button": "Intentar recuperar", "recover_world.done.failed": "Error al recuperar un estado anterior.", "recover_world.done.success": "¡Recuperación exitosa!", "recover_world.done.title": "Recuperación terminada", "recover_world.issue.missing_file": "Falta un archivo", "recover_world.issue.none": "<PERSON> problemas", "recover_world.message": "Se produjeron los siguientes problemas al intentar leer la carpeta «%s».\nTal vez sea posible restaurar el mundo de una versión anterior o informar del error en el registro de errores.", "recover_world.no_fallback": "No hay estados de recuperación disponibles", "recover_world.restore": "Intentar restaurar", "recover_world.restoring": "Intentando recuperar el mundo...", "recover_world.state_entry": "Estado desde %s: ", "recover_world.state_entry.unknown": "desconocido", "recover_world.title": "Error al cargar el mundo", "recover_world.warning": "Error al cargar el resumen del mundo", "resourcePack.broken_assets": "SE DETECTARON RECURSOS CON ERRORES", "resourcePack.high_contrast.name": "Contraste alto", "resourcePack.load_fail": "Error al recargar el recurso", "resourcePack.programmer_art.name": "Arte de programador", "resourcePack.runtime_failure": "Se detectó un error en el paquete de recursos", "resourcePack.server.name": "Recursos específicos del mundo", "resourcePack.title": "Se<PERSON><PERSON><PERSON><PERSON> paque<PERSON> de recursos", "resourcePack.vanilla.description": "La apariencia predeterminada de Minecraft", "resourcePack.vanilla.name": "Por defecto", "resourcepack.downloading": "Descargando paque<PERSON> de recursos", "resourcepack.progress": "Descargando archivo (%s MB)...", "resourcepack.requesting": "Realizando solicitud...", "screenshot.failure": "No se pudo guardar la captura de pantalla: %s", "screenshot.success": "Captura guardada como %s", "selectServer.add": "Ag<PERSON><PERSON> servidor", "selectServer.defaultName": "<PERSON><PERSON><PERSON>", "selectServer.delete": "Bo<PERSON>r", "selectServer.deleteButton": "Bo<PERSON>r", "selectServer.deleteQuestion": "¿Seguro que querés borrar este servidor?", "selectServer.deleteWarning": "«%s» va a desaparecer... ¡para siempre! (¡eso es mucho tiempo!)", "selectServer.direct": "Conexión directa", "selectServer.edit": "<PERSON><PERSON>", "selectServer.hiddenAddress": "(IP oculta)", "selectServer.refresh": "Actualizar", "selectServer.select": "Entrar al servidor", "selectWorld.access_failure": "Error al entrar al mundo", "selectWorld.allowCommands": "Per<PERSON><PERSON> comandos", "selectWorld.allowCommands.info": "/gamemode, /experience, etc.", "selectWorld.allowCommands.new": "Per<PERSON><PERSON> comandos", "selectWorld.backupEraseCache": "<PERSON><PERSON><PERSON> datos en caché", "selectWorld.backupJoinConfirmButton": "<PERSON><PERSON>r una copia y cargar", "selectWorld.backupJoinSkipButton": "¡Sé lo que estoy haciendo!", "selectWorld.backupQuestion.customized": "Los mundos personalizados ya no son compatibles", "selectWorld.backupQuestion.downgrade": "Degradar un mundo a una versión anterior no es compatible", "selectWorld.backupQuestion.experimental": "Los mundos con opciones de generación experimentales no tienen soporte", "selectWorld.backupQuestion.snapshot": "¿Seguro que querés cargar este mundo?", "selectWorld.backupWarning.customized": "Minecraft ya no es compatible con los mundos personalizados. Podés cargar el mundo y jugar igual que antes, pero si se genera nuevo terreno no va a tener el estilo personalizado. ¡Lamentamos las molestias!", "selectWorld.backupWarning.downgrade": "Este mundo fue jugado por última vez en la versión %s; estas en la versión %s. Degradar un mundo a una versión anterior podría corromper el archivo; no podemos garantizar que cargue o funcione. Si todavía querés continuar, ¡hacé una copia de seguridad!", "selectWorld.backupWarning.experimental": "Este mundo usa opciones experimentales que pueden parar de funcionar en cualquier momento. No podemos garantizar que cargue o funcione correctamente. ¡Cuidado!", "selectWorld.backupWarning.snapshot": "Este mundo se jugó por última vez en la versión %s; estás en la versión %s. <PERSON>r favor, hacé una copia de seguridad para no experimentar corrupciones de mundo.", "selectWorld.bonusItems": "Baúl bonus", "selectWorld.cheats": "comandos", "selectWorld.commands": "<PERSON><PERSON><PERSON>", "selectWorld.conversion": "¡Tiene que ser convertido!", "selectWorld.conversion.tooltip": "Este mundo tiene que abrirse en una versión más antigua (como la 1.6.4) para ser convertido sin problemas", "selectWorld.create": "<PERSON><PERSON>r un mundo nuevo", "selectWorld.customizeType": "Personalizar", "selectWorld.dataPacks": "Paquetes de <PERSON>", "selectWorld.data_read": "Leyendo datos del mundo...", "selectWorld.delete": "Bo<PERSON>r", "selectWorld.deleteButton": "Bo<PERSON>r", "selectWorld.deleteQuestion": "¿Seguro que querés borrar este mundo?", "selectWorld.deleteWarning": "«%s» va a desaparecer... ¡para siempre! (¡eso es mucho tiempo!)", "selectWorld.delete_failure": "Error al borrar el mundo", "selectWorld.edit": "<PERSON><PERSON>", "selectWorld.edit.backup": "Hacer copia de seguridad", "selectWorld.edit.backupCreated": "%s", "selectWorld.edit.backupFailed": "Error al realizar la copia de seguridad", "selectWorld.edit.backupFolder": "<PERSON><PERSON><PERSON>a de copias", "selectWorld.edit.backupSize": "Mundo copiado (%s MB)", "selectWorld.edit.export_worldgen_settings": "Exportar opciones de generación", "selectWorld.edit.export_worldgen_settings.failure": "Error al exportar ", "selectWorld.edit.export_worldgen_settings.success": "Se exportó", "selectWorld.edit.openFolder": "<PERSON><PERSON>r carpeta del mundo", "selectWorld.edit.optimize": "<PERSON>timi<PERSON> mundo", "selectWorld.edit.resetIcon": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.save": "Guardar", "selectWorld.edit.title": "<PERSON>ar mundo", "selectWorld.enterName": "Nombre del mundo", "selectWorld.enterSeed": "Semilla para generar el mundo", "selectWorld.experimental": "Experimental", "selectWorld.experimental.details": "Detalles", "selectWorld.experimental.details.entry": "Características experimentales necesarias: %s", "selectWorld.experimental.details.title": "Requisitos de características experimentales", "selectWorld.experimental.message": "¡Tené cuidado!\nEsta configuración requiere características que todavía están en desarrollo. Tu mundo puede dejar de funcionar, romperse o no funcionar en futuras actualizaciones.", "selectWorld.experimental.title": "Advertencia de características experimentales", "selectWorld.experiments": "Experimentos", "selectWorld.experiments.info": "Los experimentos son posibles características nuevas. <PERSON><PERSON>, algunas cosas pueden romperse. Los experimentos no pueden desactivarse después de la creación del mundo.", "selectWorld.futureworld.error.text": "Algo salió mal al intentar cargar un mundo de una versión más reciente. Fue una operación arriesgada desde el principio; sentimos que no haya funcionado.", "selectWorld.futureworld.error.title": "¡Se produjo un error!", "selectWorld.gameMode": "Modo", "selectWorld.gameMode.adventure": "Aventura", "selectWorld.gameMode.adventure.info": "Igual al modo supervivencia, pero no podés colocar ni destruir bloques.", "selectWorld.gameMode.adventure.line1": "Igual que el modo supervivencia, pero", "selectWorld.gameMode.adventure.line2": "pueden agregar ni sacar bloques.", "selectWorld.gameMode.creative": "Creativo", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON>, constru<PERSON> y explorá sin límites. <PERSON>des volar, tener recursos ilimitados, y ser inmune al daño.", "selectWorld.gameMode.creative.line1": "Recursos ilimitados, vuelo libre y", "selectWorld.gameMode.creative.line2": "podés romper bloques al instante.", "selectWorld.gameMode.hardcore": "Hardcore", "selectWorld.gameMode.hardcore.info": "Similar al modo supervivencia, pero con la dificultad bloqueada en «Difícil» y con una única vida.", "selectWorld.gameMode.hardcore.line1": "Igual que supervivencia, pero", "selectWorld.gameMode.hardcore.line2": "más difícil y con sólo una vida.", "selectWorld.gameMode.spectator": "Espectador", "selectWorld.gameMode.spectator.info": "<PERSON>d<PERSON> mirar, pero no tocar.", "selectWorld.gameMode.spectator.line1": "Se mira, no se toca", "selectWorld.gameMode.survival": "Supervivencia", "selectWorld.gameMode.survival.info": "Explorá un mundo lleno de misterios, donde podr<PERSON> construir, recolectar, fabricar y enfrentarte a monstruos.", "selectWorld.gameMode.survival.line1": "Buscá recursos, fabricá, ganá", "selectWorld.gameMode.survival.line2": "experiencia, comé y cuidate.", "selectWorld.gameRules": "Reglas del juego", "selectWorld.import_worldgen_settings": "Importar opciones", "selectWorld.import_worldgen_settings.failure": "Error al importar opciones", "selectWorld.import_worldgen_settings.select_file": "Seleccionar archivo de opciones (.json)", "selectWorld.incompatible.description": "Este mundo no se puede abrir en esta versión.\nFue jugado por última vez en la versión %s.", "selectWorld.incompatible.info": "Versión incompatible: %s", "selectWorld.incompatible.title": "Versión incompatible", "selectWorld.incompatible.tooltip": "Este mundo no se puede abrir porque se creó en una versión no compatible.", "selectWorld.incompatible_series": "Creado por una versión incompatible", "selectWorld.load_folder_access": "¡No se puede leer o acceder a la carpeta donde se guardan los mundos!", "selectWorld.loading_list": "Cargando lista de mundos", "selectWorld.locked": "Mundo abierto en otra sesión de Minecraft", "selectWorld.mapFeatures": "Generar estructuras", "selectWorld.mapFeatures.info": "Aldeas, naufragios, etc.", "selectWorld.mapType": "Tipo de mundo", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Más opciones del mundo...", "selectWorld.newWorld": "Mundo nuevo", "selectWorld.recreate": "<PERSON><PERSON><PERSON>", "selectWorld.recreate.customized.text": "Los mundos personalizados no son compatibles con esta versión de Minecraft. Podemos intentar rehacerlo usando la misma semilla y las mismas propiedades. Eso sí, va a ser como los mundos normales. ¡Lamentamos las molestias!", "selectWorld.recreate.customized.title": "Los mundos personalizados ya no son compatibles", "selectWorld.recreate.error.text": "Algo salió mal al intentar rehacer el mundo.", "selectWorld.recreate.error.title": "¡Se produjo un error!", "selectWorld.resource_load": "Preparando recursos...", "selectWorld.resultFolder": "Va a guardarse en:", "selectWorld.search": "buscar mundos", "selectWorld.seedInfo": "No escribir para usar una al azar.", "selectWorld.select": "<PERSON><PERSON> al mundo seleccionado", "selectWorld.targetFolder": "Carpeta de guardado: %s", "selectWorld.title": "Seleccionar mundo", "selectWorld.tooltip.fromNewerVersion1": "¡El mundo pertenece a una versión más reciente,", "selectWorld.tooltip.fromNewerVersion2": "si se carga en esta versión puede corromperse!", "selectWorld.tooltip.snapshot1": "Hacé una copia de seguridad del mundo", "selectWorld.tooltip.snapshot2": "antes de cargarlo en esta versión snapshot.", "selectWorld.unable_to_load": "No se pueden cargar los mundos", "selectWorld.version": "versión:", "selectWorld.versionJoinButton": "<PERSON><PERSON> de todas formas", "selectWorld.versionQuestion": "¿Seguro que querés cargar este mundo?", "selectWorld.versionUnknown": "¿?", "selectWorld.versionWarning": "¡Este mundo se jugó por última vez con la versión %s! ¡Si se juega utilizando la versión actual podría corromperse!", "selectWorld.warning.deprecated.question": "Algunas de las características utilizadas están obsoletas y dejarán de funcionar en el futuro. ¿Deseas continuar?", "selectWorld.warning.deprecated.title": "¡Advertencia! Estos ajustes utilizan características obsoletas", "selectWorld.warning.experimental.question": "Estos ajustes son experimentales y podrían dejar de funcionar en algún momento. ¿Deseas continuar?", "selectWorld.warning.experimental.title": "¡Advertencia! Estos ajustes utilizan características experimentales", "selectWorld.warning.lowDiskSpace.description": "No queda mucho espacio en tu dispositivo.\nTu mundo podría ser dañado si te quedás sin espacio en el disco mientras jugás.", "selectWorld.warning.lowDiskSpace.title": "¡Atención! ¡Queda poco espacio en el disco!", "selectWorld.world": "Mundo", "sign.edit": "Editar mensaje del cartel", "sleep.not_possible": "No hay suficientes jugadores durmiendo para pasar la noche", "sleep.players_sleeping": "%s/%s jugadores durmiendo", "sleep.skipping_night": "Durmiendo esta noche", "slot.only_single_allowed": "Solo se permiten espacios individuales, obtenido: %s", "slot.unknown": "Slot desconocido: %s", "snbt.parser.empty_key": "La clave no puede estar vacía", "snbt.parser.expected_binary_numeral": "Se esperaba un número binario", "snbt.parser.expected_decimal_numeral": "Se esperaba un número decimal", "snbt.parser.expected_float_type": "Se esperaba un número de punto flotante", "snbt.parser.expected_hex_escape": "Se esperaba una cadena de longitud %s", "snbt.parser.expected_hex_numeral": "Se esperaba un número hexadecimal", "snbt.parser.expected_integer_type": "Se esperaba un número entero", "snbt.parser.expected_non_negative_number": "Se esperaba un número no negativo", "snbt.parser.expected_number_or_boolean": "Se requiere un número o valor booleano", "snbt.parser.expected_string_uuid": "Se requiere una cadena con un UUID válido", "snbt.parser.expected_unquoted_string": "Se requiere una cadena sin comillas válida", "snbt.parser.infinity_not_allowed": "No se permiten números infinitos", "snbt.parser.invalid_array_element_type": "El tipo de los elementos de la lista no es válido", "snbt.parser.invalid_character_name": "Nombre de carácter Unicode no válido", "snbt.parser.invalid_codepoint": "Carácter Unicode no válido: %s", "snbt.parser.invalid_string_contents": "Contenido de cadena no válido", "snbt.parser.invalid_unquoted_start": "Las cadenas sin comillas no pueden empezar por los dígitos 0-9, + o -", "snbt.parser.leading_zero_not_allowed": "Los números decimales no pueden empezar por 0", "snbt.parser.no_such_operation": "La operación no existe: %s", "snbt.parser.number_parse_failure": "Error al analizar número: %s", "snbt.parser.undescore_not_allowed": "Los guiones bajos al comienzo o al final de un número no están permitidos", "soundCategory.ambient": "Ambiente", "soundCategory.block": "Bloques", "soundCategory.hostile": "Criaturas hostiles", "soundCategory.master": "Volumen general", "soundCategory.music": "Música", "soundCategory.neutral": "Criaturas pacíficas", "soundCategory.player": "<PERSON><PERSON><PERSON>", "soundCategory.record": "Bloques musicales", "soundCategory.ui": "Interfaz", "soundCategory.voice": "Voces y diálogos", "soundCategory.weather": "Precipitaciones", "spectatorMenu.close": "<PERSON><PERSON><PERSON>", "spectatorMenu.next_page": "Siguient<PERSON>", "spectatorMenu.previous_page": "Anterior", "spectatorMenu.root.prompt": "Presioná una tecla para seleccionar un comando, y volvé a presionarla para usarlo.", "spectatorMenu.team_teleport": "Teletransportarse a un miembro del equipo", "spectatorMenu.team_teleport.prompt": "Seleccioná un equipo para teletransportarte", "spectatorMenu.teleport": "Teletransportarte a un jugador", "spectatorMenu.teleport.prompt": "Seleccioná un jugador para teletransportarte", "stat.generalButton": "General", "stat.itemsButton": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "<PERSON><PERSON> criados", "stat.minecraft.aviate_one_cm": "Distancia volada con elytra", "stat.minecraft.bell_ring": "Campanas to<PERSON>das", "stat.minecraft.boat_one_cm": "Distancia en bote", "stat.minecraft.clean_armor": "Partes de armadura limpiadas", "stat.minecraft.clean_banner": "Estandartes limpiados", "stat.minecraft.clean_shulker_box": "Cajas de shulker vaciadas", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON> trepada", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON> a<PERSON>", "stat.minecraft.damage_absorbed": "Daño <PERSON>", "stat.minecraft.damage_blocked_by_shield": "Daño bloqueado con escudo", "stat.minecraft.damage_dealt": "<PERSON><PERSON> causado", "stat.minecraft.damage_dealt_absorbed": "<PERSON>ño causado (absorbido)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON> causado (resistido)", "stat.minecraft.damage_resisted": "Daño resistido", "stat.minecraft.damage_taken": "<PERSON><PERSON> recibido", "stat.minecraft.deaths": "Númer<PERSON>uer<PERSON>", "stat.minecraft.drop": "Objetos tirados", "stat.minecraft.eat_cake_slice": "Trozos de torta comidos", "stat.minecraft.enchant_item": "Objetos encantados", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.fill_cauldron": "<PERSON><PERSON>", "stat.minecraft.fish_caught": "Peces pescados", "stat.minecraft.fly_one_cm": "Distancia volada", "stat.minecraft.happy_ghast_one_cm": "Distancia en ghast feliz", "stat.minecraft.horse_one_cm": "Distancia a caballo", "stat.minecraft.inspect_dispenser": "Dispensadores inspeccionados", "stat.minecraft.inspect_dropper": "Soltadores inspeccionados", "stat.minecraft.inspect_hopper": "Tolvas inspeccionadas", "stat.minecraft.interact_with_anvil": "Usos de yunques", "stat.minecraft.interact_with_beacon": "Usos de faros", "stat.minecraft.interact_with_blast_furnace": "Usos de altos hornos", "stat.minecraft.interact_with_brewingstand": "Usos de destiladoras", "stat.minecraft.interact_with_campfire": "Usos de fogatas", "stat.minecraft.interact_with_cartography_table": "Usos de mesas de cartografía", "stat.minecraft.interact_with_crafting_table": "Usos de mesas de crafteo", "stat.minecraft.interact_with_furnace": "Usos de hornos", "stat.minecraft.interact_with_grindstone": "Usos de afiladoras", "stat.minecraft.interact_with_lectern": "Usos de atriles", "stat.minecraft.interact_with_loom": "Usos de máquinas de telar", "stat.minecraft.interact_with_smithing_table": "Usos de mesas de herrería", "stat.minecraft.interact_with_smoker": "Usos de ahumadores", "stat.minecraft.interact_with_stonecutter": "Usos de cortapiedras", "stat.minecraft.jump": "Saltos", "stat.minecraft.leave_game": "Veces que saliste de la partida", "stat.minecraft.minecart_one_cm": "Distancia en carrito", "stat.minecraft.mob_kills": "Criaturas matadas", "stat.minecraft.open_barrel": "<PERSON><PERSON> abiertos", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON> abie<PERSON>", "stat.minecraft.open_enderchest": "Baúles de ender abiertos", "stat.minecraft.open_shulker_box": "Cajas de shulker abiertas", "stat.minecraft.pig_one_cm": "Distancia en chancho", "stat.minecraft.play_noteblock": "Bloques musicales golpeados", "stat.minecraft.play_record": "Discos reproducidos", "stat.minecraft.play_time": "Tiempo jugado", "stat.minecraft.player_kills": "Ju<PERSON><PERSON> matados", "stat.minecraft.pot_flower": "Plantas enmacetadas", "stat.minecraft.raid_trigger": "Invasiones activadas", "stat.minecraft.raid_win": "<PERSON>es ganadas", "stat.minecraft.sleep_in_bed": "Noches dormidas", "stat.minecraft.sneak_time": "Tiempo agachado", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON> corriendo", "stat.minecraft.strider_one_cm": "Distancia en strider", "stat.minecraft.swim_one_cm": "Distancia nadada", "stat.minecraft.talked_to_villager": "Charlas con aldeanos", "stat.minecraft.target_hit": "Tiros al blanco en dianas", "stat.minecraft.time_since_death": "Tiempo desde últ. muerte", "stat.minecraft.time_since_rest": "Tiempo desde últ. descanso", "stat.minecraft.total_world_time": "Tiempo con el mundo abierto", "stat.minecraft.traded_with_villager": "Comercios con aldeanos", "stat.minecraft.trigger_trapped_chest": "Baúles trampa activados", "stat.minecraft.tune_noteblock": "Bloques musicales afinados", "stat.minecraft.use_cauldron": "Agua tomada de calderos", "stat.minecraft.walk_on_water_one_cm": "Dist. caminada sobre agua", "stat.minecraft.walk_one_cm": "Distancia caminada", "stat.minecraft.walk_under_water_one_cm": "Dist. caminada bajo agua", "stat.mobsButton": "Criaturas", "stat_type.minecraft.broken": "<PERSON><PERSON><PERSON> roto", "stat_type.minecraft.crafted": "Veces fabricado", "stat_type.minecraft.dropped": "<PERSON><PERSON><PERSON> solta<PERSON>", "stat_type.minecraft.killed": "Criaturas matadas: %s", "stat_type.minecraft.killed.none": "Criaturas matadas: 0", "stat_type.minecraft.killed_by": "Muertes causadas: %2$s", "stat_type.minecraft.killed_by.none": "Muertes causadas: 0", "stat_type.minecraft.mined": "<PERSON><PERSON><PERSON> minado", "stat_type.minecraft.picked_up": "<PERSON><PERSON><PERSON> a<PERSON>", "stat_type.minecraft.used": "<PERSON>eces usado", "stats.none": "-", "structure_block.button.detect_size": "DETECTAR", "structure_block.button.load": "CARGAR", "structure_block.button.save": "GUARDAR", "structure_block.custom_data": "Nombre de dataTag personalizado", "structure_block.detect_size": "Detecta tamaño y posición de la estructura:", "structure_block.hover.corner": "Esquina: %s", "structure_block.hover.data": "Datos: %s", "structure_block.hover.load": "Cargar: %s", "structure_block.hover.save": "Guardar: %s", "structure_block.include_entities": "Incluir entidades:", "structure_block.integrity": "Integridad y semilla de la estructura", "structure_block.integrity.integrity": "Integridad de la estructura", "structure_block.integrity.seed": "Semilla de la estructura", "structure_block.invalid_structure_name": "«%s» no es un nombre de estructura válido", "structure_block.load_not_found": "La estructura «%s» no está disponible", "structure_block.load_prepare": "Posición de la estructura «%s» preparada", "structure_block.load_success": "Estructura «%s» cargada", "structure_block.mode.corner": "Esquina", "structure_block.mode.data": "Datos", "structure_block.mode.load": "<PERSON><PERSON>", "structure_block.mode.save": "Guardar", "structure_block.mode_info.corner": "Esquina: colocalo en esquina para marcar", "structure_block.mode_info.data": "Datos: usa la lógica del juego", "structure_block.mode_info.load": "Cargar: cargar desde archivo", "structure_block.mode_info.save": "Guardar: guardar en archivo", "structure_block.position": "Posición relativa", "structure_block.position.x": "coordenada x de la posición relativa", "structure_block.position.y": "coordenada y de la posición relativa", "structure_block.position.z": "coordenada z de la posición relativa", "structure_block.save_failure": "No se pudo guardar la estructura «%s»", "structure_block.save_success": "Estructura guardada como «%s»", "structure_block.show_air": "Mostrar bloques invisibles:", "structure_block.show_boundingbox": "Mostrar límites:", "structure_block.size": "Tamaño de la estructura", "structure_block.size.x": "dimensión de la estructura en el eje x", "structure_block.size.y": "dimensión de la estructura en el eje y", "structure_block.size.z": "dimensión de la estructura en el eje z", "structure_block.size_failure": "No se pudo detectar el tamaño de la estructura. Agregá más esquinas con el nombre de la estructura", "structure_block.size_success": "Se detectó el tamaño de la estructura «%s»", "structure_block.strict": "Colocación estricta:", "structure_block.structure_name": "Nombre de la estructura", "subtitles.ambient.cave": "Sonido desconocido", "subtitles.ambient.sound": "Ruido inquietante", "subtitles.block.amethyst_block.chime": "Amatista tintinea", "subtitles.block.amethyst_block.resonate": "Amatista resuena", "subtitles.block.anvil.destroy": "<PERSON><PERSON> des<PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON>", "subtitles.block.anvil.use": "<PERSON><PERSON> usado", "subtitles.block.barrel.close": "<PERSON>il cerrado", "subtitles.block.barrel.open": "<PERSON><PERSON> abierto", "subtitles.block.beacon.activate": "Faro activado", "subtitles.block.beacon.ambient": "<PERSON><PERSON><PERSON><PERSON>o", "subtitles.block.beacon.deactivate": "Faro desactivado", "subtitles.block.beacon.power_select": "Poder de faro seleccionado", "subtitles.block.beehive.drip": "<PERSON><PERSON>", "subtitles.block.beehive.enter": "<PERSON><PERSON> entra en una colmena", "subtitles.block.beehive.exit": "Abeja sale de una colmena", "subtitles.block.beehive.shear": "Tijeras raspando", "subtitles.block.beehive.work": "<PERSON><PERSON>", "subtitles.block.bell.resonate": "Campana resuena", "subtitles.block.bell.use": "Campanada", "subtitles.block.big_dripleaf.tilt_down": "Plantaforma se inclina hacia abajo", "subtitles.block.big_dripleaf.tilt_up": "Plantaforma se inclina hacia arriba", "subtitles.block.blastfurnace.fire_crackle": "Crepitar de alto horno", "subtitles.block.brewing_stand.brew": "Burbujas de destiladora", "subtitles.block.bubble_column.bubble_pop": "Burbujas explotan", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON> ascendiente", "subtitles.block.bubble_column.upwards_inside": "Ascenso en corriente", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON><PERSON> descendiente", "subtitles.block.bubble_column.whirlpool_inside": "Descenso en corriente", "subtitles.block.button.click": "Chasquido de botón", "subtitles.block.cake.add_candle": "Torta se aplasta", "subtitles.block.campfire.crackle": "Crepitar de fogata", "subtitles.block.candle.crackle": "Crepitar de vela", "subtitles.block.candle.extinguish": "Vela se apaga", "subtitles.block.chest.close": "<PERSON><PERSON> cerrado", "subtitles.block.chest.locked": "<PERSON><PERSON> blo<PERSON>", "subtitles.block.chest.open": "<PERSON><PERSON> abie<PERSON>o", "subtitles.block.chorus_flower.death": "Flor de chorus se marchita", "subtitles.block.chorus_flower.grow": "Flor de chorus crece", "subtitles.block.comparator.click": "Chasquido de comparador", "subtitles.block.composter.empty": "Compostera vaciada", "subtitles.block.composter.fill": "Compostera llenada", "subtitles.block.composter.ready": "Compostera lista", "subtitles.block.conduit.activate": "Canalizador activado", "subtitles.block.conduit.ambient": "Latidos de canalizador", "subtitles.block.conduit.attack.target": "Canalizador ataca", "subtitles.block.conduit.deactivate": "Canalizador desactivado", "subtitles.block.copper_bulb.turn_off": "Lámpara de cobre se apaga", "subtitles.block.copper_bulb.turn_on": "Lámpara de cobre se enciende", "subtitles.block.copper_trapdoor.close": "Escotilla se cierra", "subtitles.block.copper_trapdoor.open": "Escotilla se abre", "subtitles.block.crafter.craft": "Fabricador fabrica", "subtitles.block.crafter.fail": "Fabricador falla", "subtitles.block.creaking_heart.hurt": "Corazon de crujidor grita", "subtitles.block.creaking_heart.idle": "Ruido inquietante", "subtitles.block.creaking_heart.spawn": "Corazón de crujidor se despierta", "subtitles.block.deadbush.idle": "Sonidos secos", "subtitles.block.decorated_pot.insert": "<PERSON><PERSON><PERSON><PERSON> decorado se llena", "subtitles.block.decorated_pot.insert_fail": "J<PERSON>rón decorado se tambalea", "subtitles.block.decorated_pot.shatter": "<PERSON><PERSON><PERSON><PERSON> decorado se rompe", "subtitles.block.dispenser.dispense": "Objeto dispensado", "subtitles.block.dispenser.fail": "Error de un dispensador", "subtitles.block.door.toggle": "Rechinido de puerta", "subtitles.block.dried_ghast.ambient": "<PERSON><PERSON><PERSON> <PERSON> jadea", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON> deshi<PERSON> se rehidrata", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON><PERSON> des<PERSON> se empapa", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON> des<PERSON>o se siente mejor", "subtitles.block.dry_grass.ambient": "Sonidos de viento", "subtitles.block.enchantment_table.use": "Mesa de encantamientos usada", "subtitles.block.end_portal.spawn": "Portal del End abierto", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON> de ender colocado", "subtitles.block.eyeblossom.close": "Miraflor se cierra", "subtitles.block.eyeblossom.idle": "<PERSON><PERSON><PERSON> susurra", "subtitles.block.eyeblossom.open": "Miraflor se abre", "subtitles.block.fence_gate.toggle": "Rechinido de <PERSON>ón", "subtitles.block.fire.ambient": "Crepitar del fuego", "subtitles.block.fire.extinguish": "Fuego extinguido", "subtitles.block.firefly_bush.idle": "Zumbido de luciérnaga", "subtitles.block.frogspawn.hatch": "Renacuajo eclosiona", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON>pit<PERSON> de <PERSON>o", "subtitles.block.generic.break": "Bloque roto", "subtitles.block.generic.fall": "Algo cae sobre un bloque", "subtitles.block.generic.footsteps": "Pasos", "subtitles.block.generic.hit": "Rotura de bloque", "subtitles.block.generic.place": "Bloque colocado", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON><PERSON> usada", "subtitles.block.growing_plant.crop": "<PERSON><PERSON> recortada", "subtitles.block.hanging_sign.waxed_interact_fail": "Cartel se tambalea", "subtitles.block.honey_block.slide": "Deslizamiento en bloque de miel", "subtitles.block.iron_trapdoor.close": "Escotilla cerrada", "subtitles.block.iron_trapdoor.open": "Escotilla abierta", "subtitles.block.lava.ambient": "Burbujeo de lava", "subtitles.block.lava.extinguish": "Silbido de lava", "subtitles.block.lever.click": "Chasquido de palanca", "subtitles.block.note_block.note": "Bloque musical", "subtitles.block.pale_hanging_moss.idle": "Ruido inquietante", "subtitles.block.piston.move": "Movimiento de pistón", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON>va goteando en un caldero", "subtitles.block.pointed_dripstone.drip_water": "Agua got<PERSON>do", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Agua gotea en un caldero", "subtitles.block.pointed_dripstone.land": "Estalactita se cae", "subtitles.block.portal.ambient": "Zumbido de portal", "subtitles.block.portal.travel": "Zumbido de portal se desvanece", "subtitles.block.portal.trigger": "Zumbido de portal se intensifica", "subtitles.block.pressure_plate.click": "Chasquido de placa de presión", "subtitles.block.pumpkin.carve": "Tijeras tallando", "subtitles.block.redstone_torch.burnout": "Antorcha arde", "subtitles.block.respawn_anchor.ambient": "Zumbido de nexo de reaparición", "subtitles.block.respawn_anchor.charge": "Nexo de reaparición cargado", "subtitles.block.respawn_anchor.deplete": "Nexo de reaparición pierde carga", "subtitles.block.respawn_anchor.set_spawn": "Nexo de reaparición establecido", "subtitles.block.sand.idle": "Sonidos arenosos", "subtitles.block.sand.wind": "Sonidos de viento", "subtitles.block.sculk.charge": "Sculk burbujea", "subtitles.block.sculk.spread": "Sculk se esparce", "subtitles.block.sculk_catalyst.bloom": "Catalizador de sculk brota", "subtitles.block.sculk_sensor.clicking": "Sensor de sculk detecta", "subtitles.block.sculk_sensor.clicking_stop": "Sensor sculk deja de detectar", "subtitles.block.sculk_shrieker.shriek": "<PERSON><PERSON><PERSON> de sculk chilla", "subtitles.block.shulker_box.close": "Caja de shulker se cierra", "subtitles.block.shulker_box.open": "Caja de shulker se abre", "subtitles.block.sign.waxed_interact_fail": "Cartel se balancea", "subtitles.block.smithing_table.use": "Mesa de herrería usada", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON> echa humo", "subtitles.block.sniffer_egg.crack": "<PERSON><PERSON> de sniffer se raja", "subtitles.block.sniffer_egg.hatch": "Huevo de sniffer eclosiona", "subtitles.block.sniffer_egg.plop": "Sniffer pone un huevo", "subtitles.block.sponge.absorb": "Esponja <PERSON>", "subtitles.block.sweet_berry_bush.pick_berries": "Bayas caen", "subtitles.block.trapdoor.close": "Escotilla se cierra", "subtitles.block.trapdoor.open": "Escotilla se abre", "subtitles.block.trapdoor.toggle": "Rechinido de escotilla", "subtitles.block.trial_spawner.about_to_spawn_item": "Objeto siniestro se prepara", "subtitles.block.trial_spawner.ambient": "Crepitar de generador de desafío", "subtitles.block.trial_spawner.ambient_charged": "Crepitar siniestro", "subtitles.block.trial_spawner.ambient_ominous": "Crepitar siniestro ", "subtitles.block.trial_spawner.charge_activate": "Presagio envuelve un generador de desafío", "subtitles.block.trial_spawner.close_shutter": "Generador de desafío se cierra", "subtitles.block.trial_spawner.detect_player": "Generador de desafío <PERSON>gado", "subtitles.block.trial_spawner.eject_item": "Generador de desafío expulsa objetos", "subtitles.block.trial_spawner.ominous_activate": "Presagio envuelve un generador de desafío", "subtitles.block.trial_spawner.open_shutter": "Generador de desafío se abre", "subtitles.block.trial_spawner.spawn_item": "Objet<PERSON> sin<PERSON> cae", "subtitles.block.trial_spawner.spawn_item_begin": "Objeto siniestro aparece", "subtitles.block.trial_spawner.spawn_mob": "Generador de desafío genera una criatura", "subtitles.block.tripwire.attach": "<PERSON><PERSON> at<PERSON>", "subtitles.block.tripwire.click": "Hilo se acciona", "subtitles.block.tripwire.detach": "<PERSON><PERSON>", "subtitles.block.vault.activate": "Arca activada", "subtitles.block.vault.ambient": "Crepitar de arc<PERSON>", "subtitles.block.vault.close_shutter": "Arca se cierra", "subtitles.block.vault.deactivate": "Arca desactivada", "subtitles.block.vault.eject_item": "Arca expulsa un objeto", "subtitles.block.vault.insert_item": "Arca se desbloquea", "subtitles.block.vault.insert_item_fail": "Arca rechaza un objeto", "subtitles.block.vault.open_shutter": "Arca se abre", "subtitles.block.vault.reject_rewarded_player": "Arca rechaza a un jugador", "subtitles.block.water.ambient": "Agua en movimiento", "subtitles.block.wet_sponge.dries": "Esponja se seca", "subtitles.chiseled_bookshelf.insert": "Libro colocado", "subtitles.chiseled_bookshelf.insert_enchanted": "Libro encantado colocado", "subtitles.chiseled_bookshelf.take": "Libro agarrado", "subtitles.chiseled_bookshelf.take_enchanted": "Libro encantado a<PERSON>", "subtitles.enchant.thorns.hit": "Picado con espinas", "subtitles.entity.allay.ambient_with_item": "Allay busca", "subtitles.entity.allay.ambient_without_item": "<PERSON>ay anhela", "subtitles.entity.allay.death": "<PERSON>ay muere", "subtitles.entity.allay.hurt": "Allay herido", "subtitles.entity.allay.item_given": "<PERSON>ay r<PERSON>e", "subtitles.entity.allay.item_taken": "<PERSON>ay agarra", "subtitles.entity.allay.item_thrown": "Allay tira", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.brush": "<PERSON><PERSON><PERSON> cepilla<PERSON>", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.armadillo.hurt_reduced": "Armadillo se protege", "subtitles.entity.armadillo.land": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON> se asoma", "subtitles.entity.armadillo.roll": "Armadillo se enrolla", "subtitles.entity.armadillo.scute_drop": "Armadillo suelta una escama", "subtitles.entity.armadillo.unroll_finish": "Armadillo se desenrolla", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON> se asoma", "subtitles.entity.armor_stand.fall": "Caída de un objeto", "subtitles.entity.arrow.hit": "Impacto de flecha", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON>r golpeado", "subtitles.entity.arrow.shoot": "Flecha disparada", "subtitles.entity.axolotl.attack": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON><PERSON> muere", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.axolotl.idle_air": "<PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.idle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.splash": "<PERSON><PERSON><PERSON><PERSON> salpica agua", "subtitles.entity.axolotl.swim": "<PERSON><PERSON><PERSON><PERSON> nada", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> muere", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.takeoff": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bee.ambient": "Zumbido de abeja", "subtitles.entity.bee.death": "<PERSON><PERSON> muere", "subtitles.entity.bee.hurt": "<PERSON><PERSON>", "subtitles.entity.bee.loop": "Zumbido de abeja", "subtitles.entity.bee.loop_aggressive": "Zumbido de abeja furiosa", "subtitles.entity.bee.pollinate": "Zumbido de abeja feliz", "subtitles.entity.bee.sting": "Abeja pica", "subtitles.entity.blaze.ambient": "Respiros de blaze", "subtitles.entity.blaze.burn": "Crepitar de blaze", "subtitles.entity.blaze.death": "<PERSON> muere", "subtitles.entity.blaze.hurt": "<PERSON> herido", "subtitles.entity.blaze.shoot": "<PERSON> dispara", "subtitles.entity.boat.paddle_land": "Remando en tierra", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Esqueleto pantanoso traquetea", "subtitles.entity.bogged.death": "Esqueleto pantanoso muere", "subtitles.entity.bogged.hurt": "Esqueleto pantanoso herido", "subtitles.entity.breeze.charge": "Breeze carga", "subtitles.entity.breeze.death": "<PERSON><PERSON> muere", "subtitles.entity.breeze.deflect": "<PERSON><PERSON>", "subtitles.entity.breeze.hurt": "Breeze herido", "subtitles.entity.breeze.idle_air": "<PERSON><PERSON> vuela", "subtitles.entity.breeze.idle_ground": "Breeze zumba", "subtitles.entity.breeze.inhale": "Breeze inhala", "subtitles.entity.breeze.jump": "Breeze salta", "subtitles.entity.breeze.land": "Breeze aterriza", "subtitles.entity.breeze.shoot": "<PERSON><PERSON> dispara", "subtitles.entity.breeze.slide": "<PERSON>ze se desliza", "subtitles.entity.breeze.whirl": "<PERSON>ze gira", "subtitles.entity.breeze.wind_burst": "Carga ventosa estalla", "subtitles.entity.camel.ambient": "Camello ronca", "subtitles.entity.camel.dash": "Camello se lanza", "subtitles.entity.camel.dash_ready": "Camello se recupera", "subtitles.entity.camel.death": "Camello muere", "subtitles.entity.camel.eat": "Camello come", "subtitles.entity.camel.hurt": "Camello herido", "subtitles.entity.camel.saddle": "<PERSON>ura equipada", "subtitles.entity.camel.sit": "Camello se sienta", "subtitles.entity.camel.stand": "Camello se levanta", "subtitles.entity.camel.step": "Pasos de camello", "subtitles.entity.camel.step_sand": "Pasos de camello sobre arena", "subtitles.entity.cat.ambient": "Maullidos de gato", "subtitles.entity.cat.beg_for_food": "Gato pide comida", "subtitles.entity.cat.death": "<PERSON><PERSON> muere", "subtitles.entity.cat.eat": "Gato come", "subtitles.entity.cat.hiss": "Bufido de gato", "subtitles.entity.cat.hurt": "Gato herido", "subtitles.entity.cat.purr": "Ronroneos de gato", "subtitles.entity.chicken.ambient": "Cacareo de gallina", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.chicken.egg": "Una gallina pone un huevo", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.cod.death": "Bacalao muere", "subtitles.entity.cod.flop": "Bacalao aletea", "subtitles.entity.cod.hurt": "Bacalao herido", "subtitles.entity.cow.ambient": "Mugido de vaca", "subtitles.entity.cow.death": "Vaca muere", "subtitles.entity.cow.hurt": "Vaca herida", "subtitles.entity.cow.milk": "Vaca ordeñada", "subtitles.entity.creaking.activate": "Crujidor observa", "subtitles.entity.creaking.ambient": "Crujidor cruje", "subtitles.entity.creaking.attack": "Cru<PERSON>or ataca", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON><PERSON> se <PERSON>a", "subtitles.entity.creaking.death": "<PERSON><PERSON><PERSON><PERSON> muere", "subtitles.entity.creaking.freeze": "Crujidor se frena", "subtitles.entity.creaking.spawn": "Crujidor a<PERSON>", "subtitles.entity.creaking.sway": "<PERSON><PERSON><PERSON><PERSON> go<PERSON>", "subtitles.entity.creaking.twitch": "Crujidor se retuerce", "subtitles.entity.creaking.unfreeze": "Crujidor se mueve", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON><PERSON> creeper", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON><PERSON>ín", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON> salta", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.dolphin.splash": "Delfín salpica agua", "subtitles.entity.dolphin.swim": "Delfín nada", "subtitles.entity.donkey.ambient": "Rebuzno de burro", "subtitles.entity.donkey.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.chest": "Baúl equipado a un burro", "subtitles.entity.donkey.death": "<PERSON><PERSON> muere", "subtitles.entity.donkey.eat": "<PERSON><PERSON> come", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> her<PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON> salta", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON> lanzando tridente", "subtitles.entity.drowned.step": "Pasos de ahogado", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON> nada", "subtitles.entity.egg.throw": "<PERSON><PERSON> lanza<PERSON>", "subtitles.entity.elder_guardian.ambient": "Quejido de guardián an<PERSON>o", "subtitles.entity.elder_guardian.ambient_land": "Aleteo de guardián an<PERSON>o", "subtitles.entity.elder_guardian.curse": "Maldición de guardián anciano", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON> anciano muere", "subtitles.entity.elder_guardian.flop": "<PERSON><PERSON><PERSON> anciano ale<PERSON>a", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON> anciano herido", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.ender_dragon.flap": "Dr<PERSON>ón aletea", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON> dispara", "subtitles.entity.ender_eye.death": "<PERSON><PERSON> de ender cae", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> de ender lanzado", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> de ender la<PERSON>", "subtitles.entity.enderman.ambient": "Vwoopeo de end<PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON> muere", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> her<PERSON>", "subtitles.entity.enderman.scream": "Enderman grita", "subtitles.entity.enderman.stare": "Grito de enderman", "subtitles.entity.enderman.teleport": "Enderman se teletransporta", "subtitles.entity.endermite.ambient": "Endermite se arrastra", "subtitles.entity.endermite.death": "Endermite muere", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.evoker.ambient": "<PERSON><PERSON>uro de evocador", "subtitles.entity.evoker.cast_spell": "Evocador lanza un hechizo", "subtitles.entity.evoker.celebrate": "Evocador festeja", "subtitles.entity.evoker.death": "Evoc<PERSON> muere", "subtitles.entity.evoker.hurt": "Evocador herido", "subtitles.entity.evoker.prepare_attack": "Evocador prepara un ataque", "subtitles.entity.evoker.prepare_summon": "Evocador prepara una invocación", "subtitles.entity.evoker.prepare_wololo": "Evocador prepara un hechizo", "subtitles.entity.evoker_fangs.attack": "Rotura de colmillos", "subtitles.entity.experience_orb.pickup": "Experiencia agarrada", "subtitles.entity.firework_rocket.blast": "Explosión de fuegos artificiales", "subtitles.entity.firework_rocket.launch": "Lanzamiento de fuegos artificiales", "subtitles.entity.firework_rocket.twinkle": "Destellos de fuegos artificiales", "subtitles.entity.fish.swim": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.fishing_bobber.retrieve": "Carnada recuperada", "subtitles.entity.fishing_bobber.splash": "Salpicadura de carnada", "subtitles.entity.fishing_bobber.throw": "Carnada lanzada", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON> se enoja", "subtitles.entity.fox.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.bite": "<PERSON><PERSON><PERSON> muer<PERSON>", "subtitles.entity.fox.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.fox.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.fox.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.sleep": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.sniff": "Zorro olfatea", "subtitles.entity.fox.spit": "<PERSON><PERSON><PERSON> escupe", "subtitles.entity.fox.teleport": "Zorro se teletransporta", "subtitles.entity.frog.ambient": "<PERSON> croa", "subtitles.entity.frog.death": "<PERSON> muere", "subtitles.entity.frog.eat": "<PERSON> come", "subtitles.entity.frog.hurt": "Rana <PERSON>", "subtitles.entity.frog.lay_spawn": "<PERSON>", "subtitles.entity.frog.long_jump": "<PERSON>a", "subtitles.entity.generic.big_fall": "Caída de un objeto", "subtitles.entity.generic.burn": "Algo se incendia", "subtitles.entity.generic.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.drink": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.eat": "Comiendo", "subtitles.entity.generic.explode": "Explosión", "subtitles.entity.generic.extinguish_fire": "Fuego extinguido", "subtitles.entity.generic.hurt": "Algo te hiere", "subtitles.entity.generic.small_fall": "Leve impacto contra el piso", "subtitles.entity.generic.splash": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.swim": "Na<PERSON>do", "subtitles.entity.generic.wind_burst": "Carga ventosa estalla", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> dispara", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON><PERSON> muere", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.add_item": "<PERSON> lumi<PERSON> ll<PERSON>", "subtitles.entity.glow_item_frame.break": "<PERSON> lumi<PERSON>o se rompe", "subtitles.entity.glow_item_frame.place": "<PERSON> lumi<PERSON> colocado", "subtitles.entity.glow_item_frame.remove_item": "<PERSON> lumi<PERSON> vaciado", "subtitles.entity.glow_item_frame.rotate_item": "<PERSON> lumi<PERSON> girado", "subtitles.entity.glow_squid.ambient": "Calamar luminoso nada", "subtitles.entity.glow_squid.death": "Calamar luminoso muere", "subtitles.entity.glow_squid.hurt": "Calamar luminoso herido", "subtitles.entity.glow_squid.squirt": "Calamar luminoso dispara tinta", "subtitles.entity.goat.ambient": "Cabra grita", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.goat.horn_break": "Cuerno de cabra se desprende", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> herida", "subtitles.entity.goat.long_jump": "<PERSON><PERSON><PERSON> salta", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.prepare_ram": "Cabra se prepara", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON> embis<PERSON>", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON><PERSON> brama", "subtitles.entity.goat.step": "Pasos de cabra", "subtitles.entity.guardian.ambient": "Quejido de guardián", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.attack": "Guardián dispara", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON><PERSON> feliz can<PERSON>", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON> feliz muere", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON> feliz listo", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON><PERSON> feliz se detiene", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON> feliz herido", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.angry": "Gruñ<PERSON> de hoglin furioso", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> se zombifica", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON>n her<PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> retrocede", "subtitles.entity.hoglin.step": "Pasos de hoglin", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.armor": "Armadura para caballo equipada", "subtitles.entity.horse.breathe": "Respiros de caballo", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.horse.gallop": "Galopes de caballo", "subtitles.entity.horse.hurt": "Caballo herido", "subtitles.entity.horse.jump": "<PERSON><PERSON>llo saltando", "subtitles.entity.horse.saddle": "<PERSON>ura equipada", "subtitles.entity.husk.ambient": "Quejido de zombi momificado", "subtitles.entity.husk.converted_to_zombie": "Zombi se desmomifica", "subtitles.entity.husk.death": "<PERSON><PERSON><PERSON> momificado muere", "subtitles.entity.husk.hurt": "<PERSON><PERSON><PERSON> momificado herido", "subtitles.entity.illusioner.ambient": "Murmuro de ilusionista", "subtitles.entity.illusioner.cast_spell": "Ilusionista lanza un hechizo", "subtitles.entity.illusioner.death": "Ilusionista muere", "subtitles.entity.illusioner.hurt": "Ilusionista herido", "subtitles.entity.illusioner.mirror_move": "Ilusionista se desplaza", "subtitles.entity.illusioner.prepare_blindness": "Ilusionista se prepara para cegar", "subtitles.entity.illusioner.prepare_mirror": "Ilusionista prepara un espejismo", "subtitles.entity.iron_golem.attack": "Gólem de hierro ataca", "subtitles.entity.iron_golem.damage": "Gólem de hierro se rompe", "subtitles.entity.iron_golem.death": "Gólem de hierro muere", "subtitles.entity.iron_golem.hurt": "Gólem de hierro herido", "subtitles.entity.iron_golem.repair": "Gólem de hierro se arregla", "subtitles.entity.item.break": "Objet<PERSON> destruido", "subtitles.entity.item.pickup": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.add_item": "Objeto colocado en un marco", "subtitles.entity.item_frame.break": "<PERSON> se rompe", "subtitles.entity.item_frame.place": "<PERSON>", "subtitles.entity.item_frame.remove_item": "Objeto retirado de un marco", "subtitles.entity.item_frame.rotate_item": "Chasquido de ma<PERSON>o", "subtitles.entity.leash_knot.break": "Nudo de la rienda se rompe", "subtitles.entity.leash_knot.place": "Nudo de la rienda se ata", "subtitles.entity.lightning_bolt.impact": "Rayo impactado", "subtitles.entity.lightning_bolt.thunder": "Estruendos de truenos", "subtitles.entity.llama.ambient": "<PERSON><PERSON>", "subtitles.entity.llama.angry": "Balido de llama furiosa", "subtitles.entity.llama.chest": "Baúl equipado a una llama", "subtitles.entity.llama.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.llama.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.llama.hurt": "<PERSON><PERSON><PERSON> herida", "subtitles.entity.llama.spit": "Llama escupe", "subtitles.entity.llama.step": "Pasos de llama", "subtitles.entity.llama.swag": "Llama decorada", "subtitles.entity.magma_cube.death": "Cubo de magma muere", "subtitles.entity.magma_cube.hurt": "Cubo de magma herido", "subtitles.entity.magma_cube.squish": "Cubo de magma se aplasta", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> de <PERSON>", "subtitles.entity.minecart.inside_underwater": "Ruido de carrito bajo el agua", "subtitles.entity.minecart.riding": "Carrito en movimiento", "subtitles.entity.mooshroom.convert": "Mooshroom se transforma", "subtitles.entity.mooshroom.eat": "Mooshroom come", "subtitles.entity.mooshroom.milk": "Mooshroom ordeñada", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom ordeñada sospechosamente", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON> de <PERSON>ula", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON> de <PERSON>ula", "subtitles.entity.mule.chest": "<PERSON>úl equipado a una mula", "subtitles.entity.mule.death": "<PERSON><PERSON> muere", "subtitles.entity.mule.eat": "<PERSON><PERSON> come", "subtitles.entity.mule.hurt": "<PERSON><PERSON> herida", "subtitles.entity.mule.jump": "<PERSON><PERSON> salta", "subtitles.entity.painting.break": "Cuadro se rompe", "subtitles.entity.painting.place": "Cuadro puesto", "subtitles.entity.panda.aggressive_ambient": "<PERSON><PERSON>", "subtitles.entity.panda.ambient": "<PERSON><PERSON><PERSON>da", "subtitles.entity.panda.bite": "<PERSON>da muerde", "subtitles.entity.panda.cant_breed": "Balido de panda", "subtitles.entity.panda.death": "<PERSON>da muere", "subtitles.entity.panda.eat": "Panda come", "subtitles.entity.panda.hurt": "Panda herido", "subtitles.entity.panda.pre_sneeze": "Panda se rasca la nariz", "subtitles.entity.panda.sneeze": "Panda estorn<PERSON>", "subtitles.entity.panda.step": "Pasos de panda", "subtitles.entity.panda.worried_ambient": "Gruñido de <PERSON>", "subtitles.entity.parrot.ambient": "<PERSON><PERSON> habla", "subtitles.entity.parrot.death": "<PERSON><PERSON> muere", "subtitles.entity.parrot.eats": "<PERSON>ro come", "subtitles.entity.parrot.fly": "<PERSON><PERSON> vuela", "subtitles.entity.parrot.hurts": "<PERSON>ro herido", "subtitles.entity.parrot.imitate.blaze": "<PERSON>ro imita a un blaze", "subtitles.entity.parrot.imitate.bogged": "Loro imita a un esqueleto pantanoso", "subtitles.entity.parrot.imitate.breeze": "Loro imita a un breeze", "subtitles.entity.parrot.imitate.creaking": "<PERSON>ro imita a un crujidor", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON> imita a un creeper", "subtitles.entity.parrot.imitate.drowned": "<PERSON>ro imita a un ahogado", "subtitles.entity.parrot.imitate.elder_guardian": "Loro imita a un guardián", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON>ro imita al dragón", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON> imita a un endermite", "subtitles.entity.parrot.imitate.evoker": "<PERSON>ro imita a un evocador", "subtitles.entity.parrot.imitate.ghast": "<PERSON>ro imita a un ghast", "subtitles.entity.parrot.imitate.guardian": "Loro imita a un guardián", "subtitles.entity.parrot.imitate.hoglin": "Loro imita a un hoglin", "subtitles.entity.parrot.imitate.husk": "Loro imita a un zombi momificado", "subtitles.entity.parrot.imitate.illusioner": "Loro imita a un ilusionista", "subtitles.entity.parrot.imitate.magma_cube": "Loro imita a un cubo de magma", "subtitles.entity.parrot.imitate.phantom": "<PERSON>ro imita a un phantom", "subtitles.entity.parrot.imitate.piglin": "<PERSON>ro imita a un piglin", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON>ro imita a un piglin", "subtitles.entity.parrot.imitate.pillager": "Loro imita a un saqueador", "subtitles.entity.parrot.imitate.ravager": "Loro imita a un devastador", "subtitles.entity.parrot.imitate.shulker": "<PERSON>ro imita a un shulker", "subtitles.entity.parrot.imitate.silverfish": "Loro imita a un pez de plata", "subtitles.entity.parrot.imitate.skeleton": "Loro imita a un esqueleto", "subtitles.entity.parrot.imitate.slime": "Loro imita a un slime", "subtitles.entity.parrot.imitate.spider": "Loro imita a una araña", "subtitles.entity.parrot.imitate.stray": "Loro imita a un esqueleto glacial", "subtitles.entity.parrot.imitate.vex": "Loro imita a un vex", "subtitles.entity.parrot.imitate.vindicator": "Loro imita a un vindicador", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON> imita a un warden", "subtitles.entity.parrot.imitate.witch": "Loro imita a una bruja", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON> imita al <PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "Loro imita a un esqueleto del Wither", "subtitles.entity.parrot.imitate.zoglin": "<PERSON>ro imita a un zoglin", "subtitles.entity.parrot.imitate.zombie": "<PERSON>ro imita a un zombi", "subtitles.entity.parrot.imitate.zombie_villager": "Loro imita a un aldeano zombi", "subtitles.entity.phantom.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.phantom.bite": "Phantom muerde", "subtitles.entity.phantom.death": "Phantom muere", "subtitles.entity.phantom.flap": "Phantom aletea", "subtitles.entity.phantom.hurt": "Phantom herido", "subtitles.entity.phantom.swoop": "Phantom ataca", "subtitles.entity.pig.ambient": "<PERSON><PERSON><PERSON><PERSON>an<PERSON>", "subtitles.entity.pig.death": "<PERSON><PERSON> muere", "subtitles.entity.pig.hurt": "<PERSON><PERSON>", "subtitles.entity.pig.saddle": "<PERSON>ura equipada", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> admira un ítem", "subtitles.entity.piglin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.piglin.angry": "<PERSON><PERSON><PERSON><PERSON> de piglin furioso", "subtitles.entity.piglin.celebrate": "<PERSON>lin celebra", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> se zombifica", "subtitles.entity.piglin.death": "<PERSON><PERSON> muere", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> her<PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON><PERSON> de piglin con envidia", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> retrocede", "subtitles.entity.piglin.step": "Pasos de piglin", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON><PERSON><PERSON> piglin bruto", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON><PERSON><PERSON> de piglin bruto furioso", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON><PERSON> bruto se zombifica", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> bruto muere", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> bruto herido", "subtitles.entity.piglin_brute.step": "<PERSON>s de piglin bruto", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.pillager.celebrate": "Saqueador festeja", "subtitles.entity.pillager.death": "<PERSON>que<PERSON> muere", "subtitles.entity.pillager.hurt": "Saqueador herido", "subtitles.entity.player.attack.crit": "Ataque crítico", "subtitles.entity.player.attack.knockback": "Ataque con empuje", "subtitles.entity.player.attack.strong": "Ataque fuerte", "subtitles.entity.player.attack.sweep": "Ataque con barrido", "subtitles.entity.player.attack.weak": "Ataque débil", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON> repite", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.player.freeze_hurt": "Jugador se congela", "subtitles.entity.player.hurt": "<PERSON><PERSON>r herido", "subtitles.entity.player.hurt_drown": "Jugado<PERSON>(a) se ahoga", "subtitles.entity.player.hurt_on_fire": "Jugador(a) arde", "subtitles.entity.player.levelup": "Un jugador sube de nivel", "subtitles.entity.player.teleport": "Jugador se teletransporta", "subtitles.entity.polar_bear.ambient": "Quejido de oso polar", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON><PERSON> de oso polar tararea", "subtitles.entity.polar_bear.death": "<PERSON>so polar muere", "subtitles.entity.polar_bear.hurt": "Oso polar herido", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON> de <PERSON>", "subtitles.entity.potion.splash": "<PERSON><PERSON><PERSON> rota", "subtitles.entity.potion.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_out": "Pez globo se desinfla", "subtitles.entity.puffer_fish.blow_up": "Pez globo se infla", "subtitles.entity.puffer_fish.death": "Pez globo muere", "subtitles.entity.puffer_fish.flop": "Pez globo aletea", "subtitles.entity.puffer_fish.hurt": "Pez globo herido", "subtitles.entity.puffer_fish.sting": "Pez globo pica", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON>o ataca", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON>o her<PERSON>", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON> salta", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.attack": "Devast<PERSON> muerde", "subtitles.entity.ravager.celebrate": "Devastador festeja", "subtitles.entity.ravager.death": "Devast<PERSON> muere", "subtitles.entity.ravager.hurt": "Devastador herido", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.step": "Pasos de devastador", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON>", "subtitles.entity.salmon.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.salmon.flop": "Salmón aletea", "subtitles.entity.salmon.hurt": "Salmón herido", "subtitles.entity.sheep.ambient": "Balido de oveja", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.sheep.hurt": "Oveja herida", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> se cierra", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.shulker.open": "<PERSON>lk<PERSON> se abre", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> dispara", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> se teletransporta", "subtitles.entity.shulker_bullet.hit": "Proyectil de <PERSON> explota", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON><PERSON> de Shulker se rompe", "subtitles.entity.silverfish.ambient": "<PERSON><PERSON><PERSON><PERSON> de pez de plata", "subtitles.entity.silverfish.death": "Pez de plata muere", "subtitles.entity.silverfish.hurt": "Pez de plata herido", "subtitles.entity.skeleton.ambient": "Traqueteo de esqueleto", "subtitles.entity.skeleton.converted_to_stray": "Esqueleto se convierte en Esqueleto Glacial", "subtitles.entity.skeleton.death": "Esqueleto muere", "subtitles.entity.skeleton.hurt": "Esqueleto herido", "subtitles.entity.skeleton.shoot": "Esqueleto dispara", "subtitles.entity.skeleton_horse.ambient": "<PERSON><PERSON><PERSON> de caballo esqueleto", "subtitles.entity.skeleton_horse.death": "C<PERSON>llo esqueleto muere", "subtitles.entity.skeleton_horse.hurt": "Caballo esqueleto herido", "subtitles.entity.skeleton_horse.jump_water": "Caballo esqueleto salta", "subtitles.entity.skeleton_horse.swim": "Caballo esqueleto nada", "subtitles.entity.slime.attack": "Slime ataca", "subtitles.entity.slime.death": "Slime muere", "subtitles.entity.slime.hurt": "Slime herido", "subtitles.entity.slime.squish": "Slime se aplasta", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.sniffer.digging": "Sniffer cava", "subtitles.entity.sniffer.digging_stop": "<PERSON>ni<PERSON> se levanta", "subtitles.entity.sniffer.drop_seed": "<PERSON>niffer suelta una semilla", "subtitles.entity.sniffer.eat": "Sniffer come", "subtitles.entity.sniffer.egg_crack": "<PERSON><PERSON> de sniffer se raja", "subtitles.entity.sniffer.egg_hatch": "Huevo de sniffer eclosiona", "subtitles.entity.sniffer.happy": "Sniffer se emociona", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON> huele", "subtitles.entity.sniffer.searching": "Sniffer busca", "subtitles.entity.sniffer.sniffing": "Sniffer olfatea", "subtitles.entity.sniffer.step": "Pasos de sniffer", "subtitles.entity.snow_golem.death": "Gólem de nieve muere", "subtitles.entity.snow_golem.hurt": "Gólem de nieve herido", "subtitles.entity.snowball.throw": "Bola de nieve lanzada", "subtitles.entity.spider.ambient": "Siseo de araña", "subtitles.entity.spider.death": "<PERSON><PERSON> muere", "subtitles.entity.spider.hurt": "<PERSON><PERSON> herida", "subtitles.entity.squid.ambient": "Calamar nada", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.squid.squirt": "Calamar dispara tinta", "subtitles.entity.stray.ambient": "Traqueteo de esqueleto glacial", "subtitles.entity.stray.death": "Esqueleto glacial muere", "subtitles.entity.stray.hurt": "Esqueleto glacial herido", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.strider.eat": "Strider come", "subtitles.entity.strider.happy": "Stride<PERSON> canta", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON>r", "subtitles.entity.strider.retreat": "Strider retrocede", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON> muere", "subtitles.entity.tadpole.flop": "Renac<PERSON>jo chapotea", "subtitles.entity.tadpole.grow_up": "Renacuajo crece", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.tnt.primed": "Mecha de TNT", "subtitles.entity.tropical_fish.death": "Pez tropical muere", "subtitles.entity.tropical_fish.flop": "Pez tropical chapotea", "subtitles.entity.tropical_fish.hurt": "Pez tropical herido", "subtitles.entity.turtle.ambient_land": "Gruñido de tortuga", "subtitles.entity.turtle.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.turtle.death_baby": "Cría de tortuga muere", "subtitles.entity.turtle.egg_break": "Huevo de tortuga se rompe", "subtitles.entity.turtle.egg_crack": "Huevo de tortuga se raja", "subtitles.entity.turtle.egg_hatch": "Huevo de tortuga eclosiona", "subtitles.entity.turtle.hurt": "<PERSON><PERSON><PERSON> herida", "subtitles.entity.turtle.hurt_baby": "Cría de tortuga herida", "subtitles.entity.turtle.lay_egg": "Una tortuga pone un huevo", "subtitles.entity.turtle.shamble": "<PERSON><PERSON><PERSON> repta", "subtitles.entity.turtle.shamble_baby": "<PERSON>ría de tortuga repta", "subtitles.entity.turtle.swim": "Tortuga nada", "subtitles.entity.vex.ambient": "Ruido de vex", "subtitles.entity.vex.charge": "<PERSON><PERSON><PERSON> de vex", "subtitles.entity.vex.death": "Vex muere", "subtitles.entity.vex.hurt": "Vex herido", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.celebrate": "Aldeano festeja", "subtitles.entity.villager.death": "Aldeano muere", "subtitles.entity.villager.hurt": "Aldeano herido", "subtitles.entity.villager.no": "Aldeano no acepta", "subtitles.entity.villager.trade": "Aldeano intercambia", "subtitles.entity.villager.work_armorer": "Herrero de armaduras trabaja", "subtitles.entity.villager.work_butcher": "Carnicero trabaja", "subtitles.entity.villager.work_cartographer": "Cartógrafo trabaja", "subtitles.entity.villager.work_cleric": "Sacerdote trabaja", "subtitles.entity.villager.work_farmer": "Granjero trabaja", "subtitles.entity.villager.work_fisherman": "Pescador trabaja", "subtitles.entity.villager.work_fletcher": "Flechero trabaja", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON> trabaja", "subtitles.entity.villager.work_librarian": "Bibliotecario trabaja", "subtitles.entity.villager.work_mason": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_shepherd": "<PERSON> t<PERSON>", "subtitles.entity.villager.work_toolsmith": "Herrero de herramientas trabaja", "subtitles.entity.villager.work_weaponsmith": "Herrero de armas trabaja", "subtitles.entity.villager.yes": "Aldeano acepta", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON> de vindicador", "subtitles.entity.vindicator.celebrate": "Vindicador festeja", "subtitles.entity.vindicator.death": "Vindicador muere", "subtitles.entity.vindicator.hurt": "Vindicador herido", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON> de comerciante nómada", "subtitles.entity.wandering_trader.death": "Comerciante nómada muere", "subtitles.entity.wandering_trader.disappeared": "Comerciante nómada desaparece", "subtitles.entity.wandering_trader.drink_milk": "Comerciante nómada toma leche", "subtitles.entity.wandering_trader.drink_potion": "Comerciante nómada toma una poción", "subtitles.entity.wandering_trader.hurt": "Comerciante nómada herido", "subtitles.entity.wandering_trader.no": "Comerciante nómada rechaza", "subtitles.entity.wandering_trader.reappeared": "Comerciante nómada aparece", "subtitles.entity.wandering_trader.trade": "Comerciante nómada vende", "subtitles.entity.wandering_trader.yes": "Comerciante nómada acepta", "subtitles.entity.warden.agitated": "<PERSON> grita enojado", "subtitles.entity.warden.ambient": "<PERSON> se queja", "subtitles.entity.warden.angry": "Warden se enfurece", "subtitles.entity.warden.attack_impact": "Warden ataca", "subtitles.entity.warden.death": "<PERSON> muere", "subtitles.entity.warden.dig": "Warden cava", "subtitles.entity.warden.emerge": "Warden emerge", "subtitles.entity.warden.heartbeat": "<PERSON><PERSON><PERSON> de warden late", "subtitles.entity.warden.hurt": "Warden herido", "subtitles.entity.warden.listening": "<PERSON> detecta", "subtitles.entity.warden.listening_angry": "Warden detecta furioso", "subtitles.entity.warden.nearby_close": "Warden se acerca", "subtitles.entity.warden.nearby_closer": "Warden a<PERSON>", "subtitles.entity.warden.nearby_closest": "Warden se aproxima", "subtitles.entity.warden.roar": "<PERSON> ruge", "subtitles.entity.warden.sniff": "Warden olfatea", "subtitles.entity.warden.sonic_boom": "<PERSON> descar<PERSON>", "subtitles.entity.warden.sonic_charge": "Warden carga", "subtitles.entity.warden.step": "Pasos de warden", "subtitles.entity.warden.tendril_clicks": "Antenas de warden chasquean", "subtitles.entity.wind_charge.throw": "Carga ventosa lanzada", "subtitles.entity.wind_charge.wind_burst": "Carga ventosa estalla", "subtitles.entity.witch.ambient": "<PERSON><PERSON>", "subtitles.entity.witch.celebrate": "Bruja festeja", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.witch.drink": "B<PERSON>ja to<PERSON>o", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON> herida", "subtitles.entity.witch.throw": "Bruja tira pociones", "subtitles.entity.wither.ambient": "<PERSON><PERSON> se enoja", "subtitles.entity.wither.death": "<PERSON><PERSON> muere", "subtitles.entity.wither.hurt": "Wither herido", "subtitles.entity.wither.shoot": "Wither ataca", "subtitles.entity.wither.spawn": "<PERSON><PERSON> liberado", "subtitles.entity.wither_skeleton.ambient": "Traqueteo de esqueleto <PERSON>er", "subtitles.entity.wither_skeleton.death": "Esqueleto del Wither muere", "subtitles.entity.wither_skeleton.hurt": "Esqueleto del Wither herido", "subtitles.entity.wolf.ambient": "<PERSON><PERSON>", "subtitles.entity.wolf.bark": "Ladrido de lobo", "subtitles.entity.wolf.death": "Lobo muere", "subtitles.entity.wolf.growl": "Gruñ<PERSON> de <PERSON>", "subtitles.entity.wolf.hurt": "Lobo herido", "subtitles.entity.wolf.pant": "Lobo jadea", "subtitles.entity.wolf.shake": "Lobo sacudiéndose", "subtitles.entity.wolf.whine": "Lloriqueo de lobo", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.angry": "Gru<PERSON><PERSON> de z<PERSON>lin furioso", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.zoglin.step": "Pasos de z<PERSON>lin", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON> go<PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON> rota", "subtitles.entity.zombie.converted_to_drowned": "Zombi se convierte en ahogado", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.zombie.destroy_egg": "Huevo de tortuga pisoteado", "subtitles.entity.zombie.hurt": "<PERSON><PERSON>i herido", "subtitles.entity.zombie.infect": "Zombi infectando", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON><PERSON> de caballo zombi", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON>llo zombi muere", "subtitles.entity.zombie_horse.hurt": "Caballo zombi herido", "subtitles.entity.zombie_villager.ambient": "Gru<PERSON><PERSON> de aldeano z<PERSON>i", "subtitles.entity.zombie_villager.converted": "Aldeano zombi se cura", "subtitles.entity.zombie_villager.cure": "Aldeano zombi se agita", "subtitles.entity.zombie_villager.death": "Aldeano zombi muere", "subtitles.entity.zombie_villager.hurt": "Aldeano zombi herido", "subtitles.entity.zombified_piglin.ambient": "Gruñ<PERSON> de piglin zombificado", "subtitles.entity.zombified_piglin.angry": "Gruñ<PERSON> de piglin zombificado furioso", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON> zombificado muere", "subtitles.entity.zombified_piglin.hurt": "<PERSON><PERSON> zombificado herido", "subtitles.event.mob_effect.bad_omen": "Un presagio se manifiesta", "subtitles.event.mob_effect.raid_omen": "Invasión se avecina", "subtitles.event.mob_effect.trial_omen": "Desafío siniestro se avecina", "subtitles.event.raid.horn": "<PERSON><PERSON>", "subtitles.item.armor.equip": "Armadura equipada", "subtitles.item.armor.equip_chain": "Tintineo de armadura de cota de malla", "subtitles.item.armor.equip_diamond": "Tintineo de armadura de diamante", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON> de elytra", "subtitles.item.armor.equip_gold": "Tintineo de armadura de oro", "subtitles.item.armor.equip_iron": "Tintineo de armadura de hierro", "subtitles.item.armor.equip_leather": "Frufrú de armadura de cuero", "subtitles.item.armor.equip_netherite": "Tintineo de armadura de netherita", "subtitles.item.armor.equip_turtle": "Caparazón de tortuga equipado", "subtitles.item.armor.equip_wolf": "Armadura para lobo equipada", "subtitles.item.armor.unequip_wolf": "Armadura para lobo se suelta", "subtitles.item.axe.scrape": "<PERSON><PERSON> raspa", "subtitles.item.axe.strip": "<PERSON><PERSON> raspan<PERSON>", "subtitles.item.axe.wax_off": "<PERSON><PERSON> removida", "subtitles.item.bone_meal.use": "Polvo de hueso cruje", "subtitles.item.book.page_turn": "Cambio de página", "subtitles.item.book.put": "Libro colocado", "subtitles.item.bottle.empty": "Botella vaciada", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON> ll<PERSON>", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "Cepillando grava", "subtitles.item.brush.brushing.gravel.complete": "Cepillado de grava terminado", "subtitles.item.brush.brushing.sand": "Cepillando arena", "subtitles.item.brush.brushing.sand.complete": "Cepillado de arena terminado", "subtitles.item.bucket.empty": "Balde vaciado", "subtitles.item.bucket.fill": "<PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "<PERSON><PERSON><PERSON><PERSON> recogido", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON> capturado", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON><PERSON> capturado", "subtitles.item.bundle.drop_contents": "Bolsa vaciada", "subtitles.item.bundle.insert": "Objeto empa<PERSON>ado", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON>a llena", "subtitles.item.bundle.remove_one": "Ob<PERSON><PERSON>", "subtitles.item.chorus_fruit.teleport": "Jugador se teletransporta", "subtitles.item.crop.plant": "<PERSON>lla cultivada", "subtitles.item.crossbow.charge": "Ballesta cargando", "subtitles.item.crossbow.hit": "Impacto de flecha", "subtitles.item.crossbow.load": "Ballesta cargada", "subtitles.item.crossbow.shoot": "Disparo de ballesta", "subtitles.item.dye.use": "Tinte mancha", "subtitles.item.elytra.flying": "Silbido", "subtitles.item.firecharge.use": "Zumbido de bola de fuego", "subtitles.item.flintandsteel.use": "<PERSON><PERSON><PERSON><PERSON> de en<PERSON>ed<PERSON>", "subtitles.item.glow_ink_sac.use": "Saco de tinta luminosa mancha", "subtitles.item.goat_horn.play": "Cuerno de cabra suena", "subtitles.item.hoe.till": "<PERSON><PERSON><PERSON>", "subtitles.item.honey_bottle.drink": "Tragando", "subtitles.item.honeycomb.wax_on": "Bloque encerado", "subtitles.item.horse_armor.unequip": "Armadura para caballo se suelta", "subtitles.item.ink_sac.use": "Saco de tinta mancha", "subtitles.item.lead.break": "Rienda se rompe", "subtitles.item.lead.tied": "Rienda atada", "subtitles.item.lead.untied": "Rienda desatada", "subtitles.item.llama_carpet.unequip": "Alfombra se suelta", "subtitles.item.lodestone_compass.lock": "Brújula magnetizada con magnetita", "subtitles.item.mace.smash_air": "<PERSON>za golpea", "subtitles.item.mace.smash_ground": "<PERSON>za golpea", "subtitles.item.nether_wart.plant": "Verruga cultivada", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON> se rompe", "subtitles.item.saddle.unequip": "<PERSON><PERSON> se suelta", "subtitles.item.shears.shear": "Chasquido de tijeras", "subtitles.item.shears.snip": "Tijeras cortan", "subtitles.item.shield.block": "Bloqueo con escudo", "subtitles.item.shovel.flatten": "<PERSON><PERSON>", "subtitles.item.spyglass.stop_using": "Catalejo se retrae", "subtitles.item.spyglass.use": "Catalejo se <PERSON>e", "subtitles.item.totem.use": "Tótem activado", "subtitles.item.trident.hit": "Tridente apuñala", "subtitles.item.trident.hit_ground": "Tridente vibra", "subtitles.item.trident.return": "Tridente regresa", "subtitles.item.trident.riptide": "Tridente propulsa", "subtitles.item.trident.throw": "Tridente lanzado", "subtitles.item.trident.thunder": "Truenos de tridente", "subtitles.item.wolf_armor.break": "Armadura para lobo se rompe", "subtitles.item.wolf_armor.crack": "Armadura para lobo se agrieta", "subtitles.item.wolf_armor.damage": "Armadura para lobo dañada", "subtitles.item.wolf_armor.repair": "Armadura para lobo reparada", "subtitles.particle.soul_escape": "Alma escapa", "subtitles.ui.cartography_table.take_result": "Mapa dibujado", "subtitles.ui.hud.bubble_pop": "Oxígeno se agota", "subtitles.ui.loom.take_result": "Máquina de telar usada", "subtitles.ui.stonecutter.take_result": "Cortapiedras usado", "subtitles.weather.rain": "<PERSON><PERSON><PERSON>", "symlink_warning.message": "Cargar mundos desde carpetas con enlaces simbólicos puede ser inseguro si no sabés exactamente lo que estás haciendo. Por favor, visitá %s para más información.", "symlink_warning.message.pack": "<PERSON><PERSON> paquetes con enlaces simbólicos puede ser poco seguro si no sabés bien lo que estás haciendo. Por favor, visitá %s para más información.", "symlink_warning.message.world": "Cargar mundos desde carpetas con enlaces simbólicos puede ser poco seguro si no sabés bien lo que estás haciendo. Por favor, visitá %s para más información.", "symlink_warning.more_info": "Más información", "symlink_warning.title": "La carpeta del mundo contiene enlaces simbólicos", "symlink_warning.title.pack": "Los paquetes añadidos contienen enlaces simbólicos", "symlink_warning.title.world": "La carpeta del mundo contiene enlaces simbólicos", "team.collision.always": "Siempre", "team.collision.never": "Nunca", "team.collision.pushOtherTeams": "Colisión entre jugadores de distinto equipo", "team.collision.pushOwnTeam": "Colisión entre jugadores del mismo equipo", "team.notFound": "Equipo desconocido: %s", "team.visibility.always": "Siempre", "team.visibility.hideForOtherTeams": "Ocultar para otros equipos", "team.visibility.hideForOwnTeam": "Ocultar para el equipo propio", "team.visibility.never": "Nunca", "telemetry.event.advancement_made.description": "Entender el contexto en el que se realiza un progreso nos ayudará a comprender y mejorar la progresión del juego.", "telemetry.event.advancement_made.title": "Progreso realizado", "telemetry.event.game_load_times.description": "Este evento puede ayudarnos a determinar donde se necesitan las mejoras de rendimiento, midiendo los tiempos de ejecución de las diferentes fases de arranque.", "telemetry.event.game_load_times.title": "Tiempos de carga del juego", "telemetry.event.optional": "%s (opcional)", "telemetry.event.optional.disabled": "%s (opcional): Desactivado", "telemetry.event.performance_metrics.description": "Conocer el perfil de rendimiento de Minecraft nos ayuda a optimizar el juego para muchas especificaciones y sistemas operativos. \nLa versión del juego se incluye para ayudarnos a comparar el rendimiento de nuevas versiones.", "telemetry.event.performance_metrics.title": "Medición del rendimiento", "telemetry.event.required": "%s (necesario)", "telemetry.event.world_load_times.description": "Es importante que sepamos cuánto tiempo se tarda en entrar en un mundo y cómo cambia eso con el tiempo. Por eje<PERSON><PERSON>, cuando añadimos nuevas características o realizamos cambios técnicos grandes, necesitamos ver qué impacto tienen en los tiempos de carga.", "telemetry.event.world_load_times.title": "Tiempos de carga del mundo", "telemetry.event.world_loaded.description": "Saber cómo juegan los jugadores a Minecraft (qué modo de juego, cliente o servidor modificado y versión del juego usan) nos ayuda a mejorar los aspectos que más le importan a los jugadores.\nEl evento «Mundo cargado» está unido al de «Mundo descargado» para calcular cuánto duró la sesión de juego.", "telemetry.event.world_loaded.title": "<PERSON><PERSON> cargado", "telemetry.event.world_unloaded.description": "Este evento está unido al de «Mundo cargado» para calcular cuánto ha durado la sesión de juego.\nLa duración (en segundos y ticks) se mide cuando una sesión de juego termina (saliendo al menú principal, desconectándose de un servidor).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "Tiempo de juego (ticks)", "telemetry.property.advancement_id.title": "ID del progreso", "telemetry.property.client_id.title": "ID de cliente", "telemetry.property.client_modded.title": "Cliente modificado", "telemetry.property.dedicated_memory_kb.title": "Memoria dedicada (kB)", "telemetry.property.event_timestamp_utc.title": "Hora del evento (UTC)", "telemetry.property.frame_rate_samples.title": "Muestras de fotogramas por segundo (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON>", "telemetry.property.game_version.title": "Versión del juego", "telemetry.property.launcher_name.title": "Nombre del lanzador", "telemetry.property.load_time_bootstrap_ms.title": "Tiempo de arranque (milisegundos)", "telemetry.property.load_time_loading_overlay_ms.title": "Tiempo en pantalla de carga (milisegundos)", "telemetry.property.load_time_pre_window_ms.title": "Tiempo antes de que la ventana se abra (milisegundos)", "telemetry.property.load_time_total_time_ms.title": "Tiempo total de carga (milisegundos)", "telemetry.property.minecraft_session_id.title": "ID de sesión de Minecraft", "telemetry.property.new_world.title": "Mundo nuevo", "telemetry.property.number_of_samples.title": "Número de muestras", "telemetry.property.operating_system.title": "Sistema operativo", "telemetry.property.opt_in.title": "Inscrito", "telemetry.property.platform.title": "Plataforma", "telemetry.property.realms_map_content.title": "Contenido del mapa de Realms (nombre del minijuego)", "telemetry.property.render_distance.title": "Distancia de renderizado", "telemetry.property.render_time_samples.title": "Muestras de tiempo de renderizado", "telemetry.property.seconds_since_load.title": "Tiempo desde la carga (segundos)", "telemetry.property.server_modded.title": "Servidor modificado", "telemetry.property.server_type.title": "<PERSON><PERSON><PERSON> de servidor", "telemetry.property.ticks_since_load.title": "Tiempo desde la carga (ticks)", "telemetry.property.used_memory_samples.title": "Memoria RAM utilizada", "telemetry.property.user_id.title": "ID de usuario", "telemetry.property.world_load_time_ms.title": "Tiempo de carga del mundo (milisegundos)", "telemetry.property.world_session_id.title": "ID de sesión del mundo", "telemetry_info.button.give_feedback": "Danos tu opinión", "telemetry_info.button.privacy_statement": "Declaración de privacidad", "telemetry_info.button.show_data": "Ver mis datos", "telemetry_info.opt_in.description": "<PERSON>y <PERSON> de enviar datos de telemetría opcional", "telemetry_info.property_title": "<PERSON>tos incluidos", "telemetry_info.screen.description": "La recolección de datos nos ayuda a mejorar Minecraft guiándonos hacia direcciones que sean relevantes para nuestros jugadores.\nTambién podés enviar sugerencias para ayudarnos a seguir mejorando Minecraft.", "telemetry_info.screen.title": "Recolección de datos de telemetría", "test.error.block_property_mismatch": "Se requiere que la propiedad %s sea %s, obtenido: %s", "test.error.block_property_missing": "Falta una propiedad de bloque, se requiere que la propiedad %s sea %s", "test.error.entity_property": "La entidad %s falló la prueba: %s", "test.error.entity_property_details": "La entidad %s falló la prueba: %s. Se requiere: %s, obtenido: %s", "test.error.expected_block": "Se requiere el bloque %s, obtenido: %s", "test.error.expected_block_tag": "Se requiere el bloque en #%s, obtenido: %s", "test.error.expected_container_contents": "El contenedor debe contener: %s", "test.error.expected_container_contents_single": "El contenedor debe contener un único: %s", "test.error.expected_empty_container": "El contenedor debe estar vacío", "test.error.expected_entity": "Se requiere %s", "test.error.expected_entity_around": "Se requiere que %s exista alrededor de %s, %s, %s", "test.error.expected_entity_count": "Se requieren %s entidades del tipo %s, se encontraron %s", "test.error.expected_entity_data": "Se requiere que los datos de la entidad sean: %s, obtenido: %s", "test.error.expected_entity_data_predicate": "Los datos de la entidad no coinciden para %s", "test.error.expected_entity_effect": "Se requiere que %s tenga efecto %s %s", "test.error.expected_entity_having": "El inventario de la entidad debe contener %s", "test.error.expected_entity_holding": "La entidad debe sostener %s", "test.error.expected_entity_in_test": "Se requiere que %s exista en la prueba", "test.error.expected_entity_not_touching": "No se esperaba que %s tocase %s, %s, %s (relativo: %s, %s, %s)", "test.error.expected_entity_touching": "Se requiere que %s toque %s, %s, %s (relativo: %s, %s, %s)", "test.error.expected_item": "Se requiere un objeto del tipo %s", "test.error.expected_items_count": "Se requieren %s objetos del tipo %s, se encontraron %s", "test.error.fail": "Se cumplieron las condiciones de falla", "test.error.invalid_block_type": "Se encontró un tipo de bloque no esperado: %s", "test.error.missing_block_entity": "Falta una entidad-bloque", "test.error.position": "%s en %s, %s, %s (relativo: %s, %s, %s) en el tick %s", "test.error.sequence.condition_already_triggered": "Condición ya activada en %s", "test.error.sequence.condition_not_triggered": "Condición no activada", "test.error.sequence.invalid_tick": "Éxito en tick no válido: se esperaba %s", "test.error.sequence.not_completed": "La prueba se terminó antes de que se completara la secuencia", "test.error.set_biome": "No se pudo establecer el bioma para la prueba", "test.error.spawn_failure": "No se pudo crear la entidad %s", "test.error.state_not_equal": "Estado incorrecto. Se requiere %s, obtenido: %s", "test.error.structure.failure": "Error al colocar la estructura de prueba para %s", "test.error.tick": "%s en el tick %s", "test.error.ticking_without_structure": "Ejecutar la prueba antes de colocar la estructura", "test.error.timeout.no_result": "No hubo ningún resultado durante %s ticks", "test.error.timeout.no_sequences_finished": "Ninguna secuencia finalizó durante %s ticks", "test.error.too_many_entities": "Se requiere que exista un único %s alrededor de %s, %s, %s, pero se encontraron %s", "test.error.unexpected_block": "No se esperaba que el bloque fuese %s", "test.error.unexpected_entity": "No se esperaba que %s existiera", "test.error.unexpected_item": "No se esperaba un objeto del tipo %s", "test.error.unknown": "Error interno desconocido: %s", "test.error.value_not_equal": "Se requiere que %s sea %s, obtenido: %s", "test.error.wrong_block_entity": "Tipo incorrecto de entidad-bloque: %s", "test_block.error.missing": "Falta el bloque %s en la estructura de prueba", "test_block.error.too_many": "Demasiados bloques %s", "test_block.invalid_timeout": "Tiempo de espera no válido (%s): debe ser un número positivo de ticks", "test_block.message": "Mensaje:", "test_block.mode.accept": "Aceptar", "test_block.mode.fail": "<PERSON>ar", "test_block.mode.log": "Registrar", "test_block.mode.start": "Iniciar", "test_block.mode_info.accept": "Modo aceptar: acepta superación de (parte de) una prueba", "test_block.mode_info.fail": "Modo fallar: falla la prueba", "test_block.mode_info.log": "Modo registrar: registra un mensaje", "test_block.mode_info.start": "Modo iniciar: el punto de inicio de una prueba", "test_instance.action.reset": "Reiniciar y cargar", "test_instance.action.run": "<PERSON><PERSON> y ejecutar", "test_instance.action.save": "Guardar estructura", "test_instance.description.batch": "Lote: %s", "test_instance.description.failed": "Falló: %s", "test_instance.description.function": "Función: %s", "test_instance.description.invalid_id": "Id. de prueba no válida", "test_instance.description.no_test": "Esa prueba no existe", "test_instance.description.structure": "Estructura: %s", "test_instance.description.type": "Tipo: %s", "test_instance.type.block_based": "Prueba basada en bloques", "test_instance.type.function": "Prueba de función integrada", "test_instance_block.entities": "Entidades:", "test_instance_block.error.no_test": "No se pudo ejecutar la instancia de prueba en %s, %s, %s porque hay una prueba sin definir", "test_instance_block.error.no_test_structure": "No se pudo realizar la instancia de prueba en %s, %s, %s porque no tiene estructura de prueba", "test_instance_block.error.unable_to_save": "No se pudo guardar la plantilla de estructura de prueba en %s, %s, %s", "test_instance_block.invalid": "[no válido]", "test_instance_block.reset_success": "Se reinició la prueba %s", "test_instance_block.rotation": "Rotación:", "test_instance_block.size": "Tamaño de la estructura de prueba", "test_instance_block.starting": "Iniciando prueba %s", "test_instance_block.test_id": "Id. de instancia de prueba", "title.32bit.deprecation": "Se ha detectado un sistema de 32 bits. ¡Esto podría impedirte jugar en el futuro, ya que se necesitará un sistema de 64 bits!", "title.32bit.deprecation.realms": "Minecraft requerirá un sistema de 64 bits pronto, lo que podría impedirte jugar o usar Realms en este dispositivo. Necesitarás cancelar manualmente cualquier suscripción de Realms.", "title.32bit.deprecation.realms.check": "No mostrar este mensaje de nuevo", "title.32bit.deprecation.realms.header": "Sistema de 32 bits detectado", "title.credits": "Copyright Mojang AB. ¡No distribuir!", "title.multiplayer.disabled": "El modo multijugador está desactivado. Por favor, revisá las configuraciones de tu cuenta de Microsoft.", "title.multiplayer.disabled.banned.name": "Debés cambiar tu nombre de usuario antes de jugar en multijugador", "title.multiplayer.disabled.banned.permanent": "Tu cuenta fue suspendida permanentemente y no podrás jugar en línea", "title.multiplayer.disabled.banned.temporary": "Tu cuenta fue suspendida temporalmente y no podrás jugar en línea", "title.multiplayer.lan": "Multijugador (en LAN)", "title.multiplayer.other": "Multijugador (servidor de terceros)", "title.multiplayer.realms": "Multijugador (Realms)", "title.singleplayer": "Un jugador", "translation.test.args": "%s %s", "translation.test.complex": "¡Prefijo, %s%2$s de nuevo %s y %1$s finalmente %s y también %1$s!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hola %", "translation.test.invalid2": "hola %s", "translation.test.none": "¡Hola, mundo!", "translation.test.world": "mundo", "trim_material.minecraft.amethyst": "Incrustaciones de amatista", "trim_material.minecraft.copper": "Incrustaciones de cobre", "trim_material.minecraft.diamond": "Incrustaciones de diamante", "trim_material.minecraft.emerald": "Incrustaciones de esmeralda", "trim_material.minecraft.gold": "Incrustaciones de oro", "trim_material.minecraft.iron": "Incrustaciones de hierro", "trim_material.minecraft.lapis": "Incrustaciones de lapislázuli", "trim_material.minecraft.netherite": "Incrustaciones de netherita", "trim_material.minecraft.quartz": "Incrustaciones de cuarzo", "trim_material.minecraft.redstone": "Incrustaciones de redstone", "trim_material.minecraft.resin": "Incrustaciones de resina", "trim_pattern.minecraft.bolt": "Adorno de rayo", "trim_pattern.minecraft.coast": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.dune": "<PERSON><PERSON><PERSON>na", "trim_pattern.minecraft.eye": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.flow": "Adorno de es<PERSON>ral", "trim_pattern.minecraft.host": "Adorno de anfitrión", "trim_pattern.minecraft.raiser": "Adorno de elevamiento", "trim_pattern.minecraft.rib": "Adorno de costillas", "trim_pattern.minecraft.sentry": "Adorno de centinela", "trim_pattern.minecraft.shaper": "Adorno de modelador", "trim_pattern.minecraft.silence": "Adorno de silencio", "trim_pattern.minecraft.snout": "Adorno de hocico", "trim_pattern.minecraft.spire": "Adorno de a<PERSON>ja", "trim_pattern.minecraft.tide": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.vex": "Adorno de vex", "trim_pattern.minecraft.ward": "<PERSON><PERSON><PERSON> de guardián", "trim_pattern.minecraft.wayfinder": "Adorno de guía", "trim_pattern.minecraft.wild": "<PERSON><PERSON><PERSON>", "tutorial.bundleInsert.description": "Click derecho para añadir objetos", "tutorial.bundleInsert.title": "Usar una bolsa", "tutorial.craft_planks.description": "El libro de recetas ayuda.", "tutorial.craft_planks.title": "¡<PERSON>abricá madera!", "tutorial.find_tree.description": "Golpeá su tronco.", "tutorial.find_tree.title": "¡Encontrá un árbol!", "tutorial.look.description": "Para ello, usá el mouse.", "tutorial.look.title": "¡<PERSON>á alrededor!", "tutorial.move.description": "Saltá con %s.", "tutorial.move.title": "¡Movete con %s, %s, %s y %s!", "tutorial.open_inventory.description": "Apretá %s.", "tutorial.open_inventory.title": "¡Abrí tu inventario!", "tutorial.punch_tree.description": "Mantené %s.", "tutorial.punch_tree.title": "¡Destruí el árbol!", "tutorial.socialInteractions.description": "Apretá %s para abrir", "tutorial.socialInteractions.title": "Interacciones sociales", "upgrade.minecraft.netherite_upgrade": "Me<PERSON>ra de netherita"}