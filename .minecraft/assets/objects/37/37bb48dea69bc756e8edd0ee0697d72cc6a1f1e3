{"accessibility.onboarding.accessibility.button": "Opcions d'accessibilitat...", "accessibility.onboarding.screen.narrator": "Prem Enter per activar el narrador", "accessibility.onboarding.screen.title": "Benvingut al Minecraft!\n\nT'agradaria activar el narrador o veure les opcions d'accessibilitat?", "addServer.add": "Fet", "addServer.enterIp": "IP del servidor", "addServer.enterName": "Nom del servidor", "addServer.resourcePack": "Paquets de recursos", "addServer.resourcePack.disabled": "Desactivat", "addServer.resourcePack.enabled": "Activat", "addServer.resourcePack.prompt": "Preguntar", "addServer.title": "Modifica la informació del servidor", "advMode.command": "Comandament del bloc", "advMode.mode": "Mode", "advMode.mode.auto": "Repetitiu", "advMode.mode.autoexec.bat": "Sempre actiu", "advMode.mode.conditional": "Condicional", "advMode.mode.redstone": "Per impuls", "advMode.mode.redstoneTriggered": "Necessita redstone", "advMode.mode.sequence": "En cadena", "advMode.mode.unconditional": "Incondicional", "advMode.notAllowed": "S'ha de ser operador i estar en mode Creatiu", "advMode.notEnabled": "Els blocs de comandaments no estan permesos en aquest servidor", "advMode.previousOutput": "Resultat anterior", "advMode.setCommand": "Canvia el comandament del bloc", "advMode.setCommand.success": "Comandament establert: %s", "advMode.trackOutput": "Fer seguiment del resultat", "advMode.triggering": "Activable", "advMode.type": "<PERSON><PERSON><PERSON>", "advancement.advancementNotFound": "Avenç desconegut: %s", "advancements.adventure.adventuring_time.description": "Descobreix tots els biomes", "advancements.adventure.adventuring_time.title": "Hora d'aventures", "advancements.adventure.arbalistic.description": "Mata a cinc criatures úniques amb un sol tret de ballesta", "advancements.adventure.arbalistic.title": "Assassinat quíntuple", "advancements.adventure.avoid_vibration.description": "<PERSON><PERSON> ajupit a prop d'un sensor sculk, un sculk xisclador o d'un vigilant per evitar que et sentin", "advancements.adventure.avoid_vibration.title": "De puntetes", "advancements.adventure.blowback.description": "Mata una brisa retornant-li una càrrega de vent", "advancements.adventure.blowback.title": "Molt tocat per la tramuntana", "advancements.adventure.brush_armadillo.description": "Obté escames d'armadillo utilitzant un pinzell", "advancements.adventure.brush_armadillo.title": "No és això una escama?", "advancements.adventure.bullseye.description": "Fes diana des d'almenys 30 metres de distància", "advancements.adventure.bullseye.title": "<PERSON>", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Fabrica un gerro decorat amb 4 fragments de ceràmica", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Restauració meticulos<PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "Estigues a prop d'un elaborador mentre que elabori un elaborador", "advancements.adventure.crafters_crafting_crafters.title": "Elaboradors elaborant elaboradors", "advancements.adventure.fall_from_world_height.description": "Deixa't caure des del cim del món (límit de construcció) fins al fons del món i sobreviu", "advancements.adventure.fall_from_world_height.title": "Coves i penya-segats", "advancements.adventure.heart_transplanter.description": "Col·loqueu un cor cruixent amb l'alineació correcta entre dos blocs de troncs de roure pàl·lid", "advancements.adventure.heart_transplanter.title": "Trasplantador de cors", "advancements.adventure.hero_of_the_village.description": "Protegeix amb èxit un poble d'un setge", "advancements.adventure.hero_of_the_village.title": "Heroi del poble", "advancements.adventure.honey_block_slide.description": "Cau sobre un bloc de mel per esmorteir la teva caiguda", "advancements.adventure.honey_block_slide.title": "Situació complicada", "advancements.adventure.kill_a_mob.description": "Ataca i venç un monstre hostil", "advancements.adventure.kill_a_mob.title": "Caçamonstres", "advancements.adventure.kill_all_mobs.description": "Mata a un monstre hostil de cada tipus", "advancements.adventure.kill_all_mobs.title": "Sense pietat", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Mata una criatura prop d'un sculk catalitzador", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "<PERSON>s propaga", "advancements.adventure.lighten_up.description": "Treu un tros de coure amb una destral per fer-ho més brillant", "advancements.adventure.lighten_up.title": "Il·luminat", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Protegeix un vilatà d'un llamp sense que s'encengui un foc", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Protector de sobretensions", "advancements.adventure.minecraft_trials_edition.description": "Entra en una cambra de reptes", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON>", "advancements.adventure.ol_betsy.description": "Dispara una ballesta", "advancements.adventure.ol_betsy.title": "La vella Betsy", "advancements.adventure.overoverkill.description": "Infligeix 50 de dany d'un sol cop utilitzant la maça", "advancements.adventure.overoverkill.title": "Sobre-rematar", "advancements.adventure.play_jukebox_in_meadows.description": "Fes que els prats vibrin amb la dolça música d'un tocadiscs", "advancements.adventure.play_jukebox_in_meadows.title": "El so de la música", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Llegeix el senyal d'energia d'una llibreria cisellada emprant un comparador", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "El poder dels llibres", "advancements.adventure.revaulting.description": "Desbloqueja una caixa forta ominosa utilitzant una clau de reptes ominosa", "advancements.adventure.revaulting.title": "Capsa de pandora", "advancements.adventure.root.description": "Aventura, exploració i combat", "advancements.adventure.root.title": "Aventura", "advancements.adventure.salvage_sherd.description": "Raspa un bloc sospitós per aconseguir un fragment de ceràmica", "advancements.adventure.salvage_sherd.title": "Respectant les restes", "advancements.adventure.shoot_arrow.description": "Encerta alguna cosa amb una fletxa", "advancements.adventure.shoot_arrow.title": "Bona punteria!", "advancements.adventure.sleep_in_bed.description": "Dorm en un llit per canviar el teu punt de reaparició", "advancements.adventure.sleep_in_bed.title": "Dolços somnis", "advancements.adventure.sniper_duel.description": "Mata a un esquelet des de 50 metres o més", "advancements.adventure.sniper_duel.title": "Duel d'arquers", "advancements.adventure.spyglass_at_dragon.description": "Observa un drac d'Ender amb una ullera de llarga vista", "advancements.adventure.spyglass_at_dragon.title": "És un avió?", "advancements.adventure.spyglass_at_ghast.description": "Observa un ghast amb una ullera de llarga vista", "advancements.adventure.spyglass_at_ghast.title": "És un globus?", "advancements.adventure.spyglass_at_parrot.description": "Observa un lloro amb una ullera de llarga vista", "advancements.adventure.spyglass_at_parrot.title": "És un ocell?", "advancements.adventure.summon_iron_golem.description": "Invoca un gòlem de ferro per ajudar a defensar un poble", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.throw_trident.description": "Tira un trident a alguna cosa\nNota: Tirar la teva única arma no és una bona idea.", "advancements.adventure.throw_trident.title": "Jugada mestra", "advancements.adventure.totem_of_undying.description": "Utilitza un tòtem de l'immortalitat per evitar la mort", "advancements.adventure.totem_of_undying.title": "Post mortem", "advancements.adventure.trade.description": "Fes un intercanvi amb un vilatà", "advancements.adventure.trade.title": "Quina ganga!", "advancements.adventure.trade_at_world_height.description": "Comercia amb un vilatà a l'altura de construcció màxima", "advancements.adventure.trade_at_world_height.title": "Venedor estel·lar", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Aplica aquests motlles de ferreria almenys una vegada: agulles, musell, costelles, vigilant, silenci, esperit, marea, cerca camins", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON> amb estil", "advancements.adventure.trim_with_any_armor_pattern.description": "Decora una armadura amb una taula de ferreria", "advancements.adventure.trim_with_any_armor_pattern.title": "Creant un nou estil", "advancements.adventure.two_birds_one_arrow.description": "Mata dos fantasmes amb una fletxa perforadora", "advancements.adventure.two_birds_one_arrow.title": "Dos ocells, un tret", "advancements.adventure.under_lock_and_key.description": "Utilitza una clau de reptes en una caixa forta", "advancements.adventure.under_lock_and_key.title": "<PERSON>ta clau i pany", "advancements.adventure.use_lodestone.description": "Utilitza una Brúixola a una Magnetita", "advancements.adventure.use_lodestone.title": "<PERSON><PERSON><PERSON> tornar enrere", "advancements.adventure.very_very_frightening.description": "Colpeja a un vilatà amb un llamp", "advancements.adventure.very_very_frightening.title": "Molt molt espantós", "advancements.adventure.voluntary_exile.description": "Mata un capità dels assaltants.\nPot ser hauries d'allunyar-te un temps dels pobles...", "advancements.adventure.voluntary_exile.title": "<PERSON>ili voluntari", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Camina sobre la neu en pols... sense enfonsar-t'hi", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Lleuger com una llebre", "advancements.adventure.who_needs_rockets.description": "Utilitza una càrrega de vent per llançar-te 8 blocs cap amunt", "advancements.adventure.who_needs_rockets.title": "Qui necessita coets?", "advancements.adventure.whos_the_pillager_now.description": "Fes que un saquejador tasti la seva pròpia medicina", "advancements.adventure.whos_the_pillager_now.title": "El saquejador saquejat", "advancements.empty": "No sembla que hi hagi res per aquí...", "advancements.end.dragon_breath.description": "<PERSON>cull al<PERSON> de drac en una ampolla de vidre", "advancements.end.dragon_breath.title": "Quin mal alè!", "advancements.end.dragon_egg.description": "Aconsegueix l'ou de drac", "advancements.end.dragon_egg.title": "La nova generació", "advancements.end.elytra.description": "Troba uns èlitres", "advancements.end.elytra.title": "Fins a l'infinit... i més enllà!", "advancements.end.enter_end_gateway.description": "Escapa de l'illa principal", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON><PERSON> ll<PERSON>", "advancements.end.find_end_city.description": "<PERSON>tra, què és el pitjor que pot passar?", "advancements.end.find_end_city.title": "La ciutat al final del joc", "advancements.end.kill_dragon.description": "Bona sort", "advancements.end.kill_dragon.title": "Allibera l'End", "advancements.end.levitate.description": "Levita fins a una altura de 50 blocs al ser atacat per un shulker", "advancements.end.levitate.title": "Bones vistes des d'aquí", "advancements.end.respawn_dragon.description": "Torna a invocar el Drac d<PERSON>", "advancements.end.respawn_dragon.title": "La fi... 2", "advancements.end.root.description": "O el començament?", "advancements.end.root.title": "La fi.", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Fes que un allay deixi un pastís en un bloc musical", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Aniversari feliç", "advancements.husbandry.allay_deliver_item_to_player.description": "Fes que un allay et porti ítems", "advancements.husbandry.allay_deliver_item_to_player.title": "Tens un amic en mi", "advancements.husbandry.axolotl_in_a_bucket.description": "Atrapa un axolot amb una galleda", "advancements.husbandry.axolotl_in_a_bucket.title": "El depredador més bufó", "advancements.husbandry.balanced_diet.description": "Menja tot el que sigui comestible, encara que no sigui saludable", "advancements.husbandry.balanced_diet.title": "Una dieta equilibrada", "advancements.husbandry.breed_all_animals.description": "Fes que es reprodueixin dos animals de cada tipus!", "advancements.husbandry.breed_all_animals.title": "De dos en dos", "advancements.husbandry.breed_an_animal.description": "<PERSON><PERSON> dos animals junts", "advancements.husbandry.breed_an_animal.title": "Els lloros i els ratpenats", "advancements.husbandry.complete_catalogue.description": "Ensinistra totes les espècies de gat!", "advancements.husbandry.complete_catalogue.title": "Un gatàleg complet", "advancements.husbandry.feed_snifflet.description": "Alimenta una cria d'ensumador", "advancements.husbandry.feed_snifflet.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.fishy_business.description": "Atrapa un peix", "advancements.husbandry.fishy_business.title": "Un assumpte escamós", "advancements.husbandry.froglights.description": "Tingues totes les granollums al teu inventari", "advancements.husbandry.froglights.title": "Amb els nostres poders combinats!", "advancements.husbandry.kill_axolotl_target.description": "Fes equip amb un axolot i guanya una baralla", "advancements.husbandry.kill_axolotl_target.title": "La força curativa de l'amistat!", "advancements.husbandry.leash_all_frog_variants.description": "Lliga totes les espècies de granota", "advancements.husbandry.leash_all_frog_variants.title": "L'es-croac-dró al complet", "advancements.husbandry.make_a_sign_glow.description": "Fes lluir el text de qualsevol mena de cartell", "advancements.husbandry.make_a_sign_glow.title": "Brilla i admira!", "advancements.husbandry.netherite_hoe.description": "Fes servir un lingot de netherita per millorar una aixada. <PERSON><PERSON> seguit, reconsidera la teva decisió", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON> hàbits", "advancements.husbandry.obtain_sniffer_egg.description": "Obté un ou d'ensumador", "advancements.husbandry.obtain_sniffer_egg.title": "Quina olor més curiosa", "advancements.husbandry.place_dried_ghast_in_water.description": "Col·loca un Ghast sec dins l'aigua", "advancements.husbandry.place_dried_ghast_in_water.title": "Manteniu-vos Hidratats!", "advancements.husbandry.plant_any_sniffer_seed.description": "<PERSON>a qualsevol llavor d'un ensumador", "advancements.husbandry.plant_any_sniffer_seed.title": "Sembrant el passat", "advancements.husbandry.plant_seed.description": "Planta una llavor i observa com creix", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON><PERSON><PERSON> tant ràpid...", "advancements.husbandry.remove_wolf_armor.description": "Treu l'armadura de llop utilitzant tisores", "advancements.husbandry.remove_wolf_armor.title": "Esquilada lluent", "advancements.husbandry.repair_wolf_armor.description": "Repara una armadura de llop danyada utilitzant escates d'armadillo", "advancements.husbandry.repair_wolf_armor.title": "Com nova", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Navega amb una cabra", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Bon vent i cabra nova!", "advancements.husbandry.root.description": "El món és ple d'amics i menjar", "advancements.husbandry.root.title": "Prosperitat", "advancements.husbandry.safely_harvest_honey.description": "Utilitza una foguera per obtenir mel d'un rusc amb una ampolla de vidre sense enfurismar a les abelles", "advancements.husbandry.safely_harvest_honey.title": "Ja ens esfumem", "advancements.husbandry.silk_touch_nest.description": "Mou un rusc que contingui 3 abelles utilitzant toc de seda", "advancements.husbandry.silk_touch_nest.title": "Eixamferència", "advancements.husbandry.tactical_fishing.description": "Atrapa un peix... sense canya de pescar!", "advancements.husbandry.tactical_fishing.title": "Pesca tàctica", "advancements.husbandry.tadpole_in_a_bucket.description": "Atrapa un capgròs amb una galleda", "advancements.husbandry.tadpole_in_a_bucket.title": "Capgròs cap-turat", "advancements.husbandry.tame_an_animal.description": "Domestica un animal", "advancements.husbandry.tame_an_animal.title": "Millors amics per sempre", "advancements.husbandry.wax_off.description": "Treu la cera d'un bloc de coure!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.wax_on.description": "Aplica bresca a un bloc de coure!", "advancements.husbandry.wax_on.title": "Encerar", "advancements.husbandry.whole_pack.description": "Domestica totes les races de llop", "advancements.husbandry.whole_pack.title": "Un de cada", "advancements.nether.all_effects.description": "Aconsegueix tots els efectes a la vegada", "advancements.nether.all_effects.title": "Com hem arribat fins aquí?", "advancements.nether.all_potions.description": "Aconsegueix tots els efectes de pocions a la vegada", "advancements.nether.all_potions.title": "Barreja explosiva", "advancements.nether.brew_potion.description": "Elabora una poció", "advancements.nether.brew_potion.title": "Destil·leria local", "advancements.nether.charge_respawn_anchor.description": "Carrega una àncora de reaparició al màxim", "advancements.nether.charge_respawn_anchor.title": "No pas \"nou\" vides", "advancements.nether.create_beacon.description": "Construeix i col·loca un far", "advancements.nether.create_beacon.title": "Que es faci la llum", "advancements.nether.create_full_beacon.description": "Fes que un far treballi a màxima potència", "advancements.nether.create_full_beacon.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.distract_piglin.description": "Distreu piglins amb or", "advancements.nether.distract_piglin.title": "El brill de l'or", "advancements.nether.explore_nether.description": "Explora tots els biomes del Nether", "advancements.nether.explore_nether.title": "Destinacions turístiques populars", "advancements.nether.fast_travel.description": "Utilitza el Nether per recórrer 7 km a la superfície", "advancements.nether.fast_travel.title": "Forat negre", "advancements.nether.find_bastion.description": "Entra dins de les restes d'un bastió", "advancements.nether.find_bastion.title": "<PERSON><PERSON><PERSON> foren els dies", "advancements.nether.find_fortress.description": "Endinsa't dins una Fortalesa del Nether", "advancements.nether.find_fortress.title": "Una fortalesa terrible", "advancements.nether.get_wither_skull.description": "Aconsegueix la calavera d'un esquelet wither", "advancements.nether.get_wither_skull.title": "Cap de trons", "advancements.nether.loot_bastion.description": "Saqueja un cofre dins de les restes d'un bastió", "advancements.nether.loot_bastion.title": "<PERSON><PERSON><PERSON>", "advancements.nether.netherite_armor.description": "Aconsegueix una armadura de netherita completa", "advancements.nether.netherite_armor.title": "Cobreix-me de restes", "advancements.nether.obtain_ancient_debris.description": "Aconsegueix restes ancestrals", "advancements.nether.obtain_ancient_debris.title": "Amagat a les profunditats", "advancements.nether.obtain_blaze_rod.description": "Aconsegueix una vara flamejant d'un Esperit del foc", "advancements.nether.obtain_blaze_rod.title": "Jugant amb foc", "advancements.nether.obtain_crying_obsidian.description": "Aconsegueix obsidiana plorosa", "advancements.nether.obtain_crying_obsidian.title": "Qui està tallant cebes?", "advancements.nether.return_to_sender.description": "Venç a un ghast retornant-li una bola de foc", "advancements.nether.return_to_sender.title": "Retorn al remitent", "advancements.nether.ride_strider.description": "Cavalca un caminant usant una canya amb fong guerxo", "advancements.nether.ride_strider.title": "Aquesta barca té potes", "advancements.nether.ride_strider_in_overworld_lava.description": "Fes un llaaaarg passeig amb un caminant en un llac de lava a la superfície", "advancements.nether.ride_strider_in_overworld_lava.title": "Com a casa", "advancements.nether.root.description": "Porta roba d'estiu", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "Invoca al Wither", "advancements.nether.summon_wither.title": "Dr<PERSON>", "advancements.nether.uneasy_alliance.description": "Rescata un ghast, porta'l a la superfície... i després, acaba amb ell", "advancements.nether.uneasy_alliance.title": "Falsa aliança", "advancements.nether.use_lodestone.description": "Orienta una brúixola en una fita", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON> tornar enrere", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Debilita i cura a un vilatà zombi", "advancements.story.cure_zombie_villager.title": "Zombiòleg", "advancements.story.deflect_arrow.description": "Desvia un projectil amb un escut", "advancements.story.deflect_arrow.title": "Avui no, gràcies", "advancements.story.enchant_item.description": "Encanta un objecte en una taula d'encanteris", "advancements.story.enchant_item.title": "Ni<PERSON>man<PERSON>", "advancements.story.enter_the_end.description": "Travessa el portal de l'End", "advancements.story.enter_the_end.title": "La fi?", "advancements.story.enter_the_nether.description": "Construeix, encén i travessa un portal del Nether", "advancements.story.enter_the_nether.title": "Ànimes condemnades", "advancements.story.follow_ender_eye.description": "Segueix un ull d'<PERSON>er", "advancements.story.follow_ender_eye.title": "Ull espia", "advancements.story.form_obsidian.description": "Aconsegueix un bloc d'obsidiana", "advancements.story.form_obsidian.title": "<PERSON><PERSON><PERSON>", "advancements.story.iron_tools.description": "Millora el teu pic", "advancements.story.iron_tools.title": "Bona elecció!", "advancements.story.lava_bucket.description": "Omple una galleda amb lava", "advancements.story.lava_bucket.title": "La cosa està que crema!", "advancements.story.mine_diamond.description": "Aconsegueix diamants", "advancements.story.mine_diamond.title": "Diamants!", "advancements.story.mine_stone.description": "Pica roca amb el teu nou pic", "advancements.story.mine_stone.title": "L'edat de pedra", "advancements.story.obtain_armor.description": "Protegeix-te amb una peça d'armadura de ferro", "advancements.story.obtain_armor.title": "Vesteix-te", "advancements.story.root.description": "El cor i la història del joc", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "L'armadura de diamant salva vides", "advancements.story.shiny_gear.title": "Cobreix-me amb diamants", "advancements.story.smelt_iron.description": "Fon un lingot de ferro", "advancements.story.smelt_iron.title": "Adquirint tecnologia", "advancements.story.upgrade_tools.description": "Elabora un pic millor", "advancements.story.upgrade_tools.title": "Obtenint una millora", "advancements.toast.challenge": "Repte completat!", "advancements.toast.goal": "Objectiu assolit!", "advancements.toast.task": "Avenç fet!", "argument.anchor.invalid": "La posició d'ancoratge de l'entitat és invàlida: %s", "argument.angle.incomplete": "Incomplet (es requereix 1 angle)", "argument.angle.invalid": "<PERSON><PERSON> in<PERSON>", "argument.block.id.invalid": "Tipus de bloc desconegut: %s", "argument.block.property.duplicate": "La propietat \"%s\" del bloc %s només pot establir-se un cop", "argument.block.property.invalid": "El bloc %s no accepta \"%s\" per a la propietat \"%s\"", "argument.block.property.novalue": "Es requereix un valor per a la propietat \"%s\" del bloc %s", "argument.block.property.unclosed": "Es requereix \"]\" per tancar les propietats del bloc", "argument.block.property.unknown": "El bloc %s no poseeix la propietat \"%s\"", "argument.block.tag.disallowed": "Aquí no es permet l'ús de #, només blocs actuals", "argument.color.invalid": "Color desconegut: %s", "argument.component.invalid": "Component de xat invàlid: %s", "argument.criteria.invalid": "Criteri '%s' desconegut", "argument.dimension.invalid": "Dimensió desconeguda: %s", "argument.double.big": "El valor doble no pot ser major que %s (trobat: %s)", "argument.double.low": "El valor doble no pot ser menor que %s (trobat: %s)", "argument.entity.invalid": "Nom o UUID invàlids", "argument.entity.notfound.entity": "No s'ha trobat cap entitat", "argument.entity.notfound.player": "No s'ha trobat cap jugador", "argument.entity.options.advancements.description": "Jugadors amb avenços", "argument.entity.options.distance.description": "Distància fins a l'entitat", "argument.entity.options.distance.negative": "La distància no pot ser negativa", "argument.entity.options.dx.description": "Entitats entre X i X+dX", "argument.entity.options.dy.description": "Entitats entre Y i Y+dY", "argument.entity.options.dz.description": "Entitats entre Z i Z+dZ", "argument.entity.options.gamemode.description": "Jugadors amb mode de joc", "argument.entity.options.inapplicable": "L'opció \"%s\" no pot aplicar-se aquí", "argument.entity.options.level.description": "Nivell d'experiència", "argument.entity.options.level.negative": "El nivell no pot ser negatiu", "argument.entity.options.limit.description": "Nombre màxim d'entitats per tornar", "argument.entity.options.limit.toosmall": "El límit ha de ser almenys 1", "argument.entity.options.mode.invalid": "Mode de joc invàlid o desconegut: %s", "argument.entity.options.name.description": "Nom de l'entitat", "argument.entity.options.nbt.description": "Entitats amb una NBT determinada", "argument.entity.options.predicate.description": "Predicat personalitzat", "argument.entity.options.scores.description": "Entitats amb una puntuació determinada", "argument.entity.options.sort.description": "Ordenar entitats", "argument.entity.options.sort.irreversible": "El tipus de classificació \"%s\" és desconegut o invàlid", "argument.entity.options.tag.description": "Entitats amb una etiqueta determinada", "argument.entity.options.team.description": "Entitats en un equip determinat", "argument.entity.options.type.description": "Entitats d'un tipus determinat", "argument.entity.options.type.invalid": "Tipus d'entitat desconeguda o invàlida: %s", "argument.entity.options.unknown": "Opció desconeguda: %s", "argument.entity.options.unterminated": "Es requereix \"]\" per tancar les opcions", "argument.entity.options.valueless": "Es requereix un valor per a l'opció \"%s\"", "argument.entity.options.x.description": "posició x", "argument.entity.options.x_rotation.description": "Rotació x de l'entitat", "argument.entity.options.y.description": "posició y", "argument.entity.options.y_rotation.description": "Rotació y de l'entitat", "argument.entity.options.z.description": "posició z", "argument.entity.selector.allEntities": "Totes les entitats", "argument.entity.selector.allPlayers": "Tots els jugadors", "argument.entity.selector.missing": "Falta el tipus de selector", "argument.entity.selector.nearestEntity": "Entitat més propera", "argument.entity.selector.nearestPlayer": "Jugador més proper", "argument.entity.selector.not_allowed": "Selector no permès", "argument.entity.selector.randomPlayer": "Jugador aleatori", "argument.entity.selector.self": "Entitat actual", "argument.entity.selector.unknown": "Tipus de selector desconegut: %s", "argument.entity.toomany": "Només es permet una entitat, però el selector utilitzat permet més d'una", "argument.enum.invalid": "El valor \"%s\" és invàlid", "argument.float.big": "El valor float no pot ser major que %s (trobat: %s)", "argument.float.low": "El valor float no pot ser menor que %s (trobat: %s)", "argument.gamemode.invalid": "Mode de joc desconegut: %s", "argument.hexcolor.invalid": "'%s' Codi d'hex invàlid", "argument.id.invalid": "ID invàlida", "argument.id.unknown": "ID desconeguda: %s", "argument.integer.big": "El nombre enter no pot ser major que %s (trobat: %s)", "argument.integer.low": "El nombre enter no pot ser menor que %s (trobat: %s)", "argument.item.id.invalid": "Ítem desconegut: %s", "argument.item.tag.disallowed": "Aquí no es permet l'ús d'etiquetes, només ítems veritables", "argument.literal.incorrect": "<PERSON>s requereix el valor literal \"%s\"", "argument.long.big": "El valor enter no pot ser major que %s, s'ha trobat %s", "argument.long.low": "El valor enter no pot ser menor que %s, s'ha trobat %s", "argument.message.too_long": "Missatge del xat és massa llarg (%s > màxim %s caràcters)", "argument.nbt.array.invalid": "Tipus de fletxa invàlid: %s", "argument.nbt.array.mixed": "No es pot insertar %s a %s", "argument.nbt.expected.compound": "Es requereix etiqueta composta", "argument.nbt.expected.key": "<PERSON><PERSON> requereix clau", "argument.nbt.expected.value": "<PERSON><PERSON> requereix valor", "argument.nbt.list.mixed": "No es pot insertar %s a la llista de %s", "argument.nbt.trailing": "Hi ha dades de més", "argument.player.entities": "Aquest comandament només afecta a jugadors, però el selector utilitzar inclou entitats", "argument.player.toomany": "Només es permet un jugador, però el selector utilitzat permet més d'un", "argument.player.unknown": "Aquest jugador no existeix", "argument.pos.missing.double": "Es requereix una coordenada", "argument.pos.missing.int": "Es requereix la posició d'un bloc", "argument.pos.mixed": "No es pot utilitzar una barreja de coordenades global i local (o s'utilitza ^ amb totes o amb cap)", "argument.pos.outofbounds": "Aquesta posició està fora dels límits permesos.", "argument.pos.outofworld": "Aquesta posició està fora d'aquest món!", "argument.pos.unloaded": "Aquesta posició no està carregada", "argument.pos2d.incomplete": "Incomplet (es requereixen 2 coordenades)", "argument.pos3d.incomplete": "Incomplet (es requereixen 3 coordenades)", "argument.range.empty": "Es requereix un valor o un interval de valors", "argument.range.ints": "No<PERSON>s s'accepten nombres enters, no decimals", "argument.range.swapped": "El mínim no pot ser més gran que el màxim", "argument.resource.invalid_type": "L'element '%s' té el tipus erroni '%s' (s'esperava '%s')", "argument.resource.not_found": "No s'ha trobat l'element '%s' de tipus '%s'", "argument.resource_or_id.failed_to_parse": "No s'ha pogut analitzar l'estructura: %s", "argument.resource_or_id.invalid": "Identitat o etiqueta no vàlida", "argument.resource_or_id.no_such_element": "No s'ha pogut trobar l'element %s al registre %s", "argument.resource_selector.not_found": "Sense coincidències per '%s' del tipus '%s'", "argument.resource_tag.invalid_type": "L'etiqueta '%s' té el tipus erroni '%s' (s'esper<PERSON> '%s')", "argument.resource_tag.not_found": "No s'ha trobat l'etiqueta '%s' de tipus '%s'", "argument.rotation.incomplete": "Incomplet (es requereixen 2 coordenades)", "argument.scoreHolder.empty": "No s'han trobat marcadors de puntatge rellevants", "argument.scoreboardDisplaySlot.invalid": "Espai de mostra desconegut: %s", "argument.style.invalid": "Estil invàlid: %s", "argument.time.invalid_tick_count": "El compte de cicles no pot ser negatiu", "argument.time.invalid_unit": "Unitat invàlida", "argument.time.tick_count_too_low": "El compte de cicles no pot ser inferior a %s, s'ha trobat %s", "argument.uuid.invalid": "UUID invàlid", "argument.waypoint.invalid": "L'entitat seleccionada no és un punt de referència", "arguments.block.tag.unknown": "Etiqueta de bloc desconeguda: %s", "arguments.function.tag.unknown": "Etiqueta de funció desconeguda: %s", "arguments.function.unknown": "Funció desconeguda: %s", "arguments.item.component.expected": "Es requereix un component d'ítem", "arguments.item.component.malformed": "Component '%s' malformat: '%s'", "arguments.item.component.repeated": "El component d'ítems '%s' s'ha repetit, però només es pot especificar un valor", "arguments.item.component.unknown": "Component d'ítem '%s' desconegut", "arguments.item.malformed": "Ítem mal format: '%s'", "arguments.item.overstacked": "%s només pot apilar-se fins a %s", "arguments.item.predicate.malformed": "Predicat '%s' malformat: '%s'", "arguments.item.predicate.unknown": "Predicat d'ítem desconegut '%s\"", "arguments.item.tag.unknown": "Etiqueta d'objecte desconeguda: %s", "arguments.nbtpath.node.invalid": "Element invàlid en la ruta de l'NBT", "arguments.nbtpath.nothing_found": "No s'han trobat cap elements que coincideixin amb %s", "arguments.nbtpath.too_deep": "L'NBT resultant es troba massa imbricada", "arguments.nbtpath.too_large": "L'NBT resultant és massa gran", "arguments.objective.notFound": "Objectiu de la taula de puntuació desconegut: %s", "arguments.objective.readonly": "L'obectiu \"%s\" de la taula de puntuació és només de lectura", "arguments.operation.div0": "No es pot dividir per zero", "arguments.operation.invalid": "Operació <PERSON>", "arguments.swizzle.invalid": "Eixos invàlids: es requereix la combinació de \"x\", \"y\" i \"z\"", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "Duresa de l'armadura", "attribute.name.attack_damage": "Dany per atac", "attribute.name.attack_knockback": "Retrocés de l'atac", "attribute.name.attack_speed": "Velocitat de l'atac", "attribute.name.block_break_speed": "Velocitat del trencament de bloc", "attribute.name.block_interaction_range": "Distància d'interacció amb blocs", "attribute.name.burning_time": "Temps en flames", "attribute.name.camera_distance": "Distància de càmera", "attribute.name.entity_interaction_range": "Distància d'interacció amb entitats", "attribute.name.explosion_knockback_resistance": "Resistència al retrocés d'explosions", "attribute.name.fall_damage_multiplier": "Multiplicador de dany per caiguda", "attribute.name.flying_speed": "Velocitat de vol", "attribute.name.follow_range": "Distància de seguiment del monstre", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Duresa de l'armadura", "attribute.name.generic.attack_damage": "Dany per atac", "attribute.name.generic.attack_knockback": "Retrocés de l'atac", "attribute.name.generic.attack_speed": "Velocitat d'atac", "attribute.name.generic.block_interaction_range": "Distància d'interacció de blocs", "attribute.name.generic.burning_time": "Duració de cremar", "attribute.name.generic.entity_interaction_range": "Distància d'interacció d'entitats", "attribute.name.generic.explosion_knockback_resistance": "Resistència al retrocés d'explosions", "attribute.name.generic.fall_damage_multiplier": "Multiplicador de danys per alçada", "attribute.name.generic.flying_speed": "Velocitat de vol", "attribute.name.generic.follow_range": "Distància de seguiment de criatures", "attribute.name.generic.gravity": "Gravetat", "attribute.name.generic.jump_strength": "Força de salt", "attribute.name.generic.knockback_resistance": "Resistència al retrocés", "attribute.name.generic.luck": "Sort", "attribute.name.generic.max_absorption": "<PERSON><PERSON>or<PERSON><PERSON> m<PERSON>xi<PERSON>", "attribute.name.generic.max_health": "<PERSON>ut màxima", "attribute.name.generic.movement_efficiency": "Eficiència de moviment", "attribute.name.generic.movement_speed": "Velocitat", "attribute.name.generic.oxygen_bonus": "Oxigen addicional", "attribute.name.generic.safe_fall_distance": "Distància de caiguda segura", "attribute.name.generic.scale": "Escala", "attribute.name.generic.step_height": "Alçada del pas", "attribute.name.generic.water_movement_efficiency": "Eficiència de moviment aquàtica", "attribute.name.gravity": "Gravetat", "attribute.name.horse.jump_strength": "Força de salt del cavall", "attribute.name.jump_strength": "Força de salt", "attribute.name.knockback_resistance": "Resistència a l'empenta", "attribute.name.luck": "Sort", "attribute.name.max_absorption": "<PERSON><PERSON>or<PERSON><PERSON> m<PERSON>xi<PERSON>", "attribute.name.max_health": "Vida màxima", "attribute.name.mining_efficiency": "Eficiència de mineria", "attribute.name.movement_efficiency": "Eficiència de moviment", "attribute.name.movement_speed": "Velocitat", "attribute.name.oxygen_bonus": "Oxigen addicional", "attribute.name.player.block_break_speed": "Velocitat de trencar blocs", "attribute.name.player.block_interaction_range": "Distància d'interacció de blocs", "attribute.name.player.entity_interaction_range": "Distància d'interacció d'entitats", "attribute.name.player.mining_efficiency": "Eficiència minera", "attribute.name.player.sneaking_speed": "Velocitat en ajupir-se", "attribute.name.player.submerged_mining_speed": "Velocitat minera bussejant", "attribute.name.player.sweeping_damage_ratio": "<PERSON><PERSON><PERSON>'es<PERSON>", "attribute.name.safe_fall_distance": "Distància de caiguda segura", "attribute.name.scale": "Escala", "attribute.name.sneaking_speed": "Velocitat en ajupir-se", "attribute.name.spawn_reinforcements": "Reforços zombi", "attribute.name.step_height": "Pas amunt", "attribute.name.submerged_mining_speed": "Velocitat de mineria submergida", "attribute.name.sweeping_damage_ratio": "<PERSON><PERSON> de <PERSON>'<PERSON>", "attribute.name.tempt_range": "Radi de moviment temptatiu", "attribute.name.water_movement_efficiency": "Eficiència de moviment aquàtica", "attribute.name.waypoint_receive_range": "Rang de recepció del punt de referència", "attribute.name.waypoint_transmit_range": "Rang de transmissió del punt de referència", "attribute.name.zombie.spawn_reinforcements": "Reforços zombi", "biome.minecraft.badlands": "Terra erma", "biome.minecraft.bamboo_jungle": "<PERSON><PERSON>", "biome.minecraft.basalt_deltas": "Deltes de basalt", "biome.minecraft.beach": "<PERSON><PERSON><PERSON>", "biome.minecraft.birch_forest": "Bosc de bedolls", "biome.minecraft.cherry_grove": "Bosc de cirerers", "biome.minecraft.cold_ocean": "Oceà fred", "biome.minecraft.crimson_forest": "Bosc carmesí", "biome.minecraft.dark_forest": "Bosc fosc", "biome.minecraft.deep_cold_ocean": "Oceà fred profund", "biome.minecraft.deep_dark": "Foscor profunda", "biome.minecraft.deep_frozen_ocean": "Oceà congelat profund", "biome.minecraft.deep_lukewarm_ocean": "Oceà tebi profund", "biome.minecraft.deep_ocean": "Oceà profund", "biome.minecraft.desert": "Desert", "biome.minecraft.dripstone_caves": "Coves càrstiques", "biome.minecraft.end_barrens": "Erms de l'End", "biome.minecraft.end_highlands": "Terres altes de l'End", "biome.minecraft.end_midlands": "Planes de l'End", "biome.minecraft.eroded_badlands": "Terra erma erosionada", "biome.minecraft.flower_forest": "Bosc de flors", "biome.minecraft.forest": "Bosc", "biome.minecraft.frozen_ocean": "Oceà congelat", "biome.minecraft.frozen_peaks": "Cims gelats", "biome.minecraft.frozen_river": "<PERSON><PERSON> congel<PERSON>", "biome.minecraft.grove": "Arbreda", "biome.minecraft.ice_spikes": "Pics de gel", "biome.minecraft.jagged_peaks": "<PERSON><PERSON><PERSON>", "biome.minecraft.jungle": "<PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON> te<PERSON>", "biome.minecraft.lush_caves": "Coves frondoses", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.meadow": "<PERSON><PERSON>", "biome.minecraft.mushroom_fields": "Camps de bolets", "biome.minecraft.nether_wastes": "Erms del Nether", "biome.minecraft.ocean": "Oceà", "biome.minecraft.old_growth_birch_forest": "Bedollar verge", "biome.minecraft.old_growth_pine_taiga": "Pineda verge", "biome.minecraft.old_growth_spruce_taiga": "Taig<PERSON>", "biome.minecraft.pale_garden": "J<PERSON><PERSON> pàl·lid", "biome.minecraft.plains": "Planes", "biome.minecraft.river": "Riu", "biome.minecraft.savanna": "Sabana", "biome.minecraft.savanna_plateau": "Altiplà de sabana", "biome.minecraft.small_end_islands": "Illes de l'End", "biome.minecraft.snowy_beach": "Platja nevada", "biome.minecraft.snowy_plains": "Planes nevades", "biome.minecraft.snowy_slopes": "Vessants nevades", "biome.minecraft.snowy_taiga": "Taigà nevada", "biome.minecraft.soul_sand_valley": "<PERSON>l d'<PERSON>", "biome.minecraft.sparse_jungle": "Jungla dispersa", "biome.minecraft.stony_peaks": "Cims pedregosos", "biome.minecraft.stony_shore": "Costa pedregosa", "biome.minecraft.sunflower_plains": "Plana de gira-sols", "biome.minecraft.swamp": "Pantà", "biome.minecraft.taiga": "Taigà", "biome.minecraft.the_end": "L'End", "biome.minecraft.the_void": "El buit", "biome.minecraft.warm_ocean": "Oceà càlid", "biome.minecraft.warped_forest": "Bosc guerxo", "biome.minecraft.windswept_forest": "Bosc ventós", "biome.minecraft.windswept_gravelly_hills": "<PERSON><PERSON> pedregosos ventosos", "biome.minecraft.windswept_hills": "<PERSON><PERSON> ventosos", "biome.minecraft.windswept_savanna": "Sabana ventosa", "biome.minecraft.wooded_badlands": "Terres ermes boscoses", "block.minecraft.acacia_button": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_door": "Porta d'acàcia", "block.minecraft.acacia_fence": "Tanca d'acàcia", "block.minecraft.acacia_fence_gate": "Porta de tanca d'acàcia", "block.minecraft.acacia_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> d'a<PERSON>", "block.minecraft.acacia_leaves": "Fulles d'acàcia", "block.minecraft.acacia_log": "Tronc d'acàcia", "block.minecraft.acacia_planks": "Fusta d'acàcia", "block.minecraft.acacia_pressure_plate": "Placa de pressió d'acàcia", "block.minecraft.acacia_sapling": "Esqueix d'acàcia", "block.minecraft.acacia_sign": "Cartell d'acàcia", "block.minecraft.acacia_slab": "Llosa d'acàcia", "block.minecraft.acacia_stairs": "<PERSON><PERSON><PERSON>'a<PERSON>", "block.minecraft.acacia_trapdoor": "Trapa d'acàcia", "block.minecraft.acacia_wall_hanging_sign": "<PERSON><PERSON><PERSON> penjant d'acàcia amb suport", "block.minecraft.acacia_wall_sign": "Cartell d'acàcia a la paret", "block.minecraft.acacia_wood": "Fusta d'acàcia", "block.minecraft.activator_rail": "Rail activador", "block.minecraft.air": "Aire", "block.minecraft.allium": "All bord", "block.minecraft.amethyst_block": "Bloc d'ametista", "block.minecraft.amethyst_cluster": "<PERSON><PERSON><PERSON><PERSON> d'ametista", "block.minecraft.ancient_debris": "Restes ancestrals", "block.minecraft.andesite": "Andesita", "block.minecraft.andesite_slab": "Llosa d'andesita", "block.minecraft.andesite_stairs": "Escales d'andesita", "block.minecraft.andesite_wall": "<PERSON>r d'and<PERSON><PERSON>", "block.minecraft.anvil": "Enclusa", "block.minecraft.attached_melon_stem": "Tija de síndria connectada", "block.minecraft.attached_pumpkin_stem": "Tija de carbassa connectada", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Fulles d'azalea", "block.minecraft.azure_bluet": "Houston<PERSON> caerulea", "block.minecraft.bamboo": "Bambú", "block.minecraft.bamboo_block": "Bloc de bambú", "block.minecraft.bamboo_button": "<PERSON><PERSON><PERSON>", "block.minecraft.bamboo_door": "Porta de bambú", "block.minecraft.bamboo_fence": "Tanca de bam<PERSON>ú", "block.minecraft.bamboo_fence_gate": "Porta de tanca de bambú", "block.minecraft.bamboo_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> de b<PERSON>", "block.minecraft.bamboo_mosaic": "Bambú entrellaçat", "block.minecraft.bamboo_mosaic_slab": "Llosa de bambú entrellaçat", "block.minecraft.bamboo_mosaic_stairs": "Escales de bambú entrellaçat", "block.minecraft.bamboo_planks": "Fusta de bam<PERSON>ú", "block.minecraft.bamboo_pressure_plate": "Placa de pressió de bambú", "block.minecraft.bamboo_sapling": "<PERSON><PERSON> de bam<PERSON>", "block.minecraft.bamboo_sign": "<PERSON><PERSON><PERSON> de b<PERSON>", "block.minecraft.bamboo_slab": "Llosa de bam<PERSON>ú", "block.minecraft.bamboo_stairs": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.bamboo_trapdoor": "Trapa de bam<PERSON>ú", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> de bamb<PERSON> amb suport", "block.minecraft.bamboo_wall_sign": "Cartell de bambú a la paret", "block.minecraft.banner.base.black": "Camp de sable", "block.minecraft.banner.base.blue": "Camp d'atzur", "block.minecraft.banner.base.brown": "Camp marró", "block.minecraft.banner.base.cyan": "Camp cian", "block.minecraft.banner.base.gray": "Camp gris", "block.minecraft.banner.base.green": "Camp de sinople", "block.minecraft.banner.base.light_blue": "Camp blau cel", "block.minecraft.banner.base.light_gray": "Camp d'argent", "block.minecraft.banner.base.lime": "Camp llima", "block.minecraft.banner.base.magenta": "Camp magenta", "block.minecraft.banner.base.orange": "Camp ataronjat", "block.minecraft.banner.base.pink": "Camp rosa", "block.minecraft.banner.base.purple": "Camp de porpra", "block.minecraft.banner.base.red": "Camp de gules", "block.minecraft.banner.base.white": "Camp blanc", "block.minecraft.banner.base.yellow": "Camp d'or", "block.minecraft.banner.border.black": "<PERSON><PERSON><PERSON> de sable", "block.minecraft.banner.border.blue": "Bordura d'atzur", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON> grisa", "block.minecraft.banner.border.green": "Bordura de sinople", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON> blau cel", "block.minecraft.banner.border.light_gray": "Bordura d'argent", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON> llima", "block.minecraft.banner.border.magenta": "Bordura magenta", "block.minecraft.banner.border.orange": "Bordura ataronjada", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.border.purple": "Bordura de porpra", "block.minecraft.banner.border.red": "Bordura de gules", "block.minecraft.banner.border.white": "<PERSON>rd<PERSON> blanca", "block.minecraft.banner.border.yellow": "Bordura d'or", "block.minecraft.banner.bricks.black": "Ma<PERSON><PERSON><PERSON> de sable", "block.minecraft.banner.bricks.blue": "Maçonat d'atzur", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.banner.bricks.gray": "Ma<PERSON><PERSON>t gris", "block.minecraft.banner.bricks.green": "Maçonat de sinople", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON><PERSON><PERSON> blau cel", "block.minecraft.banner.bricks.light_gray": "Ma<PERSON><PERSON><PERSON> d'argent", "block.minecraft.banner.bricks.lime": "Ma<PERSON><PERSON><PERSON> llima", "block.minecraft.banner.bricks.magenta": "Maçonat magenta", "block.minecraft.banner.bricks.orange": "Maçonat ataronjat", "block.minecraft.banner.bricks.pink": "Ma<PERSON><PERSON>t rosa", "block.minecraft.banner.bricks.purple": "Maçonat <PERSON>", "block.minecraft.banner.bricks.red": "Maçonat de gules", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON><PERSON> blanc", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON><PERSON> d'or", "block.minecraft.banner.circle.black": "<PERSON><PERSON> de sable", "block.minecraft.banner.circle.blue": "<PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.circle.gray": "<PERSON><PERSON> grisa", "block.minecraft.banner.circle.green": "<PERSON><PERSON> de sinople", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON> blau cel", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON><PERSON> d'argent", "block.minecraft.banner.circle.lime": "<PERSON><PERSON> ll<PERSON>", "block.minecraft.banner.circle.magenta": "Rodella magenta", "block.minecraft.banner.circle.orange": "<PERSON><PERSON> at<PERSON>", "block.minecraft.banner.circle.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.circle.purple": "<PERSON><PERSON>", "block.minecraft.banner.circle.red": "Rodella de gules", "block.minecraft.banner.circle.white": "<PERSON><PERSON> blanca", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON><PERSON> d'or", "block.minecraft.banner.creeper.black": "<PERSON> de creeper de sable", "block.minecraft.banner.creeper.blue": "<PERSON> de creeper d'atzur", "block.minecraft.banner.creeper.brown": "<PERSON> de creeper marró", "block.minecraft.banner.creeper.cyan": "<PERSON> creeper cian", "block.minecraft.banner.creeper.gray": "<PERSON> de creeper gris", "block.minecraft.banner.creeper.green": "<PERSON> de creeper de sinople", "block.minecraft.banner.creeper.light_blue": "<PERSON> de creeper blau cel", "block.minecraft.banner.creeper.light_gray": "<PERSON> de creeper d'argent", "block.minecraft.banner.creeper.lime": "<PERSON> de creeper llima", "block.minecraft.banner.creeper.magenta": "Cara de creeper magenta", "block.minecraft.banner.creeper.orange": "<PERSON> de creeper ataro<PERSON><PERSON>", "block.minecraft.banner.creeper.pink": "<PERSON> de creeper rosa", "block.minecraft.banner.creeper.purple": "<PERSON> de creeper porpra", "block.minecraft.banner.creeper.red": "Cara de creeper de gules", "block.minecraft.banner.creeper.white": "<PERSON> de creeper blanca", "block.minecraft.banner.creeper.yellow": "<PERSON> de creeper d'or", "block.minecraft.banner.cross.black": "Sautor de sable", "block.minecraft.banner.cross.blue": "Sautor <PERSON>", "block.minecraft.banner.cross.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "Sautor cian", "block.minecraft.banner.cross.gray": "Sautor gris", "block.minecraft.banner.cross.green": "Sautor de sinople", "block.minecraft.banner.cross.light_blue": "Sautor blau cel", "block.minecraft.banner.cross.light_gray": "Sautor d'argent", "block.minecraft.banner.cross.lime": "Sautor <PERSON><PERSON>", "block.minecraft.banner.cross.magenta": "Sautor magenta", "block.minecraft.banner.cross.orange": "Sautor <PERSON>", "block.minecraft.banner.cross.pink": "Sautor rosa", "block.minecraft.banner.cross.purple": "Sautor <PERSON>", "block.minecraft.banner.cross.red": "Sautor de gules", "block.minecraft.banner.cross.white": "Sautor blanc", "block.minecraft.banner.cross.yellow": "Sautor d'or", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON><PERSON> dentada de sable", "block.minecraft.banner.curly_border.blue": "<PERSON>rdura dentada d'atzur", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> dentada marr<PERSON>", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON> dentada cian", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON> dentada grisa", "block.minecraft.banner.curly_border.green": "Bordura dentada de sinople", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON><PERSON> dentada blau cel", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON><PERSON> dentada d'argent", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON> dentada llima", "block.minecraft.banner.curly_border.magenta": "Bordura dentada magenta", "block.minecraft.banner.curly_border.orange": "<PERSON><PERSON><PERSON> dentada at<PERSON>", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON> dentada rosa", "block.minecraft.banner.curly_border.purple": "Bordura dentada de p<PERSON>pra", "block.minecraft.banner.curly_border.red": "Bordura dentada de gules", "block.minecraft.banner.curly_border.white": "<PERSON>rdura dentada blanca", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON><PERSON> dentada d'or", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON>, 1r de sable", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON>, 1r d'at<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON>, 1r marr<PERSON>", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON>, 1r cian", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON>, 1r gris", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON>, 1r de sinople", "block.minecraft.banner.diagonal_left.light_blue": "<PERSON><PERSON>, 1r blau cel", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON>, 1r d'argent", "block.minecraft.banner.diagonal_left.lime": "<PERSON><PERSON>, 1r llima", "block.minecraft.banner.diagonal_left.magenta": "<PERSON><PERSON>, 1r magenta", "block.minecraft.banner.diagonal_left.orange": "<PERSON><PERSON>, 1r at<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.pink": "<PERSON><PERSON>, 1r rosa", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON>, 1r de porpra", "block.minecraft.banner.diagonal_left.red": "Tallat, 1r de gules", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON>, 1r blanc", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON>, 1r d'or", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON><PERSON>, 1r de sable", "block.minecraft.banner.diagonal_right.blue": "<PERSON><PERSON><PERSON>, 1r d'at<PERSON>r", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON>, 1r marr<PERSON>", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON>, 1r cian", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON>, 1r gris", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON>, 1r de sinople", "block.minecraft.banner.diagonal_right.light_blue": "<PERSON><PERSON><PERSON>, 1r blau cel", "block.minecraft.banner.diagonal_right.light_gray": "Trinxat, 1r d'argent", "block.minecraft.banner.diagonal_right.lime": "<PERSON><PERSON><PERSON>, 1r llima", "block.minecraft.banner.diagonal_right.magenta": "<PERSON><PERSON><PERSON>, 1r magenta", "block.minecraft.banner.diagonal_right.orange": "<PERSON><PERSON><PERSON>, 1r at<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON>, 1r rosa", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON><PERSON>, 1r de porpra", "block.minecraft.banner.diagonal_right.red": "Trinxat, 1r de gules", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON>, 1r blanc", "block.minecraft.banner.diagonal_right.yellow": "Trinxat, 1r d'or", "block.minecraft.banner.diagonal_up_left.black": "<PERSON>nx<PERSON>, 2n de sable", "block.minecraft.banner.diagonal_up_left.blue": "<PERSON>nxat, 2n d'atzur", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON><PERSON>, 2n marr<PERSON>", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON><PERSON>, 2n cian", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON><PERSON>, 2n gris", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON>, 2n de sinople", "block.minecraft.banner.diagonal_up_left.light_blue": "<PERSON><PERSON><PERSON>, 2n blau cel", "block.minecraft.banner.diagonal_up_left.light_gray": "Trinxat, 2n d'argent", "block.minecraft.banner.diagonal_up_left.lime": "<PERSON><PERSON><PERSON>, 2n llima", "block.minecraft.banner.diagonal_up_left.magenta": "<PERSON>nx<PERSON>, 2n magenta", "block.minecraft.banner.diagonal_up_left.orange": "<PERSON><PERSON><PERSON>, 2n ataronjat", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON><PERSON><PERSON>, 2n rosa", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON>nx<PERSON>, 2n de porpra", "block.minecraft.banner.diagonal_up_left.red": "Trinxat, 2n de gules", "block.minecraft.banner.diagonal_up_left.white": "<PERSON><PERSON><PERSON>, 2n blanc", "block.minecraft.banner.diagonal_up_left.yellow": "Trinxat, 2n d'or", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON>, 2n de sable", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON>, 2n d'at<PERSON>r", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON>, 2n marr<PERSON>", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON>, 2n cian", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON>, 2n gris", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON>, 2n de sinople", "block.minecraft.banner.diagonal_up_right.light_blue": "<PERSON><PERSON>, 2n blau cel", "block.minecraft.banner.diagonal_up_right.light_gray": "Tallat, 2n d'argent", "block.minecraft.banner.diagonal_up_right.lime": "<PERSON><PERSON>, 2n llima", "block.minecraft.banner.diagonal_up_right.magenta": "<PERSON><PERSON>, 2n magenta", "block.minecraft.banner.diagonal_up_right.orange": "<PERSON><PERSON>, 2n ataronjat", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON>, 2n rosa", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON>, 2n de porpra", "block.minecraft.banner.diagonal_up_right.red": "Tallat, 2n de gules", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON>, 2n blanc", "block.minecraft.banner.diagonal_up_right.yellow": "Tallat, 2n d'or", "block.minecraft.banner.flow.black": "Espiral negre", "block.minecraft.banner.flow.blue": "Espiral blau", "block.minecraft.banner.flow.brown": "Espiral marró", "block.minecraft.banner.flow.cyan": "Espiral cian", "block.minecraft.banner.flow.gray": "Espiral gris", "block.minecraft.banner.flow.green": "Espiral verd", "block.minecraft.banner.flow.light_blue": "Espiral blau cel", "block.minecraft.banner.flow.light_gray": "Espiral gris clar", "block.minecraft.banner.flow.lime": "Espiral llima", "block.minecraft.banner.flow.magenta": "Espiral magenta", "block.minecraft.banner.flow.orange": "Espiral taronja", "block.minecraft.banner.flow.pink": "Espiral rosa", "block.minecraft.banner.flow.purple": "Espiral morat", "block.minecraft.banner.flow.red": "Espiral vermell", "block.minecraft.banner.flow.white": "Espiral blanc", "block.minecraft.banner.flow.yellow": "Espiral groc", "block.minecraft.banner.flower.black": "Flor de sable", "block.minecraft.banner.flower.blue": "<PERSON>lor d'atzur", "block.minecraft.banner.flower.brown": "<PERSON><PERSON> marr<PERSON>", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.flower.gray": "Flor gris", "block.minecraft.banner.flower.green": "Flor de sinople", "block.minecraft.banner.flower.light_blue": "Flor blau cel", "block.minecraft.banner.flower.light_gray": "Flor d'argent", "block.minecraft.banner.flower.lime": "<PERSON>lor llima", "block.minecraft.banner.flower.magenta": "Flor magenta", "block.minecraft.banner.flower.orange": "Flor at<PERSON>", "block.minecraft.banner.flower.pink": "Flor rosa", "block.minecraft.banner.flower.purple": "Flor de porpra", "block.minecraft.banner.flower.red": "Flor de gules", "block.minecraft.banner.flower.white": "Flor blanca", "block.minecraft.banner.flower.yellow": "Flor d'or", "block.minecraft.banner.globe.black": "Planeta de sable", "block.minecraft.banner.globe.blue": "Planeta blau", "block.minecraft.banner.globe.brown": "Planeta marró", "block.minecraft.banner.globe.cyan": "Planeta cian", "block.minecraft.banner.globe.gray": "Planeta gris", "block.minecraft.banner.globe.green": "Planeta verd", "block.minecraft.banner.globe.light_blue": "Planeta blau cel", "block.minecraft.banner.globe.light_gray": "Planeta gris clar", "block.minecraft.banner.globe.lime": "Planeta llima", "block.minecraft.banner.globe.magenta": "Planeta magenta", "block.minecraft.banner.globe.orange": "Planeta taronja", "block.minecraft.banner.globe.pink": "Planeta rosa", "block.minecraft.banner.globe.purple": "Planeta morat", "block.minecraft.banner.globe.red": "Planeta roig", "block.minecraft.banner.globe.white": "Planeta blanc", "block.minecraft.banner.globe.yellow": "Planeta groc", "block.minecraft.banner.gradient.black": "Degradat de sable", "block.minecraft.banner.gradient.blue": "Degradat d'atzur", "block.minecraft.banner.gradient.brown": "Degradat marró", "block.minecraft.banner.gradient.cyan": "Degradat cian", "block.minecraft.banner.gradient.gray": "Degradat gris", "block.minecraft.banner.gradient.green": "Degradat de sinople", "block.minecraft.banner.gradient.light_blue": "Degradat blau cel", "block.minecraft.banner.gradient.light_gray": "Degradat d'argent", "block.minecraft.banner.gradient.lime": "Degradat lima", "block.minecraft.banner.gradient.magenta": "Degradat magenta", "block.minecraft.banner.gradient.orange": "Degradat ataronjat", "block.minecraft.banner.gradient.pink": "Degradat rosa", "block.minecraft.banner.gradient.purple": "Degradat de porpra", "block.minecraft.banner.gradient.red": "Degradat de gules", "block.minecraft.banner.gradient.white": "Degradat blanc", "block.minecraft.banner.gradient.yellow": "Degradat d'or", "block.minecraft.banner.gradient_up.black": "Peu degradat de sable", "block.minecraft.banner.gradient_up.blue": "Peu degradat d'atzur", "block.minecraft.banner.gradient_up.brown": "Peu degradat marró", "block.minecraft.banner.gradient_up.cyan": "Peu degradat cian", "block.minecraft.banner.gradient_up.gray": "Peu degradat gris", "block.minecraft.banner.gradient_up.green": "Peu degradat de sinople", "block.minecraft.banner.gradient_up.light_blue": "Peu degradat blau cel", "block.minecraft.banner.gradient_up.light_gray": "Peu degradat d'argent", "block.minecraft.banner.gradient_up.lime": "Peu degradat lima", "block.minecraft.banner.gradient_up.magenta": "Peu degradat magenta", "block.minecraft.banner.gradient_up.orange": "Peu degradat ataronjat", "block.minecraft.banner.gradient_up.pink": "Peu degradat rosa", "block.minecraft.banner.gradient_up.purple": "Peu degradat de porpra", "block.minecraft.banner.gradient_up.red": "Peu degradat de gules", "block.minecraft.banner.gradient_up.white": "Peu degradat blanc", "block.minecraft.banner.gradient_up.yellow": "Peu degradat d'or", "block.minecraft.banner.guster.black": "<PERSON><PERSON><PERSON> negra", "block.minecraft.banner.guster.blue": "<PERSON><PERSON>a blava", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON> grisa", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON> verda", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON> blau cel", "block.minecraft.banner.guster.light_gray": "<PERSON>risa gris clar", "block.minecraft.banner.guster.lime": "<PERSON><PERSON><PERSON> llima", "block.minecraft.banner.guster.magenta": "<PERSON>risa magenta", "block.minecraft.banner.guster.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.guster.purple": "<PERSON><PERSON><PERSON> morada", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON> vermella", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON> blanca", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON> groga", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON><PERSON>, 1r de sable", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON>, 1r d'at<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON>, 1r marr<PERSON>", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON>, 1r cian", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON><PERSON>, 1r gris", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON>, 1r de sinople", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON><PERSON>, 1r blau cel", "block.minecraft.banner.half_horizontal.light_gray": "<PERSON><PERSON><PERSON>, 1r d'argent", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON><PERSON>, 1r llima", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON><PERSON>, 1r magenta", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON>, 1r at<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON>, 1r rosa", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON><PERSON>, 1r de porpra", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON><PERSON>, 1r de gules", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON>, 1r blanc", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON><PERSON>, 1r d'or", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON><PERSON><PERSON>, 2n de sable", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON><PERSON>, 2n d'at<PERSON>r", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON><PERSON>, 2n marr<PERSON>", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON><PERSON>, 2n cian", "block.minecraft.banner.half_horizontal_bottom.gray": "<PERSON><PERSON><PERSON>, 2n gris", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON><PERSON>, 2n de sinople", "block.minecraft.banner.half_horizontal_bottom.light_blue": "<PERSON><PERSON><PERSON>, 2n blau cel", "block.minecraft.banner.half_horizontal_bottom.light_gray": "<PERSON><PERSON><PERSON>, 2n d'argent", "block.minecraft.banner.half_horizontal_bottom.lime": "<PERSON><PERSON><PERSON>, 2n llima", "block.minecraft.banner.half_horizontal_bottom.magenta": "<PERSON><PERSON><PERSON>, 2n magenta", "block.minecraft.banner.half_horizontal_bottom.orange": "<PERSON><PERSON><PERSON>, 2n ataro<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON><PERSON><PERSON>, 2n rosa", "block.minecraft.banner.half_horizontal_bottom.purple": "<PERSON><PERSON><PERSON>, 2n de porpra", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON><PERSON>, 2n de gules", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON><PERSON>, 2n blanc", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON><PERSON>, 2n d'or", "block.minecraft.banner.half_vertical.black": "Partit, 1r de sable", "block.minecraft.banner.half_vertical.blue": "<PERSON>it, 1r d'at<PERSON>r", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON>, 1r marr<PERSON>", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON>, 1r cian", "block.minecraft.banner.half_vertical.gray": "<PERSON><PERSON>, 1r gris", "block.minecraft.banner.half_vertical.green": "Partit, 1r de sinople", "block.minecraft.banner.half_vertical.light_blue": "<PERSON><PERSON>, 1r blau cel", "block.minecraft.banner.half_vertical.light_gray": "Partit, 1r d'argent", "block.minecraft.banner.half_vertical.lime": "<PERSON><PERSON>, 1r llima", "block.minecraft.banner.half_vertical.magenta": "<PERSON>it, 1r magenta", "block.minecraft.banner.half_vertical.orange": "<PERSON><PERSON>, 1r at<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON>, 1r rosa", "block.minecraft.banner.half_vertical.purple": "Partit, 1r de porpra", "block.minecraft.banner.half_vertical.red": "Partit, 1r de gules", "block.minecraft.banner.half_vertical.white": "<PERSON><PERSON>, 1r blanc", "block.minecraft.banner.half_vertical.yellow": "Partit, 1r d'or", "block.minecraft.banner.half_vertical_right.black": "Partit, 2n de sable", "block.minecraft.banner.half_vertical_right.blue": "<PERSON>it, 2n d'atzur", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON>, 2n marr<PERSON>", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON>, 2n cian", "block.minecraft.banner.half_vertical_right.gray": "<PERSON>it, 2n gris", "block.minecraft.banner.half_vertical_right.green": "Partit, 2n de sinople", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON>, 2n blau cel", "block.minecraft.banner.half_vertical_right.light_gray": "Partit, 2n d'argent", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON>, 2n llima", "block.minecraft.banner.half_vertical_right.magenta": "Partit, 2n magenta", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON>, 2n ataronjat", "block.minecraft.banner.half_vertical_right.pink": "<PERSON>it, 2n rosa", "block.minecraft.banner.half_vertical_right.purple": "Partit, 2n de porpra", "block.minecraft.banner.half_vertical_right.red": "Partit, 2n de gules", "block.minecraft.banner.half_vertical_right.white": "<PERSON>it, 2n blanc", "block.minecraft.banner.half_vertical_right.yellow": "Partit, 2n d'or", "block.minecraft.banner.mojang.black": "<PERSON><PERSON><PERSON> de sable", "block.minecraft.banner.mojang.blue": "Emblema d'atzur", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON> de sinople", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON><PERSON> blau cel", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON><PERSON> d'argent", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON><PERSON> llima", "block.minecraft.banner.mojang.magenta": "Emblema magenta", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON><PERSON> at<PERSON>", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.red": "Emblema de gules", "block.minecraft.banner.mojang.white": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON><PERSON> d'or", "block.minecraft.banner.piglin.black": "Morro de sable", "block.minecraft.banner.piglin.blue": "Morro <PERSON>", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON>", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.piglin.green": "Morro de sinople", "block.minecraft.banner.piglin.light_blue": "<PERSON>rro blau cel", "block.minecraft.banner.piglin.light_gray": "Morro d'argent", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON> llima", "block.minecraft.banner.piglin.magenta": "Morro magenta", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON>", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON> porpra", "block.minecraft.banner.piglin.red": "Morro de gules", "block.minecraft.banner.piglin.white": "<PERSON><PERSON> blanc", "block.minecraft.banner.piglin.yellow": "Morro d'or", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON><PERSON> de sable", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.rhombus.green": "Losange de sinople", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON> blau cel", "block.minecraft.banner.rhombus.light_gray": "Losange d'argent", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.banner.rhombus.magenta": "Losange magenta", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.red": "Losange de gules", "block.minecraft.banner.rhombus.white": "Losange blanca", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON> d'or", "block.minecraft.banner.skull.black": "Calavera de sable", "block.minecraft.banner.skull.blue": "Calavera d'atzur", "block.minecraft.banner.skull.brown": "<PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.banner.skull.cyan": "Calavera cian", "block.minecraft.banner.skull.gray": "Calavera grisa", "block.minecraft.banner.skull.green": "Calavera de sinople", "block.minecraft.banner.skull.light_blue": "Calavera blau cel", "block.minecraft.banner.skull.light_gray": "Calavera d'argent", "block.minecraft.banner.skull.lime": "Calavera llima", "block.minecraft.banner.skull.magenta": "Calavera magenta", "block.minecraft.banner.skull.orange": "Calavera ataro<PERSON>da", "block.minecraft.banner.skull.pink": "Calaver<PERSON> rosa", "block.minecraft.banner.skull.purple": "Calavera de p<PERSON>pra", "block.minecraft.banner.skull.red": "Calavera de gules", "block.minecraft.banner.skull.white": "Calavera blanca", "block.minecraft.banner.skull.yellow": "Calavera d'or", "block.minecraft.banner.small_stripes.black": "Quatre pals de sable", "block.minecraft.banner.small_stripes.blue": "Quatre pals d'atzur", "block.minecraft.banner.small_stripes.brown": "Quatre pals marrons", "block.minecraft.banner.small_stripes.cyan": "Quatre pals cian", "block.minecraft.banner.small_stripes.gray": "Quatre pals grisos", "block.minecraft.banner.small_stripes.green": "Quatre pals de sinople", "block.minecraft.banner.small_stripes.light_blue": "Quatre pals blau cel", "block.minecraft.banner.small_stripes.light_gray": "Quatre pals d'argent", "block.minecraft.banner.small_stripes.lime": "Quatre pals llima", "block.minecraft.banner.small_stripes.magenta": "Quatre pals magenta", "block.minecraft.banner.small_stripes.orange": "Quatre pals ataronjats", "block.minecraft.banner.small_stripes.pink": "Quatre pals rosa", "block.minecraft.banner.small_stripes.purple": "Quatre pals de porpra", "block.minecraft.banner.small_stripes.red": "Quatre pals de gules", "block.minecraft.banner.small_stripes.white": "Quatre pals blancs", "block.minecraft.banner.small_stripes.yellow": "Quatre pals d'or", "block.minecraft.banner.square_bottom_left.black": "Cantó destre de la punta de sable", "block.minecraft.banner.square_bottom_left.blue": "Cantó destre de la punta d'atzur", "block.minecraft.banner.square_bottom_left.brown": "Cantó destre de la punta marró", "block.minecraft.banner.square_bottom_left.cyan": "Cantó destre de la punta cian", "block.minecraft.banner.square_bottom_left.gray": "Cantó destre de la punta gris", "block.minecraft.banner.square_bottom_left.green": "Cantó destre de la punta de sinople", "block.minecraft.banner.square_bottom_left.light_blue": "Cantó destre de la punta blau cel", "block.minecraft.banner.square_bottom_left.light_gray": "Cantó destre de la punta d'argent", "block.minecraft.banner.square_bottom_left.lime": "Cantó destre de la punta llima", "block.minecraft.banner.square_bottom_left.magenta": "Cantó destre de la punta magenta", "block.minecraft.banner.square_bottom_left.orange": "Cantó destre de la punta ataronjat", "block.minecraft.banner.square_bottom_left.pink": "Cantó destre de la punta rosa", "block.minecraft.banner.square_bottom_left.purple": "Cantó destre de la punta de porpra", "block.minecraft.banner.square_bottom_left.red": "Cantó destre de la punta de gules", "block.minecraft.banner.square_bottom_left.white": "Cantó destre de la punta blanc", "block.minecraft.banner.square_bottom_left.yellow": "Cantó destre de la punta d'or", "block.minecraft.banner.square_bottom_right.black": "Cantó sinistre de la punta de sable", "block.minecraft.banner.square_bottom_right.blue": "Cantó sinistre de la punta d'atzur", "block.minecraft.banner.square_bottom_right.brown": "Cantó sinistre de la punta marró", "block.minecraft.banner.square_bottom_right.cyan": "Cantó sinistre de la punta cian", "block.minecraft.banner.square_bottom_right.gray": "Cantó sinistre de la punta gris", "block.minecraft.banner.square_bottom_right.green": "Cantó sinistre de la punta de sinople", "block.minecraft.banner.square_bottom_right.light_blue": "Cantó sinistre de la punta blau cel", "block.minecraft.banner.square_bottom_right.light_gray": "Cantó sinistre de la punta d'argent", "block.minecraft.banner.square_bottom_right.lime": "Cantó sinistre de la punta llima", "block.minecraft.banner.square_bottom_right.magenta": "Cantó sinistre de la punta magenta", "block.minecraft.banner.square_bottom_right.orange": "Cantó sinistre de la punta ataronjat", "block.minecraft.banner.square_bottom_right.pink": "Cantó sinistre de la punta rosa", "block.minecraft.banner.square_bottom_right.purple": "Cantó sinistre de la punta de porpra", "block.minecraft.banner.square_bottom_right.red": "Cantó sinistre de la punta de gules", "block.minecraft.banner.square_bottom_right.white": "Cantó sinistre de la punta blanc", "block.minecraft.banner.square_bottom_right.yellow": "Cantó sinistre de la punta d'or", "block.minecraft.banner.square_top_left.black": "Cantó destre del cap de sable", "block.minecraft.banner.square_top_left.blue": "Cantó destre del cap d'atzur", "block.minecraft.banner.square_top_left.brown": "Cantó destre del cap marró", "block.minecraft.banner.square_top_left.cyan": "Cantó destre del cap cian", "block.minecraft.banner.square_top_left.gray": "Cantó destre del cap gris", "block.minecraft.banner.square_top_left.green": "Cantó destre del cap de sinople", "block.minecraft.banner.square_top_left.light_blue": "Cantó destre del cap blau cel", "block.minecraft.banner.square_top_left.light_gray": "Cantó destre del cap d'argent", "block.minecraft.banner.square_top_left.lime": "Cantó destre del cap llima", "block.minecraft.banner.square_top_left.magenta": "Cantó destre del cap magenta", "block.minecraft.banner.square_top_left.orange": "Cantó destre del cap ataronjat", "block.minecraft.banner.square_top_left.pink": "Cantó destre del cap rosa", "block.minecraft.banner.square_top_left.purple": "Cantó destre del cap de porpra", "block.minecraft.banner.square_top_left.red": "Cantó destre del cap de gules", "block.minecraft.banner.square_top_left.white": "Cantó destre del cap blanc", "block.minecraft.banner.square_top_left.yellow": "Cantó destre del cap d'or", "block.minecraft.banner.square_top_right.black": "Cantó sinistre del cap de sable", "block.minecraft.banner.square_top_right.blue": "Cantó sinistre del cap d'atzur", "block.minecraft.banner.square_top_right.brown": "Cantó sinistre del cap marró", "block.minecraft.banner.square_top_right.cyan": "Cantó sinistre del cap cian", "block.minecraft.banner.square_top_right.gray": "Cantó sinistre del cap gris", "block.minecraft.banner.square_top_right.green": "Cantó sinistre del cap de sinople", "block.minecraft.banner.square_top_right.light_blue": "Cantó sinistre del cap blau cel", "block.minecraft.banner.square_top_right.light_gray": "Cantó sinistre del cap d'argent", "block.minecraft.banner.square_top_right.lime": "Cantó sinistre del cap llima", "block.minecraft.banner.square_top_right.magenta": "Cantó sinistre del cap magenta", "block.minecraft.banner.square_top_right.orange": "Cantó sinistre del cap ataronjat", "block.minecraft.banner.square_top_right.pink": "Cantó sinistre del cap rosa", "block.minecraft.banner.square_top_right.purple": "Cantó sinistre del cap de porpra", "block.minecraft.banner.square_top_right.red": "Cantó sinistre del cap de gules", "block.minecraft.banner.square_top_right.white": "Cantó sinistre del cap blanc", "block.minecraft.banner.square_top_right.yellow": "Cantó sinistre del cap d'or", "block.minecraft.banner.straight_cross.black": "<PERSON><PERSON><PERSON> de sable", "block.minecraft.banner.straight_cross.blue": "<PERSON>reu d'atzur", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON> grisa", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON>u de sinople", "block.minecraft.banner.straight_cross.light_blue": "<PERSON><PERSON><PERSON> blau cel", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON><PERSON> d'argent", "block.minecraft.banner.straight_cross.lime": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.straight_cross.magenta": "Creu magenta", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.red": "Creu de gules", "block.minecraft.banner.straight_cross.white": "Creu blanca", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON><PERSON> d'or", "block.minecraft.banner.stripe_bottom.black": "Peu de sable", "block.minecraft.banner.stripe_bottom.blue": "Peu d'atzur", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON>ian", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.stripe_bottom.green": "Peu de sinople", "block.minecraft.banner.stripe_bottom.light_blue": "<PERSON><PERSON> blau cel", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON>eu d'argent", "block.minecraft.banner.stripe_bottom.lime": "<PERSON><PERSON> <PERSON>ima", "block.minecraft.banner.stripe_bottom.magenta": "Peu magenta", "block.minecraft.banner.stripe_bottom.orange": "Peu ataronja<PERSON>", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.stripe_bottom.purple": "Peu de porpra", "block.minecraft.banner.stripe_bottom.red": "Peu de gules", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON> blanc", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON>eu d'or", "block.minecraft.banner.stripe_center.black": "Pal de sable", "block.minecraft.banner.stripe_center.blue": "Pal d'atzur", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "Pal cian", "block.minecraft.banner.stripe_center.gray": "Pal gris", "block.minecraft.banner.stripe_center.green": "Pal de sinople", "block.minecraft.banner.stripe_center.light_blue": "Pal blau cel", "block.minecraft.banner.stripe_center.light_gray": "Pal d'argent", "block.minecraft.banner.stripe_center.lime": "<PERSON>l llima", "block.minecraft.banner.stripe_center.magenta": "Pal magenta", "block.minecraft.banner.stripe_center.orange": "Pal ataro<PERSON>", "block.minecraft.banner.stripe_center.pink": "Pal rosa", "block.minecraft.banner.stripe_center.purple": "Pal de porpra", "block.minecraft.banner.stripe_center.red": "Pal de gules", "block.minecraft.banner.stripe_center.white": "<PERSON>l blanc", "block.minecraft.banner.stripe_center.yellow": "Pal d'or", "block.minecraft.banner.stripe_downleft.black": "Barra de sable", "block.minecraft.banner.stripe_downleft.blue": "Barra d'atzur", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.stripe_downleft.gray": "<PERSON>a grisa", "block.minecraft.banner.stripe_downleft.green": "Barra de sinople", "block.minecraft.banner.stripe_downleft.light_blue": "Barra blau cel", "block.minecraft.banner.stripe_downleft.light_gray": "Barra d'argent", "block.minecraft.banner.stripe_downleft.lime": "<PERSON><PERSON> llima", "block.minecraft.banner.stripe_downleft.magenta": "Barra magenta", "block.minecraft.banner.stripe_downleft.orange": "Barra at<PERSON>", "block.minecraft.banner.stripe_downleft.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.stripe_downleft.purple": "Barra de p<PERSON>pra", "block.minecraft.banner.stripe_downleft.red": "Barra de gules", "block.minecraft.banner.stripe_downleft.white": "Barra blanca", "block.minecraft.banner.stripe_downleft.yellow": "Barra d'or", "block.minecraft.banner.stripe_downright.black": "Banda de sable", "block.minecraft.banner.stripe_downright.blue": "Banda d'atzur", "block.minecraft.banner.stripe_downright.brown": "Banda marró", "block.minecraft.banner.stripe_downright.cyan": "Banda cian", "block.minecraft.banner.stripe_downright.gray": "Banda grisa", "block.minecraft.banner.stripe_downright.green": "Banda de sinople", "block.minecraft.banner.stripe_downright.light_blue": "Banda blau cel", "block.minecraft.banner.stripe_downright.light_gray": "Banda d'argent", "block.minecraft.banner.stripe_downright.lime": "Banda llima", "block.minecraft.banner.stripe_downright.magenta": "Banda magenta", "block.minecraft.banner.stripe_downright.orange": "Banda ataronjada", "block.minecraft.banner.stripe_downright.pink": "Banda rosa", "block.minecraft.banner.stripe_downright.purple": "Banda de porpra", "block.minecraft.banner.stripe_downright.red": "Banda de gules", "block.minecraft.banner.stripe_downright.white": "Banda blanca", "block.minecraft.banner.stripe_downright.yellow": "Banda d'or", "block.minecraft.banner.stripe_left.black": "Dextrat de sable", "block.minecraft.banner.stripe_left.blue": "Dextrat d'atzur", "block.minecraft.banner.stripe_left.brown": "Dextrat marró", "block.minecraft.banner.stripe_left.cyan": "Dex<PERSON>t cian", "block.minecraft.banner.stripe_left.gray": "Dextrat gris", "block.minecraft.banner.stripe_left.green": "Dextrat de sinople", "block.minecraft.banner.stripe_left.light_blue": "Dextrat blau cel", "block.minecraft.banner.stripe_left.light_gray": "Dextrat d'argent", "block.minecraft.banner.stripe_left.lime": "Dextrat llima", "block.minecraft.banner.stripe_left.magenta": "Dextrat magenta", "block.minecraft.banner.stripe_left.orange": "Dextrat ataronjat", "block.minecraft.banner.stripe_left.pink": "Dextrat rosa", "block.minecraft.banner.stripe_left.purple": "Dextrat de porpra", "block.minecraft.banner.stripe_left.red": "Dextrat de gules", "block.minecraft.banner.stripe_left.white": "Dextrat blanc", "block.minecraft.banner.stripe_left.yellow": "Dextrat d'or", "block.minecraft.banner.stripe_middle.black": "<PERSON>aixa de sable", "block.minecraft.banner.stripe_middle.blue": "Faixa d'atzur", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.stripe_middle.cyan": "Faixa cian", "block.minecraft.banner.stripe_middle.gray": "Faixa grisa", "block.minecraft.banner.stripe_middle.green": "Faixa de sinople", "block.minecraft.banner.stripe_middle.light_blue": "Faixa blau cel", "block.minecraft.banner.stripe_middle.light_gray": "Fai<PERSON> d'argent", "block.minecraft.banner.stripe_middle.lime": "Faixa llima", "block.minecraft.banner.stripe_middle.magenta": "Faixa magenta", "block.minecraft.banner.stripe_middle.orange": "Faixa ataronjada", "block.minecraft.banner.stripe_middle.pink": "Faixa rosa", "block.minecraft.banner.stripe_middle.purple": "Faixa de porpra", "block.minecraft.banner.stripe_middle.red": "Faixa de gules", "block.minecraft.banner.stripe_middle.white": "Faixa blanca", "block.minecraft.banner.stripe_middle.yellow": "Faixa d'or", "block.minecraft.banner.stripe_right.black": "<PERSON><PERSON><PERSON> de sable", "block.minecraft.banner.stripe_right.blue": "Sinistrat d'atzur", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.stripe_right.gray": "Sinistrat gris", "block.minecraft.banner.stripe_right.green": "Sinistrat de sinople", "block.minecraft.banner.stripe_right.light_blue": "Sin<PERSON><PERSON> blau cel", "block.minecraft.banner.stripe_right.light_gray": "<PERSON><PERSON><PERSON> d'argent", "block.minecraft.banner.stripe_right.lime": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.banner.stripe_right.magenta": "Sinistrat magenta", "block.minecraft.banner.stripe_right.orange": "Sinistrat ataro<PERSON>t", "block.minecraft.banner.stripe_right.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.stripe_right.purple": "Sinist<PERSON> p<PERSON>", "block.minecraft.banner.stripe_right.red": "Sinistrat de gules", "block.minecraft.banner.stripe_right.white": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.banner.stripe_right.yellow": "Sinist<PERSON> d'or", "block.minecraft.banner.stripe_top.black": "Cap de sable", "block.minecraft.banner.stripe_top.blue": "Cap d'atzur", "block.minecraft.banner.stripe_top.brown": "Cap marró", "block.minecraft.banner.stripe_top.cyan": "Cap cian", "block.minecraft.banner.stripe_top.gray": "Cap gris", "block.minecraft.banner.stripe_top.green": "Cap de sinople", "block.minecraft.banner.stripe_top.light_blue": "Cap blau cel", "block.minecraft.banner.stripe_top.light_gray": "Cap d'argent", "block.minecraft.banner.stripe_top.lime": "Cap llima", "block.minecraft.banner.stripe_top.magenta": "Cap magenta", "block.minecraft.banner.stripe_top.orange": "Cap ataronjat", "block.minecraft.banner.stripe_top.pink": "Cap rosa", "block.minecraft.banner.stripe_top.purple": "Cap de porpra", "block.minecraft.banner.stripe_top.red": "Cap de gules", "block.minecraft.banner.stripe_top.white": "Cap blanc", "block.minecraft.banner.stripe_top.yellow": "Cap d'or", "block.minecraft.banner.triangle_bottom.black": "Gaiat a la punta de sable", "block.minecraft.banner.triangle_bottom.blue": "Gaiat a la punta d'atzur", "block.minecraft.banner.triangle_bottom.brown": "Gaiat a la punta marró", "block.minecraft.banner.triangle_bottom.cyan": "Gaiat a la punta cian", "block.minecraft.banner.triangle_bottom.gray": "Gaiat a la punta gris", "block.minecraft.banner.triangle_bottom.green": "Gaiat a la punta de sinople", "block.minecraft.banner.triangle_bottom.light_blue": "Gaiat a la punta blau cel", "block.minecraft.banner.triangle_bottom.light_gray": "Gaiat a la punta d'argent", "block.minecraft.banner.triangle_bottom.lime": "Gaiat a la punta llima", "block.minecraft.banner.triangle_bottom.magenta": "Gaiat a la punta magenta", "block.minecraft.banner.triangle_bottom.orange": "Gaiat a la punta ataronjat", "block.minecraft.banner.triangle_bottom.pink": "Gaiat a la punta rosa", "block.minecraft.banner.triangle_bottom.purple": "Gaiat a la punta de porpra", "block.minecraft.banner.triangle_bottom.red": "Gaiat a la punta de gules", "block.minecraft.banner.triangle_bottom.white": "Gaiat a la punta blanc", "block.minecraft.banner.triangle_bottom.yellow": "Gaiat a la punta d'or", "block.minecraft.banner.triangle_top.black": "Gaiat al cap de sable", "block.minecraft.banner.triangle_top.blue": "Gaiat al cap d'atzur", "block.minecraft.banner.triangle_top.brown": "Gaiat al cap marró", "block.minecraft.banner.triangle_top.cyan": "Gaiat al cap cian", "block.minecraft.banner.triangle_top.gray": "Gaiat al cap gris", "block.minecraft.banner.triangle_top.green": "Gaiat al cap de sinople", "block.minecraft.banner.triangle_top.light_blue": "Gaiat al cap blau cel", "block.minecraft.banner.triangle_top.light_gray": "Gaiat al cap d'argent", "block.minecraft.banner.triangle_top.lime": "Gaiat al cap llima", "block.minecraft.banner.triangle_top.magenta": "Gaiat al cap magenta", "block.minecraft.banner.triangle_top.orange": "Gaiat al cap ataronjat", "block.minecraft.banner.triangle_top.pink": "Gaiat al cap rosa", "block.minecraft.banner.triangle_top.purple": "Gaiat al cap de porpra", "block.minecraft.banner.triangle_top.red": "Gaiat al cap de gules", "block.minecraft.banner.triangle_top.white": "Gaiat al cap blanc", "block.minecraft.banner.triangle_top.yellow": "Gaiat al cap d'or", "block.minecraft.banner.triangles_bottom.black": "Peu dentat de sable", "block.minecraft.banner.triangles_bottom.blue": "Peu dentat d'atzur", "block.minecraft.banner.triangles_bottom.brown": "Peu dentat marró", "block.minecraft.banner.triangles_bottom.cyan": "Peu dentat cian", "block.minecraft.banner.triangles_bottom.gray": "Peu dentat gris", "block.minecraft.banner.triangles_bottom.green": "Peu dentat de sinople", "block.minecraft.banner.triangles_bottom.light_blue": "Peu dentat blau cel", "block.minecraft.banner.triangles_bottom.light_gray": "Peu dentat d'argent", "block.minecraft.banner.triangles_bottom.lime": "Peu dentat llima", "block.minecraft.banner.triangles_bottom.magenta": "Peu dentat magenta", "block.minecraft.banner.triangles_bottom.orange": "Peu dentat ataronjat", "block.minecraft.banner.triangles_bottom.pink": "Peu dentat rosa", "block.minecraft.banner.triangles_bottom.purple": "Peu dentat de porpra", "block.minecraft.banner.triangles_bottom.red": "Peu dentat de gules", "block.minecraft.banner.triangles_bottom.white": "Peu dentat blanc", "block.minecraft.banner.triangles_bottom.yellow": "Peu dentat d'or", "block.minecraft.banner.triangles_top.black": "Cap dentat de sable", "block.minecraft.banner.triangles_top.blue": "Cap dentat d'atzur", "block.minecraft.banner.triangles_top.brown": "Cap dentat marró", "block.minecraft.banner.triangles_top.cyan": "Cap dentat cian", "block.minecraft.banner.triangles_top.gray": "Cap dentat gris", "block.minecraft.banner.triangles_top.green": "Cap dentat de sinople", "block.minecraft.banner.triangles_top.light_blue": "Cap dentat blau cel", "block.minecraft.banner.triangles_top.light_gray": "Cap dentat d'argent", "block.minecraft.banner.triangles_top.lime": "Cap dentat llima", "block.minecraft.banner.triangles_top.magenta": "Cap dentat magenta", "block.minecraft.banner.triangles_top.orange": "Cap dentat ataronjat", "block.minecraft.banner.triangles_top.pink": "Cap dentat rosa", "block.minecraft.banner.triangles_top.purple": "Cap dentat de porpra", "block.minecraft.banner.triangles_top.red": "Cap dentat de gules", "block.minecraft.banner.triangles_top.white": "Cap dentat blanc", "block.minecraft.banner.triangles_top.yellow": "Cap dentat d'or", "block.minecraft.barrel": "Barril", "block.minecraft.barrier": "Barr<PERSON>", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "Far", "block.minecraft.beacon.primary": "<PERSON><PERSON> primari", "block.minecraft.beacon.secondary": "<PERSON><PERSON> secundari", "block.minecraft.bed.no_sleep": "Només pots dormir durant la nit o tempestes", "block.minecraft.bed.not_safe": "No hauries de dormir ara, hi ha monstres a prop teu", "block.minecraft.bed.obstructed": "El llit està obstruït", "block.minecraft.bed.occupied": "Aquest llit està ocupat", "block.minecraft.bed.too_far_away": "No hauries de dormir ara, el llit està massa lluny", "block.minecraft.bedrock": "Roca base", "block.minecraft.bee_nest": "<PERSON><PERSON><PERSON>", "block.minecraft.beehive": "Rusc fix", "block.minecraft.beetroots": "Remolatxes", "block.minecraft.bell": "Campana", "block.minecraft.big_dripleaf": "Degotadora gran", "block.minecraft.big_dripleaf_stem": "Tija de degotadora gran", "block.minecraft.birch_button": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_door": "Porta de fusta de bedoll", "block.minecraft.birch_fence": "Tanca de bedoll", "block.minecraft.birch_fence_gate": "Porta de tanca de bedoll", "block.minecraft.birch_hanging_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_leaves": "<PERSON><PERSON> de <PERSON>", "block.minecraft.birch_log": "Tronc de bedoll", "block.minecraft.birch_planks": "<PERSON><PERSON>", "block.minecraft.birch_pressure_plate": "Placa de pressió de bedoll", "block.minecraft.birch_sapling": "Esqueix de <PERSON>", "block.minecraft.birch_sign": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.birch_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> de <PERSON>oll amb suport", "block.minecraft.birch_wall_sign": "Cartel<PERSON> de bedoll a la paret", "block.minecraft.birch_wood": "<PERSON><PERSON>", "block.minecraft.black_banner": "<PERSON><PERSON><PERSON><PERSON> de sable", "block.minecraft.black_bed": "<PERSON><PERSON> negre", "block.minecraft.black_candle": "Espel<PERSON> negra", "block.minecraft.black_candle_cake": "Pastís amb espelma negra", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON> negra", "block.minecraft.black_concrete": "Formigó negre", "block.minecraft.black_concrete_powder": "Pols de formigó negre", "block.minecraft.black_glazed_terracotta": "<PERSON><PERSON> negra", "block.minecraft.black_shulker_box": "<PERSON><PERSON><PERSON> de shulker negra", "block.minecraft.black_stained_glass": "<PERSON><PERSON><PERSON> tenyit de negre", "block.minecraft.black_stained_glass_pane": "Panell de vidre tenyit de negre", "block.minecraft.black_terracotta": "Terracota negra", "block.minecraft.black_wool": "<PERSON><PERSON> negra", "block.minecraft.blackstone": "Roca negra", "block.minecraft.blackstone_slab": "Llosa de roca negra", "block.minecraft.blackstone_stairs": "Escales de roca negra", "block.minecraft.blackstone_wall": "Mur de roca negra", "block.minecraft.blast_furnace": "Alt forn", "block.minecraft.blue_banner": "Estendard d'atzur", "block.minecraft.blue_bed": "<PERSON><PERSON> blau", "block.minecraft.blue_candle": "Espelma blava", "block.minecraft.blue_candle_cake": "Pastís amb espelma blava", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON> blava", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON> blau", "block.minecraft.blue_concrete_powder": "Pols de formigó blau", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON> blava", "block.minecraft.blue_ice": "<PERSON><PERSON> blau", "block.minecraft.blue_orchid": "Orquídia blava", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON>ker blava", "block.minecraft.blue_stained_glass": "<PERSON><PERSON><PERSON> ten<PERSON> de blau", "block.minecraft.blue_stained_glass_pane": "<PERSON>l de vidre tenyit de blau", "block.minecraft.blue_terracotta": "Terracota blava", "block.minecraft.blue_wool": "<PERSON><PERSON> blava", "block.minecraft.bone_block": "Bloc d'os", "block.minecraft.bookshelf": "Llibreria", "block.minecraft.brain_coral": "<PERSON><PERSON>", "block.minecraft.brain_coral_block": "Bloc de corall de cervell", "block.minecraft.brain_coral_fan": "Gorgònia de cervell", "block.minecraft.brain_coral_wall_fan": "Gorgònia de cervell a la paret", "block.minecraft.brewing_stand": "Altar de pocions", "block.minecraft.brick_slab": "Llosa <PERSON>", "block.minecraft.brick_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.brick_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.bricks": "<PERSON><PERSON>", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_bed": "<PERSON><PERSON>rr<PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.brown_candle_cake": "Pastís amb espelma marr<PERSON>", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "Pols de formigó marró", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON>", "block.minecraft.brown_mushroom": "<PERSON><PERSON>", "block.minecraft.brown_mushroom_block": "Bloc de bolet marró", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON> marró", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass_pane": "Panell de vidre tenyit de <PERSON>", "block.minecraft.brown_terracotta": "Terracota marró", "block.minecraft.brown_wool": "<PERSON><PERSON>", "block.minecraft.bubble_column": "Columna de bombolles", "block.minecraft.bubble_coral": "<PERSON><PERSON>", "block.minecraft.bubble_coral_block": "Bloc de corall de bombolla", "block.minecraft.bubble_coral_fan": "Gorgònia de bombolla", "block.minecraft.bubble_coral_wall_fan": "Gorgònia de bombolla a la paret", "block.minecraft.budding_amethyst": "Ametista cristal·litzable", "block.minecraft.bush": "<PERSON><PERSON>", "block.minecraft.cactus": "Cactus", "block.minecraft.cactus_flower": "Flor de Cactus", "block.minecraft.cake": "Pastís", "block.minecraft.calcite": "Calcita", "block.minecraft.calibrated_sculk_sensor": "Sensor Sculk calibrat", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>", "block.minecraft.candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "<PERSON><PERSON> amb espelma", "block.minecraft.carrots": "Pastana<PERSON>", "block.minecraft.cartography_table": "Taula de cartografia", "block.minecraft.carved_pumpkin": "Carbassa <PERSON>ada", "block.minecraft.cauldron": "<PERSON><PERSON>", "block.minecraft.cave_air": "Aire de cova", "block.minecraft.cave_vines": "Lianes de cova", "block.minecraft.cave_vines_plant": "Planta de lianes de cova", "block.minecraft.chain": "Cadena", "block.minecraft.chain_command_block": "Bloc de comandaments en cadena", "block.minecraft.cherry_button": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_door": "Porta de cirerer", "block.minecraft.cherry_fence": "Tanca de cirerer", "block.minecraft.cherry_fence_gate": "Porta de tanca de cirerer", "block.minecraft.cherry_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> c<PERSON>", "block.minecraft.cherry_leaves": "<PERSON>es de cirerer", "block.minecraft.cherry_log": "Tronc de cirerer", "block.minecraft.cherry_planks": "Fu<PERSON> c<PERSON>rer", "block.minecraft.cherry_pressure_plate": "Placa de pressió de cirerer", "block.minecraft.cherry_sapling": "Esqueix de cirerer", "block.minecraft.cherry_sign": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.cherry_slab": "<PERSON>los<PERSON> de c<PERSON>rer", "block.minecraft.cherry_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_trapdoor": "<PERSON>rap<PERSON> de cirerer", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> de cirerer amb suport", "block.minecraft.cherry_wall_sign": "Cartell de cirerer a la paret", "block.minecraft.cherry_wood": "Fu<PERSON> c<PERSON>rer", "block.minecraft.chest": "Cofre", "block.minecraft.chipped_anvil": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "block.minecraft.chiseled_bookshelf": "Prestatgeria", "block.minecraft.chiseled_copper": "Coure cisellat", "block.minecraft.chiseled_deepslate": "Pissarra cisellada", "block.minecraft.chiseled_nether_bricks": "Maons del Nether cisellats", "block.minecraft.chiseled_polished_blackstone": "Roca negra polida cisellada", "block.minecraft.chiseled_quartz_block": "Bloc de quars cisellat", "block.minecraft.chiseled_red_sandstone": "Roca sorrenca cisellada", "block.minecraft.chiseled_resin_bricks": "Maons de resina cisellats", "block.minecraft.chiseled_sandstone": "Roca sorrenca cisellada", "block.minecraft.chiseled_stone_bricks": "Maons de roca cisellats", "block.minecraft.chiseled_tuff": "<PERSON><PERSON> c<PERSON>", "block.minecraft.chiseled_tuff_bricks": "Maons de tuf cisellats", "block.minecraft.chorus_flower": "Flor de tornada", "block.minecraft.chorus_plant": "Planta de tornada", "block.minecraft.clay": "<PERSON><PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Iridàcia tancada", "block.minecraft.coal_block": "Bloc de carbó", "block.minecraft.coal_ore": "<PERSON><PERSON> <PERSON>", "block.minecraft.coarse_dirt": "Terra seca", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.cobbled_deepslate_slab": "<PERSON>los<PERSON> d'em<PERSON>", "block.minecraft.cobbled_deepslate_stairs": "<PERSON><PERSON><PERSON> d'emped<PERSON> de <PERSON>", "block.minecraft.cobbled_deepslate_wall": "<PERSON><PERSON> d'<PERSON><PERSON><PERSON> de <PERSON>", "block.minecraft.cobblestone": "Pedra", "block.minecraft.cobblestone_slab": "Llosa de pedra", "block.minecraft.cobblestone_stairs": "<PERSON><PERSON><PERSON> de pedra", "block.minecraft.cobblestone_wall": "<PERSON><PERSON> <PERSON>edra", "block.minecraft.cobweb": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cocoa": "Cacau", "block.minecraft.command_block": "Bloc de comandaments", "block.minecraft.comparator": "Comparador", "block.minecraft.composter": "Compostador", "block.minecraft.conduit": "Canalitzador", "block.minecraft.copper_block": "Bloc de coure", "block.minecraft.copper_bulb": "Bombeta de coure", "block.minecraft.copper_door": "Porta de coure", "block.minecraft.copper_grate": "Reixa de coure", "block.minecraft.copper_ore": "Mena de coure", "block.minecraft.copper_trapdoor": "Trapa de coure", "block.minecraft.cornflower": "Blavet", "block.minecraft.cracked_deepslate_bricks": "Maons de pissarra esquerdats", "block.minecraft.cracked_deepslate_tiles": "Raj<PERSON>s de pissarra esquerdades", "block.minecraft.cracked_nether_bricks": "Maons del Nether esquerdats", "block.minecraft.cracked_polished_blackstone_bricks": "Maons de roca negra polida esquerdats", "block.minecraft.cracked_stone_bricks": "Maons de roca esquerdats", "block.minecraft.crafter": "Elaborador", "block.minecraft.crafting_table": "<PERSON><PERSON>elabora<PERSON>", "block.minecraft.creaking_heart": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.creeper_head": "Cap de creeper", "block.minecraft.creeper_wall_head": "Cap de creeper a la paret", "block.minecraft.crimson_button": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_door": "Porta car<PERSON>ina", "block.minecraft.crimson_fence": "Tanca carmesina", "block.minecraft.crimson_fence_gate": "Porta de tanca carmesina", "block.minecraft.crimson_fungus": "<PERSON><PERSON> car<PERSON>", "block.minecraft.crimson_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON>", "block.minecraft.crimson_hyphae": "Hifes carmesines", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_planks": "<PERSON><PERSON> car<PERSON>", "block.minecraft.crimson_pressure_plate": "Placa de pressió carmesina", "block.minecraft.crimson_roots": "Arrels carmesines", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON> car<PERSON>", "block.minecraft.crimson_stairs": "<PERSON><PERSON><PERSON> carmesines", "block.minecraft.crimson_stem": "<PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON> car<PERSON>ina", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON><PERSON> penjant carmes<PERSON> amb suport", "block.minecraft.crimson_wall_sign": "<PERSON>tell car<PERSON> a la paret", "block.minecraft.crying_obsidian": "Obsidiana plorosa", "block.minecraft.cut_copper": "Coure tallat", "block.minecraft.cut_copper_slab": "Llosa de coure tallat", "block.minecraft.cut_copper_stairs": "Escales de coure tallat", "block.minecraft.cut_red_sandstone": "Roca sorrenca vermella tallada", "block.minecraft.cut_red_sandstone_slab": "Llosa de roca sorrenca vermella tallada", "block.minecraft.cut_sandstone": "Roca sorrenca tallada", "block.minecraft.cut_sandstone_slab": "Llosa de roca sorrenca tallada", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_bed": "<PERSON><PERSON> cian", "block.minecraft.cyan_candle": "<PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON> amb espelma cian", "block.minecraft.cyan_carpet": "<PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_concrete_powder": "Pols de formigó cian", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON> cian", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON> de cian", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON> de vidre tenyit de cian", "block.minecraft.cyan_terracotta": "Terracota cian", "block.minecraft.cyan_wool": "<PERSON><PERSON>", "block.minecraft.damaged_anvil": "En<PERSON>lusa molt danyada", "block.minecraft.dandelion": "<PERSON>t de ll<PERSON>", "block.minecraft.dark_oak_button": "Botó de pomer fosc", "block.minecraft.dark_oak_door": "Porta de pomer fosc", "block.minecraft.dark_oak_fence": "Tanca de pomer fosc", "block.minecraft.dark_oak_fence_gate": "Porta de tanca de pomer fosc", "block.minecraft.dark_oak_hanging_sign": "<PERSON><PERSON><PERSON> penjant de pomer fosc", "block.minecraft.dark_oak_leaves": "Fulles de pomer fosc", "block.minecraft.dark_oak_log": "Tronc de pomer fosc", "block.minecraft.dark_oak_planks": "Fusta de pomer fosc", "block.minecraft.dark_oak_pressure_plate": "Placa de pressió de pomer fosc", "block.minecraft.dark_oak_sapling": "Esqueix de pomer fosc", "block.minecraft.dark_oak_sign": "Cartell de pomer fosc", "block.minecraft.dark_oak_slab": "Llosa de pomer fosc", "block.minecraft.dark_oak_stairs": "Escales de pomer fosc", "block.minecraft.dark_oak_trapdoor": "Trapa de pomer fosc", "block.minecraft.dark_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> penjant de pomer fosc amb suport", "block.minecraft.dark_oak_wall_sign": "Cartell de pomer fosc a la paret", "block.minecraft.dark_oak_wood": "Fusta de pomer fosc", "block.minecraft.dark_prismarine": "Prismarina fosca", "block.minecraft.dark_prismarine_slab": "Llosa de prismarina fosca", "block.minecraft.dark_prismarine_stairs": "Escales de prismarina fosca", "block.minecraft.daylight_detector": "Sensor de llum solar", "block.minecraft.dead_brain_coral": "<PERSON><PERSON> de cervell mort", "block.minecraft.dead_brain_coral_block": "Bloc de corall de cervell mort", "block.minecraft.dead_brain_coral_fan": "Gorgònia de cervell morta", "block.minecraft.dead_brain_coral_wall_fan": "Gorgònia de cervell morta a la paret", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON> de bombolla mort", "block.minecraft.dead_bubble_coral_block": "Bloc de corall de bombolla mort", "block.minecraft.dead_bubble_coral_fan": "Gorgònia de bombolla morta", "block.minecraft.dead_bubble_coral_wall_fan": "Gorgònia de bombolla morta a la paret", "block.minecraft.dead_bush": "Arbust mort", "block.minecraft.dead_fire_coral": "<PERSON>l de foc mort", "block.minecraft.dead_fire_coral_block": "Bloc de corall de foc mort", "block.minecraft.dead_fire_coral_fan": "Gorgònia de foc morta", "block.minecraft.dead_fire_coral_wall_fan": "Gorgònia de foc morta a la paret", "block.minecraft.dead_horn_coral": "<PERSON><PERSON> de banya mort", "block.minecraft.dead_horn_coral_block": "Bloc de corall de banya mort", "block.minecraft.dead_horn_coral_fan": "Gorgònia de banya morta", "block.minecraft.dead_horn_coral_wall_fan": "Gorgònia de banya morta a la paret", "block.minecraft.dead_tube_coral": "Corall de tub mort", "block.minecraft.dead_tube_coral_block": "Bloc de corall de tub mort", "block.minecraft.dead_tube_coral_fan": "Gorgònia de tub morta", "block.minecraft.dead_tube_coral_wall_fan": "Gorgònia de tub morta a la paret", "block.minecraft.decorated_pot": "Gerro decorat", "block.minecraft.deepslate": "Pissarra", "block.minecraft.deepslate_brick_slab": "Llosa de <PERSON> de <PERSON>arra", "block.minecraft.deepslate_brick_stairs": "<PERSON><PERSON><PERSON> <PERSON> de <PERSON>", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON> <PERSON> <PERSON><PERSON> de <PERSON>", "block.minecraft.deepslate_bricks": "<PERSON><PERSON>", "block.minecraft.deepslate_coal_ore": "Mena de carbó en pissarra", "block.minecraft.deepslate_copper_ore": "Mena de coure en pissarra", "block.minecraft.deepslate_diamond_ore": "Mena de diamant en pissarra", "block.minecraft.deepslate_emerald_ore": "Mena de maragda en pissarra", "block.minecraft.deepslate_gold_ore": "Mena d'or en pissarra", "block.minecraft.deepslate_iron_ore": "Mena de ferro en pissarra", "block.minecraft.deepslate_lapis_ore": "Mena de lapislàtzuli en pissarra", "block.minecraft.deepslate_redstone_ore": "Mena de redstone en pissarra", "block.minecraft.deepslate_tile_slab": "Llosa de rajoles de <PERSON>arra", "block.minecraft.deepslate_tile_stairs": "<PERSON><PERSON><PERSON> de rajoles de <PERSON>", "block.minecraft.deepslate_tile_wall": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_tiles": "<PERSON><PERSON><PERSON>", "block.minecraft.detector_rail": "Rail detector", "block.minecraft.diamond_block": "Bloc de diamant", "block.minecraft.diamond_ore": "<PERSON><PERSON> <PERSON>", "block.minecraft.diorite": "Di<PERSON>ta", "block.minecraft.diorite_slab": "Llosa de diorita", "block.minecraft.diorite_stairs": "Escales de diorita", "block.minecraft.diorite_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.dirt": "Terra", "block.minecraft.dirt_path": "Cam<PERSON> de <PERSON>rra", "block.minecraft.dispenser": "Dispensador", "block.minecraft.dragon_egg": "<PERSON><PERSON> de drac", "block.minecraft.dragon_head": "Cap de drac", "block.minecraft.dragon_wall_head": "Cap de drac a la paret", "block.minecraft.dried_ghast": "Ghast Sec", "block.minecraft.dried_kelp_block": "Bloc d'algues seques", "block.minecraft.dripstone_block": "Bloc d'espeleotema", "block.minecraft.dropper": "Subministrador", "block.minecraft.emerald_block": "Bloc de maragda", "block.minecraft.emerald_ore": "Mena de <PERSON>", "block.minecraft.enchanting_table": "<PERSON><PERSON>'en<PERSON>", "block.minecraft.end_gateway": "Entrada de l'End", "block.minecraft.end_portal": "Portal de l'End", "block.minecraft.end_portal_frame": "Marc del portal de l'End", "block.minecraft.end_rod": "Vara de l'End", "block.minecraft.end_stone": "Roca de l'End", "block.minecraft.end_stone_brick_slab": "Llosa de maons de roca de l'End", "block.minecraft.end_stone_brick_stairs": "<PERSON><PERSON><PERSON> de maons de roca de l'End", "block.minecraft.end_stone_brick_wall": "<PERSON>r de maons de roca de l'End", "block.minecraft.end_stone_bricks": "Maons de roca de l'End", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Coure exposat cisellat", "block.minecraft.exposed_copper": "Coure exposat", "block.minecraft.exposed_copper_bulb": "Bombeta de coure exposat", "block.minecraft.exposed_copper_door": "Porta de coure exposat", "block.minecraft.exposed_copper_grate": "Reixa de coure exposat", "block.minecraft.exposed_copper_trapdoor": "Trapa de coure exposat", "block.minecraft.exposed_cut_copper": "Coure tallat exposat", "block.minecraft.exposed_cut_copper_slab": "Llosa de coure tallat exposada", "block.minecraft.exposed_cut_copper_stairs": "Escales de coure tallat exposades", "block.minecraft.farmland": "Terra de cultiu", "block.minecraft.fern": "Falguera", "block.minecraft.fire": "Foc", "block.minecraft.fire_coral": "Corall de foc", "block.minecraft.fire_coral_block": "Bloc de corall de foc", "block.minecraft.fire_coral_fan": "Gorgònia de foc", "block.minecraft.fire_coral_wall_fan": "Gorgònia de foc a la paret", "block.minecraft.firefly_bush": "Matoll de cuques de llum", "block.minecraft.fletching_table": "<PERSON><PERSON> flet<PERSON>", "block.minecraft.flower_pot": "Test", "block.minecraft.flowering_azalea": "Azalea florida", "block.minecraft.flowering_azalea_leaves": "Fulles d'azalea florida", "block.minecraft.frogspawn": "Ous de granota", "block.minecraft.frosted_ice": "<PERSON><PERSON>", "block.minecraft.furnace": "Forn", "block.minecraft.gilded_blackstone": "Roca negra lluent", "block.minecraft.glass": "<PERSON><PERSON><PERSON>", "block.minecraft.glass_pane": "Panell de vidre", "block.minecraft.glow_lichen": "Liquen lluent", "block.minecraft.glowstone": "Roca brillant", "block.minecraft.gold_block": "Bloc d'or", "block.minecraft.gold_ore": "<PERSON>a d'or", "block.minecraft.granite": "Granit", "block.minecraft.granite_slab": "Llosa de g<PERSON>", "block.minecraft.granite_stairs": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.granite_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.grass": "<PERSON><PERSON>", "block.minecraft.grass_block": "Bloc d'herba", "block.minecraft.gravel": "Grava", "block.minecraft.gray_banner": "Estendard gris", "block.minecraft.gray_bed": "<PERSON><PERSON> gris", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON><PERSON> grisa", "block.minecraft.gray_candle_cake": "Pastís amb espelma grisa", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON> grisa", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON> gris", "block.minecraft.gray_concrete_powder": "Pols de formigó gris", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON> gris", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON> grisa", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON> tenyi<PERSON> de gris", "block.minecraft.gray_stained_glass_pane": "<PERSON>l de vidre tenyit de gris", "block.minecraft.gray_terracotta": "Terracota grisa", "block.minecraft.gray_wool": "<PERSON><PERSON>", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON><PERSON> de sinople", "block.minecraft.green_bed": "Llit verd", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON><PERSON> verda", "block.minecraft.green_candle_cake": "Pastís amb espelma verda", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON> verda", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON> verd", "block.minecraft.green_concrete_powder": "Pols de formigó verd", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON> verda", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON> de shulker verda", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON> tenyi<PERSON> de verd", "block.minecraft.green_stained_glass_pane": "<PERSON>l de vidre tenyit de verd", "block.minecraft.green_terracotta": "Terracota verda", "block.minecraft.green_wool": "<PERSON><PERSON> ve<PERSON>", "block.minecraft.grindstone": "A<PERSON>lad<PERSON>", "block.minecraft.hanging_roots": "Arrels penjants", "block.minecraft.hay_block": "Bala de fenc", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON> dens", "block.minecraft.heavy_weighted_pressure_plate": "Placa de pressió per a pes elevat", "block.minecraft.honey_block": "Bloc de mel", "block.minecraft.honeycomb_block": "Bloc de bresca", "block.minecraft.hopper": "Tremuja", "block.minecraft.horn_coral": "<PERSON><PERSON> de ban<PERSON>", "block.minecraft.horn_coral_block": "Bloc de corall de banya", "block.minecraft.horn_coral_fan": "Gorgònia de banya", "block.minecraft.horn_coral_wall_fan": "Gorgònia de banya a la paret", "block.minecraft.ice": "<PERSON>el", "block.minecraft.infested_chiseled_stone_bricks": "Maons de roca cisellats infestats", "block.minecraft.infested_cobblestone": "Pedra infestada", "block.minecraft.infested_cracked_stone_bricks": "Maons de roca esquerdats infestats", "block.minecraft.infested_deepslate": "Pissarra infestada", "block.minecraft.infested_mossy_stone_bricks": "Maons de roca amb molsa infestats", "block.minecraft.infested_stone": "Roca infestada", "block.minecraft.infested_stone_bricks": "Maons de roca infestats", "block.minecraft.iron_bars": "Barrots de ferro", "block.minecraft.iron_block": "Bloc de ferro", "block.minecraft.iron_door": "Porta de ferro", "block.minecraft.iron_ore": "Mena de ferro", "block.minecraft.iron_trapdoor": "Trapa de ferro", "block.minecraft.jack_o_lantern": "Carbassa de Halloween", "block.minecraft.jigsaw": "Bloc trencaclosques", "block.minecraft.jukebox": "Tocadiscs", "block.minecraft.jungle_button": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_door": "Porta de j<PERSON>la", "block.minecraft.jungle_fence": "Tanca de j<PERSON>la", "block.minecraft.jungle_fence_gate": "Porta de tanca de jungla", "block.minecraft.jungle_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON>", "block.minecraft.jungle_leaves": "<PERSON><PERSON> de jungla", "block.minecraft.jungle_log": "Tronc de jungla", "block.minecraft.jungle_planks": "<PERSON><PERSON>", "block.minecraft.jungle_pressure_plate": "Placa de pressió de jungla", "block.minecraft.jungle_sapling": "Esqueix de j<PERSON>la", "block.minecraft.jungle_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_wall_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> de jungla amb suport", "block.minecraft.jungle_wall_sign": "Cartell de jungla a la paret", "block.minecraft.jungle_wood": "<PERSON><PERSON>", "block.minecraft.kelp": "Alga", "block.minecraft.kelp_plant": "<PERSON><PERSON>'alga", "block.minecraft.ladder": "Escala", "block.minecraft.lantern": "Fanal", "block.minecraft.lapis_block": "Bloc de lapislàtzuli", "block.minecraft.lapis_ore": "Mena de <PERSON>", "block.minecraft.large_amethyst_bud": "Cristall d'ametista gran", "block.minecraft.large_fern": "Falguera gran", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Calderó amb lava", "block.minecraft.leaf_litter": "Fulles seques", "block.minecraft.lectern": "Faristol", "block.minecraft.lever": "Palanca", "block.minecraft.light": "<PERSON><PERSON>", "block.minecraft.light_blue_banner": "Estendard blau cel", "block.minecraft.light_blue_bed": "<PERSON><PERSON> blau cel", "block.minecraft.light_blue_candle": "E<PERSON>el<PERSON> blau cel", "block.minecraft.light_blue_candle_cake": "Pastís amb espelma blau cel", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON> blau cel", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON> blau cel", "block.minecraft.light_blue_concrete_powder": "Pols de formigó blau cel", "block.minecraft.light_blue_glazed_terracotta": "<PERSON><PERSON> blau cel", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON>ker blau cel", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON> tenyit de blau cel", "block.minecraft.light_blue_stained_glass_pane": "<PERSON>l de vidre tenyit de blau cel", "block.minecraft.light_blue_terracotta": "Terracota blau cel", "block.minecraft.light_blue_wool": "<PERSON><PERSON> blau cel", "block.minecraft.light_gray_banner": "Estendard d'argent", "block.minecraft.light_gray_bed": "Llit gris clar", "block.minecraft.light_gray_candle": "Espelma gris clar", "block.minecraft.light_gray_candle_cake": "Pastís amb espelma gris clar", "block.minecraft.light_gray_carpet": "<PERSON><PERSON>a gris clar", "block.minecraft.light_gray_concrete": "Formigó gris clar", "block.minecraft.light_gray_concrete_powder": "Pols de formigó gris clar", "block.minecraft.light_gray_glazed_terracotta": "<PERSON><PERSON> gris clar", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON> de shulker gris clar", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON> tenyit de gris clar", "block.minecraft.light_gray_stained_glass_pane": "<PERSON>l de vidre tenyit de gris clar", "block.minecraft.light_gray_terracotta": "Terracota gris clar", "block.minecraft.light_gray_wool": "<PERSON><PERSON> gris clar", "block.minecraft.light_weighted_pressure_plate": "Placa de pressió per a pes lleuger", "block.minecraft.lightning_rod": "Parallamps", "block.minecraft.lilac": "Lilà", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON>", "block.minecraft.lily_pad": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_bed": "<PERSON><PERSON> llima", "block.minecraft.lime_candle": "<PERSON><PERSON><PERSON><PERSON> llima", "block.minecraft.lime_candle_cake": "Pastís amb espelma llima", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON> llima", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON> ll<PERSON>", "block.minecraft.lime_concrete_powder": "Pols de formigó llima", "block.minecraft.lime_glazed_terracotta": "<PERSON><PERSON>", "block.minecraft.lime_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON>ker llima", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON> ten<PERSON> de llima", "block.minecraft.lime_stained_glass_pane": "Panell de vidre tenyit de llima", "block.minecraft.lime_terracotta": "Terracota verd llima", "block.minecraft.lime_wool": "<PERSON><PERSON>", "block.minecraft.lodestone": "Fita", "block.minecraft.loom": "Teler", "block.minecraft.magenta_banner": "Estendard magenta", "block.minecraft.magenta_bed": "Llit magenta", "block.minecraft.magenta_candle": "Espelma magenta", "block.minecraft.magenta_candle_cake": "Pastís amb espelma magenta", "block.minecraft.magenta_carpet": "Catifa magenta", "block.minecraft.magenta_concrete": "Formigó magenta", "block.minecraft.magenta_concrete_powder": "Pols de formigó magenta", "block.minecraft.magenta_glazed_terracotta": "<PERSON>ola magenta", "block.minecraft.magenta_shulker_box": "Caixa de shulker magenta", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON><PERSON> tenyi<PERSON> de magenta", "block.minecraft.magenta_stained_glass_pane": "Panell de vidre tenyit de magenta", "block.minecraft.magenta_terracotta": "Terracota magenta", "block.minecraft.magenta_wool": "<PERSON><PERSON> magenta", "block.minecraft.magma_block": "Bloc de magma", "block.minecraft.mangrove_button": "<PERSON><PERSON><PERSON> mangle", "block.minecraft.mangrove_door": "Porta de mangle", "block.minecraft.mangrove_fence": "Tanca de mangle", "block.minecraft.mangrove_fence_gate": "Porta de tanca de mangle", "block.minecraft.mangrove_hanging_sign": "<PERSON><PERSON><PERSON> penjant de mangle", "block.minecraft.mangrove_leaves": "Fulles de mangle", "block.minecraft.mangrove_log": "Tronc de mangle", "block.minecraft.mangrove_planks": "Fusta de mangle", "block.minecraft.mangrove_pressure_plate": "Placa de pressió de mangle", "block.minecraft.mangrove_propagule": "Propàgul de mangle", "block.minecraft.mangrove_roots": "Arrels de mangle", "block.minecraft.mangrove_sign": "Cartell de mangle", "block.minecraft.mangrove_slab": "<PERSON>los<PERSON> de mangle", "block.minecraft.mangrove_stairs": "<PERSON><PERSON><PERSON> de mangle", "block.minecraft.mangrove_trapdoor": "Trapa de mangle", "block.minecraft.mangrove_wall_hanging_sign": "<PERSON><PERSON><PERSON> penjant de mangle amb suport", "block.minecraft.mangrove_wall_sign": "Cartell de mangle a la paret", "block.minecraft.mangrove_wood": "Fusta de mangle", "block.minecraft.medium_amethyst_bud": "Cristall d'ametista mitjà", "block.minecraft.melon": "Síndria", "block.minecraft.melon_stem": "<PERSON><PERSON> de <PERSON>dr<PERSON>", "block.minecraft.moss_block": "Bloc de molsa", "block.minecraft.moss_carpet": "<PERSON><PERSON>a de molsa", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON> amb molsa", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON><PERSON> de pedra amb molsa", "block.minecraft.mossy_cobblestone_stairs": "<PERSON><PERSON><PERSON> de pedra amb molsa", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> de pedra amb molsa", "block.minecraft.mossy_stone_brick_slab": "<PERSON><PERSON><PERSON> de maons de roca amb molsa", "block.minecraft.mossy_stone_brick_stairs": "<PERSON><PERSON><PERSON> de maons de roca amb molsa", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON> de maons de roca amb molsa", "block.minecraft.mossy_stone_bricks": "Maons de roca amb molsa", "block.minecraft.moving_piston": "Pistó en moviment", "block.minecraft.mud": "<PERSON>", "block.minecraft.mud_brick_slab": "Llosa de to<PERSON>", "block.minecraft.mud_brick_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.mud_brick_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.mud_bricks": "<PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "Arrels de mangle enfangades", "block.minecraft.mushroom_stem": "<PERSON><PERSON> <PERSON> bole<PERSON>", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Tanca de maó del Nether", "block.minecraft.nether_brick_slab": "Llosa de maons del Nether", "block.minecraft.nether_brick_stairs": "<PERSON><PERSON><PERSON> de ma<PERSON> del Nether", "block.minecraft.nether_brick_wall": "<PERSON><PERSON> <PERSON> del Nether", "block.minecraft.nether_bricks": "<PERSON><PERSON> del <PERSON>her", "block.minecraft.nether_gold_ore": "<PERSON><PERSON> <PERSON>'or <PERSON>", "block.minecraft.nether_portal": "Portal del Nether", "block.minecraft.nether_quartz_ore": "Mena de quars del Nether", "block.minecraft.nether_sprouts": "Brots del Nether", "block.minecraft.nether_wart": "Berruga del Nether", "block.minecraft.nether_wart_block": "Bloc de berrugues del Nether", "block.minecraft.netherite_block": "Bloc de netherita", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Bloc musical", "block.minecraft.oak_button": "Botó de pomer", "block.minecraft.oak_door": "Porta de pomer", "block.minecraft.oak_fence": "Tanca de pomer", "block.minecraft.oak_fence_gate": "Porta de tanca de pomer", "block.minecraft.oak_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> de pomer", "block.minecraft.oak_leaves": "Fulles de pomer", "block.minecraft.oak_log": "Tronc de pomer", "block.minecraft.oak_planks": "Fusta de pomer", "block.minecraft.oak_pressure_plate": "Placa de pressió de pomer", "block.minecraft.oak_sapling": "Esqueix de pomer", "block.minecraft.oak_sign": "Cartell de pomer", "block.minecraft.oak_slab": "Llosa de pomer", "block.minecraft.oak_stairs": "Escales de pomer", "block.minecraft.oak_trapdoor": "Trapa de pomer", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> penjant de pomer amb suport", "block.minecraft.oak_wall_sign": "Cartell de pomer a la paret", "block.minecraft.oak_wood": "Fusta de pomer", "block.minecraft.observer": "Observador", "block.minecraft.obsidian": "Obsidiana", "block.minecraft.ochre_froglight": "Granollum ocre", "block.minecraft.ominous_banner": "<PERSON>stendar<PERSON> o<PERSON>", "block.minecraft.open_eyeblossom": "Iridàcia oberta", "block.minecraft.orange_banner": "Estendard ataronjat", "block.minecraft.orange_bed": "<PERSON><PERSON>", "block.minecraft.orange_candle": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>ja", "block.minecraft.orange_candle_cake": "Pastís amb espelma taronja", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete": "<PERSON>ig<PERSON>", "block.minecraft.orange_concrete_powder": "Pols de formigó taronja", "block.minecraft.orange_glazed_terracotta": "<PERSON><PERSON>", "block.minecraft.orange_shulker_box": "<PERSON><PERSON><PERSON> de shulker taronja", "block.minecraft.orange_stained_glass": "<PERSON><PERSON><PERSON> de <PERSON>", "block.minecraft.orange_stained_glass_pane": "Panell de vidre tenyit de ta<PERSON>ja", "block.minecraft.orange_terracotta": "Terracota taronja", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_wool": "<PERSON><PERSON>", "block.minecraft.oxeye_daisy": "Margarida", "block.minecraft.oxidized_chiseled_copper": "Coure oxidat cisellat", "block.minecraft.oxidized_copper": "Coure oxidat", "block.minecraft.oxidized_copper_bulb": "Bombeta de coure oxidat", "block.minecraft.oxidized_copper_door": "Porta de coure oxidat", "block.minecraft.oxidized_copper_grate": "Reixa de coure oxidat", "block.minecraft.oxidized_copper_trapdoor": "Trapa de coure oxidat", "block.minecraft.oxidized_cut_copper": "Coure tallat oxidat", "block.minecraft.oxidized_cut_copper_slab": "Llosa de coure tallat oxidada", "block.minecraft.oxidized_cut_copper_stairs": "Escales de coure tallat oxidades", "block.minecraft.packed_ice": "Gel compactat", "block.minecraft.packed_mud": "Fang compactat", "block.minecraft.pale_hanging_moss": "Molsa pàl·lida penjant", "block.minecraft.pale_moss_block": "Bloc de molsa pàl·lida", "block.minecraft.pale_moss_carpet": "Catifa de molsa pàl·lida", "block.minecraft.pale_oak_button": "Botó de pomer pàl·lid", "block.minecraft.pale_oak_door": "Porta de pomer pàl·lid", "block.minecraft.pale_oak_fence": "Tanca de pomer pàl·lid", "block.minecraft.pale_oak_fence_gate": "Porta de tanca de pomer pàl·lid", "block.minecraft.pale_oak_hanging_sign": "Rètol de paret de pomer pàl·lid", "block.minecraft.pale_oak_leaves": "Fulles de pomer pàl·lid", "block.minecraft.pale_oak_log": "Tronc de pomer pàl·lid", "block.minecraft.pale_oak_planks": "Taulons de fusta de pomer pàl·lid", "block.minecraft.pale_oak_pressure_plate": "Placa de pressió de pomer pàl·lid", "block.minecraft.pale_oak_sapling": "Esqueix de pomer pàl·lid", "block.minecraft.pale_oak_sign": "Rètol de pomer pàl·lid", "block.minecraft.pale_oak_slab": "Llosa de pomer pàl·lid", "block.minecraft.pale_oak_stairs": "<PERSON><PERSON><PERSON> de roure pal li", "block.minecraft.pale_oak_trapdoor": "<PERSON>rap<PERSON> de roure pal li", "block.minecraft.pale_oak_wall_hanging_sign": "Cartell penjant de pomer pàl·lid amb suport", "block.minecraft.pale_oak_wall_sign": "Cartell de pomer pàl·lid", "block.minecraft.pale_oak_wood": "Fusta de roure pàl·lid", "block.minecraft.pearlescent_froglight": "Granollum nacrada", "block.minecraft.peony": "Peònia", "block.minecraft.petrified_oak_slab": "Llosa de pomer petrificada", "block.minecraft.piglin_head": "Cap de piglin", "block.minecraft.piglin_wall_head": "Cap de piglin a la paret", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_bed": "<PERSON><PERSON> rosa", "block.minecraft.pink_candle": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_candle_cake": "Pastís amb espelma rosa", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_concrete": "Formig<PERSON> rosa", "block.minecraft.pink_concrete_powder": "Pols de formigó rosa", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON> rosa", "block.minecraft.pink_petals": "Pè<PERSON> rosa", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON>ker rosa", "block.minecraft.pink_stained_glass": "<PERSON><PERSON><PERSON> tenyi<PERSON> de rosa", "block.minecraft.pink_stained_glass_pane": "Panell de vidre tenyit de rosa", "block.minecraft.pink_terracotta": "Terracota rosa", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_wool": "<PERSON><PERSON> rosa", "block.minecraft.piston": "<PERSON><PERSON><PERSON>", "block.minecraft.piston_head": "Cap de pistó", "block.minecraft.pitcher_crop": "Cultiu de planta gerro", "block.minecraft.pitcher_plant": "<PERSON><PERSON> gerro", "block.minecraft.player_head": "Cap de jugador", "block.minecraft.player_head.named": "Cap de %s", "block.minecraft.player_wall_head": "Cap de jugador a la paret", "block.minecraft.podzol": "<PERSON><PERSON><PERSON>", "block.minecraft.pointed_dripstone": "Espeleotema punxegut", "block.minecraft.polished_andesite": "<PERSON><PERSON> polida", "block.minecraft.polished_andesite_slab": "Llosa d'andesita polida", "block.minecraft.polished_andesite_stairs": "Escales d'andesita polida", "block.minecraft.polished_basalt": "Basalt polit", "block.minecraft.polished_blackstone": "Roca negra polida", "block.minecraft.polished_blackstone_brick_slab": "Llosa de maons de roca negra polida", "block.minecraft.polished_blackstone_brick_stairs": "<PERSON><PERSON><PERSON> de maons de roca negra polida", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON> de maons de roca negra polida", "block.minecraft.polished_blackstone_bricks": "Maons de roca negra polida", "block.minecraft.polished_blackstone_button": "Botó de roca negra polida", "block.minecraft.polished_blackstone_pressure_plate": "Placa de pressió de roca negra polida", "block.minecraft.polished_blackstone_slab": "Llosa de roca negra polida", "block.minecraft.polished_blackstone_stairs": "Escales de roca negra polida", "block.minecraft.polished_blackstone_wall": "Mur de roca negra polida", "block.minecraft.polished_deepslate": "Pissarra polida", "block.minecraft.polished_deepslate_slab": "Llosa de pissarra polida", "block.minecraft.polished_deepslate_stairs": "<PERSON><PERSON><PERSON> de pissarra polida", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON> <PERSON> polida", "block.minecraft.polished_diorite": "<PERSON><PERSON>ta polida", "block.minecraft.polished_diorite_slab": "Llosa de diorita polida", "block.minecraft.polished_diorite_stairs": "Escales de diorita polida", "block.minecraft.polished_granite": "Granit polit", "block.minecraft.polished_granite_slab": "Llosa de granit polit", "block.minecraft.polished_granite_stairs": "Escales de granit polit", "block.minecraft.polished_tuff": "<PERSON><PERSON> polit", "block.minecraft.polished_tuff_slab": "Llosa de tuf polit", "block.minecraft.polished_tuff_stairs": "<PERSON><PERSON><PERSON> de tuf polit", "block.minecraft.polished_tuff_wall": "<PERSON>r de tuf polit", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "Patates", "block.minecraft.potted_acacia_sapling": "Test amb esqueix d'acàcia", "block.minecraft.potted_allium": "Test amb allium", "block.minecraft.potted_azalea_bush": "Test amb azalea", "block.minecraft.potted_azure_bluet": "Test amb azure bluet", "block.minecraft.potted_bamboo": "Test amb bambú", "block.minecraft.potted_birch_sapling": "Test amb esqueix de bedoll", "block.minecraft.potted_blue_orchid": "Test amb orq<PERSON><PERSON><PERSON> blava", "block.minecraft.potted_brown_mushroom": "Test amb bolet marró", "block.minecraft.potted_cactus": "Test amb cactus", "block.minecraft.potted_cherry_sapling": "Test amb esqueix de cirerer", "block.minecraft.potted_closed_eyeblossom": "Test amb iridàcia tancada", "block.minecraft.potted_cornflower": "Test amb blavet", "block.minecraft.potted_crimson_fungus": "Test amb fong carmesí", "block.minecraft.potted_crimson_roots": "Test amb arrels carmesines", "block.minecraft.potted_dandelion": "Test amb dent de lleó", "block.minecraft.potted_dark_oak_sapling": "Test amb esqueix de pomer fosc", "block.minecraft.potted_dead_bush": "Test amb arbust sec", "block.minecraft.potted_fern": "Test amb falguera", "block.minecraft.potted_flowering_azalea_bush": "Test amb azalea florida", "block.minecraft.potted_jungle_sapling": "Test amb esqueix de jungla", "block.minecraft.potted_lily_of_the_valley": "Test amb lliri de maig", "block.minecraft.potted_mangrove_propagule": "Test amb propàgul de mangle", "block.minecraft.potted_oak_sapling": "Test amb esqueix de pomer", "block.minecraft.potted_open_eyeblossom": "Test amb iridàcia oberta", "block.minecraft.potted_orange_tulip": "Test amb tulipà taronja", "block.minecraft.potted_oxeye_daisy": "Test amb margarida", "block.minecraft.potted_pale_oak_sapling": "Test amb esqueix de pomer pàl·lid", "block.minecraft.potted_pink_tulip": "Test amb tulipà rosa", "block.minecraft.potted_poppy": "Test amb rosella", "block.minecraft.potted_red_mushroom": "Test amb bolet vermell", "block.minecraft.potted_red_tulip": "Test amb tulipà vermell", "block.minecraft.potted_spruce_sapling": "Test amb esqueix de pi", "block.minecraft.potted_torchflower": "Test amb florxa", "block.minecraft.potted_warped_fungus": "Test amb fong guerxo", "block.minecraft.potted_warped_roots": "Test amb arrels guerxes", "block.minecraft.potted_white_tulip": "Test amb tulipà blanc", "block.minecraft.potted_wither_rose": "Test amb rosa de wither", "block.minecraft.powder_snow": "Neu pols", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON> amb neu pols", "block.minecraft.powered_rail": "Rail propulsor", "block.minecraft.prismarine": "Prismarina", "block.minecraft.prismarine_brick_slab": "Llosa de maons de prismarina", "block.minecraft.prismarine_brick_stairs": "<PERSON><PERSON><PERSON> de maons de prismarina", "block.minecraft.prismarine_bricks": "Maons de prismarina", "block.minecraft.prismarine_slab": "Llosa de prismarina", "block.minecraft.prismarine_stairs": "Escales de prismarina", "block.minecraft.prismarine_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.pumpkin": "Carbassa", "block.minecraft.pumpkin_stem": "<PERSON><PERSON> de <PERSON>", "block.minecraft.purple_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_bed": "Llit lila", "block.minecraft.purple_candle": "Espelma lila", "block.minecraft.purple_candle_cake": "Pastís amb espelma lila", "block.minecraft.purple_carpet": "Catifa lila", "block.minecraft.purple_concrete": "<PERSON><PERSON><PERSON> morat", "block.minecraft.purple_concrete_powder": "Pols de formigó morat", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON> morada", "block.minecraft.purple_shulker_box": "Caixa de shulker lila", "block.minecraft.purple_stained_glass": "<PERSON><PERSON><PERSON> tenyit de lila", "block.minecraft.purple_stained_glass_pane": "Panell de vidre tenyit de lila", "block.minecraft.purple_terracotta": "Terracota morada", "block.minecraft.purple_wool": "<PERSON><PERSON> lila", "block.minecraft.purpur_block": "Bloc de purpur", "block.minecraft.purpur_pillar": "<PERSON><PERSON> de purpur", "block.minecraft.purpur_slab": "Llosa de purpur", "block.minecraft.purpur_stairs": "<PERSON><PERSON><PERSON> de purpur", "block.minecraft.quartz_block": "Bloc de quars", "block.minecraft.quartz_bricks": "<PERSON>ns de quars", "block.minecraft.quartz_pillar": "<PERSON><PERSON> de quars", "block.minecraft.quartz_slab": "<PERSON><PERSON><PERSON> de quars", "block.minecraft.quartz_stairs": "<PERSON><PERSON><PERSON> de quars", "block.minecraft.rail": "Rail", "block.minecraft.raw_copper_block": "Bloc de coure en brut", "block.minecraft.raw_gold_block": "Bloc d'or en brut", "block.minecraft.raw_iron_block": "Bloc de ferro en brut", "block.minecraft.red_banner": "Estendard de gules", "block.minecraft.red_bed": "<PERSON><PERSON> vermell", "block.minecraft.red_candle": "Espel<PERSON> vermella", "block.minecraft.red_candle_cake": "Pastís amb espelma vermella", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON> vermella", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON> ve<PERSON>", "block.minecraft.red_concrete_powder": "Pols de formigó vermell", "block.minecraft.red_glazed_terracotta": "<PERSON><PERSON> vermella", "block.minecraft.red_mushroom": "<PERSON><PERSON> vermell", "block.minecraft.red_mushroom_block": "<PERSON> de bolet vermell", "block.minecraft.red_nether_brick_slab": "Llosa de ma<PERSON> del Nether vermells", "block.minecraft.red_nether_brick_stairs": "<PERSON><PERSON><PERSON> de ma<PERSON> del Nether vermells", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON> <PERSON> ma<PERSON> del Nether vermells", "block.minecraft.red_nether_bricks": "Maons del Nether vermells", "block.minecraft.red_sand": "Sorra vermella", "block.minecraft.red_sandstone": "Roca sorrenca vermella", "block.minecraft.red_sandstone_slab": "Llosa de roca sorrenca vermella", "block.minecraft.red_sandstone_stairs": "Escales de roca sorrenca vermella", "block.minecraft.red_sandstone_wall": "Mur de roca sorrenca vermella", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON> vermella", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON> ten<PERSON> de ve<PERSON>", "block.minecraft.red_stained_glass_pane": "<PERSON>l de vidre tenyit de vermell", "block.minecraft.red_terracotta": "Terracota vermella", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> vermella", "block.minecraft.red_wool": "<PERSON><PERSON> vermella", "block.minecraft.redstone_block": "Bloc de redstone", "block.minecraft.redstone_lamp": "Làmpada de redstone", "block.minecraft.redstone_ore": "Mena de redstone", "block.minecraft.redstone_torch": "Torxa de redstone", "block.minecraft.redstone_wall_torch": "Torxa de redstone a la paret", "block.minecraft.redstone_wire": "Cable de redstone", "block.minecraft.reinforced_deepslate": "Pissarra reforçada", "block.minecraft.repeater": "Repetidor", "block.minecraft.repeating_command_block": "Bloc de comandaments repetitiu", "block.minecraft.resin_block": "Bloc de resina", "block.minecraft.resin_brick_slab": "Llosa de ma<PERSON> de resina", "block.minecraft.resin_brick_stairs": "<PERSON><PERSON><PERSON> de ma<PERSON> de resina", "block.minecraft.resin_brick_wall": "<PERSON><PERSON> <PERSON> ma<PERSON> de resina", "block.minecraft.resin_bricks": "Maons de resina", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON> de resina", "block.minecraft.respawn_anchor": "Àncora de reaparició", "block.minecraft.rooted_dirt": "Terra amb arrels", "block.minecraft.rose_bush": "<PERSON><PERSON>", "block.minecraft.sand": "Sorra", "block.minecraft.sandstone": "Roca sorrenca", "block.minecraft.sandstone_slab": "Llosa de roca sorrenca", "block.minecraft.sandstone_stairs": "Escales de roca sorrenca", "block.minecraft.sandstone_wall": "Mur de roca sorrenca", "block.minecraft.scaffolding": "Bastida", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk catalitzador", "block.minecraft.sculk_sensor": "Sensor sculk", "block.minecraft.sculk_shrieker": "Sculk xisclador", "block.minecraft.sculk_vein": "<PERSON><PERSON><PERSON> d'sculk", "block.minecraft.sea_lantern": "Llanterna marina", "block.minecraft.sea_pickle": "Cogombre de mar", "block.minecraft.seagrass": "Herba marina", "block.minecraft.set_spawn": "Punt de reaparició establert", "block.minecraft.short_dry_grass": "Herba seca baixa", "block.minecraft.short_grass": "Herba curta", "block.minecraft.shroomlight": "Girgolàmpada", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "Calavera d'esquelet", "block.minecraft.skeleton_wall_skull": "Calavera d'esquelet a la paret", "block.minecraft.slime_block": "<PERSON> de llim", "block.minecraft.small_amethyst_bud": "<PERSON><PERSON><PERSON> d'ametista petit", "block.minecraft.small_dripleaf": "Degotadora petita", "block.minecraft.smithing_table": "<PERSON><PERSON>er<PERSON>", "block.minecraft.smoker": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Basalt llis", "block.minecraft.smooth_quartz": "Bloc de quars llis", "block.minecraft.smooth_quartz_slab": "<PERSON><PERSON><PERSON> de quars llis", "block.minecraft.smooth_quartz_stairs": "<PERSON><PERSON><PERSON> de quars llis", "block.minecraft.smooth_red_sandstone": "Roca sorrenca llisa", "block.minecraft.smooth_red_sandstone_slab": "Llosa de roca sorrenca vermella llisa", "block.minecraft.smooth_red_sandstone_stairs": "Escales de roca sorrenca vermella llisa", "block.minecraft.smooth_sandstone": "Roca sorrenca llisa", "block.minecraft.smooth_sandstone_slab": "Llosa de roca sorrenca llisa", "block.minecraft.smooth_sandstone_stairs": "Escales de roca sorrenca llisa", "block.minecraft.smooth_stone": "Roca llisa", "block.minecraft.smooth_stone_slab": "Llosa de roca llisa", "block.minecraft.sniffer_egg": "<PERSON><PERSON> d'ensu<PERSON><PERSON>", "block.minecraft.snow": "<PERSON>eu", "block.minecraft.snow_block": "Bloc de neu", "block.minecraft.soul_campfire": "Foguera d'ànimes", "block.minecraft.soul_fire": "Foc d'ànimes", "block.minecraft.soul_lantern": "Llanterna d'ànimes", "block.minecraft.soul_sand": "Sorra d'à<PERSON>", "block.minecraft.soul_soil": "Sòl d'ànimes", "block.minecraft.soul_torch": "Torxa d'ànimes", "block.minecraft.soul_wall_torch": "Torxa d'ànimes a la paret", "block.minecraft.spawn.not_valid": "No tens llit o una àncora de reaparició carregada, o ha estat obstruït/da", "block.minecraft.spawner": "Generador de monstres", "block.minecraft.spawner.desc1": "Al fer-hi clic amb un ou generador:", "block.minecraft.spawner.desc2": "Estableix el tipus d'entitat", "block.minecraft.sponge": "Esponja", "block.minecraft.spore_blossom": "Flor d'espores", "block.minecraft.spruce_button": "Botó de pi", "block.minecraft.spruce_door": "Porta de pi", "block.minecraft.spruce_fence": "Tanca de pi", "block.minecraft.spruce_fence_gate": "Porta de tanca de pi", "block.minecraft.spruce_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> de pi", "block.minecraft.spruce_leaves": "Fulles de pi", "block.minecraft.spruce_log": "Tronc de pi", "block.minecraft.spruce_planks": "Fusta de pi", "block.minecraft.spruce_pressure_plate": "Placa de pressió de pi", "block.minecraft.spruce_sapling": "Esqueix de pi", "block.minecraft.spruce_sign": "Cartell de pi", "block.minecraft.spruce_slab": "Llosa de pi", "block.minecraft.spruce_stairs": "<PERSON><PERSON><PERSON> de pi", "block.minecraft.spruce_trapdoor": "Trapa de pi", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON><PERSON> penjant de pi amb suport", "block.minecraft.spruce_wall_sign": "Cartell de pi a la paret", "block.minecraft.spruce_wood": "Fusta de pi", "block.minecraft.sticky_piston": "<PERSON><PERSON><PERSON>", "block.minecraft.stone": "Roca", "block.minecraft.stone_brick_slab": "Llosa de maons de roca", "block.minecraft.stone_brick_stairs": "<PERSON><PERSON><PERSON> de ma<PERSON> de roca", "block.minecraft.stone_brick_wall": "<PERSON><PERSON> de ma<PERSON> de roca", "block.minecraft.stone_bricks": "Maons de roca", "block.minecraft.stone_button": "Botó de roca", "block.minecraft.stone_pressure_plate": "Placa de pressió de roca", "block.minecraft.stone_slab": "Llosa de roca", "block.minecraft.stone_stairs": "Escales de roca", "block.minecraft.stonecutter": "Talla-roques", "block.minecraft.stripped_acacia_log": "Tronc d'acàcia escorçat", "block.minecraft.stripped_acacia_wood": "Fusta d'acàcia escorçada", "block.minecraft.stripped_bamboo_block": "Bloc de bambú escorçat", "block.minecraft.stripped_birch_log": "Tronc de bedoll escorçat", "block.minecraft.stripped_birch_wood": "Fusta de bedoll escorçada", "block.minecraft.stripped_cherry_log": "Tronc de cirerer escorçat", "block.minecraft.stripped_cherry_wood": "Fusta de cirerer escorçada", "block.minecraft.stripped_crimson_hyphae": "Hifes carmesines escorçades", "block.minecraft.stripped_crimson_stem": "Peu carmesí escorçat", "block.minecraft.stripped_dark_oak_log": "Tronc de pomer fosc escorçat", "block.minecraft.stripped_dark_oak_wood": "Fusta de pomer fosc escorçada", "block.minecraft.stripped_jungle_log": "Tronc de jungla escorçat", "block.minecraft.stripped_jungle_wood": "Fusta de jungla escorçada", "block.minecraft.stripped_mangrove_log": "Tronc de mangle escorçat", "block.minecraft.stripped_mangrove_wood": "Fusta de mangle escorçada", "block.minecraft.stripped_oak_log": "Tronc de pomer escorçat", "block.minecraft.stripped_oak_wood": "Fusta de pomer escorçada", "block.minecraft.stripped_pale_oak_log": "Tronc de pomer pàl·lid escorçat", "block.minecraft.stripped_pale_oak_wood": "Fusta de pomer pàl·lid escorçada", "block.minecraft.stripped_spruce_log": "Tronc de pi escorçat", "block.minecraft.stripped_spruce_wood": "Fusta de pi escorçada", "block.minecraft.stripped_warped_hyphae": "Hifes guerxes escorçades", "block.minecraft.stripped_warped_stem": "Peu guerxo escorçat", "block.minecraft.structure_block": "Bloc estructural", "block.minecraft.structure_void": "Buit estructural", "block.minecraft.sugar_cane": "<PERSON><PERSON>", "block.minecraft.sunflower": "Gira-sol", "block.minecraft.suspicious_gravel": "Grava sospitosa", "block.minecraft.suspicious_sand": "Sorra sospitosa", "block.minecraft.sweet_berry_bush": "A<PERSON>ust de baies dolces", "block.minecraft.tall_dry_grass": "Herba seca alta", "block.minecraft.tall_grass": "Herba alta", "block.minecraft.tall_seagrass": "Herba marina alta", "block.minecraft.target": "<PERSON>", "block.minecraft.terracotta": "Terracota", "block.minecraft.test_block": "<PERSON> de proves", "block.minecraft.test_instance_block": "Bloc de prova d'instàncies", "block.minecraft.tinted_glass": "Vidre opac", "block.minecraft.tnt": "<PERSON><PERSON><PERSON>", "block.minecraft.tnt.disabled": "Les explosions de TNT estan deshabilitades", "block.minecraft.torch": "Torxa", "block.minecraft.torchflower": "Florxa", "block.minecraft.torchflower_crop": "<PERSON><PERSON><PERSON>", "block.minecraft.trapped_chest": "<PERSON><PERSON><PERSON> trampa", "block.minecraft.trial_spawner": "Generador de reptes", "block.minecraft.tripwire": "<PERSON><PERSON>", "block.minecraft.tripwire_hook": "Ganxo", "block.minecraft.tube_coral": "<PERSON>l de <PERSON>", "block.minecraft.tube_coral_block": "Bloc de corall de tub", "block.minecraft.tube_coral_fan": "Gorgònia de tub", "block.minecraft.tube_coral_wall_fan": "Gorgònia de tub a la paret", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "<PERSON>los<PERSON> de ma<PERSON> de tuf ", "block.minecraft.tuff_brick_stairs": "<PERSON><PERSON><PERSON> de ma<PERSON> de tuf", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON> de ma<PERSON> de tuf", "block.minecraft.tuff_bricks": "<PERSON><PERSON> de tuf", "block.minecraft.tuff_slab": "<PERSON><PERSON><PERSON> de tuf", "block.minecraft.tuff_stairs": "<PERSON><PERSON><PERSON> de tuf", "block.minecraft.tuff_wall": "<PERSON><PERSON> de tuf", "block.minecraft.turtle_egg": "<PERSON>u de tortuga", "block.minecraft.twisting_vines": "Lianes retorçades", "block.minecraft.twisting_vines_plant": "Lianes retorçades", "block.minecraft.vault": "Caixa forta", "block.minecraft.verdant_froglight": "Granollum verdosa", "block.minecraft.vine": "<PERSON><PERSON>", "block.minecraft.void_air": "Aire del buit", "block.minecraft.wall_torch": "Torxa a la paret", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_door": "Porta guerxa", "block.minecraft.warped_fence": "Tanca guerxa", "block.minecraft.warped_fence_gate": "Porta de tanca guerxa", "block.minecraft.warped_fungus": "<PERSON><PERSON> guer<PERSON>o", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON> pen<PERSON> g<PERSON>o", "block.minecraft.warped_hyphae": "<PERSON><PERSON><PERSON> guer<PERSON>", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.warped_planks": "<PERSON><PERSON> guerxa", "block.minecraft.warped_pressure_plate": "Placa de pressió guerxa", "block.minecraft.warped_roots": "Arrels guerxes", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON> guer<PERSON>o", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON> guer<PERSON>", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON> guer<PERSON>", "block.minecraft.warped_stem": "<PERSON><PERSON> guer<PERSON>o", "block.minecraft.warped_trapdoor": "<PERSON><PERSON><PERSON> guerxa", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON> penjant guerxo amb suport", "block.minecraft.warped_wall_sign": "Cartell guerxo a la paret", "block.minecraft.warped_wart_block": "Bloc de berrugues guerxes", "block.minecraft.water": "Aigua", "block.minecraft.water_cauldron": "<PERSON><PERSON> amb aigua", "block.minecraft.waxed_chiseled_copper": "Coure encerat cisellat", "block.minecraft.waxed_copper_block": "Bloc de coure encerat", "block.minecraft.waxed_copper_bulb": "Bombeta de coure encerat", "block.minecraft.waxed_copper_door": "Porta de coure encerat", "block.minecraft.waxed_copper_grate": "Reixa de coure encerat", "block.minecraft.waxed_copper_trapdoor": "Trapa de coure encerat", "block.minecraft.waxed_cut_copper": "Coure tallat encerat", "block.minecraft.waxed_cut_copper_slab": "Llosa de coure tallat encerada", "block.minecraft.waxed_cut_copper_stairs": "Escales de coure tallat encerades", "block.minecraft.waxed_exposed_chiseled_copper": "Coure exposat encerat cisellat", "block.minecraft.waxed_exposed_copper": "Coure exposat encerat", "block.minecraft.waxed_exposed_copper_bulb": "Bombeta de coure exposat encerat", "block.minecraft.waxed_exposed_copper_door": "Porta de coure exposat encerat", "block.minecraft.waxed_exposed_copper_grate": "Reixa de coure exposat encerat", "block.minecraft.waxed_exposed_copper_trapdoor": "Trapa de coure exposat encerat", "block.minecraft.waxed_exposed_cut_copper": "Coure tallat exposat encerat", "block.minecraft.waxed_exposed_cut_copper_slab": "Llosa de coure tallat exposada encerada", "block.minecraft.waxed_exposed_cut_copper_stairs": "Escales de coure tallat exposades encerades", "block.minecraft.waxed_oxidized_chiseled_copper": "Coure oxidat encerat cisellat", "block.minecraft.waxed_oxidized_copper": "Coure oxidat encerat", "block.minecraft.waxed_oxidized_copper_bulb": "Bombeta de coure oxidat encerat", "block.minecraft.waxed_oxidized_copper_door": "Porta de coure oxidat encerat", "block.minecraft.waxed_oxidized_copper_grate": "Reixa de coure oxidat encerat", "block.minecraft.waxed_oxidized_copper_trapdoor": "Trapa de coure oxidat encerat", "block.minecraft.waxed_oxidized_cut_copper": "Coure tallat oxidat encerat", "block.minecraft.waxed_oxidized_cut_copper_slab": "Llosa de coure tallat oxidat encerada", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Escales de coure tallat oxidat encerades", "block.minecraft.waxed_weathered_chiseled_copper": "Coure corroït encerat cisellat", "block.minecraft.waxed_weathered_copper": "Coure corroït encerat", "block.minecraft.waxed_weathered_copper_bulb": "Bombeta de coure corroït encerat", "block.minecraft.waxed_weathered_copper_door": "Porta de coure corroït encerat", "block.minecraft.waxed_weathered_copper_grate": "Reixa de coure corroït encerat", "block.minecraft.waxed_weathered_copper_trapdoor": "Trapa de coure corroït encerat", "block.minecraft.waxed_weathered_cut_copper": "Coure tallat corroït encerat", "block.minecraft.waxed_weathered_cut_copper_slab": "Llosa de coure tallat corroïda encerada", "block.minecraft.waxed_weathered_cut_copper_stairs": "Escales de coure tallat corroïdes encerades", "block.minecraft.weathered_chiseled_copper": "Coure corroït cisellat", "block.minecraft.weathered_copper": "Coure corroït", "block.minecraft.weathered_copper_bulb": "Bombeta de coure corroït", "block.minecraft.weathered_copper_door": "Porta de coure corroït", "block.minecraft.weathered_copper_grate": "Reixa de coure corroït", "block.minecraft.weathered_copper_trapdoor": "Trapa de coure corroït", "block.minecraft.weathered_cut_copper": "Coure tallat corroït", "block.minecraft.weathered_cut_copper_slab": "Llosa de coure tallat corroïda", "block.minecraft.weathered_cut_copper_stairs": "Escales de coure tallat corroïdes", "block.minecraft.weeping_vines": "Lianes ploroses", "block.minecraft.weeping_vines_plant": "Lianes ploroses", "block.minecraft.wet_sponge": "Esponja humida", "block.minecraft.wheat": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.white_banner": "Estendard blanc", "block.minecraft.white_bed": "<PERSON><PERSON> blanc", "block.minecraft.white_candle": "<PERSON><PERSON><PERSON><PERSON> blanca", "block.minecraft.white_candle_cake": "Pastís amb espelma blanca", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON> blanca", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.white_concrete_powder": "Pols de formigó blanc", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON> blanca", "block.minecraft.white_shulker_box": "Cai<PERSON> de shulker blanca", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.white_stained_glass_pane": "Panell de vidre tenyit de blanc", "block.minecraft.white_terracotta": "Terracota blanca", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> blanca", "block.minecraft.white_wool": "<PERSON><PERSON> blan<PERSON>", "block.minecraft.wildflowers": "Flors silvestres", "block.minecraft.wither_rose": "<PERSON>", "block.minecraft.wither_skeleton_skull": "Calavera d'esquelet wither", "block.minecraft.wither_skeleton_wall_skull": "Calavera d'esquelet wither a la paret", "block.minecraft.yellow_banner": "Estendard d'or", "block.minecraft.yellow_bed": "<PERSON><PERSON> groc", "block.minecraft.yellow_candle": "<PERSON><PERSON><PERSON><PERSON> groga", "block.minecraft.yellow_candle_cake": "<PERSON>ís amb espelma groga", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON> groga", "block.minecraft.yellow_concrete": "Formigó groc", "block.minecraft.yellow_concrete_powder": "Pols de formigó groc", "block.minecraft.yellow_glazed_terracotta": "<PERSON><PERSON> g<PERSON>a", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON> groga", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON> ten<PERSON> de g<PERSON>c", "block.minecraft.yellow_stained_glass_pane": "Panell de vidre tenyit de groc", "block.minecraft.yellow_terracotta": "Terracota groga", "block.minecraft.yellow_wool": "<PERSON><PERSON>", "block.minecraft.zombie_head": "Cap de zombi", "block.minecraft.zombie_wall_head": "Cap de zombi a la paret", "book.byAuthor": "escrit per %1$s", "book.edit.title": "Pantalla d'edició del llibre", "book.editTitle": "Escriu el títol del llibre:", "book.finalizeButton": "Signa i tanca", "book.finalizeWarning": "NOTA: Un cop signat, un llibre no és editable.", "book.generation.0": "Original", "book.generation.1": "Còpia de l'original", "book.generation.2": "Còpia d'una còpia", "book.generation.3": "Fet bocins", "book.invalid.tag": "* Etiqueta invàlida *", "book.pageIndicator": "Pàgina %1$s de %2$s", "book.page_button.next": "<PERSON><PERSON><PERSON><PERSON> segü<PERSON>", "book.page_button.previous": "Pàgina anterior", "book.sign.title": "Pantalla de signatura del llibre", "book.sign.titlebox": "Títol", "book.signButton": "Signar", "book.view.title": "Pantalla de visualització del llibre", "build.tooHigh": "El límit d'alçada per construir és de %s blocs", "chat.cannotSend": "No es pot enviar el missatge de xat", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Fes clic per a teletransportar-te", "chat.copy": "Copiar", "chat.copy.click": "Fes clic per copiar-ho al porta-retalls", "chat.deleted_marker": "Aquest missatge ha estat eliminat pel servidor.", "chat.disabled.chain_broken": "Xat deshabilitat a causa del trencament d'una cadena. Si us plau, intenta reconnectar-t'hi.", "chat.disabled.expiredProfileKey": "El xat s'ha desactivat perquè la clau del perfil pública ha caducat. Intenta tornar a connectar-te.", "chat.disabled.invalid_command_signature": "El comandament tenia signatures de comando inesperades o no en té.", "chat.disabled.invalid_signature": "Xat tenia una signatura invàlida. Si us plau, intenta reconnectar-te.", "chat.disabled.launcher": "Xat deshabilitat des de les opcions del llançador. No es pot enviar el missatge.", "chat.disabled.missingProfileKey": "El xat s'ha desactivat perquè falta la clau del perfil pública. Intenta tornar a connectar-te.", "chat.disabled.options": "Xat deshabilitat des de les opcions del client.", "chat.disabled.out_of_order_chat": "El xat està fora de servei. S'ha canviat l'hora del teu sistema?", "chat.disabled.profile": "Xat deshabilitat des de la configuració del compte. Prem '%s' un altre cop per a més informació.", "chat.disabled.profile.moreInfo": "Xat deshabilitat des de la configuració del compte. No es poden enviar ni veure missatges.", "chat.editBox": "xat", "chat.filtered": "Filtrat pel servidor.", "chat.filtered_full": "El servidor ha amagat el teu missatge per alguns jugadors.", "chat.link.confirm": "Est<PERSON>s segur de voler obrir l'enllaç?", "chat.link.confirmTrusted": "Vols obrir aquest enllaç o copiar-lo al porta-papers?", "chat.link.open": "Obrir en el navegador", "chat.link.warning": "Mai obris enllaços de gent en la qui no confies!", "chat.queue": "[+%s línies pendents]", "chat.square_brackets": "[%s]", "chat.tag.error": "El servidor ha enviat un missatge no vàlid.", "chat.tag.modified": "Missatge modificat pel servidor. Missatge original:", "chat.tag.not_secure": "Missatge sense verificar. No pot ser reportat.", "chat.tag.system": "Missatge del servidor. No pot ser reportat.", "chat.tag.system_single_player": "Missatge del servidor.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s ha completat el repte %s", "chat.type.advancement.goal": "%s ha assolit l'objectiu %s", "chat.type.advancement.task": "%s ha aconseguit l'avenç %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Missatge de l'equip", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s diu %s", "chat.validation_error": "Error de validació del xat", "chat_screen.message": "Missatge a enviar: %s", "chat_screen.title": "Pantalla del xat", "chat_screen.usage": "Escriu un missatge i prem Enter per enviar", "chunk.toast.checkLog": "Mira el registre per a més detalls", "chunk.toast.loadFailure": "No s'ha pogut carregar el chunk a %s", "chunk.toast.lowDiskSpace": "Poc espai al disc!", "chunk.toast.lowDiskSpace.description": "Pot ser que no es pugui guardar el món.", "chunk.toast.saveFailure": "No s'ha pogut guardar el chunk a %s", "clear.failed.multiple": "No s'han trobat ítems a l'inventari de %s jugadors", "clear.failed.single": "No s'han trobat ítems a l'inventari del jugador %s", "color.minecraft.black": "Negre", "color.minecraft.blue": "Blau", "color.minecraft.brown": "<PERSON><PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON>", "color.minecraft.green": "Verd", "color.minecraft.light_blue": "<PERSON>lau cel", "color.minecraft.light_gray": "<PERSON><PERSON> clar", "color.minecraft.lime": "Llima", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "<PERSON>ron<PERSON>", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "<PERSON>", "color.minecraft.red": "<PERSON><PERSON><PERSON>", "color.minecraft.white": "<PERSON>", "color.minecraft.yellow": "Groc", "command.context.here": "<--[AQUÍ]", "command.context.parse_error": "%s a la posició %s: %s", "command.exception": "No es pot analitzar el comandament: %s", "command.expected.separator": "Es requereix un espai en blanc per concluir un argument. Algunes dades estan escrites sense separar", "command.failed": "S'ha produït un error inesperat en intentar executar el comandament", "command.forkLimit": "S'ha arribat al màxim de contextos (%s)", "command.unknown.argument": "L'argument no és correcte per al comadament", "command.unknown.command": "Comandament desconegut o incomplet, consulteu l'error a continuació", "commands.advancement.criterionNotFound": "L'avenç %1$s no conté el criteri \"%2$s\"", "commands.advancement.grant.criterion.to.many.failure": "No s'ha pogut concedir el criteri \"%s\" de l'avanç %s a %s jugadors degut a que ja el tenen", "commands.advancement.grant.criterion.to.many.success": "S'ha concedit el criteri \"%s\" de l'avenç %s a %s jugadors", "commands.advancement.grant.criterion.to.one.failure": "No s'ha pogut concedir el criteri \"%s\" de l'avenç %s a %s degut a que ja el té", "commands.advancement.grant.criterion.to.one.success": "S'ha concedit el criteri \"%s\" de l'avenç %s a %s", "commands.advancement.grant.many.to.many.failure": "No s'han pogut concedir %s avenços a %s jugadors degut a que ja els tenen", "commands.advancement.grant.many.to.many.success": "S'han concedit %s avenços a %s jugadors", "commands.advancement.grant.many.to.one.failure": "No s'han pogut concedir %s avenços a %s degut a que ja els té", "commands.advancement.grant.many.to.one.success": "S'han concedit %s avenços a %s", "commands.advancement.grant.one.to.many.failure": "No s'ha pogut concedir l'avenç %s a %s jugadors degut a que ja el tenen", "commands.advancement.grant.one.to.many.success": "S'ha concedit l'avenç %s a %s jugadors", "commands.advancement.grant.one.to.one.failure": "No s'ha pogut concedir l'avenç %s a %s degut a que ja el té", "commands.advancement.grant.one.to.one.success": "S'ha concedit l'avenç %s a %s", "commands.advancement.revoke.criterion.to.many.failure": "No s'ha pogut retirar el criteri \"%s\" de l'avenç %s de %s jugadors degut a que no el tenen", "commands.advancement.revoke.criterion.to.many.success": "S'ha retirat el criteri \"%s\" de l'avenç %s de %s jugadors", "commands.advancement.revoke.criterion.to.one.failure": "No s'ha pogut retirar el criteri \"%s\" de l'avenç %s de %s degut a que no el té", "commands.advancement.revoke.criterion.to.one.success": "S'ha retirat el criteri \"%s\" de l'avenç %s de %s", "commands.advancement.revoke.many.to.many.failure": "No s'han pogut retirar %s avenços a %s jugadors degut a que no els tenen", "commands.advancement.revoke.many.to.many.success": "S'han retirat %s avenços de %s jugadors", "commands.advancement.revoke.many.to.one.failure": "No s'han pogut retirar %s avenços de %s degut a que no els té", "commands.advancement.revoke.many.to.one.success": "S'han retirat %s avenços de %s", "commands.advancement.revoke.one.to.many.failure": "No s'ha pogut retirar l'avenç %s de %s jugadors degut a que no el tenen", "commands.advancement.revoke.one.to.many.success": "S'ha retirat l'avenç %s de %s jugadors", "commands.advancement.revoke.one.to.one.failure": "No s'ha pogut retirar l'avenç %s de %s degut a que no el té", "commands.advancement.revoke.one.to.one.success": "S'ha retirat l'avenç %s de %s", "commands.attribute.base_value.get.success": "El valor base de l'atribut %s de l'entitat %s és %s", "commands.attribute.base_value.reset.success": "El valor base de l'atribut %s de l'entitat %s s'ha restablert al predeterminat %s", "commands.attribute.base_value.set.success": "El valor base de l'atribut %s de l'entitat %s s'ha canviat a %s", "commands.attribute.failed.entity": "%s no és una entitat vàlida per aquest comandament", "commands.attribute.failed.modifier_already_present": "El modificador %s ja està present a l'atribut %s de l'entitat %s", "commands.attribute.failed.no_attribute": "L'entitat %s no té l'atribut %s", "commands.attribute.failed.no_modifier": "L'atribut %s de l'entitat %s no té el modificador %s", "commands.attribute.modifier.add.success": "Afegit el modificador %s a l'atribut %s de l'entitat %s", "commands.attribute.modifier.remove.success": "Eliminat el modificador %s de l'atribut %s de l'entitat %s", "commands.attribute.modifier.value.get.success": "El valor del modificador %s de l'atribut %s de l'entitat %s és %s", "commands.attribute.value.get.success": "El valor de l'atribut %s de l'entitat %s és %s", "commands.ban.failed": "<PERSON>s ha canviat, el jugador ja estava bandejat", "commands.ban.success": "S'ha bandejat a %s: %s", "commands.banip.failed": "Res ha canviat, l'IP ja estava bandejada", "commands.banip.info": "Aquest bandejament afecta %s jugador(s): %s", "commands.banip.invalid": "IP no vàlida o jugador desconegut", "commands.banip.success": "S'ha bandejat l'IP %s: %s", "commands.banlist.entry": "%2$s ha bandejat a %1$s: %3$s", "commands.banlist.entry.unknown": "(Desconegut)", "commands.banlist.list": "Hi ha %s bandejament(s):", "commands.banlist.none": "No hi ha bandejaments", "commands.bossbar.create.failed": "Ja existeix una barra de l'amo amb ID \"%s\"", "commands.bossbar.create.success": "S'ha creat la barra de l'amo \"%s\"", "commands.bossbar.get.max": "La barra de l'amo \"%s\" té un valor màxim de %s", "commands.bossbar.get.players.none": "La barra de l'amo \"%s\" no té jugadors connectats", "commands.bossbar.get.players.some": "La barra personalitzada de l'amo %s té %s jugador(s) connectats: %s", "commands.bossbar.get.value": "La barra de l'amo \"%s\" té un valor de %s", "commands.bossbar.get.visible.hidden": "La barra de l'amo \"%s\" està oculta", "commands.bossbar.get.visible.visible": "La barra de l'amo \"%s\" està visible", "commands.bossbar.list.bars.none": "No hi ha cap barra de l'amo activa", "commands.bossbar.list.bars.some": "Hi ha %s barra(es) personalitzada(es) de l'amo actiu(s): %s", "commands.bossbar.remove.success": "S'ha eliminat la barra de l'amo \"%s\"", "commands.bossbar.set.color.success": "S'ha canviat el color de la barra de l'amo \"%s\"", "commands.bossbar.set.color.unchanged": "Res ha canviat, aquest ja era el color d'aquesta barra de l'amo", "commands.bossbar.set.max.success": "S'ha canviat el màxim de la barra de l'amo \"%s\" a %s", "commands.bossbar.set.max.unchanged": "Res ha canviat, aquest ja era el màxim d'aquesta barra de l'amo", "commands.bossbar.set.name.success": "S'ha renombrat la barra de l'amo \"%s\"", "commands.bossbar.set.name.unchanged": "Res ha canviat, aquest ja era el nom d'aquesta barra de l'amo", "commands.bossbar.set.players.success.none": "La barra de l'amo \"%s\" no té jugadors associats", "commands.bossbar.set.players.success.some": "La barra personalitzada de l'amo %s té ara %s jugador(s): %s", "commands.bossbar.set.players.unchanged": "Res ha canviat, aquests jugadors ja estaven a la barra de l'amo sense ningú per agregar o eliminar", "commands.bossbar.set.style.success": "S'ha canviat l'estil de la barra de l'amo \"%s\"", "commands.bossbar.set.style.unchanged": "Res ha canviat, aquest ja era l'estil d'aquesta barra de l'amo", "commands.bossbar.set.value.success": "S'ha canviat el valor de la barra de l'amo \"%s\" a %s", "commands.bossbar.set.value.unchanged": "Res ha canviat, aquest ja era el valor d'aquesta barra de l'amo", "commands.bossbar.set.visibility.unchanged.hidden": "Res ha canviat, la barra de l'amo ja estava oculta", "commands.bossbar.set.visibility.unchanged.visible": "Res ha canviat, la barra de l'amo ja estava visible", "commands.bossbar.set.visible.success.hidden": "La barra de l'amo \"%s\" està ara oculta", "commands.bossbar.set.visible.success.visible": "La barra de l'amo \"%s\" és ara visible", "commands.bossbar.unknown": "No existeix cap barra de l'amo amb ID \"%s\"", "commands.clear.success.multiple": "S'han eliminat %s objecte(s) de %s jugadors", "commands.clear.success.single": "S'han eliminat %s objecte(s) del jugador %s", "commands.clear.test.multiple": "S'ha(n) trobat %s ítem(s) que coincideixen a l'inventari de %s jugadors", "commands.clear.test.single": "S'ha(n) trobat %s ítem(s) que coincideixen a l'inventari de %s", "commands.clone.failed": "No s'han clonat blocs", "commands.clone.overlap": "L'àrea d'origen i l'àrea de destí no poden coincidir", "commands.clone.success": "S'han clonat %s bloc(s)", "commands.clone.toobig": "Hi ha massa blocs dins l'àrea especificada (el màxim és %s, s'han especificat %s)", "commands.damage.invulnerable": "L'objetiu és invulnerable a aquest tipus de mal", "commands.damage.success": "%s punts de vida trets a %s", "commands.data.block.get": "%s del bloc a %s, %s, %s, després de l'escala de %s ha canviat a %s", "commands.data.block.invalid": "El bloc especificat no és una entitat-bloc", "commands.data.block.modified": "S'han modificat les dades del bloc a %s, %s, %s", "commands.data.block.query": "El bloc a %s, %s, %s conté les següents dades: %s", "commands.data.entity.get": "%s de %s, després d'un factor escala de %s, ha canviat a %s", "commands.data.entity.invalid": "No s'han pogut modificar les dades del jugador", "commands.data.entity.modified": "S'han modificat les dades de l'entitat %s", "commands.data.entity.query": "L'entitat %s té les següents dades: %s", "commands.data.get.invalid": "No s'ha pogut obtenir %s, només es permeten etiquetes numèriques", "commands.data.get.multiple": "Aquest argument només accepta un sol valor NBT", "commands.data.get.unknown": "No s'ha pogut obtenir %s, l'etiqueta no existeix", "commands.data.merge.failed": "Res ha canviat. Les propietats especificades ja tenen aquests valors", "commands.data.modify.expected_list": "S'esperava una llista, s'ha obtingut: %s", "commands.data.modify.expected_object": "S'esperava un objecte, s'ha obtingut: %s", "commands.data.modify.expected_value": "S'esperava un valor, s'ha obtingut: %s", "commands.data.modify.invalid_index": "L'índex de llista no és vàlid: %s", "commands.data.modify.invalid_substring": "Índexs de subcadenes no vàlids: %s fins %s", "commands.data.storage.get": "%s al contenidor %s després d'un factor d'escalada de %s són %s", "commands.data.storage.modified": "Emmagatzematge modificat %s", "commands.data.storage.query": "L'emmagatzematge %s conté: %s", "commands.datapack.create.already_exists": "Un paquet anomenat '%s' ja existeix", "commands.datapack.create.invalid_full_name": "Invalid new pack name '%s'", "commands.datapack.create.invalid_name": "Caràcters invàlids al nom del nou paquet '%s'", "commands.datapack.create.io_failure": "No es pot crear un paquet anomenat '%s', revisa els registres", "commands.datapack.create.metadata_encode_failure": "No s'han pogut codificar les metadades del paquet anomenat '%s': %s", "commands.datapack.create.success": "S'ha creat un nou paquet buit anomenat '%s'", "commands.datapack.disable.failed": "El paquet de dades \"%s\" no estava activat!", "commands.datapack.disable.failed.feature": "El paquet de dades '%s' no es pot desactivar, ja que és part d'una de les variables actives!", "commands.datapack.enable.failed": "El paquet de dades \"%s\" ja estava activat!", "commands.datapack.enable.failed.no_flags": "El paquet de dades '%s' no es pot activar perquè les variables necessàries no han estat activades en aquest món: %s!", "commands.datapack.list.available.none": "No hi ha més paquets de dades disponibles", "commands.datapack.list.available.success": "Hi ha %s paquet(s) de dades disponibles: %s", "commands.datapack.list.enabled.none": "No hi ha paquets de dades actius", "commands.datapack.list.enabled.success": "Hi ha %s paquet(s) de dades actius: %s", "commands.datapack.modify.disable": "Desactivant el paquet de dades %s", "commands.datapack.modify.enable": "S'està activant el paquet de dades %s", "commands.datapack.unknown": "Paquet de dades desconegut: %s", "commands.debug.alreadyRunning": "L'anàlisi de cicles ja havia començat", "commands.debug.function.noRecursion": "No es pot traçar des de dins d'una funció", "commands.debug.function.noReturnRun": "El seguiment no es pot utilitzar amb", "commands.debug.function.success.multiple": "S'ha(n) traçat %s comandament(s) de %s funcions al fitxer de sortida %s", "commands.debug.function.success.single": "S'ha(n) traçat %s comandament(s) de la funció '%s' al fitxer de sortida %s", "commands.debug.function.traceFailed": "No s'ha pogut traçar la funció", "commands.debug.notRunning": "L'anàlisi de cicles no ha començat", "commands.debug.started": "Anàlisi de cicles iniciat", "commands.debug.stopped": "S'ha aturat l'anàlisi de cicles després de %s segons i %s cicles (%s cicles per segon)", "commands.defaultgamemode.success": "El mode de joc per defecte és ara %s", "commands.deop.failed": "<PERSON>s ha canviat, el jugador no era administrador", "commands.deop.success": "%s ja no és administrador(a) del servidor", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "La dificultat no ha canviat, ja estava configurada a %s", "commands.difficulty.query": "La dificultat és %s", "commands.difficulty.success": "La dificultat s'ha establert a %s", "commands.drop.no_held_items": "L'entitat no pot sostenir cap objecte", "commands.drop.no_loot_table": "L'entitat %s no te taula de botí", "commands.drop.no_loot_table.block": "El bloc %s no té taula de botí", "commands.drop.success.multiple": "S'han deixat anar %s ítems", "commands.drop.success.multiple_with_table": "S'han deixat anar %s ítems de la taula de botí %s", "commands.drop.success.single": "Deixat anar %s %s", "commands.drop.success.single_with_table": "S'ha deixat anar %s %s de la taula de botí %s", "commands.effect.clear.everything.failed": "L'objectiu no té efectes per eliminar", "commands.effect.clear.everything.success.multiple": "S'han eliminat tots els efectes de %s objectius", "commands.effect.clear.everything.success.single": "S'han eliminat tots els efectes de %s", "commands.effect.clear.specific.failed": "L'objectiu no té l'efecte especificat", "commands.effect.clear.specific.success.multiple": "S'ha eliminat l'efecte %s de %s objectius", "commands.effect.clear.specific.success.single": "S'ha eliminat l'efecte %s de %s", "commands.effect.give.failed": "No s'ha pogut aplicar aquest efecte (l'objectiu és immune als efectes, o té alguna cosa més forta)", "commands.effect.give.success.multiple": "S'ha aplicat l'efecte %s sobre %s entitats", "commands.effect.give.success.single": "S'ha aplicat l'efecte %s a %s", "commands.enchant.failed": "Res ha canviat, l'objectiu no tenia un ítem a la mà o l'ítem no és compatible amb l'encanteri", "commands.enchant.failed.entity": "%s no és una entitat vàlida per aquest comandament", "commands.enchant.failed.incompatible": "%s no admet aquest encanteri", "commands.enchant.failed.itemless": "%s no sosté cap ítem", "commands.enchant.failed.level": "%s és superior al nivell màxim de l'encanteri (%s)", "commands.enchant.success.multiple": "S'ha aplicat l'encanteri %s a %s entitats", "commands.enchant.success.single": "S'ha aplicat l'encanteri %s a l'ítem de %s", "commands.execute.blocks.toobig": "Hi ha massa blocs dins l'àrea especificada (el màxim és %s, s'han especificat %s)", "commands.execute.conditional.fail": "La prova no s'ha superat", "commands.execute.conditional.fail_count": "La prova no s'ha superat, quantitat: %s", "commands.execute.conditional.pass": "La prova s'ha superat", "commands.execute.conditional.pass_count": "La prova s'ha superat, quantitat: %s", "commands.execute.function.instantiationFailure": "Error a l'inicialitzar la funció %s: %s", "commands.experience.add.levels.success.multiple": "S'han donat %s nivells d'experiència a %s jugadors", "commands.experience.add.levels.success.single": "S'han donat %s nivells d'experiència a %s", "commands.experience.add.points.success.multiple": "S'han donat %s punts d'experiència a %s jugadors", "commands.experience.add.points.success.single": "S'han donat %s punts d'experiència a %s", "commands.experience.query.levels": "%s té %s nivells d'experiència", "commands.experience.query.points": "%s té %s punts d'experiència", "commands.experience.set.levels.success.multiple": "S'ha canviat el nivell d'experiència de %2$s jugadors a %1$s", "commands.experience.set.levels.success.single": "S'ha canviat el nivell d'experiència de %2$s a %1$s", "commands.experience.set.points.invalid": "No es poden establir punts d'experiència per sobre del màxim de punts actual", "commands.experience.set.points.success.multiple": "S'han canviat els punts d'experiència de %2$s jugadors a %1$s", "commands.experience.set.points.success.single": "S'han canviat els punts d'experiècia de %2$s a %1$s", "commands.fill.failed": "No s'han emplenat blocs", "commands.fill.success": "S'han omplert %s bloc(s) amb èxit", "commands.fill.toobig": "Hi ha massa blocs dins l'àrea especificada (el màxim és %s, s'han especificat %s)", "commands.fillbiome.success": "Biomes definits entre %s, %s, %s i %s, %s, %s", "commands.fillbiome.success.count": "%s Entrada/entrades del bioma establert entre %s, %s, %s i %s, %s, %s", "commands.fillbiome.toobig": "Hi ha massa blocs dins el volum especificat (el màxim és %s, s'han especificat %s)", "commands.forceload.added.failure": "No s'ha marcat cap chunk per forçar-ne la càrrega", "commands.forceload.added.multiple": "S'han marcat %s chunks en %s de %s a %s per forçar-ne la càrrega", "commands.forceload.added.none": "No s'ha trobat cap chunk carregar a la força en %s", "commands.forceload.added.single": "S'ha marcat el chunk %s en %s per forçar-ne la càrrega", "commands.forceload.list.multiple": "S'han trobat %s chunks carregats a la força en %s a: %s", "commands.forceload.list.single": "S'ha trobat un chunk carregat a la força en %s a: %s", "commands.forceload.query.failure": "El chunk de %s en %s no està marcat per forçar-ne la càrrega", "commands.forceload.query.success": "El chunk de %s en %s està marcat per forçar-ne la càrrega", "commands.forceload.removed.all": "S'han desmarcat tots els chunks carregats a la força en %s", "commands.forceload.removed.failure": "No s'ha deixat de forçar la càrrega de cap chunk", "commands.forceload.removed.multiple": "S'han desmarcat %s chunks en %s de %s a %s per forçar-ne la càrrega", "commands.forceload.removed.single": "S'ha desmarcat el chunk de %s en %s per forçar-ne la càrrega", "commands.forceload.toobig": "Hi ha masses chunks a la zona especificada (el màxim és %s, s'han especificat %s)", "commands.function.error.argument_not_compound": "El tipus d'argument no és vàlid: %s, s'esperava Compound", "commands.function.error.missing_argument": "Falta l'argument %2$s a la funció %1$s", "commands.function.error.missing_arguments": "Falten arguments a la funció %s", "commands.function.error.parse": "En executar la macro %s: la comanda '%s' ha provocat l'error: %s", "commands.function.instantiationFailure": "Error en inicialitzar la funció %s: %s", "commands.function.result": "Funció '%s' ha retornat %s", "commands.function.scheduled.multiple": "Executant funcions %s", "commands.function.scheduled.no_functions": "No s'ha trobat cap funció amb el nom %s", "commands.function.scheduled.single": "Executant la funció %s", "commands.function.success.multiple": "S'ha(n) executat %s comandament(s) de %s funcions", "commands.function.success.multiple.result": "Executades %s funcions", "commands.function.success.single": "S'han executat %s comandament(s) de la funció '%s'", "commands.function.success.single.result": "Funció '%2$s' ha retornat %1$s", "commands.gamemode.success.other": "El mode de joc d'en %s s'ha canviat a %s", "commands.gamemode.success.self": "El teu mode de joc s'ha canviat a %s", "commands.gamerule.query": "La norma %s està establerta com: %s", "commands.gamerule.set": "La norma %s ha canviat a \"%s\"", "commands.give.failed.toomanyitems": "No es pot donar més de %s %s", "commands.give.success.multiple": "%3$s jugadors han rebut %1$s de %2$s", "commands.give.success.single": "%3$s ha rebut %1$s de %2$s", "commands.help.failed": "Comandament desconegut o permisos insuficients", "commands.item.block.set.success": "S'ha substituït un espai en %s, %s, %s amb %s", "commands.item.entity.set.success.multiple": "S'ha substituït un espai en %s entitats amb %s", "commands.item.entity.set.success.single": "S'ha substituït un espai en %s amb %s", "commands.item.source.no_such_slot": "L'origen no té la ranura %s", "commands.item.source.not_a_container": "La posició origen %s, %s, %s no és un contenidor", "commands.item.target.no_changed.known_item": "Cap objectiu ha acceptat l'ítem %s a la ranura %s", "commands.item.target.no_changes": "Cap objectiu ha acceptat l'ítem a la ranura %s", "commands.item.target.no_such_slot": "L'objectiu no té l'espai %s", "commands.item.target.not_a_container": "La posició objectiu %s, %s, %s no és un contenidor", "commands.jfr.dump.failed": "No s'ha pogut copiar el registre de JFR: %s", "commands.jfr.start.failed": "No s'ha pogut iniciar l'anàlisi de rendiment amb JFR", "commands.jfr.started": "S’ha iniciat l'anàlisi de rendiment amb JFR", "commands.jfr.stopped": "S'ha aturat l'anàlisi de rendiment amb JFR i s'ha copiat a %s", "commands.kick.owner.failed": "No es pot expulsar el propietari del servidor al joc LAN", "commands.kick.singleplayer.failed": "No es pot expulsar en un joc individual fora de línia", "commands.kick.success": "S'ha explusat a %s: %s", "commands.kill.success.multiple": "S'han eliminat %s entitats", "commands.kill.success.single": "Assassinat %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Hi ha %s d'un màxim de %s jugadors en línia: %s", "commands.locate.biome.not_found": "No hi ha cap bioma de tipus \"%s\" a una distància raonable", "commands.locate.biome.success": "El bioma de tipus %s més proper està a %s (A %s blocs de distància)", "commands.locate.poi.not_found": "No s'ha trobat un punt d'interès de tipus \"%s\" a una distància raonable", "commands.locate.poi.success": "El punt d'interès de tipus %s més proper està a %s (A %s blocs de distància)", "commands.locate.structure.invalid": "No hi ha cap estructura de tipus \"%s\"", "commands.locate.structure.not_found": "No s'ha trobat cap estructura de tipus \"%s\" a prop", "commands.locate.structure.success": "L'estructura de tipus %s més propera està a %s (A %s blocs de distància)", "commands.message.display.incoming": "%s t'està xiuxiuejant això: %s", "commands.message.display.outgoing": "Has xiuxiuejat això a %s: %s", "commands.op.failed": "<PERSON><PERSON> ha canviat, el jugador ja era administrador", "commands.op.success": "%s s'ha convertit en administrador(a) del servidor", "commands.pardon.failed": "Res ha canviat, el jugador no estava bandejat", "commands.pardon.success": "S'ha desbandejat a %s", "commands.pardonip.failed": "Res ha canviat, aquesta IP no estava bandejada", "commands.pardonip.invalid": "IP invàlida", "commands.pardonip.success": "S'ha desbandejat l'IP %s", "commands.particle.failed": "La partícula no era visible per a ningú", "commands.particle.success": "Mostrant partícula %s", "commands.perf.alreadyRunning": "L'anàlisi de rendiment ja havia començat", "commands.perf.notRunning": "L'anàlisi de rendiment no ha començat", "commands.perf.reportFailed": "No s'ha pogut crear un informe de depuració", "commands.perf.reportSaved": "S'ha creat un informe de depuració a %s", "commands.perf.started": "S'ha iniciat l'anàlisi de rendiment de 10 segons (fes servir '/perf stop' per aturar-lo prematurament)", "commands.perf.stopped": "S'ha aturat l'anàlisi de rendiment després de %s segon(s) i %s cicle(s) (%s cicle(s) per segon)", "commands.place.feature.failed": "No s'ha pogut col·locar l'element", "commands.place.feature.invalid": "No hi ha cap element de tipus \"%s\"", "commands.place.feature.success": "S'ha col·locat \"%s\" a %s, %s, %s", "commands.place.jigsaw.failed": "Error en generar la peça", "commands.place.jigsaw.invalid": "No existeix cap grup de plantilles amb el tipus \"%s\"", "commands.place.jigsaw.success": "Peça generada a %s, %s, %s", "commands.place.structure.failed": "No s'ha pogut col·locar l'estructura", "commands.place.structure.invalid": "No hi ha cap estructura de tipus \"%s\"", "commands.place.structure.success": "Estructura \"%s\" generada a %s, %s, %s", "commands.place.template.failed": "No s'ha pogut col·locar la plantilla", "commands.place.template.invalid": "No hi ha cap plantilla amb l'id \"%s\"", "commands.place.template.success": "Plantilla \"%s\" carregada a %s, %s, %s", "commands.playsound.failed": "El so està massa lluny per a ser escoltat", "commands.playsound.success.multiple": "S'ha reproduït el so %s a %s jugadors", "commands.playsound.success.single": "S'ha reproduït el so %s a %s", "commands.publish.alreadyPublished": "La partida multijugador ja s'allotja al port %s", "commands.publish.failed": "Impossible allotjar joc local", "commands.publish.started": "Joc local allotjat al port %s", "commands.publish.success": "El joc multijugador està ara allotjat al port %s", "commands.random.error.range_too_large": "El rang d'un valor aleatori ha de ser com a màxim 2147483646", "commands.random.error.range_too_small": "El rang d'un valor aleatori ha de ser com a mínim 2", "commands.random.reset.all.success": "Reinicia %s seqüència/es aleatòria/es", "commands.random.reset.success": "Reinicia la seqüència aleatòria %s", "commands.random.roll": "%s ha tret %s (entre %s i %s)", "commands.random.sample.success": "Valor aleatoritzat: %s", "commands.recipe.give.failed": "No s'han après noves receptes", "commands.recipe.give.success.multiple": "S'han desbloquejat %s receptes per a %s jugadors", "commands.recipe.give.success.single": "S'han desbloquejat %s receptes per a %s", "commands.recipe.take.failed": "Cap recepta podria ser oblidada", "commands.recipe.take.success.multiple": "S'han eliminat %s receptes a %s jugadors", "commands.recipe.take.success.single": "S'han eliminat %s receptes a %s", "commands.reload.failure": "Error en la recàrrega; es mantindran les dades antigues", "commands.reload.success": "Recarregant!", "commands.ride.already_riding": "%s ja està muntant %s", "commands.ride.dismount.success": "%s ha baixat de %s", "commands.ride.mount.failure.cant_ride_players": "Els jugadors no poden ser muntats", "commands.ride.mount.failure.generic": "%s no ha pogut muntar a %s", "commands.ride.mount.failure.loop": "Una entitat no pot muntar-se a si mateixa o un dels seus passatgers", "commands.ride.mount.failure.wrong_dimension": "No es pot muntar una entitat en una dimensió diferent", "commands.ride.mount.success": "%s ha mutat a %s", "commands.ride.not_riding": "%s no es troba en cap vehicle", "commands.rotate.success": "Girat", "commands.save.alreadyOff": "El desat ja està desactivat", "commands.save.alreadyOn": "El desat ja està activat", "commands.save.disabled": "L'autoguardat està ara desactivat", "commands.save.enabled": "L'autoguardat està ara activat", "commands.save.failed": "Error al guardar la partida (hi ha prou espai al disc dur?)", "commands.save.saving": "Guardant la partida... (pot tardar una mica)", "commands.save.success": "La partida s'ha guardat", "commands.schedule.cleared.failure": "No hi ha cap planificació amb la id %s", "commands.schedule.cleared.success": "S'ha(n) eliminat %s planificació/planificacions amb id %s", "commands.schedule.created.function": "S'ha programat la funció \"%s\" en %s cicle(s) en temps de joc %s", "commands.schedule.created.tag": "S'ha programat l'etiqueta \"%s\" en %s cicles en temps de joc %s", "commands.schedule.macro": "No es pot carregar en Macro", "commands.schedule.same_tick": "No es pot programar per al cicle actual", "commands.scoreboard.objectives.add.duplicate": "Ja existeix un objectiu amb aquest nom", "commands.scoreboard.objectives.add.success": "S'ha creat l'objectiu %s", "commands.scoreboard.objectives.display.alreadyEmpty": "<PERSON><PERSON> ha canviat, aquesta ranura ja estava buida", "commands.scoreboard.objectives.display.alreadySet": "<PERSON><PERSON> ha canviat, aquesta ranura ja mostra aquest objectiu", "commands.scoreboard.objectives.display.cleared": "S'han tret els objectius que es mostraven a la ranura %s", "commands.scoreboard.objectives.display.set": "S'ha establert que la ranura %s mostri l'objectiu %s", "commands.scoreboard.objectives.list.empty": "No hi ha objectius", "commands.scoreboard.objectives.list.success": "Hi ha %s objectiu(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "S'ha desactivat l'actualització automàtica de mostrar a l'objectiu %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "S'ha activat l'actualització automàtica de mostrar per a l'objectiu %s", "commands.scoreboard.objectives.modify.displayname": "S'ha canviat el nom a mostrar de %s a %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "S'ha esborrat el format del número per defecte de l'objectiu %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "S'ha canviat el format del número per defecte de l'objectiu %s", "commands.scoreboard.objectives.modify.rendertype": "S'ha canviat el tipus de renderitzat de l'objectiu %s", "commands.scoreboard.objectives.remove.success": "S'ha eliminat l'objectiu %s", "commands.scoreboard.players.add.success.multiple": "S'han afegit %s punts a l'objectiu %s per a %s entitats", "commands.scoreboard.players.add.success.single": "S'han afegit %s punts a l'objectiu %s per a %s (ara té %s punts)", "commands.scoreboard.players.display.name.clear.success.multiple": "S'ha esborrat el text a mostrar de %s entitats a %s", "commands.scoreboard.players.display.name.clear.success.single": "S'ha esborrat el text a mostrar de %s a %s", "commands.scoreboard.players.display.name.set.success.multiple": "S'ha canviat el text a mostrar a %s per a %s entitats a %s", "commands.scoreboard.players.display.name.set.success.single": "S'ha canviat el text a mostrar a %s per a %s a %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "S'ha esborrat el format de nombre per a %s entitats a %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "S'ha esborrat el format de nombre de %s a %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "S'ha canviat el format de nombre per a %s entitats a %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "S'ha canviat el format de nombre per a %s a %s", "commands.scoreboard.players.enable.failed": "Res ha canviat, l'activador ja estava activat", "commands.scoreboard.players.enable.invalid": "La funció \"enable\" només serveix amb objectius de tipus \"trigger\"", "commands.scoreboard.players.enable.success.multiple": "S'ha activat l'activador %s per a %s entitats", "commands.scoreboard.players.enable.success.single": "S'ha activat l'activador %s per a %s", "commands.scoreboard.players.get.null": "No s'ha pogut obtenir la puntuació de l'objectiu %s per a %s, no hi ha registre", "commands.scoreboard.players.get.success": "%s té una puntuació de %s a %s", "commands.scoreboard.players.list.empty": "No hi ha entitats registrades", "commands.scoreboard.players.list.entity.empty": "%s no té puntuació per mostrar", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s té puntuació(ns) de %s:", "commands.scoreboard.players.list.success": "Hi ha %s entitat/entitats registrada/es: %s", "commands.scoreboard.players.operation.success.multiple": "S'ha actualitzat la puntuació de l'objectiu %s per a %s entitats", "commands.scoreboard.players.operation.success.single": "S'ha canviat la puntuació de l'objectiu %s per a %s a %s", "commands.scoreboard.players.remove.success.multiple": "S'han eliminat %s punts de l'objectiu %s per a %s entitats", "commands.scoreboard.players.remove.success.single": "S'han eliminat %s punts de l'objectiu %s per a %s (ara té %s)", "commands.scoreboard.players.reset.all.multiple": "S'han reiniciat totes les puntuacions de %s entitats", "commands.scoreboard.players.reset.all.single": "S'han reiniciat totes les puntuacions de %s", "commands.scoreboard.players.reset.specific.multiple": "S'ha reiniciat la puntuació de l'objectiu %s per a %s entitats", "commands.scoreboard.players.reset.specific.single": "S'ha reiniciat la puntuació de l'objectiu %s per a %s", "commands.scoreboard.players.set.success.multiple": "S'ha canviat la puntuació de l'objectiu %s per a %s entitats a %s", "commands.scoreboard.players.set.success.single": "S'ha canviat la puntuació de l'objectiu %s de %s a %s", "commands.seed.success": "Llavor: %s", "commands.setblock.failed": "No s'ha pogut col·locar el bloc", "commands.setblock.success": "El bloc a %s, %s, %s ha canviat", "commands.setidletimeout.success": "Ara el temps màxim d'inactivitat del jugador és %s minut(s)", "commands.setidletimeout.success.disabled": "El temps màxim d'inactivitat dels jugadors s'ha desactivat", "commands.setworldspawn.failure.not_overworld": "Només es pot establir el punt d'aparició del món pel món superior", "commands.setworldspawn.success": "S'ha canviat el punt d'aparició del món a %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Definit el punt de reaparició a %s, %s, %s [%s] en la dimensió %s per a %s jugadors", "commands.spawnpoint.success.single": "Definit el punt de reaparició a %s, %s, %s [%s] en la dimensió %s per a %s", "commands.spectate.not_spectator": "%s no està en mode espectador", "commands.spectate.self": "No pots observar-te a tu mateix", "commands.spectate.success.started": "Observant a %s", "commands.spectate.success.stopped": "Ja no s'està observant cap entitat", "commands.spreadplayers.failed.entities": "No s'han pogut repartir %s entitats al voltant de %s, %s (són massa entitats per aquest espai, prova repartint-les com a màxim %s)", "commands.spreadplayers.failed.invalid.height": "El paràmetre maxHeight %s és invàlid; es requereix un valor major a %s, el mínim del món", "commands.spreadplayers.failed.teams": "No s'han pogut repartir %s equip(s) al voltant de %s, %s (massa entitats per aquest espai, prova repartint-les com a màxim %s)", "commands.spreadplayers.success.entities": "S'ha(n) escampat %s jugador(s) al voltant de %s, %s, amb una distància mitjana de %s blocs entre ells", "commands.spreadplayers.success.teams": "S'ha(n) escampat %s equip(s) al voltant de %s, %s, amb una distància mitjana de %s blocs entre ells", "commands.stop.stopping": "Aturant el servidor", "commands.stopsound.success.source.any": "<PERSON>'han aturat tots el sons \"%s\"", "commands.stopsound.success.source.sound": "S'ha aturat el so \"%s\" de la font \"%s\"", "commands.stopsound.success.sourceless.any": "<PERSON><PERSON>han aturat tots el sons", "commands.stopsound.success.sourceless.sound": "S'ha aturat el so \"%s\"", "commands.summon.failed": "No s'ha pogut generar l'entitat", "commands.summon.failed.uuid": "No s'ha pogut generar l'entitat degut a UUIDs duplicats", "commands.summon.invalidPosition": "Posició invàlida per a invocar", "commands.summon.success": "S'ha generat l'entitat \"%s\"", "commands.tag.add.failed": "L'objectiu o bé ja té l'etiqueta o en té massa", "commands.tag.add.success.multiple": "S'ha afegit l'etiqueta \"%s\" a %s entitats", "commands.tag.add.success.single": "S'ha afegit l'etiqueta \"%s\" a %s", "commands.tag.list.multiple.empty": "Les %s entitats no tenen etiquetes", "commands.tag.list.multiple.success": "Les %s entitats tenen un total de %s etiquetes: %s", "commands.tag.list.single.empty": "%s no té etiquetes", "commands.tag.list.single.success": "%s té %s etiquetes: %s", "commands.tag.remove.failed": "L'objectiu no té aquesta etiqueta", "commands.tag.remove.success.multiple": "S'ha eliminat l'etiqueta \"%s\" de %s entitats", "commands.tag.remove.success.single": "S'ha eliminat l'etiqueta \"%s\" de %s", "commands.team.add.duplicate": "Ja existeix un equip amb aquest nom", "commands.team.add.success": "S'ha creat l'equip %s", "commands.team.empty.success": "S'han eliminat a %s membre(s) de l'equip %s", "commands.team.empty.unchanged": "Res ha canviat, l'equip ja estava buit", "commands.team.join.success.multiple": "S'han afegit %s membres a l'equip %s", "commands.team.join.success.single": "S'ha afegit a %s a l'equip %s", "commands.team.leave.success.multiple": "S'han eliminat a %s membres dels seus equips", "commands.team.leave.success.single": "S'ha eliminat a %s del seu equip", "commands.team.list.members.empty": "L'equip %s no té membres", "commands.team.list.members.success": "L'equip %s té %s membre(s): %s", "commands.team.list.teams.empty": "No hi ha equips", "commands.team.list.teams.success": "Hi ha %s equip(s): %s", "commands.team.option.collisionRule.success": "S'ha canviat la norma de col·lisió de l'equip %s a \"%s\"", "commands.team.option.collisionRule.unchanged": "Res ha canviat, la norma de col·lisió ja tenia aquest valor", "commands.team.option.color.success": "S'ha canviat el color de l'equip %s a %s", "commands.team.option.color.unchanged": "Res ha canviat, l'equip ja tenia aquest color", "commands.team.option.deathMessageVisibility.success": "S'ha canviat la visibilitat dels avisos de mort per a l'equip %s a \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Res ha canviat, la visualització dels missatges de mort ja tenia aquest valor", "commands.team.option.friendlyfire.alreadyDisabled": "Res ha canviat, el foc amic ja estava desactivat a l'equip", "commands.team.option.friendlyfire.alreadyEnabled": "Res ha canviat, el foc amic ja estava activat a l'equip", "commands.team.option.friendlyfire.disabled": "S'ha desactivat el foc amic a l'equip %s", "commands.team.option.friendlyfire.enabled": "S'ha activat el foc amic a l'equip %s", "commands.team.option.name.success": "S'ha actualitzat el nom de l'equip %s", "commands.team.option.name.unchanged": "Res ha canviat, aquest equip ja tenia aquest nom", "commands.team.option.nametagVisibility.success": "S'ha canviat la visibilitat dels noms de jugadors per a l'equip %s a \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Res ha canviat, la visibilitat dels noms dels jugadors ja tenia aquest valor", "commands.team.option.prefix.success": "S'ha establert el prefix de l'equip a %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Res ha canvia<PERSON>, els membres de l'equip ja no podien veure els seus companys invisibles", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Res ha canviat, aquest equip ja podia veure companys invisibles", "commands.team.option.seeFriendlyInvisibles.disabled": "Des d'ara els membes de l'equip %s no poden veure als seus companys invisibles", "commands.team.option.seeFriendlyInvisibles.enabled": "Des d'ara els membres de l'equip %s poden veure als seus companys invisibles", "commands.team.option.suffix.success": "S'ha establert el sufix de l'equip a %s", "commands.team.remove.success": "S'ha eliminat l'equip %s", "commands.teammsg.failed.noteam": "Has d'estar en un equip per poder enviar-li un missatge", "commands.teleport.invalidPosition": "Posició invàlida per teletransportar-se", "commands.teleport.success.entity.multiple": "%s entitats teletransportades a %s", "commands.teleport.success.entity.single": "Teletransportat %s a %s", "commands.teleport.success.location.multiple": "%s entitats teletransportades a %s, %s, %s", "commands.teleport.success.location.single": "S'ha teletransportat al jugador %s a %s, %s, %s", "commands.test.batch.starting": "Començant entorn %s del lot %s", "commands.test.clear.error.no_tests": "No s'ha pogut trobar cap prova per netejar", "commands.test.clear.success": "S'ha(n) eliminat %s estructures", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Pressiona per copiar al porta-retalls", "commands.test.create.success": "Configuració creada per la prova %s", "commands.test.error.no_test_containing_pos": "No s'ha pogut trobar cap instància de test que contingui, %s, %s, %s", "commands.test.error.no_test_instances": "No s'ha trobat cap instància de test", "commands.test.error.non_existant_test": "No s'ha pogut trobar el test %s", "commands.test.error.structure_not_found": "No s'ha pogut trobar l'estructura de test %s", "commands.test.error.test_instance_not_found": "No s'ha pogut trobar cap bloc de prova", "commands.test.error.test_instance_not_found.position": "No s'ha pogut trobar cap bloc de prova a %s, %s, %s", "commands.test.error.too_large": "La mida de l'estructura ha de ser més petita de %s blocs", "commands.test.locate.done": "S'ha acabat la cerca, s'ha(n) trobat %s estructures", "commands.test.locate.found": "S'ha trobat una estructura a: %s (distància: %s)", "commands.test.locate.started": "Ha començat la cerca d'estructures de prova, això pot trigar un temps...", "commands.test.no_tests": "No hi ha proves per iniciar", "commands.test.relative_position": "Posició relativa a %s: %s", "commands.test.reset.error.no_tests": "No s'ha trobat cap prova per reiniciar", "commands.test.reset.success": "S'ha(n) reiniciat %s estructures", "commands.test.run.no_tests": "No s'han trobat proves", "commands.test.run.running": "Començant %s prova(es)...", "commands.test.summary": "Prova de Joc completada! %s prova(es) estan en funcionament", "commands.test.summary.all_required_passed": "Totes les proves requerides s'han superat :)", "commands.test.summary.failed": "%s proves requerida/es han fallat :(", "commands.test.summary.optional_failed": "%s proves opcional/s ha/n fallat :(", "commands.tick.query.percentiles": "Percentils: P50: %sms P95: %sms P99: %sms, mostra: %s", "commands.tick.query.rate.running": "Taxa de cicle objectiu: %s per segon\nTemps mitjà per cicle: %sms (Objectiu:%sms)", "commands.tick.query.rate.sprinting": "Taxa de cicle objectiu: %s per segon (ignorat, només referència).\nTemps mitjà per cicle: %sms", "commands.tick.rate.success": "Posa la taxa de cicle objectiu a %s per segon", "commands.tick.sprint.report": "Correguda completada amb %s cicles per segon, %s o ms per cicle", "commands.tick.sprint.stop.fail": "No hi ha cicle de córrer en curs", "commands.tick.sprint.stop.success": "Un corregut de cicle s'ha interromput", "commands.tick.status.frozen": "El joc està congelat", "commands.tick.status.lagging": "El joc s'està executant, però no pot mantenir amb la taxa de cicle objectiu", "commands.tick.status.running": "El joc funciona normal", "commands.tick.status.sprinting": "El joc està corrent", "commands.tick.step.fail": "No es pot passar el joc - el joc ha d'estar congelat primer", "commands.tick.step.stop.fail": "No hi ha pas de cicle en curs", "commands.tick.step.stop.success": "Un pas de cicle ha sigut interromput", "commands.tick.step.success": "Passant %s cicle(s)", "commands.time.query": "Hora: %s tics", "commands.time.set": "Temps ajustat a %s", "commands.title.cleared.multiple": "S'han eliminat els títols de %s jugadors", "commands.title.cleared.single": "S'han eliminat els títols de %s", "commands.title.reset.multiple": "S'han reiniciat les opcions de títol per a %s jugadors", "commands.title.reset.single": "S'han reiniciat les opcions de títol per a %s", "commands.title.show.actionbar.multiple": "Mostrant nou títol a la barra d'ítems per a %s jugadors", "commands.title.show.actionbar.single": "Mostrant nou títol a la barra d'ítems de %s", "commands.title.show.subtitle.multiple": "Mostrant nou subtítol per a %s jugadors", "commands.title.show.subtitle.single": "Mostrant nou subtítol per a %s", "commands.title.show.title.multiple": "Mostrant nou títol per a %s jugadors", "commands.title.show.title.single": "Mostrant nou títol per a %s", "commands.title.times.multiple": "S'ha canviat el temps de visualització del títol per a %s jugadors", "commands.title.times.single": "S'ha canviat el temps de visualització del títol per a %s", "commands.transfer.error.no_players": "S'ha d'especificar almenys un jugador per transferir", "commands.transfer.success.multiple": "Transferint %s jugadors a %s:%s", "commands.transfer.success.single": "Transferint %s a %s:%s", "commands.trigger.add.success": "S'ha activat l'activador %s (s'ha afegit %s al valor)", "commands.trigger.failed.invalid": "Només pots activar objectius del tipus \"trigger\"", "commands.trigger.failed.unprimed": "Encara no pots activar aquest objectiu activador", "commands.trigger.set.success": "S'ha activat l'activador %s (s'ha canviat el valor a %s)", "commands.trigger.simple.success": "S'ha activat l'activador %s", "commands.version.build_time": "temps_de_construcció = %s", "commands.version.data": "Dades = %s", "commands.version.header": "Informació de la versió:", "commands.version.id": "id = %s", "commands.version.name": "nom %s", "commands.version.pack.data": "dades_del_paquet = %s", "commands.version.pack.resource": "font_del_paquet = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "estable = no", "commands.version.stable.yes": "estable = sí", "commands.waypoint.list.empty": "No punts de referènia a %s", "commands.waypoint.list.success": "%s punt(s) de referència a %s: %s", "commands.waypoint.modify.color": "El color del punt de referència és ara %s", "commands.waypoint.modify.color.reset": "Reiniciar el color del punt de referència", "commands.waypoint.modify.style": "L'estil del punt de referència s'ha canviat", "commands.weather.set.clear": "S'ha canviat el temps a clar", "commands.weather.set.rain": "S'ha canviat el temps a pluja", "commands.weather.set.thunder": "S'ha canviat el temps a tempesta", "commands.whitelist.add.failed": "El jugador ja estava a la llista blanca", "commands.whitelist.add.success": "S'ha afegit %s a la llista blanca", "commands.whitelist.alreadyOff": "La llista blanca ja estava desactivada", "commands.whitelist.alreadyOn": "La llista blanca ja estava activada", "commands.whitelist.disabled": "S'ha desactivat la llista blanca", "commands.whitelist.enabled": "S'ha activat la llista blanca", "commands.whitelist.list": "Hi ha %s jugador(s) a la llista blanca: %s", "commands.whitelist.none": "No hi ha jugadors a la llista blanca", "commands.whitelist.reloaded": "Llista blanca recarregada", "commands.whitelist.remove.failed": "El jugador no estava a la lista blanca", "commands.whitelist.remove.success": "S'ha esborrat %s de la llista blanca", "commands.worldborder.center.failed": "Res ha canviat, el centre del límit del món ja estava en aquesta posició", "commands.worldborder.center.success": "S'ha canviat el centre del límit del món a %s, %s", "commands.worldborder.damage.amount.failed": "Res ha canviat, el dany del límit del món ja era aquesta quantitat", "commands.worldborder.damage.amount.success": "S'ha establert la quantitat de dany fora del límit del món a %s per bloc cada segon", "commands.worldborder.damage.buffer.failed": "Res ha canviat, la zona segura fora del món ja estava a aquesta distància", "commands.worldborder.damage.buffer.success": "S'ha canviat la zona segura fora del límit del món a %s bloc(s)", "commands.worldborder.get": "El límit del món és actualment de %s bloc(s) d'ample", "commands.worldborder.set.failed.big": "El límit del món no pot ser més llarg de %s blocs", "commands.worldborder.set.failed.far": "El límit del món no pot superar els %s blocs de distància", "commands.worldborder.set.failed.nochange": "Res ha canviat, el límit del món ja tenia aquest tamany", "commands.worldborder.set.failed.small": "El límit del món no pot ser més petit d'un bloc", "commands.worldborder.set.grow": "Augmentant el límit del món a %s blocs durant %s segons", "commands.worldborder.set.immediate": "S'ha canviat el límit del món a %s bloc(s) d'ample", "commands.worldborder.set.shrink": "Reduint el límit del món a %s bloc(s) durant %s segon(s)", "commands.worldborder.warning.distance.failed": "Res ha canviat, l'avís del límit del món ja tenia aquesta distància", "commands.worldborder.warning.distance.success": "S'ha canviat la distància per avisar del límit del món a %s bloc(s)", "commands.worldborder.warning.time.failed": "Res ha <PERSON>via<PERSON>, l'avís del límit del món ja durava aquests segons", "commands.worldborder.warning.time.success": "S'ha canviat el temps d'avís del límit del món a %s segon(s)", "compliance.playtime.greaterThan24Hours": "Fa més de 24 hores que jugues", "compliance.playtime.hours": "Fa %s hora/es que jugues", "compliance.playtime.message": "Jugar de forma excessiva pot interferir amb la vida diària", "connect.aborted": "Connexió cancel·lada", "connect.authorizing": "Identificant-se...", "connect.connecting": "Connectant amb el servidor ...", "connect.encrypting": "Encriptant...", "connect.failed": "Error en connectar amb el servidor", "connect.failed.transfer": "La connexió ha fallat mentre es transferia al servidor", "connect.joining": "Entrant al món...", "connect.negotiating": "Connectant...", "connect.reconfiging": "Reconfigurant...", "connect.reconfiguring": "Reconfigurant...", "connect.transferring": "Transferint a un nou servidor...", "container.barrel": "Barril", "container.beacon": "Far", "container.beehive.bees": "Abelles: %s/%s", "container.beehive.honey": "Mel: %s/%s", "container.blast_furnace": "Alt forn", "container.brewing": "Altar de pocions", "container.cartography_table": "Taula de cartografia", "container.chest": "Cofre", "container.chestDouble": "Cofre gran", "container.crafter": "Elaborador", "container.crafting": "Elaboració", "container.creative": "<PERSON><PERSON><PERSON><PERSON>", "container.dispenser": "Dispensador", "container.dropper": "Subministrador", "container.enchant": "Encantar", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s trossos de lapislàtzuli", "container.enchant.lapis.one": "1 tros de lapislàtzuli", "container.enchant.level.many": "%s nivells d'encanteri", "container.enchant.level.one": "1 nivell d'encanteri", "container.enchant.level.requirement": "Nivell requerit: %s", "container.enderchest": "<PERSON><PERSON><PERSON>", "container.furnace": "Forn", "container.grindstone_title": "Reparar i desencantar", "container.hopper": "Tremuja d'ítems", "container.inventory": "Inventari", "container.isLocked": "%s està bloquejat!", "container.lectern": "Faristol", "container.loom": "Teler", "container.repair": "Reparar i renombrar", "container.repair.cost": "Cost de l'encanteri: %1$s", "container.repair.expensive": "Massa car!", "container.shulkerBox": "<PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "i %s més...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "No es pot obrir. El botí encara no s'ha generat.", "container.stonecutter": "Talla-roques", "container.upgrade": "Millorar equipament", "container.upgrade.error_tooltip": "L'ítem no es pot millorar d'aquesta manera", "container.upgrade.missing_template_tooltip": "Afegeix un patró de ferreria", "controls.keybinds": "Controls...", "controls.keybinds.duplicateKeybinds": "Aquesta tecla també s'utilitza per:\n%s", "controls.keybinds.title": "Controls", "controls.reset": "Reiniciar", "controls.resetAll": "Reiniciar tecles", "controls.title": "Controls", "createWorld.customize.buffet.biome": "Selecciona un bioma", "createWorld.customize.buffet.title": "<PERSON><PERSON><PERSON> món bufet", "createWorld.customize.flat.height": "Alçada", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Límit inferior -%s", "createWorld.customize.flat.layer.top": "Límit superior - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON><PERSON> capa", "createWorld.customize.flat.tile": "Material de la capa", "createWorld.customize.flat.title": "Personalització del món extraplà", "createWorld.customize.presets": "Predeterminats", "createWorld.customize.presets.list": "Com alternativa, aquí n'hi han alguns que hem fet per a tu!", "createWorld.customize.presets.select": "Fes servir el predeterminat", "createWorld.customize.presets.share": "Voleu compartir la vostra configuració personalitzada amb algú? Utilitzeu la casella següent!", "createWorld.customize.presets.title": "Selecciona un tipus de món predeterminat", "createWorld.preparing": "Preparant la generació del món...", "createWorld.tab.game.title": "<PERSON><PERSON>", "createWorld.tab.more.title": "Més", "createWorld.tab.world.title": "Món", "credits_and_attribution.button.attribution": "Atribució", "credits_and_attribution.button.credits": "Crè<PERSON><PERSON>", "credits_and_attribution.button.licenses": "Llicències", "credits_and_attribution.screen.title": "Crèdits i atribucions", "dataPack.bundle.description": "Afegeix l'objecte experimental \"sarró\"", "dataPack.bundle.name": "<PERSON><PERSON><PERSON>", "dataPack.locator_bar.description": "Mostrar la direcció dels altres jugador en multijugador", "dataPack.locator_bar.name": "Barra Localitzadora", "dataPack.minecart_improvements.description": "Moviment millorat per a les vagonetes", "dataPack.minecart_improvements.name": "Millores per a les vagonetes", "dataPack.redstone_experiments.description": "Canvis experimentals de redstone", "dataPack.redstone_experiments.name": "Experiments pedra vermella", "dataPack.title": "Selecciona paquets de dades", "dataPack.trade_rebalance.description": "Intercanvis actualitzats pels vilatans", "dataPack.trade_rebalance.name": "Reequilibri d'intercanvis dels vilatans", "dataPack.update_1_20.description": "Noves característiques i contingut per a Minecraft 1.20", "dataPack.update_1_20.name": "Actualització 1.20", "dataPack.update_1_21.description": "Noves característiques i contingut per a Minecraft 1.21", "dataPack.update_1_21.name": "Actualització 1.21", "dataPack.validation.back": "<PERSON><PERSON>", "dataPack.validation.failed": "No s'han pogut validar els paquets de dades!", "dataPack.validation.reset": "<PERSON><PERSON><PERSON><PERSON>", "dataPack.validation.working": "S'estan validant els paquets de dades seleccionats...", "dataPack.vanilla.description": "Les dades per defecte de Minecraft", "dataPack.vanilla.name": "Per defecte", "dataPack.winter_drop.description": "Nou contingut a l'edició del Ivern", "dataPack.winter_drop.name": "Entrega del ivern", "datapackFailure.safeMode": "Mode segur", "datapackFailure.safeMode.failed.description": "Aquest món conté dades invàlides o corruptes.", "datapackFailure.safeMode.failed.title": "No s'ha pogut carregar el món en mode segur.", "datapackFailure.title": "Els errors dels paquets de dades seleccionats han impedit que el món es carregui. Pots intentar carregar-lo únicament amb el paquet de dades per defecte (\"mode segur\"), o tornar al menú principal i arreglar-ho manualment.", "death.attack.anvil": "%1$s ha estat aixafat per una enclusa", "death.attack.anvil.player": "A %1$s li ha caigut una enclusa mentre lluitava amb %2$s", "death.attack.arrow": "%2$s ha disparat a %1$s", "death.attack.arrow.item": "%2$s ha disparat a %1$s utilitzant %3$s", "death.attack.badRespawnPoint.link": "Diseny intencional del joc", "death.attack.badRespawnPoint.message": "%1$s ha sigut assassinat/da per %2$s", "death.attack.cactus": "%1$s s'ha topat amb un cactus", "death.attack.cactus.player": "%1$s s'ha topat amb un cactus mentre fugia de %2$s", "death.attack.cramming": "%1$s ha estat aplanat", "death.attack.cramming.player": "%1$s ha sigut aixafat/da per %2$s", "death.attack.dragonBreath": "%1$s ha sigut calcinat per l'alè del drac", "death.attack.dragonBreath.player": "%1$s ha sigut rostit per l'alè de drac per %2$s", "death.attack.drown": "%1$s s'ha ofegat", "death.attack.drown.player": "%1$s s'ha ofegat mentre fugia de %2$s", "death.attack.dryout": "%1$s ha mort de deshidratació", "death.attack.dryout.player": "%1$s ha mort de deshidratació mentre fugia de %2$s", "death.attack.even_more_magic": "%1$s ha mort per molt més art de màgia", "death.attack.explosion": "%1$s ha volat pels aires", "death.attack.explosion.player": "%2$s ha fet volar pels aires a %1$s", "death.attack.explosion.player.item": "%1$s ha explotat per %2$s utilitzant %3$s", "death.attack.fall": "%1$s s'ha estavellat contra el terra", "death.attack.fall.player": "%1$s ha colpejat fortament el terra mentre fugia de %2$s", "death.attack.fallingBlock": "%1$s ha estat aixafat per un bloc que queia", "death.attack.fallingBlock.player": "A %1$s li ha caigut un bloc mentre lluitava amb %2$s", "death.attack.fallingStalactite": "%1$s ha estat travessat per una estalactita", "death.attack.fallingStalactite.player": "%1$s era enfilat per una estalactita mentre lluitava amb %2$s", "death.attack.fireball": "%2$s li ha llançat una bola de foc a %1$s", "death.attack.fireball.item": "%2$s li ha llançat una bola de foc a %1$s utilitzant %3$s", "death.attack.fireworks": "%1$s ha fet un pet com una gla", "death.attack.fireworks.item": "%1$s ha esclatat a causa d'un foc artificial disparat amb %3$s per %2$s", "death.attack.fireworks.player": "%1$s ha fet un pet com una gla mentre lluitava amb %2$s", "death.attack.flyIntoWall": "%1$s ha experimentat l'energia cinètica", "death.attack.flyIntoWall.player": "%1$s ha experimentat l'energia cinètica mentre fugia de %2$s", "death.attack.freeze": "%1$s ha mort d'hipotèrmia", "death.attack.freeze.player": "%2$s ha congelat a %1$s fins que ha mort", "death.attack.generic": "%1$s ha mort", "death.attack.generic.player": "%1$s ha mort per %2$s", "death.attack.genericKill": "%1$s ha estat assassinat", "death.attack.genericKill.player": "%1$s ha estat assassinat mentre lluitava amb %2$s", "death.attack.hotFloor": "%1$s ha descobert que el terra era lava", "death.attack.hotFloor.player": "%1$s ha entrat en una zona perillosa a causa de %2$s", "death.attack.inFire": "%1$s ha cremat fins a les cendres", "death.attack.inFire.player": "%1$s ha caminat sobre foc mentre lluitava amb %2$s", "death.attack.inWall": "%1$s s'ha asfixiat", "death.attack.inWall.player": "%1$s s'ha asfixiat en una paret mentre lluitava amb %2$s", "death.attack.indirectMagic": "%1$s ha estat assassinat per %2$s utilitzant màgia", "death.attack.indirectMagic.item": "%1$s ha estat assassinat per %2$s utilitzant %3$s", "death.attack.lava": "%1$s ha provat de nadar en lava", "death.attack.lava.player": "%1$s ha provat de nadar en lava mentre fugia de %2$s", "death.attack.lightningBolt": "%1$s ha sigut rostit per un llamp", "death.attack.lightningBolt.player": "A %1$s li ha caigut un llamp mentre lluitava contra %2$s", "death.attack.mace_smash": "Vas ser aixafat per ", "death.attack.mace_smash.item": "Vas ser aixafat per %2$s amb", "death.attack.magic": "%1$s ha estat assassinat amb màgia", "death.attack.magic.player": "%1$s ha estat assassinat amb màgia mentre fugia de %2$s", "death.attack.message_too_long": "Lamentablement, aquest missatge era massa llarg per ser enviat. Ho sentim! Aquí tens la versió abreujada: %s", "death.attack.mob": "%1$s ha estat assassinat per %2$s", "death.attack.mob.item": "%1$s ha estat assassinat per %2$s utilitzant %3$s", "death.attack.onFire": "%1$s ha cremat fins a la mort", "death.attack.onFire.item": "%1$s ha cremat fins a les cendres mentre lluitava amb %2$s utilitzant %3$s", "death.attack.onFire.player": "%1$s s'ha cremat fins a les cendres mentre lluitava amb %2$s", "death.attack.outOfWorld": "%1$s ha caigut del món", "death.attack.outOfWorld.player": "%1$s no volia viure en el mateix món que %2$s", "death.attack.outsideBorder": "%1$s s'ha anat més enllà dels confins d'aquest món", "death.attack.outsideBorder.player": "%1$s ha anat més enllà dels confins d'aquest món mentre lluitava amb %2$s", "death.attack.player": "%1$s ha estat assassinat per %2$s", "death.attack.player.item": "%1$s ha estat assassinat per %2$s utilitzant %3$s", "death.attack.sonic_boom": "%1$s ha estat obliterat amb un bram", "death.attack.sonic_boom.item": "%1$s ha estat obliterat amb un bram mentre fugia de %2$s amb %3$s", "death.attack.sonic_boom.player": "%1$s ha estat obliterat amb un bram mentre fugia de %2$s", "death.attack.stalagmite": "%1$s ha estat empalat amb una estalagmita", "death.attack.stalagmite.player": "%1$s s'ha empalat en una estalagmita mentre lluitava amb %2$s", "death.attack.starve": "%1$s ha mort de gana", "death.attack.starve.player": "%1$s s'ha mort de gana mentre lluitava amb %2$s", "death.attack.sting": "%1$s ha mort per una picada d'abella", "death.attack.sting.item": "%2$s ha picat a %1$s fins a la mort utilitzant %3$s", "death.attack.sting.player": "%1$s ha mort per una picada de %2$s", "death.attack.sweetBerryBush": "%1$s ha mort entre espines en un arbust de baies dolces", "death.attack.sweetBerryBush.player": "%1$s ha mort entre espines en un arbust de baies dolces mentre fugia de %2$s", "death.attack.thorns": "%1$s ha estat assassinat provant de ferir a %2$s", "death.attack.thorns.item": "%1$s era assassinat per %3$s mentre provava ferir a %2$s", "death.attack.thrown": "%1$s ha estat colpejat per %2$s", "death.attack.thrown.item": "%1$s ha estat colpejat per %2$s utilitzant %3$s", "death.attack.trident": "%1$s ha sigut empalat/da per %2$s", "death.attack.trident.item": "%1$s ha sigut empalat/da per %2$s amb %3$s", "death.attack.wither": "A %1$s se li ha pansit l'ànima", "death.attack.wither.player": "A %1$s se li ha pansit l'ànima mentre lluitava amb %2$s", "death.attack.witherSkull": "%1$s ha estat encertat per un crani de %2$s", "death.attack.witherSkull.item": "%1$s ha estat encertat per un crani de %2$s utilitzant %3$s", "death.fell.accident.generic": "%1$s ha caigut d'un lloc alt", "death.fell.accident.ladder": "%1$s ha caigut d'una escala", "death.fell.accident.other_climbable": "%1$s no sap escalar", "death.fell.accident.scaffolding": "%1$s ha caigut d'una bastida", "death.fell.accident.twisting_vines": "%1$s ha caigut d'unes lianes retorçades", "death.fell.accident.vines": "%1$s ha caigut d'unes lianes", "death.fell.accident.weeping_vines": "%1$s s'ha oblidat d'agafar-se a les lianes", "death.fell.assist": "%1$s ha estat condemnat a caure per %2$s", "death.fell.assist.item": "%1$s ha estat condemnat a caure per %2$s utilitzant %3$s", "death.fell.finish": "%1$s ha caigut i ha estat rematat per %2$s", "death.fell.finish.item": "%1$s ha caigut i ha estat rematat per %2$s utilitzant %3$s", "death.fell.killer": "%1$s ha estat condemnat a caure", "deathScreen.quit.confirm": "Est<PERSON>s segur de voler sortir?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathScreen.score": "Puntuació", "deathScreen.score.value": "Puntuació: %s", "deathScreen.spectate": "Fer d'espectador", "deathScreen.title": "Has mort!", "deathScreen.title.hardcore": "Fi de la partida!", "deathScreen.titleScreen": "<PERSON><PERSON> principal", "debug.advanced_tooltips.help": "F3 + H = Descripcions avançades", "debug.advanced_tooltips.off": "Descripcions avançades: amagades", "debug.advanced_tooltips.on": "Descripcions avançades: visibles", "debug.chunk_boundaries.help": "F3 + G = Mostra els límits del chunk", "debug.chunk_boundaries.off": "Límits dels chunks: amagats", "debug.chunk_boundaries.on": "Límits dels chunks: visibles", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON>a xat", "debug.copy_location.help": "F3 + C = Copiar la ubicació com el comandament /tp. <PERSON>ten-les presionades per forçar el tancament del joc", "debug.copy_location.message": "La posició s'ha copiat al portapapers", "debug.crash.message": "Estàs presionant F3 + C. Si no les deixes anar, el joc deixarà de funcionar.", "debug.crash.warning": "El joc deixarà de funcionar en %s...", "debug.creative_spectator.error": "No tens permís per canviar el mode de joc", "debug.creative_spectator.help": "F3 + N = <PERSON>via entre el mode de joc anterior <-> espectador", "debug.dump_dynamic_textures": "S'han desat les textures dinàmiques a %s", "debug.dump_dynamic_textures.help": "F3 + S = Abocament de textures dinàmiques", "debug.gamemodes.error": "No tens permís per obrir el seleccionador de mode de joc", "debug.gamemodes.help": "F3 + F4 = Obre el seleccionador de modes de joc", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s següent", "debug.help.help": "F3 + Q = Mostrar aquesta llista", "debug.help.message": "Tecles:", "debug.inspect.client.block": "S'han copiat les dades del bloc al portapapers", "debug.inspect.client.entity": "S'han copiat les dades de l'entitat al portapapers", "debug.inspect.help": "F3 + I = Copiar dades d'entitat o bloc al portapapers", "debug.inspect.server.block": "S'han copiat les dades del bloc al portapapers", "debug.inspect.server.entity": "S'han copiat les dades de l'entitat al portapapers", "debug.pause.help": "F3 + Esc = Aturar sense menú de pausa (si el pausat és possible)", "debug.pause_focus.help": "F3 + P = Atura el joc al canviar de finestra", "debug.pause_focus.off": "Aturar al canviar de finestra: desactivat", "debug.pause_focus.on": "Aturar al canviar de finestra: activat", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Inicia/atura l'anàlisi", "debug.profiling.start": "<PERSON><PERSON><PERSON>i iniciat, durarà %s segons. Prem F3 + L per aturar-lo prematurament", "debug.profiling.stop": "Anàlisi finalitzada. Els resultats s'han desat a %s", "debug.reload_chunks.help": "F3 + A = Recarrega els chunks", "debug.reload_chunks.message": "Recarregant tots els chunks", "debug.reload_resourcepacks.help": "F3 + T = <PERSON>car<PERSON>gar paquets de recursos", "debug.reload_resourcepacks.message": "Paquets de recursos recarregats", "debug.show_hitboxes.help": "F3 + B = Mostrar caixes de col·lisió", "debug.show_hitboxes.off": "Caixes de col·lisió: ocultes", "debug.show_hitboxes.on": "Caixes de col·lisió: visibles", "debug.version.header": "Informació de la versió del client", "debug.version.help": "F3 + V = Informació de la versió del client", "demo.day.1": "Aquesta demostració durarà 5 dies del joc, aprofita!", "demo.day.2": "Segon dia", "demo.day.3": "<PERSON><PERSON>er dia", "demo.day.4": "Quart dia", "demo.day.5": "Aquest és el teu últim dia!", "demo.day.6": "El cinquè dia ha acabat, utilitza %s per guardar una captura de pantalla de la teva creació.", "demo.day.warning": "Se t'acaba el temps!", "demo.demoExpired": "S'ha acabat el temps de demostració!", "demo.help.buy": "Compra'l ara!", "demo.help.fullWrapped": "Aquesta demostració durarà 5 dies del joc (aproximadament 1 hora i 40 minuts de temps real). Comprova els avenços per guiar-te! Diverteix-te!", "demo.help.inventory": "Utilitza %1$s per obrir l'inventari", "demo.help.jump": "Prem %1$s per saltar", "demo.help.later": "Continua jugant!", "demo.help.movement": "Utilitza %1$s, %2$s, %3$s, %4$s i el ratolí per desplaçar-te", "demo.help.movementMouse": "Mira al teu voltant amb el ratolí.", "demo.help.movementShort": "Mou-te amb %1$s, %2$s, %3$s i %4$s", "demo.help.title": "Minecraft mode demostració", "demo.remainingTime": "Temps restant: %s", "demo.reminder": "El temps de demostració s'ha acabat. Compra el joc per a continuar o començar un món nou!", "difficulty.lock.question": "Esteu segur que voleu bloquejar la dificultat d'aquest món? Això farà que aquest món sempre estigui en %1$s, i mai podràs canviar-ho.", "difficulty.lock.title": "Bloqueja la dificultat del món", "disconnect.endOfStream": "Fi del flux de dades", "disconnect.exceeded_packet_rate": "Expulsat per excedir el límit de freqüència de paquets", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignorant sol·licitud d'estat", "disconnect.loginFailedInfo": "Error a l'iniciar sessió: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "El mode multijugador està desactivat. Si us plau, comprova la configuració del teu compte de Microsoft.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON><PERSON> (Prova de reiniciar el joc i el llançador)", "disconnect.loginFailedInfo.serversUnavailable": "Els servidors d'autenticació no estan disponibles. <PERSON>na a intentar-ho.", "disconnect.loginFailedInfo.userBanned": "Has estat bandejat del joc en línia", "disconnect.lost": "Connexió perduda", "disconnect.packetError": "Error del protocol de xarxa", "disconnect.spam": "Expulsat per fer spam", "disconnect.timeout": "Temps d'espera esgotat", "disconnect.transfer": "Transferit a un nou servidor", "disconnect.unknownHost": "Amfitrió desconegut", "download.pack.failed": "%s de %s paquets no s'han pogut descarregar", "download.pack.progress.bytes": "Progrés: %s (mida total desconeguda)", "download.pack.progress.percent": "Progrés: %s%%", "download.pack.title": "Descarregant paquet de recursos %s/%s", "editGamerule.default": "Per defecte: %s", "editGamerule.title": "Edita les normes del joc", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.bad_omen": "<PERSON> presagi", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Canalització", "effect.minecraft.darkness": "Foscor", "effect.minecraft.dolphins_grace": "Gràcia de dofí", "effect.minecraft.fire_resistance": "Resistència ígnia", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON>", "effect.minecraft.haste": "Pressa", "effect.minecraft.health_boost": "Salut millorada", "effect.minecraft.hero_of_the_village": "Heroi del poble", "effect.minecraft.hunger": "Gana", "effect.minecraft.infested": "Infestació", "effect.minecraft.instant_damage": "Dany instantani", "effect.minecraft.instant_health": "Curació instantània", "effect.minecraft.invisibility": "Invisibilitat", "effect.minecraft.jump_boost": "Salt", "effect.minecraft.levitation": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.luck": "Sort", "effect.minecraft.mining_fatigue": "Fatiga", "effect.minecraft.nausea": "Nàusea", "effect.minecraft.night_vision": "Visió nocturna", "effect.minecraft.oozing": "Supura", "effect.minecraft.poison": "Verí", "effect.minecraft.raid_omen": "<PERSON><PERSON><PERSON> de setge", "effect.minecraft.regeneration": "Regeneració", "effect.minecraft.resistance": "Resistència", "effect.minecraft.saturation": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON><PERSON> lenta", "effect.minecraft.slowness": "Lentitud", "effect.minecraft.speed": "Velocitat", "effect.minecraft.strength": "Força", "effect.minecraft.trial_omen": "Presagi de reptes", "effect.minecraft.unluck": "Mala sort", "effect.minecraft.water_breathing": "Respiració aquàtica", "effect.minecraft.weakness": "Debilitat", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "<PERSON><PERSON> carregat", "effect.minecraft.wither": "<PERSON>er", "effect.none": "Sense efectes", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Aiguafinitat", "enchantment.minecraft.bane_of_arthropods": "<PERSON><PERSON> artròpode", "enchantment.minecraft.binding_curse": "Maledicció de vinculació", "enchantment.minecraft.blast_protection": "Anti-explosió", "enchantment.minecraft.breach": "Fissura", "enchantment.minecraft.channeling": "Conductivitat", "enchantment.minecraft.density": "Densitat", "enchantment.minecraft.depth_strider": "Aletes de peix", "enchantment.minecraft.efficiency": "Eficiència", "enchantment.minecraft.feather_falling": "<PERSON><PERSON> ploma", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON><PERSON> de foc", "enchantment.minecraft.fire_protection": "Protecció ígnia", "enchantment.minecraft.flame": "Flama", "enchantment.minecraft.fortune": "Fortuna", "enchantment.minecraft.frost_walker": "<PERSON><PERSON> congel<PERSON>", "enchantment.minecraft.impaling": "Empalament", "enchantment.minecraft.infinity": "Infinitat", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.loyalty": "Lleialtat", "enchantment.minecraft.luck_of_the_sea": "La sort de la mar", "enchantment.minecraft.lure": "Esquer", "enchantment.minecraft.mending": "Reparació", "enchantment.minecraft.multishot": "Multitret", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "Força", "enchantment.minecraft.projectile_protection": "Anti-projectil", "enchantment.minecraft.protection": "Protecció", "enchantment.minecraft.punch": "Empent<PERSON>", "enchantment.minecraft.quick_charge": "Càrrega ràpida", "enchantment.minecraft.respiration": "Respiració", "enchantment.minecraft.riptide": "Propulsió aquàtica", "enchantment.minecraft.sharpness": "Fulla esmolada", "enchantment.minecraft.silk_touch": "Toc de seda", "enchantment.minecraft.smite": "Exorcisme", "enchantment.minecraft.soul_speed": "Velocitat d'ànima", "enchantment.minecraft.sweeping": "Fil desolador", "enchantment.minecraft.sweeping_edge": "Fil desolador", "enchantment.minecraft.swift_sneak": "Ajupiment lleuger", "enchantment.minecraft.thorns": "Espines", "enchantment.minecraft.unbreaking": "Durabilitat", "enchantment.minecraft.vanishing_curse": "Maledicció de desaparició", "enchantment.minecraft.wind_burst": "Esclata de vent", "entity.minecraft.acacia_boat": "El vaixell fet d'acàcia", "entity.minecraft.acacia_chest_boat": "El vaixell fet d'acàcia amb un cofre", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Núvol d'efecte d'àrea", "entity.minecraft.armadillo": "Armadillo", "entity.minecraft.armor_stand": "Maniquí", "entity.minecraft.arrow": "Fletxa", "entity.minecraft.axolotl": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bamboo_chest_raft": "Rai de bambú amb cofre", "entity.minecraft.bamboo_raft": "Rai de bamb<PERSON>", "entity.minecraft.bat": "Ratpenat", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "Barca de bedoll", "entity.minecraft.birch_chest_boat": "Barca de bedoll amb cofre", "entity.minecraft.blaze": "Esperit del foc", "entity.minecraft.block_display": "Mostra el bloc", "entity.minecraft.boat": "Barca", "entity.minecraft.bogged": "Aiguamoll", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "<PERSON><PERSON><PERSON><PERSON> ventosa", "entity.minecraft.camel": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cat": "Gat", "entity.minecraft.cave_spider": "Aranya de cova", "entity.minecraft.cherry_boat": "Barca de cirerer", "entity.minecraft.cherry_chest_boat": "Barca de cirerer amb cofre", "entity.minecraft.chest_boat": "Barca de bedoll amb cofre", "entity.minecraft.chest_minecart": "Vagoneta amb cofre", "entity.minecraft.chicken": "Pollastre", "entity.minecraft.cod": "Bacallà", "entity.minecraft.command_block_minecart": "Vagoneta amb bloc de comandaments", "entity.minecraft.cow": "Vaca", "entity.minecraft.creaking": "Cruixidor", "entity.minecraft.creaking_transient": "Cruixidor", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Barca de pomer fosc", "entity.minecraft.dark_oak_chest_boat": "Barca de pomer fosc amb cofre", "entity.minecraft.dolphin": "Dofí", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Bola de foc de drac", "entity.minecraft.drowned": "Ofegat", "entity.minecraft.egg": "<PERSON><PERSON>", "entity.minecraft.elder_guardian": "Guardià an<PERSON>", "entity.minecraft.end_crystal": "Cristall de l'End", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Invocador", "entity.minecraft.evoker_fangs": "Ullals d'invocador", "entity.minecraft.experience_bottle": "Ampolla màgica llençada", "entity.minecraft.experience_orb": "Orbe d'experiència ", "entity.minecraft.eye_of_ender": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "entity.minecraft.falling_block": "Bloc caient", "entity.minecraft.falling_block_type": "%s caient", "entity.minecraft.fireball": "Bola de foc", "entity.minecraft.firework_rocket": "Focs artificials", "entity.minecraft.fishing_bobber": "Ham", "entity.minecraft.fox": "<PERSON><PERSON><PERSON>", "entity.minecraft.frog": "Granota", "entity.minecraft.furnace_minecart": "Vagoneta amb forn", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Gegant", "entity.minecraft.glow_item_frame": "<PERSON>", "entity.minecraft.glow_squid": "Calamar <PERSON>", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Guardià", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Vagoneta amb tremuja", "entity.minecraft.horse": "Cavall", "entity.minecraft.husk": "Zomb<PERSON> ressec", "entity.minecraft.illusioner": "Il·lusionista", "entity.minecraft.interaction": "Interacció", "entity.minecraft.iron_golem": "Gòlem de ferro", "entity.minecraft.item": "Ítem", "entity.minecraft.item_display": "Mostra-ítem", "entity.minecraft.item_frame": "<PERSON>", "entity.minecraft.jungle_boat": "Barca de j<PERSON>la", "entity.minecraft.jungle_chest_boat": "Barca de jungla amb cofre", "entity.minecraft.killer_bunny": "El conill assassí", "entity.minecraft.leash_knot": "<PERSON><PERSON> <PERSON> corre<PERSON>", "entity.minecraft.lightning_bolt": "Llamp", "entity.minecraft.lingering_potion": "<PERSON>ci<PERSON>", "entity.minecraft.llama": "Llama", "entity.minecraft.llama_spit": "Escopinada de llama", "entity.minecraft.magma_cube": "Cub de magma", "entity.minecraft.mangrove_boat": "Barca de mangle", "entity.minecraft.mangrove_chest_boat": "Barca de mangle amb cofre", "entity.minecraft.marker": "Marcador", "entity.minecraft.minecart": "Vagoneta", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "Barca de pomer", "entity.minecraft.oak_chest_boat": "Barca de pomer amb cofre", "entity.minecraft.ocelot": "Ocelot", "entity.minecraft.ominous_item_spawner": "Generador d'ítems ominós", "entity.minecraft.painting": "Quadre", "entity.minecraft.pale_oak_boat": "Barca de pomer pàl·lid", "entity.minecraft.pale_oak_chest_boat": "Barca de pomer pàl·lid cofre", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Lloro", "entity.minecraft.phantom": "Fantasma", "entity.minecraft.pig": "Porc", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON>lin ferotge", "entity.minecraft.pillager": "Saquejador", "entity.minecraft.player": "Jugador", "entity.minecraft.polar_bear": "<PERSON><PERSON>", "entity.minecraft.potion": "Poció", "entity.minecraft.pufferfish": "Peix globus", "entity.minecraft.rabbit": "<PERSON><PERSON>", "entity.minecraft.ravager": "Devastador", "entity.minecraft.salmon": "<PERSON><PERSON><PERSON>", "entity.minecraft.sheep": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON>", "entity.minecraft.silverfish": "Peixet de plata", "entity.minecraft.skeleton": "E<PERSON><PERSON>", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.slime": "Llim", "entity.minecraft.small_fireball": "Bola de foc petita", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Gòlem de neu", "entity.minecraft.snowball": "<PERSON><PERSON>u", "entity.minecraft.spawner_minecart": "Vagoneta amb generador de monstres", "entity.minecraft.spectral_arrow": "Fletxa espectral", "entity.minecraft.spider": "Aranya", "entity.minecraft.splash_potion": "Poció llancívola", "entity.minecraft.spruce_boat": "Barca de pi", "entity.minecraft.spruce_chest_boat": "Barca de pi amb cofre", "entity.minecraft.squid": "Calamar", "entity.minecraft.stray": "Esquelet extraviat", "entity.minecraft.strider": "Caminant", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Mostra-text", "entity.minecraft.tnt": "<PERSON><PERSON><PERSON> activada", "entity.minecraft.tnt_minecart": "Vagoneta amb dinamita", "entity.minecraft.trader_llama": "Llama ambulant", "entity.minecraft.trident": "Trident", "entity.minecraft.tropical_fish": "Peix tropical", "entity.minecraft.tropical_fish.predefined.0": "Anemone", "entity.minecraft.tropical_fish.predefined.1": "Peix cirurgià negre", "entity.minecraft.tropical_fish.predefined.10": "Ídol morú", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON><PERSON> papal<PERSON>a adornat", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON><PERSON><PERSON> ciliaris", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON><PERSON> vermell", "entity.minecraft.tropical_fish.predefined.15": "Ophioblen<PERSON> atlanticus", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON> vermell", "entity.minecraft.tropical_fish.predefined.17": "Barbut", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON><PERSON> p<PERSON>", "entity.minecraft.tropical_fish.predefined.19": "Peix ballesta", "entity.minecraft.tropical_fish.predefined.2": "Pex cirurgià blau", "entity.minecraft.tropical_fish.predefined.20": "<PERSON><PERSON>x ll<PERSON> de cua groga", "entity.minecraft.tropical_fish.predefined.21": "Peix cirurgià groc", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "Cíclid", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON> p<PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Lluitador de Siam rosa", "entity.minecraft.tropical_fish.predefined.7": "Pseudochromidae", "entity.minecraft.tropical_fish.predefined.8": "<PERSON><PERSON><PERSON> vermell emperador", "entity.minecraft.tropical_fish.predefined.9": "Múl·lid", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Peix bloc", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "Saltarí", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "D'escull", "entity.minecraft.tropical_fish.type.spotty": "Tacat", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.turtle": "Tortuga", "entity.minecraft.vex": "Esperit", "entity.minecraft.villager": "Vilatà", "entity.minecraft.villager.armorer": "<PERSON><PERSON>", "entity.minecraft.villager.butcher": "Carnisser", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "<PERSON><PERSON>", "entity.minecraft.villager.fisherman": "Pescador", "entity.minecraft.villager.fletcher": "Fletxer", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliotecari", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "Sapastre", "entity.minecraft.villager.none": "Vilatà", "entity.minecraft.villager.shepherd": "Pastor", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "Esmolet", "entity.minecraft.vindicator": "Vindicador", "entity.minecraft.wandering_trader": "Comerciant", "entity.minecraft.warden": "Vigilant", "entity.minecraft.wind_charge": "Càrrega de vent", "entity.minecraft.witch": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON> wither", "entity.minecraft.wither_skull": "<PERSON><PERSON> de wither", "entity.minecraft.wolf": "<PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "Vilatà zombi", "entity.minecraft.zombified_piglin": "<PERSON>lin zombificat", "entity.not_summonable": "No s'ha pogut generar l'entitat de tipus %s", "event.minecraft.raid": "Setge", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "event.minecraft.raid.raiders_remaining": "Assaltants restants: %s", "event.minecraft.raid.victory": "Victòria", "event.minecraft.raid.victory.full": "Setge - Victòria", "filled_map.buried_treasure": "Mapa del tresor ", "filled_map.explorer_jungle": "Mapa d'exploració de la jungla", "filled_map.explorer_swamp": "Mapa d'exploració del pantà", "filled_map.id": "ID %s", "filled_map.level": "(Nivell %s/%s)", "filled_map.locked": "Bloquejat", "filled_map.mansion": "Mapa d'exploració forestal", "filled_map.monument": "Mapa d'exploració oceànica", "filled_map.scale": "Escala 1:%s", "filled_map.trial_chambers": "Mapa de cambra de reptes", "filled_map.unknown": "Mapa desconegut", "filled_map.village_desert": "Mapa d'exploració del poble del desert", "filled_map.village_plains": "Mapa d'exploració del poble de les planes", "filled_map.village_savanna": "Mapa d'exploració del poble de la sabana", "filled_map.village_snowy": "Mapa d'exploració del poble nevat", "filled_map.village_taiga": "Mapa d'exploració del poble de taigà", "flat_world_preset.minecraft.bottomless_pit": "Pou sense fons", "flat_world_preset.minecraft.classic_flat": "Extraplà clàssic", "flat_world_preset.minecraft.desert": "Desert", "flat_world_preset.minecraft.overworld": "Superfície", "flat_world_preset.minecraft.redstone_ready": "Preparat per a redstone", "flat_world_preset.minecraft.snowy_kingdom": "Regne nevat", "flat_world_preset.minecraft.the_void": "El buit", "flat_world_preset.minecraft.tunnelers_dream": "Paradís miner", "flat_world_preset.minecraft.water_world": "Món d'aigua", "flat_world_preset.unknown": "???", "gameMode.adventure": "Mode aventura", "gameMode.changed": "El mode de joc ha estat actualitzat a %s", "gameMode.creative": "Mode creatiu", "gameMode.hardcore": "Mode Extrem!", "gameMode.spectator": "Mode espectador", "gameMode.survival": "Mode supervivència", "gamerule.allowFireTicksAwayFromPlayer": "Carregar foc lluny dels jugadors", "gamerule.allowFireTicksAwayFromPlayer.description": "Controla si el foc o la lava haurien d'estar carregats a més de 8 chunks de distància de qualsevol jugador", "gamerule.announceAdvancements": "Anunciar a<PERSON>", "gamerule.blockExplosionDropDecay": "Alguns blocs no deixaran caure botí en explotar per interaccions entre blocs", "gamerule.blockExplosionDropDecay.description": "Alguns ítems que surten de blocs destruïts per una explosió causada per interaccións de blocs es perdran.", "gamerule.category.chat": "Xat", "gamerule.category.drops": "Botí", "gamerule.category.misc": "Miscel·lanis", "gamerule.category.mobs": "Criatures", "gamerule.category.player": "Jugador", "gamerule.category.spawning": "Generació de criatures", "gamerule.category.updates": "<PERSON><PERSON> al món", "gamerule.commandBlockOutput": "Mostrar resultat dels blocs de comandaments", "gamerule.commandModificationBlockLimit": "Límit de blocs modificables per comandament", "gamerule.commandModificationBlockLimit.description": "El nombre de blocs que poden ser canviats a la vegada per un comandament, com ara /fill o /clone.", "gamerule.disableElytraMovementCheck": "Desactiva la comprovació de moviment per a èlitres", "gamerule.disablePlayerMovementCheck": "Desactiva la comprovació de moviment dels jugadors", "gamerule.disableRaids": "Desactivar setges", "gamerule.doDaylightCycle": "Avançar l'hora del dia", "gamerule.doEntityDrops": "Deixar caure entitats", "gamerule.doEntityDrops.description": "Controla si les entitats (vagonetes amb cofre i ítems que contenen, marcs, etc) deixen caure ítems al trencar-les", "gamerule.doFireTick": "Propagar/apagar focs", "gamerule.doImmediateRespawn": "Reaparèixer immediatament", "gamerule.doInsomnia": "Generar fantasmes", "gamerule.doLimitedCrafting": "Requerir recepta per elaborar", "gamerule.doLimitedCrafting.description": "Si s'activa, els jugadors només podran elaborar receptes apreses.", "gamerule.doMobLoot": "Deixar caure botí de criatures", "gamerule.doMobLoot.description": "Controla els recursos que criatures poden soltar, incloent-hi orbes d'experiència.", "gamerule.doMobSpawning": "Generar criatures", "gamerule.doMobSpawning.description": "Algunes entitats poden tenir les seves pròpies regles.", "gamerule.doPatrolSpawning": "Generar bandes de saquejadors", "gamerule.doTileDrops": "Deixar caure blocs", "gamerule.doTileDrops.description": "Controla els recursos que blocs poden soltar, incloent-hi orbes d'experiència.", "gamerule.doTraderSpawning": "Genera comerciants errants", "gamerule.doVinesSpread": "Propagació de lianes", "gamerule.doVinesSpread.description": "Controla si les lianes es propaguen a blocs adjacents a l'atzar. Això no afecta a altres tipus de lianes com ara lianes ploroses, lianes retorçades, etc.", "gamerule.doWardenSpawning": "Generar vigilants", "gamerule.doWeatherCycle": "Meteorologia canviant", "gamerule.drowningDamage": "Mal per ofegament", "gamerule.enderPearlsVanishOnDeath": "Les perles d'Ender desapareixen al morir", "gamerule.enderPearlsVanishOnDeath.description": "Controla si les perles d'Ender llençades per un jugador desapareixen quan aquest mor.", "gamerule.entitiesWithPassengersCanUsePortals": "Entitats amb passatgers poden utilitzar portals", "gamerule.entitiesWithPassengersCanUsePortals.description": "Permet entitats amb passatgers teleportar a través de portals del nether, end i l'entrada de l'end.", "gamerule.fallDamage": "Mal en caure", "gamerule.fireDamage": "Mal al cremar-se", "gamerule.forgiveDeadPlayers": "Perdonar jugadors morts", "gamerule.forgiveDeadPlayers.description": "Les criatures neutrals que estiguin enfadades deixaran d'estar-ho quan el jugador que persegueixen mori a prop.", "gamerule.freezeDamage": "Mal al congelar-se", "gamerule.globalSoundEvents": "Sons importants globals", "gamerule.globalSoundEvents.description": "Quan certs esdeveniments succeeixen, com ara l'aparició d'un monstre important, el so es pot sentir a tot arreu.", "gamerule.keepInventory": "Conservar l'inventari al morir", "gamerule.lavaSourceConversion": "La lava es converteix en font", "gamerule.lavaSourceConversion.description": "Si lava fluent està rodejada en dues bandes per fonts de lava, també es convertirà en una font.", "gamerule.locatorBar": "Activar la barra localitzadora de jugadors", "gamerule.locatorBar.description": ".", "gamerule.logAdminCommands": "Gravar comandaments fets per administradors", "gamerule.maxCommandChainLength": "Límit de cadenes de blocs de comandaments", "gamerule.maxCommandChainLength.description": "S'aplica a les cadenes i funcions de blocs de comandaments.", "gamerule.maxCommandForkCount": "Límit de contextos en comandaments", "gamerule.maxCommandForkCount.description": "Nombre màxim de contextos que es poden emprar en comandaments com 'execute as'.", "gamerule.maxEntityCramming": "Límit d'entitats en un mateix bloc", "gamerule.minecartMaxSpeed": "Velocitat màxima d'una vagoneta", "gamerule.minecartMaxSpeed.description": "Velocitat màxima predeterminada d'una vagoneta en moviment a terra.", "gamerule.mobExplosionDropDecay": "Alguns blocs no deixaran caure botí en explotar per criatures", "gamerule.mobExplosionDropDecay.description": "Alguns ítems que surten de blocs destruits per una explosió causada per una criatura es perdran.", "gamerule.mobGriefing": "Permetre que les criatures destrueixin", "gamerule.naturalRegeneration": "Regenerar vida", "gamerule.playersNetherPortalCreativeDelay": "Retràs del jugador en mode creatiu en el portal del Nether", "gamerule.playersNetherPortalCreativeDelay.description": "Temps (en cicles) que un jugador en mode creatiu té per situar-se en un portal del Nether abans de canviar dimensions.", "gamerule.playersNetherPortalDefaultDelay": "Retràs del jugador en mode no-creatiu en el portal del Nether", "gamerule.playersNetherPortalDefaultDelay.description": "Temps (en cicles) que un jugador en mode no-creatiu té per situar-se en un portal del Nether abans de canviar dimensions.", "gamerule.playersSleepingPercentage": "Percentatge de jugadors dormint", "gamerule.playersSleepingPercentage.description": "El percentatge de jugadors connectats que han d'estar dormint per fer-se de dia.", "gamerule.projectilesCanBreakBlocks": "Projectils poden trencar blocs", "gamerule.projectilesCanBreakBlocks.description": "Controla si projectils d'impacte destruiran blocs que són destructibles per ells.", "gamerule.randomTickSpeed": "Freqüència dels cicles aleatoris", "gamerule.reducedDebugInfo": "Reduir l'informació de depuració", "gamerule.reducedDebugInfo.description": "Limita el contingut de la pantalla de depuració.", "gamerule.sendCommandFeedback": "Enviar respostes de comandaments", "gamerule.showDeathMessages": "Mostrar missatges de mort", "gamerule.snowAccumulationHeight": "Alçària de la neu acumulada", "gamerule.snowAccumulationHeight.description": "<PERSON>uan neva, s'apilen capes de neu al terra fins aquesta quantitat màxima.", "gamerule.spawnChunkRadius": "Radi del chunk del punt d'aparició", "gamerule.spawnChunkRadius.description": "Quantitat de chunks que es mantindran carregats al voltant del punt d'aparició del món superior.", "gamerule.spawnRadius": "Radi del punt de reaparició", "gamerule.spawnRadius.description": "Controla la mida de l'àrea al voltant del punt d'aparició que poden aparèixer jugadors.", "gamerule.spectatorsGenerateChunks": "Permet la generació de terreny en mode espectador", "gamerule.tntExplodes": "Permetre activar la TNT i fer-ne explotar", "gamerule.tntExplosionDropDecay": "Alguns blocs no deixaran caure botí en explotar per dinamita", "gamerule.tntExplosionDropDecay.description": "Alguns ítems que surten de blocs destruïts per una explosió causada per dinamita es perdran.", "gamerule.universalAnger": "Enuig universal", "gamerule.universalAnger.description": "Les criatures neutrals enfurismades ataquen a qualsevol jugador que estigui a prop, no només al jugador que les hagi enfurismat. Funciona millor si forgiveDeadPlayers està desactivat.", "gamerule.waterSourceConversion": "L'aigua es converteix en font", "gamerule.waterSourceConversion.description": "Si aigua fluent està rodejada en dues bandes per fonts d'aigua, també es convertirà en una font.", "generator.custom": "Personalitzat", "generator.customized": "Personalitzat (antic)", "generator.minecraft.amplified": "AMPLIFICAT", "generator.minecraft.amplified.info": "Nota: Només per diversió, necessita un ordinador potent.", "generator.minecraft.debug_all_block_states": "Mode de depuració", "generator.minecraft.flat": "Extraplà", "generator.minecraft.large_biomes": "Superbiomes", "generator.minecraft.normal": "Per defecte", "generator.minecraft.single_biome_surface": "Bioma únic", "generator.single_biome_caves": "Coves", "generator.single_biome_floating_islands": "Illes flotants", "gui.abuseReport.attestation": "Enviant aquesta denúncia confirmes que la informació que has proveït és precisa i complet segons el teu millor coneixement.", "gui.abuseReport.comments": "<PERSON><PERSON><PERSON>", "gui.abuseReport.describe": "Compartir detalls ens ajudarà a prendre una decisió adequada.", "gui.abuseReport.discard.content": "Si surts ara, s'esborrarà aquesta denúncia i els teus comentaris.\nEstàs segur que vols sortir?", "gui.abuseReport.discard.discard": "Surt i descarta la denúncia", "gui.abuseReport.discard.draft": "Desar com a esborrany", "gui.abuseReport.discard.return": "<PERSON><PERSON><PERSON> editant", "gui.abuseReport.discard.title": "Vols descartar la denúncia i els comentaris?", "gui.abuseReport.draft.content": "Vols seguir editant la denúncia existent o descartar-la i crear-ne una de nova?", "gui.abuseReport.draft.discard": "Descar<PERSON>", "gui.abuseReport.draft.edit": "<PERSON><PERSON><PERSON> editant", "gui.abuseReport.draft.quittotitle.content": "Vols seguir editant la denúncia o descartar-la?", "gui.abuseReport.draft.quittotitle.title": "L'esborrany de la denúncia es perdrà si surts", "gui.abuseReport.draft.title": "Editar l'esborrany de la denúncia de xat?", "gui.abuseReport.error.title": "Problema en enviar la teva denúncia", "gui.abuseReport.message": "On has observat el mal comportament?\nAixò ens ajudarà en la investigació del teu cas.", "gui.abuseReport.more_comments": "Si us plau, descriu el que ha passat:", "gui.abuseReport.name.comment_box_label": "Si us plau, descriu per què vols denunciar aquest nom:", "gui.abuseReport.name.reporting": "Estàs denunciant a \"%s\".", "gui.abuseReport.name.title": "Denuncia nom d'un jugador", "gui.abuseReport.observed_what": "Per què estàs denunciant això?", "gui.abuseReport.read_info": "Aprèn més sobre les denúncies", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drogues o alcohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Algú està animant altres a participar en activitats il·legals relacionades amb les drogues o animant els menors a beure.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Explotació sexual o abús de menors", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Algú està parlant de o promovent un comportament indecent que implica infants.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Difamació, suplantació d'identitat o informació falsa", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Algú perjudica la reputació d'una altra persona, suplantant la identitat d'algú o compartint informació falsa amb l'objectiu d'explotar o causar confusió a altres.", "gui.abuseReport.reason.description": "Des<PERSON>rip<PERSON>ó:", "gui.abuseReport.reason.false_reporting": "Enviament d'informes falsos", "gui.abuseReport.reason.generic": "<PERSON>s vull denunciar", "gui.abuseReport.reason.generic.description": "Estic enfadat amb ells / han fet una cosa que no m'agrada.", "gui.abuseReport.reason.harassment_or_bullying": "Assetjament", "gui.abuseReport.reason.harassment_or_bullying.description": "Algú t'està humiliant, atacant o assetjant a tu o a algú altre. Això inclou quan algú intenta repetidament contactar amb tu o algú altre sense consentiment o publica informació personal privada teva o d'algú altre sense consentiment.", "gui.abuseReport.reason.hate_speech": "Discurs d'odi", "gui.abuseReport.reason.hate_speech.description": "Algú t'està atacant a tu o a un altre jugador basant-se en característiques de la seva identitat, com la religió, la raça o la sexualitat.", "gui.abuseReport.reason.imminent_harm": "Perill imminent: Amenaça de ferir a altres", "gui.abuseReport.reason.imminent_harm.description": "Algú amenaça de ferir-te a tu o algú altre a la vida real.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Imatges íntimes no consentides", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Algú està parlant de, compartint o incitant el trànsit d'imatges privades i íntimes.", "gui.abuseReport.reason.self_harm_or_suicide": "Autolesió o suïcidi", "gui.abuseReport.reason.self_harm_or_suicide.description": "Algú està amenaçant o parlant de fer-se mal a si mateix a la vida real.", "gui.abuseReport.reason.sexually_inappropriate": "Sexualment inapropiat", "gui.abuseReport.reason.sexually_inappropriate.description": "Aparences que mostren elements de naturalesa sexual, òrgans sexuals o violència sexual", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorisme o extremisme violent", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Algú està parlant sobre, promovent, o amenaçant de cometre actes de terrorisme o extremisme violent per raons polítiques, religioses, ideològiques o altres motius.", "gui.abuseReport.reason.title": "Tria una categoria per a la denúncia", "gui.abuseReport.report_sent_msg": "Hem rebut la teva denúncia. Gràcies!\nEl nostre equip ho revisarà al més aviat possible.", "gui.abuseReport.select_reason": "Tria una categoria per a la denúncia", "gui.abuseReport.send": "<PERSON><PERSON><PERSON>", "gui.abuseReport.send.comment_too_long": "Si us plau, sigues més breu", "gui.abuseReport.send.error_message": "S'ha produït un error en enviar la teva denúncia:\n'%s'", "gui.abuseReport.send.generic_error": "S'ha produït un error inesperat en enviar la teva denúncia.", "gui.abuseReport.send.http_error": "S'ha produït un error HTTP inesperat en enviar la teva denúncia.", "gui.abuseReport.send.json_error": "S'ha detectat un paquet mal estructurat en enviar la teva denúncia.", "gui.abuseReport.send.no_reason": "Si us plau, tria una categoria per a la denúncia", "gui.abuseReport.send.not_attested": "Si us plau, llegeix el text anterior i marca la casella de selecció per enviar la denúncia", "gui.abuseReport.send.service_unavailable": "No s'ha pogut contactar amb el servei de denúncies per abús. Assegura't d'estar connectat a Internet i torna-ho a intentar.", "gui.abuseReport.sending.title": "S'està enviant la teva denúncia...", "gui.abuseReport.sent.title": "Denúncia enviada", "gui.abuseReport.skin.title": "Denuncia l'aparença d'un jugador", "gui.abuseReport.title": "Denuncia un jugador", "gui.abuseReport.type.chat": "Missatges del xat", "gui.abuseReport.type.name": "Nom del jugador", "gui.abuseReport.type.skin": "Aparença del jugador", "gui.acknowledge": "Entès", "gui.advancements": "Avenços", "gui.all": "Totes", "gui.back": "<PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\nAprèn més al següent enllaç: %s", "gui.banned.description.permanent": "El teu compte ha estat suspès permanentment; això vol dir que no podràs jugar en línia o unir-te a Realms.", "gui.banned.description.reason": "Hem rebut un informe de mal comportament per part del teu compte fa poc. Els nostres moderadors han revisat el teu cas i l'han identificat com a %s, cosa que va en contra de la normativa de la comunitat de Minecraft.", "gui.banned.description.reason_id": "Codi: %s", "gui.banned.description.reason_id_message": "Codi: %s - %s", "gui.banned.description.temporary": "%s Fins aleshores, no podràs jugar en línia o unir-te a Realms.", "gui.banned.description.temporary.duration": "El teu compte està suspès temporalment, serà reactivat en %s.", "gui.banned.description.unknownreason": "Hem rebut un informe de mal comportament per part del teu compte fa poc. Els nostres moderadors han revisat el teu cas i han identificat que no compleix amb la normativa de la comunitat de Minecraft.", "gui.banned.name.description": "El teu nom actual - %s- no compleix amb les normes de la comunitat. Pots jugar individual, però hauràs de canviar el teu nom per a jugar en línia.\n\nAprèn més o presenta una revisió del cas al següent enllaç: %s", "gui.banned.name.title": "Nom no permès en multijugador", "gui.banned.reason.defamation_impersonation_false_information": "Suplantació d'identitat o compartir informació per a abusar o confondre d'altres", "gui.banned.reason.drugs": "Referències a drogues il·legals", "gui.banned.reason.extreme_violence_or_gore": "Representacions de violència excessiva o gore real", "gui.banned.reason.false_reporting": "Informes excessius o erronis", "gui.banned.reason.fraud": "Ús o adquisició de contingut fraudulenta", "gui.banned.reason.generic_violation": "Viola la normativa de la comunitat", "gui.banned.reason.harassment_or_bullying": "Llenguatge abusiu premeditat i mal intencionat", "gui.banned.reason.hate_speech": "Discurs d'odi o discriminació", "gui.banned.reason.hate_terrorism_notorious_figure": "Fa referència a grups discriminatoris, organitzacions terroristes, o figures infames", "gui.banned.reason.imminent_harm_to_person_or_property": "Mostra intenció de causar danys reals a persones o béns", "gui.banned.reason.nudity_or_pornography": "Mostra contingut lasciu o pornogràfic", "gui.banned.reason.sexually_inappropriate": "Temes o contingut de caràcter sexual", "gui.banned.reason.spam_or_advertising": "Missatges brossa o anuncis", "gui.banned.skin.description": "La teva aparença actual no compleix amb les normes de la comunitat. Encara pots jugar amb una aparença predeterminada, o selecciona una nova. \n\nAprèn més o presenta una revisió del cas al següent enllaç: %s", "gui.banned.skin.title": "Aparença no permès", "gui.banned.title.permanent": "Compte suspès permanentment", "gui.banned.title.temporary": "Compte su<PERSON><PERSON> temporalment", "gui.cancel": "Cancel·la", "gui.chatReport.comments": "<PERSON><PERSON><PERSON>", "gui.chatReport.describe": "Compartir detalls ens ajudarà a prendre una decisió adequada.", "gui.chatReport.discard.content": "Si surts ara, s'esborraran aquesta denúncia i els teus comentaris.\nSegur que vols sortir?", "gui.chatReport.discard.discard": "Sortir i descartar la denúncia", "gui.chatReport.discard.draft": "Desar com a esborrany", "gui.chatReport.discard.return": "Segueix editant", "gui.chatReport.discard.title": "Descartar l'informe i els comentaris?", "gui.chatReport.draft.content": "Vols seguir editant la denúncia existent o descartar-la i crear-ne una de nova?", "gui.chatReport.draft.discard": "Descar<PERSON>", "gui.chatReport.draft.edit": "<PERSON><PERSON><PERSON> editant", "gui.chatReport.draft.quittotitle.content": "Vols seguir editant la denúncia o descartar-la?", "gui.chatReport.draft.quittotitle.title": "L'esborrany de la denúncia es perdrà si surts", "gui.chatReport.draft.title": "Editar l'esborrany de la denúncia?", "gui.chatReport.more_comments": "Si us plau, descriu el que ha passat:", "gui.chatReport.observed_what": "Per què estàs denunciant això?", "gui.chatReport.read_info": "Aprèn més sobre les denúncies", "gui.chatReport.report_sent_msg": "Hem rebut el vostre informe amb èxit. Gràcies!\n\nEl nostre equip el revisarà al més aviat possible.", "gui.chatReport.select_chat": "Selecciona els missatges del xat a denunciar", "gui.chatReport.select_reason": "Tria una categoria per a la denúncia", "gui.chatReport.selected_chat": "%s missatge(s) seleccionat(s) per a denunciar", "gui.chatReport.send": "<PERSON><PERSON><PERSON>", "gui.chatReport.send.comments_too_long": "Si us plau, sigues més breu", "gui.chatReport.send.no_reason": "Si us plau, tria una categoria per a la denúncia", "gui.chatReport.send.no_reported_messages": "Si us plau, selecciona com a mínim un missatge del xat per a denunciar-lo", "gui.chatReport.send.too_many_messages": "Estàs intentant incloure massa missatges en la denúncia", "gui.chatReport.title": "Denunciar missatge d'un jugador", "gui.chatSelection.context": "Els missatges previs i posteriors a la selecció s'inclouran per proporcionar context addicional", "gui.chatSelection.fold": "%s missatges ocults", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s s'ha unit al xat", "gui.chatSelection.message.narrate": "%s ha dit: %s a les %s", "gui.chatSelection.selected": "Missatge(s) seleccionats: %s/%s", "gui.chatSelection.title": "Selecciona els missatges del xat que vols denunciar", "gui.continue": "<PERSON><PERSON><PERSON><PERSON>", "gui.copy_link_to_clipboard": "Copia l'enllaç al porta-retalls", "gui.days": "%s dia/es", "gui.done": "Fet", "gui.down": "Ava<PERSON>", "gui.entity_tooltip.type": "Tipus: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s arxius s'han rebutjat", "gui.fileDropFailure.title": "Error en afegir arxius", "gui.hours": "%s hora/es", "gui.loadingMinecraft": "Carregant Minecraft", "gui.minutes": "%s minut/s", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Botó de %s", "gui.narrate.editBox": "Barra de text de %s: %s", "gui.narrate.slider": "Barra de desplaçament de %s", "gui.narrate.tab": "Pestanya de %s", "gui.no": "No", "gui.none": "Cap", "gui.ok": "Fet", "gui.open_report_dir": "Obre el directori d'informes", "gui.proceed": "Continua", "gui.recipebook.moreRecipes": "Fes clic dret per a més receptes", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Cerca...", "gui.recipebook.toggleRecipes.all": "Totes les receptes", "gui.recipebook.toggleRecipes.blastable": "Receptes d'alt forn", "gui.recipebook.toggleRecipes.craftable": "Receptes elaborables", "gui.recipebook.toggleRecipes.smeltable": "Receptes de forn", "gui.recipebook.toggleRecipes.smokable": "Receptes de fumador", "gui.report_to_server": "Informa el servidor", "gui.socialInteractions.blocking_hint": "Gestiona amb el compte de Microsoft", "gui.socialInteractions.empty_blocked": "Cap jugador bloquejat al xat", "gui.socialInteractions.empty_hidden": "Cap jugador amagat al xat", "gui.socialInteractions.hidden_in_chat": "Els missatges al xat del jugador %s s'amagaran", "gui.socialInteractions.hide": "Amaga al xat", "gui.socialInteractions.narration.hide": "Amaga els missatges de %s", "gui.socialInteractions.narration.report": "Denunciar el jugador %s", "gui.socialInteractions.narration.show": "Mostra els missatges de %s", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "No s'ha pogut trobar cap jugador amb aquest nom", "gui.socialInteractions.search_hint": "Cerca...", "gui.socialInteractions.server_label.multiple": "%s - %s jugadors", "gui.socialInteractions.server_label.single": "%s - %s jugador", "gui.socialInteractions.show": "Mostra al xat", "gui.socialInteractions.shown_in_chat": "Els missatges al xat del jugador %s es mostraran", "gui.socialInteractions.status_blocked": "Bloquejades", "gui.socialInteractions.status_blocked_offline": "Bloquejat - Sense connexió", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Amagat - Sense connexió", "gui.socialInteractions.status_offline": "Sense connexió", "gui.socialInteractions.tab_all": "Totes", "gui.socialInteractions.tab_blocked": "Bloquejat", "gui.socialInteractions.tab_hidden": "Amagada", "gui.socialInteractions.title": "Interaccions socials", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON> missatges", "gui.socialInteractions.tooltip.report": "Denúncia un jugador", "gui.socialInteractions.tooltip.report.disabled": "El servei de denúncies no està disponible", "gui.socialInteractions.tooltip.report.no_messages": "No hi ha missatges denunciables del jugador %s", "gui.socialInteractions.tooltip.report.not_reportable": "Aquest jugador no pot ser denunciat perquè els seus missatges de xat no poden ser verificats en aquest servidor", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON> missatges", "gui.stats": "Estadístiques", "gui.toMenu": "Torna a la llista de servidors", "gui.toRealms": "Torna a la llista de servidors de Realm", "gui.toTitle": "<PERSON><PERSON> al menú principal", "gui.toWorld": "Tornar a la llista de mons", "gui.togglable_slot": "Fes clic per desactivar la ranura", "gui.up": "Amunt", "gui.waitingForResponse.button.inactive": "Back (%ss)", "gui.waitingForResponse.title": "Esperant un servidor", "gui.yes": "Sí", "hanging_sign.edit": "Edita el missatge del cartell penjant", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Cridar", "instrument.minecraft.dream_goat_horn": "Somia<PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Reflexionar", "instrument.minecraft.seek_goat_horn": "Buscar", "instrument.minecraft.sing_goat_horn": "Cantar", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "<PERSON><PERSON><PERSON><PERSON>", "inventory.hotbarInfo": "Desa la barra d'eines amb %1$s+%2$s", "inventory.hotbarSaved": "Barra d'eines desada (recupera-la amb %1$s+%2$s)", "item.canBreak": "Pot trencar:", "item.canPlace": "Es pot col·locar en:", "item.canUse.unknown": "Desconegut", "item.color": "Color: %s", "item.components": "%s component(s)", "item.disabled": "Ítem desactivat", "item.durability": "Durabilitat: %s / %s", "item.dyed": "Tenyit", "item.minecraft.acacia_boat": "Barca d'acàcia", "item.minecraft.acacia_chest_boat": "Barca d'acàcia amb cofre", "item.minecraft.allay_spawn_egg": "<PERSON><PERSON> allay", "item.minecraft.amethyst_shard": "Fragment d’ametista", "item.minecraft.angler_pottery_shard": "Fragment de ceràmica de canya de pescar", "item.minecraft.angler_pottery_sherd": "Fragment de ceràmica de canya de pescar", "item.minecraft.apple": "<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Fragment de ceràmica d'arquer", "item.minecraft.archer_pottery_sherd": "Fragment de ceràmica d'arquer", "item.minecraft.armadillo_scute": "Escata d'arm<PERSON>llo", "item.minecraft.armadillo_spawn_egg": "<PERSON><PERSON>", "item.minecraft.armor_stand": "Maniquí", "item.minecraft.arms_up_pottery_shard": "Fragment de ceràmica de ninot", "item.minecraft.arms_up_pottery_sherd": "Fragment de ceràmica de ninot", "item.minecraft.arrow": "Fletxa", "item.minecraft.axolotl_bucket": "Galleda amb axolot", "item.minecraft.axolotl_spawn_egg": "<PERSON><PERSON> ax<PERSON>", "item.minecraft.baked_potato": "Patata cuita", "item.minecraft.bamboo_chest_raft": "Rai de bambú amb cofre", "item.minecraft.bamboo_raft": "Rai de bamb<PERSON>", "item.minecraft.bat_spawn_egg": "Generar ratpenat", "item.minecraft.bee_spawn_egg": "<PERSON><PERSON> abella", "item.minecraft.beef": "Bistec cru", "item.minecraft.beetroot": "Remolatxa", "item.minecraft.beetroot_seeds": "Llavors de remolatxa", "item.minecraft.beetroot_soup": "Sopa de remolatxa", "item.minecraft.birch_boat": "Barca de bedoll", "item.minecraft.birch_chest_boat": "Barca de bedoll amb cofre", "item.minecraft.black_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.black_dye": "Tint negre", "item.minecraft.black_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.blade_pottery_shard": "Fragment de ceràmica d'espasa", "item.minecraft.blade_pottery_sherd": "Fragment de ceràmica d'espasa", "item.minecraft.blaze_powder": "Pols de foc", "item.minecraft.blaze_rod": "<PERSON><PERSON>", "item.minecraft.blaze_spawn_egg": "Generar esperit del foc", "item.minecraft.blue_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_dye": "<PERSON><PERSON> blau", "item.minecraft.blue_egg": "<PERSON>u blau", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "Generador d'a<PERSON>ll", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.bolt_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>'armad<PERSON>: <PERSON><PERSON><PERSON>", "item.minecraft.bone": "<PERSON><PERSON>", "item.minecraft.bone_meal": "<PERSON><PERSON>", "item.minecraft.book": "Llibre", "item.minecraft.bordure_indented_banner_pattern": "<PERSON><PERSON><PERSON> bord<PERSON> dentada", "item.minecraft.bow": "Arc", "item.minecraft.bowl": "<PERSON><PERSON>", "item.minecraft.bread": "Pa", "item.minecraft.breeze_rod": "Vara de brisa", "item.minecraft.breeze_spawn_egg": "<PERSON><PERSON>", "item.minecraft.brewer_pottery_shard": "Fragment de ceràmica de poció", "item.minecraft.brewer_pottery_sherd": "Fragment de ceràmica de poció", "item.minecraft.brewing_stand": "Altar de pocions", "item.minecraft.brick": "<PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON><PERSON><PERSON> ma<PERSON>", "item.minecraft.brown_dye": "<PERSON><PERSON> marr<PERSON>", "item.minecraft.brown_egg": "<PERSON><PERSON> marr<PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.brush": "<PERSON><PERSON><PERSON>", "item.minecraft.bucket": "Galleda", "item.minecraft.bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty": "<PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Pot contenir una pila mixta d'ítems", "item.minecraft.bundle.full": "<PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Fragment de ceràmica de foc", "item.minecraft.burn_pottery_sherd": "Fragment de ceràmica de foc", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON> d<PERSON>", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON> amb <PERSON>anaga", "item.minecraft.cat_spawn_egg": "Generar gat", "item.minecraft.cauldron": "<PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Generar aranya de cova", "item.minecraft.chainmail_boots": "Botes de cota de malla", "item.minecraft.chainmail_chestplate": "Cuirassa de cota de malla", "item.minecraft.chainmail_helmet": "Casc de cota de malla", "item.minecraft.chainmail_leggings": "Pantalons de cota de malla", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON> vegetal", "item.minecraft.cherry_boat": "Barca de cirerer", "item.minecraft.cherry_chest_boat": "Barca de cirerer amb cofre", "item.minecraft.chest_minecart": "Vagoneta amb cofre", "item.minecraft.chicken": "<PERSON><PERSON> <PERSON>", "item.minecraft.chicken_spawn_egg": "<PERSON><PERSON> poll<PERSON>", "item.minecraft.chorus_fruit": "Fruita de tornada", "item.minecraft.clay_ball": "Bola d'arg<PERSON>", "item.minecraft.clock": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.coal": "<PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.coast_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>armad<PERSON>: Costa", "item.minecraft.cocoa_beans": "Grans de cacau", "item.minecraft.cod": "Bacallà cru", "item.minecraft.cod_bucket": "Galleda amb bacall<PERSON>", "item.minecraft.cod_spawn_egg": "<PERSON><PERSON>", "item.minecraft.command_block_minecart": "Vagoneta amb bloc de comandaments", "item.minecraft.compass": "Brúixola", "item.minecraft.cooked_beef": "Bistec", "item.minecraft.cooked_chicken": "Pollastre rostit", "item.minecraft.cooked_cod": "Bacallà cuit", "item.minecraft.cooked_mutton": "Carn d'ovella cuita", "item.minecraft.cooked_porkchop": "Carn de porc cuita", "item.minecraft.cooked_rabbit": "Conill rostit", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON> cuit", "item.minecraft.cookie": "Gale<PERSON>", "item.minecraft.copper_ingot": "Lingot de coure", "item.minecraft.cow_spawn_egg": "Generar vaca", "item.minecraft.creaking_spawn_egg": "<PERSON><PERSON>", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.desc": "Estampat de creeper", "item.minecraft.creeper_banner_pattern.new": "<PERSON><PERSON><PERSON>", "item.minecraft.creeper_spawn_egg": "Generar creeper", "item.minecraft.crossbow": "Ballesta", "item.minecraft.crossbow.projectile": "Projectil:", "item.minecraft.crossbow.projectile.multiple": "Projectil: %s X %s", "item.minecraft.crossbow.projectile.single": "Projectil: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON><PERSON> c<PERSON>", "item.minecraft.cyan_dye": "<PERSON><PERSON> cian", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Fragment de ceràmica de creeper", "item.minecraft.danger_pottery_sherd": "Fragment de ceràmica de creeper", "item.minecraft.dark_oak_boat": "Barca de pomer fosc", "item.minecraft.dark_oak_chest_boat": "Barca de pomer fosc amb cofre", "item.minecraft.debug_stick": "Pal de <PERSON>", "item.minecraft.debug_stick.empty": "%s no té propietats", "item.minecraft.debug_stick.select": "has seleccionat \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" ha canviat a %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "Destral de diamant", "item.minecraft.diamond_boots": "<PERSON><PERSON>", "item.minecraft.diamond_chestplate": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.diamond_helmet": "Casc de diamant", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "Armadura de diamant per a cavalls", "item.minecraft.diamond_leggings": "Malles de diamant", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON>", "item.minecraft.diamond_sword": "Espasa de diamant", "item.minecraft.disc_fragment_5": "Fragment de disc", "item.minecraft.disc_fragment_5.desc": "Disc de música - 5", "item.minecraft.dolphin_spawn_egg": "<PERSON>rar do<PERSON>", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON> ruc", "item.minecraft.dragon_breath": "<PERSON>è <PERSON>", "item.minecraft.dried_kelp": "Algues seques", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON> ofegat", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>'armad<PERSON>: <PERSON><PERSON>", "item.minecraft.echo_shard": "Fragment d'eco", "item.minecraft.egg": "Ou", "item.minecraft.elder_guardian_spawn_egg": "Generar guard<PERSON> an<PERSON>", "item.minecraft.elytra": "Èlitres", "item.minecraft.emerald": "<PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "Llibre encantat", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON> da<PERSON>da encantada", "item.minecraft.end_crystal": "Cristall de l'End", "item.minecraft.ender_dragon_spawn_egg": "<PERSON><PERSON> drac d'<PERSON><PERSON>", "item.minecraft.ender_eye": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON> enderman", "item.minecraft.endermite_spawn_egg": "Generar endermite", "item.minecraft.evoker_spawn_egg": "Generar invocador", "item.minecraft.experience_bottle": "Ampolla màgica", "item.minecraft.explorer_pottery_shard": "Fragment de ceràmica de mapa", "item.minecraft.explorer_pottery_sherd": "Fragment de ceràmica de mapa", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>arm<PERSON>: <PERSON><PERSON>", "item.minecraft.feather": "Ploma", "item.minecraft.fermented_spider_eye": "Ull d'aranya fermentat", "item.minecraft.field_masoned_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.filled_map": "Mapa", "item.minecraft.fire_charge": "Càrrega de foc", "item.minecraft.firework_rocket": "Focs artificials", "item.minecraft.firework_rocket.flight": "Duració del vol:", "item.minecraft.firework_rocket.multiple_stars": "%s X %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Estrella de focs artificials", "item.minecraft.firework_star.black": "Negre", "item.minecraft.firework_star.blue": "Blau", "item.minecraft.firework_star.brown": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Personalitzat", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "Canviar a", "item.minecraft.firework_star.flicker": "Espurnejar", "item.minecraft.firework_star.gray": "<PERSON><PERSON>", "item.minecraft.firework_star.green": "Verd", "item.minecraft.firework_star.light_blue": "<PERSON>lau cel", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON> clar", "item.minecraft.firework_star.lime": "Llima", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "<PERSON>ron<PERSON>", "item.minecraft.firework_star.pink": "<PERSON>", "item.minecraft.firework_star.purple": "<PERSON>", "item.minecraft.firework_star.red": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape": "Forma desconeguda", "item.minecraft.firework_star.shape.burst": "Es<PERSON>latar", "item.minecraft.firework_star.shape.creeper": "En forma de creeper", "item.minecraft.firework_star.shape.large_ball": "Esfera gran", "item.minecraft.firework_star.shape.small_ball": "Esfera petita", "item.minecraft.firework_star.shape.star": "En forma d'estrella", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "<PERSON>", "item.minecraft.firework_star.yellow": "Groc", "item.minecraft.fishing_rod": "<PERSON><PERSON>car", "item.minecraft.flint": "Sílex", "item.minecraft.flint_and_steel": "Sílex i acer", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.flow_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>'armad<PERSON>: Espiral", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.desc": "Espiral", "item.minecraft.flow_banner_pattern.new": "Estampat d'espiral", "item.minecraft.flow_pottery_sherd": "Fragment de ceràmica d'espiral", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.desc": "Estampat de flor", "item.minecraft.flower_banner_pattern.new": "Estampat de flor", "item.minecraft.flower_pot": "Test", "item.minecraft.fox_spawn_egg": "<PERSON><PERSON> guineu", "item.minecraft.friend_pottery_shard": "Fragment de ceràmica d'amistat", "item.minecraft.friend_pottery_sherd": "Fragment de ceràmica d'amistat", "item.minecraft.frog_spawn_egg": "Generar granota", "item.minecraft.furnace_minecart": "Vagoneta amb forn", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON> ghast", "item.minecraft.ghast_tear": "Llàgrima de ghast", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON> de vidre", "item.minecraft.glistering_melon_slice": "Tallada de síndria da<PERSON>", "item.minecraft.globe_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.desc": "Estampat de planeta", "item.minecraft.globe_banner_pattern.new": "Estampat de planeta", "item.minecraft.glow_berries": "Baies brillants", "item.minecraft.glow_ink_sac": "Sac de tinta lluent", "item.minecraft.glow_item_frame": "<PERSON>", "item.minecraft.glow_squid_spawn_egg": "Generar calamar lluent", "item.minecraft.glowstone_dust": "Pols de roca brillant", "item.minecraft.goat_horn": "Banya de cabra", "item.minecraft.goat_spawn_egg": "Generar cabra", "item.minecraft.gold_ingot": "<PERSON><PERSON> d'or", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON><PERSON><PERSON> d'or", "item.minecraft.golden_apple": "Poma d'or", "item.minecraft.golden_axe": "Destral d'or", "item.minecraft.golden_boots": "<PERSON><PERSON> d'or", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "Cuirassa d'or", "item.minecraft.golden_helmet": "Casc d'or", "item.minecraft.golden_hoe": "Aixada d'or", "item.minecraft.golden_horse_armor": "Armadura d'or per a cavalls", "item.minecraft.golden_leggings": "Malles d'or", "item.minecraft.golden_pickaxe": "Pic d'or", "item.minecraft.golden_shovel": "Pala d'or", "item.minecraft.golden_sword": "Espasa d'or", "item.minecraft.gray_bundle": "<PERSON><PERSON><PERSON> gris", "item.minecraft.gray_dye": "Tint gris", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON> verd", "item.minecraft.green_dye": "Tint verd", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.guardian_spawn_egg": "<PERSON><PERSON>", "item.minecraft.gunpowder": "Pólvora", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON><PERSON> brisa", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "Estampat de breeze", "item.minecraft.guster_pottery_sherd": "Fragment de ceràmica de brisa", "item.minecraft.happy_ghast_spawn_egg": "<PERSON><PERSON>", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Cor del mar", "item.minecraft.heart_pottery_shard": "Fragment de ceràmica d'amor", "item.minecraft.heart_pottery_sherd": "Fragment de ceràmica d'amor", "item.minecraft.heartbreak_pottery_shard": "Fragment de ceràmica de desamor", "item.minecraft.heartbreak_pottery_sherd": "Fragment de ceràmica de desamor", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON> ho<PERSON>n", "item.minecraft.honey_bottle": "Pot de mel", "item.minecraft.honeycomb": "Bresca", "item.minecraft.hopper_minecart": "Vagoneta amb tremuja", "item.minecraft.horse_spawn_egg": "<PERSON>rar cavall", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.host_armor_trim_smithing_template.new": "Disseny d'armadura d'amfitrió", "item.minecraft.howl_pottery_shard": "Fragment de ceràmica de llop", "item.minecraft.howl_pottery_sherd": "Fragment de ceràmica de llop", "item.minecraft.husk_spawn_egg": "<PERSON>rar zombi ressec", "item.minecraft.ink_sac": "Sac de tinta", "item.minecraft.iron_axe": "Destral de ferro", "item.minecraft.iron_boots": "Botes de ferro", "item.minecraft.iron_chestplate": "Cuirassa de ferro", "item.minecraft.iron_golem_spawn_egg": "Generar gòlem de ferro", "item.minecraft.iron_helmet": "Casc de ferro", "item.minecraft.iron_hoe": "Aixada de ferro", "item.minecraft.iron_horse_armor": "Armadura de ferro per a cavalls", "item.minecraft.iron_ingot": "Lingot de ferro", "item.minecraft.iron_leggings": "Malles de ferro", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON><PERSON> de ferro", "item.minecraft.iron_pickaxe": "Pic de ferro", "item.minecraft.iron_shovel": "Pala de ferro", "item.minecraft.iron_sword": "Espasa de ferro", "item.minecraft.item_frame": "<PERSON>", "item.minecraft.jungle_boat": "Barca de j<PERSON>la", "item.minecraft.jungle_chest_boat": "Barca de jungla amb cofre", "item.minecraft.knowledge_book": "Llibre del coneixement", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Galleda de lava", "item.minecraft.lead": "<PERSON><PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "Botes de cuir", "item.minecraft.leather_chestplate": "Túnica de cuir", "item.minecraft.leather_helmet": "Casc de cuir", "item.minecraft.leather_horse_armor": "Armadura de cuir per a cavalls", "item.minecraft.leather_leggings": "Pantalons de cuir", "item.minecraft.light_blue_bundle": "<PERSON><PERSON><PERSON> blau cel", "item.minecraft.light_blue_dye": "<PERSON>t blau clar", "item.minecraft.light_blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_bundle": "<PERSON><PERSON><PERSON> gris clar", "item.minecraft.light_gray_dye": "Tint gris clar", "item.minecraft.light_gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON> ll<PERSON>", "item.minecraft.lime_dye": "<PERSON><PERSON> llima", "item.minecraft.lime_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion": "<PERSON>ci<PERSON>", "item.minecraft.lingering_potion.effect.awkward": "Poció estranya persistent", "item.minecraft.lingering_potion.effect.empty": "Poció persistent impossible d'elaborar", "item.minecraft.lingering_potion.effect.fire_resistance": "Poció de resistència ígnia persistent", "item.minecraft.lingering_potion.effect.harming": "<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.healing": "Poció de curació persistent", "item.minecraft.lingering_potion.effect.infested": "Poció d'infestació persistent", "item.minecraft.lingering_potion.effect.invisibility": "Poció d'invisibilitat persistent", "item.minecraft.lingering_potion.effect.leaping": "Poció de salt persistent", "item.minecraft.lingering_potion.effect.levitation": "Poció de levitació persistent", "item.minecraft.lingering_potion.effect.luck": "<PERSON><PERSON><PERSON> persistent", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON><PERSON> mundana persistent", "item.minecraft.lingering_potion.effect.night_vision": "Poció de visió nocturna persistent", "item.minecraft.lingering_potion.effect.oozing": "Poció de supura persistent", "item.minecraft.lingering_potion.effect.poison": "<PERSON><PERSON><PERSON> verino<PERSON> persistent", "item.minecraft.lingering_potion.effect.regeneration": "Poció de regeneració persistent", "item.minecraft.lingering_potion.effect.slow_falling": "Poció persistent de caiguda lenta", "item.minecraft.lingering_potion.effect.slowness": "<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.strength": "Poció de força persistent", "item.minecraft.lingering_potion.effect.swiftness": "Poció de velocitat persistent", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON><PERSON> den<PERSON>", "item.minecraft.lingering_potion.effect.turtle_master": "Poció persistent del Mestre tortuga", "item.minecraft.lingering_potion.effect.water": "Ampolla d'aigua persistent", "item.minecraft.lingering_potion.effect.water_breathing": "Poció de respiració aquàtica persistent", "item.minecraft.lingering_potion.effect.weakness": "Poció debilitant persistent", "item.minecraft.lingering_potion.effect.weaving": "Poció de teixidura persistent", "item.minecraft.lingering_potion.effect.wind_charged": "Poció de vent carregat persistent", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON> llama", "item.minecraft.lodestone_compass": "Brúixola guia", "item.minecraft.mace": "<PERSON><PERSON>", "item.minecraft.magenta_bundle": "Sarró magenta", "item.minecraft.magenta_dye": "Tint magenta", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Crema de magma", "item.minecraft.magma_cube_spawn_egg": "Generar cub de magma", "item.minecraft.mangrove_boat": "Barca de mangle", "item.minecraft.mangrove_chest_boat": "Barca de mangle amb cofre", "item.minecraft.map": "Mapa en blanc", "item.minecraft.melon_seeds": "Llavors de síndria", "item.minecraft.melon_slice": "Tallada de síndria", "item.minecraft.milk_bucket": "Galleda amb llet", "item.minecraft.minecart": "Vagoneta", "item.minecraft.miner_pottery_shard": "Fragment de ceràmica de miner", "item.minecraft.miner_pottery_sherd": "Fragment de ceràmica de miner", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.desc": "Estampat de Mojang", "item.minecraft.mojang_banner_pattern.new": "Estampat de Mojang", "item.minecraft.mooshroom_spawn_egg": "<PERSON><PERSON> mooshroom", "item.minecraft.mourner_pottery_shard": "Fragment de ceràmica de Vigilant", "item.minecraft.mourner_pottery_sherd": "Fragment de ceràmica de Vigilant", "item.minecraft.mule_spawn_egg": "<PERSON>rar mula", "item.minecraft.mushroom_stew": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "item.minecraft.music_disc_11": "Disc de música", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Disc de música", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Disc de música", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Disc de música", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Disc de música", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Disc de música", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Disc de música", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Disc de música", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "Disc de música", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Music Disc", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Disc de música", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Disc de música", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Disc de música", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Disc de música", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Disc de música", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Disc de música", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Disc de música", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Disc de música", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Disc de música", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Disc de música", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Disc de música", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Carn d'ovella crua", "item.minecraft.name_tag": "Etiqueta de nom", "item.minecraft.nautilus_shell": "Closca de nàutil", "item.minecraft.nether_brick": "<PERSON><PERSON>", "item.minecraft.nether_star": "Estrella del Nether", "item.minecraft.nether_wart": "Berruga del Nether", "item.minecraft.netherite_axe": "Destral de netherita", "item.minecraft.netherite_boots": "Botes de netherita", "item.minecraft.netherite_chestplate": "Cuirassa de netherita", "item.minecraft.netherite_helmet": "Casc de netherita", "item.minecraft.netherite_hoe": "Aixada de netherita", "item.minecraft.netherite_ingot": "<PERSON>ot de netherita", "item.minecraft.netherite_leggings": "Malles de netherita", "item.minecraft.netherite_pickaxe": "Pic de netherita", "item.minecraft.netherite_scrap": "<PERSON><PERSON><PERSON> de netherita", "item.minecraft.netherite_shovel": "<PERSON>la de netherita", "item.minecraft.netherite_sword": "Espasa de netherita", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.netherite_upgrade_smithing_template.new": "<PERSON><PERSON> de netherita", "item.minecraft.oak_boat": "Barca de pomer", "item.minecraft.oak_chest_boat": "Barca de pomer amb cofre", "item.minecraft.ocelot_spawn_egg": "<PERSON><PERSON> ocelot", "item.minecraft.ominous_bottle": "<PERSON><PERSON><PERSON> ominosa", "item.minecraft.ominous_trial_key": "<PERSON><PERSON> <PERSON> reptes ominós", "item.minecraft.orange_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.orange_dye": "Tint taron<PERSON>", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.painting": "Quadre", "item.minecraft.pale_oak_boat": "Barca de pomer pàl·lid", "item.minecraft.pale_oak_chest_boat": "Barca de pomer pàl·lid amb cofre", "item.minecraft.panda_spawn_egg": "<PERSON>rar panda", "item.minecraft.paper": "Paper", "item.minecraft.parrot_spawn_egg": "<PERSON><PERSON> ll<PERSON>", "item.minecraft.phantom_membrane": "Membrana fantasmal", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON> fantasma", "item.minecraft.pig_spawn_egg": "Generar porc", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "Estampat de musell", "item.minecraft.piglin_brute_spawn_egg": "Generar piglin ferotge", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON> piglin", "item.minecraft.pillager_spawn_egg": "<PERSON><PERSON> sa<PERSON>", "item.minecraft.pink_bundle": "<PERSON><PERSON><PERSON> rosa", "item.minecraft.pink_dye": "<PERSON>t rosa", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.pitcher_plant": "<PERSON><PERSON> gerro", "item.minecraft.pitcher_pod": "<PERSON>ina de planta gerro", "item.minecraft.plenty_pottery_shard": "Fragment de ceràmica de cofre", "item.minecraft.plenty_pottery_sherd": "Fragment de ceràmica de cofre", "item.minecraft.poisonous_potato": "Patata verinosa", "item.minecraft.polar_bear_spawn_egg": "Generar ós polar", "item.minecraft.popped_chorus_fruit": "Fruita de tornada cuita", "item.minecraft.porkchop": "Carn de porc crua", "item.minecraft.potato": "Pat<PERSON>", "item.minecraft.potion": "Poció", "item.minecraft.potion.effect.awkward": "Poció estranya", "item.minecraft.potion.effect.empty": "Poció impossible d'elaborar", "item.minecraft.potion.effect.fire_resistance": "Poció de resistència ígnia", "item.minecraft.potion.effect.harming": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.healing": "Poció de curació", "item.minecraft.potion.effect.infested": "Poció d'infestació", "item.minecraft.potion.effect.invisibility": "Poció d'invisibilitat", "item.minecraft.potion.effect.leaping": "Poció de salt", "item.minecraft.potion.effect.levitation": "Poció de levitació", "item.minecraft.potion.effect.luck": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.mundane": "<PERSON><PERSON><PERSON> mundana", "item.minecraft.potion.effect.night_vision": "Poció de visió nocturna", "item.minecraft.potion.effect.oozing": "Poció de supura", "item.minecraft.potion.effect.poison": "<PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.potion.effect.regeneration": "Poció de regeneració", "item.minecraft.potion.effect.slow_falling": "Poció de caiguda lenta", "item.minecraft.potion.effect.slowness": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.strength": "Poció de força", "item.minecraft.potion.effect.swiftness": "Poció de velocitat", "item.minecraft.potion.effect.thick": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.turtle_master": "Poció del Mestre tortuga", "item.minecraft.potion.effect.water": "Ampolla d'aigua", "item.minecraft.potion.effect.water_breathing": "Poció de respiració aquàtica", "item.minecraft.potion.effect.weakness": "Poció debilitant", "item.minecraft.potion.effect.weaving": "Poció de teixidura", "item.minecraft.potion.effect.wind_charged": "Poció de vent carregat", "item.minecraft.pottery_shard_archer": "Fragment de ceràmica d'arquer", "item.minecraft.pottery_shard_arms_up": "Fragment de ceràmica de ninot", "item.minecraft.pottery_shard_prize": "Fragment de ceràmica de joia", "item.minecraft.pottery_shard_skull": "Fragment de ceràmica de calavera", "item.minecraft.powder_snow_bucket": "Galleda de neu pols", "item.minecraft.prismarine_crystals": "Cristalls de prismarina", "item.minecraft.prismarine_shard": "Fragment de prismarina", "item.minecraft.prize_pottery_shard": "Fragment de ceràmica de joia", "item.minecraft.prize_pottery_sherd": "Fragment de ceràmica de joia", "item.minecraft.pufferfish": "Peix globus", "item.minecraft.pufferfish_bucket": "Galleda amb peix globus", "item.minecraft.pufferfish_spawn_egg": "Generar peix globus", "item.minecraft.pumpkin_pie": "Pastís de carbassa", "item.minecraft.pumpkin_seeds": "Llavors de carbassa", "item.minecraft.purple_bundle": "Sarró lila", "item.minecraft.purple_dye": "Tint lila", "item.minecraft.purple_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.quartz": "<PERSON><PERSON><PERSON> del <PERSON>her", "item.minecraft.rabbit": "Carn de conill crua", "item.minecraft.rabbit_foot": "Pota de conill", "item.minecraft.rabbit_hide": "<PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON> conill", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.raiser_armor_trim_smithing_template.new": "Disseny d'armadura d'elevació", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON>", "item.minecraft.raw_copper": "Coure en brut", "item.minecraft.raw_gold": "Or en brut", "item.minecraft.raw_iron": "Ferro en brut", "item.minecraft.recovery_compass": "Brúixola de retorn", "item.minecraft.red_bundle": "<PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.red_dye": "<PERSON><PERSON> vermell", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.redstone": "Pols de redstone", "item.minecraft.resin_brick": "Maons de resina", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON> de resina", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>'arm<PERSON> de <PERSON>", "item.minecraft.rotten_flesh": "Carn podrida", "item.minecraft.saddle": "<PERSON><PERSON> de munta<PERSON>", "item.minecraft.salmon": "Salmó cru", "item.minecraft.salmon_bucket": "Galleda amb salmó", "item.minecraft.salmon_spawn_egg": "<PERSON><PERSON>", "item.minecraft.scrape_pottery_sherd": "Fragment de ceràmica de destral", "item.minecraft.scute": "Escata", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.sentry_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>'arm<PERSON> de sentine<PERSON>", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> d'armadura de modelador", "item.minecraft.sheaf_pottery_shard": "Fragment de ceràmica de palla", "item.minecraft.sheaf_pottery_sherd": "Fragment de ceràmica de blat", "item.minecraft.shears": "Tisores d'esquilar", "item.minecraft.sheep_spawn_egg": "<PERSON><PERSON> ovella", "item.minecraft.shelter_pottery_shard": "Fragment de ceràmica de refugi", "item.minecraft.shelter_pottery_sherd": "Fragment de ceràmica de refugi", "item.minecraft.shield": "Escut", "item.minecraft.shield.black": "Escut negre", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON> blau", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON> marró", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON> cian", "item.minecraft.shield.gray": "Escut gris", "item.minecraft.shield.green": "Escut verd", "item.minecraft.shield.light_blue": "Escut blau cel", "item.minecraft.shield.light_gray": "Escut gris clar", "item.minecraft.shield.lime": "Escut llima", "item.minecraft.shield.magenta": "Escut magenta", "item.minecraft.shield.orange": "Escut taronja", "item.minecraft.shield.pink": "Escut rosa", "item.minecraft.shield.purple": "Escut lila", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON> vermell", "item.minecraft.shield.white": "Escut blanc", "item.minecraft.shield.yellow": "Escut groc", "item.minecraft.shulker_shell": "Closca <PERSON>", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON>", "item.minecraft.sign": "<PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.silence_armor_trim_smithing_template.new": "Disseny d'armadura de silenci", "item.minecraft.silverfish_spawn_egg": "<PERSON>rar peixet de plata", "item.minecraft.skeleton_horse_spawn_egg": "<PERSON><PERSON> cavall esquel<PERSON>", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON> esquelet", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON>ó <PERSON>", "item.minecraft.skull_banner_pattern.desc": "Estampat de calavera", "item.minecraft.skull_banner_pattern.new": "Estampat de calavera", "item.minecraft.skull_pottery_shard": "Fragment de ceràmica de calavera", "item.minecraft.skull_pottery_sherd": "Fragment de ceràmica de calavera", "item.minecraft.slime_ball": "<PERSON><PERSON>", "item.minecraft.slime_spawn_egg": "<PERSON><PERSON> llim", "item.minecraft.smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.smithing_template.applies_to": "S'aplica a:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Afegeix un lingot o un cristall", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Afegeix una peça d'armadura", "item.minecraft.smithing_template.armor_trim.ingredients": "Lingots i cristalls", "item.minecraft.smithing_template.ingredients": "Ingredients:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Afegeix un lingot de netherita", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Armament de diamant", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Afegeix una armadura, arma o eina de diamant", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "<PERSON>ot de netherita", "item.minecraft.smithing_template.upgrade": "Millorar: ", "item.minecraft.sniffer_spawn_egg": "<PERSON><PERSON> ensumad<PERSON>", "item.minecraft.snort_pottery_shard": "Fragment de ceràmica d'ensumador", "item.minecraft.snort_pottery_sherd": "Fragment de ceràmica d'ensumador", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.snout_armor_trim_smithing_template.new": "Disseny d'armadura de musell", "item.minecraft.snow_golem_spawn_egg": "Generar gòlem de neu", "item.minecraft.snowball": "<PERSON><PERSON>u", "item.minecraft.spectral_arrow": "Fletxa fantasmal", "item.minecraft.spider_eye": "Ull d'aranya", "item.minecraft.spider_spawn_egg": "<PERSON><PERSON> a<PERSON>", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.spire_armor_trim_smithing_template.new": "Disseny d'armadura d'a<PERSON>", "item.minecraft.splash_potion": "Poció llancívola", "item.minecraft.splash_potion.effect.awkward": "Poció estranya llancívola", "item.minecraft.splash_potion.effect.empty": "Poció llancívola impossible d'elaborar", "item.minecraft.splash_potion.effect.fire_resistance": "Poció de resistència ígnia llancívola", "item.minecraft.splash_potion.effect.harming": "Poció dany<PERSON> llancívola", "item.minecraft.splash_potion.effect.healing": "Poció de curació llancívola", "item.minecraft.splash_potion.effect.infested": "Poció d'infestació llancívola", "item.minecraft.splash_potion.effect.invisibility": "Poció d'invisibilitat llancívola", "item.minecraft.splash_potion.effect.leaping": "Poció de salt llancívola", "item.minecraft.splash_potion.effect.levitation": "Poció de levitació llancívola", "item.minecraft.splash_potion.effect.luck": "Poció sortosa llancívola", "item.minecraft.splash_potion.effect.mundane": "Poció mundana llancívola", "item.minecraft.splash_potion.effect.night_vision": "Poció de visió nocturna llancívola", "item.minecraft.splash_potion.effect.oozing": "Poció de supura llancívola", "item.minecraft.splash_potion.effect.poison": "Poció verinosa llancívola", "item.minecraft.splash_potion.effect.regeneration": "Poció de regeneració llancívola", "item.minecraft.splash_potion.effect.slow_falling": "Poció llancívola de caiguda lenta", "item.minecraft.splash_potion.effect.slowness": "Poció alentidora llancívola", "item.minecraft.splash_potion.effect.strength": "Poció de força llancívola", "item.minecraft.splash_potion.effect.swiftness": "Poció de velocitat llancívola", "item.minecraft.splash_potion.effect.thick": "Poció densa llancí<PERSON>la", "item.minecraft.splash_potion.effect.turtle_master": "Poció llancívola del Mestre tortuga", "item.minecraft.splash_potion.effect.water": "Ampolla d'aigua llancívola", "item.minecraft.splash_potion.effect.water_breathing": "Poció de respiració aquàtica llancívola", "item.minecraft.splash_potion.effect.weakness": "Poció debilitant llancívola", "item.minecraft.splash_potion.effect.weaving": "Poció de teixidura llancívola", "item.minecraft.splash_potion.effect.wind_charged": "Poció de carregada de vent llancívola", "item.minecraft.spruce_boat": "Barca de pi", "item.minecraft.spruce_chest_boat": "Barca de pi amb cofre", "item.minecraft.spyglass": "Ullera de llarga vista", "item.minecraft.squid_spawn_egg": "<PERSON><PERSON> calamar", "item.minecraft.stick": "Pal", "item.minecraft.stone_axe": "Destral de pedra", "item.minecraft.stone_hoe": "Aixada de pedra", "item.minecraft.stone_pickaxe": "<PERSON><PERSON> de <PERSON>edra", "item.minecraft.stone_shovel": "<PERSON><PERSON> de pedra", "item.minecraft.stone_sword": "Espasa de pedra", "item.minecraft.stray_spawn_egg": "Generar esquelet extraviat", "item.minecraft.strider_spawn_egg": "Generar caminant", "item.minecraft.string": "Fil", "item.minecraft.sugar": "<PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON><PERSON><PERSON><PERSON>sp<PERSON>", "item.minecraft.sweet_berries": "<PERSON><PERSON> do<PERSON>", "item.minecraft.tadpole_bucket": "Galleda amb capgròs", "item.minecraft.tadpole_spawn_egg": "<PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.tide_armor_trim_smithing_template.new": "Disseny d'armadura de la marea", "item.minecraft.tipped_arrow": "Fletxa amb efecte", "item.minecraft.tipped_arrow.effect.awkward": "Fletxa amb efecte", "item.minecraft.tipped_arrow.effect.empty": "Fletxa amb efecte impossible d'elaborar", "item.minecraft.tipped_arrow.effect.fire_resistance": "Fletxa de resistència ígnia", "item.minecraft.tipped_arrow.effect.harming": "Fletxa de dany augmentat", "item.minecraft.tipped_arrow.effect.healing": "Fletxa curativa", "item.minecraft.tipped_arrow.effect.infested": "Fletxa d'infestació", "item.minecraft.tipped_arrow.effect.invisibility": "Fletxa d'invisibilitat", "item.minecraft.tipped_arrow.effect.leaping": "Fletxa de salt", "item.minecraft.tipped_arrow.effect.levitation": "Fletxa de levitació", "item.minecraft.tipped_arrow.effect.luck": "Fletxa sortosa", "item.minecraft.tipped_arrow.effect.mundane": "Fletxa amb efecte", "item.minecraft.tipped_arrow.effect.night_vision": "Fletxa de visió nocturna", "item.minecraft.tipped_arrow.effect.oozing": "Fletxa de supura", "item.minecraft.tipped_arrow.effect.poison": "Fletxa de verí", "item.minecraft.tipped_arrow.effect.regeneration": "Fletxa de regeneració", "item.minecraft.tipped_arrow.effect.slow_falling": "Fletxa de caiguda lenta", "item.minecraft.tipped_arrow.effect.slowness": "Fletxa alentidora", "item.minecraft.tipped_arrow.effect.strength": "Fletxa de força", "item.minecraft.tipped_arrow.effect.swiftness": "Fletxa de velocitat", "item.minecraft.tipped_arrow.effect.thick": "Fletxa amb efecte", "item.minecraft.tipped_arrow.effect.turtle_master": "Fletxa del Mestre tortuga", "item.minecraft.tipped_arrow.effect.water": "Fletxa esquitxadora", "item.minecraft.tipped_arrow.effect.water_breathing": "Fletxa de respiració aquàtica", "item.minecraft.tipped_arrow.effect.weakness": "Fletxa debilitant", "item.minecraft.tipped_arrow.effect.weaving": "Fletxa de teixidura", "item.minecraft.tipped_arrow.effect.wind_charged": "Fletxa de vent carregat", "item.minecraft.tnt_minecart": "Vagoneta amb dinamita", "item.minecraft.torchflower_seeds": "Llavors de florxa", "item.minecraft.totem_of_undying": "Tòtem de l'immortalitat", "item.minecraft.trader_llama_spawn_egg": "<PERSON>rar llama ambulant", "item.minecraft.trial_key": "<PERSON><PERSON> <PERSON>", "item.minecraft.trident": "Trident", "item.minecraft.tropical_fish": "Peix tropical", "item.minecraft.tropical_fish_bucket": "Galleda amb peix tropical", "item.minecraft.tropical_fish_spawn_egg": "Generar peix tropical", "item.minecraft.turtle_helmet": "Closca de tortuga", "item.minecraft.turtle_scute": "Escata de tortuga", "item.minecraft.turtle_spawn_egg": "<PERSON><PERSON> tortuga", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.vex_armor_trim_smithing_template.new": "Disseny d'armadura d'esperit", "item.minecraft.vex_spawn_egg": "Generar esperit", "item.minecraft.villager_spawn_egg": "Generar vilatà", "item.minecraft.vindicator_spawn_egg": "Generar vindicador", "item.minecraft.wandering_trader_spawn_egg": "<PERSON>rar comerciant", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.ward_armor_trim_smithing_template.new": "Disseny d'armadura de vigilant", "item.minecraft.warden_spawn_egg": "<PERSON><PERSON> vigilant", "item.minecraft.warped_fungus_on_a_stick": "<PERSON><PERSON> amb fong guerxo", "item.minecraft.water_bucket": "Galleda d'aigua", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Disseny d'armadura de cerca camins", "item.minecraft.wheat": "<PERSON><PERSON>", "item.minecraft.wheat_seeds": "<PERSON><PERSON><PERSON> de blat", "item.minecraft.white_bundle": "<PERSON><PERSON><PERSON> blanc", "item.minecraft.white_dye": "<PERSON><PERSON> blanc", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON><PERSON>er<PERSON>", "item.minecraft.wild_armor_trim_smithing_template.new": "Disseny d'armadura salvatge", "item.minecraft.wind_charge": "Càrrega Ventosa", "item.minecraft.witch_spawn_egg": "Generar bruixa", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON> esquelet wither", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON> wither", "item.minecraft.wolf_armor": "<PERSON><PERSON><PERSON> de ll<PERSON>", "item.minecraft.wolf_spawn_egg": "<PERSON><PERSON> llop", "item.minecraft.wooden_axe": "Destral de fusta", "item.minecraft.wooden_hoe": "Aixada de fusta", "item.minecraft.wooden_pickaxe": "Pic de fusta", "item.minecraft.wooden_shovel": "Pala de fusta", "item.minecraft.wooden_sword": "Espasa de fusta", "item.minecraft.writable_book": "Llibre i tinter", "item.minecraft.written_book": "Llibre escrit", "item.minecraft.yellow_bundle": "<PERSON><PERSON><PERSON> groc", "item.minecraft.yellow_dye": "Tint groc", "item.minecraft.yellow_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON>lin", "item.minecraft.zombie_horse_spawn_egg": "Generar cavall zombi", "item.minecraft.zombie_spawn_egg": "<PERSON><PERSON> z<PERSON>", "item.minecraft.zombie_villager_spawn_egg": "Generar vilatà zombi", "item.minecraft.zombified_piglin_spawn_egg": "Generar piglin zombificat", "item.modifiers.any": "Quan s'equipa:", "item.modifiers.armor": "En el cos:", "item.modifiers.body": "Quan s'equipa:", "item.modifiers.chest": "Al cos:", "item.modifiers.feet": "<PERSON><PERSON> peus:", "item.modifiers.hand": "Mentre es manté:", "item.modifiers.head": "Al cap:", "item.modifiers.legs": "A les cames:", "item.modifiers.mainhand": "A la mà principal:", "item.modifiers.offhand": "A la mà secundària:", "item.modifiers.saddle": "En equipar muntura:", "item.nbt_tags": "NBT: %s etiqueta(es)", "item.op_block_warning.line1": "Advertència:", "item.op_block_warning.line2": "Podrien executar-se comandaments amb aquest bloc", "item.op_block_warning.line3": "No el facis servir a menys que coneguis el seu interior!", "item.unbreakable": "Indestructible", "itemGroup.buildingBlocks": "Blocs de construcció", "itemGroup.coloredBlocks": "Blocs de colors", "itemGroup.combat": "Combat", "itemGroup.consumables": "Consumibles", "itemGroup.crafting": "Elaboració", "itemGroup.foodAndDrink": "Ali<PERSON> i begudes", "itemGroup.functional": "Blocs funcionals", "itemGroup.hotbar": "Barres d'eines desades", "itemGroup.ingredients": "Ingredients", "itemGroup.inventory": "Inventari del mode supervivència", "itemGroup.natural": "Blocs naturals", "itemGroup.op": "Eines d'administradors", "itemGroup.redstone": "Redstone", "itemGroup.search": "Buscar ítems", "itemGroup.spawnEggs": "<PERSON>us de generació", "itemGroup.tools": "<PERSON><PERSON>", "item_modifier.unknown": "Modificador d'ítem desconegut: %s", "jigsaw_block.final_state": "Es transforma en:", "jigsaw_block.generate": "Genera", "jigsaw_block.joint.aligned": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint.rollable": "Rotable", "jigsaw_block.joint_label": "Tipus d'unió:", "jigsaw_block.keep_jigsaws": "Mantenir blocs trencaclosques", "jigsaw_block.levels": "Nivells: %s", "jigsaw_block.name": "Nom:", "jigsaw_block.placement_priority": "Prioritat de col·locació:", "jigsaw_block.placement_priority.tooltip": "Quan aquest bloc trencaclosques es connecta a una peça, aquest és l'ordre en el qual la peça és processada per connexions en l'estructura més amplia.\n\nPeces seran processades en prioritat descendent amb l'ordre d'inserció de llaços trencats.", "jigsaw_block.pool": "Grup objectiu:", "jigsaw_block.selection_priority": "Prioritat de selecció:", "jigsaw_block.selection_priority.tooltip": "Quan la peça pare s'està processant per connexions, aquest és l'ordre en el qual aquest bloc trencaclosques atempta de connectar-se a l'objectiu.\n\nTrencaclosques seran processats de prioritat descendent amb ordres aleatòries trencant llaços.", "jigsaw_block.target": "Nom de l'objectiu:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON><PERSON> (Music Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Avenços", "key.attack": "Ataca<PERSON>/destruir", "key.back": "Caminar en<PERSON>e", "key.categories.creative": "Mode creatiu", "key.categories.gameplay": "Jugabilitat", "key.categories.inventory": "Inventari", "key.categories.misc": "Diversos", "key.categories.movement": "Moviment", "key.categories.multiplayer": "Multijugador", "key.categories.ui": "Interfície de joc", "key.chat": "Obrir el xat", "key.command": "Obrir comandament", "key.drop": "Llençar l'ítem seleccionat", "key.forward": "Caminar endavan<PERSON>", "key.fullscreen": "Des/activa pantalla completa", "key.hotbar.1": "Tecla ràpida 1", "key.hotbar.2": "Tecla ràpida 2", "key.hotbar.3": "Tecla ràpida 3", "key.hotbar.4": "Tecla ràpida 4", "key.hotbar.5": "Tecla ràpida 5", "key.hotbar.6": "Tecla ràpida 6", "key.hotbar.7": "Tecla ràpida 7", "key.hotbar.8": "Tecla ràpida 8", "key.hotbar.9": "Tecla ràpida 9", "key.inventory": "Obrir/Tancar inventari", "key.jump": "Saltar", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.caps.lock": "<PERSON><PERSON><PERSON> maj<PERSON>", "key.keyboard.comma": ",", "key.keyboard.delete": "Suprimeix", "key.keyboard.down": "Fletxa cap avall", "key.keyboard.end": "Fi", "key.keyboard.enter": "Intro", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Inici", "key.keyboard.insert": "Inserir", "key.keyboard.keypad.0": "0 (TN)", "key.keyboard.keypad.1": "1 (TN)", "key.keyboard.keypad.2": "2 (TN)", "key.keyboard.keypad.3": "3 (TN)", "key.keyboard.keypad.4": "4 (TN)", "key.keyboard.keypad.5": "5 (TN)", "key.keyboard.keypad.6": "6 (TN)", "key.keyboard.keypad.7": "7 (TN)", "key.keyboard.keypad.8": "8 (TN)", "key.keyboard.keypad.9": "9 (TN)", "key.keyboard.keypad.add": "+ (TN)", "key.keyboard.keypad.decimal": ". (TN)", "key.keyboard.keypad.divide": "/ (TN)", "key.keyboard.keypad.enter": "Intro (TN)", "key.keyboard.keypad.equal": "= (TN)", "key.keyboard.keypad.multiply": "* (TN)", "key.keyboard.keypad.subtract": "- (TN)", "key.keyboard.left": "Fletxa cap a l'esquerra", "key.keyboard.left.alt": "Alt es<PERSON>re", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Control esq.", "key.keyboard.left.shift": "<PERSON><PERSON> esq.", "key.keyboard.left.win": "Windows de l'esquerra", "key.keyboard.menu": "Menú", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Bloq <PERSON>ú<PERSON>", "key.keyboard.page.down": "Av Pàg", "key.keyboard.page.up": "<PERSON>", "key.keyboard.pause": "<PERSON><PERSON>", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON><PERSON><PERSON>", "key.keyboard.right": "Fletxa cap a la dreta", "key.keyboard.right.alt": "Alt dret", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Control dret", "key.keyboard.right.shift": "<PERSON><PERSON> d<PERSON>", "key.keyboard.right.win": "Windows de la dreta", "key.keyboard.scroll.lock": "Bloq Despl", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Espai", "key.keyboard.tab": "Tabulador", "key.keyboard.unknown": "Sense enllaç", "key.keyboard.up": "Fletxa cap amunt", "key.keyboard.world.1": "Món 1", "key.keyboard.world.2": "Món 2", "key.left": "Desplaçament lateral esquerra", "key.loadToolbarActivator": "Carrega l'activador de la barra d'eines", "key.mouse": "Botó %1$s", "key.mouse.left": "<PERSON><PERSON> esquerre", "key.mouse.middle": "Clic central", "key.mouse.right": "Clic dret", "key.pickItem": "Agafar bloc", "key.playerlist": "Llista de Jugadors", "key.quickActions": "Quick Actions", "key.right": "Desplaçament lateral dret", "key.saveToolbarActivator": "Desa l'activador de la barra d'eines", "key.screenshot": "Captura de pantalla", "key.smoothCamera": "Canviar càmera cinemàtica", "key.sneak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.socialInteractions": "Pantalla d'interaccions socials", "key.spectatorOutlines": "Destacar juga<PERSON> (espectadors)", "key.sprint": "<PERSON><PERSON><PERSON>", "key.swapOffhand": "Intercanviar l'objecte amb la mà secundària", "key.togglePerspective": "Canviar perspectiva", "key.use": "Utilitzar ítem/col·locar bloc", "known_server_link.announcements": "<PERSON><PERSON><PERSON>", "known_server_link.community": "Comunitat", "known_server_link.community_guidelines": "<PERSON><PERSON> de la comunitat", "known_server_link.feedback": "<PERSON><PERSON><PERSON>", "known_server_link.forums": "Fòrums", "known_server_link.news": "Notícies", "known_server_link.report_bug": "Informa d'un error del servidor", "known_server_link.status": "Estat", "known_server_link.support": "Suport", "known_server_link.website": "Pàgina web", "lanServer.otherPlayers": "Configuració pels altres jugadors", "lanServer.port": "Nombre de port", "lanServer.port.invalid": "El port no és vàlid.\nBuida la caixa de text o escriu un nombre entre 1024 i 65535.", "lanServer.port.invalid.new": "El port no és vàlid.\nBuida la caixa de text o escriu un nombre entre %s i %s.", "lanServer.port.unavailable": "Port no disponible.\nBuida la caixa de text o escriu un nombre diferent entre 1024 i 65535.", "lanServer.port.unavailable.new": "Port no disponible.\nBuida la caixa de text o escriu un nombre diferent entre %s i %s.", "lanServer.scanning": "S'estan buscant mons a la teva xarxa local", "lanServer.start": "Començar món LAN", "lanServer.title": "Món LAN", "language.code": "cat_ES", "language.name": "Català", "language.region": "Catalunya", "lectern.take_book": "Retira el llibre", "loading.progress": "%s%%", "mco.account.privacy.info": "Llegir més sobre Mojang i lleis de privacitat", "mco.account.privacy.info.button": "Llegeix més sobre GDPR", "mco.account.privacy.information": "Mojang implementa certs procediments per ajudar a protegir als menors i la seva privacitat, que consisteixen en complir amb la Llei de Protecció de Privacitat Infantil a Internet (COPPA) i el Reglament General de Protecció de Dades (RGPD).\n\nHas de tenir consentiment parental abans de poder tenir accés al teu compte de Realms.", "mco.account.privacyinfo": "Mojang implementa certs procediments per ajudar a protegir als menors i la seva privacitat, incloent complir amb l'Acte per la Protecció de la Privacitat Infantil a Internet (COPPA) i el Reglament General de Protecció de Dades (RGPD).\n\nPot ser necessari obtenir consentiment parental abans de poder tenir accés al teu compte de Realms.\n\nSi tens un compte de Minecraft antic (inicies sessió amb el teu nom d'usuari), hauràs de migrar el teu compte a un de Mojang per poder accedir a Realms.", "mco.account.update": "Actualitza el compte", "mco.activity.noactivity": "Cap activitat durant el(s) darrer(s) %s dia/dies", "mco.activity.title": "Activitat del jugador", "mco.backup.button.download": "Descarregar l'última", "mco.backup.button.reset": "Reiniciar món", "mco.backup.button.restore": "Restaura", "mco.backup.button.upload": "<PERSON><PERSON><PERSON> món", "mco.backup.changes.tooltip": "<PERSON><PERSON>", "mco.backup.entry": "Còpia de seguretat (%s)", "mco.backup.entry.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.enabledPack": "Paquet(s) activat(s)", "mco.backup.entry.gameDifficulty": "Dificultat del joc", "mco.backup.entry.gameMode": "Mode de joc", "mco.backup.entry.gameServerVersion": "Versió del servidor del joc", "mco.backup.entry.name": "Nom", "mco.backup.entry.seed": "<PERSON><PERSON><PERSON>", "mco.backup.entry.templateName": "Nom de la plantilla", "mco.backup.entry.undefined": "<PERSON>vi no definit", "mco.backup.entry.uploaded": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.worldType": "Tipus de món", "mco.backup.generate.world": "<PERSON><PERSON> m<PERSON>", "mco.backup.info.title": "Canvis de l'última còpia de seguretat", "mco.backup.narration": "Còpia de seguretat de %s", "mco.backup.nobackups": "Aquest realm no disposa de cap còpia de seguretat actualment.", "mco.backup.restoring": "Restaurant el teu realm", "mco.backup.unknown": "DESCONEGUT", "mco.brokenworld.download": "<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "Si us plau, reinicieu o seleccioneu un altre món.", "mco.brokenworld.message.line2": "També pots descarregar el món per a un jugador.", "mco.brokenworld.minigame.title": "Aquest minijoc ja no és compatible", "mco.brokenworld.nonowner.error": "Si us plau, espera fins que el propietari del realm reiniciï el món", "mco.brokenworld.nonowner.title": "El món està antiquat", "mco.brokenworld.play": "<PERSON><PERSON>", "mco.brokenworld.reset": "Reiniciar", "mco.brokenworld.title": "El teu món actual ja no és compatible", "mco.client.incompatible.msg.line1": "El teu client no és compatible amb Minecraft Realms.", "mco.client.incompatible.msg.line2": "Si us plau, utilitza la versió més recent de Minecraft.", "mco.client.incompatible.msg.line3": "Realms no és compatible amb les versions de prova (snapshots).", "mco.client.incompatible.title": "Client incompatible!", "mco.client.outdated.stable.version": "La teva versió del client (%s) no és compatible amb Realms.\n\nSi us plau, utilitza la versió més recent de Minecraft.", "mco.client.unsupported.snapshot.version": "La teva versió del client (%s) no és compatible amb Realms.\n\nRealms no és disponible per aquesta versió de prova.", "mco.compatibility.downgrade": "Degradar", "mco.compatibility.downgrade.description": "Aquest món era últim jugat en la versió %s; estàs a la versió %s. Carregant un món en una versió anterior el pot corrompre - no et podem assegurar que es carregui o funcioni. \n\nUna còpia de seguretat del món serà desada en \"Còpies de seguretat\". Si us plau, restaura el teu món si és necessari.", "mco.compatibility.incompatible.popup.title": "Versió incompatible", "mco.compatibility.incompatible.releaseType.popup.message": "El món que t'estàs intentant unir no és compatible amb la teva versió del joc.", "mco.compatibility.incompatible.series.popup.message": "Aquest món era últim jugat en la versió %s; estàs en la versió %s.\n\nAquestes sèries no són compatibles amb un a l'altre. Necessites un nou món per jugar-hi en aquesta versió.", "mco.compatibility.unverifiable.message": "L'última versió en què aquest món es va jugar no s'ha pogut verificar. Si el món és actualitzat o rebaixat, una còpia de seguretat es crearà automàticament i desat sota \"Còpies de seguretat\".", "mco.compatibility.unverifiable.title": "Comptabilitat no verificable", "mco.compatibility.upgrade": "<PERSON><PERSON><PERSON><PERSON>", "mco.compatibility.upgrade.description": "Aquest món era últim jugat en la versió %s; estàs en la versió %s.\n\nUna còpia de seguretat del teu món es desarà sota \"Còpies de seguretat\". Si us plau, restaura el teu món si cal.", "mco.compatibility.upgrade.friend.description": "Aquest món era últim jugat en la versió %s; estàs en la versió %s.\n\nUna còpia de seguretat del món es desarà sota \"Còpies de seguretat\". \n\nL'amo del Realm pot restaurar el món si cal.", "mco.compatibility.upgrade.title": "Se<PERSON>r que vols actualitzar aquest món?", "mco.configure.current.minigame": "Actual", "mco.configure.world.activityfeed.disabled": "El resum de l'activitat està desactivat temporalment", "mco.configure.world.backup": "Còpies de seguretat del món", "mco.configure.world.buttons.activity": "Activitat dels jugadors", "mco.configure.world.buttons.close": "Tanca el realm", "mco.configure.world.buttons.delete": "Eliminar", "mco.configure.world.buttons.done": "Fet", "mco.configure.world.buttons.edit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.invite": "Convida un jugador", "mco.configure.world.buttons.moreoptions": "Més opcions", "mco.configure.world.buttons.newworld": "Nou món", "mco.configure.world.buttons.open": "Obre el realm", "mco.configure.world.buttons.options": "Opcions del món", "mco.configure.world.buttons.players": "Jugadors", "mco.configure.world.buttons.region_preference": "Selecciona una regió...", "mco.configure.world.buttons.resetworld": "Reiniciar món", "mco.configure.world.buttons.save": "Desar", "mco.configure.world.buttons.settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.subscription": "Subscripció", "mco.configure.world.buttons.switchminigame": "Canviar de Minijoc", "mco.configure.world.close.question.line1": "El teu 'Realm' deixarà d'estar disponible.", "mco.configure.world.close.question.line2": "Segur que vols fer això?", "mco.configure.world.close.question.title": "Necessites fer canvis sense cap interrupció?", "mco.configure.world.closing": "Tancant el 'Realm'...", "mco.configure.world.commandBlocks": "Blocs de comandaments", "mco.configure.world.delete.button": "Eliminar Realm", "mco.configure.world.delete.question.line1": "El teu realm s'esborrarà permanentment", "mco.configure.world.delete.question.line2": "Segur que vols fer això?", "mco.configure.world.description": "Descripció del Realm", "mco.configure.world.edit.slot.name": "Nom del món", "mco.configure.world.edit.subscreen.adventuremap": "Algunes opcions estàn desactivades ja que el món actual és un mapa d'aventures", "mco.configure.world.edit.subscreen.experience": "Algunes configuracions estàn desactivades ja que el món actual és una experiència", "mco.configure.world.edit.subscreen.inspiration": "Algunes opcions no estan disponibles degut a que el món actual és només una font d'inspiració", "mco.configure.world.forceGameMode": "Forçar el mode de joc", "mco.configure.world.invite.narration": "Tens %s invitació/invitacions nova/noves", "mco.configure.world.invite.profile.name": "Nom", "mco.configure.world.invited": "Convidat", "mco.configure.world.invited.number": "Convidat (%s)", "mco.configure.world.invites.normal.tooltip": "Jugador normal", "mco.configure.world.invites.ops.tooltip": "Administrador", "mco.configure.world.invites.remove.tooltip": "Eliminar", "mco.configure.world.leave.question.line1": "Si abandones aquest 'Realm' no el tornaràs a veure a no ser que se't torni a convidar", "mco.configure.world.leave.question.line2": "Segur que vols fer això?", "mco.configure.world.loading": "Carregant Realm", "mco.configure.world.location": "Localització", "mco.configure.world.minigame": "Actual: %s", "mco.configure.world.name": "Nom del Realm", "mco.configure.world.opening": "Obrint el Realm...", "mco.configure.world.players.error": "No existeix cap jugador amb aquest nom", "mco.configure.world.players.inviting": "Convidant jugador...", "mco.configure.world.players.title": "Jugadors", "mco.configure.world.pvp": "Combat entre jugadors", "mco.configure.world.region_preference": "Preferència de regió", "mco.configure.world.region_preference.title": "Selecció de preferència de regió", "mco.configure.world.reset.question.line1": "El teu món es tornarà a generar i es perdrà el teu món actual", "mco.configure.world.reset.question.line2": "Segur que vols fer això?", "mco.configure.world.resourcepack.question": "Necessites un paquet de recursos personalitzat per jugar en aquest realm\n\nVols descarregar-lo i jugar?", "mco.configure.world.resourcepack.question.line1": "Necessites un paquet de recursos personalitzat per poder jugar aquest realm", "mco.configure.world.resourcepack.question.line2": "Vols descarregar-lo i jugar?", "mco.configure.world.restore.download.question.line1": "Aquest món serà descarregat i afegit als teus mons de joc individual.", "mco.configure.world.restore.download.question.line2": "Vols continuar?", "mco.configure.world.restore.question.line1": "El teu món serà restaurat a la data '%s' (%s)", "mco.configure.world.restore.question.line2": "Segur que vols fer això?", "mco.configure.world.settings.expired": "No pots editar la configuració d'un Realm vençut", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot": "Món %s", "mco.configure.world.slot.empty": "<PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "Es canviarà el seu reialme a un altre món", "mco.configure.world.slot.switch.question.line2": "Segur que vols fer això?", "mco.configure.world.slot.tooltip": "Canvia al món", "mco.configure.world.slot.tooltip.active": "Unir-se", "mco.configure.world.slot.tooltip.minigame": "Canviar a un minijoc", "mco.configure.world.spawnAnimals": "Generar animals", "mco.configure.world.spawnMonsters": "Generar monstres", "mco.configure.world.spawnNPCs": "<PERSON>rar vilatans", "mco.configure.world.spawnProtection": "Protecció del punt de reaparició", "mco.configure.world.spawn_toggle.message": "Desactivar aquesta opció eliminarà totes les entitats d'aquest tipus", "mco.configure.world.spawn_toggle.message.npc": "Desactivar aquesta opció eliminarà totes les entitats d'aquest tipus, com els vilatans", "mco.configure.world.spawn_toggle.title": "Atenció!", "mco.configure.world.status": "Estat", "mco.configure.world.subscription.day": "dia", "mco.configure.world.subscription.days": "dies", "mco.configure.world.subscription.expired": "Caducat", "mco.configure.world.subscription.extend": "Ampliar la subscripció", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON> d'un dia", "mco.configure.world.subscription.month": "mes", "mco.configure.world.subscription.months": "mesos", "mco.configure.world.subscription.recurring.daysleft": "Renovat automàticament en", "mco.configure.world.subscription.recurring.info": "Els canvis fets en la teva subscripció als Realms, com ara afegir més temps o desactivar el pagament, no tindran efecte fins a la pròxima data de facturació.", "mco.configure.world.subscription.remaining.days": "%1$s dia/es", "mco.configure.world.subscription.remaining.months": "%1$s mes/os", "mco.configure.world.subscription.remaining.months.days": "%1$s mes/os, %2$s dia/es", "mco.configure.world.subscription.start": "Data d'inici", "mco.configure.world.subscription.tab": "Subscripció", "mco.configure.world.subscription.timeleft": "Temps restant", "mco.configure.world.subscription.title": "Informació de la subscripció", "mco.configure.world.subscription.unknown": "Desconegut", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON> m<PERSON>", "mco.configure.world.switch.slot.subtitle": "Aquest món és buit, si us plau, trieu què fer", "mco.configure.world.title": "Configurar realm:", "mco.configure.world.uninvite.player": "¿Estàs segur que vols desconvidar '%s'?", "mco.configure.world.uninvite.question": "Se<PERSON>r que vols des-convidar", "mco.configure.worlds.title": "Mons", "mco.connect.authorizing": "Identificant-se...", "mco.connect.connecting": "Connectant al Realm...", "mco.connect.failed": "Error en connectar al realm", "mco.connect.region": "Regió del servidor: %s", "mco.connect.success": "Fet", "mco.create.world": "<PERSON><PERSON>", "mco.create.world.error": "Has d'introduir un nom!", "mco.create.world.failed": "No s'ha pogut crear el món!", "mco.create.world.reset.title": "Creant món...", "mco.create.world.skip": "Omet", "mco.create.world.subtitle": "Si vols, selecciona un món per posar-lo al teu nou realm", "mco.create.world.wait": "Creant el 'Realm'...", "mco.download.cancelled": "Descàrrega cancel·lada", "mco.download.confirmation.line1": "El món que vols descarregar supera els %s", "mco.download.confirmation.line2": "No podràs carregar aquest món al teu realm una altra vegada", "mco.download.confirmation.oversized": "El món que vols descarregar supera els %s\n\nNo podràs pujar aquest món al teu realm una altra vegada", "mco.download.done": "Descàrrega finalitzada", "mco.download.downloading": "Descarregant", "mco.download.extracting": "Extraient", "mco.download.failed": "Descàrrega fallada", "mco.download.percent": "%s %%", "mco.download.preparing": "Preparant la descàrrega", "mco.download.resourcePack.fail": "No s'ha pogut descarregar el paquet de recursos!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Descarregant l'últim món", "mco.error.invalid.session.message": "Si us plau, prova a reiniciar Minecraft", "mco.error.invalid.session.title": "<PERSON><PERSON><PERSON>", "mco.errorMessage.6001": "Client antiquat", "mco.errorMessage.6002": "Condicions de Servei no acceptades", "mco.errorMessage.6003": "Límit de descàrrega assolit", "mco.errorMessage.6004": "Límit de <PERSON>rrega assolit", "mco.errorMessage.6005": "<PERSON><PERSON> blo<PERSON>", "mco.errorMessage.6006": "El món està antiquat", "mco.errorMessage.6007": "Usuari està en massa Realms", "mco.errorMessage.6008": "Nom del Realm no vàlid", "mco.errorMessage.6009": "La descripció del Realm no és vàlida", "mco.errorMessage.connectionFailure": "S'ha produït un error, proveu-ho més tard.", "mco.errorMessage.generic": "S'ha produït un error: ", "mco.errorMessage.initialize.failed": "Error en iniciar el Realm", "mco.errorMessage.noDetails": "No s'han proporcionat detalls de l'error", "mco.errorMessage.realmsService": "S'ha produït un error (%s):", "mco.errorMessage.realmsService.configurationError": "S'ha produït un error inesperat mentre s'editaven les opcions del món", "mco.errorMessage.realmsService.connectivity": "No s'ha pogut connectar a Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "No s'ha pogut comprovar la versió compatible, s'ha obtingut la resposta: %s", "mco.errorMessage.retry": "Torna a provar l'operació", "mco.errorMessage.serviceBusy": "Realms està ocupat en aquest moment.\nProva de connectar-te al teu Realm de nou en uns minuts.", "mco.gui.button": "<PERSON><PERSON><PERSON>", "mco.gui.ok": "D'acord", "mco.info": "Informació!", "mco.invited.player.narration": "Jugador %s s'ha convidat", "mco.invites.button.accept": "Accepta", "mco.invites.button.reject": "Rebutjar", "mco.invites.nopending": "Cap invitació pendent!", "mco.invites.pending": "Nova invitació!/Noves invitacions!", "mco.invites.title": "Invitacions pendents", "mco.minigame.world.changeButton": "Selecciona un altre minijoc", "mco.minigame.world.info.line1": "Ai<PERSON>ò substituirà temporalment el teu món per un minijoc!", "mco.minigame.world.info.line2": "Podràs tornar al teu món original sense perdre res.", "mco.minigame.world.noSelection": "Si us plau, fes una selecció", "mco.minigame.world.restore": "Acabant minijoc...", "mco.minigame.world.restore.question.line1": "El minijoc acabarà i el teu 'Realm' serà restaurat.", "mco.minigame.world.restore.question.line2": "Segur que vols fer això?", "mco.minigame.world.selected": "Minijoc seleccionat:", "mco.minigame.world.slot.screen.title": "Canviant el món...", "mco.minigame.world.startButton": "Canvia", "mco.minigame.world.starting.screen.title": "Començant minijoc...", "mco.minigame.world.stopButton": "Acaba el minijoc", "mco.minigame.world.switch.new": "Seleccionar un altre minijoc?", "mco.minigame.world.switch.title": "<PERSON>via de petit joc", "mco.minigame.world.title": "Canvia el realm a minijoc", "mco.news": "Notícies de Minecraft Realms", "mco.notification.dismiss": "Descar<PERSON>", "mco.notification.transferSubscription.buttonText": "Transfereix Ara", "mco.notification.transferSubscription.message": "Les subscripcions de Java Realms s'estan movent al Microsoft Store. No deixis que la teva subscripció s'expiri!\nTransfereix ara i aconsegueix 30 dies de Realms gratuïts. \nVes a la teva pàgina de perfil en minecraft.net per transferir la teva subscripció.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON><PERSON>", "mco.notification.visitUrl.message.default": "Si us plau, visita el següent enllaç", "mco.onlinePlayers": "Jugadors en línia", "mco.play.button.realm.closed": "El Realm està tancat", "mco.question": "Pregunta", "mco.reset.world.adventure": "Aventures", "mco.reset.world.experience": "Experiències", "mco.reset.world.generate": "Nou món", "mco.reset.world.inspiration": "Inspiració", "mco.reset.world.resetting.screen.title": "Reiniciant món...", "mco.reset.world.seed": "Llavor (opcional)", "mco.reset.world.template": "Plantilles del món", "mco.reset.world.title": "Reiniciar món", "mco.reset.world.upload": "<PERSON><PERSON><PERSON> món", "mco.reset.world.warning": "Això substituirà el món actual del teu realm", "mco.selectServer.buy": "Compra un realm!", "mco.selectServer.close": "<PERSON><PERSON>", "mco.selectServer.closed": "Realm tancat", "mco.selectServer.closeserver": "Tancar Realm", "mco.selectServer.configure": "Configurar realm", "mco.selectServer.configureRealm": "Configurar realm", "mco.selectServer.create": "Crear realm", "mco.selectServer.create.subtitle": "Selecciona a un món per posar al teu nou Realm", "mco.selectServer.expired": "Realm esgotat", "mco.selectServer.expiredList": "La teva subscripció ha caducat", "mco.selectServer.expiredRenew": "<PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Subscriure's", "mco.selectServer.expiredTrial": "El teu període de prova s'ha acabat", "mco.selectServer.expires.day": "Caduca en un dia", "mco.selectServer.expires.days": "Caduca en %s dies", "mco.selectServer.expires.soon": "Caduca aviat", "mco.selectServer.leave": "Sortir del <PERSON>", "mco.selectServer.loading": "Carregant la llista de Realms", "mco.selectServer.mapOnlySupportedForVersion": "Aquest mapa no és compatible amb la versió %s", "mco.selectServer.minigame": "Minijoc:", "mco.selectServer.minigameName": "Minijoc: %s", "mco.selectServer.minigameNotSupportedInVersion": "No es pot jugar aquest minijoc amb la versió %s", "mco.selectServer.noRealms": "Pareix que no tens un Realm. Afegeix-ne un per jugar amb els teus amics.", "mco.selectServer.note": "Nota:", "mco.selectServer.open": "Realm obert", "mco.selectServer.openserver": "Obrir realm", "mco.selectServer.play": "<PERSON><PERSON>", "mco.selectServer.popup": "Minecraft Realms és un servei simple i segur per a gaudir d'un món de Minecraft en línia amb fins a 10 amics a la vegada. Suporta a molts minijocs i mons personalitzats! Només cal que pagui el propietari del realm.", "mco.selectServer.purchase": "Afegeix un Realm", "mco.selectServer.trial": "Aconsegueix una prova!", "mco.selectServer.uninitialized": "Fes clic per a crear el teu realm!", "mco.snapshot.createSnapshotPopup.text": "Estàs a punt de crear una versió de prova de Realms gratuïta que estarà aparellat amb la teva subscripció de Realms. Aquesta versió de prova de Realms serà accessible mentre que la subscripció pagada estigui activa. El teu Realm pagat no serà afectat.", "mco.snapshot.createSnapshotPopup.title": "<PERSON><PERSON>r versió de prova de Realm?", "mco.snapshot.creating": "Creant versió de prova de Realm...", "mco.snapshot.description": "Aparellat amb %s", "mco.snapshot.friendsRealm.downgrade": "Necessites estar en la versió %s per unir-te aquest Realm", "mco.snapshot.friendsRealm.upgrade": "%s necessita actualitzar el seu Realm abans que puguis jugar aquesta versió de prova", "mco.snapshot.paired": "Aquesta versió de prova està aparellat amb %s", "mco.snapshot.parent.tooltip": "Utilitza l'última publicació de Minecraft per jugar en aquest Realm", "mco.snapshot.start": "Comença versió de prova de Realm gratuïta", "mco.snapshot.subscription.info": "Això és una versió de prova de Realm que està aparellada a la subscripció del teu realm '%s'. Es quedarà actiu mentre estigui amb el Realm aparellat.", "mco.snapshot.tooltip": "Utiliza versions de prova de Realms per fe una ullada a les pròximes versions de Minecraft, que potser inclouen noves característiques i canvis.\n\nPots trobar els teus Realms normals en la versió de llançament del joc.", "mco.snapshotRealmsPopup.message": "Els Realms ja estan disponibles en les versions de prova, a partir de la 23w41a. Cada subscripció a Realms inclou una versió de prova de Realm gratuïta, independent del Realm de Java normal!", "mco.snapshotRealmsPopup.title": "Els Realms estan ara disponibles en versions de prova", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON><PERSON>", "mco.template.button.publisher": "Autor", "mco.template.button.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.button.trailer": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.default.name": "Plantilla pel món", "mco.template.info.tooltip": "Pàgina web de l'autor", "mco.template.name": "Plantilla", "mco.template.select.failure": "No s'ha pogut obtenir la llista de contingut d'aquesta categoria.\nSi us plau, revisa la teva connexió a internet o torna a intentar-ho més tard.", "mco.template.select.narrate.authors": "Autors: %s", "mco.template.select.narrate.version": "versió %s", "mco.template.select.none": "Vaja! Sembla que aquesta categoria de contingut es troba buida. \nSi us plau, torna a mirar-la més tard, o, en cas de que siguis un/a creador/a, %s.", "mco.template.select.none.linkTitle": "considera enviar alguna cosa tu mateix", "mco.template.title": "Plantilles de mons", "mco.template.title.minigame": "Minijocs", "mco.template.trailer.tooltip": "Tràiler del mapa", "mco.terms.buttons.agree": "<PERSON> estic d'acord", "mco.terms.buttons.disagree": "No hi estic d'acord", "mco.terms.sentence.1": "Accepto les condicions d'ús", "mco.terms.sentence.2": "Condicions de Servei", "mco.terms.title": "Condicions de Servei de Minecraft Realms", "mco.time.daysAgo": "Fa %1$s dia/es", "mco.time.hoursAgo": "Fa %1$s hora(es)", "mco.time.minutesAgo": "Fa %1$s minut(s)", "mco.time.now": "ara mateix", "mco.time.secondsAgo": "Fa %1$s segon(s)", "mco.trial.message.line1": "Vols aconseguir el teu propi realm?", "mco.trial.message.line2": "Fes clic aquí per més informació!", "mco.upload.button.name": "<PERSON><PERSON><PERSON>", "mco.upload.cancelled": "<PERSON><PERSON><PERSON><PERSON> cancel·lada", "mco.upload.close.failure": "No s'ha pogut tancar el teu realm, si us plau torna a intentar-ho més tard", "mco.upload.done": "<PERSON><PERSON><PERSON><PERSON>", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Ha fallat la pujada! (%s)", "mco.upload.failed.too_big.description": "El món seleccionat és massa gran. La grandària màxima permesa és de %s.", "mco.upload.failed.too_big.title": "El món és massa gran", "mco.upload.hardcore": "No es poden pujar mons en mode extrem!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Preparant el teu món", "mco.upload.select.world.none": "No s'han trobat mons individuals!", "mco.upload.select.world.subtitle": "Si us plau, selecciona el món individual a pujar", "mco.upload.select.world.title": "<PERSON><PERSON><PERSON> m<PERSON>", "mco.upload.size.failure.line1": "'%s' és massa gran!", "mco.upload.size.failure.line2": "Ocupa %s. La mida màxima permesa és %s.", "mco.upload.uploading": "Pujant '%s'", "mco.upload.verifying": "Verificant el teu món", "mco.version": "Versió: %s", "mco.warning": "Atenció!", "mco.worldSlot.minigame": "Minijoc", "menu.custom_options": "Opcions de personalització...", "menu.custom_options.title": "Opcions de personalització", "menu.custom_options.tooltip": "Important: les Opcions de personalització les proporcionen servidors i/o continguts externs.\nTingui cura!", "menu.custom_screen_info.button_narration": "Això és una pantalla de personalització. Més informació.", "menu.custom_screen_info.contents": "Els continguts d'aquesta pantalla els controlen servidors externs i mapes que no pertanyen, controlen o supervisen Mojang Studios ni Microsoft.\n\nTingui cura! Sigui prudent a l'hora de seguir enllaços i mai proporcioni informació personal, incloent-hi detalls d'inici de sessió.\n\nSi aquesta pantalla no li deixa jugar, també pot desconectar-se del servidor actual en prémer el botó inferior.", "menu.custom_screen_info.disconnect": "Pantalla de personalització rebutjada", "menu.custom_screen_info.title": "Informació sobre les pantalles de personalització", "menu.custom_screen_info.tooltip": "Això és una pantalla de personalització. Premi aquí per a més informació.", "menu.disconnect": "Desconnecta't", "menu.feedback": "Comentaris...", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "Menú del joc", "menu.modded": " (Modificat)", "menu.multiplayer": "Multijugador", "menu.online": "Minecraft Realms", "menu.options": "Opcions...", "menu.paused": "<PERSON><PERSON>", "menu.playdemo": "Juga en un món de demostració", "menu.playerReporting": "Denunciar un jugador", "menu.preparingSpawn": "Preparant l'àrea de reaparició: %s%%", "menu.quick_actions": "Operacions ràpides...", "menu.quick_actions.title": "Operacions ràpides", "menu.quit": "Sortir del joc", "menu.reportBugs": "Informa d'un error", "menu.resetdemo": "Reinicia món de demostració", "menu.returnToGame": "Tornar al joc", "menu.returnToMenu": "Des<PERSON> i surt al Menú Principal", "menu.savingChunks": "Desant els chunks", "menu.savingLevel": "Desant el món", "menu.sendFeedback": "<PERSON><PERSON><PERSON> opini<PERSON>", "menu.server_links": "Enllaços de servidors...", "menu.server_links.title": "Enllaços de servidors", "menu.shareToLan": "<PERSON><PERSON><PERSON> per LAN", "menu.singleplayer": "Individual", "menu.working": "Processant...", "merchant.deprecated": "Els vilatans reposen dos vegades al dia.", "merchant.level.1": "Novell", "merchant.level.2": "Aprenent", "merchant.level.3": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.4": "Expert", "merchant.level.5": "Mestre", "merchant.title": "%s - %s", "merchant.trades": "Intercanvis", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Baixa amb %1$s", "multiplayer.applyingPack": "S'està aplicant el paquet de recursos", "multiplayer.confirm_command.parse_errors": "Està intentant executar un comandament no reconegut o invàlid. \nEstà segur?\nComandament: %s", "multiplayer.confirm_command.permissions_required": "Està intentant executar un comandament que requereix autoritzacions elevades. \nÉs possible que això afecti el vostre joc de manera negativa. \nEstà segur? \nComandament: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "Els servidors d'autenticació no funcionen. Prova-ho més tard!", "multiplayer.disconnect.bad_chat_index": "Missatge desaparegut o reordenat rebut del servidor", "multiplayer.disconnect.banned": "T'han bandejat d'aquest servidor", "multiplayer.disconnect.banned.expiration": "\nEl teu bandejament s'alçarà el %s", "multiplayer.disconnect.banned.reason": "T'han bandejat d'aquest servidor.\nMotiu: %s", "multiplayer.disconnect.banned_ip.expiration": "\nEl teu bandejament s'alçarà el %s", "multiplayer.disconnect.banned_ip.reason": "La teva IP està bandejada d'aquest servidor.\nMotiu: %s", "multiplayer.disconnect.chat_validation_failed": "Validació del missatge del xat fallada", "multiplayer.disconnect.duplicate_login": "T'has identificat des d'un altre lloc", "multiplayer.disconnect.expired_public_key": "Clau del perfil pública caducada. Comprova que l'hora del sistema estigui sincronitzada i intenta-ho de nou reiniciant el joc.", "multiplayer.disconnect.flying": "Volar no està permés en aquest servidor", "multiplayer.disconnect.generic": "Desconnectat", "multiplayer.disconnect.idling": "Has estat inactiu durant massa estona!", "multiplayer.disconnect.illegal_characters": "Caràcters no permesos al xat", "multiplayer.disconnect.incompatible": "Client incompatible! Has d'utilitzar la versió %s", "multiplayer.disconnect.invalid_entity_attacked": "Estàs intentant atacar una entitat no vàlida", "multiplayer.disconnect.invalid_packet": "El servidor ha enviat un paquet invàlid", "multiplayer.disconnect.invalid_player_data": "Les dades del jugador són invàlides", "multiplayer.disconnect.invalid_player_movement": "S'ha rebut un paquet de moviment de jugador invàlid", "multiplayer.disconnect.invalid_public_key_signature": "Signatura per a la clau de perfil pública no vàlida.\nIntenta-ho de nou reiniciant el joc.", "multiplayer.disconnect.invalid_public_key_signature.new": "Signatura de la clau pública del perfil no és vàlida.\nProveu de reiniciar el joc.", "multiplayer.disconnect.invalid_vehicle_movement": "S'ha rebut un paquet de moviment de vehicle invàlid", "multiplayer.disconnect.ip_banned": "La teva IP ha estat bandejada d'aquest servidor", "multiplayer.disconnect.kicked": "Expulsat/da per un administrador", "multiplayer.disconnect.missing_tags": "Grup d'etiquetes rebut del servidor incomplet.\nSi us plau, contacta amb l'administrador del servidor.", "multiplayer.disconnect.name_taken": "Ja hi ha algú connectat amb el teu nom", "multiplayer.disconnect.not_whitelisted": "No ets a la llista blanca d'aquest servidor!", "multiplayer.disconnect.out_of_order_chat": "S'ha rebut un paquet de xat fora d'ordre. Ha canviat l'hora del teu sistema?", "multiplayer.disconnect.outdated_client": "Client incompatible! Has d'utilitzar la versió %s", "multiplayer.disconnect.outdated_server": "Client incompatible! Has d'utilitzar la versió %s", "multiplayer.disconnect.server_full": "El servidor està ple!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON><PERSON> tancat", "multiplayer.disconnect.slow_login": "Has tardat massa en iniciar la sessió", "multiplayer.disconnect.too_many_pending_chats": "Massa missatges no reconeguts", "multiplayer.disconnect.transfers_disabled": "El servidor no accepta transferències", "multiplayer.disconnect.unexpected_query_response": "No s'esperaven dades personalitzades per part del client", "multiplayer.disconnect.unsigned_chat": "S'ha rebut un paquet de xat amb una signatura invàlida o absent.", "multiplayer.disconnect.unverified_username": "No s'ha pogut verificar el teu nom d'usuari!", "multiplayer.downloadingStats": "Recuperant estadístiques...", "multiplayer.downloadingTerrain": "Carregant terreny...", "multiplayer.lan.server_found": "Nou servidor trobat: %s", "multiplayer.message_not_delivered": "No s'ha pogut enviar el missatge, revisa els registres del servidor: %s", "multiplayer.player.joined": "%s s'ha unit a la partida", "multiplayer.player.joined.renamed": "%s (abans conegut com %s) s'ha unit a la partida", "multiplayer.player.left": "%s ha abandonat la partida", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Jugadors en línia: %s", "multiplayer.requiredTexturePrompt.disconnect": "Aquest servidor requereix un paquet de recursos personalitzat", "multiplayer.requiredTexturePrompt.line1": "Aquest servidor requereix l'ús d'un paquet de recursos personalitzat.", "multiplayer.requiredTexturePrompt.line2": "Rebutjar aquest paquet de recursos personalitzat et desconnectarà del servidor.", "multiplayer.socialInteractions.not_available": "Les interaccions socials només estan disponibles en mons multijugador", "multiplayer.status.and_more": "...i %s més...", "multiplayer.status.cancelled": "Cancel·lat", "multiplayer.status.cannot_connect": "No s'ha pogut connectar amb el servidor", "multiplayer.status.cannot_resolve": "No s'ha pogut resoldre el nom d'amfitrió", "multiplayer.status.finished": "Acabat", "multiplayer.status.incompatible": "Versió incompatible!", "multiplayer.status.motd.narration": "Missatge del dia: %s", "multiplayer.status.no_connection": "(sense connexió)", "multiplayer.status.old": "Antiquat", "multiplayer.status.online": "En línia", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Latència %s mil·lisegons", "multiplayer.status.pinging": "Comprovant connexió...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s de %s jugadors en línia", "multiplayer.status.quitting": "Sortint", "multiplayer.status.request_handled": "S'ha gestionat la sol·licitud d'estat", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "S'ha rebut un estat no sol·licitat", "multiplayer.status.version.narration": "Versió del servidor: %s", "multiplayer.stopSleeping": "Sortir del llit", "multiplayer.texturePrompt.failure.line1": "No s'ha pogut aplicar el paquet de recursos del servidor", "multiplayer.texturePrompt.failure.line2": "Qualsevol funcionalitat que necessiti recursos personalitzats podria no funcionar com és esperat", "multiplayer.texturePrompt.line1": "Aquest servidor recomana l'ús d'un paquet de recursos personalitzat.", "multiplayer.texturePrompt.line2": "Vols descarregar-lo i instal·lar-lo automàticament?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMissatge del servidor:\n%s", "multiplayer.title": "Mode multijugador", "multiplayer.unsecureserver.toast": "Els missatges enviats en aquest servidor poden haver estat modificats i no representar el missatge original", "multiplayer.unsecureserver.toast.title": "Els missatges del xat no s'han pogut verificar", "multiplayerWarning.check": "No tornis a mostrar aquesta advertència", "multiplayerWarning.header": "Atenció: <PERSON>c en línia de tercers", "multiplayerWarning.message": "Atenció: el joc en línia està proveït per servidors de tercers els quals no són gestionats, supervisats, ni propietat de Mojang Studios o Microsoft. Mentre jugues en línia, és possible que siguis exposat a missatges de xat sense moderar o a altres tipus de contingut generat per usuaris que poden no ser adequats per a tothom.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Botó: %s", "narration.button.usage.focused": "Prem Enter per activar", "narration.button.usage.hovered": "Fes clic esquerre per activar", "narration.checkbox": "Casella de selecció: %s", "narration.checkbox.usage.focused": "Prem Enter per marcar o desmarcar", "narration.checkbox.usage.hovered": "Fes clic esquerre per marcar o desmarcar", "narration.component_list.usage": "Premeu Tabulador per a navegar al següent element", "narration.cycle_button.usage.focused": "Prem Enter per canviar a %s", "narration.cycle_button.usage.hovered": "Fes clic esquerre per canviar a %s", "narration.edit_box": "Caixa d'edició: %s", "narration.item": "Item: %s", "narration.recipe": "Recepta de: %s", "narration.recipe.usage": "<PERSON><PERSON> clic esquerre per a seleccionar", "narration.recipe.usage.more": "Fes clic dret per mostrar més receptes", "narration.selection.usage": "Premeu les tecles amunt i avall per moure-us a una altra entrada", "narration.slider.usage.focused": "Premeu es<PERSON>ra o dreta per a canviar el valor", "narration.slider.usage.hovered": "Arrossega el control lliscant per canviar el valor", "narration.suggestion": "Seleccionat el suggeriment %s de %s: %s", "narration.suggestion.tooltip": "Seleccionat el suggeriment %s de %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Prem Tab per passar al següent suggeriment", "narration.suggestion.usage.cycle.hidable": "Prem Tab per passar al següent suggeriment, o Surt per abandonar els suggeriments", "narration.suggestion.usage.fill.fixed": "Prem Tab per utilitzar el suggeriment", "narration.suggestion.usage.fill.hidable": "Prem ''Tab'' per utilitzar el suggeriment, o ''Esc'' per abandonar els suggeriments", "narration.tab_navigation.usage": "Prem Ctrl i Tab per a canviar de pestanya", "narrator.button.accessibility": "Accessibilitat", "narrator.button.difficulty_lock": "Bloquejar la dificultat", "narrator.button.difficulty_lock.locked": "Bloquejada", "narrator.button.difficulty_lock.unlocked": "Desb<PERSON>quejada", "narrator.button.language": "Idioma", "narrator.controls.bound": "%s està vinculat a %s", "narrator.controls.reset": "Reinicia el botó de l'acció: %s", "narrator.controls.unbound": "%s no està vinculat", "narrator.joining": "<PERSON><PERSON><PERSON><PERSON> entrant", "narrator.loading": "Carregant: %s", "narrator.loading.done": "Fet", "narrator.position.list": "S'ha seleccionat la fila de la llista %s de %s", "narrator.position.object_list": "S'ha seleccionat l'element de la fila %s de %s", "narrator.position.screen": "Element en la pantalla %s de %s", "narrator.position.tab": "S'ha seleccionat la pestanya %s de %s", "narrator.ready_to_play": "Preparat per a jugar", "narrator.screen.title": "<PERSON><PERSON> principal", "narrator.screen.usage": "Usa el ratolí o la tecla tabulador per seleccionar l'element", "narrator.select": "Has seleccionat: %s", "narrator.select.world": "Has seleccionat %s, jugat per última vegada: %s, %s, %s, versió: %s", "narrator.select.world_info": "Seleccionat %s, última jugada %s, %s", "narrator.toast.disabled": "S'ha desactivat el narrador", "narrator.toast.enabled": "S'ha activat el narrador", "optimizeWorld.confirm.description": "Això intentarà optimitzar el teu món assegurant-se que totes les dades es guardin en el format del joc més recent. Això podria tardar molt de temps, depenent de la mida del teu món. Un cop acabat, el teu món hauria de funcionar més ràpidament, però ja no serà compatible amb versions antigues del joc. Estàs segur de que vols fer-ho?", "optimizeWorld.confirm.proceed": "Crea una còpia de seguretat i optimitza", "optimizeWorld.confirm.title": "Optimitzar el món", "optimizeWorld.info.converted": "Chunks actualitzats: %s", "optimizeWorld.info.skipped": "Chunks omesos: %s", "optimizeWorld.info.total": "Chunks en total: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Comptant els chunks...", "optimizeWorld.stage.failed": "Error! :(", "optimizeWorld.stage.finished": "Finalitzant...", "optimizeWorld.stage.finished.chunks": "Acabant l'actualització de chunks...", "optimizeWorld.stage.finished.entities": "Acabant l'actualització d'entitats...", "optimizeWorld.stage.finished.poi": "Acabant l'actualització dels punts d'interès...", "optimizeWorld.stage.upgrading": "Actualitzant tots els chunks...", "optimizeWorld.stage.upgrading.chunks": "Actualitzant tots els chunks...", "optimizeWorld.stage.upgrading.entities": "Actualitzant totes les entitats...", "optimizeWorld.stage.upgrading.poi": "Actualitzant tots els punts d'interès...", "optimizeWorld.title": "Optimitzant el món \"%s\"", "options.accessibility": "Opcions d'accessibilitat...", "options.accessibility.high_contrast": "Contrast alt", "options.accessibility.high_contrast.error.tooltip": "El paquet de recursos d'alt contrast no està disponible", "options.accessibility.high_contrast.tooltip": "Millora el contrast en els elements de la IU", "options.accessibility.high_contrast_block_outline": "Vores de blocs d'alt contrast", "options.accessibility.high_contrast_block_outline.tooltip": "Augmenta el contrast de la vora del bloc que estiguis apuntant.", "options.accessibility.link": "Guia d'accessibilitat", "options.accessibility.menu_background_blurriness": "Nitidesa del fons del menú", "options.accessibility.menu_background_blurriness.tooltip": "Canvia la nitidesa dels fons dels menús", "options.accessibility.narrator_hotkey": "Tecla d'accés ràpid a la narradora", "options.accessibility.narrator_hotkey.mac.tooltip": "Permet activar i desactivar el narrador amb 'Cmd+B'", "options.accessibility.narrator_hotkey.tooltip": "Permet activar i desactivar el narrador amb Ctrl+B", "options.accessibility.panorama_speed": "Velocitat de desplaçament dels panorames", "options.accessibility.text_background": "Fons del text", "options.accessibility.text_background.chat": "Xat", "options.accessibility.text_background.everywhere": "A tot arreu", "options.accessibility.text_background_opacity": "Opacitat de fons", "options.accessibility.title": "Opcions d'accessibilitat...", "options.allowServerListing": "Permet ser llistat en servidors", "options.allowServerListing.tooltip": "Els servidors podran llistar jugadors en línia com a part del seu estat públic.\nAmb aquesta opció desactivada el teu nom no apareixerà en aquestes llistes.", "options.ao": "Il·luminació suau", "options.ao.max": "Màxim", "options.ao.min": "<PERSON><PERSON><PERSON>", "options.ao.off": "Desactivada", "options.attack.crosshair": "Mira", "options.attack.hotbar": "Barra d'eines", "options.attackIndicator": "Indicador d'atac", "options.audioDevice": "Dispositiu", "options.audioDevice.default": "Per defecte del sistema", "options.autoJump": "Salt automàtic", "options.autoSuggestCommands": "Suggerir comandaments", "options.autosaveIndicator": "Indicador de guardat automàtic", "options.biomeBlendRadius": "Transició de biomes", "options.biomeBlendRadius.1": "Desactivat (Més ràpid)", "options.biomeBlendRadius.11": "11x11 (Extrem)", "options.biomeBlendRadius.13": "13x13 (Exagerat)", "options.biomeBlendRadius.15": "15x15 (Màxim)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (Normal)", "options.biomeBlendRadius.7": "7x7 (Detallat)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON>)", "options.chat": "Configuracions del xat...", "options.chat.color": "Colors", "options.chat.delay": "Retràs del xat: %s segons", "options.chat.delay_none": "Retràs del xat: Cap", "options.chat.height.focused": "Alçada enfocada", "options.chat.height.unfocused": "Alçada desenfocada", "options.chat.line_spacing": "Interlineat", "options.chat.links": "Enllaços web", "options.chat.links.prompt": "Avisar a l'obrir enllaços", "options.chat.opacity": "Opacitat del xat", "options.chat.scale": "Mida del text del xat", "options.chat.title": "Configuració del xat...", "options.chat.visibility": "Xat", "options.chat.visibility.full": "Visible", "options.chat.visibility.hidden": "Amagat", "options.chat.visibility.system": "Només comandaments", "options.chat.width": "Amplad<PERSON>", "options.chunks": "%s chunks", "options.clouds.fancy": "Detallats", "options.clouds.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.controls": "Controls...", "options.credits_and_attribution": "Crèdits i atribucions...", "options.damageTiltStrength": "Sacseig per ferida", "options.damageTiltStrength.tooltip": "L'intensitat del sacseig de la càmera al ferir-se.", "options.darkMojangStudiosBackgroundColor": "Logotip <PERSON>oc<PERSON>", "options.darkMojangStudiosBackgroundColor.tooltip": "Canvia el color de fons de la pantalla de càrrega de Mojang Studios a negre.", "options.darknessEffectScale": "Pulsació de la foscor", "options.darknessEffectScale.tooltip": "Controla la variació en l'efecte de foscor quan un vigilant o sculk xisclador te l'aplica.", "options.difficulty": "Dificultat", "options.difficulty.easy": "Fàcil", "options.difficulty.easy.info": "Apareixen criatures hostils, però fan menys mal. S'ha de menjar i tenir gana treu salut fins a 5 cors.", "options.difficulty.hard": "Dif<PERSON><PERSON>l", "options.difficulty.hard.info": "Apareixen criatures hostils i fan més mal. S'ha de menjar i tenir gana treu salut fins a morir.", "options.difficulty.hardcore": "Extrem", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Apareixen criatures hostils i fan mal estàndard. S'ha de menjar i tenir gana treu salut fins a mig cor.", "options.difficulty.online": "Dificultat del servidor", "options.difficulty.peaceful": "Pacífica", "options.difficulty.peaceful.info": "No apareixen criatures hostils i només apareixen algunes criatures neutrals. No hi ha fam i la salut sempre es regenera.", "options.directionalAudio": "So direccional", "options.directionalAudio.off.tooltip": "So Estèreo <PERSON>", "options.directionalAudio.on.tooltip": "Utilitza àudio direccional basat en HRTF per millorar la simulació del so 3D. Requereix un maquinari d'àudio compatible amb HRTF i ofereix una millor experiència quan es fan servir auriculars.", "options.discrete_mouse_scroll": "Desplaçament discret", "options.entityDistanceScaling": "Distància de renderitzat d'entitats", "options.entityShadows": "Ombres d'entitats", "options.font": "Configuracions de la lletra predeterminada...", "options.font.title": "Configuracions de la font", "options.forceUnicodeFont": "Força font unicode", "options.fov": "Camp de visió", "options.fov.max": "<PERSON><PERSON> <PERSON>", "options.fov.min": "Normal", "options.fovEffectScale": "Efectes del camp de visió", "options.fovEffectScale.tooltip": "Controla la variació del camp de visió en aplicar-se efectes del joc.", "options.framerate": "%s fps", "options.framerateLimit": "FPS màxims", "options.framerateLimit.max": "Il·limitat", "options.fullscreen": "Pantalla completa", "options.fullscreen.current": "Actual", "options.fullscreen.entry": "%sx%s@%s(%sbit)", "options.fullscreen.resolution": "Resolució de pantalla completa", "options.fullscreen.unavailable": "Configuració no disponible", "options.gamma": "Il·luminació", "options.gamma.default": "Per defecte", "options.gamma.max": "<PERSON><PERSON><PERSON>", "options.gamma.min": "Fosc", "options.generic_value": "%s: %s", "options.glintSpeed": "Velocitat de la lluentor", "options.glintSpeed.tooltip": "Controla a quina velocitat es mou l'efecte de lluentor en ítems encantats.", "options.glintStrength": "Intensitat de la lluentor", "options.glintStrength.tooltip": "Controla la transparència de l'efecte de lluentor en ítems encantats.", "options.graphics": "<PERSON><PERSON>à<PERSON><PERSON>", "options.graphics.fabulous": "Fabulosos!", "options.graphics.fabulous.tooltip": "Els gràfics %s utilitzen ombrejadors de pantalla per dibuixar el temps atmosfèric, els núvols i les partícules darrere de blocs translúcids i aigua.\nAquesta opció pot afectar severament el rendiment per a dispositius portàtils i monitors 4K.", "options.graphics.fancy": "Detallats", "options.graphics.fancy.tooltip": "Els gràfics detallats equilibren el rendiment i la qualitat per a la majoria de dispositius.\nEl temps atmosfèric, els núvols i les partícules poden no aparèixer darrere de blocs transparents o aigua.", "options.graphics.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "Els gràfics ràpids redueixen la quantitat de pluja i neu visualment.\nEls efectes de transparència estan desactivats per a diversos blocs com ara les fulles dels arbres.", "options.graphics.warning.accept": "Continua sense suport", "options.graphics.warning.cancel": "<PERSON><PERSON>", "options.graphics.warning.message": "La teva targeta gràfica ha estat detectada com a incompatible per els gràfics %s.\n\nPots ignorar aquest missatge i continuar, però no es proveirà suport per al dispositiu en cas que utilitzis els gràfics %s.", "options.graphics.warning.renderer": "Processador detectat: [%s]", "options.graphics.warning.title": "Targeta gràfica no compatible", "options.graphics.warning.vendor": "Fabricant detectat: [%s]", "options.graphics.warning.version": "Versió d'OpenGL detectada: [%s]", "options.guiScale": "Mida del menú", "options.guiScale.auto": "Automàtic", "options.hidden": "Amagat", "options.hideLightningFlashes": "Amaga les resplendors dels llamps", "options.hideLightningFlashes.tooltip": "Evita que els llamps facin que s'il·lumini el cel. Els llamps encara seran visibles.", "options.hideMatchedNames": "Oculta els noms coincidents", "options.hideMatchedNames.tooltip": "Els servidors de tercers poden enviar missatges al xat en formats no estàndards.\nAmb aquesta opció activada, els jugadors ocults es podran trobar segons els noms dels remitents del xat.", "options.hideSplashTexts": "Amaga textos d'esquitxos", "options.hideSplashTexts.tooltip": "Amaga el text groc esquitxó en el menú principal.", "options.inactivityFpsLimit": "Reduir FPS quan", "options.inactivityFpsLimit.afk": "Per inactivitat", "options.inactivityFpsLimit.afk.tooltip": "Limita els FPS a 30 en passar un minut sense activitat. El límit augmentarà a 10 FPS després de passar 10 minuts sense activitat.", "options.inactivityFpsLimit.minimized": "En minimitzar", "options.inactivityFpsLimit.minimized.tooltip": "Només es limitaran els FPS quan el joc estigui minimitzat.", "options.invertMouse": "Invertir el ratolí", "options.japaneseGlyphVariants": "Variacions de glifs Japonesos", "options.japaneseGlyphVariants.tooltip": "Utilitza variacions de caràcters CJK Japonesos en la lletra predeterminada", "options.key.hold": "Mantenir", "options.key.toggle": "Canvia", "options.language": "Idioma...", "options.language.title": "Idioma", "options.languageAccuracyWarning": "(Les traduccions poden no ser fiables al 100%)", "options.languageWarning": "Les traduccions poden no ser fiables al 100%%", "options.mainHand": "Mà principal", "options.mainHand.left": "Esquerra", "options.mainHand.right": "Dreta", "options.mipmapLevels": "<PERSON>vell de minimaping", "options.modelPart.cape": "Capa", "options.modelPart.hat": "Barret", "options.modelPart.jacket": "<PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "Camal esquerra", "options.modelPart.left_sleeve": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.right_pants_leg": "Camal dret", "options.modelPart.right_sleeve": "Màniga dreta", "options.mouseWheelSensitivity": "Sensibilitat de roda", "options.mouse_settings": "Configuració del ratolí...", "options.mouse_settings.title": "Configuració del ratolí", "options.multiplayer.title": "Configuració - Multijugador", "options.multiplier": "%sx", "options.music_frequency": "Music Frequency", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Frequent", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "<PERSON><PERSON><PERSON>-ho tot", "options.narrator.chat": "Narra el xat", "options.narrator.notavailable": "No disponible", "options.narrator.off": "<PERSON><PERSON><PERSON>", "options.narrator.system": "Narra el sistema", "options.notifications.display_time": "Durada de les notificacions", "options.notifications.display_time.tooltip": "Controla quant temps es mostren les notificacions a la pantalla.", "options.off": "NO", "options.off.composed": "%s: desactivat", "options.on": "SI", "options.on.composed": "%s: activat", "options.online": "En línia...", "options.online.title": "Opcions en línia", "options.onlyShowSecureChat": "Mostrar solament els missatges segurs", "options.onlyShowSecureChat.tooltip": "Mostra només missatges d'altres jugadors si es pot verificar que han estat enviats pel jugador i que no han estat modificats.", "options.operatorItemsTab": "Secció d'ítems d'administrador", "options.particles": "Partícules", "options.particles.all": "Totes", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "Mínimes", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Constructor de chunks", "options.prioritizeChunkUpdates.byPlayer": "<PERSON> acci<PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Algunes accions dins d’un chunk es recompilaran immediatament. Això inclou col·locar i destruir blocs.", "options.prioritizeChunkUpdates.nearby": "Sempre", "options.prioritizeChunkUpdates.nearby.tooltip": "Els chunks propers sempre es compilen immediatament. Això pot impactar al rendiment del joc quan es col·loquin o destrueixin blocs.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Els chunks propers es compilen en fils paral·lels. Això pot provocar breus forats visuals quan es destrueixen blocs.", "options.rawMouseInput": "Entrada directa", "options.realmsNotifications": "Notícies i invitacions de Realms", "options.realmsNotifications.tooltip": "Aporta notícies de Realms i invitacions al menú principal i mostra la respectiva icona en el botó de Realms.", "options.reducedDebugInfo": "Reduir dades de F3", "options.renderClouds": "Núvols", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "Distància de renderitzat", "options.resourcepack": "Paquets de recursos...", "options.rotateWithMinecart": "Gira amb les vagonetes", "options.rotateWithMinecart.tooltip": "Si la vista del jugador ha de girar al costat de la vagoneta. Només disponible a mons amb la configuració experimental 'Millores de Vagoneta' activada.", "options.screenEffectScale": "Efectes de distorsió", "options.screenEffectScale.tooltip": "Intensitat dels efectes de distorsió de la pantalla causats per la nàusea o pels portals del Nether.\nEn valors més baixos, l'efecte de nàusea es substituirà per una superposició de color verd a la pantalla.", "options.sensitivity": "Sensibilitat", "options.sensitivity.max": "HIPERVELOCITAT!!!", "options.sensitivity.min": "*zzz*", "options.showNowPlayingToast": "Show Music Toast", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "Mostrar subtítols", "options.simulationDistance": "Distància de la simulació", "options.skinCustomisation": "Aparença...", "options.skinCustomisation.title": "Aparença", "options.sounds": "<PERSON><PERSON><PERSON> i sons...", "options.sounds.title": "Opcions de música i sons", "options.telemetry": "Dades telemètriques...", "options.telemetry.button": "Recol·lecció de dades", "options.telemetry.button.tooltip": "\"%s\" inclou només dades necessàries.\n\"%s\" inclou tant dades opcionals com necessàries.", "options.telemetry.disabled": "Telemetria desactivada.", "options.telemetry.state.all": "Completa", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON>", "options.telemetry.state.none": "Cap", "options.title": "Opcions", "options.touchscreen": "Mode de pantalla tàctil", "options.video": "Configuració de vídeo...", "options.videoTitle": "Configuració de vídeo", "options.viewBobbing": "Moviment de la càmera", "options.visible": "Visible", "options.vsync": "Sincronització vertical", "outOfMemory.message": "Minecraft s'ha quedat sense memòria disponible.\n\nAixò pot haver passat a causa d'un error del joc o per no assignar prou memòria a la Màquina Virtual de Java.\n\nPer evitar la corrupció del món, la partida actual s'ha tancat. Hem provat d'alliberar memòria suficient per deixar-te tornar al menú principal i poder tornar a jugar, però és possible que no hagi funcionat.\n\nSi us plau reinicia el joc si tornes a veure aquest missatge de nou.", "outOfMemory.title": "Sense memòria!", "pack.available.title": "Disponible", "pack.copyFailure": "Error en copiar els paquets", "pack.dropConfirm": "Vols afegir els següents paquets a Minecraft?", "pack.dropInfo": "Arrossega arxius dins d'aquesta finestra per afegir paquets", "pack.dropRejected.message": "Les següents entrades no són paquets vàlids i no seran copiades:\n%s", "pack.dropRejected.title": "Entrades sense paquet", "pack.folderInfo": "(Col·loca els paquets de recursos aquí)", "pack.incompatible": "Incompatible", "pack.incompatible.confirm.new": "Aquest paquet es va fer per a una versió més recent de Minecraft i podria no funcionar correctament.", "pack.incompatible.confirm.old": "Aquest paquet es va fer per a una versió més antiga de Minecraft i podria no funcionar correctament.", "pack.incompatible.confirm.title": "Estàs segur que vols carregar aquest paquet?", "pack.incompatible.new": "(Fet per a una versió més nova de Minecraft)", "pack.incompatible.old": "(Fet per a una versió més vella de Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Obre la carpeta dels paquets", "pack.selected.title": "Seleccionat", "pack.source.builtin": "integrat", "pack.source.feature": "característica", "pack.source.local": "local", "pack.source.server": "servidor", "pack.source.world": "món", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanès", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "<PERSON><PERSON> posterior", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Barroc", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Objectiu bombardejat amb èxit", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Ram", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Calavera en flames", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Ocell de cavernes", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Canviant", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "<PERSON> <PERSON><PERSON>, se<PERSON><PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Terra", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "L'últim amo", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Falguera", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Lluitadors", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Foc", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Hu<PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "<PERSON><PERSON><PERSON> amb tres pebrots", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Llumí", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditatiu", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "Or<PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passatge", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Porc-escena", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Planta de jade", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "La piscina", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Passeig pels prats", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Litoral", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "<PERSON><PERSON> mortal", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Calavera i roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "Tot a punt", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Gira-sols", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "posta_de_sol_densa", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Desempaquetat", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "El buit", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "Caminant", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Erm", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Aigua", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Vent", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Quadre a l'atzar", "parsing.bool.expected": "Es requereix un valor booleà", "parsing.bool.invalid": "<PERSON>or booleà invàlid: \"%s\" no és \"true\" ni \"false\"", "parsing.double.expected": "Es requereix un valor doble", "parsing.double.invalid": "Valor doble invàlid: %s", "parsing.expected": "Es requereix \"%s\"", "parsing.float.expected": "<PERSON>s requereix un valor float", "parsing.float.invalid": "Valor float invàlid: %s", "parsing.int.expected": "<PERSON><PERSON> requereix un nombre enter", "parsing.int.invalid": "Nombre enter invàlid: %s", "parsing.long.expected": "S'esperava un enter", "parsing.long.invalid": "El valor enter \"%s\" no és vàlid", "parsing.quote.escape": "La seqüència d'escapament \"\\%s\" és invàlida en una cadena amb cometes", "parsing.quote.expected.end": "Falta el tancament de cometes de la cadena", "parsing.quote.expected.start": "Falten cometes a l'inici de la cadena", "particle.invalidOptions": "No es pot analitzar les opcions de la partícula: %s", "particle.notFound": "Partícula desconeguda: %s", "permissions.requires.entity": "Es requereix una entitat per executar aquest comandament aquí", "permissions.requires.player": "Es requereix un jugador per executar aquest comandament aquí", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Quan s'aplica:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predicat desconegut: %s", "quickplay.error.invalid_identifier": "No s'ha pogut trobar el món amb l'identificador donat", "quickplay.error.realm_connect": "No s'ha pogut connectar al Realm", "quickplay.error.realm_permission": "No tens permís per connectar-te a aquest Realm", "quickplay.error.title": "Ha fallat el joc ràpid", "realms.configuration.region.australia_east": "Nova Gal·les del Sud, Austràlia", "realms.configuration.region.australia_southeast": "Victòria, Austràlia", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "Índia", "realms.configuration.region.central_us": "Iowa, EUA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virgínia, EUA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "França", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Japó Occidental", "realms.configuration.region.korea_central": "Corea del Sud", "realms.configuration.region.north_central_us": "Illinois, EUA", "realms.configuration.region.north_europe": "Irlanda", "realms.configuration.region.south_central_us": "Texas, EUA", "realms.configuration.region.southeast_asia": "Singapur", "realms.configuration.region.sweden_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.uae_north": "Emirats Àrabs Units (EAU)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, EUA", "realms.configuration.region.west_europe": "<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.west_us": "Califòrnia, EUA", "realms.configuration.region.west_us_2": "Washington, EUA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "Minecraft Realms no és compatible amb les versions de prova", "recipe.notFound": "Recepta desconeguda: %s", "recipe.toast.description": "Comprova el receptari", "recipe.toast.title": "Noves receptes!", "record.nowPlaying": "Reproduint: %s", "recover_world.bug_tracker": "Informa d'un error", "recover_world.button": "Intenta recuperar", "recover_world.done.failed": "No s'ha pogut recuperar de l'estat anterior.", "recover_world.done.success": "S'ha recuperat amb èxit!", "recover_world.done.title": "Recuperació feta", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON><PERSON> falta", "recover_world.issue.none": "Cap problema", "recover_world.message": "Els següents problemes s'han produït intentant de llegir la carpeta del món \"%s\".\nÉs possible restaurar el món d'un estat més vell o pots informar aquest problema al bug tracker.", "recover_world.no_fallback": "Cap estat disponible per recuperar", "recover_world.restore": "Intenta restaurar", "recover_world.restoring": "Intentant de restaurar el món...", "recover_world.state_entry": "Estat des de %s: ", "recover_world.state_entry.unknown": "desconegut", "recover_world.title": "No s'ha pogut carregar el món", "recover_world.warning": "No s'ha pogut carregar el resum del món", "resourcePack.broken_assets": "RECURSOS DEFECTUOSOS DETECTATS", "resourcePack.high_contrast.name": "Contrast alt", "resourcePack.load_fail": "Error en recarregar el paquet de recursos", "resourcePack.programmer_art.name": "Art de programador", "resourcePack.runtime_failure": "Error amb paquet de recursos detectat", "resourcePack.server.name": "Recursos específics del món", "resourcePack.title": "Selecciona paquets de recursos", "resourcePack.vanilla.description": "L'aparença per defecte de Minecraft", "resourcePack.vanilla.name": "Per defecte", "resourcepack.downloading": "Descarregant paquet de recursos", "resourcepack.progress": "Descarregant fitxer (%s MB)...", "resourcepack.requesting": "<PERSON>t petició...", "screenshot.failure": "No s'ha pogut guardar la captura de pantalla: %s", "screenshot.success": "Captura guardada com a %s", "selectServer.add": "<PERSON><PERSON><PERSON><PERSON> servidor", "selectServer.defaultName": "Servidor de minecraft", "selectServer.delete": "Eliminar", "selectServer.deleteButton": "Eliminar", "selectServer.deleteQuestion": "Segur que vols eliminar aquest servidor?", "selectServer.deleteWarning": "\"%s\" desapareixerà per sempre! (Molt temps!)", "selectServer.direct": "Connexió directa", "selectServer.edit": "<PERSON><PERSON>", "selectServer.hiddenAddress": "(Amagada)", "selectServer.refresh": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.select": "Entrar al servidor", "selectWorld.access_failure": "Error en accedir al món", "selectWorld.allowCommands": "Permet trucs", "selectWorld.allowCommands.info": "Comandaments com /gamemode, /experience", "selectWorld.allowCommands.new": "Permetre Comandaments", "selectWorld.backupEraseCache": "Esborra les dades de la memòria cau", "selectWorld.backupJoinConfirmButton": "Crea una còpia de seguretat i carrega-la", "selectWorld.backupJoinSkipButton": "Sé el que estic fent!", "selectWorld.backupQuestion.customized": "Els mons personalitzats ja no són compatibles", "selectWorld.backupQuestion.downgrade": "No està permès passar un món a una versió anterior", "selectWorld.backupQuestion.experimental": "Els mons amb configuracions experimentals no són compatibles", "selectWorld.backupQuestion.snapshot": "Segur que vols carregar aquest món?", "selectWorld.backupWarning.customized": "Aquesta versió de Minecraft no disposa de suport per a mons personalitzats. Encara pots carregar el món, però el nou terreny generat tindrà l'aspecte d'un món normal i corrent. Disculpeu les molèsties!", "selectWorld.backupWarning.downgrade": "En aquest món s'hi ha jugat per últim cop en la versió %s; i estàs en la %s. Passar el món a una versió anterior el podria corrompre. No et podem garantir que es carregui o funcioni. Si vols continuar, si us plau, fes primer una còpia de seguretat!", "selectWorld.backupWarning.experimental": "Aquest món utilitza configuracions experimentals que podrien parar de funcionar en qualsevol moment. No podem garantir que es pugui carregar o que funcioni. Ves amb compte!", "selectWorld.backupWarning.snapshot": "S'ha jugat a aquest món per última vegada a la versió %s; estàs a la versió %s. Si us plau, fes una còpia de seguretat en cas de que hi hagi una corrupció del món!", "selectWorld.bonusItems": "Cofre de bonificació", "selectWorld.cheats": "Trucs", "selectWorld.commands": "Comandaments", "selectWorld.conversion": "Ha de ser convertit!", "selectWorld.conversion.tooltip": "Aquest món s'ha d'obrir en una versió més antiga (com la 1.6.4) perquè pugui ser convertit de forma segura", "selectWorld.create": "<PERSON><PERSON>r un nou món", "selectWorld.customizeType": "<PERSON><PERSON><PERSON>", "selectWorld.dataPacks": "Paquets de dades", "selectWorld.data_read": "Llegint les dades del món...", "selectWorld.delete": "Eliminar", "selectWorld.deleteButton": "Eliminar", "selectWorld.deleteQuestion": "Segur que vols eliminar aquest món?", "selectWorld.deleteWarning": "\"%s\" desapareixerà... per sempre! (molt de temps!)", "selectWorld.delete_failure": "No s'ha pogut eliminar el món", "selectWorld.edit": "<PERSON><PERSON>", "selectWorld.edit.backup": "<PERSON>r c<PERSON><PERSON> de segu<PERSON>", "selectWorld.edit.backupCreated": "%s", "selectWorld.edit.backupFailed": "La còpia de seguretat ha fallat", "selectWorld.edit.backupFolder": "<PERSON><PERSON><PERSON> de <PERSON>ò<PERSON>", "selectWorld.edit.backupSize": "Món copiat (%s MB)", "selectWorld.edit.export_worldgen_settings": "Exporta configuració de generació del món", "selectWorld.edit.export_worldgen_settings.failure": "No s'ha pogut exportar", "selectWorld.edit.export_worldgen_settings.success": "Exportat", "selectWorld.edit.openFolder": "<PERSON><PERSON><PERSON> del món", "selectWorld.edit.optimize": "Optimitzar el món", "selectWorld.edit.resetIcon": "Restaurar la icona", "selectWorld.edit.save": "Desar", "selectWorld.edit.title": "Modificar món", "selectWorld.enterName": "Nom del món", "selectWorld.enterSeed": "Llavor pel generador de mons", "selectWorld.experimental": "Experimental", "selectWorld.experimental.details": "Detalls", "selectWorld.experimental.details.entry": "Característiques experimentals requerides: %s", "selectWorld.experimental.details.title": "Requeriments de funcions experimentals", "selectWorld.experimental.message": "Ves amb compte!\nAquesta configuració requereix característiques que estan en desenvolupament. El teu món pot fallar, espatllar-se o no funcionar en futures actualitzacions.", "selectWorld.experimental.title": "Avís de característiques experimentals", "selectWorld.experiments": "Experiments", "selectWorld.experiments.info": "Els experiments són característiques de prova. Vés amb compte, és possible que algunes coses s'espatllin. Els experiments no es poden desactivar un cop s'ha creat el món.", "selectWorld.futureworld.error.text": "S'ha produït un error al intentar carregar un món d'una versió posterior. Era una operació arriscada, sentim que no hagi funcionat.", "selectWorld.futureworld.error.title": "S'ha produït un error!", "selectWorld.gameMode": "Mode de joc", "selectWorld.gameMode.adventure": "Aventura", "selectWorld.gameMode.adventure.info": "Com el mode de supervivència, però no es poden col·locar ni treure blocs.", "selectWorld.gameMode.adventure.line1": "Com el mode de supervivència,", "selectWorld.gameMode.adventure.line2": "però no pots posar ni treure blocs", "selectWorld.gameMode.creative": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, construeix i explora sense límits. Es pot volar, no hi ha límit de materials i els monstres no fan mal.", "selectWorld.gameMode.creative.line1": "Recursos il·limitats, vol lliure i", "selectWorld.gameMode.creative.line2": "destrucció de blocs instantània", "selectWorld.gameMode.hardcore": "Extrem", "selectWorld.gameMode.hardcore.info": "Mode de supervivència fixat a la dificultat «Difícil». No es pot reaparèixer després de morir.", "selectWorld.gameMode.hardcore.line1": "Com el mode de supervivència, però bloquejat a la", "selectWorld.gameMode.hardcore.line2": "màxima dificultat, i amb una sola vida", "selectWorld.gameMode.spectator": "Espectador", "selectWorld.gameMode.spectator.info": "Es mira però no es toca.", "selectWorld.gameMode.spectator.line1": "Pots mirar però no tocar", "selectWorld.gameMode.survival": "Supervivència", "selectWorld.gameMode.survival.info": "Explora un món misteriós on es pot construir, recol·lectar, crear i lluitar contra monstres.", "selectWorld.gameMode.survival.line1": "Busca recursos, elabora, guanya", "selectWorld.gameMode.survival.line2": "nivells, salut i fam", "selectWorld.gameRules": "Regles del joc", "selectWorld.import_worldgen_settings": "I<PERSON>rta configuració", "selectWorld.import_worldgen_settings.failure": "Error a l'importar la configuració", "selectWorld.import_worldgen_settings.select_file": "Selecciona el fitxer de configuració (.json)", "selectWorld.incompatible.description": "Aquest món no es pot obrir en aquesta versió.\nEra últim jugat en la versió %s.", "selectWorld.incompatible.info": "Versió incompatible: %s", "selectWorld.incompatible.title": "Versió incompatible", "selectWorld.incompatible.tooltip": "Aquest món no es pot obrir perquè era creat en una versió incompatible.", "selectWorld.incompatible_series": "S’ha creat en una versió incompatible", "selectWorld.load_folder_access": "Incapaç de llegir o accedir a la carpeta on es guarden els mons!", "selectWorld.loading_list": "Carregant la llista de mons", "selectWorld.locked": "Ja obert en una altra finestra de Minecraft", "selectWorld.mapFeatures": "Generar estructures", "selectWorld.mapFeatures.info": "Pobles, naufragis, etc.", "selectWorld.mapType": "Tipus de món", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Més opcions de món...", "selectWorld.newWorld": "Nou món", "selectWorld.recreate": "Restaurar", "selectWorld.recreate.customized.text": "Els mons personalitzats no són compatibles en aquesta versió de Minecraft. Podem intentar refer-lo amb la mateixa llavor i les mateixes propietats, però l'aspecte serà el d'un món normal i corrent. Disculpeu les molèsties!", "selectWorld.recreate.customized.title": "Els mons personalitzats ja no són compatibles", "selectWorld.recreate.error.text": "S'ha produït un error en intentar refer el món.", "selectWorld.recreate.error.title": "S'ha produït un error!", "selectWorld.resource_load": "Preparant recursos...", "selectWorld.resultFolder": "Es desarà a:", "selectWorld.search": "busca mons", "selectWorld.seedInfo": "Deixar en blanc per a una llavor aleatòria", "selectWorld.select": "Juga al món seleccionat", "selectWorld.targetFolder": "Guarda a la carpeta: %s", "selectWorld.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> món", "selectWorld.tooltip.fromNewerVersion1": "El món s'ha desat en una versió més nova,", "selectWorld.tooltip.fromNewerVersion2": "si el carregues, pot haver-hi problemes!", "selectWorld.tooltip.snapshot1": "No t'oblidis de fer una còpia de seguretat del món", "selectWorld.tooltip.snapshot2": "abans de carregar-lo en aquesta versió.", "selectWorld.unable_to_load": "No s'han pogut carregar els mons", "selectWorld.version": "Versió:", "selectWorld.versionJoinButton": "Carrega igualment", "selectWorld.versionQuestion": "Segur que vols carregar aquest món?", "selectWorld.versionUnknown": "desconegut", "selectWorld.versionWarning": "Vas jugar per darrer cop a aquest món amb la versió %s! Si hi jugues a la versió actual, es podria corrompre!", "selectWorld.warning.deprecated.question": "Algunes de les característiques utilitzades són obsoletes i deixaran de funcionar en el futur. Vols continuar?", "selectWorld.warning.deprecated.title": "Atenció! Aquesta opció fa ús de característiques obsoletes", "selectWorld.warning.experimental.question": "Aquestes opcions són experimentals i podrien deixar de funcionar en un futur. Vols continuar?", "selectWorld.warning.experimental.title": "Atenció! Aquestes opcions fan ús de característiques experimentals", "selectWorld.warning.lowDiskSpace.description": "No queda molt espai en el teu dispositiu.\nSi et quedes sense espai mentre que jugues pot provocar que el teu món es corrompi.", "selectWorld.warning.lowDiskSpace.title": "Avis! Poc espai al disc!", "selectWorld.world": "Món", "sign.edit": "Edita el missatge del cartell", "sleep.not_possible": "Impossible dormir durant aquesta nit", "sleep.players_sleeping": "%s/%s jugadors dormint", "sleep.skipping_night": "Dormint fins l'alba...", "slot.only_single_allowed": "A soles es permeten espais individuals, has obtingut '%s'", "slot.unknown": "Ranura desconeguda: %s", "snbt.parser.empty_key": "La tecla no pot estar buida", "snbt.parser.expected_binary_numeral": "S'esperava un nombre binari", "snbt.parser.expected_decimal_numeral": "S'esperava un nombre decimal", "snbt.parser.expected_float_type": "S'esperava un nombre de coma flotant", "snbt.parser.expected_hex_escape": "S'esperava una cadena de longitud %s", "snbt.parser.expected_hex_numeral": "S'esperava un nombre hexadecimal", "snbt.parser.expected_integer_type": "S'esperava un nombre enter", "snbt.parser.expected_non_negative_number": "S'esperava un nombre positiu", "snbt.parser.expected_number_or_boolean": "S'esperava un nombre o una booleana", "snbt.parser.expected_string_uuid": "S'esperava una cadena amb un UUID vàlid", "snbt.parser.expected_unquoted_string": "S'esperava una cadena sense cometes vàlida", "snbt.parser.infinity_not_allowed": "No es permeten nombres infinits", "snbt.parser.invalid_array_element_type": "Tipus de llista d'elements invàlid", "snbt.parser.invalid_character_name": "Nombre de caràcter invàlid", "snbt.parser.invalid_codepoint": "Valor invàlid del caràcter: %s", "snbt.parser.invalid_string_contents": "Invàlid contingut de cadena ", "snbt.parser.invalid_unquoted_start": "Una cadena sense cometes no pot començar amb nombres 0-9, + o -", "snbt.parser.leading_zero_not_allowed": "Els nombres decimals  no poden començar amb 0", "snbt.parser.no_such_operation": "No existeix l'operació: %s", "snbt.parser.number_parse_failure": "Error analitzant el nombre: %s", "snbt.parser.undescore_not_allowed": "Els guions baixos  no estan permesos al principi i final d'un nombre ", "soundCategory.ambient": "Ambient", "soundCategory.block": "Blocs", "soundCategory.hostile": "Criatures hostils", "soundCategory.master": "Volum general", "soundCategory.music": "Música", "soundCategory.neutral": "Criatures passives", "soundCategory.player": "Jugadors", "soundCategory.record": "Blocs musicals", "soundCategory.ui": "UI", "soundCategory.voice": "Veu", "soundCategory.weather": "Clima", "spectatorMenu.close": "Tanca el menú", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON><PERSON> segü<PERSON>", "spectatorMenu.previous_page": "Pàgina anterior", "spectatorMenu.root.prompt": "Prem una per seleccionar una comandament i novament per utilitzar-lo.", "spectatorMenu.team_teleport": "Teletransportar-se a un membre de l'equip", "spectatorMenu.team_teleport.prompt": "Selecciona un equip a qui teletransportar-se", "spectatorMenu.teleport": "Teletransportar-se a un jugador", "spectatorMenu.teleport.prompt": "Selecciona un jugador a qui teletransportar-se", "stat.generalButton": "General", "stat.itemsButton": "Ítems", "stat.minecraft.animals_bred": "Animals criats", "stat.minecraft.aviate_one_cm": "Distància amb èlitres", "stat.minecraft.bell_ring": "Campanes tocades", "stat.minecraft.boat_one_cm": "Distància en barca", "stat.minecraft.clean_armor": "Parts de l'armadura netejades", "stat.minecraft.clean_banner": "Estendard netejat", "stat.minecraft.clean_shulker_box": "Caixes de shulker buidades", "stat.minecraft.climb_one_cm": "Distància escalada", "stat.minecraft.crouch_one_cm": "Distància ajupit", "stat.minecraft.damage_absorbed": "<PERSON><PERSON>it", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON> blo<PERSON> per l'escut", "stat.minecraft.damage_dealt": "Mal infligit", "stat.minecraft.damage_dealt_absorbed": "<PERSON>y causat (absorbit)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON> causat (resistit)", "stat.minecraft.damage_resisted": "Dany resistit", "stat.minecraft.damage_taken": "<PERSON> rebut", "stat.minecraft.deaths": "Nombre de morts", "stat.minecraft.drop": "Ítems llançats", "stat.minecraft.eat_cake_slice": "Trossos de pastís menjats", "stat.minecraft.enchant_item": "Ítems encantats", "stat.minecraft.fall_one_cm": "Distància caiguda", "stat.minecraft.fill_cauldron": "Calderons emplenats", "stat.minecraft.fish_caught": "Peixos pescats", "stat.minecraft.fly_one_cm": "Distància volada", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "Distància a cavall", "stat.minecraft.inspect_dispenser": "Dispensadors inspeccionats", "stat.minecraft.inspect_dropper": "Subministradors inspeccionats", "stat.minecraft.inspect_hopper": "Tremuges inspeccionades", "stat.minecraft.interact_with_anvil": "Interaccions amb encluses", "stat.minecraft.interact_with_beacon": "Interaccions amb fars", "stat.minecraft.interact_with_blast_furnace": "Interaccions amb alts forns", "stat.minecraft.interact_with_brewingstand": "Interaccions amb altars de pocions", "stat.minecraft.interact_with_campfire": "Interaccions amb fogueres", "stat.minecraft.interact_with_cartography_table": "Interaccions amb taules de cartografia", "stat.minecraft.interact_with_crafting_table": "Interaccions amb taules d'elaboració", "stat.minecraft.interact_with_furnace": "Interaccions amb forns", "stat.minecraft.interact_with_grindstone": "Interaccions amb afiladores", "stat.minecraft.interact_with_lectern": "Interaccions amb faristols", "stat.minecraft.interact_with_loom": "Interaccions amb telers", "stat.minecraft.interact_with_smithing_table": "Interaccions amb taules de ferreria", "stat.minecraft.interact_with_smoker": "Interaccions amb fumadors", "stat.minecraft.interact_with_stonecutter": "Interaccions amb talla-roques", "stat.minecraft.jump": "Salts", "stat.minecraft.leave_game": "<PERSON><PERSON> abandonats", "stat.minecraft.minecart_one_cm": "Distància en vagoneta", "stat.minecraft.mob_kills": "Criatures matades", "stat.minecraft.open_barrel": "<PERSON><PERSON> oberts", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON> oberts", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON>'<PERSON> o<PERSON>", "stat.minecraft.open_shulker_box": "Caixes de shulker obertes", "stat.minecraft.pig_one_cm": "Distància en porc", "stat.minecraft.play_noteblock": "Blocs musicals reproduïts", "stat.minecraft.play_record": "Discs reproduïts", "stat.minecraft.play_time": "Temps jugat", "stat.minecraft.player_kills": "Jugadors morts", "stat.minecraft.pot_flower": "Plantes sembrades en testos", "stat.minecraft.raid_trigger": "Setges desencadenats", "stat.minecraft.raid_win": "Setges guanyats", "stat.minecraft.sleep_in_bed": "<PERSON><PERSON> que has dormit en un llit", "stat.minecraft.sneak_time": "Temps ajupit", "stat.minecraft.sprint_one_cm": "Distància corrent", "stat.minecraft.strider_one_cm": "Distància en caminant", "stat.minecraft.swim_one_cm": "Distància nedada", "stat.minecraft.talked_to_villager": "Parlat amb els vilatans", "stat.minecraft.target_hit": "Blancs encertats", "stat.minecraft.time_since_death": "Temps des de l'última mort", "stat.minecraft.time_since_rest": "Temps des de l'últim descans", "stat.minecraft.total_world_time": "Temps amb el món carregat", "stat.minecraft.traded_with_villager": "Negociat amb els vilatans", "stat.minecraft.trigger_trapped_chest": "Cofres trampa activats", "stat.minecraft.tune_noteblock": "Blocs musicals sintonitzats", "stat.minecraft.use_cauldron": "Aigua extreta d'un calderó", "stat.minecraft.walk_on_water_one_cm": "Caminat sobre aigua", "stat.minecraft.walk_one_cm": "Distància caminada", "stat.minecraft.walk_under_water_one_cm": "Caminat sota aigua", "stat.mobsButton": "Criatures", "stat_type.minecraft.broken": "Vegades esgotat", "stat_type.minecraft.crafted": "Vegades elaborat", "stat_type.minecraft.dropped": "Llançat", "stat_type.minecraft.killed": "Has assassinat %s %s", "stat_type.minecraft.killed.none": "<PERSON> has assassinat a %s(s)", "stat_type.minecraft.killed_by": "%s t'ha matat %s vegada(es)", "stat_type.minecraft.killed_by.none": "Mai has estat assassinat per %s", "stat_type.minecraft.mined": "Vegades obtingut", "stat_type.minecraft.picked_up": "<PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.used": "Vegades utilitzat", "stats.none": "-", "structure_block.button.detect_size": "DETECTA", "structure_block.button.load": "CARREGA", "structure_block.button.save": "DESA", "structure_block.custom_data": "Nom d'etiqueta de dades personalitzat", "structure_block.detect_size": "Detecta la mida i posició de l'estructura:", "structure_block.hover.corner": "Vèrtex: %s", "structure_block.hover.data": "Dades: %s", "structure_block.hover.load": "Carrega: %s", "structure_block.hover.save": "Guarda: %s", "structure_block.include_entities": "Inclou entitats:", "structure_block.integrity": "Integritat i llavor de l'estructura", "structure_block.integrity.integrity": "Integritat de l'estructura", "structure_block.integrity.seed": "Llavor de l'estructura", "structure_block.invalid_structure_name": "Nom de l'estructura \"%s\" invàlid", "structure_block.load_not_found": "L'estructura '%s' no està disponible", "structure_block.load_prepare": "S'ha preparat la posició de l'estructura '%s'", "structure_block.load_success": "Estructura carregada des de '%s'", "structure_block.mode.corner": "Cantonada", "structure_block.mode.data": "<PERSON><PERSON>", "structure_block.mode.load": "Carrega", "structure_block.mode.save": "Desar", "structure_block.mode_info.corner": "Mode de cantonades - Indicador de posició i mida", "structure_block.mode_info.data": "Mode de dades - Fes servir la lògica del joc", "structure_block.mode_info.load": "Mode de càrrega - Carrega des de fitxer", "structure_block.mode_info.save": "Mode de desat - Desa en fitxer", "structure_block.position": "Posició relativa", "structure_block.position.x": "posició relativa x", "structure_block.position.y": "posició relativa y", "structure_block.position.z": "posició relativa z", "structure_block.save_failure": "No s'ha pogut guardar l'estructura '%s'", "structure_block.save_success": "Estructura guardada amb el nom '%s'", "structure_block.show_air": "Mostra blocs invisibles:", "structure_block.show_boundingbox": "Mostra límits:", "structure_block.size": "Mida de l'estructura", "structure_block.size.x": "grandària de l'estructura x", "structure_block.size.y": "grandària de l'estructura y", "structure_block.size.z": "grandària de l'estructura z", "structure_block.size_failure": "No s'ha pogut detectar el tamany de l'estructura. Afegeix més cantonades amb el nom de l'estructura corresponent", "structure_block.size_success": "Mida detectada correctament per '%s'", "structure_block.strict": "Posicionament Estricte:", "structure_block.structure_name": "Nom de l'estructura", "subtitles.ambient.cave": "Soroll inquietant", "subtitles.ambient.sound": "<PERSON><PERSON>", "subtitles.block.amethyst_block.chime": "Ametista repica", "subtitles.block.amethyst_block.resonate": "Ametista ressona", "subtitles.block.anvil.destroy": "Enclusa és destruïda", "subtitles.block.anvil.land": "Enclusa aterra", "subtitles.block.anvil.use": "Enclusa és utilitzada", "subtitles.block.barrel.close": "Barril es tanca", "subtitles.block.barrel.open": "Barril s'obre", "subtitles.block.beacon.activate": "Far s'activa", "subtitles.block.beacon.ambient": "Far brunzeix", "subtitles.block.beacon.deactivate": "Far es desactiva", "subtitles.block.beacon.power_select": "Poder del far seleccionat", "subtitles.block.beehive.drip": "<PERSON>", "subtitles.block.beehive.enter": "Abella entra al rusc", "subtitles.block.beehive.exit": "Abella surt del rusc", "subtitles.block.beehive.shear": "Bresca és extreta", "subtitles.block.beehive.work": "<PERSON><PERSON> t<PERSON>", "subtitles.block.bell.resonate": "<PERSON><PERSON> re<PERSON>a", "subtitles.block.bell.use": "Campanades", "subtitles.block.big_dripleaf.tilt_down": "Degotadora s'inclina avall", "subtitles.block.big_dripleaf.tilt_up": "Degotadora s'inclina amunt", "subtitles.block.blastfurnace.fire_crackle": "Alt forn crepita", "subtitles.block.brewing_stand.brew": "Altar de pocions bombolleja", "subtitles.block.bubble_column.bubble_pop": "Bombolles esclaten", "subtitles.block.bubble_column.upwards_ambient": "Corrent ascendeix", "subtitles.block.bubble_column.upwards_inside": "Corrent t'empeny", "subtitles.block.bubble_column.whirlpool_ambient": "Corrent s'arremolina", "subtitles.block.bubble_column.whirlpool_inside": "Corrent et xucla", "subtitles.block.button.click": "Bo<PERSON><PERSON> premut", "subtitles.block.cake.add_candle": "Pastís s'aixafa", "subtitles.block.campfire.crackle": "Foguera espurneja", "subtitles.block.candle.crackle": "<PERSON><PERSON>el<PERSON> crepita", "subtitles.block.candle.extinguish": "Espelma s'extingeix", "subtitles.block.chest.close": "Cofre es tanca", "subtitles.block.chest.locked": "Cofre <PERSON>s bloquejat", "subtitles.block.chest.open": "Cofre s'obre", "subtitles.block.chorus_flower.death": "Flor de tornada es panseix", "subtitles.block.chorus_flower.grow": "Flor de tornada creix", "subtitles.block.comparator.click": "Comparador s'activa", "subtitles.block.composter.empty": "Compostador es buida", "subtitles.block.composter.fill": "Compostador s'omple", "subtitles.block.composter.ready": "Compostador composta", "subtitles.block.conduit.activate": "Canalitzador s'activa", "subtitles.block.conduit.ambient": "Canalitzador batega", "subtitles.block.conduit.attack.target": "Canalitzador ataca", "subtitles.block.conduit.deactivate": "Canalitzador es desactiva", "subtitles.block.copper_bulb.turn_off": "Bombeta de coure s'apaga", "subtitles.block.copper_bulb.turn_on": "Bombeta de coure s'encén", "subtitles.block.copper_trapdoor.close": "Trapa es tanca", "subtitles.block.copper_trapdoor.open": "Trapa s'obre", "subtitles.block.crafter.craft": "Elaborador elaborat", "subtitles.block.crafter.fail": "Elaborador ha fallat", "subtitles.block.creaking_heart.hurt": "Es queixa el cor del cruixidor", "subtitles.block.creaking_heart.idle": "Soroll inquietant", "subtitles.block.creaking_heart.spawn": "Desperta el cor del cruixidor", "subtitles.block.deadbush.idle": "Sons secs", "subtitles.block.decorated_pot.insert": "Gerro decorat es plena", "subtitles.block.decorated_pot.insert_fail": "Gerro decorat es sacseja", "subtitles.block.decorated_pot.shatter": "Gerro decorat es trenca", "subtitles.block.dispenser.dispense": "Ítem és dispensat", "subtitles.block.dispenser.fail": "Dispensador falla", "subtitles.block.door.toggle": "<PERSON>a grinyola", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "<PERSON><PERSON> d'encanteris s'usa", "subtitles.block.end_portal.spawn": "Portal de l'End s'obre", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON> <PERSON>'<PERSON><PERSON> é<PERSON> col·locat", "subtitles.block.eyeblossom.close": "Iridàcia es tanca", "subtitles.block.eyeblossom.idle": "Iridàcia xiuxiueja", "subtitles.block.eyeblossom.open": "Iridàcia s'obre", "subtitles.block.fence_gate.toggle": "Porta de tanca grinyola", "subtitles.block.fire.ambient": "Foc crepita", "subtitles.block.fire.extinguish": "Foc és extingit", "subtitles.block.firefly_bush.idle": "<PERSON><PERSON><PERSON><PERSON> de cuques de llum", "subtitles.block.frogspawn.hatch": "Capgrossos neixen", "subtitles.block.furnace.fire_crackle": "Forn crepita", "subtitles.block.generic.break": "Bloc és trencat", "subtitles.block.generic.fall": "Alguna cosa cau a un bloc", "subtitles.block.generic.footsteps": "Passos", "subtitles.block.generic.hit": "Bloc trencant-se", "subtitles.block.generic.place": "Bloc és col·locat", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.growing_plant.crop": "Planta és retallada", "subtitles.block.hanging_sign.waxed_interact_fail": "Cartell es balanceja", "subtitles.block.honey_block.slide": "Lliscant en un bloc de mel", "subtitles.block.iron_trapdoor.close": "Trapa es tanca", "subtitles.block.iron_trapdoor.open": "Trapa s'obre", "subtitles.block.lava.ambient": "Esclats de lava", "subtitles.block.lava.extinguish": "<PERSON>va xiscla", "subtitles.block.lever.click": "Palanca activada", "subtitles.block.note_block.note": "Bloc musical sona", "subtitles.block.pale_hanging_moss.idle": "Soroll inquietant", "subtitles.block.piston.move": "Moviment de pistó", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> got<PERSON>a", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava goteja dins un calderó", "subtitles.block.pointed_dripstone.drip_water": "Aigua goteja", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Aigua goteja dins un calderó", "subtitles.block.pointed_dripstone.land": "Estalactita s'estavella", "subtitles.block.portal.ambient": "Brunzit de portal", "subtitles.block.portal.travel": "Soroll del portal s'esvaeix", "subtitles.block.portal.trigger": "Soroll del portal s'intensifica", "subtitles.block.pressure_plate.click": "Placa de pressió activada", "subtitles.block.pumpkin.carve": "Carbassa és buidada", "subtitles.block.redstone_torch.burnout": "Torxa crema", "subtitles.block.respawn_anchor.ambient": "Brunzit de portal", "subtitles.block.respawn_anchor.charge": "Àncora de reaparició és carregada", "subtitles.block.respawn_anchor.deplete": "Àncora de reaparició es buida", "subtitles.block.respawn_anchor.set_spawn": "Àncora de reaparició estableix el punt de reaparició", "subtitles.block.sand.idle": "Sons de sorra", "subtitles.block.sand.wind": "Sons de vent", "subtitles.block.sculk.charge": "Sculk bombolleja", "subtitles.block.sculk.spread": "Sculk s'escampa", "subtitles.block.sculk_catalyst.bloom": "Sculk catalitzador floreix", "subtitles.block.sculk_sensor.clicking": "Sensor sculk vibra", "subtitles.block.sculk_sensor.clicking_stop": "Sensor sculk deixa de clicar", "subtitles.block.sculk_shrieker.shriek": "Sculk xisclador xiscla", "subtitles.block.shulker_box.close": "Shulker es tanca", "subtitles.block.shulker_box.open": "<PERSON>lk<PERSON> s'obre", "subtitles.block.sign.waxed_interact_fail": "Cartell es balanceja", "subtitles.block.smithing_table.use": "<PERSON><PERSON> de ferreria utilitzada", "subtitles.block.smoker.smoke": "El fumador està fumant", "subtitles.block.sniffer_egg.crack": "L'ou d'ensumador desclosa", "subtitles.block.sniffer_egg.hatch": "L'ou d'ensumador desclosa", "subtitles.block.sniffer_egg.plop": "Ensumador pon un ou", "subtitles.block.sponge.absorb": "L'esponja xucla", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON>es s<PERSON> recollides", "subtitles.block.trapdoor.close": "Trapa es tanca", "subtitles.block.trapdoor.open": "Trapa s'obre", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON><PERSON> grinyola", "subtitles.block.trial_spawner.about_to_spawn_item": "Ítem ominós es prepara", "subtitles.block.trial_spawner.ambient": "Generador de reptes fa crepitades", "subtitles.block.trial_spawner.ambient_charged": "Generador de reptes ominós fa crepitades", "subtitles.block.trial_spawner.ambient_ominous": "Generador de reptes ominós fa crepitades", "subtitles.block.trial_spawner.charge_activate": "Presagi engoleix un generador de reptes", "subtitles.block.trial_spawner.close_shutter": "Generador de reptes es tanca", "subtitles.block.trial_spawner.detect_player": "Generador de reptes es carrega", "subtitles.block.trial_spawner.eject_item": "Generador de reptes expulsa ítems", "subtitles.block.trial_spawner.ominous_activate": "Presagi engoleix un generador de reptes", "subtitles.block.trial_spawner.open_shutter": "Generador de reptes s'obre", "subtitles.block.trial_spawner.spawn_item": "Ítem o<PERSON>ós cau", "subtitles.block.trial_spawner.spawn_item_begin": "Ítem ominós aparei<PERSON>", "subtitles.block.trial_spawner.spawn_mob": "Criatura genera", "subtitles.block.tripwire.attach": "Corda lligada", "subtitles.block.tripwire.click": "Corda espetega", "subtitles.block.tripwire.detach": "<PERSON><PERSON>", "subtitles.block.vault.activate": "Caixa forta encesa", "subtitles.block.vault.ambient": "Caixa forta crepita", "subtitles.block.vault.close_shutter": "Caixa forta tanca", "subtitles.block.vault.deactivate": "Caixa forta s'extingeix", "subtitles.block.vault.eject_item": "Caixa forta expulsa un objecte", "subtitles.block.vault.insert_item": "Caixa forta desbloquejada", "subtitles.block.vault.insert_item_fail": "Caixa forta no s'ha pogut desblo<PERSON>jar", "subtitles.block.vault.open_shutter": "Caixa forta s'obre", "subtitles.block.vault.reject_rewarded_player": "Caixa forta rebutja jugador ja recompensat", "subtitles.block.water.ambient": "Flux d'aigua", "subtitles.block.wet_sponge.dries": "Esponja s'asseca", "subtitles.chiseled_bookshelf.insert": "Llibre és col·locat", "subtitles.chiseled_bookshelf.insert_enchanted": "Llibre encantat col·locat", "subtitles.chiseled_bookshelf.take": "Llibre és retirat", "subtitles.chiseled_bookshelf.take_enchanted": "Llibre encantat pres", "subtitles.enchant.thorns.hit": "Punxada d'unes espines", "subtitles.entity.allay.ambient_with_item": "Allay cerca", "subtitles.entity.allay.ambient_without_item": "<PERSON>ay anhela", "subtitles.entity.allay.death": "Allay mor", "subtitles.entity.allay.hurt": "Allay és ferit", "subtitles.entity.allay.item_given": "Allay riu", "subtitles.entity.allay.item_taken": "<PERSON>ay ajuda", "subtitles.entity.allay.item_thrown": "<PERSON><PERSON> ll<PERSON>", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.armadillo.brush": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> ferit", "subtitles.entity.armadillo.hurt_reduced": "L'armadillo es cobreix", "subtitles.entity.armadillo.land": "<PERSON><PERSON><PERSON> aterra", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON>ra", "subtitles.entity.armadillo.roll": "Armadillo s'enrosca", "subtitles.entity.armadillo.scute_drop": "Escata cau", "subtitles.entity.armadillo.unroll_finish": "Armadillo es desenrosca", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON>ra", "subtitles.entity.armor_stand.fall": "Maniquí cau", "subtitles.entity.arrow.hit": "Impacte de fletxa", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON> ferit", "subtitles.entity.arrow.shoot": "Fletxa disparada", "subtitles.entity.axolotl.attack": "<PERSON><PERSON><PERSON>t ataca", "subtitles.entity.axolotl.death": "Axolot mor", "subtitles.entity.axolotl.hurt": "Axolot és ferit", "subtitles.entity.axolotl.idle_air": "Axolot xiscla", "subtitles.entity.axolotl.idle_water": "Axolot xiscla", "subtitles.entity.axolotl.splash": "Axolot esquitxa", "subtitles.entity.axolotl.swim": "<PERSON><PERSON><PERSON><PERSON> neda", "subtitles.entity.bat.ambient": "Ratpenat xiscla", "subtitles.entity.bat.death": "Ratpenat mor", "subtitles.entity.bat.hurt": "Ratpenat és ferit", "subtitles.entity.bat.takeoff": "Ratpenat s'enlaira", "subtitles.entity.bee.ambient": "<PERSON><PERSON> brun<PERSON>", "subtitles.entity.bee.death": "<PERSON><PERSON> mor", "subtitles.entity.bee.hurt": "Abella és ferida", "subtitles.entity.bee.loop": "<PERSON><PERSON> brun<PERSON>", "subtitles.entity.bee.loop_aggressive": "Abella brunzeix furiosament", "subtitles.entity.bee.pollinate": "<PERSON><PERSON> brunzint feliçment", "subtitles.entity.bee.sting": "<PERSON><PERSON>", "subtitles.entity.blaze.ambient": "Esperit del foc respira", "subtitles.entity.blaze.burn": "Esperit del foc crepita", "subtitles.entity.blaze.death": "Esperit del foc mor", "subtitles.entity.blaze.hurt": "Esperit del foc és ferit", "subtitles.entity.blaze.shoot": "Esperit del doc dispara", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Esquelet empantanat trontolla", "subtitles.entity.bogged.death": "Aiguamoll mor", "subtitles.entity.bogged.hurt": "Aiguamoll ferit", "subtitles.entity.breeze.charge": "<PERSON><PERSON><PERSON> carrega", "subtitles.entity.breeze.death": "<PERSON>ze mor", "subtitles.entity.breeze.deflect": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.hurt": "<PERSON>ze ferit", "subtitles.entity.breeze.idle_air": "Breeze vola", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.inhale": "Breeze inspira", "subtitles.entity.breeze.jump": "Breeze salta", "subtitles.entity.breeze.land": "Breeze aterra", "subtitles.entity.breeze.shoot": "<PERSON><PERSON> dispara", "subtitles.entity.breeze.slide": "Breeze llisca", "subtitles.entity.breeze.whirl": "Brisa fa remolins", "subtitles.entity.breeze.wind_burst": "Càrrega de vent esclata", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON><PERSON> s'impulsa", "subtitles.entity.camel.dash_ready": "Dromedari es recupera", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON><PERSON> mor", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON><PERSON> menja", "subtitles.entity.camel.hurt": "Dromedari és ferit", "subtitles.entity.camel.saddle": "Sella de muntar és equipada", "subtitles.entity.camel.sit": "Dromedari s'asseu", "subtitles.entity.camel.stand": "Dromedari s'aixeca", "subtitles.entity.camel.step": "Dromedari camina", "subtitles.entity.camel.step_sand": "Dromedari camina per sorra", "subtitles.entity.cat.ambient": "Miols de gat", "subtitles.entity.cat.beg_for_food": "Gat demana", "subtitles.entity.cat.death": "Gat mor", "subtitles.entity.cat.eat": "Gat menja", "subtitles.entity.cat.hiss": "Gat xiscla", "subtitles.entity.cat.hurt": "Gat és ferit", "subtitles.entity.cat.purr": "Gat ronca", "subtitles.entity.chicken.ambient": "Pollastre clo<PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.chicken.egg": "Ou post", "subtitles.entity.chicken.hurt": "Pollastre és ferit", "subtitles.entity.cod.death": "Bacallà mor", "subtitles.entity.cod.flop": "Bacallà xipolleja", "subtitles.entity.cod.hurt": "Bacallà és ferit", "subtitles.entity.cow.ambient": "Vaca mugeix", "subtitles.entity.cow.death": "Vaca mor", "subtitles.entity.cow.hurt": "Vaca és ferida", "subtitles.entity.cow.milk": "Vaca es muny", "subtitles.entity.creaking.activate": "Cruixidor observa", "subtitles.entity.creaking.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> cruix", "subtitles.entity.creaking.attack": "C<PERSON>ix<PERSON>r ataca", "subtitles.entity.creaking.deactivate": "Cruixidor es calma", "subtitles.entity.creaking.death": "Cruixidor s'esmicola", "subtitles.entity.creaking.freeze": "Cruixidor para", "subtitles.entity.creaking.spawn": "Cruixidor es manifesta", "subtitles.entity.creaking.sway": "Cruixidor és atacat", "subtitles.entity.creaking.twitch": "Contraccions de cruixidor", "subtitles.entity.creaking.unfreeze": "cruixidor es mou", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.creeper.hurt": "Creeper és ferit", "subtitles.entity.creeper.primed": "Creeper xiscla", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON> x<PERSON>", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON> ferit", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON>a", "subtitles.entity.dolphin.play": "Dof<PERSON> juga", "subtitles.entity.dolphin.splash": "Dofí esquitxa", "subtitles.entity.dolphin.swim": "<PERSON><PERSON><PERSON>da", "subtitles.entity.donkey.ambient": "<PERSON><PERSON>", "subtitles.entity.donkey.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.chest": "S'equipa un cofre a un ruc", "subtitles.entity.donkey.death": "<PERSON><PERSON> mor", "subtitles.entity.donkey.eat": "<PERSON><PERSON>", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> <PERSON>s ferit", "subtitles.entity.donkey.jump": "<PERSON><PERSON>", "subtitles.entity.drowned.ambient": "Ofegat borbolla", "subtitles.entity.drowned.ambient_water": "Ofegat borbolla", "subtitles.entity.drowned.death": "Ofegat mor", "subtitles.entity.drowned.hurt": "Ofegat és ferit", "subtitles.entity.drowned.shoot": "Ofegat llança un trident", "subtitles.entity.drowned.step": "Ofegat camina", "subtitles.entity.drowned.swim": "Ofegat neda", "subtitles.entity.egg.throw": "<PERSON><PERSON>", "subtitles.entity.elder_guardian.ambient": "Guardià ancià gemega", "subtitles.entity.elder_guardian.ambient_land": "Guardià ancià neda", "subtitles.entity.elder_guardian.curse": "Guardià anci<PERSON> male<PERSON>", "subtitles.entity.elder_guardian.death": "Guardià ancià mor", "subtitles.entity.elder_guardian.flop": "Guardià ancià xipoll<PERSON>a", "subtitles.entity.elder_guardian.hurt": "Guardià ancià és ferit", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON> mor", "subtitles.entity.ender_dragon.flap": "Drac vola", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON>", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON> <PERSON> ferit", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON> dispara", "subtitles.entity.ender_eye.death": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> cau", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> disparat", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> d'Ender vola", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> murmura", "subtitles.entity.enderman.death": "<PERSON><PERSON> mor", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> és ferit", "subtitles.entity.enderman.scream": "<PERSON><PERSON> crida", "subtitles.entity.enderman.stare": "<PERSON><PERSON> xiscla", "subtitles.entity.enderman.teleport": "Enderman es teletransporta", "subtitles.entity.endermite.ambient": "Endermite s'arrossega", "subtitles.entity.endermite.death": "Endermite mor", "subtitles.entity.endermite.hurt": "Endermite és ferit", "subtitles.entity.evoker.ambient": "Invocador murmura", "subtitles.entity.evoker.cast_spell": "Invocador llança un encanteri", "subtitles.entity.evoker.celebrate": "Invocador celebra", "subtitles.entity.evoker.death": "Invocador mor", "subtitles.entity.evoker.hurt": "Invocador és ferit", "subtitles.entity.evoker.prepare_attack": "Invocador es prepara per atacar", "subtitles.entity.evoker.prepare_summon": "Invocador crida els mals esperits", "subtitles.entity.evoker.prepare_wololo": "Invocador es prepara per a maleficiar", "subtitles.entity.evoker_fangs.attack": "<PERSON><PERSON><PERSON> d'ul<PERSON> mossega", "subtitles.entity.experience_orb.pickup": "Experiència guanyada", "subtitles.entity.firework_rocket.blast": "Focs artificials exploten", "subtitles.entity.firework_rocket.launch": "Focs artificials són llançats", "subtitles.entity.firework_rocket.twinkle": "Espurnes de focs artificials", "subtitles.entity.fish.swim": "Esquitxades", "subtitles.entity.fishing_bobber.retrieve": "<PERSON> recollit", "subtitles.entity.fishing_bobber.splash": "Ham esquitxa", "subtitles.entity.fishing_bobber.throw": "<PERSON>", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON> s'enfada", "subtitles.entity.fox.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.bite": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.fox.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.hurt": "<PERSON><PERSON><PERSON> és ferida", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON> x<PERSON>la", "subtitles.entity.fox.sleep": "<PERSON><PERSON><PERSON> ron<PERSON>", "subtitles.entity.fox.sniff": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.spit": "<PERSON><PERSON><PERSON> escup", "subtitles.entity.fox.teleport": "Guineu es teletransporta", "subtitles.entity.frog.ambient": "Granota rauca", "subtitles.entity.frog.death": "Granota mor", "subtitles.entity.frog.eat": "Granota menja", "subtitles.entity.frog.hurt": "Granota és ferida", "subtitles.entity.frog.lay_spawn": "Granota pon ous", "subtitles.entity.frog.long_jump": "Granota salta", "subtitles.entity.generic.big_fall": "Quelcom cau al terra", "subtitles.entity.generic.burn": "Es crema", "subtitles.entity.generic.death": "Agonia", "subtitles.entity.generic.drink": "<PERSON><PERSON>", "subtitles.entity.generic.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.explode": "Explosió", "subtitles.entity.generic.extinguish_fire": "Foc extingit", "subtitles.entity.generic.hurt": "Quelcom és ferit", "subtitles.entity.generic.small_fall": "Lleu impacte contra el terra", "subtitles.entity.generic.splash": "Esquitxades", "subtitles.entity.generic.swim": "<PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Càrrega de vent esclata", "subtitles.entity.ghast.ambient": "Ghast plora", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> ferit", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> dispara", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "S'omple un marc lluent", "subtitles.entity.glow_item_frame.break": "Se trenca un marc lluent", "subtitles.entity.glow_item_frame.place": "Es col·loca un marc lluent", "subtitles.entity.glow_item_frame.remove_item": "Es buida un marc lluent", "subtitles.entity.glow_item_frame.rotate_item": "Ítem en un marc lluent és rodat", "subtitles.entity.glow_squid.ambient": "Calamar lluent neda", "subtitles.entity.glow_squid.death": "Cal<PERSON>r lluent mor", "subtitles.entity.glow_squid.hurt": "Calamar lluent és ferit", "subtitles.entity.glow_squid.squirt": "Calamar lluent dispara tinta", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> bela", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.horn_break": "Banya de cabra es trenca", "subtitles.entity.goat.hurt": "Cabra és ferida", "subtitles.entity.goat.long_jump": "<PERSON><PERSON><PERSON> salta", "subtitles.entity.goat.milk": "Cabra és munyida", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON><PERSON> trepitja", "subtitles.entity.goat.ram_impact": "Cabra envesteix", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON><PERSON> brama", "subtitles.entity.goat.step": "Cabra camina", "subtitles.entity.guardian.ambient": "Guardià gemega", "subtitles.entity.guardian.ambient_land": "Guardià neda", "subtitles.entity.guardian.attack": "Guardià dispara", "subtitles.entity.guardian.death": "Guardià mor", "subtitles.entity.guardian.flop": "Guardià <PERSON>a", "subtitles.entity.guardian.hurt": "Guardià és ferit", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> rugeix furiosament", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.hoglin.converted_to_zombified": "Hoglin es converteix en zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON>n és ferit", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> recula", "subtitles.entity.hoglin.step": "Hoglin camina", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.armor": "Armadura de cavall equipada", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON> respira", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.gallop": "Cavall galopa", "subtitles.entity.horse.hurt": "Cavall és ferit", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON> salta", "subtitles.entity.horse.saddle": "Sella de muntar és equipada", "subtitles.entity.husk.ambient": "Zombi ressec remuga", "subtitles.entity.husk.converted_to_zombie": "Zombi ressec es converteix en zombi", "subtitles.entity.husk.death": "<PERSON><PERSON><PERSON> ressec mor", "subtitles.entity.husk.hurt": "Zombi ressec és ferit", "subtitles.entity.illusioner.ambient": "Il·lusionista murmura", "subtitles.entity.illusioner.cast_spell": "Il·lusionista llança un encanteri", "subtitles.entity.illusioner.death": "Il·lusionista mor", "subtitles.entity.illusioner.hurt": "Il·lusionista és ferit", "subtitles.entity.illusioner.mirror_move": "Il·lusionista es desplaça", "subtitles.entity.illusioner.prepare_blindness": "Il·lusionista es prepara per a encegar", "subtitles.entity.illusioner.prepare_mirror": "Il·lusionista es prepara per multiplicar-se", "subtitles.entity.iron_golem.attack": "Gòlem de ferro ataca", "subtitles.entity.iron_golem.damage": "Gòlem de ferro es trenca", "subtitles.entity.iron_golem.death": "Gòlem de ferro mor", "subtitles.entity.iron_golem.hurt": "Gòlem de ferro és ferit", "subtitles.entity.iron_golem.repair": "Gòlem de ferro reparat", "subtitles.entity.item.break": "Ítem es trenca", "subtitles.entity.item.pickup": "Ítem és recollit", "subtitles.entity.item_frame.add_item": "Ítem és emmarcat", "subtitles.entity.item_frame.break": "<PERSON>", "subtitles.entity.item_frame.place": "Marc <PERSON> col·locat", "subtitles.entity.item_frame.remove_item": "<PERSON> buid<PERSON>", "subtitles.entity.item_frame.rotate_item": "Ítem emmarcat és rodat", "subtitles.entity.leash_knot.break": "El nus de la corretja es trenca", "subtitles.entity.leash_knot.place": "Nus de corretja lligat", "subtitles.entity.lightning_bolt.impact": "Llamp", "subtitles.entity.lightning_bolt.thunder": "Trons", "subtitles.entity.llama.ambient": "<PERSON><PERSON><PERSON> bala", "subtitles.entity.llama.angry": "Llama bela furiosament", "subtitles.entity.llama.chest": "S'equipa un cofre a una llama", "subtitles.entity.llama.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.llama.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.llama.hurt": "Llama és ferida", "subtitles.entity.llama.spit": "Llama escup", "subtitles.entity.llama.step": "Llama camina", "subtitles.entity.llama.swag": "Llama decorada", "subtitles.entity.magma_cube.death": "Cub de magma mor", "subtitles.entity.magma_cube.hurt": "Cub de magma és ferit", "subtitles.entity.magma_cube.squish": "Cub de magma salta", "subtitles.entity.minecart.inside": "<PERSON><PERSON> de Minecraft", "subtitles.entity.minecart.inside_underwater": "Jangles de Minecraft sota l'aigua", "subtitles.entity.minecart.riding": "Vagon<PERSON> rod<PERSON>", "subtitles.entity.mooshroom.convert": "Mooshroom es transforma", "subtitles.entity.mooshroom.eat": "Mooshroom menja", "subtitles.entity.mooshroom.milk": "Mooshroom és munyida", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom és munyida amb recel", "subtitles.entity.mule.ambient": "<PERSON><PERSON> bramula", "subtitles.entity.mule.angry": "<PERSON><PERSON> brama", "subtitles.entity.mule.chest": "S'equipa un cofre a una mula", "subtitles.entity.mule.death": "<PERSON><PERSON> mor", "subtitles.entity.mule.eat": "<PERSON><PERSON>", "subtitles.entity.mule.hurt": "Mula és ferida", "subtitles.entity.mule.jump": "<PERSON><PERSON> salta", "subtitles.entity.painting.break": "Q<PERSON>re des<PERSON>", "subtitles.entity.painting.place": "Quadre col·locat", "subtitles.entity.panda.aggressive_ambient": "Panda panteixa", "subtitles.entity.panda.ambient": "Panda esbufega", "subtitles.entity.panda.bite": "Panda mossega", "subtitles.entity.panda.cant_breed": "Gemec de panda", "subtitles.entity.panda.death": "Panda mor", "subtitles.entity.panda.eat": "Panda menja", "subtitles.entity.panda.hurt": "Panda és ferit", "subtitles.entity.panda.pre_sneeze": "Pessigolles al nas del panda", "subtitles.entity.panda.sneeze": "Panda esternuda", "subtitles.entity.panda.step": "Panda camina", "subtitles.entity.panda.worried_ambient": "<PERSON><PERSON><PERSON><PERSON>da", "subtitles.entity.parrot.ambient": "Lloro xerra", "subtitles.entity.parrot.death": "Lloro mor", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.fly": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.hurts": "Lloro és ferit", "subtitles.entity.parrot.imitate.blaze": "Lloro respira", "subtitles.entity.parrot.imitate.bogged": "Lloro imita un esquelet empantanat", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.creaking": "Lloro imita un cruixidor", "subtitles.entity.parrot.imitate.creeper": "Lloro xiscla", "subtitles.entity.parrot.imitate.drowned": "Lloro fa gàrgares", "subtitles.entity.parrot.imitate.elder_guardian": "Lloro gemega", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.endermite": "Lloro s'arrossega", "subtitles.entity.parrot.imitate.evoker": "Lloro murmura", "subtitles.entity.parrot.imitate.ghast": "Lloro plora", "subtitles.entity.parrot.imitate.guardian": "Lloro gemega", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON> remuga", "subtitles.entity.parrot.imitate.illusioner": "Lloro murmura", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON><PERSON> salta", "subtitles.entity.parrot.imitate.phantom": "Lloro xiscla", "subtitles.entity.parrot.imitate.piglin": "Lloro esbufega", "subtitles.entity.parrot.imitate.piglin_brute": "Lloro esbufega", "subtitles.entity.parrot.imitate.pillager": "Lloro murmura", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.shulker": "Lloro a<PERSON>ita", "subtitles.entity.parrot.imitate.silverfish": "Lloro xiscla", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON> tron<PERSON>", "subtitles.entity.parrot.imitate.slime": "Lloro s'aixafa i salta", "subtitles.entity.parrot.imitate.spider": "Lloro xiscla", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON><PERSON> tron<PERSON>", "subtitles.entity.parrot.imitate.vex": "Lloro turmenta", "subtitles.entity.parrot.imitate.vindicator": "Lloro murmura", "subtitles.entity.parrot.imitate.warden": "Lloro es lamenta", "subtitles.entity.parrot.imitate.witch": "Lloro riu", "subtitles.entity.parrot.imitate.wither": "Lloro s'enfada", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON><PERSON> tron<PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON> remuga", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON> remuga", "subtitles.entity.phantom.ambient": "Fantasma xiscla", "subtitles.entity.phantom.bite": "Fantas<PERSON> mossega", "subtitles.entity.phantom.death": "Fantasma mor", "subtitles.entity.phantom.flap": "Fantasma ale<PERSON>ja", "subtitles.entity.phantom.hurt": "Fantasma és ferit", "subtitles.entity.phantom.swoop": "Fantasma s'apropa", "subtitles.entity.pig.ambient": "<PERSON><PERSON>", "subtitles.entity.pig.death": "<PERSON><PERSON> mor", "subtitles.entity.pig.hurt": "Porc <PERSON> ferit", "subtitles.entity.pig.saddle": "Sella de muntar és equipada", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> admira un ítem", "subtitles.entity.piglin.ambient": "<PERSON>lin esbufega", "subtitles.entity.piglin.angry": "Piglin esbufega furiosament", "subtitles.entity.piglin.celebrate": "<PERSON>lin celebra", "subtitles.entity.piglin.converted_to_zombified": "Piglin es converteix en piglin zombificat", "subtitles.entity.piglin.death": "<PERSON><PERSON> mor", "subtitles.entity.piglin.hurt": "<PERSON>lin és ferit", "subtitles.entity.piglin.jealous": "Piglin esbufega gelosament", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> recula", "subtitles.entity.piglin.step": "Piglin camina", "subtitles.entity.piglin_brute.ambient": "Piglin ferotge esbufega", "subtitles.entity.piglin_brute.angry": "Piglin ferotge esbufega furiosament", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin ferotge es converteix en piglin zombificat", "subtitles.entity.piglin_brute.death": "<PERSON>lin ferotge mor", "subtitles.entity.piglin_brute.hurt": "<PERSON>lin ferotge és ferit", "subtitles.entity.piglin_brute.step": "Piglin ferotge camina", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.celebrate": "Saquejador celebra", "subtitles.entity.pillager.death": "Saqueja<PERSON> mor", "subtitles.entity.pillager.hurt": "Saquejador <PERSON> ferit", "subtitles.entity.player.attack.crit": "<PERSON><PERSON>", "subtitles.entity.player.attack.knockback": "Atac amb retrocés", "subtitles.entity.player.attack.strong": "Atac fort", "subtitles.entity.player.attack.sweep": "Atac d'escombrat", "subtitles.entity.player.attack.weak": "Atac feble", "subtitles.entity.player.burp": "Rot", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.player.freeze_hurt": "Jugador es congela", "subtitles.entity.player.hurt": "Jugador és ferit", "subtitles.entity.player.hurt_drown": "Jugador s'ofega", "subtitles.entity.player.hurt_on_fire": "Jugador es crema", "subtitles.entity.player.levelup": "Jugador puja de nivell", "subtitles.entity.player.teleport": "Jugador teleporta", "subtitles.entity.polar_bear.ambient": "Ós polar remuga", "subtitles.entity.polar_bear.ambient_baby": "Ós polar taral·leja", "subtitles.entity.polar_bear.death": "Ós polar mor", "subtitles.entity.polar_bear.hurt": "Ós polar és ferit", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON> polar rugeix", "subtitles.entity.potion.splash": "Ampolla trencada", "subtitles.entity.potion.throw": "Ampolla llen<PERSON>", "subtitles.entity.puffer_fish.blow_out": "Peix globus es desinfla", "subtitles.entity.puffer_fish.blow_up": "Peix globus s'infla", "subtitles.entity.puffer_fish.death": "Peix globus mor", "subtitles.entity.puffer_fish.flop": "Peix globus xipolleja", "subtitles.entity.puffer_fish.hurt": "Peix globus és ferit", "subtitles.entity.puffer_fish.sting": "Peix globus pica", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON> güella", "subtitles.entity.rabbit.attack": "<PERSON><PERSON> ataca", "subtitles.entity.rabbit.death": "<PERSON><PERSON> mor", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON> ferit", "subtitles.entity.rabbit.jump": "<PERSON><PERSON> salta", "subtitles.entity.ravager.ambient": "Devast<PERSON> gruny", "subtitles.entity.ravager.attack": "Devast<PERSON> moss<PERSON>a", "subtitles.entity.ravager.celebrate": "Devastador celebra", "subtitles.entity.ravager.death": "Devast<PERSON> mor", "subtitles.entity.ravager.hurt": "Devastador és ferit", "subtitles.entity.ravager.roar": "Dev<PERSON><PERSON>", "subtitles.entity.ravager.step": "Devastador camina", "subtitles.entity.ravager.stunned": "Devastador at<PERSON>it", "subtitles.entity.salmon.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.salmon.flop": "<PERSON><PERSON><PERSON>", "subtitles.entity.salmon.hurt": "Salmó <PERSON>s ferit", "subtitles.entity.sheep.ambient": "<PERSON><PERSON><PERSON> bela", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.sheep.hurt": "Ovella és ferida", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "Shulker es tanca", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> ferit", "subtitles.entity.shulker.open": "<PERSON>lk<PERSON> s'obre", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> dispara", "subtitles.entity.shulker.teleport": "Shulker es teletransporta", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON> explota", "subtitles.entity.shulker_bullet.hurt": "Bala de shulker es trenca", "subtitles.entity.silverfish.ambient": "Peixet de plata xiscla", "subtitles.entity.silverfish.death": "Peixet de plata mor", "subtitles.entity.silverfish.hurt": "Peixet de plata és ferit", "subtitles.entity.skeleton.ambient": "<PERSON><PERSON><PERSON> trontolla", "subtitles.entity.skeleton.converted_to_stray": "Esquelet s'extravia", "subtitles.entity.skeleton.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.skeleton.hurt": "Esquelet és ferit", "subtitles.entity.skeleton.shoot": "<PERSON><PERSON><PERSON> dispara", "subtitles.entity.skeleton_horse.ambient": "<PERSON><PERSON><PERSON> esquelètic plora", "subtitles.entity.skeleton_horse.death": "<PERSON><PERSON><PERSON> esquel<PERSON><PERSON> mor", "subtitles.entity.skeleton_horse.hurt": "<PERSON><PERSON><PERSON> esquel<PERSON><PERSON> ferit", "subtitles.entity.skeleton_horse.jump_water": "<PERSON><PERSON>ll esquelètic saltant", "subtitles.entity.skeleton_horse.swim": "<PERSON><PERSON><PERSON> es<PERSON><PERSON> neda", "subtitles.entity.slime.attack": "Llim ataca", "subtitles.entity.slime.death": "<PERSON><PERSON> mor", "subtitles.entity.slime.hurt": "Llim és ferit", "subtitles.entity.slime.squish": "Llim s'aixafa i salta", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON><PERSON> mor", "subtitles.entity.sniffer.digging": "Ensumador cava", "subtitles.entity.sniffer.digging_stop": "Ensumador s'aixeca", "subtitles.entity.sniffer.drop_seed": "Ensumador deixa caure llavor", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.egg_crack": "<PERSON><PERSON> d'ensumador s'esquerda", "subtitles.entity.sniffer.egg_hatch": "L'ou d'ensumador desclosa", "subtitles.entity.sniffer.happy": "Ensumador es delecta", "subtitles.entity.sniffer.hurt": "Ensumador <PERSON> ferit", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "subtitles.entity.sniffer.searching": "Ensumador busca", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.step": "Ensumador camina", "subtitles.entity.snow_golem.death": "Gòlem de neu mor", "subtitles.entity.snow_golem.hurt": "Gòlem de neu és ferit", "subtitles.entity.snowball.throw": "Bola de neu vola", "subtitles.entity.spider.ambient": "Aranya xiscla", "subtitles.entity.spider.death": "Ara<PERSON> mor", "subtitles.entity.spider.hurt": "Aranya és ferida", "subtitles.entity.squid.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.squid.hurt": "Calamar és ferit", "subtitles.entity.squid.squirt": "Calamar dispara tinta", "subtitles.entity.stray.ambient": "Esquelet extraviat trontolla", "subtitles.entity.stray.death": "<PERSON>squelet extraviat mor", "subtitles.entity.stray.hurt": "Esquelet extraviat és ferit", "subtitles.entity.strider.death": "Caminant mor", "subtitles.entity.strider.eat": "Caminant menja", "subtitles.entity.strider.happy": "Caminant entona", "subtitles.entity.strider.hurt": "Caminant és ferit", "subtitles.entity.strider.idle": "Caminant piula", "subtitles.entity.strider.retreat": "Caminant recula", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> mor", "subtitles.entity.tadpole.flop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.grow_up": "Capgròs creix", "subtitles.entity.tadpole.hurt": "Capgròs és ferit", "subtitles.entity.tnt.primed": "TNT encesa", "subtitles.entity.tropical_fish.death": "Peix tropical mor", "subtitles.entity.tropical_fish.flop": "Peix tropical xipolleja", "subtitles.entity.tropical_fish.hurt": "Peix tropical és ferit", "subtitles.entity.turtle.ambient_land": "<PERSON><PERSON><PERSON> grinyola", "subtitles.entity.turtle.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.turtle.death_baby": "Cria de tortuga mor", "subtitles.entity.turtle.egg_break": "Ou de tortuga es trenca", "subtitles.entity.turtle.egg_crack": "Ou de tortuga s'esquerda", "subtitles.entity.turtle.egg_hatch": "Ou de tortuga eclosiona", "subtitles.entity.turtle.hurt": "Tortuga és ferida", "subtitles.entity.turtle.hurt_baby": "Cria de tortuga ferida", "subtitles.entity.turtle.lay_egg": "Tor<PERSON>ga pon ou", "subtitles.entity.turtle.shamble": "<PERSON><PERSON><PERSON> repta", "subtitles.entity.turtle.shamble_baby": "Cria de tortuga repta", "subtitles.entity.turtle.swim": "<PERSON><PERSON><PERSON> neda", "subtitles.entity.vex.ambient": "Esperit turmenta", "subtitles.entity.vex.charge": "Esperit xiscla", "subtitles.entity.vex.death": "<PERSON><PERSON>it mor", "subtitles.entity.vex.hurt": "Esperit é<PERSON> ferit", "subtitles.entity.villager.ambient": "Vilatà remuga", "subtitles.entity.villager.celebrate": "Vilatà celebra", "subtitles.entity.villager.death": "Vilatà mor", "subtitles.entity.villager.hurt": "Vilatà és ferit", "subtitles.entity.villager.no": "Vilatà desconforme", "subtitles.entity.villager.trade": "Vilatà comercia", "subtitles.entity.villager.work_armorer": "Un armer treballa", "subtitles.entity.villager.work_butcher": "Un carnisser treballa", "subtitles.entity.villager.work_cartographer": "Un cartògraf treballa", "subtitles.entity.villager.work_cleric": "Un clergue fa missa", "subtitles.entity.villager.work_farmer": "Un granger treballa", "subtitles.entity.villager.work_fisherman": "Un pescador treballa", "subtitles.entity.villager.work_fletcher": "Un fletxer treballa", "subtitles.entity.villager.work_leatherworker": "Un pelleter treballa", "subtitles.entity.villager.work_librarian": "Un bibliotecari treballa", "subtitles.entity.villager.work_mason": "Un obrer treballa", "subtitles.entity.villager.work_shepherd": "Un pastor treballa", "subtitles.entity.villager.work_toolsmith": "Un ferrer treballa", "subtitles.entity.villager.work_weaponsmith": "Un esmolet treballa", "subtitles.entity.villager.yes": "Vilatà assenteix", "subtitles.entity.vindicator.ambient": "Vindicador murmura", "subtitles.entity.vindicator.celebrate": "Vindicador celebra", "subtitles.entity.vindicator.death": "Vindicador mor", "subtitles.entity.vindicator.hurt": "Vindicador és ferit", "subtitles.entity.wandering_trader.ambient": "Comerciant murmura", "subtitles.entity.wandering_trader.death": "Comerciant mor", "subtitles.entity.wandering_trader.disappeared": "Comerciant desapareix", "subtitles.entity.wandering_trader.drink_milk": "Comerciant beu llet", "subtitles.entity.wandering_trader.drink_potion": "Comerciant beu una poció", "subtitles.entity.wandering_trader.hurt": "Comerciant és ferit", "subtitles.entity.wandering_trader.no": "Comerciant rebutja", "subtitles.entity.wandering_trader.reappeared": "Comerciant apareix", "subtitles.entity.wandering_trader.trade": "Comerciant comercia", "subtitles.entity.wandering_trader.yes": "Comerciant accepta", "subtitles.entity.warden.agitated": "Vigilant remuga furiosament", "subtitles.entity.warden.ambient": "Vigilant es lamenta", "subtitles.entity.warden.angry": "Vigilant s'enfurisma", "subtitles.entity.warden.attack_impact": "Vigilant ataca", "subtitles.entity.warden.death": "Vigilant mor", "subtitles.entity.warden.dig": "Vigilant excava", "subtitles.entity.warden.emerge": "Vigilant sorgeix", "subtitles.entity.warden.heartbeat": "Cor d'un vigilant batega", "subtitles.entity.warden.hurt": "Vigilant <PERSON><PERSON> ferit", "subtitles.entity.warden.listening": "Vigilant se n'adona", "subtitles.entity.warden.listening_angry": "Vigilant se n'adona furiosament", "subtitles.entity.warden.nearby_close": "Vigilant s'acos<PERSON>", "subtitles.entity.warden.nearby_closer": "Vigilant avança", "subtitles.entity.warden.nearby_closest": "Vigilant s'apropa", "subtitles.entity.warden.roar": "Vigilant rugeix", "subtitles.entity.warden.sniff": "Vigilant ensuma", "subtitles.entity.warden.sonic_boom": "Vigilant brama", "subtitles.entity.warden.sonic_charge": "Vigilant envesteix", "subtitles.entity.warden.step": "Vigilant camina", "subtitles.entity.warden.tendril_clicks": "Circells d'un vigilant espeteguen", "subtitles.entity.wind_charge.throw": "Càrrega de vent vola", "subtitles.entity.wind_charge.wind_burst": "Càrrega de vent esclata", "subtitles.entity.witch.ambient": "B<PERSON>ixa riu", "subtitles.entity.witch.celebrate": "B<PERSON>ixa celebra", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON><PERSON> mor", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON><PERSON> beu", "subtitles.entity.witch.hurt": "Bruixa és ferida", "subtitles.entity.witch.throw": "Bruixa llença pocions", "subtitles.entity.wither.ambient": "Wither s'enfada", "subtitles.entity.wither.death": "Wither mor", "subtitles.entity.wither.hurt": "<PERSON>er <PERSON> ferit", "subtitles.entity.wither.shoot": "Wither ataca", "subtitles.entity.wither.spawn": "El Wither és alliberat", "subtitles.entity.wither_skeleton.ambient": "<PERSON><PERSON><PERSON> wither trontolla", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON><PERSON> wither mor", "subtitles.entity.wither_skeleton.hurt": "<PERSON><PERSON><PERSON> wither és ferit", "subtitles.entity.wolf.ambient": "Llop esbufega", "subtitles.entity.wolf.bark": "<PERSON><PERSON><PERSON><PERSON> de llop", "subtitles.entity.wolf.death": "<PERSON><PERSON> mor", "subtitles.entity.wolf.growl": "<PERSON><PERSON>", "subtitles.entity.wolf.hurt": "Llop és ferit", "subtitles.entity.wolf.pant": "Calçons de llop", "subtitles.entity.wolf.shake": "Llop es remena", "subtitles.entity.wolf.whine": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> rugeix furiosament", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.zoglin.hurt": "Zoglin és ferit", "subtitles.entity.zoglin.step": "Zoglin camina", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON> remuga", "subtitles.entity.zombie.attack_wooden_door": "Porta tremola", "subtitles.entity.zombie.break_wooden_door": "Porta es trenca", "subtitles.entity.zombie.converted_to_drowned": "Zombi es converteix en ofegat", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON> mor", "subtitles.entity.zombie.destroy_egg": "Ou de tortuga és aixafat", "subtitles.entity.zombie.hurt": "Zombi és ferit", "subtitles.entity.zombie.infect": "<PERSON><PERSON><PERSON> infecta", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON>ll zombi plora", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON> zombi mor", "subtitles.entity.zombie_horse.hurt": "Cavall zombi és ferit", "subtitles.entity.zombie_villager.ambient": "Vilatà zombi remuga", "subtitles.entity.zombie_villager.converted": "Vilatà zombi vocifera", "subtitles.entity.zombie_villager.cure": "Vilatà zombi sospira", "subtitles.entity.zombie_villager.death": "Vilatà zombi mor", "subtitles.entity.zombie_villager.hurt": "Vilatà zombi és ferit", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON> zombificat gruny", "subtitles.entity.zombified_piglin.angry": "<PERSON>lin zombificat gruny furiosament", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON> zombificat mor", "subtitles.entity.zombified_piglin.hurt": "Piglin zombificat és ferit", "subtitles.event.mob_effect.bad_omen": "Un presagi s'ha establit", "subtitles.event.mob_effect.raid_omen": "S'acosta un setge", "subtitles.event.mob_effect.trial_omen": "Un repte ominós s'acosta", "subtitles.event.raid.horn": "<PERSON> sinistre", "subtitles.item.armor.equip": "Armadura equipada", "subtitles.item.armor.equip_chain": "Armadura de cota de malla dringa", "subtitles.item.armor.equip_diamond": "Armadura de diamant equipada", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON>", "subtitles.item.armor.equip_gold": "Armadura d'or equipada", "subtitles.item.armor.equip_iron": "Armadura de ferro és equipada", "subtitles.item.armor.equip_leather": "Armadura de cuir cruix", "subtitles.item.armor.equip_netherite": "Armadura de netherita és equipada", "subtitles.item.armor.equip_turtle": "Closca de tortuga equipada", "subtitles.item.armor.equip_wolf": "Corretja d'armadura per a llops", "subtitles.item.armor.unequip_wolf": "Armadura per a llop es desequipa", "subtitles.item.axe.scrape": "Destral escorça", "subtitles.item.axe.strip": "Destral escorça", "subtitles.item.axe.wax_off": "Bloc desencerat", "subtitles.item.bone_meal.use": "<PERSON><PERSON> d'ossos és usada", "subtitles.item.book.page_turn": "Cruixit de pàgina", "subtitles.item.book.put": "S'ha col·locat un llibre", "subtitles.item.bottle.empty": "Ampolla es buida", "subtitles.item.bottle.fill": "S'o<PERSON> una ampolla", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "Raspallant grava", "subtitles.item.brush.brushing.gravel.complete": "Raspallat de grava Completat", "subtitles.item.brush.brushing.sand": "Ra<PERSON><PERSON><PERSON>rra", "subtitles.item.brush.brushing.sand.complete": "Raspallat de sorra completat", "subtitles.item.bucket.empty": "Galleda buidada", "subtitles.item.bucket.fill": "Galleda emplenada", "subtitles.item.bucket.fill_axolotl": "<PERSON><PERSON><PERSON><PERSON> recollit", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON><PERSON> capturat", "subtitles.item.bucket.fill_tadpole": "Capg<PERSON><PERSON><PERSON> capturat", "subtitles.item.bundle.drop_contents": "Sac es buida", "subtitles.item.bundle.insert": "Ítem és empaquetat", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON><PERSON> ple", "subtitles.item.bundle.remove_one": "Ítem és desempaquetat", "subtitles.item.chorus_fruit.teleport": "Jugador es teletransporta", "subtitles.item.crop.plant": "Cultiu plantat", "subtitles.item.crossbow.charge": "Ballesta carregant", "subtitles.item.crossbow.hit": "Fletxa impacta", "subtitles.item.crossbow.load": "Ballesta carregada", "subtitles.item.crossbow.shoot": "<PERSON>ret de ballesta", "subtitles.item.dye.use": "Tint tenyeix", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "Una bola de foc passa volant", "subtitles.item.flintandsteel.use": "Encene<PERSON> en<PERSON>", "subtitles.item.glow_ink_sac.use": "Sac de tinta lluent taca", "subtitles.item.goat_horn.play": "<PERSON>ya de cabra sona", "subtitles.item.hoe.till": "Aixada llaura", "subtitles.item.honey_bottle.drink": "Empassant", "subtitles.item.honeycomb.wax_on": "Bloc encerat", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "Sac de tinta taca", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "Brúixola és orientada en una fita", "subtitles.item.mace.smash_air": "<PERSON><PERSON>", "subtitles.item.mace.smash_ground": "<PERSON><PERSON>", "subtitles.item.nether_wart.plant": "Berruga plantada", "subtitles.item.ominous_bottle.dispose": "Ampolla es trenca", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "<PERSON><PERSON><PERSON> tallen", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "Bloqueig amb escut", "subtitles.item.shovel.flatten": "Pala aplana", "subtitles.item.spyglass.stop_using": "Ullera és retreta", "subtitles.item.spyglass.use": "Ullera és estesa", "subtitles.item.totem.use": "Tòtem activat", "subtitles.item.trident.hit": "Punyalada de trident", "subtitles.item.trident.hit_ground": "Trident vibra", "subtitles.item.trident.return": "Trident torna", "subtitles.item.trident.riptide": "Trident brunzeix", "subtitles.item.trident.throw": "<PERSON><PERSON>", "subtitles.item.trident.thunder": "Trident trona", "subtitles.item.wolf_armor.break": "Armadura de llop es trenca", "subtitles.item.wolf_armor.crack": "Armadura de llop s'esquerda", "subtitles.item.wolf_armor.damage": "<PERSON><PERSON><PERSON> de llop rep danys", "subtitles.item.wolf_armor.repair": "Armadura de llop es repara", "subtitles.particle.soul_escape": "Ànima s'escapa", "subtitles.ui.cartography_table.take_result": "Mapa és dibuixat", "subtitles.ui.hud.bubble_pop": "S'acaba l'oxigen", "subtitles.ui.loom.take_result": "Teler és usat", "subtitles.ui.stonecutter.take_result": "Pedra és tallada", "subtitles.weather.rain": "<PERSON><PERSON><PERSON>", "symlink_warning.message": "Carregar mons des de carpetes amb enllaços simbòlics pot ser insegur si no saps exactament què estàs fent. Si us plau, visita %s per aprendre'n més.", "symlink_warning.message.pack": "<PERSON>egar paquets amb enllaços simbòlics pot ser insegur si no saps exactament què estàs fent. Si us plau, visita %s per aprendre'n més.", "symlink_warning.message.world": "Carregar mons des de carpetes amb enllaços simbòlics pot ser insegur si no saps exactament què estàs fent. Si us plau, visita %s per aprendre'n més.", "symlink_warning.more_info": "Més informació", "symlink_warning.title": "La carpeta Món conté enllaços simbòlics", "symlink_warning.title.pack": "Els paquets afegits contenen enllaços simbòlics", "symlink_warning.title.world": "La carpeta del món conté enllaços simbòlics", "team.collision.always": "Sempre", "team.collision.never": "<PERSON>", "team.collision.pushOtherTeams": "Colisió entre jugadors d'equips diferents", "team.collision.pushOwnTeam": "Colisió entre jugadors del mateix equip", "team.notFound": "Equip desconegut: %s", "team.visibility.always": "Sempre", "team.visibility.hideForOtherTeams": "Ocultar per a altres equips", "team.visibility.hideForOwnTeam": "Ocultar pel seu equip", "team.visibility.never": "<PERSON>", "telemetry.event.advancement_made.description": "Entendre el context desparés de fer un avenç, ens pot ajudar a millorar i entendre la progressió del joc.", "telemetry.event.advancement_made.title": "Avenç fet", "telemetry.event.game_load_times.description": "Aquest esdeveniment ens pot ajudar a esbrinar a on millores del rendiment d'inici es necessiten, mesurant el temps d'execució de les diferents fases d'inici.", "telemetry.event.game_load_times.title": "Temps de càrrega del joc", "telemetry.event.optional": "%s (Opcional)", "telemetry.event.optional.disabled": "%s (Opcional) - Desactivat", "telemetry.event.performance_metrics.description": "Conèixer el rendiment general de Minecraft ens ajuda a sintonitzar i optimitzar el joc per a una àmplia gamma de maquinari i sistemes operatius. \nLa versió del joc s'inclou per ajudar-nos a comparar el perfil de rendiment per les noves versions de Minecraft.", "telemetry.event.performance_metrics.title": "Dades sobre el rendiment", "telemetry.event.required": "%s (Requerit)", "telemetry.event.world_load_times.description": "Saber quant temps es tarda en unir-se a un món és important per a nosaltres, així com veure si canvia en el futur. Per exemple, volem veure quin impacte afegir noves característiques o fer grans canvis tècnics té sobre el temps de càrrega.", "telemetry.event.world_load_times.title": "Temps de càrrega del món", "telemetry.event.world_loaded.description": "Conèixer com juguen a Minecraft els jugadors (per exemple el mode de joc, si el client o servidor ha estat modificat, i la versió de joc) ens permet concentrar-nos en fer actualitzacions que millorin les àrees de major importància pels jugadors. \nLa clau \"Món carregat\" fa parella amb la clau \"Món descarregat\" per calcular quant de temps ha durat la sessió de joc.", "telemetry.event.world_loaded.title": "Món carregat", "telemetry.event.world_unloaded.description": "Aquesta clau s'aparella amb \"Món carregat\" per a calcular quant de temps ha durat la sessió.\nLa durada (en segons i cicles) es mesura quan ha acabat una sessió del món (tornant a la pantalla de títol, desconnectant-se d'un servidor).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "Temps de joc (Ticks)", "telemetry.property.advancement_id.title": "ID de l'avenç", "telemetry.property.client_id.title": "ID del client", "telemetry.property.client_modded.title": "Client modificat", "telemetry.property.dedicated_memory_kb.title": "Memòria dedicada (kB)", "telemetry.property.event_timestamp_utc.title": "Hora d'enregistrament de la clau (UTC)", "telemetry.property.frame_rate_samples.title": "Mostres de fotogrames per segon (FPS)", "telemetry.property.game_mode.title": "Mode de joc", "telemetry.property.game_version.title": "Versió del joc", "telemetry.property.launcher_name.title": "Nom del llançador", "telemetry.property.load_time_bootstrap_ms.title": "Temps de càrrega (Mil·lisegons)", "telemetry.property.load_time_loading_overlay_ms.title": "Temps en pantalla de càrrega (Mil·lisegons)", "telemetry.property.load_time_pre_window_ms.title": "Temps abans que la finestra s'obri (Mil·lisegons)", "telemetry.property.load_time_total_time_ms.title": "Temps total de càrrega (Mil·lisegons)", "telemetry.property.minecraft_session_id.title": "ID de la sessió de Minecraft", "telemetry.property.new_world.title": "Nou món", "telemetry.property.number_of_samples.title": "Nombre de mostres", "telemetry.property.operating_system.title": "Sistema operatiu", "telemetry.property.opt_in.title": "Optat", "telemetry.property.platform.title": "Plataforma", "telemetry.property.realms_map_content.title": "Contingut del mapa de Realms (Nom del minijoc)", "telemetry.property.render_distance.title": "Distància de renderitzat", "telemetry.property.render_time_samples.title": "Mostres del temps de renderització", "telemetry.property.seconds_since_load.title": "Temps des de la càrrega (segons)", "telemetry.property.server_modded.title": "Servidor modificat", "telemetry.property.server_type.title": "<PERSON><PERSON><PERSON> de servidor", "telemetry.property.ticks_since_load.title": "Temps des de la càrrega (cicles)", "telemetry.property.used_memory_samples.title": "RAM usada", "telemetry.property.user_id.title": "ID d'usuari", "telemetry.property.world_load_time_ms.title": "Temps de càrrega del món (mil·lisegons)", "telemetry.property.world_session_id.title": "ID de la sessió del món", "telemetry_info.button.give_feedback": "Deixar un comentari", "telemetry_info.button.privacy_statement": "Declaració de privacitat", "telemetry_info.button.show_data": "<PERSON>r les meves dades", "telemetry_info.opt_in.description": "Jo consento a enviar data de telemetria opcional", "telemetry_info.property_title": "Dad<PERSON> incloses", "telemetry_info.screen.description": "Recollir aquestes dades ens ajuda a millorar el Minecraft guiant-nos cap a coses que són rellevants per als nostres jugadors.\nTambé pots deixar un comentari amb les teves opinions per ajudar-nos a continuar millorant el joc.", "telemetry_info.screen.title": "Recol·lecció de dades telemètriques", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Es requereix %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entitat hauria d'agafar %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Error per crear entitat %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Molt de %s blocs", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Missatge:", "test_block.mode.accept": "Acceptar", "test_block.mode.fail": "<PERSON>ar", "test_block.mode.log": "Registre", "test_block.mode.start": "<PERSON><PERSON><PERSON><PERSON>", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Mode Registrar - Registra un missatge", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reiniciar i Carregar", "test_instance.action.run": "Carregar i Obrir", "test_instance.action.save": "Guardar Estructura", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Failed: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Invalid test ID", "test_instance.description.no_test": "No such test", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Entitats:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[invalid]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotació:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "S'ha detectat un sistema operatiu de 32 bits: això et pot impedir jugar en el futur, ja que caldrà tenir un sistema de 64 bits!", "title.32bit.deprecation.realms": "Minecraft aviat necessitarà tenir un sistema operatiu de 64 bits per a funcionar, el qual t'impedirà jugar o utilitzar Realms en aquest dispositiu. Hauràs de cancel·lar qualsevol subscripció a Realms manualment.", "title.32bit.deprecation.realms.check": "No tornis a mostrar aquest missatge", "title.32bit.deprecation.realms.header": "Sistema de 32 bits detectat", "title.credits": "Drets d'autor Mojang AB. No distribuir!", "title.multiplayer.disabled": "El mode multijugador està deshabilitat. Si us plau, comprova la configuració del teu compte de Microsoft.", "title.multiplayer.disabled.banned.name": "Has de canviar el teu nom d'usuari abans de jugar en línia", "title.multiplayer.disabled.banned.permanent": "El joc en línia està suspès permanentment per al teu compte", "title.multiplayer.disabled.banned.temporary": "El joc en línia està suspès temporalment per al teu compte", "title.multiplayer.lan": "Multijugador (local)", "title.multiplayer.other": "Multijugador (servidor de tercers)", "title.multiplayer.realms": "Multijugador (Realms)", "title.singleplayer": "Individual", "translation.test.args": "%s %s", "translation.test.complex": "Prefix, %s%2$s de nou %s i %1$s finalment %s i també %1$s de nou!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "Hola %", "translation.test.invalid2": "hola %s", "translation.test.none": "Hola, món!", "translation.test.world": "món", "trim_material.minecraft.amethyst": "Material: Ametista", "trim_material.minecraft.copper": "Material: Coure", "trim_material.minecraft.diamond": "Material: <PERSON><PERSON><PERSON>", "trim_material.minecraft.emerald": "Material: <PERSON><PERSON><PERSON>", "trim_material.minecraft.gold": "Material: Or", "trim_material.minecraft.iron": "Material: <PERSON><PERSON>", "trim_material.minecraft.lapis": "Material: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.netherite": "Material: <PERSON><PERSON><PERSON>", "trim_material.minecraft.quartz": "Material: Quars", "trim_material.minecraft.redstone": "Material: Redstone", "trim_material.minecraft.resin": "Material de resina", "trim_pattern.minecraft.bolt": "<PERSON><PERSON><PERSON>'armad<PERSON>: <PERSON><PERSON><PERSON>", "trim_pattern.minecraft.coast": "<PERSON><PERSON><PERSON>armad<PERSON>: Costa", "trim_pattern.minecraft.dune": "<PERSON><PERSON><PERSON>'armad<PERSON>: <PERSON><PERSON>", "trim_pattern.minecraft.eye": "<PERSON><PERSON><PERSON>arm<PERSON>: <PERSON><PERSON>", "trim_pattern.minecraft.flow": "<PERSON><PERSON><PERSON>'armad<PERSON>: Espiral", "trim_pattern.minecraft.host": "<PERSON><PERSON><PERSON> d'armadura: <PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.raiser": "<PERSON><PERSON>ó d'armadura: <PERSON>eva<PERSON><PERSON>", "trim_pattern.minecraft.rib": "<PERSON><PERSON><PERSON>'armadura: <PERSON><PERSON><PERSON>", "trim_pattern.minecraft.sentry": "<PERSON><PERSON><PERSON>'armadura: Sentinella", "trim_pattern.minecraft.shaper": "<PERSON><PERSON><PERSON> d'armadura: <PERSON><PERSON><PERSON>", "trim_pattern.minecraft.silence": "<PERSON><PERSON><PERSON> d'armadura: <PERSON><PERSON><PERSON>", "trim_pattern.minecraft.snout": "<PERSON><PERSON><PERSON> d'armadura: <PERSON><PERSON>", "trim_pattern.minecraft.spire": "<PERSON><PERSON><PERSON> d'armadura: <PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.tide": "<PERSON><PERSON><PERSON>'armad<PERSON>: <PERSON><PERSON>", "trim_pattern.minecraft.vex": "<PERSON><PERSON><PERSON>'armad<PERSON>: <PERSON><PERSON><PERSON>", "trim_pattern.minecraft.ward": "<PERSON><PERSON><PERSON>: Vigilant", "trim_pattern.minecraft.wayfinder": "<PERSON><PERSON><PERSON> d'armadura: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.wild": "<PERSON><PERSON><PERSON>'arm<PERSON>: <PERSON><PERSON><PERSON>", "tutorial.bundleInsert.description": "Fes clic dret per afegir ítems", "tutorial.bundleInsert.title": "Utilitza un sarró", "tutorial.craft_planks.description": "El receptari et pot ajudar", "tutorial.craft_planks.title": "Elabora fusta", "tutorial.find_tree.description": "Colpeja'l per obtenir fusta", "tutorial.find_tree.title": "Troba un arbre", "tutorial.look.description": "Fes servir el ratolí per girar", "tutorial.look.title": "Mira al teu voltant", "tutorial.move.description": "Salta amb %s", "tutorial.move.title": "Mou-te amb %s, %s, %s i %s", "tutorial.open_inventory.description": "Prem %s", "tutorial.open_inventory.title": "Obre l'inventari", "tutorial.punch_tree.description": "Mantingues %s premut", "tutorial.punch_tree.title": "Destrueix l'arbre", "tutorial.socialInteractions.description": "Prem %s per obrir", "tutorial.socialInteractions.title": "Interaccions socials", "upgrade.minecraft.netherite_upgrade": "<PERSON><PERSON> de netherita"}