{"accessibility.onboarding.accessibility.button": "Configuració d'accessibilitat...", "accessibility.onboarding.screen.narrator": "Prem \"Intro\" per a activar el narrador", "accessibility.onboarding.screen.title": "Benvingut al Minecraft!\n\nDesitges activar el narrador o vore els ajustos d'accessibilitat?", "addServer.add": "Fet", "addServer.enterIp": "Adreça del servidor", "addServer.enterName": "Nom del servidor", "addServer.resourcePack": "Paquets de recursos", "addServer.resourcePack.disabled": "no", "addServer.resourcePack.enabled": "sí", "addServer.resourcePack.prompt": "Pregunta", "addServer.title": "Modifica la informació del servidor", "advMode.command": "Consola de comandament", "advMode.mode": "Mode", "advMode.mode.auto": "Repeteix", "advMode.mode.autoexec.bat": "Sempre actiu", "advMode.mode.conditional": "Condicional", "advMode.mode.redstone": "Impuls", "advMode.mode.redstoneTriggered": "Necessita redstone", "advMode.mode.sequence": "Cadena", "advMode.mode.unconditional": "Incondicional", "advMode.notAllowed": "S'ha de ser operador i estar en mode creatiu", "advMode.notEnabled": "El bloc de comandaments no està disponible en aquest servidor", "advMode.previousOutput": "Eixida anterior", "advMode.setCommand": "Canvia el comandament del bloc", "advMode.setCommand.success": "Comandament establit: %s", "advMode.trackOutput": "Mira el resultat", "advMode.triggering": "Desencadenant", "advMode.type": "<PERSON><PERSON><PERSON>", "advancement.advancementNotFound": "Avanç desconegut: %s", "advancements.adventure.adventuring_time.description": "Descobrix tots els biomes", "advancements.adventure.adventuring_time.title": "Hora d'aventures", "advancements.adventure.arbalistic.description": "Mata cinc criatures diferents amb un sol tir de ballesta", "advancements.adventure.arbalistic.title": "Assassinat quíntuple", "advancements.adventure.avoid_vibration.description": "Camina sigilosament prop d'un sensor de sculk o d'un castellà per a evitar que et detecte", "advancements.adventure.avoid_vibration.title": "Sigilós com un gat", "advancements.adventure.blowback.description": "Mata una brisa retornant-li una càrrega de vent", "advancements.adventure.blowback.title": "Represàlia", "advancements.adventure.brush_armadillo.description": "Obtín plaques d'armadillo utilitzant un raspall", "advancements.adventure.brush_armadillo.title": "Implacable?", "advancements.adventure.bullseye.description": "Encerta la diana des d'almenys 30 metres de distància", "advancements.adventure.bullseye.title": "En el blanc", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Fes un gerro amb 4 fragments de gerro", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Restaurac<PERSON><PERSON> ca<PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "Sigues testimoni d'un fabricador fabricant un fabricador", "advancements.adventure.crafters_crafting_crafters.title": "Fabricadors fabricant fabricadors", "advancements.adventure.fall_from_world_height.description": "Cau-te des del punt més alt del món fins al punt més baix i sobreviu", "advancements.adventure.fall_from_world_height.title": "Coves i penya-segats", "advancements.adventure.heart_transplanter.description": "Col·loca un cor de crepitant, perfectament alineat, entre dos troncs de roure tètric", "advancements.adventure.heart_transplanter.title": "Transplantador de cors", "advancements.adventure.hero_of_the_village.description": "Protegix amb èxit una vila d'un setge", "advancements.adventure.hero_of_the_village.title": "Heroi de la vila", "advancements.adventure.honey_block_slide.description": "Salta sobre un bloc de mel per a esmorteir la teua caiguda", "advancements.adventure.honey_block_slide.title": "Situació embafosa", "advancements.adventure.kill_a_mob.description": "Mata qualsevol monstre", "advancements.adventure.kill_a_mob.title": "Caçamonstres", "advancements.adventure.kill_all_mobs.description": "Mata un monstre de cada tipus", "advancements.adventure.kill_all_mobs.title": "A per tots!", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "<PERSON> un ésser prop d'un catalitzador de sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "S'escampa", "advancements.adventure.lighten_up.description": "Raspa una làmpada de coure amb una destral perquè faça més llum", "advancements.adventure.lighten_up.title": "Il·luminat", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Protegix un vilatà d'una electrocució sense començar un incendi", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "<PERSON><PERSON><PERSON><PERSON> elèct<PERSON>", "advancements.adventure.minecraft_trials_edition.description": "Posa un peu en una masmorra de reptes", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: edici<PERSON> repte(s)", "advancements.adventure.ol_betsy.description": "Dispara una ballesta", "advancements.adventure.ol_betsy.title": "La vella Betsy", "advancements.adventure.overoverkill.description": "Infligix 50 de dany d'un sol colp utilitzant la maça", "advancements.adventure.overoverkill.title": "Re-rematar", "advancements.adventure.play_jukebox_in_meadows.description": "Dona vida als prats amb el so de la música d'un tocadiscos", "advancements.adventure.play_jukebox_in_meadows.title": "Al ritme del compàs", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Llig la potència del senyal d'una llibreria cisellada amb un comparador", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "El poder de la lectura", "advancements.adventure.revaulting.description": "Obri una caixa forta ominosa amb una clau de reptes ominosa", "advancements.adventure.revaulting.title": "Manyà", "advancements.adventure.root.description": "Aventura, exploració i combat", "advancements.adventure.root.title": "Aventura", "advancements.adventure.salvage_sherd.description": "Raspalla un bloc sospitós per obtenir un fragment de gerro", "advancements.adventure.salvage_sherd.title": "Respectant els antics", "advancements.adventure.shoot_arrow.description": "Dispara a alguna cosa amb una fletxa", "advancements.adventure.shoot_arrow.title": "Bona punteria!", "advancements.adventure.sleep_in_bed.description": "Dorm en un llit per a canviar el teu punt d'aparició", "advancements.adventure.sleep_in_bed.title": "Bona nit!", "advancements.adventure.sniper_duel.description": "Mata un esquelet des de més de 50 metres (blocs) de distància", "advancements.adventure.sniper_duel.title": "Duel d'arquers", "advancements.adventure.spyglass_at_dragon.description": "Mira al Drac de la Fi a través d'una ullera", "advancements.adventure.spyglass_at_dragon.title": "És un avió?", "advancements.adventure.spyglass_at_ghast.description": "Mira a un ghast a través d'una ullera", "advancements.adventure.spyglass_at_ghast.title": "És un globus?", "advancements.adventure.spyglass_at_parrot.description": "Mira a un lloro a través d'una ullera", "advancements.adventure.spyglass_at_parrot.title": "És un pardal?", "advancements.adventure.summon_iron_golem.description": "Genera un guardià de ferro per protegir la vila", "advancements.adventure.summon_iron_golem.title": "El guardaespatlles", "advancements.adventure.throw_trident.description": "Llança un trident a qualsevol cosa. \nNota: llançar la teua única arma no és bona idea.", "advancements.adventure.throw_trident.title": "Broma pesada", "advancements.adventure.totem_of_undying.description": "Utilitza un tòtem de la immortalitat per burlar la mort", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.trade.description": "Comercia amb èxit amb un vilatà", "advancements.adventure.trade.title": "Això ho pague jo!", "advancements.adventure.trade_at_world_height.description": "Comercia amb un vilatà al punt més alt del món", "advancements.adventure.trade_at_world_height.title": "Comerciant estelar", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Aplica els següents motles de ferreria almenys una vegada: c<PERSON><PERSON><PERSON>, morro, os, guard<PERSON>à, silenci, esperit, marea, explorador", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON> amb estil", "advancements.adventure.trim_with_any_armor_pattern.description": "Fes una armadura amb un disseny en una ferreria", "advancements.adventure.trim_with_any_armor_pattern.title": "Creant un nou aspecte", "advancements.adventure.two_birds_one_arrow.description": "<PERSON> dos ànimes amb una sola fletxa perforant", "advancements.adventure.two_birds_one_arrow.title": "Dos pardals d'un tir", "advancements.adventure.under_lock_and_key.description": "Utilitza una clau de reptes en una caixa forta", "advancements.adventure.under_lock_and_key.title": "Tancat a pany i clau", "advancements.adventure.use_lodestone.description": "<PERSON><PERSON>ta una br<PERSON>ixola", "advancements.adventure.use_lodestone.title": "Au a casa! Per on era?", "advancements.adventure.very_very_frightening.description": "Colpeja un vilatà amb un raig", "advancements.adventure.very_very_frightening.title": "Molt molt aterrador", "advancements.adventure.voluntary_exile.description": "Mata un capità dels assaltants.\nPot ser hauries d'allunyar-te un temps de les viles...", "advancements.adventure.voluntary_exile.title": "<PERSON>ili voluntari", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Camina sobre neu pols... sense afonar-te en ella", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Lleuger com un conill", "advancements.adventure.who_needs_rockets.description": "Utilitza una càrrega de vent per a propulsar-te 7 blocs cap amunt", "advancements.adventure.who_needs_rockets.title": "Qui necessita coets?", "advancements.adventure.whos_the_pillager_now.description": "Dona-li a un bandit de la seua pròpia medicina", "advancements.adventure.whos_the_pillager_now.title": "Qui és el bandit ara?", "advancements.empty": "No pareix que hi haja res per ací...", "advancements.end.dragon_breath.description": "Recull l'alé de drac en una botella de vidre", "advancements.end.dragon_breath.title": "Llava't les dents!", "advancements.end.dragon_egg.description": "Aconseguix l'ou del drac", "advancements.end.dragon_egg.title": "La nova generació", "advancements.end.elytra.description": "Troba uns èlitres", "advancements.end.elytra.title": "El límit és el cel", "advancements.end.enter_end_gateway.description": "<PERSON>ig de l'illa", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON> remota", "advancements.end.find_end_city.description": "Ves-hi. <PERSON>u<PERSON> és el pitjor que podria passar?", "advancements.end.find_end_city.title": "La ciutat al final del joc", "advancements.end.kill_dragon.description": "Bona sort", "advancements.end.kill_dragon.title": "Allibera l'End", "advancements.end.levitate.description": "Levita 50 blocs a causa de l'atac d'un shulker", "advancements.end.levitate.title": "Quines vistes!", "advancements.end.respawn_dragon.description": "Torna a generar el drac d'Ender", "advancements.end.respawn_dragon.title": "L'End... de nou...", "advancements.end.root.description": "O el principi?", "advancements.end.root.title": "L'End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Fes que un al·lai amolle un pastís sobre un bloc musical", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "En el dia d'avui", "advancements.husbandry.allay_deliver_item_to_player.description": "Fes que un al·lai et porte objectes", "advancements.husbandry.allay_deliver_item_to_player.title": "Hi ha un amic en mi", "advancements.husbandry.axolotl_in_a_bucket.description": "Atrapa un axolot amb un poal", "advancements.husbandry.axolotl_in_a_bucket.title": "El depredador més bonic", "advancements.husbandry.balanced_diet.description": "Menja tot allò comestible, encara que siga perjudicial per a la teua salut", "advancements.husbandry.balanced_diet.title": "Dieta mediterrània", "advancements.husbandry.breed_all_animals.description": "<PERSON><PERSON><PERSON> tots els animals!", "advancements.husbandry.breed_all_animals.title": "De dos en dos", "advancements.husbandry.breed_an_animal.description": "A<PERSON><PERSON> dos animals", "advancements.husbandry.breed_an_animal.title": "Fan bona parella", "advancements.husbandry.complete_catalogue.description": "Domestica tots els tipus de gat!", "advancements.husbandry.complete_catalogue.title": "Un \"gatàleg\" complet", "advancements.husbandry.feed_snifflet.description": "Alimenta una cria de rastrejador", "advancements.husbandry.feed_snifflet.title": "Rastrejadorets", "advancements.husbandry.fishy_business.description": "Pesca un peix", "advancements.husbandry.fishy_business.title": "Cosa de peixos", "advancements.husbandry.froglights.description": "Tin totes les granollums al teu inventari", "advancements.husbandry.froglights.title": "Combinem els nostres poders!", "advancements.husbandry.kill_axolotl_target.description": "Fes equip amb un axolot i guanya una baralla", "advancements.husbandry.kill_axolotl_target.title": "El poder curatiu de l'amistat!", "advancements.husbandry.leash_all_frog_variants.description": "Agafa tots els tipus de granotes amb un llaç", "advancements.husbandry.leash_all_frog_variants.title": "Plaga de granotes", "advancements.husbandry.make_a_sign_glow.description": "Fes que el text de qualsevol cartell brille", "advancements.husbandry.make_a_sign_glow.title": "Brilla i admira!", "advancements.husbandry.netherite_hoe.description": "Fes servir un lingot de netherita per millorar una aixada, i després pensa si estàs bé del cap", "advancements.husbandry.netherite_hoe.title": "No tens trellat o què?", "advancements.husbandry.obtain_sniffer_egg.description": "Obt<PERSON> un ou de rastrejador", "advancements.husbandry.obtain_sniffer_egg.title": "Això fa bona olor", "advancements.husbandry.place_dried_ghast_in_water.description": "Col·loca un bloc de ghast sec dins de l'aigua", "advancements.husbandry.place_dried_ghast_in_water.title": "<PERSON><PERSON><PERSON>'t!", "advancements.husbandry.plant_any_sniffer_seed.description": "Planta qualsevol llavor de rastrejador", "advancements.husbandry.plant_any_sniffer_seed.title": "Sembrant el passat", "advancements.husbandry.plant_seed.description": "Planta una llavor i mira com creix", "advancements.husbandry.plant_seed.title": "Creixen tan de pressa...", "advancements.husbandry.remove_wolf_armor.description": "Util<PERSON><PERSON> unes tisores d'esquilar per a llevar l'armadura a un llop", "advancements.husbandry.remove_wolf_armor.title": "Venen retallades!", "advancements.husbandry.repair_wolf_armor.description": "Repara una armadura de llop utilitzant plaques d'armadillo", "advancements.husbandry.repair_wolf_armor.title": "Com nou", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Puja a una barca i flota amb una cabra", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Cabra marinera!", "advancements.husbandry.root.description": "El món està ple d'amics i menjar", "advancements.husbandry.root.title": "Me'n vaig a l'hort!", "advancements.husbandry.safely_harvest_honey.description": "Utilitza una foguera per a recollir mel d'un buc amb una botella sense alterar a les abelles", "advancements.husbandry.safely_harvest_honey.title": "Apicultor", "advancements.husbandry.silk_touch_nest.description": "Mou un rusc amb tres abelles dins utilitzant el toc suau", "advancements.husbandry.silk_touch_nest.title": "Mudança abellil", "advancements.husbandry.tactical_fishing.description": "Pesca un peix... sense canya de pescar!", "advancements.husbandry.tactical_fishing.title": "Pesca tàctica", "advancements.husbandry.tadpole_in_a_bucket.description": "<PERSON><PERSON>a un cullerot amb un poal", "advancements.husbandry.tadpole_in_a_bucket.title": "A poalades", "advancements.husbandry.tame_an_animal.description": "Domestica un animal", "advancements.husbandry.tame_an_animal.title": "Com carn i ungla", "advancements.husbandry.wax_off.description": "Polix la cera d'un bloc de coure!", "advancements.husbandry.wax_off.title": "Polir cera", "advancements.husbandry.wax_on.description": "Encera un bloc de coure amb una bresca!", "advancements.husbandry.wax_on.title": "Posar cera", "advancements.husbandry.whole_pack.description": "Domestica totes les races de llop", "advancements.husbandry.whole_pack.title": "Un de cada", "advancements.nether.all_effects.description": "Aconseguix tots els efectes alhora", "advancements.nether.all_effects.title": "Com hem arribat fins ací?", "advancements.nether.all_potions.description": "Aconsegueix tots els efectes de les pocions alhora", "advancements.nether.all_potions.title": "Mescla explosiva", "advancements.nether.brew_potion.description": "Elabora una poció", "advancements.nether.brew_potion.title": "Destil·leria local", "advancements.nether.charge_respawn_anchor.description": "Carrega al màxim una àncora de reaparició", "advancements.nether.charge_respawn_anchor.title": "Set vides? Va, home va!", "advancements.nether.create_beacon.description": "Construïx i col·loca un far màgic", "advancements.nether.create_beacon.title": "Que es faça la llum", "advancements.nether.create_full_beacon.description": "Posa un far màgic a la seua màxima potència", "advancements.nether.create_full_beacon.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.distract_piglin.description": "Distrau els piglins amb or", "advancements.nether.distract_piglin.title": "Com brilla!", "advancements.nether.explore_nether.description": "Explora tots els biomes del Nether", "advancements.nether.explore_nether.title": "Atracció turística", "advancements.nether.fast_travel.description": "Utilitza el Nether per recórrer 7 km a la superfície", "advancements.nether.fast_travel.title": "Forat de cuc", "advancements.nether.find_bastion.description": "Entra dins d'un bastió", "advancements.nether.find_bastion.title": "Quins temps", "advancements.nether.find_fortress.description": "Endinsa't dins d'una fortalesa del Nether", "advancements.nether.find_fortress.title": "Una fortalesa terrible", "advancements.nether.get_wither_skull.description": "Aconseguix una calavera d'esquelet wither", "advancements.nether.get_wither_skull.title": "Per un cap", "advancements.nether.loot_bastion.description": "Saqueja un cofre d'un bastió", "advancements.nether.loot_bastion.title": "Porcs de guerra", "advancements.nether.netherite_armor.description": "Aconseguix una armadura de netherita completa", "advancements.nether.netherite_armor.title": "Cobrix-me d'enderrocs", "advancements.nether.obtain_ancient_debris.description": "Obtín enderrocs antics", "advancements.nether.obtain_ancient_debris.title": "Amagat a les profunditats", "advancements.nether.obtain_blaze_rod.description": "Aconseguix una vara flamenjant d'una Flama", "advancements.nether.obtain_blaze_rod.title": "Jugant amb foc", "advancements.nether.obtain_crying_obsidian.description": "Obtín obsidiana plorosa", "advancements.nether.obtain_crying_obsidian.title": "Qui està pelant ceba?", "advancements.nether.return_to_sender.description": "Mata un ghast retornant-li una bola de foc", "advancements.nether.return_to_sender.title": "Retorn al remitent", "advancements.nether.ride_strider.description": "Cavalca un caminant amb un fong estrambòtic en un pal", "advancements.nether.ride_strider.title": "Aquesta barca té cames!", "advancements.nether.ride_strider_in_overworld_lava.description": "Fes un bon passeig sobre un caminant en un llac de lava de la superfície", "advancements.nether.ride_strider_in_overworld_lava.title": "Com a casa", "advancements.nether.root.description": "Posa't fresc", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "Invoca al wither", "advancements.nether.summon_wither.title": "Dr. <PERSON>", "advancements.nether.uneasy_alliance.description": "Rescata un ghast del Nether, porta'l a la superfície... i després mata'l", "advancements.nether.uneasy_alliance.title": "Falsa aliança", "advancements.nether.use_lodestone.description": "<PERSON><PERSON>ta una br<PERSON>ixola", "advancements.nether.use_lodestone.title": "Au a casa! Per on era?", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Debilita i cura un vilatà zombi", "advancements.story.cure_zombie_villager.title": "Exorcista", "advancements.story.deflect_arrow.description": "Desvia un projectil amb un escut", "advancements.story.deflect_arrow.title": "Hui no, gràcies", "advancements.story.enchant_item.description": "Encanta un objecte en la taula d'encanteris", "advancements.story.enchant_item.title": "B<PERSON>ixot", "advancements.story.enter_the_end.description": "Entra al portal a l'End", "advancements.story.enter_the_end.title": "L'End?", "advancements.story.enter_the_nether.description": "Construïx, activa i entra a un portal al Nether", "advancements.story.enter_the_nether.title": "Ànimes condemnades", "advancements.story.follow_ender_eye.description": "Seguix un ull d'<PERSON>er", "advancements.story.follow_ender_eye.title": "Ull espia", "advancements.story.form_obsidian.description": "Obtín un bloc d'obsidiana", "advancements.story.form_obsidian.title": "<PERSON><PERSON><PERSON> f<PERSON>a", "advancements.story.iron_tools.description": "Millora el teu pic", "advancements.story.iron_tools.title": "Bona elecció!", "advancements.story.lava_bucket.description": "Ompli un poal amb lava", "advancements.story.lava_bucket.title": "Crema!", "advancements.story.mine_diamond.description": "Aconsegueix diamants", "advancements.story.mine_diamond.title": "Diamants!", "advancements.story.mine_stone.description": "Mina pedra amb el teu nou pic", "advancements.story.mine_stone.title": "Edat de pedra", "advancements.story.obtain_armor.description": "Protegix-te el cos amb una peça d'armadura de ferro", "advancements.story.obtain_armor.title": "Vestix-te", "advancements.story.root.description": "El cor i la història del joc", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "L'armadura de diamant salva vides", "advancements.story.shiny_gear.title": "Cobrix-me de diamants", "advancements.story.smelt_iron.description": "Forja un lingot de ferro", "advancements.story.smelt_iron.title": "Adquirint tecnologia", "advancements.story.upgrade_tools.description": "Millora el teu pic", "advancements.story.upgrade_tools.title": "Obtenint una millora", "advancements.toast.challenge": "Repte completat!", "advancements.toast.goal": "Objectiu assolit!", "advancements.toast.task": "Avanç fet!", "argument.anchor.invalid": "La posició d'ancoratge de l'entitat és invàlida: %s", "argument.angle.incomplete": "Incomplet (s'esperava 1 angle)", "argument.angle.invalid": "<PERSON><PERSON> in<PERSON>", "argument.block.id.invalid": "Tipus de bloc desconegut: \"%s\"", "argument.block.property.duplicate": "La propietat \"%s\" del bloc %s només pot establir-se una vegada", "argument.block.property.invalid": "El bloc %s no accepta \"%s\" per a la propietat \"%s\"", "argument.block.property.novalue": "S'esperava un valor per a la propietat \"%s\" del bloc %s", "argument.block.property.unclosed": "Falta un ] per tancar les propietats del bloc", "argument.block.property.unknown": "El bloc %s no té la propietat \"%s\"", "argument.block.tag.disallowed": "Ací no es permeten les etiquetes, només blocs actuals", "argument.color.invalid": "El color \"%s\" és desconegut", "argument.component.invalid": "Component del xat invàlid: %s", "argument.criteria.invalid": "El criteri \"%s\" és desconegut", "argument.dimension.invalid": "Dimensió desconeguda: %s", "argument.double.big": "El valor real llarg no pot ser major que %s, s'ha trobat %s", "argument.double.low": "El valor real llarg no pot ser menor que %s, s'ha trobat %s", "argument.entity.invalid": "Nom o UUID invàlids", "argument.entity.notfound.entity": "No s'ha trobat cap entitat", "argument.entity.notfound.player": "No s'ha trobat cap jugador", "argument.entity.options.advancements.description": "Jugadors amb a<PERSON>", "argument.entity.options.distance.description": "Distància a l'entitat", "argument.entity.options.distance.negative": "La distància no pot ser negativa", "argument.entity.options.dx.description": "Entitats entre x i x + dx", "argument.entity.options.dy.description": "Entitats entre y i y + dy", "argument.entity.options.dz.description": "Entitats entre z i z + dz", "argument.entity.options.gamemode.description": "Jugadors amb el mode de joc", "argument.entity.options.inapplicable": "L'opció \"%s\" no pot aplicar-se ací", "argument.entity.options.level.description": "Nivell d'experiència", "argument.entity.options.level.negative": "El nivell no pot ser negatiu", "argument.entity.options.limit.description": "Nombre màxim d'entitats a retornar", "argument.entity.options.limit.toosmall": "El límit ha de ser almenys 1", "argument.entity.options.mode.invalid": "Mode de joc invàlid o desconegut: %s", "argument.entity.options.name.description": "Nom de l'entitat", "argument.entity.options.nbt.description": "Entitats amb NBT", "argument.entity.options.predicate.description": "Predicat personalitzat", "argument.entity.options.scores.description": "Entitats amb puntuacions", "argument.entity.options.sort.description": "Ordena les entitats", "argument.entity.options.sort.irreversible": "El tipus d'ordenació \"%s\" és desconegut o invàlid", "argument.entity.options.tag.description": "Entitats amb etiquetes", "argument.entity.options.team.description": "Entitats en l'equip", "argument.entity.options.type.description": "Entitats del tipus", "argument.entity.options.type.invalid": "Tipus d'entitat desconeguda o invàlida: %s", "argument.entity.options.unknown": "Opció desconeguda: %s", "argument.entity.options.unterminated": "Falta el tancament de les opcions", "argument.entity.options.valueless": "Es requerix un valor per a l'opció \"%s\"", "argument.entity.options.x.description": "Posició x", "argument.entity.options.x_rotation.description": "Rotació x de l'entitat", "argument.entity.options.y.description": "Posició y", "argument.entity.options.y_rotation.description": "Rotació y de l'entitat", "argument.entity.options.z.description": "Posició z", "argument.entity.selector.allEntities": "Totes les entitats", "argument.entity.selector.allPlayers": "Tots els jugadors", "argument.entity.selector.missing": "Falta el tipus de selector", "argument.entity.selector.nearestEntity": "Entitat més pròxima", "argument.entity.selector.nearestPlayer": "Jugador més proper", "argument.entity.selector.not_allowed": "El selector no està permés", "argument.entity.selector.randomPlayer": "Jugador aleatori", "argument.entity.selector.self": "Entitat seleccionada", "argument.entity.selector.unknown": "Tipus de selector desconegut: %s", "argument.entity.toomany": "Només es permet una entitat, però el selector utilitzat en permet més d'una", "argument.enum.invalid": "El valor \"%s\" no és vàlid", "argument.float.big": "El valor real no pot ser major que %s, s'ha trobat %s", "argument.float.low": "El valor real no pot ser menor que %s, s'ha trobat %s", "argument.gamemode.invalid": "Mode de joc desconegut: %s", "argument.hexcolor.invalid": "Codi de color hexadecimal \"%s\" invàlid", "argument.id.invalid": "ID invàlid", "argument.id.unknown": "ID desconeguda: %s", "argument.integer.big": "El nombre enter no pot ser major que %s, s'ha trobat %s", "argument.integer.low": "El nombre enter no pot ser menor que %s, s'ha trobat %s", "argument.item.id.invalid": "Objecte desconegut: %s", "argument.item.tag.disallowed": "Ací no es permeten les etiquetes, només objectes actuals", "argument.literal.incorrect": "Es requerix el valor literal \"%s\"", "argument.long.big": "El valor enter no pot ser major que %s, s'ha trobat %s", "argument.long.low": "El valor enter no pot ser menor que %s, s'ha trobat %s", "argument.message.too_long": "El missatge del xat és massa llarg (%s > màxim %s caràcters)", "argument.nbt.array.invalid": "Tipus de fletxa invàlid: %s", "argument.nbt.array.mixed": "No es pot inserir %s a %s", "argument.nbt.expected.compound": "S'esperava una etiqueta composta", "argument.nbt.expected.key": "Falta la clau", "argument.nbt.expected.value": "Falta el valor", "argument.nbt.list.mixed": "No es pot inserir %s a la llista de %s", "argument.nbt.trailing": "Hi ha dades de més", "argument.player.entities": "Aquest comandament només afecta a jugadors, però el selector utilitzar inclou entitats", "argument.player.toomany": "Només es permet un jugador, però el selector utilitzat en permet més d'un", "argument.player.unknown": "Eixe jugador no existix", "argument.pos.missing.double": "S'esperava una coordenada", "argument.pos.missing.int": "S'esperava la posició d'un bloc", "argument.pos.mixed": "No es poden mesclar coordenades globals i locals (o s'utilitza ^ amb totes o amb cap)", "argument.pos.outofbounds": "La posició està fora dels límits del món.", "argument.pos.outofworld": "Eixa posició està fora del món!", "argument.pos.unloaded": "Eixa posició no està carregada", "argument.pos2d.incomplete": "Incomplet (s'esperaven 2 coordenades)", "argument.pos3d.incomplete": "Incomplet (s'esperaven 3 coordenades)", "argument.range.empty": "S'esperava un valor o un interval de valors", "argument.range.ints": "No<PERSON>s s'accepten nombres enters, no decimals", "argument.range.swapped": "El mínim no pot ser més gran que el màxim", "argument.resource.invalid_type": "L'element \"%s\" té el tipus incorrecte \"%s\" (s'esperava \"%s\")", "argument.resource.not_found": "No es troba l'element \"%s\" del tipus \"%s\"", "argument.resource_or_id.failed_to_parse": "No s'ha pogut analitzar l'estructura: %s", "argument.resource_or_id.invalid": "Id o etiqueta invàlida", "argument.resource_or_id.no_such_element": "No es pot trobar l'element \"%s\" al registre \"%s\"", "argument.resource_selector.not_found": "No hi ha coincidències per al selector \"%s\" del tipus \"%s\"", "argument.resource_tag.invalid_type": "L'etiqueta \"%s\" té el tipus incorrecte \"%s\" (s'esperava \"%s\")", "argument.resource_tag.not_found": "No es troba l'etiqueta \"%s\" del tipus \"%s\"", "argument.rotation.incomplete": "Incomplet (s'esperaven 2 coordenades)", "argument.scoreHolder.empty": "No s'han trobat marcadors de puntuació rellevants", "argument.scoreboardDisplaySlot.invalid": "Espai de mostra desconegut: \"%s\"", "argument.style.invalid": "Estil invàlid: %s", "argument.time.invalid_tick_count": "El recompte de tics no pot ser negatiu", "argument.time.invalid_unit": "Unitat invàlida", "argument.time.tick_count_too_low": "El recompte de tics no pot ser menor que %s, s'ha trobat %s", "argument.uuid.invalid": "L'UUID és invàlida", "argument.waypoint.invalid": "L'entitat seleccionada no és un punt de ruta", "arguments.block.tag.unknown": "Etiqueta de bloc desconeguda: \"%s\"", "arguments.function.tag.unknown": "Etiqueta de funció desconeguda: \"%s\"", "arguments.function.unknown": "Funció desconeguda: %s", "arguments.item.component.expected": "Component d'objecte esperat", "arguments.item.component.malformed": "Component \"%s\" malformat: \"%s\"", "arguments.item.component.repeated": "El component d'objectes \"%s\" s'ha repetit, però només es pot especificar un valor", "arguments.item.component.unknown": "Component d'objecte \"%s\" desconegut", "arguments.item.malformed": "Objecte malformat: \"%s\"", "arguments.item.overstacked": "%s només pot apilar-se fins a %s", "arguments.item.predicate.malformed": "El predicat \"%s\" està mal format: \"%s\"", "arguments.item.predicate.unknown": "Predicat d'objecte desconegut \"%s\"", "arguments.item.tag.unknown": "Etiqueta d'objecte desconeguda: %s", "arguments.nbtpath.node.invalid": "La ruta NBT no és vàlida", "arguments.nbtpath.nothing_found": "No s'ha trobat cap element coincident amb %s", "arguments.nbtpath.too_deep": "L'NBT resultant està niuada massa profundament", "arguments.nbtpath.too_large": "L'NBT resultant és massa gran", "arguments.objective.notFound": "Objectiu de la taula de puntuació desconegut: \"%s\"", "arguments.objective.readonly": "L'obectiu \"%s\" de la taula de puntuació és només de lectura", "arguments.operation.div0": "No es pot dividir per zero", "arguments.operation.invalid": "Operació <PERSON>", "arguments.swizzle.invalid": "La combinació d'eixos no és vàlida, ha de ser \"x\", \"y\" i \"z\"", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "Duresa de l'armadura", "attribute.name.attack_damage": "<PERSON><PERSON>'<PERSON>ac", "attribute.name.attack_knockback": "Espenta de l'atac", "attribute.name.attack_speed": "Velocitat de l'atac", "attribute.name.block_break_speed": "Velocitat de trencament de bloc", "attribute.name.block_interaction_range": "Rang d'interacció amb blocs", "attribute.name.burning_time": "Temps de crema", "attribute.name.camera_distance": "Distància de càmera", "attribute.name.entity_interaction_range": "Rang d'interacció amb entitats", "attribute.name.explosion_knockback_resistance": "Resistència al retrocés per explosió", "attribute.name.fall_damage_multiplier": "Multiplicador de dany per caiguda", "attribute.name.flying_speed": "Velocitat de vol", "attribute.name.follow_range": "Rang de seguiment de les criatures", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Duresa de l'armadura", "attribute.name.generic.attack_damage": "Dany per atac", "attribute.name.generic.attack_knockback": "<PERSON><PERSON> espentant", "attribute.name.generic.attack_speed": "Velocitat d'atac", "attribute.name.generic.block_interaction_range": "Rang d'interacció amb blocs", "attribute.name.generic.burning_time": "Temps de crema", "attribute.name.generic.entity_interaction_range": "Rang d'interacció amb entitats", "attribute.name.generic.explosion_knockback_resistance": "Resistència al retrocés per explosió", "attribute.name.generic.fall_damage_multiplier": "Multiplicador de dany per caiguda", "attribute.name.generic.flying_speed": "Velocitat volant", "attribute.name.generic.follow_range": "Rang de seguiment de l'ésser", "attribute.name.generic.gravity": "Gravetat", "attribute.name.generic.jump_strength": "Força de salt", "attribute.name.generic.knockback_resistance": "Resistència a l'espenta", "attribute.name.generic.luck": "Sort", "attribute.name.generic.max_absorption": "<PERSON><PERSON>or<PERSON><PERSON> m<PERSON>xi<PERSON>", "attribute.name.generic.max_health": "<PERSON>ut màxima", "attribute.name.generic.movement_efficiency": "Eficiència de moviment", "attribute.name.generic.movement_speed": "Velocitat", "attribute.name.generic.oxygen_bonus": "Bonificació d'oxigen", "attribute.name.generic.safe_fall_distance": "Distància de caiguda segura", "attribute.name.generic.scale": "Escala", "attribute.name.generic.step_height": "Grandària del pas", "attribute.name.generic.water_movement_efficiency": "Eficiència de moviment en l'aigua", "attribute.name.gravity": "Gravetat", "attribute.name.horse.jump_strength": "Impuls de salt del cavall", "attribute.name.jump_strength": "Força de salt", "attribute.name.knockback_resistance": "Resistència a l'espenta", "attribute.name.luck": "Sort", "attribute.name.max_absorption": "<PERSON><PERSON>or<PERSON><PERSON> m<PERSON>xi<PERSON>", "attribute.name.max_health": "<PERSON>ut màxima", "attribute.name.mining_efficiency": "Eficiència minera", "attribute.name.movement_efficiency": "Eficiència de moviment", "attribute.name.movement_speed": "Velocitat", "attribute.name.oxygen_bonus": "Bonificació d'oxigen", "attribute.name.player.block_break_speed": "Velocitat de trencament de blocs", "attribute.name.player.block_interaction_range": "Rang d'interacció amb blocs", "attribute.name.player.entity_interaction_range": "Rang d'interacció amb entitats", "attribute.name.player.mining_efficiency": "Eficiència minera", "attribute.name.player.sneaking_speed": "Velocitat en acatxar-se", "attribute.name.player.submerged_mining_speed": "Velocitat minera bussejant", "attribute.name.player.sweeping_damage_ratio": "<PERSON><PERSON><PERSON><PERSON> de dany per batuda", "attribute.name.safe_fall_distance": "Distància de caiguda segura", "attribute.name.scale": "Escala", "attribute.name.sneaking_speed": "Velocitat en acatxar-se", "attribute.name.spawn_reinforcements": "Reforços de zombis", "attribute.name.step_height": "Grandària del pas", "attribute.name.submerged_mining_speed": "Velocitat minera bussejant", "attribute.name.sweeping_damage_ratio": "<PERSON><PERSON><PERSON><PERSON> de dany per batuda", "attribute.name.tempt_range": "Rang d'atracció de criatures", "attribute.name.water_movement_efficiency": "Eficiència de moviment en l'aigua", "attribute.name.waypoint_receive_range": "Rang de recepció de punt de ruta", "attribute.name.waypoint_transmit_range": "Rang de transmissió de punt de ruta", "attribute.name.zombie.spawn_reinforcements": "Reforços de zombis", "biome.minecraft.badlands": "<PERSON><PERSON><PERSON> ermes", "biome.minecraft.bamboo_jungle": "Bambú de jungla", "biome.minecraft.basalt_deltas": "Delta de basalt", "biome.minecraft.beach": "<PERSON><PERSON><PERSON>", "biome.minecraft.birch_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON>", "biome.minecraft.cold_ocean": "Oceà gelat", "biome.minecraft.crimson_forest": "Bosc carmesí", "biome.minecraft.dark_forest": "Bosc fosc", "biome.minecraft.deep_cold_ocean": "Oceà profund gèlid", "biome.minecraft.deep_dark": "Profunditats obscures", "biome.minecraft.deep_frozen_ocean": "Oceà profund congelat", "biome.minecraft.deep_lukewarm_ocean": "Oceà profund tebi", "biome.minecraft.deep_ocean": "Oceà profund", "biome.minecraft.desert": "Desert", "biome.minecraft.dripstone_caves": "Coves d'espeleotema", "biome.minecraft.end_barrens": "L'End - Z<PERSON>rida", "biome.minecraft.end_highlands": "L'End - Terres altes", "biome.minecraft.end_midlands": "Zona mitgera de l'End", "biome.minecraft.eroded_badlands": "Terres ermes <PERSON>ades", "biome.minecraft.flower_forest": "Bosc floral", "biome.minecraft.forest": "Bosc", "biome.minecraft.frozen_ocean": "Oceà congelat", "biome.minecraft.frozen_peaks": "Pics gelats", "biome.minecraft.frozen_river": "<PERSON><PERSON> congel<PERSON>", "biome.minecraft.grove": "Arbreda", "biome.minecraft.ice_spikes": "Pics de gel", "biome.minecraft.jagged_peaks": "<PERSON><PERSON><PERSON>", "biome.minecraft.jungle": "<PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON> te<PERSON>", "biome.minecraft.lush_caves": "Coves frondoses", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.meadow": "<PERSON><PERSON>", "biome.minecraft.mushroom_fields": "Camps de xampinyons", "biome.minecraft.nether_wastes": "Erms del Nether", "biome.minecraft.ocean": "Oceà", "biome.minecraft.old_growth_birch_forest": "Bedollar ancestral", "biome.minecraft.old_growth_pine_taiga": "<PERSON><PERSON><PERSON> de <PERSON> ancestrals", "biome.minecraft.old_growth_spruce_taiga": "Taigà de pícees ancestrals", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON>", "biome.minecraft.plains": "Planures", "biome.minecraft.river": "Riu", "biome.minecraft.savanna": "Sabana", "biome.minecraft.savanna_plateau": "Planures de sabana", "biome.minecraft.small_end_islands": "Illes de l'End menudes", "biome.minecraft.snowy_beach": "Platja nevada", "biome.minecraft.snowy_plains": "<PERSON><PERSON><PERSON> nevats", "biome.minecraft.snowy_slopes": "Vessants nevats", "biome.minecraft.snowy_taiga": "Taigà nevada", "biome.minecraft.soul_sand_valley": "<PERSON>l d'<PERSON>", "biome.minecraft.sparse_jungle": "Jungla dispersa", "biome.minecraft.stony_peaks": "Pics pedregosos", "biome.minecraft.stony_shore": "Costa pedregosa", "biome.minecraft.sunflower_plains": "Prats de gira-sols", "biome.minecraft.swamp": "Pantà", "biome.minecraft.taiga": "Taigà", "biome.minecraft.the_end": "L'End", "biome.minecraft.the_void": "El buit", "biome.minecraft.warm_ocean": "Oceà temperat", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.windswept_forest": "Bosc ventós", "biome.minecraft.windswept_gravelly_hills": "<PERSON><PERSON><PERSON><PERSON> ventoses pedregoses", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON><PERSON> ventoses", "biome.minecraft.windswept_savanna": "Sabana ventosa", "biome.minecraft.wooded_badlands": "Terres ermes boscoses", "block.minecraft.acacia_button": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_door": "Porta d'acàcia", "block.minecraft.acacia_fence": "Tanca d'acàcia", "block.minecraft.acacia_fence_gate": "Porta de tanca d'acàcia", "block.minecraft.acacia_hanging_sign": "Cartell d'ac<PERSON><PERSON> pen<PERSON>", "block.minecraft.acacia_leaves": "Fulles d'acàcia", "block.minecraft.acacia_log": "Tronc d'acàcia", "block.minecraft.acacia_planks": "Taulons d'acàcia", "block.minecraft.acacia_pressure_plate": "Placa de pressió d'acàcia", "block.minecraft.acacia_sapling": "Esqueix d'acàcia", "block.minecraft.acacia_sign": "Cartell d'acàcia", "block.minecraft.acacia_slab": "Llosa d'acàcia", "block.minecraft.acacia_stairs": "<PERSON><PERSON><PERSON>'a<PERSON>", "block.minecraft.acacia_trapdoor": "Trapa d'acàcia", "block.minecraft.acacia_wall_hanging_sign": "Cartell d'acàcia penjant de paret", "block.minecraft.acacia_wall_sign": "Cartell d'acàcia de paret", "block.minecraft.acacia_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.activator_rail": "Via activadora", "block.minecraft.air": "Aire", "block.minecraft.allium": "Allium", "block.minecraft.amethyst_block": "Bloc d'ametista", "block.minecraft.amethyst_cluster": "<PERSON><PERSON><PERSON><PERSON> d'ametista", "block.minecraft.ancient_debris": "Enderrocs antics", "block.minecraft.andesite": "Andesita", "block.minecraft.andesite_slab": "Llosa d'andesita", "block.minecraft.andesite_stairs": "Escales d'andesita", "block.minecraft.andesite_wall": "<PERSON>r d'and<PERSON><PERSON>", "block.minecraft.anvil": "Enclusa", "block.minecraft.attached_melon_stem": "Tija de meló d'Alger unida", "block.minecraft.attached_pumpkin_stem": "Tija de carabassa unida", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Fulles d'azalea", "block.minecraft.azure_bluet": "Houston<PERSON> caerulea", "block.minecraft.bamboo": "Bambú", "block.minecraft.bamboo_block": "Bloc de bambú", "block.minecraft.bamboo_button": "<PERSON><PERSON><PERSON>", "block.minecraft.bamboo_door": "Porta de bambú", "block.minecraft.bamboo_fence": "Tanca de bam<PERSON>ú", "block.minecraft.bamboo_fence_gate": "Porta de tanca de bambú", "block.minecraft.bamboo_hanging_sign": "<PERSON><PERSON><PERSON> de bamb<PERSON> pen<PERSON>t", "block.minecraft.bamboo_mosaic": "Mosaic de bam<PERSON>ú", "block.minecraft.bamboo_mosaic_slab": "Llosa de mosaic de bambú", "block.minecraft.bamboo_mosaic_stairs": "Escales de mosaic de bambú", "block.minecraft.bamboo_planks": "Taulons de bam<PERSON>ú", "block.minecraft.bamboo_pressure_plate": "Placa de pressió de bambú", "block.minecraft.bamboo_sapling": "Esqueix de bambú", "block.minecraft.bamboo_sign": "<PERSON><PERSON><PERSON> de b<PERSON>", "block.minecraft.bamboo_slab": "Llosa de bam<PERSON>ú", "block.minecraft.bamboo_stairs": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.bamboo_trapdoor": "Trapa de bam<PERSON>ú", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON><PERSON><PERSON> de bambú penjant de paret", "block.minecraft.bamboo_wall_sign": "<PERSON>tell de bambú de paret", "block.minecraft.banner.base.black": "Superfície negra", "block.minecraft.banner.base.blue": "Superfície blava", "block.minecraft.banner.base.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.cyan": "<PERSON><PERSON><PERSON><PERSON>ian", "block.minecraft.banner.base.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.green": "<PERSON><PERSON><PERSON><PERSON> verda", "block.minecraft.banner.base.light_blue": "Superfície blava clara", "block.minecraft.banner.base.light_gray": "<PERSON>f<PERSON>cie grisa clara", "block.minecraft.banner.base.lime": "Superf<PERSON>cie verda llima", "block.minecraft.banner.base.magenta": "<PERSON>f<PERSON>cie magenta", "block.minecraft.banner.base.orange": "<PERSON>f<PERSON>cie <PERSON>", "block.minecraft.banner.base.pink": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.base.purple": "Superfície mora<PERSON>", "block.minecraft.banner.base.red": "Superf<PERSON>cie roja", "block.minecraft.banner.base.white": "Superfície blanca", "block.minecraft.banner.base.yellow": "<PERSON><PERSON><PERSON><PERSON>a", "block.minecraft.banner.border.black": "Bordura negra", "block.minecraft.banner.border.blue": "Bordura blava", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON> grisa", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON> verda", "block.minecraft.banner.border.light_blue": "Bordura blava clara", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON> grisa clara", "block.minecraft.banner.border.lime": "Bo<PERSON><PERSON> verda llima", "block.minecraft.banner.border.magenta": "Bordura magenta", "block.minecraft.banner.border.orange": "<PERSON>rd<PERSON> taronja", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.border.purple": "Bordura morada", "block.minecraft.banner.border.red": "Bord<PERSON> roja", "block.minecraft.banner.border.white": "<PERSON>rd<PERSON> blanca", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON> groga", "block.minecraft.banner.bricks.black": "Maçonat negre", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON><PERSON><PERSON> blau", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.banner.bricks.gray": "Ma<PERSON><PERSON>t gris", "block.minecraft.banner.bricks.green": "Ma<PERSON><PERSON><PERSON> verd", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON><PERSON><PERSON> blau clar", "block.minecraft.banner.bricks.light_gray": "Ma<PERSON>onat gris clar", "block.minecraft.banner.bricks.lime": "Ma<PERSON><PERSON>t verd llima", "block.minecraft.banner.bricks.magenta": "Maçonat magenta", "block.minecraft.banner.bricks.orange": "Ma<PERSON>onat <PERSON>", "block.minecraft.banner.bricks.pink": "Ma<PERSON><PERSON>t rosa", "block.minecraft.banner.bricks.purple": "Maçonat morat", "block.minecraft.banner.bricks.red": "Ma<PERSON>onat roig", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON><PERSON> blanc", "block.minecraft.banner.bricks.yellow": "Maçonat groc", "block.minecraft.banner.circle.black": "<PERSON><PERSON> negra", "block.minecraft.banner.circle.blue": "<PERSON><PERSON> blava", "block.minecraft.banner.circle.brown": "<PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.circle.gray": "<PERSON><PERSON> grisa", "block.minecraft.banner.circle.green": "<PERSON><PERSON> verda", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON> blava clara", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON> grisa clara", "block.minecraft.banner.circle.lime": "<PERSON><PERSON> verda llima", "block.minecraft.banner.circle.magenta": "Rodella magenta", "block.minecraft.banner.circle.orange": "<PERSON><PERSON>", "block.minecraft.banner.circle.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.circle.purple": "<PERSON><PERSON> morada", "block.minecraft.banner.circle.red": "<PERSON><PERSON>", "block.minecraft.banner.circle.white": "<PERSON><PERSON> blanca", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON> groga", "block.minecraft.banner.creeper.black": "Estampat de creeper negre", "block.minecraft.banner.creeper.blue": "Estampat de creeper blau", "block.minecraft.banner.creeper.brown": "Estampat de creeper marró", "block.minecraft.banner.creeper.cyan": "Estampat de creeper cian", "block.minecraft.banner.creeper.gray": "Estampat de creeper gris", "block.minecraft.banner.creeper.green": "Estampat de creeper verd", "block.minecraft.banner.creeper.light_blue": "Estampat de creeper blau clar", "block.minecraft.banner.creeper.light_gray": "Estampat de creeper gris clar", "block.minecraft.banner.creeper.lime": "Estampat de creeper verd llima", "block.minecraft.banner.creeper.magenta": "Estampat de creeper magenta", "block.minecraft.banner.creeper.orange": "Estampat de creeper taronja", "block.minecraft.banner.creeper.pink": "Estampat de creeper rosa", "block.minecraft.banner.creeper.purple": "Estampat de creeper morat", "block.minecraft.banner.creeper.red": "Estampat de creeper roig", "block.minecraft.banner.creeper.white": "Estampat de creeper blanc", "block.minecraft.banner.creeper.yellow": "Estampat de creeper groc", "block.minecraft.banner.cross.black": "<PERSON><PERSON> negra", "block.minecraft.banner.cross.blue": "<PERSON>pa blava", "block.minecraft.banner.cross.brown": "<PERSON><PERSON> marr<PERSON>", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.cross.gray": "<PERSON><PERSON> grisa", "block.minecraft.banner.cross.green": "<PERSON><PERSON> verda", "block.minecraft.banner.cross.light_blue": "<PERSON>pa blava clara", "block.minecraft.banner.cross.light_gray": "<PERSON><PERSON> grisa clara", "block.minecraft.banner.cross.lime": "<PERSON><PERSON> verda llima", "block.minecraft.banner.cross.magenta": "Aspa magenta", "block.minecraft.banner.cross.orange": "<PERSON><PERSON> taronja", "block.minecraft.banner.cross.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.cross.purple": "Aspa morada", "block.minecraft.banner.cross.red": "<PERSON><PERSON> roja", "block.minecraft.banner.cross.white": "As<PERSON> blanca", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON> groga", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON><PERSON> dentada negra", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON> dentada blava", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> dentada marr<PERSON>", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON> dentada cian", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON> dentada grisa", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON> dentada verda", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON><PERSON> dentada blava clara", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON><PERSON> dentada grisa clara", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON> dentada verda llima", "block.minecraft.banner.curly_border.magenta": "Bordura dentada magenta", "block.minecraft.banner.curly_border.orange": "<PERSON><PERSON><PERSON> dentada ta<PERSON>ja", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON> dentada rosa", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON><PERSON> dentada morada", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON> dentada roja", "block.minecraft.banner.curly_border.white": "<PERSON>rdura dentada blanca", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON><PERSON> dentada groga", "block.minecraft.banner.diagonal_left.black": "Tallat negre a dret", "block.minecraft.banner.diagonal_left.blue": "Tallat blau a dret", "block.minecraft.banner.diagonal_left.brown": "Tallat marró a dret", "block.minecraft.banner.diagonal_left.cyan": "Tallat cian a dret", "block.minecraft.banner.diagonal_left.gray": "Tallat gris a dret", "block.minecraft.banner.diagonal_left.green": "Tallat verd a dret", "block.minecraft.banner.diagonal_left.light_blue": "Tallat blau clar a dret", "block.minecraft.banner.diagonal_left.light_gray": "Tallat gris clar a dret", "block.minecraft.banner.diagonal_left.lime": "Tallat verd llima a dret", "block.minecraft.banner.diagonal_left.magenta": "Tallat magenta a dret", "block.minecraft.banner.diagonal_left.orange": "Tallat taronja a dret", "block.minecraft.banner.diagonal_left.pink": "Tallat rosa a dret", "block.minecraft.banner.diagonal_left.purple": "Tallat morat a dret", "block.minecraft.banner.diagonal_left.red": "Tallat roig a dret", "block.minecraft.banner.diagonal_left.white": "Tallat blanc a dret", "block.minecraft.banner.diagonal_left.yellow": "Tallat groc a dret", "block.minecraft.banner.diagonal_right.black": "Tallat negre a tort", "block.minecraft.banner.diagonal_right.blue": "Tallat blau a tort", "block.minecraft.banner.diagonal_right.brown": "Tallat marró a tort", "block.minecraft.banner.diagonal_right.cyan": "Tallat cian a tort", "block.minecraft.banner.diagonal_right.gray": "Tallat gris a tort", "block.minecraft.banner.diagonal_right.green": "Tallat verd a tort", "block.minecraft.banner.diagonal_right.light_blue": "Tallat blau clar a tort", "block.minecraft.banner.diagonal_right.light_gray": "Tallat gris clar a tort", "block.minecraft.banner.diagonal_right.lime": "Tallat verd llima a tort", "block.minecraft.banner.diagonal_right.magenta": "Tallat magenta a tort", "block.minecraft.banner.diagonal_right.orange": "Tallat taronja a tort", "block.minecraft.banner.diagonal_right.pink": "Tallat rosa a tort", "block.minecraft.banner.diagonal_right.purple": "Tallat morat a tort", "block.minecraft.banner.diagonal_right.red": "Tallat roig a tort", "block.minecraft.banner.diagonal_right.white": "Tallat blanc a tort", "block.minecraft.banner.diagonal_right.yellow": "Tallat groc a tort", "block.minecraft.banner.diagonal_up_left.black": "Tallat invertit negre a destra", "block.minecraft.banner.diagonal_up_left.blue": "Tallat invertit blau a destra", "block.minecraft.banner.diagonal_up_left.brown": "Tallat invertit marró a destra", "block.minecraft.banner.diagonal_up_left.cyan": "Tallat invertit cian a destra", "block.minecraft.banner.diagonal_up_left.gray": "Tallat invertit gris a destra", "block.minecraft.banner.diagonal_up_left.green": "Tallat invertit verd a destra", "block.minecraft.banner.diagonal_up_left.light_blue": "Tallat invertit blau clar a destra", "block.minecraft.banner.diagonal_up_left.light_gray": "Tallat invertit gris clar a destra", "block.minecraft.banner.diagonal_up_left.lime": "Tallat invertit verd llima a destra", "block.minecraft.banner.diagonal_up_left.magenta": "Tallat invertit magenta a destra", "block.minecraft.banner.diagonal_up_left.orange": "Tallat invertit taronja a destra", "block.minecraft.banner.diagonal_up_left.pink": "Tallat invertit rosa a destra", "block.minecraft.banner.diagonal_up_left.purple": "Tallat invertit morat a destra", "block.minecraft.banner.diagonal_up_left.red": "Tallat invertit roig a destra", "block.minecraft.banner.diagonal_up_left.white": "Tallat invertit blanc a destra", "block.minecraft.banner.diagonal_up_left.yellow": "Tallat invertit groc a destra", "block.minecraft.banner.diagonal_up_right.black": "Tallat invertit negre a sinistra", "block.minecraft.banner.diagonal_up_right.blue": "Tallat invertit blau a sinistra", "block.minecraft.banner.diagonal_up_right.brown": "Tallat invertit marró a sinistra", "block.minecraft.banner.diagonal_up_right.cyan": "Tallat invertit cian a sinistra", "block.minecraft.banner.diagonal_up_right.gray": "Tallat invertit gris a sinistra", "block.minecraft.banner.diagonal_up_right.green": "Tallat invertit verd a sinistra", "block.minecraft.banner.diagonal_up_right.light_blue": "Tallat invertit blau clar a sinistra", "block.minecraft.banner.diagonal_up_right.light_gray": "Tallat invertit gris clar a sinistra", "block.minecraft.banner.diagonal_up_right.lime": "Tallat invertit verd llima a sinistra", "block.minecraft.banner.diagonal_up_right.magenta": "Tallat invertit magenta a sinistra", "block.minecraft.banner.diagonal_up_right.orange": "Tallat invertit taronja a sinistra", "block.minecraft.banner.diagonal_up_right.pink": "Tallat invertit rosa a sinistra", "block.minecraft.banner.diagonal_up_right.purple": "Tallat invertit morat a sinistra", "block.minecraft.banner.diagonal_up_right.red": "Tallat invertit roig a sinistra", "block.minecraft.banner.diagonal_up_right.white": "Tallat invertit blanc a sinistra", "block.minecraft.banner.diagonal_up_right.yellow": "Tallat invertit groc a sinistra", "block.minecraft.banner.flow.black": "Flux negre", "block.minecraft.banner.flow.blue": "Flux blau", "block.minecraft.banner.flow.brown": "Flux marró", "block.minecraft.banner.flow.cyan": "Flux cian", "block.minecraft.banner.flow.gray": "Flux gris", "block.minecraft.banner.flow.green": "Flux verd", "block.minecraft.banner.flow.light_blue": "Flux blau clar", "block.minecraft.banner.flow.light_gray": "Flux gris clar", "block.minecraft.banner.flow.lime": "Flux verd llima", "block.minecraft.banner.flow.magenta": "Flux magenta", "block.minecraft.banner.flow.orange": "Flux taronja", "block.minecraft.banner.flow.pink": "Flux rosa", "block.minecraft.banner.flow.purple": "Flux morat", "block.minecraft.banner.flow.red": "Flux roig", "block.minecraft.banner.flow.white": "Flux blanc", "block.minecraft.banner.flow.yellow": "Flux groc", "block.minecraft.banner.flower.black": "Estampat de flor negre", "block.minecraft.banner.flower.blue": "Estampat de flor blau", "block.minecraft.banner.flower.brown": "Estampat de flor marró", "block.minecraft.banner.flower.cyan": "Estampat de flor cian", "block.minecraft.banner.flower.gray": "Estampat de flor gris", "block.minecraft.banner.flower.green": "Estampat de flor verd", "block.minecraft.banner.flower.light_blue": "Estampat de flor blau clar", "block.minecraft.banner.flower.light_gray": "Estampat de flor gris clar", "block.minecraft.banner.flower.lime": "Estampat de flor verd llima", "block.minecraft.banner.flower.magenta": "Estampat de flor magenta", "block.minecraft.banner.flower.orange": "Estampat de flor taronja", "block.minecraft.banner.flower.pink": "Estampat de flor rosa", "block.minecraft.banner.flower.purple": "Estampat de flor morat", "block.minecraft.banner.flower.red": "Estampat de flor roig", "block.minecraft.banner.flower.white": "Estampat de flor blanc", "block.minecraft.banner.flower.yellow": "Estampat de flor groc", "block.minecraft.banner.globe.black": "Planeta negre", "block.minecraft.banner.globe.blue": "Planeta blau", "block.minecraft.banner.globe.brown": "Planeta marró", "block.minecraft.banner.globe.cyan": "Planeta cian", "block.minecraft.banner.globe.gray": "Planeta gris", "block.minecraft.banner.globe.green": "Planeta verd", "block.minecraft.banner.globe.light_blue": "Planeta blau clar", "block.minecraft.banner.globe.light_gray": "Planeta gris clar", "block.minecraft.banner.globe.lime": "Planeta verd llima", "block.minecraft.banner.globe.magenta": "Planeta magenta", "block.minecraft.banner.globe.orange": "Planeta taronja", "block.minecraft.banner.globe.pink": "Planeta rosa", "block.minecraft.banner.globe.purple": "Planeta morat", "block.minecraft.banner.globe.red": "Planeta roig", "block.minecraft.banner.globe.white": "Planeta blanc", "block.minecraft.banner.globe.yellow": "Planeta groc", "block.minecraft.banner.gradient.black": "Difuminat negre", "block.minecraft.banner.gradient.blue": "Difuminat blau", "block.minecraft.banner.gradient.brown": "Di<PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.banner.gradient.cyan": "Difuminat cian", "block.minecraft.banner.gradient.gray": "Difuminat gris", "block.minecraft.banner.gradient.green": "Difuminat verd", "block.minecraft.banner.gradient.light_blue": "Difuminat blau clar", "block.minecraft.banner.gradient.light_gray": "Difuminat gris clar", "block.minecraft.banner.gradient.lime": "Difuminat verd llima", "block.minecraft.banner.gradient.magenta": "Difuminat magenta", "block.minecraft.banner.gradient.orange": "Difuminat taronja", "block.minecraft.banner.gradient.pink": "Difuminat rosa", "block.minecraft.banner.gradient.purple": "Difuminat morat", "block.minecraft.banner.gradient.red": "Difuminat roig", "block.minecraft.banner.gradient.white": "Difuminat blanc", "block.minecraft.banner.gradient.yellow": "Difuminat groc", "block.minecraft.banner.gradient_up.black": "Difuminat negre des de la base", "block.minecraft.banner.gradient_up.blue": "Difuminat blau des de la base", "block.minecraft.banner.gradient_up.brown": "Difuminat marró des de la base", "block.minecraft.banner.gradient_up.cyan": "Difuminat cian des de la base", "block.minecraft.banner.gradient_up.gray": "Difuminat gris des de la base", "block.minecraft.banner.gradient_up.green": "Difuminat verd des de la base", "block.minecraft.banner.gradient_up.light_blue": "Difuminat blau clar des de la base", "block.minecraft.banner.gradient_up.light_gray": "Difuminat gris clar des de la base", "block.minecraft.banner.gradient_up.lime": "Difuminat verd llima des de la base", "block.minecraft.banner.gradient_up.magenta": "Difuminat magenta des de la base", "block.minecraft.banner.gradient_up.orange": "Difuminat taronja des de la base", "block.minecraft.banner.gradient_up.pink": "Difuminat rosa des de la base", "block.minecraft.banner.gradient_up.purple": "Difuminat morat des de la base", "block.minecraft.banner.gradient_up.red": "Difuminat roig des de la base", "block.minecraft.banner.gradient_up.white": "Difuminat blanc des de la base", "block.minecraft.banner.gradient_up.yellow": "Difuminat groc des de la base", "block.minecraft.banner.guster.black": "Ràfega negra", "block.minecraft.banner.guster.blue": "Ràfega blava", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.guster.cyan": "Ràfega cian", "block.minecraft.banner.guster.gray": "Ràfega gris", "block.minecraft.banner.guster.green": "Ràfega verda", "block.minecraft.banner.guster.light_blue": "Ràfega blava clara", "block.minecraft.banner.guster.light_gray": "Ràfega grisa clara", "block.minecraft.banner.guster.lime": "Ràfega verda llima", "block.minecraft.banner.guster.magenta": "Ràfega magenta", "block.minecraft.banner.guster.orange": "Ràfega ta<PERSON>ja", "block.minecraft.banner.guster.pink": "Ràfega rosa", "block.minecraft.banner.guster.purple": "Ràfega morada", "block.minecraft.banner.guster.red": "Ràfega roja", "block.minecraft.banner.guster.white": "Ràfega blanca", "block.minecraft.banner.guster.yellow": "Ràfega groga", "block.minecraft.banner.half_horizontal.black": "Meitat superior negra", "block.minecraft.banner.half_horizontal.blue": "Meitat superior blava", "block.minecraft.banner.half_horizontal.brown": "Meitat superior marró", "block.minecraft.banner.half_horizontal.cyan": "Meitat superior cian", "block.minecraft.banner.half_horizontal.gray": "Meitat superior grisa", "block.minecraft.banner.half_horizontal.green": "Meitat superior verda", "block.minecraft.banner.half_horizontal.light_blue": "Meitat superior blava clara", "block.minecraft.banner.half_horizontal.light_gray": "Meitat superior grisa clara", "block.minecraft.banner.half_horizontal.lime": "Meitat superior verda llima", "block.minecraft.banner.half_horizontal.magenta": "Meitat superior magenta", "block.minecraft.banner.half_horizontal.orange": "Meitat superior taronja", "block.minecraft.banner.half_horizontal.pink": "Meitat superior rosa", "block.minecraft.banner.half_horizontal.purple": "Meitat superior morada", "block.minecraft.banner.half_horizontal.red": "Meitat superior roja", "block.minecraft.banner.half_horizontal.white": "Meitat superior blanca", "block.minecraft.banner.half_horizontal.yellow": "Meitat superior groga", "block.minecraft.banner.half_horizontal_bottom.black": "Meitat inferior negra", "block.minecraft.banner.half_horizontal_bottom.blue": "Meitat inferior blava", "block.minecraft.banner.half_horizontal_bottom.brown": "Meitat inferior marró", "block.minecraft.banner.half_horizontal_bottom.cyan": "Meitat inferior cian", "block.minecraft.banner.half_horizontal_bottom.gray": "Meitat inferior grisa", "block.minecraft.banner.half_horizontal_bottom.green": "Meitat inferior verda", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Meitat inferior blava clara", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Meitat inferior grisa clara", "block.minecraft.banner.half_horizontal_bottom.lime": "Meitat inferior verda llima", "block.minecraft.banner.half_horizontal_bottom.magenta": "Meitat inferior magenta", "block.minecraft.banner.half_horizontal_bottom.orange": "Meitat inferior taronja", "block.minecraft.banner.half_horizontal_bottom.pink": "Meitat inferior rosa", "block.minecraft.banner.half_horizontal_bottom.purple": "Meitat inferior morada", "block.minecraft.banner.half_horizontal_bottom.red": "Meitat inferior roja", "block.minecraft.banner.half_horizontal_bottom.white": "Meitat inferior blanca", "block.minecraft.banner.half_horizontal_bottom.yellow": "Meitat inferior groga", "block.minecraft.banner.half_vertical.black": "Flanc negre", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON><PERSON> blau", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.half_vertical.gray": "<PERSON>lanc gris", "block.minecraft.banner.half_vertical.green": "Flanc verd", "block.minecraft.banner.half_vertical.light_blue": "<PERSON><PERSON><PERSON> blau clar", "block.minecraft.banner.half_vertical.light_gray": "Flanc gris clar", "block.minecraft.banner.half_vertical.lime": "Flanc verd llima", "block.minecraft.banner.half_vertical.magenta": "Flanc magenta", "block.minecraft.banner.half_vertical.orange": "Flanc taronja", "block.minecraft.banner.half_vertical.pink": "<PERSON>lan<PERSON> rosa", "block.minecraft.banner.half_vertical.purple": "Flanc morat", "block.minecraft.banner.half_vertical.red": "Flanc roig", "block.minecraft.banner.half_vertical.white": "<PERSON>lan<PERSON> blanc", "block.minecraft.banner.half_vertical.yellow": "Flanc groc", "block.minecraft.banner.half_vertical_right.black": "Flanc negre invertit", "block.minecraft.banner.half_vertical_right.blue": "Flanc blau invertit", "block.minecraft.banner.half_vertical_right.brown": "Flanc marró invertit", "block.minecraft.banner.half_vertical_right.cyan": "Flanc cian invertit", "block.minecraft.banner.half_vertical_right.gray": "Flanc gris invertit", "block.minecraft.banner.half_vertical_right.green": "Flanc verd invertit", "block.minecraft.banner.half_vertical_right.light_blue": "Flanc blau clar invertit", "block.minecraft.banner.half_vertical_right.light_gray": "Flanc gris clar invertit", "block.minecraft.banner.half_vertical_right.lime": "Flanc verd llima invertit", "block.minecraft.banner.half_vertical_right.magenta": "Flanc magenta invertit", "block.minecraft.banner.half_vertical_right.orange": "Flanc taronja invertit", "block.minecraft.banner.half_vertical_right.pink": "Flanc rosa invertit", "block.minecraft.banner.half_vertical_right.purple": "Flanc morat invertit", "block.minecraft.banner.half_vertical_right.red": "Flanc roig invertit", "block.minecraft.banner.half_vertical_right.white": "Flanc blanc invertit", "block.minecraft.banner.half_vertical_right.yellow": "Flanc groc invertit", "block.minecraft.banner.mojang.black": "Cosa negra", "block.minecraft.banner.mojang.blue": "Cosa blava", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON> cian", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON> grisa", "block.minecraft.banner.mojang.green": "Cosa verda", "block.minecraft.banner.mojang.light_blue": "Cosa blava clara", "block.minecraft.banner.mojang.light_gray": "Cosa grisa clara", "block.minecraft.banner.mojang.lime": "Cosa verda llima", "block.minecraft.banner.mojang.magenta": "Cosa magenta", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON> ta<PERSON>", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.mojang.purple": "Cosa morada", "block.minecraft.banner.mojang.red": "Cosa roja", "block.minecraft.banner.mojang.white": "Cosa blanca", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON> groga", "block.minecraft.banner.piglin.black": "<PERSON><PERSON> negre", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON> blau", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON>", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.piglin.green": "<PERSON><PERSON> verd", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON> blau clar", "block.minecraft.banner.piglin.light_gray": "Morro gris clar", "block.minecraft.banner.piglin.lime": "<PERSON>rro verd llima", "block.minecraft.banner.piglin.magenta": "Morro magenta", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON>", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.piglin.purple": "Morro morat", "block.minecraft.banner.piglin.red": "<PERSON><PERSON> roig", "block.minecraft.banner.piglin.white": "<PERSON><PERSON> blanc", "block.minecraft.banner.piglin.yellow": "<PERSON>rro groc", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON><PERSON> negre", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON><PERSON> blau", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON> verd", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON> blau clar", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON>e gris clar", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON> verd llima", "block.minecraft.banner.rhombus.magenta": "Rombe magenta", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON><PERSON> morat", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON><PERSON> roig", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON> groc", "block.minecraft.banner.skull.black": "Estampat de calavera negre", "block.minecraft.banner.skull.blue": "Estampat de calavera blau", "block.minecraft.banner.skull.brown": "Estampat de calavera marró", "block.minecraft.banner.skull.cyan": "Estampat de calavera cian", "block.minecraft.banner.skull.gray": "Estampat de calavera gris", "block.minecraft.banner.skull.green": "Estampat de calavera verd", "block.minecraft.banner.skull.light_blue": "Estampat de calavera blau clar", "block.minecraft.banner.skull.light_gray": "Estampat de calavera gris clar", "block.minecraft.banner.skull.lime": "Estampat de calavera verd llima", "block.minecraft.banner.skull.magenta": "Estampat de calavera magenta", "block.minecraft.banner.skull.orange": "Estampat de calavera taronja", "block.minecraft.banner.skull.pink": "Estampat de calavera rosa", "block.minecraft.banner.skull.purple": "Estampat de calavera morat", "block.minecraft.banner.skull.red": "Estampat de calavera roig", "block.minecraft.banner.skull.white": "Estampat de calavera blanc", "block.minecraft.banner.skull.yellow": "Estampat de calavera groc", "block.minecraft.banner.small_stripes.black": "Quadribarrada negra", "block.minecraft.banner.small_stripes.blue": "Quadribarrada blava", "block.minecraft.banner.small_stripes.brown": "Quadribarrada marró", "block.minecraft.banner.small_stripes.cyan": "Quadribarrada cian", "block.minecraft.banner.small_stripes.gray": "Quadribarrada grisa", "block.minecraft.banner.small_stripes.green": "Quadribarrada verda", "block.minecraft.banner.small_stripes.light_blue": "Quadribarrada blava clara", "block.minecraft.banner.small_stripes.light_gray": "Quadribarrada grisa clara", "block.minecraft.banner.small_stripes.lime": "Quadribarrada verda llima", "block.minecraft.banner.small_stripes.magenta": "Quadribarrada magenta", "block.minecraft.banner.small_stripes.orange": "Quadribarrada taronja", "block.minecraft.banner.small_stripes.pink": "Quadribarrada rosa", "block.minecraft.banner.small_stripes.purple": "Quadribarrada morada", "block.minecraft.banner.small_stripes.red": "Quadribarrada roja", "block.minecraft.banner.small_stripes.white": "Quadribarrada blanca", "block.minecraft.banner.small_stripes.yellow": "Quadribarrada groga", "block.minecraft.banner.square_bottom_left.black": "Cantó negre a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.blue": "Cantó blau a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.brown": "Cantó marró a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.cyan": "Cantó cian a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.gray": "Cantó gris a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.green": "Cantó verd a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.light_blue": "Cantó blau clar a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.light_gray": "Cantó gris clar a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.lime": "Cantó verd llima a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.magenta": "Cantó magenta a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.orange": "Cantó taronja a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.pink": "Cantó rosa a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.purple": "Cantó morat a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.red": "Cantó roig a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.white": "Cantó blanc a la part inferior esquerra", "block.minecraft.banner.square_bottom_left.yellow": "Cantó groc a la part inferior esquerra", "block.minecraft.banner.square_bottom_right.black": "Cantó negre a la part inferior dreta", "block.minecraft.banner.square_bottom_right.blue": "Cantó blau a la part inferior dreta", "block.minecraft.banner.square_bottom_right.brown": "Can<PERSON><PERSON> marró a la part inferior dreta", "block.minecraft.banner.square_bottom_right.cyan": "Cantó cian a la part inferior dreta", "block.minecraft.banner.square_bottom_right.gray": "Cantó gris a la part inferior dreta", "block.minecraft.banner.square_bottom_right.green": "Cantó verd a la part inferior dreta", "block.minecraft.banner.square_bottom_right.light_blue": "Cant<PERSON> blau clar a la part inferior dreta", "block.minecraft.banner.square_bottom_right.light_gray": "Cantó gris clar a la part inferior dreta", "block.minecraft.banner.square_bottom_right.lime": "Cantó verd llima a la part inferior dreta", "block.minecraft.banner.square_bottom_right.magenta": "Cantó magenta a la part inferior dreta", "block.minecraft.banner.square_bottom_right.orange": "Cantó taronja a la part inferior dreta", "block.minecraft.banner.square_bottom_right.pink": "Cantó rosa a la part inferior dreta", "block.minecraft.banner.square_bottom_right.purple": "Cantó morat a la part inferior dreta", "block.minecraft.banner.square_bottom_right.red": "Cantó roig a la part inferior dreta", "block.minecraft.banner.square_bottom_right.white": "Cantó blanc a la part inferior dreta", "block.minecraft.banner.square_bottom_right.yellow": "Cantó groc a la part inferior dreta", "block.minecraft.banner.square_top_left.black": "Cantó negre a la part superior esquerra", "block.minecraft.banner.square_top_left.blue": "Cantó blau a la part superior esquerra", "block.minecraft.banner.square_top_left.brown": "Cantó marró a la part superior esquerra", "block.minecraft.banner.square_top_left.cyan": "Cantó cian a la part superior esquerra", "block.minecraft.banner.square_top_left.gray": "Cantó gris a la part superior esquerra", "block.minecraft.banner.square_top_left.green": "Cantó verd a la part superior esquerra", "block.minecraft.banner.square_top_left.light_blue": "Cantó blau clar a la part superior esquerra", "block.minecraft.banner.square_top_left.light_gray": "Cantó gris clar a la part superior esquerra", "block.minecraft.banner.square_top_left.lime": "Cantó verd llima a la part superior esquerra", "block.minecraft.banner.square_top_left.magenta": "Cantó magenta a la part superior esquerra", "block.minecraft.banner.square_top_left.orange": "Cantó taronja a la part superior esquerra", "block.minecraft.banner.square_top_left.pink": "Cantó rosa a la part superior esquerra", "block.minecraft.banner.square_top_left.purple": "Cantó morat a la part superior esquerra", "block.minecraft.banner.square_top_left.red": "Cantó roig a la part superior esquerra", "block.minecraft.banner.square_top_left.white": "Cantó blanc a la part superior esquerra", "block.minecraft.banner.square_top_left.yellow": "Cantó groc a la part superior esquerra", "block.minecraft.banner.square_top_right.black": "Cantó negre a la part superior dreta", "block.minecraft.banner.square_top_right.blue": "Cantó blau a la part superior dreta", "block.minecraft.banner.square_top_right.brown": "Cantó marró a la part superior dreta", "block.minecraft.banner.square_top_right.cyan": "Cantó cian a la part superior dreta", "block.minecraft.banner.square_top_right.gray": "Cantó gris a la part superior dreta", "block.minecraft.banner.square_top_right.green": "Cantó verd a la part superior dreta", "block.minecraft.banner.square_top_right.light_blue": "Cantó blau clar a la part superior dreta", "block.minecraft.banner.square_top_right.light_gray": "Cantó gris clar a la part superior dreta", "block.minecraft.banner.square_top_right.lime": "Cantó verd llima a la part superior dreta", "block.minecraft.banner.square_top_right.magenta": "Cantó magenta a la part superior dreta", "block.minecraft.banner.square_top_right.orange": "Cantó taronja a la part superior dreta", "block.minecraft.banner.square_top_right.pink": "Cantó rosa a la part superior dreta", "block.minecraft.banner.square_top_right.purple": "Cantó morat a la part superior dreta", "block.minecraft.banner.square_top_right.red": "Cantó roig a la part superior dreta", "block.minecraft.banner.square_top_right.white": "Cantó blanc a la part superior dreta", "block.minecraft.banner.square_top_right.yellow": "Cantó groc a la part superior dreta", "block.minecraft.banner.straight_cross.black": "Creu negra", "block.minecraft.banner.straight_cross.blue": "Creu blava", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON><PERSON> cian", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON> grisa", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON> verda", "block.minecraft.banner.straight_cross.light_blue": "Creu blava clara", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON>u grisa clara", "block.minecraft.banner.straight_cross.lime": "<PERSON><PERSON><PERSON> verda llima", "block.minecraft.banner.straight_cross.magenta": "Creu magenta", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.straight_cross.purple": "Creu morada", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON><PERSON> roja", "block.minecraft.banner.straight_cross.white": "Creu blanca", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON><PERSON> groga", "block.minecraft.banner.stripe_bottom.black": "Base negra", "block.minecraft.banner.stripe_bottom.blue": "Base blava", "block.minecraft.banner.stripe_bottom.brown": "Base marró", "block.minecraft.banner.stripe_bottom.cyan": "Base cian", "block.minecraft.banner.stripe_bottom.gray": "Base grisa", "block.minecraft.banner.stripe_bottom.green": "Base verda", "block.minecraft.banner.stripe_bottom.light_blue": "Base blava clara", "block.minecraft.banner.stripe_bottom.light_gray": "Base grisa clara", "block.minecraft.banner.stripe_bottom.lime": "Base verda llima", "block.minecraft.banner.stripe_bottom.magenta": "Base magenta", "block.minecraft.banner.stripe_bottom.orange": "Base taronja", "block.minecraft.banner.stripe_bottom.pink": "Base rosa", "block.minecraft.banner.stripe_bottom.purple": "Base morada", "block.minecraft.banner.stripe_bottom.red": "Base roja", "block.minecraft.banner.stripe_bottom.white": "Base blanca", "block.minecraft.banner.stripe_bottom.yellow": "Base groga", "block.minecraft.banner.stripe_center.black": "Franja vertical central negra", "block.minecraft.banner.stripe_center.blue": "Franja vertical central blava", "block.minecraft.banner.stripe_center.brown": "Franja vertical central marró", "block.minecraft.banner.stripe_center.cyan": "Franja vertical central cian", "block.minecraft.banner.stripe_center.gray": "Franja vertical central grisa", "block.minecraft.banner.stripe_center.green": "Franja vertical central verda", "block.minecraft.banner.stripe_center.light_blue": "Franja vertical central blava clara", "block.minecraft.banner.stripe_center.light_gray": "Franja vertical central grisa clara", "block.minecraft.banner.stripe_center.lime": "Franja vertical central verda llima", "block.minecraft.banner.stripe_center.magenta": "Franja vertical central magenta", "block.minecraft.banner.stripe_center.orange": "Franja vertical central taronja", "block.minecraft.banner.stripe_center.pink": "Franja vertical central rosa", "block.minecraft.banner.stripe_center.purple": "Franja vertical central morada", "block.minecraft.banner.stripe_center.red": "Franja vertical central roja", "block.minecraft.banner.stripe_center.white": "Franja vertical central blanca", "block.minecraft.banner.stripe_center.yellow": "Franja vertical central groga", "block.minecraft.banner.stripe_downleft.black": "Banda negra a dret", "block.minecraft.banner.stripe_downleft.blue": "Banda blava a dret", "block.minecraft.banner.stripe_downleft.brown": "Banda marró a dret", "block.minecraft.banner.stripe_downleft.cyan": "Banda cian a dret", "block.minecraft.banner.stripe_downleft.gray": "Banda grisa a dret", "block.minecraft.banner.stripe_downleft.green": "Banda verda a dret", "block.minecraft.banner.stripe_downleft.light_blue": "Banda blava clara a dret", "block.minecraft.banner.stripe_downleft.light_gray": "Banda grisa clara a dret", "block.minecraft.banner.stripe_downleft.lime": "Banda verda llima a dret", "block.minecraft.banner.stripe_downleft.magenta": "Banda magenta a dret", "block.minecraft.banner.stripe_downleft.orange": "Banda taronja a dret", "block.minecraft.banner.stripe_downleft.pink": "Banda rosa a dret", "block.minecraft.banner.stripe_downleft.purple": "Banda morada a dret", "block.minecraft.banner.stripe_downleft.red": "Banda roja a dret", "block.minecraft.banner.stripe_downleft.white": "Banda blanca a dret", "block.minecraft.banner.stripe_downleft.yellow": "Banda groga a dret", "block.minecraft.banner.stripe_downright.black": "Banda negra", "block.minecraft.banner.stripe_downright.blue": "Banda blava", "block.minecraft.banner.stripe_downright.brown": "Banda marró", "block.minecraft.banner.stripe_downright.cyan": "Banda cian", "block.minecraft.banner.stripe_downright.gray": "Banda grisa", "block.minecraft.banner.stripe_downright.green": "Banda verda", "block.minecraft.banner.stripe_downright.light_blue": "Banda blava clara", "block.minecraft.banner.stripe_downright.light_gray": "Banda grisa clara", "block.minecraft.banner.stripe_downright.lime": "Banda verda llima", "block.minecraft.banner.stripe_downright.magenta": "Banda magenta", "block.minecraft.banner.stripe_downright.orange": "Banda taronja", "block.minecraft.banner.stripe_downright.pink": "Banda rosa", "block.minecraft.banner.stripe_downright.purple": "Banda morada", "block.minecraft.banner.stripe_downright.red": "Banda roja", "block.minecraft.banner.stripe_downright.white": "Banda blanca", "block.minecraft.banner.stripe_downright.yellow": "Banda groga", "block.minecraft.banner.stripe_left.black": "Franja vertical negra a la part esquerra", "block.minecraft.banner.stripe_left.blue": "Franja vertical blava a la part esquerra", "block.minecraft.banner.stripe_left.brown": "Franja vertical marró a la part esquerra", "block.minecraft.banner.stripe_left.cyan": "Franja vertical cian a la part esquerra", "block.minecraft.banner.stripe_left.gray": "Franja vertical grisa a la part esquerra", "block.minecraft.banner.stripe_left.green": "Franja vertical verda a la part esquerra", "block.minecraft.banner.stripe_left.light_blue": "Franja vertical blava clara a la part esquerra", "block.minecraft.banner.stripe_left.light_gray": "Franja vertical grisa clara a la part esquerra", "block.minecraft.banner.stripe_left.lime": "Franja vertical verda llima a la part esquerra", "block.minecraft.banner.stripe_left.magenta": "Franja vertical magenta a la part esquerra", "block.minecraft.banner.stripe_left.orange": "Franja vertical taronja a la part esquerra", "block.minecraft.banner.stripe_left.pink": "Franja vertical rosa a la part esquerra", "block.minecraft.banner.stripe_left.purple": "Franja vertical morada a la part esquerra", "block.minecraft.banner.stripe_left.red": "Franja vertical roja a la part esquerra", "block.minecraft.banner.stripe_left.white": "Franja vertical blanca a la part esquerra", "block.minecraft.banner.stripe_left.yellow": "Franja vertical groga a la part esquerra", "block.minecraft.banner.stripe_middle.black": "Franja horitzontal central negra", "block.minecraft.banner.stripe_middle.blue": "Franja horitzontal central blava", "block.minecraft.banner.stripe_middle.brown": "Franja horitzontal central marró", "block.minecraft.banner.stripe_middle.cyan": "Franja horitzontal central cian", "block.minecraft.banner.stripe_middle.gray": "Franja horitzontal central grisa", "block.minecraft.banner.stripe_middle.green": "Franja horitzontal central verda", "block.minecraft.banner.stripe_middle.light_blue": "Franja horitzontal central blava clara", "block.minecraft.banner.stripe_middle.light_gray": "Franja horitzontal central grisa clara", "block.minecraft.banner.stripe_middle.lime": "Franja horitzontal central verda llima", "block.minecraft.banner.stripe_middle.magenta": "Franja horitzontal central magenta", "block.minecraft.banner.stripe_middle.orange": "Franja horitzontal central taronja", "block.minecraft.banner.stripe_middle.pink": "Franja horitzontal central rosa", "block.minecraft.banner.stripe_middle.purple": "Franja horitzontal central morada", "block.minecraft.banner.stripe_middle.red": "Franja horitzontal central roja", "block.minecraft.banner.stripe_middle.white": "Franja horitzontal central blanca", "block.minecraft.banner.stripe_middle.yellow": "Franja horitzontal central groga", "block.minecraft.banner.stripe_right.black": "Franja vertical negra a la part dreta", "block.minecraft.banner.stripe_right.blue": "Franja vertical blava a la part dreta", "block.minecraft.banner.stripe_right.brown": "Franja vertical marró a la part dreta", "block.minecraft.banner.stripe_right.cyan": "Franja vertical cian a la part dreta", "block.minecraft.banner.stripe_right.gray": "Franja vertical grisa a la part dreta", "block.minecraft.banner.stripe_right.green": "Franja vertical verda a la part dreta", "block.minecraft.banner.stripe_right.light_blue": "Franja vertical blava clara a la part dreta", "block.minecraft.banner.stripe_right.light_gray": "Franja vertical grisa clara a la part dreta", "block.minecraft.banner.stripe_right.lime": "Franja vertical verda llima a la part dreta", "block.minecraft.banner.stripe_right.magenta": "Franja vertical magenta a la part dreta", "block.minecraft.banner.stripe_right.orange": "Franja vertical taronja a la part dreta", "block.minecraft.banner.stripe_right.pink": "Franja vertical rosa a la part dreta", "block.minecraft.banner.stripe_right.purple": "Franja vertical morada a la part dreta", "block.minecraft.banner.stripe_right.red": "Franja vertical roja a la part dreta", "block.minecraft.banner.stripe_right.white": "Franja vertical blanca a la part dreta", "block.minecraft.banner.stripe_right.yellow": "Franja vertical groga a la part dreta", "block.minecraft.banner.stripe_top.black": "Cap negre", "block.minecraft.banner.stripe_top.blue": "Cap blau", "block.minecraft.banner.stripe_top.brown": "Cap marró", "block.minecraft.banner.stripe_top.cyan": "Cap cian", "block.minecraft.banner.stripe_top.gray": "Cap gris", "block.minecraft.banner.stripe_top.green": "Cap verd", "block.minecraft.banner.stripe_top.light_blue": "Cap blau clar", "block.minecraft.banner.stripe_top.light_gray": "Cap gris clar", "block.minecraft.banner.stripe_top.lime": "Cap verd llima", "block.minecraft.banner.stripe_top.magenta": "Cap magenta", "block.minecraft.banner.stripe_top.orange": "Cap taronja", "block.minecraft.banner.stripe_top.pink": "Cap rosa", "block.minecraft.banner.stripe_top.purple": "Cap morat", "block.minecraft.banner.stripe_top.red": "Cap roig", "block.minecraft.banner.stripe_top.white": "Cap blanc", "block.minecraft.banner.stripe_top.yellow": "Cap groc", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON> negre", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON> blau", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "<PERSON><PERSON>ian", "block.minecraft.banner.triangle_bottom.gray": "<PERSON>u gris", "block.minecraft.banner.triangle_bottom.green": "<PERSON>u verd", "block.minecraft.banner.triangle_bottom.light_blue": "<PERSON>u blau clar", "block.minecraft.banner.triangle_bottom.light_gray": "Bou gris clar", "block.minecraft.banner.triangle_bottom.lime": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.magenta": "Bou magenta", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON> morat", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON> roig", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON> blanc", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON> groc", "block.minecraft.banner.triangle_top.black": "Bou invertit negre", "block.minecraft.banner.triangle_top.blue": "Bou invertit blau", "block.minecraft.banner.triangle_top.brown": "Bou invertit marró", "block.minecraft.banner.triangle_top.cyan": "Bou invertit cian", "block.minecraft.banner.triangle_top.gray": "Bou invertit gris", "block.minecraft.banner.triangle_top.green": "Bou invertit verd", "block.minecraft.banner.triangle_top.light_blue": "Bou invertit blau clar", "block.minecraft.banner.triangle_top.light_gray": "Bou invertit gris clar", "block.minecraft.banner.triangle_top.lime": "Bou invertit verd llima", "block.minecraft.banner.triangle_top.magenta": "Bou invertit magenta", "block.minecraft.banner.triangle_top.orange": "Bou invertit taronja", "block.minecraft.banner.triangle_top.pink": "Bou invertit rosa", "block.minecraft.banner.triangle_top.purple": "Bou invertit morat", "block.minecraft.banner.triangle_top.red": "Bou invertit roig", "block.minecraft.banner.triangle_top.white": "Bou invertit blanc", "block.minecraft.banner.triangle_top.yellow": "Bou invertit groc", "block.minecraft.banner.triangles_bottom.black": "Base dentada negra", "block.minecraft.banner.triangles_bottom.blue": "Base dentada blava", "block.minecraft.banner.triangles_bottom.brown": "Base dentada marró", "block.minecraft.banner.triangles_bottom.cyan": "Base dentada cian", "block.minecraft.banner.triangles_bottom.gray": "Base dentada grisa", "block.minecraft.banner.triangles_bottom.green": "Base dentada verda", "block.minecraft.banner.triangles_bottom.light_blue": "Base dentada blava clara", "block.minecraft.banner.triangles_bottom.light_gray": "Base dentada grisa clara", "block.minecraft.banner.triangles_bottom.lime": "Base dentada verda llima", "block.minecraft.banner.triangles_bottom.magenta": "Base dentada magenta", "block.minecraft.banner.triangles_bottom.orange": "Base dentada taronja", "block.minecraft.banner.triangles_bottom.pink": "Base dentada rosa", "block.minecraft.banner.triangles_bottom.purple": "Base dentada morada", "block.minecraft.banner.triangles_bottom.red": "Base dentada roja", "block.minecraft.banner.triangles_bottom.white": "Base dentada blanca", "block.minecraft.banner.triangles_bottom.yellow": "Base dentada groga", "block.minecraft.banner.triangles_top.black": "Cap dentat negre", "block.minecraft.banner.triangles_top.blue": "Cap dentat blau", "block.minecraft.banner.triangles_top.brown": "Cap dentat marró", "block.minecraft.banner.triangles_top.cyan": "Cap dentat cian", "block.minecraft.banner.triangles_top.gray": "Cap dentat gris", "block.minecraft.banner.triangles_top.green": "Cap dentat verd", "block.minecraft.banner.triangles_top.light_blue": "Cap dentat blau clar", "block.minecraft.banner.triangles_top.light_gray": "Cap dentat gris clar", "block.minecraft.banner.triangles_top.lime": "Cap dentat verd llima", "block.minecraft.banner.triangles_top.magenta": "Cap dentat magenta", "block.minecraft.banner.triangles_top.orange": "Cap dentat taronja", "block.minecraft.banner.triangles_top.pink": "Cap dentat rosa", "block.minecraft.banner.triangles_top.purple": "Cap dentat morat", "block.minecraft.banner.triangles_top.red": "Cap dentat roig", "block.minecraft.banner.triangles_top.white": "Cap dentat blanc", "block.minecraft.banner.triangles_top.yellow": "Cap dentat groc", "block.minecraft.barrel": "Barril", "block.minecraft.barrier": "Barr<PERSON>", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "Far màgic", "block.minecraft.beacon.primary": "<PERSON><PERSON> primari", "block.minecraft.beacon.secondary": "<PERSON><PERSON> secundari", "block.minecraft.bed.no_sleep": "Només pots dormir de nit o durant tronades", "block.minecraft.bed.not_safe": "No hauries de dormir ara: hi ha monstres per ací prop", "block.minecraft.bed.obstructed": "El llit està obstruït", "block.minecraft.bed.occupied": "Este llit està ocupat", "block.minecraft.bed.too_far_away": "No hauries de dormir ara: el llit està massa lluny", "block.minecraft.bedrock": "Roca base", "block.minecraft.bee_nest": "<PERSON><PERSON><PERSON>", "block.minecraft.beehive": "Buc", "block.minecraft.beetroots": "Remolatxes", "block.minecraft.bell": "Campana", "block.minecraft.big_dripleaf": "Fulla relliscosa gran", "block.minecraft.big_dripleaf_stem": "Tija de fulla relliscosa gran", "block.minecraft.birch_button": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_door": "Porta de bedoll", "block.minecraft.birch_fence": "Tanca de bedoll", "block.minecraft.birch_fence_gate": "Porta de tanca de bedoll", "block.minecraft.birch_hanging_sign": "<PERSON><PERSON><PERSON> de <PERSON> pen<PERSON>t", "block.minecraft.birch_leaves": "<PERSON><PERSON> de <PERSON>", "block.minecraft.birch_log": "Tronc de bedoll", "block.minecraft.birch_planks": "<PERSON><PERSON>", "block.minecraft.birch_pressure_plate": "Placa de pressió de bedoll", "block.minecraft.birch_sapling": "Esqueix de <PERSON>", "block.minecraft.birch_sign": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.birch_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_wall_hanging_sign": "<PERSON><PERSON><PERSON> de bedoll penjant de paret", "block.minecraft.birch_wall_sign": "<PERSON><PERSON><PERSON> de bedoll de paret", "block.minecraft.birch_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.black_banner": "Estandard negre", "block.minecraft.black_bed": "<PERSON><PERSON> negre", "block.minecraft.black_candle": "Ciri negre", "block.minecraft.black_candle_cake": "Pastís amb ciri negre", "block.minecraft.black_carpet": "Estora negra", "block.minecraft.black_concrete": "Formigó negre", "block.minecraft.black_concrete_powder": "Pols de formigó negre", "block.minecraft.black_glazed_terracotta": "Terracota glacejada negra", "block.minecraft.black_shulker_box": "<PERSON><PERSON><PERSON> de shulker negra", "block.minecraft.black_stained_glass": "<PERSON><PERSON><PERSON><PERSON> negra", "block.minecraft.black_stained_glass_pane": "Panell de vidriera negre", "block.minecraft.black_terracotta": "Terracota negra", "block.minecraft.black_wool": "<PERSON><PERSON> negra", "block.minecraft.blackstone": "Roca negra", "block.minecraft.blackstone_slab": "Llosa de roca negra", "block.minecraft.blackstone_stairs": "Escales de roca negra", "block.minecraft.blackstone_wall": "Mur de roca negra", "block.minecraft.blast_furnace": "Alt forn", "block.minecraft.blue_banner": "Estandard blau", "block.minecraft.blue_bed": "<PERSON><PERSON> blau", "block.minecraft.blue_candle": "<PERSON><PERSON> blau", "block.minecraft.blue_candle_cake": "Pastís amb ciri blau", "block.minecraft.blue_carpet": "Estora blava", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON> blau", "block.minecraft.blue_concrete_powder": "Pols de formigó blau", "block.minecraft.blue_glazed_terracotta": "Terracota glacejada blava", "block.minecraft.blue_ice": "<PERSON><PERSON> blau", "block.minecraft.blue_orchid": "Orquídia blava", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON>ker blava", "block.minecraft.blue_stained_glass": "<PERSON><PERSON><PERSON><PERSON> blava", "block.minecraft.blue_stained_glass_pane": "Panell de vidriera blau", "block.minecraft.blue_terracotta": "Terracota blava", "block.minecraft.blue_wool": "<PERSON><PERSON> blava", "block.minecraft.bone_block": "Bloc d'os", "block.minecraft.bookshelf": "Llibreria", "block.minecraft.brain_coral": "<PERSON><PERSON>", "block.minecraft.brain_coral_block": "Bloc de corall cerebral", "block.minecraft.brain_coral_fan": "Gorgònia cerebral", "block.minecraft.brain_coral_wall_fan": "Gorgònia cerebral de paret", "block.minecraft.brewing_stand": "Altar de pocions", "block.minecraft.brick_slab": "Llosa de rajoles", "block.minecraft.brick_stairs": "<PERSON><PERSON><PERSON> raj<PERSON>", "block.minecraft.brick_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "Estandard marró", "block.minecraft.brown_bed": "<PERSON><PERSON>rr<PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON> marró", "block.minecraft.brown_candle_cake": "Pastís amb ciri marró", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "Pols de formigó marró", "block.minecraft.brown_glazed_terracotta": "Terracota glacejada marró", "block.minecraft.brown_mushroom": "Xamp<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.brown_mushroom_block": "Bloc de xampinyó marró", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON> marró", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.brown_stained_glass_pane": "Panell de vidriera marró", "block.minecraft.brown_terracotta": "Terracota marró", "block.minecraft.brown_wool": "<PERSON><PERSON>", "block.minecraft.bubble_column": "Columna de bombolles", "block.minecraft.bubble_coral": "<PERSON><PERSON> bomboll<PERSON>", "block.minecraft.bubble_coral_block": "Bloc de corall de bombolles", "block.minecraft.bubble_coral_fan": "Gorgònia de bombolles", "block.minecraft.bubble_coral_wall_fan": "Gorgònia de bombolles de paret", "block.minecraft.budding_amethyst": "Ametista incipient", "block.minecraft.bush": "Arbust", "block.minecraft.cactus": "Cactus", "block.minecraft.cactus_flower": "Flor de cactus", "block.minecraft.cake": "Pastís", "block.minecraft.calcite": "Calcita", "block.minecraft.calibrated_sculk_sensor": "Sensor de sculk calibrat", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>", "block.minecraft.candle": "Ciri", "block.minecraft.candle_cake": "Ciri de pastís", "block.minecraft.carrots": "<PERSON><PERSON>", "block.minecraft.cartography_table": "Taula de cartografia", "block.minecraft.carved_pumpkin": "Carabassa <PERSON>ada", "block.minecraft.cauldron": "<PERSON><PERSON>", "block.minecraft.cave_air": "Aire de cova", "block.minecraft.cave_vines": "Lianes de cova", "block.minecraft.cave_vines_plant": "Planta de lianes de cova", "block.minecraft.chain": "Cadena", "block.minecraft.chain_command_block": "Bloc de comandaments encadenats", "block.minecraft.cherry_button": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_door": "Porta de cirerer", "block.minecraft.cherry_fence": "Tanca de cirerer", "block.minecraft.cherry_fence_gate": "Porta de tanca de cirerer", "block.minecraft.cherry_hanging_sign": "<PERSON><PERSON><PERSON> <PERSON> cire<PERSON> pen<PERSON>t", "block.minecraft.cherry_leaves": "<PERSON>es de cirerer", "block.minecraft.cherry_log": "Tronc de cirerer", "block.minecraft.cherry_planks": "Taulons de cirerer", "block.minecraft.cherry_pressure_plate": "Placa de pressió de cirerer", "block.minecraft.cherry_sapling": "Esqueix de cirerer", "block.minecraft.cherry_sign": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.cherry_slab": "<PERSON>los<PERSON> de c<PERSON>rer", "block.minecraft.cherry_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_trapdoor": "<PERSON>rap<PERSON> de cirerer", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON> de cirerer penjant de paret", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON><PERSON> de cirerer de paret", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.chest": "Cofre", "block.minecraft.chipped_anvil": "Enclusa esquerdada", "block.minecraft.chiseled_bookshelf": "Llibreria cisellada", "block.minecraft.chiseled_copper": "Coure cisellat", "block.minecraft.chiseled_deepslate": "Pissarra cisellada", "block.minecraft.chiseled_nether_bricks": "Raj<PERSON>s del Nether cisellades", "block.minecraft.chiseled_polished_blackstone": "Roca negra polida cisellada", "block.minecraft.chiseled_quartz_block": "Bloc de quars cisellat", "block.minecraft.chiseled_red_sandstone": "Roca arenosa roja cisellada", "block.minecraft.chiseled_resin_bricks": "<PERSON><PERSON><PERSON> de resina cisellades", "block.minecraft.chiseled_sandstone": "Roca arenosa cisellada", "block.minecraft.chiseled_stone_bricks": "Rajoles de roca cisellades", "block.minecraft.chiseled_tuff": "Tova volcànica cisellada", "block.minecraft.chiseled_tuff_bricks": "Rajoles de tova volcànica cisellada", "block.minecraft.chorus_flower": "Flor de coral", "block.minecraft.chorus_plant": "Coral", "block.minecraft.clay": "<PERSON><PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Florull tancat", "block.minecraft.coal_block": "Bloc de carbó", "block.minecraft.coal_ore": "<PERSON><PERSON> <PERSON>", "block.minecraft.coarse_dirt": "Terra estèril", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.cobbled_deepslate_slab": "<PERSON>los<PERSON> d'em<PERSON>", "block.minecraft.cobbled_deepslate_stairs": "<PERSON><PERSON><PERSON> d'emped<PERSON> de <PERSON>", "block.minecraft.cobbled_deepslate_wall": "<PERSON><PERSON> d'<PERSON><PERSON><PERSON> de <PERSON>", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "<PERSON><PERSON><PERSON>'em<PERSON>", "block.minecraft.cobblestone_stairs": "<PERSON><PERSON><PERSON> d'empedrat", "block.minecraft.cobblestone_wall": "<PERSON><PERSON> d'em<PERSON><PERSON>", "block.minecraft.cobweb": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cocoa": "Cacau", "block.minecraft.command_block": "Bloc de comandaments", "block.minecraft.comparator": "Comparador de redstone", "block.minecraft.composter": "Compostador", "block.minecraft.conduit": "Canalitzador", "block.minecraft.copper_block": "Bloc de coure", "block.minecraft.copper_bulb": "Làmpada de coure", "block.minecraft.copper_door": "Porta de coure", "block.minecraft.copper_grate": "Reixeta de coure", "block.minecraft.copper_ore": "Mena de coure", "block.minecraft.copper_trapdoor": "Trapa de coure", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Raj<PERSON>s de pissarra esquerdades", "block.minecraft.cracked_deepslate_tiles": "<PERSON><PERSON><PERSON> de pissarra esquerdats", "block.minecraft.cracked_nether_bricks": "Rajoles del Nether esquerdades", "block.minecraft.cracked_polished_blackstone_bricks": "Rajoles de roca negra polida esquerdades", "block.minecraft.cracked_stone_bricks": "Rajoles de roca esquerdades", "block.minecraft.crafter": "Fabricador", "block.minecraft.crafting_table": "Taula de manufactures", "block.minecraft.creaking_heart": "<PERSON><PERSON> <PERSON> crepitant", "block.minecraft.creeper_head": "Cap de creeper", "block.minecraft.creeper_wall_head": "Cap de creeper de paret", "block.minecraft.crimson_button": "Botó de fusta carmesina", "block.minecraft.crimson_door": "Porta de fusta carmesina", "block.minecraft.crimson_fence": "Tanca carmesí", "block.minecraft.crimson_fence_gate": "Porta de tanca carmesí", "block.minecraft.crimson_fungus": "<PERSON><PERSON> car<PERSON>", "block.minecraft.crimson_hanging_sign": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.crimson_hyphae": "<PERSON><PERSON>í", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_planks": "Taulons de fusta carmesí", "block.minecraft.crimson_pressure_plate": "Placa de pressió carmesí", "block.minecraft.crimson_roots": "Arrels carmesines", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_stem": "<PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON><PERSON> car<PERSON><PERSON> pen<PERSON> de paret", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON> car<PERSON> de paret", "block.minecraft.crying_obsidian": "Obsidiana plorosa", "block.minecraft.cut_copper": "Coure tallat", "block.minecraft.cut_copper_slab": "Llosa de coure tallat", "block.minecraft.cut_copper_stairs": "Escales de coure tallat", "block.minecraft.cut_red_sandstone": "Roca arenosa roja tallada", "block.minecraft.cut_red_sandstone_slab": "Llosa de roca arenosa roja tallada", "block.minecraft.cut_sandstone": "Roca arenosa tallada", "block.minecraft.cut_sandstone_slab": "Llosa de roca arenosa tallada", "block.minecraft.cyan_banner": "Estandard cian", "block.minecraft.cyan_bed": "<PERSON><PERSON> cian", "block.minecraft.cyan_candle": "<PERSON><PERSON> cian", "block.minecraft.cyan_candle_cake": "Pastís amb ciri cian", "block.minecraft.cyan_carpet": "Estora cian", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_concrete_powder": "Pols de formigó cian", "block.minecraft.cyan_glazed_terracotta": "Terracota glacejada cian", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON><PERSON> cian", "block.minecraft.cyan_stained_glass_pane": "Panell de vidriera cian", "block.minecraft.cyan_terracotta": "Terracota cian", "block.minecraft.cyan_wool": "<PERSON><PERSON>", "block.minecraft.damaged_anvil": "<PERSON><PERSON><PERSON><PERSON> vella", "block.minecraft.dandelion": "<PERSON>t de ll<PERSON>", "block.minecraft.dark_oak_button": "Botó de roure fosc", "block.minecraft.dark_oak_door": "Porta roure fosc", "block.minecraft.dark_oak_fence": "Tanca de roure fosc", "block.minecraft.dark_oak_fence_gate": "Porta de tanca de roure fosc", "block.minecraft.dark_oak_hanging_sign": "<PERSON>tell de roure fosc pen<PERSON>t", "block.minecraft.dark_oak_leaves": "Fulles de roure fosc", "block.minecraft.dark_oak_log": "Tronc de roure fosc", "block.minecraft.dark_oak_planks": "Taulons de roure fosc", "block.minecraft.dark_oak_pressure_plate": "Placa de pressió de roure fosc", "block.minecraft.dark_oak_sapling": "Esqueix de roure fosc", "block.minecraft.dark_oak_sign": "Cartell de roure fosc", "block.minecraft.dark_oak_slab": "Llosa de roure fosc", "block.minecraft.dark_oak_stairs": "E<PERSON>les de roure fosc", "block.minecraft.dark_oak_trapdoor": "Trapa de roure fosc", "block.minecraft.dark_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> de roure fosc penjant de paret", "block.minecraft.dark_oak_wall_sign": "Cartell de roure fosc de paret", "block.minecraft.dark_oak_wood": "<PERSON><PERSON><PERSON> de roure fosc", "block.minecraft.dark_prismarine": "Prismarina obscura", "block.minecraft.dark_prismarine_slab": "Llosa de prismarina obscura", "block.minecraft.dark_prismarine_stairs": "Escales de prismarina obscura", "block.minecraft.daylight_detector": "Sensor de llum solar", "block.minecraft.dead_brain_coral": "Corall cerebral mort", "block.minecraft.dead_brain_coral_block": "Bloc de corall cerebral mort", "block.minecraft.dead_brain_coral_fan": "Gorgònia cerebral morta", "block.minecraft.dead_brain_coral_wall_fan": "Gorgònia cerebral de paret morta", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON> de bombolles mort", "block.minecraft.dead_bubble_coral_block": "Bloc de corall de bombolles mort", "block.minecraft.dead_bubble_coral_fan": "Gorgònia de bombolles morta", "block.minecraft.dead_bubble_coral_wall_fan": "Gorgònia de bombolles de paret morta", "block.minecraft.dead_bush": "Arbust mort", "block.minecraft.dead_fire_coral": "<PERSON>l de foc mort", "block.minecraft.dead_fire_coral_block": "Bloc de corall de foc mort", "block.minecraft.dead_fire_coral_fan": "Gorgònia de foc morta", "block.minecraft.dead_fire_coral_wall_fan": "Gorgònia de foc de paret morta", "block.minecraft.dead_horn_coral": "<PERSON><PERSON> de banya mort", "block.minecraft.dead_horn_coral_block": "Bloc de corall de banya mort", "block.minecraft.dead_horn_coral_fan": "Gorgònia de banya morta", "block.minecraft.dead_horn_coral_wall_fan": "Gorgònia de banya de paret morta", "block.minecraft.dead_tube_coral": "Corall de tub mort", "block.minecraft.dead_tube_coral_block": "Bloc de corall de tub mort", "block.minecraft.dead_tube_coral_fan": "Gorgònia de tub morta", "block.minecraft.dead_tube_coral_wall_fan": "Gorgònia de tub de paret morta", "block.minecraft.decorated_pot": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate": "Pissarra", "block.minecraft.deepslate_brick_slab": "Llosa de rajoles de <PERSON>arra", "block.minecraft.deepslate_brick_stairs": "<PERSON><PERSON><PERSON> de rajoles de <PERSON>", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON> <PERSON> raj<PERSON> de <PERSON>", "block.minecraft.deepslate_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_coal_ore": "Mena de car<PERSON>ó <PERSON>", "block.minecraft.deepslate_copper_ore": "Mena de coure pissarrosa", "block.minecraft.deepslate_diamond_ore": "<PERSON><PERSON> <PERSON> diamant <PERSON>", "block.minecraft.deepslate_emerald_ore": "<PERSON>a de ma<PERSON>a", "block.minecraft.deepslate_gold_ore": "Mena d'or pissarrosa", "block.minecraft.deepslate_iron_ore": "Mena de ferro <PERSON>a", "block.minecraft.deepslate_lapis_ore": "Mena de lapislàtzuli pissar<PERSON>a", "block.minecraft.deepslate_redstone_ore": "Mena de redstone pissarrosa", "block.minecraft.deepslate_tile_slab": "Llosa de taulells de pissarra", "block.minecraft.deepslate_tile_stairs": "<PERSON><PERSON><PERSON> de taulells de pissarra", "block.minecraft.deepslate_tile_wall": "<PERSON><PERSON> <PERSON> taule<PERSON> de <PERSON>", "block.minecraft.deepslate_tiles": "<PERSON><PERSON><PERSON>", "block.minecraft.detector_rail": "Via detectora", "block.minecraft.diamond_block": "Bloc de diamant", "block.minecraft.diamond_ore": "<PERSON><PERSON> <PERSON>", "block.minecraft.diorite": "Di<PERSON>ta", "block.minecraft.diorite_slab": "Llosa de diorita", "block.minecraft.diorite_stairs": "Escales de diorita", "block.minecraft.diorite_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.dirt": "Terra", "block.minecraft.dirt_path": "Cam<PERSON> de <PERSON>rra", "block.minecraft.dispenser": "Dispensador", "block.minecraft.dragon_egg": "Ou del drac de la Fi", "block.minecraft.dragon_head": "Cap del drac", "block.minecraft.dragon_wall_head": "Cap del drac de paret", "block.minecraft.dried_ghast": "Ghast sec", "block.minecraft.dried_kelp_block": "Bloc d'algues seques", "block.minecraft.dripstone_block": "Bloc d'espeleotema", "block.minecraft.dropper": "Subministrador", "block.minecraft.emerald_block": "Bloc de maragda", "block.minecraft.emerald_ore": "Mena de <PERSON>", "block.minecraft.enchanting_table": "<PERSON><PERSON>'en<PERSON>", "block.minecraft.end_gateway": "Porta de l'End", "block.minecraft.end_portal": "Portal a l'End", "block.minecraft.end_portal_frame": "Marc del portal a l'End", "block.minecraft.end_rod": "Vara de l'End", "block.minecraft.end_stone": "Roca de l'End", "block.minecraft.end_stone_brick_slab": "Llosa de rajoles de roca de l'End", "block.minecraft.end_stone_brick_stairs": "Escales de rajoles de roca de l'End", "block.minecraft.end_stone_brick_wall": "Mur de rajoles de roca de l'End", "block.minecraft.end_stone_bricks": "Rajoles de roca de l'End", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Coure exposat cisellat", "block.minecraft.exposed_copper": "Coure exposat", "block.minecraft.exposed_copper_bulb": "Làmpada de coure exposat", "block.minecraft.exposed_copper_door": "Porta de coure exposat", "block.minecraft.exposed_copper_grate": "Reixeta de coure exposat", "block.minecraft.exposed_copper_trapdoor": "Trapa de coure exposat", "block.minecraft.exposed_cut_copper": "Coure tallat exposat", "block.minecraft.exposed_cut_copper_slab": "Llosa de coure tallat exposat", "block.minecraft.exposed_cut_copper_stairs": "Escales de coure tallat exposat", "block.minecraft.farmland": "Terra arada", "block.minecraft.fern": "Falaguera", "block.minecraft.fire": "Foc", "block.minecraft.fire_coral": "Corall de foc", "block.minecraft.fire_coral_block": "Bloc de corall de foc", "block.minecraft.fire_coral_fan": "Gorgònia de foc", "block.minecraft.fire_coral_wall_fan": "Gorgònia de foc de paret", "block.minecraft.firefly_bush": "Arbust amb ll<PERSON>nes", "block.minecraft.fletching_table": "<PERSON>er de fletxes", "block.minecraft.flower_pot": "Test", "block.minecraft.flowering_azalea": "Azalea florent", "block.minecraft.flowering_azalea_leaves": "Fulles d'azalea florent", "block.minecraft.frogspawn": "Ous de granota", "block.minecraft.frosted_ice": "<PERSON><PERSON>", "block.minecraft.furnace": "Forn", "block.minecraft.gilded_blackstone": "Roca negra daurada", "block.minecraft.glass": "<PERSON><PERSON><PERSON>", "block.minecraft.glass_pane": "Panell de vidre", "block.minecraft.glow_lichen": "Liquen fosforescent", "block.minecraft.glowstone": "Roca brillant", "block.minecraft.gold_block": "Bloc d'or", "block.minecraft.gold_ore": "<PERSON>a d'or", "block.minecraft.granite": "Granit", "block.minecraft.granite_slab": "Llosa de g<PERSON>", "block.minecraft.granite_stairs": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.granite_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.grass": "<PERSON><PERSON>", "block.minecraft.grass_block": "Bloc d'herba", "block.minecraft.gravel": "Grava", "block.minecraft.gray_banner": "Estandard gris", "block.minecraft.gray_bed": "<PERSON><PERSON> gris", "block.minecraft.gray_candle": "Ciri gris", "block.minecraft.gray_candle_cake": "Pastís amb ciri gris", "block.minecraft.gray_carpet": "Estora grisa", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON> gris", "block.minecraft.gray_concrete_powder": "Pols de formigó gris", "block.minecraft.gray_glazed_terracotta": "Terracota glacejada grisa", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON> grisa", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON><PERSON> grisa", "block.minecraft.gray_stained_glass_pane": "Panell de vidriera gris", "block.minecraft.gray_terracotta": "Terracota grisa", "block.minecraft.gray_wool": "<PERSON><PERSON>", "block.minecraft.green_banner": "Estandard verd", "block.minecraft.green_bed": "Llit verd", "block.minecraft.green_candle": "<PERSON>iri verd", "block.minecraft.green_candle_cake": "Pastís amb ciri verd", "block.minecraft.green_carpet": "Estora verda", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON> verd", "block.minecraft.green_concrete_powder": "Pols de formigó verd", "block.minecraft.green_glazed_terracotta": "Terracota glacejada verda", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON> de shulker verda", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON><PERSON> verda", "block.minecraft.green_stained_glass_pane": "Panell de vidriera verd", "block.minecraft.green_terracotta": "Terracota verda", "block.minecraft.green_wool": "<PERSON><PERSON> ve<PERSON>", "block.minecraft.grindstone": "<PERSON><PERSON>", "block.minecraft.hanging_roots": "Arrels penjants", "block.minecraft.hay_block": "Bala de fenc", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON> dens", "block.minecraft.heavy_weighted_pressure_plate": "Placa de pressió per a pes elevat", "block.minecraft.honey_block": "Bloc de mel", "block.minecraft.honeycomb_block": "Bloc de bresca", "block.minecraft.hopper": "Tremuja", "block.minecraft.horn_coral": "<PERSON><PERSON> de ban<PERSON>", "block.minecraft.horn_coral_block": "Bloc de corall de banya", "block.minecraft.horn_coral_fan": "Gorgònia de banya", "block.minecraft.horn_coral_wall_fan": "Gorgònia de banya de paret", "block.minecraft.ice": "<PERSON>el", "block.minecraft.infested_chiseled_stone_bricks": "Rajoles de roca cisellades infestades", "block.minecraft.infested_cobblestone": "Empedrat infestat", "block.minecraft.infested_cracked_stone_bricks": "Rajoles de roca esquerdades infestades", "block.minecraft.infested_deepslate": "Pissarra infestada", "block.minecraft.infested_mossy_stone_bricks": "Raj<PERSON>s de roca amb molsa infestades", "block.minecraft.infested_stone": "Roca infestada", "block.minecraft.infested_stone_bricks": "Rajoles de roca infestades", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON> de ferro", "block.minecraft.iron_block": "Bloc de ferro", "block.minecraft.iron_door": "Porta de ferro", "block.minecraft.iron_ore": "Mena de ferro", "block.minecraft.iron_trapdoor": "Trapa de ferro", "block.minecraft.jack_o_lantern": "Carabassa de Halloween", "block.minecraft.jigsaw": "Bloc de trencaclosques", "block.minecraft.jukebox": "Tocadiscs", "block.minecraft.jungle_button": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_door": "Porta de j<PERSON>la", "block.minecraft.jungle_fence": "Tanca de j<PERSON>la", "block.minecraft.jungle_fence_gate": "Porta de tanca de jungla", "block.minecraft.jungle_hanging_sign": "<PERSON><PERSON><PERSON> <PERSON> j<PERSON>", "block.minecraft.jungle_leaves": "<PERSON><PERSON> de jungla", "block.minecraft.jungle_log": "Tronc de jungla", "block.minecraft.jungle_planks": "<PERSON><PERSON>", "block.minecraft.jungle_pressure_plate": "Placa de pressió de jungla", "block.minecraft.jungle_sapling": "Esqueix de j<PERSON>la", "block.minecraft.jungle_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_wall_hanging_sign": "<PERSON><PERSON><PERSON> de jungla penjant de paret", "block.minecraft.jungle_wall_sign": "<PERSON><PERSON><PERSON> de jungla de paret", "block.minecraft.jungle_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp": "Alga", "block.minecraft.kelp_plant": "Esqueix d'alga", "block.minecraft.ladder": "Escala", "block.minecraft.lantern": "Fanal", "block.minecraft.lapis_block": "Bloc de lapislàtzuli", "block.minecraft.lapis_ore": "Mena de <PERSON>", "block.minecraft.large_amethyst_bud": "Brot d'ametista gran", "block.minecraft.large_fern": "Falaguera gran", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Calderó amb lava", "block.minecraft.leaf_litter": "Fulles seques", "block.minecraft.lectern": "Faristol", "block.minecraft.lever": "Palanca", "block.minecraft.light": "<PERSON><PERSON>", "block.minecraft.light_blue_banner": "Estandard blau clar", "block.minecraft.light_blue_bed": "<PERSON>lit blau clar", "block.minecraft.light_blue_candle": "<PERSON>iri blau clar", "block.minecraft.light_blue_candle_cake": "Pastís amb ciri blau clar", "block.minecraft.light_blue_carpet": "Estora blava clara", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON> blau clar", "block.minecraft.light_blue_concrete_powder": "Pols de formigó blau clar", "block.minecraft.light_blue_glazed_terracotta": "Terracota glacejada blava clara", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON>hulker blava clara", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON><PERSON> blau clar", "block.minecraft.light_blue_stained_glass_pane": "<PERSON>l de vidriera blau clar", "block.minecraft.light_blue_terracotta": "Terracota blava clara", "block.minecraft.light_blue_wool": "<PERSON><PERSON> blava clara", "block.minecraft.light_gray_banner": "Estandard gris clar", "block.minecraft.light_gray_bed": "Llit gris clar", "block.minecraft.light_gray_candle": "Ciri gris clar", "block.minecraft.light_gray_candle_cake": "Pastís amb ciri gris clar", "block.minecraft.light_gray_carpet": "Estora grisa clara", "block.minecraft.light_gray_concrete": "Formigó gris clar", "block.minecraft.light_gray_concrete_powder": "Pols de formigó gris clar", "block.minecraft.light_gray_glazed_terracotta": "Terracota glacejada grisa clara", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON>ker grisa clara", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON><PERSON> grisa clara", "block.minecraft.light_gray_stained_glass_pane": "<PERSON>l de vidriera gris clar", "block.minecraft.light_gray_terracotta": "Terracota grisa clara", "block.minecraft.light_gray_wool": "<PERSON><PERSON> grisa clara", "block.minecraft.light_weighted_pressure_plate": "Placa de pressió per a pes lleuger", "block.minecraft.lightning_rod": "Parallamps", "block.minecraft.lilac": "Lilà", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON> de la Mare de Déu", "block.minecraft.lily_pad": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_banner": "Estandard verd llima", "block.minecraft.lime_bed": "<PERSON><PERSON> verd llima", "block.minecraft.lime_candle": "<PERSON>iri verd llima", "block.minecraft.lime_candle_cake": "Pastís amb ciri verd llima", "block.minecraft.lime_carpet": "Estora verda llima", "block.minecraft.lime_concrete": "Formigó verd llima", "block.minecraft.lime_concrete_powder": "Pols de formigó verd llima", "block.minecraft.lime_glazed_terracotta": "Terracota glacejada verda llima", "block.minecraft.lime_shulker_box": "<PERSON><PERSON><PERSON> de shulker verda llima", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON><PERSON> verd llima", "block.minecraft.lime_stained_glass_pane": "Panell de vidriera verd llima", "block.minecraft.lime_terracotta": "Terracota verda llima", "block.minecraft.lime_wool": "<PERSON><PERSON> ve<PERSON>ima", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON>", "block.minecraft.loom": "Teler", "block.minecraft.magenta_banner": "Estandard magenta", "block.minecraft.magenta_bed": "Llit magenta", "block.minecraft.magenta_candle": "Ciri magenta", "block.minecraft.magenta_candle_cake": "Pastís amb ciri magenta", "block.minecraft.magenta_carpet": "Estora magenta", "block.minecraft.magenta_concrete": "Formigó magenta", "block.minecraft.magenta_concrete_powder": "Pols de formigó magenta", "block.minecraft.magenta_glazed_terracotta": "Terracota glacejada magenta", "block.minecraft.magenta_shulker_box": "Caixa de shulker magenta", "block.minecraft.magenta_stained_glass": "V<PERSON><PERSON>a magenta", "block.minecraft.magenta_stained_glass_pane": "Panell de vidriera magenta", "block.minecraft.magenta_terracotta": "Terracota magenta", "block.minecraft.magenta_wool": "<PERSON><PERSON> magenta", "block.minecraft.magma_block": "Bloc de magma", "block.minecraft.mangrove_button": "<PERSON><PERSON><PERSON> mangle", "block.minecraft.mangrove_door": "Porta de mangle", "block.minecraft.mangrove_fence": "Tanca de mangle", "block.minecraft.mangrove_fence_gate": "Porta de tanca de mangle", "block.minecraft.mangrove_hanging_sign": "Cartell de mangle penjant", "block.minecraft.mangrove_leaves": "Fulles de mangle", "block.minecraft.mangrove_log": "Tronc de mangle", "block.minecraft.mangrove_planks": "Taulons de mangle", "block.minecraft.mangrove_pressure_plate": "Placa de pressió de mangle", "block.minecraft.mangrove_propagule": "Propàgul de mangle", "block.minecraft.mangrove_roots": "Arrels de mangle", "block.minecraft.mangrove_sign": "Cartell de mangle", "block.minecraft.mangrove_slab": "<PERSON>los<PERSON> de mangle", "block.minecraft.mangrove_stairs": "<PERSON><PERSON><PERSON> de mangle", "block.minecraft.mangrove_trapdoor": "Trapa de mangle", "block.minecraft.mangrove_wall_hanging_sign": "Cartell de mangle penjant de paret", "block.minecraft.mangrove_wall_sign": "Cartell de mangle de paret", "block.minecraft.mangrove_wood": "<PERSON><PERSON><PERSON> de mangle", "block.minecraft.medium_amethyst_bud": "Brot d'ametista mitjà", "block.minecraft.melon": "<PERSON><PERSON>", "block.minecraft.melon_stem": "<PERSON><PERSON> de <PERSON>", "block.minecraft.moss_block": "Bloc de molsa", "block.minecraft.moss_carpet": "Estora de molsa", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON> amb molsa", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON><PERSON> d'empedrat amb molsa", "block.minecraft.mossy_cobblestone_stairs": "<PERSON><PERSON><PERSON> d'empedrat amb molsa", "block.minecraft.mossy_cobblestone_wall": "<PERSON>r d'empedrat amb molsa", "block.minecraft.mossy_stone_brick_slab": "Llosa de rajoles de roca amb molsa", "block.minecraft.mossy_stone_brick_stairs": "<PERSON><PERSON><PERSON> de rajoles de roca amb molsa", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON> de rajoles de roca amb molsa", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON><PERSON> de roca amb molsa", "block.minecraft.moving_piston": "Pistó en moviment", "block.minecraft.mud": "<PERSON>", "block.minecraft.mud_brick_slab": "Llosa de rajoles de fang", "block.minecraft.mud_brick_stairs": "<PERSON><PERSON><PERSON> de rajoles de fang", "block.minecraft.mud_brick_wall": "<PERSON><PERSON> de rajoles de fang", "block.minecraft.mud_bricks": "<PERSON><PERSON><PERSON> de fang", "block.minecraft.muddy_mangrove_roots": "Arrels de mangle enfangades", "block.minecraft.mushroom_stem": "<PERSON><PERSON>", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Tanca de rajoles del Nether", "block.minecraft.nether_brick_slab": "Llosa de rajoles del Nether", "block.minecraft.nether_brick_stairs": "Escales de rajoles del Nether", "block.minecraft.nether_brick_wall": "<PERSON><PERSON> <PERSON> raj<PERSON> del Nether", "block.minecraft.nether_bricks": "<PERSON><PERSON><PERSON> del <PERSON>her", "block.minecraft.nether_gold_ore": "<PERSON><PERSON> <PERSON>'or <PERSON>", "block.minecraft.nether_portal": "Portal al Nether", "block.minecraft.nether_quartz_ore": "<PERSON>a de quars", "block.minecraft.nether_sprouts": "Brots del Nether", "block.minecraft.nether_wart": "<PERSON><PERSON>her", "block.minecraft.nether_wart_block": "Bloc de bolet del Nether", "block.minecraft.netherite_block": "Bloc de netherita", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Bloc musical", "block.minecraft.oak_button": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_door": "Porta de roure", "block.minecraft.oak_fence": "Tanca de roure", "block.minecraft.oak_fence_gate": "Porta de tanca de roure", "block.minecraft.oak_hanging_sign": "<PERSON><PERSON><PERSON> de roure pen<PERSON>", "block.minecraft.oak_leaves": "<PERSON>es de roure", "block.minecraft.oak_log": "Tronc de roure", "block.minecraft.oak_planks": "<PERSON><PERSON> de roure", "block.minecraft.oak_pressure_plate": "Placa de pressió de roure", "block.minecraft.oak_sapling": "Esqueix de roure", "block.minecraft.oak_sign": "<PERSON><PERSON><PERSON> de roure", "block.minecraft.oak_slab": "Llosa de roure", "block.minecraft.oak_stairs": "<PERSON><PERSON><PERSON> de roure", "block.minecraft.oak_trapdoor": "Trapa de roure", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> de roure penjant de paret", "block.minecraft.oak_wall_sign": "<PERSON><PERSON><PERSON> de roure de paret", "block.minecraft.oak_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.observer": "Observador", "block.minecraft.obsidian": "Obsidiana", "block.minecraft.ochre_froglight": "Granollum ocre", "block.minecraft.ominous_banner": "<PERSON><PERSON>ard omin<PERSON>", "block.minecraft.open_eyeblossom": "<PERSON><PERSON><PERSON> obert", "block.minecraft.orange_banner": "Estandard taronja", "block.minecraft.orange_bed": "<PERSON><PERSON>", "block.minecraft.orange_candle": "Ciri taronja", "block.minecraft.orange_candle_cake": "Pastís amb ciri taronja", "block.minecraft.orange_carpet": "Estora taronja", "block.minecraft.orange_concrete": "<PERSON>ig<PERSON>", "block.minecraft.orange_concrete_powder": "Pols de formigó taronja", "block.minecraft.orange_glazed_terracotta": "Terracota glacejada taronja", "block.minecraft.orange_shulker_box": "<PERSON><PERSON><PERSON> de shulker taronja", "block.minecraft.orange_stained_glass": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>ja", "block.minecraft.orange_stained_glass_pane": "Panell de vidriera taronja", "block.minecraft.orange_terracotta": "Terracota taronja", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_wool": "<PERSON><PERSON>", "block.minecraft.oxeye_daisy": "Margarida", "block.minecraft.oxidized_chiseled_copper": "Coure oxidat cisellat", "block.minecraft.oxidized_copper": "Coure oxidat", "block.minecraft.oxidized_copper_bulb": "Làmpada de coure oxidat", "block.minecraft.oxidized_copper_door": "Porta de coure oxidat", "block.minecraft.oxidized_copper_grate": "Reixeta de coure oxidat", "block.minecraft.oxidized_copper_trapdoor": "Trapa de coure oxidat", "block.minecraft.oxidized_cut_copper": "Coure tallat oxidat", "block.minecraft.oxidized_cut_copper_slab": "Llosa de coure tallat oxidat", "block.minecraft.oxidized_cut_copper_stairs": "Escales de coure tallat oxidat", "block.minecraft.packed_ice": "Gel compacte", "block.minecraft.packed_mud": "Fang compacte", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "block.minecraft.pale_moss_block": "Bloc de molsa tètrica", "block.minecraft.pale_moss_carpet": "Estora de molsa tètrica", "block.minecraft.pale_oak_button": "<PERSON><PERSON><PERSON> de roure tè<PERSON>", "block.minecraft.pale_oak_door": "Porta de roure tètric", "block.minecraft.pale_oak_fence": "Tanca de roure tètric", "block.minecraft.pale_oak_fence_gate": "Porta de tanca de roure tètric", "block.minecraft.pale_oak_hanging_sign": "<PERSON><PERSON><PERSON> de roure t<PERSON><PERSON>", "block.minecraft.pale_oak_leaves": "Fulles de roure tètric", "block.minecraft.pale_oak_log": "Tronc de roure tètric", "block.minecraft.pale_oak_planks": "<PERSON><PERSON> de roure tètric", "block.minecraft.pale_oak_pressure_plate": "Placa de pressió de roure tètric", "block.minecraft.pale_oak_sapling": "Esqueix de roure tètric", "block.minecraft.pale_oak_sign": "<PERSON><PERSON><PERSON> de roure tètric", "block.minecraft.pale_oak_slab": "<PERSON><PERSON><PERSON> de roure tètric", "block.minecraft.pale_oak_stairs": "<PERSON><PERSON><PERSON> de roure tètric", "block.minecraft.pale_oak_trapdoor": "<PERSON><PERSON><PERSON> de roure tètric", "block.minecraft.pale_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> de roure tè<PERSON> penjant de paret", "block.minecraft.pale_oak_wall_sign": "<PERSON><PERSON><PERSON> de roure tètric de paret", "block.minecraft.pale_oak_wood": "<PERSON><PERSON><PERSON>oure t<PERSON>", "block.minecraft.pearlescent_froglight": "Granollum perlescent", "block.minecraft.peony": "Peònia", "block.minecraft.petrified_oak_slab": "Llosa petrificada de roure", "block.minecraft.piglin_head": "Cap de piglin", "block.minecraft.piglin_wall_head": "Cap de piglin de paret", "block.minecraft.pink_banner": "Estandard rosa", "block.minecraft.pink_bed": "<PERSON><PERSON> rosa", "block.minecraft.pink_candle": "Ciri rosa", "block.minecraft.pink_candle_cake": "Pastís amb ciri rosa", "block.minecraft.pink_carpet": "Estora rosa", "block.minecraft.pink_concrete": "Formig<PERSON> rosa", "block.minecraft.pink_concrete_powder": "Pols de formigó rosa", "block.minecraft.pink_glazed_terracotta": "Terracota glacejada rosa", "block.minecraft.pink_petals": "Pè<PERSON> rosa", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON>ker rosa", "block.minecraft.pink_stained_glass": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_stained_glass_pane": "Panell de vidriera rosa", "block.minecraft.pink_terracotta": "Terracota rosa", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_wool": "<PERSON><PERSON> rosa", "block.minecraft.piston": "<PERSON><PERSON><PERSON>", "block.minecraft.piston_head": "Cap de pistó", "block.minecraft.pitcher_crop": "Cultiu de planta de gerra", "block.minecraft.pitcher_plant": "Planta de gerra", "block.minecraft.player_head": "Cap del jugador", "block.minecraft.player_head.named": "Cap de %s", "block.minecraft.player_wall_head": "Cap del jugador de paret", "block.minecraft.podzol": "<PERSON><PERSON><PERSON>", "block.minecraft.pointed_dripstone": "Espeleotema punteguda", "block.minecraft.polished_andesite": "<PERSON><PERSON> polida", "block.minecraft.polished_andesite_slab": "Llosa d'andesita polida", "block.minecraft.polished_andesite_stairs": "Escales d'andesita polida", "block.minecraft.polished_basalt": "Basalt polit", "block.minecraft.polished_blackstone": "Roca negra polida", "block.minecraft.polished_blackstone_brick_slab": "Llosa de rajoles de roca negra polida", "block.minecraft.polished_blackstone_brick_stairs": "Escales de rajoles de roca negra polida", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON> de rajoles de roca negra polida", "block.minecraft.polished_blackstone_bricks": "Rajoles de roca negra polida", "block.minecraft.polished_blackstone_button": "Botó de roca negra polida", "block.minecraft.polished_blackstone_pressure_plate": "Placa de pressió de roca negra polida", "block.minecraft.polished_blackstone_slab": "Llosa de roca negra polida", "block.minecraft.polished_blackstone_stairs": "Escales de roca negra polida", "block.minecraft.polished_blackstone_wall": "Mur de roca negra polida", "block.minecraft.polished_deepslate": "Pissarra polida", "block.minecraft.polished_deepslate_slab": "Llosa de pissarra polida", "block.minecraft.polished_deepslate_stairs": "<PERSON><PERSON><PERSON> de pissarra polida", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON> <PERSON> polida", "block.minecraft.polished_diorite": "<PERSON><PERSON>ta polida", "block.minecraft.polished_diorite_slab": "Llosa de diorita polida", "block.minecraft.polished_diorite_stairs": "Escales de diorita polida", "block.minecraft.polished_granite": "Granit polit", "block.minecraft.polished_granite_slab": "Llosa de granit polida", "block.minecraft.polished_granite_stairs": "Escales de granit polit", "block.minecraft.polished_tuff": "Tova volcànica polida", "block.minecraft.polished_tuff_slab": "Llosa de tova volcànica polida", "block.minecraft.polished_tuff_stairs": "Escales de tova volcànica polida", "block.minecraft.polished_tuff_wall": "Mur de tova volcànica polida", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "Creïlles", "block.minecraft.potted_acacia_sapling": "Test amb esqueix d'acàcia", "block.minecraft.potted_allium": "Test amb allium", "block.minecraft.potted_azalea_bush": "Test amb azalea", "block.minecraft.potted_azure_bluet": "Test amb houstonia caerulea", "block.minecraft.potted_bamboo": "Test amb bambú", "block.minecraft.potted_birch_sapling": "Test amb esqueix de bedoll", "block.minecraft.potted_blue_orchid": "Test amb orq<PERSON><PERSON><PERSON> blava", "block.minecraft.potted_brown_mushroom": "Test amb x<PERSON><PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.potted_cactus": "Test amb cactus", "block.minecraft.potted_cherry_sapling": "Test amb esqueix de cirerer", "block.minecraft.potted_closed_eyeblossom": "Test amb florull tancat", "block.minecraft.potted_cornflower": "Test amb blauet", "block.minecraft.potted_crimson_fungus": "Test amb fong carmesí", "block.minecraft.potted_crimson_roots": "Test amb arrels carmesines", "block.minecraft.potted_dandelion": "Test amb dent de lleó", "block.minecraft.potted_dark_oak_sapling": "Test amb esqueix de roure fosc", "block.minecraft.potted_dead_bush": "Test amb arbust mort", "block.minecraft.potted_fern": "Test amb falaguera", "block.minecraft.potted_flowering_azalea_bush": "Test amb azalea florent", "block.minecraft.potted_jungle_sapling": "Test amb esqueix de jungla", "block.minecraft.potted_lily_of_the_valley": "Test amb lliri de la Mare de Déu", "block.minecraft.potted_mangrove_propagule": "Test amb propàgul de mangle", "block.minecraft.potted_oak_sapling": "Test amb esqueix de roure", "block.minecraft.potted_open_eyeblossom": "Test amb florull obert", "block.minecraft.potted_orange_tulip": "Test amb tulipa taronja", "block.minecraft.potted_oxeye_daisy": "Test amb margarida", "block.minecraft.potted_pale_oak_sapling": "Test amb esqueix de roure tètric", "block.minecraft.potted_pink_tulip": "Test amb tulipa rosa", "block.minecraft.potted_poppy": "Test amb rosella", "block.minecraft.potted_red_mushroom": "Test amb xamp<PERSON><PERSON>ó roig", "block.minecraft.potted_red_tulip": "Test amb tulipa roja", "block.minecraft.potted_spruce_sapling": "Test amb esqueix de pícea", "block.minecraft.potted_torchflower": "Test amb flamaflor", "block.minecraft.potted_warped_fungus": "Test amb fong estramb<PERSON>tic", "block.minecraft.potted_warped_roots": "Test amb arrels estrambòtiques", "block.minecraft.potted_white_tulip": "Test amb tulipa blanca", "block.minecraft.potted_wither_rose": "Test amb rosa mústia", "block.minecraft.powder_snow": "Neu pols", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON> amb neu pols", "block.minecraft.powered_rail": "Via d'accelaració", "block.minecraft.prismarine": "Prismarina", "block.minecraft.prismarine_brick_slab": "Llosa de rajoles de prismarina", "block.minecraft.prismarine_brick_stairs": "Escales de rajoles de prismarina", "block.minecraft.prismarine_bricks": "<PERSON><PERSON>s de prismarina", "block.minecraft.prismarine_slab": "Llosa de prismarina", "block.minecraft.prismarine_stairs": "Escales de prismarina", "block.minecraft.prismarine_wall": "<PERSON><PERSON> <PERSON>", "block.minecraft.pumpkin": "Carabassa", "block.minecraft.pumpkin_stem": "<PERSON><PERSON> de <PERSON>", "block.minecraft.purple_banner": "Estandard morat", "block.minecraft.purple_bed": "<PERSON><PERSON> morat", "block.minecraft.purple_candle": "Ciri morat", "block.minecraft.purple_candle_cake": "Pastís amb ciri morat", "block.minecraft.purple_carpet": "Estora morada", "block.minecraft.purple_concrete": "<PERSON><PERSON><PERSON> morat", "block.minecraft.purple_concrete_powder": "Pols de formigó morat", "block.minecraft.purple_glazed_terracotta": "Terracota glacejada morada", "block.minecraft.purple_shulker_box": "<PERSON><PERSON><PERSON> de shulker morada", "block.minecraft.purple_stained_glass": "<PERSON><PERSON><PERSON><PERSON> morada", "block.minecraft.purple_stained_glass_pane": "Panell de vidriera morat", "block.minecraft.purple_terracotta": "Terracota morada", "block.minecraft.purple_wool": "<PERSON><PERSON> m<PERSON>", "block.minecraft.purpur_block": "Bloc de purpur", "block.minecraft.purpur_pillar": "<PERSON><PERSON> de purpur", "block.minecraft.purpur_slab": "Llosa de purpur", "block.minecraft.purpur_stairs": "<PERSON><PERSON><PERSON> de purpur", "block.minecraft.quartz_block": "Bloc de quars", "block.minecraft.quartz_bricks": "<PERSON><PERSON><PERSON> de quars", "block.minecraft.quartz_pillar": "<PERSON><PERSON> de quars", "block.minecraft.quartz_slab": "<PERSON><PERSON><PERSON> de quars", "block.minecraft.quartz_stairs": "<PERSON><PERSON><PERSON> de quars", "block.minecraft.rail": "Via", "block.minecraft.raw_copper_block": "Bloc de mineral de coure", "block.minecraft.raw_gold_block": "Bloc de mineral d'or", "block.minecraft.raw_iron_block": "Bloc de mineral de ferro", "block.minecraft.red_banner": "Estandard roig", "block.minecraft.red_bed": "<PERSON><PERSON> roig", "block.minecraft.red_candle": "Ciri roig", "block.minecraft.red_candle_cake": "Pastís amb ciri roig", "block.minecraft.red_carpet": "Estora roja", "block.minecraft.red_concrete": "Formigó roig", "block.minecraft.red_concrete_powder": "Pols de formigó roig", "block.minecraft.red_glazed_terracotta": "Terracota glacejada roja", "block.minecraft.red_mushroom": "Xampinyó roig", "block.minecraft.red_mushroom_block": "Bloc de xampinyó roig", "block.minecraft.red_nether_brick_slab": "Llosa de rajoles del Nether roges", "block.minecraft.red_nether_brick_stairs": "Escales de rajoles del Nether roges", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON> <PERSON> rajoles del Nether roges", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON> del Nether roges", "block.minecraft.red_sand": "Arena roja", "block.minecraft.red_sandstone": "Roca arenosa roja", "block.minecraft.red_sandstone_slab": "Llosa de roca arenosa roja", "block.minecraft.red_sandstone_stairs": "Escales de roca arenosa roja", "block.minecraft.red_sandstone_wall": "<PERSON>r de roca arenosa roja", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON> de shulker roja", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON><PERSON> roja", "block.minecraft.red_stained_glass_pane": "Panell de vidriera roig", "block.minecraft.red_terracotta": "Terracota roja", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.red_wool": "<PERSON><PERSON>", "block.minecraft.redstone_block": "Bloc de redstone", "block.minecraft.redstone_lamp": "Làmpada de redstone", "block.minecraft.redstone_ore": "Mena de redstone", "block.minecraft.redstone_torch": "Torxa de redstone", "block.minecraft.redstone_wall_torch": "Torxa de redstone de paret", "block.minecraft.redstone_wire": "Cable de redstone", "block.minecraft.reinforced_deepslate": "Pissarra profunda reforçada", "block.minecraft.repeater": "Repetidor de redstone", "block.minecraft.repeating_command_block": "Bloc de comandaments de repetició", "block.minecraft.resin_block": "Bloc de resina", "block.minecraft.resin_brick_slab": "Llosa de rajoles de resina", "block.minecraft.resin_brick_stairs": "<PERSON><PERSON><PERSON> de rajoles de resina", "block.minecraft.resin_brick_wall": "<PERSON><PERSON> de rajoles de resina", "block.minecraft.resin_bricks": "<PERSON><PERSON><PERSON> de resina", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON>", "block.minecraft.respawn_anchor": "Àncora de reaparició", "block.minecraft.rooted_dirt": "Terra amb arrels", "block.minecraft.rose_bush": "<PERSON><PERSON>", "block.minecraft.sand": "Arena", "block.minecraft.sandstone": "Roca arenosa", "block.minecraft.sandstone_slab": "Llosa de roca arenosa", "block.minecraft.sandstone_stairs": "Escales de roca arenosa", "block.minecraft.sandstone_wall": "<PERSON>r de roca arenosa", "block.minecraft.scaffolding": "Bastida", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Catalitzador de sculk", "block.minecraft.sculk_sensor": "Sensor de sculk", "block.minecraft.sculk_shrieker": "Sculk cridaner", "block.minecraft.sculk_vein": "<PERSON><PERSON> de sculk", "block.minecraft.sea_lantern": "Llanterna marina", "block.minecraft.sea_pickle": "Cogombre de mar", "block.minecraft.seagrass": "Herba marina", "block.minecraft.set_spawn": "S'ha establit el punt de reaparició", "block.minecraft.short_dry_grass": "Herba seca curta", "block.minecraft.short_grass": "Herba curta", "block.minecraft.shroomlight": "Bloc d'espores lluminoses", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "Calavera d'esquelet", "block.minecraft.skeleton_wall_skull": "Calavera d'esquelet de paret", "block.minecraft.slime_block": "<PERSON> de llim", "block.minecraft.small_amethyst_bud": "Brot d'ametista xicotet", "block.minecraft.small_dripleaf": "Fulla relliscosa xicoteta", "block.minecraft.smithing_table": "Ferreria", "block.minecraft.smoker": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Basalt llis", "block.minecraft.smooth_quartz": "Bloc de quars llis", "block.minecraft.smooth_quartz_slab": "<PERSON><PERSON><PERSON> de quars llis", "block.minecraft.smooth_quartz_stairs": "<PERSON><PERSON><PERSON> de quars llis", "block.minecraft.smooth_red_sandstone": "Roca arenosa roja llisa", "block.minecraft.smooth_red_sandstone_slab": "Llosa de roca arenosa roja llisa", "block.minecraft.smooth_red_sandstone_stairs": "E<PERSON>les de roca arenosa roja llisa", "block.minecraft.smooth_sandstone": "Roca arenosa llisa", "block.minecraft.smooth_sandstone_slab": "Llosa de roca arenosa llisa", "block.minecraft.smooth_sandstone_stairs": "E<PERSON>les de roca arenosa llisa", "block.minecraft.smooth_stone": "Roca llisa", "block.minecraft.smooth_stone_slab": "Llosa de roca llisa", "block.minecraft.sniffer_egg": "<PERSON>u de rastrejador", "block.minecraft.snow": "<PERSON>eu", "block.minecraft.snow_block": "Bloc de neu", "block.minecraft.soul_campfire": "Foguera d'ànimes", "block.minecraft.soul_fire": "Foc d'ànimes", "block.minecraft.soul_lantern": "Fanal d'ànimes", "block.minecraft.soul_sand": "Arena d'ànimes", "block.minecraft.soul_soil": "Terra d'ànimes", "block.minecraft.soul_torch": "Torxa d'ànimes", "block.minecraft.soul_wall_torch": "Torxa d'ànimes de paret", "block.minecraft.spawn.not_valid": "No tens cap llit ni àncora de reaparició, o està obstruït/ïda", "block.minecraft.spawner": "Generador de monstres", "block.minecraft.spawner.desc1": "Interactua amb un generador de criatures:", "block.minecraft.spawner.desc2": "Definix el tipus de criatura", "block.minecraft.sponge": "Esponja", "block.minecraft.spore_blossom": "Flor d'espores", "block.minecraft.spruce_button": "Bo<PERSON>ó <PERSON>", "block.minecraft.spruce_door": "Porta de pícea", "block.minecraft.spruce_fence": "Tanca de pícea", "block.minecraft.spruce_fence_gate": "Porta de tanca de pícea", "block.minecraft.spruce_hanging_sign": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.spruce_leaves": "Fulles de pícea", "block.minecraft.spruce_log": "Tronc de pícea", "block.minecraft.spruce_planks": "Taulons de pícea", "block.minecraft.spruce_pressure_plate": "Placa de pressió de pícea", "block.minecraft.spruce_sapling": "Esqueix de pícea", "block.minecraft.spruce_sign": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.spruce_slab": "Llosa de p<PERSON>", "block.minecraft.spruce_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_trapdoor": "Trapa de p<PERSON>cea", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON><PERSON> de p<PERSON><PERSON> pen<PERSON> de paret", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON><PERSON> de p<PERSON><PERSON> de paret", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.sticky_piston": "Pistó <PERSON>", "block.minecraft.stone": "Roca", "block.minecraft.stone_brick_slab": "Llosa de rajoles de roca", "block.minecraft.stone_brick_stairs": "Escales de rajoles de roca", "block.minecraft.stone_brick_wall": "<PERSON><PERSON> de rajoles de roca", "block.minecraft.stone_bricks": "<PERSON><PERSON><PERSON> de roca", "block.minecraft.stone_button": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_pressure_plate": "Placa de pressió de pedra", "block.minecraft.stone_slab": "Llosa de roca", "block.minecraft.stone_stairs": "Escales de roca", "block.minecraft.stonecutter": "Tallapedra", "block.minecraft.stripped_acacia_log": "Tronc d'acàcia escorçat", "block.minecraft.stripped_acacia_wood": "Llenya d’acàcia escorçada", "block.minecraft.stripped_bamboo_block": "Bloc de bambú escorçat", "block.minecraft.stripped_birch_log": "Tronc de bedoll escorçat", "block.minecraft.stripped_birch_wood": "Llenya de bedoll escorçada", "block.minecraft.stripped_cherry_log": "Tronc de cirerer escorçat", "block.minecraft.stripped_cherry_wood": "Llenya de cirerer escorçada", "block.minecraft.stripped_crimson_hyphae": "Hifa carmesí escorçada", "block.minecraft.stripped_crimson_stem": "Tija carmesí escorçada", "block.minecraft.stripped_dark_oak_log": "Tronc de roure fosc escorçat", "block.minecraft.stripped_dark_oak_wood": "Llenya de roure fosc escorçada", "block.minecraft.stripped_jungle_log": "Tronc de jungla escorçat", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON>ya de jungla escorçada", "block.minecraft.stripped_mangrove_log": "Tronc de mangle escorçat", "block.minecraft.stripped_mangrove_wood": "<PERSON>lenya de mangle escorçada", "block.minecraft.stripped_oak_log": "Tronc de roure escorçat", "block.minecraft.stripped_oak_wood": "Llenya de roure escorçada", "block.minecraft.stripped_pale_oak_log": "Tronc de roure tètric escorçat", "block.minecraft.stripped_pale_oak_wood": "<PERSON><PERSON><PERSON> de roure tè<PERSON> escorçada", "block.minecraft.stripped_spruce_log": "Tronc de pícea es<PERSON>", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON><PERSON> <PERSON><PERSON> es<PERSON>", "block.minecraft.stripped_warped_hyphae": "Hifa estrambòtica escorçada", "block.minecraft.stripped_warped_stem": "Tija de fong estrambòtic escorçada", "block.minecraft.structure_block": "Bloc d'estructura", "block.minecraft.structure_void": "Estructura buida", "block.minecraft.sugar_cane": "<PERSON><PERSON>", "block.minecraft.sunflower": "Gira-sol", "block.minecraft.suspicious_gravel": "Grava sospitosa", "block.minecraft.suspicious_sand": "Arena sospitosa", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tall_dry_grass": "Herba seca alta", "block.minecraft.tall_grass": "Herba alta", "block.minecraft.tall_seagrass": "Herba marina alta", "block.minecraft.target": "<PERSON>", "block.minecraft.terracotta": "Terracota", "block.minecraft.test_block": "<PERSON> de proves", "block.minecraft.test_instance_block": "Bloc de proves d'instància", "block.minecraft.tinted_glass": "Vidre opac", "block.minecraft.tnt": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tnt.disabled": "Les explosions de dinamita estan desactivades", "block.minecraft.torch": "Torxa", "block.minecraft.torchflower": "Flamaflor", "block.minecraft.torchflower_crop": "<PERSON>ult<PERSON> de flamaflor", "block.minecraft.trapped_chest": "<PERSON><PERSON><PERSON> trampa", "block.minecraft.trial_spawner": "Generador de reptes", "block.minecraft.tripwire": "<PERSON><PERSON>", "block.minecraft.tripwire_hook": "Ganxo", "block.minecraft.tube_coral": "<PERSON>l de <PERSON>", "block.minecraft.tube_coral_block": "Bloc de corall de tub", "block.minecraft.tube_coral_fan": "Gorgònia de tub", "block.minecraft.tube_coral_wall_fan": "Gorgònia de tub de paret", "block.minecraft.tuff": "Tova volcànica", "block.minecraft.tuff_brick_slab": "Llosa de rajoles de tova volcànica", "block.minecraft.tuff_brick_stairs": "Escales de rajoles de tova volcànica", "block.minecraft.tuff_brick_wall": "Mur de rajoles de tova volcànica", "block.minecraft.tuff_bricks": "Rajoles de tova volcànica", "block.minecraft.tuff_slab": "Llosa de tova volcànica", "block.minecraft.tuff_stairs": "Escales de tova volcànica", "block.minecraft.tuff_wall": "Mur de tova volcànica", "block.minecraft.turtle_egg": "Ou de tortuga marina", "block.minecraft.twisting_vines": "Lianes estrambòtiques", "block.minecraft.twisting_vines_plant": "Lianes estrambòtiques", "block.minecraft.vault": "Caixa forta", "block.minecraft.verdant_froglight": "Granollum verda", "block.minecraft.vine": "<PERSON><PERSON>", "block.minecraft.void_air": "Aire del buit", "block.minecraft.wall_torch": "<PERSON><PERSON> de paret", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_door": "Porta estrambòtica", "block.minecraft.warped_fence": "Tanca estrambòtica", "block.minecraft.warped_fence_gate": "Porta de tanca estrambòtica", "block.minecraft.warped_fungus": "<PERSON><PERSON> estram<PERSON>", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> pen<PERSON>t", "block.minecraft.warped_hyphae": "Hifa estrambòtica", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_planks": "Taulons de fusta estrambòtica", "block.minecraft.warped_pressure_plate": "Placa de pressió estrambòtica", "block.minecraft.warped_roots": "Arrels estrambòtiques", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_slab": "Llosa estrambòtica", "block.minecraft.warped_stairs": "<PERSON><PERSON>les estrambòtiques", "block.minecraft.warped_stem": "<PERSON>ija de fong estramb<PERSON>tic", "block.minecraft.warped_trapdoor": "Trapa estrambòtica", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON> est<PERSON><PERSON><PERSON> penjant de paret", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON> est<PERSON><PERSON><PERSON> de paret", "block.minecraft.warped_wart_block": "Bloc de fong estrambòtic", "block.minecraft.water": "Aigua", "block.minecraft.water_cauldron": "<PERSON><PERSON> amb aigua", "block.minecraft.waxed_chiseled_copper": "Coure cisellat encerat", "block.minecraft.waxed_copper_block": "Bloc de coure encerat", "block.minecraft.waxed_copper_bulb": "Làmpada de coure encerat", "block.minecraft.waxed_copper_door": "Porta de coure encerat", "block.minecraft.waxed_copper_grate": "Reixeta de coure encerat", "block.minecraft.waxed_copper_trapdoor": "Trapa de coure encerat", "block.minecraft.waxed_cut_copper": "Coure tallat encerat", "block.minecraft.waxed_cut_copper_slab": "Llosa de coure tallat encerat", "block.minecraft.waxed_cut_copper_stairs": "Escales de coure tallat encerat", "block.minecraft.waxed_exposed_chiseled_copper": "Coure cisellat exposat encerat", "block.minecraft.waxed_exposed_copper": "Coure encerat exposat", "block.minecraft.waxed_exposed_copper_bulb": "Làmpada de coure exposat encerat", "block.minecraft.waxed_exposed_copper_door": "Porta de coure exposat encerat", "block.minecraft.waxed_exposed_copper_grate": "Reixeta de coure exposat encerat", "block.minecraft.waxed_exposed_copper_trapdoor": "Trapa de coure exposat encerat", "block.minecraft.waxed_exposed_cut_copper": "Coure tallat encerat exposat", "block.minecraft.waxed_exposed_cut_copper_slab": "Llosa de coure tallat encerat exposat", "block.minecraft.waxed_exposed_cut_copper_stairs": "Escales de coure tallat encerat exposat", "block.minecraft.waxed_oxidized_chiseled_copper": "Coure cisellat oxidat encerat", "block.minecraft.waxed_oxidized_copper": "Coure oxidat encerat", "block.minecraft.waxed_oxidized_copper_bulb": "Làmpada de coure oxidat encerat", "block.minecraft.waxed_oxidized_copper_door": "Porta de coure oxidat encerat", "block.minecraft.waxed_oxidized_copper_grate": "Reixeta de coure oxidat encerat", "block.minecraft.waxed_oxidized_copper_trapdoor": "Trapa de coure oxidat encerat", "block.minecraft.waxed_oxidized_cut_copper": "Coure tallat oxidat encerat", "block.minecraft.waxed_oxidized_cut_copper_slab": "Llosa de coure tallat oxidat encerat", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Escales de coure tallat oxidat encerat", "block.minecraft.waxed_weathered_chiseled_copper": "Coure cisellat rovellat encerat", "block.minecraft.waxed_weathered_copper": "Coure encerat rovellat", "block.minecraft.waxed_weathered_copper_bulb": "Làmpada de coure rovellat encerat", "block.minecraft.waxed_weathered_copper_door": "Porta de coure rovellat encerat", "block.minecraft.waxed_weathered_copper_grate": "Reixeta de coure rovellat encerat", "block.minecraft.waxed_weathered_copper_trapdoor": "Trapa de coure rovellat encerat", "block.minecraft.waxed_weathered_cut_copper": "Coure tallat encerat rovellat", "block.minecraft.waxed_weathered_cut_copper_slab": "Llosa de coure tallat encerat rovellat", "block.minecraft.waxed_weathered_cut_copper_stairs": "Escales de coure tallat encerat rovellat", "block.minecraft.weathered_chiseled_copper": "Coure cisellat rovellat", "block.minecraft.weathered_copper": "Coure rovellat", "block.minecraft.weathered_copper_bulb": "Làmpada de coure rovellat", "block.minecraft.weathered_copper_door": "Porta de coure rovellat", "block.minecraft.weathered_copper_grate": "Reixeta de coure rovellat", "block.minecraft.weathered_copper_trapdoor": "Trapa de coure rovellat", "block.minecraft.weathered_cut_copper": "Coure tallat rovellat", "block.minecraft.weathered_cut_copper_slab": "Llosa de coure tallat rovellada", "block.minecraft.weathered_cut_copper_stairs": "Escales de coure tallat rovellades", "block.minecraft.weeping_vines": "Lianes carmesines", "block.minecraft.weeping_vines_plant": "Lianes carmesines", "block.minecraft.wet_sponge": "Esponja humida", "block.minecraft.wheat": "<PERSON><PERSON><PERSON> de <PERSON>", "block.minecraft.white_banner": "Estandard blanc", "block.minecraft.white_bed": "<PERSON><PERSON> blanc", "block.minecraft.white_candle": "<PERSON>iri blanc", "block.minecraft.white_candle_cake": "Pastís amb ciri blanc", "block.minecraft.white_carpet": "Estora blanca", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.white_concrete_powder": "Pols de formigó blanc", "block.minecraft.white_glazed_terracotta": "Terracota glacejada blanca", "block.minecraft.white_shulker_box": "Cai<PERSON> de shulker blanca", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON><PERSON> blanca", "block.minecraft.white_stained_glass_pane": "Panell de vidriera blanc", "block.minecraft.white_terracotta": "Terracota blanca", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> blanca", "block.minecraft.white_wool": "<PERSON><PERSON> blan<PERSON>", "block.minecraft.wildflowers": "Flors silvestres", "block.minecraft.wither_rose": "<PERSON>", "block.minecraft.wither_skeleton_skull": "Calavera d'esquelet wither", "block.minecraft.wither_skeleton_wall_skull": "Calavera d'esquelet wither de paret", "block.minecraft.yellow_banner": "Estandard groc", "block.minecraft.yellow_bed": "<PERSON><PERSON> groc", "block.minecraft.yellow_candle": "Ciri groc", "block.minecraft.yellow_candle_cake": "Pastís amb ciri groc", "block.minecraft.yellow_carpet": "E<PERSON><PERSON> groga", "block.minecraft.yellow_concrete": "Formigó groc", "block.minecraft.yellow_concrete_powder": "Pols de formigó groc", "block.minecraft.yellow_glazed_terracotta": "Terracota glacejada groga", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON> de <PERSON> groga", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON><PERSON> groga", "block.minecraft.yellow_stained_glass_pane": "Panell de vidriera groc", "block.minecraft.yellow_terracotta": "Terracota groga", "block.minecraft.yellow_wool": "<PERSON><PERSON>", "block.minecraft.zombie_head": "Cap de zombi", "block.minecraft.zombie_wall_head": "Cap de zombi de paret", "book.byAuthor": "per %1$s", "book.edit.title": "Pantalla d'edició del llibre", "book.editTitle": "Posa un títol al llibre:", "book.finalizeButton": "Signa i tanca", "book.finalizeWarning": "Atenció! Quan signes el llibre no podràs editar-lo.", "book.generation.0": "Original", "book.generation.1": "Còpia de l'original", "book.generation.2": "Còpia de la còpia", "book.generation.3": "Fet a trossos", "book.invalid.tag": "* Etiqueta invàlida *", "book.pageIndicator": "Pàgina %1$s de %2$s", "book.page_button.next": "<PERSON><PERSON><PERSON><PERSON> segü<PERSON>", "book.page_button.previous": "Pàgina anterior", "book.sign.title": "Pantalla de signatura del llibre", "book.sign.titlebox": "Títol", "book.signButton": "Signatura", "book.view.title": "Pantalla de visualització del llibre", "build.tooHigh": "El límit de construcció és %s", "chat.cannotSend": "No es pot enviar el missatge de xat", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Fes clic per a teletransportar-te", "chat.copy": "Copiar al porta-retalls", "chat.copy.click": "Fes clic per copiar-ho al porta-retalls", "chat.deleted_marker": "Este missatge del xat ha sigut eliminat pel servidor.", "chat.disabled.chain_broken": "El xat s'ha desactivat a causa d'una cadena trencada. Intenta reconnectar-te.", "chat.disabled.expiredProfileKey": "El xat s'ha desactivat degut a què la clau pública del perfil ha expirat. Prova reconnectant.", "chat.disabled.invalid_command_signature": "Un comandament ha tingut una signatura d'argument inesperada o absent.", "chat.disabled.invalid_signature": "El xat ha tingut una signatura invàlida. Per favor, prova a reconnectar-te.", "chat.disabled.launcher": "El xat està desactivat per les opcions del llançador. No es pot enviar el missatge.", "chat.disabled.missingProfileKey": "El xat s'ha desactivat a causa de la falta d'una clau pública del perfil. Prova reconnectant.", "chat.disabled.options": "El xat està desactivat a les opcions del client.", "chat.disabled.out_of_order_chat": "El xat està fora de servici. Ha canviat l'hora del sistema?", "chat.disabled.profile": "El xat no està permés per la configuració del compte. Prem \"%s\" de nou per a més informació.", "chat.disabled.profile.moreInfo": "El xat no està permés per la configuració del compte. No es poden enviar ni rebre missatges.", "chat.editBox": "xat", "chat.filtered": "Filtrat pel servidor.", "chat.filtered_full": "El servidor ha ocultat el teu missatge per a alguns jugadors.", "chat.link.confirm": "Estàs segur que vols obrir la pàgina web?", "chat.link.confirmTrusted": "Vols obrir este enllaç o copiar-lo al porta-retalls?", "chat.link.open": "<PERSON><PERSON> al navegador", "chat.link.warning": "No òbrigues mai enllaços de gent que no coneixes!", "chat.queue": "[+%s línies pendents]", "chat.square_brackets": "[%s]", "chat.tag.error": "El servidor ha enviat un missatge invàlid.", "chat.tag.modified": "Missatge modificat pel servidor. Original:", "chat.tag.not_secure": "Missatge no verificat. No es pot denunciar.", "chat.tag.system": "Missatge del servidor. No es pot denunciar.", "chat.tag.system_single_player": "Missatge del servidor.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s ha completat el repte %s", "chat.type.advancement.goal": "%s ha assolit l'objectiu %s", "chat.type.advancement.task": "%s ha fet l'avanç %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Missatge de l'equip", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s diu %s", "chat.validation_error": "Error de validació del xat", "chat_screen.message": "Missatge per a enviar: %s", "chat_screen.title": "Pantalla del xat", "chat_screen.usage": "Escriu un missatge i prem \"Enter\" per a enviar", "chunk.toast.checkLog": "Mira el registre per a obtindre més detalls", "chunk.toast.loadFailure": "No s'ha pogut carregar el chunk en %s", "chunk.toast.lowDiskSpace": "Queda poc d'espai al disc!", "chunk.toast.lowDiskSpace.description": "Potser no es puga guardar el món.", "chunk.toast.saveFailure": "No s'ha pogut guardar el chunk en %s", "clear.failed.multiple": "No s'han trobat objectes a l'inventari de %s jugadors", "clear.failed.single": "No s'ha trobat cap objecte a l'inventari de %s", "color.minecraft.black": "Negre", "color.minecraft.blue": "Blau", "color.minecraft.brown": "<PERSON><PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON>", "color.minecraft.green": "Verd", "color.minecraft.light_blue": "<PERSON><PERSON> clar", "color.minecraft.light_gray": "<PERSON><PERSON> clar", "color.minecraft.lime": "<PERSON><PERSON>", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "<PERSON>ron<PERSON>", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "<PERSON><PERSON>", "color.minecraft.red": "<PERSON><PERSON><PERSON>", "color.minecraft.white": "<PERSON>", "color.minecraft.yellow": "Groc", "command.context.here": "<--[ACÍ]", "command.context.parse_error": "%s a la posició %s: %s", "command.exception": "No es pot analitzar el comandament: %s", "command.expected.separator": "Es requerix un espai en blanc per concloure un argument, per<PERSON> has posat dades", "command.failed": "Ha ocorregut un error en intentar executar el comandament", "command.forkLimit": "S'ha superat el número màxim de contextos (%s)", "command.unknown.argument": "L'argument del comandament no és correcte", "command.unknown.command": "Comandament desconegut o incomplet, mira baix l'error", "commands.advancement.criterionNotFound": "L'avanç %1$s no conté el criteri \"%2$s\"", "commands.advancement.grant.criterion.to.many.failure": "No s'ha pogut concedir el criteri \"%s\" de l'avanç %s a %s jugadors ja que ja els tenien", "commands.advancement.grant.criterion.to.many.success": "S'ha concedit el criteri \"%s\" de l'avanç %s a %s jugadors", "commands.advancement.grant.criterion.to.one.failure": "No s'ha pogut concedir el criteri \"%s\" de l'avanç %s a %s ja que ja el tenia", "commands.advancement.grant.criterion.to.one.success": "S'ha concedit el criteri \"%s\" de l'avanç %s a %s", "commands.advancement.grant.many.to.many.failure": "No s'han pogut concedir %s avanços a %s jugadors ja que ja els tenien", "commands.advancement.grant.many.to.many.success": "S'han concedit %s avanços a %s jugadors", "commands.advancement.grant.many.to.one.failure": "No s'han pogut concedir %s avanços a %s ja que ja els tenia", "commands.advancement.grant.many.to.one.success": "S'han concedit %s avanços a %s", "commands.advancement.grant.one.to.many.failure": "No s'ha pogut concedir l'avanç %s a %s jugadors ja que ja el tenien", "commands.advancement.grant.one.to.many.success": "S'ha concedit l'avanç %s a %s jugadors", "commands.advancement.grant.one.to.one.failure": "No s'ha pogut concedir l'avanç %s a %s ja que ja el tenia", "commands.advancement.grant.one.to.one.success": "S'ha concedit l'avanç %s a %s", "commands.advancement.revoke.criterion.to.many.failure": "No s'ha pogut revocar el criteri \"%s\" de l'avanç %s a %s jugadors ja que no els tenien", "commands.advancement.revoke.criterion.to.many.success": "S'ha revocat el criteri \"%s\" de l'avanç %s a %s jugadors", "commands.advancement.revoke.criterion.to.one.failure": "No s'ha pogut revocar el criteri \"%s\" de l'avanç %s a %s ja que no el tenia", "commands.advancement.revoke.criterion.to.one.success": "S'ha revocat el criteri \"%s\" de l'avanç %s a %s", "commands.advancement.revoke.many.to.many.failure": "No s'han pogut revocar %s avanços a %s jugadors ja que no els tenien", "commands.advancement.revoke.many.to.many.success": "S'han revocat %s avanços a %s jugadors", "commands.advancement.revoke.many.to.one.failure": "No s'han pogut revocar %s avanços a %s ja que no els tenia", "commands.advancement.revoke.many.to.one.success": "S'han revocat %s avanços a %s", "commands.advancement.revoke.one.to.many.failure": "No s'ha pogut revocar l'avanç %s a %s jugadors ja que no el tenien", "commands.advancement.revoke.one.to.many.success": "S'ha revocat l'avanç %s a %s jugadors", "commands.advancement.revoke.one.to.one.failure": "No s'ha pogut revocar l'avanç %s a %s ja que no el tenia", "commands.advancement.revoke.one.to.one.success": "S'ha revocat l'avanç %s a %s", "commands.attribute.base_value.get.success": "El valor base de l'atribut %s de l'entitat %s és %s", "commands.attribute.base_value.reset.success": "El valor base per a l'atribut %s per a l'entitat %s s'ha reiniciat al predeterminat %s", "commands.attribute.base_value.set.success": "El valor base de l'atribut %s de l'entitat %s s'ha establit a %s", "commands.attribute.failed.entity": "%s no és una entitat vàlida per al comandament", "commands.attribute.failed.modifier_already_present": "El modificador %s ja està present a l'atribut %s de l'entitat %s", "commands.attribute.failed.no_attribute": "L'entitat %s no té l'atribut %s", "commands.attribute.failed.no_modifier": "L'atribut %s de l'entitat %s no té el modificador %s", "commands.attribute.modifier.add.success": "S'ha afegit el modificador %s a l'atribut %s de l'entitat %s", "commands.attribute.modifier.remove.success": "S'ha eliminat el modificador %s a l'atribut %s de l'entitat %s", "commands.attribute.modifier.value.get.success": "El valor del modificador %s en l'atribut %s de l'entitat %s és %s", "commands.attribute.value.get.success": "El valor de l'atribut %s de l'entitat %s és %s", "commands.ban.failed": "<PERSON>s ha canvia<PERSON>, el jugador ja estava bloquejat", "commands.ban.success": "S'ha bloquejat a %s: %s", "commands.banip.failed": "<PERSON>s ha canviat, la IP ja estava bloquejada", "commands.banip.info": "Este bloqueig afecta a %s jugador/s: %s", "commands.banip.invalid": "IP no vàlida o jugador desconegut", "commands.banip.success": "S'ha bloquejat la IP %s: %s", "commands.banlist.entry": "%s ha sigut bloquejat per %s: %s", "commands.banlist.entry.unknown": "(Desconegut)", "commands.banlist.list": "Hi ha %s compte/s bloquejat/s:", "commands.banlist.none": "No hi ha comptes bloquejats", "commands.bossbar.create.failed": "Ja existix una barra de l'enemic final amb l'ID \"%s\"", "commands.bossbar.create.success": "S'ha creat la barra personalitzada de l'enemic final %s", "commands.bossbar.get.max": "La barra personalitzada d'enemic final %s té un valor màxim de %s", "commands.bossbar.get.players.none": "La barra personalitzada d'enemic final %s no té ara cap jugador en línia", "commands.bossbar.get.players.some": "La barra personalitzada d'enemic final %s té ara %s jugador/s en línia: %s", "commands.bossbar.get.value": "La barra personalitzada d'enemic final %s té un valor de %s", "commands.bossbar.get.visible.hidden": "La barra personalitzada d'enemic final %s està oculta", "commands.bossbar.get.visible.visible": "S'està mostrant la barra personalitzada d'enemic final %s", "commands.bossbar.list.bars.none": "No hi ha cap barra personalitzada d'enemic final activa", "commands.bossbar.list.bars.some": "Hi ha %s barres personalitzades d'enemic final actives: %s", "commands.bossbar.remove.success": "S'ha eliminat la barra personalitzada de l'enemic final %s", "commands.bossbar.set.color.success": "S'ha canviat el color de la barra personalitzada d'enemic final %s", "commands.bossbar.set.color.unchanged": "Res ha canviat, eixe ja era el color de la barra de l'enemic final", "commands.bossbar.set.max.success": "La barra personalitzada d'enemic final %s ha canviat el valor màxim a %s", "commands.bossbar.set.max.unchanged": "Res ha canviat, eixe ja era el màxim de la barra de l'enemic final", "commands.bossbar.set.name.success": "S'ha canviat el nom de la barra personalitzada d'enemic final %s", "commands.bossbar.set.name.unchanged": "Res ha canviat, eixe ja era el nom de la barra de l'enemic final", "commands.bossbar.set.players.success.none": "La barra personalitzada d'enemic final %s no té cap jugador", "commands.bossbar.set.players.success.some": "La barra personalitzada d'enemic final %s té ara %s jugador/s: %s", "commands.bossbar.set.players.unchanged": "Res ha canviat, eixos jugadors ja estaven a la barra de l'enemic final sense ningú per afegir o eliminar", "commands.bossbar.set.style.success": "S'ha canviat l'estil de la barra personalitzada d'enemic final %s", "commands.bossbar.set.style.unchanged": "Res ha canviat, eixe ja era l'estil de la barra de l'enemic final", "commands.bossbar.set.value.success": "La barra personalitzada d'enemic final %s ha canviat el seu valor a %s", "commands.bossbar.set.value.unchanged": "Res ha canviat, eixe ja era el valor de la barra de l'enemic final", "commands.bossbar.set.visibility.unchanged.hidden": "Res ha canviat, la barra de l'enemic final ja estava oculta", "commands.bossbar.set.visibility.unchanged.visible": "Res ha canviat, la barra de l'enemic final ja era visible", "commands.bossbar.set.visible.success.hidden": "La barra personalitzada d'enemic final %s està ara oculta", "commands.bossbar.set.visible.success.visible": "La barra personalitzada d'enemic final %s és ara visible", "commands.bossbar.unknown": "No existix cap barra de l'enemic final amb l'ID \"%s\"", "commands.clear.success.multiple": "S'han eliminat %s objectes de %s jugadors", "commands.clear.success.single": "S'han eliminat %s objectes del jugador %s", "commands.clear.test.multiple": "S'han trobat %s objectes coincidents en %s jugadors", "commands.clear.test.single": "S'han trobat %s objectes coincidents en el jugador %s", "commands.clone.failed": "No s'han clonat blocs", "commands.clone.overlap": "L'àrea d'origen i l'àrea de destí no poden coincidir", "commands.clone.success": "S'han clonat correctament %s blocs", "commands.clone.toobig": "Hi ha massa blocs a l'àrea especificada (el màxim és %s, s'han especificat %s)", "commands.damage.invulnerable": "L'objectiu és immune al dany escollit", "commands.damage.success": "S'ha aplicat %s de dany a %s", "commands.data.block.get": "%s al bloc %s, %s, %s després de l'escala de %s és %s", "commands.data.block.invalid": "El bloc especificat no és una bloc-entitat", "commands.data.block.modified": "S'ha modificat les dades del bloc en %s, %s, %s", "commands.data.block.query": "%s, %s, %s té les dades de bloc %s", "commands.data.entity.get": "%s en %s després de l'escala de %s és %s", "commands.data.entity.invalid": "No s'han pogut modificar les dades del jugador", "commands.data.entity.modified": "S'han modificat les dades de l'entitat %s", "commands.data.entity.query": "%s té les dades d'entitat %s", "commands.data.get.invalid": "No s'ha pogut obtenir %s, només es permeten etiquetes numèriques", "commands.data.get.multiple": "L'argument accepta només un valor NBT", "commands.data.get.unknown": "No s'ha pogut obtenir %s, l'etiqueta no existix", "commands.data.merge.failed": "Res ha canviat. Les propietats especificades ja tenien eixos valors", "commands.data.modify.expected_list": "S'esperava una llista i s'ha trobat %s", "commands.data.modify.expected_object": "S'esperava un objecte i s'ha trobat %s", "commands.data.modify.expected_value": "Valor esperat, obtingut: %s", "commands.data.modify.invalid_index": "L'índex de llista no és vàlid: %s", "commands.data.modify.invalid_substring": "Índexs de la subcadena no vàlids: %s a %s", "commands.data.storage.get": "%s al contenidor %s després d'un factor d'escalada de %s són %s", "commands.data.storage.modified": "S'ha modificat el contenidor %s", "commands.data.storage.query": "El contenidor %s té el següent contingut: %s", "commands.datapack.create.already_exists": "El pack amb el nom \"%s\" ja existeix", "commands.datapack.create.invalid_full_name": "\"%s\" no és un nom vàlid per al nou paquet", "commands.datapack.create.invalid_name": "El pac amb el nom \"%s\" té caràcters invàlids", "commands.datapack.create.io_failure": "No es pot crear el pack amb el nom \"%s\", revisa els registres", "commands.datapack.create.metadata_encode_failure": "No s'han pogut codificar les metadades del paquet anomenat \"%s\": %s", "commands.datapack.create.success": "S'ha creat un pack buit amb el nom \"%s\"", "commands.datapack.disable.failed": "El paquet de dades \"%s\" no està activat!", "commands.datapack.disable.failed.feature": "No es pot desactivar el paquet de dades \"%s\", ja que és part d'una variable activada!", "commands.datapack.enable.failed": "El paquet de dades \"%s\" ja estava activat!", "commands.datapack.enable.failed.no_flags": "El paquet de dades \"%s\" no es pot activar, ja que les variables necessàries no estan activades en este món: %s!", "commands.datapack.list.available.none": "No hi ha més paquets de dades disponibles", "commands.datapack.list.available.success": "Hi ha %s paquet/s de dades disponible/s: %s", "commands.datapack.list.enabled.none": "No hi ha paquets de dades activats", "commands.datapack.list.enabled.success": "Hi ha %s paquet/s de dades activat/s: %s", "commands.datapack.modify.disable": "Desactivant el paquet de dades %s", "commands.datapack.modify.enable": "Activant el paquet de dades %s", "commands.datapack.unknown": "Paquet de dades desconegut: %s", "commands.debug.alreadyRunning": "L'analitzador de tics ja està iniciat", "commands.debug.function.noRecursion": "No s'ha pogut traçar des de dins de la funció", "commands.debug.function.noReturnRun": "El seguiment no es pot utilitzar amb el retorn d'execució", "commands.debug.function.success.multiple": "S'han traçat %s comandaments de %s funcions al fitxer %s", "commands.debug.function.success.single": "S'han traçat %s comandaments de la funció \"%s\" al fitxer %s", "commands.debug.function.traceFailed": "No s'ha pogut traçar la funció", "commands.debug.notRunning": "L'analitzador de tics no s'ha iniciat", "commands.debug.started": "S'ha iniciat l'analitzador de tics", "commands.debug.stopped": "S'ha parat l'anàlisi de cicles després de %s segons i %s cicles (%s cicles per segon)", "commands.defaultgamemode.success": "El mode de joc per defecte és ara %s", "commands.deop.failed": "Res ha canviat. El jugador no és administrador", "commands.deop.success": "%s ja no és administrador", "commands.dialog.clear.multiple": "S'ha borrat el diàleg per a %s jugadors", "commands.dialog.clear.single": "S'ha borrat el diàleg per a %s", "commands.dialog.show.multiple": "S'ha mostrat el diàleg per a %s jugadors", "commands.dialog.show.single": "S'ha mostrat el diàleg per a %s", "commands.difficulty.failure": "La dificultat no ha canviat, ja estava configurada en %s", "commands.difficulty.query": "La dificultat és %s", "commands.difficulty.success": "S'ha canviat la dificultat a %s", "commands.drop.no_held_items": "L'entitat no pot sostenir cap objecte", "commands.drop.no_loot_table": "L'entitat %s no té cap taula de botins", "commands.drop.no_loot_table.block": "El bloc %s no té cap taula de botins", "commands.drop.success.multiple": "S'han amollat %s objectes", "commands.drop.success.multiple_with_table": "S'han amollat %s objectes de la taula de botins %s", "commands.drop.success.single": "S'ha amollat %s %s", "commands.drop.success.single_with_table": "S'han amollat %s %s de la taula de botins %s", "commands.effect.clear.everything.failed": "L'objectiu no té efectes a eliminar", "commands.effect.clear.everything.success.multiple": "S'han eliminat tots els efectes a %s objectius", "commands.effect.clear.everything.success.single": "S'han eliminat tots els efectes a %s", "commands.effect.clear.specific.failed": "L'objectiu no té l'efecte especificat", "commands.effect.clear.specific.success.multiple": "S'ha eliminat l'efecte %s a %s objectius", "commands.effect.clear.specific.success.single": "S'ha eliminat l'efecte %s a %s", "commands.effect.give.failed": "No s'ha pogut aplicar l'efecte (l'objectiu és immune als efectes, o en té algun més fort)", "commands.effect.give.success.multiple": "S'ha aplicat l'efecte %s a %s objectius", "commands.effect.give.success.single": "S'ha aplicat l'efecte %s a %s", "commands.enchant.failed": "Res ha canviat. Els objectius no tenen cap objecte a les mans o l'encanteri no es pot aplicar", "commands.enchant.failed.entity": "%s no és una entitat vàlida per al comandament", "commands.enchant.failed.incompatible": "%s no admet eixe encanteri", "commands.enchant.failed.itemless": "%s no està subjectant cap objecte", "commands.enchant.failed.level": "%s és superior al nivell màxim de l'encanteri (%s)", "commands.enchant.success.multiple": "S'ha aplicat l'encanteri %s a %s entitats", "commands.enchant.success.single": "S'ha aplicat l'encanteri %s a l'objecte %s", "commands.execute.blocks.toobig": "Hi ha massa blocs a l'àrea especificada (el màxim és %s, s'han especificat %s)", "commands.execute.conditional.fail": "Prova no superada", "commands.execute.conditional.fail_count": "Prova no superada, quantitat: %s", "commands.execute.conditional.pass": "Prova superada", "commands.execute.conditional.pass_count": "Prova superada, quantitat: %s", "commands.execute.function.instantiationFailure": "No s'ha pogut instanciar la funció %s: %s", "commands.experience.add.levels.success.multiple": "S'han donat %s nivells d'experiència a %s jugadors", "commands.experience.add.levels.success.single": "S'han donat %s nivells d'experiència a %s", "commands.experience.add.points.success.multiple": "S'han donat %s punts d'experiència a %s jugadors", "commands.experience.add.points.success.single": "S'han donat %s punts d'experiència a %s", "commands.experience.query.levels": "%s té %s nivells d'experiència", "commands.experience.query.points": "%s té %s punts d'experiència", "commands.experience.set.levels.success.multiple": "S'han establit %s nivells d'experiència a %s jugadors", "commands.experience.set.levels.success.single": "S'han establit %s nivells d'experiència a %s", "commands.experience.set.points.invalid": "No es poden establir punts d'experiència per damunt del màxim de punts per al nivell actual del jugador", "commands.experience.set.points.success.multiple": "S'han establit %s punts d'experiència a %s jugadors", "commands.experience.set.points.success.single": "S'han establit %s punts d'experiència a %s", "commands.fill.failed": "No s'han posat blocs", "commands.fill.success": "S'han omplit correctament %s bloc/s", "commands.fill.toobig": "Hi ha massa blocs a l'àrea especificada (el màxim és %s, s'han especificat %s)", "commands.fillbiome.success": "S'han establit els biomes entre %s, %s, %s i %s, %s, %s", "commands.fillbiome.success.count": "S'han establit %s entrada/es de bioma entre %s, %s, %s i %s, %s, %s", "commands.fillbiome.toobig": "Hi ha massa blocs al volum especificat (màxim %s, especificat %s)", "commands.forceload.added.failure": "No s'ha marcat cap chunk per forçar-ne la càrrega", "commands.forceload.added.multiple": "S'han marcat %s chunks en %s de %s a %s per forçar-ne la càrrega", "commands.forceload.added.none": "No s'ha trobat cap chunk carregar a la força en %s", "commands.forceload.added.single": "S'ha marcat el chunk %s en %s per forçar-ne la càrrega", "commands.forceload.list.multiple": "S'han trobat %s chunks carregats a la força en %s a: %s", "commands.forceload.list.single": "S'ha trobat un chunk carregat a la força en %s a: %s", "commands.forceload.query.failure": "El chunk de %s en %s no està marcat per forçar-ne la càrrega", "commands.forceload.query.success": "El chunk de %s en %s està marcat per forçar-ne la càrrega", "commands.forceload.removed.all": "S'han desmarcat tots els chunks carregats a la força en %s", "commands.forceload.removed.failure": "No s'ha deixat de forçar la càrrega de cap chunk", "commands.forceload.removed.multiple": "S'han desmarcat %s chunks en %s de %s a %s per forçar-ne la càrrega", "commands.forceload.removed.single": "S'ha desmarcat el chunk de %s en %s per forçar-ne la càrrega", "commands.forceload.toobig": "Massa chunks a la zona especificada (el màxim és %s, s'han especificat %s)", "commands.function.error.argument_not_compound": "El tipus d'argument no és vàlid: %s, s'esperava un compost", "commands.function.error.missing_argument": "Falta l'argument %2$s en la funció %1$s", "commands.function.error.missing_arguments": "Falten arguments en la funció %s", "commands.function.error.parse": "Mentre s'instanciava la macro %s: el comandament \"%s\" ha causat un error: %s", "commands.function.instantiationFailure": "No s'ha pogut instanciar la funció %s: %s", "commands.function.result": "La funció %s ha retornat %s", "commands.function.scheduled.multiple": "Executant les funcions %s", "commands.function.scheduled.no_functions": "No s'ha trobat cap funció per al nom %s", "commands.function.scheduled.single": "Executant les funcions %s", "commands.function.success.multiple": "S'han executat %s comandaments de %s funcions", "commands.function.success.multiple.result": "S'han executat %s funcions", "commands.function.success.single": "S'han executat %s comandaments de la funció \"%s\"", "commands.function.success.single.result": "La funció \"%2$s\" ha tornat %1$s", "commands.gamemode.success.other": "Has canviat el mode de joc de %s a %s", "commands.gamemode.success.self": "Has canviat el teu mode de joc a %s", "commands.gamerule.query": "La regla %s està establida a %s", "commands.gamerule.set": "La regla %s s'ha establit ara a %s", "commands.give.failed.toomanyitems": "No es poden donar més de %s de %s", "commands.give.success.multiple": "S'han donat %s %s a %s jugadors", "commands.give.success.single": "S'han donat %s %s a %s", "commands.help.failed": "Comandament desconegut o permisos insuficients", "commands.item.block.set.success": "S'ha substituït un espai en %s, %s, %s amb %s", "commands.item.entity.set.success.multiple": "S'ha substituït un espai en %s entitats amb %s", "commands.item.entity.set.success.single": "S'ha substituït un espai en %s amb %s", "commands.item.source.no_such_slot": "L'origen no té el solc %s", "commands.item.source.not_a_container": "La posició origen indicada %s, %s, %s no és un contenidor", "commands.item.target.no_changed.known_item": "Cap objectiu ha acceptat l'objecte %s al solc %s", "commands.item.target.no_changes": "Cap objectiu ha acceptat l'objecte al solc %s", "commands.item.target.no_such_slot": "L'objectiu no té l'espai %s", "commands.item.target.not_a_container": "La posició objectiu indicada %s, %s, %s no és un contenidor", "commands.jfr.dump.failed": "Error en copiar el registre JFR: %s", "commands.jfr.start.failed": "No s'ha pogut iniciar el perfilat JFR", "commands.jfr.started": "S'ha iniciat el perfilat JFR", "commands.jfr.stopped": "S'ha parat el perfilat JFR i s'ha copiat a %s", "commands.kick.owner.failed": "No pots expulsar a l'amo del servidor LAN", "commands.kick.singleplayer.failed": "No es pot expulsar en una partida d'un jugador fora de línia", "commands.kick.success": "S'ha expulsat a %s: %s", "commands.kill.success.multiple": "S'han eliminat %s entitats", "commands.kill.success.single": "S'ha assassinat a %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Hi ha %s de %s jugadors en línia: %s", "commands.locate.biome.not_found": "No s'ha trobat cap bioma pròxim del tipus \"%s\"", "commands.locate.biome.success": "El %s més proper està a %s (%s blocs)", "commands.locate.poi.not_found": "No s'ha trobat cap punt d'interés del tipus \"%s\" a una distància raonable", "commands.locate.poi.success": "El %s més proper està a %s (%s blocs)", "commands.locate.structure.invalid": "No hi ha cap estructura amb el tipus \"%s\"", "commands.locate.structure.not_found": "No s'ha trobat cap estructura del tipus \"%s\" a prop", "commands.locate.structure.success": "El %s més proper està a %s (%s blocs)", "commands.message.display.incoming": "%s t'ha xiuxiuejat el següent: %s", "commands.message.display.outgoing": "Has xiuxiuejat a %s el següent: %s", "commands.op.failed": "<PERSON><PERSON> ha canviat, el jugador ja era administrador", "commands.op.success": "%s ara és administrador", "commands.pardon.failed": "Res ha canviat, el jugador no estava bloquejat", "commands.pardon.success": "S'ha desbloquejat a %s", "commands.pardonip.failed": "Res ha canviat, l'adreça IP no estava bloquejada", "commands.pardonip.invalid": "L'adreça IP no és vàlida", "commands.pardonip.success": "S'ha desbloquejat la IP %s", "commands.particle.failed": "La partícula no era visible per ningú", "commands.particle.success": "Mostrant la partícula %s", "commands.perf.alreadyRunning": "L'analitzador de rendiment ja està iniciat", "commands.perf.notRunning": "L'analitzador de rendiment no s'ha iniciat", "commands.perf.reportFailed": "No s'ha pogut crear un informe de depuració", "commands.perf.reportSaved": "S'ha creat un informe de depuració a %s", "commands.perf.started": "S'ha iniciat l'anàlisi de rendiment per 10 segons (\"perf stop\" per parar-ho abans)", "commands.perf.stopped": "S'ha parat l'anàlisi de rendiment després de %s segons i %s tic/s (%s tic/s per segon)", "commands.place.feature.failed": "No s'ha pogut col·locar l'element", "commands.place.feature.invalid": "No hi ha cap element amb el tipus \"%s\"", "commands.place.feature.success": "S'ha col·locat \"%s\" a %s, %s, %s", "commands.place.jigsaw.failed": "Ha fallat la generació del trencaclosques", "commands.place.jigsaw.invalid": "No hi ha cap grup de plantilles del tipus \"%s\"", "commands.place.jigsaw.success": "S'ha generat un trencaclosques a %s, %s, %s", "commands.place.structure.failed": "Ha fallat la col·locació de l'estructura", "commands.place.structure.invalid": "No hi ha cap estructura amb el tipus \"%s\"", "commands.place.structure.success": "S'ha generat l'estructura \"%s\" a %s, %s, %s", "commands.place.template.failed": "No s'ha pogut col·locar la plantilla", "commands.place.template.invalid": "No hi ha cap plantilla amb l'id \"%s\"", "commands.place.template.success": "S'ha carregat la plantilla \"%s\" a %s, %s, %s", "commands.playsound.failed": "El so està massa lluny per escoltar-lo", "commands.playsound.success.multiple": "S'ha reproduït el so %s a %s jugadors", "commands.playsound.success.single": "S'ha reproduït el so %s a %s", "commands.publish.alreadyPublished": "La partida multijugador ja s'allotja al port %s", "commands.publish.failed": "Impossible hostatjar el joc local", "commands.publish.started": "Joc local hostatjat al port %s", "commands.publish.success": "La partida multijugador està ara allotjada al port %s", "commands.random.error.range_too_large": "El rang del valor aleatori ha de ser com a màxim 2147483646", "commands.random.error.range_too_small": "El rang del valor aleatori ha de ser com a mínim 2", "commands.random.reset.all.success": "Reinicia %s seqüències aleatòries", "commands.random.reset.success": "Reinicia la seqüència aleatòria %s", "commands.random.roll": "%s ha rodat %s (de %s a %s)", "commands.random.sample.success": "Valor aleatoritzat: %s", "commands.recipe.give.failed": "No s'han aprés noves receptes", "commands.recipe.give.success.multiple": "S'han desbloquejat %s receptes per a %s jugadors", "commands.recipe.give.success.single": "S'han desbloquejat %s receptes per a %s", "commands.recipe.take.failed": "Les receptes no es poden oblidar", "commands.recipe.take.success.multiple": "S'han eliminat %s receptes de %s jugadors", "commands.recipe.take.success.single": "S'han eliminat %s receptes de %s", "commands.reload.failure": "Error en carregar amb les dades antigues", "commands.reload.success": "Recarregant!", "commands.ride.already_riding": "%s ja està muntat en %s", "commands.ride.dismount.success": "%s ha deixat de muntar en %s", "commands.ride.mount.failure.cant_ride_players": "Els jugadors no poden ser cavalcats", "commands.ride.mount.failure.generic": "%s no ha pogut començar a muntar en %s", "commands.ride.mount.failure.loop": "No es pot cavalcar una entitat en sí mateixa o en els seus passatgers", "commands.ride.mount.failure.wrong_dimension": "No pots cavalcar una entitat en una altra dimensió", "commands.ride.mount.success": "%s ha començat a muntar en %s", "commands.ride.not_riding": "%s no està muntant cap vehicle", "commands.rotate.success": "S'ha rotat %s", "commands.save.alreadyOff": "La funció de guardat ja està desactivada", "commands.save.alreadyOn": "La funció de guardat ja està activada", "commands.save.disabled": "El guardat automàtic està ara desactivat", "commands.save.enabled": "El guardat automàtic està ara activat", "commands.save.failed": "Error en guardar la partida (hi ha prou espai al disc dur?)", "commands.save.saving": "Guardant el joc (serà un moment!)", "commands.save.success": "S'ha guardat el joc", "commands.schedule.cleared.failure": "No hi ha cap planificació amb la id %s", "commands.schedule.cleared.success": "S'han eliminat %s planificacions amb id %s", "commands.schedule.created.function": "S'ha programat la funció \"%s\" en %s tic/s en temps de joc %s", "commands.schedule.created.tag": "S'ha programat l'etiqueta \"%s\" en %s tics en temps de joc %s", "commands.schedule.macro": "No s'ha pogut programar una macro", "commands.schedule.same_tick": "No es pot programar per al tic actual", "commands.scoreboard.objectives.add.duplicate": "Ja existeix un objectiu amb eixe nom", "commands.scoreboard.objectives.add.success": "S'ha creat l'objectiu %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Res ha canviat, l'espai ja estava buit", "commands.scoreboard.objectives.display.alreadySet": "Res ha canviat, l'espai ja estava mostrant l'objectiu", "commands.scoreboard.objectives.display.cleared": "S'han netejat tots els objectius de la pantalla %s", "commands.scoreboard.objectives.display.set": "S'ha establit que la pantalla %s mostre l'objectiu %s", "commands.scoreboard.objectives.list.empty": "No hi ha objectius", "commands.scoreboard.objectives.list.success": "Hi ha %s objectiu/s: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "S'ha desactivat la notificació d'actualització automàtica per a l'objectiu %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "S'ha activat la notificació d'actualització automàtica per a l'objectiu %s", "commands.scoreboard.objectives.modify.displayname": "S'ha canviat el nom per mostrar de %s a %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "S'ha esborrat el format per defecte del número de l'objectiu %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "S'ha canviat el format per defecte del número de l'objectiu %s", "commands.scoreboard.objectives.modify.rendertype": "S'ha canviat el tipus de renderitzat de l'objectiu %s", "commands.scoreboard.objectives.remove.success": "S'ha eliminat l'objectiu %s", "commands.scoreboard.players.add.success.multiple": "S'ha afegit %s a %s per a %s entitats", "commands.scoreboard.players.add.success.single": "S'ha afegit %s a %s per a %s (ara té %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "S'ha esborrat el nom per a mostrar per a %s entitats en %s", "commands.scoreboard.players.display.name.clear.success.single": "S'ha esborrat el nom per a mostrar per a %s en %s", "commands.scoreboard.players.display.name.set.success.multiple": "S'ha canviat el nom per a mostrar a %s per a %s entitats en %s", "commands.scoreboard.players.display.name.set.success.single": "S'ha canviat el nom per a mostrar a %s per a %s en %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "S'ha esborrat el format del número per a %s entitats en %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "S'ha esborrat el format del número per a %s en %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "S'ha canviat el format del número per a %s entitats en %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "S'ha canviat el format del número per a %s en %s", "commands.scoreboard.players.enable.failed": "Res ha canviat, l'activador ja estava activat", "commands.scoreboard.players.enable.invalid": "La funció \"activa\" només servix per a objectius del tipus \"activador\"", "commands.scoreboard.players.enable.success.multiple": "S'ha activat el desencadenant %s per a %s entitats", "commands.scoreboard.players.enable.success.single": "S'ha activat el causant %s per a %s", "commands.scoreboard.players.get.null": "No s'ha pogut obtenir la puntuació de l'objectiu %s per a %s; no hi ha registre", "commands.scoreboard.players.get.success": "%s té %s %s", "commands.scoreboard.players.list.empty": "No hi ha entitats registrades", "commands.scoreboard.players.list.entity.empty": "%s no té puntuacions per mostrar", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s té %s puntuacions:", "commands.scoreboard.players.list.success": "Hi ha %s entitat/s registrada/es: %s", "commands.scoreboard.players.operation.success.multiple": "S'ha actualitzat %s per a %s entitats", "commands.scoreboard.players.operation.success.single": "S'ha establit %s per a %s a %s", "commands.scoreboard.players.remove.success.multiple": "S'ha afegit %s a %s per a %s entitats", "commands.scoreboard.players.remove.success.single": "S'ha eliminat %s de %s per a %s (ara té %s)", "commands.scoreboard.players.reset.all.multiple": "S'han reiniciat totes les puntuacions per a %s entitats", "commands.scoreboard.players.reset.all.single": "S'han reiniciat totes les puntuacions per a %s", "commands.scoreboard.players.reset.specific.multiple": "S'ha reiniciat %s per a %s entitats", "commands.scoreboard.players.reset.specific.single": "S'ha reiniciat %s per a %s", "commands.scoreboard.players.set.success.multiple": "S'ha establit %s per a %s entitats a %s", "commands.scoreboard.players.set.success.single": "S'ha establit %s per a %s a %s", "commands.seed.success": "Llavor: %s", "commands.setblock.failed": "No s'ha pogut col·locar el bloc", "commands.setblock.success": "S'ha canviat el bloc en %s, %s, %s", "commands.setidletimeout.success": "El temps d'inactivitat dels jugadors és ara de %s minuts", "commands.setidletimeout.success.disabled": "El temps d'inactivitat del jugador està ara desactivat", "commands.setworldspawn.failure.not_overworld": "Només pots establir el punt de reaparició a la superfície", "commands.setworldspawn.success": "S'ha establit el punt de reaparició del món a %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "S'ha establit el punt de reaparició a %s, %s, %s [%s] a %s per a %s jugadors", "commands.spawnpoint.success.single": "S'ha establit el punt de reaparició %s, %s, %s [%s] a %s per a %s", "commands.spectate.not_spectator": "%s no està en mode espectador", "commands.spectate.self": "No pots observar-te a tu mateix", "commands.spectate.success.started": "Estàs observant a %s", "commands.spectate.success.stopped": "Ja no estàs observant cap entitat", "commands.spreadplayers.failed.entities": "No s'han pogut repartir %s entitats al voltant de %s, %s (hi ha massa entitats per l'espai - prova a repartir-les com a màxim a %s)", "commands.spreadplayers.failed.invalid.height": "El maxHeight %s no és vàlid; s'esperava un major que el mínim per al món %s", "commands.spreadplayers.failed.teams": "No s'han pogut repartir %s equips al voltant de %s, %s (hi ha massa entitats per l'espai - prova a repartir-los com a màxim a %s)", "commands.spreadplayers.success.entities": "S'han escampat %s entitats al voltant de %s, %s amb una distància mitjana entre ells de %s blocs", "commands.spreadplayers.success.teams": "S'han escampat %s equips al voltant de %s, %s amb una distància mitjana de separació entre ells de %s blocs", "commands.stop.stopping": "Parant el servidor", "commands.stopsound.success.source.any": "S'han parat tots els sons de \"%s\"", "commands.stopsound.success.source.sound": "S'ha parat el so \"%s\" de la font \"%s\"", "commands.stopsound.success.sourceless.any": "<PERSON>'han parat tots els sons", "commands.stopsound.success.sourceless.sound": "S'ha parat el so \"%s\"", "commands.summon.failed": "No s'ha pogut generar l'entitat", "commands.summon.failed.uuid": "No s'ha pogut invocar l'entitat degut a la duplicació de les UUIDs", "commands.summon.invalidPosition": "La posició no és vàlida per invocar", "commands.summon.success": "S'ha generat un/a nou/va %s", "commands.tag.add.failed": "L'objectiu o bé ja té l'etiqueta o bé en té massa", "commands.tag.add.success.multiple": "S'ha afegit l'etiqueta \"%s\" a %s entitats", "commands.tag.add.success.single": "S'ha afegit l'etiqueta \"%s\" a %s", "commands.tag.list.multiple.empty": "No hi ha etiquetes en %s entitats", "commands.tag.list.multiple.success": "%s entitats tenen un total de %s etiquetes: %s", "commands.tag.list.single.empty": "%s no té etiquetes", "commands.tag.list.single.success": "%s té %s etiquetes: %s", "commands.tag.remove.failed": "L'objectiu no té eixa etiqueta", "commands.tag.remove.success.multiple": "S'ha eliminat l'etiqueta \"%s\" de %s entitats", "commands.tag.remove.success.single": "S'ha eliminat l'etiqueta \"%s\" de %s", "commands.team.add.duplicate": "Ja existix un equip amb eixe nom", "commands.team.add.success": "S'ha creat l'equip %s", "commands.team.empty.success": "S'han eliminat %s membres de l'equip %s", "commands.team.empty.unchanged": "Res ha canviat, l'equip ja estava buit", "commands.team.join.success.multiple": "S'han afegit %s membres per a l'equip %s", "commands.team.join.success.single": "S'ha afegit %s a l'equip %s", "commands.team.leave.success.multiple": "S'han eliminat %s membres de tots els equips", "commands.team.leave.success.single": "S'ha eliminat %s de tots els equips", "commands.team.list.members.empty": "L'equip %s no té cap membre", "commands.team.list.members.success": "L'equip %s té %s membre/s: %s", "commands.team.list.teams.empty": "No hi ha equips", "commands.team.list.teams.success": "Hi ha %s equip/s: %s", "commands.team.option.collisionRule.success": "La regla de col·lisió per a l'equip %s és ara \"%s\"", "commands.team.option.collisionRule.unchanged": "Res ha canviat, la norma de col·lisió ja tenia eixe valor", "commands.team.option.color.success": "S'ha actualitzat el color de l'equip %s a %s", "commands.team.option.color.unchanged": "Res ha canviat, l'equip ja tenia eixe color", "commands.team.option.deathMessageVisibility.success": "La visibilitat del missatge de mort de l'equip %s és ara \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Res ha canviat, la visualització dels missatges de mort ja tenia eixe valor", "commands.team.option.friendlyfire.alreadyDisabled": "Res ha canviat, el foc amic ja estava desactivat per a l'equip", "commands.team.option.friendlyfire.alreadyEnabled": "Res ha canviat, el foc amic ja estava activat per a l'equip", "commands.team.option.friendlyfire.disabled": "S'ha desactivat el foc amic per a l'equip %s", "commands.team.option.friendlyfire.enabled": "S'ha activat el foc amic per a l'equip %s", "commands.team.option.name.success": "S'ha actualitzat el nom de l'equip a %s", "commands.team.option.name.unchanged": "No ha canviat res. L'equip ja tenia eixe nom", "commands.team.option.nametagVisibility.success": "La visibilitat dels noms dels jugadors per a l'equip %s és ara \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Res ha canviat, la visibilitat dels noms dels jugadors ja tenia eixe valor", "commands.team.option.prefix.success": "S'ha establit el prefix de l'equip a %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Res ha canvia<PERSON>, els membres de l'equip ja no podien veure els seus companys invisibles", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Res ha canviat, eixe equip ja podia vore els seus companys invisibles", "commands.team.option.seeFriendlyInvisibles.disabled": "L'equip %s ja no pot veure els seus companys invisibles", "commands.team.option.seeFriendlyInvisibles.enabled": "L'equip %s ara pot veure els seus companys invisibles", "commands.team.option.suffix.success": "S'ha establit el sufix de l'equip a %s", "commands.team.remove.success": "Eliminat l'equip %s", "commands.teammsg.failed.noteam": "Has d'estar en un equip per poder enviar-li un missatge", "commands.teleport.invalidPosition": "La posició no és vàlida per teletransportar-te", "commands.teleport.success.entity.multiple": "S'han teletransportat %s entitats a %s", "commands.teleport.success.entity.single": "Teletransportat %s a %s", "commands.teleport.success.location.multiple": "S'han teletransportat %s entitats a %s, %s, %s", "commands.teleport.success.location.single": "Teletransportat %s a %s,%s,%s", "commands.test.batch.starting": "Iniciant entorn %s del grup %s", "commands.test.clear.error.no_tests": "No s'ha pogut trobar cap prova per a eliminar", "commands.test.clear.success": "S'han eliminat %s estructures", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Clica per a copiar-ho al porta-retalls", "commands.test.create.success": "S'ha creat un test de configuració per al test %s", "commands.test.error.no_test_containing_pos": "No s'ha trobat la instància de prova que conté %s, %s, %s", "commands.test.error.no_test_instances": "No s'han trobat instàncies de proves", "commands.test.error.non_existant_test": "La prova %s no s'ha trobat", "commands.test.error.structure_not_found": "L'estructura de prova %s no s'ha trobat", "commands.test.error.test_instance_not_found": "No s'ha pogut trobar la instància de prova per al bloc-entitat", "commands.test.error.test_instance_not_found.position": "No s'ha pogut trobar la instància de prova per al bloc-entitat, per a la prova a %s, %s, %s", "commands.test.error.too_large": "La grandària de l'estructura ha de ser menor a %s blocs en cada eix", "commands.test.locate.done": "Localització finalitzada. S'han trobat %s estructures", "commands.test.locate.found": "Estructura trobada a: %s (distància: %s)", "commands.test.locate.started": "Localització d'estructures de prova iniciada. Açò pot tardar un poc...", "commands.test.no_tests": "No hi ha proves per a executar", "commands.test.relative_position": "Posició relativa a %s: %s", "commands.test.reset.error.no_tests": "No s'ha trobat cap prova per a reiniciar", "commands.test.reset.success": "L'estructura/estructures %s s'ha/han restablit", "commands.test.run.no_tests": "No s'han trobat proves", "commands.test.run.running": "Executant %s test/s...", "commands.test.summary": "Prova del joc completada! Executat/s %s test/s", "commands.test.summary.all_required_passed": "S'han superat tots els tests requerits :)", "commands.test.summary.failed": "Han fallat %s tests requerits :(", "commands.test.summary.optional_failed": "Han fallat %s tests opcionals", "commands.tick.query.percentiles": "Percentils: P50: %sms P95: %sms P99: %sms, mostra: %s", "commands.tick.query.rate.running": "Taxa de tics de l'objectiu: %s per segon.\nTemps mitjà per tic: %sms (Objectiu: %sms)", "commands.tick.query.rate.sprinting": "Taxa de tics de l'objectiu: %s per segon (ignorat, només de referència).\nTemps mitjà per tic: %sms", "commands.tick.rate.success": "Establix la taxa de tics de l'objectiu en %s per segon", "commands.tick.sprint.report": "S'ha completat la correguda amb %s tics per segon, o %s ms per tic", "commands.tick.sprint.stop.fail": "No hi ha cap correguda de tics en procés", "commands.tick.sprint.stop.success": "S'ha interromput una correguda de tic", "commands.tick.status.frozen": "El joc s'ha penjat", "commands.tick.status.lagging": "El joc s'està executant, però no es pot mantindre amb la taxa de tics de l'objectiu", "commands.tick.status.running": "El joc funciona amb normalitat", "commands.tick.status.sprinting": "El joc va a tota velocitat", "commands.tick.step.fail": "No s'ha pogut passar el joc - ha d'estar parat", "commands.tick.step.stop.fail": "No hi ha cap pas de tic en procés", "commands.tick.step.stop.success": "S'ha interromput un pas de tic", "commands.tick.step.success": "Passant %s tic/s", "commands.time.query": "Hora: %s", "commands.time.set": "S'ha canviat l'hora a %s", "commands.title.cleared.multiple": "S'han netejat els títols de %s jugadors", "commands.title.cleared.single": "S'han netejat els títols de %s", "commands.title.reset.multiple": "S'han reiniciat les opcions dels títols per a %s jugadors", "commands.title.reset.single": "S'han reiniciat les opcions dels títols per a %s", "commands.title.show.actionbar.multiple": "Mostrant el nou títol de barra d'acció per a %s jugadors", "commands.title.show.actionbar.single": "Mostrant el nou títol de barra d'acció per a %s", "commands.title.show.subtitle.multiple": "Mostrant nou subtítol per a %s jugadors", "commands.title.show.subtitle.single": "Mostrant nou subtítol per a %s", "commands.title.show.title.multiple": "Mostrant nou títol per a %s jugadors", "commands.title.show.title.single": "Mostrant nou títol per a %s", "commands.title.times.multiple": "S'ha canviat el temps de visualització del títol per a %s jugadors", "commands.title.times.single": "S'ha canviat el temps de visualització del títol per a %s", "commands.transfer.error.no_players": "Has d'especificar almenys un jugador per transferir", "commands.transfer.success.multiple": "Transferint %s jugadors a %s:%s", "commands.transfer.success.single": "Transferint %s a %s:%s", "commands.trigger.add.success": "%s desencadenat (s'ha afegit %s al valor)", "commands.trigger.failed.invalid": "Només pots activar objectius del tipus \"activador\"", "commands.trigger.failed.unprimed": "Encara no pots activar eixe objectiu", "commands.trigger.set.success": "%s desencadenat (s'ha establit el valor a %s)", "commands.trigger.simple.success": "%s desencadenat", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Informació de la versió del servidor:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No hi ha punts de ruta en %s", "commands.waypoint.list.success": "%s punts de ruta en %s: %s", "commands.waypoint.modify.color": "El color del punt de ruta ara és %s", "commands.waypoint.modify.color.reset": "Restableix el color del punt de ruta", "commands.waypoint.modify.style": "S'ha canviat l'estil del punt de ruta", "commands.weather.set.clear": "S'ha canviat l'oratge a ras", "commands.weather.set.rain": "S'ha canviat l'oratge a pluja", "commands.weather.set.thunder": "S'ha canviat l'oratge a pluja i tronada", "commands.whitelist.add.failed": "El jugador ja estava a la llista blanca", "commands.whitelist.add.success": "%s s'ha afegit a la llista blanca", "commands.whitelist.alreadyOff": "La llista blanca ja estava desactivada", "commands.whitelist.alreadyOn": "La llista blanca ja estava activada", "commands.whitelist.disabled": "S'ha desactivat la llista blanca", "commands.whitelist.enabled": "S'ha activat la llista blanca", "commands.whitelist.list": "Hi ha %s jugador/s a la llista blanca: %s", "commands.whitelist.none": "No hi ha cap jugador a la llista blanca", "commands.whitelist.reloaded": "Llista blanca recarregada", "commands.whitelist.remove.failed": "El jugador no està a la llista blanca", "commands.whitelist.remove.success": "%s s'ha eliminat de la llista blanca", "commands.worldborder.center.failed": "Res ha canviat, el centre del límit del món ja estava en eixa posició", "commands.worldborder.center.success": "S'ha establit el centre del límit del món a %s, %s", "commands.worldborder.damage.amount.failed": "Res ha canviat, el dany del límit del món ja era eixa quantitat", "commands.worldborder.damage.amount.success": "S'ha establit el dany del límit del món a %s per bloc per cada segon", "commands.worldborder.damage.buffer.failed": "Res ha canviat, la zona segura fora del món ja estava a eixa distància", "commands.worldborder.damage.buffer.success": "S'ha establit la zona segura del límit del món a %s blocs", "commands.worldborder.get": "El límit del món té actualment %s bloc/s d'ample", "commands.worldborder.set.failed.big": "La vora del món no pot ser més ampla que %s blocs", "commands.worldborder.set.failed.far": "El límit del món no pot estar més lluny que %s blocs", "commands.worldborder.set.failed.nochange": "Res ha canviat, el límit del món ja tenia eixa grandària", "commands.worldborder.set.failed.small": "El límit del món no pot ser menor que 1 bloc", "commands.worldborder.set.grow": "Ampliant el límit del món a %s blocs d'ample en %s segons", "commands.worldborder.set.immediate": "S'ha establit el límit del món a %s bloc/s d'ample", "commands.worldborder.set.shrink": "Reduint el límit del món a %s bloc/s d'ample en %s segon/s", "commands.worldborder.warning.distance.failed": "Res ha canviat, l'avís del límit del món ja tenia eixa distància", "commands.worldborder.warning.distance.success": "S'ha establit la distància d'advertència del límit del món a %s bloc/s", "commands.worldborder.warning.time.failed": "Res ha canviat, l'avís del límit del món ja durava eixe temps", "commands.worldborder.warning.time.success": "S'ha establit el temps d'advertència del límit del món a %s segon/s", "compliance.playtime.greaterThan24Hours": "Has estat jugant durant més de 24 hores", "compliance.playtime.hours": "Has estat jugant durant %s hora/(es)", "compliance.playtime.message": "Jugar a videojocs durant massa temps pot afectar a la teua vida diària", "connect.aborted": "<PERSON><PERSON><PERSON><PERSON>", "connect.authorizing": "Identificació en curs...", "connect.connecting": "Connectant amb el servidor...", "connect.encrypting": "Xifrant...", "connect.failed": "Error en connectar amb el servidor", "connect.failed.transfer": "La connexió ha fallat mentre es transferia al servidor", "connect.joining": "Entrant al món...", "connect.negotiating": "Connectant...", "connect.reconfiging": "Reconfigurant...", "connect.reconfiguring": "Reconfigurant...", "connect.transferring": "Transferint al nou servidor...", "container.barrel": "Barril", "container.beacon": "Far màgic", "container.beehive.bees": "Abelles: %s/%s", "container.beehive.honey": "Mel: %s/%s", "container.blast_furnace": "Alt forn", "container.brewing": "Altar de pocions", "container.cartography_table": "Taula de cartografia", "container.chest": "Cofre", "container.chestDouble": "Cofre gran", "container.crafter": "Fabricador", "container.crafting": "Manufacturar", "container.creative": "Selecció de l'objecte", "container.dispenser": "Dispensador", "container.dropper": "Subministrador", "container.enchant": "<PERSON><PERSON><PERSON><PERSON>", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s trossos de lapislàtzuli", "container.enchant.lapis.one": "1 Lapislàtzuli", "container.enchant.level.many": "%s nivells d'encanteri", "container.enchant.level.one": "1 nivell d'experiència", "container.enchant.level.requirement": "Nivell necessari: %s", "container.enderchest": "<PERSON><PERSON><PERSON>", "container.furnace": "Forn", "container.grindstone_title": "Repara i desencanta", "container.hopper": "Tremuja d'objectes", "container.inventory": "Inventari", "container.isLocked": "%s està bloquejat!", "container.lectern": "Faristol", "container.loom": "Teler", "container.repair": "Repara i reanomena", "container.repair.cost": "Cost de l'encanteri: %1$s", "container.repair.expensive": "Massa car!", "container.shulkerBox": "<PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "i %s més...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "No s'ha pogut obrir. El botí encara no s'ha generat.", "container.stonecutter": "Tallapedra", "container.upgrade": "Millora l'equipament", "container.upgrade.error_tooltip": "L'objecte no es pot millorar així", "container.upgrade.missing_template_tooltip": "Afig motle de ferreria", "controls.keybinds": "Controls...", "controls.keybinds.duplicateKeybinds": "Esta tecla ja s'utilitza per a:\n%s", "controls.keybinds.title": "Controls", "controls.reset": "Reinicia", "controls.resetAll": "Reinicia tecles", "controls.title": "Controls", "createWorld.customize.buffet.biome": "Tria un bioma", "createWorld.customize.buffet.title": "Personalització del món d'un sol bioma", "createWorld.customize.flat.height": "Alçada", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Límit inferior - %s", "createWorld.customize.flat.layer.top": "Límit superior - %s", "createWorld.customize.flat.removeLayer": "Eliminar la capa", "createWorld.customize.flat.tile": "Material de la capa", "createWorld.customize.flat.title": "Personalització de l'extraplà", "createWorld.customize.presets": "Predeterminats", "createWorld.customize.presets.list": "Com a alternativa, ací n'hi han alguns ja fets!", "createWorld.customize.presets.select": "Utilitza el predeterminat", "createWorld.customize.presets.share": "Vols compartir la teua plantilla amb algú? Utilitza la caixa de baix!", "createWorld.customize.presets.title": "Selecciona'n un predeterminat", "createWorld.preparing": "Preparant la creació del món...", "createWorld.tab.game.title": "<PERSON><PERSON>", "createWorld.tab.more.title": "Més", "createWorld.tab.world.title": "Món", "credits_and_attribution.button.attribution": "Atribució", "credits_and_attribution.button.credits": "Crè<PERSON><PERSON>", "credits_and_attribution.button.licenses": "Llicències", "credits_and_attribution.screen.title": "Crèdits i atribucions", "dataPack.bundle.description": "Habilita el sac experimental", "dataPack.bundle.name": "Sacs", "dataPack.locator_bar.description": "Mostra la direcció dels altres jugadors en multijugador", "dataPack.locator_bar.name": "Barra de localització", "dataPack.minecart_improvements.description": "Moviment millorat per a vagonetes", "dataPack.minecart_improvements.name": "Millores de les vagonetes", "dataPack.redstone_experiments.description": "Canvis experimentals de redstone", "dataPack.redstone_experiments.name": "Experiments de redstone", "dataPack.title": "Selecciona els paquets de dades", "dataPack.trade_rebalance.description": "S'han actualitzat els intercanvis dels vilatans", "dataPack.trade_rebalance.name": "Reequilibri dels intercanvis dels vilatans", "dataPack.update_1_20.description": "Noves funcions i contingut per al Minecraft 1.20", "dataPack.update_1_20.name": "Actualització 1.20", "dataPack.update_1_21.description": "Noves funcions i contingut per al Minecraft 1.21", "dataPack.update_1_21.name": "Actualització 1.21", "dataPack.validation.back": "Arrere", "dataPack.validation.failed": "Ha fallat la verificació dels paquets de dades!", "dataPack.validation.reset": "Valors per defecte", "dataPack.validation.working": "Validant els paquets de dades seleccionats...", "dataPack.vanilla.description": "Les dades per defecte del Minecraft", "dataPack.vanilla.name": "Per defecte", "dataPack.winter_drop.description": "Noves funcions i contingut per a l'estrena d'hivern", "dataPack.winter_drop.name": "<PERSON><PERSON><PERSON>", "datapackFailure.safeMode": "Mode segur", "datapackFailure.safeMode.failed.description": "Este món conté dades guardades corruptes o invàlides.", "datapackFailure.safeMode.failed.title": "Error en carregar el món en mode segur.", "datapackFailure.title": "Els errors dels paquets de dades seleccionats impedixen que el món es carregue.\nPots provar a carregar-lo amb els paquets estàndard (\"mode segur\") o tornar a la pantalla del títol i corregir el problema manualment.", "death.attack.anvil": "%1$s ha sigut esclafat per una enclusa que queia", "death.attack.anvil.player": "%1$s ha sigut esclafat per una enclusa mentre lluitava contra %2$s", "death.attack.arrow": "%2$s ha disparat i assassinat %1$s", "death.attack.arrow.item": "%2$s ha disparat i assassinat %1$s utilitzant %3$s", "death.attack.badRespawnPoint.link": "Intenció del disseny del joc", "death.attack.badRespawnPoint.message": "%1$s ha sigut assassinat per %2$s", "death.attack.cactus": "%1$s s'ha punxat fins la mort", "death.attack.cactus.player": "%1$s ha xocat amb un cactus mentre fugia de %2$s", "death.attack.cramming": "%1$s ha sigut esclafat massa fort", "death.attack.cramming.player": "%1$s ha sigut esclafat per %2$s", "death.attack.dragonBreath": "%1$s ha sigut rostit per l'alè del drac", "death.attack.dragonBreath.player": "%1$s s'ha rostit en l'alé de drac per %2$s", "death.attack.drown": "%1$s s'ha ofegat", "death.attack.drown.player": "%1$s s'ha ofegat mentre intentava escapar de %2$s", "death.attack.dryout": "%1$s s'ha mort deshidratat", "death.attack.dryout.player": "%1$s s'ha mort deshidratat mentre fugia de %2$s", "death.attack.even_more_magic": "%1$s ha mort per molt més que art de màgia", "death.attack.explosion": "%1$s ha volat pels aires", "death.attack.explosion.player": "%2$s ha fet volar pels aires %1$s", "death.attack.explosion.player.item": "%2$s ha volat pels aires %1$s amb %3$s", "death.attack.fall": "%1$s s'ha matat de la caiguda", "death.attack.fall.player": "%1$s s'ha matat de la caiguda mentre fugia de %2$s", "death.attack.fallingBlock": "%1$s ha sigut esclafat per un bloc que queia", "death.attack.fallingBlock.player": "%1$s ha sigut esclafat per un bloc mentre lluitava contra %2$s", "death.attack.fallingStalactite": "%1$s ha mort travessat per una estalactita", "death.attack.fallingStalactite.player": "%1$s ha mort travessat per una estalactita mentre lluitava amb %2$s", "death.attack.fireball": "%2$s li ha llançat una bola de foc a %1$s i l'ha mort", "death.attack.fireball.item": "%2$s li ha llançat una bola de foc a %1$s utilitzant %3$s i l'ha mort", "death.attack.fireworks": "%1$s ha format part d'una mascletà", "death.attack.fireworks.item": "%1$s ha format part d'una mascletà disparada des de %3$s pel pirotècnic %2$s", "death.attack.fireworks.player": "%1$s ha format part d'una mascletà mentre lluitava amb %2$s", "death.attack.flyIntoWall": "%1$s ha experimentat l'energia cinètica", "death.attack.flyIntoWall.player": "%1$s ha experimentat l'energia cinètica mentre fugia de %2$s", "death.attack.freeze": "%1$s s'ha gelat i ha mort", "death.attack.freeze.player": "%1$s ha sigut congelat per %2$s", "death.attack.generic": "%1$s ha mort", "death.attack.generic.player": "%1$s ha mort a causa de %2$s", "death.attack.genericKill": "%1$s ha sigut assassinat", "death.attack.genericKill.player": "%1$s ha sigut assassinat mentre lluitava contra %2$s", "death.attack.hotFloor": "%1$s ha descobert que el terra era lava", "death.attack.hotFloor.player": "%1$s ha entrat en la zona de perill degut a %2$s", "death.attack.inFire": "%1$s s'ha socarrat", "death.attack.inFire.player": "%1$s ha entrat al foc mentre lluitava amb %2$s", "death.attack.inWall": "%1$s s'ha asfixiat dins d'una paret", "death.attack.inWall.player": "%1$s s'ha asfixiat en una paret mentre lluitava contra %2$s", "death.attack.indirectMagic": "%2$s ha assassinat %1$s utilitzant màgia", "death.attack.indirectMagic.item": "%2$s ha assassinat %1$s utilitzant %3$s", "death.attack.lava": "%1$s ha provat a nadar en la lava", "death.attack.lava.player": "%1$s ha intentat nadar en la lava mentre tractava d'escapar de %2$s", "death.attack.lightningBolt": "%1$s ha sigut socarrat per un llamp", "death.attack.lightningBolt.player": "Ha caigut un llamp sobre %1$s mentre lluitava contra %2$s", "death.attack.mace_smash": "%1$s ha sigut destrossat per %2$s", "death.attack.mace_smash.item": "%1$s ha sigut destrossat per %2$s utilitzant %3$s", "death.attack.magic": "%1$s ha sigut assassinat amb magia", "death.attack.magic.player": "%1$s ha sigut assassinat amb màgia mentre fugia de %2$s", "death.attack.message_too_long": "El missatge és massa llarg per enviar-se completament. Ho lamentem! Ací en tens una versió abreujada: %s", "death.attack.mob": "%1$s ha sigut assassinat per %2$s", "death.attack.mob.item": "%1$s ha sigut assassinat per %2$s utilitzant %3$s", "death.attack.onFire": "%1$s s'ha socarrat i ha mort", "death.attack.onFire.item": "%1$s s'ha rostit mentre lluitava amb %2$s empunyant %3$s", "death.attack.onFire.player": "%1$s s'ha rostit mentre lluitava amb %2$s", "death.attack.outOfWorld": "%1$s ha caigut al buit", "death.attack.outOfWorld.player": "%1$s no volia viure al mateix món que %2$s", "death.attack.outsideBorder": "%1$s se n'ha anat dels confins del món", "death.attack.outsideBorder.player": "%1$s se n'ha anat dels confins del món mentre lluitava amb %2$s", "death.attack.player": "%1$s ha sigut assassinat per %2$s", "death.attack.player.item": "%1$s ha sigut assassinat per %2$s utilitzant %3$s", "death.attack.sonic_boom": "%1$s ha sigut arrasat per un crit carregat sònicament", "death.attack.sonic_boom.item": "%1$s ha sigut arrasat per un crit sònic mentre intentava escapar de %2$s empunyant %3$s", "death.attack.sonic_boom.player": "%1$s ha sigut arrasat per un crit sònic mentre escapava de %2$s", "death.attack.stalagmite": "%1$s ha sigut travessat per una estalagmita", "death.attack.stalagmite.player": "%1$s ha sigut empalat per una estalagmita mentre lluitava amb %2$s", "death.attack.starve": "%1$s s'ha mort de fam", "death.attack.starve.player": "%1$s ha mort de fam mentre lluitava amb %2$s", "death.attack.sting": "%1$s ha mort d'una picada", "death.attack.sting.item": "%1$s ha mort d'una picada de %2$s utilitzant %3$s", "death.attack.sting.player": "%1$s ha mort d'una picada de %2$s", "death.attack.sweetBerryBush": "%1$s s'ha punxat fins a la mort amb un esbarzer", "death.attack.sweetBerryBush.player": "%1$s s'ha punxat fins a la mort amb un esbarzer mentre escapava de %2$s", "death.attack.thorns": "%1$s ha sigut assassinat mentre intentava ferir %2$s", "death.attack.thorns.item": "%1$s ha sigut assassinat per %3$s tractant de colpejar %2$s", "death.attack.thrown": "%1$s ha sigut colpit per %2$s", "death.attack.thrown.item": "%2$s ha colpit %1$s utilitzant %3$s", "death.attack.trident": "%1$s ha sigut arponat per %2$s", "death.attack.trident.item": "%1$s ha sigut arponat per %2$s utilitzant %3$s", "death.attack.wither": "%1$s s'ha semat", "death.attack.wither.player": "%1$s s'ha marcit mentre lluitava contra %2$s", "death.attack.witherSkull": "%1$s ha sigut alcançat per una calavera de %2$s", "death.attack.witherSkull.item": "%1$s ha sigut alcançat per una calavera de %2$s utilitzant %3$s", "death.fell.accident.generic": "%1$s s'ha matat de la caiguda", "death.fell.accident.ladder": "%1$s s'ha caigut per les escales", "death.fell.accident.other_climbable": "%1$s ha caigut mentre escalava", "death.fell.accident.scaffolding": "%1$s ha caigut d'una bastida", "death.fell.accident.twisting_vines": "%1$s ha caigut d'unes lianes estrambòtiques", "death.fell.accident.vines": "%1$s ha caigut d'unes lianes", "death.fell.accident.weeping_vines": "%1$s ha caigut mentre trepava per unes lianes carmesines", "death.fell.assist": "%2$s ha obligat a caure %1$s d'un lloc alt", "death.fell.assist.item": "%2$s ha obligat a caure %1$s utilitzant %3$s", "death.fell.finish": "%1$s ha caigut d'un lloc alt i %2$s l'ha rematat", "death.fell.finish.item": "%1$s ha caigut d'un lloc alt i %2$s l'ha rematat utilitzant %3$s", "death.fell.killer": "%1$s ha sigut obligat a caure d'un lloc alt i ha mort", "deathScreen.quit.confirm": "Est<PERSON>s segur que vols eixir?", "deathScreen.respawn": "<PERSON>apa<PERSON><PERSON><PERSON>", "deathScreen.score": "Puntuació", "deathScreen.score.value": "Puntuació: %s", "deathScreen.spectate": "Observa el món", "deathScreen.title": "Has mort!", "deathScreen.title.hardcore": "Fi del joc!", "deathScreen.titleScreen": "Pantalla del títol", "debug.advanced_tooltips.help": "F3 + H = Descripció avançada d'objectes", "debug.advanced_tooltips.off": "Descripció avançada d'objectes: amaga", "debug.advanced_tooltips.on": "Descripció avançada d'objectes: mostra", "debug.chunk_boundaries.help": "F3 + G = Mostra les vores dels chunks", "debug.chunk_boundaries.off": "Vores dels chunks: amaga", "debug.chunk_boundaries.on": "Vores dels chunks: mostra", "debug.clear_chat.help": "F3 + D = Neteja el xat", "debug.copy_location.help": "F3 + C = Copia la localització com a comandament /tp, mantín F3 + C per tancar bruscament el joc", "debug.copy_location.message": "La posició s'ha copiat al porta-retalls", "debug.crash.message": "Estàs prement F3 + C. Si no les deixes anar, el joc deixarà de funcionar.", "debug.crash.warning": "Tancant bruscament el joc en %s...", "debug.creative_spectator.error": "No tens permís per a canviar el mode de joc", "debug.creative_spectator.help": "F3 + N = Alterna entre el mode de joc anterior <-> espectador", "debug.dump_dynamic_textures": "Les textures dinàmiques s'han guardat en %s", "debug.dump_dynamic_textures.help": "F3 + S = bolca les textures dinàmiques", "debug.gamemodes.error": "No tens permisos per obrir el seleccionador del mode de joc", "debug.gamemodes.help": "F3 + F4 = Obri el seleccionador del mode de joc", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s següent", "debug.help.help": "F3 + Q = Mostra esta llista", "debug.help.message": "Dreceres de teclat:", "debug.inspect.client.block": "S'han copiat les dades del bloc del client al porta-retalls", "debug.inspect.client.entity": "S'han copiat les dades de l'entitat del client al porta-retalls", "debug.inspect.help": "F3 + I = Copia les dades d'un bloc o entitat al porta-retalls", "debug.inspect.server.block": "S'han copiat les dades del bloc del servidor al porta-retalls", "debug.inspect.server.entity": "S'han copiat les dades de l'entitat del servidor al porta-retalls", "debug.pause.help": "F3 + Esc = Pausa sense el menú de pausa (si el pausat és possible)", "debug.pause_focus.help": "F3 + P = Pausa el joc en canviar de finestra", "debug.pause_focus.off": "Pausa el joc en canviar de finestra: desactivat", "debug.pause_focus.on": "Pausa el joc en canviar de finestra: activat", "debug.prefix": "[Depurador]:", "debug.profiling.help": "F3 + L = Comença/para l'anàlisi", "debug.profiling.start": "S'ha iniciat l'anàlisi per %s segons. Prem F3 + L per parar-lo abans", "debug.profiling.stop": "Anàlisi finalitzada. Els resultats s'han guardat en %s", "debug.reload_chunks.help": "F3 + A = Recarrega els chunks", "debug.reload_chunks.message": "Recarregant tots els chunks", "debug.reload_resourcepacks.help": "F3 + T = Recarrega els paquets de recursos", "debug.reload_resourcepacks.message": "<PERSON>'han recarregat els paquets de recursos", "debug.show_hitboxes.help": "F3 + B = Mostra les hitboxes", "debug.show_hitboxes.off": "Hitboxes: amaga", "debug.show_hitboxes.on": "Hitboxes: mostra", "debug.version.header": "Informació de la versió del client:", "debug.version.help": "F3 + V = informació de la versió del client", "demo.day.1": "La demostració durarà cinc dies del joc. Fes-ho el millor que pugues!", "demo.day.2": "Segon dia", "demo.day.3": "<PERSON><PERSON>er dia", "demo.day.4": "Quart dia", "demo.day.5": "Este és el teu últim dia!", "demo.day.6": "Ha passat el cinqué dia. Utilitza %s per fer una captura de pantalla de la teua creació.", "demo.day.warning": "El temps s'acaba!", "demo.demoExpired": "El temps de demostració s'ha acabat!", "demo.help.buy": "Compra'l ara!", "demo.help.fullWrapped": "La demostració durarà cinc dies del joc (1 hora i 40 minuts de la vida real més o menys). Comprova els avanços per veure idees! Passa-ho bé!", "demo.help.inventory": "Utilitza %1$s per obrir el teu inventari", "demo.help.jump": "Bota polsant %1$s", "demo.help.later": "Continua jugant!", "demo.help.movement": "Utilitza %1$s, %2$s, %3$s, %4$s i el ratolí per moure la càmera", "demo.help.movementMouse": "Mira al teu voltant amb el ratolí", "demo.help.movementShort": "Mou-te polsant %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft mode demostració", "demo.remainingTime": "Temps que falta: %s", "demo.reminder": "El temps de demostració s'ha acabat. Compra el joc per continuar o comença un nou món!", "difficulty.lock.question": "Estàs segur que vols bloquejar la dificultat del món? Açò farà que aquest siga sempre %1$s, i mai més podràs canviar-ho.", "difficulty.lock.title": "Bloqueja la dificultat del món", "disconnect.endOfStream": "Fi del flux", "disconnect.exceeded_packet_rate": "Expulsat per excedir el límit de paquets", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignorant la petició d'estat", "disconnect.loginFailedInfo": "Error en iniciar la sessió: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "El mode multijugador està desactivat. Per favor, comprova la configuració del teu compte de Microsoft.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON><PERSON> (prova reiniciant el joc i el llançador)", "disconnect.loginFailedInfo.serversUnavailable": "Els servidors d'autenticació no estan disponibles. <PERSON>na a intentar-ho.", "disconnect.loginFailedInfo.userBanned": "Estàs expulsat del mode multijugador", "disconnect.lost": "Connexió perduda", "disconnect.packetError": "Error de protocol de xarxa", "disconnect.spam": "Expulsat per fer spam", "disconnect.timeout": "Temps d'espera esgotat", "disconnect.transfer": "S'ha transferit a un altre servidor", "disconnect.unknownHost": "Amfitrió desconegut", "download.pack.failed": "%s de %s paquets no s'han pogut descarregar", "download.pack.progress.bytes": "Progrés: %s (grandària total desconeguda)", "download.pack.progress.percent": "Progrés: %s%%", "download.pack.title": "Descarregant paquet de recursos %s/%s", "editGamerule.default": "Per defecte: %s", "editGamerule.title": "Edita les regles del joc", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.bad_omen": "<PERSON> presagi", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Poder de canalització", "effect.minecraft.darkness": "Foscor", "effect.minecraft.dolphins_grace": "Gràcia de dofí", "effect.minecraft.fire_resistance": "Resistència ígnia", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON>", "effect.minecraft.haste": "Pressa", "effect.minecraft.health_boost": "Salut extra", "effect.minecraft.hero_of_the_village": "Heroi de la vila", "effect.minecraft.hunger": "Fam", "effect.minecraft.infested": "Infestat", "effect.minecraft.instant_damage": "Dany instantani", "effect.minecraft.instant_health": "Curació instantània", "effect.minecraft.invisibility": "Invisibilitat", "effect.minecraft.jump_boost": "Súper salt", "effect.minecraft.levitation": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.luck": "Sort", "effect.minecraft.mining_fatigue": "Fatiga minera", "effect.minecraft.nausea": "Ois", "effect.minecraft.night_vision": "Visió nocturna", "effect.minecraft.oozing": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.poison": "Verí", "effect.minecraft.raid_omen": "<PERSON><PERSON><PERSON> d'assalt", "effect.minecraft.regeneration": "Regeneració", "effect.minecraft.resistance": "Resistència", "effect.minecraft.saturation": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON><PERSON> lenta", "effect.minecraft.slowness": "Lentitud", "effect.minecraft.speed": "Velocitat", "effect.minecraft.strength": "Força", "effect.minecraft.trial_omen": "Presagi de repte", "effect.minecraft.unluck": "Mala sort", "effect.minecraft.water_breathing": "Respiració aquàtica", "effect.minecraft.weakness": "Debilitat", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "Ventositat", "effect.minecraft.wither": "<PERSON><PERSON>", "effect.none": "Sense efectes", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Aiguafinitat", "enchantment.minecraft.bane_of_arthropods": "La perdició dels artròpodes", "enchantment.minecraft.binding_curse": "Maledic<PERSON><PERSON>", "enchantment.minecraft.blast_protection": "Protecció contra explosions", "enchantment.minecraft.breach": "Clevill", "enchantment.minecraft.channeling": "Canalització", "enchantment.minecraft.density": "Densitat", "enchantment.minecraft.depth_strider": "Aletes", "enchantment.minecraft.efficiency": "Eficiència", "enchantment.minecraft.feather_falling": "<PERSON><PERSON> ploma", "enchantment.minecraft.fire_aspect": "Fulla de foc", "enchantment.minecraft.fire_protection": "Protecció ígnia", "enchantment.minecraft.flame": "Fletxes incendiàries", "enchantment.minecraft.fortune": "Fortuna", "enchantment.minecraft.frost_walker": "Peus gelats", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "Fletxes infinites", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.loyalty": "Lleialtat", "enchantment.minecraft.luck_of_the_sea": "La sort de la mar", "enchantment.minecraft.lure": "Esquer", "enchantment.minecraft.mending": "Reparació amb l'experiència", "enchantment.minecraft.multishot": "<PERSON><PERSON> m<PERSON>", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "Potència", "enchantment.minecraft.projectile_protection": "Protecció contra projectils", "enchantment.minecraft.protection": "Protecció", "enchantment.minecraft.punch": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.quick_charge": "Càrrega ràpida", "enchantment.minecraft.respiration": "Respiració", "enchantment.minecraft.riptide": "Corrent marina", "enchantment.minecraft.sharpness": "Fulla esmolada", "enchantment.minecraft.silk_touch": "Toc de seda", "enchantment.minecraft.smite": "Mortalitat", "enchantment.minecraft.soul_speed": "Favor de les ànimes", "enchantment.minecraft.sweeping": "Batuda", "enchantment.minecraft.sweeping_edge": "Batuda", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.thorns": "Espines", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "Maledicció de desaparició", "enchantment.minecraft.wind_burst": "Explosió de vent", "entity.minecraft.acacia_boat": "Barca d'acàcia", "entity.minecraft.acacia_chest_boat": "Barca d'acàcia amb cofre", "entity.minecraft.allay": "Al·lai", "entity.minecraft.area_effect_cloud": "Àrea del núvol d'efecte", "entity.minecraft.armadillo": "Armadillo", "entity.minecraft.armor_stand": "Suport per a armadures", "entity.minecraft.arrow": "Fletxa", "entity.minecraft.axolotl": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bamboo_chest_raft": "Rai de bambú amb cofre", "entity.minecraft.bamboo_raft": "Rai de bamb<PERSON>", "entity.minecraft.bat": "Ratpenat", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "Barca de bedoll", "entity.minecraft.birch_chest_boat": "Barca de bedoll amb cofre", "entity.minecraft.blaze": "Flama", "entity.minecraft.block_display": "Exhibir bloc", "entity.minecraft.boat": "Barca", "entity.minecraft.bogged": "Esquelet empantanat", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Càrrega de vent", "entity.minecraft.camel": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cat": "Gat", "entity.minecraft.cave_spider": "Aranya de cova", "entity.minecraft.cherry_boat": "Barca de cirerer", "entity.minecraft.cherry_chest_boat": "Barca de cirerer amb cofre", "entity.minecraft.chest_boat": "Barca amb cofre", "entity.minecraft.chest_minecart": "Vagoneta amb cofre", "entity.minecraft.chicken": "Pollastre", "entity.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Vagoneta amb bloc de comandaments", "entity.minecraft.cow": "Vaca", "entity.minecraft.creaking": "Crepitant", "entity.minecraft.creaking_transient": "Crepitant", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Barca de roure fosc", "entity.minecraft.dark_oak_chest_boat": "Barca de roure fosc amb cofre", "entity.minecraft.dolphin": "Dofí", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Bola de foc de drac", "entity.minecraft.drowned": "Ofegat", "entity.minecraft.egg": "<PERSON><PERSON>", "entity.minecraft.elder_guardian": "Gran guardià de les profunditats", "entity.minecraft.end_crystal": "Cristall de l'End", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Evocador", "entity.minecraft.evoker_fangs": "Ullals d’invocador", "entity.minecraft.experience_bottle": "<PERSON>'ha llançat una botella màgica", "entity.minecraft.experience_orb": "Orbe d'experiència", "entity.minecraft.eye_of_ender": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "entity.minecraft.falling_block": "Bloc caient", "entity.minecraft.falling_block_type": "%s està caient", "entity.minecraft.fireball": "Bola de foc", "entity.minecraft.firework_rocket": "Focs artificials", "entity.minecraft.fishing_bobber": "Suro", "entity.minecraft.fox": "<PERSON><PERSON><PERSON>", "entity.minecraft.frog": "Granota", "entity.minecraft.furnace_minecart": "Vagoneta amb forn", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Gegant", "entity.minecraft.glow_item_frame": "<PERSON> f<PERSON>", "entity.minecraft.glow_squid": "Calamar fosforescent", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Guardià de les profunditats", "entity.minecraft.happy_ghast": "La dona feliç", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Vagoneta amb tremuja", "entity.minecraft.horse": "Cavall", "entity.minecraft.husk": "Mòmia", "entity.minecraft.illusioner": "Il·lusionista", "entity.minecraft.interaction": "Interacció", "entity.minecraft.iron_golem": "Guardià de ferro", "entity.minecraft.item": "Objecte", "entity.minecraft.item_display": "<PERSON><PERSON><PERSON>e", "entity.minecraft.item_frame": "<PERSON>", "entity.minecraft.jungle_boat": "Barca de j<PERSON>la", "entity.minecraft.jungle_chest_boat": "Barca de jungla amb cofre", "entity.minecraft.killer_bunny": "El conill assassí", "entity.minecraft.leash_knot": "Nus de llaç", "entity.minecraft.lightning_bolt": "<PERSON><PERSON>", "entity.minecraft.lingering_potion": "<PERSON>ci<PERSON>", "entity.minecraft.llama": "Llama", "entity.minecraft.llama_spit": "Escopinyada de Llama", "entity.minecraft.magma_cube": "Cub de magma", "entity.minecraft.mangrove_boat": "Barca de mangle", "entity.minecraft.mangrove_chest_boat": "Barca de mangle amb cofre", "entity.minecraft.marker": "Marcador", "entity.minecraft.minecart": "Vagoneta", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "Barca de roure", "entity.minecraft.oak_chest_boat": "Barca de roure amb cofre", "entity.minecraft.ocelot": "Ocelot", "entity.minecraft.ominous_item_spawner": "Generador ominós d'objectes", "entity.minecraft.painting": "Quadre", "entity.minecraft.pale_oak_boat": "Barca de roure tètric", "entity.minecraft.pale_oak_chest_boat": "Barca de roure tètric amb cofre", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Lloro", "entity.minecraft.phantom": "<PERSON><PERSON><PERSON>", "entity.minecraft.pig": "Porc", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "Piglin brut", "entity.minecraft.pillager": "Bandit", "entity.minecraft.player": "Jugador", "entity.minecraft.polar_bear": "Os polar", "entity.minecraft.potion": "Poció", "entity.minecraft.pufferfish": "Peix globus", "entity.minecraft.rabbit": "<PERSON><PERSON>", "entity.minecraft.ravager": "Bèstia dels bandits", "entity.minecraft.salmon": "<PERSON><PERSON><PERSON>", "entity.minecraft.sheep": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON>", "entity.minecraft.silverfish": "Peixet de plata", "entity.minecraft.skeleton": "E<PERSON><PERSON>", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.slime": "<PERSON><PERSON> de llim", "entity.minecraft.small_fireball": "Bola de foc xicoteta", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "<PERSON><PERSON> de <PERSON>u", "entity.minecraft.snowball": "<PERSON><PERSON>u", "entity.minecraft.spawner_minecart": "Vagoneta amb generador de monstres", "entity.minecraft.spectral_arrow": "Fletxa espectral", "entity.minecraft.spider": "Aranya", "entity.minecraft.splash_potion": "Poció explosiva", "entity.minecraft.spruce_boat": "Barca de pícea", "entity.minecraft.spruce_chest_boat": "Barca de p<PERSON>cea amb cofre", "entity.minecraft.squid": "Calamar", "entity.minecraft.stray": "Esquelet glacial", "entity.minecraft.strider": "Caminant", "entity.minecraft.tadpole": "Cullerot", "entity.minecraft.text_display": "Exhibir text", "entity.minecraft.tnt": "Dinamita encesa", "entity.minecraft.tnt_minecart": "Vagoneta amb explosius", "entity.minecraft.trader_llama": "Llama de <PERSON>ga", "entity.minecraft.trident": "Trident", "entity.minecraft.tropical_fish": "Peix tropical", "entity.minecraft.tropical_fish.predefined.0": "Anemone", "entity.minecraft.tropical_fish.predefined.1": "Peix cirurgià negre", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON> moro", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON><PERSON> papal<PERSON>a adornat", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON><PERSON>l reina", "entity.minecraft.tropical_fish.predefined.14": "Cíclid roig", "entity.minecraft.tropical_fish.predefined.15": "Ophioblen<PERSON> atlanticus", "entity.minecraft.tropical_fish.predefined.16": "Pagre roig", "entity.minecraft.tropical_fish.predefined.17": "Polinèmid", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON>x pallasso to<PERSON>", "entity.minecraft.tropical_fish.predefined.19": "Peix ballesta", "entity.minecraft.tropical_fish.predefined.2": "Peix cirurgià blau", "entity.minecraft.tropical_fish.predefined.20": "<PERSON><PERSON>x ll<PERSON> de cua groga", "entity.minecraft.tropical_fish.predefined.21": "Peix cirurgià groc", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "Cíclid", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON> p<PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Batallaire rosa", "entity.minecraft.tropical_fish.predefined.7": "Pseudochromidae", "entity.minecraft.tropical_fish.predefined.8": "Pagre emperador roig", "entity.minecraft.tropical_fish.predefined.9": "Múl·lid", "entity.minecraft.tropical_fish.type.betty": "Arrodonit", "entity.minecraft.tropical_fish.type.blockfish": "Peix bloc", "entity.minecraft.tropical_fish.type.brinely": "D'aigua salada", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON><PERSON> argila", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON> all<PERSON>", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "Allargat", "entity.minecraft.tropical_fish.type.snooper": "D'escull", "entity.minecraft.tropical_fish.type.spotty": "Tacat", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Peix sol", "entity.minecraft.turtle": "Tortuga marina", "entity.minecraft.vex": "Esperit", "entity.minecraft.villager": "Vilatà", "entity.minecraft.villager.armorer": "<PERSON><PERSON>", "entity.minecraft.villager.butcher": "Carnisser", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "<PERSON><PERSON>", "entity.minecraft.villager.fisherman": "Pescador", "entity.minecraft.villager.fletcher": "Fletxer", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliotecari", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "Vilatà", "entity.minecraft.villager.shepherd": "Pastor", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON>", "entity.minecraft.vindicator": "<PERSON><PERSON><PERSON>", "entity.minecraft.wandering_trader": "Comerciant", "entity.minecraft.warden": "<PERSON><PERSON><PERSON>", "entity.minecraft.wind_charge": "Càrrega de vent", "entity.minecraft.witch": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON> wither", "entity.minecraft.wither_skull": "Calavera wither llan<PERSON>", "entity.minecraft.wolf": "<PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "Vilatà zombi", "entity.minecraft.zombified_piglin": "<PERSON><PERSON> z<PERSON>i", "entity.not_summonable": "No es pot invocar l'entitat del tipus %s", "event.minecraft.raid": "Assalt", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Assalt - Derrota", "event.minecraft.raid.raiders_remaining": "Queden %s assaltants", "event.minecraft.raid.victory": "Victòria", "event.minecraft.raid.victory.full": "Assalt - Victòria", "filled_map.buried_treasure": "Mapa del tresor enterrat", "filled_map.explorer_jungle": "Mapa d'exploració de jungla", "filled_map.explorer_swamp": "Mapa d'exploració de pantà", "filled_map.id": "Id #%s", "filled_map.level": "(Nivell %s/%s)", "filled_map.locked": "Bloquejat", "filled_map.mansion": "Mapa d'exploració de boscos", "filled_map.monument": "Mapa d'exploració d'oceans", "filled_map.scale": "Escala 1:%s", "filled_map.trial_chambers": "Mapa de masmorra de reptes", "filled_map.unknown": "Mapa desconegut", "filled_map.village_desert": "Mapa de vila de desert", "filled_map.village_plains": "Mapa de vila de prats", "filled_map.village_savanna": "Mapa de vila de sabana", "filled_map.village_snowy": "Mapa de vila nevada", "filled_map.village_taiga": "Mapa de vila de taigà", "flat_world_preset.minecraft.bottomless_pit": "Pou sense fons", "flat_world_preset.minecraft.classic_flat": "Extraplà clàssic", "flat_world_preset.minecraft.desert": "Desert", "flat_world_preset.minecraft.overworld": "Superfície", "flat_world_preset.minecraft.redstone_ready": "Preparat per al redstone", "flat_world_preset.minecraft.snowy_kingdom": "Regne de neu", "flat_world_preset.minecraft.the_void": "El buit", "flat_world_preset.minecraft.tunnelers_dream": "Paradís miner", "flat_world_preset.minecraft.water_world": "Món d'aigua", "flat_world_preset.unknown": "???", "gameMode.adventure": "Mode aventura", "gameMode.changed": "El teu mode de joc s'ha actualitzat a %s", "gameMode.creative": "Mode creatiu", "gameMode.hardcore": "Mode extrem", "gameMode.spectator": "Mode espectador", "gameMode.survival": "Mode supervivència", "gamerule.allowFireTicksAwayFromPlayer": "Tics de foc allunyats dels jugadors", "gamerule.allowFireTicksAwayFromPlayer.description": "Controla si el foc o la lava haurien de carregar tics a una distància de més de 8 chunks dels jugadors", "gamerule.announceAdvancements": "Anuncia els avanços", "gamerule.blockExplosionDropDecay": "En les explosions de blocs per interacció, alguns blocs no amollaran res", "gamerule.blockExplosionDropDecay.description": "Alguns dels objectes amollats per blocs destruïts per explosions causades per interacció amb blocs es destruiran en l'explosió.", "gamerule.category.chat": "Xat", "gamerule.category.drops": "<PERSON><PERSON><PERSON>", "gamerule.category.misc": "Altres", "gamerule.category.mobs": "Criatures", "gamerule.category.player": "Jugador", "gamerule.category.spawning": "<PERSON>rac<PERSON><PERSON>", "gamerule.category.updates": "Esdeveniments del món", "gamerule.commandBlockOutput": "Retransmet l'eixida del bloc de comandaments", "gamerule.commandModificationBlockLimit": "Límit de blocs modificables per comandaments", "gamerule.commandModificationBlockLimit.description": "Quantitat de blocs que es poden canviar alhora amb un comandament específic, com \"fill\" o \"clone\".", "gamerule.disableElytraMovementCheck": "Desactiva la comprovació de moviment dels èlitres", "gamerule.disablePlayerMovementCheck": "Desactiva la comprovació de moviment del jugador", "gamerule.disableRaids": "Desactiva els assalts", "gamerule.doDaylightCycle": "Avança l'hora del dia", "gamerule.doEntityDrops": "Amolla l'equipament de l'entitat", "gamerule.doEntityDrops.description": "Controla el contingut de les vagonetes (inventaris inclosos), marcs, barques, etc.", "gamerule.doFireTick": "Propagació del foc", "gamerule.doImmediateRespawn": "Reapareix immediatament", "gamerule.doInsomnia": "<PERSON>ra <PERSON>", "gamerule.doLimitedCrafting": "Exigix receptes per poder manufacturar", "gamerule.doLimitedCrafting.description": "Si està activat, els jugadors només podran manufacturar receptes desbloquejades.", "gamerule.doMobLoot": "Els éssers amollen objectes", "gamerule.doMobLoot.description": "Controla que els éssers amollen recursos, incloent orbes d'experiència.", "gamerule.doMobSpawning": "<PERSON><PERSON>", "gamerule.doMobSpawning.description": "Algunes entitats poden tindre regles separades.", "gamerule.doPatrolSpawning": "Genera patrulles de bandits", "gamerule.doTileDrops": "Amolla blocs", "gamerule.doTileDrops.description": "Controla que els blocs amollen recursos, incloent orbes d'experiència.", "gamerule.doTraderSpawning": "Generar comerciants", "gamerule.doVinesSpread": "Propagació de lianes", "gamerule.doVinesSpread.description": "Controla si les lianes es propaguen aleatòriament als blocs adjacents o no. No afecta a altres tipus de lianes com ara les carmesines, les estrambòtiques, etc.", "gamerule.doWardenSpawning": "Generar castellans", "gamerule.doWeatherCycle": "Temps atmosfèric", "gamerule.drowningDamage": "Dany per ofegament", "gamerule.enderPearlsVanishOnDeath": "Les perles d'Ender llançades desapareixen en morir", "gamerule.enderPearlsVanishOnDeath.description": "Determina si les perles d'Ender llançades per un jugador desapareixen quan es mor o no.", "gamerule.entitiesWithPassengersCanUsePortals": "Les entitats amb passatgers poden utilitzar portals", "gamerule.entitiesWithPassengersCanUsePortals.description": "Permet que les entitats amb passatgers es teletransporten a través de portals del Nether, de l'End i portes de l'End.", "gamerule.fallDamage": "Dany per caiguda", "gamerule.fireDamage": "Dany per foc", "gamerule.forgiveDeadPlayers": "Perdona els jugadors morts", "gamerule.forgiveDeadPlayers.description": "Els éssers neutrals enfadats es relaxaran quan el jugador objectiu mora prop.", "gamerule.freezeDamage": "Dany per congel<PERSON>", "gamerule.globalSoundEvents": "Esdeveniments de so globals", "gamerule.globalSoundEvents.description": "Quan certes coses ocorreguen, com ara la generació d'un enemic final, el so s'escoltarà per tot arreu.", "gamerule.keepInventory": "Conserva l'inventari després de morir", "gamerule.lavaSourceConversion": "La lava es convertix en font", "gamerule.lavaSourceConversion.description": "Quan la lava fluent es rodege per dos costats per lava, es convertirà en font.", "gamerule.locatorBar": "Activa la barra de localització de jugadors", "gamerule.locatorBar.description": "Quan s'activa, es mostra una barra a la pantalla que indica la direcció dels jugadors.", "gamerule.logAdminCommands": "Notifica els comandaments d'administrador", "gamerule.maxCommandChainLength": "Límit de longitud del comandament", "gamerule.maxCommandChainLength.description": "S'aplica a les cadenes i funcions del bloc de comandaments.", "gamerule.maxCommandForkCount": "Límit de contextos del commandament", "gamerule.maxCommandForkCount.description": "Número màxim de contextos que poden ser usats per comandaments com \"execute as\".", "gamerule.maxEntityCramming": "Límit d'entitats per bloc", "gamerule.minecartMaxSpeed": "Velocitat màxima de les vagonetes", "gamerule.minecartMaxSpeed.description": "Velocitat màxima per defecte d'una vagoneta en moviment a terra", "gamerule.mobExplosionDropDecay": "En explosions de criatures, alguns blocs no amollaran res", "gamerule.mobExplosionDropDecay.description": "Alguns dels objectes amollats per blocs destruïts per explosions causades per criatures es destruiran en l'explosió.", "gamerule.mobGriefing": "Permet accions destructives dels éssers", "gamerule.naturalRegeneration": "Regenera la vida", "gamerule.playersNetherPortalCreativeDelay": "Temps d'espera del jugador en mode creatiu per a entrar a un portal del Nether", "gamerule.playersNetherPortalCreativeDelay.description": "Temps (en tics) que un jugador en mode creatiu ha d'estar en un portal del Nether per a canviar de dimensió.", "gamerule.playersNetherPortalDefaultDelay": "Temps d'espera del jugador en un mode distint al creatiu per a entrar a un portal del Nether", "gamerule.playersNetherPortalDefaultDelay.description": "Temps (en tics) que un jugador en un mode distint al creatiu ha d'estar en un portal del Nether per a canviar de dimensió.", "gamerule.playersSleepingPercentage": "Percentatge de gent dormint", "gamerule.playersSleepingPercentage.description": "Percentatge de jugadors que han d'estar dormint per poder passar la nit.", "gamerule.projectilesCanBreakBlocks": "Els projectils poden trencar blocs", "gamerule.projectilesCanBreakBlocks.description": "Determina si els projectils poden trencar blocs que puguen ser trencats per ells o no.", "gamerule.randomTickSpeed": "Freqüència de tics aleatoris", "gamerule.reducedDebugInfo": "Reduïx la informació de depuració", "gamerule.reducedDebugInfo.description": "Limita el contingut de la pantalla de depuració.", "gamerule.sendCommandFeedback": "Opina sobre els comandaments", "gamerule.showDeathMessages": "Mostra missatges de morts", "gamerule.snowAccumulationHeight": "Alçada d'acumulació de neu", "gamerule.snowAccumulationHeight.description": "Quan neve, la capa de neu arribarà com a màxim fins a eixe número de capes.", "gamerule.spawnChunkRadius": "Radi del punt de reaparició", "gamerule.spawnChunkRadius.description": "Quantitat de chunks que es mantindran carregats al voltant del punt de reaparició de la superfície.", "gamerule.spawnRadius": "Radi del punt de reaparició", "gamerule.spawnRadius.description": "Controla la grandària de l'àrea al voltant del punt de reaparició on els jugadors poden aparéixer.", "gamerule.spectatorsGenerateChunks": "Permet als espectadors generar terreny", "gamerule.tntExplodes": "Permet que la dinamita puga activar-se i esclatar", "gamerule.tntExplosionDropDecay": "En explosions d'explosius, alguns blocs no amollaran res", "gamerule.tntExplosionDropDecay.description": "Alguns dels objectes amollats per blocs destruïts per explosions causades per explosius es destruiran en l'explosió.", "gamerule.universalAnger": "Enuig universal", "gamerule.universalAnger.description": "Els éssers neutrals enfadats atacaran qualsevol jugador que estiga prop, no només qui els haja provocat. Funciona millor si l'opció \"Perdona els jugadors morts\" està desactivada.", "gamerule.waterSourceConversion": "L'aigua es convertix en font", "gamerule.waterSourceConversion.description": "Quan l'aigua fluent es rodege per dos costats per aigua, es convertirà en font.", "generator.custom": "Personalitzat", "generator.customized": "Personalitzat antic", "generator.minecraft.amplified": "AMPLIFICAT", "generator.minecraft.amplified.info": "Avís: Només per diversió! Necessites un ordinador potent.", "generator.minecraft.debug_all_block_states": "Mode de depuració", "generator.minecraft.flat": "Extraplà", "generator.minecraft.large_biomes": "Biomes grans", "generator.minecraft.normal": "Per defecte", "generator.minecraft.single_biome_surface": "Bioma únic", "generator.single_biome_caves": "Coves", "generator.single_biome_floating_islands": "Illes flotants", "gui.abuseReport.attestation": "Enviant esta denúncia, confirmes que la informació proporcionada és precisa i completa segons el teu coneixement.", "gui.abuseReport.comments": "<PERSON><PERSON><PERSON>", "gui.abuseReport.describe": "Compartir detalls ens ajudarà a prendre la millor decisió.", "gui.abuseReport.discard.content": "Si ixes perdràs la denúncia i els comentaris.\nSegur que vols eixir?", "gui.abuseReport.discard.discard": "Ix i descarta la denúncia", "gui.abuseReport.discard.draft": "Guarda com a esborrany", "gui.abuseReport.discard.return": "Continua editant", "gui.abuseReport.discard.title": "Vols descartar la denúncia i els comentaris?", "gui.abuseReport.draft.content": "Vols continuar editant la denúncia existent o descartar-la i crear-ne una de nova?", "gui.abuseReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.abuseReport.draft.edit": "Continua editant", "gui.abuseReport.draft.quittotitle.content": "Vols continuar editant-la o descartar-la?", "gui.abuseReport.draft.quittotitle.title": "Tens una denúncia de xat en esborranys que es perdrà si ixes", "gui.abuseReport.draft.title": "Vols editar l'esborrany de la denúncia de xat?", "gui.abuseReport.error.title": "Hi ha hagut un problema en enviar la teua denúncia", "gui.abuseReport.message": "On has vist el mal comportament?\nAixò ens ajudarà a estudiar el teu cas.", "gui.abuseReport.more_comments": "Per favor, descriu què ha passat:", "gui.abuseReport.name.comment_box_label": "Per favor, descriu per què vols denunciar este nom:", "gui.abuseReport.name.reporting": "Estàs denunciant a \"%s\".", "gui.abuseReport.name.title": "Denúncia el nom del jugador", "gui.abuseReport.observed_what": "Per què denuncies això?", "gui.abuseReport.read_info": "<PERSON>én dobre denunciar", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drogues o alcohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Algú està animant als altres a participar en activitats il·legals relacionades amb drogues o animant a menors a beure.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Abús o explotació sexual de menors", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Algú està parlant o incitant a comportaments indecents en menors.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Difama<PERSON><PERSON>", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Algú està danyant la reputació d'un altre, per exemple compartint informació falsa, amb la intenció d'explotar o enganyar els altres.", "gui.abuseReport.reason.description": "Des<PERSON>rip<PERSON>ó:", "gui.abuseReport.reason.false_reporting": "<PERSON><PERSON><PERSON> falsa", "gui.abuseReport.reason.generic": "Vull denunciar-los", "gui.abuseReport.reason.generic.description": "Estic molest amb ells/es / han fet una cosa que no m'agrada.", "gui.abuseReport.reason.harassment_or_bullying": "Assetjament", "gui.abuseReport.reason.harassment_or_bullying.description": "Algú t'està avergonyint, atacant o assetjant a tu o algú altre. Això inclou quan algú intenta repetidament contactar amb tu o amb algú altre sense consentiment o publicant informació personal teua o d'una altra persona sense permís (\"dòxing\").", "gui.abuseReport.reason.hate_speech": "Discurs d'odi", "gui.abuseReport.reason.hate_speech.description": "Algú s'està clavant amb tu o un altre jugador per motius d'identitat, com ara religió, raça o sexualitat.", "gui.abuseReport.reason.imminent_harm": "Amenaça de danyar als altres", "gui.abuseReport.reason.imminent_harm.description": "Algú està tractant de fer-te mal o a algú altre en la vida real.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Imatges íntimes sense consentiment", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Algú està parlant o compartint imatges privades i íntimes.", "gui.abuseReport.reason.self_harm_or_suicide": "Autolesió o suïcidi", "gui.abuseReport.reason.self_harm_or_suicide.description": "Algú està tractant d'autolesionar-se en la vida real, o parlant de fer-ho.", "gui.abuseReport.reason.sexually_inappropriate": "Sexualment inapropiat", "gui.abuseReport.reason.sexually_inappropriate.description": "Aspectes que són gràfics en temes d'actes sexuals, òrgans sexuals i violència sexual.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorisme o extremisme violent", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON>g<PERSON> està parlant, incitant o amenaçant de cometre actes de terrorisme o violència extrema per motius polítics, religiosos, ideològics o altres raons.", "gui.abuseReport.reason.title": "Selecciona el tipus de denúncia", "gui.abuseReport.report_sent_msg": "Hem rebut la teua denúncia. Gràcies!\n\nEl nostre equip la revisarà el més prompte possible.", "gui.abuseReport.select_reason": "Selecciona la categoria de la denúncia", "gui.abuseReport.send": "Envia la denúncia", "gui.abuseReport.send.comment_too_long": "Per favor, acurta el comentari", "gui.abuseReport.send.error_message": "S'ha tornat un error mentre s'enviava la denúncia:\n\"%s\"", "gui.abuseReport.send.generic_error": "S'ha trobat un error inesperat mentre s'enviava la denúncia.", "gui.abuseReport.send.http_error": "Ha ocorregut un error HTTP inesperat mentre s'enviava la teua denúncia.", "gui.abuseReport.send.json_error": "S'ha trobat una càrrega útil mal estructurada mentre s'enviava la teua denúncia.", "gui.abuseReport.send.no_reason": "Per favor, tria una categoria de denúncia", "gui.abuseReport.send.not_attested": "Per favor, llig el text anterior i marca la casella per a poder enviar la denúncia", "gui.abuseReport.send.service_unavailable": "No s'ha pogut accedir al servei de denúncies d'abusos. Assegura't que tens connexió a internet i torna-ho a provar.", "gui.abuseReport.sending.title": "Enviant la teua denúncia...", "gui.abuseReport.sent.title": "Denúncia enviada", "gui.abuseReport.skin.title": "Denuncia l'aspecte d'un jugador", "gui.abuseReport.title": "Denuncia un jugador", "gui.abuseReport.type.chat": "Missatges del xat", "gui.abuseReport.type.name": "Nom del jugador", "gui.abuseReport.type.skin": "<PERSON><PERSON><PERSON> del jugador", "gui.acknowledge": "<PERSON><PERSON><PERSON>", "gui.advancements": "Avanços", "gui.all": "Totes", "gui.back": "<PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\nAprén-ne més al següent enllaç: %s", "gui.banned.description.permanent": "El teu compte està tancat per sempre, cosa que vol dir que ja no podràs jugar en línia o unir-te als Realms.", "gui.banned.description.reason": "Recentment hem rebut la teua denúncia per mal comportament. Els nostres moderadors han revisat el cas i l'han identificat com a %s, cosa que va en contra dels estàndards de la comunitat del Minecraft.", "gui.banned.description.reason_id": "Codi: %s", "gui.banned.description.reason_id_message": "Codi: %s - %s", "gui.banned.description.temporary": "%s Mentrestant, no podràs jugar en línia ni unir-te als Realms.", "gui.banned.description.temporary.duration": "El teu compte està suspés temporalment i es reactivarà en %s.", "gui.banned.description.unknownreason": "Recentment hem rebut la teua denúncia per mal comportament. Els nostres moderadors han revisat el cas i han vist que va en contra dels estàndards de la comunitat del Minecraft.", "gui.banned.name.description": "El teu nom actual - \"%s\" - no complix amb els estàndards de la comunitat. Podràs jugar en mode \"Un jugador\", però hauràs de canviar-te'l per jugar en línia.\n\nAprén-ne més o presenta una apel·lació en l'enllaç següent: %s", "gui.banned.name.title": "El nom no està permés en multijugador", "gui.banned.reason.defamation_impersonation_false_information": "Suplantació o compartició d'informació per a explotar o enganyar als altres", "gui.banned.reason.drugs": "Referències a drogues il·legals", "gui.banned.reason.extreme_violence_or_gore": "Representacions de violència excessiva o gore en la vida real", "gui.banned.reason.false_reporting": "Denúncies excessives o imprecises", "gui.banned.reason.fraud": "Adquisició o ús fraudulent de contingut", "gui.banned.reason.generic_violation": "Violació dels estàndards de la comunitat", "gui.banned.reason.harassment_or_bullying": "Llenguatge abussiu utilitzat de forma directa o perjudicial", "gui.banned.reason.hate_speech": "Discurs d'odi o discriminació", "gui.banned.reason.hate_terrorism_notorious_figure": "Referències a grups d'odi, organitzacions terroristes o figures notòries", "gui.banned.reason.imminent_harm_to_person_or_property": "Intent de causar mal en la vida real a persones o propietats", "gui.banned.reason.nudity_or_pornography": "Mostra de material pornogràfic o luxuriós", "gui.banned.reason.sexually_inappropriate": "Temes o contingut de naturalesa sexual", "gui.banned.reason.spam_or_advertising": "Spam o publicitat", "gui.banned.skin.description": "El teu aspecte actual no complix amb els estàndards de la comunitat. Podràs jugar amb un aspecte per defecte o triar-ne un de nou.\n\nAprén-ne més o presenta una apel·lació en l'enllaç següent: %s", "gui.banned.skin.title": "Aspecte no permés", "gui.banned.title.permanent": "Compte tancat permanentment", "gui.banned.title.temporary": "Compte suspés temporalment", "gui.cancel": "Cancel·la", "gui.chatReport.comments": "<PERSON><PERSON><PERSON>", "gui.chatReport.describe": "Compartir-ne els detalls ens ajudarà a prendre la decisió més adequada.", "gui.chatReport.discard.content": "Si ixes perdràs la denúncia i els comentaris.\nSegur que vols eixir?", "gui.chatReport.discard.discard": "Ix i descarta la denúncia", "gui.chatReport.discard.draft": "Guarda com a esborrany", "gui.chatReport.discard.return": "Continua editant", "gui.chatReport.discard.title": "Vols descartar la denúncia i els comentaris?", "gui.chatReport.draft.content": "Vols continuar editant la denúncia existent o vols descartar-la i crear-ne una de nova?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "Continua editant", "gui.chatReport.draft.quittotitle.content": "Vols continuar editant o descartar-lo?", "gui.chatReport.draft.quittotitle.title": "Tens un esborrany de denúncia del xat que es perdrà si tanques", "gui.chatReport.draft.title": "Vols editar l'esborrany de la denúncia del xat?", "gui.chatReport.more_comments": "Conta'ns què ha passat:", "gui.chatReport.observed_what": "Per què ho denuncies?", "gui.chatReport.read_info": "<PERSON>én més sobre denunciar", "gui.chatReport.report_sent_msg": "Hem rebut la teua denúncia. Gràcies!\n\nLa revisarem el més prompte possible.", "gui.chatReport.select_chat": "Tria els missatges del xat per denunciar", "gui.chatReport.select_reason": "Tria una categoria de denúncia", "gui.chatReport.selected_chat": "%s missatge(s) de xat seleccionat(s) per denunciar", "gui.chatReport.send": "Envia la denúncia", "gui.chatReport.send.comments_too_long": "Per favor, acurta el comentari", "gui.chatReport.send.no_reason": "Selecciona una categoria de denúncia", "gui.chatReport.send.no_reported_messages": "Selecciona almenys un missatge del xat per denunciar", "gui.chatReport.send.too_many_messages": "Has inclòs massa missatges a la denúncia", "gui.chatReport.title": "Denuncia el xat d'un jugador", "gui.chatSelection.context": "Els missatges porpers als seleccionats seran inclosos per tindre més informació", "gui.chatSelection.fold": "%s missatges amagats", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s s'ha unit al xat", "gui.chatSelection.message.narrate": "%s ha dit: %s a %s", "gui.chatSelection.selected": "%s/%s missatge(s) seleccionat(s)", "gui.chatSelection.title": "Selecciona els missatges a denunciar", "gui.continue": "Continua", "gui.copy_link_to_clipboard": "Copia l'enllaç al porta-retalls", "gui.days": "%s dia/es", "gui.done": "Fet", "gui.down": "Ava<PERSON>", "gui.entity_tooltip.type": "Tipus: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s fitxers rebutjats", "gui.fileDropFailure.title": "No s'han pogut afegir els arxius", "gui.hours": "%s hora/es", "gui.loadingMinecraft": "Carregant el Minecraft", "gui.minutes": "%s minut(s)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Botó de %s", "gui.narrate.editBox": "%s edita la caixa: %s", "gui.narrate.slider": "Barra de desplaçament de %s", "gui.narrate.tab": "Pestanya de %s", "gui.no": "No", "gui.none": "Cap", "gui.ok": "D'acord", "gui.open_report_dir": "Obri el directori d'informes", "gui.proceed": "Continua", "gui.recipebook.moreRecipes": "Clic dret per a més informació", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Busca...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON> tot", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON> forjables", "gui.recipebook.toggleRecipes.craftable": "Mostrant manufacturables", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON> forjables", "gui.recipebook.toggleRecipes.smokable": "Mostrant fumables", "gui.report_to_server": "Informa'n el servidor", "gui.socialInteractions.blocking_hint": "Gestiona amb un compte de Microsoft", "gui.socialInteractions.empty_blocked": "Cap jugador bloquejat al xat", "gui.socialInteractions.empty_hidden": "Cap jugador amagat al xat", "gui.socialInteractions.hidden_in_chat": "Els missatges de xat de %s s'amagaran", "gui.socialInteractions.hide": "Amaga al xat", "gui.socialInteractions.narration.hide": "Amaga els missatges de %s", "gui.socialInteractions.narration.report": "Denúncia al jugador %s", "gui.socialInteractions.narration.show": "Mostra els missatges de %s", "gui.socialInteractions.report": "Denuncia", "gui.socialInteractions.search_empty": "No s'ha trobat cap jugador amb eixe nom", "gui.socialInteractions.search_hint": "Busca...", "gui.socialInteractions.server_label.multiple": "%s - %s jugadors", "gui.socialInteractions.server_label.single": "%s - %s jugador", "gui.socialInteractions.show": "Mostra al xat", "gui.socialInteractions.shown_in_chat": "Els missatges de xat de %s es mostraran", "gui.socialInteractions.status_blocked": "Bloquejat", "gui.socialInteractions.status_blocked_offline": "Bloquejat - Sense connexió", "gui.socialInteractions.status_hidden": "Amagat", "gui.socialInteractions.status_hidden_offline": "Amagat - Sense connexió", "gui.socialInteractions.status_offline": "Sense connexió", "gui.socialInteractions.tab_all": "<PERSON><PERSON>", "gui.socialInteractions.tab_blocked": "Bloquejat", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON>lt", "gui.socialInteractions.title": "Interaccions socials", "gui.socialInteractions.tooltip.hide": "Amaga els missatges", "gui.socialInteractions.tooltip.report": "Denúncia al jugador", "gui.socialInteractions.tooltip.report.disabled": "El servici de denúncia no està disponible", "gui.socialInteractions.tooltip.report.no_messages": "No hi ha missatges denunciables del jugador %s", "gui.socialInteractions.tooltip.report.not_reportable": "Este jugador no pot ser denunciat perquè els seus missatges de xat no poden ser verificats en este servidor", "gui.socialInteractions.tooltip.show": "<PERSON>ra els missatges", "gui.stats": "Estadístiques", "gui.toMenu": "Torna a la llista de servidors", "gui.toRealms": "Torna a la llista de Realms", "gui.toTitle": "Torna a l'inici", "gui.toWorld": "Torna a la llista de mons", "gui.togglable_slot": "Fes clic per a desactivar l'espai", "gui.up": "Amunt", "gui.waitingForResponse.button.inactive": "Torna (%ss)", "gui.waitingForResponse.title": "Esperant el servidor", "gui.yes": "Sí", "hanging_sign.edit": "Edita el missatge del cartell penjant", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Cridada", "instrument.minecraft.dream_goat_horn": "Somni", "instrument.minecraft.feel_goat_horn": "Sen<PERSON><PERSON>ó", "instrument.minecraft.ponder_goat_horn": "Pensament", "instrument.minecraft.seek_goat_horn": "Busca", "instrument.minecraft.sing_goat_horn": "Cant", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON>", "inventory.binSlot": "Destruïx l'objecte", "inventory.hotbarInfo": "Guarda la barra de ferramentes amb %1$s+%2$s", "inventory.hotbarSaved": "S'han guardat els objectes de la barra de ferramentes (restaura'ls amb %1$s+%2$s)", "item.canBreak": "Pot trencar:", "item.canPlace": "Es pot posar a:", "item.canUse.unknown": "Desconegut", "item.color": "Color: %s", "item.components": "%s component(s)", "item.disabled": "Objecte desactivat", "item.durability": "Duració: %s / %s", "item.dyed": "Tenyit", "item.minecraft.acacia_boat": "Barca d'acàcia", "item.minecraft.acacia_chest_boat": "Barca d'acàcia amb cofre", "item.minecraft.allay_spawn_egg": "Generar al·lai", "item.minecraft.amethyst_shard": "Fragment d'ametista", "item.minecraft.angler_pottery_shard": "Fragments de gerro de pescador", "item.minecraft.angler_pottery_sherd": "Fragments de gerro de pescador", "item.minecraft.apple": "<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Fragments de gerro d'arquer", "item.minecraft.archer_pottery_sherd": "Fragments de gerro d'arquer", "item.minecraft.armadillo_scute": "Placa d'armadillo", "item.minecraft.armadillo_spawn_egg": "<PERSON><PERSON>", "item.minecraft.armor_stand": "Suport per a armadures", "item.minecraft.arms_up_pottery_shard": "Fragments de gerro de braços amunt", "item.minecraft.arms_up_pottery_sherd": "Fragments de gerro de braços amunt", "item.minecraft.arrow": "Fletxa", "item.minecraft.axolotl_bucket": "<PERSON>al amb axolot", "item.minecraft.axolotl_spawn_egg": "<PERSON><PERSON> ax<PERSON>", "item.minecraft.baked_potato": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bamboo_chest_raft": "Rai de bambú amb cofre", "item.minecraft.bamboo_raft": "Rai de bamb<PERSON>", "item.minecraft.bat_spawn_egg": "Generar ratpenat", "item.minecraft.bee_spawn_egg": "<PERSON><PERSON> abella", "item.minecraft.beef": "Bistec cru", "item.minecraft.beetroot": "Remolatxa", "item.minecraft.beetroot_seeds": "Llavors de remolatxa", "item.minecraft.beetroot_soup": "Sopa de remolatxa", "item.minecraft.birch_boat": "Barca de bedoll", "item.minecraft.birch_chest_boat": "Barca de bedoll amb cofre", "item.minecraft.black_bundle": "<PERSON><PERSON> negre", "item.minecraft.black_dye": "Tint negre", "item.minecraft.black_harness": "<PERSON><PERSON><PERSON> negre", "item.minecraft.blade_pottery_shard": "Fragments de gerro de fil", "item.minecraft.blade_pottery_sherd": "Fragments de gerro de fil", "item.minecraft.blaze_powder": "Pols de foc", "item.minecraft.blaze_rod": "<PERSON><PERSON>", "item.minecraft.blaze_spawn_egg": "<PERSON><PERSON>", "item.minecraft.blue_bundle": "<PERSON><PERSON> blau", "item.minecraft.blue_dye": "<PERSON><PERSON> blau", "item.minecraft.blue_egg": "<PERSON>u blau", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON> blau", "item.minecraft.bogged_spawn_egg": "Generar esquelet empantanat", "item.minecraft.bolt_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.bolt_armor_trim_smithing_template.new": "Disseny d'armadura de caragol", "item.minecraft.bone": "<PERSON><PERSON>", "item.minecraft.bone_meal": "<PERSON><PERSON>", "item.minecraft.book": "Llibre", "item.minecraft.bordure_indented_banner_pattern": "Estandard amb bordura dentada", "item.minecraft.bow": "Arc de fletxes", "item.minecraft.bowl": "<PERSON><PERSON>", "item.minecraft.bread": "Pa", "item.minecraft.breeze_rod": "Vara de brisa", "item.minecraft.breeze_spawn_egg": "<PERSON><PERSON> brisa", "item.minecraft.brewer_pottery_shard": "Fragments de gerro d'alquimista", "item.minecraft.brewer_pottery_sherd": "Fragments de gerro d'alquimista", "item.minecraft.brewing_stand": "Altar de pocions", "item.minecraft.brick": "<PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON><PERSON> ma<PERSON>", "item.minecraft.brown_dye": "<PERSON><PERSON> marr<PERSON>", "item.minecraft.brown_egg": "<PERSON><PERSON> marr<PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.brush": "Ra<PERSON><PERSON>", "item.minecraft.bucket": "<PERSON>al", "item.minecraft.bundle": "<PERSON><PERSON>", "item.minecraft.bundle.empty": "<PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Pot contenir una pila mixta d'objectes", "item.minecraft.bundle.full": "<PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Fragments de gerro de foc", "item.minecraft.burn_pottery_sherd": "Fragments de gerro de foc", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON> d<PERSON>", "item.minecraft.carrot": "<PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON> en un pal", "item.minecraft.cat_spawn_egg": "Generar gat", "item.minecraft.cauldron": "<PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Generar aranya de cova", "item.minecraft.chainmail_boots": "Botes de cota de malla", "item.minecraft.chainmail_chestplate": "Cuirassa de cota de malla", "item.minecraft.chainmail_helmet": "Casc de cota de malla", "item.minecraft.chainmail_leggings": "Pantalons de cota de malla", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON> vegetal", "item.minecraft.cherry_boat": "Barca de cirerer", "item.minecraft.cherry_chest_boat": "Barca de cirerer amb cofre", "item.minecraft.chest_minecart": "Vagoneta amb cofre", "item.minecraft.chicken": "Pollastre cru", "item.minecraft.chicken_spawn_egg": "<PERSON><PERSON> gallina", "item.minecraft.chorus_fruit": "Fruit del coral", "item.minecraft.clay_ball": "Bola d'arg<PERSON>", "item.minecraft.clock": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.coal": "<PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.coast_armor_trim_smithing_template.new": "Disseny d'armadura de costa", "item.minecraft.cocoa_beans": "Grans de cacau", "item.minecraft.cod": "Abadejo cru", "item.minecraft.cod_bucket": "<PERSON><PERSON> amb abadejo", "item.minecraft.cod_spawn_egg": "<PERSON><PERSON>", "item.minecraft.command_block_minecart": "Vagoneta amb bloc de comandaments", "item.minecraft.compass": "Brúixola", "item.minecraft.cooked_beef": "Bistec torrat", "item.minecraft.cooked_chicken": "<PERSON><PERSON><PERSON> torrat", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_mutton": "Carn d'ovella torrada", "item.minecraft.cooked_porkchop": "Carn de porc torrada", "item.minecraft.cooked_rabbit": "<PERSON><PERSON> de conill torrada", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.cookie": "Gale<PERSON>", "item.minecraft.copper_ingot": "Lingot de coure", "item.minecraft.cow_spawn_egg": "Generar vaca", "item.minecraft.creaking_spawn_egg": "Generar crepitant", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON>ard de creeper", "item.minecraft.creeper_banner_pattern.desc": "Estampat de creeper", "item.minecraft.creeper_banner_pattern.new": "Estandard de creeper carregat", "item.minecraft.creeper_spawn_egg": "Generar creeper", "item.minecraft.crossbow": "Ballesta", "item.minecraft.crossbow.projectile": "Projectil:", "item.minecraft.crossbow.projectile.multiple": "Projectil: %s x %s", "item.minecraft.crossbow.projectile.single": "Projectil: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON> cian", "item.minecraft.cyan_dye": "<PERSON><PERSON> cian", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Fragments de gerro de perill", "item.minecraft.danger_pottery_sherd": "Fragments de gerro de perill", "item.minecraft.dark_oak_boat": "Barca de roure fosc", "item.minecraft.dark_oak_chest_boat": "Barca de roure fosc amb cofre", "item.minecraft.debug_stick": "Pal de depuració", "item.minecraft.debug_stick.empty": "%s no té propietats", "item.minecraft.debug_stick.select": "s'ha seleccionat \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" ha canviat a %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "Destral de diamant", "item.minecraft.diamond_boots": "<PERSON><PERSON>", "item.minecraft.diamond_chestplate": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.diamond_helmet": "Casc de diamant", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "Armadura de diamant per a cavalls", "item.minecraft.diamond_leggings": "Malles de diamant", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON>", "item.minecraft.diamond_sword": "Espasa de diamant", "item.minecraft.disc_fragment_5": "Fragment de disc", "item.minecraft.disc_fragment_5.desc": "Disc de música - 5", "item.minecraft.dolphin_spawn_egg": "<PERSON>rar do<PERSON>", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON> ase", "item.minecraft.dragon_breath": "<PERSON><PERSON>", "item.minecraft.dried_kelp": "Alga seca", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON> ofegat", "item.minecraft.dune_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.dune_armor_trim_smithing_template.new": "Disseny d'armadura de duna", "item.minecraft.echo_shard": "Fragment ecoic", "item.minecraft.egg": "Ou", "item.minecraft.elder_guardian_spawn_egg": "Generar gran guardià de les profunditats", "item.minecraft.elytra": "Èlitres", "item.minecraft.emerald": "<PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "Llibre encantat", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON> da<PERSON>da encantada", "item.minecraft.end_crystal": "Cristall de l'End", "item.minecraft.ender_dragon_spawn_egg": "<PERSON><PERSON> drac d'<PERSON><PERSON>", "item.minecraft.ender_eye": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON> enderman", "item.minecraft.endermite_spawn_egg": "Generar endermite", "item.minecraft.evoker_spawn_egg": "Generar evocador", "item.minecraft.experience_bottle": "<PERSON><PERSON><PERSON> m<PERSON>a", "item.minecraft.explorer_pottery_shard": "Fragments de gerro d'explorador", "item.minecraft.explorer_pottery_sherd": "Fragments de gerro d'explorador", "item.minecraft.eye_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.eye_armor_trim_smithing_template.new": "Disseny d'arm<PERSON><PERSON> d'ull", "item.minecraft.feather": "Ploma", "item.minecraft.fermented_spider_eye": "Ull d'aranya fermentat", "item.minecraft.field_masoned_banner_pattern": "Estandard maçonat", "item.minecraft.filled_map": "Mapa", "item.minecraft.fire_charge": "Projectil de foc", "item.minecraft.firework_rocket": "Focs artificials", "item.minecraft.firework_rocket.flight": "Duració del vol:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Estrela de focs artificials", "item.minecraft.firework_star.black": "Negre", "item.minecraft.firework_star.blue": "Blau", "item.minecraft.firework_star.brown": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Personalitzat", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "Canviar a", "item.minecraft.firework_star.flicker": "Espurnejar", "item.minecraft.firework_star.gray": "<PERSON><PERSON>", "item.minecraft.firework_star.green": "Verd", "item.minecraft.firework_star.light_blue": "<PERSON><PERSON> clar", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON> clar", "item.minecraft.firework_star.lime": "<PERSON><PERSON>", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "<PERSON>ron<PERSON>", "item.minecraft.firework_star.pink": "<PERSON>", "item.minecraft.firework_star.purple": "<PERSON><PERSON>", "item.minecraft.firework_star.red": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape": "Forma desconeguda", "item.minecraft.firework_star.shape.burst": "Explotar", "item.minecraft.firework_star.shape.creeper": "En forma de cara de creeper", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON> gran", "item.minecraft.firework_star.shape.small_ball": "Bo<PERSON> xico<PERSON>ta", "item.minecraft.firework_star.shape.star": "En forma d'estrela", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "<PERSON>", "item.minecraft.firework_star.yellow": "Groc", "item.minecraft.fishing_rod": "<PERSON><PERSON>car", "item.minecraft.flint": "Sílex", "item.minecraft.flint_and_steel": "Sílex i ferro", "item.minecraft.flow_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.flow_armor_trim_smithing_template.new": "Disseny d'armadura de flux", "item.minecraft.flow_banner_pattern": "Estandard de flux", "item.minecraft.flow_banner_pattern.desc": "Flux", "item.minecraft.flow_banner_pattern.new": "Estandard de flux", "item.minecraft.flow_pottery_sherd": "Fragments de gerro de flux", "item.minecraft.flower_banner_pattern": "Estandard floral", "item.minecraft.flower_banner_pattern.desc": "Estampat de flors", "item.minecraft.flower_banner_pattern.new": "<PERSON><PERSON><PERSON> de flor", "item.minecraft.flower_pot": "Test", "item.minecraft.fox_spawn_egg": "<PERSON><PERSON> rabosa", "item.minecraft.friend_pottery_shard": "Fragments de gerro d'amic", "item.minecraft.friend_pottery_sherd": "Fragments de gerro d'amic", "item.minecraft.frog_spawn_egg": "Generar granota", "item.minecraft.furnace_minecart": "Vagoneta amb forn", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON> ghast", "item.minecraft.ghast_tear": "Llàgrima de ghast", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "<PERSON><PERSON><PERSON> de me<PERSON>ó <PERSON>", "item.minecraft.globe_banner_pattern": "Estandard planeta", "item.minecraft.globe_banner_pattern.desc": "Planeta", "item.minecraft.globe_banner_pattern.new": "Estandard de planeta", "item.minecraft.glow_berries": "<PERSON>es fosforescents", "item.minecraft.glow_ink_sac": "Sac de tinta fosforescent", "item.minecraft.glow_item_frame": "<PERSON> f<PERSON>", "item.minecraft.glow_squid_spawn_egg": "Generar calamar fosforescent", "item.minecraft.glowstone_dust": "Pols de roca brillant", "item.minecraft.goat_horn": "Banya de cabra", "item.minecraft.goat_spawn_egg": "Generar cabra", "item.minecraft.gold_ingot": "<PERSON><PERSON> d'or", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON> d'or", "item.minecraft.golden_apple": "Poma d'or", "item.minecraft.golden_axe": "Destral d'or", "item.minecraft.golden_boots": "<PERSON><PERSON> d'or", "item.minecraft.golden_carrot": "<PERSON><PERSON> d'or", "item.minecraft.golden_chestplate": "Cuirassa d'or", "item.minecraft.golden_helmet": "Casc d'or", "item.minecraft.golden_hoe": "Aixada d'or", "item.minecraft.golden_horse_armor": "Armadura d'or per a cavalls", "item.minecraft.golden_leggings": "Malles d'or", "item.minecraft.golden_pickaxe": "Pic d'or", "item.minecraft.golden_shovel": "Pala d'or", "item.minecraft.golden_sword": "Espasa d'or", "item.minecraft.gray_bundle": "Saquet gris", "item.minecraft.gray_dye": "Tint gris", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.green_bundle": "Saquet verd", "item.minecraft.green_dye": "Tint verd", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.guardian_spawn_egg": "Generar guardià de les profunditats", "item.minecraft.gunpowder": "Pólvora", "item.minecraft.guster_banner_pattern": "Estandard de ràfega", "item.minecraft.guster_banner_pattern.desc": "Ràfega", "item.minecraft.guster_banner_pattern.new": "Estandard de ràfega", "item.minecraft.guster_pottery_sherd": "Fragments de gerro de ràfega", "item.minecraft.happy_ghast_spawn_egg": "<PERSON><PERSON> ghast feliç", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "El cor de la mar", "item.minecraft.heart_pottery_shard": "Fragments de gerro de cor", "item.minecraft.heart_pottery_sherd": "Fragments de gerro de cor", "item.minecraft.heartbreak_pottery_shard": "Fragments de gerro de cor trencat", "item.minecraft.heartbreak_pottery_sherd": "Fragments de gerro de cor trencat", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON> ho<PERSON>n", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON> de <PERSON>l", "item.minecraft.honeycomb": "Bresca", "item.minecraft.hopper_minecart": "Vagoneta amb tremuja", "item.minecraft.horse_spawn_egg": "<PERSON>rar cavall", "item.minecraft.host_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.host_armor_trim_smithing_template.new": "Disseny d'armadura d'hostatge", "item.minecraft.howl_pottery_shard": "Fragments de gerro d'udol", "item.minecraft.howl_pottery_sherd": "Fragments de gerro d'udol", "item.minecraft.husk_spawn_egg": "<PERSON><PERSON> mò<PERSON>", "item.minecraft.ink_sac": "Sac de tinta", "item.minecraft.iron_axe": "Destral de ferro", "item.minecraft.iron_boots": "Botes de ferro", "item.minecraft.iron_chestplate": "Cuirassa de ferro", "item.minecraft.iron_golem_spawn_egg": "Generar guardià de ferro", "item.minecraft.iron_helmet": "Casc de ferro", "item.minecraft.iron_hoe": "Aixada de ferro", "item.minecraft.iron_horse_armor": "Armadura de ferro per a cavalls", "item.minecraft.iron_ingot": "Lingot de ferro", "item.minecraft.iron_leggings": "Malles de ferro", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON> de ferro", "item.minecraft.iron_pickaxe": "Pic de ferro", "item.minecraft.iron_shovel": "Pala de ferro", "item.minecraft.iron_sword": "Espasa de ferro", "item.minecraft.item_frame": "<PERSON>", "item.minecraft.jungle_boat": "Barca de j<PERSON>la", "item.minecraft.jungle_chest_boat": "Barca de jungla amb cofre", "item.minecraft.knowledge_book": "Enciclopèdia", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Poal amb lava", "item.minecraft.lead": "<PERSON><PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "Botes de cuir", "item.minecraft.leather_chestplate": "Túnica de cuir", "item.minecraft.leather_helmet": "Casc de cuir", "item.minecraft.leather_horse_armor": "Armadura de cuir per a cavalls", "item.minecraft.leather_leggings": "Pantalons de cuir", "item.minecraft.light_blue_bundle": "Saquet blau clar", "item.minecraft.light_blue_dye": "<PERSON>t blau clar", "item.minecraft.light_blue_harness": "<PERSON><PERSON><PERSON> blau clar", "item.minecraft.light_gray_bundle": "Saquet gris clar", "item.minecraft.light_gray_dye": "Tint gris clar", "item.minecraft.light_gray_harness": "<PERSON><PERSON><PERSON> gris clar", "item.minecraft.lime_bundle": "<PERSON><PERSON> verd llima", "item.minecraft.lime_dye": "<PERSON>t verd llima", "item.minecraft.lime_harness": "<PERSON><PERSON><PERSON> verd llima", "item.minecraft.lingering_potion": "<PERSON>ci<PERSON>", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON><PERSON> absurda persistent", "item.minecraft.lingering_potion.effect.empty": "Poció persistent no fabricable", "item.minecraft.lingering_potion.effect.fire_resistance": "Poció persistent de resistència al foc", "item.minecraft.lingering_potion.effect.harming": "Poció persistent de dany", "item.minecraft.lingering_potion.effect.healing": "Poció persistent de curació", "item.minecraft.lingering_potion.effect.infested": "Poció persistent d'infestació", "item.minecraft.lingering_potion.effect.invisibility": "Poció persistent d'invisibilitat", "item.minecraft.lingering_potion.effect.leaping": "Poció persistent de salt", "item.minecraft.lingering_potion.effect.levitation": "Poció persistent de levitació", "item.minecraft.lingering_potion.effect.luck": "Poció persistent de sort", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON><PERSON> mundana persistent", "item.minecraft.lingering_potion.effect.night_vision": "Poció persistent de visió nocturna", "item.minecraft.lingering_potion.effect.oozing": "Poció persistent de supuració", "item.minecraft.lingering_potion.effect.poison": "Poció persistent verinosa", "item.minecraft.lingering_potion.effect.regeneration": "Poció persistent de regeneració", "item.minecraft.lingering_potion.effect.slow_falling": "Poció persistent de caiguda lenta", "item.minecraft.lingering_potion.effect.slowness": "Poció persistent de lentitud", "item.minecraft.lingering_potion.effect.strength": "Poció persistent de força", "item.minecraft.lingering_potion.effect.swiftness": "Poció persistent de velocitat", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON><PERSON> den<PERSON>", "item.minecraft.lingering_potion.effect.turtle_master": "Poció persistent del Geni Tortuga", "item.minecraft.lingering_potion.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.water_breathing": "Poció persistent de respiració aquàtica", "item.minecraft.lingering_potion.effect.weakness": "Poció persistent de debilitat", "item.minecraft.lingering_potion.effect.weaving": "Poció persistent de teixidura", "item.minecraft.lingering_potion.effect.wind_charged": "Poció persistent de ventositat", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON> llama", "item.minecraft.lodestone_compass": "Brúixola imantada", "item.minecraft.mace": "<PERSON><PERSON>", "item.minecraft.magenta_bundle": "Saquet magenta", "item.minecraft.magenta_dye": "Tint magenta", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON> magenta", "item.minecraft.magma_cream": "Bola de magma", "item.minecraft.magma_cube_spawn_egg": "Generar cub de magma", "item.minecraft.mangrove_boat": "Barca de mangle", "item.minecraft.mangrove_chest_boat": "Barca de mangle amb cofre", "item.minecraft.map": "Mapa en blanc", "item.minecraft.melon_seeds": "Llavors de me<PERSON>ó <PERSON>ger", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON> de <PERSON>", "item.minecraft.milk_bucket": "Poal amb llet", "item.minecraft.minecart": "Vagoneta", "item.minecraft.miner_pottery_shard": "Fragments de gerro de miner", "item.minecraft.miner_pottery_sherd": "Fragments de gerro de miner", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.mojang_banner_pattern.desc": "Cosa", "item.minecraft.mojang_banner_pattern.new": "<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.mooshroom_spawn_egg": "<PERSON><PERSON> mooshroom", "item.minecraft.mourner_pottery_shard": "Fragments de gerro de dolent", "item.minecraft.mourner_pottery_sherd": "Fragments de gerro de dolent", "item.minecraft.mule_spawn_egg": "<PERSON>rar mula", "item.minecraft.mushroom_stew": "Esto<PERSON>t <PERSON> x<PERSON>s", "item.minecraft.music_disc_11": "Disc de música", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Disc de música", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Disc de música", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Disc de música", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Disc de música", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Disc de música", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Disc de música", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Disc de música", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "Disc de música", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Disc de música", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Disc de música", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Disc de música", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Disc de música", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Disc de música", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Disc de música", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Disc de música", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Disc de música", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Disc de música", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Disc de música", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Disc de música", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Disc de música", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Carn d'ovella crua", "item.minecraft.name_tag": "Etiqueta de nom", "item.minecraft.nautilus_shell": "Closca de nautilus", "item.minecraft.nether_brick": "<PERSON><PERSON>her", "item.minecraft.nether_star": "Estrela del Nether", "item.minecraft.nether_wart": "<PERSON><PERSON>her", "item.minecraft.netherite_axe": "Destral de netherita", "item.minecraft.netherite_boots": "Botes de netherita", "item.minecraft.netherite_chestplate": "Cuirassa de netherita", "item.minecraft.netherite_helmet": "Casc de netherita", "item.minecraft.netherite_hoe": "Aixada de netherita", "item.minecraft.netherite_ingot": "<PERSON>ot de netherita", "item.minecraft.netherite_leggings": "Malles de netherita", "item.minecraft.netherite_pickaxe": "Pic de netherita", "item.minecraft.netherite_scrap": "Fragments de netherita", "item.minecraft.netherite_shovel": "<PERSON>la de netherita", "item.minecraft.netherite_sword": "Espasa de netherita", "item.minecraft.netherite_upgrade_smithing_template": "Motle de ferreria", "item.minecraft.netherite_upgrade_smithing_template.new": "<PERSON><PERSON> de netherita", "item.minecraft.oak_boat": "Barca de roure", "item.minecraft.oak_chest_boat": "Barca de roure amb cofre", "item.minecraft.ocelot_spawn_egg": "<PERSON><PERSON> ocelot", "item.minecraft.ominous_bottle": "<PERSON><PERSON><PERSON> ominosa", "item.minecraft.ominous_trial_key": "<PERSON><PERSON> <PERSON> reptes ominosa", "item.minecraft.orange_bundle": "<PERSON><PERSON> ta<PERSON>", "item.minecraft.orange_dye": "Tint taron<PERSON>", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.painting": "Quadre", "item.minecraft.pale_oak_boat": "Barca de roure tètric", "item.minecraft.pale_oak_chest_boat": "Barca de roure tètric amb cofre", "item.minecraft.panda_spawn_egg": "<PERSON>rar panda", "item.minecraft.paper": "Paper", "item.minecraft.parrot_spawn_egg": "<PERSON><PERSON> ll<PERSON>", "item.minecraft.phantom_membrane": "Membrana d'ànima", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON>", "item.minecraft.pig_spawn_egg": "Generar porc", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON><PERSON> de piglin", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "<PERSON><PERSON><PERSON> de morro", "item.minecraft.piglin_brute_spawn_egg": "Generar piglin brut", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON> piglin", "item.minecraft.pillager_spawn_egg": "Generar bandit", "item.minecraft.pink_bundle": "<PERSON><PERSON> rosa", "item.minecraft.pink_dye": "<PERSON>t rosa", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON> rosa", "item.minecraft.pitcher_plant": "Planta de gerra", "item.minecraft.pitcher_pod": "Baina de planta de gerra", "item.minecraft.plenty_pottery_shard": "Fragments de gerro de plenitud", "item.minecraft.plenty_pottery_sherd": "Fragments de gerro de plenitud", "item.minecraft.poisonous_potato": "Creïlla enverinada", "item.minecraft.polar_bear_spawn_egg": "Generar os polar", "item.minecraft.popped_chorus_fruit": "Fruit del coral esclafit", "item.minecraft.porkchop": "Carn de porc crua", "item.minecraft.potato": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion": "Poció", "item.minecraft.potion.effect.awkward": "Poció <PERSON>", "item.minecraft.potion.effect.empty": "Poció no fabricable", "item.minecraft.potion.effect.fire_resistance": "Poció de resistència ígnia", "item.minecraft.potion.effect.harming": "Poció <PERSON>", "item.minecraft.potion.effect.healing": "Poció de curació", "item.minecraft.potion.effect.infested": "Poció d'infestació", "item.minecraft.potion.effect.invisibility": "Poció d'invisibilitat", "item.minecraft.potion.effect.leaping": "Poció de salt", "item.minecraft.potion.effect.levitation": "Poció de levitació", "item.minecraft.potion.effect.luck": "Poció de sort", "item.minecraft.potion.effect.mundane": "Poció sense efectes", "item.minecraft.potion.effect.night_vision": "Poció de visió nocturna", "item.minecraft.potion.effect.oozing": "Poció de supuració", "item.minecraft.potion.effect.poison": "Poció de verí", "item.minecraft.potion.effect.regeneration": "Poció de regeneració", "item.minecraft.potion.effect.slow_falling": "Poció de caiguda lenta", "item.minecraft.potion.effect.slowness": "Poció de lentitud", "item.minecraft.potion.effect.strength": "Poció de força", "item.minecraft.potion.effect.swiftness": "Poció de velocitat", "item.minecraft.potion.effect.thick": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.turtle_master": "Poció del Geni Tortuga", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON>ua", "item.minecraft.potion.effect.water_breathing": "Poció de respiració aquàtica", "item.minecraft.potion.effect.weakness": "Poció de debilitat", "item.minecraft.potion.effect.weaving": "Poció de teixidura", "item.minecraft.potion.effect.wind_charged": "Poció de ventositat", "item.minecraft.pottery_shard_archer": "Fragment de ceràmica d'arquer", "item.minecraft.pottery_shard_arms_up": "Fragment de ceràmica de braços en alt", "item.minecraft.pottery_shard_prize": "Fragments de ceràmica de premi", "item.minecraft.pottery_shard_skull": "Fragment de ceràmica de calavera", "item.minecraft.powder_snow_bucket": "Poal amb neu pols", "item.minecraft.prismarine_crystals": "Cristalls de prismarina", "item.minecraft.prismarine_shard": "Fragment de prismarina", "item.minecraft.prize_pottery_shard": "Fragments de gerro de premi", "item.minecraft.prize_pottery_sherd": "Fragments de gerro de premi", "item.minecraft.pufferfish": "Peix globus", "item.minecraft.pufferfish_bucket": "Poal amb peix globus", "item.minecraft.pufferfish_spawn_egg": "Generar peix globus", "item.minecraft.pumpkin_pie": "Coca de carabassa", "item.minecraft.pumpkin_seeds": "Llavors de carabassa", "item.minecraft.purple_bundle": "<PERSON><PERSON> morat", "item.minecraft.purple_dye": "Tint morat", "item.minecraft.purple_harness": "<PERSON><PERSON><PERSON> m<PERSON>", "item.minecraft.quartz": "Quars", "item.minecraft.rabbit": "Carn de conill crua", "item.minecraft.rabbit_foot": "Pota de conill", "item.minecraft.rabbit_hide": "<PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON> conill", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.raiser_armor_trim_smithing_template.new": "Disseny d'armadura d'elevació", "item.minecraft.ravager_spawn_egg": "<PERSON>rar b<PERSON>tia dels bandits", "item.minecraft.raw_copper": "Mineral de coure", "item.minecraft.raw_gold": "Mineral d'or", "item.minecraft.raw_iron": "Mineral de ferro", "item.minecraft.recovery_compass": "Brúixola de la troballa", "item.minecraft.red_bundle": "<PERSON><PERSON> roig", "item.minecraft.red_dye": "Tint roig", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON> roig", "item.minecraft.redstone": "Pols de redstone", "item.minecraft.resin_brick": "<PERSON><PERSON> resina", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.rib_armor_trim_smithing_template.new": "Disseny d'armadura d'os", "item.minecraft.rotten_flesh": "Carn podrida", "item.minecraft.saddle": "<PERSON><PERSON> de munta<PERSON>", "item.minecraft.salmon": "Salmó cru", "item.minecraft.salmon_bucket": "<PERSON><PERSON> amb salmó", "item.minecraft.salmon_spawn_egg": "<PERSON><PERSON>", "item.minecraft.scrape_pottery_sherd": "Fragments de gerro d'arrap", "item.minecraft.scute": "Placa", "item.minecraft.sentry_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.sentry_armor_trim_smithing_template.new": "Disseny d'armadura de sentinella", "item.minecraft.shaper_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.shaper_armor_trim_smithing_template.new": "Disseny d'armadura de forma", "item.minecraft.sheaf_pottery_shard": "Fragments de gerro de feix", "item.minecraft.sheaf_pottery_sherd": "Fragments de gerro de feix", "item.minecraft.shears": "Tisores d'esquilar", "item.minecraft.sheep_spawn_egg": "<PERSON><PERSON> ovella", "item.minecraft.shelter_pottery_shard": "Fragments de gerro de refugi", "item.minecraft.shelter_pottery_sherd": "Fragments de gerro de refugi", "item.minecraft.shield": "Escut", "item.minecraft.shield.black": "Escut negre", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON> blau", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON> marró", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON> cian", "item.minecraft.shield.gray": "Escut gris", "item.minecraft.shield.green": "Escut verd", "item.minecraft.shield.light_blue": "E<PERSON><PERSON> blau clar", "item.minecraft.shield.light_gray": "Escut gris clar", "item.minecraft.shield.lime": "Escut verd llima", "item.minecraft.shield.magenta": "Escut magenta", "item.minecraft.shield.orange": "Escut taronja", "item.minecraft.shield.pink": "Escut rosa", "item.minecraft.shield.purple": "Escut morat", "item.minecraft.shield.red": "Escut roig", "item.minecraft.shield.white": "Escut blanc", "item.minecraft.shield.yellow": "Escut groc", "item.minecraft.shulker_shell": "Closca <PERSON>", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON>", "item.minecraft.sign": "<PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.silence_armor_trim_smithing_template.new": "Disseny d'aramdura del silenci", "item.minecraft.silverfish_spawn_egg": "<PERSON>rar peixet de plata", "item.minecraft.skeleton_horse_spawn_egg": "<PERSON><PERSON> cavall esquel<PERSON>", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON> esquelet", "item.minecraft.skull_banner_pattern": "Estandard calavera", "item.minecraft.skull_banner_pattern.desc": "Estampat de calavera", "item.minecraft.skull_banner_pattern.new": "Estandard de calavera", "item.minecraft.skull_pottery_shard": "Fragments de gerro de calavera", "item.minecraft.skull_pottery_sherd": "Fragments de gerro de calavera", "item.minecraft.slime_ball": "<PERSON><PERSON>", "item.minecraft.slime_spawn_egg": "Generar cub de llim", "item.minecraft.smithing_template": "Motle de ferreria", "item.minecraft.smithing_template.applies_to": "S'aplica a:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Afig un lingot o cristall", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Afig una peça d'armadura", "item.minecraft.smithing_template.armor_trim.ingredients": "Lingots i cristalls", "item.minecraft.smithing_template.ingredients": "Ingredients:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Afig un lingot de netherita", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Equipament de diamant", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Afig una armadura, arma o ferramenta de diamant", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "<PERSON>ot de netherita", "item.minecraft.smithing_template.upgrade": "Millora: ", "item.minecraft.sniffer_spawn_egg": "<PERSON><PERSON>", "item.minecraft.snort_pottery_shard": "Fragments de gerro de bufit", "item.minecraft.snort_pottery_sherd": "Fragments de gerro de bufit", "item.minecraft.snout_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.snout_armor_trim_smithing_template.new": "Disseny d'armadura de morro", "item.minecraft.snow_golem_spawn_egg": "<PERSON><PERSON> ninot de neu", "item.minecraft.snowball": "<PERSON><PERSON>u", "item.minecraft.spectral_arrow": "Fletxa espectral", "item.minecraft.spider_eye": "Ull d'aranya", "item.minecraft.spider_spawn_egg": "<PERSON><PERSON> a<PERSON>", "item.minecraft.spire_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.spire_armor_trim_smithing_template.new": "Disseny d'armadura de cúpula", "item.minecraft.splash_potion": "Poció explosiva", "item.minecraft.splash_potion.effect.awkward": "Poció explosiva absurda", "item.minecraft.splash_potion.effect.empty": "Poció explosiva no fabricable", "item.minecraft.splash_potion.effect.fire_resistance": "Poció explosiva de resistencia al foc", "item.minecraft.splash_potion.effect.harming": "Poció explosiva de dany", "item.minecraft.splash_potion.effect.healing": "Poció explosiva de curació", "item.minecraft.splash_potion.effect.infested": "Poció explosiva d'infestació", "item.minecraft.splash_potion.effect.invisibility": "Poció explosiva d'invisibilitat", "item.minecraft.splash_potion.effect.leaping": "Poció explosiva de salt", "item.minecraft.splash_potion.effect.levitation": "Poció explosiva de levitació", "item.minecraft.splash_potion.effect.luck": "Poció explosiva de sort", "item.minecraft.splash_potion.effect.mundane": "Poció explosiva mundana", "item.minecraft.splash_potion.effect.night_vision": "Poció explosiva de visió nocturna", "item.minecraft.splash_potion.effect.oozing": "Poció explosiva de supuració", "item.minecraft.splash_potion.effect.poison": "Poció explosiva de verí", "item.minecraft.splash_potion.effect.regeneration": "Poció explosiva de regeneració", "item.minecraft.splash_potion.effect.slow_falling": "Poció explosiva de caiguda lenta", "item.minecraft.splash_potion.effect.slowness": "Poció explosiva de lentitud", "item.minecraft.splash_potion.effect.strength": "Poció explosiva de força", "item.minecraft.splash_potion.effect.swiftness": "Poció explosiva de velocitat", "item.minecraft.splash_potion.effect.thick": "Poció explosiva densa", "item.minecraft.splash_potion.effect.turtle_master": "Poció explosiva del Geni Tortuga", "item.minecraft.splash_potion.effect.water": "Botella d'aigua explosiva", "item.minecraft.splash_potion.effect.water_breathing": "Poció explosiva de respiració aquàtica", "item.minecraft.splash_potion.effect.weakness": "Poció explosiva de debilitat", "item.minecraft.splash_potion.effect.weaving": "Poció explosiva de teixidura", "item.minecraft.splash_potion.effect.wind_charged": "Poció explosiva de ventositat", "item.minecraft.spruce_boat": "Barca de pícea", "item.minecraft.spruce_chest_boat": "Barca de p<PERSON>cea amb cofre", "item.minecraft.spyglass": "Ullera", "item.minecraft.squid_spawn_egg": "<PERSON><PERSON> calamar", "item.minecraft.stick": "Pal", "item.minecraft.stone_axe": "Destral de pedra", "item.minecraft.stone_hoe": "Aixada de pedra", "item.minecraft.stone_pickaxe": "<PERSON><PERSON> de <PERSON>edra", "item.minecraft.stone_shovel": "<PERSON><PERSON> de pedra", "item.minecraft.stone_sword": "Espasa de pedra", "item.minecraft.stray_spawn_egg": "Generar esquelet glacial", "item.minecraft.strider_spawn_egg": "Generar caminant", "item.minecraft.string": "Fil", "item.minecraft.sugar": "<PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON><PERSON><PERSON><PERSON>sp<PERSON>", "item.minecraft.sweet_berries": "Mores", "item.minecraft.tadpole_bucket": "<PERSON><PERSON> amb cullerot", "item.minecraft.tadpole_spawn_egg": "<PERSON><PERSON> cullerot", "item.minecraft.tide_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.tide_armor_trim_smithing_template.new": "Disseny d'armadura de marea", "item.minecraft.tipped_arrow": "Fletxa amb efecte", "item.minecraft.tipped_arrow.effect.awkward": "Fletxa amb efecte", "item.minecraft.tipped_arrow.effect.empty": "Fletxa amb efecte impossible d'elaborar", "item.minecraft.tipped_arrow.effect.fire_resistance": "Fletxa de resistència ígnia", "item.minecraft.tipped_arrow.effect.harming": "Fletxa de dany augmentat", "item.minecraft.tipped_arrow.effect.healing": "Fletxa curativa", "item.minecraft.tipped_arrow.effect.infested": "Fletxa d'infestació", "item.minecraft.tipped_arrow.effect.invisibility": "Fletxa d'invisibilitat", "item.minecraft.tipped_arrow.effect.leaping": "Fletxa de salt", "item.minecraft.tipped_arrow.effect.levitation": "Fletxa de levitació", "item.minecraft.tipped_arrow.effect.luck": "Fletxa de la sort", "item.minecraft.tipped_arrow.effect.mundane": "Fletxa amb efecte", "item.minecraft.tipped_arrow.effect.night_vision": "Fletxa de visió nocturna", "item.minecraft.tipped_arrow.effect.oozing": "Fletxa de supuració", "item.minecraft.tipped_arrow.effect.poison": "Fletxa enverinada", "item.minecraft.tipped_arrow.effect.regeneration": "Fletxa de regeneració", "item.minecraft.tipped_arrow.effect.slow_falling": "Fletxa de caiguda lenta", "item.minecraft.tipped_arrow.effect.slowness": "Fletxa de lentitud", "item.minecraft.tipped_arrow.effect.strength": "Fletxa de força", "item.minecraft.tipped_arrow.effect.swiftness": "Fletxa de velocitat", "item.minecraft.tipped_arrow.effect.thick": "Fletxa amb efecte", "item.minecraft.tipped_arrow.effect.turtle_master": "Fletxa del Geni Tortuga", "item.minecraft.tipped_arrow.effect.water": "Fletxa esquitxadora", "item.minecraft.tipped_arrow.effect.water_breathing": "Fletxa de respiració aquàtica", "item.minecraft.tipped_arrow.effect.weakness": "Fletxa de debilitat", "item.minecraft.tipped_arrow.effect.weaving": "Fletxa de teixidura", "item.minecraft.tipped_arrow.effect.wind_charged": "Fletxa de ventositat", "item.minecraft.tnt_minecart": "Vagoneta amb explosius", "item.minecraft.torchflower_seeds": "Llavors de flamaflor", "item.minecraft.totem_of_undying": "Tòtem de la immortalitat", "item.minecraft.trader_llama_spawn_egg": "<PERSON>rar llama <PERSON>", "item.minecraft.trial_key": "<PERSON><PERSON> <PERSON>", "item.minecraft.trident": "Trident", "item.minecraft.tropical_fish": "Peix tropical", "item.minecraft.tropical_fish_bucket": "Poal amb peix tropical", "item.minecraft.tropical_fish_spawn_egg": "Generar peix tropical", "item.minecraft.turtle_helmet": "Closca de tortuga marina", "item.minecraft.turtle_scute": "Placa de tortuga", "item.minecraft.turtle_spawn_egg": "Generar tortuga marina", "item.minecraft.vex_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.vex_armor_trim_smithing_template.new": "Disseny d'armadura d'esperit", "item.minecraft.vex_spawn_egg": "Generar esperit", "item.minecraft.villager_spawn_egg": "Generar vilatà", "item.minecraft.vindicator_spawn_egg": "Generar defensor", "item.minecraft.wandering_trader_spawn_egg": "<PERSON>rar comerciant", "item.minecraft.ward_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.ward_armor_trim_smithing_template.new": "Disseny d'armadura de guardià", "item.minecraft.warden_spawn_egg": "<PERSON><PERSON>", "item.minecraft.warped_fungus_on_a_stick": "Fong estrambòtic en un pal", "item.minecraft.water_bucket": "<PERSON><PERSON> amb aigua", "item.minecraft.wayfinder_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Disseny d'armadura d'explorador", "item.minecraft.wheat": "<PERSON><PERSON>", "item.minecraft.wheat_seeds": "<PERSON><PERSON><PERSON> de blat", "item.minecraft.white_bundle": "Saquet blanc", "item.minecraft.white_dye": "<PERSON><PERSON> blanc", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "Motle de ferreria", "item.minecraft.wild_armor_trim_smithing_template.new": "Disseny d'armadura salvatge", "item.minecraft.wind_charge": "Càrrega de vent", "item.minecraft.witch_spawn_egg": "Generar bruixa", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON> esquelet wither", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON> wither", "item.minecraft.wolf_armor": "<PERSON><PERSON><PERSON> de ll<PERSON>", "item.minecraft.wolf_spawn_egg": "<PERSON><PERSON> llop", "item.minecraft.wooden_axe": "Destral de fusta", "item.minecraft.wooden_hoe": "Aixada de fusta", "item.minecraft.wooden_pickaxe": "Pic de fusta", "item.minecraft.wooden_shovel": "Pala de fusta", "item.minecraft.wooden_sword": "Espasa de fusta", "item.minecraft.writable_book": "<PERSON><PERSON><PERSON>, tinter i ploma", "item.minecraft.written_book": "Llibre escrit", "item.minecraft.yellow_bundle": "<PERSON>quet groc", "item.minecraft.yellow_dye": "Tint groc", "item.minecraft.yellow_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON>lin", "item.minecraft.zombie_horse_spawn_egg": "Generar cavall zombi", "item.minecraft.zombie_spawn_egg": "<PERSON><PERSON> z<PERSON>", "item.minecraft.zombie_villager_spawn_egg": "Generar vilatà zombi", "item.minecraft.zombified_piglin_spawn_egg": "<PERSON><PERSON> piglin zombi", "item.modifiers.any": "Quan s'equipa:", "item.modifiers.armor": "Quan es porta:", "item.modifiers.body": "Quan s'equipa:", "item.modifiers.chest": "Al pit:", "item.modifiers.feet": "<PERSON><PERSON> peus:", "item.modifiers.hand": "Quan se sosté:", "item.modifiers.head": "Al cap:", "item.modifiers.legs": "A les cames:", "item.modifiers.mainhand": "A la mà principal:", "item.modifiers.offhand": "A la mà secundaria:", "item.modifiers.saddle": "Amb sella de muntar:", "item.nbt_tags": "NBT: %s etiqueta/es", "item.op_block_warning.line1": "Advertència:", "item.op_block_warning.line2": "Si es col·loca este objecte es podria executar un comandament", "item.op_block_warning.line3": "No l'utilitzes si no saps el seu contingut!", "item.unbreakable": "Indestructible", "itemGroup.buildingBlocks": "Blocs de construcció", "itemGroup.coloredBlocks": "Blocs de colors", "itemGroup.combat": "<PERSON><PERSON><PERSON>", "itemGroup.consumables": "Consumibles", "itemGroup.crafting": "Manufactura", "itemGroup.foodAndDrink": "<PERSON><PERSON> i beguda", "itemGroup.functional": "Blocs funcionals", "itemGroup.hotbar": "Barres de ferramentes guardades", "itemGroup.ingredients": "Ingredients", "itemGroup.inventory": "Inventari del mode supervivència", "itemGroup.natural": "Blocs naturals", "itemGroup.op": "Funcions d'administrador", "itemGroup.redstone": "Blocs de redstone", "itemGroup.search": "Buscador", "itemGroup.spawnEggs": "Generadors", "itemGroup.tools": "Ferramentes i utilitats", "item_modifier.unknown": "Modificador d'objecte desconegut: %s", "jigsaw_block.final_state": "Es converteix en:", "jigsaw_block.generate": "Genera", "jigsaw_block.joint.aligned": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint.rollable": "Rotable", "jigsaw_block.joint_label": "Tipus d'unió:", "jigsaw_block.keep_jigsaws": "Trencaclosques", "jigsaw_block.levels": "Nivells: %s", "jigsaw_block.name": "Nom:", "jigsaw_block.placement_priority": "Prioritat de col·locació:", "jigsaw_block.placement_priority.tooltip": "Quan este bloc de trencaclosques es connecta a una peça, eixe serà l'ordre amb què eixa peça serà processada per a connexions en l'estructura gran.\n\nLes peces es processaran en prioritat descendent amb trencament d'enllaços per ordre d'inserció.", "jigsaw_block.pool": "Grup objectiu:", "jigsaw_block.selection_priority": "Prioritat de selecció:", "jigsaw_block.selection_priority.tooltip": "Quan la peça mare està sent processada per a connexions, eixe serà l'ordre amb què eixe bloc de trencaclosques s'intentarà connectar amb la peça objectiu.\n\nEls trencacloques es processaran en prioritat descendent amb trencament d'enllaços aleatoris.", "jigsaw_block.target": "Nom de l'objectiu:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON><PERSON> (Music Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Avanços", "key.attack": "Ataca<PERSON>/destruir", "key.back": "Caminar arrere", "key.categories.creative": "Mode creatiu", "key.categories.gameplay": "Jugabilitat", "key.categories.inventory": "Inventari", "key.categories.misc": "Altres", "key.categories.movement": "Moviment", "key.categories.multiplayer": "Multijugador", "key.categories.ui": "Interfície del joc", "key.chat": "Obrir el xat", "key.command": "Obri comandament", "key.drop": "Tira l'objecte seleccionat", "key.forward": "Caminar avant", "key.fullscreen": "Activa/desactiva pantalla completa", "key.hotbar.1": "Tecla ràpida 1", "key.hotbar.2": "Tecla ràpida 2", "key.hotbar.3": "Tecla ràpida 3", "key.hotbar.4": "Tecla ràpida 4", "key.hotbar.5": "Tecla ràpida 5", "key.hotbar.6": "Tecla ràpida 6", "key.hotbar.7": "Tecla ràpida 7", "key.hotbar.8": "Tecla ràpida 8", "key.hotbar.9": "Tecla ràpida 9", "key.inventory": "Obri/Tanca l'inventari", "key.jump": "Botar", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.caps.lock": "<PERSON><PERSON><PERSON>", "key.keyboard.comma": ",", "key.keyboard.delete": "Elimina", "key.keyboard.down": "Fletxa avall", "key.keyboard.end": "Final", "key.keyboard.enter": "Intro", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Inici", "key.keyboard.insert": "Insereix", "key.keyboard.keypad.0": "0 (teclat numèric)", "key.keyboard.keypad.1": "1 (teclat numèric)", "key.keyboard.keypad.2": "2 (teclat numèric)", "key.keyboard.keypad.3": "3 (teclat numèric)", "key.keyboard.keypad.4": "4 (teclat numèric)", "key.keyboard.keypad.5": "5 (teclat numèric)", "key.keyboard.keypad.6": "6 (tec<PERSON> num<PERSON>ric)", "key.keyboard.keypad.7": "7 (tec<PERSON> num<PERSON>ric)", "key.keyboard.keypad.8": "8 (teclat numèric)", "key.keyboard.keypad.9": "9 (tec<PERSON> numèric)", "key.keyboard.keypad.add": "+ (teclat numèric)", "key.keyboard.keypad.decimal": ". (teclat numèric)", "key.keyboard.keypad.divide": "/ (teclat numèric)", "key.keyboard.keypad.enter": "Intro (teclat numèric)", "key.keyboard.keypad.equal": "= (teclat numèric)", "key.keyboard.keypad.multiply": "* (teclat numèric)", "key.keyboard.keypad.subtract": "- (teclat numèric)", "key.keyboard.left": "Fletxa esquerra", "key.keyboard.left.alt": "Alt es<PERSON>re", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Control esquerre", "key.keyboard.left.shift": "<PERSON><PERSON><PERSON><PERSON> es<PERSON>", "key.keyboard.left.win": "Windows esquerre", "key.keyboard.menu": "Menú", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Blo<PERSON>", "key.keyboard.page.down": "Av Pàg", "key.keyboard.page.up": "<PERSON>", "key.keyboard.pause": "Pausa", "key.keyboard.period": ".", "key.keyboard.print.screen": "Imprimeix pantalla", "key.keyboard.right": "Fletxa dreta", "key.keyboard.right.alt": "Alt dret", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Control dret", "key.keyboard.right.shift": "<PERSON><PERSON><PERSON><PERSON> dret", "key.keyboard.right.win": "Windows dret", "key.keyboard.scroll.lock": "Bloq Despl", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Espai", "key.keyboard.tab": "Tabulador", "key.keyboard.unknown": "Sense enllaç", "key.keyboard.up": "Fletxa amunt", "key.keyboard.world.1": "Món 1", "key.keyboard.world.2": "Món 2", "key.left": "Esquerra", "key.loadToolbarActivator": "Carrega la barra d'objectes", "key.mouse": "Botó %1$s", "key.mouse.left": "<PERSON><PERSON><PERSON>", "key.mouse.middle": "Botó del mig", "key.mouse.right": "<PERSON><PERSON><PERSON> dret", "key.pickItem": "Agafar bloc", "key.playerlist": "Llista de jugadors", "key.quickActions": "Accions ràpides", "key.right": "Dreta", "key.saveToolbarActivator": "Guarda la barra d'objectes", "key.screenshot": "Fes una captura de pantalla", "key.smoothCamera": "Alternar càmera cinemàtica", "key.sneak": "Acatxar-se", "key.socialInteractions": "Pantalla d'interaccions socials", "key.spectatorOutlines": "Destacar juga<PERSON> (espectadors)", "key.sprint": "<PERSON><PERSON><PERSON>", "key.swapOffhand": "Intercanvia l'objecte amb la mà secundària", "key.togglePerspective": "Alterna perspectiva", "key.use": "Utilitzar objecte/posar bloc", "known_server_link.announcements": "<PERSON><PERSON><PERSON>", "known_server_link.community": "Comunitat", "known_server_link.community_guidelines": "Directrius de la comunitat", "known_server_link.feedback": "<PERSON><PERSON><PERSON>", "known_server_link.forums": "Fòrums", "known_server_link.news": "Notícies", "known_server_link.report_bug": "Informar d'un error del servidor", "known_server_link.status": "Estat", "known_server_link.support": "Suport", "known_server_link.website": "Pàgina web", "lanServer.otherPlayers": "Configuració per als altres jugadors", "lanServer.port": "Número del port", "lanServer.port.invalid": "El port no és vàlid.\nDeixa en blanc el camp o posa un altre número entre 1024 i 65535.", "lanServer.port.invalid.new": "El port no és vàlid.\nDeixa el camp buit o introduïx un número entre %s i %s.", "lanServer.port.unavailable": "El port no està disponible.\nDeixa en blanc el camp o posa un altre número entre 1024 i 65535.", "lanServer.port.unavailable.new": "El port no està disponible.\nDeixa el camp buit o introduïx un número diferent entre %s i %s.", "lanServer.scanning": "Buscant jocs a la teua xarxa local", "lanServer.start": "Començar món LAN", "lanServer.title": "Món LAN", "language.code": "cat_ES-VC", "language.name": "Català (Valencià)", "language.region": "<PERSON><PERSON>", "lectern.take_book": "Retira el llibre", "loading.progress": "%s%%", "mco.account.privacy.info": "Llig més sobre Mojang i les lleis de privacitat", "mco.account.privacy.info.button": "Llig més sobre el GDPR", "mco.account.privacy.information": "Mojang implementa procediments segurs per a ajudar a protegir els xiquets i xiquetes i la seua privacitat incloent el compliment amb la Llei de Protecció de la Privacitat de Xiquets i Xiquetes en línia (COPPA) i la Reglament General de Protecció de Dades (GDPR).\n\nNecessitaràs el permís dels teus pares per accedir al teu compte de Realms.", "mco.account.privacyinfo": "Mojang implementa certs procediments per ajudar a protegir als menors i la seua privacitat incloent complir amb la Llei de Protecció de Privacitat Infantil a Internet (COPPA) i el Reglament General de Protecció de Dades (RGPD).\n\nHas de tenir consentiment parental abans de poder tenir accés al teu compte de Realms.\n\nSi tens un compte de Minecraft antic (inicies sessió amb el teu nom d'usuari), hauràs de migrar el teu compte a un de Mojang per poder accedir a Realms.", "mco.account.update": "Actualitza el compte", "mco.activity.noactivity": "No hi ha hagut cap activitat des de fa %s dia/dies", "mco.activity.title": "Activitat del jugador", "mco.backup.button.download": "Descarrega'n l'últim", "mco.backup.button.reset": "Reinicia el món", "mco.backup.button.restore": "Restaura", "mco.backup.button.upload": "<PERSON>uja un món", "mco.backup.changes.tooltip": "<PERSON><PERSON>", "mco.backup.entry": "Còpia de seguretat (%s)", "mco.backup.entry.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.enabledPack": "Pack/s activat/s", "mco.backup.entry.gameDifficulty": "Dificultat del joc", "mco.backup.entry.gameMode": "Mode de joc", "mco.backup.entry.gameServerVersion": "Versió del servidor del joc", "mco.backup.entry.name": "Nom", "mco.backup.entry.seed": "<PERSON><PERSON><PERSON>", "mco.backup.entry.templateName": "Nom de la plantilla", "mco.backup.entry.undefined": "<PERSON>vi no definit", "mco.backup.entry.uploaded": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.worldType": "Tipus de món", "mco.backup.generate.world": "Genera el món", "mco.backup.info.title": "Canvis des de l'última còpia de seguretat", "mco.backup.narration": "Còpia de seguretat de %s", "mco.backup.nobackups": "Este realm encara no té cap còpia de seguretat.", "mco.backup.restoring": "Restaurant el teu realm", "mco.backup.unknown": "DESCONEGUDA", "mco.brokenworld.download": "Baixa", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "Per favor, reinicia'l o tria'n un altre.", "mco.brokenworld.message.line2": "També tens l'opció de descarregar el món per a un jugador.", "mco.brokenworld.minigame.title": "El minijoc ja no és compatible", "mco.brokenworld.nonowner.error": "Per favor, espera al propietari del realm per reiniciar el món", "mco.brokenworld.nonowner.title": "El món està obsolet", "mco.brokenworld.play": "Juga", "mco.brokenworld.reset": "Reinicia", "mco.brokenworld.title": "El teu món actual ja no és compatible", "mco.client.incompatible.msg.line1": "El teu client no és compatible amb els Realms.", "mco.client.incompatible.msg.line2": "Per favor, utilitza la versió més recent de Minecraft.", "mco.client.incompatible.msg.line3": "Els Realms no són compatibles amb les versions de prova.", "mco.client.incompatible.title": "Client incompatible!", "mco.client.outdated.stable.version": "La teua versió de client (%s) no és compatible amb els Realms.\n\nPer favor, utilitza la versió més recent del Minecraft.", "mco.client.unsupported.snapshot.version": "La teua versió de client (%s) no és compatible amb els Realms.\n\nEls Realms no estan disponibles per a esta versió de prova.", "mco.compatibility.downgrade": "Degrada", "mco.compatibility.downgrade.description": "Es va jugar a este món per última vegada en la versió %s; tu estàs en la versió %s. Carregar un món en una versió antiga pot corrompre'l - no podem garantir que carregue o funcione.\n\nEs guardarà una còpia de seguretat del teu món en \"World backups\". Per favor, restaura'l si és necessari.", "mco.compatibility.incompatible.popup.title": "Versió incompatible", "mco.compatibility.incompatible.releaseType.popup.message": "El món al qual vols unir-te és incompatible amb la versió actual.", "mco.compatibility.incompatible.series.popup.message": "Este món va ser jugat per última vegada en la versió %s; estàs en la versió %s.\n\nEstes séries no són compatibles entre elles. Es necessita jugar a un nou món en esta versió.", "mco.compatibility.unverifiable.message": "No s'ha pogut verificar l'última versió amb la qual es va jugar a este món. Si el món s'actualitza o s'obri en una versió antiga, es crearà i guardarà una còpia de seguretat automàticament en \"World backups\".", "mco.compatibility.unverifiable.title": "Compatibilitat no verificable", "mco.compatibility.upgrade": "<PERSON><PERSON><PERSON><PERSON>", "mco.compatibility.upgrade.description": "Es va jugar a este món per última vegada en la versió %s; tu estàs en la versió %s.\n\nEs guardarà una còpia de seguretat del teu món en \"World backups\". Per favor, restaura'l si és necessari.", "mco.compatibility.upgrade.friend.description": "Es va jugar a este món per última vegada en la versió %s; tu estàs en la versió %s.\n\nEs guardarà una còpia de seguretat del món en \"World Backups\".\n\nEl propietari del Realm pot restaurar el món si és necessari.", "mco.compatibility.upgrade.title": "Se<PERSON>r que vols actualitzar este món?", "mco.configure.current.minigame": "Actual", "mco.configure.world.activityfeed.disabled": "L’activitat del jugador està temporalment desactivada", "mco.configure.world.backup": "Còpies de seguretat del món", "mco.configure.world.buttons.activity": "Activitat del jugador", "mco.configure.world.buttons.close": "Tanca el Realm", "mco.configure.world.buttons.delete": "Elimina", "mco.configure.world.buttons.done": "Fet", "mco.configure.world.buttons.edit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.invite": "Convida un jugador", "mco.configure.world.buttons.moreoptions": "Més opcions", "mco.configure.world.buttons.newworld": "Nou món", "mco.configure.world.buttons.open": "<PERSON>bri el Realm", "mco.configure.world.buttons.options": "Opcions del món", "mco.configure.world.buttons.players": "Jugadors", "mco.configure.world.buttons.region_preference": "Selecciona una regió...", "mco.configure.world.buttons.resetworld": "Reinicia el món", "mco.configure.world.buttons.save": "<PERSON><PERSON>", "mco.configure.world.buttons.settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.subscription": "Subscripció", "mco.configure.world.buttons.switchminigame": "Canvia el mini joc", "mco.configure.world.close.question.line1": "El teu realm no estarà disponible.", "mco.configure.world.close.question.line2": "Segur que vols continuar?", "mco.configure.world.close.question.title": "Necessites fer canvis sense interrupcions?", "mco.configure.world.closing": "Tancant el realm...", "mco.configure.world.commandBlocks": "Blocs de comandaments", "mco.configure.world.delete.button": "Elimina el <PERSON>", "mco.configure.world.delete.question.line1": "El teu realm serà eliminat per sempre", "mco.configure.world.delete.question.line2": "Segur que vols continuar?", "mco.configure.world.description": "Descripció del Realm", "mco.configure.world.edit.slot.name": "Nom del món", "mco.configure.world.edit.subscreen.adventuremap": "Algunes opcions estaran desactivades mentre el món actual estiga en mode aventura", "mco.configure.world.edit.subscreen.experience": "Algunes opcions estaran desactivades mentre el món actual estiga en mode experiència", "mco.configure.world.edit.subscreen.inspiration": "Algunes opcions estaran desactivades mentre el món actual siga una inspiració", "mco.configure.world.forceGameMode": "Força el mode de joc", "mco.configure.world.invite.narration": "Tens %s noves invitacions", "mco.configure.world.invite.profile.name": "Nom", "mco.configure.world.invited": "Convidat", "mco.configure.world.invited.number": "Convidat (%s)", "mco.configure.world.invites.normal.tooltip": "Usuari normal", "mco.configure.world.invites.ops.tooltip": "Operador", "mco.configure.world.invites.remove.tooltip": "Elimina", "mco.configure.world.leave.question.line1": "Si abandones este realm no el voràs fins que no et conviden de nou", "mco.configure.world.leave.question.line2": "Segur que vols continuar?", "mco.configure.world.loading": "Carregant Realm", "mco.configure.world.location": "Ubicació", "mco.configure.world.minigame": "Actual: %s", "mco.configure.world.name": "Nom del Realm", "mco.configure.world.opening": "Obrint el realm...", "mco.configure.world.players.error": "No existeix cap jugador amb eixe nom", "mco.configure.world.players.inviting": "Convidant al jugador...", "mco.configure.world.players.title": "Jugadors", "mco.configure.world.pvp": "PVP (podràs atacar i ser atacat!)", "mco.configure.world.region_preference": "Preferència de regió", "mco.configure.world.region_preference.title": "Selecció de preferència de regió", "mco.configure.world.reset.question.line1": "El teu món serà regenerat i l'actual es perdrà", "mco.configure.world.reset.question.line2": "Segur que vols continuar?", "mco.configure.world.resourcepack.question": "Necessites un paquet de recursos personalitzat per jugar en este el realm.\n\nVols descarregar-lo i jugar-hi?", "mco.configure.world.resourcepack.question.line1": "Este realm requereix un paquet de recursos personalitzat", "mco.configure.world.resourcepack.question.line2": "Vols descarregar-lo i instal·lar-lo automàticament per jugar?", "mco.configure.world.restore.download.question.line1": "El món serà descarregat i afegit als teus mons de joc individual.", "mco.configure.world.restore.download.question.line2": "Vols continuar?", "mco.configure.world.restore.question.line1": "El teu món serà restaurat a la data \"%s\" (%s)", "mco.configure.world.restore.question.line2": "Segur que vols continuar?", "mco.configure.world.settings.expired": "No pots editar la configuració d'un Realm que ha expirat", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot": "Món %s", "mco.configure.world.slot.empty": "<PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "El teu realm canviarà a un altre món", "mco.configure.world.slot.switch.question.line2": "Segur que vols continuar?", "mco.configure.world.slot.tooltip": "Canvia al món", "mco.configure.world.slot.tooltip.active": "Unix-te", "mco.configure.world.slot.tooltip.minigame": "Canvia a minijoc", "mco.configure.world.spawnAnimals": "Genera animals", "mco.configure.world.spawnMonsters": "Genera monstres", "mco.configure.world.spawnNPCs": "Genera vilatans i els seus pobles", "mco.configure.world.spawnProtection": "Genera protecció", "mco.configure.world.spawn_toggle.message": "Desactivant esta opció eliminaràs totes les entitats existents d'eixe tipus", "mco.configure.world.spawn_toggle.message.npc": "Desactivant esta opció eliminaràs totes les entitats existents d'eixe tipus, com ara vilatans", "mco.configure.world.spawn_toggle.title": "Atenció!", "mco.configure.world.status": "Estat", "mco.configure.world.subscription.day": "dia", "mco.configure.world.subscription.days": "dies", "mco.configure.world.subscription.expired": "Caducat", "mco.configure.world.subscription.extend": "Prolongar la subscripció", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON> d'un dia", "mco.configure.world.subscription.month": "mes", "mco.configure.world.subscription.months": "mesos", "mco.configure.world.subscription.recurring.daysleft": "Es renovarà automàticament en", "mco.configure.world.subscription.recurring.info": "Els canvis fets en la teua subscripció als Realms, com ara afegir més temps o desactivar el pagament, no tindran efecte fins la pròxima data de facturació.", "mco.configure.world.subscription.remaining.days": "%1$s dia/es", "mco.configure.world.subscription.remaining.months": "%1$s mes/os", "mco.configure.world.subscription.remaining.months.days": "%1$s mes/os, %2$s dia/es", "mco.configure.world.subscription.start": "Data d'inici", "mco.configure.world.subscription.tab": "Subscripció", "mco.configure.world.subscription.timeleft": "Temps restant", "mco.configure.world.subscription.title": "La teua subscripció", "mco.configure.world.subscription.unknown": "Desconegut", "mco.configure.world.switch.slot": "<PERSON>rea un món", "mco.configure.world.switch.slot.subtitle": "Este món està buit. Per favor, indica què vols fer", "mco.configure.world.title": "Configura el Realm:", "mco.configure.world.uninvite.player": "Se<PERSON>r que vols desconvidar a \"%s\"?", "mco.configure.world.uninvite.question": "Estàs segur que vols desconvidar", "mco.configure.worlds.title": "Mons", "mco.connect.authorizing": "Identificació en curs...", "mco.connect.connecting": "Connectant amb el realm...", "mco.connect.failed": "Error en connectar amb el realm", "mco.connect.region": "Regió del servidor: %s", "mco.connect.success": "Fet", "mco.create.world": "<PERSON><PERSON>", "mco.create.world.error": "Has de posar-li un nom!", "mco.create.world.failed": "No s'ha pogut crear el món!", "mco.create.world.reset.title": "Creant el món...", "mco.create.world.skip": "Omet", "mco.create.world.subtitle": "Si vols, selecciona el món que t'agradaria posar al teu nou realm", "mco.create.world.wait": "Creant el realm...", "mco.download.cancelled": "Descàrregua cancel·lada", "mco.download.confirmation.line1": "El món que vas a descarregar és més gran que %s", "mco.download.confirmation.line2": "No podràs pujar este món al teu realm mai més", "mco.download.confirmation.oversized": "El món que intentes descarregar és més gran que %s.\n\nNo podràs pujar-lo de nou al teu realm", "mco.download.done": "Descàrrega finalitzada", "mco.download.downloading": "Descarregant", "mco.download.extracting": "Extraient", "mco.download.failed": "Descàrrega fallida", "mco.download.percent": "%s %%", "mco.download.preparing": "Preparant la descàrrega", "mco.download.resourcePack.fail": "Error en descarregar el paquet de recursos!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Descarregant l'últim món", "mco.error.invalid.session.message": "Per favor, prova a reiniciar el Minecraft", "mco.error.invalid.session.title": "Sessió no vàlida", "mco.errorMessage.6001": "Client antiquat", "mco.errorMessage.6002": "Termes de servei no acceptats", "mco.errorMessage.6003": "Límit de descàrregues assolit", "mco.errorMessage.6004": "Límit de pujada assolit", "mco.errorMessage.6005": "<PERSON><PERSON> blo<PERSON>", "mco.errorMessage.6006": "El món està antiquat", "mco.errorMessage.6007": "L'usuari està en massa Realms", "mco.errorMessage.6008": "Nom del Realm no vàlid", "mco.errorMessage.6009": "Descripció del Realm no vàlida", "mco.errorMessage.connectionFailure": "Ha ocorregut un error. Intenta-ho més tard.", "mco.errorMessage.generic": "Ha ocorregut un error: ", "mco.errorMessage.initialize.failed": "No s'ha pogut iniciar el Realm", "mco.errorMessage.noDetails": "No s'han prove<PERSON>t detalls de l'error", "mco.errorMessage.realmsService": "Ha ocorregut un error (%s):", "mco.errorMessage.realmsService.configurationError": "S'ha produït un error inesperat mentre s'editaven les opcions del món", "mco.errorMessage.realmsService.connectivity": "No s'ha pogut connectar als Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "No s'ha pogut comprovar la versió compatible, resposta: %s", "mco.errorMessage.retry": "<PERSON>na-ho a provar", "mco.errorMessage.serviceBusy": "Realms està ocupat en este moment.\nProva a connectar-te al teu Realm de nou en uns minuts.", "mco.gui.button": "<PERSON><PERSON><PERSON>", "mco.gui.ok": "D'acord", "mco.info": "Info!", "mco.invited.player.narration": "Jugador convidat %s", "mco.invites.button.accept": "Accepta", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "No tens invitacions pendents!", "mco.invites.pending": "Noves invitacions!", "mco.invites.title": "Invitacions pendents", "mco.minigame.world.changeButton": "Tria un altre mini joc", "mco.minigame.world.info.line1": "Ai<PERSON>ò substituirà temporalment el teu món per un minijoc!", "mco.minigame.world.info.line2": "Podràs tornar més tard al món original sense perdre res.", "mco.minigame.world.noSelection": "Per favor, tria", "mco.minigame.world.restore": "Finalitzant el mini joc...", "mco.minigame.world.restore.question.line1": "El mini joc acabarà i el teu realm serà restaurat.", "mco.minigame.world.restore.question.line2": "Est<PERSON>s segur que vols fer-ho?", "mco.minigame.world.selected": "Mini joc seleccionat:", "mco.minigame.world.slot.screen.title": "Canviant el món...", "mco.minigame.world.startButton": "Canvia", "mco.minigame.world.starting.screen.title": "Començant el mini joc...", "mco.minigame.world.stopButton": "Acaba el mini joc", "mco.minigame.world.switch.new": "Vols triar un altre mini joc?", "mco.minigame.world.switch.title": "Canvia el mini joc", "mco.minigame.world.title": "Canvia el realm a mini joc", "mco.news": "Notícies dels Realms", "mco.notification.dismiss": "<PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "Transferix ara", "mco.notification.transferSubscription.message": "Les subscripcions dels Realms de Java ara estaran a la botiga de Microsoft. No deixes que la teua subscripció caduque!\nTransferix-la ara i obtín 30 dies de Realms gratuïts.\nVes al teu perfil en minecraft.net per a transferir la teua subscripció.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON>'<PERSON>ç", "mco.notification.visitUrl.message.default": "Per favor, visita l'enllaç de baix", "mco.onlinePlayers": "Jugadors en línia", "mco.play.button.realm.closed": "El Realm està tancat", "mco.question": "Pregunta", "mco.reset.world.adventure": "Aventures", "mco.reset.world.experience": "Experiències", "mco.reset.world.generate": "Nou món", "mco.reset.world.inspiration": "Inspiració", "mco.reset.world.resetting.screen.title": "Reiniciant el món...", "mco.reset.world.seed": "Llavor (opcional)", "mco.reset.world.template": "Plantilles de món", "mco.reset.world.title": "Reinicia el món", "mco.reset.world.upload": "Puja el món", "mco.reset.world.warning": "Açò eliminarà per sempre el teu realm", "mco.selectServer.buy": "Compra un Realm!", "mco.selectServer.close": "Tanca", "mco.selectServer.closed": "Realm tancat", "mco.selectServer.closeserver": "Tanca el realm", "mco.selectServer.configure": "Configura el realm", "mco.selectServer.configureRealm": "Configura el realm", "mco.selectServer.create": "<PERSON><PERSON> un <PERSON>", "mco.selectServer.create.subtitle": "Selecciona un món per posar al nou realm", "mco.selectServer.expired": "Ha caducat el realm", "mco.selectServer.expiredList": "La teua subscripció ha caducat", "mco.selectServer.expiredRenew": "<PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Subscriu-te", "mco.selectServer.expiredTrial": "La teua prova s'ha acabat", "mco.selectServer.expires.day": "<PERSON><PERSON><PERSON>", "mco.selectServer.expires.days": "Caduca en %s dies", "mco.selectServer.expires.soon": "Caducarà prompte", "mco.selectServer.leave": "Abandona el Realm", "mco.selectServer.loading": "Carregant llista dels Realms", "mco.selectServer.mapOnlySupportedForVersion": "Aquest mapa no és compatible en %s", "mco.selectServer.minigame": "Mini joc:", "mco.selectServer.minigameName": "Minijoc: %s", "mco.selectServer.minigameNotSupportedInVersion": "No es pot jugar a este minijoc en %s", "mco.selectServer.noRealms": "Pareix que no tens cap Realm. Afig-ne un per jugar amb els teus amics.", "mco.selectServer.note": "Nota:", "mco.selectServer.open": "Obri el realm", "mco.selectServer.openserver": "<PERSON>bri el Realm", "mco.selectServer.play": "Juga", "mco.selectServer.popup": "Realms és una forma segura i senzilla de jugar en línia amb un món de Minecraft amb, com a màxim, deu amics alhora. Suporta una barbaritat de minijocs i molts mons personalitzats! Només l'amo del Realm ha de pagar.", "mco.selectServer.purchase": "Afig un realm", "mco.selectServer.trial": "Obtín una prova!", "mco.selectServer.uninitialized": "Clica per crear el realm!", "mco.snapshot.createSnapshotPopup.text": "Estàs a punt de crear un Realm de versió en proves gratuït que s'unirà a la teua subscripció pagada de Realms. Este nou Realm de versió en proves serà accessible mentre la subscripció pagada estiga vigent. El teu Realm pagat no es vorà afectat.", "mco.snapshot.createSnapshotPopup.title": "Vols crear un Realm de versió en proves?", "mco.snapshot.creating": "Creant un Realm de versió en proves...", "mco.snapshot.description": "Emparellat amb %s", "mco.snapshot.friendsRealm.downgrade": "Has d'estar en la versió %s per a unir-te a este Realm", "mco.snapshot.friendsRealm.upgrade": "%s ha d'actualitzar el seu Realm abans que pugues jugar des d'esta versió en proves", "mco.snapshot.paired": "Este Realm de versió en proves està emparellat amb %s", "mco.snapshot.parent.tooltip": "Utilitza l'última versió de Minecraft per a jugar a este Realm", "mco.snapshot.start": "Comença un Realm de versió en proves", "mco.snapshot.subscription.info": "Este és un Realm de versió en proves que està unit a la subscripció del teu Realm \"%s\". Romandrà actiu mentre ho estiga el Realm al qual està emparellat.", "mco.snapshot.tooltip": "Utilitza els Realms de versions en proves per a fer una ullada a les pròximes versions de Minecraft, que poden incloure nous continguts i canvis.\n\nPots trobar els teus Realms normals en les versions estrenades del joc.", "mco.snapshotRealmsPopup.message": "Els Realms estan ara disponibles a les versions en proves, a partir de la 23w41a. Totes les subscripcions als Realms inclouen una versió de prova de Realm gratuïta a banda del teu Realm de Java normal!", "mco.snapshotRealmsPopup.title": "Els Realms estan ara disponibles a les versions en proves", "mco.snapshotRealmsPopup.urlText": "Apren-ne més", "mco.template.button.publisher": "Autor", "mco.template.button.select": "Selecciona", "mco.template.button.trailer": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.default.name": "Selecciona una plantilla (opcional)", "mco.template.info.tooltip": "Pàgina de publicació", "mco.template.name": "Plantilla", "mco.template.select.failure": "No hem pogut recuperar la llista de contingut d’esta categoria.\nPer favor, revisa la teua connexió a internet, o intenta-ho més tard.", "mco.template.select.narrate.authors": "Autors: %s", "mco.template.select.narrate.version": "versió %s", "mco.template.select.none": "Vaja! Pareix que la categoria del contingut està buida. Per favor, consulta-ho més tard per obtenir contingut nou, o, si eres un creador, %s.", "mco.template.select.none.linkTitle": "potser podries considerar enviar-nos alguna de les teues idees", "mco.template.title": "Plantilles de mons", "mco.template.title.minigame": "Mini jocs", "mco.template.trailer.tooltip": "Tràiler del mapa", "mco.terms.buttons.agree": "D'acord", "mco.terms.buttons.disagree": "No hi estic d'acord", "mco.terms.sentence.1": "Estic d'acord amb els Minecraft Realms", "mco.terms.sentence.2": "Condicions d'ús", "mco.terms.title": "Condicions d'ús dels Realms", "mco.time.daysAgo": "Fa %1$s dia/es", "mco.time.hoursAgo": "Fa %1$s hora/es", "mco.time.minutesAgo": "Fa %1$s minut/s", "mco.time.now": "ara mateix", "mco.time.secondsAgo": "Fa %1$s segon/s", "mco.trial.message.line1": "Vols aconseguir el teu propi realm?", "mco.trial.message.line2": "Fes clic ací per a més informació!", "mco.upload.button.name": "<PERSON><PERSON><PERSON>", "mco.upload.cancelled": "<PERSON><PERSON><PERSON><PERSON> cancel·lada", "mco.upload.close.failure": "No s'ha pogut tancar el teu realm. Per favor, prova-ho més tard", "mco.upload.done": "<PERSON><PERSON><PERSON><PERSON> completada", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Ha fallat la pujada! (%s)", "mco.upload.failed.too_big.description": "El món seleccionat és massa gran. La grandària màxima permesa és %s.", "mco.upload.failed.too_big.title": "El món és massa gran", "mco.upload.hardcore": "Els mons extrems no es poden actualitzar!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Preparant les característiques del teu món", "mco.upload.select.world.none": "No s'han trobat mons d'un jugador!", "mco.upload.select.world.subtitle": "Per favor, selecciona un món d'un jugador per pujar-lo", "mco.upload.select.world.title": "<PERSON>uja un món", "mco.upload.size.failure.line1": "\"%s\" és massa gran!", "mco.upload.size.failure.line2": "Ocupa %s. La grandària màxima permesa és %s.", "mco.upload.uploading": "Pujant \"%s\"", "mco.upload.verifying": "Verificant el teu mapa", "mco.version": "Versió: %s", "mco.warning": "Atenció!", "mco.worldSlot.minigame": "Minijoc", "menu.custom_options": "Opcions personalitzades...", "menu.custom_options.title": "Opcions personalitzades", "menu.custom_options.tooltip": "Nota: les opcions personalitzades són proporcionades per servidors de tercers i/o altres tipus de contingut.\nUtilitza-ho amb coniximent!", "menu.custom_screen_info.button_narration": "Esta és una pantalla personalitzada. Més informació.", "menu.custom_screen_info.contents": "Els continguts d'aquesta pantalla els controlen servidors de tercers i mapes que no pertanyen, controlen o supervisen Mojang Studios ni Microsoft.\n\nVes a espai! Compte a l'hora d'entrar a enllaços i mai proporciones informació personal ni detalls d'inici de sessió.\n\nSi aquesta pantalla no et deixa jugar, també pots desconnectar-te del servidor actual amb el botó inferior.", "menu.custom_screen_info.disconnect": "S'ha rebutjat la pantalla personalitzada", "menu.custom_screen_info.title": "Informació sobre les pantalles personalitzades", "menu.custom_screen_info.tooltip": "Esta és una pantalla personalitzada. Clica per a més informació.", "menu.disconnect": "Desconnecta", "menu.feedback": "Comentaris...", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "Menú del joc", "menu.modded": " (Modificat)", "menu.multiplayer": "Multijugador", "menu.online": "Minecraft Realms", "menu.options": "Opcions...", "menu.paused": "<PERSON><PERSON> paus<PERSON>", "menu.playdemo": "Juga al món de demostració", "menu.playerReporting": "<PERSON><PERSON><PERSON>", "menu.preparingSpawn": "Preparant l'àrea de reaparició: %s%%", "menu.quick_actions": "Accions ràpides...", "menu.quick_actions.title": "Accions ràpides", "menu.quit": "Eixir del joc", "menu.reportBugs": "Informa d'error", "menu.resetdemo": "Reinicia el món de demostració", "menu.returnToGame": "Torna al joc", "menu.returnToMenu": "Guarda i ix al menú principal", "menu.savingChunks": "Guardant els chunks", "menu.savingLevel": "Guardant el món", "menu.sendFeedback": "Opina", "menu.server_links": "Enllaços del servidor...", "menu.server_links.title": "Enllaços del servidor", "menu.shareToLan": "<PERSON><PERSON><PERSON> per LAN", "menu.singleplayer": "Un jugador", "menu.working": "Processant...", "merchant.deprecated": "Els vilatans reposen dos vegades al dia.", "merchant.level.1": "Novençà", "merchant.level.2": "Aprenent", "merchant.level.3": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.4": "Expert", "merchant.level.5": "Mestre", "merchant.title": "%s - %s", "merchant.trades": "Intercanvis", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Polsa %1$s per desmuntar", "multiplayer.applyingPack": "S'està aplicant el paquet de recursos", "multiplayer.confirm_command.parse_errors": "Estàs intentant executar un comandament no reconegut o invàlid.\nEstàs segur?\nComandament: %s", "multiplayer.confirm_command.permissions_required": "Estàs intentant executar un comandament que requerix permisos elevats. \nÉs possible que això afecte el joc negativament. \nEstàs segur? \nComandament: %s", "multiplayer.confirm_command.title": "Confirma l'execució del comandament", "multiplayer.disconnect.authservers_down": "Els servidors d'autenticació no funcionen. Per favor, prova-ho més tard. Ho lamentem!", "multiplayer.disconnect.bad_chat_index": "Rebut un missatge de xat perdut o reordenat del servidor", "multiplayer.disconnect.banned": "T'han bloquejat en este servidor", "multiplayer.disconnect.banned.expiration": "\nEl teu bloqueig serà eliminat en %s", "multiplayer.disconnect.banned.reason": "T'han bloquejat en este servidor.\nMotiu: %s", "multiplayer.disconnect.banned_ip.expiration": "\nEl teu bloqueig serà eliminat en %s", "multiplayer.disconnect.banned_ip.reason": "La teua adreça IP ha sigut bloquejada en este servidor.\nMotiu: %s", "multiplayer.disconnect.chat_validation_failed": "No s'ha pogut validar el missatge del xat", "multiplayer.disconnect.duplicate_login": "Has iniciat sessi<PERSON> des d'un altre lloc", "multiplayer.disconnect.expired_public_key": "La clau pública del perfil ha expirat. Comprova que l'hora del sistema està sincronitzada i prova de reiniciar el joc.", "multiplayer.disconnect.flying": "Volar no està permés en aquest servidor", "multiplayer.disconnect.generic": "Desconnectat", "multiplayer.disconnect.idling": "Has estat inactiu massa temps!", "multiplayer.disconnect.illegal_characters": "Caràcters no permesos al xat", "multiplayer.disconnect.incompatible": "Client incompatible! Per favor, utilitza %s", "multiplayer.disconnect.invalid_entity_attacked": "Estàs intentant atacar una entitat no vàlida", "multiplayer.disconnect.invalid_packet": "El servidor ha enviat un paquet incorrecte", "multiplayer.disconnect.invalid_player_data": "La informació del jugador no és vàlida", "multiplayer.disconnect.invalid_player_movement": "S'ha rebut un paquet de moviment de jugador invàlid", "multiplayer.disconnect.invalid_public_key_signature": "La signatura per a la clau pública del perfil no és vàlida.\nProva a reiniciar el joc.", "multiplayer.disconnect.invalid_public_key_signature.new": "La signatura per a la clau pública del perfil no és vàlida.\nProva de reiniciar el joc.", "multiplayer.disconnect.invalid_vehicle_movement": "S'ha rebut un paquet de moviment de vehicle invàlid", "multiplayer.disconnect.ip_banned": "La teua IP ha sigut bloquejada en este servidor", "multiplayer.disconnect.kicked": "Has sigut explulsat/da per un administrador", "multiplayer.disconnect.missing_tags": "Conjunt incomplet d'etiquetes rebudes del servidor.\nPer favor, contacta amb l'administrador del servidor.", "multiplayer.disconnect.name_taken": "Eixe nom ja està pillat", "multiplayer.disconnect.not_whitelisted": "No estàs en la llista blanca d'este servidor!", "multiplayer.disconnect.out_of_order_chat": "Xat fora de servici. Has canviat l'hora del sistema?", "multiplayer.disconnect.outdated_client": "Client incompatible! Per favor, fes servir %s", "multiplayer.disconnect.outdated_server": "Client incompatible! Per favor, fes servir %s", "multiplayer.disconnect.server_full": "El servidor està ple!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON><PERSON> tancat", "multiplayer.disconnect.slow_login": "Has tardat massa en iniciar la sessió", "multiplayer.disconnect.too_many_pending_chats": "Massa missatges del xat sense confirmar", "multiplayer.disconnect.transfers_disabled": "El servidor no accepta transferències", "multiplayer.disconnect.unexpected_query_response": "No s'esperaven dades personalitzades per part del client", "multiplayer.disconnect.unsigned_chat": "S'ha rebut un paquet de xat sense signatura o signatura no vàlida.", "multiplayer.disconnect.unverified_username": "No s'ha pogut verificar el teu nom d'usuari!", "multiplayer.downloadingStats": "Recuperant les estadístiques...", "multiplayer.downloadingTerrain": "Carregant el terreny...", "multiplayer.lan.server_found": "S'ha trobat un nou servidor: %s", "multiplayer.message_not_delivered": "No s'ha pogut enviar el missatge. Comprova els registres del servidor: %s", "multiplayer.player.joined": "%s s'ha unit al joc", "multiplayer.player.joined.renamed": "%s (també conegut com %s) s'ha unit al joc", "multiplayer.player.left": "%s se n'ha anat", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Jugadors en línia: %s", "multiplayer.requiredTexturePrompt.disconnect": "El servidor requerix un paquet de recursos personalitzat", "multiplayer.requiredTexturePrompt.line1": "Este servidor requerix l'ús d'un paquet de recursos personalitzat.", "multiplayer.requiredTexturePrompt.line2": "Si rebutges este paquet de recursos personalitzat se't desconnectarà del servidor.", "multiplayer.socialInteractions.not_available": "Les interaccions socials només estan disponibles als mons de multijugador", "multiplayer.status.and_more": "... i %s més ...", "multiplayer.status.cancelled": "Cancel·lat", "multiplayer.status.cannot_connect": "No s'ha pogut connectar amb el servidor", "multiplayer.status.cannot_resolve": "No s'ha pogut resoldre el nom d'amfitrió", "multiplayer.status.finished": "Acabat", "multiplayer.status.incompatible": "Versió incompatible!", "multiplayer.status.motd.narration": "Missatge del dia: %s", "multiplayer.status.no_connection": "(sense connexió)", "multiplayer.status.old": "Antiquat", "multiplayer.status.online": "En línia", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping de %s mil·lisegons", "multiplayer.status.pinging": "Ping en curs...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s de %s jugadors en línia", "multiplayer.status.quitting": "<PERSON><PERSON><PERSON>", "multiplayer.status.request_handled": "S'ha rebut la sol·licitud d'estat", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "S'ha rebut un estat no sol·licitat", "multiplayer.status.version.narration": "Versió del servidor: %s", "multiplayer.stopSleeping": "Alça't del llit", "multiplayer.texturePrompt.failure.line1": "No s'ha pogut aplicar el paquet de recursos del servidor", "multiplayer.texturePrompt.failure.line2": "Tota funcionalitat que necessite recursos personalitzats, podria no funcionar com s'espera", "multiplayer.texturePrompt.line1": "Este servidor recomana l'ús d'un paquet de recursos personalitzat.", "multiplayer.texturePrompt.line2": "T'agradaria baixar-lo i instal·lar-lo automàticament?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMissatge del servidor:\n%s", "multiplayer.title": "Juga a multijugador", "multiplayer.unsecureserver.toast": "Els missatges enviats en este servidor podrien ser modificats i no mostrar el missatge original", "multiplayer.unsecureserver.toast.title": "Missatges del xat no verificables", "multiplayerWarning.check": "No ho tornes a mostrar", "multiplayerWarning.header": "Atenció: joc en línia de tercers", "multiplayerWarning.message": "Atenció: el joc en línia és oferit per servidors de tercers, els quals no són operats, supervisats ni són propietat de Mojang o Microsoft. Durant el joc en línia pots estar exposat a missatges no moderats i altre tipus de contingut que podria no ser adequat per a tots/es.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Botó: %s", "narration.button.usage.focused": "Prem Intro per activar-ho", "narration.button.usage.hovered": "Clic esquerre per activar-ho", "narration.checkbox": "Casella de selecció: %s", "narration.checkbox.usage.focused": "Prem Intro per alternar", "narration.checkbox.usage.hovered": "Clic esquerre per alternar", "narration.component_list.usage": "Prem tabulador per navegar al següent element", "narration.cycle_button.usage.focused": "Prem Intro per canviar a %s", "narration.cycle_button.usage.hovered": "Clic esquerre per canviar a %s", "narration.edit_box": "Caixa d'edició: %s", "narration.item": "Objecte: %s", "narration.recipe": "Recepta per a %s", "narration.recipe.usage": "<PERSON><PERSON> esquerre per seleccionar-ho", "narration.recipe.usage.more": "Clic dret per mostrar més receptes", "narration.selection.usage": "Prem les fletxes d'amunt i avall per moure't a una altra entrada", "narration.slider.usage.focused": "Prem les fletxes esquerra o dreta per canviar-ne el valor", "narration.slider.usage.hovered": "Arrossega la barra per canviar el valor", "narration.suggestion": "S'ha seleccionat el suggeriment %s de %s: %s", "narration.suggestion.tooltip": "S'ha seleccionat el suggeriment %s de %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Prem Tab per passar a la següent suggerència", "narration.suggestion.usage.cycle.hidable": "Prem Tab per passar a la següent suggerència, o Esc per eixir dels suggeriments", "narration.suggestion.usage.fill.fixed": "Prem Tab per utilitzar la suggerència", "narration.suggestion.usage.fill.hidable": "Prem Tab per utilitzar la suggerència, o Esc per eixir dels suggeriments", "narration.tab_navigation.usage": "Prem Ctrl i Tab per canviar entre pestanyes", "narrator.button.accessibility": "Accessibilitat", "narrator.button.difficulty_lock": "Bloqueig de dificultat", "narrator.button.difficulty_lock.locked": "Bloquejada", "narrator.button.difficulty_lock.unlocked": "Desb<PERSON>quejada", "narrator.button.language": "Llengua", "narrator.controls.bound": "%s està vinculat amb %s", "narrator.controls.reset": "Reinicia el botó %s", "narrator.controls.unbound": "%s no està vinculat", "narrator.joining": "Unint-se", "narrator.loading": "Carregant: %s", "narrator.loading.done": "Fet", "narrator.position.list": "Fila %s de %s seleccionada", "narrator.position.object_list": "Element de la fila %s de %s seleccionat", "narrator.position.screen": "Element en la pantalla %s de %s", "narrator.position.tab": "Pestanya %s de %s seleccionada", "narrator.ready_to_play": "Preparat per a jugar", "narrator.screen.title": "Pantalla del títol", "narrator.screen.usage": "Utilitza el cursor del ratolí o el tabulador per seleccionar un element", "narrator.select": "Seleccionat: %s", "narrator.select.world": "Has seleccionat %s, jugat per última vegada: %s, %s, %s, versió: %s", "narrator.select.world_info": "Seleccionat %s, jugat per última vegada: %s, %s", "narrator.toast.disabled": "S'ha desactivat el narrador", "narrator.toast.enabled": "S'ha activat el narrador", "optimizeWorld.confirm.description": "Açò tractarà d'optimitzar el teu món assegurant-se que totes les dades es guarden en el format del joc més recent. Això podria tardar molt de temps, depenent de la grandària del teu món. Una vegada acabat, el món hauria de funcionar encara més ràpid, però ja no serà compatible amb versions antigues. Estàs segur que vols fer-ho?", "optimizeWorld.confirm.proceed": "Crea una còpia de segurat i optimitza", "optimizeWorld.confirm.title": "Optimitza el món", "optimizeWorld.info.converted": "Chunks actualitzats: %s", "optimizeWorld.info.skipped": "Chunks omesos: %s", "optimizeWorld.info.total": "Chunks en total: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Calculant els chunks...", "optimizeWorld.stage.failed": "Error! :(", "optimizeWorld.stage.finished": "Acabant...", "optimizeWorld.stage.finished.chunks": "Acabant d'actualitzar els chunks...", "optimizeWorld.stage.finished.entities": "Acabant d'actualitzar les entitats...", "optimizeWorld.stage.finished.poi": "Acabant d'actualitzar els punts d'interés...", "optimizeWorld.stage.upgrading": "Actualitzant tots els chunks...", "optimizeWorld.stage.upgrading.chunks": "Actualitzant tots els chunks...", "optimizeWorld.stage.upgrading.entities": "Actualitzant totes les entitats...", "optimizeWorld.stage.upgrading.poi": "Actualitzant tots els punts d'interés...", "optimizeWorld.title": "Optimitzant el món \"%s\"", "options.accessibility": "Configuració d'accessibilitat...", "options.accessibility.high_contrast": "Contrast alt", "options.accessibility.high_contrast.error.tooltip": "El paquet de recursos d'alt contrast no està disponible.", "options.accessibility.high_contrast.tooltip": "Millora el contrast dels elements de la IU.", "options.accessibility.high_contrast_block_outline": "Vora de blocs d'alt contrast", "options.accessibility.high_contrast_block_outline.tooltip": "Augmenta el contrast de la vora dels blocs objectiu.", "options.accessibility.link": "Guia d'accessibilitat", "options.accessibility.menu_background_blurriness": "Nitidesa del fons del menú", "options.accessibility.menu_background_blurriness.tooltip": "Canvia la nitidesa dels fons dels menús", "options.accessibility.narrator_hotkey": "Tecla ràpida del narrador", "options.accessibility.narrator_hotkey.mac.tooltip": "Permet activar i desactivar el narrador amb \"Cmd+B\".", "options.accessibility.narrator_hotkey.tooltip": "Permet activar i desactivar el narrador amb \"Ctrl+B\".", "options.accessibility.panorama_speed": "Velocitat del panorama", "options.accessibility.text_background": "Fons del text", "options.accessibility.text_background.chat": "Xat", "options.accessibility.text_background.everywhere": "A tot arreu", "options.accessibility.text_background_opacity": "Opacitat del fons del text", "options.accessibility.title": "Configuració d'accessibilitat...", "options.allowServerListing": "Permet llistats de servidors", "options.allowServerListing.tooltip": "Els servidors podran llistar jugadors en línia com a part del seu estat públic.\nAmb esta opció desactivada el teu nom no apareixerà en estes llistes.", "options.ao": "Il·luminació suau", "options.ao.max": "Màxim", "options.ao.min": "<PERSON><PERSON><PERSON>", "options.ao.off": "NO", "options.attack.crosshair": "punt de mira", "options.attack.hotbar": "barra", "options.attackIndicator": "Indicador d'atac", "options.audioDevice": "Dispositiu", "options.audioDevice.default": "Per defecte del sistema", "options.autoJump": "<PERSON><PERSON> automà<PERSON>", "options.autoSuggestCommands": "Suggerix comandaments", "options.autosaveIndicator": "Indicador d'autoguardat", "options.biomeBlendRadius": "Transició del bioma", "options.biomeBlendRadius.1": "NO (Més rà<PERSON>)", "options.biomeBlendRadius.11": "11x11 (Extrem)", "options.biomeBlendRadius.13": "13x13 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.15": "15x15 (Màxim)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (Normal)", "options.biomeBlendRadius.7": "7x7 (Alt)", "options.biomeBlendRadius.9": "9x9 (<PERSON>lt alt)", "options.chat": "Configuració del xat...", "options.chat.color": "Colors", "options.chat.delay": "Retard del xat: %s segon/s", "options.chat.delay_none": "Retard del xat: cap", "options.chat.height.focused": "Alçada enfocada", "options.chat.height.unfocused": "Alçada desenfocada", "options.chat.line_spacing": "Interlineat", "options.chat.links": "Enllaços a pàgines web", "options.chat.links.prompt": "Preguntar en clicar enllaços", "options.chat.opacity": "Opacitat del text del xat", "options.chat.scale": "Grandària del text del xat", "options.chat.title": "Configuració del xat...", "options.chat.visibility": "Xat", "options.chat.visibility.full": "Visible", "options.chat.visibility.hidden": "Amagat", "options.chat.visibility.system": "Sols comandaments", "options.chat.width": "Amplad<PERSON>", "options.chunks": "%s chunks", "options.clouds.fancy": "Detallats", "options.clouds.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.controls": "Controls...", "options.credits_and_attribution": "Crèdits i atribucions...", "options.damageTiltStrength": "Sacseig per mal", "options.damageTiltStrength.tooltip": "El moviment del sacseig de la càmera causat quan et fas mal.", "options.darkMojangStudiosBackgroundColor": "Logotip <PERSON>oc<PERSON>", "options.darkMojangStudiosBackgroundColor.tooltip": "Canvia el color de fons de la pantalla de càrrega de Mojang Studios a negre.", "options.darknessEffectScale": "Pols fosc", "options.darknessEffectScale.tooltip": "Controla com els polsos de l'efecte de foscor t'afecten quan un castellà o un sculk cridaner te'l donen.", "options.difficulty": "Dificultat", "options.difficulty.easy": "Fàcil", "options.difficulty.easy.info": "Es generen criatures hostils però dèbils. La barra de fam es buidarà i et podrà reduir la vida fins a 5 cors.", "options.difficulty.hard": "Dif<PERSON><PERSON>l", "options.difficulty.hard.info": "Es generen criatures hostils molt fortes. La barra de fam es buidarà fins la mort.", "options.difficulty.hardcore": "Extrem", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Es generen criatures hostils de nivell estàndard. La barra de fam es buidarà i et podrà reduir la vida fins que et quede mig cor.", "options.difficulty.online": "Dificultat del servidor", "options.difficulty.peaceful": "Pacífica", "options.difficulty.peaceful.info": "Sense criatures hostils i només algunes de neutrals. Mai tens fam i et regeneres ràpidament.", "options.directionalAudio": "Àudio direccional", "options.directionalAudio.off.tooltip": "So d'altaveu clàssic", "options.directionalAudio.on.tooltip": "Utilitza audio direccional basat en HRTF per a millorar el so en 3D. Requerix un maquinari d'àudio compatible amb HRTF, i s'experimenta millor amb cascos.", "options.discrete_mouse_scroll": "Desplaçament discret", "options.entityDistanceScaling": "Distància d'entitats", "options.entityShadows": "Ombres de les entitats", "options.font": "Opcions de font...", "options.font.title": "Opcions de font", "options.forceUnicodeFont": "Força font unicode", "options.fov": "Camp de visió", "options.fov.max": "<PERSON><PERSON> <PERSON>", "options.fov.min": "Normal", "options.fovEffectScale": "Efectes del camp de visió", "options.fovEffectScale.tooltip": "Controla quant pot canviar el camp de visió amb determinats efectes.", "options.framerate": "%s fps", "options.framerateLimit": "FPS màxims", "options.framerateLimit.max": "Il·limitat", "options.fullscreen": "Pantalla completa", "options.fullscreen.current": "Actual", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "Resolució de pantalla completa", "options.fullscreen.unavailable": "Configuració no disponible", "options.gamma": "<PERSON><PERSON><PERSON><PERSON>", "options.gamma.default": "Per defecte", "options.gamma.max": "Lluent", "options.gamma.min": "Fosc", "options.generic_value": "%s: %s", "options.glintSpeed": "Velocitat de brillantor", "options.glintSpeed.tooltip": "Controla com de ràpid brillaran els objectes encantats.", "options.glintStrength": "Intensitat de brillantor", "options.glintStrength.tooltip": "Controla la transparència de la brillantor dels objectes encantats.", "options.graphics": "<PERSON><PERSON>à<PERSON><PERSON>", "options.graphics.fabulous": "Meravellosos!", "options.graphics.fabulous.tooltip": "Els gràfics %s utilitzen ombrejadors de pantalla per dibuixar l'oratge, els núvols i les partícules de darrere dels blocs translúcids i l'aigua.\nAixò pot afectar severament el rendiment de dispositius portàtils i pantalles 4K.", "options.graphics.fancy": "Detallats", "options.graphics.fancy.tooltip": "Els gràfics detallats equilibren el rendiment i la qualitat per a la majoria de dispositius.\nL'oratge, els núvols i les partícules poden no aparéixer darrere dels blocs translúcids o l'aigua.", "options.graphics.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "Els gràfics ràpids reduïxen la quantitat de pluja i neu visibles.\nEls efectes de transparència es desactiven per a diversos blocs, com ara les fulles dels arbres.", "options.graphics.warning.accept": "Continua sense suport", "options.graphics.warning.cancel": "<PERSON><PERSON>", "options.graphics.warning.message": "La teua targeta gràfica no és compatible amb els gràfics %s.\n\nPots ignorar açò i continuar, però no tindràs suport per al teu dispositiu en cas que utilitzes els gràfics %s.", "options.graphics.warning.renderer": "Renderitzador detectat: [%s]", "options.graphics.warning.title": "La targeta gràfica no és compatible", "options.graphics.warning.vendor": "Fabricant detectat: [%s]", "options.graphics.warning.version": "Versió d'OpenGL detectada: [%s]", "options.guiScale": "Grandària del menú", "options.guiScale.auto": "Automàtica", "options.hidden": "Amagat", "options.hideLightningFlashes": "Amaga els resplendors dels llamps", "options.hideLightningFlashes.tooltip": "Evita que els llamps facen resplendir el cel. Els llamps en sí, sí que es voran.", "options.hideMatchedNames": "Amaga els noms coincidents", "options.hideMatchedNames.tooltip": "Els servidors de tercers poden enviar missatges de xat en formats no estàndard.\nAmb esta opció activada els jugadors amagats podran ser trobats a través dels noms dels remitents del xat.", "options.hideSplashTexts": "Amaga els textos de portada", "options.hideSplashTexts.tooltip": "Amaga els textos grocs de portada al menú principal.", "options.inactivityFpsLimit": "Reduïx els FPS quan", "options.inactivityFpsLimit.afk": "està inactiu", "options.inactivityFpsLimit.afk.tooltip": "Limita els fotogrames a 30 quan el jugador està inactiu més d'un minut. Es reduïx a 10 després de 9 minuts.", "options.inactivityFpsLimit.minimized": "està minimitzat", "options.inactivityFpsLimit.minimized.tooltip": "<PERSON>ita els fotogrames només quan la finestra del joc està minimitzada.", "options.invertMouse": "Invertir el ratolí", "options.japaneseGlyphVariants": "Variants de símbols japonesos", "options.japaneseGlyphVariants.tooltip": "Utilitza variants japoneses dels caràcters xinesos, coreans i japonesos en la font per defecte.", "options.key.hold": "Mantín", "options.key.toggle": "Canvia", "options.language": "Llengua...", "options.language.title": "Llengua", "options.languageAccuracyWarning": "(Les traduccions són realitzades per la comunitat)", "options.languageWarning": "Les traduccions estan fetes per la comunitat", "options.mainHand": "Mà principal", "options.mainHand.left": "Esquerra", "options.mainHand.right": "Dreta", "options.mipmapLevels": "<PERSON><PERSON><PERSON>", "options.modelPart.cape": "Capa", "options.modelPart.hat": "Barret", "options.modelPart.jacket": "Abric", "options.modelPart.left_pants_leg": "Camal esquerre", "options.modelPart.left_sleeve": "Mànega <PERSON>", "options.modelPart.right_pants_leg": "Camal dret", "options.modelPart.right_sleeve": "Mànega dreta", "options.mouseWheelSensitivity": "Sensibilitat del desplaçament", "options.mouse_settings": "Configuració del ratolí...", "options.mouse_settings.title": "Configuració del ratolí", "options.multiplayer.title": "Configuració del mode multijugador...", "options.multiplier": "%sx", "options.music_frequency": "Freqüència de la música", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "Per defecte", "options.music_frequency.frequent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.music_frequency.tooltip": "Canvia la freqüència amb què sona la música mentre es juga en un món.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "Narra-ho tot", "options.narrator.chat": "Narra el xat", "options.narrator.notavailable": "No disponible", "options.narrator.off": "NO", "options.narrator.system": "Narra el sistema", "options.notifications.display_time": "Temps de notificació", "options.notifications.display_time.tooltip": "Controla quant de temps es mantenen les notificacions visibles en la pantalla.", "options.off": "NO", "options.off.composed": "%s: NO", "options.on": "SÍ", "options.on.composed": "%s: SÍ", "options.online": "En línia...", "options.online.title": "Opcions en línia", "options.onlyShowSecureChat": "Mostra només el xat segur", "options.onlyShowSecureChat.tooltip": "Mostra només els missatges d'altres jugadors que puguen ser verificats que han sigut enviats per estos jugadors, i no han sigut modificats.", "options.operatorItemsTab": "Objectes d'admin.", "options.particles": "Partícules", "options.particles.all": "Totes", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "Mínimes", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Constructor de chunks", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON><PERSON><PERSON> mitj<PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Algunes accions en un chunk faran que este es recompile immediatament. Això inclou la col·locació i destrucció de blocs.", "options.prioritizeChunkUpdates.nearby": "Bloqueig complet", "options.prioritizeChunkUpdates.nearby.tooltip": "Els chunks propers sempre són compilats immediatament. Això es vorà reflectit en el rendiment del joc quan trenques o poses blocs.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Els chunks propers estan compilats en fils paral·lels. Això vol dir que potser hi haja menys forats visuals quan trenques blocs.", "options.rawMouseInput": "Entrada directa", "options.realmsNotifications": "Notícies i invitacions dels Realms", "options.realmsNotifications.tooltip": "Aporta notícies de Realms i invitacions al menú principal i mostra les respectives icones al botó de Realms.", "options.reducedDebugInfo": "Reduir informació de F3", "options.renderClouds": "Núvols", "options.renderCloudsDistance": "Distància de núvols", "options.renderDistance": "Punt remot de visió", "options.resourcepack": "Paquets de recursos...", "options.rotateWithMinecart": "Girs amb les vagonetes", "options.rotateWithMinecart.tooltip": "Controla si la visió del jugador gira amb els girs de les vagonetes. Només disponible als mons amb l'opció \"Millores de les vagonetes\" activada.", "options.screenEffectScale": "Efectes de distorsió", "options.screenEffectScale.tooltip": "Intensitat dels efectes de distorsió de la pantalla causats per l'ois o pels portals del Nether.\nEn valors més baixos, l'efecte d'ois se substituirà per una superposició de color verd a la pantalla.", "options.sensitivity": "Sensibilitat", "options.sensitivity.max": "HIPERVELOCITAT!!!", "options.sensitivity.min": "*aaaa...*", "options.showNowPlayingToast": "Mostra la notificació de la música", "options.showNowPlayingToast.tooltip": "Mostra una notificació quan la música comença a sonar. La mateixa notificació es mostra fixa quan el joc està en pausa i la música està sonant.", "options.showSubtitles": "Mostra subtítols", "options.simulationDistance": "Distància de simulació", "options.skinCustomisation": "Personalització de l'aspecte...", "options.skinCustomisation.title": "Personalització de l'aspecte", "options.sounds": "Música i so...", "options.sounds.title": "Opcions de música i so", "options.telemetry": "Dades telemètriques...", "options.telemetry.button": "Recollida de dades", "options.telemetry.button.tooltip": "\"%s\" inclou només les dades necessàries.\n\"%s\" inclou també les dades opcionals.", "options.telemetry.disabled": "La telemetria està desactivada.", "options.telemetry.state.all": "Totes", "options.telemetry.state.minimal": "Mínimes", "options.telemetry.state.none": "Cap", "options.title": "Opcions", "options.touchscreen": "Mode pantalla tàctil", "options.video": "Configuració de vídeo...", "options.videoTitle": "Configuració de vídeo", "options.viewBobbing": "Moviment de càmera", "options.visible": "Visible", "options.vsync": "VSync", "outOfMemory.message": "Minecraft s'ha quedat sense memòria.\n\nLa causa podria ser un error en el joc o el fet que la màquina virtual de Java no dispose de més memòria.\n\nEl joc s'ha tancat per a previndre mals majors. Hem intentat alliberar memòria sufucient perquè pugues continuar jugant, però potser no ha funcionat.\n\nPer favor, reinicia el joc si tornes a vore este missatge.", "outOfMemory.title": "Sense memòria!", "pack.available.title": "Disponible", "pack.copyFailure": "Error en copiar els paquets", "pack.dropConfirm": "Vols afegir els següents paquets al Minecraft?", "pack.dropInfo": "Arrossega i amolla fitxers en esta finestra per afegir paquets", "pack.dropRejected.message": "Les següents entrades no són paquets vàlids i no s'han copiat:\n %s", "pack.dropRejected.title": "Entrades sense paquet", "pack.folderInfo": "(Posa els fitxers dels paquets ací)", "pack.incompatible": "Incompatible", "pack.incompatible.confirm.new": "Este paquet es va fer per una versió més moderna del Minecraft i potser no funcione correctament.", "pack.incompatible.confirm.old": "Este paquet es va fer per una versió vella del Minecraft i potser no funcione correctament.", "pack.incompatible.confirm.title": "Segur que vols carregar este paquet?", "pack.incompatible.new": "(Fet per a una versió més recent de Minecraft)", "pack.incompatible.old": "(Fet per a una versió antiga de Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Obri la carpeta dels paquets", "pack.selected.title": "Seleccionat", "pack.source.builtin": "integrat", "pack.source.feature": "<PERSON><PERSON><PERSON>", "pack.source.local": "local", "pack.source.server": "servidor", "pack.source.world": "món", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "L'albanés", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "<PERSON><PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Barroc", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Objectiu bombardejat correctament", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Ram", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Calavera en flames", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Pardal de cova", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Canviant", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "<PERSON>, <PERSON><PERSON><PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creesatge", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Terra", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Enemic final", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Falguera", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Lluitadors", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Foc", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Hu<PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "<PERSON><PERSON><PERSON> picant", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON><PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditatiu", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "Or<PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passatge", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Assenyalador", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "<PERSON><PERSON>", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "La piscina", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Passeig pels prats", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "La mar", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Espiral mortal", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Calavera i roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Gira-sols", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Desempaquetat", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "El buit", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "Rodamon", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Erm", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Aigua", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Vent", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "El wither", "painting.random": "<PERSON><PERSON><PERSON>", "parsing.bool.expected": "Es requerix un valor booleà", "parsing.bool.invalid": "<PERSON>or booleà invàlid: \"%s\" no és \"true\" ni \"false\"", "parsing.double.expected": "Es requerix un valor real llarg", "parsing.double.invalid": "Valor real llarg invàlid: %s", "parsing.expected": "Es requerix \"%s\"", "parsing.float.expected": "Es requerix un valor real", "parsing.float.invalid": "Valor real invàlid: %s", "parsing.int.expected": "Es requerix un nombre enter", "parsing.int.invalid": "Nombre enter invàlid: %s", "parsing.long.expected": "S'esperava un enter", "parsing.long.invalid": "El valor enter \"%s\" no és vàlid", "parsing.quote.escape": "La seqüència d'escapament \"\\%s\" és invàlida en una cadena amb cometes", "parsing.quote.expected.end": "Falta el tancament de cometes de la cadena", "parsing.quote.expected.start": "Falten cometes a l'inici de la cadena", "particle.invalidOptions": "No s'han pogut analitzar les opcions de partícula: %s", "particle.notFound": "Partícula desconeguda: \"%s\"", "permissions.requires.entity": "Es requerix una entitat per executar este comandament ací", "permissions.requires.player": "Es requerix un jugador per executar este comandament ací", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Quan s'aplica:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predicat desconegut: %s", "quickplay.error.invalid_identifier": "No s'ha trobat cap món amb l'identificador donat", "quickplay.error.realm_connect": "No s'ha pogut connectar amb el Realm", "quickplay.error.realm_permission": "No tens permís per connectar-te a este Realm", "quickplay.error.title": "Ha fallat el joc ràpid", "realms.configuration.region.australia_east": "Nova Gal·les del Sud, Austràlia", "realms.configuration.region.australia_southeast": "Victòria, Austràlia", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "Índia", "realms.configuration.region.central_us": "Iowa, EUA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virgínia, EUA", "realms.configuration.region.east_us_2": "Carolina del Nord, EUA", "realms.configuration.region.france_central": "Frància", "realms.configuration.region.japan_east": "Japó (est)", "realms.configuration.region.japan_west": "Japó (oest)", "realms.configuration.region.korea_central": "Corea del Sud", "realms.configuration.region.north_central_us": "Illinois, EUA", "realms.configuration.region.north_europe": "Irlanda", "realms.configuration.region.south_central_us": "Texas, EUA", "realms.configuration.region.southeast_asia": "Singapur", "realms.configuration.region.sweden_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.uae_north": "Emirats Àrabs Units (EAU)", "realms.configuration.region.uk_south": "Anglaterra (sud)", "realms.configuration.region.west_central_us": "Utah, EUA", "realms.configuration.region.west_europe": "<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.west_us": "Califòrnia, EUA", "realms.configuration.region.west_us_2": "Washington, EUA", "realms.configuration.region_preference.automatic_owner": "Automàtic (Ping del propietari del Realm)", "realms.configuration.region_preference.automatic_player": "Automàtic (primer en entrar)", "realms.missing.snapshot.error.text": "Minecraft Realms no és compatible amb les versions en proves", "recipe.notFound": "Recepta desconeguda: %s", "recipe.toast.description": "Obri el llibre de receptes", "recipe.toast.title": "Noves receptes desbloquejades!", "record.nowPlaying": "Està sonant: %s", "recover_world.bug_tracker": "Informa d'un error", "recover_world.button": "Intenta recuperar", "recover_world.done.failed": "No s'ha pogut recuperar l'últim estat guardat.", "recover_world.done.success": "La recuperació s'ha fet amb èxit!", "recover_world.done.title": "Recuperació feta", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON> algun arxiu", "recover_world.issue.none": "Cap error", "recover_world.message": "S'han produït els següents errors en intentar llegir la carpeta del món \"%s\".\nEs podria restaurar el món des d'un estat anterior o informar d'este problema al rastrejador d'errors.", "recover_world.no_fallback": "No hi ha cap estat disponible per recuperar", "recover_world.restore": "Intenta restaurar", "recover_world.restoring": "Intentant restaurar el món...", "recover_world.state_entry": "Estat de %s: ", "recover_world.state_entry.unknown": "desconegut", "recover_world.title": "No s'ha pogut carregar el món", "recover_world.warning": "No s'ha pogut carregar el resum del món", "resourcePack.broken_assets": "RECURSOS DEFECTUOSOS DETECTATS", "resourcePack.high_contrast.name": "Contrast alt", "resourcePack.load_fail": "Error en carregar el recurs", "resourcePack.programmer_art.name": "Art del programador", "resourcePack.runtime_failure": "S'ha detectat un error del paquet de recursos", "resourcePack.server.name": "Recursos específics del món", "resourcePack.title": "Selecciona paquet de recursos", "resourcePack.vanilla.description": "L'aparença de sempre del Minecraft", "resourcePack.vanilla.name": "Per defecte", "resourcepack.downloading": "Descarregant el paquet de recursos", "resourcepack.progress": "Descarregant arxiu (%s MB)...", "resourcepack.requesting": "S'està fent la sol·licitud...", "screenshot.failure": "No es pot guardar la captura de pantalla: %s", "screenshot.success": "Guardada captura de pantalla com a %s", "selectServer.add": "Afig un servidor", "selectServer.defaultName": "<PERSON><PERSON><PERSON>", "selectServer.delete": "Elimina", "selectServer.deleteButton": "Elimina", "selectServer.deleteQuestion": "Estàs segur que vols eliminar este servidor?", "selectServer.deleteWarning": "\"%s\" es perdrà per sempre!", "selectServer.direct": "Connexió directa", "selectServer.edit": "<PERSON><PERSON>", "selectServer.hiddenAddress": "(Oculta)", "selectServer.refresh": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.select": "Unix-te al servidor", "selectWorld.access_failure": "No s'ha pogut accedir al món", "selectWorld.allowCommands": "Permetre trucs", "selectWorld.allowCommands.info": "Comandaments com ara /gamemode, /experience", "selectWorld.allowCommands.new": "Permet comandaments", "selectWorld.backupEraseCache": "Esborra les dades de la memòria cau", "selectWorld.backupJoinConfirmButton": "Crea i carrega còpia de seguretat", "selectWorld.backupJoinSkipButton": "Sé el que faig!", "selectWorld.backupQuestion.customized": "Els mons personalitzats ja no són compatibles", "selectWorld.backupQuestion.downgrade": "No pots desactualitzar un món", "selectWorld.backupQuestion.experimental": "Els mons que utilitzen configuració experimental no estan suportats", "selectWorld.backupQuestion.snapshot": "Estàs segur que vols carregar este món?", "selectWorld.backupWarning.customized": "Desgraciadament, no suportem mons personalitzats en esta versió del Minecraft. Podem carregar el món i deixar-ho tot tal qual està, però tot el nou terreny que es genere no serà personalitzat. Ho lamentem!", "selectWorld.backupWarning.downgrade": "Este món s'ha jugat per última vegada amb la versió %s; ara estàs en la %s. Si desactualitzes el món, este podria corrompre's - no podem garantir que funcione. Si així i tot vols continuar, fes una còpia de seguretat abans.", "selectWorld.backupWarning.experimental": "Este món utilitza configuració experimental que podria deixar de funcionar en qualsevol moment. No podem garantir que carregue o funcione. Ves amb compte!", "selectWorld.backupWarning.snapshot": "S'ha jugat a este món per última vegada en la versió %s; estàs en la versió %s. Per favor, fes una còpia de seguretat per evitar la corrupció del món.", "selectWorld.bonusItems": "Cofre de bonificació", "selectWorld.cheats": "Trucs", "selectWorld.commands": "Comandaments", "selectWorld.conversion": "Ha de ser convertit!", "selectWorld.conversion.tooltip": "Has d'obrir este món des d'una versió antiga (com ara la 1.6.4) per a convertir-lo de forma segura", "selectWorld.create": "Crea un nou món", "selectWorld.customizeType": "<PERSON><PERSON><PERSON>", "selectWorld.dataPacks": "Paquets de dades", "selectWorld.data_read": "Llegint les dades del món...", "selectWorld.delete": "Elimina", "selectWorld.deleteButton": "Elimina", "selectWorld.deleteQuestion": "Estàs segur que vols eliminar este món?", "selectWorld.deleteWarning": "\"%s\" es perdrà per sempre! (Molt de temps!)", "selectWorld.delete_failure": "No s'ha pogut eliminar el món", "selectWorld.edit": "<PERSON><PERSON>", "selectWorld.edit.backup": "Fes-ne una còpia", "selectWorld.edit.backupCreated": "Còpia del %s", "selectWorld.edit.backupFailed": "La còpia de seguretat ha fallat", "selectWorld.edit.backupFolder": "Obri la carpeta de còpies de seguretat", "selectWorld.edit.backupSize": "grandària: %s MB", "selectWorld.edit.export_worldgen_settings": "Exporta la configuració de generació", "selectWorld.edit.export_worldgen_settings.failure": "Ha fallat l'exportació", "selectWorld.edit.export_worldgen_settings.success": "Exportat", "selectWorld.edit.openFolder": "Obri la carpeta del món", "selectWorld.edit.optimize": "Optimitza el món", "selectWorld.edit.resetIcon": "Restaura la icona", "selectWorld.edit.save": "Guarda", "selectWorld.edit.title": "Edita el món", "selectWorld.enterName": "Nom del món", "selectWorld.enterSeed": "Llavor per al generador de mons", "selectWorld.experimental": "Experimental", "selectWorld.experimental.details": "Detalls", "selectWorld.experimental.details.entry": "Funcions experimentals requerides: %s", "selectWorld.experimental.details.title": "Requeriments de funcions experimentals", "selectWorld.experimental.message": "Compte!\nEsta configuració requerix característiques que encara estan en desenvolupament. El món podria trencar-se o deixar de funcionar en futures actualitzacions.", "selectWorld.experimental.title": "Alerta de funcions experimentals", "selectWorld.experiments": "Experiments", "selectWorld.experiments.info": "Els experiments són noves característiques potencials. Ves amb compte ja que algunes coses podrien deixar de funcionar. Els experiments no es poden desactivar una vegada s'ha creat el món.", "selectWorld.futureworld.error.text": "Alguna cosa ha anat malament mentre es carregava un món des d'una versió més moderna. Era un risc que hi havia des del principi. <PERSON> sentim, no ha funcionat.", "selectWorld.futureworld.error.title": "Ha ocorregut un error!", "selectWorld.gameMode": "Mode de joc", "selectWorld.gameMode.adventure": "Aventura", "selectWorld.gameMode.adventure.info": "Igual que el mode supervivència però no pots ni llevar ni posar blocs.", "selectWorld.gameMode.adventure.line1": "Igual que al mode supervivència, però no pots", "selectWorld.gameMode.adventure.line2": "ni posar ni trencar blocs", "selectWorld.gameMode.creative": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, construïx i explora sense límits. Pots volar, no tens límit de materials i els monstres no poden fer-te mal.", "selectWorld.gameMode.creative.line1": "Recursos il·limitats, vol lliure i", "selectWorld.gameMode.creative.line2": "destrucció de blocs instantània", "selectWorld.gameMode.hardcore": "Extrem", "selectWorld.gameMode.hardcore.info": "Mode de supervivència amb dificultat bloquejada en \"Difícil\". Si mors no reapareixes.", "selectWorld.gameMode.hardcore.line1": "Igual que al mode supervivència, però bloquejat a la màxima", "selectWorld.gameMode.hardcore.line2": "dificultat, i sense possibilitat de reaparéixer", "selectWorld.gameMode.spectator": "Espectador", "selectWorld.gameMode.spectator.info": "Es mira però no es toca.", "selectWorld.gameMode.spectator.line1": "Pots mirar però no tocar", "selectWorld.gameMode.survival": "Supervivència", "selectWorld.gameMode.survival.info": "Explora un món misteriós on construir, recollir, fabricar i lluitar contra monstres.", "selectWorld.gameMode.survival.line1": "Busca recursos, elabora, guanya", "selectWorld.gameMode.survival.line2": "nivells, salut i fam", "selectWorld.gameRules": "Regles del joc", "selectWorld.import_worldgen_settings": "Importa la configuració", "selectWorld.import_worldgen_settings.failure": "S'ha produït un error en importar la configuració", "selectWorld.import_worldgen_settings.select_file": "Selecciona el fitxer de configuració (.json)", "selectWorld.incompatible.description": "Este món no es pot obrir en esta versió.\nVa ser obert per última vegada en la versió %s.", "selectWorld.incompatible.info": "Versió incompatible: %s", "selectWorld.incompatible.title": "Versió incompatible", "selectWorld.incompatible.tooltip": "Este món on es pot obrir perquè es va crear en una versió incompatible.", "selectWorld.incompatible_series": "Creat per una versió incompatible", "selectWorld.load_folder_access": "No s'ha pogut accedir a la carpeta on es guarden els mons!", "selectWorld.loading_list": "Carregant la llista de mons", "selectWorld.locked": "Bloquejat per una altra instància en execució del Minecraft", "selectWorld.mapFeatures": "Generar estructures", "selectWorld.mapFeatures.info": "Pobles, naufragis, etc.", "selectWorld.mapType": "Tipus de món", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Més opcions del món...", "selectWorld.newWorld": "Nou món", "selectWorld.recreate": "Restaura", "selectWorld.recreate.customized.text": "Els mons personalitzats ja no són compatibles en esta versió del Minecraft. Podem intentar recrear-lo amb la mateixa llavor i propietats, però el terreny personalitzat es perdrà. Ho lamentem!", "selectWorld.recreate.customized.title": "Els mons personalitzats ja no són compatibles", "selectWorld.recreate.error.text": "Alguna cosa ha anat malament a l'hora de recrear el món.", "selectWorld.recreate.error.title": "Ha ocorregut un error!", "selectWorld.resource_load": "Preparant recursos...", "selectWorld.resultFolder": "Es guardarà a:", "selectWorld.search": "busca mons", "selectWorld.seedInfo": "Deixar en blanc per a una llavor aleatòria", "selectWorld.select": "Juga al món seleccionat", "selectWorld.targetFolder": "Guarda a la carpeta: %s", "selectWorld.title": "Selecciona un món", "selectWorld.tooltip.fromNewerVersion1": "Aquest món es va guardar en una versió més recent,", "selectWorld.tooltip.fromNewerVersion2": "i carregar-lo pot causar problemes!", "selectWorld.tooltip.snapshot1": "No t'oblides de fer una còpia de seguretat d'este món", "selectWorld.tooltip.snapshot2": "abans es carregar-lo en aquesta versió preliminar.", "selectWorld.unable_to_load": "No s'han pogut carregar els mons", "selectWorld.version": "Versió:", "selectWorld.versionJoinButton": "Carrega-ho igualment", "selectWorld.versionQuestion": "Realment vols carregar este món?", "selectWorld.versionUnknown": "Desconegut", "selectWorld.versionWarning": "Vas jugar a este món per última vegada en la versió \"%s\" i carregar-lo en esta nova versió podria causar la seua corrupció!", "selectWorld.warning.deprecated.question": "Algunes funcions utilitzades són antigues i podrien deixar de funcionar en un futur. Segur que vols seguir?", "selectWorld.warning.deprecated.title": "Atenció! Eixa configuració utilitza funcions antigues", "selectWorld.warning.experimental.question": "Eixa configuració és experimental i podria deixar de funcionar. Segur que vols seguir?", "selectWorld.warning.experimental.title": "Atenció! Eixa configuració utilitza funcions experimentals", "selectWorld.warning.lowDiskSpace.description": "No queda molt d'espai al teu dispositiu.\nSi et quedes sense espai mentre jugues, el teu món es podria danyar.", "selectWorld.warning.lowDiskSpace.title": "Atenció! Queda poc d'espai al disc!", "selectWorld.world": "Món", "sign.edit": "Edita el missatge del cartell", "sleep.not_possible": "No es pot passar la nit", "sleep.players_sleeping": "%s/%s jugadors dormint", "sleep.skipping_night": "Passant la nit", "slot.only_single_allowed": "Només es permeten espais individuals, s'ha obtingut \"%s\"", "slot.unknown": "Espai desconegut: \"%s\"", "snbt.parser.empty_key": "La clau no pot estar buida", "snbt.parser.expected_binary_numeral": "S'esperava un nombre binari", "snbt.parser.expected_decimal_numeral": "S'esperava un nombre decimal", "snbt.parser.expected_float_type": "S'esperava un nombre en coma flotant", "snbt.parser.expected_hex_escape": "S'esperava un caràcter literal de longitud %s", "snbt.parser.expected_hex_numeral": "S'esperava un nombre hexadecimal", "snbt.parser.expected_integer_type": "S'esperava un nombre enter", "snbt.parser.expected_non_negative_number": "S'esperava un número natural", "snbt.parser.expected_number_or_boolean": "Es requerix un número o un booleà", "snbt.parser.expected_string_uuid": "Es requerix una cadena amb un UUID vàlid", "snbt.parser.expected_unquoted_string": "S'esperava una cadena vàlida sense cometes", "snbt.parser.infinity_not_allowed": "No es poden utilitzar nombres infinits", "snbt.parser.invalid_array_element_type": "El tipus d'element al vector no és vàlid", "snbt.parser.invalid_character_name": "El nom del caràcter unicode no és vàlid", "snbt.parser.invalid_codepoint": "Valor de caràcter de unicode invàlid: %s", "snbt.parser.invalid_string_contents": "Continguts de la cadena no vàlids", "snbt.parser.invalid_unquoted_start": "Les cadenes sense cometes no poden començar amb els dígits 0-9, + o -", "snbt.parser.leading_zero_not_allowed": "Els nombres decimals no poden començar amb 0", "snbt.parser.no_such_operation": "L'operació no existix: %s", "snbt.parser.number_parse_failure": "No s'ha pogut analitzar el nombre: %s", "snbt.parser.undescore_not_allowed": "La barra baixa no pot estar al principi o al final d'un nombre", "soundCategory.ambient": "Ambient/ecosistema", "soundCategory.block": "Blocs", "soundCategory.hostile": "Criatures hostils", "soundCategory.master": "Volum general", "soundCategory.music": "Música", "soundCategory.neutral": "Criatures amigues", "soundCategory.player": "Jugadors", "soundCategory.record": "Tocadiscs/blocs musicals", "soundCategory.ui": "IU", "soundCategory.voice": "Veu", "soundCategory.weather": "Oratge", "spectatorMenu.close": "Tanca el menú", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON><PERSON> segü<PERSON>", "spectatorMenu.previous_page": "Pàgina anterior", "spectatorMenu.root.prompt": "Prem una tecla per seleccionar un comandament i novament per utilitzar-lo.", "spectatorMenu.team_teleport": "Teletransporta a un membre de l'equip", "spectatorMenu.team_teleport.prompt": "Selecciona un equip per teletransportar-te", "spectatorMenu.teleport": "Teletransporta al jugador", "spectatorMenu.teleport.prompt": "Selecciona un jugador per teletransportar-lo a", "stat.generalButton": "General", "stat.itemsButton": "Objectes", "stat.minecraft.animals_bred": "Animals reproduïts", "stat.minecraft.aviate_one_cm": "Distància amb èlitres", "stat.minecraft.bell_ring": "Campanes tocades", "stat.minecraft.boat_one_cm": "Distància en barca", "stat.minecraft.clean_armor": "Peces d'armadura netejades", "stat.minecraft.clean_banner": "Estandards netejats", "stat.minecraft.clean_shulker_box": "S'han buidat les caixes de shulker", "stat.minecraft.climb_one_cm": "Distància escalant", "stat.minecraft.crouch_one_cm": "Distància ajupit", "stat.minecraft.damage_absorbed": "<PERSON><PERSON>it", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON> blo<PERSON> per l'escut", "stat.minecraft.damage_dealt": "<PERSON><PERSON> causat", "stat.minecraft.damage_dealt_absorbed": "<PERSON>y causat (absorbit)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON> causat (resistit)", "stat.minecraft.damage_resisted": "Dany resistit", "stat.minecraft.damage_taken": "<PERSON><PERSON> rebut", "stat.minecraft.deaths": "Morts", "stat.minecraft.drop": "Objectes amollats", "stat.minecraft.eat_cake_slice": "Trossos de pastís menjats", "stat.minecraft.enchant_item": "Objectes encantats", "stat.minecraft.fall_one_cm": "Distància caient", "stat.minecraft.fill_cauldron": "Calderons emplenats", "stat.minecraft.fish_caught": "Peixos pescats", "stat.minecraft.fly_one_cm": "Distància volant", "stat.minecraft.happy_ghast_one_cm": "Distància amb ghast feliç", "stat.minecraft.horse_one_cm": "Distància cavalcada", "stat.minecraft.inspect_dispenser": "Dispensadors examinats", "stat.minecraft.inspect_dropper": "Dispensadors examinats", "stat.minecraft.inspect_hopper": "Tremuges examinades", "stat.minecraft.interact_with_anvil": "Interaccions amb encluses", "stat.minecraft.interact_with_beacon": "Interaccions amb fars màgics", "stat.minecraft.interact_with_blast_furnace": "Interaccions amb alts forns", "stat.minecraft.interact_with_brewingstand": "Interaccions amb altar de pocions", "stat.minecraft.interact_with_campfire": "Interaccions amb fogueres", "stat.minecraft.interact_with_cartography_table": "Interaccions amb taules de cartografia", "stat.minecraft.interact_with_crafting_table": "Objectes manufacturats", "stat.minecraft.interact_with_furnace": "Interaccions amb forns", "stat.minecraft.interact_with_grindstone": "Interaccions amb moles", "stat.minecraft.interact_with_lectern": "Interaccions amb faristols", "stat.minecraft.interact_with_loom": "Interaccions amb telers", "stat.minecraft.interact_with_smithing_table": "Interaccions amb ferreries", "stat.minecraft.interact_with_smoker": "Interaccions amb fumadors", "stat.minecraft.interact_with_stonecutter": "Interaccions amb tallapedres", "stat.minecraft.jump": "<PERSON><PERSON>", "stat.minecraft.leave_game": "<PERSON><PERSON> abandonats", "stat.minecraft.minecart_one_cm": "Distància en vagoneta", "stat.minecraft.mob_kills": "Assassinats a éssers", "stat.minecraft.open_barrel": "<PERSON><PERSON> oberts", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON> oberts", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON>'<PERSON> o<PERSON>", "stat.minecraft.open_shulker_box": "Caixes de shulker obertes", "stat.minecraft.pig_one_cm": "Distància en porc", "stat.minecraft.play_noteblock": "Blocs musicals tocats", "stat.minecraft.play_record": "Discs reproduïts", "stat.minecraft.play_time": "Temps jugat", "stat.minecraft.player_kills": "Jugadors assassinats", "stat.minecraft.pot_flower": "Plantes sembrades en tests", "stat.minecraft.raid_trigger": "Assalts desencadenats", "stat.minecraft.raid_win": "Assalts guanyats", "stat.minecraft.sleep_in_bed": "<PERSON><PERSON> que has dormit en un llit", "stat.minecraft.sneak_time": "Temps acatxat", "stat.minecraft.sprint_one_cm": "Distància corrent", "stat.minecraft.strider_one_cm": "Distància sobre caminants", "stat.minecraft.swim_one_cm": "Distància nadant", "stat.minecraft.talked_to_villager": "Converses amb vilatans", "stat.minecraft.target_hit": "<PERSON><PERSON> en<PERSON>", "stat.minecraft.time_since_death": "Temps des de l'última mort", "stat.minecraft.time_since_rest": "Temps des de l'últim descans", "stat.minecraft.total_world_time": "Temps amb el món obert", "stat.minecraft.traded_with_villager": "Negocis amb els vilatans", "stat.minecraft.trigger_trapped_chest": "Cofres trampa activats", "stat.minecraft.tune_noteblock": "Blocs musicals afinats", "stat.minecraft.use_cauldron": "Aigua agafada dels calderons", "stat.minecraft.walk_on_water_one_cm": "Caminat sobre aigua", "stat.minecraft.walk_one_cm": "Distància a peu", "stat.minecraft.walk_under_water_one_cm": "Distància bussejada", "stat.mobsButton": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "Vegades trencat", "stat_type.minecraft.crafted": "Voltes elaborat", "stat_type.minecraft.dropped": "Amollats", "stat_type.minecraft.killed": "Has matat %s %s", "stat_type.minecraft.killed.none": "<PERSON> has assassinat a %s", "stat_type.minecraft.killed_by": "%s t'ha matat %s vegada/es", "stat_type.minecraft.killed_by.none": "%s mai t'ha matat", "stat_type.minecraft.mined": "Voltes minat", "stat_type.minecraft.picked_up": "Recollits", "stat_type.minecraft.used": "Voltes utilitzat", "stats.none": "-", "structure_block.button.detect_size": "DETECTA", "structure_block.button.load": "CARREGA", "structure_block.button.save": "GUARDA", "structure_block.custom_data": "Nom d'etiqueta personalitzat", "structure_block.detect_size": "Detecta la grandària i la posició de l'estructura:", "structure_block.hover.corner": "Cantó: %s", "structure_block.hover.data": "Dades: %s", "structure_block.hover.load": "Carrega: %s", "structure_block.hover.save": "Guarda: %s", "structure_block.include_entities": "Inclou entitats:", "structure_block.integrity": "Integritat i llavor de l'estructura", "structure_block.integrity.integrity": "Integritat de l'estructura", "structure_block.integrity.seed": "Llavor de l'estructura", "structure_block.invalid_structure_name": "El nom de l'estructura \"%s\" no és vàlid", "structure_block.load_not_found": "L'estructura \"%s\" no està disponible ", "structure_block.load_prepare": "Posició de l'estructura \"%s\" preparada", "structure_block.load_success": "Estructura carregada de \"%s\"", "structure_block.mode.corner": "<PERSON><PERSON><PERSON>", "structure_block.mode.data": "<PERSON><PERSON>", "structure_block.mode.load": "Carrega", "structure_block.mode.save": "Guarda", "structure_block.mode_info.corner": "Mode dels cantons - marcador de col·locació i mida", "structure_block.mode_info.data": "Mode de dades - marcador l<PERSON> del joc", "structure_block.mode_info.load": "Mode de càrrega - carrega des d'un fitxer", "structure_block.mode_info.save": "Mode de guardat - guarda en un fitxer", "structure_block.position": "Posició relativa", "structure_block.position.x": "posició relativa x", "structure_block.position.y": "posició relativa y", "structure_block.position.z": "posició relativa z", "structure_block.save_failure": "No s'ha pogut guardar l'estructura \"%s\"", "structure_block.save_success": "S'ha guardat l'estructura com a \"%s\"", "structure_block.show_air": "Mostra els blocs invisibles:", "structure_block.show_boundingbox": "Mostra els límits:", "structure_block.size": "Tamany de l'estructura", "structure_block.size.x": "grandària de l'estructura x", "structure_block.size.y": "grandària de l'estructura y", "structure_block.size.z": "grandària de l'estructura z", "structure_block.size_failure": "No s’ha pogut detectar la grandària de l’estructura. Afig més cantons amb el nom de l’estructura corresponent", "structure_block.size_success": "Mida detectada correctament per \"%s\"", "structure_block.strict": "Col·locació estricta:", "structure_block.structure_name": "Nom de l'estructura", "subtitles.ambient.cave": "<PERSON><PERSON> misteri<PERSON>", "subtitles.ambient.sound": "Soroll inquietant", "subtitles.block.amethyst_block.chime": "Ametista repicant", "subtitles.block.amethyst_block.resonate": "L'ametista està resonant", "subtitles.block.anvil.destroy": "<PERSON>'ha destruït una enclusa", "subtitles.block.anvil.land": "Una enclusa està caient", "subtitles.block.anvil.use": "S'ha utilitzat una enclusa", "subtitles.block.barrel.close": "S'ha tancat un barril", "subtitles.block.barrel.open": "S'ha obert un barril", "subtitles.block.beacon.activate": "S'ha activat un far màgic", "subtitles.block.beacon.ambient": "Un far màgic està ressonant", "subtitles.block.beacon.deactivate": "S'ha desactivat un far màgic", "subtitles.block.beacon.power_select": "S'ha seleccionat un poder del far màgic", "subtitles.block.beehive.drip": "<PERSON>", "subtitles.block.beehive.enter": "Una abella ha entrat al buc", "subtitles.block.beehive.exit": "Una abella ha eixit del buc", "subtitles.block.beehive.shear": "S'ha brescat un buc", "subtitles.block.beehive.work": "Una abella està treballant", "subtitles.block.bell.resonate": "Una campana està ressonant", "subtitles.block.bell.use": "Està sonant una campana", "subtitles.block.big_dripleaf.tilt_down": "Una fulla relliscosa s'està doblegant cap a baix", "subtitles.block.big_dripleaf.tilt_up": "Una fulla relliscosa s'està doblegant cap a dalt", "subtitles.block.blastfurnace.fire_crackle": "Espurneig d'alt forn", "subtitles.block.brewing_stand.brew": "Bambolles de l'altar de pocions", "subtitles.block.bubble_column.bubble_pop": "Bombolles explotant", "subtitles.block.bubble_column.upwards_ambient": "Flux de bombolles", "subtitles.block.bubble_column.upwards_inside": "So de bombolles", "subtitles.block.bubble_column.whirlpool_ambient": "Remolí de bombolles", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON><PERSON> bomboll<PERSON>", "subtitles.block.button.click": "S'ha polsat un botó", "subtitles.block.cake.add_candle": "Pastís apretant-se", "subtitles.block.campfire.crackle": "Una foguera està espurnejant", "subtitles.block.candle.crackle": "Espurneig de ciri", "subtitles.block.candle.extinguish": "S'ha apagat un ciri", "subtitles.block.chest.close": "S'ha tancat un cofre", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.open": "S'ha obert un cofre", "subtitles.block.chorus_flower.death": "Flor de coral pansida", "subtitles.block.chorus_flower.grow": "Flor de coral madura", "subtitles.block.comparator.click": "Espetecs de comparador", "subtitles.block.composter.empty": "S'ha buidat un compostador", "subtitles.block.composter.fill": "S'ha omplit un compostador", "subtitles.block.composter.ready": "Un compostador està compostant", "subtitles.block.conduit.activate": "S'ha activat un canalitzador", "subtitles.block.conduit.ambient": "Un canalitzador està bategant", "subtitles.block.conduit.attack.target": "Un canalitzador està atacant", "subtitles.block.conduit.deactivate": "S'ha desactivat un canalitzador", "subtitles.block.copper_bulb.turn_off": "S'ha apagat una làmpada de coure", "subtitles.block.copper_bulb.turn_on": "S'ha encés una làmpada de coure", "subtitles.block.copper_trapdoor.close": "S'ha tancat una trapa", "subtitles.block.copper_trapdoor.open": "S'ha obert una trapa", "subtitles.block.crafter.craft": "Un fabricador ha fabricat", "subtitles.block.crafter.fail": "Un fabricador ha fallat", "subtitles.block.creaking_heart.hurt": "Crits de cor de crepitant", "subtitles.block.creaking_heart.idle": "So inquietant", "subtitles.block.creaking_heart.spawn": "S'ha despertat un cor de crepitant", "subtitles.block.deadbush.idle": "So <PERSON><PERSON>", "subtitles.block.decorated_pot.insert": "S'ha emplenat un gerro", "subtitles.block.decorated_pot.insert_fail": "Un gerro està trontollant", "subtitles.block.decorated_pot.shatter": "Un gerro s'ha fet miquetes", "subtitles.block.dispenser.dispense": "Objecte dispensat", "subtitles.block.dispenser.fail": "Error del dispensador", "subtitles.block.door.toggle": "Grinyol de porta", "subtitles.block.dried_ghast.ambient": "Soroll de secor", "subtitles.block.dried_ghast.ambient_water": "Un ghast sec s'està rehidratant", "subtitles.block.dried_ghast.place_in_water": "Un ghast sec s'ha posat a remulla", "subtitles.block.dried_ghast.transition": "Un ghast sec s'està sentint millor", "subtitles.block.dry_grass.ambient": "<PERSON><PERSON> de vent", "subtitles.block.enchantment_table.use": "S'ha utilitzat una taula d'encanteris", "subtitles.block.end_portal.spawn": "S'ha obert un portal a l'End", "subtitles.block.end_portal_frame.fill": "S'ha col·locat un ull d'<PERSON>er", "subtitles.block.eyeblossom.close": "S'ha tancat un florull", "subtitles.block.eyeblossom.idle": "<PERSON><PERSON><PERSON>", "subtitles.block.eyeblossom.open": "S'ha obert un florull", "subtitles.block.fence_gate.toggle": "Cruixit de porta de tanca", "subtitles.block.fire.ambient": "Espurneig de foc", "subtitles.block.fire.extinguish": "Foc extingit", "subtitles.block.firefly_bush.idle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.frogspawn.hatch": "Un cullerot ha desclosat", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON><PERSON><PERSON> de forn", "subtitles.block.generic.break": "Bloc trencat", "subtitles.block.generic.fall": "Alguna cosa ha caigut", "subtitles.block.generic.footsteps": "Passos", "subtitles.block.generic.hit": "Trencament de bloc", "subtitles.block.generic.place": "Bloc col·locat", "subtitles.block.grindstone.use": "<PERSON><PERSON> usada", "subtitles.block.growing_plant.crop": "Planta cultivada", "subtitles.block.hanging_sign.waxed_interact_fail": "Un cartell està trontollant", "subtitles.block.honey_block.slide": "Lliscant per un bloc de mel", "subtitles.block.iron_trapdoor.close": "Tancament de trapa", "subtitles.block.iron_trapdoor.open": "Obertura de trapa", "subtitles.block.lava.ambient": "Bombolla de lava", "subtitles.block.lava.extinguish": "Xiulada de lava", "subtitles.block.lever.click": "Accionament de palanca", "subtitles.block.note_block.note": "Bloc musical sonant", "subtitles.block.pale_hanging_moss.idle": "So inquietant", "subtitles.block.piston.move": "Moviment de pistó", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON><PERSON> gotejant en un calderó", "subtitles.block.pointed_dripstone.drip_water": "Aigua gotejant", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Aigua gotejant en un calderó", "subtitles.block.pointed_dripstone.land": "Ha caigut una estalactita", "subtitles.block.portal.ambient": "Brunzit de portal", "subtitles.block.portal.travel": "El soroll del portal s'atenua", "subtitles.block.portal.trigger": "El soroll del portal s'intensifica", "subtitles.block.pressure_plate.click": "Accionament de placa de pressió", "subtitles.block.pumpkin.carve": "Tisores esquilant", "subtitles.block.redstone_torch.burnout": "Una torxa està crepitant", "subtitles.block.respawn_anchor.ambient": "Brunzit d'àncora de reaparició", "subtitles.block.respawn_anchor.charge": "L'àncora de reaparició està carregada", "subtitles.block.respawn_anchor.deplete": "L'àncora de reaparició està perdent càrrega", "subtitles.block.respawn_anchor.set_spawn": "L'àncora de reaparició ha establit el punt de reaparició", "subtitles.block.sand.idle": "So arenós", "subtitles.block.sand.wind": "So de vent", "subtitles.block.sculk.charge": "Un sculk està bambollejant", "subtitles.block.sculk.spread": "Un sculk s'està escampant", "subtitles.block.sculk_catalyst.bloom": "Un catalitzador de sculk ha fet brotar", "subtitles.block.sculk_sensor.clicking": "Un sensor de sculk ha començat a vibrar", "subtitles.block.sculk_sensor.clicking_stop": "Un sensor de sculk ha parat de vibrar", "subtitles.block.sculk_shrieker.shriek": "Un sculk cridaner està cridant", "subtitles.block.shulker_box.close": "Una caixa de shulker s'ha tancat", "subtitles.block.shulker_box.open": "Una caixa de shulker s'ha obert", "subtitles.block.sign.waxed_interact_fail": "Un cartell s'està balance<PERSON>t", "subtitles.block.smithing_table.use": "S'ha utilitzat una ferreria", "subtitles.block.smoker.smoke": "Un fumador està tirant fum", "subtitles.block.sniffer_egg.crack": "Un ou de rastrejador s'ha clevillat", "subtitles.block.sniffer_egg.hatch": "Un ou de rastrejador ha desclosat", "subtitles.block.sniffer_egg.plop": "Un rastrejador ha post", "subtitles.block.sponge.absorb": "Una esponja està absorbint", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON> caigut baies", "subtitles.block.trapdoor.close": "S'ha tancat una trapa", "subtitles.block.trapdoor.open": "S'ha obert una trapa", "subtitles.block.trapdoor.toggle": "Cruixit de trapa", "subtitles.block.trial_spawner.about_to_spawn_item": "Un objecte ominós s'està preparant", "subtitles.block.trial_spawner.ambient": "Espurneig de generador de reptes", "subtitles.block.trial_spawner.ambient_charged": "Espurneig de generador de reptes ominós", "subtitles.block.trial_spawner.ambient_ominous": "Espurneig de generador de reptes ominós", "subtitles.block.trial_spawner.charge_activate": "Un presagi envolta un generador de reptes", "subtitles.block.trial_spawner.close_shutter": "Un generador de reptes s'ha tancat", "subtitles.block.trial_spawner.detect_player": "Un generador de reptes s'ha recarregat", "subtitles.block.trial_spawner.eject_item": "Un generador de reptes ha expulsat objectes", "subtitles.block.trial_spawner.ominous_activate": "Un presagi envolta un generador de reptes", "subtitles.block.trial_spawner.open_shutter": "Un generador de reptes s'ha obert", "subtitles.block.trial_spawner.spawn_item": "S'ha amollat un objecte ominós", "subtitles.block.trial_spawner.spawn_item_begin": "Ha aparegut un objecte ominós", "subtitles.block.trial_spawner.spawn_mob": "S'ha generat una criatura", "subtitles.block.tripwire.attach": "S'ha connectat una corda trampa", "subtitles.block.tripwire.click": "Accionament de corda trampa", "subtitles.block.tripwire.detach": "S'ha desconnectat una corda trampa", "subtitles.block.vault.activate": "S'ha encés una caixa forta", "subtitles.block.vault.ambient": "Espurneig de caixa forta", "subtitles.block.vault.close_shutter": "S'ha tancat una caixa forta", "subtitles.block.vault.deactivate": "S'ha apagat una caixa forta", "subtitles.block.vault.eject_item": "Una caixa forta ha expulsat un objecte", "subtitles.block.vault.insert_item": "<PERSON>'ha desbloquejat una caixa forta", "subtitles.block.vault.insert_item_fail": "No s'ha pogut desbloquejar una caixa forta", "subtitles.block.vault.open_shutter": "S'ha obert una caixa forta", "subtitles.block.vault.reject_rewarded_player": "Una caixa forta ha rebutjat un jugador", "subtitles.block.water.ambient": "Aigua fluïx", "subtitles.block.wet_sponge.dries": "Una esponja s'ha eixugat", "subtitles.chiseled_bookshelf.insert": "S'ha col·locat un llibre", "subtitles.chiseled_bookshelf.insert_enchanted": "S'ha col·locat un llibre encantat", "subtitles.chiseled_bookshelf.take": "S'ha agafat un llibre", "subtitles.chiseled_bookshelf.take_enchanted": "S'ha agafat un llibre encantat", "subtitles.enchant.thorns.hit": "Punxada amb espines", "subtitles.entity.allay.ambient_with_item": "Un al·lai està buscant", "subtitles.entity.allay.ambient_without_item": "Un al·lai està anhelant", "subtitles.entity.allay.death": "Ha mort un al·lai", "subtitles.entity.allay.hurt": "Han ferit un al·lai", "subtitles.entity.allay.item_given": "Un al·lai està somrient satisfet", "subtitles.entity.allay.item_taken": "Un al·lai està recollint", "subtitles.entity.allay.item_thrown": "Un al·lai està llançant", "subtitles.entity.armadillo.ambient": "Un armadillo està grunyint", "subtitles.entity.armadillo.brush": "S'ha raspallat un armadillo", "subtitles.entity.armadillo.death": "Ha mort un armadillo", "subtitles.entity.armadillo.eat": "Un armadillo està menjant", "subtitles.entity.armadillo.hurt": "<PERSON> ferit un armadillo", "subtitles.entity.armadillo.hurt_reduced": "Un armadillo s'ha protegit", "subtitles.entity.armadillo.land": "Un armadillo ha aterrat", "subtitles.entity.armadillo.peek": "Un armadillo està espiant", "subtitles.entity.armadillo.roll": "Un armadillo s'ha enroscat", "subtitles.entity.armadillo.scute_drop": "Ha caigut una placa", "subtitles.entity.armadillo.unroll_finish": "Un armadillo s'ha desenrotllat", "subtitles.entity.armadillo.unroll_start": "Un armadillo està espiant", "subtitles.entity.armor_stand.fall": "Caiguda d'un objecte", "subtitles.entity.arrow.hit": "Impactes de fletxa", "subtitles.entity.arrow.hit_player": "Impacte a un jugador", "subtitles.entity.arrow.shoot": "Fletxa disparada", "subtitles.entity.axolotl.attack": "Un axolot està atacant", "subtitles.entity.axolotl.death": "Ha mort un axolot", "subtitles.entity.axolotl.hurt": "<PERSON> ferit un axolot", "subtitles.entity.axolotl.idle_air": "Un axolot està cridant", "subtitles.entity.axolotl.idle_water": "Un axolot està cridant", "subtitles.entity.axolotl.splash": "Un axolot està esquitxant", "subtitles.entity.axolotl.swim": "Un axolot està nadant", "subtitles.entity.bat.ambient": "Un ratpenat està cridant", "subtitles.entity.bat.death": "Ha mort un ratpenat", "subtitles.entity.bat.hurt": "Han ferit un ratpenat", "subtitles.entity.bat.takeoff": "Un ratpenat ha començat a volar", "subtitles.entity.bee.ambient": "<PERSON><PERSON><PERSON><PERSON>'<PERSON>", "subtitles.entity.bee.death": "Ha mort una abella", "subtitles.entity.bee.hurt": "<PERSON> ferit una abella", "subtitles.entity.bee.loop": "<PERSON><PERSON><PERSON><PERSON>'<PERSON>", "subtitles.entity.bee.loop_aggressive": "Una abella està brunzint furiosament", "subtitles.entity.bee.pollinate": "Una abella està brunzint feliçment", "subtitles.entity.bee.sting": "Una abella està picant", "subtitles.entity.blaze.ambient": "Respiració de Flama", "subtitles.entity.blaze.burn": "Una Flama està espurnejant", "subtitles.entity.blaze.death": "Ha mort una Flama", "subtitles.entity.blaze.hurt": "<PERSON> ferit una Flama", "subtitles.entity.blaze.shoot": "Una Flama està disparant", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Un esquelet empantanat s'està sacsant", "subtitles.entity.bogged.death": "Ha mort un esquelet empantanat", "subtitles.entity.bogged.hurt": "Han ferit un esquelet empantanat", "subtitles.entity.breeze.charge": "Una brisa està carregant", "subtitles.entity.breeze.death": "Ha mort una brisa", "subtitles.entity.breeze.deflect": "Una brisa ha desviat un atac", "subtitles.entity.breeze.hurt": "<PERSON> ferit una brisa", "subtitles.entity.breeze.idle_air": "Una brisa està volant", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON><PERSON><PERSON> de brisa", "subtitles.entity.breeze.inhale": "Una brisa està inhalant", "subtitles.entity.breeze.jump": "Una brisa està botant", "subtitles.entity.breeze.land": "Una brisa ha aterrat", "subtitles.entity.breeze.shoot": "Una brisa està disparant", "subtitles.entity.breeze.slide": "Una brisa està lliscant", "subtitles.entity.breeze.whirl": "Una brisa està girant", "subtitles.entity.breeze.wind_burst": "Ha explotat una càrrega de vent", "subtitles.entity.camel.ambient": "Un dromedari està grunyint", "subtitles.entity.camel.dash": "Un dromedari s'ha llançat", "subtitles.entity.camel.dash_ready": "Un dromedari s'està recuperant", "subtitles.entity.camel.death": "Ha mort un dromedari", "subtitles.entity.camel.eat": "Un dromedari està menjant", "subtitles.entity.camel.hurt": "<PERSON> ferit un dromedari", "subtitles.entity.camel.saddle": "S'ha equipat una sella de muntar", "subtitles.entity.camel.sit": "Un dromedari s'ha assegut", "subtitles.entity.camel.stand": "Un dromedari s'ha posat dempeus", "subtitles.entity.camel.step": "Passos de dromedari", "subtitles.entity.camel.step_sand": "Passos de dromedari sobre arena", "subtitles.entity.cat.ambient": "Miols de gat", "subtitles.entity.cat.beg_for_food": "Un gat està demanant", "subtitles.entity.cat.death": "Ha mort un gat", "subtitles.entity.cat.eat": "Un gat està menjant", "subtitles.entity.cat.hiss": "Un gat està xiulant", "subtitles.entity.cat.hurt": "Han ferit un gat", "subtitles.entity.cat.purr": "Un gat està rumrumejant", "subtitles.entity.chicken.ambient": "Escataineig", "subtitles.entity.chicken.death": "Ha mort una gallina", "subtitles.entity.chicken.egg": "Una gallina ha post un ou", "subtitles.entity.chicken.hurt": "<PERSON> ferit una gallina", "subtitles.entity.cod.death": "Ha mort un abadejo", "subtitles.entity.cod.flop": "Un abadejo està xipollejant", "subtitles.entity.cod.hurt": "<PERSON> ferit un abadejo", "subtitles.entity.cow.ambient": "Mugit", "subtitles.entity.cow.death": "Ha mort una vaca", "subtitles.entity.cow.hurt": "Han ferit una vaca", "subtitles.entity.cow.milk": "<PERSON> munyit una vaca", "subtitles.entity.creaking.activate": "S'ha activat un crepitant", "subtitles.entity.creaking.ambient": "Un crepitant està crepitant", "subtitles.entity.creaking.attack": "Un crepitant està atacant", "subtitles.entity.creaking.deactivate": "S'ha desactivat un crepitant", "subtitles.entity.creaking.death": "Ha mort un crepitant", "subtitles.entity.creaking.freeze": "Un crepitant ha parat", "subtitles.entity.creaking.spawn": "Un crepitant viu", "subtitles.entity.creaking.sway": "<PERSON> colpejat un crepitant", "subtitles.entity.creaking.twitch": "Un crepitant s'està sacsant", "subtitles.entity.creaking.unfreeze": "Un crepitant es mou", "subtitles.entity.creeper.death": "Ha mort un creeper", "subtitles.entity.creeper.hurt": "<PERSON> ferit un creeper", "subtitles.entity.creeper.primed": "Un creeper està xiulant", "subtitles.entity.dolphin.ambient": "Un dofí està cridant", "subtitles.entity.dolphin.ambient_water": "Un dofí està xiulant", "subtitles.entity.dolphin.attack": "Un dofí està atacant", "subtitles.entity.dolphin.death": "Ha mort un dofí", "subtitles.entity.dolphin.eat": "Un dofí està menjant", "subtitles.entity.dolphin.hurt": "<PERSON> ferit un dofí", "subtitles.entity.dolphin.jump": "Un dofí està botant", "subtitles.entity.dolphin.play": "Un dofí està jugant", "subtitles.entity.dolphin.splash": "Un dofí està esquitxant", "subtitles.entity.dolphin.swim": "Un dofí està nadant", "subtitles.entity.donkey.ambient": "<PERSON>", "subtitles.entity.donkey.angry": "<PERSON>", "subtitles.entity.donkey.chest": "S'ha equipat un cofre en un ase", "subtitles.entity.donkey.death": "Ha mort un ase", "subtitles.entity.donkey.eat": "Un ase està menjant", "subtitles.entity.donkey.hurt": "Han ferit un ase", "subtitles.entity.donkey.jump": "Un ase està botant", "subtitles.entity.drowned.ambient": "Un ofegat està borbollejant", "subtitles.entity.drowned.ambient_water": "Un ofegat està borbollejant", "subtitles.entity.drowned.death": "Ha mort un ofegat", "subtitles.entity.drowned.hurt": "Han ferit un ofegat", "subtitles.entity.drowned.shoot": "Un ofegat ha llançat un trident", "subtitles.entity.drowned.step": "Passos d'ofegat", "subtitles.entity.drowned.swim": "Un ofegat està nadant", "subtitles.entity.egg.throw": "<PERSON>'ha llançat un ou", "subtitles.entity.elder_guardian.ambient": "Un gran guardià de les profunditats està gemegant", "subtitles.entity.elder_guardian.ambient_land": "Un gran guardià de les profunditats està aletejant", "subtitles.entity.elder_guardian.curse": "Un gran guardià de les profunditats t'ha maleït", "subtitles.entity.elder_guardian.death": "Ha mort un gran guardià de les profunditats", "subtitles.entity.elder_guardian.flop": "Un gran guardià de les profunditats està xipollejant", "subtitles.entity.elder_guardian.hurt": "Han ferit un gran guardià de les profunditats", "subtitles.entity.ender_dragon.ambient": "Un drac està rugint", "subtitles.entity.ender_dragon.death": "Ha mort un drac", "subtitles.entity.ender_dragon.flap": "Un drac està volant", "subtitles.entity.ender_dragon.growl": "Un drac està grunyint", "subtitles.entity.ender_dragon.hurt": "<PERSON> ferit un drac", "subtitles.entity.ender_dragon.shoot": "Un drac ha disparat", "subtitles.entity.ender_eye.death": "Està caient un ull d'<PERSON>er", "subtitles.entity.ender_eye.launch": "<PERSON>'ha llan<PERSON>t un ull d'<PERSON>er", "subtitles.entity.ender_pearl.throw": "<PERSON>'ha llançat una perla d'Ender", "subtitles.entity.enderman.ambient": "Un enderman està murmurant", "subtitles.entity.enderman.death": "Ha mort un enderman", "subtitles.entity.enderman.hurt": "<PERSON> ferit un enderman", "subtitles.entity.enderman.scream": "Un enderman està cridant", "subtitles.entity.enderman.stare": "Un enderman està cridant", "subtitles.entity.enderman.teleport": "Un enderman s'ha teletransportat", "subtitles.entity.endermite.ambient": "Un endermite està cavant", "subtitles.entity.endermite.death": "Ha mort un endermite", "subtitles.entity.endermite.hurt": "<PERSON> ferit un endermite", "subtitles.entity.evoker.ambient": "Un evocador està murmurant", "subtitles.entity.evoker.cast_spell": "Un evocador està llançant un conjur", "subtitles.entity.evoker.celebrate": "Un invocador està aplaudint", "subtitles.entity.evoker.death": "Ha mort un evocador", "subtitles.entity.evoker.hurt": "<PERSON> ferit un evocador", "subtitles.entity.evoker.prepare_attack": "Un evocador està preparant-se per atacar", "subtitles.entity.evoker.prepare_summon": "Un evocador està preparant-se per a una invocació", "subtitles.entity.evoker.prepare_wololo": "Un evocador està preparant un encanteri", "subtitles.entity.evoker_fangs.attack": "S'ha trencat un ullal", "subtitles.entity.experience_orb.pickup": "S'ha guanyat experiència", "subtitles.entity.firework_rocket.blast": "Han explotat focs artificials", "subtitles.entity.firework_rocket.launch": "S'han llançat focs artificials", "subtitles.entity.firework_rocket.twinkle": "Focs artificials estan espurne<PERSON>t", "subtitles.entity.fish.swim": "Esquitxant", "subtitles.entity.fishing_bobber.retrieve": "S'ha recuperat un suro", "subtitles.entity.fishing_bobber.splash": "Esquitx de l’esquer de la canya de pescar", "subtitles.entity.fishing_bobber.throw": "S'ha llançat un suro de pesca", "subtitles.entity.fox.aggro": "Una rabosa s'ha enfadat", "subtitles.entity.fox.ambient": "Una rabosa està gemegant", "subtitles.entity.fox.bite": "Una rabosa ha mossegat", "subtitles.entity.fox.death": "Ha mort una rabosa", "subtitles.entity.fox.eat": "Una rabosa està menjant", "subtitles.entity.fox.hurt": "<PERSON> ferit una rabosa", "subtitles.entity.fox.screech": "Una rabosa està cridant", "subtitles.entity.fox.sleep": "Una rabosa està roncant", "subtitles.entity.fox.sniff": "Una rabosa està olorant", "subtitles.entity.fox.spit": "Una rabosa ha escopit", "subtitles.entity.fox.teleport": "Una rabosa s'ha teletransportat", "subtitles.entity.frog.ambient": "<PERSON><PERSON>", "subtitles.entity.frog.death": "Ha mort una granota", "subtitles.entity.frog.eat": "Una granota està menjant", "subtitles.entity.frog.hurt": "Han ferit una granota", "subtitles.entity.frog.lay_spawn": "Una granota ha post", "subtitles.entity.frog.long_jump": "Una granota està saltant", "subtitles.entity.generic.big_fall": "Alguna cosa ha caigut", "subtitles.entity.generic.burn": "Cremant-se", "subtitles.entity.generic.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.drink": "Bevent", "subtitles.entity.generic.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.explode": "Explosió", "subtitles.entity.generic.extinguish_fire": "S'ha extingit el foc", "subtitles.entity.generic.hurt": "Alguna cosa s'ha ferit", "subtitles.entity.generic.small_fall": "Algú ha entropessat", "subtitles.entity.generic.splash": "Esquitxant", "subtitles.entity.generic.swim": "<PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Una càrrega de vent ha esclatat", "subtitles.entity.ghast.ambient": "Un ghast està plorant", "subtitles.entity.ghast.death": "Ha mort un ghast", "subtitles.entity.ghast.hurt": "<PERSON> ferit un ghast", "subtitles.entity.ghast.shoot": "Un ghast està disparant", "subtitles.entity.ghastling.ambient": "Una cria de ghast està parrupant", "subtitles.entity.ghastling.death": "Ha mort una cria de ghast", "subtitles.entity.ghastling.hurt": "<PERSON> ferit una cria de ghast", "subtitles.entity.ghastling.spawn": "Ha aparegut una cria de ghast", "subtitles.entity.glow_item_frame.add_item": "S'ha emplenat un marc fosforescent", "subtitles.entity.glow_item_frame.break": "S'ha trencat un marc fosforescent", "subtitles.entity.glow_item_frame.place": "S'ha col·locat un marc fosforescent", "subtitles.entity.glow_item_frame.remove_item": "S'ha buidat un marc fosforescent", "subtitles.entity.glow_item_frame.rotate_item": "S'ha interaccionat amb un marc fosforescent", "subtitles.entity.glow_squid.ambient": "Un calamar fosforescent està nadant", "subtitles.entity.glow_squid.death": "Ha mort un calamar fosforescent", "subtitles.entity.glow_squid.hurt": "<PERSON> ferit un calamar fosforescent", "subtitles.entity.glow_squid.squirt": "Un calamar fosforescent ha expulsat tinta", "subtitles.entity.goat.ambient": "Una cabra està belant", "subtitles.entity.goat.death": "Ha mort una cabra", "subtitles.entity.goat.eat": "Una cabra està menjant", "subtitles.entity.goat.horn_break": "S'ha trencat una banya de cabra", "subtitles.entity.goat.hurt": "<PERSON> ferit una cabra", "subtitles.entity.goat.long_jump": "Una cabra està saltant", "subtitles.entity.goat.milk": "<PERSON> munyit una cabra", "subtitles.entity.goat.prepare_ram": "Una cabra està xafant", "subtitles.entity.goat.ram_impact": "Una cabra està envestint", "subtitles.entity.goat.screaming.ambient": "Una cabra està bramant", "subtitles.entity.goat.step": "Passos de cabra", "subtitles.entity.guardian.ambient": "Un guardià de les profunditats està gemegant", "subtitles.entity.guardian.ambient_land": "Un guardià de les profunditats està aletejant", "subtitles.entity.guardian.attack": "Un guardià de les profunditats està disparant", "subtitles.entity.guardian.death": "Ha mort un guardià de les profunditats", "subtitles.entity.guardian.flop": "Un guardià de les profunditats està xipollejant", "subtitles.entity.guardian.hurt": "Han ferit un guardià de les profunditats", "subtitles.entity.happy_ghast.ambient": "Un ghast feliç està canturrutxant", "subtitles.entity.happy_ghast.death": "Ha mort un ghast feliç", "subtitles.entity.happy_ghast.equip": "S'ha equipat un arnés", "subtitles.entity.happy_ghast.harness_goggles_down": "Un ghast feliç està preparat", "subtitles.entity.happy_ghast.harness_goggles_up": "Un ghast feliç s'ha parat", "subtitles.entity.happy_ghast.hurt": "<PERSON> ferit un ghast feliç", "subtitles.entity.happy_ghast.unequip": "S'ha llevat un arnés", "subtitles.entity.hoglin.ambient": "Un hoglin està grunyint", "subtitles.entity.hoglin.angry": "Un hoglin està grunyint enfadat", "subtitles.entity.hoglin.attack": "Un hoglin està atacant", "subtitles.entity.hoglin.converted_to_zombified": "Un hoglin s'ha convertit en zoglin", "subtitles.entity.hoglin.death": "Ha mort un hoglin", "subtitles.entity.hoglin.hurt": "<PERSON> ferit un hoglin", "subtitles.entity.hoglin.retreat": "Un hoglin s'està retirant", "subtitles.entity.hoglin.step": "Passos de hoglin", "subtitles.entity.horse.ambient": "<PERSON><PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON>", "subtitles.entity.horse.armor": "S'ha equipat armadura en un cavall", "subtitles.entity.horse.breathe": "Un cavall està respirant", "subtitles.entity.horse.death": "Ha mort un cavall", "subtitles.entity.horse.eat": "Un cavall està menjant", "subtitles.entity.horse.gallop": "Un cavall està galopant", "subtitles.entity.horse.hurt": "<PERSON> ferit un cavall", "subtitles.entity.horse.jump": "Un cavall està saltant", "subtitles.entity.horse.saddle": "S'ha equipat una sella de muntar", "subtitles.entity.husk.ambient": "Una mòmia està gemegant", "subtitles.entity.husk.converted_to_zombie": "Una mòmia s'ha convertit en zombi", "subtitles.entity.husk.death": "Ha mort una mòmia", "subtitles.entity.husk.hurt": "Han ferit una mòmia", "subtitles.entity.illusioner.ambient": "Un il·lusionista està murmurant", "subtitles.entity.illusioner.cast_spell": "Un il·lusionista està llançant un encanteri", "subtitles.entity.illusioner.death": "Ha mort un il·lusionista", "subtitles.entity.illusioner.hurt": "Han ferit un il·lusionista", "subtitles.entity.illusioner.mirror_move": "Un il·lusionista s'està desplaçant", "subtitles.entity.illusioner.prepare_blindness": "Un il·lusionista està preparant ceguera", "subtitles.entity.illusioner.prepare_mirror": "Un il·lusionista està preparant la seua multiplicació", "subtitles.entity.iron_golem.attack": "Un guardià de ferro està atacant", "subtitles.entity.iron_golem.damage": "S'ha trencat un guardià de ferro", "subtitles.entity.iron_golem.death": "Ha mort un guardià de ferro", "subtitles.entity.iron_golem.hurt": "Han ferit un guardià de ferro", "subtitles.entity.iron_golem.repair": "S'ha reparat un guardià de ferro", "subtitles.entity.item.break": "S'ha trencat un objecte", "subtitles.entity.item.pickup": "S'ha agafat un objecte", "subtitles.entity.item_frame.add_item": "S'ha emplenat un marc", "subtitles.entity.item_frame.break": "S'ha trencat un marc", "subtitles.entity.item_frame.place": "S'ha col·locat un marc", "subtitles.entity.item_frame.remove_item": "S'ha buidat un marc", "subtitles.entity.item_frame.rotate_item": "S'ha interaccionat amb un marc", "subtitles.entity.leash_knot.break": "S'ha trencat un llaç", "subtitles.entity.leash_knot.place": "S'ha lligat un llaç", "subtitles.entity.lightning_bolt.impact": "Llamp", "subtitles.entity.lightning_bolt.thunder": "Trons", "subtitles.entity.llama.ambient": "Bel de llama", "subtitles.entity.llama.angry": "Una llama està belant enfadada", "subtitles.entity.llama.chest": "S'ha equipat un cofre a una llama", "subtitles.entity.llama.death": "Ha mort una llama", "subtitles.entity.llama.eat": "Una llama està menjant", "subtitles.entity.llama.hurt": "<PERSON> ferit una llama", "subtitles.entity.llama.spit": "Una llama ha escopit", "subtitles.entity.llama.step": "Passos de llama", "subtitles.entity.llama.swag": "Han decorat una llama", "subtitles.entity.magma_cube.death": "Ha mort un cub de magma", "subtitles.entity.magma_cube.hurt": "Han ferit un cub de magma", "subtitles.entity.magma_cube.squish": "Un cub de magma s'està comprimint", "subtitles.entity.minecart.inside": "Una vagoneta està dringant", "subtitles.entity.minecart.inside_underwater": "Una vagoneta està dringant baix de l'aigua", "subtitles.entity.minecart.riding": "Una vagoneta s'està desplaçant", "subtitles.entity.mooshroom.convert": "Una mooshroom s'està transformant", "subtitles.entity.mooshroom.eat": "Una mooshroom està menjant", "subtitles.entity.mooshroom.milk": "<PERSON> munyit una mooshroom", "subtitles.entity.mooshroom.suspicious_milk": "<PERSON> munyit una mooshroom amb recel", "subtitles.entity.mule.ambient": "<PERSON><PERSON>", "subtitles.entity.mule.angry": "Una mula està bramant", "subtitles.entity.mule.chest": "Han equipat un cofre en una mula", "subtitles.entity.mule.death": "Ha mort una mula", "subtitles.entity.mule.eat": "Una mula està menjant", "subtitles.entity.mule.hurt": "<PERSON> ferit una mula", "subtitles.entity.mule.jump": "Una mula està botant", "subtitles.entity.painting.break": "S'ha despenjat un quadre", "subtitles.entity.painting.place": "<PERSON>'ha penjat un quadre", "subtitles.entity.panda.aggressive_ambient": "Un panda està panteixant", "subtitles.entity.panda.ambient": "Un panda està esbufegant", "subtitles.entity.panda.bite": "Un panda està mossegant", "subtitles.entity.panda.cant_breed": "Esbramec de panda", "subtitles.entity.panda.death": "Ha mort un panda", "subtitles.entity.panda.eat": "Un panda està menjant", "subtitles.entity.panda.hurt": "<PERSON> ferit un panda", "subtitles.entity.panda.pre_sneeze": "Pessigolleig al nas de panda", "subtitles.entity.panda.sneeze": "Un panda ha esternudat", "subtitles.entity.panda.step": "Passos de panda", "subtitles.entity.panda.worried_ambient": "Un panda està gemegant", "subtitles.entity.parrot.ambient": "Un lloro està parlant", "subtitles.entity.parrot.death": "Ha mort un lloro", "subtitles.entity.parrot.eats": "Un lloro està menjant", "subtitles.entity.parrot.fly": "Un lloro està revoltant", "subtitles.entity.parrot.hurts": "<PERSON> ferit un lloro", "subtitles.entity.parrot.imitate.blaze": "Un lloro està respirant", "subtitles.entity.parrot.imitate.bogged": "Un lloro s'està sacsant", "subtitles.entity.parrot.imitate.breeze": "Un lloro està inhalant", "subtitles.entity.parrot.imitate.creaking": "Un lloro està crepitant", "subtitles.entity.parrot.imitate.creeper": "Un lloro està xiulant", "subtitles.entity.parrot.imitate.drowned": "Un lloro està borbollejant", "subtitles.entity.parrot.imitate.elder_guardian": "Un lloro està gemegant", "subtitles.entity.parrot.imitate.ender_dragon": "Un lloro està rugint", "subtitles.entity.parrot.imitate.endermite": "Un lloro està cavant", "subtitles.entity.parrot.imitate.evoker": "Un lloro està murmurant", "subtitles.entity.parrot.imitate.ghast": "Un lloro està plorant", "subtitles.entity.parrot.imitate.guardian": "Un lloro està gemegant", "subtitles.entity.parrot.imitate.hoglin": "Un lloro està grunyint", "subtitles.entity.parrot.imitate.husk": "Un lloro està gemegant", "subtitles.entity.parrot.imitate.illusioner": "Un lloro està murmurant", "subtitles.entity.parrot.imitate.magma_cube": "Un lloro s'està comprimint", "subtitles.entity.parrot.imitate.phantom": "Un lloro està imitant una ànima", "subtitles.entity.parrot.imitate.piglin": "Un lloro està esbufegant", "subtitles.entity.parrot.imitate.piglin_brute": "Un lloro està esbufegant", "subtitles.entity.parrot.imitate.pillager": "Un lloro està murmurant", "subtitles.entity.parrot.imitate.ravager": "Un lloro està grunyint", "subtitles.entity.parrot.imitate.shulker": "Un lloro està aguaitant", "subtitles.entity.parrot.imitate.silverfish": "Un lloro està xiulant", "subtitles.entity.parrot.imitate.skeleton": "Un lloro s'està sacsant", "subtitles.entity.parrot.imitate.slime": "Un lloro s'està comprimint", "subtitles.entity.parrot.imitate.spider": "Un lloro està xiulant", "subtitles.entity.parrot.imitate.stray": "Un lloro està gemegant", "subtitles.entity.parrot.imitate.vex": "Un lloro està molestant", "subtitles.entity.parrot.imitate.vindicator": "Un lloro està murmurant", "subtitles.entity.parrot.imitate.warden": "Un lloro està gemecant", "subtitles.entity.parrot.imitate.witch": "Un lloro s'està rient", "subtitles.entity.parrot.imitate.wither": "Un lloro s'ha enfadat", "subtitles.entity.parrot.imitate.wither_skeleton": "Un lloro està gemegant", "subtitles.entity.parrot.imitate.zoglin": "Un lloro està grunyint", "subtitles.entity.parrot.imitate.zombie": "Un lloro està gemegant", "subtitles.entity.parrot.imitate.zombie_villager": "Un lloro està gemegant", "subtitles.entity.phantom.ambient": "Una ànima està cridant", "subtitles.entity.phantom.bite": "Una ànima està mossegant", "subtitles.entity.phantom.death": "Ha mort una ànima", "subtitles.entity.phantom.flap": "Una ànima està aletejant", "subtitles.entity.phantom.hurt": "Han ferit una ànima", "subtitles.entity.phantom.swoop": "Una ànima s'està acostant", "subtitles.entity.pig.ambient": "<PERSON><PERSON>", "subtitles.entity.pig.death": "Ha mort un porc", "subtitles.entity.pig.hurt": "<PERSON> ferit un porc", "subtitles.entity.pig.saddle": "S'ha equipat una sella de muntar", "subtitles.entity.piglin.admiring_item": "Un piglin està mirant un objecte", "subtitles.entity.piglin.ambient": "Un piglin està esbufegant", "subtitles.entity.piglin.angry": "Un piglin està esbufegant enfadat", "subtitles.entity.piglin.celebrate": "Un piglin està celebrant", "subtitles.entity.piglin.converted_to_zombified": "Un piglin s'ha convertit en un piglin zombi", "subtitles.entity.piglin.death": "Ha mort un piglin", "subtitles.entity.piglin.hurt": "<PERSON> ferit un piglin", "subtitles.entity.piglin.jealous": "Un piglin està esbufegant amb enveja", "subtitles.entity.piglin.retreat": "Un piglin s'està retirant", "subtitles.entity.piglin.step": "Passos de piglin", "subtitles.entity.piglin_brute.ambient": "Un piglin brut està esbufegant", "subtitles.entity.piglin_brute.angry": "Un piglin brut està esbufegant enfadat", "subtitles.entity.piglin_brute.converted_to_zombified": "Un piglin brut s'ha convertit en piglin zombi", "subtitles.entity.piglin_brute.death": "Ha mort un piglin brut", "subtitles.entity.piglin_brute.hurt": "Han ferit un piglin brut", "subtitles.entity.piglin_brute.step": "Passos de piglin brut", "subtitles.entity.pillager.ambient": "Un bandit està murmurant", "subtitles.entity.pillager.celebrate": "Un bandit està aplaudint", "subtitles.entity.pillager.death": "Ha mort un bandit", "subtitles.entity.pillager.hurt": "<PERSON> ferit un bandit", "subtitles.entity.player.attack.crit": "<PERSON><PERSON>", "subtitles.entity.player.attack.knockback": "Atac amb espenta", "subtitles.entity.player.attack.strong": "Atac fort", "subtitles.entity.player.attack.sweep": "Atac amb batuda", "subtitles.entity.player.attack.weak": "Atac d<PERSON>", "subtitles.entity.player.burp": "Rot", "subtitles.entity.player.death": "Ha mort un jugador", "subtitles.entity.player.freeze_hurt": "Un jugador s'ha congelat", "subtitles.entity.player.hurt": "<PERSON> ferit un jugador", "subtitles.entity.player.hurt_drown": "Un jugador s'està ofegant", "subtitles.entity.player.hurt_on_fire": "Un jugador s'està cremant", "subtitles.entity.player.levelup": "Un jugador ha pujat de nivell", "subtitles.entity.player.teleport": "Un jugador s'ha teletransportat", "subtitles.entity.polar_bear.ambient": "Un os polar està gemegant", "subtitles.entity.polar_bear.ambient_baby": "Una cria d'os polar està brunzint", "subtitles.entity.polar_bear.death": "Ha mort un os polar", "subtitles.entity.polar_bear.hurt": "Han ferit un os polar", "subtitles.entity.polar_bear.warning": "Esbramec d'os polar", "subtitles.entity.potion.splash": "S'ha trencat una botella", "subtitles.entity.potion.throw": "<PERSON>'ha llançat una botella", "subtitles.entity.puffer_fish.blow_out": "Un peix globus s'ha desunflat", "subtitles.entity.puffer_fish.blow_up": "Un peix globus s'ha unflat", "subtitles.entity.puffer_fish.death": "Ha mort un peix globus", "subtitles.entity.puffer_fish.flop": "Un peix globus està xipollejant", "subtitles.entity.puffer_fish.hurt": "Han ferit un peix globus", "subtitles.entity.puffer_fish.sting": "Un peix globus està picant", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.attack": "Un conill està atacant", "subtitles.entity.rabbit.death": "Ha mort un conill", "subtitles.entity.rabbit.hurt": "<PERSON> ferit un conill", "subtitles.entity.rabbit.jump": "Un conill està saltant", "subtitles.entity.ravager.ambient": "Una bèstia dels bandits està grunyint", "subtitles.entity.ravager.attack": "Una bèstia dels bandits està mossegant", "subtitles.entity.ravager.celebrate": "Una bèstia dels bandits està aplaudint", "subtitles.entity.ravager.death": "Ha mort una bèstia dels bandits", "subtitles.entity.ravager.hurt": "Han ferit una bèstia dels bandits", "subtitles.entity.ravager.roar": "Una bèstia dels bandits està rugint", "subtitles.entity.ravager.step": "Passos de bèstia dels bandits", "subtitles.entity.ravager.stunned": "Han atordit una bèstia dels bandits", "subtitles.entity.salmon.death": "Ha mort un salmó", "subtitles.entity.salmon.flop": "Un salmó està xipollejant", "subtitles.entity.salmon.hurt": "<PERSON> ferit un salmó", "subtitles.entity.sheep.ambient": "Bel", "subtitles.entity.sheep.death": "Ha mort una ovella", "subtitles.entity.sheep.hurt": "<PERSON> ferit una ovella", "subtitles.entity.shulker.ambient": "Un shulker està aguaitant", "subtitles.entity.shulker.close": "Un shulker s'ha tancat", "subtitles.entity.shulker.death": "Ha mort un shulker", "subtitles.entity.shulker.hurt": "<PERSON> ferit un shulker", "subtitles.entity.shulker.open": "Un shulker s'ha obert", "subtitles.entity.shulker.shoot": "Un shulker està disparant", "subtitles.entity.shulker.teleport": "Un shulker s'ha teletransportat", "subtitles.entity.shulker_bullet.hit": "Un projectil de shulker ha explotat", "subtitles.entity.shulker_bullet.hurt": "Un projectil de shulker s'ha trencat", "subtitles.entity.silverfish.ambient": "Carrisqueig", "subtitles.entity.silverfish.death": "Ha mort un peixet de plata", "subtitles.entity.silverfish.hurt": "<PERSON> ferit un peixet de plata", "subtitles.entity.skeleton.ambient": "Un esquelet s'està sacsant", "subtitles.entity.skeleton.converted_to_stray": "Un esquelet s'ha convertit en un esquelet glacial", "subtitles.entity.skeleton.death": "Ha mort un esquelet", "subtitles.entity.skeleton.hurt": "Han ferit un esquelet", "subtitles.entity.skeleton.shoot": "Un esquelet ha disparat", "subtitles.entity.skeleton_horse.ambient": "Un cavall esquelètic està gemegant", "subtitles.entity.skeleton_horse.death": "Ha mort un cavall esquelètic", "subtitles.entity.skeleton_horse.hurt": "<PERSON> ferit un cavall esquelètic", "subtitles.entity.skeleton_horse.jump_water": "Un cavall esquelètic està saltant", "subtitles.entity.skeleton_horse.swim": "Un cavall esquelètic està nadant", "subtitles.entity.slime.attack": "Un cub de llim està atacant", "subtitles.entity.slime.death": "Ha mort un cub de llim", "subtitles.entity.slime.hurt": "Han ferit un cub de llim", "subtitles.entity.slime.squish": "Un cub de llim s'està comprimint", "subtitles.entity.sniffer.death": "Ha mort un rastrejador", "subtitles.entity.sniffer.digging": "Un rastrejador està excavant", "subtitles.entity.sniffer.digging_stop": "Un rastrejador s'ha alçat", "subtitles.entity.sniffer.drop_seed": "Un rastrejador ha amollat llavors", "subtitles.entity.sniffer.eat": "Un rastrejador està menjant", "subtitles.entity.sniffer.egg_crack": "Un ou de rastrejador s'ha clevillat", "subtitles.entity.sniffer.egg_hatch": "Un ou de rastrejador ha desclosat", "subtitles.entity.sniffer.happy": "Un rastrejador s'està delectant", "subtitles.entity.sniffer.hurt": "<PERSON> ferit un rastrejador", "subtitles.entity.sniffer.idle": "Un rastrejador està grunyint", "subtitles.entity.sniffer.scenting": "Un rastrejador està olorant", "subtitles.entity.sniffer.searching": "Un rastrejador està buscant", "subtitles.entity.sniffer.sniffing": "Un rastrejador està olorant", "subtitles.entity.sniffer.step": "Passos de rastrejador", "subtitles.entity.snow_golem.death": "Ha mort un ninot de neu", "subtitles.entity.snow_golem.hurt": "<PERSON> ferit un ninot de neu", "subtitles.entity.snowball.throw": "Han llançat una bola de neu", "subtitles.entity.spider.ambient": "Carrisqueig", "subtitles.entity.spider.death": "Ha mort una aranya", "subtitles.entity.spider.hurt": "Han ferit una aranya", "subtitles.entity.squid.ambient": "Un calamar està nadant", "subtitles.entity.squid.death": "Ha mort un calamar", "subtitles.entity.squid.hurt": "<PERSON> ferit un calamar", "subtitles.entity.squid.squirt": "Un calamar està amollant tinta", "subtitles.entity.stray.ambient": "Un esquelet glacial s'està sacsant", "subtitles.entity.stray.death": "Ha mort un esquelet glacial", "subtitles.entity.stray.hurt": "Han ferit un esquelet glacial", "subtitles.entity.strider.death": "Ha mort un caminant", "subtitles.entity.strider.eat": "Un caminant està menjant", "subtitles.entity.strider.happy": "Un caminant està cantant", "subtitles.entity.strider.hurt": "Han ferit un caminant", "subtitles.entity.strider.idle": "Un caminant està cridant", "subtitles.entity.strider.retreat": "Un caminant s'està retirant", "subtitles.entity.tadpole.death": "Ha mort un cullerot", "subtitles.entity.tadpole.flop": "Un cullerot està xipollejant", "subtitles.entity.tadpole.grow_up": "Un cullerot ha crescut", "subtitles.entity.tadpole.hurt": "<PERSON> ferit un cullerot", "subtitles.entity.tnt.primed": "S'ha encés la metxa dels explosius", "subtitles.entity.tropical_fish.death": "Ha mort un peix tropical", "subtitles.entity.tropical_fish.flop": "Un peix tropical està esquitxant", "subtitles.entity.tropical_fish.hurt": "<PERSON> ferit un peix tropical", "subtitles.entity.turtle.ambient_land": "Una tortuga marina està cridant", "subtitles.entity.turtle.death": "Ha mort una tortuga marina", "subtitles.entity.turtle.death_baby": "Ha mort una cria de tortuga marina", "subtitles.entity.turtle.egg_break": "Un ou de tortuga s'ha trencat", "subtitles.entity.turtle.egg_crack": "Un ou de tortuga s'ha clevillat", "subtitles.entity.turtle.egg_hatch": "Un ou de tortuga ha desclosat", "subtitles.entity.turtle.hurt": "Han ferit una tortuga marina", "subtitles.entity.turtle.hurt_baby": "Han ferit una cria de tortuga marina", "subtitles.entity.turtle.lay_egg": "Una tortuga marina ha post un ou", "subtitles.entity.turtle.shamble": "Una tortuga marina està reptant", "subtitles.entity.turtle.shamble_baby": "Una cria de tortuga marina està reptant", "subtitles.entity.turtle.swim": "Una tortuga marina està nadant", "subtitles.entity.vex.ambient": "Un esperit està molestant", "subtitles.entity.vex.charge": "Un esperit està cridant", "subtitles.entity.vex.death": "Ha mort un esperit", "subtitles.entity.vex.hurt": "Han ferit un esperit", "subtitles.entity.villager.ambient": "Un vilatà està xarrant", "subtitles.entity.villager.celebrate": "Un vilatà està aplaudint", "subtitles.entity.villager.death": "Ha mort un vilatà", "subtitles.entity.villager.hurt": "Han ferit un vilatà", "subtitles.entity.villager.no": "Un vilatà ha rebutjat alguna cosa", "subtitles.entity.villager.trade": "Un vilatà està fent negocis", "subtitles.entity.villager.work_armorer": "Un armer està treballant", "subtitles.entity.villager.work_butcher": "Un carnisser està treballant", "subtitles.entity.villager.work_cartographer": "Un cartògraf està treballant", "subtitles.entity.villager.work_cleric": "Un capellà està fent missa", "subtitles.entity.villager.work_farmer": "Un granger està treballant", "subtitles.entity.villager.work_fisherman": "Un pescador està treballant", "subtitles.entity.villager.work_fletcher": "Un fletxer està treballant", "subtitles.entity.villager.work_leatherworker": "Un pelleter està treballant", "subtitles.entity.villager.work_librarian": "Un bibliotecari està treballant", "subtitles.entity.villager.work_mason": "Un obrer està treballant", "subtitles.entity.villager.work_shepherd": "Un pastor està treballant", "subtitles.entity.villager.work_toolsmith": "Un ferreter està treballant", "subtitles.entity.villager.work_weaponsmith": "Un armer està treballant", "subtitles.entity.villager.yes": "Un vilatà ha acceptat alguna cosa", "subtitles.entity.vindicator.ambient": "Un defensor està murmurant", "subtitles.entity.vindicator.celebrate": "Un defensor està aplaudint", "subtitles.entity.vindicator.death": "Ha mort un defensor", "subtitles.entity.vindicator.hurt": "<PERSON> ferit un defensor", "subtitles.entity.wandering_trader.ambient": "Un comerciant està murmurant", "subtitles.entity.wandering_trader.death": "Ha mort un comerciant", "subtitles.entity.wandering_trader.disappeared": "Ha desaparegut un comerciant", "subtitles.entity.wandering_trader.drink_milk": "Un comerciant està bevent llet", "subtitles.entity.wandering_trader.drink_potion": "Un comerciant està bevent-se una poció", "subtitles.entity.wandering_trader.hurt": "<PERSON> ferit un comerciant", "subtitles.entity.wandering_trader.no": "Un comerciant no està d'acord", "subtitles.entity.wandering_trader.reappeared": "Ha aparegut un comerciant", "subtitles.entity.wandering_trader.trade": "Un comerciant està negociant", "subtitles.entity.wandering_trader.yes": "Un comerciant està d'acord", "subtitles.entity.warden.agitated": "Un castellà està grunyint enfadat", "subtitles.entity.warden.ambient": "Un castellà està gemecant", "subtitles.entity.warden.angry": "Un castellà està furiós", "subtitles.entity.warden.attack_impact": "Un castellà està atacant", "subtitles.entity.warden.death": "Ha mort un castellà", "subtitles.entity.warden.dig": "Un castellà està excavant", "subtitles.entity.warden.emerge": "Un castellà està emergint", "subtitles.entity.warden.heartbeat": "El cor d'un castellà està bategant", "subtitles.entity.warden.hurt": "Han ferit un castellà", "subtitles.entity.warden.listening": "Un castellà està prestant atenció", "subtitles.entity.warden.listening_angry": "Un castellà està prestant atenció enfadat", "subtitles.entity.warden.nearby_close": "Un castellà s'està aproximant", "subtitles.entity.warden.nearby_closer": "Un castellà està avançant", "subtitles.entity.warden.nearby_closest": "Un castellà s'està aproximant", "subtitles.entity.warden.roar": "Un castellà està rugint", "subtitles.entity.warden.sniff": "Un castellà està olorant", "subtitles.entity.warden.sonic_boom": "Un castellà ha explotat", "subtitles.entity.warden.sonic_charge": "Un castellà està carregant", "subtitles.entity.warden.step": "Passos de castellà", "subtitles.entity.warden.tendril_clicks": "Les banyes d'un castellà estan vibrant", "subtitles.entity.wind_charge.throw": "Una càrrega de vent està volant", "subtitles.entity.wind_charge.wind_burst": "Ha explotat una càrrega de vent", "subtitles.entity.witch.ambient": "Una bruixa s'està rient", "subtitles.entity.witch.celebrate": "Una bruixa està aplaudint", "subtitles.entity.witch.death": "Ha mort una bruixa", "subtitles.entity.witch.drink": "Una bruixa està bebent", "subtitles.entity.witch.hurt": "<PERSON> ferit una bruixa", "subtitles.entity.witch.throw": "Una bruixa està llançant pocions", "subtitles.entity.wither.ambient": "Un wither s'ha enfadat", "subtitles.entity.wither.death": "Ha mort un wither", "subtitles.entity.wither.hurt": "Han ferit un wither", "subtitles.entity.wither.shoot": "Un wither està atacant", "subtitles.entity.wither.spawn": "S'ha invocat un wither", "subtitles.entity.wither_skeleton.ambient": "Un esquelet wither s'està sacsant", "subtitles.entity.wither_skeleton.death": "Ha mort un esquelet wither", "subtitles.entity.wither_skeleton.hurt": "Han ferit un esquelet wither", "subtitles.entity.wolf.ambient": "Un llop està panteixant", "subtitles.entity.wolf.bark": "Un llop està lladrant", "subtitles.entity.wolf.death": "Ha mort un llop", "subtitles.entity.wolf.growl": "Un llop està grunyint", "subtitles.entity.wolf.hurt": "<PERSON> ferit un llop", "subtitles.entity.wolf.pant": "Un llop està panteixant", "subtitles.entity.wolf.shake": "Un llop s'està sacsejant", "subtitles.entity.wolf.whine": "Un llop està gemegant", "subtitles.entity.zoglin.ambient": "Un zoglin està grunyint", "subtitles.entity.zoglin.angry": "Un zoglin està grunyint enfadat", "subtitles.entity.zoglin.attack": "Un zoglin està atacant", "subtitles.entity.zoglin.death": "Ha mort un zoglin", "subtitles.entity.zoglin.hurt": "<PERSON> ferit un zoglin", "subtitles.entity.zoglin.step": "Passos de zoglin", "subtitles.entity.zombie.ambient": "Un zombi està gemegant", "subtitles.entity.zombie.attack_wooden_door": "Una porta està tremolant", "subtitles.entity.zombie.break_wooden_door": "Una porta s'ha trencat", "subtitles.entity.zombie.converted_to_drowned": "Un zombi s'ha convertit en un ofegat", "subtitles.entity.zombie.death": "Ha mort un zombi", "subtitles.entity.zombie.destroy_egg": "Han xafat un ou de tortuga", "subtitles.entity.zombie.hurt": "Han ferit un zombi", "subtitles.entity.zombie.infect": "Un zombi està infectant", "subtitles.entity.zombie_horse.ambient": "Un cavall zombi està gemegant", "subtitles.entity.zombie_horse.death": "Ha mort un cavall zombi", "subtitles.entity.zombie_horse.hurt": "<PERSON> ferit un cavall zombi", "subtitles.entity.zombie_villager.ambient": "Un vilatà zombi està gemegant", "subtitles.entity.zombie_villager.converted": "Un vilatà zombi està vociferant", "subtitles.entity.zombie_villager.cure": "Un vilatà zombi està tafanejant", "subtitles.entity.zombie_villager.death": "Ha mort un vilatà zombi", "subtitles.entity.zombie_villager.hurt": "Han ferit un vilatà zombi", "subtitles.entity.zombified_piglin.ambient": "Un piglin zombi està grunyint", "subtitles.entity.zombified_piglin.angry": "Un piglin zombi està grunyint enfadat", "subtitles.entity.zombified_piglin.death": "Ha mort un piglin zombi", "subtitles.entity.zombified_piglin.hurt": "<PERSON> ferit un piglin zombi", "subtitles.event.mob_effect.bad_omen": "Un presagi s'ha establit", "subtitles.event.mob_effect.raid_omen": "Un assalt és imminent a prop", "subtitles.event.mob_effect.trial_omen": "Un repte ominós és imminent a prop", "subtitles.event.raid.horn": "So atronador <PERSON>", "subtitles.item.armor.equip": "Armadura equipada", "subtitles.item.armor.equip_chain": "Una armadura de cota de malla està dringant", "subtitles.item.armor.equip_diamond": "Una armadura de diamant està dringant", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON>", "subtitles.item.armor.equip_gold": "Una armadura d'or està dringant", "subtitles.item.armor.equip_iron": "Una armadura de ferro està dringant", "subtitles.item.armor.equip_leather": "Una armadura de cuir està dringant", "subtitles.item.armor.equip_netherite": "Una armadura de netherita està dringant", "subtitles.item.armor.equip_turtle": "Closques de tortuga equipades", "subtitles.item.armor.equip_wolf": "Una armadura de llop està estirant", "subtitles.item.armor.unequip_wolf": "Un llop s'ha llevat l'armadura", "subtitles.item.axe.scrape": "Destral rascant", "subtitles.item.axe.strip": "Destral rascant", "subtitles.item.axe.wax_off": "Polir cera", "subtitles.item.bone_meal.use": "S'està utilitzant farina d'ossos", "subtitles.item.book.page_turn": "Cruixit de pàgina", "subtitles.item.book.put": "S'ha col·locat un llibre", "subtitles.item.bottle.empty": "S'ha buidat una botella", "subtitles.item.bottle.fill": "Emplenant botella", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "S'està raspallant grava", "subtitles.item.brush.brushing.gravel.complete": "S'ha completat el raspallament de grava", "subtitles.item.brush.brushing.sand": "S'està raspallant arena", "subtitles.item.brush.brushing.sand.complete": "S'ha completat el raspallament d'arena", "subtitles.item.bucket.empty": "S'ha buidat un poal", "subtitles.item.bucket.fill": "S'ha emplenat un poal", "subtitles.item.bucket.fill_axolotl": "Han agafat un axolot", "subtitles.item.bucket.fill_fish": "S'ha capturat un peix", "subtitles.item.bucket.fill_tadpole": "S'ha capturat un cullerot", "subtitles.item.bundle.drop_contents": "S'ha buidat un saquet", "subtitles.item.bundle.insert": "S'ha guardat un objecte", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON> ple", "subtitles.item.bundle.remove_one": "S'ha descarregat un objecte", "subtitles.item.chorus_fruit.teleport": "Un jugador s'ha teletransportat", "subtitles.item.crop.plant": "S'ha cultivat la llavor", "subtitles.item.crossbow.charge": "<PERSON> carregat una ballesta", "subtitles.item.crossbow.hit": "Impactes de fletxa", "subtitles.item.crossbow.load": "Estan carregant una ballesta", "subtitles.item.crossbow.shoot": "<PERSON> disparat una ballesta", "subtitles.item.dye.use": "S'ha tintat alguna cosa", "subtitles.item.elytra.flying": "Xiulets d'èlitres", "subtitles.item.firecharge.use": "<PERSON>'ha llançat una bola de foc", "subtitles.item.flintandsteel.use": "Espetec de sílex i ferro", "subtitles.item.glow_ink_sac.use": "Un sac de tinta fosforescent ha tacat", "subtitles.item.goat_horn.play": "Una banya de cabra està sonant", "subtitles.item.hoe.till": "<PERSON><PERSON><PERSON> ll<PERSON>", "subtitles.item.honey_bottle.drink": "Algú està engolint mel", "subtitles.item.honeycomb.wax_on": "Posar cera", "subtitles.item.horse_armor.unequip": "S'ha llevat una armadura de cavall", "subtitles.item.ink_sac.use": "Un sac de tinta ha tacat", "subtitles.item.lead.break": "S'ha trencat un llaç", "subtitles.item.lead.tied": "S'ha nugat un llaç", "subtitles.item.lead.untied": "S'ha desnugat un llaç", "subtitles.item.llama_carpet.unequip": "S'ha llevat una estora", "subtitles.item.lodestone_compass.lock": "S'ha fixat la brúixola imantada a l'imant", "subtitles.item.mace.smash_air": "Atac de <PERSON>", "subtitles.item.mace.smash_ground": "Atac de <PERSON>", "subtitles.item.nether_wart.plant": "S'ha plantat una llavor", "subtitles.item.ominous_bottle.dispose": "S'ha trencat una botella", "subtitles.item.saddle.unequip": "S'ha llevat una sella de muntar", "subtitles.item.shears.shear": "Han esclafit unes tisores d'esquilar", "subtitles.item.shears.snip": "S'ha tallat un llaç", "subtitles.item.shield.block": "S'ha bloquejat un atac amb un escut", "subtitles.item.shovel.flatten": "Aplanant amb pala", "subtitles.item.spyglass.stop_using": "Ullera plegada", "subtitles.item.spyglass.use": "<PERSON>tens<PERSON><PERSON>ul<PERSON>a", "subtitles.item.totem.use": "S'ha activat un tòtem", "subtitles.item.trident.hit": "Un trident s'ha enfonsat en el cos d'algú", "subtitles.item.trident.hit_ground": "Un trident està vibrant", "subtitles.item.trident.return": "Un trident està tornant", "subtitles.item.trident.riptide": "Un trident està brunzint", "subtitles.item.trident.throw": "Sorolls de trident", "subtitles.item.trident.thunder": "Trons de trident", "subtitles.item.wolf_armor.break": "Una armadura de gos s'ha trencat", "subtitles.item.wolf_armor.crack": "Una armadura de gos s'ha clevillat", "subtitles.item.wolf_armor.damage": "Una armadura de gos s'ha danyat", "subtitles.item.wolf_armor.repair": "Una armadura de gos s'ha reparat", "subtitles.particle.soul_escape": "<PERSON><PERSON><PERSON> escapant", "subtitles.ui.cartography_table.take_result": "S'ha dibuixat un mapa", "subtitles.ui.hud.bubble_pop": "S'acaba l'oxigen", "subtitles.ui.loom.take_result": "S'ha utilitzat un teler", "subtitles.ui.stonecutter.take_result": "S'ha utilitzat un tallapedra", "subtitles.weather.rain": "Està plovent", "symlink_warning.message": "Carregar mons des de carpetes amb enllaços simbòlics pot no ser segur si no saps exactament que estàs fent. Visita %s per a aprendre'n més.", "symlink_warning.message.pack": "<PERSON><PERSON><PERSON> paquets amb enllaços simbòlics pot no ser segur si no saps exactament què estàs fent. Per favor, visita %s per saber-ne més.", "symlink_warning.message.world": "Carregar mons des de carpetes amb enllaços simbòlics pot no ser segur si no saps exactament què estàs fent. Per favor, visita %s per saber-ne més.", "symlink_warning.more_info": "Més informació", "symlink_warning.title": "La carpeta del món conté enllaços simbòlics", "symlink_warning.title.pack": "S'han afegit paquets que contenen enllaços simbòlics", "symlink_warning.title.world": "La carpeta del món conté enllaços simbòlics", "team.collision.always": "Sempre", "team.collision.never": "<PERSON>", "team.collision.pushOtherTeams": "Espentar altres equips", "team.collision.pushOwnTeam": "Espentar al propi equip", "team.notFound": "Equip desconegut: \"%s\"", "team.visibility.always": "Sempre", "team.visibility.hideForOtherTeams": "Amagat per a altres equips", "team.visibility.hideForOwnTeam": "Amagat per al propi equip", "team.visibility.never": "<PERSON>", "telemetry.event.advancement_made.description": "Entendre el context després de fer un avanç ens ajuda a entendre millor i millorar la progressió del joc.", "telemetry.event.advancement_made.title": "Avanç fet", "telemetry.event.game_load_times.description": "Este esdeveniment ens pot ajudar a determinar les millores de rendiment necessàries mesurant el temps d'execució de les diferents fases d'inici.", "telemetry.event.game_load_times.title": "Temps de càrrega del joc", "telemetry.event.optional": "%s (opcional)", "telemetry.event.optional.disabled": "%s (opcional) - Desactivat", "telemetry.event.performance_metrics.description": "Conéixer el rendiment general del Minecraft ens ajuda a optimitzar el joc per a un gran nombre de màquines i sistemes operatius.\nLa versió del joc també ens ajuda a comparar el rendiment per a les noves versions del Minecraft.", "telemetry.event.performance_metrics.title": "Mètriques de rendiment", "telemetry.event.required": "%s (requerit)", "telemetry.event.world_load_times.description": "És important saber quant tardes en unir-te a un món, i com això canvia amb el temps. Per exemple, quan afegim noves funcions o fem canvis tècnics, necessitem vore quin impacte té en el temps de càrrega.", "telemetry.event.world_load_times.title": "Temps de càrrega dels mons", "telemetry.event.world_loaded.description": "Saber com els jugadors juguen al Minecraft (com ara el mode de joc, clients o servidor modificats i versió del joc) ens permet centrar les actualitzacions en les parts on els jugadors més ho necessiten. La càrrega del món està emparellada amb la descàrrega del món per calcular quant ha durat la sessió.", "telemetry.event.world_loaded.title": "Món carregat", "telemetry.event.world_unloaded.description": "Este esdeveniment està emparellat amb la càrrega del món per calcular quant ha durat la sessió.\nLa duració (en segons i tics) es mesura quan la sessió d'un món acaba (tornant al títol o desconnectant d'un servidor).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "Temps de joc (tics)", "telemetry.property.advancement_id.title": "ID de l'avanç", "telemetry.property.client_id.title": "ID del client", "telemetry.property.client_modded.title": "Client modificat", "telemetry.property.dedicated_memory_kb.title": "Memòria dedicada (kB)", "telemetry.property.event_timestamp_utc.title": "Hora de l'esdeveniment (UTC)", "telemetry.property.frame_rate_samples.title": "Fotogrames per segon (FPS)", "telemetry.property.game_mode.title": "Mode de joc", "telemetry.property.game_version.title": "Versió del joc", "telemetry.property.launcher_name.title": "Nom del llançador", "telemetry.property.load_time_bootstrap_ms.title": "Temps de càrrega (mil·lisegons)", "telemetry.property.load_time_loading_overlay_ms.title": "Temps en pantalla de càrrega (mil·lisegons)", "telemetry.property.load_time_pre_window_ms.title": "Temps abans que s'òbriga la finestra (mil·lisegons)", "telemetry.property.load_time_total_time_ms.title": "Temps total de càrrega (mil·lisegons)", "telemetry.property.minecraft_session_id.title": "ID de la sessió del Minecraft", "telemetry.property.new_world.title": "Nou món", "telemetry.property.number_of_samples.title": "Recompte de mostres", "telemetry.property.operating_system.title": "Sistema operatiu", "telemetry.property.opt_in.title": "Inscrit", "telemetry.property.platform.title": "Plataforma", "telemetry.property.realms_map_content.title": "Contingut del mapa de Realms (nom del minijoc)", "telemetry.property.render_distance.title": "Distància de renderitzat", "telemetry.property.render_time_samples.title": "Mostra de temps de renderització", "telemetry.property.seconds_since_load.title": "Temps des de la càrrega (segons)", "telemetry.property.server_modded.title": "Servidor modificat", "telemetry.property.server_type.title": "<PERSON><PERSON><PERSON> de servidor", "telemetry.property.ticks_since_load.title": "Temps des de la càrrega (tics)", "telemetry.property.used_memory_samples.title": "Utilitza memòria d'accés aleatori", "telemetry.property.user_id.title": "ID d'usuari", "telemetry.property.world_load_time_ms.title": "Temps de càrrega del món (mil·lisegons)", "telemetry.property.world_session_id.title": "ID de la sessió del món", "telemetry_info.button.give_feedback": "Dona'ns la teua opinió", "telemetry_info.button.privacy_statement": "Declaració de privacitat", "telemetry_info.button.show_data": "Obri les meues dades", "telemetry_info.opt_in.description": "Consentisc l'enviament de dades de telemetria opcionals", "telemetry_info.property_title": "Dad<PERSON> incloses", "telemetry_info.screen.description": "Recollir estes dades ens ajuda a millorar el Minecraft guiant-nos cap a allò important per als jugadors.\nTambé ens pots enviar la teua opinió per millorar encara més el Minecraft.", "telemetry_info.screen.title": "Recollida de dades telemètriques", "test.error.block_property_mismatch": "La propietat %s que havia de ser %s, ha resultat ser %s", "test.error.block_property_missing": "Falta una propietat del bloc, s'esperava que la propietat %s fora %s", "test.error.entity_property": "L'entitat %s ha fallat la prova: %s", "test.error.entity_property_details": "Han fallat els tests de l'entitat %s: %s, s'esperaven %s, ha sigut %s", "test.error.expected_block": "S'esperava el bloc %s, s'ha obtingut %s", "test.error.expected_block_tag": "S'esperava el bloc en #%s, s'ha obtingut %s", "test.error.expected_container_contents": "El contenidor hauria de tindre %s", "test.error.expected_container_contents_single": "El contenidor hauria de tindre un %s simple", "test.error.expected_empty_container": "El contenidor hauria d'estar buit", "test.error.expected_entity": "S'esperava %s", "test.error.expected_entity_around": "S'esperava %s per existir al voltant de %s, %s, %s", "test.error.expected_entity_count": "S'esperaven %s entitats del tipus %s, s'han trobat %s", "test.error.expected_entity_data": "S'esperava que les dades de l'entitat foren %s, han sigut %s", "test.error.expected_entity_data_predicate": "Les dades de l'entitat no coincidixen per a %s", "test.error.expected_entity_effect": "S'esperava que %s tinguera l'efecte %s %s", "test.error.expected_entity_having": "L'inventari de l'entitat hauria de tindre %s", "test.error.expected_entity_holding": "L'entitat hauria d'estar subjectant %s", "test.error.expected_entity_in_test": "S'esperava que %s existira al test", "test.error.expected_entity_not_touching": "No s'esperava que %s estiguera tocant %s, %s, %s (relatiu: %s, %s, %s)", "test.error.expected_entity_touching": "S'esperava que %s estiguera tocant %s, %s, %s (relatiu: %s, %s, %s)", "test.error.expected_item": "S'esperava un objecte del tipus %s", "test.error.expected_items_count": "S'esperaven %s objectes del tipus %s, s'han trobat %s", "test.error.fail": "<PERSON>'han complit les condicions de fallada", "test.error.invalid_block_type": "S'ha trobat un tipus de bloc no esperat: %s", "test.error.missing_block_entity": "Falta un bloc-entitat", "test.error.position": "%s en %s, %s, %s (relatiu: %s, %s, %s) al tic %s", "test.error.sequence.condition_already_triggered": "Condició ja activada en %s", "test.error.sequence.condition_not_triggered": "La condició no està activada", "test.error.sequence.invalid_tick": "Èxit en tic no vàlid: s'esperava %s", "test.error.sequence.not_completed": "S'ha acabat el temps del test abans que la seqüència es completara", "test.error.set_biome": "Error en establir el bioma per al test", "test.error.spawn_failure": "Error en crear l'entitat %s", "test.error.state_not_equal": "Estat incorrecte. S'esperava %s, ha sigut %s", "test.error.structure.failure": "No s'ha pogut col·locar l'estructura de prova per a %s", "test.error.tick": "%s el tic %s", "test.error.ticking_without_structure": "Verificant prova abans de col·locar l'estructura", "test.error.timeout.no_result": "Sense èxit, o fracàs, entre %s tics", "test.error.timeout.no_sequences_finished": "No ha finalitzat cap seqüència en %s tics", "test.error.too_many_entities": "S'esperava només un %s existent al voltant de %s, %s, %s, però s'han trobat %s", "test.error.unexpected_block": "No s'esperava un bloc en %s", "test.error.unexpected_entity": "No s'esperava que %s existira", "test.error.unexpected_item": "No s'esperava un objecte del tipus %s", "test.error.unknown": "Error intern desconegut: %s", "test.error.value_not_equal": "S'esperava que %s fora %s, ha sigut %s", "test.error.wrong_block_entity": "Tipus de bloc-entitat incorrecte: %s", "test_block.error.missing": "A l'estructura de prova li falta el bloc %s", "test_block.error.too_many": "Massa %s blocs", "test_block.invalid_timeout": "Temps de pausa invàlid (%s) - ha de ser un número de tics positiu", "test_block.message": "Missatge:", "test_block.mode.accept": "Acceptació", "test_block.mode.fail": "Fallida", "test_block.mode.log": "Registre", "test_block.mode.start": "Inici", "test_block.mode_info.accept": "Mode d'acceptació - quan accepta l'èxit d'una prova (o part d'aquesta)", "test_block.mode_info.fail": "Mode fallida - quan la prova falla", "test_block.mode_info.log": "Mode registre - registra un missatge", "test_block.mode_info.start": "Mode inici - el punt de partida d'una prova", "test_instance.action.reset": "Restablix i carrega", "test_instance.action.run": "Carrega i executa", "test_instance.action.save": "Guarda l'estructura", "test_instance.description.batch": "Remesa: %s", "test_instance.description.failed": "Ha fallat: %s", "test_instance.description.function": "Funció: %s", "test_instance.description.invalid_id": "ID de prova invàlid", "test_instance.description.no_test": "No hi ha cap prova", "test_instance.description.structure": "Estructura: %s", "test_instance.description.type": "Tipus: %s", "test_instance.type.block_based": "<PERSON>va basada en blocs", "test_instance.type.function": "Prova de funció integrada", "test_instance_block.entities": "Entitats:", "test_instance_block.error.no_test": "No s'ha pogut executar la instància de prova a %s, %s, %s perquè té una prova sense definir", "test_instance_block.error.no_test_structure": "No es pot executar la instància de prova en %s, %s, %s perquè no conté cap estructura de prova", "test_instance_block.error.unable_to_save": "No s'ha pogut guardar la plantilla de l'estructura de prova per a la instància de prova a %s, %s, %s", "test_instance_block.invalid": "[invàlid]", "test_instance_block.reset_success": "S'ha reiniciat amb èxit la prova: %s", "test_instance_block.rotation": "Rotació:", "test_instance_block.size": "Prova de grandària d'estructura", "test_instance_block.starting": "Iniciant prova %s", "test_instance_block.test_id": "ID d'instància de prova", "title.32bit.deprecation": "S'ha detectat un sistema de 32 bits: això farà que en un futur no pugues jugar, ja que serà necessari un sistema de 64 bits!", "title.32bit.deprecation.realms": "El Minecraft requerirà prompte un sistema de 64 bits, cosa que farà que no pugues jugar o utilitzar Realms en este dispositiu. Hauràs de cancel·lar manualment qualsevol subscripció de Realms.", "title.32bit.deprecation.realms.check": "No ho tornes a mostrar", "title.32bit.deprecation.realms.header": "S'ha detectat un sistema de 32 bits", "title.credits": "Copyright Mojang AB. No ho distribuïsques!", "title.multiplayer.disabled": "El multijugador està desactivat. Per favor comprova la configuració del teu compte de Microsoft.", "title.multiplayer.disabled.banned.name": "Has de canviar-te el nom abans de poder jugar en línia", "title.multiplayer.disabled.banned.permanent": "El teu compte està suspés permanentment de jugar en línia", "title.multiplayer.disabled.banned.temporary": "El teu compte està suspés temporalment de jugar en línia", "title.multiplayer.lan": "Multijugador (LAN)", "title.multiplayer.other": "Multijugador (servidor de tercers)", "title.multiplayer.realms": "Multijugador (Realms)", "title.singleplayer": "Un jugador", "translation.test.args": "%s %s", "translation.test.complex": "Prefix, %s%2$s de nou %s i %1$s finalment %s i també %1$s de nou!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hola %", "translation.test.invalid2": "hola %s", "translation.test.none": "Hola, món!", "translation.test.world": "món", "trim_material.minecraft.amethyst": "Material d'ametista", "trim_material.minecraft.copper": "Material de coure", "trim_material.minecraft.diamond": "Material de diamant", "trim_material.minecraft.emerald": "Material de maragda", "trim_material.minecraft.gold": "Material d'or", "trim_material.minecraft.iron": "Material de ferro", "trim_material.minecraft.lapis": "Material de lapislàtzuli", "trim_material.minecraft.netherite": "Material de netherita", "trim_material.minecraft.quartz": "Material de quars", "trim_material.minecraft.redstone": "Material de redstone", "trim_material.minecraft.resin": "Material de resina", "trim_pattern.minecraft.bolt": "Disseny d'armadura de llamp", "trim_pattern.minecraft.coast": "Disseny d'armadura de costa", "trim_pattern.minecraft.dune": "Disseny d'armadura de duna", "trim_pattern.minecraft.eye": "Disseny d'arm<PERSON><PERSON> d'ull", "trim_pattern.minecraft.flow": "Disseny d'armadura de flux", "trim_pattern.minecraft.host": "Disseny d'armadura d'hostatge", "trim_pattern.minecraft.raiser": "Disseny d'armadura d'elevació", "trim_pattern.minecraft.rib": "Disseny d'armadura d’os", "trim_pattern.minecraft.sentry": "Disseny d'armadura de sentinella", "trim_pattern.minecraft.shaper": "Disseny d'armadura de forma", "trim_pattern.minecraft.silence": "Disseny d'armadura del silenci", "trim_pattern.minecraft.snout": "Disseny d'armadura de morro", "trim_pattern.minecraft.spire": "Disseny d'armadura de cúpula", "trim_pattern.minecraft.tide": "Disseny d'armadura de marea", "trim_pattern.minecraft.vex": "Disseny d'armadura d'esperit", "trim_pattern.minecraft.ward": "Disseny d'armadura de guardià", "trim_pattern.minecraft.wayfinder": "Disseny d'armadura d'explorador", "trim_pattern.minecraft.wild": "Disseny d'armadura salvatge", "tutorial.bundleInsert.description": "Fes clic dret per a afegir objectes", "tutorial.bundleInsert.title": "U<PERSON><PERSON><PERSON> un saquet", "tutorial.craft_planks.description": "El llibre de receptes et pot ajudar", "tutorial.craft_planks.title": "Elabora taulons de fusta", "tutorial.find_tree.description": "Colpeja'l per obtindre fusta", "tutorial.find_tree.title": "Troba un arbre", "tutorial.look.description": "Fes servir el ratolí per girar", "tutorial.look.title": "Mira al teu voltant", "tutorial.move.description": "Prem %s per botar", "tutorial.move.title": "Mou-te amb %s, %s, %s i %s", "tutorial.open_inventory.description": "Prem %s", "tutorial.open_inventory.title": "Obri l'inventari", "tutorial.punch_tree.description": "Mantín %s premut", "tutorial.punch_tree.title": "Destruïx l'arbre", "tutorial.socialInteractions.description": "Prem %s per obrir", "tutorial.socialInteractions.title": "Interaccions socials", "upgrade.minecraft.netherite_upgrade": "<PERSON><PERSON> de netherita"}