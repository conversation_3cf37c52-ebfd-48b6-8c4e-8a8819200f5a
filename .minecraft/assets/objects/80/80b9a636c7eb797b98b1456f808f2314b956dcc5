{"accessibility.onboarding.accessibility.button": "Barrierefreiheit …", "accessibility.onboarding.screen.narrator": "<PERSON><PERSON> dee <PERSON>-Taste, um deee Sprachausgab anzummachen", "accessibility.onboarding.screen.title": "<PERSON><PERSON><PERSON> willkomme in Minecraft!\n\nWillste die Sprachausgab aktivier'n odder de Ärgleichkeitsstellunge bsuche?", "addServer.add": "<PERSON><PERSON><PERSON><PERSON>", "addServer.enterIp": "Sörvorradresse", "addServer.enterName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addServer.resourcePack": "Sörvorr-Ressourcenbagede", "addServer.resourcePack.disabled": "Deaggdivierd", "addServer.resourcePack.enabled": "Aggdivierd", "addServer.resourcePack.prompt": "Abfra<PERSON>", "addServer.title": "Sörvorrinformatschonen bearbeidn", "advMode.command": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode": "Modus", "advMode.mode.auto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "<PERSON><PERSON><PERSON>r aggdiv", "advMode.mode.conditional": "<PERSON><PERSON>d", "advMode.mode.redstone": "Imbuls", "advMode.mode.redstoneTriggered": "Benödischd Redstone", "advMode.mode.sequence": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.unconditional": "Unbedingd", "advMode.notAllowed": "Nur ee Oberadorr im Greadiv-Modus kann Befähle e<PERSON>ähm", "advMode.notEnabled": "Befählsblögge sin uff diesm Sörvorr nisch eingeschalded", "advMode.previousOutput": "Läddsde Ausgabe", "advMode.setCommand": "<PERSON><PERSON><PERSON><PERSON>", "advMode.setCommand.success": "Befähl gesäddsd: %s", "advMode.trackOutput": "Ausgabe vorrfolgn", "advMode.triggering": "Auslösn", "advMode.type": "<PERSON><PERSON>", "advancement.advancementNotFound": "Unbeganndorr Fordschridd ‚%s‘", "advancements.adventure.adventuring_time.description": "Enddegge alle Biome", "advancements.adventure.adventuring_time.title": "Abendeuorrzeid", "advancements.adventure.arbalistic.description": "Erlesche fünf undorrschiedlische Greadurn mim eem Armbrusdschuss", "advancements.adventure.arbalistic.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.avoid_vibration.description": "Schleische in dorr Nähe eenes Sculk-Sensors odorr Wärdorrs, um zu vorrhindorrn, dass de bemergd wirsd", "advancements.adventure.avoid_vibration.title": "Schleischn 100", "advancements.adventure.blowback.description": "Orlege eene B<PERSON>e mid eener abgewärdn Böen-Windgugl", "advancements.adventure.blowback.title": "Gegnwind", "advancements.adventure.brush_armadillo.description": "<PERSON>utze e<PERSON> Pinsel, um ein Hornschild vom Panzer eines Gürteltiers zu lösen", "advancements.adventure.brush_armadillo.title": "Ist das nicht putzig?", "advancements.adventure.bullseye.description": "Triff die Midde eenes Zielbloggs aus mindesdens 30 Blöggn Endfernung", "advancements.adventure.bullseye.title": "Volldräfforr!", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Stelle aus vier Töpferscherben einen verzierten Krug her", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Beerdsame Resterierung", "advancements.adventure.crafters_crafting_crafters.description": "Befind dich in dor Nähe eenes Wergorrs, wenn diesor eenen We<PERSON>rr herschdelld", "advancements.adventure.crafters_crafting_crafters.title": "Wergorr wergln Wergorr", "advancements.adventure.fall_from_world_height.description": "Schdürdse im frein Fall von dorr Weldoborrgränse (de maggs<PERSON><PERSON>) bis zur Weldundorrgränse un üborrlebe dies", "advancements.adventure.fall_from_world_height.title": "Höhln & Glibbn", "advancements.adventure.heart_transplanter.description": "Setze ein Knarzherz in der richtigen Ausrichtung zwischen zwei Blasseichenstämme", "advancements.adventure.heart_transplanter.title": "Herzverpflanzer", "advancements.adventure.hero_of_the_village.description": "Verteidsche een Dorf erfolgreisch vorr eem Üborrfall", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.honey_block_slide.description": "<PERSON><PERSON><PERSON>nge an een Honischblogg, um dein Schdurdds abzufangn", "advancements.adventure.honey_block_slide.title": "<PERSON><PERSON> klebri<PERSON>", "advancements.adventure.kill_a_mob.description": "Erlesche een Monsdorr", "advancements.adventure.kill_a_mob.title": "Monsdorrjägorr", "advancements.adventure.kill_all_mobs.description": "Erlesche jedes Monsdorr mindesdens een Mal", "advancements.adventure.kill_all_mobs.title": "Meisdorrjägorr", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "<PERSON><PERSON><PERSON> eene <PERSON> in dorr Nähe eenes Sculk-Gadalysadorrs", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Es vorrbreided sisch", "advancements.adventure.lighten_up.description": "<PERSON><PERSON>z eene <PERSON>ferleuch<PERSON> mid eener Axd ab, um se hellorr zu machn", "advancements.adventure.lighten_up.title": "Ufhelln", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Schüds<PERSON> een Dorfbewohnorr vorr eem unorwünsch<PERSON> Schogg, ohne dabei een Brand auszulösn", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Üborrschbannungsschudds", "advancements.adventure.minecraft_trials_edition.description": "Wage dich in eene Brüfungskammorr", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Brüfung(s)-Edition", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON><PERSON> eene <PERSON> ab", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON> hab de Gnarre g<PERSON>rd", "advancements.adventure.overoverkill.description": "Vorrursache mid dem Schdreidgolbn 50 Herzn Schadn in eenem eenzigen Hieborr", "advancements.adventure.overoverkill.title": "Großmeidorr des Gampfs", "advancements.adventure.play_jukebox_in_meadows.description": "Lass de Berschwiesn mid dem Glang dorr Musigg aus eem Bladdnschbielorr ufflehm", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON> dorr <PERSON>", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "<PERSON><PERSON> mit 'n <PERSON>mp<PERSON><PERSON> de Siggrastärke von 'ner geackerten Büchereggal aus", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Flimmern in jedem Bändel", "advancements.adventure.revaulting.description": "Schließe einen unheilvollen Tresor mit einem unheilvollen Prüfungsschlüssel auf", "advancements.adventure.revaulting.title": "Panzerknacker", "advancements.adventure.root.description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ng un Gambf", "advancements.adventure.root.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.salvage_sherd.description": "Hahnbaut n komischen Klotz a, um en Töpferbrocken zu durschtöbbern", "advancements.adventure.salvage_sherd.title": "Ofpassn vurm Olldn", "advancements.adventure.shoot_arrow.description": "Triff een Wesn mid eem Pfeil", "advancements.adventure.shoot_arrow.title": "Zielübungn", "advancements.adventure.sleep_in_bed.description": "Schlaf in eem Bädde, um dein Wiedorreinschdiegsbungd zu ändorrn", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON> was <PERSON><PERSON><PERSON>", "advancements.adventure.sniper_duel.description": "Erlesche een Schgeledd aus mindesdens 50 Blöggn Endfernung", "advancements.adventure.sniper_duel.title": "Scharfschüdsnduell", "advancements.adventure.spyglass_at_dragon.description": "Bedrachde den Enderdrachn dursch een Färnrohr", "advancements.adventure.spyglass_at_dragon.title": "Is es ä Fluchzeusch?", "advancements.adventure.spyglass_at_ghast.description": "Bedrachde een G<PERSON>t dursch een Färnrohr", "advancements.adventure.spyglass_at_ghast.title": "Is es <PERSON> Ballon?", "advancements.adventure.spyglass_at_parrot.description": "Bedrachde een Babagei dursch een Färnrohr", "advancements.adventure.spyglass_at_parrot.title": "Is es ä Voochl?", "advancements.adventure.summon_iron_golem.description": "<PERSON>ue een <PERSON>, um bei dorr Verteidschung eenes Do<PERSON>s midzuhelfn", "advancements.adventure.summon_iron_golem.title": "Das 1. <PERSON><PERSON><PERSON><PERSON> do<PERSON>", "advancements.adventure.throw_trident.description": "Wirf dein Dreizagg uff irschendwas.\nHinwees: <PERSON><PERSON> is keene gude <PERSON>, deine einzsche Waffe wegzuwerfn.", "advancements.adventure.throw_trident.title": "<PERSON>en <PERSON> zum Wegwerfn", "advancements.adventure.totem_of_undying.description": "<PERSON><PERSON><PERSON> een Dodem dorr Unschderblischkeed, um dem Dod von dorr Schibbe zu schbringn", "advancements.adventure.totem_of_undying.title": "Posd mordem", "advancements.adventure.trade.description": "Schließe een Handel mid eem Dorfbewohnorr ab", "advancements.adventure.trade.title": "Was ä Geschäfd!", "advancements.adventure.trade_at_world_height.description": "Handle mid eem Dorfbewohnorr uff dorr maggsimaln <PERSON>", "advancements.adventure.trade_at_world_height.title": "Schdernenhändlorr", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "<PERSON>d jede von den<PERSON> Schmiedevorlaagn mindestns eemol an: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Gezeitn un Wegfinder", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Schmießen mit Stil", "advancements.adventure.trim_with_any_armor_pattern.description": "Versieh en Rüstungsdeel an en Schmieeddiesch mit en Rüstungsbesatz", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON> ne<PERSON> Au<PERSON>hn", "advancements.adventure.two_birds_one_arrow.description": "Erlesche zwee Phandome mid eem <PERSON>", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON>e uff eenen <PERSON>", "advancements.adventure.under_lock_and_key.description": "<PERSON><PERSON><PERSON> eenen Dresorr mid eenem Brüfungsschlüssl", "advancements.adventure.under_lock_and_key.title": "<PERSON>nner Schloss un Riegl", "advancements.adventure.use_lodestone.description": "<PERSON><PERSON><PERSON> einen Kompass an einem Leitstein", "advancements.adventure.use_lodestone.title": "Weißt du, wo die Leitstein<PERSON> stehen?", "advancements.adventure.very_very_frightening.description": "Driff een <PERSON>orr mid eem Blidds", "advancements.adventure.very_very_frightening.title": "Angsd un Schreggn", "advancements.adventure.voluntary_exile.description": "Döde een Räuborhaubdmann.\nZiehe möglischorrweese in Bedrachd, disch vore<PERSON><PERSON> von <PERSON>rf<PERSON> fernzuhaldn …", "advancements.adventure.voluntary_exile.title": "Freiwillisches Eggsil", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Gehe uff <PERSON> … ohne darin zu vor<PERSON>n", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Le<PERSON>d wie een <PERSON>l", "advancements.adventure.who_needs_rockets.description": "Benutz eene Windkugl, um dich 7 Blögge in de Höhe zu schleudorrn", "advancements.adventure.who_needs_rockets.title": "Wer brooch schon <PERSON>?", "advancements.adventure.whos_the_pillager_now.description": "<PERSON><PERSON> e<PERSON><PERSON> Plündororr ne Gostbroobe seinorr eischnen Medizin", "advancements.adventure.whos_the_pillager_now.title": "Wer isn j<PERSON>d dorr Plündororr?", "advancements.empty": "Hier scheind es nüschd zu g<PERSON>hm …", "advancements.end.dragon_breath.description": "Sammle Drachnadem in eenorr Glasbulle", "advancements.end.dragon_breath.title": "Du brauchsd ä Pfeffi", "advancements.end.dragon_egg.description": "<PERSON><PERSON> das Drachnei", "advancements.end.dragon_egg.title": "De näschsde Generatschoon", "advancements.end.elytra.description": "<PERSON><PERSON>", "advancements.end.elytra.title": "Hindorrm Horizond gehd’s weidorr", "advancements.end.enter_end_gateway.description": "Endgomme dorr Insel", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON>", "advancements.end.find_end_city.description": "Geh schon nei, was kannde bassiern?", "advancements.end.find_end_city.title": "De Schdadt am Ende des Schbiels", "advancements.end.kill_dragon.description": "<PERSON><PERSON>", "advancements.end.kill_dragon.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.end.levitate.description": "Schwebe dursch een Shulker-Geschoss 50 Blögge uffwärds", "advancements.end.levitate.title": "<PERSON><PERSON><PERSON><PERSON> von hier obn", "advancements.end.respawn_dragon.description": "Rufe den Enderdrachn ins Lebn zurügg", "advancements.end.respawn_dragon.title": "<PERSON> … <PERSON><PERSON> wied<PERSON> …", "advancements.end.root.description": "… odorr dorr <PERSON>?", "advancements.end.root.title": "<PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Lass en Hilfsgeisd een Guchn bei eem Nodnblogg ableschn", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Geburdsdaachslied", "advancements.husbandry.allay_deliver_item_to_player.description": "Lass dir en Geschnschdand von eem Hilfsgeisd lieforrn", "advancements.husbandry.allay_deliver_item_to_player.title": "Du hasd ’n Freund in mir", "advancements.husbandry.axolotl_in_a_bucket.description": "<PERSON> een <PERSON>olodl mid eem <PERSON>r", "advancements.husbandry.axolotl_in_a_bucket.title": "Das niedlischsde Raubdier", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON> all<PERSON>, selbsd wenn es nisch gud für disch is", "advancements.husbandry.balanced_diet.title": "Ausgewochne Ernährung", "advancements.husbandry.breed_all_animals.description": "Vormähre alle Dierardn!", "advancements.husbandry.breed_all_animals.title": "Baarweese", "advancements.husbandry.breed_an_animal.description": "Vormähre zwee Diere", "advancements.husbandry.breed_an_animal.title": "De Hühnschn un de Blümschn", "advancements.husbandry.complete_catalogue.description": "<PERSON><PERSON><PERSON>e alle Gadsnardn!", "advancements.husbandry.complete_catalogue.title": "<PERSON><PERSON> go<PERSON><PERSON><PERSON>", "advancements.husbandry.feed_snifflet.description": "Fütter en Schnüfflerkinner", "advancements.husbandry.feed_snifflet.title": "Schnupperkurs", "advancements.husbandry.fishy_business.description": "<PERSON> een <PERSON>", "advancements.husbandry.fishy_business.title": "<PERSON>rr<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.froglights.description": "Habe alle Fruschlichdorr im Invendar", "advancements.husbandry.froglights.title": "Mid voreendn Gräfdn!", "advancements.husbandry.kill_axolotl_target.description": "Vorrbünde disch mid eem Aggsolodl un gewinn een Gambf", "advancements.husbandry.kill_axolotl_target.title": "De heilende Grafd dorr Freundschafd!", "advancements.husbandry.leash_all_frog_variants.description": "<PERSON>be jede <PERSON> an eenorr <PERSON>", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON><PERSON> de Gerle um de Häusorr z<PERSON>hn", "advancements.husbandry.make_a_sign_glow.description": "Lassn de Text von nem beliebign Schield leuchtn", "advancements.husbandry.make_a_sign_glow.title": "<PERSON><PERSON><PERSON>de un sch<PERSON>une!", "advancements.husbandry.netherite_hoe.description": "<PERSON><PERSON><PERSON> een Nädderidbarrn, um eene Hagge uffzuwärdn un denge dann üborr deine Lebensendscheidungn nach", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON> g<PERSON><PERSON>", "advancements.husbandry.obtain_sniffer_egg.description": "Beschaff en Schnüfflerei", "advancements.husbandry.obtain_sniffer_egg.title": "En ungewöhnlicher Geruch", "advancements.husbandry.place_dried_ghast_in_water.description": "Setze einen ausgetrockneten Ghast in Wasser", "advancements.husbandry.place_dried_ghast_in_water.title": "<PERSON><PERSON><PERSON> hydriert!", "advancements.husbandry.plant_any_sniffer_seed.description": "<PERSON><PERSON><PERSON> von nem Schnüffler ausgegrabne Samn", "advancements.husbandry.plant_any_sniffer_seed.title": "<PERSON> <PERSON><PERSON><PERSON>", "advancements.husbandry.plant_seed.description": "<PERSON><PERSON><PERSON>n un sieh ihn beim <PERSON>n zu", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "<PERSON><PERSON><PERSON> mit einer Schere den Wolfspanzer von e<PERSON>m Wolf", "advancements.husbandry.remove_wolf_armor.title": "<PERSON><PERSON><PERSON><PERSON>‐schnapp!", "advancements.husbandry.repair_wolf_armor.description": "Repariere einen beschädigten Wolfspanzer mit Gürteltier‐Hornschilden vollständig", "advancements.husbandry.repair_wolf_armor.title": "So gut wie neu", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Schdeische in ä Bood un mach mid norr Ziesche de Fliesche", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Ohne G<PERSON>ß kee Breis!", "advancements.husbandry.root.description": "De Weld is vollorr Freunde un Essn", "advancements.husbandry.root.title": "Landwürdschafd", "advancements.husbandry.safely_harvest_honey.description": "<PERSON><PERSON> en Lagerfeuer, um mit en Glasflasche Honig von nem Bienenstock zu sammln, ohne de Bienen ze ärgern", "advancements.husbandry.safely_harvest_honey.title": "Sssei unsorr Gasd", "advancements.husbandry.silk_touch_nest.description": "Beweesche een von drei Bien bewohndes Biennesd mid Behudsamkeed", "advancements.husbandry.silk_touch_nest.title": "Bienwandorung", "advancements.husbandry.tactical_fishing.description": "Fang een <PERSON>sch … ohne Angel!", "advancements.husbandry.tactical_fishing.title": "Dagdisches Fischn", "advancements.husbandry.tadpole_in_a_bucket.description": "<PERSON> <PERSON> in eem <PERSON>", "advancements.husbandry.tadpole_in_a_bucket.title": "<PERSON><PERSON>", "advancements.husbandry.tame_an_animal.description": "Zähme ä Dier", "advancements.husbandry.tame_an_animal.title": "Allorbesde Freunde", "advancements.husbandry.wax_off.description": "<PERSON><PERSON><PERSON> Waggs von eem Gupforrblogg ab!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.wax_on.description": "Waggse een Gupforrblogg mid eenorr Honischwabe!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.whole_pack.description": "Zähme alle Wolfsarten", "advancements.husbandry.whole_pack.title": "Das ganze <PERSON>", "advancements.nether.all_effects.description": "Habe jedn Schdaduseffegd gleischzeidsch", "advancements.nether.all_effects.title": "Wie haben wir das geschaffd?", "advancements.nether.all_potions.description": "<PERSON>be jedn Drangeffegd gleischzeidsch", "advancements.nether.all_potions.title": "Eene gefährlische Mischung", "advancements.nether.brew_potion.description": "<PERSON><PERSON><PERSON> e<PERSON>", "advancements.nether.brew_potion.title": "Alchemie", "advancements.nether.charge_respawn_anchor.description": "Lade een Seelnangorr uff die höchsde Schdufe uff", "advancements.nether.charge_respawn_anchor.title": "<PERSON><PERSON> ganz „siebn“ Lebn", "advancements.nether.create_beacon.description": "Erschdelle un bladdsiere een Le<PERSON>chdfeuorr", "advancements.nether.create_beacon.title": "Den Nachbar<PERSON> he<PERSON>dn", "advancements.nether.create_full_beacon.description": "Bring een <PERSON><PERSON><PERSON> uff volle Schdärge", "advancements.nether.create_full_beacon.title": "Le<PERSON>chddurmwärdorr", "advancements.nether.distract_piglin.description": "<PERSON><PERSON> mid Gold ab", "advancements.nether.distract_piglin.title": "Oh, wie glänz’sch", "advancements.nether.explore_nether.description": "Enddegge alle Netherbiome", "advancements.nether.explore_nether.title": "Heeßbegährde Reiseziele", "advancements.nether.fast_travel.description": "<PERSON><PERSON><PERSON> Nether, um 7 km in dorr Oborrweld zu reisn", "advancements.nether.fast_travel.title": "Subraumdransbord", "advancements.nether.find_bastion.description": "<PERSON>ridd e<PERSON>", "advancements.nether.find_bastion.title": "Das warn noch Zeidn …", "advancements.nether.find_fortress.description": "<PERSON><PERSON><PERSON> in eene Netherfesdung ein", "advancements.nether.find_fortress.title": "<PERSON>ene schregglische Fesdung", "advancements.nether.get_wither_skull.description": "Erbeude een With<PERSON>chgeleddnischl", "advancements.nether.get_wither_skull.title": "<PERSON><PERSON> gra<PERSON>ches Schgeledd", "advancements.nether.loot_bastion.description": "Blü<PERSON><PERSON> eene <PERSON> in eenorr Basdionsruine", "advancements.nether.loot_bastion.title": "Gambfschweene", "advancements.nether.netherite_armor.description": "Besorsche dir eene gombledde Nädderidrüsdung", "advancements.nether.netherite_armor.title": "Bedegg mich mid Schrodd", "advancements.nether.obtain_ancient_debris.description": "Beschaffe andign Schrodd", "advancements.nether.obtain_ancient_debris.title": "Vorborschn in den Diefn", "advancements.nether.obtain_blaze_rod.description": "Erleischdore eene Lohä um ihre Rude", "advancements.nether.obtain_blaze_rod.title": "Schbiel mid dem Feuorr", "advancements.nether.obtain_crying_obsidian.description": "Beschaffe ningelndn Obsidian", "advancements.nether.obtain_crying_obsidian.title": "Wer schneided hier <PERSON>?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON><PERSON> eenen <PERSON> mid sein eischenen Waffn", "advancements.nether.return_to_sender.title": "<PERSON><PERSON><PERSON><PERSON> zum Absendor", "advancements.nether.ride_strider.description": "<PERSON><PERSON> eenen <PERSON>eidorr mid eener Würrbilzrude", "advancements.nether.ride_strider.title": "<PERSON><PERSON> diesm „<PERSON><PERSON><PERSON>", "advancements.nether.ride_strider_in_overworld_lava.description": "Undorrnimm mid eem Schreidorr een laaaangn Ridd uff eem <PERSON> in dorr Oborrweld", "advancements.nether.ride_strider_in_overworld_lava.title": "Ä Gefühl dorr Heimad", "advancements.nether.root.description": "<PERSON><PERSON><PERSON> dir was <PERSON><PERSON><PERSON> an", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "Erschaffe den Wither", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "<PERSON>f<PERSON>hre eene<PERSON> aus’m <PERSON>, bring ihn sischorr in de Oborrweld … und erledsche ihn dann", "advancements.nether.uneasy_alliance.title": "Unheilvolle <PERSON>", "advancements.nether.use_lodestone.description": "<PERSON><PERSON><PERSON> een Gombass an eem <PERSON>en", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON>, wo de <PERSON>’ schdehn?", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Schwäsche een Zombiedorfbewohnorr un heile ihn dann", "advancements.story.cure_zombie_villager.title": "Zombieardsd", "advancements.story.deflect_arrow.description": "<PERSON>änge ee Geschoss mid eem Schüld ab", "advancements.story.deflect_arrow.title": "<PERSON><PERSON> nisch, dange", "advancements.story.enchant_item.description": "V<PERSON><PERSON>ub<PERSON> een Geschnschdand an eem Zaubordisch", "advancements.story.enchant_item.title": "Vorzauboror", "advancements.story.enter_the_end.description": "<PERSON><PERSON><PERSON> Endbordal", "advancements.story.enter_the_end.title": "<PERSON> Ende?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON>, endzünde un bedridd een Netherbordal", "advancements.story.enter_the_nether.title": "Wir müssn noch dieforr", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON>ge eem <PERSON>", "advancements.story.follow_ender_eye.title": "Das flieschnde Ooche", "advancements.story.form_obsidian.description": "Beschaffe een Obsidianblogg", "advancements.story.form_obsidian.title": "Ice Bucket Challenge", "advancements.story.iron_tools.description": "Vorbessre deine <PERSON>e", "advancements.story.iron_tools.title": "<PERSON><PERSON> viel Eisn im Blud", "advancements.story.lava_bucket.description": "<PERSON><PERSON><PERSON> een <PERSON>", "advancements.story.lava_bucket.title": "Mansche möschn’s heiß", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.title": "Diamandn!", "advancements.story.mine_stone.description": "<PERSON><PERSON> Sc<PERSON>deen mit deinorr neun <PERSON> ab", "advancements.story.mine_stone.title": "Schdeenzeid", "advancements.story.obtain_armor.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> disch mid eem Rüsdungsdeil aus Eisn", "advancements.story.obtain_armor.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.root.description": "Dorr Gern un de Geschischde des Schbiels", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Diamandrüsdung redded Läbn", "advancements.story.shiny_gear.title": "<PERSON><PERSON><PERSON> misch mid <PERSON>", "advancements.story.smelt_iron.description": "Schmilz E<PERSON>nä<PERSON> im Ofn zu eem Eisnbarrn", "advancements.story.smelt_iron.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.upgrade_tools.description": "<PERSON><PERSON><PERSON><PERSON> <PERSON>ne bessre <PERSON><PERSON><PERSON><PERSON><PERSON>gge her", "advancements.story.upgrade_tools.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.toast.challenge": "Uffgabe erledischd!", "advancements.toast.goal": "<PERSON><PERSON> er<PERSON>d!", "advancements.toast.task": "Fordschridd erzield!", "argument.anchor.invalid": "Ungüldsche Objegdangorbositschoon ‚%s‘", "argument.angle.incomplete": "Unvollschdändsch (1 Bliggwingl erwarded)", "argument.angle.invalid": "Ungüldschor<PERSON>", "argument.block.id.invalid": "Unbeganndorr Blogg ‚%s‘", "argument.block.property.duplicate": "Zuschdand ‚%s‘ kann nur eenmal für den Blogg %s gesäddsd wärdn", "argument.block.property.invalid": "Blogg %s aggzebdierd ‚%s‘ für Bloggzuschdand ‚%s‘ nisch", "argument.block.property.novalue": "Fählendorr Werd für Zuschdand ‚%s‘ des Bloggs %s", "argument.block.property.unclosed": "Schließende Glammorr ] für Bloggzuschdand erwarded", "argument.block.property.unknown": "Blogg %s besiddsd den Zuschdand ‚%s‘ nisch", "argument.block.tag.disallowed": "<PERSON><PERSON> sin hier nisch erlo<PERSON>, nur dadsächlische Blögge", "argument.color.invalid": "Unbegannde Farbe ‚%s‘", "argument.component.invalid": "Ungüldsche Deggsdgombonende ‚%s‘", "argument.criteria.invalid": "Unbeganndes Griderium ‚%s‘", "argument.dimension.invalid": "Unbegannde Dimension ‚%s‘", "argument.double.big": "Gommazahl darf nisch größorr als %s sein, %s is zu groß", "argument.double.low": "Go<PERSON><PERSON>hl darf nisch gleenorr als %s sein, %s is zu gleen", "argument.entity.invalid": "Ungüldschorr Name odorr ungüldsche UUID", "argument.entity.notfound.entity": "<PERSON><PERSON> wurde keen Objegd gefundn", "argument.entity.notfound.player": "<PERSON>s wurde keen <PERSON>bielorr gefundn", "argument.entity.options.advancements.description": "Schbielorr mid Fordschriddn", "argument.entity.options.distance.description": "Endfernung zum Objegd", "argument.entity.options.distance.negative": "Endfernung darf nisch negadiv sein", "argument.entity.options.dx.description": "Objegde zwischn X un X + dX", "argument.entity.options.dy.description": "Objegde zwischn Y un Y + dY", "argument.entity.options.dz.description": "Objegde zwischn Z un Z + dZ", "argument.entity.options.gamemode.description": "Schbielorr mid Schbielmodus", "argument.entity.options.inapplicable": "Fildorr ‚%s‘ is hier nisch anwendbar", "argument.entity.options.level.description": "Erfahrungsschdufe", "argument.entity.options.level.negative": "Erfahrungsschdufe darf nisch negadiv sein", "argument.entity.options.limit.description": "<PERSON><PERSON><PERSON><PERSON>üggzulieforndorr Objegde", "argument.entity.options.limit.toosmall": "Limid muss mindesdens 1 bedragn", "argument.entity.options.mode.invalid": "Ungüldschorr odorr unbeganndorr Schbielmodus ‚%s‘", "argument.entity.options.name.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.nbt.description": "Objegde mid NBT", "argument.entity.options.predicate.description": "Benuddsorrdefinierdes Prädigad", "argument.entity.options.scores.description": "Objegde mid Bungdeschdändn", "argument.entity.options.sort.description": "<PERSON><PERSON>jeg<PERSON> sordi<PERSON>", "argument.entity.options.sort.irreversible": "Ungüldsche odorr unbegannde Sordierung ‚%s‘", "argument.entity.options.tag.description": "Objegde mid Edigedd", "argument.entity.options.team.description": "Objegde im Team", "argument.entity.options.type.description": "Objegde des Dyps", "argument.entity.options.type.invalid": "Ungüldschorr odorr unbeganndorr Objegddyp ‚%s‘", "argument.entity.options.unknown": "Unbeganndorr Fildorr ‚%s‘", "argument.entity.options.unterminated": "Schließende Glammorr ] für Fildorr erwarded", "argument.entity.options.valueless": "Werd für Fildorr ‚%s‘ erwarded", "argument.entity.options.x.description": "X-Bositschoon", "argument.entity.options.x_rotation.description": "Gobbneigung des Objegds", "argument.entity.options.y.description": "Y-Bositschoon", "argument.entity.options.y_rotation.description": "Bliggrischdung des Objegds", "argument.entity.options.z.description": "Z-Bositschoon", "argument.entity.selector.allEntities": "Alle Objegde", "argument.entity.selector.allPlayers": "Alle Schbielorr", "argument.entity.selector.missing": "Fählendorr Selegdor", "argument.entity.selector.nearestEntity": "Nächstes Objekt", "argument.entity.selector.nearestPlayer": "Näschsdorr Schbielorr", "argument.entity.selector.not_allowed": "Seleg<PERSON>r nisch erlo<PERSON>d", "argument.entity.selector.randomPlayer": "Zufällischorr Schbielorr", "argument.entity.selector.self": "Aggduelles Objegd", "argument.entity.selector.unknown": "Unbeganndorr Selegdorr ‚%s‘", "argument.entity.toomany": "Nur een <PERSON>g<PERSON> is erlo<PERSON>d, aborr dorr angegäbne Selegdorr könnde mährore lieforn", "argument.enum.invalid": "Ungüldschorr Werd „%s“", "argument.float.big": "Gommazahl darf nisch größorr als %s sein, %s is zu groß", "argument.float.low": "Go<PERSON><PERSON>hl darf nisch gleenorr als %s sein, %s is zu gleen", "argument.gamemode.invalid": "Unbeganndorr Schbielmodus ‚%s‘", "argument.hexcolor.invalid": "Ungültiger Hex‐Farbcode ‚%s‘", "argument.id.invalid": "Ungüldsche ID", "argument.id.unknown": "Unbegannde ID ‚%s‘", "argument.integer.big": "Gan<PERSON><PERSON> darf nisch größorr als %s sein, %s is zu groß", "argument.integer.low": "<PERSON><PERSON><PERSON><PERSON> darf nisch gleenorr als %s sein, %s is zu gleen", "argument.item.id.invalid": "Unbeganndorr Geschnschdand ‚%s‘", "argument.item.tag.disallowed": "<PERSON><PERSON> sin hier nisch erlo<PERSON>d, nur dadsächlische Geschnschdände", "argument.literal.incorrect": "Zeischnfolge ‚%s‘ erwarded", "argument.long.big": "<PERSON> Ganzzahl darf nisch größorr als %s sein, %s is zu groß", "argument.long.low": "<PERSON> darf nisch gleenorr als %s sein, %s is zu gleen", "argument.message.too_long": "Chatnachricht war zu lang (%s > höchstens %s Zeichen)", "argument.nbt.array.invalid": "Ungüldschorr Array-Typ ‚%s‘", "argument.nbt.array.mixed": "%s kann nisch in %s eingefüschd wärdn", "argument.nbt.expected.compound": "Verbunddaten erwartet", "argument.nbt.expected.key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> erwarded", "argument.nbt.expected.value": "Werd erwarded", "argument.nbt.list.mixed": "%s kann nisch in Lisde von %s eingefüschd wärdn", "argument.nbt.trailing": "Unerwardede nachfolgende Dadn", "argument.player.entities": "Diesorr Befähl kann nur uff Schbielorr angewended wärdn, dorr angegäbne Selegdorr schließd aborr ooch Objegde mid nei", "argument.player.toomany": "Nur ee Schbielorr is erloobd, aborr dorr angegäbne Selegdorr könnde mehrore lieforn", "argument.player.unknown": "Diesorr Schbielorr eggsischdierd nisch", "argument.pos.missing.double": "Goordina<PERSON> erwarded", "argument.pos.missing.int": "Bloggbositschoon erwarded", "argument.pos.mixed": "Logale un globale Goordinadn dürfn nisch gemischd wärdn (endwedorr alles mid ^ odorr ohne)", "argument.pos.outofbounds": "Diese Bositschoon lieschd außorrhalb des erloobdn Bereischs.", "argument.pos.outofworld": "<PERSON><PERSON> befinded sisch außorhalb dorr Weld", "argument.pos.unloaded": "<PERSON><PERSON> is nisch geladn", "argument.pos2d.incomplete": "Unvullschdändsch (2 Goordinadn erwarded)", "argument.pos3d.incomplete": "Unvollschdändsch (3 Goordinadn erwarded)", "argument.range.empty": "Werd odorr Werdebereisch erwarded", "argument.range.ints": "Nur Ganzzahln ohne Nachkommaschdelln erloobd", "argument.range.swapped": "Minimalwerd darf nisch größorr als dorr Maggsimalwerd sein", "argument.resource.invalid_type": "Element '%s' hat en falschn Typ '%s' ('%s' erwartet)", "argument.resource.not_found": "Element '%s' vom Typ '%s' nich gefunden", "argument.resource_or_id.failed_to_parse": "Gonsdrugtschoon gonnde nich analysiorrd wärdn: %s", "argument.resource_or_id.invalid": "Ungüldige ID odorr ungüldiges Edigedd", "argument.resource_or_id.no_such_element": "Element ‚%s‘ in Register ‚%s‘ nicht gefunden", "argument.resource_selector.not_found": "<PERSON>ine Übereinstimmungen für Selektor ‚%s‘ vom Typ ‚%s‘", "argument.resource_tag.invalid_type": "Etikett '%s' hat en falschn Typ '%s' ('%s' erwartet)", "argument.resource_tag.not_found": "Etikett '%s' vom Typ '%s' nich gefunden", "argument.rotation.incomplete": "Unvollschdändsch (2 Rodationsangabn erwarded)", "argument.scoreHolder.empty": "<PERSON>s wurdn keene relevandn Bungdehaldorr gefundn", "argument.scoreboardDisplaySlot.invalid": "Unbegannde Anzeischebositschoon ‚%s‘", "argument.style.invalid": "Ungüldige Schrifdufzeichnung: %s", "argument.time.invalid_tick_count": "<PERSON><PERSON><PERSON> dorr <PERSON>iggs darf nisch negadiv sein", "argument.time.invalid_unit": "Ungüldsche Eenheed", "argument.time.tick_count_too_low": "<PERSON><PERSON><PERSON> <PERSON> de Ticks darf nich klener als %s sein, %s gefunden", "argument.uuid.invalid": "Ungüldsche UUID", "argument.waypoint.invalid": "Das ausgewählte Objekt ist kein Wegpunkt", "arguments.block.tag.unknown": "Unbeganndorr Bloggalias ‚%s‘", "arguments.function.tag.unknown": "Unbeganndorr Fungtschoonsalias ‚%s‘", "arguments.function.unknown": "Unbegannde Fungtschoon ‚%s‘", "arguments.item.component.expected": "Gegnschdandsgombonende erworrded", "arguments.item.component.malformed": "Fehlorrhafde ‚%s‘‐Gombonende: ‚%s‘", "arguments.item.component.repeated": "Gegnschdandsgombonende ‚%s‘ worrde wied<PERSON><PERSON><PERSON>, es darf jedoch nur een Werd angegebn wärdn", "arguments.item.component.unknown": "Unbekannte Gegenstandskomponente ‚%s‘", "arguments.item.malformed": "Fehlerhafter Gegenstand: ‚%s‘", "arguments.item.overstacked": "Maggsimale <PERSON>hdablgreeße von %s is %s", "arguments.item.predicate.malformed": "Fehlerhaftes ‚%s‘‐Prädikat: ‚%s‘", "arguments.item.predicate.unknown": "Unbekanntes Gegenstandsprädikat ‚%s‘", "arguments.item.tag.unknown": "Unbeganndorr Geschnschdandsalias ‚%s‘", "arguments.nbtpath.node.invalid": "Ungüldsches Elemend im NBT-Pfad", "arguments.nbtpath.nothing_found": "<PERSON>s wurden keene Elemende gefundn, die %s entsprechn", "arguments.nbtpath.too_deep": "Resultiern NBT zu tief verschachtelt", "arguments.nbtpath.too_large": "Resultiern NBT zu grooß", "arguments.objective.notFound": "Unbeganndes Ziel ‚%s‘", "arguments.objective.readonly": "Das Ziel %s kann nur ausgelesn wärdn", "arguments.operation.div0": "<PERSON><PERSON> kann ni gedeild wärn", "arguments.operation.invalid": "Ungüldsche Oberation", "arguments.swizzle.invalid": "Ungüldsche Achsngombinadschoon von X, <PERSON> <PERSON>", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s %% %s", "attribute.modifier.equals.2": "%s %% %s", "attribute.modifier.plus.0": "+ %s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s %% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s %% %s", "attribute.name.armor": "Rüstung", "attribute.name.armor_toughness": "Rüstungshärte", "attribute.name.attack_damage": "Angriffsschaden", "attribute.name.attack_knockback": "Angriffs<PERSON><PERSON><PERSON><PERSON>", "attribute.name.attack_speed": "Angriffsgeschwindigkeit", "attribute.name.block_break_speed": "Blockabbaugeschwindigkeit", "attribute.name.block_interaction_range": "Block‐Interaktionsweite", "attribute.name.burning_time": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.camera_distance": "Kameraabstand", "attribute.name.entity_interaction_range": "Objekt‐Interaktionsweite", "attribute.name.explosion_knockback_resistance": "Explosionsstandfestigkeit", "attribute.name.fall_damage_multiplier": "Fallschaden‐Multiplikator", "attribute.name.flying_speed": "Fluggeschwindigkeit", "attribute.name.follow_range": "Kreaturen‐Verfolgedistanz", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Rüsdungshärde", "attribute.name.generic.attack_damage": "Angriffsschadn", "attribute.name.generic.attack_knockback": "Angriffs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_speed": "Angriffsgeschwindischkeed", "attribute.name.generic.block_interaction_range": "Block‐Interaktionsweite", "attribute.name.generic.burning_time": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.entity_interaction_range": "Objekt‐Interaktionsweite", "attribute.name.generic.explosion_knockback_resistance": "Explosionsstandfestigkeit", "attribute.name.generic.fall_damage_multiplier": "Fallschaden‐Multiplikator", "attribute.name.generic.flying_speed": "Fluggeschwindischkeed", "attribute.name.generic.follow_range": "Greadurn-Folgedisdanz", "attribute.name.generic.gravity": "Schwerkraft", "attribute.name.generic.jump_strength": "Sprungstärke", "attribute.name.generic.knockback_resistance": "Schdandfesdischkeed", "attribute.name.generic.luck": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.max_absorption": "Maximale Absorption", "attribute.name.generic.max_health": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.movement_efficiency": "Bewegungseffizienz", "attribute.name.generic.movement_speed": "Geschwindischkeed", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON><PERSON>", "attribute.name.generic.scale": "Größe", "attribute.name.generic.step_height": "Schritthöhe", "attribute.name.generic.water_movement_efficiency": "Wasser‐Bewegungseffizienz", "attribute.name.gravity": "Schwerkraft", "attribute.name.horse.jump_strength": "Färdeschbrungschdärge", "attribute.name.jump_strength": "Sprungstärke", "attribute.name.knockback_resistance": "Standfestigkeit", "attribute.name.luck": "Glück", "attribute.name.max_absorption": "Maximale Absorption", "attribute.name.max_health": "Maximale Gesundheit", "attribute.name.mining_efficiency": "A<PERSON>ueff<PERSON><PERSON>z", "attribute.name.movement_efficiency": "Bewegungseffizienz", "attribute.name.movement_speed": "Geschwindigkeit", "attribute.name.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.block_break_speed": "Blockabbaugeschwindigkeit", "attribute.name.player.block_interaction_range": "Block‐Interaktionsweite", "attribute.name.player.entity_interaction_range": "Objekt‐Interaktionsweite", "attribute.name.player.mining_efficiency": "A<PERSON>ueff<PERSON><PERSON>z", "attribute.name.player.sneaking_speed": "Schleichgeschwindigkeit", "attribute.name.player.submerged_mining_speed": "Unterwasser‐Abbaugeschwindigkeit", "attribute.name.player.sweeping_damage_ratio": "Schwungschadensverhältnis", "attribute.name.safe_fall_distance": "<PERSON><PERSON><PERSON>", "attribute.name.scale": "Größe", "attribute.name.sneaking_speed": "Schleichgeschwindigkeit", "attribute.name.spawn_reinforcements": "Zombie‐Verstärkung", "attribute.name.step_height": "Schritthöhe", "attribute.name.submerged_mining_speed": "Unterwasser‐Abbaugeschwindigkeit", "attribute.name.sweeping_damage_ratio": "Schwungschadensverhältnis", "attribute.name.tempt_range": "Kreaturen‐Anlockdistanz", "attribute.name.water_movement_efficiency": "Wasser‐Bewegungseffizienz", "attribute.name.waypoint_receive_range": "Wegpunkt‐Empfangsreichweite", "attribute.name.waypoint_transmit_range": "Wegpunkt‐Sendereichweite", "attribute.name.zombie.spawn_reinforcements": "Zombie-Vorschdärgung", "biome.minecraft.badlands": "Daflbersche", "biome.minecraft.bamboo_jungle": "Bambusdschungl", "biome.minecraft.basalt_deltas": "Basalddeldas", "biome.minecraft.beach": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.birch_forest": "Bürschnwald", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cold_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.crimson_forest": "Karmesinwald", "biome.minecraft.dark_forest": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON>", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "Vorreisde <PERSON>ee", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.desert": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "Drobbschdeenhöhln", "biome.minecraft.end_barrens": "End-Kargland", "biome.minecraft.end_highlands": "End-Hochland", "biome.minecraft.end_midlands": "End-Middlland", "biome.minecraft.eroded_badlands": "Abgedragne Daflbersche", "biome.minecraft.flower_forest": "Blumenwald", "biome.minecraft.forest": "<PERSON><PERSON>", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "Vorreisde Gibfl", "biome.minecraft.frozen_river": "Voreisdorr F<PERSON>", "biome.minecraft.grove": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.ice_spikes": "Eiszabfntundra", "biome.minecraft.jagged_peaks": "Zorrklüffde Gibfl", "biome.minecraft.jungle": "Dschungl", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.lush_caves": "Übbsche Höhln", "biome.minecraft.mangrove_swamp": "Mangrovnsumbf", "biome.minecraft.meadow": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.mushroom_fields": "Bilzland", "biome.minecraft.nether_wastes": "Nether-Ödland", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "Bürschn-Urwald", "biome.minecraft.old_growth_pine_taiga": "Gieforrn-Urtaiga", "biome.minecraft.old_growth_spruce_taiga": "Fischdn-Urtaiga", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON>", "biome.minecraft.plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.river": "Fluss", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Savannenhochebene", "biome.minecraft.small_end_islands": "<PERSON><PERSON><PERSON>", "biome.minecraft.snowy_beach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.snowy_plains": "Vorrschneide Ebene", "biome.minecraft.snowy_slopes": "Vorrschneide Hänge", "biome.minecraft.snowy_taiga": "Vorrschneide Taiga", "biome.minecraft.soul_sand_valley": "Seelnsanddaal", "biome.minecraft.sparse_jungle": "Lischdorr Dschungl", "biome.minecraft.stony_peaks": "Schdeinsche Gibfl", "biome.minecraft.stony_shore": "Schdeinsche Güsde", "biome.minecraft.sunflower_plains": "Sonnenblumebene", "biome.minecraft.swamp": "Sumbf", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "<PERSON>", "biome.minecraft.the_void": "Das Nüschd", "biome.minecraft.warm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_forest": "Zorrzausdorr Wald", "biome.minecraft.windswept_gravelly_hills": "Zorrzausde Geröllhüschl", "biome.minecraft.windswept_hills": "Zorrzausde Hüschl", "biome.minecraft.windswept_savanna": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.wooded_badlands": "Bewaldede Daflbersche", "block.minecraft.acacia_button": "Agazienhulzgnopp", "block.minecraft.acacia_door": "Agazienhulzdiere", "block.minecraft.acacia_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_fence_gate": "Agazienhulzzaundorr", "block.minecraft.acacia_hanging_sign": "Agazienhulzhängeschüld", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_log": "Agazienschdamm", "block.minecraft.acacia_planks": "Agazienhulzbreddorr", "block.minecraft.acacia_pressure_plate": "Agazienhulzdruggbladde", "block.minecraft.acacia_sapling": "Agaziense<PERSON><PERSON>ling", "block.minecraft.acacia_sign": "Agazienhulzsch<PERSON>ld", "block.minecraft.acacia_slab": "Agazienhulzschdufe", "block.minecraft.acacia_stairs": "Agazienhulzdrebbe", "block.minecraft.acacia_trapdoor": "Agazienhulzfalldiere", "block.minecraft.acacia_wall_hanging_sign": "Agazienhulzwandhängeschüld", "block.minecraft.acacia_wall_sign": "Agazienhulzwandschüld", "block.minecraft.acacia_wood": "Agazienhulz", "block.minecraft.activator_rail": "Aggdivierungsschiene", "block.minecraft.air": "Lu<PERSON>d", "block.minecraft.allium": "Sc<PERSON><PERSON>nlau<PERSON>", "block.minecraft.amethyst_block": "Amethystdrusnblogg", "block.minecraft.amethyst_cluster": "Amethysthaufn", "block.minecraft.ancient_debris": "<PERSON><PERSON><PERSON>", "block.minecraft.andesite": "Andesid", "block.minecraft.andesite_slab": "Andesidschdufe", "block.minecraft.andesite_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.andesite_wall": "Andesidmauor", "block.minecraft.anvil": "Ambuss", "block.minecraft.attached_melon_stem": "Melonrange", "block.minecraft.attached_pumpkin_stem": "Gürbisschdängl", "block.minecraft.azalea": "<PERSON><PERSON><PERSON>", "block.minecraft.azalea_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azure_bluet": "Porzellanschdernschn", "block.minecraft.bamboo": "Bambus", "block.minecraft.bamboo_block": "Bambusblogg", "block.minecraft.bamboo_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_door": "Bambusdiere", "block.minecraft.bamboo_fence": "Bambuszaun", "block.minecraft.bamboo_fence_gate": "Bambuszaundorr", "block.minecraft.bamboo_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_mosaic": "Bambusmosaik", "block.minecraft.bamboo_mosaic_slab": "Bambusmosaikplatte", "block.minecraft.bamboo_mosaic_stairs": "Bambusmosaiktreppe", "block.minecraft.bamboo_planks": "Bambusbreddorr", "block.minecraft.bamboo_pressure_plate": "Bambusdruckplatte", "block.minecraft.bamboo_sapling": "Bambusschbrössling", "block.minecraft.bamboo_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_slab": "Bambusschdufe", "block.minecraft.bamboo_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_trapdoor": "Bambusfalldiere", "block.minecraft.bamboo_wall_hanging_sign": "Bambuswandhänges<PERSON><PERSON>ld", "block.minecraft.bamboo_wall_sign": "Bambus<PERSON><PERSON><PERSON><PERSON>ld", "block.minecraft.banner.base.black": "Ledsche schworze Fläsche", "block.minecraft.banner.base.blue": "Ledsche blaue Fläsche", "block.minecraft.banner.base.brown": "Ledsche braune Fläsche", "block.minecraft.banner.base.cyan": "Ledsche dürgise <PERSON>", "block.minecraft.banner.base.gray": "Ledsche graue Fläsche", "block.minecraft.banner.base.green": "Ledsche griene Fläsche", "block.minecraft.banner.base.light_blue": "Ledsche hellblaue Fläsche", "block.minecraft.banner.base.light_gray": "Ledsche hellgraue Fläsche", "block.minecraft.banner.base.lime": "Ledsche hellgriene Fläsche", "block.minecraft.banner.base.magenta": "Ledsche magenda Fläsche", "block.minecraft.banner.base.orange": "Ledsche orangsche Fläsche", "block.minecraft.banner.base.pink": "Ledsche pinge Fläsche", "block.minecraft.banner.base.purple": "Ledsche lila Fläsche", "block.minecraft.banner.base.red": "<PERSON><PERSON> rode F<PERSON><PERSON><PERSON>", "block.minecraft.banner.base.white": "Ledsche weeße Fläsche", "block.minecraft.banner.base.yellow": "Ledsche gelbe Fläsche", "block.minecraft.banner.border.black": "Schworzorr <PERSON>", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.lime": "Hellg<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.magenta": "<PERSON>gen<PERSON>", "block.minecraft.banner.border.orange": "Orangsch<PERSON><PERSON>", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.purple": "<PERSON>", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.yellow": "<PERSON>el<PERSON><PERSON>", "block.minecraft.banner.bricks.black": "<PERSON><PERSON><PERSON> schworz gemauord", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON><PERSON> blau gema<PERSON>rd", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON> braun gemauord", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON> d<PERSON> g<PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON><PERSON> grau gema<PERSON>", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON> grien gemauord", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON><PERSON> gemauord", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON> magenda gemauord", "block.minecraft.banner.bricks.orange": "<PERSON><PERSON><PERSON> gemauord", "block.minecraft.banner.bricks.pink": "<PERSON><PERSON><PERSON> ping gemauord", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON><PERSON> lila g<PERSON>", "block.minecraft.banner.bricks.red": "<PERSON><PERSON><PERSON> rod gemauord", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON> weeß gemauord", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON> gelb gemauord", "block.minecraft.banner.circle.black": "Schworze Guchl", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON><PERSON> Guchl", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.lime": "Hellg<PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "Magenda Guchl", "block.minecraft.banner.circle.orange": "Orangsche Guchl", "block.minecraft.banner.circle.pink": "<PERSON><PERSON>", "block.minecraft.banner.circle.purple": "<PERSON>", "block.minecraft.banner.circle.red": "<PERSON><PERSON>", "block.minecraft.banner.circle.white": "We<PERSON><PERSON> Guchl", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.black": "Sch<PERSON>rz<PERSON><PERSON>", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.light_blue": "Hellblauorr <PERSON>ree<PERSON>", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.magenta": "Magenda Creeper", "block.minecraft.banner.creeper.orange": "Orang<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.purple": "<PERSON>", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.black": "Schworzes Andreasgreuz", "block.minecraft.banner.cross.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.lime": "Hellgrienes <PERSON>uz", "block.minecraft.banner.cross.magenta": "Magenda Andreasgreuz", "block.minecraft.banner.cross.orange": "Orangsches Andreasgreuz", "block.minecraft.banner.cross.pink": "<PERSON><PERSON>", "block.minecraft.banner.cross.purple": "<PERSON>", "block.minecraft.banner.cross.red": "<PERSON><PERSON>", "block.minecraft.banner.cross.white": "Weeßes Andreasgreuz", "block.minecraft.banner.cross.yellow": "Gelbes Andreasgreuz", "block.minecraft.banner.curly_border.black": "Schworzorr Schbigglbord", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.light_blue": "Hellblauorr Schbigglbord", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON><PERSON><PERSON>lbord", "block.minecraft.banner.curly_border.lime": "Hellgrienorr Sc<PERSON>gglbord", "block.minecraft.banner.curly_border.magenta": "Magenda Schbigglbord", "block.minecraft.banner.curly_border.orange": "Orangschorr Schbigglbord", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.purple": "<PERSON>", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.white": "Weeßorr <PERSON>gglbord", "block.minecraft.banner.curly_border.yellow": "Gelborr Schbigglbord", "block.minecraft.banner.diagonal_left.black": "Schworz schrächlings gedeild", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON> schrächlings gedeild", "block.minecraft.banner.diagonal_left.brown": "Braun schrächlings gedeild", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> gedeild", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON> schr<PERSON><PERSON><PERSON> gedeild", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON> schr<PERSON><PERSON> gedeild", "block.minecraft.banner.diagonal_left.light_blue": "Hell<PERSON>ues schrächlings gedeild", "block.minecraft.banner.diagonal_left.light_gray": "Hellgrau schrächlings gedeild", "block.minecraft.banner.diagonal_left.lime": "Hell<PERSON>rien schr<PERSON>chlings gedeild", "block.minecraft.banner.diagonal_left.magenta": "Magenda schrächlings gedeild", "block.minecraft.banner.diagonal_left.orange": "Orangsch schrächlings gedeild", "block.minecraft.banner.diagonal_left.pink": "<PERSON> schr<PERSON>lings gedeild", "block.minecraft.banner.diagonal_left.purple": "<PERSON> s<PERSON>r<PERSON> gedeild", "block.minecraft.banner.diagonal_left.red": "<PERSON> s<PERSON>r<PERSON><PERSON> gedeild", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> schr<PERSON>chlings gedeild", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON> schr<PERSON><PERSON><PERSON> gedeild", "block.minecraft.banner.diagonal_right.black": "Schworz schrächreschds gedeild", "block.minecraft.banner.diagonal_right.blue": "Blau schrächreschds gedeild", "block.minecraft.banner.diagonal_right.brown": "Braun schrächreschds gedeild", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>chds gedeild", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON> schrächreschds gedeild", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON> s<PERSON>rächreschds gedeild", "block.minecraft.banner.diagonal_right.light_blue": "Hellblau schrächreschds gedeild", "block.minecraft.banner.diagonal_right.light_gray": "Hellgrau schrächreschds gedeild", "block.minecraft.banner.diagonal_right.lime": "Hellgrien schrächreschds gedeild", "block.minecraft.banner.diagonal_right.magenta": "Magenda schrächreschds gedeild", "block.minecraft.banner.diagonal_right.orange": "Orangsch schrächreschds gedeild", "block.minecraft.banner.diagonal_right.pink": "<PERSON> s<PERSON><PERSON>ds gedeild", "block.minecraft.banner.diagonal_right.purple": "<PERSON> s<PERSON>chds gedeild", "block.minecraft.banner.diagonal_right.red": "<PERSON> s<PERSON><PERSON>ds gedeild", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON>ß schrächreschds gedeild", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON> s<PERSON>r<PERSON>ch<PERSON>chds gedeild", "block.minecraft.banner.diagonal_up_left.black": "Schworz schrächreschds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.blue": "Blau schrächreschds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON> schrächreschds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.gray": "G<PERSON>u schrächreschds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON> schrächreschds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.light_blue": "Hellblau schrächreschds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.light_gray": "Hellgrau schrächreschds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.lime": "Hellgrien schrächreschds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.magenta": "Magenda schrächreschds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.orange": "Orangsch schrächreschds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON> s<PERSON>r<PERSON><PERSON><PERSON>ds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON> s<PERSON>r<PERSON>ch<PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.red": "<PERSON> s<PERSON><PERSON><PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.white": "<PERSON><PERSON>ß schrächreschds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON><PERSON> schr<PERSON>chreschds gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.black": "Schworz schräch<PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON> schr<PERSON><PERSON><PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON> schräch<PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> g<PERSON> (Inverdiord)", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON><PERSON> schr<PERSON><PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON> schr<PERSON><PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.light_blue": "Hellblau schräch<PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.light_gray": "Hellgrau schr<PERSON>ch<PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.lime": "Hellgrien schr<PERSON><PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.magenta": "Magenda schr<PERSON>ch<PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.orange": "Orangsch schräch<PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON> schr<PERSON><PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON> s<PERSON>r<PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON><PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON> schr<PERSON><PERSON> gedeild (Inverdiord)", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON> s<PERSON>r<PERSON><PERSON><PERSON> gedeild (Inverdiord)", "block.minecraft.banner.flow.black": "Schwarzer Fluss", "block.minecraft.banner.flow.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.brown": "Brauner Fluss", "block.minecraft.banner.flow.cyan": "Türkiser Fluss", "block.minecraft.banner.flow.gray": "<PERSON><PERSON><PERSON> Flus<PERSON>", "block.minecraft.banner.flow.green": "<PERSON>r<PERSON><PERSON> Fluss", "block.minecraft.banner.flow.light_blue": "Hellblauer Fluss", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON><PERSON> Fluss", "block.minecraft.banner.flow.lime": "Hellgrüner Fluss", "block.minecraft.banner.flow.magenta": "Magenta Fluss", "block.minecraft.banner.flow.orange": "Oranger Fluss", "block.minecraft.banner.flow.pink": "<PERSON>", "block.minecraft.banner.flow.purple": "<PERSON><PERSON> Fluss", "block.minecraft.banner.flow.red": "<PERSON><PERSON>", "block.minecraft.banner.flow.white": "<PERSON><PERSON><PERSON> Fluss", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON><PERSON> F<PERSON>", "block.minecraft.banner.flower.black": "Schworze Blume", "block.minecraft.banner.flower.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.brown": "<PERSON><PERSON>", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON><PERSON> Blu<PERSON>", "block.minecraft.banner.flower.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.lime": "Hellg<PERSON><PERSON>", "block.minecraft.banner.flower.magenta": "Magenda Blume", "block.minecraft.banner.flower.orange": "Orangsche Blu<PERSON>", "block.minecraft.banner.flower.pink": "<PERSON><PERSON>", "block.minecraft.banner.flower.purple": "<PERSON>", "block.minecraft.banner.flower.red": "<PERSON><PERSON>", "block.minecraft.banner.flower.white": "Weeße Blume", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.black": "Schworzorr Globus", "block.minecraft.banner.globe.blue": "Blauorr Globus", "block.minecraft.banner.globe.brown": "Braunorr Globus", "block.minecraft.banner.globe.cyan": "Dürgisorr Globus", "block.minecraft.banner.globe.gray": "Grauorr Globus", "block.minecraft.banner.globe.green": "Grienorr Globus", "block.minecraft.banner.globe.light_blue": "Hellblauorr Globus", "block.minecraft.banner.globe.light_gray": "Hellgrauorr Globus", "block.minecraft.banner.globe.lime": "Hellgrienorr Globus", "block.minecraft.banner.globe.magenta": "Magenda Globus", "block.minecraft.banner.globe.orange": "Orangschorr Globus", "block.minecraft.banner.globe.pink": "Pingorr Globus", "block.minecraft.banner.globe.purple": "Lila Globus", "block.minecraft.banner.globe.red": "Rodorr Globus", "block.minecraft.banner.globe.white": "Weeßorr Globus", "block.minecraft.banner.globe.yellow": "Gelborr Globus", "block.minecraft.banner.gradient.black": "Schworz<PERSON><PERSON>", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON>of", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON><PERSON>of", "block.minecraft.banner.gradient.light_blue": "<PERSON>blauorr <PERSON>loof", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON><PERSON><PERSON>loof", "block.minecraft.banner.gradient.lime": "Hellg<PERSON><PERSON><PERSON>loof", "block.minecraft.banner.gradient.magenta": "Magenda Farbvorloof", "block.minecraft.banner.gradient.orange": "Orangsch<PERSON><PERSON>", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON><PERSON>loof", "block.minecraft.banner.gradient.purple": "<PERSON>", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON>loof", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.yellow": "Gel<PERSON><PERSON>", "block.minecraft.banner.gradient_up.black": "Sch<PERSON>rz<PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON><PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.light_blue": "<PERSON><PERSON><PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.light_gray": "<PERSON><PERSON><PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Inverdierd)", "block.minecraft.banner.gradient_up.magenta": "Magen<PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.orange": "Orangsch<PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.purple": "<PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.gradient_up.yellow": "<PERSON>el<PERSON><PERSON> (Inverdiord)", "block.minecraft.banner.guster.black": "Schwarzer Windstoßer", "block.minecraft.banner.guster.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.brown": "<PERSON><PERSON> Windstoßer", "block.minecraft.banner.guster.cyan": "Türkiser Windstoßer", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON><PERSON> Windsto<PERSON>", "block.minecraft.banner.guster.light_blue": "Hellblauer Windstoßer", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON>uer Windstoßer", "block.minecraft.banner.guster.lime": "Hellgrüner Windstoßer", "block.minecraft.banner.guster.magenta": "Magenta Windstoßer", "block.minecraft.banner.guster.orange": "<PERSON><PERSON>", "block.minecraft.banner.guster.pink": "<PERSON>", "block.minecraft.banner.guster.purple": "<PERSON><PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON> Windstoßer", "block.minecraft.banner.half_horizontal.black": "Oobn schworz gedeild", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON> blau gedeild", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON> braun gedeild", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> g<PERSON>d", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON><PERSON> grau gedeild", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON> grien gedeild", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON><PERSON> hell<PERSON> gedeild", "block.minecraft.banner.half_horizontal.light_gray": "<PERSON><PERSON><PERSON> hell<PERSON> gedeild", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON><PERSON> gedeild", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON>n magenda gedeild", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON> or<PERSON> gedeild", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON>n ping gedeild", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON><PERSON> lila g<PERSON>ild", "block.minecraft.banner.half_horizontal.red": "O<PERSON>n rod gedeild", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON>n weeß gedeild", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON>n gelb gedeild", "block.minecraft.banner.half_horizontal_bottom.black": "Und’n schworz gedeild", "block.minecraft.banner.half_horizontal_bottom.blue": "Und’n blau gedeild", "block.minecraft.banner.half_horizontal_bottom.brown": "Und’n braun gedeild", "block.minecraft.banner.half_horizontal_bottom.cyan": "Und’n dürgis gedeild", "block.minecraft.banner.half_horizontal_bottom.gray": "Und’n grau gedeild", "block.minecraft.banner.half_horizontal_bottom.green": "Und’n grien gedeild", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Und’n hellblau gedeild", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Und’n hellgrau gedeild", "block.minecraft.banner.half_horizontal_bottom.lime": "Und’n hellgrien gedeild", "block.minecraft.banner.half_horizontal_bottom.magenta": "Und’n magenda gedeild", "block.minecraft.banner.half_horizontal_bottom.orange": "Und’n orangsch gedeild", "block.minecraft.banner.half_horizontal_bottom.pink": "Und’n ping gedeild", "block.minecraft.banner.half_horizontal_bottom.purple": "Und’n lila gedeild", "block.minecraft.banner.half_horizontal_bottom.red": "Und’n rod gedeild", "block.minecraft.banner.half_horizontal_bottom.white": "Und’n weeß gedeild", "block.minecraft.banner.half_horizontal_bottom.yellow": "Und’n gelb gedeild", "block.minecraft.banner.half_vertical.black": "Reschds schworz geschbaldn", "block.minecraft.banner.half_vertical.blue": "Reschds blau geschbaldn", "block.minecraft.banner.half_vertical.brown": "Reschds braun geschbaldn", "block.minecraft.banner.half_vertical.cyan": "Reschds dürgis g<PERSON>", "block.minecraft.banner.half_vertical.gray": "Reschds grau geschbaldn", "block.minecraft.banner.half_vertical.green": "Reschds grien geschbaldn", "block.minecraft.banner.half_vertical.light_blue": "Reschds hellblau geschbaldn", "block.minecraft.banner.half_vertical.light_gray": "Reschds hellgrau geschbaldn", "block.minecraft.banner.half_vertical.lime": "Reschds hellgrien geschbaldn", "block.minecraft.banner.half_vertical.magenta": "Reschds magenda geschbaldn", "block.minecraft.banner.half_vertical.orange": "Reschds orangsch geschbaldn", "block.minecraft.banner.half_vertical.pink": "Re<PERSON><PERSON> ping geschbaldn", "block.minecraft.banner.half_vertical.purple": "Reschds lila geschbaldn", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON><PERSON> rod geschbaldn", "block.minecraft.banner.half_vertical.white": "<PERSON><PERSON><PERSON> weeß geschbaldn", "block.minecraft.banner.half_vertical.yellow": "Reschds gelb geschbaldn", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON> schworz geschbaldn", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON> blau ges<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.brown": "<PERSON>s braun geschbaldn", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON> dürg<PERSON> g<PERSON>", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON> grau gesch<PERSON>dn", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON> grien gesch<PERSON>dn", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON><PERSON> gesch<PERSON>dn", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON><PERSON><PERSON><PERSON> gesch<PERSON>dn", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON>s magenda ges<PERSON><PERSON>dn", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON> geschbaldn", "block.minecraft.banner.half_vertical_right.pink": "<PERSON>s ping geschbaldn", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON> lila g<PERSON>", "block.minecraft.banner.half_vertical_right.red": "<PERSON>s rod geschbaldn", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON> weeß geschbaldn", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON> gelb ges<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.black": "Schworzes Mojang-Logo", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON>ues Mojang-Logo", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.lime": "Hellgrienes <PERSON>g-Logo", "block.minecraft.banner.mojang.magenta": "Magenda Mojang-Logo", "block.minecraft.banner.mojang.orange": "Orangsches Mojang-Logo", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON>", "block.minecraft.banner.mojang.purple": "<PERSON>", "block.minecraft.banner.mojang.red": "<PERSON><PERSON>", "block.minecraft.banner.mojang.white": "Weeßes Mojang-Logo", "block.minecraft.banner.mojang.yellow": "Gelbes Mojang-Logo", "block.minecraft.banner.piglin.black": "Schworze Gusche", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.light_blue": "Hellblaue Gus<PERSON>", "block.minecraft.banner.piglin.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.lime": "Hellgrien<PERSON>", "block.minecraft.banner.piglin.magenta": "Magenda Gusche", "block.minecraft.banner.piglin.orange": "Orangsche Gusche", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON>", "block.minecraft.banner.piglin.purple": "<PERSON>", "block.minecraft.banner.piglin.red": "<PERSON><PERSON>", "block.minecraft.banner.piglin.white": "Weeße Gusche", "block.minecraft.banner.piglin.yellow": "Gelbe <PERSON>", "block.minecraft.banner.rhombus.black": "Schworz<PERSON> Ra<PERSON>", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.orange": "Orang<PERSON>", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.purple": "<PERSON>", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.white": "Weeße Raude", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.black": "Schworz<PERSON><PERSON>", "block.minecraft.banner.skull.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.magenta": "Magenda Nischl", "block.minecraft.banner.skull.orange": "Orangsch<PERSON><PERSON>", "block.minecraft.banner.skull.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.purple": "<PERSON>", "block.minecraft.banner.skull.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.yellow": "<PERSON>el<PERSON><PERSON>", "block.minecraft.banner.small_stripes.black": "<PERSON>orr schworze Pfähle", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON><PERSON> blau<PERSON>", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON><PERSON>raue <PERSON>", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON>r griene Pfähle", "block.minecraft.banner.small_stripes.light_blue": "<PERSON><PERSON><PERSON> Pfähle", "block.minecraft.banner.small_stripes.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.lime": "<PERSON><PERSON><PERSON>g<PERSON>e Pfähle", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.orange": "Fiorr orangsche Pfähle", "block.minecraft.banner.small_stripes.pink": "<PERSON><PERSON><PERSON> pinge <PERSON>", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON><PERSON> rode <PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON><PERSON> we<PERSON> Pfähle", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON><PERSON> gelbe <PERSON>", "block.minecraft.banner.square_bottom_left.black": "Schworzes reschdes Undorregg", "block.minecraft.banner.square_bottom_left.blue": "<PERSON><PERSON><PERSON> reschdes Undorregg", "block.minecraft.banner.square_bottom_left.brown": "Braunes reschdes Undorregg", "block.minecraft.banner.square_bottom_left.cyan": "Dürgises reschdes Undorregg", "block.minecraft.banner.square_bottom_left.gray": "<PERSON><PERSON><PERSON> re<PERSON>des Undorregg", "block.minecraft.banner.square_bottom_left.green": "Grienes reschdes Undorregg", "block.minecraft.banner.square_bottom_left.light_blue": "Hellblaues reschdes Undorregg", "block.minecraft.banner.square_bottom_left.light_gray": "<PERSON><PERSON><PERSON> reschdes Undorregg", "block.minecraft.banner.square_bottom_left.lime": "Hellgrienes reschdes Undorregg", "block.minecraft.banner.square_bottom_left.magenta": "Magenda reschdes Undorregg", "block.minecraft.banner.square_bottom_left.orange": "Orangsches reschdes Undorregg", "block.minecraft.banner.square_bottom_left.pink": "<PERSON><PERSON> reschdes Undorregg", "block.minecraft.banner.square_bottom_left.purple": "<PERSON> re<PERSON> Undorregg", "block.minecraft.banner.square_bottom_left.red": "Rodes reschdes Undorregg", "block.minecraft.banner.square_bottom_left.white": "Weeßes reschdes Undorregg", "block.minecraft.banner.square_bottom_left.yellow": "Gelbes reschdes Undorregg", "block.minecraft.banner.square_bottom_right.black": "Schworzes linges Undorregg", "block.minecraft.banner.square_bottom_right.blue": "<PERSON><PERSON><PERSON> linges <PERSON>gg", "block.minecraft.banner.square_bottom_right.brown": "<PERSON><PERSON> linges Undorregg", "block.minecraft.banner.square_bottom_right.cyan": "<PERSON><PERSON><PERSON><PERSON> linges Undorregg", "block.minecraft.banner.square_bottom_right.gray": "<PERSON><PERSON><PERSON> l<PERSON>s <PERSON>dor<PERSON>gg", "block.minecraft.banner.square_bottom_right.green": "<PERSON><PERSON><PERSON> linges Undorregg", "block.minecraft.banner.square_bottom_right.light_blue": "Hell<PERSON>ues linges Undorregg", "block.minecraft.banner.square_bottom_right.light_gray": "<PERSON><PERSON><PERSON> linges <PERSON>dor<PERSON>gg", "block.minecraft.banner.square_bottom_right.lime": "Hellgrienes linges Undorregg", "block.minecraft.banner.square_bottom_right.magenta": "Magenda linges Undorregg", "block.minecraft.banner.square_bottom_right.orange": "Orangsches linges Undorregg", "block.minecraft.banner.square_bottom_right.pink": "<PERSON><PERSON> linges <PERSON>gg", "block.minecraft.banner.square_bottom_right.purple": "<PERSON> linges <PERSON>dor<PERSON>gg", "block.minecraft.banner.square_bottom_right.red": "Rodes linges Undorregg", "block.minecraft.banner.square_bottom_right.white": "Weeßes linges Undorregg", "block.minecraft.banner.square_bottom_right.yellow": "Gelbes linges Undorregg", "block.minecraft.banner.square_top_left.black": "Schworzes reschdes Oborregg", "block.minecraft.banner.square_top_left.blue": "<PERSON><PERSON><PERSON> re<PERSON> Oborregg", "block.minecraft.banner.square_top_left.brown": "Braunes reschdes Oborregg", "block.minecraft.banner.square_top_left.cyan": "Dürgises reschdes Oborregg", "block.minecraft.banner.square_top_left.gray": "<PERSON><PERSON><PERSON> re<PERSON> Oborregg", "block.minecraft.banner.square_top_left.green": "Grienes reschdes Oborregg", "block.minecraft.banner.square_top_left.light_blue": "Hellblaues reschdes Oborregg", "block.minecraft.banner.square_top_left.light_gray": "<PERSON><PERSON><PERSON> reschdes Oborregg", "block.minecraft.banner.square_top_left.lime": "Hellgrienes reschdes Oborregg", "block.minecraft.banner.square_top_left.magenta": "<PERSON> re<PERSON> Oborregg", "block.minecraft.banner.square_top_left.orange": "Orangsches reschdes Oborregg", "block.minecraft.banner.square_top_left.pink": "<PERSON><PERSON> re<PERSON>des Oborregg", "block.minecraft.banner.square_top_left.purple": "<PERSON> re<PERSON> Oborregg", "block.minecraft.banner.square_top_left.red": "Rodes reschdes Oborregg", "block.minecraft.banner.square_top_left.white": "Weeßes reschdes Oborregg", "block.minecraft.banner.square_top_left.yellow": "Gelbes reschdes Oborregg", "block.minecraft.banner.square_top_right.black": "Schworzes linges Oborregg", "block.minecraft.banner.square_top_right.blue": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.brown": "<PERSON><PERSON> linges Oborregg", "block.minecraft.banner.square_top_right.cyan": "<PERSON><PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.gray": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.green": "<PERSON><PERSON><PERSON> linges <PERSON>gg", "block.minecraft.banner.square_top_right.light_blue": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.light_gray": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.lime": "Hellgrienes linges <PERSON>gg", "block.minecraft.banner.square_top_right.magenta": "<PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.orange": "Orangsches linges <PERSON>gg", "block.minecraft.banner.square_top_right.pink": "<PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.purple": "<PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.red": "Rodes linges O<PERSON>regg", "block.minecraft.banner.square_top_right.white": "Weeßes linges Oborregg", "block.minecraft.banner.square_top_right.yellow": "Gelbes linges <PERSON>gg", "block.minecraft.banner.straight_cross.black": "Schworzes Greuz", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON> G<PERSON>", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_blue": "Hell<PERSON>ues Greuz", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.lime": "Hellgrienes Greuz", "block.minecraft.banner.straight_cross.magenta": "Magenda Greuz", "block.minecraft.banner.straight_cross.orange": "Orangsches Greuz", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.purple": "<PERSON>", "block.minecraft.banner.straight_cross.red": "Rodes Greuz", "block.minecraft.banner.straight_cross.white": "Weeßes Greuz", "block.minecraft.banner.straight_cross.yellow": "Gelbes Greuz", "block.minecraft.banner.stripe_bottom.black": "Schworzorr <PERSON>", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "Hellblauorr <PERSON>", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.lime": "Hellgrien<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.magenta": "Magenda Bannorrfuß", "block.minecraft.banner.stripe_bottom.orange": "Orangschorr <PERSON>", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.purple": "<PERSON>", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.yellow": "Gelborr <PERSON>", "block.minecraft.banner.stripe_center.black": "Schworzorr <PERSON>", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.lime": "Hellgrienor<PERSON>", "block.minecraft.banner.stripe_center.magenta": "Magenda Pfahl", "block.minecraft.banner.stripe_center.orange": "Orangschorr <PERSON>", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.purple": "<PERSON>", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.white": "Weeßor<PERSON>", "block.minecraft.banner.stripe_center.yellow": "Gelborr <PERSON>", "block.minecraft.banner.stripe_downleft.black": "Schworzorr <PERSON>balgn", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_blue": "Hellblauorr Schrächlingsbalgn", "block.minecraft.banner.stripe_downleft.light_gray": "<PERSON><PERSON><PERSON><PERSON>balgn", "block.minecraft.banner.stripe_downleft.lime": "Hellgrienorr <PERSON>bal<PERSON>", "block.minecraft.banner.stripe_downleft.magenta": "Magenda Schrächlingsbalgn", "block.minecraft.banner.stripe_downleft.orange": "Orangschorr Schrä<PERSON>lingsbalgn", "block.minecraft.banner.stripe_downleft.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.purple": "<PERSON>", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.white": "We<PERSON>ß<PERSON><PERSON>balgn", "block.minecraft.banner.stripe_downleft.yellow": "Gelborr <PERSON>hrä<PERSON>lingsbalgn", "block.minecraft.banner.stripe_downright.black": "Schworzor<PERSON>", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.light_blue": "Hellblauorr <PERSON>balgn", "block.minecraft.banner.stripe_downright.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.lime": "Hellgrien<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.magenta": "Magenda Schrächbalgn", "block.minecraft.banner.stripe_downright.orange": "Orangschorr <PERSON>", "block.minecraft.banner.stripe_downright.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.purple": "<PERSON>", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.yellow": "Gelborr <PERSON>", "block.minecraft.banner.stripe_left.black": "Schworze reschde Flange", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON><PERSON> reschde Flange", "block.minecraft.banner.stripe_left.brown": "Braune reschde Flange", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.gray": "<PERSON><PERSON><PERSON> reschde Flange", "block.minecraft.banner.stripe_left.green": "Griene reschde Flange", "block.minecraft.banner.stripe_left.light_blue": "Hellblaue reschde Flange", "block.minecraft.banner.stripe_left.light_gray": "Hellgraue reschde Flange", "block.minecraft.banner.stripe_left.lime": "Hellgriene reschde Flange", "block.minecraft.banner.stripe_left.magenta": "Magenda reschde Flange", "block.minecraft.banner.stripe_left.orange": "Orangsche reschde Flange", "block.minecraft.banner.stripe_left.pink": "<PERSON><PERSON> reschde Flange", "block.minecraft.banner.stripe_left.purple": "<PERSON> re<PERSON>", "block.minecraft.banner.stripe_left.red": "Rode reschde Flange", "block.minecraft.banner.stripe_left.white": "Weeße reschde Flange", "block.minecraft.banner.stripe_left.yellow": "Gelbe reschde Flange", "block.minecraft.banner.stripe_middle.black": "Schworzorr Balgn", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "Hellblauorr Balgn", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.lime": "Hellg<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.magenta": "Magenda Balgn", "block.minecraft.banner.stripe_middle.orange": "Orangschorr <PERSON>", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.purple": "<PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "Weeßorr Balgn", "block.minecraft.banner.stripe_middle.yellow": "Gelborr Balgn", "block.minecraft.banner.stripe_right.black": "Schworze linge Flange", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON> l<PERSON>e", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_right.gray": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_right.light_blue": "Hellblaue linge Flange", "block.minecraft.banner.stripe_right.light_gray": "<PERSON><PERSON><PERSON> linge Flange", "block.minecraft.banner.stripe_right.lime": "Hellgriene linge Flange", "block.minecraft.banner.stripe_right.magenta": "Magenda linge Flange", "block.minecraft.banner.stripe_right.orange": "Orangsche linge Flange", "block.minecraft.banner.stripe_right.pink": "<PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_right.purple": "<PERSON> l<PERSON>", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON> linge <PERSON>e", "block.minecraft.banner.stripe_right.white": "Weeße linge Flange", "block.minecraft.banner.stripe_right.yellow": "Gelbe linge Flange", "block.minecraft.banner.stripe_top.black": "Schworzes <PERSON>ha<PERSON>", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.orange": "Orangsches Bannorrhaubd", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.purple": "<PERSON>", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.white": "Weeßes Bannorrhaubd", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON><PERSON>d", "block.minecraft.banner.triangle_bottom.black": "Schworze halbe <PERSON>", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "Hellblaue halbe <PERSON>", "block.minecraft.banner.triangle_bottom.light_gray": "<PERSON><PERSON><PERSON> halbe <PERSON>", "block.minecraft.banner.triangle_bottom.lime": "Hellgriene hal<PERSON>", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.orange": "Orangsche halbe Sch<PERSON>ze", "block.minecraft.banner.triangle_bottom.pink": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.purple": "<PERSON>", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.white": "Weeße halbe Schbidze", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON>elbe halbe <PERSON>", "block.minecraft.banner.triangle_top.black": "Schworze geschdürzde halbe Schbidze", "block.minecraft.banner.triangle_top.blue": "<PERSON><PERSON>e geschdürzde halbe <PERSON>", "block.minecraft.banner.triangle_top.brown": "Braune geschdürzde halbe Schbidze", "block.minecraft.banner.triangle_top.cyan": "<PERSON><PERSON><PERSON><PERSON> geschdürzde halbe Schbidze", "block.minecraft.banner.triangle_top.gray": "<PERSON><PERSON>ue geschdürzde halbe <PERSON>", "block.minecraft.banner.triangle_top.green": "Griene geschdürzde halbe Schbidze", "block.minecraft.banner.triangle_top.light_blue": "Hellblaue geschdürzde halbe Schbidze", "block.minecraft.banner.triangle_top.light_gray": "Hellgraue geschdürzde halbe Schbidze", "block.minecraft.banner.triangle_top.lime": "Hellgriene geschdürzde halbe Schbidze", "block.minecraft.banner.triangle_top.magenta": "Magenda geschdürzde halbe Schbidze", "block.minecraft.banner.triangle_top.orange": "Orangsche geschdürzde halbe Schbidze", "block.minecraft.banner.triangle_top.pink": "Pinge geschdürzde halbe <PERSON>", "block.minecraft.banner.triangle_top.purple": "Lila geschdürzde halbe <PERSON>", "block.minecraft.banner.triangle_top.red": "Rode geschdürzde halbe Schbidze", "block.minecraft.banner.triangle_top.white": "Weeße geschdürzde halbe Schbidze", "block.minecraft.banner.triangle_top.yellow": "Gelbe geschdürzde halbe Schbidze", "block.minecraft.banner.triangles_bottom.black": "Schworzorr geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_bottom.blue": "Blauorr geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_bottom.brown": "Braunorr geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_bottom.cyan": "D<PERSON>rg<PERSON>orr geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_bottom.gray": "G<PERSON><PERSON>rr geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_bottom.green": "Grienorr geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_bottom.light_blue": "Hellblauorr geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_bottom.light_gray": "Hellgrauorr geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_bottom.lime": "Hellgrienorr geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_bottom.magenta": "Magenda geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_bottom.orange": "Orangschorr geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_bottom.pink": "<PERSON><PERSON><PERSON> geschbiggeldorr Bannorrf<PERSON>ß", "block.minecraft.banner.triangles_bottom.purple": "<PERSON> g<PERSON><PERSON><PERSON>gg<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON><PERSON> geschbiggeldorr Bannorrf<PERSON>ß", "block.minecraft.banner.triangles_bottom.white": "Weeßorr geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_bottom.yellow": "Gelborr geschbiggeldorr Bannorrfuß", "block.minecraft.banner.triangles_top.black": "Schworzes geschbiggeldes Bannorrhaubd", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON>ggeldes Bannorrhaubd", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON> geschbiggeldes Bannorrhaubd", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON>rgises geschbiggeldes Bannorrhaubd", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Bannorrhaubd", "block.minecraft.banner.triangles_top.green": "G<PERSON><PERSON> geschbiggeldes Bannorrhaubd", "block.minecraft.banner.triangles_top.light_blue": "Hell<PERSON>ues geschbiggeldes Bannorrhaubd", "block.minecraft.banner.triangles_top.light_gray": "<PERSON><PERSON><PERSON> geschbiggeldes Bannorrhaubd", "block.minecraft.banner.triangles_top.lime": "Hellgrienes geschbiggeldes Bannorrhaubd", "block.minecraft.banner.triangles_top.magenta": "Magenda geschbiggeldes Bannorrhaubd", "block.minecraft.banner.triangles_top.orange": "Orangsches geschbiggeldes Bannorrhaubd", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON> geschbiggeld<PERSON> Bannorrhaubd", "block.minecraft.banner.triangles_top.purple": "<PERSON> g<PERSON>gg<PERSON><PERSON> Bannorrhaubd", "block.minecraft.banner.triangles_top.red": "Rodes geschbiggeldes Bannorrhaubd", "block.minecraft.banner.triangles_top.white": "Weeßes geschbiggeldes Bannorrhaubd", "block.minecraft.banner.triangles_top.yellow": "Gelbes geschbiggeldes Bannorrhaubd", "block.minecraft.barrel": "Fass", "block.minecraft.barrier": "Barriere", "block.minecraft.basalt": "Basald", "block.minecraft.beacon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beacon.primary": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beacon.secondary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bed.no_sleep": "Du kannsd nur nachds odorr bei Gewiddorr schlafn", "block.minecraft.bed.not_safe": "Du kannsd jäddse nisch schlafn, es sin Monsdorr in dorr Nähe", "block.minecraft.bed.obstructed": "<PERSON><PERSON> is bloggierd", "block.minecraft.bed.occupied": "<PERSON><PERSON> is beleeschd", "block.minecraft.bed.too_far_away": "Du kannsd jäddse nisch schlafn, das Bädde is zu weid endfernd", "block.minecraft.bedrock": "Grundgeschdeen", "block.minecraft.bee_nest": "Biennesd", "block.minecraft.beehive": "Bienschdogg", "block.minecraft.beetroots": "<PERSON><PERSON>", "block.minecraft.bell": "Glogge", "block.minecraft.big_dripleaf": "Großes Drobbbladd", "block.minecraft.big_dripleaf_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>ch<PERSON>", "block.minecraft.birch_button": "Bürschnhulzgnopp", "block.minecraft.birch_door": "Bürschnhulzdiere", "block.minecraft.birch_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_fence_gate": "Bürschnhulzzaundorr", "block.minecraft.birch_hanging_sign": "Bürschnhulzhänges<PERSON><PERSON><PERSON>", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_log": "Bürschnschdamm", "block.minecraft.birch_planks": "Bürschnhulzbreddorr", "block.minecraft.birch_pressure_plate": "Bürschnhulzdruggbladde", "block.minecraft.birch_sapling": "Bürschnseddsling", "block.minecraft.birch_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_slab": "Bürschnhulzschdufe", "block.minecraft.birch_stairs": "<PERSON><PERSON><PERSON>nh<PERSON>zdrebbe", "block.minecraft.birch_trapdoor": "Bürschnhulzfalldiere", "block.minecraft.birch_wall_hanging_sign": "Bürschnhulzwandhänges<PERSON><PERSON>ld", "block.minecraft.birch_wall_sign": "Bürschnhulzwandschüld", "block.minecraft.birch_wood": "Bürschnhulz", "block.minecraft.black_banner": "Schworzes Bannorr", "block.minecraft.black_bed": "Schworzes Bädde", "block.minecraft.black_candle": "Schworze Gerdse", "block.minecraft.black_candle_cake": "Guchn mid schworzorr Gerdse", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_concrete": "Sch<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_glazed_terracotta": "Schworze glasierde Geramigg", "block.minecraft.black_shulker_box": "Schworze Shulker-Gissde", "block.minecraft.black_stained_glass": "Schworzes Glas", "block.minecraft.black_stained_glass_pane": "Schworze Glasscheebe", "block.minecraft.black_terracotta": "Schworze Geramigg", "block.minecraft.black_wool": "Schworze Wolle", "block.minecraft.blackstone": "Schworzschdeen", "block.minecraft.blackstone_slab": "Schworzschdeenschdufe", "block.minecraft.blackstone_stairs": "Schworzschdeendrebbe", "block.minecraft.blackstone_wall": "Schworzschdeenmauor", "block.minecraft.blast_furnace": "Schmelzofn", "block.minecraft.blue_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle_cake": "<PERSON><PERSON>n mid blauorr <PERSON>", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON><PERSON> glas<PERSON> G<PERSON>", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.bone_block": "Gnochnblogg", "block.minecraft.bookshelf": "Schwahrdnregal", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_block": "Hirngorallnblogg", "block.minecraft.brain_coral_fan": "Hirngorallnfäschorr", "block.minecraft.brain_coral_wall_fan": "Hirngorallnwandfäschorr", "block.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_slab": "Zieschlschdufe", "block.minecraft.brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_wall": "Zieschlschdeenmauor", "block.minecraft.bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "<PERSON><PERSON>", "block.minecraft.brown_bed": "<PERSON><PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON>", "block.minecraft.brown_candle_cake": "<PERSON><PERSON>n mid brauno<PERSON>", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_glazed_terracotta": "Braune glasierde Geramigg", "block.minecraft.brown_mushroom": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON>r Bilzblogg", "block.minecraft.brown_shulker_box": "<PERSON><PERSON>-G<PERSON>de", "block.minecraft.brown_stained_glass": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.brown_terracotta": "<PERSON><PERSON> Gera<PERSON>gg", "block.minecraft.brown_wool": "<PERSON><PERSON>", "block.minecraft.bubble_column": "Blasnsäule", "block.minecraft.bubble_coral": "Blasngoralle", "block.minecraft.bubble_coral_block": "Blasngorallnblogg", "block.minecraft.bubble_coral_fan": "Blasngorallnfäschorr", "block.minecraft.bubble_coral_wall_fan": "Blasngorallnwandfäschorr", "block.minecraft.budding_amethyst": "Gnosbende Amethystdruse", "block.minecraft.bush": "<PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Kaktusblüte", "block.minecraft.cake": "<PERSON><PERSON><PERSON>", "block.minecraft.calcite": "Galzid", "block.minecraft.calibrated_sculk_sensor": "Kali<PERSON><PERSON><PERSON>", "block.minecraft.campfire": "Gnäggorrschn", "block.minecraft.candle": "<PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "Guchn mid Gerdse", "block.minecraft.carrots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.carved_pumpkin": "Geschnidzdorr <PERSON>ü<PERSON>", "block.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_air": "Höhlnlufd", "block.minecraft.cave_vines": "Höhlnrangn", "block.minecraft.cave_vines_plant": "Höhlnrangnpflanze", "block.minecraft.chain": "<PERSON><PERSON><PERSON>", "block.minecraft.chain_command_block": "Keddn-Befählsblogg", "block.minecraft.cherry_button": "Kirschknopf", "block.minecraft.cherry_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_leaves": "<PERSON><PERSON>elü<PERSON><PERSON>", "block.minecraft.cherry_log": "Kirschendiele", "block.minecraft.cherry_planks": "Kirschendieln", "block.minecraft.cherry_pressure_plate": "Kirschedruckplatte", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_sign": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_slab": "<PERSON><PERSON>enschwi<PERSON>", "block.minecraft.cherry_stairs": "Kirschentreppe", "block.minecraft.cherry_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_wall_sign": "Kirschewandschild", "block.minecraft.cherry_wood": "Girs<PERSON><PERSON>z", "block.minecraft.chest": "<PERSON><PERSON><PERSON>", "block.minecraft.chipped_anvil": "Angeschlachnorr Ambuss", "block.minecraft.chiseled_bookshelf": "Gemeißeltes Bücherschaff", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_deepslate": "G<PERSON><PERSON><PERSON><PERSON><PERSON>nschieforr", "block.minecraft.chiseled_nether_bricks": "Gemeißelde Netherzieschl", "block.minecraft.chiseled_polished_blackstone": "G<PERSON><PERSON><PERSON><PERSON><PERSON> bolierd<PERSON>r <PERSON>", "block.minecraft.chiseled_quartz_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rod<PERSON><PERSON>", "block.minecraft.chiseled_resin_bricks": "Gemeißelte Harzziegel", "block.minecraft.chiseled_sandstone": "Gemeißeldorr <PERSON>en", "block.minecraft.chiseled_stone_bricks": "Gemeißeldorr Schdeenzieschl", "block.minecraft.chiseled_tuff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_tuff_bricks": "Gemeißelte Tuffsteinziegel", "block.minecraft.chorus_flower": "Chorusblü<PERSON>", "block.minecraft.chorus_plant": "Choruspflanze", "block.minecraft.clay": "Don", "block.minecraft.closed_eyeblossom": "Geschlossene Augenblüte", "block.minecraft.coal_block": "Gohleblogg", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "Grobe Ährde", "block.minecraft.cobbled_deepslate": "Bruchdiefnschieforr", "block.minecraft.cobbled_deepslate_slab": "Bruchdiefnschieforrschdufe", "block.minecraft.cobbled_deepslate_stairs": "Bruchdiefnschieforrdrebbe", "block.minecraft.cobbled_deepslate_wall": "Bruchdiefnschieforrmauor", "block.minecraft.cobblestone": "Bruchschdeen", "block.minecraft.cobblestone_slab": "Bruchschdeenschdufe", "block.minecraft.cobblestone_stairs": "Bruchschdeendrebbe", "block.minecraft.cobblestone_wall": "Bruchschdeenmauor", "block.minecraft.cobweb": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cocoa": "Gaggau", "block.minecraft.command_block": "Befählsblogg", "block.minecraft.comparator": "Redstone-Gombaradorr", "block.minecraft.composter": "Gomboschdorr", "block.minecraft.conduit": "Aquisadorr", "block.minecraft.copper_block": "Gupforrblogg", "block.minecraft.copper_bulb": "Kupferleuchte", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON>rr<PERSON><PERSON>", "block.minecraft.copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "<PERSON><PERSON>’sche Diefnschieforrzieschl", "block.minecraft.cracked_deepslate_tiles": "Riss’sche Diefnschieforrfliesn", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON><PERSON>sche Netherzieschl", "block.minecraft.cracked_polished_blackstone_bricks": "Riss’sche bolierde Schworzschdeenzieschl", "block.minecraft.cracked_stone_bricks": "<PERSON><PERSON><PERSON>sche Schdeenzieschl", "block.minecraft.crafter": "<PERSON><PERSON><PERSON>", "block.minecraft.crafting_table": "<PERSON><PERSON><PERSON>", "block.minecraft.creaking_heart": "Knarzherz", "block.minecraft.creeper_head": "Creeper<PERSON><PERSON>l", "block.minecraft.creeper_wall_head": "Creeper<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_door": "G<PERSON><PERSON>ndiere", "block.minecraft.crimson_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence_gate": "Garmesinzaundorr", "block.minecraft.crimson_fungus": "Garmesinbilz", "block.minecraft.crimson_hanging_sign": "Garmesinhänges<PERSON><PERSON><PERSON>", "block.minecraft.crimson_hyphae": "Garmesinhyphn", "block.minecraft.crimson_nylium": "Garmesin-Nädsl", "block.minecraft.crimson_planks": "Garmesinbreddorr", "block.minecraft.crimson_pressure_plate": "Garmesindruggbladde", "block.minecraft.crimson_roots": "Garmesinwurdsln", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "Garmesinschdufe", "block.minecraft.crimson_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_stem": "Garmesinschdiel", "block.minecraft.crimson_trapdoor": "Garmesinfalldiere", "block.minecraft.crimson_wall_hanging_sign": "Garmesin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_wall_sign": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crying_obsidian": "Ninglndorr Obsidian", "block.minecraft.cut_copper": "Geschniddnorr Gupforrblogg", "block.minecraft.cut_copper_slab": "Geschniddne Gupforrschdufe", "block.minecraft.cut_copper_stairs": "Geschni<PERSON><PERSON>", "block.minecraft.cut_red_sandstone": "Gesch<PERSON><PERSON><PERSON><PERSON> rod<PERSON><PERSON>", "block.minecraft.cut_red_sandstone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rode Sandschdeenschdufe", "block.minecraft.cut_sandstone": "Geschniddnorr Sands<PERSON>deen", "block.minecraft.cut_sandstone_slab": "Geschniddne Sandschdeenschdufe", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_candle_cake": "Guchn mid dürg<PERSON><PERSON>r <PERSON>", "block.minecraft.cyan_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> g<PERSON> Gera<PERSON>", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.damaged_anvil": "Beschädigdorr Ambuss", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "Schworzeischnhulzgnopp", "block.minecraft.dark_oak_door": "Schworzeischnhulzdiere", "block.minecraft.dark_oak_fence": "Schworzeisch<PERSON><PERSON>un", "block.minecraft.dark_oak_fence_gate": "Schworzeischnhulzzaundorr", "block.minecraft.dark_oak_hanging_sign": "Schworzeischnhulzhänges<PERSON>üld", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_log": "Schworzeischnschdamm", "block.minecraft.dark_oak_planks": "Schworzeischnhulzbreddorr", "block.minecraft.dark_oak_pressure_plate": "Schworzeischnhulzdruggbladde", "block.minecraft.dark_oak_sapling": "Schworzeischnseddsling", "block.minecraft.dark_oak_sign": "Schworzeisch<PERSON><PERSON><PERSON><PERSON><PERSON>ld", "block.minecraft.dark_oak_slab": "Schworzeischnhulzschdufe", "block.minecraft.dark_oak_stairs": "Schworzeischnhulzdrebbe", "block.minecraft.dark_oak_trapdoor": "Schworzeischnhulzfalldiere", "block.minecraft.dark_oak_wall_hanging_sign": "Schworzeischnhulzwandhängeschüld", "block.minecraft.dark_oak_wall_sign": "Schworzeischnhulzwandschüld", "block.minecraft.dark_oak_wood": "Schworzeischnhulz", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "Du<PERSON>", "block.minecraft.dark_prismarine_stairs": "Dungle <PERSON>", "block.minecraft.daylight_detector": "Daacheslischdsensor", "block.minecraft.dead_brain_coral": "Abgeschdorbne Hirngoralle", "block.minecraft.dead_brain_coral_block": "Abgeschdorb<PERSON><PERSON> Hirngorallnblogg", "block.minecraft.dead_brain_coral_fan": "Abgeschdorbnorr Hirngorallnfäschorr", "block.minecraft.dead_brain_coral_wall_fan": "Abgeschdorbnorr Hirngorallnwandfäschorr", "block.minecraft.dead_bubble_coral": "Abgeschdorbne Blasngoralle", "block.minecraft.dead_bubble_coral_block": "Abgeschdorbnorr Blasngorallnblogg", "block.minecraft.dead_bubble_coral_fan": "Abgeschdorbnorr Blasngorallnfäschorr", "block.minecraft.dead_bubble_coral_wall_fan": "Abgeschdorbnorr Blasngorallnwandfäschorr", "block.minecraft.dead_bush": "Dodorr Busch", "block.minecraft.dead_fire_coral": "Abgeschdorbne <PERSON>alle", "block.minecraft.dead_fire_coral_block": "Abgesch<PERSON><PERSON><PERSON><PERSON>gorallnblogg", "block.minecraft.dead_fire_coral_fan": "Abgeschdorb<PERSON><PERSON> Feuorrgorallnfäschorr", "block.minecraft.dead_fire_coral_wall_fan": "Abgeschdorb<PERSON><PERSON> Feuorrgorallnwandfäschorr", "block.minecraft.dead_horn_coral": "Abgeschdorbne Geweihgoralle", "block.minecraft.dead_horn_coral_block": "Abgeschdorbnorr Geweihgorallnblogg", "block.minecraft.dead_horn_coral_fan": "Abgeschdorbnorr Geweihgorallnfäschorr", "block.minecraft.dead_horn_coral_wall_fan": "Abgeschdorbnorr Geweihgorallnwandfäschorr", "block.minecraft.dead_tube_coral": "Abgeschdorbne Orschlgoralle", "block.minecraft.dead_tube_coral_block": "Abgeschdorbnorr Orschlgorallnblogg", "block.minecraft.dead_tube_coral_fan": "Abgeschdorbnorr Orschlgorallnfäschorr", "block.minecraft.dead_tube_coral_wall_fan": "Abgeschdorbnorr Orschlgorallnwandfäschorr", "block.minecraft.decorated_pot": "Vorzierder Grug", "block.minecraft.deepslate": "Diefnschieforr", "block.minecraft.deepslate_brick_slab": "Diefnschieforrzieschlschdufe", "block.minecraft.deepslate_brick_stairs": "Diefnschieforrzieschldrebbe", "block.minecraft.deepslate_brick_wall": "Diefnschieforrzieschlmauor", "block.minecraft.deepslate_bricks": "Diefnschieforrzieschl", "block.minecraft.deepslate_coal_ore": "Diefnschieforr-Schdeengohle", "block.minecraft.deepslate_copper_ore": "Diefnschieforr-Gupforrärds", "block.minecraft.deepslate_diamond_ore": "Diefnschieforr-Diamandärds", "block.minecraft.deepslate_emerald_ore": "Diefnschieforr-Smaragdärds", "block.minecraft.deepslate_gold_ore": "Diefnschieforr-Goldärds", "block.minecraft.deepslate_iron_ore": "Diefnschieforr-Eisnärds", "block.minecraft.deepslate_lapis_ore": "Diefnschieforr-Labisladsuliärds", "block.minecraft.deepslate_redstone_ore": "Diefnschieforr-Redstone-Ärds", "block.minecraft.deepslate_tile_slab": "Diefnschieforrfliesnschdufe", "block.minecraft.deepslate_tile_stairs": "Diefnschieforrfliesndrebbe", "block.minecraft.deepslate_tile_wall": "Diefnschieforrfliesnmauor", "block.minecraft.deepslate_tiles": "Diefnschieforrfliesn", "block.minecraft.detector_rail": "Sensorschiene", "block.minecraft.diamond_block": "Diamandblogg", "block.minecraft.diamond_ore": "Diamandärds", "block.minecraft.diorite": "Diorid", "block.minecraft.diorite_slab": "Dioridschdufe", "block.minecraft.diorite_stairs": "Di<PERSON>dd<PERSON><PERSON>", "block.minecraft.diorite_wall": "Dioridma<PERSON>r", "block.minecraft.dirt": "Ährde", "block.minecraft.dirt_path": "Dramblpfad", "block.minecraft.dispenser": "Werforr", "block.minecraft.dragon_egg": "Dr<PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "Drachn<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dried_ghast": "Ausgetrockneter Ghast", "block.minecraft.dried_kelp_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dripstone_block": "Drobbschdeenblogg", "block.minecraft.dropper": "Schbendorr", "block.minecraft.emerald_block": "Smaragdblogg", "block.minecraft.emerald_ore": "Smaragdärds", "block.minecraft.enchanting_table": "Zaubordisch", "block.minecraft.end_gateway": "Enddransidbordal", "block.minecraft.end_portal": "Endbordal", "block.minecraft.end_portal_frame": "Endbordalrahm", "block.minecraft.end_rod": "Endschdab", "block.minecraft.end_stone": "Endschdeen", "block.minecraft.end_stone_brick_slab": "Endschdeenzieschlschdufe", "block.minecraft.end_stone_brick_stairs": "Endschdeenzieschldrebbe", "block.minecraft.end_stone_brick_wall": "Endschdeenzieschlmauor", "block.minecraft.end_stone_bricks": "Endschdeenzieschl", "block.minecraft.ender_chest": "Enderdruhe", "block.minecraft.exposed_chiseled_copper": "<PERSON><PERSON><PERSON> gemeißelter <PERSON>", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON><PERSON> Gupforrblogg", "block.minecraft.exposed_copper_bulb": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_door": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_grate": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_cut_copper": "Blangorr geschniddnorr Gupforrblogg", "block.minecraft.exposed_cut_copper_slab": "Blange geschniddne Gupforrschdufe", "block.minecraft.exposed_cut_copper_stairs": "<PERSON>lang<PERSON> geschniddne Gupforrdrebbe", "block.minecraft.farmland": "Aggorbodn", "block.minecraft.fern": "Farn", "block.minecraft.fire": "<PERSON><PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_block": "Feuorrgorallnblogg", "block.minecraft.fire_coral_fan": "Feuorrgorallnfäschorr", "block.minecraft.fire_coral_wall_fan": "Feuorrgorallnwandfäschorr", "block.minecraft.firefly_bush": "Glühwürmchenbusch", "block.minecraft.fletching_table": "Bochnordisch", "block.minecraft.flower_pot": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea": "Blühnde Azalee", "block.minecraft.flowering_azalea_leaves": "Blühndes Azaleenlaub", "block.minecraft.frogspawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.frosted_ice": "Brüschiges Eis", "block.minecraft.furnace": "Ofn", "block.minecraft.gilded_blackstone": "Golddurschzochnorr Schworzschdeen", "block.minecraft.glass": "Glas", "block.minecraft.glass_pane": "Glasscheebe", "block.minecraft.glow_lichen": "Le<PERSON><PERSON>dflesch<PERSON>", "block.minecraft.glowstone": "Leuschdschdeen", "block.minecraft.gold_block": "Goldblogg", "block.minecraft.gold_ore": "Goldärds", "block.minecraft.granite": "<PERSON><PERSON>", "block.minecraft.granite_slab": "Granidschdufe", "block.minecraft.granite_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.granite_wall": "Granidmauor", "block.minecraft.grass": "Gras", "block.minecraft.grass_block": "Grasblogg", "block.minecraft.gravel": "<PERSON><PERSON>", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_candle_cake": "Guchn mid grauorr <PERSON>", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON><PERSON> glasierde <PERSON>", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.green_candle_cake": "Guchn mid grienorr G<PERSON>", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON>e glasierde G<PERSON>", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.grindstone": "Schleefschdeen", "block.minecraft.hanging_roots": "Hängewurdsln", "block.minecraft.hay_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.heavy_core": "Schwä<PERSON><PERSON>", "block.minecraft.heavy_weighted_pressure_plate": "Grobwäschebladde", "block.minecraft.honey_block": "Honischblogg", "block.minecraft.honeycomb_block": "Honischwabnblogg", "block.minecraft.hopper": "Drischdorr", "block.minecraft.horn_coral": "Geweihgoralle", "block.minecraft.horn_coral_block": "Geweihgorallnblogg", "block.minecraft.horn_coral_fan": "Geweihgorallnfäschorr", "block.minecraft.horn_coral_wall_fan": "Geweihgorallnwandfäschorr", "block.minecraft.ice": "<PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "Befallne gemeißelde Schdeenzieschl", "block.minecraft.infested_cobblestone": "Befallnorr Bruchschdeen", "block.minecraft.infested_cracked_stone_bricks": "Befall<PERSON>r riss’schorr <PERSON>l", "block.minecraft.infested_deepslate": "<PERSON><PERSON><PERSON><PERSON>chieforr", "block.minecraft.infested_mossy_stone_bricks": "<PERSON><PERSON><PERSON> bemo<PERSON>de Schdeenzieschl", "block.minecraft.infested_stone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.infested_stone_bricks": "Befallne Schdeenzieschl", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "Eisnblogg", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "Eisnärds", "block.minecraft.iron_trapdoor": "Eisnfalldiere", "block.minecraft.jack_o_lantern": "Gürbisladderne", "block.minecraft.jigsaw": "Vorrbundblogg", "block.minecraft.jukebox": "Bladdnschbielorr", "block.minecraft.jungle_button": "Drobnhulzgnopp", "block.minecraft.jungle_door": "Drobnhulzdiere", "block.minecraft.jungle_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_fence_gate": "Drobnhulzzaundorr", "block.minecraft.jungle_hanging_sign": "Drobnhulzhänges<PERSON><PERSON><PERSON>", "block.minecraft.jungle_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_log": "Drobnbohmschdamm", "block.minecraft.jungle_planks": "Drobnhulzbreddorr", "block.minecraft.jungle_pressure_plate": "Drobnhulzdruggbladde", "block.minecraft.jungle_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_slab": "Drobnhulzschdufe", "block.minecraft.jungle_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_trapdoor": "Drobnhulzfalldiere", "block.minecraft.jungle_wall_hanging_sign": "Drobnhulzwandhängeschüld", "block.minecraft.jungle_wall_sign": "Drobnhulzwandschüld", "block.minecraft.jungle_wood": "Drobnhulz", "block.minecraft.kelp": "Seedang", "block.minecraft.kelp_plant": "Seedangschdängl", "block.minecraft.ladder": "<PERSON><PERSON><PERSON>", "block.minecraft.lantern": "<PERSON><PERSON><PERSON>", "block.minecraft.lapis_block": "Labisladsuliblogg", "block.minecraft.lapis_ore": "Labisladsuliärds", "block.minecraft.large_amethyst_bud": "Große Amethystgnosbe", "block.minecraft.large_fern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Lavagessl", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lectern": "Lesebuld", "block.minecraft.lever": "<PERSON><PERSON><PERSON>", "block.minecraft.light": "Lischdblogg", "block.minecraft.light_blue_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_candle": "Hellblaue Gerdse", "block.minecraft.light_blue_candle_cake": "Guchn mid hellblauorr G<PERSON>se", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_blue_concrete_powder": "Hell<PERSON>uorr <PERSON>", "block.minecraft.light_blue_glazed_terracotta": "Hellblaue glasierde Geramigg", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON>ue <PERSON>-Gissde", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_stained_glass_pane": "Hellblaue Glasscheebe", "block.minecraft.light_blue_terracotta": "Hellblaue Geramigg", "block.minecraft.light_blue_wool": "Hellblaue Wolle", "block.minecraft.light_gray_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_candle_cake": "Guchn mid hellgrauorr <PERSON>", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_glazed_terracotta": "Hell<PERSON>ue glasierde Geramigg", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_terracotta": "<PERSON><PERSON><PERSON> Geramigg", "block.minecraft.light_gray_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.light_weighted_pressure_plate": "Feinwäschebladde", "block.minecraft.lightning_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lilac": "Fliedor", "block.minecraft.lily_of_the_valley": "Maiglöggschn", "block.minecraft.lily_pad": "Seerosnbladd", "block.minecraft.lime_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_bed": "Hellgrienes Bädde", "block.minecraft.lime_candle": "Hellgriene Gerd<PERSON>", "block.minecraft.lime_candle_cake": "<PERSON><PERSON>n mid hellgrienorr G<PERSON>", "block.minecraft.lime_carpet": "Hellgrien<PERSON><PERSON>", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_glazed_terracotta": "Hellgriene glasierde Geramigg", "block.minecraft.lime_shulker_box": "Hellg<PERSON><PERSON>-G<PERSON>de", "block.minecraft.lime_stained_glass": "Hellg<PERSON><PERSON>", "block.minecraft.lime_stained_glass_pane": "Hellgriene Glass<PERSON>ebe", "block.minecraft.lime_terracotta": "Hellgriene Geramigg", "block.minecraft.lime_wool": "Hellgrien<PERSON> W<PERSON>e", "block.minecraft.lodestone": "Leedschdeen", "block.minecraft.loom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_banner": "Magenda Bannorr", "block.minecraft.magenta_bed": "Magenda Bädde", "block.minecraft.magenta_candle": "Magenda Gerdse", "block.minecraft.magenta_candle_cake": "Guchn mid magenda Gerdse", "block.minecraft.magenta_carpet": "Magenda Täppsch", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete_powder": "Magenda Droggnbedong", "block.minecraft.magenta_glazed_terracotta": "Magenda glasierde Geramigg", "block.minecraft.magenta_shulker_box": "Magenda Shulker-Gissde", "block.minecraft.magenta_stained_glass": "Magenda Glas", "block.minecraft.magenta_stained_glass_pane": "Magenda Glasscheebe", "block.minecraft.magenta_terracotta": "Magenda Geramigg", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.magma_block": "Magmablogg", "block.minecraft.mangrove_button": "Mangrovnhulzgnopp", "block.minecraft.mangrove_door": "Mangrovnhulzdiere", "block.minecraft.mangrove_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_fence_gate": "Mangrovnhulzzaundorr", "block.minecraft.mangrove_hanging_sign": "Mangrovnhulzhängeschüld", "block.minecraft.mangrove_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_log": "Mangrovnschdamm", "block.minecraft.mangrove_planks": "Mangrovnhulzbreddorr", "block.minecraft.mangrove_pressure_plate": "Mangrovnhulzdruggbladde", "block.minecraft.mangrove_propagule": "Mangrovn-Ke<PERSON>ling", "block.minecraft.mangrove_roots": "Mangrovnwurdsln", "block.minecraft.mangrove_sign": "Man<PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_slab": "Mangrovnhulzschdufe", "block.minecraft.mangrove_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>zdre<PERSON>", "block.minecraft.mangrove_trapdoor": "Mangrovnhulzfalldiere", "block.minecraft.mangrove_wall_hanging_sign": "Mangrovnhulzwandhängeschüld", "block.minecraft.mangrove_wall_sign": "Mangrovnhulzwandschüld", "block.minecraft.mangrove_wood": "Mangrovnhulz", "block.minecraft.medium_amethyst_bud": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.melon": "<PERSON><PERSON>", "block.minecraft.melon_stem": "Melon<PERSON><PERSON><PERSON>", "block.minecraft.moss_block": "Moosblogg", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_cobblestone": "Bemoosdorr Bruchschdeen", "block.minecraft.mossy_cobblestone_slab": "Bemoosde Bruchschdeenschdufe", "block.minecraft.mossy_cobblestone_stairs": "Bemoosde Bruchschdeendrebbe", "block.minecraft.mossy_cobblestone_wall": "Bemoosde Bruchschdeenmauor", "block.minecraft.mossy_stone_brick_slab": "Bemoosde Schdeenzieschlschdufe", "block.minecraft.mossy_stone_brick_stairs": "Bemoosde Schdeenzieschldrebbe", "block.minecraft.mossy_stone_brick_wall": "Bemoosde Schdeenzieschlmauor", "block.minecraft.mossy_stone_bricks": "Bemoosde Schdeenzieschl", "block.minecraft.moving_piston": "Beweeschdorr Blogg", "block.minecraft.mud": "Schlamm", "block.minecraft.mud_brick_slab": "Schlammzieschlschdufe", "block.minecraft.mud_brick_stairs": "Schlammzieschldrebbe", "block.minecraft.mud_brick_wall": "Schlamm<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_bricks": "Schlammzieschl", "block.minecraft.muddy_mangrove_roots": "Schlammsche Mangrovnwurdsln", "block.minecraft.mushroom_stem": "Bilzschdiel", "block.minecraft.mycelium": "Mydsäl", "block.minecraft.nether_brick_fence": "Netherzieschlzaun", "block.minecraft.nether_brick_slab": "Netherzieschlschdufe", "block.minecraft.nether_brick_stairs": "Netherzieschldrebbe", "block.minecraft.nether_brick_wall": "Netherzieschlmauor", "block.minecraft.nether_bricks": "Netherzieschl", "block.minecraft.nether_gold_ore": "Nethergoldärds", "block.minecraft.nether_portal": "Netherbordal", "block.minecraft.nether_quartz_ore": "Netherquarzärds", "block.minecraft.nether_sprouts": "Netherschbrossn", "block.minecraft.nether_wart": "Netherwarzn", "block.minecraft.nether_wart_block": "Netherwarznblogg", "block.minecraft.netherite_block": "Nädderidblogg", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Noodnblogg", "block.minecraft.oak_button": "Eischnhulzgnopp", "block.minecraft.oak_door": "Eischnhulzdiere", "block.minecraft.oak_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_fence_gate": "Eischnhulzzaundorr", "block.minecraft.oak_hanging_sign": "Eischnhulzhänges<PERSON><PERSON><PERSON>", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_log": "Eischnschdamm", "block.minecraft.oak_planks": "Eischnhulzbreddorr", "block.minecraft.oak_pressure_plate": "Eischnhulzdruggbladde", "block.minecraft.oak_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>ling", "block.minecraft.oak_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_slab": "Eischnhulzschdufe", "block.minecraft.oak_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_trapdoor": "Eischnhulzfalldiere", "block.minecraft.oak_wall_hanging_sign": "Eischnhulzwandhängeschüld", "block.minecraft.oak_wall_sign": "Eischnhulzwandschüld", "block.minecraft.oak_wood": "Eischnh<PERSON>z", "block.minecraft.observer": "Beobachdor", "block.minecraft.obsidian": "Obsidian", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ominous_banner": "Unheilvolles Bannorr", "block.minecraft.open_eyeblossom": "Geöffnete Augenblüte", "block.minecraft.orange_banner": "Orangsches Bannorr", "block.minecraft.orange_bed": "Orangsches Bädde", "block.minecraft.orange_candle": "Orangsche Gerdse", "block.minecraft.orange_candle_cake": "Guchn mid orangschorr Gerd<PERSON>", "block.minecraft.orange_carpet": "Orangschnorr <PERSON>", "block.minecraft.orange_concrete": "Orang<PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_glazed_terracotta": "Orangsche glasierde Geramigg", "block.minecraft.orange_shulker_box": "Orangschne Shulker-Gissde", "block.minecraft.orange_stained_glass": "Orangsches Glas", "block.minecraft.orange_stained_glass_pane": "Orangsche Glasscheebe", "block.minecraft.orange_terracotta": "Orangschne Geramigg", "block.minecraft.orange_tulip": "Orangsche Dulbe", "block.minecraft.orange_wool": "Orangsche Wolle", "block.minecraft.oxeye_daisy": "Margoride", "block.minecraft.oxidized_chiseled_copper": "Oxidierter gemeißelter <PERSON>", "block.minecraft.oxidized_copper": "Oxidierdorr Gupforrblogg", "block.minecraft.oxidized_copper_bulb": "Oxidierde Gupforrleuchte", "block.minecraft.oxidized_copper_door": "Oxidierte Kupfertür", "block.minecraft.oxidized_copper_grate": "<PERSON>xi<PERSON><PERSON>", "block.minecraft.oxidized_copper_trapdoor": "Oxidierde Gupforrfalldiere", "block.minecraft.oxidized_cut_copper": "Oxidierdorr geschniddnorr Gupforrblogg", "block.minecraft.oxidized_cut_copper_slab": "Oxidierde geschniddne Gupforrblogg", "block.minecraft.oxidized_cut_copper_stairs": "Oxidierde geschniddne Gupforrdrebbe", "block.minecraft.packed_ice": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.packed_mud": "Fesdorr <PERSON>hl<PERSON>m", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON><PERSON> Blassmo<PERSON>", "block.minecraft.pale_moss_block": "Blassmoosblock", "block.minecraft.pale_moss_carpet": "<PERSON>lassmoostep<PERSON><PERSON>", "block.minecraft.pale_oak_button": "Blasseichenholzknopf", "block.minecraft.pale_oak_door": "Blasseichenholztür", "block.minecraft.pale_oak_fence": "Blasseichen<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_fence_gate": "Blasseichenholzzauntor", "block.minecraft.pale_oak_hanging_sign": "Blasseichenholzhängeschild", "block.minecraft.pale_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_log": "Blasseichenstamm", "block.minecraft.pale_oak_planks": "<PERSON><PERSON>ei<PERSON>hol<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_pressure_plate": "Blasseichenholzdruckplatte", "block.minecraft.pale_oak_sapling": "Blasseichensetzling", "block.minecraft.pale_oak_sign": "Blasseichenholzschild", "block.minecraft.pale_oak_slab": "Blasseichenholzstufe", "block.minecraft.pale_oak_stairs": "Blasseichenholztreppe", "block.minecraft.pale_oak_trapdoor": "Blasseichenholzfalltür", "block.minecraft.pale_oak_wall_hanging_sign": "Blasseichenholzwandhängeschild", "block.minecraft.pale_oak_wall_sign": "Blasseichenhol<PERSON><PERSON><PERSON>child", "block.minecraft.pale_oak_wood": "Blasseichenholz", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.peony": "Pfingsdrose", "block.minecraft.petrified_oak_slab": "Vorschdeinorde <PERSON>", "block.minecraft.piglin_head": "Pig<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.piglin_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_banner": "<PERSON><PERSON>", "block.minecraft.pink_bed": "<PERSON><PERSON>", "block.minecraft.pink_candle": "<PERSON><PERSON>", "block.minecraft.pink_candle_cake": "<PERSON><PERSON>n mid pingorr <PERSON>", "block.minecraft.pink_carpet": "<PERSON>", "block.minecraft.pink_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON> glasierde Gera<PERSON>", "block.minecraft.pink_petals": "<PERSON>", "block.minecraft.pink_shulker_box": "<PERSON><PERSON>", "block.minecraft.pink_stained_glass": "<PERSON><PERSON>", "block.minecraft.pink_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.pink_terracotta": "<PERSON><PERSON>", "block.minecraft.pink_tulip": "<PERSON><PERSON>", "block.minecraft.pink_wool": "<PERSON><PERSON>", "block.minecraft.piston": "Golbn", "block.minecraft.piston_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pitcher_crop": "Gannenfrucht", "block.minecraft.pitcher_plant": "Gannenpflanze", "block.minecraft.player_head": "Schbielor<PERSON><PERSON><PERSON>", "block.minecraft.player_head.named": "Nischl von %s", "block.minecraft.player_wall_head": "Schbielorr<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON><PERSON><PERSON> Dr<PERSON>deen", "block.minecraft.polished_andesite": "Bolierdorr Andesid", "block.minecraft.polished_andesite_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_basalt": "Bolierdorr Basald", "block.minecraft.polished_blackstone": "Bolierdorr Schworzschdeen", "block.minecraft.polished_blackstone_brick_slab": "Bolierde Schworzschdeenzieschlschdufe", "block.minecraft.polished_blackstone_brick_stairs": "Bolier<PERSON>rzschdeenzieschldrebbe", "block.minecraft.polished_blackstone_brick_wall": "Bolier<PERSON>rzschdeenzieschlmauor", "block.minecraft.polished_blackstone_bricks": "Bolierde <PERSON>rzschdeenzieschl", "block.minecraft.polished_blackstone_button": "Bolierdorr Schworzschdeengnopp", "block.minecraft.polished_blackstone_pressure_plate": "Bolierde Schworzschdeendruggbladde", "block.minecraft.polished_blackstone_slab": "Bolierde <PERSON>rzschdeenschdufe", "block.minecraft.polished_blackstone_stairs": "<PERSON><PERSON><PERSON>endre<PERSON>", "block.minecraft.polished_blackstone_wall": "Bolier<PERSON>rzschdeenmauor", "block.minecraft.polished_deepslate": "Bolierdorr Diefnschieforr", "block.minecraft.polished_deepslate_slab": "Bolierde <PERSON>orrschdufe", "block.minecraft.polished_deepslate_stairs": "<PERSON><PERSON><PERSON>rdre<PERSON>", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON><PERSON>orrmauo<PERSON>", "block.minecraft.polished_diorite": "Bolierdorr Diorid", "block.minecraft.polished_diorite_slab": "Bolierde <PERSON>", "block.minecraft.polished_diorite_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_granite": "Bolierdorr <PERSON>", "block.minecraft.polished_granite_slab": "Bolier<PERSON>", "block.minecraft.polished_granite_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff": "Polierdorr <PERSON>", "block.minecraft.polished_tuff_slab": "Polierte Tuffsteenschdufe", "block.minecraft.polished_tuff_stairs": "Polierde <PERSON>", "block.minecraft.polished_tuff_wall": "Polierde <PERSON>", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "Gardoffln", "block.minecraft.potted_acacia_sapling": "Eengedobbdorr Agazienseddsling", "block.minecraft.potted_allium": "Eengedobbdorr <PERSON>lauch", "block.minecraft.potted_azalea_bush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_azure_bluet": "Eengedobbdes Porzellanschdernschn", "block.minecraft.potted_bamboo": "Eengedobbdorr Bambus", "block.minecraft.potted_birch_sapling": "Eengedobbdorr Bürschnseddsling", "block.minecraft.potted_blue_orchid": "Eengedobb<PERSON> blaue Orchi<PERSON>e", "block.minecraft.potted_brown_mushroom": "Eengedobb<PERSON><PERSON>", "block.minecraft.potted_cactus": "Eengedobb<PERSON><PERSON>", "block.minecraft.potted_cherry_sapling": "Eingedopfor Girschsetzling", "block.minecraft.potted_closed_eyeblossom": "Eingetopfte geschlossene Augenblüte", "block.minecraft.potted_cornflower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_crimson_fungus": "Eengedobbdorr Garmesinbilz", "block.minecraft.potted_crimson_roots": "Eengedobbde Garmesinwurdsln", "block.minecraft.potted_dandelion": "<PERSON><PERSON>ed<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_dark_oak_sapling": "Eengedobbdorr Schworzeischnseddsling", "block.minecraft.potted_dead_bush": "Eengedobbdorr dodorr Busch", "block.minecraft.potted_fern": "Eengedobbdorr Farn", "block.minecraft.potted_flowering_azalea_bush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> blühnde Azalee", "block.minecraft.potted_jungle_sapling": "Eengedobbdorr <PERSON>", "block.minecraft.potted_lily_of_the_valley": "Eengedobb<PERSON>öggschn", "block.minecraft.potted_mangrove_propagule": "Eengedobbdorr Mangrovn-Ke<PERSON>ling", "block.minecraft.potted_oak_sapling": "Eengedobbdorr E<PERSON>nseddsling", "block.minecraft.potted_open_eyeblossom": "Eingetopfte geöffnete Augenblüte", "block.minecraft.potted_orange_tulip": "Eengedobbde orangsche Dulbe", "block.minecraft.potted_oxeye_daisy": "Eengedobbde <PERSON>", "block.minecraft.potted_pale_oak_sapling": "Eingetopfter Blasseichensetzling", "block.minecraft.potted_pink_tulip": "Eengedobb<PERSON> pinge <PERSON>", "block.minecraft.potted_poppy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_red_mushroom": "Eengedobbdorr rodorr <PERSON>", "block.minecraft.potted_red_tulip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rode <PERSON><PERSON><PERSON>", "block.minecraft.potted_spruce_sapling": "Eengedobbdorr Fi<PERSON>ling", "block.minecraft.potted_torchflower": "Eingedopfte Fackllilie", "block.minecraft.potted_warped_fungus": "Eengedobbdorr Würrbilz", "block.minecraft.potted_warped_roots": "Eengedobbde <PERSON>", "block.minecraft.potted_white_tulip": "Eengedobbde weeße Dulbe", "block.minecraft.potted_wither_rose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.powder_snow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.powder_snow_cauldron": "Bulvorrschneegessl", "block.minecraft.powered_rail": "Andriebsschiene", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Brismarinzieschlschdufe", "block.minecraft.prismarine_brick_stairs": "Brismarinzieschldrebbe", "block.minecraft.prismarine_bricks": "Brismarinzieschl", "block.minecraft.prismarine_slab": "Brismarinschdufe", "block.minecraft.prismarine_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_banner": "<PERSON>", "block.minecraft.purple_bed": "<PERSON>", "block.minecraft.purple_candle": "<PERSON>", "block.minecraft.purple_candle_cake": "Guchn mid lila <PERSON>", "block.minecraft.purple_carpet": "<PERSON>", "block.minecraft.purple_concrete": "<PERSON>", "block.minecraft.purple_concrete_powder": "<PERSON>", "block.minecraft.purple_glazed_terracotta": "<PERSON>", "block.minecraft.purple_shulker_box": "<PERSON>", "block.minecraft.purple_stained_glass": "<PERSON>", "block.minecraft.purple_stained_glass_pane": "<PERSON>", "block.minecraft.purple_terracotta": "<PERSON>", "block.minecraft.purple_wool": "<PERSON>", "block.minecraft.purpur_block": "Purpurblogg", "block.minecraft.purpur_pillar": "Purpursäule", "block.minecraft.purpur_slab": "Purpurschdufe", "block.minecraft.purpur_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_block": "Quarzblogg", "block.minecraft.quartz_bricks": "Quarz<PERSON><PERSON>l", "block.minecraft.quartz_pillar": "Quarzsäule", "block.minecraft.quartz_slab": "Q<PERSON>rzsch<PERSON><PERSON>", "block.minecraft.quartz_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.rail": "<PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "Rohgupforrblogg", "block.minecraft.raw_gold_block": "Roh<PERSON>blogg", "block.minecraft.raw_iron_block": "Roheisnblogg", "block.minecraft.red_banner": "<PERSON><PERSON>", "block.minecraft.red_bed": "<PERSON><PERSON>", "block.minecraft.red_candle": "<PERSON><PERSON>", "block.minecraft.red_candle_cake": "<PERSON><PERSON><PERSON> mid rodorr <PERSON>", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.red_glazed_terracotta": "Rode glasierde Geramigg", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON>", "block.minecraft.red_mushroom_block": "<PERSON><PERSON><PERSON>", "block.minecraft.red_nether_brick_slab": "Rode Netherzieschlschdufe", "block.minecraft.red_nether_brick_stairs": "Rode Netherzieschltrebbe", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON> Netherzieschlmauor", "block.minecraft.red_nether_bricks": "Rode Netherzieschl", "block.minecraft.red_sand": "<PERSON><PERSON><PERSON> Sand", "block.minecraft.red_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "<PERSON><PERSON>", "block.minecraft.red_sandstone_stairs": "<PERSON><PERSON>", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON>", "block.minecraft.red_shulker_box": "<PERSON><PERSON>-<PERSON>", "block.minecraft.red_stained_glass": "<PERSON><PERSON>", "block.minecraft.red_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.red_terracotta": "<PERSON><PERSON> Geramigg", "block.minecraft.red_tulip": "<PERSON><PERSON>", "block.minecraft.red_wool": "<PERSON><PERSON>", "block.minecraft.redstone_block": "Redstone-Blogg", "block.minecraft.redstone_lamp": "Redstone-Funsl", "block.minecraft.redstone_ore": "Redstone-Ärds", "block.minecraft.redstone_torch": "Redstone-Faggl", "block.minecraft.redstone_wall_torch": "Redstone-Wandfaggl", "block.minecraft.redstone_wire": "Redstone-Leidung", "block.minecraft.reinforced_deepslate": "Vorrschdärgdorr Diefnschieforr", "block.minecraft.repeater": "Redstone-Vorschdärgor", "block.minecraft.repeating_command_block": "Wiedorrhol-Befählsblogg", "block.minecraft.resin_block": "Harzblock", "block.minecraft.resin_brick_slab": "Harzziegelstufe", "block.minecraft.resin_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_clump": "Harz<PERSON>lump<PERSON>", "block.minecraft.respawn_anchor": "Seelnangorr", "block.minecraft.rooted_dirt": "Wurdslährde", "block.minecraft.rose_bush": "Rosnschdrauch", "block.minecraft.sand": "Sand", "block.minecraft.sandstone": "Sandschdeen", "block.minecraft.sandstone_slab": "Sandschdeenschdufe", "block.minecraft.sandstone_stairs": "Sandschdeendrebbe", "block.minecraft.sandstone_wall": "Sandschdeenmauor", "block.minecraft.scaffolding": "Gerüschd", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk-Gadalysadorr", "block.minecraft.sculk_sensor": "Sculk-Sensor", "block.minecraft.sculk_shrieker": "Sculk-Greischorr", "block.minecraft.sculk_vein": "Sculk-Adorr", "block.minecraft.sea_lantern": "Seeladderne", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Wiedorreinschdiegsbungd gesäddsd", "block.minecraft.short_dry_grass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.short_grass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shroomlight": "Bilzlischd", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "Schgeleddnischl", "block.minecraft.skeleton_wall_skull": "Schgeledd-Wandnischl", "block.minecraft.slime_block": "Schleimblogg", "block.minecraft.small_amethyst_bud": "<PERSON><PERSON><PERSON>", "block.minecraft.small_dripleaf": "<PERSON><PERSON><PERSON>", "block.minecraft.smithing_table": "Schmiededisch", "block.minecraft.smoker": "Räuschoroofn", "block.minecraft.smooth_basalt": "Gladdorr Basald", "block.minecraft.smooth_quartz": "Gladdorr <PERSON>ua<PERSON>g", "block.minecraft.smooth_quartz_slab": "Gladde Qua<PERSON>du<PERSON>", "block.minecraft.smooth_quartz_stairs": "<PERSON><PERSON>", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON><PERSON> rod<PERSON>r <PERSON>", "block.minecraft.smooth_red_sandstone_slab": "<PERSON><PERSON> rodde <PERSON>", "block.minecraft.smooth_red_sandstone_stairs": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.smooth_sandstone": "Gladdorr Sandschdeen", "block.minecraft.smooth_sandstone_slab": "Gladde Sands<PERSON>deenschdufe", "block.minecraft.smooth_sandstone_stairs": "<PERSON><PERSON>", "block.minecraft.smooth_stone": "Gladdorr Schdeen", "block.minecraft.smooth_stone_slab": "Gladde Schdeenschdufe", "block.minecraft.sniffer_egg": "Schnüfflor-Ei", "block.minecraft.snow": "Sc<PERSON><PERSON>", "block.minecraft.snow_block": "Sc<PERSON><PERSON>blogg", "block.minecraft.soul_campfire": "Seelnlagorfeuorr", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_lantern": "Seelnladderne", "block.minecraft.soul_sand": "Seelnsand", "block.minecraft.soul_soil": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_torch": "Seelnfaggl", "block.minecraft.soul_wall_torch": "Seelnwandfaggl", "block.minecraft.spawn.not_valid": "<PERSON> hasd kee <PERSON>de un keen uffgeladnen Seelnangorr, odorr dei Wiedorreenschdiegsbungd wurde bloggierd", "block.minecraft.spawner": "Schbornorr", "block.minecraft.spawner.desc1": "<PERSON><PERSON>ze Spawn-Ei:", "block.minecraft.spawner.desc2": "Lescht Greadurendyp fest", "block.minecraft.sponge": "<PERSON><PERSON><PERSON>", "block.minecraft.spore_blossom": "Schbornblüde", "block.minecraft.spruce_button": "Fischdnhulzgnopp", "block.minecraft.spruce_door": "Fischdnhulzdiere", "block.minecraft.spruce_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence_gate": "Fischdnhulzzaundorr", "block.minecraft.spruce_hanging_sign": "Fischdnhulzh<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_leaves": "Fischdnnadln", "block.minecraft.spruce_log": "Fischdnschdamm", "block.minecraft.spruce_planks": "Fischdnhulzbreddorr", "block.minecraft.spruce_pressure_plate": "Fischdnhulzdruggbladde", "block.minecraft.spruce_sapling": "Fi<PERSON>dnseddsling", "block.minecraft.spruce_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_slab": "Fischdnhulzschdufe", "block.minecraft.spruce_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_trapdoor": "Fischdnhulzfalldiere", "block.minecraft.spruce_wall_hanging_sign": "Fischdnhulzwandhänges<PERSON>üld", "block.minecraft.spruce_wall_sign": "Fischdnhulzwandschüld", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sticky_piston": "Gläbrigorr Golbn", "block.minecraft.stone": "Schdeen", "block.minecraft.stone_brick_slab": "Schdeenzieschlschdufe", "block.minecraft.stone_brick_stairs": "Schdeenzieschldrebbe", "block.minecraft.stone_brick_wall": "Schdeenzieschlmauor", "block.minecraft.stone_bricks": "Schdeenzieschl", "block.minecraft.stone_button": "Schdeengnopp", "block.minecraft.stone_pressure_plate": "Schdeendruggbladde", "block.minecraft.stone_slab": "Schdeenschdufe", "block.minecraft.stone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stonecutter": "Schdeensäsche", "block.minecraft.stripped_acacia_log": "<PERSON><PERSON><PERSON><PERSON>r Agazienschdamm", "block.minecraft.stripped_acacia_wood": "Endrindedes Agazie<PERSON>ulz", "block.minecraft.stripped_bamboo_block": "Geschäldor Bambusblock", "block.minecraft.stripped_birch_log": "Endrindedorr Bürschnschdamm", "block.minecraft.stripped_birch_wood": "Endrindedes Bürschnhulz", "block.minecraft.stripped_cherry_log": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_crimson_hyphae": "Geschälde Garmesinhyphn", "block.minecraft.stripped_crimson_stem": "Gesch<PERSON><PERSON><PERSON> Garm<PERSON>nschdiel", "block.minecraft.stripped_dark_oak_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>eischnschdamm", "block.minecraft.stripped_dark_oak_wood": "Endrindedes Schworzeischnhulz", "block.minecraft.stripped_jungle_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_jungle_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_mangrove_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_oak_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_pale_oak_log": "Entrindeter Blasseichenstamm", "block.minecraft.stripped_pale_oak_wood": "Entrindetes Blasseichenholz", "block.minecraft.stripped_spruce_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_warped_hyphae": "Geschälde Würrhyphn", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.structure_block": "Gonsdrugtschoonsblogg", "block.minecraft.structure_void": "Gonsdrugtschoonsleere", "block.minecraft.sugar_cane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sunflower": "Sonnenblume", "block.minecraft.suspicious_gravel": "Seltsamor Gies", "block.minecraft.suspicious_sand": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON><PERSON>beerschdrauch", "block.minecraft.tall_dry_grass": "<PERSON><PERSON> Trockengras", "block.minecraft.tall_grass": "Hohes Gras", "block.minecraft.tall_seagrass": "<PERSON><PERSON> Seegras", "block.minecraft.target": "Zielblogg", "block.minecraft.terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.test_block": "Testblock", "block.minecraft.test_instance_block": "Testinstanzblock", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT‐Explosionen sind deaktiviert", "block.minecraft.torch": "Faggl", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "Fackllilienpflanze", "block.minecraft.trapped_chest": "Redstone-<PERSON><PERSON><PERSON>", "block.minecraft.trial_spawner": "Prüfungs-Spawnorr", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "<PERSON><PERSON>", "block.minecraft.tube_coral": "Orschlgoralle", "block.minecraft.tube_coral_block": "Orschlgorallnblogg", "block.minecraft.tube_coral_fan": "Orschlgorallnfäschorr", "block.minecraft.tube_coral_wall_fan": "Orschlgorallnwandfäschorr", "block.minecraft.tuff": "Tuffschdeen", "block.minecraft.tuff_brick_slab": "Tuffsteenzieschlschdufe", "block.minecraft.tuff_brick_stairs": "Tuffsteenzieschldrebbe", "block.minecraft.tuff_brick_wall": "<PERSON>ffs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_bricks": "Tuffsteenzieschl", "block.minecraft.tuff_slab": "Tuffsteenschdufe", "block.minecraft.tuff_stairs": "<PERSON>ffs<PERSON><PERSON><PERSON>", "block.minecraft.tuff_wall": "<PERSON>ffsteenma<PERSON>rr", "block.minecraft.turtle_egg": "Schüldgrödnei", "block.minecraft.twisting_vines": "Zwürblrangn", "block.minecraft.twisting_vines_plant": "Zwürblrangnpflanze", "block.minecraft.vault": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.vine": "<PERSON><PERSON><PERSON>", "block.minecraft.void_air": "Leernlufd", "block.minecraft.wall_torch": "Wandfaggl", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fence_gate": "<PERSON><PERSON><PERSON><PERSON>undorr", "block.minecraft.warped_fungus": "W<PERSON><PERSON><PERSON>z", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_hyphae": "W<PERSON>rrhyph<PERSON>", "block.minecraft.warped_nylium": "Würr-Nä<PERSON>l", "block.minecraft.warped_planks": "Würrbreddorr", "block.minecraft.warped_pressure_plate": "W<PERSON>rrdruggbladde", "block.minecraft.warped_roots": "Würrwurdsln", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wart_block": "Würrwarznblogg", "block.minecraft.water": "Was<PERSON><PERSON>", "block.minecraft.water_cauldron": "Wassorrgessl", "block.minecraft.waxed_chiseled_copper": "Gewachsdorr gemeißeldorr Gupforrblogg", "block.minecraft.waxed_copper_block": "Gewachsdorr Gupforrblogg", "block.minecraft.waxed_copper_bulb": "Gewachsde Gupforrleuchde", "block.minecraft.waxed_copper_door": "Gewachsde Gupforrdiere", "block.minecraft.waxed_copper_grate": "Gewachsdorr G<PERSON>t", "block.minecraft.waxed_copper_trapdoor": "Gewachsde Gupforrfalldiere", "block.minecraft.waxed_cut_copper": "Gewachsdorr geschniddnorr Gupforrblogg", "block.minecraft.waxed_cut_copper_slab": "Gewachsde geschniddne Gupforrschdufe", "block.minecraft.waxed_cut_copper_stairs": "Gewachsde geschniddne Gupforrdrebbe", "block.minecraft.waxed_exposed_chiseled_copper": "Gewachsdorr blangorr gemeißeldorr Gupferblogg", "block.minecraft.waxed_exposed_copper": "Gewachsdorr blangorr <PERSON>blogg", "block.minecraft.waxed_exposed_copper_bulb": "Gewachsde blange Gupferleuchde", "block.minecraft.waxed_exposed_copper_door": "Gewachsde blange Gupferdiere", "block.minecraft.waxed_exposed_copper_grate": "Gewachsdorr b<PERSON><PERSON><PERSON>", "block.minecraft.waxed_exposed_copper_trapdoor": "Gewachsde blange Gupforrfalldiere", "block.minecraft.waxed_exposed_cut_copper": "Gewachsdorr blangorr geschniddnorr Gupforrblogg", "block.minecraft.waxed_exposed_cut_copper_slab": "Gewachsde blange geschniddne Gupforrschdufe", "block.minecraft.waxed_exposed_cut_copper_stairs": "Gewachsde blange geschniddne Gupforrdrebbe", "block.minecraft.waxed_oxidized_chiseled_copper": "Gewachsdorr oxidierdorr gemeißeldorr Gupferblogg", "block.minecraft.waxed_oxidized_copper": "Gewachsdorr oxidierdorr Gupforrblogg", "block.minecraft.waxed_oxidized_copper_bulb": "Gewachsde oxidierde Gupferleude", "block.minecraft.waxed_oxidized_copper_door": "Gewachsde oxidierde Gupferdiere", "block.minecraft.waxed_oxidized_copper_grate": "Gewachdorr oxidier<PERSON><PERSON>", "block.minecraft.waxed_oxidized_copper_trapdoor": "Gewachsde oxidierde Gupforrfalldiere", "block.minecraft.waxed_oxidized_cut_copper": "Gewachsdorr oxidierdorr geschniddnorr G<PERSON>forrblogg", "block.minecraft.waxed_oxidized_cut_copper_slab": "Gewachsde oxidierde geschniddne Gupforrschdufe", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Gewachsde oxidierde geschniddne Gupforrdrebbe", "block.minecraft.waxed_weathered_chiseled_copper": "Gewachsdorr vorrwiddorrdorr gemeißeldorr Gupferblogg", "block.minecraft.waxed_weathered_copper": "Gewachsdorr vorrwiddorrdorr Gupforrblogg", "block.minecraft.waxed_weathered_copper_bulb": "Gewachde vorrwiddorrde Gupferleuchde", "block.minecraft.waxed_weathered_copper_door": "Gewachsde vorrwiddorrde Gupferdiere", "block.minecraft.waxed_weathered_copper_grate": "Gewachsdorr vorrwiddorrdorr Gupferrost", "block.minecraft.waxed_weathered_copper_trapdoor": "Gewachsde vorrwiddorrde Gupforrfalldiere", "block.minecraft.waxed_weathered_cut_copper": "Gewachsdorr vorrwiddorrdorr geschniddnorr Gupforrblogg", "block.minecraft.waxed_weathered_cut_copper_slab": "Gewachsde vorrwiddorrde geschniddne Gupforrschdufe", "block.minecraft.waxed_weathered_cut_copper_stairs": "Gewachsde vorrwiddorrde geschniddne Gupforrdrebbe", "block.minecraft.weathered_chiseled_copper": "Vorrwiddorrdorr gemeißeldorr Gupferblogg", "block.minecraft.weathered_copper": "<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON>blogg", "block.minecraft.weathered_copper_bulb": "Vorrwidd<PERSON>rde Gupferleuchde", "block.minecraft.weathered_copper_door": "<PERSON>orr<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_grate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_trapdoor": "<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON> Gupforrfalldiere", "block.minecraft.weathered_cut_copper": "Vorrwidd<PERSON><PERSON>rr geschniddnorr Gupforrblogg", "block.minecraft.weathered_cut_copper_slab": "Vorrwiddorrde geschniddne Gupforrschdufe", "block.minecraft.weathered_cut_copper_stairs": "Vorrwidd<PERSON>rde geschniddne Gupforrdrebbe", "block.minecraft.weeping_vines": "Drauorrrangn", "block.minecraft.weeping_vines_plant": "Drauorrrangnpflanze", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON>", "block.minecraft.wheat": "Weidsnpflanze", "block.minecraft.white_banner": "Weeßes Bannorr", "block.minecraft.white_bed": "Weeßes Bädde", "block.minecraft.white_candle": "Weeße Gerdse", "block.minecraft.white_candle_cake": "Guchn mid weeßorr G<PERSON>", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_glazed_terracotta": "Weeße glasierde Geramigg", "block.minecraft.white_shulker_box": "Weeße Shulker-Gissde", "block.minecraft.white_stained_glass": "Weeßes Glas", "block.minecraft.white_stained_glass_pane": "Weeße Glasscheebe", "block.minecraft.white_terracotta": "Weeße Geramigg", "block.minecraft.white_tulip": "Weeße Dulbe", "block.minecraft.white_wool": "Weeße Wolle", "block.minecraft.wildflowers": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Witherschgeleddnischl", "block.minecraft.wither_skeleton_wall_skull": "Witherschgeledd-<PERSON><PERSON><PERSON>l", "block.minecraft.yellow_banner": "<PERSON><PERSON><PERSON> Bannorr", "block.minecraft.yellow_bed": "Gelbes Bädde", "block.minecraft.yellow_candle": "Gelbe Gerdse", "block.minecraft.yellow_candle_cake": "Guchn mid gelborr Gerdse", "block.minecraft.yellow_carpet": "Gelborr <PERSON>", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "Gelbe glasierde Geramigg", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON>-Gissde", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass_pane": "Gelbe Glasscheebe", "block.minecraft.yellow_terracotta": "Gelbe Geramigg", "block.minecraft.yellow_wool": "<PERSON><PERSON><PERSON> W<PERSON>e", "block.minecraft.zombie_head": "Zombienischl", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "book.byAuthor": "von %1$s", "book.edit.title": "Buchbearbeitungsfenster", "book.editTitle": "Schwahrdndidl eingähm:", "book.finalizeButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> signiern", "book.finalizeWarning": "Hinwees! Wen<PERSON> de die Schwahrde signiersd, kann se nisch mähr bearbeided wärdn.", "book.generation.0": "Orischinal", "book.generation.1": "Gobie des Orischinals", "book.generation.2": "Gobie e<PERSON><PERSON>", "book.generation.3": "Zorrissn", "book.invalid.tag": "*Ungüldsche Schwahrdendadn*", "book.pageIndicator": "Seide %1$s von %2$s", "book.page_button.next": "Nächste Seite", "book.page_button.previous": "Vorherige Seite", "book.sign.title": "Buchsignierfenster", "book.sign.titlebox": "Titel", "book.signButton": "Signiern", "book.view.title": "Buchansichtsfenster", "build.tooHigh": "De maggsimale Ba<PERSON> bedrägd %s", "chat.cannotSend": "Chat-Na<PERSON>rischd kunnde nisch gesended wärdn", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Gligge zum Delebordiern", "chat.copy": "Ling gobiern", "chat.copy.click": "<PERSON><PERSON><PERSON><PERSON>, um in de Zwischnablache zu gobiern", "chat.deleted_marker": "Diese dschättnachricht wurde vom Sörvor <PERSON>d.", "chat.disabled.chain_broken": "<PERSON><PERSON> wurde ofgrund ennor undorbrochenen gette deagdiviert. Bid<PERSON> versuche, dich orneut zu vorbindn.", "chat.disabled.expiredProfileKey": "Dor schätt worde ofgrund ennes abgeloofenen öffendlichn Profilschlüssl deagdiviert. <PERSON>te vorsuch, disch orneut zu vorbindn.", "chat.disabled.invalid_command_signature": "<PERSON><PERSON> hatte unorwardede oder fehlnde Befehlargumend-Signaduren.", "chat.disabled.invalid_signature": "<PERSON><PERSON> <PERSON><PERSON> hadde eene ungüldige Signadur. Bidde versuch dich neu zu vorrbindn.", "chat.disabled.launcher": "Dor schätt wurde durch Launschor-Einschdellung deagdiviert. Nachricht kann nisch gesended werdn.", "chat.disabled.missingProfileKey": "Dor schätt worde ofgrund ennes abgeloofenen öffendlichn Profilschlüssl deagdiviert. <PERSON>te vorsuch, disch orneut zu vorbindn.", "chat.disabled.options": "<PERSON>r schätt wurde indn Client-Einschdellungen deagdivierd.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON> <PERSON><PERSON> worrde in falschorr Reinfolge empfangen. Had sich deene Sysdemzeed vorränderd?", "chat.disabled.profile": "<PERSON><PERSON> sch<PERSON> is durch gontoeinschdellungen nisch erlaubd. Drügge %s orneut, um weidore Informatschionen zu orhaltn.", "chat.disabled.profile.moreInfo": "Chat is dursch Gondoeenschdellungn nisch erloobd. Nachrischdn könn nisch gesended odorr angezeischd wärdn.", "chat.editBox": "Cha<PERSON>", "chat.filtered": "Gefildort vom Sörvor.", "chat.filtered_full": "<PERSON><PERSON> had deine Nachrischd für einige Schbielorr ausgeblended.", "chat.link.confirm": "<PERSON><PERSON><PERSON> sic<PERSON>, dass de die folgende Webseide öffnen möschdesd?", "chat.link.confirmTrusted": "Willsde den Ling öffnen odorr in de Zwischnablache gobiern?", "chat.link.open": "<PERSON><PERSON>", "chat.link.warning": "<PERSON><PERSON><PERSON> keene <PERSON>, denen du ni vor<PERSON>d!", "chat.queue": "[+%s ausschdehnde Zeiln]", "chat.square_brackets": "[%s]", "chat.tag.error": "<PERSON><PERSON> had eene ungüldige Nachrichd gesended.", "chat.tag.modified": "Die Nachricht wurde vom Sörvor vorännort. Original:", "chat.tag.not_secure": "Nicht üborbrüfte Nachricht. Kann nisch gemelded werdn.", "chat.tag.system": "Sörvornachricht. Kann nisch gemelded werdn.", "chat.tag.system_single_player": "Sörvorrna<PERSON>rischd.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s had de Uffgabe %s gebaggd", "chat.type.advancement.goal": "%s hads Ziel %s erreichd", "chat.type.advancement.task": "%s hadn <PERSON>d %s gemachd", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Team benachrischdschn", "chat.type.team.sent": "-> %s<%s>%s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s sagd %s", "chat.validation_error": "Chatüborprüfungsfehlorr", "chat_screen.message": "<PERSON>u sendende Nachrischd: %s", "chat_screen.title": "Chatfensdorr", "chat_screen.usage": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>ähm un zum Sendn de Eengabedasde drüggn", "chunk.toast.checkLog": "Sieh Ausgabeprodogoll für Eenzelheeten", "chunk.toast.loadFailure": "Chunk bei %s gonnde nich geladn wärdn", "chunk.toast.lowDiskSpace": "<PERSON><PERSON> freeorr Speichorblatz!", "chunk.toast.lowDiskSpace.description": "De Weld gann under Umständen nich gespeichord wärdn.", "chunk.toast.saveFailure": "Chunk bei %s gonnde nich gespeichorrd wärdn", "clear.failed.multiple": "Bei %s Schbielorrn wurdn keene Geschnschdände gefundn", "clear.failed.single": "Bei %s wurdn keene Geschnschdände gefundn", "color.minecraft.black": "Schworz", "color.minecraft.blue": "Blau", "color.minecraft.brown": "<PERSON>", "color.minecraft.cyan": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.gray": "G<PERSON><PERSON>", "color.minecraft.green": "Grien", "color.minecraft.light_blue": "Hellblau", "color.minecraft.light_gray": "Hellgrau", "color.minecraft.lime": "Hellgrien", "color.minecraft.magenta": "Magenda", "color.minecraft.orange": "Orangsch", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "<PERSON>", "color.minecraft.red": "Rod", "color.minecraft.white": "<PERSON><PERSON><PERSON>", "color.minecraft.yellow": "<PERSON><PERSON><PERSON>", "command.context.here": "<--[HIER]", "command.context.parse_error": "%s an Bositschoon %s: %s", "command.exception": "Befähl fählorrhafd: %s", "command.expected.separator": "<PERSON><PERSON><PERSON><PERSON><PERSON> vorr neum Argumend erwarded", "command.failed": "<PERSON><PERSON><PERSON><PERSON> dorr Ausführung dieses Befähls is ee Fählorr uffgedrädn", "command.forkLimit": "Maximale Anzahl an Befehlsrahm (%s) erreichd", "command.unknown.argument": "Ungüldsches Befählsargumend", "command.unknown.command": "Unbeganndorr odorr unvollschdändschorr Befähl, siehe undn für Fählorr", "commands.advancement.criterionNotFound": "Griderium ‚%2$s‘ gehörd nisch zum Fordschridd %1$s", "commands.advancement.grant.criterion.to.many.failure": "Griderium ‚%s‘ des Fordschridds %s kunnde %s Schbielorrn nisch gewährd wärdn, da es bereids erfülld is", "commands.advancement.grant.criterion.to.many.success": "Griderium ‚%s‘ des Fordschridds %s wurde %s Schbielorrn gewährd", "commands.advancement.grant.criterion.to.one.failure": "Griderium ‚%s‘ des Fordschridds %s kunnde %s nisch gewährd wärdn, da es bereids erfülld is", "commands.advancement.grant.criterion.to.one.success": "Griderium ‚%s‘ des Fordschridds %s wurde %s gewährd", "commands.advancement.grant.many.to.many.failure": "%s Fordschridde konndn %s Schbielorrn nisch gewährd wärdn, da sie bereids erreischd wurdn", "commands.advancement.grant.many.to.many.success": "%s Fordschridde wurden %s Schbielorrn gewährd", "commands.advancement.grant.many.to.one.failure": "%s Fordschridde konndn %s nisch gewährd wärdn, da sie bereids erreischd wurdn", "commands.advancement.grant.many.to.one.success": "%s Fordschridde wurdn %s gewährd", "commands.advancement.grant.one.to.many.failure": "Fordschridd %s kunnde %s Schbielorrn nisch gewährd wärdn, da er bereids erreischd wurde", "commands.advancement.grant.one.to.many.success": "Fordschridd %s wurde %s Schbielorrn gewährd", "commands.advancement.grant.one.to.one.failure": "Fordschridd %s kunnde %s nisch gewährd wärdn, da er bereids erreischd wurde", "commands.advancement.grant.one.to.one.success": "Fordschridd %s wurde %s gewährd", "commands.advancement.revoke.criterion.to.many.failure": "Griderium ‚%s‘ des Fordschridds %s kunnde %s Schbielorrn nisch endzogn wärdn, da es nisch erfülld war", "commands.advancement.revoke.criterion.to.many.success": "Griderium ‚%s‘ des Fordschridds %s wurde %s Schbielorrn endzogn", "commands.advancement.revoke.criterion.to.one.failure": "Griderium ‚%s‘ des Fordschridds %s kunnde %s nisch endzogn wärdn, da es nisch erfülld war", "commands.advancement.revoke.criterion.to.one.success": "Griderium ‚%s‘ des Fordschridds %s wurde %s endzogn", "commands.advancement.revoke.many.to.many.failure": "%s Fordschridde konndn %s Schbielorrn nisch endzogn wärdn, da sie nisch erreischd wurdn", "commands.advancement.revoke.many.to.many.success": "%s Fordschridde wurdn %s Schbielorrn endzogn", "commands.advancement.revoke.many.to.one.failure": "%s Fordschridde konndn %s nisch endzogn wärdn, da sie nisch erreischd wurdn", "commands.advancement.revoke.many.to.one.success": "%s Fordschridde wurdn %s endzogn", "commands.advancement.revoke.one.to.many.failure": "Fordschridd %s kunnde %s Schbielorrn nisch endzogn wärdn, da er nisch erreischd wurde", "commands.advancement.revoke.one.to.many.success": "Fordschridd %s wurde %s Schbielorrn endzogn", "commands.advancement.revoke.one.to.one.failure": "Fordschridd %s kunnde %s nisch endzogn wärdn, da er nisch erreischd wurde", "commands.advancement.revoke.one.to.one.success": "Fordschridd %s wurde %s endzogn", "commands.attribute.base_value.get.success": "Grundwerd des Addribuds %s des Objegds %s is %s", "commands.attribute.base_value.reset.success": "Grundwert für das Attribut %s von Objekt %s wurde auf den Standardwert %s zurückgesetzt", "commands.attribute.base_value.set.success": "Grundwerd des Addribuds %s des Objegds %s wurde uff %s gesäddsd", "commands.attribute.failed.entity": "%s is keen güldsches Objegd für diesn Befähl", "commands.attribute.failed.modifier_already_present": "Modifigadorr %s is bei Addribud %s des Objegds %s bereids vorhandn", "commands.attribute.failed.no_attribute": "Objegd %s had keen Addribud %s", "commands.attribute.failed.no_modifier": "Addribud %s des Objekds %s had keen Modifigadorr %s", "commands.attribute.modifier.add.success": "Modifigadorr %s wurde zum Addribud %s des Objegds %s hinzugefüschd", "commands.attribute.modifier.remove.success": "Modifigadorr %s wur<PERSON> von Addribud %s des Objegds %s endfernd", "commands.attribute.modifier.value.get.success": "Werd des Modifigadorrs %s des Addribuds %s des Objegds %s is %s", "commands.attribute.value.get.success": "Werd des Addribuds %s des Objegds %s is %s", "commands.ban.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, dorr <PERSON>r is bereids geschbärrd", "commands.ban.success": "%s wurde geschbärrd: %s", "commands.banip.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, de IP-Adresse is bereids geschbärrd", "commands.banip.info": "Diese <PERSON>re bedriffd %s Schbielorr: %s", "commands.banip.invalid": "Ungüldsche IP-Adresse odorr unbeganndorr Schbielorr", "commands.banip.success": "IP-Adresse %s wurde geschbärrd: %s", "commands.banlist.entry": "%s wurde von %s geschbärrd: %s", "commands.banlist.entry.unknown": "(unbegannd)", "commands.banlist.list": "Es gibd %s Schbärrungen:", "commands.banlist.none": "<PERSON><PERSON> gibd keene Schbärrungen", "commands.bossbar.create.failed": "<PERSON><PERSON>de mid dorr ID ‚%s‘ eggsischdierd bereids", "commands.bossbar.create.success": "Benuddsorrdefinierde Bossleisde %s wurde erschdelld", "commands.bossbar.get.max": "Dorr Maggs<PERSON>lwerd dorr benuddsorrdefinierdn Bossleisde %s bedrägd %s", "commands.bossbar.get.players.none": "Benuddsorrdefinierde Bossleisde %s had aggduell keene Schbielorr online", "commands.bossbar.get.players.some": "Benuddsordefinierde Bossleisde %s had aggduell %s Schbielorr onlein: %s", "commands.bossbar.get.value": "Dorr aggduelle Werd dorr benuddsorrdefinierdn Bossleisde %s bedrägd %s", "commands.bossbar.get.visible.hidden": "Benuddsorrdefinierde Bossleisde %s is aggduell unsischdbar", "commands.bossbar.get.visible.visible": "Benuddsorrdefinierde Bossleisde %s is aggduell sischdbar", "commands.bossbar.list.bars.none": "Es sin keene benuddsorrdefinierdn Bossleisdn aggdiv", "commands.bossbar.list.bars.some": "Es sin %s benuddsordefinierde Bossleisdn aggdiv: %s", "commands.bossbar.remove.success": "Benuddsorrdefinierde Bossleisde %s wurde endfernd", "commands.bossbar.set.color.success": "Farbe dorr benuddsorrdefinierdn Bossleisde %s wurde geändorrd", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, de <PERSON><PERSON><PERSON> had bereids diese <PERSON>be", "commands.bossbar.set.max.success": "Maggsimalwerd dorr benuddsorrdefinierdn Bossleisde %s wurde uff %s geändorrd", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, de <PERSON><PERSON><PERSON> had bereids dieses Maximum", "commands.bossbar.set.name.success": "Benuddsorrdefinierde Bossleisde %s wurde umbenannd", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, <PERSON> <PERSON><PERSON> had bereids diesn Nam", "commands.bossbar.set.players.success.none": "Benuddsorrdefinierde Bossleisde %s had keene Schbielorr mähr", "commands.bossbar.set.players.success.some": "Benuddsorrdefinierde Bossleisde %s had aggduell %s Schbielorr: %s", "commands.bossbar.set.players.unchanged": "<PERSON>ü<PERSON><PERSON> hads’sch geändorrd, diese Schbielorr sin bereids dorr Bossleisde zugeordned, es wurde niemand hinzugefüschd odorr endfernd", "commands.bossbar.set.style.success": "Eindeilung dorr benuddsorrdefinierdn Bossleisde %s wurde geändorrd", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, <PERSON> <PERSON><PERSON><PERSON> had bereids diese Eindeilung", "commands.bossbar.set.value.success": "Werd dorr benuddsorrdefinierdn Bossleisde %s wurde uff %s geändorrd", "commands.bossbar.set.value.unchanged": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, de <PERSON><PERSON> had bereids diesn Werd", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, de Bossleisde is bereids unsischdbar", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändo<PERSON>d, de Bossleisde is bereids sischdbar", "commands.bossbar.set.visible.success.hidden": "Benuddsorrdefinierde Bossleisde %s is nu unsischdbar", "commands.bossbar.set.visible.success.visible": "Benuddsorrdefinierde Bossleisde %s is nu sischdbar", "commands.bossbar.unknown": "Es eggsischdierd keene Bossleisde mid dorr ID ‚%s‘", "commands.clear.success.multiple": "%s Gegenschdände wurdn von %s Schbielorrn endfernd", "commands.clear.success.single": "%s Gegenschdände wurdn von %s endfernd", "commands.clear.test.multiple": "%s üboreinschdimmende Gegenschdände wurdn bei %s Schbielorrn gefundn", "commands.clear.test.single": "%s üboreinschdimmende gegenschdände wurdn bei %s gefundn", "commands.clone.failed": "<PERSON><PERSON> wurdn keene Blögge gobierd", "commands.clone.overlap": "Quell- un Zielbereisch dürfn sisch nisch üborrschneidn", "commands.clone.success": "%s Blögge wurdn erfolgreisch gobierd", "commands.clone.toobig": "Zu viele Blögge im angegäbnen Bereisch (maggsimal %s, angegäbn %s)", "commands.damage.invulnerable": "<PERSON><PERSON> kann dursch den angegebenen Schadensdyp nisch vorletzt werdn", "commands.damage.success": "%s <PERSON><PERSON><PERSON> wurde off %s angewand", "commands.data.block.get": "%s von Blogg bei (%s, %s, %s) muldiblizierd mid %s is %s", "commands.data.block.invalid": "<PERSON><PERSON>b<PERSON>g had keene Bloggdadn", "commands.data.block.modified": "Bloggdadn bei (%s, %s, %s) wurdn geändorrd", "commands.data.block.query": "Blogg bei (%s, %s, %s) besiddsd die folschendn Bloggdadn: %s", "commands.data.entity.get": "%s von %s muldiblizierd mid %s is %s", "commands.data.entity.invalid": "Schbielorrdadn könn nisch geändorrd wärdn", "commands.data.entity.modified": "Objegddadn von %s wurdn geändorrd", "commands.data.entity.query": "%s besiddsd die folschenden Objegddadn: %s", "commands.data.get.invalid": "‚%s‘ kann nisch abgefrachd wä<PERSON><PERSON>, de Eischnschafd is keen<PERSON>", "commands.data.get.multiple": "Dieses Argumend aggzebdierd nur een eenslnen NBT-Werd", "commands.data.get.unknown": "‚%s‘ kann nisch abgefragd wärdn, die Eischnschafd eggsischdierd nisch", "commands.data.merge.failed": "Nüschd had si<PERSON> g<PERSON><PERSON>, de angegäbenen Eischnschafdn habn bereids diese Werde", "commands.data.modify.expected_list": "<PERSON><PERSON><PERSON> erwarded, ‚%s‘ erhaldn", "commands.data.modify.expected_object": "<PERSON><PERSON><PERSON><PERSON><PERSON> erwarded, ‚%s‘ erhaldn", "commands.data.modify.expected_value": "Werd erworded, %s orhaldn", "commands.data.modify.invalid_index": "Ungüldschorr Lisdnindeggs: %s", "commands.data.modify.invalid_substring": "Ungüldige Abschniddsindizes: %s bis %s", "commands.data.storage.get": "%s im Schbeischorr %s mid %s muldiblizierd is %s", "commands.data.storage.modified": "Schbeischorr %s wurde geändorrd", "commands.data.storage.query": "Schbeischorr %s benuddsd de folgndn Dadn: %s", "commands.datapack.create.already_exists": "Ein Datenpaket mit dem Namen ‚%s‘ existiert bereits", "commands.datapack.create.invalid_full_name": "Ungültiger Name ‚%s‘ für neues Datenpaket", "commands.datapack.create.invalid_name": "Der Datenpaketname ‚%s‘ enthält ungültige Zeichen", "commands.datapack.create.io_failure": "Datenpaket ‚%s‘ konnte nicht erstellt werden; siehe <PERSON>", "commands.datapack.create.metadata_encode_failure": "Metadaten für Datenpaket ‚%s‘ konnten nicht kodiert werden: %s", "commands.datapack.create.success": "Neues leeres Datenpaket ‚%s‘ wurde erstellt", "commands.datapack.disable.failed": "Dadnbaged ‚%s‘ is bereids deaggdivierd", "commands.datapack.disable.failed.feature": "<PERSON><PERSON>bageed ‚%s‘ gann nich deagdi<PERSON> wärdn, da es Beschdanddeil eenes agdivn Umschaltorrs is!", "commands.datapack.enable.failed": "Dadnbaged ‚%s‘ is bereids aggdivierd", "commands.datapack.enable.failed.no_flags": "Das Dadnbaked '%s* kann nisch agdivierd werdn, weil die benödigden Umschaldor für diese Weld nisch agdivierd sin: %s!", "commands.datapack.list.available.none": "<PERSON>s sin keene weidoren <PERSON> vorfügboar", "commands.datapack.list.available.success": "Es sin %s Dadnbagede vorfügbor: %s", "commands.datapack.list.enabled.none": "Es sin keene Dad<PERSON> aggdivierd", "commands.datapack.list.enabled.success": "Es sin %s Dadnbagede aggdivierd: %s", "commands.datapack.modify.disable": "Deaggdiviere Dadnbaged %s", "commands.datapack.modify.enable": "Aggdiviere Dadnbaged %s", "commands.datapack.unknown": "<PERSON>be<PERSON><PERSON>d ‚%s‘", "commands.debug.alreadyRunning": "Tigg-Uffzeischnung wurde bereids geschdarded", "commands.debug.function.noRecursion": "Es kann nisch aus e<PERSON>rr Fungtschoon heraus uffgezeischned wärdn", "commands.debug.function.noReturnRun": "Abloofufzeichnung gann nich mid „return run“ vorrwended wärdn", "commands.debug.function.success.multiple": "%s Befähle von %s Fungtschonen wurdn in de Ausgabedadei %s offgezeischned", "commands.debug.function.success.single": "%s Befähle dor Fungtschoon '%s' wurdn in de Ausgabedadei %s offgezeischned", "commands.debug.function.traceFailed": "Fungtschoon konnde nisch uffgezeischned wärdn", "commands.debug.notRunning": "Tigg-Uffzeischnung is nisch aggdiv", "commands.debug.started": "Tigg-Uffzeischnung wurde geschdarded", "commands.debug.stopped": "Tigg-Uffzeischnung wurde nach %s Segundn un %s <PERSON><PERSON> geschdobbd (%s <PERSON><PERSON> de Segunde)", "commands.defaultgamemode.success": "Schdandardschbielmodus wurde uff %s gesäddsd", "commands.deop.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, dorr <PERSON> is bere<PERSON> keen Oberadorr", "commands.deop.success": "%s is keen <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>hr", "commands.dialog.clear.multiple": "Dialog wird für %s Spieler geschlossen", "commands.dialog.clear.single": "Dialog wird für %s geschlossen", "commands.dialog.show.multiple": "Dialog wird %s Spielern angezeigt", "commands.dialog.show.single": "Dialog wird %s angezeigt", "commands.difficulty.failure": "Nüschd had sisch geändorrd, de Schwierischkeed liegd bereids bei %s", "commands.difficulty.query": "De Schwierischkeed is %s", "commands.difficulty.success": "Schwierischkeed wurde uff %s gesäddsd", "commands.drop.no_held_items": "Obje<PERSON>d kann keene Geschnschdände trachn", "commands.drop.no_loot_table": "Objegd %s had keene <PERSON>", "commands.drop.no_loot_table.block": "Block %s hat keine Be<PERSON>belle", "commands.drop.success.multiple": "%s Geschnschdände falln gelassn", "commands.drop.success.multiple_with_table": "%s Geschnschdände aus dorr Beudedabelle %s falln gelassn", "commands.drop.success.single": "%s %s falln gelassn", "commands.drop.success.single_with_table": "%s %s aus Beudedabelle %s falln gelassn", "commands.effect.clear.everything.failed": "<PERSON><PERSON> besiddsd keene zu endfernenden Effegde", "commands.effect.clear.everything.success.multiple": "Alle Effegde wurdn von %s Zieln endfernd", "commands.effect.clear.everything.success.single": "Alle Effegde wurdn von %s endfernd", "commands.effect.clear.specific.failed": "<PERSON>iel besiddsd den zu endfernenden Effegd nisch", "commands.effect.clear.specific.success.multiple": "Effegd %s wurde von %s Zieln endfernd", "commands.effect.clear.specific.success.single": "Effegd %s wurde von %s endfernd", "commands.effect.give.failed": "Effegd kunnde nisch angewand wärdn (das Ziel is endwedorr resisdend odorr besiddsd eene schdärgre Schdufe)", "commands.effect.give.success.multiple": "Effegd %s wurde uff %s Ziele angewand", "commands.effect.give.success.single": "Effegd %s wurde uff %s angewand", "commands.enchant.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, de Z<PERSON>e haldn endwedorr keen Geschnschdand in ihrorr Haubdhand odorr de Vorzauborung kunnde nisch angewended wärdn", "commands.enchant.failed.entity": "%s is keen güldsches Objegd für diesn Befähl", "commands.enchant.failed.incompatible": "%s kann damid nisch vorzaubord wärdn", "commands.enchant.failed.itemless": "%s häld keen Geschnschdand in dorr Haubdhand", "commands.enchant.failed.level": "Schdufe %s is zu hoch, de maggsimale Sch<PERSON><PERSON> diesorr Vorzauborung bedräschd %s", "commands.enchant.success.multiple": "Vorzauborung %s wurde uff %s Objegde angewended", "commands.enchant.success.single": "Vorzauborung %s wurde uff den Geschnschdand von %s angewended", "commands.execute.blocks.toobig": "Zu viele Blögge im angegäbnen Bereisch (maggsimal %s, angegäbn %s)", "commands.execute.conditional.fail": "Dest fählgeschlachn", "commands.execute.conditional.fail_count": "Dest fählgeschlachn, Anzahl: %s", "commands.execute.conditional.pass": "<PERSON><PERSON> er<PERSON>", "commands.execute.conditional.pass_count": "<PERSON><PERSON>, <PERSON><PERSON>hl: %s", "commands.execute.function.instantiationFailure": "Fungtion ‚%s‘ gonnde nich ausgewerded wärdn: %s", "commands.experience.add.levels.success.multiple": "%s Erfahrungsschdufn wurdn an %s Schbielorr gegäbn", "commands.experience.add.levels.success.single": "%s Erfahrungsschdufn wurdn an %s gegäbn", "commands.experience.add.points.success.multiple": "%s Erfahrungsbungde wurdn an %s Schbielorr gegäbn", "commands.experience.add.points.success.single": "%s Erfahrungsbungde wurdn an %s gegäbn", "commands.experience.query.levels": "%s had Erfahrungsschdufe %s", "commands.experience.query.points": "%s had %s Erfahrungsbungde", "commands.experience.set.levels.success.multiple": "Erfahrungsschdufn von %2$s Schbielorrn wurdn uff %1$s gesäddsd", "commands.experience.set.levels.success.single": "Erfahrungsschdufe von %2$s wurde uff %1$s gesäddsd", "commands.experience.set.points.invalid": "Erfahrungsbungde dürfn nisch heehorr gesäddsd wärdn als das Maggsimum dorr aggduelln Erfahrungsschdufe des Schbielorrs", "commands.experience.set.points.success.multiple": "Erfahrungsbungde von %2$s Schbielorrn wurdn uff %1$s gesäddsd", "commands.experience.set.points.success.single": "Erfahrungsbungde von %2$s wurdn uff %1$s gesäddsd", "commands.fill.failed": "<PERSON><PERSON> wurdn keene Blögge bladdsierd", "commands.fill.success": "%s Blögge wurdn erfolgreisch bladdsierd", "commands.fill.toobig": "Zu viele Blögge im angegäbnen Bereisch (maggsimal %s, angegäbn %s)", "commands.fillbiome.success": "Biome zwischn %s, %s, %s und %s, %s, %s festgelescht", "commands.fillbiome.success.count": "%s Biom-Eindräge zwischn %s, %s, %s, und %s, %s, %s festgelescht", "commands.fillbiome.toobig": "Zu viele Blögge im angegebenenn Bereisch (maggsimal %s, angegebn %s)", "commands.forceload.added.failure": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, diese <PERSON> wärdn bereits dauorrhafd geladn", "commands.forceload.added.multiple": "%s Chungs in %s von %s bis %s wärdn nu dauorrhafd geladn", "commands.forceload.added.none": "In %s wärdn keene Chungs dauorrhafd geladn", "commands.forceload.added.single": "Chung %s in %s würd nu dauorrhafd geladn", "commands.forceload.list.multiple": "In %2$s wärdn %1$s Chungs dauorrhafd geladn: %s", "commands.forceload.list.single": "In %s würd ee Chung dauorrhafd geladn: %s", "commands.forceload.query.failure": "Chung %s in %s würd nisch dauorrhafd geladn", "commands.forceload.query.success": "Chung %s in %s würd da<PERSON>rr<PERSON><PERSON>d geladn", "commands.forceload.removed.all": "In %s wärdn nun keene Chungs mähr dauorrhafd geladn", "commands.forceload.removed.failure": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, diese <PERSON> wurdn ooch bisher nisch dauorrhafd geladn", "commands.forceload.removed.multiple": "%s Chung<PERSON> in %s von %s bis %s wärdn nu nisch mähr dauorrhafd geladn", "commands.forceload.removed.single": "Chung %s in %s würd nu nisch mähr dauorrhafd geladn", "commands.forceload.toobig": "Zu viele Chungs im angegäbnen Bereisch (maggsimal %s, angegäbn %s)", "commands.function.error.argument_not_compound": "Ungüldigorr Argumenddüb: ‚%s‘ ist geen Vorrbund", "commands.function.error.missing_argument": "Fehlndes Argumend ‚%2$s‘ für de Fungtion ‚%1$s‘", "commands.function.error.missing_arguments": "Fehlende Argumente für die Funktion ‚%s‘", "commands.function.error.parse": "Bei der Auswertung des Makros ‚%s‘ hat der Befehl ‚%s‘ einen Fehler verursacht: %s", "commands.function.instantiationFailure": "Funktion ‚%s‘ konnte nicht ausgewertet werden: %s", "commands.function.result": "Funktion ‚%s‘ lieferte %s zurück", "commands.function.scheduled.multiple": "Funktionen (%s) werden ausgeführt", "commands.function.scheduled.no_functions": "Es wurde keine Funktion mit dem Namen ‚%s‘ gefunden", "commands.function.scheduled.single": "Funktion ‚%s‘ wird ausgeführt", "commands.function.success.multiple": "%s Befehl(e) von %s Funktionen wurde(n) ausgeführt", "commands.function.success.multiple.result": "%s Funktionen ausgeführt", "commands.function.success.single": "%s Be<PERSON>hl(e) der Funktion ‚%s‘ wurde(n) ausgeführt", "commands.function.success.single.result": "Funktion ‚%2$s‘ lieferte %1$s zurück", "commands.gamemode.success.other": "<PERSON><PERSON> von %s wurde uff %s gesäddsd", "commands.gamemode.success.self": "<PERSON><PERSON>lmodus wurde uff %s gesäddsd", "commands.gamerule.query": "De Schbielreeschl %s is derzeid ‚%s‘", "commands.gamerule.set": "Schbielreschl %s wurde uff ‚%s‘ gesäddsd", "commands.give.failed.toomanyitems": "Kann nisch mähr als %s %s gäbn", "commands.give.success.multiple": "%s %s an %s Schbielorr gegäbn", "commands.give.success.single": "%s %s an %s gegäbn", "commands.help.failed": "Unbeganndorr Befähl odorr unzureeschnde Bereschdigung", "commands.item.block.set.success": "Inhald eenes Invendarbladdses bei (%s, %s, %s) wurde dursch %s ersäddsd", "commands.item.entity.set.success.multiple": "Inhald eenes Invendarbladdses von %s Objegdn wurde dursch %s ersäddsd", "commands.item.entity.set.success.single": "Inhald eenes Invendarbladdses von %s wurde dursch %s ersäddsd", "commands.item.source.no_such_slot": "Quelle besiddsd Invendarbladds %s nisch", "commands.item.source.not_a_container": "Quellbositschoon %s, %s, %s besiddsd keen Invendar", "commands.item.target.no_changed.known_item": "Keen Ziel lässt %s bei Invendarbladds %s zu", "commands.item.target.no_changes": "Keen Ziel lässd Geschnschdände bei Invendarbladds %s zu", "commands.item.target.no_such_slot": "Ziel besiddsd Invendarbladds %s nisch", "commands.item.target.not_a_container": "Zielbositschoon %s, %s, %s besiddsd keen Invendar", "commands.jfr.dump.failed": "Fähl<PERSON>r beim <PERSON>rn dorr JFR-Uffzeischnung: %s", "commands.jfr.start.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> beim <PERSON> dorr JFR-Uffzeischnung", "commands.jfr.started": "JFR-Uffzeischnung geschdarded", "commands.jfr.stopped": "JFR-Uffzeischnung geschdobbd un in %s geschbeischorrd", "commands.kick.owner.failed": "Der Betreiber eines LAN‐Spiels kann nicht hinausgeworfen werden", "commands.kick.singleplayer.failed": "In nicht freigegebenen Einzelspielen kann nicht hinausgeworfen werden", "commands.kick.success": "%s wurde rausgeworfn: %s", "commands.kill.success.multiple": "%s Objegde wurdn beseidschd", "commands.kill.success.single": "%s wurde beseidschd", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Es sin %s von maggsimal %s Schbielorn online: %s", "commands.locate.biome.not_found": "<PERSON>s kunnde in angemessnorr Endfernung kee Biom des Dyps „%s“ gefundn wärdn", "commands.locate.biome.success": "%s is am näschsdn bei %s (%s Blögge endfernd)", "commands.locate.poi.not_found": "Es konnde in angemessnorr Endfernung kee Zielbungd des Dyps „%s“ gefundn wärdn", "commands.locate.poi.success": "Zielbungd %s is am näschsdn bei %s (%s Blögge endfernd)", "commands.locate.structure.invalid": "<PERSON><PERSON> gibd keene Schdrugdur des Dyps „%s“", "commands.locate.structure.not_found": "Es konnde keene Schdrugdur des Dyps „%s“ in dorr Nähe gefundn wärdn", "commands.locate.structure.success": "%s is am näschsdn bei %s (%s Blögge endfernd)", "commands.message.display.incoming": "%s fl<PERSON><PERSON><PERSON>d dir zu: %s", "commands.message.display.outgoing": "Du flüsdorsd %s zu: %s", "commands.op.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, dorr <PERSON>r is bereids een Oberadorr", "commands.op.success": "%s wurde zum Sörvorroberadorr ernannd", "commands.pardon.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, dorr <PERSON>r is nisch geschbärrd", "commands.pardon.success": "%s wurde <PERSON>b<PERSON>d", "commands.pardonip.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, diese IP-Adresse is nisch geschbärrd", "commands.pardonip.invalid": "Ungüldsche IP-Adresse", "commands.pardonip.success": "IP-Adresse %s wurde endschbärrd", "commands.particle.failed": "Bardiggl war für niemandn sischdbar", "commands.particle.success": "Bardiggl %s würd angezeischd", "commands.perf.alreadyRunning": "Leisdungsuffzeischnung wurde bereids geschdarded", "commands.perf.notRunning": "Leisdungsuffzeischnung is nisch aggdiv", "commands.perf.reportFailed": "Debug-Berischd kunnde nisch erschdelld wärdn", "commands.perf.reportSaved": "Debug-Berischd wurde erschdelld in %s", "commands.perf.started": "10-seg<PERSON>ndi<PERSON> Leisdungsuffzeischnung wurde geschdarded (benuddse ‚/debug stop‘ um se vorze<PERSON>ch zu beendn)", "commands.perf.stopped": "Leistungsaufzeichnung wurde nach %s Sekunde(n) und %s Tick(s) beendet (%s Tick(s) pro Sekunde)", "commands.place.feature.failed": "Merschmal konnde ni bladdsierd wärdn", "commands.place.feature.invalid": "<PERSON>s gibd kee Merschmal des Dyps „%s“", "commands.place.feature.success": "„%s“ bei %s, %s, %s bladdsierd", "commands.place.jigsaw.failed": "Vorrbund konnde nisch genorrierd wärdn", "commands.place.jigsaw.invalid": "<PERSON><PERSON> gibd keene Vorlaachnquelle des Dyps „%s“", "commands.place.jigsaw.success": "Vorrbund wurde bei %s, %s, %s genorrierd", "commands.place.structure.failed": "Gonsdrugtschoon konnde nisch bladdsierd wärdn", "commands.place.structure.invalid": "<PERSON><PERSON> gibd keene Schdrugdur des Dyps „%s“", "commands.place.structure.success": "Gonsdrugtschoon „%s“ wurde bei %s, %s, %s genorrierd", "commands.place.template.failed": "Vorlaache konnde nisch bladdsierd wärdn", "commands.place.template.invalid": "Es eggsischdierd keene V<PERSON>ache mid dorr ID „%s“", "commands.place.template.success": "Vorlaache „%s“ bei %s, %s, %s geladn", "commands.playsound.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> is zu weid endfernd, um gehörd zu wärdn", "commands.playsound.success.multiple": "Geräusch %s wurde für %s Schbielorr abgeschbield", "commands.playsound.success.single": "Geräusch %s wurde für %s abgeschbield", "commands.publish.alreadyPublished": "LAN-Schbiel is bereids undorr Bord %s erreischboar", "commands.publish.failed": "<PERSON><PERSON> kunnde keen LAN-Schbiel erschdelld wärdn", "commands.publish.started": "LAN-Schbiel undorr Bord %s erreischboar", "commands.publish.success": "LAN-Schbiel is jäddse undorr Bord %s erreischboar", "commands.random.error.range_too_large": "Zahlenbereich der Zufallszahl darf höchstens 2147483646 Werte umfassen", "commands.random.error.range_too_small": "Zahlenbereich der Zufallszahl muss mindestens 2 Werte umfassen", "commands.random.reset.all.success": "%s Zufallsfolge(n) wurde(n) zurückgesetzt", "commands.random.reset.success": "Zufallsfolge ‚%s‘ wurde zurückgesetzt", "commands.random.roll": "%s hat %s gewürfelt (zwischen %s und %s einschließlich)", "commands.random.sample.success": "Zufallswert: %s", "commands.recipe.give.failed": "<PERSON><PERSON> wurdn keene neun Rezebde freigeschalded", "commands.recipe.give.success.multiple": "%s Rezebde wurdn für %s Schbielorr freigeschalded", "commands.recipe.give.success.single": "%s Rezebde wurdn für %s freigeschalded", "commands.recipe.take.failed": "<PERSON>s konndn keene Rezebde endfernd wärdn", "commands.recipe.take.success.multiple": "%s Rezebde wurdn von %s Schbielorrn endfernd", "commands.recipe.take.success.single": "%s Rezebde wurdn von %s endfernd", "commands.reload.failure": "Neuladn fählgeschlachn, alde Dadn wärdn beibehaldn", "commands.reload.success": "<PERSON><PERSON><PERSON><PERSON> wärdn neu geladn", "commands.ride.already_riding": "%s ist bereits auf %s aufgestiegen", "commands.ride.dismount.success": "%s ist von %s abgestiegen", "commands.ride.mount.failure.cant_ride_players": "<PERSON><PERSON> kann nichts aufsteigen", "commands.ride.mount.failure.generic": "%s konnte nicht auf %s aufsteigen", "commands.ride.mount.failure.loop": "Kann das Objekt nicht auf sich selbst oder einen seiner Passagiere aufsteigen lassen", "commands.ride.mount.failure.wrong_dimension": "<PERSON><PERSON> in anderer Dimension kann nicht aufgestiegen werden", "commands.ride.mount.success": "%s ist auf %s aufgestiegen", "commands.ride.not_riding": "%s ist auf kein Fahrmittel aufgestiegen", "commands.rotate.success": "%s wurde gedreht", "commands.save.alreadyOff": "Audomatsches Schbeischorrn is bereids deaggdivierd", "commands.save.alreadyOn": "Audomatsches Schbeischorrn is bereids aggdivierd", "commands.save.disabled": "Audomatsches Schbeischorrn is jä<PERSON><PERSON> deaggdivierd", "commands.save.enabled": "Audomatsches Schbeischorrn is jäddse aggdivierd", "commands.save.failed": "Schbiel kunnde nisch geschbeischorrd wärdn (is genug freiorr Schbeischorrbladds vorhandn?)", "commands.save.saving": "<PERSON><PERSON><PERSON><PERSON> würd geschbeischorrd (das kann een Momend dauorrn!)", "commands.save.success": "Schbiel wurde geschbeischorrd", "commands.schedule.cleared.failure": "<PERSON>s gibd keene Blanungn mid dorr ID %s", "commands.schedule.cleared.success": "<PERSON>s wurde(n) %s Planung(en) mit der ID %s entfernt", "commands.schedule.created.function": "Funktion ‚%s‘ wurde in %s Tick(s) zur Spielzeit %s eingeplant", "commands.schedule.created.tag": "Edigedd ‚%s<PERSON> wurde in %s <PERSON><PERSON> zur Schbielzeid %s eingepland", "commands.schedule.macro": "<PERSON><PERSON><PERSON> kö<PERSON>n nicht geplant werden", "commands.schedule.same_tick": "<PERSON><PERSON><PERSON> den aggduelln Tigg kann nüschd eingepland wärdn", "commands.scoreboard.objectives.add.duplicate": "Ä Ziel mid diesm Nam eggsischdierd bereids", "commands.scoreboard.objectives.add.success": "Neues Ziel %s wurde erschdelld", "commands.scoreboard.objectives.display.alreadyEmpty": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändo<PERSON>d, de Anzeischebositschoon is bereids leer", "commands.scoreboard.objectives.display.alreadySet": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, de Anzeischebositschoon zeischd bereids das Ziel", "commands.scoreboard.objectives.display.cleared": "Alle Ziele in Anzeischebositschoon %s wurdn gelöschd", "commands.scoreboard.objectives.display.set": "Anzeischebositschoon %s zeischd nu das Ziel %s", "commands.scoreboard.objectives.list.empty": "<PERSON><PERSON> gibd keene Ziele", "commands.scoreboard.objectives.list.success": "Es gibt %s Punkte‐Ziel(e): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Automatische Aktualisierung des Anzeigenamens wurde für Punkte‐Ziel ‚%s‘ deaktiviert", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Automatische Aktualisierung des Anzeigenamens wurde für Punkte‐Ziel ‚%s‘ aktiviert", "commands.scoreboard.objectives.modify.displayname": "Anzeischename von %s wurde zu %s geännord", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Standard‐Zahlendarstellung von Punkte‐Ziel ‚%s‘ wurde zurückgesetzt", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Standard‐Zahlendarstellung von Punkte‐Ziel ‚%s‘ wurde geändert", "commands.scoreboard.objectives.modify.rendertype": "Darschdellungsard von Ziel %s wurde geändorrd", "commands.scoreboard.objectives.remove.success": "Ziel %s wurde endfernd", "commands.scoreboard.players.add.success.multiple": "%2$s wurde für %3$s Objegde um %1$s erheehd", "commands.scoreboard.players.add.success.single": "%2$s wurde für %3$s um %1$s erheehd (jäddse %4$s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Anzeigename in %2$s wurde für %1$s Objekte zurückgesetzt", "commands.scoreboard.players.display.name.clear.success.single": "Anzeigename in %2$s wurde für %1$s zurückgesetzt", "commands.scoreboard.players.display.name.set.success.multiple": "Anzeigename in %3$s wurde für %2$s Objekte zu %1$s geändert", "commands.scoreboard.players.display.name.set.success.single": "Anzeigename in %3$s wurde für %2$s zu %1$s geändert", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Zahlendarstellung in %2$s wurde für %1$s Objekte zurückgesetzt", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Zahlendarstellung in %2$s wurde für %1$s zurückgesetzt", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Zahlendarstellung in %2$s wurde für %1$s Objekte geändert", "commands.scoreboard.players.display.numberFormat.set.success.single": "Zahlendarstellung in %2$s wurde für %1$s geändert", "commands.scoreboard.players.enable.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändo<PERSON>d, dorr <PERSON> is bereids aggdivierd", "commands.scoreboard.players.enable.invalid": "Diesorr Befähl fundgschonierd nur für Auslösorr (trigger-Ziele)", "commands.scoreboard.players.enable.success.multiple": "Auslösorr %s wurde für %s Objegde aggdivierd", "commands.scoreboard.players.enable.success.single": "Auslösorr %s wurde für %s aggdivierd", "commands.scoreboard.players.get.null": "Werd von %s für %s kann nisch abgefragd wärdn, da keener gesäddsd is", "commands.scoreboard.players.get.success": "%s had %s %s", "commands.scoreboard.players.list.empty": "<PERSON><PERSON> gibd keene üborrwachdn Objegde", "commands.scoreboard.players.list.entity.empty": "%s had keene Bungdeschdände", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s hat %s Punktestand/‐stände:", "commands.scoreboard.players.list.success": "Es gibt %s überwachte(s) Objekt(e): %s", "commands.scoreboard.players.operation.success.multiple": "%s wurde für %s Objegde geändorrd", "commands.scoreboard.players.operation.success.single": "%s wurde für %s uff %s geändorrd", "commands.scoreboard.players.remove.success.multiple": "%2$s wurde für %3$s Objegde um %1$s vorringord", "commands.scoreboard.players.remove.success.single": "%2$s wurde für %3$s um %1$s vorringord (jäddse %4$s)", "commands.scoreboard.players.reset.all.multiple": "Alle Bungdeschdände wurdn für %s Objegde gelöschd", "commands.scoreboard.players.reset.all.single": "Alle Bungdeschdände wurdn für %s gelöschd", "commands.scoreboard.players.reset.specific.multiple": "%s wurde für %s Objegde gelöschd", "commands.scoreboard.players.reset.specific.single": "%s wurde für %s gelöschd", "commands.scoreboard.players.set.success.multiple": "%s wurde für %s Objegde uff %s gesäddsd", "commands.scoreboard.players.set.success.single": "%s wurde für %s uff %s gesäddsd", "commands.seed.success": "Dorr <PERSON>werd is %s", "commands.setblock.failed": "Blogg kunnde nisch bladdsierd wärdn", "commands.setblock.success": "Blogg bei (%s, %s, %s) wurde geändorrd", "commands.setidletimeout.success": "Spieler werden nun nach %s Minute(n) Untätigkeit vom Server getrennt", "commands.setidletimeout.success.disabled": "Spieler werden nun bei Untätigkeit nicht vom Server getrennt", "commands.setworldspawn.failure.not_overworld": "Welteinstiegspunkt kann nur für die Oberwelt gesetzt werden", "commands.setworldspawn.success": "Weldeinschdiegsbungd wurde uff (%s, %s, %s) [%s] gesäddsd", "commands.spawnpoint.success.multiple": "Einschdiegsbungd von %6$s Schbielorn wurde uff (%1$s, %2$s, %3$s) [%4$s] in %5$s gesäddsd", "commands.spawnpoint.success.single": "Einschdiegsbungd von %6$s wurde uff (%1$s, %2$s, %3$s) [%4$s] in %5$s gesäddsd", "commands.spectate.not_spectator": "%s is nisch im Zuschauorrmodus", "commands.spectate.self": "Du kannsd disch nisch selbsd beobachdn", "commands.spectate.success.started": "Du beobachdsd nun %s", "commands.spectate.success.stopped": "Du beobachd’sd keen Objegd mähr", "commands.spreadplayers.failed.entities": "%s Objekt(e) konnte(n) nicht um (%s, %s) verteilt werden (zu wenig Platz für Objekte – versuche, höchstens %s zu verteilen)", "commands.spreadplayers.failed.invalid.height": "Ungüldschorr Werd für maxHeight: %s. Er muss heejorr sein als de Weldundorrgrenze %s", "commands.spreadplayers.failed.teams": "%s Team(s) konnte(n) nicht um (%s, %s) verteilt werden (zu wenig Platz für Teams – versuche, höchstens %s zu verteilen)", "commands.spreadplayers.success.entities": "%s Objekt(e) wurde(n) rund um (%s, %s) mit einem Abstand von durchschnittlich %s Block/Blöcken zueinander verteilt", "commands.spreadplayers.success.teams": "%s Deam(s) worrde(n) rund um (%s, %s) mid eenem Abschdand von durchschniddlich %s Blöggen zueinander verdeild", "commands.stop.stopping": "<PERSON><PERSON><PERSON><PERSON><PERSON> würd g<PERSON>", "commands.stopsound.success.source.any": "Alle %s-Geräusche wurdn geschdobbd", "commands.stopsound.success.source.sound": "Geräusch %s für de Geräuschard %s wurde geschdobbd", "commands.stopsound.success.sourceless.any": "Alle Geräusche wurdn geschdobbd", "commands.stopsound.success.sourceless.sound": "Geräusch %s wurde geschdobbd", "commands.summon.failed": "Objegd kunnde nisch erzeuschd wärdn", "commands.summon.failed.uuid": "Objegd kunnde nisch erzeuschd wärdn; die UUID eggsischdierd bereids", "commands.summon.invalidPosition": "Ungüldsche Bositschoon zum Erzeuschn", "commands.summon.success": "%s wurde erzeuschd", "commands.tag.add.failed": "Endwedorr had das Ziel bereids dieses Edigedd odorr es had zu viele Edigeddn", "commands.tag.add.success.multiple": "Edigedd ‚%s‘ wurde %s Objegdn hinzugefüschd", "commands.tag.add.success.single": "Edigedd ‚%s‘ wurde %s hinzugefüschd", "commands.tag.list.multiple.empty": "Die %s ausgewähldn Objegde habn keen Edigedd", "commands.tag.list.multiple.success": "Die %s ausgewähldn Objegde habn insgesamd %s Edigeddn: %s", "commands.tag.list.single.empty": "%s had keene <PERSON>", "commands.tag.list.single.success": "%s had %s Edigedden: %s", "commands.tag.remove.failed": "<PERSON><PERSON> besi<PERSON> dieses Edigedd nisch", "commands.tag.remove.success.multiple": "Edigedd ‚%s‘ wurde von %s Objegdn endfernd", "commands.tag.remove.success.single": "Edigedd ‚%s‘ wurde von %s endfernd", "commands.team.add.duplicate": "Ä Team mid diesm Nam eggsischdierd bereids", "commands.team.add.success": "Team %s wurde ersch<PERSON>d", "commands.team.empty.success": "%s Mitglied(er) wurde(n) aus Team %s entfernt", "commands.team.empty.unchanged": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, das Team is bereids leer", "commands.team.join.success.multiple": "%s <PERSON><PERSON><PERSON><PERSON><PERSON> wurdn zu Team %s hinzugefüschd", "commands.team.join.success.single": "%s wurde zu Team %s hinzugefüschd", "commands.team.leave.success.multiple": "%s Midgliedorr wurdn aus Teams endfernd", "commands.team.leave.success.single": "%s wurde aus jedm Team endfernd", "commands.team.list.members.empty": "Team %s had keene <PERSON>or", "commands.team.list.members.success": "Deam %s had %s Midglied(orr): %s", "commands.team.list.teams.empty": "<PERSON><PERSON> gibd keene Teams", "commands.team.list.teams.success": "Es gibd %s Deam(s): %s", "commands.team.option.collisionRule.success": "Schiebereschl für Team %s wurde uff ‚%s‘ gesäddsd", "commands.team.option.collisionRule.unchanged": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, <PERSON> <PERSON><PERSON><PERSON><PERSON> had bereids diesn Werd", "commands.team.option.color.success": "Farbe von Team %s wurde uff %s gesäddsd", "commands.team.option.color.unchanged": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON>d, das Team had bereids diese Farbe", "commands.team.option.deathMessageVisibility.success": "Sischdbarkeed dorr Dodesmeldungn von Team %s wurde uff ‚%s‘ gesäddsd", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, de <PERSON><PERSON><PERSON><PERSON> dorr <PERSON>du<PERSON> had bereids diesn Werd", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, für dieses Team is Eischnbeschuss bereids deaggdivierd", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON>ü<PERSON>d hads’sch geändorrd, für dieses Team is Eischnbeschuss bereids aggdivierd", "commands.team.option.friendlyfire.disabled": "Eischnbeschuss wurde für Team %s deaggdivierd", "commands.team.option.friendlyfire.enabled": "Eischnbeschuss wurde für Team %s aggdivierd", "commands.team.option.name.success": "Name von Team %s wurde aggdualisierd", "commands.team.option.name.unchanged": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändo<PERSON>d, das Team had bereids diesn Nam", "commands.team.option.nametagVisibility.success": "Sischd<PERSON><PERSON> dorr <PERSON> von Team %s wurde uff ‚%s‘ gesäddsd", "commands.team.option.nametagVisibility.unchanged": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, de <PERSON><PERSON><PERSON><PERSON> dorr <PERSON> had bereids diesn Werd", "commands.team.option.prefix.success": "Team-P<PERSON><PERSON><PERSON><PERSON> wurde uff %s gesäddsd", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändo<PERSON>d, das Team kann bereids unsischdbare Teammidgliedorr nisch sehn", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON>d, das Team kann bereids unsischdbare Teammidgliedorr sehn", "commands.team.option.seeFriendlyInvisibles.disabled": "Team %s kann keene unsischdbarn Teammidgliedorr mähr sehn", "commands.team.option.seeFriendlyInvisibles.enabled": "Team %s kann nun un<PERSON><PERSON><PERSON><PERSON><PERSON> Teammidgliedorr <PERSON>hn", "commands.team.option.suffix.success": "Team-<PERSON><PERSON><PERSON> wurde uff %s gesäddsd", "commands.team.remove.success": "Team %s wurde endfernd", "commands.teammsg.failed.noteam": "<PERSON> mussd in eem Team sein, um de Team zu benachrischdign", "commands.teleport.invalidPosition": "Ungüldsche Bositschoon zum Delebordiern", "commands.teleport.success.entity.multiple": "%s Objegde wurdn zu %s delebordierd", "commands.teleport.success.entity.single": "%s wurde zu %s delebordierd", "commands.teleport.success.location.multiple": "%s Objeg<PERSON> wurdn zu (%s, %s, %s) delebordierd", "commands.teleport.success.location.single": "%s wurde zu (%s, %s, %s) delebordierd", "commands.test.batch.starting": "Umgebung %s, Teil %s wird gestartet", "commands.test.clear.error.no_tests": "<PERSON>s wurden keine Tests zum Löschen gefunden", "commands.test.clear.success": "%s Konstruktion(en) wurde(n) gelöscht", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "<PERSON><PERSON><PERSON>, um in die Zwischenablage zu kopieren", "commands.test.create.success": "Testaufbau wurde für Test %s erstellt", "commands.test.error.no_test_containing_pos": "Es kann keine Testinstanz gefunden werden, die %s, %s, %s enthält", "commands.test.error.no_test_instances": "<PERSON>s wurden keine Testinstanzen gefunden", "commands.test.error.non_existant_test": "Test %s konnte nicht gefunden werden", "commands.test.error.structure_not_found": "Testkonstruktion %s konnte nicht gefunden werden", "commands.test.error.test_instance_not_found": "Testinstanz‐Blockobjekt konnte nicht gefunden werden", "commands.test.error.test_instance_not_found.position": "Testinstanz‐Blockobjekt konnte nicht für Test bei %s, %s, %s gefunden werden", "commands.test.error.too_large": "Die Konstruktion darf in jeder Richtung höchstens %s Blö<PERSON> groß sein", "commands.test.locate.done": "Suche abgeschlossen, %s Konstruktion(en) gefunden", "commands.test.locate.found": "Konstruktion bei %s gefunden (Entfernung: %s)", "commands.test.locate.started": "Testkonstruktion(en) werden ausfindig gemacht, das könnte ein bisschen dauern ...", "commands.test.no_tests": "Keine Tests zum Ausführen vorhanden", "commands.test.relative_position": "Position relativ zu %s: %s", "commands.test.reset.error.no_tests": "<PERSON><PERSON> wurden keine Tests zum Zurücksetzen gefunden", "commands.test.reset.success": "%s Konstruktion(en) wurde(n) zurückgesetzt", "commands.test.run.no_tests": "Keine Tests gefunden", "commands.test.run.running": "%s Test(s) wird/werden ausgeführt …", "commands.test.summary": "Spielüberprüfung abgeschlossen! %s Test(s) wurde(n) ausgeführt", "commands.test.summary.all_required_passed": "Alle erforderlichen Tests waren erfolgreich :)", "commands.test.summary.failed": "%s erforderliche(r) Test(s) schlug(en) fehl :(", "commands.test.summary.optional_failed": "%s optionale(r) Test(s) schlug(en) fehl", "commands.tick.query.percentiles": "Perzendile: P50: %s ms, P95: %s ms, P99: %s ms; Schdichbrobe: %s", "commands.tick.query.rate.running": "Soll‐Diggradde: %s pro Segunde.\nDurchschniddliche Zeed pro Digg: %s ms (Soll: %s ms)", "commands.tick.query.rate.sprinting": "Soll‐Diggrade: %s pro Segunde (nich berüggsichdigd, nur zorr Reforrenz).\nDurchschniddliche Zeed pro Digg: %s ms", "commands.tick.rate.success": "Soll‐Diggrate worrde uf %s pro Segunde gesedzd", "commands.tick.sprint.report": "Beschleunigung worrde mid %s Diggs pro Segunde bzw. %s ms pro Digg abgeschlossn", "commands.tick.sprint.stop.fail": "<PERSON><PERSON> finded dorz<PERSON> keene <PERSON>‐Beschleunigung schdadd", "commands.tick.sprint.stop.success": "Digg‐Beschleunigung worrrde abgebrochn", "commands.tick.status.frozen": "<PERSON><PERSON> is e<PERSON><PERSON><PERSON><PERSON>", "commands.tick.status.lagging": "<PERSON><PERSON>, gann jedoch nich mid dor <PERSON>‐Diggradde midhaldn", "commands.tick.status.running": "Das Spiel läuft normal", "commands.tick.status.sprinting": "Das Spiel läuft beschleunigt", "commands.tick.step.fail": "Spiel konnte nicht fortgeschritten werden – es muss zunächst eingefroren werden", "commands.tick.step.stop.fail": "Es findet derzeit kein Tick‐Fortschreiten statt", "commands.tick.step.stop.success": "Tick‐Fortschreiten wurde abgebrochen", "commands.tick.step.success": "Es wird um %s Tick(s) fortgeschritten", "commands.time.query": "Die aggduelle Zeid is %s", "commands.time.set": "Zeid wurde uff %s gesäddsd", "commands.title.cleared.multiple": "Alle Didl wurdn für %s Schbielorr endfernd", "commands.title.cleared.single": "Alle Didl wurdn für %s endfernd", "commands.title.reset.multiple": "Didl-Anzeischezeidn wurdn für %s Schbielorr zurügggesäddsd", "commands.title.reset.single": "Didl-Anzeischezeidn wurdn für %s zurügggesäddsd", "commands.title.show.actionbar.multiple": "Neuorr Aggtionsleisdndidl würd für %s Schbielorr angezeischd", "commands.title.show.actionbar.single": "Neuorr Aggtionsleisdndidl würd für %s angezeischd", "commands.title.show.subtitle.multiple": "Neuorr Undorrdidl würd für %s Schbielorr angezeischd", "commands.title.show.subtitle.single": "Neuorr Undorrdidl würd für %s angezeischd", "commands.title.show.title.multiple": "Neuorr Didl würd für %s Schbielorr angezeischd", "commands.title.show.title.single": "Neuorr Didl würd für %s angezeischd", "commands.title.times.multiple": "Didl-Anzeischezeidn wurdn für %s Schbielorr geändorrd", "commands.title.times.single": "Didl-Anzeischezeidn wurdn für %s geändorrd", "commands.transfer.error.no_players": "Es muss mindestens ein zu übertragender Spieler angegeben werden", "commands.transfer.success.multiple": "%s Spieler werden an %s:%s übertragen", "commands.transfer.success.single": "%s wird an %s:%s übertragen", "commands.trigger.add.success": "%s wurde ausgelösd (Werd wurde um %s erheehd)", "commands.trigger.failed.invalid": "<PERSON><PERSON> is keen <PERSON> (trigger-<PERSON><PERSON>)", "commands.trigger.failed.unprimed": "Auslösorr is noch nisch aggdivierd", "commands.trigger.set.success": "%s wurde ausgelösd (Werd wurde uff %s gesäddsd)", "commands.trigger.simple.success": "%s wurde ausgelösd", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "<PERSON><PERSON> in %s", "commands.waypoint.list.success": "%s Wegpunkt(e) in %s: %s", "commands.waypoint.modify.color": "Wegpunktfarbe ist nun %s", "commands.waypoint.modify.color.reset": "Wegpunktfarbe wurde zurückgesetzt", "commands.waypoint.modify.style": "Aussehen des Wegpunkts wurde geändert", "commands.weather.set.clear": "<PERSON><PERSON><PERSON><PERSON><PERSON> wurde uff Glar geändorrd", "commands.weather.set.rain": "<PERSON><PERSON><PERSON><PERSON><PERSON> wurde uff Räschn geändorrd", "commands.weather.set.thunder": "<PERSON><PERSON><PERSON><PERSON><PERSON> wurde uff Gewiddorr geändorrd", "commands.whitelist.add.failed": "Schbielorr is bereids uff dorr <PERSON>", "commands.whitelist.add.success": "%s wurde zur Gäsdelisde hinzugefüschd", "commands.whitelist.alreadyOff": "<PERSON> Gä<PERSON>delisde is bereids deaggdivierd", "commands.whitelist.alreadyOn": "De Gä<PERSON>delisde is bereids aggdivierd", "commands.whitelist.disabled": "<PERSON> is jä<PERSON><PERSON> deaggdivierd", "commands.whitelist.enabled": "<PERSON> G<PERSON>del<PERSON>de is jädd<PERSON> aggdivierd", "commands.whitelist.list": "Es steht/stehen %s Spieler auf der Gästeliste: %s", "commands.whitelist.none": "<PERSON>s sin keene Schbielorr uff dorr <PERSON>", "commands.whitelist.reloaded": "<PERSON> Gä<PERSON>delisde wurde neu geladn", "commands.whitelist.remove.failed": "Schbielorr is nisch uff dorr <PERSON>", "commands.whitelist.remove.success": "%s wurde von dorr Gäsdelisde endfernd", "commands.worldborder.center.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, de <PERSON>ldbar<PERSON><PERSON> had bereids diese Midde", "commands.worldborder.center.success": "De Midde dorr Weldbarriere wurde uff (%s, %s) gesäddsd", "commands.worldborder.damage.amount.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, dorr von dorr Weldbarriere vorursachde Schadn had bereids diesn Werd", "commands.worldborder.damage.amount.success": "<PERSON><PERSON>n dorr Weldbarriere wurde uff %s pro Blogg jede Segunde gesäddsd", "commands.worldborder.damage.buffer.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, dorr <PERSON>vor<PERSON>ährdheidsbereisch um de Weldbarriere had bereids diese Greeße", "commands.worldborder.damage.buffer.success": "Der Unversehrtheitsbereich um die Weltbarriere wurde auf %s Block/Blöcke gesetzt", "commands.worldborder.get": "Die Weltbarriere hat derzeit eine Weite von %s Block/Blöcken", "commands.worldborder.set.failed.big": "De Weide dorr Weldbarriere darf nisch größorr als %s Blögge sein", "commands.worldborder.set.failed.far": "De Weldbarriere darf nisch weidorr als %s Blögge endfernd sein", "commands.worldborder.set.failed.nochange": "<PERSON><PERSON><PERSON><PERSON> hads’sch ge<PERSON><PERSON><PERSON><PERSON>, de <PERSON>ld<PERSON><PERSON><PERSON> had bereids diese Weide", "commands.worldborder.set.failed.small": "De Weide dorr Weldbarriere darf nisch gleenorr als 1 Blogg sein", "commands.worldborder.set.grow": "De Weldbarriere würd innorhalb von %2$s Segundn uff eene Weide von %1$s Blöggn vorgrößord", "commands.worldborder.set.immediate": "Die Weltbarriere wurde auf eine Weite von %s Block/Blöcken gesetzt", "commands.worldborder.set.shrink": "Die Weltbarriere wird innerhalb von %2$s Sekunde(n) auf eine Weite von %1$s Block/Blöcke verkleinert", "commands.worldborder.warning.distance.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch geändorrd, dorr Warnbereisch um de Weldbarriere had bereids diese Greeße", "commands.worldborder.warning.distance.success": "Der Warnbereich um die Weltbarriere wurde auf %s Block/Blöcke gesetzt", "commands.worldborder.warning.time.failed": "<PERSON><PERSON><PERSON><PERSON> hads’sch g<PERSON><PERSON><PERSON><PERSON><PERSON>, de <PERSON>nzeid dorr <PERSON>ldbarriere had bereids diesn Werd", "commands.worldborder.warning.time.success": "Die Warnzeit der Weltbarriere wurde auf %s Sekunde(n) gesetzt", "compliance.playtime.greaterThan24Hours": "Du schbielsd bereids seid mähr als 24 Schdundn", "compliance.playtime.hours": "Du schbielsd bereids seid %s Schdund(n)", "compliance.playtime.message": "Üborrmäß’sches Schbieln kann dein Alldaach beeinflussn", "connect.aborted": "Abgebrochn", "connect.authorizing": "<PERSON><PERSON><PERSON>", "connect.connecting": "Vorrbinde zum Sörvorr …", "connect.encrypting": "Vorrschlüssln …", "connect.failed": "Vorrbindungsuffbau fählgeschlachn", "connect.failed.transfer": "Verbindung bei Übertragung an den Server fehlgeschlagen", "connect.joining": "<PERSON><PERSON>", "connect.negotiating": "<PERSON><PERSON><PERSON><PERSON> …", "connect.reconfiging": "<PERSON>eu konfigurier<PERSON> …", "connect.reconfiguring": "<PERSON>eu konfigurier<PERSON> …", "connect.transferring": "Übertrage an neuen Server …", "container.barrel": "Fass", "container.beacon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.beehive.bees": "Bienen: %s/%s", "container.beehive.honey": "Honig: %s/%s", "container.blast_furnace": "Schmelzofn", "container.brewing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.cartography_table": "<PERSON><PERSON><PERSON><PERSON>", "container.chest": "<PERSON><PERSON><PERSON>", "container.chestDouble": "Große Druhe", "container.crafter": "<PERSON><PERSON><PERSON>", "container.crafting": "Handwersch", "container.creative": "Geschnschdände", "container.dispenser": "Werforr", "container.dropper": "Schbendorr", "container.enchant": "Vorrzauborrn", "container.enchant.clue": "%s …?", "container.enchant.lapis.many": "%s <PERSON><PERSON><PERSON><PERSON><PERSON>", "container.enchant.lapis.one": "1 Labisladsuli", "container.enchant.level.many": "%s Erfahrungsschdufn", "container.enchant.level.one": "1 Erfahrungsschdufe", "container.enchant.level.requirement": "Erfordorrlische Erfahrungsschdufe: %s", "container.enderchest": "Enderdruhe", "container.furnace": "Ofn", "container.grindstone_title": "Reboriern & Endzauborn", "container.hopper": "Drischdorr", "container.inventory": "Invendar", "container.isLocked": "%s is vorschlossn!", "container.lectern": "Lesebuld", "container.loom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.repair": "Reboriern & Benenn", "container.repair.cost": "Erfahrungsgosdn: %1$s", "container.repair.expensive": "Zu deuorr!", "container.shulkerBox": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%2$s× %1$s", "container.shulkerBox.more": "un %s weido<PERSON> …", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Räuschoroofn", "container.spectatorCantOpen": "Kann nisch geöffned wärdn. Inhald wurde noch nisch genorrierd.", "container.stonecutter": "Schdeensäsche", "container.upgrade": "Ausrüsdung uffwärdn", "container.upgrade.error_tooltip": "Gegenstand kann so nicht aufgewertet werden", "container.upgrade.missing_template_tooltip": "Schmiedevorlage hinzufügen", "controls.keybinds": "Dasdnbeleschung …", "controls.keybinds.duplicateKeybinds": "Diese Taste wird auch verwendet für:\n%s", "controls.keybinds.title": "Dasdnbeleschung", "controls.reset": "Schdandard", "controls.resetAll": "<PERSON><PERSON>ddsn", "controls.title": "Schdeuorrung", "createWorld.customize.buffet.biome": "Bidde wähle een Biom", "createWorld.customize.buffet.title": "An<PERSON><PERSON><PERSON> dorr Buffet-Weld", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Bodn – %s", "createWorld.customize.flat.layer.top": "Oborrfläsche - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON><PERSON> endfern", "createWorld.customize.flat.tile": "Schischdmadorial", "createWorld.customize.flat.title": "Suborrflachlandanbassung", "createWorld.customize.presets": "Vorlaachen", "createWorld.customize.presets.list": "Aldorrnadiv sin hier eenige, die wir zuvorr erschdelld ham!", "createWorld.customize.presets.select": "Voreenschdellung benuddsn", "createWorld.customize.presets.share": "Möschdesd du deine Vorlaache mid jemandn deiln? Benuddse das Eengabefeld!", "createWorld.customize.presets.title": "<PERSON><PERSON>dellung auswähln", "createWorld.preparing": "Welderschdellung würd vorbereided …", "createWorld.tab.game.title": "Schbiel", "createWorld.tab.more.title": "<PERSON><PERSON>", "createWorld.tab.world.title": "Weld", "credits_and_attribution.button.attribution": "Namensnennung", "credits_and_attribution.button.credits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.button.licenses": "<PERSON><PERSON><PERSON>", "credits_and_attribution.screen.title": "Mitwirkende und Namensnennung", "dataPack.bundle.description": "Aktiviert das experimentelle Bündel", "dataPack.bundle.name": "Bündel", "dataPack.locator_bar.description": "Zeigt im Mehrspielermodus die Richtung anderer Spieler an", "dataPack.locator_bar.name": "Ortungsleiste", "dataPack.minecart_improvements.description": "Verbesserte Bewegung von <PERSON>", "dataPack.minecart_improvements.name": "Lorenverbesserungen", "dataPack.redstone_experiments.description": "Experimentelle Redstone‐Änderungen", "dataPack.redstone_experiments.name": "Redstone‐Experimente", "dataPack.title": "<PERSON><PERSON>bagede auswähln", "dataPack.trade_rebalance.description": "Überarbeitete Angebote für Dorfbewohner", "dataPack.trade_rebalance.name": "Neugewichtung des Dorfbewohnerhandels", "dataPack.update_1_20.description": "Neue Spielelemente und Inhalte für Minecraft 1.20", "dataPack.update_1_20.name": "Aktualisierung 1.20", "dataPack.update_1_21.description": "Neue Spielelemente und Inhalte für Minecraft 1.21", "dataPack.update_1_21.name": "Aktualisierung 1.21", "dataPack.validation.back": "<PERSON><PERSON><PERSON><PERSON>", "dataPack.validation.failed": "Üborrprüfung dorr Dadnbagede fählgeschlachn!", "dataPack.validation.reset": "<PERSON><PERSON>üggsäddsn", "dataPack.validation.working": "Ausgewählde Dadnbagede wärdn üborrprüfd …", "dataPack.vanilla.description": "De Sc<PERSON>dandarddadn für Minecraft", "dataPack.vanilla.name": "Schdandard", "dataPack.winter_drop.description": "Neue Spielelemente und Inhalte für den Winter‐Drop", "dataPack.winter_drop.name": "Winter‐Drop", "datapackFailure.safeMode": "Ab<PERSON><PERSON><PERSON><PERSON><PERSON>", "datapackFailure.safeMode.failed.description": "Diese Welt enthält ungültige oder beschädigte Speicherdaten.", "datapackFailure.safeMode.failed.title": "Laden der Welt im abgesicherten Modus fehlgeschlagen", "datapackFailure.title": "Fählorr in den derzeid ausgewähldn Dadnbagedn ham das Ladn dorr Weld vorhindorrd.\nDu kannsd endwedorr vorrsuchn, se nur midm Vanilla-Dadnbaged zu ladn („abgesischorrdorr Modus“), odorr zum Haubdmenü zurüggzugehn un es manuell behebn.", "death.attack.anvil": "%1$s wurde von eem fallendn Ambuss zorquedschd", "death.attack.anvil.player": "%1$s wurde während des Gambfes mid %2$s von eem fallendn Ambuss zorquedschd", "death.attack.arrow": "%1$s wurde von %2$s erschossn", "death.attack.arrow.item": "%1$s wurde von %2$s mid %3$s erschossn", "death.attack.badRespawnPoint.link": "beabsischdigdm Schbieldesign", "death.attack.badRespawnPoint.message": "%1$s wurde von %2$s gedöded", "death.attack.cactus": "%1$s wurde von eem Gaggdus zu Dode geschdochn", "death.attack.cactus.player": "%1$s rannde beim <PERSON>, %2$s zu endkomm, in een Gaggdus", "death.attack.cramming": "%1$s wurde zorquedschd", "death.attack.cramming.player": "%1$s wurde von %2$s zorquedschd", "death.attack.dragonBreath": "%1$s wurde in Drachenatem geröstet", "death.attack.dragonBreath.player": "%1$s wurde durch %2$s in Drachenatem geröstet", "death.attack.drown": "%1$s erdrang", "death.attack.drown.player": "%1$s erdrang beim <PERSON>, %2$s zu endkomm", "death.attack.dryout": "%1$s schdarb an Wassorrmangel", "death.attack.dryout.player": "%1$s schdarb beim <PERSON>, %2$s zu endkomm, an Wassorrmangel", "death.attack.even_more_magic": "%1$s wurde dursch vorschdärgde Magie gedöded", "death.attack.explosion": "%1$s wurde in de Lufd geschbrengd", "death.attack.explosion.player": "%1$s wurde dursch %2$s in de Lufd geschbrengd", "death.attack.explosion.player.item": "%1$s wurde von %2$s mid %3$s in de Lufd geschbrengd", "death.attack.fall": "%1$s fiel dorr Schwergrafd zum Obfor", "death.attack.fall.player": "%1$s fiel beim <PERSON>, %2$s zu endkomm, dorr Schwergrafd zum Obforr", "death.attack.fallingBlock": "%1$s wurde von eem fallendn Blogg zorquedschd", "death.attack.fallingBlock.player": "%1$s wurde während des Gambfes mid %2$s von eem fallendn Blogg zorquedschd", "death.attack.fallingStalactite": "%1$s wurde von eem fallnden Schdalagdidn uffgeschbießd", "death.attack.fallingStalactite.player": "%1$s wurde während des Gampfes mid %2$s von eem fallnden Schdalagdidn uffgeschbießd", "death.attack.fireball": "%1$s wurde von %2$s flambierd", "death.attack.fireball.item": "%1$s wurde von %2$s mid %3$s flambierd", "death.attack.fireworks": "%1$s flog mid eem Gnall in de Lufd", "death.attack.fireworks.item": "%1$s flog uffgrund e<PERSON><PERSON>, das von %2$s mid %3$s abgeschossn wurde, mid eem Gnall in de Lufd", "death.attack.fireworks.player": "%1$s flog während des Gambfes mid %2$s mid eem Gnall in de Lufd", "death.attack.flyIntoWall": "%1$s erfuhr ginedische Enerschie", "death.attack.flyIntoWall.player": "%1$s erfuhr beim <PERSON>, %2$s zu endkomm, ginedische Enerschie", "death.attack.freeze": "%1$s is erfrorn", "death.attack.freeze.player": "%1$s is dursch %2$s erfrorn", "death.attack.generic": "%1$s schdarb", "death.attack.generic.player": "%1$s schdarb weschn %2$s", "death.attack.genericKill": "%1$s wurde gedöded", "death.attack.genericKill.player": "%1$s wurde im Kampf gegen %2$s getötet", "death.attack.hotFloor": "%1$s wurde dorr <PERSON>dn zu heeß", "death.attack.hotFloor.player": "%1$s geried weschn %2$s in de Gefahrnzone", "death.attack.inFire": "%1$s ging in Flamm uff", "death.attack.inFire.player": "%1$s lief während des Gambfes mid %2$s ins Feuorr", "death.attack.inWall": "%1$s wurde lebendsch begrabn", "death.attack.inWall.player": "%1$s wurde während des Gambfes mid %2$s lebendsch begrabn", "death.attack.indirectMagic": "%1$s wurde von %2$s mid Magie gedöded", "death.attack.indirectMagic.item": "%1$s wurde von %2$s mid %3$s gedöded", "death.attack.lava": "%1$s vorrsuchde, in <PERSON><PERSON> zu schwimmen", "death.attack.lava.player": "%1$s fiel beim <PERSON>, %2$s zu endkomm, in Lava", "death.attack.lightningBolt": "%1$s wurde vom Blidds gedroffn", "death.attack.lightningBolt.player": "%1$s wurde während des Gambfes mid %2$s vom Blidds gedroffn", "death.attack.mace_smash": "%1$s wurde von %2$s zersch<PERSON>ert", "death.attack.mace_smash.item": "%1$s wurde von %2$s mit %3$s zersch<PERSON>ert", "death.attack.magic": "%1$s wurde dursch Magie gedöded", "death.attack.magic.player": "%1$s wurde beim <PERSON>, %2$s zu endkomm, dursch Magie gedöded", "death.attack.message_too_long": "<PERSON><PERSON><PERSON> is de Meldung zu lang, um angezeischd wärdn zu könn. Hier is enne vorkürzde Version: %s", "death.attack.mob": "%1$s wurde von %2$s erschlachn", "death.attack.mob.item": "%1$s wurde von %2$s mid %3$s erschlachn", "death.attack.onFire": "%1$s vorbrannde", "death.attack.onFire.item": "%1$s wurde während des Gambfes mid %2$s mid %3$s gerösded", "death.attack.onFire.player": "%1$s wurde während des Gambfes mid %2$s gerö<PERSON>ded", "death.attack.outOfWorld": "%1$s fiel aus dorr Weld", "death.attack.outOfWorld.player": "%1$s wollde nisch mähr in dorselbn Weld wie %2$s lebn", "death.attack.outsideBorder": "%1$s hat de Grenzn diesor Weld verlassn", "death.attack.outsideBorder.player": "%1$s verließ im Kampf gegen %2$s die Grenzen dieser Welt", "death.attack.player": "%1$s wurde von %2$s erschlachn", "death.attack.player.item": "%1$s wurde von %2$s mid %3$s erschlachn", "death.attack.sonic_boom": "%1$s wurde dursch schallgeladnes Greischn ausgelöschd", "death.attack.sonic_boom.item": "%1$s wurde beim <PERSON>, %2$s mid %3$s zu endkomm, dursch schallgeladnes Greischn ausgelöschd", "death.attack.sonic_boom.player": "%1$s wurde beim <PERSON>, %2$s zu endkomm, dursch schallgeladnes Greischn ausgelöschd", "death.attack.stalagmite": "%1$s wurde von eem Schdalagmidn uffgeschbießd", "death.attack.stalagmite.player": "%1$s wurde während des Gambfes mid %2$s von eem Schdalagmidn uffgeschbießd", "death.attack.starve": "%1$s vorhungorde", "death.attack.starve.player": "%1$s vorhungorde während des Gambfes mid %2$s", "death.attack.sting": "%1$s wurde zu Dode geschdochn", "death.attack.sting.item": "%1$s wurde von %2$s mid %3$s zu Dode geschdochn", "death.attack.sting.player": "%1$s wurde von %2$s zu Dode geschdochn", "death.attack.sweetBerryBush": "%1$s had sich an eem Süßbeerschdrauch zu Dode geschdochn", "death.attack.sweetBerryBush.player": "%1$s had sich be<PERSON>, %2$s zu endkomm, an eem S<PERSON>ßbeerschdrauch zu Dode geschdochn", "death.attack.thorns": "%1$s wurde be<PERSON>, %2$s zu vorleddsn, gedöded", "death.attack.thorns.item": "%1$s wurde beim <PERSON>, %2$s zu vorleddsn, von %3$s gedöded", "death.attack.thrown": "%1$s wurde von %2$s zu Dode gebrüscheld", "death.attack.thrown.item": "%1$s wurde von %2$s mid %3$s zu Dode gebrüscheld", "death.attack.trident": "%1$s wurde von %2$s uffgeschbießd", "death.attack.trident.item": "%1$s wurde von %2$s mid %3$s uffgeschbießd", "death.attack.wither": "%1$s vordorrde", "death.attack.wither.player": "%1$s vordorrde während des Gambfes mid %2$s", "death.attack.witherSkull": "%1$s wurde von %2$s mid eem Nischl erschossn", "death.attack.witherSkull.item": "%1$s wurde von %2$s mid %3$s mid eem <PERSON> ersch<PERSON>n", "death.fell.accident.generic": "%1$s fiel aus zu großorr Heeje", "death.fell.accident.ladder": "%1$s schdürddsde von eener Leidorr ab", "death.fell.accident.other_climbable": "%1$s schdürddsde beim Gläddorrn ab", "death.fell.accident.scaffolding": "%1$s schdürddsde von eem Gerüschd ab", "death.fell.accident.twisting_vines": "%1$s schdürddsde von Zwürblrangn ab", "death.fell.accident.vines": "%1$s schdürddsde von Rangn ab", "death.fell.accident.weeping_vines": "%1$s schdürddsde von Drauorrrangn ab", "death.fell.assist": "%1$s wurde von %2$s zum Abschdurdds vordammd", "death.fell.assist.item": "%1$s wurde von %2$s mid %3$s zum Abschdurdds vordammd", "death.fell.finish": "%1$s fiel zu dief un wurde von %2$s erledischd", "death.fell.finish.item": "%1$s fiel zu dief un wurde von %2$s mid %3$s erledischd", "death.fell.killer": "%1$s wurde zum Abschdurdds vordammd", "deathScreen.quit.confirm": "<PERSON><PERSON><PERSON>, dassde das Schbiel vorlassn möschdesd?", "deathScreen.respawn": "Wiedorrbelebn", "deathScreen.score": "Bungdeschdand", "deathScreen.score.value": "Punktestand: %s", "deathScreen.spectate": "Weld beobachdn", "deathScreen.title": "Du bisd geschdorbn!", "deathScreen.title.hardcore": "Schbiel vorbei!", "deathScreen.titleScreen": "Haubdmenü", "debug.advanced_tooltips.help": "F3 + H = <PERSON><PERSON><PERSON><PERSON><PERSON>", "debug.advanced_tooltips.off": "<PERSON><PERSON><PERSON><PERSON><PERSON> Schnellinfos: unsischdbar", "debug.advanced_tooltips.on": "Erweidorrde Schnellinfos: si<PERSON><PERSON><PERSON>", "debug.chunk_boundaries.help": "F3 + G = Chunggränzn anzeischn", "debug.chunk_boundaries.off": "Chunggränzn: unsischdbar", "debug.chunk_boundaries.on": "Chunggränzn: si<PERSON><PERSON><PERSON>", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON> leern", "debug.copy_location.help": "F3 + C = Bositschoon als /tp-Befähl gobiern odorr gedr<PERSON><PERSON><PERSON> haldn, um een Abschurds zu erzwingn", "debug.copy_location.message": "Bositschoon wurde in de Zwischnablache gobierd", "debug.crash.message": "F3 + C sin gedrüggd. Dies würd das Schbiel abschdürddsn lassn, wenn se nisch los<PERSON>n wärdn.", "debug.crash.warning": "Abschdurdds in %s …", "debug.creative_spectator.error": "Schbielmodus kunnde nisch geändorrd wärdn; keene Beräschdigung", "debug.creative_spectator.help": "F3 + N = Zwischn vorherischm Schbielmodus un Zuschauorrmodus wechsln", "debug.dump_dynamic_textures": "Dynamische Texturen in %s gespeichert", "debug.dump_dynamic_textures.help": "F3 + S = Dynamische Texturen ausgeben", "debug.gamemodes.error": "Schbielmodusauswahl kann weschn fählendorr Bäreschdigung nisch geöffned wärdn", "debug.gamemodes.help": "F3 + F4 = Schbielmodusauswahl öffnen", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Näschsdor", "debug.help.help": "F3 + Q = Diese Lisde anzeischn", "debug.help.message": "Dasdnbelegung:", "debug.inspect.client.block": "Cliendseidsche Bloggdaden in Zwischnablache gobierd", "debug.inspect.client.entity": "Cliendseidsche Objegddaden in Zwischnablache gobierd", "debug.inspect.help": "F3 + I = Blogg- odorr O<PERSON>n in Zwischnablache gobiern", "debug.inspect.server.block": "Sörvorrseidsche Bloggdadn in Zwischnablache gobierd", "debug.inspect.server.entity": "Sörvorrseidsche Objegddadn in Zwischnablache gobierd", "debug.pause.help": "F3 + Esc = Schbiel ohne Bausnmenü anhaldn (sofern möglisch)", "debug.pause_focus.help": "F3 + P = Bausiern bei Fogusvorlusd", "debug.pause_focus.off": "Bausiern bei Fogusvorlusd: deaggdivierd", "debug.pause_focus.on": "Bausiern bei Fogusvorlusd: aggdivierd", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Brofilerschdellung schdardn/beendn", "debug.profiling.start": "Debug-Uffzeischnung wurde für %s Segundn geschdarded. Benuddse F3 + L, um se vorzeidsch zu beendn", "debug.profiling.stop": "Brofilerschdellung beended. Ergäbnisse wurdn in %s geschbeischorrd.", "debug.reload_chunks.help": "F3 + A = <PERSON><PERSON> neu ladn", "debug.reload_chunks.message": "Alle Chungs wärdn neu geladn", "debug.reload_resourcepacks.help": "F3 + T = Ressourcenbagede neu ladn", "debug.reload_resourcepacks.message": "Ressourcenbagede neu geladn", "debug.show_hitboxes.help": "F3 + B = Hit<PERSON>ggsn anzeischn", "debug.show_hitboxes.off": "Hitboggsn: unsischdbar", "debug.show_hitboxes.on": "Hitboggsn: si<PERSON><PERSON><PERSON>", "debug.version.header": "Clientseitige Versionsinformationen:", "debug.version.help": "F3 + V = Clientseitige Versionsinformationen ausgeben", "demo.day.1": "Diese Demo läufd fünf Schpieldaache. Gib dei Besdes!", "demo.day.2": "<PERSON><PERSON><PERSON><PERSON>", "demo.day.3": "<PERSON><PERSON><PERSON><PERSON>", "demo.day.4": "<PERSON><PERSON><PERSON><PERSON>", "demo.day.5": "Fünfdorr un läddsdorr Daach!", "demo.day.6": "<PERSON><PERSON> fün<PERSON><PERSON><PERSON> is zu Ende. Drügge %s, um een Sgrienshot deinorr Schöpfung abzuschbeischorrn.", "demo.day.warning": "<PERSON><PERSON> is glei rum!", "demo.demoExpired": "<PERSON> Demo-<PERSON>eid is abgeloofn!", "demo.help.buy": "<PERSON><PERSON><PERSON><PERSON> koofn!", "demo.help.fullWrapped": "Diese Demo dauorrd 5 <PERSON>hbieldaache (ca. 1 Schdunde un 40 Minudn eschde Zeid). <PERSON><PERSON> dir de Fordschridde für Hinweese an! Viel <PERSON>h<PERSON>ß!", "demo.help.inventory": "Mid %1$s öffnesd du dei Invendar", "demo.help.jump": "Drügge %1$s, um zu schbringn", "demo.help.later": "Weidorrschbieln!", "demo.help.movement": "Benuddse %1$s, %2$s, %3$s, %4$s un de Maus, um disch zu beweschn", "demo.help.movementMouse": "<PERSON><PERSON> disch midhilfe dorr Maus um", "demo.help.movementShort": "Bewesche disch mid %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft-Demo-Modus", "demo.remainingTime": "Vorrbleibende Zeid: %s", "demo.reminder": "<PERSON> Demozeid is abgeloofn. Ko<PERSON> Schbiel, um fordzufahrn, odorr erschdelle eene neue Weld!", "difficulty.lock.question": "<PERSON><PERSON><PERSON> sic<PERSON>, dass de die Schwierischkeed diesorr Weld figgsiern möschdesd? Dies würd de Weld für immorr uff %1$s seddsn un du wirsd diese Eenschdellung nisch mähr ändorrn könn.", "difficulty.lock.title": "Schwierischkeed diesorr Weld figgsiern", "disconnect.endOfStream": "<PERSON><PERSON> do<PERSON>üborrdragung", "disconnect.exceeded_packet_rate": "Wäschn Üborrschreidung des Bagedradnlimits hinausgeworfn", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Statusanfrage wurde nicht beantwortet", "disconnect.loginFailedInfo": "Anmäldung fählgeschlachn: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "<PERSON><PERSON>lorrmodus is deaggdivierd, bidde üborrprüfe de Eenschdellungn deines Microsoft-Gondos.", "disconnect.loginFailedInfo.invalidSession": "Ungüldsche Siddsung (Brobiere, das Schbiel un den Launschorr neu zu schdardn)", "disconnect.loginFailedInfo.serversUnavailable": "De Audhendifizierungssörvorr sind derzeid nisch erreischbar. Bidde vorrsuche es noch ma.", "disconnect.loginFailedInfo.userBanned": "Du bisd vom Online-Schbieln ausgeschlossn", "disconnect.lost": "Vorrbindung undorrbrochn", "disconnect.packetError": "Fehler im Netzwerkprotokoll", "disconnect.spam": "Rausgeworfn weschn Spamming", "disconnect.timeout": "Zeidüborrschreidung", "disconnect.transfer": "An einen anderen Server übertragen", "disconnect.unknownHost": "Unbeganndorr <PERSON>", "download.pack.failed": "%s von %s <PERSON><PERSON>(en) konnte(n) nicht heruntergeladen werden", "download.pack.progress.bytes": "Fortschritt: %s (Gesamtgröße unbekannt)", "download.pack.progress.percent": "Fortschritt: %s %%", "download.pack.title": "Ressourcenpaket %s/%s wird heruntergeladen", "editGamerule.default": "Schdandard: %s", "editGamerule.title": "Schbielreeschln bearbeidn", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorbtion", "effect.minecraft.bad_omen": "<PERSON><PERSON><PERSON>", "effect.minecraft.blindness": "Blindheed", "effect.minecraft.conduit_power": "Meeresgrafd", "effect.minecraft.darkness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "Gunsd des Delfins", "effect.minecraft.fire_resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.haste": "<PERSON><PERSON>", "effect.minecraft.health_boost": "Eggsdraenergie", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON>", "effect.minecraft.hunger": "<PERSON><PERSON>", "effect.minecraft.infested": "Befallen", "effect.minecraft.instant_damage": "Diregdschadn", "effect.minecraft.instant_health": "Diregdheilung", "effect.minecraft.invisibility": "Unsischdbarkeed", "effect.minecraft.jump_boost": "Schbrunggrafd", "effect.minecraft.levitation": "Schwäbegrafd", "effect.minecraft.luck": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "Übelkeed", "effect.minecraft.night_vision": "Nachdsischd", "effect.minecraft.oozing": "Schleimen", "effect.minecraft.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.raid_omen": "<PERSON><PERSON><PERSON>", "effect.minecraft.regeneration": "Regenoration", "effect.minecraft.resistance": "Resisdenz", "effect.minecraft.saturation": "Säddigung", "effect.minecraft.slow_falling": "Sanfdorr Fall", "effect.minecraft.slowness": "Langsamkeed", "effect.minecraft.speed": "Geschwindischkeed", "effect.minecraft.strength": "<PERSON><PERSON><PERSON>ä<PERSON>", "effect.minecraft.trial_omen": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.unluck": "<PERSON><PERSON><PERSON>", "effect.minecraft.water_breathing": "Undorrwassorradem", "effect.minecraft.weakness": "Schwäsche", "effect.minecraft.weaving": "<PERSON>en", "effect.minecraft.wind_charged": "Windgeladen", "effect.minecraft.wither": "Verdorrung", "effect.none": "<PERSON><PERSON>", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Wassorraffinidäd", "enchantment.minecraft.bane_of_arthropods": "Nemesis dorr G<PERSON>or", "enchantment.minecraft.binding_curse": "<PERSON><PERSON><PERSON> dorr <PERSON>", "enchantment.minecraft.blast_protection": "Eggsblosionsschudds", "enchantment.minecraft.breach": "Durchbruch", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.density": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "Wassorrläuforr", "enchantment.minecraft.efficiency": "Effizienz", "enchantment.minecraft.feather_falling": "Fädorrfall", "enchantment.minecraft.fire_aspect": "Vorbrennung", "enchantment.minecraft.fire_protection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.flame": "<PERSON>lamme", "enchantment.minecraft.fortune": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.frost_walker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "Unendlischkeed", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "Blündorung", "enchantment.minecraft.loyalty": "Dreue", "enchantment.minecraft.luck_of_the_sea": "G<PERSON><PERSON>gg des Meeres", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "Reboradur", "enchantment.minecraft.multishot": "Mährfachschuss", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "<PERSON><PERSON><PERSON>ä<PERSON>", "enchantment.minecraft.projectile_protection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.protection": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.punch": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.quick_charge": "Schnellladn", "enchantment.minecraft.respiration": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.riptide": "Soch", "enchantment.minecraft.sharpness": "Schärfe", "enchantment.minecraft.silk_touch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.smite": "Bann", "enchantment.minecraft.soul_speed": "Seelnläufor", "enchantment.minecraft.sweeping": "Schwunggrafd", "enchantment.minecraft.sweeping_edge": "Schwungkraft", "enchantment.minecraft.swift_sneak": "Hu<PERSON><PERSON>", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "Fluch des Vorschwindns", "enchantment.minecraft.wind_burst": "<PERSON><PERSON><PERSON>", "entity.minecraft.acacia_boat": "Akazienholzboot", "entity.minecraft.acacia_chest_boat": "Akazienholztruhenboot", "entity.minecraft.allay": "Hilfsgeisd", "entity.minecraft.area_effect_cloud": "Bardigglwolge", "entity.minecraft.armadillo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.armor_stand": "Rüsdungsschdändorr", "entity.minecraft.arrow": "Pfeil", "entity.minecraft.axolotl": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bamboo_chest_raft": "Bambus-Truhenfloß", "entity.minecraft.bamboo_raft": "Bambusfloß", "entity.minecraft.bat": "Flä<PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Biene", "entity.minecraft.birch_boat": "Birkenholzboot", "entity.minecraft.birch_chest_boat": "Birkenholztruhenboot", "entity.minecraft.blaze": "Lo<PERSON>ä", "entity.minecraft.block_display": "Blockdarsteller", "entity.minecraft.boat": "Boot", "entity.minecraft.bogged": "Sumpfskelett", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Windkugel", "entity.minecraft.camel": "Dr<PERSON><PERSON>", "entity.minecraft.cat": "Miedsekaddse", "entity.minecraft.cave_spider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cherry_boat": "<PERSON>rsch<PERSON>zboot", "entity.minecraft.cherry_chest_boat": "Kirschholztruhenboot", "entity.minecraft.chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.chest_minecart": "Güdorrlore", "entity.minecraft.chicken": "Hühnschn", "entity.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Befählsblogglore", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "Knarz", "entity.minecraft.creaking_transient": "Knarz", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Schwarzeichenholzboot", "entity.minecraft.dark_oak_chest_boat": "Schwarzeichenholztruhenboot", "entity.minecraft.dolphin": "Delfin", "entity.minecraft.donkey": "Esl", "entity.minecraft.dragon_fireball": "Drachn-<PERSON><PERSON><PERSON><PERSON><PERSON>l", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.egg": "Geworfnes Ei", "entity.minecraft.elder_guardian": "Großorr Wäschdorr", "entity.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "Geworfne Enderbärle", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Magior", "entity.minecraft.evoker_fangs": "Fangzähne", "entity.minecraft.experience_bottle": "Geworfnes Erfahrungsfläschschn", "entity.minecraft.experience_orb": "Erfahrungsguchl", "entity.minecraft.eye_of_ender": "Enderooche", "entity.minecraft.falling_block": "Fallendorr Blogg", "entity.minecraft.falling_block_type": "Fallender %s", "entity.minecraft.fireball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.firework_rocket": "Feuorrwergsragede", "entity.minecraft.fishing_bobber": "Schwimmorr", "entity.minecraft.fox": "<PERSON><PERSON>", "entity.minecraft.frog": "<PERSON><PERSON><PERSON>", "entity.minecraft.furnace_minecart": "Ofnlore", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON>", "entity.minecraft.goat": "Z<PERSON><PERSON>", "entity.minecraft.guardian": "Wäschdorr", "entity.minecraft.happy_ghast": "Glücklicher Ghast", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Drischdorrlore", "entity.minecraft.horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.husk": "Wüsdnzombie", "entity.minecraft.illusioner": "Illusionisd", "entity.minecraft.interaction": "Interaktionsobjekt", "entity.minecraft.iron_golem": "Eisngolem", "entity.minecraft.item": "Geschnschdand", "entity.minecraft.item_display": "Gegenstandsdarsteller", "entity.minecraft.item_frame": "<PERSON><PERSON>", "entity.minecraft.jungle_boat": "Tropenholzboot", "entity.minecraft.jungle_chest_boat": "Tropenholztruhenboot", "entity.minecraft.killer_bunny": "<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.leash_knot": "Leinengnodn", "entity.minecraft.lightning_bolt": "Blidds", "entity.minecraft.lingering_potion": "Verweiltrank", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "Lamaschbugge", "entity.minecraft.magma_cube": "Magmawürfl", "entity.minecraft.mangrove_boat": "Mangrovenholzboot", "entity.minecraft.mangrove_chest_boat": "Mangrovenholztruhenboot", "entity.minecraft.marker": "<PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "Lore", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON><PERSON>", "entity.minecraft.oak_boat": "Eichenholzboot", "entity.minecraft.oak_chest_boat": "Eichenholztruhenboot", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Unheilvoller Gegenstands-Spawner", "entity.minecraft.painting": "Gemäldä", "entity.minecraft.pale_oak_boat": "Blasseichenholzboot", "entity.minecraft.pale_oak_chest_boat": "Blasseichenholztruhenboot", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Babagei", "entity.minecraft.phantom": "<PERSON><PERSON><PERSON>", "entity.minecraft.pig": "Schwe<PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "Piglin-Barbar", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.player": "Schbielorr", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.potion": "<PERSON><PERSON>", "entity.minecraft.pufferfish": "G<PERSON>lfisch", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Vorwüsdor", "entity.minecraft.salmon": "Lachs", "entity.minecraft.sheep": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON>er-Geschoss", "entity.minecraft.silverfish": "Silborfischschn", "entity.minecraft.skeleton": "Schgeledd", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.slime": "Schleim", "entity.minecraft.small_fireball": "<PERSON><PERSON><PERSON>", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Schneegolem", "entity.minecraft.snowball": "Schneeball", "entity.minecraft.spawner_minecart": "Spawner-Lore", "entity.minecraft.spectral_arrow": "Schbegdralpfeil", "entity.minecraft.spider": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.splash_potion": "Wurftrank", "entity.minecraft.spruce_boat": "Fichtenholzboot", "entity.minecraft.spruce_chest_boat": "Fichtenholztruhenboot", "entity.minecraft.squid": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.stray": "Eiswandororr", "entity.minecraft.strider": "Schreidorr", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Textdarsteller", "entity.minecraft.tnt": "Gezündedes TNT", "entity.minecraft.tnt_minecart": "TNT-Lore", "entity.minecraft.trader_llama": "Hä<PERSON><PERSON>lam<PERSON>", "entity.minecraft.trident": "<PERSON><PERSON><PERSON>gg", "entity.minecraft.tropical_fish": "Drobnfisch", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "Schworz<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.10": "Halfdorfisch", "entity.minecraft.tropical_fish.predefined.11": "Orangeschdreefn-Faldorrfisch", "entity.minecraft.tropical_fish.predefined.12": "Babageifisch", "entity.minecraft.tropical_fish.predefined.13": "Diadem-Brachdkaisorfisch", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "Rodlibbn-Schleimfisch", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "Drüggorfisch", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.20": "Gelbschwanz-Babageifisch", "entity.minecraft.tropical_fish.predefined.21": "Gelborr <PERSON>ndoggdor", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "Bundbarsch", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Siamesischorr Zuggorrwaddegambffisch", "entity.minecraft.tropical_fish.predefined.7": "Zwergbarsch", "entity.minecraft.tropical_fish.predefined.8": "Kaisorr-Schnabbor", "entity.minecraft.tropical_fish.predefined.9": "Seebarbe", "entity.minecraft.tropical_fish.type.betty": "Beidschnfisch", "entity.minecraft.tropical_fish.type.blockfish": "Bloggfisch", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.dasher": "Flidzor", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "Glidzorfisch", "entity.minecraft.tropical_fish.type.kob": "Dr<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.spotty": "Fleggn-Libbfisch", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Sunnschreefnfisch", "entity.minecraft.turtle": "Schüldgröde", "entity.minecraft.vex": "Blagegeisd", "entity.minecraft.villager": "Dorfbewohnorr", "entity.minecraft.villager.armorer": "Banzormachorr", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "Geisdlischorr", "entity.minecraft.villager.farmer": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "Pfeilmachorr", "entity.minecraft.villager.leatherworker": "Gärborr", "entity.minecraft.villager.librarian": "Bibliothegar", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "Dorfbewohnorr", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "Waffnschmied", "entity.minecraft.vindicator": "<PERSON><PERSON>", "entity.minecraft.wandering_trader": "Fahrendorr Händlorr", "entity.minecraft.warden": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wind_charge": "Windkugel", "entity.minecraft.witch": "<PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Witherschgeledd", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zombie", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "Zombiedorfbewohnorr", "entity.minecraft.zombified_piglin": "Zombifiziordorr <PERSON>", "entity.not_summonable": "Objekt des Typs %s kann nicht erzeugt werden", "event.minecraft.raid": "Üborrfall", "event.minecraft.raid.defeat": "Niedorrl<PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Überfall – Niederlage", "event.minecraft.raid.raiders_remaining": "Vorbleibende Räubor: %s", "event.minecraft.raid.victory": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.victory.full": "Überfall – Sieg", "filled_map.buried_treasure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.explorer_jungle": "Dschungel-Entdeckerkarte", "filled_map.explorer_swamp": "Sumpf-Entdeckerkarte", "filled_map.id": "Nr. %s", "filled_map.level": "(Schdufe %s/%s)", "filled_map.locked": "G<PERSON><PERSON><PERSON><PERSON>", "filled_map.mansion": "<PERSON>ald<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.monument": "Oz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.scale": "Maßschdab 1:%s", "filled_map.trial_chambers": "Prüfungs-Entdeckerkarte", "filled_map.unknown": "Unbegannde Garde", "filled_map.village_desert": "Wüstendorfkarte", "filled_map.village_plains": "Ebenendorfkarte", "filled_map.village_savanna": "Savannendorfkarte", "filled_map.village_snowy": "Schneedorfkarte", "filled_map.village_taiga": "Taigadorfkar<PERSON>", "flat_world_preset.minecraft.bottomless_pit": "Bodnlosorr A<PERSON>grund", "flat_world_preset.minecraft.classic_flat": "Klass’sches Flachland", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.redstone_ready": "Redstone, färdsch, los", "flat_world_preset.minecraft.snowy_kingdom": "Schneekönigreisch", "flat_world_preset.minecraft.the_void": "Das Nüschd", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON><PERSON>uo<PERSON>", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON>r<PERSON><PERSON>", "flat_world_preset.unknown": "???", "gameMode.adventure": "<PERSON><PERSON>uorr<PERSON><PERSON>", "gameMode.changed": "<PERSON><PERSON>hbielmodus wurde zu %s geändorrd", "gameMode.creative": "Greativmodus", "gameMode.hardcore": "Hardcore-Modus!", "gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameMode.survival": "Üborrlebnsmodus", "gamerule.allowFireTicksAwayFromPlayer": "Von <PERSON>n entferntes Feuer aktualisieren", "gamerule.allowFireTicksAwayFromPlayer.description": "<PERSON><PERSON><PERSON>, o<PERSON> <PERSON><PERSON> und Lava in einer Entfernung von mehr als 8 <PERSON><PERSON> von einem Spieler aktualisiert werden", "gamerule.announceAdvancements": "Fordschridde begannd gäbn", "gamerule.blockExplosionDropDecay": "Block‐Explosionsausbeute verschwindet", "gamerule.blockExplosionDropDecay.description": "Einiges von der Ausbeute von <PERSON>, die durch Explosionen aufgrund von Blockinteraktionen zerstört werden, geht dabei verloren.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Ausbeude", "gamerule.category.misc": "Vorschiedenes", "gamerule.category.mobs": "Greadurn", "gamerule.category.player": "Schbielorr", "gamerule.category.spawning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.category.updates": "Weldaggdualisierungn", "gamerule.commandBlockOutput": "Befählsbloggausgabe anzeischn", "gamerule.commandModificationBlockLimit": "Blockgrenze für Befehlsänderungen", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON> der Blöcke, die mit einem Befehl wie „fill“ oder „clone“ gleichzeitig geändert werden können.", "gamerule.disableElytraMovementCheck": "Elydrenflugüborrbrüfung deaggdiviern", "gamerule.disablePlayerMovementCheck": "Spielerbewegung nicht überprüfen", "gamerule.disableRaids": "Üborrfälle deaggdiviern", "gamerule.doDaylightCycle": "Voranschreidn dorr Dageszeid", "gamerule.doEntityDrops": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> falln lassn", "gamerule.doEntityDrops.description": "Gond<PERSON>ier<PERSON> das Fallnlassn dorr Ausbeude von <PERSON> (eenschließlisch Invendarn), <PERSON><PERSON>, <PERSON><PERSON><PERSON> usw.", "gamerule.doFireTick": "Feuorrausbreedung", "gamerule.doImmediateRespawn": "Sofordsch<PERSON><PERSON>orreinschdieg", "gamerule.doInsomnia": "<PERSON><PERSON><PERSON> er<PERSON>n", "gamerule.doLimitedCrafting": "Rezebde zur Herschdellung benödigd", "gamerule.doLimitedCrafting.description": "<PERSON><PERSON>, g<PERSON><PERSON><PERSON>lorr nur bereids freigeschaldede Rezebde nuddsen.", "gamerule.doMobLoot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> falln lassn", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON>, ob Kreaturen ihre Ausbeute fallen lassen (einschließlich Erfahrungskugeln).", "gamerule.doMobSpawning": "Greadurn erzeuschen", "gamerule.doMobSpawning.description": "<PERSON><PERSON>r einige Objekte können eigene Regeln gelten.", "gamerule.doPatrolSpawning": "Plündororr‐Badrouillen erzeuschn", "gamerule.doTileDrops": "Bloggausbeude falln lassn", "gamerule.doTileDrops.description": "<PERSON><PERSON><PERSON>, ob <PERSON><PERSON><PERSON><PERSON> ihre Ausbeute fallen lassen (einschließlich Erfahrungskugeln).", "gamerule.doTraderSpawning": "Fahrende Händlorr erzeuschn", "gamerule.doVinesSpread": "Rankenausbreitung", "gamerule.doVinesSpread.description": "<PERSON><PERSON><PERSON>, ob sich Ranken zufällig auf benachbarte Blöcke ausbreiten können. Wirkt sich nicht auf andere Rankenarten wie Trauerranken, Zwirbelranken usw. aus.", "gamerule.doWardenSpawning": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doWeatherCycle": "Wäddorrweggsl", "gamerule.drowningDamage": "<PERSON><PERSON><PERSON> du<PERSON>", "gamerule.enderPearlsVanishOnDeath": "Enderperlen‐Mitleidenschaft", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON><PERSON><PERSON>, ob vom Spieler geworfene Enderperlen verschwinden, wenn dieser stirbt.", "gamerule.entitiesWithPassengersCanUsePortals": "Portalnutzung durch Objekte mit Passagieren", "gamerule.entitiesWithPassengersCanUsePortals.description": "Ermöglicht es Objekten mit Passagieren, sich durch Nether‐, End‐ und Endtransitportale zu teleportieren.", "gamerule.fallDamage": "Fallschadn", "gamerule.fireDamage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.forgiveDeadPlayers": "Geschdorbenen Schbielorn vorgäbn", "gamerule.forgiveDeadPlayers.description": "Vorärgorde neudrale Greadurn hörn uff, wü<PERSON><PERSON> zu sein, wenn dorr Zielschbielorr in dorr Nähe schdürbd.", "gamerule.freezeDamage": "Erfrierungsschadn", "gamerule.globalSoundEvents": "Weltweite Geräuschereignisse", "gamerule.globalSoundEvents.description": "Wenn bestimmte Spielereignisse eintreten, z. B. das Auftauchen eines Endgegners, ist das Geräusch überall zu hören.", "gamerule.keepInventory": "Invendar nach Dod behaldn", "gamerule.lavaSourceConversion": "Lava‐Erneuerbarkeit", "gamerule.lavaSourceConversion.description": "Wenn fließende Lava auf zwei Seiten von Lavaquellen umgeben ist, wird sie zu einer Quelle.", "gamerule.locatorBar": "Spielerortungsleiste aktivieren", "gamerule.locatorBar.description": "<PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON>, zeigt eine Leiste auf dem Bildschirm die Richtung der Mitspieler an.", "gamerule.logAdminCommands": "Adminisdradorbefehle brodogolliern", "gamerule.maxCommandChainLength": "Oborrgränze für Befählskeddn", "gamerule.maxCommandChainLength.description": "Wird auf Befehlsblockketten und Funktionen angewandt.", "gamerule.maxCommandForkCount": "Obergrenze für Befehlsrahmen", "gamerule.maxCommandForkCount.description": "Maximale Anzahl an Befehlsrahmen, die von Befehlen wie „execute as“ benutzt werden können.", "gamerule.maxEntityCramming": "Oborrgränze für Objegdgedränge", "gamerule.minecartMaxSpeed": "Loren‐Höchstgeschwindigkeit", "gamerule.minecartMaxSpeed.description": "Höchstgeschwindigkeit, die eine Lore an Land standardmäßig erreichen kann.", "gamerule.mobExplosionDropDecay": "Kreatur‐Explosionsausbeute verschwindet", "gamerule.mobExplosionDropDecay.description": "Einige<PERSON> von der Ausbeute von <PERSON>, die durch von Kreaturen verursachte Explosionen zerstört werden, geht dabei verloren.", "gamerule.mobGriefing": "Zorschdörung dursch Greadurn", "gamerule.naturalRegeneration": "Gesundheid regenorriern", "gamerule.playersNetherPortalCreativeDelay": "Netherportal‐Verzögerung im Kreativmodus", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON><PERSON> (in Ticks), die ein Spieler im Kreativmodus in einem Netherportal verbringen muss, bevor dieser die Dimension wechselt.", "gamerule.playersNetherPortalDefaultDelay": "Netherportal‐Verzögerung", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON><PERSON> (in Ticks), die ein Spieler außerhalb des Kreativmodus in einem Netherportal verbringen muss, bevor dieser die Dimension wechselt.", "gamerule.playersSleepingPercentage": "Schläforrandeil", "gamerule.playersSleepingPercentage.description": "<PERSON> Brozendzahl an Schbielorrn, die schlafn müssn, um eene Nachd zu üborrschbringn.", "gamerule.projectilesCanBreakBlocks": "Blockzerstörung durch Geschosse", "gamerule.projectilesCanBreakBlocks.description": "<PERSON><PERSON><PERSON>, ob aufprallende Geschosse Blöcke zerstören, die durch sie zerstörbar wären.", "gamerule.randomTickSpeed": "H<PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>n", "gamerule.reducedDebugInfo": "Debug‐Infos reduziern", "gamerule.reducedDebugInfo.description": "Begränzd de Inhalde dorr Debug‐Anzeische.", "gamerule.sendCommandFeedback": "Befählsausgabe anzeischn", "gamerule.showDeathMessages": "Dodesmeldungn anzeischn", "gamerule.snowAccumulationHeight": "Schneedeckenhöhe", "gamerule.snowAccumulationHeight.description": "<PERSON>n es schneit, entsteht auf dem Boden eine Schneedecke, die höchstens diese Anzahl von <PERSON> erreicht.", "gamerule.spawnChunkRadius": "Einstiegs‐Chunk‐Radius", "gamerule.spawnChunkRadius.description": "Anzahl der Chunks um den Einstiegspunkt in der Oberwelt, die dauerhaft geladen bleiben.", "gamerule.spawnRadius": "<PERSON><PERSON> Wiedorreinschdiegsbereischs", "gamerule.spawnRadius.description": "Steuert die Größe des Bereichs um den Einstiegspunkt, in dem Spieler in die Welt einsteigen können.", "gamerule.spectatorsGenerateChunks": "<PERSON><PERSON><PERSON><PERSON><PERSON> erzeuschn Landschafd", "gamerule.tntExplodes": "Entzünden und Explodieren von TNT", "gamerule.tntExplosionDropDecay": "TNT‐Explosionsausbeute verschwindet", "gamerule.tntExplosionDropDecay.description": "Einiges von der Ausbeute von <PERSON>, die durch TNT‐Explosionen zerstört werden, geht dabei verloren.", "gamerule.universalAnger": "Allgemeine Vorärschorung", "gamerule.universalAnger.description": "Vorärschorde neudrale Greadurn greifn jedn Schbielorr in dorr Nähe an – nisch nur den Schbielorr, der se vorärschord had. Fungtschonierd am besdn, wenn forgiveDeadPlayers deaggdivierd is.", "gamerule.waterSourceConversion": "Wasser‐Erneuerbarkeit", "gamerule.waterSourceConversion.description": "<PERSON>n fließendes Wasser auf zwei Seiten von Wasserquellen umgeben ist, wird es zu einer Quelle.", "generator.custom": "<PERSON><PERSON><PERSON><PERSON>", "generator.customized": "Ald angebassd", "generator.minecraft.amplified": "ZORGLÜFDED", "generator.minecraft.amplified.info": "Hinwees: <PERSON><PERSON> zum <PERSON>! Erfordorrd een schdargn Reschnorr.", "generator.minecraft.debug_all_block_states": "Debug-Modus", "generator.minecraft.flat": "Flachland", "generator.minecraft.large_biomes": "Große Biome", "generator.minecraft.normal": "Schdandard", "generator.minecraft.single_biome_surface": "Eenslnes Biom", "generator.single_biome_caves": "<PERSON><PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "Schwebende Inseln", "gui.abuseReport.attestation": "Mit der Einreichung dieser Meldung versicherst du, dass deine Angaben wahrheitsgetreu und vollständig sind – nach bestem Wissen.", "gui.abuseReport.comments": "Anmerkungen", "gui.abuseReport.describe": "<PERSON> Angabe von Einzelheiten hilft uns dabei, eine fundierte Entscheidung zu treffen.", "gui.abuseReport.discard.content": "Wenn du die Seite verlässt, gehen diese Meldung und deine Anmerkungen verloren.\n<PERSON><PERSON> du sicher, dass du sie verlassen möchtest?", "gui.abuseReport.discard.discard": "Verlassen und Meldung verwerfen", "gui.abuseReport.discard.draft": "Als Entwurf speichern", "gui.abuseReport.discard.return": "Bearbeitung fortsetzen", "gui.abuseReport.discard.title": "Meldung und Anmerkungen verwerfen?", "gui.abuseReport.draft.content": "Möchtest du die vorhandene Meldung weiterbearbeiten oder sie verwerfen und eine neue Meldung erstellen?", "gui.abuseReport.draft.discard": "Verwerfen", "gui.abuseReport.draft.edit": "Bearbeitung fortsetzen", "gui.abuseReport.draft.quittotitle.content": "Möchtest du ihn weiterbearbeiten oder verwerfen?", "gui.abuseReport.draft.quittotitle.title": "Du hast einen Meldeentwurf, der verloren geht, wenn du das Spiel verlässt.", "gui.abuseReport.draft.title": "Meldeentwurf bearbeiten?", "gui.abuseReport.error.title": "<PERSON><PERSON><PERSON><PERSON> beim <PERSON>n dei<PERSON>", "gui.abuseReport.message": "Wo hast du das unerwünschte Verhalten wahrgenommen?\nDiese Auswahl erleichtert es uns, deinen Vorgang zu un<PERSON>uchen.", "gui.abuseReport.more_comments": "<PERSON><PERSON> schildere, was vorgefallen ist:", "gui.abuseReport.name.comment_box_label": "<PERSON><PERSON> schildere, we<PERSON><PERSON><PERSON> du diesen Profilnamen melden möchtest:", "gui.abuseReport.name.reporting": "Du meldest „%s“.", "gui.abuseReport.name.title": "Unangemessenen Profilnamen des Spielers melden", "gui.abuseReport.observed_what": "Was ist der Grund für die Meldung?", "gui.abuseReport.read_info": "<PERSON><PERSON><PERSON><PERSON> mehr zum Melden", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Droochn odorr Algohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "<PERSON><PERSON> ermu<PERSON>gt andere, sich an rechtswidrigen drogenbezogenen Aktivitäten zu beteiligen, oder regt Minderjährige zum Alkoholkonsum an.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Seggsuelle Ausbeudung odorr <PERSON><PERSON><PERSON>", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON><PERSON> schbrischd üborr anschdößsches Vorrhaldn geschnüborr Kindorrn odorr fördorrd es uff and<PERSON>.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Vorrleumdung, Idendidädsdiebschdahl odorr Falschinformatschonen", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "<PERSON><PERSON> schä<PERSON>gt deinen Ruf oder jenen einer anderen Person, zum Beispiel durch die Verbreitung von Falschinformationen mit dem Ziel, andere zu täuschen oder zu betrügen.", "gui.abuseReport.reason.description": "Beschreibung:", "gui.abuseReport.reason.false_reporting": "Falschmeldung", "gui.abuseReport.reason.generic": "Ich möchte diesen Spieler melden", "gui.abuseReport.reason.generic.description": "Ich ärgere mich über diesen Spieler bzw. dieser hat etwas getan, was mir nicht gefällt.", "gui.abuseReport.reason.harassment_or_bullying": "Beläsdschung odorr <PERSON>", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON>, attack<PERSON><PERSON> oder schikaniert dich oder eine andere Person. Dies gilt auch, wenn jemand wied<PERSON><PERSON>t versucht, mit dir oder einer anderen Person ungefragt in Verbindung zu treten oder persönliche Privatinformationen über dich oder eine andere Person ungefragt zu veröffentlichen („Doxen“).", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.hate_speech.description": "<PERSON><PERSON> greift dich oder einen anderen Spieler aufgrund von Merkmalen seiner Identität wie Religion, Ethnie oder Sexualität an.", "gui.abuseReport.reason.imminent_harm": "<PERSON><PERSON><PERSON><PERSON><PERSON> – <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "gui.abuseReport.reason.imminent_harm.description": "<PERSON><PERSON> d<PERSON>ht damit, dir oder jemand anderem im wirklichen Leben Schaden zuzufügen.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Indime Bildorr ohne Eenvorrschdändnis", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON> spricht über private und intime Bilder, gibt sie weiter oder macht sie auf andere Weise zugänglich.", "gui.abuseReport.reason.self_harm_or_suicide": "<PERSON><PERSON><PERSON><PERSON><PERSON> – Selbsdvorrledsung odorr Selbsdmord", "gui.abuseReport.reason.self_harm_or_suicide.description": "<PERSON><PERSON> droht damit, sich selbst im wirklichen Leben zu verletzen, oder sp<PERSON>t davon, das zu tun.", "gui.abuseReport.reason.sexually_inappropriate": "<PERSON><PERSON><PERSON>", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins, die sexuelle Handlungen, Geschlechtsorgane und/oder sexuelle Gewalt grafisch darstellen.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Derrorismus odorr gewalddädschorr Eggsdremismus", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON> spricht über Terrorismus oder gewalttätigen Extremismus aus politischen, religiösen, ideologischen oder anderen Gründen, fö<PERSON><PERSON> ihn oder droht damit, ihn zu verüben.", "gui.abuseReport.reason.title": "Meldegadegorie auswähln", "gui.abuseReport.report_sent_msg": "Wir haben deine Meldung erfolgreich erhalten. Danke!\n\nUnser Team wird sich möglichst zeitnah darum kümmern.", "gui.abuseReport.select_reason": "Meldekategorie auswählen", "gui.abuseReport.send": "<PERSON><PERSON><PERSON> absenden", "gui.abuseReport.send.comment_too_long": "Bitte kürze die Anmerkung.", "gui.abuseReport.send.error_message": "Beim Senden deiner Meldung ist ein Fehler aufgetreten:\n‚%s‘", "gui.abuseReport.send.generic_error": "<PERSON>im <PERSON>en deiner Meldung ist ein unerwarteter Fehler aufgetreten.", "gui.abuseReport.send.http_error": "Beim <PERSON> deiner Meldung ist ein unerwarteter HTTP‐Fehler aufgetreten.", "gui.abuseReport.send.json_error": "Beim <PERSON>en deiner Meldung wurden fehlerhafte Nutzdaten festgestellt.", "gui.abuseReport.send.no_reason": "Bitte wähle eine Meldekategorie.", "gui.abuseReport.send.not_attested": "Bitte lies die obige Aussage und hake das Kontrollkästchen ab, um die Meldung absenden zu können.", "gui.abuseReport.send.service_unavailable": "Der Dienst zur Meldung von Fehlverhalten kann nicht erreicht werden. <PERSON>te stelle sicher, dass du mit dem Internet verbunden bist, und versuche es erneut.", "gui.abuseReport.sending.title": "<PERSON><PERSON> wird gesended ...", "gui.abuseReport.sent.title": "<PERSON><PERSON><PERSON> gesended", "gui.abuseReport.skin.title": "Skin des Spielers melden", "gui.abuseReport.title": "<PERSON><PERSON><PERSON> melden", "gui.abuseReport.type.chat": "Chatnachrichten", "gui.abuseReport.type.name": "Profilname", "gui.abuseReport.type.skin": "Skin", "gui.acknowledge": "Beschdädschn", "gui.advancements": "Fordschridde", "gui.all": "Alle", "gui.back": "<PERSON><PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\nErfahre mehr undorr folchndem Link: %s", "gui.banned.description.permanent": "<PERSON><PERSON> is da<PERSON>rr<PERSON><PERSON>d gesch<PERSON><PERSON>, was bedeuded, dassde weder online schbieln noch Realms beidredn kannsd.", "gui.banned.description.reason": "Wir haben kürzlich eine Meldung über schlechtes Verhalten von deinem Konto erhalten. Unsere Moderatoren haben deinen Fall nun überprüft und als %s erkannt, was gegen die Minecraft‐Community‐Standards verstößt.", "gui.banned.description.reason_id": "Code: %s", "gui.banned.description.reason_id_message": "Code: %s – %s", "gui.banned.description.temporary": "%s Bis dahin kannsde weder online schbieln noch Realms beidredn.", "gui.banned.description.temporary.duration": "<PERSON><PERSON> is vorrüborrgehnd geschberrd und wird in %s wiedorr aggdivierd.", "gui.banned.description.unknownreason": "Wir haben kürzlich eine Meldung über schlechtes Verhalten von deinem Konto erhalten. Unsere Moderatoren haben deinen Fall nun überprüft und festgestellt, dass er gegen die Minecraft‐Community‐Standards verstößt.", "gui.banned.name.description": "Dein derzeitiger Profilname „%s“ verstößt gegen unsere Community‐Standards. Du kannst im Einzelspielermodus spielen, musst aber deinen Profilnamen ändern, um online spielen zu können.\n\nErfahre mehr unter dem folgenden Link oder reiche dort eine Einzelfallprüfung ein: %s", "gui.banned.name.title": "Profilname im Mehrspielermodus nicht erlaubt", "gui.banned.reason.defamation_impersonation_false_information": "Identitätsdiebstahl oder Verbreitung von Informationen, um andere zu täuschen oder zu betrügen", "gui.banned.reason.drugs": "Erwähnung rechtswidriger Drogen", "gui.banned.reason.extreme_violence_or_gore": "Darstellungen von exzessiver Gewalt oder blutigen Szenen aus dem wirklichen Leben", "gui.banned.reason.false_reporting": "Übermäßig viele falsche oder ungenaue Meldungen", "gui.banned.reason.fraud": "Betrügerische Aneignung oder Verwertung von Inhalten", "gui.banned.reason.generic_violation": "Verletzung der Community‐Standards", "gui.banned.reason.harassment_or_bullying": "Beleidigende Sprache, die in gezielt verletzender Weise verwendet wurde", "gui.banned.reason.hate_speech": "Hassrede oder Diskriminierung", "gui.banned.reason.hate_terrorism_notorious_figure": "Erwä<PERSON><PERSON> von Hassgruppen, terroristischen Vereinigungen oder gefährlichen Persönlichkeiten", "gui.banned.reason.imminent_harm_to_person_or_property": "<PERSON><PERSON><PERSON><PERSON>, Personen oder Eigentum im wirklichen Leben zu schädigen", "gui.banned.reason.nudity_or_pornography": "Darstellung von anzüglichem oder pornografischem Material", "gui.banned.reason.sexually_inappropriate": "Themen oder Inhalte mit sexuellem Bezug", "gui.banned.reason.spam_or_advertising": "Spam oder Werbung", "gui.banned.skin.description": "Dein derzeitiger Skin verstößt gegen unsere Community‐Standards. Du kannst mit einem Standard‐Skin weiterspielen oder einen neuen Skin auswählen.\n\nErfahre mehr unter dem folgenden Link oder reiche dort eine Einzelfallprüfung ein: %s", "gui.banned.skin.title": "<PERSON> nicht erlaubt", "gui.banned.title.permanent": "Gondo da<PERSON><PERSON><PERSON><PERSON>d gesch<PERSON>rd", "gui.banned.title.temporary": "Gondo vorrüborrgehend geschberrd", "gui.cancel": "Abbräschn", "gui.chatReport.comments": "Anmerkungen", "gui.chatReport.describe": "<PERSON>dn würd uns helfn, eene fundierde Endscheidung zu dreffn.", "gui.chatReport.discard.content": "<PERSON><PERSON> de die Se<PERSON> vorrlä<PERSON>t, gehn diese Meldung un deine Gommendare vorrlorrn.\n<PERSON><PERSON><PERSON> si<PERSON>, dass du se vorrlassn willsd?", "gui.chatReport.discard.discard": "Vorrlassn un Meldung vorrwerfn", "gui.chatReport.discard.draft": "Als Entwurf speichern", "gui.chatReport.discard.return": "Bearbeidung fordseddsn", "gui.chatReport.discard.title": "Meldung un Gommendare vorrwerfn?", "gui.chatReport.draft.content": "Möchtest du die vorhandene Meldung weiterbearbeiten oder sie verwerfen und eine neue Meldung erstellen?", "gui.chatReport.draft.discard": "Vorrwerfn", "gui.chatReport.draft.edit": "Bearbeidung fordseddsn", "gui.chatReport.draft.quittotitle.content": "<PERSON>öschdes<PERSON> ihn weidorr bearbeidn odorr vorrwerfn?", "gui.chatReport.draft.quittotitle.title": "Du hast einen Entwurf einer Chatmeldung, der verloren geht, wenn du das Spiel verlässt.", "gui.chatReport.draft.title": "Entwurf der Chatmeldung bearbeiten?", "gui.chatReport.more_comments": "<PERSON><PERSON><PERSON>, was bassierd is:", "gui.chatReport.observed_what": "Warum meldesde das?", "gui.chatReport.read_info": "Erfahre mehr üborr das Meldn", "gui.chatReport.report_sent_msg": "Wir ham deine Meldung erfulgreisch erhaldn. Dange!\n\nUnsorr Team wird se so schnell wie möglisch üborrprüfn.", "gui.chatReport.select_chat": "Zu meldende Chatnachrischdn auswähln", "gui.chatReport.select_reason": "Meldegadegorie auswähln", "gui.chatReport.selected_chat": "%s Chatnachrischd(n) zum Meldn ausgewähld", "gui.chatReport.send": "<PERSON><PERSON><PERSON> sendn", "gui.chatReport.send.comments_too_long": "Bidde kürdse den Gommendar", "gui.chatReport.send.no_reason": "B<PERSON>de wähle eene Meldegadegorie", "gui.chatReport.send.no_reported_messages": "<PERSON>te wähle mindesdens eene Chatnachrischd zum Meldn aus", "gui.chatReport.send.too_many_messages": "<PERSON>uch<PERSON><PERSON>, zu viele Nachrischdn in de Meldung uffzunehm", "gui.chatReport.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> meldn", "gui.chatSelection.context": "Diese Auswahl umgebende Nachrischdn wurdn uffgenomm, um zusäddslisch Gondeggsd zu biedn", "gui.chatSelection.fold": "%s Nachrischd(n) vorrschdeggd", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s ist dem Chat beigetreten", "gui.chatSelection.message.narrate": "%s sagde: %s um %s", "gui.chatSelection.selected": "%s/%s Nachrischd(n) ausgewähld", "gui.chatSelection.title": "Zu meldende Chatnachrischdn auswähln", "gui.continue": "Fordfahrn", "gui.copy_link_to_clipboard": "<PERSON>", "gui.days": "%s <PERSON>ach(e)", "gui.done": "<PERSON><PERSON><PERSON><PERSON>", "gui.down": "<PERSON><PERSON><PERSON>r", "gui.entity_tooltip.type": "Dyp: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s <PERSON><PERSON> wurden abgelehnt", "gui.fileDropFailure.title": "Hinzufügen der Dateien fehlgeschlagen", "gui.hours": "%s Schdunde(n)", "gui.loadingMinecraft": "Minecraft wird geladen", "gui.minutes": "%s Minude(n)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s Gnobb", "gui.narrate.editBox": "%s Bearbeidungsfeld: %s", "gui.narrate.slider": "%s Schiebereschlorr", "gui.narrate.tab": "%s‐Reiter", "gui.no": "<PERSON><PERSON>", "gui.none": "<PERSON><PERSON>", "gui.ok": "<PERSON><PERSON><PERSON>", "gui.open_report_dir": "Berichtsverzeichnis öffnen", "gui.proceed": "Fordfahrn", "gui.recipebook.moreRecipes": "Reschdsgligg für mähr", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "<PERSON>e …", "gui.recipebook.toggleRecipes.all": "Alles würd angezeischd", "gui.recipebook.toggleRecipes.blastable": "Hersch<PERSON>bar<PERSON> würd angeze<PERSON>d", "gui.recipebook.toggleRecipes.craftable": "Hersch<PERSON>bar<PERSON> würd angeze<PERSON>d", "gui.recipebook.toggleRecipes.smeltable": "Hersch<PERSON>bar<PERSON> würd angeze<PERSON>d", "gui.recipebook.toggleRecipes.smokable": "Hersch<PERSON>bar<PERSON> würd angeze<PERSON>d", "gui.report_to_server": "<PERSON> den <PERSON> melden", "gui.socialInteractions.blocking_hint": "Mid Microsoft-Gondo vorwaldn", "gui.socialInteractions.empty_blocked": "Keene bloggierdn Schbielorr im Chat", "gui.socialInteractions.empty_hidden": "<PERSON><PERSON>lorr im Chat ausgebländed", "gui.socialInteractions.hidden_in_chat": "Chatnachrischdn von %s wärdn ausgebländed", "gui.socialInteractions.hide": "<PERSON><PERSON> au<PERSON>blä<PERSON>n", "gui.socialInteractions.narration.hide": "Na<PERSON>ris<PERSON>dn von %s ausblendn", "gui.socialInteractions.narration.report": "Schbielorr %s meldn", "gui.socialInteractions.narration.show": "Na<PERSON>rischdn von %s anzeischn", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "<PERSON><PERSON> kunnde <PERSON> Schbielorr mid diesm Nam gefundn wärdn", "gui.socialInteractions.search_hint": "<PERSON>e …", "gui.socialInteractions.server_label.multiple": "%s - %s Schbielorr", "gui.socialInteractions.server_label.single": "%s - %s Schbielorr", "gui.socialInteractions.show": "<PERSON><PERSON>", "gui.socialInteractions.shown_in_chat": "Chatnachrischdn von %s wärdn angezeischd", "gui.socialInteractions.status_blocked": "Bloggierd", "gui.socialInteractions.status_blocked_offline": "Bloggierd – Offline", "gui.socialInteractions.status_hidden": "Vorschdeggd", "gui.socialInteractions.status_hidden_offline": "Ausgebländed – Offline", "gui.socialInteractions.status_offline": "Offline", "gui.socialInteractions.tab_all": "Alle", "gui.socialInteractions.tab_blocked": "Bloggierd", "gui.socialInteractions.tab_hidden": "Vorschdeggd", "gui.socialInteractions.title": "Soodsschale Indoraggtschonen", "gui.socialInteractions.tooltip.hide": "Nachrischdn ausblendn", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON><PERSON><PERSON> meldn", "gui.socialInteractions.tooltip.report.disabled": "<PERSON>rr <PERSON>d is nisch vorrfügbar", "gui.socialInteractions.tooltip.report.no_messages": "<PERSON><PERSON> Na<PERSON>dn von Schbielorr %s, die gemelded wärdn könn", "gui.socialInteractions.tooltip.report.not_reportable": "<PERSON><PERSON><PERSON> Schbielorr kann nisch gemelded wärdn, da seine Chatnachrischdn uff diesm Sörvorr nisch üborrprüfd wärdn könn", "gui.socialInteractions.tooltip.show": "Nachrischdn anzeischn", "gui.stats": "Schdadisdign", "gui.toMenu": "Zurügg zur Sörvorrauswahl", "gui.toRealms": "Zurück zur Realm‐Auswahl", "gui.toTitle": "Zurügg zum Didlbildschürm", "gui.toWorld": "Zurück zur Weltauswahl", "gui.togglable_slot": "<PERSON><PERSON><PERSON>, um Inventarplatz zu sperren", "gui.up": "<PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "Zurück (%s s)", "gui.waitingForResponse.title": "<PERSON>f <PERSON> warten", "gui.yes": "<PERSON>u", "hanging_sign.edit": "Hängeschüldbeschrifdung bearbeidn", "instrument.minecraft.admire_goat_horn": "Bewundorrn", "instrument.minecraft.call_goat_horn": "Rufn", "instrument.minecraft.dream_goat_horn": "Dräumn", "instrument.minecraft.feel_goat_horn": "F<PERSON><PERSON>n", "instrument.minecraft.ponder_goat_horn": "Nachdengn", "instrument.minecraft.seek_goat_horn": "<PERSON>n", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "Sehn’n", "inventory.binSlot": "Geschnschdand zorrdebborrn", "inventory.hotbarInfo": "Schnellzugriffsleisde mid %1$s+%2$s schbeischorrn", "inventory.hotbarSaved": "Schnellzugriffsleisde geschbeischorrd (mid %1$s+%2$s wiedorrherschdelln)", "item.canBreak": "<PERSON>nn abbaun:", "item.canPlace": "<PERSON>nn bladdsierd wärdn uff:", "item.canUse.unknown": "Unbekannt", "item.color": "Forbe: %s", "item.components": "%s Komponente(n)", "item.disabled": "Deaktivierter Gegenstand", "item.durability": "Haldbarkeed: %s/%s", "item.dyed": "Gefärbd", "item.minecraft.acacia_boat": "Agazienhulzbood", "item.minecraft.acacia_chest_boat": "Agazienhulzdr<PERSON>nbood", "item.minecraft.allay_spawn_egg": "Hilfsgeisd-Schborn-Ei", "item.minecraft.amethyst_shard": "Amethystschärbe", "item.minecraft.angler_pottery_shard": "Angler-T<PERSON>pferscherbe", "item.minecraft.angler_pottery_sherd": "Angler-T<PERSON>pferscherbe", "item.minecraft.apple": "Abbl", "item.minecraft.archer_pottery_shard": "Schützen-Töpferscherbe", "item.minecraft.archer_pottery_sherd": "Schützen-Töpferscherbe", "item.minecraft.armadillo_scute": "Gürteltier-Hornschild", "item.minecraft.armadillo_spawn_egg": "Gürteltier-Spawn-Ei", "item.minecraft.armor_stand": "Rüsdungsschdändorr", "item.minecraft.arms_up_pottery_shard": "Gebärden-Töpferscherbe", "item.minecraft.arms_up_pottery_sherd": "Gebärden-Töpferscherbe", "item.minecraft.arrow": "Pfeil", "item.minecraft.axolotl_bucket": "<PERSON><PERSON><PERSON>dlehmorr", "item.minecraft.axolotl_spawn_egg": "Aggsolodl-Schborn-Ei", "item.minecraft.baked_potato": "Ofngardoffl", "item.minecraft.bamboo_chest_raft": "Bambus-Truhenfloß", "item.minecraft.bamboo_raft": "Bambusfloß", "item.minecraft.bat_spawn_egg": "Flädorrmaus-Schborn-Ei", "item.minecraft.bee_spawn_egg": "Bien-Schborn-Ei", "item.minecraft.beef": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot": "<PERSON><PERSON>", "item.minecraft.beetroot_seeds": "Rode-Beede-Samn", "item.minecraft.beetroot_soup": "<PERSON><PERSON>dsch", "item.minecraft.birch_boat": "Bürschnhulzbood", "item.minecraft.birch_chest_boat": "B<PERSON>rschnhulzdruhnbood", "item.minecraft.black_bundle": "Schwarzes Bündel", "item.minecraft.black_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.black_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blade_pottery_shard": "Klingen-Töpferscherbe", "item.minecraft.blade_pottery_sherd": "Klingen-Töpferscherbe", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_spawn_egg": "Lohän-Schborn-Ei", "item.minecraft.blue_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blue_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "Sumpfskelett-Spawn-Ei", "item.minecraft.bolt_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolzen-Rüstungsbesatz", "item.minecraft.bone": "Gnochn", "item.minecraft.bone_meal": "Gnochnmehl", "item.minecraft.book": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "Spickelbord-Bannervorlage", "item.minecraft.bow": "<PERSON><PERSON><PERSON>", "item.minecraft.bowl": "Schüssl", "item.minecraft.bread": "Broud", "item.minecraft.breeze_rod": "Böenrute", "item.minecraft.breeze_spawn_egg": "Böen-Spawn-Ei", "item.minecraft.brewer_pottery_shard": "B<PERSON>uer-Töpferscherbe", "item.minecraft.brewer_pottery_sherd": "B<PERSON>uer-Töpferscherbe", "item.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON><PERSON> Bündel", "item.minecraft.brown_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.brown_egg": "<PERSON><PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON>", "item.minecraft.brush": "<PERSON><PERSON>l", "item.minecraft.bucket": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty": "<PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Nimmt einen gemischten Stapel Gegenstände auf", "item.minecraft.bundle.full": "Voll", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Flammen-Töpferscherbe", "item.minecraft.burn_pottery_sherd": "Flammen-Töpferscherbe", "item.minecraft.camel_spawn_egg": "Dromedar-Spawn-Ei", "item.minecraft.carrot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cat_spawn_egg": "Miedsekaddsn-Schborn-Ei", "item.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Höhlnschbinn-Schborn-Ei", "item.minecraft.chainmail_boots": "Keddnlaadschn", "item.minecraft.chainmail_chestplate": "Keddnhemd", "item.minecraft.chainmail_helmet": "Keddnhaube", "item.minecraft.chainmail_leggings": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_boat": "<PERSON>rsch<PERSON>zboot", "item.minecraft.cherry_chest_boat": "Kirschholztruhenboot", "item.minecraft.chest_minecart": "Güdorrlore", "item.minecraft.chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "Hühnschn-Schborn-Ei", "item.minecraft.chorus_fruit": "Chorusfruchd", "item.minecraft.clay_ball": "Donglumbn", "item.minecraft.clock": "<PERSON><PERSON>", "item.minecraft.coal": "<PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.coast_armor_trim_smithing_template.new": "Küsten-Rüstungsbesatz", "item.minecraft.cocoa_beans": "Gaggaubohn", "item.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod_spawn_egg": "Gabljau-<PERSON><PERSON><PERSON>-<PERSON>i", "item.minecraft.command_block_minecart": "Befählsblogglore", "item.minecraft.compass": "Gombass", "item.minecraft.cooked_beef": "Schdeak", "item.minecraft.cooked_chicken": "Gebradnes H<PERSON>schn", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_mutton": "Gebradnes <PERSON>lf<PERSON>sch", "item.minecraft.cooked_porkchop": "Geb<PERSON><PERSON>", "item.minecraft.cooked_rabbit": "Gebrad<PERSON> G<PERSON>l", "item.minecraft.cooked_salmon": "Gebradnorr <PERSON>", "item.minecraft.cookie": "Gehgs", "item.minecraft.copper_ingot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cow_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "item.minecraft.creaking_spawn_egg": "Knarz-Spawn-Ei", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per", "item.minecraft.creeper_banner_pattern.new": "Creeper-<PERSON><PERSON>", "item.minecraft.creeper_spawn_egg": "Creeper-Schborn-Ei", "item.minecraft.crossbow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.crossbow.projectile": "Geschoss:", "item.minecraft.crossbow.projectile.multiple": "Geschoss: %s× %s", "item.minecraft.crossbow.projectile.single": "Geschoss: %s", "item.minecraft.cyan_bundle": "Türkises Bündel", "item.minecraft.cyan_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cyan_harness": "Türkises Geschirr", "item.minecraft.danger_pottery_shard": "Gefahren-Töpferscherbe", "item.minecraft.danger_pottery_sherd": "Gefahren-Töpferscherbe", "item.minecraft.dark_oak_boat": "Schworzeischnhulzbood", "item.minecraft.dark_oak_chest_boat": "Schworzeischnhulzdruhnbood", "item.minecraft.debug_stick": "Debug-Schdogg", "item.minecraft.debug_stick.empty": "%s had keene Bloggzuschdände", "item.minecraft.debug_stick.select": "‚%s‘ ausgewähld (%s)", "item.minecraft.debug_stick.update": "‚%s‘ is jäddse %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "Diamandaggsd", "item.minecraft.diamond_boots": "Diamandlaadschn", "item.minecraft.diamond_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_leggings": "Diamandbee<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "Diamands<PERSON><PERSON>e", "item.minecraft.diamond_shovel": "Diamandschibbe", "item.minecraft.diamond_sword": "Diamandschwerd", "item.minecraft.disc_fragment_5": "Bladdnbruchschdügg", "item.minecraft.disc_fragment_5.desc": "Schallbladde - 5", "item.minecraft.dolphin_spawn_egg": "Delfin-Schborn-Ei", "item.minecraft.donkey_spawn_egg": "Esl-Schborn-Ei", "item.minecraft.dragon_breath": "Drachnadem", "item.minecraft.dried_kelp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.drowned_spawn_egg": "Erdrungnen-Schborn-Ei", "item.minecraft.dune_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.dune_armor_trim_smithing_template.new": "Dünen-Rüstungsbesatz", "item.minecraft.echo_shard": "Echoscherbe", "item.minecraft.egg": "<PERSON>i", "item.minecraft.elder_guardian_spawn_egg": "Großorr-Wäschdorr-Schborn-Ei", "item.minecraft.elytra": "<PERSON><PERSON>n", "item.minecraft.emerald": "Smaragd", "item.minecraft.enchanted_book": "Vorzauborde Schwahrde", "item.minecraft.enchanted_golden_apple": "Vorzaubordorr goldnorr Abbl", "item.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_dragon_spawn_egg": "Enderdrachn-Schborn-Ei", "item.minecraft.ender_eye": "Enderooche", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON>-Schborn-Ei", "item.minecraft.endermite_spawn_egg": "Endermiten-Schborn-Ei", "item.minecraft.evoker_spawn_egg": "Magiorr-Sc<PERSON>born-Ei", "item.minecraft.experience_bottle": "Erfahrungsfläschschn", "item.minecraft.explorer_pottery_shard": "Entdecker-Töpferscherbe", "item.minecraft.explorer_pottery_sherd": "Entdecker-Töpferscherbe", "item.minecraft.eye_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.eye_armor_trim_smithing_template.new": "Augen-Rüstungsbesatz", "item.minecraft.feather": "Fädorr", "item.minecraft.fermented_spider_eye": "Fermendierdes Schbin<PERSON>oche", "item.minecraft.field_masoned_banner_pattern": "Mauerung-Bannervorlage", "item.minecraft.filled_map": "Garde", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_rocket": "Feuorrwergsragede", "item.minecraft.firework_rocket.flight": "Flugdauorr:", "item.minecraft.firework_rocket.multiple_stars": "%s× %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Feuorrwergsschdern", "item.minecraft.firework_star.black": "Schworz", "item.minecraft.firework_star.blue": "Blau", "item.minecraft.firework_star.brown": "<PERSON>", "item.minecraft.firework_star.custom_color": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.cyan": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON><PERSON><PERSON> zu", "item.minecraft.firework_star.flicker": "Fungln", "item.minecraft.firework_star.gray": "G<PERSON><PERSON>", "item.minecraft.firework_star.green": "Grien", "item.minecraft.firework_star.light_blue": "Hellblau", "item.minecraft.firework_star.light_gray": "Hellgrau", "item.minecraft.firework_star.lime": "Hellgrien", "item.minecraft.firework_star.magenta": "Magenda", "item.minecraft.firework_star.orange": "Orangsch", "item.minecraft.firework_star.pink": "<PERSON>", "item.minecraft.firework_star.purple": "<PERSON>", "item.minecraft.firework_star.red": "Rod", "item.minecraft.firework_star.shape": "Unbegannde Form", "item.minecraft.firework_star.shape.burst": "Eggsblosion", "item.minecraft.firework_star.shape.creeper": "Creeperförmisch", "item.minecraft.firework_star.shape.large_ball": "Große Guchl", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.star": "Schdernförmisch", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.yellow": "<PERSON><PERSON><PERSON>", "item.minecraft.fishing_rod": "<PERSON><PERSON>", "item.minecraft.flint": "Feuorrschdeen", "item.minecraft.flint_and_steel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.flow_armor_trim_smithing_template.new": "Fluss-Rüstungsbesatz", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.desc": "Fluss", "item.minecraft.flow_banner_pattern.new": "Fluss-<PERSON><PERSON><PERSON>", "item.minecraft.flow_pottery_sherd": "Fluss-Töpferscherbe", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "Blumen-<PERSON><PERSON><PERSON>", "item.minecraft.flower_pot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.fox_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "item.minecraft.friend_pottery_shard": "Freundes-Töpferscherbe", "item.minecraft.friend_pottery_sherd": "Freundes-Töpferscherbe", "item.minecraft.frog_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>born-Ei", "item.minecraft.furnace_minecart": "Ofnlore", "item.minecraft.ghast_spawn_egg": "Ghast-Schborn-Ei", "item.minecraft.ghast_tear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "Glidzornde Melonscheibe", "item.minecraft.globe_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.desc": "Globus", "item.minecraft.globe_banner_pattern.new": "Globus-Bannervorlage", "item.minecraft.glow_berries": "Leuschdbeern", "item.minecraft.glow_ink_sac": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_squid_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-Schborn-E<PERSON>", "item.minecraft.glowstone_dust": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>daub", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Zieschn-Schborn-Ei", "item.minecraft.gold_ingot": "Goldbarrn", "item.minecraft.gold_nugget": "Goldglumbn", "item.minecraft.golden_apple": "Goldnorr Abbl", "item.minecraft.golden_axe": "Goldaggsd", "item.minecraft.golden_boots": "Goldlaadschn", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_horse_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_leggings": "Goldbeenschudds", "item.minecraft.golden_pickaxe": "Goldsch<PERSON>dshagge", "item.minecraft.golden_shovel": "Goldschibbe", "item.minecraft.golden_sword": "Goldschwerd", "item.minecraft.gray_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guardian_spawn_egg": "Wäschdorr-Schborn-Ei", "item.minecraft.gunpowder": "Schworzbulvorr", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.desc": "Windstoßer", "item.minecraft.guster_banner_pattern.new": "Windstoßer-Bannervorlage", "item.minecraft.guster_pottery_sherd": "Windstoßer-Töpferscherbe", "item.minecraft.happy_ghast_spawn_egg": "Glücklicher-Ghast-Spawn-Ei", "item.minecraft.harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Härds des Meeres", "item.minecraft.heart_pottery_shard": "Herz-Töpferscherbe", "item.minecraft.heart_pottery_sherd": "Herz-Töpferscherbe", "item.minecraft.heartbreak_pottery_shard": "Herzschmerz-Töpferscherbe", "item.minecraft.heartbreak_pottery_sherd": "Herzschmerz-Töpferscherbe", "item.minecraft.hoglin_spawn_egg": "Hoglin-Schborn-Ei", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.honeycomb": "Honischwab<PERSON>", "item.minecraft.hopper_minecart": "Drischdorrlore", "item.minecraft.horse_spawn_egg": "Färde-Schborn-Ei", "item.minecraft.host_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.host_armor_trim_smithing_template.new": "Gastwirts-Rüstungsbesatz", "item.minecraft.howl_pottery_shard": "Geheul-Töpferscherbe", "item.minecraft.howl_pottery_sherd": "Geheul-Töpferscherbe", "item.minecraft.husk_spawn_egg": "Wüsdnzombie-Schborn-Ei", "item.minecraft.ink_sac": "Dindnbeudl", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_boots": "Eisnlaadschn", "item.minecraft.iron_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "Eisengolem-Spawn-Ei", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_hoe": "Eisnhagge", "item.minecraft.iron_horse_armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_ingot": "Eisnbarrn", "item.minecraft.iron_leggings": "Eisnbeenschudds", "item.minecraft.iron_nugget": "Eisnglumbn", "item.minecraft.iron_pickaxe": "Eisns<PERSON><PERSON>ds<PERSON>e", "item.minecraft.iron_shovel": "Eisnschibbe", "item.minecraft.iron_sword": "Eisnschwerd", "item.minecraft.item_frame": "<PERSON><PERSON>", "item.minecraft.jungle_boat": "Drobnhulzbood", "item.minecraft.jungle_chest_boat": "Dr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>bood", "item.minecraft.knowledge_book": "Schwahrde des Wissens", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lead": "<PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "Lädorlaadschn", "item.minecraft.leather_chestplate": "Lädorjagge", "item.minecraft.leather_helmet": "Lädormiddse", "item.minecraft.leather_horse_armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_leggings": "Lädorhose", "item.minecraft.light_blue_bundle": "<PERSON><PERSON>ues Bündel", "item.minecraft.light_blue_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.lime_bundle": "Hellgrünes Bündel", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_harness": "Hellgr<PERSON><PERSON>", "item.minecraft.lingering_potion": "Vorweildrang", "item.minecraft.lingering_potion.effect.awkward": "Seldsamorr Vorweildrang", "item.minecraft.lingering_potion.effect.empty": "<PERSON><PERSON> brauba<PERSON>rr <PERSON>orweild<PERSON>", "item.minecraft.lingering_potion.effect.fire_resistance": "Vorweildrang dorr Feuorrresisdenz", "item.minecraft.lingering_potion.effect.harming": "Vorweildrang des Schadns", "item.minecraft.lingering_potion.effect.healing": "Vorweildrang dorr Heilung", "item.minecraft.lingering_potion.effect.infested": "Verweiltrank des Befalls", "item.minecraft.lingering_potion.effect.invisibility": "Vorweildrang dorr <PERSON>", "item.minecraft.lingering_potion.effect.leaping": "Vorweildrang dorr Schbrunggrafd", "item.minecraft.lingering_potion.effect.levitation": "Vorweildrang dorr Schwebegrafd", "item.minecraft.lingering_potion.effect.luck": "Vorweildrang des Glüggs", "item.minecraft.lingering_potion.effect.mundane": "Gewöhnlischorr Vorweildrang", "item.minecraft.lingering_potion.effect.night_vision": "Vorweildrang dorr Nachdsischd", "item.minecraft.lingering_potion.effect.oozing": "Verweiltrank des Schleimens", "item.minecraft.lingering_potion.effect.poison": "Vorweildrang dorr Vorgifdung", "item.minecraft.lingering_potion.effect.regeneration": "Vorweildrang dorr Regenoration", "item.minecraft.lingering_potion.effect.slow_falling": "Vorweildrang des sanfdn Falls", "item.minecraft.lingering_potion.effect.slowness": "Vorweildrang dorr <PERSON>", "item.minecraft.lingering_potion.effect.strength": "Vorweildrang dorr Schdärge", "item.minecraft.lingering_potion.effect.swiftness": "Vorweildrang dorr <PERSON>", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.turtle_master": "Vorweildrang des Schüldgrödnmeisdors", "item.minecraft.lingering_potion.effect.water": "Vorweilnde Wassorrbulle", "item.minecraft.lingering_potion.effect.water_breathing": "Vorweildrang dorr Undorrwassorradmung", "item.minecraft.lingering_potion.effect.weakness": "Vorweildrang dorr Schwäsche", "item.minecraft.lingering_potion.effect.weaving": "Verweiltrank des Webens", "item.minecraft.lingering_potion.effect.wind_charged": "Verweiltrank der Windladung", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lodestone_compass": "Leidschdeen-Gombass", "item.minecraft.mace": "Streitkolben", "item.minecraft.magenta_bundle": "Magenta Bündel", "item.minecraft.magenta_dye": "Ma<PERSON><PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Magmacreme", "item.minecraft.magma_cube_spawn_egg": "Magmawürfl-Schborn-Ei", "item.minecraft.mangrove_boat": "Mangrovnhulzbood", "item.minecraft.mangrove_chest_boat": "Man<PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>bood", "item.minecraft.map": "<PERSON><PERSON>", "item.minecraft.melon_seeds": "Melongerne", "item.minecraft.melon_slice": "Melonscheibe", "item.minecraft.milk_bucket": "Milschehmorr", "item.minecraft.minecart": "Lore", "item.minecraft.miner_pottery_shard": "Bergarbeiter-Töpferscherbe", "item.minecraft.miner_pottery_sherd": "Bergarbeiter-Töpferscherbe", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.desc": "Mojang-Logo", "item.minecraft.mojang_banner_pattern.new": "Mojang-Logo-Bannervorlage", "item.minecraft.mooshroom_spawn_egg": "Mooshroom-Schborn-Ei", "item.minecraft.mourner_pottery_shard": "<PERSON><PERSON><PERSON>-Töpferscherbe", "item.minecraft.mourner_pottery_sherd": "<PERSON><PERSON><PERSON>-Töpferscherbe", "item.minecraft.mule_spawn_egg": "Mauldier-Schborn-Ei", "item.minecraft.mushroom_stew": "Bilzsubbe", "item.minecraft.music_disc_11": "Schallbladde", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Schallbladde", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Schallbladde", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Schallbladde", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Schallbladde", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Schallbladde", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Schallplatte", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Schallplatte", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> (Spieluhr)", "item.minecraft.music_disc_far": "Schallbladde", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Schallplatte", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Schallbladde", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Schallbladde", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Schallbladde", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Schallbladde", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Schallplatte", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Schallbladde", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Schallbladde", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Schallbladde", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Schallplatte", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Schallbladde", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Schallbladde", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON>", "item.minecraft.name_tag": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.nautilus_shell": "Naudilusschale", "item.minecraft.nether_brick": "Netherzieschl", "item.minecraft.nether_star": "Netherschdern", "item.minecraft.nether_wart": "Netherwarze", "item.minecraft.netherite_axe": "Nädderidaggsd", "item.minecraft.netherite_boots": "Nädderidlaadschn", "item.minecraft.netherite_chestplate": "Nädderidhar<PERSON><PERSON><PERSON>", "item.minecraft.netherite_helmet": "Nädderidhälm", "item.minecraft.netherite_hoe": "Nädderidhagge", "item.minecraft.netherite_ingot": "Nädderidbarrn", "item.minecraft.netherite_leggings": "Nädderidbeenschudds", "item.minecraft.netherite_pickaxe": "Nädderidschbiddshagge", "item.minecraft.netherite_scrap": "Nädderidschdügge", "item.minecraft.netherite_shovel": "Nädderidschaufl", "item.minecraft.netherite_sword": "Nädderidschwerd", "item.minecraft.netherite_upgrade_smithing_template": "Schmiedevorlage", "item.minecraft.netherite_upgrade_smithing_template.new": "Netheritaufwertung", "item.minecraft.oak_boat": "Eischnhulzbood", "item.minecraft.oak_chest_boat": "Eischnh<PERSON>zdr<PERSON>nbood", "item.minecraft.ocelot_spawn_egg": "Oddselod-Schborn-Ei", "item.minecraft.ominous_bottle": "Unheilvolle Flasche", "item.minecraft.ominous_trial_key": "Unheilvoller Prüfungsschlüssel", "item.minecraft.orange_bundle": "Oranges Bündel", "item.minecraft.orange_dye": "<PERSON>ang<PERSON><PERSON><PERSON>", "item.minecraft.orange_harness": "Oranges <PERSON>", "item.minecraft.painting": "Gemäldä", "item.minecraft.pale_oak_boat": "Blasseichenholzboot", "item.minecraft.pale_oak_chest_boat": "Blasseichenholztruhenboot", "item.minecraft.panda_spawn_egg": "Panda-<PERSON><PERSON>born-E<PERSON>", "item.minecraft.paper": "<PERSON>bb<PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Babagein-Schborn-Ei", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.phantom_spawn_egg": "Phandom-<PERSON><PERSON>born-<PERSON><PERSON>", "item.minecraft.pig_spawn_egg": "Schweene-Schborn-Ei", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "Schnauzen-Bannervorlage", "item.minecraft.piglin_brute_spawn_egg": "Piglin-Barbarn-<PERSON><PERSON>born-Ei", "item.minecraft.piglin_spawn_egg": "Piglin-Sc<PERSON>born-Ei", "item.minecraft.pillager_spawn_egg": "Plündororr-Schborn-Ei", "item.minecraft.pink_bundle": "<PERSON>", "item.minecraft.pink_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "Kannenpflanze", "item.minecraft.pitcher_pod": "Kannenpflanzenkapsel", "item.minecraft.plenty_pottery_shard": "Reichtums-Töpferscherbe", "item.minecraft.plenty_pottery_sherd": "Reichtums-Töpferscherbe", "item.minecraft.poisonous_potato": "Gifdsche Gardoffl", "item.minecraft.polar_bear_spawn_egg": "Eisbärn-Schborn-Ei", "item.minecraft.popped_chorus_fruit": "Gebladdsde Chorusfruchd", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON>", "item.minecraft.potato": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion": "<PERSON><PERSON>", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.empty": "<PERSON><PERSON>", "item.minecraft.potion.effect.fire_resistance": "<PERSON><PERSON> dorr <PERSON>", "item.minecraft.potion.effect.harming": "<PERSON><PERSON>", "item.minecraft.potion.effect.healing": "<PERSON><PERSON> do<PERSON>", "item.minecraft.potion.effect.infested": "Trank des Befalls", "item.minecraft.potion.effect.invisibility": "<PERSON><PERSON> dorr <PERSON>", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON> dorr <PERSON>grafd", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON> do<PERSON>grafd", "item.minecraft.potion.effect.luck": "<PERSON><PERSON>", "item.minecraft.potion.effect.mundane": "Gewöhnlischorr Drang", "item.minecraft.potion.effect.night_vision": "<PERSON><PERSON> do<PERSON>", "item.minecraft.potion.effect.oozing": "<PERSON>rank des Schleimens", "item.minecraft.potion.effect.poison": "<PERSON><PERSON> do<PERSON>", "item.minecraft.potion.effect.regeneration": "<PERSON><PERSON> dorr <PERSON>", "item.minecraft.potion.effect.slow_falling": "Drang des sanfdn Falls", "item.minecraft.potion.effect.slowness": "<PERSON><PERSON>", "item.minecraft.potion.effect.strength": "<PERSON><PERSON>", "item.minecraft.potion.effect.swiftness": "<PERSON><PERSON> do<PERSON>", "item.minecraft.potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s<PERSON><PERSON>", "item.minecraft.potion.effect.turtle_master": "Drang des Schüldgrödnmeisdors", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON> do<PERSON>", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON> do<PERSON>", "item.minecraft.potion.effect.weaving": "<PERSON><PERSON> des Webens", "item.minecraft.potion.effect.wind_charged": "<PERSON><PERSON> der Windladung", "item.minecraft.pottery_shard_archer": "Schützen-Töpferscherbe", "item.minecraft.pottery_shard_arms_up": "Gebärden-Töpferscherbe", "item.minecraft.pottery_shard_prize": "Juwelen-Töpferscherbe", "item.minecraft.pottery_shard_skull": "Totenkopf-Töpferscherbe", "item.minecraft.powder_snow_bucket": "Bulvorrschneeehmorr", "item.minecraft.prismarine_crystals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_shard": "Brismarinschärbe", "item.minecraft.prize_pottery_shard": "Juwelen-Töpferscherbe", "item.minecraft.prize_pottery_sherd": "Juwelen-Töpferscherbe", "item.minecraft.pufferfish": "G<PERSON>lfisch", "item.minecraft.pufferfish_bucket": "Guchlfischehmorr", "item.minecraft.pufferfish_spawn_egg": "Guchlfisch-Schborn-Ei", "item.minecraft.pumpkin_pie": "G<PERSON><PERSON><PERSON><PERSON><PERSON>n", "item.minecraft.pumpkin_seeds": "Gürbisgerne", "item.minecraft.purple_bundle": "Violettes Bündel", "item.minecraft.purple_dye": "<PERSON>", "item.minecraft.purple_harness": "<PERSON><PERSON>", "item.minecraft.quartz": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_foot": "Hasnpfode", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "Garniggl-<PERSON><PERSON>born-Ei", "item.minecraft.rabbit_stew": "Garnigglragout", "item.minecraft.raiser_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.raiser_armor_trim_smithing_template.new": "Aufzieher-Rüstungsbesatz", "item.minecraft.ravager_spawn_egg": "Vorwüsdorr-Schborn-Ei", "item.minecraft.raw_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_gold": "<PERSON><PERSON><PERSON>", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "Bergungsgombass", "item.minecraft.red_bundle": "Rotes Bündel", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON><PERSON>", "item.minecraft.redstone": "Redstone-Sch<PERSON>ob", "item.minecraft.resin_brick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.resin_clump": "Harz<PERSON>lump<PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.rib_armor_trim_smithing_template.new": "Rippen-Rüstungsbesatz", "item.minecraft.rotten_flesh": "Vorrgamme<PERSON><PERSON>", "item.minecraft.saddle": "Saddl", "item.minecraft.salmon": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.salmon_bucket": "Lachsehmorr", "item.minecraft.salmon_spawn_egg": "Lachs-Schborn-Ei", "item.minecraft.scrape_pottery_sherd": "Abschabe-Töpferscherbe", "item.minecraft.scute": "Hornschild", "item.minecraft.sentry_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.sentry_armor_trim_smithing_template.new": "Wachen-Rüstungsbesatz", "item.minecraft.shaper_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.shaper_armor_trim_smithing_template.new": "Gestalter-Rüstungsbesatz", "item.minecraft.sheaf_pottery_shard": "Garben-Töpferscherbe", "item.minecraft.sheaf_pottery_sherd": "Garben-Töpferscherbe", "item.minecraft.shears": "<PERSON><PERSON>ä<PERSON><PERSON>", "item.minecraft.sheep_spawn_egg": "Schafs-Schborn-Ei", "item.minecraft.shelter_pottery_shard": "Zufluchts-Töpferscherbe", "item.minecraft.shelter_pottery_sherd": "Zufluchts-Töpferscherbe", "item.minecraft.shield": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.magenta": "Magenda Schüld", "item.minecraft.shield.orange": "Orang<PERSON><PERSON><PERSON>", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.purple": "<PERSON>", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "item.minecraft.sign": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.silence_armor_trim_smithing_template.new": "Stille-Rüstungsbesatz", "item.minecraft.silverfish_spawn_egg": "Silborfischschn-Schborn-Ei", "item.minecraft.skeleton_horse_spawn_egg": "Schgeleddfärd-Schborn-Ei", "item.minecraft.skeleton_spawn_egg": "Schgeledd-Schborn-Ei", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "Schädel-Bannervorlage", "item.minecraft.skull_pottery_shard": "Totenkopf-Töpferscherbe", "item.minecraft.skull_pottery_sherd": "Totenkopf-Töpferscherbe", "item.minecraft.slime_ball": "Schleimball", "item.minecraft.slime_spawn_egg": "Schleim-Schborn-Ei", "item.minecraft.smithing_template": "Schmiedevorlage", "item.minecraft.smithing_template.applies_to": "<PERSON><PERSON><PERSON><PERSON> auf:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Barren oder Kristall hinzufügen", "item.minecraft.smithing_template.armor_trim.applies_to": "Rüstung", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Rüstungsteil hinzufügen", "item.minecraft.smithing_template.armor_trim.ingredients": "Barren & Kristalle", "item.minecraft.smithing_template.ingredients": "Werkstoffe:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Netheritbarren hinzufügen", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamandausrü<PERSON><PERSON>ng", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Rüstungsteil, Werkzeug oder Schwert aus Diamant hinzufügen", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Nädderidbarrn", "item.minecraft.smithing_template.upgrade": "Aufwertung: ", "item.minecraft.sniffer_spawn_egg": "Schnüffler-Spawn-Ei", "item.minecraft.snort_pottery_shard": "Schnaub-Töpferscherbe", "item.minecraft.snort_pottery_sherd": "Schnaub-Töpferscherbe", "item.minecraft.snout_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.snout_armor_trim_smithing_template.new": "Schnauzen-Rüstungsbesatz", "item.minecraft.snow_golem_spawn_egg": "Schneegolem-Schborn-Ei", "item.minecraft.snowball": "Schneeball", "item.minecraft.spectral_arrow": "Schbegdralpfeil", "item.minecraft.spider_eye": "Schbinnooche", "item.minecraft.spider_spawn_egg": "Schbinn-Schborn-Ei", "item.minecraft.spire_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.spire_armor_trim_smithing_template.new": "Turmspitzen-Rüstungsbesatz", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON>", "item.minecraft.splash_potion.effect.fire_resistance": "<PERSON>rf<PERSON>ng dorr <PERSON>z", "item.minecraft.splash_potion.effect.harming": "Wurfdrang des Schadns", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON> dorr Heilung", "item.minecraft.splash_potion.effect.infested": "Wurftrank des Befalls", "item.minecraft.splash_potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON> dorr <PERSON>", "item.minecraft.splash_potion.effect.leaping": "<PERSON>rfdrang dorr Schbrunggrafd", "item.minecraft.splash_potion.effect.levitation": "Wurfdrang dorr Schwebegrafd", "item.minecraft.splash_potion.effect.luck": "Wurfdrang des Glüggs", "item.minecraft.splash_potion.effect.mundane": "Gewöhnlischorr Wu<PERSON>drang", "item.minecraft.splash_potion.effect.night_vision": "<PERSON>rfdra<PERSON> dorr Na<PERSON>d", "item.minecraft.splash_potion.effect.oozing": "Wurftrank des Schleimens", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON> dorr <PERSON>gi<PERSON>dung", "item.minecraft.splash_potion.effect.regeneration": "Wurfdrang dorr Regenoration", "item.minecraft.splash_potion.effect.slow_falling": "Wurfdrang des sanfdn Falls", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON> dorr <PERSON>", "item.minecraft.splash_potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON> dorr <PERSON>", "item.minecraft.splash_potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON> dorr <PERSON>", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s<PERSON><PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "Wurfdrang des Schüldgrödnmeisdors", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON> dorr <PERSON>", "item.minecraft.splash_potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON> dorr <PERSON>w<PERSON>", "item.minecraft.splash_potion.effect.weaving": "Wurftrank des Webens", "item.minecraft.splash_potion.effect.wind_charged": "Wurftrank der Windladung", "item.minecraft.spruce_boat": "Fischdnhulzbood", "item.minecraft.spruce_chest_boat": "Fischdnhulzdruhnbood", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "Dindnfisch-Schborn-Ei", "item.minecraft.stick": "Schdogg", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_hoe": "Sch<PERSON>enhagg<PERSON>", "item.minecraft.stone_pickaxe": "Sc<PERSON><PERSON>ens<PERSON>gge", "item.minecraft.stone_shovel": "Schdeenschibbe", "item.minecraft.stone_sword": "Schdeenschwerd", "item.minecraft.stray_spawn_egg": "Eiswandororr-<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "item.minecraft.strider_spawn_egg": "Schreidorr-Spawn-Ei", "item.minecraft.string": "<PERSON><PERSON><PERSON>", "item.minecraft.sugar": "Zuggorr", "item.minecraft.suspicious_stew": "Susbeggde Subbe", "item.minecraft.sweet_berries": "Süßbeern", "item.minecraft.tadpole_bucket": "Gaulquabbnehmorr", "item.minecraft.tadpole_spawn_egg": "Gaulquappn-<PERSON><PERSON>born-Ei", "item.minecraft.tide_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.tide_armor_trim_smithing_template.new": "Gezeiten-Rüstungsbesatz", "item.minecraft.tipped_arrow": "Gedrängdorr Pfeil", "item.minecraft.tipped_arrow.effect.awkward": "Gedrängdorr Pfeil", "item.minecraft.tipped_arrow.effect.empty": "<PERSON><PERSON>dellbarorr gedrängdorr Pfeil", "item.minecraft.tipped_arrow.effect.fire_resistance": "<PERSON><PERSON><PERSON> dorr <PERSON>", "item.minecraft.tipped_arrow.effect.harming": "Pfeil des Schadns", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON><PERSON> dorr <PERSON>", "item.minecraft.tipped_arrow.effect.infested": "Pfeil des Befalls", "item.minecraft.tipped_arrow.effect.invisibility": "<PERSON><PERSON><PERSON> dorr <PERSON>", "item.minecraft.tipped_arrow.effect.leaping": "<PERSON><PERSON><PERSON> dorr <PERSON>runggrafd", "item.minecraft.tipped_arrow.effect.levitation": "<PERSON><PERSON><PERSON> dorr <PERSON>grafd", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON><PERSON> Glüggs", "item.minecraft.tipped_arrow.effect.mundane": "Gedrängdorr Pfeil", "item.minecraft.tipped_arrow.effect.night_vision": "<PERSON><PERSON><PERSON> do<PERSON>", "item.minecraft.tipped_arrow.effect.oozing": "Pfeil des Schleimens", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON><PERSON> do<PERSON>", "item.minecraft.tipped_arrow.effect.regeneration": "<PERSON><PERSON><PERSON> dorr <PERSON>", "item.minecraft.tipped_arrow.effect.slow_falling": "Pfeil des sanfdn Falls", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.strength": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.swiftness": "<PERSON><PERSON><PERSON> do<PERSON>", "item.minecraft.tipped_arrow.effect.thick": "Gedrängdorr Pfeil", "item.minecraft.tipped_arrow.effect.turtle_master": "Pfeil des Schüldgrödnmeisdors", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.water_breathing": "<PERSON><PERSON><PERSON> dorr <PERSON>", "item.minecraft.tipped_arrow.effect.weakness": "<PERSON><PERSON><PERSON> do<PERSON>", "item.minecraft.tipped_arrow.effect.weaving": "Pfeil des Webens", "item.minecraft.tipped_arrow.effect.wind_charged": "Pfeil der Windladung", "item.minecraft.tnt_minecart": "TNT-Lore", "item.minecraft.torchflower_seeds": "Fackelliliensamen", "item.minecraft.totem_of_undying": "Dodem dorr Unschderblischkeed", "item.minecraft.trader_llama_spawn_egg": "Händlorlama-Sc<PERSON>born-Ei", "item.minecraft.trial_key": "Prüfungsschlüssel", "item.minecraft.trident": "<PERSON><PERSON><PERSON>gg", "item.minecraft.tropical_fish": "Drobnfisch", "item.minecraft.tropical_fish_bucket": "Drobnfischehmorr", "item.minecraft.tropical_fish_spawn_egg": "Drobnfisch-Schborn-Ei", "item.minecraft.turtle_helmet": "Schüldgrödnbanzor", "item.minecraft.turtle_scute": "Schildkröten-Hornschild", "item.minecraft.turtle_spawn_egg": "Schüldgrödn-Schborn-Ei", "item.minecraft.vex_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.vex_armor_trim_smithing_template.new": "Plagegeister-Rüstungsbesatz", "item.minecraft.vex_spawn_egg": "Blagegeisdorr-Schborn-Ei", "item.minecraft.villager_spawn_egg": "Dorfbewohnorr-Schborn-Ei", "item.minecraft.vindicator_spawn_egg": "Dienorr-Schborn-Ei", "item.minecraft.wandering_trader_spawn_egg": "Fahrendorr-Händlorr-Schborn-Ei", "item.minecraft.ward_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.ward_armor_trim_smithing_template.new": "Warthof-Rüstungsbesatz", "item.minecraft.warden_spawn_egg": "Wärdorr-Schborn-Ei", "item.minecraft.warped_fungus_on_a_stick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.water_bucket": "Wassorrehmorr", "item.minecraft.wayfinder_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Wegfinder-Rüstungsbesatz", "item.minecraft.wheat": "Weidsn", "item.minecraft.wheat_seeds": "Weidsngörnor", "item.minecraft.white_bundle": "Weißes Bündel", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.white_harness": "Weißes Geschirr", "item.minecraft.wild_armor_trim_smithing_template": "Schmiedevorlage", "item.minecraft.wild_armor_trim_smithing_template.new": "Wildnis-Rüstungsbesatz", "item.minecraft.wind_charge": "Windkugel", "item.minecraft.witch_spawn_egg": "Heggsn-Schborn-Ei", "item.minecraft.wither_skeleton_spawn_egg": "Witherschgeledd-Schborn-Ei", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "item.minecraft.wolf_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.wolf_spawn_egg": "Wu<PERSON>s-Sc<PERSON>born-<PERSON>i", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON>e", "item.minecraft.wooden_shovel": "Hulzschibbe", "item.minecraft.wooden_sword": "Hulzschwerd", "item.minecraft.writable_book": "Schwahrde un Fädorr", "item.minecraft.written_book": "Beschriebne Schwahrde", "item.minecraft.yellow_bundle": "Gelbes Bündel", "item.minecraft.yellow_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.yellow_harness": "<PERSON><PERSON><PERSON> Geschirr", "item.minecraft.zoglin_spawn_egg": "Zoglin-Schborn-Ei", "item.minecraft.zombie_horse_spawn_egg": "Zombiefärde-Schborn-Ei", "item.minecraft.zombie_spawn_egg": "Zombie-<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "item.minecraft.zombie_villager_spawn_egg": "Zombiedorfbewohnorr-Schborn-Ei", "item.minecraft.zombified_piglin_spawn_egg": "Zombifizierdorr-Piglin-Schborn-Ei", "item.modifiers.any": "Ausgerüstet:", "item.modifiers.armor": "Angelegt:", "item.modifiers.body": "Angelegt:", "item.modifiers.chest": "Am Görborr:", "item.modifiers.feet": "An den Füßn:", "item.modifiers.hand": "Gehalten:", "item.modifiers.head": "<PERSON><PERSON><PERSON>m <PERSON><PERSON>:", "item.modifiers.legs": "An d<PERSON>n <PERSON><PERSON>:", "item.modifiers.mainhand": "In dorr Haubdhand:", "item.modifiers.offhand": "In dorr <PERSON>hand:", "item.modifiers.saddle": "Gesattelt:", "item.nbt_tags": "NBT: %s Eischnschafd(n)", "item.op_block_warning.line1": "Achtung:", "item.op_block_warning.line2": "Die Benutzung dieses Gegenstandes könnte einen Befehl ausführen", "item.op_block_warning.line3": "<PERSON><PERSON> ben<PERSON>, wenn du den genauen Inhalt kennst!", "item.unbreakable": "Unzorschdörbar", "itemGroup.buildingBlocks": "Baublögge", "itemGroup.coloredBlocks": "Farbige Blöcke", "itemGroup.combat": "Gambf", "itemGroup.consumables": "Nahrungsmittel & Tränke", "itemGroup.crafting": "Werkstoffe", "itemGroup.foodAndDrink": "Nahrungsmittel & Tränke", "itemGroup.functional": "Gebrauchsblöcke", "itemGroup.hotbar": "Geschbeischorrde Schnellzugriffsleisdn", "itemGroup.ingredients": "Werkstoffe", "itemGroup.inventory": "Invendar", "itemGroup.natural": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.op": "Operatorhilfsmittel", "itemGroup.redstone": "Redstone", "itemGroup.search": "<PERSON><PERSON>", "itemGroup.spawnEggs": "Spawn‐Eier", "itemGroup.tools": "Werkzeuge & Hilfsmittel", "item_modifier.unknown": "Unbeganndorr Geschnschdandsmodifigadorr: %s", "jigsaw_block.final_state": "<PERSON><PERSON><PERSON> zu:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "Ausgerischded", "jigsaw_block.joint.rollable": "Drehboar", "jigsaw_block.joint_label": "Vorrbindung:", "jigsaw_block.keep_jigsaws": "Vorrbünde behaldn", "jigsaw_block.levels": "Schdufn: %s", "jigsaw_block.name": "Name:", "jigsaw_block.placement_priority": "Platzierpriorität:", "jigsaw_block.placement_priority.tooltip": "<PERSON><PERSON> sich dieser Verbundblock mit einem Stück verbindet, ist dies die Reihenfolge, in der das Stück auf Verbindungen zur Gesamtkonstruktion verarbeitet wird.\n\nStücke werden in absteigender Priorität verarbeitet, wobei Gleichstände durch die Einfügungsreihenfolge gelöst werden.", "jigsaw_block.pool": "Bezugsquelle:", "jigsaw_block.selection_priority": "Auswahlpriorität:", "jigsaw_block.selection_priority.tooltip": "Wenn das zugehörige Stück auf Verbindungen verarbeitet wird, ist dies die Reihenfolge, in der dieser Verbundblock versucht, sich mit seinem Zielstück zu verbinden.\n\nVerbünde werden in absteigender Priorität verarbeitet, wobei Gleichstände durch eine zufällige Reihenfolge gelöst werden.", "jigsaw_block.target": "Zielname:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (Spieluhr)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> – Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Fordschridde", "key.attack": "Ang<PERSON><PERSON>n/A<PERSON>un", "key.back": "Rüggwärds", "key.categories.creative": "Greativmodus", "key.categories.gameplay": "Sc<PERSON><PERSON>lm<PERSON>chanigg", "key.categories.inventory": "Invendar", "key.categories.misc": "Vorschiedenes", "key.categories.movement": "Beweschung", "key.categories.multiplayer": "Mährschbielorr", "key.categories.ui": "Schbielschdeuorung", "key.chat": "<PERSON><PERSON>", "key.command": "Befählszeile öffnen", "key.drop": "Geschnschdand falln lassn", "key.forward": "Forwärds", "key.fullscreen": "<PERSON><PERSON><PERSON><PERSON> weggsln", "key.hotbar.1": "Schnellzugriff 1", "key.hotbar.2": "Schnellzugriff 2", "key.hotbar.3": "Schnellzugriff 3", "key.hotbar.4": "Schnellzugriff 4", "key.hotbar.5": "Schnellzugriff 5", "key.hotbar.6": "Schnellzugriff 6", "key.hotbar.7": "Schnellzugriff 7", "key.hotbar.8": "Schnellzugriff 8", "key.hotbar.9": "Schnellzugriff 9", "key.inventory": "Invendar öffnen/schließn", "key.jump": "Schbringn", "key.keyboard.apostrophe": "’", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.caps.lock": "Feststelltaste", "key.keyboard.comma": ",", "key.keyboard.delete": "Entf", "key.keyboard.down": "Undn", "key.keyboard.end": "Ände", "key.keyboard.enter": "<PERSON><PERSON><PERSON>", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Einfg", "key.keyboard.keypad.0": "Num 0", "key.keyboard.keypad.1": "Num 1", "key.keyboard.keypad.2": "Num 2", "key.keyboard.keypad.3": "Num 3", "key.keyboard.keypad.4": "Num 4", "key.keyboard.keypad.5": "Num 5", "key.keyboard.keypad.6": "Num 6", "key.keyboard.keypad.7": "Num 7", "key.keyboard.keypad.8": "Num 8", "key.keyboard.keypad.9": "Num 9", "key.keyboard.keypad.add": "Num +", "key.keyboard.keypad.decimal": "<PERSON><PERSON> ,", "key.keyboard.keypad.divide": "Num /", "key.keyboard.keypad.enter": "<PERSON><PERSON>", "key.keyboard.keypad.equal": "Num =", "key.keyboard.keypad.multiply": "Num *", "key.keyboard.keypad.subtract": "Num -", "key.keyboard.left": "<PERSON><PERSON>", "key.keyboard.left.alt": "Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "<PERSON>rg lings", "key.keyboard.left.shift": "Umschalt lings", "key.keyboard.left.win": "<PERSON>a lings", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "<PERSON><PERSON>", "key.keyboard.page.down": "Bild ab", "key.keyboard.page.up": "Bild uff", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON>g", "key.keyboard.right": "Re<PERSON><PERSON>", "key.keyboard.right.alt": "Alt Gr", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Strg reschds", "key.keyboard.right.shift": "Umschalt reschds", "key.keyboard.right.win": "<PERSON>a re<PERSON>", "key.keyboard.scroll.lock": "Rolln", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "<PERSON><PERSON> belegt", "key.keyboard.up": "Obn", "key.keyboard.world.1": "Weld 1", "key.keyboard.world.2": "Weld 2", "key.left": "<PERSON><PERSON>", "key.loadToolbarActivator": "Schnellzugriffsleisde ladn", "key.mouse": "Mausdasde %1$s", "key.mouse.left": "<PERSON><PERSON><PERSON><PERSON>", "key.mouse.middle": "Middlgligg", "key.mouse.right": "Reschdsgligg", "key.pickItem": "Blogg auswähln", "key.playerlist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.quickActions": "Schnellaktionen", "key.right": "Re<PERSON><PERSON>", "key.saveToolbarActivator": "Schnellzugriffsleisde schbeischorrn", "key.screenshot": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>", "key.smoothCamera": "Gameravorhaldn weggsln", "key.sneak": "Schleischn", "key.socialInteractions": "Indoraggtschonsfensdorr", "key.spectatorOutlines": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Zuschauorr)", "key.sprint": "Schbrindn", "key.swapOffhand": "Geschnschdand mid Zweedhand dauschn", "key.togglePerspective": "Berspegdive weggsln", "key.use": "Benuddsn/Bladdsiern", "known_server_link.announcements": "Ankündigungen", "known_server_link.community": "Gemeinschaft", "known_server_link.community_guidelines": "Gemeinschaftsrichtlinien", "known_server_link.feedback": "Rückmeldung", "known_server_link.forums": "<PERSON>en", "known_server_link.news": "Neuigkeiten", "known_server_link.report_bug": "Fehler am Server melden", "known_server_link.status": "Status", "known_server_link.support": "Hilfe/Unterstützung", "known_server_link.website": "Website", "lanServer.otherPlayers": "Eenschdellungn für andre <PERSON>r", "lanServer.port": "Portnummer", "lanServer.port.invalid": "<PERSON>in gültiger Port.\nLass das Eingabefeld leer oder gib eine Nummer zwischen 1024 und 65535 ein.", "lanServer.port.invalid.new": "Kein gültiger Port.\nLass das Eingabefeld leer oder gib eine Nummer zwischen %s und %s ein.", "lanServer.port.unavailable": "Port nicht verfügbar.\nLass das Eingabefeld leer oder gib eine andere Nummer zwischen 1024 und 65535 ein.", "lanServer.port.unavailable.new": "Port nicht verfügbar.\nLass das Eingabefeld leer oder gib eine andere Nummer zwischen %s und %s ein.", "lanServer.scanning": "Suche nach Schbieln im logaln Neddswerg", "lanServer.start": "LAN-<PERSON><PERSON> schdardn", "lanServer.title": "LAN-Weld", "language.code": "sxu_DE", "language.name": "<PERSON><PERSON><PERSON><PERSON>sch", "language.region": "Saggsn", "lectern.take_book": "Schwahr<PERSON> nähm", "loading.progress": "%s %%", "mco.account.privacy.info": "Erfahre mähr über Mojang un Dadnschudzgeseddse", "mco.account.privacy.info.button": "Erfahre mehr über die DSGVO", "mco.account.privacy.information": "Mojang wendet bestimmte Verfahren an, um Kinder und deren Privatsphäre zu schützen; so werden das „Gesetz zum Schutz der Privatsphäre von Kindern im Internet“ (COPPA) und die Datenschutz‐Grundverordnung (DSGVO) eingehalten.\n\nMöglicherweise musst du die Zustimmung deiner Eltern e<PERSON>holen, bevor du auf dein Realms‐Konto zugreifen kannst.", "mco.account.privacyinfo": "Mojang wended beschdimmde Verfahrn an, um Kindorr un dern Brivadsphäre zu schüddsn. So wärdn undorr andorm das „Gesedds zum Schudds dorr Brivadsphäre von Kindorn im Indorrned“ (COPPA) un die Dadnschudds-Grundvorordnung (DSGVO) eingehaldn.\n\nMöglischorrweese benödigsd du de Zuschdimmung deinorr Eldorn, um uff dei Realms-Gondo zuzugreifn.\n\nWenn du ee ältores Minecraft-Gondo hasd (du meldsd disch mid deim Benutzornam an), mussde dieses in ee Mojang-Gondo umwandln, um uff Realms zugreifn zu könn.", "mco.account.update": "Gondo aggdualisiern", "mco.activity.noactivity": "Keine Aktivität in dem/den letzten %s Tag(en)", "mco.activity.title": "Schbielorraggdividäd", "mco.backup.button.download": "Aggdl. herundorrladn", "mco.backup.button.reset": "Zurüggsäddsn", "mco.backup.button.restore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>dell<PERSON>", "mco.backup.button.upload": "Weld hochladn", "mco.backup.changes.tooltip": "Ändorungn", "mco.backup.entry": "Sicherung (%s)", "mco.backup.entry.description": "Beschreibung", "mco.backup.entry.enabledPack": "Aktivierte(s) Paket(e)", "mco.backup.entry.gameDifficulty": "Spielschwierigkeit", "mco.backup.entry.gameMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Spielserverversion", "mco.backup.entry.name": "Name", "mco.backup.entry.seed": "Startwert", "mco.backup.entry.templateName": "<PERSON><PERSON><PERSON>nname", "mco.backup.entry.undefined": "Unbestimmte Änderung", "mco.backup.entry.uploaded": "Hochgeladn", "mco.backup.entry.worldType": "Welddyp", "mco.backup.generate.world": "Weld genorriern", "mco.backup.info.title": "Änderungen gegenüber der letzten Sicherung", "mco.backup.narration": "Sicherung von %s", "mco.backup.nobackups": "<PERSON>ses <PERSON> had derzeed keene <PERSON>.", "mco.backup.restoring": "<PERSON><PERSON> wied<PERSON><PERSON>dell<PERSON>", "mco.backup.unknown": "UNBEKANNT", "mco.brokenworld.download": "Herundorrladn", "mco.brokenworld.downloaded": "Herundorrgeladn", "mco.brokenworld.message.line1": "Bidde säddse de Weld zurügg odorr wähle eene andre aus.", "mco.brokenworld.message.line2": "Du kannsd de Weld ooch für den Eenslschbielorrmodus herundorrladn.", "mco.brokenworld.minigame.title": "Dieses Minischbiel würd nisch mähr undorrschdüddsd", "mco.brokenworld.nonowner.error": "Bidde warde uff das Zurüggsäddsn dorr Weld dursch’n Realm-Besiddsor", "mco.brokenworld.nonowner.title": "<PERSON><PERSON> is voralded", "mco.brokenworld.play": "Schbieln", "mco.brokenworld.reset": "Zurüggsäddsn", "mco.brokenworld.title": "Deine aggduelle Weld würd nisch mähr undorrschdüddsd", "mco.client.incompatible.msg.line1": "<PERSON><PERSON> is nisch mid Realms gombadibl.", "mco.client.incompatible.msg.line2": "Bidde nuddse de neusde Fährsion von Minecraft.", "mco.client.incompatible.msg.line3": "Realms is nisch mid Endwigglungsfährsionen gombadibl.", "mco.client.incompatible.title": "Ingombadiblorr Cliend!", "mco.client.outdated.stable.version": "Die Version deines Clients (%s) ist nicht mit Realms kompatibel.\n\nBitte verwende die neueste Version von Minecraft.", "mco.client.unsupported.snapshot.version": "Die Version deines Clients (%s) ist nicht mit Realms kompatibel.\n\nRealms ist für diese Entwicklungsversion nicht verfügbar.", "mco.compatibility.downgrade": "Herabstufen", "mco.compatibility.downgrade.description": "Diese Welt wurde zuletzt in der Version %s gespielt; du befindest dich auf Version %s. Eine Welt herabzustufen, kann diese beschädigen – wir können nicht garantieren, dass sie geladen wird oder funktioniert.\n\nUnter „Sicherungen“ wird eine Sicherheitskopie deiner Welt gespeichert. Bitte setze sie im Bedarfsfall darauf zurück.", "mco.compatibility.incompatible.popup.title": "Inkompatible Version", "mco.compatibility.incompatible.releaseType.popup.message": "<PERSON> Welt, die du betreten willst, ist mit deiner aktuellen Version nicht kompatibel.", "mco.compatibility.incompatible.series.popup.message": "Diese Welt wurde zuletzt in der Version %s gespielt; du befindest dich auf Version %s.\n\nDiese Versionsstände sind nicht miteinander kompatibel. Um in der vorliegenden Version zu spielen, wird eine neue Welt benötigt.", "mco.compatibility.unverifiable.message": "Es konnte nicht festgestellt werden, in welcher Version diese Welt zuletzt gespielt wurde. Wenn du sie hoch‐ bzw. herabstufst, wird automatisch eine Sicherheitskopie erstellt und unter „Sicherungen“ gespeichert.", "mco.compatibility.unverifiable.title": "Kompatibilität nicht überprüfbar", "mco.compatibility.upgrade": "Hochstufen", "mco.compatibility.upgrade.description": "Diese Welt wurde zuletzt in der Version %s gespielt; du befindest dich auf Version %s.\n\nUnter „Sicherungen“ wird eine Sicherheitskopie deiner Welt gespeichert.\n\nBitte setze sie im Bedarfsfall darauf zurück.", "mco.compatibility.upgrade.friend.description": "Diese Welt wurde zuletzt in der Version %s gespielt; du befindest dich auf Version %s.\n\nUnter „Sicherungen“ wird eine Sicherheitskopie der Welt gespeichert.\n\nDer Besitzer des Realms kann die Welt im Bedarfsfall wiederherstellen.", "mco.compatibility.upgrade.title": "Möchtest du diese Welt wirklich hochstufen?", "mco.configure.current.minigame": "Aggduell", "mco.configure.world.activityfeed.disabled": "Schbieloraggdividädsanzeische vorüborrgehnd ausgeschalded", "mco.configure.world.backup": "Sischorrungn", "mco.configure.world.buttons.activity": "Schbielorraggdividäd", "mco.configure.world.buttons.close": "Realm schließn", "mco.configure.world.buttons.delete": "L<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.done": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.edit": "Eenschdellungn", "mco.configure.world.buttons.invite": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>n", "mco.configure.world.buttons.moreoptions": "Wei<PERSON><PERSON>", "mco.configure.world.buttons.newworld": "Neue Welt", "mco.configure.world.buttons.open": "Offnes Realm", "mco.configure.world.buttons.options": "Weld-Obtschoon", "mco.configure.world.buttons.players": "Schbielorr", "mco.configure.world.buttons.region_preference": "Region auswählen …", "mco.configure.world.buttons.resetworld": "Zurüggsäddsn", "mco.configure.world.buttons.save": "Speichern", "mco.configure.world.buttons.settings": "Eenschdellungn", "mco.configure.world.buttons.subscription": "Abonnement", "mco.configure.world.buttons.switchminigame": "Minischbiel weggsln", "mco.configure.world.close.question.line1": "<PERSON>i <PERSON> würd nisch mähr vorfügboar sein.", "mco.configure.world.close.question.line2": "<PERSON><PERSON><PERSON> si<PERSON>, dass de dies dun möschdesd?", "mco.configure.world.close.question.title": "Du musst Änderungen vornehmen, ohne dass es zu Störungen kommt?", "mco.configure.world.closing": "Schließe das Realm …", "mco.configure.world.commandBlocks": "Befählsblögge", "mco.configure.world.delete.button": "Realm löschn", "mco.configure.world.delete.question.line1": "Dei <PERSON> würd endgüldsch <PERSON>öschd", "mco.configure.world.delete.question.line2": "<PERSON><PERSON><PERSON> si<PERSON>, dass de dies dun möschdesd?", "mco.configure.world.description": "Beschreibung des Realms", "mco.configure.world.edit.slot.name": "Name dorr Weld", "mco.configure.world.edit.subscreen.adventuremap": "<PERSON><PERSON><PERSON> Eenschdellungn sin deaggdivierd, da deine aggduelle Weld eene Abendeuorrweld is", "mco.configure.world.edit.subscreen.experience": "<PERSON><PERSON><PERSON> Eenschdellungn sin deaggdivierd, da deine aggduelle Weld eene Erfahrungsweld is", "mco.configure.world.edit.subscreen.inspiration": "Eenige Eenschdellungn sin deaggdivierd, da deine aggduelle Weld eene Inschbirationsweld is", "mco.configure.world.forceGameMode": "Schbielmodus erzwingn", "mco.configure.world.invite.narration": "Du hast %s neue Einladung(en)", "mco.configure.world.invite.profile.name": "Name", "mco.configure.world.invited": "Eingeladn", "mco.configure.world.invited.number": "Eingeladen (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON><PERSON>", "mco.configure.world.invites.ops.tooltip": "Oberadorr", "mco.configure.world.invites.remove.tooltip": "Entfärn", "mco.configure.world.leave.question.line1": "<PERSON><PERSON> de dieses Realm vorlässd, kannsde es nisch mähr bedrädn, bis de erneud e<PERSON>ladn wirsd", "mco.configure.world.leave.question.line2": "<PERSON><PERSON><PERSON> si<PERSON>, dass de dies dun möschdesd?", "mco.configure.world.loading": "Realm wird geladen", "mco.configure.world.location": "Ord", "mco.configure.world.minigame": "Aggduell: %s", "mco.configure.world.name": "Name des Realms", "mco.configure.world.opening": "<PERSON><PERSON><PERSON> das Realm …", "mco.configure.world.players.error": "<PERSON><PERSON> <PERSON>ischdierd keen Schbielorr mid diesm Nam", "mco.configure.world.players.inviting": "<PERSON><PERSON>ler wird eingeladen …", "mco.configure.world.players.title": "Schbielorr", "mco.configure.world.pvp": "Schbielorr gäschn Schbielorr (PvP)", "mco.configure.world.region_preference": "Bevorzugte Region", "mco.configure.world.region_preference.title": "Regionsauswahl", "mco.configure.world.reset.question.line1": "<PERSON><PERSON> Weld würd neu erzeuschd un deine aggduelle Weld gehd vorrlorn", "mco.configure.world.reset.question.line2": "<PERSON><PERSON><PERSON> si<PERSON>, dass de dies dun möschdesd?", "mco.configure.world.resourcepack.question": "Du benötigst ein benutzerdefiniertes Ressourcenpaket, um auf diesem Realm zu spielen.\n\nMöchtest du es herunterladen und damit spielen?", "mco.configure.world.resourcepack.question.line1": "<PERSON><PERSON> Weld erfordorrd ee benuddsorrdefinierdes Ressourcenbaged.", "mco.configure.world.resourcepack.question.line2": "Möschdesde es audomadsch herundorrladn un schbieln?", "mco.configure.world.restore.download.question.line1": "De Weld würd herundorrgeladn un zu dein Eenslschbielorrweldn hinzugefüschd.", "mco.configure.world.restore.download.question.line2": "Möschdesde fordfahrn?", "mco.configure.world.restore.question.line1": "De<PERSON> Weld würd uff den Schdand vom ‚%s‘ (%s) zurügggesäddsd", "mco.configure.world.restore.question.line2": "<PERSON><PERSON><PERSON> si<PERSON>, dass de dies dun möschdesd?", "mco.configure.world.settings.expired": "Die Einstellungen eines abgelaufenen Realms können nicht bearbeitet werden", "mco.configure.world.settings.title": "Eenschdellungn", "mco.configure.world.slot": "Weld %s", "mco.configure.world.slot.empty": "<PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "<PERSON>i <PERSON> würd uff eene andre <PERSON>ld geweg<PERSON>eld", "mco.configure.world.slot.switch.question.line2": "<PERSON><PERSON><PERSON> si<PERSON>, dass de dies dun möschdesd?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON> <PERSON>ld weggsln", "mco.configure.world.slot.tooltip.active": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot.tooltip.minigame": "<PERSON><PERSON> weggsln", "mco.configure.world.spawnAnimals": "<PERSON><PERSON> er<PERSON>", "mco.configure.world.spawnMonsters": "Monsdorr er<PERSON>n", "mco.configure.world.spawnNPCs": "Dorfbewohnorr er<PERSON>n", "mco.configure.world.spawnProtection": "Schbornbungdschudds", "mco.configure.world.spawn_toggle.message": "Wenn du diese Option ausschaltest, werden alle vorhandenen Objekte dieses Ty<PERSON> entfernt", "mco.configure.world.spawn_toggle.message.npc": "<PERSON>n du diese Option ausschaltest, werden alle vorhandenen Objekte dieses <PERSON>, wie <PERSON><PERSON><PERSON>hner, entfernt", "mco.configure.world.spawn_toggle.title": "Bass uff!", "mco.configure.world.status": "Schdadus", "mco.configure.world.subscription.day": "<PERSON><PERSON>", "mco.configure.world.subscription.days": "<PERSON><PERSON>", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "Abonnemend vorlängorrn", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON><PERSON><PERSON> als een <PERSON>g", "mco.configure.world.subscription.month": "<PERSON><PERSON>", "mco.configure.world.subscription.months": "Monade", "mco.configure.world.subscription.recurring.daysleft": "Audomatsch vorlängorrd in", "mco.configure.world.subscription.recurring.info": "Änderungen an deinem Realms‐Abonnement, wie die Verlängerung der Laufzeit oder die Deaktivierung der wiederkehrenden Abrechnung, werden erst ab deinem nächsten Abrechnungstermin berücksichtigt.", "mco.configure.world.subscription.remaining.days": "%1$s Tag(e)", "mco.configure.world.subscription.remaining.months": "%1$s Monat(e)", "mco.configure.world.subscription.remaining.months.days": "%1$s Monat(e), %2$s Tag(e)", "mco.configure.world.subscription.start": "Begin<PERSON>", "mco.configure.world.subscription.tab": "Abonnement", "mco.configure.world.subscription.timeleft": "Vorbleibende Zeid", "mco.configure.world.subscription.title": "Abonnementdetails", "mco.configure.world.subscription.unknown": "Unbegannd", "mco.configure.world.switch.slot": "<PERSON>ld erze<PERSON>n", "mco.configure.world.switch.slot.subtitle": "<PERSON>se <PERSON> is leer, bidde wähle, was zu dun is", "mco.configure.world.title": "Realm einrischdn:", "mco.configure.world.uninvite.player": "Bist du sicher, dass du %s wieder ausladen möchtest?", "mco.configure.world.uninvite.question": "<PERSON><PERSON><PERSON>, dass de diesn Schbielorr ausladn möschdesd:", "mco.configure.worlds.title": "Weldn", "mco.connect.authorizing": "<PERSON><PERSON><PERSON>", "mco.connect.connecting": "Vorrbinde midm Realm …", "mco.connect.failed": "Kunnde nisch midm Realm vorrbindn …", "mco.connect.region": "Serverregion: %s", "mco.connect.success": "<PERSON><PERSON><PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.create.world.error": "Du musst een <PERSON>!", "mco.create.world.failed": "Welterstellung fehlgeschlagen!", "mco.create.world.reset.title": "Weld würd erze<PERSON>chd …", "mco.create.world.skip": "Üborrschbringn", "mco.create.world.subtitle": "<PERSON><PERSON><PERSON><PERSON>, welsche Weld uff dei neues Realm geladn wärdn soll (obdschonal)", "mco.create.world.wait": "<PERSON> würd erzeuschd …", "mco.download.cancelled": "Herundorrladn abgebrochn", "mco.download.confirmation.line1": "<PERSON>, die de herundorrladn willsd, is größorr als %s", "mco.download.confirmation.line2": "<PERSON> würsd diese Weld uff dei Realm nisch wiedorr hochladn könn", "mco.download.confirmation.oversized": "<PERSON> Welt, die du herunterladen willst, ist größer als %s.\n\nDu wirst diese Welt nicht mehr auf dein Realm hochladen können.", "mco.download.done": "Herundorrladn erledschd", "mco.download.downloading": "Herundorrladn", "mco.download.extracting": "Endbaggn", "mco.download.failed": "Herundorrladn gescheidord", "mco.download.percent": "%s %%", "mco.download.preparing": "Herundorrladn vorbereidn", "mco.download.resourcePack.fail": "Herunterladen des Ressourcenpakets fehlgeschlagen!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s pro Sekunde", "mco.download.title": "Neusde Weld herundorrladn", "mco.error.invalid.session.message": "<PERSON><PERSON><PERSON> v<PERSON><PERSON>, Minecraft un ggf. Launschorr neu zu schdardn", "mco.error.invalid.session.title": "Ungüldsche Siddsungs-ID", "mco.errorMessage.6001": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.errorMessage.6002": "Nudzungsbedingungn nisch aggzebdierd", "mco.errorMessage.6003": "Herundorrlade<PERSON><PERSON> er<PERSON>d", "mco.errorMessage.6004": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.errorMessage.6005": "<PERSON>lt ges<PERSON>rt", "mco.errorMessage.6006": "Welt ist veraltet", "mco.errorMessage.6007": "<PERSON>utzer ist in zu vielen Realms", "mco.errorMessage.6008": "Ungültiger Realm‐Name", "mco.errorMessage.6009": "Ungültige Realm‐Beschreibung", "mco.errorMessage.connectionFailure": "Es is ee <PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>, bidde versuchs schbädorr noch ma.", "mco.errorMessage.generic": "Ein Fehler ist aufgetreten: ", "mco.errorMessage.initialize.failed": "Realm‐Initialisierung fehlgeschlagen", "mco.errorMessage.noDetails": "<PERSON><PERSON>hlerbeschreibung vorhanden", "mco.errorMessage.realmsService": "Ein Fehler ist aufgetreten (%s):", "mco.errorMessage.realmsService.configurationError": "<PERSON>im <PERSON> der Weltoptionen ist ein unerwarteter Fehler aufgetreten", "mco.errorMessage.realmsService.connectivity": "Verbindung zu Realms konnte nicht hergestellt werden: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Kompatible Version konnte nicht überprüft werden; erhaltene Antwort: %s", "mco.errorMessage.retry": "<PERSON><PERSON><PERSON><PERSON> wied<PERSON>n", "mco.errorMessage.serviceBusy": "Realms is derzeid schdarg ausgelasded.\n<PERSON><PERSON><PERSON> vorrs<PERSON>, disch in ä paar Minudn noch ma mid deim Realm zu vorrbindn.", "mco.gui.button": "Schaldfläsche", "mco.gui.ok": "<PERSON><PERSON><PERSON>", "mco.info": "Hinweis!", "mco.invited.player.narration": "%s wurde eingeladen", "mco.invites.button.accept": "<PERSON><PERSON><PERSON><PERSON>", "mco.invites.button.reject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.invites.nopending": "Keene ausschdehndn Einladungn!", "mco.invites.pending": "Neue Einladung(en)!", "mco.invites.title": "Ausschdehnde Einladungn", "mco.minigame.world.changeButton": "<PERSON><PERSON> auswähln", "mco.minigame.world.info.line1": "Dies ersäddsd deine Weld vorüborrgehnd mid eem Minischbiel!", "mco.minigame.world.info.line2": "Du kannsd schbädorr ohne Vorlusde zu deinorr urschbrünglischn Weld zurügggährn.", "mco.minigame.world.noSelection": "Bidde driff eene <PERSON>", "mco.minigame.world.restore": "Beende Minischbiel …", "mco.minigame.world.restore.question.line1": "Das Minischbiel würd beended un dei Realm würd wiedorrhergeschdelld.", "mco.minigame.world.restore.question.line2": "<PERSON><PERSON><PERSON> si<PERSON>, dass de dies dun möschdesd?", "mco.minigame.world.selected": "Ausgewähldes Minischbiel:", "mco.minigame.world.slot.screen.title": "Weld würd geweggseld …", "mco.minigame.world.startButton": "Weggsln", "mco.minigame.world.starting.screen.title": "Schdarde Minischbiel …", "mco.minigame.world.stopButton": "<PERSON>sch<PERSON><PERSON>dn", "mco.minigame.world.switch.new": "Ä andres Minischbiel auswähln?", "mco.minigame.world.switch.title": "Minischbiel weggsln", "mco.minigame.world.title": "Realm zu Minischbiel weggsln", "mco.news": "Realms‐Neuischkeedn", "mco.notification.dismiss": "Schließen", "mco.notification.transferSubscription.buttonText": "Jetzt übertragen", "mco.notification.transferSubscription.message": "Java‐Realms‐Abonnements werden in den Microsoft Store verschoben. Lass dein Abonnement nicht auslaufen!\nJetzt übertragen und 30 Tage Realms kostenlos erhalten.\nGehe zum Profil auf minecraft.net, um dein Abonnement zu übertragen.", "mco.notification.visitUrl.buttonText.default": "<PERSON>", "mco.notification.visitUrl.message.default": "Bitte besuche den nachfolgenden Link", "mco.onlinePlayers": "Spieler online", "mco.play.button.realm.closed": "Realm ist geschlossen", "mco.question": "<PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "<PERSON><PERSON><PERSON>", "mco.reset.world.experience": "Erfahrungsweldn", "mco.reset.world.generate": "Neue Weld", "mco.reset.world.inspiration": "Inschbirationsweldn", "mco.reset.world.resetting.screen.title": "Weld würd zurügggesäddsd …", "mco.reset.world.seed": "Sc<PERSON><PERSON>dwerd (Optschonal)", "mco.reset.world.template": "Realm-V<PERSON>lachn", "mco.reset.world.title": "Zurüggsäddsn", "mco.reset.world.upload": "Weld hochladn", "mco.reset.world.warning": "<PERSON>s würd de aggduelle Weld deines Realms ersäddsn", "mco.selectServer.buy": "Koofe een Realm!", "mco.selectServer.close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.closed": "Geschlossnes Realm", "mco.selectServer.closeserver": "Realm schließn", "mco.selectServer.configure": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.configureRealm": "Realm einrichten", "mco.selectServer.create": "Realm erzeuschn", "mco.selectServer.create.subtitle": "<PERSON><PERSON>hle die Welt für dein neues Realm aus", "mco.selectServer.expired": "Abgeloofnes Realm", "mco.selectServer.expiredList": "<PERSON><PERSON> is abgeloofn", "mco.selectServer.expiredRenew": "Erneuorn", "mco.selectServer.expiredSubscribe": "Abonniern", "mco.selectServer.expiredTrial": "<PERSON><PERSON> is abgeloofn", "mco.selectServer.expires.day": "<PERSON><PERSON><PERSON><PERSON> in eenem Dag ab", "mco.selectServer.expires.days": "Läufd in %s Dagn ab", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON>d bald ab", "mco.selectServer.leave": "Realm vorlassn", "mco.selectServer.loading": "Realm‐Auswahl wird geladen", "mco.selectServer.mapOnlySupportedForVersion": "Diese Weld würd in %s nisch undorrschdüddsd", "mco.selectServer.minigame": "Minischbiel:", "mco.selectServer.minigameName": "Minispiel: %s", "mco.selectServer.minigameNotSupportedInVersion": "Dieses Minischbiel kann nisch mit %s geschbield wärdn", "mco.selectServer.noRealms": "Du scheinst kein Realm zu haben. Füge ein Realm hinzu, um mit deinen Freunden zu spielen.", "mco.selectServer.note": "Hinwees:", "mco.selectServer.open": "Offnes Realm", "mco.selectServer.openserver": "Offnes Realm", "mco.selectServer.play": "Schbieln", "mco.selectServer.popup": "Realms is ä sischorrorr un eenfachorr Wäsch, Minecraft online mid bis zu 10 Freundn gleichzeidsch zu schbieln. Es undorrschdüddsd viele Minischbiele un eischene Weldn! Nur dorr Besiddsorr des Realms muss zahln.", "mco.selectServer.purchase": "Realm hinzufüschn", "mco.selectServer.trial": "<PERSON><PERSON><PERSON><PERSON>’s ma aus!", "mco.selectServer.uninitialized": "Realm erschdelln!", "mco.snapshot.createSnapshotPopup.text": "<PERSON> bi<PERSON> da<PERSON>, ein kostenloses Realm für Entwicklungsversionen zu erstellen, das mit deinem zahlungspflichtigen Realms‐Abonnement verknüpft wird. Dieses neue Entwicklungsversionen‐Realm kann so lange genutzt werden, wie das zahlungspflichtige Abonnement aktiv ist. An deinem bezahlten Realm ändert sich dadurch nichts.", "mco.snapshot.createSnapshotPopup.title": "Entwicklungsversionen‐Realm erstellen?", "mco.snapshot.creating": "Entwicklungsversionen‐Realm wird erstellt …", "mco.snapshot.description": "Verknüpft mit „%s“", "mco.snapshot.friendsRealm.downgrade": "Du <PERSON>ötigst die Version %s, um dieses Realm zu betreten.", "mco.snapshot.friendsRealm.upgrade": "%s muss das Realm hochstufen, bevor du in dieser Version darauf spielen kannst.", "mco.snapshot.paired": "Dieses Entwicklungsversionen‐Realm ist mit „%s“ verknüpft", "mco.snapshot.parent.tooltip": "Verwende die aktuelle Vollversion, um auf diesem Realm zu spielen.", "mco.snapshot.start": "Kostenloses Entwicklungsversionen‐Realm anlegen", "mco.snapshot.subscription.info": "Dies ist ein Realm für Entwicklungsversionen, das mit dem Abonnement deines Realms „%s“ verknüpft ist. E<PERSON> bleibt so lange aktiv, wie das damit verknüpfte Realm aktiv ist.", "mco.snapshot.tooltip": "Entwicklungsversionen‐Realms verschaffen dir einen Einblick in zukünftige Versionen von Minecraft, die möglicherweise neue Spielelemente und sonstige Änderungen enthalten.\n\nDeine normalen Realms kannst du in der aktuellen Vollversion abrufen.", "mco.snapshotRealmsPopup.message": "Realms gibt es nun auch in Entwicklungsversionen, beginnend mit 23w41a. Jedes Realms‐Abonnement wird um ein kostenloses Realm für Entwicklungsversionen ergänzt, das von deinem normalen Java‐Realm getrennt ist!", "mco.snapshotRealmsPopup.title": "Realms nun in Entwicklungsversionen verfügbar", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON> er<PERSON>", "mco.template.button.publisher": "Herausgäborr", "mco.template.button.select": "Auswähln", "mco.template.button.trailer": "Vorschau", "mco.template.default.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.info.tooltip": "Webseide des Herausgäborrs", "mco.template.name": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.select.failure": "De Lisde mid den Inhaldn diesorr Gadegorie kunnde nisch abgerufn wärdn. \nBidde üborrprüfe deine Indorrnedvorrbindung odorr vorrsuche es schbädorr noch ma.", "mco.template.select.narrate.authors": "Audorn: %s", "mco.template.select.narrate.version": "Fährsion %s", "mco.template.select.none": "<PERSON><PERSON><PERSON>, diese Inhaldsgadegorie scheind aggduell leer zu sein.\n<PERSON><PERSON>de schaue demnächsd noch ma vorbei, odorr er<PERSON><PERSON>,\nwenn de een Erschdellorr bist, %s.", "mco.template.select.none.linkTitle": "selbsd een Inhald einzure<PERSON>n", "mco.template.title": "Realm-V<PERSON>lachn", "mco.template.title.minigame": "Minischbiele", "mco.template.trailer.tooltip": "Weldvorschau", "mco.terms.buttons.agree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.terms.buttons.disagree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.terms.sentence.1": "<PERSON>ch aggsebdiere de Minecraft Realms", "mco.terms.sentence.2": "Nudzungsbädingungn", "mco.terms.title": "Realms‐Nudzungsbädingungn", "mco.time.daysAgo": "vor %1$s Tag(en)", "mco.time.hoursAgo": "vor %1$s Stunde(n)", "mco.time.minutesAgo": "vor %1$s Minute(n)", "mco.time.now": "gerade eben", "mco.time.secondsAgo": "vor %1$s Sekunde(n)", "mco.trial.message.line1": "Möschdesde dei eischnes Realm?", "mco.trial.message.line2": "Gligge hier für mähr Informatschonen!", "mco.upload.button.name": "Hochladn", "mco.upload.cancelled": "Hochladn abgebrochn", "mco.upload.close.failure": "Kunnde dein Realm nisch schließn, bidde vorrsuche es schbädorr noch ma", "mco.upload.done": "Hochladn färdsch", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Hochladn gescheidord! (%s)", "mco.upload.failed.too_big.description": "Die ausgewählte Welt ist zu groß. Die maximal zulässige Größe beträgt %s.", "mco.upload.failed.too_big.title": "<PERSON>lt zu groß", "mco.upload.hardcore": "Weldn im Hardcore-Modus könn’ nisch hochgeladn wärdn!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Bereide deine Welddadn vor", "mco.upload.select.world.none": "Keene Eenslschbielorrweldn gefundn!", "mco.upload.select.world.subtitle": "Bidde wähle eene Eenslschbielorrweld zum Hochladn", "mco.upload.select.world.title": "Weld hochladn", "mco.upload.size.failure.line1": "‚%s‘ is zu groß!", "mco.upload.size.failure.line2": "Es is %s. De maggsimal zul<PERSON>ss’sche Greeße is %s.", "mco.upload.uploading": "Lade ‚%s‘ hoch", "mco.upload.verifying": "Üborrprüfe <PERSON>", "mco.version": "Version: %s", "mco.warning": "Bass uff!", "mco.worldSlot.minigame": "Minischbiel", "menu.custom_options": "Benutzerdefinierte Optionen …", "menu.custom_options.title": "Benutzerdefinierte Optionen", "menu.custom_options.tooltip": "Hin<PERSON>s: Benutzerdefinierte Optionen werden von Servern und/oder Inhalten Dritter bereitgestellt.\nSei vorsichtig!", "menu.custom_screen_info.button_narration": "Dies ist ein benutzerdefiniertes Menü. Mehr erfahren.", "menu.custom_screen_info.contents": "Die Inhalte dieses Fensters werden von <PERSON> und Karten Dritter definiert, die Mojang Studios oder Microsoft weder gehören, noch von ihnen betrieben oder überwacht werden.\n\nSei vorsichtig! Lasse immer Vorsicht walten, wenn du Internetadressen folgst, und verrate niemals deine persönlichen Daten (wie deine Anmeldedaten).\n\nWenn dieses Fenster dich am Spielen hindert, kannst du deine Verbindung zum aktuellen Server auch über die untenstehende Schaltfläche trennen.", "menu.custom_screen_info.disconnect": "Benutzerdefiniertes Menü abgelehnt", "menu.custom_screen_info.title": "<PERSON><PERSON><PERSON><PERSON> zu benutzerdefinierten Menü<PERSON>", "menu.custom_screen_info.tooltip": "Dies ist ein benutzerdefiniertes Menü. <PERSON><PERSON><PERSON> hier, um mehr zu erfahren.", "menu.disconnect": "Vorrbindung drennen", "menu.feedback": "Rückmeldung …", "menu.feedback.title": "Rückmeldung", "menu.game": "Schbielmenü", "menu.modded": " (modifizierd)", "menu.multiplayer": "Mährschbielorr", "menu.online": "Minecraft Realms", "menu.options": "<PERSON><PERSON><PERSON><PERSON> …", "menu.paused": "Schbiel angehaldn", "menu.playdemo": "<PERSON><PERSON><PERSON>ld sch<PERSON>ln", "menu.playerReporting": "<PERSON><PERSON><PERSON><PERSON><PERSON> meldn", "menu.preparingSpawn": "Einschdiegsbereisch würd angezeischd: %s%%", "menu.quick_actions": "Schnellaktionen …", "menu.quick_actions.title": "Schnellaktionen", "menu.quit": "<PERSON><PERSON><PERSON><PERSON>dn", "menu.reportBugs": "<PERSON><PERSON><PERSON><PERSON><PERSON> meldn", "menu.resetdemo": "<PERSON><PERSON><PERSON><PERSON>ädd<PERSON>n", "menu.returnToGame": "Zurügg zum Schbiel", "menu.returnToMenu": "Schbeischorrn un zurügg zum Haubdmenü", "menu.savingChunks": "<PERSON><PERSON> wärdn geschbeischorrd", "menu.savingLevel": "Weld würd geschbeischorrd", "menu.sendFeedback": "Feedback gäbn", "menu.server_links": "Server‐Links …", "menu.server_links.title": "Server‐Links", "menu.shareToLan": "Im LAN uffmachn", "menu.singleplayer": "Eenslschbielorr", "menu.working": "<PERSON><PERSON><PERSON> bearbeided …", "merchant.deprecated": "Dorfbewohnorr fülln ihrn Warnbeschdand bis zu zweemal am Daach uff.", "merchant.level.1": "<PERSON><PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.3": "<PERSON><PERSON><PERSON>", "merchant.level.4": "Eggsbärrde", "merchant.level.5": "<PERSON><PERSON><PERSON>", "merchant.title": "%s – %s", "merchant.trades": "Ang<PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Drügge %1$s, um abzuschdeign", "multiplayer.applyingPack": "Resso<PERSON><PERSON><PERSON><PERSON><PERSON> würd an<PERSON>wand", "multiplayer.confirm_command.parse_errors": "<PERSON>, einen unbekannten oder ungültigen Befehl auszuführen.\nBist du sicher?\nBefehl: %s", "multiplayer.confirm_command.permissions_required": "<PERSON>, einen <PERSON>, der ein erhöhtes Berechtigungslevel erfordert.\nDies könnte sich negativ auf dein Spiel auswirken.\nBist du sicher?\nBefehl: %s", "multiplayer.confirm_command.title": "Befehlsausführung bestätigen", "multiplayer.disconnect.authservers_down": "De Audhendifizierungssörvorr sind derzeid nisch erreischboar. Bidde vorrsuche es noch ma.", "multiplayer.disconnect.bad_chat_index": "Verpasste oder umgeordnete Chatnachricht vom Server erkannt", "multiplayer.disconnect.banned": "Du wurdesd uff dies’m <PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "multiplayer.disconnect.banned.expiration": "\nDei Schberre würd am %s uffgehobn", "multiplayer.disconnect.banned.reason": "Du wurdesd uff dies’m <PERSON><PERSON>rvorr geschberrd.\nGrund: %s", "multiplayer.disconnect.banned_ip.expiration": "\nDei Schberre würd am %s uffgehobn", "multiplayer.disconnect.banned_ip.reason": "Deine IP-<PERSON><PERSON><PERSON> wurde uff dies’m Sörvorr geschberrd.\nGrund: %s", "multiplayer.disconnect.chat_validation_failed": "Fählorr bei dorr Üborrprüfung dorr Chatnachrischd", "multiplayer.disconnect.duplicate_login": "<PERSON> hasd disch von eem and<PERSON><PERSON> aus angemälded", "multiplayer.disconnect.expired_public_key": "Der öffentliche Profilschlüssel ist abgelaufen. Überprüfe, ob deine Systemzeit synchronisiert ist, und versuche, dein Spiel neu zu starten.", "multiplayer.disconnect.flying": "<PERSON><PERSON><PERSON><PERSON> is uff dies’m <PERSON><PERSON><PERSON><PERSON><PERSON> nisch erloobd", "multiplayer.disconnect.generic": "Vorrbindung gedrennd", "multiplayer.disconnect.idling": "Du warsd zu lange undädisch!", "multiplayer.disconnect.illegal_characters": "Unzuläss’sche Zeischn im Chat", "multiplayer.disconnect.incompatible": "Ingombadiblorr Cliend! Vorrwende bidde %s", "multiplayer.disconnect.invalid_entity_attacked": "<PERSON>s wurde vor<PERSON>, een ungüldsches Objegd anzugreifn", "multiplayer.disconnect.invalid_packet": "<PERSON><PERSON> had een ungüld<PERSON><PERSON> Baged gesended", "multiplayer.disconnect.invalid_player_data": "Ungüldsche Schbielorrdadn", "multiplayer.disconnect.invalid_player_movement": "Ungüldsches Baged zur Schbielorrbewegung empfangn", "multiplayer.disconnect.invalid_public_key_signature": "Ungüldsche Signadur für den öffendlischn Brofilschlüssl.\nVorrsuch, dei Schbiel neu zu schdardn.", "multiplayer.disconnect.invalid_public_key_signature.new": "Ungültige Signatur für den öffentlichen Profilschlüssel.\nVersuche, dein Spiel neu zu starten.", "multiplayer.disconnect.invalid_vehicle_movement": "Ungüldsches Baged zur Fahrzeuschbewegung empfangn", "multiplayer.disconnect.ip_banned": "Deine IP-<PERSON><PERSON><PERSON> wurde uff diesm Sörvorr g<PERSON>berrd", "multiplayer.disconnect.kicked": "<PERSON><PERSON> had disch vom Sörvorr geworfn", "multiplayer.disconnect.missing_tags": "Unvollschdändschorr Edigeddnsadds vom Sörvorr empfangn.\nBidde benachrischdsche den Sörvorrbedreiborr.", "multiplayer.disconnect.name_taken": "Diesorr Name würd bereids vorrwended", "multiplayer.disconnect.not_whitelisted": "Du bisd nisch uff dorr G<PERSON>delisde dieses Sörvorrs!", "multiplayer.disconnect.out_of_order_chat": "<PERSON>erward<PERSON>s <PERSON>d empfangn. Had sisch deine Sysdemzeid geändorrd?", "multiplayer.disconnect.outdated_client": "Ingombadiblorr Cliend! Vorrwende bidde %s", "multiplayer.disconnect.outdated_server": "Ingombadiblorr Cliend! Vorrwende bidde %s", "multiplayer.disconnect.server_full": "<PERSON><PERSON> is voll!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer.disconnect.slow_login": "<PERSON><PERSON>g had zu lange gedauorrd", "multiplayer.disconnect.too_many_pending_chats": "Zu viele unbeschdädschde Chatnachrischdn", "multiplayer.disconnect.transfers_disabled": "Der Server nimmt keine Übertragungen entgegen.", "multiplayer.disconnect.unexpected_query_response": "Unerwardede benuddsorrdefinierde Dadn vom Cliend empfangn", "multiplayer.disconnect.unsigned_chat": "Chatbaged mid fählendorr odorr ungüldschorr Signadur empfangn.", "multiplayer.disconnect.unverified_username": "Benuddsorrname kunnde nisch üborrprüfd wärdn!", "multiplayer.downloadingStats": "Schdadisdign wärdn abgerufn …", "multiplayer.downloadingTerrain": "<PERSON><PERSON><PERSON><PERSON> würd geladn …", "multiplayer.lan.server_found": "Neuen Server gefunden: %s", "multiplayer.message_not_delivered": "Chatnachrischd kunnde nisch zugeschdelld wärdn, bidde Brodogolldadein des Sörvorrs prüfn: %s", "multiplayer.player.joined": "%s hads <PERSON>h<PERSON>l <PERSON>", "multiplayer.player.joined.renamed": "%s (frühorr begannd als %s) hads Schbiel bedrädn", "multiplayer.player.left": "%s hads Schbiel vorlassn", "multiplayer.player.list.hp": "%s LP", "multiplayer.player.list.narration": "Schbielorr online: %s", "multiplayer.requiredTexturePrompt.disconnect": "<PERSON><PERSON><PERSON><PERSON><PERSON> er<PERSON>rd ee benuddsorrdefinierdes Ressourcenbaged", "multiplayer.requiredTexturePrompt.line1": "<PERSON><PERSON><PERSON><PERSON><PERSON>orrwendung eenes ben<PERSON><PERSON><PERSON><PERSON>ierdn Ressourcenbageds.", "multiplayer.requiredTexturePrompt.line2": "<PERSON><PERSON> de dieses benuddsorrdefinierde Ressourcenbaged ablehn<PERSON>d, wirs<PERSON> von diesm Sörvorr gedrennd.", "multiplayer.socialInteractions.not_available": "Soodsschale Indoraggtschonen sin nur in Mährschbielorweldn vorfügboar", "multiplayer.status.and_more": "… un %s weido<PERSON> …", "multiplayer.status.cancelled": "Abgebrochn", "multiplayer.status.cannot_connect": "De Vorrbindung zum Sörvorr kunnde ni hergeschdelld wärn", "multiplayer.status.cannot_resolve": "De Sörvorradresse gonnde ni uffgelösd wärdn", "multiplayer.status.finished": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Ingombadible Fährsion!", "multiplayer.status.motd.narration": "Nachricht des Tages: %s", "multiplayer.status.no_connection": "(<PERSON><PERSON>orrb<PERSON>ung)", "multiplayer.status.old": "Ald", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping: %s Millisekunden", "multiplayer.status.pinging": "<PERSON><PERSON><PERSON><PERSON><PERSON> …", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s von %s Schbielorrn online", "multiplayer.status.quitting": "<PERSON><PERSON><PERSON> beended", "multiplayer.status.request_handled": "Schdadusanfrache wurde bearbeided", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Schdadus ohne Anfrache erhaldn", "multiplayer.status.version.narration": "Sörvorr‐Fährsion: %s", "multiplayer.stopSleeping": "<PERSON><PERSON><PERSON>de<PERSON>", "multiplayer.texturePrompt.failure.line1": "Das Ressourcenbaged des Sörvorrs konnde nisch angewand wärdn", "multiplayer.texturePrompt.failure.line2": "<PERSON><PERSON>, die benuddsorrdefinierde Ressourcen erfordorrd, arbeided möglischorrweese nisch wie erwarded", "multiplayer.texturePrompt.line1": "<PERSON><PERSON> schl<PERSON>chd ee Ressourcenbaged vorr.", "multiplayer.texturePrompt.line2": "Möschdesde es audomagisch rundorrladn un inschdalliern?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nNachrischd vom Sörvorr:\n%s", "multiplayer.title": "Mährschbielorr schbieln", "multiplayer.unsecureserver.toast": "Die auf diesem Server gesendeten Nachrichten können verändert werden und entsprechen möglicherweise nicht der ursprünglichen Nachricht.", "multiplayer.unsecureserver.toast.title": "Chatnachrischdn könn nisch üborrprüfd wärdn", "multiplayerWarning.check": "Diesn Hinwees nisch mehr anzeischn", "multiplayerWarning.header": "Pass uff: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von Driddanbiedorrn", "multiplayerWarning.message": "Vorsischd: <PERSON><PERSON>lorrmodus würd üborr Driddanbiedorr‐Sörvorr angebodn, die Mojang Studios odorr Microsoft wedorr gehörn, noch von diesn bedriebn odorr üborrwachd wärdn. Bei dorr Vorrwendung des Mährschbielorrmodus könndesde unmodorrierdn Chatnachrischdn odorr sonsdschn nudsorgenorierdn Inhaldn ausgesäddsd sein, die womöglisch nisch für jedn angemessn sin.", "music.game.a_familiar_room": "<PERSON> – A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> – An Ordinary Day", "music.game.ancestry": "<PERSON> Ancestry", "music.game.below_and_above": "<PERSON> – Below and Above", "music.game.broken_clocks": "<PERSON> – Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 <PERSON> <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> – Comforting Memories", "music.game.creative.aria_math": "C418 – Aria Math", "music.game.creative.biome_fest": "C418 – Biome Fest", "music.game.creative.blind_spots": "C418 – Blind Spots", "music.game.creative.dreiton": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 <PERSON> <PERSON><PERSON>", "music.game.creative.taswell": "C418 – Taswell", "music.game.crescent_dunes": "<PERSON> – Crescent Dunes", "music.game.danny": "C418 – <PERSON>", "music.game.deeper": "<PERSON> – <PERSON>er", "music.game.dry_hands": "C418 – Dry Hands", "music.game.echo_in_the_wind": "<PERSON> – Echo in the Wind", "music.game.eld_unknown": "<PERSON> – <PERSON><PERSON>", "music.game.end.alpha": "C418 – Alpha", "music.game.end.boss": "C418 – <PERSON>", "music.game.end.the_end": "C418 – The End", "music.game.endless": "<PERSON> – End<PERSON>", "music.game.featherfall": "<PERSON> – Featherfall", "music.game.fireflies": "<PERSON> – Fireflies", "music.game.floating_dream": "<PERSON><PERSON> – Floating Dream", "music.game.haggstrom": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> – Infinite Amethyst", "music.game.key": "C418 – Key", "music.game.komorebi": "<PERSON><PERSON> komorebi", "music.game.left_to_bloom": "<PERSON> – Left to Bloom", "music.game.lilypad": "<PERSON> – Lily<PERSON>", "music.game.living_mice": "C418 – <PERSON> Mice", "music.game.mice_on_venus": "C418 – <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 – Mine<PERSON>", "music.game.nether.ballad_of_the_cats": "C418 – Ballad of the Cats", "music.game.nether.concrete_halls": "C418 – Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 – <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> – <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> – So Below", "music.game.nether.warmth": "C418 – Warmth", "music.game.one_more_day": "<PERSON> – One More Day", "music.game.os_piano": "<PERSON> – <PERSON>’s Piano", "music.game.oxygene": "C418 – Oxygène", "music.game.pokopoko": "<PERSON><PERSON> poko<PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> Puzzlebox", "music.game.stand_tall": "<PERSON> – Stand Tall", "music.game.subwoofer_lullaby": "C418 – Subwoofer Lullaby", "music.game.swamp.aerie": "<PERSON> <PERSON><PERSON>", "music.game.swamp.firebugs": "<PERSON> – Firebugs", "music.game.swamp.labyrinthine": "<PERSON> – Labyrinthine", "music.game.sweden": "C418 – Sweden", "music.game.watcher": "<PERSON> – Watcher", "music.game.water.axolotl": "C418 – Axolotl", "music.game.water.dragon_fish": "C418 – <PERSON> Fish", "music.game.water.shuniji": "C418 – <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON> – <PERSON>", "music.game.wet_hands": "C418 – <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON> yakusoku", "music.menu.beginning_2": "C418 – Beginning 2", "music.menu.floating_trees": "C418 – Floating Trees", "music.menu.moog_city_2": "C418 – Moog City 2", "music.menu.mutation": "C418 – Mutation", "narration.button": "Gnopp: %s", "narration.button.usage.focused": "Eengabedas<PERSON> drüggn, um zu aggdiviern", "narration.button.usage.hovered": "<PERSON><PERSON><PERSON><PERSON>, um zu aggdiviern", "narration.checkbox": "Gondrollgäsdschn: %s", "narration.checkbox.usage.focused": "Eengabedasde drüggn, um umzuschaldn", "narration.checkbox.usage.hovered": "Ling<PERSON>gligg, um umzuschaldn", "narration.component_list.usage": "Tabuladordasde drüggn, um zum näschsdn Elemend zu weggsln", "narration.cycle_button.usage.focused": "Eengabedas<PERSON> drüggn, um zu %s zu weggsln", "narration.cycle_button.usage.hovered": "<PERSON><PERSON><PERSON><PERSON>, um zu %s zu weggsln", "narration.edit_box": "Bearbeidungsfeld: %s", "narration.item": "Gegenstand: %s", "narration.recipe": "Rezebd für %s", "narration.recipe.usage": "<PERSON><PERSON><PERSON><PERSON>, um auszuw<PERSON>hln", "narration.recipe.usage.more": "Reschdsgligg, um mähr Rezebde anzuzeischn", "narration.selection.usage": "<PERSON><PERSON><PERSON> odorr undere Pfeildasde drüggn, um zu eem andren <PERSON> zu weggsln", "narration.slider.usage.focused": "Linge odorr reschde Pfeildasde drüggn, um den Werd zu ändorrn", "narration.slider.usage.hovered": "Schiebereschlor<PERSON> z<PERSON>hn, um den Werd zu ändorrn", "narration.suggestion": "Vorschlach %s von %s ausgewähld: %s", "narration.suggestion.tooltip": "Vorschlach %s von %s ausgewähld: %s (%s)", "narration.suggestion.usage.cycle.fixed": "<PERSON>ücke die Tabulatortaste, um zum nächsten Vorschlag zu blättern", "narration.suggestion.usage.cycle.hidable": "<PERSON><PERSON>e die Tabulatortaste, um zum nächsten Vorschlag zu blättern, oder Escape, um die Vorschläge zu verlassen", "narration.suggestion.usage.fill.fixed": "Drücke die Tabulatortaste, um den Vorschlag zu verwenden", "narration.suggestion.usage.fill.hidable": "<PERSON><PERSON>e die Tabulatortaste, um den Vorschlag zu verwenden, oder Escape, um die Vorschläge zu verlassen", "narration.tab_navigation.usage": "Drücke Steuerung und die Tabulatortaste, um zwischen den Reitern zu wechseln", "narrator.button.accessibility": "Barr<PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock": "Schwierischkeedsschberre", "narrator.button.difficulty_lock.locked": "G<PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "<PERSON><PERSON><PERSON><PERSON>", "narrator.button.language": "<PERSON><PERSON><PERSON><PERSON>", "narrator.controls.bound": "%s is uff %s gesäddsd", "narrator.controls.reset": "%s uff <PERSON><PERSON><PERSON><PERSON><PERSON> zurüggsäddsn", "narrator.controls.unbound": "%s is nisch zugewiesn", "narrator.joining": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.loading": "<PERSON><PERSON><PERSON> geladn: %s", "narrator.loading.done": "<PERSON><PERSON><PERSON><PERSON>", "narrator.position.list": "Lisdnzeile %s von %s ausgewähld", "narrator.position.object_list": "Zeilnelemend %s von %s ausgewähld", "narrator.position.screen": "Bildschürm-Elemend %s von %s", "narrator.position.tab": "Reiter %s von %s ausgewählt", "narrator.ready_to_play": "Be<PERSON><PERSON> zu spielen", "narrator.screen.title": "Haubdmenü", "narrator.screen.usage": "Vorrwende den Mauszeischorr odorr de Tabuladordasde, um ä Elemend auszuwähln", "narrator.select": "Ausgewähld: %s", "narrator.select.world": "%s, zuläddsd geschbield: %s, %s, %s, Fährsion: %s", "narrator.select.world_info": "%s, zuletzt gespielt: %s, %s", "narrator.toast.disabled": "Schbrachausgabe deaggdivierd", "narrator.toast.enabled": "Schbrachausgabe aggdivierd", "optimizeWorld.confirm.description": "Dies würd deine Weld dursch Schbeischorrn dorr Dadn im neusden Formad obdimiern. Dies kann eene sehr lange <PERSON> da<PERSON>, abh<PERSON><PERSON><PERSON> von dorr Greeße deinorr Weld. <PERSON><PERSON><PERSON> du<PERSON>führd, kann deine Weld im Schbiel schnellorr sein, kann jedoch nisch mähr mid äldren Fährsionen des Schbiels geladn wärdn. <PERSON><PERSON><PERSON> sisch<PERSON>, dass de fordfahrn möschdesd?", "optimizeWorld.confirm.proceed": "Kopie erstellen und optimieren", "optimizeWorld.confirm.title": "Weld obdimiern", "optimizeWorld.info.converted": "Aggdualisierde Chungs: %s", "optimizeWorld.info.skipped": "Üborrschbrungene Chungs: %s", "optimizeWorld.info.total": "Chungs insgesamd: %s", "optimizeWorld.progress.counter": "%s/%s", "optimizeWorld.progress.percentage": "%s %%", "optimizeWorld.stage.counting": "<PERSON><PERSON> wärdn gez<PERSON>hld …", "optimizeWorld.stage.failed": "Fählgeschlachn! :-(", "optimizeWorld.stage.finished": "Fertigstellen …", "optimizeWorld.stage.finished.chunks": "Chunk‐Aktualisierung wird abgeschlossen …", "optimizeWorld.stage.finished.entities": "Objektaktualisierung wird abgeschlossen …", "optimizeWorld.stage.finished.poi": "Zielpunktaktualisierung wird abgeschlossen …", "optimizeWorld.stage.upgrading": "Alle Chunks werden aktualisiert …", "optimizeWorld.stage.upgrading.chunks": "Alle Chunks werden aktualisiert …", "optimizeWorld.stage.upgrading.entities": "Alle Objekte werden aktualisiert …", "optimizeWorld.stage.upgrading.poi": "Alle Zielpunkte werden aktualisiert …", "optimizeWorld.title": "Weld ‚%s‘ würd obdimierd", "options.accessibility": "Barrierefreiheit …", "options.accessibility.high_contrast": "<PERSON><PERSON>", "options.accessibility.high_contrast.error.tooltip": "Das Ressourcenpaket für hohen Kontrast ist nicht verfügbar.", "options.accessibility.high_contrast.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Kontrast von Elementen der Benutzeroberfläche.", "options.accessibility.high_contrast_block_outline": "Kontras<PERSON><PERSON>", "options.accessibility.high_contrast_block_outline.tooltip": "<PERSON>rhöht den Kontrast der Umrisslinien des angezielten Blocks.", "options.accessibility.link": "Barrierefreiheedsleidfadn", "options.accessibility.menu_background_blurriness": "Menühintergrund‐Unschärfe", "options.accessibility.menu_background_blurriness.tooltip": "Steuert die Weichzeichnung der Menühintergründe.", "options.accessibility.narrator_hotkey": "Sprachausgaben‐Kurzanwahl", "options.accessibility.narrator_hotkey.mac.tooltip": "Ermöglicht es, die Sprachausgabe mit [⌘] + [B] ein‐ und auszuschalten.", "options.accessibility.narrator_hotkey.tooltip": "Ermöglicht es, die Sprachausgabe mit [Strg] + [B] ein‐ und auszuschalten.", "options.accessibility.panorama_speed": "Panorama‐Bildlaufgeschwindigkeit", "options.accessibility.text_background": "Däggsdhindorgrund", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "Üborrall", "options.accessibility.text_background_opacity": "Däggsdhindorgr.‐Degggr.", "options.accessibility.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> …", "options.allowServerListing": "Sörvorr-Ufflisdung zulassn", "options.allowServerListing.tooltip": "Sörvorr könn Online-Schbielorr als Deil ihres öffentlischn Schdadus ufflisdn.\nWenn diese Optschoon deaggdivierd is, erscheind dei Name nisch in solschn Lisdn.", "options.ao": "Weesche <PERSON>", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "Minimum", "options.ao.off": "Aus", "options.attack.crosshair": "Fadngreuz", "options.attack.hotbar": "Schnellzugr.", "options.attackIndicator": "Angriffsanzeische", "options.audioDevice": "<PERSON><PERSON><PERSON><PERSON>", "options.audioDevice.default": "Sysdemschdandard", "options.autoJump": "Audomatsch schbringn", "options.autoSuggestCommands": "Befählsvorschläsche", "options.autosaveIndicator": "Audoschbeischorrn-Anzeische", "options.biomeBlendRadius": "Biomüborrgang", "options.biomeBlendRadius.1": "Aus (am schnellsdn)", "options.biomeBlendRadius.11": "11×11 (eggsdreem)", "options.biomeBlendRadius.13": "13×13 (prods’sch)", "options.biomeBlendRadius.15": "15×15 (maggsimal)", "options.biomeBlendRadius.3": "3×3 (schnell)", "options.biomeBlendRadius.5": "5×5 (normal)", "options.biomeBlendRadius.7": "7×7 (hoch)", "options.biomeBlendRadius.9": "9×9 (s<PERSON>hr hoch)", "options.chat": "Chateinstellungen …", "options.chat.color": "Farbn", "options.chat.delay": "Chatvorzögerung: %s Segundn", "options.chat.delay_none": "Chatvorzögorrung: <PERSON><PERSON>", "options.chat.height.focused": "Heeje (geöffned)", "options.chat.height.unfocused": "<PERSON><PERSON><PERSON> (geschlossn)", "options.chat.line_spacing": "Zeilnabschdand", "options.chat.links": "Weblings", "options.chat.links.prompt": "<PERSON><PERSON> be<PERSON> lassn", "options.chat.opacity": "Chat-Däggsddegggrafd", "options.chat.scale": "Chat-Däggsdgreeße", "options.chat.title": "Chateenschdellungn …", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.hidden": "Vorschdeggd", "options.chat.visibility.system": "Nur Befähle", "options.chat.width": "<PERSON><PERSON><PERSON>", "options.chunks": "%s <PERSON>s", "options.clouds.fancy": "<PERSON><PERSON><PERSON><PERSON>", "options.clouds.fast": "<PERSON><PERSON><PERSON>", "options.controls": "Schdeuorung …", "options.credits_and_attribution": "Mitwirkende & Namensnennung …", "options.damageTiltStrength": "Schadensneigung", "options.damageTiltStrength.tooltip": "Die Stärke des Kamerawackelns, das beim Erleiden von Sc<PERSON>en auftritt.", "options.darkMojangStudiosBackgroundColor": "Eenfarbsches Logo", "options.darkMojangStudiosBackgroundColor.tooltip": "Ändorrd de Hindorrgrundfarbe des Ladebildschürms von Mojang Studios zu Schworz.", "options.darknessEffectScale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.darknessEffectScale.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wie schdarg dorr Effegd „Dunglheed“ bulsierd, wenn ein Wärdorr odorr Sculk‐Greischorr ihn an disch weidorrgibd.", "options.difficulty": "Schwierischkeed", "options.difficulty.easy": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.easy.info": "Feindliche Kreaturen erscheinen, verursachen aber nur geringen Schaden. Die Hungerleiste leert sich und zehrt die Gesundheit bis auf fünf <PERSON> auf.", "options.difficulty.hard": "Schwer", "options.difficulty.hard.info": "Feindliche Kreaturen erscheinen und verursachen hohen Schaden. Die Hungerleiste leert sich und kann zum Hungertod führen.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Feindliche Kreaturen erscheinen und verursachen normalen Schaden. Die Hungerleiste leert sich und zehrt die Gesundheit bis auf ein halbes Herz auf.", "options.difficulty.online": "Sörvorr-Schwierischkeed", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "<PERSON>ine feindlichen Kreaturen und nur manche neutrale Kreaturen erscheinen. Die Hungerleiste leert sich nicht und verlorene Gesundheit erholt sich mit der Zeit.", "options.directionalAudio": "Raumglang", "options.directionalAudio.off.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.directionalAudio.on.tooltip": "Vorrwended HRTF-b<PERSON><PERSON><PERSON> Raumglang, um de Simulatschoon von 3D-Don zu vorrbessorrn. Erfordorrd HRTF-gombadible Audiohardware un lässd sisch am besdn mid Gobbhörorrn erl<PERSON>hm.", "options.discrete_mouse_scroll": "Eischnschdändsches Bläddorrn", "options.entityDistanceScaling": "Endfernung zum Objegd", "options.entityShadows": "Objegdschaddn", "options.font": "Schriftarteinstellungen …", "options.font.title": "Schriftarteinstellungen", "options.forceUnicodeFont": "Unicode erzwingn", "options.fov": "Sisch<PERSON>feld", "options.fov.max": "Quake Pro", "options.fov.min": "Normal", "options.fovEffectScale": "Sischdfeldeffegde", "options.fovEffectScale.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wie schdarg das Sischdfeld dursch Schbielmeschanigeffegde vorrändorrd würd.", "options.framerate": "%s FPS", "options.framerateLimit": "<PERSON><PERSON>", "options.framerateLimit.max": "Unbegränzd", "options.fullscreen": "Vollbild", "options.fullscreen.current": "Aggduell", "options.fullscreen.entry": "%s × %s (%s Hz, %s Bit)", "options.fullscreen.resolution": "Vollbildufflösung", "options.fullscreen.unavailable": "Eenschdellung nisch vorfügboar", "options.gamma": "Hellischkeed", "options.gamma.default": "Schdandard", "options.gamma.max": "Hell", "options.gamma.min": "<PERSON><PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Schimmerbewegung", "options.glintSpeed.tooltip": "<PERSON><PERSON><PERSON>, wie schnell sich der Schimmer verzauberter Gegenstände bewegt.", "options.glintStrength": "Schimmerstärke", "options.glintStrength.tooltip": "<PERSON><PERSON><PERSON>, wie deutlich der Schimmer verzauberter Gegenstände sichtbar ist.", "options.graphics": "<PERSON>ig<PERSON><PERSON>", "options.graphics.fabulous": "<PERSON><PERSON>lhafd!", "options.graphics.fabulous.tooltip": "<PERSON><PERSON> „%s“ v<PERSON><PERSON><PERSON><PERSON>, um Weddorr, Wolgn un Bardiggl ooch hindorr lischddurchläss’schn Blöggn un Wassorr da<PERSON>uschdelln.\nDies kann de Leisdung uff dragbarn Gerädn un 4K‐Bildschürmen erheblisch beeindräschdschn.", "options.graphics.fancy": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fancy.tooltip": "<PERSON><PERSON> „<PERSON>hön“ schdelld uff’n meisdn Gerädn eenen Ausgleisch zwüschn Leisdung un Qualidäd her.\n<PERSON><PERSON><PERSON><PERSON>, Wolgn un Bardiggl könn hindorr lischddurchläss’schn Blöggn un Wassorr nisch angezeischd wärdn.", "options.graphics.fast": "<PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "<PERSON><PERSON> „Schnell“ reduzierd de Menge an sischdbarem Räschn un Schnee.\nTransbarenzeffeggde sin für vorschiedne Blögge wie Loob deaggdivierd.", "options.graphics.warning.accept": "Ohne Unterstützung fortfahren", "options.graphics.warning.cancel": "Bring mich zurück", "options.graphics.warning.message": "<PERSON><PERSON> wurde ergannd, dass deine Grafiggardde den Grafigmodus „%s“ nisch undorrschdüddsd.\n\nDu kannsd diese Mäldung ignoriern un fordfahrn, aborr für dei Geräd würd keene Undorschdüdzung angebodn, wenn de disch endsche<PERSON>d, den Grafigmodus „%s“ zu vorwendn.", "options.graphics.warning.renderer": "Rendororr ergannd: [%s]", "options.graphics.warning.title": "Grafiggardde würd ni undorrschdüddsd", "options.graphics.warning.vendor": "Herschdellorr ergannd: [%s]", "options.graphics.warning.version": "OpenGL-Fährsion ergannd: [%s]", "options.guiScale": "GUI-Greeße", "options.guiScale.auto": "Audomatsch", "options.hidden": "Vorschdeggd", "options.hideLightningFlashes": "Blidslischdorr vorrschdeggn", "options.hideLightningFlashes.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dass Blidse den Himml uffleuschdn lassn. De Blidse selborr sin weidorrhin sisch<PERSON>bar.", "options.hideMatchedNames": "Üborreenschdimmende Nam ausblendn", "options.hideMatchedNames.tooltip": "Driddanbiedorr-<PERSON><PERSON><PERSON><PERSON><PERSON> sendn Chatnachrischdn möglischorrweese in eem nisch schdandardmäßschn Formad.\nWenn diese Optschoon aggdivierd is, wärdn vorrschdeggde Schbielorr anhand dorr Absendorrnam diesn zugeordned.", "options.hideSplashTexts": "Menüsprüche ausblenden", "options.hideSplashTexts.tooltip": "Blendet die gelben Sprüche im Hauptmenü aus.", "options.inactivityFpsLimit": "FPS verringern, wenn", "options.inactivityFpsLimit.afk": "Abwesend", "options.inactivityFpsLimit.afk.tooltip": "Begrenzt die Bildwiederholrate auf 30 Bilder pro Sekunde, wenn der Spieler länger als 1 Minute keine Eingabe tätigt. Begrenzt sie nach 9 weiteren Minuten auf 10 Bilder pro Sekunde.", "options.inactivityFpsLimit.minimized": "Minimiert", "options.inactivityFpsLimit.minimized.tooltip": "Begrenzt die Bildwiederholrate nur dann, wenn das Spielfenster minimiert ist.", "options.invertMouse": "<PERSON><PERSON> um<PERSON>n", "options.japaneseGlyphVariants": "Japanische Glyphenvarianten", "options.japaneseGlyphVariants.tooltip": "Verwendet japanische Varianten von CJK‐Zeichen in der Standardschriftart.", "options.key.hold": "<PERSON><PERSON>", "options.key.toggle": "Umschaldn", "options.language": "<PERSON><PERSON><PERSON><PERSON> …", "options.language.title": "<PERSON><PERSON><PERSON>", "options.languageAccuracyWarning": "Die Übersetzungen sind möglicherweise nicht ganz genau.", "options.languageWarning": "De Üborrsäddsungn sin möglichorweese nisch zu 100 %% gorregd", "options.mainHand": "<PERSON><PERSON><PERSON><PERSON>", "options.mainHand.left": "<PERSON><PERSON>", "options.mainHand.right": "Re<PERSON><PERSON>", "options.mipmapLevels": "Mipmap-Schdufn", "options.modelPart.cape": "<PERSON><PERSON>", "options.modelPart.hat": "<PERSON><PERSON>", "options.modelPart.jacket": "J<PERSON>ge", "options.modelPart.left_pants_leg": "<PERSON><PERSON>", "options.modelPart.left_sleeve": "<PERSON><PERSON><PERSON>", "options.modelPart.right_pants_leg": "<PERSON><PERSON><PERSON>", "options.modelPart.right_sleeve": "Reschdorr Ärml", "options.mouseWheelSensitivity": "Mausradembfindlischkeed", "options.mouse_settings": "Mauseenschdellungn …", "options.mouse_settings.title": "Mauseenschdellungn", "options.multiplayer.title": "Mährschbielorreenschdellungn …", "options.multiplier": "%s×", "options.music_frequency": "Musikhäufigkeit", "options.music_frequency.constant": "Durchgä<PERSON><PERSON>", "options.music_frequency.default": "Standard", "options.music_frequency.frequent": "<PERSON><PERSON><PERSON><PERSON>", "options.music_frequency.tooltip": "<PERSON><PERSON><PERSON>, wie häufig Musik beim Spielen in einer Welt wiedergegeben wird.", "options.narrator": "Schbrachausgabe", "options.narrator.all": "<PERSON>d alles", "options.narrator.chat": "<PERSON><PERSON>", "options.narrator.notavailable": "Nisch vorrfügbar", "options.narrator.off": "Aus", "options.narrator.system": "Liesd Sysdem", "options.notifications.display_time": "Benachrichtigungsdauer", "options.notifications.display_time.tooltip": "Beeinflusst die Zeitspanne, in der alle Benachrichtigungen auf dem Bildschirm sichtbar bleiben.", "options.off": "Aus", "options.off.composed": "%s: Aus", "options.on": "An", "options.on.composed": "%s: An", "options.online": "Online …", "options.online.title": "Online-Eenschdellungn", "options.onlyShowSecureChat": "<PERSON><PERSON> si<PERSON><PERSON> anzeischn", "options.onlyShowSecureChat.tooltip": "<PERSON>eisch nur Nachrischdn von andren <PERSON>rn an, die nachweeslisch von diesn gesended un nisch vorrändorrd wurdn.", "options.operatorItemsTab": "Operatorhilfsmittel‐Reiter", "options.particles": "Bardiggl", "options.particles.all": "Alle", "options.particles.decreased": "Vorringord", "options.particles.minimal": "Minimal", "options.percent_add_value": "%s: +%s %%", "options.percent_value": "%s: %s %%", "options.pixel_value": "%s: %s px", "options.prioritizeChunkUpdates": "Chunggombilierung", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON> bloggiernd", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Einische Aggtschonen innorrhalb eenes Chungs führn zu eenorr sofordschn Neugombilierung des Chungs. Dazu gehörn das Bladdsiern und Zorrschdö<PERSON> von Blö<PERSON>n.", "options.prioritizeChunkUpdates.nearby": "Ganz bloggiernd", "options.prioritizeChunkUpdates.nearby.tooltip": "Benachbarde Chungs wärdn immorr soford gombilierd. Dies kann de Schbielleisdung beeindr<PERSON><PERSON><PERSON><PERSON>, wenn Blögge bladdsierd odorr zorrsch<PERSON>örd wärdn.", "options.prioritizeChunkUpdates.none": "Barallel", "options.prioritizeChunkUpdates.none.tooltip": "Benachbarde Chungs wärdn barallel gombilierd. Dies kann zu kurds sischdbarn Löschorrn führn, wenn de Blögge zorrschdörd wärdn.", "options.rawMouseInput": "<PERSON><PERSON><PERSON>", "options.realmsNotifications": "Realms‐Neuigkeiten & Einladungen", "options.realmsNotifications.tooltip": "Ruft Realms‐Neuigkeiten und ‐Einladungen im Hauptmenü ab und zeigt ihre jeweiligen Symbole auf der Schaltfläche „Realms“ an.", "options.reducedDebugInfo": "Reduzierde Debug-Infos", "options.renderClouds": "Wolgn", "options.renderCloudsDistance": "Wolkenreichweite", "options.renderDistance": "Sischdweide", "options.resourcepack": "Ressourcenbagede …", "options.rotateWithMinecart": "<PERSON><PERSON> <PERSON> mit<PERSON>", "options.rotateWithMinecart.tooltip": "<PERSON><PERSON><PERSON>, ob sich die Blickrichtung des Spielers mit Loren mitdreht. Nur in Welten verfügbar, in denen das Experiment „Lorenverbesse‐ rungen“ eingeschaltet ist.", "options.screenEffectScale": "Vorzärrungseffegde", "options.screenEffectScale.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>keed- un Netherbordal-Vorzerrungseffegdn.\nBei niedrigoren Werdn würd dorr Übelkeed-Effegd dursch eene griene Üborrlagorung ausgedauschd.", "options.sensitivity": "Empfindlischkeed", "options.sensitivity.max": "DURBO!!!", "options.sensitivity.min": "*gähn*", "options.showNowPlayingToast": "Liedtitel einblenden", "options.showNowPlayingToast.tooltip": "<PERSON><PERSON>gt eine Einblendung, sobald ein Lied zu spielen beginnt. Während das Lied abgespielt wird, ist diese Einblendung außerdem im Pausenmenü zu sehen.", "options.showSubtitles": "Undorrdidl anzeischn", "options.simulationDistance": "Simulatschoonsweide", "options.skinCustomisation": "Schginn-Anbassung …", "options.skinCustomisation.title": "Schginn-Anbassung", "options.sounds": "Musigg un Geräusche …", "options.sounds.title": "Musigg- & Ger<PERSON><PERSON><PERSON>chonen", "options.telemetry": "Telemetriedaten …", "options.telemetry.button": "Datenerhebung", "options.telemetry.button.tooltip": "„%s“ enthält nur erforderliche Daten.\n„%s“ enthält sowohl optionale als auch erforderliche Daten.", "options.telemetry.disabled": "Telemetrie ist deaktiviert.", "options.telemetry.state.all": "Alles", "options.telemetry.state.minimal": "Minimal", "options.telemetry.state.none": "<PERSON><PERSON>", "options.title": "<PERSON><PERSON><PERSON><PERSON>", "options.touchscreen": "Dadschsgrien-Modus", "options.video": "Grafigeenschdellungn …", "options.videoTitle": "Grafigeenschdellungn", "options.viewBobbing": "Gehbeweschung", "options.visible": "<PERSON><PERSON><PERSON><PERSON>", "options.vsync": "VSync", "outOfMemory.message": "Minecraft had keen frein Arbeedsschbeischorr mähr.\n\nDas kann an eem Fählorr im Schbiel lieschn odorr daran, dass dorr Java Virtual Machine nisch genüschnd Arbeedsschbeeschorr zugewiesn wurde.\n\nUm Beschädschungn an dorr Weld zu vorhindorrn, wurde das aggduelle Schbiel beended. Wir ham vorrsuchd, genüschnd Arbeedsschbeischorr freizugehm, damid de zum Ha<PERSON>dmenü zurügggehrn un weidorrschbieln kannsd, aborr das had möglischorrweese nisch fungtschoonierd.\n\nBidde schdarde das Schbiel neu, wenn de diese Meldung erneud siehsd.", "outOfMemory.title": "Kee freiorr Arbeedsschbeischorr mähr!", "pack.available.title": "Vorfügboar", "pack.copyFailure": "Bagede kunnd’n nisch gobierd wärdn", "pack.dropConfirm": "Möschdesd du de folgendn Bagede zu Minecraft hinzufüschn?", "pack.dropInfo": "Um Bagede hinzuzufüschn, ziehe sie in dieses Fensdorr nei", "pack.dropRejected.message": "Die folgenden Einträge waren keine gültigen Pakete und wurden nicht kopiert:\n %s", "pack.dropRejected.title": "Nicht‐Paket‐Einträge", "pack.folderInfo": "(Bageddadein hier einfüschn)", "pack.incompatible": "Ingombadibl", "pack.incompatible.confirm.new": "Dieses Baged wurde für eene neuore Fährsion von Minecraft erschdelld un fungtschoonierd möglischorrweese nisch rischdsch.", "pack.incompatible.confirm.old": "Dieses Baged wurde für eene äldore Fährsion von Minecraft erschdelld un funktschonierd möglischorrweese nisch mähr rischdsch.", "pack.incompatible.confirm.title": "<PERSON><PERSON><PERSON>, dass de dieses Baged ladn möschdesd?", "pack.incompatible.new": "(<PERSON><PERSON><PERSON> <PERSON>ne neuore Fährsion von Minecraft erschdelld)", "pack.incompatible.old": "(<PERSON><PERSON><PERSON> <PERSON>ne äldre Fährsion von Minecraft erschdelld)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Bagedord<PERSON><PERSON>", "pack.selected.title": "Ausgewähld", "pack.source.builtin": "<PERSON>gelieford", "pack.source.feature": "Schbielelemend", "pack.source.local": "logal", "pack.source.server": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.world": "Weld", "painting.dimensions": "%s × %s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "<PERSON>", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Der Hinterhof", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "<PERSON><PERSON>", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON>iel erfolgreich bombardiert", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Der Blumenstrauß", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Der Totenkopf in Flammen", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON> Büste", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Der Höhlenvogel", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Der Kostümwechsel", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Die Begegnung", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Erde", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Der Endgegner", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "<PERSON> Kämpfer", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Der Fund", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Bodenständig", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Der Döner mit drei Pfefferonen", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Nebelschwaden", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditativ", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON> Kugel", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Die Zitruseule", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Der Durchgang", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Das Schweinebildnis", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Der Pfennigbaum", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Der Zeigefinger", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "<PERSON>", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "<PERSON>", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "<PERSON>", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "<PERSON>ü<PERSON>", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Die Mühsal des Irdischen", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Ein Totenkopf und Rosen", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "Die Bühne ist bereitet", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "<PERSON> Sonnenblumen", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "<PERSON>", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Die Gezeiten", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Entpackt", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "<PERSON>", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Die Ödnis", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "<PERSON><PERSON>", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Luft", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>", "painting.random": "Zufälliges Motiv", "parsing.bool.expected": "Wahrheidswerd erwarded", "parsing.bool.invalid": "Ungüldschorr Wahrheidswerd: ‚%s‘ is wedorr ‚true‘ noch ‚false‘", "parsing.double.expected": "<PERSON><PERSON><PERSON><PERSON> erwarded", "parsing.double.invalid": "Ungüldsche Gommazahl ‚%s‘", "parsing.expected": "‚%s‘ erwarded", "parsing.float.expected": "<PERSON><PERSON><PERSON><PERSON> erwarded", "parsing.float.invalid": "Ungüldsche Gommazahl ‚%s‘", "parsing.int.expected": "<PERSON><PERSON><PERSON><PERSON> erwarded", "parsing.int.invalid": "Ungüldsche Ganzzahl ‚%s‘", "parsing.long.expected": "<PERSON> erwarded", "parsing.long.invalid": "Ungüldsche lange Ganzzahl ‚%s‘", "parsing.quote.escape": "Ungüldsche Escape-Sequenz ‚\\%s‘ in Zeischngedde", "parsing.quote.expected.end": "Fählendes Anführungszeischn am Ende dorr Zeischngedde", "parsing.quote.expected.start": "Fählendes Anführungszeischn am Beginn dorr Zeischngedde", "particle.invalidOptions": "Partikeleigenschaften können nicht ausgewertet werden: %s", "particle.notFound": "<PERSON>be<PERSON>ndes Bardiggl ‚%s‘", "permissions.requires.entity": "Diesorr Befähl kann nur von eem Objegd ausgeführd wärdn", "permissions.requires.player": "Diesorr Befähl kann nur von eem Schbielorr ausgeführd wärdn", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Auswirgungn:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Unbeganndes Prädigad: %s", "quickplay.error.invalid_identifier": "Es konnte keine Welt mit der angegebenen Kennung gefunden werden", "quickplay.error.realm_connect": "Verbindung mit dem Realm konnte nicht hergestellt werden", "quickplay.error.realm_permission": "<PERSON><PERSON><PERSON><PERSON> Berechtigung, eine Verbindung mit diesem Realm herzustellen", "quickplay.error.title": "Schnellspielen fehlgeschlagen", "realms.configuration.region.australia_east": "New South Wales, Australien", "realms.configuration.region.australia_southeast": "Victoria, Australien", "realms.configuration.region.brazil_south": "Brasilien", "realms.configuration.region.central_india": "Indien", "realms.configuration.region.central_us": "Iowa, Vereinigte Staaten", "realms.configuration.region.east_asia": "Hongkong", "realms.configuration.region.east_us": "Virginia, Vereinigte Staaten", "realms.configuration.region.east_us_2": "North Carolina, Vereinigte Staaten", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.japan_west": "Westjapan", "realms.configuration.region.korea_central": "Südkorea", "realms.configuration.region.north_central_us": "Illinois, Vereinigte Staaten", "realms.configuration.region.north_europe": "Irland", "realms.configuration.region.south_central_us": "Texas, Vereinigte Staaten", "realms.configuration.region.southeast_asia": "Singapur", "realms.configuration.region.sweden_central": "Schweden", "realms.configuration.region.uae_north": "Vereinigte Arabische Emirate (VAE)", "realms.configuration.region.uk_south": "Südengland", "realms.configuration.region.west_central_us": "Utah, Vereinigte Staaten", "realms.configuration.region.west_europe": "Niederlande", "realms.configuration.region.west_us": "Kalifornien, Vereinigte Staaten", "realms.configuration.region.west_us_2": "Washington, Vereinigte Staaten", "realms.configuration.region_preference.automatic_owner": "Automatisch (nahe Realm‐Besitzer)", "realms.configuration.region_preference.automatic_player": "Automatisch (nahe 1. Spieler je Sitzung)", "realms.missing.snapshot.error.text": "Realms würd derzeid in Endwigglungsfährsionen nisch undorrschdüddsd", "recipe.notFound": "Unbeganndes Rezebd ‚%s‘", "recipe.toast.description": "<PERSON><PERSON><PERSON> in deine Rezebdschwahrde", "recipe.toast.title": "Neue Rezebde vorfügboar!", "record.nowPlaying": "Jäddse schbield: %s", "recover_world.bug_tracker": "Einen Fehler melden", "recover_world.button": "Wiederherstellung versuchen", "recover_world.done.failed": "Die Welt konnte nicht aus früherem Zustand wiederhergestellt werden.", "recover_world.done.success": "Die Welt wurde erfolgreich wiederhergestellt!", "recover_world.done.title": "Wiederherstellung abgeschlossen", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON><PERSON>", "recover_world.issue.none": "<PERSON><PERSON>", "recover_world.message": "<PERSON><PERSON>, den Weltordner „%s“ einzulesen, sind die unten angegebenen Fehler aufgetreten.\nMöglicherweise lässt sich die Welt aus einem älteren Zustand wiederherstellen, oder du kannst dieses Problem im Bugtracker melden.", "recover_world.no_fallback": "<PERSON><PERSON> Z<PERSON>and für eine Wiederherstellung vorhanden", "recover_world.restore": "Wiederherstellung versuchen", "recover_world.restoring": "Es wird versucht, die Welt wiederherzustellen …", "recover_world.state_entry": "<PERSON><PERSON><PERSON> von %s: ", "recover_world.state_entry.unknown": "unbekannt", "recover_world.title": "Laden der Welt fehlgeschlagen", "recover_world.warning": "Laden der Weltbeschreibung fehlgeschlagen", "resourcePack.broken_assets": "DEFEGDE RESSOURCEN ERGANND", "resourcePack.high_contrast.name": "<PERSON><PERSON>", "resourcePack.load_fail": "Neuladn dorr Ressourcen fählgeschlachn", "resourcePack.programmer_art.name": "Programmiererkunst", "resourcePack.runtime_failure": "Fehler in Ressourcenpaket erkannt", "resourcePack.server.name": "Weldschbezifische Ressourcen", "resourcePack.title": "Ressourcenbagede auswähln", "resourcePack.vanilla.description": "<PERSON> Standardaussehen von Minecraft", "resourcePack.vanilla.name": "Schdandard", "resourcepack.downloading": "Ressour<PERSON><PERSON><PERSON>d würd herundor<PERSON>n", "resourcepack.progress": "Dad<PERSON> würd herundorrgeladn (%s MB) …", "resourcepack.requesting": "Sende Anfrache …", "screenshot.failure": "<PERSON>nn Sgrienshot nisch schbeischorrn: %s", "screenshot.success": "Sgrienshot geschbeischorrd undorr %s", "selectServer.add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectServer.defaultName": "Minecraft-Sör<PERSON>r", "selectServer.delete": "L<PERSON><PERSON><PERSON>", "selectServer.deleteButton": "L<PERSON><PERSON><PERSON>", "selectServer.deleteQuestion": "<PERSON><PERSON><PERSON>, dass de diesn Sörvorr endfernen möschdesd?", "selectServer.deleteWarning": "‚%s‘ würd für immorr vorlorn sein! (<PERSON>e lange Zeid!)", "selectServer.direct": "Direggd vorrbindn", "selectServer.edit": "<PERSON><PERSON><PERSON>", "selectServer.hiddenAddress": "(Vorschdeggd)", "selectServer.refresh": "Aggdualisiern", "selectServer.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.access_failure": "Zugriff uff de Weld fählgeschlachn", "selectWorld.allowCommands": "Cheads erloobn", "selectWorld.allowCommands.info": "Bef<PERSON>hle wie /gamemode, /experience", "selectWorld.allowCommands.new": "<PERSON><PERSON><PERSON><PERSON> erlauben", "selectWorld.backupEraseCache": "Zwischngeschbeischorrde Dadn löschn", "selectWorld.backupJoinConfirmButton": "Gobie erschdelln un ladn", "selectWorld.backupJoinSkipButton": "<PERSON>ch weeß, was ich mach!", "selectWorld.backupQuestion.customized": "Angebassde Weldn wärdn nisch mähr undorrschdüddsd", "selectWorld.backupQuestion.downgrade": "Das Herabschdufn eenorr Weld würd nisch undorrschdüddsd", "selectWorld.backupQuestion.experimental": "Weldn mid eggsberimendelln Optschonen sin ni undorrschdüddsd", "selectWorld.backupQuestion.snapshot": "Möschdes<PERSON> diese Weld wirglisch ladn?", "selectWorld.backupWarning.customized": "Bedauorrlischorrweese wärdn angebassde Weldn in diesorr Fährsion von Minecraft nisch undorrschdüddsd. De Weld kann weidorrhin geladn wärdn un alles bleibd so wie es war, aborr neu genorrierde Landschafd würd nisch mähr angebassd sein. Wir endschuldschen uns für de Unannehmlischkeedn!", "selectWorld.backupWarning.downgrade": "Diese Weld wurde zuläddsd in dorr Fährsion %s geschbield; du bisd uff Fährsion %s. De <PERSON> mid eenorr äldren Fährsion zu schbieln, kann zu Dadnbeschädschung führn – wir könn nisch garandiern, dass de Weld lädt odorr läufd. Wen<PERSON> de droddsdem fordfahrn möschdesd, erschdelle bidde ‘ne Sischorrheedsgobie!", "selectWorld.backupWarning.experimental": "Diese Weld vorrwended eggsberimendelle Eenschdellungn, die jedorrzeid nisch mehr fungtschooniern könn. Wir könn nisch garandiern, dass sie geladn würd odorr fungtschoonierd. Du bedriddsd unbeganndes Gelände!", "selectWorld.backupWarning.snapshot": "Diese Weld wurde zuläddsd mid dorr Fährsion %s geschbield; aggduell benuddsde die Fährsion %s. Bidde erschdelle eene <PERSON>, da de Weld beschädischd wärdn könnde!", "selectWorld.bonusItems": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.cheats": "Cheads", "selectWorld.commands": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.conversion": "Muss umgewandeld wärdn!", "selectWorld.conversion.tooltip": "<PERSON><PERSON> Weld muss in eenorr äldren Fährsion (wie 1.6.4) ge<PERSON><PERSON><PERSON> wärdn, um sischorr umgewandeld zu wärdn", "selectWorld.create": "Neue Weld erschdelln", "selectWorld.customizeType": "Anbassn", "selectWorld.dataPacks": "<PERSON><PERSON><PERSON>", "selectWorld.data_read": "Welddadn wärdn eingelesn …", "selectWorld.delete": "L<PERSON><PERSON><PERSON>", "selectWorld.deleteButton": "L<PERSON><PERSON><PERSON>", "selectWorld.deleteQuestion": "<PERSON><PERSON><PERSON>, dass de de Weld löschn möschdesd?", "selectWorld.deleteWarning": "‚%s‘ würd für immorr vorlorn sein! (<PERSON>e lange Zeid!)", "selectWorld.delete_failure": "Löschn dorr Weld fählgeschlachn", "selectWorld.edit": "<PERSON><PERSON><PERSON>", "selectWorld.edit.backup": "Sischorrungssgobie erschdelln", "selectWorld.edit.backupCreated": "Gesischorrd: %s", "selectWorld.edit.backupFailed": "Sischorrung fählgeschlachn", "selectWorld.edit.backupFolder": "Sischorrungsgobienordnorr <PERSON>", "selectWorld.edit.backupSize": "Greeße: %s MB", "selectWorld.edit.export_worldgen_settings": "Weldgenerierungseenschdellungn eggsbordiern", "selectWorld.edit.export_worldgen_settings.failure": "Eggsbord fählgeschlachn", "selectWorld.edit.export_worldgen_settings.success": "Eggsbordierd", "selectWorld.edit.openFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.optimize": "Weld obdimiern", "selectWorld.edit.resetIcon": "Symbol zurüggsäddsn", "selectWorld.edit.save": "Schbeischorrn", "selectWorld.edit.title": "<PERSON>ld bearbeidn", "selectWorld.enterName": "Name dorr Weld", "selectWorld.enterSeed": "Schdardwerd für den Weldgenerador", "selectWorld.experimental": "Eggsberimendell", "selectWorld.experimental.details": "Details", "selectWorld.experimental.details.entry": "Erfordorrlische eggsberimendelle Schbielelemende: %s", "selectWorld.experimental.details.title": "Anforderungen der experimentellen Spielelemente", "selectWorld.experimental.message": "Sei vorsichtig!\nDiese Konfiguration er<PERSON><PERSON>, die sich noch in der Entwicklung befinden. Deine Welt könnte abstürzen, kaputt<PERSON>hen oder mit zukünftigen Aktualisierungen nicht mehr funktionieren.", "selectWorld.experimental.title": "Uffbassn: Eggsberimendelle Schbielelemende", "selectWorld.experiments": "Experimente", "selectWorld.experiments.info": "Experimente sind mögliche neue Spielelemente. <PERSON><PERSON> <PERSON><PERSON>, da deine Welt beschädigt werden kann. Nach dem Erstellen der Welt können sie nicht mehr deaktiviert werden.", "selectWorld.futureworld.error.text": "<PERSON><PERSON> eenorr Weld aus eenorr neuorrn Fährsion is ä Fählorr uffgedrädn. Dies war von Anfang an ä risgandes Undorrfangn; le<PERSON><PERSON> hads nisch fungtschoonierd.", "selectWorld.futureworld.error.title": "N Fählorr is uffgedrädn!", "selectWorld.gameMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure.info": "Wie der Überlebensmodus, aber Blöcke können nicht platziert oder abgebaut werden.", "selectWorld.gameMode.adventure.line1": "<PERSON><PERSON> dorr <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> könn jedoch", "selectWorld.gameMode.adventure.line2": "nisch bladds<PERSON>d odorr z<PERSON><PERSON> wärdn", "selectWorld.gameMode.creative": "Greativ", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON><PERSON>, baue und erkunde ohne Grenzen. Du kannst fliegen, verfügst über unendlich viel Materialien und Monster können dir nichts anhaben.", "selectWorld.gameMode.creative.line1": "Unbegränz<PERSON> R<PERSON>sch<PERSON>, Flugmodus un", "selectWorld.gameMode.creative.line2": "sofordsches Zordebborn von <PERSON>lö<PERSON>n", "selectWorld.gameMode.hardcore": "Hardcore", "selectWorld.gameMode.hardcore.info": "Der Überlebensmodus, ges<PERSON>rt auf der Schwierigkeit „Schwer“. Du kannst dich nicht wiederbeleben, wenn du stirbst.", "selectWorld.gameMode.hardcore.line1": "Üborrlebnsmodus uff dorr heechsdn", "selectWorld.gameMode.hardcore.line2": "Schwierischkeed mid endgüldschem Dod", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "<PERSON><PERSON> gucken, nicht anfassen.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON> guggn, nisch anfassn", "selectWorld.gameMode.survival": "Üborrlebn", "selectWorld.gameMode.survival.info": "Erkunde eine geheimnisvolle Welt, in der du bauen, sammeln, Handwerk treiben und Monster bekämpfen kannst.", "selectWorld.gameMode.survival.line1": "Suche nach Ressourcen, baue Wergzeusche, sammle", "selectWorld.gameMode.survival.line2": "Erfahrung und gämbfe ums Üborrlebn", "selectWorld.gameRules": "Schbielreeschln", "selectWorld.import_worldgen_settings": "Eenschdellungn imbordiern", "selectWorld.import_worldgen_settings.failure": "<PERSON><PERSON><PERSON><PERSON><PERSON> beim <PERSON> dorr <PERSON>chdellungn", "selectWorld.import_worldgen_settings.select_file": "Eenschdellungsdadei auswähln (.json)", "selectWorld.incompatible.description": "Diese Welt kann in der aktuellen Version nicht geöffnet werden.\nSie wurde zuletzt in der Version %s gespielt.", "selectWorld.incompatible.info": "Inkompatible Version: %s", "selectWorld.incompatible.title": "Inkompatible Version", "selectWorld.incompatible.tooltip": "Diese Welt kann nicht ge<PERSON><PERSON><PERSON> werden, da sie mit einer inkompatiblen Version erstellt wurde.", "selectWorld.incompatible_series": "Dursch eene ingombadible Fährsion erschdelld", "selectWorld.load_folder_access": "<PERSON><PERSON><PERSON> Ordnorr, in dem de Weldn geschbeischorrd sin, kunnde nisch zugegriffn wärdn!", "selectWorld.loading_list": "<PERSON>ldn<PERSON><PERSON> würd geladn", "selectWorld.locked": "<PERSON> e<PERSON> andren loofendn Minecraft-Inschdans geschberrd", "selectWorld.mapFeatures": "<PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON> g<PERSON>", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON><PERSON>, Schiffswracks usw.", "selectWorld.mapType": "Weldtyp", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "<PERSON><PERSON><PERSON> …", "selectWorld.newWorld": "Neue Weld", "selectWorld.recreate": "<PERSON><PERSON>", "selectWorld.recreate.customized.text": "Angebassde Weldn wärdn in diesorr Fährsion von Minecraft nisch mähr undorrschdüddsd. <PERSON><PERSON> würd vor<PERSON>, de Weld noch ma mid demselbn Schdardwerd un denselbn Eischnschafdn zu erze<PERSON>n, aborr alle Landschafdsanbassungn gehn vorlorn. Wir endschuldschn uns für de Unannehmlischkeedn!", "selectWorld.recreate.customized.title": "Angebassde Weldn wärdn nisch mähr undorrschdüddsd", "selectWorld.recreate.error.text": "<PERSON><PERSON> is beim <PERSON>uch eene Weld wiedorr zu beschaffn schief geloofn.", "selectWorld.recreate.error.title": "N Fählorr is uffgedrädn!", "selectWorld.resource_load": "Ressourcen werden vorbereitet …", "selectWorld.resultFolder": "<PERSON><PERSON><PERSON> geschbeischorrd in:", "selectWorld.search": "nach Weldn suchn", "selectWorld.seedInfo": "<PERSON><PERSON> lassn für een zufällschn Schdardwerd", "selectWorld.select": "Ausgewählde Weld schbieln", "selectWorld.targetFolder": "Speicherordner: %s", "selectWorld.title": "Weld auswähln", "selectWorld.tooltip.fromNewerVersion1": "De We<PERSON> wurde in eener neuoren Fährsion geschbeischorrd,", "selectWorld.tooltip.fromNewerVersion2": "das Ladn diesorr Weld könnde Brobleme vorursachn!", "selectWorld.tooltip.snapshot1": "<PERSON><PERSON><PERSON><PERSON> ni, diese Weld zu sischorrn,", "selectWorld.tooltip.snapshot2": "bevorr du se in diesorr Fährsion öffnesd.", "selectWorld.unable_to_load": "Weldn konndn nisch geladn wärdn", "selectWorld.version": "Fährsion:", "selectWorld.versionJoinButton": "Droddsdem ladn", "selectWorld.versionQuestion": "Möschdes<PERSON> diese Weld wirglisch ladn?", "selectWorld.versionUnknown": "unbegannd", "selectWorld.versionWarning": "Diese Weld wurde zuleddsd in dorr Fährsion %s geschbield. Sie in diesorr Fährsion zu ladn, kann de Weld beschädschen!", "selectWorld.warning.deprecated.question": "Einige der verwendeten Spielelemente sind veraltet und werden in Zukunft nicht mehr funktionieren. Möchtest du fortfahren?", "selectWorld.warning.deprecated.title": "Uffbassn! Diese Eenschdellungn vorrwendn vorraldede Schbielelemende", "selectWorld.warning.experimental.question": "Diese Obdschoon sin exberimendell un könndn irschendwann ni mähr gehn. Willsde wirglisch weidermachn?", "selectWorld.warning.experimental.title": "Uffbassn! Diese Eenschdellungn vorrwendn eggsberimendelle Schbielelemende", "selectWorld.warning.lowDiskSpace.description": "Auf deinem Gerät ist nicht mehr viel freier Speicherplatz vorhanden.\nWenn der Speicherplatz während des Spiels aufgebraucht ist, kann dies dazu führen, dass deine Welt beschädigt wird.", "selectWorld.warning.lowDiskSpace.title": "Achtung! Wenig freier Speicherplatz!", "selectWorld.world": "Weld", "sign.edit": "Schüldbeschrifdung bearbeidn", "sleep.not_possible": "De Na<PERSON>d kann nisch durschgeschlafn wärdn", "sleep.players_sleeping": "%s/%s Schbielorrn schlafn", "sleep.skipping_night": "<PERSON>", "slot.only_single_allowed": "Nur einzelne Inventarplätze sind erlaubt, ‚%s‘ erhalten", "slot.unknown": "Unbeganndorr Invendarbladds ‚%s‘", "snbt.parser.empty_key": "<PERSON><PERSON><PERSON><PERSON> darf nicht leer sein", "snbt.parser.expected_binary_numeral": "Bin<PERSON><PERSON><PERSON> erwartet", "snbt.parser.expected_decimal_numeral": "Dezimalzahl erwartet", "snbt.parser.expected_float_type": "Gleitkommazahl erwartet", "snbt.parser.expected_hex_escape": "Zeichenkette der Länge %s erwartet", "snbt.parser.expected_hex_numeral": "Hexadez<PERSON><PERSON><PERSON><PERSON> erwart<PERSON>", "snbt.parser.expected_integer_type": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "snbt.parser.expected_non_negative_number": "Nichtnegative Zahl erwartet", "snbt.parser.expected_number_or_boolean": "Zahl oder Wahrheitswert erwartet", "snbt.parser.expected_string_uuid": "Zeichenkette erwartet, die eine gültige UUID darstellt", "snbt.parser.expected_unquoted_string": "Gültige Zeichenkette ohne Anführungszeichen erwartet", "snbt.parser.infinity_not_allowed": "Nicht endliche Zahlen sind nicht erlaubt", "snbt.parser.invalid_array_element_type": "<PERSON>g<PERSON><PERSON><PERSON> von <PERSON>", "snbt.parser.invalid_character_name": "Ungültiger Name von Unicode‐Zeichen", "snbt.parser.invalid_codepoint": "Ungültiger Wert für Unicode‐Zeichen: %s", "snbt.parser.invalid_string_contents": "Ungültiger Inhalt der Zeichenkette", "snbt.parser.invalid_unquoted_start": "Zeichenketten ohne Anführungszeichen dürfen nicht mit den Ziffern 0–9 oder mit den Zeichen + und − beginnen", "snbt.parser.leading_zero_not_allowed": "Dezimalzahlen dürfen nicht mit 0 beginnen", "snbt.parser.no_such_operation": "Eine Operation ‚%s‘ existiert nicht", "snbt.parser.number_parse_failure": "Zahl konnte nicht gelesen werden: %s", "snbt.parser.undescore_not_allowed": "Unterstriche sind an Anfang und Ende einer Zahl nicht erlaubt", "soundCategory.ambient": "Admosphäre/Umgebung", "soundCategory.block": "Blögge", "soundCategory.hostile": "<PERSON><PERSON><PERSON>", "soundCategory.master": "Gesamdlaudschdärge", "soundCategory.music": "<PERSON><PERSON><PERSON>", "soundCategory.neutral": "Friedlische Greadurn", "soundCategory.player": "Schbielorr", "soundCategory.record": "Musiggblögge", "soundCategory.ui": "Benutzeroberfläche", "soundCategory.voice": "Schdimme/Schbrache", "soundCategory.weather": "Wäddorr", "spectatorMenu.close": "<PERSON><PERSON> sch<PERSON>n", "spectatorMenu.next_page": "Näschsde Seide", "spectatorMenu.previous_page": "Vorhärische Seide", "spectatorMenu.root.prompt": "<PERSON><PERSON><PERSON><PERSON> eene <PERSON>, um een Be<PERSON>hl zu wähln, und drügge erne<PERSON>, um ihn auszuführn.", "spectatorMenu.team_teleport": "<PERSON><PERSON>", "spectatorMenu.team_teleport.prompt": "<PERSON><PERSON><PERSON><PERSON> das Team, zu dem de disch delebordiern möschdesd", "spectatorMenu.teleport": "<PERSON><PERSON>", "spectatorMenu.teleport.prompt": "<PERSON><PERSON><PERSON><PERSON> den Schbielorr, zu dem de disch delebordiern möschdesd", "stat.generalButton": "<PERSON><PERSON><PERSON><PERSON>", "stat.itemsButton": "Geschnschdände", "stat.minecraft.animals_bred": "<PERSON><PERSON>", "stat.minecraft.aviate_one_cm": "Schdregge mid Elydren geflogn", "stat.minecraft.bell_ring": "Glogge gel<PERSON>", "stat.minecraft.boat_one_cm": "Schdregge im Bood gefahrn", "stat.minecraft.clean_armor": "Rüsdungsde<PERSON> gew<PERSON>n", "stat.minecraft.clean_banner": "<PERSON><PERSON><PERSON> gew<PERSON>n", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> gew<PERSON>n", "stat.minecraft.climb_one_cm": "Sc<PERSON><PERSON><PERSON>e gegläddorrd", "stat.minecraft.crouch_one_cm": "Schdregge geschlischn", "stat.minecraft.damage_absorbed": "<PERSON><PERSON><PERSON> absorbierd", "stat.minecraft.damage_blocked_by_shield": "Schadn mid Schüld abgewährd", "stat.minecraft.damage_dealt": "Schadn zugefüschd", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON>n zugefüschd (absorbierd)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON> zugefüschd (widorschdandn)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON> w<PERSON>", "stat.minecraft.damage_taken": "<PERSON><PERSON><PERSON>", "stat.minecraft.deaths": "<PERSON><PERSON>", "stat.minecraft.drop": "Geschnschdände falln gelassn", "stat.minecraft.eat_cake_slice": "Guchnschdügge gegessn", "stat.minecraft.enchant_item": "Geschnschdände vorzaubord", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON> gefalln", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON>", "stat.minecraft.fish_caught": "Fische gefangn", "stat.minecraft.fly_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON> geflogn", "stat.minecraft.happy_ghast_one_cm": "Strecke auf glücklichem Ghast geflogen", "stat.minecraft.horse_one_cm": "<PERSON><PERSON><PERSON>gge uff <PERSON> geri<PERSON>n", "stat.minecraft.inspect_dispenser": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.inspect_dropper": "Schbendorr dursch<PERSON>d", "stat.minecraft.inspect_hopper": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_anvil": "<PERSON><PERSON><PERSON> ben<PERSON>d", "stat.minecraft.interact_with_beacon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_blast_furnace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_brewingstand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_campfire": "Gnägg<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_cartography_table": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_crafting_table": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON>", "stat.minecraft.interact_with_grindstone": "Sc<PERSON><PERSON>fschde<PERSON> benudd<PERSON>d", "stat.minecraft.interact_with_lectern": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_loom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_smithing_table": "Schmiededisch benuddsd", "stat.minecraft.interact_with_smoker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ben<PERSON>", "stat.minecraft.interact_with_stonecutter": "Schdeensäsche benuddsd", "stat.minecraft.jump": "Sc<PERSON>brünge", "stat.minecraft.leave_game": "Schbiel vorlassn", "stat.minecraft.minecart_one_cm": "Schdregge in Lore gefahrn", "stat.minecraft.mob_kills": "Greadurn erleschd", "stat.minecraft.open_barrel": "Fass geöffned", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON> g<PERSON>", "stat.minecraft.open_enderchest": "Enderdruhe geöffned", "stat.minecraft.open_shulker_box": "Shulker-Gissde geöffned", "stat.minecraft.pig_one_cm": "<PERSON><PERSON><PERSON><PERSON>e uff Schween geriddn", "stat.minecraft.play_noteblock": "Noodnblögge abgeschbield", "stat.minecraft.play_record": "Schallbladdn abgeschbield", "stat.minecraft.play_time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "stat.minecraft.pot_flower": "Pflanzn eingedobbd", "stat.minecraft.raid_trigger": "Üborrfälle ausgelösd", "stat.minecraft.raid_win": "Üborrfälle besieschd", "stat.minecraft.sleep_in_bed": "In eem Bädde geschlafn", "stat.minecraft.sneak_time": "Geschlischne Zeid", "stat.minecraft.sprint_one_cm": "Schdregge geschbrinded", "stat.minecraft.strider_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON> uff Schreidorr geriddn", "stat.minecraft.swim_one_cm": "Schdregge geschwomm", "stat.minecraft.talked_to_villager": "Mid Dorfbewohnorrn geschbrochn", "stat.minecraft.target_hit": "Zielblogg gedroffn", "stat.minecraft.time_since_death": "<PERSON><PERSON><PERSON> seid läddsdm Dod", "stat.minecraft.time_since_rest": "<PERSON><PERSON><PERSON> seid läddsdm Ausruhn", "stat.minecraft.total_world_time": "Zeid mid geöffnedorr Weld", "stat.minecraft.traded_with_villager": "Mid Dorfbewohnorrn gehandeld", "stat.minecraft.trigger_trapped_chest": "Redstone-<PERSON><PERSON><PERSON>d", "stat.minecraft.tune_noteblock": "Noodnblögge geschdimmd", "stat.minecraft.use_cauldron": "Wassorr aus Gessln geschöbfd", "stat.minecraft.walk_on_water_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON> uff <PERSON><PERSON>n", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON>n", "stat.minecraft.walk_under_water_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON> und<PERSON>r <PERSON><PERSON><PERSON>n", "stat.mobsButton": "Greadurn", "stat_type.minecraft.broken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.crafted": "Hergeschdelld", "stat_type.minecraft.dropped": "<PERSON>n gelassn", "stat_type.minecraft.killed": "Du hasd %s %s erleschd", "stat_type.minecraft.killed.none": "Du hasd %s nie erleschd", "stat_type.minecraft.killed_by": "%s had disch %s mal gedöded", "stat_type.minecraft.killed_by.none": "Du wurdesd nie von %s gedöded", "stat_type.minecraft.mined": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.picked_up": "Uffgesammeld", "stat_type.minecraft.used": "<PERSON><PERSON><PERSON><PERSON>", "stats.none": "X", "structure_block.button.detect_size": "<PERSON><PERSON><PERSON>", "structure_block.button.load": "<PERSON><PERSON>", "structure_block.button.save": "Schbeischorrn", "structure_block.custom_data": "Benuddsorrdefinierde NBT-Dadn", "structure_block.detect_size": "Konstruktionsgröße und ‐position erkennen:", "structure_block.hover.corner": "Egge: %s", "structure_block.hover.data": "Dadn: %s", "structure_block.hover.load": "Ladn: %s", "structure_block.hover.save": "Schbeischorrn: %s", "structure_block.include_entities": "Objeg<PERSON> e<PERSON>be<PERSON>hn:", "structure_block.integrity": "Vollschdändischkeed un Schdardwerd dorr Gonsdrugtschoon", "structure_block.integrity.integrity": "Gonsdrugtschoonsvollschdändischkeed", "structure_block.integrity.seed": "Gonsdrugtschoons-Schdardwerd", "structure_block.invalid_structure_name": "Ungüldschorr Gonsdrugtschoonsname ‚%s‘", "structure_block.load_not_found": "Gonsdrugtschoon ‚%s‘ nisch gefundn", "structure_block.load_prepare": "Bosits<PERSON><PERSON> von Gonsdrugts<PERSON>on ‚%s‘ vorbereided", "structure_block.load_success": "Gonsdrugtschoon ‚%s‘ geladn", "structure_block.mode.corner": "<PERSON><PERSON>", "structure_block.mode.data": "<PERSON><PERSON>", "structure_block.mode.load": "<PERSON><PERSON>", "structure_block.mode.save": "Schbeischorrn", "structure_block.mode_info.corner": "Egg‐Modus – Margierung von Bositschoon un Greeße", "structure_block.mode_info.data": "Dadn‐Modus – Margierung dorr Schbiellogig", "structure_block.mode_info.load": "Lade‐Modus – Aus Dadei ladn", "structure_block.mode_info.save": "Schbeischorr‐Modus – In Dadei schbeischorrn", "structure_block.position": "<PERSON><PERSON><PERSON>", "structure_block.position.x": "Reladive X-Bositschoon", "structure_block.position.y": "Reladive Y-Bositschoon", "structure_block.position.z": "Reladive Z-Bositschoon", "structure_block.save_failure": "Gonsdrugtschoon ‚%s‘ kunnde nisch geschbeischorrd wärdn", "structure_block.save_success": "Gonsdrugtschoon als ‚%s‘ geschbeischorrd", "structure_block.show_air": "Unsischdbare Blögge anzeischn:", "structure_block.show_boundingbox": "Begränzungsrahm anzeischn:", "structure_block.size": "Gonsdrugtschoonsgreeße", "structure_block.size.x": "Gonsdrugtschoonsgreeße (X)", "structure_block.size.y": "Gonsdrugtschoonsgreeße (Y)", "structure_block.size.z": "Gonsdrugtschoonsgreeße (Z)", "structure_block.size_failure": "Kann Gonsdrugtschoonsgreeße nisch erkenn - füsche Eggn mid gleischm Gonsdrugtschoonsnam hinzu", "structure_block.size_success": "G<PERSON>ße von ‚%s‘ erfolgreisch ergannd", "structure_block.strict": "Strikte Platzierung:", "structure_block.structure_name": "Gonsdrugtschoonsname", "subtitles.ambient.cave": "Gruslisches Geräusch", "subtitles.ambient.sound": "Unheimliches Geräusch", "subtitles.block.amethyst_block.chime": "Amethyst glingd", "subtitles.block.amethyst_block.resonate": "<PERSON><PERSON><PERSON><PERSON> schwingt mit", "subtitles.block.anvil.destroy": "Ambuss zordebbord", "subtitles.block.anvil.land": "Ambuss gelanded", "subtitles.block.anvil.use": "<PERSON><PERSON><PERSON> ben<PERSON>d", "subtitles.block.barrel.close": "Fass schließd", "subtitles.block.barrel.open": "Fass öffned", "subtitles.block.beacon.activate": "Le<PERSON><PERSON>d<PERSON><PERSON>rr aggdivierd", "subtitles.block.beacon.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> brummd", "subtitles.block.beacon.deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> de<PERSON>", "subtitles.block.beacon.power_select": "Leuschdfeuorrgrafd ausgewähld", "subtitles.block.beehive.drip": "Honisch droppd", "subtitles.block.beehive.enter": "Biene betridd Schdogg", "subtitles.block.beehive.exit": "Biene vorlässt Schdogg", "subtitles.block.beehive.shear": "<PERSON><PERSON><PERSON><PERSON><PERSON> schabd", "subtitles.block.beehive.work": "<PERSON>ien arbeidn", "subtitles.block.bell.resonate": "Glogge halld nach", "subtitles.block.bell.use": "Glogge lä<PERSON>", "subtitles.block.big_dripleaf.tilt_down": "Dr<PERSON><PERSON><PERSON>d gibbd herundorr", "subtitles.block.big_dripleaf.tilt_up": "Drobbbladd rischded sisch uff", "subtitles.block.blastfurnace.fire_crackle": "Schmelzofn gnisdord", "subtitles.block.brewing_stand.brew": "Brauschdand blubbord", "subtitles.block.bubble_column.bubble_pop": "Blasn bladdsn", "subtitles.block.bubble_column.upwards_ambient": "Blasn sch<PERSON>rudeln", "subtitles.block.bubble_column.upwards_inside": "Blasn schdrömen", "subtitles.block.bubble_column.whirlpool_ambient": "Blasn wirbeln", "subtitles.block.bubble_column.whirlpool_inside": "Blasn ziehn", "subtitles.block.button.click": "<PERSON><PERSON><PERSON> gliggd", "subtitles.block.cake.add_candle": "Guchn schwabbeld", "subtitles.block.campfire.crackle": "Gnäggorrschn gnisdord", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON>", "subtitles.block.candle.extinguish": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.close": "<PERSON><PERSON><PERSON> sch<PERSON>d", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.block.chest.open": "<PERSON><PERSON><PERSON>", "subtitles.block.chorus_flower.death": "Chorusb<PERSON><PERSON><PERSON> welgd", "subtitles.block.chorus_flower.grow": "Chorusblüde wächsd", "subtitles.block.comparator.click": "<PERSON><PERSON><PERSON><PERSON> gliggd", "subtitles.block.composter.empty": "Gombosch<PERSON><PERSON> geleerd", "subtitles.block.composter.fill": "Gomboschdorr befülld", "subtitles.block.composter.ready": "Gomboschdorr gombostiert", "subtitles.block.conduit.activate": "Aquis<PERSON>r aggdivierd", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON><PERSON> pulsierd", "subtitles.block.conduit.attack.target": "Aquis<PERSON><PERSON> greefd an", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.copper_bulb.turn_off": "Kupferleuchte schaltet aus", "subtitles.block.copper_bulb.turn_on": "Kupferle<PERSON>te schaltet ein", "subtitles.block.copper_trapdoor.close": "Falldier<PERSON> schließd", "subtitles.block.copper_trapdoor.open": "<PERSON><PERSON><PERSON> ö<PERSON>", "subtitles.block.crafter.craft": "<PERSON>rker werkelt", "subtitles.block.crafter.fail": "<PERSON><PERSON><PERSON> versagt", "subtitles.block.creaking_heart.hurt": "Knarzherz grummelt", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON><PERSON> Gerä<PERSON>", "subtitles.block.creaking_heart.spawn": "Knarzherz erwacht", "subtitles.block.deadbush.idle": "Zweige knistern", "subtitles.block.decorated_pot.insert": "Verzierter Krug befüllt", "subtitles.block.decorated_pot.insert_fail": "Verzierter Krug wackelt", "subtitles.block.decorated_pot.shatter": "Verzierter Krug zerbricht", "subtitles.block.dispenser.dispense": "Geschnschdand geworfn", "subtitles.block.dispenser.fail": "Werforr vorsagd", "subtitles.block.door.toggle": "<PERSON><PERSON> g<PERSON>", "subtitles.block.dried_ghast.ambient": "Trockene Geräusche", "subtitles.block.dried_ghast.ambient_water": "Ausgetrockneter Ghast nimmt Was<PERSON> auf", "subtitles.block.dried_ghast.place_in_water": "Ausgetrockneter Ghast quillt", "subtitles.block.dried_ghast.transition": "Ausgetrocknetem Ghast geht es besser", "subtitles.block.dry_grass.ambient": "Wind weht", "subtitles.block.enchantment_table.use": "Zaubor<PERSON><PERSON> benudd<PERSON>d", "subtitles.block.end_portal.spawn": "Endbordal öffned sisch", "subtitles.block.end_portal_frame.fill": "Enderooche eingesädzd", "subtitles.block.eyeblossom.close": "Augenblüte schließt", "subtitles.block.eyeblossom.idle": "Augenblüte <PERSON>", "subtitles.block.eyeblossom.open": "Augenblü<PERSON>", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON><PERSON><PERSON> gnarrd", "subtitles.block.fire.ambient": "<PERSON><PERSON><PERSON> g<PERSON>dord", "subtitles.block.fire.extinguish": "<PERSON><PERSON><PERSON> er<PERSON>", "subtitles.block.firefly_bush.idle": "Glühwürmchen sch<PERSON>rren", "subtitles.block.frogspawn.hatch": "Gaulquabbe schlübfd", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON> g<PERSON>dord", "subtitles.block.generic.break": "Blogg zorschdörd", "subtitles.block.generic.fall": "Etwas fällt auf einen Block", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.generic.hit": "Blogg brischd", "subtitles.block.generic.place": "Blogg bladdsierd", "subtitles.block.grindstone.use": "Sc<PERSON><PERSON>fschde<PERSON> benudd<PERSON>d", "subtitles.block.growing_plant.crop": "Pflanze geschduddsd", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> wackelt", "subtitles.block.honey_block.slide": "Honischblogg herundorrudschn", "subtitles.block.iron_trapdoor.close": "Falldier<PERSON> schließd", "subtitles.block.iron_trapdoor.open": "<PERSON><PERSON><PERSON> ö<PERSON>", "subtitles.block.lava.ambient": "Lava blubbord", "subtitles.block.lava.extinguish": "<PERSON><PERSON>", "subtitles.block.lever.click": "<PERSON><PERSON><PERSON> umgeleschd", "subtitles.block.note_block.note": "Noodnblogg schbield", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON><PERSON> Gerä<PERSON>", "subtitles.block.piston.move": "Golbn arbeided", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> drobbd", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON><PERSON> drobbd in Gessl", "subtitles.block.pointed_dripstone.drip_water": "<PERSON><PERSON><PERSON> drobbd", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON><PERSON><PERSON> drobbd in Gessl", "subtitles.block.pointed_dripstone.land": "Schdalagdid fälld rundorr", "subtitles.block.portal.ambient": "<PERSON><PERSON><PERSON> wa<PERSON>", "subtitles.block.portal.travel": "Bordalrauschn lässd nach", "subtitles.block.portal.trigger": "Bo<PERSON><PERSON><PERSON><PERSON>chn nimmd zu", "subtitles.block.pressure_plate.click": "Druggbladde gliggd", "subtitles.block.pumpkin.carve": "Sch<PERSON>re schnidzd", "subtitles.block.redstone_torch.burnout": "Faggl züschd", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON> wa<PERSON>", "subtitles.block.respawn_anchor.charge": "Seelnang<PERSON><PERSON> uffgeladn", "subtitles.block.respawn_anchor.deplete": "Wiedorreinschdiegsangorr vorbrauchd", "subtitles.block.respawn_anchor.set_spawn": "Seelnagorr säddsd Wiedorreinschdiegsbungd", "subtitles.block.sand.idle": "Sand weht", "subtitles.block.sand.wind": "Wind weht", "subtitles.block.sculk.charge": "Sculk brodeld", "subtitles.block.sculk.spread": "Sculk vorrbreided sisch", "subtitles.block.sculk_catalyst.bloom": "Sculk-Gadalysadorr erblühd", "subtitles.block.sculk_sensor.clicking": "Sculk-<PERSON><PERSON><PERSON> beginnd zu gliggn", "subtitles.block.sculk_sensor.clicking_stop": "Sculk-<PERSON><PERSON><PERSON> hörd uff zu gliggn", "subtitles.block.sculk_shrieker.shriek": "Sculk-Greischorr greischd", "subtitles.block.shulker_box.close": "Shulker-G<PERSON><PERSON> schließd", "subtitles.block.shulker_box.open": "Shulker-Gissde öffned", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> wackelt", "subtitles.block.smithing_table.use": "Schmiededisch benuddsd", "subtitles.block.smoker.smoke": "Räuschoroofn räuschord", "subtitles.block.sniffer_egg.crack": "Schnüffler‐<PERSON>i knackt", "subtitles.block.sniffer_egg.hatch": "Schnüfflerjunges schlüpft", "subtitles.block.sniffer_egg.plop": "Schnüffler legt Ei", "subtitles.block.sponge.absorb": "<PERSON><PERSON><PERSON> saugt auf", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON>", "subtitles.block.trapdoor.close": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.trapdoor.open": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON><PERSON> gnar<PERSON>", "subtitles.block.trial_spawner.about_to_spawn_item": "Unheilvoller Gegenstand bereitet vor", "subtitles.block.trial_spawner.ambient": "Prüfungs‐Spawner knistert", "subtitles.block.trial_spawner.ambient_charged": "Unheilvolles Geknister", "subtitles.block.trial_spawner.ambient_ominous": "Unheilvolles Geknister", "subtitles.block.trial_spawner.charge_activate": "Unheil umhüllt Prüfungs‐Spawner", "subtitles.block.trial_spawner.close_shutter": "Prüfungs‐Spawner schließt", "subtitles.block.trial_spawner.detect_player": "Prüfungs‐Spawner lädt auf", "subtitles.block.trial_spawner.eject_item": "Prüfungs‐Spawner wirft Beute aus", "subtitles.block.trial_spawner.ominous_activate": "Unheil umhüllt Prüfungs‐Spawner", "subtitles.block.trial_spawner.open_shutter": "Prüfungs‐Spawner öffnet", "subtitles.block.trial_spawner.spawn_item": "Unheilvoller Gegenstand fallen gelassen", "subtitles.block.trial_spawner.spawn_item_begin": "Unheilvoller Gegenstand erscheint", "subtitles.block.trial_spawner.spawn_mob": "Prüfungs‐Spawner erschafft Kreatur", "subtitles.block.tripwire.attach": "Schdolbordrahd geschbannd", "subtitles.block.tripwire.click": "Schdolbordrahd ausgelösd", "subtitles.block.tripwire.detach": "Schdolbordrahd endschbannd", "subtitles.block.vault.activate": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.ambient": "Tresor knistert", "subtitles.block.vault.close_shutter": "<PERSON><PERSON><PERSON> sch<PERSON>t", "subtitles.block.vault.deactivate": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.eject_item": "Tresor wirft Gegenstand aus", "subtitles.block.vault.insert_item": "Tresor entriegelt", "subtitles.block.vault.insert_item_fail": "Tresor ak<PERSON><PERSON><PERSON><PERSON> nicht", "subtitles.block.vault.open_shutter": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.reject_rewarded_player": "Tresor weist <PERSON><PERSON> ab", "subtitles.block.water.ambient": "<PERSON><PERSON><PERSON> fließd", "subtitles.block.wet_sponge.dries": "<PERSON><PERSON><PERSON> trocknet", "subtitles.chiseled_bookshelf.insert": "<PERSON>uch hineingestellt", "subtitles.chiseled_bookshelf.insert_enchanted": "Verzaubertes Buch hineingestellt", "subtitles.chiseled_bookshelf.take": "<PERSON>uch entnommen", "subtitles.chiseled_bookshelf.take_enchanted": "Verzaubertes Buch entnommen", "subtitles.enchant.thorns.hit": "<PERSON><PERSON><PERSON> schdeschn", "subtitles.entity.allay.ambient_with_item": "<PERSON><PERSON>sge<PERSON>d suchd", "subtitles.entity.allay.ambient_without_item": "<PERSON>lfsge<PERSON>d schmachded", "subtitles.entity.allay.death": "Hilfsgeisd schdürbd", "subtitles.entity.allay.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>n", "subtitles.entity.allay.item_given": "<PERSON><PERSON>sge<PERSON>d gluggsd", "subtitles.entity.allay.item_taken": "<PERSON>lf<PERSON><PERSON><PERSON>d hilfd", "subtitles.entity.allay.item_thrown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> schleudo<PERSON>d", "subtitles.entity.armadillo.ambient": "Gürteltier grunzt", "subtitles.entity.armadillo.brush": "Hornschild abgebürstet", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON> frisst", "subtitles.entity.armadillo.hurt": "Gürteltier nimmt <PERSON>en", "subtitles.entity.armadillo.hurt_reduced": "Gürteltier schützt sich", "subtitles.entity.armadillo.land": "Gürteltier landet", "subtitles.entity.armadillo.peek": "Gürteltier späht", "subtitles.entity.armadillo.roll": "Gürteltier rollt sich ein", "subtitles.entity.armadillo.scute_drop": "Gürteltier wirft Hornschild ab", "subtitles.entity.armadillo.unroll_finish": "G<PERSON>rteltier entrollt sich", "subtitles.entity.armadillo.unroll_start": "Gürteltier späht", "subtitles.entity.armor_stand.fall": "Rüsdungsschdändorr is gefalln", "subtitles.entity.arrow.hit": "Pfeil driffd", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>n", "subtitles.entity.arrow.shoot": "<PERSON><PERSON><PERSON> abgefeuorrd", "subtitles.entity.axolotl.attack": "<PERSON><PERSON><PERSON><PERSON> greifd an", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON>dl schdürbd", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.axolotl.idle_air": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.idle_water": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.splash": "Aggs<PERSON>dl blädschorrd", "subtitles.entity.axolotl.swim": "<PERSON><PERSON><PERSON><PERSON> schwimmd", "subtitles.entity.bat.ambient": "Flädor<PERSON><PERSON> greis<PERSON>d", "subtitles.entity.bat.death": "Flädor<PERSON><PERSON> s<PERSON>", "subtitles.entity.bat.hurt": "<PERSON><PERSON>ä<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.bat.takeoff": "Flädorrmaus flieschd los", "subtitles.entity.bee.ambient": "Biene summd", "subtitles.entity.bee.death": "Biene schdürbd", "subtitles.entity.bee.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.bee.loop": "Biene summd", "subtitles.entity.bee.loop_aggressive": "Biene summd vorärgorrd", "subtitles.entity.bee.pollinate": "Biene summd fröhlisch", "subtitles.entity.bee.sting": "Biene schdichd", "subtitles.entity.blaze.ambient": "<PERSON><PERSON><PERSON> admed", "subtitles.entity.blaze.burn": "Lohä gnisdord", "subtitles.entity.blaze.death": "<PERSON><PERSON><PERSON> schdürbd", "subtitles.entity.blaze.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.blaze.shoot": "<PERSON><PERSON><PERSON> schi<PERSON>d", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Sumpfskelett klappert", "subtitles.entity.bogged.death": "Sumpfskelett stirbt", "subtitles.entity.bogged.hurt": "Sumpfskelett nimmt Schaden", "subtitles.entity.breeze.charge": "<PERSON><PERSON><PERSON> lädt auf", "subtitles.entity.breeze.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.breeze.deflect": "<PERSON><PERSON><PERSON> wehrt ab", "subtitles.entity.breeze.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.idle_air": "<PERSON><PERSON><PERSON> fliegt", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON><PERSON> schwi<PERSON>t", "subtitles.entity.breeze.inhale": "<PERSON><PERSON><PERSON> holt Luft", "subtitles.entity.breeze.jump": "<PERSON><PERSON><PERSON> springt", "subtitles.entity.breeze.land": "<PERSON><PERSON><PERSON> landet", "subtitles.entity.breeze.shoot": "<PERSON><PERSON><PERSON> s<PERSON>t", "subtitles.entity.breeze.slide": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.whirl": "<PERSON><PERSON><PERSON> wirbelt", "subtitles.entity.breeze.wind_burst": "Windkugel platzt", "subtitles.entity.camel.ambient": "Dr<PERSON><PERSON> g<PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON> pre<PERSON>t", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON> erholt sich", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON> stirbt", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON> frisst", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.saddle": "Dr<PERSON><PERSON> g<PERSON>t", "subtitles.entity.camel.sit": "Dr<PERSON>dar legt sich hin", "subtitles.entity.camel.stand": "Dr<PERSON>dar erhebt sich", "subtitles.entity.camel.step": "Dr<PERSON><PERSON>", "subtitles.entity.camel.step_sand": "Dromedar läuf<PERSON> im Sand", "subtitles.entity.cat.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cat.beg_for_food": "Miedsekaddse beddld", "subtitles.entity.cat.death": "Miedsekaddse schdürbd", "subtitles.entity.cat.eat": "Miedsekaddse frissd", "subtitles.entity.cat.hiss": "Miedsekaddse fauchd", "subtitles.entity.cat.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.cat.purr": "<PERSON><PERSON><PERSON><PERSON><PERSON> schnurrd", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> gag<PERSON>", "subtitles.entity.chicken.death": "Hühnschn schdürbd", "subtitles.entity.chicken.egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> leschd Ei", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cod.flop": "<PERSON><PERSON><PERSON><PERSON><PERSON> bladschd", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cow.ambient": "Guh muhd", "subtitles.entity.cow.death": "<PERSON><PERSON> schdürbd", "subtitles.entity.cow.hurt": "<PERSON>uh nimmd <PERSON>", "subtitles.entity.cow.milk": "Guh würd gemolgn", "subtitles.entity.creaking.activate": "<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.ambient": "Knarz knarzt", "subtitles.entity.creaking.attack": "K<PERSON>z greift an", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON> beruhigt sich", "subtitles.entity.creaking.death": "Knarz zerbr<PERSON>lt", "subtitles.entity.creaking.freeze": "Knarz erstarrt", "subtitles.entity.creaking.spawn": "Knarz nimmt Gestalt an", "subtitles.entity.creaking.sway": "<PERSON><PERSON><PERSON> get<PERSON>n", "subtitles.entity.creaking.twitch": "<PERSON><PERSON><PERSON> zu<PERSON>t", "subtitles.entity.creaking.unfreeze": "<PERSON><PERSON><PERSON> rührt sich", "subtitles.entity.creeper.death": "C<PERSON>per schdürbd", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.creeper.primed": "Creeper züschd", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON>", "subtitles.entity.dolphin.ambient_water": "Delfin pfeifd", "subtitles.entity.dolphin.attack": "<PERSON><PERSON> greifd an", "subtitles.entity.dolphin.death": "<PERSON><PERSON> schdürbd", "subtitles.entity.dolphin.eat": "Delfin frissd", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.dolphin.jump": "Delfin schbringd", "subtitles.entity.dolphin.play": "Del<PERSON> schbield", "subtitles.entity.dolphin.splash": "<PERSON><PERSON> bladschd", "subtitles.entity.dolphin.swim": "Delfin schwimmd", "subtitles.entity.donkey.ambient": "Esl iahd", "subtitles.entity.donkey.angry": "<PERSON><PERSON><PERSON> wiehord", "subtitles.entity.donkey.chest": "<PERSON><PERSON><PERSON> bebaggd", "subtitles.entity.donkey.death": "Esl schdürbd", "subtitles.entity.donkey.eat": "Esl frissd", "subtitles.entity.donkey.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON> springt", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> gluggord", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON><PERSON><PERSON> gluggord", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> sch<PERSON>ü<PERSON>d", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON><PERSON><PERSON> würf<PERSON>", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>d", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON><PERSON><PERSON> schwimmd", "subtitles.entity.egg.throw": "<PERSON><PERSON>d", "subtitles.entity.elder_guardian.ambient": "G<PERSON>ßorr Wäschdorr schdöhnd", "subtitles.entity.elder_guardian.ambient_land": "Großorr Wäschdorr zabbeld", "subtitles.entity.elder_guardian.curse": "Großorr Wäschdorr vorrfluchd", "subtitles.entity.elder_guardian.death": "G<PERSON>ßorr Wäschdorr schdürbd", "subtitles.entity.elder_guardian.flop": "Großorr Wäschdorr bladschd", "subtitles.entity.elder_guardian.hurt": "G<PERSON>ßorr Wäschdorr nimmd Schadn", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON>", "subtitles.entity.ender_dragon.flap": "<PERSON><PERSON> fladdor<PERSON>", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON> fauchd", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.ender_eye.death": "Enderooche pladzd", "subtitles.entity.ender_eye.launch": "Enderooche geworfn", "subtitles.entity.ender_pearl.throw": "Enderbä<PERSON> flieschd", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> woobd", "subtitles.entity.enderman.death": "<PERSON><PERSON> schdürbd", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.enderman.scream": "Enderman kreischt", "subtitles.entity.enderman.stare": "<PERSON><PERSON> schreid", "subtitles.entity.enderman.teleport": "<PERSON><PERSON>", "subtitles.entity.endermite.ambient": "Endermite grabbeld", "subtitles.entity.endermite.death": "Endermite schdürbd", "subtitles.entity.endermite.hurt": "Endermite nimmd <PERSON>", "subtitles.entity.evoker.ambient": "<PERSON><PERSON><PERSON> murmeld", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON><PERSON>", "subtitles.entity.evoker.celebrate": "<PERSON><PERSON><PERSON> jubeld", "subtitles.entity.evoker.death": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.evoker.prepare_attack": "<PERSON><PERSON><PERSON> bereided Angriff vor", "subtitles.entity.evoker.prepare_summon": "Magiorr bereided Beschwörung vor", "subtitles.entity.evoker.prepare_wololo": "Magiorr bereided Zauborr vor", "subtitles.entity.evoker_fangs.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON> schnabbn zu", "subtitles.entity.experience_orb.pickup": "Erfahrung erhaldn", "subtitles.entity.firework_rocket.blast": "Feuorrwerg eggsblodierd", "subtitles.entity.firework_rocket.launch": "Feuorrwerg zünded", "subtitles.entity.firework_rocket.twinkle": "Feuorrwerg fungeld", "subtitles.entity.fish.swim": "Platschen", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.fishing_bobber.splash": "Schwimmorr bladschd", "subtitles.entity.fishing_bobber.throw": "Schwimmorr ausgeworfn", "subtitles.entity.fox.aggro": "<PERSON><PERSON> er<PERSON>", "subtitles.entity.fox.ambient": "<PERSON><PERSON> winseld", "subtitles.entity.fox.bite": "<PERSON><PERSON> beißd", "subtitles.entity.fox.death": "<PERSON><PERSON> sch<PERSON>rbd", "subtitles.entity.fox.eat": "<PERSON><PERSON> frissd", "subtitles.entity.fox.hurt": "<PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.fox.screech": "<PERSON><PERSON> greischd", "subtitles.entity.fox.sleep": "<PERSON><PERSON> schnarschd", "subtitles.entity.fox.sniff": "<PERSON><PERSON> schnubbord", "subtitles.entity.fox.spit": "<PERSON><PERSON> schbuggd aus", "subtitles.entity.fox.teleport": "<PERSON><PERSON>", "subtitles.entity.frog.ambient": "<PERSON><PERSON><PERSON> quagd", "subtitles.entity.frog.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.frog.eat": "<PERSON><PERSON><PERSON> frissd", "subtitles.entity.frog.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.frog.lay_spawn": "<PERSON><PERSON><PERSON>", "subtitles.entity.frog.long_jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.big_fall": "<PERSON><PERSON> is gefalln", "subtitles.entity.generic.burn": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.death": "Schderbn", "subtitles.entity.generic.drink": "Schlürfn", "subtitles.entity.generic.eat": "Essn", "subtitles.entity.generic.explode": "Eggsblosion", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON><PERSON> er<PERSON>", "subtitles.entity.generic.hurt": "<PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.generic.small_fall": "<PERSON><PERSON> schdolbord", "subtitles.entity.generic.splash": "Bladschn", "subtitles.entity.generic.swim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Windkugel platzt", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> ning<PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> s<PERSON><PERSON>", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> schi<PERSON>", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON><PERSON> gurrt", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON><PERSON> erscheint", "subtitles.entity.glow_item_frame.add_item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.break": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.place": "<PERSON><PERSON><PERSON><PERSON><PERSON> blad<PERSON><PERSON>d", "subtitles.entity.glow_item_frame.remove_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> geleerd", "subtitles.entity.glow_item_frame.rotate_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> gliggd", "subtitles.entity.glow_squid.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> schwimmd", "subtitles.entity.glow_squid.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> schdürbd", "subtitles.entity.glow_squid.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.glow_squid.squirt": "<PERSON><PERSON><PERSON>ddindnfisch vorrschbriddsd Dinde", "subtitles.entity.goat.ambient": "Ziesche blögd", "subtitles.entity.goat.death": "Ziesche schdürbd", "subtitles.entity.goat.eat": "Ziesche frissd", "subtitles.entity.goat.horn_break": "Zieschnhorn brüschd ab", "subtitles.entity.goat.hurt": "Ziesche nimmd <PERSON>", "subtitles.entity.goat.long_jump": "Ziesche schbringd", "subtitles.entity.goat.milk": "Ziesche würd gemolgn", "subtitles.entity.goat.prepare_ram": "Ziesche schdampfd", "subtitles.entity.goat.ram_impact": "Z<PERSON><PERSON>md", "subtitles.entity.goat.screaming.ambient": "Zies<PERSON> schre<PERSON>", "subtitles.entity.goat.step": "Ziesche läufd", "subtitles.entity.guardian.ambient": "Wäschdorr schdöhnd", "subtitles.entity.guardian.ambient_land": "Wäschdorr zabbeld", "subtitles.entity.guardian.attack": "<PERSON>äsch<PERSON><PERSON> schießd", "subtitles.entity.guardian.death": "Wäschdorr schdürbd", "subtitles.entity.guardian.flop": "Wäschdorr bladschd", "subtitles.entity.guardian.hurt": "<PERSON>äsch<PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.happy_ghast.ambient": "Glücklicher Ghast trällert", "subtitles.entity.happy_ghast.death": "Glücklicher G<PERSON> stirbt", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.harness_goggles_down": "Glücklicher Ghast ist flugbereit", "subtitles.entity.happy_ghast.harness_goggles_up": "Glücklicher Ghast hält an", "subtitles.entity.happy_ghast.hurt": "Glücklicher Ghast nimm<PERSON>en", "subtitles.entity.happy_ghast.unequip": "Geschirr abgenommen", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> g<PERSON>d", "subtitles.entity.hoglin.angry": "Hoglin grunzd vorärgorrd", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> greifd an", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> würd zombifizierd", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> we<PERSON>", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON> w<PERSON>d", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON> w<PERSON>d", "subtitles.entity.horse.armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>chd", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON> frissd", "subtitles.entity.horse.gallop": "<PERSON><PERSON><PERSON> galobbierd", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.saddle": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.husk.ambient": "Wüsdnzombie schdöhnd", "subtitles.entity.husk.converted_to_zombie": "Wüsdnzomb<PERSON> würd zu <PERSON>", "subtitles.entity.husk.death": "Wüsdnzombie schdürbd", "subtitles.entity.husk.hurt": "Wüsdnzombie nimmd Schadn", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON><PERSON> murmeld", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>bord", "subtitles.entity.illusioner.death": "Il<PERSON><PERSON><PERSON> schdürbd", "subtitles.entity.illusioner.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.illusioner.mirror_move": "Illusionisd brojizierd Dobblgängor", "subtitles.entity.illusioner.prepare_blindness": "Illusion<PERSON>d bereided Blindheed vor", "subtitles.entity.illusioner.prepare_mirror": "Illusionisd bereided Brojegtion vor", "subtitles.entity.iron_golem.attack": "Eisngolem greifd an", "subtitles.entity.iron_golem.damage": "Eisngolem würd besch<PERSON>d", "subtitles.entity.iron_golem.death": "Eisngolem schdürbd", "subtitles.entity.iron_golem.hurt": "Eisngolem nimmd <PERSON>", "subtitles.entity.iron_golem.repair": "Eisngolem reborierd", "subtitles.entity.item.break": "Geschnschdand zorbrischd", "subtitles.entity.item.pickup": "Geschnschdand uffgehobn", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON>", "subtitles.entity.item_frame.break": "<PERSON><PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON> blad<PERSON>d", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON>", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON> gliggd", "subtitles.entity.leash_knot.break": "<PERSON><PERSON>", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> an<PERSON>", "subtitles.entity.lightning_bolt.impact": "Bliddseenschlach", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON><PERSON> grolld", "subtitles.entity.llama.ambient": "<PERSON> b<PERSON>d", "subtitles.entity.llama.angry": "<PERSON> blö<PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON>d", "subtitles.entity.llama.chest": "<PERSON> be<PERSON>d", "subtitles.entity.llama.death": "<PERSON> s<PERSON>", "subtitles.entity.llama.eat": "<PERSON> frissd", "subtitles.entity.llama.hurt": "<PERSON> ni<PERSON><PERSON>", "subtitles.entity.llama.spit": "<PERSON> s<PERSON>d", "subtitles.entity.llama.step": "<PERSON>", "subtitles.entity.llama.swag": "<PERSON>", "subtitles.entity.magma_cube.death": "Magmawürfl schdürbd", "subtitles.entity.magma_cube.hurt": "Magmawürfl nimmd <PERSON>", "subtitles.entity.magma_cube.squish": "Magmawürfl schwabbeld", "subtitles.entity.minecart.inside": "<PERSON>re rattert", "subtitles.entity.minecart.inside_underwater": "<PERSON>re rattert unter Was<PERSON>", "subtitles.entity.minecart.riding": "Lore rolld", "subtitles.entity.mooshroom.convert": "Mooshroom vorrwandeld sisch", "subtitles.entity.mooshroom.eat": "Mooshroom frissd", "subtitles.entity.mooshroom.milk": "Mooshroom würd gemolgn", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom würd seldsam gemolkn", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON>d", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON> wiehord", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON> bebaggd", "subtitles.entity.mule.death": "<PERSON><PERSON><PERSON> sch<PERSON>ürbd", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON> frissd", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.mule.jump": "<PERSON><PERSON><PERSON> springt", "subtitles.entity.painting.break": "Gemälde endfernd", "subtitles.entity.painting.place": "Gemäldä bladdsierd", "subtitles.entity.panda.aggressive_ambient": "<PERSON>da grolld", "subtitles.entity.panda.ambient": "Panda h<PERSON>", "subtitles.entity.panda.bite": "Panda beißd", "subtitles.entity.panda.cant_breed": "Panda blögd", "subtitles.entity.panda.death": "Panda schdürbd", "subtitles.entity.panda.eat": "Panda frissd", "subtitles.entity.panda.hurt": "<PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.panda.pre_sneeze": "Panda vorrschb<PERSON>rd <PERSON>", "subtitles.entity.panda.sneeze": "<PERSON><PERSON> ni<PERSON>d", "subtitles.entity.panda.step": "Panda läufd", "subtitles.entity.panda.worried_ambient": "<PERSON>da wimmorrd", "subtitles.entity.parrot.ambient": "Baba<PERSON>i sch<PERSON>schd", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON> s<PERSON>ü<PERSON>", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON> frissd", "subtitles.entity.parrot.fly": "Bapagei fladdord", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON><PERSON> admed", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON><PERSON> schwi<PERSON>t", "subtitles.entity.parrot.imitate.creaking": "<PERSON><PERSON><PERSON> knarzt", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON>i <PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON> glug<PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON><PERSON> grabbeld", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON> murmeld", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON> gnurrd", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON><PERSON> murmeld", "subtitles.entity.parrot.imitate.magma_cube": "Babagei schwabbeld", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON><PERSON> schna<PERSON>", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON> schna<PERSON>", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON><PERSON> murmeld", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.silverfish": "<PERSON><PERSON>i <PERSON>", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON> gla<PERSON>", "subtitles.entity.parrot.imitate.slime": "Babagei schwabbeld", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON>i <PERSON>", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON><PERSON> gla<PERSON>", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON><PERSON> blagd", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON> murmeld", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON><PERSON> jam<PERSON>", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON>i <PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON><PERSON> gla<PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON>", "subtitles.entity.phantom.ambient": "Phandom greischd", "subtitles.entity.phantom.bite": "<PERSON><PERSON><PERSON> beißd", "subtitles.entity.phantom.death": "Phand<PERSON> schdürbd", "subtitles.entity.phantom.flap": "<PERSON><PERSON><PERSON> fladdorrd", "subtitles.entity.phantom.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.phantom.swoop": "<PERSON>and<PERSON> schdößd herab", "subtitles.entity.pig.ambient": "<PERSON>h<PERSON><PERSON> grunsd", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON><PERSON> schdürbd", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.pig.saddle": "Schween gesaddld", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> bewundorrd G<PERSON>chnschdand", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> schna<PERSON>d", "subtitles.entity.piglin.angry": "<PERSON>lin schnaubd vorärgorrd", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> feiord", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> würd zombifizierd", "subtitles.entity.piglin.death": "<PERSON><PERSON> sch<PERSON>ürbd", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> schnaubd neidsch", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> weischd <PERSON>", "subtitles.entity.piglin.step": "<PERSON><PERSON> l<PERSON>d", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON>-<PERSON><PERSON> schna<PERSON>d", "subtitles.entity.piglin_brute.angry": "Piglin-<PERSON><PERSON> schna<PERSON>d vorärgorrd", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin-<PERSON><PERSON> würd zombifizierd", "subtitles.entity.piglin_brute.death": "Piglin-<PERSON><PERSON> s<PERSON>", "subtitles.entity.piglin_brute.hurt": "Piglin-<PERSON><PERSON> nimm<PERSON>", "subtitles.entity.piglin_brute.step": "Piglin-<PERSON><PERSON> l<PERSON>", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> murmeld", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jubeld", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> schdürbd", "subtitles.entity.pillager.hurt": "<PERSON>lü<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.player.attack.crit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.knockback": "Rüggschdoßangriff", "subtitles.entity.player.attack.strong": "<PERSON>h<PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.sweep": "Schwungangriff", "subtitles.entity.player.attack.weak": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.burp": "R<PERSON><PERSON>bsorr", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> schdürbd", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> friorrd", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON><PERSON><PERSON> erdringd", "subtitles.entity.player.hurt_on_fire": "Schbielorr brennd", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON><PERSON><PERSON> bimmeld", "subtitles.entity.player.teleport": "Spieler teleportiert", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> gnurrd", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON> brummd", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>mm<PERSON>", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.potion.splash": "Bulle zorbrischd", "subtitles.entity.potion.throw": "<PERSON><PERSON> gewurfn", "subtitles.entity.puffer_fish.blow_out": "Guchlfisch schrumbfd", "subtitles.entity.puffer_fish.blow_up": "Guchlfisch bläsd sisch uff", "subtitles.entity.puffer_fish.death": "Guchlfisch schdürbd", "subtitles.entity.puffer_fish.flop": "Guchlfisch bladschd", "subtitles.entity.puffer_fish.hurt": "Guchlfisch nimmd Schadn", "subtitles.entity.puffer_fish.sting": "Guchlfisch schdüschd", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> fiebd", "subtitles.entity.rabbit.attack": "Garni<PERSON>l greifd an", "subtitles.entity.rabbit.death": "Garniggl schdürbd", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON><PERSON><PERSON> hobbeld", "subtitles.entity.ravager.ambient": "Vorwüsdorr grunzd", "subtitles.entity.ravager.attack": "Vorwüsdorr beißd", "subtitles.entity.ravager.celebrate": "Vorwüsdorr jubeld", "subtitles.entity.ravager.death": "Vorwüsdorr schdürbd", "subtitles.entity.ravager.hurt": "Vorwüsdorr nimmd <PERSON>", "subtitles.entity.ravager.roar": "Vorwüsdorr brülld", "subtitles.entity.ravager.step": "Vorwüsdorr läufd", "subtitles.entity.ravager.stunned": "Vorwüsdorr bedäubd", "subtitles.entity.salmon.death": "Lachs schdürbd", "subtitles.entity.salmon.flop": "Lachs pladschd", "subtitles.entity.salmon.hurt": "<PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.sheep.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> la<PERSON>d", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> sch<PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> schi<PERSON>d", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker_bullet.hit": "Shulkergeschoss eggsblodierd", "subtitles.entity.shulker_bullet.hurt": "Shulkergeschoss zorrfälld", "subtitles.entity.silverfish.ambient": "Silborfischschn züschd", "subtitles.entity.silverfish.death": "Silborfischschn schdürbd", "subtitles.entity.silverfish.hurt": "Silborfischschn nimmd Schadn", "subtitles.entity.skeleton.ambient": "<PERSON><PERSON><PERSON><PERSON> gla<PERSON>ord", "subtitles.entity.skeleton.converted_to_stray": "<PERSON>hgel<PERSON> v<PERSON>rwandeld sisch zu Eiswandororr", "subtitles.entity.skeleton.death": "Sc<PERSON>geledd schdürbd", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON><PERSON> nimm<PERSON>", "subtitles.entity.skeleton.shoot": "<PERSON><PERSON><PERSON><PERSON> schi<PERSON>d", "subtitles.entity.skeleton_horse.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>re<PERSON>", "subtitles.entity.skeleton_horse.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sch<PERSON>d", "subtitles.entity.skeleton_horse.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.skeleton_horse.jump_water": "Skelettpferd springt", "subtitles.entity.skeleton_horse.swim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> schwimmd", "subtitles.entity.slime.attack": "Schleim greifd an", "subtitles.entity.slime.death": "Schleim schdürbd", "subtitles.entity.slime.hurt": "Sc<PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.slime.squish": "Schleim schwabbeld", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> stirbt", "subtitles.entity.sniffer.digging": "Schnüffler wühlt", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON><PERSON><PERSON> erhebt sich", "subtitles.entity.sniffer.drop_seed": "Schnüffler lässt Samen fallen", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON> frisst", "subtitles.entity.sniffer.egg_crack": "Schnüffler‐<PERSON>i knackt", "subtitles.entity.sniffer.egg_hatch": "Schnüfflerjunges schlüpft", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON><PERSON><PERSON> freut sich", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmt <PERSON>", "subtitles.entity.sniffer.idle": "Schnüffler grunzt", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON><PERSON><PERSON> wittert", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON><PERSON><PERSON> sucht", "subtitles.entity.sniffer.sniffing": "Schnüffler schnüffelt", "subtitles.entity.sniffer.step": "Schnüffler läuft", "subtitles.entity.snow_golem.death": "Schneegolem schdürbd", "subtitles.entity.snow_golem.hurt": "Sc<PERSON><PERSON>gol<PERSON> nimmd <PERSON>", "subtitles.entity.snowball.throw": "Schneeball flieschd", "subtitles.entity.spider.ambient": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>schd", "subtitles.entity.spider.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.spider.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.squid.ambient": "Dindnfisch schwimmd", "subtitles.entity.squid.death": "Dindnfisch schdürbd", "subtitles.entity.squid.hurt": "Dindnfisch nimmd <PERSON>", "subtitles.entity.squid.squirt": "Dindnfisch vorrschbriddsd Dinde", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> glabbord", "subtitles.entity.stray.death": "Eiswan<PERSON><PERSON><PERSON> sch<PERSON>", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.strider.death": "Schreidorr schdürbd", "subtitles.entity.strider.eat": "Schreidorr frissd", "subtitles.entity.strider.happy": "<PERSON><PERSON><PERSON><PERSON><PERSON> dr<PERSON><PERSON><PERSON>", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.strider.idle": "Schreidorr zürbd", "subtitles.entity.strider.retreat": "Schreidor<PERSON> weischd <PERSON>", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON> schd<PERSON>d", "subtitles.entity.tadpole.flop": "Gaulquabbe bladschd", "subtitles.entity.tadpole.grow_up": "<PERSON><PERSON><PERSON><PERSON> wächst heran", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.tnt.primed": "TNT züschd", "subtitles.entity.tropical_fish.death": "Drobnfisch schdürbd", "subtitles.entity.tropical_fish.flop": "Drobnfisch zabbeld", "subtitles.entity.tropical_fish.hurt": "Drobn<PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.turtle.ambient_land": "Schüldgrö<PERSON> fiebd", "subtitles.entity.turtle.death": "Schüldgröde schdürbd", "subtitles.entity.turtle.death_baby": "Schüldgrödnjunges schdürbd", "subtitles.entity.turtle.egg_break": "Schüldgrödnei zorbrischd", "subtitles.entity.turtle.egg_crack": "Schüldgrödnei brischd", "subtitles.entity.turtle.egg_hatch": "Schüldgrödnei schlübbd", "subtitles.entity.turtle.hurt": "Schüldgrö<PERSON> nimmd <PERSON>", "subtitles.entity.turtle.hurt_baby": "Schüldgrödnjunges nimmd Schadn", "subtitles.entity.turtle.lay_egg": "Schüldgr<PERSON><PERSON> leschd Ei", "subtitles.entity.turtle.shamble": "Schüldgröde wadscheld", "subtitles.entity.turtle.shamble_baby": "Schüldgrödnjunges wadscheld", "subtitles.entity.turtle.swim": "Schüldgröde schwimmd", "subtitles.entity.vex.ambient": "Blagegeisd blagd", "subtitles.entity.vex.charge": "Blagege<PERSON><PERSON> schreid", "subtitles.entity.vex.death": "Blagegeisd schdürbd", "subtitles.entity.vex.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.villager.ambient": "Dorfbewohnorr murmeld", "subtitles.entity.villager.celebrate": "Dorfbewohnorr jubeld", "subtitles.entity.villager.death": "Dorfbewohnorr schdürbd", "subtitles.entity.villager.hurt": "Dorfbewohnorr nimmd <PERSON>", "subtitles.entity.villager.no": "Dorfbewohnorr lehnd ab", "subtitles.entity.villager.trade": "Dorfbewohnorr handeld", "subtitles.entity.villager.work_armorer": "Banzorrmachorr arbeided", "subtitles.entity.villager.work_butcher": "Fleesch<PERSON><PERSON> arbeided", "subtitles.entity.villager.work_cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON> arbei<PERSON>", "subtitles.entity.villager.work_cleric": "Geisdlischorr arbeided", "subtitles.entity.villager.work_farmer": "Bauorr arbeided", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON><PERSON><PERSON> arbeided", "subtitles.entity.villager.work_fletcher": "Pfeilmachorr arbeided", "subtitles.entity.villager.work_leatherworker": "Gärborr arbeided", "subtitles.entity.villager.work_librarian": "Bibliothegar arbeided", "subtitles.entity.villager.work_mason": "<PERSON><PERSON><PERSON> arbeided", "subtitles.entity.villager.work_shepherd": "Schäforr arbeided", "subtitles.entity.villager.work_toolsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> arbeided", "subtitles.entity.villager.work_weaponsmith": "Waffnschmied arbeided", "subtitles.entity.villager.yes": "Dorfbewohnorr schdimmd zu", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON> murmeld", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON><PERSON> jubeld", "subtitles.entity.vindicator.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.wandering_trader.ambient": "Fahrendorr Händlorr murmeld", "subtitles.entity.wandering_trader.death": "Fahrendorr Händlorr schdürbd", "subtitles.entity.wandering_trader.disappeared": "Fahrendorr Händlorr vorschwinded", "subtitles.entity.wandering_trader.drink_milk": "Fahrendorr Händlorr dringd <PERSON>", "subtitles.entity.wandering_trader.drink_potion": "Fahrendorr Händlorr dringd <PERSON>", "subtitles.entity.wandering_trader.hurt": "Fahrendorr Händlorr nimmd <PERSON>", "subtitles.entity.wandering_trader.no": "Fahrendorr Händlorr lehnd ab", "subtitles.entity.wandering_trader.reappeared": "Fahrendorr Händlorr <PERSON>", "subtitles.entity.wandering_trader.trade": "Fahrendorr Händlorr handeld", "subtitles.entity.wandering_trader.yes": "Fahrendorr Händlorr schdimmd zu", "subtitles.entity.warden.agitated": "<PERSON><PERSON><PERSON><PERSON> gnurrd wütnd", "subtitles.entity.warden.ambient": "<PERSON><PERSON><PERSON><PERSON> jam<PERSON>", "subtitles.entity.warden.angry": "<PERSON><PERSON><PERSON><PERSON> doobd", "subtitles.entity.warden.attack_impact": "<PERSON><PERSON><PERSON><PERSON> landed <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.warden.dig": "<PERSON><PERSON><PERSON><PERSON> gräbd", "subtitles.entity.warden.emerge": "<PERSON><PERSON><PERSON><PERSON> dauchd uff", "subtitles.entity.warden.heartbeat": "Wärdorrherds schläschd", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.warden.listening": "<PERSON><PERSON><PERSON><PERSON> horschd uff", "subtitles.entity.warden.listening_angry": "<PERSON><PERSON><PERSON><PERSON> horschd vorärgorrd uff", "subtitles.entity.warden.nearby_close": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> sisch", "subtitles.entity.warden.nearby_closer": "<PERSON><PERSON><PERSON><PERSON> rüggd vor", "subtitles.entity.warden.nearby_closest": "Wärdorr nahd", "subtitles.entity.warden.roar": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.warden.sniff": "<PERSON><PERSON><PERSON><PERSON> schnu<PERSON>", "subtitles.entity.warden.sonic_boom": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON><PERSON> läd uff", "subtitles.entity.warden.step": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.warden.tendril_clicks": "Wärdorrrangn gliggn", "subtitles.entity.wind_charge.throw": "<PERSON>kugel fliegt", "subtitles.entity.wind_charge.wind_burst": "Windkugel platzt", "subtitles.entity.witch.ambient": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.witch.celebrate": "<PERSON><PERSON><PERSON> jubeld", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON> sch<PERSON>d", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON> dringd", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON> w<PERSON>d", "subtitles.entity.wither.ambient": "<PERSON><PERSON>", "subtitles.entity.wither.death": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.wither.hurt": "<PERSON><PERSON> ni<PERSON>d <PERSON>", "subtitles.entity.wither.shoot": "Wither greifd an", "subtitles.entity.wither.spawn": "<PERSON><PERSON> end<PERSON>sseld", "subtitles.entity.wither_skeleton.ambient": "Witherschgeledd glabbord", "subtitles.entity.wither_skeleton.death": "Witherschgeledd schdürbd", "subtitles.entity.wither_skeleton.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.wolf.ambient": "<PERSON><PERSON>", "subtitles.entity.wolf.bark": "<PERSON> bellt", "subtitles.entity.wolf.death": "<PERSON><PERSON>", "subtitles.entity.wolf.growl": "<PERSON><PERSON> g<PERSON>d", "subtitles.entity.wolf.hurt": "<PERSON><PERSON>", "subtitles.entity.wolf.pant": "<PERSON> hechelt", "subtitles.entity.wolf.shake": "<PERSON><PERSON>", "subtitles.entity.wolf.whine": "<PERSON>", "subtitles.entity.zoglin.ambient": "Z<PERSON>lin grunzd", "subtitles.entity.zoglin.angry": "Zoglin grunzd vorärgorrd", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> greifd an", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> schdürbd", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> l<PERSON>d", "subtitles.entity.zombie.ambient": "<PERSON> schd<PERSON>hnd", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON> wü<PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.converted_to_drowned": "Zombie würd zu Erdrungnem", "subtitles.entity.zombie.death": "<PERSON> schdürbd", "subtitles.entity.zombie.destroy_egg": "Schüldgrödnei zordrädn", "subtitles.entity.zombie.hurt": "<PERSON> nimmd <PERSON>", "subtitles.entity.zombie.infect": "Zombie infizierd", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>re<PERSON>", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd <PERSON>", "subtitles.entity.zombie_villager.ambient": "Zombiedorfbewohnorr schdöhnd", "subtitles.entity.zombie_villager.converted": "Zombiedorfbewohnorr geheild", "subtitles.entity.zombie_villager.cure": "Zombiedorfbewohnorr züschd", "subtitles.entity.zombie_villager.death": "Zombiedorfbewohnorr schdürbd", "subtitles.entity.zombie_villager.hurt": "Zombiedorfbewohnorr nimmd <PERSON>", "subtitles.entity.zombified_piglin.ambient": "Zombifizierdorr Piglin grunzd", "subtitles.entity.zombified_piglin.angry": "Zombifizierdorr Piglin grunzd vorärgorrd", "subtitles.entity.zombified_piglin.death": "Zombifizierdorr <PERSON> schdürbd", "subtitles.entity.zombified_piglin.hurt": "Zombifizierdorr <PERSON> nimmd <PERSON>", "subtitles.event.mob_effect.bad_omen": "<PERSON><PERSON>il erg<PERSON>", "subtitles.event.mob_effect.raid_omen": "Überfall naht", "subtitles.event.mob_effect.trial_omen": "Unheilvolle Prüfung naht", "subtitles.event.raid.horn": "Unheilvolles Horn erschalld", "subtitles.item.armor.equip": "Ausrüsdung angeleschd", "subtitles.item.armor.equip_chain": "Keddnrüsdung rassld", "subtitles.item.armor.equip_diamond": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> glirrd", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON> rascheln", "subtitles.item.armor.equip_gold": "Goldrüsdung glimbord", "subtitles.item.armor.equip_iron": "Eisnrüsdung schäbbord", "subtitles.item.armor.equip_leather": "Lädorrüsdung gnürschd", "subtitles.item.armor.equip_netherite": "Nädderidrüsdung schäbbord", "subtitles.item.armor.equip_turtle": "Schüldgrödnbanzorr glabbord", "subtitles.item.armor.equip_wolf": "Wolfspanzer festgezurrt", "subtitles.item.armor.unequip_wolf": "Wolfspanzer abgestreift", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON> schabd", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON> schabd", "subtitles.item.axe.wax_off": "End<PERSON><PERSON><PERSON>", "subtitles.item.bone_meal.use": "Gnochnmehl riesld", "subtitles.item.book.page_turn": "<PERSON><PERSON> rascheld", "subtitles.item.book.put": "Schwahrde gladschd", "subtitles.item.bottle.empty": "<PERSON><PERSON> geleerd", "subtitles.item.bottle.fill": "<PERSON><PERSON>", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "Kiesabpinseln", "subtitles.item.brush.brushing.gravel.complete": "Kiesabpinseln abgeschlossen", "subtitles.item.brush.brushing.sand": "Sandabpinseln", "subtitles.item.brush.brushing.sand.complete": "Sandabpinseln abgeschlossen", "subtitles.item.bucket.empty": "<PERSON><PERSON><PERSON><PERSON> geleerd", "subtitles.item.bucket.fill": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "Aggsolodl uffgenomm", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON> e<PERSON>", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bundle.drop_contents": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bundle.insert": "Geschnschdand einge<PERSON>ggd", "subtitles.item.bundle.insert_fail": "Bündel voll", "subtitles.item.bundle.remove_one": "Geschnschdand ausgepaggd ", "subtitles.item.chorus_fruit.teleport": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.crop.plant": "Pflanze gepflanzd", "subtitles.item.crossbow.charge": "<PERSON><PERSON><PERSON><PERSON> spannd", "subtitles.item.crossbow.hit": "Pfeil driffd", "subtitles.item.crossbow.load": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.crossbow.shoot": "<PERSON><PERSON><PERSON><PERSON> schießd", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "subtitles.item.elytra.flying": "<PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "<PERSON><PERSON>rr<PERSON><PERSON><PERSON>sch<PERSON>", "subtitles.item.flintandsteel.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.glow_ink_sac.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>l gleggsd", "subtitles.item.goat_horn.play": "<PERSON><PERSON><PERSON> schbield", "subtitles.item.hoe.till": "Hagge pflügd", "subtitles.item.honey_bottle.drink": "Schluggn", "subtitles.item.honeycomb.wax_on": "Waggsn", "subtitles.item.horse_armor.unequip": "Rossharnisch abgestreift", "subtitles.item.ink_sac.use": "Dindn<PERSON><PERSON><PERSON> gleggsd", "subtitles.item.lead.break": "<PERSON><PERSON> re<PERSON>", "subtitles.item.lead.tied": "<PERSON><PERSON> an<PERSON>", "subtitles.item.lead.untied": "<PERSON><PERSON>", "subtitles.item.llama_carpet.unequip": "<PERSON><PERSON>ich abgestreift", "subtitles.item.lodestone_compass.lock": "Gombass uff Leedschdeen ausgerischded", "subtitles.item.mace.smash_air": "Streitkolben schmettert", "subtitles.item.mace.smash_ground": "Streitkolben schmettert", "subtitles.item.nether_wart.plant": "Pflanze gepflanzd", "subtitles.item.ominous_bottle.dispose": "Flasche zerbricht", "subtitles.item.saddle.unequip": "Sattel abgestreift", "subtitles.item.shears.shear": "<PERSON><PERSON><PERSON><PERSON><PERSON> sch<PERSON>", "subtitles.item.shears.snip": "<PERSON><PERSON> schne<PERSON>t", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON><PERSON> währd ab", "subtitles.item.shovel.flatten": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.item.spyglass.stop_using": "Färnrohr fährd een", "subtitles.item.spyglass.use": "Färnrohr fährd aus", "subtitles.item.totem.use": "Dodem ausgelösd", "subtitles.item.trident.hit": "Dr<PERSON>zagg schbießd uff", "subtitles.item.trident.hit_ground": "<PERSON><PERSON><PERSON><PERSON> vibrierd", "subtitles.item.trident.return": "<PERSON><PERSON><PERSON><PERSON> gehrd <PERSON>", "subtitles.item.trident.riptide": "Dreizagg ziehd", "subtitles.item.trident.throw": "<PERSON><PERSON><PERSON><PERSON> glirrd", "subtitles.item.trident.thunder": "Dr<PERSON>zagg<PERSON><PERSON><PERSON> grolld", "subtitles.item.wolf_armor.break": "Wolfspanzer zerbricht", "subtitles.item.wolf_armor.crack": "Wolfspanzer wird brüchig", "subtitles.item.wolf_armor.damage": "<PERSON><PERSON><PERSON> beschädigt", "subtitles.item.wolf_armor.repair": "Wolfspanzer repariert", "subtitles.particle.soul_escape": "<PERSON><PERSON> endweischd", "subtitles.ui.cartography_table.take_result": "Garde gezeischned", "subtitles.ui.hud.bubble_pop": "Luftvorrat nimmt ab", "subtitles.ui.loom.take_result": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.ui.stonecutter.take_result": "Schdeensäsche benuddsd", "subtitles.weather.rain": "<PERSON><PERSON><PERSON><PERSON>", "symlink_warning.message": "Welten aus Ordnern mit symbolischen Verknüpfungen zu laden, stellt ein Risi<PERSON>, wenn du dich nicht genau damit auskennst. Bitte besuche %s, um mehr zu erfahren.", "symlink_warning.message.pack": "Pakete mit symbolischen Verknüpfungen zu laden, stellt ein Risi<PERSON>, wenn du dich nicht genau damit auskennst. Bitte besuche %s, um mehr zu erfahren.", "symlink_warning.message.world": "Welten aus Ordnern mit symbolischen Verknüpfungen zu laden, stellt ein Risi<PERSON>, wenn du dich nicht genau damit auskennst. Bitte besuche %s, um mehr zu erfahren.", "symlink_warning.more_info": "Weitere Informationen", "symlink_warning.title": "Weltordner enthält symbolische Verknüpfungen", "symlink_warning.title.pack": "Hinzugefügte(s) Paket(e) enthält/enthalten symbolische Verknüpfungen", "symlink_warning.title.world": "Weltordner enthält symbolische Verknüpfungen", "team.collision.always": "<PERSON><PERSON><PERSON><PERSON>", "team.collision.never": "<PERSON><PERSON>", "team.collision.pushOtherTeams": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "team.collision.pushOwnTeam": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>ne Teammidgliedor", "team.notFound": "Unbeganndes Team ‚%s‘", "team.visibility.always": "<PERSON><PERSON><PERSON><PERSON>", "team.visibility.hideForOtherTeams": "Für andre Teams vorschdeggn", "team.visibility.hideForOwnTeam": "Für das eischne Team vorschdeggn", "team.visibility.never": "<PERSON><PERSON>", "telemetry.event.advancement_made.description": "<PERSON><PERSON> er<PERSON>, unter welchen Gegebenheiten Fortschritte erzielt werden, kann uns dabei helfen, den Spielverlauf besser zu verstehen und ihn zu verbessern.", "telemetry.event.advancement_made.title": "Fortschritt erzielt", "telemetry.event.game_load_times.description": "<PERSON><PERSON> kann uns helfen, her<PERSON><PERSON><PERSON><PERSON>, wo Verbesserungen des Startvorgangs notwendig sind, indem es die Ausführungszeiten der einzelnen Startphasen misst.", "telemetry.event.game_load_times.title": "Spielladezeiten", "telemetry.event.optional": "%s (optschonal)", "telemetry.event.optional.disabled": "%s (optional) – <PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.event.performance_metrics.description": "Die genaue Ermittlung des allgemeinen Leistungsprofils von Minecraft hilft uns, das Spiel an eine Vielzahl von Geräteeigenschaften und Betriebssystemen abzustimmen und dafür zu optimieren. \nDie Angabe der Spielversion hilft uns, das Leistungsprofil für neue Versionen von Minecraft zu vergleichen.", "telemetry.event.performance_metrics.title": "Leistungsmetriken", "telemetry.event.required": "%s (erfordorrlisch)", "telemetry.event.world_load_times.description": "<PERSON><PERSON> ist wich<PERSON>g, dass wir verstehen, wie lange es dauert, eine Welt zu betreten, und wie sich das im Lauf der Zeit ändert. Wenn wir beispielsweise neue Spielelemente hinzufügen oder umfassende technische Änderungen vornehmen, müssen wir wissen, wie sich das auf die Ladezeiten auswirkt.", "telemetry.event.world_load_times.title": "Weltladezeiten", "telemetry.event.world_loaded.description": "<PERSON><PERSON> <PERSON>, wie Spieler Minecraft spielen (z. B. <PERSON>lmodus, Spielversion und modifizierter Client bzw. Server), erlaubt es uns, Spielaktualisierungen auf die Bereiche auszurichten, die ihnen von Bedeutung sind.\nDas Ereignis „Welt geladen“ ist mit dem Ereignis „Welt entladen“ gekoppelt, um zu berechnen, wie lange die Spielsitzung gedauert hat.", "telemetry.event.world_loaded.title": "Welt geladen", "telemetry.event.world_unloaded.description": "Dieses <PERSON>reign<PERSON> ist mit dem Ereignis „Welt geladen“ gek<PERSON><PERSON><PERSON>, um zu berechnen, wie lange die Weltsitzung gedauert hat.\nWenn eine Weltsitzung beendet wurde (Verlassen der Welt bzw. Trennen der Verbindung zu einem Server), wird die Dauer (in Sekunden und Ticks) gemessen.", "telemetry.event.world_unloaded.title": "Welt entladen", "telemetry.property.advancement_game_time.title": "Spielzeit (Ticks)", "telemetry.property.advancement_id.title": "Fortschritts‐ID", "telemetry.property.client_id.title": "Client‐ID", "telemetry.property.client_modded.title": "Client modifiziert", "telemetry.property.dedicated_memory_kb.title": "Zugewiesener Arbeitsspeicher (KB)", "telemetry.property.event_timestamp_utc.title": "Zeitpunkt des Ereignisses (koordinierte Weltzeit)", "telemetry.property.frame_rate_samples.title": "Datenwerte zur Bildwiederholrate (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.property.game_version.title": "Schbielfährsion", "telemetry.property.launcher_name.title": "Launcher‐Name", "telemetry.property.load_time_bootstrap_ms.title": "Initialisierungszeit (Millisekunden)", "telemetry.property.load_time_loading_overlay_ms.title": "Zeit im Ladebildschirm (Millisekunden)", "telemetry.property.load_time_pre_window_ms.title": "<PERSON><PERSON>, bevor sich das Spielfenster öffnet (Millisekunden)", "telemetry.property.load_time_total_time_ms.title": "Gesamte Ladezeit (Millisekunden)", "telemetry.property.minecraft_session_id.title": "Minecraft‐Sitzungs‐ID", "telemetry.property.new_world.title": "Neue Weld", "telemetry.property.number_of_samples.title": "Anzahl der Datenwerte", "telemetry.property.operating_system.title": "Betriebssystem", "telemetry.property.opt_in.title": "Erforderliches/optionales <PERSON>", "telemetry.property.platform.title": "Plattform", "telemetry.property.realms_map_content.title": "Realms‐Weltinhalt (Name des Minispiels)", "telemetry.property.render_distance.title": "Sichtweite", "telemetry.property.render_time_samples.title": "Datenwerte zur Renderzeit", "telemetry.property.seconds_since_load.title": "Zeit seit dem Laden (Sekunden)", "telemetry.property.server_modded.title": "Server modifiziert", "telemetry.property.server_type.title": "Servertyp", "telemetry.property.ticks_since_load.title": "Zeit seit dem Laden (Ticks)", "telemetry.property.used_memory_samples.title": "Verwendeter Arbeitsspeicher", "telemetry.property.user_id.title": "Benutzer‐ID", "telemetry.property.world_load_time_ms.title": "Weltladezeit (Millisekunden)", "telemetry.property.world_session_id.title": "Welt‐Sitzungs‐ID", "telemetry_info.button.give_feedback": "Feedback geben", "telemetry_info.button.privacy_statement": "Datenschutzerklärung", "telemetry_info.button.show_data": "<PERSON><PERSON> e<PERSON>ehen", "telemetry_info.opt_in.description": "Ich stimme der Übermittlung optionaler Telemetriedaten zu", "telemetry_info.property_title": "Enthaltene Daten", "telemetry_info.screen.description": "Die Erhebung dieser Daten hilft uns, Minecraft zu verbessern, indem wir uns damit in die Richtungen weisen lassen, die unseren Spielern wichtig sind.\nDu kannst auch zusätzliches Feedback abgeben, damit wir Minecraft weiter verbessern können.", "telemetry_info.screen.title": "Telemetrie‐Datenerhebung", "test.error.block_property_mismatch": "Zustand %s wurde als %s erwartet, war aber %s", "test.error.block_property_missing": "Blockeigenschaft nicht vorhanden, Eigenschaft %s wurde als %s erwartet", "test.error.entity_property": "Objekt %s erfüllte Test nicht: %s", "test.error.entity_property_details": "Objekt %s hat Test %s nicht erfüllt: %s erwartet, war aber %s", "test.error.expected_block": "Block %s erwartet, war aber %s", "test.error.expected_block_tag": "Block wurde in #%s erwartet, war aber %s", "test.error.expected_container_contents": "<PERSON><PERSON><PERSON><PERSON> muss enthalten: %s", "test.error.expected_container_contents_single": "<PERSON><PERSON><PERSON><PERSON> muss ein %s enthalten", "test.error.expected_empty_container": "<PERSON><PERSON><PERSON><PERSON> muss leer sein", "test.error.expected_entity": "%s erwartet", "test.error.expected_entity_around": "%s wurde bei %s, %s, %s erwartet", "test.error.expected_entity_count": "%s Objekte vom Typ %s erwartet, %s gefunden", "test.error.expected_entity_data": "Objektdaten %s wurden erwartet, waren aber %s", "test.error.expected_entity_data_predicate": "Objektdaten für %s stimmen nicht überein", "test.error.expected_entity_effect": "Effekt für %s wurde als %s %s erwartet", "test.error.expected_entity_having": "Objektinventar hätte %s enthalten sollen", "test.error.expected_entity_holding": "Objekt hätte %s halten sollen", "test.error.expected_entity_in_test": "Erwartete %s im Test", "test.error.expected_entity_not_touching": "%s wurde nicht als an %s, %s, %s angrenzend erwartet (relativ: %s, %s, %s)", "test.error.expected_entity_touching": "%s wurde als an %s, %s, %s angrenzend erwartet (relativ: %s, %s, %s)", "test.error.expected_item": "Gegenstand vom Typ %s erwartet", "test.error.expected_items_count": "%s Gegenstände vom Typ %s erwartet, aber %s gefunden", "test.error.fail": "Fehlkriterien erfüllt", "test.error.invalid_block_type": "Unerwarteter Blocktyp gefunden: %s", "test.error.missing_block_entity": "Fehlendes Blockobjekt", "test.error.position": "%s bei %s, %s, %s (relativ: %s, %s, %s) in Tick %s", "test.error.sequence.condition_already_triggered": "Bedingung bereits in Tick %s ausgelöst", "test.error.sequence.condition_not_triggered": "Bedingung nicht ausgelöst", "test.error.sequence.invalid_tick": "In ungültigem Tick stattgefunden: %s wurde erwartet", "test.error.sequence.not_completed": "Test vor Abschluss des Vorgangs abgelaufen", "test.error.set_biome": "Biom für Test konnte nicht festgelegt werden", "test.error.spawn_failure": "Objekt %s konnte nicht erzeugt werden", "test.error.state_not_equal": "Falscher Zustand. %s erwartet, war %s", "test.error.structure.failure": "Testkonstruktion für %s konnte nicht platziert werden", "test.error.tick": "%s in Tick %s", "test.error.ticking_without_structure": "Testausführung begann, bevor Konstruktion platziert wurde", "test.error.timeout.no_result": "Weder Erfolg noch Fehlschlag innerhalb von %s Ticks", "test.error.timeout.no_sequences_finished": "<PERSON><PERSON> Testabläufe innerhalb von %s Ticks abgeschlossen", "test.error.too_many_entities": "Nur ein %s um %s, %s, %s erwartet, aber %s gefunden", "test.error.unexpected_block": "Block wurde nicht als %s erwartet", "test.error.unexpected_entity": "<PERSON><PERSON> erwartet, %s vorzufinden", "test.error.unexpected_item": "Gegenstand vom Typ %s wurde nicht erwartet", "test.error.unknown": "Unbekannter interner <PERSON><PERSON>: %s", "test.error.value_not_equal": "%s wurde als %s erwartet, war aber %s", "test.error.wrong_block_entity": "Falscher Blockobjekttyp: %s", "test_block.error.missing": "Testkonstruktion hat keinen %s‐Block", "test_block.error.too_many": "Zu viele %s‐Blöcke", "test_block.invalid_timeout": "Ungültiges Timeout (%s) – muss eine positive <PERSON><PERSON><PERSON> von <PERSON> sein", "test_block.message": "Nachricht:", "test_block.mode.accept": "Akzeptieren", "test_block.mode.fail": "Fehlschlag", "test_block.mode.log": "Protokoll", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Akzeptanzmodus – <PERSON>mmt (teilweisen) Erfolg eines Tests entgegen", "test_block.mode_info.fail": "Fehlermodus – Lässt den Test fehlschlagen", "test_block.mode_info.log": "Protokollmodus – Protokolliere eine Nachricht", "test_block.mode_info.start": "Startmodus – Der Anfangspunkt eines Tests", "test_instance.action.reset": "Zurücksetzen und laden", "test_instance.action.run": "Laden und ausführen", "test_instance.action.save": "Konstruktion speichern", "test_instance.description.batch": "Teil: %s", "test_instance.description.failed": "Fehlgeschlagen: %s", "test_instance.description.function": "Funktion: %s", "test_instance.description.invalid_id": "Ungültige Test‐ID", "test_instance.description.no_test": "Dieser Test existiert nicht", "test_instance.description.structure": "Konstruktion: %s", "test_instance.description.type": "Typ: %s", "test_instance.type.block_based": "Blockbasierter Test", "test_instance.type.function": "Integrierter Funktionstest", "test_instance_block.entities": "Objekte:", "test_instance_block.error.no_test": "Testinstanz bei %s, %s, %s kann nicht ausgeführt werden, da ihr kein Test zugewiesen ist", "test_instance_block.error.no_test_structure": "Testinstanz bei %s, %s, %s konnte nicht ausgeführt werden, da ihre Testkonstruktion fehlt", "test_instance_block.error.unable_to_save": "Testkonstruktionsvorlage für Testinstanz bei %s, %s, %s kann nicht gespeichert werden", "test_instance_block.invalid": "[ung<PERSON><PERSON><PERSON>]", "test_instance_block.reset_success": "Test %s wurde erfolgreich zurückgesetzt", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Größe der Testkonstruktion", "test_instance_block.starting": "Test %s wird gestartet", "test_instance_block.test_id": "Testinstanz‐ID", "title.32bit.deprecation": "32-Bit-Sysdem ergannd: Dies kann disch daran hindorrn, in Zugunfd zu schbieln, da ee 64-Bit-Sysdem erfordorrlisch is!", "title.32bit.deprecation.realms": "Minecraft würd bald ä 64-Bit-<PERSON><PERSON><PERSON><PERSON>, w<PERSON><PERSON><PERSON> de Realms uff diesm Geräd nisch mähr schbieln odorr vorrwendn kannsd. Du mussd jedes Realms-Abonnement manuell kündischn.", "title.32bit.deprecation.realms.check": "Diesn Hinwees nisch mehr anzeischn", "title.32bit.deprecation.realms.header": "32‐Bit‐Sysdem ergannd", "title.credits": "Copyright Mojang AB. Nicht verbreiten!", "title.multiplayer.disabled": "<PERSON><PERSON>lorrmodus is deaggdivierd, bidde üborrprüfe de Eenschdellungn deines Microsoft-Gondos.", "title.multiplayer.disabled.banned.name": "Du musst deinen Profilnamen ändern, bevor du online spielen kannst", "title.multiplayer.disabled.banned.permanent": "<PERSON><PERSON> is dauorrhafd vom Online-Schbiel ausgeschlossn", "title.multiplayer.disabled.banned.temporary": "<PERSON><PERSON> is vorr<PERSON><PERSON>rgehend vom Online-Schbiel ausgeschlossn", "title.multiplayer.lan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "Mährschbielorr (Driddanbiedorr-Sörvorr)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r (Realms)", "title.singleplayer": "Eenslschbielorr", "translation.test.args": "%s %s", "translation.test.complex": "Prefix, %s%2$s again %s and %1$s lastly %s and also %1$s again!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hi %", "translation.test.invalid2": "hi %s", "translation.test.none": "Hello, world!", "translation.test.world": "world", "trim_material.minecraft.amethyst": "Amethyst", "trim_material.minecraft.copper": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.diamond": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.emerald": "Smaragd", "trim_material.minecraft.gold": "Gold", "trim_material.minecraft.iron": "Eisen", "trim_material.minecraft.lapis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.netherite": "Netherit", "trim_material.minecraft.quartz": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.redstone": "Redstone", "trim_material.minecraft.resin": "<PERSON><PERSON>", "trim_pattern.minecraft.bolt": "Bolzen-Rüstungsbesatz", "trim_pattern.minecraft.coast": "Küsten-Rüstungsbesatz", "trim_pattern.minecraft.dune": "Dünen-Rüstungsbesatz", "trim_pattern.minecraft.eye": "Augen-Rüstungsbesatz", "trim_pattern.minecraft.flow": "Fluss-Rüstungsbesatz", "trim_pattern.minecraft.host": "Gastwirts-Rüstungsbesatz", "trim_pattern.minecraft.raiser": "Aufzieher-Rüstungsbesatz", "trim_pattern.minecraft.rib": "Rippen-Rüstungsbesatz", "trim_pattern.minecraft.sentry": "Wachen-Rüstungsbesatz", "trim_pattern.minecraft.shaper": "Gestalter-Rüstungsbesatz", "trim_pattern.minecraft.silence": "Stille-Rüstungsbesatz", "trim_pattern.minecraft.snout": "Schnauzen-Rüstungsbesatz", "trim_pattern.minecraft.spire": "Turmspitzen-Rüstungsbesatz", "trim_pattern.minecraft.tide": "Gezeiten-Rüstungsbesatz", "trim_pattern.minecraft.vex": "Plagegeister-Rüstungsbesatz", "trim_pattern.minecraft.ward": "Warthof-Rüstungsbesatz", "trim_pattern.minecraft.wayfinder": "Wegfinder-Rüstungsbesatz", "trim_pattern.minecraft.wild": "Wildnis-Rüstungsbesatz", "tutorial.bundleInsert.description": "Reschdsgligg, um Geschnschdände hinzuzufüschn", "tutorial.bundleInsert.title": "Vorrwende een <PERSON>ü<PERSON>l", "tutorial.craft_planks.description": "<PERSON> Rezebdschwahrde hilfd dir", "tutorial.craft_planks.title": "<PERSON><PERSON><PERSON><PERSON> her", "tutorial.find_tree.description": "Schlache ihn für Hulz", "tutorial.find_tree.title": "<PERSON><PERSON> e<PERSON>", "tutorial.look.description": "Bewesche die Maus", "tutorial.look.title": "<PERSON><PERSON> disch um", "tutorial.move.description": "Schbringe mid %s", "tutorial.move.title": "Loofe mid %s, %s, %s un %s", "tutorial.open_inventory.description": "Drügge %s", "tutorial.open_inventory.title": "Mach dei Invendar uff", "tutorial.punch_tree.description": "Halde %s fesd", "tutorial.punch_tree.title": "<PERSON><PERSON><PERSON>", "tutorial.socialInteractions.description": "Drügge %s zum Öffnen", "tutorial.socialInteractions.title": "Soodsschale Indoraggtschonen", "upgrade.minecraft.netherite_upgrade": "Netheritaufwertung"}