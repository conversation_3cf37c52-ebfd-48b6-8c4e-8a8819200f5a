{"accessibility.onboarding.accessibility.button": "Agordi pri Aliirebleso...", "accessibility.onboarding.screen.narrator": "<PERSON><PERSON> '<PERSON><PERSON>' por ebligar la rakontanto", "accessibility.onboarding.screen.title": "Bonveno al Minecraft!\n\nVolus vu ebligar la Rakontanto o vizitar la Agordi pri Alireblo?", "addServer.add": "Finitaĵo", "addServer.enterIp": "Adreso di servero", "addServer.enterName": "Nomo di servero", "addServer.resourcePack": "Havajari di servero", "addServer.resourcePack.disabled": "Deshabili<PERSON>", "addServer.resourcePack.enabled": "Habilitata", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON>", "addServer.title": "Modifikar informeso de servero", "advMode.command": "<PERSON><PERSON><PERSON>", "advMode.mode": "Maniero", "advMode.mode.auto": "Iterar", "advMode.mode.autoexec.bat": "Sempre aktiva", "advMode.mode.conditional": "Kondicionala", "advMode.mode.redstone": "Impulsar", "advMode.mode.redstoneTriggered": "Bezonas redstoneo", "advMode.mode.sequence": "Ĉeno", "advMode.mode.unconditional": "Nekondicionala", "advMode.notAllowed": "Vu mustas esar operacanto en modo kreiva", "advMode.notEnabled": "Bloki di komandi ne esas habilitata en ica servero", "advMode.previousOutput": "<PERSON><PERSON><PERSON> rezulto", "advMode.setCommand": "Definar komando por bloko", "advMode.setCommand.success": "Komando definesis: %s", "advMode.trackOutput": "Sporo eligo", "advMode.triggering": "Aktiviganta", "advMode.type": "Tipo", "advancement.advancementNotFound": "Avanco nekonocata: %s", "advancements.adventure.adventuring_time.description": "<PERSON><PERSON><PERSON> singla biomi", "advancements.adventure.adventuring_time.title": "Tempo por aventuro", "advancements.adventure.arbalistic.description": "Ocidar kin kreaji unika kun un pafo di arbalesto", "advancements.adventure.arbalistic.title": "Arbalestika", "advancements.adventure.avoid_vibration.description": "Ŝteliri apude skulk-sensoron aŭ gardiston por preventi ke ĝi detektu vin", "advancements.adventure.avoid_vibration.title": "Repto 100", "advancements.adventure.blowback.description": "Kill a Breeze with a deflected Breeze-shot <PERSON> Charge", "advancements.adventure.blowback.title": "Kiel vento blovis", "advancements.adventure.brush_armadillo.description": "Get Armadillo Scutes from an Armadillo using a Brush", "advancements.adventure.brush_armadillo.title": "Isn't It Scute?", "advancements.adventure.bullseye.description": "Frapu la centro de la celbloko de almenaŭ 30 metroj for", "advancements.adventure.bullseye.title": "Centraĵo", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Faru dekoritan poton el 4 argilaj skerboj", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Kuraĝa Restaŭro", "advancements.adventure.crafters_crafting_crafters.description": "<PERSON><PERSON>u apud Ĉrafter kiam li kreoj Ĉrafter", "advancements.adventure.crafters_crafting_crafters.title": "Ĉrafterj Ĉrafting Ĉrafterj", "advancements.adventure.fall_from_world_height.description": "Libera salto de la supro de la mondo (konstrua limo) ĝis la fundo de la mondo kaj restadi vivanta", "advancements.adventure.fall_from_world_height.title": "Kavernoj & Krutajxoj", "advancements.adventure.heart_transplanter.description": "Place a Creaking Heart with the correct alignment between two Pale Oak Log blocks", "advancements.adventure.heart_transplanter.title": "Heart Transplanter", "advancements.adventure.hero_of_the_village.description": "Defensar sucesante vilajo de invado", "advancements.adventure.hero_of_the_village.title": "Vilaĝheroo", "advancements.adventure.honey_block_slide.description": "Saltex aden bloko di mielo por kusenizar vua falado", "advancements.adventure.honey_block_slide.title": "Situesto necerta", "advancements.adventure.kill_a_mob.description": "<PERSON><PERSON>ar i<PERSON> monstro sovaja", "advancements.adventure.kill_a_mob.title": "Chasanto di monstro", "advancements.adventure.kill_all_mobs.description": "Ocidar un de singla monstri sovaja", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON> chasita", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "<PERSON><PERSON><PERSON><PERSON> beston apude skulk-katalisto", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Ĝi plimultiĝas", "advancements.adventure.lighten_up.description": "Skrapu la oksidigon de kupra lampo per hakilo por plibrightigi ĝin", "advancements.adventure.lighten_up.title": "<PERSON><PERSON> pli lumo", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Protektar vilaĝano de nedezirosa elektra ŝoko sen ekfajrigi", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Ŝtormoprotilekto", "advancements.adventure.minecraft_trials_edition.description": "Vizitu la Kastelejon de Provado", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON> por <PERSON>(j)", "advancements.adventure.ol_betsy.description": "Pa<PERSON> per arbalesto", "advancements.adventure.ol_betsy.title": "Destensilfingro pruritas", "advancements.adventure.overoverkill.description": "Ka<PERSON><PERSON> pli ol 50 koroj da damaĝo per unu frapo de martelo", "advancements.adventure.overoverkill.title": "Mi - neevitebla morto", "advancements.adventure.play_jukebox_in_meadows.description": "<PERSON><PERSON><PERSON>ej<PERSON>jn per muziko el sonosendo", "advancements.adventure.play_jukebox_in_meadows.title": "La sono de muziko", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Legi la energiasignalon de klingita librokasedo per komparanto", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "La Povo de Libroj", "advancements.adventure.revaulting.description": "Unlock an Ominous Vault with an Ominous Trial Key", "advancements.adventure.revaulting.title": "Revaulting", "advancements.adventure.root.description": "Aventuro, exploro e kombato", "advancements.adventure.root.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.salvage_sherd.description": "Gliti suspektan blokon por akiri potseramikon", "advancements.adventure.salvage_sherd.title": "Respektante la restaĵojn", "advancements.adventure.shoot_arrow.description": "Pafi ion per sago", "advancements.adventure.shoot_arrow.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.sleep_in_bed.description": "Dormi en lito por ŝanĝi vian reaperpunkton", "advancements.adventure.sleep_in_bed.title": "Facez dolca sonji", "advancements.adventure.sniper_duel.description": "Mortigar skeleto d'adminime 50 metri for", "advancements.adventure.sniper_duel.title": "<PERSON><PERSON> tiralieri", "advancements.adventure.spyglass_at_dragon.description": "Rigardi la Ender Drako per spiono", "advancements.adventure.spyglass_at_dragon.title": "Ĉu Ĝi estas Flugilo?", "advancements.adventure.spyglass_at_ghast.description": "Rigardi gasmon per spiono", "advancements.adventure.spyglass_at_ghast.title": "Ka ol esas balono?", "advancements.adventure.spyglass_at_parrot.description": "Rigardi papagon per spiono", "advancements.adventure.spyglass_at_parrot.title": "Ka ol esas Ucelo?", "advancements.adventure.summon_iron_golem.description": "Kunvokar fera golemo por defensar vilajo", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON> enga<PERSON>a", "advancements.adventure.throw_trident.description": "Ĵeti tridenton al io.\nNoto: Ĵeti for via sola armilo ne estas bona ideo.", "advancements.adventure.throw_trident.title": "<PERSON><PERSON>", "advancements.adventure.totem_of_undying.description": "<PERSON><PERSON> di Nemortiveso por trompar morto", "advancements.adventure.totem_of_undying.title": "Po<PERSON><PERSON>", "advancements.adventure.trade.description": "Sucese komercar kun vilajano", "advancements.adventure.trade.title": "Tanta duelo!", "advancements.adventure.trade_at_world_height.description": "Negoci kun vilagano ĉe la konstrua alteco limo", "advancements.adventure.trade_at_world_height.title": "Stelkomercisto", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Apliki ĉi tiujn kovro-modelojn minimume unufoje: <PERSON><PERSON>, Vizaĝo, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Vojetrovaĵo", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Metiĉado kun stilo", "advancements.adventure.trim_with_any_armor_pattern.description": "Krei tre kovritan armilon ĉe forĝa tablo", "advancements.adventure.trim_with_any_armor_pattern.title": "Kreante novan aspekton", "advancements.adventure.two_birds_one_arrow.description": "<PERSON><PERSON><PERSON>tom<PERSON> per trapasa sago", "advancements.adventure.two_birds_one_arrow.title": "<PERSON> uceli, un flecho", "advancements.adventure.under_lock_and_key.description": "Unlock a Vault with a Trial Key", "advancements.adventure.under_lock_and_key.title": "Under Lock and Key", "advancements.adventure.use_lodestone.description": "Use a Compass on a Lodestone", "advancements.adventure.use_lodestone.title": "Country Lode, Take Me Home", "advancements.adventure.very_very_frightening.description": "Strokar vilajano per fulmino", "advancements.adventure.very_very_frightening.title": "Tre tre teroriganta", "advancements.adventure.voluntary_exile.description": "Mortigar kapitano di invado.\nForsan konsiderez eskartar su de vilaji dum poka tempo...", "advancements.adventure.voluntary_exile.title": "<PERSON><PERSON> vol<PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "<PERSON><PERSON> sur pulvsneĝon... sen sinki en ĝin", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Lejera quale kuniklo", "advancements.adventure.who_needs_rockets.description": "Use a Wind Charge to launch yourself upward 8 blocks", "advancements.adventure.who_needs_rockets.title": "Who Needs Rockets?", "advancements.adventure.whos_the_pillager_now.description": "Donar a spoilanton gusto di lia medicino propra", "advancements.adventure.whos_the_pillager_now.title": "Qua esas la spoilanto nun?", "advancements.empty": "Ne semblas ke irgo esas hika...", "advancements.end.dragon_breath.description": "Kolekti drakospiron en vitroflaskon", "advancements.end.dragon_breath.title": "<PERSON>u bezonas minto", "advancements.end.dragon_egg.description": "Tenar l'ovo di drako", "advancements.end.dragon_egg.title": "La sequanta generaciono", "advancements.end.elytra.description": "<PERSON><PERSON><PERSON>", "advancements.end.elytra.title": "Cielo esas la limito", "advancements.end.enter_end_gateway.description": "Eskapar del insulo", "advancements.end.enter_end_gateway.title": "Fora ekiro", "advancements.end.find_end_city.description": "<PERSON><PERSON><PERSON>, quo povus eventar?", "advancements.end.find_end_city.title": "L'urbo ye la ludo-fino", "advancements.end.kill_dragon.description": "Fortuno", "advancements.end.kill_dragon.title": "Liberigez la End", "advancements.end.levitate.description": "Flotacar supre 50 bloki pro ataki di shulkero", "advancements.end.levitate.title": "Superba panoramo", "advancements.end.respawn_dragon.description": "Rigenitar l'ender-drako", "advancements.end.respawn_dragon.title": "La End... Itere...", "advancements.end.root.description": "O la komenco?", "advancements.end.root.title": "La End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Havi ke alajo faligu kukon ĉe notobloko", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Naskiĝtago-Ka<PERSON>", "advancements.husbandry.allay_deliver_item_to_player.description": "Havi ke alajo transdonu al vi erojn", "advancements.husbandry.allay_deliver_item_to_player.title": "Vi havas amikon en mi", "advancements.husbandry.axolotl_in_a_bucket.description": "Kapti akzolotlon en sitelo", "advancements.husbandry.axolotl_in_a_bucket.title": "La plej ĉarma predanto", "advancements.husbandry.balanced_diet.description": "Manjar omno qua esas manjebla, mem se ol ne nutrus vu", "advancements.husbandry.balanced_diet.title": "Dieto equilibrata", "advancements.husbandry.breed_all_animals.description": "<PERSON><PERSON>r omna animali!", "advancements.husbandry.breed_all_animals.title": "Duope", "advancements.husbandry.breed_an_animal.description": "<PERSON><PERSON><PERSON>", "advancements.husbandry.breed_an_animal.title": "La papagayi e la vespertilii", "advancements.husbandry.complete_catalogue.description": "<PERSON><PERSON> ĉiujn variaĵojn de katoj!", "advancements.husbandry.complete_catalogue.title": "Katologo kompleta", "advancements.husbandry.feed_snifflet.description": "<PERSON><PERSON><PERSON> manĝaĵon al musaĉeto", "advancements.husbandry.feed_snifflet.title": "Malgra<PERSON>j musetoj", "advancements.husbandry.fishy_business.description": "<PERSON><PERSON><PERSON>o", "advancements.husbandry.fishy_business.title": "Afero di fisho", "advancements.husbandry.froglights.description": "Havi ĉiujn lumbrilojn en via inventaro", "advancements.husbandry.froglights.title": "Kun niaj fortoj kombinitaj!", "advancements.husbandry.kill_axolotl_target.description": "Kune labori kun akzolotlo kaj gajni batalon", "advancements.husbandry.kill_axolotl_target.title": "La kuriga forto de amikeco!", "advancements.husbandry.leash_all_frog_variants.description": "Havi ĉiun variaĵon de rano sur plombo", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON>am la bando alvenas en urbon", "advancements.husbandry.make_a_sign_glow.description": "<PERSON><PERSON> de iu ajn speco de signo brili", "advancements.husbandry.make_a_sign_glow.title": "<PERSON><PERSON>u kaj rigardu!", "advancements.husbandry.netherite_hoe.description": "Uzi neteritan metalon por plibonigi ha<PERSON>, kaj poste revalori viajn vivo-elektojn", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.obtain_sniffer_egg.description": "Obtén un huevo de Esnifador", "advancements.husbandry.obtain_sniffer_egg.title": "Puŝas Interese", "advancements.husbandry.place_dried_ghast_in_water.description": "Place a Dried Ghast block into water", "advancements.husbandry.place_dried_ghast_in_water.title": "Stay Hydrated!", "advancements.husbandry.plant_any_sniffer_seed.description": "Planta una semilla encontrada por un Esnifador", "advancements.husbandry.plant_any_sniffer_seed.title": "<PERSON>i la Pasinton", "advancements.husbandry.plant_seed.description": "Plantacar semino e spektar lu kreskar", "advancements.husbandry.plant_seed.title": "Prenez ula grano!", "advancements.husbandry.remove_wolf_armor.description": "Remove Wolf Armor from a Wolf using Shears", "advancements.husbandry.remove_wolf_armor.title": "Shear Brilliance", "advancements.husbandry.repair_wolf_armor.description": "Fully repair damaged Wolf Armor using Armadillo Scutes", "advancements.husbandry.repair_wolf_armor.title": "Bona kom Nova", "advancements.husbandry.ride_a_boat_with_a_goat.description": "<PERSON><PERSON> en boaton kaj plaŭdu kun kapro", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Kio ajn plaŭdas vian kapron!", "advancements.husbandry.root.description": "La mondo esas amikoza e manjajoza", "advancements.husbandry.root.title": "Kultiveso", "advancements.husbandry.safely_harvest_honey.description": "<PERSON>zi fajrokuirilon por kolekti mielon el abelahivejo per vitroflasko sen ĝeneti la abelojn", "advancements.husbandry.safely_harvest_honey.title": "<PERSON>a mielo esas vua mielo", "advancements.husbandry.silk_touch_nest.description": "<PERSON>t<PERSON> un Stup de Albine cu 3 <PERSON><PERSON>, folo<PERSON><PERSON> Mătase", "advancements.husbandry.silk_touch_nest.title": "Spoliabelo plenjornala", "advancements.husbandry.tactical_fishing.description": "Kapti fiŝon... sen fiŝokaptilo!", "advancements.husbandry.tactical_fishing.title": "Peskado taktikala", "advancements.husbandry.tadpole_in_a_bucket.description": "Snare a Tadhead in a Buck", "advancements.husbandry.tadpole_in_a_bucket.title": "Yen ni venas, micra ranidaro!", "advancements.husbandry.tame_an_animal.description": "Amansar <PERSON>o", "advancements.husbandry.tame_an_animal.title": "Amiki por sempre", "advancements.husbandry.wax_off.description": "<PERSON><PERSON><PERSON> vakson for de kupra bloko!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.wax_on.description": "Apliki mielo-ombon al kupra bloko!", "advancements.husbandry.wax_on.title": "Vakso <PERSON>", "advancements.husbandry.whole_pack.description": "Tame one of each Wolf variant", "advancements.husbandry.whole_pack.title": "The Whole Pack", "advancements.nether.all_effects.description": "Aplikar omna efekti ad ipsa a sama tempo", "advancements.nether.all_effects.title": "Quale ni arivis hike?", "advancements.nether.all_potions.description": "Aplikar omna efekti di pociono ad ipsa a sama tempo", "advancements.nether.all_potions.title": "<PERSON><PERSON><PERSON>", "advancements.nether.brew_potion.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.brew_potion.title": "Pocionifeyo lokala", "advancements.nether.charge_respawn_anchor.description": "Ŝargi respawn-ankron ĝis la maksimumo", "advancements.nether.charge_respawn_anchor.title": "Ne Eĉ \"Naŭ\" Vivoj", "advancements.nether.create_beacon.description": "Konstruktar e pozar baliso", "advancements.nether.create_beacon.title": "<PERSON><PERSON> la lumo", "advancements.nether.create_full_beacon.description": "<PERSON><PERSON><PERSON> beliso plene-povesa", "advancements.nether.create_full_beacon.title": "Balisisto", "advancements.nether.distract_piglin.description": "<PERSON><PERSON><PERSON> kun oraj <PERSON>", "advancements.nether.distract_piglin.title": "Oh <PERSON><PERSON><PERSON>", "advancements.nether.explore_nether.description": "Explorar omna biomi di Nether", "advancements.nether.explore_nether.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.fast_travel.description": "<PERSON><PERSON> la Nether por voyajar 7km sur la surfaso", "advancements.nether.fast_travel.title": "Bulo en subspaco", "advancements.nether.find_bastion.description": "Eniru Bastion Restaĵon", "advancements.nether.find_bastion.title": "Tioj Estis <PERSON> Tagoj", "advancements.nether.find_fortress.description": "Deskov<PERSON> ed enirar fortreso di Nether", "advancements.nether.find_fortress.title": "Fortreso di terorajo", "advancements.nether.get_wither_skull.description": "Obtenar kranio di witherskeleto", "advancements.nether.get_wither_skull.title": "Spooky Scary Skeleton", "advancements.nether.loot_bastion.description": "<PERSON><PERSON> en Bastion Restaĵo", "advancements.nether.loot_bastion.title": "<PERSON><PERSON><PERSON>", "advancements.nether.netherite_armor.description": "<PERSON><PERSON> kompletaĵon da Neterita armilo", "advancements.nether.netherite_armor.title": "Kovru min per rudimentoj", "advancements.nether.obtain_ancient_debris.description": "Obtenar resajo anciena", "advancements.nether.obtain_ancient_debris.title": "Kaŝita en Profundoj", "advancements.nether.obtain_blaze_rod.description": "Ganar bastono di blazo", "advancements.nether.obtain_blaze_rod.title": "<PERSON>o", "advancements.nether.obtain_crying_obsidian.description": "Obtenar obsidiano ploranta", "advancements.nether.obtain_crying_obsidian.title": "Qua tranchas onyoni?", "advancements.nether.return_to_sender.description": "Exterminar ghasto kun <PERSON>o", "advancements.nether.return_to_sender.title": "<PERSON><PERSON><PERSON> a sendanto", "advancements.nether.ride_strider.description": "V<PERSON>jiru Strideron kun Fuson el Verdramaĝa Fungo sur Stango", "advancements.nether.ride_strider.title": "Ĉi tiu boato havas krurojn", "advancements.nether.ride_strider_in_overworld_lava.description": "Prendu Strideron por tre longa vojaĝo sur laveja lago en la Malsupra Mondo", "advancements.nether.ride_strider_in_overworld_lava.title": "Sentiĝas kiel hejmo", "advancements.nether.root.description": "Adportez vesti somerala", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON>", "advancements.nether.summon_wither.title": "Bonvelkez!", "advancements.nether.uneasy_alliance.description": "<PERSON><PERSON>, adduktar ol sekure al surfaco... e lore mortigar ol", "advancements.nether.uneasy_alliance.title": "Ali<PERSON><PERSON>", "advancements.nether.use_lodestone.description": "<PERSON><PERSON> kompason sur lodeŝtono", "advancements.nether.use_lodestone.title": "Lokaĵo, konduku min hejmen", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Febligar e lore risanigar zombia vilajano", "advancements.story.cure_zombie_villager.title": "Zombia mediko", "advancements.story.deflect_arrow.description": "Deflekti projektilon per Ŝildo", "advancements.story.deflect_arrow.title": "<PERSON>e cadie, danko", "advancements.story.enchant_item.description": "Charmar objekto a charmtablo", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "Enirar la portalo dil End", "advancements.story.enter_the_end.title": "La Fino?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON><PERSON><PERSON>, acendar ed enirar portalo di Nether", "advancements.story.enter_the_nether.title": "Ni mustas irar plu profunde", "advancements.story.follow_ender_eye.description": "Sequar Enderokulo", "advancements.story.follow_ender_eye.title": "<PERSON>uli supra!", "advancements.story.form_obsidian.description": "Obtenar bloko di obsidiano", "advancements.story.form_obsidian.title": "<PERSON><PERSON><PERSON>", "advancements.story.iron_tools.description": "Ameliorar vua piocho", "advancements.story.iron_tools.title": "La chanto tranchanta di teknologio", "advancements.story.lava_bucket.description": "<PERSON><PERSON><PERSON> kovrilon per lavego", "advancements.story.lava_bucket.title": "Atencez! Ol esas varmega", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.title": "<PERSON><PERSON><PERSON>!", "advancements.story.mine_stone.description": "Minar petro kun vua nova piocho", "advancements.story.mine_stone.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.obtain_armor.description": "Protektar vu su kun peco di fera homarmaro", "advancements.story.obtain_armor.title": "Vestizes tu su", "advancements.story.root.description": "La kordio e rakonto del ludo", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "<PERSON><PERSON><PERSON> ho<PERSON>maro salvas vivi", "advancements.story.shiny_gear.title": "Kovru min per diamantoj", "advancements.story.smelt_iron.description": "<PERSON><PERSON> ferinan ĉifon", "advancements.story.smelt_iron.title": "Aquirez fero", "advancements.story.upgrade_tools.description": "Konstruktar plu bona piocho", "advancements.story.upgrade_tools.title": "Qualeso superiora", "advancements.toast.challenge": "Defio kompletiges<PERSON>!", "advancements.toast.goal": "<PERSON><PERSON><PERSON> at<PERSON>!", "advancements.toast.task": "Avancado atinge<PERSON>!", "argument.anchor.invalid": "Ankroposteno di ento nevalida %s", "argument.angle.incomplete": "Nekompleta (atendata 1 angulo)", "argument.angle.invalid": "<PERSON><PERSON><PERSON> an<PERSON>", "argument.block.id.invalid": "Bloktipo nekonocata '%s'", "argument.block.property.duplicate": "Proprajo '%s' povas nur esar indikatis unfoye por bloko %s", "argument.block.property.invalid": "Bloko %s ne aceptas '%s' por %s proprajo", "argument.block.property.novalue": "Valoro expektata per proprajo '%s' sur bloko %s", "argument.block.property.unclosed": "Expektis klo<PERSON> ] por propraji di bloko-stando", "argument.block.property.unknown": "Bloko %s ne havas la proprajo '%s'", "argument.block.tag.disallowed": "Tagi ne esas permitata hike, nur bloki aktuala", "argument.color.invalid": "Koloro nekonocata '%s'", "argument.component.invalid": "Kompozanto di mesajo nevalida: %s", "argument.criteria.invalid": "Nekonata kriterio '%s'", "argument.dimension.invalid": "Nekonocata dimensiono '%s'", "argument.double.big": "<PERSON><PERSON><PERSON> mustas ne esar plu kam %s, trovis %s", "argument.double.low": "<PERSON><PERSON><PERSON> mustas ne esar min kam %s, trovis %s", "argument.entity.invalid": "Nomo od UUID nevalida", "argument.entity.notfound.entity": "<PERSON><PERSON> ento esis trovitis", "argument.entity.notfound.player": "<PERSON><PERSON> ludanto esis trovitis", "argument.entity.options.advancements.description": "Luderi kun progresadi", "argument.entity.options.distance.description": "Disto ad ento", "argument.entity.options.distance.negative": "Disto ne povas esar negativa", "argument.entity.options.dx.description": "Enti inter x ed x + dx", "argument.entity.options.dy.description": "Enti inter y ed y + dy", "argument.entity.options.dz.description": "Enti inter z ed z + dz", "argument.entity.options.gamemode.description": "<PERSON><PERSON><PERSON><PERSON> kun ludadreĝimo", "argument.entity.options.inapplicable": "Selekto '%s' ne esas aplikebla hike", "argument.entity.options.level.description": "Experienca nivelo", "argument.entity.options.level.negative": "Nivelo ne devas esar negativo", "argument.entity.options.limit.description": "Maxima nombri de enti por retrovenar", "argument.entity.options.limit.toosmall": "<PERSON><PERSON> must<PERSON> esar adminime 1", "argument.entity.options.mode.invalid": "Ludomodo nevalida o nekonocata '%s'", "argument.entity.options.name.description": "Nomo di la ento", "argument.entity.options.nbt.description": "Enti kun NBT", "argument.entity.options.predicate.description": "Predikato <PERSON>igi<PERSON>", "argument.entity.options.scores.description": "<PERSON>ti kun punti", "argument.entity.options.sort.description": "<PERSON><PERSON><PERSON><PERSON> la enti", "argument.entity.options.sort.irreversible": "Tipo di asorto nevalida o nekonocata '%s'", "argument.entity.options.tag.description": "<PERSON><PERSON> kun etiketo", "argument.entity.options.team.description": "Enti en esquado", "argument.entity.options.type.description": "Enti di tipo", "argument.entity.options.type.invalid": "Entotipo nevalida o nekonocata '%s'", "argument.entity.options.unknown": "Selekto nekonocata '%s'", "argument.entity.options.unterminated": "Expektis fino di selekti", "argument.entity.options.valueless": "Valoro expektata por selekto '%s'", "argument.entity.options.x.description": "x poziciono", "argument.entity.options.x_rotation.description": "Rotacado x di la ento", "argument.entity.options.y.description": "y poziciono", "argument.entity.options.y_rotation.description": "Rotacado y di la ento", "argument.entity.options.z.description": "z poziciono", "argument.entity.selector.allEntities": "<PERSON><PERSON>na enti", "argument.entity.selector.allPlayers": "<PERSON><PERSON><PERSON> luderi", "argument.entity.selector.missing": "Tipo di selektanto mankanta", "argument.entity.selector.nearestEntity": "Nearest entity", "argument.entity.selector.nearestPlayer": "Maxim proxima ludero", "argument.entity.selector.not_allowed": "Selektanto nepermisata", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON> ludero", "argument.entity.selector.self": "Aktuala ento", "argument.entity.selector.unknown": "Tipo di selektanto nekonocata '%s'", "argument.entity.toomany": "Nur un ento esas permisis, ma la provizita selektanto permisas plu kam un", "argument.enum.invalid": "Nevalida valoro \"%s\"", "argument.float.big": "Flotacanto mustas ne esar plu kam %s; trovis %s", "argument.float.low": "Flotacanto mustas ne esar min kam %s; trovis %s", "argument.gamemode.invalid": "Nekonata ludadreĝimo: %s", "argument.hexcolor.invalid": "Invalid hex color code '%s'", "argument.id.invalid": "ID nevalida", "argument.id.unknown": "ID nekonocata: %s", "argument.integer.big": "Integro mustas ne esar plu kam %s, trovis %s", "argument.integer.low": "Integro mustas ne esar min kam %s, trovis %s", "argument.item.id.invalid": "Objekto nekonocata '%s'", "argument.item.tag.disallowed": "Tagi n'esas permisis hike, nur objekti aktuala", "argument.literal.incorrect": "Expektis literalo %s", "argument.long.big": "Longnombro mustas ne esar plu kam %s, trovis %s", "argument.long.low": "Longnombro mustas ne esar min kam %s, trovis %s", "argument.message.too_long": "Chat message was too long (%s > maximum %s characters)", "argument.nbt.array.invalid": "Tipo di tableo nevalida '%s'", "argument.nbt.array.mixed": "Ne povas insertar %s aden %s", "argument.nbt.expected.compound": "Expected compound tag", "argument.nbt.expected.key": "Kelfo expektata", "argument.nbt.expected.value": "Valoro expektata", "argument.nbt.list.mixed": "Ne povas insertar %s aden listo di %s", "argument.nbt.trailing": "Trekanta donaji neexpektata", "argument.player.entities": "Nur ludanti povas esar efektigata per ica komando, ma la provizita selektanto inkluzas enti", "argument.player.toomany": "Nur un ludanto esas permisis, ma la provizita selektanto permisas plu kam un", "argument.player.unknown": "Ta ludanto n'existas", "argument.pos.missing.double": "Expektis k<PERSON>", "argument.pos.missing.int": "Expektis posteno di bloko", "argument.pos.mixed": "Ne povas mixar mondo e lokala koordinati (omno mustas sive uzar ^ o ne)", "argument.pos.outofbounds": "Tiu pozicio estas ekster la permesitaj limoj.", "argument.pos.outofworld": "Ta posteno esas extera mondo!", "argument.pos.unloaded": "Ta posteno n'esas charjita", "argument.pos2d.incomplete": "Nekompleta (atendataj 2 koordinoj)", "argument.pos3d.incomplete": "Nekompleta (expektis 3 koordinati)", "argument.range.empty": "Expektis valoro o variado di valori", "argument.range.ints": "<PERSON>ur totano<PERSON>ri esas permisis, ne decimali", "argument.range.swapped": "Minimo ne povas esar plu granda kam maximo", "argument.resource.invalid_type": "La elemento '%s' havas malĝustan tipon '%s' (atendita '%s')", "argument.resource.not_found": "Ne povas trovi la elementon '%s' de tipo '%s'", "argument.resource_or_id.failed_to_parse": "Failed to parse structure: %s", "argument.resource_or_id.invalid": "Malĝusta id aŭ etikedo", "argument.resource_or_id.no_such_element": "Can't find element '%s' in registry '%s'", "argument.resource_selector.not_found": "No matches for selector '%s' of type '%s'", "argument.resource_tag.invalid_type": "La etikedo '%s' havas malĝustan tipon '%s' (atendita '%s')", "argument.resource_tag.not_found": "Ne povas trovi la etikedon '%s' de tipo '%s'", "argument.rotation.incomplete": "Nekompleta (expektis 2 koordinati)", "argument.scoreHolder.empty": "Ne povis trovar ula punti-portilo relatanta", "argument.scoreboardDisplaySlot.invalid": "Montro-<PERSON><PERSON> nekonoc<PERSON> '%s'", "argument.style.invalid": "Nevalida stilo: %s", "argument.time.invalid_tick_count": "Konto di tiki mustas esar ne-negativa", "argument.time.invalid_unit": "Uno nevalida", "argument.time.tick_count_too_low": "La blokkvanto ne devas esti malpli ol %s, trovita %s", "argument.uuid.invalid": "Nevalida UUID", "argument.waypoint.invalid": "Selected entity is not a waypoint", "arguments.block.tag.unknown": "Bloktago nekonocata '%s'", "arguments.function.tag.unknown": "Funciontago nekonocata '%s'", "arguments.function.unknown": "Functiono nekonocata %s", "arguments.item.component.expected": "Espertita komponanto elemento", "arguments.item.component.malformed": "Malformita '%s' komponanto: '%s'", "arguments.item.component.repeated": "Item component '%s' was repeated, but only one value can be specified", "arguments.item.component.unknown": "Unknown item component '%s'", "arguments.item.malformed": "Malformed item: '%s'", "arguments.item.overstacked": "%s povas nur amasigar til %s", "arguments.item.predicate.malformed": "Malformed '%s' predicate: '%s'", "arguments.item.predicate.unknown": "Unknown item predicate '%s'", "arguments.item.tag.unknown": "Tago di objektalo nekonocata '%s'", "arguments.nbtpath.node.invalid": "Elemento di NBT-voyo esas nevalida", "arguments.nbtpath.nothing_found": "Trovis nula elementi qua korespondas a %s", "arguments.nbtpath.too_deep": "La rezultanta NBT tro profunde ennestita", "arguments.nbtpath.too_large": "La rezultanta NBT tro granda", "arguments.objective.notFound": "Objektalo di puntitableo nekonocata '%s'", "arguments.objective.readonly": "Objektalo di puntitableo '%s' esas lektar-nura", "arguments.operation.div0": "Ne povas dividar per zero", "arguments.operation.invalid": "Operaco nevalida", "arguments.swizzle.invalid": "Kombino di axi nevalida, expektis komdino di 'x', 'y' e 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "<PERSON><PERSON>", "attribute.name.attack_damage": "Attack Damage", "attribute.name.attack_knockback": "Attack Knockback", "attribute.name.attack_speed": "Attack Speed", "attribute.name.block_break_speed": "Block Break Speed", "attribute.name.block_interaction_range": "Block Interaction Range", "attribute.name.burning_time": "Burning Time", "attribute.name.camera_distance": "Camera Distance", "attribute.name.entity_interaction_range": "Entity Interaction Range", "attribute.name.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.fall_damage_multiplier": "Fall Damage Multiplier", "attribute.name.flying_speed": "Flying Speed", "attribute.name.follow_range": "<PERSON>b <PERSON> Range", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Hardeso di homarmaro", "attribute.name.generic.attack_damage": "Domajo di atako", "attribute.name.generic.attack_knockback": "<PERSON><PERSON><PERSON> Puŝo", "attribute.name.generic.attack_speed": "Rapideso di atako", "attribute.name.generic.block_interaction_range": "<PERSON><PERSON><PERSON>", "attribute.name.generic.burning_time": "Burning Time", "attribute.name.generic.entity_interaction_range": "<PERSON><PERSON><PERSON>", "attribute.name.generic.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.generic.fall_damage_multiplier": "Fall Damage Multiplier", "attribute.name.generic.flying_speed": "Flugspeedo", "attribute.name.generic.follow_range": "Sequodisto di kreaji", "attribute.name.generic.gravity": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.jump_strength": "Povo de saltado", "attribute.name.generic.knockback_resistance": "Rezisto a retrosalto", "attribute.name.generic.luck": "Ŝanco", "attribute.name.generic.max_absorption": "<PERSON><PERSON><PERSON><PERSON>or<PERSON>", "attribute.name.generic.max_health": "Maxima saneso", "attribute.name.generic.movement_efficiency": "Movement Efficiency", "attribute.name.generic.movement_speed": "Rapideco", "attribute.name.generic.oxygen_bonus": "Oxygen Bonus", "attribute.name.generic.safe_fall_distance": "Sekura Falodistanco", "attribute.name.generic.scale": "Skal<PERSON>", "attribute.name.generic.step_height": "Altezo di Pazo", "attribute.name.generic.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.gravity": "<PERSON><PERSON>", "attribute.name.horse.jump_strength": "Forteso di salto di kavalo", "attribute.name.jump_strength": "Jump Strength", "attribute.name.knockback_resistance": "Knockback Resistance", "attribute.name.luck": "Fortuno", "attribute.name.max_absorption": "Max Absorption", "attribute.name.max_health": "Max Health", "attribute.name.mining_efficiency": "Mining Efficiency", "attribute.name.movement_efficiency": "Movement Efficiency", "attribute.name.movement_speed": "Rapideso", "attribute.name.oxygen_bonus": "Oxygen Bonus", "attribute.name.player.block_break_speed": "Rompopa Rapideco", "attribute.name.player.block_interaction_range": "Raggio di Interago di Bloko", "attribute.name.player.entity_interaction_range": "Raggio di Interago di Enteco", "attribute.name.player.mining_efficiency": "Mining Efficiency", "attribute.name.player.sneaking_speed": "Sneaking Speed", "attribute.name.player.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.player.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.safe_fall_distance": "Safe Fall Distance", "attribute.name.scale": "Skal<PERSON>", "attribute.name.sneaking_speed": "Sneaking Speed", "attribute.name.spawn_reinforcements": "Zombie Reinforcements", "attribute.name.step_height": "Step Height", "attribute.name.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.tempt_range": "Mob Tempt Range", "attribute.name.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.waypoint_receive_range": "Waypoint Receive Range", "attribute.name.waypoint_transmit_range": "Waypoint Transmit Range", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON><PERSON> rin<PERSON>i", "biome.minecraft.badlands": "Badlandi", "biome.minecraft.bamboo_jungle": "Bambujunglo", "biome.minecraft.basalt_deltas": "Delto bazalta", "biome.minecraft.beach": "<PERSON><PERSON><PERSON>", "biome.minecraft.birch_forest": "Birkforesto", "biome.minecraft.cherry_grove": "Ĉeriza Arbaro", "biome.minecraft.cold_ocean": "<PERSON><PERSON> plajo", "biome.minecraft.crimson_forest": "<PERSON><PERSON>", "biome.minecraft.dark_forest": "Foresto obskura", "biome.minecraft.deep_cold_ocean": "<PERSON>lda oceano profunda", "biome.minecraft.deep_dark": "Tenebro profunda", "biome.minecraft.deep_frozen_ocean": "Frostita oceano profunda", "biome.minecraft.deep_lukewarm_ocean": "Tepida oceano profunda", "biome.minecraft.deep_ocean": "Oceano profunda", "biome.minecraft.desert": "Dezerto", "biome.minecraft.dripstone_caves": "Kaverno di <PERSON>tri", "biome.minecraft.end_barrens": "<PERSON><PERSON> sterila dil <PERSON>", "biome.minecraft.end_highlands": "Altateri dil End", "biome.minecraft.end_midlands": "Mez<PERSON>i dil End", "biome.minecraft.eroded_badlands": "Badlandi erodita", "biome.minecraft.flower_forest": "Foresto di flori", "biome.minecraft.forest": "Foresto", "biome.minecraft.frozen_ocean": "<PERSON><PERSON>", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_river": "<PERSON><PERSON>o", "biome.minecraft.grove": "<PERSON><PERSON><PERSON>", "biome.minecraft.ice_spikes": "Stalagmiti di glacio", "biome.minecraft.jagged_peaks": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.jungle": "<PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "<PERSON>pida oceano", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON>", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON> di <PERSON>gliero", "biome.minecraft.meadow": "Prato", "biome.minecraft.mushroom_fields": "Agri di champinioni", "biome.minecraft.nether_wastes": "<PERSON><PERSON> ster<PERSON>", "biome.minecraft.ocean": "Oceano", "biome.minecraft.old_growth_birch_forest": "Malnova Kresko da Betularo Arbaro", "biome.minecraft.old_growth_pine_taiga": "Taĥa arbaro kun malnovaj pinoj", "biome.minecraft.old_growth_spruce_taiga": "Taĥa arbaro kun malnovaj abioj", "biome.minecraft.pale_garden": "Pale Garden", "biome.minecraft.plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.river": "Rivero", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Platajo di savano", "biome.minecraft.small_end_islands": "Mikra insuli dil End", "biome.minecraft.snowy_beach": "<PERSON><PERSON><PERSON> plajo", "biome.minecraft.snowy_plains": "<PERSON><PERSON><PERSON>i", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON> penti", "biome.minecraft.snowy_taiga": "<PERSON><PERSON><PERSON> taigo", "biome.minecraft.soul_sand_valley": "Valo di anmosablo", "biome.minecraft.sparse_jungle": "<PERSON><PERSON>", "biome.minecraft.stony_peaks": "Montopinto petra", "biome.minecraft.stony_shore": "Rivo petra", "biome.minecraft.sunflower_plains": "Planaji di sunflori", "biome.minecraft.swamp": "<PERSON><PERSON>", "biome.minecraft.taiga": "Taigo", "biome.minecraft.the_end": "La End", "biome.minecraft.the_void": "La vakuajo", "biome.minecraft.warm_ocean": "Varma oceano", "biome.minecraft.warped_forest": "<PERSON><PERSON> bi<PERSON>", "biome.minecraft.windswept_forest": "Ventofrapita <PERSON>", "biome.minecraft.windswept_gravelly_hills": "Ventofrapitaj <PERSON> kun Ŝtongruzo", "biome.minecraft.windswept_hills": "Ventfrapegaj Montetoj", "biome.minecraft.windswept_savanna": "Ventfrapegan Savanon", "biome.minecraft.wooded_badlands": "Badlandi arboroza", "block.minecraft.acacia_button": "<PERSON><PERSON> aka<PERSON>", "block.minecraft.acacia_door": "<PERSON><PERSON>", "block.minecraft.acacia_fence": "Fenco akacia", "block.minecraft.acacia_fence_gate": "<PERSON><PERSON><PERSON> aka<PERSON>", "block.minecraft.acacia_hanging_sign": "Pendanta signo akacia", "block.minecraft.acacia_leaves": "Folii akacia", "block.minecraft.acacia_log": "<PERSON><PERSON>", "block.minecraft.acacia_planks": "<PERSON><PERSON>", "block.minecraft.acacia_pressure_plate": "Presoplak<PERSON>", "block.minecraft.acacia_sapling": "Arboreto akacia", "block.minecraft.acacia_sign": "<PERSON><PERSON>", "block.minecraft.acacia_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_stairs": "Eskalero akacia", "block.minecraft.acacia_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.acacia_wall_hanging_sign": "Murala pendanta signo akacia", "block.minecraft.acacia_wall_sign": "Murala signo akacia", "block.minecraft.acacia_wood": "Ligno aka<PERSON>", "block.minecraft.activator_rail": "Relo aktiviga", "block.minecraft.air": "Aero", "block.minecraft.allium": "Alliumo", "block.minecraft.amethyst_block": "Bloko di ametisto", "block.minecraft.amethyst_cluster": "Amaso di ametisto", "block.minecraft.ancient_debris": "Resajo anciena", "block.minecraft.andesite": "<PERSON><PERSON>", "block.minecraft.andesite_slab": "Plako andesita", "block.minecraft.andesite_stairs": "Eskalero andesita", "block.minecraft.andesite_wall": "Muro andesita", "block.minecraft.anvil": "Amboso", "block.minecraft.attached_melon_stem": "<PERSON><PERSON>a me<PERSON>", "block.minecraft.attached_pumpkin_stem": "Stipito di kukurbito atachata", "block.minecraft.azalea": "Azaleo", "block.minecraft.azalea_leaves": "Folii azalea", "block.minecraft.azure_bluet": "<PERSON><PERSON><PERSON> ho<PERSON>o", "block.minecraft.bamboo": "<PERSON><PERSON><PERSON>", "block.minecraft.bamboo_block": "B<PERSON><PERSON> da bambuo", "block.minecraft.bamboo_button": "<PERSON>ono bambua", "block.minecraft.bamboo_door": "<PERSON><PERSON> b<PERSON>", "block.minecraft.bamboo_fence": "Fenco bam<PERSON>a", "block.minecraft.bamboo_fence_gate": "<PERSON><PERSON><PERSON> bam<PERSON>a", "block.minecraft.bamboo_hanging_sign": "Pendanta signo bambua", "block.minecraft.bamboo_mosaic": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.bamboo_mosaic_slab": "Plako di mozaiko bambua", "block.minecraft.bamboo_mosaic_stairs": "Eskalero di mozaiko bambua", "block.minecraft.bamboo_planks": "<PERSON><PERSON> bam<PERSON>", "block.minecraft.bamboo_pressure_plate": "Presoplak<PERSON> bam<PERSON>a", "block.minecraft.bamboo_sapling": "<PERSON><PERSON><PERSON><PERSON> bam<PERSON>a", "block.minecraft.bamboo_sign": "<PERSON><PERSON> b<PERSON>", "block.minecraft.bamboo_slab": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.bamboo_stairs": "Eskalero bambua", "block.minecraft.bamboo_trapdoor": "<PERSON><PERSON><PERSON> bam<PERSON>a", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON><PERSON>a pendanta signo bambua", "block.minecraft.bamboo_wall_sign": "<PERSON><PERSON><PERSON> signo bambua", "block.minecraft.banner.base.black": "Plene nigra kampo", "block.minecraft.banner.base.blue": "<PERSON><PERSON> blua kampo", "block.minecraft.banner.base.brown": "Plene bruna kampo", "block.minecraft.banner.base.cyan": "<PERSON>y <PERSON><PERSON>", "block.minecraft.banner.base.gray": "<PERSON><PERSON> griza kampo", "block.minecraft.banner.base.green": "<PERSON><PERSON> verda kampo", "block.minecraft.banner.base.light_blue": "Fully Light Blue Field", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON> he<PERSON> kampo", "block.minecraft.banner.base.lime": "Fully Lime Field", "block.minecraft.banner.base.magenta": "Plene magenta kampo", "block.minecraft.banner.base.orange": "Fully Orange Field", "block.minecraft.banner.base.pink": "Fully Pink Field", "block.minecraft.banner.base.purple": "Plene purpura kampo", "block.minecraft.banner.base.red": "<PERSON><PERSON> ruga kampo", "block.minecraft.banner.base.white": "<PERSON><PERSON> blanka kampo", "block.minecraft.banner.base.yellow": "Plene flava kampo", "block.minecraft.banner.border.black": "<PERSON><PERSON> bordumo", "block.minecraft.banner.border.blue": "<PERSON><PERSON> bordumo", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON> bord<PERSON>o", "block.minecraft.banner.border.cyan": "<PERSON><PERSON> bord<PERSON>o", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON> bord<PERSON>o", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON> bordumo", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> bordumo", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> bordumo", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON> bordumo", "block.minecraft.banner.border.magenta": "<PERSON><PERSON>a bordumo", "block.minecraft.banner.border.orange": "<PERSON><PERSON><PERSON><PERSON> bord<PERSON>o", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON> bordumo", "block.minecraft.banner.border.purple": "<PERSON><PERSON><PERSON> bordumo", "block.minecraft.banner.border.red": "<PERSON><PERSON> bordumo", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON> bordumo", "block.minecraft.banner.border.yellow": "<PERSON><PERSON> bordumo", "block.minecraft.banner.bricks.black": "<PERSON><PERSON> brik<PERSON>", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON> br<PERSON>", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON><PERSON> br<PERSON>", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON> br<PERSON>", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> brik<PERSON>", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> brik<PERSON>", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON><PERSON> brik<PERSON>", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON> brik<PERSON>", "block.minecraft.banner.bricks.orange": "<PERSON><PERSON><PERSON><PERSON> br<PERSON>", "block.minecraft.banner.bricks.pink": "<PERSON><PERSON><PERSON> brik<PERSON>", "block.minecraft.banner.bricks.purple": "Purpura brikmuro", "block.minecraft.banner.bricks.red": "<PERSON><PERSON> br<PERSON>", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON> brik<PERSON>", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON> brik<PERSON>", "block.minecraft.banner.circle.black": "<PERSON><PERSON> cirklo", "block.minecraft.banner.circle.blue": "<PERSON><PERSON> c<PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON> c<PERSON>", "block.minecraft.banner.circle.gray": "Griza cirklo", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.banner.circle.light_blue": "<PERSON>iel<PERSON><PERSON>a c<PERSON>", "block.minecraft.banner.circle.light_gray": "Klargriza cirk<PERSON>", "block.minecraft.banner.circle.lime": "Klarverda cirklo", "block.minecraft.banner.circle.magenta": "Magenta cirklo", "block.minecraft.banner.circle.orange": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.banner.circle.pink": "Rozea cirklo", "block.minecraft.banner.circle.purple": "<PERSON><PERSON><PERSON> cirklo", "block.minecraft.banner.circle.red": "<PERSON><PERSON> c<PERSON>", "block.minecraft.banner.circle.white": "<PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON> cirklo", "block.minecraft.banner.creeper.black": "Nigra vizajo di creepero", "block.minecraft.banner.creeper.blue": "Blua vizajo di creeper", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON><PERSON> vizajo di creeper", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON> vizajo di creeper", "block.minecraft.banner.creeper.gray": "Griza vizajo di creeper", "block.minecraft.banner.creeper.green": "Verda vizajo di creeper", "block.minecraft.banner.creeper.light_blue": "Cielblua vizajo di creeper", "block.minecraft.banner.creeper.light_gray": "Klargriza vizajo di creeper", "block.minecraft.banner.creeper.lime": "Klarverda vizajo di creeper", "block.minecraft.banner.creeper.magenta": "Magenta vizajo di creeper", "block.minecraft.banner.creeper.orange": "Oranjea vizajo di creeper", "block.minecraft.banner.creeper.pink": "Rozea vizajo di creeper", "block.minecraft.banner.creeper.purple": "Purpura vizajo di creeper", "block.minecraft.banner.creeper.red": "Reda vizajo di creeper", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON> vizajo di creeper", "block.minecraft.banner.creeper.yellow": "<PERSON>lava vizajo di creeper", "block.minecraft.banner.cross.black": "Nigra kruco diagonala", "block.minecraft.banner.cross.blue": "<PERSON><PERSON> kruco <PERSON>a", "block.minecraft.banner.cross.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON>", "block.minecraft.banner.cross.gray": "Griza kruco diagonala", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON> k<PERSON>a", "block.minecraft.banner.cross.light_blue": "Cielblua kru<PERSON>a", "block.minecraft.banner.cross.light_gray": "Klargriza kruco diagonala", "block.minecraft.banner.cross.lime": "Klarverda kruco diagonala", "block.minecraft.banner.cross.magenta": "Magenta kruco diagonala", "block.minecraft.banner.cross.orange": "Or<PERSON><PERSON><PERSON>a", "block.minecraft.banner.cross.pink": "Rozea kruco <PERSON>a", "block.minecraft.banner.cross.purple": "Purpura kruco diagonala", "block.minecraft.banner.cross.red": "Reda kruco diagonala", "block.minecraft.banner.cross.white": "<PERSON><PERSON><PERSON> kruco diagonala", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON> kru<PERSON>a", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON> bordumo dentoza", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON> bordumo dentoza", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> bord<PERSON>o den<PERSON>", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON> bordumo den<PERSON>", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON> bordumo den<PERSON>", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON> bordumo den<PERSON>", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> bordumo dentoza", "block.minecraft.banner.curly_border.light_gray": "Klargriza bordumo dentoza", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON><PERSON> bordumo dento<PERSON>", "block.minecraft.banner.curly_border.magenta": "Ma<PERSON>a bordumo dentoza", "block.minecraft.banner.curly_border.orange": "<PERSON><PERSON><PERSON><PERSON> bord<PERSON>o den<PERSON>", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON> bordumo dentoza", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON><PERSON> bordumo dentoza", "block.minecraft.banner.curly_border.red": "<PERSON>a bordumo dentoza", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON> bordumo dento<PERSON>", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON> bordumo dentoza", "block.minecraft.banner.diagonal_left.black": "Nigra diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.blue": "Blua diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON> diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.cyan": "Cyana diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.gray": "Griza diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.green": "Verda diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.light_blue": "Cielblua diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.light_gray": "Klargriza diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.lime": "Klarverda diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.magenta": "Magenta diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.orange": "Oranjea diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.pink": "Rozea diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.purple": "Purpura diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.red": "Reda diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> diagonalo suprasinistra", "block.minecraft.banner.diagonal_left.yellow": "<PERSON>lava diagonalo suprasinistra", "block.minecraft.banner.diagonal_right.black": "Nigra diagonalo supradextra", "block.minecraft.banner.diagonal_right.blue": "Blua diagonalo supradextra", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON> diagonalo supradextra", "block.minecraft.banner.diagonal_right.cyan": "Cyana diagonalo supradextra", "block.minecraft.banner.diagonal_right.gray": "Griza diagonalo supradextra", "block.minecraft.banner.diagonal_right.green": "Verda diagonalo supradextra", "block.minecraft.banner.diagonal_right.light_blue": "Cielblua diagonalo supradextra", "block.minecraft.banner.diagonal_right.light_gray": "Klargriza diagonalo supradextra", "block.minecraft.banner.diagonal_right.lime": "Klarverda diagonalo supradextra", "block.minecraft.banner.diagonal_right.magenta": "Magenta diagonalo supradextra", "block.minecraft.banner.diagonal_right.orange": "Oranjea diagonalo supradextra", "block.minecraft.banner.diagonal_right.pink": "Rozea diagonalo supradextra", "block.minecraft.banner.diagonal_right.purple": "Purpura diagonalo supradextra", "block.minecraft.banner.diagonal_right.red": "Reda diagonalo supradextra", "block.minecraft.banner.diagonal_right.white": "Blanka diagonalo supradextra", "block.minecraft.banner.diagonal_right.yellow": "Flava diagonalo supradextra", "block.minecraft.banner.diagonal_up_left.black": "Nigra diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.blue": "Blua diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON><PERSON> diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.cyan": "Cyana diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.gray": "Griza diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.green": "Verda diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.light_blue": "Cielblua diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.light_gray": "Klargriza diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.lime": "Klarverda diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.magenta": "Magenta diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.orange": "Oranjea diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.pink": "Rozea diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.purple": "Purpura diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.red": "Reda diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.white": "Blanka diagonalo infradextra", "block.minecraft.banner.diagonal_up_left.yellow": "Flava diagonalo infradextra", "block.minecraft.banner.diagonal_up_right.black": "Nigra diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.blue": "Blua diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON><PERSON> diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.cyan": "Cyana diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.gray": "Griza diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.green": "Verda diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.light_blue": "Cielblua diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.light_gray": "Klargriza diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.lime": "Klarverda diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.magenta": "Magenta diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.orange": "Oranjea diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.pink": "Rozea diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.purple": "Purpura diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.red": "Reda diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.white": "Blanka diagonalo infrasinistra", "block.minecraft.banner.diagonal_up_right.yellow": "Flava diagonalo infrasinistra", "block.minecraft.banner.flow.black": "<PERSON><PERSON>", "block.minecraft.banner.flow.blue": "Blua F<PERSON>o", "block.minecraft.banner.flow.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON>", "block.minecraft.banner.flow.gray": "Griza Fluo", "block.minecraft.banner.flow.green": "Verda Fluo", "block.minecraft.banner.flow.light_blue": "Cielblua Fluo", "block.minecraft.banner.flow.light_gray": "Hela Griza Fluo", "block.minecraft.banner.flow.lime": "Lime Flow", "block.minecraft.banner.flow.magenta": "Magenta Fluo", "block.minecraft.banner.flow.orange": "Oranjea Fluo", "block.minecraft.banner.flow.pink": "Rozea Fluo", "block.minecraft.banner.flow.purple": "<PERSON><PERSON>", "block.minecraft.banner.flow.red": "Ruga Fluo", "block.minecraft.banner.flow.white": "Blanka Fluo", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON>", "block.minecraft.banner.flower.black": "Nigra floro", "block.minecraft.banner.flower.blue": "Blua floro", "block.minecraft.banner.flower.brown": "<PERSON>runa floro", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON> floro", "block.minecraft.banner.flower.gray": "Griza floro", "block.minecraft.banner.flower.green": "Verda floro", "block.minecraft.banner.flower.light_blue": "Cielblua floro", "block.minecraft.banner.flower.light_gray": "Klargriza floro", "block.minecraft.banner.flower.lime": "Klarverda floro", "block.minecraft.banner.flower.magenta": "Magenta floro", "block.minecraft.banner.flower.orange": "Oranjea floro", "block.minecraft.banner.flower.pink": "Rozea floro", "block.minecraft.banner.flower.purple": "Purpura floro", "block.minecraft.banner.flower.red": "Reda floro", "block.minecraft.banner.flower.white": "Blanka floro", "block.minecraft.banner.flower.yellow": "Flava floro", "block.minecraft.banner.globe.black": "Nigra globo", "block.minecraft.banner.globe.blue": "Blua globo", "block.minecraft.banner.globe.brown": "Bruna globo", "block.minecraft.banner.globe.cyan": "Cyana globo", "block.minecraft.banner.globe.gray": "Griza globo", "block.minecraft.banner.globe.green": "Verda globo", "block.minecraft.banner.globe.light_blue": "Cielblua globo", "block.minecraft.banner.globe.light_gray": "Klargriza globo", "block.minecraft.banner.globe.lime": "Klarverda globo", "block.minecraft.banner.globe.magenta": "Magenta globo", "block.minecraft.banner.globe.orange": "Oranjea globo", "block.minecraft.banner.globe.pink": "Rozea globo", "block.minecraft.banner.globe.purple": "Purpura globo", "block.minecraft.banner.globe.red": "Reda globo", "block.minecraft.banner.globe.white": "Blanka globo", "block.minecraft.banner.globe.yellow": "Flava globo", "block.minecraft.banner.gradient.black": "Nigra gradiento supra", "block.minecraft.banner.gradient.blue": "<PERSON>a gradiento supra", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON> gradiento supra", "block.minecraft.banner.gradient.cyan": "Cyana gradiento supra", "block.minecraft.banner.gradient.gray": "Griza gradiento supra", "block.minecraft.banner.gradient.green": "Verda gradiento supra", "block.minecraft.banner.gradient.light_blue": "Cielblua gradiento supra", "block.minecraft.banner.gradient.light_gray": "Klargriza gradiento supra", "block.minecraft.banner.gradient.lime": "Klarverda gradiento supra", "block.minecraft.banner.gradient.magenta": "Magenta gradiento supra", "block.minecraft.banner.gradient.orange": "Oranjea gradiento supra", "block.minecraft.banner.gradient.pink": "Rozea gradiento supra", "block.minecraft.banner.gradient.purple": "Purpura gradiento supra", "block.minecraft.banner.gradient.red": "Reda gradiento supra", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON> gradiento supra", "block.minecraft.banner.gradient.yellow": "<PERSON>lava gradiento supra", "block.minecraft.banner.gradient_up.black": "Nigra gradiento infra", "block.minecraft.banner.gradient_up.blue": "Blua gradiento infra", "block.minecraft.banner.gradient_up.brown": "Bruna gradiento infra", "block.minecraft.banner.gradient_up.cyan": "Cyana gradiento infra", "block.minecraft.banner.gradient_up.gray": "Griza gradiento infra", "block.minecraft.banner.gradient_up.green": "Verda gradiento infra", "block.minecraft.banner.gradient_up.light_blue": "Cielblua gradiento infra", "block.minecraft.banner.gradient_up.light_gray": "Klargriza gradiento infra", "block.minecraft.banner.gradient_up.lime": "Klarverda gradiento infra", "block.minecraft.banner.gradient_up.magenta": "Magenta gradiento infra", "block.minecraft.banner.gradient_up.orange": "Oranjea gradiento infra", "block.minecraft.banner.gradient_up.pink": "Rozea gradiento infra", "block.minecraft.banner.gradient_up.purple": "Purpura gradiento infra", "block.minecraft.banner.gradient_up.red": "Reda gradiento infra", "block.minecraft.banner.gradient_up.white": "Blanka gradiento infra", "block.minecraft.banner.gradient_up.yellow": "Flava gradiento infra", "block.minecraft.banner.guster.black": "<PERSON><PERSON> Virvolto", "block.minecraft.banner.guster.blue": "<PERSON><PERSON> Virvolto", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON>", "block.minecraft.banner.guster.gray": "Griza Virvolto", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON> Vir<PERSON>", "block.minecraft.banner.guster.light_blue": "Cielblua Virvolto", "block.minecraft.banner.guster.light_gray": "Klargriza Virvolto", "block.minecraft.banner.guster.lime": "Klarverda Virvolto", "block.minecraft.banner.guster.magenta": "Magenta Virvolto", "block.minecraft.banner.guster.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.pink": "Rozea Virvolto", "block.minecraft.banner.guster.purple": "Purpura Virvolto", "block.minecraft.banner.guster.red": "Reda Virvolto", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON> Virvolto", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.black": "Nigra duimo supra", "block.minecraft.banner.half_horizontal.blue": "<PERSON>a duimo supra", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON> duimo supra", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON> duimo supra", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON><PERSON> duimo supra", "block.minecraft.banner.half_horizontal.green": "Verda duimo supra", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> duimo supra", "block.minecraft.banner.half_horizontal.light_gray": "<PERSON>larg<PERSON><PERSON> duimo supra", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON><PERSON><PERSON> duimo supra", "block.minecraft.banner.half_horizontal.magenta": "Magenta duimo supra", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON><PERSON> duimo supra", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON>ea duimo supra", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON><PERSON> duimo supra", "block.minecraft.banner.half_horizontal.red": "<PERSON>a duimo supra", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON> duimo supra", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON> duimo supra", "block.minecraft.banner.half_horizontal_bottom.black": "Nigra duimo infra", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON>a duimo infra", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON><PERSON> du<PERSON> infra", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON> du<PERSON> infra", "block.minecraft.banner.half_horizontal_bottom.gray": "Griza duimo infra", "block.minecraft.banner.half_horizontal_bottom.green": "Verda duimo infra", "block.minecraft.banner.half_horizontal_bottom.light_blue": "<PERSON>iel<PERSON><PERSON>a duimo infra", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Klargriza duimo infra", "block.minecraft.banner.half_horizontal_bottom.lime": "Klarverda duimo infra", "block.minecraft.banner.half_horizontal_bottom.magenta": "Magenta duimo infra", "block.minecraft.banner.half_horizontal_bottom.orange": "Oran<PERSON>a duimo infra", "block.minecraft.banner.half_horizontal_bottom.pink": "Rozea duimo infra", "block.minecraft.banner.half_horizontal_bottom.purple": "Purpura duimo infra", "block.minecraft.banner.half_horizontal_bottom.red": "Reda duimo infra", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON><PERSON> duimo infra", "block.minecraft.banner.half_horizontal_bottom.yellow": "Flava duimo infra", "block.minecraft.banner.half_vertical.black": "Nigra duimo sinistra", "block.minecraft.banner.half_vertical.blue": "Blua diagonalo infrasinistra", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON> diagonalo infrasinistra", "block.minecraft.banner.half_vertical.cyan": "Cyana diagonalo infrasinistra", "block.minecraft.banner.half_vertical.gray": "Griza diagonalo infrasinistra", "block.minecraft.banner.half_vertical.green": "Verda diagonalo infrasinistra", "block.minecraft.banner.half_vertical.light_blue": "Cielblua diagonalo infrasinistra", "block.minecraft.banner.half_vertical.light_gray": "Klargriza diagonalo infrasinistra", "block.minecraft.banner.half_vertical.lime": "Klarverda diagonalo infrasinistra", "block.minecraft.banner.half_vertical.magenta": "Magenta diagonalo infrasinistra", "block.minecraft.banner.half_vertical.orange": "Oranjea diagonalo infrasinistra", "block.minecraft.banner.half_vertical.pink": "Rozea diagonalo infrasinistra", "block.minecraft.banner.half_vertical.purple": "Purpura diagonalo infrasinistra", "block.minecraft.banner.half_vertical.red": "Reda duimo sinistra", "block.minecraft.banner.half_vertical.white": "Blanka diagonalo infrasinistra", "block.minecraft.banner.half_vertical.yellow": "Flava diagonalo infrasinistra", "block.minecraft.banner.half_vertical_right.black": "Nigra duimo dextra", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON> du<PERSON> dextra", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON><PERSON> <PERSON><PERSON> dextra", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON> du<PERSON> dextra", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON><PERSON> <PERSON><PERSON> dextra", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON>a du<PERSON> dextra", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> du<PERSON> dextra", "block.minecraft.banner.half_vertical_right.light_gray": "Klarg<PERSON><PERSON> duimo dextra", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON><PERSON><PERSON> duimo dextra", "block.minecraft.banner.half_vertical_right.magenta": "Magenta duimo dextra", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON><PERSON><PERSON> du<PERSON> dextra", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON><PERSON> duimo dextra", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON><PERSON> duimo dextra", "block.minecraft.banner.half_vertical_right.red": "<PERSON>a duimo dextra", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON><PERSON> du<PERSON> dextra", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON> duimo dextra", "block.minecraft.banner.mojang.black": "<PERSON>gra insigno", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON> insigno", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON><PERSON> insigno", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON> insigno", "block.minecraft.banner.mojang.gray": "Griza insigno", "block.minecraft.banner.mojang.green": "<PERSON><PERSON>a insigno", "block.minecraft.banner.mojang.light_blue": "Cielblua insigno", "block.minecraft.banner.mojang.light_gray": "Klargriza insigno", "block.minecraft.banner.mojang.lime": "Klarverda insigno", "block.minecraft.banner.mojang.magenta": "Magenta insigno", "block.minecraft.banner.mojang.orange": "Oran<PERSON>a insigno", "block.minecraft.banner.mojang.pink": "Rozea insigno", "block.minecraft.banner.mojang.purple": "Purpura insigno", "block.minecraft.banner.mojang.red": "Reda insigno", "block.minecraft.banner.mojang.white": "<PERSON><PERSON><PERSON> insigno", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON> insigno", "block.minecraft.banner.piglin.black": "Nigra musaĉa vizaĝo", "block.minecraft.banner.piglin.blue": "Blua musaĉa vizaĝo", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON> musaĉa vizaĝo", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON>", "block.minecraft.banner.piglin.gray": "Griza musaĉa vizaĝo", "block.minecraft.banner.piglin.green": "Verda musaĉa vizaĝo", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> musaĉa vizaĝo", "block.minecraft.banner.piglin.light_gray": "Helgriza musaĉa vizaĝo", "block.minecraft.banner.piglin.lime": "Lime S<PERSON>ut", "block.minecraft.banner.piglin.magenta": "Magenta musaĉa vizaĝo", "block.minecraft.banner.piglin.orange": "Orange Snout", "block.minecraft.banner.piglin.pink": "<PERSON> Snout", "block.minecraft.banner.piglin.purple": "Purpura musaĉa vizaĝo", "block.minecraft.banner.piglin.red": "Ruĝa musaĉa vizaĝo", "block.minecraft.banner.piglin.white": "Blanka musaĉa vizaĝo", "block.minecraft.banner.piglin.yellow": "Flava musaĉa vizaĝo", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON> rombo", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON> rombo", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON> r<PERSON>o", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON> r<PERSON>o", "block.minecraft.banner.rhombus.gray": "Griza rombo", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON> rombo", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> rombo", "block.minecraft.banner.rhombus.light_gray": "Klarg<PERSON><PERSON> rombo", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON><PERSON> rombo", "block.minecraft.banner.rhombus.magenta": "Magenta rombo", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON><PERSON><PERSON> rombo", "block.minecraft.banner.rhombus.pink": "Rozea rombo", "block.minecraft.banner.rhombus.purple": "Purpura rombo", "block.minecraft.banner.rhombus.red": "<PERSON>a rombo", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON><PERSON> rombo", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON> rombo", "block.minecraft.banner.skull.black": "Nigra kranio", "block.minecraft.banner.skull.blue": "<PERSON><PERSON> kranio", "block.minecraft.banner.skull.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.skull.gray": "Griza kranio", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON> k<PERSON>o", "block.minecraft.banner.skull.light_blue": "Cielblua kranio", "block.minecraft.banner.skull.light_gray": "Klargriza kranio", "block.minecraft.banner.skull.lime": "Klarverda kranio", "block.minecraft.banner.skull.magenta": "Magenta kranio", "block.minecraft.banner.skull.orange": "Or<PERSON><PERSON><PERSON> k<PERSON>o", "block.minecraft.banner.skull.pink": "Rozea kranio", "block.minecraft.banner.skull.purple": "Purpura kranio", "block.minecraft.banner.skull.red": "Reda kranio", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON> kranio", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON> kranio", "block.minecraft.banner.small_stripes.black": "Nigra strieti", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON> strieti", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON> strieti", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON> stri<PERSON>", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON><PERSON> strieti", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON> strieti", "block.minecraft.banner.small_stripes.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> strieti", "block.minecraft.banner.small_stripes.light_gray": "<PERSON>larg<PERSON><PERSON> strieti", "block.minecraft.banner.small_stripes.lime": "<PERSON><PERSON><PERSON><PERSON> strieti", "block.minecraft.banner.small_stripes.magenta": "Ma<PERSON>a strieti", "block.minecraft.banner.small_stripes.orange": "<PERSON><PERSON><PERSON><PERSON> strieti", "block.minecraft.banner.small_stripes.pink": "R<PERSON>ea strieti", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON><PERSON> strieti", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON> strieti", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON><PERSON> strieti", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON> strieti", "block.minecraft.banner.square_bottom_left.black": "Nigra quadranto infradextra", "block.minecraft.banner.square_bottom_left.blue": "Blua quadranto infradextra", "block.minecraft.banner.square_bottom_left.brown": "Bruna quadranto infradextra", "block.minecraft.banner.square_bottom_left.cyan": "Ciana quadranto infradextra", "block.minecraft.banner.square_bottom_left.gray": "Griza quadranto infradextra", "block.minecraft.banner.square_bottom_left.green": "Verda quadranto infradextra", "block.minecraft.banner.square_bottom_left.light_blue": "Cielblua quadranto infradextra", "block.minecraft.banner.square_bottom_left.light_gray": "Klargriza quadranto infradextra", "block.minecraft.banner.square_bottom_left.lime": "Klarverda quadranto infradextra", "block.minecraft.banner.square_bottom_left.magenta": "Magenta quadranto infradextra", "block.minecraft.banner.square_bottom_left.orange": "Oranjea quadranto infradextra", "block.minecraft.banner.square_bottom_left.pink": "Rozea quadranto infradextra", "block.minecraft.banner.square_bottom_left.purple": "Purpura quadranto infradextra", "block.minecraft.banner.square_bottom_left.red": "Reda quadranto infradextra", "block.minecraft.banner.square_bottom_left.white": "Blanka quadranto infradextra", "block.minecraft.banner.square_bottom_left.yellow": "Flava quadranto infradextra", "block.minecraft.banner.square_bottom_right.black": "Nigra quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.blue": "Blua quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.brown": "Bruna quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.cyan": "Ciana quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.gray": "Griza quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.green": "Verda quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.light_blue": "Cielblua quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.light_gray": "Klargriza quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.lime": "Klarverda quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.magenta": "Magenta quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.orange": "Oreanjea quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.pink": "Rozea quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.purple": "Purpura quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.red": "Reda quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.white": "Blanka quadranto infrasinistra", "block.minecraft.banner.square_bottom_right.yellow": "Flava quadranto infrasinistra", "block.minecraft.banner.square_top_left.black": "Nigra quadranto supradextra", "block.minecraft.banner.square_top_left.blue": "Blua quadranto supradextra", "block.minecraft.banner.square_top_left.brown": "Bruna quadranto supradextra", "block.minecraft.banner.square_top_left.cyan": "Ciana quadranto supradextra", "block.minecraft.banner.square_top_left.gray": "Griza quadranto supradextra", "block.minecraft.banner.square_top_left.green": "Verda quadranto supradextra", "block.minecraft.banner.square_top_left.light_blue": "Cielblua quadranto supradextra", "block.minecraft.banner.square_top_left.light_gray": "Klargriza quadranto supradextra", "block.minecraft.banner.square_top_left.lime": "Klarverda quadranto supradextra", "block.minecraft.banner.square_top_left.magenta": "Magenta quadranto supradextra", "block.minecraft.banner.square_top_left.orange": "Oranjea quadranto supradextra", "block.minecraft.banner.square_top_left.pink": "Rozea quadranto supradextra", "block.minecraft.banner.square_top_left.purple": "Purpura quadranto supradextra", "block.minecraft.banner.square_top_left.red": "Reda quadranto supradextra", "block.minecraft.banner.square_top_left.white": "Blanka quadranto supradextra", "block.minecraft.banner.square_top_left.yellow": "Flava quadranto supradextra", "block.minecraft.banner.square_top_right.black": "Nigra quadranto suprasinistra", "block.minecraft.banner.square_top_right.blue": "Blua quadranto suprasinistra", "block.minecraft.banner.square_top_right.brown": "<PERSON>runa quadranto suprasinistra", "block.minecraft.banner.square_top_right.cyan": "Ciana quadranto suprasinistra", "block.minecraft.banner.square_top_right.gray": "Griza quadranto suprasinistra", "block.minecraft.banner.square_top_right.green": "Verda quadranto suprasinistra", "block.minecraft.banner.square_top_right.light_blue": "Cielblua quadranto suprasinistra", "block.minecraft.banner.square_top_right.light_gray": "Klargriza quadranto suprasinistra", "block.minecraft.banner.square_top_right.lime": "Klarverda quadranto suprasinistra", "block.minecraft.banner.square_top_right.magenta": "Magenta quadranto suprasinistra", "block.minecraft.banner.square_top_right.orange": "Oranjea quadranto suprasinistra", "block.minecraft.banner.square_top_right.pink": "Rozea quadranto suprasinistra", "block.minecraft.banner.square_top_right.purple": "Purpura quadranto suprasinistra", "block.minecraft.banner.square_top_right.red": "Reda quadranto suprasinistra", "block.minecraft.banner.square_top_right.white": "Blanka quadranto suprasinistra", "block.minecraft.banner.square_top_right.yellow": "Flava quadranto suprasinistra", "block.minecraft.banner.straight_cross.black": "Nigra kruco", "block.minecraft.banner.straight_cross.blue": "Blua kruco", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.gray": "Griza kruco", "block.minecraft.banner.straight_cross.green": "Verda kruco", "block.minecraft.banner.straight_cross.light_blue": "Cielblua kruco", "block.minecraft.banner.straight_cross.light_gray": "Klargriza kruco", "block.minecraft.banner.straight_cross.lime": "Klarverda kruco", "block.minecraft.banner.straight_cross.magenta": "Magenta kruco", "block.minecraft.banner.straight_cross.orange": "Oranjea kruco", "block.minecraft.banner.straight_cross.pink": "Rozea kruco", "block.minecraft.banner.straight_cross.purple": "Purpura kruco", "block.minecraft.banner.straight_cross.red": "Reda kruco", "block.minecraft.banner.straight_cross.white": "Blanka kruco", "block.minecraft.banner.straight_cross.yellow": "Flava kruco", "block.minecraft.banner.stripe_bottom.black": "Nigra bazo", "block.minecraft.banner.stripe_bottom.blue": "Blua bazo", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON><PERSON> bazo", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON> bazo", "block.minecraft.banner.stripe_bottom.gray": "Griza bazo", "block.minecraft.banner.stripe_bottom.green": "Verda bazo", "block.minecraft.banner.stripe_bottom.light_blue": "Cielblua bazo", "block.minecraft.banner.stripe_bottom.light_gray": "Klargriza bazo", "block.minecraft.banner.stripe_bottom.lime": "Klarverda bazo", "block.minecraft.banner.stripe_bottom.magenta": "Magenta bazo", "block.minecraft.banner.stripe_bottom.orange": "Oranjea bazo", "block.minecraft.banner.stripe_bottom.pink": "Rozea bazo", "block.minecraft.banner.stripe_bottom.purple": "Purpura bazo", "block.minecraft.banner.stripe_bottom.red": "Reda bazo", "block.minecraft.banner.stripe_bottom.white": "Blanka bazo", "block.minecraft.banner.stripe_bottom.yellow": "Flava bazo", "block.minecraft.banner.stripe_center.black": "Nigra strio centrala", "block.minecraft.banner.stripe_center.blue": "Blua strio centrala", "block.minecraft.banner.stripe_center.brown": "Bruna strio centrala", "block.minecraft.banner.stripe_center.cyan": "Cyana strio centrala", "block.minecraft.banner.stripe_center.gray": "Griza strio centrala", "block.minecraft.banner.stripe_center.green": "Verda strio centrala", "block.minecraft.banner.stripe_center.light_blue": "Cielblua strio centrala", "block.minecraft.banner.stripe_center.light_gray": "Klargriza strio centrala", "block.minecraft.banner.stripe_center.lime": "Klarverda strio centrala", "block.minecraft.banner.stripe_center.magenta": "Magenta strio centrala", "block.minecraft.banner.stripe_center.orange": "Oranjea strio centrala", "block.minecraft.banner.stripe_center.pink": "Rozea strio centrala", "block.minecraft.banner.stripe_center.purple": "Purpura strio centrala", "block.minecraft.banner.stripe_center.red": "Reda strio centrala", "block.minecraft.banner.stripe_center.white": "Blanka strio centrala", "block.minecraft.banner.stripe_center.yellow": "Flava strio centrala", "block.minecraft.banner.stripe_downleft.black": "Nigra diagonalo sinistra", "block.minecraft.banner.stripe_downleft.blue": "Blua diagonalo sinistra", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON><PERSON> diagonalo sinistra", "block.minecraft.banner.stripe_downleft.cyan": "Cyana diagonalo sinistra", "block.minecraft.banner.stripe_downleft.gray": "Griza diagonalo sinistra", "block.minecraft.banner.stripe_downleft.green": "<PERSON>erda diagonalo sinistra", "block.minecraft.banner.stripe_downleft.light_blue": "Cielblua diagonalo sinistra", "block.minecraft.banner.stripe_downleft.light_gray": "Klargriza diagonalo sinistra", "block.minecraft.banner.stripe_downleft.lime": "Klarverda diagonalo sinistra", "block.minecraft.banner.stripe_downleft.magenta": "Magenta diagonalo sinistra", "block.minecraft.banner.stripe_downleft.orange": "<PERSON><PERSON><PERSON><PERSON> diagonalo sinistra", "block.minecraft.banner.stripe_downleft.pink": "Rozea diagonalo sinistra", "block.minecraft.banner.stripe_downleft.purple": "Purpura diagonalo sinistra", "block.minecraft.banner.stripe_downleft.red": "Reda diagonalo sinistra", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON><PERSON> diagonalo sinistra", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON> diagonalo sinistra", "block.minecraft.banner.stripe_downright.black": "Nigra diagonalo dextra", "block.minecraft.banner.stripe_downright.blue": "Blua diagonalo dextra", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON><PERSON>o dextra", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON>o dextra", "block.minecraft.banner.stripe_downright.gray": "Griza diagonalo dextra", "block.minecraft.banner.stripe_downright.green": "Verda diagonalo dextra", "block.minecraft.banner.stripe_downright.light_blue": "Cielblua diagonalo dextra", "block.minecraft.banner.stripe_downright.light_gray": "Klargriza diagonalo dextra", "block.minecraft.banner.stripe_downright.lime": "Klarverda diagonalo dextra", "block.minecraft.banner.stripe_downright.magenta": "Magenta diagonalo dextra", "block.minecraft.banner.stripe_downright.orange": "<PERSON><PERSON><PERSON><PERSON> diagonalo dextra", "block.minecraft.banner.stripe_downright.pink": "Rozea diagonalo dextra", "block.minecraft.banner.stripe_downright.purple": "Purpura diagonalo dextra", "block.minecraft.banner.stripe_downright.red": "Reda diagonalo dextra", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON> diagonalo dextra", "block.minecraft.banner.stripe_downright.yellow": "<PERSON><PERSON> diagonalo dextra", "block.minecraft.banner.stripe_left.black": "Nigra strio dextra", "block.minecraft.banner.stripe_left.blue": "Blua strio dextra", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON><PERSON> strio dextra", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON> strio dextra", "block.minecraft.banner.stripe_left.gray": "Griza strio dextra", "block.minecraft.banner.stripe_left.green": "Verda strio dextra", "block.minecraft.banner.stripe_left.light_blue": "Cielblua strio dextra", "block.minecraft.banner.stripe_left.light_gray": "Klargriza strio dextra", "block.minecraft.banner.stripe_left.lime": "Klarverda strio dextra", "block.minecraft.banner.stripe_left.magenta": "Magenta strio dextra", "block.minecraft.banner.stripe_left.orange": "Oranjea strio dextra", "block.minecraft.banner.stripe_left.pink": "Rozea strio dextra", "block.minecraft.banner.stripe_left.purple": "Purpura strio dextra", "block.minecraft.banner.stripe_left.red": "Reda strio dextra", "block.minecraft.banner.stripe_left.white": "<PERSON><PERSON><PERSON> strio dextra", "block.minecraft.banner.stripe_left.yellow": "Flava strio dextra", "block.minecraft.banner.stripe_middle.black": "Nigra stango", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON> stango", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON> stango", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON> stango", "block.minecraft.banner.stripe_middle.gray": "Griza stango", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON> stango", "block.minecraft.banner.stripe_middle.light_blue": "Ciel<PERSON><PERSON>a stango", "block.minecraft.banner.stripe_middle.light_gray": "Klargriza stango", "block.minecraft.banner.stripe_middle.lime": "Klar<PERSON><PERSON> stango", "block.minecraft.banner.stripe_middle.magenta": "Magenta stango", "block.minecraft.banner.stripe_middle.orange": "Or<PERSON><PERSON>a stango", "block.minecraft.banner.stripe_middle.pink": "Rozea stango", "block.minecraft.banner.stripe_middle.purple": "Purpura stango", "block.minecraft.banner.stripe_middle.red": "<PERSON>a stango", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON> stango", "block.minecraft.banner.stripe_middle.yellow": "Flava stango", "block.minecraft.banner.stripe_right.black": "Nigra strio sinistra", "block.minecraft.banner.stripe_right.blue": "Blua strio sinistra", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON><PERSON> strio sinistra", "block.minecraft.banner.stripe_right.cyan": "Cyana strio sinistra", "block.minecraft.banner.stripe_right.gray": "Griza strio sinistra", "block.minecraft.banner.stripe_right.green": "Verda strio sinistra", "block.minecraft.banner.stripe_right.light_blue": "Cielblua strio sinistra", "block.minecraft.banner.stripe_right.light_gray": "Klargriza strio sinistra", "block.minecraft.banner.stripe_right.lime": "Klarverda strio sinistra", "block.minecraft.banner.stripe_right.magenta": "Magenta strio sinistra", "block.minecraft.banner.stripe_right.orange": "Oranjea strio sinistra", "block.minecraft.banner.stripe_right.pink": "Rozea strio sinistra", "block.minecraft.banner.stripe_right.purple": "Purpura strio sinistra", "block.minecraft.banner.stripe_right.red": "Reda strio sinistra", "block.minecraft.banner.stripe_right.white": "<PERSON><PERSON><PERSON> strio sinistra", "block.minecraft.banner.stripe_right.yellow": "Flava strio sinistra", "block.minecraft.banner.stripe_top.black": "<PERSON>gra chefo", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON> chefo", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON><PERSON>o", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.gray": "Griza chefo", "block.minecraft.banner.stripe_top.green": "Verda chefo", "block.minecraft.banner.stripe_top.light_blue": "Cielblua chefo", "block.minecraft.banner.stripe_top.light_gray": "Klargriza chefo", "block.minecraft.banner.stripe_top.lime": "Klarverda chefo", "block.minecraft.banner.stripe_top.magenta": "Magenta chefo", "block.minecraft.banner.stripe_top.orange": "Or<PERSON><PERSON>a chefo", "block.minecraft.banner.stripe_top.pink": "Rozea chefo", "block.minecraft.banner.stripe_top.purple": "Purpura chefo", "block.minecraft.banner.stripe_top.red": "Reda chefo", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON> chefo", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON> chefo", "block.minecraft.banner.triangle_bottom.black": "Nigra chevrono infra", "block.minecraft.banner.triangle_bottom.blue": "Blua chevrono infra", "block.minecraft.banner.triangle_bottom.brown": "<PERSON>runa chevrono infra", "block.minecraft.banner.triangle_bottom.cyan": "Cyana chevrono infra", "block.minecraft.banner.triangle_bottom.gray": "Griza chevrono infra", "block.minecraft.banner.triangle_bottom.green": "Verda chevrono infra", "block.minecraft.banner.triangle_bottom.light_blue": "Cielblua chevrono infra", "block.minecraft.banner.triangle_bottom.light_gray": "Klargriza chevrono infra", "block.minecraft.banner.triangle_bottom.lime": "Klarverda chevrono infra", "block.minecraft.banner.triangle_bottom.magenta": "Magenta chevrono infra", "block.minecraft.banner.triangle_bottom.orange": "Oranjea chevrono infra", "block.minecraft.banner.triangle_bottom.pink": "Rozea chevrono infra", "block.minecraft.banner.triangle_bottom.purple": "Purpura chevrono infra", "block.minecraft.banner.triangle_bottom.red": "Reda chevrono infra", "block.minecraft.banner.triangle_bottom.white": "Blanka chevrono infra", "block.minecraft.banner.triangle_bottom.yellow": "Flava chevrono infra", "block.minecraft.banner.triangle_top.black": "Nigra chevrono supra", "block.minecraft.banner.triangle_top.blue": "<PERSON>a chevrono supra", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON><PERSON> chevrono supra", "block.minecraft.banner.triangle_top.cyan": "<PERSON><PERSON> chevrono supra", "block.minecraft.banner.triangle_top.gray": "Griza chevrono supra", "block.minecraft.banner.triangle_top.green": "Verda chevrono supra", "block.minecraft.banner.triangle_top.light_blue": "Cielblua chevrono supra", "block.minecraft.banner.triangle_top.light_gray": "Klargriza chevrono supra", "block.minecraft.banner.triangle_top.lime": "Klarverda chevrono supra", "block.minecraft.banner.triangle_top.magenta": "Magenta chevrono supra", "block.minecraft.banner.triangle_top.orange": "Oranjea chevrono supra", "block.minecraft.banner.triangle_top.pink": "Rozea chevrono supra", "block.minecraft.banner.triangle_top.purple": "Purpura chevrono supra", "block.minecraft.banner.triangle_top.red": "Reda chevrono supra", "block.minecraft.banner.triangle_top.white": "<PERSON><PERSON><PERSON> chevrono supra", "block.minecraft.banner.triangle_top.yellow": "<PERSON>lava chevrono supra", "block.minecraft.banner.triangles_bottom.black": "Nigra denti infra", "block.minecraft.banner.triangles_bottom.blue": "<PERSON>a denti infra", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON><PERSON> denti infra", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON> denti infra", "block.minecraft.banner.triangles_bottom.gray": "Griza denti infra", "block.minecraft.banner.triangles_bottom.green": "Verda denti infra", "block.minecraft.banner.triangles_bottom.light_blue": "Ciel<PERSON><PERSON><PERSON> denti infra", "block.minecraft.banner.triangles_bottom.light_gray": "Klargriza denti infra", "block.minecraft.banner.triangles_bottom.lime": "Klarver<PERSON> denti infra", "block.minecraft.banner.triangles_bottom.magenta": "Magenta denti infra", "block.minecraft.banner.triangles_bottom.orange": "<PERSON><PERSON><PERSON><PERSON> denti infra", "block.minecraft.banner.triangles_bottom.pink": "Rozea denti infra", "block.minecraft.banner.triangles_bottom.purple": "Purpura denti infra", "block.minecraft.banner.triangles_bottom.red": "Reda denti infra", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON><PERSON> denti infra", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON> denti infra", "block.minecraft.banner.triangles_top.black": "Nigra denti supra", "block.minecraft.banner.triangles_top.blue": "<PERSON>a denti supra", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON> denti supra", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON> denti supra", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON> denti supra", "block.minecraft.banner.triangles_top.green": "Verda denti supra", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> denti supra", "block.minecraft.banner.triangles_top.light_gray": "Klarg<PERSON><PERSON> denti supra", "block.minecraft.banner.triangles_top.lime": "<PERSON><PERSON><PERSON><PERSON> denti supra", "block.minecraft.banner.triangles_top.magenta": "Magenta denti supra", "block.minecraft.banner.triangles_top.orange": "<PERSON><PERSON><PERSON><PERSON> denti supra", "block.minecraft.banner.triangles_top.pink": "<PERSON>ozea denti supra", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON><PERSON> denti supra", "block.minecraft.banner.triangles_top.red": "<PERSON>a denti supra", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON> denti supra", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON> denti supra", "block.minecraft.barrel": "<PERSON><PERSON>", "block.minecraft.barrier": "<PERSON><PERSON>", "block.minecraft.basalt": "Bazalto", "block.minecraft.beacon": "Bali<PERSON>", "block.minecraft.beacon.primary": "Povo prima", "block.minecraft.beacon.secondary": "<PERSON><PERSON> sekundara", "block.minecraft.bed.no_sleep": "Vu povas nur dormar ye nokto o dum sturmi", "block.minecraft.bed.not_safe": "Vu ne povas restar nun; monsteri esas proxima", "block.minecraft.bed.obstructed": "Ica lito esas inkombrata", "block.minecraft.bed.occupied": "Ica lito esas okupata", "block.minecraft.bed.too_far_away": "<PERSON>u ne darfas restar nun; la lito esas tro distanta", "block.minecraft.bedrock": "Bazpetro", "block.minecraft.bee_nest": "<PERSON><PERSON><PERSON>", "block.minecraft.beehive": "<PERSON><PERSON><PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON>", "block.minecraft.bell": "<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "<PERSON><PERSON><PERSON><PERSON>a", "block.minecraft.big_dripleaf_stem": "<PERSON><PERSON><PERSON> di gutofolio granda", "block.minecraft.birch_button": "<PERSON><PERSON> birka", "block.minecraft.birch_door": "<PERSON>rdo birka", "block.minecraft.birch_fence": "Fenco birka", "block.minecraft.birch_fence_gate": "Pordeto birka", "block.minecraft.birch_hanging_sign": "Pendanta signo birka", "block.minecraft.birch_leaves": "Folii birka", "block.minecraft.birch_log": "<PERSON><PERSON>", "block.minecraft.birch_planks": "Plan<PERSON> birka", "block.minecraft.birch_pressure_plate": "Presoplako birka", "block.minecraft.birch_sapling": "Arboreto birka", "block.minecraft.birch_sign": "Signo birka", "block.minecraft.birch_slab": "Plako birka", "block.minecraft.birch_stairs": "Eskalero birka", "block.minecraft.birch_trapdoor": "Trapo birka", "block.minecraft.birch_wall_hanging_sign": "Murala pendanta signo birka", "block.minecraft.birch_wall_sign": "Murala signo birka", "block.minecraft.birch_wood": "Ligno birka", "block.minecraft.black_banner": "<PERSON><PERSON> banero", "block.minecraft.black_bed": "Nigra lito", "block.minecraft.black_candle": "<PERSON><PERSON> kandelo", "block.minecraft.black_candle_cake": "<PERSON>ko kun nigra kandelo", "block.minecraft.black_carpet": "<PERSON>gra tapiso", "block.minecraft.black_concrete": "<PERSON>gra betono", "block.minecraft.black_concrete_powder": "<PERSON><PERSON> bet<PERSON>", "block.minecraft.black_glazed_terracotta": "Nigra terakoto glezita", "block.minecraft.black_shulker_box": "Nigra buxo di Shulkero", "block.minecraft.black_stained_glass": "Nigra vitro tintita", "block.minecraft.black_stained_glass_pane": "Nigra vitrokarelo tintita", "block.minecraft.black_terracotta": "<PERSON><PERSON> te<PERSON>", "block.minecraft.black_wool": "<PERSON>gra lano", "block.minecraft.blackstone": "Nigrapet<PERSON>", "block.minecraft.blackstone_slab": "Plako di Nigrapetro", "block.minecraft.blackstone_stairs": "Eskalero di nigrapetro", "block.minecraft.blackstone_wall": "<PERSON>ro di nigrapetro", "block.minecraft.blast_furnace": "Fornego", "block.minecraft.blue_banner": "<PERSON><PERSON> banero", "block.minecraft.blue_bed": "Blua lito", "block.minecraft.blue_candle": "<PERSON><PERSON> kande<PERSON>", "block.minecraft.blue_candle_cake": "<PERSON><PERSON> kun blua kandelo", "block.minecraft.blue_carpet": "<PERSON><PERSON> tapiso", "block.minecraft.blue_concrete": "<PERSON><PERSON> betono", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON> bet<PERSON>", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON> te<PERSON> glezita", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON><PERSON><PERSON> blua", "block.minecraft.blue_shulker_box": "Blua buxo di Shulkero", "block.minecraft.blue_stained_glass": "Blua vitro tintita", "block.minecraft.blue_stained_glass_pane": "Blua vitrokarelo tintita", "block.minecraft.blue_terracotta": "<PERSON><PERSON>", "block.minecraft.blue_wool": "<PERSON><PERSON> lano", "block.minecraft.bone_block": "Bloko di osto", "block.minecraft.bookshelf": "Biblioteko", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.brain_coral_block": "Bloko di cerebrala koralio", "block.minecraft.brain_coral_fan": "Fano di cerebrala koralio", "block.minecraft.brain_coral_wall_fan": "Murala fano di cerebrala koralio", "block.minecraft.brewing_stand": "Brasostativo", "block.minecraft.brick_slab": "<PERSON><PERSON><PERSON> di briko", "block.minecraft.brick_stairs": "Eskalero di briko", "block.minecraft.brick_wall": "Muro di briki", "block.minecraft.bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON> banero", "block.minecraft.brown_bed": "<PERSON><PERSON><PERSON> lito", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.brown_candle_cake": "<PERSON><PERSON> kun bruna kandelo", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON> tapiso", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON> betono", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON><PERSON> gle<PERSON>ta", "block.minecraft.brown_mushroom": "Fungo bruna", "block.minecraft.brown_mushroom_block": "Bloko di fungo bruna", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON> buxo <PERSON> Shulkero", "block.minecraft.brown_stained_glass": "<PERSON>runa vitro tintita", "block.minecraft.brown_stained_glass_pane": "<PERSON><PERSON><PERSON> vit<PERSON><PERSON> tintita", "block.minecraft.brown_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_column": "Kolono di spumo", "block.minecraft.bubble_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_block": "Bloko di buleta koralio", "block.minecraft.bubble_coral_fan": "Fano di buleta koralio", "block.minecraft.bubble_coral_wall_fan": "Murala fano di buleta koralio", "block.minecraft.budding_amethyst": "Ametisto burjonif<PERSON>a", "block.minecraft.bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Cactus Flower", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "<PERSON><PERSON><PERSON><PERSON>-sensoro", "block.minecraft.campfire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle": "<PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "<PERSON><PERSON> kun kandelo", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Tablo di kartografio", "block.minecraft.carved_pumpkin": "Skultata kukurb<PERSON>", "block.minecraft.cauldron": "Kaldrono", "block.minecraft.cave_air": "Aero kavernala", "block.minecraft.cave_vines": "Kavkreskoj", "block.minecraft.cave_vines_plant": "Kavkreska planto", "block.minecraft.chain": "<PERSON><PERSON>", "block.minecraft.chain_command_block": "Bloko di komandi katenita", "block.minecraft.cherry_button": "Ĉeriza butono", "block.minecraft.cherry_door": "Ĉeriza pordo", "block.minecraft.cherry_fence": "Ĉeriza barilo", "block.minecraft.cherry_fence_gate": "Ĉeriza pordbarilo", "block.minecraft.cherry_hanging_sign": "Ĉeriza pendanta signo", "block.minecraft.cherry_leaves": "Ĉerizaj folioj", "block.minecraft.cherry_log": "Ĉeriza ligno", "block.minecraft.cherry_planks": "Ĉerizaj tabuloj", "block.minecraft.cherry_pressure_plate": "Ĉeriza prem-plato", "block.minecraft.cherry_sapling": "Ĉeriza plantido", "block.minecraft.cherry_sign": "Ĉeriza signo", "block.minecraft.cherry_slab": "Ĉeriza tavolo", "block.minecraft.cherry_stairs": "Kirsan Ŝtuparoj", "block.minecraft.cherry_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_wall_hanging_sign": "Tabulo sur muro kun bildeto de kirsoj", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.chest": "Kof<PERSON>", "block.minecraft.chipped_anvil": "Amboso ruptetita", "block.minecraft.chiseled_bookshelf": "Biblioteko c<PERSON>", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_deepslate": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.chiseled_nether_bricks": "Netherbriki cizelita", "block.minecraft.chiseled_polished_blackstone": "Polisita nigrapetro cizelita", "block.minecraft.chiseled_quartz_block": "Bloko di quarco cizelita", "block.minecraft.chiseled_red_sandstone": "Reda greso cizelita", "block.minecraft.chiseled_resin_bricks": "Chiseled Resin Bricks", "block.minecraft.chiseled_sandstone": "Greso cizelita", "block.minecraft.chiseled_stone_bricks": "Petrabriki cizelita", "block.minecraft.chiseled_tuff": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_tuff_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chorus_flower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chorus_plant": "Chorusplanto", "block.minecraft.clay": "<PERSON><PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Closed Eyeblossom", "block.minecraft.coal_block": "Bloko di karbono", "block.minecraft.coal_ore": "<PERSON><PERSON>o di karbono", "block.minecraft.coarse_dirt": "Sulo grosiera", "block.minecraft.cobbled_deepslate": "Profundardezo <PERSON>", "block.minecraft.cobbled_deepslate_slab": "Plako di profundardezo eskombra", "block.minecraft.cobbled_deepslate_stairs": "Eskalero di profundardezo eskombra", "block.minecraft.cobbled_deepslate_wall": "Muro di profundardezo eskombra", "block.minecraft.cobblestone": "Eskombro", "block.minecraft.cobblestone_slab": "Plako di eskombro", "block.minecraft.cobblestone_stairs": "Eskalero di eskombro", "block.minecraft.cobblestone_wall": "Muro di eskombro", "block.minecraft.cobweb": "<PERSON><PERSON><PERSON>", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Bloko di komandi", "block.minecraft.comparator": "Komparilo di redstoneo", "block.minecraft.composter": "<PERSON><PERSON><PERSON>", "block.minecraft.conduit": "Povoduktilo", "block.minecraft.copper_block": "Bloko di kupro", "block.minecraft.copper_bulb": "<PERSON><PERSON>", "block.minecraft.copper_door": "<PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON>", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON> di <PERSON>upro", "block.minecraft.copper_trapdoor": "<PERSON><PERSON>", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "<PERSON><PERSON>i profunda<PERSON>za krakita", "block.minecraft.cracked_deepslate_tiles": "Teguli profundardeza krakita", "block.minecraft.cracked_nether_bricks": "Netherbriki krakita", "block.minecraft.cracked_polished_blackstone_bricks": "Polisita briki nigrapetra krakita", "block.minecraft.cracked_stone_bricks": "Petrabriko krak<PERSON>", "block.minecraft.crafter": "<PERSON><PERSON><PERSON>", "block.minecraft.crafting_table": "Tablo di fabrikado", "block.minecraft.creaking_heart": "Creaking Heart", "block.minecraft.creeper_head": "<PERSON><PERSON>", "block.minecraft.creeper_wall_head": "<PERSON><PERSON><PERSON> ka<PERSON> di <PERSON>", "block.minecraft.crimson_button": "<PERSON><PERSON> karmezina", "block.minecraft.crimson_door": "<PERSON><PERSON> ka<PERSON>", "block.minecraft.crimson_fence": "Fenco karmez<PERSON>", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.crimson_fungus": "Fungo karmezina", "block.minecraft.crimson_hanging_sign": "Pendanta signo karmezina", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_planks": "<PERSON><PERSON> ka<PERSON>", "block.minecraft.crimson_pressure_plate": "Presoplako ka<PERSON>", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_sign": "<PERSON><PERSON>", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_stairs": "Eskalero karmezina", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_wall_hanging_sign": "Murala pendanta signo karmezina", "block.minecraft.crimson_wall_sign": "<PERSON>rala signo karmez<PERSON>", "block.minecraft.crying_obsidian": "Obsidiano ploranta", "block.minecraft.cut_copper": "<PERSON><PERSON><PERSON>ita", "block.minecraft.cut_copper_slab": "<PERSON><PERSON><PERSON> kupra tailita", "block.minecraft.cut_copper_stairs": "Eskalero kupra tailita", "block.minecraft.cut_red_sandstone": "Reda greso tailita", "block.minecraft.cut_red_sandstone_slab": "Plako di reda greso tailita", "block.minecraft.cut_sandstone": "Greso tailita", "block.minecraft.cut_sandstone_slab": "Plako di greso tailita", "block.minecraft.cyan_banner": "<PERSON><PERSON>", "block.minecraft.cyan_bed": "<PERSON>ana lito", "block.minecraft.cyan_candle": "<PERSON><PERSON>", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON> kun cyana kandelo", "block.minecraft.cyan_carpet": "<PERSON><PERSON> tap<PERSON>o", "block.minecraft.cyan_concrete": "<PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON>", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON> g<PERSON>", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON> buxo <PERSON>lkero", "block.minecraft.cyan_stained_glass": "Cyana vitro tintita", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON> vit<PERSON> tintita", "block.minecraft.cyan_terracotta": "<PERSON><PERSON>", "block.minecraft.cyan_wool": "<PERSON><PERSON>", "block.minecraft.damaged_anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.dandelion": "Leondento", "block.minecraft.dark_oak_button": "<PERSON><PERSON> obs<PERSON>rka", "block.minecraft.dark_oak_door": "<PERSON><PERSON> o<PERSON>", "block.minecraft.dark_oak_fence": "Fenco obskurquerka", "block.minecraft.dark_oak_fence_gate": "Pordeto obskurquerka", "block.minecraft.dark_oak_hanging_sign": "Pendanta signo obs<PERSON>rquerka", "block.minecraft.dark_oak_leaves": "Folii obskurquerka", "block.minecraft.dark_oak_log": "<PERSON><PERSON>", "block.minecraft.dark_oak_planks": "<PERSON><PERSON> o<PERSON>", "block.minecraft.dark_oak_pressure_plate": "Presoplako obskurquerka", "block.minecraft.dark_oak_sapling": "Aboreto obskurquerka", "block.minecraft.dark_oak_sign": "<PERSON><PERSON>", "block.minecraft.dark_oak_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_stairs": "Eskalero obskurquerka", "block.minecraft.dark_oak_trapdoor": "Trapo o<PERSON>rka", "block.minecraft.dark_oak_wall_hanging_sign": "Murala pendanta signo obskurquerka", "block.minecraft.dark_oak_wall_sign": "Murala signo obs<PERSON>rka", "block.minecraft.dark_oak_wood": "Ligno obskurquerka", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.dark_prismarine_slab": "Plako di prismarino obskura", "block.minecraft.dark_prismarine_stairs": "Eskalero di prismarino obskura", "block.minecraft.daylight_detector": "Detektanto di dilumo", "block.minecraft.dead_brain_coral": "Cere<PERSON>la koralio mortinta", "block.minecraft.dead_brain_coral_block": "Bloko di cerebrala koralio mortinta", "block.minecraft.dead_brain_coral_fan": "Fano di cerebrala koralio mortinta", "block.minecraft.dead_brain_coral_wall_fan": "Murala fano di cerebrala koralio mortinta", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON><PERSON> koralio mortinta", "block.minecraft.dead_bubble_coral_block": "Bloko di buleta koralio mortinta", "block.minecraft.dead_bubble_coral_fan": "Fano di buleta koralio mortinta", "block.minecraft.dead_bubble_coral_wall_fan": "Murala fano di buleta koralio mortinta", "block.minecraft.dead_bush": "<PERSON><PERSON><PERSON>o mortinta", "block.minecraft.dead_fire_coral": "Fairala koralio mortinta", "block.minecraft.dead_fire_coral_block": "Bloko di fairala koralio mortinta", "block.minecraft.dead_fire_coral_fan": "Fano di fairala koralio mortinta", "block.minecraft.dead_fire_coral_wall_fan": "Murala fano di fairala koralio mortinta", "block.minecraft.dead_horn_coral": "Korna koralio mortinta", "block.minecraft.dead_horn_coral_block": "Bloko di korna koralio mortinta", "block.minecraft.dead_horn_coral_fan": "Fano di korna koralio mortinta", "block.minecraft.dead_horn_coral_wall_fan": "Murala fano di korna koralio mortinta", "block.minecraft.dead_tube_coral": "Pipeta koralio mortinta", "block.minecraft.dead_tube_coral_block": "Bloko di pipeta koralio mortinta", "block.minecraft.dead_tube_coral_fan": "Fano di pipeta koralio mortinta", "block.minecraft.dead_tube_coral_wall_fan": "Murala fano di pipeta koralio mortinta", "block.minecraft.decorated_pot": "Ornamentita Poto", "block.minecraft.deepslate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_brick_slab": "Plako di briko profundardeza", "block.minecraft.deepslate_brick_stairs": "Eskalero di briko profundardeza", "block.minecraft.deepslate_brick_wall": "<PERSON>ro di briko profundardeza", "block.minecraft.deepslate_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_coal_ore": "Erco profunda<PERSON>za di karbono", "block.minecraft.deepslate_copper_ore": "Erco profunda<PERSON>za di kupro", "block.minecraft.deepslate_diamond_ore": "<PERSON>rco profu<PERSON> di di<PERSON>to", "block.minecraft.deepslate_emerald_ore": "<PERSON><PERSON><PERSON> pro<PERSON> di s<PERSON>aldo", "block.minecraft.deepslate_gold_ore": "<PERSON>rco profunda<PERSON> di oro", "block.minecraft.deepslate_iron_ore": "<PERSON><PERSON>o profunda<PERSON> di fero", "block.minecraft.deepslate_lapis_ore": "<PERSON>rco profu<PERSON> di lapislazulo", "block.minecraft.deepslate_redstone_ore": "Erco profundardeza di redstoneo", "block.minecraft.deepslate_tile_slab": "Plako di tegulo profundardeza", "block.minecraft.deepslate_tile_stairs": "Eskalero di tegulo profundardeza", "block.minecraft.deepslate_tile_wall": "Muro di tegulo profundardeza", "block.minecraft.deepslate_tiles": "Teguli profundardeza", "block.minecraft.detector_rail": "<PERSON><PERSON> di detekteso", "block.minecraft.diamond_block": "Bloko di diamanto", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_stairs": "Eskalero diorita", "block.minecraft.diorite_wall": "<PERSON><PERSON>", "block.minecraft.dirt": "Sulo", "block.minecraft.dirt_path": "Voyo di sulo", "block.minecraft.dispenser": "Lansilo", "block.minecraft.dragon_egg": "Ovo di drako", "block.minecraft.dragon_head": "<PERSON><PERSON>o", "block.minecraft.dragon_wall_head": "<PERSON><PERSON><PERSON> kapo di drako", "block.minecraft.dried_ghast": "<PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Bloko di algo sika", "block.minecraft.dripstone_block": "Bloko di gutopetro", "block.minecraft.dropper": "Distributilo", "block.minecraft.emerald_block": "Bloko di smeraldo", "block.minecraft.emerald_ore": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.enchanting_table": "Sorcotablo", "block.minecraft.end_gateway": "Pordeyo di End", "block.minecraft.end_portal": "Portalo di End", "block.minecraft.end_portal_frame": "Portalframo di End", "block.minecraft.end_rod": "Bastono di End", "block.minecraft.end_stone": "Petro di End", "block.minecraft.end_stone_brick_slab": "Plako di petrabriki di End", "block.minecraft.end_stone_brick_stairs": "Eskalero di petrabriki di End", "block.minecraft.end_stone_brick_wall": "Muro di petrabriki di End", "block.minecraft.end_stone_bricks": "Petrabriki di End", "block.minecraft.ender_chest": "<PERSON><PERSON>kof<PERSON>", "block.minecraft.exposed_chiseled_copper": "Esponita <PERSON>", "block.minecraft.exposed_copper": "Kupro expozita", "block.minecraft.exposed_copper_bulb": "Esponita <PERSON>", "block.minecraft.exposed_copper_door": "Esponi<PERSON>", "block.minecraft.exposed_copper_grate": "Esponi<PERSON>", "block.minecraft.exposed_copper_trapdoor": "Esponita <PERSON>", "block.minecraft.exposed_cut_copper": "Kupro expozita tailita", "block.minecraft.exposed_cut_copper_slab": "Plako kupra expozita tailita", "block.minecraft.exposed_cut_copper_stairs": "Eskalero kupra expozita tailita", "block.minecraft.farmland": "Farmsulo", "block.minecraft.fern": "<PERSON>lik<PERSON>", "block.minecraft.fire": "Fair<PERSON>", "block.minecraft.fire_coral": "Fairala koralio", "block.minecraft.fire_coral_block": "Bloko di fairala koralio", "block.minecraft.fire_coral_fan": "Fano di fairala koralio", "block.minecraft.fire_coral_wall_fan": "Murala fano di fairala koralio", "block.minecraft.firefly_bush": "Firefly Bush", "block.minecraft.fletching_table": "Tablo di flechado", "block.minecraft.flower_pot": "Florpoto", "block.minecraft.flowering_azalea": "Florifanta azaleo", "block.minecraft.flowering_azalea_leaves": "Florifanta folii azalea", "block.minecraft.frogspawn": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.frosted_ice": "<PERSON><PERSON><PERSON>", "block.minecraft.furnace": "Fu<PERSON><PERSON>", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.glass": "Vitro", "block.minecraft.glass_pane": "Vit<PERSON>arel<PERSON>", "block.minecraft.glow_lichen": "Lumlikeno", "block.minecraft.glowstone": "Lumpetro", "block.minecraft.gold_block": "Bloko di oro", "block.minecraft.gold_ore": "<PERSON><PERSON>o di oro", "block.minecraft.granite": "<PERSON><PERSON>", "block.minecraft.granite_slab": "Plako granita", "block.minecraft.granite_stairs": "Eskalero granita", "block.minecraft.granite_wall": "<PERSON>ro granita", "block.minecraft.grass": "<PERSON><PERSON>", "block.minecraft.grass_block": "Bloko di gazono", "block.minecraft.gravel": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON> banero", "block.minecraft.gray_bed": "Griza lito", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON> kandelo", "block.minecraft.gray_candle_cake": "<PERSON><PERSON> kun griza kandelo", "block.minecraft.gray_carpet": "Griza tapiso", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON> betono", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_glazed_terracotta": "<PERSON>riza te<PERSON> glezita", "block.minecraft.gray_shulker_box": "Griza buxo di Shulkero", "block.minecraft.gray_stained_glass": "Griza vitro tintita", "block.minecraft.gray_stained_glass_pane": "Griza vitrokarelo tintita", "block.minecraft.gray_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_wool": "Griza lano", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON> banero", "block.minecraft.green_bed": "Verda lito", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON> kande<PERSON>", "block.minecraft.green_candle_cake": "<PERSON><PERSON> kun verda kandelo", "block.minecraft.green_carpet": "<PERSON><PERSON>a tapiso", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON> betono", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON><PERSON> te<PERSON> gle<PERSON>ta", "block.minecraft.green_shulker_box": "Verda buxo di Shulkero", "block.minecraft.green_stained_glass": "Verda vitro tintita", "block.minecraft.green_stained_glass_pane": "Verda vitrokarelo tintita", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON> lano", "block.minecraft.grindstone": "Grindstono", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.hay_block": "Bloko di feno", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON>", "block.minecraft.heavy_weighted_pressure_plate": "Grava presoplako pezanta", "block.minecraft.honey_block": "Bloko di mielo", "block.minecraft.honeycomb_block": "Bloko di vabo", "block.minecraft.hopper": "Funelego", "block.minecraft.horn_coral": "<PERSON><PERSON> k<PERSON>", "block.minecraft.horn_coral_block": "Bloko di korna koralio", "block.minecraft.horn_coral_fan": "Fano di korna koralio", "block.minecraft.horn_coral_wall_fan": "Murala fano di korna koralio", "block.minecraft.ice": "Glacio", "block.minecraft.infested_chiseled_stone_bricks": "Infestita petrabriki cizelita", "block.minecraft.infested_cobblestone": "Infestita eskombro", "block.minecraft.infested_cracked_stone_bricks": "Infestita petrabriki krakita", "block.minecraft.infested_deepslate": "Infestita profundardezo", "block.minecraft.infested_mossy_stone_bricks": "Infestita petrabriki muskoza", "block.minecraft.infested_stone": "Infestita petro", "block.minecraft.infested_stone_bricks": "Infestita petrabriki", "block.minecraft.iron_bars": "Greto fera", "block.minecraft.iron_block": "Bloko di fero", "block.minecraft.iron_door": "<PERSON><PERSON> fera", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON> di fero", "block.minecraft.iron_trapdoor": "Trapo fera", "block.minecraft.jack_o_lantern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jigsaw": "Konstruktbloko", "block.minecraft.jukebox": "Muzikbuxo", "block.minecraft.jungle_button": "Butono mahagoniera", "block.minecraft.jungle_door": "<PERSON><PERSON> ma<PERSON>", "block.minecraft.jungle_fence": "Fenco mahagoniera", "block.minecraft.jungle_fence_gate": "Pordeto maha<PERSON>", "block.minecraft.jungle_hanging_sign": "Pendanta signo ma<PERSON>", "block.minecraft.jungle_leaves": "Folii ma<PERSON>", "block.minecraft.jungle_log": "<PERSON><PERSON>", "block.minecraft.jungle_planks": "Plan<PERSON> ma<PERSON>", "block.minecraft.jungle_pressure_plate": "Presoplako mahagoniera", "block.minecraft.jungle_sapling": "Arboreto mahagoniera", "block.minecraft.jungle_sign": "Signo ma<PERSON>", "block.minecraft.jungle_slab": "Plako ma<PERSON>", "block.minecraft.jungle_stairs": "Eskalero mahagoniera", "block.minecraft.jungle_trapdoor": "Trapo mahagoniera", "block.minecraft.jungle_wall_hanging_sign": "Murala pendanta signo maha<PERSON>", "block.minecraft.jungle_wall_sign": "Murala signo ma<PERSON>", "block.minecraft.jungle_wood": "Ligno mahagoniera", "block.minecraft.kelp": "Algo", "block.minecraft.kelp_plant": "Planto di algo", "block.minecraft.ladder": "Skal<PERSON>", "block.minecraft.lantern": "<PERSON><PERSON>", "block.minecraft.lapis_block": "Bloko di lapislazulo", "block.minecraft.lapis_ore": "<PERSON><PERSON><PERSON> <PERSON> lap<PERSON>", "block.minecraft.large_amethyst_bud": "<PERSON><PERSON> bur<PERSON>o ametista", "block.minecraft.large_fern": "Filikego", "block.minecraft.lava": "Lavao", "block.minecraft.lava_cauldron": "Kaldrono di lavao", "block.minecraft.leaf_litter": "Leaf Litter", "block.minecraft.lectern": "Libropupitro", "block.minecraft.lever": "Lev<PERSON>", "block.minecraft.light": "<PERSON><PERSON>", "block.minecraft.light_blue_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON> banero", "block.minecraft.light_blue_bed": "Cielblua lito", "block.minecraft.light_blue_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON> kandelo", "block.minecraft.light_blue_candle_cake": "<PERSON><PERSON> kun cielb<PERSON>a kandelo", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON> tapiso", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON> betono", "block.minecraft.light_blue_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON> bet<PERSON>", "block.minecraft.light_blue_glazed_terracotta": "Cielblua terak<PERSON> glezita", "block.minecraft.light_blue_shulker_box": "Cielblua buxo di Shulkero", "block.minecraft.light_blue_stained_glass": "Cielblua vitro tintita", "block.minecraft.light_blue_stained_glass_pane": "Cielblua vitrokarelo tintita", "block.minecraft.light_blue_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_blue_wool": "Cielblua lano", "block.minecraft.light_gray_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON> banero", "block.minecraft.light_gray_bed": "Klargriza lito", "block.minecraft.light_gray_candle": "<PERSON>lar<PERSON><PERSON><PERSON> kande<PERSON>", "block.minecraft.light_gray_candle_cake": "<PERSON><PERSON> kun klargriza kandelo", "block.minecraft.light_gray_carpet": "Klargriza tapiso", "block.minecraft.light_gray_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON> betono", "block.minecraft.light_gray_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_glazed_terracotta": "Klargriza te<PERSON> glezita", "block.minecraft.light_gray_shulker_box": "Klargriza buxo di Shulkero", "block.minecraft.light_gray_stained_glass": "Klargriza vitro tintita", "block.minecraft.light_gray_stained_glass_pane": "Klargriza vitrokarelo tintita", "block.minecraft.light_gray_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_wool": "Klargriza lano", "block.minecraft.light_weighted_pressure_plate": "Lejera presoplako pezanta", "block.minecraft.lightning_rod": "Parafulmino", "block.minecraft.lilac": "Lilaco", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lily_pad": "Nenufaro", "block.minecraft.lime_banner": "Klarverda banero", "block.minecraft.lime_bed": "Klarverda lito", "block.minecraft.lime_candle": "<PERSON><PERSON><PERSON><PERSON> kandelo", "block.minecraft.lime_candle_cake": "<PERSON><PERSON> kun klarverda kandelo", "block.minecraft.lime_carpet": "Klarver<PERSON> tapiso", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON> betono", "block.minecraft.lime_concrete_powder": "Klar<PERSON><PERSON>", "block.minecraft.lime_glazed_terracotta": "Klarverda terakoto glezita", "block.minecraft.lime_shulker_box": "Klarverda buxo di Shulkero", "block.minecraft.lime_stained_glass": "Klarverda vitro tintita", "block.minecraft.lime_stained_glass_pane": "Klarverda vitrokarelo tintita", "block.minecraft.lime_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_wool": "Klar<PERSON><PERSON> lano", "block.minecraft.lodestone": "Magnetstono", "block.minecraft.loom": "Texilo", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON> banero", "block.minecraft.magenta_bed": "Magenta lito", "block.minecraft.magenta_candle": "<PERSON><PERSON><PERSON> kandelo", "block.minecraft.magenta_candle_cake": "<PERSON><PERSON> kun magenta kandelo", "block.minecraft.magenta_carpet": "Magenta tapiso", "block.minecraft.magenta_concrete": "<PERSON><PERSON>a betono", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON><PERSON> bet<PERSON>", "block.minecraft.magenta_glazed_terracotta": "Magenta terak<PERSON> glezita", "block.minecraft.magenta_shulker_box": "Magenta buxo di Shulkero", "block.minecraft.magenta_stained_glass": "Magenta vitro tintita", "block.minecraft.magenta_stained_glass_pane": "Magenta vitrokarelo tintita", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_wool": "Ma<PERSON>a lano", "block.minecraft.magma_block": "Bloko di magmo", "block.minecraft.mangrove_button": "<PERSON><PERSON> mangliera", "block.minecraft.mangrove_door": "<PERSON><PERSON> man<PERSON>", "block.minecraft.mangrove_fence": "Fen<PERSON> mangliera", "block.minecraft.mangrove_fence_gate": "<PERSON><PERSON><PERSON> mangliera", "block.minecraft.mangrove_hanging_sign": "Pendanta signo mangliera", "block.minecraft.mangrove_leaves": "Folii mangliera", "block.minecraft.mangrove_log": "<PERSON><PERSON>", "block.minecraft.mangrove_planks": "<PERSON><PERSON> man<PERSON>", "block.minecraft.mangrove_pressure_plate": "Presoplako mangliera", "block.minecraft.mangrove_propagule": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_roots": "<PERSON><PERSON><PERSON> man<PERSON>", "block.minecraft.mangrove_sign": "<PERSON><PERSON>", "block.minecraft.mangrove_slab": "<PERSON><PERSON><PERSON> man<PERSON>", "block.minecraft.mangrove_stairs": "Eskalero mangliera", "block.minecraft.mangrove_trapdoor": "<PERSON><PERSON><PERSON> man<PERSON>", "block.minecraft.mangrove_wall_hanging_sign": "Murala pendanta signo mangliera", "block.minecraft.mangrove_wall_sign": "Murala signo mangliera", "block.minecraft.mangrove_wood": "Ligno mangliera", "block.minecraft.medium_amethyst_bud": "Mezagranda bur<PERSON>o ametista", "block.minecraft.melon": "<PERSON><PERSON>", "block.minecraft.melon_stem": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.moss_block": "Bloko di musko", "block.minecraft.moss_carpet": "Tapiso di musko", "block.minecraft.mossy_cobblestone": "Eskombro muskoza", "block.minecraft.mossy_cobblestone_slab": "Plako di eskombro muskoza", "block.minecraft.mossy_cobblestone_stairs": "Eskalero di muskoza eskombro", "block.minecraft.mossy_cobblestone_wall": "Muro di eskombro muskoza", "block.minecraft.mossy_stone_brick_slab": "Plako di petrabriki muskoza", "block.minecraft.mossy_stone_brick_stairs": "Eskalero di petrabriki muskoza", "block.minecraft.mossy_stone_brick_wall": "Muro di petrabriki muskoza", "block.minecraft.mossy_stone_bricks": "Petrabriki muskoza", "block.minecraft.moving_piston": "<PERSON><PERSON><PERSON> movanta", "block.minecraft.mud": "<PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Plako di fangobriko", "block.minecraft.mud_brick_stairs": "Eskalero di fangobriko", "block.minecraft.mud_brick_wall": "Muro di fangobriki", "block.minecraft.mud_bricks": "Fang<PERSON><PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "<PERSON>oza radiki mangliera", "block.minecraft.mushroom_stem": "Stipito di fungo", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Netherbrika fenco", "block.minecraft.nether_brick_slab": "Plako di netherbriko", "block.minecraft.nether_brick_stairs": "Eskalero di netherbriko", "block.minecraft.nether_brick_wall": "Muro di netherbriki", "block.minecraft.nether_bricks": "Netherbriki", "block.minecraft.nether_gold_ore": "<PERSON><PERSON><PERSON> di oro netherala", "block.minecraft.nether_portal": "Portalo di Nether", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON><PERSON> di quarco", "block.minecraft.nether_sprouts": "Sprosi di Nether", "block.minecraft.nether_wart": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_wart_block": "Bloko di netherveruko", "block.minecraft.netherite_block": "Bloko di netherito", "block.minecraft.netherrack": "Netherracko", "block.minecraft.note_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_button": "<PERSON><PERSON> querka", "block.minecraft.oak_door": "<PERSON><PERSON> querka", "block.minecraft.oak_fence": "<PERSON><PERSON> querka", "block.minecraft.oak_fence_gate": "<PERSON><PERSON><PERSON> querka", "block.minecraft.oak_hanging_sign": "Pendanta signo querka", "block.minecraft.oak_leaves": "<PERSON>olii querka", "block.minecraft.oak_log": "<PERSON><PERSON>", "block.minecraft.oak_planks": "<PERSON><PERSON> querka", "block.minecraft.oak_pressure_plate": "Presoplako querka", "block.minecraft.oak_sapling": "Arboreto querka", "block.minecraft.oak_sign": "<PERSON><PERSON> que<PERSON>", "block.minecraft.oak_slab": "<PERSON><PERSON><PERSON> que<PERSON>", "block.minecraft.oak_stairs": "Eskalero querka", "block.minecraft.oak_trapdoor": "<PERSON><PERSON><PERSON> querka", "block.minecraft.oak_wall_hanging_sign": "Murala pendanta signo querka", "block.minecraft.oak_wall_sign": "Murala signo querka", "block.minecraft.oak_wood": "<PERSON><PERSON> querka", "block.minecraft.observer": "Observilo", "block.minecraft.obsidian": "Obsidiano", "block.minecraft.ochre_froglight": "<PERSON><PERSON>", "block.minecraft.ominous_banner": "<PERSON><PERSON>", "block.minecraft.open_eyeblossom": "Open Eyeblossom", "block.minecraft.orange_banner": "<PERSON><PERSON><PERSON><PERSON> banero", "block.minecraft.orange_bed": "Oranjea lito", "block.minecraft.orange_candle": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.orange_candle_cake": "<PERSON><PERSON> kun oranjea kandelo", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON><PERSON> tapiso", "block.minecraft.orange_concrete": "<PERSON><PERSON><PERSON><PERSON> betono", "block.minecraft.orange_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> gle<PERSON>ta", "block.minecraft.orange_shulker_box": "Oranjea buxo di Shulkero", "block.minecraft.orange_stained_glass": "Oranjea vitro tintita", "block.minecraft.orange_stained_glass_pane": "Oranjea vitrokarelo tintita", "block.minecraft.orange_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON><PERSON> tulipo", "block.minecraft.orange_wool": "<PERSON><PERSON><PERSON><PERSON> lano", "block.minecraft.oxeye_daisy": "Margrito", "block.minecraft.oxidized_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper": "Kupro oxidizita", "block.minecraft.oxidized_copper_bulb": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_grate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_cut_copper": "Kupro oxidizita tailita", "block.minecraft.oxidized_cut_copper_slab": "Plako kupra oxidizita tailita", "block.minecraft.oxidized_cut_copper_stairs": "Eskalero kupra oxidizita tailita", "block.minecraft.packed_ice": "<PERSON><PERSON><PERSON> ferma", "block.minecraft.packed_mud": "<PERSON><PERSON> ferma", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON>", "block.minecraft.pale_moss_block": "Pale Moss Block", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON> Moss Carpet", "block.minecraft.pale_oak_button": "Pale Oak Button", "block.minecraft.pale_oak_door": "Pale Oak Door", "block.minecraft.pale_oak_fence": "Pale Oak Fence", "block.minecraft.pale_oak_fence_gate": "Pale Oak Fence Gate", "block.minecraft.pale_oak_hanging_sign": "Pale Oak Hanging Sign", "block.minecraft.pale_oak_leaves": "Pale Oak Leaves", "block.minecraft.pale_oak_log": "Pale Oak Log", "block.minecraft.pale_oak_planks": "Pale Oak Planks", "block.minecraft.pale_oak_pressure_plate": "Pale Oak Pressure Plate", "block.minecraft.pale_oak_sapling": "Pale Oak Sapling", "block.minecraft.pale_oak_sign": "Pale Oak Sign", "block.minecraft.pale_oak_slab": "Pale Oak Slab", "block.minecraft.pale_oak_stairs": "Pale Oak Stairs", "block.minecraft.pale_oak_trapdoor": "Pale Oak Trapdoor", "block.minecraft.pale_oak_wall_hanging_sign": "Pale Oak Wall Hanging Sign", "block.minecraft.pale_oak_wall_sign": "Pale Oak Wall Sign", "block.minecraft.pale_oak_wood": "Pale Oak Wood", "block.minecraft.pearlescent_froglight": "Perl-lumina <PERSON>", "block.minecraft.peony": "Peonio", "block.minecraft.petrified_oak_slab": "<PERSON><PERSON><PERSON> plako querka", "block.minecraft.piglin_head": "Kapulkapo", "block.minecraft.piglin_wall_head": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON> banero", "block.minecraft.pink_bed": "Rozea lito", "block.minecraft.pink_candle": "Roz<PERSON> kandelo", "block.minecraft.pink_candle_cake": "<PERSON><PERSON> kun rozea kandelo", "block.minecraft.pink_carpet": "Rozea tapiso", "block.minecraft.pink_concrete": "<PERSON><PERSON><PERSON> betono", "block.minecraft.pink_concrete_powder": "Roz<PERSON> bet<PERSON>", "block.minecraft.pink_glazed_terracotta": "Rozea te<PERSON> glezita", "block.minecraft.pink_petals": "Pink Petals", "block.minecraft.pink_shulker_box": "Rozea buxo di Shulkero", "block.minecraft.pink_stained_glass": "Rozea vitro tintita", "block.minecraft.pink_stained_glass_pane": "Rozea vitrokarelo tintita", "block.minecraft.pink_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_tulip": "Rozea tulipo", "block.minecraft.pink_wool": "Rozea lano", "block.minecraft.piston": "Pistono", "block.minecraft.piston_head": "<PERSON><PERSON>", "block.minecraft.pitcher_crop": "Kruĉa <PERSON><PERSON><PERSON>", "block.minecraft.pitcher_plant": "Kruĉa Planto", "block.minecraft.player_head": "<PERSON><PERSON> di ludanto", "block.minecraft.player_head.named": "<PERSON><PERSON> di %s", "block.minecraft.player_wall_head": "<PERSON><PERSON><PERSON> ka<PERSON> di ludanto", "block.minecraft.podzol": "<PERSON>d<PERSON><PERSON>", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON><PERSON> pinta", "block.minecraft.polished_andesite": "Polisita andesito", "block.minecraft.polished_andesite_slab": "Polisita plako andesita", "block.minecraft.polished_andesite_stairs": "Polisita eskalero andesita", "block.minecraft.polished_basalt": "Bazalto polisita", "block.minecraft.polished_blackstone": "Polisita nigrapetro", "block.minecraft.polished_blackstone_brick_slab": "Polisita plako di briko nigrapetra", "block.minecraft.polished_blackstone_brick_stairs": "Polisita eskalero di briko nigrapetra", "block.minecraft.polished_blackstone_brick_wall": "Polisita muro di briko nigrapetra", "block.minecraft.polished_blackstone_bricks": "Polisita briki nigrapetra", "block.minecraft.polished_blackstone_button": "Polisita butono nigrapetra", "block.minecraft.polished_blackstone_pressure_plate": "Polisita presoplako nigrapetra", "block.minecraft.polished_blackstone_slab": "Polisita plako nigrapetra", "block.minecraft.polished_blackstone_stairs": "Polisita eskalero nigrapetra", "block.minecraft.polished_blackstone_wall": "Muro di polisita nigrapetro", "block.minecraft.polished_deepslate": "Polisita profundardezo", "block.minecraft.polished_deepslate_slab": "Plako di polisita profundardezo", "block.minecraft.polished_deepslate_stairs": "Eskalero di polisita profundardezo", "block.minecraft.polished_deepslate_wall": "Muro di polisita profundardezo", "block.minecraft.polished_diorite": "Polisita diorito", "block.minecraft.polished_diorite_slab": "Polisita plako diorita", "block.minecraft.polished_diorite_stairs": "Polisita eskalero diorita", "block.minecraft.polished_granite": "Polisita granito", "block.minecraft.polished_granite_slab": "Polisita plako granita", "block.minecraft.polished_granite_stairs": "Polisita eskalero granita", "block.minecraft.polished_tuff": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff_stairs": "<PERSON><PERSON><PERSON><PERSON> Ŝtuparoj", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.poppy": "<PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "Potato", "block.minecraft.potted_acacia_sapling": "Aboreto akacia en poto", "block.minecraft.potted_allium": "Alliumo en poto", "block.minecraft.potted_azalea_bush": "Azaleo en poto", "block.minecraft.potted_azure_bluet": "<PERSON><PERSON>ra aciano en poto", "block.minecraft.potted_bamboo": "Bambuo en poto", "block.minecraft.potted_birch_sapling": "Aboreto birka en poto", "block.minecraft.potted_blue_orchid": "Blua orkideo en poto", "block.minecraft.potted_brown_mushroom": "Bruna Fungo en Poto", "block.minecraft.potted_cactus": "Kaktuso en poto", "block.minecraft.potted_cherry_sapling": "<PERSON><PERSON><PERSON> Branĉeto", "block.minecraft.potted_closed_eyeblossom": "Potted Closed Eyeblossom", "block.minecraft.potted_cornflower": "Aciano en poto", "block.minecraft.potted_crimson_fungus": "Fungo karmezina en poto", "block.minecraft.potted_crimson_roots": "<PERSON><PERSON><PERSON> karmez<PERSON> en poto", "block.minecraft.potted_dandelion": "Leondento en poto", "block.minecraft.potted_dark_oak_sapling": "Aboreto obskurquerka en poto", "block.minecraft.potted_dead_bush": "Mortinta busho en poto", "block.minecraft.potted_fern": "Filiko en poto", "block.minecraft.potted_flowering_azalea_bush": "Florifanta azaleo en poto", "block.minecraft.potted_jungle_sapling": "Aboreto mahagoniera en poto", "block.minecraft.potted_lily_of_the_valley": "Maifloro en poto", "block.minecraft.potted_mangrove_propagule": "Propagulo mangliera en poto", "block.minecraft.potted_oak_sapling": "Aboreto querka en poto", "block.minecraft.potted_open_eyeblossom": "Potted Open Eyeblossom", "block.minecraft.potted_orange_tulip": "Oranjea tulipo en poto", "block.minecraft.potted_oxeye_daisy": "Margrito en poto", "block.minecraft.potted_pale_oak_sapling": "Potted Pale Oak Sapling", "block.minecraft.potted_pink_tulip": "Rozea tulipo en poto", "block.minecraft.potted_poppy": "Papavero en poto", "block.minecraft.potted_red_mushroom": "Reda Fungo en Poto", "block.minecraft.potted_red_tulip": "Reda tulipo en poto", "block.minecraft.potted_spruce_sapling": "Aboreto abieta en poto", "block.minecraft.potted_torchflower": "Enpota Fakelo-floro", "block.minecraft.potted_warped_fungus": "Fungo bizara en poto", "block.minecraft.potted_warped_roots": "Radiki bizara en poto", "block.minecraft.potted_white_tulip": "<PERSON><PERSON>a tulipo en poto", "block.minecraft.potted_wither_rose": "With<PERSON><PERSON>zo en poto", "block.minecraft.powder_snow": "<PERSON><PERSON> pulvera", "block.minecraft.powder_snow_cauldron": "Kaldrono di nivo pulvera", "block.minecraft.powered_rail": "<PERSON><PERSON> di propulso", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "<PERSON><PERSON><PERSON> di briko prismarina", "block.minecraft.prismarine_brick_stairs": "Eskalero di briki prismarina", "block.minecraft.prismarine_bricks": "Briki di prismarino", "block.minecraft.prismarine_slab": "Plako di prismarino", "block.minecraft.prismarine_stairs": "Eskalero di prismarino", "block.minecraft.prismarine_wall": "Muro di prismarino", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Stipito di kukurbito", "block.minecraft.purple_banner": "<PERSON><PERSON><PERSON> banero", "block.minecraft.purple_bed": "Purpura lito", "block.minecraft.purple_candle": "Pur<PERSON> kandelo", "block.minecraft.purple_candle_cake": "<PERSON>ko kun purpura kandelo", "block.minecraft.purple_carpet": "Purpura tapiso", "block.minecraft.purple_concrete": "<PERSON><PERSON><PERSON> betono", "block.minecraft.purple_concrete_powder": "Purpura betonpulvero", "block.minecraft.purple_glazed_terracotta": "Purpura terakoto glezita", "block.minecraft.purple_shulker_box": "Purpura buxo di Shulkero", "block.minecraft.purple_stained_glass": "Purpura vitro tintita", "block.minecraft.purple_stained_glass_pane": "Purpura vitrokarelo tintita", "block.minecraft.purple_terracotta": "Pur<PERSON> terakoto", "block.minecraft.purple_wool": "Pur<PERSON> lano", "block.minecraft.purpur_block": "Bloko di purpuro", "block.minecraft.purpur_pillar": "Kolono di purpuro", "block.minecraft.purpur_slab": "Plako di purpuro", "block.minecraft.purpur_stairs": "Eskalero di purpuro", "block.minecraft.quartz_block": "Bloko di quarco", "block.minecraft.quartz_bricks": "<PERSON><PERSON><PERSON> di quarco", "block.minecraft.quartz_pillar": "Kolono di quarco", "block.minecraft.quartz_slab": "Plako di quarco", "block.minecraft.quartz_stairs": "Eskalero di quarco", "block.minecraft.rail": "<PERSON><PERSON>", "block.minecraft.raw_copper_block": "Bloko di kruda kupro", "block.minecraft.raw_gold_block": "Bloko di kruda oro", "block.minecraft.raw_iron_block": "Bloko di kruda fero", "block.minecraft.red_banner": "<PERSON><PERSON> banero", "block.minecraft.red_bed": "Reda lito", "block.minecraft.red_candle": "<PERSON><PERSON> kande<PERSON>", "block.minecraft.red_candle_cake": "<PERSON><PERSON> kun reda kandelo", "block.minecraft.red_carpet": "Reda tapiso", "block.minecraft.red_concrete": "<PERSON><PERSON> betono", "block.minecraft.red_concrete_powder": "<PERSON>a bet<PERSON>ro", "block.minecraft.red_glazed_terracotta": "Reda terak<PERSON> glezita", "block.minecraft.red_mushroom": "Reda fungo", "block.minecraft.red_mushroom_block": "Bloki di fungo reda", "block.minecraft.red_nether_brick_slab": "Plako di reda netherbriki", "block.minecraft.red_nether_brick_stairs": "Eskalero di reda netherbriki", "block.minecraft.red_nether_brick_wall": "Muro di reda netherbriki", "block.minecraft.red_nether_bricks": "Reda netherbriki", "block.minecraft.red_sand": "Reda sablo", "block.minecraft.red_sandstone": "Reda greso", "block.minecraft.red_sandstone_slab": "Plako di greso reda", "block.minecraft.red_sandstone_stairs": "Eskalero di reda greso", "block.minecraft.red_sandstone_wall": "Muro di reda greso", "block.minecraft.red_shulker_box": "Reda buxo di Shulkero", "block.minecraft.red_stained_glass": "Reda vitro tintita", "block.minecraft.red_stained_glass_pane": "Reda vitrokarelo tintita", "block.minecraft.red_terracotta": "<PERSON><PERSON>", "block.minecraft.red_tulip": "<PERSON><PERSON> tulipo", "block.minecraft.red_wool": "Reda lano", "block.minecraft.redstone_block": "Bloko di redstoneo", "block.minecraft.redstone_lamp": "<PERSON><PERSON> di redstoneo", "block.minecraft.redstone_ore": "<PERSON><PERSON>o di redstoneo", "block.minecraft.redstone_torch": "Torch di redstoneo", "block.minecraft.redstone_wall_torch": "Murala torcho di redstoneo", "block.minecraft.redstone_wire": "Redstonefilo", "block.minecraft.reinforced_deepslate": "Plufortigita profundardezo", "block.minecraft.repeater": "Repetilo di redstoneo", "block.minecraft.repeating_command_block": "Bloko di komandi iteranta", "block.minecraft.resin_block": "Block of Resin", "block.minecraft.resin_brick_slab": "Resin Brick Slab", "block.minecraft.resin_brick_stairs": "Resin Brick Stairs", "block.minecraft.resin_brick_wall": "Resin Brick Wall", "block.minecraft.resin_bricks": "Resin Bricks", "block.minecraft.resin_clump": "<PERSON><PERSON>", "block.minecraft.respawn_anchor": "Regenitankro", "block.minecraft.rooted_dirt": "Sulo radiki<PERSON>", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sand": "Sa<PERSON><PERSON>", "block.minecraft.sandstone": "Greso", "block.minecraft.sandstone_slab": "Plako di greso", "block.minecraft.sandstone_stairs": "Eskalero di greso", "block.minecraft.sandstone_wall": "Muro di greso", "block.minecraft.scaffolding": "Eshafodo", "block.minecraft.sculk": "<PERSON><PERSON><PERSON>", "block.minecraft.sculk_catalyst": "Katalizivo di Sculko", "block.minecraft.sculk_sensor": "Detektoro di Sculko", "block.minecraft.sculk_shrieker": "Krieganto di Sculko", "block.minecraft.sculk_vein": "<PERSON><PERSON><PERSON>", "block.minecraft.sea_lantern": "Marlanterno", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "Marherbo", "block.minecraft.set_spawn": "Rigenitar-loko definesis", "block.minecraft.short_dry_grass": "Short Dry Grass", "block.minecraft.short_grass": "<PERSON><PERSON>", "block.minecraft.shroomlight": "Fungalumo", "block.minecraft.shulker_box": "Buxo di Shulkero", "block.minecraft.skeleton_skull": "Kranio di skeleto", "block.minecraft.skeleton_wall_skull": "Murala kranio di skeleto", "block.minecraft.slime_block": "Bloko di slamo", "block.minecraft.small_amethyst_bud": "<PERSON><PERSON> burjono ametista", "block.minecraft.small_dripleaf": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smithing_table": "Tablo di forjisto", "block.minecraft.smoker": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "<PERSON><PERSON><PERSON> g<PERSON>a", "block.minecraft.smooth_quartz": "Bloko di quarco glata", "block.minecraft.smooth_quartz_slab": "Plako di quarco glata", "block.minecraft.smooth_quartz_stairs": "Eskalero di quarco glata", "block.minecraft.smooth_red_sandstone": "Reda greso glata", "block.minecraft.smooth_red_sandstone_slab": "Plako di reda greso glata", "block.minecraft.smooth_red_sandstone_stairs": "Eskalero di reda greso glata", "block.minecraft.smooth_sandstone": "Greso glata", "block.minecraft.smooth_sandstone_slab": "Plako di greso glata", "block.minecraft.smooth_sandstone_stairs": "Eskalero di greso glata", "block.minecraft.smooth_stone": "<PERSON><PERSON> glata", "block.minecraft.smooth_stone_slab": "Plako di petro glata", "block.minecraft.sniffer_egg": "Esnifador O<PERSON>", "block.minecraft.snow": "<PERSON><PERSON>", "block.minecraft.snow_block": "Bloko di nivo", "block.minecraft.soul_campfire": "Amnokampfairo", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_lantern": "Anmolanterno", "block.minecraft.soul_sand": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_soil": "<PERSON><PERSON><PERSON>", "block.minecraft.soul_torch": "Amnotorcho", "block.minecraft.soul_wall_torch": "<PERSON><PERSON><PERSON> am<PERSON>", "block.minecraft.spawn.not_valid": "Vu ne havas hemlito o regenitankro charjita, od ol esis obstruktata", "block.minecraft.spawner": "Monstra Kreilo", "block.minecraft.spawner.desc1": "Interagi kun Naski-Ovo:", "block.minecraft.spawner.desc2": "<PERSON><PERSON><PERSON>", "block.minecraft.sponge": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spore_blossom": "<PERSON><PERSON>o sporoza", "block.minecraft.spruce_button": "<PERSON><PERSON> abieta", "block.minecraft.spruce_door": "<PERSON><PERSON> a<PERSON>", "block.minecraft.spruce_fence": "Fenco abieta", "block.minecraft.spruce_fence_gate": "Pordeto abieta", "block.minecraft.spruce_hanging_sign": "Pendanta signo abieta", "block.minecraft.spruce_leaves": "Folii abieta", "block.minecraft.spruce_log": "<PERSON><PERSON> a<PERSON>", "block.minecraft.spruce_planks": "Planki abieta", "block.minecraft.spruce_pressure_plate": "Presoplako abieta", "block.minecraft.spruce_sapling": "Arboreto abieta", "block.minecraft.spruce_sign": "Signo a<PERSON>", "block.minecraft.spruce_slab": "Plako a<PERSON>", "block.minecraft.spruce_stairs": "Eskalero abieta", "block.minecraft.spruce_trapdoor": "Trapo abieta", "block.minecraft.spruce_wall_hanging_sign": "Murala pendanta signo abieta", "block.minecraft.spruce_wall_sign": "Murala signo abieta", "block.minecraft.spruce_wood": "Ligno abieta", "block.minecraft.sticky_piston": "<PERSON>stono adherema", "block.minecraft.stone": "Petro", "block.minecraft.stone_brick_slab": "Plako di petrabriko", "block.minecraft.stone_brick_stairs": "Eskalero di petrabriko", "block.minecraft.stone_brick_wall": "<PERSON>ro di petrabriki", "block.minecraft.stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_button": "<PERSON><PERSON>", "block.minecraft.stone_pressure_plate": "<PERSON> pre<PERSON>lako", "block.minecraft.stone_slab": "Plako di petro", "block.minecraft.stone_stairs": "Eskalero di petro", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "<PERSON><PERSON> akacia sen kortico", "block.minecraft.stripped_acacia_wood": "Ligno akacia sen kortico", "block.minecraft.stripped_bamboo_block": "Bloko de Senŝelita Bambuo", "block.minecraft.stripped_birch_log": "<PERSON>io birka sen kortico", "block.minecraft.stripped_birch_wood": "Ligno birka sen kortico", "block.minecraft.stripped_cherry_log": "Senpela Ĉeriza Registro", "block.minecraft.stripped_cherry_wood": "Senpela Ĉeriza Ligno", "block.minecraft.stripped_crimson_hyphae": "Hyfei karmezina sen kortico", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON> karmezina sen kortico", "block.minecraft.stripped_dark_oak_log": "<PERSON><PERSON> o<PERSON> sen kortico", "block.minecraft.stripped_dark_oak_wood": "Ligno obskurquerka sen kortico", "block.minecraft.stripped_jungle_log": "<PERSON><PERSON> ma<PERSON> sen kortico", "block.minecraft.stripped_jungle_wood": "Ligno mahagoniera sen kortico", "block.minecraft.stripped_mangrove_log": "<PERSON><PERSON> mangliera sen kortico", "block.minecraft.stripped_mangrove_wood": "Ligno mangliera sen kortico", "block.minecraft.stripped_oak_log": "<PERSON><PERSON> que<PERSON> sen kortico", "block.minecraft.stripped_oak_wood": "Ligno querka sen kortico", "block.minecraft.stripped_pale_oak_log": "Stripped Pale Oak Log", "block.minecraft.stripped_pale_oak_wood": "Stripped Pale Oak Wood", "block.minecraft.stripped_spruce_log": "<PERSON>io abieta sen kortico", "block.minecraft.stripped_spruce_wood": "Ligno abieta sen kortico", "block.minecraft.stripped_warped_hyphae": "Hyfei bizara sen kortico", "block.minecraft.stripped_warped_stem": "Stipo bizara sen kortico", "block.minecraft.structure_block": "<PERSON><PERSON><PERSON> struk<PERSON>", "block.minecraft.structure_void": "<PERSON><PERSON><PERSON> struk<PERSON>", "block.minecraft.sugar_cane": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sunflower": "Sunfloro", "block.minecraft.suspicious_gravel": "Suspekta Ŝtelo", "block.minecraft.suspicious_sand": "Suspekta <PERSON>", "block.minecraft.sweet_berry_bush": "Arbusto di dolcaberi", "block.minecraft.tall_dry_grass": "Tall Dry Grass", "block.minecraft.tall_grass": "Herbo alta", "block.minecraft.tall_seagrass": "Marherbo alta", "block.minecraft.target": "Pafsko<PERSON>", "block.minecraft.terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.test_block": "Test Block", "block.minecraft.test_instance_block": "Test Instance Block", "block.minecraft.tinted_glass": "<PERSON>it<PERSON> obsku<PERSON>ita", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT explosions are disabled", "block.minecraft.torch": "<PERSON><PERSON>", "block.minecraft.torchflower": "Torĉafloro", "block.minecraft.torchflower_crop": "Kultivaĵo de Torĉafloro", "block.minecraft.trapped_chest": "<PERSON><PERSON><PERSON> insidiita", "block.minecraft.trial_spawner": "Provospawnilo", "block.minecraft.tripwire": "Insidia filo", "block.minecraft.tripwire_hook": "Hoko di insidia filo", "block.minecraft.tube_coral": "Pipeta koralio", "block.minecraft.tube_coral_block": "Bloko di pipeta koralio", "block.minecraft.tube_coral_fan": "Fano di pipeta koralio", "block.minecraft.tube_coral_wall_fan": "Murala fano di pipeta koralio", "block.minecraft.tuff": "Tofo", "block.minecraft.tuff_brick_slab": "<PERSON> el tufa briko", "block.minecraft.tuff_brick_stairs": "Sxtupoj el tufa briko", "block.minecraft.tuff_brick_wall": "Murŝtona murejo el tufo", "block.minecraft.tuff_bricks": "Murŝtonoj el tufo", "block.minecraft.tuff_slab": "Tavolo el tufo", "block.minecraft.tuff_stairs": "Ŝtuparo el tufo", "block.minecraft.tuff_wall": "<PERSON><PERSON><PERSON> murejo", "block.minecraft.turtle_egg": "Ovo di tortugo", "block.minecraft.twisting_vines": "Viti tordanta", "block.minecraft.twisting_vines_plant": "Planto di viti tordanta", "block.minecraft.vault": "Ŝlosilejo", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON>", "block.minecraft.vine": "Viti", "block.minecraft.void_air": "Aero vakuala", "block.minecraft.wall_torch": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_button": "Butono bizara", "block.minecraft.warped_door": "<PERSON><PERSON>", "block.minecraft.warped_fence": "Fenco bi<PERSON>a", "block.minecraft.warped_fence_gate": "<PERSON><PERSON><PERSON> bizara", "block.minecraft.warped_fungus": "Fungo bizara", "block.minecraft.warped_hanging_sign": "Pendanta signo bizara", "block.minecraft.warped_hyphae": "<PERSON>y<PERSON>i bizara", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_planks": "<PERSON><PERSON> bi<PERSON>a", "block.minecraft.warped_pressure_plate": "Presoplako bizara", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON> bi<PERSON>a", "block.minecraft.warped_sign": "<PERSON><PERSON>", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_stairs": "Eskalero bizara", "block.minecraft.warped_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_trapdoor": "<PERSON><PERSON><PERSON> bi<PERSON>a", "block.minecraft.warped_wall_hanging_sign": "Murala pendanta signo bizara", "block.minecraft.warped_wall_sign": "Murala signo bizara", "block.minecraft.warped_wart_block": "Bloko di veruko bizara", "block.minecraft.water": "Aquo", "block.minecraft.water_cauldron": "Kaldrono di aquo", "block.minecraft.waxed_chiseled_copper": "<PERSON><PERSON><PERSON> skul<PERSON>ita kupro", "block.minecraft.waxed_copper_block": "Vaxizita bloko di kupro", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON><PERSON> k<PERSON>o", "block.minecraft.waxed_copper_door": "Pordo el vaksita kupro", "block.minecraft.waxed_copper_grate": "<PERSON><PERSON><PERSON> kupra grato", "block.minecraft.waxed_copper_trapdoor": "<PERSON><PERSON> pord<PERSON>uo el<PERSON>a", "block.minecraft.waxed_cut_copper": "<PERSON><PERSON><PERSON><PERSON> kupro tailita", "block.minecraft.waxed_cut_copper_slab": "Vaxizita plako kupra tailita", "block.minecraft.waxed_cut_copper_stairs": "Vaxizita eskalero kupra tailita", "block.minecraft.waxed_exposed_chiseled_copper": "Vaksita ekstera skulptita kupro", "block.minecraft.waxed_exposed_copper": "Vaxizita kupro expozita", "block.minecraft.waxed_exposed_copper_bulb": "Vaksita ekstera kupra bulbo", "block.minecraft.waxed_exposed_copper_door": "Vaksita ekstera kupra pordo", "block.minecraft.waxed_exposed_copper_grate": "Vaksita ekstera kupra grato", "block.minecraft.waxed_exposed_copper_trapdoor": "Vaksita ekstera kupra pordtruo", "block.minecraft.waxed_exposed_cut_copper": "Vaxizita kupro expozita tailita", "block.minecraft.waxed_exposed_cut_copper_slab": "Vaxizita plako kupra expozita tailita", "block.minecraft.waxed_exposed_cut_copper_stairs": "Vaxizita eskalero kupra expozita tailita", "block.minecraft.waxed_oxidized_chiseled_copper": "Vaksita oksidiĝinta skulptita kupro", "block.minecraft.waxed_oxidized_copper": "Vaxizita kupro oxidizita", "block.minecraft.waxed_oxidized_copper_bulb": "<PERSON><PERSON><PERSON> oksidiĝinta kupra bulbo", "block.minecraft.waxed_oxidized_copper_door": "Vaks<PERSON> oksidiĝinta kupra pordo", "block.minecraft.waxed_oxidized_copper_grate": "<PERSON><PERSON><PERSON> oksidiĝinta kupra grato", "block.minecraft.waxed_oxidized_copper_trapdoor": "Vaksita oksidiĝinta kupra pordtruo", "block.minecraft.waxed_oxidized_cut_copper": "Vaxizita kupro oxidizita tailita", "block.minecraft.waxed_oxidized_cut_copper_slab": "Vaxizita plako kupra oxidizita tailita", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Vaxizita eskalero kupra oxidizita tailita", "block.minecraft.waxed_weathered_chiseled_copper": "<PERSON><PERSON><PERSON> veterita skulptita kupro", "block.minecraft.waxed_weathered_copper": "<PERSON><PERSON><PERSON><PERSON> kupro frusta", "block.minecraft.waxed_weathered_copper_bulb": "<PERSON><PERSON><PERSON> veterita kupra bulbo", "block.minecraft.waxed_weathered_copper_door": "<PERSON><PERSON><PERSON> veterita kupra pordo", "block.minecraft.waxed_weathered_copper_grate": "<PERSON><PERSON><PERSON> veterita kupra grato", "block.minecraft.waxed_weathered_copper_trapdoor": "<PERSON><PERSON><PERSON> veterita kupra pordtruo", "block.minecraft.waxed_weathered_cut_copper": "Vaxizita kupro frusta tailita", "block.minecraft.waxed_weathered_cut_copper_slab": "Vaxizita plako kupra frusta tailita", "block.minecraft.waxed_weathered_cut_copper_stairs": "Vaxizita eskalero kupra frusta tailita", "block.minecraft.weathered_chiseled_copper": "Veterita skulptita kupro", "block.minecraft.weathered_copper": "<PERSON><PERSON><PERSON> frusta", "block.minecraft.weathered_copper_bulb": "Veterita kupra bulbo", "block.minecraft.weathered_copper_door": "Veterita kupra pordo", "block.minecraft.weathered_copper_grate": "Veterita kupra grato", "block.minecraft.weathered_copper_trapdoor": "Veterita kupra pordtruo", "block.minecraft.weathered_cut_copper": "<PERSON><PERSON><PERSON> frusta tailita", "block.minecraft.weathered_cut_copper_slab": "<PERSON><PERSON><PERSON> kupra frusta tailita", "block.minecraft.weathered_cut_copper_stairs": "Eskalero kupra frusta tailita", "block.minecraft.weeping_vines": "Viti ploranta", "block.minecraft.weeping_vines_plant": "Planto di viti ploranta", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON><PERSON> imbibita", "block.minecraft.wheat": "Rekoltajo di frumento", "block.minecraft.white_banner": "<PERSON><PERSON><PERSON> banero", "block.minecraft.white_bed": "<PERSON><PERSON>a lito", "block.minecraft.white_candle": "<PERSON><PERSON><PERSON> kandelo", "block.minecraft.white_candle_cake": "<PERSON><PERSON> kun <PERSON>a kandelo", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON> tapiso", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON> betono", "block.minecraft.white_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON> te<PERSON> glezita", "block.minecraft.white_shulker_box": "<PERSON><PERSON><PERSON> buxo di Shulkero", "block.minecraft.white_stained_glass": "Blanka vitro tintita", "block.minecraft.white_stained_glass_pane": "<PERSON>lanka vitrokarelo tintita", "block.minecraft.white_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> tulipo", "block.minecraft.white_wool": "<PERSON><PERSON><PERSON> lano", "block.minecraft.wildflowers": "Wildflowers", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Kranio di witherskeleto", "block.minecraft.wither_skeleton_wall_skull": "Murala kranio di witherskeleto", "block.minecraft.yellow_banner": "<PERSON><PERSON> banero", "block.minecraft.yellow_bed": "<PERSON>lava lito", "block.minecraft.yellow_candle": "<PERSON><PERSON> kande<PERSON>", "block.minecraft.yellow_candle_cake": "<PERSON>ko kun flava kandelo", "block.minecraft.yellow_carpet": "<PERSON><PERSON> tapiso", "block.minecraft.yellow_concrete": "<PERSON><PERSON> betono", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "<PERSON><PERSON> te<PERSON> glezita", "block.minecraft.yellow_shulker_box": "<PERSON>lava buxo di Shulkero", "block.minecraft.yellow_stained_glass": "Flava vitro tintita", "block.minecraft.yellow_stained_glass_pane": "<PERSON><PERSON> vitrok<PERSON>o tintita", "block.minecraft.yellow_terracotta": "<PERSON><PERSON>", "block.minecraft.yellow_wool": "<PERSON><PERSON> lano", "block.minecraft.zombie_head": "<PERSON><PERSON> zombio", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON> kapo di zombio", "book.byAuthor": "per %1$s", "book.edit.title": "Book Edit Screen", "book.editTitle": "Skribar titulo:", "book.finalizeButton": "Signatar e klozar", "book.finalizeWarning": "Avertez! Kande vu signatos la libro, ol ne plus esos redaktebla.", "book.generation.0": "Originalajo", "book.generation.1": "<PERSON><PERSON><PERSON> original<PERSON>", "book.generation.2": "<PERSON><PERSON><PERSON> kopiuro", "book.generation.3": "<PERSON><PERSON><PERSON>", "book.invalid.tag": "* Etiketo di libro nevalida *", "book.pageIndicator": "Pagino %1$s ek %2$s", "book.page_button.next": "Next Page", "book.page_button.previous": "Previous Page", "book.sign.title": "Book Sign Screen", "book.sign.titlebox": "Title", "book.signButton": "Signatar", "book.view.title": "Book View Screen", "build.tooHigh": "La maksimuma alteco por konstruo estas %s", "chat.cannotSend": "<PERSON>e povas sendar mesajo", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Klikar por teleportar", "chat.copy": "Ko<PERSON>r", "chat.copy.click": "Kliktez por kopiar a klipbordo", "chat.deleted_marker": "Ĉi tiu babilmesaĝo estis forigita de la servilo.", "chat.disabled.chain_broken": "La babilado malkapablas pro rompita ĉeno. Bonvolu reprovi konekti.", "chat.disabled.expiredProfileKey": "Babilado malŝaltiĝis pro eksvalida publika ŝlosilo de profilo. Bonvolu reprovi konekti.", "chat.disabled.invalid_command_signature": "Command had unexpected or missing command argument signatures.", "chat.disabled.invalid_signature": "<PERSON><PERSON> had an invalid signature. Please try reconnecting.", "chat.disabled.launcher": "Babilado malŝaltiĝis pro lanĉilon opcio. Ne eblas sendi mesaĝon.", "chat.disabled.missingProfileKey": "Ne darfas konversar pro ke klefo publika di profilo esas mankanta. Voluntez probar rikonektar.", "chat.disabled.options": "Babilado malŝaltiĝis en klientaj opcioj.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON> received out-of-order. Did your system time change?", "chat.disabled.profile": "Babilado ne permesatas laŭ agordoj de konto. Premu '%s' denove por pli da informo.", "chat.disabled.profile.moreInfo": "Babilado ne permesatas laŭ agordoj de konto. Ne eblas sendi aŭ rigardi mesaĝojn.", "chat.editBox": "konverso", "chat.filtered": "Filtrita de la servilo.", "chat.filtered_full": "La servilo kaŝis vian mesaĝon al iuj ludantoj.", "chat.link.confirm": "Ka vu certe volas apertar la retloko sequanta?", "chat.link.confirmTrusted": "Ka vu volas apertar ica ligilo o kop<PERSON> ol?", "chat.link.open": "Apertar en reto-nevigilo", "chat.link.warning": "Nulafoye apertez ligili de personi qua vu ne fidas!", "chat.queue": "[+%s linei eventonta]", "chat.square_brackets": "[%s]", "chat.tag.error": "La servilo sendis nevalidan mesaĝon.", "chat.tag.modified": "Mesaĝo modifita de la servilo. Originala:", "chat.tag.not_secure": "Nekontrolata mesaĝo. Ne eblas raporti ĝin.", "chat.tag.system": "Servila mesaĝo. Ne eblas raporti ĝin.", "chat.tag.system_single_player": "Servila mesaĝo.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s jus completis la chalenjo %s", "chat.type.advancement.goal": "%s jus atteindis l'objektalo %s", "chat.type.advancement.task": "%s jus progresis a %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Komunikar kun esquado", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s dicas %s", "chat.validation_error": "Eraro de babilada validigo", "chat_screen.message": "Sendota Mesajo: %s", "chat_screen.title": "Skreno di konverso", "chat_screen.usage": "Entajpu mesaĝon kaj premu Enmeti por sendi", "chunk.toast.checkLog": "See log for more details", "chunk.toast.loadFailure": "Failed to load chunk at %s", "chunk.toast.lowDiskSpace": "Poco da spaco sur disko!", "chunk.toast.lowDiskSpace.description": "Might not be able to save the world.", "chunk.toast.saveFailure": "Failed to save chunk at %s", "clear.failed.multiple": "Nula objekti esis trovita sur %s ludanti", "clear.failed.single": "Nula objekti esis trovita sur ludanto %s", "color.minecraft.black": "Nigra", "color.minecraft.blue": "<PERSON><PERSON>", "color.minecraft.brown": "<PERSON><PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "Griza", "color.minecraft.green": "<PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Cielblua", "color.minecraft.light_gray": "Klargriza", "color.minecraft.lime": "Klarverda", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.pink": "Rozea", "color.minecraft.purple": "Purpura", "color.minecraft.red": "Ruĝa", "color.minecraft.white": "<PERSON><PERSON><PERSON>", "color.minecraft.yellow": "Flava", "command.context.here": "<--[HIKE]", "command.context.parse_error": "%s ĉe pozicio %s: %s", "command.exception": "Ne povis analizar sintaxala komando: %s", "command.expected.separator": "Expektis spaco blanka a finar un argumento, ma trovis donaji trananta", "command.failed": "Neexpektita eroro eventis dum ke vu probis exekutar ta impero", "command.forkLimit": "Maksimuma nombro de kuntekstoj (%s) atingita", "command.unknown.argument": "Argumento nekorekta por komando", "command.unknown.command": "Nekonata aŭ nekompleta komando, vidu suben por eraro", "commands.advancement.criterionNotFound": "La avancado %1$s ne kontenas la kriterio '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Ne povis grantar kriterio '%s' de avancado %s a(d) %s luderi pro ke li ja havas ol", "commands.advancement.grant.criterion.to.many.success": "Grantita kriterio '%s' de avancado %s a(d) %s luderi", "commands.advancement.grant.criterion.to.one.failure": "Ne povas grantar kriterio '%s' de avancado %s a(d) %s pro ke lu ja havas ol", "commands.advancement.grant.criterion.to.one.success": "Grantita kriterio '%s' de avancado %s a(d) %s", "commands.advancement.grant.many.to.many.failure": "Ne povas grantar %s avancadi a(d) %s luderi pro ke li ja havas ol", "commands.advancement.grant.many.to.many.success": "Grantita %s avancadi a(d) %s luderi", "commands.advancement.grant.many.to.one.failure": "Ne povas grantar %s avancadi a(d) %s pro ke lu ja havas ol", "commands.advancement.grant.many.to.one.success": "Grantita %s avancadi a(d) %s", "commands.advancement.grant.one.to.many.failure": "Ne povis grantar la avancado %s a(d) %s luderi pro ke lu ja havas ol", "commands.advancement.grant.one.to.many.success": "Grantita la avancado %s a(d) %s luderi", "commands.advancement.grant.one.to.one.failure": "Ne povis grantar avancado %s a(d) %s pro ke lu ja havas ol", "commands.advancement.grant.one.to.one.success": "Grantita la avancado %s a(d) %s", "commands.advancement.revoke.criterion.to.many.failure": "Ne povis revokar kriterio '%s' di avancado %s de %s luderi pro ke li ne havas ol", "commands.advancement.revoke.criterion.to.many.success": "Revokita kriterio '%s' di avancado %s de %s luderi", "commands.advancement.revoke.criterion.to.one.failure": "Ne povis revokar kriterio '%s' di avancado %s de %s pro ke lu ne havas ol", "commands.advancement.revoke.criterion.to.one.success": "Revokita kriterio '%s' di avancado %s de %s", "commands.advancement.revoke.many.to.many.failure": "Ne povis revokar %s avancadi de %s luderi pro ke li ne havas ol", "commands.advancement.revoke.many.to.many.success": "Revokita %s avancadi de %s luderi", "commands.advancement.revoke.many.to.one.failure": "Ne povis revokar %s avancadi de %s pro ke li ne havas ol", "commands.advancement.revoke.many.to.one.success": "Revokita %s avancadi de %s", "commands.advancement.revoke.one.to.many.failure": "Ne povis revokar avancado %s de %s luderi pro ke li ne havas ol", "commands.advancement.revoke.one.to.many.success": "Revokita la avancado %s de %s luderi", "commands.advancement.revoke.one.to.one.failure": "Ne povis revokar avancado %s de %s pro ke li ne havas ol", "commands.advancement.revoke.one.to.one.success": "Revokita la avancado %s de %s", "commands.attribute.base_value.get.success": "Baza valoro de atributo %s por ento %s estas %s", "commands.attribute.base_value.reset.success": "Base value for attribute %s for entity %s reset to default %s", "commands.attribute.base_value.set.success": "Baza valoro por atributo %s por ento %s difinita al %s", "commands.attribute.failed.entity": "%s ne esas ento valida por ica komando", "commands.attribute.failed.modifier_already_present": "Modifanto %s jam estas sur atributo %s por ento %s", "commands.attribute.failed.no_attribute": "Ento %s ne havas atributon %s", "commands.attribute.failed.no_modifier": "Atributo %s por ento %s ne havas modifanton %s", "commands.attribute.modifier.add.success": "Aldonita modifanton %s al atributo %s por ento %s", "commands.attribute.modifier.remove.success": "Forigita modifanton %s de atributo %s por ento %s", "commands.attribute.modifier.value.get.success": "Valoro de modifanto %s pri atributo %s por ento %s estas %s", "commands.attribute.value.get.success": "Valoro de atributo %s por ento %s estas %s", "commands.ban.failed": "<PERSON><PERSON> chan<PERSON>. La ludanto esas ja blokusata", "commands.ban.success": "Blokusita %s: %s", "commands.banip.failed": "<PERSON>ulo chanjis. Ta IP esas ja blokusata", "commands.banip.info": "Ĉi tiu forbaro tuŝas %s ludanto(jn): %s", "commands.banip.invalid": "IP-adreso nevalida o ludanto nekonocata", "commands.banip.success": "Blokusita IP-adresi %%s: %s", "commands.banlist.entry": "%s esis blokusita per %s: %s", "commands.banlist.entry.unknown": "(Unknown)", "commands.banlist.list": "Estas %s forbaro(j):", "commands.banlist.none": "Ne esas blokusi", "commands.bossbar.create.failed": "Bossbaro ja existas kun la ID '%s'", "commands.bossbar.create.success": "Personaligita chefostango %s esis kreita", "commands.bossbar.get.max": "Personaligita chefostango %s havas maximo da %s", "commands.bossbar.get.players.none": "Personaligita chefostango %s ne havas luderi prezente enrete", "commands.bossbar.get.players.some": "Propra ĉefplanko %s havas %s ludanto(j) n aktuale konektitaj: %s", "commands.bossbar.get.value": "Personaligita chefostango %s havas valoro da %s", "commands.bossbar.get.visible.hidden": "Personaligita chefostango %s esas prezente celita", "commands.bossbar.get.visible.visible": "Personaligita chefostango %s esas prezente montrita", "commands.bossbar.list.bars.none": "Ne esas aktiva bossbari personaligita", "commands.bossbar.list.bars.some": "Estas %s aktivaj propraj ĉefplanko(j): %s", "commands.bossbar.remove.success": "Personaligita chefostango %s esis forigita", "commands.bossbar.set.color.success": "Personaligita chefostango %s chanjis koloro", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON> chan<PERSON>. T'esas ja la koloro d'ica bossbaro", "commands.bossbar.set.max.success": "Personaligita chefostango %s chanjis maximo a %s", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON> chan<PERSON>. T'esas ja la maximo d'ica bossbaro", "commands.bossbar.set.name.success": "Personaligita chefostango %s esis rinomizita", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON> chan<PERSON>. T'esas ja la nomo d'ica bossbaro", "commands.bossbar.set.players.success.none": "La boss-baro <PERSON> %s ne havas plu ludari", "commands.bossbar.set.players.success.some": "Propra ĉefplanko %s nun havas %s ludanto(j) n: %s", "commands.bossbar.set.players.unchanged": "<PERSON><PERSON> chanjis. Iti ludanti esas ja sur la bossbaro kun nulu a adjuntar o diplasar", "commands.bossbar.set.style.success": "Personaligita chefostango %s chanjis stilo", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON> chan<PERSON>. T'esas ja la stilo d'ica bossbaro", "commands.bossbar.set.value.success": "Personaligita chefostango %s chanjis valoro a %s", "commands.bossbar.set.value.unchanged": "<PERSON><PERSON> chan<PERSON>. T'esas ja la valoro d'ica bossbaro", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON> chan<PERSON>. La bossbaro esas ja celita", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON> chan<PERSON>. La bossbaro esas ja videbla", "commands.bossbar.set.visible.success.hidden": "Personaligita chefostango %s esas nun nevidebla", "commands.bossbar.set.visible.success.visible": "Personaligita chefostango %s esas nun videbla", "commands.bossbar.unknown": "Nula bossbaro existas kun la ID '%s'", "commands.clear.success.multiple": "Forigis %s ero (j) n de %s ludanto(j) j", "commands.clear.success.single": "Forigis %s objekto(j) n de ludanto %s", "commands.clear.test.multiple": "<PERSON><PERSON><PERSON>(j) %s kong<PERSON><PERSON>(j) objekto(j) ĉe %s ludanto(j)", "commands.clear.test.single": "T<PERSON>ita(j) %s kong<PERSON><PERSON>(j) objekto(j) ĉe ludanto %s", "commands.clone.failed": "<PERSON><PERSON> bloki esis kopiis", "commands.clone.overlap": "Le fonto e destino ne povas imbrikar", "commands.clone.success": "Sukcese klonis %s bloko(j) n", "commands.clone.toobig": "Tro multa bloki en la areo specigata (maximo %s, specigata %s)", "commands.damage.invulnerable": "Celo estas nevundebla al la donita tipo de difekto", "commands.damage.success": "Aplikis %s difekton al %s", "commands.data.block.get": "%s ye bloki %s, %s, %s pos skala faktoro di %s esas %s", "commands.data.block.invalid": "La skopobloko ne esas blokento", "commands.data.block.modified": "Modifikita bloko-doanji di %s, %s, %s", "commands.data.block.query": "%s, %s, %s havas la sequanta bloko-doanji: %s", "commands.data.entity.get": "%s ye %s pos skalo-faktoro di %s esas %s", "commands.data.entity.invalid": "<PERSON>e povas modifikar donaji di ludanto", "commands.data.entity.modified": "Modifikita ento-donaji di %s", "commands.data.entity.query": "%s havas la sequanta ento-donaji: %s", "commands.data.get.invalid": "Ne povas obtenar %s; nur tagi nombrala esas permisata", "commands.data.get.multiple": "Ica argumento acceptas NBT-valoro sola", "commands.data.get.unknown": "Ne povis obtenar %s; tago ne existas", "commands.data.merge.failed": "<PERSON><PERSON> chanji<PERSON>. La propaji specigita ja havas ica valori", "commands.data.modify.expected_list": "Expektis listo, recevis: %s", "commands.data.modify.expected_object": "Expektis ento, recevis: %s", "commands.data.modify.expected_value": "<PERSON><PERSON><PERSON> valoro, ricevis: %s", "commands.data.modify.invalid_index": "Indexo di list nevalida: %s", "commands.data.modify.invalid_substring": "Neeblaj subigitaj indeksoj de subteksto: %s ĝis %s", "commands.data.storage.get": "%s en konserveso %s pos skalfaktoro di %s en %s", "commands.data.storage.modified": "Konserveso modifikita %s", "commands.data.storage.query": "Konserveso %s havas la kontenti sequenta: %s", "commands.datapack.create.already_exists": "Pack with name '%s' already exists", "commands.datapack.create.invalid_full_name": "Invalid new pack name '%s'", "commands.datapack.create.invalid_name": "Invalid characters in new pack name '%s'", "commands.datapack.create.io_failure": "Can't create pack with name '%s', check logs", "commands.datapack.create.metadata_encode_failure": "Failed to encode metadata for pack with name '%s': %s", "commands.datapack.create.success": "Created new empty pack with name '%s'", "commands.datapack.disable.failed": "Pako '%s' ne esas enablita!", "commands.datapack.disable.failed.feature": "Pack '%s' cannot be disabled, since it is part of an enabled flag!", "commands.datapack.enable.failed": "Pako '%s' esas ja enablita!", "commands.datapack.enable.failed.no_flags": "La pako '%s' ne povas esti ebligita, ĉar postulas flagoj ne estas ebligitaj en ĉi tiu mondo: %s!", "commands.datapack.list.available.none": "Existas nula plusa donaji-paki disponebla", "commands.datapack.list.available.success": "Haveblas %s datum-pakaĵo(j): %s", "commands.datapack.list.enabled.none": "Existas nula donaji-paki disponebla", "commands.datapack.list.enabled.success": "Haveblas %s aktivigi<PERSON>j datum-pako(j): %s", "commands.datapack.modify.disable": "Deshabilitas datumpacko %s", "commands.datapack.modify.enable": "Inicias datumpack %s", "commands.datapack.unknown": "Donaji-pako nekonocata '%s'", "commands.debug.alreadyRunning": "La tikoprofililo jam komencas", "commands.debug.function.noRecursion": "Ne povas traci el interne de funkcio", "commands.debug.function.noReturnRun": "T<PERSON><PERSON> ne povas esti uzata kun 'return run", "commands.debug.function.success.multiple": "Tracitaj %s komando(j) el %s funkcio(j) al dosiero de eligo %s", "commands.debug.function.success.single": "Tracitaj %s komando(j) el la funkcio '%s' al elira dosiero %s", "commands.debug.function.traceFailed": "Malsukcesis traci <PERSON>", "commands.debug.notRunning": "La takta profililo ne komencis", "commands.debug.started": "Komencita takta profilado", "commands.debug.stopped": "Haltita takta profilado post %s sekundoj kaj %s taktoj (%s taktoj je sekundo)", "commands.defaultgamemode.success": "La prestablisita ludo-modo esas nun %s", "commands.deop.failed": "<PERSON><PERSON> chan<PERSON>. La ludanto ne esas operacanto", "commands.deop.success": "Nun %s ja ne esas operacano di servero", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "Desfacileso ne chanjis; ol ja esas definita a %s", "commands.difficulty.query": "La desfacileso esas %s", "commands.difficulty.success": "La desfacileso nun esas %s", "commands.drop.no_held_items": "Ento ne povas tenar ula objekti", "commands.drop.no_loot_table": "Ento %s ne havas spoili-tabelo", "commands.drop.no_loot_table.block": "Block %s has no loot table", "commands.drop.success.multiple": "Faligis %s objekti", "commands.drop.success.multiple_with_table": "Faligis %s objekti de spoili-tabelo %s", "commands.drop.success.single": "Ĵetis %s %s", "commands.drop.success.single_with_table": "Ĵetis %s %s el profiltabelo %s", "commands.effect.clear.everything.failed": "Skopo havas nula efekti efacar", "commands.effect.clear.everything.success.multiple": "Forigita omna efekti de %s skopi", "commands.effect.clear.everything.success.single": "Forigita omna efekti de %s", "commands.effect.clear.specific.failed": "Skopo ne havas la efekto demandata", "commands.effect.clear.specific.success.multiple": "Forigita efekto %s de %s skopi", "commands.effect.clear.specific.success.single": "Forigita efekto %s de %s", "commands.effect.give.failed": "Ne povas aplikar ica efekto (skopo esas sive imuna de efekti, o havas ulo plu forta)", "commands.effect.give.success.multiple": "Aplikita efekto %s a(d) %s skopi", "commands.effect.give.success.single": "Aplikita efekto %s a(d) %s", "commands.enchant.failed": "<PERSON><PERSON> chanjis. <PERSON><PERSON><PERSON> sive havas nula objekto en lia manui o la charmivo ne povis esar aplikita", "commands.enchant.failed.entity": "%s ne validas kiel ento por ĉi tiu komando", "commands.enchant.failed.incompatible": "%s ne esas konciliebla kun ita charmivo", "commands.enchant.failed.itemless": "%s ne tenas irga objekto", "commands.enchant.failed.level": "%s esas plu granda kam la nivelo maxima di %s, qua suportesas per ita charmivo", "commands.enchant.success.multiple": "Aplikita sorco %s a %s enti", "commands.enchant.success.single": "Aplikita sorco %s ad objekto di %s", "commands.execute.blocks.toobig": "Tro multaj blokoj en la indikita areo (maksimumo %s, indikitaj %s)", "commands.execute.conditional.fail": "Examino esis nesucesanta", "commands.execute.conditional.fail_count": "Examino esis nesucesanta, quanto: %s", "commands.execute.conditional.pass": "Examino esis sucesanta", "commands.execute.conditional.pass_count": "Examino esis sucesanta, quanto: %s", "commands.execute.function.instantiationFailure": "Malsukcesis instancigi funkcion %s: %s", "commands.experience.add.levels.success.multiple": "Donita %s experienco-niveli a(d) %s luderi", "commands.experience.add.levels.success.single": "Donita %s experienco-niveli a(d) %s", "commands.experience.add.points.success.multiple": "Donita %s pointi di experienco a %s luderi", "commands.experience.add.points.success.single": "Donita %s pointi di experienco a %s", "commands.experience.query.levels": "%s havas %s experienco-niveli", "commands.experience.query.points": "%s havas %s pointi di experienco", "commands.experience.set.levels.success.multiple": "Pozita %s experienco-niveli ye %s luderi", "commands.experience.set.levels.success.single": "Pozita %s experienco-nivelo ye %s", "commands.experience.set.points.invalid": "Ne povas definar pointi di experienco super la pointi maxima por la nuna nivelo di ludanto", "commands.experience.set.points.success.multiple": "Pozita %s pointi di experienco ye %s luderi", "commands.experience.set.points.success.single": "Pozita %s pointi di experienco ye %s", "commands.fill.failed": "<PERSON><PERSON> bloki esis plenigis", "commands.fill.success": "Sukcese plenigis %s blokon (blokojn)", "commands.fill.toobig": "Tro multaj blokoj en la indikita areo (maksimumo %s, indikitaj %s)\"", "commands.fillbiome.success": "Biomezuri staranta inter %s, %s, %s kaj %s, %s, %s", "commands.fillbiome.success.count": "%s Izolita bioma enigo/enigoj difinitaj inter %s, %s, %s kaj %s, %s, %s", "commands.fillbiome.toobig": "Tro multaj blokoj en la difinita volumo (maksimumo %s, difinita %s)", "commands.forceload.added.failure": "Nula chunk indikesis por charjo koaktanta", "commands.forceload.added.multiple": "Indikis %s chunki en %s de %s a %s charjesar koaktante", "commands.forceload.added.none": "Nula chunki charjita koaktante trovesis en %s", "commands.forceload.added.single": "Indikis chunko %s en %s a charjesar koaktante", "commands.forceload.list.multiple": "%s chunki charjita koaktante trovesis en %s a: %s", "commands.forceload.list.single": "Chunko charjita koaktante trovesis en %s a: %s", "commands.forceload.query.failure": "Chunko a %s en %s ne indikesas charjar koaktante", "commands.forceload.query.success": "Chunko a %s en %s indikesas charjar koaktante", "commands.forceload.removed.all": "Desindikis omna chunki charjita koaktante en %s", "commands.forceload.removed.failure": "Nula chunk efacesis de la charjo koaktanta", "commands.forceload.removed.multiple": "Desindikis %s chunki en %s de %s a %s charjar koaktante", "commands.forceload.removed.single": "Desindikis chunko %s en %s charjar koaktante", "commands.forceload.toobig": "Tro multa chunk en la selektita areo (maxime %s, selektite %s)", "commands.function.error.argument_not_compound": "Nevalida tipa argumento: %s, atendita <PERSON>", "commands.function.error.missing_argument": "Mankanta argumento %2$s al la funkcio %1$s", "commands.function.error.missing_arguments": "Mankas argumentojn al la funkcio %s", "commands.function.error.parse": "Pli detaligi pri la makro %s: La komando '%s' kaŭzis eraron: %s", "commands.function.instantiationFailure": "Malsukcesis la enigo de la funkcio %s: %s", "commands.function.result": "La funkcio %s redonis %s", "commands.function.scheduled.multiple": "<PERSON><PERSON><PERSON><PERSON>: %s", "commands.function.scheduled.no_functions": "Ne trovas iajn funk<PERSON>jn por la nomo %s", "commands.function.scheduled.single": "Funkciante la funkcio %s", "commands.function.success.multiple": "Efektivigis %s komando(j) n el %s funkcio(j)", "commands.function.success.multiple.result": "Efektivigis %s funkcio(j) n", "commands.function.success.single": "Efektivigis %s komando(j) n el la funkcio '%s'", "commands.function.success.single.result": "La funkcio '%2$s' redonis %1$s", "commands.gamemode.success.other": "Definar ludo-modo di %s a %s", "commands.gamemode.success.self": "Definar su ipsa ludo-modo a %s", "commands.gamerule.query": "Ludo-regulo %s esas aktuale establisita a(d): %s", "commands.gamerule.set": "Ludo-regulo %s esas nun establisita a(d): %s", "commands.give.failed.toomanyitems": "Ne povas doni pli ol %s el %s", "commands.give.success.multiple": "Donita %s %s a(d) %s luderi", "commands.give.success.single": "Donita %s %s a(d) %s", "commands.help.failed": "Komando nekonocata o permisi nesuficanta", "commands.item.block.set.success": "<PERSON><PERSON><PERSON><PERSON> ranuro a %s, %s, %s kun %s", "commands.item.entity.set.success.multiple": "<PERSON><PERSON><PERSON><PERSON> en %s enti kun %s", "commands.item.entity.set.success.single": "<PERSON><PERSON><PERSON><PERSON> ran<PERSON> en %s kun %s", "commands.item.source.no_such_slot": "La fonto ne havas pozicion %s", "commands.item.source.not_a_container": "La fonto pozicio %s, %s, %s ne estas kontenilo", "commands.item.target.no_changed.known_item": "Neniu celo akceptis la objekton %s en la pozicion %s", "commands.item.target.no_changes": "Neniu celo akceptis la objekton en la pozicion %s", "commands.item.target.no_such_slot": "La skopo ne havas la ranuro %s", "commands.item.target.not_a_container": "La celo pozicio %s, %s, %s ne estas kontenilo", "commands.jfr.dump.failed": "Malsukcesis elverŝi JFR registrado: %s", "commands.jfr.start.failed": "Malsukcesis komenci JFR profiladon", "commands.jfr.started": "JFR profilado komenciĝis", "commands.jfr.stopped": "JFR profilado haltiĝis kaj estis elverŝita al %s", "commands.kick.owner.failed": "Ne eblas elpuŝi la servon posedanton en LAN ludo", "commands.kick.singleplayer.failed": "<PERSON><PERSON> kick in an offline singleplayer game", "commands.kick.success": "Ekpulsita %s: %s", "commands.kill.success.multiple": "Mortigita %s enti", "commands.kill.success.single": "Mortigis %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Estas %s el maksimumo de %s ludantoj enrete: %s", "commands.locate.biome.not_found": "Ne eblis trovi biomon de tipo \"%s\" en akceptebla distanco", "commands.locate.biome.success": "La maxim proxima %s esas ye %s (%s bloki fore)", "commands.locate.poi.not_found": "Ne eblis trovi punkton de intereso de tipo \"%s\" en akceptebla distanco", "commands.locate.poi.success": "La maxim proxima %s esas ye %s (%s bloki fore)", "commands.locate.structure.invalid": "Ne ekzistas strukturo kun tipo \"%s\"", "commands.locate.structure.not_found": "Ne eblis trovi strukturon de tipo \"%s\" proksime", "commands.locate.structure.success": "La maxim proxima %s esas ye %s (%s bloki fore)", "commands.message.display.incoming": "%s susuris a vu: %s", "commands.message.display.outgoing": "Vu susuras a %s: %s", "commands.op.failed": "<PERSON><PERSON> chan<PERSON>. La ludanto ja esas operacanto", "commands.op.success": "Divenita %s servero-operacanto", "commands.pardon.failed": "<PERSON><PERSON> chan<PERSON>. La ludanto ne esas blokusata", "commands.pardon.success": "Desblokusita %s", "commands.pardonip.failed": "Nulo chanjis. Ita IP ne esas blokusata", "commands.pardonip.invalid": "IP-adreso nevalida", "commands.pardonip.success": "Desblokusita IP-adreso %s", "commands.particle.failed": "La partikulo ne esis videbla por ulu", "commands.particle.success": "Monstras partikulo %s", "commands.perf.alreadyRunning": "La efikeca profiliro jam komenciĝis", "commands.perf.notRunning": "La efikeca profiliro ne komenciĝis", "commands.perf.reportFailed": "<PERSON><PERSON><PERSON> krear raporto pri debugar", "commands.perf.reportSaved": "Kreis raporto pri debugar en %s", "commands.perf.started": "Ko<PERSON><PERSON> profiliro de la efikeco dum 10 sekundoj (uzu '/perf stop' por halti antaŭe)", "commands.perf.stopped": "Haltigis la efikeca profiliro post %s sekundo(j) kaj %s bloko(j) (%s bloko(j) je sekundo)", "commands.place.feature.failed": "Malsukcesis lokigi ecofon", "commands.place.feature.invalid": "Ne ekzistas ecofon kun tipo \"%s\"", "commands.place.feature.success": "Lokigis \"%s\" ĉe %s, %s, %s", "commands.place.jigsaw.failed": "Malsukcesis generi ĉifon", "commands.place.jigsaw.invalid": "Ne ekzistas ŝablona kradparkejo kun tipo \"%s\"", "commands.place.jigsaw.success": "Generita artaĵo ĉe %s, %s, %s", "commands.place.structure.failed": "Malsukcesis loki strukturon", "commands.place.structure.invalid": "Ne ekzistas strukturo kun tipo \"%s\"", "commands.place.structure.success": "Generita strukturo \"%s\" ĉe %s, %s, %s", "commands.place.template.failed": "Malsukcesis loki ŝablonon", "commands.place.template.invalid": "Ne ekzistas ŝablono kun id \"%s\"", "commands.place.template.success": "Ŝablono \"%s\" ŝargita ĉe %s, %s, %s", "commands.playsound.failed": "La sono esas tro distanta esar audita", "commands.playsound.success.multiple": "Pleita sono %s a(d) %s luderi", "commands.playsound.success.single": "Pleita sono %s a(d) %s", "commands.publish.alreadyPublished": "Ludo di plura ludanti ja hostesas en portuo %s", "commands.publish.failed": "<PERSON>e povas hostar ludo lokala", "commands.publish.started": "Ludo lokala esas hostata en portuo %s", "commands.publish.success": "Plura luderi ludo esas nun hostita ye portuo %s", "commands.random.error.range_too_large": "La intervalo de la hazarda valoro devas esti plej multe 2147483646", "commands.random.error.range_too_small": "La intervalo de la hazarda valoro devas esti minimume 2", "commands.random.reset.all.success": "Rekomencigi %s hazarda(j) n sekvenco(j) n", "commands.random.reset.success": "Rekomencigi hazardan sekvencon %s", "commands.random.roll": "%s ruligis %s (de %s al %s)", "commands.random.sample.success": "Hazarda valoro: %s", "commands.recipe.give.failed": "Nula nova recepti esis lernatis", "commands.recipe.give.success.multiple": "Klefapertita %s recepti por %s luderi", "commands.recipe.give.success.single": "Klefapertita %s recepti por %s", "commands.recipe.take.failed": "<PERSON>ula recepti povis esar obliviita", "commands.recipe.take.success.multiple": "Prenita %s recepti de %s luderi", "commands.recipe.take.success.single": "Prenita %s recepti de %s", "commands.reload.failure": "<PERSON><PERSON><PERSON>; retenas datumi anciena", "commands.reload.success": "<PERSON><PERSON><PERSON><PERSON>!", "commands.ride.already_riding": "%s jam rajdas sur %s", "commands.ride.dismount.success": "%s ĉesis rajdi sur %s", "commands.ride.mount.failure.cant_ride_players": "Ludant<PERSON><PERSON> ne povas esti rajditaj", "commands.ride.mount.failure.generic": "%s ne sukcesis komenci rajdi %s", "commands.ride.mount.failure.loop": "Ne eblas rajdi enton mem aŭ iun el ties pasaĝeroj", "commands.ride.mount.failure.wrong_dimension": "Ne eblas rajdi enton en malsama dimensio", "commands.ride.mount.success": "%s ekis rajdi %s", "commands.ride.not_riding": "%s ne rajdas ian ajn veturilon", "commands.rotate.success": "Rotated %s", "commands.save.alreadyOff": "Konservo esas ja desaktiva", "commands.save.alreadyOn": "Konservo esas ja aktiva", "commands.save.disabled": "Automata konservo esas nun neaktiva", "commands.save.enabled": "Automata konservo esas nun habilitita", "commands.save.failed": "Ne povas konservas la ludo (kad esas sat spaco sur la disko?)", "commands.save.saving": "Konservas la ludo (vartez kelka sekundi)", "commands.save.success": "<PERSON><PERSON><PERSON><PERSON><PERSON> ludo", "commands.schedule.cleared.failure": "Nula preparadi kun ID %s", "commands.schedule.cleared.success": "Forigis %s planaron(jn) kun ido %s", "commands.schedule.created.function": "Funkcio '%s' planita je %s bato(j) ĉe ludadtempo %s", "commands.schedule.created.tag": "Preparis tago '%s' en %s tiki a ludtempo %s", "commands.schedule.macro": "Can't schedule a macro", "commands.schedule.same_tick": "<PERSON>e povas preparar por tiko aktuala", "commands.scoreboard.objectives.add.duplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON> ja existas kun ita nomo", "commands.scoreboard.objectives.add.success": "Kreita nova objektali %s", "commands.scoreboard.objectives.display.alreadyEmpty": "<PERSON><PERSON> chan<PERSON>. Ita montro-ranuro ja esas vakua", "commands.scoreboard.objectives.display.alreadySet": "<PERSON><PERSON> chanji<PERSON>. Ita montro-ranuro ja montras ita objektalo", "commands.scoreboard.objectives.display.cleared": "Vakuigita ula skopi en ranuro %s", "commands.scoreboard.objectives.display.set": "Pozita ranuro %s por montrar skopo %s", "commands.scoreboard.objectives.list.empty": "<PERSON><PERSON><PERSON> nula objektali", "commands.scoreboard.objectives.list.success": "Estas %s celo(j): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Malkapablis aŭtomatan ĝisdatigon de la ekranon por celo %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Kapabligita aŭtomatan ĝisdatigon de la ekrano por celo %s", "commands.scoreboard.objectives.modify.displayname": "Ŝanĝis la montran nomon de %s al %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Forviŝis la defaŭltan nombroformaton de la celo %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Ŝanĝita defaŭltan nombroformaton de la celo %s", "commands.scoreboard.objectives.modify.rendertype": "Ŝanĝis la desegna tipon de la celo %s", "commands.scoreboard.objectives.remove.success": "Forigita objektali %s", "commands.scoreboard.players.add.success.multiple": "Adicionita %s a %s por %s enti", "commands.scoreboard.players.add.success.single": "Adicionita %s a %s por %s (nun %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Forviŝis montritan nomon por %s entecoj en %s", "commands.scoreboard.players.display.name.clear.success.single": "Forviŝis montritan nomon por %s en %s", "commands.scoreboard.players.display.name.set.success.multiple": "Ŝanĝis montritan nomon al %s por %s entecoj en %s", "commands.scoreboard.players.display.name.set.success.single": "Ŝanĝis montritan nomon al %s por %s en %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Forviŝis la nombroformaton por %s entecoj en %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Forviŝis la nombroformaton por %s en %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Ŝanĝis nombroformaton por %s entecoj en %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Ŝanĝis nombroformaton por %s en %s", "commands.scoreboard.players.enable.failed": "<PERSON><PERSON> chanji<PERSON>. Ita destensilo esas ja habilitis", "commands.scoreboard.players.enable.invalid": "Habilitar nur functionas sur destensil-objektali", "commands.scoreboard.players.enable.success.multiple": "Habilitita deskuplo %s por %s enti", "commands.scoreboard.players.enable.success.single": "Habilitis detensilo %s por %s", "commands.scoreboard.players.get.null": "Ne povas obtenar valoro di %s por %s; nulo esas definita", "commands.scoreboard.players.get.success": "%s havas %s %s", "commands.scoreboard.players.list.empty": "Ne esas detektita enti", "commands.scoreboard.players.list.entity.empty": "%s ne havas punti por montrar", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s havas %s poento(j) n:", "commands.scoreboard.players.list.success": "Estas %s spureblaj ento/entoj: %s", "commands.scoreboard.players.operation.success.multiple": "Aktualigita %s por %s enti", "commands.scoreboard.players.operation.success.single": "Agordis %s por %s al %s", "commands.scoreboard.players.remove.success.multiple": "Forigita %s de %s por %s enti", "commands.scoreboard.players.remove.success.single": "Forigita %s de %s por %s (nun %s)", "commands.scoreboard.players.reset.all.multiple": "Ripozita omna punti por %s enti", "commands.scoreboard.players.reset.all.single": "Ripozita omna punti por %s", "commands.scoreboard.players.reset.specific.multiple": "Ripozita %s por %s enti", "commands.scoreboard.players.reset.specific.single": "Ripozita %s por %s", "commands.scoreboard.players.set.success.multiple": "Pozita %s por %s enti a %s", "commands.scoreboard.players.set.success.single": "Pozita %s por %s a(d) %s", "commands.seed.success": "Semino: %s", "commands.setblock.failed": "Ne povis pozar la bloko", "commands.setblock.success": "Chanjita la bloko a(d) %s, %s, %s", "commands.setidletimeout.success": "<PERSON>un la daŭro de senokupaĵo por la ludanto estas %s minuto(j)", "commands.setidletimeout.success.disabled": "The player idle timeout is now disabled", "commands.setworldspawn.failure.not_overworld": "Nur eblas agordi la naskiĝejon de la ĉefa mondo", "commands.setworldspawn.success": "Agordis la mondan elirpunkton al %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Agordis la elirpunkton al %s, %s, %s [%s] en %s por %s ludantoj", "commands.spawnpoint.success.single": "Agordis naskiĝpunkton al %s, %s, %s [%s] en %s por %s", "commands.spectate.not_spectator": "%s ne esas en modo spektanta", "commands.spectate.self": "<PERSON>e povas spektar su", "commands.spectate.success.started": "Nun spektas %s", "commands.spectate.success.stopped": "Ne plus spektas ento", "commands.spreadplayers.failed.entities": "Ne povis disvastigi %s enton/entojn ĉirkaŭ %s, %s (tro multaj entoj por la spaco - provu uzi disvastigon de plejmulte %s)", "commands.spreadplayers.failed.invalid.height": "Nevalida maksimuma alteco %s; atendata pli ol la minimumo de la mondo %s", "commands.spreadplayers.failed.teams": "Ne povis disvastigi %s teamo(j) n ĉirkaŭ %s, %s (tro multaj entoj por la spaco - provu uzi disvastigon de plejmulte %s)", "commands.spreadplayers.success.entities": "Disvastigi %s ludanto(j) n ĉirkaŭ %s, %s kun avera distanco de %s blokoj inter ili", "commands.spreadplayers.success.teams": "Isvastigi %s ekipojn (j) ĉirkaŭ %s, %s kun avera distanco de %s blokoj inter ili", "commands.stop.stopping": "Cesas la servero", "commands.stopsound.success.source.any": "Cesita omna '%s' soni", "commands.stopsound.success.source.sound": "Cesita sono '%s' di fonto '%s'", "commands.stopsound.success.sourceless.any": "<PERSON>sita omna soni", "commands.stopsound.success.sourceless.sound": "Cesita sono '%s'", "commands.summon.failed": "<PERSON>e povas vokitar ento", "commands.summon.failed.uuid": "Ne povas alvoki la enton pro duoblaj UUID-oj", "commands.summon.invalidPosition": "Nevalida pozicio por alvoko", "commands.summon.success": "Sumnita nova %s", "commands.tag.add.failed": "Skopo sive ja havas la tago o havas tro multa tagi", "commands.tag.add.success.multiple": "Adjuntita etiketo '%s' a %s enti", "commands.tag.add.success.single": "Adjuntita etiketo '%s' a %s", "commands.tag.list.multiple.empty": "Ne esas etiketi ye la %s enti", "commands.tag.list.multiple.success": "La %s enti havas %s totala etiketi: %s", "commands.tag.list.single.empty": "%s ne havas etiketi", "commands.tag.list.single.success": "%s havas %s etiketi: %s", "commands.tag.remove.failed": "Skopo ne havas ica tago", "commands.tag.remove.success.multiple": "Forigita etiketo '%s' de %s enti", "commands.tag.remove.success.single": "Forigita etiketo '%s' de %s", "commands.team.add.duplicate": "Esquado ja existas kun ita nomo", "commands.team.add.success": "Kreita esquado %s", "commands.team.empty.success": "Origis %s membrojn (j) de teamo %s", "commands.team.empty.unchanged": "<PERSON><PERSON> chanji<PERSON>. Ita esquado ja esas vakua", "commands.team.join.success.multiple": "Adicionita %s membri ad esquado %s", "commands.team.join.success.single": "Adicionita %s ad esquado %s", "commands.team.leave.success.multiple": "Forigita %s membri de ula esquado", "commands.team.leave.success.single": "Forigita %s de ula esquado", "commands.team.list.members.empty": "Ne esas membri en esquado %s", "commands.team.list.members.success": "Teamo %s havas %s membrojn (j): %s", "commands.team.list.teams.empty": "Ne esas esquadi", "commands.team.list.teams.success": "Estas %s teamo(j): %s", "commands.team.option.collisionRule.success": "Koliziono-regulo por esquado %s esas nun \"%s\"", "commands.team.option.collisionRule.unchanged": "<PERSON>ulo chanjis. Regulo di koliziono ja esas ita valoro", "commands.team.option.color.success": "Aktualigita la koloro por esquado %s a %s", "commands.team.option.color.unchanged": "<PERSON><PERSON> chanjis. Ita esquado ja havas ita koloro", "commands.team.option.deathMessageVisibility.success": "Videblesa morto-mesajo por esquado %s esas nun \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON><PERSON> chanji<PERSON>. Videbleso di mortmesaji ja esas ita valoro", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON><PERSON> chanjis. <PERSON><PERSON> amika<PERSON> esas ja deshabilitata por ita esquado", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON><PERSON> chanjis. <PERSON><PERSON> amika<PERSON> esas ja habilitata por ita esquado", "commands.team.option.friendlyfire.disabled": "Neaktivita amikala fairo por esquado %s", "commands.team.option.friendlyfire.enabled": "Habilitita amikala fairo por esquado %s", "commands.team.option.name.success": "Ĝisdatigis la nomon de teamo %s", "commands.team.option.name.unchanged": "<PERSON><PERSON> chanji<PERSON>. Ita esquado ja havas ita nomo", "commands.team.option.nametagVisibility.success": "Videblesa nomo-etiketo por esquado %s esas nun \"%s\"", "commands.team.option.nametagVisibility.unchanged": "<PERSON><PERSON> chanji<PERSON>. Videbleso di nomtagi ja esas ita valoro", "commands.team.option.prefix.success": "Prefixon d'esquado definis a %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON><PERSON> chanji<PERSON>. Ita esquado ne povas ja vidar coesquadi nevidebla", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON><PERSON> chanji<PERSON>. Ita esquado povas ja vidar coesquadi nevidebla", "commands.team.option.seeFriendlyInvisibles.disabled": "Esquado %s ne povas vidar nevidebla esquadani plu-longatempe", "commands.team.option.seeFriendlyInvisibles.enabled": "Esquado %s povas nun vidar nevidebla esquadani", "commands.team.option.suffix.success": "Suffixon d'esquado definis a %s", "commands.team.remove.success": "Efacis esquado %s", "commands.teammsg.failed.noteam": "Vu devas esar en esquado por komunikar kun via esquado", "commands.teleport.invalidPosition": "Nevalida pozicio por teleporti", "commands.teleport.success.entity.multiple": "Teleportita %s enti a(d) %s", "commands.teleport.success.entity.single": "Teleportis %s a %s", "commands.teleport.success.location.multiple": "Teleportita %s enti a %s, %s, %s", "commands.teleport.success.location.single": "Teleportis %s a %s, %s, %s", "commands.test.batch.starting": "Starting environment %s batch %s", "commands.test.clear.error.no_tests": "Could not find any tests to clear", "commands.test.clear.success": "Cleared %s structure(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Click to copy to clipboard", "commands.test.create.success": "Created test setup for test %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Found no test instances", "commands.test.error.non_existant_test": "Test %s could not be found", "commands.test.error.structure_not_found": "Test structure %s could not be found", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "The structure size must be less than %s blocks along each axis", "commands.test.locate.done": "Finished locating, found %s structure(s)", "commands.test.locate.found": "Found structure at: %s (distance: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "No tests to run", "commands.test.relative_position": "Position relative to %s: %s", "commands.test.reset.error.no_tests": "Could not find any tests to reset", "commands.test.reset.success": "Reset %s structure(s)", "commands.test.run.no_tests": "No tests found", "commands.test.run.running": "Running %s test(s)...", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "All required tests passed :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "Percentiloj: P50: %sms P95: %sms P99: %sms, ekzemplo: %s", "commands.tick.query.rate.running": "<PERSON><PERSON> t<PERSON>: %s je sekundo.\nAvera tempo je tiketo: %sms (Celo: %sms)", "commands.tick.query.rate.sprinting": "<PERSON><PERSON> t<PERSON>: %s je sekundo (ignorita, nur referecita).\nAvera tempo je tiketo: %sms", "commands.tick.rate.success": "Agordi la celan tikrateon je %s je sekundo", "commands.tick.sprint.report": "Ku<PERSON>do kompletigita per %s bloko(j) je sekundo, aŭ %s ms je bloko", "commands.tick.sprint.stop.fail": "Neniu bloksprinto en progreso", "commands.tick.sprint.stop.success": "Bloksprinto interrompiĝis", "commands.tick.status.frozen": "La ludilo frostiĝis", "commands.tick.status.lagging": "La ludo funkcias, sed ne kapablas sekvi la celitan tikfrekvencan ritmon", "commands.tick.status.running": "La ludo funkcias normale", "commands.tick.status.sprinting": "La ludo kuradas rapide", "commands.tick.step.fail": "Ne povas paŝi la ludon - la ludo devas unue frostiĝi", "commands.tick.step.stop.fail": "Neniu paŝo de bloko en progreso", "commands.tick.step.stop.success": "Bloko de paŝo interrompiĝis", "commands.tick.step.success": "Paŝante %s bloko(j)", "commands.time.query": "La tempo esas %s", "commands.time.set": "Definar la tempo a %s", "commands.title.cleared.multiple": "Vakuigita tituli por %s luderi", "commands.title.cleared.single": "Vakuigita tituli por %s", "commands.title.reset.multiple": "Ripozita titula ajusti por %s luderi", "commands.title.reset.single": "Ripozita titula ajusti por %s", "commands.title.show.actionbar.multiple": "Montranta nova funcionostango por %s luderi", "commands.title.show.actionbar.single": "Montras nova subtitulo por %s", "commands.title.show.subtitle.multiple": "Montras nova subtitulo por %s luderi", "commands.title.show.subtitle.single": "Montras nova subtitulo por %s", "commands.title.show.title.multiple": "Montras nova titulo por %s luderi", "commands.title.show.title.single": "Montras nova titulo por %s", "commands.title.times.multiple": "Chanjita la titulo montrado foyi por %s luderi", "commands.title.times.single": "Chanjita titulo montrado foyi por %s", "commands.transfer.error.no_players": "Oni devas indiki minimume unu ludanto por transloki", "commands.transfer.success.multiple": "Transloko de %s ludantoj al %s:%s", "commands.transfer.success.single": "Transloko de %s al %s:%s", "commands.trigger.add.success": "Deskuplita %s (adicionita %s a valoro)", "commands.trigger.failed.invalid": "Vu povas nur detensilar objektali dil tipo 'trigger'", "commands.trigger.failed.unprimed": "Vu ne povas destensilar ica objektalo ankore", "commands.trigger.set.success": "Deskuplita %s (pozita valoro a %s)", "commands.trigger.simple.success": "Deskuplita %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "La vetero esas nun klara", "commands.weather.set.rain": "La vetero esas nun pluvoza", "commands.weather.set.thunder": "La vetero esas nun pluvo e tondri", "commands.whitelist.add.failed": "Ludanto ja esas sur listo-blanka", "commands.whitelist.add.success": "Adjuntis %s al listo-blanka", "commands.whitelist.alreadyOff": "Listo-blanka ja esas desaktivata", "commands.whitelist.alreadyOn": "Listo-blanka ja esas aktivata", "commands.whitelist.disabled": "Listo blanka esas nun desengajata", "commands.whitelist.enabled": "Listo blanka esas nun enswichata", "commands.whitelist.list": "Estas %s ludanto(j) en la blanka listo: %s", "commands.whitelist.none": "Ne esas luderi an la listo blanka", "commands.whitelist.reloaded": "<PERSON><PERSON><PERSON><PERSON> la listo-blanka", "commands.whitelist.remove.failed": "Ludanto ne esas sur listo-blanka", "commands.whitelist.remove.success": "Diplasis %s del listo-blanka", "commands.worldborder.center.failed": "<PERSON><PERSON> chanji<PERSON>. La bordo di mondo ja esas centrigita ibe", "commands.worldborder.center.success": "Pozita la centro di la bordo di la mondo a %s, %s", "commands.worldborder.damage.amount.failed": "<PERSON><PERSON> chanji<PERSON>. La domajo di bordo ja esas ita quanto", "commands.worldborder.damage.amount.success": "Agordi la mondmuron-daman al %s po bloko ĉiu sekundo", "commands.worldborder.damage.buffer.failed": "<PERSON><PERSON> chan<PERSON>. La domaj-bufro di bordo ja esas ita disto", "commands.worldborder.damage.buffer.success": "Agordi la mondmuron-damanan rezervon al %s bloko(j)", "commands.worldborder.get": "La monda muro nun estas %s bloko(j) larĝa", "commands.worldborder.set.failed.big": "La monda muro ne povas esti pli larĝa ol %s blokoj", "commands.worldborder.set.failed.far": "La monda muro ne povas esti pli ekstera ol %s blokoj", "commands.worldborder.set.failed.nochange": "<PERSON><PERSON> chanji<PERSON>. La bordo di mondo ja esas di ita grandeso", "commands.worldborder.set.failed.small": "<PERSON>rdo di mondo ne povas esar plu mikra ke 1 bloko en larjeso", "commands.worldborder.set.grow": "Augmentanta la mondo-bordo a %s bloki larja en %s sekundi", "commands.worldborder.set.immediate": "Agordi la mondmuron al %s bloko(j) larĝa", "commands.worldborder.set.shrink": "Kontrakci la mondmuron al %s bloko(j) larĝa dum %s sekundo(j)", "commands.worldborder.warning.distance.failed": "<PERSON><PERSON> chanji<PERSON>. La averto di bordo ja esas ita disto", "commands.worldborder.warning.distance.success": "Agordi la mondmuron-avertan distancon al %s bloko(j)", "commands.worldborder.warning.time.failed": "<PERSON><PERSON> chanji<PERSON>. La averto di bordo ja esas ita longeso di tempo", "commands.worldborder.warning.time.success": "Agordi la mondmuron-avertan tempon al %s sekundo(j)", "compliance.playtime.greaterThan24Hours": "Vu ludis dum plu kam 24 hori", "compliance.playtime.hours": "Vu ludis dum %s horo/i", "compliance.playtime.message": "T<PERSON>aj ludadkutimoj povas interferi kun normala ĉiutaga vivo", "connect.aborted": "<PERSON><PERSON><PERSON>", "connect.authorizing": "Acesas...", "connect.connecting": "Konektas al servero...", "connect.encrypting": "Enkriptas...", "connect.failed": "Faliis a konektar al servero", "connect.failed.transfer": "Manko di konekto dum transdonado al la servilo", "connect.joining": "Eniras mondo...", "connect.negotiating": "Negocias...", "connect.reconfiging": "Reagordante...", "connect.reconfiguring": "Reagordante...", "connect.transferring": "Transdono al nova servilo...", "container.barrel": "<PERSON><PERSON>", "container.beacon": "Bali<PERSON>", "container.beehive.bees": "Bees: %s / %s", "container.beehive.honey": "Honey: %s / %s", "container.blast_furnace": "Fandoforno", "container.brewing": "Brulilo-Standeto", "container.cartography_table": "Kartografia <PERSON>lo", "container.chest": "Kof<PERSON>", "container.chestDouble": "Kofrego", "container.crafter": "Metils<PERSON><PERSON>", "container.crafting": "Fabrikado", "container.creative": "Objektselekto", "container.dispenser": "<PERSON><PERSON> Disĵetilo", "container.dropper": "Eliraĵo", "container.enchant": "<PERSON><PERSON><PERSON>", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s lapis<PERSON><PERSON><PERSON>", "container.enchant.lapis.one": "1 lapislazulo", "container.enchant.level.many": "%s niveli di charmivo", "container.enchant.level.one": "1 nivelo di charmivo", "container.enchant.level.requirement": "<PERSON><PERSON><PERSON>: %s", "container.enderchest": "<PERSON><PERSON><PERSON><PERSON>", "container.furnace": "Forno", "container.grindstone_title": "Reparar e dessorcar", "container.hopper": "Funelego", "container.inventory": "Inventaro", "container.isLocked": "%s esas klefagata!", "container.lectern": "Legpulto", "container.loom": "Araneo", "container.repair": "Reparar & nomizar", "container.repair.cost": "Kusto di charmivo: %1$s", "container.repair.expensive": "Tro chera!", "container.shulkerBox": "Ŝulkera skatolo", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "e %s plusa...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Fumkaptisto", "container.spectatorCantOpen": "Ne povas apertar. Objekti ne generesas ankore.", "container.stonecutter": "Ŝtontranĉisto", "container.upgrade": "Altigo de la ekipaĵo", "container.upgrade.error_tooltip": "La ero ne povas esti altigita tiel ĉi", "container.upgrade.missing_template_tooltip": "Aldoni ŝmelantan ŝablon", "controls.keybinds": "Klavobindaj...", "controls.keybinds.duplicateKeybinds": "Ĉi tiu klavo ankaŭ estas uzata por:\n%s", "controls.keybinds.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "controls.reset": "Ridefinar", "controls.resetAll": "<PERSON><PERSON><PERSON> klavi", "controls.title": "<PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Voluntez selektar biomo", "createWorld.customize.buffet.title": "Personalizigo di mondo bufeta", "createWorld.customize.flat.height": "Alteso", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Infro - %s", "createWorld.customize.flat.layer.top": "Supro - %s", "createWorld.customize.flat.removeLayer": "Efacar strato", "createWorld.customize.flat.tile": "Materio di strato", "createWorld.customize.flat.title": "Personalizo di superplana", "createWorld.customize.presets": "Predefini", "createWorld.customize.presets.list": "Alternative, hike esas kelka ni facis plu frue!", "createWorld.customize.presets.select": "<PERSON><PERSON> prede<PERSON>o", "createWorld.customize.presets.share": "Ka vu volas partigar vua predefino? U<PERSON>z la suba buxo!", "createWorld.customize.presets.title": "<PERSON><PERSON><PERSON><PERSON> predefino", "createWorld.preparing": "Preparas por kreado di mondo...", "createWorld.tab.game.title": "<PERSON><PERSON>", "createWorld.tab.more.title": "Plu", "createWorld.tab.world.title": "<PERSON><PERSON>", "credits_and_attribution.button.attribution": "Atribuado", "credits_and_attribution.button.credits": "<PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.button.licenses": "<PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.screen.title": "<PERSON><PERSON><PERSON><PERSON>", "dataPack.bundle.description": "Habilitas experimentala fasko objekto", "dataPack.bundle.name": "<PERSON><PERSON>", "dataPack.locator_bar.description": "Show the direction of other players in multiplayer", "dataPack.locator_bar.name": "Locator Bar", "dataPack.minecart_improvements.description": "Improved movement for Minecarts", "dataPack.minecart_improvements.name": "Minecart Improvements", "dataPack.redstone_experiments.description": "Experimental Redstone changes", "dataPack.redstone_experiments.name": "Redstone Experiments", "dataPack.title": "<PERSON><PERSON><PERSON><PERSON>", "dataPack.trade_rebalance.description": "Ĝisdatigitaj komercoj por vilaĝanoj", "dataPack.trade_rebalance.name": "Rebalancado de komercoj por vilaĝanoj", "dataPack.update_1_20.description": "Novaj trajtoj kaj enhavoj por Minecraft 1.20", "dataPack.update_1_20.name": "Ĝisdatigo 1.20", "dataPack.update_1_21.description": "Novaj trajtoj kaj enhavoj por Minecraft 1.21", "dataPack.update_1_21.name": "Ĝisdatigo 1.21", "dataPack.validation.back": "<PERSON><PERSON><PERSON>", "dataPack.validation.failed": "Datumpacki konfirmado faliis!", "dataPack.validation.reset": "Rivenar a norma", "dataPack.validation.working": "Konfirmas da<PERSON>i selektata...", "dataPack.vanilla.description": "La defaŭlta datumo por Minecraft", "dataPack.vanilla.name": "Defaŭlta", "dataPack.winter_drop.description": "New features and content for the Winter Drop", "dataPack.winter_drop.name": "Winter Drop", "datapackFailure.safeMode": "Ludomodo", "datapackFailure.safeMode.failed.description": "Ĉi tiu mondo enhavas nevalidan aŭ difektitan konservdatenon.", "datapackFailure.safeMode.failed.title": "Malsukcesis ŝargi la mondon en Sekura Reĝimo.", "datapackFailure.title": "Eraroj en aktuale elektitaj datumopakoj malhelpis al la mondo ŝargi.\nVi povas provi ŝargi ĝin nur kun la vanila datumopako (\"sekura reĝimo\"), aŭ reiri al la titola ekrano kaj ripari ĝin mane.", "death.attack.anvil": "%1$s aplastesis per amboso falanta", "death.attack.anvil.player": "%1$s aplastesis per amboso falanta dum ke kombatis %2$s", "death.attack.arrow": "%1$s lancesis per %2$s", "death.attack.arrow.item": "%1$s lancesis per %2$s kun %3$s", "death.attack.badRespawnPoint.link": "Intencala ludodesegno", "death.attack.badRespawnPoint.message": "%1$s mortigesis per %2$s", "death.attack.cactus": "%1$s mortis per piketo", "death.attack.cactus.player": "%1$s marchis aden kaktuso dum ke esforcis eskapar %2$s", "death.attack.cramming": "%1$s aplastesis tro multe", "death.attack.cramming.player": "%1$s aplastesis per %2$s", "death.attack.dragonBreath": "%1$s estis rostita de draĉa spirado", "death.attack.dragonBreath.player": "%1$s estis rostita de draĉa spirado de %2$s", "death.attack.drown": "%1$s dronis", "death.attack.drown.player": "%1$s dronis dum ke esforcis eskapar %2$s", "death.attack.dryout": "%1$s mortis pro malsekigo", "death.attack.dryout.player": "%1$s mortis pro malsekigo dum provado eskapi %2$s", "death.attack.even_more_magic": "%1$s mortigesis per mem plu magio", "death.attack.explosion": "%1$s mortis pro explozo", "death.attack.explosion.player": "%1$s explozesis per %2$s", "death.attack.explosion.player.item": "%1$s explozesis per %2$s kun %3$s", "death.attack.fall": "%1$s terovenis tro forte", "death.attack.fall.player": "%1$s terovenis tro forte dum ke esforcis eskapar %2$s", "death.attack.fallingBlock": "%1$s aplastesis per bloko falanta", "death.attack.fallingBlock.player": "%1$s aplastesis per bloko falanta dum ke kombatis %2$s", "death.attack.fallingStalactite": "%1$s estis trapikita de falanta stalaktito", "death.attack.fallingStalactite.player": "%1$s estis trapikita de falanta stalaktito dum batalo kun %2$s", "death.attack.fireball": "%1$s mortesis per fairbulo de %2$s", "death.attack.fireball.item": "%1$s mortesis per fairbulo de %2$s kun %3$s", "death.attack.fireworks": "%1$s departis kun explozo", "death.attack.fireworks.item": "%1$s eksplodis per bruo pro fajroverko elsendita de %3$s de %2$s", "death.attack.fireworks.player": "%1$s departis kun explozo dum ke kombatis %2$s", "death.attack.flyIntoWall": "%1$s sentis energio cinetika", "death.attack.flyIntoWall.player": "%1$s sentis energio cinetika dum ke esforcis eskapar %2$s", "death.attack.freeze": "%1$s frostis ĝis la morto", "death.attack.freeze.player": "%1$s frostis ĝis la morto de %2$s", "death.attack.generic": "%1$s mortis", "death.attack.generic.player": "%1$s mortis pro ke %2$s", "death.attack.genericKill": "%1$s estis mortigita", "death.attack.genericKill.player": "%1$s estis mortigita dum batalo kun %2$s", "death.attack.hotFloor": "%1$s deskovris ke la tero esis lavao", "death.attack.hotFloor.player": "%1$s piediris en la danĝera zono pro %2$s", "death.attack.inFire": "%1$s glutesis per flami", "death.attack.inFire.player": "%1$s marchis aden fairo dum ke kombatis %2$s", "death.attack.inWall": "%1$s sufokis en muro", "death.attack.inWall.player": "%1$s sufokis en muro dum ke kombatis %2$s", "death.attack.indirectMagic": "%1$s mortigesis per %2$s kun magio", "death.attack.indirectMagic.item": "%1$s mortigesis per %2$s kun %3$s", "death.attack.lava": "%1$s probis natar en lavao", "death.attack.lava.player": "%1$s probis natar en lavao por eskapar %2$s", "death.attack.lightningBolt": "%1$s frapesis per fulmino", "death.attack.lightningBolt.player": "%1$s frapesis per fulmino dum ke kombatis %2$s", "death.attack.mace_smash": "%1$s was smashed by %2$s", "death.attack.mace_smash.item": "%1$s was smashed by %2$s with %3$s", "death.attack.magic": "%1$s mortigesis per magio", "death.attack.magic.player": "%1$s mortigesis per magio dum eforcis eskapar %2$s", "death.attack.message_too_long": "<PERSON><PERSON><PERSON>, la mesaĝo estis tro longa por tute transdoni. Pardonu! Jen mallonga versio: %s", "death.attack.mob": "%1$s ocidesis per %2$s", "death.attack.mob.item": "%1$s ocidesis per %2$s kun %3$s", "death.attack.onFire": "%1$s mortis pro brulo", "death.attack.onFire.item": "%1$s estis bruligita ĝis krumpo dum batalo kun %2$s tenante %3$s", "death.attack.onFire.player": "%1$s brulesis til cindri dum ke kombatis %2$s", "death.attack.outOfWorld": "%1$s falis ek la mondo", "death.attack.outOfWorld.player": "%1$s ne volis habitar en la mondo sama kam %2$s", "death.attack.outsideBorder": "%1$s forlasis la limojn de ĉi tiu mondo", "death.attack.outsideBorder.player": "%1$s forlasis la limojn de ĉi tiu mondo dum batalo kun %2$s", "death.attack.player": "%1$s estis mortigita de %2$s", "death.attack.player.item": "%1$s estis mortigita de %2$s uzante %3$s", "death.attack.sonic_boom": "%1$s estis ekstermita per sonike ŝarĝita kriegado", "death.attack.sonic_boom.item": "%1$s estis ekstermita per sonike ŝarĝita kriegado dum provo eskapi de %2$s tenante %3$s", "death.attack.sonic_boom.player": "%1$s estis ekstermita per sonike ŝarĝita kriegado dum provo eskapi de %2$s", "death.attack.stalagmite": "%1$s estis trapikita de stalagmito", "death.attack.stalagmite.player": "%1$s estis trapikita de stalagmito dum batalo kun %2$s", "death.attack.starve": "%1$s mortis pro hungro", "death.attack.starve.player": "%1$s mortis pro hungro dum ke kombatis %2$s", "death.attack.sting": "%1$s mortis per dardo", "death.attack.sting.item": "%1$s mortis pro piko de %2$s uzante %3$s", "death.attack.sting.player": "%1$s mortis per dardo be %2$s", "death.attack.sweetBerryBush": "%1$s mortis pro pikar per arbusto di dolcaberi", "death.attack.sweetBerryBush.player": "%1$s mortis pro pikar per arbusto di dolcaberi dum ke esforcis eskapar %2$s", "death.attack.thorns": "%1$s mortigesis dum ke esforcis vundar %2$s", "death.attack.thorns.item": "%1$s mortigesis per %3$s dum ke esforcis vundar %2$s", "death.attack.thrown": "%1$s bombardesis per %2$s", "death.attack.thrown.item": "%1$s bombardesis per %2$s kun %3$s", "death.attack.trident": "%1$s empalesis per %2$s", "death.attack.trident.item": "%1$s empalesis per %2$s kun %3$s", "death.attack.wither": "%1$s mortigesis per witherigo", "death.attack.wither.player": "%1$s mortigesis per witherigo dum ke kombatis %2$s", "death.attack.witherSkull": "%1$s estis pafita per kranio el %2$s", "death.attack.witherSkull.item": "%1$s estis pafita per kranio el %2$s uzante %3$s", "death.fell.accident.generic": "%1$s falis de alta loko", "death.fell.accident.ladder": "%1$s falis de skalo", "death.fell.accident.other_climbable": "%1$s falis dum klimis", "death.fell.accident.scaffolding": "%1$s falis el stelaro", "death.fell.accident.twisting_vines": "%1$s falis de viti tordanta", "death.fell.accident.vines": "%1$s falis de liani", "death.fell.accident.weeping_vines": "%1$s falis de viti ploranta", "death.fell.assist": "%1$s kondamnesis falar per %2$s", "death.fell.assist.item": "%1$s kondamnesis falar per %2$s kun %3$s", "death.fell.finish": "%1$s falis tro fore e finigesis per %2$s", "death.fell.finish.item": "%1$s falis tro fore e finigesis per %2$s kun %3$s", "death.fell.killer": "%1$s kondamnesis falar", "deathScreen.quit.confirm": "Ka vu certe volas livar?", "deathScreen.respawn": "Rigenitar", "deathScreen.score": "Punt<PERSON>nto", "deathScreen.score.value": "Poentoj: %s", "deathScreen.spectate": "Spekti la mondon", "deathScreen.title": "Vi mortis!", "deathScreen.title.hardcore": "Ludo finiĝis!", "deathScreen.titleScreen": "<PERSON><PERSON>", "debug.advanced_tooltips.help": "F3 + H = Statistiki avancata di utensili", "debug.advanced_tooltips.off": "Statistiki avancata di utensili: nevidebla", "debug.advanced_tooltips.on": "Statistiki avancata di utensili: videbla", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON> bordi di chunki", "debug.chunk_boundaries.off": "Bordi di chunki: nevidebla", "debug.chunk_boundaries.on": "Bordi di chunki: videbla", "debug.clear_chat.help": "F3 + D = Netigar mesaji", "debug.copy_location.help": "F3 + C = Kopiar loko kom /tp komando, tenar F3 + C por panear la ludo", "debug.copy_location.message": "<PERSON><PERSON><PERSON> loko ad bornplank<PERSON>", "debug.crash.message": "F3 + C esas depresata. Ico paneos la ludo ecepte se esos liberigata.", "debug.crash.warning": "Paneor en %s...", "debug.creative_spectator.error": "<PERSON>e povas ŝanĝi la ludadmanieron; mankas permeso", "debug.creative_spectator.help": "F3 + N = <PERSON><PERSON><PERSON> antaŭan ludan modon <-> spektanto", "debug.dump_dynamic_textures": "Konservis dinamikajn teksturojn al %s", "debug.dump_dynamic_textures.help": "F3 + S = Elverŝi dinamikajn teksturojn", "debug.gamemodes.error": "Ne eblas malfermi la ludadmanieron ŝaltilon; mankas permeso", "debug.gamemodes.help": "F3 + F4 = Malfermi la ludadmanieron ŝaltilon", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Venonta", "debug.help.help": "F3 + Q = Montrar ica listo", "debug.help.message": "<PERSON><PERSON><PERSON> di klavi:", "debug.inspect.client.block": "<PERSON><PERSON><PERSON> b<PERSON> a bornplanketo", "debug.inspect.client.entity": "<PERSON><PERSON><PERSON> ento-don<PERSON><PERSON> a bornplanketo", "debug.inspect.help": "F3 + I = <PERSON><PERSON><PERSON> ento o blokdonaji a bornplanketo", "debug.inspect.server.block": "<PERSON><PERSON><PERSON> a bornplanketo", "debug.inspect.server.entity": "<PERSON><PERSON><PERSON> ento-don<PERSON><PERSON> a bornplanketo", "debug.pause.help": "F3 + Esk + <PERSON><PERSON><PERSON> sen pauzmenuo (se pauzar esas possibla)", "debug.pause_focus.help": "F3 + P = <PERSON><PERSON><PERSON> sur foko perdita", "debug.pause_focus.off": "<PERSON><PERSON>r sur foko perdita: deshabilitata", "debug.pause_focus.on": "Pauzar sur foko perdita: habilitata", "debug.prefix": "[Debugar]:", "debug.profiling.help": "F3 + L = Ek/kontenigi profilon", "debug.profiling.start": "Profilo komencis dum %s sekundoj. Uzu F3 + L por halti frue", "debug.profiling.stop": "La profilo finiĝis. Konservis rezultojn al %s", "debug.reload_chunks.help": "F3 + A = <PERSON><PERSON><PERSON> <PERSON>i", "debug.reload_chunks.message": "Richarjas omna chunki", "debug.reload_resourcepacks.help": "F3 + T = <PERSON><PERSON><PERSON> hava<PERSON>i", "debug.reload_resourcepacks.message": "<PERSON><PERSON><PERSON><PERSON>", "debug.show_hitboxes.help": "F3 + B = <PERSON><PERSON> frapobuxi", "debug.show_hitboxes.off": "Frapobuxi: nevidebla", "debug.show_hitboxes.on": "Frapobuxi: videbla", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Client version info", "demo.day.1": "Ica demo duros por kin dii enluda. Esforcez vua maxim bona!", "demo.day.2": "<PERSON><PERSON>", "demo.day.3": "<PERSON><PERSON>", "demo.day.4": "<PERSON><PERSON> qua<PERSON><PERSON>", "demo.day.5": "Ico esas vua dio finala!", "demo.day.6": "Vu pasis vua dio kinesma. Uzez %s por savar skrenkapto di vua kreado.", "demo.day.warning": "Vua tempo esas preske pasinta!", "demo.demoExpired": "La demonstro jus expiris!", "demo.help.buy": "<PERSON><PERSON><PERSON> nun!", "demo.help.fullWrapped": "Ica demo duros 5 dii enluda (cirkum 1 horo e 40 minuti di tempo reala). Spektez la avanci por indici! Havez bona tempo!", "demo.help.inventory": "Uzez la %1$s klavo por apertar vua inventario", "demo.help.jump": "Saltez per presar la %1$s klavo", "demo.help.later": "Durar a ludar!", "demo.help.movement": "Uzez la klavi %1$s, %2$s, %3$s, %4$s e la mauso por movar", "demo.help.movementMouse": "Cirkumregardez per uzar la mauso", "demo.help.movementShort": "Movez per presar la %1$s, %2$s, %3$s, %4$s klavi", "demo.help.title": "Demonstro di Minecraft", "demo.remainingTime": "Tempo cetera: %s", "demo.reminder": "La tempo di demo jus expiris. Komprez la ludo por durar o startar mondo nova!", "difficulty.lock.question": "Ka vu certe volas koaktar la desfacileso di vua mondo? Ico definos la mondo estar todos %1$s, e vu ni povos changar ito itere.", "difficulty.lock.title": "Koaktar desfacileso di mondo", "disconnect.endOfStream": "Fino di fluo", "disconnect.exceeded_packet_rate": "Ekpulsesis pro ecesas limito pri paketrapideso", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignori peton pri stato", "disconnect.loginFailedInfo": "Faliis ad acesar: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Ludado di plura ludanti es deshabilitata. Voluntez spektar vua ajusti kontala di Microsoft.", "disconnect.loginFailedInfo.invalidSession": "Sesiono nevalida (probez ristartar vua ludo e la startilo)", "disconnect.loginFailedInfo.serversUnavailable": "La aŭtentigaj serviloj nuntempas neatingeblaj. Bonvolu reprovi.", "disconnect.loginFailedInfo.userBanned": "Vu esas interdiktata de ludanta enlinee", "disconnect.lost": "Konekto perdesis", "disconnect.packetError": "Network Protocol Error", "disconnect.spam": "Ekpulsesis por mala konduto", "disconnect.timeout": "Konekto expiris", "disconnect.transfer": "Transdonita al alia servilo", "disconnect.unknownHost": "Nekonata servo", "download.pack.failed": "%s ek %s paketi malsuk<PERSON> elŝuti", "download.pack.progress.bytes": "Avanco: %s (totala grandeco nekonata)", "download.pack.progress.percent": "Avanco: %s%%", "download.pack.title": "Elshutas risurso-pako %s/%s", "editGamerule.default": "Originali", "editGamerule.title": "<PERSON><PERSON><PERSON><PERSON>", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorbeso", "effect.minecraft.bad_omen": "<PERSON>a auguro", "effect.minecraft.blindness": "Blindeso", "effect.minecraft.conduit_power": "Energio di povoduktilo", "effect.minecraft.darkness": "Malklareco", "effect.minecraft.dolphins_grace": "Delfingracio", "effect.minecraft.fire_resistance": "Fairorezisto", "effect.minecraft.glowing": "Brileso", "effect.minecraft.haste": "<PERSON><PERSON>", "effect.minecraft.health_boost": "Sanesbonigo", "effect.minecraft.hero_of_the_village": "Tutelanto di vilajano", "effect.minecraft.hunger": "<PERSON><PERSON>", "effect.minecraft.infested": "Infested", "effect.minecraft.instant_damage": "<PERSON><PERSON><PERSON> instanta", "effect.minecraft.instant_health": "Saneso instanta", "effect.minecraft.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.jump_boost": "Saltego", "effect.minecraft.levitation": "<PERSON>lug<PERSON><PERSON>", "effect.minecraft.luck": "Chanco", "effect.minecraft.mining_fatigue": "Fatigo di minado", "effect.minecraft.nausea": "<PERSON><PERSON><PERSON>", "effect.minecraft.night_vision": "Noktovidado", "effect.minecraft.oozing": "Oozing", "effect.minecraft.poison": "To<PERSON><PERSON>", "effect.minecraft.raid_omen": "Raid Omen", "effect.minecraft.regeneration": "Regenero", "effect.minecraft.resistance": "Rezisto", "effect.minecraft.saturation": "Satureso", "effect.minecraft.slow_falling": "<PERSON><PERSON> faligo", "effect.minecraft.slowness": "<PERSON><PERSON><PERSON>", "effect.minecraft.speed": "Rapideso", "effect.minecraft.strength": "Forteso", "effect.minecraft.trial_omen": "Trial Omen", "effect.minecraft.unluck": "Malchanco", "effect.minecraft.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.weakness": "<PERSON><PERSON><PERSON>", "effect.minecraft.weaving": "Weaving", "effect.minecraft.wind_charged": "Wind Charged", "effect.minecraft.wither": "Velkigo", "effect.none": "<PERSON><PERSON> efekti", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Aqua afineso", "enchantment.minecraft.bane_of_arthropods": "Pesto di artropodi", "enchantment.minecraft.binding_curse": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.blast_protection": "Explozoprotekto", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "Kanaligado", "enchantment.minecraft.density": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.feather_falling": "P<PERSON><PERSON>lad<PERSON>", "enchantment.minecraft.fire_aspect": "Fairaspekto", "enchantment.minecraft.fire_protection": "Fairprotekto", "enchantment.minecraft.flame": "<PERSON><PERSON>", "enchantment.minecraft.fortune": "Fortuno", "enchantment.minecraft.frost_walker": "Frostmarchar", "enchantment.minecraft.impaling": "Empalado", "enchantment.minecraft.infinity": "Infiniteso", "enchantment.minecraft.knockback": "Retrosalto", "enchantment.minecraft.looting": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.loyalty": "Loyaleso", "enchantment.minecraft.luck_of_the_sea": "Chanco di la maro", "enchantment.minecraft.lure": "<PERSON><PERSON>", "enchantment.minecraft.mending": "<PERSON><PERSON>do", "enchantment.minecraft.multishot": "Poliflecho", "enchantment.minecraft.piercing": "Penetreso", "enchantment.minecraft.power": "Poveso", "enchantment.minecraft.projectile_protection": "Projektilprotekto", "enchantment.minecraft.protection": "Protekto", "enchantment.minecraft.punch": "Retrofrapo", "enchantment.minecraft.quick_charge": "Pafajo rapida", "enchantment.minecraft.respiration": "Respiro", "enchantment.minecraft.riptide": "<PERSON>mpul<PERSON>", "enchantment.minecraft.sharpness": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.smite": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.soul_speed": "Rapideso amnala", "enchantment.minecraft.sweeping": "<PERSON><PERSON><PERSON><PERSON> repidega", "enchantment.minecraft.sweeping_edge": "Plibonigo de Batala Areo", "enchantment.minecraft.swift_sneak": "Rapida Kaŝiĝo", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "Malediko di desparado", "enchantment.minecraft.wind_burst": "<PERSON> Burst", "entity.minecraft.acacia_boat": "Acacia Boat", "entity.minecraft.acacia_chest_boat": "Acacia Boat with Chest", "entity.minecraft.allay": "<PERSON><PERSON><PERSON>", "entity.minecraft.area_effect_cloud": "Nubo di areoefekto", "entity.minecraft.armadillo": "Arm<PERSON><PERSON>", "entity.minecraft.armor_stand": "Vestiero", "entity.minecraft.arrow": "<PERSON><PERSON><PERSON>", "entity.minecraft.axolotl": "<PERSON><PERSON>olo<PERSON>", "entity.minecraft.bamboo_chest_raft": "Bamboo Raft with Chest", "entity.minecraft.bamboo_raft": "Bamboo Raft", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "<PERSON> Boat", "entity.minecraft.birch_chest_boat": "<PERSON> Boat with Chest", "entity.minecraft.blaze": "<PERSON><PERSON><PERSON>", "entity.minecraft.block_display": "<PERSON><PERSON>", "entity.minecraft.boat": "Boat", "entity.minecraft.bogged": "Marĉano", "entity.minecraft.breeze": "Brizo", "entity.minecraft.breeze_wind_charge": "Vento ŝarĝo", "entity.minecraft.camel": "<PERSON><PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "Kavernaraneo", "entity.minecraft.cherry_boat": "Cherry Boat", "entity.minecraft.cherry_chest_boat": "Cherry Boat with Chest", "entity.minecraft.chest_boat": "Boat with Chest", "entity.minecraft.chest_minecart": "Minvagono kun kofro", "entity.minecraft.chicken": "<PERSON><PERSON>", "entity.minecraft.cod": "Gado", "entity.minecraft.command_block_minecart": "Minvagono kun bloko di komandi", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "Creaking", "entity.minecraft.creaking_transient": "Creaking", "entity.minecraft.creeper": "Creepero", "entity.minecraft.dark_oak_boat": "Dark Oak Boat", "entity.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "entity.minecraft.dolphin": "Delfino", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Fair<PERSON><PERSON> di drako", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON>", "entity.minecraft.egg": "<PERSON>vo jetita", "entity.minecraft.elder_guardian": "<PERSON>a guardero", "entity.minecraft.end_crystal": "<PERSON><PERSON><PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON> d<PERSON>o", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON>ita", "entity.minecraft.enderman": "<PERSON><PERSON><PERSON>", "entity.minecraft.endermite": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.evoker": "Evkoanto", "entity.minecraft.evoker_fangs": "Dentego di evokero", "entity.minecraft.experience_bottle": "Botelo di charmivo jetita", "entity.minecraft.experience_orb": "Globo di experienco", "entity.minecraft.eye_of_ender": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.falling_block": "Bloko falanta", "entity.minecraft.falling_block_type": "Faliĝanta %s", "entity.minecraft.fireball": "Fairbulo", "entity.minecraft.firework_rocket": "Fuzeo di pirotekno", "entity.minecraft.fishing_bobber": "<PERSON>", "entity.minecraft.fox": "Foxo", "entity.minecraft.frog": "<PERSON><PERSON>", "entity.minecraft.furnace_minecart": "Minvagono kun furnazo", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Giganto", "entity.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON>", "entity.minecraft.glow_squid": "Lumkalmar<PERSON>", "entity.minecraft.goat": "Ka<PERSON><PERSON>", "entity.minecraft.guardian": "Guardero", "entity.minecraft.happy_ghast": "<PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Minvagono kun funelego", "entity.minecraft.horse": "Kavalo", "entity.minecraft.husk": "<PERSON><PERSON>", "entity.minecraft.illusioner": "Iluzionanto", "entity.minecraft.interaction": "Interago", "entity.minecraft.iron_golem": "<PERSON><PERSON>o fera", "entity.minecraft.item": "Objekto", "entity.minecraft.item_display": "<PERSON><PERSON>", "entity.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "entity.minecraft.jungle_boat": "Jungle Boat", "entity.minecraft.jungle_chest_boat": "Jungle Boat with Chest", "entity.minecraft.killer_bunny": "La kuniklo ocidema", "entity.minecraft.leash_knot": "Laznodo", "entity.minecraft.lightning_bolt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.lingering_potion": "Lingering Potion", "entity.minecraft.llama": "<PERSON><PERSON>", "entity.minecraft.llama_spit": "Lama<PERSON><PERSON>", "entity.minecraft.magma_cube": "Magmakubo", "entity.minecraft.mangrove_boat": "Mangrove Boat", "entity.minecraft.mangrove_chest_boat": "Mangrove Boat with Chest", "entity.minecraft.marker": "<PERSON><PERSON>", "entity.minecraft.minecart": "Minvagono", "entity.minecraft.mooshroom": "Mooshroomo", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "Oak Boat", "entity.minecraft.oak_chest_boat": "Oak Boat with Chest", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Ominous <PERSON>em <PERSON>wner", "entity.minecraft.painting": "Pikturo", "entity.minecraft.pale_oak_boat": "Pale Oak Boat", "entity.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "entity.minecraft.panda": "Pando", "entity.minecraft.parrot": "Papagayo", "entity.minecraft.phantom": "<PERSON><PERSON><PERSON>", "entity.minecraft.pig": "Porko", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON>b<PERSON><PERSON>", "entity.minecraft.pillager": "S<PERSON>ilanto", "entity.minecraft.player": "Ludanto", "entity.minecraft.polar_bear": "Urso blanka", "entity.minecraft.potion": "Pociono", "entity.minecraft.pufferfish": "Suflantofisho", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Devastanto", "entity.minecraft.salmon": "<PERSON><PERSON>", "entity.minecraft.sheep": "Mutono", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Shulkerkuglo", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON>nta<PERSON><PERSON>", "entity.minecraft.skeleton": "Skeleto", "entity.minecraft.skeleton_horse": "Skeleta kavalo", "entity.minecraft.slime": "<PERSON><PERSON>", "entity.minecraft.small_fireball": "Fairbuleto", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "<PERSON><PERSON><PERSON>", "entity.minecraft.snowball": "Nivbul<PERSON>", "entity.minecraft.spawner_minecart": "<PERSON><PERSON><PERSON><PERSON> kun Monstraj Produktanto", "entity.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON> spektrala", "entity.minecraft.spider": "Araneo", "entity.minecraft.splash_potion": "Splash Potion", "entity.minecraft.spruce_boat": "Spruce Boat", "entity.minecraft.spruce_chest_boat": "Spruce Boat with Chest", "entity.minecraft.squid": "<PERSON><PERSON><PERSON>", "entity.minecraft.stray": "Divaganto", "entity.minecraft.strider": "Pazeganto", "entity.minecraft.tadpole": "Ranlarvo", "entity.minecraft.text_display": "<PERSON><PERSON>", "entity.minecraft.tnt": "TNT amorcita", "entity.minecraft.tnt_minecart": "Minvagono kun TNT", "entity.minecraft.trader_llama": "<PERSON><PERSON>", "entity.minecraft.trident": "Tridento", "entity.minecraft.tropical_fish": "<PERSON><PERSON> trop<PERSON>", "entity.minecraft.tropical_fish.predefined.0": "Aktinifisho", "entity.minecraft.tropical_fish.predefined.1": "<PERSON><PERSON><PERSON> rostra", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON> m<PERSON>", "entity.minecraft.tropical_fish.predefined.11": "Ornita papilion<PERSON>o", "entity.minecraft.tropical_fish.predefined.12": "Papagayfisho", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.14": "Ciklido reda", "entity.minecraft.tropical_fish.predefined.15": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.16": "Snapanto reda", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "Klaunfisho tomatea", "entity.minecraft.tropical_fish.predefined.19": "Balistfisho", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON><PERSON> blua", "entity.minecraft.tropical_fish.predefined.20": "Papagayfisho flavkauda", "entity.minecraft.tropical_fish.predefined.21": "Kirurgo flava", "entity.minecraft.tropical_fish.predefined.3": "Papilionfisho", "entity.minecraft.tropical_fish.predefined.4": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.5": "Bafunofisho", "entity.minecraft.tropical_fish.predefined.6": "Siamana komb<PERSON>o", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "Imperiestrala snapanto reda", "entity.minecraft.tropical_fish.predefined.9": "Surmuleto", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Blokfisho", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "Argilfisho", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "Friskanto", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Sunstrio", "entity.minecraft.turtle": "Tortugo", "entity.minecraft.vex": "Vexo", "entity.minecraft.villager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.armorer": "Armtifisto", "entity.minecraft.villager.butcher": "B<PERSON>sto", "entity.minecraft.villager.cartographer": "Kartografo", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "Farmisto", "entity.minecraft.villager.fisherman": "Peskero", "entity.minecraft.villager.fletcher": "Flechisto", "entity.minecraft.villager.leatherworker": "Ledropreparisto", "entity.minecraft.villager.librarian": "Bibliotekisto", "entity.minecraft.villager.mason": "Masonisto", "entity.minecraft.villager.nitwit": "Idioto", "entity.minecraft.villager.none": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.shepherd": "<PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "Utensilforjisto", "entity.minecraft.villager.weaponsmith": "Armforji<PERSON>", "entity.minecraft.vindicator": "Praviganto", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON> k<PERSON>", "entity.minecraft.warden": "Guardo", "entity.minecraft.wind_charge": "Venta Ŝargo", "entity.minecraft.witch": "<PERSON><PERSON><PERSON>", "entity.minecraft.wither": "Withero", "entity.minecraft.wither_skeleton": "Witherskeleto", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "Volfo", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zombio", "entity.minecraft.zombie_horse": "Zombia kavalo", "entity.minecraft.zombie_villager": "Zombia vilajano", "entity.minecraft.zombified_piglin": "<PERSON><PERSON>", "entity.not_summonable": "Ne povas alvoki estaĵon de tipo %s", "event.minecraft.raid": "Invado", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Invalo - Venki", "event.minecraft.raid.raiders_remaining": "<PERSON><PERSON><PERSON>: %s", "event.minecraft.raid.victory": "<PERSON><PERSON>", "event.minecraft.raid.victory.full": "<PERSON>val<PERSON> <PERSON> <PERSON><PERSON>", "filled_map.buried_treasure": "Mapo di trezoro enterigita", "filled_map.explorer_jungle": "Mapo de Esplorado de Ĝangalo", "filled_map.explorer_swamp": "Mapo de Esplorado de Marĉo", "filled_map.id": "ID #%s", "filled_map.level": "(Nivelo %s/%s)", "filled_map.locked": "Fermata", "filled_map.mansion": "Explormapo di arbaro", "filled_map.monument": "Explormapo di oceano", "filled_map.scale": "Skalo 1:%s", "filled_map.trial_chambers": "Trial Explorer Map", "filled_map.unknown": "Mapo nekonocata", "filled_map.village_desert": "Mapo de Dezerta Vilaĝo", "filled_map.village_plains": "Mapo de Kampa Vilaĝo", "filled_map.village_savanna": "Mapo de Savana Vilaĝo", "filled_map.village_snowy": "Mapo de Niva Vilaĝo", "filled_map.village_taiga": "Mapo de Taĝa Vilaĝo", "flat_world_preset.minecraft.bottomless_pit": "Foso senfunda", "flat_world_preset.minecraft.classic_flat": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.desert": "Dezerto", "flat_world_preset.minecraft.overworld": "Superamondo", "flat_world_preset.minecraft.redstone_ready": "Pronta por Redstoneo", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.the_void": "La vakuajo", "flat_world_preset.minecraft.tunnelers_dream": "Revo di ministi", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON> a<PERSON>", "flat_world_preset.unknown": "???", "gameMode.adventure": "<PERSON><PERSON> a<PERSON>", "gameMode.changed": "Vua ludomodo aktualigesis a %s", "gameMode.creative": "<PERSON>do k<PERSON>", "gameMode.hardcore": "Modo extrema!", "gameMode.spectator": "<PERSON><PERSON> spektanta", "gameMode.survival": "<PERSON><PERSON> transvivala", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Controls whether or not fire and lava should be able to tick further than 8 chunks away from any player", "gamerule.announceAdvancements": "<PERSON><PERSON><PERSON>", "gamerule.blockExplosionDropDecay": "En blokaj interagadoj de eksplodoj, kelkaj blokoj ne faligos sian rabon", "gamerule.blockExplosionDropDecay.description": "Kelkaj el la faligoj el blokoj detruitaj de eksplodoj kaŭzitaj per blokaj interagadoj perdas sin en la eksplodo.", "gamerule.category.chat": "Babilado", "gamerule.category.drops": "Faligoj", "gamerule.category.misc": "<PERSON><PERSON><PERSON>", "gamerule.category.mobs": "<PERSON><PERSON><PERSON>", "gamerule.category.player": "Ludanto", "gamerule.category.spawning": "Naskiĝo", "gamerule.category.updates": "<PERSON><PERSON><PERSON> Ĝisdatigoj", "gamerule.commandBlockOutput": "Elsendi la eliron de komandblokaĵo", "gamerule.commandModificationBlockLimit": "<PERSON><PERSON><PERSON> de bloko de komand modifiko", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON>, kiuj povas esti ŝanĝitaj samtempe per unu komando, kiel ekzemple 'plenumu' aŭ 'klonu'.", "gamerule.disableElytraMovementCheck": "Malŝalti kontrolon de flugilo-movo", "gamerule.disablePlayerMovementCheck": "Disable player movement check", "gamerule.disableRaids": "Malŝalti invadojn", "gamerule.doDaylightCycle": "Antaŭenigi la tempon de la tago", "gamerule.doEntityDrops": "Faligi ekipaĵon de la estaĵo", "gamerule.doEntityDrops.description": "Kontrolas la faligojn el minveturiloj (inkluzive de inventaroj), bildokadroj, ŝipetoj, ktp.", "gamerule.doFireTick": "Ĝisdatigi fajron", "gamerule.doImmediateRespawn": "Revivi tuj", "gamerule.doInsomnia": "<PERSON><PERSON><PERSON>", "gamerule.doLimitedCrafting": "Postuli recepton por meti", "gamerule.doLimitedCrafting.description": "Se aktiva, ludantoj povos meti nur malŝlositajn receptojn.", "gamerule.doMobLoot": "Ĵeti rabas varbon", "gamerule.doMobLoot.description": "Regas resurso<PERSON>, inkluzive de spertokugloj.", "gamerule.doMobSpawning": "<PERSON><PERSON><PERSON>", "gamerule.doMobSpawning.description": "<PERSON>uj estaĵoj eble havas apartajn regu<PERSON>.", "gamerule.doPatrolSpawning": "<PERSON><PERSON><PERSON> pill<PERSON> patrolojn", "gamerule.doTileDrops": "<PERSON><PERSON>", "gamerule.doTileDrops.description": "Kontrolas la elspezojn de resursoj de blokoj, inkluzive de sperto-globuloj.", "gamerule.doTraderSpawning": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> k<PERSON>", "gamerule.doVinesSpread": "La vinberoj pliboniĝas", "gamerule.doVinesSpread.description": "Kontrolas ĉu la bloko de Vinberoj disvastiĝas hazardme al najbaraj blokoj. Ne efikas aliajn specojn de vinberblokoj, kiel <PERSON>, Turniĝvinberoj, ktp.", "gamerule.doWardenSpawning": "<PERSON><PERSON><PERSON>", "gamerule.doWeatherCycle": "Aktualigi veteron", "gamerule.drowningDamage": "<PERSON><PERSON><PERSON> dronadon", "gamerule.enderPearlsVanishOnDeath": "Ĵetitaj endaj perloj malaperas post morto", "gamerule.enderPearlsVanishOnDeath.description": "Ĉu endaj perloj ĵetitaj de ludanto malaperas kiam tiu ludanto mortas.", "gamerule.entitiesWithPassengersCanUsePortals": "Entities with passengers can use portals", "gamerule.entitiesWithPassengersCanUsePortals.description": "Allow entities with passengers to teleport through Nether Portals, End Portals, and End Gateways.", "gamerule.fallDamage": "Kaŭzi falan damaĝon", "gamerule.fireDamage": "Kaŭzi fajran damaĝon", "gamerule.forgiveDeadPlayers": "Pardonu la mortintojn ludantojn", "gamerule.forgiveDeadPlayers.description": "<PERSON><PERSON><PERSON><PERSON>aj bestoj ĉesas esti iritaj kiam la celata ludanto mortas apud.", "gamerule.freezeDamage": "Kaŭzi frost-damaĝon", "gamerule.globalSoundEvents": "Universaj sonaj okazaĵoj", "gamerule.globalSoundEvents.description": "<PERSON><PERSON> okazas certaj ludaj event<PERSON>, kiel ek<PERSON><PERSON> la generiĝo de estro, la sono estas aŭdebla ĉie.", "gamerule.keepInventory": "Konservi ekipaĵon post morto", "gamerule.lavaSourceConversion": "Lavo konvertiĝas al fonto", "gamerule.lavaSourceConversion.description": "<PERSON><PERSON> fluanta lavo estas ĉirkaŭita de lavo-fontoj de du flankoj, ĝi konvertiĝas en fonton.", "gamerule.locatorBar": "Enable player <PERSON><PERSON><PERSON>", "gamerule.locatorBar.description": "When enabled, a bar is shown on the screen to indicate the direction of players.", "gamerule.logAdminCommands": "Elsendi administrajn k<PERSON>", "gamerule.maxCommandChainLength": "Limigo de grandeco de komandĉeno", "gamerule.maxCommandChainLength.description": "Aplikas al komandbloka ĉenoj kaj funkcioj.", "gamerule.maxCommandForkCount": "Limigo de komanda kunteksto", "gamerule.maxCommandForkCount.description": "<PERSON><PERSON><PERSON><PERSON> nombro de kuntekstoj, kiuj povas esti uzataj per komandoj, kiel 'plenumu kiel'.", "gamerule.maxEntityCramming": "Krampnombra limo de estaĵoj", "gamerule.minecartMaxSpeed": "Minecart max speed", "gamerule.minecartMaxSpeed.description": "Maximum default speed of a moving Minecart on land.", "gamerule.mobExplosionDropDecay": "En mobaj eksplodoj, kelkaj blokoj ne faligos sian rabaĵon", "gamerule.mobExplosionDropDecay.description": "Kelkaj el la faligoj el blokoj detruitaj de eksplodoj kaŭzitaj de bestoj perdas en la eksplodo.", "gamerule.mobGriefing": "<PERSON><PERSON><PERSON> detruiĝajn agojn de bestoj", "gamerule.naturalRegeneration": "Regeneri sano", "gamerule.playersNetherPortalCreativeDelay": "Atendo de la Nether-portalo de la ludanto en la kreiva modo", "gamerule.playersNetherPortalCreativeDelay.description": "Tempo (en taktoj) kiun bezonas ludanto en kreiva modo stari en Nether-portalo antaŭ ŝanĝado de dimensioj.", "gamerule.playersNetherPortalDefaultDelay": "Atendo de la Nether-portalo de la ludanto en ne-kreiva modo", "gamerule.playersNetherPortalDefaultDelay.description": "Tempo (en taktoj) kiun bezonas ludanto en ne-kreiva modo stari en Nether-portalo antaŭ ŝanĝado de dimensioj.", "gamerule.playersSleepingPercentage": "<PERSON><PERSON> procento", "gamerule.playersSleepingPercentage.description": "La procento de ludantoj, kiuj devas dormi por preterlasi la nokton.", "gamerule.projectilesCanBreakBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> rompi blo<PERSON>jn", "gamerule.projectilesCanBreakBlocks.description": "Kontrolas ĉu trafojaj projektiloj detrui blo<PERSON>, kiuj povas esti detruitaj de ili.", "gamerule.randomTickSpeed": "Rate de la hazardaj taktoj de la ticado", "gamerule.reducedDebugInfo": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>on", "gamerule.reducedDebugInfo.description": "Limigas enhavon de eltena ekrano.", "gamerule.sendCommandFeedback": "<PERSON><PERSON> koman<PERSON>on", "gamerule.showDeathMessages": "<PERSON><PERSON> m<PERSON>", "gamerule.snowAccumulationHeight": "Alto di akumulado de neĝo", "gamerule.snowAccumulationHeight.description": "<PERSON><PERSON> neĝas, tavoloj de neĝo formas sur la tero ĝis maksimume tiu nombro de tavoloj.", "gamerule.spawnChunkRadius": "Kreado di Bloko-Radiuso", "gamerule.spawnChunkRadius.description": "Amount of chunks that stay loaded around the overworld spawn position.", "gamerule.spawnRadius": "<PERSON><PERSON><PERSON>", "gamerule.spawnRadius.description": "Controls the size of the area around the spawn point that players can spawn in.", "gamerule.spectatorsGenerateChunks": "<PERSON><PERSON><PERSON> spektantojn generi teron", "gamerule.tntExplodes": "Allow TNT to be activated and to explode", "gamerule.tntExplosionDropDecay": "Je TNT-<PERSON><PERSON><PERSON><PERSON><PERSON>, iuj blokoj ne ĵetos sian rabon", "gamerule.tntExplosionDropDecay.description": "Iuj el la elĵetoj de blokoj detruitaj per eksplodoj kaŭzitaj de TNT forperdiĝas en la eksplodo.", "gamerule.universalAnger": "Universala kolero", "gamerule.universalAnger.description": "Kolerecintaj neutralaj bestoj atakas ajnan <PERSON>, ne nur tiun, kiu ilin kole<PERSON>. <PERSON><PERSON><PERSON> bone funk<PERSON>, se forgiveDeadPlayers estas malakti<PERSON>gita.", "gamerule.waterSourceConversion": "Akvo ŝanĝiĝas al fonto", "gamerule.waterSourceConversion.description": "<PERSON><PERSON> fluanta akvo estas ĉirkaŭita de akvofontoj sur du flankoj, ĝi konvertiĝas en fonton.", "generator.custom": "<PERSON><PERSON><PERSON>", "generator.customized": "<PERSON><PERSON><PERSON>", "generator.minecraft.amplified": "AMPLIGITA", "generator.minecraft.amplified.info": "Avertez: <PERSON>ur por amuzo! Bezonas forta komputero.", "generator.minecraft.debug_all_block_states": "Modo di debugar", "generator.minecraft.flat": "Superplana", "generator.minecraft.large_biomes": "Granda biomi", "generator.minecraft.normal": "Originala", "generator.minecraft.single_biome_surface": "Biomo sola", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "Insuli flotacanta", "gui.abuseReport.attestation": "By submitting this report, you confirm that the information you have provided is accurate and complete to the best of your knowledge.", "gui.abuseReport.comments": "<PERSON><PERSON><PERSON>", "gui.abuseReport.describe": "Detala donado de informoj helpos nin fari bone informitan decidon.", "gui.abuseReport.discard.content": "Se vi foriros, vi perdos ĉi tiun raporton kaj viajn komentojn. Ĉu vi certas, ke vi volas foriri?", "gui.abuseReport.discard.discard": "<PERSON>iri kaj Forĵeti <PERSON>orton", "gui.abuseReport.discard.draft": "Konservi kiel Skizon", "gui.abuseReport.discard.return": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.discard.title": "Forĵeti raporton kaj komentojn?", "gui.abuseReport.draft.content": "Ĉu vi volus daŭrigi la nuntempan redakton aŭ forĵeti ĝin kaj krei novan?", "gui.abuseReport.draft.discard": "Nuligar", "gui.abuseReport.draft.edit": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.draft.quittotitle.content": "Ĉu vi ŝatus daŭrigi redaktadon aŭ ĝin forĵeti?", "gui.abuseReport.draft.quittotitle.title": "Vi havas provi<PERSON><PERSON> babi<PERSON>, kiu perdos, se vi eliros", "gui.abuseReport.draft.title": "Redaktar la provizoran raporton pri babilado?", "gui.abuseReport.error.title": "Problemo eventas kande sendas vua raporto", "gui.abuseReport.message": "Kie vi rimarkis la malbonan konduton?\nTio helpus nin esplori vian aferon.", "gui.abuseReport.more_comments": "Bonvolu priskribi kio okazis:", "gui.abuseReport.name.comment_box_label": "Please describe why you want to report this name:", "gui.abuseReport.name.reporting": "Vi raportas pri '%s'.", "gui.abuseReport.name.title": "<PERSON><PERSON><PERSON> ludanton per nomo", "gui.abuseReport.observed_what": "Pro kial vi raportas ĉi tion?", "gui.abuseReport.read_info": "<PERSON><PERSON><PERSON> pri raportado", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Narkotaji od alkoholo", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "<PERSON><PERSON> kurajozigas altri partoprenar nelegala agi relate narkotaji o kurajozigas drinkar minore.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Sexuala exploto od insultado di infanti", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON><PERSON> parolas pri od altre stimulas nedecanta konduto relate infanti.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON>, imitado, o publikigo di falsa informi", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Ulu nocas reputo di altru, fingas esar altru, o publikigas falsa informi por explotar o misduktar altri.", "gui.abuseReport.reason.description": "Deskripto:", "gui.abuseReport.reason.false_reporting": "Falsa raporto", "gui.abuseReport.reason.generic": "Mi volas raporti ilin", "gui.abuseReport.reason.generic.description": "Mi estas ĉagrenita pro ili / ili faris ion, kion mi ne ŝatas.", "gui.abuseReport.reason.harassment_or_bullying": "Persekutado o tiranesado", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON> shamigas, ataka<PERSON>, o tiranesas vu od altru. Ico inkluzas ke ulu plurafoye probas kontaktar vu od altru sen konsento o publikigas privata personala informi pri vu od altru sen konsento.", "gui.abuseReport.reason.hate_speech": "Odioza parola<PERSON>", "gui.abuseReport.reason.hate_speech.description": "<PERSON><PERSON> atakas vu od altru segun karakterizivi di identeso, quale religio, raso, o sexualeso.", "gui.abuseReport.reason.imminent_harm": "Minacanta domajo - minaco doma<PERSON> altri", "gui.abuseReport.reason.imminent_harm.description": "Ulu minacas domajar vu od altru en reala vivo.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Senkonsenta intima bildigo", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON>, divid<PERSON>, aŭ alie promocias privatajn kaj intmimajn bildojn.", "gui.abuseReport.reason.self_harm_or_suicide": "Minacanta domajo - autodomajado o suocido", "gui.abuseReport.reason.self_harm_or_suicide.description": "Ulu minacas domajar su en reala vivo o parolas pri domajar su en reala vivo.", "gui.abuseReport.reason.sexually_inappropriate": "Sexually inappropriate", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins that are graphic in nature relating to sexual acts, sexual organs, and sexual violence.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terorismo o violenta extremismo", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON> parola<PERSON> p<PERSON>, sti<PERSON><PERSON>, o minacas exekutar agi di terorismo o violenta extremismo pro politikala, religiala, ideologiala, od altra kauzi.", "gui.abuseReport.reason.title": "Selektez la kategorio di raporto", "gui.abuseReport.report_sent_msg": "Ni sukcese ricevis vian raporton. Dankon!\n\nNia teamo revizios ĝin kiel eble plej baldaŭ.", "gui.abuseReport.select_reason": "Elektu <PERSON>", "gui.abuseReport.send": "<PERSON><PERSON>", "gui.abuseReport.send.comment_too_long": "Bonvolu mallongigi la komenton", "gui.abuseReport.send.error_message": "Eroro eventas dum sendado di vua raporto:\n'%s'", "gui.abuseReport.send.generic_error": "Trovis neexpektita eroro dum sendado di vua raporto.", "gui.abuseReport.send.http_error": "Neatendita HTTP-era<PERSON> ok<PERSON>s dum sendado de via raporto.", "gui.abuseReport.send.json_error": "<PERSON><PERSON><PERSON> malformitan enhavon dum sendado de via raporto.", "gui.abuseReport.send.no_reason": "Bonvolu elekti raportan kategorion", "gui.abuseReport.send.not_attested": "Please read the text above and tick the checkbox to be able to send the report", "gui.abuseReport.send.service_unavailable": "Neatingeblas atingi la Servon de Raportado pri Maluzo. Bonvolu certiĝi, ke vi estas konektita al la interreto, kaj provu denove.", "gui.abuseReport.sending.title": "Sendas vua raporto...", "gui.abuseReport.sent.title": "Raporto esas sendita", "gui.abuseReport.skin.title": "Raporti pri la ludanto - Haŭto", "gui.abuseReport.title": "<PERSON><PERSON><PERSON>", "gui.abuseReport.type.chat": "Mesaĝoj de la Babilejo", "gui.abuseReport.type.name": "Ludantnomo", "gui.abuseReport.type.skin": "<PERSON><PERSON><PERSON>", "gui.acknowledge": "<PERSON><PERSON><PERSON>", "gui.advancements": "Progresoj", "gui.all": "Omni", "gui.back": "<PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\nLernu pli ĉe la jenaj ligilo: %s", "gui.banned.description.permanent": "Via konto estas definitive malpermesita, kio signifas, ke vi ne povas ludi enreta aŭ aliĝi al Mondo.", "gui.banned.description.reason": "Ni lastatempe ricevis raporton pri malbona konduto de via konto. <PERSON>aj moderantoj nun revizis vian aferon kaj identigis ĝin kiel %s, kio kontraŭas la Normojn de la Minecraft Komunumo.", "gui.banned.description.reason_id": "Kodexo: %s", "gui.banned.description.reason_id_message": "Kodexo: %s - %s", "gui.banned.description.temporary": "%s Ĝis tiam, vi ne povas ludi enreta aŭ aliĝi al Mondo.", "gui.banned.description.temporary.duration": "Via konto estas tempore suspendita kaj estos reaktivigita post %s.", "gui.banned.description.unknownreason": "Ni lastatempe ricevis raporton pri malbona konduto de via konto. <PERSON><PERSON> moderantoj nun revizis vian aferon kaj konstatis, ke ĝi kontraŭas la Normojn de la Minecraft Komunumo.", "gui.banned.name.description": "Your current name - \"%s\" - violates our Community Standards. You can play singleplayer, but will need to change your name to play online.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.name.title": "La nomo ne estas permesita en laultularo", "gui.banned.reason.defamation_impersonation_false_information": "En personaĵo aŭ divido de informoj por ekspluati aŭ trompi aliajn", "gui.banned.reason.drugs": "Referencoj al malpermesitaj drogoj", "gui.banned.reason.extreme_violence_or_gore": "Prezentoj de reala ekstremeza violento aŭ sangiĝo", "gui.banned.reason.false_reporting": "<PERSON><PERSON>j malveraj aŭ malkorektaj raportoj", "gui.banned.reason.fraud": "Fraŭda akirado aŭ uzo de enhavo", "gui.banned.reason.generic_violation": "Kontraŭleĝa enirado en Komunumnormoj", "gui.banned.reason.harassment_or_bullying": "Abuza lingvo uzata direkte, damaĝe aliajn", "gui.banned.reason.hate_speech": "Malamo parolo aŭ diskriminacio", "gui.banned.reason.hate_terrorism_notorious_figure": "Referencoj al malamaj grupoj, terorismaj organizaĵoj, aŭ famaj personoj", "gui.banned.reason.imminent_harm_to_person_or_property": "Intenco kaŭzi damaĝon en la reala vivo al personoj aŭ bienoj", "gui.banned.reason.nudity_or_pornography": "Montri malĉastan aŭ pornografiaĵon", "gui.banned.reason.sexually_inappropriate": "Temoj aŭ enhavo de seksa naturo", "gui.banned.reason.spam_or_advertising": "Spamo aŭ reklamo", "gui.banned.skin.description": "Via nuna haŭto kontraŭas niajn Normojn de la Komunumo. Vi tamen povas ludi kun la defaŭlta haŭto aŭ elekti novan.\n\nPor lerni pli aŭ sendi revizion de via kazo, vizitu la jenan ligilon: %s", "gui.banned.skin.title": "La haŭto ne permesiĝas", "gui.banned.title.permanent": "Konto esas permanante blokusita", "gui.banned.title.temporary": "Konto esas tempale suspensita", "gui.cancel": "<PERSON><PERSON><PERSON>", "gui.chatReport.comments": "<PERSON><PERSON><PERSON>", "gui.chatReport.describe": "<PERSON><PERSON> detali helpos ni facar informita decido.", "gui.chatReport.discard.content": "Se vu livos, ica raporto e vua komenti esos perdita.\nKa vu esas certa, vu volas livar?", "gui.chatReport.discard.discard": "<PERSON><PERSON> ed e<PERSON><PERSON> raporto", "gui.chatReport.discard.draft": "Konservez kom klado", "gui.chatReport.discard.return": "<PERSON><PERSON> mod<PERSON>", "gui.chatReport.discard.title": "Ka efacar raporto e komenti?", "gui.chatReport.draft.content": "Ka vu volas durar modifikar l'existanta raporto od efacar ol e krear nova raporto?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "<PERSON><PERSON> mod<PERSON>", "gui.chatReport.draft.quittotitle.content": "Ka vu volas durar modifikar od efacar ol?", "gui.chatReport.draft.quittotitle.title": "Vu havas klado di raportado konversa qua perdos se vu livos", "gui.chatReport.draft.title": "Ka modifikar klado di raportado konversa?", "gui.chatReport.more_comments": "<PERSON><PERSON>ez deskriptar quo eventis:", "gui.chatReport.observed_what": "Pro quo vu raportas ico?", "gui.chatReport.read_info": "Lernu pri Raportado", "gui.chatReport.report_sent_msg": "Ni sucesante recevis vua raporto. Danko!\n\nNia esquado revuos ol tam balde kam posible.", "gui.chatReport.select_chat": "<PERSON><PERSON><PERSON><PERSON> mesaji raportar", "gui.chatReport.select_reason": "Selektez la kategorio di raporto", "gui.chatReport.selected_chat": "%s mesajo/i esas selektita raportar", "gui.chatReport.send": "Sendar raporto", "gui.chatReport.send.comments_too_long": "Voluntez kurtigar la komento", "gui.chatReport.send.no_reason": "<PERSON>untez selektar kategorio di raporto", "gui.chatReport.send.no_reported_messages": "<PERSON>untez selektar adminime una mesajo raportar", "gui.chatReport.send.too_many_messages": "Tro multa mesaji en la raporto", "gui.chatReport.title": "<PERSON><PERSON><PERSON> lude<PERSON>", "gui.chatSelection.context": "La mesaĝoj rilataj al ĉi tiu elekto estos inkluditaj por provizi plian kuntekston", "gui.chatSelection.fold": "%s mesaji esas celita", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s eniris la konverso", "gui.chatSelection.message.narrate": "%s dicis: %s ye %s", "gui.chatSelection.selected": "%s/%s mesajo/i esas selektita", "gui.chatSelection.title": "<PERSON><PERSON><PERSON><PERSON> mesaji raportar", "gui.continue": "<PERSON><PERSON><PERSON><PERSON>", "gui.copy_link_to_clipboard": "Kopii ligilon al presobufero", "gui.days": "%s tago(j)", "gui.done": "<PERSON><PERSON>", "gui.down": "Infre", "gui.entity_tooltip.type": "Tipo: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Rejected %s files", "gui.fileDropFailure.title": "Failed to add files", "gui.hours": "%s horo(j)", "gui.loadingMinecraft": "Ŝargado de Minecraft", "gui.minutes": "%s minuto(j)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s butono", "gui.narrate.editBox": "%s buxo di modifiko: %s", "gui.narrate.slider": "%s giltilo", "gui.narrate.tab": "%s langeto", "gui.no": "No", "gui.none": "<PERSON><PERSON>", "gui.ok": "O.K.", "gui.open_report_dir": "Open Report Directory", "gui.proceed": "Procedar", "gui.recipebook.moreRecipes": "Destra-klikez per pluso", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Serchez...", "gui.recipebook.toggleRecipes.all": "Montras omni", "gui.recipebook.toggleRecipes.blastable": "Montras forne<PERSON>li", "gui.recipebook.toggleRecipes.craftable": "Montras fabrikebli", "gui.recipebook.toggleRecipes.smeltable": "Montras fuzebli", "gui.recipebook.toggleRecipes.smokable": "Montras fumizebli", "gui.report_to_server": "Report To Server", "gui.socialInteractions.blocking_hint": "Administrar kun konto di Microsoft", "gui.socialInteractions.empty_blocked": "<PERSON><PERSON> ludanti blo<PERSON>ata en konverso", "gui.socialInteractions.empty_hidden": "<PERSON>ula ludanti celata en konverso", "gui.socialInteractions.hidden_in_chat": "Mesaji de %s celesos", "gui.socialInteractions.hide": "<PERSON>lar en konverso", "gui.socialInteractions.narration.hide": "<PERSON>lar mesaji de %s", "gui.socialInteractions.narration.report": "Raportar ludero %s", "gui.socialInteractions.narration.show": "Montrar mesaji de %s", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "<PERSON><PERSON><PERSON> nula ludanti kun ita uzernomo", "gui.socialInteractions.search_hint": "Ser<PERSON><PERSON>...", "gui.socialInteractions.server_label.multiple": "%s - %s ludanti", "gui.socialInteractions.server_label.single": "%s - %s ludanto", "gui.socialInteractions.show": "Montrar en konverso", "gui.socialInteractions.shown_in_chat": "Mesaji de %s montresos", "gui.socialInteractions.status_blocked": "Blokusita", "gui.socialInteractions.status_blocked_offline": "Barata - Eklinea", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Celata - Eklinea", "gui.socialInteractions.status_offline": "Ek<PERSON>a", "gui.socialInteractions.tab_all": "Omni", "gui.socialInteractions.tab_blocked": "<PERSON><PERSON>", "gui.socialInteractions.tab_hidden": "Nevidebla", "gui.socialInteractions.title": "Sociala Interakti", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON>", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON> lude<PERSON>", "gui.socialInteractions.tooltip.report.disabled": "La servado di raporto esas nedisponebla", "gui.socialInteractions.tooltip.report.no_messages": "Nula raportebla mesaji de ludero %s", "gui.socialInteractions.tooltip.report.not_reportable": "<PERSON>ca ludero ne esas raportebla, pro ke lia mesaji ne povas esar verifikita en ica servero", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON>", "gui.stats": "Statistiki", "gui.toMenu": "<PERSON><PERSON><PERSON> a serverlisto", "gui.toRealms": "<PERSON><PERSON> al la listo de Mondo", "gui.toTitle": "<PERSON><PERSON><PERSON> a titulskreno", "gui.toWorld": "<PERSON><PERSON> al la listo de Mondoj", "gui.togglable_slot": "<PERSON><PERSON><PERSON> por malaktivigi ludejon", "gui.up": "Supre", "gui.waitingForResponse.button.inactive": "Back (%ss)", "gui.waitingForResponse.title": "Waiting for Server", "gui.yes": "Yes", "hanging_sign.edit": "Redakti mesaĝon de pendanta signo", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Voko", "instrument.minecraft.dream_goat_horn": "Sonĝo", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Serĉi", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "Destruktar objekto", "inventory.hotbarInfo": "Konservar bendo kun %1$s+%2$s", "inventory.hotbarSaved": "Objektbendo konservesis (restaurar kun %1$s+%2$s)", "item.canBreak": "<PERSON><PERSON> minar:", "item.canPlace": "Pozebla sur:", "item.canUse.unknown": "Nekonata", "item.color": "Koloro: %s", "item.components": "Komponento(j) %s", "item.disabled": "Malŝaltita ero", "item.durability": "Dureso: %s / %s", "item.dyed": "<PERSON><PERSON><PERSON>", "item.minecraft.acacia_boat": "<PERSON><PERSON>", "item.minecraft.acacia_chest_boat": "<PERSON><PERSON> akacia kun kofro", "item.minecraft.allay_spawn_egg": "Kreurovo di alejo", "item.minecraft.amethyst_shard": "Ruptopeco ametista", "item.minecraft.angler_pottery_shard": "Fiŝkaptista A<PERSON><PERSON><PERSON>o", "item.minecraft.angler_pottery_sherd": "Fiŝkaptista A<PERSON><PERSON><PERSON>o", "item.minecraft.apple": "Pomo", "item.minecraft.archer_pottery_shard": "<PERSON><PERSON>", "item.minecraft.archer_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.armadillo_scute": "<PERSON><PERSON><PERSON>", "item.minecraft.armadillo_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.armor_stand": "Vestiero", "item.minecraft.arms_up_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.arms_up_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.axolotl_bucket": "Sitelo di axoloto", "item.minecraft.axolotl_spawn_egg": "Kreurovo di axoloto", "item.minecraft.baked_potato": "<PERSON><PERSON><PERSON> bakita", "item.minecraft.bamboo_chest_raft": "<PERSON>fto bambua kun kofro", "item.minecraft.bamboo_raft": "<PERSON><PERSON><PERSON> b<PERSON>", "item.minecraft.bat_spawn_egg": "Kreurovo di vespertilio", "item.minecraft.bee_spawn_egg": "Frayifanto-ovo di abelo", "item.minecraft.beef": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot": "Betravo", "item.minecraft.beetroot_seeds": "Semini di betravo", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "<PERSON><PERSON> birka", "item.minecraft.birch_chest_boat": "Batelo birka kun kofro", "item.minecraft.black_bundle": "Black Bundle", "item.minecraft.black_dye": "Nigra tinto", "item.minecraft.black_harness": "<PERSON> Harness", "item.minecraft.blade_pottery_shard": "Klinga Argilpeco", "item.minecraft.blade_pottery_sherd": "Klinga Argilpeco", "item.minecraft.blaze_powder": "<PERSON>ul<PERSON><PERSON> di blazo", "item.minecraft.blaze_rod": "Bastono di blazo", "item.minecraft.blaze_spawn_egg": "Kreurovo di blazo", "item.minecraft.blue_bundle": "Blue Bundle", "item.minecraft.blue_dye": "<PERSON><PERSON> tinto", "item.minecraft.blue_egg": "Blue Egg", "item.minecraft.blue_harness": "<PERSON>", "item.minecraft.bogged_spawn_egg": "Bogged Spawn Egg", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolt Armor Trim", "item.minecraft.bone": "<PERSON><PERSON>", "item.minecraft.bone_meal": "Polvo di osto", "item.minecraft.book": "Libro", "item.minecraft.bordure_indented_banner_pattern": "Bordure Indented Banner Pattern", "item.minecraft.bow": "Arkarmo", "item.minecraft.bowl": "<PERSON><PERSON>", "item.minecraft.bread": "Pan<PERSON>", "item.minecraft.breeze_rod": "Brizo Bastono", "item.minecraft.breeze_spawn_egg": "Brizo Naski Ovo", "item.minecraft.brewer_pottery_shard": "Bieranto Argilpeco", "item.minecraft.brewer_pottery_sherd": "Bieranto Argilpeco", "item.minecraft.brewing_stand": "Brasostativo", "item.minecraft.brick": "<PERSON><PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON> Bundle", "item.minecraft.brown_dye": "<PERSON><PERSON><PERSON> tinto", "item.minecraft.brown_egg": "<PERSON>", "item.minecraft.brown_harness": "<PERSON>", "item.minecraft.brush": "Pen<PERSON>", "item.minecraft.bucket": "<PERSON><PERSON>", "item.minecraft.bundle": "Fasko", "item.minecraft.bundle.empty": "Empty", "item.minecraft.bundle.empty.description": "Can hold a mixed stack of items", "item.minecraft.bundle.full": "Full", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "<PERSON><PERSON>lant<PERSON>rg<PERSON>", "item.minecraft.burn_pottery_sherd": "<PERSON><PERSON>lant<PERSON>rg<PERSON>", "item.minecraft.camel_spawn_egg": "Kreurovo di kamelo", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "Karoto sur bastono", "item.minecraft.cat_spawn_egg": "Kreurovo di kato", "item.minecraft.cauldron": "Kaldrono", "item.minecraft.cave_spider_spawn_egg": "Kreurovo di kavernaraneo", "item.minecraft.chainmail_boots": "<PERSON><PERSON>", "item.minecraft.chainmail_chestplate": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>ta", "item.minecraft.chainmail_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.chainmail_leggings": "<PERSON><PERSON><PERSON> ma<PERSON>", "item.minecraft.charcoal": "Lignakarbono", "item.minecraft.cherry_boat": "Ĉeriza Boato", "item.minecraft.cherry_chest_boat": "Ĉeriza <PERSON>o kun Kofro", "item.minecraft.chest_minecart": "Minvagono kun kofro", "item.minecraft.chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "Kreurovo di hano", "item.minecraft.chorus_fruit": "Chorusf<PERSON><PERSON>", "item.minecraft.clay_ball": "Argilbulo", "item.minecraft.clock": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.coal": "Karbono", "item.minecraft.coast_armor_trim_smithing_template": "Forĝarto Matrico", "item.minecraft.coast_armor_trim_smithing_template.new": "Coast Armor Trim", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON><PERSON> gado", "item.minecraft.cod_bucket": "Sitelo di gado", "item.minecraft.cod_spawn_egg": "Kreurovo di gado", "item.minecraft.command_block_minecart": "Minvagono kun bloko di komandi", "item.minecraft.compass": "<PERSON><PERSON>", "item.minecraft.cooked_beef": "Bifsteko", "item.minecraft.cooked_chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_cod": "Ko<PERSON>ta gado", "item.minecraft.cooked_mutton": "<PERSON><PERSON><PERSON> muto<PERSON>", "item.minecraft.cooked_porkchop": "Koquita porkokarno", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.cookie": "Bisquito", "item.minecraft.copper_ingot": "<PERSON><PERSON> kupra", "item.minecraft.cow_spawn_egg": "Kreurovo di bovo", "item.minecraft.creaking_spawn_egg": "Creaking Spawn Egg", "item.minecraft.creeper_banner_pattern": "Shablono di banero", "item.minecraft.creeper_banner_pattern.desc": "Creeperfacio", "item.minecraft.creeper_banner_pattern.new": "Creeper Charge Banner Pattern", "item.minecraft.creeper_spawn_egg": "Kreurovo di creepero", "item.minecraft.crossbow": "Arbalesto", "item.minecraft.crossbow.projectile": "Projektilo:", "item.minecraft.crossbow.projectile.multiple": "Projectile: %s x %s", "item.minecraft.crossbow.projectile.single": "Projectile: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON>", "item.minecraft.cyan_dye": "<PERSON><PERSON> tinto", "item.minecraft.cyan_harness": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Danĝera A<PERSON><PERSON><PERSON>o", "item.minecraft.danger_pottery_sherd": "Danĝera A<PERSON><PERSON><PERSON>o", "item.minecraft.dark_oak_boat": "<PERSON><PERSON>", "item.minecraft.dark_oak_chest_boat": "<PERSON>elo obs<PERSON>rquerka kun kofro", "item.minecraft.debug_stick": "Bastono di debugar", "item.minecraft.debug_stick.empty": "%s ne havas propaji", "item.minecraft.debug_stick.select": "selektis \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" a %s", "item.minecraft.diamond": "Diamanto", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_boots": "<PERSON><PERSON>", "item.minecraft.diamond_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "<PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "Piocho di<PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.disc_fragment_5": "Fragmento diska", "item.minecraft.disc_fragment_5.desc": "Muzikdisko - 5", "item.minecraft.dolphin_spawn_egg": "Kreurovo di delfino", "item.minecraft.donkey_spawn_egg": "Kreurovo di asno", "item.minecraft.dragon_breath": "Respirado di drako", "item.minecraft.dried_kelp": "<PERSON><PERSON><PERSON>", "item.minecraft.drowned_spawn_egg": "Kreurovo di dronito", "item.minecraft.dune_armor_trim_smithing_template": "Forĝarto Matrico", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON>ne Armor <PERSON>", "item.minecraft.echo_shard": "Eĥa A<PERSON><PERSON><PERSON>o", "item.minecraft.egg": "Ovo", "item.minecraft.elder_guardian_spawn_egg": "Kreurovo di seniora guardero", "item.minecraft.elytra": "<PERSON><PERSON>", "item.minecraft.emerald": "<PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "Libro sorcita", "item.minecraft.enchanted_golden_apple": "So<PERSON>ita pomo ora", "item.minecraft.end_crystal": "<PERSON><PERSON><PERSON>", "item.minecraft.ender_dragon_spawn_egg": "<PERSON>er <PERSON><PERSON><PERSON>", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Kreurovo di endermano", "item.minecraft.endermite_spawn_egg": "Kreurovo di endermito", "item.minecraft.evoker_spawn_egg": "Kreurovo di evokanto", "item.minecraft.experience_bottle": "Botelo di sorco", "item.minecraft.explorer_pottery_shard": "Esploranto Argilpeco", "item.minecraft.explorer_pottery_sherd": "Esploranto Argilpeco", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON> <PERSON> forĝarto", "item.minecraft.eye_armor_trim_smithing_template.new": "Eye Armor Trim", "item.minecraft.feather": "Plumo", "item.minecraft.fermented_spider_eye": "Okulo di araneo fermentacita", "item.minecraft.field_masoned_banner_pattern": "Field Masoned Banner Pattern", "item.minecraft.filled_map": "Mapo", "item.minecraft.fire_charge": "Fairbulo", "item.minecraft.firework_rocket": "Fuzeo di pirotekno", "item.minecraft.firework_rocket.flight": "Duro di flugo:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Stelo di pirotekno", "item.minecraft.firework_star.black": "Nigra", "item.minecraft.firework_star.blue": "<PERSON><PERSON>", "item.minecraft.firework_star.brown": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Individuala", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON><PERSON> ad", "item.minecraft.firework_star.flicker": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.gray": "Griza", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Cielblua", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.lime": "Klarverda", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.pink": "Rozea", "item.minecraft.firework_star.purple": "Purpura", "item.minecraft.firework_star.red": "Red<PERSON>", "item.minecraft.firework_star.shape": "Formo nesavata", "item.minecraft.firework_star.shape.burst": "Erupto", "item.minecraft.firework_star.shape.creeper": "Creeperformo", "item.minecraft.firework_star.shape.large_ball": "Globego", "item.minecraft.firework_star.shape.small_ball": "Globeto", "item.minecraft.firework_star.shape.star": "Stelformo", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.yellow": "Flava", "item.minecraft.fishing_rod": "Peskobastono", "item.minecraft.flint": "Silexo", "item.minecraft.flint_and_steel": "Silexo e stalo", "item.minecraft.flow_armor_trim_smithing_template": "Forĝista ŝablono", "item.minecraft.flow_armor_trim_smithing_template.new": "Flow Armor Trim", "item.minecraft.flow_banner_pattern": "Standarda bendero", "item.minecraft.flow_banner_pattern.desc": "Fluo", "item.minecraft.flow_banner_pattern.new": "<PERSON> <PERSON> Pattern", "item.minecraft.flow_pottery_sherd": "Flow Pottery Sherd", "item.minecraft.flower_banner_pattern": "Standarda desegno", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "Flower Charge Banner Pattern", "item.minecraft.flower_pot": "Flora vazo", "item.minecraft.fox_spawn_egg": "Kreurovo di foxo", "item.minecraft.friend_pottery_shard": "<PERSON><PERSON> a<PERSON> ŝerco", "item.minecraft.friend_pottery_sherd": "<PERSON><PERSON> a<PERSON> ŝerco", "item.minecraft.frog_spawn_egg": "Kreurovo di rano", "item.minecraft.furnace_minecart": "Minvagono kun furnazo", "item.minecraft.ghast_spawn_egg": "Kreurovo di ghasto", "item.minecraft.ghast_tear": "Lakrimo di ghasto", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON> botelo", "item.minecraft.glistering_melon_slice": "Loncho di melono brilanta", "item.minecraft.globe_banner_pattern": "Standarda desegno", "item.minecraft.globe_banner_pattern.desc": "Terkubo", "item.minecraft.globe_banner_pattern.new": "Globe Banner Pattern", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON>", "item.minecraft.glow_ink_sac": "<PERSON>ksak<PERSON> l<PERSON>", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.glow_squid_spawn_egg": "Kreurovo di lumkalmaro", "item.minecraft.glowstone_dust": "Lumpetra polvo", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON> korno", "item.minecraft.goat_spawn_egg": "Kreurovo di kapro", "item.minecraft.gold_ingot": "<PERSON><PERSON> ora", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON> ora", "item.minecraft.golden_apple": "Pomo ora", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON>a", "item.minecraft.golden_boots": "<PERSON><PERSON>a", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON> ora", "item.minecraft.golden_chestplate": "<PERSON><PERSON><PERSON> ora", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON> ora", "item.minecraft.golden_hoe": "<PERSON><PERSON> ora", "item.minecraft.golden_horse_armor": "<PERSON><PERSON><PERSON><PERSON> ora", "item.minecraft.golden_leggings": "<PERSON><PERSON><PERSON> ora", "item.minecraft.golden_pickaxe": "Piocho ora", "item.minecraft.golden_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_sword": "<PERSON><PERSON><PERSON> ora", "item.minecraft.gray_bundle": "<PERSON>", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON> tinto", "item.minecraft.gray_harness": "<PERSON>", "item.minecraft.green_bundle": "Green Bundle", "item.minecraft.green_dye": "<PERSON><PERSON>a tinto", "item.minecraft.green_harness": "<PERSON>ss", "item.minecraft.guardian_spawn_egg": "Kreurovo di guardero", "item.minecraft.gunpowder": "Kanonpulvero", "item.minecraft.guster_banner_pattern": "<PERSON>", "item.minecraft.guster_banner_pattern.desc": "Virvolto", "item.minecraft.guster_banner_pattern.new": "<PERSON><PERSON>", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON>y <PERSON>", "item.minecraft.happy_ghast_spawn_egg": "Happy Ghast Spawn Egg", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Kordio di la maro", "item.minecraft.heart_pottery_shard": "Fragmento de argila koro", "item.minecraft.heart_pottery_sherd": "Fragmento de argila koro", "item.minecraft.heartbreak_pottery_shard": "Fragmento de disrompita argila koro", "item.minecraft.heartbreak_pottery_sherd": "Fragmento de disrompita argila kero", "item.minecraft.hoglin_spawn_egg": "Kreurovo di hoglino", "item.minecraft.honey_bottle": "Botelo di mielo", "item.minecraft.honeycomb": "Vabo", "item.minecraft.hopper_minecart": "Minvagono kun funelego", "item.minecraft.horse_spawn_egg": "Kreurovo di kavalo", "item.minecraft.host_armor_trim_smithing_template": "Forĝa modelo", "item.minecraft.host_armor_trim_smithing_template.new": "Host <PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "<PERSON><PERSON> di argilo di kriado", "item.minecraft.howl_pottery_sherd": "<PERSON><PERSON> di argilo di kriado", "item.minecraft.husk_spawn_egg": "Kreurovo di husko", "item.minecraft.ink_sac": "Inksako", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON> fera", "item.minecraft.iron_boots": "<PERSON><PERSON> fera", "item.minecraft.iron_chestplate": "Torokuraso fera", "item.minecraft.iron_golem_spawn_egg": "Ovo di aparado di fero", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON> fera", "item.minecraft.iron_hoe": "Bineto fera", "item.minecraft.iron_horse_armor": "Kavalkuraso fera", "item.minecraft.iron_ingot": "Lingoto fera", "item.minecraft.iron_leggings": "<PERSON><PERSON><PERSON> fera", "item.minecraft.iron_nugget": "Pepiro fera", "item.minecraft.iron_pickaxe": "Piocho fera", "item.minecraft.iron_shovel": "<PERSON><PERSON><PERSON> fera", "item.minecraft.iron_sword": "Espado fera", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "<PERSON><PERSON>", "item.minecraft.jungle_chest_boat": "<PERSON><PERSON> maha<PERSON> kun kofro", "item.minecraft.knowledge_book": "Enciklopedio", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Sitelo di Lavao", "item.minecraft.lead": "<PERSON><PERSON>", "item.minecraft.leather": "<PERSON><PERSON>", "item.minecraft.leather_boots": "<PERSON><PERSON>", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_helmet": "<PERSON><PERSON> ledra", "item.minecraft.leather_horse_armor": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.light_blue_bundle": "Light Blue Bundle", "item.minecraft.light_blue_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON> tinto", "item.minecraft.light_blue_harness": "Light Blue Harness", "item.minecraft.light_gray_bundle": "Light Gray Bundle", "item.minecraft.light_gray_dye": "Klarg<PERSON><PERSON> tinto", "item.minecraft.light_gray_harness": "<PERSON> <PERSON>", "item.minecraft.lime_bundle": "Lime Bundle", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON> tinto", "item.minecraft.lime_harness": "<PERSON><PERSON>", "item.minecraft.lingering_potion": "Duranta pociono", "item.minecraft.lingering_potion.effect.awkward": "Duranta pociono stranja", "item.minecraft.lingering_potion.effect.empty": "Duranta pociono nefabrikebla", "item.minecraft.lingering_potion.effect.fire_resistance": "Duranta pociono di fairorezisto", "item.minecraft.lingering_potion.effect.harming": "Duranta pociono di domajo", "item.minecraft.lingering_potion.effect.healing": "Duranta pociono di remedio", "item.minecraft.lingering_potion.effect.infested": "Lingering Potion of Infestation", "item.minecraft.lingering_potion.effect.invisibility": "Duranta pociono di nevidebleso", "item.minecraft.lingering_potion.effect.leaping": "Duranta pociono di saltego", "item.minecraft.lingering_potion.effect.levitation": "Duranta pociono di flugeteso", "item.minecraft.lingering_potion.effect.luck": "Duranta pociono di chanco", "item.minecraft.lingering_potion.effect.mundane": "Duranta pociono ordinara", "item.minecraft.lingering_potion.effect.night_vision": "Duranta pociono di noktvidado", "item.minecraft.lingering_potion.effect.oozing": "Lingering Potion of Oozing", "item.minecraft.lingering_potion.effect.poison": "Duranta pociono di toxiko", "item.minecraft.lingering_potion.effect.regeneration": "Duranta pociono di regenereso", "item.minecraft.lingering_potion.effect.slow_falling": "Duranta pociono di lenta faligo", "item.minecraft.lingering_potion.effect.slowness": "Duranta pociono di lenteso", "item.minecraft.lingering_potion.effect.strength": "Duranta pociono di forteso", "item.minecraft.lingering_potion.effect.swiftness": "Duranta pociono di rapideso", "item.minecraft.lingering_potion.effect.thick": "Duranta pociono densa", "item.minecraft.lingering_potion.effect.turtle_master": "Duranta pociono di la tortugomastro", "item.minecraft.lingering_potion.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.water_breathing": "Duranta pociono di aquarespiro", "item.minecraft.lingering_potion.effect.weakness": "Duranta pociono di febleso", "item.minecraft.lingering_potion.effect.weaving": "Lingering Potion of Weaving", "item.minecraft.lingering_potion.effect.wind_charged": "Lingering Potion of Wind Charging", "item.minecraft.llama_spawn_egg": "Kreurovo di lamao", "item.minecraft.lodestone_compass": "<PERSON><PERSON>", "item.minecraft.mace": "<PERSON><PERSON>", "item.minecraft.magenta_bundle": "Magenta Bundle", "item.minecraft.magenta_dye": "Ma<PERSON>a tinto", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Kremo di magmo", "item.minecraft.magma_cube_spawn_egg": "Kreurovo di magmakubo", "item.minecraft.mangrove_boat": "<PERSON><PERSON>", "item.minecraft.mangrove_chest_boat": "<PERSON><PERSON> mangliera kun kofro", "item.minecraft.map": "Mapo vakua", "item.minecraft.melon_seeds": "Semini di melono", "item.minecraft.melon_slice": "Loncho di melono", "item.minecraft.milk_bucket": "Sitelo di lakto", "item.minecraft.minecart": "Minvagono", "item.minecraft.miner_pottery_shard": "<PERSON><PERSON> di argilo di mineanto", "item.minecraft.miner_pottery_sherd": "<PERSON><PERSON> di argilo di mineanto", "item.minecraft.mojang_banner_pattern": "Stendardo-Mustro", "item.minecraft.mojang_banner_pattern.desc": "Simbolo", "item.minecraft.mojang_banner_pattern.new": "<PERSON> Banner <PERSON>", "item.minecraft.mooshroom_spawn_egg": "Kreurovo di mooshroomo", "item.minecraft.mourner_pottery_shard": "<PERSON><PERSON> di argilo di ploranto", "item.minecraft.mourner_pottery_sherd": "<PERSON><PERSON> di argilo di ploranto", "item.minecraft.mule_spawn_egg": "Kreurovo di mulo", "item.minecraft.mushroom_stew": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11": "Muzikdisko", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Muzikdisko", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Muzikdisko", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Muzikdisko", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Muzikdisko", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Muzikdisko", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Music Disc", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Music Disc", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "Muzikdisko", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Music Disc", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Muzikdisko", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Muzikdisko", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Muzikdisko", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Muzikdisko", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Music Disc", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Muzikala disko", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Muzikdisko", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Muzikdisko", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Music Disc", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Muzikdisko", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Muzikdisko", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON>", "item.minecraft.name_tag": "Nometiketo", "item.minecraft.nautilus_shell": "<PERSON><PERSON> di nautilo", "item.minecraft.nether_brick": "Netherbriko", "item.minecraft.nether_star": "<PERSON><PERSON> di Nether", "item.minecraft.nether_wart": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_axe": "<PERSON><PERSON><PERSON> net<PERSON>ita", "item.minecraft.netherite_boots": "<PERSON><PERSON> netherita", "item.minecraft.netherite_chestplate": "<PERSON><PERSON><PERSON> netherita", "item.minecraft.netherite_helmet": "<PERSON><PERSON><PERSON> netherita", "item.minecraft.netherite_hoe": "Bineto netherita", "item.minecraft.netherite_ingot": "<PERSON><PERSON> netherita", "item.minecraft.netherite_leggings": "<PERSON><PERSON><PERSON> netherita", "item.minecraft.netherite_pickaxe": "Piocho netherita", "item.minecraft.netherite_scrap": "Fragmento netherita", "item.minecraft.netherite_shovel": "<PERSON><PERSON><PERSON> netherita", "item.minecraft.netherite_sword": "<PERSON><PERSON><PERSON> netherita", "item.minecraft.netherite_upgrade_smithing_template": "Forjo-<PERSON>ro", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherite Upgrade", "item.minecraft.oak_boat": "<PERSON><PERSON>", "item.minecraft.oak_chest_boat": "<PERSON><PERSON> querka kun kofro", "item.minecraft.ocelot_spawn_egg": "Kreurovo di oceloto", "item.minecraft.ominous_bottle": "Ominous <PERSON>", "item.minecraft.ominous_trial_key": "Ominous Trial Key", "item.minecraft.orange_bundle": "Orange Bundle", "item.minecraft.orange_dye": "<PERSON><PERSON><PERSON><PERSON> tinto", "item.minecraft.orange_harness": "Orange Harness", "item.minecraft.painting": "Pikturo", "item.minecraft.pale_oak_boat": "Pale Oak Boat", "item.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "item.minecraft.panda_spawn_egg": "Kreurovo di pando", "item.minecraft.paper": "Papero", "item.minecraft.parrot_spawn_egg": "Kreurovo di papagayo", "item.minecraft.phantom_membrane": "Membrano di fantomo", "item.minecraft.phantom_spawn_egg": "Kreurovo di fantomo", "item.minecraft.pig_spawn_egg": "Kreurovo di porko", "item.minecraft.piglin_banner_pattern": "Stendardo-Mustro", "item.minecraft.piglin_banner_pattern.desc": "Fasko", "item.minecraft.piglin_banner_pattern.new": "Snout Banner Pattern", "item.minecraft.piglin_brute_spawn_egg": "Kreurovo di piglinbruto", "item.minecraft.piglin_spawn_egg": "Kreurovo di piglino", "item.minecraft.pillager_spawn_egg": "Kreurovo di spoilanto", "item.minecraft.pink_bundle": "Pink Bundle", "item.minecraft.pink_dye": "<PERSON>ozea tinto", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "Pichilo-Planto", "item.minecraft.pitcher_pod": "Kruĉeto-Pod", "item.minecraft.plenty_pottery_shard": "<PERSON>lte da Poterca Ŝarĝo", "item.minecraft.plenty_pottery_sherd": "<PERSON>lte da Poterca Ŝardo", "item.minecraft.poisonous_potato": "Potato toxika", "item.minecraft.polar_bear_spawn_egg": "Kreurovo di urso blanka", "item.minecraft.popped_chorus_fruit": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON>", "item.minecraft.potato": "Potato", "item.minecraft.potion": "Pociono", "item.minecraft.potion.effect.awkward": "Pociono stranja", "item.minecraft.potion.effect.empty": "Pociono nefabrikebla", "item.minecraft.potion.effect.fire_resistance": "Pociono di fairorezisto", "item.minecraft.potion.effect.harming": "Pociono di domajo", "item.minecraft.potion.effect.healing": "Pociono di remedio", "item.minecraft.potion.effect.infested": "Potion of Infestation", "item.minecraft.potion.effect.invisibility": "Pociono di nevidebleso", "item.minecraft.potion.effect.leaping": "Pociono di saltego", "item.minecraft.potion.effect.levitation": "Pociono di flugeteso", "item.minecraft.potion.effect.luck": "Pociono di chanco", "item.minecraft.potion.effect.mundane": "Pociono ordinara", "item.minecraft.potion.effect.night_vision": "Pociono di noktvidado", "item.minecraft.potion.effect.oozing": "Potion of Oozing", "item.minecraft.potion.effect.poison": "Pociono di toxiko", "item.minecraft.potion.effect.regeneration": "Pociono di regenereso", "item.minecraft.potion.effect.slow_falling": "Pociono di lenta faligo", "item.minecraft.potion.effect.slowness": "Pociono di lenteso", "item.minecraft.potion.effect.strength": "Pociono di forteso", "item.minecraft.potion.effect.swiftness": "Pociono di rapideso", "item.minecraft.potion.effect.thick": "Pociono den<PERSON>", "item.minecraft.potion.effect.turtle_master": "Pociono di la tortugomastro", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.water_breathing": "Pociono di aquarespiro", "item.minecraft.potion.effect.weakness": "Pociono di febleso", "item.minecraft.potion.effect.weaving": "Potion of Weaving", "item.minecraft.potion.effect.wind_charged": "Potion of Wind Charging", "item.minecraft.pottery_shard_archer": "Arĉisto de Poterca Ŝarĝo", "item.minecraft.pottery_shard_arms_up": "Poterca Ŝarĝo de Brakoj Supre", "item.minecraft.pottery_shard_prize": "Premia Poterca Ŝarĝo", "item.minecraft.pottery_shard_skull": "Kranio de Poterca Ŝarĝo", "item.minecraft.powder_snow_bucket": "Sitelo di nivo pulvera", "item.minecraft.prismarine_crystals": "<PERSON><PERSON><PERSON> di prismarino", "item.minecraft.prismarine_shard": "Ruptopeco di prismarino", "item.minecraft.prize_pottery_shard": "Premia Poterca Ŝarĝo", "item.minecraft.prize_pottery_sherd": "Premia Poterca Ŝardo", "item.minecraft.pufferfish": "Suflantofisho", "item.minecraft.pufferfish_bucket": "Sitelo di suflantofisho", "item.minecraft.pufferfish_spawn_egg": "Frayifanto-ovo di suflantofisho", "item.minecraft.pumpkin_pie": "Torto di kukurbito", "item.minecraft.pumpkin_seeds": "Semini di kukurbito", "item.minecraft.purple_bundle": "Purple Bundle", "item.minecraft.purple_dye": "<PERSON><PERSON><PERSON> tinto", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "Quarco di <PERSON>her", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_foot": "Pedo di kuniklo", "item.minecraft.rabbit_hide": "Felo di kuniklo", "item.minecraft.rabbit_spawn_egg": "Kreurovo di kuniklo", "item.minecraft.rabbit_stew": "Kunikloragu<PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "Ŝablono por forĝado", "item.minecraft.raiser_armor_trim_smithing_template.new": "Raiser Armor Trim", "item.minecraft.ravager_spawn_egg": "Kreurovo di devastanto", "item.minecraft.raw_copper": "<PERSON><PERSON><PERSON>", "item.minecraft.raw_gold": "<PERSON><PERSON><PERSON> oro", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON> fero", "item.minecraft.recovery_compass": "Busolo di rekupero", "item.minecraft.red_bundle": "Red Bundle", "item.minecraft.red_dye": "<PERSON><PERSON> tinto", "item.minecraft.red_harness": "<PERSON>", "item.minecraft.redstone": "Pulvero di redstoneo", "item.minecraft.resin_brick": "Resin Brick", "item.minecraft.resin_clump": "<PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "Ŝablono por forĝado", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.rotten_flesh": "<PERSON><PERSON>", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.salmon_bucket": "Sitelo di salmono", "item.minecraft.salmon_spawn_egg": "Kreurovo di salmono", "item.minecraft.scrape_pottery_sherd": "Scrape Pottery Sherd", "item.minecraft.scute": "<PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Ŝablono por forĝado", "item.minecraft.sentry_armor_trim_smithing_template.new": "Sentry Armor Trim", "item.minecraft.shaper_armor_trim_smithing_template": "Ŝablono por forĝado", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> Armor <PERSON>", "item.minecraft.sheaf_pottery_shard": "Garbo de Poterca Ŝarĝo", "item.minecraft.sheaf_pottery_sherd": "Garbo de Poterca Ŝardo", "item.minecraft.shears": "Cizego", "item.minecraft.sheep_spawn_egg": "Kreurovo di mutono", "item.minecraft.shelter_pottery_shard": "Rifuĝejo de Poterca Ŝarĝo", "item.minecraft.shelter_pottery_sherd": "Rifuĝejo de Poterca Ŝardo", "item.minecraft.shield": "<PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON>gra shildo", "item.minecraft.shield.blue": "<PERSON><PERSON> s<PERSON>do", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.shield.cyan": "<PERSON><PERSON> s<PERSON>", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON> s<PERSON>do", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>do", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>do", "item.minecraft.shield.magenta": "<PERSON><PERSON>a shildo", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>do", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON> s<PERSON>do", "item.minecraft.shield.purple": "<PERSON><PERSON><PERSON> shildo", "item.minecraft.shield.red": "<PERSON><PERSON> s<PERSON>do", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON> s<PERSON>do", "item.minecraft.shield.yellow": "<PERSON><PERSON> shildo", "item.minecraft.shulker_shell": "Skalio di shulkero", "item.minecraft.shulker_spawn_egg": "Kreurovo di shulkero", "item.minecraft.sign": "<PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON><PERSON> Modelaĵo", "item.minecraft.silence_armor_trim_smithing_template.new": "Silence Armor Trim", "item.minecraft.silverfish_spawn_egg": "Kreurovo di argentafisho", "item.minecraft.skeleton_horse_spawn_egg": "Kreurovo di skeleta kavalo", "item.minecraft.skeleton_spawn_egg": "Kreurovo di skeleto", "item.minecraft.skull_banner_pattern": "Standarda Ŝablono", "item.minecraft.skull_banner_pattern.desc": "Kranio", "item.minecraft.skull_banner_pattern.new": "Skull Charge Banner Pattern", "item.minecraft.skull_pottery_shard": "Krania Poterca Ŝarĝo", "item.minecraft.skull_pottery_sherd": "Krania Poterca Ŝardo", "item.minecraft.slime_ball": "Slambulo", "item.minecraft.slime_spawn_egg": "Kreurovo di slamo", "item.minecraft.smithing_template": "<PERSON><PERSON><PERSON> Modelaĵo", "item.minecraft.smithing_template.applies_to": "A<PERSON><PERSON><PERSON><PERSON> al:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Aldoni metalaĵon aŭ kristalon", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Aldoni peco de armilo", "item.minecraft.smithing_template.armor_trim.ingredients": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.ingredients": "Ingrediencoj:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamanta Ekipaĵo", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Aldoni diamantan armilon, armilon, aŭ ilon", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Inferplata Slito", "item.minecraft.smithing_template.upgrade": "Altigo: ", "item.minecraft.sniffer_spawn_egg": "Esnifador Naskiĝa Ovo", "item.minecraft.snort_pottery_shard": "Frunza Poterca Ŝarĝo", "item.minecraft.snort_pottery_sherd": "Frunza Poterca Ŝardo", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON><PERSON> Modelaĵo", "item.minecraft.snout_armor_trim_smithing_template.new": "Snout Armor Trim", "item.minecraft.snow_golem_spawn_egg": "Eko-Ovo de Neĝa Viro", "item.minecraft.snowball": "Nivbul<PERSON>", "item.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON> spektrala", "item.minecraft.spider_eye": "<PERSON>ulo di araneo", "item.minecraft.spider_spawn_egg": "Kreurovo di araneo", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON><PERSON> Modelaĵo", "item.minecraft.spire_armor_trim_smithing_template.new": "Spire Arm<PERSON>", "item.minecraft.splash_potion": "Jetebla pociono", "item.minecraft.splash_potion.effect.awkward": "Jetebla pociono stranja", "item.minecraft.splash_potion.effect.empty": "Jetebla pociono nefabrikebla", "item.minecraft.splash_potion.effect.fire_resistance": "Jetebla pociono di fairorezisto", "item.minecraft.splash_potion.effect.harming": "Jetebla pociono di domajo", "item.minecraft.splash_potion.effect.healing": "Jetebla pociono di remedio", "item.minecraft.splash_potion.effect.infested": "Splash Potion of Infestation", "item.minecraft.splash_potion.effect.invisibility": "Jetebla pociono di nevidebleso", "item.minecraft.splash_potion.effect.leaping": "Jetebla pociono di saltego", "item.minecraft.splash_potion.effect.levitation": "Jetebla pociono di flugeteso", "item.minecraft.splash_potion.effect.luck": "Jetebla pociono di chanco", "item.minecraft.splash_potion.effect.mundane": "Jetebla pociono ordinara", "item.minecraft.splash_potion.effect.night_vision": "Jetebla pociono di noktvidado", "item.minecraft.splash_potion.effect.oozing": "Splash Potion of Oozing", "item.minecraft.splash_potion.effect.poison": "Jetebla pociono di toxiko", "item.minecraft.splash_potion.effect.regeneration": "Jetebla pociono di regenereso", "item.minecraft.splash_potion.effect.slow_falling": "Jetebla pociono di lenta faligo", "item.minecraft.splash_potion.effect.slowness": "Jetebla pociono di lenteso", "item.minecraft.splash_potion.effect.strength": "Jetebla pociono di forteso", "item.minecraft.splash_potion.effect.swiftness": "Jetebla pociono di rapideso", "item.minecraft.splash_potion.effect.thick": "Jetebla pociono densa", "item.minecraft.splash_potion.effect.turtle_master": "Jetebla pociono di tortugomastro", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON> a<PERSON>", "item.minecraft.splash_potion.effect.water_breathing": "Jetebla pociono di aquarespiro", "item.minecraft.splash_potion.effect.weakness": "Jetebla pociono di febleso", "item.minecraft.splash_potion.effect.weaving": "Splash Potion of Weaving", "item.minecraft.splash_potion.effect.wind_charged": "Splash Potion of Wind Charging", "item.minecraft.spruce_boat": "<PERSON><PERSON> a<PERSON>", "item.minecraft.spruce_chest_boat": "<PERSON>elo abieta kun kofro", "item.minecraft.spyglass": "Lorno", "item.minecraft.squid_spawn_egg": "Kreurovo di kalmaro", "item.minecraft.stick": "Bastono", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON> petra", "item.minecraft.stone_hoe": "Bineto petra", "item.minecraft.stone_pickaxe": "Piocho petra", "item.minecraft.stone_shovel": "<PERSON><PERSON><PERSON> petra", "item.minecraft.stone_sword": "Espado petra", "item.minecraft.stray_spawn_egg": "Kreurovo di vaganto", "item.minecraft.strider_spawn_egg": "Kreurovo di pazeganto", "item.minecraft.string": "<PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON><PERSON><PERSON><PERSON>ek<PERSON>", "item.minecraft.sweet_berries": "Dolca<PERSON><PERSON>", "item.minecraft.tadpole_bucket": "<PERSON>lo di ranlarvo", "item.minecraft.tadpole_spawn_egg": "Kreurovo di ranlarvo", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON><PERSON> Modelaĵo", "item.minecraft.tide_armor_trim_smithing_template.new": "Tide Armor Trim", "item.minecraft.tipped_arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.awkward": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.empty": "Pintata flecho nefabrikebla", "item.minecraft.tipped_arrow.effect.fire_resistance": "Flecho di fairorezisto", "item.minecraft.tipped_arrow.effect.harming": "Flecho di domajo", "item.minecraft.tipped_arrow.effect.healing": "Flecho di remedio", "item.minecraft.tipped_arrow.effect.infested": "Arrow of Infestation", "item.minecraft.tipped_arrow.effect.invisibility": "Flecho di nevidebleso", "item.minecraft.tipped_arrow.effect.leaping": "Flecho di saltego", "item.minecraft.tipped_arrow.effect.levitation": "Flecho di flugeteso", "item.minecraft.tipped_arrow.effect.luck": "Flecho di chanco", "item.minecraft.tipped_arrow.effect.mundane": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.night_vision": "Flecho di noktvidado", "item.minecraft.tipped_arrow.effect.oozing": "Arrow of Oozing", "item.minecraft.tipped_arrow.effect.poison": "Flecho di toxiko", "item.minecraft.tipped_arrow.effect.regeneration": "Flecho di regenereso", "item.minecraft.tipped_arrow.effect.slow_falling": "Flecho di lenta faligo", "item.minecraft.tipped_arrow.effect.slowness": "Flecho di lenteso", "item.minecraft.tipped_arrow.effect.strength": "Flecho di forteso", "item.minecraft.tipped_arrow.effect.swiftness": "Flecho di rapideso", "item.minecraft.tipped_arrow.effect.thick": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.turtle_master": "Flecho di la tortugomastro", "item.minecraft.tipped_arrow.effect.water": "Flecho di plaudeso", "item.minecraft.tipped_arrow.effect.water_breathing": "Flecho di aquarespiro", "item.minecraft.tipped_arrow.effect.weakness": "Flecho di febleso", "item.minecraft.tipped_arrow.effect.weaving": "Arrow of Weaving", "item.minecraft.tipped_arrow.effect.wind_charged": "Arrow of Wind Charging", "item.minecraft.tnt_minecart": "Minvagono kun TNT", "item.minecraft.torchflower_seeds": "<PERSON><PERSON><PERSON>", "item.minecraft.totem_of_undying": "Totemo di nemortivado", "item.minecraft.trader_llama_spawn_egg": "Kreurovo di lamao komercanta", "item.minecraft.trial_key": "Provo-Klavo", "item.minecraft.trident": "Tridento", "item.minecraft.tropical_fish": "<PERSON><PERSON> trop<PERSON>", "item.minecraft.tropical_fish_bucket": "Sitelo di fishi tropikala", "item.minecraft.tropical_fish_spawn_egg": "Kreurovo di fisho tropikala", "item.minecraft.turtle_helmet": "Skalio di torguto", "item.minecraft.turtle_scute": "<PERSON><PERSON>", "item.minecraft.turtle_spawn_egg": "Kreurovo di tortugo", "item.minecraft.vex_armor_trim_smithing_template": "Ŝablono por forĝado", "item.minecraft.vex_armor_trim_smithing_template.new": "Vex Armor Trim", "item.minecraft.vex_spawn_egg": "Kreurovo di vexo", "item.minecraft.villager_spawn_egg": "Kreurovo di vilajano", "item.minecraft.vindicator_spawn_egg": "Kreurovo di vindikatoro", "item.minecraft.wandering_trader_spawn_egg": "Kreurovo vaganta komercanto", "item.minecraft.ward_armor_trim_smithing_template": "Ŝablono por forĝado", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON>", "item.minecraft.warden_spawn_egg": "Ova por elirigo de Kustodanto", "item.minecraft.warped_fungus_on_a_stick": "Fungo bizara sur bastono", "item.minecraft.water_bucket": "<PERSON>lo di Aquo", "item.minecraft.wayfinder_armor_trim_smithing_template": "Ŝablono por forĝado", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Wayfinder Armor Trim", "item.minecraft.wheat": "Frumento", "item.minecraft.wheat_seeds": "Semini di frumento", "item.minecraft.white_bundle": "White Bundle", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON> tinto", "item.minecraft.white_harness": "<PERSON> Harness", "item.minecraft.wild_armor_trim_smithing_template": "Ŝablono por forĝado", "item.minecraft.wild_armor_trim_smithing_template.new": "Wild Armor Trim", "item.minecraft.wind_charge": "Vento ŝarĝo", "item.minecraft.witch_spawn_egg": "Kreurovo di sorcino", "item.minecraft.wither_skeleton_spawn_egg": "Kreurovo di witherskeleto", "item.minecraft.wither_spawn_egg": "Ova por elirigo de la Kontraŭulo", "item.minecraft.wolf_armor": "<PERSON><PERSON>", "item.minecraft.wolf_spawn_egg": "Kreurovo di volfo", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON> ligna", "item.minecraft.wooden_hoe": "<PERSON>eto ligna", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON>o ligna", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON> ligna", "item.minecraft.wooden_sword": "<PERSON><PERSON><PERSON> ligna", "item.minecraft.writable_book": "Libro e plumo", "item.minecraft.written_book": "Libro skribita", "item.minecraft.yellow_bundle": "Yellow Bundle", "item.minecraft.yellow_dye": "<PERSON>lava tinto", "item.minecraft.yellow_harness": "Yellow Harness", "item.minecraft.zoglin_spawn_egg": "Kreurovo di zoglino", "item.minecraft.zombie_horse_spawn_egg": "Kreurovo di zombia kavalo", "item.minecraft.zombie_spawn_egg": "Kreurovo di zombio", "item.minecraft.zombie_villager_spawn_egg": "Kreurovo di zombia vilajano", "item.minecraft.zombified_piglin_spawn_egg": "Kreurovo di piglino zombigita", "item.modifiers.any": "When equipped:", "item.modifiers.armor": "When worn:", "item.modifiers.body": "When equipped:", "item.modifiers.chest": "<PERSON><PERSON> sur la korpo:", "item.modifiers.feet": "<PERSON>am sur la piedoj:", "item.modifiers.hand": "When held:", "item.modifiers.head": "<PERSON><PERSON> sur la kapo:", "item.modifiers.legs": "<PERSON><PERSON> sur la kruroj:", "item.modifiers.mainhand": "<PERSON><PERSON> en la ĉefa mano:", "item.modifiers.offhand": "<PERSON><PERSON> en la maldekstra mano:", "item.modifiers.saddle": "When saddled:", "item.nbt_tags": "NBT: %s tago/i", "item.op_block_warning.line1": "Warning:", "item.op_block_warning.line2": "Use of this item might lead to command execution", "item.op_block_warning.line3": "Do not use unless you know the exact contents!", "item.unbreakable": "Neminebla", "itemGroup.buildingBlocks": "Bloki di konstrukto", "itemGroup.coloredBlocks": "<PERSON><PERSON><PERSON>", "itemGroup.combat": "<PERSON><PERSON><PERSON>", "itemGroup.consumables": "Konsumeblaji", "itemGroup.crafting": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.foodAndDrink": "Manĝaĵoj kaj trinkaĵoj", "itemGroup.functional": "Bloki funcionala", "itemGroup.hotbar": "<PERSON><PERSON> konser<PERSON>ta", "itemGroup.ingredients": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "itemGroup.inventory": "Inventario di transvivo", "itemGroup.natural": "Bloki naturala", "itemGroup.op": "Operaciaj Utilaĵoj", "itemGroup.redstone": "Bloki di redstoneo", "itemGroup.search": "<PERSON><PERSON><PERSON>", "itemGroup.spawnEggs": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.tools": "Utensili & utilaji", "item_modifier.unknown": "Nekonata ero-modifilo: %s", "jigsaw_block.final_state": "Divenas:", "jigsaw_block.generate": "<PERSON><PERSON>", "jigsaw_block.joint.aligned": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint.rollable": "Rulebla", "jigsaw_block.joint_label": "Tipo di junturo:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.levels": "Niveloj: %s", "jigsaw_block.name": "Nomo:", "jigsaw_block.placement_priority": "Prioritato de lokigado:", "jigsaw_block.placement_priority.tooltip": "<PERSON><PERSON> tiu ĉi Puzlo-bloko konektiĝas kun peco, tiu peco estas prilaborata laŭ la jara ordo por konektoj en la pli larĝa strukturo.\n\nPecoj estos prilaborataj en malkreskanta prioritato kun rompado de ordinaro de enmetado.", "jigsaw_block.pool": "<PERSON><PERSON>:", "jigsaw_block.selection_priority": "Elekta Prioritato:", "jigsaw_block.selection_priority.tooltip": "<PERSON>am la gepatra peco estas prilaborata por konektoj, ĉi tiu estas la ordo, en kiu ĉi tiu Puzlo-bloko provas konektiĝi kun sia celata peco.\n\nPuzloj estos prilaborataj en malkreskanta prioritato kun hazarda ordigado rompante egalajn.", "jigsaw_block.target": "Skopnomo:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON><PERSON> (Music Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Progresi", "key.attack": "Atakar/destruktar", "key.back": "Marchar indietre", "key.categories.creative": "Kreiva Modo", "key.categories.gameplay": "Ludeso", "key.categories.inventory": "Apertar/klozar inventario", "key.categories.misc": "Diversa", "key.categories.movement": "Movemento", "key.categories.multiplayer": "Multludanto", "key.categories.ui": "Estalajo di ludo", "key.chat": "Apertar konverso", "key.command": "Apertar komando", "key.drop": "Lasar objekto selektata falar", "key.forward": "<PERSON><PERSON> adelante", "key.fullscreen": "Ajustar plenskreno", "key.hotbar.1": "Butono di bendo 1", "key.hotbar.2": "Butono di bendo 2", "key.hotbar.3": "Butono di bendo 3", "key.hotbar.4": "Butono di bendo 4", "key.hotbar.5": "Butono di bendo 5", "key.hotbar.6": "Butono di bendo 6", "key.hotbar.7": "Butono di bendo 7", "key.hotbar.8": "Butono di bendo 8", "key.hotbar.9": "Butono di bendo 9", "key.inventory": "Apertar/klozar inventario", "key.jump": "Saltar", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps-seruro", "key.keyboard.comma": ",", "key.keyboard.delete": "<PERSON><PERSON>", "key.keyboard.down": "Flecho Infra", "key.keyboard.end": "Fino", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Eskapar", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "<PERSON><PERSON>", "key.keyboard.insert": "Inserto", "key.keyboard.keypad.0": "0 en kuseneto", "key.keyboard.keypad.1": "1 en kuseneto", "key.keyboard.keypad.2": "2 en kuseneto", "key.keyboard.keypad.3": "3 en kuseneto", "key.keyboard.keypad.4": "4 en kuseneto", "key.keyboard.keypad.5": "5 en kuseneto", "key.keyboard.keypad.6": "6 en kuseneto", "key.keyboard.keypad.7": "7 en kuseneto", "key.keyboard.keypad.8": "8 en kuseneto", "key.keyboard.keypad.9": "9 en kuseneto", "key.keyboard.keypad.add": "Klavkuseneto +", "key.keyboard.keypad.decimal": "Decimalo en kuseneto", "key.keyboard.keypad.divide": "/ en kuseneto", "key.keyboard.keypad.enter": "Enirar en kuseneto", "key.keyboard.keypad.equal": "= en kuseneto", "key.keyboard.keypad.multiply": "* en kuseneto", "key.keyboard.keypad.subtract": "- en kuseneto", "key.keyboard.left": "<PERSON><PERSON><PERSON> sinistra", "key.keyboard.left.alt": "Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Ctrl sinistra", "key.keyboard.left.shift": "Shift sinistra", "key.keyboard.left.win": "Win sinistra", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "<PERSON>um-<PERSON><PERSON><PERSON>", "key.keyboard.page.down": "<PERSON><PERSON><PERSON> adinfre", "key.keyboard.page.up": "<PERSON><PERSON><PERSON> adsupre", "key.keyboard.pause": "Pauzo", "key.keyboard.period": ".", "key.keyboard.print.screen": "Impr skreno", "key.keyboard.right": "<PERSON><PERSON><PERSON> de<PERSON>tra", "key.keyboard.right.alt": "Alt Gr", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Ctrl dextra", "key.keyboard.right.shift": "Shift dextra", "key.keyboard.right.win": "Win dextra", "key.keyboard.scroll.lock": "Scroll-se<PERSON><PERSON>", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Spaco", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "<PERSON>e Ligitaj", "key.keyboard.up": "Flecho supra", "key.keyboard.world.1": "Mondo 1", "key.keyboard.world.2": "Mondo 2", "key.left": "Destrosar sinistre", "key.loadToolbarActivator": "<PERSON><PERSON><PERSON> aktiva<PERSON> di <PERSON>o", "key.mouse": "Butono %1$s", "key.mouse.left": "<PERSON><PERSON><PERSON> butono", "key.mouse.middle": "<PERSON><PERSON> but<PERSON>", "key.mouse.right": "<PERSON><PERSON> butono", "key.pickItem": "<PERSON><PERSON><PERSON><PERSON> bloko", "key.playerlist": "List<PERSON>r ludanti", "key.quickActions": "Quick Actions", "key.right": "<PERSON><PERSON><PERSON> de<PERSON><PERSON>", "key.saveToolbarActivator": "Konservar aktivatanto di bendo", "key.screenshot": "Prenar skren<PERSON>o", "key.smoothCamera": "Ajustar kamero cinematika", "key.sneak": "Reptar", "key.socialInteractions": "Ekrano di sociala interago", "key.spectatorOutlines": "<PERSON><PERSON><PERSON><PERSON> (spektanti)", "key.sprint": "<PERSON><PERSON>", "key.swapOffhand": "S<PERSON><PERSON><PERSON> la objekto kun maldekstra mano", "key.togglePerspective": "Ajustar perspektivo", "key.use": "<PERSON><PERSON> objekto/pozar bloko", "known_server_link.announcements": "Announcements", "known_server_link.community": "Community", "known_server_link.community_guidelines": "Community Guidelines", "known_server_link.feedback": "Kontakti nin", "known_server_link.forums": "Forumo", "known_server_link.news": "<PERSON><PERSON>", "known_server_link.report_bug": "Report Server Bug", "known_server_link.status": "Statuso", "known_server_link.support": "Subteno", "known_server_link.website": "<PERSON><PERSON><PERSON>", "lanServer.otherPlayers": "<PERSON>ju<PERSON><PERSON> por altra ludanti", "lanServer.port": "Nombro di pordo", "lanServer.port.invalid": "Ne valida pordo.\nLasar la eniga skatolo malplena o enigu nombron inter 1024 kaj 65535.", "lanServer.port.invalid.new": "Ne valida pordo.\nLasar la eniga skatolo malplena aŭ enigu nombron inter %s kaj %s.", "lanServer.port.unavailable": "Pordo ne havebla.\nLasar la eniga skatolo malplena aŭ enigu alian nombron inter 1024 kaj 65535.", "lanServer.port.unavailable.new": "Pordo ne havebla.\nLasar la eniga skatolo malplena aŭ enigu alian nombron inter %s kaj %s.", "lanServer.scanning": "Serchas ludi en vua reto lokala", "lanServer.start": "Startar mondo di LAN", "lanServer.title": "Mondo en LAN", "language.code": "ido", "language.name": "Ido", "language.region": "Idia", "lectern.take_book": "Prenar libro", "loading.progress": "%s%%", "mco.account.privacy.info": "Lektar plu pri Mojang e legi di privateso", "mco.account.privacy.info.button": "Legu pli pri GDPR", "mco.account.privacy.information": "Mojang implementas certajn procedurojn por helpi protekti infanojn kaj ilian privatecon, inkluzive de konformado kun la Leĝo pri la Protekto de Infanretaj Privatecaj <PERSON>toj (COPPA) kaj la Ĝenerala Reguligado de Datprotekto (GDPR).\n\nEble vi bezonos akiri la konsenton de gepatroj antaŭ ol vi povos aliri vian Realms-konton.", "mco.account.privacyinfo": "Mojang aplikas ula procedi por helpar protektar pueri e lia privateso inkluze konsentar kun l’Akto pri enlinea privateso-protektado di pueri (COPPA) e Regulizo general pri donaji-protekteso (GDPR).\n\nVu forsan mustas obtenar konsento patrala ante vu povas acesar vua Realms-konto.\n\nSe vu havas plu anciena Minecraft-konto (se vu eniras per vua uzero-nomo), vu mustas transformar vua konto a Mojang-konto por acesar Realms.", "mco.account.update": "Aktualigar konto", "mco.activity.noactivity": "<PERSON>eniu agado dum la lastaj %s tago(j)", "mco.activity.title": "<PERSON><PERSON> di ludanto", "mco.backup.button.download": "Deskargar maxim recenta", "mco.backup.button.reset": "Rekomencigi la mondon", "mco.backup.button.restore": "Restaurar", "mco.backup.button.upload": "Alŝuti la mondon", "mco.backup.changes.tooltip": "<PERSON><PERSON>", "mco.backup.entry": "Sekurkopio (%s)", "mco.backup.entry.description": "Priskribo", "mco.backup.entry.enabledPack": "<PERSON><PERSON><PERSON><PERSON><PERSON>(j)", "mco.backup.entry.gameDifficulty": "Urbeco di Malfacilo di Ludo", "mco.backup.entry.gameMode": "<PERSON><PERSON><PERSON> de ludo", "mco.backup.entry.gameServerVersion": "Versio de la ludo-servilo", "mco.backup.entry.name": "Nom", "mco.backup.entry.seed": "Semo", "mco.backup.entry.templateName": "Nomo de la ŝablono", "mco.backup.entry.undefined": "Nedifinita ŝanĝo", "mco.backup.entry.uploaded": "Alŝutita", "mco.backup.entry.worldType": "<PERSON><PERSON><PERSON> de mondo", "mco.backup.generate.world": "<PERSON><PERSON><PERSON> mondo", "mco.backup.info.title": "Ŝanĝoj de la lasta sekurkopiaĵo", "mco.backup.narration": "Kopio de %s", "mco.backup.nobackups": "Ica realmo nune havas nula kopiuri sekura.", "mco.backup.restoring": "Restauras vua realm", "mco.backup.unknown": "Nekonata", "mco.brokenworld.download": "<PERSON><PERSON><PERSON>", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "Voluntez ristartar o selektar altra mondo.", "mco.brokenworld.message.line2": "<PERSON>u povas anke deskargar la mondo a modo di un ludanto.", "mco.brokenworld.minigame.title": "Ica ludeto ne ankore suportesas", "mco.brokenworld.nonowner.error": "<PERSON>untez vartar por la posedanto dil realmo a ristartar la mondo", "mco.brokenworld.nonowner.title": "La mondo esas obsoleta", "mco.brokenworld.play": "<PERSON><PERSON>", "mco.brokenworld.reset": "Rekomenci", "mco.brokenworld.title": "Vua nuna mondo ne ankore suportesas", "mco.client.incompatible.msg.line1": "Vua kliento esas nekonciliebla kun Realms.", "mco.client.incompatible.msg.line2": "Voluntez uzar la versiono maxim recenta di Minecraft.", "mco.client.incompatible.msg.line3": "Realms esas nekonciliebla kun proboversioni.", "mco.client.incompatible.title": "Kliento nekonciliebla!", "mco.client.outdated.stable.version": "Your client version (%s) is not compatible with Realms.\n\nPlease use the most recent version of Minecraft.", "mco.client.unsupported.snapshot.version": "Your client version (%s) is not compatible with Realms.\n\nRealms is not available for this snapshot version.", "mco.compatibility.downgrade": "Rebaŝigi", "mco.compatibility.downgrade.description": "Ĉi tiu mondo estis laste ludita en versio %s; vi estas en versio %s. Rebaŝigo de la mondo povus kaŭzi korupton - ni ne garantias, ke ĝi ŝarĝiĝos aŭ funkciigos.\n\nKopio de via mondo estos konservita sub 'Mondreservoj'. Bonvolu restaŭri vian mondon se necese.", "mco.compatibility.incompatible.popup.title": "Incompatible version", "mco.compatibility.incompatible.releaseType.popup.message": "The world you are trying to join is incompatible with the version you are on.", "mco.compatibility.incompatible.series.popup.message": "This world was last played in version %s; you are on version %s.\n\nThese series are not compatible with each other. A new world is needed to play on this version.", "mco.compatibility.unverifiable.message": "La versio, en kiu ĉi tiu mondo laste estis ludita, ne povis esti konfirmita. Se la mondo estas pliigita aŭ malpliigita, aŭtomate kreas kaj konservas kopion sub 'Mondreservoj\".", "mco.compatibility.unverifiable.title": "Kongrueco ne verifiebla", "mco.compatibility.upgrade": "Altigo", "mco.compatibility.upgrade.description": "Ta mondo estis laste ludita en versio %s; vi estas je versio %s.\n\n<PERSON><PERSON> de via mondo estos konservita sub «Mondaj rezervkopi». Bonvolu restaŭri vian mondon, se necese.", "mco.compatibility.upgrade.friend.description": "This world was last played in version %s; you are on version %s.\n\nA backup of the world will be saved under \"World Backups\".\n\nThe owner of the Realm can restore the world if needed.", "mco.compatibility.upgrade.title": "Ĉu vi vere volas altigi vian mondon?", "mco.configure.current.minigame": "Nuna", "mco.configure.world.activityfeed.disabled": "Fluo di ludanti tempale nekapabiligata", "mco.configure.world.backup": "Sekura kopiuri di mondo", "mco.configure.world.buttons.activity": "Aktiveso di ludanti", "mco.configure.world.buttons.close": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.delete": "<PERSON><PERSON>", "mco.configure.world.buttons.done": "<PERSON><PERSON>", "mco.configure.world.buttons.edit": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.invite": "Invi<PERSON> ludanto", "mco.configure.world.buttons.moreoptions": "<PERSON><PERSON><PERSON><PERSON> plusa", "mco.configure.world.buttons.newworld": "New World", "mco.configure.world.buttons.open": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.options": "Selekti di mondo", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Select Region...", "mco.configure.world.buttons.resetworld": "Ristartar mondo", "mco.configure.world.buttons.save": "Save", "mco.configure.world.buttons.settings": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.subscription": "Abono", "mco.configure.world.buttons.switchminigame": "<PERSON><PERSON> lude<PERSON>", "mco.configure.world.close.question.line1": "Vua realmo divenos nedisponebla.", "mco.configure.world.close.question.line2": "Ĉu vi certas, ke vi volas daŭrigi?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "<PERSON><PERSON><PERSON> la realmo...", "mco.configure.world.commandBlocks": "Bloki di komandi", "mco.configure.world.delete.button": "Efacar <PERSON>", "mco.configure.world.delete.question.line1": "Vua realmo efacesos permanente", "mco.configure.world.delete.question.line2": "Ĉu vi certas, ke vi volas daŭrigi?", "mco.configure.world.description": "Deskripturo di realmo", "mco.configure.world.edit.slot.name": "Nomo di mondo", "mco.configure.world.edit.subscreen.adventuremap": "Kelka ajusti esas nekapabla pro ke vua nuna mondo esas aventuro", "mco.configure.world.edit.subscreen.experience": "Kelka ajusti esas nekapabla pro ke vua nuna mondo esas experienco", "mco.configure.world.edit.subscreen.inspiration": "Kelka ajusti esas nekapabligata pro ke vua nuna mondo esas inspiro", "mco.configure.world.forceGameMode": "<PERSON><PERSON><PERSON> ludo<PERSON>", "mco.configure.world.invite.narration": "<PERSON>i havas %s novajn invitojn(j)", "mco.configure.world.invite.profile.name": "Nomo", "mco.configure.world.invited": "Invitita", "mco.configure.world.invited.number": "Invitita (%s)", "mco.configure.world.invites.normal.tooltip": "Uzanto ordinara", "mco.configure.world.invites.ops.tooltip": "Operacanto", "mco.configure.world.invites.remove.tooltip": "Efacar", "mco.configure.world.leave.question.line1": "Se vu livos ica realmo, vu ne povos rivenar sen ke vu riinvitesos", "mco.configure.world.leave.question.line2": "Ĉu vi certas, ke vi volas daŭrigi?", "mco.configure.world.loading": "Loading Realm", "mco.configure.world.location": "Lok<PERSON>", "mco.configure.world.minigame": "Nuna: %s", "mco.configure.world.name": "Nomo di realmo", "mco.configure.world.opening": "Apertas la realmo...", "mco.configure.world.players.error": "Ludanto kun nomo donita ne existas", "mco.configure.world.players.inviting": "Invi<PERSON>a luda<PERSON>...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "Vua mondo regeneresos e vua nuna mondo perdesos", "mco.configure.world.reset.question.line2": "Ĉu vi certas, ke vi volas daŭrigi?", "mco.configure.world.resourcepack.question": "You need a custom resource pack to play on this Realm\n\nDo you want to download it and play?", "mco.configure.world.resourcepack.question.line1": "Vu bezonas havajaro personalizita por ludar en ica realmo", "mco.configure.world.resourcepack.question.line2": "Ka vu volas deskargar ol ed instalar ol automatale por ludar?", "mco.configure.world.restore.download.question.line1": "La mondo deskargesos ed adjuntesos a vua mondi di un ludanto.", "mco.configure.world.restore.download.question.line2": "Ka vu volas procedar?", "mco.configure.world.restore.question.line1": "Vua mondo restauresos al dato '%s' (%s)", "mco.configure.world.restore.question.line2": "Ĉu vi certas, ke vi volas daŭrigi?", "mco.configure.world.settings.expired": "You cannot edit settings of an expired Realm", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot": "Mondo %s", "mco.configure.world.slot.empty": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "Vua realmo movesos ad altra mondo", "mco.configure.world.slot.switch.question.line2": "Ĉu vi certas, ke vi volas daŭrigi?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON> a mondo", "mco.configure.world.slot.tooltip.active": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot.tooltip.minigame": "<PERSON><PERSON> por ludeto", "mco.configure.world.spawnAnimals": "Genitar animali", "mco.configure.world.spawnMonsters": "<PERSON><PERSON><PERSON>", "mco.configure.world.spawnNPCs": "<PERSON><PERSON><PERSON>", "mco.configure.world.spawnProtection": "Protekto di geniteyo", "mco.configure.world.spawn_toggle.message": "Malŝalti tiun opcio'n FORIGOS ĈIUNAJN jam ekzistantajn entojn de tiu tipo", "mco.configure.world.spawn_toggle.message.npc": "Malŝalti tiun opcio'n FORIGOS ĈIUNAJN jam ekzistantajn entojn de tiu tipo, kiel Villagers", "mco.configure.world.spawn_toggle.title": "Averto!", "mco.configure.world.status": "<PERSON><PERSON>", "mco.configure.world.subscription.day": "<PERSON><PERSON>", "mco.configure.world.subscription.days": "<PERSON><PERSON>", "mco.configure.world.subscription.expired": "Expirita", "mco.configure.world.subscription.extend": "<PERSON><PERSON><PERSON> a<PERSON>", "mco.configure.world.subscription.less_than_a_day": "<PERSON> kam un dio", "mco.configure.world.subscription.month": "<PERSON><PERSON>", "mco.configure.world.subscription.months": "<PERSON><PERSON>", "mco.configure.world.subscription.recurring.daysleft": "Automatale rinovigesis en", "mco.configure.world.subscription.recurring.info": "Ŝanĝoj faritaj al via abono ĉe Realms, kiel ekzemple ŝanĝo de tempo aŭ malŝaltado de ripetanta fakturado, ne estos efektivaj ĝis via sekvanta faktur-dato.", "mco.configure.world.subscription.remaining.days": "%1$s tago(j)", "mco.configure.world.subscription.remaining.months": "%1$s monato(j)", "mco.configure.world.subscription.remaining.months.days": "%1$s monato(j), %2$s tago(j)", "mco.configure.world.subscription.start": "Dato di starto", "mco.configure.world.subscription.tab": "Subscription", "mco.configure.world.subscription.timeleft": "Tempo cetera", "mco.configure.world.subscription.title": "<PERSON><PERSON> a<PERSON>o", "mco.configure.world.subscription.unknown": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON> mondo", "mco.configure.world.switch.slot.subtitle": "<PERSON>ca mondo esas vakua, selektez quale krear vua mondo", "mco.configure.world.title": "Konfigurizar realmo:", "mco.configure.world.uninvite.player": "Ĉu vi certas, ke vi volas nuligi la inviton por '%s'?", "mco.configure.world.uninvite.question": "Ka vu certe volas desinvitar", "mco.configure.worlds.title": "<PERSON><PERSON>", "mco.connect.authorizing": "Ensalutado...", "mco.connect.connecting": "Konektas al realmo...", "mco.connect.failed": "Faliis a konektar al realmo", "mco.connect.region": "Server region: %s", "mco.connect.success": "<PERSON><PERSON>", "mco.create.world": "<PERSON><PERSON><PERSON>", "mco.create.world.error": "Vu mustas skribar nomo!", "mco.create.world.failed": "Failed to create world!", "mco.create.world.reset.title": "<PERSON><PERSON><PERSON> mondo...", "mco.create.world.skip": "<PERSON><PERSON><PERSON>", "mco.create.world.subtitle": "<PERSON><PERSON><PERSON><PERSON>, selektez qua mondo vu volas pozar sur vua nova realmo", "mco.create.world.wait": "<PERSON><PERSON><PERSON>...", "mco.download.cancelled": "<PERSON><PERSON><PERSON> c<PERSON>", "mco.download.confirmation.line1": "La mondo ke vu deskargos esas plu granda kam %s", "mco.download.confirmation.line2": "Vu ne povos kargar ica mondo a vue realmo itere", "mco.download.confirmation.oversized": "The world you are going to download is larger than %s\n\nYou won't be able to upload this world to your Realm again", "mco.download.done": "<PERSON><PERSON><PERSON> finis", "mco.download.downloading": "<PERSON><PERSON><PERSON>", "mco.download.extracting": "Extraktas", "mco.download.failed": "<PERSON><PERSON><PERSON>", "mco.download.percent": "%s %%", "mco.download.preparing": "Preparas deskargo", "mco.download.resourcePack.fail": "Malsukcesis elŝuti la risursosakon!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Deskargas mondo maxim recenta", "mco.error.invalid.session.message": "Voluntez probar a ristartar Minecraft", "mco.error.invalid.session.title": "Sesiono nevalida", "mco.errorMessage.6001": "Kliento esas obsoleta", "mco.errorMessage.6002": "Kondicioni di servado ne aceptesis", "mco.errorMessage.6003": "<PERSON><PERSON>", "mco.errorMessage.6004": "Limito di kargo atinge<PERSON>", "mco.errorMessage.6005": "<PERSON><PERSON> blo<PERSON>", "mco.errorMessage.6006": "La mondo estas malnovigita", "mco.errorMessage.6007": "Uzanto estas en tro multaj Realms", "mco.errorMessage.6008": "Nevalida nomo de la mondo Realm", "mco.errorMessage.6009": "Nevalida priskribo de la mondo Realm", "mco.errorMessage.connectionFailure": "<PERSON><PERSON><PERSON>, voluntez riprobar plu tarde.", "mco.errorMessage.generic": "<PERSON><PERSON>: ", "mco.errorMessage.initialize.failed": "Failed to initialize Realm", "mco.errorMessage.noDetails": "Ne estas provizitaj detaloj pri la eraro", "mco.errorMessage.realmsService": "<PERSON><PERSON> (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Ne povis konekti al Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Ne povis kontroli la konforman version, ricevis respondo: %s", "mco.errorMessage.retry": "Reprovi la operacion", "mco.errorMessage.serviceBusy": "Realms okupata en tiu momento. Bonvolu reprovi konekti vin al via Realm post kelkaj minutoj.", "mco.gui.button": "Butono", "mco.gui.ok": "Bone", "mco.info": "Informo!", "mco.invited.player.narration": "Invitita uzanto %s", "mco.invites.button.accept": "Aceptar", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "<PERSON><PERSON> inviti vartanta!", "mco.invites.pending": "<PERSON><PERSON> invito(j)!", "mco.invites.title": "<PERSON><PERSON><PERSON>a", "mco.minigame.world.changeButton": "Selektar altra ludeto", "mco.minigame.world.info.line1": "Ica tempe remplasos vua mondo per ludeto!", "mco.minigame.world.info.line2": "Vu povos rivenar plu tarde a vua mondo originala sen perdar ulo.", "mco.minigame.world.noSelection": "<PERSON><PERSON><PERSON> facar se<PERSON>o", "mco.minigame.world.restore": "Finigas ludeto...", "mco.minigame.world.restore.question.line1": "La ludeto finos e vua realmo restauresos.", "mco.minigame.world.restore.question.line2": "Ka vu certe volas facar ico?", "mco.minigame.world.selected": "Ludeto selektata:", "mco.minigame.world.slot.screen.title": "<PERSON><PERSON> mondo...", "mco.minigame.world.startButton": "<PERSON><PERSON>", "mco.minigame.world.starting.screen.title": "Startas ludeto...", "mco.minigame.world.stopButton": "Finigar ludeto", "mco.minigame.world.switch.new": "Ka vu volas selektar altra ludeto?", "mco.minigame.world.switch.title": "Ŝanĝi la malgrandan ludon", "mco.minigame.world.title": "Chanjar <PERSON>o a ludeto", "mco.news": "Informo di Realms", "mco.notification.dismiss": "<PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON>i nun", "mco.notification.transferSubscription.message": "La abonoj de Java Realms transiras al Microsoft Store. Ne lasu vian abonon eksvalidi! Transdonu nun kaj ricevu 30 tagojn da Realms senpage. Iru al via Profilo ĉe minecraft.net por transdoni vian abonon.", "mco.notification.visitUrl.buttonText.default": "Malfermi ligilon", "mco.notification.visitUrl.message.default": "Bonvolu viziti la suban ligilon", "mco.onlinePlayers": "Online Players", "mco.play.button.realm.closed": "Realm is closed", "mco.question": "<PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "<PERSON><PERSON><PERSON>", "mco.reset.world.experience": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.generate": "Nova mondo", "mco.reset.world.inspiration": "Inspiro", "mco.reset.world.resetting.screen.title": "Ristartas mondo...", "mco.reset.world.seed": "Semino (neobligata)", "mco.reset.world.template": "Shabloni di mondo", "mco.reset.world.title": "Rekomenci la mondon", "mco.reset.world.upload": "<PERSON><PERSON><PERSON> mondo", "mco.reset.world.warning": "Ico remplasos la nuna mondo di vua realmo", "mco.selectServer.buy": "Ko<PERSON>rar realmo!", "mco.selectServer.close": "<PERSON><PERSON><PERSON>", "mco.selectServer.closed": "<PERSON><PERSON> k<PERSON>", "mco.selectServer.closeserver": "<PERSON><PERSON><PERSON>", "mco.selectServer.configure": "Konfigurizar <PERSON>o", "mco.selectServer.configureRealm": "Agordi la mondon Realm", "mco.selectServer.create": "<PERSON><PERSON><PERSON>o", "mco.selectServer.create.subtitle": "Elektu la mondon, kiun vi volas aldoni al via nova mondo realm", "mco.selectServer.expired": "Relamo expirita", "mco.selectServer.expiredList": "Vua realmo expiris", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": "Vua periodo di probo finis", "mco.selectServer.expires.day": "Expiros ye un dio", "mco.selectServer.expires.days": "Expiros ye %s dii", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON>", "mco.selectServer.leave": "<PERSON><PERSON>", "mco.selectServer.loading": "Ŝargado de la listo de Realms", "mco.selectServer.mapOnlySupportedForVersion": "Ica mapo esas nesuportata en %s", "mco.selectServer.minigame": "Ludeto:", "mco.selectServer.minigameName": "Malgranda ludo: %s", "mco.selectServer.minigameNotSupportedInVersion": "Ne povas ludar ica ludeto en %s", "mco.selectServer.noRealms": "Ŝajnas, ke vi ne havas Realm. Aldonu Realm por ludi kune kun viaj amikoj.", "mco.selectServer.note": "Avertez:", "mco.selectServer.open": "Apertar realmo", "mco.selectServer.openserver": "Malfermi realm", "mco.selectServer.play": "<PERSON><PERSON>", "mco.selectServer.popup": "Realms esas sekura e simpla moyeno a juar mondo enlinea di Minecraft kun maxime dek amiki unfoye. Ol suportas multa ludeti ed abundo di mondi personalizita! Nur la posedanto dil realmo devas pagar.", "mco.selectServer.purchase": "Aldoni Realm", "mco.selectServer.trial": "Aquirar probo!", "mco.selectServer.uninitialized": "<PERSON><PERSON><PERSON> por krear vua nova realmo!", "mco.snapshot.createSnapshotPopup.text": "Vi pretas krei senkostan Snapshot-realon, kiu estos rilatita kun via pagita Realms-abono. Ĉi tiu nova Snapshot-realo estos alirebla tiel longe, kiel aktiva estas via pagita abono. Via pagita realo ne estos influata.", "mco.snapshot.createSnapshotPopup.title": "<PERSON><PERSON><PERSON>?", "mco.snapshot.creating": "<PERSON><PERSON><PERSON>...", "mco.snapshot.description": "\"Kun \"%s\"", "mco.snapshot.friendsRealm.downgrade": "Vi bezonas havi la version %s por aliĝi al ĉi tiu Realm", "mco.snapshot.friendsRealm.upgrade": "%s bezonas plibonigi si<PERSON>, antaŭ ol vi povos ludigi de ĉi tiu versio", "mco.snapshot.paired": "Ĉi tiu Ekzemplo de Realm estas kunparigita kun \"%s\"", "mco.snapshot.parent.tooltip": "U<PERSON> la lastan eldonon de Minecraft por ludi en ĉi tiu Realm", "mco.snapshot.start": "Ekstartu liberan Ekzemplon de Realm", "mco.snapshot.subscription.info": "Tiu ĉi Antaŭa Versio de Realm estos kunligita kun via abono de Realm '%s'. Ĝi restos aktiva tiom longe, kiom la kunligita Realm.", "mco.snapshot.tooltip": "Uzu la Ekzemplan Realms por antaŭenrigardi venontajn versiojn de Minecraft, kiuj povas enhavi novajn trajtojn kaj aliajn ŝanĝojn.\n\nVi povas trovi viajn normalajn Realms en la publikigita versio de la ludo.", "mco.snapshotRealmsPopup.message": "Realms nun haveblas en Ekzemploj ekde Ekzemplo 23w41a. Ĉiu abono de Realms liveras libera Ekzemplo de Realm, kiu estas aparta de via normala Java Realm!", "mco.snapshotRealmsPopup.title": "Realms nun disponeblas en Antaŭaj Versioj", "mco.snapshotRealmsPopup.urlText": "Scii pli", "mco.template.button.publisher": "Editanto", "mco.template.button.select": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.button.trailer": "Avanprizento", "mco.template.default.name": "Shablono di mondo", "mco.template.info.tooltip": "<PERSON><PERSON><PERSON> dil kreanto", "mco.template.name": "Shablono", "mco.template.select.failure": "Ni ne povis riganar la listo di kontentajo por ica kategorio. Voluntez spektar vua konekto al Interreto, o riprobar plu tarde.", "mco.template.select.narrate.authors": "Autori: %s", "mco.template.select.narrate.version": "versiono %s", "mco.template.select.none": "Oops, ol semblas ke ica kategorio di kontentajo esas vakua prezente. Voluntez rivenar plu tarde por nova kontentajo o se vu esas kreanto, %s.", "mco.template.select.none.linkTitle": "konsiderez prizentar kelko ipsa", "mco.template.title": "Mondomodeloj", "mco.template.title.minigame": "<PERSON><PERSON><PERSON>", "mco.template.trailer.tooltip": "Filmeto di mapo", "mco.terms.buttons.agree": "Aceptar", "mco.terms.buttons.disagree": "<PERSON><PERSON><PERSON>", "mco.terms.sentence.1": "Me aceptas la", "mco.terms.sentence.2": "kondicioni di servado pri Minecraft Realms", "mco.terms.title": "Kondicioni di servado pri Realms", "mco.time.daysAgo": "Antaŭ %1$s tago(j)", "mco.time.hoursAgo": "Antaŭ %1$s horo(j)", "mco.time.minutesAgo": "Antaŭ %1$s minuto(j)", "mco.time.now": "nun", "mco.time.secondsAgo": "Antaŭ %1$s sekundo(j)", "mco.trial.message.line1": "Ka vu volas havar vua propra realmo?", "mco.trial.message.line2": "Kliktez hike por plusa informo!", "mco.upload.button.name": "<PERSON><PERSON><PERSON>", "mco.upload.cancelled": "<PERSON><PERSON> cesi<PERSON>", "mco.upload.close.failure": "Ne povis klozar vua realmo, voluntez riprobar plu tarde", "mco.upload.done": "<PERSON><PERSON> finis", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Kargo faliis! (%s)", "mco.upload.failed.too_big.description": "The selected world is too big. The maximum allowed size is %s.", "mco.upload.failed.too_big.title": "World too big", "mco.upload.hardcore": "Mondi extrema ne povas karges<PERSON>!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Preparas vua mondo", "mco.upload.select.world.none": "<PERSON><PERSON> mondi di un ludanto trovesis!", "mco.upload.select.world.subtitle": "<PERSON><PERSON><PERSON> selektar mondo di un ludanto por kargar", "mco.upload.select.world.title": "Alŝuti la mondon", "mco.upload.size.failure.line1": "'%s' esas tro granda!", "mco.upload.size.failure.line2": "Ol esas %s. La maxima dimensiono permisata esas %s.", "mco.upload.uploading": "<PERSON><PERSON><PERSON> '%s'", "mco.upload.verifying": "<PERSON><PERSON><PERSON><PERSON> mondo", "mco.version": "Versio: %s", "mco.warning": "Averto!", "mco.worldSlot.minigame": "<PERSON><PERSON><PERSON> ludo", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "Deskonektar", "menu.feedback": "Feedback...", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "Menuo di ludo", "menu.modded": " (kun modifiko)", "menu.multiplayer": "<PERSON><PERSON><PERSON> ludanti", "menu.online": "Minecraft Realms", "menu.options": "Selekti...", "menu.paused": "<PERSON><PERSON> pauza<PERSON>", "menu.playdemo": "Ludar demo-mondo", "menu.playerReporting": "<PERSON><PERSON><PERSON> lude<PERSON>", "menu.preparingSpawn": "Preparas genitareo: %s%%", "menu.quick_actions": "Quick Actions...", "menu.quick_actions.title": "Quick Actions", "menu.quit": "<PERSON><PERSON> ludo", "menu.reportBugs": "<PERSON><PERSON><PERSON> er<PERSON>", "menu.resetdemo": "Ridefinar demo-mondo", "menu.returnToGame": "<PERSON><PERSON><PERSON> a ludo", "menu.returnToMenu": "Konservar e livar a titulo", "menu.savingChunks": "Konservas chunki", "menu.savingLevel": "Konse<PERSON>s mondo", "menu.sendFeedback": "Donar retroago", "menu.server_links": "Server Links...", "menu.server_links.title": "Server Links", "menu.shareToLan": "Apertar a LAN", "menu.singleplayer": "Un ludanto", "menu.working": "<PERSON><PERSON><PERSON><PERSON>...", "merchant.deprecated": "<PERSON><PERSON><PERSON>i rifurnisas ad maxime du foyi per dio.", "merchant.level.1": "Novico", "merchant.level.2": "Apprentiso", "merchant.level.3": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.4": "Experto", "merchant.level.5": "Maestro", "merchant.title": "%s - %s", "merchant.trades": "<PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Presez %1$s por decensar", "multiplayer.applyingPack": "Aplikante risurcpakon", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "Serveri di autentikigo ne funcionas. Voluntez riprobar plu tarde, pardonez!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "Vu esas blokusata de ica servero", "multiplayer.disconnect.banned.expiration": "\n<PERSON>ua blokuso efacesos ye %s", "multiplayer.disconnect.banned.reason": "Vu esas blokusata de ica servero.\nKauzo: %s", "multiplayer.disconnect.banned_ip.expiration": "\nVia malpermeso estos forigita je %s", "multiplayer.disconnect.banned_ip.reason": "Vua IP-adreso esas blokusata de ica servero.\nKauzo: %s", "multiplayer.disconnect.chat_validation_failed": "Mesaĝo en la babilejo nevalidas", "multiplayer.disconnect.duplicate_login": "Vu acesis ye altra loko", "multiplayer.disconnect.expired_public_key": "Malvalida publika ŝlosilo de profilo. <PERSON><PERSON><PERSON><PERSON>, ĉu via sistemo havas sinkronizitan tempon, kaj provu restartigi vian ludon.", "multiplayer.disconnect.flying": "Flugo ne esas habilitata en ica servero", "multiplayer.disconnect.generic": "Deskonektita", "multiplayer.disconnect.idling": "Vu ociis tro longe!", "multiplayer.disconnect.illegal_characters": "Simboli ilegala en babliado", "multiplayer.disconnect.incompatible": "Kliento nekonciliebla! Voluntez uzar %s", "multiplayer.disconnect.invalid_entity_attacked": "Probas atakar ento nevalida", "multiplayer.disconnect.invalid_packet": "La servilo sendis nevalidan pakaĵon", "multiplayer.disconnect.invalid_player_data": "Nevalidaj l<PERSON>j da<PERSON>", "multiplayer.disconnect.invalid_player_movement": "Pakato di movo di ludanto nevalida ricevesis", "multiplayer.disconnect.invalid_public_key_signature": "Nevalida subskribo por publika ŝlosilo de profilo.\nProvu restartigi vian ludon.", "multiplayer.disconnect.invalid_public_key_signature.new": "Nevalida subskribo por publika ŝlosilo de profilo.\nProvu restartigi vian ludon.", "multiplayer.disconnect.invalid_vehicle_movement": "Pakato di movo di veturilo nevalida ricevesis", "multiplayer.disconnect.ip_banned": "Vua IP-adreso blokusesis de ica servero", "multiplayer.disconnect.kicked": "Ekpulsesis per operacanto", "multiplayer.disconnect.missing_tags": "<PERSON><PERSON><PERSON> nekompleta di tagi recevesis de servilo. Voluntez kontaktar direktanto di servilo.", "multiplayer.disconnect.name_taken": "Ta nomo esas ja prenata", "multiplayer.disconnect.not_whitelisted": "Vu ne esas blanka-listata en ica servero!", "multiplayer.disconnect.out_of_order_chat": "Alvenis babileja pakaĵo senordo. Ĉu ŝanĝiĝis via sistemo horo?", "multiplayer.disconnect.outdated_client": "Nekongrua kliento! Bonvolu uzi %s", "multiplayer.disconnect.outdated_server": "Nekongrua kliento! Bonvolu uzi %s", "multiplayer.disconnect.server_full": "La servero esas plena!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON>", "multiplayer.disconnect.slow_login": "Prenis trop multa tempo por acesar", "multiplayer.disconnect.too_many_pending_chats": "<PERSON><PERSON> multaj nekonfirm<PERSON><PERSON> babilmesagxoj", "multiplayer.disconnect.transfers_disabled": "Servilo ne akceptas transdonojn", "multiplayer.disconnect.unexpected_query_response": "Neexpektata donaji personalizita de kliento", "multiplayer.disconnect.unsigned_chat": "Ricevis babilmesaĝon kun mankanta aŭ nevalida subskribo.", "multiplayer.disconnect.unverified_username": "Faliis a verifikar vua uzernomo!", "multiplayer.downloadingStats": "Riganas statistiki...", "multiplayer.downloadingTerrain": "Char<PERSON> tereno...", "multiplayer.lan.server_found": "Nova servilo trovita: %s", "multiplayer.message_not_delivered": "Ne povas livrar mesajo, spektez logi di servero: %s", "multiplayer.player.joined": "%s eniris la ludo", "multiplayer.player.joined.renamed": "%s (olim savita kom %s) eniris la ludo", "multiplayer.player.left": "%s livis la ludo", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "<PERSON><PERSON> ludo<PERSON>: %s", "multiplayer.requiredTexturePrompt.disconnect": "La servilo postulas propran risurcpakon", "multiplayer.requiredTexturePrompt.line1": "Ĉi tiu servilo postulas la uzon de propra risurcpako.", "multiplayer.requiredTexturePrompt.line2": "Malakcepti ĉi tiun propran risurcpakon forigos vin de ĉi tiu servilo.", "multiplayer.socialInteractions.not_available": "Sociala interakti esas nur disponebla en mondi di plura ludanti", "multiplayer.status.and_more": "... e %s pluse ...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON>", "multiplayer.status.cannot_connect": "<PERSON>e povas konektar a servero", "multiplayer.status.cannot_resolve": "Ne povas resolvar <PERSON>mo", "multiplayer.status.finished": "<PERSON><PERSON>", "multiplayer.status.incompatible": "Versiono kekonciliebla!", "multiplayer.status.motd.narration": "Taga mesaĝo: %s", "multiplayer.status.no_connection": "(nula konekto)", "multiplayer.status.old": "Obsoleta", "multiplayer.status.online": "<PERSON><PERSON>", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s milisekundoj", "multiplayer.status.pinging": "Pingas...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s ek %s ludantoj ĉeestas", "multiplayer.status.quitting": "Abandonas", "multiplayer.status.request_handled": "Demando di stando traktesis", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Ricevis stando nedemandita", "multiplayer.status.version.narration": "Versio di servilo: %s", "multiplayer.stopSleeping": "<PERSON>ar lito", "multiplayer.texturePrompt.failure.line1": "Pakaĵo di risurso di servilo ne povas esti aplikitaj", "multiplayer.texturePrompt.failure.line2": "Ia funkciado qua postulas proprajn risursojn povas ne funkcii kiel atendite", "multiplayer.texturePrompt.line1": "Ica servero rekomendas l'uzo di havajaro personalizita.", "multiplayer.texturePrompt.line2": "Ka vu volas deskargar ed instalar ol automatale?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMesaĝo di servilo:\n%s", "multiplayer.title": "<PERSON><PERSON> plura ludanti", "multiplayer.unsecureserver.toast": "Mesaĝoj senditaj sur ĉi tiu servilo povas esti modifitaj kaj eble ne reflektas la originalan mesaĝon", "multiplayer.unsecureserver.toast.title": "Ĉataj mesaĝoj ne povas esti certigitaj", "multiplayerWarning.check": "Ne montrar ica skreno itere", "multiplayerWarning.header": "Avertez: Ludado enlinea de exterludanto", "multiplayerWarning.message": "Avertez: Ludado enlinea ofresas per servili extera ke ne esas posedata, direktata, o surveyata per Mojang Studios o Microsoft. Dum ludado enlinea, vu forsan exposesas a mesaji nemoderata od altra tipi di kontento uzantkreata ke forsan ne esar konventanta por omnu.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Botono", "narration.button.usage.focused": "Al<PERSON><PERSON><PERSON> la 'Enter'-klavon por aktivigi", "narration.button.usage.hovered": "Alklaku la maldekstran butonon por aktivigi", "narration.checkbox": "Markobutono: %s", "narration.checkbox.usage.focused": "Presez Enter por ajustar", "narration.checkbox.usage.hovered": "Sinistra-klikez por ajustar", "narration.component_list.usage": "Presez Tab por navigar a sequanta elemento", "narration.cycle_button.usage.focused": "Presez Enter por chanjar a %s", "narration.cycle_button.usage.hovered": "Sinistra-klikez por chanjar a %s", "narration.edit_box": "Redaktado-bokso: %s", "narration.item": "Item: %s", "narration.recipe": "Recepto por %s", "narration.recipe.usage": "Sinistra-klikez por selektar", "narration.recipe.usage.more": "Dextra-klikez por montrar plusa recepti", "narration.selection.usage": "Presez supre ed infre klavi por swichar inter selektaji", "narration.slider.usage.focused": "Presez sinistre o dextre klavi por chanjar valoro", "narration.slider.usage.hovered": "Tranez glitflugilo por chanjar valoro", "narration.suggestion": "Sugesto selektata: %s di %s: %s", "narration.suggestion.tooltip": "Sugesto selektata: %s di %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Puŝu la Tab klavon por cikli al la sekva sugesto", "narration.suggestion.usage.cycle.hidable": "Puŝu la Tab klavon por cikli al la sekva sugesto, aŭ la Ekskluzivigu klavon por forlasi sugestojn", "narration.suggestion.usage.fill.fixed": "Puŝu la Tab klavon por uzi la sugeston", "narration.suggestion.usage.fill.hidable": "Puŝu la Tab klavon por uzi la sugeston, aŭ la Ekskluzivigu klavon por forlasi sugestojn", "narration.tab_navigation.usage": "Puŝu Ctrl kaj Tab por ŝalti inter langetoj", "narrator.button.accessibility": "Acesebles<PERSON>", "narrator.button.difficulty_lock": "<PERSON><PERSON> di facileso", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "Neklefagita", "narrator.button.language": "<PERSON><PERSON>", "narrator.controls.bound": "%s ligesis a %s", "narrator.controls.reset": "Ridefinar butono %s", "narrator.controls.unbound": "%s ne ligesas", "narrator.joining": "Eniras", "narrator.loading": "Charjas: %s", "narrator.loading.done": "<PERSON><PERSON>", "narrator.position.list": "Lineo di listo selektata: %s di %s", "narrator.position.object_list": "Elemento di lineo selektata: %s di %s", "narrator.position.screen": "Elemento skrena %s di %s", "narrator.position.tab": "Elektita langeto %s el %s", "narrator.ready_to_play": "Preta por ludi", "narrator.screen.title": "Titulskreno", "narrator.screen.usage": "Uzez kursoro di mauso o Tab klavo por selektar elemento", "narrator.select": "Selektata: %s", "narrator.select.world": "Selektata %s, plu recente ludis: %s, %s, %s, versiono: %s", "narrator.select.world_info": "Elektita %s, lasta ludado: %s, %s", "narrator.toast.disabled": "Naracanto estas desablata", "narrator.toast.enabled": "Naracanto estas habilitata", "optimizeWorld.confirm.description": "Ico probos a maxbonigar vua mondo per certigar su omna donaji amasigesas en la formato di ludo maxim recenta. Ico forsan prenar tempo tre longa, dependas pri vua mondo. <PERSON>nde finota, vua mondo forsan ludar plu rapide ma ne plus esos konciliebla kun versioni plu anciena di ludo. Ka vu certe volas procedar?", "optimizeWorld.confirm.proceed": "Create Backup and Optimize", "optimizeWorld.confirm.title": "Maxbonigar mondo", "optimizeWorld.info.converted": "<PERSON><PERSON><PERSON> chunki: %s", "optimizeWorld.info.skipped": "Omisis chunki: %s", "optimizeWorld.info.total": "Totalo di chunki: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "<PERSON><PERSON><PERSON> chunki...", "optimizeWorld.stage.failed": "Faliis! :(", "optimizeWorld.stage.finished": "Finado...", "optimizeWorld.stage.finished.chunks": "Finado de ŝargo de peci...", "optimizeWorld.stage.finished.entities": "Finado de ŝargo de entecoj...", "optimizeWorld.stage.finished.poi": "Finishing up upgrading points of interest...", "optimizeWorld.stage.upgrading": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.chunks": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.entities": "Upgrading all entities...", "optimizeWorld.stage.upgrading.poi": "Upgrading all points of interest...", "optimizeWorld.title": "Maxbonigas mondo '%s'", "options.accessibility": "Atingeblecaj Agordoj...", "options.accessibility.high_contrast": "Alta kontrasto", "options.accessibility.high_contrast.error.tooltip": "Risurso-pakaĵo kun alta kontrasto ne disponeblas", "options.accessibility.high_contrast.tooltip": "Pligrandigas la kontraston de UI-elementoj", "options.accessibility.high_contrast_block_outline": "High Contrast Block Outlines", "options.accessibility.high_contrast_block_outline.tooltip": "Enhances the block outline contrast of the targeted block.", "options.accessibility.link": "Alirebla Gvidilo", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON> Blur", "options.accessibility.menu_background_blurriness.tooltip": "Changes the blurriness of menu backgrounds.", "options.accessibility.narrator_hotkey": "<PERSON><PERSON><PERSON> por Legant<PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "\"Permesas ŝalti kaj malŝalti la rakontanton per 'Cmd+B'\"", "options.accessibility.narrator_hotkey.tooltip": "Ebligas ŝalti la leganton per 'Ctrl+B'", "options.accessibility.panorama_speed": "Skrolrapideco de panoramo", "options.accessibility.text_background": "Dopaframo di texto", "options.accessibility.text_background.chat": "Ĉatado", "options.accessibility.text_background.everywhere": "Omnaloke", "options.accessibility.text_background_opacity": "Opakeso di dopaframo di texto", "options.accessibility.title": "Ajusti di acesebleso...", "options.allowServerListing": "<PERSON><PERSON><PERSON> servilo-liston", "options.allowServerListing.tooltip": "Ser<PERSON><PERSON><PERSON> povas listigi luda<PERSON> en sia publika stato.\nSe ĉi tiu opcio estas malaktivigita, via nomo ne aperos en tiaj listoj.", "options.ao": "<PERSON><PERSON> glata", "options.ao.max": "<PERSON><PERSON>", "options.ao.min": "Minimo", "options.ao.off": "NO", "options.attack.crosshair": "Skopo", "options.attack.hotbar": "<PERSON><PERSON>", "options.attackIndicator": "Indikilo di atako", "options.audioDevice": "A<PERSON>ato", "options.audioDevice.default": "<PERSON><PERSON><PERSON>", "options.autoJump": "Saltar automate", "options.autoSuggestCommands": "Sugesti por komandi", "options.autosaveIndicator": "Indikilo de aŭtomata konservo", "options.biomeBlendRadius": "Mixo di biomi", "options.biomeBlendRadius.1": "NEAGANTA (Maxim rapida)", "options.biomeBlendRadius.11": "11x11 (Extrema)", "options.biomeBlendRadius.13": "13x13 (Pompoza)", "options.biomeBlendRadius.15": "15x15 (<PERSON><PERSON>)", "options.biomeBlendRadius.3": "3x3 (Rapida)", "options.biomeBlendRadius.5": "5x5 (Normala)", "options.biomeBlendRadius.7": "7x7 (Alta)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON> alta)", "options.chat": "<PERSON><PERSON><PERSON><PERSON> de <PERSON> Ĉato...", "options.chat.color": "<PERSON><PERSON><PERSON>", "options.chat.delay": "Prokrast-tempo de la Ĉato: %s sekundoj", "options.chat.delay_none": "Prokrast-tempo de la Ĉato: <PERSON><PERSON><PERSON>", "options.chat.height.focused": "Alto enfokigita", "options.chat.height.unfocused": "Alto neenfokigita", "options.chat.line_spacing": "Linispaco", "options.chat.links": "Ligili al Interreto", "options.chat.links.prompt": "Avertar sur ligili", "options.chat.opacity": "Opakeso di konverso", "options.chat.scale": "Grandeso di texto konversala", "options.chat.title": "Ajusti di konverso...", "options.chat.visibility": "Kon<PERSON><PERSON>", "options.chat.visibility.full": "<PERSON><PERSON><PERSON>", "options.chat.visibility.hidden": "Kaŝita", "options.chat.visibility.system": "<PERSON><PERSON> k<PERSON>", "options.chat.width": "<PERSON><PERSON><PERSON><PERSON>", "options.chunks": "%s chunki", "options.clouds.fancy": "Ĉika", "options.clouds.fast": "Rapida", "options.controls": "<PERSON><PERSON><PERSON>...", "options.credits_and_attribution": "<PERSON><PERSON><PERSON><PERSON> kaj <PERSON>...", "options.damageTiltStrength": "Difekt-kliniĝo", "options.damageTiltStrength.tooltip": "La kvanto de kamera skuado pro doloro.", "options.darkMojangStudiosBackgroundColor": "Monkroma Logo", "options.darkMojangStudiosBackgroundColor.tooltip": "Ŝanĝas la koloron de la fono de la ŝargoskermo de Mojang Studios al nigra.", "options.darknessEffectScale": "<PERSON><PERSON><PERSON> pulsado", "options.darknessEffectScale.tooltip": "Kontrolas kiel multe la efiko de mallumo pulsas kiam Vardeno aŭ Skulk-kriono donas ĝin al vi.", "options.difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.easy": "Facila", "options.difficulty.easy.info": "Malamikaj bestoj naskiĝas sed igas malpli da vundo. Malsato malplenigas la manĝometon kaj senigas la sanon ĝis 5 koroj.", "options.difficulty.hard": "<PERSON><PERSON><PERSON>", "options.difficulty.hard.info": "Malamikaj bestoj naskiĝas kaj igas pli da vundo. Malsato malplenigas la manĝometon kaj elĉerpas tutan sanon.", "options.difficulty.hardcore": "Ekstrema", "options.difficulty.normal": "Normala", "options.difficulty.normal.info": "Malamikaj bestoj naskiĝas kaj igas la standardan vundon. Malsato malplenigas la manĝometon kaj forprenas sanon ĝis duono de koro.", "options.difficulty.online": "Malsimpleco de la servilo", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "Ne malamikaj bestoj kaj nur kelkaj neŭtralaj bestoj naskiĝas. La malsatometro ne malplenigas kaj la sano regeneriĝas kun la tempo.", "options.directionalAudio": "<PERSON><PERSON><PERSON>", "options.directionalAudio.off.tooltip": "Klasika Sterea sono", "options.directionalAudio.on.tooltip": "Uzas HRTF-bazan direkta sonorilaton por plibonigi la imiton de 3D-sono. Postulas HRTF-kompatan sonorilecon, kaj estas plej bone spertata per hejdmajstroj.", "options.discrete_mouse_scroll": "Skrolanto distinta", "options.entityDistanceScaling": "Disto de ento", "options.entityShadows": "Ombri di enti", "options.font": "Tiparo-agordo...", "options.font.title": "Tiparo-agordo", "options.forceUnicodeFont": "Koaktar unicodo", "options.fov": "Agro di vidado", "options.fov.max": "Quake Pro", "options.fov.min": "Normala", "options.fovEffectScale": "Efekti pri agro di vidado", "options.fovEffectScale.tooltip": "Regas kiom la vidkampo povas ŝanĝiĝi kun ludadaj efikoj.", "options.framerate": "%s FPS", "options.framerateLimit": "FPS maxima", "options.framerateLimit.max": "<PERSON><PERSON><PERSON>", "options.fullscreen": "Plenskreno", "options.fullscreen.current": "Aktuala", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "Rezolvo plenskrena", "options.fullscreen.unavailable": "Ajusto nedisponebla", "options.gamma": "<PERSON><PERSON><PERSON><PERSON>", "options.gamma.default": "Defaŭlta", "options.gamma.max": "Brilanta", "options.gamma.min": "<PERSON><PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Brilo-Rapideco", "options.glintSpeed.tooltip": "Kontroli la rapidon de la videbla brileto sur ensorĉitaj objektoj.", "options.glintStrength": "Briletoforto", "options.glintStrength.tooltip": "Kontroli la grado de transpareco de la videbla brileto sur ensorĉitaj objektoj.", "options.graphics": "Graf<PERSON>", "options.graphics.fabulous": "Fablatra!", "options.graphics.fabulous.tooltip": "%s grafiki uzas skren-shaderi por charjar vetero, nubi e partikuli dope bloki transparanta ed aquo.\nIco forsan severe efektar rendimento en aparati portebla e 4k-skreni.", "options.graphics.fancy": "Detaloza", "options.graphics.fancy.tooltip": "Grafiki detaloza equilibras rendimento e qualeso por la majoritato di mashini. Vetero, nubi, e partikuli forsan ne aparas dope bloki transparanta od aquo.", "options.graphics.fast": "Rapida", "options.graphics.fast.tooltip": "Grafiki rapida reduktas la denseso di pluvo e nivo videbla. Efekti transparentala esas invalida por multa bloki tala folii.", "options.graphics.warning.accept": "Daŭrigi sen subteno", "options.graphics.warning.cancel": "Rekondu<PERSON> min", "options.graphics.warning.message": "Via grafika aparato estas detekita kiel nesupportata por la grafika opcio %s.\n\nVi povas preteratenti tion kaj daŭrigi, tamen subteno ne estos provizita por via aparato se vi elektos uzi la grafikon %s.", "options.graphics.warning.renderer": "Detektitas rendumilo: [%s]", "options.graphics.warning.title": "La grafika aparato ne estas subtenata", "options.graphics.warning.vendor": "Vendidulo detektita: [%s]", "options.graphics.warning.version": "Detektita OpenGL-versio: [%s]", "options.guiScale": "Skalo di GUI", "options.guiScale.auto": "Automata", "options.hidden": "Nevidebla", "options.hideLightningFlashes": "Kaŝi radion ĉe fulmo", "options.hideLightningFlashes.tooltip": "Malhelpas la lumon de la ĉielo dum fulmotondro. La fulmoj mem restos videblaj.", "options.hideMatchedNames": "Kaŝi kong<PERSON><PERSON>n nomojn", "options.hideMatchedNames.tooltip": "Serviloj de triaj programistoj povas sendi babilajn mesaĝojn en ne-normalaj formoj. Kun tiu ĉi opcio, kaŝitaj ludantoj estos rilatigitaj laŭ la nomoj de la sendantoj de la babilo.", "options.hideSplashTexts": "Kaŝi teksto-jet<PERSON>jn", "options.hideSplashTexts.tooltip": "Kaŝas la flavan tekston en la ĉefmenuo.", "options.inactivityFpsLimit": "Reduce FPS when", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Limits framerate to 30 when the game is not getting any player input for more than a minute. Further limits it to 10 after 9 more minutes.", "options.inactivityFpsLimit.minimized": "Minimized", "options.inactivityFpsLimit.minimized.tooltip": "Limits framerate only when the game window is minimized.", "options.invertMouse": "Inversigar Mauso", "options.japaneseGlyphVariants": "Japanese Glyph Variants", "options.japaneseGlyphVariants.tooltip": "Uses Japanese variants of CJK characters in the default font.", "options.key.hold": "Detenar", "options.key.toggle": "Baskuligar", "options.language": "Linguo...", "options.language.title": "Lingvo", "options.languageAccuracyWarning": "(Tradukitaj lingvoj povas ne kongrui je 100%)", "options.languageWarning": "<PERSON><PERSON><PERSON> tradukuri forsan ne esas 100%% korekta", "options.mainHand": "<PERSON><PERSON> precipua", "options.mainHand.left": "Sinistra", "options.mainHand.right": "Dextra", "options.mipmapLevels": "Niveli di mipmapo", "options.modelPart.cape": "Mantelo", "options.modelPart.hat": "Chapelo", "options.modelPart.jacket": "<PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.modelPart.left_sleeve": "<PERSON><PERSON> sin<PERSON>", "options.modelPart.right_pants_leg": "<PERSON><PERSON><PERSON><PERSON><PERSON> dextra", "options.modelPart.right_sleeve": "<PERSON><PERSON> dextra", "options.mouseWheelSensitivity": "Sentemeto di skrolanto", "options.mouse_settings": "Ajusti di mauso...", "options.mouse_settings.title": "Ajusti di mauso", "options.multiplayer.title": "Ajusti di plura ludanti...", "options.multiplier": "%sx", "options.music_frequency": "Music Frequency", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Frequent", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "Naracanto", "options.narrator.all": "Naracas omno", "options.narrator.chat": "<PERSON><PERSON><PERSON> k<PERSON>", "options.narrator.notavailable": "Nedisponebla", "options.narrator.off": "NO", "options.narrator.system": "<PERSON><PERSON><PERSON> siste<PERSON>", "options.notifications.display_time": "Sciigoj", "options.notifications.display_time.tooltip": "Influas la daŭron de la tempa intervalo, dum kiu ĉiuj sciigoj restas videblaj sur la ekrano.", "options.off": "No", "options.off.composed": "%s: No", "options.on": "Yes", "options.on.composed": "%s: Yes", "options.online": "Enlinea...", "options.online.title": "Selekti di enlinea", "options.onlyShowSecureChat": "<PERSON><PERSON> nur sekuran babiladon", "options.onlyShowSecureChat.tooltip": "Montri nur mesaĝojn de aliaj ludantoj, kiuj povas esti konfirmitaj kiel senditaj de tiu ludanto kaj ne esti ŝanĝitaj.", "options.operatorItemsTab": "Tabulon de operacilingredoj", "options.particles": "Partikuli", "options.particles.all": "Ĉio", "options.particles.decreased": "Diminuti<PERSON>", "options.particles.minimal": "Minimala", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer": "Duon<PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "<PERSON><PERSON><PERSON> agoj en peco tuj rekompilos ĝin. Tio inkluzivas la enmeton kaj detruon de blokoj.", "options.prioritizeChunkUpdates.nearby": "<PERSON><PERSON>", "options.prioritizeChunkUpdates.nearby.tooltip": "Proksimaj pecoj ĉiam estas tuj rekompilitaj. Tio povas influi pri la ludeldonado, kiam blokoj estas metitaj aŭ detruitaj.", "options.prioritizeChunkUpdates.none": "Filadanta", "options.prioritizeChunkUpdates.none.tooltip": "Proksimaj pecoj estas kompilitaj en paralelaj fadenoj. Tio povas kaŭzi mallongajn videblajn truojn, kiam blokoj estas detruitaj.", "options.rawMouseInput": "<PERSON><PERSON>", "options.realmsNotifications": "Novaĵoj pri Realaĵoj kaj <PERSON>j", "options.realmsNotifications.tooltip": "Fetches Realms news and invites in the title screen and displays their respective icon on the Realms button.", "options.reducedDebugInfo": "Informeso di F3 reduktata", "options.renderClouds": "Nubi", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "Disto di charjo", "options.resourcepack": "Havajari...", "options.rotateWithMinecart": "Rotate with Minecarts", "options.rotateWithMinecart.tooltip": "Whether the player's view should rotate with a turning Minecart. Only available in worlds with the 'Minecart Improvements' experimental setting turned on.", "options.screenEffectScale": "Efekti distordala", "options.screenEffectScale.tooltip": "Fortseo di nauzeo ed efekti distordala en portalo di Nether. An valori mikra, le efekto di nauzeo esas substitucata per skrentinto verda.", "options.sensitivity": "Sentemeso", "options.sensitivity.max": "MAXIMA RAPIDESO!!!", "options.sensitivity.min": "*ocito*", "options.showNowPlayingToast": "Show Music Toast", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "Montrar subtituli", "options.simulationDistance": "Disto di simulo", "options.skinCustomisation": "Personalizo di aspekto...", "options.skinCustomisation.title": "Personalizo di aspekto", "options.sounds": "Muziko & soni...", "options.sounds.title": "Ajusti di muziko & sono", "options.telemetry": "Telemetria datumoj...", "options.telemetry.button": "Datokolekto", "options.telemetry.button.tooltip": "\"%s\" enhavas nur la gravajn datumojn.\n\"%s\" enhavas kaj la gravajn kaj la aldonaĵajn datumojn.", "options.telemetry.disabled": "<PERSON><PERSON><PERSON><PERSON> is disabled.", "options.telemetry.state.all": "Ĉio", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON>", "options.telemetry.state.none": "<PERSON><PERSON><PERSON>", "options.title": "<PERSON><PERSON><PERSON><PERSON>", "options.touchscreen": "<PERSON>do di tushaskreno", "options.video": "Selekti grafikala...", "options.videoTitle": "Selekti grafikala", "options.viewBobbing": "<PERSON><PERSON><PERSON>", "options.visible": "Videbla", "options.vsync": "VSync", "outOfMemory.message": "Minecraft has run out of memory.\n\nThis could be caused by a bug in the game or by the Java Virtual Machine not being allocated enough memory.\n\nTo prevent world corruption, the current game has quit. We've tried to free up enough memory to let you go back to the main menu and back to playing, but this may not have worked.\n\nPlease restart the game if you see this message again.", "outOfMemory.title": "Sen memoro!", "pack.available.title": "Disponebla", "pack.copyFailure": "Malsukcesis kopii pakaĵojn", "pack.dropConfirm": "Do you want to add the following packs to Minecraft?", "pack.dropInfo": "Drag and drop files into this window to add packs", "pack.dropRejected.message": "The following entries were not valid packs and were not copied:\n %s", "pack.dropRejected.title": "Non-empakiti<PERSON> eniroj", "pack.folderInfo": "(<PERSON><PERSON> dos<PERSON> de pako ĉi tie)", "pack.incompatible": "Nekonciliebla", "pack.incompatible.confirm.new": "This pack was made for a newer version of Minecraft and may not work correctly.", "pack.incompatible.confirm.old": "This pack was made for an older version of Minecraft and may no longer work correctly.", "pack.incompatible.confirm.title": "Vi certas, ĉu vi volas ŝarĝi ĉi tiun pakaĵon?", "pack.incompatible.new": "(Faces<PERSON> por versiono plu nova di Minecraft)", "pack.incompatible.old": "(Facesis por versiono plu anciena di Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON><PERSON> dosierujon kun pakaĵo", "pack.selected.title": "Sele<PERSON><PERSON>", "pack.source.builtin": "enkonstruita", "pack.source.feature": "funkcio", "pack.source.local": "loka", "pack.source.server": "servilo", "pack.source.world": "mondo", "painting.dimensions": "%s per %s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albana", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON>lo sukcese bombardita", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Busto", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bonĵoro <PERSON><PERSON><PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON>", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Lasta cefo", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Batalantoj", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Modesteco", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Partio", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Porkasceno", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisarbo", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "La Pisko", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortala Ŝpelo", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "<PERSON><PERSON><PERSON> ka<PERSON>", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "La sceneo estas pretigita", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "densa_sun<PERSON><PERSON>o", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "La senĉes", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Desertlando", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Akvo", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Vento", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Hazardelekta varianto", "parsing.bool.expected": "Expektis booleano", "parsing.bool.invalid": "<PERSON><PERSON><PERSON> nevalida, expektis 'true' o 'false' ma trovis '%s'", "parsing.double.expected": "Expektis duoplo", "parsing.double.invalid": "Duoplo nevalida '%s'", "parsing.expected": "Expektis '%s'", "parsing.float.expected": "Expektis flotacanto", "parsing.float.invalid": "Flotacanto nevalida '%s'", "parsing.int.expected": "Expektis integro", "parsing.int.invalid": "Integro nevalida '%s'", "parsing.long.expected": "Expektis long<PERSON>", "parsing.long.invalid": "Longnombro nevalida '%s'", "parsing.quote.escape": "Sequo di eskapo nevalida '/%s' en kateno kotita", "parsing.quote.expected.end": "<PERSON><PERSON><PERSON> kateno neklo<PERSON>ta", "parsing.quote.expected.start": "Expektis koto a starter kateno", "particle.invalidOptions": "Can't parse particle options: %s", "particle.notFound": "Partikulo nekonocata: %s", "permissions.requires.entity": "Ento esas necesa por facar ica komando hike", "permissions.requires.player": "Ludanto esas necesa por facar ica komando hike", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Dum aplikita:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predikato nekonocata: %s", "quickplay.error.invalid_identifier": "Could not find world with the provided identifier", "quickplay.error.realm_connect": "Mankis konekto al Realm", "quickplay.error.realm_permission": "Lacking permission to connect to this Realm", "quickplay.error.title": "Malsukcesis rapide ludi", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Western Japan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "United Arab Emirates (UAE)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "Realms prezente ne suportesas en proboversioni", "recipe.notFound": "Recepto nekonocata: %s", "recipe.toast.description": "Spektez vua libro di recepti", "recipe.toast.title": "Nova recepti!", "record.nowPlaying": "<PERSON>un ludas: %s", "recover_world.bug_tracker": "Raporti pri eraro", "recover_world.button": "Provo por restaŭri", "recover_world.done.failed": "Failed to recover from previous state.", "recover_world.done.success": "La restaŭro sukcesis!", "recover_world.done.title": "Restaŭro finita", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON>", "recover_world.issue.none": "<PERSON><PERSON><PERSON> problemo", "recover_world.message": "The following issues occurred while trying to read world folder \"%s\".\nIt might be possible to restore the world from an older state or you can report this issue on the bug tracker.", "recover_world.no_fallback": "No state to recover from available", "recover_world.restore": "Tentativo por restaŭri", "recover_world.restoring": "Attempting to restore world...", "recover_world.state_entry": "Stato pri %s: ", "recover_world.state_entry.unknown": "nekon<PERSON>", "recover_world.title": "Malsukcesis ŝargi la mondon", "recover_world.warning": "Failed to load world summary", "resourcePack.broken_assets": "HAVAJI DOMAJITA DETEKTESAS", "resourcePack.high_contrast.name": "Alta Klaro", "resourcePack.load_fail": "Richarjo di havajaro faliis", "resourcePack.programmer_art.name": "Programisto Arto", "resourcePack.runtime_failure": "Resource pack error detected", "resourcePack.server.name": "<PERSON><PERSON><PERSON> specifika a mondo", "resourcePack.title": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>", "resourcePack.vanilla.description": "The default look and feel of Minecraft", "resourcePack.vanilla.name": "Defaŭlta", "resourcepack.downloading": "Des<PERSON><PERSON> ha<PERSON>o", "resourcepack.progress": "Deskargas arkivo (%s MB)...", "resourcepack.requesting": "Postulo en progreso...", "screenshot.failure": "Ne povis konservar skrenkapto: %s", "screenshot.success": "Konservis skrenkapto kam %s", "selectServer.add": "<PERSON><PERSON><PERSON>o", "selectServer.defaultName": "Servero di Minecraft", "selectServer.delete": "<PERSON><PERSON>", "selectServer.deleteButton": "<PERSON><PERSON>", "selectServer.deleteQuestion": "Ka vu certe volas efacar ica servero?", "selectServer.deleteWarning": "'%s' will be lost forever! (A long time!)", "selectServer.direct": "<PERSON><PERSON> k<PERSON>", "selectServer.edit": "<PERSON><PERSON><PERSON>", "selectServer.hiddenAddress": "(<PERSON><PERSON><PERSON>)", "selectServer.refresh": "Rifreshigar", "selectServer.select": "<PERSON><PERSON><PERSON>o", "selectWorld.access_failure": "F<PERSON>is ace<PERSON> mondo", "selectWorld.allowCommands": "<PERSON><PERSON><PERSON> k<PERSON>", "selectWorld.allowCommands.info": "<PERSON><PERSON><PERSON> kam /gamemode, /experience", "selectWorld.allowCommands.new": "Allow Commands", "selectWorld.backupEraseCache": "<PERSON><PERSON>car donaji en <PERSON>o", "selectWorld.backupJoinConfirmButton": "<PERSON><PERSON><PERSON> sekura kopiuro e charjar", "selectWorld.backupJoinSkipButton": "Me savas quon me facas!", "selectWorld.backupQuestion.customized": "Mondi personalizita ne plu suportesas", "selectWorld.backupQuestion.downgrade": "Downgrading a world is not supported", "selectWorld.backupQuestion.experimental": "<PERSON>di qui uzas ajusti experimentala ne suportesas", "selectWorld.backupQuestion.snapshot": "Ka vu certe volas charjar ica mondo?", "selectWorld.backupWarning.customized": "<PERSON><PERSON><PERSON><PERSON>, ni ne suportas mondi personalizita en ica versiono di Minecraft. Ni povas ankore charjar ica mondo e retenar omno tam esis, ma ula nove genitita tereno ne esos plu personalizita. Ni regretas la nekomodeso!", "selectWorld.backupWarning.downgrade": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work. If you still want to continue, please make a backup.", "selectWorld.backupWarning.experimental": "<PERSON>ca mondo uzas ajusti experimentala qui povos nefunctionar irgatempe. Ni ne povas garantiar ke ol charjos or functionos. Atencez!", "selectWorld.backupWarning.snapshot": "Ta mondo maxim recente ludesis per la versiono %s; vu uzas la versiono %s. <PERSON><PERSON>ez facar sekura kopiuro kaze ke mondo-korupto eventus!", "selectWorld.bonusItems": "Kofro komplementa", "selectWorld.cheats": "<PERSON><PERSON><PERSON>", "selectWorld.commands": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.conversion": "Mustas konvertesas!", "selectWorld.conversion.tooltip": "This world must be opened in an older version (like 1.6.4) to be safely converted", "selectWorld.create": "<PERSON><PERSON>r nova mondo", "selectWorld.customizeType": "Personalizar", "selectWorld.dataPacks": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.data_read": "Lektas datumi di mondo...", "selectWorld.delete": "Efacar", "selectWorld.deleteButton": "Efacar", "selectWorld.deleteQuestion": "Ka vu certe volas efacar ica mondo?", "selectWorld.deleteWarning": "'%s' esos perdata por sempre! (Longa tempo!)", "selectWorld.delete_failure": "<PERSON><PERSON><PERSON> e<PERSON> mondo", "selectWorld.edit": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.backup": "<PERSON><PERSON><PERSON> k<PERSON>", "selectWorld.edit.backupCreated": "Kopiis: %s", "selectWorld.edit.backupFailed": "<PERSON><PERSON> k<PERSON> faliis", "selectWorld.edit.backupFolder": "Apertar direktorio di kopiuri", "selectWorld.edit.backupSize": "grandeso: %s MB", "selectWorld.edit.export_worldgen_settings": "Exporta<PERSON>z <PERSON>stonoj ek Mondo Konstruktanta", "selectWorld.edit.export_worldgen_settings.failure": "Exportaco faliis", "selectWorld.edit.export_worldgen_settings.success": "<PERSON><PERSON><PERSON>", "selectWorld.edit.openFolder": "Apertar direktorio di mondo", "selectWorld.edit.optimize": "Maxbonigar mondo", "selectWorld.edit.resetIcon": "<PERSON>n<PERSON><PERSON>", "selectWorld.edit.save": "Konservar", "selectWorld.edit.title": "<PERSON><PERSON><PERSON><PERSON>do", "selectWorld.enterName": "Nomo di mondo", "selectWorld.enterSeed": "Semino por la genitilo di mondo", "selectWorld.experimental": "Experimentala", "selectWorld.experimental.details": "<PERSON><PERSON><PERSON>", "selectWorld.experimental.details.entry": "Required experimental features: %s", "selectWorld.experimental.details.title": "Experimental Feature Requirements", "selectWorld.experimental.message": "Be careful!\nThis configuration requires features that are still under development. Your world might crash, break, or not work with future updates.", "selectWorld.experimental.title": "Avertez: <PERSON><PERSON><PERSON>", "selectWorld.experiments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.experiments.info": "Experiments are potential new features. Be careful as things might break. Experiments can't be turned off after world creation.", "selectWorld.futureworld.error.text": "<PERSON><PERSON> faliis dum ke esforcas charjar mondo de futura versiono. Ico esis riskoza procedo origine; ni regretas ke ol ne funcionis.", "selectWorld.futureworld.error.title": "Eroro eventis!", "selectWorld.gameMode": "Ludomodo", "selectWorld.gameMode.adventure": "Aventurala", "selectWorld.gameMode.adventure.info": "Same as Survival Mode, but blocks can't be added or removed.", "selectWorld.gameMode.adventure.line1": "Identa a Modo transvivala, ma bloki ne povas", "selectWorld.gameMode.adventure.line2": "adjuntesar od e<PERSON>sar", "selectWorld.gameMode.creative": "Kreiva", "selectWorld.gameMode.creative.info": "Create, build, and explore without limits. You can fly, have endless materials, and can't be hurt by monsters.", "selectWorld.gameMode.creative.line1": "Moyeni <PERSON>, flugo libra e", "selectWorld.gameMode.creative.line2": "destruktar bloki cainstante", "selectWorld.gameMode.hardcore": "Extrema", "selectWorld.gameMode.hardcore.info": "Survival Mode locked to 'Hard' difficulty. You can't respawn if you die.", "selectWorld.gameMode.hardcore.line1": "Identa a Modo transvivala, klefagita an maxim ardua", "selectWorld.gameMode.hardcore.line2": "desfacileso, e nur un vivo", "selectWorld.gameMode.spectator": "Spektanto", "selectWorld.gameMode.spectator.info": "You can look but don't touch.", "selectWorld.gameMode.spectator.line1": "<PERSON>u darfas vidar, ma ne tushar", "selectWorld.gameMode.survival": "Transvivo", "selectWorld.gameMode.survival.info": "Explore a mysterious world where you build, collect, craft, and fight monsters.", "selectWorld.gameMode.survival.line1": "Ser<PERSON>z por moyeni, fabrik<PERSON>, ganez", "selectWorld.gameMode.survival.line2": "niveli, saneso e hungro", "selectWorld.gameRules": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "selectWorld.import_worldgen_settings.failure": "<PERSON><PERSON><PERSON> dum importas ajusti", "selectWorld.import_worldgen_settings.select_file": "<PERSON><PERSON><PERSON><PERSON> failo di ajusti (.json)", "selectWorld.incompatible.description": "This world cannot be opened in this version.\nIt was last played in version %s.", "selectWorld.incompatible.info": "Incompatible version: %s", "selectWorld.incompatible.title": "Incompatible version", "selectWorld.incompatible.tooltip": "This world cannot be opened because it was created by an incompatible version.", "selectWorld.incompatible_series": "Created by an incompatible version", "selectWorld.load_folder_access": "Ne povas lektar od acesar direktorio di konservi di mondi!", "selectWorld.loading_list": "<PERSON>rjas listo di mondi", "selectWorld.locked": "Blokusesis per altra instanco di Minecraft", "selectWorld.mapFeatures": "<PERSON><PERSON><PERSON> s<PERSON>", "selectWorld.mapFeatures.info": "Villages, Shipwrecks, etc.", "selectWorld.mapType": "Tipo di mondo", "selectWorld.mapType.normal": "Normala", "selectWorld.moreWorldOptions": "Plusa selekti di mondo...", "selectWorld.newWorld": "Nova mondo", "selectWorld.recreate": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.recreate.customized.text": "Mondi personalizita n'e plu suportesas en ica versiono di Minecraft. Ne povas esforcar a rikrear ol kun la sama semino e propraji, ma ula personalizi di terano perdesos. Ni regretas la nekomodeso!", "selectWorld.recreate.customized.title": "Customized worlds are no longer supported", "selectWorld.recreate.error.text": "<PERSON><PERSON> faliis dum ke esforcas rikrear mondo.", "selectWorld.recreate.error.title": "An error occurred!", "selectWorld.resource_load": "Preparing Resources...", "selectWorld.resultFolder": "Konservesos en:", "selectWorld.search": "serchar mondi", "selectWorld.seedInfo": "Lasar blanka por semino hazarda", "selectWorld.select": "<PERSON><PERSON> mondo selektata", "selectWorld.targetFolder": "Ko<PERSON><PERSON>vi dosieru<PERSON>: %s", "selectWorld.title": "<PERSON><PERSON><PERSON><PERSON> mondo", "selectWorld.tooltip.fromNewerVersion1": "<PERSON>do konserve<PERSON> en plu nova versiono,", "selectWorld.tooltip.fromNewerVersion2": "charjar ica mondo forsan igar problemi!", "selectWorld.tooltip.snapshot1": "Ne obliviez facar kopiuro sekura di ica mondo", "selectWorld.tooltip.snapshot2": "ante vu charjas ol en ica proboversiono.", "selectWorld.unable_to_load": "<PERSON>e povas charjar mondi", "selectWorld.version": "Versiono:", "selectWorld.versionJoinButton": "<PERSON><PERSON><PERSON>", "selectWorld.versionQuestion": "Ka vu certe volas charjar ica mondo?", "selectWorld.versionUnknown": "nekonocata", "selectWorld.versionWarning": "Ica mondo laste ludesis en versiono %s e charjar ol en ica versiono forsan koruptigar ol!", "selectWorld.warning.deprecated.question": "Some features used are deprecated and will stop working in the future. Do you wish to proceed?", "selectWorld.warning.deprecated.title": "Warning! These settings are using deprecated features", "selectWorld.warning.experimental.question": "Ica ajusti esas experimentala e povas malfuncionar future. Ka vu volas procedar?", "selectWorld.warning.experimental.title": "Avertez! Ica ajusti uzas traiti experimentala", "selectWorld.warning.lowDiskSpace.description": "There is not much space left on your device.\nRunning out of disk space while in game can lead to your world being damaged.", "selectWorld.warning.lowDiskSpace.title": "Warning! Low disk space!", "selectWorld.world": "<PERSON><PERSON>", "sign.edit": "Edit Sign Message", "sleep.not_possible": "No amount of rest can pass this night", "sleep.players_sleeping": "%s/%s players sleeping", "sleep.skipping_night": "Sleeping through this night", "slot.only_single_allowed": "Only single slots allowed, got '%s'", "slot.unknown": "<PERSON><PERSON><PERSON> nekonocata '%s'", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "Medio", "soundCategory.block": "Bloki", "soundCategory.hostile": "<PERSON><PERSON><PERSON>", "soundCategory.master": "Sonforteso precipua", "soundCategory.music": "<PERSON><PERSON><PERSON>", "soundCategory.neutral": "<PERSON><PERSON><PERSON>", "soundCategory.player": "<PERSON><PERSON><PERSON>", "soundCategory.record": "Bloki muzikala", "soundCategory.ui": "UI", "soundCategory.voice": "Voco/parolo", "soundCategory.weather": "Vetero", "spectatorMenu.close": "<PERSON><PERSON><PERSON>", "spectatorMenu.next_page": "Sekva Paĝo", "spectatorMenu.previous_page": "Antaŭa Paĝo", "spectatorMenu.root.prompt": "Presez klavo por selektar komando, ed itere por uzar ol.", "spectatorMenu.team_teleport": "Teleportar ad esquadano", "spectatorMenu.team_teleport.prompt": "Selektez esquado por teleportar ad ol", "spectatorMenu.teleport": "Teleportar a ludanto", "spectatorMenu.teleport.prompt": "Selektez ludanto por teleportar a lu", "stat.generalButton": "Generala", "stat.itemsButton": "Objekti", "stat.minecraft.animals_bred": "<PERSON><PERSON> edukesis", "stat.minecraft.aviate_one_cm": "Disto per elitri", "stat.minecraft.bell_ring": "<PERSON><PERSON><PERSON>", "stat.minecraft.boat_one_cm": "Disto en batelo", "stat.minecraft.clean_armor": "Kuracopeci purigesis", "stat.minecraft.clean_banner": "<PERSON><PERSON>", "stat.minecraft.clean_shulker_box": "Shulkerbuxi purigesis", "stat.minecraft.climb_one_cm": "Disto klimesis", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON> blotisas", "stat.minecraft.damage_absorbed": "Domajo absorbesis", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON><PERSON> blo<PERSON> per shildo", "stat.minecraft.damage_dealt": "<PERSON>aj<PERSON> impozesis", "stat.minecraft.damage_dealt_absorbed": "Domajo impozesis (absorbesis)", "stat.minecraft.damage_dealt_resisted": "Domajo impozesis (rezistesis)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_taken": "<PERSON><PERSON><PERSON>", "stat.minecraft.deaths": "Nombro di morti", "stat.minecraft.drop": "Objekti faligesis", "stat.minecraft.eat_cake_slice": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.enchant_item": "Objekti sorcesis", "stat.minecraft.fall_one_cm": "Disto falesis", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON><PERSON> plenigesis", "stat.minecraft.fish_caught": "<PERSON><PERSON> ka<PERSON>", "stat.minecraft.fly_one_cm": "<PERSON><PERSON> fulgesis", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "Disto per kavalo", "stat.minecraft.inspect_dispenser": "<PERSON><PERSON><PERSON> furet<PERSON>is", "stat.minecraft.inspect_dropper": "Distributili furetumesis", "stat.minecraft.inspect_hopper": "<PERSON><PERSON><PERSON> furet<PERSON>is", "stat.minecraft.interact_with_anvil": "Interakti kun amboso", "stat.minecraft.interact_with_beacon": "Interakti kun balisi", "stat.minecraft.interact_with_blast_furnace": "Interakti kun fornego", "stat.minecraft.interact_with_brewingstand": "Interakti kun brasadostativi", "stat.minecraft.interact_with_campfire": "Interakti kun kamp<PERSON>o", "stat.minecraft.interact_with_cartography_table": "Interakti kun tablo di kartografio", "stat.minecraft.interact_with_crafting_table": "Interakti kun tablo di fabrikado", "stat.minecraft.interact_with_furnace": "Interakti kun furnazo", "stat.minecraft.interact_with_grindstone": "Interakti kun grindstono", "stat.minecraft.interact_with_lectern": "Interakti kun libropupitro", "stat.minecraft.interact_with_loom": "Interakti kun texilo", "stat.minecraft.interact_with_smithing_table": "Interakti kun tablo di forjisto", "stat.minecraft.interact_with_smoker": "Interakti kun fumizilo", "stat.minecraft.interact_with_stonecutter": "Interakti kun petroaliilo", "stat.minecraft.jump": "<PERSON><PERSON>", "stat.minecraft.leave_game": "<PERSON><PERSON>", "stat.minecraft.minecart_one_cm": "Disto en minvagono", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON> mortigesis", "stat.minecraft.open_barrel": "<PERSON><PERSON>", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON>", "stat.minecraft.open_enderchest": "Enderkofri a<PERSON>esis", "stat.minecraft.open_shulker_box": "Shulkerbuxi apertesis", "stat.minecraft.pig_one_cm": "Disto per porko", "stat.minecraft.play_noteblock": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.play_record": "Muzik<PERSON><PERSON> p<PERSON>", "stat.minecraft.play_time": "Tempo ludesis", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON> mortigesis", "stat.minecraft.pot_flower": "Planti pozesis aden poti", "stat.minecraft.raid_trigger": "Invadi efektigesis", "stat.minecraft.raid_win": "Invadi vinkesis", "stat.minecraft.sleep_in_bed": "Nokti dormesis en lito", "stat.minecraft.sneak_time": "Tempo reptesis", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON> k<PERSON>", "stat.minecraft.strider_one_cm": "<PERSON><PERSON><PERSON> per <PERSON>rider", "stat.minecraft.swim_one_cm": "Disto natesis", "stat.minecraft.talked_to_villager": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.target_hit": "Pafskopi frapita", "stat.minecraft.time_since_death": "Tempo depos antea morto", "stat.minecraft.time_since_rest": "Tempo depos antea repozo", "stat.minecraft.total_world_time": "Time with World Open", "stat.minecraft.traded_with_villager": "Komerci kun vilajani", "stat.minecraft.trigger_trapped_chest": "<PERSON><PERSON><PERSON> insidiita <PERSON>", "stat.minecraft.tune_noteblock": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.use_cauldron": "Aquo prenesis de <PERSON>i", "stat.minecraft.walk_on_water_one_cm": "<PERSON><PERSON> march<PERSON> surmare", "stat.minecraft.walk_one_cm": "<PERSON><PERSON> march<PERSON>", "stat.minecraft.walk_under_water_one_cm": "Disto marchesis submare", "stat.mobsButton": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "Quanto ruptesis", "stat_type.minecraft.crafted": "Quanto fabrikesis", "stat_type.minecraft.dropped": "Quanto faligis", "stat_type.minecraft.killed": "Vu mortigis %s %s", "stat_type.minecraft.killed.none": "Vu nultempe mortigis %s", "stat_type.minecraft.killed_by": "%s ocidis vu %s foyo/i", "stat_type.minecraft.killed_by.none": "Vu nultempe mortigesis per %s", "stat_type.minecraft.mined": "Quanto minesis", "stat_type.minecraft.picked_up": "Quant<PERSON> k<PERSON>", "stat_type.minecraft.used": "Nombro di uzi", "stats.none": "-", "structure_block.button.detect_size": "DETEKTAR", "structure_block.button.load": "CHARJAR", "structure_block.button.save": "KONSERVAR", "structure_block.custom_data": "Nomo di donajitago personaligita", "structure_block.detect_size": "Detect Structure Size and Position:", "structure_block.hover.corner": "Angulo: %s", "structure_block.hover.data": "Donaji: %s", "structure_block.hover.load": "Charjar: %s", "structure_block.hover.save": "Konservar: %s", "structure_block.include_entities": "Include Entities:", "structure_block.integrity": "Integreso e semino di strukturo", "structure_block.integrity.integrity": "Integreso di strukturo", "structure_block.integrity.seed": "Semino di strukturo", "structure_block.invalid_structure_name": "Nomo di strukturo '%s' esas nevalida", "structure_block.load_not_found": "Strukturo '%s' esas nedisponebla", "structure_block.load_prepare": "Posteno di strukturo '%s' preparesis", "structure_block.load_success": "<PERSON>ruk<PERSON><PERSON> char<PERSON> de '%s'", "structure_block.mode.corner": "<PERSON><PERSON>", "structure_block.mode.data": "<PERSON><PERSON><PERSON>", "structure_block.mode.load": "<PERSON><PERSON><PERSON>", "structure_block.mode.save": "<PERSON><PERSON><PERSON><PERSON>", "structure_block.mode_info.corner": "Corner Mode - Placement and size marker", "structure_block.mode_info.data": "Datumpacki - Indikilo di ludlogiko", "structure_block.mode_info.load": "Load Mode - Load from file", "structure_block.mode_info.save": "Save Mode - Write to file", "structure_block.position": "Posteno relativa", "structure_block.position.x": "posteno relativa x", "structure_block.position.y": "posteno relativa y", "structure_block.position.z": "posteno relativa z", "structure_block.save_failure": "<PERSON>e povas konservar strukturo '%s'", "structure_block.save_success": "Strukturo konservesis kom '%s'", "structure_block.show_air": "<PERSON><PERSON> erojn:", "structure_block.show_boundingbox": "<PERSON><PERSON> l<PERSON>:", "structure_block.size": "Grandeso di strukturo", "structure_block.size.x": "grandeso di strukturo x", "structure_block.size.y": "grandeso di strukturo y", "structure_block.size.z": "grandeso di strukturo z", "structure_block.size_failure": "Ne povas detektar grandeso di strukturo. Adjuntez anguli kun nomi di strukuro k<PERSON>pondanta", "structure_block.size_success": "Grandeso detektesis sucesante por '%s'", "structure_block.strict": "Strict Placement:", "structure_block.structure_name": "Nomo di strukturo", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON><PERSON> pavori<PERSON>a", "subtitles.ambient.sound": "Eerie noise", "subtitles.block.amethyst_block.chime": "<PERSON><PERSON><PERSON>", "subtitles.block.amethyst_block.resonate": "Ametisto resonacas", "subtitles.block.anvil.destroy": "<PERSON><PERSON><PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON><PERSON>", "subtitles.block.anvil.use": "<PERSON><PERSON><PERSON>", "subtitles.block.barrel.close": "<PERSON><PERSON>", "subtitles.block.barrel.open": "<PERSON><PERSON>", "subtitles.block.beacon.activate": "La bekon aktivigas", "subtitles.block.beacon.ambient": "La bekon murmuradas", "subtitles.block.beacon.deactivate": "La bekon malkativigas", "subtitles.block.beacon.power_select": "Elektita povo de la beko", "subtitles.block.beehive.drip": "<PERSON><PERSON>", "subtitles.block.beehive.enter": "<PERSON><PERSON> abeluyo", "subtitles.block.beehive.exit": "Abelo livas abeluyo", "subtitles.block.beehive.shear": "Cizego skrapas", "subtitles.block.beehive.work": "<PERSON><PERSON> laboras", "subtitles.block.bell.resonate": "<PERSON><PERSON><PERSON> resonas", "subtitles.block.bell.use": "<PERSON>los<PERSON> son<PERSON>", "subtitles.block.big_dripleaf.tilt_down": "<PERSON><PERSON> folieta kliniĝas malsupren", "subtitles.block.big_dripleaf.tilt_up": "<PERSON>uto folieta kliniĝas supren", "subtitles.block.blastfurnace.fire_crackle": "Fornego bruisas", "subtitles.block.brewing_stand.brew": "Buleti de brasadostativo", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON> fluas", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON> hastas", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON>uleti vorticas", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON>", "subtitles.block.button.click": "Butono kliktas", "subtitles.block.cake.add_candle": "<PERSON><PERSON>", "subtitles.block.campfire.crackle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON>", "subtitles.block.candle.extinguish": "Candle extinguishes", "subtitles.block.chest.close": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.open": "Kofro a<PERSON>", "subtitles.block.chorus_flower.death": "<PERSON><PERSON><PERSON><PERSON> velkas", "subtitles.block.chorus_flower.grow": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.comparator.click": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.composter.empty": "<PERSON><PERSON><PERSON>", "subtitles.block.composter.fill": "<PERSON><PERSON><PERSON> plenigesis", "subtitles.block.composter.ready": "Dungilo dungigas", "subtitles.block.conduit.activate": "La kanalo funkciigas", "subtitles.block.conduit.ambient": "Impulsi di Conduit", "subtitles.block.conduit.attack.target": "Ataki di Conduit", "subtitles.block.conduit.deactivate": "Conduit deactivates", "subtitles.block.copper_bulb.turn_off": "<PERSON><PERSON> lampingo malbriliĝas", "subtitles.block.copper_bulb.turn_on": "<PERSON><PERSON> lampingo brilas", "subtitles.block.copper_trapdoor.close": "Trapdoor closes", "subtitles.block.copper_trapdoor.open": "Trapdoor opens", "subtitles.block.crafter.craft": "Crafter crafts", "subtitles.block.crafter.fail": "Crafter fails crafting", "subtitles.block.creaking_heart.hurt": "Creaking Heart grumbles", "subtitles.block.creaking_heart.idle": "Eerie noise", "subtitles.block.creaking_heart.spawn": "Creaking Heart awakens", "subtitles.block.deadbush.idle": "Dry sounds", "subtitles.block.decorated_pot.insert": "Decorated Pot fills", "subtitles.block.decorated_pot.insert_fail": "Decorated Pot wobbles", "subtitles.block.decorated_pot.shatter": "Decorated Pot shatters", "subtitles.block.dispenser.dispense": "Objekto lansita", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON>", "subtitles.block.door.toggle": "<PERSON><PERSON> stri<PERSON>", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "Enchanting Table used", "subtitles.block.end_portal.spawn": "End Portal opens", "subtitles.block.end_portal_frame.fill": "Eye of <PERSON><PERSON> attaches", "subtitles.block.eyeblossom.close": "Eyeblossom closes", "subtitles.block.eyeblossom.idle": "Eyeblossom whispers", "subtitles.block.eyeblossom.open": "Eyeblossom opens", "subtitles.block.fence_gate.toggle": "Fencopordeto stridas", "subtitles.block.fire.ambient": "<PERSON><PERSON>", "subtitles.block.fire.extinguish": "Fairo extingesas", "subtitles.block.firefly_bush.idle": "Fireflies buzz", "subtitles.block.frogspawn.hatch": "Tadpole hatches", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON><PERSON>", "subtitles.block.generic.break": "<PERSON><PERSON><PERSON> frak<PERSON>", "subtitles.block.generic.fall": "Something falls on a block", "subtitles.block.generic.footsteps": "<PERSON><PERSON>", "subtitles.block.generic.hit": "Bloko frakasa<PERSON>", "subtitles.block.generic.place": "<PERSON><PERSON><PERSON> pozesas", "subtitles.block.grindstone.use": "Grindstono uzesis", "subtitles.block.growing_plant.crop": "Plant cropped", "subtitles.block.hanging_sign.waxed_interact_fail": "Sign wobbles", "subtitles.block.honey_block.slide": "<PERSON><PERSON>as adinfre bloko di mielo", "subtitles.block.iron_trapdoor.close": "Trapo klo<PERSON>", "subtitles.block.iron_trapdoor.open": "Trapo apertas", "subtitles.block.lava.ambient": "Lavabuleti explozas", "subtitles.block.lava.extinguish": "Lavao sisas", "subtitles.block.lever.click": "Levero k<PERSON>", "subtitles.block.note_block.note": "<PERSON><PERSON><PERSON><PERSON> pleas", "subtitles.block.pale_hanging_moss.idle": "Eerie noise", "subtitles.block.piston.move": "<PERSON><PERSON><PERSON> movas", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> da <PERSON>", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava drips into Cauldron", "subtitles.block.pointed_dripstone.drip_water": "<PERSON><PERSON> da akvo", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Water drips into Cauldron", "subtitles.block.pointed_dripstone.land": "Stalactite crashes down", "subtitles.block.portal.ambient": "<PERSON><PERSON>", "subtitles.block.portal.travel": "Portal noise fades", "subtitles.block.portal.trigger": "Portal noise intensifies", "subtitles.block.pressure_plate.click": "Presoplako kliktas", "subtitles.block.pumpkin.carve": "Shears carve", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON>", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON> an<PERSON>", "subtitles.block.respawn_anchor.charge": "Regenitankro esas charjata", "subtitles.block.respawn_anchor.deplete": "Ren<PERSON><PERSON> an<PERSON>ro malfortiĝas", "subtitles.block.respawn_anchor.set_spawn": "Renaski ankro starigas spawnpunkton", "subtitles.block.sand.idle": "Sandy sounds", "subtitles.block.sand.wind": "Windy sounds", "subtitles.block.sculk.charge": "Sculk bubbles", "subtitles.block.sculk.spread": "Sculk spreads", "subtitles.block.sculk_catalyst.bloom": "Sculk Catalyst blooms", "subtitles.block.sculk_sensor.clicking": "Sc<PERSON><PERSON> Sensor clicks", "subtitles.block.sculk_sensor.clicking_stop": "<PERSON><PERSON><PERSON> stops clicking", "subtitles.block.sculk_shrieker.shriek": "<PERSON><PERSON><PERSON> shrieks", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON>", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.block.sign.waxed_interact_fail": "Sign wobbles", "subtitles.block.smithing_table.use": "Forjista tablo uzita", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.sniffer_egg.crack": "Sniffer Egg cracks", "subtitles.block.sniffer_egg.hatch": "Sniffer Egg hatches", "subtitles.block.sniffer_egg.plop": "Sniffer plops", "subtitles.block.sponge.absorb": "Sponge sucks", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON>", "subtitles.block.trapdoor.close": "Trapdoor closes", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON><PERSON> stridas", "subtitles.block.trial_spawner.about_to_spawn_item": "Ominous item prepares", "subtitles.block.trial_spawner.ambient": "Trial Spawner crackles", "subtitles.block.trial_spawner.ambient_charged": "Sinistra krakado", "subtitles.block.trial_spawner.ambient_ominous": "Sinistra krakado", "subtitles.block.trial_spawner.charge_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.close_shutter": "Trial Spawner closes", "subtitles.block.trial_spawner.detect_player": "Trial Spawner charges up", "subtitles.block.trial_spawner.eject_item": "Trial Spawner ejects items", "subtitles.block.trial_spawner.ominous_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.open_shutter": "Trial Spawner opens", "subtitles.block.trial_spawner.spawn_item": "Ominous item drops", "subtitles.block.trial_spawner.spawn_item_begin": "Ominous item appears", "subtitles.block.trial_spawner.spawn_mob": "Trial Spawner spawns a mob", "subtitles.block.tripwire.attach": "<PERSON><PERSON><PERSON> at<PERSON>", "subtitles.block.tripwire.click": "<PERSON><PERSON><PERSON>", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.activate": "La magazeno ekbrulas", "subtitles.block.vault.ambient": "La magazeno krakadas", "subtitles.block.vault.close_shutter": "La magazeno klozar", "subtitles.block.vault.deactivate": "La magazeno malbruligas", "subtitles.block.vault.eject_item": "La magazeno eldonas objekton", "subtitles.block.vault.insert_item": "La magazeno malferiĝas", "subtitles.block.vault.insert_item_fail": "La magazeno rejetas objekton", "subtitles.block.vault.open_shutter": "La magazeno apertas", "subtitles.block.vault.reject_rewarded_player": "La magazeno rejetas la ludanton", "subtitles.block.water.ambient": "Aquo fluas", "subtitles.block.wet_sponge.dries": "La sponjo sekas", "subtitles.chiseled_bookshelf.insert": "La libro metita", "subtitles.chiseled_bookshelf.insert_enchanted": "Enchanted Book placed", "subtitles.chiseled_bookshelf.take": "La libro prenita", "subtitles.chiseled_bookshelf.take_enchanted": "Enchanted Book taken", "subtitles.enchant.thorns.hit": "<PERSON><PERSON><PERSON> p<PERSON>s", "subtitles.entity.allay.ambient_with_item": "Allay sercas", "subtitles.entity.allay.ambient_without_item": "<PERSON>ay malga<PERSON>as", "subtitles.entity.allay.death": "Allay mortas", "subtitles.entity.allay.hurt": "<PERSON>ay doloras", "subtitles.entity.allay.item_given": "Allay ridas", "subtitles.entity.allay.item_taken": "Allay silentas", "subtitles.entity.allay.item_thrown": "Allay jetas", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.brush": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.eat": "<PERSON><PERSON>lo mangas", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.hurt_reduced": "Armadillo shields itself", "subtitles.entity.armadillo.land": "Armdilo <PERSON>s", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.roll": "Armdilo Ruliĝas Supren", "subtitles.entity.armadillo.scute_drop": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.unroll_finish": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON>", "subtitles.entity.armor_stand.fall": "<PERSON><PERSON> f<PERSON>", "subtitles.entity.arrow.hit": "<PERSON><PERSON><PERSON> f<PERSON>as", "subtitles.entity.arrow.hit_player": "Ludanto frapas", "subtitles.entity.arrow.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.attack": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.death": "Axoloto mortas", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.idle_air": "Axoloto c<PERSON>", "subtitles.entity.axolotl.idle_water": "Axoloto c<PERSON>", "subtitles.entity.axolotl.splash": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.swim": "Axoloto natas", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.death": "<PERSON>es<PERSON><PERSON>o mortas", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.takeoff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bee.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.bee.death": "<PERSON><PERSON> mortas", "subtitles.entity.bee.hurt": "<PERSON><PERSON>", "subtitles.entity.bee.loop": "<PERSON><PERSON>", "subtitles.entity.bee.loop_aggressive": "<PERSON><PERSON> i<PERSON>e", "subtitles.entity.bee.pollinate": "<PERSON><PERSON> felice", "subtitles.entity.bee.sting": "<PERSON><PERSON>", "subtitles.entity.blaze.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.blaze.burn": "<PERSON><PERSON><PERSON>", "subtitles.entity.blaze.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.blaze.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.blaze.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_land": "Roado", "subtitles.entity.boat.paddle_water": "Roado", "subtitles.entity.bogged.ambient": "Marĉano krakas ostoj", "subtitles.entity.bogged.death": "Marĉano mortas", "subtitles.entity.bogged.hurt": "Marĉano doloras", "subtitles.entity.breeze.charge": "Brizo sargas", "subtitles.entity.breeze.death": "Brizo mortas", "subtitles.entity.breeze.deflect": "Brizo deflektas", "subtitles.entity.breeze.hurt": "Brizo doloras", "subtitles.entity.breeze.idle_air": "Brizo flugi", "subtitles.entity.breeze.idle_ground": "Brizo bruegas", "subtitles.entity.breeze.inhale": "Brizo inspiras", "subtitles.entity.breeze.jump": "Brizo supflugi", "subtitles.entity.breeze.land": "Brizo aterizas", "subtitles.entity.breeze.shoot": "Brizo pafas", "subtitles.entity.breeze.slide": "Brizo glisas", "subtitles.entity.breeze.whirl": "Brizo rotacias", "subtitles.entity.breeze.wind_burst": "Projektilo di vento eksplodas", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON>as", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON> salteto", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.camel.eat": "Ka<PERSON>o mangas", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.saddle": "<PERSON><PERSON><PERSON><PERSON>lo", "subtitles.entity.camel.sit": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.stand": "<PERSON><PERSON><PERSON> le<PERSON>", "subtitles.entity.camel.step": "<PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.camel.step_sand": "<PERSON><PERSON><PERSON> pasoj sablo", "subtitles.entity.cat.ambient": "<PERSON><PERSON>", "subtitles.entity.cat.beg_for_food": "<PERSON><PERSON> pet<PERSON>", "subtitles.entity.cat.death": "<PERSON><PERSON> mortas", "subtitles.entity.cat.eat": "<PERSON><PERSON> mangas", "subtitles.entity.cat.hiss": "<PERSON><PERSON>", "subtitles.entity.cat.hurt": "<PERSON><PERSON>", "subtitles.entity.cat.purr": "<PERSON><PERSON>", "subtitles.entity.chicken.ambient": "Hano klukas", "subtitles.entity.chicken.death": "<PERSON><PERSON> mortas", "subtitles.entity.chicken.egg": "<PERSON><PERSON>ivas", "subtitles.entity.chicken.hurt": "<PERSON><PERSON>", "subtitles.entity.cod.death": "Gado mortas", "subtitles.entity.cod.flop": "Gado saltetas", "subtitles.entity.cod.hurt": "Gado doloras", "subtitles.entity.cow.ambient": "<PERSON><PERSON> bramas", "subtitles.entity.cow.death": "<PERSON><PERSON> mortas", "subtitles.entity.cow.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.cow.milk": "<PERSON><PERSON> me<PERSON>", "subtitles.entity.creaking.activate": "Creaking watches", "subtitles.entity.creaking.ambient": "Creaking creaks", "subtitles.entity.creaking.attack": "Creaking attacks", "subtitles.entity.creaking.deactivate": "Creaking calms", "subtitles.entity.creaking.death": "Creaking crumbles", "subtitles.entity.creaking.freeze": "Creaking stops", "subtitles.entity.creaking.spawn": "Creaking manifests", "subtitles.entity.creaking.sway": "Creaking is hit", "subtitles.entity.creaking.twitch": "Creaking twitches", "subtitles.entity.creaking.unfreeze": "Creaking moves", "subtitles.entity.creeper.death": "Creepero mortas", "subtitles.entity.creeper.hurt": "Creepero vundesas", "subtitles.entity.creeper.primed": "Creepero sisas", "subtitles.entity.dolphin.ambient": "Delfino pipias", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON> si<PERSON>las", "subtitles.entity.dolphin.attack": "<PERSON>fin<PERSON> at<PERSON>", "subtitles.entity.dolphin.death": "Delfino mortas", "subtitles.entity.dolphin.eat": "Delfino manjas", "subtitles.entity.dolphin.hurt": "Delfino do<PERSON>", "subtitles.entity.dolphin.jump": "Delfino saltas", "subtitles.entity.dolphin.play": "Delfino ludas", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON> bar<PERSON>", "subtitles.entity.dolphin.swim": "Delfino natas", "subtitles.entity.donkey.ambient": "<PERSON><PERSON> bramas", "subtitles.entity.donkey.angry": "<PERSON><PERSON> bramas", "subtitles.entity.donkey.chest": "Asnokofro equipesas", "subtitles.entity.donkey.death": "<PERSON><PERSON> mortas", "subtitles.entity.donkey.eat": "<PERSON><PERSON>o mangas", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON><PERSON> salt<PERSON>", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON> mugas", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON> jetas tridento", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON> na<PERSON>", "subtitles.entity.egg.throw": "Ovo flugas", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON> gardero jemas", "subtitles.entity.elder_guardian.ambient_land": "<PERSON>a gardero frapas", "subtitles.entity.elder_guardian.curse": "<PERSON><PERSON> gardero imp<PERSON>as", "subtitles.entity.elder_guardian.death": "Seniora gardero mortas", "subtitles.entity.elder_guardian.flop": "<PERSON><PERSON> gardero saltas", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON> gardero dolora<PERSON>", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.ender_dragon.flap": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.ender_eye.death": "<PERSON><PERSON> <PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON><PERSON> flugas", "subtitles.entity.enderman.ambient": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.enderman.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON> krias", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON>", "subtitles.entity.enderman.teleport": "Enderman teleportas", "subtitles.entity.endermite.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.endermite.death": "Endermite mortas", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.ambient": "Imprik<PERSON> murmuras", "subtitles.entity.evoker.cast_spell": "Imprikero magias", "subtitles.entity.evoker.celebrate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.death": "<PERSON><PERSON><PERSON><PERSON> mortas", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.evoker.prepare_attack": "Evokero preparas atako", "subtitles.entity.evoker.prepare_summon": "Evokero preparas invokigo", "subtitles.entity.evoker.prepare_wololo": "Evokero preparas charmigo", "subtitles.entity.evoker_fangs.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.experience_orb.pickup": "Experien<PERSON>", "subtitles.entity.firework_rocket.blast": "Explozi di piroteknaji", "subtitles.entity.firework_rocket.launch": "Piroteknajo lansas", "subtitles.entity.firework_rocket.twinkle": "Pirotek<PERSON><PERSON> cintili<PERSON>", "subtitles.entity.fish.swim": "Splashes", "subtitles.entity.fishing_bobber.retrieve": "Flotajo estas elprenita", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON><PERSON><PERSON> plaudas", "subtitles.entity.fishing_bobber.throw": "Flotacajo jetesis", "subtitles.entity.fox.aggro": "Foxo i<PERSON>", "subtitles.entity.fox.ambient": "Foxo krietas", "subtitles.entity.fox.bite": "<PERSON>o mordas", "subtitles.entity.fox.death": "Foxo mortas", "subtitles.entity.fox.eat": "<PERSON><PERSON> man<PERSON>", "subtitles.entity.fox.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.fox.screech": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.fox.sleep": "<PERSON><PERSON> ron<PERSON>", "subtitles.entity.fox.sniff": "<PERSON><PERSON> sniflas", "subtitles.entity.fox.spit": "<PERSON><PERSON> sputas", "subtitles.entity.fox.teleport": "Vulpo translokigi", "subtitles.entity.frog.ambient": "<PERSON><PERSON>", "subtitles.entity.frog.death": "<PERSON><PERSON> mortas", "subtitles.entity.frog.eat": "<PERSON><PERSON> mangas", "subtitles.entity.frog.hurt": "<PERSON><PERSON>", "subtitles.entity.frog.lay_spawn": "<PERSON><PERSON> dem<PERSON>", "subtitles.entity.frog.long_jump": "<PERSON><PERSON>", "subtitles.entity.generic.big_fall": "Lu falis", "subtitles.entity.generic.burn": "Brulado", "subtitles.entity.generic.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.drink": "Drink<PERSON><PERSON>", "subtitles.entity.generic.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.explode": "Explozo", "subtitles.entity.generic.extinguish_fire": "Fairo extingesas", "subtitles.entity.generic.hurt": "<PERSON><PERSON>", "subtitles.entity.generic.small_fall": "<PERSON><PERSON> facas mispazo", "subtitles.entity.generic.splash": "Plaudado", "subtitles.entity.generic.swim": "Natado", "subtitles.entity.generic.wind_burst": "Projektilo di vento eksplodas", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "Luminanta kadro estas plenigita", "subtitles.entity.glow_item_frame.break": "Luminanta kadro estas rompita", "subtitles.entity.glow_item_frame.place": "Luminanta kadro estas lokita", "subtitles.entity.glow_item_frame.remove_item": "Luminanta kadro estas malplenigita", "subtitles.entity.glow_item_frame.rotate_item": "Klakado de luminanta kadro", "subtitles.entity.glow_squid.ambient": "<PERSON><PERSON><PERSON> kalmaro nagas", "subtitles.entity.glow_squid.death": "<PERSON><PERSON><PERSON> kalmaro mortas", "subtitles.entity.glow_squid.hurt": "<PERSON><PERSON><PERSON> kalma<PERSON> do<PERSON>", "subtitles.entity.glow_squid.squirt": "Luminanta kalmaro lansis inkon", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.goat.eat": "Kapro mangas", "subtitles.entity.goat.horn_break": "<PERSON><PERSON> korno estas rompita", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.long_jump": "Ka<PERSON><PERSON> salteto", "subtitles.entity.goat.milk": "Kapro estas miltita", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON><PERSON>asi", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.step": "<PERSON><PERSON><PERSON>o<PERSON>", "subtitles.entity.guardian.ambient": "<PERSON><PERSON>", "subtitles.entity.guardian.ambient_land": "<PERSON>ero r<PERSON>", "subtitles.entity.guardian.attack": "Guardero la<PERSON>as", "subtitles.entity.guardian.death": "G<PERSON><PERSON><PERSON> mortas", "subtitles.entity.guardian.flop": "Guardero saltas", "subtitles.entity.guardian.hurt": "Guardero <PERSON>", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> grondas", "subtitles.entity.hoglin.angry": "Ho<PERSON>no grondas iracoze", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> transformis en Zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> retretas", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.armor": "Kavalo-kuraso equipesas", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON> respira<PERSON>", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.gallop": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.saddle": "<PERSON><PERSON> equi<PERSON>", "subtitles.entity.husk.ambient": "<PERSON><PERSON> j<PERSON>", "subtitles.entity.husk.converted_to_zombie": "Husk transformis en Zombio", "subtitles.entity.husk.death": "<PERSON><PERSON> mortas", "subtitles.entity.husk.hurt": "<PERSON><PERSON> dolora<PERSON>", "subtitles.entity.illusioner.ambient": "Iluzioniganto murmuras", "subtitles.entity.illusioner.cast_spell": "Iluzioniganto sorcas", "subtitles.entity.illusioner.death": "Iluzioniganto mortas", "subtitles.entity.illusioner.hurt": "Iluzioniganto <PERSON>", "subtitles.entity.illusioner.mirror_move": "Iluzioniganto diplasas", "subtitles.entity.illusioner.prepare_blindness": "Iluzioniganto preparas blindeso", "subtitles.entity.illusioner.prepare_mirror": "Iluzioniganto preparas imajo spegula", "subtitles.entity.iron_golem.attack": "<PERSON><PERSON>o fera atakas", "subtitles.entity.iron_golem.damage": "<PERSON><PERSON> golemo frakasas", "subtitles.entity.iron_golem.death": "Golemo fera mortas", "subtitles.entity.iron_golem.hurt": "Golemo fera do<PERSON>", "subtitles.entity.iron_golem.repair": "<PERSON>ra golemo reparesas", "subtitles.entity.item.break": "Objekto frakasas", "subtitles.entity.item.pickup": "Objekto riganesas", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON> plenige<PERSON>", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON><PERSON> pozesas", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> broken", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> tied", "subtitles.entity.lightning_bolt.impact": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON><PERSON>", "subtitles.entity.llama.ambient": "<PERSON><PERSON>", "subtitles.entity.llama.angry": "<PERSON><PERSON> bra<PERSON> iracoze", "subtitles.entity.llama.chest": "Kofro por lamao equipesas", "subtitles.entity.llama.death": "<PERSON><PERSON> mortas", "subtitles.entity.llama.eat": "<PERSON><PERSON>", "subtitles.entity.llama.hurt": "<PERSON><PERSON>", "subtitles.entity.llama.spit": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.llama.step": "<PERSON><PERSON>", "subtitles.entity.llama.swag": "<PERSON><PERSON>", "subtitles.entity.magma_cube.death": "Magmakubo mortas", "subtitles.entity.magma_cube.hurt": "Magmakubo doloras", "subtitles.entity.magma_cube.squish": "Magmakubo aplastas", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> jangles", "subtitles.entity.minecart.inside_underwater": "Minecart jangles underwater", "subtitles.entity.minecart.riding": "<PERSON><PERSON><PERSON><PERSON> rulas", "subtitles.entity.mooshroom.convert": "Mooshroomo transformas", "subtitles.entity.mooshroom.eat": "<PERSON><PERSON><PERSON><PERSON> manjas", "subtitles.entity.mooshroom.milk": "Mooshroom<PERSON> melkesas", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroomo melkesas suspektinde", "subtitles.entity.mule.ambient": "<PERSON><PERSON>", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.mule.chest": "Ko<PERSON><PERSON> por mulo equipesas", "subtitles.entity.mule.death": "<PERSON><PERSON> mortas", "subtitles.entity.mule.eat": "Jentrajo mangas", "subtitles.entity.mule.hurt": "<PERSON><PERSON>", "subtitles.entity.mule.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.painting.break": "<PERSON><PERSON><PERSON> frak<PERSON>", "subtitles.entity.painting.place": "Pikt<PERSON> pozesas", "subtitles.entity.panda.aggressive_ambient": "<PERSON><PERSON>", "subtitles.entity.panda.ambient": "<PERSON><PERSON>", "subtitles.entity.panda.bite": "<PERSON><PERSON> mordas", "subtitles.entity.panda.cant_breed": "Pando bramas", "subtitles.entity.panda.death": "<PERSON><PERSON> mortas", "subtitles.entity.panda.eat": "Pando manjas", "subtitles.entity.panda.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.panda.pre_sneeze": "Nazo di pando titilas", "subtitles.entity.panda.sneeze": "<PERSON><PERSON>", "subtitles.entity.panda.step": "Pando pazas", "subtitles.entity.panda.worried_ambient": "Pando ploretas", "subtitles.entity.parrot.ambient": "Papagayo dicas", "subtitles.entity.parrot.death": "Papa<PERSON><PERSON> mortas", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON> manjas", "subtitles.entity.parrot.fly": "Papago flugas", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.parrot.imitate.blaze": "Papagayo respiras", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON> bruegas", "subtitles.entity.parrot.imitate.creaking": "Parrot creaks", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON><PERSON> si<PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON> glug<PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.parrot.imitate.ender_dragon": "Papagayo bramegas", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON><PERSON> kuregas", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON> murmuras", "subtitles.entity.parrot.imitate.ghast": "Papagayo ploras", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON> lamentas", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON> b<PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.parrot.imitate.illusioner": "Papago murmuradas", "subtitles.entity.parrot.imitate.magma_cube": "Papaga<PERSON> splastas", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.pillager": "Papago murmuradas", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON> grunas", "subtitles.entity.parrot.imitate.shulker": "Papagayo guatas", "subtitles.entity.parrot.imitate.silverfish": "<PERSON><PERSON> grum<PERSON>", "subtitles.entity.parrot.imitate.skeleton": "Papa<PERSON><PERSON> k<PERSON>", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON> glutas", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON> grum<PERSON>", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON><PERSON> veksas", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON> murmuras", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON> hulas", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither": "Papagayo iracigas", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON> b<PERSON>", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON>", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON>", "subtitles.entity.phantom.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.phantom.bite": "<PERSON><PERSON><PERSON>", "subtitles.entity.phantom.death": "Fan<PERSON>o mortas", "subtitles.entity.phantom.flap": "<PERSON><PERSON><PERSON>", "subtitles.entity.phantom.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.phantom.swoop": "Fantomo infrestrokas", "subtitles.entity.pig.ambient": "<PERSON><PERSON><PERSON> bramas", "subtitles.entity.pig.death": "Porko mortas", "subtitles.entity.pig.hurt": "Porko do<PERSON>", "subtitles.entity.pig.saddle": "<PERSON><PERSON><PERSON><PERSON>lo", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> admiras objekto", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> snortas", "subtitles.entity.piglin.angry": "<PERSON><PERSON> snortas iracoze", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> celebras", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> converts to Zombified Piglin", "subtitles.entity.piglin.death": "<PERSON><PERSON> mortas", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> snorts enviously", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> retiras", "subtitles.entity.piglin.step": "<PERSON><PERSON> pasoj", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON> si<PERSON>", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON>tala kolere siblas", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON>lin Brute converts to Zombified Piglin", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON>rutala mortas", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> Brutal<PERSON> doloras", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON> steps", "subtitles.entity.pillager.ambient": "Spoilanto murmuras", "subtitles.entity.pillager.celebrate": "S<PERSON><PERSON><PERSON> aklamas", "subtitles.entity.pillager.death": "Spoilanto mortas", "subtitles.entity.pillager.hurt": "S<PERSON>ilanto do<PERSON>", "subtitles.entity.player.attack.crit": "<PERSON><PERSON><PERSON> f<PERSON>", "subtitles.entity.player.attack.knockback": "Knockback attack", "subtitles.entity.player.attack.strong": "Forta frapo", "subtitles.entity.player.attack.sweep": "Sweeping attack", "subtitles.entity.player.attack.weak": "Malforta frapo", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "Ludanto mortas", "subtitles.entity.player.freeze_hurt": "Ludanto frostigas", "subtitles.entity.player.hurt": "Ludanto doloras", "subtitles.entity.player.hurt_drown": "Ludanto dronas", "subtitles.entity.player.hurt_on_fire": "Ludant<PERSON> brulas", "subtitles.entity.player.levelup": "Ludanto ganas experienco", "subtitles.entity.player.teleport": "Player teleports", "subtitles.entity.polar_bear.ambient": "Urso blanka jemas", "subtitles.entity.polar_bear.ambient_baby": "Urso blanka grunas", "subtitles.entity.polar_bear.death": "Urso glaciala mortas", "subtitles.entity.polar_bear.hurt": "Urso blanka doloras", "subtitles.entity.polar_bear.warning": "Urso blanka bramas", "subtitles.entity.potion.splash": "Botelo frakasa<PERSON>", "subtitles.entity.potion.throw": "Botelo jet<PERSON>as", "subtitles.entity.puffer_fish.blow_out": "Suf<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_up": "Suflantofisho inflas", "subtitles.entity.puffer_fish.death": "Suflantofisho mortas", "subtitles.entity.puffer_fish.flop": "Suflantofisho <PERSON>", "subtitles.entity.puffer_fish.hurt": "Suflantofisho do<PERSON>", "subtitles.entity.puffer_fish.sting": "Suflant<PERSON><PERSON><PERSON> pikas", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON> salt<PERSON>", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.ravager.attack": "Devastanto mordas", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.death": "Devastanto mortas", "subtitles.entity.ravager.hurt": "Devastanto doloras", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON><PERSON> bramas", "subtitles.entity.ravager.step": "Devastanto pazas", "subtitles.entity.ravager.stunned": "Devastanto aturdesas", "subtitles.entity.salmon.death": "<PERSON><PERSON> mortas", "subtitles.entity.salmon.flop": "<PERSON><PERSON>", "subtitles.entity.salmon.hurt": "<PERSON><PERSON>", "subtitles.entity.sheep.ambient": "<PERSON><PERSON><PERSON> bramas", "subtitles.entity.sheep.death": "<PERSON>tono mortas", "subtitles.entity.sheep.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> gua<PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> closes", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> opens", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> la<PERSON>", "subtitles.entity.shulker.teleport": "Shulkero teleportas", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON> Bullet explodes", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON> Bullet breaks", "subtitles.entity.silverfish.ambient": "Argenta fisho sisas", "subtitles.entity.silverfish.death": "Argenta fisho mortas", "subtitles.entity.silverfish.hurt": "Argenta fisho doloras", "subtitles.entity.skeleton.ambient": "Skel<PERSON> bruisas", "subtitles.entity.skeleton.converted_to_stray": "Skeleton transformigis en senhejma", "subtitles.entity.skeleton.death": "Skeleto mortas", "subtitles.entity.skeleton.hurt": "Skeleto lezas", "subtitles.entity.skeleton.shoot": "Skeleto pafas", "subtitles.entity.skeleton_horse.ambient": "Skeleta kavalo krias", "subtitles.entity.skeleton_horse.death": "Skeleta kavalo mortas", "subtitles.entity.skeleton_horse.hurt": "Skeleta kavalo doloras", "subtitles.entity.skeleton_horse.jump_water": "Skeleton Horse jumps", "subtitles.entity.skeleton_horse.swim": "Skeleta kavalo natas", "subtitles.entity.slime.attack": "<PERSON><PERSON>", "subtitles.entity.slime.death": "<PERSON><PERSON> mortas", "subtitles.entity.slime.hurt": "<PERSON><PERSON>", "subtitles.entity.slime.squish": "<PERSON><PERSON>", "subtitles.entity.sniffer.death": "<PERSON>ni<PERSON> mortis", "subtitles.entity.sniffer.digging": "<PERSON><PERSON><PERSON> ka<PERSON>", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON> levigas", "subtitles.entity.sniffer.drop_seed": "<PERSON><PERSON><PERSON> faligas semojn", "subtitles.entity.sniffer.eat": "Sniffer mangas", "subtitles.entity.sniffer.egg_crack": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.sniffer.egg_hatch": "<PERSON><PERSON><PERSON> elbrodas ovo", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON> go<PERSON>", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON> doloras", "subtitles.entity.sniffer.idle": "Sniffer gruntas", "subtitles.entity.sniffer.scenting": "Sniffer flaras ion", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON> sercas", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON> pasoj", "subtitles.entity.snow_golem.death": "<PERSON><PERSON>o nivala mortas", "subtitles.entity.snow_golem.hurt": "<PERSON><PERSON><PERSON> nivala dolora<PERSON>", "subtitles.entity.snowball.throw": "Nivbulo flugas", "subtitles.entity.spider.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.spider.death": "Araneo mortas", "subtitles.entity.spider.hurt": "Araneo <PERSON>", "subtitles.entity.squid.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.squid.squirt": "<PERSON><PERSON><PERSON> pafas inko", "subtitles.entity.stray.ambient": "Vaganto k<PERSON>", "subtitles.entity.stray.death": "Vaganto mortas", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.death": "Strider mortas", "subtitles.entity.strider.eat": "Strider mangas", "subtitles.entity.strider.happy": "Strider kantas", "subtitles.entity.strider.hurt": "Strider do<PERSON>s", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON> pip<PERSON>as", "subtitles.entity.strider.retreat": "<PERSON><PERSON><PERSON> re<PERSON>", "subtitles.entity.tadpole.death": "Ranlarvo mortas", "subtitles.entity.tadpole.flop": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.grow_up": "<PERSON><PERSON>", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.tnt.primed": "TNT amorcesas", "subtitles.entity.tropical_fish.death": "<PERSON><PERSON><PERSON> fiso mortas", "subtitles.entity.tropical_fish.flop": "T<PERSON><PERSON><PERSON> fisho splashas", "subtitles.entity.tropical_fish.hurt": "Tropikala <PERSON>o doloras", "subtitles.entity.turtle.ambient_land": "<PERSON><PERSON><PERSON> pip<PERSON>", "subtitles.entity.turtle.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.turtle.death_baby": "Bebetortugo mortas", "subtitles.entity.turtle.egg_break": "Testudina ovo frakasas", "subtitles.entity.turtle.egg_crack": "Testudina ovo fendas", "subtitles.entity.turtle.egg_hatch": "<PERSON><PERSON><PERSON>", "subtitles.entity.turtle.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.turtle.hurt_baby": "Bebetortugo <PERSON>", "subtitles.entity.turtle.lay_egg": "Tortugo depozas ovo", "subtitles.entity.turtle.shamble": "<PERSON><PERSON>go marchas tranante", "subtitles.entity.turtle.shamble_baby": "Bebetortugo marchas tranante", "subtitles.entity.turtle.swim": "Tortugo natas", "subtitles.entity.vex.ambient": "Vexero vexas", "subtitles.entity.vex.charge": "Vexo kriegas", "subtitles.entity.vex.death": "Vexero mortas", "subtitles.entity.vex.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.celebrate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.death": "<PERSON><PERSON>jan<PERSON> mortas", "subtitles.entity.villager.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.no": "<PERSON><PERSON><PERSON><PERSON> disputas", "subtitles.entity.villager.trade": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_armorer": "Armifisto laboras", "subtitles.entity.villager.work_butcher": "Buchisto laboras", "subtitles.entity.villager.work_cartographer": "Kartografo laboras", "subtitles.entity.villager.work_cleric": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_farmer": "Farmisto laboras", "subtitles.entity.villager.work_fisherman": "Peskero laboras", "subtitles.entity.villager.work_fletcher": "Flechisto laboras", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON>", "subtitles.entity.villager.work_librarian": "Bibliotekisto labor<PERSON>", "subtitles.entity.villager.work_mason": "Masonisto laboras", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON> labor<PERSON>", "subtitles.entity.villager.work_toolsmith": "Utensilforjisto laboras", "subtitles.entity.villager.work_weaponsmith": "Armforjisto laboras", "subtitles.entity.villager.yes": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.ambient": "Praviganto murmuras", "subtitles.entity.vindicator.celebrate": "Pravigant<PERSON>", "subtitles.entity.vindicator.death": "Praviganto mortas", "subtitles.entity.vindicator.hurt": "Praviganto do<PERSON>", "subtitles.entity.wandering_trader.ambient": "Vaganta komer<PERSON>to murmuras", "subtitles.entity.wandering_trader.death": "Vaganta komercanto mortas", "subtitles.entity.wandering_trader.disappeared": "<PERSON>oja<PERSON><PERSON> k<PERSON><PERSON> ma<PERSON>", "subtitles.entity.wandering_trader.drink_milk": "Vojaganta komercisto trinkas lakton", "subtitles.entity.wandering_trader.drink_potion": "Vojaganta komercisto trinkas mikston", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON> komer<PERSON> do<PERSON>", "subtitles.entity.wandering_trader.no": "Vaganta komercanto ne konkordas", "subtitles.entity.wandering_trader.reappeared": "Vojaganta komercisto aperas", "subtitles.entity.wandering_trader.trade": "Vaganta komercanto komer<PERSON>", "subtitles.entity.wandering_trader.yes": "Vaganta komercan<PERSON> k<PERSON>", "subtitles.entity.warden.agitated": "Warden kolere lamentas", "subtitles.entity.warden.ambient": "Warden lamentas", "subtitles.entity.warden.angry": "Warden en kolero", "subtitles.entity.warden.attack_impact": "<PERSON> frapas bato", "subtitles.entity.warden.death": "Warden mortas", "subtitles.entity.warden.dig": "Warden enterigas sin", "subtitles.entity.warden.emerge": "<PERSON> aperas", "subtitles.entity.warden.heartbeat": "Warden`s koro batis", "subtitles.entity.warden.hurt": "<PERSON> do<PERSON>s", "subtitles.entity.warden.listening": "<PERSON> ausk<PERSON>", "subtitles.entity.warden.listening_angry": "<PERSON> ma<PERSON><PERSON> ausk<PERSON>as", "subtitles.entity.warden.nearby_close": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.warden.nearby_closer": "Warden pli proks<PERSON><PERSON>s", "subtitles.entity.warden.nearby_closest": "<PERSON> draws close", "subtitles.entity.warden.roar": "Gard<PERSON><PERSON> b<PERSON>", "subtitles.entity.warden.sniff": "Gardanto nju<PERSON>s", "subtitles.entity.warden.sonic_boom": "Gardanto frapas per sono", "subtitles.entity.warden.sonic_charge": "Gardanto akumulas sargon", "subtitles.entity.warden.step": "<PERSON> pasoj", "subtitles.entity.warden.tendril_clicks": "La moustaches de la gardanto klakas", "subtitles.entity.wind_charge.throw": "<PERSON><PERSON><PERSON> blovop<PERSON>", "subtitles.entity.wind_charge.wind_burst": "Projektilo di vento eksplodas", "subtitles.entity.witch.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON> drinkas", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.wither.ambient": "Withero iracigas", "subtitles.entity.wither.death": "<PERSON><PERSON> mortas", "subtitles.entity.wither.hurt": "<PERSON><PERSON> do<PERSON>", "subtitles.entity.wither.shoot": "<PERSON><PERSON> at<PERSON>", "subtitles.entity.wither.spawn": "Withero liberigesas", "subtitles.entity.wither_skeleton.ambient": "Witherskeleto bruisas", "subtitles.entity.wither_skeleton.death": "Witherskeleto mortas", "subtitles.entity.wither_skeleton.hurt": "Witherskeleto doloras", "subtitles.entity.wolf.ambient": "<PERSON><PERSON>", "subtitles.entity.wolf.bark": "Wolf barks", "subtitles.entity.wolf.death": "<PERSON>fo mortas", "subtitles.entity.wolf.growl": "Volfo grondas", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.pant": "Wolf pants", "subtitles.entity.wolf.shake": "<PERSON><PERSON>", "subtitles.entity.wolf.whine": "Wolf whines", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> kolere blekas", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> at<PERSON>", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> mortas", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> pasoj", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON> jemas", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON> skuas", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON> rompiĝas", "subtitles.entity.zombie.converted_to_drowned": "Zombio transformiĝis en Dronanto", "subtitles.entity.zombie.death": "Zombio mortas", "subtitles.entity.zombie.destroy_egg": "Testudo-ovo estas premita", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON> do<PERSON>", "subtitles.entity.zombie.infect": "Zombio infektas", "subtitles.entity.zombie_horse.ambient": "Zombia kavalo krias", "subtitles.entity.zombie_horse.death": "Zombia kavalo mortas", "subtitles.entity.zombie_horse.hurt": "<PERSON>omb<PERSON> kavalo doloras", "subtitles.entity.zombie_villager.ambient": "Zombia vilajano jemas", "subtitles.entity.zombie_villager.converted": "<PERSON><PERSON><PERSON> Vilajan<PERSON>", "subtitles.entity.zombie_villager.cure": "Zombio Vilajano susp<PERSON>", "subtitles.entity.zombie_villager.death": "Zombia vilajano mortas", "subtitles.entity.zombie_villager.hurt": "Zombia vilajano do<PERSON>s", "subtitles.entity.zombified_piglin.ambient": "<PERSON>omb<PERSON> gruntas", "subtitles.entity.zombified_piglin.angry": "Zombio Piglin kolere gruntas", "subtitles.entity.zombified_piglin.death": "Zombio Piglin mortas", "subtitles.entity.zombified_piglin.hurt": "Zombio Piglin dolora<PERSON>", "subtitles.event.mob_effect.bad_omen": "Omen takes hold", "subtitles.event.mob_effect.raid_omen": "Raid looms nearby", "subtitles.event.mob_effect.trial_omen": "Ominous trial looms nearby", "subtitles.event.raid.horn": "Malaugura korno sonas", "subtitles.item.armor.equip": "Kuraso equipas", "subtitles.item.armor.equip_chain": "<PERSON><PERSON>", "subtitles.item.armor.equip_diamond": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON> brui<PERSON>", "subtitles.item.armor.equip_gold": "Homarmaro ora tinkligas", "subtitles.item.armor.equip_iron": "Homarmaro fera kliktigas", "subtitles.item.armor.equip_leather": "<PERSON><PERSON><PERSON><PERSON> bruisas", "subtitles.item.armor.equip_netherite": "<PERSON><PERSON><PERSON><PERSON> netherita k<PERSON>", "subtitles.item.armor.equip_turtle": "Turtle Shell thunks", "subtitles.item.armor.equip_wolf": "<PERSON><PERSON>", "subtitles.item.armor.unequip_wolf": "<PERSON><PERSON>", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON>", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON> hakado", "subtitles.item.axe.wax_off": "Vakso forigas", "subtitles.item.bone_meal.use": "Bruo de ostpulvoro", "subtitles.item.book.page_turn": "Paperfolio su<PERSON>ras", "subtitles.item.book.put": "Libro batas", "subtitles.item.bottle.empty": "<PERSON><PERSON><PERSON> estas senen<PERSON>gita", "subtitles.item.bottle.fill": "Botelo plenigas", "subtitles.item.brush.brushing.generic": "P<PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "Purigo de Gravio", "subtitles.item.brush.brushing.gravel.complete": "Purigo de gravio estas kompletigita", "subtitles.item.brush.brushing.sand": "<PERSON><PERSON><PERSON>lo", "subtitles.item.brush.brushing.sand.complete": "Purigo de sablo estas kompletigita", "subtitles.item.bucket.empty": "<PERSON><PERSON>", "subtitles.item.bucket.fill": "<PERSON><PERSON> plenigas", "subtitles.item.bucket.fill_axolotl": "<PERSON><PERSON><PERSON><PERSON><PERSON> estas kaptita", "subtitles.item.bucket.fill_fish": "<PERSON>so estas kaptita", "subtitles.item.bucket.fill_tadpole": "Ka<PERSON><PERSON> estas kaptita", "subtitles.item.bundle.drop_contents": "Sako estas senenhavigita", "subtitles.item.bundle.insert": "Objekto estas aldonita", "subtitles.item.bundle.insert_fail": "Bundle full", "subtitles.item.bundle.remove_one": "Objekto estas elprenita", "subtitles.item.chorus_fruit.teleport": "Ludanto teleportas", "subtitles.item.crop.plant": "Rekontajo plantacesas", "subtitles.item.crossbow.charge": "Arbalesto tensigas", "subtitles.item.crossbow.hit": "Frapo per sago", "subtitles.item.crossbow.load": "Arbalesto pafaj<PERSON>as", "subtitles.item.crossbow.shoot": "Arbalesto pafas", "subtitles.item.dye.use": "Koloranto estas aplikita", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "Fairbulo <PERSON>", "subtitles.item.flintandsteel.use": "Silexo e stalo kliktas", "subtitles.item.glow_ink_sac.use": "Brilinkoj estas aplikitaj", "subtitles.item.goat_horn.play": "<PERSON><PERSON><PERSON>", "subtitles.item.hoe.till": "Bineto kultivas", "subtitles.item.honey_bottle.drink": "Gloto", "subtitles.item.honeycomb.wax_on": "Vakso estas aplikitaj", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "Inkoj estas aplikitaj", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "Lodestone Compass locks onto Lodestone", "subtitles.item.mace.smash_air": "<PERSON> smashes", "subtitles.item.mace.smash_ground": "<PERSON> smashes", "subtitles.item.nether_wart.plant": "Surbo estas plantita", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON> rompigas", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "Cizo kliktas", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "<PERSON><PERSON>", "subtitles.item.shovel.flatten": "Shov<PERSON> platiga<PERSON>", "subtitles.item.spyglass.stop_using": "Spektroskopo faldigas", "subtitles.item.spyglass.use": "Spektroskopo malfermigas", "subtitles.item.totem.use": "Totemo funcionas", "subtitles.item.trident.hit": "Tridento empalas", "subtitles.item.trident.hit_ground": "Tridento vibras", "subtitles.item.trident.return": "Tridento rivenas", "subtitles.item.trident.riptide": "Tridento aceleras", "subtitles.item.trident.throw": "Tridento frapas", "subtitles.item.trident.thunder": "Tridento strokas fulmino", "subtitles.item.wolf_armor.break": "<PERSON><PERSON><PERSON><PERSON> rompiga", "subtitles.item.wolf_armor.crack": "Luparmajo sveligas", "subtitles.item.wolf_armor.damage": "Damago estas farita al luparmajo", "subtitles.item.wolf_armor.repair": "Luparmajo estas restaurita", "subtitles.particle.soul_escape": "<PERSON><PERSON>", "subtitles.ui.cartography_table.take_result": "Mapo estas kreita", "subtitles.ui.hud.bubble_pop": "Breath meter dropping", "subtitles.ui.loom.take_result": "Tkestilo estas uzita", "subtitles.ui.stonecutter.take_result": "Stonhakisto estas uzita", "subtitles.weather.rain": "<PERSON><PERSON><PERSON> falas", "symlink_warning.message": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.pack": "Loading packs with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.world": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.more_info": "Plia informo", "symlink_warning.title": "World folder contains symbolic links", "symlink_warning.title.pack": "Added pack(s) contain(s) symbolic links", "symlink_warning.title.world": "The world folder contains symbolic links", "team.collision.always": "<PERSON><PERSON>", "team.collision.never": "<PERSON><PERSON><PERSON>", "team.collision.pushOtherTeams": "Pulsar altra esquadi", "team.collision.pushOwnTeam": "Pulsar propra esquado", "team.notFound": "Esquado nekonocata '%s'", "team.visibility.always": "Sempre", "team.visibility.hideForOtherTeams": "<PERSON><PERSON> por altra esquadi", "team.visibility.hideForOwnTeam": "<PERSON><PERSON> por propra esquado", "team.visibility.never": "Nul-tempe", "telemetry.event.advancement_made.description": "Understanding the context behind receiving an advancement can help us better understand and improve the progression of the game.", "telemetry.event.advancement_made.title": "Advancement Made", "telemetry.event.game_load_times.description": "This event can help us figure out where startup performance improvements are needed by measuring the execution times of the startup phases.", "telemetry.event.game_load_times.title": "Game Load Times", "telemetry.event.optional": "%s (Optional)", "telemetry.event.optional.disabled": "%s (Optional) - Disabled", "telemetry.event.performance_metrics.description": "Knowing the overall performance profile of Minecraft helps us tune and optimize the game for a wide range of machine specifications and operating systems. \nGame version is included to help us compare the performance profile for new versions of Minecraft.", "telemetry.event.performance_metrics.title": "Performance Metrics", "telemetry.event.required": "%s (Required)", "telemetry.event.world_load_times.description": "It's important for us to understand how long it takes to join a world, and how that changes over time. For example, when we add new features or do larger technical changes, we need to see what impact that had on load times.", "telemetry.event.world_load_times.title": "World Load Times", "telemetry.event.world_loaded.description": "Knowing how players play Minecraft (such as Game Mode, client or server modded, and game version) allows us to focus game updates to improve the areas that players care about most.\nThe World Loaded event is paired with the World Unloaded event to calculate how long the play session has lasted.", "telemetry.event.world_loaded.title": "World Loaded", "telemetry.event.world_unloaded.description": "This event is paired with the World Loaded event to calculate how long the world session has lasted.\nThe duration (in seconds and ticks) is measured when a world session has ended (quitting to title, disconnecting from a server).", "telemetry.event.world_unloaded.title": "Migrado de la mondo", "telemetry.property.advancement_game_time.title": "Tempo de ludo (tikoj)", "telemetry.property.advancement_id.title": "Atingo Identigilo", "telemetry.property.client_id.title": "<PERSON>lient<PERSON>", "telemetry.property.client_modded.title": "Modifiteco de kliento", "telemetry.property.dedicated_memory_kb.title": "<PERSON><PERSON><PERSON> memoro (kB)", "telemetry.property.event_timestamp_utc.title": "<PERSON><PERSON>ono (UTC)", "telemetry.property.frame_rate_samples.title": "Sango de bildfrekvenco (FPS)", "telemetry.property.game_mode.title": "Ludadreĝimo", "telemetry.property.game_version.title": "Ludospeco", "telemetry.property.launcher_name.title": "<PERSON><PERSON>", "telemetry.property.load_time_bootstrap_ms.title": "Tempo de inicializado (milisekundoj)", "telemetry.property.load_time_loading_overlay_ms.title": "Tempo sur ekrano de ŝargado (milisekundoj)", "telemetry.property.load_time_pre_window_ms.title": "Time Before Window Opens (Milliseconds)", "telemetry.property.load_time_total_time_ms.title": "Entuta ŝargotempo (milisekundoj)", "telemetry.property.minecraft_session_id.title": "Identigilo de Sesio Minecraft", "telemetry.property.new_world.title": "Nova Mondo", "telemetry.property.number_of_samples.title": "Grandeco de elektitaĵo", "telemetry.property.operating_system.title": "Operaciumo", "telemetry.property.opt_in.title": "Konsento por sendi", "telemetry.property.platform.title": "Platformo", "telemetry.property.realms_map_content.title": "Realms Map Content (Minigame Name)", "telemetry.property.render_distance.title": "Vida distanco", "telemetry.property.render_time_samples.title": "Traktado de tempo de kadro", "telemetry.property.seconds_since_load.title": "Time Since Load (Seconds)", "telemetry.property.server_modded.title": "Server Modded", "telemetry.property.server_type.title": "Server Type", "telemetry.property.ticks_since_load.title": "Time Since Load (Ticks)", "telemetry.property.used_memory_samples.title": "Used Random Access Memory", "telemetry.property.user_id.title": "User ID", "telemetry.property.world_load_time_ms.title": "World Load Time (Milliseconds)", "telemetry.property.world_session_id.title": "World Session ID", "telemetry_info.button.give_feedback": "<PERSON><PERSON> recenzo", "telemetry_info.button.privacy_statement": "Deklario pri privateco", "telemetry_info.button.show_data": "View My Data", "telemetry_info.opt_in.description": "I consent to sending optional telemetry data", "telemetry_info.property_title": "Included Data", "telemetry_info.screen.description": "Collecting this data helps us improve Minecraft by guiding us in directions that are relevant to our players.\nYou can also send in additional feedback to help us keep improving Minecraft.", "telemetry_info.screen.title": "Telemetry Data Collection", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Message:", "test_block.mode.accept": "Accept", "test_block.mode.fail": "Fail", "test_block.mode.log": "Log", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Save Structure", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Failed: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Invalid test ID", "test_instance.description.no_test": "No such test", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Entities:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[invalid]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "32-bit system detected: this may prevent you from playing in the future as a 64-bit system will be required!", "title.32bit.deprecation.realms": "Minecraft will soon require a 64-bit system, which will prevent you from playing or using Realms on this device. You will need to manually cancel any Realms subscription.", "title.32bit.deprecation.realms.check": "Do not show this screen again", "title.32bit.deprecation.realms.header": "32-bit system detected", "title.credits": "Copyright Mojang AB. Do not distribute!", "title.multiplayer.disabled": "Multiplayer is disabled. Please check your Microsoft account settings.", "title.multiplayer.disabled.banned.name": "You must change your name before you can play online", "title.multiplayer.disabled.banned.permanent": "Your account is permanently suspended from online play", "title.multiplayer.disabled.banned.temporary": "Your account is temporarily suspended from online play", "title.multiplayer.lan": "<PERSON><PERSON><PERSON> luda<PERSON> (LAN)", "title.multiplayer.other": "Multiplayer (3rd-party Server)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON> lundanti (Realms)", "title.singleplayer": "Un ludanto", "translation.test.args": "%s %s", "translation.test.complex": "Prefixo, %s%2$s itere %s e %1$s laste %s ed anke %1$s itere!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "saluto %", "translation.test.invalid2": "saluto %s", "translation.test.none": "Saluto, mondo!", "translation.test.world": "mondo", "trim_material.minecraft.amethyst": "Ametisto Materialo", "trim_material.minecraft.copper": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.diamond": "Diaman<PERSON>", "trim_material.minecraft.emerald": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.gold": "Oro Materialo", "trim_material.minecraft.iron": "<PERSON><PERSON>", "trim_material.minecraft.lapis": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.netherite": "Neterita Materialo", "trim_material.minecraft.quartz": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.redstone": "Rugastono Materialo", "trim_material.minecraft.resin": "Resin Material", "trim_pattern.minecraft.bolt": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.coast": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.dune": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.eye": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.flow": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.host": "Finituro Ĉefo", "trim_pattern.minecraft.raiser": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.rib": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.sentry": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.shaper": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.silence": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.snout": "Finituro Mufaĵo", "trim_pattern.minecraft.spire": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.tide": "<PERSON>ituro <PERSON>ren", "trim_pattern.minecraft.vex": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.ward": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.wayfinder": "Finituro Serĉisto", "trim_pattern.minecraft.wild": "Finituro Profundo", "tutorial.bundleInsert.description": "Dextra-klikez por insertar objekti", "tutorial.bundleInsert.title": "<PERSON><PERSON> f<PERSON>o", "tutorial.craft_planks.description": "La libro di recepti povas helpar", "tutorial.craft_planks.title": "Fabrikez planki ligna", "tutorial.find_tree.description": "Pugnofrapez ol por kolektar ligno", "tutorial.find_tree.title": "Trovez arboro", "tutorial.look.description": "Turnez per movar vua muso", "tutorial.look.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tutorial.move.description": "Saltez kun %s", "tutorial.move.title": "Movez kun %s, %s, %s ed %s", "tutorial.open_inventory.description": "Presez %s", "tutorial.open_inventory.title": "Apertez vua inventario", "tutorial.punch_tree.description": "Prestenez %s", "tutorial.punch_tree.title": "Destruktez la arboro", "tutorial.socialInteractions.description": "Presez %s por apertar", "tutorial.socialInteractions.title": "Sociaj interagoj", "upgrade.minecraft.netherite_upgrade": "<PERSON><PERSON><PERSON>"}