{"accessibility.onboarding.accessibility.button": "Kisegít<PERSON> beállítások…", "accessibility.onboarding.screen.narrator": "Nyomj Entert a felolvasás bekapcsolásához", "accessibility.onboarding.screen.title": "Üdvözlünk a Minecraftban!\n\nSzeretnéd bekapcsolni a felolvasást vagy ránézni a kisegítő beállításokra?", "addServer.add": "<PERSON><PERSON><PERSON>", "addServer.enterIp": "Szerver címe", "addServer.enterName": "Szerver neve", "addServer.resourcePack": "Szerver forráscsomagjai", "addServer.resourcePack.disabled": "<PERSON><PERSON>", "addServer.resourcePack.enabled": "<PERSON><PERSON>", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addServer.title": "Szerver adatainak szerkesztése", "advMode.command": "Konzolparancs", "advMode.mode": "<PERSON><PERSON><PERSON>", "advMode.mode.auto": "Ismétlődő", "advMode.mode.autoexec.bat": "<PERSON><PERSON>ton a<PERSON>ív", "advMode.mode.conditional": "Feltételes", "advMode.mode.redstone": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.redstoneTriggered": "Redstone-ra aktív", "advMode.mode.sequence": "<PERSON><PERSON><PERSON>", "advMode.mode.unconditional": "Feltétel nélküli", "advMode.notAllowed": "Kreatív módban lévő operátornak kellene lenned", "advMode.notEnabled": "A parancsblokkok nem engedélyezettek ezen a szerveren", "advMode.previousOutput": "Előző kimenet", "advMode.setCommand": "Konzolparancs beállítása a blokkhoz", "advMode.setCommand.success": "Parancs beállítva: %s", "advMode.trackOutput": "Kimenet követése", "advMode.triggering": "Aktiválás", "advMode.type": "<PERSON><PERSON><PERSON>", "advancement.advancementNotFound": "Ismeretlen előrelépés: %s", "advancements.adventure.adventuring_time.description": "Fedezz fel minden biomot", "advancements.adventure.adventuring_time.title": "Kalandozásra fel!", "advancements.adventure.arbalistic.description": "Terí<PERSON> le ötféle mobot egyetlen számszeríj-lövéssel", "advancements.adventure.arbalistic.title": "Számszerű fölény", "advancements.adventure.avoid_vibration.description": "Lopakodj el egy sculkérzékelő vagy Strázsa me<PERSON>, hogy ne vegyen észre", "advancements.adventure.avoid_vibration.title": "Csak csendben, csak halkan...", "advancements.adventure.blowback.description": "Pusztíts el egy Őrszelet egy Őrszéltől származó széllöket eltérítésével", "advancements.adventure.blowback.title": "Szembeszél", "advancements.adventure.brush_armadillo.description": "Söp<PERSON><PERSON>j le néhány szarulemezt egy taturól egy ecset segítségével", "advancements.adventure.brush_armadillo.title": "H<PERSON>t nem pikkelyesen aranyos?", "advancements.adventure.bullseye.description": "Találd el nyíllal egy céltábla közepét legalább 30 méterről", "advancements.adventure.bullseye.title": "Telitalálat", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Készíts egy díszített cserépedényt 4 cseréptöredékből", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Gondos he<PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "<PERSON><PERSON><PERSON>, ahogy egy barkácsoló legyárt egy bark<PERSON>ol<PERSON>", "advancements.adventure.crafters_crafting_crafters.title": "Barkácsoló-barkácsoló barkácsoló", "advancements.adventure.fall_from_world_height.description": "Zuhanj szabadesésben a világ tetejétől (az építéshatártól) a világ alj<PERSON>, és éld túl", "advancements.adventure.fall_from_world_height.title": "Barlangok és szirtek", "advancements.adventure.heart_transplanter.description": "Helyezz el egy Csikorgószívet helyesen két Sápadttölgy-rönk közé", "advancements.adventure.heart_transplanter.title": "Szívátültetés", "advancements.adventure.hero_of_the_village.description": "Védj meg egy megrohamozott falut", "advancements.adventure.hero_of_the_village.title": "A falu hőse", "advancements.adventure.honey_block_slide.description": "Ugorj egy méz<PERSON>lo<PERSON>, hogy felfogja az esésedet", "advancements.adventure.honey_block_slide.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_a_mob.description": "<PERSON><PERSON><PERSON> meg egy ellenséges lényt", "advancements.adventure.kill_a_mob.title": "Szörnyvadász", "advancements.adventure.kill_all_mobs.description": "Vadássz le egyet minden ellenséges szörnyből", "advancements.adventure.kill_all_mobs.title": "Szerezd meg mind!", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "<PERSON><PERSON>j meg egy mobot egy sculkgerjesztő közelében", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "<PERSON><PERSON> terjed", "advancements.adventure.lighten_up.description": "Ka<PERSON><PERSON>s meg egy rézizzót a fej<PERSON>, hogy fényesebb legyen", "advancements.adventure.lighten_up.title": "Lőn világosság", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Védj meg egy falusit a villámcsapástól ané<PERSON>l, hogy tűz ütne ki", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Túlfeszültség-védelem", "advancements.adventure.minecraft_trials_edition.description": "Me<PERSON>sz<PERSON>j be egy Küzdőtérre", "advancements.adventure.minecraft_trials_edition.title": "Küzdj és bízva bízzál", "advancements.adventure.ol_betsy.description": "Lőj egy számszeríjjal", "advancements.adventure.ol_betsy.title": "A vén Böske", "advancements.adventure.overoverkill.description": "Okozz ötven szívnyi sebzést egyetlen buzogányütéssel", "advancements.adventure.overoverkill.title": "Les<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.play_jukebox_in_meadows.description": "Keltsd életre a rétet egy zenegép segítségével", "advancements.adventure.play_jukebox_in_meadows.title": "A muzsika hangja", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Olvasd le egy faragott könyvespolc jelerősségét egy komparátor segítségével", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "A könyvek ereje", "advancements.adventure.revaulting.description": "Nyiss ki egy vészjósló széfet egy vészjósló kulccsal", "advancements.adventure.revaulting.title": "Széfek széfje", "advancements.adventure.root.description": "<PERSON><PERSON><PERSON><PERSON>, felfedezés és harc", "advancements.adventure.root.title": "Kaland", "advancements.adventure.salvage_sherd.description": "Söprögess el egy g<PERSON> b<PERSON>, és szerezz belőle egy cseréptöredéket", "advancements.adventure.salvage_sherd.title": "Maradványok t<PERSON>ztelete", "advancements.adventure.shoot_arrow.description": "Találj el valamit nyíllal", "advancements.adventure.shoot_arrow.title": "A játékosok nyilaitól...", "advancements.adventure.sleep_in_bed.description": "<PERSON><PERSON><PERSON> e<PERSON>, hogy megváltozzon az újraéledési helyed", "advancements.adventure.sleep_in_bed.title": "Aludj el szépen", "advancements.adventure.sniper_duel.description": "<PERSON><PERSON><PERSON> meg egy csontvázat legalább 50 méterről", "advancements.adventure.sniper_duel.title": "Lövészp<PERSON><PERSON><PERSON>", "advancements.adventure.spyglass_at_dragon.description": "Nézz rá az Endersárkányra távcsövön keresztül", "advancements.adventure.spyglass_at_dragon.title": "<PERSON><PERSON>?", "advancements.adventure.spyglass_at_ghast.description": "Nézz rá egy ghastra távcsövön keresz<PERSON>ül", "advancements.adventure.spyglass_at_ghast.title": "<PERSON><PERSON>?", "advancements.adventure.spyglass_at_parrot.description": "Nézz rá egy papagájra távcsövön kere<PERSON>ül", "advancements.adventure.spyglass_at_parrot.title": "Mi ez, egy madár?", "advancements.adventure.summon_iron_golem.description": "Ép<PERSON>ts vasgólemet egy falu védelmére", "advancements.adventure.summon_iron_golem.title": "Zsoldos kerestetik", "advancements.adventure.throw_trident.description": "Találj el valamit egy szigonnyal.\nMegjegyzés: Eldobni az egyetlen fegyvered nem jó <PERSON>.", "advancements.adventure.throw_trident.title": "<PERSON><PERSON>", "advancements.adventure.totem_of_undying.description": "Menekülj meg a haláltól egy Életmentés totemével", "advancements.adventure.totem_of_undying.title": "Feltámadás", "advancements.adventure.trade.description": "<PERSON><PERSON>ss üzletet egy falusival", "advancements.adventure.trade.title": "Micsoda üzlet!", "advancements.adventure.trade_at_world_height.description": "Kereskedj egy falusival az építési határnál", "advancements.adventure.trade_at_world_height.title": "<PERSON>t c<PERSON>gos kiszolgálás", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Használj fel mindezekből a kovácssablonokból legalább egyet-egyet: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Bosszancs, Hullámos, Vándor", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_any_armor_pattern.description": "Díszíts ki egy páncélt egy kovácsasztalon", "advancements.adventure.trim_with_any_armor_pattern.title": "Kovácsolj új <PERSON>", "advancements.adventure.two_birds_one_arrow.description": "Lőj le két Fantomot egy átdöfő nyíllal", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON><PERSON> legyet egy csapásra", "advancements.adventure.under_lock_and_key.description": "Nyiss ki egy széfet egy küzdőtéri kulccsal", "advancements.adventure.under_lock_and_key.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.use_lodestone.description": "Használj egy iránytűt egy iránykövön", "advancements.adventure.use_lodestone.title": "<PERSON> elmész, visszavár", "advancements.adventure.very_very_frightening.description": "<PERSON><PERSON><PERSON><PERSON> vill<PERSON> egy falusit", "advancements.adventure.very_very_frightening.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.voluntary_exile.description": "Ö<PERSON>j meg egy rablókapitányt.\nTalán <PERSON>mes lenne távol maradni a falvaktól egy ideig...", "advancements.adventure.voluntary_exile.title": "Önkéntes száműzetés", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>... <PERSON><PERSON><PERSON><PERSON><PERSON>, hogy be<PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Könnyű, mint a nyúl", "advancements.adventure.who_needs_rockets.description": "Röpítsd magad 8 blokk magasra egy széllökettel", "advancements.adventure.who_needs_rockets.title": "Minek a tűzijáték?", "advancements.adventure.whos_the_pillager_now.description": "Etess meg egy rablót a saját főztjével", "advancements.adventure.whos_the_pillager_now.title": "<PERSON><PERSON><PERSON>, ki foszto<PERSON>?", "advancements.empty": "<PERSON><PERSON>, hogy nincs itt semmi...", "advancements.end.dragon_breath.description": "Palackozd be az Endersárkány lehelletét", "advancements.end.dragon_breath.title": "<PERSON><PERSON><PERSON> f<PERSON> egy rág<PERSON>", "advancements.end.dragon_egg.description": "Fogd k<PERSON>z<PERSON> a sárkánytojást", "advancements.end.dragon_egg.title": "<PERSON><PERSON> ú<PERSON>", "advancements.end.elytra.description": "Ta<PERSON><PERSON><PERSON><PERSON> egy kitinszárnyat", "advancements.end.elytra.title": "Szárnyakat ad", "advancements.end.enter_end_gateway.description": "Juss ki a szigetről", "advancements.end.enter_end_gateway.title": "Szabadság", "advancements.end.find_end_city.description": "<PERSON><PERSON><PERSON>j csak be, mi bajod lehet?", "advancements.end.find_end_city.title": "Világvégi város", "advancements.end.kill_dragon.description": "Sok szerencsét", "advancements.end.kill_dragon.title": "Szabadítsd meg az Endet", "advancements.end.levitate.description": "Lebegj egy shulkertámadástól 50 métert felfelé", "advancements.end.levitate.title": "Remek a kilátás idefentről", "advancements.end.respawn_dragon.description": "Idézd meg újra az Endersárkányt", "advancements.end.respawn_dragon.title": "Kezdheted elölről", "advancements.end.root.description": "<PERSON><PERSON>, mint egy új kaland kezdete", "advancements.end.root.title": "Az End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Vegyél rá egy n<PERSON>, hogy dobjon tortát egy hangdobozra", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Sz<PERSON><PERSON><PERSON> dal", "advancements.husbandry.allay_deliver_item_to_player.description": "Vegy<PERSON><PERSON> rá egy n<PERSON>, hogy hozzon neked t<PERSON>t", "advancements.husbandry.allay_deliver_item_to_player.title": "<PERSON><PERSON><PERSON> egy jó <PERSON>", "advancements.husbandry.axolotl_in_a_bucket.description": "<PERSON>ogj meg egy axolotlt vödörrel", "advancements.husbandry.axolotl_in_a_bucket.title": "A legcukibb ragadozó", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON><PERSON><PERSON>, ami eh<PERSON>, ak<PERSON> is, ha nem egészséges", "advancements.husbandry.balanced_diet.title": "Kiegyensúlyozott táplálkozás", "advancements.husbandry.breed_all_animals.description": "Pároztass minden állatfajból kettőt", "advancements.husbandry.breed_all_animals.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.breed_an_animal.description": "Pároztass k<PERSON>", "advancements.husbandry.breed_an_animal.title": "Romantikus vacsora", "advancements.husbandry.complete_catalogue.description": "Szelíd<PERSON>ts meg egyet-egyet minden macskafajtából!", "advancements.husbandry.complete_catalogue.title": "<PERSON><PERSON> cica, k<PERSON>t cica...", "advancements.husbandry.feed_snifflet.description": "<PERSON><PERSON>s meg egy orron<PERSON>ófi<PERSON>", "advancements.husbandry.feed_snifflet.title": "<PERSON><PERSON>", "advancements.husbandry.fishy_business.description": "<PERSON><PERSON><PERSON> egy halat", "advancements.husbandry.fishy_business.title": "Valami bűzlik", "advancements.husbandry.froglights.description": "Vedd magadhoz az összes fajta varangyfényt", "advancements.husbandry.froglights.title": "Egyesített hat<PERSON>ból...!", "advancements.husbandry.kill_axolotl_target.description": "<PERSON><PERSON><PERSON> egy axolotllal és nyerj meg egy csat<PERSON>t", "advancements.husbandry.kill_axolotl_target.title": "A barátság gyógyító ereje!", "advancements.husbandry.leash_all_frog_variants.description": "Lasszózz meg egy-egy békát minden fajtából", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON> három testőr", "advancements.husbandry.make_a_sign_glow.description": "Világítsd ki egy tábla szövegét", "advancements.husbandry.make_a_sign_glow.title": "Ez itt a neonreklám helye!", "advancements.husbandry.netherite_hoe.description": "<PERSON><PERSON><PERSON><PERSON> fel egy kap<PERSON>t egy nether<PERSON><PERSON><PERSON><PERSON><PERSON>, majd alaposan gondold át az é<PERSON>ed", "advancements.husbandry.netherite_hoe.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.obtain_sniffer_egg.description": "Szerezz egy orron<PERSON>ó<PERSON>", "advancements.husbandry.obtain_sniffer_egg.title": "Érdekes illat", "advancements.husbandry.place_dried_ghast_in_water.description": "Rakj egy Kiszáradt Ghast blokkot a vízbe", "advancements.husbandry.place_dried_ghast_in_water.title": "Rendszeresen hidratálj!", "advancements.husbandry.plant_any_sniffer_seed.description": "Ültess el egy Orrontó által felásott ősi magot", "advancements.husbandry.plant_any_sniffer_seed.title": "A múlt elültetése", "advancements.husbandry.plant_seed.description": "<PERSON><PERSON><PERSON> el egy magot, <PERSON>s nézd, ah<PERSON> megnő", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "S<PERSON><PERSON> le egy farkaspáncélt egy farkasról, o<PERSON><PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.title": "Kopasz kutya", "advancements.husbandry.repair_wolf_armor.description": "Javíts meg egy sérült farkaspáncélt tatupikkelyekkel", "advancements.husbandry.repair_wolf_armor.title": "A blöki új ruhája", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Evezzetek kettesben: egy<PERSON><PERSON><PERSON> te s kedvenced, egy kec<PERSON>", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Kellemes eme nedves mederben, nemde?", "advancements.husbandry.root.description": "A világ tele van étellel és barátokkal", "advancements.husbandry.root.title": "Gazdálkodás", "advancements.husbandry.safely_harvest_honey.description": "Egy tábortűz segítségével gyűjts mézet egy kaptárból egy palackba anélkül, hogy felingerelnéd a méheket", "advancements.husbandry.safely_harvest_honey.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, édes", "advancements.husbandry.silk_touch_nest.description": "Költöztess el egy méhkast Gyengéd érintéssel, benne 3 méhvel", "advancements.husbandry.silk_touch_nest.title": "Méhvándorlás", "advancements.husbandry.tactical_fishing.description": "Fogj egy halat... horgászbot nélkül!", "advancements.husbandry.tactical_fishing.title": "Takt<PERSON><PERSON>", "advancements.husbandry.tadpole_in_a_bucket.description": "<PERSON>ogj meg egy ebi<PERSON>t vödörrel", "advancements.husbandry.tadpole_in_a_bucket.title": "Ebihalásza<PERSON>", "advancements.husbandry.tame_an_animal.description": "Szelídíts meg egy álla<PERSON>t", "advancements.husbandry.tame_an_animal.title": "Barátok mindörökké", "advancements.husbandry.wax_off.description": "S<PERSON>d le a viaszt egy rézblokkról!", "advancements.husbandry.wax_off.title": "Viaszt le", "advancements.husbandry.wax_on.description": "Viaszolj be egy rézblokkot méhsejttel!", "advancements.husbandry.wax_on.title": "Viaszt fel", "advancements.husbandry.whole_pack.description": "Szelídíts meg egy-egy farkast az összes fajtából", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON><PERSON> kutya, m<PERSON>ik eb", "advancements.nether.all_effects.description": "Érezd az összes létező hatást egyszerre", "advancements.nether.all_effects.title": "Hogy jutottunk idáig?", "advancements.nether.all_potions.description": "Legyél az összes bájital hatása alatt egyszerre", "advancements.nether.all_potions.title": "Mindenízű koktél", "advancements.nether.brew_potion.description": "Főzz bájitalt", "advancements.nether.brew_potion.title": "<PERSON><PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.description": "Tölts fel maximálisan egy újraéledés-kapcsot", "advancements.nether.charge_respawn_anchor.title": "<PERSON><PERSON>n kilenc <PERSON>", "advancements.nether.create_beacon.description": "Készíts el és üzemelj be egy jelzőfényt", "advancements.nether.create_beacon.title": "Fény az <PERSON>", "advancements.nether.create_full_beacon.description": "Készíts egy maximális erejű jelzőfényt", "advancements.nether.create_full_beacon.title": "Világnak világa", "advancements.nether.distract_piglin.description": "Tereld el a Piglinek figyelmét arannyal", "advancements.nether.distract_piglin.title": "<PERSON><PERSON><PERSON><PERSON>, csillog!", "advancements.nether.explore_nether.description": "Fedezd fel a Nether minden biomját", "advancements.nether.explore_nether.title": "Pokoljárás", "advancements.nether.fast_travel.description": "Utazz 7 km felvilági távolságot a Netherben", "advancements.nether.fast_travel.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.find_bastion.description": "Fedezz fel egy b<PERSON><PERSON>", "advancements.nether.find_bastion.title": "<PERSON><PERSON><PERSON>", "advancements.nether.find_fortress.description": "Ostromolj meg egy nether-er<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.find_fortress.title": "Csak legyen hozzá erőd", "advancements.nether.get_wither_skull.description": "Szerezd meg egy Withercsontváz kopony<PERSON>j<PERSON>t", "advancements.nether.get_wither_skull.title": "'<PERSON><PERSON>, s<PERSON><PERSON><PERSON>...'", "advancements.nether.loot_bastion.description": "Rámolj ki egy lád<PERSON>t egy bástyaromban", "advancements.nether.loot_bastion.title": "Add az aranyod, aranyod", "advancements.nether.netherite_armor.description": "S<PERSON><PERSON>z be egy teljes netheritpáncélzatot", "advancements.nether.netherite_armor.title": "<PERSON><PERSON><PERSON><PERSON> be t<PERSON>rmel<PERSON>", "advancements.nether.obtain_ancient_debris.description": "Szerezz némi ősi törmeléket", "advancements.nether.obtain_ancient_debris.title": "Mit rejt a mély?", "advancements.nether.obtain_blaze_rod.description": "Szabadíts meg egy Őrlángot a lángrúdjától", "advancements.nether.obtain_blaze_rod.title": "Tüzet szüntess", "advancements.nether.obtain_crying_obsidian.description": "Szerezz könnyező obszidiánt", "advancements.nether.obtain_crying_obsidian.title": "<PERSON>z olyan meghat<PERSON>", "advancements.nether.return_to_sender.description": "Pusztíts el egy Ghastot egy tűzgolyóval", "advancements.nether.return_to_sender.title": "Vissza a feladónak", "advancements.nether.ride_strider.description": "Lovagolj meg egy <PERSON>, és vezesd egy torz gomb<PERSON><PERSON>", "advancements.nether.ride_strider.title": "<PERSON><PERSON>", "advancements.nether.ride_strider_in_overworld_lava.description": "Tegyél meg egy jóóó hosszú utat álomfutóval egy felvilági lávatavon", "advancements.nether.ride_strider_in_overworld_lava.title": "Akárcsak otthon", "advancements.nether.root.description": "<PERSON><PERSON><PERSON> otthon a pulóvert", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "Id<PERSON>zd meg a <PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON> h<PERSON>", "advancements.nether.uneasy_alliance.description": "Ments ki egy Ghastot a Netherből, hozd át épségben a Felvilágba... majd öld meg", "advancements.nether.uneasy_alliance.title": "<PERSON><PERSON><PERSON> szövetség", "advancements.nether.use_lodestone.description": "Mágnesezz fel egy iránytűt egy iránykövön", "advancements.nether.use_lodestone.title": "<PERSON> elmész, visszavár", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Gyengíts le és gyógyíts meg egy élőhalott falusit", "advancements.story.cure_zombie_villager.title": "Zombior<PERSON>", "advancements.story.deflect_arrow.description": "Háríts el egy lövedéket egy pajzzsal", "advancements.story.deflect_arrow.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nem nyert", "advancements.story.enchant_item.description": "Bűvölj meg egy tárgyat egy varázslóasztalon", "advancements.story.enchant_item.title": "Elbűvölő egyéniség", "advancements.story.enter_the_end.description": "Lépj be az End-portálba", "advancements.story.enter_the_end.title": "Vége?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON><PERSON><PERSON> meg egy <PERSON>-port<PERSON><PERSON>, g<PERSON><PERSON><PERSON><PERSON><PERSON> be, <PERSON>s kelj <PERSON> rajta", "advancements.story.enter_the_nether.title": "Mélyebbre kell menn<PERSON>nk", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON><PERSON><PERSON> mit lát?", "advancements.story.form_obsidian.description": "Szerezz egy obszidiánblokkot", "advancements.story.form_obsidian.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.iron_tools.description": "Fejleszd tovább a csákányod", "advancements.story.iron_tools.title": "Munkáscsákány vascsákány", "advancements.story.lava_bucket.description": "<PERSON><PERSON><PERSON> meg egy vödrö<PERSON> l<PERSON>", "advancements.story.lava_bucket.title": "Vig<PERSON>á<PERSON>, forró", "advancements.story.mine_diamond.description": "Szerezz gyémántokat", "advancements.story.mine_diamond.title": "Gyémántok!", "advancements.story.mine_stone.description": "Bányássz követ az új csákányoddal", "advancements.story.mine_stone.title": "K<PERSON>korszaki", "advancements.story.obtain_armor.description": "Vértezd fel magad egy vaspáncéllal", "advancements.story.obtain_armor.title": "Öltözz be", "advancements.story.root.description": "A játék leglényege", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "A gyémántpáncélzat életet menthet", "advancements.story.shiny_gear.title": "Boríts be gyémántokkal", "advancements.story.smelt_iron.description": "<PERSON><PERSON> egy vasrudat", "advancements.story.smelt_iron.title": "<PERSON><PERSON><PERSON>, amíg meleg", "advancements.story.upgrade_tools.description": "Készíts egy erősebb csákányt", "advancements.story.upgrade_tools.title": "A fejlődés útján", "advancements.toast.challenge": "Kihívás teljesítve!", "advancements.toast.goal": "Cél teljesítve!", "advancements.toast.task": "Előrelépés teljesítve!", "argument.anchor.invalid": "Érvénytelen koordinátarögzítési pont: %s", "argument.angle.incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON> (1-nek kellene lennie)", "argument.angle.invalid": "Érvénytelen s<PERSON>ög", "argument.block.id.invalid": "Ismeretlen blokktípus: '%s'", "argument.block.property.duplicate": "A(z) '%s' tulajdonság csak egyszer adható meg %s blokk számára", "argument.block.property.invalid": "A(z) %1$s blokk %3$s tulajdonsága nem vehet fel '%2$s' értéket", "argument.block.property.novalue": "A(z) %2$s blokk '%1$s' tulajdonságának hiányzik az értéke", "argument.block.property.unclosed": "Hiányzó ']' a blokktulajdonságok listájának végéről", "argument.block.property.unknown": "%s blokk nem rendelkezik a(z) '%s' tulajdonsággal", "argument.block.tag.disallowed": "Csak konkrét blokknevek adhatók meg itt, címkék nem", "argument.color.invalid": "Ismeretlen szín: '%s'", "argument.component.invalid": "Érvénytelen komponens: %s", "argument.criteria.invalid": "Ismeretlen feltételek: \"%s\"", "argument.dimension.invalid": "Ismeretlen dimenzió: '%s'", "argument.double.big": "A \"double\" törtszám nem lehet nagyobb, mint %s, de %s van megadva", "argument.double.low": "A \"double\" törtszám nem lehet kisebb, mint %s, de %s van megadva", "argument.entity.invalid": "Érvénytelen név vagy UUID", "argument.entity.notfound.entity": "<PERSON><PERSON> entitás", "argument.entity.notfound.player": "<PERSON><PERSON>", "argument.entity.options.advancements.description": "Játékosok előrelépései", "argument.entity.options.distance.description": "Entitás távolsága", "argument.entity.options.distance.negative": "A távolság nem lehet negatív", "argument.entity.options.dx.description": "Entitások x és x + dx között", "argument.entity.options.dy.description": "Entitások y és y + dy között", "argument.entity.options.dz.description": "Entitások z és z + dz között", "argument.entity.options.gamemode.description": "Játékosok játékmódja", "argument.entity.options.inapplicable": "A(z) '%s' opció itt nem alkalmazható", "argument.entity.options.level.description": "Tapasztalati szint", "argument.entity.options.level.negative": "A szint nem lehet negatív", "argument.entity.options.limit.description": "Visszaadott entitások maximális száma", "argument.entity.options.limit.toosmall": "A korlát nem lehet 1-n<PERSON><PERSON> kisebb", "argument.entity.options.mode.invalid": "Érvénytelen vagy ismeretlen játékmód: '%s'", "argument.entity.options.name.description": "Entitás neve", "argument.entity.options.nbt.description": "Entitások NBT-címkéi", "argument.entity.options.predicate.description": "<PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.scores.description": "Entitások pontszámai", "argument.entity.options.sort.description": "Entitások sorrendje", "argument.entity.options.sort.irreversible": "Érvénytelen vagy ismeretlen rendezési mód: '%s'", "argument.entity.options.tag.description": "Entitások címkéje", "argument.entity.options.team.description": "Entitások csapata", "argument.entity.options.type.description": "Entitások típusa", "argument.entity.options.type.invalid": "Érvénytelen vagy ismeretlen entitástípus: '%s'", "argument.entity.options.unknown": "Ismeretlen opció: '%s'", "argument.entity.options.unterminated": "Lezáratlan opciólista", "argument.entity.options.valueless": "A(z) '%s' opciónak hiányzik az értéke", "argument.entity.options.x.description": "X koordináta", "argument.entity.options.x_rotation.description": "Entitás függőleges elfordulása", "argument.entity.options.y.description": "<PERSON>", "argument.entity.options.y_rotation.description": "Entitás vízszintes elfordulása", "argument.entity.options.z.description": "<PERSON>", "argument.entity.selector.allEntities": "Minden entitás", "argument.entity.selector.allPlayers": "<PERSON><PERSON>", "argument.entity.selector.missing": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "argument.entity.selector.nearestEntity": "Legközelebbi entitás", "argument.entity.selector.nearestPlayer": "Legközelebbi <PERSON>", "argument.entity.selector.not_allowed": "<PERSON><PERSON> megen<PERSON> s<PERSON>", "argument.entity.selector.randomPlayer": "Véletlenszerű játékos", "argument.entity.selector.self": "Jelenlegi entitás", "argument.entity.selector.unknown": "Ismeretlen szelektortípus: '%s'", "argument.entity.toomany": "A parancs egy entit<PERSON><PERSON> v<PERSON>, de a megadott szelektor többet jelöl meg", "argument.enum.invalid": "Érvén<PERSON><PERSON>: \"%s\"", "argument.float.big": "A lebegőpontos törtszám nem lehet nagyobb, mint %s, de %s van megadva", "argument.float.low": "A lebegőpontos törtszám nem lehet kisebb, mint %s, de %s van megadva", "argument.gamemode.invalid": "Ismeretlen játékmód: %s", "argument.hexcolor.invalid": "A \"%s\" egy érvénytelen hex színkód", "argument.id.invalid": "Érvén<PERSON><PERSON>", "argument.id.unknown": "Ismeretlen azonosító: %s", "argument.integer.big": "<PERSON>z egész szám nem lehet nagyobb, mint %s, de %s van megadva", "argument.integer.low": "<PERSON>z egész sz<PERSON> nem le<PERSON>t kisebb, mint %s, de %s van megadva", "argument.item.id.invalid": "Ismeretlen tárgy: '%s'", "argument.item.tag.disallowed": "Csak konkrét tárgynevek adhatók meg itt, címkék nem", "argument.literal.incorrect": "Hiányzó '%s' literál", "argument.long.big": "A \"long\" egész s<PERSON> nem lehet nagyobb, mint %s, de %s van megadva", "argument.long.low": "A \"long\" egész s<PERSON> nem le<PERSON>t kisebb, mint %s, de %s van megadva", "argument.message.too_long": "Az üzenet túl ho<PERSON> (%s > maximális %s karakter)", "argument.nbt.array.invalid": "Érvénytelen tömbtípus: '%s'", "argument.nbt.array.mixed": "<PERSON>em <PERSON> %s elemet beszúrni %s listába", "argument.nbt.expected.compound": "<PERSON><PERSON><PERSON><PERSON> összetett címke", "argument.nbt.expected.key": "Hiányz<PERSON>", "argument.nbt.expected.value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.nbt.list.mixed": "Nem le<PERSON>t %s elemet beszúrni egy %s listába", "argument.nbt.trailing": "<PERSON><PERSON> v<PERSON>rt to<PERSON><PERSON><PERSON><PERSON>", "argument.player.entities": "A parancs csak játékosokra lehet hatás<PERSON>, de a megadott szelektor entitásokat is megjelöl", "argument.player.toomany": "A parancs egy j<PERSON><PERSON> vá<PERSON>, de a megadott szelektor többet jelöl meg", "argument.player.unknown": "A megadott játékos nem létezik", "argument.pos.missing.double": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.pos.missing.int": "Hián<PERSON>zó blokk-koordináta", "argument.pos.mixed": "Az abszolút és a relatív koordináták nem vegyíthetőek (vagy mindenhol ^-nak kell lennie, vagy sehol sem)", "argument.pos.outofbounds": "Ez a pozíció kívül esik a megengedett határokon.", "argument.pos.outofworld": "Ez a hely a világon kívül esik", "argument.pos.unloaded": "Ez a hely nincs betöltve", "argument.pos2d.incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON> (2-nek kellene lennie)", "argument.pos3d.incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON> (3-nak kellene lennie)", "argument.range.empty": "Hiányzó érték vagy értéktartomány", "argument.range.ints": "Csak egész számok engedély<PERSON>ettek, törtek nem", "argument.range.swapped": "A minimum nem haladhatja meg a maximumot", "argument.resource.invalid_type": "A(z) '%s' elem típusa hibás ('%s' a várt '%s' helyett)", "argument.resource.not_found": "Nem található a(z) '%2$s' típusú '%1$s' elem", "argument.resource_or_id.failed_to_parse": "<PERSON><PERSON>lmezni a struktúrát: %s", "argument.resource_or_id.invalid": "Érvén<PERSON><PERSON> azon<PERSON>í<PERSON>ó vagy c<PERSON>", "argument.resource_or_id.no_such_element": "Nem <PERSON> \"%s\" elem a \"%s\" registryben", "argument.resource_selector.not_found": "<PERSON>em <PERSON> pár a(z) '%s' szelektorhoz '%s' típusban", "argument.resource_tag.invalid_type": "A(z) '%s' címke típusa hibás ('%s' a várt '%s' helyett)", "argument.resource_tag.not_found": "Nem <PERSON>ó a(z) '%2$s' típusú '%1$s' címke", "argument.rotation.incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON> (2-nek kellene lennie)", "argument.scoreHolder.empty": "<PERSON>em <PERSON> a megfelelő pontszámmal rendelkező entitás", "argument.scoreboardDisplaySlot.invalid": "Ismeretlen megjelení<PERSON> hely: '%s'", "argument.style.invalid": "Érvénytelen stílus: %s", "argument.time.invalid_tick_count": "A tickek sz<PERSON>ma nem lehet negatív", "argument.time.invalid_unit": "Érvénytelen mértékegység", "argument.time.tick_count_too_low": "A tickek sz<PERSON>ma nem lehet kisebb, mint %s, de %s van megadva", "argument.uuid.invalid": "Érvénytelen UUID", "argument.waypoint.invalid": "A kiválasztott entitás nem egy waypoint", "arguments.block.tag.unknown": "Ismeretlen blokkcímke: '%s'", "arguments.function.tag.unknown": "Ismeretlen eljár<PERSON>ímke: '%s'", "arguments.function.unknown": "Ismeretlen eljárás: '%s'", "arguments.item.component.expected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments.item.component.malformed": "Hibás komponens '%s' számára: „%s”", "arguments.item.component.repeated": "A(z) '%s' tárgykomponens többször s<PERSON>el, pedig csak egy érték adható meg", "arguments.item.component.unknown": "Ismeretlen tárgykomponens: „%s”", "arguments.item.malformed": "Hibás tárgy: „%s”", "arguments.item.overstacked": "%s csak %s-ig halmozható", "arguments.item.predicate.malformed": "Hibás feltétel '%s' számára: „%s”", "arguments.item.predicate.unknown": "Ismeretlen tárgyfeltétel: „%s”", "arguments.item.tag.unknown": "Ismeretlen tárgycímke: '%s'", "arguments.nbtpath.node.invalid": "Érvénytelen NBT-útvonalelem", "arguments.nbtpath.nothing_found": "Nem <PERSON>ható egyezés %s elemmel", "arguments.nbtpath.too_deep": "A kapott NBT elemei túl mélyen vannak egymásba ágyazva", "arguments.nbtpath.too_large": "A kapott NBT túl nagy", "arguments.objective.notFound": "Ismeretlen pontozási cél: '%s'", "arguments.objective.readonly": "A(z) '%s' pontozási cél csak olvasható", "arguments.operation.div0": "<PERSON><PERSON><PERSON><PERSON> nem lehet o<PERSON>", "arguments.operation.invalid": "Érvénytelen <PERSON>", "arguments.swizzle.invalid": "É<PERSON><PERSON><PERSON><PERSON><PERSON> (swizzle): csak 'x', 'y' és 'z' s<PERSON><PERSON><PERSON><PERSON><PERSON> benne", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "vértezet", "attribute.name.armor_toughness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.attack_damage": "sebzés", "attribute.name.attack_knockback": "hátralökés", "attribute.name.attack_speed": "t<PERSON><PERSON><PERSON><PERSON>", "attribute.name.block_break_speed": "blokktö<PERSON><PERSON>", "attribute.name.block_interaction_range": "Blokk-interakciós hatótávolság", "attribute.name.burning_time": "égési <PERSON>", "attribute.name.camera_distance": "Kamera Távolság", "attribute.name.entity_interaction_range": "Entitáns-interakciós hatótávolság", "attribute.name.explosion_knockback_resistance": "Robbantási hátralökéssel szembeni ellenállás", "attribute.name.fall_damage_multiplier": "Lehullási sebzés sokszorozó", "attribute.name.flying_speed": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.follow_range": "Követési távolság", "attribute.name.generic.armor": "vértezet", "attribute.name.generic.armor_toughness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_damage": "sebzés", "attribute.name.generic.attack_knockback": "hátralökés", "attribute.name.generic.attack_speed": "t<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.block_interaction_range": "blokkhasználati <PERSON>ótáv", "attribute.name.generic.burning_time": "É<PERSON><PERSON>", "attribute.name.generic.entity_interaction_range": "Entitás interakció hatótáv", "attribute.name.generic.explosion_knockback_resistance": "Robbantási hátralökéssel szembeni ellenállás", "attribute.name.generic.fall_damage_multiplier": "esésisebzés-szorzó", "attribute.name.generic.flying_speed": "rep<PERSON><PERSON><PERSON>", "attribute.name.generic.follow_range": "követési távolság", "attribute.name.generic.gravity": "<PERSON>ra<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.jump_strength": "ugráserősség", "attribute.name.generic.knockback_resistance": "stabilitás", "attribute.name.generic.luck": "szerencse", "attribute.name.generic.max_absorption": "maximum elnyelés", "attribute.name.generic.max_health": "maximum életpont", "attribute.name.generic.movement_efficiency": "Mozgási hatékonyság", "attribute.name.generic.movement_speed": "sebesség", "attribute.name.generic.oxygen_bonus": "oxigénb<PERSON><PERSON><PERSON>", "attribute.name.generic.safe_fall_distance": "biztonságos esési ma<PERSON>", "attribute.name.generic.scale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.step_height": "lépésmagasság", "attribute.name.generic.water_movement_efficiency": "Vízbéli mozgási hatékonyság", "attribute.name.gravity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.horse.jump_strength": "lóugrás-erősség", "attribute.name.jump_strength": "Ugráserősség", "attribute.name.knockback_resistance": "Lök<PERSON><PERSON>", "attribute.name.luck": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.max_absorption": "<PERSON><PERSON><PERSON>", "attribute.name.max_health": "<PERSON><PERSON><PERSON>", "attribute.name.mining_efficiency": "Bányászási hatékonyság", "attribute.name.movement_efficiency": "Mozgási hatékonyság", "attribute.name.movement_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.oxygen_bonus": "Oxigén-b<PERSON><PERSON>z", "attribute.name.player.block_break_speed": "Blokktörési g<PERSON>ág", "attribute.name.player.block_interaction_range": "Blokk-interakciós hatótávolság", "attribute.name.player.entity_interaction_range": "entitáshasználati hatótáv", "attribute.name.player.mining_efficiency": "bányászási hatékonyság", "attribute.name.player.sneaking_speed": "lopakodási se<PERSON>ég", "attribute.name.player.submerged_mining_speed": "víz alatti á<PERSON>ási se<PERSON>g", "attribute.name.player.sweeping_damage_ratio": "Suhintási sebzésarány", "attribute.name.safe_fall_distance": "Veszélytelen lehullási távolság", "attribute.name.scale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.sneaking_speed": "Lopakodási se<PERSON>ég", "attribute.name.spawn_reinforcements": "Élőhalott utánpótlás", "attribute.name.step_height": "Lépésmagasság", "attribute.name.submerged_mining_speed": "Elemrült <PERSON>i g<PERSON>", "attribute.name.sweeping_damage_ratio": "Suhintási sebzésarány", "attribute.name.tempt_range": "Csalogatási hatótávolság", "attribute.name.water_movement_efficiency": "Vízbéli m<PERSON>áshatékonyság", "attribute.name.waypoint_receive_range": "Waypoint Fogadási Távolság", "attribute.name.waypoint_transmit_range": "Waypoint Átviteli Távolság", "attribute.name.zombie.spawn_reinforcements": "zombi-erős<PERSON><PERSON>s", "biome.minecraft.badlands": "Badland", "biome.minecraft.bamboo_jungle": "Bambuszdzsungel", "biome.minecraft.basalt_deltas": "Bazaltdelta", "biome.minecraft.beach": "Tengerpar<PERSON>", "biome.minecraft.birch_forest": "Nyírfaerdő", "biome.minecraft.cherry_grove": "Cseresznyeliget", "biome.minecraft.cold_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.crimson_forest": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "biome.minecraft.dark_forest": "Sötét erdő", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.desert": "Sivatag", "biome.minecraft.dripstone_caves": "Cseppkőbarlangok", "biome.minecraft.end_barrens": "End-pusztaság", "biome.minecraft.end_highlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.end_midlands": "End-k<PERSON>zépföld", "biome.minecraft.eroded_badlands": "Lepusztult badland", "biome.minecraft.flower_forest": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_ocean": "Befagyott óceán", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_river": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.grove": "Liget", "biome.minecraft.ice_spikes": "Jégtüskék", "biome.minecraft.jagged_peaks": "Csipkézett hegycsúcsok", "biome.minecraft.jungle": "<PERSON><PERSON><PERSON>gel", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON>", "biome.minecraft.mangrove_swamp": "Mangrovemocsár", "biome.minecraft.meadow": "<PERSON><PERSON><PERSON>", "biome.minecraft.mushroom_fields": "Gombamező", "biome.minecraft.nether_wastes": "Netherpusztaság", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "Nyírfaőserdő", "biome.minecraft.old_growth_pine_taiga": "Ősfenyves", "biome.minecraft.old_growth_spruce_taiga": "Mamutfenyő-őserdő", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON><PERSON> liget", "biome.minecraft.plains": "Mező", "biome.minecraft.river": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna": "Szavanna", "biome.minecraft.savanna_plateau": "Szavannás fen<PERSON>", "biome.minecraft.small_end_islands": "<PERSON><PERSON>-szigetek", "biome.minecraft.snowy_beach": "<PERSON><PERSON>", "biome.minecraft.snowy_plains": "Havas mezők", "biome.minecraft.snowy_slopes": "<PERSON><PERSON> he<PERSON>", "biome.minecraft.snowy_taiga": "<PERSON><PERSON> tajga", "biome.minecraft.soul_sand_valley": "Lélekhomokvölgy", "biome.minecraft.sparse_jungle": "Ritkás dzsungel", "biome.minecraft.stony_peaks": "Sziklás hegycsúcsok", "biome.minecraft.stony_shore": "<PERSON>zi<PERSON><PERSON><PERSON>", "biome.minecraft.sunflower_plains": "Napraforgómező", "biome.minecraft.swamp": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.taiga": "<PERSON><PERSON><PERSON>", "biome.minecraft.the_end": "Az End", "biome.minecraft.the_void": "Az Űr", "biome.minecraft.warm_ocean": "<PERSON>eg <PERSON>", "biome.minecraft.warped_forest": "<PERSON><PERSON>", "biome.minecraft.windswept_forest": "Szélfútta erdő", "biome.minecraft.windswept_gravelly_hills": "Szélfútta üledékes dombok", "biome.minecraft.windswept_hills": "Szélfútta dombok", "biome.minecraft.windswept_savanna": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "biome.minecraft.wooded_badlands": "<PERSON><PERSON><PERSON><PERSON> badland", "block.minecraft.acacia_button": "Aká<PERSON><PERSON> gomb", "block.minecraft.acacia_door": "Akáciafa ajtó", "block.minecraft.acacia_fence": "Akáciafa <PERSON>", "block.minecraft.acacia_fence_gate": "Akáciafa kerí<PERSON>pu", "block.minecraft.acacia_hanging_sign": "Akáciafa függőtábla", "block.minecraft.acacia_leaves": "Akácialevelek", "block.minecraft.acacia_log": "Akáciarönk", "block.minecraft.acacia_planks": "Akáciadeszka", "block.minecraft.acacia_pressure_plate": "Akáciafa nyomólap", "block.minecraft.acacia_sapling": "Akáciacsemete", "block.minecraft.acacia_sign": "Akáciafa tábla", "block.minecraft.acacia_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.acacia_stairs": "Akáciafa lépcső", "block.minecraft.acacia_trapdoor": "Akáciafa csapóajtó", "block.minecraft.acacia_wall_hanging_sign": "Akáciafa fali függőtábla", "block.minecraft.acacia_wall_sign": "Akáciafa falitábla", "block.minecraft.acacia_wood": "Akáciablokk", "block.minecraft.activator_rail": "Aktiválósín", "block.minecraft.air": "Levegő", "block.minecraft.allium": "Hagymavirág", "block.minecraft.amethyst_block": "Ametisztblokk", "block.minecraft.amethyst_cluster": "Ametiszt-kristálycsoport", "block.minecraft.ancient_debris": "Ősi törmelék", "block.minecraft.andesite": "Andezit", "block.minecraft.andesite_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.andesite_stairs": "Andezitlépcső", "block.minecraft.andesite_wall": "Andezitfal", "block.minecraft.anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Csatlakozott dinnyeszár", "block.minecraft.attached_pumpkin_stem": "Csatlakozott tökszár", "block.minecraft.azalea": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azalea_leaves": "Azálealevelek", "block.minecraft.azure_bluet": "Kékpárna", "block.minecraft.bamboo": "Bambusz", "block.minecraft.bamboo_block": "Bambuszblokk", "block.minecraft.bamboo_button": "Bambuszgomb", "block.minecraft.bamboo_door": "Bambuszajtó", "block.minecraft.bamboo_fence": "Bambuszkerítés", "block.minecraft.bamboo_fence_gate": "Bambusz kerí<PERSON>", "block.minecraft.bamboo_hanging_sign": "Bambusz függőtábla", "block.minecraft.bamboo_mosaic": "Bambuszmozaik", "block.minecraft.bamboo_mosaic_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-lap", "block.minecraft.bamboo_mosaic_stairs": "Bambuszmozaik-lépcső", "block.minecraft.bamboo_planks": "Bambuszdeszka", "block.minecraft.bamboo_pressure_plate": "Bambusz nyomólap", "block.minecraft.bamboo_sapling": "Bambuszhajtás", "block.minecraft.bamboo_sign": "Bambusztábla", "block.minecraft.bamboo_slab": "Bambuszlap", "block.minecraft.bamboo_stairs": "Bambuszlépcső", "block.minecraft.bamboo_trapdoor": "Bambusz csapóajtó", "block.minecraft.bamboo_wall_hanging_sign": "Bambusz fali függőtábla", "block.minecraft.bamboo_wall_sign": "Bambusz falitábla", "block.minecraft.banner.base.black": "Teljesen fekete mező", "block.minecraft.banner.base.blue": "Teljesen kék mező", "block.minecraft.banner.base.brown": "<PERSON><PERSON><PERSON> barna mez<PERSON>", "block.minecraft.banner.base.cyan": "Teljesen türkiz mező", "block.minecraft.banner.base.gray": "Teljesen szürke mező", "block.minecraft.banner.base.green": "<PERSON><PERSON><PERSON> z<PERSON>ld me<PERSON>", "block.minecraft.banner.base.light_blue": "Teljesen világoskék mező", "block.minecraft.banner.base.light_gray": "Teljesen világosszürke mező", "block.minecraft.banner.base.lime": "Teljesen világoszöld mező", "block.minecraft.banner.base.magenta": "<PERSON><PERSON>sen b<PERSON>bor <PERSON>", "block.minecraft.banner.base.orange": "Teljesen narancssárga mező", "block.minecraft.banner.base.pink": "Teljesen rózsaszín <PERSON>z<PERSON>", "block.minecraft.banner.base.purple": "Teljesen lila mez<PERSON>", "block.minecraft.banner.base.red": "Teljesen vörös <PERSON>", "block.minecraft.banner.base.white": "<PERSON><PERSON><PERSON> feh<PERSON>", "block.minecraft.banner.base.yellow": "<PERSON><PERSON>sen s<PERSON> mez<PERSON>", "block.minecraft.banner.border.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.brown": "<PERSON><PERSON>", "block.minecraft.banner.border.cyan": "Türkizkék ráma", "block.minecraft.banner.border.gray": "Szürke ráma", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "Világoskék ráma", "block.minecraft.banner.border.light_gray": "Világosszürke ráma", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.orange": "Narancssárga rá<PERSON>", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.purple": "<PERSON>", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.black": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON> t<PERSON>a", "block.minecraft.banner.bricks.cyan": "Türkizkék téglaminta", "block.minecraft.banner.bricks.gray": "Szürke téglaminta", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.light_blue": "Világoskék téglaminta", "block.minecraft.banner.bricks.light_gray": "Világosszürke téglaminta", "block.minecraft.banner.bricks.lime": "Vil<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.orange": "Narancssárga téglaminta", "block.minecraft.banner.bricks.pink": "Róz<PERSON><PERSON><PERSON> téglaminta", "block.minecraft.banner.bricks.purple": "<PERSON> t<PERSON>", "block.minecraft.banner.bricks.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON> tég<PERSON>a", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "Türkizkék lepényes", "block.minecraft.banner.circle.gray": "Szürke lepényes", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "Világoskék lepényes", "block.minecraft.banner.circle.light_gray": "Világosszürke lepényes", "block.minecraft.banner.circle.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.orange": "Narancssá<PERSON>", "block.minecraft.banner.circle.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.purple": "<PERSON>", "block.minecraft.banner.circle.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.black": "<PERSON><PERSON><PERSON> creeper c<PERSON><PERSON>k<PERSON><PERSON>", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON><PERSON> creeper c<PERSON><PERSON>k<PERSON><PERSON>", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON> creeper c<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.cyan": "Türkizkék creeper címerkép", "block.minecraft.banner.creeper.gray": "Szürke creeper címerkép", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON> creeper c<PERSON><PERSON>k<PERSON><PERSON>", "block.minecraft.banner.creeper.light_blue": "Világoskék creeper címerkép", "block.minecraft.banner.creeper.light_gray": "Világosszürke creeper címerkép", "block.minecraft.banner.creeper.lime": "Világos<PERSON><PERSON><PERSON> creeper címerkép", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON> creeper c<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.orange": "Narancssárga creeper címerk<PERSON>p", "block.minecraft.banner.creeper.pink": "R<PERSON>z<PERSON><PERSON><PERSON> creeper címerkép", "block.minecraft.banner.creeper.purple": "<PERSON> creeper c<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>er c<PERSON><PERSON>", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON><PERSON> creeper c<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON> creeper c<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "Türkizkék harántkereszt", "block.minecraft.banner.cross.gray": "Szürke harántkereszt", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_blue": "Világoskék harántkereszt", "block.minecraft.banner.cross.light_gray": "Világosszürke harántkereszt", "block.minecraft.banner.cross.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.orange": "Narancssárga <PERSON>", "block.minecraft.banner.cross.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.purple": "<PERSON>", "block.minecraft.banner.cross.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.black": "<PERSON>kete fog<PERSON> ráma", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON> rá<PERSON>", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON> rá<PERSON>", "block.minecraft.banner.curly_border.cyan": "Türkizkék fogas ráma", "block.minecraft.banner.curly_border.gray": "Szürke fogas ráma", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.light_blue": "Világoskék fogas ráma", "block.minecraft.banner.curly_border.light_gray": "Világosszürke fogas ráma", "block.minecraft.banner.curly_border.lime": "V<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.orange": "Narancssárga <PERSON> ráma", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>as rá<PERSON>", "block.minecraft.banner.curly_border.purple": "<PERSON> r<PERSON>", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.banner.diagonal_left.black": "Feketével balhar<PERSON>lt", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.diagonal_left.cyan": "Türkizk<PERSON><PERSON><PERSON> balharán<PERSON>lt", "block.minecraft.banner.diagonal_left.gray": "Szürkével balharántolt", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.diagonal_left.light_blue": "Világosk<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.light_gray": "Világosszürkével balharántolt", "block.minecraft.banner.diagonal_left.lime": "Világoszö<PERSON><PERSON> balhar<PERSON>lt", "block.minecraft.banner.diagonal_left.magenta": "Bíborral balharántolt", "block.minecraft.banner.diagonal_left.orange": "Naran<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.pink": "Rózsaszínnel balharán<PERSON>lt", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.red": "V<PERSON>r<PERSON><PERSON> balharántolt", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.cyan": "Türkizk<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.gray": "Szürkével <PERSON>", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.light_blue": "Világosk<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.light_gray": "Világosszürk<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.lime": "Világosz<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.magenta": "Bí<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.orange": "<PERSON>ran<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.pink": "Rózsasz<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> har<PERSON>", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.black": "Feketével al<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.cyan": "Türkizk<PERSON><PERSON><PERSON> al<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.gray": "Szürkével al<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.light_blue": "Világosk<PERSON><PERSON><PERSON> al<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.light_gray": "Világosszürkével alul<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.lime": "Világoszöld<PERSON> al<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.magenta": "Bíborral alul<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.orange": "Naran<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.pink": "Rózsaszínnel al<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.white": "<PERSON><PERSON><PERSON><PERSON> al<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.black": "Feketével alul<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.cyan": "Türkizk<PERSON><PERSON><PERSON> al<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.diagonal_up_right.gray": "Szürkével alulr<PERSON><PERSON> b<PERSON>", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.diagonal_up_right.light_blue": "Világosk<PERSON><PERSON><PERSON> al<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.diagonal_up_right.light_gray": "Világosszürkével alulról b<PERSON>", "block.minecraft.banner.diagonal_up_right.lime": "Világoszöld<PERSON> al<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.diagonal_up_right.magenta": "Bíborral alulról bal<PERSON>lt", "block.minecraft.banner.diagonal_up_right.orange": "Naran<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.pink": "Rózsaszínnel alulr<PERSON>l balhar<PERSON>lt", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.black": "Fe<PERSON><PERSON> örvény", "block.minecraft.banner.flow.blue": "<PERSON><PERSON>k örv<PERSON>y", "block.minecraft.banner.flow.brown": "<PERSON><PERSON>", "block.minecraft.banner.flow.cyan": "Türkizkék örvény", "block.minecraft.banner.flow.gray": "Szürke örvény", "block.minecraft.banner.flow.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.light_blue": "Világoskék örvény", "block.minecraft.banner.flow.light_gray": "Világosszürke örvény", "block.minecraft.banner.flow.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.orange": "Narancssárga <PERSON>", "block.minecraft.banner.flow.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.purple": "<PERSON>", "block.minecraft.banner.flow.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.black": "Fekete v<PERSON>", "block.minecraft.banner.flower.blue": "<PERSON>ék virág <PERSON>", "block.minecraft.banner.flower.brown": "<PERSON><PERSON> v<PERSON>", "block.minecraft.banner.flower.cyan": "Türkizkék virág címerkép", "block.minecraft.banner.flower.gray": "Szürke virág cí<PERSON>kép", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.light_blue": "Világoskék virág címerkép", "block.minecraft.banner.flower.light_gray": "Világosszürke virág címerkép", "block.minecraft.banner.flower.lime": "Világoszöld virág cí<PERSON>kép", "block.minecraft.banner.flower.magenta": "Bíbor v<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.orange": "Narancssárga virág címerkép", "block.minecraft.banner.flower.pink": "Rózsa<PERSON><PERSON> virág címerkép", "block.minecraft.banner.flower.purple": "<PERSON> v<PERSON>", "block.minecraft.banner.flower.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON><PERSON> v<PERSON><PERSON>", "block.minecraft.banner.globe.black": "Fekete földgömb", "block.minecraft.banner.globe.blue": "Kék földgömb", "block.minecraft.banner.globe.brown": "<PERSON><PERSON>", "block.minecraft.banner.globe.cyan": "Türkizkék földgömb", "block.minecraft.banner.globe.gray": "Szürke földgömb", "block.minecraft.banner.globe.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.light_blue": "Világoskék földgömb", "block.minecraft.banner.globe.light_gray": "Világosszürke földgömb", "block.minecraft.banner.globe.lime": "Világoszöld fö<PERSON>ö<PERSON>", "block.minecraft.banner.globe.magenta": "Bíbor f<PERSON>", "block.minecraft.banner.globe.orange": "Narancssárga földgömb", "block.minecraft.banner.globe.pink": "Rózsa<PERSON><PERSON> földgömb", "block.minecraft.banner.globe.purple": "<PERSON>", "block.minecraft.banner.globe.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.yellow": "<PERSON><PERSON><PERSON> f<PERSON>", "block.minecraft.banner.gradient.black": "Fekete színátmenet", "block.minecraft.banner.gradient.blue": "Kék színátmenet", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "Türkizkék színátmenet", "block.minecraft.banner.gradient.gray": "Szürke színátmenet", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_blue": "Világoskék színátmenet", "block.minecraft.banner.gradient.light_gray": "Világosszürke színátmenet", "block.minecraft.banner.gradient.lime": "Vil<PERSON><PERSON><PERSON><PERSON><PERSON>átmenet", "block.minecraft.banner.gradient.magenta": "Bíbor <PERSON>á<PERSON>men<PERSON>", "block.minecraft.banner.gradient.orange": "Narancssárga színátmenet", "block.minecraft.banner.gradient.pink": "Rózsa<PERSON><PERSON> színátmenet", "block.minecraft.banner.gradient.purple": "<PERSON>", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON>átmen<PERSON>", "block.minecraft.banner.gradient_up.black": "Fekete színátmenet alulról", "block.minecraft.banner.gradient_up.blue": "Kék színátmenet alulról", "block.minecraft.banner.gradient_up.brown": "<PERSON>na s<PERSON>ínátmenet alulról", "block.minecraft.banner.gradient_up.cyan": "Türkizkék színátmenet alulról", "block.minecraft.banner.gradient_up.gray": "Szürke színátmenet alulról", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON>átmenet alulról", "block.minecraft.banner.gradient_up.light_blue": "Világoskék színátmenet alulról", "block.minecraft.banner.gradient_up.light_gray": "Világosszürke színátmenet alulról", "block.minecraft.banner.gradient_up.lime": "Világos<PERSON><PERSON>ld színátmenet alulról", "block.minecraft.banner.gradient_up.magenta": "Bíbor színátmenet alulról", "block.minecraft.banner.gradient_up.orange": "Narancssárga színátmenet alulról", "block.minecraft.banner.gradient_up.pink": "Rózsaszín színátmenet alulról", "block.minecraft.banner.gradient_up.purple": "<PERSON>átmenet alulró<PERSON>", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>ulró<PERSON>", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>et <PERSON>ul<PERSON>ó<PERSON>", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON>rga színátmenet alulról", "block.minecraft.banner.guster.black": "Fekete széllökő", "block.minecraft.banner.guster.blue": "Kék széllökő", "block.minecraft.banner.guster.brown": "<PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "Türkizkék széllökő", "block.minecraft.banner.guster.gray": "Szürke széllökő", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_blue": "Világoskék széllökő", "block.minecraft.banner.guster.light_gray": "Világosszürke széllökő", "block.minecraft.banner.guster.lime": "Világoszöld széllökő", "block.minecraft.banner.guster.magenta": "Bíbor széllökő", "block.minecraft.banner.guster.orange": "Narancssárga széllökő", "block.minecraft.banner.guster.pink": "Rózsa<PERSON><PERSON> sz<PERSON>llök<PERSON>", "block.minecraft.banner.guster.purple": "<PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.yellow": "S<PERSON>rga széllökő", "block.minecraft.banner.half_horizontal.black": "Feketével vágott", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.cyan": "Türkizkék<PERSON> vágott", "block.minecraft.banner.half_horizontal.gray": "Szürkével vágott", "block.minecraft.banner.half_horizontal.green": "Zölddel vágott", "block.minecraft.banner.half_horizontal.light_blue": "Világosk<PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.half_horizontal.light_gray": "Világosszürkével vágott", "block.minecraft.banner.half_horizontal.lime": "Világoszölddel vá<PERSON>t", "block.minecraft.banner.half_horizontal.magenta": "Bíborral vágott", "block.minecraft.banner.half_horizontal.orange": "Narancs<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.pink": "Rózsaszínnel vágott", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.red": "Vörössel vágott", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.black": "Feketével alulról vágott", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.cyan": "Türkizkékkel alulról vágott", "block.minecraft.banner.half_horizontal_bottom.gray": "Szürkével alulról vágott", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON><PERSON>del alulról vágott", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Világosk<PERSON><PERSON><PERSON> alulról vá<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Világosszürkével alulról vágott", "block.minecraft.banner.half_horizontal_bottom.lime": "Világoszölddel alulról vágott", "block.minecraft.banner.half_horizontal_bottom.magenta": "Bíborral alulról vágott", "block.minecraft.banner.half_horizontal_bottom.orange": "Narancs<PERSON><PERSON><PERSON><PERSON><PERSON>ó<PERSON> v<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.pink": "Rózsaszínnel alulról vágott", "block.minecraft.banner.half_horizontal_bottom.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.red": "Vörössel alulról v<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.black": "Feketével hasított", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.cyan": "Türkizk<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.gray": "Szürkével hasított", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.light_blue": "Világos<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.light_gray": "Világosszürkével hasított", "block.minecraft.banner.half_vertical.lime": "Világoszöld<PERSON>", "block.minecraft.banner.half_vertical.magenta": "Bíborral hasított", "block.minecraft.banner.half_vertical.orange": "<PERSON>ran<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.pink": "Rózsaszínnel <PERSON>tt", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON>r<PERSON><PERSON> hasított", "block.minecraft.banner.half_vertical.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.black": "Feketével b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.cyan": "Türkizk<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>tt", "block.minecraft.banner.half_vertical_right.gray": "Szürkével balr<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "Világosk<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.light_gray": "Világosszürkével balr<PERSON>l <PERSON>", "block.minecraft.banner.half_vertical_right.lime": "Világoszöld<PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.magenta": "Bíborral balról has<PERSON>tt", "block.minecraft.banner.half_vertical_right.orange": "Narancs<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.pink": "Rózsaszínnel balról <PERSON>tt", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON><PERSON>í<PERSON>p", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "Türkizkék Mojang címerkép", "block.minecraft.banner.mojang.gray": "Szürke Mojang címerkép", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "Világoskék Mojang címerkép", "block.minecraft.banner.mojang.light_gray": "Világosszürke Mojang címerkép", "block.minecraft.banner.mojang.lime": "Vil<PERSON><PERSON><PERSON><PERSON><PERSON>í<PERSON>ké<PERSON>", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.orange": "Narancssárga <PERSON>í<PERSON>", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ímerkép", "block.minecraft.banner.mojang.purple": "<PERSON>", "block.minecraft.banner.mojang.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.black": "Fekete disznóorr", "block.minecraft.banner.piglin.blue": "Kék disznóorr", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.gray": "Szürke disznóorr", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.light_blue": "Világoskék disznóorr", "block.minecraft.banner.piglin.light_gray": "Világosszürke disznóorr", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ó<PERSON>", "block.minecraft.banner.piglin.magenta": "Bíbor disznóorr", "block.minecraft.banner.piglin.orange": "Narancssárga disznóorr", "block.minecraft.banner.piglin.pink": "R<PERSON>z<PERSON><PERSON><PERSON> disznóorr", "block.minecraft.banner.piglin.purple": "<PERSON>", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON><PERSON> disznóorr", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.blue": "Kék nagyruta", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON> na<PERSON>", "block.minecraft.banner.rhombus.cyan": "Türkizkék nagyruta", "block.minecraft.banner.rhombus.gray": "Szürke nagyruta", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_blue": "Világoskék nagyruta", "block.minecraft.banner.rhombus.light_gray": "Világosszürke nagyruta", "block.minecraft.banner.rhombus.lime": "Világoszöld nagyruta", "block.minecraft.banner.rhombus.magenta": "Bíbor nagyruta", "block.minecraft.banner.rhombus.orange": "Narancssárga nagyruta", "block.minecraft.banner.rhombus.pink": "Rózsasz<PERSON> nagyruta", "block.minecraft.banner.rhombus.purple": "<PERSON>", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON> na<PERSON>", "block.minecraft.banner.skull.black": "Fekete kop<PERSON>a <PERSON>í<PERSON>k<PERSON>", "block.minecraft.banner.skull.blue": "Kék koponya címerkép", "block.minecraft.banner.skull.brown": "<PERSON><PERSON> kop<PERSON>a <PERSON>", "block.minecraft.banner.skull.cyan": "Türkizkék koponya címerkép", "block.minecraft.banner.skull.gray": "Szürke koponya címerkép", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON>í<PERSON>k<PERSON>", "block.minecraft.banner.skull.light_blue": "Világoskék koponya címerkép", "block.minecraft.banner.skull.light_gray": "Világosszürke koponya címerkép", "block.minecraft.banner.skull.lime": "Világoszöld koponya címerkép", "block.minecraft.banner.skull.magenta": "Bíbor kop<PERSON>a <PERSON>í<PERSON>ké<PERSON>", "block.minecraft.banner.skull.orange": "Narancssárga koponya címerkép", "block.minecraft.banner.skull.pink": "Rózsaszín koponya címerkép", "block.minecraft.banner.skull.purple": "<PERSON> k<PERSON>", "block.minecraft.banner.skull.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON><PERSON> koponya címerké<PERSON>", "block.minecraft.banner.small_stripes.black": "Feketével cölöpölt", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.cyan": "Türkizkék<PERSON> cölöpölt", "block.minecraft.banner.small_stripes.gray": "Szürkével cölöpölt", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON><PERSON> cölöpölt", "block.minecraft.banner.small_stripes.light_blue": "Világoské<PERSON><PERSON> cölöpölt", "block.minecraft.banner.small_stripes.light_gray": "Világosszürkével cölöpölt", "block.minecraft.banner.small_stripes.lime": "Világoszölddel cölöpölt", "block.minecraft.banner.small_stripes.magenta": "Bíborral cölöpölt", "block.minecraft.banner.small_stripes.orange": "Naran<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.pink": "Rózsaszínnel cölöpölt", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.red": "Vörössel cölöpölt", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.blue": "<PERSON><PERSON><PERSON> alsótelek", "block.minecraft.banner.square_bottom_left.brown": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.cyan": "Türkizkék alsótelek", "block.minecraft.banner.square_bottom_left.gray": "Szürke alsótelek", "block.minecraft.banner.square_bottom_left.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.light_blue": "Világoskék alsótelek", "block.minecraft.banner.square_bottom_left.light_gray": "Világosszürke alsótelek", "block.minecraft.banner.square_bottom_left.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.orange": "Narancssárga <PERSON>ek", "block.minecraft.banner.square_bottom_left.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.purple": "<PERSON>", "block.minecraft.banner.square_bottom_left.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.black": "Fekete bal-alsótelek", "block.minecraft.banner.square_bottom_right.blue": "<PERSON><PERSON>k bal-alsótelek", "block.minecraft.banner.square_bottom_right.brown": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.cyan": "Türkizkék bal-alsótelek", "block.minecraft.banner.square_bottom_right.gray": "Szürke bal-alsótelek", "block.minecraft.banner.square_bottom_right.green": "<PERSON><PERSON><PERSON> bal-als<PERSON>telek", "block.minecraft.banner.square_bottom_right.light_blue": "Világoskék bal-alsótelek", "block.minecraft.banner.square_bottom_right.light_gray": "Világosszürke bal-alsótelek", "block.minecraft.banner.square_bottom_right.lime": "Világosz<PERSON>ld bal-alsótelek", "block.minecraft.banner.square_bottom_right.magenta": "Bíbor bal-<PERSON><PERSON><PERSON>telek", "block.minecraft.banner.square_bottom_right.orange": "Narancssárga bal-alsótelek", "block.minecraft.banner.square_bottom_right.pink": "R<PERSON><PERSON><PERSON><PERSON><PERSON> bal-alsótelek", "block.minecraft.banner.square_bottom_right.purple": "<PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.white": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.yellow": "<PERSON><PERSON><PERSON> bal-<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_left.black": "Fekete telek", "block.minecraft.banner.square_top_left.blue": "Kék telek", "block.minecraft.banner.square_top_left.brown": "Barna telek", "block.minecraft.banner.square_top_left.cyan": "Türkizkék telek", "block.minecraft.banner.square_top_left.gray": "Szürke telek", "block.minecraft.banner.square_top_left.green": "<PERSON><PERSON><PERSON> telek", "block.minecraft.banner.square_top_left.light_blue": "Világoskék telek", "block.minecraft.banner.square_top_left.light_gray": "Világosszürke telek", "block.minecraft.banner.square_top_left.lime": "Világoszöld telek", "block.minecraft.banner.square_top_left.magenta": "Bíbor telek", "block.minecraft.banner.square_top_left.orange": "Narancssárga telek", "block.minecraft.banner.square_top_left.pink": "Rózsaszín telek", "block.minecraft.banner.square_top_left.purple": "<PERSON> telek", "block.minecraft.banner.square_top_left.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> telek", "block.minecraft.banner.square_top_left.white": "<PERSON><PERSON><PERSON><PERSON> telek", "block.minecraft.banner.square_top_left.yellow": "S<PERSON>rga telek", "block.minecraft.banner.square_top_right.black": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.square_top_right.blue": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.square_top_right.brown": "<PERSON><PERSON>", "block.minecraft.banner.square_top_right.cyan": "Türkizkék baltelek", "block.minecraft.banner.square_top_right.gray": "Szürke baltelek", "block.minecraft.banner.square_top_right.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_right.light_blue": "Világoskék baltelek", "block.minecraft.banner.square_top_right.light_gray": "Világosszürke baltelek", "block.minecraft.banner.square_top_right.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.banner.square_top_right.magenta": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.square_top_right.orange": "Narancss<PERSON><PERSON> baltelek", "block.minecraft.banner.square_top_right.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bal<PERSON>", "block.minecraft.banner.square_top_right.purple": "<PERSON> b<PERSON>", "block.minecraft.banner.square_top_right.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_right.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_right.yellow": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "block.minecraft.banner.straight_cross.black": "Fekete kere<PERSON>t", "block.minecraft.banner.straight_cross.blue": "<PERSON>ék k<PERSON>", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.cyan": "Türkizkék kereszt", "block.minecraft.banner.straight_cross.gray": "Szürke kereszt", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_blue": "Világoskék kereszt", "block.minecraft.banner.straight_cross.light_gray": "Világosszürke kereszt", "block.minecraft.banner.straight_cross.lime": "Vil<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.orange": "Narancssárga kereszt", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.straight_cross.purple": "<PERSON>", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON><PERSON> talp", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON><PERSON> talp", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON> talp", "block.minecraft.banner.stripe_bottom.cyan": "Türkizkék talp", "block.minecraft.banner.stripe_bottom.gray": "Szürke talp", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON> talp", "block.minecraft.banner.stripe_bottom.light_blue": "Világoskék talp", "block.minecraft.banner.stripe_bottom.light_gray": "Világosszürke talp", "block.minecraft.banner.stripe_bottom.lime": "Világoszöld talp", "block.minecraft.banner.stripe_bottom.magenta": "Bíbor talp", "block.minecraft.banner.stripe_bottom.orange": "Narancssárga talp", "block.minecraft.banner.stripe_bottom.pink": "Róz<PERSON><PERSON><PERSON> talp", "block.minecraft.banner.stripe_bottom.purple": "<PERSON> talp", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> talp", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON><PERSON> talp", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON><PERSON> talp", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "Türkizkék cölöp", "block.minecraft.banner.stripe_center.gray": "Szürke cölöp", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_blue": "Világoskék cölöp", "block.minecraft.banner.stripe_center.light_gray": "Világosszürke cölöp", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.orange": "Narancssárga <PERSON>", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.purple": "<PERSON>", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.black": "Fekete balharántpólya", "block.minecraft.banner.stripe_downleft.blue": "K<PERSON>k balharántpólya", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.cyan": "Türkizkék balharántpólya", "block.minecraft.banner.stripe_downleft.gray": "Szürke balharántpólya", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_blue": "Világoskék balharántpólya", "block.minecraft.banner.stripe_downleft.light_gray": "Világosszürke balharántpólya", "block.minecraft.banner.stripe_downleft.lime": "Világos<PERSON><PERSON><PERSON> balharántpólya", "block.minecraft.banner.stripe_downleft.magenta": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.stripe_downleft.orange": "Narancssárga balharántpólya", "block.minecraft.banner.stripe_downleft.pink": "R<PERSON><PERSON><PERSON><PERSON><PERSON> balharántpólya", "block.minecraft.banner.stripe_downleft.purple": "<PERSON>", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON><PERSON> b<PERSON>harán<PERSON>pólya", "block.minecraft.banner.stripe_downright.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.cyan": "Türkizkék harántpólya", "block.minecraft.banner.stripe_downright.gray": "Szürke harántpólya", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.light_blue": "Világoskék harántpólya", "block.minecraft.banner.stripe_downright.light_gray": "Világosszürke harántpólya", "block.minecraft.banner.stripe_downright.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.orange": "Narancssárga <PERSON>", "block.minecraft.banner.stripe_downright.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.purple": "<PERSON>", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.cyan": "Türkizkék jobb cölöp", "block.minecraft.banner.stripe_left.gray": "Szürke jobb <PERSON>", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.light_blue": "Világoskék jobb <PERSON>", "block.minecraft.banner.stripe_left.light_gray": "Világosszürke jobb <PERSON>", "block.minecraft.banner.stripe_left.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.orange": "Narancss<PERSON><PERSON>", "block.minecraft.banner.stripe_left.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.purple": "<PERSON>", "block.minecraft.banner.stripe_left.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.black": "Fekete csík", "block.minecraft.banner.stripe_middle.blue": "Kék csík", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON> c<PERSON>", "block.minecraft.banner.stripe_middle.cyan": "Türkizkék csík", "block.minecraft.banner.stripe_middle.gray": "Szürke csík", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "Világoskék csík", "block.minecraft.banner.stripe_middle.light_gray": "Világosszürke csík", "block.minecraft.banner.stripe_middle.lime": "Világoszöld csík", "block.minecraft.banner.stripe_middle.magenta": "Bíbor c<PERSON>ík", "block.minecraft.banner.stripe_middle.orange": "Narancssárga csík", "block.minecraft.banner.stripe_middle.pink": "Rózsaszín csík", "block.minecraft.banner.stripe_middle.purple": "<PERSON> c<PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.banner.stripe_middle.yellow": "S<PERSON>rga csík", "block.minecraft.banner.stripe_right.black": "<PERSON><PERSON><PERSON> bal c<PERSON>", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON><PERSON> bal <PERSON>", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON> bal <PERSON>", "block.minecraft.banner.stripe_right.cyan": "Türkizkék bal cölöp", "block.minecraft.banner.stripe_right.gray": "Szürke bal cölöp", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON><PERSON> bal c<PERSON><PERSON>", "block.minecraft.banner.stripe_right.light_blue": "Világoskék bal cölöp", "block.minecraft.banner.stripe_right.light_gray": "Világosszürke bal cölöp", "block.minecraft.banner.stripe_right.lime": "Vil<PERSON><PERSON><PERSON><PERSON><PERSON> bal <PERSON><PERSON>", "block.minecraft.banner.stripe_right.magenta": "<PERSON><PERSON><PERSON> bal <PERSON>", "block.minecraft.banner.stripe_right.orange": "Narancssárga bal c<PERSON>", "block.minecraft.banner.stripe_right.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bal c<PERSON>", "block.minecraft.banner.stripe_right.purple": "<PERSON> bal <PERSON>", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> bal <PERSON>", "block.minecraft.banner.stripe_right.white": "<PERSON><PERSON><PERSON><PERSON> bal <PERSON>", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON><PERSON> bal <PERSON>", "block.minecraft.banner.stripe_top.black": "Fekete fő", "block.minecraft.banner.stripe_top.blue": "Kék fő", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON> fő", "block.minecraft.banner.stripe_top.cyan": "Türkizkék fő", "block.minecraft.banner.stripe_top.gray": "Szürke fő", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_blue": "Világoskék fő", "block.minecraft.banner.stripe_top.light_gray": "Világosszürke fő", "block.minecraft.banner.stripe_top.lime": "Világoszöld fő", "block.minecraft.banner.stripe_top.magenta": "Bíbor fő", "block.minecraft.banner.stripe_top.orange": "Narancssárga fő", "block.minecraft.banner.stripe_top.pink": "Rózsaszín fő", "block.minecraft.banner.stripe_top.purple": "<PERSON> fő", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> fő", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.yellow": "Sárga fő", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON>e fordí<PERSON>tt <PERSON>", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON>k fordított <PERSON>", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "Türkizkék fordított ék", "block.minecraft.banner.triangle_bottom.gray": "Szürke fordított ék", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON> ford<PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "Világoskék fordított ék", "block.minecraft.banner.triangle_bottom.light_gray": "Világosszürke fordított ék", "block.minecraft.banner.triangle_bottom.lime": "Világoszöld fordított ék", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON> for<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.orange": "Narancssárga fordított ék", "block.minecraft.banner.triangle_bottom.pink": "R<PERSON><PERSON><PERSON><PERSON><PERSON> fordított ék", "block.minecraft.banner.triangle_bottom.purple": "<PERSON> for<PERSON>", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON><PERSON> for<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.black": "Fe<PERSON><PERSON>", "block.minecraft.banner.triangle_top.blue": "Kék <PERSON>", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.cyan": "Türkizkék ék", "block.minecraft.banner.triangle_top.gray": "Szürke ék", "block.minecraft.banner.triangle_top.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.light_blue": "Világoskék ék", "block.minecraft.banner.triangle_top.light_gray": "Világosszürke ék", "block.minecraft.banner.triangle_top.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.magenta": "<PERSON>í<PERSON>", "block.minecraft.banner.triangle_top.orange": "Narancssárga <PERSON>", "block.minecraft.banner.triangle_top.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.purple": "<PERSON>", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON><PERSON> fogas talp", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON><PERSON>as talp", "block.minecraft.banner.triangles_bottom.brown": "<PERSON><PERSON> fogas talp", "block.minecraft.banner.triangles_bottom.cyan": "Türkizkék fogas talp", "block.minecraft.banner.triangles_bottom.gray": "Szürke fogas talp", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON><PERSON>as talp", "block.minecraft.banner.triangles_bottom.light_blue": "Világoskék fogas talp", "block.minecraft.banner.triangles_bottom.light_gray": "Világosszürke fogas talp", "block.minecraft.banner.triangles_bottom.lime": "Világos<PERSON><PERSON><PERSON>as talp", "block.minecraft.banner.triangles_bottom.magenta": "<PERSON><PERSON><PERSON> talp", "block.minecraft.banner.triangles_bottom.orange": "Narancssárga fogas talp", "block.minecraft.banner.triangles_bottom.pink": "R<PERSON>z<PERSON><PERSON><PERSON> fogas talp", "block.minecraft.banner.triangles_bottom.purple": "<PERSON> fogas talp", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> talp", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON><PERSON><PERSON> talp", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON><PERSON> fogas talp", "block.minecraft.banner.triangles_top.black": "Fekete fogas fő", "block.minecraft.banner.triangles_top.blue": "Kék fog<PERSON> fő", "block.minecraft.banner.triangles_top.brown": "<PERSON>na fogas fő", "block.minecraft.banner.triangles_top.cyan": "Türkizkék fogas fő", "block.minecraft.banner.triangles_top.gray": "Szürke fogas fő", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON> fő", "block.minecraft.banner.triangles_top.light_blue": "Világoskék fogas fő", "block.minecraft.banner.triangles_top.light_gray": "Világosszürke fogas fő", "block.minecraft.banner.triangles_top.lime": "Világoszöld <PERSON> fő", "block.minecraft.banner.triangles_top.magenta": "Bíbor <PERSON> fő", "block.minecraft.banner.triangles_top.orange": "Narancssárga fogas fő", "block.minecraft.banner.triangles_top.pink": "Rózsaszín fogas fő", "block.minecraft.banner.triangles_top.purple": "<PERSON> fogas fő", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> fő", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON><PERSON> fő", "block.minecraft.banner.triangles_top.yellow": "Sárga fogas fő", "block.minecraft.barrel": "<PERSON><PERSON><PERSON>", "block.minecraft.barrier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.basalt": "Bazalt", "block.minecraft.beacon": "Jelzőfény", "block.minecraft.beacon.primary": "Elsődleges képesség", "block.minecraft.beacon.secondary": "Másodlagos képesség", "block.minecraft.bed.no_sleep": "Csak éjjel vagy viharos időben aludhatsz", "block.minecraft.bed.not_safe": "Most nem pihen<PERSON>, mert szörnyek vannak a közeledben", "block.minecraft.bed.obstructed": "Ez az ágy nem hozzáférhető", "block.minecraft.bed.occupied": "<PERSON>z az ágy <PERSON>t", "block.minecraft.bed.too_far_away": "Most nem p<PERSON>, t<PERSON> van az ágyad", "block.minecraft.bedrock": "Alapkő", "block.minecraft.bee_nest": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beehive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beetroots": "Cékla", "block.minecraft.bell": "Hara<PERSON>", "block.minecraft.big_dripleaf": "<PERSON><PERSON>", "block.minecraft.big_dripleaf_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_button": "Nyí<PERSON><PERSON> gomb", "block.minecraft.birch_door": "Nyírfa ajtó", "block.minecraft.birch_fence": "Nyí<PERSON><PERSON>", "block.minecraft.birch_fence_gate": "Nyírfa <PERSON>", "block.minecraft.birch_hanging_sign": "Nyírfa függőtábla", "block.minecraft.birch_leaves": "Nyírfalevelek", "block.minecraft.birch_log": "Nyírfarönk", "block.minecraft.birch_planks": "Nyírfa desz<PERSON>", "block.minecraft.birch_pressure_plate": "Nyírfa n<PERSON>lap", "block.minecraft.birch_sapling": "Nyírfacsemete", "block.minecraft.birch_sign": "Nyírfa tábla", "block.minecraft.birch_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.birch_stairs": "Nyírfa lépcső", "block.minecraft.birch_trapdoor": "Nyírfa csapóajtó", "block.minecraft.birch_wall_hanging_sign": "Nyírfa fali függőtábla", "block.minecraft.birch_wall_sign": "Nyírfa falitábla", "block.minecraft.birch_wood": "Nyírfablokk", "block.minecraft.black_banner": "Fekete zászló", "block.minecraft.black_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.black_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.black_candle_cake": "Torta fekete gyer<PERSON>", "block.minecraft.black_carpet": "Fekete szőnyeg", "block.minecraft.black_concrete": "<PERSON><PERSON><PERSON> beton", "block.minecraft.black_concrete_powder": "Fekete szárazbeton", "block.minecraft.black_glazed_terracotta": "Fekete mázas kerámia", "block.minecraft.black_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.black_stained_glass": "Fekete festett üveg", "block.minecraft.black_stained_glass_pane": "Fekete festett üveglap", "block.minecraft.black_terracotta": "Fekete terrakotta", "block.minecraft.black_wool": "Fekete gya<PERSON>", "block.minecraft.blackstone": "Feketekő", "block.minecraft.blackstone_slab": "<PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.blackstone_stairs": "Feketekő lépcső", "block.minecraft.blackstone_wall": "Feketekő fal", "block.minecraft.blast_furnace": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_banner": "Kék zászló", "block.minecraft.blue_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle_cake": "<PERSON>ta kék g<PERSON>", "block.minecraft.blue_carpet": "Kék szőnyeg", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON> beton", "block.minecraft.blue_concrete_powder": "Kék szárazbeton", "block.minecraft.blue_glazed_terracotta": "Kék m<PERSON> kerá<PERSON>", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON>k <PERSON>", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON> shulkerdoboz", "block.minecraft.blue_stained_glass": "Kék festett üveg", "block.minecraft.blue_stained_glass_pane": "Kék festett üveglap", "block.minecraft.blue_terracotta": "Kék terrakotta", "block.minecraft.blue_wool": "Kék g<PERSON>", "block.minecraft.bone_block": "Csontblokk", "block.minecraft.bookshelf": "Könyvespolc", "block.minecraft.brain_coral": "Agykorall", "block.minecraft.brain_coral_block": "Agykorall-blokk", "block.minecraft.brain_coral_fan": "Agykorall-legyező", "block.minecraft.brain_coral_wall_fan": "Fali agykorall-legyező", "block.minecraft.brewing_stand": "Főzőállvány", "block.minecraft.brick_slab": "Tégla-lap", "block.minecraft.brick_stairs": "Téglalépcső", "block.minecraft.brick_wall": "Téglafal", "block.minecraft.bricks": "Tégla", "block.minecraft.brown_banner": "<PERSON><PERSON>l<PERSON>", "block.minecraft.brown_bed": "<PERSON><PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON>", "block.minecraft.brown_candle_cake": "<PERSON><PERSON> barna <PERSON>", "block.minecraft.brown_carpet": "Barna szőnyeg", "block.minecraft.brown_concrete": "<PERSON><PERSON> beton", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON>", "block.minecraft.brown_glazed_terracotta": "Barna m<PERSON> kerámia", "block.minecraft.brown_mushroom": "<PERSON><PERSON> gomba", "block.minecraft.brown_mushroom_block": "<PERSON>na go<PERSON>lo<PERSON>k", "block.minecraft.brown_shulker_box": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass": "Barna festett üveg", "block.minecraft.brown_stained_glass_pane": "<PERSON><PERSON> festett üveglap", "block.minecraft.brown_terracotta": "Barna terrakotta", "block.minecraft.brown_wool": "<PERSON><PERSON>", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "Buborékkorall", "block.minecraft.bubble_coral_block": "Buborékkorall-blokk", "block.minecraft.bubble_coral_fan": "Buborékkorall-legyező", "block.minecraft.bubble_coral_wall_fan": "Fali buborékkorall-legyező", "block.minecraft.budding_amethyst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> am<PERSON>", "block.minecraft.bush": "Cserje", "block.minecraft.cactus": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "Behangolt sculkérzékelő", "block.minecraft.campfire": "Tábortűz", "block.minecraft.candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "<PERSON><PERSON>", "block.minecraft.carrots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Térképasztal", "block.minecraft.carved_pumpkin": "Faragott tök", "block.minecraft.cauldron": "Üst", "block.minecraft.cave_air": "Bar<PERSON><PERSON> leveg<PERSON>", "block.minecraft.cave_vines": "Barlangi indák", "block.minecraft.cave_vines_plant": "Barlangi indaszár", "block.minecraft.chain": "<PERSON><PERSON><PERSON>", "block.minecraft.chain_command_block": "Láncolt paranc<PERSON>blokk", "block.minecraft.cherry_button": "Cseresznyefa gomb", "block.minecraft.cherry_door": "Cseresznyefa ajtó", "block.minecraft.cherry_fence": "Cseresznyefa kerítés", "block.minecraft.cherry_fence_gate": "Cseresznyefa kerítéskapu", "block.minecraft.cherry_hanging_sign": "Cseresznyefa függőtábla", "block.minecraft.cherry_leaves": "Cseresznyefa-levelek", "block.minecraft.cherry_log": "Cseresznyefarönk", "block.minecraft.cherry_planks": "Cseresznyefa deszka", "block.minecraft.cherry_pressure_plate": "Cseresznyefa nyomólap", "block.minecraft.cherry_sapling": "Cseresznyefa-csemete", "block.minecraft.cherry_sign": "Cseresznyefa tábla", "block.minecraft.cherry_slab": "C<PERSON><PERSON>z<PERSON><PERSON><PERSON> lap", "block.minecraft.cherry_stairs": "Cseresznyefa lépcső", "block.minecraft.cherry_trapdoor": "Cseresznyefa csapóajtó", "block.minecraft.cherry_wall_hanging_sign": "Cseresznyefa fali függőtábla", "block.minecraft.cherry_wall_sign": "Cseresznyefa falitábla", "block.minecraft.cherry_wood": "Cseresznyefablokk", "block.minecraft.chest": "<PERSON><PERSON><PERSON>", "block.minecraft.chipped_anvil": "<PERSON><PERSON>t üll<PERSON>", "block.minecraft.chiseled_bookshelf": "Faragott könyvespolc", "block.minecraft.chiseled_copper": "V<PERSON>ett rézblokk", "block.minecraft.chiseled_deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_nether_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_polished_blackstone": "Vésett csiszolt feketekő", "block.minecraft.chiseled_quartz_block": "Vésett kvarcblokk", "block.minecraft.chiseled_red_sandstone": "Vésett vör<PERSON>s <PERSON>", "block.minecraft.chiseled_resin_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_tuff": "<PERSON><PERSON><PERSON> tufa", "block.minecraft.chiseled_tuff_bricks": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.chorus_flower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chorus_plant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.clay": "<PERSON>gyag", "block.minecraft.closed_eyeblossom": "<PERSON><PERSON><PERSON>", "block.minecraft.coal_block": "Szénblokk", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "Terméketlen föld", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobbled_deepslate_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.cobbled_deepslate_stairs": "Zúzott-mélypala lépcső", "block.minecraft.cobbled_deepslate_wall": "Zúzott-mélypala fal", "block.minecraft.cobblestone": "Zúzottkő", "block.minecraft.cobblestone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.cobblestone_stairs": "Zúzottkő lépcső", "block.minecraft.cobblestone_wall": "Zúzottkő-fal", "block.minecraft.cobweb": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Parancsblokk", "block.minecraft.comparator": "Redstone-komparátor", "block.minecraft.composter": "Komposztáló", "block.minecraft.conduit": "Csatorn<PERSON>", "block.minecraft.copper_block": "Rézblokk", "block.minecraft.copper_bulb": "Rézizzó", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cornflower": "Búzavirág", "block.minecraft.cracked_deepslate_bricks": "<PERSON><PERSON>t mélypalatégla", "block.minecraft.cracked_deepslate_tiles": "<PERSON><PERSON><PERSON> mélypalac<PERSON>mp<PERSON>", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON>t nethertégla", "block.minecraft.cracked_polished_blackstone_bricks": "Repedt csiszolt feketekőtégla", "block.minecraft.cracked_stone_bricks": "Repedezett kőtégla", "block.minecraft.crafter": "Barkácsoló", "block.minecraft.crafting_table": "Barkácsasztal", "block.minecraft.creaking_heart": "Csikorgószív", "block.minecraft.creeper_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.creeper_wall_head": "<PERSON><PERSON> creep<PERSON>fej", "block.minecraft.crimson_button": "<PERSON><PERSON><PERSON><PERSON> gomb", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "block.minecraft.crimson_fence": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fungus": "<PERSON><PERSON><PERSON><PERSON> gomba", "block.minecraft.crimson_hanging_sign": "Ka<PERSON>zsin függőtábla", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_nylium": "Karmazsin nílium", "block.minecraft.crimson_planks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_pressure_plate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_roots": "Karmaz<PERSON>", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_stairs": "Karmaz<PERSON> l<PERSON>", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON><PERSON> tönk", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON>ajtó", "block.minecraft.crimson_wall_hanging_sign": "Karmazsin fali függőtábla", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crying_obsidian": "Könnyező obszidián", "block.minecraft.cut_copper": "Vágott rézblokk", "block.minecraft.cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cut_copper_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cut_red_sandstone": "Vágott vörös <PERSON>", "block.minecraft.cut_red_sandstone_slab": "V<PERSON>got<PERSON> v<PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.cut_sandstone": "Vágott homokkő", "block.minecraft.cut_sandstone_slab": "<PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.cyan_banner": "Türkizkék zászló", "block.minecraft.cyan_bed": "Türkizkék ágy", "block.minecraft.cyan_candle": "Türkizkék gyertya", "block.minecraft.cyan_candle_cake": "Torta türkizkék gyertyával", "block.minecraft.cyan_carpet": "Türkizkék szőnyeg", "block.minecraft.cyan_concrete": "Türkizkék beton", "block.minecraft.cyan_concrete_powder": "Türkizkék szárazbeton", "block.minecraft.cyan_glazed_terracotta": "Türkizkék mázas kerámia", "block.minecraft.cyan_shulker_box": "Türkizkék shulkerdoboz", "block.minecraft.cyan_stained_glass": "Türkizkék festett üveg", "block.minecraft.cyan_stained_glass_pane": "Türkizkék festett üveglap", "block.minecraft.cyan_terracotta": "Türkizkék terrakotta", "block.minecraft.cyan_wool": "Türkizkék gyapjú", "block.minecraft.damaged_anvil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dandelion": "Pitypang", "block.minecraft.dark_oak_button": "Sö<PERSON>tt<PERSON><PERSON><PERSON> gomb", "block.minecraft.dark_oak_door": "Sötéttölgy ajtó", "block.minecraft.dark_oak_fence": "Sötéttö<PERSON>gy ker<PERSON>", "block.minecraft.dark_oak_fence_gate": "Sötéttölgy kerítéskapu", "block.minecraft.dark_oak_hanging_sign": "Sötéttölgy függőtábla", "block.minecraft.dark_oak_leaves": "Sötéttölgy-levelek", "block.minecraft.dark_oak_log": "Sötéttölgy-rönk", "block.minecraft.dark_oak_planks": "Sötéttölgy deszka", "block.minecraft.dark_oak_pressure_plate": "Sötéttölgy n<PERSON>", "block.minecraft.dark_oak_sapling": "Sötéttölgy-csemete", "block.minecraft.dark_oak_sign": "Sötéttölgy tábla", "block.minecraft.dark_oak_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.dark_oak_stairs": "Sötéttölgy lépcső", "block.minecraft.dark_oak_trapdoor": "Sötéttölgy csapóajtó", "block.minecraft.dark_oak_wall_hanging_sign": "Sötéttölgy fali függőtábla", "block.minecraft.dark_oak_wall_sign": "Sötéttölgy f<PERSON>", "block.minecraft.dark_oak_wood": "Sötéttölgyblokk", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_stairs": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.daylight_detector": "Napfény<PERSON><PERSON><PERSON>", "block.minecraft.dead_brain_coral": "Elhalt agykorall", "block.minecraft.dead_brain_coral_block": "Elhalt agykorall-blokk", "block.minecraft.dead_brain_coral_fan": "Elhalt agykorall-legyező", "block.minecraft.dead_brain_coral_wall_fan": "Elhalt fali agykorall-legyező", "block.minecraft.dead_bubble_coral": "Elhalt buborékkorall", "block.minecraft.dead_bubble_coral_block": "Elhalt buborékkorall-blokk", "block.minecraft.dead_bubble_coral_fan": "Elhalt buborékkorall-legyező", "block.minecraft.dead_bubble_coral_wall_fan": "Elhalt fali buborékkorall-legyező", "block.minecraft.dead_bush": "Száraz kóró", "block.minecraft.dead_fire_coral": "Elhalt tűzkorall", "block.minecraft.dead_fire_coral_block": "Elhalt tűzkorall-blokk", "block.minecraft.dead_fire_coral_fan": "Elhalt tűzkorall-legyező", "block.minecraft.dead_fire_coral_wall_fan": "Elhalt fali tűzkorall-legyező", "block.minecraft.dead_horn_coral": "Elhalt agancskorall", "block.minecraft.dead_horn_coral_block": "Elhalt agancskorall-blokk", "block.minecraft.dead_horn_coral_fan": "Elhalt agancskorall-legyező", "block.minecraft.dead_horn_coral_wall_fan": "Elhalt fali agancskorall-legyező", "block.minecraft.dead_tube_coral": "Elhalt csőkorall", "block.minecraft.dead_tube_coral_block": "Elhalt csőkorall-blokk", "block.minecraft.dead_tube_coral_fan": "Elhalt csőkorall-legyező", "block.minecraft.dead_tube_coral_wall_fan": "Elhalt fali csőkorall-legyező", "block.minecraft.decorated_pot": "<PERSON><PERSON><PERSON><PERSON><PERSON>tt <PERSON>", "block.minecraft.deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.deepslate_brick_stairs": "Mélypalatégla-lépcső", "block.minecraft.deepslate_brick_wall": "Mélypalatégla-fal", "block.minecraft.deepslate_bricks": "Mélypalatégla", "block.minecraft.deepslate_coal_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_diamond_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_emerald_ore": "<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_gold_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_iron_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_lapis_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_redstone_ore": "Redstone-<PERSON><PERSON>", "block.minecraft.deepslate_tile_slab": "<PERSON><PERSON><PERSON>palacsemp<PERSON>-lap", "block.minecraft.deepslate_tile_stairs": "Mélypalacsempe-lépcső", "block.minecraft.deepslate_tile_wall": "Mélypalacsempe-fal", "block.minecraft.deepslate_tiles": "Mélypala csempe", "block.minecraft.detector_rail": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diamond_block": "Gyémántblokk", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "Dioritlap", "block.minecraft.diorite_stairs": "Dioritlépcső", "block.minecraft.diorite_wall": "Dioritfal", "block.minecraft.dirt": "<PERSON><PERSON><PERSON>", "block.minecraft.dirt_path": "Ösvény", "block.minecraft.dispenser": "Adagoló", "block.minecraft.dragon_egg": "Sárkányt<PERSON>", "block.minecraft.dragon_head": "Sárkán<PERSON><PERSON>j", "block.minecraft.dragon_wall_head": "<PERSON><PERSON>j", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Szárí<PERSON>tt hínárblokk", "block.minecraft.dripstone_block": "Cseppkőblokk", "block.minecraft.dropper": "Kid<PERSON>ó", "block.minecraft.emerald_block": "Smaragdblokk", "block.minecraft.emerald_ore": "Smaragdé<PERSON>", "block.minecraft.enchanting_table": "Varázslóasztal", "block.minecraft.end_gateway": "End-kapu", "block.minecraft.end_portal": "End-portál", "block.minecraft.end_portal_frame": "Endportál-keret", "block.minecraft.end_rod": "End-rúd", "block.minecraft.end_stone": "Endkő", "block.minecraft.end_stone_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.end_stone_brick_stairs": "Endkőtégla-lépcső", "block.minecraft.end_stone_brick_wall": "Endkőtégla-fal", "block.minecraft.end_stone_bricks": "Endkőtégla", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Megviselt vésett rézblokk", "block.minecraft.exposed_copper": "Megviselt rézblokk", "block.minecraft.exposed_copper_bulb": "Megviselt rézizzó", "block.minecraft.exposed_copper_door": "<PERSON><PERSON><PERSON> rézajtó", "block.minecraft.exposed_copper_grate": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_trapdoor": "Megviselt réz csapóajtó", "block.minecraft.exposed_cut_copper": "Megviselt vágott rézblokk", "block.minecraft.exposed_cut_copper_slab": "Megviselt vágott r<PERSON>", "block.minecraft.exposed_cut_copper_stairs": "Megviselt vágott rézlépcső", "block.minecraft.farmland": "Termőföld", "block.minecraft.fern": "Páfrán<PERSON>", "block.minecraft.fire": "Tűz", "block.minecraft.fire_coral": "Tűzkorall", "block.minecraft.fire_coral_block": "Tűzkorall-blokk", "block.minecraft.fire_coral_fan": "Tűzkorall-legyező", "block.minecraft.fire_coral_wall_fan": "Fali tűzkorall-legyező", "block.minecraft.firefly_bush": "Szentjánosbogár-bokor", "block.minecraft.fletching_table": "<PERSON><PERSON><PERSON><PERSON>z<PERSON>", "block.minecraft.flower_pot": "Virágcserép", "block.minecraft.flowering_azalea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.frogspawn": "Békapeték", "block.minecraft.frosted_ice": "Gyorsfagyasztott jég", "block.minecraft.furnace": "<PERSON><PERSON><PERSON>", "block.minecraft.gilded_blackstone": "Aranyozott feketekő", "block.minecraft.glass": "Üveg", "block.minecraft.glass_pane": "Üveglap", "block.minecraft.glow_lichen": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glowstone": "Izzókő", "block.minecraft.gold_block": "Aranyblokk", "block.minecraft.gold_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.granite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.granite_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.granite_stairs": "Gránitlépcső", "block.minecraft.granite_wall": "Gránitfal", "block.minecraft.grass": "Fű", "block.minecraft.grass_block": "<PERSON><PERSON><PERSON> földblokk", "block.minecraft.gravel": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_banner": "Szürke zászló", "block.minecraft.gray_bed": "Szürke ágy", "block.minecraft.gray_candle": "Szürke gyertya", "block.minecraft.gray_candle_cake": "<PERSON><PERSON> szürke gyer<PERSON>", "block.minecraft.gray_carpet": "Szürke szőnyeg", "block.minecraft.gray_concrete": "Szürke beton", "block.minecraft.gray_concrete_powder": "Szürke szárazbeton", "block.minecraft.gray_glazed_terracotta": "Szürke mázas kerámia", "block.minecraft.gray_shulker_box": "Szürke shulkerdoboz", "block.minecraft.gray_stained_glass": "Szürke festett üveg", "block.minecraft.gray_stained_glass_pane": "Szürke festett üveglap", "block.minecraft.gray_terracotta": "Szürke terrakotta", "block.minecraft.gray_wool": "Szürke gyapjú", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.green_candle_cake": "<PERSON><PERSON>", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON><PERSON> ker<PERSON>", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON> festett üveg", "block.minecraft.green_stained_glass_pane": "<PERSON><PERSON><PERSON> festett üveglap", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.grindstone": "Köszörűkő", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.hay_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.heavy_core": "Nehéz mag", "block.minecraft.heavy_weighted_pressure_plate": "<PERSON>eh<PERSON>z <PERSON><PERSON><PERSON> n<PERSON>", "block.minecraft.honey_block": "Mézblokk", "block.minecraft.honeycomb_block": "Méhsejtblokk", "block.minecraft.hopper": "T<PERSON><PERSON><PERSON><PERSON>r", "block.minecraft.horn_coral": "Agancskorall", "block.minecraft.horn_coral_block": "Agancskorall-blokk", "block.minecraft.horn_coral_fan": "Agancskorall-legyező", "block.minecraft.horn_coral_wall_fan": "Fali agancskorall-legyező", "block.minecraft.ice": "<PERSON><PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "Vésett kőtégla ezüstmollyal", "block.minecraft.infested_cobblestone": "Zúzottkő ezüstmollyal", "block.minecraft.infested_cracked_stone_bricks": "Repedt kőtégla ezüstmollyal", "block.minecraft.infested_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.infested_mossy_stone_bricks": "Mohás kőtégla ezüstmollyal", "block.minecraft.infested_stone": "Kő ezüstmollyal", "block.minecraft.infested_stone_bricks": "Kőtégla ezüstmollyal", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "Vasblokk", "block.minecraft.iron_door": "Vasajtó", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_trapdoor": "Vas csapóajtó", "block.minecraft.jack_o_lantern": "Töklámpás", "block.minecraft.jigsaw": "Kirakósblokk", "block.minecraft.jukebox": "Zenegép", "block.minecraft.jungle_button": "Dzsungelfa gomb", "block.minecraft.jungle_door": "Dzsungelfa ajtó", "block.minecraft.jungle_fence": "Dzsungelfa kerítés", "block.minecraft.jungle_fence_gate": "Dzsungelfa kerítéskapu", "block.minecraft.jungle_hanging_sign": "Dzsungelfa függőtábla", "block.minecraft.jungle_leaves": "Dzsungelfalevelek", "block.minecraft.jungle_log": "Dzsungelfarönk", "block.minecraft.jungle_planks": "Dzsungelfa deszka", "block.minecraft.jungle_pressure_plate": "Dzsungelfa nyomólap", "block.minecraft.jungle_sapling": "Dzsungelfacsemete", "block.minecraft.jungle_sign": "Dzsungelfa tábla", "block.minecraft.jungle_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.jungle_stairs": "Dzsungelfa lépcső", "block.minecraft.jungle_trapdoor": "Dzsungelfa csapóajtó", "block.minecraft.jungle_wall_hanging_sign": "Dzsungelfa fali függőtábla", "block.minecraft.jungle_wall_sign": "Dzsungelfa falitábla", "block.minecraft.jungle_wood": "Dzsungelfablokk", "block.minecraft.kelp": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.kelp_plant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ladder": "Létra", "block.minecraft.lantern": "Lámpás", "block.minecraft.lapis_block": "Lazuritblokk", "block.minecraft.lapis_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.large_amethyst_bud": "<PERSON><PERSON>", "block.minecraft.large_fern": "<PERSON><PERSON>", "block.minecraft.lava": "<PERSON><PERSON><PERSON>", "block.minecraft.lava_cauldron": "<PERSON><PERSON><PERSON><PERSON><PERSON> teli üst", "block.minecraft.leaf_litter": "Avar", "block.minecraft.lectern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lever": "<PERSON><PERSON>", "block.minecraft.light": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_banner": "Világoskék zászló", "block.minecraft.light_blue_bed": "Világoskék ágy", "block.minecraft.light_blue_candle": "Világoskék gyertya", "block.minecraft.light_blue_candle_cake": "Torta világoskék gyertyával", "block.minecraft.light_blue_carpet": "Világoskék szőnyeg", "block.minecraft.light_blue_concrete": "Világoskék beton", "block.minecraft.light_blue_concrete_powder": "Világoskék szárazbeton", "block.minecraft.light_blue_glazed_terracotta": "Világoskék mázas kerámia", "block.minecraft.light_blue_shulker_box": "Világoskék shulkerdoboz", "block.minecraft.light_blue_stained_glass": "Világoskék festett üveg", "block.minecraft.light_blue_stained_glass_pane": "Világoskék festett üveglap", "block.minecraft.light_blue_terracotta": "Világoskék terrakotta", "block.minecraft.light_blue_wool": "Világoskék gyapjú", "block.minecraft.light_gray_banner": "Világosszürke zászló", "block.minecraft.light_gray_bed": "Világosszürke ágy", "block.minecraft.light_gray_candle": "Világosszürke gyertya", "block.minecraft.light_gray_candle_cake": "Torta világosszürke gyertyával", "block.minecraft.light_gray_carpet": "Világosszürke szőnyeg", "block.minecraft.light_gray_concrete": "Világosszürk<PERSON> beton", "block.minecraft.light_gray_concrete_powder": "Világosszürke szárazbeton", "block.minecraft.light_gray_glazed_terracotta": "Világosszürke mázas kerámia", "block.minecraft.light_gray_shulker_box": "Világosszürke shulkerdoboz", "block.minecraft.light_gray_stained_glass": "Világosszürke festett üveg", "block.minecraft.light_gray_stained_glass_pane": "Világosszürke festett üveglap", "block.minecraft.light_gray_terracotta": "Világosszürke terrakotta", "block.minecraft.light_gray_wool": "Világosszürke gyapjú", "block.minecraft.light_weighted_pressure_plate": "Könnyű súlymérő nyomólap", "block.minecraft.lightning_rod": "Villámh<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lilac": "Orgona", "block.minecraft.lily_of_the_valley": "Gyöngyvirág", "block.minecraft.lily_pad": "Tavirózsa", "block.minecraft.lime_banner": "Világos<PERSON><PERSON><PERSON>", "block.minecraft.lime_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_candle_cake": "Torta vilá<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_carpet": "Világoszöld szőnyeg", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete_powder": "Vilá<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.lime_glazed_terracotta": "Vil<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON> ker<PERSON>", "block.minecraft.lime_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_stained_glass": "Világoszöld festett üveg", "block.minecraft.lime_stained_glass_pane": "Világoszöld festett üveglap", "block.minecraft.lime_terracotta": "Világoszöld terra<PERSON>", "block.minecraft.lime_wool": "Világoszöld g<PERSON>j<PERSON>", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.loom": "Szövőszék", "block.minecraft.magenta_banner": "Bíbor zászló", "block.minecraft.magenta_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_candle_cake": "Torta b<PERSON><PERSON>", "block.minecraft.magenta_carpet": "Bíbor szőnyeg", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete_powder": "Bíbor szárazbeton", "block.minecraft.magenta_glazed_terracotta": "<PERSON>í<PERSON> m<PERSON> ker<PERSON>", "block.minecraft.magenta_shulker_box": "Bíbor <PERSON>", "block.minecraft.magenta_stained_glass": "Bíbor festett üveg", "block.minecraft.magenta_stained_glass_pane": "Bíbor festett üveglap", "block.minecraft.magenta_terracotta": "<PERSON>í<PERSON>", "block.minecraft.magenta_wool": "Bíbor g<PERSON>", "block.minecraft.magma_block": "Magmablokk", "block.minecraft.mangrove_button": "Mangrovefa gomb", "block.minecraft.mangrove_door": "Mangrovefa ajtó", "block.minecraft.mangrove_fence": "Mangrovefa kerítés", "block.minecraft.mangrove_fence_gate": "Mangrovefa kerítéskapu", "block.minecraft.mangrove_hanging_sign": "Mangrovefa függőtábla", "block.minecraft.mangrove_leaves": "Mangrovelevelek", "block.minecraft.mangrove_log": "Mangroverönk", "block.minecraft.mangrove_planks": "Mangrovedeszka", "block.minecraft.mangrove_pressure_plate": "Mangrovefa nyomólap", "block.minecraft.mangrove_propagule": "Mangrovemagonc", "block.minecraft.mangrove_roots": "Mangrovegyökerek", "block.minecraft.mangrove_sign": "Mangrovefa tábla", "block.minecraft.mangrove_slab": "<PERSON><PERSON><PERSON> lap", "block.minecraft.mangrove_stairs": "Mangrovefa lépcső", "block.minecraft.mangrove_trapdoor": "Mangrovefa csapóajtó", "block.minecraft.mangrove_wall_hanging_sign": "Mangrovefa fali függőtábla", "block.minecraft.mangrove_wall_sign": "Mangrovefa falitábla", "block.minecraft.mangrove_wood": "Mangroveblokk", "block.minecraft.medium_amethyst_bud": "Közepes ametisztkristály", "block.minecraft.melon": "<PERSON><PERSON><PERSON>", "block.minecraft.melon_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.moss_block": "Mohablokk", "block.minecraft.moss_carpet": "Mohaszőnyeg", "block.minecraft.mossy_cobblestone": "Mo<PERSON>ás zúzottkő", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON><PERSON> lap", "block.minecraft.mossy_cobblestone_stairs": "Mo<PERSON>ás zúzottkőlépcső", "block.minecraft.mossy_cobblestone_wall": "Mo<PERSON>ás zúzottkő-fal", "block.minecraft.mossy_stone_brick_slab": "<PERSON><PERSON><PERSON> lap", "block.minecraft.mossy_stone_brick_stairs": "Mohás kőtégla-lépcső", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON><PERSON> k<PERSON>glafal", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.moving_piston": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud": "Isza<PERSON>", "block.minecraft.mud_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.mud_brick_stairs": "Vályogtégla-lépcső", "block.minecraft.mud_brick_wall": "V<PERSON><PERSON>ogté<PERSON>la-fal", "block.minecraft.mud_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON><PERSON><PERSON> man<PERSON>gyökerek", "block.minecraft.mushroom_stem": "Gombatönk", "block.minecraft.mycelium": "Micélium", "block.minecraft.nether_brick_fence": "Nethertégla-k<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.nether_brick_stairs": "Nethertégla-lépcső", "block.minecraft.nether_brick_wall": "Nethertégla-fal", "block.minecraft.nether_bricks": "Nethertégla", "block.minecraft.nether_gold_ore": "Netheraranyérc", "block.minecraft.nether_portal": "Nether-portál", "block.minecraft.nether_quartz_ore": "Netherkvarcérc", "block.minecraft.nether_sprouts": "Nethercsírák", "block.minecraft.nether_wart": "Netherbibircsók", "block.minecraft.nether_wart_block": "Netherbibircsók-blokk", "block.minecraft.netherite_block": "Netheritblokk", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Hangdoboz", "block.minecraft.oak_button": "Tölgyfa gomb", "block.minecraft.oak_door": "Tölgyfa ajtó", "block.minecraft.oak_fence": "Tölgyfa kerítés", "block.minecraft.oak_fence_gate": "Tölgyfa kerítéskapu", "block.minecraft.oak_hanging_sign": "Tölgyfa függőtábla", "block.minecraft.oak_leaves": "Tölgylevelek", "block.minecraft.oak_log": "Tölgyfarönk", "block.minecraft.oak_planks": "Tölgydeszka", "block.minecraft.oak_pressure_plate": "Tölgyfa nyomólap", "block.minecraft.oak_sapling": "Tölgycsemete", "block.minecraft.oak_sign": "Tölgyfa tábla", "block.minecraft.oak_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.oak_stairs": "Tölgyfa lépcső", "block.minecraft.oak_trapdoor": "Tölgyfa csapóajtó", "block.minecraft.oak_wall_hanging_sign": "Tölgyfa fali függőtábla", "block.minecraft.oak_wall_sign": "Tölgyfa falitábla", "block.minecraft.oak_wood": "Tölgyfablokk", "block.minecraft.observer": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.obsidian": "Obszidián", "block.minecraft.ochre_froglight": "Okker varangyfény", "block.minecraft.ominous_banner": "Vészjósló zászló", "block.minecraft.open_eyeblossom": "Nyitott <PERSON>", "block.minecraft.orange_banner": "Narancssárga zászló", "block.minecraft.orange_bed": "Narancssá<PERSON>", "block.minecraft.orange_candle": "Narancssárga <PERSON>", "block.minecraft.orange_candle_cake": "<PERSON>ta na<PERSON>cs<PERSON><PERSON>", "block.minecraft.orange_carpet": "Narancssárga szőnyeg", "block.minecraft.orange_concrete": "Narancs<PERSON><PERSON><PERSON> beton", "block.minecraft.orange_concrete_powder": "Narancssárga szárazbeton", "block.minecraft.orange_glazed_terracotta": "Narancssárga mázas kerámia", "block.minecraft.orange_shulker_box": "Narancssárga shulkerdoboz", "block.minecraft.orange_stained_glass": "Narancssárga festett üveg", "block.minecraft.orange_stained_glass_pane": "Narancssárga festett üveglap", "block.minecraft.orange_terracotta": "Narancssárga terrakotta", "block.minecraft.orange_tulip": "Narancssárga tulipán", "block.minecraft.orange_wool": "Narancssárga gyapjú", "block.minecraft.oxeye_daisy": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "Patinás vésett rézblokk", "block.minecraft.oxidized_copper": "<PERSON><PERSON>ás rézblokk", "block.minecraft.oxidized_copper_bulb": "<PERSON><PERSON><PERSON> rézi<PERSON>", "block.minecraft.oxidized_copper_door": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_grate": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_trapdoor": "Patinás réz csapóajtó", "block.minecraft.oxidized_cut_copper": "Patinás vágott rézblokk", "block.minecraft.oxidized_cut_copper_slab": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_cut_copper_stairs": "Patinás vágott réz<PERSON>", "block.minecraft.packed_ice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.packed_mud": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_moss_block": "F<PERSON><PERSON>olmoha-blokk", "block.minecraft.pale_moss_carpet": "Fátyolmoha-szőnyeg", "block.minecraft.pale_oak_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gomb", "block.minecraft.pale_oak_door": "Sápadttölgy ajtó", "block.minecraft.pale_oak_fence": "Sápadtt<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_fence_gate": "Sápadttölgy k<PERSON>", "block.minecraft.pale_oak_hanging_sign": "Sápadttölgy függőtábla", "block.minecraft.pale_oak_leaves": "Sápadttölgy-levelek", "block.minecraft.pale_oak_log": "Sápadttölgy-rönk", "block.minecraft.pale_oak_planks": "Sápadttölgy deszka", "block.minecraft.pale_oak_pressure_plate": "Sápadtt<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_sapling": "Sápadtt<PERSON><PERSON><PERSON>e", "block.minecraft.pale_oak_sign": "Sápadttölgy <PERSON>", "block.minecraft.pale_oak_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.pale_oak_stairs": "Sápadttölgy lépcső", "block.minecraft.pale_oak_trapdoor": "Sápadtt<PERSON><PERSON>gy csapóajtó", "block.minecraft.pale_oak_wall_hanging_sign": "Sápadttölgy fali függőtábla", "block.minecraft.pale_oak_wall_sign": "Sápadtt<PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_wood": "Sápadttölgyblokk", "block.minecraft.pearlescent_froglight": "Gyöngyházfényű varangyfény", "block.minecraft.peony": "Bazsarózsa", "block.minecraft.petrified_oak_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tö<PERSON><PERSON><PERSON> lap", "block.minecraft.piglin_head": "Piglinfej", "block.minecraft.piglin_wall_head": "<PERSON><PERSON>", "block.minecraft.pink_banner": "Rózsa<PERSON><PERSON> z<PERSON>l<PERSON>", "block.minecraft.pink_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_candle_cake": "Torta ró<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_carpet": "Rózsaszín szőnyeg", "block.minecraft.pink_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>on", "block.minecraft.pink_concrete_powder": "Rózsaszín szárazbeton", "block.minecraft.pink_glazed_terracotta": "Róz<PERSON><PERSON><PERSON> mázas kerámia", "block.minecraft.pink_petals": "Rózsaszín szirmok", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.pink_stained_glass": "Rózsaszín festett üveg", "block.minecraft.pink_stained_glass_pane": "Rózsasz<PERSON> festett üveglap", "block.minecraft.pink_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> terrakotta", "block.minecraft.pink_tulip": "Rózsaszín tulipán", "block.minecraft.pink_wool": "Rózsa<PERSON><PERSON> g<PERSON>", "block.minecraft.piston": "Du<PERSON><PERSON><PERSON>", "block.minecraft.piston_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pitcher_crop": "Kancsókahüvely", "block.minecraft.pitcher_plant": "Kancsókanövény", "block.minecraft.player_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head.named": "%s feje", "block.minecraft.player_wall_head": "<PERSON><PERSON>", "block.minecraft.podzol": "Podzol", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite": "Csiszolt andezit", "block.minecraft.polished_andesite_slab": "Csiszolt andezitlap", "block.minecraft.polished_andesite_stairs": "Csiszolt andezitlépcső", "block.minecraft.polished_basalt": "Csiszolt bazalt", "block.minecraft.polished_blackstone": "Csiszolt feketekő", "block.minecraft.polished_blackstone_brick_slab": "Csiszolt feketekőtégla lap", "block.minecraft.polished_blackstone_brick_stairs": "Csiszolt feketekőtégla-lépcső", "block.minecraft.polished_blackstone_brick_wall": "Csiszolt feketekőtégla-fal", "block.minecraft.polished_blackstone_bricks": "Csiszolt feketekőtégla", "block.minecraft.polished_blackstone_button": "Csiszolt feketekő gomb", "block.minecraft.polished_blackstone_pressure_plate": "Csiszolt feketekő nyomólap", "block.minecraft.polished_blackstone_slab": "Csiszolt feketek<PERSON> lap", "block.minecraft.polished_blackstone_stairs": "Csiszolt feketekő lépcső", "block.minecraft.polished_blackstone_wall": "Csiszolt feketekő fal", "block.minecraft.polished_deepslate": "<PERSON><PERSON><PERSON>lt m<PERSON>", "block.minecraft.polished_deepslate_slab": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> lap", "block.minecraft.polished_deepslate_stairs": "Csiszolt mélypala lépcső", "block.minecraft.polished_deepslate_wall": "Csiszolt mélypala fal", "block.minecraft.polished_diorite": "Csiszolt diorit", "block.minecraft.polished_diorite_slab": "Csiszolt dioritlap", "block.minecraft.polished_diorite_stairs": "Csiszolt dioritlépcső", "block.minecraft.polished_granite": "Csiszolt gránit", "block.minecraft.polished_granite_slab": "Csiszolt gránitlap", "block.minecraft.polished_granite_stairs": "Csiszolt gránitlépcső", "block.minecraft.polished_tuff": "Csiszolt tufa", "block.minecraft.polished_tuff_slab": "Csiszolt tufalap", "block.minecraft.polished_tuff_stairs": "Csiszolt tufalépcső", "block.minecraft.polished_tuff_wall": "Csiszolt tufafal", "block.minecraft.poppy": "Pipacs", "block.minecraft.potatoes": "Burgonyák", "block.minecraft.potted_acacia_sapling": "Akáciacsemete cserépben", "block.minecraft.potted_allium": "Hagymavir<PERSON><PERSON>", "block.minecraft.potted_azalea_bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_azure_bluet": "Kék<PERSON><PERSON><PERSON>", "block.minecraft.potted_bamboo": "Ba<PERSON><PERSON>z cserépben", "block.minecraft.potted_birch_sapling": "Nyírfacsemete cserépben", "block.minecraft.potted_blue_orchid": "<PERSON><PERSON><PERSON>ea cser<PERSON>", "block.minecraft.potted_brown_mushroom": "<PERSON><PERSON> gomba c<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_cactus": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_cherry_sapling": "Cseresznyefa-csemete cserépben", "block.minecraft.potted_closed_eyeblossom": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>", "block.minecraft.potted_cornflower": "Búzavir<PERSON><PERSON>", "block.minecraft.potted_crimson_fungus": "<PERSON><PERSON><PERSON><PERSON> gomba c<PERSON>", "block.minecraft.potted_crimson_roots": "Karmazsin gyökerek cserépben", "block.minecraft.potted_dandelion": "Pitypang c<PERSON>", "block.minecraft.potted_dark_oak_sapling": "Sötéttölgy-cse<PERSON>e cser<PERSON>", "block.minecraft.potted_dead_bush": "Száraz kóró c<PERSON>", "block.minecraft.potted_fern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_flowering_azalea_bush": "V<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_jungle_sapling": "Dzsungelfacsemete cserépben", "block.minecraft.potted_lily_of_the_valley": "Gyöngyvir<PERSON><PERSON>", "block.minecraft.potted_mangrove_propagule": "Mangrovemagonc cserépben", "block.minecraft.potted_oak_sapling": "Tölgycsemete cserépben", "block.minecraft.potted_open_eyeblossom": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>vir<PERSON>g", "block.minecraft.potted_orange_tulip": "Narancssárga tulipán cserépben", "block.minecraft.potted_oxeye_daisy": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_pale_oak_sapling": "Sápadttölgy-c<PERSON><PERSON>e c<PERSON>", "block.minecraft.potted_pink_tulip": "Rózsaszín tulipán cserépben", "block.minecraft.potted_poppy": "Pipacs cserépben", "block.minecraft.potted_red_mushroom": "<PERSON><PERSON> go<PERSON> c<PERSON><PERSON><PERSON>", "block.minecraft.potted_red_tulip": "<PERSON>ros tuli<PERSON> c<PERSON>ben", "block.minecraft.potted_spruce_sapling": "Fenyőcsemete cserépben", "block.minecraft.potted_torchflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_warped_fungus": "<PERSON>z gomba cseré<PERSON>ben", "block.minecraft.potted_warped_roots": "Torz gyökerek cserépben", "block.minecraft.potted_white_tulip": "<PERSON><PERSON><PERSON><PERSON> tuli<PERSON> c<PERSON>", "block.minecraft.potted_wither_rose": "Witherr<PERSON><PERSON><PERSON>", "block.minecraft.powder_snow": "Porhó", "block.minecraft.powder_snow_cauldron": "Porhóval teli üst", "block.minecraft.powered_rail": "Gyorsítósín", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.prismarine_brick_stairs": "Prizmarintégla lépcső", "block.minecraft.prismarine_bricks": "Prizmarintégla", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_wall": "Prizmarinfal", "block.minecraft.pumpkin": "Tök", "block.minecraft.pumpkin_stem": "Tökszár", "block.minecraft.purple_banner": "<PERSON>", "block.minecraft.purple_bed": "<PERSON>", "block.minecraft.purple_candle": "<PERSON>", "block.minecraft.purple_candle_cake": "Torta lila <PERSON>", "block.minecraft.purple_carpet": "<PERSON>", "block.minecraft.purple_concrete": "<PERSON> beton", "block.minecraft.purple_concrete_powder": "<PERSON>", "block.minecraft.purple_glazed_terracotta": "<PERSON> m<PERSON> kerá<PERSON>", "block.minecraft.purple_shulker_box": "<PERSON>", "block.minecraft.purple_stained_glass": "<PERSON> festett üveg", "block.minecraft.purple_stained_glass_pane": "<PERSON> feste<PERSON> üveg<PERSON>", "block.minecraft.purple_terracotta": "<PERSON> terra<PERSON>", "block.minecraft.purple_wool": "<PERSON>", "block.minecraft.purpur_block": "Purpurblokk", "block.minecraft.purpur_pillar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purpur_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purpur_stairs": "Purpurlépcső", "block.minecraft.quartz_block": "Kvarcblokk", "block.minecraft.quartz_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_stairs": "Kvar<PERSON>lép<PERSON>", "block.minecraft.rail": "Sín", "block.minecraft.raw_copper_block": "Nyersrézblokk", "block.minecraft.raw_gold_block": "Nyersaranyblokk", "block.minecraft.raw_iron_block": "Nyersvasblokk", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_candle_cake": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON> ker<PERSON>", "block.minecraft.red_mushroom": "<PERSON><PERSON>", "block.minecraft.red_mushroom_block": "<PERSON><PERSON>", "block.minecraft.red_nether_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.red_nether_brick_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON>l<PERSON>", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>fal", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_sand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.red_sandstone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON> lépcső", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON> fal", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON> festett üveg", "block.minecraft.red_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON><PERSON> feste<PERSON> üveglap", "block.minecraft.red_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_tulip": "Piros tuli<PERSON>án", "block.minecraft.red_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.redstone_block": "Redstone-blokk", "block.minecraft.redstone_lamp": "Redstone-lámpa", "block.minecraft.redstone_ore": "Redstone-érc", "block.minecraft.redstone_torch": "Redstone-fáklya", "block.minecraft.redstone_wall_torch": "Fali redstone-fáklya", "block.minecraft.redstone_wire": "Redstone-vezeték", "block.minecraft.reinforced_deepslate": "Megerősített m<PERSON>", "block.minecraft.repeater": "Redstone-<PERSON><PERSON><PERSON>", "block.minecraft.repeating_command_block": "Ismétlő parancsblokk", "block.minecraft.resin_block": "Gyantablokk", "block.minecraft.resin_brick_slab": "Gyantatég<PERSON> f<PERSON>", "block.minecraft.resin_brick_stairs": "Gyantatégla lépcső", "block.minecraft.resin_brick_wall": "Gyantatégla-fal", "block.minecraft.resin_bricks": "Gyantatégla", "block.minecraft.resin_clump": "Gyan<PERSON><PERSON>b", "block.minecraft.respawn_anchor": "Újraéledés-kapocs", "block.minecraft.rooted_dirt": "Gyöker<PERSON> föld", "block.minecraft.rose_bush": "Rózsabokor", "block.minecraft.sand": "Homok", "block.minecraft.sandstone": "Homokkő", "block.minecraft.sandstone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.sandstone_stairs": "Homokkő lépcső", "block.minecraft.sandstone_wall": "Homokkő fal", "block.minecraft.scaffolding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculkgerjesztő", "block.minecraft.sculk_sensor": "Sculkérz<PERSON><PERSON>", "block.minecraft.sculk_shrieker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk_vein": "Sculkvéna", "block.minecraft.sea_lantern": "Tengeri lá<PERSON>", "block.minecraft.sea_pickle": "Világító <PERSON>", "block.minecraft.seagrass": "Tengerifű", "block.minecraft.set_spawn": "Újraéledési pont beállítva", "block.minecraft.short_dry_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.short_grass": "Alacsony fű", "block.minecraft.shroomlight": "Fényspóra", "block.minecraft.shulker_box": "<PERSON><PERSON>erd<PERSON><PERSON>", "block.minecraft.skeleton_skull": "Csontvázkoponya", "block.minecraft.skeleton_wall_skull": "Fali csontvázkoponya", "block.minecraft.slime_block": "Nyálkablokk", "block.minecraft.small_amethyst_bud": "<PERSON><PERSON>", "block.minecraft.small_dripleaf": "<PERSON><PERSON>", "block.minecraft.smithing_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smoker": "Füst<PERSON>lő", "block.minecraft.smooth_basalt": "<PERSON><PERSON> baz<PERSON>t", "block.minecraft.smooth_quartz": "Csiszolt kvarcblokk", "block.minecraft.smooth_quartz_slab": "Csiszolt kvarclap", "block.minecraft.smooth_quartz_stairs": "Csiszolt kvarclépcső", "block.minecraft.smooth_red_sandstone": "Csiszolt vörös homokkő", "block.minecraft.smooth_red_sandstone_slab": "Csiszolt vörös ho<PERSON> lap", "block.minecraft.smooth_red_sandstone_stairs": "Csiszolt vörös homokkő lépcső", "block.minecraft.smooth_sandstone": "Csiszolt homokkő", "block.minecraft.smooth_sandstone_slab": "Csiszolt homokk<PERSON> lap", "block.minecraft.smooth_sandstone_stairs": "Csiszolt homokkő lépcső", "block.minecraft.smooth_stone": "Csiszolt kő", "block.minecraft.smooth_stone_slab": "Csiszolt k<PERSON>lap", "block.minecraft.sniffer_egg": "Orrontótojás", "block.minecraft.snow": "<PERSON><PERSON>", "block.minecraft.snow_block": "Hóblokk", "block.minecraft.soul_campfire": "Lélektábortűz", "block.minecraft.soul_fire": "Lélektűz", "block.minecraft.soul_lantern": "Léleklámpás", "block.minecraft.soul_sand": "Lélekhomok", "block.minecraft.soul_soil": "Lélektalaj", "block.minecraft.soul_torch": "Lélekfáklya", "block.minecraft.soul_wall_torch": "Fali lélekfáklya", "block.minecraft.spawn.not_valid": "<PERSON><PERSON><PERSON> vagy egy feltöltött újraéledés-kapcsod, vagy nem has<PERSON>", "block.minecraft.spawner": "Idézőketrec", "block.minecraft.spawner.desc1": "Interakció idézőtojással:", "block.minecraft.spawner.desc2": "Beállítja a mob típusát", "block.minecraft.sponge": "Szivacs", "block.minecraft.spore_blossom": "Spóravirág", "block.minecraft.spruce_button": "Fenyőfa gomb", "block.minecraft.spruce_door": "Fenyőfa ajtó", "block.minecraft.spruce_fence": "Fenyőfa kerí<PERSON>s", "block.minecraft.spruce_fence_gate": "Fenyőfa kerítéskapu", "block.minecraft.spruce_hanging_sign": "Fenyőfa függőtábla", "block.minecraft.spruce_leaves": "Fenyőlevelek", "block.minecraft.spruce_log": "Fenyőrönk", "block.minecraft.spruce_planks": "Fenyődeszka", "block.minecraft.spruce_pressure_plate": "Fenyőfa nyomólap", "block.minecraft.spruce_sapling": "Fenyőcsemete", "block.minecraft.spruce_sign": "Fenyőtábla", "block.minecraft.spruce_slab": "<PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.spruce_stairs": "Fenyőfa lépcső", "block.minecraft.spruce_trapdoor": "Fenyőfa csapóajtó", "block.minecraft.spruce_wall_hanging_sign": "Fenyőfa fali függőtábla", "block.minecraft.spruce_wall_sign": "Fenyőfa falitábla", "block.minecraft.spruce_wood": "Fenyőblokk", "block.minecraft.sticky_piston": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone": "Kő", "block.minecraft.stone_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.stone_brick_stairs": "Kőtégla-lépcső", "block.minecraft.stone_brick_wall": "Kőtéglafal", "block.minecraft.stone_bricks": "Kőtégla", "block.minecraft.stone_button": "Kőgomb", "block.minecraft.stone_pressure_plate": "<PERSON><PERSON>", "block.minecraft.stone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_stairs": "Kőlépcső", "block.minecraft.stonecutter": "Kővágó", "block.minecraft.stripped_acacia_log": "Kérgezett akáciarönk", "block.minecraft.stripped_acacia_wood": "Kérgezett akáciablokk", "block.minecraft.stripped_bamboo_block": "Kérge<PERSON>tt bambuszblokk", "block.minecraft.stripped_birch_log": "Kérgezett nyírfarönk", "block.minecraft.stripped_birch_wood": "Kérgezett nyírfablokk", "block.minecraft.stripped_cherry_log": "<PERSON><PERSON><PERSON><PERSON>tt cseresznyefarönk", "block.minecraft.stripped_cherry_wood": "K<PERSON><PERSON><PERSON>tt cseresznyefablokk", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> go<PERSON>", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON> karma<PERSON> tönk", "block.minecraft.stripped_dark_oak_log": "Kérgezett sötéttölgy-rönk", "block.minecraft.stripped_dark_oak_wood": "Kérgezett sötéttölgyblokk", "block.minecraft.stripped_jungle_log": "Kérge<PERSON>tt dzsungelfarönk", "block.minecraft.stripped_jungle_wood": "Kérgezett dzsungelfablokk", "block.minecraft.stripped_mangrove_log": "<PERSON><PERSON><PERSON><PERSON>tt mangroverönk", "block.minecraft.stripped_mangrove_wood": "Kérgezett mangroveblokk", "block.minecraft.stripped_oak_log": "Kérgezett tölgyfarönk", "block.minecraft.stripped_oak_wood": "Kérgezett tölgyfablokk", "block.minecraft.stripped_pale_oak_log": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ttölgy-rönk", "block.minecraft.stripped_pale_oak_wood": "Kérgezett sápadttölgyblokk", "block.minecraft.stripped_spruce_log": "Kérgezett fenyőrönk", "block.minecraft.stripped_spruce_wood": "Kérgezett fenyőblokk", "block.minecraft.stripped_warped_hyphae": "<PERSON><PERSON><PERSON><PERSON><PERSON> torz gombafonal<PERSON>", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON> torz tönk", "block.minecraft.structure_block": "Struktúrablokk", "block.minecraft.structure_void": "Struktúraű<PERSON>", "block.minecraft.sugar_cane": "Cukornád", "block.minecraft.sunflower": "Napraforgó", "block.minecraft.suspicious_gravel": "<PERSON><PERSON><PERSON>", "block.minecraft.suspicious_sand": "<PERSON><PERSON><PERSON>", "block.minecraft.sweet_berry_bush": "Édesbogyóbokor", "block.minecraft.tall_dry_grass": "Hosszú száraz fű", "block.minecraft.tall_grass": "Magas fű", "block.minecraft.tall_seagrass": "Magas tengerifű", "block.minecraft.target": "Céltábla", "block.minecraft.terracotta": "Terrakotta", "block.minecraft.test_block": "Tesztblokk", "block.minecraft.test_instance_block": "Tesztpéldány-blokk", "block.minecraft.tinted_glass": "Sötétített üveg", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "A TNT-robbanások ki vannak kapcsolva", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "Fáklyaliliom", "block.minecraft.torchflower_crop": "Fáklyaliliom-ültetvény", "block.minecraft.trapped_chest": "Csapdaláda", "block.minecraft.trial_spawner": "Küzdőtéri idézőketrec", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "Botlódrót-horog", "block.minecraft.tube_coral": "Csőkorall", "block.minecraft.tube_coral_block": "Csőkorall-blokk", "block.minecraft.tube_coral_fan": "Csőkorall-legyező", "block.minecraft.tube_coral_wall_fan": "Fali csőkorall-legyező", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> lap", "block.minecraft.tuff_brick_stairs": "Tufatégla lépcső", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON> fal", "block.minecraft.tuff_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_slab": "<PERSON><PERSON>", "block.minecraft.tuff_stairs": "Tufalépcső", "block.minecraft.tuff_wall": "Tu<PERSON><PERSON><PERSON>", "block.minecraft.turtle_egg": "Teknőstojás", "block.minecraft.twisting_vines": "Tekergő indák", "block.minecraft.twisting_vines_plant": "Tekergő indaszár", "block.minecraft.vault": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON>", "block.minecraft.vine": "Inda", "block.minecraft.void_air": "Űrbéli levegő", "block.minecraft.wall_torch": "<PERSON><PERSON>", "block.minecraft.warped_button": "<PERSON><PERSON> gomb", "block.minecraft.warped_door": "<PERSON><PERSON>", "block.minecraft.warped_fence": "<PERSON><PERSON>", "block.minecraft.warped_fence_gate": "<PERSON><PERSON>", "block.minecraft.warped_fungus": "<PERSON><PERSON> gomba", "block.minecraft.warped_hanging_sign": "Torz függőtábla", "block.minecraft.warped_hyphae": "<PERSON><PERSON> gombafonalak", "block.minecraft.warped_nylium": "Torz nílium", "block.minecraft.warped_planks": "<PERSON><PERSON>", "block.minecraft.warped_pressure_plate": "<PERSON><PERSON>", "block.minecraft.warped_roots": "Torz gyökerek", "block.minecraft.warped_sign": "<PERSON>z tábla", "block.minecraft.warped_slab": "<PERSON>z <PERSON>", "block.minecraft.warped_stairs": "<PERSON>z lépcső", "block.minecraft.warped_stem": "<PERSON><PERSON>nk", "block.minecraft.warped_trapdoor": "Torz csapóajtó", "block.minecraft.warped_wall_hanging_sign": "Torz fali függőtábla", "block.minecraft.warped_wall_sign": "<PERSON><PERSON>", "block.minecraft.warped_wart_block": "Torz bibircsókblokk", "block.minecraft.water": "Víz", "block.minecraft.water_cauldron": "Vízzel teli üst", "block.minecraft.waxed_chiseled_copper": "Viaszolt vésett rézblokk", "block.minecraft.waxed_copper_block": "Viaszolt rézblokk", "block.minecraft.waxed_copper_bulb": "Viaszolt rézizzó", "block.minecraft.waxed_copper_door": "Viaszolt rézajtó", "block.minecraft.waxed_copper_grate": "Viasz<PERSON>", "block.minecraft.waxed_copper_trapdoor": "Viaszolt réz csapóajtó", "block.minecraft.waxed_cut_copper": "Viaszolt vágott rézblokk", "block.minecraft.waxed_cut_copper_slab": "Viaszolt vágott rézlap", "block.minecraft.waxed_cut_copper_stairs": "Viaszolt vágott rézlépcső", "block.minecraft.waxed_exposed_chiseled_copper": "<PERSON><PERSON><PERSON>, megviselt vésett rézblokk", "block.minecraft.waxed_exposed_copper": "Viaszolt megviselt rézblokk", "block.minecraft.waxed_exposed_copper_bulb": "Viaszolt megviselt rézizzó", "block.minecraft.waxed_exposed_copper_door": "Viaszolt megviselt rézajtó", "block.minecraft.waxed_exposed_copper_grate": "Viaszolt megviselt rézros<PERSON>ly", "block.minecraft.waxed_exposed_copper_trapdoor": "<PERSON><PERSON><PERSON>, megviselt réz csapóajtó", "block.minecraft.waxed_exposed_cut_copper": "<PERSON><PERSON><PERSON>, megviselt vágott rézblokk", "block.minecraft.waxed_exposed_cut_copper_slab": "<PERSON><PERSON><PERSON>, megviselt vágott rézlap", "block.minecraft.waxed_exposed_cut_copper_stairs": "<PERSON><PERSON><PERSON>, megviselt vágott rézlépcső", "block.minecraft.waxed_oxidized_chiseled_copper": "<PERSON><PERSON><PERSON>, patinás vésett rézblokk", "block.minecraft.waxed_oxidized_copper": "Viaszolt patinás rézblokk", "block.minecraft.waxed_oxidized_copper_bulb": "Viaszolt patinás rézizzó", "block.minecraft.waxed_oxidized_copper_door": "Viaszolt patinás rézajtó", "block.minecraft.waxed_oxidized_copper_grate": "Viaszolt patin<PERSON>", "block.minecraft.waxed_oxidized_copper_trapdoor": "<PERSON><PERSON><PERSON>, patinás réz csapóajtó", "block.minecraft.waxed_oxidized_cut_copper": "<PERSON><PERSON><PERSON>, patinás vágott rézblokk", "block.minecraft.waxed_oxidized_cut_copper_slab": "<PERSON><PERSON><PERSON>, pat<PERSON><PERSON> vá<PERSON>t r<PERSON>", "block.minecraft.waxed_oxidized_cut_copper_stairs": "<PERSON><PERSON><PERSON>, patinás vágott rézlépcső", "block.minecraft.waxed_weathered_chiseled_copper": "<PERSON><PERSON><PERSON>, vih<PERSON>vert vésett rézblokk", "block.minecraft.waxed_weathered_copper": "Viaszolt viharvert rézblokk", "block.minecraft.waxed_weathered_copper_bulb": "Viaszolt viharvert ré<PERSON>zzó", "block.minecraft.waxed_weathered_copper_door": "Viaszolt viharvert <PERSON>", "block.minecraft.waxed_weathered_copper_grate": "Viaszolt vihar<PERSON>", "block.minecraft.waxed_weathered_copper_trapdoor": "<PERSON><PERSON><PERSON>, vih<PERSON><PERSON> r<PERSON>ajtó", "block.minecraft.waxed_weathered_cut_copper": "<PERSON><PERSON><PERSON>, vih<PERSON>vert vágott rézblokk", "block.minecraft.waxed_weathered_cut_copper_slab": "<PERSON><PERSON><PERSON>, vih<PERSON><PERSON> v<PERSON>", "block.minecraft.waxed_weathered_cut_copper_stairs": "<PERSON><PERSON><PERSON>, vih<PERSON><PERSON> v<PERSON><PERSON><PERSON> réz<PERSON>cső", "block.minecraft.weathered_chiseled_copper": "Viharvert vésett rézblokk", "block.minecraft.weathered_copper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_bulb": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_grate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON> r<PERSON> c<PERSON>ajtó", "block.minecraft.weathered_cut_copper": "<PERSON><PERSON><PERSON><PERSON> vá<PERSON>t rézblokk", "block.minecraft.weathered_cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "block.minecraft.weathered_cut_copper_stairs": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.weeping_vines": "Keserves indák", "block.minecraft.weeping_vines_plant": "Keserves indaszár", "block.minecraft.wet_sponge": "<PERSON><PERSON>", "block.minecraft.wheat": "Búzatermés", "block.minecraft.white_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_candle_cake": "<PERSON><PERSON> feh<PERSON>", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.white_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON><PERSON> festett üveg", "block.minecraft.white_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON> feste<PERSON> üveglap", "block.minecraft.white_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON><PERSON> tuli<PERSON>", "block.minecraft.white_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wildflowers": "Vadvirágok", "block.minecraft.wither_rose": "Witherr<PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Withercsontváz-koponya", "block.minecraft.wither_skeleton_wall_skull": "Fali withercsontváz-koponya", "block.minecraft.yellow_banner": "Sárga zászló", "block.minecraft.yellow_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_candle_cake": "<PERSON><PERSON> s<PERSON>", "block.minecraft.yellow_carpet": "Sárga szőnyeg", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON> beton", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "<PERSON><PERSON><PERSON> m<PERSON> ker<PERSON>", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass": "Sárga festett üveg", "block.minecraft.yellow_stained_glass_pane": "<PERSON><PERSON>rga festett üveglap", "block.minecraft.yellow_terracotta": "<PERSON><PERSON><PERSON> terrakotta", "block.minecraft.yellow_wool": "<PERSON><PERSON><PERSON> gya<PERSON>", "block.minecraft.zombie_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.zombie_wall_head": "<PERSON><PERSON>", "book.byAuthor": "szerző: %1$s", "book.edit.title": "Könyv szerkesztő oldal", "book.editTitle": "A könyv címe:", "book.finalizeButton": "Aláírás és bezárás", "book.finalizeWarning": "Vigyázz! Ha aláírtad a kö<PERSON>vet, tö<PERSON><PERSON> már nem s<PERSON>.", "book.generation.0": "Eredeti", "book.generation.1": "<PERSON><PERSON><PERSON> m<PERSON>", "book.generation.2": "Másolat m<PERSON>", "book.generation.3": "<PERSON><PERSON><PERSON>", "book.invalid.tag": "* Érvén<PERSON><PERSON> c<PERSON> *", "book.pageIndicator": "Oldal: %1$s/%2$s", "book.page_button.next": "Következő oldal", "book.page_button.previous": "Előző oldal", "book.sign.title": "Könyv aláíró <PERSON>", "book.sign.titlebox": "Cím", "book.signButton": "Aláírás", "book.view.title": "Könyv olvcasó oldal", "build.tooHigh": "Az építés felső határa %s blokk", "chat.cannotSend": "<PERSON><PERSON> elküldeni az üzenetet", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Kattints a teleportáláshoz", "chat.copy": "Másolás vágólapra", "chat.copy.click": "Kattints a vágólapra másoláshoz", "chat.deleted_marker": "Ezt az üzenetet a szerver törölte.", "chat.disabled.chain_broken": "A chat le van tiltva egy megszakadt üzenetlánc miatt. K<PERSON>rj<PERSON>k, pr<PERSON><PERSON><PERSON><PERSON><PERSON>.", "chat.disabled.expiredProfileKey": "A chat a fiókod nyilvános kulcsának lejárta miatt le <PERSON> tilt<PERSON>. K<PERSON><PERSON><PERSON><PERSON><PERSON>, próbá<PERSON>j meg újracsatlakozni.", "chat.disabled.invalid_command_signature": "A parancsnak váratlan vagy hiányos a parancs érv aláírása.", "chat.disabled.invalid_signature": "Egy csevegő üzenet aláírása érvénytelen. Kérjük, próbálj meg új<PERSON>lak<PERSON>ni.", "chat.disabled.launcher": "A csevegő le van tiltva az Indítóból. Az üzenet nem küldhető el.", "chat.disabled.missingProfileKey": "A chat le van tilt<PERSON>, mert a fiókod nyilvános kulcsa hiányzik. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, próbá<PERSON>j meg újracsatlakozni.", "chat.disabled.options": "A csevegő le van tiltva a kliens beállításokban.", "chat.disabled.out_of_order_chat": "Soron kívüli csevegő üzenet érkezett. Megváltozott a rendszeridőd?", "chat.disabled.profile": "A csevegő használata nincs engedélyezve fiókbeállításokban. További információért nyomd meg újra a(z) '%s' billentyűt.", "chat.disabled.profile.moreInfo": "A chat le van tiltva a fiókbeállításokban. Az üzenetek küldése és megjelenítése nem lehetséges.", "chat.editBox": "chat", "chat.filtered": "Szerver által szűrve.", "chat.filtered_full": "A szerver elrejtette az üzenetedet a játékosok egy része elől.", "chat.link.confirm": "Biztos vagy ben<PERSON>, hogy megnyitod a következő weboldalt?", "chat.link.confirmTrusted": "Meg akarod nyitni ezt a linket, vagy a vágólapra szeretnéd másolni?", "chat.link.open": "Megnyitás böngészőben", "chat.link.warning": "Sose nyiss meg linket olya<PERSON><PERSON><PERSON>, akiben nem bízol meg!", "chat.queue": "[+%s függőben lévő sor]", "chat.square_brackets": "[%s]", "chat.tag.error": "A szerver hibás üzenetet küldött.", "chat.tag.modified": "Az üzenetet a szerver módosította. Eredeti:", "chat.tag.not_secure": "Nem <PERSON>gyott üzenet. Nem lehet bejelenteni.", "chat.tag.system": "Szerverüzenet. Nem lehet bejelenteni.", "chat.tag.system_single_player": "Szerverüzenet.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s teljesítette a(z) %s kihívást", "chat.type.advancement.goal": "%s elérte a(z) %s célt", "chat.type.advancement.task": "%s megtette a(z) %s előrelépést", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Csapatüzenet küldése", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s írja: %s", "chat.validation_error": "Hiba a chat ellenőrzésekor", "chat_screen.message": "Elküldendő üzenet: %s", "chat_screen.title": "Chatablak", "chat_screen.usage": "Írd be az üzeneted és nyomj Entert az elküldéshez", "chunk.toast.checkLog": "Részletekért lásd a naplót", "chunk.toast.loadFailure": "A(z) %s chunk nem tölthető be", "chunk.toast.lowDiskSpace": "Kevés a szabad tá<PERSON>hely!", "chunk.toast.lowDiskSpace.description": "<PERSON><PERSON><PERSON>, hogy a világot nem si<PERSON>ül menteni.", "chunk.toast.saveFailure": "A(z) %s chunk nem menthető el", "clear.failed.multiple": "<PERSON>em <PERSON> tárgyak %s játékosnál", "clear.failed.single": "Nem voltak tárgyak %s játékosnál", "color.minecraft.black": "Fekete", "color.minecraft.blue": "<PERSON><PERSON><PERSON>", "color.minecraft.brown": "<PERSON><PERSON>", "color.minecraft.cyan": "Türkizkék", "color.minecraft.gray": "Szürke", "color.minecraft.green": "<PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Világoskék", "color.minecraft.light_gray": "Világosszürke", "color.minecraft.lime": "Világoszöld", "color.minecraft.magenta": "Bíbor", "color.minecraft.orange": "Narancssárga", "color.minecraft.pink": "Róz<PERSON><PERSON><PERSON>", "color.minecraft.purple": "<PERSON>", "color.minecraft.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.white": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.yellow": "<PERSON><PERSON><PERSON>", "command.context.here": "<--[ITT]", "command.context.parse_error": "%s a(z) %s. pozíciónál: %s", "command.exception": "Parancsértelmezés sikertelen: %s", "command.expected.separator": "Hiányzó szóköz az argumentumok között", "command.failed": "Váratlan hiba történt a parancs futtatásakor", "command.forkLimit": "Végrehajtási környezetek maximális s<PERSON> (%s) elérve", "command.unknown.argument": "Hibás parancsargumentum", "command.unknown.command": "Ismeretlen vagy befeje<PERSON>n parancs, l<PERSON><PERSON> le<PERSON> a hib<PERSON>t", "commands.advancement.criterionNotFound": "A(z) %1$s előrelépésnek nem része a(z) '%2$s' kritérium", "commands.advancement.grant.criterion.to.many.failure": "%3$s játékos már teljesítette a(z) %2$s előrelépés '%1$s' kritériumát", "commands.advancement.grant.criterion.to.many.success": "%3$s játékos megkapta a(z) %2$s előrelépés '%1$s' kritériumát", "commands.advancement.grant.criterion.to.one.failure": "%3$s már teljesítette a(z) %2$s előrelépés '%1$s' kritériumát", "commands.advancement.grant.criterion.to.one.success": "%3$s megkapta a(z) %2$s előrelépés '%1$s' kritériumát", "commands.advancement.grant.many.to.many.failure": "%2$s játékos már teljesítette a(z) %1$s előrelépéseket", "commands.advancement.grant.many.to.many.success": "%2$s játékos megkapta a(z) %1$s előrelépéseket", "commands.advancement.grant.many.to.one.failure": "%2$s már teljesítette a(z) %1$s előrelépéseket", "commands.advancement.grant.many.to.one.success": "%2$s megkapta a(z) %1$s előrelépéseket", "commands.advancement.grant.one.to.many.failure": "%2$s játékos már teljesítette a(z) %1$s előrelépést", "commands.advancement.grant.one.to.many.success": "%2$s játékos megkapta a(z) %1$s előrelépést", "commands.advancement.grant.one.to.one.failure": "%2$s már teljesítette a(z) %1$s előrelépést", "commands.advancement.grant.one.to.one.success": "%2$s megkapta a(z) %1$s előrelépést", "commands.advancement.revoke.criterion.to.many.failure": "%3$s játékos még nem teljesítette a(z) %2$s előrelépés '%1$s' kritériumát", "commands.advancement.revoke.criterion.to.many.success": "%3$s játékos elvesztette a(z) %2$s előrelépés '%1$s' kritériumát", "commands.advancement.revoke.criterion.to.one.failure": "%3$s még nem teljesítette a(z) %2$s előrelépés '%1$s' kritériumát", "commands.advancement.revoke.criterion.to.one.success": "%3$s elvesztette a(z) %2$s előrelépés '%1$s' kritériumát", "commands.advancement.revoke.many.to.many.failure": "%2$s játékos még nem teljesítette a(z) %1$s előrelépéseket", "commands.advancement.revoke.many.to.many.success": "%2$s játékos elvesztett %1$s előrelépést", "commands.advancement.revoke.many.to.one.failure": "%2$s még nem teljesítette a(z) %1$s előrelépéseket", "commands.advancement.revoke.many.to.one.success": "%2$s elvesztett %1$s előrelépést", "commands.advancement.revoke.one.to.many.failure": "%2$s játékos még nem teljesítette a(z) %1$s előrelépést", "commands.advancement.revoke.one.to.many.success": "%2$s játékos elvesztette a(z) %1$s előrelépést", "commands.advancement.revoke.one.to.one.failure": "%2$s még nem teljesítette a(z) %1$s előrelépést", "commands.advancement.revoke.one.to.one.success": "%2$s elvesztette a(z) %1$s előrelépést", "commands.attribute.base_value.get.success": "%2$s entitás %1$s tulajdonságának alapértéke %3$s", "commands.attribute.base_value.reset.success": "%2$s entitás %1$s tulajdonságának alapértéke mostantól újra %3$s", "commands.attribute.base_value.set.success": "%2$s entitás %1$s tulajdonságának alapértéke mostantól %3$s", "commands.attribute.failed.entity": "%s nem érvényes entitás ehhez a parancshoz", "commands.attribute.failed.modifier_already_present": "%3$s entitás %2$s attribútumán már szerepel %1$s módosító", "commands.attribute.failed.no_attribute": "%s entitáshoz nem tartozik %s tulajdonság", "commands.attribute.failed.no_modifier": "%2$s entitás %1$s tulajdonságának nincs %3$s módosítója", "commands.attribute.modifier.add.success": "%s módosító hozzáadva %3$s entitás %2$s tulajdonságához", "commands.attribute.modifier.remove.success": "%s módosító eltávolítva %3$s entitás %2$s tulajdonságáról", "commands.attribute.modifier.value.get.success": "%3$s entitás %2$s tulajdonságán szereplő %1$s módosító értéke %4$s", "commands.attribute.value.get.success": "%2$s entitás %1$s tulajdonságának értéke %3$s", "commands.ban.failed": "<PERSON><PERSON>i sem vált<PERSON>: a játékos már ki van <PERSON>", "commands.ban.success": "%s kitiltva: %s", "commands.banip.failed": "<PERSON><PERSON>i sem változott: ez az IP-cím már ki <PERSON>", "commands.banip.info": "Ez a tiltás %s játékosra vonatkozik: %s", "commands.banip.invalid": "Érvénytelen IP-cím vagy ismeretlen j<PERSON>", "commands.banip.success": "A(z) %s IP-cím kitiltva: %s", "commands.banlist.entry": "%s kitiltva %s által: %s", "commands.banlist.entry.unknown": "(Ismeretlen)", "commands.banlist.list": "%s <PERSON><PERSON>ény<PERSON>:", "commands.banlist.none": "<PERSON><PERSON> sin<PERSON>ilt<PERSON>", "commands.bossbar.create.failed": "Már létezik főellenség-jelzőcsík '%s' azonosítóval", "commands.bossbar.create.success": "%s főellenség-jelzőcsík létrehozva", "commands.bossbar.get.max": "A(z) %s főellenség-jelzőcsík maximuma %s", "commands.bossbar.get.players.none": "A(z) %s főellenség-jelzőcsíkhoz nem tartoznak bejelentkezett játékosok", "commands.bossbar.get.players.some": "A(z) %s főellenség-jelzőcsíkhoz %s bejelentkezett játékos tartozik: %s", "commands.bossbar.get.value": "A(z) %s főellenség-jelzőcsík értéke %s", "commands.bossbar.get.visible.hidden": "A(z) %s főellenség-jelzőcsík rejtett", "commands.bossbar.get.visible.visible": "A(z) %s főellenség-jelzőcsík látható", "commands.bossbar.list.bars.none": "Nincsenek aktív egyéni főellenség-jelzőcsíkok", "commands.bossbar.list.bars.some": "%s egyéni főellenség-jelzőcsík aktív: %s", "commands.bossbar.remove.success": "%s főellenség-jelzőcsík eltávolítva", "commands.bossbar.set.color.success": "A(z) %s főellenség-jelzőcsík átszínezve", "commands.bossbar.set.color.unchanged": "Semmi sem változott: ez a főellenség-jelzőcsík már ilyen színű", "commands.bossbar.set.max.success": "A(z) %s főellenség-jelzőcsík maximuma %s értékre <PERSON>llítva", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON>i sem v<PERSON>lt<PERSON>: ennek a főellenség-jelzőcsíknak már ez a maximuma", "commands.bossbar.set.name.success": "A(z) %s főellenség-jelzőcsík átnevezve", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON>i sem v<PERSON>lt<PERSON>: ennek a főellenség-jelzőcsíknak már ez a neve", "commands.bossbar.set.players.success.none": "A(z) %s főellenség-jelzőcsíkhoz már nem tartozik játékos", "commands.bossbar.set.players.success.some": "A(z) %s főellenség-jelzőcsíkhoz most %s játékos tartozik: %s", "commands.bossbar.set.players.unchanged": "Semmi sem változott: már pontosan ezek a játékosok látják a jelzőcs<PERSON>ot, senkit sem kell hozzáadni vagy eltávolítani", "commands.bossbar.set.style.success": "A(z) %s főellenség-jelzőcsík stílusa megváltoztatva", "commands.bossbar.set.style.unchanged": "Semmi sem változott: ez a főellenség-jelzőcsík már ilyen stílusú", "commands.bossbar.set.value.success": "A(z) %s főellenség-jelzőcsík %s értékre állítva", "commands.bossbar.set.value.unchanged": "Semmi sem vált<PERSON>: ennek a főellenség-jelzőcsíknak már ez az értéke", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON>mmi sem vált<PERSON>: ez a főellenség-jelző<PERSON><PERSON> már rejtett", "commands.bossbar.set.visibility.unchanged.visible": "Semmi sem változott: ez a főellenség-jelzőcsík már lá<PERSON>tó", "commands.bossbar.set.visible.success.hidden": "A(z) %s főellenség-jelzőcsík most rejtett", "commands.bossbar.set.visible.success.visible": "A(z) %s főellenség-jelzőcsík most látható", "commands.bossbar.unknown": "Nem létezik főellenség-jelzőcsík '%s' azonosítóval", "commands.clear.success.multiple": "%s tárgy eltávolítva %s játékos eszköztárából", "commands.clear.success.single": "%s tárgy eltávolítva %s eszköztárából", "commands.clear.test.multiple": "%s ilyen tárgy található %s játékos eszköztárában", "commands.clear.test.single": "%s ilyen tárgy található %s eszköztárában", "commands.clone.failed": "<PERSON>gy blokk sem lett lemásolva", "commands.clone.overlap": "A forrás- és a célterület nem fedheti át egymást", "commands.clone.success": "%s blokk sikeresen lemásolva", "commands.clone.toobig": "Túl sok blokk van a megadott területen (%s a maximum, %s megadva)", "commands.damage.invulnerable": "A célpont nem képes sérülni a megadott sebzéstípustól", "commands.damage.success": "%s sebzés okozva %s célpontnak", "commands.data.block.get": "A(z) %2$s, %3$s, %4$s helyen lévő blokk '%1$s' tulajdonsága %5$s szorzóval %6$s", "commands.data.block.invalid": "A célblokk nem blokkentitás", "commands.data.block.modified": "A(z) %s, %s, %s he<PERSON>en lévő blokk adatai módosítva", "commands.data.block.query": "A(z) %s, %s, %s helyen lévő blokk adatai: %s", "commands.data.entity.get": "%2$s '%1$s' tulajdonsága %3$s szorzóval %4$s", "commands.data.entity.invalid": "A játékos adatai nem módosíthatóak", "commands.data.entity.modified": "%s entitás adatai módosítva", "commands.data.entity.query": "%s entitás adatai a következők: %s", "commands.data.get.invalid": "%s nem érhető el; csak számcímkék engedélyezettek", "commands.data.get.multiple": "Ez az argumentum csak egy NBT-értéket fogad el", "commands.data.get.unknown": "%s nem érhető el; a címke nem létezik", "commands.data.merge.failed": "Semmi sem változott. A megadott tulajdonságok már ezekkel az értékekkel rendelkeznek", "commands.data.modify.expected_list": "Hiányzó lista ehelyett: %s", "commands.data.modify.expected_object": "Hiányzó objektum ehelyett: %s", "commands.data.modify.expected_value": "Hi<PERSON><PERSON><PERSON><PERSON>: %s", "commands.data.modify.invalid_index": "Érvénytelen listaindex: %s", "commands.data.modify.invalid_substring": "Érvénytelen substring indexek %s-tól/től %s-ig", "commands.data.storage.get": "%2$s tároló '%1$s' tulajdonsága %3$s szorzóval %4$s", "commands.data.storage.modified": "%s tároló módosítva", "commands.data.storage.query": "%s tá<PERSON>ó tartalma: %s", "commands.datapack.create.already_exists": "<PERSON><PERSON><PERSON> l<PERSON>te<PERSON>k egy '%s' nevű adatcsomag", "commands.datapack.create.invalid_full_name": "Ér<PERSON><PERSON><PERSON><PERSON> adatcsomagnév: '%s'", "commands.datapack.create.invalid_name": "Érvénytelen karakterek az adatcsomag új nevében: '%s'", "commands.datapack.create.io_failure": "Nem lehet létrehozni datapack-et \"%s\" néven, ellenőrizd le a naplókat", "commands.datapack.create.metadata_encode_failure": "A(z) '%s' nevű adatcsomag metaadatait nem sikerült kódolni: %s", "commands.datapack.create.success": "Új üres adatcsomag \"%s\" nével sikeresen létrehozva", "commands.datapack.disable.failed": "A(z) '%s' csomag már ki van kapcsolva!", "commands.datapack.disable.failed.feature": "A(z) '%s' adatcsomag nem tilt<PERSON>, mivel r<PERSON>ze egy aktív kapcsolónak!", "commands.datapack.enable.failed": "A(z) '%s' csomag már be van ka<PERSON>ol<PERSON>!", "commands.datapack.enable.failed.no_flags": "A(z) '%s' adatcsomag nem engedélyezhető, mivel a szükséges kapcsolók nem aktívak ebben a világban: %s!", "commands.datapack.list.available.none": "<PERSON><PERSON><PERSON> tö<PERSON> el<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>cs<PERSON>", "commands.datapack.list.available.success": "Összesen %s adatcsomag elérhető: %s", "commands.datapack.list.enabled.none": "Nincsenek bekapcsolt adatcsomagok", "commands.datapack.list.enabled.success": "Jelenleg %s adatcsomagok vannak engedélyezve: %s", "commands.datapack.modify.disable": "%s adat<PERSON><PERSON><PERSON> let<PERSON>", "commands.datapack.modify.enable": "%s adat<PERSON><PERSON>g engedé<PERSON>ezve", "commands.datapack.unknown": "Ismeretlen adatcsomag: '%s'", "commands.debug.alreadyRunning": "A tickmérés már fut", "commands.debug.function.noRecursion": "<PERSON><PERSON> le<PERSON> k<PERSON> indítani el<PERSON><PERSON> be<PERSON>", "commands.debug.function.noReturnRun": "A követés nem használható a „return run” alparancsra", "commands.debug.function.success.multiple": "%s parancs nyomon követése %s eljárásból elmentve a(z) %s fájlba", "commands.debug.function.success.single": "%s parancs nyomon követése a(z) „%s” eljárásból elmentve a(z) %s fájlba", "commands.debug.function.traceFailed": "<PERSON><PERSON> követni az eljárás le<PERSON>", "commands.debug.notRunning": "A tickmérés nincs elindítva", "commands.debug.started": "Teljesítményvizsgálat el lett indítva", "commands.debug.stopped": "Vizsgálat leállítva %s másodperc és %s tick után (%s tick másodpercenként)", "commands.defaultgamemode.success": "Mostantól a %s az alapértelmezett játékmód", "commands.deop.failed": "Se<PERSON>i sem változott: a játékos nem operátor", "commands.deop.success": "%s többé nem operátor a szerveren", "commands.dialog.clear.multiple": "Párbeszédablak törölve ezeknek a játékosoknak: %s", "commands.dialog.clear.single": "Párbeszédablak törölve ennek: %s", "commands.dialog.show.multiple": "Párbeszédablak megjelenítve ezeknek a játékosoknak: %s", "commands.dialog.show.single": "Párbeszédablak megjelenítve ennek: %s", "commands.difficulty.failure": "A nehézség nem változott: már %s foko<PERSON><PERSON> van állítva", "commands.difficulty.query": "A jelenlegi nehézségi fokozat %s", "commands.difficulty.success": "A nehézségi fokozat átállítva: %s", "commands.drop.no_held_items": "Az entitás semmit sem tud kézben tartani", "commands.drop.no_loot_table": "%s entitáshoz nem tartozik zsákmányeloszlás", "commands.drop.no_loot_table.block": "%s blokkhoz nem tartozik zsákmányeloszlás", "commands.drop.success.multiple": "%s t<PERSON><PERSON>", "commands.drop.success.multiple_with_table": "%s tárgy eldobva a(z) %s eloszlás szerint", "commands.drop.success.single": "%s %s eldobva", "commands.drop.success.single_with_table": "%s %s eldobva a(z) %s eloszlás szerint", "commands.effect.clear.everything.failed": "A célponton nincs eltávolítható hatás", "commands.effect.clear.everything.success.multiple": "Minden hatás eltávolítva %s célpontról", "commands.effect.clear.everything.success.single": "Minden hatás eltávolítva %s játékosról", "commands.effect.clear.specific.failed": "A célpont nem áll a megadott hatás alatt", "commands.effect.clear.specific.success.multiple": "%s hatás eltávolítva %s célpontról", "commands.effect.clear.specific.success.single": "%s hatás eltávolítva %s játékosról", "commands.effect.give.failed": "Ez a hatás nem alkalmazható (a célpont vagy immunis a hatásokra, vagy egy erősebb hatás alatt <PERSON>ll)", "commands.effect.give.success.multiple": "%s hatás alkalmazva %s célpontra", "commands.effect.give.success.single": "%s hatás alkalmazva %s játékosra", "commands.enchant.failed": "Semmi sem tört<PERSON>t: a célpontok nem tartanak semmi olyat a kezükben, amire ez a varázslat ráillik", "commands.enchant.failed.entity": "%s nem érvényes entitás ehhez a parancshoz", "commands.enchant.failed.incompatible": "Ez a varázslat nem szerepelhet %s tárgyon", "commands.enchant.failed.itemless": "%s nem tart semmit a kezében", "commands.enchant.failed.level": "Ez a varázslat nem lehet %s. szintű; a maximális szint %s", "commands.enchant.success.multiple": "%s varázslat alkalmazva %s entitásra", "commands.enchant.success.single": "%s varázslat alkalmazva %s kézben tartott tárgyára", "commands.execute.blocks.toobig": "Túl sok blokk van a megadott területen (%s a maximum, %s megadva)", "commands.execute.conditional.fail": "A feltétel nem teljesült", "commands.execute.conditional.fail_count": "A feltétel %s alkalommal nem teljesült", "commands.execute.conditional.pass": "A feltétel teljesült", "commands.execute.conditional.pass_count": "A feltétel %s alkalommal teljesült", "commands.execute.function.instantiationFailure": "<PERSON><PERSON> példányosítani a(z) %s eljárást: %s", "commands.experience.add.levels.success.multiple": "%2$s játékos megkapott %1$s tapasztalati szintet", "commands.experience.add.levels.success.single": "%2$s megkapott %1$s tapasztalati szintet", "commands.experience.add.points.success.multiple": "%2$s játékos megkapott %1$s tapasztalati pontot", "commands.experience.add.points.success.single": "%2$s megkapott %1$s tapasztalati pontot", "commands.experience.query.levels": "%s játékosnak %s tapasztalati szintje van", "commands.experience.query.points": "%s játékosnak %s tapasztalati pontja van", "commands.experience.set.levels.success.multiple": "%2$s játékos tapasztalata %1$s. szintre állítva", "commands.experience.set.levels.success.single": "%2$s tapasztalata %1$s. szintre állítva", "commands.experience.set.points.invalid": "A tapasztalati pontok száma nem állítható a játékos jelenlegi szintjének maximuma fölé", "commands.experience.set.points.success.multiple": "%2$s játékos tapasztalata %1$s pontra állítva", "commands.experience.set.points.success.single": "%2$s tapasztalata %1$s pontra állítva", "commands.fill.failed": "<PERSON>gy blokk sem lett kitöltve", "commands.fill.success": "%s blokk sikeresen kitöltve", "commands.fill.toobig": "Túl sok blokk van a megadott területen (%s a maximum, %s megadva)", "commands.fillbiome.success": "Biom módosítva %s, %s, %s és %s, %s, %s között", "commands.fillbiome.success.count": "%s biombejegyzés módosítva %s, %s, %s és %s, %s, %s között", "commands.fillbiome.toobig": "Túl sok blokk van a megadott területen (%s a maximum, %s megadva)", "commands.forceload.added.failure": "Egy chunk sem lett maradandóként megjelölve", "commands.forceload.added.multiple": "A(z) %2$s dimenzió %1$s chunkja %s és %s között megjelölve maradandóan betöltendőként", "commands.forceload.added.none": "Nem <PERSON>ó maradandóan betöltődő chunk a(z) %s dimenzióban", "commands.forceload.added.single": "A(z) %2$s dimenzió %1$s chunkja megjelölve maradandóan betöltendőként", "commands.forceload.list.multiple": "%s maradandóan betöltődő chunk található a(z) %s dimenzióban itt: %s", "commands.forceload.list.single": "Egy maradandóan betöltődő chunk található a(z) %s dimenzióban itt: %s", "commands.forceload.query.failure": "A(z) %2$s dimenzió %1$s chunkja nem maradandóan betöltendő", "commands.forceload.query.success": "A(z) %2$s dimenzió %1$s chunkja maradandóan betöltendő", "commands.forceload.removed.all": "Minden maradandóan betöltődő chunk eltávolítva a(z) %s dimenzióban", "commands.forceload.removed.failure": "<PERSON><PERSON> ma<PERSON><PERSON> betöltendő chunk sem lett eltávolítva", "commands.forceload.removed.multiple": "A(z) %2$s dimenzió %1$s chunkja %s és %s között nincs többé megjelölve maradandóan betöltendőként", "commands.forceload.removed.single": "A(z) %2$s dimenzió %1$s chunkja nincs többé megjelölve maradandóan betöltendőként", "commands.forceload.toobig": "Túl sok chunk van a megadott területen (maximum %s, %s meghatározott)", "commands.function.error.argument_not_compound": "Érvénytelen paraméter: %s, várt ért<PERSON>k: Összetett", "commands.function.error.missing_argument": "Hiányzó %2$s paraméter a(z) %1$s eljáráshoz", "commands.function.error.missing_arguments": "Hiányzó paraméterek a(z) %s eljáráshoz", "commands.function.error.parse": "A macro telepítése közben %s: Parancs \"%s\" hiba: %s", "commands.function.instantiationFailure": "<PERSON><PERSON> példányosítani a(z) %s eljárást: %s", "commands.function.result": "A(z) '%s' eljárás %s <PERSON>rt<PERSON><PERSON><PERSON> tért vissza", "commands.function.scheduled.multiple": "%s eljárások futtatása", "commands.function.scheduled.no_functions": "Nem <PERSON>ó %s nevű eljárás", "commands.function.scheduled.single": "%s el<PERSON><PERSON><PERSON><PERSON> futtat<PERSON>a", "commands.function.success.multiple": "%s parancs végrehajtva %s eljárásból", "commands.function.success.multiple.result": "%s eljárás végrehajtva", "commands.function.success.single": "%s parancs végrehajtva a(z) „%s” eljárásból", "commands.function.success.single.result": "A(z) '%2$s' eljárás %1$s ért<PERSON>kkel tért vissza", "commands.gamemode.success.other": "%s játékmódja átállítva %sra", "commands.gamemode.success.self": "Saját játékmód átállítva %sra", "commands.gamerule.query": "A(z) %s játékszabály jelenlegi értéke: %s", "commands.gamerule.set": "A(z) %s játékszabály értéke mostantól: %s", "commands.give.failed.toomanyitems": "%2$s tárgyból maximum %1$s adható", "commands.give.success.multiple": "%3$s játékos megkapott %1$s %2$s tárgyat", "commands.give.success.single": "%3$s megkapott %1$s %2$s tárgyat", "commands.help.failed": "Ismeretlen parancs vagy nem megfelelő jogosultság", "commands.item.block.set.success": "Egy rekesz tartalma a(z) %s, %s, %s helyen k<PERSON> %s tárgyra", "commands.item.entity.set.success.multiple": "%s entitás egy-egy rekeszének tartalma kicserélve %s tárgyra", "commands.item.entity.set.success.single": "%s egy rekeszének tartalma kicserélve %s tárgyra", "commands.item.source.no_such_slot": "A forrásnak nincs '%s' rekesze", "commands.item.source.not_a_container": "A forrásblokk a(z) %s, %s, %s koordinátákon nem tároló", "commands.item.target.no_changed.known_item": "<PERSON><PERSON> c<PERSON>pont sem fogadta el a(z) %s tárgyat a(z) %s rekeszbe", "commands.item.target.no_changes": "<PERSON><PERSON> c<PERSON> sem fogadta el a tárgyat a(z) %s rekeszbe", "commands.item.target.no_such_slot": "A célpontnak nincs '%s' rekesze", "commands.item.target.not_a_container": "A célpozíció a(z) %s, %s, %s koordinátákon nem egy tároló", "commands.jfr.dump.failed": "A JFR-elemzés eredményét nem sikerült menteni: %s", "commands.jfr.start.failed": "A JFR-elemzés indítása sikertelen", "commands.jfr.started": "JFR-elemzés elindítva", "commands.jfr.stopped": "JFR-elemzés leállítva és mentve %s néven", "commands.kick.owner.failed": "LAN játékban nem lehet kirúgni a szerver tulajdonosát", "commands.kick.singleplayer.failed": "Offline egyjátékos módban nem lehet kirúgni senkit", "commands.kick.success": "%s kirúgva: %s", "commands.kill.success.multiple": "%s entitás megölve", "commands.kill.success.single": "%s megölve", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "%s j<PERSON><PERSON><PERSON> van online a(z) %s maximumból: %s", "commands.locate.biome.not_found": "Nem <PERSON> \"%s\" típusú biom ésszerű távolságon belül", "commands.locate.biome.success": "A legközelebbi %s biom a(z) %s koordinátákon található (%s blokk távolságban)", "commands.locate.poi.not_found": "Nem <PERSON> \"%s\" típus ésszerű távolságon belül", "commands.locate.poi.success": "A legközelebbi %s a(z) %s koordinátákon található (%s blokk távolságban)", "commands.locate.structure.invalid": "Nem l<PERSON> \"%s\" típusú struktúra", "commands.locate.structure.not_found": "Nem <PERSON> \"%s\" típusú struktúra a közelben", "commands.locate.structure.success": "A legközelebbi %s építmény a(z) %s koordinátákon található (%s blokk távolságban)", "commands.message.display.incoming": "%s suttog neked: %s", "commands.message.display.outgoing": "Suttogsz %s játékosnak: %s", "commands.op.failed": "<PERSON><PERSON>i sem változott: a játékos már operátor", "commands.op.success": "%s mostantól operátor a szerveren", "commands.pardon.failed": "<PERSON><PERSON>i sem változott: a játékos nincs kitiltva", "commands.pardon.success": "%s tiltása feloldva", "commands.pardonip.failed": "<PERSON><PERSON>i sem változott: ez az IP-cím nincs kit<PERSON>va", "commands.pardonip.invalid": "Érvénytelen IP-cím", "commands.pardonip.success": "A(z) %s IP-cím tiltása feloldva", "commands.particle.failed": "A részecske senki számára sem volt látható", "commands.particle.success": "%s részecske megjelenítve", "commands.perf.alreadyRunning": "A teljesítményvizsgálat már fut", "commands.perf.notRunning": "A teljesítményvizsgálat nincs elindítva", "commands.perf.reportFailed": "A hibajelentés létrehozása nem sikerült", "commands.perf.reportSaved": "Hibajelentés létrehozva itt: %s", "commands.perf.started": "10 másodperces teljesítményvizsgálat elindítva (használd a '/perf stop' parancsot a megszakításhoz)", "commands.perf.stopped": "Teljesítményvizsgálat leállítva %s másodperc és %s tick után (%s tick másodpercenként)", "commands.place.feature.failed": "<PERSON><PERSON> elhelyezni a tereptárgyat", "commands.place.feature.invalid": "<PERSON>em l<PERSON> \"%s\" típusú tereptárgy", "commands.place.feature.success": "\"%s\" elhelyezve %s, %s, %s koordinátákon", "commands.place.jigsaw.failed": "A kirakósdarab generálása sikertelen", "commands.place.jigsaw.invalid": "Nem l<PERSON> \"%s\" típusú sablonhalmaz", "commands.place.jigsaw.success": "Kirakósdarab elhelyezve %s, %s, %s koordinátákon", "commands.place.structure.failed": "<PERSON><PERSON> elhelyezni a struktúrát", "commands.place.structure.invalid": "Nem l<PERSON> \"%s\" típusú struktúra", "commands.place.structure.success": "\"%s\"struktúra létrehozva %s, %s, %s koordinátákon", "commands.place.template.failed": "<PERSON><PERSON> elhelyezni a sablont", "commands.place.template.invalid": "Nem l<PERSON> \"%s\" nevű sablon", "commands.place.template.success": "\"%s\" sablon betöltve %s, %s, %s koordinátákon", "commands.playsound.failed": "A hangforrás hallótávolságon kívül esik", "commands.playsound.success.multiple": "%s hang lej<PERSON>zva %s játékosnak", "commands.playsound.success.single": "%s hang lej<PERSON>zva %s játékosnak", "commands.publish.alreadyPublished": "<PERSON><PERSON><PERSON> meg van osztva többszemélyes játék a(z) %s számú porton", "commands.publish.failed": "<PERSON><PERSON><PERSON> j<PERSON>k kiszolgálása sikertelen", "commands.publish.started": "Helyi játék megosztva a következő porton: %s", "commands.publish.success": "Többszemélyes játék megosztva a(z) %s számú porton", "commands.random.error.range_too_large": "A véletlenszerű érték korlátja legfeljebb 2147483646 lehet", "commands.random.error.range_too_small": "A véletlenszerű érték korlátjának legalább 2-nek kell lennie", "commands.random.reset.all.success": "%s véletlenszerű szekvencia(k) visszaállítása", "commands.random.reset.success": "Véletlenszerű szekvencia visszaállítása %s", "commands.random.roll": "%s sorsolva %s (%s és %s közt)", "commands.random.sample.success": "Véletlenszerű érték: %s", "commands.recipe.give.failed": "<PERSON><PERSON> sem tanult új receptet", "commands.recipe.give.success.multiple": "%s recept feloldva %s játékos számára", "commands.recipe.give.success.single": "%s recept feloldva %s számára", "commands.recipe.take.failed": "Senki sem felejtett el meglévő receptet", "commands.recipe.take.success.multiple": "%s recept lezárva %s játékos számára", "commands.recipe.take.success.single": "%s recept lezárva %s számára", "commands.reload.failure": "Az újratöltés sikertelen; a régi adatok megmaradnak", "commands.reload.success": "Újratöltés!", "commands.ride.already_riding": "%s már %s entitáson ül", "commands.ride.dismount.success": "%s leszállt %s entitásról", "commands.ride.mount.failure.cant_ride_players": "A játékosokra nem lehet felülni", "commands.ride.mount.failure.generic": "%s nem tudott felülni %s entitásra", "commands.ride.mount.failure.loop": "Az entitás nem ülhet saj<PERSON> mag<PERSON>, sem az utasain", "commands.ride.mount.failure.wrong_dimension": "Az entitás nem ülhet egy más dimenzióban lévő járművön", "commands.ride.mount.success": "%s felült %s entitásra", "commands.ride.not_riding": "%s nem <PERSON>l semmilyen j<PERSON>űvön", "commands.rotate.success": "%s elforgatva", "commands.save.alreadyOff": "A mentés már ki van <PERSON>", "commands.save.alreadyOn": "A mentés már be van <PERSON>", "commands.save.disabled": "Automatikus men<PERSON> k<PERSON>", "commands.save.enabled": "Automat<PERSON>us men<PERSON>", "commands.save.failed": "<PERSON><PERSON> menteni a j<PERSON> (van elég hely a lemezen?)", "commands.save.saving": "<PERSON><PERSON><PERSON><PERSON> mentése (ez eltarthat egy kis ideig!)", "commands.save.success": "Játék elmentve", "commands.schedule.cleared.failure": "Nincs ütemezés %s azonosítóval", "commands.schedule.cleared.success": "%s ütemezés törölve %s azonosítóval", "commands.schedule.created.function": "„%s” el<PERSON><PERSON><PERSON><PERSON> beütemezve a játék szerinti %3$s időre, %2$s tick múlva", "commands.schedule.created.tag": "„%s” címke beütemezve a játék szerinti %3$s időre, %2$s tick múlva", "commands.schedule.macro": "<PERSON>em le<PERSON>éges makrót ütemezni", "commands.schedule.same_tick": "A jelenlegi tickre nem t<PERSON>z ütemezni", "commands.scoreboard.objectives.add.duplicate": "Már létezik ilyen nevű cél", "commands.scoreboard.objectives.add.success": "%s cél létrehozva", "commands.scoreboard.objectives.display.alreadyEmpty": "Semmi sem változott: ez a megjelenítési hely már <PERSON>", "commands.scoreboard.objectives.display.alreadySet": "<PERSON><PERSON>i sem vált<PERSON>: ezen a helyen már meg van jelenítve ez a cél", "commands.scoreboard.objectives.display.cleared": "Mindegyik cél eltávolítva a(z) %s helyről", "commands.scoreboard.objectives.display.set": "'%2$s' cél megjelenítve a(z) %1$s helyen", "commands.scoreboard.objectives.list.empty": "<PERSON><PERSON><PERSON><PERSON> célok", "commands.scoreboard.objectives.list.success": "%s cél létezik: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "%s cél kijelzésének frissítése kikapcsolva", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "%s cél kijelzésének frissítése bekapcsolva", "commands.scoreboard.objectives.modify.displayname": "%s cél megjelenített neve mostantól %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "%s cél alapértelmezett számformátuma törölve", "commands.scoreboard.objectives.modify.objectiveFormat.set": "%s cél alapértelmezett számformátuma megváltoztatva", "commands.scoreboard.objectives.modify.rendertype": "%s cél megjelenítési módja megváltoztatva", "commands.scoreboard.objectives.remove.success": "%s cél eltávolítva", "commands.scoreboard.players.add.success.multiple": "%3$s entitás '2$s' értéke megnövelve %1$s értékkel", "commands.scoreboard.players.add.success.single": "%3$s '2$s' értéke megnövelve %1$s értékkel (most 4$s)", "commands.scoreboard.players.display.name.clear.success.multiple": "%s entitás megjelenített neve törölve %s cél alatt", "commands.scoreboard.players.display.name.clear.success.single": "%s megjelenített neve törölve %s cél alatt", "commands.scoreboard.players.display.name.set.success.multiple": "%2$s entitás megjelenített neve mostantól '%1$s' %s cél alatt", "commands.scoreboard.players.display.name.set.success.single": "%2$s megjelenített neve mostantól '%1$s' %s cél alatt", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "%s entitás pontszámának formátuma törölve %s cél alatt", "commands.scoreboard.players.display.numberFormat.clear.success.single": "%s pontszámának formátuma törölve %s cél alatt", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "%s entitás pontszámának formátuma megváltoztatva %s cél alatt", "commands.scoreboard.players.display.numberFormat.set.success.single": "%s pontszámának formátuma megváltoztatva %s cél alatt", "commands.scoreboard.players.enable.failed": "<PERSON>mmi sem változott: ez a trigger már en<PERSON><PERSON><PERSON><PERSON>ett", "commands.scoreboard.players.enable.invalid": "Az \"enable\" parancs csak trigger-célokra alkalmazható", "commands.scoreboard.players.enable.success.multiple": "%s trigger engedélyezve %s entitás számára", "commands.scoreboard.players.enable.success.single": "%s trigger engedélyezve %s számára", "commands.scoreboard.players.get.null": "Nem ér<PERSON> el %2$s játékos %1$s pontszáma, mert nincs be<PERSON>", "commands.scoreboard.players.get.success": "%s já<PERSON>kos '%3$s' értéke %2$s", "commands.scoreboard.players.list.empty": "Nincsenek követett entitások", "commands.scoreboard.players.list.entity.empty": "%s entitásnak nincs pontsz<PERSON>ma", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s entitásnak %s pontszáma van:", "commands.scoreboard.players.list.success": "%s entitás áll követés alatt: %s", "commands.scoreboard.players.operation.success.multiple": "%2$s entitás '%1$s' értéke frissítve", "commands.scoreboard.players.operation.success.single": "%2$s j<PERSON><PERSON><PERSON> '1$s' <PERSON><PERSON><PERSON><PERSON> most '3$s'", "commands.scoreboard.players.remove.success.multiple": "%3$s entitás '2$s' értéke csökkentve %1$s értékkel", "commands.scoreboard.players.remove.success.single": "%3$s '2$s' értéke csökkentve %1$s értékkel (most 4$s)", "commands.scoreboard.players.reset.all.multiple": "%s entitás minden pontszáma <PERSON>", "commands.scoreboard.players.reset.all.single": "%s <PERSON>en p<PERSON><PERSON><PERSON><PERSON>", "commands.scoreboard.players.reset.specific.multiple": "%2$s entitás %1$s pontszáma nullázva", "commands.scoreboard.players.reset.specific.single": "%2$s '%1$s' pontszáma nullázva", "commands.scoreboard.players.set.success.multiple": "%2$s entitás '%1$s' értéke most '%3$s'", "commands.scoreboard.players.set.success.single": "%2$s játékos '%1$s' értéke megváltoztatva erre: '%3$s'", "commands.seed.success": "Kezdőérték: %s", "commands.setblock.failed": "<PERSON><PERSON> elhelyezni a blokkot", "commands.setblock.success": "Blokk megváltoztatva a(z) %s, %s, %s koordinátákon", "commands.setidletimeout.success": "A megengedett tétlenség mostantól %s perc", "commands.setidletimeout.success.disabled": "A megengedett tétlenség mostantól korlátlan", "commands.setworldspawn.failure.not_overworld": "A globális kezdési pont beállítása csak a Felvilágban lehetséges", "commands.setworldspawn.success": "Globális kezdési pont beállítva a(z) %s, %s, %s [%s] koordinátákra", "commands.spawnpoint.success.multiple": "%6$s játékos kezdési pontja a(z) %5$s dimenzió %s, %s, %s [%s] koordinátáira állítva", "commands.spawnpoint.success.single": "%6$s kezdési pontja a(z) %5$s dimenzió %s, %s, %s [%s] koordinátáira állítva", "commands.spectate.not_spectator": "%s nincs s<PERSON> módban", "commands.spectate.self": "<PERSON><PERSON> saj<PERSON>t magad", "commands.spectate.success.started": "%s szemlélése folyamatban", "commands.spectate.success.stopped": "Entitás szemlélése befejezve", "commands.spreadplayers.failed.entities": "<PERSON>em <PERSON> %s entitást szétszórni %s, %s k<PERSON>r<PERSON>l (túl sok entitás ennyi helyre – próbálj legfeljebb %s szórási távolságot használni)", "commands.spreadplayers.failed.invalid.height": "A maxHeight értéke (%s) érvénytelen; ebben a világban minimum %s lehet", "commands.spreadplayers.failed.teams": "<PERSON>em <PERSON> %s csapatot szétszórni %s, %s körül (túl sok entitás ennyi helyre – próbálj legfeljebb %s szórási távolságot használni)", "commands.spreadplayers.success.entities": "%s játékos szétszórva a(z) %s, %s középpont körül, egymástól %s blokkos átlagtávolságban", "commands.spreadplayers.success.teams": "%s csapat szétszórva a(z) %s, %s középpont körül, egymástól %s blokkos átlagtávolságban", "commands.stop.stopping": "Szerver leállítása", "commands.stopsound.success.source.any": "Minden '%s' hang le<PERSON>", "commands.stopsound.success.source.sound": "'%s' hang leállítva a(z) '%s' forrásból", "commands.stopsound.success.sourceless.any": "Minden hang le<PERSON>llí<PERSON>va", "commands.stopsound.success.sourceless.sound": "'%s' hang le<PERSON><PERSON><PERSON>", "commands.summon.failed": "<PERSON><PERSON> megidézni az entitást", "commands.summon.failed.uuid": "<PERSON><PERSON> megidézni az entitást, mert az UUID már <PERSON>t", "commands.summon.invalidPosition": "Érvénytelen pozíció az idézéshez", "commands.summon.success": "Új %s megidézve", "commands.tag.add.failed": "A célpont vagy rendelkezik már ezzel a címkével, vagy túl sok címké<PERSON> van", "commands.tag.add.success.multiple": "'%s' címke hozzáadva %s entitáshoz", "commands.tag.add.success.single": "'%s' címke hozzáadva %s entitáshoz", "commands.tag.list.multiple.empty": "A vizsgált %s entitáson nincsenek címkék", "commands.tag.list.multiple.success": "A vizsgált %s entitáson %s címke van összesen: %s", "commands.tag.list.single.empty": "Nincsenek címkék a(z) %s entitáson", "commands.tag.list.single.success": "A(z) %s entitáson %s címke van: %s", "commands.tag.remove.failed": "A célpont nem rendelkezik ezzel a címkével", "commands.tag.remove.success.multiple": "'%s' címke eltávolítva %s entitásról", "commands.tag.remove.success.single": "'%s' címke eltávolítva %s entitásról", "commands.team.add.duplicate": "Már létezik ilyen nevű csapat", "commands.team.add.success": "%s csapat létrehozva", "commands.team.empty.success": "%s játékos eltávolítva a(z) %s csapatból", "commands.team.empty.unchanged": "Semmi sem változott. Ez a csapat már üres", "commands.team.join.success.multiple": "%s játékos hozzáadva a(z) %s csapathoz", "commands.team.join.success.single": "%s hozzáadva a(z) %s csapathoz", "commands.team.leave.success.multiple": "%s játékos eltávolítva minden csapatából", "commands.team.leave.success.single": "%s eltávolítva minden csapatból", "commands.team.list.members.empty": "A(z) %s csapatnak nincsenek tagjai", "commands.team.list.members.success": "A(z) %s csapatnak %s tagja van: %s", "commands.team.list.teams.empty": "<PERSON><PERSON><PERSON><PERSON> csapatok", "commands.team.list.teams.success": "%s csapat létezik: %s", "commands.team.option.collisionRule.success": "A(z) %s csapat új ütközési szabálya: \"%s\"", "commands.team.option.collisionRule.unchanged": "Semmi sem változott: az ütközési sza<PERSON><PERSON> már ilyen értékű", "commands.team.option.color.success": "A(z) %s csapat színe átállítva %s színre", "commands.team.option.color.unchanged": "Semmi sem változott. Ez a csapat már ilyen színű", "commands.team.option.deathMessageVisibility.success": "A(z) %s csapat halálüzeneteinek új láthatósági szabálya: \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Semmi sem változott: a halálüzenetek láthatósága már ilyen értékű", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON><PERSON>i sem vált<PERSON>ott: ez a csapat már nem sebezheti meg a csapattársakat", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON><PERSON>i sem változott: ez a csapat már megsebez<PERSON>ti a csapattársakat", "commands.team.option.friendlyfire.disabled": "A(z) %s csapat tagjai most nem tudják megsebezni egymást", "commands.team.option.friendlyfire.enabled": "A(z) %s csapat tagjai most megsebezhetik egymást", "commands.team.option.name.success": "%s csapat neve frissítve", "commands.team.option.name.unchanged": "<PERSON><PERSON><PERSON> sem v<PERSON>ltoz<PERSON>: ennek a csapatnak már ez a neve", "commands.team.option.nametagVisibility.success": "A(z) %s csapat névjelzőinek új láthatósági szabálya: \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Semmi sem változott: a névláthatóság már ilyen értékű", "commands.team.option.prefix.success": "Csapat előtagja beállítva: %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON><PERSON>i sem vált<PERSON>: ez a csapat már nem látja a láthatatlan csapattársakat", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON><PERSON>i sem vált<PERSON>: ez a csapat már látja a láthatatlan csapattársakat", "commands.team.option.seeFriendlyInvisibles.disabled": "A(z) %s csapat most nem láthatja a láthatatlan csapattársakat", "commands.team.option.seeFriendlyInvisibles.enabled": "A(z) %s csapat most láthatja a láthatatlan csapattársakat", "commands.team.option.suffix.success": "Csapat utótagja beállítva: %s", "commands.team.remove.success": "%s csapat törölve", "commands.teammsg.failed.noteam": "<PERSON><PERSON> csa<PERSON>ü<PERSON>, ha nincs csapatod", "commands.teleport.invalidPosition": "Érvénytelen pozíció a teleportáláshoz", "commands.teleport.success.entity.multiple": "%s entitás elteleportálva %s játékoshoz", "commands.teleport.success.entity.single": "%s elteleportálva %s játékoshoz", "commands.teleport.success.location.multiple": "%s entitás elteleportálva a(z) %s, %s, %s koordinátákra", "commands.teleport.success.location.single": "%s elteleportálva a(z) %s, %s, %s koordinátákra", "commands.test.batch.starting": "%s környezet %s tesztsorozatának indítása", "commands.test.clear.error.no_tests": "<PERSON><PERSON> törölhető teszt", "commands.test.clear.success": "%s struktúra törölve", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Kattints a vágólapra másoláshoz", "commands.test.create.success": "Tesztszerkezet létrehozva a(z) %s teszthez", "commands.test.error.no_test_containing_pos": "<PERSON><PERSON>, amely tartalma<PERSON> a(z) %s, %s, %s koordinátákat", "commands.test.error.no_test_instances": "<PERSON><PERSON>", "commands.test.error.non_existant_test": "%s teszt nem található", "commands.test.error.structure_not_found": "%s teszt struktúra nem található", "commands.test.error.test_instance_not_found": "<PERSON>em <PERSON> a tesztpéldány-blokkentitás", "commands.test.error.test_instance_not_found.position": "Nem ta<PERSON>ó a tesztpéldány-blokkentitás a(z) %s, %s, %s koordinátákon található teszthez", "commands.test.error.too_large": "A struktúra mérete egyik tengely mentén sem érheti el a %s blokkot", "commands.test.locate.done": "Keresés befejezve, %s struktúra találva", "commands.test.locate.found": "A megtalált struktúra a következő helyen található: %s (távolság: %s)", "commands.test.locate.started": "Elkezdődött a tesztstruktúrák felkutatása, ez eltarthat egy darabig...", "commands.test.no_tests": "<PERSON><PERSON><PERSON> futt<PERSON> te<PERSON>t", "commands.test.relative_position": "Pozíció %s-hoz viszonyítva: %s", "commands.test.reset.error.no_tests": "<PERSON><PERSON> v<PERSON>llítható teszt", "commands.test.reset.success": "%s struktúra visszaállítva", "commands.test.run.no_tests": "<PERSON><PERSON>", "commands.test.run.running": "%s teszt futtatása...", "commands.test.summary": "Játékteszt befejezve! %s teszt futott le", "commands.test.summary.all_required_passed": "Minden szükséges teszt sikerült :)", "commands.test.summary.failed": "%s szükséges teszt meghiúsult :(", "commands.test.summary.optional_failed": "%s opcionális teszt meghiúsult", "commands.tick.query.percentiles": "Percentilisek: P50: %s ms P95: %s ms P99: %s ms; minta: %s", "commands.tick.query.rate.running": "Tickráta c<PERSON>lért<PERSON>ke: %s másodpercenként.\nÁtlagos idő tickenként: %s ms (Cél: %s ms)", "commands.tick.query.rate.sprinting": "Tickráta célértéke: %s másodpercenként (viszonyítási alap, figyelmen kívül hagyva).\nÁtlagos idő tickenként: %s ms", "commands.tick.rate.success": "Tickráta célértéke beállítva %s tickre másodpercenként", "commands.tick.sprint.report": "A tickpörgetés befejeződött %s tickkel másodpercenként vagy %s ms-mal tickenként", "commands.tick.sprint.stop.fail": "<PERSON><PERSON><PERSON> nincs tickpörge<PERSON><PERSON> foly<PERSON>", "commands.tick.sprint.stop.success": "Tickpörge<PERSON><PERSON>", "commands.tick.status.frozen": "A játék ki van merevítve", "commands.tick.status.lagging": "A játék fut, de a célrátával nem képes lépést tartani", "commands.tick.status.running": "A játék szabályos ütemben fut", "commands.tick.status.sprinting": "A játék csúcssebességgel pörög", "commands.tick.step.fail": "A játékot nem lehet léptetni – ehhez a játék kimerevítése szükséges", "commands.tick.step.stop.fail": "<PERSON><PERSON><PERSON> nincs tickléptetés folyamatban", "commands.tick.step.stop.success": "Tickléptetés megszakítva", "commands.tick.step.success": "Előreléptetés %s tickkel", "commands.time.query": "Az idő %s", "commands.time.set": "<PERSON>dő <PERSON>llítva %s értékre", "commands.title.cleared.multiple": "Címek eltüntetve %s játékos számára", "commands.title.cleared.single": "Címek eltüntetve %s számára", "commands.title.reset.multiple": "Címbeállítások visszaállítva %s játékosok számára", "commands.title.reset.single": "Címbeállítások visszaállítva %s számára", "commands.title.show.actionbar.multiple": "Új g<PERSON>rstár-felirat megjelenítve %s játékos számára", "commands.title.show.actionbar.single": "Új g<PERSON>rstár-felirat megjelenítve %s számára", "commands.title.show.subtitle.multiple": "Új felirat megjelenítve %s játékosok számára", "commands.title.show.subtitle.single": "Új felirat megjelenítve %s számára", "commands.title.show.title.multiple": "Új cím megjelenítve %s játékos számára", "commands.title.show.title.single": "Új cím megjelenítve %s játékos számára", "commands.title.times.multiple": "Megjelenítési időzítés megváltoztatva %s játékos számára", "commands.title.times.single": "Megjelenítési időzítés megváltoztatva %s számára", "commands.transfer.error.no_players": "Legalább egy továbbítandó játékos megadása szükséges", "commands.transfer.success.multiple": "%s játékos továbbítása a(z) %s:%s szerverre", "commands.transfer.success.single": "%s továbbítása a(z) %s:%s szerverre", "commands.trigger.add.success": "%s trigger aktiválva (%s hozzáadva az értékhez)", "commands.trigger.failed.invalid": "Csak \"trigger\" t<PERSON><PERSON><PERSON> célokat tudsz aktiválni", "commands.trigger.failed.unprimed": "Még nem tudod aktiválni ezt a célt", "commands.trigger.set.success": "%s trigger aktiválva (érték beállítva erre: %s)", "commands.trigger.simple.success": "%s trigger aktiválva", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Szerververzió infó:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "<PERSON><PERSON>senek waypointok %s-nál/nél", "commands.waypoint.list.success": "%s waypoint %s: %s-nál/nél", "commands.waypoint.modify.color": "A waypoint színe mostantól %s", "commands.waypoint.modify.color.reset": "Waypoint szín visszaállítása", "commands.waypoint.modify.style": "Waypoint stílus megváltoztatva", "commands.weather.set.clear": "<PERSON><PERSON> idő<PERSON><PERSON><PERSON><PERSON> derültre állítva", "commands.weather.set.rain": "<PERSON>z időjá<PERSON>ás esősre állítva", "commands.weather.set.thunder": "<PERSON>z időjárás viharosra állítva", "commands.whitelist.add.failed": "A játékos már szerepel a fehérlistán", "commands.whitelist.add.success": "%s hozzáadva a fehérlistához", "commands.whitelist.alreadyOff": "A fehérlista már ki van ka<PERSON>olva", "commands.whitelist.alreadyOn": "A fehérlista már be van ka<PERSON>ol<PERSON>", "commands.whitelist.disabled": "A fehérlista most ki van kapcsolva", "commands.whitelist.enabled": "A fehérlista most be van ka<PERSON>va", "commands.whitelist.list": "%s j<PERSON><PERSON><PERSON> van a fehérlistán: %s", "commands.whitelist.none": "Nincsenek játékosok a fehérlistán", "commands.whitelist.reloaded": "Fehérlista újratöltve", "commands.whitelist.remove.failed": "A játékos nincs a fehérlistán", "commands.whitelist.remove.success": "%s levéve a fehérlistáról", "commands.worldborder.center.failed": "Semmi sem vált<PERSON>ott: a világhatár középpontja már ezen a helyen van", "commands.worldborder.center.success": "Világhatár középpontja beállítva a(z) %s, %s koordinátákra", "commands.worldborder.damage.amount.failed": "Semmi sem változott: a világhat<PERSON>r már ennyit sebez", "commands.worldborder.damage.amount.success": "Világhatár sebzése beállítva blokkonként %s pontra minden másodpercben", "commands.worldborder.damage.buffer.failed": "Semmi sem változott: a világhatár türelmi zónája már e<PERSON>a", "commands.worldborder.damage.buffer.success": "Világhatár türelmi zónája beállítva %s blokkra", "commands.worldborder.get": "A világhatár jelenleg %s blokk széles", "commands.worldborder.set.failed.big": "A világhatár nem lehet nagyobb %s blokknál", "commands.worldborder.set.failed.far": "A világhatár nem lehet kijjebb %s blokknál", "commands.worldborder.set.failed.nochange": "Se<PERSON>i sem változott: a világhatár már ek<PERSON>a méretű", "commands.worldborder.set.failed.small": "A világhatár nem lehet kisebb 1 blokknál", "commands.worldborder.set.grow": "Világhatár kitágítása %s blokk szélesre %s másodperc alatt", "commands.worldborder.set.immediate": "Világhatár beállítva %s blokk szélesre", "commands.worldborder.set.shrink": "Világhatár összehúzása %s blokk szélesre %s másodperc alatt", "commands.worldborder.warning.distance.failed": "Semmi sem változott: a világhatár figyelmeztetési távolsága már ekkora", "commands.worldborder.warning.distance.success": "Világhatár figyelmeztetési távolsága beállítva %s blokkra", "commands.worldborder.warning.time.failed": "Semmi sem változott: a világhatár figyelmeztetési ideje már ilyen hosszú", "commands.worldborder.warning.time.success": "Világhatár figyelmeztetési ideje beállítva %s másodpercre", "compliance.playtime.greaterThan24Hours": "Több mint 24 ór<PERSON><PERSON> j<PERSON>l", "compliance.playtime.hours": "%s órája j<PERSON>zol", "compliance.playtime.message": "A túlzott videojáték-használat zavarhatja a szokásos napi életvitelt", "connect.aborted": "Megszakítva", "connect.authorizing": "Bejelentkezés...", "connect.connecting": "Csatlakozás a szerverhez...", "connect.encrypting": "Titkosítás...", "connect.failed": "A szerverhez való csatlakozás si<PERSON>en", "connect.failed.transfer": "A kapcsolat megszakadt a szerverre történő áthelyezés közben", "connect.joining": "Csatlakozás a világhoz...", "connect.negotiating": "Egyeztetés...", "connect.reconfiging": "Újrakonfigurálás...", "connect.reconfiguring": "Újrakonfigurálás...", "connect.transferring": "Áthelyezés egy másik szerverre...", "container.barrel": "<PERSON><PERSON><PERSON>", "container.beacon": "Jelzőfény", "container.beehive.bees": "Méhek: %s/%s", "container.beehive.honey": "Méz: %s/%s", "container.blast_furnace": "<PERSON><PERSON><PERSON>", "container.brewing": "Főzőállvány", "container.cartography_table": "Térképasztal", "container.chest": "<PERSON><PERSON><PERSON>", "container.chestDouble": "<PERSON><PERSON>", "container.crafter": "Barkácsoló", "container.crafting": "Barkácsolás", "container.creative": "Tárgyválasztás", "container.dispenser": "Adagoló", "container.dropper": "Kid<PERSON>ó", "container.enchant": "Varázslás", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s lazurit", "container.enchant.lapis.one": "1 lazurit", "container.enchant.level.many": "%s tapasztalati szint", "container.enchant.level.one": "1 tapasztalati szint", "container.enchant.level.requirement": "%s szint szükséges", "container.enderchest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.furnace": "<PERSON><PERSON><PERSON>", "container.grindstone_title": "Javítás és varázstalanítás", "container.hopper": "T<PERSON><PERSON><PERSON><PERSON>r", "container.inventory": "Felszerelés", "container.isLocked": "A(z) %s le van z<PERSON>rva!", "container.lectern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.loom": "Szövőszék", "container.repair": "Javítás és átnevezés", "container.repair.cost": "Varázslat ára: %1$s", "container.repair.expensive": "Túl dr<PERSON>ga!", "container.shulkerBox": "<PERSON><PERSON>erd<PERSON><PERSON>", "container.shulkerBox.itemCount": "%2$s %1$s", "container.shulkerBox.more": "és még %s...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Füst<PERSON>lő", "container.spectatorCantOpen": "<PERSON><PERSON> t<PERSON>; a zsákmány még nincs megh<PERSON>.", "container.stonecutter": "Kővágó", "container.upgrade": "Felszerelés fejlesztése", "container.upgrade.error_tooltip": "A tárgy nem fejleszthető ilyen módon", "container.upgrade.missing_template_tooltip": "Helyezz ide kovácssablont", "controls.keybinds": "Billentyűzetbeállítások...", "controls.keybinds.duplicateKeybinds": "Ez a billentyű már t<PERSON> van ehhez:\n%s", "controls.keybinds.title": "Billentyűzetbeállítások", "controls.reset": "Eredeti", "controls.resetAll": "Billentyűk visszaállítása", "controls.title": "Irányítás", "createWorld.customize.buffet.biome": "Válassz egy biomot", "createWorld.customize.buffet.title": "Svédasztalvilág testreszabása", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Legalul - %s", "createWorld.customize.flat.layer.top": "Legfelül - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON><PERSON> eltávolítása", "createWorld.customize.flat.tile": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.flat.title": "<PERSON><PERSON><PERSON> lap<PERSON> világ testreszabása", "createWorld.customize.presets": "Minták", "createWorld.customize.presets.list": "<PERSON><PERSON>, itt van <PERSON>, amit k<PERSON><PERSON><PERSON>n csin<PERSON>ltunk!", "createWorld.customize.presets.select": "<PERSON><PERSON>", "createWorld.customize.presets.share": "Szeretnéd megosztani a mintádat valakivel? Használd az alábbi mezőt!", "createWorld.customize.presets.title": "Minta választása", "createWorld.preparing": "Világ létrehozásának előkészítése...", "createWorld.tab.game.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.tab.more.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.tab.world.title": "<PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.button.attribution": "Felhasznált technológiák", "credits_and_attribution.button.credits": "Közreműködők", "credits_and_attribution.button.licenses": "Licencek", "credits_and_attribution.screen.title": "Közreműködők és felhasznált technológiák", "dataPack.bundle.description": "A kísérleti Batyu tárgy engedélyezése", "dataPack.bundle.name": "Batyuk", "dataPack.locator_bar.description": "Más játékosok irányának mutatása többjátékos módban", "dataPack.locator_bar.name": "<PERSON><PERSON><PERSON>", "dataPack.minecart_improvements.description": "A csillék mozgásával kapcsolatos fejlesztések", "dataPack.minecart_improvements.name": "Továbbfejlesztett csillék", "dataPack.redstone_experiments.description": "Kísérleti változtatások a vöröskő működésében", "dataPack.redstone_experiments.name": "Vöröskő-kísérletek", "dataPack.title": "Adatcsomagok kiválasztása", "dataPack.trade_rebalance.description": "A falusiak átdolgozott ajánlatai", "dataPack.trade_rebalance.name": "Falusiak kereskedelmi újraegyensúlyozása", "dataPack.update_1_20.description": "Új funkciók és tartalmak a Minecraft 1.20-hoz", "dataPack.update_1_20.name": "1.20-as f<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataPack.update_1_21.description": "Új funkciók és tartalmak a Minecraft 1.21-hez", "dataPack.update_1_21.name": "1.21-es fris<PERSON><PERSON><PERSON>s", "dataPack.validation.back": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataPack.validation.failed": "Az adatcsomag ellenőrzése sikertelen!", "dataPack.validation.reset": "Alapértelmezett visszaállítása", "dataPack.validation.working": "Kiválasztott adatcsomagok hitelesítése...", "dataPack.vanilla.description": "A Minecraft eredeti ad<PERSON>", "dataPack.vanilla.name": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataPack.winter_drop.description": "Új funkciók és tartalmak a Téli tartalomfrissítéshez", "dataPack.winter_drop.name": "<PERSON><PERSON><PERSON>", "datapackFailure.safeMode": "Biztonsá<PERSON> mód", "datapackFailure.safeMode.failed.description": "Ez a világ érvénytelen vagy sérült mentési adatokat tartalmaz.", "datapackFailure.safeMode.failed.title": "A világot nem sikerült biztonságos módban betölteni.", "datapackFailure.title": "A jelenleg aktív adatcsomagok hibái miatt nem sikerült betölteni a világot.\nMegpróbálhatod betölteni „biztonságos módban” (azaz csak gyári adatcsomaggal), vagy v<PERSON>rhe<PERSON>z a főmenübe, hogy magad hárítsd el a hibát.", "death.attack.anvil": "%1$s kilapult egy zuhanó <PERSON> alatt", "death.attack.anvil.player": "%1$s kilapult egy <PERSON><PERSON><PERSON>, mik<PERSON>zben vele harcolt: %2$s", "death.attack.arrow": "%2$s lelőtte %1$s játékost", "death.attack.arrow.item": "%2$s lelőtte %1$s játékost %3$s nevű fegyverével", "death.attack.badRespawnPoint.link": "játéktervezési megfontolások", "death.attack.badRespawnPoint.message": "%1$s %2$s áldo<PERSON>ta lett", "death.attack.cactus": "%1$s belehalt a szurkálásba", "death.attack.cactus.player": "%1$s nekiment egy kaktusznak, miközben %2$s elől menekült", "death.attack.cramming": "%1$s összepréselődött a tömegben", "death.attack.cramming.player": "%2$s agyontaposta %s$1 játékost", "death.attack.dragonBreath": "%1$s megsült az Endersárkány leheletében", "death.attack.dragonBreath.player": "%2$s megsütötte %1$s játékost az Endersárkány lehelletében", "death.attack.drown": "%1$s megfulladt", "death.attack.drown.player": "%1$s megfulladt, miközben %2$s elől menekült", "death.attack.dryout": "%1$s belehalt a kiszáradásba", "death.attack.dryout.player": "%1$s belehalt a kiszáradásba, miközben %2$s elől menekült", "death.attack.even_more_magic": "%1$s még több varázslat áldozatává vált", "death.attack.explosion": "%1$s felrobbant", "death.attack.explosion.player": "%1$s %2$s robbantásának áldozata lett", "death.attack.explosion.player.item": "%2$s felrobbantotta %1$s játékost %3$s használatával", "death.attack.fall": "%1$s túl erősen ért földet", "death.attack.fall.player": "%1$s túl erősen <PERSON>rt földet, miközben %2$s elől menekült", "death.attack.fallingBlock": "%1$s kilapult egy zuhanó blokk alatt", "death.attack.fallingBlock.player": "%1$s kilapult egy zu<PERSON> blo<PERSON>k alatt, mik<PERSON>zben vele harcolt: %2$s", "death.attack.fallingStalactite": "%1$s játékost felnyársalta egy leeső függőcseppkő", "death.attack.fallingStalactite.player": "%1$s-t felnyársalta egy leeső függőcseppkő, miközben vele harcolt: %2$s", "death.attack.fireball": "%1$s %2$s tűzgolyójának áldozata lett", "death.attack.fireball.item": "%2$s letűzgolyózta %1$s játékost %3$s használatával", "death.attack.fireworks": "%1$s látványos véget ért", "death.attack.fireworks.item": "%1$s látványos véget ért %2$s %3$s fegyveréből kilőtt rakétája á<PERSON>l", "death.attack.fireworks.player": "%1$s látványos v<PERSON>get <PERSON>, miközben vele harcolt: %2$s", "death.attack.flyIntoWall": "%1$s megismerkedett a mozgási energiával", "death.attack.flyIntoWall.player": "%1$s nekirepült egy falnak, miközben %2$s elől menekült", "death.attack.freeze": "%1$s halálra fagyott", "death.attack.freeze.player": "%2$s halálra fagyasztotta %1$s játékost", "death.attack.generic": "%1$s meghalt", "death.attack.generic.player": "%1$s meghalt %2$s miatt", "death.attack.genericKill": "%1$s meg lett ölve", "death.attack.genericKill.player": "%1$s-t megölték miközben %2$s játékossal harcolt", "death.attack.hotFloor": "%1$s játékosnak forró lett a talaj a talpa alatt", "death.attack.hotFloor.player": "%1$s forró talajra sétált %2$s miatt", "death.attack.inFire": "%1$s lángba borult", "death.attack.inFire.player": "%1$s beles<PERSON><PERSON><PERSON><PERSON> a tűzbe, miközben vele harcolt: %2$s", "death.attack.inWall": "%1$s besz<PERSON><PERSON> egy falba", "death.attack.inWall.player": "%1$s megful<PERSON><PERSON> egy falban, miközben vele harcolt: %2$s", "death.attack.indirectMagic": "%1$s belehalt %2$s varázslatába", "death.attack.indirectMagic.item": "%2$s megölte %1$s játékost %3$s használatával", "death.attack.lava": "%1$s megpróbált lávában úszni", "death.attack.lava.player": "%1$s lávába ugrott %2$s elől", "death.attack.lightningBolt": "%1$s fejébe csapott a villám", "death.attack.lightningBolt.player": "%1$s villámcsap<PERSON><PERSON> s<PERSON>, miközben vele harcolt: %2$s", "death.attack.mace_smash": "%2$s összezúzta %1$s játékost", "death.attack.mace_smash.item": "%1$s játékost összezúzta %2$s %3$s tárggyal", "death.attack.magic": "%1$s var<PERSON><PERSON><PERSON>t áldo<PERSON> lett", "death.attack.magic.player": "%1$s belehalt egy varázslatba, miközben %2$s elől menekült", "death.attack.message_too_long": "Valójában az üzenet túl hosszú volt a kézbesítéshez. Bocsánat! Itt a megvágott verzió: %s", "death.attack.mob": "%1$s egy %2$s áldo<PERSON>ta lett", "death.attack.mob.item": "%1$s %2$s áldo<PERSON>ta lett, %3$s nevű fegyvere által", "death.attack.onFire": "%1$s összeégett", "death.attack.onFire.item": "%1$s ropogósra sült, miközben %2$s játékos %3$s nevű fegyvere ellen harcolt", "death.attack.onFire.player": "%1$s ropogósra sült, miközben vele harcolt: %2$s", "death.attack.outOfWorld": "%1$s kizuhant a világból", "death.attack.outOfWorld.player": "%1$s nem akart ugy<PERSON>bban a világban élni, mint %2$s", "death.attack.outsideBorder": "%1$s kifutott a világból", "death.attack.outsideBorder.player": "%1$s kifutott a világból, miközben %2$s ellen harcolt", "death.attack.player": "%1$s %2$s áldo<PERSON>ta lett", "death.attack.player.item": "%1$s %2$s játékos %3$s nevű fegyverének áldozata lett", "death.attack.sonic_boom": "%1$s megsemmisült egy szonikus gerjesztésű sikolyban", "death.attack.sonic_boom.item": "%1$s játékost elpusztította egy szonikus gerjesztésű sikoly, miközben megpróbált elmenekülni %2$s elől, akinél %3$s volt", "death.attack.sonic_boom.player": "%1$s megsemmisült egy szonikus gerjesztésű sikolyban, miközben %2$s elől menekült", "death.attack.stalagmite": "%1$s játékost felnyársalta egy állócseppkő", "death.attack.stalagmite.player": "%1$s-t felnyársalta egy cseppkő, miközben vele harcolt: %2$s", "death.attack.starve": "%1$s éhen halt", "death.attack.starve.player": "%1$s <PERSON><PERSON><PERSON>, mik<PERSON>zben vele harcolt: %2$s", "death.attack.sting": "%1$s belehalt egy csípésbe", "death.attack.sting.item": "%2$s halálra szurkálta %1$s játékost %3$s nevű fegyverével", "death.attack.sting.player": "%2$s halálra csípte %1$s játékost", "death.attack.sweetBerryBush": "%1$s játékost halálra szúrta egy édesbogyóbokor", "death.attack.sweetBerryBush.player": "%1$s játékost halálra szúrta egy bogyóbokor, miközben %2$s elől menekült", "death.attack.thorns": "%1$s meghalt, miközben %2$s páncélját ütögette", "death.attack.thorns.item": "%1$s belehalt %2$s %3$s nevű páncéljának ütögetésébe", "death.attack.thrown": "%2$s agyondobálta %1$s játékost", "death.attack.thrown.item": "%2$s agyondobta %1$s játékost %3$s tárggyal", "death.attack.trident": "%2$s megszigonyozta %1$s játékost", "death.attack.trident.item": "%2$s felnyársalta %1$s játékost %3$s nevű szigonyával", "death.attack.wither": "%1$s elsorvadt", "death.attack.wither.player": "%1$s el<PERSON><PERSON><PERSON>, miközben vele harcolt: %2$s", "death.attack.witherSkull": "%1$s játékost lelőtte egy %2$s a koponyájával", "death.attack.witherSkull.item": "%1$s játékost egy %2$s koponyája találta el %3$s segítségével", "death.fell.accident.generic": "%1$s lezuhant a magasból", "death.fell.accident.ladder": "%1$s lezuhant egy létráról", "death.fell.accident.other_climbable": "%1$s lezuhant mászás közben", "death.fell.accident.scaffolding": "%1$s leesett egy állványzatról", "death.fell.accident.twisting_vines": "%1$s lezuhant a tekergő indákról", "death.fell.accident.vines": "%1$s lezuhant az indákról", "death.fell.accident.weeping_vines": "%1$s lezuhant a keserves indákról", "death.fell.assist": "%1$s a halálba zuhant %2$s miatt", "death.fell.assist.item": "%1$s a halálba zuhant %2$s %3$s nevű fegyvere által", "death.fell.finish": "%1$s túl m<PERSON><PERSON><PERSON>, majd %2$s kivégezte", "death.fell.finish.item": "%1$s túl m<PERSON><PERSON><PERSON>, majd %2$s kivégezte %3$s nevű fegyverével", "death.fell.killer": "%1$s a halálba zuhant", "deathScreen.quit.confirm": "Biztosan ki akarsz lépni?", "deathScreen.respawn": "Újraéledés", "deathScreen.score": "Pontszám", "deathScreen.score.value": "Pontszám: %s", "deathScreen.spectate": "<PERSON><PERSON><PERSON><PERSON>", "deathScreen.title": "Meghaltál!", "deathScreen.title.hardcore": "Vége a játéknak!", "deathScreen.titleScreen": "Főmenü", "debug.advanced_tooltips.help": "F3 + H = Bővebb eszközleírások", "debug.advanced_tooltips.off": "Technikai eszközleírások: ki", "debug.advanced_tooltips.on": "Technikai eszközleírások: be", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON><PERSON><PERSON><PERSON>", "debug.chunk_boundaries.off": "Chunkhatárok: ki", "debug.chunk_boundaries.on": "Chun<PERSON><PERSON>árok: be", "debug.clear_chat.help": "F3 + D = Chatablak törlése", "debug.copy_location.help": "F3 + C = <PERSON><PERSON> m<PERSON> /tp parancsk<PERSON>t; tartsd lenyomva a játék összeomlásához", "debug.copy_location.message": "Koordináták a vágólapra másolva", "debug.crash.message": "Lenyomtad az F3 + C kombinációt. Ha nem engeded fel, a játék összeomlik.", "debug.crash.warning": "%s másodperc az összeomlásig...", "debug.creative_spectator.error": "<PERSON><PERSON><PERSON> enged<PERSON><PERSON><PERSON> a játékmódváltáshoz", "debug.creative_spectator.help": "F3 + N = Előző játékmód és szemlélő mód közti váltás", "debug.dump_dynamic_textures": "Dinamikus textúrák elmentve ide: %s", "debug.dump_dynamic_textures.help": "F3 + S = Dinamikus textúr<PERSON> kigyűj<PERSON>se", "debug.gamemodes.error": "<PERSON><PERSON> a játékmódválasztót, nincs r<PERSON> engedé<PERSON>ed", "debug.gamemodes.help": "F3 + F4 = Játékmódválasztó megnyitása", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Következő", "debug.help.help": "F3 + Q = E lista megjelenítése", "debug.help.message": "Billentyűparancsok:", "debug.inspect.client.block": "Blokk kliensoldali adatai a vágólapra másolva", "debug.inspect.client.entity": "Entitás kliensoldali adatai a vágólapra másolva", "debug.inspect.help": "F3 + I = Blokk vagy entitás adatainak másolása a vágólapra", "debug.inspect.server.block": "Blokk szerveroldali adatai a vágólapra másolva", "debug.inspect.server.entity": "Entitás szerveroldali adatai a vágólapra másolva", "debug.pause.help": "F3 + Esc = Szüneteltetés menü <PERSON> (ha a játék szüneteltethető)", "debug.pause_focus.help": "F3 + P = Szüneteltetés ablakfókusz elvesztésekor", "debug.pause_focus.off": "Szüneteltetés fókusz elvesztésekor: ki", "debug.pause_focus.on": "Szüneteltetés fókusz elvesztésekor: be", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Teljesítményrögzítés indítása/leállítása", "debug.profiling.start": "A vizsgálat elindult %s másodpercig. Nyomd le az F3 + L billentyűkombinációt a megszakításhoz", "debug.profiling.stop": "A rögzítés befejeződött. Teljesítményadatok elmentve %s néven", "debug.reload_chunks.help": "F3 + A = Chunkok újratöltése", "debug.reload_chunks.message": "Minden chunk újratöltése", "debug.reload_resourcepacks.help": "F3 + T = Forráscsomagok újratöltése", "debug.reload_resourcepacks.message": "Forráscsomagok újratöltve", "debug.show_hitboxes.help": "F3 + B = <PERSON><PERSON><PERSON>ó téglatestek megjelenítése", "debug.show_hitboxes.off": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>glatestek: ki", "debug.show_hitboxes.on": "<PERSON><PERSON><PERSON><PERSON>: be", "debug.version.header": "Klie<PERSON> verz<PERSON>:", "debug.version.help": "F3 + V = Kliensverzió inform<PERSON>", "demo.day.1": "<PERSON><PERSON>n a demóban csak öt játékbeli napig van lehetőséged játszani. Hozd a legjobb formád!", "demo.day.2": "Második nap", "demo.day.3": "Harmadik nap", "demo.day.4": "Negyedik nap", "demo.day.5": "Ez az utolsó napod!", "demo.day.6": "Eltelt az ötödik nap, a(z) %s billentyűvel készíthetsz képernyőképet az alkotásodról.", "demo.day.warning": "Mind<PERSON><PERSON>rt letelik az időd!", "demo.demoExpired": "Lej<PERSON>rt a demó időkorlátja!", "demo.help.buy": "Vedd meg most!", "demo.help.fullWrapped": "A demó 5 játékbeli napig tart (valós időben kb. 1 óra 40 percig). Nézd meg az „Előrelépések” menüt segítségért! Jó szórakozást!", "demo.help.inventory": "Használd a(z) %1$s gombot a felszerelésed megnyitásához!", "demo.help.jump": "A(z) %1$s gomb megny<PERSON><PERSON><PERSON><PERSON>", "demo.help.later": "Játék folytatása!", "demo.help.movement": "Használd a(z) %1$s, %2$s, %3$s, %4$s gombokat és az egeret a mozgáshoz!", "demo.help.movementMouse": "Nézz körül az egérrel", "demo.help.movementShort": "Mozgás a következő gombokkal: %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft demó mód", "demo.remainingTime": "Hátralévő idő: %s", "demo.reminder": "A próbaidő letelt. Vedd meg a játékot a folytat<PERSON>, vagy kezdj egy új világot!", "difficulty.lock.question": "Biztos vagy benne, ho<PERSON> rögzíted ennek a világnak a nehézségét? Innentől a világ nehézsége mindig %1$s lesz, és soha nem fogod tudni megváltoztatni.", "difficulty.lock.title": "Világnehézség rögzítése", "disconnect.endOfStream": "Adatfolyam vége", "disconnect.exceeded_packet_rate": "Kirúgtak a csomagküldési korlát túllépése miatt", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Állapotkérés figyelmen kívül hagyása", "disconnect.loginFailedInfo": "A belépés sikertelen: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "A többjátékos mód le <PERSON> tilt<PERSON>. Kérjük ellenőrizze a Microsoft-fiókjának beállításait.", "disconnect.loginFailedInfo.invalidSession": "Érvénytelen munkamenet (Próbáld meg újraindítani a játékot és az indítót)", "disconnect.loginFailedInfo.serversUnavailable": "A hitelesítő szerverek jelenleg nem elérhetőek. Próbálkozz újra később.", "disconnect.loginFailedInfo.userBanned": "El vagy tiltva az online játéktól", "disconnect.lost": "A kapcsolat megszűnt", "disconnect.packetError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disconnect.spam": "Spammelés miatt kirúgva", "disconnect.timeout": "Időtúllépés", "disconnect.transfer": "<PERSON><PERSON><PERSON><PERSON><PERSON> egy másik szer<PERSON>re", "disconnect.unknownHost": "Ismeretlen kiszolgáló", "download.pack.failed": "%2$s csomagból %1$s letöltése sikertelen", "download.pack.progress.bytes": "Letöltés: %s (összméret ismeretlen)", "download.pack.progress.percent": "Letöltés: %s%%", "download.pack.title": "%s/%s forráscsomag letöltése", "editGamerule.default": "Alapérték: %s", "editGamerule.title": "Játékszabályok szerkesztése", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Elnyelt életerő", "effect.minecraft.bad_omen": "Rossz ómen", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Csatornaeffektus", "effect.minecraft.darkness": "Sötétség", "effect.minecraft.dolphins_grace": "Delfin kecsessége", "effect.minecraft.fire_resistance": "Tűzáll<PERSON>g", "effect.minecraft.glowing": "Fénylés", "effect.minecraft.haste": "Sietség", "effect.minecraft.health_boost": "Életerősí<PERSON>s", "effect.minecraft.hero_of_the_village": "A falu hőse", "effect.minecraft.hunger": "Éhség", "effect.minecraft.infested": "Molyteny<PERSON>t", "effect.minecraft.instant_damage": "Azonnali <PERSON>", "effect.minecraft.instant_health": "Azonnali gyógyulás", "effect.minecraft.invisibility": "Láthatatlanság", "effect.minecraft.jump_boost": "Magas<PERSON><PERSON><PERSON>", "effect.minecraft.levitation": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.luck": "Szerencse", "effect.minecraft.mining_fatigue": "F<PERSON>radts<PERSON>g", "effect.minecraft.nausea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.oozing": "Nyálkaképzés", "effect.minecraft.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.raid_omen": "Fosztogató-ómen", "effect.minecraft.regeneration": "Regeneráció", "effect.minecraft.resistance": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.saturation": "Telítettség", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON>", "effect.minecraft.slowness": "Las<PERSON>úsá<PERSON>", "effect.minecraft.speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.strength": "<PERSON><PERSON><PERSON>", "effect.minecraft.trial_omen": "Próba-ómen", "effect.minecraft.unluck": "Balszerencse", "effect.minecraft.water_breathing": "Vízlégzés", "effect.minecraft.weakness": "Gyengeség", "effect.minecraft.weaving": "Hálószövés", "effect.minecraft.wind_charged": "Széllökés", "effect.minecraft.wither": "Sorvadás", "effect.none": "<PERSON><PERSON><PERSON> hat<PERSON>", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Vízaffinitás", "enchantment.minecraft.bane_of_arthropods": "Ízeltlábúak ve<PERSON>", "enchantment.minecraft.binding_curse": "Bilincselés átka", "enchantment.minecraft.blast_protection": "Robbanásvédelem", "enchantment.minecraft.breach": "Páncéltö<PERSON><PERSON>", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.density": "Sűrűség", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "Hatékonyság", "enchantment.minecraft.feather_falling": "Könnyű esés", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_protection": "Tűzáll<PERSON>g", "enchantment.minecraft.flame": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.fortune": "Szerencse", "enchantment.minecraft.frost_walker": "Fagyjárás", "enchantment.minecraft.impaling": "Felnyársalás", "enchantment.minecraft.infinity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.knockback": "Hátralökés", "enchantment.minecraft.looting": "Zsákmányolás", "enchantment.minecraft.loyalty": "Hűség", "enchantment.minecraft.luck_of_the_sea": "Horg<PERSON><PERSON>szerenc<PERSON>", "enchantment.minecraft.lure": "Csalétek", "enchantment.minecraft.mending": "Önjavítás", "enchantment.minecraft.multishot": "Többszörö<PERSON>", "enchantment.minecraft.piercing": "Átdöfés", "enchantment.minecraft.power": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "Lövedékvédelem", "enchantment.minecraft.protection": "V<PERSON>delem", "enchantment.minecraft.punch": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.quick_charge": "G<PERSON>rs töltés", "enchantment.minecraft.respiration": "Légzés", "enchantment.minecraft.riptide": "Szökőár", "enchantment.minecraft.sharpness": "Élesség", "enchantment.minecraft.silk_touch": "Gyengéd <PERSON>", "enchantment.minecraft.smite": "Sújtás", "enchantment.minecraft.soul_speed": "Lélekjárás", "enchantment.minecraft.sweeping": "Suhintás", "enchantment.minecraft.sweeping_edge": "Suhintás", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.thorns": "Tüskék", "enchantment.minecraft.unbreaking": "Törhetetlenség", "enchantment.minecraft.vanishing_curse": "Eltű<PERSON><PERSON>", "enchantment.minecraft.wind_burst": "Förgeteg", "entity.minecraft.acacia_boat": "Akáciafa c<PERSON>ónak", "entity.minecraft.acacia_chest_boat": "Akáciafa csónak ládával", "entity.minecraft.allay": "Nyugtancs", "entity.minecraft.area_effect_cloud": "Időző bájitalfelhő", "entity.minecraft.armadillo": "<PERSON><PERSON>", "entity.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.arrow": "Nyílvessző", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Bambusztu<PERSON><PERSON>", "entity.minecraft.bamboo_raft": "Bambusztutaj", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "<PERSON><PERSON><PERSON>", "entity.minecraft.birch_boat": "Nyírfa csónak", "entity.minecraft.birch_chest_boat": "Nyírfa csónak ládával", "entity.minecraft.blaze": "Őrláng", "entity.minecraft.block_display": "Blokkmegjelenítő", "entity.minecraft.boat": "Csónak", "entity.minecraft.bogged": "Láplövész", "entity.minecraft.breeze": "Őrszél", "entity.minecraft.breeze_wind_charge": "Széllöket", "entity.minecraft.camel": "<PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "Barlangi pók", "entity.minecraft.cherry_boat": "Cseresznyefa csónak", "entity.minecraft.cherry_chest_boat": "Cseresznyefa csónak ládával", "entity.minecraft.chest_boat": "Csónak ládával", "entity.minecraft.chest_minecart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "Tőkehal", "entity.minecraft.command_block_minecart": "Parancsblokk-csille", "entity.minecraft.cow": "<PERSON><PERSON><PERSON>", "entity.minecraft.creaking": "Csikorgó", "entity.minecraft.creaking_transient": "Csikorgó", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Sötéttölgy c<PERSON>ak", "entity.minecraft.dark_oak_chest_boat": "Sötéttölgy csónak ládával", "entity.minecraft.dolphin": "Delfin", "entity.minecraft.donkey": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Sárkánytűzgolyó", "entity.minecraft.drowned": "Vízbefúlt", "entity.minecraft.egg": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.elder_guardian": "<PERSON>én <PERSON>", "entity.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON> endergy<PERSON>ngy", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Idéző", "entity.minecraft.evoker_fangs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.experience_bottle": "El<PERSON>bott Varázslat palackja", "entity.minecraft.experience_orb": "Tapasztalati gömb", "entity.minecraft.eye_of_ender": "<PERSON><PERSON> s<PERSON>", "entity.minecraft.falling_block": "<PERSON><PERSON><PERSON><PERSON> blokk", "entity.minecraft.falling_block_type": "Zuhanó ‘%s’ blokk", "entity.minecraft.fireball": "Tűzgolyó", "entity.minecraft.firework_rocket": "Tűzijáték", "entity.minecraft.fishing_bobber": "Úszó", "entity.minecraft.fox": "<PERSON><PERSON><PERSON>", "entity.minecraft.frog": "Béka", "entity.minecraft.furnace_minecart": "<PERSON><PERSON><PERSON>", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "Fénylő eszközkeret", "entity.minecraft.glow_squid": "Fénylő <PERSON>", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Őrszem", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Tölcséres csille", "entity.minecraft.horse": "<PERSON><PERSON>", "entity.minecraft.husk": "Kiszáradt zombi", "entity.minecraft.illusioner": "Szemfényvesztő", "entity.minecraft.interaction": "Interaktor", "entity.minecraft.iron_golem": "Vasgólem", "entity.minecraft.item": "<PERSON><PERSON><PERSON>", "entity.minecraft.item_display": "Tárgymegjelenítő", "entity.minecraft.item_frame": "Eszközkeret", "entity.minecraft.jungle_boat": "Dzsungelfa csónak", "entity.minecraft.jungle_chest_boat": "Dzsungelfa csónak ládával", "entity.minecraft.killer_bunny": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.leash_knot": "Las<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.lightning_bolt": "Villám", "entity.minecraft.lingering_potion": "Id<PERSON><PERSON><PERSON> bájital", "entity.minecraft.llama": "<PERSON><PERSON><PERSON>", "entity.minecraft.llama_spit": "Lámaköpet", "entity.minecraft.magma_cube": "Magmakocka", "entity.minecraft.mangrove_boat": "Mangrovefa csónak", "entity.minecraft.mangrove_chest_boat": "Mangrovefa csónak ládával", "entity.minecraft.marker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "Csille", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "Öszvér", "entity.minecraft.oak_boat": "Tölgyfa csónak", "entity.minecraft.oak_chest_boat": "Tölgyfa csónak ládával", "entity.minecraft.ocelot": "Ocelot", "entity.minecraft.ominous_item_spawner": "Vészj<PERSON><PERSON><PERSON>", "entity.minecraft.painting": "Festmény", "entity.minecraft.pale_oak_boat": "Sápadtt<PERSON><PERSON>gy c<PERSON>", "entity.minecraft.pale_oak_chest_boat": "Sápadttölgy csónak ládával", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "<PERSON><PERSON>", "entity.minecraft.pig": "Malac", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON> verőlegény", "entity.minecraft.pillager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.player": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "Jegesmedve", "entity.minecraft.potion": "Bájital", "entity.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.rabbit": "Nyúl", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "<PERSON><PERSON>", "entity.minecraft.sheep": "Birka", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Shulkerlövedék", "entity.minecraft.silverfish": "Ezüstmoly", "entity.minecraft.skeleton": "Csontváz", "entity.minecraft.skeleton_horse": "Csontvázló", "entity.minecraft.slime": "<PERSON><PERSON><PERSON>lka", "entity.minecraft.small_fireball": "<PERSON><PERSON>", "entity.minecraft.sniffer": "Orrontó", "entity.minecraft.snow_golem": "Hógólem", "entity.minecraft.snowball": "Hógolyó", "entity.minecraft.spawner_minecart": "Csille idézőketreccel", "entity.minecraft.spectral_arrow": "Spektrálnyíl", "entity.minecraft.spider": "Pók", "entity.minecraft.splash_potion": "<PERSON><PERSON><PERSON> b<PERSON>", "entity.minecraft.spruce_boat": "Fenyőfa csónak", "entity.minecraft.spruce_chest_boat": "Fenyőfa csónak ládával", "entity.minecraft.squid": "Tintahal", "entity.minecraft.stray": "K<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.strider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Szövegmegjelenítő", "entity.minecraft.tnt": "Élesített TNT", "entity.minecraft.tnt_minecart": "TNT-csille", "entity.minecraft.trader_llama": "Ke<PERSON><PERSON><PERSON>", "entity.minecraft.trident": "Szigony", "entity.minecraft.tropical_fish": "Trópusi hal", "entity.minecraft.tropical_fish.predefined.0": "Korallsügér", "entity.minecraft.tropical_fish.predefined.1": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.13": "Tündöklő császárhal", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "V<PERSON>rösajk<PERSON>", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "Fonalasszárnyú hal", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bohó<PERSON>", "entity.minecraft.tropical_fish.predefined.19": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.2": "Kék doktorhal", "entity.minecraft.tropical_fish.predefined.20": "<PERSON><PERSON><PERSON>fark<PERSON> p<PERSON>", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.3": "Pillangó<PERSON>", "entity.minecraft.tropical_fish.predefined.4": "<PERSON><PERSON>g<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON><PERSON> m<PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Blokkhal", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "Uszonyhal", "entity.minecraft.tropical_fish.type.glitter": "Csillámhal", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "Csíkoshal", "entity.minecraft.tropical_fish.type.sunstreak": "Pászmahal", "entity.minecraft.turtle": "Teknős", "entity.minecraft.vex": "Bosszancs", "entity.minecraft.villager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "Hentes", "entity.minecraft.villager.cartographer": "Térképész", "entity.minecraft.villager.cleric": "Pap", "entity.minecraft.villager.farmer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "Nyílkészítő", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Könyvtáros", "entity.minecraft.villager.mason": "K<PERSON>műves", "entity.minecraft.villager.nitwit": "Lágyagyú", "entity.minecraft.villager.none": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "Fegy<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.vindicator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wandering_trader": "Vándorkereskedő", "entity.minecraft.warden": "Strázsa", "entity.minecraft.wind_charge": "Széllöket", "entity.minecraft.witch": "Boszorkány", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "With<PERSON>csontv<PERSON><PERSON>", "entity.minecraft.wither_skull": "Witherkoponya", "entity.minecraft.wolf": "<PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "<PERSON><PERSON><PERSON><PERSON><PERSON> falusi", "entity.minecraft.zombified_piglin": "Élőhalott piglin", "entity.not_summonable": "Nem idézhető meg %s típusú entitás", "event.minecraft.raid": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat": "Vereség", "event.minecraft.raid.defeat.full": "Roham - Vereség", "event.minecraft.raid.raiders_remaining": "Hátralévő támadók: %s", "event.minecraft.raid.victory": "Győzelem", "event.minecraft.raid.victory.full": "Roham - Győzelem", "filled_map.buried_treasure": "<PERSON>ásott kincses térkép", "filled_map.explorer_jungle": "Dzsungelbéli kincses térkép", "filled_map.explorer_swamp": "<PERSON><PERSON><PERSON><PERSON> kin<PERSON> t<PERSON>", "filled_map.id": "%s. sz<PERSON>", "filled_map.level": "(%s/%s. szint)", "filled_map.locked": "<PERSON><PERSON><PERSON><PERSON>", "filled_map.mansion": "<PERSON><PERSON><PERSON> kin<PERSON> térk<PERSON>", "filled_map.monument": "Tengeri kincses térkép", "filled_map.scale": "1:%s méretarányú", "filled_map.trial_chambers": "Küzdőteres térkép", "filled_map.unknown": "Ismeretlen térkép", "filled_map.village_desert": "<PERSON><PERSON><PERSON> falutérkép", "filled_map.village_plains": "Mezei falutérkép", "filled_map.village_savanna": "Szavannai falutérkép", "filled_map.village_snowy": "Hómezei falutérkép", "filled_map.village_taiga": "<PERSON><PERSON><PERSON> f<PERSON>", "flat_world_preset.minecraft.bottomless_pit": "Feneketlen lyuk", "flat_world_preset.minecraft.classic_flat": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.desert": "Sivatag", "flat_world_preset.minecraft.overworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.redstone_ready": "Tervezői terep", "flat_world_preset.minecraft.snowy_kingdom": "Hóbirodalom", "flat_world_preset.minecraft.the_void": "Az Űr", "flat_world_preset.minecraft.tunnelers_dream": "Bányászálom", "flat_world_preset.minecraft.water_world": "Vízivilág", "flat_world_preset.unknown": "???", "gameMode.adventure": "<PERSON><PERSON> mód", "gameMode.changed": "A játékmódod a következőre frissült: %s", "gameMode.creative": "Krea<PERSON>í<PERSON> mód", "gameMode.hardcore": "Hardcore mód!", "gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mód", "gameMode.survival": "<PERSON><PERSON><PERSON><PERSON><PERSON> mód", "gamerule.allowFireTicksAwayFromPlayer": "Tűz frissítése a játékosoktól távol", "gamerule.allowFireTicksAwayFromPlayer.description": "<PERSON><PERSON><PERSON> szabályozza, hogy a játékosoktól 8 chunknál távolabbi tűz és láva képes legyen-e tüzet terjeszteni", "gamerule.announceAdvancements": "Előrelépések bejelentése", "gamerule.blockExplosionDropDecay": "A blokkok robbanásai nem dobatnak minden tárgyat", "gamerule.blockExplosionDropDecay.description": "A blokk-kölcsönhatások által előidézett robbanásokban széttört blokkok által dobott tárgyak elveszhetnek a robbanáskor.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Tárgy<PERSON><PERSON><PERSON>", "gamerule.category.misc": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.mobs": "Mobok", "gamerule.category.player": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Mobok", "gamerule.category.updates": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "gamerule.commandBlockOutput": "Parancsblokk-kimenet közzététele", "gamerule.commandModificationBlockLimit": "Parancsok blokkmódosítási korlátja", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON>, hány blokk módosítható egyszerre egy parancs futtat<PERSON> (mint pl. fill vagy clone).", "gamerule.disableElytraMovementCheck": "Kitinszá<PERSON><PERSON> se<PERSON>g<PERSON><PERSON><PERSON>", "gamerule.disablePlayerMovementCheck": "Játékosmozgás ellenőrzésének <PERSON>", "gamerule.disableRaids": "Rohamok letiltása", "gamerule.doDaylightCycle": "Napszakok váltakozása", "gamerule.doEntityDrops": "Tárgyak dobása entitásoktól", "gamerule.doEntityDrops.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a <PERSON>sill<PERSON>k, cs<PERSON>akok, keretek stb. kidobják-e magukat (és a tartalmukat) tárgyakként", "gamerule.doFireTick": "Tűz terjedése", "gamerule.doImmediateRespawn": "Újraéledés azonnal", "gamerule.doInsomnia": "Fantomok teremtése", "gamerule.doLimitedCrafting": "Barkácsolás csak receptekből", "gamerule.doLimitedCrafting.description": "<PERSON> be van ka<PERSON>, a játékosok csak az elérhető recepteket tudják használni barkácsoláskor.", "gamerule.doMobLoot": "Zsákmányszerzés moboktól", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a szörnyek és állatok megölésükkor dobjanak-e zsákmányt és tapasztalatpontokat.", "gamerule.doMobSpawning": "Mobok teremtése", "gamerule.doMobSpawning.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy teremjenek-e szörnyek és állatok; néhány entitásra más szabályok vonatkozhatnak.", "gamerule.doPatrolSpawning": "Fosztogatóbrigádok teremtése", "gamerule.doTileDrops": "Tárgyszerzés blokkokból", "gamerule.doTileDrops.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a blokkok széttörésükkor dobjanak-e erőforrásokat és tapasztalatpontokat.", "gamerule.doTraderSpawning": "Vándorkereskedők teremtése", "gamerule.doVinesSpread": "Indák terjedése", "gamerule.doVinesSpread.description": "<PERSON><PERSON>t <PERSON>, hogy az Inda blokkok véletlenszerűen átterjednek-e a szomszédos blokkokra. Nincs hatással másfajta indablokkokra, mint Keserves indák, Tekergő indák stb.", "gamerule.doWardenSpawning": "Strázsák teremtése", "gamerule.doWeatherCycle": "Időjárás változása", "gamerule.drowningDamage": "Fulladási sebzés engedélyezése", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON> el<PERSON><PERSON>t endergyöngy halálkor el<PERSON>ű<PERSON>", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy egy játékos által eldobott endergyöngyök megsemmisülnek-e a játékos hal<PERSON>.", "gamerule.entitiesWithPassengersCanUsePortals": "Entitások utassal is használhatják a portálokat", "gamerule.entitiesWithPassengersCanUsePortals.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az utasokkal rendelkező entitások át tudjanak teleportálni a Nether portálokon, End portálokon és End kapukon.", "gamerule.fallDamage": "Esési sebzés engedélyezése", "gamerule.fireDamage": "Égési sebzés engedélyezése", "gamerule.forgiveDeadPlayers": "Megbocsátás a halott játékosoknak", "gamerule.forgiveDeadPlayers.description": "<PERSON> be van ka<PERSON>, a feldühödött semleges mobok nem lesznek többé dühösek egy j<PERSON>ra, mi<PERSON><PERSON> az meghalt a közelükben.", "gamerule.freezeDamage": "Fagyási sebzés engedélyezése", "gamerule.globalSoundEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.globalSoundEvents.description": "Bizonyos események hangja, mint például egy főellenség létrejöttéé, a világon mindenhol hallható.", "gamerule.keepInventory": "Felszerelés megtartása halál után", "gamerule.lavaSourceConversion": "Lávaforrás f<PERSON>ó l<PERSON>", "gamerule.lavaSourceConversion.description": "<PERSON> egy b<PERSON>, amely f<PERSON><PERSON> lá<PERSON>, k<PERSON>t oldal<PERSON> lávaforr<PERSON> határol, maga is lávaforrássá alakul.", "gamerule.locatorBar": "Játé<PERSON> engedélyezése", "gamerule.locatorBar.description": "<PERSON> be van <PERSON>, egy csík jelenik meg a képernyőn, ami a többi j<PERSON>tékos i<PERSON>ány<PERSON> mutatja.", "gamerule.logAdminCommands": "Adminisztrátori parancsok közzététele", "gamerule.maxCommandChainLength": "Parancsláncok maximális ho<PERSON>", "gamerule.maxCommandChainLength.description": "A láncolt parancsblokkokra és az eljárásokra vonatkozik.", "gamerule.maxCommandForkCount": "Parancskörnyezet korlátozása", "gamerule.maxCommandForkCount.description": "Az 'execute as' <PERSON><PERSON> has<PERSON><PERSON><PERSON> parancsok által létrehozható végrehajtási környezetek maximális száma.", "gamerule.maxEntityCramming": "Entitások zsúfolhatósága", "gamerule.minecartMaxSpeed": "Csillék sebességkorlátja", "gamerule.minecartMaxSpeed.description": "A szárazföldön mozgó csillék maximális megengedett sebessége", "gamerule.mobExplosionDropDecay": "A lények robbanásai nem dobatnak minden tárgyat", "gamerule.mobExplosionDropDecay.description": "A mobok által előidézett robbanásokban széttört blokkok által dobott tárgyak elveszhetnek a robbanáskor.", "gamerule.mobGriefing": "Mobok kihatása a világra", "gamerule.naturalRegeneration": "<PERSON><PERSON> regenerálása", "gamerule.playersNetherPortalCreativeDelay": "Nether portál késleltetése a kreatív módban lévő játékosoknak", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON><PERSON> (tickekben), amennyit egy kreatív módban lévő játékosnak egy Nether portálban kell töltenie a dimenzióváltáshoz.", "gamerule.playersNetherPortalDefaultDelay": "Nether portál késleltetése nem kreatív módban lévő játékosoknak", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON><PERSON> (tickekben), amennyit egy nem kreatív módban lévő játékosnak egy Nether portálban kell töltenie a dimenzióváltáshoz.", "gamerule.playersSleepingPercentage": "Alvó j<PERSON>kosok aránya", "gamerule.playersSleepingPercentage.description": "<PERSON>z éjszaka <PERSON>grásához szükséges alvó játékosok aránya százalékban.", "gamerule.projectilesCanBreakBlocks": "A lövedékek képesek blokkokat törni", "gamerule.projectilesCanBreakBlocks.description": "<PERSON><PERSON><PERSON>, hogy a becsapódó lövedékek széttörhetik-e az ennek megfelelő típusú blokkokat.", "gamerule.randomTickSpeed": "Véletlenszerű blokkfrissítési ráta", "gamerule.reducedDebugInfo": "<PERSON><PERSON><PERSON><PERSON> debuginformáció", "gamerule.reducedDebugInfo.description": "Korlátozza a debugképernyő tartalmát.", "gamerule.sendCommandFeedback": "Parancs eredményének kijelzése", "gamerule.showDeathMessages": "Halálok bejelentése", "gamerule.snowAccumulationHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.snowAccumulationHeight.description": "Havazáskor maximum ennyi réteg magasan gyűlhet össze a hó a földön.", "gamerule.spawnChunkRadius": "Glob<PERSON>lis k<PERSON>ület <PERSON>a", "gamerule.spawnChunkRadius.description": "Azon chunk-ok sz<PERSON>ma, am<PERSON>ek mindig betöltve maradnak a globális kezdőpont körül.", "gamerule.spawnRadius": "Újraéledési terület sugara", "gamerule.spawnRadius.description": "Annak a kezdési pont körüli területnek a méretét szabályozza, ahol a játékosok újraéledhetnek.", "gamerule.spectatorsGenerateChunks": "Terepgenerálás engedélyezése szemlélők számára", "gamerule.tntExplodes": "TNT élesítésének és felrobbantásának engedélyezése", "gamerule.tntExplosionDropDecay": "A TNT-robbanások nem dobatnak minden tárgyat", "gamerule.tntExplosionDropDecay.description": "A TNT-robbanásban széttört blokkok által dobott tárgyak elveszhetnek a robbanáskor.", "gamerule.universalAnger": "Kollektív düh", "gamerule.universalAnger.description": "Ha be van kap<PERSON>olva, a feldühített semleges mobok minden közeli játékost megtámadnak, nem csak a<PERSON>t, aki feldühítette ő<PERSON>.\nA legjobban ak<PERSON>, ha a forgiveDeadPlayers ki van kapcsolva.", "gamerule.waterSourceConversion": "Vízforrás folyó vízből", "gamerule.waterSourceConversion.description": "<PERSON> egy blo<PERSON>, amely f<PERSON><PERSON> vizet tarta<PERSON>, k<PERSON>t <PERSON> víz<PERSON>rr<PERSON> határol, maga is vízforrássá alakul.", "generator.custom": "<PERSON><PERSON><PERSON><PERSON>", "generator.customized": "<PERSON><PERSON><PERSON> fajta <PERSON>", "generator.minecraft.amplified": "SZÉLSŐSÉGES", "generator.minecraft.amplified.info": "Megjegyzés: Csak szórakozásból! Izmos gépet igényel.", "generator.minecraft.debug_all_block_states": "Debug mód", "generator.minecraft.flat": "<PERSON><PERSON><PERSON>", "generator.minecraft.large_biomes": "Nagy biomok", "generator.minecraft.normal": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "generator.minecraft.single_biome_surface": "Egyetlen biom", "generator.single_biome_caves": "Barlangok", "generator.single_biome_floating_islands": "Lebegő szigetek", "gui.abuseReport.attestation": "A bejelentés elküldésével kijelented, hogy az általad benne foglaltak legjobb tudomásod szerint a pontos és teljes igazságot tükrözik.", "gui.abuseReport.comments": "Hozzászólások", "gui.abuseReport.describe": "Ha tudunk az eset részleteiről, könnyebben hozunk jól megalapozott döntést.", "gui.abuseReport.discard.content": "Ha kilépsz, el fog veszni a jelentésed és a hozzá fűzött megjegyzéseid.\nBiztosan ki akarsz lépni?", "gui.abuseReport.discard.discard": "Kilépés és bejelentés elvetése", "gui.abuseReport.discard.draft": "Mentés <PERSON>z<PERSON>", "gui.abuseReport.discard.return": "Szerkesztés folytatása", "gui.abuseReport.discard.title": "Elveted a bejelentést és a megjegyzéseket?", "gui.abuseReport.draft.content": "Szeretnéd folytatni a meglévő bejelentés szerkesztését, vagy elveted és létrehozol egy újat?", "gui.abuseReport.draft.discard": "Elvetés", "gui.abuseReport.draft.edit": "Szerkesztés folytatása", "gui.abuseReport.draft.quittotitle.content": "Szeretnéd folytatni a szerkesztést vagy elveted?", "gui.abuseReport.draft.quittotitle.title": "Egy mentetlen bejelentésed el fog veszni, ha kilépsz", "gui.abuseReport.draft.title": "Szerkeszted az elmentett jelentésed?", "gui.abuseReport.error.title": "Probléma a bejelentés elküldésekor", "gui.abuseReport.message": "Hol tapasztaltad a nem kívánt viselkedést?\nEnnek megadása segít nekünk az eset feltárásában.", "gui.abuseReport.more_comments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> le, mi tört<PERSON>t:", "gui.abuseReport.name.comment_box_label": "<PERSON><PERSON> <PERSON>, mi<PERSON>rt akarod jelenteni ezt a játékosnevet:", "gui.abuseReport.name.reporting": "„%s” neve ellen teszel bejelentést.", "gui.abuseReport.name.title": "Játékos nevének bejelentése", "gui.abuseReport.observed_what": "<PERSON><PERSON><PERSON> te<PERSON> be<PERSON>?", "gui.abuseReport.read_info": "Tanulj a bejelentésekről", "gui.abuseReport.reason.alcohol_tobacco_drugs": "K<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vagy al<PERSON>", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Valaki k<PERSON>í<PERSON><PERSON><PERSON><PERSON> ka<PERSON> illegális tevékenységre buzdít m<PERSON>t, vagy kiskor<PERSON>ak alkoholfogyasztását népszerűsíti.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "<PERSON><PERSON><PERSON><PERSON><PERSON> szembeni szexuá<PERSON> v<PERSON>zaélés vagy b<PERSON>", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Valaki gyermekekkel szemben tanúsított szeméremsértő viselkedésr<PERSON><PERSON>, vagy azt más módon népszerűsíti.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Rágalmazás", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Valaki a te hírnevedet vagy más sze<PERSON>ly hírnevét rombolja, például hamis információkat oszt meg mások kihasználása vagy félrevezetése céljából.", "gui.abuseReport.reason.description": "Leírás:", "gui.abuseReport.reason.false_reporting": "<PERSON><PERSON>", "gui.abuseReport.reason.generic": "Bejelent<PERSON>t akarok tenni ellene", "gui.abuseReport.reason.generic.description": "Ez a játékos bosszant engem / olya<PERSON>it tett, ami nekem nem tetszik.", "gui.abuseReport.reason.harassment_or_bullying": "Zaklatás vagy <PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON> me<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, meg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vagy zaklat téged vagy más játékost. Ide tartozik az is, ha valaki ismételten megpróbál kap<PERSON>olatba lépni veled vagy más játékossal a másik fél beleegyezése nélkül, illetve ha személyes információt oszt meg rólad vagy más játékosról beleegyezés nélkül (ún. „doxolás”).", "gui.abuseReport.reason.hate_speech": "Gyűlöletbeszéd", "gui.abuseReport.reason.hate_speech.description": "Valaki támadólag lép fel veled vagy más játékossal szemben vallásra, r<PERSON><PERSON><PERSON>, nemi irányultságra vagy más identitásjellegű tulajdonságra alapozva.", "gui.abuseReport.reason.imminent_harm": "Erőszakos fenyegetés", "gui.abuseReport.reason.imminent_harm.description": "<PERSON><PERSON> a<PERSON>l fenye<PERSON>, hogy a való életben testi sért<PERSON>t okoz neked vagy valaki m<PERSON>nak.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Hozzájá<PERSON><PERSON><PERSON> n<PERSON>lküli személyes képanyag", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Valaki sze<PERSON>lyes vagy bi<PERSON> k<PERSON><PERSON>, ilyeneket terjeszt vagy más módon népszerűsít.", "gui.abuseReport.reason.self_harm_or_suicide": "Önbántalmazás vagy <PERSON>", "gui.abuseReport.reason.self_harm_or_suicide.description": "<PERSON><PERSON> a<PERSON> be<PERSON>l vagy a<PERSON>l fen<PERSON>, hogy a való életben sérülést okoz saj<PERSON>t ma<PERSON>, vagy <PERSON> dolgokat csinál.", "gui.abuseReport.reason.sexually_inappropriate": "Szexuális jellegű tartalom", "gui.abuseReport.reason.sexually_inappropriate.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> szexu<PERSON> aktus<PERSON>, nemi szer<PERSON>ez <PERSON>s szexuális erőszakhoz kapcsolódnak.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorizmus vagy erőszakos radikalizmus", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Valaki <PERSON>, illetve <PERSON>lsőséges politikai, vallási, ideológiai vagy egyéb nézetek által motivált er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a<PERSON><PERSON> fen<PERSON>, vagy azt népszerűsíti.", "gui.abuseReport.reason.title": "Jelentés típusának kiválasztása", "gui.abuseReport.report_sent_msg": "Sikeresen megkaptuk a bejelentésedet. Köszönjük!\n\nA csapatunk meg fogja vizsgálni, am<PERSON>en gyorsan csak lehet.", "gui.abuseReport.select_reason": "Bejelentés típusának kiválasztása", "gui.abuseReport.send": "Bejelentés küldése", "gui.abuseReport.send.comment_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>bb megjegyzést", "gui.abuseReport.send.error_message": "A bejelentés küldése során hiba történt:\n'%s'", "gui.abuseReport.send.generic_error": "A bejelentés küldése során váratlan hiba történt.", "gui.abuseReport.send.http_error": "A bejelentés küldése során váratlan HTTP-hiba lépett fel.", "gui.abuseReport.send.json_error": "A bejelentés küldése során hibás adatcsomag érk<PERSON>.", "gui.abuseReport.send.no_reason": "Válaszd ki a bejelentés kategóriáját", "gui.abuseReport.send.not_attested": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, o<PERSON><PERSON><PERSON> el a fenti szöveget, majd jelöld be a jelölőnégyzetet, hogy el tudd küldeni a bejelentést", "gui.abuseReport.send.service_unavailable": "A bejelentőszolgáltatást nem sikerült elérni. Bizonyosodj meg róla, hogy csatlakozva vagy az internethez, majd próbálkozz újra.", "gui.abuseReport.sending.title": "Bejelentés elküldése...", "gui.abuseReport.sent.title": "Bejelentés elküldve", "gui.abuseReport.skin.title": "<PERSON>n<PERSON><PERSON>t j<PERSON>", "gui.abuseReport.title": "Játékos j<PERSON>ntése", "gui.abuseReport.type.chat": "Chatüzenetek", "gui.abuseReport.type.name": "Játékos neve", "gui.abuseReport.type.skin": "<PERSON><PERSON><PERSON><PERSON>", "gui.acknowledge": "<PERSON><PERSON><PERSON><PERSON>", "gui.advancements": "Előrelépések", "gui.all": "Mind", "gui.back": "<PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\nTudj meg többet a következő linken: %s", "gui.banned.description.permanent": "A fiókod végleges tiltásra k<PERSON>lt, ami a<PERSON><PERSON>, hogy többé nem játszhatsz online és nem csatlakozhatsz realmekhez.", "gui.banned.description.reason": "Bejelentés érkezett hozzánk a fiókod által tanúsított kifogásolható viselkedésről. A moderátoraink megvizsgálták az esetet, és úgy találták, hogy az „%s” címén sérti a Minecraft közösségi alapelveit.", "gui.banned.description.reason_id": "Kód: %s", "gui.banned.description.reason_id_message": "Kód: %s – %s", "gui.banned.description.temporary": "%s <PERSON>z idő alatt nem játszhatsz online és nem csatlakozhatsz realmekhez.", "gui.banned.description.temporary.duration": "A fiókodat ideiglenesen felfüggesztettük, és %s múlva aktiváljuk újra.", "gui.banned.description.unknownreason": "Bejelentés érkezett hozzánk a fiókod által tanúsított kifogásolható viselkedésről. A moderátoraink megvizsgálták az esetet, és úgy találták, hogy az sérti a Minecraft közösségi alapelveit.", "gui.banned.name.description": "A jelenlegi neved – „%s” – sérti a Közösségi Alapelveinket. <PERSON><PERSON><PERSON><PERSON><PERSON> is játszhatsz egyjátékos módban, de az online játékhoz meg kell változtatnod a neved.\n\nTudj meg többet vagy kérj felülvizsgálatot ezen a linken: %s", "gui.banned.name.title": "Többjátékos módban nem engedélyezett név", "gui.banned.reason.defamation_impersonation_false_information": "Mások félrevezetése személyiséglopással vagy <PERSON>formációk megosztásával", "gui.banned.reason.drugs": "Illegális drogokra tett utalások", "gui.banned.reason.extreme_violence_or_gore": "Valóságos brutá<PERSON> erőszak vagy véres jele<PERSON>", "gui.banned.reason.false_reporting": "Nagy mennyiségű hamis vagy pontatlan bejelentés", "gui.banned.reason.fraud": "Tartalmak csalással való megszerzése vagy has<PERSON>", "gui.banned.reason.generic_violation": "A Közösségi Alapelvek megsértése", "gui.banned.reason.harassment_or_bullying": "Sértő nyelvezet használata ártó szándékkal", "gui.banned.reason.hate_speech": "Gyűlöletbeszéd vagy diszkrim<PERSON>ó", "gui.banned.reason.hate_terrorism_notorious_figure": "Gy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, terroristaszervezetekre vagy rossz hírű személyekre tett utalások", "gui.banned.reason.imminent_harm_to_person_or_property": "Valóélet-<PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON> vagy anyagi kár ok<PERSON> irán<PERSON><PERSON><PERSON>", "gui.banned.reason.nudity_or_pornography": "Kihívó vagy pornogr<PERSON><PERSON> tartalo<PERSON> közzététele", "gui.banned.reason.sexually_inappropriate": "Szexuális jellegű tartalom vagy téma", "gui.banned.reason.spam_or_advertising": "Spam vagy re<PERSON>l<PERSON>mtevékenység", "gui.banned.skin.description": "A jelenlegi kinézeted sérti a Közösségi Alapelveinket. <PERSON><PERSON><PERSON><PERSON><PERSON> is j<PERSON><PERSON>zhatsz egy alapértelmezett kinézettel, vagy v<PERSON>laszthatsz egy másikat.\n\nTudj meg többet vagy kérj felülvizsgálatot ezen a linken: %s", "gui.banned.skin.title": "<PERSON><PERSON> en<PERSON><PERSON><PERSON>", "gui.banned.title.permanent": "Fiók véglegesen letiltva", "gui.banned.title.temporary": "Fiók ideiglenesen felfüggesztve", "gui.cancel": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.comments": "Hozzászólások", "gui.chatReport.describe": "Ha tudunk az eset részleteiről, könnyebben hozunk jól megalapozott döntést.", "gui.chatReport.discard.content": "Ha kilépsz, el fog veszni a jelentésed és a hozzá fűzött megjegyzéseid.\nBiztosan ki akarsz lépni?", "gui.chatReport.discard.discard": "Kilépés és bejelentés elvetése", "gui.chatReport.discard.draft": "Mentés <PERSON>z<PERSON>", "gui.chatReport.discard.return": "Szerkesztés folytatása", "gui.chatReport.discard.title": "Elveted a jelentést és a megjegyzéseket?", "gui.chatReport.draft.content": "Szeretnéd folytatni a meglévő bejelentés szerkesztését, vagy elveted és létrehozol egy újat?", "gui.chatReport.draft.discard": "Elvetés", "gui.chatReport.draft.edit": "Szerkesztés folytatása", "gui.chatReport.draft.quittotitle.content": "Folytatni szeretnéd a szerkesztést, vagy elveted a piszkozatot?", "gui.chatReport.draft.quittotitle.title": "Egy mentetlen bejelentésed el fog veszni, ha kilépsz", "gui.chatReport.draft.title": "Szerkeszted a piszkozatot?", "gui.chatReport.more_comments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> le, mi tört<PERSON>t:", "gui.chatReport.observed_what": "<PERSON><PERSON><PERSON> te<PERSON> be<PERSON>?", "gui.chatReport.read_info": "Tanulj a bejelentésekről", "gui.chatReport.report_sent_msg": "Sikeresen megkaptuk a bejelentésedet. Köszönjük!\n\nA csapatunk meg fogja vizsgálni, am<PERSON>en gyorsan csak lehet.", "gui.chatReport.select_chat": "Bejelentendő üzenetek kiválasztása", "gui.chatReport.select_reason": "Jelentési kategória kiválasztása", "gui.chatReport.selected_chat": "%s üzenet kiválasztva a bejelentéshez", "gui.chatReport.send": "Je<PERSON>és k<PERSON>ld<PERSON>", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>bb megjegyzést", "gui.chatReport.send.no_reason": "Válaszd ki a jelentés kategóriáját", "gui.chatReport.send.no_reported_messages": "Válassz ki legalább egy üzenetet a jelentéshez", "gui.chatReport.send.too_many_messages": "Túl sok üzenet van mellékelve a bejelentéshez", "gui.chatReport.title": "Játékos j<PERSON>ntése", "gui.chatSelection.context": "A kijelöléssel szomszédos üzenetek is me<PERSON><PERSON><PERSON><PERSON>, hogy többet tudjunk meg a körülményekről", "gui.chatSelection.fold": "%s üzenet elrejtve", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s c<PERSON><PERSON><PERSON>ott a chathez", "gui.chatSelection.message.narrate": "%s üzenete %s-kor: %s", "gui.chatSelection.selected": "%s/%s üzenet kijelölve", "gui.chatSelection.title": "Bejelentendő üzenetek kiválasztása", "gui.continue": "Folytatás", "gui.copy_link_to_clipboard": "Link másolása a vágólapra", "gui.days": "%s nap", "gui.done": "<PERSON><PERSON><PERSON>", "gui.down": "Le", "gui.entity_tooltip.type": "Típus: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Visszautasított %s fájlok", "gui.fileDropFailure.title": "<PERSON><PERSON> ho<PERSON>adni a fájlokat", "gui.hours": "%s óra", "gui.loadingMinecraft": "Minecraft betöltése", "gui.minutes": "%s perc", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s gomb", "gui.narrate.editBox": "%s szövegmező: %s", "gui.narrate.slider": "%s csúszka", "gui.narrate.tab": "%s fül", "gui.no": "Nem", "gui.none": "<PERSON><PERSON><PERSON> sem", "gui.ok": "OK", "gui.open_report_dir": "<PERSON><PERSON><PERSON> k<PERSON>vt<PERSON>", "gui.proceed": "Folytatás", "gui.recipebook.moreRecipes": "<PERSON><PERSON> ka<PERSON> a továbbiakhoz", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Keresés...", "gui.recipebook.toggleRecipes.all": "Összes megjelenítve", "gui.recipebook.toggleRecipes.blastable": "Olvaszthatóak megjelenítve", "gui.recipebook.toggleRecipes.craftable": "Barkácsolhatóak megjelenítve", "gui.recipebook.toggleRecipes.smeltable": "Süthetőek megjelenítve", "gui.recipebook.toggleRecipes.smokable": "Füstölhetőek megjelenítve", "gui.report_to_server": "Je<PERSON><PERSON> a szervernek", "gui.socialInteractions.blocking_hint": "Kezelés Microsoft-fiókkal", "gui.socialInteractions.empty_blocked": "<PERSON><PERSON><PERSON> j<PERSON> a chaten", "gui.socialInteractions.empty_hidden": "<PERSON><PERSON><PERSON> re<PERSON><PERSON><PERSON> j<PERSON> a chaten", "gui.socialInteractions.hidden_in_chat": "%s üzenetei el lesznek rejtve a chatben", "gui.socialInteractions.hide": "<PERSON><PERSON><PERSON><PERSON><PERSON> a chaten", "gui.socialInteractions.narration.hide": "%s üzeneteinek elrejtése", "gui.socialInteractions.narration.report": "%s játékos jelentése", "gui.socialInteractions.narration.show": "%s üzeneteinek megjelenítése", "gui.socialInteractions.report": "Jelentés", "gui.socialInteractions.search_empty": "<PERSON>em <PERSON>ó ilyen nevű játékos", "gui.socialInteractions.search_hint": "Keresés...", "gui.socialInteractions.server_label.multiple": "%s - %s j<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.server_label.single": "%s - %s j<PERSON><PERSON><PERSON>", "gui.socialInteractions.show": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a chaten", "gui.socialInteractions.shown_in_chat": "%s üzenetei meg lesznek jelenítve a chatben", "gui.socialInteractions.status_blocked": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.status_blocked_offline": "Letiltva - Offline", "gui.socialInteractions.status_hidden": "Elrejtve", "gui.socialInteractions.status_hidden_offline": "Elrejtve - Offline", "gui.socialInteractions.status_offline": "Offline", "gui.socialInteractions.tab_all": "Összes", "gui.socialInteractions.tab_blocked": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_hidden": "Rejtve", "gui.socialInteractions.title": "Közösségi interakciók", "gui.socialInteractions.tooltip.hide": "Üzenetek elrejtése", "gui.socialInteractions.tooltip.report": "Játékos j<PERSON>ntése", "gui.socialInteractions.tooltip.report.disabled": "A jelentési szolgáltatás nem elérhető", "gui.socialInteractions.tooltip.report.no_messages": "Nincsenek jelenthető üzenetek %s játékostól", "gui.socialInteractions.tooltip.report.not_reportable": "Ez a játékos nem j<PERSON>nthető, mert az üzenetei nem hitelesíthetőek ezen a szerveren", "gui.socialInteractions.tooltip.show": "Üzenetek megjelenítése", "gui.stats": "Statisztikák", "gui.toMenu": "Vissza a szerverlistához", "gui.toRealms": "Vissza a realmek listájához", "gui.toTitle": "Vissza a főmenübe", "gui.toWorld": "Vissza a világok listájához", "gui.togglable_slot": "Kattints a rekesz lezárásához", "gui.up": "<PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "Vissza (%s s)", "gui.waitingForResponse.title": "Várakozás a szerverre", "gui.yes": "Igen", "hanging_sign.edit": "Függőtábla üzenetének módosítása", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Ku<PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "Dal", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "T<PERSON>rgyak megsemmisítése", "inventory.hotbarInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON> men<PERSON>: %1$s+%2$s", "inventory.hotbarSaved": "<PERSON><PERSON><PERSON><PERSON><PERSON> (visszállítás: %1$s+%2$s)", "item.canBreak": "Törhető:", "item.canPlace": "Letehető erre:", "item.canUse.unknown": "Ismeretlen", "item.color": "Szín: %s", "item.components": "%s komponens", "item.disabled": "<PERSON><PERSON><PERSON> tá<PERSON>", "item.durability": "Élettartam: %s / %s", "item.dyed": "<PERSON><PERSON>", "item.minecraft.acacia_boat": "Akáciafa c<PERSON>ónak", "item.minecraft.acacia_chest_boat": "Akáciafa csónak ládával", "item.minecraft.allay_spawn_egg": "Nyugtancsidéző to<PERSON>", "item.minecraft.amethyst_shard": "Ametisztszilánk", "item.minecraft.angler_pottery_shard": "Cserépszilánk ho<PERSON>ás<PERSON>", "item.minecraft.angler_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON>seré<PERSON>ö<PERSON>", "item.minecraft.apple": "Alma", "item.minecraft.archer_pottery_shard": "Cserépszilánk <PERSON>", "item.minecraft.archer_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> cseréptöredék", "item.minecraft.armadillo_scute": "Tatu-s<PERSON><PERSON><PERSON>z", "item.minecraft.armadillo_spawn_egg": "Tatuid<PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.arms_up_pottery_shard": "Cserépszilán<PERSON> karok<PERSON>", "item.minecraft.arms_up_pottery_sherd": "Felem<PERSON>t kezek cseréptöredék", "item.minecraft.arrow": "Nyílvessző", "item.minecraft.axolotl_bucket": "Axolotl vödörben", "item.minecraft.axolotl_spawn_egg": "Axolotlidé<PERSON><PERSON> to<PERSON>", "item.minecraft.baked_potato": "<PERSON><PERSON><PERSON> burgonya", "item.minecraft.bamboo_chest_raft": "Bambusztu<PERSON><PERSON>", "item.minecraft.bamboo_raft": "Bambusztutaj", "item.minecraft.bat_spawn_egg": "Denev<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bee_spawn_egg": "Méhi<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beef": "<PERSON>yers ma<PERSON>", "item.minecraft.beetroot": "Cékla", "item.minecraft.beetroot_seeds": "Céklamag", "item.minecraft.beetroot_soup": "Céklaleves", "item.minecraft.birch_boat": "Nyírfa csónak", "item.minecraft.birch_chest_boat": "Nyírfa csónak ládával", "item.minecraft.black_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.black_dye": "Fekete festék", "item.minecraft.black_harness": "<PERSON><PERSON><PERSON> h<PERSON>", "item.minecraft.blade_pottery_shard": "Cserépszi<PERSON><PERSON><PERSON>", "item.minecraft.blade_pottery_sherd": "<PERSON><PERSON> cseréptöred<PERSON>k", "item.minecraft.blaze_powder": "Őrlángpor", "item.minecraft.blaze_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_spawn_egg": "Őrlángidéző tojás", "item.minecraft.blue_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_dye": "Kék festék", "item.minecraft.blue_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "Láplövészidéző toj<PERSON>", "item.minecraft.bolt_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.bolt_armor_trim_smithing_template.new": "Szegecselt páncéldísz", "item.minecraft.bone": "Cson<PERSON>", "item.minecraft.bone_meal": "Csontliszt", "item.minecraft.book": "Könyv", "item.minecraft.bordure_indented_banner_pattern": "Csipkézett szegélyű zászlóminta", "item.minecraft.bow": "<PERSON><PERSON>", "item.minecraft.bowl": "<PERSON><PERSON><PERSON>", "item.minecraft.bread": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.breeze_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "Őrszélidéző tojás", "item.minecraft.brewer_pottery_shard": "Cserépszilánk lombikkal", "item.minecraft.brewer_pottery_sherd": "Italfőző cseréptöredék", "item.minecraft.brewing_stand": "Főzőállvány", "item.minecraft.brick": "Tégla", "item.minecraft.brown_bundle": "<PERSON><PERSON>", "item.minecraft.brown_dye": "<PERSON><PERSON>", "item.minecraft.brown_egg": "<PERSON><PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON>", "item.minecraft.brush": "Ecset", "item.minecraft.bucket": "Vödör", "item.minecraft.bundle": "<PERSON><PERSON>", "item.minecraft.bundle.empty": "Üres", "item.minecraft.bundle.empty.description": "Képes egy vegyes összetételű halmot tárolni", "item.minecraft.bundle.full": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Cserépszilánk lánggal", "item.minecraft.burn_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.camel_spawn_egg": "Teveid<PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.carrot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON>", "item.minecraft.cat_spawn_egg": "Macskaidéző toj<PERSON>", "item.minecraft.cauldron": "Üst", "item.minecraft.cave_spider_spawn_egg": "Barlangi pókot idéző to<PERSON>", "item.minecraft.chainmail_boots": "Láncbakan<PERSON>", "item.minecraft.chainmail_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_helmet": "Lán<PERSON>isa<PERSON>", "item.minecraft.chainmail_leggings": "<PERSON><PERSON><PERSON> lá<PERSON>zárvédő", "item.minecraft.charcoal": "Faszén", "item.minecraft.cherry_boat": "Cseresznyefa csónak", "item.minecraft.cherry_chest_boat": "Cseresznyefa csónak ládával", "item.minecraft.chest_minecart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chicken": "<PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "Csirkeidéző to<PERSON>", "item.minecraft.chorus_fruit": "Refréngyümölcs", "item.minecraft.clay_ball": "Agyaggolyó", "item.minecraft.clock": "<PERSON><PERSON>", "item.minecraft.coal": "Szén", "item.minecraft.coast_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.coast_armor_trim_smithing_template.new": "Tengerparti <PERSON>", "item.minecraft.cocoa_beans": "Kakaóbab", "item.minecraft.cod": "<PERSON><PERSON>", "item.minecraft.cod_bucket": "Tőkehal vödörben", "item.minecraft.cod_spawn_egg": "Tőke<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.command_block_minecart": "Parancsblokk-csille", "item.minecraft.compass": "Iránytű", "item.minecraft.cooked_beef": "<PERSON><PERSON><PERSON> ma<PERSON>", "item.minecraft.cooked_chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_mutton": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_porkchop": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_rabbit": "Sült n<PERSON>", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cow_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.creaking_spawn_egg": "Csikorgóidé<PERSON><PERSON> toj<PERSON>", "item.minecraft.creeper_banner_pattern": "Zászlóminta", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per címerkép", "item.minecraft.creeper_banner_pattern.new": "Feltöltődött Creeper zászlóminta", "item.minecraft.creeper_spawn_egg": "Creeperidé<PERSON><PERSON> to<PERSON>", "item.minecraft.crossbow": "Számszeríj", "item.minecraft.crossbow.projectile": "Lövedék:", "item.minecraft.crossbow.projectile.multiple": "Lövedék: %s %s", "item.minecraft.crossbow.projectile.single": "Lövedék: %s", "item.minecraft.cyan_bundle": "Türkizkék batyu", "item.minecraft.cyan_dye": "Türkizkék festék", "item.minecraft.cyan_harness": "Türkizkék hám", "item.minecraft.danger_pottery_shard": "Cserépszilánk <PERSON>el", "item.minecraft.danger_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dark_oak_boat": "Sötéttölgy c<PERSON>ak", "item.minecraft.dark_oak_chest_boat": "Sötéttölgy csónak ládával", "item.minecraft.debug_stick": "Debugpálca", "item.minecraft.debug_stick.empty": "%s nem rendelkezik tulajdonságokkal", "item.minecraft.debug_stick.select": "\"%s\" kiválasztva (%s)", "item.minecraft.debug_stick.update": "\"%s\" értéke %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "Gyémántfejsze", "item.minecraft.diamond_boots": "Gyémántcsizma", "item.minecraft.diamond_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_helmet": "Gyémántsisak", "item.minecraft.diamond_hoe": "G<PERSON>émántkapa", "item.minecraft.diamond_horse_armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_leggings": "G<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "item.minecraft.diamond_pickaxe": "Gyémántcsákány", "item.minecraft.diamond_shovel": "Gyémántásó", "item.minecraft.diamond_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.disc_fragment_5": "Hanglemez-szi<PERSON>ánk", "item.minecraft.disc_fragment_5.desc": "Hanglemez – 5", "item.minecraft.dolphin_spawn_egg": "Delfinidé<PERSON><PERSON> to<PERSON>", "item.minecraft.donkey_spawn_egg": "Szam<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dragon_breath": "S<PERSON>rk<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dried_kelp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.drowned_spawn_egg": "Vízbefúltidéző toj<PERSON>", "item.minecraft.dune_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.dune_armor_trim_smithing_template.new": "Dűne-páncéldísz", "item.minecraft.echo_shard": "Visszhangszilánk", "item.minecraft.egg": "<PERSON><PERSON><PERSON>", "item.minecraft.elder_guardian_spawn_egg": "Vén őrszemet idéző toj<PERSON>", "item.minecraft.elytra": "Kitinszárny", "item.minecraft.emerald": "Smaragd", "item.minecraft.enchanted_book": "Varázskönyv", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_dragon_spawn_egg": "Endersárkány-<PERSON><PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.ender_eye": "<PERSON><PERSON> s<PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Endermanid<PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.endermite_spawn_egg": "Endermite-<PERSON><PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.evoker_spawn_egg": "Idézőt idéző tojás", "item.minecraft.experience_bottle": "Varázslat p<PERSON>ck<PERSON>", "item.minecraft.explorer_pottery_shard": "Cserépszilán<PERSON> t<PERSON>", "item.minecraft.explorer_pottery_sherd": "Felfedező cseréptöredék", "item.minecraft.eye_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.feather": "Toll", "item.minecraft.fermented_spider_eye": "Erjesztett pókszem", "item.minecraft.field_masoned_banner_pattern": "Falazott zászlóminta", "item.minecraft.filled_map": "Térkép", "item.minecraft.fire_charge": "Tűzgolyó", "item.minecraft.firework_rocket": "Tűzijáték", "item.minecraft.firework_rocket.flight": "Repülés ho<PERSON>:", "item.minecraft.firework_rocket.multiple_stars": "%s %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Tűzijátékcsillag", "item.minecraft.firework_star.black": "Fekete", "item.minecraft.firework_star.blue": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.brown": "<PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.cyan": "Türkizkék", "item.minecraft.firework_star.fade_to": "Színátmenet:", "item.minecraft.firework_star.flicker": "Villódzás", "item.minecraft.firework_star.gray": "Szürke", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Világoskék", "item.minecraft.firework_star.light_gray": "Világosszürke", "item.minecraft.firework_star.lime": "Világoszöld", "item.minecraft.firework_star.magenta": "Bíbor", "item.minecraft.firework_star.orange": "Narancssárga", "item.minecraft.firework_star.pink": "Róz<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.purple": "<PERSON>", "item.minecraft.firework_star.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape": "Ismeretlen forma", "item.minecraft.firework_star.shape.burst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.creeper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON>", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON>", "item.minecraft.firework_star.shape.star": "Csillag alakú", "item.minecraft.firework_star.trail": "Csóva", "item.minecraft.firework_star.white": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.yellow": "<PERSON><PERSON><PERSON>", "item.minecraft.fishing_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flint": "Kovakő", "item.minecraft.flint_and_steel": "Kovakő és acél", "item.minecraft.flow_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.flow_armor_trim_smithing_template.new": "Örvény-pán<PERSON><PERSON>", "item.minecraft.flow_banner_pattern": "Zászlóminta", "item.minecraft.flow_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.new": "Légörvény zászlóminta", "item.minecraft.flow_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern": "Zászlóminta", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "Virág címerképes zászlóminta", "item.minecraft.flower_pot": "Virágcserép", "item.minecraft.fox_spawn_egg": "Rókaid<PERSON><PERSON><PERSON>", "item.minecraft.friend_pottery_shard": "Cserépszilánk baráttal", "item.minecraft.friend_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.frog_spawn_egg": "Békaidé<PERSON><PERSON> to<PERSON>", "item.minecraft.furnace_minecart": "<PERSON><PERSON><PERSON>", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ghast_tear": "Ghastkönny", "item.minecraft.glass_bottle": "Üvegpalack", "item.minecraft.glistering_melon_slice": "Csillá<PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern": "Zászlóminta", "item.minecraft.globe_banner_pattern.desc": "<PERSON><PERSON>ldgö<PERSON>", "item.minecraft.globe_banner_pattern.new": "Földgömb<PERSON><PERSON>", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_ink_sac": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_item_frame": "Fénylő eszközkeret", "item.minecraft.glow_squid_spawn_egg": "Fénylő tintahalat idéző tojás", "item.minecraft.glowstone_dust": "Izzókőpor", "item.minecraft.goat_horn": "Kecsketülök", "item.minecraft.goat_spawn_egg": "Kecskeidé<PERSON><PERSON> to<PERSON>", "item.minecraft.gold_ingot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gold_nugget": "Ara<PERSON>rög", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "Aranyfejsze", "item.minecraft.golden_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "<PERSON><PERSON>", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "Ara<PERSON>ka<PERSON>", "item.minecraft.golden_horse_armor": "<PERSON><PERSON>", "item.minecraft.golden_leggings": "<PERSON><PERSON> l<PERSON>zárvédő", "item.minecraft.golden_pickaxe": "Aranycsákány", "item.minecraft.golden_shovel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_sword": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gray_bundle": "Szürke batyu", "item.minecraft.gray_dye": "Szürke festék", "item.minecraft.gray_harness": "Szürke hám", "item.minecraft.green_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.green_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.guardian_spawn_egg": "Őrszemidéző tojás", "item.minecraft.gunpowder": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern": "Zászlóminta", "item.minecraft.guster_banner_pattern.desc": "Széllökő", "item.minecraft.guster_banner_pattern.new": "Széllökő zászlóminta", "item.minecraft.guster_pottery_sherd": "Széllökő cseréptöredék", "item.minecraft.happy_ghast_spawn_egg": "Vidám ghastot idéző toj<PERSON>", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON> s<PERSON>", "item.minecraft.heart_pottery_shard": "Cserépszilánk szívvel", "item.minecraft.heart_pottery_sherd": "Szív cseréptöredék", "item.minecraft.heartbreak_pottery_shard": "Cserépszilánk törött szívvel", "item.minecraft.heartbreak_pottery_sherd": "Összetört szív cseréptöredék", "item.minecraft.hoglin_spawn_egg": "Hoglinidé<PERSON><PERSON>", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.honeycomb": "Méhsejt", "item.minecraft.hopper_minecart": "Tölcséres csille", "item.minecraft.horse_spawn_egg": "Lóidé<PERSON><PERSON> to<PERSON>", "item.minecraft.host_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.host_armor_trim_smithing_template.new": "Gazda páncéldísz", "item.minecraft.howl_pottery_shard": "Cserépszilánk far<PERSON>", "item.minecraft.howl_pottery_sherd": "Üvöltés cseréptöredék", "item.minecraft.husk_spawn_egg": "Kiszáradt zombit idéző tojás", "item.minecraft.ink_sac": "Tintazsák", "item.minecraft.iron_axe": "Vasfejsze", "item.minecraft.iron_boots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_chestplate": "<PERSON><PERSON> mell<PERSON>", "item.minecraft.iron_golem_spawn_egg": "Vasgólemidé<PERSON><PERSON> to<PERSON>", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_hoe": "Vaskapa", "item.minecraft.iron_horse_armor": "Vas lópáncél", "item.minecraft.iron_ingot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_leggings": "Vas lábszárvédő", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "Vascsákány", "item.minecraft.iron_shovel": "Vasásó", "item.minecraft.iron_sword": "Vaskard", "item.minecraft.item_frame": "Eszközkeret", "item.minecraft.jungle_boat": "Dzsungelfa csónak", "item.minecraft.jungle_chest_boat": "Dzsungelfa csónak ládával", "item.minecraft.knowledge_book": "Tudás kö<PERSON>ve", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Lávásvödör", "item.minecraft.lead": "<PERSON><PERSON><PERSON>", "item.minecraft.leather": "Bőr", "item.minecraft.leather_boots": "Bőrcsizma", "item.minecraft.leather_chestplate": "Bőrzubbony", "item.minecraft.leather_helmet": "Bőrsisak", "item.minecraft.leather_horse_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_leggings": "Bőrnadrág", "item.minecraft.light_blue_bundle": "Világoskék batyu", "item.minecraft.light_blue_dye": "Világoskék festék", "item.minecraft.light_blue_harness": "Világoskék hám", "item.minecraft.light_gray_bundle": "Világossz<PERSON><PERSON><PERSON> batyu", "item.minecraft.light_gray_dye": "Világosszürke festék", "item.minecraft.light_gray_harness": "Világosszürke hám", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>yu", "item.minecraft.lime_dye": "Világoszöld festék", "item.minecraft.lime_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion": "Id<PERSON><PERSON><PERSON> bájital", "item.minecraft.lingering_potion.effect.awkward": "Ügyetlen időző bájital", "item.minecraft.lingering_potion.effect.empty": "Nem főzhető időző bájital", "item.minecraft.lingering_potion.effect.fire_resistance": "Tűzállóság időző bájitala", "item.minecraft.lingering_potion.effect.harming": "Sebzés időző bájitala", "item.minecraft.lingering_potion.effect.healing": "Gyógyítás időző bájitala", "item.minecraft.lingering_potion.effect.infested": "Molytenyészet időző bájitala", "item.minecraft.lingering_potion.effect.invisibility": "Láthatatlanság időző bájitala", "item.minecraft.lingering_potion.effect.leaping": "Szökkenés időző bájitala", "item.minecraft.lingering_potion.effect.levitation": "Lebegés <PERSON> bájitala", "item.minecraft.lingering_potion.effect.luck": "Szerencse időző bájitala", "item.minecraft.lingering_potion.effect.mundane": "Átlagos id<PERSON>ző bájital", "item.minecraft.lingering_potion.effect.night_vision": "Éjjellátás időző bájitala", "item.minecraft.lingering_potion.effect.oozing": "Nyálkaképzés időző bájitala", "item.minecraft.lingering_potion.effect.poison": "Mérgezés időző bájitala", "item.minecraft.lingering_potion.effect.regeneration": "Regeneráció id<PERSON>ző bájitala", "item.minecraft.lingering_potion.effect.slow_falling": "<PERSON><PERSON><PERSON> bájitala", "item.minecraft.lingering_potion.effect.slowness": "Lomhaság <PERSON> bájitala", "item.minecraft.lingering_potion.effect.strength": "<PERSON><PERSON><PERSON> bájitala", "item.minecraft.lingering_potion.effect.swiftness": "Fürgeség időző bájitala", "item.minecraft.lingering_potion.effect.thick": "Sűrű időző bájital", "item.minecraft.lingering_potion.effect.turtle_master": "Teknősmester időző bájitala", "item.minecraft.lingering_potion.effect.water": "Időző vizespalack", "item.minecraft.lingering_potion.effect.water_breathing": "Víz alatti légzés időző bájitala", "item.minecraft.lingering_potion.effect.weakness": "Gyengeség időző bájitala", "item.minecraft.lingering_potion.effect.weaving": "Hálószövés időző bájitala", "item.minecraft.lingering_potion.effect.wind_charged": "Széllökés időző bájitala", "item.minecraft.llama_spawn_egg": "Lá<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lodestone_compass": "Mágnesezett iránytű", "item.minecraft.mace": "Buzogány", "item.minecraft.magenta_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.magenta_dye": "Bíbor festék", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Magmakrém", "item.minecraft.magma_cube_spawn_egg": "Magmakocka-idé<PERSON><PERSON> to<PERSON>", "item.minecraft.mangrove_boat": "Mangrovefa csónak", "item.minecraft.mangrove_chest_boat": "Mangrovefa csónak ládával", "item.minecraft.map": "<PERSON><PERSON> t<PERSON>ép", "item.minecraft.melon_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.melon_slice": "Dinnyeszelet", "item.minecraft.milk_bucket": "Tejesvödör", "item.minecraft.minecart": "Csille", "item.minecraft.miner_pottery_shard": "Cserépszilánk csákánnyal", "item.minecraft.miner_pottery_sherd": "Bányász cseréptöredék", "item.minecraft.mojang_banner_pattern": "Zászlóminta", "item.minecraft.mojang_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.new": "<PERSON><PERSON><PERSON>", "item.minecraft.mooshroom_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mourner_pottery_shard": "Cserépszilánk gyászolóval", "item.minecraft.mourner_pottery_sherd": "Gyászoló cseréptöred<PERSON>k", "item.minecraft.mule_spawn_egg": "Öszvé<PERSON><PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.mushroom_stew": "Gombaragu", "item.minecraft.music_disc_11": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11.desc": "C418 – 11", "item.minecraft.music_disc_13": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_13.desc": "C418 – 13", "item.minecraft.music_disc_5": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_5.desc": "<PERSON> – 5", "item.minecraft.music_disc_blocks": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_blocks.desc": "C418 – blocks", "item.minecraft.music_disc_cat": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_cat.desc": "C418 – cat", "item.minecraft.music_disc_chirp": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_chirp.desc": "C418 – chirp", "item.minecraft.music_disc_creator": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_creator.desc": "<PERSON> – <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> – Creator (zenedoboz)", "item.minecraft.music_disc_far": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_far.desc": "C418 – far", "item.minecraft.music_disc_lava_chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - <PERSON><PERSON><PERSON>", "item.minecraft.music_disc_mall": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_mall.desc": "C418 – mall", "item.minecraft.music_disc_mellohi": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_mellohi.desc": "C418 – mellohi", "item.minecraft.music_disc_otherside": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_otherside.desc": "<PERSON> – <PERSON>ide", "item.minecraft.music_disc_pigstep": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_pigstep.desc": "<PERSON> Pigstep", "item.minecraft.music_disc_precipice": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_precipice.desc": "<PERSON> – Precipice", "item.minecraft.music_disc_relic": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_relic.desc": "<PERSON> <PERSON><PERSON>", "item.minecraft.music_disc_stal": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_stal.desc": "C418 – stal", "item.minecraft.music_disc_strad": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_strad.desc": "C418 – strad", "item.minecraft.music_disc_tears": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_tears.desc": "<PERSON> – Tears", "item.minecraft.music_disc_wait": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_wait.desc": "C418 – wait", "item.minecraft.music_disc_ward": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_ward.desc": "C418 – ward", "item.minecraft.mutton": "Nyers birkahús", "item.minecraft.name_tag": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.nautilus_shell": "Nautiluszhéj", "item.minecraft.nether_brick": "Nethertégla", "item.minecraft.nether_star": "Nethercsillag", "item.minecraft.nether_wart": "<PERSON>her <PERSON>", "item.minecraft.netherite_axe": "Netheritfejsze", "item.minecraft.netherite_boots": "Netheritcsizma", "item.minecraft.netherite_chestplate": "<PERSON><PERSON><PERSON> mell<PERSON>", "item.minecraft.netherite_helmet": "Netheritsisak", "item.minecraft.netherite_hoe": "<PERSON><PERSON>itka<PERSON>", "item.minecraft.netherite_ingot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_leggings": "Netherit lábszárvédő", "item.minecraft.netherite_pickaxe": "Netheritcsákány", "item.minecraft.netherite_scrap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_shovel": "Netheritásó", "item.minecraft.netherite_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template": "Kovácssablon", "item.minecraft.netherite_upgrade_smithing_template.new": "<PERSON><PERSON><PERSON> fej<PERSON>", "item.minecraft.oak_boat": "Tölgyfa csónak", "item.minecraft.oak_chest_boat": "Tölgyfa csónak ládával", "item.minecraft.ocelot_spawn_egg": "Ocelotidéz<PERSON> to<PERSON>", "item.minecraft.ominous_bottle": "Vészj<PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.ominous_trial_key": "Vészj<PERSON><PERSON><PERSON>", "item.minecraft.orange_bundle": "Narancss<PERSON><PERSON> batyu", "item.minecraft.orange_dye": "Narancssárga festék", "item.minecraft.orange_harness": "Narancssárga hám", "item.minecraft.painting": "Festmény", "item.minecraft.pale_oak_boat": "Sápadtt<PERSON><PERSON>gy c<PERSON>", "item.minecraft.pale_oak_chest_boat": "Sápadttölgy csónak ládával", "item.minecraft.panda_spawn_egg": "Pandaidéző to<PERSON>", "item.minecraft.paper": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Papag<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.phantom_membrane": "Fantommembrán", "item.minecraft.phantom_spawn_egg": "Fantomidé<PERSON><PERSON> to<PERSON>", "item.minecraft.pig_spawn_egg": "Malacidé<PERSON><PERSON> to<PERSON>", "item.minecraft.piglin_banner_pattern": "Zászlóminta", "item.minecraft.piglin_banner_pattern.desc": "Disznóorr", "item.minecraft.piglin_banner_pattern.new": "<PERSON><PERSON><PERSON>", "item.minecraft.piglin_brute_spawn_egg": "Piglin verőlegényt idéző toj<PERSON>", "item.minecraft.piglin_spawn_egg": "Piglinidéző to<PERSON>", "item.minecraft.pillager_spawn_egg": "Rablóid<PERSON><PERSON><PERSON>", "item.minecraft.pink_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>yu", "item.minecraft.pink_dye": "Rózsaszín festék", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>m", "item.minecraft.pitcher_plant": "Kancsókanövény", "item.minecraft.pitcher_pod": "Kancsókahüvely", "item.minecraft.plenty_pottery_shard": "Cserépszilánk ládával", "item.minecraft.plenty_pottery_sherd": "Bőség cseréptöredék", "item.minecraft.poisonous_potato": "Mérgező burgonya", "item.minecraft.polar_bear_spawn_egg": "Jegesmedve-idéző toj<PERSON>", "item.minecraft.popped_chorus_fruit": "Pattogatott refréngyümölcs", "item.minecraft.porkchop": "Nyers disznóhús", "item.minecraft.potato": "Burgonya", "item.minecraft.potion": "Bájital", "item.minecraft.potion.effect.awkward": "Ügyetlen bájital", "item.minecraft.potion.effect.empty": "<PERSON><PERSON> bájit<PERSON>", "item.minecraft.potion.effect.fire_resistance": "Tűz<PERSON>ll<PERSON><PERSON><PERSON> bájit<PERSON>", "item.minecraft.potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "item.minecraft.potion.effect.healing": "Gyógyítás bájitala", "item.minecraft.potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bájit<PERSON>", "item.minecraft.potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "item.minecraft.potion.effect.leaping": "Szökkenés bájitala", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.luck": "Szerencse bájitala", "item.minecraft.potion.effect.mundane": "Átl<PERSON>s bá<PERSON>", "item.minecraft.potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "item.minecraft.potion.effect.oozing": "Nyálkaképzés bájitala", "item.minecraft.potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON> bájital", "item.minecraft.potion.effect.regeneration": "Regener<PERSON>ció b<PERSON>", "item.minecraft.potion.effect.slow_falling": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "item.minecraft.potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "item.minecraft.potion.effect.strength": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "item.minecraft.potion.effect.thick": "Sűrű bájital", "item.minecraft.potion.effect.turtle_master": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "item.minecraft.potion.effect.water": "Vizespalack", "item.minecraft.potion.effect.water_breathing": "V<PERSON>z alatti légzés bájitala", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON>nges<PERSON><PERSON> b<PERSON>", "item.minecraft.potion.effect.weaving": "Hálószövés bájitala", "item.minecraft.potion.effect.wind_charged": "Széllökés bájitala", "item.minecraft.pottery_shard_archer": "Cserépszilánk <PERSON>", "item.minecraft.pottery_shard_arms_up": "Cserépszilán<PERSON> karok<PERSON>", "item.minecraft.pottery_shard_prize": "Cserépszilánk kinccsel", "item.minecraft.pottery_shard_skull": "Cserépszilánk koponyával", "item.minecraft.powder_snow_bucket": "Porhavas vödör", "item.minecraft.prismarine_crystals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_shard": "Prizmarinszilánk", "item.minecraft.prize_pottery_shard": "Cserépszilánk kinccsel", "item.minecraft.prize_pottery_sherd": "Jutalom cseréptöredék", "item.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pufferfish_bucket": "Gömbhal vödörben", "item.minecraft.pufferfish_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pumpkin_pie": "Tökpite", "item.minecraft.pumpkin_seeds": "Tökmag", "item.minecraft.purple_bundle": "<PERSON>", "item.minecraft.purple_dye": "<PERSON> f<PERSON>", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "Netherkvarc", "item.minecraft.rabbit": "Nyers nyúlhús", "item.minecraft.rabbit_foot": "Nyúlláb", "item.minecraft.rabbit_hide": "<PERSON><PERSON>úlb<PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "Nyúlid<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_stew": "Nyúlpörkölt", "item.minecraft.raiser_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.raiser_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON> páncéld<PERSON>", "item.minecraft.ravager_spawn_egg": "Pusztítóidé<PERSON>ő to<PERSON>", "item.minecraft.raw_copper": "Nyers<PERSON>z", "item.minecraft.raw_gold": "Nyersarany", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "Visszatérés iránytűje", "item.minecraft.red_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.redstone": "Redstone-por", "item.minecraft.resin_brick": "Gyantatégla", "item.minecraft.resin_clump": "Gyan<PERSON><PERSON>b", "item.minecraft.rib_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON>rd<PERSON> p<PERSON>", "item.minecraft.rotten_flesh": "<PERSON><PERSON><PERSON><PERSON> hús", "item.minecraft.saddle": "<PERSON><PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON> lazac", "item.minecraft.salmon_bucket": "Lazac vödörben", "item.minecraft.salmon_spawn_egg": "Lazacidé<PERSON><PERSON> to<PERSON>", "item.minecraft.scrape_pottery_sherd": "<PERSON><PERSON><PERSON>ö<PERSON>", "item.minecraft.scute": "Szarulemez", "item.minecraft.sentry_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.sentry_armor_trim_smithing_template.new": "Őrségi pán<PERSON><PERSON><PERSON>z", "item.minecraft.shaper_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.shaper_armor_trim_smithing_template.new": "Alakító <PERSON>", "item.minecraft.sheaf_pottery_shard": "Cserépszilánk köteggel", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.shears": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sheep_spawn_egg": "Birkaidéző to<PERSON>", "item.minecraft.shelter_pottery_shard": "Cseréps<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shelter_pottery_sherd": "Menedék cseréptöredék", "item.minecraft.shield": "Pajz<PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.shield.blue": "Kék <PERSON>", "item.minecraft.shield.brown": "<PERSON>na paj<PERSON>", "item.minecraft.shield.cyan": "Türkizkék pajzs", "item.minecraft.shield.gray": "Szürke pajzs", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.light_blue": "Világoskék pajzs", "item.minecraft.shield.light_gray": "Világosszürke pajzs", "item.minecraft.shield.lime": "Világos<PERSON><PERSON>ld <PERSON>", "item.minecraft.shield.magenta": "Bíbor <PERSON>", "item.minecraft.shield.orange": "Narancssárga pajzs", "item.minecraft.shield.pink": "Rózsa<PERSON><PERSON> paj<PERSON>", "item.minecraft.shield.purple": "<PERSON> p<PERSON>", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON> paj<PERSON>", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "Shulkerid<PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.sign": "Tábla", "item.minecraft.silence_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.silence_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.silverfish_spawn_egg": "Ezüstmolyidé<PERSON>ő toj<PERSON>", "item.minecraft.skeleton_horse_spawn_egg": "Csontvázlóidéző toj<PERSON>", "item.minecraft.skeleton_spawn_egg": "Csontvázidé<PERSON><PERSON> toj<PERSON>", "item.minecraft.skull_banner_pattern": "Zászlóminta", "item.minecraft.skull_banner_pattern.desc": "Koponya címerkép", "item.minecraft.skull_banner_pattern.new": "Koponya címerképes zászlóminta", "item.minecraft.skull_pottery_shard": "Cserépszilánk koponyával", "item.minecraft.skull_pottery_sherd": "Koponya cseréptöredék", "item.minecraft.slime_ball": "Nyálkagolyó", "item.minecraft.slime_spawn_egg": "Nyálkaid<PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.smithing_template": "Kovácssablon", "item.minecraft.smithing_template.applies_to": "Alkalmazható:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "He<PERSON>ezz ide fémrudat vagy k<PERSON>", "item.minecraft.smithing_template.armor_trim.applies_to": "Páncélza<PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "He<PERSON>ezz ide páncélt", "item.minecraft.smithing_template.armor_trim.ingredients": "Fémek és kristályok", "item.minecraft.smithing_template.ingredients": "Összetevők:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "<PERSON><PERSON><PERSON><PERSON> be netherit rudat", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Gyémántfelszerelésre", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "He<PERSON>ezz ide gyémántpáncélt, -fegy<PERSON> vagy -eszközt", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.upgrade": "Fejlesztés: ", "item.minecraft.sniffer_spawn_egg": "Orrontóidé<PERSON><PERSON> to<PERSON>", "item.minecraft.snort_pottery_shard": "Cserépszilánk orrontóval", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON> cseréptö<PERSON>k", "item.minecraft.snout_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.snout_armor_trim_smithing_template.new": "Disznóorros p<PERSON>", "item.minecraft.snow_golem_spawn_egg": "Hógólemidé<PERSON><PERSON> to<PERSON>", "item.minecraft.snowball": "Hógolyó", "item.minecraft.spectral_arrow": "Spektrálnyíl", "item.minecraft.spider_eye": "Pókszem", "item.minecraft.spider_spawn_egg": "Pókidé<PERSON><PERSON> to<PERSON>", "item.minecraft.spire_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.spire_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON> b<PERSON>", "item.minecraft.splash_potion.effect.awkward": "Ügyetlen robban<PERSON> bá<PERSON>al", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON> robbanó bájital", "item.minecraft.splash_potion.effect.fire_resistance": "Tűzállós<PERSON>g rob<PERSON> bájit<PERSON>", "item.minecraft.splash_potion.effect.harming": "Sebzés rob<PERSON><PERSON> b<PERSON>", "item.minecraft.splash_potion.effect.healing": "Gyógyítás robbanó bájitala", "item.minecraft.splash_potion.effect.infested": "Molytenyészet robbanó bájitala", "item.minecraft.splash_potion.effect.invisibility": "Láthatatlanság rob<PERSON> b<PERSON>jit<PERSON>", "item.minecraft.splash_potion.effect.leaping": "Szökkenés robbanó bájitala", "item.minecraft.splash_potion.effect.levitation": "Lebe<PERSON><PERSON> rob<PERSON> b<PERSON>", "item.minecraft.splash_potion.effect.luck": "Szerencse robbanó bájit<PERSON>", "item.minecraft.splash_potion.effect.mundane": "Átlagos robbanó bá<PERSON>", "item.minecraft.splash_potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rob<PERSON> b<PERSON>jit<PERSON>", "item.minecraft.splash_potion.effect.oozing": "Nyálkaképzés robbanó bájitala", "item.minecraft.splash_potion.effect.poison": "Mérgezés rob<PERSON><PERSON> b<PERSON>", "item.minecraft.splash_potion.effect.regeneration": "Regeneráció rob<PERSON> b<PERSON>", "item.minecraft.splash_potion.effect.slow_falling": "<PERSON><PERSON><PERSON> robban<PERSON> b<PERSON>", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON> rob<PERSON> b<PERSON><PERSON>", "item.minecraft.splash_potion.effect.strength": "<PERSON><PERSON><PERSON> r<PERSON> b<PERSON>", "item.minecraft.splash_potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON><PERSON> rob<PERSON> b<PERSON>", "item.minecraft.splash_potion.effect.thick": "Sűrű robbanó bájital", "item.minecraft.splash_potion.effect.turtle_master": "Teknősmester rob<PERSON><PERSON> b<PERSON>", "item.minecraft.splash_potion.effect.water": "Robbanó vizespalack", "item.minecraft.splash_potion.effect.water_breathing": "Víz alatti légzés robbanó bájitala", "item.minecraft.splash_potion.effect.weakness": "Gyengeség rob<PERSON> b<PERSON>", "item.minecraft.splash_potion.effect.weaving": "Hálószövés robbanó bájitala", "item.minecraft.splash_potion.effect.wind_charged": "Széllökés robbanó bájitala", "item.minecraft.spruce_boat": "Fenyőfa csónak", "item.minecraft.spruce_chest_boat": "Fenyőfa csónak ládával", "item.minecraft.spyglass": "Távcső", "item.minecraft.squid_spawn_egg": "Tintahalidé<PERSON><PERSON>", "item.minecraft.stick": "Bot", "item.minecraft.stone_axe": "Kőfejsze", "item.minecraft.stone_hoe": "Kőkapa", "item.minecraft.stone_pickaxe": "Kőcsákány", "item.minecraft.stone_shovel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_sword": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stray_spawn_egg": "Kóborlóid<PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.strider_spawn_egg": "Álomfutóidé<PERSON><PERSON> to<PERSON>", "item.minecraft.string": "Fonál", "item.minecraft.sugar": "<PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON><PERSON><PERSON>", "item.minecraft.sweet_berries": "<PERSON><PERSON>", "item.minecraft.tadpole_bucket": "Ebihal vödörben", "item.minecraft.tadpole_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.tide_armor_trim_smithing_template.new": "Hullámos <PERSON>", "item.minecraft.tipped_arrow": "Varázsnyíl", "item.minecraft.tipped_arrow.effect.awkward": "Varázsnyíl", "item.minecraft.tipped_arrow.effect.empty": "Nem elkészíthető varázsnyíl", "item.minecraft.tipped_arrow.effect.fire_resistance": "Tűzállóság nyila", "item.minecraft.tipped_arrow.effect.harming": "Sebzés nyila", "item.minecraft.tipped_arrow.effect.healing": "Gyógyítás nyila", "item.minecraft.tipped_arrow.effect.infested": "Molytenyészet nyila", "item.minecraft.tipped_arrow.effect.invisibility": "Láthatatlanság nyila", "item.minecraft.tipped_arrow.effect.leaping": "Szökkenés nyila", "item.minecraft.tipped_arrow.effect.levitation": "Le<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.luck": "Szerencse nyila", "item.minecraft.tipped_arrow.effect.mundane": "Varázsnyíl", "item.minecraft.tipped_arrow.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.tipped_arrow.effect.oozing": "Nyálkaképzés nyila", "item.minecraft.tipped_arrow.effect.poison": "Mérgezett nyíl", "item.minecraft.tipped_arrow.effect.regeneration": "Regeneráció n<PERSON>", "item.minecraft.tipped_arrow.effect.slow_falling": "<PERSON><PERSON><PERSON> n<PERSON>la", "item.minecraft.tipped_arrow.effect.slowness": "Lomhas<PERSON><PERSON> n<PERSON>la", "item.minecraft.tipped_arrow.effect.strength": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.thick": "Varázsnyíl", "item.minecraft.tipped_arrow.effect.turtle_master": "Teknősmester nyila", "item.minecraft.tipped_arrow.effect.water": "Vizes nyíl", "item.minecraft.tipped_arrow.effect.water_breathing": "Vízlégzés nyila", "item.minecraft.tipped_arrow.effect.weakness": "Gyengeség n<PERSON>", "item.minecraft.tipped_arrow.effect.weaving": "Hálószövés nyila", "item.minecraft.tipped_arrow.effect.wind_charged": "Széllökés nyila", "item.minecraft.tnt_minecart": "TNT-csille", "item.minecraft.torchflower_seeds": "Fáklyaliliommagok", "item.minecraft.totem_of_undying": "Életmentés toteme", "item.minecraft.trader_llama_spawn_egg": "Kereskedőláma-id<PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.trial_key": "Küzdő<PERSON><PERSON> k<PERSON>", "item.minecraft.trident": "Szigony", "item.minecraft.tropical_fish": "Trópusi hal", "item.minecraft.tropical_fish_bucket": "Trópusi hal vödörben", "item.minecraft.tropical_fish_spawn_egg": "Trópusi halat idéz<PERSON> to<PERSON>", "item.minecraft.turtle_helmet": "Teknőspáncél", "item.minecraft.turtle_scute": "Teknős-szarulemez", "item.minecraft.turtle_spawn_egg": "Teknősidéző toj<PERSON>", "item.minecraft.vex_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.vex_armor_trim_smithing_template.new": "Bosszancs-páncéldísz", "item.minecraft.vex_spawn_egg": "Bosszancsid<PERSON><PERSON><PERSON>", "item.minecraft.villager_spawn_egg": "Falusiidé<PERSON>ő to<PERSON>", "item.minecraft.vindicator_spawn_egg": "Bosszúálló-idé<PERSON><PERSON> to<PERSON>", "item.minecraft.wandering_trader_spawn_egg": "Vándorkereskedő-idéző toj<PERSON>", "item.minecraft.ward_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.ward_armor_trim_smithing_template.new": "Strázsa-páncéldísz", "item.minecraft.warden_spawn_egg": "Strázsaid<PERSON><PERSON><PERSON> toj<PERSON>", "item.minecraft.warped_fungus_on_a_stick": "<PERSON>z gomba horgás<PERSON><PERSON><PERSON>", "item.minecraft.water_bucket": "Vizesvödör", "item.minecraft.wayfinder_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Vándor-p<PERSON><PERSON><PERSON>", "item.minecraft.wheat": "Búza", "item.minecraft.wheat_seeds": "Búzamag", "item.minecraft.white_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "Kovácssablon", "item.minecraft.wild_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.wind_charge": "Széllöket", "item.minecraft.witch_spawn_egg": "Boszorkányidé<PERSON><PERSON> toj<PERSON>", "item.minecraft.wither_skeleton_spawn_egg": "Withercsontváz-idé<PERSON>ő toj<PERSON>", "item.minecraft.wither_spawn_egg": "Witherid<PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.wolf_armor": "Farkaspáncél", "item.minecraft.wolf_spawn_egg": "Farkasidé<PERSON><PERSON> to<PERSON>", "item.minecraft.wooden_axe": "Fafejsze", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "Facsákány", "item.minecraft.wooden_shovel": "Faásó", "item.minecraft.wooden_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.writable_book": "Könyv és toll", "item.minecraft.written_book": "Befejezett könyv", "item.minecraft.yellow_bundle": "<PERSON><PERSON><PERSON> batyu", "item.minecraft.yellow_dye": "<PERSON><PERSON><PERSON> festék", "item.minecraft.yellow_harness": "<PERSON><PERSON><PERSON> h<PERSON>", "item.minecraft.zoglin_spawn_egg": "Zoglinidéz<PERSON> to<PERSON>", "item.minecraft.zombie_horse_spawn_egg": "Zombilóid<PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.zombie_spawn_egg": "Zombiidéző to<PERSON>", "item.minecraft.zombie_villager_spawn_egg": "Élőhalott falusit idéző toj<PERSON>", "item.minecraft.zombified_piglin_spawn_egg": "Élőhalott piglint id<PERSON><PERSON><PERSON>", "item.modifiers.any": "Felszerelve:", "item.modifiers.armor": "Viseléskor:", "item.modifiers.body": "Felszerelve:", "item.modifiers.chest": "<PERSON>en hordva:", "item.modifiers.feet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:", "item.modifiers.hand": "Kézben tartva:", "item.modifiers.head": "<PERSON><PERSON><PERSON>:", "item.modifiers.legs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hord<PERSON>:", "item.modifiers.mainhand": "Domin<PERSON>s k<PERSON>zben tartva:", "item.modifiers.offhand": "Másodlagos kézben tartva:", "item.modifiers.saddle": "Felnyergelve:", "item.nbt_tags": "NBT: %s címke", "item.op_block_warning.line1": "Figyelem:", "item.op_block_warning.line2": "A tárgy lehelyezése parancsvégrehajtást eredményezhet", "item.op_block_warning.line3": "<PERSON><PERSON>, hacsak nem ismered a pontos tartalmát!", "item.unbreakable": "Törhetetlen", "itemGroup.buildingBlocks": "Építőelemek", "itemGroup.coloredBlocks": "Színezett blokkok", "itemGroup.combat": "<PERSON><PERSON>", "itemGroup.consumables": "Ételek és italok", "itemGroup.crafting": "Barkácsolás", "itemGroup.foodAndDrink": "Ételek és italok", "itemGroup.functional": "Használati blokkok", "itemGroup.hotbar": "Elmentett g<PERSON>", "itemGroup.ingredients": "Összetevők", "itemGroup.inventory": "Túlélő f<PERSON>zerelés", "itemGroup.natural": "Természetes blokkok", "itemGroup.op": "Operátori eszközök", "itemGroup.redstone": "Vöröskő-blokkok", "itemGroup.search": "Keresés", "itemGroup.spawnEggs": "Id<PERSON>ző<PERSON>j<PERSON>ok", "itemGroup.tools": "Eszközök", "item_modifier.unknown": "Ismeretlen tárgymódosító: %s", "jigsaw_block.final_state": "<PERSON><PERSON><PERSON>:", "jigsaw_block.generate": "Gener<PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint.rollable": "Forgathat<PERSON>", "jigsaw_block.joint_label": "Rögzítés típusa:", "jigsaw_block.keep_jigsaws": "Kirakósblokkok megtartása", "jigsaw_block.levels": "Szintek: %s", "jigsaw_block.name": "Név:", "jigsaw_block.placement_priority": "Elhelyezési prioritás:", "jigsaw_block.placement_priority.tooltip": "Amikor ez a kirakósblokk egy részstruktúrához illeszkedik, ez határozza meg, hogy az a részstruktúra hol álljon az újabb illesztések feldolgozásának sorrendjében a teljes struktúrán belül.\n\nA részstruktúrák feldolgozása a legmagasabb prioritásúval kezdődik; az azonos prioritásúaknál a beszúrási sorrend dönt.", "jigsaw_block.pool": "Célhalmaz:", "jigsaw_block.selection_priority": "Kiválasztási prioritás:", "jigsaw_block.selection_priority.tooltip": "Amikor az újabb illesztések feldolgozásában az ezt a kirakósblokkot tartalmazó részstruktúrára kerül a sor, ez határozza meg, hogy ez a blokk hol álljon az illesztési kísérletek sorrendjében a részstruktúrán belül.\n\nA kirakósblokkok feldolgozása a legmagasabb prioritásúval kezdődik; az azonos prioritásúak véletlenszerűen kerülnek sorra.", "jigsaw_block.target": "<PERSON><PERSON><PERSON> neve:", "jukebox_song.minecraft.11": "C418 – 11", "jukebox_song.minecraft.13": "C418 – 13", "jukebox_song.minecraft.5": "<PERSON> – 5", "jukebox_song.minecraft.blocks": "C418 – blocks", "jukebox_song.minecraft.cat": "C418 – cat", "jukebox_song.minecraft.chirp": "C418 – chirp", "jukebox_song.minecraft.creator": "<PERSON> – <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> – Creator (zenedoboz)", "jukebox_song.minecraft.far": "C418 – far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - <PERSON><PERSON><PERSON>", "jukebox_song.minecraft.mall": "C418 – mall", "jukebox_song.minecraft.mellohi": "C418 – mellohi", "jukebox_song.minecraft.otherside": "<PERSON> – <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> – Precipice", "jukebox_song.minecraft.relic": "<PERSON> <PERSON><PERSON>", "jukebox_song.minecraft.stal": "C418 – stal", "jukebox_song.minecraft.strad": "C418 – strad", "jukebox_song.minecraft.tears": "<PERSON> – Tears", "jukebox_song.minecraft.wait": "C418 – wait", "jukebox_song.minecraft.ward": "C418 – ward", "key.advancements": "Előrelépések", "key.attack": "Támadás/Blokktörés", "key.back": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.creative": "Krea<PERSON>í<PERSON> mód", "key.categories.gameplay": "Játékmenet", "key.categories.inventory": "Felszerelés", "key.categories.misc": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.movement": "Mozgás", "key.categories.multiplayer": "Többjáté<PERSON> mód", "key.categories.ui": "Kezelőfelület", "key.chat": "<PERSON>t <PERSON>", "key.command": "Paranc<PERSON>r <PERSON>", "key.drop": "Kiv<PERSON>lasztott t<PERSON><PERSON>", "key.forward": "Gyaloglás előre", "key.fullscreen": "<PERSON><PERSON><PERSON>", "key.hotbar.1": "Gyorstár 1-es rekesze", "key.hotbar.2": "Gyorstár 2-es rekesze", "key.hotbar.3": "Gyorstár 3-as re<PERSON><PERSON>", "key.hotbar.4": "Gyorstár 4-es rekesze", "key.hotbar.5": "Gyorstár 5-<PERSON><PERSON> <PERSON><PERSON>", "key.hotbar.6": "<PERSON><PERSON><PERSON><PERSON><PERSON> 6-<PERSON><PERSON> <PERSON><PERSON><PERSON>", "key.hotbar.7": "Gyorstár 7-<PERSON> <PERSON><PERSON><PERSON>", "key.hotbar.8": "<PERSON><PERSON><PERSON><PERSON><PERSON> 8-as <PERSON><PERSON><PERSON>", "key.hotbar.9": "Gyorstár 9-<PERSON> rekesze", "key.inventory": "Eszkö<PERSON><PERSON><PERSON><PERSON>/bezárás<PERSON>", "key.jump": "Ugrás", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Törlés", "key.keyboard.down": "Le", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Számbill. 0", "key.keyboard.keypad.1": "Számbill. 1", "key.keyboard.keypad.2": "Számbill. 2", "key.keyboard.keypad.3": "Számbill. 3", "key.keyboard.keypad.4": "Számbill. 4", "key.keyboard.keypad.5": "Számbill. 5", "key.keyboard.keypad.6": "Számbill. 6", "key.keyboard.keypad.7": "Számbill. 7", "key.keyboard.keypad.8": "Számbill. 8", "key.keyboard.keypad.9": "Számbill. 9", "key.keyboard.keypad.add": "Számbill. +", "key.keyboard.keypad.decimal": "Tizedesvessző", "key.keyboard.keypad.divide": "Számbill. /", "key.keyboard.keypad.enter": "Számbill. Enter", "key.keyboard.keypad.equal": "Számbill. =", "key.keyboard.keypad.multiply": "Számbill. *", "key.keyboard.keypad.subtract": "Számbill. -", "key.keyboard.left": "Balra", "key.keyboard.left.alt": "Bal Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Bal Ctrl", "key.keyboard.left.shift": "Bal Shift", "key.keyboard.left.win": "<PERSON><PERSON>", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "Jobbra", "key.keyboard.right.alt": "Jobb Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "<PERSON>b Ctrl", "key.keyboard.right.shift": "<PERSON><PERSON> Shift", "key.keyboard.right.win": "<PERSON><PERSON>", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Szóköz", "key.keyboard.tab": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.unknown": "<PERSON><PERSON><PERSON>", "key.keyboard.up": "<PERSON><PERSON>", "key.keyboard.world.1": "World 1", "key.keyboard.world.2": "World 2", "key.left": "Oldalazás balra", "key.loadToolbarActivator": "Gyorstárbetöltés módosítóbillentyűje", "key.mouse": "%1$s. egérgomb", "key.mouse.left": "Bal gomb", "key.mouse.middle": "Középs<PERSON> gomb", "key.mouse.right": "<PERSON><PERSON> gomb", "key.pickItem": "Blokk kiválasztása", "key.playerlist": "Játékoslista", "key.quickActions": "Gyorsműveletek", "key.right": "Oldalazás jobbra", "key.saveToolbarActivator": "Gyorstármentés módosítóbillentyűje", "key.screenshot": "Képernyőkép készítése", "key.smoothCamera": "Kameramozgás lágyítása", "key.sneak": "Lopakodás", "key.socialInteractions": "Közösségi interakciók képernyője", "key.spectatorOutlines": "Játékosok (Szemlélők) kiemelése", "key.sprint": "Futás", "key.swapOffhand": "<PERSON><PERSON><PERSON> a másik kézbe", "key.togglePerspective": "Nézetváltás", "key.use": "<PERSON><PERSON><PERSON>/Blokk lerakása", "known_server_link.announcements": "Bejelentések", "known_server_link.community": "Közösség", "known_server_link.community_guidelines": "Közösségi irányelvek", "known_server_link.feedback": "Visszajelzés", "known_server_link.forums": "Fórumok", "known_server_link.news": "<PERSON><PERSON><PERSON>", "known_server_link.report_bug": "Szerverhiba jelentése", "known_server_link.status": "<PERSON><PERSON><PERSON>", "known_server_link.support": "Támogatás", "known_server_link.website": "Weboldal", "lanServer.otherPlayers": "Más játékosok beállításai", "lanServer.port": "Port száma", "lanServer.port.invalid": "Érvénytelen port.\nHagyd üresen a szövegmezőt, vagy írj be egy számot 1024 és 65535 között.", "lanServer.port.invalid.new": "Érvénytelen port.\nHagyd üresen a szövegmezőt, vagy írj be egy számot %s és %s között.", "lanServer.port.unavailable": "A port nem elérhető.\nHagyd üresen a szövegmezőt, vagy írj be egy másik számot 1024 és 65535 között.", "lanServer.port.unavailable.new": "A port nem elérhető.\nHagyd üresen a szövegmezőt, vagy írj be egy másik számot %s és %s között.", "lanServer.scanning": "Játékok keresése a helyi hálózatodon", "lanServer.start": "LAN világ indítása", "lanServer.title": "LAN világ", "language.code": "hun_HU", "language.name": "<PERSON><PERSON><PERSON>", "language.region": "Magyarország", "lectern.take_book": "Könyv elrakása", "loading.progress": "%s%%", "mco.account.privacy.info": "Tudj meg többet a Mojangról és az adatvédelmi törvényekről", "mco.account.privacy.info.button": "<PERSON><PERSON><PERSON><PERSON> többet a GDPR-ról", "mco.account.privacy.information": "A Mojang bizonyos eljárásokat vezet be a gyermekek és a magánéletük védelme érdekében, beleértve a Gyermekek Online Adatvédelmi Törvénye (COPPA) és az Általános Adatvédelmi Rendelet (GDPR) betartását.\n\nLe<PERSON><PERSON>, hogy szülői beleegyezést kell kérned a Realms-fiókod elérése előtt.", "mco.account.privacyinfo": "A Mojang bizonyos eljárásokat vezet be a gyermekek és a magánéletük védelme érdekében, beleértve a Gyermekek Online Adatvédelmi Törvénye (COPPA) és az Általános Adatvédelmi Rendelet (GDPR) betartását.\n\nLe<PERSON><PERSON>, hogy szülői beleegyezést kell kérned a Realms-fiókod elérése előtt.\n\nHa régebbi Minecraft-fiókod van (a felhasználóneveddel jelentkezel be), át kell költöztetned a fiókot egy Mojang fiókra a Realms eléréséhez.", "mco.account.update": "<PERSON>ók f<PERSON>", "mco.activity.noactivity": "Nem volt aktivitás az elmúlt %s napban", "mco.activity.title": "Játékosaktivitiás", "mco.backup.button.download": "Legfrissebb letöltése", "mco.backup.button.reset": "Visszaállítás", "mco.backup.button.restore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.button.upload": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.changes.tooltip": "Változások", "mco.backup.entry": "Biztonsági <PERSON> (%s)", "mco.backup.entry.description": "Le<PERSON><PERSON><PERSON>", "mco.backup.entry.enabledPack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>g", "mco.backup.entry.gameDifficulty": "Játék nehézsége", "mco.backup.entry.gameMode": "<PERSON><PERSON><PERSON>kmó<PERSON>", "mco.backup.entry.gameServerVersion": "Játékszerver verziója", "mco.backup.entry.name": "Név", "mco.backup.entry.seed": "Kezdőérték", "mco.backup.entry.templateName": "Sablon neve", "mco.backup.entry.undefined": "Meghatározatlan változás", "mco.backup.entry.uploaded": "Feltöltve", "mco.backup.entry.worldType": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.generate.world": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.info.title": "Változások az utolsó biztonsági mentéshez képest", "mco.backup.narration": "Biztonsági mentés %s forrásból", "mco.backup.nobackups": "Ennek a realmnek jelenleg nincsenek biztonsági <PERSON>.", "mco.backup.restoring": "Realmed v<PERSON>za<PERSON>llí<PERSON>", "mco.backup.unknown": "ISMERETLEN", "mco.brokenworld.download": "Letöltés", "mco.brokenworld.downloaded": "Letöltve", "mco.brokenworld.message.line1": "Kérjük állítsd v<PERSON>za vagy válassz egy másik világot.", "mco.brokenworld.message.line2": "Választhatod azt is, hogy letöltöd egyjátékos világként.", "mco.brokenworld.minigame.title": "Ez a minijáték már nem támo<PERSON>ott", "mco.brokenworld.nonowner.error": "<PERSON><PERSON><PERSON> meg, hogy a realm tulajdonosa v<PERSON>zaállítsa a világot", "mco.brokenworld.nonowner.title": "<PERSON><PERSON><PERSON>", "mco.brokenworld.play": "<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.reset": "Visszaállítás", "mco.brokenworld.title": "A jelenlegi világod már nem tá<PERSON>ott", "mco.client.incompatible.msg.line1": "A kliens nem kompatibilis a Realmsszel.", "mco.client.incompatible.msg.line2": "Használd a Minecraft legújabb verzióját.", "mco.client.incompatible.msg.line3": "A Realms nem kompatibilis a snapshot verzió<PERSON>l.", "mco.client.incompatible.title": "A kliens nem kompatibilis!", "mco.client.outdated.stable.version": "A kliensed verziója (%s) már nem kompatibilis a Realmsszel.\n\nK<PERSON><PERSON><PERSON><PERSON><PERSON>, has<PERSON><PERSON><PERSON> a Minecraft legfrissebb verzióját.", "mco.client.unsupported.snapshot.version": "A kliensed verziója (%s) nem kompatibilis a Realmsszel.\n\nA Reams nem elérhető ezen a snapshot-verzión.", "mco.compatibility.downgrade": "Kompatibilitás", "mco.compatibility.downgrade.description": "Ezzel a világgal utoljára a(z) %s verzi<PERSON>ban játszottál; most a(z) %s verzi<PERSON>t használod. A világ átalakítása adatsérülést okozhat – nem garan<PERSON>áljuk, hogy betölthető és működőképes marad. Ha mégis folytatnád, készíts biztonsági mentést.\n\nA világodról készült biztonsági mentés elérhető a \"World backups\" mappában, hogy szükség esetén visszaállíthasd azt.", "mco.compatibility.incompatible.popup.title": "Inkompatibilis verzi<PERSON>", "mco.compatibility.incompatible.releaseType.popup.message": "A <PERSON>, am<PERSON><PERSON>z csatlakozni próbálsz, nem kompatibilis az általad használt verzióval.", "mco.compatibility.incompatible.series.popup.message": "Ezzel a világgal utoljára a(z) %s verzióban játszottál; most a(z) %s verziót használod.\n\nEz a két sorozat nem kompatibilis egymással. A jelenlegi verzióban való játékhoz létre kell hoznod egy új világot.", "mco.compatibility.unverifiable.message": "Ezzel a világgal utoljára egy nem megállapítható verzióban játszottál. Ha a világ verzióját megváltoztatod, automatikusan létrejön egy biztonsági mentés, amelyet a „Biztonsági mentések” menüpontból érhetsz el.", "mco.compatibility.unverifiable.title": "A kompatibilitás nem ellenőrizhető", "mco.compatibility.upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.compatibility.upgrade.description": "Ezzel a világgal utoljára a(z) %s verzióban játszottál; most a(z) %s verziót használod.\n\nA világodról biztonsági mentés készül, amely elérhető a „Biztonsági mentések” menüpontból, hogy szükség esetén visszaállíthasd azt.", "mco.compatibility.upgrade.friend.description": "Ezt a világot utoljára a %s verzióban játszották le; Ön a %s verzióban van.\n\nA világ biztonsági másolata a „Világ biztonsági másolatok” alatt lesz elmentve.\n\nA birodalom tulajdonosa szükség esetén visszaállíthatja a világot.", "mco.compatibility.upgrade.title": "<PERSON><PERSON><PERSON>, hogy frissíteni akarod ezt a világot?", "mco.configure.current.minigame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.activityfeed.disabled": "A játékosok követése ideiglenesen letiltva", "mco.configure.world.backup": "A világ biztonsági men<PERSON>", "mco.configure.world.buttons.activity": "Játékosaktivitás", "mco.configure.world.buttons.close": "Realm bezárása", "mco.configure.world.buttons.delete": "Törlés", "mco.configure.world.buttons.done": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.edit": "Beállítások", "mco.configure.world.buttons.invite": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.moreoptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.newworld": "<PERSON>j világ", "mco.configure.world.buttons.open": "Realm megnyitása", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Válassz Régiót...", "mco.configure.world.buttons.resetworld": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>llít<PERSON>", "mco.configure.world.buttons.save": "Men<PERSON>s", "mco.configure.world.buttons.settings": "Beállítások", "mco.configure.world.buttons.subscription": "Előfizetés", "mco.configure.world.buttons.switchminigame": "Minijáték váltása", "mco.configure.world.close.question.line1": "A Realmed elérhetetlen lesz.", "mco.configure.world.close.question.line2": "Biztosan folytatni szeretnéd?", "mco.configure.world.close.question.title": "Szeretnél módosításokat végrehajtani megszakítás nélül?", "mco.configure.world.closing": "A Realm lezárása...", "mco.configure.world.commandBlocks": "Parancsblokkok", "mco.configure.world.delete.button": "Realm törlése", "mco.configure.world.delete.question.line1": "A Realm véglesen törlődik", "mco.configure.world.delete.question.line2": "Biztosan fojtatni szeretnéd?", "mco.configure.world.description": "Realm leírása", "mco.configure.world.edit.slot.name": "<PERSON><PERSON><PERSON><PERSON> neve", "mco.configure.world.edit.subscreen.adventuremap": "Néhány be<PERSON>llí<PERSON> nem elérhető, mert a jelenlegi világod kalandvilág", "mco.configure.world.edit.subscreen.experience": "Néhány be<PERSON>llí<PERSON>ás nem elérhető, mert a jelenlegi világod élményvilág", "mco.configure.world.edit.subscreen.inspiration": "Néhány be<PERSON>llí<PERSON> nem elérhető, mert a jelenlegi világod ihletvilág", "mco.configure.world.forceGameMode": "Játékmód k<PERSON>zeríté<PERSON>", "mco.configure.world.invite.narration": "%s <PERSON><PERSON> fel<PERSON> van", "mco.configure.world.invite.profile.name": "Név", "mco.configure.world.invited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "Meghívva (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invites.ops.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invites.remove.tooltip": "Eltávolítás", "mco.configure.world.leave.question.line1": "<PERSON> el<PERSON>od ezt a <PERSON>et, nem <PERSON><PERSON><PERSON>, amíg nem ka<PERSON>t", "mco.configure.world.leave.question.line2": "Biztosan folytatni szeretnéd?", "mco.configure.world.loading": "Realm betöltése", "mco.configure.world.location": "<PERSON><PERSON>", "mco.configure.world.minigame": "Jelenlegi: %s", "mco.configure.world.name": "Realm neve", "mco.configure.world.opening": "A Realm megnyitása...", "mco.configure.world.players.error": "<PERSON><PERSON><PERSON> ilyen névvel", "mco.configure.world.players.inviting": "Játékos me<PERSON>ívása...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Régió <PERSON>", "mco.configure.world.region_preference.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.reset.question.line1": "A világod újragenerálódik, a jelenlegi pedig el fog veszni", "mco.configure.world.reset.question.line2": "Biztosan ezt akarod tenni?", "mco.configure.world.resourcepack.question": "<PERSON><PERSON><PERSON>, hogy ebben a <PERSON><PERSON>, e<PERSON><PERSON><PERSON> van szükséged.\n\nSzeretnéd letölteni a játékhoz?", "mco.configure.world.resourcepack.question.line1": "Ez a Realm egy egyedi forráscsomagot igényel", "mco.configure.world.resourcepack.question.line2": "<PERSON><PERSON><PERSON><PERSON>éd <PERSON>ö<PERSON>i a játékhoz?", "mco.configure.world.restore.download.question.line1": "A világ letöltődik és hozzáadódik az egyjátékos világaidhoz.", "mco.configure.world.restore.download.question.line2": "Biztosan folytatod?", "mco.configure.world.restore.question.line1": "A világod visszaáll a(z) '%s' (%s) dátumra", "mco.configure.world.restore.question.line2": "Biztosan ezt akarod tenni?", "mco.configure.world.settings.expired": "<PERSON>em v<PERSON>od meg egy lej<PERSON>rt <PERSON> beállításait", "mco.configure.world.settings.title": "Beállítások", "mco.configure.world.slot": "%s. vil<PERSON>g", "mco.configure.world.slot.empty": "Üres", "mco.configure.world.slot.switch.question.line1": "A Realm átvált egy másik világra", "mco.configure.world.slot.switch.question.line2": "Biztosan folytatni szeretnéd?", "mco.configure.world.slot.tooltip": "Váltás a világra", "mco.configure.world.slot.tooltip.active": "Csatlakozás", "mco.configure.world.slot.tooltip.minigame": "Váltás a minijátékra", "mco.configure.world.spawnAnimals": "<PERSON><PERSON><PERSON>", "mco.configure.world.spawnMonsters": "Szörnyek termése", "mco.configure.world.spawnNPCs": "Falusiak generálása", "mco.configure.world.spawnProtection": "Kezdőhely-védelem", "mco.configure.world.spawn_toggle.message": "Az opció kikapcsolásával MINDEN ilyen típusú entitás TÖRLŐDIK a világból", "mco.configure.world.spawn_toggle.message.npc": "Az opció k<PERSON>olásával MINDEN ilyen típusú entitás, péld<PERSON>ul falusi, TÖRLŐDIK a világból", "mco.configure.world.spawn_toggle.title": "Figyelem!", "mco.configure.world.status": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.day": "nap", "mco.configure.world.subscription.days": "nap", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "Előfizetés <PERSON>osszabbítása", "mco.configure.world.subscription.less_than_a_day": "Ke<PERSON><PERSON><PERSON>, mint egy nap", "mco.configure.world.subscription.month": "hónap", "mco.configure.world.subscription.months": "hónap", "mco.configure.world.subscription.recurring.daysleft": "Idő az automatikus megújításig:", "mco.configure.world.subscription.recurring.info": "A Realms-előfizetéseden tett változtatások – mint az időtartam kiterjesztése vagy a megújítás kikapcsolása – csak a következő számlázáskor lépnek érvénybe.", "mco.configure.world.subscription.remaining.days": "%1$s nap", "mco.configure.world.subscription.remaining.months": "%1$s hónap", "mco.configure.world.subscription.remaining.months.days": "%1$s hónap, %2$s nap", "mco.configure.world.subscription.start": "Kezdő dátum", "mco.configure.world.subscription.tab": "Felirat<PERSON>z<PERSON>", "mco.configure.world.subscription.timeleft": "Hátralévő idő", "mco.configure.world.subscription.title": "Előfizetési adatok", "mco.configure.world.subscription.unknown": "Ismeretlen", "mco.configure.world.switch.slot": "Világ létrehoz<PERSON>a", "mco.configure.world.switch.slot.subtitle": "Ez a világ üres; v<PERSON><PERSON><PERSON><PERSON> ki, mi<PERSON>et s<PERSON><PERSON> lé<PERSON>ho<PERSON>ni", "mco.configure.world.title": "Realm beállítása:", "mco.configure.world.uninvite.player": "Biztosan v<PERSON>od a meghívást '%s' játékostól?", "mco.configure.world.uninvite.question": "Biztosan v<PERSON>zavonod a meghívást tőle:", "mco.configure.worlds.title": "Világok", "mco.connect.authorizing": "Bejelentkezés...", "mco.connect.connecting": "Csatlakozás a Realmhoz...", "mco.connect.failed": "<PERSON><PERSON> csatlakozni a Realmhoz", "mco.connect.region": "Szerver régió: %s", "mco.connect.success": "<PERSON><PERSON><PERSON>", "mco.create.world": "Létrehozás", "mco.create.world.error": "Meg kell adnod egy nevet!", "mco.create.world.failed": "A világ létrehozása sikertelen!", "mco.create.world.reset.title": "Világ létrehozása...", "mco.create.world.skip": "Kiha<PERSON><PERSON>", "mco.create.world.subtitle": "<PERSON>, tö<PERSON> fel egy vil<PERSON><PERSON> az <PERSON>be", "mco.create.world.wait": "Realm létrehozása...", "mco.download.cancelled": "Letöltés megszakítva", "mco.download.confirmation.line1": "A letölteni kívánt világ nagyobb, mint %s", "mco.download.confirmation.line2": "<PERSON><PERSON> k<PERSON> e<PERSON>t a világot újra feltölteni a Realmsba", "mco.download.confirmation.oversized": "Az általad letölteni kívánt világ nagyobb, mint %s.\n\nEzt a világot nem tudod majd visszatölteni a realmedbe.", "mco.download.done": "Letöltés kész", "mco.download.downloading": "Letöltés", "mco.download.extracting": "Kibontás", "mco.download.failed": "Letöltés sikertelen", "mco.download.percent": "%s%%", "mco.download.preparing": "Letöltés előkészítése", "mco.download.resourcePack.fail": "A forráscsomag letöltése sikertelen!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Legutóbbi világ letöltése", "mco.error.invalid.session.message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> meg <PERSON>j<PERSON>dítani a Minecraftot", "mco.error.invalid.session.title": "Érvénytelen munkamenet-azonosító", "mco.errorMessage.6001": "<PERSON><PERSON><PERSON>", "mco.errorMessage.6002": "A Szolgáltatási feltételek nincsenek elfogadva", "mco.errorMessage.6003": "Letöltési korlát elérve", "mco.errorMessage.6004": "Feltöltési korlát elérve", "mco.errorMessage.6005": "<PERSON><PERSON><PERSON><PERSON>", "mco.errorMessage.6006": "<PERSON><PERSON><PERSON>", "mco.errorMessage.6007": "A felhasználó túl sok realm<PERSON>z <PERSON>", "mco.errorMessage.6008": "Érvénytelen Realm-név", "mco.errorMessage.6009": "Érvénytelen Realm-leírás", "mco.errorMessage.connectionFailure": "<PERSON><PERSON>; próbálkozz újra később.", "mco.errorMessage.generic": "<PERSON><PERSON> t<PERSON>: ", "mco.errorMessage.initialize.failed": "<PERSON><PERSON> inicializálni a realmet", "mco.errorMessage.noDetails": "A hiba nincs részletesen megadva", "mco.errorMessage.realmsService": "<PERSON><PERSON> tö<PERSON>ént (%s):", "mco.errorMessage.realmsService.configurationError": "Váratlanul inaktív realms hely szerkesztése", "mco.errorMessage.realmsService.connectivity": "<PERSON><PERSON> csatlakozni a Realmshez: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "<PERSON><PERSON> ellenőrizni a kompatibilis verziót, a kapott válasz: %s", "mco.errorMessage.retry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.errorMessage.serviceBusy": "A Realms jelenleg túlterhelt.\nPróbálj meg új<PERSON> a realmedhez csatlakozni pár perccel k<PERSON>.", "mco.gui.button": "<PERSON><PERSON>", "mco.gui.ok": "Ok", "mco.info": "Információ!", "mco.invited.player.narration": "%s j<PERSON><PERSON><PERSON>", "mco.invites.button.accept": "Elfogadás", "mco.invites.button.reject": "Elutasítás", "mco.invites.nopending": "Nincsenek függőben lévő felkérések!", "mco.invites.pending": "Új felkérés(ek)!", "mco.invites.title": "Függőben lévő felkérések", "mco.minigame.world.changeButton": "Másik minijáték kiválasztása", "mco.minigame.world.info.line1": "Ez ideiglenesen kicseréli a világodat egy minijátékra!", "mco.minigame.world.info.line2": "Később visszatérhetsz az eredeti világodhoz bármilyen veszteség nélkül.", "mco.minigame.world.noSelection": "Válassz ki egy mini<PERSON>tékot", "mco.minigame.world.restore": "Minijáték befejezése...", "mco.minigame.world.restore.question.line1": "A minijáték befejeződik, a Realmed pedig v<PERSON><PERSON><PERSON><PERSON> er<PERSON>.", "mco.minigame.world.restore.question.line2": "Biztosan ezt akarod tenni?", "mco.minigame.world.selected": "Kiválasztott minijáték:", "mco.minigame.world.slot.screen.title": "<PERSON><PERSON><PERSON><PERSON>...", "mco.minigame.world.startButton": "Csere", "mco.minigame.world.starting.screen.title": "Minijáték indítása...", "mco.minigame.world.stopButton": "Minijáték befejezése", "mco.minigame.world.switch.new": "Választasz egy másik minijátékot?", "mco.minigame.world.switch.title": "Minijáték váltása", "mco.minigame.world.title": "Realm minijátékká alakítása", "mco.news": "<PERSON><PERSON><PERSON> a Realmsről", "mco.notification.dismiss": "Bezárás", "mco.notification.transferSubscription.buttonText": "Átköltöztetés", "mco.notification.transferSubscription.message": "A Java Realms-előfizetések átkerülnek a Microsoft Áruházba. Vigyázz, nehogy lejárjon az előfizetésed!\nHa most átköltözteted, 30 napig ingyen használhatod a Realmst.\nAz átköltöztetést a minecraft.net oldalon, a Profil menüpontban végezheted el.", "mco.notification.visitUrl.buttonText.default": "<PERSON>", "mco.notification.visitUrl.message.default": "Látogasd meg az alábbi linket", "mco.onlinePlayers": "Elérhető játékosok", "mco.play.button.realm.closed": "<PERSON> lezá<PERSON>", "mco.question": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "<PERSON><PERSON><PERSON>", "mco.reset.world.experience": "Élmén<PERSON>k", "mco.reset.world.generate": "<PERSON>j világ", "mco.reset.world.inspiration": "<PERSON><PERSON><PERSON>", "mco.reset.world.resetting.screen.title": "<PERSON><PERSON><PERSON><PERSON>be állítása...", "mco.reset.world.seed": "Kezdő<PERSON>rték (nem kötelező)", "mco.reset.world.template": "Sablonok", "mco.reset.world.title": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>llít<PERSON>", "mco.reset.world.upload": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.warning": "Ez véglegesen törli a Realmed!", "mco.selectServer.buy": "Realm vásárlása!", "mco.selectServer.close": "Bezárás", "mco.selectServer.closed": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.closeserver": "Realm bezárása", "mco.selectServer.configure": "Realm beállí<PERSON>ása", "mco.selectServer.configureRealm": "Realm beállí<PERSON>ása", "mco.selectServer.create": "Realm létrehozása", "mco.selectServer.create.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> ki, milyen világ legyen az új realmedben", "mco.selectServer.expired": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredList": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredRenew": "Megújít<PERSON>", "mco.selectServer.expiredSubscribe": "Előfizetés", "mco.selectServer.expiredTrial": "Lejárt a próbaidőd", "mco.selectServer.expires.day": "<PERSON><PERSON> napon bel<PERSON>", "mco.selectServer.expires.days": "%s nap múlva lej<PERSON>r", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON>", "mco.selectServer.leave": "Realm elhagyása", "mco.selectServer.loading": "Realmlista betöltése", "mco.selectServer.mapOnlySupportedForVersion": "Ez a világ nem támogatott a(z) %s verzióban", "mco.selectServer.minigame": "Minijáték:", "mco.selectServer.minigameName": "Minijáték: %s", "mco.selectServer.minigameNotSupportedInVersion": "Ez a minijáték nem játszható %s alatt", "mco.selectServer.noRealms": "<PERSON><PERSON><PERSON>-ed. <PERSON><PERSON> létre egyet és játsz együtt a barátaiddal.", "mco.selectServer.note": "Megjegyzés:", "mco.selectServer.open": "Realm megnyitása", "mco.selectServer.openserver": "Realm megnyitása", "mco.selectServer.play": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.popup": "A Realmsszel biztonságosan és egyszerűen használhatsz egy online Minecraft világot akár egyszerre tíz barátoddal is. Támogat sokféle minijátékot és egyedi világokat is! Csak a realm tulajdonosának kell fizetnie.", "mco.selectServer.purchase": "Realm hozzáadása", "mco.selectServer.trial": "Próbáld ki!", "mco.selectServer.uninitialized": "Kattints ide a Realm létrehozásához!", "mco.snapshot.createSnapshotPopup.text": "Egy ingyenes snapshot-realmet készülsz létrehozni, amely a meglévő Realms-előfizetésedhez fog társulni. Ez az új snapshot-realm addig lesz elérhető, amíg az előfizetésed aktív. A fizetős Realmedet ez nem érinti.", "mco.snapshot.createSnapshotPopup.title": "Létrehozol egy snapshot-realmet?", "mco.snapshot.creating": "Snapshot-realm létrehozása…", "mco.snapshot.description": "Párosítva %s", "mco.snapshot.friendsRealm.downgrade": "A Realmhez csak a(z) %s verzióból tudsz csatlakozni", "mco.snapshot.friendsRealm.upgrade": "%s játékosnak frissítenie kell a <PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON> c<PERSON>lakozhatnál erről a verzióról", "mco.snapshot.paired": "Ez a snapshot-realm a(z) %s <PERSON><PERSON> van párosítva", "mco.snapshot.parent.tooltip": "<PERSON><PERSON><PERSON><PERSON> a Minecraft legújabb verz<PERSON>, hogy ezen a <PERSON>en j<PERSON>s", "mco.snapshot.start": "Ingyenes snapshot-realm indítása", "mco.snapshot.subscription.info": "Ez egy snapshot-realm, amely a „%s” nevű realmed előfizetéséhez van párosítva. Addig marad aktív, amíg a hozzá párosított realm.", "mco.snapshot.tooltip": "A Snapshot Realms segítségével betekintést nyerhetsz a Minecraft következő verzióiba, ahol új funkciók és egyéb változások találhatóak.\n\nA szokásos Realms szolgáltatásokat a játék kiadott verzióiban éred el.", "mco.snapshotRealmsPopup.message": "A Realms mostantól, a 23w41a verziótól kezdve a snapshot verzi<PERSON>kban is elérhető. Minden Realms-előfizetés tartalmaz egy ingyenes snapshot-realmet, amely tel<PERSON>sen független a normál Java-realmedtől!", "mco.snapshotRealmsPopup.title": "A Realms mostantól snapshotokban is elérhető", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON><PERSON> meg többet", "mco.template.button.publisher": "<PERSON><PERSON><PERSON>", "mco.template.button.select": "Kiválasztás", "mco.template.button.trailer": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.default.name": "Világsablon", "mco.template.info.tooltip": "<PERSON><PERSON><PERSON>", "mco.template.name": "Sablon", "mco.template.select.failure": "A kategória tartalma sajnos nem érhető el.\nEllenőrizd az internetkapcsolatod, vagy próbálkozz újra később.", "mco.template.select.narrate.authors": "Készítők: %s", "mco.template.select.narrate.version": "%s verzió", "mco.template.select.none": "Hoppá! <PERSON><PERSON>ű<PERSON>, ez a kategória még üres.\nNézz vissza később új tartalomért, vagy, ha tartalomkészítő vagy,\nesetleg %s.", "mco.template.select.none.linkTitle": "küldj be valamit te magad", "mco.template.title": "Világsablonok", "mco.template.title.minigame": "Minijátékok", "mco.template.trailer.tooltip": "Bemutató a világról", "mco.terms.buttons.agree": "Elfogadás", "mco.terms.buttons.disagree": "Elutasítás", "mco.terms.sentence.1": "Elfogadom a Minecraft Realms", "mco.terms.sentence.2": "szolgáltatási feltételeit", "mco.terms.title": "Realms Szolgáltatási feltételek", "mco.time.daysAgo": "%1$s napja", "mco.time.hoursAgo": "%1$s órája", "mco.time.minutesAgo": "%1$s perce", "mco.time.now": "az im<PERSON><PERSON>", "mco.time.secondsAgo": "%1$s másodperce", "mco.trial.message.line1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> egy sa<PERSON>?", "mco.trial.message.line2": "Kattints ide további információért!", "mco.upload.button.name": "Feltöltés", "mco.upload.cancelled": "Feltöltés megszakítva", "mco.upload.close.failure": "<PERSON><PERSON> be<PERSON> a realmed; pr<PERSON><PERSON><PERSON><PERSON> meg <PERSON> k<PERSON>", "mco.upload.done": "Feltöltés kész", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "A feltöltés sikertelen! (%s)", "mco.upload.failed.too_big.description": "A kiválasztott világ túl nagy. A legnagyobb megengedett méret %s.", "mco.upload.failed.too_big.title": "<PERSON><PERSON> nagy világ", "mco.upload.hardcore": "Hardcore világokat nem lehet feltölteni!", "mco.upload.percent": "%s%%", "mco.upload.preparing": "A világod adatainak előkészítése", "mco.upload.select.world.none": "Nem ta<PERSON>ható egyjátékos világ!", "mco.upload.select.world.subtitle": "Jelölj ki egy egyjátékos világot a feltöltéshez", "mco.upload.select.world.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.upload.size.failure.line1": "\"%s\" túl nagy!", "mco.upload.size.failure.line2": "A mérete %s. A megengedett legnagyobb méret %s.", "mco.upload.uploading": "'%s' feltöltése", "mco.upload.verifying": "A világod ellenőrzése", "mco.version": "Verzió: %s", "mco.warning": "Figyelem!", "mco.worldSlot.minigame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.custom_options": "<PERSON>gy<PERSON>i <PERSON>ók...", "menu.custom_options.title": "<PERSON><PERSON><PERSON><PERSON>", "menu.custom_options.tooltip": "Megjegyzés: Az egyedi opciók harmadik-fél szerveréről és/vagy tartalm<PERSON>ól származnak.\nÓvatosan bánj velük!", "menu.custom_screen_info.button_narration": "<PERSON><PERSON> egy egyedi oldal. Tov<PERSON><PERSON><PERSON> Infromá<PERSON>ó.", "menu.custom_screen_info.contents": "Ennek az oldalnak a tartalmát egy harmadik-fél határozza meg és az oldal tartalmát és a pályákat nem birtokolja, nem tartja fenn és nem ellenőrzi a Mojang Studios vagy a Microsoft.\n\nÓvatossággal használd! Mindig vigyázz amikor linkeket nyitsz meg és soha ne add meg a sze<PERSON>ly<PERSON> adataidat, beleértve a belépési adataidat.\n\nHa ez az oldal meggátol téged abban, hogy a szerveren j<PERSON>, akkor az alábbi gombbal visszatérhetsz a főmenübe.", "menu.custom_screen_info.disconnect": "Egyedi oldal elutasítva", "menu.custom_screen_info.title": "Megjegyzés az egyedi oldalakról", "menu.custom_screen_info.tooltip": "Ez egy egyedi oldal. Kattints ide további információkért.", "menu.disconnect": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "menu.feedback": "Visszajelzés...", "menu.feedback.title": "Visszajelzés", "menu.game": "Játékmenü", "menu.modded": " (<PERSON><PERSON><PERSON>)", "menu.multiplayer": "Többjáté<PERSON> mód", "menu.online": "Minecraft Realms", "menu.options": "Beállítások...", "menu.paused": "Szüneteltetve", "menu.playdemo": "Demóvilág indítása", "menu.playerReporting": "Játékos j<PERSON>ntése", "menu.preparingSpawn": "Kezdőterület előkészítése: %s%%", "menu.quick_actions": "Gyorsműveletek...", "menu.quick_actions.title": "Gyorsműveletek", "menu.quit": "Kilépés a játékból", "menu.reportBugs": "Hibajelentés", "menu.resetdemo": "Demóvilág <PERSON>", "menu.returnToGame": "Vissza a játékba", "menu.returnToMenu": "Mentés és kilépés a főmenübe", "menu.savingChunks": "Chunkok mentése", "menu.savingLevel": "<PERSON><PERSON><PERSON><PERSON>", "menu.sendFeedback": "Visszajelzés", "menu.server_links": "Szerver linkek...", "menu.server_links.title": "Szerver linkek", "menu.shareToLan": "Megosztás LAN-ra", "menu.singleplayer": "Egyjátékos mód", "menu.working": "<PERSON><PERSON> pillanat...", "merchant.deprecated": "A falusiak naponta legfeljebb kétszer jutnak új áruhoz.", "merchant.level.1": "<PERSON><PERSON><PERSON>", "merchant.level.2": "<PERSON>as", "merchant.level.3": "<PERSON><PERSON><PERSON>", "merchant.level.4": "Iparos", "merchant.level.5": "<PERSON><PERSON>", "merchant.title": "%s – %s", "merchant.trades": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Nyomd meg a(z) %1$s gombot a leszálláshoz", "multiplayer.applyingPack": "Forráscsomag al<PERSON>", "multiplayer.confirm_command.parse_errors": "Egy nem felismert vagy hibás parancsot szeretnél futtatni.\nBiztos vagy benne?\nParancs: %s", "multiplayer.confirm_command.permissions_required": "Egy olyan parancsot szeretnél futtatni amihez magasasbb jogkör szükséges.\n<PERSON><PERSON> lehet, hogy negatívan hat majd a játékodra.\nBiztos vagy benne?\nParancs: %s", "multiplayer.confirm_command.title": "Parancs futtatás megerősítése", "multiplayer.disconnect.authservers_down": "A hitelesítőszerverek nem működnek. Sajn<PERSON>l<PERSON>k, <PERSON><PERSON><PERSON><PERSON><PERSON>, próbálkozz újra később!", "multiplayer.disconnect.bad_chat_index": "Hiányzó vagy átrendezett chatüzenet érkezett a szervertől", "multiplayer.disconnect.banned": "Ki vagy tiltva a s<PERSON>verről", "multiplayer.disconnect.banned.expiration": "\nA tiltásod %s-ig van érvényben", "multiplayer.disconnect.banned.reason": "Ki vagy <PERSON>va a szerverről.\nIndoklás: %s", "multiplayer.disconnect.banned_ip.expiration": "\nA tiltásod %s-ig van érvényben", "multiplayer.disconnect.banned_ip.reason": "Az IP-c<PERSON><PERSON> k<PERSON> a szerverről.\nIndoklás: %s", "multiplayer.disconnect.chat_validation_failed": "Egy chatüzenet ellenőrzése meghiúsult", "multiplayer.disconnect.duplicate_login": "Másik helyről jelentkeztél be.", "multiplayer.disconnect.expired_public_key": "A fiókod nyilvános kulcsa lejárt. <PERSON><PERSON><PERSON><PERSON>, hogy a rendszeridőd szinkronizálva van<PERSON>e, majd pr<PERSON><PERSON><PERSON>ld meg <PERSON> a játékot.", "multiplayer.disconnect.flying": "A repülés nincs engedé<PERSON>ezve a szerveren.", "multiplayer.disconnect.generic": "Megszakadt a kapcsolat", "multiplayer.disconnect.idling": "Túl hosszú ideig voltál tétlen!", "multiplayer.disconnect.illegal_characters": "<PERSON><PERSON> megen<PERSON>ett karak<PERSON>ek a chaten", "multiplayer.disconnect.incompatible": "Nem kompatibilis a kliensed! Használd a(z) %s verziót", "multiplayer.disconnect.invalid_entity_attacked": "Érvénytelen entitást próbált<PERSON>l <PERSON>", "multiplayer.disconnect.invalid_packet": "A szerver érvénytelen adatot küldött", "multiplayer.disconnect.invalid_player_data": "Érvénytelen adat a játékostól", "multiplayer.disconnect.invalid_player_movement": "Érvénytelen j<PERSON>tékosmozgatási adatcsomag", "multiplayer.disconnect.invalid_public_key_signature": "A fiók nyilvános kulcsának aláírása érvénytelen.\nPróbáld meg újraindítani a játékot.", "multiplayer.disconnect.invalid_public_key_signature.new": "A fiók nyilvános kulcsának aláírása érvénytelen.\nPróbáld meg újraindítani a játékot.", "multiplayer.disconnect.invalid_vehicle_movement": "Érvénytelen járműmozgatási adatcsomag", "multiplayer.disconnect.ip_banned": "Az IP-c<PERSON><PERSON> ki <PERSON> erről a szerverről", "multiplayer.disconnect.kicked": "<PERSON><PERSON> operátor kirú<PERSON>t a szerverről", "multiplayer.disconnect.missing_tags": "Hiányos címkelista érkezett a szervertől. \nVedd fel a kapcsolatot a szerver üzemeltetőivel.", "multiplayer.disconnect.name_taken": "Ez a név már <PERSON>t", "multiplayer.disconnect.not_whitelisted": "<PERSON>em s<PERSON> a szerver fehérlistáján!", "multiplayer.disconnect.out_of_order_chat": "Soron kívüli chat adatcsomag érkezett. Megv<PERSON><PERSON>ozott a rendszeridőd?", "multiplayer.disconnect.outdated_client": "Nem kompatibilis a kliensed! Használd a(z) %s verziót", "multiplayer.disconnect.outdated_server": "Nem kompatibilis a kliensed! Használd a(z) %s verziót", "multiplayer.disconnect.server_full": "A szerver megtelt!", "multiplayer.disconnect.server_shutdown": "A szerver leállt", "multiplayer.disconnect.slow_login": "Bejelentkezési időtúllépés", "multiplayer.disconnect.too_many_pending_chats": "Túl sok visszaigazolatlan chatüzenet", "multiplayer.disconnect.transfers_disabled": "A szerver nem fogad áthelyezéseket", "multiplayer.disconnect.unexpected_query_response": "<PERSON>em várt egyéni adat a klienstől", "multiplayer.disconnect.unsigned_chat": "Hiányzó vagy érv<PERSON>len aláírású chat-adatcsomag érkezett.", "multiplayer.disconnect.unverified_username": "<PERSON><PERSON> a<PERSON>tani a felhasználóneved!", "multiplayer.downloadingStats": "Statisztikák betöltése...", "multiplayer.downloadingTerrain": "Domborzat betöltése...", "multiplayer.lan.server_found": "Új szerver található: %s", "multiplayer.message_not_delivered": "Az üzenetet nem sikerült elküldeni (ellenőrizd a szervernaplót): %s", "multiplayer.player.joined": "%s csatlakozott a játékhoz", "multiplayer.player.joined.renamed": "%s (k<PERSON><PERSON><PERSON><PERSON> nev<PERSON>: %s) csatlakozott a játékhoz", "multiplayer.player.left": "%s elhagyta a játékot", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Online lévő játékosok: %s", "multiplayer.requiredTexturePrompt.disconnect": "A szerver egyedi forráscsomagot igényel", "multiplayer.requiredTexturePrompt.line1": "Ez a szerver egy egyedi forráscsomag használatát igényli.", "multiplayer.requiredTexturePrompt.line2": "Ha elutasítod az egyedi forráscsomagot, lecsatlakozol a szerverről.", "multiplayer.socialInteractions.not_available": "A közösségi interakciók csak többjátékos világokban érhetőek el", "multiplayer.status.and_more": "...és még %s...", "multiplayer.status.cancelled": "Megszakítva", "multiplayer.status.cannot_connect": "<PERSON><PERSON> kapcsolódni a szerverhez", "multiplayer.status.cannot_resolve": "A hosztnév feloldása sikertelen", "multiplayer.status.finished": "Befejezve", "multiplayer.status.incompatible": "Nem kompatibilis verzió!", "multiplayer.status.motd.narration": "Napi üzenet: %s", "multiplayer.status.no_connection": "(nincs ka<PERSON>)", "multiplayer.status.old": "<PERSON><PERSON><PERSON>", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping: %s milliszekundum", "multiplayer.status.pinging": "Pingelés...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s j<PERSON><PERSON><PERSON> van online %s maximumból", "multiplayer.status.quitting": "Kilépés", "multiplayer.status.request_handled": "Állapotlekérdezés elfogadva", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Kéretlen állapotüzenet érkezett", "multiplayer.status.version.narration": "Szerver verziója: %s", "multiplayer.stopSleeping": "<PERSON><PERSON>", "multiplayer.texturePrompt.failure.line1": "<PERSON><PERSON> alkalmazni a szerver forráscsomagját", "multiplayer.texturePrompt.failure.line2": "Lehetséges, hogy az egyedi erőforrásokat igénylő funkciók nem működnek a várt módon", "multiplayer.texturePrompt.line1": "Ez a szerver egy egyedi forráscsomag használatát j<PERSON>sol<PERSON>.", "multiplayer.texturePrompt.line2": "Szeretnéd automágikusan letölteni és telepíteni?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nÜzenet a szervertől:\n%s", "multiplayer.title": "Többjáté<PERSON> mód", "multiplayer.unsecureserver.toast": "Az ezen a szerveren elküldött üzenetek módosulhatnak, és tartalmuk nem biztos, hogy tükrözi az eredetit", "multiplayer.unsecureserver.toast.title": "A chatüzenetek nem hitelesíthetőek", "multiplayerWarning.check": "Ne mutassa újra ezt az üzenetet", "multiplayerWarning.header": "Figyelem: Online játék harmadik fél szerverén", "multiplayerWarning.message": "Figyelem: Az online játék harmadik fél által biztosított szervereken zajlik, amelyeket a Mojang Studios és a Microsoft nem birtokol, üzemeltet vagy felügyel. Online játék közben olyan nem moderált üzeneteknek vagy más felhasználói tartalmaknak lehetsz kitéve, amelyek nem biztos, hogy mindenki számára megfelelőek.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Billentyű: %s", "narration.button.usage.focused": "Nyomj Entert a kiválasztáshoz", "narration.button.usage.hovered": "Kattints a bal egérgombbal a kiválasztáshoz", "narration.checkbox": "Jelölőnégyzet: %s", "narration.checkbox.usage.focused": "Nyomj Entert az átkapcsoláshoz", "narration.checkbox.usage.hovered": "Kattints a bal egérgombbal az átkapcsoláshoz", "narration.component_list.usage": "Nyomd meg a Tab billentyűt a következő elemre váltáshoz", "narration.cycle_button.usage.focused": "Nyomj Entert %s kiválasztásához", "narration.cycle_button.usage.hovered": "Kattints a bal egérgombbal %s kiválasztásához", "narration.edit_box": "Szövegmező: %s", "narration.item": "Tárgy: %s", "narration.recipe": "%s receptje", "narration.recipe.usage": "Kattints a bal egérgombbal a kijelöléshez", "narration.recipe.usage.more": "Kattints a jobb egérgombbal további receptekhez", "narration.selection.usage": "Használd a Fel és Le billentyűket a kijelölés mozgatásához", "narration.slider.usage.focused": "Használd a Bal és Jobb billentyűket az érték beállításához", "narration.slider.usage.hovered": "Húzd el a csúszkát az érték beállításához", "narration.suggestion": "%s. javaslat kiválasztva %s közül: %s", "narration.suggestion.tooltip": "%s. javaslat kiválasztva %s közül: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Nyomd meg a Tabot a következő javaslatra lépéshez", "narration.suggestion.usage.cycle.hidable": "Nyomd meg a Tabot a következő javaslatra lépéshez, vagy az Escape-et a visszalépéshez", "narration.suggestion.usage.fill.fixed": "A javaslat felhasználásához nyomd meg a Tabot", "narration.suggestion.usage.fill.hidable": "Nyomd meg a Tabot a javaslat felhasz<PERSON>lásához, vagy az Escape-et a visszalépéshez", "narration.tab_navigation.usage": "A Ctrl-Tab megnyomásával válthatsz a fülek közt", "narrator.button.accessibility": "Kisegítő beállítások", "narrator.button.difficulty_lock": "Nehézségi szint zárolása", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "<PERSON><PERSON><PERSON><PERSON>", "narrator.button.language": "Nyelv", "narrator.controls.bound": "%s t<PERSON><PERSON><PERSON><PERSON><PERSON>: %s", "narrator.controls.reset": "%s gomb vissza<PERSON>llít<PERSON>", "narrator.controls.unbound": "%s: <PERSON><PERSON><PERSON> társít<PERSON>", "narrator.joining": "Csatlakozás", "narrator.loading": "Betöltés: %s", "narrator.loading.done": "<PERSON><PERSON><PERSON>", "narrator.position.list": "Lista %s. sora kiválasztva %s közül", "narrator.position.object_list": "%s. elem kiválasztva a sorból %s közül", "narrator.position.screen": "%s. elem a képernyőn %s közül", "narrator.position.tab": "%s. fül kiválasztva %s közül", "narrator.ready_to_play": "Játékra kész", "narrator.screen.title": "Főmenü", "narrator.screen.usage": "Az egérkurzorral vagy a Tab billentyűvel válthatsz az elemek között", "narrator.select": "%s kiválasztva", "narrator.select.world": "%s kiválasztva; utoljára megnyitva: %s, %s, %s; %s verzió", "narrator.select.world_info": "%s kiválasztva; utoljára megnyitva: %s, %s", "narrator.toast.disabled": "Fe<PERSON>lvas<PERSON> k<PERSON>ol<PERSON>", "narrator.toast.enabled": "Fe<PERSON>l<PERSON><PERSON> be<PERSON>ol<PERSON>", "optimizeWorld.confirm.description": "Ez a művelet a világod minden adatát a legfrissebb formátumba konvertálja, hogy az minél hatékonyabban működjön. Ez – a világod méretétől és korától függően – nagyon sok ideig tarthat. A folyamat végeztével a világod gyorsabban működhet, de a Minecraft régebbi verzióival nem lesz kompatibilis. Biztosan ezt akarod tenni?", "optimizeWorld.confirm.proceed": "Biztonsági mentés készítése és optimalizálás", "optimizeWorld.confirm.title": "Világ <PERSON>", "optimizeWorld.info.converted": "Frissített chunkok: %s", "optimizeWorld.info.skipped": "Kihagyott chunkok: %s", "optimizeWorld.info.total": "Összes chunk: %s", "optimizeWorld.progress.counter": "%2$s / %1$s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Chunkok számlálása...", "optimizeWorld.stage.failed": "Nem si<PERSON>! :(", "optimizeWorld.stage.finished": "Véglegesítés...", "optimizeWorld.stage.finished.chunks": "Chunkok frissítésének véglegesítése...", "optimizeWorld.stage.finished.entities": "Entitások frissítésének véglegesítése...", "optimizeWorld.stage.finished.poi": "Pontokat érdeklődés frissítésének véglegesítése...", "optimizeWorld.stage.upgrading": "Az összes chunk frissítése...", "optimizeWorld.stage.upgrading.chunks": "Az összes chunk frissítése...", "optimizeWorld.stage.upgrading.entities": "Az összes entitás frissítése...", "optimizeWorld.stage.upgrading.poi": "Az összes pontot érdeklődés frissítése...", "optimizeWorld.title": "'%s' optimalizálása", "options.accessibility": "Kisegítő beállítások...", "options.accessibility.high_contrast": "Magas kontraszt", "options.accessibility.high_contrast.error.tooltip": "A Kontrasztos forráscsomag nem érhető el", "options.accessibility.high_contrast.tooltip": "Megnöveli a felhasználói felület elemeinek kontrasztját", "options.accessibility.high_contrast_block_outline": "Kontrasztos blokk-körvonalak", "options.accessibility.high_contrast_block_outline.tooltip": "Növeli a célba vett blokk körvonalának kontrasztját.", "options.accessibility.link": "Kisegítő beállítások súgója", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.menu_background_blurriness.tooltip": "A menük hátterének homályosságát szab<PERSON>za", "options.accessibility.narrator_hotkey": "<PERSON><PERSON><PERSON><PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Engedélyezi a 'Cmd+B' kombinációt a Narrátor be- és kikapcsolásához", "options.accessibility.narrator_hotkey.tooltip": "Engedélyezi a 'Ctrl+B' kombinációt a Narrátor be- és kikapcsolásához", "options.accessibility.panorama_speed": "Kezdőkép pásztázása", "options.accessibility.text_background": "Szöveg <PERSON>", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "Mindenhol", "options.accessibility.text_background_opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.title": "Kisegítő beállítások", "options.allowServerListing": "Listázás engedélyezése", "options.allowServerListing.tooltip": "A szerverek publikus állapotként megjeleníthetik az éppen online lévő játékosok listáját.\nHa ez a beállítás ki van ka<PERSON>csolva, a te neved nem fog szerepelni ezeken a listákon.", "options.ao": "Simított megvilágítás", "options.ao.max": "<PERSON><PERSON><PERSON>", "options.ao.min": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.off": "KI", "options.attack.crosshair": "Célkereszt", "options.attack.hotbar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.attackIndicator": "Támadás<PERSON>ő", "options.audioDevice": "Eszköz", "options.audioDevice.default": "A rendszer alapértelmezése", "options.autoJump": "Automatikus ugrás", "options.autoSuggestCommands": "Parancskiegészítés", "options.autosaveIndicator": "Mentést jelző ikon", "options.biomeBlendRadius": "Biomok közti átmenet", "options.biomeBlendRadius.1": "KI (Leggyorsabb)", "options.biomeBlendRadius.11": "11×11 (Extrém)", "options.biomeBlendRadius.13": "13×13 (Felvágás)", "options.biomeBlendRadius.15": "15×15 (Maximum)", "options.biomeBlendRadius.3": "3×3 (Gyors)", "options.biomeBlendRadius.5": "5×5 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.7": "7×7 (Magas)", "options.biomeBlendRadius.9": "9×9 (Nagyon magas)", "options.chat": "<PERSON><PERSON>...", "options.chat.color": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.delay": "Chatkésleltetés: %s mp", "options.chat.delay_none": "Chatkésleltetés: <PERSON><PERSON><PERSON>", "options.chat.height.focused": "Aktív ma<PERSON>ság", "options.chat.height.unfocused": "Inaktív magasság", "options.chat.line_spacing": "Sorköz", "options.chat.links": "Weblinkek", "options.chat.links.prompt": "Rákérde<PERSON><PERSON>", "options.chat.opacity": "Szövegátlátszatlanság", "options.chat.scale": "Szöveg mérete", "options.chat.title": "<PERSON>t <PERSON>...", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "Megjelenítve", "options.chat.visibility.hidden": "Elrejtve", "options.chat.visibility.system": "Csak parancsok", "options.chat.width": "Szélesség", "options.chunks": "%s chunk", "options.clouds.fancy": "S<PERSON>é<PERSON>", "options.clouds.fast": "Gyors", "options.controls": "Irányítás...", "options.credits_and_attribution": "Közreműködők és licencek...", "options.damageTiltStrength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.damageTiltStrength.tooltip": "A sérüléskor előforduló kameramozgás mértéke.", "options.darkMojangStudiosBackgroundColor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.darkMojangStudiosBackgroundColor.tooltip": "Megváltoztatja a Mojang Studios betöltőképernyő háttérszínét feketére.", "options.darknessEffectScale": "Sötétség lüktetése", "options.darknessEffectScale.tooltip": "Meghatározza a Sötétség hatás lüktetésének mért<PERSON>, amikor egy Strázsa vagy Sculksikoltó előidézi azt.", "options.difficulty": "Nehézség", "options.difficulty.easy": "<PERSON><PERSON><PERSON>", "options.difficulty.easy.info": "Keletkeznek ellenséges mobok, de kevesebbet sebeznek. Az éhségjelző kimerülhet, ekkor 5 szívig csökkenti az életerőt.", "options.difficulty.hard": "Neh<PERSON>z", "options.difficulty.hard.info": "Keletkeznek ellenséges mobok, és sokat sebeznek. Az éhségjel<PERSON><PERSON> k<PERSON>, és ekkor nullára csökkentheti az életerőt.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.normal.info": "Keletkeznek ellenséges mobok, és közepesen erősen sebeznek. Az éhségjelző kimerül<PERSON>t, ekkor egy fél szívig csökkenti az életerőt.", "options.difficulty.online": "Szerver nehézsége", "options.difficulty.peaceful": "Békés", "options.difficulty.peaceful.info": "Nem jele<PERSON>k meg ellenséges mobok, és a semlegesek közül sem mind. Az éhségjelző nem csökken, és az életerő fokozatosan feltöltődik.", "options.directionalAudio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> audi<PERSON>", "options.directionalAudio.off.tooltip": "Klasszikus sztereó hangzás", "options.directionalAudio.on.tooltip": "HRTF alapú irányított audió felhasználása a térbeli hangzás élethűbb szimulációjához. HRTF-kompatibilis hangeszközöket igényel; a legjobb hatás fejhallgatóval érhető el.", "options.discrete_mouse_scroll": "Diszk<PERSON>t gö<PERSON>", "options.entityDistanceScaling": "Entitástávolság", "options.entityShadows": "Entitások árnyéka", "options.font": "Betűkészlet…", "options.font.title": "Betűkészlet beállításai", "options.forceUnicodeFont": "Csak Unicode betűtípus", "options.fov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.fov.max": "Quake Pro", "options.fov.min": "<PERSON><PERSON><PERSON><PERSON>", "options.fovEffectScale": "Látószöghatások", "options.fovEffectScale.tooltip": "<PERSON><PERSON><PERSON>, hogy a játékbeli hatások mennyire változtassák meg a látószöget.", "options.framerate": "%s FPS", "options.framerateLimit": "<PERSON><PERSON><PERSON>", "options.framerateLimit.max": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.fullscreen": "<PERSON><PERSON><PERSON>", "options.fullscreen.current": "<PERSON><PERSON><PERSON><PERSON>", "options.fullscreen.entry": "%s×%s, %s Hz (%s bit)", "options.fullscreen.resolution": "Teljes k<PERSON> felbontása", "options.fullscreen.unavailable": "<PERSON><PERSON> beállí<PERSON>", "options.gamma": "Fényerő", "options.gamma.default": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.gamma.max": "Fényes", "options.gamma.min": "<PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Csillogás sebessége", "options.glintSpeed.tooltip": "<PERSON><PERSON><PERSON>, hogy a csillogás milyen sebességgel mozogjon a megbűvölt tárgyakon.", "options.glintStrength": "Csillogás erőssége", "options.glintStrength.tooltip": "<PERSON><PERSON><PERSON>, hogy a csillogás milyen fényesen látsszon a megbűvölt tárgyakon.", "options.graphics": "<PERSON><PERSON>", "options.graphics.fabulous": "Gyönyörű!", "options.graphics.fabulous.tooltip": "A %s grafika képernyőshadereket használ, hogy a csapadék, a felhők és a részecskék megjelenjenek az áttetsző blokkok és a víz mögött is.\nNagyban befolyásolhatja a teljesítményt hordozható készülékeken és 4K kijelzőkön.", "options.graphics.fancy": "S<PERSON>é<PERSON>", "options.graphics.fancy.tooltip": "A Szép grafika a legtöbb gépen jó egyensúlyt teremt a teljesítmény és a képminőség között.\nElőfordul, hogy a csapadék, a felhők és a részecskék nem jelennek meg az áttetsző blokkok és a víz mögött.", "options.graphics.fast": "Gyors", "options.graphics.fast.tooltip": "A Gyors grafika csökkenti a látható eső és hó mennyiségét.\nBizonyos blokkok – például a levelek – átlátszósági effektusai le vannak tiltva.", "options.graphics.warning.accept": "Folytatás támogatás nélkül", "options.graphics.warning.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.graphics.warning.message": "<PERSON><PERSON> tűnik, hogy a grafikus gyorsítód nem támogatott a %s grafikai beállítással.\n\nFigyelmen kívül hagyhatod ezt és továbbléphetsz, de nem fogunk támogatást nyújtani az eszközödhöz, ha a %s beállítást használod.", "options.graphics.warning.renderer": "Felismert renderelő: [%s]", "options.graphics.warning.title": "<PERSON>em t<PERSON>ott grafikus eszköz", "options.graphics.warning.vendor": "Felismert gyártó: [%s]", "options.graphics.warning.version": "Felismert OpenGL-verzió: [%s]", "options.guiScale": "GUI mérete", "options.guiScale.auto": "Auto", "options.hidden": "Elrejtve", "options.hideLightningFlashes": "<PERSON><PERSON> v<PERSON><PERSON><PERSON>", "options.hideLightningFlashes.tooltip": "Letiltja az égbolt villogását villámláskor. Maguk a villámok továbbra is láthatóak lesznek.", "options.hideMatchedNames": "Egyező nevek elrejtése", "options.hideMatchedNames.tooltip": "Előfordul, hogy a harmadik féltől származó szerverek nem a szokásos formátumban küldik a chatüzeneteket.\nEzzel a beállítással a rejtett játékosok üzenetei a küldő neve alapján lesznek kiszűrve.", "options.hideSplashTexts": "Üdvözlőszöveg elrejtése", "options.hideSplashTexts.tooltip": "Elrejti a sárga üdvözlőszöveget a főmenü tetejéről.", "options.inactivityFpsLimit": "Képfrissítés c<PERSON>ö<PERSON>, ha", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "30-ra kor<PERSON><PERSON><PERSON><PERSON> a k<PERSON><PERSON><PERSON>sí<PERSON>, ha a játé<PERSON> egy percél tovább inaktív. További 9 perc elteltével 10-re kor<PERSON><PERSON><PERSON><PERSON>.", "options.inactivityFpsLimit.minimized": "Csökkentett", "options.inactivityFpsLimit.minimized.tooltip": "Csak akkor csökkenti a képfrissítést, ha a játékablak le van kicsinyítve.", "options.invertMouse": "Fordított egérmozgás", "options.japaneseGlyphVariants": "Japán írásjegyváltozatok", "options.japaneseGlyphVariants.tooltip": "A távol-keleti (CJK) írásjegyek japán variánsainak használata az alapértelmezett betűkészletben", "options.key.hold": "Nyomva tartás", "options.key.toggle": "Átkapcsolás", "options.language": "Nyelv...", "options.language.title": "Nyelv", "options.languageAccuracyWarning": "(A fordításban előfordulhatnak hibák)", "options.languageWarning": "A fordításban előfordulhatnak hibák.", "options.mainHand": "<PERSON><PERSON><PERSON><PERSON>", "options.mainHand.left": "<PERSON>l", "options.mainHand.right": "<PERSON><PERSON>", "options.mipmapLevels": "MIP-leképezés mértéke", "options.modelPart.cape": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.hat": "<PERSON><PERSON>", "options.modelPart.jacket": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "Bal nadr<PERSON>gszár", "options.modelPart.left_sleeve": "<PERSON><PERSON> ingujj", "options.modelPart.right_pants_leg": "<PERSON><PERSON>", "options.modelPart.right_sleeve": "<PERSON><PERSON>j", "options.mouseWheelSensitivity": "Görgetés érzékenysége", "options.mouse_settings": "Egérbeállítások...", "options.mouse_settings.title": "Egérbeáll<PERSON>", "options.multiplayer.title": "Többjátékos beállítások...", "options.multiplier": "%s×", "options.music_frequency": "Zene gyakorisága", "options.music_frequency.constant": "<PERSON><PERSON><PERSON><PERSON>", "options.music_frequency.default": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Gyakori", "options.music_frequency.tooltip": "<PERSON><PERSON><PERSON>, mi<PERSON><PERSON> g<PERSON>kran induljon el háttérzene, amíg egy világban tartózkodsz.", "options.narrator": "Fe<PERSON>lvas<PERSON>", "options.narrator.all": "Minden", "options.narrator.chat": "Cha<PERSON>", "options.narrator.notavailable": "<PERSON><PERSON>", "options.narrator.off": "KI", "options.narrator.system": "Rendszer", "options.notifications.display_time": "Értesítés időtartama", "options.notifications.display_time.tooltip": "<PERSON><PERSON><PERSON>, hogy a felbukkanó értesítések mennyi ideig maradnak láthatóak a képernyőn.", "options.off": "KI", "options.off.composed": "%s: KI", "options.on": "BE", "options.on.composed": "%s: BE", "options.online": "Online…", "options.online.title": "Online beállítások", "options.onlyShowSecureChat": "Csak biztonságos chat", "options.onlyShowSecureChat.tooltip": "A játékosoktól származó üzenetek esetében csak azok jelennek majd meg, amelyek bizonyíthatóan a feladótól érkeztek, és nem történt rajtuk módosítás.", "options.operatorItemsTab": "Operá<PERSON><PERSON>l", "options.particles": "Részecskék", "options.particles.all": "Minden", "options.particles.decreased": "Csökkentett", "options.particles.minimal": "<PERSON><PERSON><PERSON><PERSON>", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Chunképítő", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "A chunkon belül bizonyos műveletek a chunk azonnali újra-összeállítását eredményezik. Ilyen művelet a blokkok elhelyezése és széttörése is.", "options.prioritizeChunkUpdates.nearby": "<PERSON><PERSON><PERSON> blo<PERSON>", "options.prioritizeChunkUpdates.nearby.tooltip": "A közeli chunkok összeállítása mindig azonnal megtörténik. Ez befolyásolhatja a játék teljesítményét a blokkok elhelyezésekor vagy széttörésekor.", "options.prioritizeChunkUpdates.none": "Többszálú", "options.prioritizeChunkUpdates.none.tooltip": "A közeli chunkok összeállítása párhuzamosan, több szálon történik. Emiatt a blokkok széttörésekor rövid ideig látható lyukak villanhatnak fel.", "options.rawMouseInput": "<PERSON>yers bemenet", "options.realmsNotifications": "Realms-h<PERSON><PERSON> -meghívások", "options.realmsNotifications.tooltip": "Lekérdezi a Realms-híreket és -meghívásokat a főképernyőn, és megjeleníti a megfelelő ikont a Realms gombon.", "options.reducedDebugInfo": "Csökkentett debug info", "options.renderClouds": "Felhők", "options.renderCloudsDistance": "Felhőtávolság", "options.renderDistance": "Látótávolság", "options.resourcepack": "Forráscsomagok...", "options.rotateWithMinecart": "Fordulás c<PERSON>llével <PERSON>", "options.rotateWithMinecart.tooltip": "<PERSON><PERSON><PERSON>, hogy a csillében ülő játékosok nézőpontja együtt forduljon-e a csillével, ha az kanyarodik. Csak a 'Továbbfejlesztett csillék' beállítást használó világokban érhető el.", "options.screenEffectScale": "Torzításeffektek", "options.screenEffectScale.tooltip": "A képernyőhullámzás mértékét szabályozza az Émelygés és a Nether-portál hatására.\nAlacsony értékeknél a hullámzást egy zöld képernyőszűrő váltja fel.", "options.sensitivity": "Érzékenység", "options.sensitivity.max": "HIPERGYORS!!!", "options.sensitivity.min": "*ásít*", "options.showNowPlayingToast": "Zenecím értesítésben", "options.showNowPlayingToast.tooltip": "Megjelenít egy felugró értesítést az éppen lejátszott zene címével. Ugyanez az értesítés folyamatosan látszik a játékmenüben szüneteltetéskor, amíg a zene hallható.", "options.showSubtitles": "Feliratok megjelenítése", "options.simulationDistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.skinCustomisation": "Kin<PERSON><PERSON>t test<PERSON>za<PERSON>...", "options.skinCustomisation.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.sounds": "Zene és hangok...", "options.sounds.title": "Zene- és hangbeállítások", "options.telemetry": "Telemetriai adatok...", "options.telemetry.button": "Adatgyűjtés", "options.telemetry.button.tooltip": "\"%s\" csak a szükséges adatokat tartalmazza.\n\"%s\" opcionális és szükséges adatokat is tartalmaz.", "options.telemetry.disabled": "A telemetria ki van kapcsolva.", "options.telemetry.state.all": "Minden", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON><PERSON>", "options.telemetry.state.none": "<PERSON><PERSON><PERSON>", "options.title": "Beállítások", "options.touchscreen": "Érintőképernyős mód", "options.video": "Videobeállítások...", "options.videoTitle": "Videobeállítások", "options.viewBobbing": "<PERSON><PERSON><PERSON>", "options.visible": "Megjelenítve", "options.vsync": "VSync", "outOfMemory.message": "A Minecraft kifogyott a szabad memóriából.\n\nEzt okozhatta egy programhiba vagy a Java virtuális gép számára lefoglalt túl kevés memória.\n\nHogy a világ adatainak sérülését elkerüljük, a jelenlegi játék bezáródott. Megpróbáltunk elég memóriát felszabadítani, hogy elérhesd a főmenüt és visszatérhess a játékhoz, de nem biztos, hogy ez sikerült.\n\nHa újra látod ezt az üzenetet, k<PERSON><PERSON><PERSON><PERSON><PERSON>, indítsd újra a játékot.", "outOfMemory.title": "Elfogyott a memória!", "pack.available.title": "Elérhető", "pack.copyFailure": "A csomagok másolása sikertelen", "pack.dropConfirm": "Valóban hozzáadja ezeket a csomagokat a Minecrafthoz?", "pack.dropInfo": "Csomagok hozzáadásához húzz fájlokat ebbe az ablakba", "pack.dropRejected.message": "A következő bejegyzések nem voltak érvényes csomagok, és nem másolták őket:\n %s", "pack.dropRejected.title": "Csomagmentes belépés", "pack.folderInfo": "(Ide tedd a csomagfájlokat)", "pack.incompatible": "<PERSON><PERSON>", "pack.incompatible.confirm.new": "Ez a csomag a Minecraft egy újabb verziój<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, hogy nem működik megfelelően.", "pack.incompatible.confirm.old": "Ez a csomag a Minecraft egy régebbi verziój<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, hogy nem működik megfelelően.", "pack.incompatible.confirm.title": "<PERSON><PERSON><PERSON>an be akarja tölteni ezt a csomagot?", "pack.incompatible.new": "(Újabb Minecraft verzióra készült)", "pack.incompatible.old": "(Régebb<PERSON>craft verzióra készült)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Csomagmappa megnyitása", "pack.selected.title": "Kiválasztva", "pack.source.builtin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.feature": "<PERSON><PERSON><PERSON>", "pack.source.local": "he<PERSON>i", "pack.source.server": "szerver", "pack.source.world": "vil<PERSON><PERSON>", "painting.dimensions": "%s×%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON> főellens<PERSON>g", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Tűz", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Víz", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Véletlenszerű festmény", "parsing.bool.expected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parsing.bool.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; \"true\" vagy \"false\" helyett '%s' szerepel", "parsing.double.expected": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"double\" tö<PERSON>zá<PERSON>", "parsing.double.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"double\" törtszám: '%s'", "parsing.expected": "Hiányzó '%s'", "parsing.float.expected": "Hián<PERSON><PERSON>ó lebegőpontos törtszám", "parsing.float.invalid": "Érvénytelen lebegőpontos törtszám: '%s'", "parsing.int.expected": "Hi<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "parsing.int.invalid": "Érvénytelen egész szá<PERSON>: '%s'", "parsing.long.expected": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"long\" e<PERSON><PERSON> s<PERSON>", "parsing.long.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"long\" egész: '%s'", "parsing.quote.escape": "Érvénytelen feloldószekvencia ('\\%s') idézőjeles karakterláncban", "parsing.quote.expected.end": "Lezáratlan idéző<PERSON><PERSON>", "parsing.quote.expected.start": "Hiányzó idézőjel egy karakterlánc elejéről", "particle.invalidOptions": "Részecskebeállítások értelmezése sikertelen: %s", "particle.notFound": "Ismeretlen részecske: \"%s\"", "permissions.requires.entity": "Ezt a parancsot egy entitásnak kell futtatnia", "permissions.requires.player": "Ezt a parancsot egy játékosnak kell futtatnia", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Használatkor:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Ismeretlen feltétel: %s", "quickplay.error.invalid_identifier": "<PERSON><PERSON> világ a megadott azonosítóval", "quickplay.error.realm_connect": "<PERSON><PERSON> csatlakozni a Realmhez", "quickplay.error.realm_permission": "<PERSON><PERSON><PERSON> j<PERSON>ágod a Realmhez való csatlakozáshoz", "quickplay.error.title": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> k<PERSON>deni", "realms.configuration.region.australia_east": "Új-Dél-Wales, Ausztrália", "realms.configuration.region.australia_southeast": "Victoria, Ausztrália", "realms.configuration.region.brazil_south": "Brazília", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, <PERSON><PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, <PERSON><PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.east_us_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.france_central": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.japan_east": "Kelet-Japán", "realms.configuration.region.japan_west": "Nyugat-Japán", "realms.configuration.region.korea_central": "Dél-Korea", "realms.configuration.region.north_central_us": "Illinois, <PERSON><PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.north_europe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.south_central_us": "Texas, <PERSON><PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.southeast_asia": "<PERSON><PERSON>ap<PERSON><PERSON>", "realms.configuration.region.sweden_central": "Své<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.uae_north": "Egyesült Arab Emírségek", "realms.configuration.region.uk_south": "Dél-Anglia", "realms.configuration.region.west_central_us": "Utah, <PERSON><PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.west_europe": "Hollandia", "realms.configuration.region.west_us": "Kalifornia, Egyesült <PERSON>", "realms.configuration.region.west_us_2": "Washington, <PERSON><PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region_preference.automatic_owner": "Automatikus (Realm tulajdonos ping)", "realms.configuration.region_preference.automatic_player": "Automatikus (<PERSON>ső akli belép a világba)", "realms.missing.snapshot.error.text": "A Realms jelenleg nem támogatott a snapshotokban", "recipe.notFound": "Ismeretlen recept: %s", "recipe.toast.description": "Nézd meg a receptkönyved", "recipe.toast.title": "Új receptek feloldva!", "record.nowPlaying": "Most játszott: %s", "recover_world.bug_tracker": "Hibajelentés", "recover_world.button": "Helyreállítás megkísérlése", "recover_world.done.failed": "<PERSON><PERSON>llítani az előző állapotot.", "recover_world.done.success": "A helyreállítás sikeres volt!", "recover_world.done.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recover_world.issue.missing_file": "Hiányzó fájl", "recover_world.issue.none": "<PERSON><PERSON><PERSON>", "recover_world.message": "Az alábbi hibák léptek fel a(z) „%s” világmappa olvasásakor.\nLehetséges, hogy a világ helyreállítható egy korábbi <PERSON>llapotából, vagy j<PERSON>ted ezt a problémát a hibajelentő oldalon.", "recover_world.no_fallback": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> a világ he<PERSON>ll<PERSON>", "recover_world.restore": "Visszaállítás megkísérlése", "recover_world.restoring": "Kísérlet a világ v<PERSON>zaállítására…", "recover_world.state_entry": "%s-kori <PERSON>llapot: ", "recover_world.state_entry.unknown": "ismeretlen", "recover_world.title": "A világ betöltése sikertelen", "recover_world.warning": "A világ összegzésének betöltése sikertelen", "resourcePack.broken_assets": "A JÁRULÉKOS FORRÁSFÁJLOK SÉRÜLTEK", "resourcePack.high_contrast.name": "Magas kontraszt", "resourcePack.load_fail": "A forrás újratöltése sikertelen", "resourcePack.programmer_art.name": "Programozói textúrák", "resourcePack.runtime_failure": "Hiba történt a forráscsomagban", "resourcePack.server.name": "Világhoz kötött források", "resourcePack.title": "Forráscsomagok kiválasztása", "resourcePack.vanilla.description": "A Minecraft alapértelmezett kinézete és működése", "resourcePack.vanilla.name": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "resourcepack.downloading": "<PERSON><PERSON><PERSON><PERSON><PERSON>g <PERSON>e", "resourcepack.progress": "Fájl letöltése (%s MB)...", "resourcepack.requesting": "Fájlkérés küldése...", "screenshot.failure": "Képernyőkép mentése sikertelen: %s", "screenshot.success": "Képernyőkép elmentve %s fájlnéven", "selectServer.add": "Szerver felvétele", "selectServer.defaultName": "Minecraft-szerver", "selectServer.delete": "Törlés", "selectServer.deleteButton": "Törlés", "selectServer.deleteQuestion": "<PERSON><PERSON><PERSON>, hogy eltávolítod ezt a szervert?", "selectServer.deleteWarning": "'%s' örök<PERSON> el fog veszni (ami hosszú idő)", "selectServer.direct": "Csatlakozás címhez", "selectServer.edit": "Szerkesztés", "selectServer.hiddenAddress": "(<PERSON><PERSON><PERSON><PERSON>)", "selectServer.refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectServer.select": "Csatlakozás", "selectWorld.access_failure": "A világhoz nem si<PERSON>ült hozzáférni", "selectWorld.allowCommands": "Csalások engedélyezése", "selectWorld.allowCommands.info": "<PERSON><PERSON><PERSON> para<PERSON>, mint /gamemode, /experience", "selectWorld.allowCommands.new": "Parancsok engedélyezése", "selectWorld.backupEraseCache": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.backupJoinConfirmButton": "Mentés és betöltés", "selectWorld.backupJoinSkipButton": "Vállalom a kockázatot!", "selectWorld.backupQuestion.customized": "A testreszabott világok támogatása megszűnt", "selectWorld.backupQuestion.downgrade": "A világok korábbi verzióra konvertálása nem támogatott", "selectWorld.backupQuestion.experimental": "A kísérleti beállításokat használó világok nem támogatottak", "selectWorld.backupQuestion.snapshot": "<PERSON><PERSON><PERSON>, hogy be akarod tölteni ezt a világot?", "selectWorld.backupWarning.customized": "A testreszabott világokat sajnos nem támogatjuk ebben a Minecraft-verzióban. A világ meglévő része ugyan betölthető, és minden változatlan marad, de az újonnan generált terep nem lesz többé testreszabott. Sajnáljuk az okozott kellemetlenséget!", "selectWorld.backupWarning.downgrade": "Ezzel a világgal utoljára a(z) %s verzióban játszottál; most a(z) %s verziót használod. A világ átalakítása adatsérülést okozhat – nem garan<PERSON>ljuk, hogy betölthető és működőképes marad. Ha mégis folytatnád, készíts biztonsági mentést!", "selectWorld.backupWarning.experimental": "Ez a világ kísérleti be<PERSON>llí<PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON>, hogy a jövőben nem fognak működni. <PERSON>em tud<PERSON>, hogy játszani tudsz vele. Vigyázz, mélyvíz!", "selectWorld.backupWarning.snapshot": "Ezzel a világgal utoljára a(z) %s verzióban játszottál; most a(z) %s verziót használod. Az esetleges adatvesztés megelőzése érdekében készíts biztonsági mentést!", "selectWorld.bonusItems": "Bónuszláda", "selectWorld.cheats": "Csalások", "selectWorld.commands": "Parancsok", "selectWorld.conversion": "Konvertálás szükséges!", "selectWorld.conversion.tooltip": "Ezt a világot csak egy régebbi ve<PERSON> (pl. 1.6.4) megnyitva lehet biztonságosan konvertálni", "selectWorld.create": "Új világ létrehozása", "selectWorld.customizeType": "Testreszabás", "selectWorld.dataPacks": "Adatcsomagok", "selectWorld.data_read": "Vil<PERSON>g adata<PERSON> beolvasása...", "selectWorld.delete": "Törlés", "selectWorld.deleteButton": "Törlés", "selectWorld.deleteQuestion": "<PERSON><PERSON><PERSON>, hogy törölni akarod ezt a világot?", "selectWorld.deleteWarning": "'%s' <PERSON>r<PERSON><PERSON><PERSON> el fog veszni (ami hosszú idő)!", "selectWorld.delete_failure": "A világ törlése sikertelen", "selectWorld.edit": "Szerkesztés", "selectWorld.edit.backup": "Biztons<PERSON>gi <PERSON>", "selectWorld.edit.backupCreated": "Biztonsági mentés kész: %s", "selectWorld.edit.backupFailed": "Sikertelen biztonsági mentés", "selectWorld.edit.backupFolder": "Biztonsági mentések mappája...", "selectWorld.edit.backupSize": "méret: %s MB", "selectWorld.edit.export_worldgen_settings": "Világgenerálási beállítások exportálása", "selectWorld.edit.export_worldgen_settings.failure": "<PERSON>z <PERSON><PERSON><PERSON>", "selectWorld.edit.export_worldgen_settings.success": "Export<PERSON><PERSON><PERSON>", "selectWorld.edit.openFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.optimize": "Világ <PERSON>", "selectWorld.edit.resetIcon": "<PERSON><PERSON> v<PERSON>llí<PERSON>", "selectWorld.edit.save": "Men<PERSON>s", "selectWorld.edit.title": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>keszté<PERSON>", "selectWorld.enterName": "<PERSON><PERSON><PERSON><PERSON> neve", "selectWorld.enterSeed": "Kezdőérték a világgenerátornak", "selectWorld.experimental": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.experimental.details": "Részletek", "selectWorld.experimental.details.entry": "Szükséges kísérleti funkciók: %s", "selectWorld.experimental.details.title": "Szükséges kísérleti funkciók", "selectWorld.experimental.message": "Óvatosan!\nEz a konfiguráció olyan funkció<PERSON>, amelyek még fejlesztés alatt <PERSON>. Előfordulhat, hogy a világod összeomlik, s<PERSON><PERSON><PERSON><PERSON> vagy meg sem nyílik egy későbbi verzióban.", "selectWorld.experimental.title": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>rleti funkciók", "selectWorld.experiments": "Kísérletek", "selectWorld.experiments.info": "A Kísérletek lehetséges új játékelemek. Légy óvatos: <PERSON><PERSON><PERSON>ulhat, hogy valami még nem működik jól. A Kísérleteket a velük létrehozott világban nem lehet kikapcsolni.", "selectWorld.futureworld.error.text": "Valami probléma adódott egy újabb verzióból származó világ betöltése közben. Ez a művelet eleve kockázatos volt; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy nem si<PERSON>ült.", "selectWorld.futureworld.error.title": "Hiba történt!", "selectWorld.gameMode": "<PERSON><PERSON><PERSON>kmó<PERSON>", "selectWorld.gameMode.adventure": "Kaland", "selectWorld.gameMode.adventure.info": "Ugyanaz, mint a t<PERSON><PERSON><PERSON><PERSON> mód, de nem lehet blokkokat elhelyezni vagy szétütni.", "selectWorld.gameMode.adventure.line1": "<PERSON><PERSON><PERSON><PERSON>, mint a t<PERSON><PERSON><PERSON><PERSON> mód, de nem lehet blokkokat", "selectWorld.gameMode.adventure.line2": "elhelyezni vagy szétütni", "selectWorld.gameMode.creative": "<PERSON><PERSON><PERSON>í<PERSON>", "selectWorld.gameMode.creative.info": "Korlátok nélkül építkezhetsz és felfedezhetsz. Tudsz repülni, kifogyhatatlan az anyagkészleted, és semmilyen mob sem tud neked ártani.", "selectWorld.gameMode.creative.line1": "<PERSON><PERSON><PERSON><PERSON><PERSON>, szabad repülés és", "selectWorld.gameMode.creative.line2": "blokkok azonnali megsemmisítése", "selectWorld.gameMode.hardcore": "Hardcore", "selectWorld.gameMode.hardcore.info": "<PERSON><PERSON><PERSON><PERSON><PERSON> mód <PERSON><PERSON> „Nehéz” szinten. Ha meghalsz, nem tudsz újraéledni.", "selectWorld.gameMode.hardcore.line1": "Ugy<PERSON>z, mint a túlé<PERSON><PERSON> mód, de a legnehezebb", "selectWorld.gameMode.hardcore.line2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, egy<PERSON><PERSON>l", "selectWorld.gameMode.spectator": "S<PERSON>mlélő", "selectWorld.gameMode.spectator.info": "<PERSON>ent a szemnek, semmit a kéznek.", "selectWorld.gameMode.spectator.line1": "Mindent a szem<PERSON>k, semmit a k<PERSON>znek!", "selectWorld.gameMode.survival": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.survival.info": "Fedezz fel egy t<PERSON> v<PERSON>, ahol építhetsz, gyűj<PERSON><PERSON><PERSON><PERSON>z, barkácsolhatsz és szörnyekkel harc<PERSON>.", "selectWorld.gameMode.survival.line1": "Erőforrások felkutatása, barkácsolás,", "selectWorld.gameMode.survival.line2": "tapasztalati szintek, életerő és éhség", "selectWorld.gameRules": "Játéks<PERSON>bályok", "selectWorld.import_worldgen_settings": "Beállítások importálása", "selectWorld.import_worldgen_settings.failure": "Hiba a beállítások importálásakor", "selectWorld.import_worldgen_settings.select_file": "Beállításfájl kiválasztása (.json)", "selectWorld.incompatible.description": "Ez a világ nem nyitható meg ebben a verzióban.\nUtoljára a(z) %s verzióban játszottál vele.", "selectWorld.incompatible.info": "<PERSON>em kompatibilis verzió: %s", "selectWorld.incompatible.title": "<PERSON><PERSON> kompati<PERSON> verzi<PERSON>", "selectWorld.incompatible.tooltip": "Ez a világ nem nyitható meg, mert egy nem kompatibilis verzióban készült.", "selectWorld.incompatible_series": "<PERSON>em kompatibilis verzióval lett létrehozva", "selectWorld.load_folder_access": "A mentett világokat tartalmazó mappa nem olvasható!", "selectWorld.loading_list": "Világlista betöltése", "selectWorld.locked": "<PERSON><PERSON>, j<PERSON><PERSON><PERSON> futó <PERSON>-p<PERSON>ld<PERSON><PERSON>", "selectWorld.mapFeatures": "Struktúrák generálása", "selectWorld.mapFeatures.info": "Falvak, hajóroncsok stb.", "selectWorld.mapType": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.mapType.normal": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.moreWorldOptions": "További világbeállítások...", "selectWorld.newWorld": "<PERSON>j világ", "selectWorld.recreate": "Újraalkotás", "selectWorld.recreate.customized.text": "A testreszabott világokat sajnos nem támogatjuk ebben a Minecraft-verzióban. Megpróbálhatod újraalkotni ugyanazzal a kezdőértékkel és beállításokkal, de az egyéni domborzat el fog veszni. Sajnáljuk az okozott kellemetlenséget!", "selectWorld.recreate.customized.title": "A testreszabott világok támogatása megszűnt", "selectWorld.recreate.error.text": "Valami probléma akadt egy v<PERSON>ág <PERSON> közben.", "selectWorld.recreate.error.title": "Hiba történt!", "selectWorld.resource_load": "Erőforrások előkészítése…", "selectWorld.resultFolder": "<PERSON><PERSON><PERSON>:", "selectWorld.search": "világok keresése", "selectWorld.seedInfo": "Véletlenszerű kezdőértékhez hagyd üresen", "selectWorld.select": "Játék a kiválasztott világgal", "selectWorld.targetFolder": "Mentési <PERSON>: %s", "selectWorld.title": "Világ kiválasztása", "selectWorld.tooltip.fromNewerVersion1": "A világ egy újabb verzióban volt mentve,", "selectWorld.tooltip.fromNewerVersion2": "a világ betöltése problémákat okozhat!", "selectWorld.tooltip.snapshot1": "Ne felejts el biztonsági mentést készíteni a világodról,", "selectWorld.tooltip.snapshot2": "<PERSON><PERSON><PERSON><PERSON> betöltenéd ebben a snapshotban.", "selectWorld.unable_to_load": "A világ betöltése sikertelen", "selectWorld.version": "Verzió:", "selectWorld.versionJoinButton": "Betöltés mégis", "selectWorld.versionQuestion": "<PERSON><PERSON><PERSON>, hogy be akarod tölteni ezt a világot?", "selectWorld.versionUnknown": "ismeretlen", "selectWorld.versionWarning": "Ezzel a világgal utoljára az %s verzióban játszottál; ebben a verzióban való betöltése adatvesztéshez vezethet!", "selectWorld.warning.deprecated.question": "Néhány itt használt funkció elavult, <PERSON><PERSON> lehe<PERSON>, hogy a jövőben nem fog működni. Létrehozod mégis a világot?", "selectWorld.warning.deprecated.title": "Figyelem! Ezek a beállítások elavult funkciókat használnak", "selectWorld.warning.experimental.question": "Ezek a beállítások kísérleti jellegűek, <PERSON><PERSON> le<PERSON>, hogy a jövőben nem fognak működni. Létrehozod mégis a világot?", "selectWorld.warning.experimental.title": "Figyelem! Ezek a beállítások kísérleti funkciókat használnak", "selectWorld.warning.lowDiskSpace.description": "Az eszközödön kevés a szabad tárhely.\nHa játék közben elfogy a tárhely, az a világod sérüléséhez vezethet.", "selectWorld.warning.lowDiskSpace.title": "Figyelem! Kevés szabad tárhely!", "selectWorld.world": "<PERSON><PERSON><PERSON><PERSON>", "sign.edit": "Tábla szövegének szerkesztése", "sleep.not_possible": "Ezt az éjszakát semennyi alvással sem lehet á<PERSON>", "sleep.players_sleeping": "%s/%s j<PERSON><PERSON><PERSON>sz<PERSON>", "sleep.skipping_night": "<PERSON><PERSON> <PERSON><PERSON>", "slot.only_single_allowed": "Csak egy rekeszből álló értékek adhatók meg eh<PERSON>: '%s'", "slot.unknown": "Ismeretlen rekesz: '%s'", "snbt.parser.empty_key": "A kulcs nem lehet üres", "snbt.parser.expected_binary_numeral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "snbt.parser.expected_decimal_numeral": "Hiányzó decimá<PERSON> s<PERSON>", "snbt.parser.expected_float_type": "Hián<PERSON><PERSON>ó lebegőpontos szám", "snbt.parser.expected_hex_escape": "Hiányzó %s karakteres literál", "snbt.parser.expected_hex_numeral": "Hiányzó hexadecimális szám", "snbt.parser.expected_integer_type": "Hi<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "snbt.parser.expected_non_negative_number": "Hiányzó nemnegatív szám", "snbt.parser.expected_number_or_boolean": "Hiányzó szá<PERSON> vagy <PERSON>-érték", "snbt.parser.expected_string_uuid": "Hiányzó vagy érvénytelen UUID-t tartalmazó ka<PERSON>", "snbt.parser.expected_unquoted_string": "<PERSON><PERSON>, id<PERSON>z<PERSON>jel nélküli karakterláncra szmítottunk", "snbt.parser.infinity_not_allowed": "A nem véges számok nem engedélyezettek", "snbt.parser.invalid_array_element_type": "Érvémytelen tömbelem típus", "snbt.parser.invalid_character_name": "Érvénytelen Unicode-karakterérték", "snbt.parser.invalid_codepoint": "Érvénytelen Unicode-karakterkód: %s", "snbt.parser.invalid_string_contents": "Érvénytelen karak<PERSON>lánc tartalom", "snbt.parser.invalid_unquoted_start": "Idézőjelek nélküli karakterláncok nem kezdődhetnek 0-9 k<PERSON><PERSON><PERSON><PERSON>, +-szal vagy --szal", "snbt.parser.leading_zero_not_allowed": "Tizedes számok nem kezdődhetnek 0-val", "snbt.parser.no_such_operation": "Ismeretlen művelet: %s", "snbt.parser.number_parse_failure": "<PERSON><PERSON> értelmezni a számot: %s", "snbt.parser.undescore_not_allowed": "Alulvonott karakterek nem megengedettek egy szám el<PERSON>én vagy végén", "soundCategory.ambient": "Környezet", "soundCategory.block": "Blokkok", "soundCategory.hostile": "Ellens<PERSON><PERSON> lénye<PERSON>", "soundCategory.master": "Fő hangerő", "soundCategory.music": "<PERSON><PERSON>", "soundCategory.neutral": "Barátságos lén<PERSON>", "soundCategory.player": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soundCategory.record": "Zenegép/Hangdoboz", "soundCategory.ui": "Felhasználói felület", "soundCategory.voice": "Beszéd/Hang", "soundCategory.weather": "Id<PERSON><PERSON><PERSON><PERSON><PERSON>", "spectatorMenu.close": "<PERSON><PERSON>", "spectatorMenu.next_page": "Következő oldal", "spectatorMenu.previous_page": "Előző oldal", "spectatorMenu.root.prompt": "Nyomj le egy bill<PERSON>űt egy parancs ki<PERSON>ö<PERSON>séhez, majd új<PERSON> a megerősítéshez.", "spectatorMenu.team_teleport": "Teleportálás egy <PERSON>", "spectatorMenu.team_teleport.prompt": "<PERSON><PERSON><PERSON><PERSON> egy <PERSON>, <PERSON><PERSON> teleportá<PERSON>z", "spectatorMenu.teleport": "Teleportálás játékoshoz", "spectatorMenu.teleport.prompt": "Válassz egy j<PERSON>, akihez teleportálsz", "stat.generalButton": "<PERSON><PERSON>lán<PERSON>", "stat.itemsButton": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "Tenyés<PERSON><PERSON><PERSON>", "stat.minecraft.aviate_one_cm": "Távolság <PERSON>", "stat.minecraft.bell_ring": "Megkondított ha<PERSON>", "stat.minecraft.boat_one_cm": "Távolság csónakkal", "stat.minecraft.clean_armor": "<PERSON><PERSON>tt bőrvértek", "stat.minecraft.clean_banner": "Kimosott zászlók", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON> shulkerdobozok", "stat.minecraft.climb_one_cm": "Távolság m<PERSON>va", "stat.minecraft.crouch_one_cm": "Távolság lopakodva", "stat.minecraft.damage_absorbed": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON><PERSON><PERSON> f<PERSON> se<PERSON>", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON> se<PERSON>z<PERSON>", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON><PERSON> (elnyelt)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON> (ellenállt)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.damage_taken": "Elszenvedett <PERSON>", "stat.minecraft.deaths": "Halálok száma", "stat.minecraft.drop": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.eat_cake_slice": "Megevett tortaszeletek", "stat.minecraft.enchant_item": "Megbűvölt tárgyak", "stat.minecraft.fall_one_cm": "<PERSON>á<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.fill_cauldron": "Megtöltött üstök", "stat.minecraft.fish_caught": "<PERSON><PERSON><PERSON><PERSON> halak", "stat.minecraft.fly_one_cm": "Távolság repülve", "stat.minecraft.happy_ghast_one_cm": "Távolság a Vidám Ghast-tól", "stat.minecraft.horse_one_cm": "Távolság ló<PERSON>", "stat.minecraft.inspect_dispenser": "Megnyitott adagolók", "stat.minecraft.inspect_dropper": "Megnyitott kidobók", "stat.minecraft.inspect_hopper": "Megnyitott tö<PERSON>csérek", "stat.minecraft.interact_with_anvil": "Megnyitott üllők", "stat.minecraft.interact_with_beacon": "Megnyitott jelzőfények", "stat.minecraft.interact_with_blast_furnace": "Megnyitott kohók", "stat.minecraft.interact_with_brewingstand": "Megnyitott főzőállványok", "stat.minecraft.interact_with_campfire": "Tábortüzekkel végzett műveletek", "stat.minecraft.interact_with_cartography_table": "Megnyitott térképasztalok", "stat.minecraft.interact_with_crafting_table": "Megnyitott barkácsasztalok", "stat.minecraft.interact_with_furnace": "Megnyitott kemencék", "stat.minecraft.interact_with_grindstone": "Megnyitott köszörűkövek", "stat.minecraft.interact_with_lectern": "Megnyitott olvasóállván<PERSON>k", "stat.minecraft.interact_with_loom": "Megnyitott szövőszékek", "stat.minecraft.interact_with_smithing_table": "Megnyitott kovácsasztalok", "stat.minecraft.interact_with_smoker": "Megnyitott füstölők", "stat.minecraft.interact_with_stonecutter": "Megnyitott kővágók", "stat.minecraft.jump": "Ugrások", "stat.minecraft.leave_game": "Kilépések száma", "stat.minecraft.minecart_one_cm": "Távolság csillével", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON>", "stat.minecraft.open_barrel": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_shulker_box": "Kinyitott shulkerdobozok", "stat.minecraft.pig_one_cm": "<PERSON>ávo<PERSON><PERSON><PERSON>", "stat.minecraft.play_noteblock": "Megszólaltatott hangdobozok", "stat.minecraft.play_record": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.play_time": "Játékidő", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON>", "stat.minecraft.pot_flower": "Cserépbe tett növények", "stat.minecraft.raid_trigger": "Kiprovokált rohamok", "stat.minecraft.raid_win": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.sleep_in_bed": "Ágyban töltött <PERSON>", "stat.minecraft.sneak_time": "Lopakodás ideje", "stat.minecraft.sprint_one_cm": "Távolság futva", "stat.minecraft.strider_one_cm": "Távolság <PERSON>", "stat.minecraft.swim_one_cm": "Távolság úszva", "stat.minecraft.talked_to_villager": "Beszélgetések falusiakkal", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ltá<PERSON>lák", "stat.minecraft.time_since_death": "Idő az utolsó hal<PERSON>l <PERSON>", "stat.minecraft.time_since_rest": "Idő az utolsó alvás óta", "stat.minecraft.total_world_time": "Világban eltöltött idő", "stat.minecraft.traded_with_villager": "Adásvételek falusiakkal", "stat.minecraft.trigger_trapped_chest": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.tune_noteblock": "<PERSON><PERSON><PERSON><PERSON> hangdobozok", "stat.minecraft.use_cauldron": "Üstből megtöltött palackok", "stat.minecraft.walk_on_water_one_cm": "Távolság vízen sétálva", "stat.minecraft.walk_one_cm": "Távolság sétálva", "stat.minecraft.walk_under_water_one_cm": "Távolság víz alatt sétálva", "stat.mobsButton": "Mobok", "stat_type.minecraft.broken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.crafted": "Elkészítve", "stat_type.minecraft.dropped": "<PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.killed": "%s %s mobot öltél meg", "stat_type.minecraft.killed.none": "Még sosem öltél %s mobot", "stat_type.minecraft.killed_by": "%2$s alkalommal ölt meg %1$s", "stat_type.minecraft.killed_by.none": "Még sosem ölt meg %s", "stat_type.minecraft.mined": "Bányászva", "stat_type.minecraft.picked_up": "Felszedve", "stat_type.minecraft.used": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stats.none": "–", "structure_block.button.detect_size": "INDÍTÁS", "structure_block.button.load": "BETÖLTÉS", "structure_block.button.save": "MENTÉS", "structure_block.custom_data": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>í<PERSON> neve", "structure_block.detect_size": "<PERSON><PERSON> <PERSON>s méret meghatároz<PERSON>:", "structure_block.hover.corner": "Sarok: %s", "structure_block.hover.data": "Adat: %s", "structure_block.hover.load": "Betöltés: %s", "structure_block.hover.save": "Mentés: %s", "structure_block.include_entities": "Entitások beleszámítása:", "structure_block.integrity": "Struktúra teljessége és kezdőérték", "structure_block.integrity.integrity": "Struktúra teljessége", "structure_block.integrity.seed": "Strukt<PERSON>ra k<PERSON>", "structure_block.invalid_structure_name": "Érvénytelen struktúranév: '%s'", "structure_block.load_not_found": "A(z) '%s' struktúra nem található", "structure_block.load_prepare": "'%s' struk<PERSON><PERSON><PERSON> helye előkészítve", "structure_block.load_success": "A(z) '%s' struktúra betöltve", "structure_block.mode.corner": "Sarok", "structure_block.mode.data": "Ada<PERSON>", "structure_block.mode.load": "Betöltés", "structure_block.mode.save": "Men<PERSON>s", "structure_block.mode_info.corner": "<PERSON><PERSON> mód - mé<PERSON> és helyzet megjelölése", "structure_block.mode_info.data": "Adat mód - NBT-adatok megjelölése", "structure_block.mode_info.load": "Betöltés mód - fájlból olvasás", "structure_block.mode_info.save": "Mentés mód - fájlba írás", "structure_block.position": "Re<PERSON><PERSON><PERSON>", "structure_block.position.x": "relatív x hely<PERSON>t", "structure_block.position.y": "viszonylagos y helyzet", "structure_block.position.z": "viszonylagos z helyzet", "structure_block.save_failure": "<PERSON><PERSON> elmenteni a(z) '%s' struktú<PERSON><PERSON><PERSON>", "structure_block.save_success": "Struktúra elmentve '%s' néven", "structure_block.show_air": "Láthatatlan blokkok megjelenítése:", "structure_block.show_boundingbox": "Kijelölt téglatest megjelenítése:", "structure_block.size": "Strukt<PERSON><PERSON> mé<PERSON>e", "structure_block.size.x": "struktúra X mérete", "structure_block.size.y": "struktúra Y mérete", "structure_block.size.z": "struktúra Z mérete", "structure_block.size_failure": "A struktúra méretét nem sikerült meghatározni; tegyél le megegyező nevű sarokblokkokat", "structure_block.size_success": "'%s' struk<PERSON><PERSON><PERSON> mérete si<PERSON>esen me<PERSON>", "structure_block.strict": "<PERSON><PERSON>or<PERSON> le<PERSON>z<PERSON>:", "structure_block.structure_name": "Struktúra neve", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.ambient.sound": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.amethyst_block.chime": "Ametiszt megcsendül", "subtitles.block.amethyst_block.resonate": "Ametiszt rezonál", "subtitles.block.anvil.destroy": "<PERSON><PERSON><PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON><PERSON>", "subtitles.block.anvil.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.barrel.close": "<PERSON><PERSON><PERSON>", "subtitles.block.barrel.open": "<PERSON><PERSON><PERSON>", "subtitles.block.beacon.activate": "Jelzőfény aktiválódik", "subtitles.block.beacon.ambient": "Jelzőf<PERSON><PERSON> z<PERSON>", "subtitles.block.beacon.deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.beacon.power_select": "Jelzőfény hatása kiválasztva", "subtitles.block.beehive.drip": "M<PERSON>z <PERSON>", "subtitles.block.beehive.enter": "<PERSON><PERSON><PERSON> be<PERSON> a kaptárba", "subtitles.block.beehive.exit": "<PERSON><PERSON><PERSON> a kaptá<PERSON>", "subtitles.block.beehive.shear": "<PERSON><PERSON><PERSON>", "subtitles.block.beehive.work": "Méhek dolgoznak", "subtitles.block.bell.resonate": "<PERSON>ng rezon<PERSON>", "subtitles.block.bell.use": "Harang kondul", "subtitles.block.big_dripleaf.tilt_down": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.big_dripleaf.tilt_up": "Csepplevél f<PERSON>lik", "subtitles.block.blastfurnace.fire_crackle": "<PERSON><PERSON><PERSON>", "subtitles.block.brewing_stand.brew": "Főzőállvány bugyborékol", "subtitles.block.bubble_column.bubble_pop": "Buborékok kipukkannak", "subtitles.block.bubble_column.upwards_ambient": "Buborékok <PERSON>", "subtitles.block.bubble_column.upwards_inside": "Buborékok zúgnak", "subtitles.block.bubble_column.whirlpool_ambient": "Buborékok örvénylenek", "subtitles.block.bubble_column.whirlpool_inside": "Buborékok zubo<PERSON>k", "subtitles.block.button.click": "Gomb kattan", "subtitles.block.cake.add_candle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.campfire.crackle": "Tábortűz ropog", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "subtitles.block.candle.extinguish": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.chest.close": "<PERSON><PERSON><PERSON>", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON> nem nyílik", "subtitles.block.chest.open": "<PERSON><PERSON><PERSON>", "subtitles.block.chorus_flower.death": "<PERSON>f<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.chorus_flower.grow": "Refrénvir<PERSON><PERSON> nő", "subtitles.block.comparator.click": "<PERSON><PERSON><PERSON><PERSON><PERSON> kattan", "subtitles.block.composter.empty": "Komposztáló <PERSON>", "subtitles.block.composter.fill": "Komposz<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.composter.ready": "Komposztáló kompo<PERSON>l", "subtitles.block.conduit.activate": "Csatorna aktiválódik", "subtitles.block.conduit.ambient": "Csatorna pu<PERSON>z<PERSON>", "subtitles.block.conduit.attack.target": "Csatorn<PERSON> t<PERSON>", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.copper_bulb.turn_off": "Rézizzó le<PERSON>ol", "subtitles.block.copper_bulb.turn_on": "Rézizzó felkapcsol", "subtitles.block.copper_trapdoor.close": "Csapóajtó zárul", "subtitles.block.copper_trapdoor.open": "Csapóajtó nyílik", "subtitles.block.crafter.craft": "Barkácsoló barkácsol", "subtitles.block.crafter.fail": "Barkácsoló megakad", "subtitles.block.creaking_heart.hurt": "Csikorgószív morog", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.creaking_heart.spawn": "Csikorgószív felébred", "subtitles.block.deadbush.idle": "Bokorzörgés", "subtitles.block.decorated_pot.insert": "Cserépedény telik", "subtitles.block.decorated_pot.insert_fail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.decorated_pot.shatter": "<PERSON><PERSON><PERSON>", "subtitles.block.dispenser.dispense": "<PERSON><PERSON><PERSON> adagol", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON> kattan", "subtitles.block.door.toggle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.ambient": "Kisz<PERSON><PERSON><PERSON> G<PERSON> z<PERSON>", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.dried_ghast.place_in_water": "Kiszáradt Ghast pancsol", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON><PERSON><PERSON> G<PERSON> jobban érzi mag<PERSON>t", "subtitles.block.dry_grass.ambient": "<PERSON><PERSON><PERSON> hangok", "subtitles.block.enchantment_table.use": "Varázslóasztalt használják", "subtitles.block.end_portal.spawn": "End-port<PERSON><PERSON>", "subtitles.block.end_portal_frame.fill": "<PERSON>er s<PERSON><PERSON> he<PERSON> ill<PERSON>", "subtitles.block.eyeblossom.close": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.eyeblossom.idle": "Szemvirág suttog", "subtitles.block.eyeblossom.open": "Szemvirág <PERSON>", "subtitles.block.fence_gate.toggle": "Kerítéskapu csikorog", "subtitles.block.fire.ambient": "Tűz ropog", "subtitles.block.fire.extinguish": "Tűz kialszik", "subtitles.block.firefly_bush.idle": "Szentjánosbogarak zümmögnek", "subtitles.block.frogspawn.hatch": "<PERSON><PERSON><PERSON> kikel", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON><PERSON> patto<PERSON>", "subtitles.block.generic.break": "Blokk törik", "subtitles.block.generic.fall": "Valami egy blokkra esik", "subtitles.block.generic.footsteps": "Lé<PERSON><PERSON>ek", "subtitles.block.generic.hit": "Blokk törik", "subtitles.block.generic.place": "Blokk elhelyezése", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.growing_plant.crop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> me<PERSON>", "subtitles.block.honey_block.slide": "Lecsúszás egy mézblokkon", "subtitles.block.iron_trapdoor.close": "Csapóajtó zárul", "subtitles.block.iron_trapdoor.open": "Csapóajtó nyílik", "subtitles.block.lava.ambient": "<PERSON><PERSON><PERSON> bug<PERSON>", "subtitles.block.lava.extinguish": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.block.lever.click": "<PERSON>r kattan", "subtitles.block.note_block.note": "Hangdoboz megszólal", "subtitles.block.pale_hanging_moss.idle": "Kísérteties zaj", "subtitles.block.piston.move": "Dugattyú mozog", "subtitles.block.pointed_dripstone.drip_lava": "L<PERSON>va <PERSON>", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Láva üstbe csöpög", "subtitles.block.pointed_dripstone.drip_water": "Víz c<PERSON>ö<PERSON>ög", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Víz üstbe csöpög", "subtitles.block.pointed_dripstone.land": "Függőcseppkő <PERSON>zu<PERSON>", "subtitles.block.portal.ambient": "Portál sistereg", "subtitles.block.portal.travel": "Port<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.portal.trigger": "Portálsistergés f<PERSON>", "subtitles.block.pressure_plate.click": "Nyom<PERSON><PERSON> kattan", "subtitles.block.pumpkin.carve": "<PERSON><PERSON><PERSON>", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON> sercen", "subtitles.block.respawn_anchor.ambient": "Portál sistereg", "subtitles.block.respawn_anchor.charge": "Újraéledés-kapocs feltöltődik", "subtitles.block.respawn_anchor.deplete": "Újraéledés-kapocs <PERSON>ül", "subtitles.block.respawn_anchor.set_spawn": "Újraéledés-kapocs beállítva", "subtitles.block.sand.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.sand.wind": "Szélzúgás", "subtitles.block.sculk.charge": "Sculk bugyog", "subtitles.block.sculk.spread": "Sculk terjed", "subtitles.block.sculk_catalyst.bloom": "Sculkgerjesztő virágzik", "subtitles.block.sculk_sensor.clicking": "Sculkérzé<PERSON><PERSON> kattog", "subtitles.block.sculk_sensor.clicking_stop": "Sculkérzékelő abbahagyja a kattogást", "subtitles.block.sculk_shrieker.shriek": "Sculksikoltó sikolt", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON>", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON>", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> me<PERSON>", "subtitles.block.smithing_table.use": "Kov<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.smoker.smoke": "Füstölő füstöl", "subtitles.block.sniffer_egg.crack": "Orrontótoj<PERSON> megre<PERSON>", "subtitles.block.sniffer_egg.hatch": "Orrontófióka kikel", "subtitles.block.sniffer_egg.plop": "Orron<PERSON>ó to<PERSON> rak", "subtitles.block.sponge.absorb": "Szivacs szív", "subtitles.block.sweet_berry_bush.pick_berries": "Bogyók leszedése", "subtitles.block.trapdoor.close": "Csapóaj<PERSON><PERSON>", "subtitles.block.trapdoor.open": "Csapóajtó kinyílik", "subtitles.block.trapdoor.toggle": "Csapóajtó csikorog", "subtitles.block.trial_spawner.about_to_spawn_item": "Vészjósló tárgy <PERSON>zül", "subtitles.block.trial_spawner.ambient": "Küzdőtéri ketrec ropog", "subtitles.block.trial_spawner.ambient_charged": "Vészjósló ropogás", "subtitles.block.trial_spawner.ambient_ominous": "Vészjósló ropogás", "subtitles.block.trial_spawner.charge_activate": "Ómen rátelepszik egy küzdőtéri ketrecre", "subtitles.block.trial_spawner.close_shutter": "Küzdőtéri ketrec zárul", "subtitles.block.trial_spawner.detect_player": "Küzdőtéri ketrec feltöltődik", "subtitles.block.trial_spawner.eject_item": "Küzdőtéri ketrec tárgyakat dob", "subtitles.block.trial_spawner.ominous_activate": "Ómen rátelepszik egy küzdőtéri ketrecre", "subtitles.block.trial_spawner.open_shutter": "Küzdőtéri ketrec nyílik", "subtitles.block.trial_spawner.spawn_item": "Vészjósló tárgy megidéződik", "subtitles.block.trial_spawner.spawn_item_begin": "Vészjósló tá<PERSON> me<PERSON>", "subtitles.block.trial_spawner.spawn_mob": "<PERSON><PERSON>", "subtitles.block.tripwire.attach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.tripwire.click": "Botl<PERSON>dr<PERSON>t kattan", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.vault.activate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.vault.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.vault.close_shutter": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.vault.deactivate": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.vault.eject_item": "<PERSON><PERSON><PERSON><PERSON> dob", "subtitles.block.vault.insert_item": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.vault.insert_item_fail": "<PERSON><PERSON><PERSON><PERSON> nem nyílik ki", "subtitles.block.vault.open_shutter": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON><PERSON><PERSON> egy j<PERSON>", "subtitles.block.water.ambient": "<PERSON><PERSON>z <PERSON>g", "subtitles.block.wet_sponge.dries": "Szivacs megszárad", "subtitles.chiseled_bookshelf.insert": "Könyv elhelyezése", "subtitles.chiseled_bookshelf.insert_enchanted": "Varázskönyv elhelyezve", "subtitles.chiseled_bookshelf.take": "Könyv elvétele", "subtitles.chiseled_bookshelf.take_enchanted": "Varázskönyv elvétele", "subtitles.enchant.thorns.hit": "Tüskék szúrnak", "subtitles.entity.allay.ambient_with_item": "Nyugtancs keres", "subtitles.entity.allay.ambient_without_item": "Nyugtancs sóvárog", "subtitles.entity.allay.death": "Nyugtancs meghal", "subtitles.entity.allay.hurt": "Nyugtancs sérül", "subtitles.entity.allay.item_given": "Nyugtancs kuncog", "subtitles.entity.allay.item_taken": "Nyugtancs nyugtat", "subtitles.entity.allay.item_thrown": "Nyugtancs dob", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON> horkant", "subtitles.entity.armadillo.brush": "<PERSON><PERSON>", "subtitles.entity.armadillo.death": "<PERSON><PERSON> meghal", "subtitles.entity.armadillo.eat": "<PERSON><PERSON>", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON>", "subtitles.entity.armadillo.hurt_reduced": "<PERSON><PERSON>", "subtitles.entity.armadillo.land": "<PERSON><PERSON> f<PERSON>", "subtitles.entity.armadillo.peek": "<PERSON><PERSON>", "subtitles.entity.armadillo.roll": "<PERSON><PERSON>mbölyödik", "subtitles.entity.armadillo.scute_drop": "<PERSON><PERSON> vedlik", "subtitles.entity.armadillo.unroll_finish": "<PERSON><PERSON>lyödik", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON>", "subtitles.entity.armor_stand.fall": "<PERSON><PERSON> le<PERSON>", "subtitles.entity.arrow.hit": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.arrow.hit_player": "Játékost találat ér", "subtitles.entity.arrow.shoot": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.attack": "Axolotl támad", "subtitles.entity.axolotl.death": "Ax<PERSON><PERSON> meghal", "subtitles.entity.axolotl.hurt": "Axolotl <PERSON>", "subtitles.entity.axolotl.idle_air": "Axolotl csipog", "subtitles.entity.axolotl.idle_water": "Axolotl csicsereg", "subtitles.entity.axolotl.splash": "Axolotl c<PERSON>bban", "subtitles.entity.axolotl.swim": "Axolotl <PERSON>", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.takeoff": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bee.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.bee.death": "<PERSON><PERSON><PERSON>al", "subtitles.entity.bee.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.bee.loop": "<PERSON><PERSON><PERSON>", "subtitles.entity.bee.loop_aggressive": "<PERSON><PERSON><PERSON>", "subtitles.entity.bee.pollinate": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.entity.bee.sting": "<PERSON><PERSON><PERSON>", "subtitles.entity.blaze.ambient": "Őrláng lélegzik", "subtitles.entity.blaze.burn": "Őrláng pattog", "subtitles.entity.blaze.death": "Őrláng meghal", "subtitles.entity.blaze.hurt": "Őrláng sér<PERSON>l", "subtitles.entity.blaze.shoot": "Őrláng lő", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Láplövész csörög", "subtitles.entity.bogged.death": "Láplöv<PERSON><PERSON>al", "subtitles.entity.bogged.hurt": "Láplöv<PERSON><PERSON>", "subtitles.entity.breeze.charge": "Őrszél tá<PERSON><PERSON>", "subtitles.entity.breeze.death": "Őrszél meghal", "subtitles.entity.breeze.deflect": "Őrszél h<PERSON>r<PERSON>t", "subtitles.entity.breeze.hurt": "Őrszél sér<PERSON>l", "subtitles.entity.breeze.idle_air": "Őrszél repül", "subtitles.entity.breeze.idle_ground": "Őrszél zúg", "subtitles.entity.breeze.inhale": "Őrszél belélegzik", "subtitles.entity.breeze.jump": "Őrszél ugrik", "subtitles.entity.breeze.land": "Őrszél földet ér", "subtitles.entity.breeze.shoot": "Őrszél lő", "subtitles.entity.breeze.slide": "Őrszél csúszik", "subtitles.entity.breeze.whirl": "Őrszél örvénylik", "subtitles.entity.breeze.wind_burst": "Széllöket becsapódik", "subtitles.entity.camel.ambient": "<PERSON><PERSON> horkant", "subtitles.entity.camel.dash": "<PERSON><PERSON>", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.camel.death": "<PERSON><PERSON> meghal", "subtitles.entity.camel.eat": "<PERSON><PERSON>", "subtitles.entity.camel.hurt": "<PERSON><PERSON>", "subtitles.entity.camel.saddle": "Malac felnyergelése", "subtitles.entity.camel.sit": "<PERSON><PERSON>", "subtitles.entity.camel.stand": "<PERSON><PERSON>", "subtitles.entity.camel.step": "<PERSON><PERSON>", "subtitles.entity.camel.step_sand": "<PERSON><PERSON> homo<PERSON> j<PERSON>r", "subtitles.entity.cat.ambient": "Macska nyávog", "subtitles.entity.cat.beg_for_food": "Macska kunyerál", "subtitles.entity.cat.death": "Macska meghal", "subtitles.entity.cat.eat": "Macska eszik", "subtitles.entity.cat.hiss": "Macska fúj", "subtitles.entity.cat.hurt": "Macska sérül", "subtitles.entity.cat.purr": "Macska dorombol", "subtitles.entity.chicken.ambient": "Csirke kotkodácsol", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.chicken.egg": "Csirke tojik", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON><PERSON> meghal", "subtitles.entity.cod.flop": "Tőkehal csapkod", "subtitles.entity.cod.hurt": "Tőkehal sérül", "subtitles.entity.cow.ambient": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.cow.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.cow.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.cow.milk": "<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.activate": "Csik<PERSON><PERSON><PERSON> f<PERSON>", "subtitles.entity.creaking.ambient": "Csikorgó c<PERSON>", "subtitles.entity.creaking.attack": "Csikorg<PERSON> t<PERSON>", "subtitles.entity.creaking.deactivate": "Csikorg<PERSON>", "subtitles.entity.creaking.death": "Csikorgó elporlad", "subtitles.entity.creaking.freeze": "Csik<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.spawn": "Csik<PERSON><PERSON><PERSON> f<PERSON>", "subtitles.entity.creaking.sway": "Csikor<PERSON><PERSON>", "subtitles.entity.creaking.twitch": "Csikorgó rángatózik", "subtitles.entity.creaking.unfreeze": "Csikorgó meg<PERSON>", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.creeper.primed": "Creeper sziszeg", "subtitles.entity.dolphin.ambient": "Delfin csipog", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON> f<PERSON>", "subtitles.entity.dolphin.attack": "<PERSON><PERSON> támad", "subtitles.entity.dolphin.death": "Delfin meghal", "subtitles.entity.dolphin.eat": "Delfin eszik", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON>", "subtitles.entity.dolphin.jump": "Delfin ugrik", "subtitles.entity.dolphin.play": "<PERSON><PERSON> j<PERSON>", "subtitles.entity.dolphin.splash": "<PERSON><PERSON> c<PERSON>n", "subtitles.entity.dolphin.swim": "<PERSON><PERSON>", "subtitles.entity.donkey.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.donkey.angry": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.donkey.chest": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.donkey.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.donkey.eat": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.donkey.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient": "Vízbefúlt hö<PERSON>ög", "subtitles.entity.drowned.ambient_water": "Vízbefúlt hö<PERSON>ög", "subtitles.entity.drowned.death": "Vízbefúlt meghal", "subtitles.entity.drowned.hurt": "Vízbefúlt s<PERSON>", "subtitles.entity.drowned.shoot": "Vízbefúlt szigonyt hajít", "subtitles.entity.drowned.step": "Vízbefúlt lép", "subtitles.entity.drowned.swim": "Vízbefúlt úszik", "subtitles.entity.egg.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.ambient": "<PERSON>én ő<PERSON>", "subtitles.entity.elder_guardian.ambient_land": "<PERSON>én <PERSON> vis<PERSON>t", "subtitles.entity.elder_guardian.curse": "<PERSON><PERSON>", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON> meghal", "subtitles.entity.elder_guardian.flop": "Vén ő<PERSON>m c<PERSON>pkod", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON>", "subtitles.entity.ender_dragon.ambient": "Sárkány üvölt", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>al", "subtitles.entity.ender_dragon.flap": "Sárk<PERSON><PERSON> c<PERSON>", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON><PERSON><PERSON> morog", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.shoot": "Sárkány lő", "subtitles.entity.ender_eye.death": "<PERSON><PERSON> s<PERSON><PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>l", "subtitles.entity.enderman.ambient": "Enderman vwuppog", "subtitles.entity.enderman.death": "<PERSON><PERSON> meghal", "subtitles.entity.enderman.hurt": "<PERSON><PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.stare": "<PERSON><PERSON> f<PERSON>", "subtitles.entity.enderman.teleport": "Enderman teleportál", "subtitles.entity.endermite.ambient": "Endermite mászkál", "subtitles.entity.endermite.death": "Endermite meghal", "subtitles.entity.endermite.hurt": "Endermite sérül", "subtitles.entity.evoker.ambient": "<PERSON>d<PERSON><PERSON><PERSON> mormog", "subtitles.entity.evoker.cast_spell": "<PERSON>d<PERSON><PERSON><PERSON>", "subtitles.entity.evoker.celebrate": "Idéző örvendezik", "subtitles.entity.evoker.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>al", "subtitles.entity.evoker.hurt": "Idéző sérül", "subtitles.entity.evoker.prepare_attack": "Idéző támadni készül", "subtitles.entity.evoker.prepare_summon": "Idéző idézni készül", "subtitles.entity.evoker.prepare_wololo": "Idéző megigézésre készül", "subtitles.entity.evoker_fangs.attack": "Állkapcsok csattannak", "subtitles.entity.experience_orb.pickup": "Tapasztalatszerzés", "subtitles.entity.firework_rocket.blast": "Tűzijáték robban", "subtitles.entity.firework_rocket.launch": "Tűzijáték kilövése", "subtitles.entity.firework_rocket.twinkle": "Tűzijáték villódzik", "subtitles.entity.fish.swim": "Csobba<PERSON><PERSON>", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON><PERSON>", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON><PERSON>", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.ambient": "Róka n<PERSON>", "subtitles.entity.fox.bite": "<PERSON><PERSON><PERSON> ha<PERSON>", "subtitles.entity.fox.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.fox.eat": "<PERSON><PERSON>ka <PERSON>", "subtitles.entity.fox.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.screech": "Ró<PERSON> vis<PERSON>", "subtitles.entity.fox.sleep": "Róka ho<PERSON>ol", "subtitles.entity.fox.sniff": "Róka szimatol", "subtitles.entity.fox.spit": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.teleport": "Róka teleportál", "subtitles.entity.frog.ambient": "<PERSON><PERSON><PERSON> brek<PERSON>", "subtitles.entity.frog.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.frog.eat": "Béka eszik", "subtitles.entity.frog.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.frog.lay_spawn": "<PERSON><PERSON><PERSON> pet<PERSON> rak", "subtitles.entity.frog.long_jump": "<PERSON><PERSON><PERSON> u<PERSON>", "subtitles.entity.generic.big_fall": "<PERSON><PERSON> le<PERSON>", "subtitles.entity.generic.burn": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.death": "Haldoklás", "subtitles.entity.generic.drink": "Kortyolás", "subtitles.entity.generic.eat": "Evés", "subtitles.entity.generic.explode": "Robbanás", "subtitles.entity.generic.extinguish_fire": "Tűz kialszik", "subtitles.entity.generic.hurt": "<PERSON><PERSON>", "subtitles.entity.generic.small_fall": "Valami megbotlik", "subtitles.entity.generic.splash": "Csobba<PERSON><PERSON>", "subtitles.entity.generic.swim": "Úszás", "subtitles.entity.generic.wind_burst": "Széllöket becsapódik", "subtitles.entity.ghast.ambient": "Ghast nyöszörög", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON> turb<PERSON>kol", "subtitles.entity.ghastling.death": "Ghastling meghal", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> me<PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.add_item": "Fénylő eszközkeret feltöltése", "subtitles.entity.glow_item_frame.break": "Fénylő eszközkeret eltörik", "subtitles.entity.glow_item_frame.place": "Fénylő eszközkeret elhelyezése", "subtitles.entity.glow_item_frame.remove_item": "Fénylő eszközkeret kiürül", "subtitles.entity.glow_item_frame.rotate_item": "Fénylő eszközkeret kattan", "subtitles.entity.glow_squid.ambient": "Fénylő tintahal úszik", "subtitles.entity.glow_squid.death": "<PERSON><PERSON><PERSON><PERSON> tintahal meghal", "subtitles.entity.glow_squid.hurt": "Fénylő tintahal sérül", "subtitles.entity.glow_squid.squirt": "Fénylő tintahal tintát lő", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> mekeg", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> eszik", "subtitles.entity.goat.horn_break": "Kecsketülök letörik", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.long_jump": "<PERSON><PERSON><PERSON> ug<PERSON>", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON> tejel", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.step": "<PERSON><PERSON><PERSON> lé<PERSON>", "subtitles.entity.guardian.ambient": "Őrszem dünnyög", "subtitles.entity.guardian.ambient_land": "Őrszem visít", "subtitles.entity.guardian.attack": "Őrszem lő", "subtitles.entity.guardian.death": "Őrszem meghal", "subtitles.entity.guardian.flop": "Őrszem csapkod", "subtitles.entity.guardian.hurt": "Őrszem sér<PERSON>l", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON><PERSON> ghast meghal", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> morog", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> morog", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> t<PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON>n <PERSON>ná változik", "subtitles.entity.hoglin.death": "<PERSON><PERSON>n meghal", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.ambient": "<PERSON><PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON>", "subtitles.entity.horse.armor": "<PERSON><PERSON> fel<PERSON>", "subtitles.entity.horse.breathe": "<PERSON><PERSON>", "subtitles.entity.horse.death": "<PERSON><PERSON>", "subtitles.entity.horse.eat": "<PERSON><PERSON>", "subtitles.entity.horse.gallop": "<PERSON><PERSON>", "subtitles.entity.horse.hurt": "<PERSON><PERSON>", "subtitles.entity.horse.jump": "<PERSON><PERSON>", "subtitles.entity.horse.saddle": "<PERSON><PERSON>", "subtitles.entity.husk.ambient": "Kiszáradt zombi hörög", "subtitles.entity.husk.converted_to_zombie": "Kiszáradt zombi Zombivá változik", "subtitles.entity.husk.death": "Kiszáradt zombi meghal", "subtitles.entity.husk.hurt": "Kiszáradt zombi sérül", "subtitles.entity.illusioner.ambient": "Szemfényvesztő mormog", "subtitles.entity.illusioner.cast_spell": "Szemfényvesztő varázsol", "subtitles.entity.illusioner.death": "Szemfényvesztő meghal", "subtitles.entity.illusioner.hurt": "Szemfényvesztő sérül", "subtitles.entity.illusioner.mirror_move": "Szemfényvesztő képmást vált", "subtitles.entity.illusioner.prepare_blindness": "Szemfényvesztő vakítani készül", "subtitles.entity.illusioner.prepare_mirror": "Szemfényvesztő képmásokat vet", "subtitles.entity.iron_golem.attack": "Vasgólem támad", "subtitles.entity.iron_golem.damage": "Vasgólem eltörik", "subtitles.entity.iron_golem.death": "Vasgólem meghal", "subtitles.entity.iron_golem.hurt": "Vasgólem sérül", "subtitles.entity.iron_golem.repair": "Vasgólem javítása", "subtitles.entity.item.break": "<PERSON><PERSON><PERSON>", "subtitles.entity.item.pickup": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.add_item": "Eszközkeret feltöltődik", "subtitles.entity.item_frame.break": "Eszközkeret törése", "subtitles.entity.item_frame.place": "Eszközkeret elhelyezése", "subtitles.entity.item_frame.remove_item": "Eszközkeret kiürül", "subtitles.entity.item_frame.rotate_item": "Eszközkeret kattan", "subtitles.entity.leash_knot.break": "Lasszócsomó kibomlik", "subtitles.entity.leash_knot.place": "Lasszócsomó megkötése", "subtitles.entity.lightning_bolt.impact": "<PERSON><PERSON><PERSON><PERSON> le<PERSON>", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON>", "subtitles.entity.llama.ambient": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.llama.angry": "<PERSON><PERSON><PERSON> béget", "subtitles.entity.llama.chest": "<PERSON><PERSON><PERSON>", "subtitles.entity.llama.death": "<PERSON><PERSON><PERSON>al", "subtitles.entity.llama.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.llama.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.llama.spit": "<PERSON><PERSON><PERSON>", "subtitles.entity.llama.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.llama.swag": "<PERSON><PERSON><PERSON>", "subtitles.entity.magma_cube.death": "<PERSON><PERSON><PERSON><PERSON>a meghal", "subtitles.entity.magma_cube.hurt": "Magmakocka <PERSON>", "subtitles.entity.magma_cube.squish": "Magmakocka csattog", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON>", "subtitles.entity.minecart.inside_underwater": "Csille víz alatt zörög", "subtitles.entity.minecart.riding": "<PERSON><PERSON><PERSON> gurul", "subtitles.entity.mooshroom.convert": "Mooshroom átalakul", "subtitles.entity.mooshroom.eat": "Mooshroom eszik", "subtitles.entity.mooshroom.milk": "Mooshroom tejel", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom gyanúsan tejel", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mule.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>al", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mule.jump": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.painting.break": "Festmény összetörik", "subtitles.entity.painting.place": "Festmény elhelyezése", "subtitles.entity.panda.aggressive_ambient": "Panda fújtat", "subtitles.entity.panda.ambient": "Panda liheg", "subtitles.entity.panda.bite": "Panda harap", "subtitles.entity.panda.cant_breed": "<PERSON><PERSON> béget", "subtitles.entity.panda.death": "<PERSON>da meghal", "subtitles.entity.panda.eat": "Panda eszik", "subtitles.entity.panda.hurt": "<PERSON><PERSON>", "subtitles.entity.panda.pre_sneeze": "Panda orra viszket", "subtitles.entity.panda.sneeze": "Panda tüsszent", "subtitles.entity.panda.step": "Panda lép", "subtitles.entity.panda.worried_ambient": "Panda nyöszörög", "subtitles.entity.parrot.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.fly": "<PERSON><PERSON><PERSON><PERSON> repked", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.creaking": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.ghast": "Papagáj n<PERSON>ösz<PERSON>r<PERSON>g", "subtitles.entity.parrot.imitate.guardian": "<PERSON>g<PERSON><PERSON>", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON><PERSON><PERSON> utánoz", "subtitles.entity.parrot.imitate.silverfish": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.warden": "<PERSON>g<PERSON><PERSON>", "subtitles.entity.parrot.imitate.witch": "<PERSON>g<PERSON><PERSON> vihog", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.phantom.ambient": "<PERSON><PERSON>v<PERSON>", "subtitles.entity.phantom.bite": "<PERSON><PERSON> harap", "subtitles.entity.phantom.death": "<PERSON><PERSON> meghal", "subtitles.entity.phantom.flap": "Fantom csapkod", "subtitles.entity.phantom.hurt": "<PERSON><PERSON>", "subtitles.entity.phantom.swoop": "<PERSON><PERSON> lecsap", "subtitles.entity.pig.ambient": "Malac röfög", "subtitles.entity.pig.death": "<PERSON><PERSON> meghal", "subtitles.entity.pig.hurt": "<PERSON><PERSON>", "subtitles.entity.pig.saddle": "Malac felnyergelése", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> tárgyat c<PERSON>d<PERSON>l", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> horkant", "subtitles.entity.piglin.angry": "<PERSON><PERSON> d<PERSON> horkant", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> ünnepel", "subtitles.entity.piglin.converted_to_zombified": "Piglin Élőhalott piglinné változik", "subtitles.entity.piglin.death": "<PERSON><PERSON> meghal", "subtitles.entity.piglin.hurt": "<PERSON><PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> irigykedve horkant", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> v<PERSON>", "subtitles.entity.piglin.step": "<PERSON><PERSON> l<PERSON>", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON> ver<PERSON>leg<PERSON><PERSON> horkant", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON> verőlegény d<PERSON> horkant", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin verőlegény Élőhalott piglinné változik", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> ver<PERSON><PERSON><PERSON><PERSON>al", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> ver<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON> verőlegény lép", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.pillager.celebrate": "Rabló örvendezik", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.crit": "<PERSON>rit<PERSON><PERSON>", "subtitles.entity.player.attack.knockback": "Hátralökő támadás", "subtitles.entity.player.attack.strong": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.sweep": "Suhintó tá<PERSON>", "subtitles.entity.player.attack.weak": "Gyenge támadás", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.freeze_hurt": "Játékos megfagy", "subtitles.entity.player.hurt": "Játékos <PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.levelup": "Játékos szintet lép", "subtitles.entity.player.teleport": "Játékos teleportál", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON> morog", "subtitles.entity.polar_bear.ambient_baby": "Je<PERSON><PERSON><PERSON> brummog", "subtitles.entity.polar_bear.death": "<PERSON><PERSON><PERSON><PERSON> meghal", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.warning": "Jegesmedve üvölt", "subtitles.entity.potion.splash": "<PERSON><PERSON>ck összetörik", "subtitles.entity.potion.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_out": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_up": "Gömbhal felfúvódik", "subtitles.entity.puffer_fish.death": "<PERSON><PERSON><PERSON><PERSON> meghal", "subtitles.entity.puffer_fish.flop": "Gömbhal csapkod", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.sting": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ú<PERSON>", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.ambient": "P<PERSON>z<PERSON>í<PERSON><PERSON> mordul", "subtitles.entity.ravager.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> harap", "subtitles.entity.ravager.celebrate": "Pusztító örvendezik", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> me<PERSON>al", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.roar": "Pusztító <PERSON>", "subtitles.entity.ravager.step": "Pusztító lép", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.salmon.death": "<PERSON><PERSON> meghal", "subtitles.entity.salmon.flop": "<PERSON>zac csapkod", "subtitles.entity.salmon.hurt": "<PERSON><PERSON>", "subtitles.entity.sheep.ambient": "<PERSON><PERSON><PERSON> b<PERSON>get", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.sheep.hurt": "<PERSON>irk<PERSON>", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> teleportál", "subtitles.entity.shulker_bullet.hit": "Shulkerlövedék felrobban", "subtitles.entity.shulker_bullet.hurt": "Shulkerlövedék eltörik", "subtitles.entity.silverfish.ambient": "Ezüst<PERSON><PERSON>", "subtitles.entity.silverfish.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>al", "subtitles.entity.silverfish.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.skeleton.ambient": "Csontváz csörög", "subtitles.entity.skeleton.converted_to_stray": "Csontváz Kóborlóvá változik", "subtitles.entity.skeleton.death": "Csontv<PERSON><PERSON>ghal", "subtitles.entity.skeleton.hurt": "Csontv<PERSON><PERSON>", "subtitles.entity.skeleton.shoot": "Csontváz lő", "subtitles.entity.skeleton_horse.ambient": "Csontvá<PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.skeleton_horse.death": "Csontváz ló meghal", "subtitles.entity.skeleton_horse.hurt": "Csontváz l<PERSON>", "subtitles.entity.skeleton_horse.jump_water": "Csontvázló víz alatt ugrik", "subtitles.entity.skeleton_horse.swim": "Csontvá<PERSON><PERSON><PERSON>", "subtitles.entity.slime.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "subtitles.entity.slime.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> meghal", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.slime.squish": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>g", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON><PERSON> meghal", "subtitles.entity.sniffer.digging": "Orrontó á<PERSON>", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.drop_seed": "Orrontó magot talál", "subtitles.entity.sniffer.eat": "Orrontó es<PERSON>k", "subtitles.entity.sniffer.egg_crack": "Orrontótoj<PERSON> megre<PERSON>", "subtitles.entity.sniffer.egg_hatch": "Orrontófióka kikel", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.scenting": "Orrontó szimatol", "subtitles.entity.sniffer.searching": "Orron<PERSON><PERSON> k<PERSON>", "subtitles.entity.sniffer.sniffing": "Orrontó szagot fog", "subtitles.entity.sniffer.step": "Orrontó lép", "subtitles.entity.snow_golem.death": "Hógólem meghal", "subtitles.entity.snow_golem.hurt": "Hógólem sérül", "subtitles.entity.snowball.throw": "Hógolyó re<PERSON>ül", "subtitles.entity.spider.ambient": "Pók sziszeg", "subtitles.entity.spider.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.spider.hurt": "Pók s<PERSON>ü<PERSON>", "subtitles.entity.squid.ambient": "Tintahal úszik", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.squid.hurt": "Tintahal sérül", "subtitles.entity.squid.squirt": "Tintahal tintát lő", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>al", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>al", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.happy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.retreat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.tadpole.flop": "Ebihal csapkod", "subtitles.entity.tadpole.grow_up": "Ebihal felnő", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.tnt.primed": "TNT serceg", "subtitles.entity.tropical_fish.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> hal meghal", "subtitles.entity.tropical_fish.flop": "Trópusi hal csapkod", "subtitles.entity.tropical_fish.hurt": "Trópusi hal s<PERSON>", "subtitles.entity.turtle.ambient_land": "Teknő<PERSON> c<PERSON>", "subtitles.entity.turtle.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> meghal", "subtitles.entity.turtle.death_baby": "Teknősfióka meghal", "subtitles.entity.turtle.egg_break": "Tek<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.egg_crack": "Teknőstojás megreped", "subtitles.entity.turtle.egg_hatch": "Teknősfióka kikel", "subtitles.entity.turtle.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.hurt_baby": "Teknősfióka sérül", "subtitles.entity.turtle.lay_egg": "Teknős to<PERSON> rak", "subtitles.entity.turtle.shamble": "Tek<PERSON><PERSON><PERSON> cammog", "subtitles.entity.turtle.shamble_baby": "Teknősfióka cammog", "subtitles.entity.turtle.swim": "Teknős <PERSON>", "subtitles.entity.vex.ambient": "Bosszancs bosszant", "subtitles.entity.vex.charge": "Bosszancs visít", "subtitles.entity.vex.death": "Bosszancs meghal", "subtitles.entity.vex.hurt": "Bosszancs sérül", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.celebrate": "Falusi örvendezik", "subtitles.entity.villager.death": "<PERSON><PERSON><PERSON><PERSON> meghal", "subtitles.entity.villager.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.no": "<PERSON>al<PERSON><PERSON>", "subtitles.entity.villager.trade": "Falusi üzletet köt", "subtitles.entity.villager.work_armorer": "Páncélkovács dolgozik", "subtitles.entity.villager.work_butcher": "Hentes dolgozik", "subtitles.entity.villager.work_cartographer": "Térképész dolgozik", "subtitles.entity.villager.work_cleric": "<PERSON><PERSON> do<PERSON>go<PERSON>k", "subtitles.entity.villager.work_farmer": "Földműves dolgozik", "subtitles.entity.villager.work_fisherman": "Halász dolgozik", "subtitles.entity.villager.work_fletcher": "Nyílkészítő dolgozik", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_librarian": "Könyvt<PERSON><PERSON>", "subtitles.entity.villager.work_mason": "Kőműves dolgozik", "subtitles.entity.villager.work_shepherd": "Pásztor dolgozik", "subtitles.entity.villager.work_toolsmith": "Eszközkovács dolgozik", "subtitles.entity.villager.work_weaponsmith": "Fegyverkovács dolgozik", "subtitles.entity.villager.yes": "Falusi e<PERSON>", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.vindicator.celebrate": "Bosszúálló örvendezik", "subtitles.entity.vindicator.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.ambient": "Vándorkereskedő motyog", "subtitles.entity.wandering_trader.death": "Vándorkereskedő meghal", "subtitles.entity.wandering_trader.disappeared": "Vándorkereskedő eltűnik", "subtitles.entity.wandering_trader.drink_milk": "Vándorkereskedő tejet iszik", "subtitles.entity.wandering_trader.drink_potion": "Vándorkereskedő bájitalt iszik", "subtitles.entity.wandering_trader.hurt": "Vándorkereskedő sérül", "subtitles.entity.wandering_trader.no": "Vándorkereskedő elutasít", "subtitles.entity.wandering_trader.reappeared": "Vándorkereskedő megjelenik", "subtitles.entity.wandering_trader.trade": "Vándorkereskedő üzletet köt", "subtitles.entity.wandering_trader.yes": "Vándorkereskedő elfogad", "subtitles.entity.warden.agitated": "Stráz<PERSON> d<PERSON> morog", "subtitles.entity.warden.ambient": "Strázsa nyög", "subtitles.entity.warden.angry": "Strázsa <PERSON>", "subtitles.entity.warden.attack_impact": "Strázsa ü<PERSON> talál", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> meghal", "subtitles.entity.warden.dig": "Strázsa <PERSON>", "subtitles.entity.warden.emerge": "Strázsa <PERSON>ő<PERSON>űnik", "subtitles.entity.warden.heartbeat": "Strázsa szíve dobog", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.warden.listening": "<PERSON><PERSON><PERSON><PERSON><PERSON> felfigyel", "subtitles.entity.warden.listening_angry": "Strázsa d<PERSON> felfigyel", "subtitles.entity.warden.nearby_close": "Strázsa közeleg", "subtitles.entity.warden.nearby_closer": "Strázsa k<PERSON> jön", "subtitles.entity.warden.nearby_closest": "Stráz<PERSON> k<PERSON> j<PERSON>r", "subtitles.entity.warden.roar": "Strázsa üvölt", "subtitles.entity.warden.sniff": "Strázsa szimatol", "subtitles.entity.warden.sonic_boom": "Str<PERSON><PERSON><PERSON> men<PERSON>dörög", "subtitles.entity.warden.sonic_charge": "Strázsa felkészül", "subtitles.entity.warden.step": "Strázsa lép", "subtitles.entity.warden.tendril_clicks": "Strázsa c<PERSON>á<PERSON><PERSON><PERSON> kattannak", "subtitles.entity.wind_charge.throw": "Széllöket száll", "subtitles.entity.wind_charge.wind_burst": "Széllöket becsapódik", "subtitles.entity.witch.ambient": "Boszorkány vihog", "subtitles.entity.witch.celebrate": "Boszorkány örvendezik", "subtitles.entity.witch.death": "Boszork<PERSON><PERSON>al", "subtitles.entity.witch.drink": "Boszorkány iszik", "subtitles.entity.witch.hurt": "Boszork<PERSON><PERSON>", "subtitles.entity.witch.throw": "Boszorkán<PERSON> dob", "subtitles.entity.wither.ambient": "<PERSON><PERSON>", "subtitles.entity.wither.death": "Wither meghal", "subtitles.entity.wither.hurt": "<PERSON><PERSON>", "subtitles.entity.wither.shoot": "<PERSON><PERSON>", "subtitles.entity.wither.spawn": "Wither <PERSON><PERSON> kel", "subtitles.entity.wither_skeleton.ambient": "Withercsontváz csörög", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>al", "subtitles.entity.wither_skeleton.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wolf.ambient": "<PERSON><PERSON> u<PERSON>", "subtitles.entity.wolf.bark": "<PERSON><PERSON> u<PERSON>", "subtitles.entity.wolf.death": "<PERSON><PERSON> meghal", "subtitles.entity.wolf.growl": "<PERSON><PERSON> morog", "subtitles.entity.wolf.hurt": "<PERSON><PERSON>", "subtitles.entity.wolf.pant": "<PERSON><PERSON> lih<PERSON>", "subtitles.entity.wolf.shake": "<PERSON><PERSON> me<PERSON>", "subtitles.entity.wolf.whine": "<PERSON><PERSON>", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> morog", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON> morog", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> l<PERSON>", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON>ó rázkódik", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie.converted_to_drowned": "Zombi Vízbefúlttá változik", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON> meghal", "subtitles.entity.zombie.destroy_egg": "Tek<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.infect": "Zombi fertőz", "subtitles.entity.zombie_horse.ambient": "Zombiló prüszköl", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON> ló meghal", "subtitles.entity.zombie_horse.hurt": "Zombi ló <PERSON>", "subtitles.entity.zombie_villager.ambient": "É<PERSON>őhalott falusi hö<PERSON>ög", "subtitles.entity.zombie_villager.converted": "Falusi <PERSON>", "subtitles.entity.zombie_villager.cure": "Élőhalott falusi szörcsög", "subtitles.entity.zombie_villager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> falusi meghal", "subtitles.entity.zombie_villager.hurt": "É<PERSON>őhalott falusi s<PERSON>l", "subtitles.entity.zombified_piglin.ambient": "Élőhalott piglin röfög", "subtitles.entity.zombified_piglin.angry": "Élőhalott Piglin dühösen röfög", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON><PERSON><PERSON>ott piglin meghal", "subtitles.entity.zombified_piglin.hurt": "Élőhalott piglin s<PERSON>", "subtitles.event.mob_effect.bad_omen": "<PERSON><PERSON>", "subtitles.event.mob_effect.raid_omen": "Közelgő roham fenyeget", "subtitles.event.mob_effect.trial_omen": "Közelgő vészjósló küzdelem fenyeget", "subtitles.event.raid.horn": "V<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.armor.equip": "<PERSON><PERSON><PERSON><PERSON> fel<PERSON>e", "subtitles.item.armor.equip_chain": "Láncpán<PERSON><PERSON>", "subtitles.item.armor.equip_diamond": "Gyémántpán<PERSON>l c<PERSON>g", "subtitles.item.armor.equip_elytra": "Kitinszárny zörren", "subtitles.item.armor.equip_gold": "Aranypán<PERSON><PERSON> c<PERSON>", "subtitles.item.armor.equip_iron": "Vaspáncél k<PERSON>", "subtitles.item.armor.equip_leather": "Bőrpáncél susog", "subtitles.item.armor.equip_netherite": "Netheritpáncél csattan", "subtitles.item.armor.equip_turtle": "Teknőspáncél koppan", "subtitles.item.armor.equip_wolf": "Farkaspáncél felcsatolása", "subtitles.item.armor.unequip_wolf": "Farkaspáncél lecsatolása", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "subtitles.item.axe.wax_off": "<PERSON><PERSON>", "subtitles.item.bone_meal.use": "Csontliszt ropog", "subtitles.item.book.page_turn": "Lapozás", "subtitles.item.book.put": "<PERSON><PERSON><PERSON><PERSON> puffan", "subtitles.item.bottle.empty": "Palack k<PERSON>ürül", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON> me<PERSON>k", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel.complete": "<PERSON><PERSON><PERSON>söpö<PERSON>", "subtitles.item.brush.brushing.sand": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.sand.complete": "Homok elsöpörve", "subtitles.item.bucket.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bucket.fill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "Axolotl felszedése", "subtitles.item.bucket.fill_fish": "<PERSON>", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON> be<PERSON>", "subtitles.item.bundle.drop_contents": "Bat<PERSON> k<PERSON>ü<PERSON>", "subtitles.item.bundle.insert": "<PERSON><PERSON><PERSON>", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON> me<PERSON>t", "subtitles.item.bundle.remove_one": "<PERSON><PERSON><PERSON>", "subtitles.item.chorus_fruit.teleport": "Játékos teleportál", "subtitles.item.crop.plant": "Növényültetés", "subtitles.item.crossbow.charge": "Számszeríj felhúzása", "subtitles.item.crossbow.hit": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.crossbow.load": "Számszeríj megtöltése", "subtitles.item.crossbow.shoot": "Száms<PERSON><PERSON><PERSON>", "subtitles.item.dye.use": "Festék fest", "subtitles.item.elytra.flying": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "Tűzgolyó <PERSON>", "subtitles.item.flintandsteel.use": "Kovakő és acél pattan", "subtitles.item.glow_ink_sac.use": "Fénylő tintazsák loccsan", "subtitles.item.goat_horn.play": "Kecsketülkön j<PERSON>", "subtitles.item.hoe.till": "<PERSON><PERSON>", "subtitles.item.honey_bottle.drink": "Kortyolás", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON> felvitele", "subtitles.item.horse_armor.unequip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.ink_sac.use": "Tintazsák loccsan", "subtitles.item.lead.break": "<PERSON><PERSON><PERSON>", "subtitles.item.lead.tied": "<PERSON><PERSON><PERSON>", "subtitles.item.lead.untied": "<PERSON><PERSON><PERSON>", "subtitles.item.llama_carpet.unequip": "Szőnyeg lekerül", "subtitles.item.lodestone_compass.lock": "Iránykő felmágnesez egy Iránytűt", "subtitles.item.mace.smash_air": "<PERSON>uzog<PERSON><PERSON>", "subtitles.item.mace.smash_ground": "<PERSON>uzog<PERSON><PERSON>", "subtitles.item.nether_wart.plant": "Növényültetés", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON> s<PERSON>ttörik", "subtitles.item.saddle.unequip": "<PERSON><PERSON><PERSON>", "subtitles.item.shears.shear": "<PERSON><PERSON><PERSON>", "subtitles.item.shears.snip": "<PERSON><PERSON><PERSON>", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.shovel.flatten": "<PERSON><PERSON><PERSON>", "subtitles.item.spyglass.stop_using": "Távcső összecsukódik", "subtitles.item.spyglass.use": "Távcső szétnyílik", "subtitles.item.totem.use": "Totem aktiválódik", "subtitles.item.trident.hit": "<PERSON><PERSON><PERSON> d<PERSON>", "subtitles.item.trident.hit_ground": "Szigony rezeg", "subtitles.item.trident.return": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.item.trident.riptide": "<PERSON><PERSON><PERSON>", "subtitles.item.trident.throw": "Szigony csörög", "subtitles.item.trident.thunder": "Szigony mennydörög", "subtitles.item.wolf_armor.break": "Far<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.wolf_armor.crack": "Farkaspáncél megre<PERSON>", "subtitles.item.wolf_armor.damage": "Farkas<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.wolf_armor.repair": "Farkaspáncél javí<PERSON>", "subtitles.particle.soul_escape": "Lélek kiszabadul", "subtitles.ui.cartography_table.take_result": "Térképrajzolás", "subtitles.ui.hud.bubble_pop": "Légzésmérő csökken", "subtitles.ui.loom.take_result": "Szövőszék sző", "subtitles.ui.stonecutter.take_result": "Kővágó vág", "subtitles.weather.rain": "<PERSON><PERSON><PERSON>", "symlink_warning.message": "Ha a világod mappájában szimbolikus linkek találhatóak, ve<PERSON><PERSON><PERSON><PERSON> le<PERSON>t me<PERSON>, amen<PERSON><PERSON>n nem tudod pontosan, mit csinálsz. További információkért látogass el a %s oldalra.", "symlink_warning.message.pack": "A csomagok szimbolikus linkekkel történő betöltése nem biztonságos, ha nem tudja pontosan, mit csinál. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> el a %s oldalra, ha többet szeretne megtudni.", "symlink_warning.message.world": "A világok betöltése szimbolikus linkekkel ellátott mappákból nem biztonságos, ha nem tudja pontosan, mit csinál. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> el a %s oldalra, ha többet szeretne megtudni.", "symlink_warning.more_info": "<PERSON><PERSON><PERSON>", "symlink_warning.title": "Szimbolikus linkek a világmappában", "symlink_warning.title.pack": "Szimbolikus linkek a hozzáadott csomag(ok)ban", "symlink_warning.title.world": "Szimbolikus linkek a világmappában", "team.collision.always": "<PERSON><PERSON>", "team.collision.never": "<PERSON><PERSON>", "team.collision.pushOtherTeams": "Ütközés más csapatok tagjaival", "team.collision.pushOwnTeam": "Ütközés saját csapattagokkal", "team.notFound": "Ismeretlen csapat: '%s'", "team.visibility.always": "<PERSON><PERSON>", "team.visibility.hideForOtherTeams": "Elrejtés a többi csapat elől", "team.visibility.hideForOwnTeam": "Elrejtés a saját csapat elől", "team.visibility.never": "<PERSON><PERSON>", "telemetry.event.advancement_made.description": "Az előrelépés megszerzésének körülményeire rálátva jobban megérthetjük és javíthatjuk a játékmenetet.", "telemetry.event.advancement_made.title": "Előrelépés teljesítve", "telemetry.event.game_load_times.description": "Ez az esemény segíthet nekünk <PERSON>ni, hol szükséges javítanunk a játékindítás folyamatának hatékonyságán, az egyes indítási fázisok futási idejének mérése által.", "telemetry.event.game_load_times.title": "Játékindítások időtartama", "telemetry.event.optional": "%s (nem kötelez<PERSON>)", "telemetry.event.optional.disabled": "%s (nem k<PERSON>) – Kikapcsolva", "telemetry.event.performance_metrics.description": "A Minecraft általános teljesítményprofiljának ismerete segít nekünk a gépspecifikációk és operációs rendszerek széles skálájához finomítani és optimalizálni a játékot.\nA játékverzió azért van feltüntetve, hogy segítsen összehasonlítani a Minecraft új verzióinak teljesítményprofilját.", "telemetry.event.performance_metrics.title": "Teljesítmény adatok", "telemetry.event.required": "%s (Szükséges)", "telemetry.event.world_load_times.description": "<PERSON>ont<PERSON>, mennyi ideig tart csatlakozni egy világhoz, és ez idővel hogyan változik. Amikor például új funkciókat vagy nagyobb technikai változásokat implementálunk, <PERSON><PERSON><PERSON><PERSON><PERSON> kell, hogy ezek milyen hatással voltak a betöltési időkre.", "telemetry.event.world_load_times.title": "Világbetöltések időtartama", "telemetry.event.world_loaded.description": "<PERSON> tud<PERSON>, hogy<PERSON> j<PERSON> a játékosok a Minecrafttal (mint például játékmód, modolt kliens vagy s<PERSON>, és játékverzió), az lehetővé teszi sz<PERSON>, hogy a játékosok számára legfontosabb területekre fókuszáljuk a játékfrissítéseket.\nA Betöltött Világ esemény párosítva van a Világ Elhagyva eseménnyel, hogy kiszámítsa, mennyi ideig tartott a játékmenet.", "telemetry.event.world_loaded.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.event.world_unloaded.description": "Ez az esemény párosítva van a Betöltött Világ eseménnyel azért, hogy kiszámítsuk, meddig tartott az adott világban töltött játékmenet.\nAz időtartamát (másodpercekben és tickekben) akkor rögzítjük, amikor a világban töltött játékmenet véget ért (kilépés a főmenübe, szerver elhagyása).", "telemetry.event.world_unloaded.title": "Vil<PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (tickben)", "telemetry.property.advancement_id.title": "Előrelépés azonosítója", "telemetry.property.client_id.title": "Kliensazonosító", "telemetry.property.client_modded.title": "Módosított k<PERSON>ns", "telemetry.property.dedicated_memory_kb.title": "Lefoglalt memória (kB)", "telemetry.property.event_timestamp_utc.title": "Esemény <PERSON> (UTC)", "telemetry.property.frame_rate_samples.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON><PERSON>kmó<PERSON>", "telemetry.property.game_version.title": "Játék verziója", "telemetry.property.launcher_name.title": "Indító neve", "telemetry.property.load_time_bootstrap_ms.title": "Indítási <PERSON>ő (milliszekundumban)", "telemetry.property.load_time_loading_overlay_ms.title": "Betöltőképernyőn töltött idő (milliszekundumban)", "telemetry.property.load_time_pre_window_ms.title": "Ablak megjelenéséig eltelt <PERSON>ő (milliszekundumban)", "telemetry.property.load_time_total_time_ms.title": "Teljes betöltési <PERSON>ő (milliszekundumban)", "telemetry.property.minecraft_session_id.title": "Minecraft-játékmenet azonosítója", "telemetry.property.new_world.title": "<PERSON>j világ", "telemetry.property.number_of_samples.title": "A minták száma", "telemetry.property.operating_system.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.property.opt_in.title": "Felhasz<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.platform.title": "Platform", "telemetry.property.realms_map_content.title": "Realms-v<PERSON><PERSON>g tartalma (minijáték neve)", "telemetry.property.render_distance.title": "Látótávolság", "telemetry.property.render_time_samples.title": "Megjelenítés időigénye", "telemetry.property.seconds_since_load.title": "Betöltés óta eltel<PERSON> idő (másodpercben)", "telemetry.property.server_modded.title": "Módos<PERSON><PERSON><PERSON>", "telemetry.property.server_type.title": "Szervertípus", "telemetry.property.ticks_since_load.title": "Betöltés óta el<PERSON>t idő (tickben)", "telemetry.property.used_memory_samples.title": "Felhasznált RAM", "telemetry.property.user_id.title": "Felhasználói azonosító", "telemetry.property.world_load_time_ms.title": "Világ betöltési ideje (milliszekundumban)", "telemetry.property.world_session_id.title": "Játékmenet ID", "telemetry_info.button.give_feedback": "Visszajelzés küldése", "telemetry_info.button.privacy_statement": "Adatvédelmi <PERSON>", "telemetry_info.button.show_data": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "telemetry_info.opt_in.description": "Beleegyezem az opcionális telemetriai adatok küldésébe", "telemetry_info.property_title": "Hozzáadott adat", "telemetry_info.screen.description": "Ezen adatok gyűjtése segít nekünk a Minecraft fejlesztésében azáltal, hogy a játékosaink számára releváns irányokba vezetnek minket.\nTovábbi v<PERSON>t is k<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy segíts nekünk a Minecraft továbbfejlesztésében.", "telemetry_info.screen.title": "Telemetriai adatgyűjtés", "test.error.block_property_mismatch": "Elv<PERSON>rt tulajdonság ehhez: %s, %s volt de ezt kaptuk: %s", "test.error.block_property_missing": "Hiányzó blokk tulajdonság, a(z) %s tulajdonság elvárt értéke %s", "test.error.entity_property": "%s entitáns megbukott a kísérleten: %s", "test.error.entity_property_details": "%s entitáns megbukott a kísérleten: %s, elvárt: %s, kapott:%s", "test.error.expected_block": "Az elvárás %s blokk volt de %s blokkot kaptunk", "test.error.expected_block_tag": "Számított egy blokkra a #%s-ben, ehelyett %s-t kapott", "test.error.expected_container_contents": "A tárolónak tartalmaznia kell ezt: %s", "test.error.expected_container_contents_single": "A tárolónak tartalmaznia kell egy darabot ebből: %s", "test.error.expected_empty_container": "A tárolónak üresnek kellene lennie", "test.error.expected_entity": "Hiányzó %s", "test.error.expected_entity_around": "Számított %s létezésére %s, %s és %s körül", "test.error.expected_entity_count": "Számított arra, hogy %s egy %s fajta entitás lesz, eh<PERSON>ett %s-t kapott", "test.error.expected_entity_data": "Számított entitás adatoknak egyenlőnek kellene lennie %s-vel, eh<PERSON>ett %s-vel egyenlőek", "test.error.expected_entity_data_predicate": "Entitás adatok nem helyesek %s számára", "test.error.expected_entity_effect": "Számított arra, hogy %s-nek meglegyen a %s effekt %s-es erősséggel", "test.error.expected_entity_having": "Entitás tárolójában kellene %s-nek lennie", "test.error.expected_entity_holding": "Entitásnak %s-et kellene tartania a kezében", "test.error.expected_entity_in_test": "Számított arra, hogy %s létezni fog a tesztben", "test.error.expected_entity_not_touching": "<PERSON>em <PERSON> arra, hogy %s hozzáér a %s %s %s koordinátákhoz (relatívan: %s %s %s koordináták)", "test.error.expected_entity_touching": "Számított arra, hogy %s hozzáér a %s %s %s koordinátákhoz (relatívan: %s %s %s koordináták)", "test.error.expected_item": "Számított %s fajta tárgyra", "test.error.expected_items_count": "Számított %s %s tárgyra, ehelyett %s-t kapott", "test.error.fail": "<PERSON>ba j<PERSON>", "test.error.invalid_block_type": "Váratlan blokk típus észlelve: %s", "test.error.missing_block_entity": "Hián<PERSON><PERSON><PERSON> blo<PERSON>", "test.error.position": "%s a %s %s %s (relatívan: %s %s %s) koordinátákon a %s-ed/adik ticken", "test.error.sequence.condition_already_triggered": "Feltételek már észlelve lettek %s-kor", "test.error.sequence.condition_not_triggered": "<PERSON>em tel<PERSON>ü<PERSON> a feltétel", "test.error.sequence.invalid_tick": "Az elfogadás érvénytelen tickben történt %s helyett", "test.error.sequence.not_completed": "A teszt tullépte az időkorlátot a vége előtt", "test.error.set_biome": "A biome beállítása sikertelen volt", "test.error.spawn_failure": "Nem <PERSON> létrehozni %s entitást", "test.error.state_not_equal": "Helytelen állapot: a várt %s hely<PERSON> %s", "test.error.structure.failure": "<PERSON>em <PERSON> letenni a strúktúrát %s-nek", "test.error.tick": "%s a(z) %s. tickben", "test.error.ticking_without_structure": "Teszt tickelve a struktúra lehelyezése előtt", "test.error.timeout.no_result": "Sem siker sem hiba nem történt %s ticken belül", "test.error.timeout.no_sequences_finished": "Se<PERSON>i sze<PERSON> nem teljesült %s ticken belül", "test.error.too_many_entities": "Csak egy %s létezésére számított a %s %s %s koorsdinátákon de %s-et talált", "test.error.unexpected_block": "<PERSON>em <PERSON> arra, hogy a block %s", "test.error.unexpected_entity": "Nem számított %s létezésére", "test.error.unexpected_item": "Nem számított %s fajtájú tárgyra", "test.error.unknown": "Ismeretlen belső hiba: %s", "test.error.value_not_equal": "Számított arra, hogy %s %s lesz, de %s lett", "test.error.wrong_block_entity": "<PERSON>em me<PERSON> blokkentitás-típus: %s", "test_block.error.missing": "A tesztstruktúrából hiányzik egy %s blokk", "test_block.error.too_many": "Túl sok %s blokk", "test_block.invalid_timeout": "Érvénytelen <PERSON>lát (%s) - a tickek számának pozitívnak kell lennie", "test_block.message": "Üzenet:", "test_block.mode.accept": "Elfogadás", "test_block.mode.fail": "Elutasítás", "test_block.mode.log": "Naplózás", "test_block.mode.start": "Indítás", "test_block.mode_info.accept": "Elfogadás mód - teszt (részének) elfogadása si<PERSON>ént", "test_block.mode_info.fail": "Elutasítás mód - teszt elutasítása sikertelenként", "test_block.mode_info.log": "Naplózás mód - üzenet naplófájlba írása", "test_block.mode_info.start": "Indítás mód - a teszt kezdőpontja", "test_instance.action.reset": "Visszaállítás és betöltés", "test_instance.action.run": "Betöltés és futtatás", "test_instance.action.save": "<PERSON>ruk<PERSON><PERSON><PERSON>", "test_instance.description.batch": "Halom: %s", "test_instance.description.failed": "Megbukott: %s", "test_instance.description.function": "Eljárás: %s", "test_instance.description.invalid_id": "Érvénytelen tesztazonosító", "test_instance.description.no_test": "Nincs ilyen teszt", "test_instance.description.structure": "Struktúra: %s", "test_instance.description.type": "Fajta %s", "test_instance.type.block_based": "Blokk-Alapú Tesztelés", "test_instance.type.function": "Beépített Funkció <PERSON>", "test_instance_block.entities": "Entitások:", "test_instance_block.error.no_test": "Képtelen futattni a kísérleti alkalmat itt: %s, %s, %s, mert meghatározatlan kísérletet tartalmaz", "test_instance_block.error.no_test_structure": "A(z) %s, %s, %s koordinátákon található tesztpéldány nem futtatható, mivel nincs tesztstruktúrája", "test_instance_block.error.unable_to_save": "<PERSON><PERSON> menteni a kísérleti struktúrát a kísérleti alkalomhoz itt: %s, %s, %s", "test_instance_block.invalid": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]", "test_instance_block.reset_success": "Sikeres újraindítás a kísérlethez: %s", "test_instance_block.rotation": "Elforgatás:", "test_instance_block.size": "Teszt Struktúra Mérete", "test_instance_block.starting": "%s teszt indítása", "test_instance_block.test_id": "Tesztpéldány-azonosító", "title.32bit.deprecation": "32 bites rendszer észlelve: ez k<PERSON><PERSON><PERSON> megg<PERSON><PERSON><PERSON><PERSON> a j<PERSON><PERSON><PERSON>, mert a működéshez 64 bites rendszer lesz szükséges!", "title.32bit.deprecation.realms": "A Minecraft működéséhez hamarosan 64 bites re<PERSON><PERSON><PERSON> lesz szükség, ami ki fogja zárni a játékot és a Realms használatát ezen az eszközön. Ha van Realms-előfizetésed, azt manu<PERSON><PERSON><PERSON> kell lemondanod.", "title.32bit.deprecation.realms.check": "Ne mutassa újra ezt az üzenetet", "title.32bit.deprecation.realms.header": "32 bites re<PERSON><PERSON> észlelve", "title.credits": "Copyright Mojang AB. Do not distribute!", "title.multiplayer.disabled": "A többjátékos mód le <PERSON> tilt<PERSON>. Ellenőrizze a Microsoft-fiókjának beállításait.", "title.multiplayer.disabled.banned.name": "<PERSON> kell változtatnod a neved, hogy online játszhass", "title.multiplayer.disabled.banned.permanent": "A fiókodon véglegesen fel lett függesztve az online játék", "title.multiplayer.disabled.banned.temporary": "A fiókodon ideiglenesen fel lett függesztve az online játék", "title.multiplayer.lan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "Többjátékos (Szerver harmadik féltől)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "Egyjátékos mód", "translation.test.args": "%s %s", "translation.test.complex": "Előtag, %s%2$s újra %s és %1$s végül %s és %1$s is megint!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "szia %", "translation.test.invalid2": "szia %s", "translation.test.none": "Helló, világ!", "translation.test.world": "vil<PERSON><PERSON>", "trim_material.minecraft.amethyst": "Ametisztből készült", "trim_material.minecraft.copper": "Rézből készült", "trim_material.minecraft.diamond": "Gyémántból készült", "trim_material.minecraft.emerald": "Smaragdból készült", "trim_material.minecraft.gold": "Aranyból készült", "trim_material.minecraft.iron": "Vasból készült", "trim_material.minecraft.lapis": "Lazuritból készült", "trim_material.minecraft.netherite": "Netheritből készült", "trim_material.minecraft.quartz": "Kvarcból készült", "trim_material.minecraft.redstone": "Vöröskőből készült", "trim_material.minecraft.resin": "Gyantából készült", "trim_pattern.minecraft.bolt": "Szegecselt páncéldísz", "trim_pattern.minecraft.coast": "Tengerparti <PERSON>", "trim_pattern.minecraft.dune": "Dűne-páncéldísz", "trim_pattern.minecraft.eye": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.flow": "Örvény-pán<PERSON><PERSON>", "trim_pattern.minecraft.host": "Gazdaúri <PERSON>", "trim_pattern.minecraft.raiser": "<PERSON><PERSON><PERSON><PERSON> páncéld<PERSON>", "trim_pattern.minecraft.rib": "<PERSON>rd<PERSON> p<PERSON>", "trim_pattern.minecraft.sentry": "Őrségi pán<PERSON><PERSON><PERSON>z", "trim_pattern.minecraft.shaper": "Alakító <PERSON>", "trim_pattern.minecraft.silence": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.snout": "Disznóorros p<PERSON>", "trim_pattern.minecraft.spire": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.tide": "Hullámos <PERSON>", "trim_pattern.minecraft.vex": "Bosszancs-páncéldísz", "trim_pattern.minecraft.ward": "Strázsa-páncéldísz", "trim_pattern.minecraft.wayfinder": "Vándor-p<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.wild": "<PERSON><PERSON>", "tutorial.bundleInsert.description": "Jobb egérgomb a tárgyak hozzáadásához", "tutorial.bundleInsert.title": "Hasz<PERSON><PERSON>j egy batyut", "tutorial.craft_planks.description": "A receptkönyv segíthet", "tutorial.craft_planks.title": "Barkácsolj deszkákat", "tutorial.find_tree.description": "Üsd meg, hogy fát g<PERSON>", "tutorial.find_tree.title": "<PERSON><PERSON> egy f<PERSON>t", "tutorial.look.description": "<PERSON>z<PERSON><PERSON> az egeret", "tutorial.look.title": "<PERSON><PERSON><PERSON>", "tutorial.move.description": "%s: ugr<PERSON>", "tutorial.move.title": "%s, %s, %s és %s: mozgás", "tutorial.open_inventory.description": "a(z) %s gombbal", "tutorial.open_inventory.title": "Nyisd meg az eszköztárad", "tutorial.punch_tree.description": "a(z) %s nyomva tartásával", "tutorial.punch_tree.title": "Üsd ki a fát", "tutorial.socialInteractions.description": "%s a megnyitáshoz", "tutorial.socialInteractions.title": "Közösségi interakciók", "upgrade.minecraft.netherite_upgrade": "<PERSON><PERSON><PERSON> fej<PERSON>"}