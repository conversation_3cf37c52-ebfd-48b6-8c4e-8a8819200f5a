{"accessibility.onboarding.accessibility.button": "Zugänglichkeits-Eistellunga...", "accessibility.onboarding.screen.narrator": "Auf endder drüggn um an vorschwafler zu aggdiviern", "accessibility.onboarding.screen.title": "Willkomma bei Meingraft!\nWillsd an vorschwafler aggdiviern oder mechst in die barrierefreihaddsfunktiona?", "addServer.add": "Passd", "addServer.enterIp": "Seavaadresse", "addServer.enterName": "Seavanome", "addServer.resourcePack": "Seava-Ressourcnbagede", "addServer.resourcePack.disabled": "Deaggdivierd", "addServer.resourcePack.enabled": "Aggdivierd", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addServer.title": "Seavainfoss beabbeidn", "advMode.command": "Bfehl", "advMode.mode": "Modus", "advMode.mode.auto": "Wiedaholn", "advMode.mode.autoexec.bat": "Imma agdiv", "advMode.mode.conditional": "Bdingd", "advMode.mode.redstone": "Impuls", "advMode.mode.redstoneTriggered": "Bnödigd Redstone", "advMode.mode.sequence": "Keddn", "advMode.mode.unconditional": "Unbdingd", "advMode.notAllowed": "Nua a Oberador im Kreativ-Modus koann Bfehle eingebn", "advMode.notEnabled": "Bfehlsblögge san oaf diesn Serva ned eingschalded", "advMode.previousOutput": "Letzde Ausgabn", "advMode.setCommand": "Bfehl eingebn", "advMode.setCommand.success": "Bfehl gsetzd: %s", "advMode.trackOutput": "Ausgabn verfolchn", "advMode.triggering": "Auslösn", "advMode.type": "<PERSON><PERSON>", "advancement.advancementNotFound": "Unb`gannda Foadschridd: %s", "advancements.adventure.adventuring_time.description": "Enddeck alle Biome", "advancements.adventure.adventuring_time.title": "Abendeuazaid", "advancements.adventure.arbalistic.description": "<PERSON><PERSON><PERSON> fünf undaschiedliche Greaduan mid nua anem Aambrusdschuss", "advancements.adventure.arbalistic.title": "Aambrusdschüdzn", "advancements.adventure.avoid_vibration.description": "Schlaich neba am Sculk Sensor oder am Wächta um ned erwischd zu weddn", "advancements.adventure.avoid_vibration.title": "<PERSON><PERSON><PERSON><PERSON> 100", "advancements.adventure.blowback.description": "Kill a Breeze with a deflected Breeze-shot <PERSON> Charge", "advancements.adventure.blowback.title": "Blowback", "advancements.adventure.brush_armadillo.description": "Get Armadillo Scutes from an Armadillo using a Brush", "advancements.adventure.brush_armadillo.title": "Isn't It Scute?", "advancements.adventure.bullseye.description": "Triff die Middn eines Zielblocks aus mindesdns 30 Blöckn Endfernung", "advancements.adventure.bullseye.title": "Volldreff<PERSON>", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Baschdl a deggorierde Vasn aus 4 Scherbe", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Sorgfäldigge Widderherrichtung", "advancements.adventure.crafters_crafting_crafters.description": "Be near a Crafter when it crafts a Crafter", "advancements.adventure.crafters_crafting_crafters.title": "Crafters Crafting Crafters", "advancements.adventure.fall_from_world_height.description": "St<PERSON>rtz im freie Fall vo der Weldobergränz (die maximale Bauhöhe) bis zur Weldundergränz und überleb des", "advancements.adventure.fall_from_world_height.title": "H<PERSON>hlen un Klibben", "advancements.adventure.heart_transplanter.description": "Place a Creaking Heart with the correct alignment between two Pale Oak Log blocks", "advancements.adventure.heart_transplanter.title": "Heart Transplanter", "advancements.adventure.hero_of_the_village.description": "Veadeidiche a Doaf eafolgraich von nem Übafall", "advancements.adventure.hero_of_the_village.title": "<PERSON> vom Dorf", "advancements.adventure.honey_block_slide.description": "<PERSON><PERSON><PERSON><PERSON> an nem Honichblogg, um dein Stuaz abzufangn", "advancements.adventure.honey_block_slide.title": "A klebriche Anglegnhaid", "advancements.adventure.kill_a_mob.description": "Ealech a Monsda", "advancements.adventure.kill_a_mob.title": "Monsdajächa", "advancements.adventure.kill_all_mobs.description": "<PERSON><PERSON><PERSON> jedes Mon<PERSON>da mindesdns een Mal", "advancements.adventure.kill_all_mobs.title": "Maidajächa", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Ealech a Greadua neba am Sculk Katalisaddor", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "<PERSON><PERSON> breid sich as", "advancements.adventure.lighten_up.description": "Scrape a Copper Bulb with an Axe to make it brighter", "advancements.adventure.lighten_up.title": "Lighten Up", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "B's<PERSON><PERSON><PERSON> an Dorfb'wohner vor am unärwardedem Schogg, ohne doabei an Brand auszulöse", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Spoanungsschudz", "advancements.adventure.minecraft_trials_edition.description": "Step foot in a Trial Chamber", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Trial(s) Edition", "advancements.adventure.ol_betsy.description": "Feuer a Aambruschd oab", "advancements.adventure.ol_betsy.title": "Ich hoab die Knarrn gsichad", "advancements.adventure.overoverkill.description": "Deal 50 hearts of damage in a single hit using the Mace", "advancements.adventure.overoverkill.title": "Over-Overkill", "advancements.adventure.play_jukebox_in_meadows.description": "Bring Lebe in die Oalm mid dem Klong der Mussig aus am Pladdenspieler", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Liesd a Powersignal vo a gmeißeldn Büchagstell mid an Vergleicha", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "<PERSON>ht von Büchern", "advancements.adventure.revaulting.description": "Unlock an Ominous Vault with an Ominous Trial Key", "advancements.adventure.revaulting.title": "Revaulting", "advancements.adventure.root.description": "Abendeua, Eafoaschung und Koampf", "advancements.adventure.root.title": "Abendeua", "advancements.adventure.salvage_sherd.description": "<PERSON>ürste einen komischen Block um eine Vasenscherbe zu bekommen", "advancements.adventure.salvage_sherd.title": "Respektiern vo de Rest", "advancements.adventure.shoot_arrow.description": "Triff a <PERSON>ur mid am Pfeil", "advancements.adventure.shoot_arrow.title": "Zielübung", "advancements.adventure.sleep_in_bed.description": "<PERSON><PERSON><PERSON> in am Bed, um dan Wiedaeinstiegspunkd zu ändan", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON> was <PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.sniper_duel.description": "Ealech a Sgledd aus mindesdns 50 Blöggn Endfeanung", "advancements.adventure.sniper_duel.title": "Schaafschütznduell", "advancements.adventure.spyglass_at_dragon.description": "<PERSON><PERSON><PERSON> dir oeinen Endadrochn durch an Färnglass on", "advancements.adventure.spyglass_at_dragon.title": "Ischd esch ein Fluggzeug?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON><PERSON><PERSON> dir an <PERSON>d durch a Färnglass o", "advancements.adventure.spyglass_at_ghast.title": "Ischd esch ein <PERSON>?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON><PERSON><PERSON> dir oeinen Papagoi durch oin Färnglass on", "advancements.adventure.spyglass_at_parrot.title": "Ischd esch ein F<PERSON>gl?", "advancements.adventure.summon_iron_golem.description": "<PERSON><PERSON> an <PERSON>, um bei dea Veadeidichung eines Doafs midzuhelfn", "advancements.adventure.summon_iron_golem.title": "Das 1. <PERSON><PERSON><PERSON> <PERSON><PERSON>", "advancements.adventure.throw_trident.description": "<PERSON>ia<PERSON> an Draizagg oaf irchndwas.\nHinwoas: E<PERSON> is nie a guda <PERSON>, deine einziche Waffn wechzuweafn.", "advancements.adventure.throw_trident.title": "A Witz zum Wechweafn", "advancements.adventure.totem_of_undying.description": "<PERSON><PERSON>e oan Dodem där Unstärblichkoeit, um dem Dod voan där Schibbe zu springe", "advancements.adventure.totem_of_undying.title": "Na<PERSON>m Dod", "advancements.adventure.trade.description": "<PERSON><PERSON><PERSON><PERSON> an Handl mid am Doafbwohna oab", "advancements.adventure.trade.title": "Was füa Gschäfd!", "advancements.adventure.trade_at_world_height.description": "Handle mid am Doafbewohna af där moxima<PERSON>n", "advancements.adventure.trade_at_world_height.title": "Schdärnh<PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Benutz di Schmidn-Vorlag mindestens oamoi: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ward, Stümm, Vex, Flut, Wegfinna", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Schmieden mit Style", "advancements.adventure.trim_with_any_armor_pattern.description": "<PERSON><PERSON><PERSON> a z'schniddns G'wand an da <PERSON><PERSON><PERSON>-<PERSON>l", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON>ia <PERSON> zammabasteln", "advancements.adventure.two_birds_one_arrow.description": "Ealech zwoa Phandome mit am Duachschuss", "advancements.adventure.two_birds_one_arrow.title": "Zwoa oaf an Schlach", "advancements.adventure.under_lock_and_key.description": "Unlock a Vault with a Trial Key", "advancements.adventure.under_lock_and_key.title": "Under Lock and Key", "advancements.adventure.use_lodestone.description": "Use a Compass on a Lodestone", "advancements.adventure.use_lodestone.title": "Country Lode, Take Me Home", "advancements.adventure.very_very_frightening.description": "Driff an Doefbewohne mid am Blidz", "advancements.adventure.very_very_frightening.title": "Oangsd und Schreggn", "advancements.adventure.voluntary_exile.description": "Död an Räubahaubdmann.\n<PERSON><PERSON><PERSON> mögli<PERSON>wei<PERSON>n in Bedrachd, dich voa<PERSON><PERSON> von <PERSON><PERSON> feanzu<PERSON>dn...", "advancements.adventure.voluntary_exile.title": "Freiwilliches Exil", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Wandern afm Pulverschnee... ohne ei'z'sinken", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON>d wie oin <PERSON>", "advancements.adventure.who_needs_rockets.description": "Use a Wind Charge to launch yourself upward 8 blocks", "advancements.adventure.who_needs_rockets.title": "Who Needs Rockets?", "advancements.adventure.whos_the_pillager_now.description": "Gib an Blündera a Browierala seina eignen Medizin", "advancements.adventure.whos_the_pillager_now.title": "Wea is jetzadla dea Blündera?", "advancements.empty": "<PERSON><PERSON> scheind nix zu sein...", "advancements.end.dragon_breath.description": "Sam<PERSON><PERSON> Drachnatem in a Glosflaschn", "advancements.end.dragon_breath.title": "Du brauchsd a Pfeffaminz", "advancements.end.dragon_egg.description": "<PERSON><PERSON><PERSON> das Drachnei", "advancements.end.dragon_egg.title": "Die näxde Genaration", "advancements.end.elytra.description": "<PERSON>", "advancements.end.elytra.title": "Hindam Horizond geh<PERSON>'s weida", "advancements.end.enter_end_gateway.description": "Endkomm dea Insl", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON>", "advancements.end.find_end_city.description": "Geh schon rain, was koann schon bassiean?", "advancements.end.find_end_city.title": "Die Stoad am Ende des Schbiels", "advancements.end.kill_dragon.description": "<PERSON><PERSON>", "advancements.end.kill_dragon.title": "<PERSON><PERSON><PERSON><PERSON> das Ende", "advancements.end.levitate.description": "Schweb duach a Shulkagschoss 50 Blögge rauf", "advancements.end.levitate.title": "<PERSON><PERSON><PERSON><PERSON> von <PERSON> obn", "advancements.end.respawn_dragon.description": "<PERSON><PERSON> den Enderdrachn ins Lebn zrück", "advancements.end.respawn_dragon.title": "<PERSON> ... <PERSON><PERSON><PERSON> wieda ...", "advancements.end.root.description": "... oda dea <PERSON>?", "advancements.end.root.title": "<PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "<PERSON><PERSON> an <PERSON> an <PERSON>chn auf an Nodnblogg falln", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Burtstagsständle", "advancements.husbandry.allay_deliver_item_to_player.description": "Lass an <PERSON>ay dir wos liefern", "advancements.husbandry.allay_deliver_item_to_player.title": "<PERSON> hasd an Gumbel", "advancements.husbandry.axolotl_in_a_bucket.description": "<PERSON> an Aggsolodl in am Amer", "advancements.husbandry.axolotl_in_a_bucket.title": "<PERSON><PERSON>", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON> <PERSON>, sel<PERSON>d wenn es ned guad füa dich is", "advancements.husbandry.balanced_diet.title": "Ausgwochne Eanährung", "advancements.husbandry.breed_all_animals.description": "Veamea all Dieradn!", "advancements.husbandry.breed_all_animals.title": "Baarwoaß", "advancements.husbandry.breed_an_animal.description": "Veameha zwo<PERSON> Diere", "advancements.husbandry.breed_an_animal.title": "Die Hühnchn und die Blümchn", "advancements.husbandry.complete_catalogue.description": "<PERSON><PERSON><PERSON> alle Katzenrassn!", "advancements.husbandry.complete_catalogue.title": "A kombledda Kadaloch", "advancements.husbandry.feed_snifflet.description": "<PERSON> füttern", "advancements.husbandry.feed_snifflet.title": "<PERSON><PERSON>", "advancements.husbandry.fishy_business.description": "<PERSON><PERSON> an <PERSON>", "advancements.husbandry.fishy_business.title": "Anglaglück", "advancements.husbandry.froglights.description": "Hab <PERSON><PERSON> in dam Beidel", "advancements.husbandry.froglights.title": "Mit unsarn vereindn Gräften!", "advancements.husbandry.kill_axolotl_target.description": "Verbünde dich mid enem Oxolotl und g`winne dän <PERSON>f", "advancements.husbandry.kill_axolotl_target.title": "Die Hoilendä Krofd där Freundschafd!", "advancements.husbandry.leash_all_frog_variants.description": "<PERSON><PERSON> jede Froschart an die Laane", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON>n die Gradla ins Dorf komma", "advancements.husbandry.make_a_sign_glow.description": "Lass die Schrifd an jede Ord von <PERSON> leuchtn", "advancements.husbandry.make_a_sign_glow.title": "<PERSON><PERSON><PERSON> und glotze!", "advancements.husbandry.netherite_hoe.description": "Benutz an Nedderid Barrn um a Sense zu rebariern, und deng nomma nach wos du grad gmacht hasd", "advancements.husbandry.netherite_hoe.title": "Maine größde <PERSON>fd", "advancements.husbandry.obtain_sniffer_egg.description": "<PERSON><PERSON><PERSON> ein Schnüffler Ei", "advancements.husbandry.obtain_sniffer_egg.title": "Des riecht interessant", "advancements.husbandry.place_dried_ghast_in_water.description": "Place a Dried Ghast block into water", "advancements.husbandry.place_dried_ghast_in_water.title": "Stay Hydrated!", "advancements.husbandry.plant_any_sniffer_seed.description": "Setz a Sniffer-Saat aus", "advancements.husbandry.plant_any_sniffer_seed.title": "<PERSON> pflanzen", "advancements.husbandry.plant_seed.description": "S<PERSON>a Sam<PERSON> und schau ihnen beim Wachsn zua", "advancements.husbandry.plant_seed.title": "Hobbxgäadna", "advancements.husbandry.remove_wolf_armor.description": "Remove Wolf Armor from a Wolf using Shears", "advancements.husbandry.remove_wolf_armor.title": "Shear Brilliance", "advancements.husbandry.repair_wolf_armor.description": "Fully repair damaged Wolf Armor using Armadillo Scutes", "advancements.husbandry.repair_wolf_armor.title": "Good as New", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Steig` in oin Bood und moch die Fliege mid när Ziege", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Wasch auch immer deinen Go<PERSON>d durchfliesd!", "advancements.husbandry.root.description": "Die Weld is volla Freunde und Essn", "advancements.husbandry.root.title": "Landwiadschafd", "advancements.husbandry.safely_harvest_honey.description": "Benutz a Lagerfeia, um mit am Glasflaschn Honig aus am Bienenstock z'holn, ohne de Bään zu nerva", "advancements.husbandry.safely_harvest_honey.title": "Ssssei unsa Gasd", "advancements.husbandry.silk_touch_nest.description": "<PERSON><PERSON><PERSON> an Bienastogg, mit 3 <PERSON><PERSON><PERSON> drinna, mit <PERSON><PERSON><PERSON>amkai<PERSON>", "advancements.husbandry.silk_touch_nest.title": "Bienenwandarung", "advancements.husbandry.tactical_fishing.description": "<PERSON> an Fisch... ohne a Angel!", "advancements.husbandry.tactical_fishing.title": "Dakdisches Fischn", "advancements.husbandry.tadpole_in_a_bucket.description": "Fang a Kaulkwabbn in am Amer", "advancements.husbandry.tadpole_in_a_bucket.title": "<PERSON><PERSON>", "advancements.husbandry.tame_an_animal.description": "<PERSON><PERSON><PERSON>", "advancements.husbandry.tame_an_animal.title": "Allabesde Freunde", "advancements.husbandry.wax_off.description": "Grad<PERSON> das Woax von einem Kubfarblogg ab!", "advancements.husbandry.wax_off.title": "Abg`waxd", "advancements.husbandry.wax_on.description": "Woch<PERSON> oinem Kubfarblogg mid oina Honigwahrbä!", "advancements.husbandry.wax_on.title": "Uffg`waxd", "advancements.husbandry.whole_pack.description": "Tame one of each Wolf variant", "advancements.husbandry.whole_pack.title": "The Whole Pack", "advancements.nether.all_effects.description": "<PERSON><PERSON> jedn Staduseffekd gleichzeidich", "advancements.nether.all_effects.title": "Wie habn wia des gschaffd?", "advancements.nether.all_potions.description": "<PERSON><PERSON> jedn Drankeffekd gleichzeidich", "advancements.nether.all_potions.title": "A Gfäaliche Mischung", "advancements.nether.brew_potion.description": "<PERSON><PERSON><PERSON> an <PERSON>", "advancements.nether.brew_potion.title": "Alchemie", "advancements.nether.charge_respawn_anchor.description": "Lad an Seelnanka oaf die höchsde Stufn oaf", "advancements.nether.charge_respawn_anchor.title": "Net ganz \"siebn\" Lebn", "advancements.nether.create_beacon.description": "Bau und agdivia an Leuchdfeua", "advancements.nether.create_beacon.title": "<PERSON> Na<PERSON> ha<PERSON>dn", "advancements.nether.create_full_beacon.description": "Bring a Leuchdfeua auf volle Leisdung", "advancements.nether.create_full_beacon.title": "Leuchdduamwäada", "advancements.nether.distract_piglin.description": "Lenk <PERSON> mid Goid oab", "advancements.nether.distract_piglin.title": "S’ glanzet", "advancements.nether.explore_nether.description": "Enddeck oal Netherbiome", "advancements.nether.explore_nether.title": "Hoaßbgehrdn Roaseziele", "advancements.nether.fast_travel.description": "<PERSON>utz den Netha, um 7 km in dea Obaweld zu reisn", "advancements.nether.fast_travel.title": "Subraumdransboad", "advancements.nether.find_bastion.description": "Bedridd a Bastionsruin", "advancements.nether.find_bastion.title": "Das warn noch Zeitn", "advancements.nether.find_fortress.description": "<PERSON><PERSON> in a Netherfesdung ein", "advancements.nether.find_fortress.title": "A schreckliche Fesdung", "advancements.nether.get_wither_skull.description": "<PERSON><PERSON><PERSON>", "advancements.nether.get_wither_skull.title": "<PERSON> gra<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>d", "advancements.nether.loot_bastion.description": "Plünda a Truhn aus na Bastionruina", "advancements.nether.loot_bastion.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.netherite_armor.description": "Bsorch dir ene kompledde Netheritrüsdung", "advancements.nether.netherite_armor.title": "Bdeck mi <PERSON>", "advancements.nether.obtain_ancient_debris.description": "<PERSON>schaff <PERSON><PERSON>ott", "advancements.nether.obtain_ancient_debris.title": "Veaborchn in den Tiefn", "advancements.nether.obtain_blaze_rod.description": "Ealeichdere a Lohe um ihre Rudn", "advancements.nether.obtain_blaze_rod.title": "Schbiel mid dem Feua", "advancements.nether.obtain_crying_obsidian.description": "<PERSON><PERSON><PERSON><PERSON> weinended <PERSON><PERSON><PERSON>", "advancements.nether.obtain_crying_obsidian.title": "Wea schneided Zwiebln?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON><PERSON><PERSON> an <PERSON>t mid sein eignen Woaffn", "advancements.nether.return_to_sender.title": "Zrück zum Absenda", "advancements.nether.ride_strider.description": "<PERSON> an Schreida mid a Wirrpilzrudn", "advancements.nether.ride_strider.title": "<PERSON><PERSON> <PERSON><PERSON>", "advancements.nether.ride_strider_in_overworld_lava.description": "Undernimm mid oanen Strider oanen lo<PERSON>aangen Ridd uff enem <PERSON> in där Oberweld", "advancements.nether.ride_strider_in_overworld_lava.title": "Fast wie daham", "advancements.nether.root.description": "<PERSON><PERSON><PERSON> dia was <PERSON><PERSON><PERSON> <PERSON>an", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON><PERSON> an <PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "<PERSON>f<PERSON><PERSON> an <PERSON> aus'm <PERSON>, bring ihn sicha in die Obaweld ... und ealech ihn dann", "advancements.nether.uneasy_alliance.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.use_lodestone.description": "Benutz an Kombass an am Leidstoa", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON>, wo die Leidstoa stehn", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Schwäch an Zombiedoefbewohne und hail ihn dann", "advancements.story.cure_zombie_villager.title": "Zombieoazd", "advancements.story.deflect_arrow.description": "Lenk a gschoss ab mit am Schildle", "advancements.story.deflect_arrow.title": "<PERSON> heude, dankschee", "advancements.story.enchant_item.description": "Veazaubre an Gechenschdand an nem Zaubadisch", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "Bedridd das Endboadal", "advancements.story.enter_the_end.title": "<PERSON>?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON>, endzünd und bdridd a Netherboadal", "advancements.story.enter_the_nether.title": "Wia müssn noch diefa", "advancements.story.follow_ender_eye.description": "Folch am Enderoag", "advancements.story.follow_ender_eye.title": "Das fliechende Oag", "advancements.story.form_obsidian.description": "Kriech an block Obsidian", "advancements.story.form_obsidian.title": "Ice Bucket Challenge", "advancements.story.iron_tools.description": "Mach dei Ha<PERSON>n bessa", "advancements.story.iron_tools.title": "Zu viel Aasn im Bluud", "advancements.story.lava_bucket.description": "<PERSON><PERSON><PERSON> an <PERSON> mit <PERSON>a", "advancements.story.lava_bucket.title": "<PERSON><PERSON> mögn's hoaß", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "advancements.story.mine_diamond.title": "Diormandn!", "advancements.story.mine_stone.description": "<PERSON><PERSON> mit dana neun <PERSON> ob", "advancements.story.mine_stone.title": "St<PERSON><PERSON><PERSON>", "advancements.story.obtain_armor.description": "Schütz di mid am Aasnrüsdungsdeil", "advancements.story.obtain_armor.title": "<PERSON>ach dich fein", "advancements.story.root.description": "<PERSON><PERSON> und die Gschichdn des Schbiels", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Diamandnrüsdung redded Lebn", "advancements.story.shiny_gear.title": "Bdeck mich mid Diamandn", "advancements.story.smelt_iron.description": "Schmelz an Aisnbarrn", "advancements.story.smelt_iron.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.upgrade_tools.description": "Mach a bessre Hackn", "advancements.story.upgrade_tools.title": "Dechnischa Foadschridd", "advancements.toast.challenge": "Aufgabn ealedichd!", "advancements.toast.goal": "<PERSON><PERSON>d!", "advancements.toast.task": "Foadschridd gmachd!", "argument.anchor.invalid": "Ungüldige Objegd`angkerposition %s", "argument.angle.incomplete": "Unvollständich (1 Blickwinkl eawarted)", "argument.angle.invalid": "Ungülticha Winkl", "argument.block.id.invalid": "Unb`gannda Blogg: %s", "argument.block.property.duplicate": "B`sitzdum ‚%s‘ konn nur einmal für den Blogg %s g`sädzt werdän", "argument.block.property.invalid": "Blogg %s agzebdierd,%s´ fuar Bloggz`stand,%s´ nichd", "argument.block.property.novalue": "Fählendarr <PERSON>,%s` des Bloggs %s", "argument.block.property.unclosed": "Schließnde Klamma ] füa Bloggzuschdand eawarded", "argument.block.property.unknown": "Blogg %s b`sitzd dän <PERSON>stand ‚%s‘ nichd", "argument.block.tag.disallowed": "<PERSON><PERSON> sann hiea ned e<PERSON>, nur dadsächliche Blögge", "argument.color.invalid": "Unb`ganndä Farbä: %s", "argument.component.invalid": "Ungüldige Dextkombonende: %s", "argument.criteria.invalid": "Unbekanndes Kriderium '%s'", "argument.dimension.invalid": "Unb`ganndä Dimänsion: %s", "argument.double.big": "Gommazahl darf nisch gröscharr als %s soin, %s is zu grosch", "argument.double.low": "Go<PERSON><PERSON><PERSON> darf nisch gloinarr als %s sein, %s is zu gloing", "argument.entity.invalid": "Ungüldiga Noame oda ungüldige UUID", "argument.entity.notfound.entity": "<PERSON>s wuade koa Objekd gfundn", "argument.entity.notfound.player": "<PERSON>s wuade koa <PERSON> gfundn", "argument.entity.options.advancements.description": "Schbiela mid Foadschriddn", "argument.entity.options.distance.description": "Endfeanung zum Objekd", "argument.entity.options.distance.negative": "<PERSON><PERSON><PERSON>ng daaf ned negadiv soan", "argument.entity.options.dx.description": "Objegde zwischn X und X + dX", "argument.entity.options.dy.description": "Objegde zwischn Y und Y + dY", "argument.entity.options.dz.description": "Objegde zwischn Z und Z + dZ", "argument.entity.options.gamemode.description": "<PERSON><PERSON><PERSON> mit game mode", "argument.entity.options.inapplicable": "Obzion,%s` isd hier nichd oanwendbar", "argument.entity.options.level.description": "Eafohrungsschdufn", "argument.entity.options.level.negative": "Ärfahrungsstufe doarf nich negätiv soan", "argument.entity.options.limit.description": "Maximale Anzoahl zrückzuliefanda Objekde", "argument.entity.options.limit.toosmall": "Limid musch mindeschdänst 1 bätrogen", "argument.entity.options.mode.invalid": "Ungüldiga oderr unbegannda Schi`bielmodusch %s", "argument.entity.options.name.description": "Objekdnoam", "argument.entity.options.nbt.description": "Objekde mid NBT", "argument.entity.options.predicate.description": "Bnutzadefinieades Brädikad", "argument.entity.options.scores.description": "Objekde mid Punkdeschdand", "argument.entity.options.sort.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.sort.irreversible": "Ungüldiga oderr unbegannda Sordierung `%s`", "argument.entity.options.tag.description": "Objekde mid Edikett", "argument.entity.options.team.description": "Objekde im Team", "argument.entity.options.type.description": "Objekde des Tybs", "argument.entity.options.type.invalid": "Ungüldiga oderr unbegannda Objegddyb %s", "argument.entity.options.unknown": "Unb`ganndä Obzion: `%s'", "argument.entity.options.unterminated": "Schließende Klamma ] füa Filda eawarded", "argument.entity.options.valueless": "<PERSON><PERSON><PERSON> ärwarded fuar die Obzion %s", "argument.entity.options.x.description": "X-Bosition", "argument.entity.options.x_rotation.description": "Deetzneichung des Objekds", "argument.entity.options.y.description": "Y-Bosition", "argument.entity.options.y_rotation.description": "Blickrichdung des Objekds", "argument.entity.options.z.description": "Z-Bosition", "argument.entity.selector.allEntities": "Alle Objekde", "argument.entity.selector.allPlayers": "Alle Schbiela", "argument.entity.selector.missing": "Fe<PERSON><PERSON>k<PERSON>", "argument.entity.selector.nearestEntity": "Nearest entity", "argument.entity.selector.nearestPlayer": "Nächsda Schbiela", "argument.entity.selector.not_allowed": "<PERSON>le<PERSON><PERSON> ned e<PERSON>", "argument.entity.selector.randomPlayer": "Zufälliga <PERSON>", "argument.entity.selector.self": "Akduelles Objegd", "argument.entity.selector.unknown": "Unb`gannda Selegdor: %s", "argument.entity.toomany": "<PERSON>ua a Objekd is e<PERSON><PERSON>d, oaba dea angegebne Selekdor könnde mehrere liefan", "argument.enum.invalid": "Ungülticher Werd \"%s\"", "argument.float.big": "Gommazahl darf nisch gröscharr als %s soin, %s is zu grosch", "argument.float.low": "Gommazahl darf nisch gloinarr als %s soin, %s is zu gloin", "argument.gamemode.invalid": "Unbe<PERSON><PERSON> Spielmodus: %s", "argument.hexcolor.invalid": "Invalid hex color code '%s'", "argument.id.invalid": "Ungüldiche ID", "argument.id.unknown": "Unb`gannde ID: %s", "argument.integer.big": "G<PERSON><PERSON><PERSON> darf nichd größarr als %s sein, %s is zu groß", "argument.integer.low": "<PERSON><PERSON><PERSON><PERSON> darf nisch gloinorr als %s sein, %s is zu gloin", "argument.item.id.invalid": "Unbekannda Gechnschdand '%s'", "argument.item.tag.disallowed": "<PERSON><PERSON> sann hiea ned e<PERSON>, nur dadsächliche Gegnschdände", "argument.literal.incorrect": "Zoichenfolgä,%s´ ärwa`dded", "argument.long.big": "Longä Go<PERSON>hl darf nisch größorr als %s sein, %s isd zu groß", "argument.long.low": "<PERSON><PERSON>hl darf nisch gloinar als %s sein, %s isd zu gloin", "argument.message.too_long": "Chat message was too long (%s > maximum %s characters)", "argument.nbt.array.invalid": "Ungüldiga Array-Dyp,%s`", "argument.nbt.array.mixed": "%s koan nichd in %s ein`gfügd wärdn", "argument.nbt.expected.compound": "Expected compound tag", "argument.nbt.expected.key": "Eigeschafd erwa<PERSON>", "argument.nbt.expected.value": "Werd erwarded", "argument.nbt.list.mixed": "%s koan nichd in die Lischsde von%s ein`gfügd wärdn", "argument.nbt.trailing": "Unerwordede nachfolgände Dadn", "argument.player.entities": "<PERSON><PERSON> koann nua auf Sch<PERSON><PERSON> angwan<PERSON> weadn, dea angegebne Selekdor schlißd aba auch Objekde mid ein", "argument.player.toomany": "<PERSON><PERSON> s <PERSON> is e<PERSON><PERSON>d, aba dea angegebne Selekdor könnde mehrere liefan", "argument.player.unknown": "<PERSON><PERSON> exisdiead ned", "argument.pos.missing.double": "Koordinadn eawarded", "argument.pos.missing.int": "Bloggbosition eawarded", "argument.pos.mixed": "Lokale und globale Koordinadn düafn net gmischd weadn (endweda oalles mit ^ oda ohne)", "argument.pos.outofbounds": "Diese Bosition liechd außahalb des ealaubdn Braichs.", "argument.pos.outofworld": "Diese Bosition bfinded sich außahalb dea Weld!", "argument.pos.unloaded": "<PERSON><PERSON> is ned e<PERSON>ubd", "argument.pos2d.incomplete": "Unvollständich (2 Koordinaden eawarted)", "argument.pos3d.incomplete": "Unvollschdändig (3 Koordinadn eawarded)", "argument.range.empty": "<PERSON>ad o<PERSON><PERSON><PERSON><PERSON> e<PERSON>rded", "argument.range.ints": "<PERSON>ua <PERSON>n ohne Nachkommasdelln ealaubd", "argument.range.swapped": "Minimalwead darf ned größa als dea Maximalwead sein", "argument.resource.invalid_type": "Element '%s' hat den folschn Typ '%s' (erwadded '%s')", "argument.resource.not_found": "<PERSON>nn<PERSON> '%s' midm Typ '%s' ned findn", "argument.resource_or_id.failed_to_parse": "Failed to parse structure: %s", "argument.resource_or_id.invalid": "Invalid id or tag", "argument.resource_or_id.no_such_element": "Can't find element '%s' in registry '%s'", "argument.resource_selector.not_found": "No matches for selector '%s' of type '%s'", "argument.resource_tag.invalid_type": "Kennzaichn '%s' hat den falschn Typ '%s' (erwadded '%s')", "argument.resource_tag.not_found": "<PERSON><PERSON><PERSON> '%s' midm Typ '%s' ned findn", "argument.rotation.incomplete": "Unvollschdändig (2 Rodadionsangabn eawarded)", "argument.scoreHolder.empty": "<PERSON><PERSON> wuadn koane relevanden Punkdehalda gfundn", "argument.scoreboardDisplaySlot.invalid": "Unb`gannde Oanzeigeposizion: %s", "argument.style.invalid": "Ongildicha Stoi: %s", "argument.time.invalid_tick_count": "Oahnzo<PERSON> där <PERSON> doarf nich negoativ soan", "argument.time.invalid_unit": "Ungüldige Oahnheit", "argument.time.tick_count_too_low": "Tacktradn derf ned under %s sei, %s is zu kloa", "argument.uuid.invalid": "Ungültiche UUID", "argument.waypoint.invalid": "Selected entity is not a waypoint", "arguments.block.tag.unknown": "Unb`gannda Bloggtag: %s", "arguments.function.tag.unknown": "Unb`gannda Fungdionstag: %s", "arguments.function.unknown": "Unb`ganndä Fungdion: %s", "arguments.item.component.expected": "Expected item component", "arguments.item.component.malformed": "Malformed '%s' component: '%s'", "arguments.item.component.repeated": "Item component '%s' was repeated, but only one value can be specified", "arguments.item.component.unknown": "Unknown item component '%s'", "arguments.item.malformed": "Malformed item: '%s'", "arguments.item.overstacked": "%s koan nua bisch %s g`stabld wärdn", "arguments.item.predicate.malformed": "Malformed '%s' predicate: '%s'", "arguments.item.predicate.unknown": "Unknown item predicate '%s'", "arguments.item.tag.unknown": "Unbekannda Gechnschdand '%s'", "arguments.nbtpath.node.invalid": "Ungüldiges Elemend im NBT-Pfad", "arguments.nbtpath.nothing_found": "<PERSON>s wuadn koane <PERSON>e g<PERSON>ndn, die %s <PERSON>chbre<PERSON>n", "arguments.nbtpath.too_deep": "Des NBT is zu verwochsn", "arguments.nbtpath.too_large": "Des NBT is zu groß", "arguments.objective.notFound": "Unb`g<PERSON><PERSON><PERSON><PERSON>: %s", "arguments.objective.readonly": "Dasch Ziel %s konn nur oasg`läsen wärden", "arguments.operation.div0": "<PERSON><PERSON> koann ned gedeild weadn", "arguments.operation.invalid": "Ungüldige Oberation", "arguments.swizzle.invalid": "Ungüldige Achsenkombination von X, <PERSON> und Z", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s %% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s %% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s %% %s", "attribute.name.armor": "Armor", "attribute.name.armor_toughness": "<PERSON><PERSON>", "attribute.name.attack_damage": "Attack Damage", "attribute.name.attack_knockback": "Attack Knockback", "attribute.name.attack_speed": "Attack Speed", "attribute.name.block_break_speed": "Block Break Speed", "attribute.name.block_interaction_range": "Block Interaction Range", "attribute.name.burning_time": "Burning Time", "attribute.name.camera_distance": "Camera Distance", "attribute.name.entity_interaction_range": "Entity Interaction Range", "attribute.name.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.fall_damage_multiplier": "Fall Damage Multiplier", "attribute.name.flying_speed": "Flying Speed", "attribute.name.follow_range": "<PERSON>b <PERSON> Range", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Rüsdungshäaddn", "attribute.name.generic.attack_damage": "Oangriffsschadn", "attribute.name.generic.attack_knockback": "Angriffsrückstoaß", "attribute.name.generic.attack_speed": "Oangriffsgschwindichkeid", "attribute.name.generic.block_interaction_range": "Block Interaction Range", "attribute.name.generic.burning_time": "Burning Time", "attribute.name.generic.entity_interaction_range": "Entity Interaction Range", "attribute.name.generic.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.generic.fall_damage_multiplier": "Fall Damage Multiplier", "attribute.name.generic.flying_speed": "Flugg's<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.follow_range": "Greaduan-Folgedisdanz", "attribute.name.generic.gravity": "Gravity", "attribute.name.generic.jump_strength": "Jump Strength", "attribute.name.generic.knockback_resistance": "Standfesdichkeid", "attribute.name.generic.luck": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.max_absorption": "<PERSON><PERSON>", "attribute.name.generic.max_health": "Maximale Gsundhaid", "attribute.name.generic.movement_efficiency": "Movement Efficiency", "attribute.name.generic.movement_speed": "Gschdichgeid", "attribute.name.generic.oxygen_bonus": "Oxygen Bonus", "attribute.name.generic.safe_fall_distance": "Safe Fall Distance", "attribute.name.generic.scale": "Scale", "attribute.name.generic.step_height": "Step Height", "attribute.name.generic.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.gravity": "Gravity", "attribute.name.horse.jump_strength": "Pfäadesprungstäakn", "attribute.name.jump_strength": "Jump Strength", "attribute.name.knockback_resistance": "Knockback Resistance", "attribute.name.luck": "Luck", "attribute.name.max_absorption": "Max Absorption", "attribute.name.max_health": "Max Health", "attribute.name.mining_efficiency": "Mining Efficiency", "attribute.name.movement_efficiency": "Movement Efficiency", "attribute.name.movement_speed": "Speed", "attribute.name.oxygen_bonus": "Oxygen Bonus", "attribute.name.player.block_break_speed": "Block Break Speed", "attribute.name.player.block_interaction_range": "Block Interaction Range", "attribute.name.player.entity_interaction_range": "Entity Interaction Range", "attribute.name.player.mining_efficiency": "Mining Efficiency", "attribute.name.player.sneaking_speed": "Sneaking Speed", "attribute.name.player.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.player.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.safe_fall_distance": "Safe Fall Distance", "attribute.name.scale": "Scale", "attribute.name.sneaking_speed": "Sneaking Speed", "attribute.name.spawn_reinforcements": "Zombie Reinforcements", "attribute.name.step_height": "Step Height", "attribute.name.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.tempt_range": "Mob Tempt Range", "attribute.name.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.waypoint_receive_range": "Waypoint Receive Range", "attribute.name.waypoint_transmit_range": "Waypoint Transmit Range", "attribute.name.zombie.spawn_reinforcements": "Zombie-Veastäakung", "biome.minecraft.badlands": "Taflbeach", "biome.minecraft.bamboo_jungle": "Bambusdschungl", "biome.minecraft.basalt_deltas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.beach": "Strand", "biome.minecraft.birch_forest": "Berknwold", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cold_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.crimson_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dark_forest": "Dichder Wold", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "V<PERSON><PERSON><PERSON>", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>dn", "biome.minecraft.dripstone_caves": "Tropfstoahöhln", "biome.minecraft.end_barrens": "End-Kagland", "biome.minecraft.end_highlands": "End-Hochland", "biome.minecraft.end_midlands": "End-Middlland", "biome.minecraft.eroded_badlands": "A<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.flower_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.forest": "Wold", "biome.minecraft.frozen_ocean": "V<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "Väraisde Gipfle", "biome.minecraft.frozen_river": "Veaeisda Flus<PERSON>", "biome.minecraft.grove": "<PERSON><PERSON><PERSON>", "biome.minecraft.ice_spikes": "Aiszapfndundra", "biome.minecraft.jagged_peaks": "Zärglüftede Gipfle", "biome.minecraft.jungle": "Droubn", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.lush_caves": "Übbige Höhln", "biome.minecraft.mangrove_swamp": "Mangrowensumbf", "biome.minecraft.meadow": "Oalm", "biome.minecraft.mushroom_fields": "Bilzland", "biome.minecraft.nether_wastes": "Nether-Ödland", "biome.minecraft.ocean": "<PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "<PERSON><PERSON>w<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_pine_taiga": "<PERSON><PERSON>woa<PERSON><PERSON>", "biome.minecraft.old_growth_spruce_taiga": "<PERSON><PERSON>wachsene <PERSON>n<PERSON>", "biome.minecraft.pale_garden": "Pale Garden", "biome.minecraft.plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.river": "Fluss", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Savannenho<PERSON>bne", "biome.minecraft.small_end_islands": "<PERSON><PERSON>", "biome.minecraft.snowy_beach": "Verschneite Strand", "biome.minecraft.snowy_plains": "Voaschneide Äbene", "biome.minecraft.snowy_slopes": "Voaschneide Hängä", "biome.minecraft.snowy_taiga": "Veaschneide Taiga", "biome.minecraft.soul_sand_valley": "Seelnsanddal", "biome.minecraft.sparse_jungle": "Dschungellischtung", "biome.minecraft.stony_peaks": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.stony_shore": "<PERSON><PERSON>", "biome.minecraft.sunflower_plains": "Sonnenblumenebne", "biome.minecraft.swamp": "Sumpf", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "<PERSON>", "biome.minecraft.the_void": "<PERSON>", "biome.minecraft.warm_ocean": "Wama Ozean", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_forest": "Unoardändlicha Woald", "biome.minecraft.windswept_gravelly_hills": "Unoardändliche Geröllhügel", "biome.minecraft.windswept_hills": "Unoardändlicha Hügel", "biome.minecraft.windswept_savanna": "Unoardändliche Soavanne", "biome.minecraft.wooded_badlands": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_button": "Akazienhulzgnobf", "block.minecraft.acacia_door": "Akazienhulzdüe", "block.minecraft.acacia_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_hanging_sign": "Aggazienhängeschildle", "block.minecraft.acacia_leaves": "Akazienlaab", "block.minecraft.acacia_log": "Akazienschdamm", "block.minecraft.acacia_planks": "Akazienhulzbredda", "block.minecraft.acacia_pressure_plate": "Akazienhulzdruggbladdn", "block.minecraft.acacia_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_sign": "Akazienhulzschild", "block.minecraft.acacia_slab": "Akazienhulzschdufn", "block.minecraft.acacia_stairs": "Akazienhulzdrebbn", "block.minecraft.acacia_trapdoor": "Akazienhulzfalldüe", "block.minecraft.acacia_wall_hanging_sign": "Aggazienwandhängeschlidle", "block.minecraft.acacia_wall_sign": "Akazienh<PERSON>z<PERSON><PERSON>child", "block.minecraft.acacia_wood": "Akazienhulz", "block.minecraft.activator_rail": "Agdivierungsschiene", "block.minecraft.air": "Lu<PERSON>d", "block.minecraft.allium": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.amethyst_block": "Amethysdblogg", "block.minecraft.amethyst_cluster": "Amethysdha<PERSON><PERSON>", "block.minecraft.ancient_debris": "Antiga Schrodt", "block.minecraft.andesite": "Andesidd", "block.minecraft.andesite_slab": "Andesiddschdufn", "block.minecraft.andesite_stairs": "Andesidddrebbn", "block.minecraft.andesite_wall": "Andesiddmaua", "block.minecraft.anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Küabisrankn", "block.minecraft.attached_pumpkin_stem": "Küabisrankn", "block.minecraft.azalea": "<PERSON><PERSON><PERSON>", "block.minecraft.azalea_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azure_bluet": "Boazellanstean<PERSON>n", "block.minecraft.bamboo": "Bambus", "block.minecraft.bamboo_block": "Bambusblogg", "block.minecraft.bamboo_button": "Bambusknöpfle", "block.minecraft.bamboo_door": "Bambuspfortn", "block.minecraft.bamboo_fence": "Bambuszäunla", "block.minecraft.bamboo_fence_gate": "Bambuszaunpfortn", "block.minecraft.bamboo_hanging_sign": "Bambushängeschildle", "block.minecraft.bamboo_mosaic": "Ba<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_mosaic_slab": "Bambusmosaiggstufn", "block.minecraft.bamboo_mosaic_stairs": "Bambusmosaiktreppn", "block.minecraft.bamboo_planks": "Bambusbredder", "block.minecraft.bamboo_pressure_plate": "Bambusdruggpladdn", "block.minecraft.bamboo_sapling": "Bambussprösslin", "block.minecraft.bamboo_sign": "Bambuss<PERSON>le", "block.minecraft.bamboo_slab": "Bambusstufn", "block.minecraft.bamboo_stairs": "Bambusstrebbn", "block.minecraft.bamboo_trapdoor": "Bambusfalldür", "block.minecraft.bamboo_wall_hanging_sign": "Bambuswandhängeschildle", "block.minecraft.bamboo_wall_sign": "Bambuswandschildle", "block.minecraft.banner.base.black": "Vollkumma schwoarzes Föid", "block.minecraft.banner.base.blue": "<PERSON><PERSON><PERSON><PERSON> blau<PERSON>", "block.minecraft.banner.base.brown": "<PERSON><PERSON><PERSON><PERSON> broaunes <PERSON>", "block.minecraft.banner.base.cyan": "Vollkumma dürg<PERSON>", "block.minecraft.banner.base.gray": "<PERSON>lk<PERSON><PERSON> gra<PERSON>", "block.minecraft.banner.base.green": "<PERSON><PERSON><PERSON><PERSON> grines <PERSON>", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.base.lime": "Vollkumma hellgrines <PERSON>", "block.minecraft.banner.base.magenta": "Vollkumma magenda Fö<PERSON>", "block.minecraft.banner.base.orange": "Vollkumma oranges Föid", "block.minecraft.banner.base.pink": "<PERSON><PERSON><PERSON><PERSON> bingg<PERSON>", "block.minecraft.banner.base.purple": "<PERSON><PERSON><PERSON><PERSON> violedd<PERSON>", "block.minecraft.banner.base.red": "<PERSON><PERSON><PERSON><PERSON> roud<PERSON>", "block.minecraft.banner.base.white": "Vollkumma woaßes Föid", "block.minecraft.banner.base.yellow": "<PERSON>lkumma gel<PERSON>", "block.minecraft.banner.border.black": "Schwoaza Board", "block.minecraft.banner.border.blue": "Blaua Board", "block.minecraft.banner.border.brown": "Broauna Board", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.gray": "Graua Board", "block.minecraft.banner.border.green": "Grina Board", "block.minecraft.banner.border.light_blue": "Hellblaua Board", "block.minecraft.banner.border.light_gray": "Hellgraua Board", "block.minecraft.banner.border.lime": "Hellgrina Board", "block.minecraft.banner.border.magenta": "Magenda Board", "block.minecraft.banner.border.orange": "Oranga Board", "block.minecraft.banner.border.pink": "Bingga Board", "block.minecraft.banner.border.purple": "Violedda Board", "block.minecraft.banner.border.red": "Rouda Board", "block.minecraft.banner.border.white": "Woaßa Board", "block.minecraft.banner.border.yellow": "Gelba Board", "block.minecraft.banner.bricks.black": "<PERSON><PERSON><PERSON> schwo<PERSON>z g<PERSON>t", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON><PERSON> blau <PERSON>t", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON> broaun g<PERSON>t", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON> d<PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON><PERSON> grau g<PERSON>t", "block.minecraft.banner.bricks.green": "<PERSON><PERSON><PERSON> grin g<PERSON>uert", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON><PERSON> g<PERSON>t", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON>t", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON> magenda g<PERSON>uert", "block.minecraft.banner.bricks.orange": "Föid orange gmauert", "block.minecraft.banner.bricks.pink": "<PERSON><PERSON><PERSON> bingg g<PERSON>t", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON><PERSON> violedd g<PERSON>t", "block.minecraft.banner.bricks.red": "<PERSON><PERSON><PERSON> r<PERSON> g<PERSON>t", "block.minecraft.banner.bricks.white": "<PERSON><PERSON><PERSON> woaß g<PERSON>uert", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON> gelb g<PERSON>t", "block.minecraft.banner.circle.black": "Schwoaze Kugl", "block.minecraft.banner.circle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.green": "<PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "Hellblaue Kugl", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "Magenda Kugl", "block.minecraft.banner.circle.orange": "Orange Kugl", "block.minecraft.banner.circle.pink": "<PERSON><PERSON>", "block.minecraft.banner.circle.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.white": "Woaße Kugl", "block.minecraft.banner.circle.yellow": "Gelbe <PERSON>l", "block.minecraft.banner.creeper.black": "Schwoaza Creeper", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON>una Creeper", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.green": "<PERSON><PERSON>", "block.minecraft.banner.creeper.light_blue": "Hellblaua <PERSON>", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.magenta": "Magenda Creeper", "block.minecraft.banner.creeper.orange": "Oranga Creeper", "block.minecraft.banner.creeper.pink": "Bingga Creeper", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.black": "Schwoazes Andreasgraits", "block.minecraft.banner.cross.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.magenta": "Magenda Andreasgraits", "block.minecraft.banner.cross.orange": "Oranges <PERSON>its", "block.minecraft.banner.cross.pink": "Bingges Andreasgraits", "block.minecraft.banner.cross.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.white": "Woaßes Andreasgraits", "block.minecraft.banner.cross.yellow": "Gelbes <PERSON>its", "block.minecraft.banner.curly_border.black": "Schwoaza Schbicklboard", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.brown": "Broauna Schbicklboard", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.light_blue": "Hellblaua Schbicklboard", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.magenta": "Magenda Schbicklboard", "block.minecraft.banner.curly_border.orange": "Oranga Schbicklboard", "block.minecraft.banner.curly_border.pink": "Bingga Schbicklboard", "block.minecraft.banner.curly_border.purple": "Violedda <PERSON>lboard", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON><PERSON>l<PERSON>", "block.minecraft.banner.curly_border.yellow": "Gelba <PERSON>lboard", "block.minecraft.banner.diagonal_left.black": "Schwoaz schrechlings deeld", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON> schrechlings deeld", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON> schrechlings deeld", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> de<PERSON>", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON> sch<PERSON> de<PERSON>", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON> schre<PERSON><PERSON> deeld", "block.minecraft.banner.diagonal_left.light_blue": "Hellblau schrechlings deeld", "block.minecraft.banner.diagonal_left.light_gray": "Hell<PERSON><PERSON> schrechlings deeld", "block.minecraft.banner.diagonal_left.lime": "Hell<PERSON>rin schrechlings deeld", "block.minecraft.banner.diagonal_left.magenta": "Magenda schrechlings deeld", "block.minecraft.banner.diagonal_left.orange": "Orange schrechlings deeld", "block.minecraft.banner.diagonal_left.pink": "<PERSON>g schrechlings deeld", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON><PERSON> schrechlings deeld", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON> s<PERSON> deeld", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> schrech<PERSON> deeld", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON> schre<PERSON><PERSON> deeld", "block.minecraft.banner.diagonal_right.black": "Schwoaz schrechrechds deeld", "block.minecraft.banner.diagonal_right.blue": "Blau schrechrechds deeld", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON> schrechrech<PERSON> deeld", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON> sch<PERSON><PERSON><PERSON><PERSON><PERSON> deeld", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON> sch<PERSON><PERSON><PERSON><PERSON><PERSON> deeld", "block.minecraft.banner.diagonal_right.light_blue": "Hellblau schrechrechds deeld", "block.minecraft.banner.diagonal_right.light_gray": "Hellgrau schrechrechds deeld", "block.minecraft.banner.diagonal_right.lime": "Hellgrin schrechrech<PERSON> deeld", "block.minecraft.banner.diagonal_right.magenta": "Magenda schrechrechds deeld", "block.minecraft.banner.diagonal_right.orange": "Orange schrechrechds deeld", "block.minecraft.banner.diagonal_right.pink": "<PERSON>g schrechrech<PERSON> deeld", "block.minecraft.banner.diagonal_right.purple": "Violedd schrechrechds deeld", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON> s<PERSON><PERSON> de<PERSON>", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON> schrech<PERSON> deeld", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON> sch<PERSON><PERSON><PERSON><PERSON><PERSON> deeld", "block.minecraft.banner.diagonal_up_left.black": "Schwoaz schrechrechds deeld (Invertiert)", "block.minecraft.banner.diagonal_up_left.blue": "Blau schrechre<PERSON><PERSON>eld (Invertiert)", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON><PERSON> schre<PERSON><PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON><PERSON> sch<PERSON><PERSON><PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON> sch<PERSON><PERSON><PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_left.light_blue": "Hellblau schrechrechds deeld (Invertiert)", "block.minecraft.banner.diagonal_up_left.light_gray": "Hellgrau schrechrech<PERSON> deeld (Invertiert)", "block.minecraft.banner.diagonal_up_left.lime": "Hellgrin schrech<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_left.magenta": "Magenda schrechre<PERSON><PERSON> deeld (Invertiert)", "block.minecraft.banner.diagonal_up_left.orange": "Orange schrechrechds deeld (Invertiert)", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON><PERSON> schrech<PERSON><PERSON><PERSON> de<PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_left.purple": "Violedd schre<PERSON><PERSON><PERSON><PERSON> deeld (Invertiert)", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON> s<PERSON><PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_left.white": "<PERSON><PERSON><PERSON> sch<PERSON><PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON><PERSON> sch<PERSON><PERSON><PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.black": "Schwoaz schrechlings deeld (Invertiert)", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON> sch<PERSON><PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON><PERSON> sch<PERSON> de<PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON> s<PERSON><PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.light_blue": "Hellblau schrechlings deeld (Invertiert)", "block.minecraft.banner.diagonal_up_right.light_gray": "Hellgrau sch<PERSON> de<PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.lime": "Hellgrin sch<PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.magenta": "Magenda schrech<PERSON> de<PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.orange": "Orange schrechlings deeld (Invertiert)", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON> sch<PERSON> de<PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON><PERSON> sch<PERSON> de<PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON> s<PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON> s<PERSON> (Invertiert)", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON> sch<PERSON><PERSON> (Invertiert)", "block.minecraft.banner.flow.black": "Black Flow", "block.minecraft.banner.flow.blue": "Blue Flow", "block.minecraft.banner.flow.brown": "Brown Flow", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON>", "block.minecraft.banner.flow.gray": "Gray Flow", "block.minecraft.banner.flow.green": "Green Flow", "block.minecraft.banner.flow.light_blue": "Light Blue Flow", "block.minecraft.banner.flow.light_gray": "Light Gray Flow", "block.minecraft.banner.flow.lime": "Lime Flow", "block.minecraft.banner.flow.magenta": "Magenta Flow", "block.minecraft.banner.flow.orange": "Orange Flow", "block.minecraft.banner.flow.pink": "Pink Flow", "block.minecraft.banner.flow.purple": "Purple Flow", "block.minecraft.banner.flow.red": "Red Flow", "block.minecraft.banner.flow.white": "White Flow", "block.minecraft.banner.flow.yellow": "Yellow Flow", "block.minecraft.banner.flower.black": "Schwoaze Bluma", "block.minecraft.banner.flower.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.green": "<PERSON><PERSON>", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.orange": "Orange Bluma", "block.minecraft.banner.flower.pink": "<PERSON><PERSON>", "block.minecraft.banner.flower.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.white": "Woaße Bluma", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.black": "Schwoaza Globus", "block.minecraft.banner.globe.blue": "Blaua Globus", "block.minecraft.banner.globe.brown": "Broauna Globus", "block.minecraft.banner.globe.cyan": "Dürgisa Globus", "block.minecraft.banner.globe.gray": "Graua Globus", "block.minecraft.banner.globe.green": "Grina Globus", "block.minecraft.banner.globe.light_blue": "Hellblaua Globus", "block.minecraft.banner.globe.light_gray": "Hellgraua Globus", "block.minecraft.banner.globe.lime": "Hellgrina Globus", "block.minecraft.banner.globe.magenta": "Magenda Globus", "block.minecraft.banner.globe.orange": "Oranga Globus", "block.minecraft.banner.globe.pink": "Bingga Globus", "block.minecraft.banner.globe.purple": "Violedda Globus", "block.minecraft.banner.globe.red": "Rouda Globus", "block.minecraft.banner.globe.white": "Woaßa Globus", "block.minecraft.banner.globe.yellow": "Gelba Globus", "block.minecraft.banner.gradient.black": "Schwoaza Farbverlaaf", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.brown": "Broauna Farbverlaaf", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.green": "<PERSON><PERSON>", "block.minecraft.banner.gradient.light_blue": "Hellblaua Farbverlaaf", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.magenta": "Magenda Farbverlaaf", "block.minecraft.banner.gradient.orange": "Oranga Farbverlaaf", "block.minecraft.banner.gradient.pink": "Bingga Farbverlaaf", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.black": "Schwoaza Farbverlaaf (Invertiert)", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.brown": "Broauna Farbverlaaf (Invertiert)", "block.minecraft.banner.gradient_up.cyan": "<PERSON><PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.light_blue": "Hellblaua Farbverlaaf (Invertiert)", "block.minecraft.banner.gradient_up.light_gray": "<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.lime": "<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.magenta": "Magenda Farbverlaaf (Invertiert)", "block.minecraft.banner.gradient_up.orange": "Oranga Farbverlaaf (Invertiert)", "block.minecraft.banner.gradient_up.pink": "Bingga <PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.gradient_up.yellow": "<PERSON><PERSON><PERSON> (Invertiert)", "block.minecraft.banner.guster.black": "Black Guster", "block.minecraft.banner.guster.blue": "Blue Guster", "block.minecraft.banner.guster.brown": "<PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON>", "block.minecraft.banner.guster.gray": "<PERSON>", "block.minecraft.banner.guster.green": "<PERSON> Guster", "block.minecraft.banner.guster.light_blue": "Light Blue Guster", "block.minecraft.banner.guster.light_gray": "Light Gray Guster", "block.minecraft.banner.guster.lime": "<PERSON><PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.orange": "Orange Guster", "block.minecraft.banner.guster.pink": "<PERSON> Guster", "block.minecraft.banner.guster.purple": "Purple Guster", "block.minecraft.banner.guster.red": "<PERSON> Gus<PERSON>", "block.minecraft.banner.guster.white": "White Guster", "block.minecraft.banner.guster.yellow": "Yellow Guster", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON><PERSON> schwo<PERSON>z de<PERSON>", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON> blau <PERSON>", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON> broaun de<PERSON>", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON> d<PERSON>", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON><PERSON> grau <PERSON>", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON> grin deeld", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON><PERSON> magenda deeld", "block.minecraft.banner.half_horizontal.orange": "Ouwa orange deeld", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON> bingg de<PERSON>", "block.minecraft.banner.half_horizontal.purple": "<PERSON>uwa violedd de<PERSON>", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON><PERSON> roud de<PERSON>", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON> woaß de<PERSON>", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON><PERSON> gelb de<PERSON>", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON><PERSON> schwo<PERSON>z de<PERSON>", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON>dn blau de<PERSON>", "block.minecraft.banner.half_horizontal_bottom.brown": "Undn broaun deeld", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON> dürg<PERSON>", "block.minecraft.banner.half_horizontal_bottom.gray": "Undn grau de<PERSON>", "block.minecraft.banner.half_horizontal_bottom.green": "Undn grin deeld", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Undn hellblau deeld", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Undn hellgrau de<PERSON>", "block.minecraft.banner.half_horizontal_bottom.lime": "Undn hellgrin deeld", "block.minecraft.banner.half_horizontal_bottom.magenta": "Undn magenda deeld", "block.minecraft.banner.half_horizontal_bottom.orange": "Undn orange deeld", "block.minecraft.banner.half_horizontal_bottom.pink": "Undn bingg deeld", "block.minecraft.banner.half_horizontal_bottom.purple": "Undn violedd deeld", "block.minecraft.banner.half_horizontal_bottom.red": "Undn roud deeld", "block.minecraft.banner.half_horizontal_bottom.white": "Undn woaß deeld", "block.minecraft.banner.half_horizontal_bottom.yellow": "Undn gelb deeld", "block.minecraft.banner.half_vertical.black": "Rechds schwoarz gschbaldn", "block.minecraft.banner.half_vertical.blue": "Rechds blau gschbaldn", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON> broaun g<PERSON>dn", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON><PERSON> dürgis <PERSON>", "block.minecraft.banner.half_vertical.gray": "Rechds grau gsch<PERSON>dn", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON><PERSON> grin gschbaldn", "block.minecraft.banner.half_vertical.light_blue": "Rechds hellblau gschbaldn", "block.minecraft.banner.half_vertical.light_gray": "Rechds hellgrau gschbaldn", "block.minecraft.banner.half_vertical.lime": "Rechds hellgrin gschbaldn", "block.minecraft.banner.half_vertical.magenta": "Rechds magenda gschbaldn", "block.minecraft.banner.half_vertical.orange": "Rechds orange gschbaldn", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON><PERSON> bingg gschbaldn", "block.minecraft.banner.half_vertical.purple": "Rechds violedd gschbaldn", "block.minecraft.banner.half_vertical.red": "Re<PERSON>ds roud g<PERSON>dn", "block.minecraft.banner.half_vertical.white": "Re<PERSON>ds woaß g<PERSON>baldn", "block.minecraft.banner.half_vertical.yellow": "Rechds gelb gschbaldn", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON> schwo<PERSON>z <PERSON>", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON> blau <PERSON>", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON> broaun g<PERSON>dn", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON> dürg<PERSON>", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON> grau g<PERSON>", "block.minecraft.banner.half_vertical_right.green": "Lings grin gschbaldn", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON>s magenda g<PERSON>dn", "block.minecraft.banner.half_vertical_right.orange": "Lings orange gschbaldn", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON> bingg g<PERSON><PERSON>dn", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON> violedd g<PERSON>dn", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON> roud g<PERSON>dn", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON> woaß g<PERSON>dn", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON> gelb g<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.black": "Schwoazes Mojang-Logo", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.brown": "Broaunes Mojang-Logo", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON>ues Mojang-Logo", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.lime": "Hellgrines <PERSON>-<PERSON>", "block.minecraft.banner.mojang.magenta": "Magenda Mojang-Logo", "block.minecraft.banner.mojang.orange": "Oranges Mojang-Logo", "block.minecraft.banner.mojang.pink": "Bingges Mojang-Logo", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.white": "Woaßes Mojang-Logo", "block.minecraft.banner.mojang.yellow": "Gelbes Mojang-Logo", "block.minecraft.banner.piglin.black": "Schwoaze Schnauzn", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.green": "<PERSON><PERSON>", "block.minecraft.banner.piglin.light_blue": "Hellblaue Schnauzn", "block.minecraft.banner.piglin.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.magenta": "Magenda Schnauzn", "block.minecraft.banner.piglin.orange": "Orange Schnauzn", "block.minecraft.banner.piglin.pink": "Bingge Schnauzn", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.white": "Woaße Schnauzn", "block.minecraft.banner.piglin.yellow": "Gelbe Schnauzn", "block.minecraft.banner.rhombus.black": "Schwoaze Raude", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.orange": "Orange Raude", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.white": "Woaße Raude", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.black": "Schwoaza Schedl", "block.minecraft.banner.skull.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.brown": "<PERSON><PERSON><PERSON> Schedl", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.green": "<PERSON><PERSON>", "block.minecraft.banner.skull.light_blue": "Hellblaua <PERSON>", "block.minecraft.banner.skull.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.magenta": "Magenda Schedl", "block.minecraft.banner.skull.orange": "Oranga Schedl", "block.minecraft.banner.skull.pink": "Bingga Schedl", "block.minecraft.banner.skull.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.black": "Via schwoarze Pfohle", "block.minecraft.banner.small_stripes.blue": "Via blaue Pfohle", "block.minecraft.banner.small_stripes.brown": "Via broaune Pfohle", "block.minecraft.banner.small_stripes.cyan": "Via dürgise <PERSON>", "block.minecraft.banner.small_stripes.gray": "Via graue Pfohle", "block.minecraft.banner.small_stripes.green": "Via grine Pfohle", "block.minecraft.banner.small_stripes.light_blue": "Via hellblaue Pfohle", "block.minecraft.banner.small_stripes.light_gray": "Via hellgraue Pfohle", "block.minecraft.banner.small_stripes.lime": "Via hell<PERSON><PERSON>e", "block.minecraft.banner.small_stripes.magenta": "Via magenda P<PERSON>hle", "block.minecraft.banner.small_stripes.orange": "Via orange Pfohle", "block.minecraft.banner.small_stripes.pink": "Via bingge Pfohle", "block.minecraft.banner.small_stripes.purple": "Via violedde Pfohle", "block.minecraft.banner.small_stripes.red": "Via roude Pfohle", "block.minecraft.banner.small_stripes.white": "Via woaße Pfohle", "block.minecraft.banner.small_stripes.yellow": "Via gelbe Pfohle", "block.minecraft.banner.square_bottom_left.black": "Schwoazes rechdes Ünnareck", "block.minecraft.banner.square_bottom_left.blue": "<PERSON><PERSON><PERSON> re<PERSON>", "block.minecraft.banner.square_bottom_left.brown": "<PERSON><PERSON><PERSON><PERSON> rech<PERSON>", "block.minecraft.banner.square_bottom_left.cyan": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "block.minecraft.banner.square_bottom_left.gray": "<PERSON><PERSON><PERSON> re<PERSON>", "block.minecraft.banner.square_bottom_left.green": "<PERSON><PERSON><PERSON> re<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.light_blue": "<PERSON><PERSON><PERSON> rechdes <PERSON>", "block.minecraft.banner.square_bottom_left.light_gray": "<PERSON><PERSON><PERSON> re<PERSON>", "block.minecraft.banner.square_bottom_left.lime": "<PERSON><PERSON><PERSON><PERSON> rech<PERSON>", "block.minecraft.banner.square_bottom_left.magenta": "Magenda rechdes Ü<PERSON>reck", "block.minecraft.banner.square_bottom_left.orange": "Oranges rechdes <PERSON>", "block.minecraft.banner.square_bottom_left.pink": "Bingges rechdes Ünnareck", "block.minecraft.banner.square_bottom_left.purple": "<PERSON><PERSON><PERSON><PERSON> rech<PERSON>", "block.minecraft.banner.square_bottom_left.red": "<PERSON><PERSON><PERSON> re<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.white": "Woaßes rechdes Ünnareck", "block.minecraft.banner.square_bottom_left.yellow": "Gelbes rechdes Ü<PERSON>reck", "block.minecraft.banner.square_bottom_right.black": "Sch<PERSON>az<PERSON> linges <PERSON>", "block.minecraft.banner.square_bottom_right.blue": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_bottom_right.brown": "<PERSON><PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_bottom_right.cyan": "<PERSON><PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_bottom_right.gray": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_bottom_right.green": "Grines linges <PERSON>", "block.minecraft.banner.square_bottom_right.light_blue": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_bottom_right.light_gray": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_bottom_right.lime": "<PERSON><PERSON><PERSON>s linges <PERSON>", "block.minecraft.banner.square_bottom_right.magenta": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_bottom_right.orange": "Oranges linges <PERSON>", "block.minecraft.banner.square_bottom_right.pink": "<PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_bottom_right.purple": "Violeddes linges <PERSON>", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_bottom_right.white": "W<PERSON><PERSON>s linges <PERSON>", "block.minecraft.banner.square_bottom_right.yellow": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_left.black": "Schwoazes rechdes Ouwaeck", "block.minecraft.banner.square_top_left.blue": "<PERSON><PERSON><PERSON> re<PERSON>", "block.minecraft.banner.square_top_left.brown": "<PERSON><PERSON><PERSON><PERSON> re<PERSON><PERSON>", "block.minecraft.banner.square_top_left.cyan": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "block.minecraft.banner.square_top_left.gray": "<PERSON><PERSON><PERSON> re<PERSON>", "block.minecraft.banner.square_top_left.green": "<PERSON><PERSON><PERSON> re<PERSON><PERSON>", "block.minecraft.banner.square_top_left.light_blue": "<PERSON><PERSON><PERSON> rechdes <PERSON>", "block.minecraft.banner.square_top_left.light_gray": "<PERSON><PERSON><PERSON> re<PERSON>", "block.minecraft.banner.square_top_left.lime": "Hell<PERSON><PERSON><PERSON> rech<PERSON>", "block.minecraft.banner.square_top_left.magenta": "Magenda rech<PERSON>eck", "block.minecraft.banner.square_top_left.orange": "Oranges rechdes <PERSON>", "block.minecraft.banner.square_top_left.pink": "Bingges rechdes Ouwaeck", "block.minecraft.banner.square_top_left.purple": "Violedd<PERSON> rech<PERSON>", "block.minecraft.banner.square_top_left.red": "<PERSON><PERSON><PERSON> re<PERSON><PERSON>", "block.minecraft.banner.square_top_left.white": "Woaßes rechdes Ouwaeck", "block.minecraft.banner.square_top_left.yellow": "Gelbes rechdes <PERSON>eck", "block.minecraft.banner.square_top_right.black": "Schwoazes linges <PERSON>", "block.minecraft.banner.square_top_right.blue": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.square_top_right.brown": "<PERSON><PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.cyan": "<PERSON><PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.gray": "<PERSON><PERSON><PERSON> l<PERSON>s <PERSON>", "block.minecraft.banner.square_top_right.green": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.light_blue": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.light_gray": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.lime": "<PERSON><PERSON><PERSON>s linges <PERSON>", "block.minecraft.banner.square_top_right.magenta": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.orange": "Oranges linges <PERSON>", "block.minecraft.banner.square_top_right.pink": "Bingges linges <PERSON>", "block.minecraft.banner.square_top_right.purple": "Violedd<PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.red": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.square_top_right.white": "Woaßes linges O<PERSON>", "block.minecraft.banner.square_top_right.yellow": "<PERSON><PERSON><PERSON> linges <PERSON>", "block.minecraft.banner.straight_cross.black": "Schwoazes Graits", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.magenta": "Magenda Graits", "block.minecraft.banner.straight_cross.orange": "Oranges Graits", "block.minecraft.banner.straight_cross.pink": "Bingges Graits", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.white": "Woaßes Graits", "block.minecraft.banner.straight_cross.yellow": "G<PERSON><PERSON> G<PERSON>", "block.minecraft.banner.stripe_bottom.black": "Schwoaza Bannerfiaß", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.brown": "Broauna Bannerfiaß", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.magenta": "Magenda Bannerfiaß", "block.minecraft.banner.stripe_bottom.orange": "Oranga Bannerfiaß", "block.minecraft.banner.stripe_bottom.pink": "<PERSON>ga Bannerfiaß", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.black": "Schwoaza Pfohl", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.brown": "Broauna Pfohl", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_blue": "Hellbla<PERSON>", "block.minecraft.banner.stripe_center.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.magenta": "Magenda Pfohl", "block.minecraft.banner.stripe_center.orange": "Oranga Pfohl", "block.minecraft.banner.stripe_center.pink": "Bingga Pfohl", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.black": "Schwoaza Schrechlingsbalkn", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.brown": "Broauna Schrechlingsbalkn", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_blue": "Hellblaua Schrechlingsbalkn", "block.minecraft.banner.stripe_downleft.light_gray": "<PERSON><PERSON><PERSON>balkn", "block.minecraft.banner.stripe_downleft.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.magenta": "Magenda Schrechlingsbalkn", "block.minecraft.banner.stripe_downleft.orange": "Oranga Schrechlingsbalkn", "block.minecraft.banner.stripe_downleft.pink": "Bingga Schrechlingsbalkn", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON><PERSON>a <PERSON>balkn", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON><PERSON><PERSON>lingsbalkn", "block.minecraft.banner.stripe_downleft.yellow": "G<PERSON><PERSON>lingsbalkn", "block.minecraft.banner.stripe_downright.black": "Schwoaza Schrechbalkn", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.brown": "Broauna Schrechbalkn", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.light_blue": "Hellblaua Schrechbalkn", "block.minecraft.banner.stripe_downright.light_gray": "<PERSON><PERSON><PERSON>n", "block.minecraft.banner.stripe_downright.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.magenta": "Magenda Schrechbalkn", "block.minecraft.banner.stripe_downright.orange": "Oranga Schrechbalkn", "block.minecraft.banner.stripe_downright.pink": "Bingga Schrechbalkn", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON>edda <PERSON>echbalkn", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON><PERSON>echbalkn", "block.minecraft.banner.stripe_downright.yellow": "Gelba <PERSON>echbalkn", "block.minecraft.banner.stripe_left.black": "Schwoaze rechde Flankn", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON><PERSON> rechde <PERSON>", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON><PERSON> rechde F<PERSON>n", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "block.minecraft.banner.stripe_left.gray": "<PERSON><PERSON><PERSON> re<PERSON>de <PERSON>", "block.minecraft.banner.stripe_left.green": "<PERSON><PERSON> rechde <PERSON>", "block.minecraft.banner.stripe_left.light_blue": "Hellblaue rechde Flankn", "block.minecraft.banner.stripe_left.light_gray": "Hell<PERSON><PERSON> rechde Flankn", "block.minecraft.banner.stripe_left.lime": "<PERSON><PERSON><PERSON> rechde <PERSON>", "block.minecraft.banner.stripe_left.magenta": "Magenda rechde Flankn", "block.minecraft.banner.stripe_left.orange": "Orange rechde Flankn", "block.minecraft.banner.stripe_left.pink": "Bingge rechde Flankn", "block.minecraft.banner.stripe_left.purple": "<PERSON><PERSON><PERSON><PERSON> rechde <PERSON>", "block.minecraft.banner.stripe_left.red": "<PERSON><PERSON><PERSON> rechde <PERSON>", "block.minecraft.banner.stripe_left.white": "Woaße rechde Flankn", "block.minecraft.banner.stripe_left.yellow": "Gelbe rechde Flankn", "block.minecraft.banner.stripe_middle.black": "Schwoaza Balkn", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON> Balkn", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "Hell<PERSON>ua <PERSON>n", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.magenta": "Magenda Balkn", "block.minecraft.banner.stripe_middle.orange": "Oranga Balkn", "block.minecraft.banner.stripe_middle.pink": "Bingga Balkn", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.black": "Schwoaze linge Flankn", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_right.brown": "<PERSON><PERSON><PERSON> linge <PERSON>n", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_right.gray": "<PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.banner.stripe_right.green": "<PERSON><PERSON> linge <PERSON>", "block.minecraft.banner.stripe_right.light_blue": "Hellblaue linge Flankn", "block.minecraft.banner.stripe_right.light_gray": "<PERSON><PERSON><PERSON> linge <PERSON>", "block.minecraft.banner.stripe_right.lime": "<PERSON><PERSON><PERSON> linge <PERSON>", "block.minecraft.banner.stripe_right.magenta": "Magenda linge F<PERSON>n", "block.minecraft.banner.stripe_right.orange": "Orange linge Flankn", "block.minecraft.banner.stripe_right.pink": "<PERSON><PERSON> linge <PERSON>", "block.minecraft.banner.stripe_right.purple": "<PERSON><PERSON><PERSON><PERSON> linge <PERSON>", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON><PERSON> linge <PERSON>", "block.minecraft.banner.stripe_right.white": "Woaße linge Flankn", "block.minecraft.banner.stripe_right.yellow": "Gelbe linge Flankn", "block.minecraft.banner.stripe_top.black": "Schwoazes Bannerhaad", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.magenta": "<PERSON>gen<PERSON>haad", "block.minecraft.banner.stripe_top.orange": "Oranges Bannerhaad", "block.minecraft.banner.stripe_top.pink": "Bingges Bannerhaad", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.white": "Woaßes Bannerhaad", "block.minecraft.banner.stripe_top.yellow": "G<PERSON><PERSON>ad", "block.minecraft.banner.triangle_bottom.black": "Schwoaze hoalbe Schbitzn", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON><PERSON> hoalbe <PERSON>", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON><PERSON> hoalbe <PERSON>n", "block.minecraft.banner.triangle_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON><PERSON> ho<PERSON>be <PERSON>", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON> hoalbe <PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "Hellblaue hoalbe Sch<PERSON>zn", "block.minecraft.banner.triangle_bottom.light_gray": "Hell<PERSON><PERSON> hoalbe <PERSON>n", "block.minecraft.banner.triangle_bottom.lime": "<PERSON><PERSON><PERSON> hoalbe <PERSON>n", "block.minecraft.banner.triangle_bottom.magenta": "Magenda hoalbe Schbitzn", "block.minecraft.banner.triangle_bottom.orange": "Orange hoalbe Schbitzn", "block.minecraft.banner.triangle_bottom.pink": "<PERSON><PERSON> hoalbe <PERSON>zn", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON><PERSON><PERSON> hoalbe <PERSON>n", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON><PERSON> hoalbe <PERSON>n", "block.minecraft.banner.triangle_bottom.white": "Woaße hoalbe Schbitzn", "block.minecraft.banner.triangle_bottom.yellow": "Gelbe hoalbe Schbitzn", "block.minecraft.banner.triangle_top.black": "Schwoaze gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.blue": "Blaue gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.brown": "Broaune gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.cyan": "D<PERSON>rgise gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.gray": "Graue gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.green": "<PERSON>rine gstürz<PERSON> hoalbe Schbitzn", "block.minecraft.banner.triangle_top.light_blue": "Hellblaue gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.light_gray": "Hellgraue gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.lime": "Hellgrine gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.magenta": "Magenda gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.orange": "Orange gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.pink": "Bingge gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.purple": "Violedde gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.red": "Roude gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.white": "Woaße gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangle_top.yellow": "Gelbe gstürzte hoalbe Schbitzn", "block.minecraft.banner.triangles_bottom.black": "Schwoaza gschpicklta Bannerfiaß", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.brown": "Broauna gschpicklta Bannerfiaß", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON>lta Bannerfiaß", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON><PERSON> g<PERSON>lta Bannerfiaß", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.light_blue": "Hellblaua gschpicklta Bannerfiaß", "block.minecraft.banner.triangles_bottom.light_gray": "Hellgraua gschpicklta Bannerfiaß", "block.minecraft.banner.triangles_bottom.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.magenta": "Magenda gschpicklta Bannerfiaß", "block.minecraft.banner.triangles_bottom.orange": "Oranga gschpicklta Bannerfiaß", "block.minecraft.banner.triangles_bottom.pink": "Bingga gschpicklta Bannerfiaß", "block.minecraft.banner.triangles_bottom.purple": "Violedda g<PERSON>lta Bannerfiaß", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.white": "Woaßa g<PERSON>pic<PERSON>lta Bannerfiaß", "block.minecraft.banner.triangles_bottom.yellow": "Gelba gschpicklta Bannerfiaß", "block.minecraft.banner.triangles_top.black": "Schwoazes gschpickltes Bannerhaad", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON>s g<PERSON>lt<PERSON>", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON><PERSON><PERSON>lt<PERSON>", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.light_blue": "Hell<PERSON>ues gschpickltes Banner<PERSON>ad", "block.minecraft.banner.triangles_top.light_gray": "<PERSON><PERSON><PERSON> g<PERSON>lt<PERSON>", "block.minecraft.banner.triangles_top.lime": "Hellg<PERSON>s g<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_top.magenta": "Magenda gschpickltes Bannerhaad", "block.minecraft.banner.triangles_top.orange": "Oranges gschpickltes <PERSON>", "block.minecraft.banner.triangles_top.pink": "Bingges gschpickltes Bannerhaad", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>lt<PERSON>", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>", "block.minecraft.banner.triangles_top.white": "Woaßes gschpickltes Bannerhaad", "block.minecraft.banner.triangles_top.yellow": "Gelbes gschpickltes Bannerhaad", "block.minecraft.barrel": "Fässla", "block.minecraft.barrier": "Barrier", "block.minecraft.basalt": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon": "Leuchdfeua", "block.minecraft.beacon.primary": "Brimäre Krafd", "block.minecraft.beacon.secondary": "Segundäre Krafd", "block.minecraft.bed.no_sleep": "Du kannsd nua noachds oda währnd eins Gewiddas schlafn", "block.minecraft.bed.not_safe": "<PERSON> koannst jetzadla ned s<PERSON>n, es san Monsda in dea Näh", "block.minecraft.bed.obstructed": "Des Bed is blockiead", "block.minecraft.bed.occupied": "Des Bed is bleechd", "block.minecraft.bed.too_far_away": "<PERSON> koannst jet<PERSON>la ned sch<PERSON>n, des Bed is zu woad endfeand", "block.minecraft.bedrock": "Grundgeschdein", "block.minecraft.bee_nest": "Bienanest", "block.minecraft.beehive": "Bienaschdogg", "block.minecraft.beetroots": "<PERSON><PERSON>", "block.minecraft.bell": "<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "Großes Dropfbladd", "block.minecraft.big_dripleaf_stem": "<PERSON><PERSON>ßa Dropfbladdstiel", "block.minecraft.birch_button": "Berkngnobf", "block.minecraft.birch_door": "Berknhulzdüe", "block.minecraft.birch_fence": "Berk<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_fence_gate": "Berknhulzzauntor", "block.minecraft.birch_hanging_sign": "Birgnhängeschildle", "block.minecraft.birch_leaves": "Berknlaab", "block.minecraft.birch_log": "Berknschdamm", "block.minecraft.birch_planks": "Berknhulzbredda", "block.minecraft.birch_pressure_plate": "Berknhulzdruggbladdn", "block.minecraft.birch_sapling": "Berknsetzling", "block.minecraft.birch_sign": "Berknhulzschild", "block.minecraft.birch_slab": "Berknhulzschdufn", "block.minecraft.birch_stairs": "Berknhulzdrebbn", "block.minecraft.birch_trapdoor": "Berknhulzfalldüe", "block.minecraft.birch_wall_hanging_sign": "Birgnwandhängeschildle", "block.minecraft.birch_wall_sign": "Berk<PERSON><PERSON><PERSON><PERSON><PERSON>child", "block.minecraft.birch_wood": "Berknhulz", "block.minecraft.black_banner": "Schwoazes Banner", "block.minecraft.black_bed": "Schwoazes Bedd", "block.minecraft.black_candle": "Schwoaze Kirza", "block.minecraft.black_candle_cake": "<PERSON><PERSON><PERSON> midn schwoaza Kirza", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_concrete": "Schwoaza Bedon", "block.minecraft.black_concrete_powder": "Schwoaza Druggnbedon", "block.minecraft.black_glazed_terracotta": "Schwoaze gloasierde Keramig", "block.minecraft.black_shulker_box": "Schwoaze Shulkerkischdla", "block.minecraft.black_stained_glass": "Schwoazes Glos", "block.minecraft.black_stained_glass_pane": "Schwoaze Glosschaim", "block.minecraft.black_terracotta": "Schwoaze Keramig", "block.minecraft.black_wool": "Schwoaze Woll", "block.minecraft.blackstone": "Schwoazschdoi", "block.minecraft.blackstone_slab": "Schwoazschdoischdufn", "block.minecraft.blackstone_stairs": "Schwoazschdoidrebbn", "block.minecraft.blackstone_wall": "Schwoazschdoimaua", "block.minecraft.blast_furnace": "Schmelzofn", "block.minecraft.blue_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle_cake": "<PERSON><PERSON><PERSON> midn blaua <PERSON>", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON><PERSON> glo<PERSON><PERSON>", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_orchid": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.bone_block": "Knochnblogg", "block.minecraft.bookshelf": "Bücharegal", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_block": "Herngrallnblogg", "block.minecraft.brain_coral_fan": "Herngrallnfächa", "block.minecraft.brain_coral_wall_fan": "Herngrallnwandfächa", "block.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_slab": "Zieglschdufn", "block.minecraft.brick_stairs": "Ziegld<PERSON><PERSON><PERSON>", "block.minecraft.brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bricks": "Ziegelstaa", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON><PERSON> Banner", "block.minecraft.brown_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_candle": "Broauna Kirza", "block.minecraft.brown_candle_cake": "<PERSON><PERSON>n midn broauna <PERSON>rza", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "Broauna Druggnbedon", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON><PERSON> glo<PERSON><PERSON>", "block.minecraft.brown_mushroom": "Broauna Bilz", "block.minecraft.brown_mushroom_block": "Broauna Bilzblogg", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_column": "Blasnsäule", "block.minecraft.bubble_coral": "Blaasngralle", "block.minecraft.bubble_coral_block": "Blaasngrallnblogg", "block.minecraft.bubble_coral_fan": "Blaasngrallnfächa", "block.minecraft.bubble_coral_wall_fan": "Blaasngrallnwandfächa", "block.minecraft.budding_amethyst": "Amethysdknosbmblogg", "block.minecraft.bush": "<PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Cactus Flower", "block.minecraft.cake": "<PERSON><PERSON><PERSON>", "block.minecraft.calcite": "Galzid", "block.minecraft.calibrated_sculk_sensor": "Gallibrierter Sculk-Sensor", "block.minecraft.campfire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle": "<PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "<PERSON><PERSON><PERSON> midn Ki<PERSON>", "block.minecraft.carrots": "Gelba <PERSON>ü<PERSON>n", "block.minecraft.cartography_table": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.carved_pumpkin": "G'sch<PERSON><PERSON>", "block.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_air": "Höhlnlufd", "block.minecraft.cave_vines": "Höhlnrankn", "block.minecraft.cave_vines_plant": "Höhlnranknpflanzn", "block.minecraft.chain": "Keddn", "block.minecraft.chain_command_block": "Keddn-Bfehlsblogg", "block.minecraft.cherry_button": "Kirschholzknöpfle", "block.minecraft.cherry_door": "Kirschholzpfortn", "block.minecraft.cherry_fence": "Kirschholzzäunla", "block.minecraft.cherry_fence_gate": "Kirschhol<PERSON>unpfortn", "block.minecraft.cherry_hanging_sign": "Kirschholzhängeschildle", "block.minecraft.cherry_leaves": "Kirschblädda", "block.minecraft.cherry_log": "Kirschholzstämmla", "block.minecraft.cherry_planks": "Kirschholzbredder", "block.minecraft.cherry_pressure_plate": "Kirschholzdruggpladdn", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_sign": "Kirschholzschildla", "block.minecraft.cherry_slab": "Kirschholzstufn", "block.minecraft.cherry_stairs": "Kirschholztrebbn", "block.minecraft.cherry_trapdoor": "Kirschholzfalldür", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chest": "<PERSON><PERSON><PERSON>", "block.minecraft.chipped_anvil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_bookshelf": "Gestemmts Bücharegol", "block.minecraft.chiseled_copper": "Gmoasd's <PERSON><PERSON><PERSON>", "block.minecraft.chiseled_deepslate": "Gmeißelda Diefnschiefa", "block.minecraft.chiseled_nether_bricks": "Gmeißelda Netherziegl", "block.minecraft.chiseled_polished_blackstone": "Gmeißelda poliada Schwoazschdoi", "block.minecraft.chiseled_quartz_block": "Gmeißelda Kwarzblogg", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON><PERSON><PERSON> rouda <PERSON>", "block.minecraft.chiseled_resin_bricks": "Chiseled Resin Bricks", "block.minecraft.chiseled_sandstone": "Gmeißelda Sandschdoi", "block.minecraft.chiseled_stone_bricks": "Gmeißelda Schdoiziegl", "block.minecraft.chiseled_tuff": "G<PERSON>as<PERSON>'s Tuff", "block.minecraft.chiseled_tuff_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chorus_flower": "Chorus<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chorus_plant": "Choruspflanzn", "block.minecraft.clay": "leem", "block.minecraft.closed_eyeblossom": "Closed Eyeblossom", "block.minecraft.coal_block": "Ko<PERSON>blogg", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "G<PERSON>ba Dregg", "block.minecraft.cobbled_deepslate": "Bruchdiefnschiefa", "block.minecraft.cobbled_deepslate_slab": "Bruchdiefnschiefaschdufn", "block.minecraft.cobbled_deepslate_stairs": "Bruchdiefnschiefadrebbn", "block.minecraft.cobbled_deepslate_wall": "Bruchdiefnschiefamaua", "block.minecraft.cobblestone": "Bruchschdoi", "block.minecraft.cobblestone_slab": "Bruchschdoischdufn", "block.minecraft.cobblestone_stairs": "Bruchschdoidrebbn", "block.minecraft.cobblestone_wall": "Bruchs<PERSON><PERSON>imaua", "block.minecraft.cobweb": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Bfehlsblogg", "block.minecraft.comparator": "Redstone-Kombaradoa", "block.minecraft.composter": "Komposda", "block.minecraft.conduit": "Aquis<PERSON>", "block.minecraft.copper_block": "Kupfablogg", "block.minecraft.copper_bulb": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_door": "Kupfa T<PERSON>", "block.minecraft.copper_grate": "Copper Grate", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "Copper Trapdoor", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Rissiche Diefnschiefaziegl", "block.minecraft.cracked_deepslate_tiles": "Rissiche <PERSON>chiefafliesn", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_polished_blackstone_bricks": "<PERSON><PERSON>icha poliada Schwoazschdoiziegl", "block.minecraft.cracked_stone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crafter": "Crafter", "block.minecraft.crafting_table": "Werkbänk", "block.minecraft.creaking_heart": "Creaking Heart", "block.minecraft.creeper_head": "Creeperku<PERSON>", "block.minecraft.creeper_wall_head": "Creeper-Wandkupf", "block.minecraft.crimson_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fungus": "Karmesinpilz", "block.minecraft.crimson_hanging_sign": "Dunkelrodshängeschildle", "block.minecraft.crimson_hyphae": "Karmesinhyphen", "block.minecraft.crimson_nylium": "Karmesin-Nezl", "block.minecraft.crimson_planks": "Karmesinbredda", "block.minecraft.crimson_pressure_plate": "Karmesindruggbladdn", "block.minecraft.crimson_roots": "Karmesinwurzln", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "Karmesinschdufn", "block.minecraft.crimson_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_wall_hanging_sign": "Dunkelrodswandschildle", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crying_obsidian": "Weinenda Obsidian", "block.minecraft.cut_copper": "Gschniddna Kupfablogg", "block.minecraft.cut_copper_slab": "Gschniddne Kupfaschdufn", "block.minecraft.cut_copper_stairs": "Gschniddne Kupfadrebbn", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.cut_red_sandstone_slab": "Gschniddene roude Sandschdoischdufn", "block.minecraft.cut_sandstone": "Gsch<PERSON><PERSON><PERSON>", "block.minecraft.cut_sandstone_slab": "Gschniddene Sandschdoischdufn", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON><PERSON> midn dü<PERSON><PERSON>", "block.minecraft.cyan_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.damaged_anvil": "Bschädigda Amboss", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "Schwoazächahulzgnobf", "block.minecraft.dark_oak_door": "Schwoazächahulzdüe", "block.minecraft.dark_oak_fence": "Schwoazächahulzzou", "block.minecraft.dark_oak_fence_gate": "Schwoazächazauntor", "block.minecraft.dark_oak_hanging_sign": "Schwazeichenhängeschildle", "block.minecraft.dark_oak_leaves": "Schwoazächalaab", "block.minecraft.dark_oak_log": "Schwoazächaschdamm", "block.minecraft.dark_oak_planks": "Schwoazächahulzbredda", "block.minecraft.dark_oak_pressure_plate": "Schwoazächahulzdruggbladdn", "block.minecraft.dark_oak_sapling": "Schwoazä<PERSON>ling", "block.minecraft.dark_oak_sign": "Schwoazächahulzschild", "block.minecraft.dark_oak_slab": "Schwoazeichnhulzschdufn", "block.minecraft.dark_oak_stairs": "Schwoazächahulzdrebbn", "block.minecraft.dark_oak_trapdoor": "Schwoazächahulzfalldüe", "block.minecraft.dark_oak_wall_hanging_sign": "Schwazeichenwandschildle", "block.minecraft.dark_oak_wall_sign": "Schwoazächa<PERSON><PERSON><PERSON>child", "block.minecraft.dark_oak_wood": "Schwoazächahulz", "block.minecraft.dark_prismarine": "Dungler <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "Du<PERSON>du<PERSON>", "block.minecraft.dark_prismarine_stairs": "Dungle <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.daylight_detector": "Dageslichdsensoa", "block.minecraft.dead_brain_coral": "<PERSON><PERSON>", "block.minecraft.dead_brain_coral_block": "Doda Herngrallnblogg", "block.minecraft.dead_brain_coral_fan": "Doda <PERSON>ng<PERSON>lnfächa", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON>lnwandfächa", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON>", "block.minecraft.dead_bubble_coral_block": "Doda Blaasngrallnblogg", "block.minecraft.dead_bubble_coral_fan": "Doda Blaasngrallnfächa", "block.minecraft.dead_bubble_coral_wall_fan": "Doda Blaasngrallnwandfächa", "block.minecraft.dead_bush": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral": "<PERSON><PERSON>", "block.minecraft.dead_fire_coral_block": "Doda Feuagrallnblogg", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON>nfä<PERSON>", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON>wan<PERSON>f<PERSON>", "block.minecraft.dead_horn_coral": "<PERSON><PERSON> Gwei<PERSON>", "block.minecraft.dead_horn_coral_block": "Doda Gweihgrallnblogg", "block.minecraft.dead_horn_coral_fan": "Doda Gweihgrallnfächa", "block.minecraft.dead_horn_coral_wall_fan": "Doda Gweihgrallnwandfächa", "block.minecraft.dead_tube_coral": "<PERSON><PERSON>", "block.minecraft.dead_tube_coral_block": "Doda Orgelgrallenblogg", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON>nfächa", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON>wandfä<PERSON>", "block.minecraft.decorated_pot": "Dekorierds Blumentöpfla", "block.minecraft.deepslate": "Diefnschiefa", "block.minecraft.deepslate_brick_slab": "Diefnschiefazieglschdufn", "block.minecraft.deepslate_brick_stairs": "Diefnschiefaziegldrebbn", "block.minecraft.deepslate_brick_wall": "Diefnschiefazieglmaua", "block.minecraft.deepslate_bricks": "Diefnschiefaziegl", "block.minecraft.deepslate_coal_ore": "Diefnschiefa-Stoakohln", "block.minecraft.deepslate_copper_ore": "Diefnschiefa-Kupfaätz", "block.minecraft.deepslate_diamond_ore": "Diefnschiefa-Diamandätz", "block.minecraft.deepslate_emerald_ore": "Diefnschiefa-Smaragdätz", "block.minecraft.deepslate_gold_ore": "Diefnschiefa-Goaldätz", "block.minecraft.deepslate_iron_ore": "Diefnschiefa-Aasnätz", "block.minecraft.deepslate_lapis_ore": "Diefnschiefa-Labislazuliätz", "block.minecraft.deepslate_redstone_ore": "Diefnschiefa-Redstone-Ätz", "block.minecraft.deepslate_tile_slab": "Diefnschiefafliesnschdufn", "block.minecraft.deepslate_tile_stairs": "Diefnschiefafliesndrebbn", "block.minecraft.deepslate_tile_wall": "Diefnschiefafliesnmaua", "block.minecraft.deepslate_tiles": "Diefnschiefafliesn", "block.minecraft.detector_rail": "Sensorschiene", "block.minecraft.diamond_block": "Diormandblogg", "block.minecraft.diamond_ore": "Diormandätz", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "Dioridschdufn", "block.minecraft.diorite_stairs": "Dioriddrebbn", "block.minecraft.diorite_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dirt": "<PERSON><PERSON><PERSON>", "block.minecraft.dirt_path": "Dramplpfad", "block.minecraft.dispenser": "Wärfa", "block.minecraft.dragon_egg": "Dr<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "Drachn-Wandkupf", "block.minecraft.dried_ghast": "<PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Schnähblūk", "block.minecraft.dripstone_block": "Droapfstoablogg", "block.minecraft.dropper": "Spenda", "block.minecraft.emerald_block": "Smaragdblogg", "block.minecraft.emerald_ore": "Smaragdäaz", "block.minecraft.enchanting_table": "Zaubadisch", "block.minecraft.end_gateway": "Enddransidpoadal", "block.minecraft.end_portal": "Endpoadal", "block.minecraft.end_portal_frame": "Endpoadalrahmn", "block.minecraft.end_rod": "Endstoab", "block.minecraft.end_stone": "Endsta<PERSON>", "block.minecraft.end_stone_brick_slab": "Endschdoizieglschdufn", "block.minecraft.end_stone_brick_stairs": "Endschdoiziegldrebbn", "block.minecraft.end_stone_brick_wall": "Endsch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_stone_bricks": "Endstoaziegl", "block.minecraft.ender_chest": "Enderdruhe", "block.minecraft.exposed_chiseled_copper": "Exposed Chiseled Copper", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_copper_bulb": "Exposed Copper Bulb", "block.minecraft.exposed_copper_door": "Exposed Copper Door", "block.minecraft.exposed_copper_grate": "Exposed Copper Grate", "block.minecraft.exposed_copper_trapdoor": "Exposed Copper Trapdoor", "block.minecraft.exposed_cut_copper": "<PERSON><PERSON><PERSON><PERSON>dd<PERSON> Ku<PERSON>ablogg", "block.minecraft.exposed_cut_copper_slab": "Anglaufne gschniddne Kupfaschdufn", "block.minecraft.exposed_cut_copper_stairs": "Anglau<PERSON>ne gschniddne Kupfadrebbn", "block.minecraft.farmland": "Aggaland", "block.minecraft.fern": "Faan", "block.minecraft.fire": "<PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_block": "Feuagrallnblogg", "block.minecraft.fire_coral_fan": "Feuagrallnfächa", "block.minecraft.fire_coral_wall_fan": "Feuagrallnwandfächa", "block.minecraft.firefly_bush": "Firefly Bush", "block.minecraft.fletching_table": "Bognertisch", "block.minecraft.flower_pot": "Blumndobf", "block.minecraft.flowering_azalea": "Blühnde Azalee", "block.minecraft.flowering_azalea_leaves": "Blühndes Azaleenlaab", "block.minecraft.frogspawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.frosted_ice": "Brüchiges Ais", "block.minecraft.furnace": "oufn", "block.minecraft.gilded_blackstone": "Goidduachzochena <PERSON>", "block.minecraft.glass": "Glos", "block.minecraft.glass_pane": "Glosschaim", "block.minecraft.glow_lichen": "Leuchdflechdn", "block.minecraft.glowstone": "Leuchdstaa", "block.minecraft.gold_block": "Goidblogg", "block.minecraft.gold_ore": "Goidätz", "block.minecraft.granite": "<PERSON><PERSON><PERSON>", "block.minecraft.granite_slab": "Graniddschdufn", "block.minecraft.granite_stairs": "Granidddrebbn", "block.minecraft.granite_wall": "Graniddma<PERSON>", "block.minecraft.grass": "<PERSON><PERSON><PERSON>", "block.minecraft.grass_block": "G<PERSON><PERSON>blogg", "block.minecraft.gravel": "<PERSON><PERSON>", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_candle_cake": "<PERSON><PERSON><PERSON> midn graua <PERSON>", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON><PERSON> glo<PERSON><PERSON>", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.green_banner": "G<PERSON><PERSON>", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON> Bedd", "block.minecraft.green_candle": "<PERSON><PERSON>", "block.minecraft.green_candle_cake": "<PERSON><PERSON><PERSON> midn grina <PERSON>", "block.minecraft.green_carpet": "<PERSON><PERSON>", "block.minecraft.green_concrete": "<PERSON><PERSON>", "block.minecraft.green_concrete_powder": "<PERSON><PERSON>", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON>", "block.minecraft.green_shulker_box": "<PERSON><PERSON>", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.green_terracotta": "<PERSON><PERSON>", "block.minecraft.green_wool": "<PERSON><PERSON>", "block.minecraft.grindstone": "Schleifstaa", "block.minecraft.hanging_roots": "Hängewuazl", "block.minecraft.hay_block": "Schdruabolln", "block.minecraft.heavy_core": "Heavy Core", "block.minecraft.heavy_weighted_pressure_plate": "Grobwägplattn", "block.minecraft.honey_block": "Honigblogg", "block.minecraft.honeycomb_block": "Knochnblogg", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "Gweihgralle", "block.minecraft.horn_coral_block": "Gweihgrallnblogg", "block.minecraft.horn_coral_fan": "Gweihgrallnfächa", "block.minecraft.horn_coral_wall_fan": "Gweihgrallnwandfächa", "block.minecraft.ice": "<PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "Bfallnde gmeißelda Schdoiziegl", "block.minecraft.infested_cobblestone": "Bfallnda Bruchschdoi", "block.minecraft.infested_cracked_stone_bricks": "Bfallnde rissicha Schdoiziegl", "block.minecraft.infested_deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.infested_mossy_stone_bricks": "Bfallnde bmoosde Schdoiziegl", "block.minecraft.infested_stone": "Bfallnda <PERSON>", "block.minecraft.infested_stone_bricks": "Bfallnde Schdoiziegl", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "Aasnblogg", "block.minecraft.iron_door": "Aasndüe", "block.minecraft.iron_ore": "Aasnätz", "block.minecraft.iron_trapdoor": "Aasnfalldüe", "block.minecraft.jack_o_lantern": "Küabisladern", "block.minecraft.jigsaw": "Veabundsblogg", "block.minecraft.jukebox": "Pladdnschbiela", "block.minecraft.jungle_button": "Droubnhulzgnobf", "block.minecraft.jungle_door": "Droubnhulzdüe", "block.minecraft.jungle_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_hanging_sign": "Dschungelhängeschildle", "block.minecraft.jungle_leaves": "Droubnboamlaab", "block.minecraft.jungle_log": "Droubnboamschdamm", "block.minecraft.jungle_planks": "Droubnhulzbredda", "block.minecraft.jungle_pressure_plate": "Droubnhulzdruggbladdn", "block.minecraft.jungle_sapling": "Droubnboamsetzling", "block.minecraft.jungle_sign": "Droubnhulzschild", "block.minecraft.jungle_slab": "Droubnhulzschdufn", "block.minecraft.jungle_stairs": "Droubnhulzdrebbn", "block.minecraft.jungle_trapdoor": "Droubnhulzfalldüe", "block.minecraft.jungle_wall_hanging_sign": "Dschungelwandhängeschildle", "block.minecraft.jungle_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.kelp": "<PERSON>tang", "block.minecraft.kelp_plant": "Seetangstängel", "block.minecraft.ladder": "Loidan", "block.minecraft.lantern": "Ladeane", "block.minecraft.lapis_block": "Labislazuliblogg", "block.minecraft.lapis_ore": "Labislazuliätz", "block.minecraft.large_amethyst_bud": "Große Amethysdknosb", "block.minecraft.large_fern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Lovagessl", "block.minecraft.leaf_litter": "Leaf Litter", "block.minecraft.lectern": "Lesebuld", "block.minecraft.lever": "<PERSON><PERSON><PERSON>", "block.minecraft.light": "Glichdablogg", "block.minecraft.light_blue_banner": "Hell<PERSON>ues Banner", "block.minecraft.light_blue_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_candle": "Hell<PERSON><PERSON>", "block.minecraft.light_blue_candle_cake": "<PERSON><PERSON><PERSON> midn hell<PERSON>", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_concrete": "Hell<PERSON><PERSON>", "block.minecraft.light_blue_concrete_powder": "Hellblaua Druggnbedon", "block.minecraft.light_blue_glazed_terracotta": "Hellblaue gloasierde <PERSON>", "block.minecraft.light_blue_shulker_box": "Hellblaue Shulkerkischdla", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_stained_glass_pane": "Hellblaue Glosschaim", "block.minecraft.light_blue_terracotta": "Hellblaue Keramig", "block.minecraft.light_blue_wool": "Hellblaue Woll", "block.minecraft.light_gray_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_candle_cake": "<PERSON><PERSON><PERSON> midn hell<PERSON><PERSON>", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_glazed_terracotta": "Hell<PERSON><PERSON> gloasi<PERSON><PERSON>", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_stained_glass_pane": "Hell<PERSON><PERSON> Glosschaim", "block.minecraft.light_gray_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_wool": "<PERSON><PERSON><PERSON> Woll", "block.minecraft.light_weighted_pressure_plate": "Fainwägplattn", "block.minecraft.lightning_rod": "Blidzableida", "block.minecraft.lilac": "Flieder", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lily_pad": "Seerosnbladd", "block.minecraft.lime_banner": "Hellgrines Banner", "block.minecraft.lime_bed": "Hell<PERSON><PERSON><PERSON> Bedd", "block.minecraft.lime_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_candle_cake": "<PERSON><PERSON><PERSON> midn <PERSON><PERSON><PERSON>", "block.minecraft.lime_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_glazed_terracotta": "<PERSON><PERSON><PERSON> glo<PERSON><PERSON>", "block.minecraft.lime_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_stained_glass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.lime_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.lodestone": "Le<PERSON><PERSON><PERSON>", "block.minecraft.loom": "Webstuhl", "block.minecraft.magenta_banner": "Magenda Banner", "block.minecraft.magenta_bed": "Magenda Bedd", "block.minecraft.magenta_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_candle_cake": "<PERSON><PERSON><PERSON> midn magenda Ki<PERSON>a", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete_powder": "Magenda Druggnbedon", "block.minecraft.magenta_glazed_terracotta": "Magenda gloasi<PERSON><PERSON>", "block.minecraft.magenta_shulker_box": "Magenda Shulkerkischdla", "block.minecraft.magenta_stained_glass": "Magenda Glos", "block.minecraft.magenta_stained_glass_pane": "Magenda Glosschaim", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_wool": "Magenda Woll", "block.minecraft.magma_block": "Magmablogg", "block.minecraft.mangrove_button": "Mangrownknöpfla", "block.minecraft.mangrove_door": "Mangrowndir", "block.minecraft.mangrove_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_hanging_sign": "Mangrownhängeschildle", "block.minecraft.mangrove_leaves": "<PERSON><PERSON>blädder", "block.minecraft.mangrove_log": "Mangrownstämmla", "block.minecraft.mangrove_planks": "Mangrownbredder", "block.minecraft.mangrove_pressure_plate": "Mangrowndruggbladdn", "block.minecraft.mangrove_propagule": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_roots": "Mangrownwu<PERSON><PERSON>", "block.minecraft.mangrove_sign": "Mangrownschildla", "block.minecraft.mangrove_slab": "Mangrownstufn", "block.minecraft.mangrove_stairs": "Mangrowntrebbn", "block.minecraft.mangrove_trapdoor": "Mangrownfalldür", "block.minecraft.mangrove_wall_hanging_sign": "Mangrownwandhängeschildle", "block.minecraft.mangrove_wall_sign": "Mangrown<PERSON>ds<PERSON>la", "block.minecraft.mangrove_wood": "Man<PERSON><PERSON><PERSON>", "block.minecraft.medium_amethyst_bud": "Mittläre Amethysdknosb", "block.minecraft.melon": "Melon", "block.minecraft.melon_stem": "Melonpflanzn", "block.minecraft.moss_block": "Moosblogg", "block.minecraft.moss_carpet": "Moosteppich", "block.minecraft.mossy_cobblestone": "Bmoosda Bruchschdoi", "block.minecraft.mossy_cobblestone_slab": "Bmoosde Bruchschdoischdufn", "block.minecraft.mossy_cobblestone_stairs": "Bmoosde Bruchschdoidrebbn", "block.minecraft.mossy_cobblestone_wall": "Bmoosde Bruchschdoimaua", "block.minecraft.mossy_stone_brick_slab": "Bmoosde Stoazieglschdufn", "block.minecraft.mossy_stone_brick_stairs": "Bmoosde Schdoiziegldrebbn", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_stone_bricks": "Bmoosde Stoaziegl", "block.minecraft.moving_piston": "Bwegda Blogg", "block.minecraft.mud": "Schlamm", "block.minecraft.mud_brick_slab": "Schlammziechlstufn", "block.minecraft.mud_brick_stairs": "Schlammziechltrebbn", "block.minecraft.mud_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mushroom_stem": "Bilzschdiel", "block.minecraft.mycelium": "Myzl", "block.minecraft.nether_brick_fence": "Netherzieglzou", "block.minecraft.nether_brick_slab": "Netherzieglschdufn", "block.minecraft.nether_brick_stairs": "Netherziegldrebbn", "block.minecraft.nether_brick_wall": "Netherzieglmaua", "block.minecraft.nether_bricks": "Netherziegl", "block.minecraft.nether_gold_ore": "Nethergoidätz", "block.minecraft.nether_portal": "Netherpoadal", "block.minecraft.nether_quartz_ore": "Netherkwarzätz", "block.minecraft.nether_sprouts": "Nethersprossn", "block.minecraft.nether_wart": "Netherwarzn", "block.minecraft.nether_wart_block": "Netherwarzblogg", "block.minecraft.netherite_block": "Netheritblogg", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Nodnblogg", "block.minecraft.oak_button": "Ächagnobf", "block.minecraft.oak_door": "Ächahulzdüe", "block.minecraft.oak_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_hanging_sign": "Aichnhängeschildle", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_log": "Ächaschdamm", "block.minecraft.oak_planks": "Ächahulzbredda", "block.minecraft.oak_pressure_plate": "Ächahulzdruggbladdn", "block.minecraft.oak_sapling": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_sign": "Ächahulzschild", "block.minecraft.oak_slab": "Ächahulzschdufn", "block.minecraft.oak_stairs": "Ächahulzdrebbn", "block.minecraft.oak_trapdoor": "Ächahulzfalldüe", "block.minecraft.oak_wall_hanging_sign": "Auchnwandhängeschildla", "block.minecraft.oak_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.observer": "<PERSON><PERSON><PERSON>", "block.minecraft.obsidian": "Obsidian", "block.minecraft.ochre_froglight": "<PERSON>gger<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ominous_banner": "Unhailvolles Banna", "block.minecraft.open_eyeblossom": "Open Eyeblossom", "block.minecraft.orange_banner": "Oranges Banner", "block.minecraft.orange_bed": "Oranges Bedd", "block.minecraft.orange_candle": "Oranga Kirza", "block.minecraft.orange_candle_cake": "Kuchn midn oranga Kirza", "block.minecraft.orange_carpet": "Or<PERSON>", "block.minecraft.orange_concrete": "Oranga Bedon", "block.minecraft.orange_concrete_powder": "Oranga Druggnbedon", "block.minecraft.orange_glazed_terracotta": "Orange gloasierde <PERSON>ig", "block.minecraft.orange_shulker_box": "Orange Shulkerkischdla", "block.minecraft.orange_stained_glass": "Oranges Glos", "block.minecraft.orange_stained_glass_pane": "Orange Glosschaim", "block.minecraft.orange_terracotta": "Orange Keramig", "block.minecraft.orange_tulip": "Orange Dulbn", "block.minecraft.orange_wool": "Orange Woll", "block.minecraft.oxeye_daisy": "Ma<PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "Oxidized Chiseled Copper", "block.minecraft.oxidized_copper": "Oxidieada Kupfablogg", "block.minecraft.oxidized_copper_bulb": "Oxidized Copper Bulb", "block.minecraft.oxidized_copper_door": "Oxidized Copper Door", "block.minecraft.oxidized_copper_grate": "Oxidized Copper Grate", "block.minecraft.oxidized_copper_trapdoor": "Oxidized Copper Trapdoor", "block.minecraft.oxidized_cut_copper": "Oxidieada gschniddna Kupfablogg", "block.minecraft.oxidized_cut_copper_slab": "Oxidiade gschniddne Kupfaschdufn", "block.minecraft.oxidized_cut_copper_stairs": "Oxidiade gschniddne Kupfadrebbn", "block.minecraft.packed_ice": "Baggais", "block.minecraft.packed_mud": "Zampackder Schlamm", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON>", "block.minecraft.pale_moss_block": "Pale Moss Block", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON> Moss Carpet", "block.minecraft.pale_oak_button": "Pale Oak Button", "block.minecraft.pale_oak_door": "Pale Oak Door", "block.minecraft.pale_oak_fence": "Pale Oak Fence", "block.minecraft.pale_oak_fence_gate": "Pale Oak Fence Gate", "block.minecraft.pale_oak_hanging_sign": "Pale Oak Hanging Sign", "block.minecraft.pale_oak_leaves": "Pale Oak Leaves", "block.minecraft.pale_oak_log": "Pale Oak Log", "block.minecraft.pale_oak_planks": "Pale Oak Planks", "block.minecraft.pale_oak_pressure_plate": "Pale Oak Pressure Plate", "block.minecraft.pale_oak_sapling": "Pale Oak Sapling", "block.minecraft.pale_oak_sign": "Pale Oak Sign", "block.minecraft.pale_oak_slab": "Pale Oak Slab", "block.minecraft.pale_oak_stairs": "Pale Oak Stairs", "block.minecraft.pale_oak_trapdoor": "Pale Oak Trapdoor", "block.minecraft.pale_oak_wall_hanging_sign": "Pale Oak Wall Hanging Sign", "block.minecraft.pale_oak_wall_sign": "Pale Oak Wall Sign", "block.minecraft.pale_oak_wood": "Pale Oak Wood", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.peony": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.petrified_oak_slab": "Verschdainada Eichnschdufn", "block.minecraft.piglin_head": "Piglinkupf", "block.minecraft.piglin_wall_head": "Piglinwandkupf", "block.minecraft.pink_banner": "Bingges Banner", "block.minecraft.pink_bed": "Bingges Bedd", "block.minecraft.pink_candle": "<PERSON><PERSON>", "block.minecraft.pink_candle_cake": "<PERSON><PERSON><PERSON> midn bingga <PERSON>", "block.minecraft.pink_carpet": "<PERSON><PERSON>", "block.minecraft.pink_concrete": "<PERSON><PERSON>", "block.minecraft.pink_concrete_powder": "Bingga Druggnbedon", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON> glo<PERSON>", "block.minecraft.pink_petals": "<PERSON>", "block.minecraft.pink_shulker_box": "<PERSON>ge Shu<PERSON>erkischdla", "block.minecraft.pink_stained_glass": "Bingges Glos", "block.minecraft.pink_stained_glass_pane": "<PERSON><PERSON>im", "block.minecraft.pink_terracotta": "<PERSON><PERSON>", "block.minecraft.pink_tulip": "<PERSON><PERSON>", "block.minecraft.pink_wool": "<PERSON><PERSON>", "block.minecraft.piston": "Kolbm", "block.minecraft.piston_head": "Kolbmkupf", "block.minecraft.pitcher_crop": "Pitcher C<PERSON>", "block.minecraft.pitcher_plant": "Pitcher Plant", "block.minecraft.player_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head.named": "<PERSON><PERSON> von %s", "block.minecraft.player_wall_head": "Schbiela-Wandkupf", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "Spitza Droapfstoa", "block.minecraft.polished_andesite": "Polied<PERSON>", "block.minecraft.polished_andesite_slab": "Poliada Andesiddschdufn", "block.minecraft.polished_andesite_stairs": "Poliada Andesidddrebbn", "block.minecraft.polished_basalt": "Poliada Basald", "block.minecraft.polished_blackstone": "Poliada Schwoazschdoi", "block.minecraft.polished_blackstone_brick_slab": "Poliada Schwoazschdoizieglschdufn", "block.minecraft.polished_blackstone_brick_stairs": "Poliada Schwoazschdoiziegldrebbn", "block.minecraft.polished_blackstone_brick_wall": "Poliada Schwoazschdoizieglmaua", "block.minecraft.polished_blackstone_bricks": "Poliada Schwoazschdoiziegl", "block.minecraft.polished_blackstone_button": "Poliada Schwoazschdo<PERSON>obf", "block.minecraft.polished_blackstone_pressure_plate": "Poliada Schwoazschdoidruggbladdn", "block.minecraft.polished_blackstone_slab": "Poliada Schwoazschdoischdufn", "block.minecraft.polished_blackstone_stairs": "Poliada Schwoazschdoidrebbn", "block.minecraft.polished_blackstone_wall": "Poliada Schwoazschdoimaua", "block.minecraft.polished_deepslate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate_slab": "Po<PERSON><PERSON><PERSON>n", "block.minecraft.polished_deepslate_stairs": "Poalieade Diefnschiefadrebbn", "block.minecraft.polished_deepslate_wall": "Poali<PERSON><PERSON>", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite_slab": "Poliada Dioridschdufn", "block.minecraft.polished_diorite_stairs": "Poliada Dioriddrebbn", "block.minecraft.polished_granite": "Poliada Granidd", "block.minecraft.polished_granite_slab": "Poliada Graniddschdufn", "block.minecraft.polished_granite_stairs": "Poliada Granidddrebbn", "block.minecraft.polished_tuff": "Polished <PERSON>", "block.minecraft.polished_tuff_slab": "Polished <PERSON><PERSON>", "block.minecraft.polished_tuff_stairs": "Polished <PERSON><PERSON>", "block.minecraft.polished_tuff_wall": "Polished <PERSON><PERSON>", "block.minecraft.poppy": "Mōn", "block.minecraft.potatoes": "Eardäpfl", "block.minecraft.potted_acacia_sapling": "Eingetopfta Akaziensetzling", "block.minecraft.potted_allium": "Eingetopfta Ziealauch", "block.minecraft.potted_azalea_bush": "Eingetopfte Azalee", "block.minecraft.potted_azure_bluet": "Eingetopfta Boazellansteanchn", "block.minecraft.potted_bamboo": "Eingetopfta Bambus", "block.minecraft.potted_birch_sapling": "Eingetopfta Berknsetzling", "block.minecraft.potted_blue_orchid": "Eingetopfta blaua Orchideen", "block.minecraft.potted_brown_mushroom": "Eingetopfta broauna Bilz", "block.minecraft.potted_cactus": "Eingetopfta Kakdus", "block.minecraft.potted_cherry_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_closed_eyeblossom": "Potted Closed Eyeblossom", "block.minecraft.potted_cornflower": "Eingetopfta Kornbluma", "block.minecraft.potted_crimson_fungus": "Eingedopfda Karmesinbilz", "block.minecraft.potted_crimson_roots": "Eingedopfde Karmesinwuazeln", "block.minecraft.potted_dandelion": "Eingetopft<PERSON>", "block.minecraft.potted_dark_oak_sapling": "Eingetopfta Schwoazächasetzling", "block.minecraft.potted_dead_bush": "Eingetopfta doda Bischl", "block.minecraft.potted_fern": "Eingetopfta Faan", "block.minecraft.potted_flowering_azalea_bush": "Eingetopfte blühnde Azalee", "block.minecraft.potted_jungle_sapling": "Eingetopfta Droubnboamsetzling", "block.minecraft.potted_lily_of_the_valley": "Eingetopfta Maiglöckchn", "block.minecraft.potted_mangrove_propagule": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_oak_sapling": "Eingetopfta Ächasetzling", "block.minecraft.potted_open_eyeblossom": "Potted Open Eyeblossom", "block.minecraft.potted_orange_tulip": "Eingetopfta orange Dulbn", "block.minecraft.potted_oxeye_daisy": "Eingetopfte Magaride", "block.minecraft.potted_pale_oak_sapling": "Potted Pale Oak Sapling", "block.minecraft.potted_pink_tulip": "Eingetopfta bingge Dulbn", "block.minecraft.potted_poppy": "Eingetopfta Mōn", "block.minecraft.potted_red_mushroom": "Eingetopfta rouda <PERSON>", "block.minecraft.potted_red_tulip": "Eingetopfta roude Dulbn", "block.minecraft.potted_spruce_sapling": "Eingetopfta Fichdnsetzling", "block.minecraft.potted_torchflower": "Antopfte Faggelilien", "block.minecraft.potted_warped_fungus": "Eingedopfda Wirrbilz", "block.minecraft.potted_warped_roots": "Eingedopfde Wirrwuazeln", "block.minecraft.potted_white_tulip": "Eingetopfta woaßa Dulbn", "block.minecraft.potted_wither_rose": "Eingetopfta Witherrousn", "block.minecraft.powder_snow": "Bulverschnäh", "block.minecraft.powder_snow_cauldron": "Bulverschnähgessl", "block.minecraft.powered_rail": "Ontriebschiena", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Brismarinziegelschdufn", "block.minecraft.prismarine_brick_stairs": "Prismarinziegldrebbn", "block.minecraft.prismarine_bricks": "Brismarinblogg", "block.minecraft.prismarine_slab": "Brismarinschdufn", "block.minecraft.prismarine_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "Küabispflanzn", "block.minecraft.purple_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_candle_cake": "<PERSON><PERSON><PERSON> midn viol<PERSON><PERSON>", "block.minecraft.purple_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "Violedda <PERSON>gn<PERSON>on", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> glo<PERSON><PERSON>", "block.minecraft.purple_shulker_box": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_stained_glass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purpur_block": "Purpurblogg", "block.minecraft.purpur_pillar": "P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purpur_slab": "Purpurschdufn", "block.minecraft.purpur_stairs": "Purpurdrebbn", "block.minecraft.quartz_block": "Kwarzblogg", "block.minecraft.quartz_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_pillar": "Kwarzsäuln", "block.minecraft.quartz_slab": "Kwarzschdufn", "block.minecraft.quartz_stairs": "Kwarzdrebbn", "block.minecraft.rail": "<PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "Rohkupfablogg", "block.minecraft.raw_gold_block": "Roggoidblogg", "block.minecraft.raw_iron_block": "Rohaasnblogg", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.red_candle_cake": "<PERSON><PERSON><PERSON> midn rouda <PERSON>", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.red_glazed_terracotta": "<PERSON><PERSON><PERSON> glo<PERSON><PERSON><PERSON>", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON>", "block.minecraft.red_mushroom_block": "<PERSON><PERSON><PERSON>", "block.minecraft.red_nether_brick_slab": "Roude Netherzieglschdufn", "block.minecraft.red_nether_brick_stairs": "Roude Netherziegldrebbn", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.red_nether_bricks": "<PERSON><PERSON>ziegl", "block.minecraft.red_sand": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.red_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.red_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.red_wool": "<PERSON><PERSON><PERSON> W<PERSON>", "block.minecraft.redstone_block": "Redstone-Blogg", "block.minecraft.redstone_lamp": "Redstone-Lambm", "block.minecraft.redstone_ore": "Redstone-Ätz", "block.minecraft.redstone_torch": "Redstone-Faggl", "block.minecraft.redstone_wall_torch": "Redstone-Wandfaggl", "block.minecraft.redstone_wire": "Redstone-Kobl", "block.minecraft.reinforced_deepslate": "Verstärggder Diefnschiefer", "block.minecraft.repeater": "Redstone-Verstärka", "block.minecraft.repeating_command_block": "Wiedahol-Bfehlsblogg", "block.minecraft.resin_block": "Block of Resin", "block.minecraft.resin_brick_slab": "Resin Brick Slab", "block.minecraft.resin_brick_stairs": "Resin Brick Stairs", "block.minecraft.resin_brick_wall": "Resin Brick Wall", "block.minecraft.resin_bricks": "Resin Bricks", "block.minecraft.resin_clump": "<PERSON><PERSON>", "block.minecraft.respawn_anchor": "Seelnanka", "block.minecraft.rooted_dirt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sand": "Sand", "block.minecraft.sandstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sandstone_slab": "Sandschdoischdufn", "block.minecraft.sandstone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sandstone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.scaffolding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk-Gaddalisador", "block.minecraft.sculk_sensor": "Sculk-Sensoa", "block.minecraft.sculk_shrieker": "Sculk-Graner", "block.minecraft.sculk_vein": "Sculk-Ader", "block.minecraft.sea_lantern": "<PERSON><PERSON><PERSON>", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "<PERSON><PERSON>", "block.minecraft.set_spawn": "Wiedaeinstigspunkd gsetzd", "block.minecraft.short_dry_grass": "Short Dry Grass", "block.minecraft.short_grass": "Short Grass", "block.minecraft.shroomlight": "Pilzliachd", "block.minecraft.shulker_box": "Shulkerkischdla", "block.minecraft.skeleton_skull": "<PERSON><PERSON>edd<PERSON>dl", "block.minecraft.skeleton_wall_skull": "Sgeledd-Wandschedl", "block.minecraft.slime_block": "Schloimblogg", "block.minecraft.small_amethyst_bud": "Glane Amethysdknosb", "block.minecraft.small_dripleaf": "<PERSON><PERSON><PERSON>", "block.minecraft.smithing_table": "Schmiedetisch", "block.minecraft.smoker": "Räuchaofn", "block.minecraft.smooth_basalt": "Gladda Basald", "block.minecraft.smooth_quartz": "Gladda Kwarzblogg", "block.minecraft.smooth_quartz_slab": "Gladde Kwarzschdufn", "block.minecraft.smooth_quartz_stairs": "Gladde Kwarzdrebbn", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON> r<PERSON>", "block.minecraft.smooth_red_sandstone_slab": "Gladde roude Sands<PERSON>do<PERSON>dufn", "block.minecraft.smooth_red_sandstone_stairs": "Gladde roude <PERSON>n", "block.minecraft.smooth_sandstone": "Gladda <PERSON>", "block.minecraft.smooth_sandstone_slab": "Gladde <PERSON>n", "block.minecraft.smooth_sandstone_stairs": "<PERSON><PERSON>", "block.minecraft.smooth_stone": "<PERSON><PERSON>", "block.minecraft.smooth_stone_slab": "Gladde <PERSON>n", "block.minecraft.sniffer_egg": "Schnüffler-Ei", "block.minecraft.snow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.snow_block": "Schnähblogg", "block.minecraft.soul_campfire": "Seelnlagafeua", "block.minecraft.soul_fire": "Seelnfeua", "block.minecraft.soul_lantern": "Seelnlaterne", "block.minecraft.soul_sand": "Seelnsand", "block.minecraft.soul_soil": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_torch": "Seelnfaggl", "block.minecraft.soul_wall_torch": "Seelnwandfaggl", "block.minecraft.spawn.not_valid": "<PERSON> hasd ka <PERSON>d und kenen uffgladenen Seelnanka, oda dein Wiedaeinstiegspungd wuad blokierd", "block.minecraft.spawner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spawner.desc1": "Inderaggzion mit am Erschaffungsei:", "block.minecraft.spawner.desc2": "Sets Mob Type", "block.minecraft.sponge": "<PERSON><PERSON><PERSON>", "block.minecraft.spore_blossom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_button": "Fichdngnobf", "block.minecraft.spruce_door": "Fichdnhulzdüe", "block.minecraft.spruce_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence_gate": "Fichdnhul<PERSON>unt<PERSON>", "block.minecraft.spruce_hanging_sign": "Spruce Hanging Sign", "block.minecraft.spruce_leaves": "Fichdnnoadln", "block.minecraft.spruce_log": "Fichdnschdamm", "block.minecraft.spruce_planks": "Fichdnhulzbredda", "block.minecraft.spruce_pressure_plate": "Fichdnhulzdruggbladdn", "block.minecraft.spruce_sapling": "<PERSON><PERSON>dn<PERSON><PERSON>ling", "block.minecraft.spruce_sign": "Fichdnhulzschild", "block.minecraft.spruce_slab": "Fichdnhulzschdufn", "block.minecraft.spruce_stairs": "Fichdnhulzdrebbn", "block.minecraft.spruce_trapdoor": "Fichdnhulzfalldüe", "block.minecraft.spruce_wall_hanging_sign": "Spruce Wall Hanging Sign", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>child", "block.minecraft.spruce_wood": "Fichdnhulz", "block.minecraft.sticky_piston": "Klebriga Kolbm", "block.minecraft.stone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Schdoizieglschdufn", "block.minecraft.stone_brick_stairs": "Schdoiziegldrebbn", "block.minecraft.stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_bricks": "St<PERSON> Zeigel", "block.minecraft.stone_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_pressure_plate": "Staadruggbladdn", "block.minecraft.stone_slab": "Sc<PERSON><PERSON>ischdufn", "block.minecraft.stone_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Endrindeda Akazienschdamm", "block.minecraft.stripped_acacia_wood": "Endrindeds A<PERSON>", "block.minecraft.stripped_bamboo_block": "Block of Stripped Bamboo", "block.minecraft.stripped_birch_log": "Endrindeda Berknschdamm", "block.minecraft.stripped_birch_wood": "Endrindeds Berknhulz", "block.minecraft.stripped_cherry_log": "Stripped Cherry Log", "block.minecraft.stripped_cherry_wood": "Stripped Cherry Wood", "block.minecraft.stripped_crimson_hyphae": "Geschälte Karmesinhyphen", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_dark_oak_log": "Endrindeda Schwoazächaschdamm", "block.minecraft.stripped_dark_oak_wood": "Endindeds Schwoazächahulz", "block.minecraft.stripped_jungle_log": "<PERSON><PERSON><PERSON><PERSON>dam<PERSON>", "block.minecraft.stripped_jungle_wood": "<PERSON>rin<PERSON><PERSON>", "block.minecraft.stripped_mangrove_log": "Stripped Mangrove Log", "block.minecraft.stripped_mangrove_wood": "Stripped Mangrove Wood", "block.minecraft.stripped_oak_log": "Endrindeda Ächaschdamm", "block.minecraft.stripped_oak_wood": "Endindeds Ächahulz", "block.minecraft.stripped_pale_oak_log": "Stripped Pale Oak Log", "block.minecraft.stripped_pale_oak_wood": "Stripped Pale Oak Wood", "block.minecraft.stripped_spruce_log": "Endrindeda Fichdnschdamm", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_warped_hyphae": "Geschälte Wirrhyphen", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.structure_block": "Konsdrugtiondsblogg", "block.minecraft.structure_void": "Konsdrugtiondsleere", "block.minecraft.sugar_cane": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sunflower": "Sunnebloeme", "block.minecraft.suspicious_gravel": "Suspicious Gravel", "block.minecraft.suspicious_sand": "Suspicious Sand", "block.minecraft.sweet_berry_bush": "Süßbeernschtrach", "block.minecraft.tall_dry_grass": "Tall Dry Grass", "block.minecraft.tall_grass": "<PERSON><PERSON><PERSON>s Groos", "block.minecraft.tall_seagrass": "<PERSON><PERSON><PERSON>s Seegroos", "block.minecraft.target": "Zielscheibenblogg", "block.minecraft.terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.test_block": "Test Block", "block.minecraft.test_instance_block": "Test Instance Block", "block.minecraft.tinted_glass": "Gfärbdes Glos", "block.minecraft.tnt": "DND", "block.minecraft.tnt.disabled": "TNT explosions are disabled", "block.minecraft.torch": "Faggl", "block.minecraft.torchflower": "Torch<PERSON>", "block.minecraft.torchflower_crop": "Torchflower <PERSON>", "block.minecraft.trapped_chest": "Redstone-<PERSON><PERSON><PERSON>", "block.minecraft.trial_spawner": "Trial Spawner", "block.minecraft.tripwire": "Stolbadrahd", "block.minecraft.tripwire_hook": "Hakn", "block.minecraft.tube_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tube_coral_block": "Orgelgrallnblogg", "block.minecraft.tube_coral_fan": "Orgelgrallnfächa", "block.minecraft.tube_coral_wall_fan": "Orgelgrallnwandfächa", "block.minecraft.tuff": "Duffstoa", "block.minecraft.tuff_brick_slab": "Tuff Brick Slab", "block.minecraft.tuff_brick_stairs": "Tuff Brick Stairs", "block.minecraft.tuff_brick_wall": "Tuff Brick Wall", "block.minecraft.tuff_bricks": "<PERSON>ff Bricks", "block.minecraft.tuff_slab": "<PERSON><PERSON>", "block.minecraft.tuff_stairs": "<PERSON><PERSON> St<PERSON>s", "block.minecraft.tuff_wall": "<PERSON><PERSON>", "block.minecraft.turtle_egg": "Schildgrödnei", "block.minecraft.twisting_vines": "Zwiablrankn", "block.minecraft.twisting_vines_plant": "Zwiablranknpflanz", "block.minecraft.vault": "<PERSON><PERSON>", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON>", "block.minecraft.vine": "Rankn", "block.minecraft.void_air": "Leernlufd", "block.minecraft.wall_torch": "Ko<PERSON><PERSON>dfaggl", "block.minecraft.warped_button": "Wirrknopf", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fence": "Wirrzaun", "block.minecraft.warped_fence_gate": "W<PERSON>rzaundü<PERSON>", "block.minecraft.warped_fungus": "W<PERSON>rpilz", "block.minecraft.warped_hanging_sign": "Warped Hanging Sign", "block.minecraft.warped_hyphae": "Wirrhyphen", "block.minecraft.warped_nylium": "Wirr-Nezl", "block.minecraft.warped_planks": "Wirrbredda", "block.minecraft.warped_pressure_plate": "Wirrdruggbladdn", "block.minecraft.warped_roots": "Wirrwurzln", "block.minecraft.warped_sign": "Wirrschild", "block.minecraft.warped_slab": "Wirrschdufn", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_stem": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_trapdoor": "Wirrfalldüe", "block.minecraft.warped_wall_hanging_sign": "Warped Wall Hanging Sign", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wart_block": "Wirrwarznblogg", "block.minecraft.water": "<PERSON><PERSON>", "block.minecraft.water_cauldron": "Wassagessl", "block.minecraft.waxed_chiseled_copper": "Waxed Chiseled Copper", "block.minecraft.waxed_copper_block": "Gwachsda Kupfablogg", "block.minecraft.waxed_copper_bulb": "Waxed Copper Bulb", "block.minecraft.waxed_copper_door": "Waxed Copper Door", "block.minecraft.waxed_copper_grate": "Waxed Copper Grate", "block.minecraft.waxed_copper_trapdoor": "Waxed Copper Trapdoor", "block.minecraft.waxed_cut_copper": "Gwachsda gschniddna Kupfablogg", "block.minecraft.waxed_cut_copper_slab": "Gwachste gschniddene Kupfaschdufn", "block.minecraft.waxed_cut_copper_stairs": "Gwachsde gschniddne Kupfadrebbn", "block.minecraft.waxed_exposed_chiseled_copper": "Waxed Exposed Chiseled Copper", "block.minecraft.waxed_exposed_copper": "Gwachsda anglaufna Kupfablogg", "block.minecraft.waxed_exposed_copper_bulb": "Waxed Exposed Copper Bulb", "block.minecraft.waxed_exposed_copper_door": "Waxed Exposed Copper Door", "block.minecraft.waxed_exposed_copper_grate": "Waxed Exposed Copper Grate", "block.minecraft.waxed_exposed_copper_trapdoor": "Waxed Exposed Copper Trapdoor", "block.minecraft.waxed_exposed_cut_copper": "Gwachsda anglaufna gschniddna Kupfablogg", "block.minecraft.waxed_exposed_cut_copper_slab": "Gwachste anglaufne gschniddene Kupfaschdufn", "block.minecraft.waxed_exposed_cut_copper_stairs": "Gwachsde anglaufne gschniddne Kupfadrebbn", "block.minecraft.waxed_oxidized_chiseled_copper": "Waxed Oxidized Chiseled Copper", "block.minecraft.waxed_oxidized_copper": "Gwachsda oxidieada Kupfablogg", "block.minecraft.waxed_oxidized_copper_bulb": "Waxed Oxidized Copper Bulb", "block.minecraft.waxed_oxidized_copper_door": "Waxed Oxidized Copper Door", "block.minecraft.waxed_oxidized_copper_grate": "Waxed Oxidized Copper Grate", "block.minecraft.waxed_oxidized_copper_trapdoor": "Waxed Oxidized Copper Trapdoor", "block.minecraft.waxed_oxidized_cut_copper": "Gwachsda oxidieada gschniddna Kupfablogg", "block.minecraft.waxed_oxidized_cut_copper_slab": "Gwachsde oxidieade gschniddne Kupfaschdufn", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Gwachsde oxidieade gschniddne Kupfadrebbn", "block.minecraft.waxed_weathered_chiseled_copper": "Waxed Weathered Chiseled Copper", "block.minecraft.waxed_weathered_copper": "Gwachsda veawiddada Kupfablogg", "block.minecraft.waxed_weathered_copper_bulb": "Waxed Weathered Copper Bulb", "block.minecraft.waxed_weathered_copper_door": "Waxed Weathered Copper Door", "block.minecraft.waxed_weathered_copper_grate": "Waxed Weathered Copper Grate", "block.minecraft.waxed_weathered_copper_trapdoor": "Waxed Weathered Copper Trapdoor", "block.minecraft.waxed_weathered_cut_copper": "Gwachsda veawiddada gschniddna Kupfablogg", "block.minecraft.waxed_weathered_cut_copper_slab": "Gwachsde veawiddade gschniddne Kupfaschdufn", "block.minecraft.waxed_weathered_cut_copper_stairs": "Gwachsde veawiddade gschniddne Kupfadrebbn", "block.minecraft.weathered_chiseled_copper": "Weathered Chiseled Copper", "block.minecraft.weathered_copper": "Veawiddada Ku<PERSON>ab<PERSON>g", "block.minecraft.weathered_copper_bulb": "Weathered Copper Bulb", "block.minecraft.weathered_copper_door": "Weathered Copper Door", "block.minecraft.weathered_copper_grate": "Weathered Copper Grate", "block.minecraft.weathered_copper_trapdoor": "Weathered Copper Trapdoor", "block.minecraft.weathered_cut_copper": "Veawiddada gschniddna Kupfablogg", "block.minecraft.weathered_cut_copper_slab": "Veawiddade gschniddne Kupfaschdufn", "block.minecraft.weathered_cut_copper_stairs": "Veawiddade gschniddne Kupfadrebbn", "block.minecraft.weeping_vines": "Trauerrankn", "block.minecraft.weeping_vines_plant": "Trauerranknpflanz", "block.minecraft.wet_sponge": "<PERSON><PERSON>", "block.minecraft.wheat": "Waiznpflanze", "block.minecraft.white_banner": "Woaßes Banner", "block.minecraft.white_bed": "Woaßes Bedd", "block.minecraft.white_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_candle_cake": "<PERSON><PERSON><PERSON> midn woaßa <PERSON>", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>on", "block.minecraft.white_glazed_terracotta": "Woaße gloasierde Keramig", "block.minecraft.white_shulker_box": "Woaße Shulkerkischdla", "block.minecraft.white_stained_glass": "Woaßes Glos", "block.minecraft.white_stained_glass_pane": "Woaße Glosschaim", "block.minecraft.white_terracotta": "Woaße Keramig", "block.minecraft.white_tulip": "Woaße Dulbn", "block.minecraft.white_wool": "Woaße Woll", "block.minecraft.wildflowers": "Wildflowers", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_wall_skull": "Withersgeledd-Wandschedl", "block.minecraft.yellow_banner": "Gelbes Banner", "block.minecraft.yellow_bed": "Gelbes Bedd", "block.minecraft.yellow_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_candle_cake": "<PERSON><PERSON><PERSON> midn gelba <PERSON>", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "Gelba <PERSON>", "block.minecraft.yellow_glazed_terracotta": "Gelbe gloasierde <PERSON>", "block.minecraft.yellow_shulker_box": "Gelbe <PERSON>dla", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass_pane": "Gelbe Glosschaim", "block.minecraft.yellow_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_wool": "Gelbe Woll", "block.minecraft.zombie_head": "<PERSON><PERSON><PERSON>", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "book.byAuthor": "von %1$s", "book.edit.title": "Book Edit Screen", "book.editTitle": "Bouchdiddl druffschreim:", "book.finalizeButton": "End<PERSON><PERSON><PERSON><PERSON> undeschreim", "book.finalizeWarning": "Vosichd: <PERSON><PERSON> <PERSON> Bouch uneschreibsd, kanns<PERSON> nimme beabbeidn.", "book.generation.0": "Originol", "book.generation.1": "Kobie vom Originol", "book.generation.2": "<PERSON><PERSON>", "book.generation.3": "Zrissn", "book.invalid.tag": "* Ungüld<PERSON> Buchdodn *", "book.pageIndicator": "Seide %1$s von %2$s", "book.page_button.next": "Next Page", "book.page_button.previous": "Previous Page", "book.sign.title": "Book Sign Screen", "book.sign.titlebox": "Title", "book.signButton": "Undeschreim", "book.view.title": "Book View Screen", "build.tooHigh": "Das Limid dea <PERSON>hn bedrägd %s", "chat.cannotSend": "Chat-Nochrichd konnde ned vesended wern", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Gligg druff zum Deleboadiern", "chat.copy": "<PERSON> gobiern", "chat.copy.click": "<PERSON><PERSON><PERSON>, um in die Zwischnablache zu kobiern", "chat.deleted_marker": "This chat message has been deleted by the server.", "chat.disabled.chain_broken": "<PERSON><PERSON> disabled due to broken chain. Please try reconnecting.", "chat.disabled.expiredProfileKey": "<PERSON><PERSON> disabled due to expired profile public key. Please try reconnecting.", "chat.disabled.invalid_command_signature": "Command had unexpected or missing command argument signatures.", "chat.disabled.invalid_signature": "<PERSON><PERSON> had an invalid signature. Please try reconnecting.", "chat.disabled.launcher": "Chat disabled by launcher option. Cannot send message.", "chat.disabled.missingProfileKey": "<PERSON><PERSON> disabled due to missing profile public key. Please try reconnecting.", "chat.disabled.options": "Chat disabled in client options.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON> received out-of-order. Did your system time change?", "chat.disabled.profile": "Chat is not allowed by account settings. Press '%s' again for more information.", "chat.disabled.profile.moreInfo": "Chat is not allowed by account settings. Cannot send or view messages.", "chat.editBox": "Cha<PERSON>", "chat.filtered": "Filtered by the server.", "chat.filtered_full": "The server has hidden your message for some players.", "chat.link.confirm": "Mechersd de folchende Seide wigglich effne?", "chat.link.confirmTrusted": "<PERSON><PERSON>d den Link effne oder inne Zwischenabloche neidoun?", "chat.link.open": "<PERSON><PERSON>", "chat.link.warning": "Dass du mir fei kane <PERSON>, den du ded vedrausd, effnesd!", "chat.queue": "[+%s ausstehnde Zeiln]", "chat.square_brackets": "[%s]", "chat.tag.error": "Server sent invalid message.", "chat.tag.modified": "Message modified by the server. Original:", "chat.tag.not_secure": "Unverified message. Cannot be reported.", "chat.tag.system": "Server message. Cannot be reported.", "chat.tag.system_single_player": "Server message.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s had de Aufgab %s erledichd", "chat.type.advancement.goal": "%s hads Ziel %s gschaffd", "chat.type.advancement.task": "%s had den Foadschridd %s gschaffd", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Team benochrichdign", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s sachd %s", "chat.validation_error": "Chat validation error", "chat_screen.message": "Zu sändendä Nochrichd: %s", "chat_screen.title": "G`wädzfänsda", "chat_screen.usage": "Gäbe die Nochrichd oin und drügge Endda zum Sänden", "chunk.toast.checkLog": "See log for more details", "chunk.toast.loadFailure": "Failed to load chunk at %s", "chunk.toast.lowDiskSpace": "Low disk space!", "chunk.toast.lowDiskSpace.description": "Might not be able to save the world.", "chunk.toast.saveFailure": "Failed to save chunk at %s", "clear.failed.multiple": "<PERSON>ine Gägnstende wurdän boi dän Sch`bieler %s g`fundän", "clear.failed.single": "Koine Gägnstende wurdän im Sch`bieler %s g`fundän", "color.minecraft.black": "<PERSON><PERSON><PERSON>", "color.minecraft.blue": "Blau", "color.minecraft.brown": "<PERSON>", "color.minecraft.cyan": "<PERSON><PERSON><PERSON>", "color.minecraft.gray": "G<PERSON><PERSON>", "color.minecraft.green": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Hellblau", "color.minecraft.light_gray": "Hellgrau", "color.minecraft.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.magenta": "Magenda", "color.minecraft.orange": "Orange", "color.minecraft.pink": "<PERSON><PERSON>", "color.minecraft.purple": "Violed", "color.minecraft.red": "<PERSON><PERSON>", "color.minecraft.white": "<PERSON><PERSON><PERSON>", "color.minecraft.yellow": "<PERSON><PERSON><PERSON>", "command.context.here": "<--[HIA]", "command.context.parse_error": "%s an Bosition %s: %s", "command.exception": "B`fähl fählerhafd: %s", "command.expected.separator": "Läerzeichen vor neuem Oargumend ärwardet", "command.failed": "<PERSON>ä<PERSON> dea Ausfüharung dieses Bfehls isd a Fehla aufgedredn", "command.forkLimit": "Maximum number of contexts (%s) reached", "command.unknown.argument": "Ungüldig`s B`fehlsargumend", "command.unknown.command": "Unbekanndea oda unvollständiga <PERSON>, sieh undn füa <PERSON>a", "commands.advancement.criterionNotFound": "Griderium '%2$s' ghöad ned zum Foadschrid %1$s", "commands.advancement.grant.criterion.to.many.failure": "Griderium '%s' des Foadschridds %s konnde %s Schbielan ned g<PERSON><PERSON><PERSON> weadn, da es breids eafülld is", "commands.advancement.grant.criterion.to.many.success": "Griderium '%s' des Foadschridds %s wuad %s Schbielan gwähad", "commands.advancement.grant.criterion.to.one.failure": "Griderium '%s' des Foadschridds %s konnde %s ned g<PERSON><PERSON><PERSON> weadn, da es breids eafülld is", "commands.advancement.grant.criterion.to.one.success": "Griderium '%s' des Foadschridds %s wuad %s gwähad", "commands.advancement.grant.many.to.many.failure": "%s Foadschridde konnde %s Schbielan ned g<PERSON><PERSON><PERSON> weadn, da sie breids eareichd wuadn", "commands.advancement.grant.many.to.many.success": "Foadschridde %s wuadn %s Schbielan gwähad", "commands.advancement.grant.many.to.one.failure": "%s Foadschridde konnde %s ned g<PERSON><PERSON><PERSON> we<PERSON>n, da sie breids eareichd wuadn", "commands.advancement.grant.many.to.one.success": "%s Foadschridde wuadn %s gwähad", "commands.advancement.grant.one.to.many.failure": "Foadschridd %s konnde %s Schbielan ned g<PERSON><PERSON><PERSON> weadn, da er breids eareichd wuade", "commands.advancement.grant.one.to.many.success": "Foadschridd %s wuad %s Schbielan gwähad", "commands.advancement.grant.one.to.one.failure": "Foadschridd %s konnde %s ned g<PERSON><PERSON><PERSON> weadn, da er breids eareichd wuade", "commands.advancement.grant.one.to.one.success": "Foadschridd %s wuad %s gwähad", "commands.advancement.revoke.criterion.to.many.failure": "Griderium '%s' des Foadschridds %s konnde %s Schbielan ned endzogn weadn, da es ned eafülld wa", "commands.advancement.revoke.criterion.to.many.success": "Griderium '%s' des Foadschridds %s wuad %s Schbielan endzogn", "commands.advancement.revoke.criterion.to.one.failure": "Griderium '%s' des Foadschridds %s konnde %s ned endzogn weadn, da es ned eafülld wa", "commands.advancement.revoke.criterion.to.one.success": "Griderium '%s' des Foadschridds %s wuad %s endzogn", "commands.advancement.revoke.many.to.many.failure": "%s Foadschridde konndn %s Schbielan ned endzogn weadn, da sie ned eareichd wuadn", "commands.advancement.revoke.many.to.many.success": "%s Foadschridde wuadn %s Schbielan endzogn", "commands.advancement.revoke.many.to.one.failure": "%s Foadschridde konndn %s ned end<PERSON><PERSON> weadn, da sie ned eareichd wuadn", "commands.advancement.revoke.many.to.one.success": "%s Foadschridde wuadn %s endzogn", "commands.advancement.revoke.one.to.many.failure": "Foadschridd %s konnde %s Schbielan ned endzogn weadn, da er ned eareichd wuade", "commands.advancement.revoke.one.to.many.success": "Foadschridd %s wuad %s Schbielan endzogn", "commands.advancement.revoke.one.to.one.failure": "Foadschridd %s konnde %s ned endzo<PERSON> weadn, da er ned eareichd wuade", "commands.advancement.revoke.one.to.one.success": "Foadschridd %s wuad %s endzogn", "commands.attribute.base_value.get.success": "Grundwead des Addribuds %s des Objekds %s is %s", "commands.attribute.base_value.reset.success": "Base value for attribute %s for entity %s reset to default %s", "commands.attribute.base_value.set.success": "Grundwead des Addribuds %s des Objekds %swuad oaf %s gsetzd", "commands.attribute.failed.entity": "%s isch koan güldiges Oabjekd füar dieschen Bäfähl", "commands.attribute.failed.modifier_already_present": "Modifikador %s is bei Addribud %s des Objegds %s breids voahandn", "commands.attribute.failed.no_attribute": "Objegd %s had keen Addribud %s", "commands.attribute.failed.no_modifier": "Addribud %s des Objegds %s hat keen Modifikador %s", "commands.attribute.modifier.add.success": "Modifikador %s wuad zum Addribud %s des Objekds %s hinzugfügd", "commands.attribute.modifier.remove.success": "Modifikador %s wuad vom Addribud %s des Objekds %s endfeand", "commands.attribute.modifier.value.get.success": "Wead des Modifikadors %s des Addribuds %s des Objekds %s is %s", "commands.attribute.value.get.success": "Wead des Addribuds %s des Objekds %s is %s", "commands.ban.failed": "Nich<PERSON> hoad sich geändord. Der Spieler isch schon gbannd", "commands.ban.success": "%s wuad gspead: %s", "commands.banip.failed": "<PERSON><PERSON><PERSON> hoad sich ge<PERSON>nderd, die IP-Oadresse is beroeids gsperrd", "commands.banip.info": "Die Sperrn betriffd %s <PERSON><PERSON><PERSON>: %s", "commands.banip.invalid": "Ungüldige IP-Adresse oder unbegannda Spieler", "commands.banip.success": "IP-Adressn %s wuad gschbead: %s", "commands.banlist.entry": "%s wuad von %s gschbead: %s", "commands.banlist.entry.unknown": "(Unknown)", "commands.banlist.list": "There are %s ban(s):", "commands.banlist.none": "<PERSON><PERSON> gibd ka <PERSON>hberrungn", "commands.bossbar.create.failed": "<PERSON><PERSON> exisdiert b`reids oine Bossleischte mit der ID ‚%s‘", "commands.bossbar.create.success": "Bnutzadefiniede Bossleisdn %s wuad eastelld", "commands.bossbar.get.max": "Dea Maximalwead dea bnutzadefiniede Bossleisdn %s bedrächd %s", "commands.bossbar.get.players.none": "Bnutzadefiniede Bossleisdn %s had akduell koane <PERSON>h<PERSON>la online", "commands.bossbar.get.players.some": "Custom bossbar %s has %s player(s) currently online: %s", "commands.bossbar.get.value": "Dea akduelle Wead dea bnutzadefiniede Bossleisdn %s bedrächd %s", "commands.bossbar.get.visible.hidden": "Bnutzadefiniede Bossleisdn %s is akduell veschdeggd", "commands.bossbar.get.visible.visible": "Bnutzadefiniede Bossleisdn %s is akduell sichdboe", "commands.bossbar.list.bars.none": "Es san kene bnutzadefiniede Bossleisdn agdiv", "commands.bossbar.list.bars.some": "There are %s custom bossbar(s) active: %s", "commands.bossbar.remove.success": "Bnutzadefiniede Bossleisdn %s wuad endfeand", "commands.bossbar.set.color.success": "Foabn dea bnutzadefiniede Bossleisdn %s wuad gändad", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON><PERSON> hoad sich gänder<PERSON>, die Boschleischde hoad bäreids diesche Foarbe", "commands.bossbar.set.max.success": "Maximalwead dea bnutzadefiniede Bossleisdn %s wuad oaf %s gändad", "commands.bossbar.set.max.unchanged": "Nix hoad sich g'<PERSON><PERSON><PERSON><PERSON>, diesch isd bäreids dasch Maxschimum diescher Boschleischte", "commands.bossbar.set.name.success": "Bnutzadefiniede Bossleisdn %s wuad umbnannd", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON><PERSON> hoad sich gänderd, die Boschleischte hoad bäreids dieschen Noamen", "commands.bossbar.set.players.success.none": "Bnutzadefiniede Bossleisdn %s hat kene Schbiela mea", "commands.bossbar.set.players.success.some": "Custom bossbar %s now has %s player(s): %s", "commands.bossbar.set.players.unchanged": "Nichds hoad sich g<PERSON><PERSON><PERSON>, diesche Spieler sind schohn zur Boschleischte zugeordned, es wurde niemond hinzugfügt oder entfernd", "commands.bossbar.set.style.success": "Einteilung dea bnutzadefiniede Bossleisdn %s wuad gändad", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON><PERSON> hoad sich gänder<PERSON>, die Boschleischste hoad bäreids diesche Einställungän", "commands.bossbar.set.value.success": "Wead dea bnutzadefiniede Bossleisdn %s wuad oaf %s gändad", "commands.bossbar.set.value.unchanged": "Nichds hoad sich gänderd, diesch isd bäreitz där Wärd där <PERSON>sch<PERSON>", "commands.bossbar.set.visibility.unchanged.hidden": "Nix hot sich g'<PERSON><PERSON><PERSON>, die Bossleiste is bereids värsteggd", "commands.bossbar.set.visibility.unchanged.visible": "Nix hot dich g'<PERSON><PERSON><PERSON>, die Boschleischte ischd bäreids sichbar", "commands.bossbar.set.visible.success.hidden": "Bnutzadefiniede Bossleisdn %s is nun veschdeggd", "commands.bossbar.set.visible.success.visible": "Bnutzadefiniede Bossleisdn %s is nun sichdboe", "commands.bossbar.unknown": "Esch exisdiert keine Bossleischte mit der ID ‚%s‘", "commands.clear.success.multiple": "%s Gegenständle würden von %s Spielern endfernd", "commands.clear.success.single": "%s Gegenständla vom Spieler %s endfernd", "commands.clear.test.multiple": "%s passnde Gegenständla bei %s Spielern gfundn", "commands.clear.test.single": "%s passende Gegenständla beim Spieler %s gfundn", "commands.clone.failed": "<PERSON><PERSON> wurde ko Blöcke gk<PERSON>d", "commands.clone.overlap": "<PERSON><PERSON><PERSON>- un Zielbäreich dörfe sich nich überschneidö", "commands.clone.success": "Successfully cloned %s block(s)", "commands.clone.toobig": "Zu viele Blöggä im angegebnen Breich (maximal %s, angegebn %s)", "commands.damage.invulnerable": "<PERSON> is unverwundbor gechaüber dem schodnstyp", "commands.damage.success": "%s Schodn auf %s ogwandt", "commands.data.block.get": "%s von Blogg bei (%s, %s, %s) muldibliziad mid %s is %s", "commands.data.block.invalid": "Zielblock hot koine Bloggdadn", "commands.data.block.modified": "Bloggdadn bei (%s, %s, %s) wuadn g'ändad", "commands.data.block.query": "Blogg bei (%s, %s, %s) bsitzd die folgndedn Bloggdadn: %s", "commands.data.entity.get": "%s von %s muldibliziad mid %s is %s", "commands.data.entity.invalid": "Spielerdadn können nicht g`ändert werd`n", "commands.data.entity.modified": "Objekddadn von %s wuadn g'ändad", "commands.data.entity.query": "%s bsitzd die folgnden Objekddadn: %s", "commands.data.get.invalid": "‚%s‘ kann nichd abg`frachd wä<PERSON><PERSON>, de Eisch<PERSON>chafd is keene <PERSON>", "commands.data.get.multiple": "Diesches Arg`ment agzebdiert nuor oinen einzelnen NBT-Wärd", "commands.data.get.unknown": "%s konn nichd a`bg`fragd wärden, die Oig`nschafd äxisdierd nichd", "commands.data.merge.failed": "Nix hod si gändad, de oangegebnen Eigenschofdn hom bereids de Weade", "commands.data.modify.expected_list": "Lischde ärwarded, ‚%s‘ ärh`lden", "commands.data.modify.expected_object": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ‚%s‘ ärh`lden", "commands.data.modify.expected_value": "Wert erwarded, %s erholtn", "commands.data.modify.invalid_index": "Ungüldiga Lischdenindex: %s", "commands.data.modify.invalid_substring": "Invalid substring indices: %s to %s", "commands.data.storage.get": "%s im Schbaicha %s mid %s muldibliziad is %s", "commands.data.storage.modified": "Schbaicha %s wuade g'ändad", "commands.data.storage.query": "Schbaicha %s bsitzd die folgnden Dadn: %s", "commands.datapack.create.already_exists": "Pack with name '%s' already exists", "commands.datapack.create.invalid_full_name": "Invalid new pack name '%s'", "commands.datapack.create.invalid_name": "Invalid characters in new pack name '%s'", "commands.datapack.create.io_failure": "Can't create pack with name '%s', check logs", "commands.datapack.create.metadata_encode_failure": "Failed to encode metadata for pack with name '%s': %s", "commands.datapack.create.success": "Created new empty pack with name '%s'", "commands.datapack.disable.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>,%s` isd b`reids deagdivierd!", "commands.datapack.disable.failed.feature": "Pack '%s' cannot be disabled, since it is part of an enabled flag!", "commands.datapack.enable.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>,%s` isd b`reids agdivierd!", "commands.datapack.enable.failed.no_flags": "Paget '%s' ko ned aktivierd werdn, die bnötign Umscholder für die Weld san ned oktiv: %s!", "commands.datapack.list.available.none": "<PERSON>s sann koane weideren <PERSON> ve<PERSON>a", "commands.datapack.list.available.success": "There are %s data pack(s) available: %s", "commands.datapack.list.enabled.none": "<PERSON>s sann koane <PERSON> a<PERSON>", "commands.datapack.list.enabled.success": "There are %s data pack(s) enabled: %s", "commands.datapack.modify.disable": "Deaktivier Dadnbageed %s", "commands.datapack.modify.enable": "Aktivier Dadnbageed %s", "commands.datapack.unknown": "Unbeganndes Dadnpagg ´%s´", "commands.debug.alreadyRunning": "Digg-Uff<PERSON>chnung wurrde bäreids g`statted", "commands.debug.function.noRecursion": "'S koann ned aus eina Funktion raus aufgzeichned weadn", "commands.debug.function.noReturnRun": "Tracing can't be used with return run", "commands.debug.function.success.multiple": "%s <PERSON><PERSON><PERSON>(e) von %s Funktiona wurd(n) in die Dadei %s gschriebn", "commands.debug.function.success.single": "%s <PERSON><PERSON><PERSON><PERSON> von dera Funktion %s wurd(n) in die Dadei %s gschriebn", "commands.debug.function.traceFailed": "Funktion konnde ned aufgzeichned weadn", "commands.debug.notRunning": "Digg-Uffzichnung isd nischd oaktiv", "commands.debug.started": "Digg-U<PERSON><PERSON><PERSON>nung wurde g`staded", "commands.debug.stopped": "Digg-Uffzichnung wuad nach %s <PERSON>kundn und %s Diggs g`stoppd (%s Diggs pro Sekundn)", "commands.defaultgamemode.success": "Sdandardschbielmoddus wuad oaf %s gsetzd", "commands.deop.failed": "Nix hot sich g'<PERSON><PERSON><PERSON>, der <PERSON><PERSON><PERSON> is bereids ken Operator", "commands.deop.success": "%s is ka Seavaoperador mea", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "Nix hot sich g`<PERSON><PERSON><PERSON>, die Schwierichkeid liegd b`reids boi %s", "commands.difficulty.query": "Die Schwierichkeid is %s", "commands.difficulty.success": "Schwierichkeid wuad aud %s gsetzd", "commands.drop.no_held_items": "Obje<PERSON>d kann kene Gechenstände dragn", "commands.drop.no_loot_table": "Objekd %s had kene <PERSON>", "commands.drop.no_loot_table.block": "Block %s has no loot table", "commands.drop.success.multiple": "%s Gechnstände falln glassn", "commands.drop.success.multiple_with_table": "%s Gechenstände aus Beudedabelle %s falln glassn", "commands.drop.success.single": "%s %s falln glassn", "commands.drop.success.single_with_table": "%s %s aus Beudedabelle %s falln glassn", "commands.effect.clear.everything.failed": "<PERSON><PERSON> b<PERSON>sitzd koeine zü ändfernende Äffekde", "commands.effect.clear.everything.success.multiple": "Alle Effekde wuadn von %s Zieln endfeand", "commands.effect.clear.everything.success.single": "Alle Effekde wuadn von %s endfeand", "commands.effect.clear.specific.failed": "<PERSON><PERSON> bäsitzt dän zü ändfernenden Äffekd nich", "commands.effect.clear.specific.success.multiple": "Effekd %s wuad von %s Zieln endfeand", "commands.effect.clear.specific.success.single": "Effekd %s wuad von %s endfeand", "commands.effect.give.failed": "Äffekd konnde nichd oangewand wärden (dasch Ziel isd endweder resischdent oder bäsitzt oaeine stärkere Stufe", "commands.effect.give.success.multiple": "Effekd %s wuad oaf %s Ziele angwended", "commands.effect.give.success.single": "Effekd %s wuad oaf %s angwended", "commands.enchant.failed": "Nix hot sich g`<PERSON><PERSON><PERSON>, die Ziele holden endwäda koeinen Gägenstand in ihrär Hupdthand oder die Verzuberung konnde nich oangewandt werden", "commands.enchant.failed.entity": "%s is ka gülticher Befehl für des Objekd", "commands.enchant.failed.incompatible": "%s koann domid nichd värzauberd wärden", "commands.enchant.failed.itemless": "%s häld koinen Gägenstand in där Hupdhand", "commands.enchant.failed.level": "%s isd höha ols dasch maximolä levl %s, dasch fuar die Vorzauborung undastüdzd wird", "commands.enchant.success.multiple": "Veazauberung %s wuad oaf %s Objekde angwended", "commands.enchant.success.single": "Veazauberung %s wuad auf'n Gechenstand von %s angwended", "commands.execute.blocks.toobig": "Zu viele Blögge im angegebnen Beraich (maximol %s, angebn %s)", "commands.execute.conditional.fail": "<PERSON><PERSON> f<PERSON>", "commands.execute.conditional.fail_count": "<PERSON><PERSON>, Anzohl: %s", "commands.execute.conditional.pass": "<PERSON><PERSON> er<PERSON><PERSON><PERSON><PERSON><PERSON>", "commands.execute.conditional.pass_count": "Desd b<PERSON>standen, <PERSON><PERSON><PERSON>: %s", "commands.execute.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.experience.add.levels.success.multiple": "%s Eafarungstufn wuadn an %s Schbiela gegebn", "commands.experience.add.levels.success.single": "%s Eafarungstufn wuadn an %s gegebn", "commands.experience.add.points.success.multiple": "%s Eafarungspunkde wuadn an %s Schbiela gegebn", "commands.experience.add.points.success.single": "%s Eafarungspunkde wuadn an %s gegebn", "commands.experience.query.levels": "%s had Eafahrungsstufn %s", "commands.experience.query.points": "%s had %s Eafahrungspunkde", "commands.experience.set.levels.success.multiple": "Eafahrungsstufn von %s Schbielan wuad oaf %s gsetzd", "commands.experience.set.levels.success.single": "Eafahrungsstufn von %s wuad oaf %s gsetzd", "commands.experience.set.points.invalid": "Ärfahrungspungde dürfen nich höha g`setzt wärden ols des Moximum däs oaktuellen Ärfahrungspungde des Spielers", "commands.experience.set.points.success.multiple": "Eafahrungspunkde von %s Schbielan wuadn oaf %s gsetzd", "commands.experience.set.points.success.single": "Eafarungspunkde von %s wuadn oaf %s gsetzd", "commands.fill.failed": "<PERSON><PERSON> wurden ko <PERSON>l<PERSON>cke g`fülld", "commands.fill.success": "Successfully filled %s block(s)", "commands.fill.toobig": "Zu viele Blögge im angegebnen Beraich (maximol %s, angebn %s)", "commands.fillbiome.success": "Biome zwischa %s, %s, %s und %s, %s, %s festglegt", "commands.fillbiome.success.count": "%s biome entry/entries set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.toobig": "Too many blocks in the specified volume (maximum %s, specified %s)", "commands.forceload.added.failure": "<PERSON> had sich ge<PERSON><PERSON><PERSON>, diese <PERSON>ks weadn breids dauahafd gladn", "commands.forceload.added.multiple": "%s Chunks in %s von %s bis %s weadn nun da<PERSON><PERSON><PERSON>d gladn", "commands.forceload.added.none": "In %s weadn kane <PERSON> dauahafd gladn", "commands.forceload.added.single": "Chunk %s in %s wiad nun da<PERSON><PERSON><PERSON>d gladn", "commands.forceload.list.multiple": "In %s weadn %s Chunks dauahafd gladn: %s", "commands.forceload.list.single": "In %s wiad a Chunk dauahafd gladn: %s", "commands.forceload.query.failure": "Chunk %s in %s wiad ned dauahafd gladn", "commands.forceload.query.success": "Chunk %s in %s wiad dauahafd gladn", "commands.forceload.removed.all": "In %s weadn kane <PERSON>ks mea da<PERSON>ha<PERSON>d gladn", "commands.forceload.removed.failure": "<PERSON> had sich ge<PERSON><PERSON><PERSON>, diese <PERSON><PERSON> wuadn auch früha ned da<PERSON>ha<PERSON>d gladn", "commands.forceload.removed.multiple": "%s Chunks in %s von %s bis %s weadn nun nich mea dauahafd gladn", "commands.forceload.removed.single": "Chunk %s in %s wiad nun nich mea dauahafd gladn", "commands.forceload.toobig": "Zu viele Chunks im angegebnen Breich (maximal %s, angegebn %s)", "commands.function.error.argument_not_compound": "Invalid argument type: %s, expected Compound", "commands.function.error.missing_argument": "Missing argument %2$s to function %1$s", "commands.function.error.missing_arguments": "Missing arguments to function %s", "commands.function.error.parse": "While instantiating macro %s: Command '%s' caused error: %s", "commands.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.function.result": "Function %s returned %s", "commands.function.scheduled.multiple": "Running functions %s", "commands.function.scheduled.no_functions": "Can't find any functions for name %s", "commands.function.scheduled.single": "Running function %s", "commands.function.success.multiple": "Executed %s command(s) from %s functions", "commands.function.success.multiple.result": "Executed %s functions", "commands.function.success.single": "Executed %s command(s) from function '%s'", "commands.function.success.single.result": "Function '%2$s' returned %1$s", "commands.gamemode.success.other": "<PERSON><PERSON>lmoddus von %s wuad oaf %s gesetzd", "commands.gamemode.success.self": "Da Schbielmoddus wuad oaf %s gesetzd", "commands.gamerule.query": "Die Schbielrechl %s is deazeid '%s'", "commands.gamerule.set": "Schbielrechel %s wuad oaf '%s' gsetzd", "commands.give.failed.toomanyitems": "Koann ned meha als %s von %s gebn", "commands.give.success.multiple": "%s %s an %s Schbiela gegebn", "commands.give.success.single": "%s %s an %s gegebn", "commands.help.failed": "Unbegannda Bäfähl odär unzureichände Bärechdigung", "commands.item.block.set.success": "Inhald oines Invendarplatzes bei %s, %s, %s wuade mid %s earsetzd", "commands.item.entity.set.success.multiple": "Inhald anes Invendarplatzes vo %s Objekdn wuad duach %s easetzd", "commands.item.entity.set.success.single": "Inhald anes Invendarplatzes vo %s wuad duach %s easetzd", "commands.item.source.no_such_slot": "Quelle bsitzd Invendarploatz %s ned", "commands.item.source.not_a_container": "Quellposition %s, %s, %s bsitzd keen Invendar", "commands.item.target.no_changed.known_item": "Keen Ziel lässd %s beim Invendoaploatz %s zu", "commands.item.target.no_changes": "Keen Ziel lässd Gechnschdände bei Invendoaploatz %s zu", "commands.item.target.no_such_slot": "Das Ziel b`sitzt koinen Invendarploatz %s", "commands.item.target.not_a_container": "Zielposition %s, %s, %s bsitzd keen Invendar", "commands.jfr.dump.failed": "<PERSON><PERSON><PERSON>a boam Spoachern där JFR-Uffzichnung: %s", "commands.jfr.start.failed": "<PERSON><PERSON><PERSON><PERSON> boam Starden där JFR-Uffzichnung", "commands.jfr.started": "JFR-<PERSON><PERSON><PERSON><PERSON><PERSON>ng is gestarded", "commands.jfr.stopped": "JFR-Uffzichnung gestobbd un in %s gschpeicherd", "commands.kick.owner.failed": "Cannot kick server owner in LAN game", "commands.kick.singleplayer.failed": "<PERSON><PERSON> kick in an offline singleplayer game", "commands.kick.success": "%s wuadn rausgworfn: %s", "commands.kill.success.multiple": "%s <PERSON><PERSON><PERSON><PERSON><PERSON> wuadn bseidichd", "commands.kill.success.single": "%s wuadn b<PERSON><PERSON>hd", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "'S sind %s von maximal %s Schbielan online: %s", "commands.locate.biome.not_found": "'S konnde in ongmessna Endfernung keen Biom des Typs %s gfundn werdn", "commands.locate.biome.success": "The nearest %s is at %s (%s blocks away)", "commands.locate.poi.not_found": "Could not find a point of interest of type \"%s\" within reasonable distance", "commands.locate.poi.success": "The nearest %s is at %s (%s blocks away)", "commands.locate.structure.invalid": "Dord g´bst kene Strugdur vom Dyb %s", "commands.locate.structure.not_found": "Diesier Strugdur vom Dyb %s konnde in der Nähä ned g´funden wärdn", "commands.locate.structure.success": "%s is am nächstn bei %s (%s Blögge endfeand)", "commands.message.display.incoming": "%s flüsdad dia zu: %s", "commands.message.display.outgoing": "Du flüsdasd %s zu: %s", "commands.op.failed": "<PERSON> hot sich g<PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON> isd schoan oan Operador", "commands.op.success": "%s wuad zum Servaoperador eanannd", "commands.pardon.failed": "<PERSON> hot sich g<PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON> isd nichd g<PERSON>bannd", "commands.pardon.success": "%s wuad endspead", "commands.pardonip.failed": "Nix hot sich g`<PERSON><PERSON><PERSON>, diesche IP-Adrässe isd nichd g`bannd", "commands.pardonip.invalid": "Ungüldige IP-Adrässe", "commands.pardonip.success": "IP-Adressn %s wuad endschbead", "commands.particle.failed": "<PERSON><PERSON><PERSON> wor füar niemonden sichbor", "commands.particle.success": "Partikl %s wiad gzaichd", "commands.perf.alreadyRunning": "Pärformänze-Uffzichnung wurrde bäreids g`statted", "commands.perf.notRunning": "Pärformänze-Uffzichnung isd nischd oaktiv", "commands.perf.reportFailed": "Debug-Brichd konnde ned eastelld weadn", "commands.perf.reportSaved": "Debug-Brichd wuad eastelld in %s", "commands.perf.started": "10-seg<PERSON><PERSON><PERSON> Leischtungsuffzichnung wurde g`startet (b`nutze ‚/debug stop‘ um sie vorzeitig zu b`enden)", "commands.perf.stopped": "Stopped performance profiling after %s second(s) and %s tick(s) (%s tick(s) per second)", "commands.place.feature.failed": "<PERSON><PERSON> Märgmal konndä noch b´lazierd wärd´n", "commands.place.feature.invalid": "Dord g´bst ken Märgmal vom Dyb %s", "commands.place.feature.success": "Esch wud´ä %s boi %s,%s,%s bladschird", "commands.place.jigsaw.failed": "Failed to generate jigsaw", "commands.place.jigsaw.invalid": "There is no template pool with type \"%s\"", "commands.place.jigsaw.success": "Generated jigsaw at %s, %s, %s", "commands.place.structure.failed": "Failed to place structure", "commands.place.structure.invalid": "There is no structure with type \"%s\"", "commands.place.structure.success": "Generated structure \"%s\" at %s, %s, %s", "commands.place.template.failed": "Failed to place template", "commands.place.template.invalid": "There is no template with id \"%s\"", "commands.place.template.success": "Loaded template \"%s\" at %s, %s, %s", "commands.playsound.failed": "<PERSON><PERSON><PERSON><PERSON> isd zu woit ändfernd, um g`hörd zu werden", "commands.playsound.success.multiple": "Gräusch %s wuad füa %s Schbiela abgschbield", "commands.playsound.success.single": "Gräusch %s wuad füa %s abgschbield", "commands.publish.alreadyPublished": "LAN-Schbiel is b`reids under pord %s eareichba", "commands.publish.failed": "Es konnde koan LAN-Schbie<PERSON> easch<PERSON>d weadn", "commands.publish.started": "LAN-Schbiel unda Pord %s eareichba", "commands.publish.success": "LAN-Schbiel is jetzadla under pord %s eareichba", "commands.random.error.range_too_large": "The range of the random value must be at most 2147483646", "commands.random.error.range_too_small": "The range of the random value must be at least 2", "commands.random.reset.all.success": "Reset %s random sequence(s)", "commands.random.reset.success": "Reset random sequence %s", "commands.random.roll": "%s rolled %s (from %s to %s)", "commands.random.sample.success": "Randomized value: %s", "commands.recipe.give.failed": "<PERSON><PERSON> wurden kone noen Räzepde froeig`schalded", "commands.recipe.give.success.multiple": "%s Rezebde wuadn füa %s Schbiela freigschaldn", "commands.recipe.give.success.single": "%s Rezebde wuadn füa %s freigschaldn", "commands.recipe.take.failed": "Es konnden kone Räzepde ändvernd werden", "commands.recipe.take.success.multiple": "%s Rezebde wuadn von %s Schbielan endfeand", "commands.recipe.take.success.single": "%s Rezebde wuadn von %s endfeand", "commands.reload.failure": "<PERSON><PERSON><PERSON><PERSON> feh<PERSON>, oalde <PERSON>n werdn beibhaldn", "commands.reload.success": "<PERSON><PERSON><PERSON><PERSON> weadn neu gladn!", "commands.ride.already_riding": "%s is already riding %s", "commands.ride.dismount.success": "%s hat afghert auf %s zu reiden", "commands.ride.mount.failure.cant_ride_players": "<PERSON><PERSON><PERSON> kenna ned grittn wern", "commands.ride.mount.failure.generic": "%s couldn't start riding %s", "commands.ride.mount.failure.loop": "Can't mount entity on itself or any of its passengers", "commands.ride.mount.failure.wrong_dimension": "Can't mount entity in different dimension", "commands.ride.mount.success": "%s started riding %s", "commands.ride.not_riding": "%s is not riding any vehicle", "commands.rotate.success": "Rotated %s", "commands.save.alreadyOff": "Automadisches Speichern is bereids d`akdiviert", "commands.save.alreadyOn": "Automadisches Speichern is bereids akdiviert", "commands.save.disabled": "Audomadisches Schbaichan is jetzad<PERSON>", "commands.save.enabled": "Audomadisches Schbaichan is jetzadla agdiviead", "commands.save.failed": "Spiel konnde nichd g`speicherd werden (isd gänug froeier Spoicherplotz vorhonden)", "commands.save.saving": "<PERSON><PERSON><PERSON>l wiad foadgsetd (kann an Momend dauan!)", "commands.save.success": "Schbiel wuad gspaichad", "commands.schedule.cleared.failure": "Es gibd ka Blanung mid dea ID %s", "commands.schedule.cleared.success": "Removed %s schedule(s) with id %s", "commands.schedule.created.function": "Scheduled function '%s' in %s tick(s) at gametime %s", "commands.schedule.created.tag": "Etikedd '%s' wuad in %s Ticks zua Schbielzoad eingepland", "commands.schedule.macro": "Can't schedule a macro", "commands.schedule.same_tick": "<PERSON><PERSON><PERSON> den akduelln Tigg koann nix eingepland weadn", "commands.scoreboard.objectives.add.duplicate": "<PERSON><PERSON> mid dieschem Noaman exischdierd bäreids", "commands.scoreboard.objectives.add.success": "Neues Ziel %s wuad easch<PERSON>d", "commands.scoreboard.objectives.display.alreadyEmpty": "Nix hot sich g`<PERSON><PERSON><PERSON>, die Oanzeigeposizion isd bäreids läer", "commands.scoreboard.objectives.display.alreadySet": "Nix hot sich ve<PERSON><PERSON><PERSON><PERSON>, die Oanzeigeposizion zoigd bäreids dos Ziel", "commands.scoreboard.objectives.display.cleared": "Oalle Ziele in Anzeichebosition %s wuadn glöschd", "commands.scoreboard.objectives.display.set": "Anzeichebosotion %s zeichd nun das Ziel %s", "commands.scoreboard.objectives.list.empty": "<PERSON><PERSON> gibd koan<PERSON>", "commands.scoreboard.objectives.list.success": "There are %s objective(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Disabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Enabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayname": "Oanzeigenoama vo %s wuad zua %s gändad", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Cleared default number format of objective %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Changed default number format of objective %s", "commands.scoreboard.objectives.modify.rendertype": "Daställungsart vom Ziel %s wuad gändad", "commands.scoreboard.objectives.remove.success": "Ziel %s wuade endfeand", "commands.scoreboard.players.add.success.multiple": "%2$s wuad füa %3$s Objekde um %1$s eahöhd", "commands.scoreboard.players.add.success.single": "%2$s wuad füa %3$s um %1$s eahöhd (jetzadla %4$s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Cleared display name for %s entities in %s", "commands.scoreboard.players.display.name.clear.success.single": "Cleared display name for %s in %s", "commands.scoreboard.players.display.name.set.success.multiple": "Changed display name to %s for %s entities in %s", "commands.scoreboard.players.display.name.set.success.single": "Changed display name to %s for %s in %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Cleared number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Cleared number format for %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Changed number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Changed number format for %s in %s", "commands.scoreboard.players.enable.failed": "<PERSON> hot sich g'<PERSON><PERSON><PERSON>, d<PERSON><PERSON> isd bäreids oakdivierd", "commands.scoreboard.players.enable.invalid": "<PERSON>scha <PERSON>fähl funkdionierd nur fuar <PERSON> (tigga-Ziele)", "commands.scoreboard.players.enable.success.multiple": "Auslösa %s wuad füa %s Objekde akdiviead", "commands.scoreboard.players.enable.success.single": "Auslösa %s wuad füa %s akdiviead", "commands.scoreboard.players.get.null": "<PERSON><PERSON><PERSON> von %s fuar %s konnde nichd abg`fragd wärden; da koina g`sedzt isd", "commands.scoreboard.players.get.success": "%s had %s %s", "commands.scoreboard.players.list.empty": "<PERSON><PERSON> gibd koane übawachden Objekde", "commands.scoreboard.players.list.entity.empty": "%s had koane Punkdeschdände", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s has %s score(s):", "commands.scoreboard.players.list.success": "There are %s tracked entity/entities: %s", "commands.scoreboard.players.operation.success.multiple": "%s wuad füa %s <PERSON>b<PERSON><PERSON><PERSON> g'ändad", "commands.scoreboard.players.operation.success.single": "Set %s for %s to %s", "commands.scoreboard.players.remove.success.multiple": "%2$s wuade füa %3$s Objekde um %1$s vearingad", "commands.scoreboard.players.remove.success.single": "%2$s wuade füa %3$s um %1$s vearingad (jetzadla %4$s)", "commands.scoreboard.players.reset.all.multiple": "Oalle Punkdeschdände wuadn füa %s Objekde glöschd", "commands.scoreboard.players.reset.all.single": "Oalle Punkdeschdände wuadn füa %s glöschd", "commands.scoreboard.players.reset.specific.multiple": "%s wuad füa %s Objekde glöschd", "commands.scoreboard.players.reset.specific.single": "%s wuad füa %s glöschd", "commands.scoreboard.players.set.success.multiple": "%s wuad füa %s Objekde auf %s gsetzd", "commands.scoreboard.players.set.success.single": "%s wuad füa %s auf %s gsetzd", "commands.seed.success": "Dea Stadwead is %s", "commands.setblock.failed": "Där Block konnde nichd plazierd werden", "commands.setblock.success": "Blogg bei %s, %s, %s wuad gändad", "commands.setidletimeout.success": "The player idle timeout is now %s minute(s)", "commands.setidletimeout.success.disabled": "The player idle timeout is now disabled", "commands.setworldspawn.failure.not_overworld": "Can only set the world spawn for overworld", "commands.setworldspawn.success": "Weldeinstiegspungd wuad oaf %s, %s, %s [%s] gsetzd", "commands.spawnpoint.success.multiple": "Einstiegspungd von %s Schbielan wuad oaf (%1$s, %$2s, %$3s) [%4$s] in %5$s gsetzd", "commands.spawnpoint.success.single": "Einstiegspungd von %s wuad oaf %s, %s, %s [%s] in %s gsetzd", "commands.spectate.not_spectator": "%s is nich im Zuschauamodus", "commands.spectate.self": "Du koannsd dich nich selbsd beobachdn", "commands.spectate.success.started": "Du beobachdesd nun %s", "commands.spectate.success.stopped": "Du beobachdesd koa Obje<PERSON>d meah", "commands.spreadplayers.failed.entities": "Could not spread %s entity/entities around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.failed.invalid.height": "Ungüldische Höh %s, höä alsch dasch Weldminimum %s", "commands.spreadplayers.failed.teams": "Could not spread %s team(s) around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.success.entities": "Spread %s entity/entities around %s, %s with an average distance of %s block(s) apart", "commands.spreadplayers.success.teams": "Spread %s team(s) around %s, %s with an average distance of %s block(s) apart", "commands.stop.stopping": "Serva wiad gstobbd", "commands.stopsound.success.source.any": "Oalle %s - Gräusche wuadn gschdoppd", "commands.stopsound.success.source.sound": "Gräusch %s füa die Gräuschard %s wuad gschdoppd", "commands.stopsound.success.sourceless.any": "Oalle Gräusche wuadn gschdoppd", "commands.stopsound.success.sourceless.sound": "Gräusch %s waud gschdoppd", "commands.summon.failed": "Objegd konnde nichd erzeugd werden", "commands.summon.failed.uuid": "Objegd konnde ned erzeuchd werdn; die UUID exisdierd breids", "commands.summon.invalidPosition": "Ungültiche Position zum Earzeuchn", "commands.summon.success": "%s wuad eazeuchd", "commands.tag.add.failed": "Ändweda hot dos Ziel bäreids diesches Ätigedd oder es hat zu viele Ätigedden", "commands.tag.add.success.multiple": "Edikedd '%s' wuad %s Objekdn hinzugfügd", "commands.tag.add.success.single": "Edikedd '%s' wuad %s hinzugfügd", "commands.tag.list.multiple.empty": "Die %s audgwähldn Objekde hoabn koa Edikedd", "commands.tag.list.multiple.success": "Die %s audgwähldn Objekde hoabn insgsammd %s Edikeddn: %s", "commands.tag.list.single.empty": "%s hoad koane <PERSON>n", "commands.tag.list.single.success": "%s hoad %s Edikeddn: %s", "commands.tag.remove.failed": "<PERSON><PERSON> b`sitzt dieschen Äffegd nichd", "commands.tag.remove.success.multiple": "Edikedd '%s' wuad vo %s Objekdn endfeand", "commands.tag.remove.success.single": "Edikedd '%s' wuad vo %s endfeand", "commands.team.add.duplicate": "Oan Team mid dieschem Noaman exischdierd bäreids", "commands.team.add.success": "Deam %s wuad eastelld", "commands.team.empty.success": "Removed %s member(s) from team %s", "commands.team.empty.unchanged": "<PERSON> hot sich g`<PERSON><PERSON><PERSON>, dasch Team isd bäreids läer", "commands.team.join.success.multiple": "%s Midglieda wuadn zum Deam %s zugfüchd", "commands.team.join.success.single": "%s wuad zum Deam %s zugfüchd", "commands.team.leave.success.multiple": "%s <PERSON><PERSON><PERSON> wuadn aus Deams endfeand", "commands.team.leave.success.single": "%s wuad aus jedem Deam endfeand", "commands.team.list.members.empty": "Deam %s hoad koane <PERSON>", "commands.team.list.members.success": "Team %s has %s member(s): %s", "commands.team.list.teams.empty": "<PERSON><PERSON> gibd koane <PERSON>", "commands.team.list.teams.success": "There are %s team(s): %s", "commands.team.option.collisionRule.success": "Schieberegla füa <PERSON>am %s wuad auf \"%s\" gsetzd", "commands.team.option.collisionRule.unchanged": "<PERSON> hot sich g<PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON> hot b`reids dieschen <PERSON>rd", "commands.team.option.color.success": "Foadbn vom Deam %s wuad auf %s gsetzd", "commands.team.option.color.unchanged": "Nix hot sich g<PERSON><PERSON><PERSON><PERSON>, dasch Team hot bäreids diesche Farbä", "commands.team.option.deathMessageVisibility.success": "Sichdbaakeid dea Dodesmeldungn von <PERSON> %s wuad auf \"%s\" gsetzd", "commands.team.option.deathMessageVisibility.unchanged": "Nix hot sich g`<PERSON><PERSON><PERSON>, die Schichdbargeid där Do<PERSON>chrich<PERSON> hot b`reids dies`n Wärd", "commands.team.option.friendlyfire.alreadyDisabled": "Nix hot sich g`<PERSON><PERSON><PERSON>, für disches Deam isd Oigenb`schuss b`re<PERSON> däogdivierd", "commands.team.option.friendlyfire.alreadyEnabled": "Nix hot sich g`<PERSON><PERSON><PERSON>, für disches Deam isd Oigenb`schuss b`reids oagdivierd", "commands.team.option.friendlyfire.disabled": "Aignbschuss wuad füa Deam %s deakdiviead", "commands.team.option.friendlyfire.enabled": "Aignbschuss wuad füa Deam %s akdiviead", "commands.team.option.name.success": "<PERSON><PERSON><PERSON> von Team %s w<PERSON> a<PERSON>", "commands.team.option.name.unchanged": "Nix hot sich g'<PERSON><PERSON><PERSON>, dasch Team hat b`reids diesen Namen", "commands.team.option.nametagVisibility.success": "Sichdbaakeid dea <PERSON>n von <PERSON> %s wuad auf \"%s\" gsetzd", "commands.team.option.nametagVisibility.unchanged": "Nix hot sich g`<PERSON><PERSON><PERSON>, die Schichdbargeid där Spielärnamen hot b`reids dies`n Wärd", "commands.team.option.prefix.success": "Deam-Präfix wuad auf %s gsetzd", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON> hot sich g<PERSON><PERSON><PERSON><PERSON>, disches <PERSON><PERSON> koan bä<PERSON><PERSON> koine un`sichdbarä Deammidglieda sähen", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON> hot sich g<PERSON><PERSON><PERSON><PERSON>, disches <PERSON>am koan b<PERSON><PERSON><PERSON> un`sichdbarä <PERSON>ammidglieda sähen", "commands.team.option.seeFriendlyInvisibles.disabled": "Deam %s koan koane unsichd<PERSON>en Deammid<PERSON><PERSON> meah sehn", "commands.team.option.seeFriendlyInvisibles.enabled": "Deam %s koan nun un<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>hn", "commands.team.option.suffix.success": "Deam-Suffix wuad auf %s gsetzd", "commands.team.remove.success": "Deam %s wuad endfeand", "commands.teammsg.failed.noteam": "Du muschd in oinem Deam soin, um doin Deam zu b`nachrichtig`n", "commands.teleport.invalidPosition": "Ungültige Position zum Teleportiean", "commands.teleport.success.entity.multiple": "%s Ob<PERSON><PERSON><PERSON> wuadn zu %s teleboadiead", "commands.teleport.success.entity.single": "%s wuad zu %s teleboadiead", "commands.teleport.success.location.multiple": "%s Obje<PERSON><PERSON> wuadn zu %s, %s, %s teleboadiead", "commands.teleport.success.location.single": "%s wuad zu %s, %s, %s teleboadiead", "commands.test.batch.starting": "Starting environment %s batch %s", "commands.test.clear.error.no_tests": "Could not find any tests to clear", "commands.test.clear.success": "Cleared %s structure(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Click to copy to clipboard", "commands.test.create.success": "Created test setup for test %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Found no test instances", "commands.test.error.non_existant_test": "Test %s could not be found", "commands.test.error.structure_not_found": "Test structure %s could not be found", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "The structure size must be less than %s blocks along each axis", "commands.test.locate.done": "Finished locating, found %s structure(s)", "commands.test.locate.found": "Found structure at: %s (distance: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "No tests to run", "commands.test.relative_position": "Position relative to %s: %s", "commands.test.reset.error.no_tests": "Could not find any tests to reset", "commands.test.reset.success": "Reset %s structure(s)", "commands.test.run.no_tests": "No tests found", "commands.test.run.running": "Running %s test(s)...", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "All required tests passed :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "Percentiles: P50: %sms P95: %sms P99: %sms, sample: %s", "commands.tick.query.rate.running": "Target tick rate: %s per second.\nAverage time per tick: %sms (Target: %sms)", "commands.tick.query.rate.sprinting": "Target tick rate: %s per second (ignored, reference only).\nAverage time per tick: %sms", "commands.tick.rate.success": "Set the target tick rate to %s per second", "commands.tick.sprint.report": "Sprint completed with %s ticks per second, or %s ms per tick", "commands.tick.sprint.stop.fail": "No tick sprint in progress", "commands.tick.sprint.stop.success": "Interrupted the current tick sprint", "commands.tick.status.frozen": "The game is frozen", "commands.tick.status.lagging": "The game is running, but can't keep up with the target tick rate", "commands.tick.status.running": "The game is running normally", "commands.tick.status.sprinting": "The game is sprinting", "commands.tick.step.fail": "Unable to step the game - the game must be frozen first", "commands.tick.step.stop.fail": "No tick step in progress", "commands.tick.step.stop.success": "Interrupted the current tick step", "commands.tick.step.success": "Stepping %s tick(s)", "commands.time.query": "Die akduelle Zeid is %s", "commands.time.set": "Zeid wuad aud %s gsetzd", "commands.title.cleared.multiple": "Oalle Didel wuadn füa %s Schbiela endfeand", "commands.title.cleared.single": "Oalle Didel wuadn füa %s endfeand", "commands.title.reset.multiple": "Didel-Anzaichezeidn wuadn füa %s Schbiela zurückgsetzd", "commands.title.reset.single": "Didel-Anzaichezeidn wuadn füa %s zurückgsetzd", "commands.title.show.actionbar.multiple": "Neua Akdionsloasdndidel wiad füa %s Schbiela oangzeichd", "commands.title.show.actionbar.single": "Neue Akdionsloasdndidel wiad füa %s oangzeichd", "commands.title.show.subtitle.multiple": "Neua Undadidel wiad für %s Schbiela oangzeichd", "commands.title.show.subtitle.single": "Neua Undadidel wiad für %s oangzeichd", "commands.title.show.title.multiple": "Neua Didel wiad für %s Schbiela oangzeichd", "commands.title.show.title.single": "Neua Didel wiad für %s oangzeichd", "commands.title.times.multiple": "Didel-Anzeichezoadn wuadn füa %s Schbiela gändad", "commands.title.times.single": "Didel-Anzeichezoadn wuadn füa %s gändad", "commands.transfer.error.no_players": "Must specify at least one player to transfer", "commands.transfer.success.multiple": "Transferring %s players to %s:%s", "commands.transfer.success.single": "Transferring %s to %s:%s", "commands.trigger.add.success": "%s wuad ausglösd (Werd wuade um %s eahöhd)", "commands.trigger.failed.invalid": "<PERSON><PERSON> isd gein <PERSON> (trigger-Ziel)", "commands.trigger.failed.unprimed": "Oauslöscher isd noch nichd oagdiviert", "commands.trigger.set.success": "%s wuade ausglösd (Wead wuade auf %s gsetzd)", "commands.trigger.simple.success": "%s wuad ausglösd", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "<PERSON>dda wuad oaf <PERSON> g<PERSON>", "commands.weather.set.rain": "Wedda wuad oaf Rechn gändad", "commands.weather.set.thunder": "Wedda wuad oaf Gwidda g<PERSON>ndad", "commands.whitelist.add.failed": "<PERSON><PERSON><PERSON> isd schoan uff där Gä<PERSON>lisdä", "commands.whitelist.add.success": "%s wuad zua Gästelisdn hinzugfüchd", "commands.whitelist.alreadyOff": "Die Gästelisdn is schoan d`ativiead", "commands.whitelist.alreadyOn": "Die Gästelisdn is schoan aktiviead", "commands.whitelist.disabled": "Die Gästelisdn is jetztadla deaktiviead", "commands.whitelist.enabled": "Die Gästelisdn is jetztadla aktiviead", "commands.whitelist.list": "There are %s whitelisted player(s): %s", "commands.whitelist.none": "Es san koane Schbiela oaf dea Gästelisdn", "commands.whitelist.reloaded": "Die Gästelisdn wuad neu gladn", "commands.whitelist.remove.failed": "<PERSON><PERSON><PERSON> isd nichd uff där Gästelisdä", "commands.whitelist.remove.success": "%s wuad von dea Gästelisdn endfeand", "commands.worldborder.center.failed": "<PERSON> hot sich g`<PERSON><PERSON><PERSON>, dä <PERSON>barrierä hot bäreids dische Midde", "commands.worldborder.center.success": "Die Middn dea Weldnbarriere wuad oaf %s, %s gsetzd", "commands.worldborder.damage.amount.failed": "Nix hot sich g<PERSON><PERSON><PERSON><PERSON>, dä <PERSON><PERSON><PERSON>än <PERSON>än hot bäreids dischen Wärd", "commands.worldborder.damage.amount.success": "<PERSON><PERSON>n dea Weldbarriere wuad oaf %s pro Blogg jede Sekundn gsetzd", "commands.worldborder.damage.buffer.failed": "Nix hot sich g`<PERSON><PERSON><PERSON>, dä <PERSON>ldbarrierän Uanversährdsb`reich hot bäreids dische Dischdanz", "commands.worldborder.damage.buffer.success": "Set the world border damage buffer to %s block(s)", "commands.worldborder.get": "The world border is currently %s block(s) wide", "commands.worldborder.set.failed.big": "Die Woide dea Wealdbarriere darf ned größa als %s Blögg soa", "commands.worldborder.set.failed.far": "Die Weldboierre darf nichd weida oals %s Blögge endfernd soan", "commands.worldborder.set.failed.nochange": "<PERSON> hot sich g`<PERSON><PERSON><PERSON>, dä <PERSON>barrierä hot bäreids dische Weide", "commands.worldborder.set.failed.small": "Die Woide dea Wealdbarriere darf ned kloina als 1 Blögg soan", "commands.worldborder.set.grow": "Die Weldnbarriere wiad innahoalb vo %s Sekundn oaf a Woadn vo %s Blöggn veagrößad", "commands.worldborder.set.immediate": "Set the world border to %s block(s) wide", "commands.worldborder.set.shrink": "Shrinking the world border to %s block(s) wide over %s second(s)", "commands.worldborder.warning.distance.failed": "<PERSON> hot sich g<PERSON><PERSON><PERSON><PERSON>, dä <PERSON>ldbarrierän Wornung hot bäreids dische Dischdanz", "commands.worldborder.warning.distance.success": "Set the world border warning distance to %s block(s)", "commands.worldborder.warning.time.failed": "<PERSON> hot sich g<PERSON><PERSON><PERSON><PERSON>, dä <PERSON>ldbarrierän Wornung hot bäreids dische Zeitsponnä", "commands.worldborder.warning.time.success": "Set the world border warning time to %s second(s)", "compliance.playtime.greaterThan24Hours": "Du haschd schon mähr alsch 24 Stundän gˋspield", "compliance.playtime.hours": "Du spielschd schön %s Stundä(n)", "compliance.playtime.message": "Übermäschiges Spielän kann den Alldag bˋeinträchtigän", "connect.aborted": "Ogebrochn", "connect.authorizing": "Anmelde ...", "connect.connecting": "Vebinde zum Seava ...", "connect.encrypting": "Veschlüssln ...", "connect.failed": "Vebindungsuffbau fehlgschlochn", "connect.failed.transfer": "Connection failed while transferring to the server", "connect.joining": "G<PERSON>e oaf de Weld druff ...", "connect.negotiating": "<PERSON><PERSON><PERSON><PERSON> ...", "connect.reconfiging": "Reconfiguring...", "connect.reconfiguring": "Reconfiguring...", "connect.transferring": "Transferring to new server...", "container.barrel": "Barrel", "container.beacon": "Beacon", "container.beehive.bees": "Bees: %s / %s", "container.beehive.honey": "Honey: %s / %s", "container.blast_furnace": "Blast Furnace", "container.brewing": "Brewing Stand", "container.cartography_table": "Cartography Table", "container.chest": "Chest", "container.chestDouble": "Große Druhe", "container.crafter": "Crafter", "container.crafting": "Handweak", "container.creative": "Gechenschdände", "container.dispenser": "Dispenser", "container.dropper": "Dropper", "container.enchant": "Veazauban", "container.enchant.clue": "%s …?", "container.enchant.lapis.many": "%s Labislazuli", "container.enchant.lapis.one": "1 Labislazuli", "container.enchant.level.many": "%s Erfahrungsschdufn", "container.enchant.level.one": "1 Erfahrungsschdufn", "container.enchant.level.requirement": "Eafoadaliche Erfahrungsschdufn: %s", "container.enderchest": "<PERSON><PERSON> Chest", "container.furnace": "Furnace", "container.grindstone_title": "Rebarian & Endzauban", "container.hopper": "<PERSON><PERSON><PERSON>", "container.inventory": "Inventory", "container.isLocked": "%s is veaschlossn!", "container.lectern": "Lectern", "container.loom": "Loom", "container.repair": "Rebarian & Bnennen", "container.repair.cost": "Eafahrungskosdn: %1$s", "container.repair.expensive": "<PERSON>u de<PERSON>!", "container.shulkerBox": "Shulker Box", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "und %s mea ...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Smoker", "container.spectatorCantOpen": "<PERSON>nnde ned geeffned wern. Inhald wuad noch nid generierd.", "container.stonecutter": "<PERSON><PERSON><PERSON>", "container.upgrade": "Ausrüstung oufwerdn", "container.upgrade.error_tooltip": "Item can't be upgraded this way", "container.upgrade.missing_template_tooltip": "Add <PERSON> Template", "controls.keybinds": "Taschdenbelegung...", "controls.keybinds.duplicateKeybinds": "This key is also used for:\n%s", "controls.keybinds.title": "Taschdenbelegung", "controls.reset": "Schdandard", "controls.resetAll": "Dasdn zurüggsetzn", "controls.title": "Schdeuerung", "createWorld.customize.buffet.biome": "Bidde wähl an Biom", "createWorld.customize.buffet.title": "Anbassung der Buffet-Weld", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Bodn - %s", "createWorld.customize.flat.layer.top": "Oberfläche - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON><PERSON> end<PERSON>nn", "createWorld.customize.flat.tile": "Sc<PERSON><PERSON>dmaderial", "createWorld.customize.flat.title": "Subbeflachlandanbassung", "createWorld.customize.presets": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.presets.list": "Aldernadiv san hia a boo von uns erschdellde!", "createWorld.customize.presets.select": "<PERSON><PERSON><PERSON><PERSON>e", "createWorld.customize.presets.share": "Möchtest du dei Voalog mid jemandem teiln? Benutze des Eingobfejd!", "createWorld.customize.presets.title": "Vorlache auswähln", "createWorld.preparing": "Vorbreitungn füa Welderschaffung...", "createWorld.tab.game.title": "Game", "createWorld.tab.more.title": "More", "createWorld.tab.world.title": "World", "credits_and_attribution.button.attribution": "Attribution", "credits_and_attribution.button.credits": "Credits", "credits_and_attribution.button.licenses": "Licenses", "credits_and_attribution.screen.title": "Credits and Attribution", "dataPack.bundle.description": "Enables experimental Bundle item", "dataPack.bundle.name": "Bundles", "dataPack.locator_bar.description": "Show the direction of other players in multiplayer", "dataPack.locator_bar.name": "Locator Bar", "dataPack.minecart_improvements.description": "Improved movement for Minecarts", "dataPack.minecart_improvements.name": "Minecart Improvements", "dataPack.redstone_experiments.description": "Experimental Redstone changes", "dataPack.redstone_experiments.name": "Redstone Experiments", "dataPack.title": "<PERSON><PERSON>bagede auswähln", "dataPack.trade_rebalance.description": "Updated trades for Villagers", "dataPack.trade_rebalance.name": "Villager Trade Rebalance", "dataPack.update_1_20.description": "New features and content for Minecraft 1.20", "dataPack.update_1_20.name": "Update 1.20", "dataPack.update_1_21.description": "New features and content for Minecraft 1.21", "dataPack.update_1_21.name": "Update 1.21", "dataPack.validation.back": "<PERSON><PERSON><PERSON><PERSON>", "dataPack.validation.failed": "Übabrüfung dea Dad<PERSON> hat net funktioniad!", "dataPack.validation.reset": "Oaf Sdandard zurüchsedzn", "dataPack.validation.working": "Ausgwählde Dadnbagede werdn übabrüfd...", "dataPack.vanilla.description": "Die Sdandarddadn füa Minecraft", "dataPack.vanilla.name": "<PERSON><PERSON><PERSON>", "dataPack.winter_drop.description": "New features and content for the Winter Drop", "dataPack.winter_drop.name": "Winter Drop", "datapackFailure.safeMode": "<PERSON>b<PERSON><PERSON><PERSON>", "datapackFailure.safeMode.failed.description": "This world contains invalid or corrupted save data.", "datapackFailure.safeMode.failed.title": "Failed to load world in Safe Mode.", "datapackFailure.title": "Errors in currently selected data packs prevented the world from loading.\nYou can either try to load it with only the vanilla data pack (\"safe mode\"), or go back to the title screen and fix it manually.", "death.attack.anvil": "%1$s wuade von nem fallnden Amboss zeaquedschd", "death.attack.anvil.player": "%1$s wuade wähand des Kampfs mid %2$s von nem fallnden Amboss zeaquedschd", "death.attack.arrow": "%1$s wuade von %2$s easchossn", "death.attack.arrow.item": "%1$s wuade von %2$s mid %3$s easchossn", "death.attack.badRespawnPoint.link": "beabsichdigdm Spieldesign", "death.attack.badRespawnPoint.message": "%1$s wuad von %2$s gdöded", "death.attack.cactus": "%1$s waud von nem <PERSON> zu Dode gsdochn", "death.attack.cactus.player": "%1$s lief beim <PERSON>, %2$s zu endkommn, in nen Ka<PERSON>dus", "death.attack.cramming": "%1$s wuad zaquedschd", "death.attack.cramming.player": "%1$s wuad von %2$s zeaquedschd", "death.attack.dragonBreath": "%1$s was roasted in dragon's breath", "death.attack.dragonBreath.player": "%1$s was roasted in dragon's breath by %2$s", "death.attack.drown": "%1$s easoff", "death.attack.drown.player": "%1$s easoff beim <PERSON>, %2$s zu endkommn", "death.attack.dryout": "%1$s dähydrirdä", "death.attack.dryout.player": "%1$s dähydrirdä beim Veasuch, %2$s zua endkommän", "death.attack.even_more_magic": "%1$s wuad duach veastäakde Moagie gdöded", "death.attack.explosion": "%1$s wuad in di Lufd gsprengd", "death.attack.explosion.player": "%1$s waud von %2$s in die Lufd gsprengd", "death.attack.explosion.player.item": "%1$s wuad von %2$s mid %3$s in die Lufd gsprengd", "death.attack.fall": "%1$s fiel dea Schweakrafd zum Opfa", "death.attack.fall.player": "%1$s fiel beim <PERSON>, %2$s zu <PERSON>n, dea <PERSON>akrafd zum Opfa", "death.attack.fallingBlock": "%1$s wuade von nem fallnden Blogg zeaquedschd", "death.attack.fallingBlock.player": "%1$s wuade wähand des Kampfs mid %2$s von nem fallnden Blogg zeaquedschd", "death.attack.fallingStalactite": "%1$s wuad uffgspießt von nem fallnden Stalagdidn", "death.attack.fallingStalactite.player": "%1$s wuad währnd des Kampfs med %2$s von nem fallnden Stalagdidn uffgspießt", "death.attack.fireball": "%1$s wuade von %2$s flambiad", "death.attack.fireball.item": "%1$s wuade von %2$s mid %3$s flambiad", "death.attack.fireworks": "%1$s floch mid am Knoall in die Luaft", "death.attack.fireworks.item": "%1$s floch uffgrund anes Feua<PERSON>aks, des von %2$s mid %3$s abgschossa wuad, mid am Knoall in die Luaft", "death.attack.fireworks.player": "%1$s floch währnd des Kampfs mid %2$s mid am Knoall in die Luaft", "death.attack.flyIntoWall": "%1$s eafuha kinetische Eneagie", "death.attack.flyIntoWall.player": "%1$s eafua be<PERSON>, %2$s zu endkommn, kinetische Eneagie", "death.attack.freeze": "%1$s is eafrorn", "death.attack.freeze.player": "%1$s is duach %2$s eafrorn", "death.attack.generic": "%1$s stab", "death.attack.generic.player": "%1$s stab wegn %2$s", "death.attack.genericKill": "%1$s was killed", "death.attack.genericKill.player": "%1$s was killed while fighting %2$s", "death.attack.hotFloor": "%1$s wuad dea <PERSON> zu hoaß", "death.attack.hotFloor.player": "%1$s walked into the danger zone due to %2$s", "death.attack.inFire": "%1$s ging in Flammn auf", "death.attack.inFire.player": "%1$s lief wähand des Kampfs mid %2$s ins Feua", "death.attack.inWall": "%1$s wuad bai lebendichn Laib bgrabn", "death.attack.inWall.player": "%1$s wuad wähand des Kampfs mid %2$s lebendich bgrabn", "death.attack.indirectMagic": "%1$s wuad von %2$s mid Moagie gdöded", "death.attack.indirectMagic.item": "%1$s wuad von %2$s mid %3$s gdöded", "death.attack.lava": "%1$s veasuchde in Loava zu schwimmn", "death.attack.lava.player": "%1$s fiel beim <PERSON>, %2$s zu endkommn, in Loava", "death.attack.lightningBolt": "%1$s wuad vom Blidz gdroffn", "death.attack.lightningBolt.player": "%1$s wuad wähand des Kampfs mid %2$s vom Blidz gdroffn", "death.attack.mace_smash": "%1$s was smashed by %2$s", "death.attack.mace_smash.item": "%1$s was smashed by %2$s with %3$s", "death.attack.magic": "%1$s wuad duach Moagie gdöded", "death.attack.magic.player": "%1$s wuad be<PERSON>, %2$s zua entkomma, duach Mogie getötet", "death.attack.message_too_long": "Actually, the message was too long to deliver fully. Sorry! Here's a stripped version: %s", "death.attack.mob": "%1$s wuade von %2$s easchloaggn", "death.attack.mob.item": "%1$s wuade von %2$s mid %3$s easchloaggn", "death.attack.onFire": "%1$s vabrannde", "death.attack.onFire.item": "%1$s was burned to a crisp while fighting %2$s wielding %3$s", "death.attack.onFire.player": "%1$s wuade wähand des Kampfs mid %2$s grö<PERSON>ded", "death.attack.outOfWorld": "%1$s fiel aus dea Weld", "death.attack.outOfWorld.player": "%1$s wollde nich mea in deaselbn Weld wie %2$s lebn", "death.attack.outsideBorder": "%1$s left the confines of this world", "death.attack.outsideBorder.player": "%1$s left the confines of this world while fighting %2$s", "death.attack.player": "%1$s was slain by %2$s", "death.attack.player.item": "%1$s was slain by %2$s using %3$s", "death.attack.sonic_boom": "%1$s was obliterated by a sonically-charged shriek", "death.attack.sonic_boom.item": "%1$s was obliterated by a sonically-charged shriek while trying to escape %2$s wielding %3$s", "death.attack.sonic_boom.player": "%1$s was obliterated by a sonically-charged shriek while trying to escape %2$s", "death.attack.stalagmite": "%1$s wuad von nem Stalagmidn aufgschbießd", "death.attack.stalagmite.player": "%1$s wuad wäahnd des Kampfs mid %2$s von nem Stalagmidn aufgschbießd", "death.attack.starve": "%1$s veahungade", "death.attack.starve.player": "%1$s veahungade wähand des Kampfs mid %2$s", "death.attack.sting": "%1$s hats zamgstochn", "death.attack.sting.item": "%1$s was stung to death by %2$s using %3$s", "death.attack.sting.player": "%1$s hats von %2$s zamgstochn", "death.attack.sweetBerryBush": "%1$s had sich an nem Süßbeeanstrauach zu Dode gstochn", "death.attack.sweetBerryBush.player": "%1$s had sich beim <PERSON>, %2$s zu endkommn, an nem Süßbeeanstracuh zu Dode gstochn", "death.attack.thorns": "%1$s wuad beim <PERSON>, %2$s zu vealet<PERSON>n, gdöded", "death.attack.thorns.item": "%1$s wuad beim <PERSON>, %2$s zu vealet<PERSON>n, von %3$s gdöded", "death.attack.thrown": "%1$s wuade von %2$s zu Dode gbrügld", "death.attack.thrown.item": "%1$s wuade von %2$s mid %3$s zu Dode gbrügld", "death.attack.trident": "%1$s wuad von %2$s aufgspießd", "death.attack.trident.item": "%1$s wuad von %2$s mid %3$s aufgspießd", "death.attack.wither": "%1$s veadoadde", "death.attack.wither.player": "%1$s veadoadde wähand des Kampfs mid %2$s", "death.attack.witherSkull": "%1$s wuad von %2$s mit am Schädl daschossn", "death.attack.witherSkull.item": "%1$s was shot by a skull from %2$s using %3$s", "death.fell.accident.generic": "%1$s fiel aus zu großa Höh", "death.fell.accident.ladder": "%1$s stüazde von na Leida", "death.fell.accident.other_climbable": "%1$s stüazde bem Kläddarn oab", "death.fell.accident.scaffolding": "%1$s is vom Gerüst runter gfolln", "death.fell.accident.twisting_vines": "%1$s stüazde vo Zwiabelrankn oab", "death.fell.accident.vines": "%1$s stüaz<PERSON> von <PERSON>", "death.fell.accident.weeping_vines": "%1$s stüazde vo Trauerrankn oab", "death.fell.assist": "%1$s wuad von %2$s zum Absduaz veadammd", "death.fell.assist.item": "%1$s wuad von %2$s mid %3$s zum Absduaz veadammd", "death.fell.finish": "%1$s fiel zu dief und wuad von %2$s ealedigd", "death.fell.finish.item": "%1$s fiel zu dief und wuad von %2$s mid %3$s ealedigd", "death.fell.killer": "%1$s wuad zum Absduaz veadammd", "deathScreen.quit.confirm": "Bisd de sicha, dass de das Schbiel vealassn willsd?", "deathScreen.respawn": "Wiedebelebe", "deathScreen.score": "Bunkdestand", "deathScreen.score.value": "Score: %s", "deathScreen.spectate": "Weld beobachdn", "deathScreen.title": "Du hast ins Groas gbissn!", "deathScreen.title.hardcore": "Schbiel vorba!", "deathScreen.titleScreen": "Title Screen", "debug.advanced_tooltips.help": "F3 + H = Eaweidade Schnellinfos", "debug.advanced_tooltips.off": "Eaweidade Schnellinfos: veschdeggd", "debug.advanced_tooltips.on": "Eaweidade Schnellinfos: sichdba", "debug.chunk_boundaries.help": "F3 + G = Chunkgrenzn anzeign", "debug.chunk_boundaries.off": "Chunkgrenzn: veschdeggd", "debug.chunk_boundaries.on": "Chunkgrenzn: sichdba", "debug.clear_chat.help": "F3 + D = Chat leean", "debug.copy_location.help": "F3 + C = Bosition als /tp-B<PERSON>hl kobiean oda gdr<PERSON>d haldn, um an Abstuaz zu eazwingn", "debug.copy_location.message": "Bosition wuad in die Zwischnablache kobiead", "debug.crash.message": "F3 + C san gdrückd. Dies wiad das Schbiel abstüazn lasnn, wenn se ned los<PERSON>n weadn.", "debug.crash.warning": "Abstuaz in %s ...", "debug.creative_spectator.error": "Unable to switch game mode; no permission", "debug.creative_spectator.help": "F3 + N = Cycle previous game mode <-> spectator", "debug.dump_dynamic_textures": "Saved dynamic textures to %s", "debug.dump_dynamic_textures.help": "F3 + S = Dump dynamic textures", "debug.gamemodes.error": "Spielmoddusauswahl kann wechn fehlenda Berechdigung net geöffned werdn", "debug.gamemodes.help": "F3 + F4 = Spielmoddusauswahl öffn'n", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Näxda", "debug.help.help": "F3 + Q = <PERSON>se Li<PERSON>dn anzeign", "debug.help.message": "Dasdnbelechung:", "debug.inspect.client.block": "Cliendseidige Bloggdadn in Zwischnablache kobiead", "debug.inspect.client.entity": "Cliendseidige Objekddadn in Zwischnablache kobiead", "debug.inspect.help": "F3 + I = Blogg- oda O<PERSON>adn in Zwischnablache kobiean", "debug.inspect.server.block": "Seavaseidige Bloggdadn in Zwischnablache kobiead", "debug.inspect.server.entity": "Seavaseidige Objekddadn in Zwischnablache kobiead", "debug.pause.help": "F3 + Esc = Schbiel ohne Bausemenü anhaldn (falls möglich)", "debug.pause_focus.help": "F3 + P = Bausiean bei Fokusvealusd", "debug.pause_focus.off": "Bausian bei Fokusvealusd: deaktiviad", "debug.pause_focus.on": "Bausian bei Fokusvealusd: aktiviad", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Profileastellung staadn/beendn", "debug.profiling.start": "Debugg‐Uffzeichnung wurde fuar %s Sekunden g`starded. B`nutze F3 + L, um sie vorzeidig zu b`enden", "debug.profiling.stop": "Profileastellung beended. Eagebnisse wuadn in %s gschpaichad", "debug.reload_chunks.help": "F3 + A = Chunks neu ladn", "debug.reload_chunks.message": "Alle Chunks weadn neu gladn", "debug.reload_resourcepacks.help": "F3 + T = Ressourcnbagede neu ladn", "debug.reload_resourcepacks.message": "Ressourcnbagede neu gladn", "debug.show_hitboxes.help": "F3 + B = Hitboxn anzeign", "debug.show_hitboxes.off": "Hitboxen: veschdeggd", "debug.show_hitboxes.on": "Hitboxen: sichdba", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Client version info", "demo.day.1": "De Demo haxhoidet fünf Schbuidoge. Gib dei Bests!", "demo.day.2": "Zweida Dooch", "demo.day.3": "<PERSON><PERSON><PERSON>", "demo.day.4": "<PERSON><PERSON>", "demo.day.5": "Fünfda und letzda Dooch!", "demo.day.6": "<PERSON>i fün<PERSON>a Schbuidog is zua End. Drugge %s, um oan Screenshot doana Schöpfung obzuspeichern.", "demo.day.warning": "<PERSON><PERSON> is scho fast rum!", "demo.demoExpired": "<PERSON> Demo-<PERSON>eid is rum!", "demo.help.buy": "<PERSON>zderla kaafn!", "demo.help.fullWrapped": "Diese Demo dauad 5 Schbieldache (ca. 1 Stunde und 40 Minudn echde Zoad). <PERSON>hau dia die Foadschridde füa Hinweise an! Viel Schboaß!", "demo.help.inventory": "Mid %1$s effnersde dei Invendoa", "demo.help.jump": "Drügg %1$s zum Schbringe", "demo.help.later": "Weideschbieln!", "demo.help.movement": "Drügg %1$s, %2$s, %3$s, %4$s und nimm de Maus zum bewechn", "demo.help.movementMouse": "<PERSON><PERSON><PERSON> dich midde Maus um", "demo.help.movementShort": "Bewech dich mid %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft-Demo-Moddus", "demo.remainingTime": "Resdliche Zeid: %s", "demo.reminder": "De Demozeid is obgel<PERSON><PERSON>. <PERSON><PERSON><PERSON> des Schbui, um foatzu<PERSON>n, oda eastäi a neie Wäid!", "difficulty.lock.question": "Me<PERSON>d de <PERSON>wi<PERSON><PERSON><PERSON> von der Weld wirglich fixiern? Des wird de Weld füa imme oaf %1$s setzn und du kannsd de Einschdellung nimmer änden.", "difficulty.lock.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vonne Weld fixiern", "disconnect.endOfStream": "<PERSON><PERSON> <PERSON>", "disconnect.exceeded_packet_rate": "Wechn Übaschreitung des Bageedratnlimits rausgworfn", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignoring status request", "disconnect.loginFailedInfo": "Anmeldung fehlschlochn: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Der Measchbielamoddus is deaktivierd. Bidde übaprüf die Anstellungn deines Microsoft-Kontos.", "disconnect.loginFailedInfo.invalidSession": "Ungüldiche Sitzung (Veruch, es Schbiel und en Launcher neuzuschdadde)", "disconnect.loginFailedInfo.serversUnavailable": "Die Authendifizierungsseava san deazeid ned eareichba. Bidde veasuch es späda nochamal.", "disconnect.loginFailedInfo.userBanned": "You are banned from playing online", "disconnect.lost": "Vebindung unnebrochn", "disconnect.packetError": "Network Protocol Error", "disconnect.spam": "Nausgworffe wechn Spamming", "disconnect.timeout": "Zeidübeschreidung", "disconnect.transfer": "Transferred to another server", "disconnect.unknownHost": "Unbegannda Sörva", "download.pack.failed": "%s out of %s pack(s) failed to download", "download.pack.progress.bytes": "Progress: %s (total size unknown)", "download.pack.progress.percent": "Progress: %s%%", "download.pack.title": "Downloading resource pack %s/%s", "editGamerule.default": "Sdandardwedde: %s", "editGamerule.title": "Beabeidn der Spiel Regln", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absoabtion", "effect.minecraft.bad_omen": "<PERSON><PERSON><PERSON>", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Meerskrafd", "effect.minecraft.darkness": "Darkness", "effect.minecraft.dolphins_grace": "Gunsd des Delfins", "effect.minecraft.fire_resistance": "Feuaschutz", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON>", "effect.minecraft.haste": "<PERSON><PERSON>", "effect.minecraft.health_boost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.hero_of_the_village": "Held des Doafs", "effect.minecraft.hunger": "<PERSON><PERSON>", "effect.minecraft.infested": "Infested", "effect.minecraft.instant_damage": "Direkdschadn", "effect.minecraft.instant_health": "<PERSON>rek<PERSON><PERSON><PERSON>", "effect.minecraft.invisibility": "Unsichdbakeid", "effect.minecraft.jump_boost": "Sprungkrafd", "effect.minecraft.levitation": "Schwebekrafd", "effect.minecraft.luck": "Glück", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "Üblkeid", "effect.minecraft.night_vision": "Na<PERSON>dsichd", "effect.minecraft.oozing": "Oozing", "effect.minecraft.poison": "<PERSON><PERSON>gi<PERSON><PERSON><PERSON>", "effect.minecraft.raid_omen": "Raid Omen", "effect.minecraft.regeneration": "Regeneration", "effect.minecraft.resistance": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.saturation": "Säddichung", "effect.minecraft.slow_falling": "Sanfda Fall", "effect.minecraft.slowness": "Langsamke<PERSON>", "effect.minecraft.speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.strength": "Stärkn", "effect.minecraft.trial_omen": "Trial Omen", "effect.minecraft.unluck": "Pech", "effect.minecraft.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.weakness": "Schwächn", "effect.minecraft.weaving": "Weaving", "effect.minecraft.wind_charged": "Wind Charged", "effect.minecraft.wither": "<PERSON>er", "effect.none": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Wassaaffinidäd", "enchantment.minecraft.bane_of_arthropods": "Veadeabn dea Gliedafüßla", "enchantment.minecraft.binding_curse": "<PERSON><PERSON><PERSON> dea <PERSON>", "enchantment.minecraft.blast_protection": "Exblosionsschutz", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "End<PERSON><PERSON>", "enchantment.minecraft.density": "Density", "enchantment.minecraft.depth_strider": "Was<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "Effizienz", "enchantment.minecraft.feather_falling": "Fedafall", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_protection": "Feuaschutz", "enchantment.minecraft.flame": "<PERSON>lamme", "enchantment.minecraft.fortune": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.frost_walker": "Aisläufa", "enchantment.minecraft.impaling": "Habune", "enchantment.minecraft.infinity": "<PERSON>endlichkeid", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "Blündarung", "enchantment.minecraft.loyalty": "Dreue", "enchantment.minecraft.luck_of_the_sea": "G<PERSON><PERSON><PERSON> des Meeas", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "Re<PERSON>dua", "enchantment.minecraft.multishot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "Stärkn", "enchantment.minecraft.projectile_protection": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.protection": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.punch": "Schloach", "enchantment.minecraft.quick_charge": "Schnellladn", "enchantment.minecraft.respiration": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.riptide": "Soch", "enchantment.minecraft.sharpness": "Schäafä", "enchantment.minecraft.silk_touch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.smite": "Bann", "enchantment.minecraft.soul_speed": "Seelnläufa", "enchantment.minecraft.sweeping": "Schwungkrafd", "enchantment.minecraft.sweeping_edge": "Sweeping Edge", "enchantment.minecraft.swift_sneak": "Swift Sneak", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "Fluch des Veaschwindns", "enchantment.minecraft.wind_burst": "<PERSON> Burst", "entity.minecraft.acacia_boat": "Acacia Boat", "entity.minecraft.acacia_chest_boat": "Acacia Boat with Chest", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Badiklwolkn", "entity.minecraft.armadillo": "Armadillo", "entity.minecraft.armor_stand": "Armor Stand", "entity.minecraft.arrow": "Arrow", "entity.minecraft.axolotl": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bamboo_chest_raft": "Bamboo Raft with Chest", "entity.minecraft.bamboo_raft": "Bamboo Raft", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Biena", "entity.minecraft.birch_boat": "<PERSON> Boat", "entity.minecraft.birch_chest_boat": "<PERSON> Boat with Chest", "entity.minecraft.blaze": "Lohe", "entity.minecraft.block_display": "Block Display", "entity.minecraft.boat": "Boat", "entity.minecraft.bogged": "Bogged", "entity.minecraft.breeze": "<PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Wind Charge", "entity.minecraft.camel": "Camel", "entity.minecraft.cat": "Koatzn", "entity.minecraft.cave_spider": "Höhlnschbinn", "entity.minecraft.cherry_boat": "Cherry Boat", "entity.minecraft.cherry_chest_boat": "Cherry Boat with Chest", "entity.minecraft.chest_boat": "Boat with Chest", "entity.minecraft.chest_minecart": "Minecart with Chest", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Minecart with Command Block", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "Creaking", "entity.minecraft.creaking_transient": "Creaking", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Dark Oak Boat", "entity.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "entity.minecraft.dolphin": "Delphin", "entity.minecraft.donkey": "Esl", "entity.minecraft.dragon_fireball": "Drachn-Feuakugl", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.egg": "Gworfnes Gaggeli", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.end_crystal": "End Crystal", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "G<PERSON>rf<PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.evoker": "Mogie", "entity.minecraft.evoker_fangs": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.experience_bottle": "Gworfnes Eafohrungsfläschla", "entity.minecraft.experience_orb": "Eaf<PERSON>rungsbool", "entity.minecraft.eye_of_ender": "Eye of <PERSON>er", "entity.minecraft.falling_block": "Fallnda Blogg", "entity.minecraft.falling_block_type": "Falling %s", "entity.minecraft.fireball": "Feuakugl", "entity.minecraft.firework_rocket": "Firework Rocket", "entity.minecraft.fishing_bobber": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.fox": "Fuchs", "entity.minecraft.frog": "<PERSON>", "entity.minecraft.furnace_minecart": "Minecart with Furnace", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Gigand", "entity.minecraft.glow_item_frame": "G<PERSON> Item <PERSON>", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.goat": "<PERSON><PERSON>gn", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "<PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Minecart with <PERSON>", "entity.minecraft.horse": "Gaul", "entity.minecraft.husk": "Wüsdnzombie", "entity.minecraft.illusioner": "Illusionisd", "entity.minecraft.interaction": "Interaction", "entity.minecraft.iron_golem": "Aasngolm", "entity.minecraft.item": "Gechnschdand", "entity.minecraft.item_display": "<PERSON><PERSON>", "entity.minecraft.item_frame": "<PERSON><PERSON>", "entity.minecraft.jungle_boat": "Jungle Boat", "entity.minecraft.jungle_chest_boat": "Jungle Boat with Chest", "entity.minecraft.killer_bunny": "<PERSON><PERSON><PERSON>", "entity.minecraft.leash_knot": "Lainenknodn", "entity.minecraft.lightning_bolt": "Blidz", "entity.minecraft.lingering_potion": "Lingering Potion", "entity.minecraft.llama": "<PERSON><PERSON>", "entity.minecraft.llama_spit": "Lomaschbugge", "entity.minecraft.magma_cube": "Mochmawüffel", "entity.minecraft.mangrove_boat": "Mangrove Boat", "entity.minecraft.mangrove_chest_boat": "Mangrove Boat with Chest", "entity.minecraft.marker": "<PERSON><PERSON>", "entity.minecraft.minecart": "Minecart", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON><PERSON>", "entity.minecraft.oak_boat": "Oak Boat", "entity.minecraft.oak_chest_boat": "Oak Boat with Chest", "entity.minecraft.ocelot": "<PERSON><PERSON>d", "entity.minecraft.ominous_item_spawner": "Ominous <PERSON>em <PERSON>wner", "entity.minecraft.painting": "Painting", "entity.minecraft.pale_oak_boat": "Pale Oak Boat", "entity.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "entity.minecraft.panda": "<PERSON><PERSON>", "entity.minecraft.parrot": "Babagei", "entity.minecraft.phantom": "<PERSON><PERSON><PERSON>", "entity.minecraft.pig": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.pillager": "Blündera", "entity.minecraft.player": "Sc<PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "Eisbeä", "entity.minecraft.potion": "Potion", "entity.minecraft.pufferfish": "Pufferfish", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Vawüsda", "entity.minecraft.salmon": "<PERSON><PERSON>", "entity.minecraft.sheep": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Shulkagschoss", "entity.minecraft.silverfish": "Silbefisch", "entity.minecraft.skeleton": "Sgeledd", "entity.minecraft.skeleton_horse": "Sgeleddgaul", "entity.minecraft.slime": "Schloim", "entity.minecraft.small_fireball": "<PERSON><PERSON>gl", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Schnähgolm", "entity.minecraft.snowball": "Snowball", "entity.minecraft.spawner_minecart": "<PERSON><PERSON><PERSON> with <PERSON> Spawner", "entity.minecraft.spectral_arrow": "Spectral Arrow", "entity.minecraft.spider": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.splash_potion": "Splash Potion", "entity.minecraft.spruce_boat": "Spruce Boat", "entity.minecraft.spruce_chest_boat": "Spruce Boat with Chest", "entity.minecraft.squid": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.stray": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.strider": "Schreida", "entity.minecraft.tadpole": "Tadpole", "entity.minecraft.text_display": "Text Display", "entity.minecraft.tnt": "Gzündedes DND", "entity.minecraft.tnt_minecart": "Minecart with TNT", "entity.minecraft.trader_llama": "Händlaloma", "entity.minecraft.trident": "Trident", "entity.minecraft.tropical_fish": "Tropical Fish", "entity.minecraft.tropical_fish.predefined.0": "Anemone", "entity.minecraft.tropical_fish.predefined.1": "Schwoaza Seglflossndogda", "entity.minecraft.tropical_fish.predefined.10": "Halfdafisch", "entity.minecraft.tropical_fish.predefined.11": "Orangschdraifn-Faldafisch", "entity.minecraft.tropical_fish.predefined.12": "Babageifisch", "entity.minecraft.tropical_fish.predefined.13": "Diadem-Brachdkaisafisch", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "Ruodlibbn-Schlaimfisch", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "Fingafisch", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "Drüggafisch", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.20": "Gelbschwoanz-Babageifisch", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.3": "Faldafisch", "entity.minecraft.tropical_fish.predefined.4": "Bundbasch", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Zuggawaddn-Kampffisch", "entity.minecraft.tropical_fish.predefined.7": "Zwerchbasch", "entity.minecraft.tropical_fish.predefined.8": "Kaisa-Sc<PERSON>ab<PERSON>", "entity.minecraft.tropical_fish.predefined.9": "Meabarbn", "entity.minecraft.tropical_fish.type.betty": "Beidschnfisch", "entity.minecraft.tropical_fish.type.blockfish": "Bloggfisch", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.dasher": "Flidza", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.spotty": "Fleggn-Libbnfisch", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.turtle": "<PERSON><PERSON>dgrö<PERSON>", "entity.minecraft.vex": "Gschbensd", "entity.minecraft.villager": "Villager", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "<PERSON><PERSON>", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "<PERSON><PERSON>il<PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "Geaba", "entity.minecraft.villager.librarian": "Bibliotheka", "entity.minecraft.villager.mason": "<PERSON><PERSON>", "entity.minecraft.villager.nitwit": "Do<PERSON><PERSON>", "entity.minecraft.villager.none": "Doefbewohne", "entity.minecraft.villager.shepherd": "Schäfa", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "Waffnschmied", "entity.minecraft.vindicator": "<PERSON><PERSON>", "entity.minecraft.wandering_trader": "Fahrnda Händla", "entity.minecraft.warden": "Warden", "entity.minecraft.wind_charge": "Wind Charge", "entity.minecraft.witch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "With<PERSON>gel<PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zombie", "entity.minecraft.zombie_horse": "Zombiegaul", "entity.minecraft.zombie_villager": "Zombiedoefbewohne", "entity.minecraft.zombified_piglin": "Zombifizieada <PERSON>", "entity.not_summonable": "Can't summon entity of type %s", "event.minecraft.raid": "Übafall", "event.minecraft.raid.defeat": "Niedalagn", "event.minecraft.raid.defeat.full": "Raid - Defeat", "event.minecraft.raid.raiders_remaining": "Veableibende Räuba: %s", "event.minecraft.raid.victory": "Siech", "event.minecraft.raid.victory.full": "Raid - Victory", "filled_map.buried_treasure": "Schadzkaddn", "filled_map.explorer_jungle": "Jungle Explorer Map", "filled_map.explorer_swamp": "Swamp Explorer Map", "filled_map.id": "Nr. %s", "filled_map.level": "(Stufn %s/%s)", "filled_map.locked": "Locked", "filled_map.mansion": "Wald-Entdeckakaddn", "filled_map.monument": "Ozean-Entdeckakaddn", "filled_map.scale": "Maßstab 1:%s", "filled_map.trial_chambers": "Trial Explorer Map", "filled_map.unknown": "Unbekannde Kaddn", "filled_map.village_desert": "Desert Village Map", "filled_map.village_plains": "Plains Village Map", "filled_map.village_savanna": "Savanna Village Map", "filled_map.village_snowy": "Snowy Village Map", "filled_map.village_taiga": "Taiga Village Map", "flat_world_preset.minecraft.bottomless_pit": "Bodenlose Abgrund", "flat_world_preset.minecraft.classic_flat": "Nor<PERSON>les Flachland", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.redstone_ready": "Redstone, feddich, los", "flat_world_preset.minecraft.snowy_kingdom": "Schnähgönchreich", "flat_world_preset.minecraft.the_void": "<PERSON><PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON> vom Dunn<PERSON>ue", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.unknown": "???", "gameMode.adventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameMode.changed": "<PERSON><PERSON> wuad zu %s gändad", "gameMode.creative": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameMode.hardcore": "Hardcore-Moddus!", "gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameMode.survival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Controls whether or not fire and lava should be able to tick further than 8 chunks away from any player", "gamerule.announceAdvancements": "Foadschrid<PERSON> bkannd gebn", "gamerule.blockExplosionDropDecay": "In block interaction explosions, some blocks won't drop their loot", "gamerule.blockExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by block interactions are lost in the explosion.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Ausbeude", "gamerule.category.misc": "Miscellaneous", "gamerule.category.mobs": "<PERSON><PERSON>", "gamerule.category.player": "Player", "gamerule.category.spawning": "Erzeuchung", "gamerule.category.updates": "Weldakdualisierung", "gamerule.commandBlockOutput": "Bfehlsbloggausgab anzeichn", "gamerule.commandModificationBlockLimit": "Command modification block limit", "gamerule.commandModificationBlockLimit.description": "Number of blocks that can be changed at once by one command, such as fill or clone.", "gamerule.disableElytraMovementCheck": "Elytrenfluchübaprüfung deaktiviean", "gamerule.disablePlayerMovementCheck": "Disable player movement check", "gamerule.disableRaids": "<PERSON>ba<PERSON><PERSON><PERSON>", "gamerule.doDaylightCycle": "Voaranschreidn dea Daagezeid", "gamerule.doEntityDrops": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> falln lassn", "gamerule.doEntityDrops.description": "Kondrolliead das Fallnlassn dea Ausbeude von <PERSON> (einschließlich Invendarn), <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> usw.", "gamerule.doFireTick": "Feuaausbreidung", "gamerule.doImmediateRespawn": "Sofordiga Wiedaeinstieg", "gamerule.doInsomnia": "<PERSON><PERSON> eazeu<PERSON>n", "gamerule.doLimitedCrafting": "Rezebd zua Heastellung bnötigd", "gamerule.doLimitedCrafting.description": "If enabled, players will be able to craft only unlocked recipes.", "gamerule.doMobLoot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> falln lassn", "gamerule.doMobLoot.description": "Controls resource drops from mobs, including experience orbs.", "gamerule.doMobSpawning": "<PERSON><PERSON><PERSON>n", "gamerule.doMobSpawning.description": "Some entities might have separate rules.", "gamerule.doPatrolSpawning": "Blündera-Badrouille eazeuchn", "gamerule.doTileDrops": "Bloggausbeude falln lassn", "gamerule.doTileDrops.description": "Controls resource drops from blocks, including experience orbs.", "gamerule.doTraderSpawning": "Spawn Wandering Traders", "gamerule.doVinesSpread": "Vines spread", "gamerule.doVinesSpread.description": "Controls whether or not the Vines block spreads randomly to adjacent blocks. Does not affect other types of vine blocks such as Weeping Vines, Twisting Vines, etc.", "gamerule.doWardenSpawning": "Spawn Wardens", "gamerule.doWeatherCycle": "Weddawechsl", "gamerule.drowningDamage": "<PERSON><PERSON><PERSON> duach <PERSON>rinkn", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON> Ender <PERSON> vanish on death", "gamerule.enderPearlsVanishOnDeath.description": "Whether Ender <PERSON> thrown by a player vanish when that player dies.", "gamerule.entitiesWithPassengersCanUsePortals": "Entities with passengers can use portals", "gamerule.entitiesWithPassengersCanUsePortals.description": "Allow entities with passengers to teleport through Nether Portals, End Portals, and End Gateways.", "gamerule.fallDamage": "Fallschadn", "gamerule.fireDamage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.forgiveDeadPlayers": "Gstorbene Schbiela verberchn", "gamerule.forgiveDeadPlayers.description": "Veaärchärde neudrale Greaduan hörn oaf, wü<PERSON><PERSON> zu sein, wenn dea <PERSON>la in dea Näh stiabd.", "gamerule.freezeDamage": "Eafrierungsschadn", "gamerule.globalSoundEvents": "Global sound events", "gamerule.globalSoundEvents.description": "When certain game events happen, like a boss spawning, the sound is heard everywhere.", "gamerule.keepInventory": "Invendoa nachm Dod bhaldn", "gamerule.lavaSourceConversion": "Lava converts to source", "gamerule.lavaSourceConversion.description": "When flowing lava is surrounded on two sides by lava sources it converts into a source.", "gamerule.locatorBar": "Enable player <PERSON><PERSON><PERSON>", "gamerule.locatorBar.description": "When enabled, a bar is shown on the screen to indicate the direction of players.", "gamerule.logAdminCommands": "Adminisdra<PERSON><PERSON><PERSON><PERSON><PERSON> brod<PERSON>", "gamerule.maxCommandChainLength": "Obagrenze füa Befehlskeddn", "gamerule.maxCommandChainLength.description": "Applies to command block chains and functions.", "gamerule.maxCommandForkCount": "Command context limit", "gamerule.maxCommandForkCount.description": "Maximum number of contexts that can be used by commands like 'execute as'.", "gamerule.maxEntityCramming": "Obagrenze füa O<PERSON>dränge", "gamerule.minecartMaxSpeed": "Minecart max speed", "gamerule.minecartMaxSpeed.description": "Maximum default speed of a moving Minecart on land.", "gamerule.mobExplosionDropDecay": "In mob explosions, some blocks won't drop their loot", "gamerule.mobExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by mobs are lost in the explosion.", "gamerule.mobGriefing": "Zeastörung duach Greaduan", "gamerule.naturalRegeneration": "Gsundheit derholn", "gamerule.playersNetherPortalCreativeDelay": "Player's Nether portal delay in creative mode", "gamerule.playersNetherPortalCreativeDelay.description": "Time (in ticks) that a creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersNetherPortalDefaultDelay": "Player's Nether portal delay in non-creative mode", "gamerule.playersNetherPortalDefaultDelay.description": "Time (in ticks) that a non-creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersSleepingPercentage": "Schlafquotn", "gamerule.playersSleepingPercentage.description": "Die Prozendzahl o<PERSON>, die schlafn müssn, um a Noachd zu übaspringn.", "gamerule.projectilesCanBreakBlocks": "Projectiles can break blocks", "gamerule.projectilesCanBreakBlocks.description": "Controls whether impact projectiles will destroy blocks that are destructible by them.", "gamerule.randomTickSpeed": "Häufichkeit von Zuafallseragnissn", "gamerule.reducedDebugInfo": "Debug-In<PERSON>s <PERSON>", "gamerule.reducedDebugInfo.description": "Limits contents of debug screen.", "gamerule.sendCommandFeedback": "Bfehlsausgabe anzeichn", "gamerule.showDeathMessages": "<PERSON><PERSON><PERSON>nachrichd", "gamerule.snowAccumulationHeight": "Snow accumulation height", "gamerule.snowAccumulationHeight.description": "When it snows, layers of snow form on the ground up to at most this number of layers.", "gamerule.spawnChunkRadius": "Spawn chunk radius", "gamerule.spawnChunkRadius.description": "Amount of chunks that stay loaded around the overworld spawn position.", "gamerule.spawnRadius": "<PERSON><PERSON> Wiedaeinstigsbreichs", "gamerule.spawnRadius.description": "Controls the size of the area around the spawn point that players can spawn in.", "gamerule.spectatorsGenerateChunks": "Zuschaua erzeuchn Landschafd", "gamerule.tntExplodes": "Allow TNT to be activated and to explode", "gamerule.tntExplosionDropDecay": "In TNT explosions, some blocks won't drop their loot", "gamerule.tntExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by TNT are lost in the explosion.", "gamerule.universalAnger": "Allchemeine Veaänderung", "gamerule.universalAnger.description": "Veaägarde neudrale Greaduan greifn jedn <PERSON> in dea Nähe an - ned nua den Schbiela, der se veaägard had. Funktionierd am Besdn, wenn forgiveDeadPlayers deaktiviead is.", "gamerule.waterSourceConversion": "Water converts to source", "gamerule.waterSourceConversion.description": "When flowing water is surrounded on two sides by water sources it converts into a source.", "generator.custom": "Benutzadefinied", "generator.customized": "Angebassd (old)", "generator.minecraft.amplified": "DUCHLÖCHEDD", "generator.minecraft.amplified.info": "Hiweis: <PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON> schnein Computa.", "generator.minecraft.debug_all_block_states": "Debug-<PERSON><PERSON><PERSON>", "generator.minecraft.flat": "Flachland", "generator.minecraft.large_biomes": "Große Biome", "generator.minecraft.normal": "Schdandard", "generator.minecraft.single_biome_surface": "Eenzelnes Biom", "generator.single_biome_caves": "Caves", "generator.single_biome_floating_islands": "Schwäbene Inscheln", "gui.abuseReport.attestation": "By submitting this report, you confirm that the information you have provided is accurate and complete to the best of your knowledge.", "gui.abuseReport.comments": "Comments", "gui.abuseReport.describe": "Sharing details will help us make a well-informed decision.", "gui.abuseReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.abuseReport.discard.discard": "Leave and Discard Report", "gui.abuseReport.discard.draft": "Save as Draft", "gui.abuseReport.discard.return": "Continue Editing", "gui.abuseReport.discard.title": "Discard report and comments?", "gui.abuseReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.abuseReport.draft.discard": "Discard", "gui.abuseReport.draft.edit": "Continue Editing", "gui.abuseReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.abuseReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.abuseReport.draft.title": "Edit draft chat report?", "gui.abuseReport.error.title": "Problem sending your report", "gui.abuseReport.message": "Where did you observe the bad behavior?\nThis will help us in researching your case.", "gui.abuseReport.more_comments": "Please describe what happened:", "gui.abuseReport.name.comment_box_label": "Please describe why you want to report this name:", "gui.abuseReport.name.reporting": "You are reporting \"%s\".", "gui.abuseReport.name.title": "Report Inappropriate Player Name", "gui.abuseReport.observed_what": "Why are you reporting this?", "gui.abuseReport.read_info": "Learn About Reporting", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drugs or alcohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Someone is encouraging others to partake in illegal drug related activities or encouraging underage drinking.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Child sexual exploitation or abuse", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Someone is talking about or otherwise promoting indecent behavior involving children.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Defamation", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Someone is damaging your or someone else's reputation, for example sharing false information with the aim to exploit or mislead others.", "gui.abuseReport.reason.description": "Bäschreibongh:", "gui.abuseReport.reason.false_reporting": "False Reporting", "gui.abuseReport.reason.generic": "I want to report them", "gui.abuseReport.reason.generic.description": "I'm annoyed with them / they have done something I do not like.", "gui.abuseReport.reason.harassment_or_bullying": "Dah typ do tut mich Mobbe oder Beleidige", "gui.abuseReport.reason.harassment_or_bullying.description": "Someone is shaming, attacking, or bullying you or someone else. This includes when someone is repeatedly trying to contact you or someone else without consent or posting private personal information about you or someone else without consent (\"doxing\").", "gui.abuseReport.reason.hate_speech": "Haß<PERSON><PERSON><PERSON>", "gui.abuseReport.reason.hate_speech.description": "Someone is attacking you or another player based on characteristics of their identity, like religion, race, or sexuality.", "gui.abuseReport.reason.imminent_harm": "Threat of harm to others", "gui.abuseReport.reason.imminent_harm.description": "Someone is threatening to harm you or someone else in real life.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Non-consensual intimate imagery", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Someone is talking about, sharing, or otherwise promoting private and intimate images.", "gui.abuseReport.reason.self_harm_or_suicide": "Self-harm or suicide", "gui.abuseReport.reason.self_harm_or_suicide.description": "Someone is threatening to harm themselves in real life or talking about harming themselves in real life.", "gui.abuseReport.reason.sexually_inappropriate": "Sexually inappropriate", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins that are graphic in nature relating to sexual acts, sexual organs, and sexual violence.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorism or violent extremism", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Someone is talking about, promoting, or threatening to commit acts of terrorism or violent extremism for political, religious, ideological, or other reasons.", "gui.abuseReport.reason.title": "Select Report Category", "gui.abuseReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.abuseReport.select_reason": "Select Report Category", "gui.abuseReport.send": "Send Report", "gui.abuseReport.send.comment_too_long": "Please shorten the comment", "gui.abuseReport.send.error_message": "An error was returned while sending your report:\n'%s'", "gui.abuseReport.send.generic_error": "Encountered an unexpected error while sending your report.", "gui.abuseReport.send.http_error": "An unexpected HTTP error occurred while sending your report.", "gui.abuseReport.send.json_error": "Encountered malformed payload while sending your report.", "gui.abuseReport.send.no_reason": "Please select a report category", "gui.abuseReport.send.not_attested": "Please read the text above and tick the checkbox to be able to send the report", "gui.abuseReport.send.service_unavailable": "Unable to reach the Abuse Reporting service. Please make sure you are connected to the internet and try again.", "gui.abuseReport.sending.title": "Sending your report...", "gui.abuseReport.sent.title": "Report sent", "gui.abuseReport.skin.title": "Report Player Skin", "gui.abuseReport.title": "Report Player", "gui.abuseReport.type.chat": "Chat Messages", "gui.abuseReport.type.name": "Player Name", "gui.abuseReport.type.skin": "Player Skin", "gui.acknowledge": "Fährstandna", "gui.advancements": "Advancements", "gui.all": "<PERSON><PERSON>", "gui.back": "<PERSON><PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\nLearn more at the following link: %s", "gui.banned.description.permanent": "Your account is permanently banned, which means you can't play online or join Realms.", "gui.banned.description.reason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified it as %s, which goes against the Minecraft Community Standards.", "gui.banned.description.reason_id": "Code: %s", "gui.banned.description.reason_id_message": "Code: %s - %s", "gui.banned.description.temporary": "%s Until then, you can't play online or join Realms.", "gui.banned.description.temporary.duration": "Your account is temporarily suspended and will be reactivated in %s.", "gui.banned.description.unknownreason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified that it goes against the Minecraft Community Standards.", "gui.banned.name.description": "Your current name - \"%s\" - violates our Community Standards. You can play singleplayer, but will need to change your name to play online.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.name.title": "Name Not Allowed in Multiplayer", "gui.banned.reason.defamation_impersonation_false_information": "Impersonation or sharing information to exploit or mislead others", "gui.banned.reason.drugs": "References to illegal drugs", "gui.banned.reason.extreme_violence_or_gore": "Depictions of real-life excessive violence or gore", "gui.banned.reason.false_reporting": "Excessive false or inaccurate reports", "gui.banned.reason.fraud": "Fraudulent acquisition or use of content", "gui.banned.reason.generic_violation": "Violating Community Standards", "gui.banned.reason.harassment_or_bullying": "Abusive language used in a directed, harmful manner", "gui.banned.reason.hate_speech": "Hate speech or discrimination", "gui.banned.reason.hate_terrorism_notorious_figure": "References to hate groups, terrorist organizations, or notorious figures", "gui.banned.reason.imminent_harm_to_person_or_property": "Intent to cause real-life harm to persons or property", "gui.banned.reason.nudity_or_pornography": "Displaying lewd or pornographic material", "gui.banned.reason.sexually_inappropriate": "Topics or content of a sexual nature", "gui.banned.reason.spam_or_advertising": "Spam or advertising", "gui.banned.skin.description": "Your current skin violates our Community Standards. You can still play with a default skin, or select a new one.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.skin.title": "Skin Not Allowed", "gui.banned.title.permanent": "Account permanently banned", "gui.banned.title.temporary": "Account temporarily suspended", "gui.cancel": "Obreche", "gui.chatReport.comments": "Comments", "gui.chatReport.describe": "Die Ongabe von Oinzelheidn wird uns helfn, ene fundirde Entscheidung zu dreffn.", "gui.chatReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.chatReport.discard.discard": "Leave and Discard Report", "gui.chatReport.discard.draft": "Save as Draft", "gui.chatReport.discard.return": "Bäarbeitung weider mache", "gui.chatReport.discard.title": "Discard report and comments?", "gui.chatReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.chatReport.draft.discard": "Discard", "gui.chatReport.draft.edit": "Continue Editing", "gui.chatReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.chatReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.chatReport.draft.title": "Edit draft chat report?", "gui.chatReport.more_comments": "Bidde bschreib wat passiert is:", "gui.chatReport.observed_what": "Wärum tust de das Melden?", "gui.chatReport.read_info": "<PERSON>rf<PERSON> meer übers Meldn", "gui.chatReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.chatReport.select_chat": "Zu meldnde Nochrichd auswäln", "gui.chatReport.select_reason": "<PERSON><PERSON><PERSON><PERSON> oine Mälde Kategorie", "gui.chatReport.selected_chat": "%s Chat Message(s) Selected to Report", "gui.chatReport.send": "Mäldungh absände", "gui.chatReport.send.comments_too_long": "Bitte de Kommentar kürze", "gui.chatReport.send.no_reason": "<PERSON><PERSON>hl mal ne Mhldungs Kategorie bidde", "gui.chatReport.send.no_reported_messages": "Bidde wäl mindestns oine Nochrichd zum Meldn oos", "gui.chatReport.send.too_many_messages": "Trying to include too many messages in the report", "gui.chatReport.title": "<PERSON><PERSON><PERSON> meldn", "gui.chatSelection.context": "Messages surrounding this selection will be included to provide additional context", "gui.chatSelection.fold": "%s message(s) hidden", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s joined the chat", "gui.chatSelection.message.narrate": "%s said: %s at %s", "gui.chatSelection.selected": "%s/%s message(s) selected", "gui.chatSelection.title": "Select Chat Messages to Report", "gui.continue": "Continue", "gui.copy_link_to_clipboard": "Copy Link to Clipboard", "gui.days": "%s day(s)", "gui.done": "<PERSON><PERSON><PERSON>", "gui.down": "<PERSON><PERSON><PERSON>", "gui.entity_tooltip.type": "Typ: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Rejected %s files", "gui.fileDropFailure.title": "Failed to add files", "gui.hours": "%s hour(s)", "gui.loadingMinecraft": "Loading Minecraft", "gui.minutes": "%s minute(s)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s Gnopf", "gui.narrate.editBox": "%s-Eingabefeld: %s", "gui.narrate.slider": "%s <PERSON>la", "gui.narrate.tab": "%s tab", "gui.no": "Na", "gui.none": "<PERSON>", "gui.ok": "OK", "gui.open_report_dir": "Open Report Directory", "gui.proceed": "Weidermachn", "gui.recipebook.moreRecipes": "Rechdsgligg füa mehr", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Suchn...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON> wird gezeichd", "gui.recipebook.toggleRecipes.blastable": "Schmelzbores wird gezeichd", "gui.recipebook.toggleRecipes.craftable": "Herschdell<PERSON><PERSON> wird gezeichd", "gui.recipebook.toggleRecipes.smeltable": "Schmelzbores wird gezeichd", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON> wird gezeichd", "gui.report_to_server": "Report To Server", "gui.socialInteractions.blocking_hint": "Mid Microsoft-Kondo vawaldn", "gui.socialInteractions.empty_blocked": "Keen g's<PERSON><PERSON><PERSON><PERSON> Schbiela im G'schäwtz", "gui.socialInteractions.empty_hidden": "<PERSON><PERSON> is veschdeggd im G'schwatz", "gui.socialInteractions.hidden_in_chat": "<PERSON>'s<PERSON>watz von %s wird ned g'zeichd", "gui.socialInteractions.hide": "Veastegged im G'schwatz", "gui.socialInteractions.narration.hide": "Nachrichdn von %s aasblendn", "gui.socialInteractions.narration.report": "Spiela %s meldn", "gui.socialInteractions.narration.show": "Zeihe Nachrichdn von %s", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "<PERSON><PERSON> mid dem Name wuad g'funda", "gui.socialInteractions.search_hint": "Search...", "gui.socialInteractions.server_label.multiple": "%s - %s <PERSON><PERSON>bie<PERSON>", "gui.socialInteractions.server_label.single": "%s - %s <PERSON><PERSON>bie<PERSON>", "gui.socialInteractions.show": "Zeich im G'schwatz", "gui.socialInteractions.shown_in_chat": "<PERSON>'s<PERSON>watz von %s wird g'zeichd", "gui.socialInteractions.status_blocked": "Blocked", "gui.socialInteractions.status_blocked_offline": "g's<PERSON><PERSON><PERSON><PERSON> - ned da", "gui.socialInteractions.status_hidden": "Hidden", "gui.socialInteractions.status_hidden_offline": "veschdeggd - ned da", "gui.socialInteractions.status_offline": "<PERSON> da", "gui.socialInteractions.tab_all": "All", "gui.socialInteractions.tab_blocked": "g's<PERSON><PERSON><PERSON>t", "gui.socialInteractions.tab_hidden": "Veschdeggd", "gui.socialInteractions.title": "soschiale Intaagtionen", "gui.socialInteractions.tooltip.hide": "Nachrichtn asblendn", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON> meldn", "gui.socialInteractions.tooltip.report.disabled": "<PERSON><PERSON> is ned verfügba", "gui.socialInteractions.tooltip.report.no_messages": "<PERSON>ine meldebflichdign Nachrichdn von Spiela %s", "gui.socialInteractions.tooltip.report.not_reportable": "<PERSON>ser S<PERSON> kann ned g<PERSON><PERSON>d` wer<PERSON><PERSON>, weil seine Chatnachrichd´n aufm Serwa ned verifizierd wer´n könna", "gui.socialInteractions.tooltip.show": "Zeiche Nochrichdn", "gui.stats": "Stadistikn", "gui.toMenu": "Zurügg zur Seavaauswohl", "gui.toRealms": "Back to Realms List", "gui.toTitle": "Zurügg zum Haubdmenü", "gui.toWorld": "Back to World List", "gui.togglable_slot": "Click to disable slot", "gui.up": "<PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "Back (%ss)", "gui.waitingForResponse.title": "Waiting for Server", "gui.yes": "<PERSON>", "hanging_sign.edit": "Edit Hanging Sign Message", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Call", "instrument.minecraft.dream_goat_horn": "Dream", "instrument.minecraft.feel_goat_horn": "Feel", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Seek", "instrument.minecraft.sing_goat_horn": "Sing", "instrument.minecraft.yearn_goat_horn": "Yearn", "inventory.binSlot": "Gegnstand zeastöan", "inventory.hotbarInfo": "Schnellzugriffsleisdn mid %1$s+%2$s speichan", "inventory.hotbarSaved": "Schnellzugriffsleisdn gspeichard (mid %1$s+%2$s)", "item.canBreak": "<PERSON>nn abbaun:", "item.canPlace": "Kann blaziad werdn auf:", "item.canUse.unknown": "Unknown", "item.color": "Farb: %s", "item.components": "%s component(s)", "item.disabled": "Disabled item", "item.durability": "Haldbakeid: %s / %s", "item.dyed": "Gfäabd", "item.minecraft.acacia_boat": "Akazienhulzbood", "item.minecraft.acacia_chest_boat": "Acacia Boat with Chest", "item.minecraft.allay_spawn_egg": "Allay Spawn Egg", "item.minecraft.amethyst_shard": "Amethysdscherbn", "item.minecraft.angler_pottery_shard": "Angler Pottery Shard", "item.minecraft.angler_pottery_sherd": "Angler Pottery Sherd", "item.minecraft.apple": "Abfl", "item.minecraft.archer_pottery_shard": "Archer Pottery Shard", "item.minecraft.archer_pottery_sherd": "Archer <PERSON>y Sherd", "item.minecraft.armadillo_scute": "<PERSON><PERSON><PERSON>", "item.minecraft.armadillo_spawn_egg": "Armadillo Spawn Egg", "item.minecraft.armor_stand": "Rüsdungsschdände", "item.minecraft.arms_up_pottery_shard": "Arms Up Pottery Shard", "item.minecraft.arms_up_pottery_sherd": "Arms Up Pottery Sherd", "item.minecraft.arrow": "Pfail", "item.minecraft.axolotl_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.axolotl_spawn_egg": "Axolodl-Spawn-Gaggeli", "item.minecraft.baked_potato": "Ofnkadoffe", "item.minecraft.bamboo_chest_raft": "Bamboo Raft with Chest", "item.minecraft.bamboo_raft": "Bamboo Raft", "item.minecraft.bat_spawn_egg": "Fledamaus-Spawn-G<PERSON><PERSON><PERSON>", "item.minecraft.bee_spawn_egg": "Bie-Spawn-Aa", "item.minecraft.beef": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "Roude-Bede-<PERSON><PERSON>", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "Berknhulzbood", "item.minecraft.birch_chest_boat": "<PERSON> Boat with Chest", "item.minecraft.black_bundle": "Black Bundle", "item.minecraft.black_dye": "Schwoaza Fab<PERSON>doff", "item.minecraft.black_harness": "<PERSON> Harness", "item.minecraft.blade_pottery_shard": "Blade Pottery Shard", "item.minecraft.blade_pottery_sherd": "Blade Pottery Sherd", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_rod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_spawn_egg": "Lohn-Spawn-Gag<PERSON><PERSON>", "item.minecraft.blue_bundle": "Blue Bundle", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_egg": "Blue Egg", "item.minecraft.blue_harness": "<PERSON>", "item.minecraft.bogged_spawn_egg": "Bogged Spawn Egg", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolt Armor Trim", "item.minecraft.bone": "Gnochn", "item.minecraft.bone_meal": "Gnochnmehl", "item.minecraft.book": "<PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "Bordure Indented Banner Pattern", "item.minecraft.bow": "<PERSON><PERSON><PERSON>", "item.minecraft.bowl": "Schüssl", "item.minecraft.bread": "Weggla", "item.minecraft.breeze_rod": "<PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "Breeze Spawn Egg", "item.minecraft.brewer_pottery_shard": "Brewer Pottery Shard", "item.minecraft.brewer_pottery_sherd": "Brewer Pottery Sherd", "item.minecraft.brewing_stand": "Brewing Stand", "item.minecraft.brick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON> Bundle", "item.minecraft.brown_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.brown_egg": "<PERSON>", "item.minecraft.brown_harness": "<PERSON>", "item.minecraft.brush": "Brush", "item.minecraft.bucket": "<PERSON><PERSON>", "item.minecraft.bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty": "Empty", "item.minecraft.bundle.empty.description": "Can hold a mixed stack of items", "item.minecraft.bundle.full": "Full", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Burn Pottery Shard", "item.minecraft.burn_pottery_sherd": "Burn Pottery Sherd", "item.minecraft.camel_spawn_egg": "Camel Spawn Egg", "item.minecraft.carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "Karodde am Schdegge", "item.minecraft.cat_spawn_egg": "Koatzn-Spawn-Gaggeli", "item.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Höhlnschbinn-Spawn-Gaggeli", "item.minecraft.chainmail_boots": "Keddnschdiefl", "item.minecraft.chainmail_chestplate": "Keddnhemmd", "item.minecraft.chainmail_helmet": "Keddnhaum", "item.minecraft.chainmail_leggings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_boat": "Cherry Boat", "item.minecraft.cherry_chest_boat": "Cherry Boat with Chest", "item.minecraft.chest_minecart": "Lore mid Druhe", "item.minecraft.chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "Hüecher-Spawn-Gaggeli", "item.minecraft.chorus_fruit": "Chorusfruchd", "item.minecraft.clay_ball": "Tonklumbn", "item.minecraft.clock": "U<PERSON>", "item.minecraft.coal": "<PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.coast_armor_trim_smithing_template.new": "Coast Armor Trim", "item.minecraft.cocoa_beans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON><PERSON>", "item.minecraft.cod_bucket": "Ka<PERSON>l<PERSON>uaima", "item.minecraft.cod_spawn_egg": "Kabljau-Spawn-Gaggel<PERSON>", "item.minecraft.command_block_minecart": "Lore mid Bfehlsblogg", "item.minecraft.compass": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "Schdeeg", "item.minecraft.cooked_chicken": "Gie<PERSON><PERSON>", "item.minecraft.cooked_cod": "Gbradena <PERSON>", "item.minecraft.cooked_mutton": "Gbra<PERSON><PERSON>", "item.minecraft.cooked_porkchop": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cookie": "B<PERSON>zla", "item.minecraft.copper_ingot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cow_spawn_egg": "Kou-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.creaking_spawn_egg": "Creaking Spawn Egg", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per", "item.minecraft.creeper_banner_pattern.new": "Creeper Charge Banner Pattern", "item.minecraft.creeper_spawn_egg": "Creeper-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.crossbow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.crossbow.projectile": "Gschoss:", "item.minecraft.crossbow.projectile.multiple": "Projectile: %s x %s", "item.minecraft.crossbow.projectile.single": "Projectile: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON>", "item.minecraft.cyan_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cyan_harness": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Danger Pottery Shard", "item.minecraft.danger_pottery_sherd": "Danger Pottery Sherd", "item.minecraft.dark_oak_boat": "Schwoazächahulzbood", "item.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "item.minecraft.debug_stick": "Debug-Schdegge", "item.minecraft.debug_stick.empty": "%s hod kane Bloggzuschdände", "item.minecraft.debug_stick.select": "'%s' ausgewähld (%s)", "item.minecraft.debug_stick.update": "\"%s\" is edzerdla %s", "item.minecraft.diamond": "Diormand", "item.minecraft.diamond_axe": "Diormand<PERSON><PERSON>", "item.minecraft.diamond_boots": "Diormandschdiefl", "item.minecraft.diamond_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "Diormandpfeddrüsdung", "item.minecraft.diamond_leggings": "Diormandboischuz", "item.minecraft.diamond_pickaxe": "Diormandpickl", "item.minecraft.diamond_shovel": "Diormandschaufl", "item.minecraft.diamond_sword": "Diormandschwerd", "item.minecraft.disc_fragment_5": "Disc Fragment", "item.minecraft.disc_fragment_5.desc": "Schallbladdn - 5", "item.minecraft.dolphin_spawn_egg": "Delfin-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.donkey_spawn_egg": "Esl-Spawn-Gaggeli", "item.minecraft.dragon_breath": "Drochnadem", "item.minecraft.dried_kelp": "Gdrocknedea Seedang", "item.minecraft.drowned_spawn_egg": "Erdrunggenen-Spawn-Gaggeli", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON>ne Armor <PERSON>", "item.minecraft.echo_shard": "Echo Shard", "item.minecraft.egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.elder_guardian_spawn_egg": "Großa-Wächda-Spawn-Gaggeli", "item.minecraft.elytra": "<PERSON><PERSON>", "item.minecraft.emerald": "Stala", "item.minecraft.enchanted_book": "Verzauberds Bouch", "item.minecraft.enchanted_golden_apple": "Verzauberda goidena Abfl", "item.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_dragon_spawn_egg": "Ender Dragon Spawn Egg", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Enderman-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.endermite_spawn_egg": "Endermidn-Spawn-Gaggeli", "item.minecraft.evoker_spawn_egg": "Mogie-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.experience_bottle": "Eafohrungsfläschla", "item.minecraft.explorer_pottery_shard": "Explorer <PERSON><PERSON> Shard", "item.minecraft.explorer_pottery_sherd": "Explorer <PERSON><PERSON> She<PERSON>", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.eye_armor_trim_smithing_template.new": "Eye Armor Trim", "item.minecraft.feather": "Feda", "item.minecraft.fermented_spider_eye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.field_masoned_banner_pattern": "Field Masoned Banner Pattern", "item.minecraft.filled_map": "<PERSON><PERSON><PERSON>", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_rocket": "Feuaweaksrachede", "item.minecraft.firework_rocket.flight": "Fluchdaue:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Feuaweaksschdean", "item.minecraft.firework_star.black": "Schwoaz", "item.minecraft.firework_star.blue": "Blau", "item.minecraft.firework_star.brown": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "Custom", "item.minecraft.firework_star.cyan": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "Übagang zu", "item.minecraft.firework_star.flicker": "Funggln", "item.minecraft.firework_star.gray": "G<PERSON><PERSON>", "item.minecraft.firework_star.green": "<PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Hellblau", "item.minecraft.firework_star.light_gray": "Hellgrau", "item.minecraft.firework_star.lime": "Hellgrin", "item.minecraft.firework_star.magenta": "Magenda", "item.minecraft.firework_star.orange": "Orange", "item.minecraft.firework_star.pink": "<PERSON><PERSON>", "item.minecraft.firework_star.purple": "Violedd", "item.minecraft.firework_star.red": "<PERSON><PERSON>", "item.minecraft.firework_star.shape": "Unbegannde Fom", "item.minecraft.firework_star.shape.burst": "Exblosion", "item.minecraft.firework_star.shape.creeper": "Creeperfemich", "item.minecraft.firework_star.shape.large_ball": "Große Kugl", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON>", "item.minecraft.firework_star.shape.star": "Schdeanfemich", "item.minecraft.firework_star.trail": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.white": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.yellow": "<PERSON><PERSON><PERSON>", "item.minecraft.fishing_rod": "<PERSON><PERSON>", "item.minecraft.flint": "<PERSON><PERSON><PERSON>", "item.minecraft.flint_and_steel": "Feuazeuch", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.flow_armor_trim_smithing_template.new": "Flow Armor Trim", "item.minecraft.flow_banner_pattern": "<PERSON>", "item.minecraft.flow_banner_pattern.desc": "Flow", "item.minecraft.flow_banner_pattern.new": "<PERSON> <PERSON> Pattern", "item.minecraft.flow_pottery_sherd": "Flow Pottery Sherd", "item.minecraft.flower_banner_pattern": "<PERSON>", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "Flower Charge Banner Pattern", "item.minecraft.flower_pot": "Flower Pot", "item.minecraft.fox_spawn_egg": "Fuchs-Spawn-Gaggeli", "item.minecraft.friend_pottery_shard": "<PERSON> <PERSON><PERSON>", "item.minecraft.friend_pottery_sherd": "<PERSON> <PERSON>", "item.minecraft.frog_spawn_egg": "Frog Spawn Egg", "item.minecraft.furnace_minecart": "Lore mid Ofn", "item.minecraft.ghast_spawn_egg": "Ghast-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ghast_tear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "Glitzende Melonscheim", "item.minecraft.globe_banner_pattern": "<PERSON>", "item.minecraft.globe_banner_pattern.desc": "Globus", "item.minecraft.globe_banner_pattern.new": "Globe Banner Pattern", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_ink_sac": "Leuchddindnbeudl", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_squid_spawn_egg": "Leuchddindnfisch-Spawn-Gaggeli", "item.minecraft.glowstone_dust": "Leuchdstaastaab", "item.minecraft.goat_horn": "<PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Ziegn-Spawn-Gaggeli", "item.minecraft.gold_ingot": "Goidboa", "item.minecraft.gold_nugget": "Goidglumbn", "item.minecraft.golden_apple": "Goidena Abfl", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_boots": "Goidschdiefl", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "Goid<PERSON><PERSON>", "item.minecraft.golden_horse_armor": "Goidene Pfeddrüsdung", "item.minecraft.golden_leggings": "Goid<PERSON>ischuz", "item.minecraft.golden_pickaxe": "Goidpickl", "item.minecraft.golden_shovel": "Goidschaufl", "item.minecraft.golden_sword": "Goidschwerd", "item.minecraft.gray_bundle": "<PERSON>", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.gray_harness": "<PERSON>", "item.minecraft.green_bundle": "Green Bundle", "item.minecraft.green_dye": "<PERSON><PERSON>", "item.minecraft.green_harness": "<PERSON>ss", "item.minecraft.guardian_spawn_egg": "Wächda-Spawn-Gaggeli", "item.minecraft.gunpowder": "Schwoazbulva", "item.minecraft.guster_banner_pattern": "<PERSON>", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "<PERSON><PERSON>", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON>y <PERSON>", "item.minecraft.happy_ghast_spawn_egg": "Happy Ghast Spawn Egg", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Hätz vom Meer", "item.minecraft.heart_pottery_shard": "Heart Pottery Shard", "item.minecraft.heart_pottery_sherd": "Heart Pottery Sherd", "item.minecraft.heartbreak_pottery_shard": "Heartbreak Pottery Shard", "item.minecraft.heartbreak_pottery_sherd": "Heartbreak Pottery Sherd", "item.minecraft.hoglin_spawn_egg": "Hoglin-Spawn-G<PERSON><PERSON><PERSON>", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.honeycomb": "Honichwòwe", "item.minecraft.hopper_minecart": "Lore mid Drichda", "item.minecraft.horse_spawn_egg": "Gaul-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.host_armor_trim_smithing_template.new": "Host <PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "Howl Pottery Shard", "item.minecraft.howl_pottery_sherd": "Howl Pottery Sherd", "item.minecraft.husk_spawn_egg": "Wüsdnzombie-Spawn-Gaggeli", "item.minecraft.ink_sac": "Dindnsagg", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_boots": "Aasnschdiefl", "item.minecraft.iron_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "Iron Golem Spawn Egg", "item.minecraft.iron_helmet": "Aasnhelm", "item.minecraft.iron_hoe": "Aasnhaue", "item.minecraft.iron_horse_armor": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_ingot": "Aasnboa", "item.minecraft.iron_leggings": "Aasnboischuz", "item.minecraft.iron_nugget": "Aasnglumbn", "item.minecraft.iron_pickaxe": "Aasnpickl", "item.minecraft.iron_shovel": "Aasnschaufl", "item.minecraft.iron_sword": "Aasnschwerd", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "Dr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.jungle_chest_boat": "Jungle Boat with Chest", "item.minecraft.knowledge_book": "Bouch des Wissns", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lead": "<PERSON><PERSON>", "item.minecraft.leather": "Ledda", "item.minecraft.leather_boots": "Leddaschdiefl", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather_helmet": "Leddakappn", "item.minecraft.leather_horse_armor": "Leddan<PERSON>rü<PERSON>dung", "item.minecraft.leather_leggings": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_blue_bundle": "Light Blue Bundle", "item.minecraft.light_blue_dye": "Hell<PERSON><PERSON>", "item.minecraft.light_blue_harness": "Light Blue Harness", "item.minecraft.light_gray_bundle": "Light Gray Bundle", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_harness": "<PERSON> <PERSON>", "item.minecraft.lime_bundle": "Lime Bundle", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.lime_harness": "<PERSON><PERSON>", "item.minecraft.lingering_potion": "Veaweildrangg", "item.minecraft.lingering_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.empty": "<PERSON> brauch<PERSON>", "item.minecraft.lingering_potion.effect.fire_resistance": "Veaweildrangg dea Feuaresisdenz", "item.minecraft.lingering_potion.effect.harming": "Veaweildrangg des Schadns", "item.minecraft.lingering_potion.effect.healing": "<PERSON>eaweild<PERSON><PERSON> dea <PERSON>", "item.minecraft.lingering_potion.effect.infested": "Lingering Potion of Infestation", "item.minecraft.lingering_potion.effect.invisibility": "Veaweildrangg dea Unsichdbakeid", "item.minecraft.lingering_potion.effect.leaping": "Veaweildrangg dea Sprungkrafd", "item.minecraft.lingering_potion.effect.levitation": "Veaweildrangg dea Schwebekrafd", "item.minecraft.lingering_potion.effect.luck": "Veaweildrangg des Glücks", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.night_vision": "Veaweildrangg dea <PERSON>d", "item.minecraft.lingering_potion.effect.oozing": "Lingering Potion of Oozing", "item.minecraft.lingering_potion.effect.poison": "Veaweildrangg dea Veagifdung", "item.minecraft.lingering_potion.effect.regeneration": "Veaweildrangg dea Regeneration", "item.minecraft.lingering_potion.effect.slow_falling": "Veaweildrangg des sanfdn Fallns", "item.minecraft.lingering_potion.effect.slowness": "Veaweildrangg <PERSON>", "item.minecraft.lingering_potion.effect.strength": "Veaweildrangg dea Stärkn", "item.minecraft.lingering_potion.effect.swiftness": "Veaweildrangg dea <PERSON>", "item.minecraft.lingering_potion.effect.thick": "Dickflüssiga <PERSON>", "item.minecraft.lingering_potion.effect.turtle_master": "Veaweildrangg des Schildkrödnmaistas", "item.minecraft.lingering_potion.effect.water": "Veaweilnde Wassabuddel", "item.minecraft.lingering_potion.effect.water_breathing": "Veaweildrangg dea <PERSON>mu<PERSON>", "item.minecraft.lingering_potion.effect.weakness": "Veaweildrangg dea Schwächn", "item.minecraft.lingering_potion.effect.weaving": "Lingering Potion of Weaving", "item.minecraft.lingering_potion.effect.wind_charged": "Lingering Potion of Wind Charging", "item.minecraft.llama_spawn_egg": "Loma-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lodestone_compass": "Leidstoa-Kombass", "item.minecraft.mace": "Mace", "item.minecraft.magenta_bundle": "Magenta Bundle", "item.minecraft.magenta_dye": "Magen<PERSON> Fab<PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Mochmagreem", "item.minecraft.magma_cube_spawn_egg": "Mochmawüffel-Spawn-Gaggeli", "item.minecraft.mangrove_boat": "Mangrove Boat", "item.minecraft.mangrove_chest_boat": "Mangrove Boat with Chest", "item.minecraft.map": "<PERSON><PERSON>", "item.minecraft.melon_seeds": "Melonsoma", "item.minecraft.melon_slice": "Melons<PERSON><PERSON>", "item.minecraft.milk_bucket": "Milliaima", "item.minecraft.minecart": "Lore", "item.minecraft.miner_pottery_shard": "Miner <PERSON><PERSON>", "item.minecraft.miner_pottery_sherd": "Miner <PERSON>", "item.minecraft.mojang_banner_pattern": "<PERSON>", "item.minecraft.mojang_banner_pattern.desc": "Mojang-Logo", "item.minecraft.mojang_banner_pattern.new": "<PERSON> Banner <PERSON>", "item.minecraft.mooshroom_spawn_egg": "Mooshroom-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mourner_pottery_shard": "Mourner Pottery Shard", "item.minecraft.mourner_pottery_sherd": "Mourner Pottery Sherd", "item.minecraft.mule_spawn_egg": "Mauldier-Spawn-Gaggel<PERSON>", "item.minecraft.mushroom_stew": "Bilzsubbn", "item.minecraft.music_disc_11": "Schallbladdn", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Schallbladdn", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Schallbladdn", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Schallbladdn", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Schallbladdn", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Schallbladdn", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Music Disc", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Music Disc", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "Schallbladdn", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Music Disc", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Schallbladdn", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Schallbladdn", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Schallbladdn", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Schallbladdn", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Music Disc", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Music Disc", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Schallbladdn", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Schallbladdn", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Music Disc", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Schallbladdn", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Schallbladdn", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON>", "item.minecraft.name_tag": "Noamensschild", "item.minecraft.nautilus_shell": "Naudilusschole", "item.minecraft.nether_brick": "Netherglödds", "item.minecraft.nether_star": "Netherschdean", "item.minecraft.nether_wart": "Nether Wart", "item.minecraft.netherite_axe": "Netheritaxn", "item.minecraft.netherite_boots": "Netheritschdiefl", "item.minecraft.netherite_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_helmet": "Netherithelm", "item.minecraft.netherite_hoe": "Netherithaue", "item.minecraft.netherite_ingot": "Netheritboa", "item.minecraft.netherite_leggings": "Netheritboischuz", "item.minecraft.netherite_pickaxe": "Netheritspitzhackn", "item.minecraft.netherite_scrap": "Netheritbladdn", "item.minecraft.netherite_shovel": "Netheritschaufl", "item.minecraft.netherite_sword": "Netheritschwead", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherite Upgrade", "item.minecraft.oak_boat": "Ä<PERSON><PERSON><PERSON><PERSON>d", "item.minecraft.oak_chest_boat": "Oak Boat with Chest", "item.minecraft.ocelot_spawn_egg": "Ozelodn-Spawn-G<PERSON><PERSON>i", "item.minecraft.ominous_bottle": "Ominous <PERSON>", "item.minecraft.ominous_trial_key": "Ominous Trial Key", "item.minecraft.orange_bundle": "Orange Bundle", "item.minecraft.orange_dye": "Oranga Fabbschdoff", "item.minecraft.orange_harness": "Orange Harness", "item.minecraft.painting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pale_oak_boat": "Pale Oak Boat", "item.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "item.minecraft.panda_spawn_egg": "Poanda-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.paper": "Babier", "item.minecraft.parrot_spawn_egg": "Babagein-Spawn-G<PERSON><PERSON><PERSON>", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.phantom_spawn_egg": "Phandom-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pig_spawn_egg": "Schweins-Spawn-Ei", "item.minecraft.piglin_banner_pattern": "<PERSON>", "item.minecraft.piglin_banner_pattern.desc": "Schnauzn", "item.minecraft.piglin_banner_pattern.new": "Snout Banner Pattern", "item.minecraft.piglin_brute_spawn_egg": "Piglin-Babarn-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_spawn_egg": "Piglin-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pillager_spawn_egg": "Blündera-Spawn-Gaggeli", "item.minecraft.pink_bundle": "Pink Bundle", "item.minecraft.pink_dye": "<PERSON><PERSON>", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "Pitcher Plant", "item.minecraft.pitcher_pod": "Pitcher Pod", "item.minecraft.plenty_pottery_shard": "Plenty Pottery Shard", "item.minecraft.plenty_pottery_sherd": "Plenty Pottery Sherd", "item.minecraft.poisonous_potato": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.polar_bear_spawn_egg": "Eisbeä-Spawn-Gaggeli", "item.minecraft.popped_chorus_fruit": "Geblatzde Chorusfruchd", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON>", "item.minecraft.potato": "Ka<PERSON><PERSON>", "item.minecraft.potion": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.empty": "Net brauchbara Drangg", "item.minecraft.potion.effect.fire_resistance": "<PERSON><PERSON><PERSON> dea <PERSON>", "item.minecraft.potion.effect.harming": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.healing": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.infested": "Potion of Infestation", "item.minecraft.potion.effect.invisibility": "<PERSON><PERSON><PERSON> de<PERSON>", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON><PERSON> dea <PERSON>", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON><PERSON>rafd", "item.minecraft.potion.effect.luck": "<PERSON><PERSON><PERSON> des Glücks", "item.minecraft.potion.effect.mundane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.night_vision": "<PERSON><PERSON><PERSON> de<PERSON>", "item.minecraft.potion.effect.oozing": "Potion of Oozing", "item.minecraft.potion.effect.poison": "<PERSON><PERSON><PERSON> dea <PERSON>", "item.minecraft.potion.effect.regeneration": "Drangg dea Regeneration", "item.minecraft.potion.effect.slow_falling": "Drangg des sanfdn Fallns", "item.minecraft.potion.effect.slowness": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.strength": "<PERSON><PERSON><PERSON> Stä<PERSON>n", "item.minecraft.potion.effect.swiftness": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "item.minecraft.potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.turtle_master": "Drangg des Schildkrödnmaistas", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON><PERSON> dea <PERSON>", "item.minecraft.potion.effect.weaving": "Potion of Weaving", "item.minecraft.potion.effect.wind_charged": "Potion of Wind Charging", "item.minecraft.pottery_shard_archer": "Archer Pottery Shard", "item.minecraft.pottery_shard_arms_up": "Arms Up Pottery Shard", "item.minecraft.pottery_shard_prize": "Prize <PERSON><PERSON> Shard", "item.minecraft.pottery_shard_skull": "Skull Pottery Shard", "item.minecraft.powder_snow_bucket": "Bulversch<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_crystals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prismarine_shard": "Brismarinscherbm", "item.minecraft.prize_pottery_shard": "Prize <PERSON><PERSON> Shard", "item.minecraft.prize_pottery_sherd": "Prize <PERSON><PERSON> Sherd", "item.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pufferfish_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pufferfish_spawn_egg": "Kuglfisch-Spawn-Gaggeli", "item.minecraft.pumpkin_pie": "Kärwiskuchn", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.purple_bundle": "Purple Bundle", "item.minecraft.purple_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "Netherkwarz", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_foot": "Kanigglpfode", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "Karnickel-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit_stew": "<PERSON><PERSON>gglragout", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.raiser_armor_trim_smithing_template.new": "Raiser Armor Trim", "item.minecraft.ravager_spawn_egg": "Vawüsda-Spawn-Gaggeli", "item.minecraft.raw_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_gold": "<PERSON><PERSON><PERSON>", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "Recovery Compass", "item.minecraft.red_bundle": "Red Bundle", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON>", "item.minecraft.redstone": "Redstone-Staab", "item.minecraft.resin_brick": "Resin Brick", "item.minecraft.resin_clump": "<PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.rotten_flesh": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.saddle": "Saddl", "item.minecraft.salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.salmon_bucket": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.salmon_spawn_egg": "Laggs-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.scrape_pottery_sherd": "Scrape Pottery Sherd", "item.minecraft.scute": "<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.sentry_armor_trim_smithing_template.new": "Sentry Armor Trim", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> Armor <PERSON>", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON>", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.shears": "<PERSON><PERSON>", "item.minecraft.sheep_spawn_egg": "Hämml-Spawn-Gaggeli", "item.minecraft.shelter_pottery_shard": "Shelter Pottery Shard", "item.minecraft.shelter_pottery_sherd": "<PERSON>lter Pottery Sherd", "item.minecraft.shield": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "Schwoaz<PERSON> Schild", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.green": "<PERSON><PERSON>", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.orange": "Oranga Schild", "item.minecraft.shield.pink": "<PERSON><PERSON> Schild", "item.minecraft.shield.purple": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON>", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "Shulker-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sign": "Sign", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.silence_armor_trim_smithing_template.new": "Silence Armor Trim", "item.minecraft.silverfish_spawn_egg": "Silbefisch-Spawn-Gaggeli", "item.minecraft.skeleton_horse_spawn_egg": "Sgeleddgaul-Spawn-Gaggeli", "item.minecraft.skeleton_spawn_egg": "Sgeledd-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern": "<PERSON>", "item.minecraft.skull_banner_pattern.desc": "Schedl", "item.minecraft.skull_banner_pattern.new": "Skull Charge Banner Pattern", "item.minecraft.skull_pottery_shard": "Skull Pottery Shard", "item.minecraft.skull_pottery_sherd": "Skull Pottery Sherd", "item.minecraft.slime_ball": "Schloimbool", "item.minecraft.slime_spawn_egg": "Schloim-Spawn-Gaggeli", "item.minecraft.smithing_template": "<PERSON><PERSON> Template", "item.minecraft.smithing_template.applies_to": "Applies to:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Add ingot or crystal", "item.minecraft.smithing_template.armor_trim.applies_to": "Armor", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Add a piece of armor", "item.minecraft.smithing_template.armor_trim.ingredients": "Ingots & Crystals", "item.minecraft.smithing_template.ingredients": "Ingredients:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Add Netherite Ingot", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamond Equipment", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Add diamond armor, weapon, or tool", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netherite Ingot", "item.minecraft.smithing_template.upgrade": "Upgrade: ", "item.minecraft.sniffer_spawn_egg": "Sniffer Spawn Egg", "item.minecraft.snort_pottery_shard": "Snort Pottery Shard", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON> Pottery Sherd", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.snout_armor_trim_smithing_template.new": "Snout Armor Trim", "item.minecraft.snow_golem_spawn_egg": "Snow Golem Spawn Egg", "item.minecraft.snowball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spectral_arrow": "Spekdralpfail", "item.minecraft.spider_eye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spider_spawn_egg": "Schbinn-Spawn-Gaggeli", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.spire_armor_trim_smithing_template.new": "Spire Arm<PERSON>", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.empty": "Net brauchbara Drangg", "item.minecraft.splash_potion.effect.fire_resistance": "Wu<PERSON><PERSON>ng<PERSON> dea Feua<PERSON>denz", "item.minecraft.splash_potion.effect.harming": "Wuafdrang<PERSON> des Schadns", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.infested": "Splash Potion of Infestation", "item.minecraft.splash_potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON> de<PERSON>", "item.minecraft.splash_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON><PERSON> dea <PERSON>", "item.minecraft.splash_potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON><PERSON>a <PERSON>rafd", "item.minecraft.splash_potion.effect.luck": "Wuafdrangg des Glücks", "item.minecraft.splash_potion.effect.mundane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON> dea <PERSON>", "item.minecraft.splash_potion.effect.oozing": "Splash Potion of Oozing", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON> dea <PERSON>", "item.minecraft.splash_potion.effect.regeneration": "Wuafdrangg dea Regeneration", "item.minecraft.splash_potion.effect.slow_falling": "Wuafdrangg des sanfdn Fallns", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.strength": "Wuafdrang<PERSON> dea Stärkn", "item.minecraft.splash_potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON> dea <PERSON>", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "Wuafdrangg des Schildkrödnmaistas", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON> dea <PERSON>", "item.minecraft.splash_potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON> dea <PERSON>", "item.minecraft.splash_potion.effect.weaving": "Splash Potion of Weaving", "item.minecraft.splash_potion.effect.wind_charged": "Splash Potion of Wind Charging", "item.minecraft.spruce_boat": "Fichdnhulzbood", "item.minecraft.spruce_chest_boat": "Spruce Boat with Chest", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "Dindnfisch-Spawn-Gaggeli", "item.minecraft.stick": "Schdegg<PERSON>", "item.minecraft.stone_axe": "Sc<PERSON><PERSON>iba<PERSON>", "item.minecraft.stone_hoe": "Schdoihaue", "item.minecraft.stone_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_shovel": "Sc<PERSON><PERSON><PERSON>au<PERSON>l", "item.minecraft.stone_sword": "Staanschwerd", "item.minecraft.stray_spawn_egg": "Aiswandra-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.strider_spawn_egg": "Schreida-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.string": "<PERSON><PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "Seldsame Suppn", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tadpole_bucket": "Bucket of Tadpole", "item.minecraft.tadpole_spawn_egg": "Tadpole Spawn Egg", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.tide_armor_trim_smithing_template.new": "Tide Armor Trim", "item.minecraft.tipped_arrow": "Gdränkda Pfail", "item.minecraft.tipped_arrow.effect.awkward": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.empty": "<PERSON> gdränkda Pfail", "item.minecraft.tipped_arrow.effect.fire_resistance": "P<PERSON>il <PERSON>a Feua<PERSON>denz", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON>hadns", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.tipped_arrow.effect.infested": "Arrow of Infestation", "item.minecraft.tipped_arrow.effect.invisibility": "<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.tipped_arrow.effect.leaping": "<PERSON><PERSON><PERSON> <PERSON>a <PERSON>krafd", "item.minecraft.tipped_arrow.effect.levitation": "<PERSON><PERSON><PERSON> <PERSON>bekrafd", "item.minecraft.tipped_arrow.effect.luck": "Pfail des Glücks", "item.minecraft.tipped_arrow.effect.mundane": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.night_vision": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.oozing": "Arrow of Oozing", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.regeneration": "Pfail dea Regeneration", "item.minecraft.tipped_arrow.effect.slow_falling": "Pfail des sanfdn Fallns", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.tipped_arrow.effect.strength": "Pfail dea Stärkn", "item.minecraft.tipped_arrow.effect.swiftness": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.thick": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.turtle_master": "Pfail des Schildkrödnmaistas", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.water_breathing": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.weakness": "<PERSON><PERSON><PERSON> <PERSON><PERSON>n", "item.minecraft.tipped_arrow.effect.weaving": "Arrow of Weaving", "item.minecraft.tipped_arrow.effect.wind_charged": "Arrow of Wind Charging", "item.minecraft.tnt_minecart": "Lore mid DND", "item.minecraft.torchflower_seeds": "Torchflower Seeds", "item.minecraft.totem_of_undying": "Dodem der Unschdebblichgaad", "item.minecraft.trader_llama_spawn_egg": "Händlaloma-Spawn-G<PERSON>geli", "item.minecraft.trial_key": "Trial Key", "item.minecraft.trident": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tropical_fish": "Drobnfisch", "item.minecraft.tropical_fish_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tropical_fish_spawn_egg": "Drobnfisch-Spawn-Gaggeli", "item.minecraft.turtle_helmet": "Schildgrödnbanza", "item.minecraft.turtle_scute": "<PERSON>", "item.minecraft.turtle_spawn_egg": "Schildgrödn-Spawn-Gaggeli", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.vex_armor_trim_smithing_template.new": "Vex Armor Trim", "item.minecraft.vex_spawn_egg": "Gschbensd-Spawn-Gaggeli", "item.minecraft.villager_spawn_egg": "Doefbewohne-Spawn-Gaggeli", "item.minecraft.vindicator_spawn_egg": "Diena-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wandering_trader_spawn_egg": "Fahrnda-Händla-Spawn-Gaggeli", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON>", "item.minecraft.warden_spawn_egg": "Warden Spawn Egg", "item.minecraft.warped_fungus_on_a_stick": "Wirrpilzrudn", "item.minecraft.water_bucket": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Wayfinder Armor Trim", "item.minecraft.wheat": "Waaz", "item.minecraft.wheat_seeds": "<PERSON><PERSON>znk<PERSON><PERSON>", "item.minecraft.white_bundle": "White Bundle", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.white_harness": "<PERSON> Harness", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.wild_armor_trim_smithing_template.new": "Wild Armor Trim", "item.minecraft.wind_charge": "Wind Charge", "item.minecraft.witch_spawn_egg": "Affarlaa-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wither_skeleton_spawn_egg": "Withersgeledd-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wither_spawn_egg": "Wither Spawn Egg", "item.minecraft.wolf_armor": "<PERSON>or", "item.minecraft.wolf_spawn_egg": "Wolf-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "Hulzpickl", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>l", "item.minecraft.wooden_sword": "Hulzschwerd", "item.minecraft.writable_book": "Bouch mid Fede", "item.minecraft.written_book": "Beschriebens Bouch", "item.minecraft.yellow_bundle": "Yellow Bundle", "item.minecraft.yellow_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.yellow_harness": "Yellow Harness", "item.minecraft.zoglin_spawn_egg": "Zoglin-Spawn-Gaggeli", "item.minecraft.zombie_horse_spawn_egg": "Zombiegaul-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.zombie_spawn_egg": "Zombie-Spawn-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.zombie_villager_spawn_egg": "Zombiedoefbewohne-Spawn-G<PERSON><PERSON><PERSON>", "item.minecraft.zombified_piglin_spawn_egg": "Zombifizieada-Piglin-Spawn-Gaggeli", "item.modifiers.any": "When equipped:", "item.modifiers.armor": "When worn:", "item.modifiers.body": "When equipped:", "item.modifiers.chest": "Am <PERSON>:", "item.modifiers.feet": "An den Ladschn:", "item.modifiers.hand": "When held:", "item.modifiers.head": "<PERSON><PERSON> <PERSON>:", "item.modifiers.legs": "An den Bein'n:", "item.modifiers.mainhand": "In dea Haubdhand:", "item.modifiers.offhand": "In dea Zwoadhand:", "item.modifiers.saddle": "When saddled:", "item.nbt_tags": "NBD: %s Eignschafd(n)", "item.op_block_warning.line1": "Warning:", "item.op_block_warning.line2": "Use of this item might lead to command execution", "item.op_block_warning.line3": "Do not use unless you know the exact contents!", "item.unbreakable": "Unzeaschdöaba", "itemGroup.buildingBlocks": "<PERSON><PERSON><PERSON><PERSON>", "itemGroup.coloredBlocks": "Colored Blocks", "itemGroup.combat": "<PERSON><PERSON><PERSON>", "itemGroup.consumables": "Consumables", "itemGroup.crafting": "Crafting", "itemGroup.foodAndDrink": "Food & Drinks", "itemGroup.functional": "Functional Blocks", "itemGroup.hotbar": "Gspeichade Schnellzugriffsleisdn", "itemGroup.ingredients": "Ingredients", "itemGroup.inventory": "Invendoa", "itemGroup.natural": "Natural Blocks", "itemGroup.op": "Operator Utilities", "itemGroup.redstone": "Redstone Blocks", "itemGroup.search": "<PERSON>n", "itemGroup.spawnEggs": "Spawn Eggs", "itemGroup.tools": "Tools & Utilities", "item_modifier.unknown": "Unbekannda Gechnschdandsmodifikador: %s", "jigsaw_block.final_state": "Wiad zu:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "Ausgrichded", "jigsaw_block.joint.rollable": "Dr<PERSON><PERSON>", "jigsaw_block.joint_label": "Veabindung:", "jigsaw_block.keep_jigsaws": "Veabünde behoaldn", "jigsaw_block.levels": "Ebenen: %s", "jigsaw_block.name": "Nome:", "jigsaw_block.placement_priority": "Placement Priority:", "jigsaw_block.placement_priority.tooltip": "When this Jigsaw block connects to a piece, this is the order in which that piece is processed for connections in the wider structure.\n\nPieces will be processed in descending priority with insertion order breaking ties.", "jigsaw_block.pool": "Bezugsquell:", "jigsaw_block.selection_priority": "Selection Priority:", "jigsaw_block.selection_priority.tooltip": "When the parent piece is being processed for connections, this is the order in which this Jigsaw block attempts to connect to its target piece.\n\nJigsaws will be processed in descending priority with random ordering breaking ties.", "jigsaw_block.target": "Zielnoamä:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON><PERSON> (Music Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Foadschridde", "key.attack": "Ogreife/Zerschdöan", "key.back": "Rüggwärdds", "key.categories.creative": "Creative Mode", "key.categories.gameplay": "Schbielmechonik", "key.categories.inventory": "Invendoa", "key.categories.misc": "Veaschiednes", "key.categories.movement": "Bewechung", "key.categories.multiplayer": "Multiplayer", "key.categories.ui": "Schbielschdeierung", "key.chat": "<PERSON>t effne", "key.command": "Bfehlszeileoafmachn", "key.drop": "Gechnschdand falln lassn", "key.forward": "Vorwärdds", "key.fullscreen": "Vollbild wechsln", "key.hotbar.1": "Schneizugriff 1", "key.hotbar.2": "Schneizugriff 2", "key.hotbar.3": "Schneizugriff 3", "key.hotbar.4": "Schneizugriff 4", "key.hotbar.5": "Schneizugriff 5", "key.hotbar.6": "Schneizugriff 6", "key.hotbar.7": "Schneizugriff 7", "key.hotbar.8": "Schneizugriff 8", "key.hotbar.9": "Schneizugriff 9", "key.inventory": "Invendoa oaf-/zumachn", "key.jump": "Sc<PERSON>bringa", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.caps.lock": "Fesdschdelldasdn", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "<PERSON><PERSON>", "key.keyboard.end": "<PERSON><PERSON>", "key.keyboard.enter": "Eingabe", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Pos 1", "key.keyboard.insert": "Einfg", "key.keyboard.keypad.0": "Num 0", "key.keyboard.keypad.1": "Num 1", "key.keyboard.keypad.2": "Num 2", "key.keyboard.keypad.3": "Num 3", "key.keyboard.keypad.4": "Num 4", "key.keyboard.keypad.5": "Num 5", "key.keyboard.keypad.6": "Num 6", "key.keyboard.keypad.7": "Num 7", "key.keyboard.keypad.8": "Num 8", "key.keyboard.keypad.9": "Num 9", "key.keyboard.keypad.add": "Num +", "key.keyboard.keypad.decimal": "<PERSON><PERSON> ,", "key.keyboard.keypad.divide": "Num /", "key.keyboard.keypad.enter": "<PERSON><PERSON>", "key.keyboard.keypad.equal": "Num =", "key.keyboard.keypad.multiply": "Num *", "key.keyboard.keypad.subtract": "Num -", "key.keyboard.left": "<PERSON><PERSON>", "key.keyboard.left.alt": "Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "<PERSON>rg lings", "key.keyboard.left.shift": "Umschald lings", "key.keyboard.left.win": "<PERSON>a lings", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Bild ab", "key.keyboard.page.up": "Bild nauf", "key.keyboard.pause": "<PERSON><PERSON>", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON>g", "key.keyboard.right": "Rechds", "key.keyboard.right.alt": "Alt Gr", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Strg rechds", "key.keyboard.right.shift": "Umschald rechds", "key.keyboard.right.win": "<PERSON>a re<PERSON>", "key.keyboard.scroll.lock": "Rolln", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Not Bound", "key.keyboard.up": "<PERSON><PERSON>", "key.keyboard.world.1": "Weld 1", "key.keyboard.world.2": "Weld 2", "key.left": "<PERSON><PERSON>", "key.loadToolbarActivator": "Schneizugriffsleisde ladn", "key.mouse": "Mausdasde %1$s", "key.mouse.left": "<PERSON><PERSON><PERSON><PERSON>", "key.mouse.middle": "Middlgligg", "key.mouse.right": "Rechdsgligg", "key.pickItem": "Blogg auswähln", "key.playerlist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.quickActions": "Quick Actions", "key.right": "Rechds", "key.saveToolbarActivator": "Schneizugriffsleisde schbeichen", "key.screenshot": "Screenshot", "key.smoothCamera": "Gameravahaldn wechsln", "key.sneak": "Sc<PERSON><PERSON><PERSON>n", "key.socialInteractions": "Fensda füa soziale Indaaktionen", "key.spectatorOutlines": "<PERSON><PERSON><PERSON><PERSON> magirn (Zuschaue)", "key.sprint": "<PERSON><PERSON>", "key.swapOffhand": "Gechnschdand mid Zwoadhand dauschn", "key.togglePerspective": "Besbegdive wechsln", "key.use": "Nemme/Blatziern", "known_server_link.announcements": "Announcements", "known_server_link.community": "Community", "known_server_link.community_guidelines": "Community Guidelines", "known_server_link.feedback": "<PERSON><PERSON><PERSON>", "known_server_link.forums": "Forums", "known_server_link.news": "News", "known_server_link.report_bug": "Report Server Bug", "known_server_link.status": "Status", "known_server_link.support": "Support", "known_server_link.website": "Website", "lanServer.otherPlayers": "Einschdellungen füa andere Schbiela", "lanServer.port": "Port Number", "lanServer.port.invalid": "Not a valid port.\nLeave the edit box empty or enter a number between 1024 and 65535.", "lanServer.port.invalid.new": "Not a valid port.\nLeave the edit box empty or enter a number between %s and %s.", "lanServer.port.unavailable": "Port not available.\nLeave the edit box empty or enter a different number between 1024 and 65535.", "lanServer.port.unavailable.new": "Port not available.\nLeave the edit box empty or enter a different number between %s and %s.", "lanServer.scanning": "Such nach Schbielen im logoln Netzwegg", "lanServer.start": "LAN-<PERSON><PERSON> sch<PERSON>", "lanServer.title": "LAN-Weld", "language.code": "vmf_DE", "language.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "language.region": "Franggn", "lectern.take_book": "<PERSON>uch nähmen", "loading.progress": "%s%%", "mco.account.privacy.info": "<PERSON>afah mea üba Mojang und Dadnschutzgsetze", "mco.account.privacy.info.button": "Read more about GDPR", "mco.account.privacy.information": "Mojang implements certain procedures to help protect children and their privacy including complying with the Children's Online Privacy Protection Act (COPPA) and General Data Protection Regulation (GDPR).\n\nYou may need to obtain parental consent before accessing your Realms account.", "mco.account.privacyinfo": "Mojang wended bstimmde Veafahn an, um Kinda und deren Privatsphäre zu schützn. So werdn unda andrem das \"Gesetz zum Schutz der Privatsphäre von Kindern im Internet\" (COPPA) und die Dadnschutz-Grundveaoadnung (DSGVO) einghaldn.\n\nMöglichaweise bnödidgsde die Zustimmung deina <PERSON>, um oaf dein Realm-Kondo zuzugreifn.\n\nWennsde a äldares Minecraft-Kondo hasd (du meldesd dich mid deinem Bnutzanoamn an), mussd de dieses in a Mojang-Kondo umwandln, um oaf Realms zugreifn zu könna.", "mco.account.update": "Kondo akdualisiean", "mco.activity.noactivity": "No activity for the past %s day(s)", "mco.activity.title": "Player activity", "mco.backup.button.download": "Aktuellsde rundaladn", "mco.backup.button.reset": "Reset World", "mco.backup.button.restore": "Wiedaheastelln", "mco.backup.button.upload": "Upload World", "mco.backup.changes.tooltip": "Ändarungn", "mco.backup.entry": "Backup (%s)", "mco.backup.entry.description": "Description", "mco.backup.entry.enabledPack": "Enabled Pack(s)", "mco.backup.entry.gameDifficulty": "Game Difficulty", "mco.backup.entry.gameMode": "Game Mode", "mco.backup.entry.gameServerVersion": "Game Server Version", "mco.backup.entry.name": "Name", "mco.backup.entry.seed": "Seed", "mco.backup.entry.templateName": "Template Name", "mco.backup.entry.undefined": "Undefined Change", "mco.backup.entry.uploaded": "Uploaded", "mco.backup.entry.worldType": "World Type", "mco.backup.generate.world": "<PERSON>ld generiern", "mco.backup.info.title": "Changes From Last Backup", "mco.backup.narration": "Backup from %s", "mco.backup.nobackups": "Dieses Realm had deazeid kene <PERSON>.", "mco.backup.restoring": "Dei Realm wiedaheastelln", "mco.backup.unknown": "UNKNOWN", "mco.brokenworld.download": "<PERSON><PERSON>ad<PERSON>", "mco.brokenworld.downloaded": "Rundagladn", "mco.brokenworld.message.line1": "<PERSON><PERSON>de setz die Weld zurügg oda wähl a andre aus.", "mco.brokenworld.message.line2": "Du koannsd die Weld auch füan Aanzlschbielamoddus rundaladn.", "mco.brokenworld.minigame.title": "Dieses Minischbiel wiad ned mea undastützd", "mco.brokenworld.nonowner.error": "Bidde ward oaf das Zurüggsetzn dea Weld duachn Realm-Bsitza", "mco.brokenworld.nonowner.title": "<PERSON><PERSON> is veaoalded", "mco.brokenworld.play": "Play", "mco.brokenworld.reset": "Reset", "mco.brokenworld.title": "Deine aktuelle Weld wiad ned mea undastützd", "mco.client.incompatible.msg.line1": "<PERSON><PERSON> is ned mid Realm kombadiebl.", "mco.client.incompatible.msg.line2": "Bidde nudz die neusde Veasion von Minecraft.", "mco.client.incompatible.msg.line3": "Realms is ned mid Endwigglungsveasionen kombadiebl.", "mco.client.incompatible.title": "Inkombadiebla Cliend!", "mco.client.outdated.stable.version": "Your client version (%s) is not compatible with Realms.\n\nPlease use the most recent version of Minecraft.", "mco.client.unsupported.snapshot.version": "Your client version (%s) is not compatible with Realms.\n\nRealms is not available for this snapshot version.", "mco.compatibility.downgrade": "Downgrade", "mco.compatibility.downgrade.description": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work.\n\nA backup of your world will be saved under \"World Backups\". Please restore your world if needed.", "mco.compatibility.incompatible.popup.title": "Incompatible version", "mco.compatibility.incompatible.releaseType.popup.message": "The world you are trying to join is incompatible with the version you are on.", "mco.compatibility.incompatible.series.popup.message": "This world was last played in version %s; you are on version %s.\n\nThese series are not compatible with each other. A new world is needed to play on this version.", "mco.compatibility.unverifiable.message": "The version this world was last played in could not be verified. If the world gets upgraded or downgraded, a backup will be automatically created and saved under \"World Backups\".", "mco.compatibility.unverifiable.title": "Compatibility not verifiable", "mco.compatibility.upgrade": "Upgrade", "mco.compatibility.upgrade.description": "This world was last played in version %s; you are on version %s.\n\nA backup of your world will be saved under \"World Backups\".\n\nPlease restore your world if needed.", "mco.compatibility.upgrade.friend.description": "This world was last played in version %s; you are on version %s.\n\nA backup of the world will be saved under \"World Backups\".\n\nThe owner of the Realm can restore the world if needed.", "mco.compatibility.upgrade.title": "Do you really want to upgrade this world?", "mco.configure.current.minigame": "Current", "mco.configure.world.activityfeed.disabled": "Schbielaakdividädn voarübagehend ausgschaldn", "mco.configure.world.backup": "Weld Backups", "mco.configure.world.buttons.activity": "Schbielaagdividäd", "mco.configure.world.buttons.close": "Temporarily Close Realm", "mco.configure.world.buttons.delete": "Delete", "mco.configure.world.buttons.done": "Done", "mco.configure.world.buttons.edit": "Settings", "mco.configure.world.buttons.invite": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.moreoptions": "Mer Optione", "mco.configure.world.buttons.newworld": "New World", "mco.configure.world.buttons.open": "Reopen Realm", "mco.configure.world.buttons.options": "Weld Optione", "mco.configure.world.buttons.players": "Players", "mco.configure.world.buttons.region_preference": "Select Region...", "mco.configure.world.buttons.resetworld": "<PERSON>ld zurüggsetzn", "mco.configure.world.buttons.save": "Save", "mco.configure.world.buttons.settings": "Settings", "mco.configure.world.buttons.subscription": "Abonnemend", "mco.configure.world.buttons.switchminigame": "Minispiel wechsln", "mco.configure.world.close.question.line1": "<PERSON>in <PERSON> wiad ned mea veafügba sein.", "mco.configure.world.close.question.line2": "Are you sure you want to continue?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "Realm wird gschlossn...", "mco.configure.world.commandBlocks": "Befehl-Blouk", "mco.configure.world.delete.button": "Realm löschn", "mco.configure.world.delete.question.line1": "Dei Realm wiad füa imma glöschd", "mco.configure.world.delete.question.line2": "Are you sure you want to continue?", "mco.configure.world.description": "Realm bschreibuang", "mco.configure.world.edit.slot.name": "Weldnoam", "mco.configure.world.edit.subscreen.adventuremap": "Einiche Einschdellungn san ned ve<PERSON>, da dei Weld a Abndeua is", "mco.configure.world.edit.subscreen.experience": "<PERSON><PERSON> Einschdellungn san ned ve<PERSON><PERSON>, da dei Weld a Eafahrungsweld is", "mco.configure.world.edit.subscreen.inspiration": "Einiche Einschdellungn san ned ve<PERSON>, da dei Weld a Insbirationsweld is", "mco.configure.world.forceGameMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> er<PERSON>a", "mco.configure.world.invite.narration": "You have %s new invite(s)", "mco.configure.world.invite.profile.name": "Name", "mco.configure.world.invited": "E<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "Invited (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON>", "mco.configure.world.invites.ops.tooltip": "Operador", "mco.configure.world.invites.remove.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.leave.question.line1": "<PERSON><PERSON> de dieses Realm vealässd, kannsd des ned mea bedredn, bisd de eaneud e<PERSON>ladn wiasd", "mco.configure.world.leave.question.line2": "Are you sure you want to continue?", "mco.configure.world.loading": "Loading Realm", "mco.configure.world.location": "<PERSON><PERSON>", "mco.configure.world.minigame": "Current: %s", "mco.configure.world.name": "Realm Name", "mco.configure.world.opening": "Realm wird g’offnat...", "mco.configure.world.players.error": "'s existiad koa Schbuia mid dem Naman", "mco.configure.world.players.inviting": "Inviting player...", "mco.configure.world.players.title": "Players", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "Deine Weld wiad eazeuchd und deine akduelle Weld gehd vealoan", "mco.configure.world.reset.question.line2": "Are you sure you want to continue?", "mco.configure.world.resourcepack.question": "You need a custom resource pack to play on this Realm\n\nDo you want to download it and play?", "mco.configure.world.resourcepack.question.line1": "Diese Weld eafoadad a bnutzadefinieades Ressourcnbaked", "mco.configure.world.resourcepack.question.line2": "Mogsd es rundaladn und schbieln?", "mco.configure.world.restore.download.question.line1": "Die Weld wiad rundagladn und zu deinen Einzlschbielaweldn hinzugfügd.", "mco.configure.world.restore.download.question.line2": "<PERSON><PERSON> foad<PERSON>hn?", "mco.configure.world.restore.question.line1": "<PERSON>ine Weld wiad aufn Stand vom '%s'(%s) zurügggsetzd", "mco.configure.world.restore.question.line2": "Are you sure you want to continue?", "mco.configure.world.settings.expired": "You cannot edit settings of an expired Realm", "mco.configure.world.settings.title": "Eistellunge", "mco.configure.world.slot": "Weld %s", "mco.configure.world.slot.empty": "<PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "<PERSON>in <PERSON> wiad oaf ne andre Weld gwechseld", "mco.configure.world.slot.switch.question.line2": "Are you sure you want to continue?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON> <PERSON> wechsln", "mco.configure.world.slot.tooltip.active": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot.tooltip.minigame": "Zu Minischbiel wechsln", "mco.configure.world.spawnAnimals": "<PERSON><PERSON>", "mco.configure.world.spawnMonsters": "<PERSON><PERSON>", "mco.configure.world.spawnNPCs": "Generier NPCs", "mco.configure.world.spawnProtection": "Spawnpungd-<PERSON><PERSON><PERSON>", "mco.configure.world.spawn_toggle.message": "Turning this option off will remove all existing entities of that type", "mco.configure.world.spawn_toggle.message.npc": "Turning this option off will remove all existing entities of that type, like Villagers", "mco.configure.world.spawn_toggle.title": "Warning!", "mco.configure.world.status": "Stadus", "mco.configure.world.subscription.day": "<PERSON><PERSON>", "mco.configure.world.subscription.days": "<PERSON><PERSON>", "mco.configure.world.subscription.expired": "Oagalaffa", "mco.configure.world.subscription.extend": "Abonnemend verlängern", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON> als a <PERSON>g", "mco.configure.world.subscription.month": "<PERSON><PERSON>", "mco.configure.world.subscription.months": "Monade", "mco.configure.world.subscription.recurring.daysleft": "<PERSON><PERSON><PERSON>t si von selbst in", "mco.configure.world.subscription.recurring.info": "Changes made to your Realms subscription such as stacking time or turning off recurring billing will not be reflected until your next bill date.", "mco.configure.world.subscription.remaining.days": "%1$s day(s)", "mco.configure.world.subscription.remaining.months": "%1$s month(s)", "mco.configure.world.subscription.remaining.months.days": "%1$s month(s), %2$s day(s)", "mco.configure.world.subscription.start": "Startdadum", "mco.configure.world.subscription.tab": "Subscription", "mco.configure.world.subscription.timeleft": "Zeit übrich", "mco.configure.world.subscription.title": "<PERSON><PERSON>", "mco.configure.world.subscription.unknown": "Unbegannd", "mco.configure.world.switch.slot": "<PERSON><PERSON> er<PERSON>", "mco.configure.world.switch.slot.subtitle": "<PERSON>se Weld is leea, bidde wähl, was zu dun is", "mco.configure.world.title": "Realm eistelle:", "mco.configure.world.uninvite.player": "Are you sure that you want to uninvite '%s'?", "mco.configure.world.uninvite.question": "<PERSON><PERSON> du secher, dass du die Einladung obrechn willsd", "mco.configure.worlds.title": "Weldn", "mco.connect.authorizing": "Logging in...", "mco.connect.connecting": "Vebinde zum Realm ...", "mco.connect.failed": "Vebindungsuffbau zum Realm fehlgschlochn", "mco.connect.region": "Server region: %s", "mco.connect.success": "Done", "mco.create.world": "<PERSON>rschdell<PERSON>", "mco.create.world.error": "Du musst en Name eingebn!", "mco.create.world.failed": "Failed to create world!", "mco.create.world.reset.title": "Weld erschdelle...", "mco.create.world.skip": "Auslossn", "mco.create.world.subtitle": "<PERSON><PERSON><PERSON>, suach dir ei Weld raus, die in dein Realm nei soll", "mco.create.world.wait": "Realm wird erschdelld...", "mco.download.cancelled": "Rundaladn abgbrochn", "mco.download.confirmation.line1": "<PERSON> Weld, die de rundaladn willsd, is größa als %s", "mco.download.confirmation.line2": "Du wiasd diese Weld oaf dein Realm ned wieda hochladn könn’n", "mco.download.confirmation.oversized": "The world you are going to download is larger than %s\n\nYou won't be able to upload this world to your Realm again", "mco.download.done": "<PERSON><PERSON><PERSON><PERSON> feaddich", "mco.download.downloading": "<PERSON><PERSON>ad<PERSON>", "mco.download.extracting": "Endpaggn", "mco.download.failed": "Rundaladn gscheidad", "mco.download.percent": "%s %%", "mco.download.preparing": "Rundaladn voabreidn", "mco.download.resourcePack.fail": "Failed to download resource pack!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Neusde Weld rundaladn", "mco.error.invalid.session.message": "<PERSON><PERSON><PERSON> ve<PERSON>, Mine<PERSON> neu zu stadn", "mco.error.invalid.session.title": "Ungüldiche Sitzungs-ID", "mco.errorMessage.6001": "Veaoaldeda <PERSON>", "mco.errorMessage.6002": "Nutzungsbedingungn ned akzebdiead", "mco.errorMessage.6003": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.errorMessage.6004": "<PERSON><PERSON><PERSON><PERSON><PERSON> ear<PERSON>d", "mco.errorMessage.6005": "World locked", "mco.errorMessage.6006": "World is out of date", "mco.errorMessage.6007": "User in too many Realms", "mco.errorMessage.6008": "Invalid Realm name", "mco.errorMessage.6009": "Invalid Realm description", "mco.errorMessage.connectionFailure": "<PERSON><PERSON> is a <PERSON>hla aufgedredn, bidde veasuch es schbäda nochamal.", "mco.errorMessage.generic": "An error occurred: ", "mco.errorMessage.initialize.failed": "Failed to initialize Realm", "mco.errorMessage.noDetails": "No error details provided", "mco.errorMessage.realmsService": "An error occurred (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Could not connect to Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Could not check compatible version, got response: %s", "mco.errorMessage.retry": "Retry operation", "mco.errorMessage.serviceBusy": "Realms is grad sehr belaschted\nBidde versuch, di in a paar Minuti widder mit Realms zu verbinden.", "mco.gui.button": "<PERSON><PERSON><PERSON>", "mco.gui.ok": "Ok", "mco.info": "Info!", "mco.invited.player.narration": "Invited player %s", "mco.invites.button.accept": "<PERSON><PERSON><PERSON>", "mco.invites.button.reject": "<PERSON><PERSON><PERSON><PERSON>", "mco.invites.nopending": "<PERSON>ane ausstehnden Einladungn!", "mco.invites.pending": "New invite(s)!", "mco.invites.title": "Ausstehnde Einladungn", "mco.minigame.world.changeButton": "<PERSON><PERSON> auswähln", "mco.minigame.world.info.line1": "Dies easetzd deine Weld voarübagehnd mid nem Minischbiel!", "mco.minigame.world.info.line2": "Du koannsd späda ohne Vealusde zu deina uasprünglichn Weld <PERSON>.", "mco.minigame.world.noSelection": "Bidde driff a Auswahl", "mco.minigame.world.restore": "Beende Minischbiel ...", "mco.minigame.world.restore.question.line1": "Das Minischbiel wiad beended und dein Realm wiad wiedaheagstelld.", "mco.minigame.world.restore.question.line2": "<PERSON><PERSON><PERSON> sicha, dass de foad<PERSON>hn willsd?", "mco.minigame.world.selected": "Ausgwähldes Minischbiel:", "mco.minigame.world.slot.screen.title": "Weld wechsln ...", "mco.minigame.world.startButton": "Wechsln", "mco.minigame.world.starting.screen.title": "<PERSON><PERSON><PERSON> ...", "mco.minigame.world.stopButton": "Minischbiel b’endn", "mco.minigame.world.switch.new": "A andres Mini<PERSON>biel auswähln?", "mco.minigame.world.switch.title": "Switch Minigame", "mco.minigame.world.title": "Realm zum Minischbiel wechsln", "mco.news": "Realms‐Neuichkeidn", "mco.notification.dismiss": "<PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "Transfer Now", "mco.notification.transferSubscription.message": "Java Realms subscriptions are moving to the Microsoft Store. Do not let your subscription expire!\nTransfer now and get 30 days of Realms for free.\nGo to Profile on minecraft.net to transfer your subscription.", "mco.notification.visitUrl.buttonText.default": "Open Link", "mco.notification.visitUrl.message.default": "Please visit the link below", "mco.onlinePlayers": "Online Players", "mco.play.button.realm.closed": "Realm is closed", "mco.question": "Question", "mco.reset.world.adventure": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.experience": "Erfoarung", "mco.reset.world.generate": "Neue Weld", "mco.reset.world.inspiration": "Inschpiration", "mco.reset.world.resetting.screen.title": "Weld wird zurückgsetzt...", "mco.reset.world.seed": "Seed (Optional)", "mco.reset.world.template": "Weldvoalagn", "mco.reset.world.title": "Reset World", "mco.reset.world.upload": "Weld hochladn", "mco.reset.world.warning": "S wird deine jetzge Weld deines Realms ersetzn", "mco.selectServer.buy": "Kāf en Realm!", "mco.selectServer.close": "<PERSON><PERSON><PERSON><PERSON>ß<PERSON>", "mco.selectServer.closed": "Realm gschlossn", "mco.selectServer.closeserver": "Realm schließa", "mco.selectServer.configure": "Realm ai richtn", "mco.selectServer.configureRealm": "Configure Realm", "mco.selectServer.create": "Realm erschaffen", "mco.selectServer.create.subtitle": "Select what world to put on your new Realm", "mco.selectServer.expired": "Aougeloffens Realm", "mco.selectServer.expiredList": "Dei Abonnement is oagalaffa", "mco.selectServer.expiredRenew": "Verlängern", "mco.selectServer.expiredSubscribe": "Abonniern", "mco.selectServer.expiredTrial": "Dei Testzeit is oagalaffa", "mco.selectServer.expires.day": "Laft in an Tog us", "mco.selectServer.expires.days": "Lefft in %s us", "mco.selectServer.expires.soon": "Laft ball us", "mco.selectServer.leave": "Realm verlaß", "mco.selectServer.loading": "Loading Realms List", "mco.selectServer.mapOnlySupportedForVersion": "Diese Weld wiad in %s ned undaschdützd", "mco.selectServer.minigame": "Minispiel:", "mco.selectServer.minigameName": "Minigame: %s", "mco.selectServer.minigameNotSupportedInVersion": "Des Minischbiel koann ned mid %s gschbield weadn", "mco.selectServer.noRealms": "You don't seem to have a Realm. Add a Realm to play together with your friends.", "mco.selectServer.note": "Notiz:", "mco.selectServer.open": "Realm af machng", "mco.selectServer.openserver": "Open Realm", "mco.selectServer.play": "<PERSON><PERSON><PERSON>", "mco.selectServer.popup": "Realms sin ei sechere, e<PERSON>cher weg, ei Minecraft Weld online mit bis zu zea Freindi zu spiela.   S undrstützt viel Minispieala un einige Weldn! Nua da Bsitzer muas blechn.", "mco.selectServer.purchase": "Add Realm", "mco.selectServer.trial": "Gratis usprobiern!", "mco.selectServer.uninitialized": "Klickn um dei Realm zu stoatn!", "mco.snapshot.createSnapshotPopup.text": "You are about to create a free Snapshot Realm that will be paired with your paid Realms subscription. This new Snapshot Realm will be accessible for as long as the paid subscription is active. Your paid Realm will not be affected.", "mco.snapshot.createSnapshotPopup.title": "Create Snapshot Realm?", "mco.snapshot.creating": "Creating Snapshot Realm...", "mco.snapshot.description": "Paired with \"%s\"", "mco.snapshot.friendsRealm.downgrade": "You need to be on version %s to join this Realm", "mco.snapshot.friendsRealm.upgrade": "%s needs to upgrade their Realm before you can play from this version", "mco.snapshot.paired": "This Snapshot Realm is paired with \"%s\"", "mco.snapshot.parent.tooltip": "Use the latest release of Minecraft to play on this Realm", "mco.snapshot.start": "Start free Snapshot Realm", "mco.snapshot.subscription.info": "This is a Snapshot Realm that is paired to the subscription of your Realm '%s'. It will stay active for as long as its paired Realm is.", "mco.snapshot.tooltip": "Use Snapshot Realms to get a sneak peek at upcoming versions of Minecraft, which might include new features and other changes.\n\nYou can find your normal Realms in the release version of the game.", "mco.snapshotRealmsPopup.message": "Realms are now available in Snapshots starting with Snapshot 23w41a. Every Realms subscription comes with a free Snapshot Realm that is separate from your normal Java Realm!", "mco.snapshotRealmsPopup.title": "Realms now available in Snapshots", "mco.snapshotRealmsPopup.urlText": "Learn More", "mco.template.button.publisher": "Hearausgeba", "mco.template.button.select": "Auswähln", "mco.template.button.trailer": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.default.name": "Weldvoalagn", "mco.template.info.tooltip": "Website des Hearausgebas", "mco.template.name": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.select.failure": "Die Lisdn mid den Inhaldn diesa Kategorie konndn ned abgrufn weadn.\nBidde übaprüf dei Indanedveabindung oda veasuch es späda nochamal.", "mco.template.select.narrate.authors": "Audor: %s", "mco.template.select.narrate.version": "Veasion %s", "mco.template.select.none": "<PERSON><PERSON><PERSON>, diese Inhaldskategorie scheind leea zu sein.\n<PERSON><PERSON><PERSON> schau demn<PERSON><PERSON>d nocha<PERSON> voa<PERSON>, o<PERSON> <PERSON>, \nwenns de a <PERSON>ella bisd, %s.", "mco.template.select.none.linkTitle": "selbsd an Inhald ein<PERSON>ichn", "mco.template.title": "World templates", "mco.template.title.minigame": "Minischbiele", "mco.template.trailer.tooltip": "Weldvoaschau", "mco.terms.buttons.agree": "<PERSON><PERSON><PERSON><PERSON>", "mco.terms.buttons.disagree": "<PERSON><PERSON> zu<PERSON>", "mco.terms.sentence.1": "I stimma den Minecraft Realms zu", "mco.terms.sentence.2": "Nutzungsbedingunge", "mco.terms.title": "Realms Nutzungsbedingunge", "mco.time.daysAgo": "%1$s day(s) ago", "mco.time.hoursAgo": "%1$s hour(s) ago", "mco.time.minutesAgo": "%1$s minute(s) ago", "mco.time.now": "right now", "mco.time.secondsAgo": "%1$s second(s) ago", "mco.trial.message.line1": "Moagsd a eichenes Realm?", "mco.trial.message.line2": "<PERSON><PERSON>gge hia füa mea Infoamationen!", "mco.upload.button.name": "Hochladn", "mco.upload.cancelled": "Hochladn abgbrochn", "mco.upload.close.failure": "Konnde dei Realm ned schließn, bidde veasuch es schbäda nochamal", "mco.upload.done": "<PERSON><PERSON><PERSON><PERSON> feaddich", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Hochladn fehlgschlochn! (%s)", "mco.upload.failed.too_big.description": "The selected world is too big. The maximum allowed size is %s.", "mco.upload.failed.too_big.title": "World too big", "mco.upload.hardcore": "Weldn im Hardcore-Modus könna ned hochgladn weadn!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Breide deine Weld voa", "mco.upload.select.world.none": "<PERSON><PERSON>chbielaweld gfundn!", "mco.upload.select.world.subtitle": "Bidde wähl a Aanzlschbielaweld zum Hochladn", "mco.upload.select.world.title": "Upload World", "mco.upload.size.failure.line1": "'%s' is zu groß!", "mco.upload.size.failure.line2": "Es is %s. Die maximal zulässiche Größn is %s.", "mco.upload.uploading": "Lad '%s' hoch", "mco.upload.verifying": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.version": "Version: %s", "mco.warning": "Warning!", "mco.worldSlot.minigame": "Minigame", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "Vebindung drenne", "menu.feedback": "Feedback...", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "Schpielmenü", "menu.modded": " (modifiziad)", "menu.multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "Obzione ...", "menu.paused": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>t", "menu.playdemo": "Demo-<PERSON><PERSON> schbiele", "menu.playerReporting": "Player Reporting", "menu.preparingSpawn": "Einschdiegsbereich wird vorbereided: %s %%", "menu.quick_actions": "Quick Actions...", "menu.quick_actions.title": "Quick Actions", "menu.quit": "Schbiel verlasse", "menu.reportBugs": "<PERSON><PERSON> meldn", "menu.resetdemo": "Demo-Weld zurüggsetzn", "menu.returnToGame": "Zurügg zum Schbiel", "menu.returnToMenu": "Schbeichen und zurügg zum Haubmenü", "menu.savingChunks": "<PERSON>s werden g<PERSON>", "menu.savingLevel": "Weld wird gschbeiched", "menu.sendFeedback": "Rüggmeldung gebn", "menu.server_links": "Server Links...", "menu.server_links.title": "Server Links", "menu.shareToLan": "Im <PERSON> e<PERSON>", "menu.singleplayer": "Aanzlschbiela", "menu.working": "Wird bearbeided ...", "merchant.deprecated": "Doafbwohna fülln ihrn Warnbstand bis zu zwoamal am Tach auf.", "merchant.level.1": "<PERSON><PERSON><PERSON>", "merchant.level.2": "Lehrbua", "merchant.level.3": "G<PERSON>l", "merchant.level.4": "<PERSON><PERSON><PERSON>", "merchant.level.5": "Meester", "merchant.title": "%s - %s", "merchant.trades": "Handln", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Drück %1$s, um abzusteign", "multiplayer.applyingPack": "Ressourcnbageed wiad ongwnandt", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "De Audendifizierungsseava sind grad nid erreichbo. Bidde versuchs schbäde nochamol!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "Du wuadst oaf diesm Seava g<PERSON>", "multiplayer.disconnect.banned.expiration": "\nDei <PERSON>re wird am %soafghobe", "multiplayer.disconnect.banned.reason": "Du wuadst oaf diesm Seava gschberrd.\nGrund: %s", "multiplayer.disconnect.banned_ip.expiration": "\nYour ban will be removed on %s", "multiplayer.disconnect.banned_ip.reason": "Dei IP-Adress wuad oaf diesm Seava gschberrd.\nGrund: %s", "multiplayer.disconnect.chat_validation_failed": "Chat message validation failure", "multiplayer.disconnect.duplicate_login": "Du hasd dich von am andern Cliend angemelded", "multiplayer.disconnect.expired_public_key": "Expired profile public key. Check that your system time is synchronized, and try restarting your game.", "multiplayer.disconnect.flying": "Fliechn is oaf diesm Seava nid erlaubd", "multiplayer.disconnect.generic": "Vebindung gedrennd", "multiplayer.disconnect.idling": "Du wast zu lang inaggdiv!", "multiplayer.disconnect.illegal_characters": "Unzulässiche Zeichn im Chat", "multiplayer.disconnect.incompatible": "Inkombadiebla Cliend! Bidde bnutz %s", "multiplayer.disconnect.invalid_entity_attacked": "<PERSON><PERSON> wuad vesuchd, en ungüldiches Objeggd ozugreifn", "multiplayer.disconnect.invalid_packet": "<PERSON><PERSON> had a ungüldig<PERSON> <PERSON><PERSON>d gsended", "multiplayer.disconnect.invalid_player_data": "Ungüldige Schbieladadn", "multiplayer.disconnect.invalid_player_movement": "Ungüldiches Bageed zur Schbielabewechung embfange", "multiplayer.disconnect.invalid_public_key_signature": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_public_key_signature.new": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_vehicle_movement": "Ungüldiches Bageed zur Fazzeuchbewechung embfange", "multiplayer.disconnect.ip_banned": "Dei IP-Adressn wuad oaf diesm Seava gsch<PERSON>rd", "multiplayer.disconnect.kicked": "En Operator hod dich vom Seava gworffn", "multiplayer.disconnect.missing_tags": "Unvollständiga Etikettnsatz vom Seava empfangn. Bidde benachrichdige den Seavabedreiba.", "multiplayer.disconnect.name_taken": "<PERSON> Nome is scho vergem", "multiplayer.disconnect.not_whitelisted": "Du bisd nid oaf da Gäsdelisdn vom Seava!", "multiplayer.disconnect.out_of_order_chat": "Out-of-order chat packet received. Did your system time change?", "multiplayer.disconnect.outdated_client": "Incompatible client! Please use %s", "multiplayer.disconnect.outdated_server": "Incompatible client! Please use %s", "multiplayer.disconnect.server_full": "<PERSON><PERSON> is voll!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON>", "multiplayer.disconnect.slow_login": "Der Anmeldevorgang had zu lang daued", "multiplayer.disconnect.too_many_pending_chats": "Too many unacknowledged chat messages", "multiplayer.disconnect.transfers_disabled": "Server does not accept transfers", "multiplayer.disconnect.unexpected_query_response": "Unerwaddede benutzedefinierde Doudn vom Cliend erhaldn", "multiplayer.disconnect.unsigned_chat": "Received chat packet with missing or invalid signature.", "multiplayer.disconnect.unverified_username": "Benutzernome konnde nid übebrüfd wern!", "multiplayer.downloadingStats": "Statistikn wern obgrufa...", "multiplayer.downloadingTerrain": "Landschaffd werd gelodn ...", "multiplayer.lan.server_found": "New server found: %s", "multiplayer.message_not_delivered": "Chatnachricht konnte ned zuagestäit wern, bitte Protokoidatein des Servas prüfa: %s", "multiplayer.player.joined": "%s hads <PERSON><PERSON><PERSON><PERSON> bedredn", "multiplayer.player.joined.renamed": "%s (frühe begannd als %s) hads Schbiel bedredn", "multiplayer.player.left": "%s hads Schbiel verlassn", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Online players: %s", "multiplayer.requiredTexturePrompt.disconnect": "Serva eafoaderd a bnutzadefinieades Ressourcnbaged", "multiplayer.requiredTexturePrompt.line1": "Der Serva empfiehld a Verwendung von nem benutzadefinieadn Ressourcnbaged.", "multiplayer.requiredTexturePrompt.line2": "Wenn du des bnutzadefinieade Ressourcnbaged ablehnst, wirsde vom Serva gdrennd.", "multiplayer.socialInteractions.not_available": "Soziale Intaaktionen san nua in Measchbielaweldn vorhandn", "multiplayer.status.and_more": "... und %s mea ...", "multiplayer.status.cancelled": "Ogebrochn", "multiplayer.status.cannot_connect": "De Vebindung zum Seava kann nid hergeschdelld wern.", "multiplayer.status.cannot_resolve": "Die Seavaadress konnde nid gefundn wern", "multiplayer.status.finished": "<PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Inkombadieble Veasion!", "multiplayer.status.motd.narration": "Message of the day: %s", "multiplayer.status.no_connection": "(<PERSON>eb<PERSON>ung)", "multiplayer.status.old": "Veralded", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s milliseconds", "multiplayer.status.pinging": "Abfrachn ...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s out of %s players online", "multiplayer.status.quitting": "Wird beended", "multiplayer.status.request_handled": "Schdadusanfrache wuad beabeided", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Schdadus ohne Anfrache erhaldn", "multiplayer.status.version.narration": "Server version: %s", "multiplayer.stopSleeping": "Uffschdänn", "multiplayer.texturePrompt.failure.line1": "Des Re<PERSON>baged des Servas konnde ned angwand weadn", "multiplayer.texturePrompt.failure.line2": "Jegliche Funktionalidädn, die bnutzadefiniade Ressourcn eafoadan, funktionian möglichaweise ned wie eawarded", "multiplayer.texturePrompt.line1": "<PERSON><PERSON> empfiehlt die Veawendung von nem benutzedefinierdm Resscourcnbageed.", "multiplayer.texturePrompt.line2": "<PERSON><PERSON>t de Weld audomodisch rundeladn und insdalliern?", "multiplayer.texturePrompt.serverPrompt": "%s\nNaachrichd vom Serva:\n%s", "multiplayer.title": "Measchbie<PERSON><PERSON><PERSON> schbieln", "multiplayer.unsecureserver.toast": "Messages sent on this server may be modified and might not reflect the original message", "multiplayer.unsecureserver.toast.title": "Chat messages can't be verified", "multiplayerWarning.check": "Diesn Hinweis nimmer anzeign", "multiplayerWarning.header": "Obachd: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von am Driddanbieda", "multiplayerWarning.message": "Obachd: <PERSON> Measchbielamoddus wiad üba an Driddanbieda-<PERSON><PERSON>, die weda Mojang Studios oda Microsoft ghörn, noch von diesn bedriebn oda übawachd werdn. Bei der Verwendung des Measchbielamoddus könndesd du unmoderierdn G'schwatz oda sonsdichn nutzagenerierdn Inhaldn ausgsetzt sein, die womöglich net füa jedn angmessn san.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Gnobpf: %s", "narration.button.usage.focused": "Oangabedasde drüggen, um zu oagdivieren", "narration.button.usage.hovered": "Lingsgligg zum Oagdivieren", "narration.checkbox": "Gondrollgästchen: %s", "narration.checkbox.usage.focused": "<PERSON><PERSON><PERSON>, um o<PERSON>zu<PERSON>alden", "narration.checkbox.usage.hovered": "Lingsgligg zum Oamschalden", "narration.component_list.usage": "<PERSON><PERSON>gg<PERSON> die Dabuladordasde, um zum nächsden Älemend zu wächseln", "narration.cycle_button.usage.focused": "Oeingobedasde drüggen, um zu %s zu wäx`ln", "narration.cycle_button.usage.hovered": "<PERSON><PERSON><PERSON><PERSON>, um zu %s zu wäx`ln", "narration.edit_box": "Oingabefeld: %s", "narration.item": "Item: %s", "narration.recipe": "Rezebd fuar %s", "narration.recipe.usage": "Lingsgligg zum Oaswählen", "narration.recipe.usage.more": "<PERSON><PERSON><PERSON><PERSON><PERSON>, uam mähr Rezebdä oanzuzeigen", "narration.selection.usage": "<PERSON><PERSON><PERSON>e obbere oder unddere Pfeildasde, um zu oeinem ondären Ointrag zu wächseln", "narration.slider.usage.focused": "<PERSON><PERSON>gge lingde oder rechde Pfeildasdä, um dän Wärd zu ändern", "narration.slider.usage.hovered": "Schiebäräg<PERSON> ziehen, um den Wärd zu ändern", "narration.suggestion": "Voaschlog %s vo %s ausgewählt: (%s)", "narration.suggestion.tooltip": "Voaschlog %s vo %s ausgewählt: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Press Tab to cycle to the next suggestion", "narration.suggestion.usage.cycle.hidable": "Press Tab to cycle to the next suggestion, or Escape to leave suggestions", "narration.suggestion.usage.fill.fixed": "Press Tab to use suggestion", "narration.suggestion.usage.fill.hidable": "Press Tab to use suggestion, or Escape to leave suggestions", "narration.tab_navigation.usage": "Press Ctrl and Tab to switch between tabs", "narrator.button.accessibility": "Erreichboarkeit", "narrator.button.difficulty_lock": "Schwierrichkoeitssperre", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "Entsperrd", "narrator.button.language": "<PERSON><PERSON><PERSON><PERSON>", "narrator.controls.bound": "%s isch gebunne an %s", "narrator.controls.reset": "%s zurüggsetzn", "narrator.controls.unbound": "%s isch niet gebunne", "narrator.joining": "Beid<PERSON>d", "narrator.loading": "Ladn: %s", "narrator.loading.done": "<PERSON><PERSON><PERSON>", "narrator.position.list": "Lischtenzoile %s von %s oasg`wähld", "narrator.position.object_list": "Zoilenälemende %s von %s oasg`wähld", "narrator.position.screen": "Bildschirmälemänd %s von %s", "narrator.position.tab": "Selected tab %s out of %s", "narrator.ready_to_play": "Ready to play", "narrator.screen.title": "Tiddle Bildschirm", "narrator.screen.usage": "Nutsche dän <PERSON> oda dieTabuladordasde, um däs Älemend oaszuwählen", "narrator.select": "Gwähld: %s", "narrator.select.world": "%s ausg`wähld, zuletzd g`spielt: %s, %s, %s, Veasion: %s", "narrator.select.world_info": "Selected %s, last played: %s, %s", "narrator.toast.disabled": "Schbrachausgobe deagdiviad", "narrator.toast.enabled": "Schbrachausgobe agdiviad", "optimizeWorld.confirm.description": "Des wird deine Weld durch Schbeichen der Doudn im neuesten Formad obtimiern. Des kann a sehr lange <PERSON> dauen, a<PERSON><PERSON><PERSON><PERSON><PERSON> von de G<PERSON> deina Weld. A mol durchgfärdd, kann deine Weld im Spiel schneller sei, kann abe nimme mid äldere Veasione des Schbiels gelade wern. Bisde siche, dassde fordfahre mechdesd?", "optimizeWorld.confirm.proceed": "Create Backup and Optimize", "optimizeWorld.confirm.title": "Weld obdimiern", "optimizeWorld.info.converted": "Agdualisiede Chungs: %s", "optimizeWorld.info.skipped": "Übeschbrungene Chungs: %s", "optimizeWorld.info.total": "Gesambde Chungs: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Chungs wern gezähld ...", "optimizeWorld.stage.failed": "Fehlgschlochn :(", "optimizeWorld.stage.finished": "Finishing up...", "optimizeWorld.stage.finished.chunks": "Finishing up upgrading chunks...", "optimizeWorld.stage.finished.entities": "Finishing up upgrading entities...", "optimizeWorld.stage.finished.poi": "Finishing up upgrading points of interest...", "optimizeWorld.stage.upgrading": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.chunks": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.entities": "Upgrading all entities...", "optimizeWorld.stage.upgrading.poi": "Upgrading all points of interest...", "optimizeWorld.title": "Weld '%s' wird obtimierd", "options.accessibility": "Accessibility Settings...", "options.accessibility.high_contrast": "High Contrast", "options.accessibility.high_contrast.error.tooltip": "High Contrast resource pack is not available.", "options.accessibility.high_contrast.tooltip": "Enhances the contrast of UI elements.", "options.accessibility.high_contrast_block_outline": "High Contrast Block Outlines", "options.accessibility.high_contrast_block_outline.tooltip": "Enhances the block outline contrast of the targeted block.", "options.accessibility.link": "Leidfadn zua Barrierefreiheid", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON> Blur", "options.accessibility.menu_background_blurriness.tooltip": "Changes the blurriness of menu backgrounds.", "options.accessibility.narrator_hotkey": "Narrator <PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Allows the Narrator to be toggled on and off with 'Cmd+B'.", "options.accessibility.narrator_hotkey.tooltip": "Allows the Narrator to be toggled on and off with 'Ctrl+B'.", "options.accessibility.panorama_speed": "Panorama Scroll Speed", "options.accessibility.text_background": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "überall", "options.accessibility.text_background_opacity": "Texdhindergrund-Deckkroafd", "options.accessibility.title": "Barrierefreiheid...", "options.allowServerListing": "Sever-Auflischdung zulaschen", "options.allowServerListing.tooltip": "Server könne Online-Spieler alsch Teil ihresch üffendlichen Stadus uflisten.\nWenn diesche Obzion deaktivierd is, erscheind doihn Name nichd in solschen Lischten.", "options.ao": "Weiche Beleuchdung", "options.ao.max": "S höchsde", "options.ao.min": "Minimum", "options.ao.off": "OFF", "options.attack.crosshair": "Foudngreiz", "options.attack.hotbar": "Gschwindzugr.", "options.attackIndicator": "Angriffsanzeiche", "options.audioDevice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.audioDevice.default": "Standardsyschtem", "options.autoJump": "Audomodischs Schbringa", "options.autoSuggestCommands": "Bfehlsvoeschläche", "options.autosaveIndicator": "Selbschdspeicher-Oahnzeige", "options.biomeBlendRadius": "Biomübegang", "options.biomeBlendRadius.1": "Aus (Am Schnellsdn)", "options.biomeBlendRadius.11": "11x11 (Exdreem)", "options.biomeBlendRadius.13": "13x13 (brotzich)", "options.biomeBlendRadius.15": "15x15 (Maximum)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (normal)", "options.biomeBlendRadius.7": "7x7 (hoch)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON> hoch)", "options.chat": "Cha<PERSON>s...", "options.chat.color": "Farbm", "options.chat.delay": "G'schwatzveazöcherung: %s Sekundn", "options.chat.delay_none": "G'schwatzveazöcherung: <PERSON><PERSON>", "options.chat.height.focused": "<PERSON><PERSON><PERSON> (offn)", "options.chat.height.unfocused": "<PERSON><PERSON><PERSON> (gschlossn)", "options.chat.line_spacing": "Zeilnabstand", "options.chat.links": "Weblinks", "options.chat.links.prompt": "<PERSON><PERSON><PERSON>n", "options.chat.opacity": "G'schwätz-Texddeckkroafd", "options.chat.scale": "G'schwatz - Texdgröß", "options.chat.title": "Chateinschdellungen ...", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "Shown", "options.chat.visibility.hidden": "Hidden", "options.chat.visibility.system": "<PERSON><PERSON>", "options.chat.width": "<PERSON><PERSON><PERSON>", "options.chunks": "%s <PERSON>s", "options.clouds.fancy": "Fancy", "options.clouds.fast": "Fast", "options.controls": "Schdeuerung ...", "options.credits_and_attribution": "Credits & Attribution...", "options.damageTiltStrength": "Damage Tilt", "options.damageTiltStrength.tooltip": "The amount of camera shake caused by being hurt.", "options.darkMojangStudiosBackgroundColor": "Schwoazwoaß-Logo", "options.darkMojangStudiosBackgroundColor.tooltip": "Ändad die hindagrundfabn des Loadebildschiams von Mojang Studios zu Schwoaz.", "options.darknessEffectScale": "Darkness Pulsing", "options.darknessEffectScale.tooltip": "Controls how much the Darkness effect pulses when a Warden or Sculk Shrieker gives it to you.", "options.difficulty": "Sc<PERSON><PERSON><PERSON><PERSON>ke<PERSON>", "options.difficulty.easy": "<PERSON><PERSON><PERSON>", "options.difficulty.easy.info": "Hostile mobs spawn but deal less damage. Hunger bar depletes and drains health down to 5 hearts.", "options.difficulty.hard": "Schwa", "options.difficulty.hard.info": "Hostile mobs spawn and deal more damage. Hunger bar depletes and drains all health.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Hostile mobs spawn and deal standard damage. Hunger bar depletes and drains health down to half a heart.", "options.difficulty.online": "Server-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "No hostile mobs and only some neutral mobs spawn. Hunger bar doesn't deplete and health replenishes over time.", "options.directionalAudio": "Directional Audio", "options.directionalAudio.off.tooltip": "Classic Stereo sound.", "options.directionalAudio.on.tooltip": "Uses HRTF-based directional audio to improve the simulation of 3D sound. Requires HRTF compatible audio hardware, and is best experienced with headphones.", "options.discrete_mouse_scroll": "Eichenständiches Blättan", "options.entityDistanceScaling": "Objegtentfernung", "options.entityShadows": "Objegdschaddn", "options.font": "Font Settings...", "options.font.title": "Font Settings", "options.forceUnicodeFont": "Unicode ezwinga", "options.fov": "<PERSON><PERSON><PERSON>feld (FOV)", "options.fov.max": "Quake Pro", "options.fov.min": "Normal", "options.fovEffectScale": "Sichdfeldeffegte", "options.fovEffectScale.tooltip": "Controls how much the field of view can change with gameplay effects.", "options.framerate": "%s FPS", "options.framerateLimit": "Höchsde Bildrode", "options.framerateLimit.max": "Unbegrenzd", "options.fullscreen": "Vollbild", "options.fullscreen.current": "Akduell", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "Vollbildufflösung", "options.fullscreen.unavailable": "Einstellung nicht verfügbar", "options.gamma": "Hellichkeid", "options.gamma.default": "<PERSON><PERSON><PERSON>", "options.gamma.max": "Hell", "options.gamma.min": "<PERSON><PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON>", "options.glintSpeed.tooltip": "Controls how fast the visual glint shimmers across enchanted items.", "options.glintStrength": "<PERSON><PERSON>", "options.glintStrength.tooltip": "Controls how transparent the visual glint is on enchanted items.", "options.graphics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.graphics.fabulous": "Passd scho!", "options.graphics.fabulous.tooltip": "Der Grafigmoddus \"%s\" veaw<PERSON> <PERSON>r, um Wedd<PERSON>, Wolkn und Partikl auch hinda lichtdurchläsign Blöckn und Wassa dazustelln. Dies kann die Leistung oaf dragbarn Grätn und 4K-Bildschirmn eaheblich beeindrächdign.", "options.graphics.fancy": "<PERSON><PERSON>", "options.graphics.fancy.tooltip": "<PERSON> Grafigmodd<PERSON> \"<PERSON><PERSON>\" stelld oaf den meisdn Grädn nen Ausgleich zwischn Leisdung und Qualidäd her.\nWedda, Wolkn und Partikl können hinda lichtdurchlässign Blöggn und Wassa net angzeichd werdn.", "options.graphics.fast": "Gschwind", "options.graphics.fast.tooltip": "<PERSON> Grafigmoddus \"<PERSON><PERSON><PERSON>\" reduziad die Menge an sichdboen Regn und Schnäh.\nTransbarenzeffegde san füa verschiedene Blögge wie Laub deaktiviead.", "options.graphics.warning.accept": "Continue Without Support", "options.graphics.warning.cancel": "Take Me Back", "options.graphics.warning.message": "<PERSON><PERSON> w<PERSON> er<PERSON>, dass dene Grafigkaddn den Grafigmoddus \"%s\" ned undastützd.\nDu kannsd diese Meldung ignoriern und foadfahn, aba füa dein Gräd wiad kene Undastützung angbodn, wenn du dich endscheidesd, den Grafigmoddus \"%s\" zu verwendn.", "options.graphics.warning.renderer": "Rendera erkannd: [%s]", "options.graphics.warning.title": "Grafigkaddn wiad ned undastützd", "options.graphics.warning.vendor": "<PERSON><PERSON><PERSON><PERSON><PERSON> erkannd: [%s]", "options.graphics.warning.version": "OpenGL-Veasion erkannd: [%s]", "options.guiScale": "GUI-Gress", "options.guiScale.auto": "Audomadisch", "options.hidden": "Hidden", "options.hideLightningFlashes": "Blitzschlischter versteggen", "options.hideLightningFlashes.tooltip": "Prevents Lightning Bolts from making the sky flash. The bolts themselves will still be visible.", "options.hideMatchedNames": "<PERSON><PERSON> fildan", "options.hideMatchedNames.tooltip": "3rd-party Servers may send chat messages in non-standard formats.\nWith this option on, hidden players will be matched based on chat sender names.", "options.hideSplashTexts": "Hide Splash Texts", "options.hideSplashTexts.tooltip": "Hides the yellow splash text in the main menu.", "options.inactivityFpsLimit": "Reduce FPS when", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Limits framerate to 30 when the game is not getting any player input for more than a minute. Further limits it to 10 after 9 more minutes.", "options.inactivityFpsLimit.minimized": "Minimized", "options.inactivityFpsLimit.minimized.tooltip": "Limits framerate only when the game window is minimized.", "options.invertMouse": "<PERSON><PERSON> um<PERSON>n", "options.japaneseGlyphVariants": "Japanese Glyph Variants", "options.japaneseGlyphVariants.tooltip": "Uses Japanese variants of CJK characters in the default font.", "options.key.hold": "<PERSON><PERSON>", "options.key.toggle": "Umschaldn", "options.language": "Schbroch ...", "options.language.title": "Language", "options.languageAccuracyWarning": "(Language translations may not be 100%% accurate)", "options.languageWarning": "De Übesetzungen san evenduell ned ganz <PERSON>h", "options.mainHand": "<PERSON><PERSON><PERSON><PERSON>", "options.mainHand.left": "<PERSON><PERSON>", "options.mainHand.right": "Rechds", "options.mipmapLevels": "Mipmap-Schdufn", "options.modelPart.cape": "<PERSON>dl", "options.modelPart.hat": "Kabbm", "options.modelPart.jacket": "Jaggng", "options.modelPart.left_pants_leg": "Lingges Housnba", "options.modelPart.left_sleeve": "<PERSON><PERSON>", "options.modelPart.right_pants_leg": "<PERSON><PERSON><PERSON>", "options.modelPart.right_sleeve": "<PERSON><PERSON><PERSON>", "options.mouseWheelSensitivity": "Maus Geschwindigkeit", "options.mouse_settings": "Mauseinschdellungen...", "options.mouse_settings.title": "Mauseinschdellungen", "options.multiplayer.title": "Measchbielaeinschdellungen ...", "options.multiplier": "%sx", "options.music_frequency": "Music Frequency", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Frequent", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "Schbrachausgobe", "options.narrator.all": "Liast ois", "options.narrator.chat": "<PERSON><PERSON> Chod", "options.narrator.notavailable": "<PERSON> v<PERSON>", "options.narrator.off": "OFF", "options.narrator.system": "Liast System", "options.notifications.display_time": "Notification Time", "options.notifications.display_time.tooltip": "Affects the length of time that all notifications stay visible on the screen.", "options.off": "Aus", "options.off.composed": "%s: Aus", "options.on": "An", "options.on.composed": "%s: An", "options.online": "Online...", "options.online.title": "Online-Einschtellungen", "options.onlyShowSecureChat": "Only Show Secure Chat", "options.onlyShowSecureChat.tooltip": "Only display messages from other players that can be verified to have been sent by that player, and have not been modified.", "options.operatorItemsTab": "Operator Items Tab", "options.particles": "Badiggl", "options.particles.all": "All", "options.particles.decreased": "Wen<PERSON>", "options.particles.minimal": "Minimal", "options.percent_add_value": "%s: +%s %%", "options.percent_value": "%s: %s %%", "options.pixel_value": "%s: %s px", "options.prioritizeChunkUpdates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer": "Zuhälfschte blockierend", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Mansche Aktschionen in enem Brogge führe zu ner Neukomplementierung vom dieschem Brogge. Datschu gehört des plaschieren und abgebaue von Blö<PERSON>.", "options.prioritizeChunkUpdates.nearby": "Ganzsch blockierend", "options.prioritizeChunkUpdates.nearby.tooltip": "Benachbarde Brogge werde immer sofort kompliemierd. Diesch kann die Spielleischstung beeinträchdrigen, wenn Blögge plaschierd oder zerschdört werde.", "options.prioritizeChunkUpdates.none": "Barallel", "options.prioritizeChunkUpdates.none.tooltip": "Benaschbarte Broggen werde prallalen komplimird. <PERSON><PERSON> kann zu kurz sischtbare Löscher führe, wenn Blöcke zerstört werde.", "options.rawMouseInput": "<PERSON><PERSON><PERSON>", "options.realmsNotifications": "Realms News & Invites", "options.realmsNotifications.tooltip": "Fetches Realms news and invites in the title screen and displays their respective icon on the Realms button.", "options.reducedDebugInfo": "<PERSON><PERSON>In<PERSON><PERSON>", "options.renderClouds": "Wolgn", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "Sichdweide", "options.resourcepack": "Ressourcnbagede ...", "options.rotateWithMinecart": "Rotate with Minecarts", "options.rotateWithMinecart.tooltip": "Whether the player's view should rotate with a turning Minecart. Only available in worlds with the 'Minecart Improvements' experimental setting turned on.", "options.screenEffectScale": "Verzerrungseffegt", "options.screenEffectScale.tooltip": "<PERSON><PERSON><PERSON> von Üblkeids- und Netherpoadal-Verzerrungseffekdn.\nBei niedrigern Werdn wird der Üblkeidseffegt duach a grüne Übalagarung ersetzt.", "options.sensitivity": "Embfindlichgeid", "options.sensitivity.max": "DURBO!!!", "options.sensitivity.min": "*gähn*", "options.showNowPlayingToast": "Show Music Toast", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "Undediddl anzeichn", "options.simulationDistance": "Schimulationweide", "options.skinCustomisation": "Skin-Anbassung ...", "options.skinCustomisation.title": "Skin-<PERSON><PERSON><PERSON><PERSON>", "options.sounds": "Mussig & Geräusch ...", "options.sounds.title": "Mussig- & Geräuschobzione", "options.telemetry": "Telemetry Data...", "options.telemetry.button": "Data Collection", "options.telemetry.button.tooltip": "\"%s\" includes only the required data.\n\"%s\" includes optional, as well as the required data.", "options.telemetry.disabled": "<PERSON><PERSON><PERSON><PERSON> is disabled.", "options.telemetry.state.all": "All", "options.telemetry.state.minimal": "Minimal", "options.telemetry.state.none": "None", "options.title": "Obzione", "options.touchscreen": "Touchscreen-<PERSON><PERSON><PERSON>", "options.video": "Grafigeinschdellungen ...", "options.videoTitle": "Grafigeinschdellungen", "options.viewBobbing": "Gehbewechung", "options.visible": "Sichdboe", "options.vsync": "VSync", "outOfMemory.message": "Minecraft has run out of memory.\n\nThis could be caused by a bug in the game or by the Java Virtual Machine not being allocated enough memory.\n\nTo prevent world corruption, the current game has quit. We've tried to free up enough memory to let you go back to the main menu and back to playing, but this may not have worked.\n\nPlease restart the game if you see this message again.", "outOfMemory.title": "Out of memory!", "pack.available.title": "<PERSON><PERSON><PERSON><PERSON>", "pack.copyFailure": "Bagede konndn net kobierd werdn", "pack.dropConfirm": "Maagsd du die folgndn Bagede zu Minecraft hinzufügn?", "pack.dropInfo": "Um Bagede hinz<PERSON>, zieh se in des Fensda nei", "pack.dropRejected.message": "The following entries were not valid packs and were not copied:\n %s", "pack.dropRejected.title": "Non-pack entries", "pack.folderInfo": "(<PERSON><PERSON><PERSON><PERSON><PERSON>de hia neidoa)", "pack.incompatible": "Inkombadibl", "pack.incompatible.confirm.new": "Des Resso<PERSON>bageed is füa a neuare Veasion von Minecraft und gehd evenduell ned rich<PERSON>h.", "pack.incompatible.confirm.old": "Des Ressour<PERSON>bageed is füa a äldere Veasion von Minecraft und gehd evenduell ned mea richdich.", "pack.incompatible.confirm.title": "<PERSON><PERSON>de sicha, dassde dieses <PERSON>geed ladn willsd?", "pack.incompatible.new": "(Füa e neiere Veasion von Minecraft gemachd)", "pack.incompatible.old": "(Füa e äldere Veasion von Minecraft gemachd)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Bagedoadna <PERSON>", "pack.selected.title": "Ausgwähld", "pack.source.builtin": "midglief<PERSON>", "pack.source.feature": "feature", "pack.source.local": "lokal", "pack.source.server": "<PERSON><PERSON>", "pack.source.world": "world", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Random variant", "parsing.bool.expected": "Wohrheidswärd ärwardet", "parsing.bool.invalid": "Ungüldschorr Wahrheidswerd: ‚%s‘ is wedorr ‚true‘ noch ‚false‘", "parsing.double.expected": "Gommazohl ärwaded", "parsing.double.invalid": "<PERSON>g<PERSON><PERSON>`sche Gommazohl ‚%s‘", "parsing.expected": "‚%s‘ ärwardet", "parsing.float.expected": "Gommazohl ärwaded", "parsing.float.invalid": "<PERSON>g<PERSON>ld`<PERSON>che Gomma<PERSON>hl,%s´", "parsing.int.expected": "<PERSON><PERSON><PERSON><PERSON>", "parsing.int.invalid": "Ungüldsche Gonzzahl ‚%s‘", "parsing.long.expected": "Longä Gonzzahl ärwa`det", "parsing.long.invalid": "Ungüld`sche longä Gonzzahl,%s´", "parsing.quote.escape": "Ungüldsche Escape-Sequenz ‚\\%s‘ in Zeischngedde", "parsing.quote.expected.end": "Fählendesch Anführungszeischn am Ende där Zeischngedde", "parsing.quote.expected.start": "Fählendesch Oanführungszeischn am Beginn där Zeischngedde", "particle.invalidOptions": "Can't parse particle options: %s", "particle.notFound": "Unb`ganndä Pardigel: %s", "permissions.requires.entity": "<PERSON><PERSON> kann nua von am Objekd ausgfüahd weadn", "permissions.requires.player": "<PERSON><PERSON> kann nua von am Schbiela ausgfüahd weadn", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Auswiakungn:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Unb`ganndes Prädigad: %s", "quickplay.error.invalid_identifier": "Could not find world with the provided identifier", "quickplay.error.realm_connect": "Could not connect to Realm", "quickplay.error.realm_permission": "Lacking permission to connect to this Realm", "quickplay.error.title": "Failed to Quick Play", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Western Japan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "United Arab Emirates (UAE)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "Realms wiad deazoad in Endwigglungsveasion ned undastützd", "recipe.notFound": "Unb`ganndes Rezebd: %s", "recipe.toast.description": "Sc<PERSON>u in dein Rezebdbuach", "recipe.toast.title": "Neue Rezebde veafügba!", "record.nowPlaying": "Jetzadla schbield: %s", "recover_world.bug_tracker": "Report a Bug", "recover_world.button": "Attempt to Recover", "recover_world.done.failed": "Failed to recover from previous state.", "recover_world.done.success": "Recovery was successful!", "recover_world.done.title": "Recovery done", "recover_world.issue.missing_file": "Missing file", "recover_world.issue.none": "No issues", "recover_world.message": "The following issues occurred while trying to read world folder \"%s\".\nIt might be possible to restore the world from an older state or you can report this issue on the bug tracker.", "recover_world.no_fallback": "No state to recover from available", "recover_world.restore": "Attempt to Restore", "recover_world.restoring": "Attempting to restore world...", "recover_world.state_entry": "State from %s: ", "recover_world.state_entry.unknown": "unknown", "recover_world.title": "Failed to load world", "recover_world.warning": "Failed to load world summary", "resourcePack.broken_assets": "DFEGTE RESSOURCN EAKANND", "resourcePack.high_contrast.name": "High Contrast", "resourcePack.load_fail": "Neuladen dea Ressourcn fehlgschlochn", "resourcePack.programmer_art.name": "Programmer Art", "resourcePack.runtime_failure": "Resource pack error detected", "resourcePack.server.name": "Weldspäzifische Reschurchen", "resourcePack.title": "Ressourcnbagede auswähle", "resourcePack.vanilla.description": "The default look and feel of Minecraft", "resourcePack.vanilla.name": "<PERSON><PERSON><PERSON>", "resourcepack.downloading": "Ressourcnbageed wiad rundagladn", "resourcepack.progress": "Dadei wiad rundagladn (%s MB) ...", "resourcepack.requesting": "Sende Anfrache ...", "screenshot.failure": "<PERSON>ann Screenshot ned spa<PERSON>an: %s", "screenshot.success": "Screenshot gspaichad unda %s", "selectServer.add": "<PERSON><PERSON>", "selectServer.defaultName": "Minecraft-Seava", "selectServer.delete": "Delete", "selectServer.deleteButton": "Delete", "selectServer.deleteQuestion": "<PERSON><PERSON>de siche, dassde den Seava endferne willsd?", "selectServer.deleteWarning": "'%s' will be lost forever! (A long time!)", "selectServer.direct": "Direggdvebindung", "selectServer.edit": "Edit", "selectServer.hiddenAddress": "(Verschdeggd)", "selectServer.refresh": "Aggdualisien", "selectServer.select": "<PERSON><PERSON><PERSON> gehe", "selectWorld.access_failure": "Die Welt konnte nicht betreten werden", "selectWorld.allowCommands": "Schummln ealaubt", "selectWorld.allowCommands.info": "B<PERSON>hle wie /gamemode, /experience", "selectWorld.allowCommands.new": "Allow Commands", "selectWorld.backupEraseCache": "Zwischengspeicherte Daadn löschn", "selectWorld.backupJoinConfirmButton": "<PERSON><PERSON> eastelln un ladn", "selectWorld.backupJoinSkipButton": "I waas, wos i du!", "selectWorld.backupQuestion.customized": "Angebassde Weldn wern nimmer undaschdützd", "selectWorld.backupQuestion.downgrade": "Des Hearab<PERSON>ufn eina Weld wiad nichd undastützd", "selectWorld.backupQuestion.experimental": "Weldn die exberimendelle Ansdellungn bnuzten werdn net undastützd", "selectWorld.backupQuestion.snapshot": "Do you really want to load this world?", "selectWorld.backupWarning.customized": "<PERSON><PERSON> undaschdützn mer kane angebassde Weldn in diesa Veasion von Minecraft. Mer könne die Weld drotzdem noch ladn und alles bleibd wie's is, aber neu generierde Landschafd wiad nimmer angebasst sei. Mer endschuldichn uns füa die Unannehmlichkeide!", "selectWorld.backupWarning.downgrade": "Diese Weld wuade zuletzd in dea Veasion %s gschbield; du bisd oaf Veasion %s. Die Weld mid eina älderen Veasion zu schbieln, kann zu Dadnbschädigungn füahn - wia könna ned garandiean, dass die Weld lädt oda läufd. Wenn du drotzdem foad<PERSON>hn willsd, eastell bidde ane Sichaheidskobie!", "selectWorld.backupWarning.experimental": "<PERSON><PERSON> had exberimendelle Ansdellungn die jedazeid zum Abstuaz fühn könna. Wia könna net garantiern das des funktioniad. Hia sein Drachn!", "selectWorld.backupWarning.snapshot": "Die Weld wuade zuletzd mid da Veasion %s geschbield; du bisd oaf da Veasion %s. <PERSON><PERSON> bidde ane <PERSON>skobie, da die Weld beschädichd wern könnde!", "selectWorld.bonusItems": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.cheats": "Cheats", "selectWorld.commands": "Commands", "selectWorld.conversion": "Muss gonverdierd sei!", "selectWorld.conversion.tooltip": "Diesche Welt musch in ene ältere Verschion (wie 1.6.4) ge<PERSON><PERSON><PERSON> werden, damit sicher umg`wandelt wird", "selectWorld.create": "Neue Weld erschdelle", "selectWorld.customizeType": "Anbassn", "selectWorld.dataPacks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.data_read": "<PERSON>...", "selectWorld.delete": "<PERSON><PERSON>", "selectWorld.deleteButton": "Delete", "selectWorld.deleteQuestion": "<PERSON><PERSON><PERSON> siche, dass de die Weld lösche mechsd?", "selectWorld.deleteWarning": "'%s' wiad für imme verlore sei! (A lange Zeid!)", "selectWorld.delete_failure": "Die Welt konnte nicht gelöscht werden", "selectWorld.edit": "Bearbeide", "selectWorld.edit.backup": "Sicherungskobie machn", "selectWorld.edit.backupCreated": "Gesiched: %s", "selectWorld.edit.backupFailed": "Sicherung fehlgschlochn", "selectWorld.edit.backupFolder": "Sicherungskobienodner effne", "selectWorld.edit.backupSize": "Gress: %s MB", "selectWorld.edit.export_worldgen_settings": "Aussch<PERSON><PERSON><PERSON><PERSON><PERSON> von Weldbildungsanstellungn", "selectWorld.edit.export_worldgen_settings.failure": "Ausschläußung fehlgschlachn", "selectWorld.edit.export_worldgen_settings.success": "Ausgschläußt", "selectWorld.edit.openFolder": "Weldodne effne", "selectWorld.edit.optimize": "Optimize World", "selectWorld.edit.resetIcon": "Symbol zurüggsetzn", "selectWorld.edit.save": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.title": "Weld bearbeide", "selectWorld.enterName": "Nome der Weld", "selectWorld.enterSeed": "Schdardwerd füa den Weldgenerador", "selectWorld.experimental": "Experimental", "selectWorld.experimental.details": "Details", "selectWorld.experimental.details.entry": "Required experimental features: %s", "selectWorld.experimental.details.title": "Experimental Feature Requirements", "selectWorld.experimental.message": "Be careful!\nThis configuration requires features that are still under development. Your world might crash, break, or not work with future updates.", "selectWorld.experimental.title": "Experimental Features Warning", "selectWorld.experiments": "Experiments", "selectWorld.experiments.info": "Experiments are potential new features. Be careful as things might break. Experiments can't be turned off after world creation.", "selectWorld.futureworld.error.text": "<PERSON><PERSON> eina <PERSON>ld aus eina neuaren Veasion isd was schiefglaufn. Des war von Anfang a a riskandes Underfangn und had leida net geglabbd.", "selectWorld.futureworld.error.title": "A Fehla is uffgetredn!", "selectWorld.gameMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure.info": "Same as Survival Mode, but blocks can't be added or removed.", "selectWorld.gameMode.adventure.line1": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> kö<PERSON> jedoch", "selectWorld.gameMode.adventure.line2": "könne abe nid blatzierd oder zerschdödd wern", "selectWorld.gameMode.creative": "Greadiv", "selectWorld.gameMode.creative.info": "Create, build, and explore without limits. You can fly, have endless materials, and can't be hurt by monsters.", "selectWorld.gameMode.creative.line1": "Unbegrendsde Ressourcn, Fluchmoddus und", "selectWorld.gameMode.creative.line2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.hardcore": "Hardcore", "selectWorld.gameMode.hardcore.info": "Survival Mode locked to 'Hard' difficulty. You can't respawn if you die.", "selectWorld.gameMode.hardcore.line1": "Übalebensmoddus oaf da he<PERSON>dn", "selectWorld.gameMode.hardcore.line2": "Sch<PERSON><PERSON>hkeid midd endgüldichm Doud", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "You can look but don't touch.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON> guggn, nid of<PERSON>n", "selectWorld.gameMode.survival": "Übelebm", "selectWorld.gameMode.survival.info": "Explore a mysterious world where you build, collect, craft, and fight monsters.", "selectWorld.gameMode.survival.line1": "Such nach Ressour<PERSON>n, bau Weakzeuche, sammle", "selectWorld.gameMode.survival.line2": "Erfohrung und kämbf ums übelebm", "selectWorld.gameRules": "Spielregeln", "selectWorld.import_worldgen_settings": "Einfüan der Anstellungn", "selectWorld.import_worldgen_settings.failure": "<PERSON><PERSON><PERSON> uffgedredn beim Einfüan der Anstellungn", "selectWorld.import_worldgen_settings.select_file": "Anstellungsdadei auswähln (.json)", "selectWorld.incompatible.description": "This world cannot be opened in this version.\nIt was last played in version %s.", "selectWorld.incompatible.info": "Incompatible version: %s", "selectWorld.incompatible.title": "Incompatible version", "selectWorld.incompatible.tooltip": "This world cannot be opened because it was created by an incompatible version.", "selectWorld.incompatible_series": "Mid ner inkomplatiden Verschion erstelld", "selectWorld.load_folder_access": "<PERSON><PERSON> den <PERSON>, wo die Weldn gschbeiched sin, konnde nid zugriffn wern!", "selectWorld.loading_list": "Loading World List", "selectWorld.locked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> da Minecraft breids offn ist in nem andren <PERSON>sta", "selectWorld.mapFeatures": "Erbau Konstrugtionen", "selectWorld.mapFeatures.info": "Villages, Shipwrecks, etc.", "selectWorld.mapType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.mapType.normal": "Normol", "selectWorld.moreWorldOptions": "<PERSON>a Weldo<PERSON>zione ...", "selectWorld.newWorld": "Neue Weld", "selectWorld.recreate": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.recreate.customized.text": "Angebassde Weldn wern in dieser Veasion von Minecraft nimmer undaschdützd. Mer könne versuche, sie nochamol mit dem selbn Schdardwerd und denselbn Eigenschafdn zu erschdelle, aber alle Landschafdsanbassungen sin dann wech. Mer endschuldichn uns füa die Unannehmlichkeide!", "selectWorld.recreate.customized.title": "Customized worlds are no longer supported", "selectWorld.recreate.error.text": "<PERSON><PERSON> er<PERSON>n <PERSON> aner <PERSON> is was schiefglaufn.", "selectWorld.recreate.error.title": "An error occurred!", "selectWorld.resource_load": "Preparing Resources...", "selectWorld.resultFolder": "<PERSON><PERSON><PERSON> g<PERSON> in:", "selectWorld.search": "noch Welden guggen", "selectWorld.seedInfo": "Leerlasse für zufällichn Schdardwerd", "selectWorld.select": "Ausgewählde Weld schbiele", "selectWorld.targetFolder": "Save folder: %s", "selectWorld.title": "Weld auswähle", "selectWorld.tooltip.fromNewerVersion1": "Weld wuade in aner neuen Veasion gschbeiched,", "selectWorld.tooltip.fromNewerVersion2": "das Ladn der Weld könnde Brobleme vauasochn!", "selectWorld.tooltip.snapshot1": "Dass du mie fei nid vegis<PERSON>, de <PERSON> zu sichen,", "selectWorld.tooltip.snapshot2": "bevor du se inner Endwigglungsveasion effnesd.", "selectWorld.unable_to_load": "Weldn konndn nid gelodn wern", "selectWorld.version": "Veasion:", "selectWorld.versionJoinButton": "Drotzdem ladn", "selectWorld.versionQuestion": "Willsde die Weld wigglich lesche?", "selectWorld.versionUnknown": "unbegannd", "selectWorld.versionWarning": "Diese Weld wuade zuletzd in dea Veasion %s gschbield. Sie in diesa Veasion zu ladn, kann de Weld beschädichn!", "selectWorld.warning.deprecated.question": "Some features used are deprecated and will stop working in the future. Do you wish to proceed?", "selectWorld.warning.deprecated.title": "Warning! These settings are using deprecated features", "selectWorld.warning.experimental.question": "These settings are experimental and could one day stop working. Do you wish to proceed?", "selectWorld.warning.experimental.title": "Warning! These settings are using experimental features", "selectWorld.warning.lowDiskSpace.description": "There is not much space left on your device.\nRunning out of disk space while in game can lead to your world being damaged.", "selectWorld.warning.lowDiskSpace.title": "Warning! Low disk space!", "selectWorld.world": "Weld", "sign.edit": "<PERSON> drauf<PERSON>im", "sleep.not_possible": "Die Noachd kann ned duachschla<PERSON>n werdn", "sleep.players_sleeping": "%s/%s <PERSON><PERSON><PERSON>la schlafn", "sleep.skipping_night": "Die Noachd duachschlafn", "slot.only_single_allowed": "Only single slots allowed, got '%s'", "slot.unknown": "Unb`gannda Invendarpladz: %s", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "Addmosphäre/Umgebung", "soundCategory.block": "Blögge", "soundCategory.hostile": "Feindliche Vieche", "soundCategory.master": "Gsamdlaudschdäche", "soundCategory.music": "<PERSON><PERSON>g", "soundCategory.neutral": "Freundliche Vieche", "soundCategory.player": "Sc<PERSON><PERSON><PERSON>", "soundCategory.record": "Mussigblögge", "soundCategory.ui": "UI", "soundCategory.voice": "Schdimm/Schbroch", "soundCategory.weather": "<PERSON><PERSON><PERSON><PERSON>", "spectatorMenu.close": "<PERSON>ü schließe", "spectatorMenu.next_page": "Next Page", "spectatorMenu.previous_page": "Previous Page", "spectatorMenu.root.prompt": "<PERSON><PERSON><PERSON> oaf a <PERSON>, um an Bfehl auszuwähln und nochamol, um en zu nutzn.", "spectatorMenu.team_teleport": "Zum Diembardner delebordiern", "spectatorMenu.team_teleport.prompt": "Wähl a Diem zum Delebordiern aus", "spectatorMenu.teleport": "<PERSON>um <PERSON><PERSON><PERSON>", "spectatorMenu.teleport.prompt": "Wähl an Schbiela zum Delebordiern aus", "stat.generalButton": "Allgmein", "stat.itemsButton": "Gechnschdände", "stat.minecraft.animals_bred": "<PERSON><PERSON>", "stat.minecraft.aviate_one_cm": "Sträckn mid Elydren gflogn", "stat.minecraft.bell_ring": "<PERSON>lock<PERSON> gl<PERSON>", "stat.minecraft.boat_one_cm": "Sträckn mid am Bood gfahn", "stat.minecraft.clean_armor": "Rüstungsdeile gwaschn", "stat.minecraft.clean_banner": "<PERSON><PERSON> g<PERSON>n", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gwaschn", "stat.minecraft.climb_one_cm": "Sträckn gkläddad", "stat.minecraft.crouch_one_cm": "Sträckn glschlichn", "stat.minecraft.damage_absorbed": "<PERSON><PERSON><PERSON><PERSON> absoa<PERSON>ad", "stat.minecraft.damage_blocked_by_shield": "Sc<PERSON>adn mid am Schild abgwähad", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON><PERSON> zugfüchd", "stat.minecraft.damage_dealt_absorbed": "Sc<PERSON>adn zugfüchd (absoabiead)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON><PERSON> zugfüchd (wiedaschdandn)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>dandn", "stat.minecraft.damage_taken": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>n", "stat.minecraft.deaths": "<PERSON><PERSON>", "stat.minecraft.drop": "Gegnstände falln glassn", "stat.minecraft.eat_cake_slice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>n", "stat.minecraft.enchant_item": "Gegnstände veazaubad", "stat.minecraft.fall_one_cm": "Strä<PERSON>n gfalln", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.fish_caught": "Fische gfangn", "stat.minecraft.fly_one_cm": "Sträckn gflochn", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "Sträckn aufm Pfäad griddn", "stat.minecraft.inspect_dispenser": "<PERSON><PERSON>", "stat.minecraft.inspect_dropper": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>", "stat.minecraft.inspect_hopper": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_anvil": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_beacon": "Leuchdfeua bnutzd", "stat.minecraft.interact_with_blast_furnace": "Schmelzofn bnutzd", "stat.minecraft.interact_with_brewingstand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_campfire": "Lagafeua bnutzd", "stat.minecraft.interact_with_cartography_table": "<PERSON><PERSON><PERSON><PERSON> bnutzd", "stat.minecraft.interact_with_crafting_table": "Weakbank bnutzd", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON> b<PERSON>d", "stat.minecraft.interact_with_grindstone": "Schleifstao bnutzd", "stat.minecraft.interact_with_lectern": "<PERSON><PERSON><PERSON> bnutzd", "stat.minecraft.interact_with_loom": "Webstuahl bnutzd", "stat.minecraft.interact_with_smithing_table": "Schmieddisch bnutzd", "stat.minecraft.interact_with_smoker": "Räuchaofn bnutzd", "stat.minecraft.interact_with_stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.jump": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.leave_game": "<PERSON><PERSON><PERSON>n", "stat.minecraft.minecart_one_cm": "Sträckn mid na Lore gfahn", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON> e<PERSON>d", "stat.minecraft.open_barrel": "Fässla oafgmachd", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON> o<PERSON>", "stat.minecraft.open_enderchest": "Enderdruhe oafgmachd", "stat.minecraft.open_shulker_box": "Shulkerkis<PERSON><PERSON><PERSON> oaf<PERSON>d", "stat.minecraft.pig_one_cm": "Sträckn oaf an Schwain griddn", "stat.minecraft.play_noteblock": "Nodnblögge abgschbield", "stat.minecraft.play_record": "Schallbladdn abgschbield", "stat.minecraft.play_time": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.player_kills": "Schbiela gdöded", "stat.minecraft.pot_flower": "Pflanzn aingdopfd", "stat.minecraft.raid_trigger": "Übafälle ausglösd", "stat.minecraft.raid_win": "Übafälle gwonn'n", "stat.minecraft.sleep_in_bed": "<PERSON>d bnutzd", "stat.minecraft.sneak_time": "<PERSON><PERSON><PERSON>", "stat.minecraft.sprint_one_cm": "Sträckn gschbrinded", "stat.minecraft.strider_one_cm": "Streckn oaf Schreida", "stat.minecraft.swim_one_cm": "Sträckn gschwommn", "stat.minecraft.talked_to_villager": "Mid Doafbwohnan gschbrochn", "stat.minecraft.target_hit": "Zielblogg gdroffn", "stat.minecraft.time_since_death": "Zeid seit letztem Dod", "stat.minecraft.time_since_rest": "Zeid seit letztem Schlafn", "stat.minecraft.total_world_time": "Zoad mid göffneda Weld", "stat.minecraft.traded_with_villager": "Mid Doafbwohnan ghandld", "stat.minecraft.trigger_trapped_chest": "Redstone-Truhn ausglösd", "stat.minecraft.tune_noteblock": "Nodnblögge gschdimmd", "stat.minecraft.use_cauldron": "Wassa aus Gessln gschöpfd", "stat.minecraft.walk_on_water_one_cm": "Sträckn aufm Wassa glaufn", "stat.minecraft.walk_one_cm": "Strä<PERSON>n glaufn", "stat.minecraft.walk_under_water_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON> unda Was<PERSON> glaufn", "stat.mobsButton": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.crafted": "<PERSON>ag<PERSON><PERSON><PERSON>", "stat_type.minecraft.dropped": "Falln glassn", "stat_type.minecraft.killed": "Du hasd %s %s ealegd", "stat_type.minecraft.killed.none": "Du hasd %s nie ealegd", "stat_type.minecraft.killed_by": "%s had dich %s mal gdöded", "stat_type.minecraft.killed_by.none": "Du wuadesd nie von %s gdöded", "stat_type.minecraft.mined": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.picked_up": "Oafgsammeld", "stat_type.minecraft.used": "Bnutzd", "stats.none": "-", "structure_block.button.detect_size": "E<PERSON><PERSON><PERSON>", "structure_block.button.load": "<PERSON><PERSON>", "structure_block.button.save": "<PERSON><PERSON><PERSON>", "structure_block.custom_data": "Bnutzadefinieade NBD-Dadn", "structure_block.detect_size": "Detect Structure Size and Position:", "structure_block.hover.corner": "Eckn: %s", "structure_block.hover.data": "Dadn: %s", "structure_block.hover.load": "Ladn: %s", "structure_block.hover.save": "Speichan: %s", "structure_block.include_entities": "Include Entities:", "structure_block.integrity": "Vollständichkeid und Stardwerd dea Konstruktion", "structure_block.integrity.integrity": "Konstruktionsvollständichkeid", "structure_block.integrity.seed": "Konstruktions-Stardwead", "structure_block.invalid_structure_name": "Uangüldiga Konstruktionsnoame '%s'", "structure_block.load_not_found": "Konstruktion '%s' wuad net gfundn", "structure_block.load_prepare": "Position voan Konstruktion '%s' voabreidn", "structure_block.load_success": "Konstruktion '%s' gladn", "structure_block.mode.corner": "<PERSON><PERSON><PERSON>", "structure_block.mode.data": "<PERSON><PERSON>", "structure_block.mode.load": "<PERSON><PERSON>", "structure_block.mode.save": "Save", "structure_block.mode_info.corner": "Äck-moddus - Makie<PERSON> von Position und Größn", "structure_block.mode_info.data": "Dadn-Modus - <PERSON><PERSON>", "structure_block.mode_info.load": "Load-Modus - Aus Dadei loadn", "structure_block.mode_info.save": "Speicha-Modus - in <PERSON><PERSON> speichan", "structure_block.position": "Relative Bosition", "structure_block.position.x": "Relative X-Bosition", "structure_block.position.y": "Relative Y-Bosition", "structure_block.position.z": "Relative Z-Bosition", "structure_block.save_failure": "Konstruktion '%s' konnde net gspeichad werdn", "structure_block.save_success": "Konstruktion oals '%s' gspeichad", "structure_block.show_air": "Veschdeggde Blögge anzeign:", "structure_block.show_boundingbox": "Begrenzungsrahmn anzachn:", "structure_block.size": "Konstruktionsgrößn", "structure_block.size.x": "Konstruktionsgrößn X", "structure_block.size.y": "Konstruktionsgrößn Y", "structure_block.size.z": "Konstruktionsgrößn Z", "structure_block.size_failure": "Koann Konstruktionsgrößn net eakennen - füg Eckn mid gleichm Konstruktionsnoamn hinzu", "structure_block.size_success": "G<PERSON><PERSON>ßn voan '%s' eafolgreich eakannd", "structure_block.strict": "Strict Placement:", "structure_block.structure_name": "Konstruktionsnoame", "subtitles.ambient.cave": "Grus<PERSON><PERSON> Gräusch", "subtitles.ambient.sound": "Eerie noise", "subtitles.block.amethyst_block.chime": "Amethyst glingd", "subtitles.block.amethyst_block.resonate": "Amethyst resonates", "subtitles.block.anvil.destroy": "<PERSON><PERSON><PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON><PERSON> glanded", "subtitles.block.anvil.use": "<PERSON><PERSON><PERSON> bnutzd", "subtitles.block.barrel.close": "Fässla schließd", "subtitles.block.barrel.open": "Fässla effned", "subtitles.block.beacon.activate": "Leuchdfeua agdiviead", "subtitles.block.beacon.ambient": "Leuchdfeua brummbd", "subtitles.block.beacon.deactivate": "Leuchdfeua deagdi<PERSON>ad", "subtitles.block.beacon.power_select": "Leuchdfeua ausgwähld", "subtitles.block.beehive.drip": "<PERSON><PERSON> dropfd", "subtitles.block.beehive.enter": "<PERSON><PERSON><PERSON> fliegd in'n Schdogg", "subtitles.block.beehive.exit": "Biena fliegt ausm <PERSON>", "subtitles.block.beehive.shear": "<PERSON><PERSON> schabd", "subtitles.block.beehive.work": "Biena ärbern", "subtitles.block.bell.resonate": "<PERSON><PERSON><PERSON> halld nach", "subtitles.block.bell.use": "<PERSON><PERSON><PERSON> lä<PERSON>", "subtitles.block.big_dripleaf.tilt_down": "Dropfbladd kibbd", "subtitles.block.big_dripleaf.tilt_up": "<PERSON><PERSON><PERSON><PERSON> richded sich", "subtitles.block.blastfurnace.fire_crackle": "Schmelzofn gnisdad", "subtitles.block.brewing_stand.brew": "Braustand blubbad", "subtitles.block.bubble_column.bubble_pop": "Blasn blatzn", "subtitles.block.bubble_column.upwards_ambient": "Blasn sprudln", "subtitles.block.bubble_column.upwards_inside": "Blasn strömn", "subtitles.block.bubble_column.whirlpool_ambient": "Blasn wiabln", "subtitles.block.bubble_column.whirlpool_inside": "Blasn ziehn", "subtitles.block.button.click": "Gnopf gligd", "subtitles.block.cake.add_candle": "<PERSON><PERSON>n schwabbeld", "subtitles.block.campfire.crackle": "<PERSON><PERSON><PERSON><PERSON> kn<PERSON>", "subtitles.block.candle.crackle": "<PERSON><PERSON><PERSON>", "subtitles.block.candle.extinguish": "Candle extinguishes", "subtitles.block.chest.close": "<PERSON><PERSON><PERSON> sch<PERSON>d", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON> ve<PERSON>", "subtitles.block.chest.open": "<PERSON><PERSON>e effned", "subtitles.block.chorus_flower.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> welkd", "subtitles.block.chorus_flower.grow": "Chorusblüdn wächsd", "subtitles.block.comparator.click": "<PERSON><PERSON><PERSON><PERSON> klickd", "subtitles.block.composter.empty": "<PERSON><PERSON><PERSON> gel<PERSON>", "subtitles.block.composter.fill": "Komposta befüllt", "subtitles.block.composter.ready": "Komposta kompostiad", "subtitles.block.conduit.activate": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON> bul<PERSON><PERSON>", "subtitles.block.conduit.attack.target": "<PERSON><PERSON><PERSON> greifd aa", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON>", "subtitles.block.copper_bulb.turn_off": "Copper Bulb turns off", "subtitles.block.copper_bulb.turn_on": "Copper Bulb turns on", "subtitles.block.copper_trapdoor.close": "Trapdoor closes", "subtitles.block.copper_trapdoor.open": "Trapdoor opens", "subtitles.block.crafter.craft": "Crafter crafts", "subtitles.block.crafter.fail": "Crafter fails crafting", "subtitles.block.creaking_heart.hurt": "Creaking Heart grumbles", "subtitles.block.creaking_heart.idle": "Eerie noise", "subtitles.block.creaking_heart.spawn": "Creaking Heart awakens", "subtitles.block.deadbush.idle": "Dry sounds", "subtitles.block.decorated_pot.insert": "Decorated Pot fills", "subtitles.block.decorated_pot.insert_fail": "Decorated Pot wobbles", "subtitles.block.decorated_pot.shatter": "Decorated Pot shatters", "subtitles.block.dispenser.dispense": "Gechnschdand gwoafn", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON>", "subtitles.block.door.toggle": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "Zaubadisch bnutzd", "subtitles.block.end_portal.spawn": "Endpoadal öffned sich", "subtitles.block.end_portal_frame.fill": "Enderaug eingsetzd", "subtitles.block.eyeblossom.close": "Eyeblossom closes", "subtitles.block.eyeblossom.idle": "Eyeblossom whispers", "subtitles.block.eyeblossom.open": "Eyeblossom opens", "subtitles.block.fence_gate.toggle": "Zaundoa gnarrd", "subtitles.block.fire.ambient": "<PERSON><PERSON>", "subtitles.block.fire.extinguish": "<PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Fireflies buzz", "subtitles.block.frogspawn.hatch": "Tadpole hatches", "subtitles.block.furnace.fire_crackle": "<PERSON><PERSON> kn<PERSON>", "subtitles.block.generic.break": "Blogg zeastöad", "subtitles.block.generic.fall": "Something falls on a block", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.generic.hit": "Blogg brichd", "subtitles.block.generic.place": "Blogg blaziead", "subtitles.block.grindstone.use": "Schleifstoa bnutzd", "subtitles.block.growing_plant.crop": "Pflansche gschtutzt", "subtitles.block.hanging_sign.waxed_interact_fail": "Sign wobbles", "subtitles.block.honey_block.slide": "Honichblogg nundarudschn", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON><PERSON><PERSON> sch<PERSON>ßd", "subtitles.block.iron_trapdoor.open": "<PERSON><PERSON><PERSON><PERSON> effned", "subtitles.block.lava.ambient": "Lova blubbad", "subtitles.block.lava.extinguish": "<PERSON><PERSON>", "subtitles.block.lever.click": "<PERSON><PERSON><PERSON> umglechd", "subtitles.block.note_block.note": "Nodnblogg schbield", "subtitles.block.pale_hanging_moss.idle": "Eerie noise", "subtitles.block.piston.move": "Kolb<PERSON> abeided", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> dropfd", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Loava dropfd in Gessl", "subtitles.block.pointed_dripstone.drip_water": "<PERSON><PERSON> dropfd", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON><PERSON> dropfd in Gessl", "subtitles.block.pointed_dripstone.land": "Stalagdid fälld nunda", "subtitles.block.portal.ambient": "<PERSON><PERSON><PERSON> woabbad", "subtitles.block.portal.travel": "Poadalrauschn lässd nach", "subtitles.block.portal.trigger": "Poadalrauschn nimmd zua", "subtitles.block.pressure_plate.click": "Druggbladdn klickd", "subtitles.block.pumpkin.carve": "<PERSON><PERSON> schnidzd", "subtitles.block.redstone_torch.burnout": "Faggl zischd", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON><PERSON> whooshes", "subtitles.block.respawn_anchor.charge": "Seelnanka oafgladn", "subtitles.block.respawn_anchor.deplete": "Seelnanka veabrachd", "subtitles.block.respawn_anchor.set_spawn": "Seelnanka setzd Wiedaeinstiechspunkd", "subtitles.block.sand.idle": "Sandy sounds", "subtitles.block.sand.wind": "Windy sounds", "subtitles.block.sculk.charge": "Sculk bubbles", "subtitles.block.sculk.spread": "Sculk spreads", "subtitles.block.sculk_catalyst.bloom": "Sculk Catalyst blooms", "subtitles.block.sculk_sensor.clicking": "Sculk<PERSON><PERSON><PERSON><PERSON> b<PERSON>nd zu gliggn", "subtitles.block.sculk_sensor.clicking_stop": "Sculk-<PERSON><PERSON><PERSON> höad oaf zu gliggn", "subtitles.block.sculk_shrieker.shriek": "<PERSON><PERSON><PERSON> shrieks", "subtitles.block.shulker_box.close": "Shulkerkischdl<PERSON> schließd", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> effned", "subtitles.block.sign.waxed_interact_fail": "Sign wobbles", "subtitles.block.smithing_table.use": "Schmiededisch bnutzd", "subtitles.block.smoker.smoke": "Räuchaofn räuchad", "subtitles.block.sniffer_egg.crack": "Sniffer Egg cracks", "subtitles.block.sniffer_egg.hatch": "Sniffer Egg hatches", "subtitles.block.sniffer_egg.plop": "Sniffer plops", "subtitles.block.sponge.absorb": "Sponge sucks", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON> g<PERSON>pfückd", "subtitles.block.trapdoor.close": "Trapdoor closes", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.block.trial_spawner.about_to_spawn_item": "Ominous item prepares", "subtitles.block.trial_spawner.ambient": "Trial Spawner crackles", "subtitles.block.trial_spawner.ambient_charged": "Ominous crackling", "subtitles.block.trial_spawner.ambient_ominous": "Ominous crackling", "subtitles.block.trial_spawner.charge_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.close_shutter": "Trial Spawner closes", "subtitles.block.trial_spawner.detect_player": "Trial Spawner charges up", "subtitles.block.trial_spawner.eject_item": "Trial Spawner ejects items", "subtitles.block.trial_spawner.ominous_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.open_shutter": "Trial Spawner opens", "subtitles.block.trial_spawner.spawn_item": "Ominous item drops", "subtitles.block.trial_spawner.spawn_item_begin": "Ominous item appears", "subtitles.block.trial_spawner.spawn_mob": "Trial Spawner spawns a mob", "subtitles.block.tripwire.attach": "Stolbadrahd gschbannd", "subtitles.block.tripwire.click": "Stolbadrahd ausglösd", "subtitles.block.tripwire.detach": "Stolbadrahd endschbannd", "subtitles.block.vault.activate": "Vault ignites", "subtitles.block.vault.ambient": "Vault crackles", "subtitles.block.vault.close_shutter": "Vault closes", "subtitles.block.vault.deactivate": "<PERSON>ault extinguishes", "subtitles.block.vault.eject_item": "Vault ejects item", "subtitles.block.vault.insert_item": "<PERSON><PERSON> unlocks", "subtitles.block.vault.insert_item_fail": "Vault rejects item", "subtitles.block.vault.open_shutter": "Vault opens", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON> rejects player", "subtitles.block.water.ambient": "<PERSON><PERSON> flie<PERSON>", "subtitles.block.wet_sponge.dries": "Sponge dries", "subtitles.chiseled_bookshelf.insert": "Book placed", "subtitles.chiseled_bookshelf.insert_enchanted": "Enchanted Book placed", "subtitles.chiseled_bookshelf.take": "Book taken", "subtitles.chiseled_bookshelf.take_enchanted": "Enchanted Book taken", "subtitles.enchant.thorns.hit": "Doanen stechn", "subtitles.entity.allay.ambient_with_item": "Allay seeks", "subtitles.entity.allay.ambient_without_item": "Allay yearns", "subtitles.entity.allay.death": "<PERSON><PERSON> dies", "subtitles.entity.allay.hurt": "Allay hurts", "subtitles.entity.allay.item_given": "<PERSON>ay chortles", "subtitles.entity.allay.item_taken": "Allay allays", "subtitles.entity.allay.item_thrown": "Allay tosses", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> grunts", "subtitles.entity.armadillo.brush": "<PERSON>ute is brushed off", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON> eats", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.armadillo.hurt_reduced": "Armadillo shields itself", "subtitles.entity.armadillo.land": "Armadillo lands", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armadillo.roll": "Armadillo rolls up", "subtitles.entity.armadillo.scute_drop": "Armadillo sheds scute", "subtitles.entity.armadillo.unroll_finish": "Armadillo unrolls", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armor_stand.fall": "Rüsdungssdända is gfalln", "subtitles.entity.arrow.hit": "Pfail driffd", "subtitles.entity.arrow.hit_player": "Schbiela gdroffn", "subtitles.entity.arrow.shoot": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.entity.axolotl.attack": "<PERSON><PERSON><PERSON><PERSON> greifd oan", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON><PERSON> stiabd", "subtitles.entity.axolotl.hurt": "Ax<PERSON><PERSON> nimmd an <PERSON>hoodn", "subtitles.entity.axolotl.idle_air": "Axolodl zwidschad", "subtitles.entity.axolotl.idle_water": "Axolotl chirps", "subtitles.entity.axolotl.splash": "Axolodl plätschad", "subtitles.entity.axolotl.swim": "Ax<PERSON>dl schwimmd", "subtitles.entity.bat.ambient": "Fledamaus kraischd", "subtitles.entity.bat.death": "<PERSON>led<PERSON><PERSON> stia<PERSON>d", "subtitles.entity.bat.hurt": "<PERSON>led<PERSON><PERSON> nimmd an <PERSON>n", "subtitles.entity.bat.takeoff": "<PERSON>led<PERSON><PERSON> fliechd los", "subtitles.entity.bee.ambient": "Biena brummd", "subtitles.entity.bee.death": "Biena stiabd", "subtitles.entity.bee.hurt": "Biena nimmd an Schoodn", "subtitles.entity.bee.loop": "Bee buzzes", "subtitles.entity.bee.loop_aggressive": "Biena brummd veaägard", "subtitles.entity.bee.pollinate": "Biena brummd froh", "subtitles.entity.bee.sting": "Biena schdichd", "subtitles.entity.blaze.ambient": "<PERSON><PERSON> admed", "subtitles.entity.blaze.burn": "<PERSON><PERSON> kn<PERSON>", "subtitles.entity.blaze.death": "<PERSON><PERSON> stia<PERSON>d", "subtitles.entity.blaze.hurt": "<PERSON><PERSON> nimmd an <PERSON>hoodn", "subtitles.entity.blaze.shoot": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.boat.paddle_land": "Rudan", "subtitles.entity.boat.paddle_water": "Rowing", "subtitles.entity.bogged.ambient": "Bogged rattles", "subtitles.entity.bogged.death": "Bogged dies", "subtitles.entity.bogged.hurt": "Bogged hurts", "subtitles.entity.breeze.charge": "Breeze charges", "subtitles.entity.breeze.death": "<PERSON><PERSON> dies", "subtitles.entity.breeze.deflect": "<PERSON><PERSON> deflects", "subtitles.entity.breeze.hurt": "Breeze hurts", "subtitles.entity.breeze.idle_air": "Breeze flies", "subtitles.entity.breeze.idle_ground": "Breeze whirs", "subtitles.entity.breeze.inhale": "Breeze inhales", "subtitles.entity.breeze.jump": "<PERSON><PERSON> jumps", "subtitles.entity.breeze.land": "Breeze lands", "subtitles.entity.breeze.shoot": "Breeze shoots", "subtitles.entity.breeze.slide": "Breeze slides", "subtitles.entity.breeze.whirl": "Breeze whirls", "subtitles.entity.breeze.wind_burst": "Wind Charge bursts", "subtitles.entity.camel.ambient": "Camel grunts", "subtitles.entity.camel.dash": "Camel yeets", "subtitles.entity.camel.dash_ready": "Camel recovers", "subtitles.entity.camel.death": "Camel dies", "subtitles.entity.camel.eat": "Camel eats", "subtitles.entity.camel.hurt": "Camel hurts", "subtitles.entity.camel.saddle": "Saddle equips", "subtitles.entity.camel.sit": "Camel sits down", "subtitles.entity.camel.stand": "Camel stands up", "subtitles.entity.camel.step": "Camel steps", "subtitles.entity.camel.step_sand": "Camel sands", "subtitles.entity.cat.ambient": "Koatzn miaud", "subtitles.entity.cat.beg_for_food": "Ka<PERSON><PERSON> battlt", "subtitles.entity.cat.death": "Koatzn stiabd", "subtitles.entity.cat.eat": "Kadz isst", "subtitles.entity.cat.hiss": "<PERSON><PERSON><PERSON> faucht", "subtitles.entity.cat.hurt": "Koatzn nimmd an Schoodn", "subtitles.entity.cat.purr": "Koatzn schnuard", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.chicken.egg": "Hüech legd a Gaggeli", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON> nimmd an <PERSON>", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cod.flop": "<PERSON><PERSON><PERSON><PERSON><PERSON> bladschd", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmd an <PERSON>", "subtitles.entity.cow.ambient": "<PERSON><PERSON> muhd", "subtitles.entity.cow.death": "<PERSON><PERSON>", "subtitles.entity.cow.hurt": "<PERSON><PERSON> nimmd an <PERSON>", "subtitles.entity.cow.milk": "<PERSON>u wiad gmolkn", "subtitles.entity.creaking.activate": "Creaking watches", "subtitles.entity.creaking.ambient": "Creaking creaks", "subtitles.entity.creaking.attack": "Creaking attacks", "subtitles.entity.creaking.deactivate": "Creaking calms", "subtitles.entity.creaking.death": "Creaking crumbles", "subtitles.entity.creaking.freeze": "Creaking stops", "subtitles.entity.creaking.spawn": "Creaking manifests", "subtitles.entity.creaking.sway": "Creaking is hit", "subtitles.entity.creaking.twitch": "Creaking twitches", "subtitles.entity.creaking.unfreeze": "Creaking moves", "subtitles.entity.creeper.death": "C<PERSON>per stiabd", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> nimmd an <PERSON>hoodn", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> zischd", "subtitles.entity.dolphin.ambient": "Delfin zwischad", "subtitles.entity.dolphin.ambient_water": "Delfin pfaifd", "subtitles.entity.dolphin.attack": "Delfin greift aa", "subtitles.entity.dolphin.death": "Delfin stiabd", "subtitles.entity.dolphin.eat": "Delfin frissd", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON> nimmd an Schoodn", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON> springd", "subtitles.entity.dolphin.play": "Del<PERSON> schbield", "subtitles.entity.dolphin.splash": "Del<PERSON> blatschd", "subtitles.entity.dolphin.swim": "Delfin schwimmd", "subtitles.entity.donkey.ambient": "Esl iahd", "subtitles.entity.donkey.angry": "Esl wiehad", "subtitles.entity.donkey.chest": "<PERSON><PERSON><PERSON> bebackd", "subtitles.entity.donkey.death": "Esl stiabd", "subtitles.entity.donkey.eat": "Esl frissd", "subtitles.entity.donkey.hurt": "Esl nimmd an Schoodn", "subtitles.entity.donkey.jump": "<PERSON><PERSON> jumps", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.drowned.ambient_water": "Drowned gurgles", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmd an Schoodn", "subtitles.entity.drowned.shoot": "<PERSON>rdrunggena wiafd an Draizagg", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>d", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON><PERSON><PERSON> schwimmd", "subtitles.entity.egg.throw": "<PERSON><PERSON><PERSON><PERSON> fliagt", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON><PERSON><PERSON> W<PERSON>chda stöand", "subtitles.entity.elder_guardian.ambient_land": "G<PERSON>ßa Wächda zabbld", "subtitles.entity.elder_guardian.curse": "<PERSON><PERSON><PERSON><PERSON>da veafluchd", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON><PERSON> stia<PERSON>d", "subtitles.entity.elder_guardian.flop": "<PERSON><PERSON><PERSON><PERSON> blaschd", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON><PERSON> W<PERSON>chda nimmd an Schoodn", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.ender_dragon.flap": "Dr<PERSON>n fladdad", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON> fauchd", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON>n nimmd an <PERSON>n", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.ender_eye.death": "<PERSON><PERSON><PERSON> p<PERSON>zd", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON><PERSON> gwoafn", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON><PERSON><PERSON> fliechd", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> woobd", "subtitles.entity.enderman.death": "<PERSON><PERSON> stia<PERSON>d", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> nimmd an <PERSON>hoodn", "subtitles.entity.enderman.scream": "<PERSON><PERSON> screams", "subtitles.entity.enderman.stare": "<PERSON><PERSON> schraid", "subtitles.entity.enderman.teleport": "Enderman telepoatiad", "subtitles.entity.endermite.ambient": "<PERSON><PERSON><PERSON><PERSON> grabbld", "subtitles.entity.endermite.death": "<PERSON><PERSON><PERSON><PERSON>d", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an <PERSON>hoodn", "subtitles.entity.evoker.ambient": "<PERSON><PERSON> muameld", "subtitles.entity.evoker.cast_spell": "Mogie zaubad", "subtitles.entity.evoker.celebrate": "<PERSON><PERSON> jubld", "subtitles.entity.evoker.death": "<PERSON><PERSON> s<PERSON>d", "subtitles.entity.evoker.hurt": "<PERSON><PERSON> nimmd an Schoodn", "subtitles.entity.evoker.prepare_attack": "Mogie braided Angriff voa", "subtitles.entity.evoker.prepare_summon": "Mogie braided Bschwörung voa", "subtitles.entity.evoker.prepare_wololo": "Mogie braided Zauba voa", "subtitles.entity.evoker_fangs.attack": "Fangzoahn schnabbd zua", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.firework_rocket.blast": "Feuaweak exblodiad", "subtitles.entity.firework_rocket.launch": "Feuaweak zünded", "subtitles.entity.firework_rocket.twinkle": "Feuaweak funkld", "subtitles.entity.fish.swim": "Splashes", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON><PERSON><PERSON> blaschd", "subtitles.entity.fishing_bobber.throw": "Schwimma ausgwoafn", "subtitles.entity.fox.aggro": "Fuchs erzürnd", "subtitles.entity.fox.ambient": "Fuchs winsld", "subtitles.entity.fox.bite": "<PERSON><PERSON> boaßd", "subtitles.entity.fox.death": "Fuchs stiabd", "subtitles.entity.fox.eat": "Fuchs frissd", "subtitles.entity.fox.hurt": "Fuchs nimmd an Schoodn", "subtitles.entity.fox.screech": "Fuchs kraischd", "subtitles.entity.fox.sleep": "Fuchs schnaichd", "subtitles.entity.fox.sniff": "<PERSON><PERSON> schnubbad", "subtitles.entity.fox.spit": "Fuchs schbuckd aus", "subtitles.entity.fox.teleport": "Fuchs teleboadiead", "subtitles.entity.frog.ambient": "Frog croaks", "subtitles.entity.frog.death": "<PERSON> dies", "subtitles.entity.frog.eat": "Frog eats", "subtitles.entity.frog.hurt": "Frog hurts", "subtitles.entity.frog.lay_spawn": "Frog lays spawn", "subtitles.entity.frog.long_jump": "Frog jumps", "subtitles.entity.generic.big_fall": "Something fell", "subtitles.entity.generic.burn": "Brenn'n", "subtitles.entity.generic.death": "Steabn", "subtitles.entity.generic.drink": "Schlüafn", "subtitles.entity.generic.eat": "Essn", "subtitles.entity.generic.explode": "Exblosion", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON>", "subtitles.entity.generic.hurt": "<PERSON><PERSON> nimmd an <PERSON>n", "subtitles.entity.generic.small_fall": "<PERSON><PERSON> stolbad", "subtitles.entity.generic.splash": "Bladschn", "subtitles.entity.generic.swim": "Schwimmn", "subtitles.entity.generic.wind_burst": "Wind Charge bursts", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> nimmd an <PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> gfülld", "subtitles.entity.glow_item_frame.break": "Leuchdrahmn zeabrichd", "subtitles.entity.glow_item_frame.place": "<PERSON><PERSON><PERSON><PERSON><PERSON> bla<PERSON><PERSON>", "subtitles.entity.glow_item_frame.remove_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> gleerd", "subtitles.entity.glow_item_frame.rotate_item": "<PERSON><PERSON><PERSON><PERSON><PERSON> gliggd", "subtitles.entity.glow_squid.ambient": "Leuchddindnfisch schwimmd", "subtitles.entity.glow_squid.death": "Leuchddindnfisch stiabd", "subtitles.entity.glow_squid.hurt": "Leuchddindnfisch nimmd an Schodn", "subtitles.entity.glow_squid.squirt": "Leuchddindnfisch veaspritzd Dinde", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON><PERSON> meckad", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON>n frissd", "subtitles.entity.goat.horn_break": "<PERSON><PERSON> breaks off", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON>n nimmd an Schoodn", "subtitles.entity.goat.long_jump": "Z<PERSON><PERSON>n schbringd", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON>n wiad gmolkn", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON><PERSON>n schdampfd", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON><PERSON> ram<PERSON>d", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON><PERSON><PERSON> br<PERSON>", "subtitles.entity.goat.step": "<PERSON><PERSON><PERSON><PERSON> laafd", "subtitles.entity.guardian.ambient": "Wächda stöand", "subtitles.entity.guardian.ambient_land": "Wächda zabbld", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.guardian.flop": "Wächda bladschd", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an Schoodn", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": "Hoglin gruanzd", "subtitles.entity.hoglin.angry": "Hoglin gruanzd ve<PERSON>ärgrt", "subtitles.entity.hoglin.attack": "<PERSON><PERSON>n greift aa", "subtitles.entity.hoglin.converted_to_zombified": "Ho<PERSON>n wiad zombifiziead", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON>n nimmd an <PERSON>n", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> waichd <PERSON>gg", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> laafd", "subtitles.entity.horse.ambient": "<PERSON><PERSON> wiehad", "subtitles.entity.horse.angry": "Horse neighs", "subtitles.entity.horse.armor": "Pfeddrüsdung anglegd", "subtitles.entity.horse.breathe": "<PERSON><PERSON> schna<PERSON>d", "subtitles.entity.horse.death": "<PERSON><PERSON> stia<PERSON>d", "subtitles.entity.horse.eat": "Gaul frissd", "subtitles.entity.horse.gallop": "<PERSON><PERSON> galobbie<PERSON>", "subtitles.entity.horse.hurt": "<PERSON><PERSON> nimmd an <PERSON>n", "subtitles.entity.horse.jump": "<PERSON><PERSON> s<PERSON>d", "subtitles.entity.horse.saddle": "<PERSON><PERSON> g<PERSON>d", "subtitles.entity.husk.ambient": "Wüsdnzombie stöahnd", "subtitles.entity.husk.converted_to_zombie": "Wüsdnzombie wiad zum Zombie", "subtitles.entity.husk.death": "Wüsdnzombie stiabd", "subtitles.entity.husk.hurt": "Wüsdnzombie nimmd an Schoodn", "subtitles.entity.illusioner.ambient": "Illusion<PERSON>d muamld", "subtitles.entity.illusioner.cast_spell": "Illusionisd zaubad", "subtitles.entity.illusioner.death": "Il<PERSON><PERSON><PERSON> stiabd", "subtitles.entity.illusioner.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an <PERSON>n", "subtitles.entity.illusioner.mirror_move": "Illusionisd projeziad an Dobblgänga", "subtitles.entity.illusioner.prepare_blindness": "Illusionisd braided <PERSON><PERSON><PERSON> voa", "subtitles.entity.illusioner.prepare_mirror": "Illusionisd braided Projegtion voa", "subtitles.entity.iron_golem.attack": "<PERSON><PERSON><PERSON><PERSON> graifd an", "subtitles.entity.iron_golem.damage": "Aasngolm wiad bschädigd", "subtitles.entity.iron_golem.death": "Aasgolm stiabd", "subtitles.entity.iron_golem.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an <PERSON>n", "subtitles.entity.iron_golem.repair": "Aasgol<PERSON> rebariead", "subtitles.entity.item.break": "Gechnschdand zeabrichd", "subtitles.entity.item.pickup": "Gechnschdand aufghobn", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON><PERSON> gleerd", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON><PERSON> gliggd", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> broken", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> tied", "subtitles.entity.lightning_bolt.impact": "Blid<PERSON>inschlach", "subtitles.entity.lightning_bolt.thunder": "<PERSON> grolld", "subtitles.entity.llama.ambient": "<PERSON><PERSON> blögd", "subtitles.entity.llama.angry": "<PERSON><PERSON> blögd ve<PERSON>ä<PERSON>", "subtitles.entity.llama.chest": "<PERSON><PERSON> bebackd", "subtitles.entity.llama.death": "<PERSON><PERSON> s<PERSON>d", "subtitles.entity.llama.eat": "<PERSON><PERSON> frissd", "subtitles.entity.llama.hurt": "<PERSON><PERSON> nimmd an Schoodn", "subtitles.entity.llama.spit": "<PERSON><PERSON> schbuckd", "subtitles.entity.llama.step": "<PERSON><PERSON> la<PERSON>d", "subtitles.entity.llama.swag": "<PERSON><PERSON>", "subtitles.entity.magma_cube.death": "Mochmawüffel stiabd", "subtitles.entity.magma_cube.hurt": "Mochmawüffel nimmd an Schoodn", "subtitles.entity.magma_cube.squish": "Mochmawüffel schwoabbld", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> jangles", "subtitles.entity.minecart.inside_underwater": "Minecart jangles underwater", "subtitles.entity.minecart.riding": "Lore rolld", "subtitles.entity.mooshroom.convert": "Mooshroom veawoandld sich", "subtitles.entity.mooshroom.eat": "Mooshroom frissd", "subtitles.entity.mooshroom.milk": "Mooshroom wiad gmolkn", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom wiad seldsam gmolkn", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON>d", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON> wiehad", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON> bebackd", "subtitles.entity.mule.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON> frissd", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON> nimmd an <PERSON>hoodn", "subtitles.entity.mule.jump": "<PERSON><PERSON> jumps", "subtitles.entity.painting.break": "Gmälde endfeand", "subtitles.entity.painting.place": "Gmälde blaziead", "subtitles.entity.panda.aggressive_ambient": "<PERSON><PERSON> grolld", "subtitles.entity.panda.ambient": "<PERSON><PERSON> he<PERSON>", "subtitles.entity.panda.bite": "<PERSON><PERSON> b<PERSON>", "subtitles.entity.panda.cant_breed": "<PERSON><PERSON> blökd", "subtitles.entity.panda.death": "<PERSON><PERSON> s<PERSON>d", "subtitles.entity.panda.eat": "<PERSON><PERSON> frissd", "subtitles.entity.panda.hurt": "<PERSON><PERSON> nimmd an <PERSON>n", "subtitles.entity.panda.pre_sneeze": "<PERSON><PERSON> ve<PERSON><PERSON><PERSON>", "subtitles.entity.panda.sneeze": "<PERSON><PERSON> ni<PERSON>d", "subtitles.entity.panda.step": "<PERSON><PERSON> la<PERSON>d", "subtitles.entity.panda.worried_ambient": "<PERSON><PERSON> wim<PERSON>", "subtitles.entity.parrot.ambient": "<PERSON><PERSON><PERSON> sprichd", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.eats": "<PERSON><PERSON><PERSON> frissd", "subtitles.entity.parrot.fly": "<PERSON><PERSON><PERSON> fladdad", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON> nimmd an <PERSON>n", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON><PERSON> admed", "subtitles.entity.parrot.imitate.bogged": "Parrot rattles", "subtitles.entity.parrot.imitate.breeze": "Parrot whirs", "subtitles.entity.parrot.imitate.creaking": "Parrot creaks", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON>i stöhnd", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON><PERSON> grabbld", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON> muameld", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON> moans", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON>i stöhnd", "subtitles.entity.parrot.imitate.illusioner": "Parrot murmurs", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON><PERSON> schwab<PERSON>ld", "subtitles.entity.parrot.imitate.phantom": "Babagei kraischd", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON><PERSON> schna<PERSON>", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON> snorts", "subtitles.entity.parrot.imitate.pillager": "Parrot murmurs", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.silverfish": "Parrot hisses", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.slime": "Parrot squishes", "subtitles.entity.parrot.imitate.spider": "Parrot hisses", "subtitles.entity.parrot.imitate.stray": "Parrot rattles", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON><PERSON> blachd", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON> muameld", "subtitles.entity.parrot.imitate.warden": "Parrot whines", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON>i <PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "Parrot rattles", "subtitles.entity.parrot.imitate.zoglin": "<PERSON>rro<PERSON> growls", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON> groans", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON> groans", "subtitles.entity.phantom.ambient": "Phandom kraischd", "subtitles.entity.phantom.bite": "<PERSON><PERSON><PERSON> boa<PERSON>d", "subtitles.entity.phantom.death": "<PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.phantom.flap": "Phandom fladdad", "subtitles.entity.phantom.hurt": "<PERSON><PERSON><PERSON> nimmd an <PERSON>n", "subtitles.entity.phantom.swoop": "Phandom stößd hrab", "subtitles.entity.pig.ambient": "Schwein grunzd", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an <PERSON>hoodn", "subtitles.entity.pig.saddle": "Saddle equips", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> schna<PERSON>d", "subtitles.entity.piglin.angry": "<PERSON><PERSON> schnaubd ve<PERSON>", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> feuard", "subtitles.entity.piglin.converted_to_zombified": "<PERSON>lin wiad zombifiziead", "subtitles.entity.piglin.death": "<PERSON><PERSON> stia<PERSON>d", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> nimmd an Schoodn", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> schna<PERSON>d ne<PERSON>", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> waichd z<PERSON><PERSON>gg", "subtitles.entity.piglin.step": "<PERSON><PERSON> laafd", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON>-<PERSON><PERSON> s<PERSON>", "subtitles.entity.piglin_brute.angry": "Piglin-<PERSON><PERSON> sch<PERSON>", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin-<PERSON><PERSON> wiad zombifiziead", "subtitles.entity.piglin_brute.death": "<PERSON>lin-<PERSON><PERSON>", "subtitles.entity.piglin_brute.hurt": "Piglin-<PERSON><PERSON> nimmd an <PERSON>", "subtitles.entity.piglin_brute.step": "<PERSON>lin-<PERSON><PERSON> la<PERSON>", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> muameld", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmd an Schoodn", "subtitles.entity.player.attack.crit": "Volltreffa", "subtitles.entity.player.attack.knockback": "Rüggstoßangriff", "subtitles.entity.player.attack.strong": "St<PERSON><PERSON>", "subtitles.entity.player.attack.sweep": "Gschmackesangriff", "subtitles.entity.player.attack.weak": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.player.freeze_hurt": "Schbiela friead", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an <PERSON>hoodn", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "subtitles.entity.player.hurt_on_fire": "Schbiela brennd", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON><PERSON> bi<PERSON>ld", "subtitles.entity.player.teleport": "Player teleports", "subtitles.entity.polar_bear.ambient": "Eisbeä gnuad", "subtitles.entity.polar_bear.ambient_baby": "Eisbeä brummd", "subtitles.entity.polar_bear.death": "Eisbeä stiabd", "subtitles.entity.polar_bear.hurt": "Eisbeä nimmd an Schoodn", "subtitles.entity.polar_bear.warning": "Eisbeä br<PERSON>", "subtitles.entity.potion.splash": "Buddel zeabrichd", "subtitles.entity.potion.throw": "Buddel gwoafn", "subtitles.entity.puffer_fish.blow_out": "Kuglfisch schrumpfd", "subtitles.entity.puffer_fish.blow_up": "Kuglfisch bläsd sich auf", "subtitles.entity.puffer_fish.death": "Kuglfisch stiabd", "subtitles.entity.puffer_fish.flop": "Kuglfisch bladschd", "subtitles.entity.puffer_fish.hurt": "Kuglfisch nimmd an Schoodn", "subtitles.entity.puffer_fish.sting": "Kuglfisch stiabd", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON><PERSON> fiebd", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON><PERSON> groafd aan", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an Schoodn", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON><PERSON> hobbld", "subtitles.entity.ravager.ambient": "Vawüsda grunzd", "subtitles.entity.ravager.attack": "Vawüsda boaßd", "subtitles.entity.ravager.celebrate": "Vawüsda jubld", "subtitles.entity.ravager.death": "Vawüsda stiabd", "subtitles.entity.ravager.hurt": "Vawüsda nimmd an Schoodn", "subtitles.entity.ravager.roar": "Vawüsda brülld", "subtitles.entity.ravager.step": "Vawüsda laafd", "subtitles.entity.ravager.stunned": "Vawüsda <PERSON>", "subtitles.entity.salmon.death": "<PERSON><PERSON> stiabd", "subtitles.entity.salmon.flop": "<PERSON><PERSON> bladschd", "subtitles.entity.salmon.hurt": "<PERSON><PERSON> nimmd an <PERSON><PERSON>n", "subtitles.entity.sheep.ambient": "Hämml määhd", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON><PERSON> stia<PERSON>d", "subtitles.entity.sheep.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an Schoodn", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> closes", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> nimmd an <PERSON>n", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> opens", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> schi<PERSON>d", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> teleboadiead", "subtitles.entity.shulker_bullet.hit": "Shulkagschoss explodiad", "subtitles.entity.shulker_bullet.hurt": "Shulkagschoss zeabrichd", "subtitles.entity.silverfish.ambient": "Silbe<PERSON>sch zischd", "subtitles.entity.silverfish.death": "Silbefisch stiabd", "subtitles.entity.silverfish.hurt": "Silbefisch nimmd an Schoodn", "subtitles.entity.skeleton.ambient": "<PERSON><PERSON><PERSON> glabbad", "subtitles.entity.skeleton.converted_to_stray": "<PERSON><PERSON><PERSON> veawa<PERSON>ld sich zum <PERSON>dra", "subtitles.entity.skeleton.death": "<PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON> nimmd an <PERSON>hoodn", "subtitles.entity.skeleton.shoot": "<PERSON><PERSON><PERSON> schi<PERSON>d", "subtitles.entity.skeleton_horse.ambient": "Sgeleddgaul schraid", "subtitles.entity.skeleton_horse.death": "Sgeleddgaul stiabd", "subtitles.entity.skeleton_horse.hurt": "Sgeleddgaul nimmd an Schoodn", "subtitles.entity.skeleton_horse.jump_water": "Skeleton Horse jumps", "subtitles.entity.skeleton_horse.swim": "Sgeleddgaul schwimmd", "subtitles.entity.slime.attack": "<PERSON><PERSON><PERSON><PERSON> groafd aan", "subtitles.entity.slime.death": "Schl<PERSON><PERSON> stia<PERSON>d", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an Schoodn", "subtitles.entity.slime.squish": "Schloim schwoabbld", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.sniffer.digging": "Sniffer digs", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON> stands up", "subtitles.entity.sniffer.drop_seed": "Sniffer drops seed", "subtitles.entity.sniffer.eat": "Sniffer eats", "subtitles.entity.sniffer.egg_crack": "Sniffer Egg cracks", "subtitles.entity.sniffer.egg_hatch": "Sniffer Egg hatches", "subtitles.entity.sniffer.happy": "Sniffer delights", "subtitles.entity.sniffer.hurt": "Sniffer hurts", "subtitles.entity.sniffer.idle": "Sniffer grunts", "subtitles.entity.sniffer.scenting": "Sniffer scents", "subtitles.entity.sniffer.searching": "Sniffer searches", "subtitles.entity.sniffer.sniffing": "Sniffer sniffs", "subtitles.entity.sniffer.step": "Sniffer steps", "subtitles.entity.snow_golem.death": "Schnähgolm stiabd", "subtitles.entity.snow_golem.hurt": "Sc<PERSON>ähgolm nimmd an Schoodn", "subtitles.entity.snowball.throw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fliechd", "subtitles.entity.spider.ambient": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>d", "subtitles.entity.spider.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.spider.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an <PERSON>n", "subtitles.entity.squid.ambient": "Dindnfisch schwimmd", "subtitles.entity.squid.death": "Dindnfisch stiabd", "subtitles.entity.squid.hurt": "Dindnfisch nimmd an Schoodn", "subtitles.entity.squid.squirt": "Dindnfisch veasp<PERSON>d Dinde", "subtitles.entity.stray.ambient": "<PERSON><PERSON><PERSON><PERSON> glabba<PERSON>", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an <PERSON>", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON><PERSON> stia<PERSON>d", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON><PERSON> frissd", "subtitles.entity.strider.happy": "<PERSON><PERSON><PERSON><PERSON> dr<PERSON>", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an <PERSON>hoodn", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON><PERSON> zia<PERSON>d", "subtitles.entity.strider.retreat": "Schreida waichd <PERSON>ügg", "subtitles.entity.tadpole.death": "<PERSON><PERSON> dies", "subtitles.entity.tadpole.flop": "Tadpole flops", "subtitles.entity.tadpole.grow_up": "Tadpole grows up", "subtitles.entity.tadpole.hurt": "Tadpole hurts", "subtitles.entity.tnt.primed": "DND zischd", "subtitles.entity.tropical_fish.death": "Drobn<PERSON><PERSON> s<PERSON>d", "subtitles.entity.tropical_fish.flop": "Drobnfisch zabbld", "subtitles.entity.tropical_fish.hurt": "Drobnfisch nimmd an Schoodn", "subtitles.entity.turtle.ambient_land": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fiebd", "subtitles.entity.turtle.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.turtle.death_baby": "Schildgrödnjungs stiabd", "subtitles.entity.turtle.egg_break": "Schildgrödnei zeabrichd", "subtitles.entity.turtle.egg_crack": "Schildgrödnei brichd", "subtitles.entity.turtle.egg_hatch": "Schildgrödnjungs schlübfd", "subtitles.entity.turtle.hurt": "Schildgröde nimmd an Schoodn", "subtitles.entity.turtle.hurt_baby": "Schildgrödnjungs nimmd an Schoodn", "subtitles.entity.turtle.lay_egg": "Schildgrö<PERSON> lecht Ei", "subtitles.entity.turtle.shamble": "Schildgr<PERSON><PERSON> watschld", "subtitles.entity.turtle.shamble_baby": "Schildgrödenjungs watschld", "subtitles.entity.turtle.swim": "Schildgrö<PERSON> schwimmd", "subtitles.entity.vex.ambient": "Gschbensd blachd", "subtitles.entity.vex.charge": "Gschbensd schraid", "subtitles.entity.vex.death": "Gschbensd stiabd", "subtitles.entity.vex.hurt": "Gschbensd nimmd an Schoodn", "subtitles.entity.villager.ambient": "Doefbewohne muameld", "subtitles.entity.villager.celebrate": "Doefbewohne jubld", "subtitles.entity.villager.death": "Doefbewohne stiabd", "subtitles.entity.villager.hurt": "Doefbewohne nimmd an Schoodn", "subtitles.entity.villager.no": "Doefbewohne lehnd oab", "subtitles.entity.villager.trade": "Doefbewohne hoandld", "subtitles.entity.villager.work_armorer": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_cartographer": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_cleric": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "subtitles.entity.villager.work_farmer": "<PERSON><PERSON> o<PERSON>", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_fletcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON> o<PERSON>", "subtitles.entity.villager.work_librarian": "Bibliotheka o<PERSON>", "subtitles.entity.villager.work_mason": "<PERSON><PERSON>", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "subtitles.entity.villager.work_toolsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "subtitles.entity.villager.yes": "Doefbewohne nimmd oan", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON> mua<PERSON>d", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON> j<PERSON>", "subtitles.entity.vindicator.death": "<PERSON><PERSON>", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON> nimmd an <PERSON>", "subtitles.entity.wandering_trader.ambient": "Fahrnda Händla muameld", "subtitles.entity.wandering_trader.death": "Fahrnda Händla s<PERSON>d", "subtitles.entity.wandering_trader.disappeared": "Fahrnda Händla veaschwinded", "subtitles.entity.wandering_trader.drink_milk": "Fahrnda Händla dringd <PERSON>i", "subtitles.entity.wandering_trader.drink_potion": "Fahrnda Händla dringd <PERSON>", "subtitles.entity.wandering_trader.hurt": "Fahrnda Händla nimmd an Schoodn", "subtitles.entity.wandering_trader.no": "Fahrnda Händla lehnd oab", "subtitles.entity.wandering_trader.reappeared": "Fahrnda Händla <PERSON>", "subtitles.entity.wandering_trader.trade": "Fahrnda Händla ho<PERSON>ld", "subtitles.entity.wandering_trader.yes": "Fahrnda Händla stimmd zua", "subtitles.entity.warden.agitated": "Warden groans angrily", "subtitles.entity.warden.ambient": "Warden whines", "subtitles.entity.warden.angry": "Warden rages", "subtitles.entity.warden.attack_impact": "Warden lands hit", "subtitles.entity.warden.death": "Warden dies", "subtitles.entity.warden.dig": "Warden digs", "subtitles.entity.warden.emerge": "Warden emerges", "subtitles.entity.warden.heartbeat": "Warden's heart beats", "subtitles.entity.warden.hurt": "Warden hurts", "subtitles.entity.warden.listening": "Warden takes notice", "subtitles.entity.warden.listening_angry": "Warden takes notice angrily", "subtitles.entity.warden.nearby_close": "Warden approaches", "subtitles.entity.warden.nearby_closer": "Warden advances", "subtitles.entity.warden.nearby_closest": "<PERSON> draws close", "subtitles.entity.warden.roar": "Warden roars", "subtitles.entity.warden.sniff": "Warden sniffs", "subtitles.entity.warden.sonic_boom": "Warden booms", "subtitles.entity.warden.sonic_charge": "Warden charges", "subtitles.entity.warden.step": "Warden steps", "subtitles.entity.warden.tendril_clicks": "Warden's tendrils click", "subtitles.entity.wind_charge.throw": "Wind Charge flies", "subtitles.entity.wind_charge.wind_burst": "Wind Charge bursts", "subtitles.entity.witch.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>ad", "subtitles.entity.witch.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON><PERSON><PERSON> drinkd", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimmd an <PERSON>", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON><PERSON><PERSON> wiafd", "subtitles.entity.wither.ambient": "<PERSON><PERSON>", "subtitles.entity.wither.death": "<PERSON>er stia<PERSON>d", "subtitles.entity.wither.hurt": "Wither nimmd an <PERSON>n", "subtitles.entity.wither.shoot": "Wither groafd oan", "subtitles.entity.wither.spawn": "Wither end<PERSON>ld", "subtitles.entity.wither_skeleton.ambient": "Withersgeledd glabbad", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.wither_skeleton.hurt": "<PERSON><PERSON><PERSON><PERSON> nimmd an <PERSON>hoodn", "subtitles.entity.wolf.ambient": "<PERSON> he<PERSON>", "subtitles.entity.wolf.bark": "Wolf barks", "subtitles.entity.wolf.death": "<PERSON> s<PERSON>d", "subtitles.entity.wolf.growl": "<PERSON> gnuad", "subtitles.entity.wolf.hurt": "<PERSON> nimmd an <PERSON>n", "subtitles.entity.wolf.pant": "Wolf pants", "subtitles.entity.wolf.shake": "<PERSON> sch<PERSON> sich", "subtitles.entity.wolf.whine": "Wolf whines", "subtitles.entity.zoglin.ambient": "Zoglin gruanzd", "subtitles.entity.zoglin.angry": "Zoglin gruanzd veaägard", "subtitles.entity.zoglin.attack": "Zoglin greift aa", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> s<PERSON>d", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> nimmd an Schoodn", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> laafd", "subtitles.entity.zombie.ambient": "<PERSON> stöahnd", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON> wiad grütteld", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON> z<PERSON>d", "subtitles.entity.zombie.converted_to_drowned": "Zombie wiad zum Erdrunggenen", "subtitles.entity.zombie.death": "Zombie stiabd", "subtitles.entity.zombie.destroy_egg": "Schildgrödnei zeatredn", "subtitles.entity.zombie.hurt": "Zombie nimmd an Schoodn", "subtitles.entity.zombie.infect": "Zombie infiziad", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON><PERSON> schraid", "subtitles.entity.zombie_horse.death": "Zombiegaul stiabd", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON>l nimmd an Schoodn", "subtitles.entity.zombie_villager.ambient": "Zombiedoefbewohne stöahnd", "subtitles.entity.zombie_villager.converted": "<PERSON>dorfb<PERSON>hn<PERSON> gheild", "subtitles.entity.zombie_villager.cure": "Zombiedorfbwohna z<PERSON>d", "subtitles.entity.zombie_villager.death": "Zombiedoefbewohne stiabd", "subtitles.entity.zombie_villager.hurt": "Zombiedoefbewohne nimmd an Schoodn", "subtitles.entity.zombified_piglin.ambient": "Zombifizieada Piglin grunzd", "subtitles.entity.zombified_piglin.angry": "Zombifizieada Piglin grunzd veaägard", "subtitles.entity.zombified_piglin.death": "Zombifizieada <PERSON> s<PERSON>d", "subtitles.entity.zombified_piglin.hurt": "Zombifizieada Piglin nimmd an Schoodn", "subtitles.event.mob_effect.bad_omen": "Omen takes hold", "subtitles.event.mob_effect.raid_omen": "Raid looms nearby", "subtitles.event.mob_effect.trial_omen": "Ominous trial looms nearby", "subtitles.event.raid.horn": "<PERSON>hail<PERSON><PERSON> e<PERSON>", "subtitles.item.armor.equip": "Ausrüsdung anglechd", "subtitles.item.armor.equip_chain": "Keddnrüsdung rassld", "subtitles.item.armor.equip_diamond": "Diamandrüsdung gliad", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON> raschln", "subtitles.item.armor.equip_gold": "Goldrüsdung klimbad", "subtitles.item.armor.equip_iron": "Aasnrüsdung schebbad", "subtitles.item.armor.equip_leather": "Leddarüsdung gniaschd", "subtitles.item.armor.equip_netherite": "Netheritrüstung scheppad", "subtitles.item.armor.equip_turtle": "Schildgrödnbanza klabbad", "subtitles.item.armor.equip_wolf": "Wolf Armor is fastened", "subtitles.item.armor.unequip_wolf": "Wolf Armor snips away", "subtitles.item.axe.scrape": "Axd schabd", "subtitles.item.axe.strip": "Axd schabd", "subtitles.item.axe.wax_off": "Wachs endfeand", "subtitles.item.bone_meal.use": "Gnochnmehl riesld", "subtitles.item.book.page_turn": "<PERSON><PERSON> ras<PERSON>", "subtitles.item.book.put": "<PERSON><PERSON> glad<PERSON>", "subtitles.item.bottle.empty": "<PERSON><PERSON> gleerd", "subtitles.item.bottle.fill": "Buddel gfülld", "subtitles.item.brush.brushing.generic": "Brushing", "subtitles.item.brush.brushing.gravel": "Brushing Gravel", "subtitles.item.brush.brushing.gravel.complete": "Brushing Gravel completed", "subtitles.item.brush.brushing.sand": "Brushing Sand", "subtitles.item.brush.brushing.sand.complete": "Brushing Sand completed", "subtitles.item.bucket.empty": "<PERSON><PERSON> g<PERSON>", "subtitles.item.bucket.fill": "<PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "Axolodl gschöpfd", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON> gfangn", "subtitles.item.bucket.fill_tadpole": "Tadpole captured", "subtitles.item.bundle.drop_contents": "<PERSON><PERSON><PERSON><PERSON> glee<PERSON>", "subtitles.item.bundle.insert": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>d", "subtitles.item.bundle.insert_fail": "Bundle full", "subtitles.item.bundle.remove_one": "Gegnstand auschgepaggd", "subtitles.item.chorus_fruit.teleport": "Schbiela teleboadiead", "subtitles.item.crop.plant": "Pflanzn gflanzd", "subtitles.item.crossbow.charge": "A<PERSON>b<PERSON><PERSON> schbannd", "subtitles.item.crossbow.hit": "Arrow hits", "subtitles.item.crossbow.load": "<PERSON><PERSON><PERSON><PERSON><PERSON> läd", "subtitles.item.crossbow.shoot": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>abd", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "Feuakugl zischd", "subtitles.item.flintandsteel.use": "Feuazeuch zünded", "subtitles.item.glow_ink_sac.use": "Leuchdindnbeudl klecksd", "subtitles.item.goat_horn.play": "<PERSON><PERSON> plays", "subtitles.item.hoe.till": "<PERSON>ue pfüchd", "subtitles.item.honey_bottle.drink": "Schlugge", "subtitles.item.honeycomb.wax_on": "Wachsn", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "Dindnbeudl klecksd", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "Kombass oaf Leidstoa ausgrichded", "subtitles.item.mace.smash_air": "<PERSON> smashes", "subtitles.item.mace.smash_ground": "<PERSON> smashes", "subtitles.item.nether_wart.plant": "Crop planted", "subtitles.item.ominous_bottle.dispose": "Bottle breaks", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "<PERSON><PERSON> schnaided", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON> wehrd oab", "subtitles.item.shovel.flatten": "<PERSON><PERSON><PERSON><PERSON> blä<PERSON>", "subtitles.item.spyglass.stop_using": "<PERSON><PERSON><PERSON> fährd ei", "subtitles.item.spyglass.use": "<PERSON><PERSON><PERSON> fährd aus", "subtitles.item.totem.use": "Dodem ausglösd", "subtitles.item.trident.hit": "Draizagg spießd oaf", "subtitles.item.trident.hit_ground": "Draizagg vibriad", "subtitles.item.trident.return": "Draizagg keahd zrück", "subtitles.item.trident.riptide": "Draizagg ziehd", "subtitles.item.trident.throw": "Draizagg gliad", "subtitles.item.trident.thunder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> grolld", "subtitles.item.wolf_armor.break": "<PERSON>or breaks", "subtitles.item.wolf_armor.crack": "Wolf Armor cracks", "subtitles.item.wolf_armor.damage": "Wolf Armor takes damage", "subtitles.item.wolf_armor.repair": "Wolf Armor is repaired", "subtitles.particle.soul_escape": "Seel end<PERSON>d", "subtitles.ui.cartography_table.take_result": "Kaddn g<PERSON>achned", "subtitles.ui.hud.bubble_pop": "Breath meter dropping", "subtitles.ui.loom.take_result": "Webstuhl bnutzd", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bnutzd", "subtitles.weather.rain": "<PERSON><PERSON><PERSON>", "symlink_warning.message": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.pack": "Loading packs with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.world": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.more_info": "More Information", "symlink_warning.title": "World folder contains symbolic links", "symlink_warning.title.pack": "Added pack(s) contain(s) symbolic links", "symlink_warning.title.world": "The world folder contains symbolic links", "team.collision.always": "Always", "team.collision.never": "Never", "team.collision.pushOtherTeams": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "team.collision.pushOwnTeam": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "team.notFound": "Unb`gann<PERSON>am: %s", "team.visibility.always": "Imma", "team.visibility.hideForOtherTeams": "Füa andre Teams veaschdeckn", "team.visibility.hideForOwnTeam": "Füas eigne Team veaschdeckn", "team.visibility.never": "Nie un nimma", "telemetry.event.advancement_made.description": "Understanding the context behind receiving an advancement can help us better understand and improve the progression of the game.", "telemetry.event.advancement_made.title": "Advancement Made", "telemetry.event.game_load_times.description": "This event can help us figure out where startup performance improvements are needed by measuring the execution times of the startup phases.", "telemetry.event.game_load_times.title": "Game Load Times", "telemetry.event.optional": "%s (Optional)", "telemetry.event.optional.disabled": "%s (Optional) - Disabled", "telemetry.event.performance_metrics.description": "Knowing the overall performance profile of Minecraft helps us tune and optimize the game for a wide range of machine specifications and operating systems. \nGame version is included to help us compare the performance profile for new versions of Minecraft.", "telemetry.event.performance_metrics.title": "Performance Metrics", "telemetry.event.required": "%s (Required)", "telemetry.event.world_load_times.description": "It's important for us to understand how long it takes to join a world, and how that changes over time. For example, when we add new features or do larger technical changes, we need to see what impact that had on load times.", "telemetry.event.world_load_times.title": "World Load Times", "telemetry.event.world_loaded.description": "Knowing how players play Minecraft (such as Game Mode, client or server modded, and game version) allows us to focus game updates to improve the areas that players care about most.\nThe World Loaded event is paired with the World Unloaded event to calculate how long the play session has lasted.", "telemetry.event.world_loaded.title": "World Loaded", "telemetry.event.world_unloaded.description": "This event is paired with the World Loaded event to calculate how long the world session has lasted.\nThe duration (in seconds and ticks) is measured when a world session has ended (quitting to title, disconnecting from a server).", "telemetry.event.world_unloaded.title": "World Unloaded", "telemetry.property.advancement_game_time.title": "Game Time (Ticks)", "telemetry.property.advancement_id.title": "Advancement ID", "telemetry.property.client_id.title": "Client ID", "telemetry.property.client_modded.title": "<PERSON><PERSON>", "telemetry.property.dedicated_memory_kb.title": "Dedicated Memory (kB)", "telemetry.property.event_timestamp_utc.title": "Event Timestamp (UTC)", "telemetry.property.frame_rate_samples.title": "Frame Rate Samples (FPS)", "telemetry.property.game_mode.title": "Game Mode", "telemetry.property.game_version.title": "Game Version", "telemetry.property.launcher_name.title": "Launcher Name", "telemetry.property.load_time_bootstrap_ms.title": "Bootstrap Time (Milliseconds)", "telemetry.property.load_time_loading_overlay_ms.title": "Time in Loading Screen (Milliseconds)", "telemetry.property.load_time_pre_window_ms.title": "Time Before Window Opens (Milliseconds)", "telemetry.property.load_time_total_time_ms.title": "Total Load Time (Milliseconds)", "telemetry.property.minecraft_session_id.title": "Minecraft Session ID", "telemetry.property.new_world.title": "New World", "telemetry.property.number_of_samples.title": "Sample Count", "telemetry.property.operating_system.title": "Operating System", "telemetry.property.opt_in.title": "Opt-In", "telemetry.property.platform.title": "Platform", "telemetry.property.realms_map_content.title": "Realms Map Content (Minigame Name)", "telemetry.property.render_distance.title": "Render Distance", "telemetry.property.render_time_samples.title": "Render Time Samples", "telemetry.property.seconds_since_load.title": "Time Since Load (Seconds)", "telemetry.property.server_modded.title": "Server Modded", "telemetry.property.server_type.title": "Server Type", "telemetry.property.ticks_since_load.title": "Time Since Load (Ticks)", "telemetry.property.used_memory_samples.title": "Used Random Access Memory", "telemetry.property.user_id.title": "User ID", "telemetry.property.world_load_time_ms.title": "World Load Time (Milliseconds)", "telemetry.property.world_session_id.title": "World Session ID", "telemetry_info.button.give_feedback": "<PERSON>", "telemetry_info.button.privacy_statement": "Privacy Statement", "telemetry_info.button.show_data": "View My Data", "telemetry_info.opt_in.description": "I consent to sending optional telemetry data", "telemetry_info.property_title": "Included Data", "telemetry_info.screen.description": "Collecting this data helps us improve Minecraft by guiding us in directions that are relevant to our players.\nYou can also send in additional feedback to help us keep improving Minecraft.", "telemetry_info.screen.title": "Telemetry Data Collection", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Message:", "test_block.mode.accept": "Accept", "test_block.mode.fail": "Fail", "test_block.mode.log": "Log", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Save Structure", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Failed: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Invalid test ID", "test_instance.description.no_test": "No such test", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Entities:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[invalid]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "32-Bid Syschdem ärgannd: <PERSON>sch könndä dich dran hindärn, in Zugunft zuspielen, doa ein 64-Bid Syschdem ärfodderlich ischd!", "title.32bit.deprecation.realms": "Minecraft wird boald oain 64‐Bid‐Syschdem bnödign, wod<PERSON>rch du Realmsch auf dieschem Grädt nicht mähr schpielän oder verwänden koannsd. Du musscht jädesch Realmsch‐Abonnement manuell kündigän.", "title.32bit.deprecation.realms.check": "Do not show this screen again", "title.32bit.deprecation.realms.header": "32‐Bid‐Syschdem ärgannd", "title.credits": "Copyright Mojang AB. Do not distribute!", "title.multiplayer.disabled": "Multiplayer is disabled. Please check your Microsoft account settings.", "title.multiplayer.disabled.banned.name": "You must change your name before you can play online", "title.multiplayer.disabled.banned.permanent": "Your account is permanently suspended from online play", "title.multiplayer.disabled.banned.temporary": "Your account is temporarily suspended from online play", "title.multiplayer.lan": "<PERSON><PERSON><PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Driddanbieda-Seava)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "Singleplayer", "translation.test.args": "%s %s", "translation.test.complex": "Prefix, %s%2$s again %s and %1$s lastly %s and also %1$s again!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hi %", "translation.test.invalid2": "hi %s", "translation.test.none": "Hello, world!", "translation.test.world": "world", "trim_material.minecraft.amethyst": "Amethyst Material", "trim_material.minecraft.copper": "Copper Material", "trim_material.minecraft.diamond": "Diamond Material", "trim_material.minecraft.emerald": "Emerald Material", "trim_material.minecraft.gold": "Gold Material", "trim_material.minecraft.iron": "Iron Material", "trim_material.minecraft.lapis": "Lapis Material", "trim_material.minecraft.netherite": "Netherite Material", "trim_material.minecraft.quartz": "Quartz Material", "trim_material.minecraft.redstone": "Redstone Material", "trim_material.minecraft.resin": "Resin Material", "trim_pattern.minecraft.bolt": "Bolt Armor Trim", "trim_pattern.minecraft.coast": "Coast Armor Trim", "trim_pattern.minecraft.dune": "<PERSON>ne Armor <PERSON>", "trim_pattern.minecraft.eye": "Eye Armor Trim", "trim_pattern.minecraft.flow": "Flow Armor Trim", "trim_pattern.minecraft.host": "Host <PERSON><PERSON>", "trim_pattern.minecraft.raiser": "Raiser Armor Trim", "trim_pattern.minecraft.rib": "<PERSON><PERSON>", "trim_pattern.minecraft.sentry": "Sentry Armor Trim", "trim_pattern.minecraft.shaper": "<PERSON><PERSON><PERSON> Armor <PERSON>", "trim_pattern.minecraft.silence": "Silence Armor Trim", "trim_pattern.minecraft.snout": "Snout Armor Trim", "trim_pattern.minecraft.spire": "Spire Arm<PERSON>", "trim_pattern.minecraft.tide": "Tide Armor Trim", "trim_pattern.minecraft.vex": "Vex Armor Trim", "trim_pattern.minecraft.ward": "<PERSON>", "trim_pattern.minecraft.wayfinder": "Wayfinder Armor Trim", "trim_pattern.minecraft.wild": "Wild Armor Trim", "tutorial.bundleInsert.description": "Rechdsklick, um an Gechnstand zuzufügn", "tutorial.bundleInsert.title": "<PERSON><PERSON><PERSON><PERSON> an Beudl", "tutorial.craft_planks.description": "S Rezeptbuach kann hölfe", "tutorial.craft_planks.title": "<PERSON><PERSON> hea", "tutorial.find_tree.description": "<PERSON><PERSON><PERSON> ihn füa <PERSON>", "tutorial.find_tree.title": "Find an Boam", "tutorial.look.description": "<PERSON><PERSON><PERSON>", "tutorial.look.title": "<PERSON><PERSON><PERSON> dich um", "tutorial.move.description": "Schbring mid %s", "tutorial.move.title": "Lauf mid %s, %s, %s und %s", "tutorial.open_inventory.description": "Drügg %s", "tutorial.open_inventory.title": "Effe dei Invendoa", "tutorial.punch_tree.description": "Hald %s fesd", "tutorial.punch_tree.title": "<PERSON><PERSON><PERSON> den Boam", "tutorial.socialInteractions.description": "Drück %s zum Öffnen", "tutorial.socialInteractions.title": "Social Interactions", "upgrade.minecraft.netherite_upgrade": "Netherite Upgrade"}