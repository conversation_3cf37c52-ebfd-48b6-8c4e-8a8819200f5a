{"accessibility.onboarding.accessibility.button": "Ajustes de accesibilidad...", "accessibility.onboarding.screen.narrator": "Presiona Enter para activar el narrador", "accessibility.onboarding.screen.title": "¡Bienvenido a Minecraft!\n\n¿Quieres activar el narrador o ver los ajustes de accesibilidad?", "addServer.add": "Aceptar", "addServer.enterIp": "Dirección del servidor", "addServer.enterName": "Nombre del servidor", "addServer.resourcePack": "Recursos del servidor", "addServer.resourcePack.disabled": "No", "addServer.resourcePack.enabled": "Sí", "addServer.resourcePack.prompt": "Preguntar", "addServer.title": "Editar información del servidor", "advMode.command": "Comando de consola", "advMode.mode": "Modo", "advMode.mode.auto": "Repetición", "advMode.mode.autoexec.bat": "Siempre activo", "advMode.mode.conditional": "Condicional", "advMode.mode.redstone": "<PERSON>mpul<PERSON>", "advMode.mode.redstoneTriggered": "Necesita redstone", "advMode.mode.sequence": "En cadena", "advMode.mode.unconditional": "Incondicional", "advMode.notAllowed": "Necesitas ser un administrador para usarlo en modo creativo", "advMode.notEnabled": "Los bloques de comandos no están habilitados en este servidor", "advMode.previousOutput": "Comando anterior", "advMode.setCommand": "Establece un comando de consola para el bloque", "advMode.setCommand.success": "Comando establecido: %s", "advMode.trackOutput": "Ver el resultado", "advMode.triggering": "Activando", "advMode.type": "Tipo", "advancement.advancementNotFound": "Progreso desconocido: %s", "advancements.adventure.adventuring_time.description": "Descubre todos los biomas", "advancements.adventure.adventuring_time.title": "Hora de aventura", "advancements.adventure.arbalistic.description": "Mata a cinco criaturas de diferente especie con un solo tiro de ballesta", "advancements.adventure.arbalistic.title": "Ballestería avanzada", "advancements.adventure.avoid_vibration.description": "Camina agachado cerca de un sensor de sculk o de un warden para evitar que te detecten", "advancements.adventure.avoid_vibration.title": "Sigilo 100", "advancements.adventure.blowback.description": "Mata una Brisa con una Carga de Viento", "advancements.adventure.blowback.title": "<PERSON><PERSON><PERSON> de regreso", "advancements.adventure.brush_armadillo.description": "Obtén escamas de Cachicamo usando una Brocha", "advancements.adventure.brush_armadillo.title": "¿No es Escadorable?", "advancements.adventure.bullseye.description": "Dispara al blanco de una diana desde al menos 30 metros de distancia", "advancements.adventure.bullseye.title": "¡Ojo de Halcón!", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Fabrica un jarrón decorado con 4 fragmentos de cerámica", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Restauración Cuidadosa", "advancements.adventure.crafters_crafting_crafters.description": "Mantente cerca de un fabricador cuando fabrique otro fabricador", "advancements.adventure.crafters_crafting_crafters.title": "Fabricadores Fabricando Fabricadores", "advancements.adventure.fall_from_world_height.description": "Tírate desde el cielo (el límite de construcción) hasta el fondo del mundo y sobrevive", "advancements.adventure.fall_from_world_height.title": "Cuevas y acantilados", "advancements.adventure.heart_transplanter.description": "Coloca un Corazón Crujiente con la alineación correcta entre dos bloques de Tronco de Roble Pálido", "advancements.adventure.heart_transplanter.title": "Transplantador de Corazones", "advancements.adventure.hero_of_the_village.description": "Defiende una aldea de una invasión correctamente", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON><PERSON> aldea", "advancements.adventure.honey_block_slide.description": "Deslízate por un bloque de miel para amortiguar tu caída", "advancements.adventure.honey_block_slide.title": "Situación pegajosa", "advancements.adventure.kill_a_mob.description": "Mata a un monstruo", "advancements.adventure.kill_a_mob.title": "Cazamonstruos", "advancements.adventure.kill_all_mobs.description": "Mata a una criatura hostil de cada tipo", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Mata una criatura cerca de un catalizador de sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Se propaga", "advancements.adventure.lighten_up.description": "Raspe una bombilla de cobre con un hacha para hacerla más brillante", "advancements.adventure.lighten_up.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Protege a un aldeano de una descarga indeseada sin iniciar un incendio", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Contratensiones", "advancements.adventure.minecraft_trials_edition.description": "Entra a una Cámara de Prueba", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON>(s)", "advancements.adventure.ol_betsy.description": "Dispara una ballesta", "advancements.adventure.ol_betsy.title": "La vieja Betsy", "advancements.adventure.overoverkill.description": "Haz 50 corazones de daño en un solo golpe usando el mazo", "advancements.adventure.overoverkill.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.play_jukebox_in_meadows.description": "Haz que los prados vibren con el dulce cantar de un tocadiscos", "advancements.adventure.play_jukebox_in_meadows.title": "El dulce cantar", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Lee la señal de energía de una Estantería Cincelada usando un Comparador", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "Los libros son poderosos", "advancements.adventure.revaulting.description": "Desbloquea una bóveda siniestra con una llave de prueba siniestra", "advancements.adventure.revaulting.title": "La caja de Pandora", "advancements.adventure.root.description": "Aventura, exploración y combate", "advancements.adventure.root.title": "Aventura", "advancements.adventure.salvage_sherd.description": "Cepilla un bloque sospechoso para obtener un fragmento de cerámica", "advancements.adventure.salvage_sherd.title": "Respetando los Remanentes", "advancements.adventure.shoot_arrow.description": "Dispara a algo con una flecha", "advancements.adventure.shoot_arrow.title": "En el punto de mira", "advancements.adventure.sleep_in_bed.description": "Duerme en una cama para cambiar tu punto de reaparición", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON> sue<PERSON>", "advancements.adventure.sniper_duel.description": "Mata a un esqueleto desde 50 metros o más", "advancements.adventure.sniper_duel.title": "Francocazadores", "advancements.adventure.spyglass_at_dragon.description": "Mira al Ender Dragon con un catalejo", "advancements.adventure.spyglass_at_dragon.title": "¿Es un avión?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON> a un ghast con un catalejo", "advancements.adventure.spyglass_at_ghast.title": "¿Es un globo?", "advancements.adventure.spyglass_at_parrot.description": "Mira a un loro con un catalejo", "advancements.adventure.spyglass_at_parrot.title": "¿Es un pájaro?", "advancements.adventure.summon_iron_golem.description": "Construye un gólem de hierro para ayudar a defender una aldea", "advancements.adventure.summon_iron_golem.title": "El guardaespaldas", "advancements.adventure.throw_trident.description": "Lanza un tridente a algo.\nNota: Tirar tu única arma no es una buena idea.", "advancements.adventure.throw_trident.title": "Jugada maestra", "advancements.adventure.totem_of_undying.description": "Usa un tótem de la inmortalidad para engañar a la muerte", "advancements.adventure.totem_of_undying.title": "Post mortem", "advancements.adventure.trade.description": "Intercambia con un aldeano", "advancements.adventure.trade.title": "¡Qué buen trato!", "advancements.adventure.trade_at_world_height.description": "Comercia con un aldeano en el límite de altura del mundo", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON> est<PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Aplica estos moldes de herrería al menos una vez: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, V<PERSON>, Marea, encuentra-caminos", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_any_armor_pattern.description": "Decora una pieza de armadura en una Mesa de Herrería", "advancements.adventure.trim_with_any_armor_pattern.title": "Fabricando una nueva pinta", "advancements.adventure.two_birds_one_arrow.description": "Mata a dos phantoms usando una flecha perforante", "advancements.adventure.two_birds_one_arrow.title": "Dos pájaros de un tiro", "advancements.adventure.under_lock_and_key.description": "Desbloquea una Bóveda con una Llave de Prueba", "advancements.adventure.under_lock_and_key.title": "Bajo Llave y Cerrojo", "advancements.adventure.use_lodestone.description": "Usa una brújula en un bloque de magnetita", "advancements.adventure.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON>, llévame a mi hogarcito", "advancements.adventure.very_very_frightening.description": "Golpea a un aldeano con un rayo", "advancements.adventure.very_very_frightening.title": "Impactrueno", "advancements.adventure.voluntary_exile.description": "Mata al capitán de una invasión.\nPodrías considerar mantenerte lejos de las aldeas durante un tiempo...", "advancements.adventure.voluntary_exile.title": "<PERSON><PERSON>o volunta<PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Camina sobre la nieve en polvo... sin hundirte en ella", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Ligero cual conejo", "advancements.adventure.who_needs_rockets.description": "Usa una carga de viento para lanzarte hacia arriba 8 bloques", "advancements.adventure.who_needs_rockets.title": "¿<PERSON>uién neces<PERSON>?", "advancements.adventure.whos_the_pillager_now.description": "<PERSON> a un saqueador de su propia medicina", "advancements.adventure.whos_the_pillager_now.title": "El saqueador saqueado", "advancements.empty": "Parece que no hay nada...", "advancements.end.dragon_breath.description": "Recoge aliento de dragón en un frasco", "advancements.end.dragon_breath.title": "¡Qué mal aliento!", "advancements.end.dragon_egg.description": "Consigue el huevo de dragón", "advancements.end.dragon_egg.title": "La nueva generación", "advancements.end.elytra.description": "Encuentra unos élitros", "advancements.end.elytra.title": "Al infinito... ¡y más allá!", "advancements.end.enter_end_gateway.description": "Escapa de la isla principal", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON> remota", "advancements.end.find_end_city.description": "<PERSON><PERSON>, ¿qué es lo peor que puede pasar?", "advancements.end.find_end_city.title": "La ciudad al final del juego", "advancements.end.kill_dragon.description": "Acaba con el dragón, ¡buena suerte!", "advancements.end.kill_dragon.title": "La libertad del End", "advancements.end.levitate.description": "Levita a una altura de 50 bloques al ser atacado por un shulker", "advancements.end.levitate.title": "Buenas vistas desde lo alto", "advancements.end.respawn_dragon.description": "Vuelve a invocar al dragón", "advancements.end.respawn_dragon.title": "¿Es un déjà vu?", "advancements.end.root.description": "¿Se acaba el juego? O tal vez... ¿sólo acaba de empezar?", "advancements.end.root.title": "El End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Haz que un allay deje caer un pastel en un bloque musical", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "¡Cumpleaños feliz!", "advancements.husbandry.allay_deliver_item_to_player.description": "Haz que un allay te traiga algún objeto", "advancements.husbandry.allay_deliver_item_to_player.title": "Yo soy tu amigo fiel", "advancements.husbandry.axolotl_in_a_bucket.description": "<PERSON>rapa un ajolote en un balde", "advancements.husbandry.axolotl_in_a_bucket.title": "El depredador más adorable", "advancements.husbandry.balanced_diet.description": "Come todo lo que sea comestible, aunque no sea saludable", "advancements.husbandry.balanced_diet.title": "Una dieta balanceada", "advancements.husbandry.breed_all_animals.description": "¡Haz que se reproduzcan dos animales de cada tipo!", "advancements.husbandry.breed_all_animals.title": "De dos en dos", "advancements.husbandry.breed_an_animal.description": "Haz que dos animales de la misma especie se reproduzcan", "advancements.husbandry.breed_an_animal.title": "Tor<PERSON>lit<PERSON>", "advancements.husbandry.complete_catalogue.description": "¡Domestica a todas las variantes de gato!", "advancements.husbandry.complete_catalogue.title": "Un gatálogo completo", "advancements.husbandry.feed_snifflet.description": "Ali<PERSON>a a un sniffer", "advancements.husbandry.feed_snifflet.title": "Pequeños olfateos", "advancements.husbandry.fishy_business.description": "Ve a pescar", "advancements.husbandry.fishy_business.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.froglights.description": "Ten todas las ranaluces en tu inventario", "advancements.husbandry.froglights.title": "¡Combinando nuestros poderes!", "advancements.husbandry.kill_axolotl_target.description": "Forma equipo con un ajolote y gana una pelea", "advancements.husbandry.kill_axolotl_target.title": "¡El poder curativo de la amistad!", "advancements.husbandry.leash_all_frog_variants.description": "Atrapa cada variante de rana con una rienda", "advancements.husbandry.leash_all_frog_variants.title": "Los tres mosqueteros", "advancements.husbandry.make_a_sign_glow.description": "Haz que el texto de cualquier cartel brille", "advancements.husbandry.make_a_sign_glow.title": "¡Brillante idea!", "advancements.husbandry.netherite_hoe.description": "Usa un lingote de netherita para mejorar un azadón y replantéate tu vida", "advancements.husbandry.netherite_hoe.title": "Dedicación seria", "advancements.husbandry.obtain_sniffer_egg.description": "Obtén un huevo de Sniffer", "advancements.husbandry.obtain_sniffer_egg.title": "Esto huele interesante...", "advancements.husbandry.place_dried_ghast_in_water.description": "Coloca un bloque de Ghast seco en agua", "advancements.husbandry.place_dried_ghast_in_water.title": "¡Manténgase hidratado!", "advancements.husbandry.plant_any_sniffer_seed.description": "Planta cualquier semilla de un Sniffer", "advancements.husbandry.plant_any_sniffer_seed.title": "Sembrando el pasado", "advancements.husbandry.plant_seed.description": "Planta una semilla y mírala crecer", "advancements.husbandry.plant_seed.title": "Crecen tan rápido...", "advancements.husbandry.remove_wolf_armor.description": "Retira la Armadura de un Lobo utilizando Tijeras", "advancements.husbandry.remove_wolf_armor.title": "Corta Brillantemente", "advancements.husbandry.repair_wolf_armor.description": "Repara una armadura de lobo dañada usando escamas de cachicamo", "advancements.husbandry.repair_wolf_armor.title": "Como nuevo", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Sube a un bote con una cabra", "advancements.husbandry.ride_a_boat_with_a_goat.title": "¡Las cabras flotan!", "advancements.husbandry.root.description": "El mundo está lleno de amigos y comida", "advancements.husbandry.root.title": "Prosperidad", "advancements.husbandry.safely_harvest_honey.description": "Usa una fogata para recolectar miel de una colmena usando una botella de vidrio sin molestar a las abejas", "advancements.husbandry.safely_harvest_honey.title": "Abelante, esta es tu casa", "advancements.husbandry.silk_touch_nest.description": "Mueve una colmena o un apiario que contenga 3 abejas usando Toque de seda", "advancements.husbandry.silk_touch_nest.title": "Abejé<PERSON>os de aquí", "advancements.husbandry.tactical_fishing.description": "Atrapa un pez... ¡pero sin caña de pescar!", "advancements.husbandry.tactical_fishing.title": "Pesca táctica", "advancements.husbandry.tadpole_in_a_bucket.description": "Atrapa un renacuajo en un balde", "advancements.husbandry.tadpole_in_a_bucket.title": "¿Un baldecuajo?", "advancements.husbandry.tame_an_animal.description": "Domestica a un animal", "advancements.husbandry.tame_an_animal.title": "Mejores amigos para siempre", "advancements.husbandry.wax_off.description": "¡Pule la cera de un bloque de cobre!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON> pulida", "advancements.husbandry.wax_on.description": "¡Encere un bloque de cobre!", "advancements.husbandry.wax_on.title": "Aplicar cera", "advancements.husbandry.whole_pack.description": "Domestica a todas las especies de lobo", "advancements.husbandry.whole_pack.title": "Toda la Pandilla", "advancements.nether.all_effects.description": "Ten todos los efectos posibles al mismo tiempo", "advancements.nether.all_effects.title": "¿Cómo llegamos hasta aquí?", "advancements.nether.all_potions.description": "Ten todos los efectos de pociones al mismo tiempo", "advancements.nether.all_potions.title": "Mezcla explosiva", "advancements.nether.brew_potion.description": "Elabora una poción", "advancements.nether.brew_potion.title": "Destilería local", "advancements.nether.charge_respawn_anchor.description": "Carga un nexo de reaparición al máximo", "advancements.nether.charge_respawn_anchor.title": "No \"siete\" vidas exactamente ", "advancements.nether.create_beacon.description": "Fabrica y coloca un faro", "advancements.nether.create_beacon.title": "Hágase la luz", "advancements.nether.create_full_beacon.description": "Haz que un faro trabaje a su máxima potencia", "advancements.nether.create_full_beacon.title": "Faroneitor", "advancements.nether.distract_piglin.description": "Di<PERSON>e piglins usando oro", "advancements.nether.distract_piglin.title": "Mi precioso...", "advancements.nether.explore_nether.description": "Explora todos los biomas del Nether", "advancements.nether.explore_nether.title": "Cálidos destinos turísticos", "advancements.nether.fast_travel.description": "Usa el Nether para recorrer 7 km en la superficie", "advancements.nether.fast_travel.title": "Burbuja subespacial", "advancements.nether.find_bastion.description": "Entra en las ruinas de un bastión", "advancements.nether.find_bastion.title": "Qué tiempos aquellos", "advancements.nether.find_fortress.description": "Descubre una fortaleza en el Nether", "advancements.nether.find_fortress.title": "Una terrible fortaleza", "advancements.nether.get_wither_skull.description": "Consigue el cráneo de un esqueleto del Wither", "advancements.nether.get_wither_skull.title": "Por una cabeza", "advancements.nether.loot_bastion.description": "Saquea un cofre en un bastión en ruinas", "advancements.nether.loot_bastion.title": "Guerra de cochinos", "advancements.nether.netherite_armor.description": "Consigue armadura de netherita completa", "advancements.nether.netherite_armor.title": "Cúbreme de netherita", "advancements.nether.obtain_ancient_debris.description": "Consigue escombros ancestrales", "advancements.nether.obtain_ancient_debris.title": "Oculto en las profundidades", "advancements.nether.obtain_blaze_rod.description": "Consigue una vara de blaze", "advancements.nether.obtain_blaze_rod.title": "Jugando con fuego", "advancements.nether.obtain_crying_obsidian.description": "Consigue obsidiana llorosa", "advancements.nether.obtain_crying_obsidian.title": "¿Quién está cortando cebollas?", "advancements.nether.return_to_sender.description": "Mata un ghast devolviéndole una bola de fuego", "advancements.nether.return_to_sender.title": "Devolver al remitente", "advancements.nether.ride_strider.description": "Monta a un lavagante con una caña con hongo distorsionado", "advancements.nether.ride_strider.title": "¡Este bote tiene patas!", "advancements.nether.ride_strider_in_overworld_lava.description": "Da un largo paseo con un lavagante sobre un lago de lava en la superficie", "advancements.nether.ride_strider_in_overworld_lava.title": "Como en casa", "advancements.nether.root.description": "¡Estar aquí es infernal!", "advancements.nether.root.title": "<PERSON>", "advancements.nether.summon_wither.description": "Invoca al Wither", "advancements.nether.summon_wither.title": "Dr<PERSON>", "advancements.nether.uneasy_alliance.description": "Rescata a un ghast del Nether, llévalo sano y salvo a la superficie... y... acaba con él", "advancements.nether.uneasy_alliance.title": "Falsa al<PERSON>za", "advancements.nether.use_lodestone.description": "I<PERSON>ta una brújula con un bloque de magnetita", "advancements.nether.use_lodestone.title": "Magne<PERSON>ta, llévame a casita", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Debilita y cura a un aldeano zombi", "advancements.story.cure_zombie_villager.title": "Zombiólogo", "advancements.story.deflect_arrow.description": "Desvía un proyectil con un escudo", "advancements.story.deflect_arrow.title": "Hoy no, gracias", "advancements.story.enchant_item.description": "Encanta un objeto en una mesa de encantamientos", "advancements.story.enchant_item.title": "Aprendiz de mago", "advancements.story.enter_the_end.description": "Localiza, enciende y atraviesa un portal al End", "advancements.story.enter_the_end.title": "¿Se acabó?", "advancements.story.enter_the_nether.description": "Construye, enciende y entra en un portal al Nether", "advancements.story.enter_the_nether.title": "Tenemos que ir más profundo", "advancements.story.follow_ender_eye.description": "<PERSON>gue ojos de ender", "advancements.story.follow_ender_eye.title": "Ojo espía", "advancements.story.form_obsidian.description": "Consigue un bloque de obsidiana", "advancements.story.form_obsidian.title": "Mente fría", "advancements.story.iron_tools.description": "Mejora tu pico", "advancements.story.iron_tools.title": "¿No es hierrónico?", "advancements.story.lava_bucket.description": "<PERSON><PERSON> un balde con lava", "advancements.story.lava_bucket.title": "¡La cosa está que arde!", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON> di<PERSON>", "advancements.story.mine_diamond.title": "¡DIAMANTES!", "advancements.story.mine_stone.description": "Pica piedra con tu nuevo pico", "advancements.story.mine_stone.title": "La Edad de Piedra", "advancements.story.obtain_armor.description": "Protégete con una pieza de armadura de hierro", "advancements.story.obtain_armor.title": "A vestirse", "advancements.story.root.description": "El corazón y la historia del juego", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Una armadura de diamante puede salvarte la vida", "advancements.story.shiny_gear.title": "Cúbreme de diamantes", "advancements.story.smelt_iron.description": "Funde un lingote de hierro", "advancements.story.smelt_iron.title": "La Edad de Hierro", "advancements.story.upgrade_tools.description": "Fabrica un pico mejor", "advancements.story.upgrade_tools.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.toast.challenge": "¡Desafío completado!", "advancements.toast.goal": "¡Objetivo alcanzado!", "advancements.toast.task": "¡Progreso realizado!", "argument.anchor.invalid": "La posición de anclaje de la entidad no es válida: %s", "argument.angle.incomplete": "Incompleto (se requiere 1 ángulo)", "argument.angle.invalid": "<PERSON><PERSON><PERSON>", "argument.block.id.invalid": "Tipo de bloque desconocido: %s", "argument.block.property.duplicate": "La propiedad \"%s\" del bloque %s sólo puede establecerse una vez", "argument.block.property.invalid": "El bloque %s no acepta \"%s\" para la propiedad \"%s\"", "argument.block.property.novalue": "Se requiere un valor para la propiedad \"%s\" del bloque %s", "argument.block.property.unclosed": "Se requiere \"]\" para cerrar las propiedades del bloque", "argument.block.property.unknown": "El bloque %s no posee la propiedad \"%s\"", "argument.block.tag.disallowed": "Aquí no se permite el uso de #, sólo bloques actuales", "argument.color.invalid": "Color desconocido: %s", "argument.component.invalid": "Componente de chat no válido: %s", "argument.criteria.invalid": "Criterio desconocido: %s", "argument.dimension.invalid": "La dimensión \"%s\" no existe", "argument.double.big": "El valor doble no puede ser mayor que %s (encontrado: %s)", "argument.double.low": "El valor doble no puede ser menor que %s (encontrado: %s)", "argument.entity.invalid": "Nombre o UUID no válidos", "argument.entity.notfound.entity": "No se encontraron entidades", "argument.entity.notfound.player": "No se encontraron jugadores", "argument.entity.options.advancements.description": "Jugadores con un determinado progreso", "argument.entity.options.distance.description": "Distancia a la entidad", "argument.entity.options.distance.negative": "La distancia no puede ser negativa", "argument.entity.options.dx.description": "Entidades entre X y X+dX", "argument.entity.options.dy.description": "Entidades entre Y e Y+dY", "argument.entity.options.dz.description": "Entidades entre Z y Z+dZ", "argument.entity.options.gamemode.description": "Jugadores con modos de juego", "argument.entity.options.inapplicable": "La opción \"%s\" no puede aplicarse aquí", "argument.entity.options.level.description": "Nivel de experiencia", "argument.entity.options.level.negative": "El nivel no puede ser negativo", "argument.entity.options.limit.description": "Número máximo de entidades a regresar", "argument.entity.options.limit.toosmall": "El límite tiene que ser, como mínimo, 1", "argument.entity.options.mode.invalid": "Modo de juego no válido o desconocido: %s", "argument.entity.options.name.description": "Nombre de entidad", "argument.entity.options.nbt.description": "Entidades con un determinado NBT", "argument.entity.options.predicate.description": "Predicado personalizado", "argument.entity.options.scores.description": "Entidades con un determinado puntaje", "argument.entity.options.sort.description": "Clasificar entidades", "argument.entity.options.sort.irreversible": "El tipo de clasificación \"%s\" es desconocido o no es válido", "argument.entity.options.tag.description": "Entidades con un determinada etiqueta", "argument.entity.options.team.description": "Entidades en un determinado equipo", "argument.entity.options.type.description": "Entidades de un determinado tipo", "argument.entity.options.type.invalid": "Tipo de entidad desconocido o no válido: %s", "argument.entity.options.unknown": "Opción desconocida: %s", "argument.entity.options.unterminated": "Se requiere \"]\" para cerrar las opciones", "argument.entity.options.valueless": "Se requiere un valor para la opción \"%s\"", "argument.entity.options.x.description": "posición x", "argument.entity.options.x_rotation.description": "Rotación x de la entidad", "argument.entity.options.y.description": "posición y", "argument.entity.options.y_rotation.description": "Rotación y de la entidad", "argument.entity.options.z.description": "posición z", "argument.entity.selector.allEntities": "Todas las entidades", "argument.entity.selector.allPlayers": "Todos los jugadores", "argument.entity.selector.missing": "Falta el tipo de selector", "argument.entity.selector.nearestEntity": "Entidad Cercana", "argument.entity.selector.nearestPlayer": "Jugador más cercano", "argument.entity.selector.not_allowed": "Selector no permitido", "argument.entity.selector.randomPlayer": "Jugador aleatorio", "argument.entity.selector.self": "Entidad actual", "argument.entity.selector.unknown": "Tipo de selector desconocido: %s", "argument.entity.toomany": "<PERSON><PERSON>lo se permite una entidad, sin embargo el selector utilizado permite más de una", "argument.enum.invalid": "Valor \"%s\" no válido", "argument.float.big": "El valor float no puede ser mayor que %s (encontrado: %s)", "argument.float.low": "El valor float no puede ser menor que %s (encontrado: %s)", "argument.gamemode.invalid": "Modo de juego desconocido: %s", "argument.hexcolor.invalid": "Código de color hexadecimal '%s' no válido", "argument.id.invalid": "ID no válido", "argument.id.unknown": "ID desconocido: %s", "argument.integer.big": "El número entero no puede ser mayor que %s (encontrado: %s)", "argument.integer.low": "El número entero no puede ser menor que %s (encontrado: %s)", "argument.item.id.invalid": "Objeto desconocido: %s", "argument.item.tag.disallowed": "Aquí no se permite el uso de #, sólo ítems actuales", "argument.literal.incorrect": "Se requiere el valor literal \"%s\"", "argument.long.big": "El valor long no puede ser mayor que %s (encontrado: %s)", "argument.long.low": "El valor long no puede ser menor que %s (encontrado: %s)", "argument.message.too_long": "Este mensaje fue demasiado largo (%s > máximo de %s caracteres)", "argument.nbt.array.invalid": "Tipo de matriz no válido: %s", "argument.nbt.array.mixed": "No se puede insertar %s en %s", "argument.nbt.expected.compound": "Etiqueta compuesta prevista", "argument.nbt.expected.key": "Se requiere clave", "argument.nbt.expected.value": "Se requiere valor", "argument.nbt.list.mixed": "No se puede insertar %s en la lista de %s", "argument.nbt.trailing": "Hay datos de más", "argument.player.entities": "Este comando sólo afecta a jugadores, sin embargo el selector utilizado incluye entidades", "argument.player.toomany": "<PERSON><PERSON><PERSON> se permite un jugador, sin embargo el selector utilizado permite más de uno", "argument.player.unknown": "Ese jugador no existe", "argument.pos.missing.double": "Se requiere una coordenada", "argument.pos.missing.int": "Se requiere la posición de un bloque", "argument.pos.mixed": "No se puede usar una mezcla de coordenadas global y local (o se utiliza ^ con todas o con ninguna)", "argument.pos.outofbounds": "Esta ubicación excede los límites permitidos.", "argument.pos.outofworld": "¡Esa posición está fuera del mundo!", "argument.pos.unloaded": "Esa posición no está cargada", "argument.pos2d.incomplete": "Incompleto (se esperaban 2 coordenadas)", "argument.pos3d.incomplete": "Incompleto (se requieren 3 coordenadas)", "argument.range.empty": "Se requiere un valor o un intervalo de valores", "argument.range.ints": "<PERSON><PERSON><PERSON> se permiten números enteros, números decimales no", "argument.range.swapped": "El mínimo no puede ser mayor que el máximo", "argument.resource.invalid_type": "El elemento \"%s\" es de tipo incorrecto \"%s\" (se esperaba \"%s\")", "argument.resource.not_found": "No se pudo encontrar el elemento \"%s\" de tipo \"%s\"", "argument.resource_or_id.failed_to_parse": "Falla en Análisis de Estructura: %s", "argument.resource_or_id.invalid": "Apodo o Identificación Invalida", "argument.resource_or_id.no_such_element": "No se pudo encontrar el elemento '%s' en el registro '%s'", "argument.resource_selector.not_found": "Sin coincidencias para el selector '%s' del tipo '%s'", "argument.resource_tag.invalid_type": "La etiqueta \"%s\" es del tipo incorrecto \"%s\" (se esperaba \"%s\")", "argument.resource_tag.not_found": "No se pudo encontrar la etiqueta \"%s\" de tipo \"%s\"", "argument.rotation.incomplete": "Incompleto (se requieren 2 coordenadas)", "argument.scoreHolder.empty": "No se encontraron marcadores de puntaje relevantes", "argument.scoreboardDisplaySlot.invalid": "Espacio de muestra desconocido: %s", "argument.style.invalid": "Estilo no válido: %s", "argument.time.invalid_tick_count": "La cuenta de ciclos debe ser no negativa", "argument.time.invalid_unit": "Unidad no válida", "argument.time.tick_count_too_low": "El número de ciclos no puede ser inferior a %s, encontrado: %s", "argument.uuid.invalid": "UUID no válido", "argument.waypoint.invalid": "La entidad seleccionada no es un punto de referencia", "arguments.block.tag.unknown": "Etiqueta de bloque desconocida: %s", "arguments.function.tag.unknown": "Etiqueta de función desconocida: %s", "arguments.function.unknown": "Función desconocida: %s", "arguments.item.component.expected": "Componente de objeto esperado", "arguments.item.component.malformed": "Componente '%s' malformado: '%s'", "arguments.item.component.repeated": "El componente del objeto ‘%s’ se repitió, pero solo se puede especificar un valor", "arguments.item.component.unknown": "Componente de objeto desconocido '%s'", "arguments.item.malformed": "Objeto mal formado: %s", "arguments.item.overstacked": "%s sólo puede formar un stack de hasta %s", "arguments.item.predicate.malformed": "Deformado: %s Predicado: %s", "arguments.item.predicate.unknown": "Predicado de Objeto Desconocido %s", "arguments.item.tag.unknown": "Etiqueta de ítem desconocida: %s", "arguments.nbtpath.node.invalid": "Ruta NBT no válida", "arguments.nbtpath.nothing_found": "No hay elementos que coincidan con %s", "arguments.nbtpath.too_deep": "La información NBT resultante está anidada muy profundamente", "arguments.nbtpath.too_large": "La cantidad de información NBT es muy grande", "arguments.objective.notFound": "Objetivo de tabla de puntaje desconocido: %s", "arguments.objective.readonly": "El objetivo \"%s\" de la tabla de puntaje sólo es de lectura", "arguments.operation.div0": "No se puede dividir por cero", "arguments.operation.invalid": "Operación no válida", "arguments.swizzle.invalid": "Ejes no válidos: se requiere la combinación de \"X\", \"Y\" y \"Z\"", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%2$s: %1$s%%", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "%2$s: +%1$s", "attribute.modifier.plus.1": "%2$s: +%1$s%%", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "%2$s: -%1$s", "attribute.modifier.take.1": "%2$s: -%1$s%%", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON><PERSON>", "attribute.name.armor_toughness": "Resistencia de armadura", "attribute.name.attack_damage": "Daño de ataque", "attribute.name.attack_knockback": "Empuje de ataque", "attribute.name.attack_speed": "Velocidad de ataque", "attribute.name.block_break_speed": "Velocidad de ruptura de bloques", "attribute.name.block_interaction_range": "Rango de interacción de bloques", "attribute.name.burning_time": "Duración de las quemaduras", "attribute.name.camera_distance": "Distancia de la cámara", "attribute.name.entity_interaction_range": "Rango de interacción de entidades", "attribute.name.explosion_knockback_resistance": "Resistencia al empuje de las explosiones", "attribute.name.fall_damage_multiplier": "Multiplicador de daño de caída", "attribute.name.flying_speed": "Velocidad de vuelo", "attribute.name.follow_range": "<PERSON><PERSON>", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "Resistencia de armadura", "attribute.name.generic.attack_damage": "<PERSON><PERSON> por golpe", "attribute.name.generic.attack_knockback": "Empuje de ataque", "attribute.name.generic.attack_speed": "Velocidad de ataque", "attribute.name.generic.block_interaction_range": "Rango de interacción con bloques", "attribute.name.generic.burning_time": "Duración de Quemadura", "attribute.name.generic.entity_interaction_range": "Rango de interacción con entidades", "attribute.name.generic.explosion_knockback_resistance": "Resistencia al empuje por Explosiones", "attribute.name.generic.fall_damage_multiplier": "Multiplicador de daño por caída", "attribute.name.generic.flying_speed": "Velocidad de vuelo", "attribute.name.generic.follow_range": "<PERSON><PERSON>", "attribute.name.generic.gravity": "Gravedad", "attribute.name.generic.jump_strength": "Intensidad del salto", "attribute.name.generic.knockback_resistance": "Resistencia al empuje", "attribute.name.generic.luck": "<PERSON><PERSON>", "attribute.name.generic.max_absorption": "Absorción máxima", "attribute.name.generic.max_health": "<PERSON><PERSON> m<PERSON>xi<PERSON>", "attribute.name.generic.movement_efficiency": "Eficiencia del Movimiento", "attribute.name.generic.movement_speed": "Velocidad", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON> de oxígeno", "attribute.name.generic.safe_fall_distance": "Distancia de caída segura", "attribute.name.generic.scale": "Escala", "attribute.name.generic.step_height": "Altura de salto", "attribute.name.generic.water_movement_efficiency": "Eficiencia de movimiento acuático", "attribute.name.gravity": "Gravedad", "attribute.name.horse.jump_strength": "Fuerza de salto", "attribute.name.jump_strength": "Fuerza de salto", "attribute.name.knockback_resistance": "Resistencia a empuje", "attribute.name.luck": "<PERSON><PERSON>", "attribute.name.max_absorption": "Absorción máxima", "attribute.name.max_health": "<PERSON><PERSON> m<PERSON>xi<PERSON>", "attribute.name.mining_efficiency": "Eficiencia al minar", "attribute.name.movement_efficiency": "Eficiencia de movimiento", "attribute.name.movement_speed": "Velocidad", "attribute.name.oxygen_bonus": "Oxígeno extra", "attribute.name.player.block_break_speed": "Velocidad al romper bloques", "attribute.name.player.block_interaction_range": "Rango de interacción con bloques", "attribute.name.player.entity_interaction_range": "Rango de interacción con entidades", "attribute.name.player.mining_efficiency": "Eficiencia de Minería", "attribute.name.player.sneaking_speed": "Velocid<PERSON> sigilosa", "attribute.name.player.submerged_mining_speed": "Velocidad al Minar bajo el agua", "attribute.name.player.sweeping_damage_ratio": "Proporción del Daño por Barrido", "attribute.name.safe_fall_distance": "Distancia de caída segura", "attribute.name.scale": "Escala", "attribute.name.sneaking_speed": "Velocidad al agacharse", "attribute.name.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>i", "attribute.name.step_height": "Altura de paso", "attribute.name.submerged_mining_speed": "Velocidad al minar bajo el agua", "attribute.name.sweeping_damage_ratio": "Proporción del daño de barrido", "attribute.name.tempt_range": "Rango de atracción de criaturas", "attribute.name.water_movement_efficiency": "Eficiencia del movimiento acuático", "attribute.name.waypoint_receive_range": "Rango de recepción de waypoints", "attribute.name.waypoint_transmit_range": "Rango de transmisión de waypoints", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>i", "biome.minecraft.badlands": "Badlands", "biome.minecraft.bamboo_jungle": "<PERSON><PERSON>", "biome.minecraft.basalt_deltas": "Deltas de basalto", "biome.minecraft.beach": "Playa", "biome.minecraft.birch_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.cherry_grove": "Cerezal", "biome.minecraft.cold_ocean": "Océano frío", "biome.minecraft.crimson_forest": "<PERSON><PERSON>", "biome.minecraft.dark_forest": "Bosque oscuro", "biome.minecraft.deep_cold_ocean": "Océano frío profundo", "biome.minecraft.deep_dark": "Oscuridad profunda", "biome.minecraft.deep_frozen_ocean": "Océano congelado profundo", "biome.minecraft.deep_lukewarm_ocean": "Océano tibio profundo", "biome.minecraft.deep_ocean": "Océano profundo", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "<PERSON><PERSON><PERSON>", "biome.minecraft.end_barrens": "El End - Zona árida", "biome.minecraft.end_highlands": "El End - Zona elevada", "biome.minecraft.end_midlands": "El End - Zona central", "biome.minecraft.eroded_badlands": "Badlands erosionadas", "biome.minecraft.flower_forest": "Bosque floral", "biome.minecraft.forest": "Bosque", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON> congelado", "biome.minecraft.frozen_peaks": "Cumbres heladas", "biome.minecraft.frozen_river": "Río congelado", "biome.minecraft.grove": "Arboleda", "biome.minecraft.ice_spikes": "Picos de hielo", "biome.minecraft.jagged_peaks": "Cumbres escarpadas", "biome.minecraft.jungle": "<PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "Océano tibio", "biome.minecraft.lush_caves": "<PERSON><PERSON>s fron<PERSON>", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON>", "biome.minecraft.meadow": "Prado", "biome.minecraft.mushroom_fields": "Campo fúngico", "biome.minecraft.nether_wastes": "Desiertos del Nether", "biome.minecraft.ocean": "<PERSON><PERSON>ano", "biome.minecraft.old_growth_birch_forest": "Abedular ancestral", "biome.minecraft.old_growth_pine_taiga": "<PERSON><PERSON> de pinos ancestral", "biome.minecraft.old_growth_spruce_taiga": "Bosque de pinos ancestral", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON>", "biome.minecraft.plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.river": "Río", "biome.minecraft.savanna": "Sabana", "biome.minecraft.savanna_plateau": "Meseta de sabana", "biome.minecraft.small_end_islands": "El End - <PERSON><PERSON>", "biome.minecraft.snowy_beach": "Playa nevada", "biome.minecraft.snowy_plains": "<PERSON><PERSON><PERSON> nevada", "biome.minecraft.snowy_slopes": "Laderas nevadas", "biome.minecraft.snowy_taiga": "Taiga nevada", "biome.minecraft.soul_sand_valley": "Valle de almas", "biome.minecraft.sparse_jungle": "Jungla dispersa", "biome.minecraft.stony_peaks": "Cumbres rocosas", "biome.minecraft.stony_shore": "Costa rocosa", "biome.minecraft.sunflower_plains": "Llanuras de girasoles", "biome.minecraft.swamp": "Pantano", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "El End", "biome.minecraft.the_void": "El vacío", "biome.minecraft.warm_ocean": "Océano <PERSON>", "biome.minecraft.warped_forest": "Bosque distorsionado", "biome.minecraft.windswept_forest": "<PERSON><PERSON> ventiscoso", "biome.minecraft.windswept_gravelly_hills": "Colinas pedregosas ventiscosas", "biome.minecraft.windswept_hills": "Colinas ventiscosas", "biome.minecraft.windswept_savanna": "Sabana ventiscosa", "biome.minecraft.wooded_badlands": "Páramo frondoso", "block.minecraft.acacia_button": "Botón de acacia", "block.minecraft.acacia_door": "Puerta de acacia", "block.minecraft.acacia_fence": "Cerca de acacia", "block.minecraft.acacia_fence_gate": "Puerta de cerca de acacia", "block.minecraft.acacia_hanging_sign": "Cartel colgante de acacia", "block.minecraft.acacia_leaves": "Hojas de acacia", "block.minecraft.acacia_log": "Tronco de acacia", "block.minecraft.acacia_planks": "Madera de acacia", "block.minecraft.acacia_pressure_plate": "Placa de presión de acacia", "block.minecraft.acacia_sapling": "Arbolito de acacia", "block.minecraft.acacia_sign": "Cartel de acacia", "block.minecraft.acacia_slab": "Losa de acacia", "block.minecraft.acacia_stairs": "Escaleras de acacia", "block.minecraft.acacia_trapdoor": "Escotilla de acacia", "block.minecraft.acacia_wall_hanging_sign": "Cartel colgante de acacia en pared", "block.minecraft.acacia_wall_sign": "Cartel de acacia en pared", "block.minecraft.acacia_wood": "Leño de acacia", "block.minecraft.activator_rail": "Carril activador", "block.minecraft.air": "Aire", "block.minecraft.allium": "Allium", "block.minecraft.amethyst_block": "Bloque de amatista", "block.minecraft.amethyst_cluster": "<PERSON><PERSON><PERSON><PERSON> de amatista", "block.minecraft.ancient_debris": "Escombros ancestrales", "block.minecraft.andesite": "Andesita", "block.minecraft.andesite_slab": "Losa de andesita", "block.minecraft.andesite_stairs": "Escaleras de andesita", "block.minecraft.andesite_wall": "Muro de andesita", "block.minecraft.anvil": "Yun<PERSON>", "block.minecraft.attached_melon_stem": "Tallo de patilla unido", "block.minecraft.attached_pumpkin_stem": "Tallo de auyama unido", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Hojas de azalea", "block.minecraft.azure_bluet": "Rubiácea", "block.minecraft.bamboo": "Bambú", "block.minecraft.bamboo_block": "Bloque de bam<PERSON>ú", "block.minecraft.bamboo_button": "Botón de bam<PERSON>ú", "block.minecraft.bamboo_door": "<PERSON><PERSON><PERSON> de b<PERSON>ú", "block.minecraft.bamboo_fence": "Cerca de bambú", "block.minecraft.bamboo_fence_gate": "Puerta de cerca de bambú", "block.minecraft.bamboo_hanging_sign": "Cartel colgante de bambú", "block.minecraft.bamboo_mosaic": "Mosaico de bam<PERSON>ú", "block.minecraft.bamboo_mosaic_slab": "Losa de mosaico de bam<PERSON>ú", "block.minecraft.bamboo_mosaic_stairs": "Escaleras de mosaico de bambú", "block.minecraft.bamboo_planks": "<PERSON><PERSON>", "block.minecraft.bamboo_pressure_plate": "Placa de presión de bambú", "block.minecraft.bamboo_sapling": "<PERSON><PERSON>", "block.minecraft.bamboo_sign": "Cartel de bambú", "block.minecraft.bamboo_slab": "Losa de bam<PERSON>ú", "block.minecraft.bamboo_stairs": "Escaleras de bambú", "block.minecraft.bamboo_trapdoor": "Escotilla de bam<PERSON>ú", "block.minecraft.bamboo_wall_hanging_sign": "Cartel colgante de bambú en pared", "block.minecraft.bamboo_wall_sign": "Cartel de bambú en pared", "block.minecraft.banner.base.black": "Paño completamente negro", "block.minecraft.banner.base.blue": "Paño completamente azul", "block.minecraft.banner.base.brown": "<PERSON><PERSON> completamente marr<PERSON>", "block.minecraft.banner.base.cyan": "Paño completamente turquesa", "block.minecraft.banner.base.gray": "Pa<PERSON> completamente gris", "block.minecraft.banner.base.green": "Paño completamente verde", "block.minecraft.banner.base.light_blue": "Paño completamente azul cielo", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON> completamente gris claro", "block.minecraft.banner.base.lime": "Paño completamente verde lima", "block.minecraft.banner.base.magenta": "Paño completamente magenta", "block.minecraft.banner.base.orange": "Paño completamente naranja", "block.minecraft.banner.base.pink": "<PERSON><PERSON> completamente rosa", "block.minecraft.banner.base.purple": "Paño completamente morado", "block.minecraft.banner.base.red": "Paño completamente rojo", "block.minecraft.banner.base.white": "Paño completamente blanco", "block.minecraft.banner.base.yellow": "<PERSON><PERSON> completamente amarillo", "block.minecraft.banner.border.black": "Bordura negra", "block.minecraft.banner.border.blue": "Bordura azul", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.border.cyan": "Bordura turquesa", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.border.green": "Bordura verde", "block.minecraft.banner.border.light_blue": "Bordura azul cielo", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.banner.border.lime": "Bordura verde limón", "block.minecraft.banner.border.magenta": "Bordura magenta", "block.minecraft.banner.border.orange": "<PERSON><PERSON><PERSON> naranja", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.border.purple": "Bordura morada", "block.minecraft.banner.border.red": "Bord<PERSON> roja", "block.minecraft.banner.border.white": "<PERSON>rd<PERSON> blanca", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON> amarilla", "block.minecraft.banner.bricks.black": "Mazonado negro", "block.minecraft.banner.bricks.blue": "Mazonado azul", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.banner.bricks.cyan": "Mazonado turquesa", "block.minecraft.banner.bricks.gray": "Mazonado gris", "block.minecraft.banner.bricks.green": "Mazonado verde", "block.minecraft.banner.bricks.light_blue": "Mazonado azul cielo", "block.minecraft.banner.bricks.light_gray": "Mazonado gris claro", "block.minecraft.banner.bricks.lime": "Mazonado verde limón", "block.minecraft.banner.bricks.magenta": "Mazonado magenta", "block.minecraft.banner.bricks.orange": "Ma<PERSON>ado naranja", "block.minecraft.banner.bricks.pink": "Mazonado rosa", "block.minecraft.banner.bricks.purple": "Mazonado morado", "block.minecraft.banner.bricks.red": "Mazonado rojo", "block.minecraft.banner.bricks.white": "Mazonado blanco", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON><PERSON> am<PERSON>", "block.minecraft.banner.circle.black": "<PERSON><PERSON> negro", "block.minecraft.banner.circle.blue": "<PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.circle.green": "<PERSON><PERSON> verde", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON> a<PERSON>l cielo", "block.minecraft.banner.circle.light_gray": "<PERSON><PERSON> gris claro", "block.minecraft.banner.circle.lime": "<PERSON><PERSON> verde limón", "block.minecraft.banner.circle.magenta": "Roel magenta", "block.minecraft.banner.circle.orange": "<PERSON><PERSON>", "block.minecraft.banner.circle.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.circle.purple": "<PERSON><PERSON> morado", "block.minecraft.banner.circle.red": "<PERSON><PERSON> rojo", "block.minecraft.banner.circle.white": "<PERSON><PERSON> blan<PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON>", "block.minecraft.banner.creeper.black": "Estampado negro de creeper", "block.minecraft.banner.creeper.blue": "Estampado azul de creeper", "block.minecraft.banner.creeper.brown": "Estampado marr<PERSON> de creeper", "block.minecraft.banner.creeper.cyan": "Estampado turquesa de creeper", "block.minecraft.banner.creeper.gray": "Estampado gris de creeper", "block.minecraft.banner.creeper.green": "Estampado verde de creeper", "block.minecraft.banner.creeper.light_blue": "Estampado azul cielo de creeper", "block.minecraft.banner.creeper.light_gray": "Estampado gris claro de creeper", "block.minecraft.banner.creeper.lime": "Estampado verde limón de creeper", "block.minecraft.banner.creeper.magenta": "Estampado magenta de creeper", "block.minecraft.banner.creeper.orange": "Estampado naranja de creeper", "block.minecraft.banner.creeper.pink": "Estampado rosa de creeper", "block.minecraft.banner.creeper.purple": "Estampado morado de creeper", "block.minecraft.banner.creeper.red": "Estampado rojo de creeper", "block.minecraft.banner.creeper.white": "Estampado blanco de creeper", "block.minecraft.banner.creeper.yellow": "Estampado amariillo de creeper", "block.minecraft.banner.cross.black": "<PERSON><PERSON> negra", "block.minecraft.banner.cross.blue": "<PERSON><PERSON> azul", "block.minecraft.banner.cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.cross.cyan": "Aspa turquesa", "block.minecraft.banner.cross.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.cross.green": "<PERSON>pa verde", "block.minecraft.banner.cross.light_blue": "Aspa azul cielo", "block.minecraft.banner.cross.light_gray": "<PERSON><PERSON> gris claro", "block.minecraft.banner.cross.lime": "Aspa verde limón", "block.minecraft.banner.cross.magenta": "Aspa magenta", "block.minecraft.banner.cross.orange": "<PERSON><PERSON> naranja", "block.minecraft.banner.cross.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.cross.purple": "Aspa morada", "block.minecraft.banner.cross.red": "<PERSON><PERSON> roja", "block.minecraft.banner.cross.white": "As<PERSON> blanca", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON> amarilla", "block.minecraft.banner.curly_border.black": "Bordura negra dentada", "block.minecraft.banner.curly_border.blue": "Bordura azul dentada", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> marr<PERSON> dentada", "block.minecraft.banner.curly_border.cyan": "Bordura turquesa dentada", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON> gris dentada", "block.minecraft.banner.curly_border.green": "<PERSON>rd<PERSON> verde dentada", "block.minecraft.banner.curly_border.light_blue": "Bordura azul cielo dentada", "block.minecraft.banner.curly_border.light_gray": "<PERSON>rdura gris claro dentada", "block.minecraft.banner.curly_border.lime": "Bordura verde limón dentada", "block.minecraft.banner.curly_border.magenta": "Bordura magenta dentada", "block.minecraft.banner.curly_border.orange": "<PERSON>rd<PERSON> naranja dentada", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON> rosa dentada", "block.minecraft.banner.curly_border.purple": "<PERSON>rd<PERSON> morada dentada", "block.minecraft.banner.curly_border.red": "<PERSON>rd<PERSON> roja dentada", "block.minecraft.banner.curly_border.white": "Bordura blanca dentada", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON><PERSON> amarilla dentada", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON><PERSON> negro", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON> verde", "block.minecraft.banner.diagonal_left.light_blue": "<PERSON><PERSON><PERSON> a<PERSON> cielo", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.banner.diagonal_left.lime": "Tajado verde limón", "block.minecraft.banner.diagonal_left.magenta": "Tajado magenta", "block.minecraft.banner.diagonal_left.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON><PERSON> morado", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.black": "Tronchado negro", "block.minecraft.banner.diagonal_right.blue": "Tron<PERSON>do <PERSON>l", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.cyan": "Tronchado turquesa", "block.minecraft.banner.diagonal_right.gray": "Tronchado gris", "block.minecraft.banner.diagonal_right.green": "Tronchado verde", "block.minecraft.banner.diagonal_right.light_blue": "Tronchado azul cielo", "block.minecraft.banner.diagonal_right.light_gray": "Tronchado gris claro", "block.minecraft.banner.diagonal_right.lime": "Tronchado verde limón", "block.minecraft.banner.diagonal_right.magenta": "Tronchado magenta", "block.minecraft.banner.diagonal_right.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.diagonal_right.purple": "Tron<PERSON><PERSON> morado", "block.minecraft.banner.diagonal_right.red": "Tron<PERSON>do rojo", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.black": "Tronchado negro invertido", "block.minecraft.banner.diagonal_up_left.blue": "Tronchado azul invertido", "block.minecraft.banner.diagonal_up_left.brown": "Tronchado marrón invertido", "block.minecraft.banner.diagonal_up_left.cyan": "Tronchado turquesa invertido", "block.minecraft.banner.diagonal_up_left.gray": "Tronchado gris invertido", "block.minecraft.banner.diagonal_up_left.green": "Tronchado verde invertido", "block.minecraft.banner.diagonal_up_left.light_blue": "Tronchado azul cielo invertido", "block.minecraft.banner.diagonal_up_left.light_gray": "Tronchado gris claro invertido", "block.minecraft.banner.diagonal_up_left.lime": "Tronchado verde limón invertido", "block.minecraft.banner.diagonal_up_left.magenta": "Tronchado magenta invertido", "block.minecraft.banner.diagonal_up_left.orange": "Tronchado naranja invertido", "block.minecraft.banner.diagonal_up_left.pink": "Tronchado rosa invertido", "block.minecraft.banner.diagonal_up_left.purple": "Tronchado morado invertido", "block.minecraft.banner.diagonal_up_left.red": "Tronchado rojo invertido", "block.minecraft.banner.diagonal_up_left.white": "Tronchado blanco invertido", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON><PERSON><PERSON> amarillo invertido", "block.minecraft.banner.diagonal_up_right.black": "Tajado negro invertido", "block.minecraft.banner.diagonal_up_right.blue": "Tajado a<PERSON>l invertido", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON><PERSON> ma<PERSON> invertido", "block.minecraft.banner.diagonal_up_right.cyan": "Tajado turquesa invertido", "block.minecraft.banner.diagonal_up_right.gray": "Tajado gris invertido", "block.minecraft.banner.diagonal_up_right.green": "Tajado verde invertido", "block.minecraft.banner.diagonal_up_right.light_blue": "<PERSON><PERSON>do azul cielo invertido", "block.minecraft.banner.diagonal_up_right.light_gray": "<PERSON><PERSON>do gris claro invertido", "block.minecraft.banner.diagonal_up_right.lime": "Tajado verde limón invertido", "block.minecraft.banner.diagonal_up_right.magenta": "Tajado magenta invertido", "block.minecraft.banner.diagonal_up_right.orange": "<PERSON><PERSON><PERSON> na<PERSON>ja invertido", "block.minecraft.banner.diagonal_up_right.pink": "Ta<PERSON>do rosa invertido", "block.minecraft.banner.diagonal_up_right.purple": "Ta<PERSON>do morado invertido", "block.minecraft.banner.diagonal_up_right.red": "Tajado rojo invertido", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON> blanco invertido", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON> am<PERSON>llo invertido", "block.minecraft.banner.flow.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON><PERSON> tur<PERSON>", "block.minecraft.banner.flow.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.green": "Flujo Verde", "block.minecraft.banner.flow.light_blue": "Flujo <PERSON>", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.black": "Estampado negro de flor", "block.minecraft.banner.flower.blue": "Estampado azul de flor", "block.minecraft.banner.flower.brown": "Estampado marrón de flor", "block.minecraft.banner.flower.cyan": "Estampado turquesa de flor", "block.minecraft.banner.flower.gray": "Estampado gris de flor", "block.minecraft.banner.flower.green": "Estampado verde de flor", "block.minecraft.banner.flower.light_blue": "Estampado azul cielo de flor", "block.minecraft.banner.flower.light_gray": "Estampado gris claro de flor", "block.minecraft.banner.flower.lime": "Estampado verde limón de flor", "block.minecraft.banner.flower.magenta": "Estampado magenta de flor", "block.minecraft.banner.flower.orange": "Estampado naranja de flor", "block.minecraft.banner.flower.pink": "Estampado rosa de flor", "block.minecraft.banner.flower.purple": "Estampado morado de flor", "block.minecraft.banner.flower.red": "Estampado rojo de flor", "block.minecraft.banner.flower.white": "Estampado blanco de flor", "block.minecraft.banner.flower.yellow": "Estampado amarillo de flor", "block.minecraft.banner.globe.black": "Planeta negro", "block.minecraft.banner.globe.blue": "Planeta azul", "block.minecraft.banner.globe.brown": "Planeta marrón", "block.minecraft.banner.globe.cyan": "Planeta turquesa", "block.minecraft.banner.globe.gray": "Planeta gris", "block.minecraft.banner.globe.green": "Planeta verde", "block.minecraft.banner.globe.light_blue": "Planeta azul cielo", "block.minecraft.banner.globe.light_gray": "Planeta gris claro", "block.minecraft.banner.globe.lime": "Planeta verde limón", "block.minecraft.banner.globe.magenta": "Planeta magenta", "block.minecraft.banner.globe.orange": "Planeta naranja", "block.minecraft.banner.globe.pink": "Planeta rosa", "block.minecraft.banner.globe.purple": "Planeta morado", "block.minecraft.banner.globe.red": "Planeta rojo", "block.minecraft.banner.globe.white": "Planeta blanco", "block.minecraft.banner.globe.yellow": "Planeta amarillo", "block.minecraft.banner.gradient.black": "Gradiente negro", "block.minecraft.banner.gradient.blue": "Gradiente azul", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "Gradiente turquesa", "block.minecraft.banner.gradient.gray": "Gradiente gris", "block.minecraft.banner.gradient.green": "Gradiente verde", "block.minecraft.banner.gradient.light_blue": "Gradiente azul cielo", "block.minecraft.banner.gradient.light_gray": "Grad<PERSON>e gris claro", "block.minecraft.banner.gradient.lime": "Gradiente verde limón", "block.minecraft.banner.gradient.magenta": "Gradiente magenta", "block.minecraft.banner.gradient.orange": "Gradiente naranja", "block.minecraft.banner.gradient.pink": "Gradiente rosa", "block.minecraft.banner.gradient.purple": "Gradiente morado", "block.minecraft.banner.gradient.red": "Gradiente rojo", "block.minecraft.banner.gradient.white": "Gradiente blanco", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON><PERSON> am<PERSON>llo", "block.minecraft.banner.gradient_up.black": "Gradiente negro en base", "block.minecraft.banner.gradient_up.blue": "Gradiente azul en base", "block.minecraft.banner.gradient_up.brown": "Gradiente marrón en base", "block.minecraft.banner.gradient_up.cyan": "Gradiente turquesa en campaña", "block.minecraft.banner.gradient_up.gray": "Gradiente gris en base", "block.minecraft.banner.gradient_up.green": "Gradiente verde en base", "block.minecraft.banner.gradient_up.light_blue": "Gradiente azul cielo en base", "block.minecraft.banner.gradient_up.light_gray": "Gradiente gris claro en base", "block.minecraft.banner.gradient_up.lime": "Gradiente verde limón en base", "block.minecraft.banner.gradient_up.magenta": "Gradiente magenta en base", "block.minecraft.banner.gradient_up.orange": "Gradiente naranja en base", "block.minecraft.banner.gradient_up.pink": "Gradiente rosa en base", "block.minecraft.banner.gradient_up.purple": "Gradiente morado en base", "block.minecraft.banner.gradient_up.red": "Gradiente rojo en base", "block.minecraft.banner.gradient_up.white": "Gradiente blanco en base", "block.minecraft.banner.gradient_up.yellow": "Gradiente amarillo en base", "block.minecraft.banner.guster.black": "Ventisca Negra", "block.minecraft.banner.guster.blue": "Ventisca Azul", "block.minecraft.banner.guster.brown": "Ventisca Marrón", "block.minecraft.banner.guster.cyan": "Ventisca Turquesa", "block.minecraft.banner.guster.gray": "Ventisca <PERSON>", "block.minecraft.banner.guster.green": "Ventisca Verde", "block.minecraft.banner.guster.light_blue": "Ventisca Azul Claro", "block.minecraft.banner.guster.light_gray": "Ventisca G<PERSON>", "block.minecraft.banner.guster.lime": "Ventisca Lima", "block.minecraft.banner.guster.magenta": "Ventisca <PERSON>", "block.minecraft.banner.guster.orange": "Ventisca Naranja", "block.minecraft.banner.guster.pink": "Ventisca Rosa", "block.minecraft.banner.guster.purple": "Ventisca Morada", "block.minecraft.banner.guster.red": "Ventisca Roja", "block.minecraft.banner.guster.white": "Ventisca Blanca", "block.minecraft.banner.guster.yellow": "Ventisca Amarilla", "block.minecraft.banner.half_horizontal.black": "Cortado negro", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON> a<PERSON>l", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.cyan": "Cortado turquesa", "block.minecraft.banner.half_horizontal.gray": "Cortado gris", "block.minecraft.banner.half_horizontal.green": "Cortado verde", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON>do azul cielo", "block.minecraft.banner.half_horizontal.light_gray": "Cortado gris claro", "block.minecraft.banner.half_horizontal.lime": "Cortado verde limón", "block.minecraft.banner.half_horizontal.magenta": "Cortado magenta", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON>do morado", "block.minecraft.banner.half_horizontal.red": "<PERSON>rtado rojo", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.black": "Cortado negro invertido", "block.minecraft.banner.half_horizontal_bottom.blue": "Cortado azul invertido", "block.minecraft.banner.half_horizontal_bottom.brown": "Cortado marrón invertido", "block.minecraft.banner.half_horizontal_bottom.cyan": "Cortado turquesa invertido", "block.minecraft.banner.half_horizontal_bottom.gray": "Cortado gris invertido", "block.minecraft.banner.half_horizontal_bottom.green": "Cortado verde invertido", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Cortado azul cielo invertido", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Cortado gris claro invertido", "block.minecraft.banner.half_horizontal_bottom.lime": "Cortado verde limón invertido", "block.minecraft.banner.half_horizontal_bottom.magenta": "Cortado magenta invertido", "block.minecraft.banner.half_horizontal_bottom.orange": "Cortado naranja invertido", "block.minecraft.banner.half_horizontal_bottom.pink": "Cortado rosa invertido", "block.minecraft.banner.half_horizontal_bottom.purple": "Cortado morado invertido", "block.minecraft.banner.half_horizontal_bottom.red": "Cortado rojo invertido", "block.minecraft.banner.half_horizontal_bottom.white": "Cortado blanco invertido", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON><PERSON> amarillo invertido", "block.minecraft.banner.half_vertical.black": "Flanco negro", "block.minecraft.banner.half_vertical.blue": "Flanco azul", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.cyan": "Partido turquesa", "block.minecraft.banner.half_vertical.gray": "Flanco gris", "block.minecraft.banner.half_vertical.green": "Flanco verde", "block.minecraft.banner.half_vertical.light_blue": "Flanco azul cielo", "block.minecraft.banner.half_vertical.light_gray": "Flanco gris claro a diestra", "block.minecraft.banner.half_vertical.lime": "Flanco verde limón", "block.minecraft.banner.half_vertical.magenta": "Flanco magenta", "block.minecraft.banner.half_vertical.orange": "Flanco <PERSON>", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.half_vertical.purple": "<PERSON>lanco morado", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.banner.half_vertical.white": "Flanco blanco", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.black": "Flanco negro invertido", "block.minecraft.banner.half_vertical_right.blue": "Flanco azul invertido", "block.minecraft.banner.half_vertical_right.brown": "Flanco ma<PERSON>ón invertido", "block.minecraft.banner.half_vertical_right.cyan": "Partido turquesa invertido", "block.minecraft.banner.half_vertical_right.gray": "Flanco gris invertido", "block.minecraft.banner.half_vertical_right.green": "Flanco verde invertido", "block.minecraft.banner.half_vertical_right.light_blue": "Flanco azul cielo invertido", "block.minecraft.banner.half_vertical_right.light_gray": "Flanco gris claro invertido", "block.minecraft.banner.half_vertical_right.lime": "Flanco verde limón invertido", "block.minecraft.banner.half_vertical_right.magenta": "Flanco magenta invertido", "block.minecraft.banner.half_vertical_right.orange": "Flanco naranja invertido", "block.minecraft.banner.half_vertical_right.pink": "Flanco rosa invertido", "block.minecraft.banner.half_vertical_right.purple": "Flanco morado invertido", "block.minecraft.banner.half_vertical_right.red": "Flanco rojo invertido", "block.minecraft.banner.half_vertical_right.white": "Flanco blanco invertido", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON>co amarillo invertido", "block.minecraft.banner.mojang.black": "Cosa negra", "block.minecraft.banner.mojang.blue": "Cosa a<PERSON>l", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "Cosa turquesa", "block.minecraft.banner.mojang.gray": "<PERSON>sa gris", "block.minecraft.banner.mojang.green": "Cosa verde", "block.minecraft.banner.mojang.light_blue": "Cosa azul cielo", "block.minecraft.banner.mojang.light_gray": "<PERSON>sa gris claro", "block.minecraft.banner.mojang.lime": "Cosa verde limón", "block.minecraft.banner.mojang.magenta": "Cosa magenta", "block.minecraft.banner.mojang.orange": "Cosa naranja", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.mojang.purple": "Cosa morada", "block.minecraft.banner.mojang.red": "Cosa roja", "block.minecraft.banner.mojang.white": "Cosa blanca", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON> amarilla", "block.minecraft.banner.piglin.black": "Hocico negro", "block.minecraft.banner.piglin.blue": "Hocico azul", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "Hocico tur<PERSON>", "block.minecraft.banner.piglin.gray": "Hocico gris", "block.minecraft.banner.piglin.green": "Hocico verde", "block.minecraft.banner.piglin.light_blue": "Hocico azul cielo", "block.minecraft.banner.piglin.light_gray": "<PERSON>cico gris claro", "block.minecraft.banner.piglin.lime": "Hocico verde limón", "block.minecraft.banner.piglin.magenta": "Hocico magenta", "block.minecraft.banner.piglin.orange": "Hocico na<PERSON>", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON><PERSON> morado", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.black": "Rombo negro", "block.minecraft.banner.rhombus.blue": "Rombo azul", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "Rombo turquesa", "block.minecraft.banner.rhombus.gray": "Rombo gris", "block.minecraft.banner.rhombus.green": "Rombo verde", "block.minecraft.banner.rhombus.light_blue": "Rombo azul cielo", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON>o gris claro", "block.minecraft.banner.rhombus.lime": "Rombo verde limón", "block.minecraft.banner.rhombus.magenta": "Rombo magenta", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.rhombus.purple": "Rombo morado", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON>o rojo", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON>o blanco", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON> am<PERSON>", "block.minecraft.banner.skull.black": "Estampado negro de cráneo", "block.minecraft.banner.skull.blue": "Estampado azul de cráneo", "block.minecraft.banner.skull.brown": "Estampado marrón de cráneo", "block.minecraft.banner.skull.cyan": "Estampado turquesa de cráneo", "block.minecraft.banner.skull.gray": "Estampado gris de cráneo", "block.minecraft.banner.skull.green": "Estampado verde de cráneo", "block.minecraft.banner.skull.light_blue": "Estampado azul cielo de cráneo", "block.minecraft.banner.skull.light_gray": "Estampado gris claro de cráneo", "block.minecraft.banner.skull.lime": "Estampado verde limón de cráneo", "block.minecraft.banner.skull.magenta": "Estampado magenta de cráneo", "block.minecraft.banner.skull.orange": "Estampado naranja de cráneo", "block.minecraft.banner.skull.pink": "Estampado rosa de cráneo", "block.minecraft.banner.skull.purple": "Estampado morado de cráneo", "block.minecraft.banner.skull.red": "Estampado rojo de cráneo", "block.minecraft.banner.skull.white": "Estampado blanco de cráneo", "block.minecraft.banner.skull.yellow": "Estampado amarillo de cráneo", "block.minecraft.banner.small_stripes.black": "Bastonado negro", "block.minecraft.banner.small_stripes.blue": "Bastonado azul", "block.minecraft.banner.small_stripes.brown": "Bastonado marrón", "block.minecraft.banner.small_stripes.cyan": "Palado turquesa", "block.minecraft.banner.small_stripes.gray": "Bastonado gris", "block.minecraft.banner.small_stripes.green": "Bastonado verde", "block.minecraft.banner.small_stripes.light_blue": "Bastonado azul cielo", "block.minecraft.banner.small_stripes.light_gray": "Bastonado gris claro", "block.minecraft.banner.small_stripes.lime": "Bastonado verde limón", "block.minecraft.banner.small_stripes.magenta": "Bastonado magenta", "block.minecraft.banner.small_stripes.orange": "Bastonado naranja", "block.minecraft.banner.small_stripes.pink": "Bastonado rosa", "block.minecraft.banner.small_stripes.purple": "Bastonado morado", "block.minecraft.banner.small_stripes.red": "Bastonado rojo", "block.minecraft.banner.small_stripes.white": "Bastonado blanco", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON><PERSON> amarillo", "block.minecraft.banner.square_bottom_left.black": "Cantón negro en base a diestra", "block.minecraft.banner.square_bottom_left.blue": "Cantón azul en base a diestra", "block.minecraft.banner.square_bottom_left.brown": "Cantón marrón en base a diestra", "block.minecraft.banner.square_bottom_left.cyan": "Cantón turquesa en base a diestra", "block.minecraft.banner.square_bottom_left.gray": "Cantón gris en base a diestra", "block.minecraft.banner.square_bottom_left.green": "Cantón verde en base a diestra", "block.minecraft.banner.square_bottom_left.light_blue": "Cantón azul cielo en base a diestra", "block.minecraft.banner.square_bottom_left.light_gray": "Cantón gris claro en base a diestra", "block.minecraft.banner.square_bottom_left.lime": "Cantón verde limón en base a diestra", "block.minecraft.banner.square_bottom_left.magenta": "Cantón magenta en base a diestra", "block.minecraft.banner.square_bottom_left.orange": "Cantón naranja en base a diestra", "block.minecraft.banner.square_bottom_left.pink": "Cantón rosa en base a diestra", "block.minecraft.banner.square_bottom_left.purple": "Cantón morado en base a diestra", "block.minecraft.banner.square_bottom_left.red": "Cantón rojo en base a diestra", "block.minecraft.banner.square_bottom_left.white": "Cantón blanco en base a diestra", "block.minecraft.banner.square_bottom_left.yellow": "<PERSON>t<PERSON> amarillo en base a diestra", "block.minecraft.banner.square_bottom_right.black": "Cantón negro en base a siniestra", "block.minecraft.banner.square_bottom_right.blue": "Cantón azul en base a siniestra", "block.minecraft.banner.square_bottom_right.brown": "Cantón marrón en base a siniestra", "block.minecraft.banner.square_bottom_right.cyan": "Cantón turquesa en base a siniestra", "block.minecraft.banner.square_bottom_right.gray": "Cantón gris en base a siniestra", "block.minecraft.banner.square_bottom_right.green": "Cantón verde en base a siniestra", "block.minecraft.banner.square_bottom_right.light_blue": "Cantón azul cielo en base a siniestra", "block.minecraft.banner.square_bottom_right.light_gray": "Cantón gris claro en base a siniestra", "block.minecraft.banner.square_bottom_right.lime": "Cantón verde limón en base a siniestra", "block.minecraft.banner.square_bottom_right.magenta": "Cantón magenta en base a siniestra", "block.minecraft.banner.square_bottom_right.orange": "Cantón naranja en base a siniestra", "block.minecraft.banner.square_bottom_right.pink": "Cantón rosa en base a siniestra", "block.minecraft.banner.square_bottom_right.purple": "Cantón morado en base a siniestra", "block.minecraft.banner.square_bottom_right.red": "Cantón rojo en base a siniestra", "block.minecraft.banner.square_bottom_right.white": "Cantón blanco en base a siniestra", "block.minecraft.banner.square_bottom_right.yellow": "Cantón amarillo en base a siniestra", "block.minecraft.banner.square_top_left.black": "Cantón negro en jefe a diestra", "block.minecraft.banner.square_top_left.blue": "Cantón azul en jefe a diestra", "block.minecraft.banner.square_top_left.brown": "Cantón marrón en jefe a diestra", "block.minecraft.banner.square_top_left.cyan": "Cantón diestro turquesa en jefe", "block.minecraft.banner.square_top_left.gray": "Cantón gris en jefe a diestra", "block.minecraft.banner.square_top_left.green": "Cantón verde en jefe a diestra", "block.minecraft.banner.square_top_left.light_blue": "Cantón azul cielo en jefe a diestra", "block.minecraft.banner.square_top_left.light_gray": "Cantón gris claro en jefe a diestra", "block.minecraft.banner.square_top_left.lime": "Cantón verde limón en jefe a diestra", "block.minecraft.banner.square_top_left.magenta": "Cantón magenta en jefe a diestra", "block.minecraft.banner.square_top_left.orange": "Cantón naranja en jefe a diestra", "block.minecraft.banner.square_top_left.pink": "Cantón rosa en jefe a diestra", "block.minecraft.banner.square_top_left.purple": "Cantón morado en jefe a diestra", "block.minecraft.banner.square_top_left.red": "Cantón rojo en jefe a diestra", "block.minecraft.banner.square_top_left.white": "Cantón blanco en jefe a diestra", "block.minecraft.banner.square_top_left.yellow": "Cantón amarillo en jefe a diestra", "block.minecraft.banner.square_top_right.black": "Cantón negro en jefe a siniestra", "block.minecraft.banner.square_top_right.blue": "Cantón azul en jefe a siniestra", "block.minecraft.banner.square_top_right.brown": "Cantón marrón en jefe a siniestra", "block.minecraft.banner.square_top_right.cyan": "Cantón siniestro turquesa en jefe", "block.minecraft.banner.square_top_right.gray": "Cantón gris en jefe a siniestra", "block.minecraft.banner.square_top_right.green": "Cantón verde en jefe a siniestra", "block.minecraft.banner.square_top_right.light_blue": "Cantón azul cielo en jefe a siniestra", "block.minecraft.banner.square_top_right.light_gray": "Cantón gris claro en jefe a siniestra", "block.minecraft.banner.square_top_right.lime": "Cantón verde limón en jefe a siniestra", "block.minecraft.banner.square_top_right.magenta": "Cantón magenta en jefe a siniestra", "block.minecraft.banner.square_top_right.orange": "Cantón naranja en jefe a siniestra", "block.minecraft.banner.square_top_right.pink": "Cantón rosa en jefe a siniestra", "block.minecraft.banner.square_top_right.purple": "Cantón morado en jefe a siniestra", "block.minecraft.banner.square_top_right.red": "Cantón rojo en jefe a siniestra", "block.minecraft.banner.square_top_right.white": "Cantón blanco en jefe a siniestra", "block.minecraft.banner.square_top_right.yellow": "Cantón amarillo en jefe a siniestra", "block.minecraft.banner.straight_cross.black": "Cruz negra", "block.minecraft.banner.straight_cross.blue": "Cruz azul", "block.minecraft.banner.straight_cross.brown": "<PERSON>", "block.minecraft.banner.straight_cross.cyan": "Cruz turquesa", "block.minecraft.banner.straight_cross.gray": "<PERSON> gris", "block.minecraft.banner.straight_cross.green": "Cruz verde", "block.minecraft.banner.straight_cross.light_blue": "Cruz azul cielo", "block.minecraft.banner.straight_cross.light_gray": "<PERSON> gris claro", "block.minecraft.banner.straight_cross.lime": "Cruz verde limón", "block.minecraft.banner.straight_cross.magenta": "Cruz magenta", "block.minecraft.banner.straight_cross.orange": "<PERSON> naranja", "block.minecraft.banner.straight_cross.pink": "<PERSON> rosa", "block.minecraft.banner.straight_cross.purple": "Cruz morada", "block.minecraft.banner.straight_cross.red": "Cruz roja", "block.minecraft.banner.straight_cross.white": "Cruz blanca", "block.minecraft.banner.straight_cross.yellow": "<PERSON> amari<PERSON>", "block.minecraft.banner.stripe_bottom.black": "Campaña negra", "block.minecraft.banner.stripe_bottom.blue": "Campaña azul", "block.minecraft.banner.stripe_bottom.brown": "Campaña <PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "Campaña turquesa", "block.minecraft.banner.stripe_bottom.gray": "Campaña gris", "block.minecraft.banner.stripe_bottom.green": "Campaña verde", "block.minecraft.banner.stripe_bottom.light_blue": "Campaña azul cielo", "block.minecraft.banner.stripe_bottom.light_gray": "Campaña gris claro", "block.minecraft.banner.stripe_bottom.lime": "Campaña verde limón", "block.minecraft.banner.stripe_bottom.magenta": "Campaña magenta", "block.minecraft.banner.stripe_bottom.orange": "Campaña naranja", "block.minecraft.banner.stripe_bottom.pink": "Campaña rosa", "block.minecraft.banner.stripe_bottom.purple": "Campaña morada", "block.minecraft.banner.stripe_bottom.red": "Campaña roja", "block.minecraft.banner.stripe_bottom.white": "Campaña blanca", "block.minecraft.banner.stripe_bottom.yellow": "Campaña amarilla", "block.minecraft.banner.stripe_center.black": "<PERSON>lo negro", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON>l", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "<PERSON>lo tur<PERSON>a", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.stripe_center.green": "Palo verde", "block.minecraft.banner.stripe_center.light_blue": "<PERSON><PERSON> azul cielo", "block.minecraft.banner.stripe_center.light_gray": "<PERSON>lo gris claro", "block.minecraft.banner.stripe_center.lime": "Palo verde limón", "block.minecraft.banner.stripe_center.magenta": "Palo magenta", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.stripe_center.purple": "<PERSON>lo morado", "block.minecraft.banner.stripe_center.red": "<PERSON>lo rojo", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON> blanco", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.black": "Banda negra a siniestra", "block.minecraft.banner.stripe_downleft.blue": "Banda azul a siniestra", "block.minecraft.banner.stripe_downleft.brown": "Banda marrón a siniestra", "block.minecraft.banner.stripe_downleft.cyan": "Barra turquesa", "block.minecraft.banner.stripe_downleft.gray": "Banda gris a siniestra", "block.minecraft.banner.stripe_downleft.green": "Banda verde a siniestra", "block.minecraft.banner.stripe_downleft.light_blue": "Banda azul cielo a siniestra", "block.minecraft.banner.stripe_downleft.light_gray": "Banda gris claro a siniestra", "block.minecraft.banner.stripe_downleft.lime": "Banda verde limón a siniestra", "block.minecraft.banner.stripe_downleft.magenta": "Banda magenta a siniestra", "block.minecraft.banner.stripe_downleft.orange": "Banda naranja a siniestra", "block.minecraft.banner.stripe_downleft.pink": "Banda rosa a siniestra", "block.minecraft.banner.stripe_downleft.purple": "Banda morada a siniestra", "block.minecraft.banner.stripe_downleft.red": "Banda roja a siniestra", "block.minecraft.banner.stripe_downleft.white": "Banda blanca a siniestra", "block.minecraft.banner.stripe_downleft.yellow": "Banda amarilla a siniestra", "block.minecraft.banner.stripe_downright.black": "Banda negra", "block.minecraft.banner.stripe_downright.blue": "Banda azul", "block.minecraft.banner.stripe_downright.brown": "Banda marrón", "block.minecraft.banner.stripe_downright.cyan": "Banda turquesa", "block.minecraft.banner.stripe_downright.gray": "Banda gris", "block.minecraft.banner.stripe_downright.green": "Banda verde", "block.minecraft.banner.stripe_downright.light_blue": "Banda azul cielo", "block.minecraft.banner.stripe_downright.light_gray": "Banda gris claro", "block.minecraft.banner.stripe_downright.lime": "Banda verde limón", "block.minecraft.banner.stripe_downright.magenta": "Banda magenta", "block.minecraft.banner.stripe_downright.orange": "Banda naranja", "block.minecraft.banner.stripe_downright.pink": "Banda rosa", "block.minecraft.banner.stripe_downright.purple": "Banda morada", "block.minecraft.banner.stripe_downright.red": "Banda roja", "block.minecraft.banner.stripe_downright.white": "Banda blanca", "block.minecraft.banner.stripe_downright.yellow": "Banda amarilla", "block.minecraft.banner.stripe_left.black": "Palo negro a diestra", "block.minecraft.banner.stripe_left.blue": "<PERSON>lo azul a diestra", "block.minecraft.banner.stripe_left.brown": "<PERSON><PERSON> marr<PERSON> a diestra", "block.minecraft.banner.stripe_left.cyan": "<PERSON>lo turquesa a diestra", "block.minecraft.banner.stripe_left.gray": "<PERSON>lo gris a diestra", "block.minecraft.banner.stripe_left.green": "Palo verde a diestra", "block.minecraft.banner.stripe_left.light_blue": "<PERSON>lo azul cielo a diestra", "block.minecraft.banner.stripe_left.light_gray": "Palo gris claro a diestra", "block.minecraft.banner.stripe_left.lime": "Palo verde limón a diestra", "block.minecraft.banner.stripe_left.magenta": "Palo magenta a diestra", "block.minecraft.banner.stripe_left.orange": "<PERSON><PERSON> naranja a diestra", "block.minecraft.banner.stripe_left.pink": "<PERSON>lo rosa a diestra", "block.minecraft.banner.stripe_left.purple": "Palo morado a diestra", "block.minecraft.banner.stripe_left.red": "<PERSON>lo rojo a diestra", "block.minecraft.banner.stripe_left.white": "Palo blanco a diestra", "block.minecraft.banner.stripe_left.yellow": "<PERSON><PERSON> amarillo a diestra", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON><PERSON> negra", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON><PERSON> tur<PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON> gris", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON> verde", "block.minecraft.banner.stripe_middle.light_blue": "Franja azul cielo", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.banner.stripe_middle.lime": "Franja verde limón", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON> magenta", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON><PERSON> mora<PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON> blanca", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON> am<PERSON>", "block.minecraft.banner.stripe_right.black": "Palo negro a siniestra", "block.minecraft.banner.stripe_right.blue": "Palo azul a siniestra", "block.minecraft.banner.stripe_right.brown": "<PERSON>lo marrón a siniestra", "block.minecraft.banner.stripe_right.cyan": "Palo turquesa a siniestra", "block.minecraft.banner.stripe_right.gray": "Palo gris a siniestra", "block.minecraft.banner.stripe_right.green": "Palo verde a siniestra", "block.minecraft.banner.stripe_right.light_blue": "Palo azul cielo a siniestra", "block.minecraft.banner.stripe_right.light_gray": "Palo gris claro a siniestra", "block.minecraft.banner.stripe_right.lime": "Palo verde limón a siniestra", "block.minecraft.banner.stripe_right.magenta": "Palo magenta a siniestra", "block.minecraft.banner.stripe_right.orange": "<PERSON>lo naranja a siniestra", "block.minecraft.banner.stripe_right.pink": "Palo rosa a siniestra", "block.minecraft.banner.stripe_right.purple": "Palo morado a siniestra", "block.minecraft.banner.stripe_right.red": "Palo rojo a siniestra", "block.minecraft.banner.stripe_right.white": "Palo blanco a siniestra", "block.minecraft.banner.stripe_right.yellow": "<PERSON><PERSON> amarillo a siniestra", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON> negro", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON> t<PERSON>", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON> gris", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON> verde", "block.minecraft.banner.stripe_top.light_blue": "<PERSON><PERSON> a<PERSON>l cielo", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON> gris claro", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON> verde limón", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON> magenta", "block.minecraft.banner.stripe_top.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON> rosa", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON> morado", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON> rojo", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON> blan<PERSON>", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.black": "Chevrón negro", "block.minecraft.banner.triangle_bottom.blue": "Chevrón azul", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON><PERSON><PERSON> marr<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "Chevrón turquesa", "block.minecraft.banner.triangle_bottom.gray": "Chevrón gris", "block.minecraft.banner.triangle_bottom.green": "Chevrón verde", "block.minecraft.banner.triangle_bottom.light_blue": "Chevrón azul cielo", "block.minecraft.banner.triangle_bottom.light_gray": "Chevrón gris claro", "block.minecraft.banner.triangle_bottom.lime": "Chevrón verde limón", "block.minecraft.banner.triangle_bottom.magenta": "Chevrón magenta", "block.minecraft.banner.triangle_bottom.orange": "Chevrón naranja", "block.minecraft.banner.triangle_bottom.pink": "Chevr<PERSON> rosa", "block.minecraft.banner.triangle_bottom.purple": "Chevrón morado", "block.minecraft.banner.triangle_bottom.red": "Chevrón rojo", "block.minecraft.banner.triangle_bottom.white": "Chevrón blanco", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON><PERSON><PERSON> amarillo", "block.minecraft.banner.triangle_top.black": "Chevrón negro invertido", "block.minecraft.banner.triangle_top.blue": "Chevrón azul invertido", "block.minecraft.banner.triangle_top.brown": "Chevrón marrón invertido", "block.minecraft.banner.triangle_top.cyan": "Chevrón turquesa invertido", "block.minecraft.banner.triangle_top.gray": "Chevrón gris invertido", "block.minecraft.banner.triangle_top.green": "Chevrón verde invertido", "block.minecraft.banner.triangle_top.light_blue": "Chevrón azul cielo invertido", "block.minecraft.banner.triangle_top.light_gray": "Chevrón gris claro invertido", "block.minecraft.banner.triangle_top.lime": "Chevrón verde limón invertido", "block.minecraft.banner.triangle_top.magenta": "Chevrón magenta invertido", "block.minecraft.banner.triangle_top.orange": "Chevrón naranja invertido", "block.minecraft.banner.triangle_top.pink": "Chevrón rosa invertido", "block.minecraft.banner.triangle_top.purple": "Chevrón morado invertido", "block.minecraft.banner.triangle_top.red": "Chevrón rojo invertido", "block.minecraft.banner.triangle_top.white": "Chevrón blanco invertido", "block.minecraft.banner.triangle_top.yellow": "Chevrón amarillo invertido", "block.minecraft.banner.triangles_bottom.black": "Base negra dentada", "block.minecraft.banner.triangles_bottom.blue": "Base azul dentada", "block.minecraft.banner.triangles_bottom.brown": "Base marrón dentada", "block.minecraft.banner.triangles_bottom.cyan": "Campaña turquesa dentada", "block.minecraft.banner.triangles_bottom.gray": "Base gris dentada", "block.minecraft.banner.triangles_bottom.green": "Base verde dentada", "block.minecraft.banner.triangles_bottom.light_blue": "Base azul cielo dentada", "block.minecraft.banner.triangles_bottom.light_gray": "Base gris claro dentada", "block.minecraft.banner.triangles_bottom.lime": "Base verde limón dentada", "block.minecraft.banner.triangles_bottom.magenta": "Base morada dentada", "block.minecraft.banner.triangles_bottom.orange": "Base naranja dentada", "block.minecraft.banner.triangles_bottom.pink": "Base rosa dentada", "block.minecraft.banner.triangles_bottom.purple": "Base morada dentada", "block.minecraft.banner.triangles_bottom.red": "Base roja dentada", "block.minecraft.banner.triangles_bottom.white": "Base blanca dentada", "block.minecraft.banner.triangles_bottom.yellow": "Base amarilla dentada", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON> negro dentado", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON> a<PERSON>l dentado", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON> ma<PERSON> den<PERSON>o", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON> turquesa dentado", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON> gris dentado", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON> verde dentado", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON> azul cielo dentado", "block.minecraft.banner.triangles_top.light_gray": "<PERSON><PERSON> gris claro dentado", "block.minecraft.banner.triangles_top.lime": "<PERSON><PERSON> verde lima dentado", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON> magenta dentado", "block.minecraft.banner.triangles_top.orange": "<PERSON><PERSON> na<PERSON>ja dentado", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON> rosa dentado", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON> morado dentado", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON> rojo dentado", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON> blanco dentado", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON> am<PERSON> den<PERSON>o", "block.minecraft.barrel": "Barril", "block.minecraft.barrier": "Barr<PERSON>", "block.minecraft.basalt": "Basalto", "block.minecraft.beacon": "Faro", "block.minecraft.beacon.primary": "Poder primario", "block.minecraft.beacon.secondary": "Poder secundario", "block.minecraft.bed.no_sleep": "Solo puedes dormir por la noche o durante tormentas eléctricas", "block.minecraft.bed.not_safe": "No puedes dormir ahora: hay monstruos cerca", "block.minecraft.bed.obstructed": "Esta cama está obstruida", "block.minecraft.bed.occupied": "Esta cama está ocupada", "block.minecraft.bed.too_far_away": "No puedes dormir ahora: la cama está muy lejos", "block.minecraft.bedrock": "Piedra base", "block.minecraft.bee_nest": "Colmena <PERSON>", "block.minecraft.beehive": "Apiario de abejas", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bell": "Campana", "block.minecraft.big_dripleaf": "Plantaforma grande", "block.minecraft.big_dripleaf_stem": "Tallo de plantaforma grande", "block.minecraft.birch_button": "Botón de abedul", "block.minecraft.birch_door": "Puer<PERSON> de a<PERSON>ul", "block.minecraft.birch_fence": "Cerca de abedul", "block.minecraft.birch_fence_gate": "Puerta de cerca de abedul", "block.minecraft.birch_hanging_sign": "Cartel colgante de abedul", "block.minecraft.birch_leaves": "Hojas de abedul", "block.minecraft.birch_log": "Tronco de abedul", "block.minecraft.birch_planks": "<PERSON><PERSON>", "block.minecraft.birch_pressure_plate": "Placa de presión de abedul", "block.minecraft.birch_sapling": "Arbolito de abedul", "block.minecraft.birch_sign": "Cartel de abedul", "block.minecraft.birch_slab": "Losa de abedul", "block.minecraft.birch_stairs": "Escaleras de abedul", "block.minecraft.birch_trapdoor": "Escotilla de abedul", "block.minecraft.birch_wall_hanging_sign": "Cartel colgante de abedul en pared", "block.minecraft.birch_wall_sign": "Cartel de abedul en pared", "block.minecraft.birch_wood": "<PERSON><PERSON> de abedul", "block.minecraft.black_banner": "Estandarte negro", "block.minecraft.black_bed": "Cama negra", "block.minecraft.black_candle": "Vela negra", "block.minecraft.black_candle_cake": "Torta con vela negra", "block.minecraft.black_carpet": "Alfombra negra", "block.minecraft.black_concrete": "Concreto negro", "block.minecraft.black_concrete_powder": "Cemento negro", "block.minecraft.black_glazed_terracotta": "Azulejo negro", "block.minecraft.black_shulker_box": "Caja de shulker negra", "block.minecraft.black_stained_glass": "Cristal negro", "block.minecraft.black_stained_glass_pane": "Panel de cristal negro", "block.minecraft.black_terracotta": "Terracota negra", "block.minecraft.black_wool": "<PERSON> negra", "block.minecraft.blackstone": "Piedra negra", "block.minecraft.blackstone_slab": "Losa de piedra negra", "block.minecraft.blackstone_stairs": "Escaleras de piedra negra", "block.minecraft.blackstone_wall": "<PERSON>ro de piedra negra", "block.minecraft.blast_furnace": "Alto horno", "block.minecraft.blue_banner": "Estandarte azul", "block.minecraft.blue_bed": "Cama a<PERSON>l", "block.minecraft.blue_candle": "<PERSON><PERSON> azul", "block.minecraft.blue_candle_cake": "Torta con vela azul", "block.minecraft.blue_carpet": "Alfombra azul", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON> a<PERSON>l", "block.minecraft.blue_concrete_powder": "Cemento azul", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> azul", "block.minecraft.blue_ice": "<PERSON><PERSON>", "block.minecraft.blue_orchid": "Orquídea a<PERSON>l", "block.minecraft.blue_shulker_box": "Caja de shulker azul", "block.minecraft.blue_stained_glass": "Cristal azul", "block.minecraft.blue_stained_glass_pane": "Panel de cristal azul", "block.minecraft.blue_terracotta": "Terracota azul", "block.minecraft.blue_wool": "<PERSON>", "block.minecraft.bone_block": "Bloque de huesos", "block.minecraft.bookshelf": "<PERSON><PERSON><PERSON>", "block.minecraft.brain_coral": "Coral de cerebro", "block.minecraft.brain_coral_block": "Bloque de coral de cerebro", "block.minecraft.brain_coral_fan": "Gorgon<PERSON> de cerebro", "block.minecraft.brain_coral_wall_fan": "<PERSON><PERSON><PERSON> de cerebro en pared", "block.minecraft.brewing_stand": "Soporte para pociones", "block.minecraft.brick_slab": "Losa de ladrillos", "block.minecraft.brick_stairs": "Escaleras de ladrillos", "block.minecraft.brick_wall": "<PERSON><PERSON>", "block.minecraft.bricks": "Ladrillos", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_bed": "<PERSON><PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON>", "block.minecraft.brown_candle_cake": "Torta con vela marrón", "block.minecraft.brown_carpet": "Alfombra marrón", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "Cemento <PERSON>", "block.minecraft.brown_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_mushroom": "<PERSON><PERSON>", "block.minecraft.brown_mushroom_block": "Bloque de hongo marrón", "block.minecraft.brown_shulker_box": "Caja de shulker marr<PERSON>", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass_pane": "Panel de cristal marrón", "block.minecraft.brown_terracotta": "Terracota marrón", "block.minecraft.brown_wool": "<PERSON>", "block.minecraft.bubble_column": "Columna de burbujas", "block.minecraft.bubble_coral": "Coral de burbuja", "block.minecraft.bubble_coral_block": "Bloque de coral de burbuja", "block.minecraft.bubble_coral_fan": "Gorgonia de burbuja", "block.minecraft.bubble_coral_wall_fan": "<PERSON><PERSON><PERSON> de burbuja en pared", "block.minecraft.budding_amethyst": "Brotador de amatista", "block.minecraft.bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cactus": "Cactus", "block.minecraft.cactus_flower": "Flor de cactus", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Calcita", "block.minecraft.calibrated_sculk_sensor": "Sensor de sculk calibrado", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>", "block.minecraft.candle": "Vela", "block.minecraft.candle_cake": "Torta con vela", "block.minecraft.carrots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Mesa de cartografía", "block.minecraft.carved_pumpkin": "<PERSON><PERSON> tallada", "block.minecraft.cauldron": "Calder<PERSON>", "block.minecraft.cave_air": "Aire de cueva", "block.minecraft.cave_vines": "Enredaderas de cueva", "block.minecraft.cave_vines_plant": "Planta de enredaderas de cueva", "block.minecraft.chain": "Cadena", "block.minecraft.chain_command_block": "Bloque de comandos de cadena", "block.minecraft.cherry_button": "Botón de cerezo", "block.minecraft.cherry_door": "<PERSON>uer<PERSON> de cerezo", "block.minecraft.cherry_fence": "Cerca de cerezo", "block.minecraft.cherry_fence_gate": "Puerta de cerca de cerezo", "block.minecraft.cherry_hanging_sign": "Cartel colgante de cerezo", "block.minecraft.cherry_leaves": "Hojas de cerezo", "block.minecraft.cherry_log": "Tronco de cerezo", "block.minecraft.cherry_planks": "<PERSON><PERSON> c<PERSON>", "block.minecraft.cherry_pressure_plate": "Placa de presión de cerezo", "block.minecraft.cherry_sapling": "Arbolito de cerezo", "block.minecraft.cherry_sign": "Cartel de cerezo", "block.minecraft.cherry_slab": "Losa de cerezo", "block.minecraft.cherry_stairs": "Escaleras de cerezo", "block.minecraft.cherry_trapdoor": "Escotilla de cerezo", "block.minecraft.cherry_wall_hanging_sign": "Cartel colgante de cerezo en pared", "block.minecraft.cherry_wall_sign": "Cartel de cerezo en pared", "block.minecraft.cherry_wood": "Le<PERSON> c<PERSON>zo", "block.minecraft.chest": "Baúl", "block.minecraft.chipped_anvil": "<PERSON><PERSON>", "block.minecraft.chiseled_bookshelf": "Estantería cincelada", "block.minecraft.chiseled_copper": "Cobre cincelado", "block.minecraft.chiseled_deepslate": "Pizarra abismal cincelada", "block.minecraft.chiseled_nether_bricks": "Ladrillos del Nether cincelados", "block.minecraft.chiseled_polished_blackstone": "Piedra negra pulida cincelada", "block.minecraft.chiseled_quartz_block": "Bloque de cuarzo cincelado", "block.minecraft.chiseled_red_sandstone": "Arenisca roja cincelada", "block.minecraft.chiseled_resin_bricks": "Ladrillo de Resina Cincelada", "block.minecraft.chiseled_sandstone": "Arenisca cincelada", "block.minecraft.chiseled_stone_bricks": "Ladrillos de piedra cincelados", "block.minecraft.chiseled_tuff": "Toba cincelada", "block.minecraft.chiseled_tuff_bricks": "Ladrillos de toba cincelados", "block.minecraft.chorus_flower": "Flor de chorus", "block.minecraft.chorus_plant": "Chorus", "block.minecraft.clay": "Arcilla", "block.minecraft.closed_eyeblossom": "<PERSON><PERSON> de ojo <PERSON>", "block.minecraft.coal_block": "Bloque de car<PERSON>ón", "block.minecraft.coal_ore": "Mena de <PERSON>ón", "block.minecraft.coarse_dirt": "Tierra infértil", "block.minecraft.cobbled_deepslate": "Pizarra abismal rocosa", "block.minecraft.cobbled_deepslate_slab": "Losa de pizarra abismal rocosa", "block.minecraft.cobbled_deepslate_stairs": "Escaleras de pizarra abismal rocosa", "block.minecraft.cobbled_deepslate_wall": "<PERSON>ro de pizarra abismal rocosa", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "Losa de adoquín", "block.minecraft.cobblestone_stairs": "Escaleras de adoquín", "block.minecraft.cobblestone_wall": "<PERSON><PERSON> ad<PERSON>", "block.minecraft.cobweb": "Telaraña", "block.minecraft.cocoa": "Cacao", "block.minecraft.command_block": "Bloque de comandos", "block.minecraft.comparator": "Comparador de redstone", "block.minecraft.composter": "Compostador", "block.minecraft.conduit": "Canalizador", "block.minecraft.copper_block": "Bloque de cobre", "block.minecraft.copper_bulb": "Lámpara de cobre", "block.minecraft.copper_door": "Puerta de cobre", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "Mena de cobre", "block.minecraft.copper_trapdoor": "Escotilla de cobre", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Ladrillos de pizarra abismal agrietados", "block.minecraft.cracked_deepslate_tiles": "Baldosas de pizarra abismal agrietadas", "block.minecraft.cracked_nether_bricks": "Ladrillos del Nether agrietados", "block.minecraft.cracked_polished_blackstone_bricks": "Ladrillos de piedra negra pulida agrietados", "block.minecraft.cracked_stone_bricks": "Ladrillos de piedra agrietados", "block.minecraft.crafter": "Fabricador", "block.minecraft.crafting_table": "Mesa de crafteo", "block.minecraft.creaking_heart": "Corazón de tronador", "block.minecraft.creeper_head": "C<PERSON>za de creeper", "block.minecraft.creeper_wall_head": "Cabeza de creeper en pared", "block.minecraft.crimson_button": "Bo<PERSON><PERSON>", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence": "Cerca carmesí", "block.minecraft.crimson_fence_gate": "Puerta de cerca carmesí", "block.minecraft.crimson_fungus": "<PERSON><PERSON>", "block.minecraft.crimson_hanging_sign": "Cartel colgante carmesí", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_planks": "<PERSON><PERSON>", "block.minecraft.crimson_pressure_plate": "Placa de presión carmesí", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.crimson_sign": "Cartel carmesí", "block.minecraft.crimson_slab": "Losa car<PERSON>í", "block.minecraft.crimson_stairs": "Escaleras carmesí", "block.minecraft.crimson_stem": "Tallo car<PERSON>", "block.minecraft.crimson_trapdoor": "Escotilla <PERSON>", "block.minecraft.crimson_wall_hanging_sign": "Cartel colgante carmesí en pared", "block.minecraft.crimson_wall_sign": "Cartel carmesí en pared", "block.minecraft.crying_obsidian": "Obs<PERSON>a lloro<PERSON>", "block.minecraft.cut_copper": "Cobre cortado", "block.minecraft.cut_copper_slab": "Losa de cobre cortado", "block.minecraft.cut_copper_stairs": "Escaleras de cobre cortado", "block.minecraft.cut_red_sandstone": "Arenisca roja cortada", "block.minecraft.cut_red_sandstone_slab": "Losa de arenisca roja cortada", "block.minecraft.cut_sandstone": "Arenisca cortada", "block.minecraft.cut_sandstone_slab": "Losa de arenisca cortada", "block.minecraft.cyan_banner": "Estandarte turquesa", "block.minecraft.cyan_bed": "Cama turquesa", "block.minecraft.cyan_candle": "<PERSON>ela turquesa", "block.minecraft.cyan_candle_cake": "Torta con vela turquesa", "block.minecraft.cyan_carpet": "Alfombra turquesa", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON> tur<PERSON>a", "block.minecraft.cyan_concrete_powder": "Cemento turquesa", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON>jo turquesa", "block.minecraft.cyan_shulker_box": "Caja de shulker turquesa", "block.minecraft.cyan_stained_glass": "Cristal turquesa", "block.minecraft.cyan_stained_glass_pane": "Panel de cristal turquesa", "block.minecraft.cyan_terracotta": "Terracota turquesa", "block.minecraft.cyan_wool": "<PERSON> tur<PERSON>", "block.minecraft.damaged_anvil": "<PERSON><PERSON> m<PERSON>", "block.minecraft.dandelion": "<PERSON><PERSON>", "block.minecraft.dark_oak_button": "Botón de roble oscuro", "block.minecraft.dark_oak_door": "<PERSON><PERSON><PERSON> de roble oscuro", "block.minecraft.dark_oak_fence": "Cerca de roble oscuro", "block.minecraft.dark_oak_fence_gate": "Puerta de cerca de roble oscuro", "block.minecraft.dark_oak_hanging_sign": "Cartel colgante de roble oscuro", "block.minecraft.dark_oak_leaves": "Hojas de roble oscuro", "block.minecraft.dark_oak_log": "Tronco de roble oscuro", "block.minecraft.dark_oak_planks": "<PERSON><PERSON> de roble oscuro", "block.minecraft.dark_oak_pressure_plate": "Placa de presión de roble oscuro", "block.minecraft.dark_oak_sapling": "Arbolito de roble oscuro", "block.minecraft.dark_oak_sign": "Cartel de roble oscuro", "block.minecraft.dark_oak_slab": "Losa de roble oscuro", "block.minecraft.dark_oak_stairs": "Escaleras de roble oscuro", "block.minecraft.dark_oak_trapdoor": "Escotilla de roble oscuro", "block.minecraft.dark_oak_wall_hanging_sign": "Cartel colgante de roble oscuro en pared", "block.minecraft.dark_oak_wall_sign": "Cartel de roble oscuro en pared", "block.minecraft.dark_oak_wood": "Leño de roble oscuro", "block.minecraft.dark_prismarine": "Prismarina oscura", "block.minecraft.dark_prismarine_slab": "Losa de prismarina oscura", "block.minecraft.dark_prismarine_stairs": "Escaleras de prismarina oscura", "block.minecraft.daylight_detector": "Sensor de luz solar", "block.minecraft.dead_brain_coral": "Coral de cerebro muerto", "block.minecraft.dead_brain_coral_block": "Bloque de coral de cerebro muerto", "block.minecraft.dead_brain_coral_fan": "Gorgonia de cerebro muerta", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON> de cerebro muerta en pared", "block.minecraft.dead_bubble_coral": "Coral de burbuja muerto", "block.minecraft.dead_bubble_coral_block": "Bloque de coral de burbuja muerto", "block.minecraft.dead_bubble_coral_fan": "Gorgonia de burbuja muerta", "block.minecraft.dead_bubble_coral_wall_fan": "Gorgonia de burbuja muerta en pared", "block.minecraft.dead_bush": "<PERSON><PERSON><PERSON><PERSON> muerto", "block.minecraft.dead_fire_coral": "Coral de fuego muerto", "block.minecraft.dead_fire_coral_block": "Bloque de coral de fuego muerto", "block.minecraft.dead_fire_coral_fan": "Gorgonia de fuego muerta", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON><PERSON> de fuego muerta en pared", "block.minecraft.dead_horn_coral": "Coral de cuerno muerto", "block.minecraft.dead_horn_coral_block": "Bloque de coral de cuerno muerto", "block.minecraft.dead_horn_coral_fan": "Gorgonia de cuerno muerta", "block.minecraft.dead_horn_coral_wall_fan": "Gorgonia de cuerno muerta en pared", "block.minecraft.dead_tube_coral": "Coral de tubo muerto", "block.minecraft.dead_tube_coral_block": "Bloque de coral de tubo muerto", "block.minecraft.dead_tube_coral_fan": "Gorgonia de tubo muerta", "block.minecraft.dead_tube_coral_wall_fan": "Gorgonia de tubo muerta en pared", "block.minecraft.decorated_pot": "Vasija decorada", "block.minecraft.deepslate": "Pi<PERSON>ra a<PERSON>", "block.minecraft.deepslate_brick_slab": "Losa de ladrillos de pizarra abismal", "block.minecraft.deepslate_brick_stairs": "Escaleras de ladrillos de pizarra abismal", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON> de ladrillos de pizarra abismal", "block.minecraft.deepslate_bricks": "Ladrillos de pizarra profunda", "block.minecraft.deepslate_coal_ore": "Mena de carbón de pizarra abismal", "block.minecraft.deepslate_copper_ore": "Mena de cobre de pizarra abismal", "block.minecraft.deepslate_diamond_ore": "Mena de diamante de pizarra abismal", "block.minecraft.deepslate_emerald_ore": "Mena de esmeralda de pizarra abismal", "block.minecraft.deepslate_gold_ore": "Mena de oro de pizarra abismal", "block.minecraft.deepslate_iron_ore": "Mena de hierro de pizarra abismal", "block.minecraft.deepslate_lapis_ore": "Mena de lapislázuli de pizarra abismal", "block.minecraft.deepslate_redstone_ore": "Mena de redstone de pizarra abismal", "block.minecraft.deepslate_tile_slab": "Losa de baldosas de pizarra abismal", "block.minecraft.deepslate_tile_stairs": "Escaleras de baldosas de pizarra abismal", "block.minecraft.deepslate_tile_wall": "<PERSON><PERSON> de pizarra abismal", "block.minecraft.deepslate_tiles": "Baldosas de pizarra abismal", "block.minecraft.detector_rail": "Carril detector", "block.minecraft.diamond_block": "Bloque de diamante", "block.minecraft.diamond_ore": "Mena de diamante", "block.minecraft.diorite": "Di<PERSON>ta", "block.minecraft.diorite_slab": "Losa de diorita", "block.minecraft.diorite_stairs": "Escaleras de diorita", "block.minecraft.diorite_wall": "<PERSON>ro <PERSON> di<PERSON>", "block.minecraft.dirt": "Tierra", "block.minecraft.dirt_path": "Camino de tierra", "block.minecraft.dispenser": "Dispensador", "block.minecraft.dragon_egg": "Huevo de dragón", "block.minecraft.dragon_head": "Cabeza de dragón", "block.minecraft.dragon_wall_head": "Cabeza de dragón en pared", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Bloque de algas secas", "block.minecraft.dripstone_block": "Bloque de espeleotema", "block.minecraft.dropper": "Soltador", "block.minecraft.emerald_block": "Bloque de esmeralda", "block.minecraft.emerald_ore": "Mena de esmeralda", "block.minecraft.enchanting_table": "Mesa de encantamientos", "block.minecraft.end_gateway": "Acceso al End", "block.minecraft.end_portal": "Marco <PERSON> al End", "block.minecraft.end_portal_frame": "Marco <PERSON> portal del End", "block.minecraft.end_rod": "Vara del End", "block.minecraft.end_stone": "Piedra del End", "block.minecraft.end_stone_brick_slab": "Losa de ladrillos de piedra del End", "block.minecraft.end_stone_brick_stairs": "Escaleras de ladrillos de piedra del End", "block.minecraft.end_stone_brick_wall": "Muro de ladrillos de piedra del End", "block.minecraft.end_stone_bricks": "Ladrillos de piedra del End", "block.minecraft.ender_chest": "<PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Cobre cincelado expuesto", "block.minecraft.exposed_copper": "Cobre expuesto", "block.minecraft.exposed_copper_bulb": "Lámpara de cobre expuesta", "block.minecraft.exposed_copper_door": "Puerta de cobre expuesta", "block.minecraft.exposed_copper_grate": "Rejilla de cobre expuesta", "block.minecraft.exposed_copper_trapdoor": "Escotilla de cobre expuesta", "block.minecraft.exposed_cut_copper": "Cobre cortado expuesto", "block.minecraft.exposed_cut_copper_slab": "Losa de cobre cortado expuesto", "block.minecraft.exposed_cut_copper_stairs": "Escaleras de cobre cortado", "block.minecraft.farmland": "Tierra de cultivos", "block.minecraft.fern": "<PERSON><PERSON><PERSON>", "block.minecraft.fire": "Fuego", "block.minecraft.fire_coral": "Coral de fuego", "block.minecraft.fire_coral_block": "Bloque de coral de fuego", "block.minecraft.fire_coral_fan": "Gorgonia de fuego", "block.minecraft.fire_coral_wall_fan": "<PERSON><PERSON><PERSON> de fuego en pared", "block.minecraft.firefly_bush": "Arbusto de luci<PERSON>gas", "block.minecraft.fletching_table": "Mesa de flechas", "block.minecraft.flower_pot": "Mace<PERSON>", "block.minecraft.flowering_azalea": "Azalea floreciente", "block.minecraft.flowering_azalea_leaves": "Hojas de azalea florecida", "block.minecraft.frogspawn": "<PERSON><PERSON><PERSON>a", "block.minecraft.frosted_ice": "Escarcha", "block.minecraft.furnace": "<PERSON><PERSON>", "block.minecraft.gilded_blackstone": "Piedra negra áurea", "block.minecraft.glass": "Cristal", "block.minecraft.glass_pane": "Panel de cristal", "block.minecraft.glow_lichen": "Liquen luminoso", "block.minecraft.glowstone": "Piedra luminosa", "block.minecraft.gold_block": "Bloque de oro", "block.minecraft.gold_ore": "Mena de oro", "block.minecraft.granite": "<PERSON><PERSON>", "block.minecraft.granite_slab": "Losa de granito", "block.minecraft.granite_stairs": "Escaleras de granito", "block.minecraft.granite_wall": "<PERSON><PERSON>", "block.minecraft.grass": "<PERSON><PERSON><PERSON>", "block.minecraft.grass_block": "Bloque de hierba", "block.minecraft.gravel": "Gravilla", "block.minecraft.gray_banner": "Estandarte gris", "block.minecraft.gray_bed": "<PERSON>a gris", "block.minecraft.gray_candle": "<PERSON><PERSON> gris", "block.minecraft.gray_candle_cake": "Torta con vela gris", "block.minecraft.gray_carpet": "Alfombra gris", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON> gris", "block.minecraft.gray_concrete_powder": "Cemento gris", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> gris", "block.minecraft.gray_shulker_box": "Caja de shulker gris", "block.minecraft.gray_stained_glass": "Cristal gris", "block.minecraft.gray_stained_glass_pane": "Panel de cristal gris", "block.minecraft.gray_terracotta": "Terracota gris", "block.minecraft.gray_wool": "<PERSON> gris", "block.minecraft.green_banner": "Estandarte verde", "block.minecraft.green_bed": "Cama verde", "block.minecraft.green_candle": "Vela verde", "block.minecraft.green_candle_cake": "Torta con vela verde", "block.minecraft.green_carpet": "Alfombra verde", "block.minecraft.green_concrete": "<PERSON><PERSON>to verde", "block.minecraft.green_concrete_powder": "Cemento verde", "block.minecraft.green_glazed_terracotta": "A<PERSON>lejo verde", "block.minecraft.green_shulker_box": "Caja de shulker verde", "block.minecraft.green_stained_glass": "Cristal verde", "block.minecraft.green_stained_glass_pane": "Panel de cristal verde", "block.minecraft.green_terracotta": "Terracota verde", "block.minecraft.green_wool": "Lana verde", "block.minecraft.grindstone": "A<PERSON>lad<PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON> co<PERSON>", "block.minecraft.hay_block": "<PERSON>a de he<PERSON>", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON><PERSON> pesado", "block.minecraft.heavy_weighted_pressure_plate": "Placa de presión para peso elevado", "block.minecraft.honey_block": "Bloque de miel", "block.minecraft.honeycomb_block": "Bloque de panal", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "Coral de cuerno", "block.minecraft.horn_coral_block": "Bloque de coral de cuerno", "block.minecraft.horn_coral_fan": "Gorgonia de cuerno", "block.minecraft.horn_coral_wall_fan": "<PERSON><PERSON><PERSON> de cuerno en pared", "block.minecraft.ice": "<PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "Ladrillos de piedra cincelados infestados", "block.minecraft.infested_cobblestone": "Adoquín infestado", "block.minecraft.infested_cracked_stone_bricks": "Ladrillos de piedra agrietados infestados", "block.minecraft.infested_deepslate": "Pizarra abismal infestada", "block.minecraft.infested_mossy_stone_bricks": "Ladrillos de piedra musgosa infestados", "block.minecraft.infested_stone": "Piedra infestada", "block.minecraft.infested_stone_bricks": "Ladrillos de piedra infestados", "block.minecraft.iron_bars": "<PERSON><PERSON> de <PERSON>", "block.minecraft.iron_block": "Bloque de hi<PERSON>ro", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.iron_trapdoor": "Escotilla de hi<PERSON>ro", "block.minecraft.jack_o_lantern": "<PERSON><PERSON>", "block.minecraft.jigsaw": "Bloque r<PERSON>", "block.minecraft.jukebox": "Tocadiscos", "block.minecraft.jungle_button": "Botón de j<PERSON>", "block.minecraft.jungle_door": "<PERSON><PERSON><PERSON>", "block.minecraft.jungle_fence": "Cerca de jungla", "block.minecraft.jungle_fence_gate": "Puerta de cerca de jungla", "block.minecraft.jungle_hanging_sign": "Cartel colgante de jungla", "block.minecraft.jungle_leaves": "Hojas de jungla", "block.minecraft.jungle_log": "Tronco de jungla", "block.minecraft.jungle_planks": "<PERSON><PERSON>", "block.minecraft.jungle_pressure_plate": "Placa de presión de jungla", "block.minecraft.jungle_sapling": "Arbolito <PERSON>", "block.minecraft.jungle_sign": "Cartel de jungla", "block.minecraft.jungle_slab": "<PERSON><PERSON>", "block.minecraft.jungle_stairs": "Escaleras de jungla", "block.minecraft.jungle_trapdoor": "Escotilla de jungla", "block.minecraft.jungle_wall_hanging_sign": "Cartel colgante de jungla en pared", "block.minecraft.jungle_wall_sign": "Cartel de jungla en pared", "block.minecraft.jungle_wood": "<PERSON><PERSON>", "block.minecraft.kelp": "Alga", "block.minecraft.kelp_plant": "Tallo de alga", "block.minecraft.ladder": "Escalera de mano", "block.minecraft.lantern": "Farol", "block.minecraft.lapis_block": "Bloque de Lapislázuli", "block.minecraft.lapis_ore": "Mena de lapislázuli", "block.minecraft.large_amethyst_bud": "Brote de amatista grande", "block.minecraft.large_fern": "Helecho grande", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Caldero con lava", "block.minecraft.leaf_litter": "Hojas caídas", "block.minecraft.lectern": "Atril", "block.minecraft.lever": "Palanca", "block.minecraft.light": "Luz", "block.minecraft.light_blue_banner": "Estandarte azul cielo", "block.minecraft.light_blue_bed": "Cama azul cielo", "block.minecraft.light_blue_candle": "<PERSON>ela azul cielo", "block.minecraft.light_blue_candle_cake": "Torta con vela azul cielo", "block.minecraft.light_blue_carpet": "Alfombra azul cielo", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON> azul cielo", "block.minecraft.light_blue_concrete_powder": "Cemento azul cielo", "block.minecraft.light_blue_glazed_terracotta": "<PERSON><PERSON><PERSON>jo azul cielo", "block.minecraft.light_blue_shulker_box": "Caja de shulker azul cielo", "block.minecraft.light_blue_stained_glass": "Cristal azul cielo", "block.minecraft.light_blue_stained_glass_pane": "Panel de cristal azul cielo", "block.minecraft.light_blue_terracotta": "Terracota azul cielo", "block.minecraft.light_blue_wool": "Lana azul cielo", "block.minecraft.light_gray_banner": "Estandarte gris claro", "block.minecraft.light_gray_bed": "<PERSON>a gris claro", "block.minecraft.light_gray_candle": "<PERSON><PERSON> gris claro", "block.minecraft.light_gray_candle_cake": "<PERSON>ta con vela gris claro", "block.minecraft.light_gray_carpet": "Alfombra gris claro", "block.minecraft.light_gray_concrete": "<PERSON><PERSON><PERSON> gris claro", "block.minecraft.light_gray_concrete_powder": "Cemento gris claro", "block.minecraft.light_gray_glazed_terracotta": "<PERSON><PERSON><PERSON>jo gris claro", "block.minecraft.light_gray_shulker_box": "Caja de shulker gris claro", "block.minecraft.light_gray_stained_glass": "Crist<PERSON> gris claro", "block.minecraft.light_gray_stained_glass_pane": "Panel de cristal gris claro", "block.minecraft.light_gray_terracotta": "Terracota gris claro", "block.minecraft.light_gray_wool": "<PERSON> gris claro", "block.minecraft.light_weighted_pressure_plate": "Placa de presión para peso liviano", "block.minecraft.lightning_rod": "Pararrayos", "block.minecraft.lilac": "<PERSON>", "block.minecraft.lily_of_the_valley": "Lirio del valle", "block.minecraft.lily_pad": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lime_banner": "Estandarte verde limón", "block.minecraft.lime_bed": "Cama verde limón", "block.minecraft.lime_candle": "Vela verde limón", "block.minecraft.lime_candle_cake": "Torta con vela verde limón", "block.minecraft.lime_carpet": "Alfombra verde limón", "block.minecraft.lime_concrete": "Concreto verde limón", "block.minecraft.lime_concrete_powder": "Cemento verde limón", "block.minecraft.lime_glazed_terracotta": "Azulejo verde limón", "block.minecraft.lime_shulker_box": "Caja de shulker verde limón", "block.minecraft.lime_stained_glass": "Cristal verde limón", "block.minecraft.lime_stained_glass_pane": "Panel de cristal verde limón", "block.minecraft.lime_terracotta": "Terracota verde limón", "block.minecraft.lime_wool": "Lana verde limón", "block.minecraft.lodestone": "Ma<PERSON><PERSON><PERSON>", "block.minecraft.loom": "Telar", "block.minecraft.magenta_banner": "Estandarte magenta", "block.minecraft.magenta_bed": "Cama magenta", "block.minecraft.magenta_candle": "Vela magenta", "block.minecraft.magenta_candle_cake": "Torta con vela magenta", "block.minecraft.magenta_carpet": "Alfombra magenta", "block.minecraft.magenta_concrete": "Concreto magenta", "block.minecraft.magenta_concrete_powder": "Cemento magenta", "block.minecraft.magenta_glazed_terracotta": "Azulejo magenta", "block.minecraft.magenta_shulker_box": "Caja de shulker magenta", "block.minecraft.magenta_stained_glass": "Cristal magenta", "block.minecraft.magenta_stained_glass_pane": "Panel de cristal magenta", "block.minecraft.magenta_terracotta": "Terracota magenta", "block.minecraft.magenta_wool": "Lana magenta", "block.minecraft.magma_block": "Bloque de magma", "block.minecraft.mangrove_button": "Botón de mangle", "block.minecraft.mangrove_door": "<PERSON>uerta de mangle", "block.minecraft.mangrove_fence": "Cerca de mangle", "block.minecraft.mangrove_fence_gate": "Puerta de cerca de mangle", "block.minecraft.mangrove_hanging_sign": "Cartel colgante de mangle", "block.minecraft.mangrove_leaves": "Hojas de mangle", "block.minecraft.mangrove_log": "Tronco de mangle", "block.minecraft.mangrove_planks": "<PERSON><PERSON> de mangle", "block.minecraft.mangrove_pressure_plate": "Placa de presión de mangle", "block.minecraft.mangrove_propagule": "Propágulo de mangle", "block.minecraft.mangrove_roots": "<PERSON><PERSON><PERSON> de mangle", "block.minecraft.mangrove_sign": "Cartel de mangle", "block.minecraft.mangrove_slab": "Losa de mangle", "block.minecraft.mangrove_stairs": "Escaleras de mangle", "block.minecraft.mangrove_trapdoor": "Escotilla de mangle", "block.minecraft.mangrove_wall_hanging_sign": "Cartel de mangle colgante en pared", "block.minecraft.mangrove_wall_sign": "Cartel de mangle en pared", "block.minecraft.mangrove_wood": "Leño de mangle", "block.minecraft.medium_amethyst_bud": "Brote de amatista mediano", "block.minecraft.melon": "Patilla", "block.minecraft.melon_stem": "Tallo de patilla", "block.minecraft.moss_block": "Bloque de musgo", "block.minecraft.moss_carpet": "Alfombra de musgo", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "block.minecraft.mossy_cobblestone_slab": "Losa de ado<PERSON><PERSON><PERSON> mus<PERSON>o", "block.minecraft.mossy_cobblestone_stairs": "Escaleras de adoquín musgoso", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> <PERSON> ad<PERSON><PERSON><PERSON> mus<PERSON>o", "block.minecraft.mossy_stone_brick_slab": "Losa de ladrillos de piedra musgosa", "block.minecraft.mossy_stone_brick_stairs": "Escaleras de ladrillos de piedra musgosa", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON> de ladrillos de piedra musgosa", "block.minecraft.mossy_stone_bricks": "Ladrillos de piedra musgosa", "block.minecraft.moving_piston": "Pistón en movimiento", "block.minecraft.mud": "Barro", "block.minecraft.mud_brick_slab": "Losa de ladrillos de adobe", "block.minecraft.mud_brick_stairs": "Escaleras de ladrillos de adobe", "block.minecraft.mud_brick_wall": "<PERSON><PERSON> de ladrillos de adobe", "block.minecraft.mud_bricks": "Ladrillos de adobe", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON><PERSON> de mangle embarradas", "block.minecraft.mushroom_stem": "Tallo de hongo", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Cerca de ladrillos del Nether", "block.minecraft.nether_brick_slab": "Losa de ladrillos del Nether", "block.minecraft.nether_brick_stairs": "Escaleras de ladrillos del Nether", "block.minecraft.nether_brick_wall": "<PERSON><PERSON> de ladrillos del Nether", "block.minecraft.nether_bricks": "Ladrillos del Nether", "block.minecraft.nether_gold_ore": "Mena de oro del Nether", "block.minecraft.nether_portal": "Portal al Nether", "block.minecraft.nether_quartz_ore": "Mena de cuarzo del Nether", "block.minecraft.nether_sprouts": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_wart": "Verruga del Nether", "block.minecraft.nether_wart_block": "Bloque de verrugas del Nether", "block.minecraft.netherite_block": "Bloque de netherita", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Bloque musical", "block.minecraft.oak_button": "Botón de roble", "block.minecraft.oak_door": "<PERSON><PERSON><PERSON> r<PERSON>", "block.minecraft.oak_fence": "Cerca de roble", "block.minecraft.oak_fence_gate": "Puerta de cerca de roble", "block.minecraft.oak_hanging_sign": "Cartel de roble colgante", "block.minecraft.oak_leaves": "Hojas de roble", "block.minecraft.oak_log": "Tronco de roble", "block.minecraft.oak_planks": "<PERSON><PERSON>", "block.minecraft.oak_pressure_plate": "Placa de presión de roble", "block.minecraft.oak_sapling": "Arbolito de roble", "block.minecraft.oak_sign": "Cartel de roble", "block.minecraft.oak_slab": "Losa de roble", "block.minecraft.oak_stairs": "Escaleras de roble", "block.minecraft.oak_trapdoor": "Escotilla de roble", "block.minecraft.oak_wall_hanging_sign": "Cartel de roble colgante en pared", "block.minecraft.oak_wall_sign": "Cartel de roble en pared", "block.minecraft.oak_wood": "<PERSON><PERSON> r<PERSON>", "block.minecraft.observer": "Observador", "block.minecraft.obsidian": "Obsidiana", "block.minecraft.ochre_froglight": "<PERSON><PERSON><PERSON> ocre", "block.minecraft.ominous_banner": "Estandarte de mal presagio", "block.minecraft.open_eyeblossom": "Flor de ojo Abierta", "block.minecraft.orange_banner": "Estandarte naranja", "block.minecraft.orange_bed": "<PERSON><PERSON> naranja", "block.minecraft.orange_candle": "<PERSON><PERSON> naranja", "block.minecraft.orange_candle_cake": "Torta con vela naranja", "block.minecraft.orange_carpet": "Alfombra naranja", "block.minecraft.orange_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "Cemento naranja", "block.minecraft.orange_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_shulker_box": "<PERSON>aja de shulker naranja", "block.minecraft.orange_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "Panel de cristal naranja", "block.minecraft.orange_terracotta": "Terracota naranja", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON> na<PERSON>ja", "block.minecraft.orange_wool": "<PERSON>", "block.minecraft.oxeye_daisy": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "Cobre cincelado oxidado", "block.minecraft.oxidized_copper": "Cobre oxidado", "block.minecraft.oxidized_copper_bulb": "Lámpara de cobre oxidada", "block.minecraft.oxidized_copper_door": "Puerta de cobre oxidada", "block.minecraft.oxidized_copper_grate": "Rejilla de cobre oxidada", "block.minecraft.oxidized_copper_trapdoor": "Escotilla de cobre oxidada", "block.minecraft.oxidized_cut_copper": "Cobre cortado oxidado", "block.minecraft.oxidized_cut_copper_slab": "Losa de cobre cortado oxidado", "block.minecraft.oxidized_cut_copper_stairs": "Escaleras de cobre cortado oxidado", "block.minecraft.packed_ice": "<PERSON><PERSON> comprimido", "block.minecraft.packed_mud": "Adobe", "block.minecraft.pale_hanging_moss": "Musgo p<PERSON><PERSON>o co<PERSON>", "block.minecraft.pale_moss_block": "Bloque de Musgo Pálido", "block.minecraft.pale_moss_carpet": "Alfombra de Musgo Pálido", "block.minecraft.pale_oak_button": "Botón de Madera Pálida", "block.minecraft.pale_oak_door": "Puerta de Madera Pálida", "block.minecraft.pale_oak_fence": "Valla de Madera Pálida", "block.minecraft.pale_oak_fence_gate": "Puerta de Valla de Madera Pálida", "block.minecraft.pale_oak_hanging_sign": "Cartel Colgante de Madera Pálida", "block.minecraft.pale_oak_leaves": "Hojas Pálidas", "block.minecraft.pale_oak_log": "Tronco Pálido", "block.minecraft.pale_oak_planks": "Tablones de Madera Pálida", "block.minecraft.pale_oak_pressure_plate": "Placa de presión de Madera Pálida", "block.minecraft.pale_oak_sapling": "Re<PERSON><PERSON> de Madera Pálida", "block.minecraft.pale_oak_sign": "Cartel de Madera Pálida", "block.minecraft.pale_oak_slab": "Losa de Madera Pálida", "block.minecraft.pale_oak_stairs": "Escaleras de Madera Pálida", "block.minecraft.pale_oak_trapdoor": "Escotilla de Madera Pálida", "block.minecraft.pale_oak_wall_hanging_sign": "Cartel Colgante de Madera Pálida para Paredes", "block.minecraft.pale_oak_wall_sign": "Cartel de Madera Pálida para Paredes", "block.minecraft.pale_oak_wood": "<PERSON><PERSON>", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON>", "block.minecraft.peony": "Peonía", "block.minecraft.petrified_oak_slab": "Losa de roble petrificada", "block.minecraft.piglin_head": "Cabeza de piglin", "block.minecraft.piglin_wall_head": "Cabeza de piglin en pared", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_bed": "<PERSON><PERSON> rosa", "block.minecraft.pink_candle": "<PERSON><PERSON> rosa", "block.minecraft.pink_candle_cake": "Torta con vela rosa", "block.minecraft.pink_carpet": "Alfombra rosa", "block.minecraft.pink_concrete": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_concrete_powder": "Cemento rosa", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_petals": "<PERSON><PERSON><PERSON><PERSON> rosas", "block.minecraft.pink_shulker_box": "Caja de shulker rosa", "block.minecraft.pink_stained_glass": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_stained_glass_pane": "Panel de cristal rosa", "block.minecraft.pink_terracotta": "Terracota rosa", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON> rosa", "block.minecraft.pink_wool": "<PERSON> rosa", "block.minecraft.piston": "Pistón", "block.minecraft.piston_head": "Cabeza de pistón", "block.minecraft.pitcher_crop": "Cultivo de planta odre", "block.minecraft.pitcher_plant": "Planta odre", "block.minecraft.player_head": "Cabeza de jugador", "block.minecraft.player_head.named": "Cabeza de %s", "block.minecraft.player_wall_head": "Cabeza de jugador en pared", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "Espeleotema puntiaguda", "block.minecraft.polished_andesite": "<PERSON><PERSON> pulida", "block.minecraft.polished_andesite_slab": "Losa de andesita pulida", "block.minecraft.polished_andesite_stairs": "Escaleras de andesita pulida", "block.minecraft.polished_basalt": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone": "Piedra negra pulida", "block.minecraft.polished_blackstone_brick_slab": "Losa de ladrillos de piedra negra pulida", "block.minecraft.polished_blackstone_brick_stairs": "Escaleras de ladrillos de piedra negra pulida", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON> de ladrillos de piedra negra pulida", "block.minecraft.polished_blackstone_bricks": "Ladrillos de piedra negra pulida", "block.minecraft.polished_blackstone_button": "Botón de piedra negra pulida", "block.minecraft.polished_blackstone_pressure_plate": "Placa de presión de piedra negra pulida", "block.minecraft.polished_blackstone_slab": "Losa de piedra negra pulida", "block.minecraft.polished_blackstone_stairs": "Escaleras de piedra negra pulida", "block.minecraft.polished_blackstone_wall": "<PERSON>ro de piedra negra pulida", "block.minecraft.polished_deepslate": "Pi<PERSON>ra abismal pulida", "block.minecraft.polished_deepslate_slab": "Losa de pizarra abismal pulida", "block.minecraft.polished_deepslate_stairs": "Escaleras de pizarra abismal pulida", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON> de pizarra abismal pulida", "block.minecraft.polished_diorite": "<PERSON><PERSON>ta pulida", "block.minecraft.polished_diorite_slab": "Losa de diorita pulida", "block.minecraft.polished_diorite_stairs": "Escaleras de diorita pulida", "block.minecraft.polished_granite": "<PERSON><PERSON>", "block.minecraft.polished_granite_slab": "Losa de granito pulido", "block.minecraft.polished_granite_stairs": "Escaleras de granito pulido", "block.minecraft.polished_tuff": "<PERSON><PERSON> pulida", "block.minecraft.polished_tuff_slab": "Losa de toba pulida", "block.minecraft.polished_tuff_stairs": "Escaleras de toba pulida", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON> de toba pulida", "block.minecraft.poppy": "Amapola", "block.minecraft.potatoes": "<PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Maceta con arbolito de acacia", "block.minecraft.potted_allium": "Maceta con allium", "block.minecraft.potted_azalea_bush": "Maceta con azalea", "block.minecraft.potted_azure_bluet": "Maceta con rubi<PERSON>", "block.minecraft.potted_bamboo": "Maceta con bambú", "block.minecraft.potted_birch_sapling": "Maceta con arbolito de abedul", "block.minecraft.potted_blue_orchid": "Maceta con orquídea azul", "block.minecraft.potted_brown_mushroom": "Maceta con hongo marrón", "block.minecraft.potted_cactus": "Maceta con cactus", "block.minecraft.potted_cherry_sapling": "Maceta con arbolito de cerezo", "block.minecraft.potted_closed_eyeblossom": "<PERSON><PERSON> con flor ojo cerrado", "block.minecraft.potted_cornflower": "Maceta con aciano", "block.minecraft.potted_crimson_fungus": "Maceta con hongo carmesí", "block.minecraft.potted_crimson_roots": "Maceta con ra<PERSON>ces carmesí", "block.minecraft.potted_dandelion": "Maceta con diente de león", "block.minecraft.potted_dark_oak_sapling": "Maceta con arbolito de roble oscuro", "block.minecraft.potted_dead_bush": "Maceta con arbusto muerto", "block.minecraft.potted_fern": "Maceta con helecho", "block.minecraft.potted_flowering_azalea_bush": "Maceta con azalea florecida", "block.minecraft.potted_jungle_sapling": "Maceta con arbolito de jungla", "block.minecraft.potted_lily_of_the_valley": "Maceta con lirio del valle", "block.minecraft.potted_mangrove_propagule": "Maceta con propágulo de mangle", "block.minecraft.potted_oak_sapling": "Maceta con arbolito de roble", "block.minecraft.potted_open_eyeblossom": "<PERSON><PERSON> con flor ojo abierto", "block.minecraft.potted_orange_tulip": "Maceta con tulipán naranja", "block.minecraft.potted_oxeye_daisy": "<PERSON>ta con margarita", "block.minecraft.potted_pale_oak_sapling": "Maceta con retoño de roble pálido\n", "block.minecraft.potted_pink_tulip": "Maceta con tulipán rosa", "block.minecraft.potted_poppy": "Maceta con amapola", "block.minecraft.potted_red_mushroom": "Maceta con hongo rojo", "block.minecraft.potted_red_tulip": "Maceta con tulipán rojo", "block.minecraft.potted_spruce_sapling": "Maceta con arbolito de pino", "block.minecraft.potted_torchflower": "Maceta con florantorcha", "block.minecraft.potted_warped_fungus": "Maceta con hongo distorsionado", "block.minecraft.potted_warped_roots": "Maceta con raíces distorsionadas", "block.minecraft.potted_white_tulip": "Maceta con tulipán blanco", "block.minecraft.potted_wither_rose": "Maceta con rosa del Wither", "block.minecraft.powder_snow": "Nieve polvo", "block.minecraft.powder_snow_cauldron": "Caldero de nieve polvo", "block.minecraft.powered_rail": "Carril propulsor", "block.minecraft.prismarine": "Prismarina", "block.minecraft.prismarine_brick_slab": "Losa de ladrillos de prismarina", "block.minecraft.prismarine_brick_stairs": "Escaleras de ladrillos de prismarina", "block.minecraft.prismarine_bricks": "Ladrillos de prismarina", "block.minecraft.prismarine_slab": "Losa de prismarina", "block.minecraft.prismarine_stairs": "Escaleras de prismarina", "block.minecraft.prismarine_wall": "<PERSON>ro de prismarina", "block.minecraft.pumpkin": "<PERSON><PERSON>", "block.minecraft.pumpkin_stem": "<PERSON><PERSON>", "block.minecraft.purple_banner": "Estandarte morado", "block.minecraft.purple_bed": "Cama morada", "block.minecraft.purple_candle": "<PERSON><PERSON> morada", "block.minecraft.purple_candle_cake": "Torta con vela morada", "block.minecraft.purple_carpet": "Alfombra morada", "block.minecraft.purple_concrete": "<PERSON><PERSON><PERSON> morado", "block.minecraft.purple_concrete_powder": "Cemento morado", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> morado", "block.minecraft.purple_shulker_box": "Caja de shulker morada", "block.minecraft.purple_stained_glass": "<PERSON><PERSON><PERSON> morado", "block.minecraft.purple_stained_glass_pane": "Panel de cristal morado", "block.minecraft.purple_terracotta": "Terracota morada", "block.minecraft.purple_wool": "<PERSON> mora<PERSON>", "block.minecraft.purpur_block": "Purpur", "block.minecraft.purpur_pillar": "<PERSON><PERSON> de purpur", "block.minecraft.purpur_slab": "Losa de purpur", "block.minecraft.purpur_stairs": "Escaleras de purpur", "block.minecraft.quartz_block": "Bloque de cu<PERSON>zo", "block.minecraft.quartz_bricks": "Ladrillos de cuarzo", "block.minecraft.quartz_pillar": "<PERSON><PERSON>", "block.minecraft.quartz_slab": "Losa de cuarzo", "block.minecraft.quartz_stairs": "Escaleras de cuarzo", "block.minecraft.rail": "Carril", "block.minecraft.raw_copper_block": "Bloque de cobre en bruto", "block.minecraft.raw_gold_block": "Bloque de oro en bruto", "block.minecraft.raw_iron_block": "Bloque de hierro en bruto", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.red_bed": "Cama roja", "block.minecraft.red_candle": "<PERSON><PERSON> roja", "block.minecraft.red_candle_cake": "Torta con vela roja", "block.minecraft.red_carpet": "Alfombra roja", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.red_concrete_powder": "Cemento rojo", "block.minecraft.red_glazed_terracotta": "A<PERSON><PERSON>jo rojo", "block.minecraft.red_mushroom": "<PERSON><PERSON> rojo", "block.minecraft.red_mushroom_block": "Bloque de hongo rojo", "block.minecraft.red_nether_brick_slab": "Losa de ladrillos del Nether rojos", "block.minecraft.red_nether_brick_stairs": "Escaleras de ladrillos del Nether rojos", "block.minecraft.red_nether_brick_wall": "<PERSON>ro de ladrillos del Nether rojos", "block.minecraft.red_nether_bricks": "Ladrillos del Nether rojos", "block.minecraft.red_sand": "Arena roja", "block.minecraft.red_sandstone": "Arenisca roja", "block.minecraft.red_sandstone_slab": "Losa de arenisca roja", "block.minecraft.red_sandstone_stairs": "Escaleras de arenisca roja", "block.minecraft.red_sandstone_wall": "Muro de arenisca roja", "block.minecraft.red_shulker_box": "Caja de shulker roja", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.red_stained_glass_pane": "Panel de cristal rojo", "block.minecraft.red_terracotta": "Terracota roja", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> rojo", "block.minecraft.red_wool": "<PERSON>", "block.minecraft.redstone_block": "Bloque de redstone", "block.minecraft.redstone_lamp": "Lámpara de redstone", "block.minecraft.redstone_ore": "Mena de redstone", "block.minecraft.redstone_torch": "Antorcha de redstone", "block.minecraft.redstone_wall_torch": "<PERSON><PERSON><PERSON> de redstone en pared", "block.minecraft.redstone_wire": "Cable de redstone", "block.minecraft.reinforced_deepslate": "Pizarra abismal reforzada", "block.minecraft.repeater": "Repetidor de redstone", "block.minecraft.repeating_command_block": "Bloque de comandos de repetición", "block.minecraft.resin_block": "Bloque de Resina", "block.minecraft.resin_brick_slab": "Losa de ladrillo de resina", "block.minecraft.resin_brick_stairs": "Escaleras de ladrillos de resina", "block.minecraft.resin_brick_wall": "<PERSON><PERSON> de ladrillo de resina", "block.minecraft.resin_bricks": "Ladrillos de resina", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON> de resina", "block.minecraft.respawn_anchor": "Nexo de reaparición", "block.minecraft.rooted_dirt": "<PERSON>ra enraizada", "block.minecraft.rose_bush": "<PERSON><PERSON>", "block.minecraft.sand": "Arena", "block.minecraft.sandstone": "Arenisca", "block.minecraft.sandstone_slab": "Losa de arenisca", "block.minecraft.sandstone_stairs": "Escaleras de arenisca", "block.minecraft.sandstone_wall": "Muro de aren<PERSON>ca", "block.minecraft.scaffolding": "Andamio", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Catalizador de sculk", "block.minecraft.sculk_sensor": "Sensor de sculk", "block.minecraft.sculk_shrieker": "<PERSON><PERSON><PERSON> de sculk", "block.minecraft.sculk_vein": "<PERSON><PERSON> de sculk", "block.minecraft.sea_lantern": "Linterna del mar", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "Hierba marina", "block.minecraft.set_spawn": "Punto de reaparición establecido", "block.minecraft.short_dry_grass": "Hierba seca corta", "block.minecraft.short_grass": "Grama Corta", "block.minecraft.shroomlight": "Brillongo", "block.minecraft.shulker_box": "<PERSON><PERSON> de <PERSON>ker", "block.minecraft.skeleton_skull": "<PERSON>r<PERSON><PERSON>", "block.minecraft.skeleton_wall_skull": "<PERSON>r<PERSON><PERSON> de esqueleto en pared", "block.minecraft.slime_block": "Bloque de slime", "block.minecraft.small_amethyst_bud": "Brote de amatista pequeño", "block.minecraft.small_dripleaf": "Plantaforma pequeña", "block.minecraft.smithing_table": "Mesa de herrería", "block.minecraft.smoker": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Ba<PERSON>to liso", "block.minecraft.smooth_quartz": "Bloque de cuarzo liso", "block.minecraft.smooth_quartz_slab": "Losa de cuarzo liso", "block.minecraft.smooth_quartz_stairs": "Escaleras de cuarzo liso", "block.minecraft.smooth_red_sandstone": "Arenisca roja lisa", "block.minecraft.smooth_red_sandstone_slab": "Losa de arenisca roja lisa", "block.minecraft.smooth_red_sandstone_stairs": "Escaleras de arenisca roja lisa", "block.minecraft.smooth_sandstone": "Arenisca lisa", "block.minecraft.smooth_sandstone_slab": "Losa de arenisca lisa", "block.minecraft.smooth_sandstone_stairs": "Escaleras de arenisca lisa", "block.minecraft.smooth_stone": "<PERSON><PERSON> lisa", "block.minecraft.smooth_stone_slab": "Losa de piedra lisa", "block.minecraft.sniffer_egg": "<PERSON><PERSON> de sniffer", "block.minecraft.snow": "<PERSON><PERSON>", "block.minecraft.snow_block": "Bloque de nieve", "block.minecraft.soul_campfire": "Fogata de almas", "block.minecraft.soul_fire": "Fuego de almas", "block.minecraft.soul_lantern": "Farol de almas", "block.minecraft.soul_sand": "Arena de almas", "block.minecraft.soul_soil": "Tierra de almas", "block.minecraft.soul_torch": "An<PERSON><PERSON> de almas", "block.minecraft.soul_wall_torch": "<PERSON><PERSON><PERSON> de almas en pared", "block.minecraft.spawn.not_valid": "No tienes cama ni nexo de reaparición cargado, o están obstruidos", "block.minecraft.spawner": "Generador de criaturas", "block.minecraft.spawner.desc1": "Interactúa con un huevo generador:", "block.minecraft.spawner.desc2": "Fija el tipo de criatura", "block.minecraft.sponge": "Esponja", "block.minecraft.spore_blossom": "Flor de esporas", "block.minecraft.spruce_button": "Botón de pino", "block.minecraft.spruce_door": "Puerta de pino", "block.minecraft.spruce_fence": "Cerca de pino", "block.minecraft.spruce_fence_gate": "Puerta de cerca de pino", "block.minecraft.spruce_hanging_sign": "Cartel de pino colgante", "block.minecraft.spruce_leaves": "Hojas de pino", "block.minecraft.spruce_log": "Tronco de pino", "block.minecraft.spruce_planks": "<PERSON><PERSON> de pino", "block.minecraft.spruce_pressure_plate": "Placa de presión de pino", "block.minecraft.spruce_sapling": "Arbolito de pino", "block.minecraft.spruce_sign": "Cartel de pino", "block.minecraft.spruce_slab": "Losa de pino", "block.minecraft.spruce_stairs": "Escaleras de pino", "block.minecraft.spruce_trapdoor": "Escotilla de pino", "block.minecraft.spruce_wall_hanging_sign": "Cartel colgante de pino en pared", "block.minecraft.spruce_wall_sign": "Cartel de pino en pared", "block.minecraft.spruce_wood": "Leño de pino", "block.minecraft.sticky_piston": "<PERSON><PERSON><PERSON> pegajoso", "block.minecraft.stone": "Piedra", "block.minecraft.stone_brick_slab": "Losa de ladrillos de piedra", "block.minecraft.stone_brick_stairs": "Escaleras de ladrillos de piedra", "block.minecraft.stone_brick_wall": "<PERSON><PERSON> de ladrillos de piedra", "block.minecraft.stone_bricks": "Ladrillos de piedra", "block.minecraft.stone_button": "Botón de piedra", "block.minecraft.stone_pressure_plate": "Placa de presión de piedra", "block.minecraft.stone_slab": "Losa de piedra", "block.minecraft.stone_stairs": "Escaleras de piedra", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Tronco de acacia sin corteza", "block.minecraft.stripped_acacia_wood": "Leño de acacia sin corteza", "block.minecraft.stripped_bamboo_block": "Bloque de bambú sin corteza", "block.minecraft.stripped_birch_log": "Tronco de abedul sin corteza", "block.minecraft.stripped_birch_wood": "Leño de abedul sin corteza", "block.minecraft.stripped_cherry_log": "Tronco de cerezo sin corteza", "block.minecraft.stripped_cherry_wood": "Leño de cerezo sin corteza", "block.minecraft.stripped_crimson_hyphae": "Hifas carmesí sin corteza", "block.minecraft.stripped_crimson_stem": "Tallo carmesí sin corteza", "block.minecraft.stripped_dark_oak_log": "Tronco de roble oscuro sin corteza", "block.minecraft.stripped_dark_oak_wood": "Leño de roble oscuro sin corteza", "block.minecraft.stripped_jungle_log": "Tronco de jungla sin corteza", "block.minecraft.stripped_jungle_wood": "Leño de jungla sin corteza", "block.minecraft.stripped_mangrove_log": "Tronco de mangle sin corteza", "block.minecraft.stripped_mangrove_wood": "Leño de mangle sin corteza", "block.minecraft.stripped_oak_log": "Tronco de roble sin corteza", "block.minecraft.stripped_oak_wood": "Leño de roble sin corteza", "block.minecraft.stripped_pale_oak_log": "Tronco de roble pálido sin corteza", "block.minecraft.stripped_pale_oak_wood": "Leño de roble seco sin corteza", "block.minecraft.stripped_spruce_log": "Tronco de pino sin corteza", "block.minecraft.stripped_spruce_wood": "Leño de pino sin corteza", "block.minecraft.stripped_warped_hyphae": "Hifas distorsionadas sin corteza", "block.minecraft.stripped_warped_stem": "Tallo distorsionado sin corteza", "block.minecraft.structure_block": "Bloque estructural", "block.minecraft.structure_void": "Vacío estructural", "block.minecraft.sugar_cane": "Caña de azúcar", "block.minecraft.sunflower": "Girasol", "block.minecraft.suspicious_gravel": "Gravilla sospechosa", "block.minecraft.suspicious_sand": "Arena sospechosa", "block.minecraft.sweet_berry_bush": "Arbusto de bayas dulces", "block.minecraft.tall_dry_grass": "Hierba seca alta", "block.minecraft.tall_grass": "Hierba alta", "block.minecraft.tall_seagrass": "Hierba marina alta", "block.minecraft.target": "<PERSON>", "block.minecraft.terracotta": "Terracota", "block.minecraft.test_block": "Bloque de pruebas", "block.minecraft.test_instance_block": "Bloque de instancia de prueba", "block.minecraft.tinted_glass": "Cristal opaco", "block.minecraft.tnt": "<PERSON><PERSON><PERSON>", "block.minecraft.tnt.disabled": "Las explosiones de TNT están desactivadas", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "Florantorcha", "block.minecraft.torchflower_crop": "Cultivo de florantorcha", "block.minecraft.trapped_chest": "<PERSON><PERSON> trampa", "block.minecraft.trial_spawner": "Generador de p<PERSON>bas", "block.minecraft.tripwire": "<PERSON><PERSON> trampa", "block.minecraft.tripwire_hook": "<PERSON><PERSON><PERSON>", "block.minecraft.tube_coral": "Coral de tubo", "block.minecraft.tube_coral_block": "Bloque de coral de tubo", "block.minecraft.tube_coral_fan": "Gorgonia de tubo", "block.minecraft.tube_coral_wall_fan": "<PERSON><PERSON><PERSON> de tubo en pared", "block.minecraft.tuff": "Toba", "block.minecraft.tuff_brick_slab": "Losa de ladrillos de toba", "block.minecraft.tuff_brick_stairs": "Escaleras de ladrillos de toba", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON> de ladrillos de toba", "block.minecraft.tuff_bricks": "Ladrillos de toba", "block.minecraft.tuff_slab": "Losa de toba", "block.minecraft.tuff_stairs": "Escaleras de toba", "block.minecraft.tuff_wall": "<PERSON><PERSON>", "block.minecraft.turtle_egg": "Huevo de tortuga", "block.minecraft.twisting_vines": "Enredaderas retorcidas", "block.minecraft.twisting_vines_plant": "Enredaderas retorcidas", "block.minecraft.vault": "Bóveda", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON> verde", "block.minecraft.vine": "Enredaderas", "block.minecraft.void_air": "Aire del vacío", "block.minecraft.wall_torch": "An<PERSON><PERSON> en pared", "block.minecraft.warped_button": "Botón distorsionado", "block.minecraft.warped_door": "Puerta distorsionada", "block.minecraft.warped_fence": "Cerca distorsionada", "block.minecraft.warped_fence_gate": "Puerta de cerca distorsionada", "block.minecraft.warped_fungus": "Hongo distorsionado", "block.minecraft.warped_hanging_sign": "Cartel colgante distorsionado", "block.minecraft.warped_hyphae": "Hifas distorsionadas", "block.minecraft.warped_nylium": "Necelio distorsionado", "block.minecraft.warped_planks": "Madera distorsionada", "block.minecraft.warped_pressure_plate": "Placa de presión distorsionada", "block.minecraft.warped_roots": "Raíces distorsionadas", "block.minecraft.warped_sign": "Cartel distorsionado", "block.minecraft.warped_slab": "Losa distorsionada", "block.minecraft.warped_stairs": "Escaleras distorsionadas", "block.minecraft.warped_stem": "Tallo distorsionado", "block.minecraft.warped_trapdoor": "Escotilla distorsionada", "block.minecraft.warped_wall_hanging_sign": "Cartel colgante distorsionado en pared", "block.minecraft.warped_wall_sign": "Cartel distorsionado en pared", "block.minecraft.warped_wart_block": "Bloque de verrugas distorsionadas", "block.minecraft.water": "Agua", "block.minecraft.water_cauldron": "Caldero con agua", "block.minecraft.waxed_chiseled_copper": "Cobre cincelado encerado", "block.minecraft.waxed_copper_block": "Bloque de cobre encerado", "block.minecraft.waxed_copper_bulb": "Lámpara de cobre encerada", "block.minecraft.waxed_copper_door": "Puerta de cobre encerado", "block.minecraft.waxed_copper_grate": "<PERSON><PERSON><PERSON> de cobre encerado", "block.minecraft.waxed_copper_trapdoor": "Escotilla de cobre encerado", "block.minecraft.waxed_cut_copper": "Cobre cortado encerado", "block.minecraft.waxed_cut_copper_slab": "Losa de cobre cortado encerado", "block.minecraft.waxed_cut_copper_stairs": "Escaleras de cobre cortado encerado", "block.minecraft.waxed_exposed_chiseled_copper": "Cobre cincelado expuesto encerado", "block.minecraft.waxed_exposed_copper": "Cobre expuesto encerado", "block.minecraft.waxed_exposed_copper_bulb": "Lámpara de cobre encerado expuesta", "block.minecraft.waxed_exposed_copper_door": "Puerta de cobre encerada expuesta", "block.minecraft.waxed_exposed_copper_grate": "Rejilla de cobre encerado expuesta", "block.minecraft.waxed_exposed_copper_trapdoor": "Escotilla de cobre expuesta encerada", "block.minecraft.waxed_exposed_cut_copper": "Cobre cortado expuesto encerado", "block.minecraft.waxed_exposed_cut_copper_slab": "Losa de cobre cortado expuesto encerado", "block.minecraft.waxed_exposed_cut_copper_stairs": "Escaleras de cobre cortado expuesto encerado", "block.minecraft.waxed_oxidized_chiseled_copper": "Cobre cincelado oxidado encerado", "block.minecraft.waxed_oxidized_copper": "Cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_bulb": "Lámpara de cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_door": "Puerta de cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_grate": "Rejilla de cobre oxidado encerado", "block.minecraft.waxed_oxidized_copper_trapdoor": "Escotilla de cobre oxidado encerado", "block.minecraft.waxed_oxidized_cut_copper": "Cobre cortado oxidado encerado", "block.minecraft.waxed_oxidized_cut_copper_slab": "Losa de cobre cortado oxidado encerado", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Escaleras de cobre cortado oxidado encerado", "block.minecraft.waxed_weathered_chiseled_copper": "Cobre cincelado degradado encerado", "block.minecraft.waxed_weathered_copper": "Cobre degradado encerado", "block.minecraft.waxed_weathered_copper_bulb": "Lámpara de cobre degradado encerado", "block.minecraft.waxed_weathered_copper_door": "Puerta de cobre degradado encerado", "block.minecraft.waxed_weathered_copper_grate": "Rejilla de cobre degradado encerado", "block.minecraft.waxed_weathered_copper_trapdoor": "Escotilla de cobre degradada encerada", "block.minecraft.waxed_weathered_cut_copper": "Cobre cortado degradado encerado", "block.minecraft.waxed_weathered_cut_copper_slab": "Losa de cobre cortado degradado encerado", "block.minecraft.waxed_weathered_cut_copper_stairs": "Escaleras de cobre cortado degradado encerado", "block.minecraft.weathered_chiseled_copper": "Cobre cincelado degradado", "block.minecraft.weathered_copper": "Cobre degradado", "block.minecraft.weathered_copper_bulb": "Lámpara de cobre degradado", "block.minecraft.weathered_copper_door": "Puerta de cobre degradado", "block.minecraft.weathered_copper_grate": "Rejilla de cobre degradado", "block.minecraft.weathered_copper_trapdoor": "Escotilla de cobre degradado", "block.minecraft.weathered_cut_copper": "Cobre cortado degradado", "block.minecraft.weathered_cut_copper_slab": "Losa de cobre cortado degradado", "block.minecraft.weathered_cut_copper_stairs": "Escaleras de cobre cortado degradado", "block.minecraft.weeping_vines": "Enredaderas lloronas", "block.minecraft.weeping_vines_plant": "Planta de enredaderas lloronas", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wheat": "Cultivo de trigo", "block.minecraft.white_banner": "Estandarte blanco", "block.minecraft.white_bed": "Cama blanca", "block.minecraft.white_candle": "<PERSON>ela blanca", "block.minecraft.white_candle_cake": "Torta con vela blanca", "block.minecraft.white_carpet": "Alfombra blanca", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.white_concrete_powder": "Cemento blanco", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON> blanco", "block.minecraft.white_shulker_box": "Caja de shulker blanca", "block.minecraft.white_stained_glass": "Cristal blanco", "block.minecraft.white_stained_glass_pane": "Panel de cristal blanco", "block.minecraft.white_terracotta": "Terracota blanca", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> blanco", "block.minecraft.white_wool": "<PERSON> blan<PERSON>", "block.minecraft.wildflowers": "<PERSON>lore<PERSON><PERSON> silvestres", "block.minecraft.wither_rose": "<PERSON>", "block.minecraft.wither_skeleton_skull": "Cráneo de esqueleto <PERSON>er", "block.minecraft.wither_skeleton_wall_skull": "Cráneo de esqueleto del Wither en pared", "block.minecraft.yellow_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_bed": "<PERSON><PERSON> amarilla", "block.minecraft.yellow_candle": "<PERSON><PERSON> amarilla", "block.minecraft.yellow_candle_cake": "Torta con vela amarilla", "block.minecraft.yellow_carpet": "Alfombra amarilla", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON> de shulker amarilla", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass_pane": "Panel de cristal amarillo", "block.minecraft.yellow_terracotta": "Terracota amarilla", "block.minecraft.yellow_wool": "<PERSON>", "block.minecraft.zombie_head": "Cabeza de zombi", "block.minecraft.zombie_wall_head": "Cabeza de zombi en pared", "book.byAuthor": "por %1$s", "book.edit.title": "Pantalla de Edición de Libros", "book.editTitle": "Título del libro:", "book.finalizeButton": "<PERSON><PERSON><PERSON> y cerrar", "book.finalizeWarning": "¡Nota! Al firmar el libro, ya no se podrá editarlo.", "book.generation.0": "Original", "book.generation.1": "Copia del original", "book.generation.2": "Copia de una copia", "book.generation.3": "<PERSON><PERSON><PERSON>", "book.invalid.tag": "* Tag de libro inválido *", "book.pageIndicator": "Página %1$s de %2$s", "book.page_button.next": "Siguiente página", "book.page_button.previous": "Página anterior", "book.sign.title": "Pantalla de firma del libro", "book.sign.titlebox": "<PERSON><PERSON><PERSON><PERSON>", "book.signButton": "<PERSON><PERSON><PERSON>", "book.view.title": "Pantalla de vista del libro", "build.tooHigh": "El límite para construir es de %s bloques", "chat.cannotSend": "No se pudo enviar el mensaje, revisa tus opciones de chat", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Haz clic para teletransportarte", "chat.copy": "<PERSON><PERSON><PERSON> enlace", "chat.copy.click": "<PERSON>z clic para copiar", "chat.deleted_marker": "Este mensaje fue eliminado por el servidor.", "chat.disabled.chain_broken": "El chat se deshabilitó debido a una cadena rota. Por favor, intenta reconectar.", "chat.disabled.expiredProfileKey": "Se ha desactivado el chat porque la clave pública de perfil ha caducado. Intenta reconectar.", "chat.disabled.invalid_command_signature": "El comando tenia firmas de argumentos de comando faltantes o restantes.", "chat.disabled.invalid_signature": "El chat tenía una firma no válida. Intenta reconectar.", "chat.disabled.launcher": "Chat desactivado por el lanzador. No puedes enviar mensajes.", "chat.disabled.missingProfileKey": "El chat se desabilitó debido a la falta de la clave pública del perfil. Por favor, intenta reconectar.", "chat.disabled.options": "Chat desactivado en opciones del cliente.", "chat.disabled.out_of_order_chat": "Se ha recibido el chat desordenado. ¿Ha habido algún cambio en la hora de tu dispositivo?", "chat.disabled.profile": "Chat no permitido por la configuración de la cuenta. Presiona '%s' Otra vez para mas información.", "chat.disabled.profile.moreInfo": "Chat no permitido por los ajustes de la cuenta. No se pueden ver ni mandar mensajes.", "chat.editBox": "chat", "chat.filtered": "Filtrado por el servidor.", "chat.filtered_full": "El servidor ha ocultado tu mensaje para algunos jugadores.", "chat.link.confirm": "¿Estás seguro de que quieres abrir el siguiente sitio web?", "chat.link.confirmTrusted": "¿Quieres abrir este enlace o copiarlo?", "chat.link.open": "<PERSON><PERSON><PERSON> enlace", "chat.link.warning": "¡Nunca abras enlaces de personas que no conozcas!", "chat.queue": "[+%s líneas pendientes]", "chat.square_brackets": "[%s]", "chat.tag.error": "El servidor ha enviado un mensaje no válido.", "chat.tag.modified": "Mensaje modificado por el servidor. Original:", "chat.tag.not_secure": "Mensaje no verificado. No se puede reportar.", "chat.tag.system": "Mensaje del servidor. No se puede reportar.", "chat.tag.system_single_player": "Mensaje del servidor.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s ha completado el desafio %s", "chat.type.advancement.goal": "%s ha alcanzado el objetivo %s", "chat.type.advancement.task": "%s ha conseguido el progreso %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Enviar mensaje al equipo", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s dice: %s", "chat.validation_error": "Error de validación de chat", "chat_screen.message": "Mensaje para enviar: %s", "chat_screen.title": "<PERSON><PERSON><PERSON>", "chat_screen.usage": "Escribe un mensaje y presiona Enter para enviar", "chunk.toast.checkLog": "Consulta el registro del juego para más detalles", "chunk.toast.loadFailure": "Error al cargar el chunk en %s", "chunk.toast.lowDiskSpace": "¡Queda poco espacio en el disco!", "chunk.toast.lowDiskSpace.description": "Es posible que no se pueda guardar el mundo.", "chunk.toast.saveFailure": "Error al guardar el chunk en %s", "clear.failed.multiple": "No se encontraron ítems en el inventario de %s jugadores", "clear.failed.single": "No se encontraron ítems en el inventario de %s", "color.minecraft.black": "Negro", "color.minecraft.blue": "Azul", "color.minecraft.brown": "<PERSON>r<PERSON>", "color.minecraft.cyan": "Turquesa", "color.minecraft.gray": "<PERSON><PERSON>", "color.minecraft.green": "Verde", "color.minecraft.light_blue": "A<PERSON>l cielo", "color.minecraft.light_gray": "<PERSON><PERSON> claro", "color.minecraft.lime": "Verde lima", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "<PERSON><PERSON><PERSON>", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "<PERSON><PERSON>", "color.minecraft.red": "<PERSON><PERSON><PERSON>", "color.minecraft.white": "<PERSON>", "color.minecraft.yellow": "Amarillo", "command.context.here": "<--[AQUÍ]", "command.context.parse_error": "%s en la posición %s: %s", "command.exception": "No se puede analizar el comando: %s", "command.expected.separator": "Se requiere un espacio en blanco para concluir un argumento. Algunos datos están escritos sin separar", "command.failed": "Se produjo un error inesperado al intentar ejecutar ese comando", "command.forkLimit": "Se ha alcanzado el número máximo de contextos (%s)", "command.unknown.argument": "<PERSON><PERSON><PERSON><PERSON>", "command.unknown.command": "Comando desconocido o incompleto, revisa el error a continuación", "commands.advancement.criterionNotFound": "El progreso %1$s no contiene el criterio \"%2$s\"", "commands.advancement.grant.criterion.to.many.failure": "No se le pudo dar el criterio \"%s\" del progreso %s a %s jugadores porque ya lo tienen", "commands.advancement.grant.criterion.to.many.success": "Criterio \"%s\" del progreso %s dado a %s jugadores", "commands.advancement.grant.criterion.to.one.failure": "No se pudo dar el criterio \"%s\" del progreso %s a %s porque ya lo tiene", "commands.advancement.grant.criterion.to.one.success": "Criterio \"%s\" del progreso %s dado a %s", "commands.advancement.grant.many.to.many.failure": "No se le pudo dar %s progresos a %s jugadores porque ya los tienen", "commands.advancement.grant.many.to.many.success": "Se han dado %s progresos a %s jugadores", "commands.advancement.grant.many.to.one.failure": "No se han podido dar %s progresos a %s porque ya los tiene", "commands.advancement.grant.many.to.one.success": "Se han dado %s progresos a %s", "commands.advancement.grant.one.to.many.failure": "No se pudo dar el progreso %s a %s jugadores porque ya lo tienen", "commands.advancement.grant.one.to.many.success": "Progreso %s dado a %s jugadores", "commands.advancement.grant.one.to.one.failure": "No se pudo dar el progreso %s a %s porque ya lo tiene", "commands.advancement.grant.one.to.one.success": "Progreso %s dado a %s", "commands.advancement.revoke.criterion.to.many.failure": "No se les pudo borrar el criterio \"%s\" del progreso %s a %s jugadores porque no lo tienen", "commands.advancement.revoke.criterion.to.many.success": "Criterio \"%s\" del progreso %s borrado a %s jugadores", "commands.advancement.revoke.criterion.to.one.failure": "No se pudo borrar el criterio \"%s\" del progreso %s a %s porque no lo tiene", "commands.advancement.revoke.criterion.to.one.success": "Criterio \"%s\" del progreso %s borrado a %s", "commands.advancement.revoke.many.to.many.failure": "No se les pudo borrar %s progresos a %s jugadores porque no los tienen", "commands.advancement.revoke.many.to.many.success": "%s progresos borrados a %s jugadores", "commands.advancement.revoke.many.to.one.failure": "No se le pudo borrar %s progresos a %s porque no los tiene", "commands.advancement.revoke.many.to.one.success": "%s progresos borrados a %s", "commands.advancement.revoke.one.to.many.failure": "No se pudo borrar el progreso %s a %s jugadores porque no lo tienen", "commands.advancement.revoke.one.to.many.success": "Progreso %s borrado a %s jugadores", "commands.advancement.revoke.one.to.one.failure": "No se pudo borrar el progreso %s a %s porque no lo tiene", "commands.advancement.revoke.one.to.one.success": "Progreso %s borrado a %s", "commands.attribute.base_value.get.success": "El valor base del atributo %s de la entidad %s es %s", "commands.attribute.base_value.reset.success": "Valor Base para atributos %s para entidades %s restablecido al valor predeterminado", "commands.attribute.base_value.set.success": "Valor base del atributo %s de la entidad %s establecido a %s", "commands.attribute.failed.entity": "%s no es una entidad válida para este comando", "commands.attribute.failed.modifier_already_present": "El modificador %s ya está presente en el atributo %s de la entidad %s", "commands.attribute.failed.no_attribute": "La entidad %s no tiene el atributo %s", "commands.attribute.failed.no_modifier": "El atributo %s de la entidad %s no tiene el modificador %s", "commands.attribute.modifier.add.success": "Modificador %s agregado al atributo %s de la entidad %s", "commands.attribute.modifier.remove.success": "Modificador %s eliminado del atributo %s de la entidad %s", "commands.attribute.modifier.value.get.success": "El valor del modificador %s del atributo %s de la entidad %s es %s", "commands.attribute.value.get.success": "El valor del atributo %s de la entidad %s es %s", "commands.ban.failed": "Nada cambió: el jugador ya estaba baneado", "commands.ban.success": "Se baneó a %s: %s", "commands.banip.failed": "Nada cambió: la IP ya estaba baneada", "commands.banip.info": "Este baneo afecta a %s jugador(es): %s", "commands.banip.invalid": "IP no válida o jugador desconocido", "commands.banip.success": "Se baneó la IP %s: %s", "commands.banlist.entry": "%2$s baneó a %1$s: %3$s", "commands.banlist.entry.unknown": "(Desconocid@)", "commands.banlist.list": "Hay %s prohibición(es):", "commands.banlist.none": "No hay baneos", "commands.bossbar.create.failed": "Ya existe una barra de jefe con ID \"%s\"", "commands.bossbar.create.success": "Barra de jefe personalizada %s creada", "commands.bossbar.get.max": "La barra de jefe personalizada %s tiene un valor máximo de %s", "commands.bossbar.get.players.none": "La barra de jefe personalizada %s no tiene ningún jugador en línea", "commands.bossbar.get.players.some": "La barra de jefe personalizada %s tiene %s jugador(es) en línea: %s", "commands.bossbar.get.value": "La barra de jefe personalizada %s tiene un valor de %s", "commands.bossbar.get.visible.hidden": "La barra de jefe personalizada %s se oculta actualmente", "commands.bossbar.get.visible.visible": "La barra de jefe personalizada %s se muestra actualmente", "commands.bossbar.list.bars.none": "No hay barras de jefe personalizadas activas", "commands.bossbar.list.bars.some": "Hay %s barra(s) personalizada(s) activas: %s", "commands.bossbar.remove.success": "Barra de jefe personalizada %s borrada", "commands.bossbar.set.color.success": "La barra de jefe personalizada %s cambió de color", "commands.bossbar.set.color.unchanged": "Nada cambió: ese ya es el color de esta barra de jefe", "commands.bossbar.set.max.success": "La barra de jefe personalizada %s cambió de máximo a %s", "commands.bossbar.set.max.unchanged": "Nada cambió: ese ya es el máximo de esta barra de jefe", "commands.bossbar.set.name.success": "La barra de jefe personalizada %s cambió de nombre", "commands.bossbar.set.name.unchanged": "Nada cambió: ese ya es el nombre de esta barra de jefe", "commands.bossbar.set.players.success.none": "La barra de jefe personalizada %s ya no tiene jugadores", "commands.bossbar.set.players.success.some": "La barra de jefe personalizada %s ahora tiene %s jugador(es): %s", "commands.bossbar.set.players.unchanged": "Nada cambió: esos jugadores ya están en la barra de jefe sin nadie para agregar o quitar", "commands.bossbar.set.style.success": "La barra de jefe personalizada %s cambió de estilo", "commands.bossbar.set.style.unchanged": "Nada cambió: ese ya es el estilo de esta barra de jefe", "commands.bossbar.set.value.success": "La barra de jefe personalizada %s cambió de valor a %s", "commands.bossbar.set.value.unchanged": "Nada cambió: ese ya es el valor de esta barra de jefe", "commands.bossbar.set.visibility.unchanged.hidden": "Nada cambió: la barra de jefe ya estaba oculta", "commands.bossbar.set.visibility.unchanged.visible": "Nada cambió: la barra de jefe ya era visible", "commands.bossbar.set.visible.success.hidden": "La barra de jefe personalizada %s se oculta", "commands.bossbar.set.visible.success.visible": "La barra de jefe personalizada %s se muestra", "commands.bossbar.unknown": "No existe ninguna barra de jefe con ID \"%s\"", "commands.clear.success.multiple": "%s objeto(s) fueron eliminados de %s jugadores", "commands.clear.success.single": "%s objeto(s) fueron eliminados de %s", "commands.clear.test.multiple": "Se encontraron %s objeto(s) que coinciden en el inventario de %s jugadores", "commands.clear.test.single": "Se encontraron %s objeto(s) que coinciden en el inventario de %s", "commands.clone.failed": "No se clonaron bloques", "commands.clone.overlap": "El área de origen y el área de destino no pueden coincidir", "commands.clone.success": "Bloques clonados con éxito: %s", "commands.clone.toobig": "Hay demasiados bloques en el área especificada (el máximo es %s, se especificaron %s)", "commands.damage.invulnerable": "La entidad es invulnerable al tipo de daño infligido", "commands.damage.success": "Se ha infligido %s de daño a %s", "commands.data.block.get": "%s en el bloque en %s, %s, %s, luego del factor escala de %s cambió a %s", "commands.data.block.invalid": "El bloque especificado no es una entidad-bloque", "commands.data.block.modified": "Los datos del bloque en %s, %s, %s fueron modificados", "commands.data.block.query": "El bloque en %s, %s, %s contiene los siguientes datos: %s", "commands.data.entity.get": "%s de %s, luego del factor escala de %s, cambió a %s", "commands.data.entity.invalid": "No se pudieron modificar los datos del jugador", "commands.data.entity.modified": "Datos de la entidad de %s modificados", "commands.data.entity.query": "La entidad %s tiene los siguientes datos: %s", "commands.data.get.invalid": "No se pudo obtener %s, sólo se permiten etiquetas numéricas", "commands.data.get.multiple": "Este argumento sólo acepta un valor NBT", "commands.data.get.unknown": "No se pudo obtener %s, la etiqueta no existe", "commands.data.merge.failed": "Nada cambió: las propiedades especificadas ya tenían esos valores", "commands.data.modify.expected_list": "Se requiere una lista (recibido: %s)", "commands.data.modify.expected_object": "Se requiere un ítem (recibido: %s)", "commands.data.modify.expected_value": "Se requiere un valor, obtenido: %s", "commands.data.modify.invalid_index": "Índice de lista no válido: %s", "commands.data.modify.invalid_substring": "Índices de subcadena no válidos: de %s a %s", "commands.data.storage.get": "%s en el contenedor %s, tras un factor escala de %s, es %s", "commands.data.storage.modified": "Contenedor %s modificado", "commands.data.storage.query": "El contenedor %s contiene lo siguiente: %s", "commands.datapack.create.already_exists": "Ya existe un paquete con el nombre '%s'", "commands.datapack.create.invalid_full_name": "«%s» No es un nombre de paquete válido", "commands.datapack.create.invalid_name": "Caracteres no válidos en el nuevo nombre del paquete '%s'", "commands.datapack.create.io_failure": "No se puede crear el paquete con el nombre '%s', verifique los registros", "commands.datapack.create.metadata_encode_failure": "Error al codificar los metadatos del paquete '%s': %s", "commands.datapack.create.success": "Se creó un nuevo paquete vacío con el nombre '%s'", "commands.datapack.disable.failed": "¡El paquete de datos \"%s\" no estaba activado!", "commands.datapack.disable.failed.feature": "¡El paquete de datos «%s» no puede desactivarse porque es parte de una de las variables activas!", "commands.datapack.enable.failed": "¡El paquete de datos \"%s\" ya estaba activado!", "commands.datapack.enable.failed.no_flags": "El paquete \"%s\" no puede ser habilitado porque las siguientes características no están habilitadas en este mundo: %s", "commands.datapack.list.available.none": "No hay más paquetes de datos disponibles", "commands.datapack.list.available.success": "Aquí hay %s un paquete(s) de datos disponible %s", "commands.datapack.list.enabled.none": "No hay paquetes de datos habilitados", "commands.datapack.list.enabled.success": "Aquí hay %s un paquete(s) de datos activo: %s", "commands.datapack.modify.disable": "Desactivando el paquete de datos %s", "commands.datapack.modify.enable": "Activando el paquete de datos %s", "commands.datapack.unknown": "Paquete de datos desconocido: %s", "commands.debug.alreadyRunning": "El análisis de ticks ya ha comenzado", "commands.debug.function.noRecursion": "No se puede rastrear dentro de la función", "commands.debug.function.noReturnRun": "No se puede rastrear con /return run", "commands.debug.function.success.multiple": "Se rastrearon %s comando(s) de %s funciones al archivo de salida %s", "commands.debug.function.success.single": "Se rastrearon %s comando(s) de la función «%s» al archivo de salida %s", "commands.debug.function.traceFailed": "Error al rastrear función", "commands.debug.notRunning": "No se inicio el perfilador de ciclos", "commands.debug.started": "Análisis debug iniciado", "commands.debug.stopped": "Análisis de ticks detenido luego de %s segundos y %s ticks (%s ticks por segundo)", "commands.defaultgamemode.success": "El modo de juego por defecto ahora es %s", "commands.deop.failed": "Nada cambió: el jugador no era administrador", "commands.deop.success": "%s ya no es administrador(a) del servidor", "commands.dialog.clear.multiple": "Diálogo cerrado para %s jugadores", "commands.dialog.clear.single": "Diálogo cerrado para %s", "commands.dialog.show.multiple": "Mostrando diálogo a %s jugadores", "commands.dialog.show.single": "Mostrando diálogo a %s", "commands.difficulty.failure": "La dificultad no se cambió, ya estaba configurada en %s", "commands.difficulty.query": "La dificultad está configurada en %s", "commands.difficulty.success": "La dificultad cambió a %s", "commands.drop.no_held_items": "La entidad no puede sostener ítems", "commands.drop.no_loot_table": "La entidad %s no tiene tabla de botín", "commands.drop.no_loot_table.block": "El bloque %s no tiene tabla de botín", "commands.drop.success.multiple": "Ítems soltados: %s", "commands.drop.success.multiple_with_table": "Ítems soltados de la tabla de botín %2$s: %1$s", "commands.drop.success.single": "Ítems soltados: %s de %s", "commands.drop.success.single_with_table": "Ítems soltados de la tabla de botín %3$s: %1$s de %2$s", "commands.effect.clear.everything.failed": "El objetivo no tiene efectos para borrar", "commands.effect.clear.everything.success.multiple": "Borrados todos los efectos de %s entidades", "commands.effect.clear.everything.success.single": "Todos los efectos de %s se borraron", "commands.effect.clear.specific.failed": "El objetivo no tiene el efecto especificado", "commands.effect.clear.specific.success.multiple": "Efecto %s borrado de %s entidades", "commands.effect.clear.specific.success.single": "Efecto %s borrado de %s", "commands.effect.give.failed": "No se pudo aplicar este efecto (el objetivo es inmune a los efectos o tiene algo más potente)", "commands.effect.give.success.multiple": "Efecto %s aplicado a %s entidades", "commands.effect.give.success.single": "Efecto %s aplicado a %s", "commands.enchant.failed": "Nada cambió: el objetivo no tenía un ítem en la mano o el ítem no es compatible con el encantamiento", "commands.enchant.failed.entity": "%s no es una entidad válida para este comando", "commands.enchant.failed.incompatible": "%s no admite este encantamiento", "commands.enchant.failed.itemless": "%s no sostiene ningún ítem", "commands.enchant.failed.level": "%s es superior al nivel máximo del encantamiento (%s)", "commands.enchant.success.multiple": "Encantamiento %s aplicado a %s entidades", "commands.enchant.success.single": "Encantamiento %s aplicado al ítem de %s", "commands.execute.blocks.toobig": "Demasiados bloques en el área especificada (el máximo es %s y se especificaron %s)", "commands.execute.conditional.fail": "Prueba fallida", "commands.execute.conditional.fail_count": "Prueba fallida, cantidad: %s", "commands.execute.conditional.pass": "Prueba superada", "commands.execute.conditional.pass_count": "Prueba superada, cantidad: %s", "commands.execute.function.instantiationFailure": "Error al ejecutar la función %s: %s", "commands.experience.add.levels.success.multiple": "%s niveles de experiencia dados a %s jugadores", "commands.experience.add.levels.success.single": "%s niveles de experiencia dados a %s", "commands.experience.add.points.success.multiple": "%s puntos de experiencia dados a %s jugadores", "commands.experience.add.points.success.single": "%s puntos de experiencia dados a %s", "commands.experience.query.levels": "%s tiene %s niveles de experiencia", "commands.experience.query.points": "%s tiene %s puntos de experiencia", "commands.experience.set.levels.success.multiple": "Los niveles de experiencia de %2$s jugadores cambiaron a %1$s", "commands.experience.set.levels.success.single": "Los niveles de experiencia de %2$s cambiaron a %1$s", "commands.experience.set.points.invalid": "No se pueden establecer puntos de experiencia por encima del máximo de puntos actuales", "commands.experience.set.points.success.multiple": "Los puntos de experiencia de %2$s jugadores cambiaron a %1$s", "commands.experience.set.points.success.single": "Los puntos de experiencia de %2$s cambiaron a %1$s", "commands.fill.failed": "No se rellenaron bloques", "commands.fill.success": "Bloque(s) rellenados con éxito: %s", "commands.fill.toobig": "Demasiados bloques en el área especificada (el máximo es %s y se especificaron %s)", "commands.fillbiome.success": "Biomas colocados entre %s, %s, %s y %s, %s, %s", "commands.fillbiome.success.count": "Hay %s entrada(s) de biomas entre %s, %s, %s y %s, %s, %s", "commands.fillbiome.toobig": "Demasiados bloques en el volumen especificado (el máximo es %s y se especificaron %s)", "commands.forceload.added.failure": "No se activó la carga forzosa en ningún chunk", "commands.forceload.added.multiple": "Carga forzosa activada para %s chunks en %s (de %s a %s)", "commands.forceload.added.none": "No hay chunks con carga forzosa en %s", "commands.forceload.added.single": "Carga forzosa activada para el chunk %s en %s", "commands.forceload.list.multiple": "Hay %s chunks con carga forzosa en %s: %s", "commands.forceload.list.single": "Hay 1 chunk con carga forzosa en %s: %s", "commands.forceload.query.failure": "La carga forzosa del chunk %s en %s está desactivada", "commands.forceload.query.success": "La carga forzosa del chunk %s en %s está activada", "commands.forceload.removed.all": "Carga forzosa desactivada para todos los chunks en %s", "commands.forceload.removed.failure": "Nada cambió: la carga forzosa no se desactivó de ningún chunk", "commands.forceload.removed.multiple": "Carga forzosa desactivada para %s chunks en %s (de %s a %)", "commands.forceload.removed.single": "Carga forzosa desactivada para el chunk %s en %s", "commands.forceload.toobig": "Hay demasiados chunks en el área especificada (el máximo es %s, se especificaron %s)", "commands.function.error.argument_not_compound": "Tipo de argumento invalido: %s, se esperaba Compound", "commands.function.error.missing_argument": "Falta de argumento %2$s en la funcion %1$s", "commands.function.error.missing_arguments": "Argumentos faltantes en la funcion %s", "commands.function.error.parse": "Al ejecutar la macro %s: el comando «%s» provocó el error: %s", "commands.function.instantiationFailure": "Error al realizar la función %s: %s", "commands.function.result": "La función %s ha devuelto %s", "commands.function.scheduled.multiple": "Funciones en ejecución %s", "commands.function.scheduled.no_functions": "No se encontraron funciones con el nombre %s", "commands.function.scheduled.single": "Función programada en ejecución %s", "commands.function.success.multiple": "%s comandos ejecutado(s) de %s funciones", "commands.function.success.multiple.result": "%s funciones ejecutadas", "commands.function.success.single": "%s comando(s) ejectutados de la funcion '%s'", "commands.function.success.single.result": "La función \"%2$s\" ha devuelto %1$s", "commands.gamemode.success.other": "Modo de juego de %s cambiado a %s", "commands.gamemode.success.self": "Modo de juego cambiado a %s", "commands.gamerule.query": "La regla %s está establecida como \"%s\"", "commands.gamerule.set": "La regla %s cambió a \"%s\"", "commands.give.failed.toomanyitems": "No se puede dar más de %s de %s", "commands.give.success.multiple": "%3$s jugadores recibieron %1$s de %2$s", "commands.give.success.single": "%3$s recibió %1$s de %2$s", "commands.help.failed": "Comando desconocido o permiso insuficiente", "commands.item.block.set.success": "Se ha reemplazado un espacio en %s, %s, %s con %s", "commands.item.entity.set.success.multiple": "Se ha reemplazado un espacio en %s entidades con %s", "commands.item.entity.set.success.single": "Se ha reemplazado un espacio en %s con %s", "commands.item.source.no_such_slot": "La fuente no tiene el espacio %s", "commands.item.source.not_a_container": "La ubicación inicial %s, %s, %s no es un contenedor", "commands.item.target.no_changed.known_item": "Ningún objetivo ha aceptado el objeto %s en el espacio %s", "commands.item.target.no_changes": "Ningún objetivo ha aceptado el objeto en el espacio %s", "commands.item.target.no_such_slot": "El objetivo no tiene el espacio %s", "commands.item.target.not_a_container": "La ubicación final %s, %s, %s no es un contenedor", "commands.jfr.dump.failed": "Error al copiar el registro de JFR: %s", "commands.jfr.start.failed": "Error al iniciar el perfilado JFR", "commands.jfr.started": "Iniciado el perfilado JFR", "commands.jfr.stopped": "Se ha detenido el perfilado JFR y se ha copiado a %s", "commands.kick.owner.failed": "No se puede expulsar al dueño de un juego LAN", "commands.kick.singleplayer.failed": "No se puede expulsar a un jugador en un mundo para un jugador sin conexión", "commands.kick.success": "Se expulsó a %s: %s", "commands.kill.success.multiple": "Destruidas %s entidades", "commands.kill.success.single": "Mataste a %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Hay %s de %s jugadores en línea: %s", "commands.locate.biome.not_found": "No se pudo encontrar un bioma \"%s\" a una distancia razonable", "commands.locate.biome.success": "El/la %s más cercano/a está en %s (a %s bloques de distancia)", "commands.locate.poi.not_found": "No se pudo encontrar el punto de interés \"%s\" a una distancia razonable", "commands.locate.poi.success": "El/la %s más cercano/a está en %s (a %s bloques de distancia)", "commands.locate.structure.invalid": "No hay una estructura con el tipo %s", "commands.locate.structure.not_found": "No se pudo encontrar una estructura de tipo %s cerca", "commands.locate.structure.success": "La estructura de tipo %s más cercana está en %s. (A %s bloques de distancia)", "commands.message.display.incoming": "%s te susurró: %s", "commands.message.display.outgoing": "Le susurraste a %s: %s", "commands.op.failed": "Nada cambió: el jugador ya era administrador", "commands.op.success": "Ahora %s es administrador(a) del servidor", "commands.pardon.failed": "Nada cambió: el jugador no estaba baneado", "commands.pardon.success": "Se desbaneó a %s", "commands.pardonip.failed": "Nada cambió: la IP no estaba baneada", "commands.pardonip.invalid": "IP no válida", "commands.pardonip.success": "Se desbaneó la IP %s", "commands.particle.failed": "La partícula no era visible para nadie", "commands.particle.success": "Mostrando la partícula %s", "commands.perf.alreadyRunning": "El perfilador de rendimiento ya se ha iniciado", "commands.perf.notRunning": "El perfilador de rendimiento no se ha iniciado", "commands.perf.reportFailed": "Error al crear el informe", "commands.perf.reportSaved": "Creado informe en %s", "commands.perf.started": "Análisis de rendimiento iniciado por 10 segundos (usa '/perf stop' para interrumpirlo)", "commands.perf.stopped": "Perfilado de rendimiento detenido despues de %s segundo(s) y %s ciclo(s) (%s ciclo(s) por segundo)", "commands.place.feature.failed": "Error al colocar la característica", "commands.place.feature.invalid": "No hay ninguna característica con el tipo %s", "commands.place.feature.success": "Se colocó %s en %s, %s, %s", "commands.place.jigsaw.failed": "Error al generar pieza", "commands.place.jigsaw.invalid": "El grupo de plantillas \"%s\" no existe", "commands.place.jigsaw.success": "Pieza generada en %s, %s, %s", "commands.place.structure.failed": "Error al colocar la estructura", "commands.place.structure.invalid": "No existe ninguna estructura de tipo \"%s\"", "commands.place.structure.success": "Estructura \"%s\" generada en %s, %s, %s", "commands.place.template.failed": "Error al colocar la plantilla", "commands.place.template.invalid": "No existe una plantilla con el ID \"%s\"", "commands.place.template.success": "Se ha cargado la plantilla \"%s\" en %s, %s, %s", "commands.playsound.failed": "El sonido está demasiado lejos para poder escucharse", "commands.playsound.success.multiple": "Sonido %s reproducido a %s jugadores", "commands.playsound.success.single": "Sonido %s reproducido a %s", "commands.publish.alreadyPublished": "La partida multijugador ya se aloja en el puerto %s", "commands.publish.failed": "No se pudo alojar la partida en LAN", "commands.publish.started": "Mundo en LAN alojado en el puerto %s", "commands.publish.success": "La partida multijugador se aloja en el puerto %s", "commands.random.error.range_too_large": "El rango del valor aleatorio debe ser como máximo, 2147483646", "commands.random.error.range_too_small": "El intervalo del valor aleatorio debe ser como mínimo, 2", "commands.random.reset.all.success": "Reiniciar %s secuencia(s) aleatoria(s)", "commands.random.reset.success": "Reiniciar secuencia aleatoria %s", "commands.random.roll": "%s ha sacado %s (entre %s y %s)", "commands.random.sample.success": "Valor aleatorio: %s", "commands.recipe.give.failed": "No se aprendieron nuevas recetas", "commands.recipe.give.success.multiple": "%s recetas desbloqueadas a %s jugadores", "commands.recipe.give.success.single": "%s recetas desbloqueadas para %s", "commands.recipe.take.failed": "No hay recetas olvidables", "commands.recipe.take.success.multiple": "%s recetas borradas a %s jugadores", "commands.recipe.take.success.single": "%s recetas borradas a %s", "commands.reload.failure": "<PERSON><PERSON>r al recargar, se mantienen los datos antiguos", "commands.reload.success": "¡Recargando!", "commands.ride.already_riding": "%s ya está montando %s", "commands.ride.dismount.success": "%s dej<PERSON> de <PERSON> %s", "commands.ride.mount.failure.cant_ride_players": "No se puede montar en otro jugador", "commands.ride.mount.failure.generic": "%s no pudo empezar a montar %s", "commands.ride.mount.failure.loop": "No puede montar la entidad sobre sí misma ni sobre ninguno de sus pasajeros", "commands.ride.mount.failure.wrong_dimension": "No se puede aplicar a una criatura que está en otra dimensión", "commands.ride.mount.success": "%s empezó a montar %s", "commands.ride.not_riding": "%s no conduce ningún vehículo", "commands.rotate.success": "Girar %s", "commands.save.alreadyOff": "El guardado ya estaba desactivado", "commands.save.alreadyOn": "El guardado ya estaba activado", "commands.save.disabled": "Autoguardado desactivado", "commands.save.enabled": "Autoguardado activado", "commands.save.failed": "Error al guardar la partida (¿hay suficiente espacio en el disco duro?)", "commands.save.saving": "Guardando partida... (¡un momento, por favor!)", "commands.save.success": "Partida guardada", "commands.schedule.cleared.failure": "No hay tareas programadas con ID %s", "commands.schedule.cleared.success": "%s programacion(es) removidas con id %s", "commands.schedule.created.function": "Funcion '%s' programada a %s ciclo(s) en tiempo de juego %s", "commands.schedule.created.tag": "Tag \"%s\" programado en %s ticks (hora de juego: %s)", "commands.schedule.macro": "No se puede programar una macro", "commands.schedule.same_tick": "No se puede programar para el tick actual", "commands.scoreboard.objectives.add.duplicate": "Ya existe un objetivo con este nombre", "commands.scoreboard.objectives.add.success": "Objetivo %s creado", "commands.scoreboard.objectives.display.alreadyEmpty": "Nada cambió: este espacio de muestra ya estaba vacío", "commands.scoreboard.objectives.display.alreadySet": "Nada cambió: este espacio de muestra ya estaba mostrando este objetivo", "commands.scoreboard.objectives.display.cleared": "Objetivos mostrados en la ranura %s quitados", "commands.scoreboard.objectives.display.set": "La ranura %s se estableció para mostrar el objetivo %s", "commands.scoreboard.objectives.list.empty": "No hay objetivos", "commands.scoreboard.objectives.list.success": "Hay %s objetivo(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Se ha desactivado la actualización automatica del objetivo %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Se ha activado la actualización automática del objetivo %s", "commands.scoreboard.objectives.modify.displayname": "Nombre a mostrar del objetivo %s cambiado a %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Se ha eliminado el formato numérico predefinido del objetivo %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Se ha cambiado el formato numérico predefinido del objetivo %s", "commands.scoreboard.objectives.modify.rendertype": "Tipo de renderizado del objetivo %s cambiado", "commands.scoreboard.objectives.remove.success": "Objetivo %s borrado", "commands.scoreboard.players.add.success.multiple": "%s puntos agregados al objetivo %s para %s entidades", "commands.scoreboard.players.add.success.single": "%s puntos agregados al objetivo %s para %s (ahora tiene %s puntos)", "commands.scoreboard.players.display.name.clear.success.multiple": "Se ha eliminado el nombre de %s entidades en %s", "commands.scoreboard.players.display.name.clear.success.single": "Se ha eliminado el nombre de %s en %s", "commands.scoreboard.players.display.name.set.success.multiple": "Se ha cambiado el nombre de %s para %s entidades en %s", "commands.scoreboard.players.display.name.set.success.single": "Se ha cambiado el nombre de %s a %s en %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Se ha eliminado el formato numérico de %s entidades en %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Se ha eliminado el formato numérico de %s en %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Se ha cambiado el formato numérico de %s entidades en %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Se ha cambiado el formato numérico de %s en %s", "commands.scoreboard.players.enable.failed": "Nada cambió: el trigger ya estaba activado", "commands.scoreboard.players.enable.invalid": "La función \"enable\" sólo sirve con objetivos de tipo \"trigger\"", "commands.scoreboard.players.enable.success.multiple": "Trigger %s activado para %s entidades", "commands.scoreboard.players.enable.success.single": "Se ha activado el trigger %s para %s", "commands.scoreboard.players.get.null": "No se pudo obtener el puntaje del objetivo %s para %s, no hay registro", "commands.scoreboard.players.get.success": "El puntaje del objetivo %3$s para %1$s es de %2$s", "commands.scoreboard.players.list.empty": "No hay entidades registradas", "commands.scoreboard.players.list.entity.empty": "%s no tiene puntajes para mostrar", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s tiene %s registro(s) de puntuación:", "commands.scoreboard.players.list.success": "Hay %s entidad(es) registrada(s): %s", "commands.scoreboard.players.operation.success.multiple": "Puntaje del objetivo %s actualizado a %s entidades", "commands.scoreboard.players.operation.success.single": "Se ha fijado %s para %s a %s", "commands.scoreboard.players.remove.success.multiple": "%s puntos borrados al objetivo %s para %s entidades", "commands.scoreboard.players.remove.success.single": "%s puntos borrados al objetivo %s para %s (ahora tiene %s)", "commands.scoreboard.players.reset.all.multiple": "Todos los puntajes de %s entidades fueron reiniciados", "commands.scoreboard.players.reset.all.single": "Todos los puntajes de %s fueron reiniciados", "commands.scoreboard.players.reset.specific.multiple": "Puntaje del objetivo %s reiniciado a %s entidades", "commands.scoreboard.players.reset.specific.single": "Puntaje del objetivo %s reiniciado a %s", "commands.scoreboard.players.set.success.multiple": "Puntaje del objetivo %1$s cambiado a %3$s para %2$s entidades", "commands.scoreboard.players.set.success.single": "Puntaje del objetivo %s de %s establecido en %s", "commands.seed.success": "Semilla: %s", "commands.setblock.failed": "No se pudo colocar el bloque", "commands.setblock.success": "El bloque en %s, %s, %s cambió", "commands.setidletimeout.success": "El tiempo limite de inactividad de los jugadores ahora es %s minuto(s)", "commands.setidletimeout.success.disabled": "Se desactivó el tiempo de inactividad de jugadores", "commands.setworldspawn.failure.not_overworld": "El punto de reaparición del mundo solo puede estar en la superficie", "commands.setworldspawn.success": "El punto de reaparición del mundo fue establecido en %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "El punto de reaparición de %6$s jugadores en la dimensión %5$s fue establecido en %1$s, %2$s, %3$s [%4$s]", "commands.spawnpoint.success.single": "El punto de reaparición de %6$s en la dimensión %5$s fue establecido en %1$s, %2$s, %3$s [%4$s]", "commands.spectate.not_spectator": "%s no está en modo espectador", "commands.spectate.self": "No puedes observarte a ti mismo", "commands.spectate.success.started": "El jugador observa la entidad %s", "commands.spectate.success.stopped": "El jugador dejó de observar una entidad", "commands.spreadplayers.failed.entities": "No se pudo propagar la entidad/entidades %s alrededor de %s, %s (demasiadas entidades para el espacio - intenta propagar no mas de %s)", "commands.spreadplayers.failed.invalid.height": "maxHeight %s no válido, se requiere un valor mayor al mínimo %s del mundo", "commands.spreadplayers.failed.teams": "No se pudo propagar %s equipo(s) alrededor de %s, %s (demasiadas entidades para el espacio - intenta propagar no mas de %s)", "commands.spreadplayers.success.entities": "Propagación de %s jugador(s) alrededor de %s, %s con una distancia promedio de %s bloques entre ellos", "commands.spreadplayers.success.teams": "Propagación de %s equipo(s) alrededor de %s, %s con una distancia promedio de %s bloques entre ellos", "commands.stop.stopping": "Deteniendo el servidor", "commands.stopsound.success.source.any": "Todos los sonidos \"%s\" se detuvieron", "commands.stopsound.success.source.sound": "El sonido \"%s\" de la fuente \"%s\" se detuvo", "commands.stopsound.success.sourceless.any": "Todos los sonidos se detuvieron", "commands.stopsound.success.sourceless.sound": "El sonido \"%s\" se detuvo", "commands.summon.failed": "No se pudo generar la entidad", "commands.summon.failed.uuid": "No se puede generar la entidad debido a UUID duplicados", "commands.summon.invalidPosition": "Posición no válida para invocar", "commands.summon.success": "Entidad %s generada", "commands.tag.add.failed": "El objetivo ya tenía la etiqueta o tiene demasiadas", "commands.tag.add.success.multiple": "Etiqueta \"%s\" agregada a %s entidades", "commands.tag.add.success.single": "Etiqueta \"%s\" agregada a %s", "commands.tag.list.multiple.empty": "Las %s entidades no tienen etiquetas", "commands.tag.list.multiple.success": "Las %s entidades tienen un total de %s etiquetas: %s", "commands.tag.list.single.empty": "%s no tiene etiquetas", "commands.tag.list.single.success": "%s tiene %s etiquetas: %s", "commands.tag.remove.failed": "El objetivo no tiene esta etiqueta", "commands.tag.remove.success.multiple": "Etiqueta \"%s\" borrada de %s entidades", "commands.tag.remove.success.single": "Etiqueta \"%s\" borrada de %s", "commands.team.add.duplicate": "Ya existe un equipo con este nombre", "commands.team.add.success": "Equipo %s creado", "commands.team.empty.success": "Se ha(n) eliminado miembro(s) del equipo", "commands.team.empty.unchanged": "Nada cambió: el equipo ya estaba vacío", "commands.team.join.success.multiple": "%s participantes agregados al equipo %s", "commands.team.join.success.single": "%s fue agregado/a al equipo %s", "commands.team.leave.success.multiple": "%s participantes borrados de sus equipos", "commands.team.leave.success.single": "%s fue borrado/a de su equipo", "commands.team.list.members.empty": "El equipo %s está vacío", "commands.team.list.members.success": "El equipo %s tiene %s miembro(s): %s", "commands.team.list.teams.empty": "No hay equipos", "commands.team.list.teams.success": "Hay %s equipo(s): %s", "commands.team.option.collisionRule.success": "La regla de colisión para el equipo %s cambió a \"%s\"", "commands.team.option.collisionRule.unchanged": "Nada cambió: la regla de colisión ya tenía este valor", "commands.team.option.color.success": "Color del equipo %s cambiado a %s", "commands.team.option.color.unchanged": "Nada cambió: el equipo ya tenía ese color", "commands.team.option.deathMessageVisibility.success": "La visibilidad de mensajes de muerte para el equipo %s cambió a \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Nada cambió: la visibilidad de avisos de muertes ya tenía este valor", "commands.team.option.friendlyfire.alreadyDisabled": "Nada cambió: el fuego amigo ya estaba desactivado en el equipo", "commands.team.option.friendlyfire.alreadyEnabled": "Nada cambió: el fuego amigo ya estaba activado en el equipo", "commands.team.option.friendlyfire.disabled": "Fuego amigo deshabilitado para el equipo %s", "commands.team.option.friendlyfire.enabled": "Fuego amigo habilitado para el equipo %s", "commands.team.option.name.success": "Nombre del equipo %s cambiado", "commands.team.option.name.unchanged": "Nada cambió: el equipo ya tenía ese nombre", "commands.team.option.nametagVisibility.success": "La visibilidad de nombres para los jugadores del equipo %s cambió a \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Nada cambió: la visibilidad de nombres ya tenía este valor", "commands.team.option.prefix.success": "Prefijo del equipo cambiado a %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Nada cambió: los miembros del equipo ya no podían ver a sus compañeros invisibles", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Nada cambió: los miembros del equipo ya podían ver a sus compañeros invisibles", "commands.team.option.seeFriendlyInvisibles.disabled": "El equipo %s ya no puede ver a los compañeros invisibles", "commands.team.option.seeFriendlyInvisibles.enabled": "El equipo %s puede ver ahora a los compañeros invisibles", "commands.team.option.suffix.success": "Sufijo del equipo cambiado a %s", "commands.team.remove.success": "Equipo %s borrado", "commands.teammsg.failed.noteam": "Para enviar un mensaje a tu equipo debes pertenecer a uno", "commands.teleport.invalidPosition": "Posición no válida para teletransportarse", "commands.teleport.success.entity.multiple": "%s entidades teletransportadas a %s", "commands.teleport.success.entity.single": "%s teletransportado hasta %s", "commands.teleport.success.location.multiple": "%s entidades teletransportadas a %s, %s, %s", "commands.teleport.success.location.single": "%s teletransportado a %s, %s, %s", "commands.test.batch.starting": "Iniciando entorno %s del lote %s", "commands.test.clear.error.no_tests": "No se pudo encontrar pruebas para eliminar", "commands.test.clear.success": "Se han eliminado %s estructuras", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Haz clic para copiar al portapapeles", "commands.test.create.success": "Configuración creada para la prueba %s", "commands.test.error.no_test_containing_pos": "No se pudo encontrar una instancia de prueba que contenga %s, %s, %s", "commands.test.error.no_test_instances": "No se encontraron instancias de prueba", "commands.test.error.non_existant_test": "No se pudo encontrar la prueba %s", "commands.test.error.structure_not_found": "No se pudo encontrar la estructura de prueba %s", "commands.test.error.test_instance_not_found": "No se pudo encontrar la entidad de bloque de instancia de prueba", "commands.test.error.test_instance_not_found.position": "No se pudo encontrar la entidad de bloque de instancia de prueba en %s, %s, %s", "commands.test.error.too_large": "El tamaño de la estructura debe ser menor que %s bloques en cada eje", "commands.test.locate.done": "Se terminó la búsqueda, se encontró %s estructura(s)", "commands.test.locate.found": "Se encontró una estructura en: %s (distancia: %s)", "commands.test.locate.started": "Se inició la búsqueda de estructuras de prueba, esto puede tardar un momento...", "commands.test.no_tests": "No hay pruebas que ejecutar", "commands.test.relative_position": "Posición relativa a %s: %s", "commands.test.reset.error.no_tests": "No se ha encontrado ninguna prueba para reiniciar", "commands.test.reset.success": "Se han reiniciado %s estructuras", "commands.test.run.no_tests": "No se encontró pruebas", "commands.test.run.running": "Realizando %s pruebas...", "commands.test.summary": "¡Se completó la prueba del juego! Se realizó %s prueba(s)", "commands.test.summary.all_required_passed": "Se superaron todas las pruebas requeridas :)", "commands.test.summary.failed": "Fallaron %s pruebas requeridas :(", "commands.test.summary.optional_failed": "Fallaron %s pruebas opcionales", "commands.tick.query.percentiles": "Percentiles: P50: %s ms, P95: %s ms, P99: %s ms; muestra: %s", "commands.tick.query.rate.running": "Tasa de ticks de destino: %s por segundo\nMedia de tiempo por tick: %sms (destino: %sms)", "commands.tick.query.rate.sprinting": "Tasa de ticks de destino: %s por segundo (ignorado, como referencia)\nMedia de tiempo por tick: %sms", "commands.tick.rate.success": "Se ha cambiado la tasa de ticks de destino a %s por segundo", "commands.tick.sprint.report": "Alta velocidad con %s ticks por segundo o %s ms por tick", "commands.tick.sprint.stop.fail": "No hay ticks a alta velocidad en curso", "commands.tick.sprint.stop.success": "Se ha interrumpido un tick a alta velocidad", "commands.tick.status.frozen": "El juego está congelado", "commands.tick.status.lagging": "El juego se está ejecutando, pero no puede mantenerse activo con la tasa de ticks de destino", "commands.tick.status.running": "El juego se está ejecutando a velocidad normal", "commands.tick.status.sprinting": "El juego está a alta velocidad", "commands.tick.step.fail": "Error al volver a la velocidad normal; el juego debe estar congelado primero", "commands.tick.step.stop.fail": "No hay ticks a velocidad normal en curso", "commands.tick.step.stop.success": "Se ha interrumpido un tick a velocidad normal", "commands.tick.step.success": "Regulando %s tick(s)", "commands.time.query": "Hora: %s ticks", "commands.time.set": "Tiempo ajustado a %s ticks", "commands.title.cleared.multiple": "Títulos de %s jugadores borrados", "commands.title.cleared.single": "Títulos de %s borrados", "commands.title.reset.multiple": "Opciones de título para %s jugadores reiniciadas", "commands.title.reset.single": "Opciones de título para %s reiniciadas", "commands.title.show.actionbar.multiple": "Mostrando nuevo título en barra de acción de %s jugadores", "commands.title.show.actionbar.single": "Mostrando nuevo título en la barra de acción de %s", "commands.title.show.subtitle.multiple": "Mostrando nuevo subtítulo para %s jugadores", "commands.title.show.subtitle.single": "Mostrando nuevo subtítulo para %s", "commands.title.show.title.multiple": "Mostrando nuevo título para %s jugadores", "commands.title.show.title.single": "Mostrando nuevo título para %s", "commands.title.times.multiple": "Tiempo de visualización del título de %s jugadores cambiado", "commands.title.times.single": "Tiempo de visualización del título de %s cambiado", "commands.transfer.error.no_players": "Debes especificar al menos un jugador para transferir", "commands.transfer.success.multiple": "Transfiriendo %s jugadores a %s:%s", "commands.transfer.success.single": "Transfiriendo a %s hacia %s:%s", "commands.trigger.add.success": "Trigger %s activado (se agregó %s al valor)", "commands.trigger.failed.invalid": "<PERSON><PERSON><PERSON> puedes activar objetivos de tipo \"trigger\"", "commands.trigger.failed.unprimed": "Aún no puedes activar este objetivo trigger", "commands.trigger.set.success": "Trigger %s activado (se estableció el valor en %s)", "commands.trigger.simple.success": "Trigger %s activado", "commands.version.build_time": "Tiempo de compilación = %s", "commands.version.data": "datos = %s", "commands.version.header": "Información de la versión del servidor:", "commands.version.id": "iD: %s", "commands.version.name": "nombre = %s", "commands.version.pack.data": "paquete_dato = %s", "commands.version.pack.resource": "paquete_recurso = %s", "commands.version.protocol": "protocolo = %s ( %s)", "commands.version.series": "serie = %s", "commands.version.stable.no": "estable = no", "commands.version.stable.yes": "estable = sí", "commands.waypoint.list.empty": "No hay puntos de referencia en %s", "commands.waypoint.list.success": "%s punto(s) de referencia en %s: %s", "commands.waypoint.modify.color": "El color del punto de ruta ahora es %s", "commands.waypoint.modify.color.reset": "Restablecer el color del punto de ruta", "commands.waypoint.modify.style": "Se ha cambiado el estilo del punto de referencia", "commands.weather.set.clear": "Cambiando el tiempo a despejado...", "commands.weather.set.rain": "Cambiando el tiempo a lluvia...", "commands.weather.set.thunder": "Cambiando el tiempo a tormenta...", "commands.whitelist.add.failed": "El jugador ya estaba en la lista blanca", "commands.whitelist.add.success": "Se ha añadido a %s a la lista blanca", "commands.whitelist.alreadyOff": "La lista blanca ya estaba desactivada", "commands.whitelist.alreadyOn": "La lista blanca ya estaba activada", "commands.whitelist.disabled": "Lista blanca desactivada", "commands.whitelist.enabled": "Lista blanca activada", "commands.whitelist.list": "Hay %s jugador(es) en la lista blanca: %s", "commands.whitelist.none": "La lista blanca está vacía", "commands.whitelist.reloaded": "Lista blanca recargada", "commands.whitelist.remove.failed": "El jugador no estaba en la lista blanca", "commands.whitelist.remove.success": "Se ha borrado a %s de la lista blanca", "commands.worldborder.center.failed": "Nada cambió: el centro del borde del mundo ya estaba en esa posición", "commands.worldborder.center.success": "Poniendo el centro del borde del mundo en %s, %s", "commands.worldborder.damage.amount.failed": "Nada cambió: el borde del mundo ya inflige esta cantidad de daño", "commands.worldborder.damage.amount.success": "Daño del borde del mundo establecido a %s por bloque cada segundo", "commands.worldborder.damage.buffer.failed": "Nada cambió: la zona segura fuera del borde del mundo ya estaba a esta distancia", "commands.worldborder.damage.buffer.success": "Cambiada la zona segura fuera del borde del mundo a %s bloque(s)", "commands.worldborder.get": "El borde del mundo actualmente tiene un tamaño de %s bloque(s)", "commands.worldborder.set.failed.big": "El borde del mundo no puedes ser mas grande que %s bloques amplio", "commands.worldborder.set.failed.far": "El borde del mundo no puede llegar más allá de %s bloques", "commands.worldborder.set.failed.nochange": "Nada cambió: el borde del mundo ya tenía este tamaño", "commands.worldborder.set.failed.small": "El borde del mundo no puede tener una extensión inferior a 1 bloque", "commands.worldborder.set.grow": "Aumentando el borde del mundo en %s bloques durante %s segundos", "commands.worldborder.set.immediate": "Cambiado el borde del mundo a tamaño de %s bloque(s)", "commands.worldborder.set.shrink": "Reduciendo el borde del mundo a %s bloque(s) durante %s segundo(s)", "commands.worldborder.warning.distance.failed": "Nada cambió: el aviso del borde del mundo ya tenía esta distancia", "commands.worldborder.warning.distance.success": "Cambiada la distancia de advertencia del borde del mundo a %s bloque(s)", "commands.worldborder.warning.time.failed": "Nada cambió: el aviso del borde del mundo ya duraba esta cantidad de tiempo", "commands.worldborder.warning.time.success": "Cambiado el tiempo de advertencia del borde del mundo a %s segundo(s)", "compliance.playtime.greaterThan24Hours": "Has estado jugando más de 24 horas", "compliance.playtime.hours": "Llevas jugando %s hora(s)", "compliance.playtime.message": "<PERSON><PERSON> demasiado tiempo podría interferir con tu vida diaria", "connect.aborted": "Conexión cancelada", "connect.authorizing": "Iniciando se<PERSON>...", "connect.connecting": "Conectando con el servidor...", "connect.encrypting": "Cifrando...", "connect.failed": "No se pudo conectar al servidor", "connect.failed.transfer": "Error de conexión al transferir al servidor", "connect.joining": "Entrando al mundo...", "connect.negotiating": "Conectando...", "connect.reconfiging": "Reconfigurando...", "connect.reconfiguring": "Reconfigurando...", "connect.transferring": "Transfiriendo al nuevo servidor...", "container.barrel": "Barril", "container.beacon": "Faro", "container.beehive.bees": "Abejas: %s/%s", "container.beehive.honey": "Miel: %s/%s", "container.blast_furnace": "Alto horno", "container.brewing": "Soporte para pociones", "container.cartography_table": "Mesa de cartografía", "container.chest": "Baúl", "container.chestDouble": "Baúl grande", "container.crafter": "Fabricador", "container.crafting": "Fabricación", "container.creative": "Selección de objetos", "container.dispenser": "Dispensador", "container.dropper": "Soltador", "container.enchant": "Encantar", "container.enchant.clue": "¿ %s . . . ?", "container.enchant.lapis.many": "%s de lapislázuli", "container.enchant.lapis.one": "1 de lapislázuli", "container.enchant.level.many": "%s niveles de experiencia", "container.enchant.level.one": "1 nivel de experiencia", "container.enchant.level.requirement": "Nivel necesario: %s", "container.enderchest": "<PERSON><PERSON>", "container.furnace": "<PERSON><PERSON>", "container.grindstone_title": "Reparar y desencantar", "container.hopper": "<PERSON><PERSON><PERSON>", "container.inventory": "Inventario", "container.isLocked": "¡El objeto está bloqueado!", "container.lectern": "Atril", "container.loom": "Telar", "container.repair": "Reparar y renombrar", "container.repair.cost": "Costo: %1$s", "container.repair.expensive": "¡Demasiado caro!", "container.shulkerBox": "<PERSON><PERSON> de <PERSON>ker", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "y %s más...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "No se puede abrir. El contenido no se ha generado aún.", "container.stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.upgrade": "<PERSON><PERSON>rar equipamiento", "container.upgrade.error_tooltip": "Este objeto no puede mejorarse de esta manera", "container.upgrade.missing_template_tooltip": "Añade un molde de herrería", "controls.keybinds": "Atajos de teclado...", "controls.keybinds.duplicateKeybinds": "Esta tecla también se usa para:\n%s", "controls.keybinds.title": "Atajos de teclado", "controls.reset": "Reiniciar", "controls.resetAll": "Reiniciar teclas", "controls.title": "Controles", "createWorld.customize.buffet.biome": "Selecciona el bioma del mundo", "createWorld.customize.buffet.title": "Personalización del mundo bufet", "createWorld.customize.flat.height": "Altura", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "(Fondo)  %s", "createWorld.customize.flat.layer.top": "(Superficie)  %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON> capa", "createWorld.customize.flat.tile": "Material de la capa", "createWorld.customize.flat.title": "Personalización del mundo plano", "createWorld.customize.presets": "Plantillas", "createWorld.customize.presets.list": "Alternativamente, ¡aquí hay unas que hicimos antes!", "createWorld.customize.presets.select": "Usar plantilla", "createWorld.customize.presets.share": "¿Quieres compartir tu plantilla? ¡Usa el recuadro de abajo!", "createWorld.customize.presets.title": "Selecciona una plantilla", "createWorld.preparing": "Preparando la generación del mundo...", "createWorld.tab.game.title": "Ju<PERSON>", "createWorld.tab.more.title": "Más", "createWorld.tab.world.title": "Mundo", "credits_and_attribution.button.attribution": "Atribución", "credits_and_attribution.button.credits": "C<PERSON>dit<PERSON>", "credits_and_attribution.button.licenses": "Licencias", "credits_and_attribution.screen.title": "Creditos y atribuciones", "dataPack.bundle.description": "Activa la bolsa como objeto experimental", "dataPack.bundle.name": "Bolsas", "dataPack.locator_bar.description": "Mostrar la dirección de otros jugadores en multijugador", "dataPack.locator_bar.name": "Barra de localización", "dataPack.minecart_improvements.description": "Movimiento mejorado de Vagones", "dataPack.minecart_improvements.name": "Mejoras de vagones", "dataPack.redstone_experiments.description": "Cambios experimentales del redstone", "dataPack.redstone_experiments.name": "Experimentos de redstone", "dataPack.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> de <PERSON>", "dataPack.trade_rebalance.description": "Actualización de los comercios con aldeanos", "dataPack.trade_rebalance.name": "Rebalance del comercio con aldeanos", "dataPack.update_1_20.description": "Nuevas características y contenido para Minecraft 1.20", "dataPack.update_1_20.name": "Actualización 1.20", "dataPack.update_1_21.description": "Nuevas características y contenido para Minecraft 1.21", "dataPack.update_1_21.name": "Actualización 1.21", "dataPack.validation.back": "Volver", "dataPack.validation.failed": "¡Error al validar paquetes de datos!", "dataPack.validation.reset": "Restablecer por defecto", "dataPack.validation.working": "Validando paquetes de datos seleccionados...", "dataPack.vanilla.description": "Los datos por defecto de Minecraft", "dataPack.vanilla.name": "Por defecto", "dataPack.winter_drop.description": "Nuevas características y contenido para el lanzamiento de invierno", "dataPack.winter_drop.name": "drop de Invierno", "datapackFailure.safeMode": "<PERSON><PERSON> se<PERSON>", "datapackFailure.safeMode.failed.description": "Este mundo contiene datos de guardado no válidos o corruptos.", "datapackFailure.safeMode.failed.title": "Error al cargar el mundo en modo seguro.", "datapackFailure.title": "No se ha podido cargar el mundo porque hay errores en los paquetes de datos seleccionados.\nPuedes intentar cargarlo únicamente con el paquete de datos \"vanilla\" (\"modo seguro\") o volver al menú principal y buscar una solución manualmente.", "death.attack.anvil": "%1$s fue aplastado por un yunque", "death.attack.anvil.player": "%1$s fue aplastado(a) por un yunque mientras peleaba contra %2$s", "death.attack.arrow": "%1$s fue disparado por %2$s", "death.attack.arrow.item": "%1$s fue disparado por %2$s usando su %3$s", "death.attack.badRespawnPoint.link": "Diseño intencional del juego", "death.attack.badRespawnPoint.message": "%1$s fue víctima de %2$s", "death.attack.cactus": "%1$s se pinchó hasta morir", "death.attack.cactus.player": "%1$s se quedó pegado a un cactus mientras trataba de escapar de %2$s", "death.attack.cramming": "%1$s fue aplastado con demasiada fuerza", "death.attack.cramming.player": "%1$s fue aplastado/a por %2$s", "death.attack.dragonBreath": "%1$s fue calcinado en aliento de dragón", "death.attack.dragonBreath.player": "%1$s fue calcinado en aliento de dragón por %2$s", "death.attack.drown": "%1$s se ahogó", "death.attack.drown.player": "%1$s se ahogó mientras trataba de escapar de %2$s", "death.attack.dryout": "%1$s ha muerto por deshidratación", "death.attack.dryout.player": "%1$s ha muerto por deshidratación mientras intentaba huir de %2$s", "death.attack.even_more_magic": "%1$s murió por una gran magia", "death.attack.explosion": "%1$s explotó", "death.attack.explosion.player": "%2$s hizo explotar a %1$s", "death.attack.explosion.player.item": "%2$s mandó a volar a %1$s con su %3$s", "death.attack.fall": "%1$s se golpeó contra el suelo demasiado fuerte", "death.attack.fall.player": "%1$s se chocó contra el suelo mientras trataba de escapar de %2$s", "death.attack.fallingBlock": "%1$s fue aplastado por un bloque", "death.attack.fallingBlock.player": "A %1$s le ha caído un bloque mientras luchaba contra %2$s", "death.attack.fallingStalactite": "%1$s fue aplastado por una estalactita", "death.attack.fallingStalactite.player": "%1$s fue aplastado por una estalactita mientras peleaba con %2$s", "death.attack.fireball": "%1$s fue alcanzado por una bola de fuego de %2$s", "death.attack.fireball.item": "%1$s fue quemado por una bola de fuego de %2$s usando su %3$s", "death.attack.fireworks": "%1$s voló con fuegos artificiales", "death.attack.fireworks.item": "%1$s explotó con un fuego artificial disparado desde %3$s por %2$s", "death.attack.fireworks.player": "%1$s explotó con fuegos artificiales mientras luchaba contra %2$s", "death.attack.flyIntoWall": "%1$s experimentó la energía cinética", "death.attack.flyIntoWall.player": "%1$s experimentó la energía cinética mientras trataba de escapar de %2$s", "death.attack.freeze": "%1$s se congeló hasta morir", "death.attack.freeze.player": "%1$s fue congelado hasta la muerte por %2$s", "death.attack.generic": "%1$s murió", "death.attack.generic.player": "%1$s fue asesinado/a por %2$s", "death.attack.genericKill": "%1$s ha muerto", "death.attack.genericKill.player": "%1$s ha muerto mientras luchaba contra %2$s", "death.attack.hotFloor": "%1$s descubrió que el suelo era lava", "death.attack.hotFloor.player": "%1$s ha pisado una zona de peligro debido a %2$s", "death.attack.inFire": "%1$s ardió en llamas", "death.attack.inFire.player": "%1$s caminó en fuego mientras luchaba contra %2$s", "death.attack.inWall": "%1$s se asfixió en una pared", "death.attack.inWall.player": "%1$s se asfixió en una pared mientras luchaba contra %2$s", "death.attack.indirectMagic": "%1$s fue asesinado por %2$s usando magia", "death.attack.indirectMagic.item": "%1$s fue asesinado por %2$s usando su %3$s", "death.attack.lava": "%1$s intentó nadar en lava", "death.attack.lava.player": "%1$s intentó nadar en lava para escapar de %2$s", "death.attack.lightningBolt": "%1$s fue golpeado por un rayo", "death.attack.lightningBolt.player": "%1$s fue alcanzado/a por un rayo mientras luchaba contra %2$s", "death.attack.mace_smash": "%1$s fue aplastado/a por %2$s", "death.attack.mace_smash.item": "%1$s fue aplastado por %2$s usando su %3$s", "death.attack.magic": "%1$s fue asesinado por arte de magia", "death.attack.magic.player": "%1$s murió por arte de magia mientras trataba de escapar de de %2$s", "death.attack.message_too_long": "Lamentablemente, el mensaje era demasiado largo para ser enviado. ¡Lo sentimos! Aquí tienes la versión abreviada: %s", "death.attack.mob": "%1$s fue asesinado por un(a) %2$s", "death.attack.mob.item": "%1$s fue asesinado por %2$s usando su %3$s", "death.attack.onFire": "%1$s se quemó hasta la muerte", "death.attack.onFire.item": "%1$s se quemó mientras luchaba contra %2$s empuñando %3$s", "death.attack.onFire.player": "%1$s se quemó hasta la muerte mientras luchaba contra %2$s", "death.attack.outOfWorld": "%1$s se cayó del mundo", "death.attack.outOfWorld.player": "%1$s no volverá a vivir en el mismo mundo que %2$s", "death.attack.outsideBorder": "%1$s ha abandonado los confines de este mundo", "death.attack.outsideBorder.player": "%1$s abandonó los confines del mundo mientras luchaba contra %2$s", "death.attack.player": "%1$s fue asesinado por %2$s", "death.attack.player.item": "%1$s fue asesinado por %2$s utilizando %3$s", "death.attack.sonic_boom": "%1$s fue impactado/a por un rugido supersónico", "death.attack.sonic_boom.item": "%1$s ha sido aniquilado por un chillido sónico mientras intentaba huir de %2$s equipado con %3$s", "death.attack.sonic_boom.player": "%1$s fue impactado/a por un rugido supersónico tratando de escapar de %2$s", "death.attack.stalagmite": "%1$s fue empalado por una estalagmita", "death.attack.stalagmite.player": "%1$s fue empalado por una estalagmita mientras luchaba contra %2$s", "death.attack.starve": "%1$s se murió de hambre", "death.attack.starve.player": "%1$s se murió de hambre mientras luchaba contra %2$s", "death.attack.sting": "%1$s fue picado/a hasta la muerte", "death.attack.sting.item": "%1$s ha muerto por una picadura de %2$s usando %3$s", "death.attack.sting.player": "%1$s fue picado/a hasta la muerte por %2$s", "death.attack.sweetBerryBush": "%1$s se pinchó con un arbusto de bayas dulces hasta morir", "death.attack.sweetBerryBush.player": "%1$s se pinchó con un arbusto de bayas dulces hasta morir mientras trataba de escapar de %2$s", "death.attack.thorns": "%1$s ha muerto mientras intentaba atacar a %2$s", "death.attack.thorns.item": "%1$s fue asesinado/a por %3$s cuando intentaba golpear a %2$s", "death.attack.thrown": "%1$s fue golpeado por %2$s", "death.attack.thrown.item": "%1$s fue golpeado por %2$s usando su %3$s", "death.attack.trident": "%1$s fue empalado/a por %2$s", "death.attack.trident.item": "%1$s fue empalado/a por %2$s con su %3$s", "death.attack.wither": "%1$s murió descompuesto", "death.attack.wither.player": "%1$s se descompuso mientras luchaba contra %2$s", "death.attack.witherSkull": "%1$s fue disparado/a por un cráneo de %2$s", "death.attack.witherSkull.item": "%1$s ha sido golpeado por un cráneo de %2$s usando %3$s", "death.fell.accident.generic": "%1$s cayó desde un lugar alto", "death.fell.accident.ladder": "%1$s se cayó de una escalera", "death.fell.accident.other_climbable": "%1$s se cayó mientras escalaba", "death.fell.accident.scaffolding": "%1$s se cayó de un andamio", "death.fell.accident.twisting_vines": "%1$s se cayó de unas enredaderas retorcidas", "death.fell.accident.vines": "%1$s se cayó de unas enredaderas", "death.fell.accident.weeping_vines": "%1$s se cayó de unas enredaderas lloronas", "death.fell.assist": "%1$s fue condenado a caer desde muy alto por %2$s", "death.fell.assist.item": "%1$s fue condenado a caer por %2$s usando su %3$s", "death.fell.finish": "%1$s cayó desde muy alto y fue asesinado por %2$s", "death.fell.finish.item": "%1$s cayó desde muy alto y fue rematado por %2$s usando su %3$s", "death.fell.killer": "%1$s fue condenado a caer desde muy alto", "deathScreen.quit.confirm": "¿Estás seguro de que quieres salir?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON>", "deathScreen.score": "Puntuación", "deathScreen.score.value": "Puntuación: %s", "deathScreen.spectate": "Observar mundo", "deathScreen.title": "¡Has muerto!", "deathScreen.title.hardcore": "Game Over", "deathScreen.titleScreen": "<PERSON><PERSON> principal", "debug.advanced_tooltips.help": "F3 + H = Descripción avanzada de ítems", "debug.advanced_tooltips.off": "Descripción avanzada de ítems: no", "debug.advanced_tooltips.on": "Descripción avanzada de ítems: sí", "debug.chunk_boundaries.help": "F3 + G = Mostrar bordes de chunks", "debug.chunk_boundaries.off": "Bordes de chunks: no", "debug.chunk_boundaries.on": "Bordes de chunks: sí", "debug.clear_chat.help": "F3 + D = Limpiar chat", "debug.copy_location.help": "F3 + C = Copiar la ubicación como comando /tp. Mantenlas para forzar el cierre del juego", "debug.copy_location.message": "Ubicación copiada en el portapapeles", "debug.crash.message": "Estás presionando F3 + C. Si no las sueltas, el juego dejará de funcionar.", "debug.crash.warning": "El juego dejará de funcionar en %s...", "debug.creative_spectator.error": "No tienes permiso para cambiar el modo de juego", "debug.creative_spectator.help": "F3 + N = Cambiar entre el modo de juego previo <-> espectador", "debug.dump_dynamic_textures": "Texturas dinámicas guardadas en %s", "debug.dump_dynamic_textures.help": "F3 + S = Volcar texturas dinámicas", "debug.gamemodes.error": "No se puede abrir el menú de modos de juego, no tienes permisos", "debug.gamemodes.help": "F3 + F4 = <PERSON>brir menú de modos de juego", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s siguiente", "debug.help.help": "F3 + Q = Mostrar esta lista", "debug.help.message": "Teclas:", "debug.inspect.client.block": "Se copiaron los datos del bloque (lado del cliente)", "debug.inspect.client.entity": "Se copiaron los datos de la entidad (lado del cliente)", "debug.inspect.help": "F3 + I = Copiar datos de entidad o bloque al portapapeles", "debug.inspect.server.block": "Se copiaron los datos del bloque (lado del servidor)", "debug.inspect.server.entity": "Se copiaron los datos de la entidad (lado del servidor)", "debug.pause.help": "F3+Esc = Pausa la partida sin el menú del juego (s<PERSON>lo si es posible)", "debug.pause_focus.help": "F3 + P = Pausar juego al cambiar de ventana", "debug.pause_focus.off": "Pausar juego al cambiar de ventana: no", "debug.pause_focus.on": "Pausar juego al cambiar de ventana: sí", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Iniciar/detener análisis debug", "debug.profiling.start": "Análisis iniciado por %s segundos. Usa F3 + L para interrumpirlo", "debug.profiling.stop": "Perfil a terminado. Resultados guardados en %s", "debug.reload_chunks.help": "F3 + A = Recargar chunks", "debug.reload_chunks.message": "Recargando todos los chunks", "debug.reload_resourcepacks.help": "F3 + T = <PERSON><PERSON><PERSON> paquetes de recursos", "debug.reload_resourcepacks.message": "Paquetes de recursos recargados", "debug.show_hitboxes.help": "F3 + B = Mostrar hitbox", "debug.show_hitboxes.off": "Mostrar hitbox: no", "debug.show_hitboxes.on": "Mostrar hitbox: sí", "debug.version.header": "Información de la versión del cliente:", "debug.version.help": "F3 + V = Información de la versión del cliente.", "demo.day.1": "Esta demo durará solo 5 días de juego. ¡Aprovéchala al máximo!", "demo.day.2": "<PERSON><PERSON><PERSON> d<PERSON>", "demo.day.3": "<PERSON><PERSON><PERSON> d<PERSON>", "demo.day.4": "Cuarto día", "demo.day.5": "¡Este es tu último día!", "demo.day.6": "El quinto día de juego ha terminado. Usa %s para guardar una captura de tu creación.", "demo.day.warning": "¡Tu tiempo ya casi se termina!", "demo.demoExpired": "¡El tiempo de demostración ha terminado!", "demo.help.buy": "¡<PERSON>mprar ahora!", "demo.help.fullWrapped": "Esta demostración durará 5 días del juego (1 hora y 40 minutos reales). ¡Fíjate en los progresos para guiarte! ¡Diviértete!", "demo.help.inventory": "Usa %1$s para abrir tu inventario", "demo.help.jump": "Presiona %1$s para saltar", "demo.help.later": "¡Continuar jugando!", "demo.help.movement": "Usa %1$s,%2$s, %3$s, %4$s para moverte y el mouse para mirar", "demo.help.movementMouse": "Mira a tu alrededor usando el mouse", "demo.help.movementShort": "Presiona %1$s, %2$s, %3$s o %4$s para moverte", "demo.help.title": "Demostración de Minecraft", "demo.remainingTime": "Tiempo restante: %s", "demo.reminder": "¡La demostración se ha acabado, compra el juego para continuar o crea un mundo nuevo!", "difficulty.lock.question": "¿Quieres bloquear la dificultad del mundo en %1$s? Piénsalo bien, una vez que selecciones \"Sí\" no podrás cambiarla nunca más.", "difficulty.lock.title": "Bloquear la dificultad del mundo", "disconnect.endOfStream": "Fin de la conexión", "disconnect.exceeded_packet_rate": "Expulsado/a por superar el límite de paquetes", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignorando solicitud de estado", "disconnect.loginFailedInfo": "Error al iniciar sesión: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "El modo multijugador está desactivado. Por favor, comprueba la configuración de tu cuenta de Microsoft.", "disconnect.loginFailedInfo.invalidSession": "La sesión no es válida (prueba a reiniciar el juego y el launcher)", "disconnect.loginFailedInfo.serversUnavailable": "No se tiene acceso a los servidores de autenticación. Por favor, inténtalo de nuevo.", "disconnect.loginFailedInfo.userBanned": "Se te ha prohibido jugar en línea", "disconnect.lost": "Conexión perdida", "disconnect.packetError": "Error de protocolo de red", "disconnect.spam": "Expulsión por hacer spam", "disconnect.timeout": "Tiempo de espera agotado", "disconnect.transfer": "Transferido a otro servidor", "disconnect.unknownHost": "Anfitrión desconocido", "download.pack.failed": "No se han podido descargar %s de %s paquetes", "download.pack.progress.bytes": "Progreso: %s (tamaño total desconocido)", "download.pack.progress.percent": "Progreso: %s %%", "download.pack.title": "Descargando paquete de recursos %s/%s", "editGamerule.default": "Por defecto: %s", "editGamerule.title": "Editar las reglas del juego", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorción", "effect.minecraft.bad_omen": "Mal presagio", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Canalización", "effect.minecraft.darkness": "Oscuridad", "effect.minecraft.dolphins_grace": "<PERSON><PERSON><PERSON> del<PERSON>a", "effect.minecraft.fire_resistance": "Resis. al fuego", "effect.minecraft.glowing": "Luminiscencia", "effect.minecraft.haste": "<PERSON><PERSON><PERSON> minera", "effect.minecraft.health_boost": "<PERSON><PERSON> mejorada", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON><PERSON> aldea", "effect.minecraft.hunger": "Hambre", "effect.minecraft.infested": "Infestación", "effect.minecraft.instant_damage": "<PERSON><PERSON>", "effect.minecraft.instant_health": "<PERSON><PERSON>", "effect.minecraft.invisibility": "Invisibilidad", "effect.minecraft.jump_boost": "Supersalto", "effect.minecraft.levitation": "Levitación", "effect.minecraft.luck": "<PERSON><PERSON>", "effect.minecraft.mining_fatigue": "Fatiga al minar", "effect.minecraft.nausea": "Náuseas", "effect.minecraft.night_vision": "Visión nocturna", "effect.minecraft.oozing": "<PERSON><PERSON><PERSON>", "effect.minecraft.poison": "Veneno", "effect.minecraft.raid_omen": "Presagio de invasión", "effect.minecraft.regeneration": "Regeneración", "effect.minecraft.resistance": "Resistencia", "effect.minecraft.saturation": "Saturación", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON><PERSON> lenta", "effect.minecraft.slowness": "Lentitud", "effect.minecraft.speed": "Velocidad", "effect.minecraft.strength": "Fuerza", "effect.minecraft.trial_omen": "Presagio de Prueba", "effect.minecraft.unluck": "<PERSON><PERSON> suerte", "effect.minecraft.water_breathing": "Respiración", "effect.minecraft.weakness": "Debilidad", "effect.minecraft.weaving": "Costura", "effect.minecraft.wind_charged": "<PERSON>ient<PERSON>", "effect.minecraft.wither": "<PERSON>er", "effect.none": "Sin efectos", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Afinidad acuática", "enchantment.minecraft.bane_of_arthropods": "Pesadilla de los artrópodos", "enchantment.minecraft.binding_curse": "Maldición de ligamiento", "enchantment.minecraft.blast_protection": "Protección contra explosiones", "enchantment.minecraft.breach": "Abertura", "enchantment.minecraft.channeling": "Conductividad", "enchantment.minecraft.density": "Densidad", "enchantment.minecraft.depth_strider": "Agilidad acuática", "enchantment.minecraft.efficiency": "Eficiencia", "enchantment.minecraft.feather_falling": "Caída de pluma", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.fire_protection": "Protección contra el fuego", "enchantment.minecraft.flame": "Fuego", "enchantment.minecraft.fortune": "Fortuna", "enchantment.minecraft.frost_walker": "Paso helado", "enchantment.minecraft.impaling": "Empalamiento", "enchantment.minecraft.infinity": "Infinidad", "enchantment.minecraft.knockback": "Empuje", "enchantment.minecraft.looting": "Botín", "enchantment.minecraft.loyalty": "Lealtad", "enchantment.minecraft.luck_of_the_sea": "Suerte marina", "enchantment.minecraft.lure": "Atracción", "enchantment.minecraft.mending": "Reparación", "enchantment.minecraft.multishot": "Multidisparo", "enchantment.minecraft.piercing": "Perforación", "enchantment.minecraft.power": "<PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "Protección contra proyectiles", "enchantment.minecraft.protection": "Protección", "enchantment.minecraft.punch": "Retroceso", "enchantment.minecraft.quick_charge": "Carga rápida", "enchantment.minecraft.respiration": "Respiración", "enchantment.minecraft.riptide": "Propulsión acuática", "enchantment.minecraft.sharpness": "<PERSON><PERSON>", "enchantment.minecraft.silk_touch": "Toque de seda", "enchantment.minecraft.smite": "Castigo", "enchantment.minecraft.soul_speed": "Velocidad de alma", "enchantment.minecraft.sweeping": "Barrido", "enchantment.minecraft.sweeping_edge": "Barrido", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON>lo veloz", "enchantment.minecraft.thorns": "Espinas", "enchantment.minecraft.unbreaking": "Irrompibilidad", "enchantment.minecraft.vanishing_curse": "Maldición de desaparición", "enchantment.minecraft.wind_burst": "Impulso aéreo", "entity.minecraft.acacia_boat": "Barco de acacia", "entity.minecraft.acacia_chest_boat": "Barco de acacia con cofre", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Nube de efecto persistente", "entity.minecraft.armadillo": "Cachicamo", "entity.minecraft.armor_stand": "Soporte para armadura", "entity.minecraft.arrow": "Fle<PERSON>", "entity.minecraft.axolotl": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bamboo_chest_raft": "Bote de Bambú con Cofre", "entity.minecraft.bamboo_raft": "Balsa de Bambú", "entity.minecraft.bat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "<PERSON><PERSON>", "entity.minecraft.birch_chest_boat": "<PERSON><PERSON> de Abedul con Cofre", "entity.minecraft.blaze": "Blaze", "entity.minecraft.block_display": "Bloque holográfico", "entity.minecraft.boat": "<PERSON><PERSON>", "entity.minecraft.bogged": "Esqueleto pantanoso", "entity.minecraft.breeze": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Carga de Brisa", "entity.minecraft.camel": "<PERSON>llo", "entity.minecraft.cat": "Gato", "entity.minecraft.cave_spider": "<PERSON><PERSON>", "entity.minecraft.cherry_boat": "<PERSON><PERSON> c<PERSON>", "entity.minecraft.cherry_chest_boat": "Bote de cerezo con baúl", "entity.minecraft.chest_boat": "<PERSON><PERSON> con Cofre", "entity.minecraft.chest_minecart": "Vagón con baúl", "entity.minecraft.chicken": "<PERSON><PERSON><PERSON>", "entity.minecraft.cod": "Bacalao", "entity.minecraft.command_block_minecart": "Vagón con bloque de comandos", "entity.minecraft.cow": "Vaca", "entity.minecraft.creaking": "Tronador", "entity.minecraft.creaking_transient": "Tronador", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Bote de madera oscura", "entity.minecraft.dark_oak_chest_boat": "Bote de roble oscuro con baúl", "entity.minecraft.dolphin": "Delfín", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Bola de fuego del dragón", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON>", "entity.minecraft.egg": "<PERSON><PERSON> lanza<PERSON>", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON>", "entity.minecraft.end_crystal": "Cristal del End", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON> de ender la<PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Invocador", "entity.minecraft.evoker_fangs": "Colmillos de invocador", "entity.minecraft.experience_bottle": "Frasco de experiencia lanzado", "entity.minecraft.experience_orb": "Orbe de experiencia", "entity.minecraft.eye_of_ender": "<PERSON><PERSON> de ender", "entity.minecraft.falling_block": "Bloque cayente", "entity.minecraft.falling_block_type": "En caída %s", "entity.minecraft.fireball": "Bola de fuego", "entity.minecraft.firework_rocket": "Cohete de fuegos artificiales", "entity.minecraft.fishing_bobber": "Boya de pesca", "entity.minecraft.fox": "<PERSON><PERSON><PERSON>", "entity.minecraft.frog": "<PERSON>", "entity.minecraft.furnace_minecart": "Vagón con horno", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Zombi gigante", "entity.minecraft.glow_item_frame": "<PERSON> l<PERSON>", "entity.minecraft.glow_squid": "Calamar luminoso", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Guardián", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON> feliz", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Vagón con tolva", "entity.minecraft.horse": "Caballo", "entity.minecraft.husk": "<PERSON><PERSON><PERSON> mom<PERSON>", "entity.minecraft.illusioner": "Ilusionista", "entity.minecraft.interaction": "Interacción", "entity.minecraft.iron_golem": "Gólem de <PERSON>ro", "entity.minecraft.item": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.item_display": "Objeto holográfico", "entity.minecraft.item_frame": "<PERSON>", "entity.minecraft.jungle_boat": "<PERSON><PERSON>", "entity.minecraft.jungle_chest_boat": "<PERSON><PERSON> de jungla con baúl", "entity.minecraft.killer_bunny": "El conejo asesino", "entity.minecraft.leash_knot": "<PERSON>udo de rienda", "entity.minecraft.lightning_bolt": "Rayo", "entity.minecraft.lingering_potion": "Poción persistente", "entity.minecraft.llama": "Llama", "entity.minecraft.llama_spit": "Escupitajo de llama", "entity.minecraft.magma_cube": "Cubo de magma", "entity.minecraft.mangrove_boat": "<PERSON><PERSON>", "entity.minecraft.mangrove_chest_boat": "<PERSON><PERSON> de manglares con baúl", "entity.minecraft.marker": "Marcador", "entity.minecraft.minecart": "Vagón", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "<PERSON><PERSON>", "entity.minecraft.oak_chest_boat": "<PERSON>te de roble con baúl", "entity.minecraft.ocelot": "<PERSON><PERSON>gua<PERSON>", "entity.minecraft.ominous_item_spawner": "Generador de objetos siniestros", "entity.minecraft.painting": "Cuadro", "entity.minecraft.pale_oak_boat": "<PERSON><PERSON> de madera pálida", "entity.minecraft.pale_oak_chest_boat": "Bote de madera pálida con cofre", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON>", "entity.minecraft.phantom": "Phantom", "entity.minecraft.pig": "Cochino", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON> bruto", "entity.minecraft.pillager": "Saqueador", "entity.minecraft.player": "Jugador(a)", "entity.minecraft.polar_bear": "Oso polar", "entity.minecraft.potion": "Poción", "entity.minecraft.pufferfish": "Pez globo", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Devastador", "entity.minecraft.salmon": "Salmón", "entity.minecraft.sheep": "Oveja", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Proyectil de <PERSON>ker", "entity.minecraft.silverfish": "Pez de plata", "entity.minecraft.skeleton": "Esqueleto", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON> es<PERSON><PERSON>", "entity.minecraft.slime": "Slime", "entity.minecraft.small_fireball": "Bola de fuego pequeña", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Gólem de nieve", "entity.minecraft.snowball": "<PERSON><PERSON> de <PERSON>eve", "entity.minecraft.spawner_minecart": "Vagón con generador de criaturas", "entity.minecraft.spectral_arrow": "Flecha espectral", "entity.minecraft.spider": "<PERSON><PERSON>", "entity.minecraft.splash_potion": "Poción arrojadiza", "entity.minecraft.spruce_boat": "<PERSON><PERSON> de pino", "entity.minecraft.spruce_chest_boat": "Bote de pino con baúl", "entity.minecraft.squid": "Calamar", "entity.minecraft.stray": "Esqueleto glacial", "entity.minecraft.strider": "Lavagante", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Texto holográfico", "entity.minecraft.tnt": "<PERSON><PERSON><PERSON> activada", "entity.minecraft.tnt_minecart": "Vagón con dinamita", "entity.minecraft.trader_llama": "Llama de comerciante", "entity.minecraft.trident": "Tridente", "entity.minecraft.tropical_fish": "Pez tropical", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "Pez cirujano negro", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON> moro", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON> mariposa adornado", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON> loro", "entity.minecraft.tropical_fish.predefined.13": "Cachama de piedra", "entity.minecraft.tropical_fish.predefined.14": "Cíclido rojo", "entity.minecraft.tropical_fish.predefined.15": "Ophioblen<PERSON> atlanticus", "entity.minecraft.tropical_fish.predefined.16": "Pargo rojo", "entity.minecraft.tropical_fish.predefined.17": "Barbudo", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON> payaso tomate", "entity.minecraft.tropical_fish.predefined.19": "Pez ballesta", "entity.minecraft.tropical_fish.predefined.2": "Pez cirujano azul", "entity.minecraft.tropical_fish.predefined.20": "<PERSON>ez loro de aleta amarilla", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON> cirujano amarillo", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON> mariposa", "entity.minecraft.tropical_fish.predefined.4": "Cíclido", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON> payaso", "entity.minecraft.tropical_fish.predefined.6": "<PERSON><PERSON><PERSON> rosa", "entity.minecraft.tropical_fish.predefined.7": "Pseudochromidae", "entity.minecraft.tropical_fish.predefined.8": "Pargo imperial rojo", "entity.minecraft.tropical_fish.predefined.9": "Salmonete", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON> betta", "entity.minecraft.tropical_fish.type.blockfish": "Pez bloque", "entity.minecraft.tropical_fish.type.brinely": "De agua salada", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON> arcilla", "entity.minecraft.tropical_fish.type.dasher": "<PERSON> rayas", "entity.minecraft.tropical_fish.type.flopper": "Saltarín", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "De arrecife", "entity.minecraft.tropical_fish.type.spotty": "Con manchas", "entity.minecraft.tropical_fish.type.stripey": "Con tiras", "entity.minecraft.tropical_fish.type.sunstreak": "Pez rayo de sol", "entity.minecraft.turtle": "Tortuga", "entity.minecraft.vex": "Vex", "entity.minecraft.villager": "Aldeano", "entity.minecraft.villager.armorer": "<PERSON><PERSON> de armaduras", "entity.minecraft.villager.butcher": "Carnicero", "entity.minecraft.villager.cartographer": "Cartógrafo", "entity.minecraft.villager.cleric": "Sacerdote", "entity.minecraft.villager.farmer": "Granjero", "entity.minecraft.villager.fisherman": "Pescador", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliotecario", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON>", "entity.minecraft.villager.none": "Aldeano", "entity.minecraft.villager.shepherd": "Pastor", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON> de armas", "entity.minecraft.vindicator": "Vindicador", "entity.minecraft.wandering_trader": "Comerciante nómada", "entity.minecraft.warden": "Warden", "entity.minecraft.wind_charge": "Carga de brisa", "entity.minecraft.witch": "Bruja", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Esqueleto del Wither", "entity.minecraft.wither_skull": "Proyectil del Wither", "entity.minecraft.wolf": "Lobo", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON> zombi", "entity.minecraft.zombie_villager": "Aldeano zombi", "entity.minecraft.zombified_piglin": "<PERSON><PERSON> zomb<PERSON>ado", "entity.not_summonable": "No se pudo generar una entidad de tipo %s", "event.minecraft.raid": "Invasión", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Invasión - Derrota", "event.minecraft.raid.raiders_remaining": "Invasores restantes: %s", "event.minecraft.raid.victory": "Victoria", "event.minecraft.raid.victory.full": "Invasión - Victoria", "filled_map.buried_treasure": "Mapa del tesoro", "filled_map.explorer_jungle": "Mapa de exploración de jungla", "filled_map.explorer_swamp": "Mapa de exploración de pantano", "filled_map.id": "ID: %s", "filled_map.level": "(Nivel %s/%s)", "filled_map.locked": "Bloqueado", "filled_map.mansion": "Mapa de exploración de bosques", "filled_map.monument": "Mapa de exploración del océano", "filled_map.scale": "Escala 1:%s", "filled_map.trial_chambers": "Mapa de Prueba del Explorador", "filled_map.unknown": "Mapa desconocido", "filled_map.village_desert": "Mapa de aldea de desierto", "filled_map.village_plains": "Mapa de aldea de planicie", "filled_map.village_savanna": "Mapa de aldea de sabana", "filled_map.village_snowy": "Mapa de aldea nevada", "filled_map.village_taiga": "Mapa de aldea de taiga", "flat_world_preset.minecraft.bottomless_pit": "Pozo sin fondo", "flat_world_preset.minecraft.classic_flat": "Clásico", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Superficie", "flat_world_preset.minecraft.redstone_ready": "Listo para redstone", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON> ne<PERSON>do", "flat_world_preset.minecraft.the_void": "El vacío", "flat_world_preset.minecraft.tunnelers_dream": "Sueño del minero", "flat_world_preset.minecraft.water_world": "Mundo acuático", "flat_world_preset.unknown": "¿¿??", "gameMode.adventure": "<PERSON>do avent<PERSON>", "gameMode.changed": "Modo de juego cambiado a %s", "gameMode.creative": "Modo creativo", "gameMode.hardcore": "¡Modo Extremo!", "gameMode.spectator": "Modo espectador", "gameMode.survival": "Modo supervivencia", "gamerule.allowFireTicksAwayFromPlayer": "Tick de fuego lejos de los jugadores", "gamerule.allowFireTicksAwayFromPlayer.description": "Controla si el fuego y la lava pueden hacer tick a más de 8 chunks de distancia de cualquier jugador", "gamerule.announceAdvancements": "Anunciar progresos", "gamerule.blockExplosionDropDecay": "En explosiones por interacción con bloques, algunos bloques no soltarán objetos", "gamerule.blockExplosionDropDecay.description": "Algunos de los objetos soltados por bloques destruidos a causa de explosiones derivadas de interacciones con bloques se pierden en la explosión.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Obtención de ítems", "gamerule.category.misc": "Varios", "gamerule.category.mobs": "Criaturas", "gamerule.category.player": "Jugador", "gamerule.category.spawning": "Generación de criaturas", "gamerule.category.updates": "Actualizaciones del mundo", "gamerule.commandBlockOutput": "Notificar uso de bloques de comandos", "gamerule.commandModificationBlockLimit": "Límite de bloques modificables por comandos", "gamerule.commandModificationBlockLimit.description": "Número de bloques que pueden modificarse a la vez mediante un comando, como /fill o /clone.", "gamerule.disableElytraMovementCheck": "Desactivar verificación de élitros", "gamerule.disablePlayerMovementCheck": "Desactivar verificación de movimiento de jugador", "gamerule.disableRaids": "Desactivar invasiones", "gamerule.doDaylightCycle": "Ciclo de día y noche", "gamerule.doEntityDrops": "Soltar ítems al romper entidades", "gamerule.doEntityDrops.description": "Permite que las entidades suelten ítems al romperlas. Afecta a vagones (incluyendo sus inventarios), marcos, botes, etc.", "gamerule.doFireTick": "Propagación del fuego", "gamerule.doImmediateRespawn": "Reaparición instantánea", "gamerule.doInsomnia": "<PERSON>rar phantoms", "gamerule.doLimitedCrafting": "Exigir recetas para poder fabricar", "gamerule.doLimitedCrafting.description": "<PERSON> habilitado, el jugador solo podra fabricar recetas ya aprendidas.", "gamerule.doMobLoot": "Soltar ítems al matar criaturas", "gamerule.doMobLoot.description": "Controla que las criaturas puedan soltar recursos, incluyendo orbes de experiencia.", "gamerule.doMobSpawning": "Generar criaturas", "gamerule.doMobSpawning.description": "Algunas entidades pueden tener sus propias reglas.", "gamerule.doPatrolSpawning": "Generar bandas de saqueadores", "gamerule.doTileDrops": "Soltar ítems al romper bloques", "gamerule.doTileDrops.description": "Controla que los bloques puedan soltar recursos, incluyendo orbes de experiencia.", "gamerule.doTraderSpawning": "Generar comerciantes nómadas", "gamerule.doVinesSpread": "Esparcimiento de enredaderas", "gamerule.doVinesSpread.description": "Controla si las enredaderas se esparcen aleatoriamente a los bloques cercanos. Los efectos no se aplican a otro tipo de enredaderas como las retorcidas, o las lacrimosas, etc.", "gamerule.doWardenSpawning": "Generar wardens", "gamerule.doWeatherCycle": "Ciclo meteorológico", "gamerule.drowningDamage": "<PERSON>ño por ahogamiento", "gamerule.enderPearlsVanishOnDeath": "Las Perlas de ender lanzadas desaparecen si el jugador muere", "gamerule.enderPearlsVanishOnDeath.description": "Las perlas de ender lanzadas por un jugador desaparecen cuando este muere.", "gamerule.entitiesWithPassengersCanUsePortals": "Las entidades con pasajeros pueden utilizar portales", "gamerule.entitiesWithPassengersCanUsePortals.description": "Permite a las entidades con pasajeros viajar a través de portales del Nether, del End y nexos del End.", "gamerule.fallDamage": "Daño por caída", "gamerule.fireDamage": "Daño por fuego", "gamerule.forgiveDeadPlayers": "Perdonar jugadores muertos", "gamerule.forgiveDeadPlayers.description": "Las criaturas neutrales que estén enojadas volverán a la normalidad cuando el jugador al que sigan muera cerca.", "gamerule.freezeDamage": "Daño por congelamiento", "gamerule.globalSoundEvents": "Sonidos globales", "gamerule.globalSoundEvents.description": "Cuando un evento ocurre, como la generación de un jefe, el sonido se oye en todo el mundo.", "gamerule.keepInventory": "Mantener inventario al morir", "gamerule.lavaSourceConversion": "La lava se convierte en fuente", "gamerule.lavaSourceConversion.description": "Cuando una corriente de lava está rodeada de agua por dos o más lados se convierte en fuente.", "gamerule.locatorBar": "Activar localizador de jugadores", "gamerule.locatorBar.description": "Cuando está activada, se muestra una barra en la pantalla que indica la dirección de otros jugadores.", "gamerule.logAdminCommands": "Notificar comandos de administrador", "gamerule.maxCommandChainLength": "Límite de comandos encadenados", "gamerule.maxCommandChainLength.description": "Se aplica a las cadenas de bloques de comandos y funciones.", "gamerule.maxCommandForkCount": "Límite de contextos del comando", "gamerule.maxCommandForkCount.description": "El número máximo de contextos que se pueden utilizar en comandos como «execute as».", "gamerule.maxEntityCramming": "Máx. entidades apiladas por bloque", "gamerule.minecartMaxSpeed": "Velocidad máxima de los vagones", "gamerule.minecartMaxSpeed.description": "Valocidad máxima por defecto de un vagon por tierra.", "gamerule.mobExplosionDropDecay": "En explosiones por criaturas, algunos bloques no soltarán objetos", "gamerule.mobExplosionDropDecay.description": "Algunos de los objetos soltados por bloques destruidos a causa de explosiones derivadas de criaturas desaparecen con la explosión.", "gamerule.mobGriefing": "Permitir que las criaturas destruyan", "gamerule.naturalRegeneration": "<PERSON><PERSON>ar salud", "gamerule.playersNetherPortalCreativeDelay": "Tiempo de espera para teletransportarse al Nether en el modo creativo", "gamerule.playersNetherPortalCreativeDelay.description": "Tiempo (en tics) que necesita pasar un jugador sobre el portal en modo creativo para cambiar de dimensión.", "gamerule.playersNetherPortalDefaultDelay": "Tiempo de espera para teletransportarse al Nether en modos de juego que no sean creativo", "gamerule.playersNetherPortalDefaultDelay.description": "Tiempo (en tics) que necesita pasar un jugador sobre el portal en modos de juego que no sean creativo para cambiar de dimensión.", "gamerule.playersSleepingPercentage": "Porcentaje de jugadores para dormir", "gamerule.playersSleepingPercentage.description": "El porcentaje de jugadores durmiendo necesarios para saltarse la noche al dormir.", "gamerule.projectilesCanBreakBlocks": "Los proyectiles pueden romper bloques", "gamerule.projectilesCanBreakBlocks.description": "Controla si el impacto de proyectiles destruirá bloques a los que normalmente puede destruir.", "gamerule.randomTickSpeed": "Velocidad de ticks al azar", "gamerule.reducedDebugInfo": "Reducir datos de F3", "gamerule.reducedDebugInfo.description": "Limita el contenido de la pantalla debug al presionar F3.", "gamerule.sendCommandFeedback": "Mostrar respuestas de comandos", "gamerule.showDeathMessages": "<PERSON>rar mensajes de muertes", "gamerule.snowAccumulationHeight": "Altura de acumulación de nieve", "gamerule.snowAccumulationHeight.description": "<PERSON><PERSON><PERSON> nieva, se forman capas de nieve en el suelo con este número como capas máximas.", "gamerule.spawnChunkRadius": "Radio del punto de aparición", "gamerule.spawnChunkRadius.description": "Cantidad de chunks que se mantienen cargados alrededor del punto de reaparición.", "gamerule.spawnRadius": "Radio del punto de reaparición", "gamerule.spawnRadius.description": "Controla el tamaño de la zona alrededor del punto de reaparición donde pueden aparecer los jugadores.", "gamerule.spectatorsGenerateChunks": "Generar terreno en modo espectador", "gamerule.tntExplodes": "Permitir que el TNT se active y explote.", "gamerule.tntExplosionDropDecay": "En explosiones por dinamita, algunos bloques no soltarán objetos", "gamerule.tntExplosionDropDecay.description": "Algunos de los objetos soltados por bloques destruidos a causa de explosiones derivadas de la dinamita desaparecen con la explosión.", "gamerule.universalAnger": "Ira universal", "gamerule.universalAnger.description": "Si está activa, cuando las criaturas neutrales estén enojadas atacarán a cualquier jugador cercano, no sólo al que las hizo enojar.\nFunciona mejor si la regla \"Perdonar jugadores muertos\" se desactiva.", "gamerule.waterSourceConversion": "El agua se convierte en fuente", "gamerule.waterSourceConversion.description": "Cuando una corriente de agua está rodeada de agua por dos o más lados se convierte en fuente.", "generator.custom": "personalizado", "generator.customized": "Personalizado (antiguo)", "generator.minecraft.amplified": "AMPLIFICADO", "generator.minecraft.amplified.info": "Aviso: ¡sólo por diversión!\nRequiere una buena compu.", "generator.minecraft.debug_all_block_states": "Debug", "generator.minecraft.flat": "Plano", "generator.minecraft.large_biomes": "Superbiomas", "generator.minecraft.normal": "Por defecto", "generator.minecraft.single_biome_surface": "Bioma único", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON><PERSON> flotantes", "gui.abuseReport.attestation": "Al enviar este informe, confirmas que la información que proporcionas es correcta y está completa.", "gui.abuseReport.comments": "Comentarios", "gui.abuseReport.describe": "Al compartir detalles, nos ayudas a tomar una decisión adecuada.", "gui.abuseReport.discard.content": "Si sales, este reporte y tus comentarios se descartarán.\n¿Estás seguro de que quieres salir?", "gui.abuseReport.discard.discard": "Salir y descartar reporte", "gui.abuseReport.discard.draft": "Guardar como borrador", "gui.abuseReport.discard.return": "Con<PERSON><PERSON>r editando", "gui.abuseReport.discard.title": "¿Descartar reporte y comentarios?", "gui.abuseReport.draft.content": "¿Te gustaría seguir editando el reporte ya existente o prefieres descartarlo y crear uno nuevo?", "gui.abuseReport.draft.discard": "Descar<PERSON>", "gui.abuseReport.draft.edit": "<PERSON><PERSON><PERSON>", "gui.abuseReport.draft.quittotitle.content": "¿Quieres seguir editán<PERSON>lo o descartarlo?", "gui.abuseReport.draft.quittotitle.title": "Tienes un borrador de reporte de chat que se perderá si sales", "gui.abuseReport.draft.title": "¿Editar el borrador del reporte de chat?", "gui.abuseReport.error.title": "Error al enviar tu reporte", "gui.abuseReport.message": "¿Dónde has observado el mal comportamiento?\nEsto nos ayudará a investigar tu caso.", "gui.abuseReport.more_comments": "Por favor describe lo sucedido:", "gui.abuseReport.name.comment_box_label": "Por favor, describe por qué quieres reportar este nombre:", "gui.abuseReport.name.reporting": "Estás denunciando a \"%s\".", "gui.abuseReport.name.title": "Denunciar nombre de jugador", "gui.abuseReport.observed_what": "¿Por qué estás reportando esto?", "gui.abuseReport.read_info": "Más información sobre los reportes", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Drogas o alcohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Alguien está promoviendo la participación en actividades ilegales relacionadas con drogas, o el consumo de alcohol por debajo de la edad legal.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Explotación o abuso sexual de menores", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Alguien está incitando o hablando de un comportamiento indecente hacia menores.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Difamación, suplantación de identidad o información falsa", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Alguien está dañando la reputación de otra persona, suplantando la identidad de alguien o compartiendo información falsa, con el objetivo de aprovecharse o confundir a otros.", "gui.abuseReport.reason.description": "Descripción:", "gui.abuseReport.reason.false_reporting": "Reporte falso", "gui.abuseReport.reason.generic": "<PERSON><PERSON><PERSON>", "gui.abuseReport.reason.generic.description": "Me molestó / ha hecho algo que no me gusta.", "gui.abuseReport.reason.harassment_or_bullying": "Intimidación o acoso", "gui.abuseReport.reason.harassment_or_bullying.description": "Alguien está hostigando, atacando o acosándote a ti o a otra persona. Esto incluye haber intentado contactar repetidamente contigo o con otra persona o publicar información personal privada sobre ti u otra persona sin consentimiento (\"doxeo\").", "gui.abuseReport.reason.hate_speech": "Discurso de odio", "gui.abuseReport.reason.hate_speech.description": "Alguien está atacándote a ti o a otro jugador por características de su identidad, como religión, etnia o condición sexual.", "gui.abuseReport.reason.imminent_harm": "Peligro inminente: Amenazas de daño a otros", "gui.abuseReport.reason.imminent_harm.description": "Alguien está amenazando con hacerte daño a ti o a otra persona en la vida real.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Imágenes íntimas sin consentimiento", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Alguien está pidiendo, compartiendo o incitando al tráfico de imágenes privadas e íntimas.", "gui.abuseReport.reason.self_harm_or_suicide": "Peligro inminente: Autolesión o suicidio", "gui.abuseReport.reason.self_harm_or_suicide.description": "Alguien está hablando sobre autolesionarse o amenazando con hacerlo en la vida real.", "gui.abuseReport.reason.sexually_inappropriate": "Sexualmente inapropiado", "gui.abuseReport.reason.sexually_inappropriate.description": "Aspectos que muestran elementos relacionados a actos sexuales, órganos sexuales o violencia sexual.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorismo o extremismo violento", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Alguien está hablando, incitando o amenazando con cometer actos de terrorismo o extremismo violento por motivos políticos, religiosos, ideológicos o de cualquier otra índole.", "gui.abuseReport.reason.title": "Seleccionar categoria de reporte", "gui.abuseReport.report_sent_msg": "Hemos recibido tu reporte, ¡Gracias!\n\nNuestro equipo lo revisará lo más pronto posible.", "gui.abuseReport.select_reason": "Seleccionar categoria de reporte", "gui.abuseReport.send": "Enviar reporte", "gui.abuseReport.send.comment_too_long": "Por favor, acorta el informe", "gui.abuseReport.send.error_message": "Se produjo un error al enviar tu reporte:\n\"%s\"", "gui.abuseReport.send.generic_error": "Se ha producido un error inesperado al enviar tu reporte.", "gui.abuseReport.send.http_error": "Se ha producido un error HTTP inesperado al enviar tu reporte.", "gui.abuseReport.send.json_error": "Se ha detectado una carga útil mal estructurada al enviar tu reporte.", "gui.abuseReport.send.no_reason": "Selecciona una categoría para el reporte", "gui.abuseReport.send.not_attested": "Lee el texto y asegúrate de marcar la casilla para enviar el informe", "gui.abuseReport.send.service_unavailable": "No se ha podido contactar con el servicio de reportes por abuso. Asegúrate de que estás conectado a internet e inténtalo de nuevo.", "gui.abuseReport.sending.title": "Enviando tu reporte...", "gui.abuseReport.sent.title": "Reporte enviado", "gui.abuseReport.skin.title": "Reportar aspecto de jugador", "gui.abuseReport.title": "<PERSON>ar jugador", "gui.abuseReport.type.chat": "Men<PERSON><PERSON><PERSON>", "gui.abuseReport.type.name": "Nombre del jugador", "gui.abuseReport.type.skin": "<PERSON><PERSON><PERSON> de j<PERSON>dor", "gui.acknowledge": "Entendido", "gui.advancements": "Progresos", "gui.all": "<PERSON><PERSON>", "gui.back": "Atrás", "gui.banned.description": "%s\n\n%s\n\nMás información en el siguiente enlace: %s", "gui.banned.description.permanent": "Tu cuenta fue suspendida permanentemente, lo que significa que no podrás volver a jugar en línea ni unirte a Realms.", "gui.banned.description.reason": "Recientemente hemos recibido un reporte por mal comportamiento hacia tu cuenta. Nuestros moderadores han revisado tu caso y lo han identificado como %s, lo que va en contra de los estándares de la comunidad de Minecraft.", "gui.banned.description.reason_id": "Código: %s", "gui.banned.description.reason_id_message": "Código: %s (%s)", "gui.banned.description.temporary": "%s <PERSON>ta entonces, no podrás jugar en línea ni unirte a Realms.", "gui.banned.description.temporary.duration": "Tu cuenta fue suspendida temporalmente y será reactivada en %s.", "gui.banned.description.unknownreason": "Recientemente hemos recibido un reporte por mal comportamiento hacia tu cuenta. Nuestros moderadores han revisado tu caso y han identificado que va en contra de los estándares de la comunidad de Minecraft.", "gui.banned.name.description": "Tu nombre actual, \"%s\", incumple nuestros estándares comunitarios. <PERSON>uedes jugar en mundos de un jugador, pero debes cambiar tu nombre para jugar en multijugador.\n\nAprende más o solicita una revisión del caso en el siguiente enlace: %s", "gui.banned.name.title": "Nombre no permitido en multijugador", "gui.banned.reason.defamation_impersonation_false_information": "Robo de identidad o compartir información para aprovecharse o engañar a otros", "gui.banned.reason.drugs": "Referencias a drogas ilegales", "gui.banned.reason.extreme_violence_or_gore": "Representaciones de violencia excesiva o gore en la vida real", "gui.banned.reason.false_reporting": "Exceso de reportes falsos o inexactos", "gui.banned.reason.fraud": "Adquisición o uso fraudulento de contenido", "gui.banned.reason.generic_violation": "Violación de las normas de la comunidad", "gui.banned.reason.harassment_or_bullying": "Lenguaje abusivo usado de manera directa y dañina", "gui.banned.reason.hate_speech": "Incitación al odio o discriminación", "gui.banned.reason.hate_terrorism_notorious_figure": "Referencias a grupos de odio, organizaciones terroristas o figuras notorias", "gui.banned.reason.imminent_harm_to_person_or_property": "Intención de causar daños en la vida real a personas o propiedades", "gui.banned.reason.nudity_or_pornography": "Mostrar material obsceno o pornográfico", "gui.banned.reason.sexually_inappropriate": "Temas o contenido de naturaleza sexual", "gui.banned.reason.spam_or_advertising": "Spam o publicidad", "gui.banned.skin.description": "Tu aspecto actual incumple nuestros estándares comunitarios. <PERSON><PERSON>es jugar con un aspecto predeterminado, o seleccionar uno nuevo.\n\nAprende más o solicita una revisión del caso en el siguiente enlace: %s", "gui.banned.skin.title": "Aspecto no permitido", "gui.banned.title.permanent": "Cuenta suspendida permanentemente", "gui.banned.title.temporary": "Cuenta suspendida temporalmente", "gui.cancel": "<PERSON><PERSON><PERSON>", "gui.chatReport.comments": "Comentarios", "gui.chatReport.describe": "Compartir detalles nos ayudará a tomar una decisión más precisa.", "gui.chatReport.discard.content": "Si sales ahora, se borrarán este reporte y tus comentarios.\n¿Seguro que quieres salir?", "gui.chatReport.discard.discard": "Salir y descartar reporte", "gui.chatReport.discard.draft": "Guardar borrador", "gui.chatReport.discard.return": "<PERSON><PERSON><PERSON>", "gui.chatReport.discard.title": "¿Descartar reporte y comentarios?", "gui.chatReport.draft.content": "¿Te gustaría seguir editando el reporte existente o descartarlo y crear uno nuevo?", "gui.chatReport.draft.discard": "Descar<PERSON>", "gui.chatReport.draft.edit": "<PERSON><PERSON><PERSON>", "gui.chatReport.draft.quittotitle.content": "¿Te gustaría seguir editán<PERSON>lo o descartarlo?", "gui.chatReport.draft.quittotitle.title": "Tienes un borrador de reporte que se perderá si sales", "gui.chatReport.draft.title": "¿Editar borrador de reporte?", "gui.chatReport.more_comments": "Describe lo que sucedió:", "gui.chatReport.observed_what": "¿Por qué estás reportando esto?", "gui.chatReport.read_info": "Más información sobre reportes", "gui.chatReport.report_sent_msg": "Hemos recibido tu reporte. ¡Gracias!\n\nNuestro equipo la revisará lo antes posible.", "gui.chatReport.select_chat": "Seleccionar mensaje(s) del chat para reportar", "gui.chatReport.select_reason": "Selecciona una categoría de reporte", "gui.chatReport.selected_chat": "%s mensaje(s) selecionados para reportar", "gui.chatReport.send": "Enviar reporte", "gui.chatReport.send.comments_too_long": "Por favor, acorta el comentario", "gui.chatReport.send.no_reason": "Selecciona una categoría para el reporte", "gui.chatReport.send.no_reported_messages": "Selecciona al menos un mensaje del chat para reportar", "gui.chatReport.send.too_many_messages": "Estás incluyendo demasiados mensajes en el reporte", "gui.chatReport.title": "<PERSON>ar jugador", "gui.chatSelection.context": "Los mensajes relacionados con esta selección serán incluidos para añadir un contexto adicional", "gui.chatSelection.fold": "%s mensajes ocultos", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s se unió al chat", "gui.chatSelection.message.narrate": "%s ha dicho: %s (%s)", "gui.chatSelection.selected": "Mensaje(s) seleccionados: %s/%s", "gui.chatSelection.title": "Seleccionar mensajes que quieres reportar", "gui.continue": "<PERSON><PERSON><PERSON><PERSON>", "gui.copy_link_to_clipboard": "<PERSON><PERSON>r enlace al portapapeles", "gui.days": "%s día(s)", "gui.done": "<PERSON><PERSON>", "gui.down": "Abajo", "gui.entity_tooltip.type": "Tipo: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s archivos rechazados", "gui.fileDropFailure.title": "Error al añadir archivos", "gui.hours": "%s hora(s)", "gui.loadingMinecraft": "Cargando Minecraft", "gui.minutes": "%s minuto(s)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Botón de %s", "gui.narrate.editBox": "Barra de texto de %s: %s", "gui.narrate.slider": "Barra de %s", "gui.narrate.tab": "Pestaña de %s", "gui.no": "No", "gui.none": "<PERSON><PERSON><PERSON>", "gui.ok": "Aceptar", "gui.open_report_dir": "<PERSON><PERSON><PERSON><PERSON> de informes", "gui.proceed": "<PERSON><PERSON><PERSON><PERSON>", "gui.recipebook.moreRecipes": "Haz clic derecho para ver más", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Buscar...", "gui.recipebook.toggleRecipes.all": "Mostrando todo", "gui.recipebook.toggleRecipes.blastable": "Mostrando fundibles", "gui.recipebook.toggleRecipes.craftable": "Mostrando fabricables", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON> horneables", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON> ahuma<PERSON>", "gui.report_to_server": "Reportar al servidor", "gui.socialInteractions.blocking_hint": "Administrar con cuenta de Microsoft", "gui.socialInteractions.empty_blocked": "No hay jugadores bloqueados en el chat", "gui.socialInteractions.empty_hidden": "No hay jugadores ocultos en el chat", "gui.socialInteractions.hidden_in_chat": "Los mensajes de chat de %s estarán ocultos", "gui.socialInteractions.hide": "Ocultar en el chat", "gui.socialInteractions.narration.hide": "Ocultar mensajes de %s", "gui.socialInteractions.narration.report": "Reportar a %s", "gui.socialInteractions.narration.show": "Mostrar mensajes de %s", "gui.socialInteractions.report": "Reportar", "gui.socialInteractions.search_empty": "No se ha encontrado ningún jugador con ese nombre", "gui.socialInteractions.search_hint": "Buscar...", "gui.socialInteractions.server_label.multiple": "%s - %s jugadores", "gui.socialInteractions.server_label.single": "%s - %s jugador", "gui.socialInteractions.show": "Mostrar en el chat", "gui.socialInteractions.shown_in_chat": "Los mensajes de chat de %s serán visibles", "gui.socialInteractions.status_blocked": "Bloqueado", "gui.socialInteractions.status_blocked_offline": "Bloqueado - Desconectado", "gui.socialInteractions.status_hidden": "Oculto", "gui.socialInteractions.status_hidden_offline": "Oculto - Desconectado", "gui.socialInteractions.status_offline": "Sin conexión", "gui.socialInteractions.tab_all": "Todo", "gui.socialInteractions.tab_blocked": "Bloqueado", "gui.socialInteractions.tab_hidden": "<PERSON>scondi<PERSON>", "gui.socialInteractions.title": "Interacciones sociales", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report": "<PERSON>ar jugador", "gui.socialInteractions.tooltip.report.disabled": "El servicio de reporte no está disponible", "gui.socialInteractions.tooltip.report.no_messages": "No hay mensajes de infracción de %s", "gui.socialInteractions.tooltip.report.not_reportable": "Este jugador no puede ser reportado, porque sus mensajes no se pueden verificar en este servidor", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON> mensajes", "gui.stats": "Estadísticas", "gui.toMenu": "Volver a la lista de servidores", "gui.toRealms": "Volver a la lista de Realms", "gui.toTitle": "<PERSON>ver al menú principal", "gui.toWorld": "Volver a la lista de Mundos", "gui.togglable_slot": "Haz clic para desactivar el espacio", "gui.up": "Arriba", "gui.waitingForResponse.button.inactive": "Atrás (%ss)", "gui.waitingForResponse.title": "Esperando a el servidor", "gui.yes": "Sí", "hanging_sign.edit": "Editar mensaje del cartel colgante", "instrument.minecraft.admire_goat_horn": "Admiración", "instrument.minecraft.call_goat_horn": "Llamada", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "Sen<PERSON><PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Reflexión", "instrument.minecraft.seek_goat_horn": "Búsqueda", "instrument.minecraft.sing_goat_horn": "Canto", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "Bo<PERSON>r", "inventory.hotbarInfo": "Guarda la barra actual con %1$s+%2$s", "inventory.hotbarSaved": "Barra de ítems guardada (%1$s+%2$s para usarla)", "item.canBreak": "<PERSON><PERSON><PERSON> romper:", "item.canPlace": "Puede colocarse sobre:", "item.canUse.unknown": "Desconocido", "item.color": "Color: %s", "item.components": "%s componente(s)", "item.disabled": "<PERSON><PERSON><PERSON><PERSON>", "item.durability": "Durabilidad: %s / %s", "item.dyed": "Teñido", "item.minecraft.acacia_boat": "Bote de acacia", "item.minecraft.acacia_chest_boat": "Bote de acacia con baúl", "item.minecraft.allay_spawn_egg": "<PERSON><PERSON> allay", "item.minecraft.amethyst_shard": "Fragmento de amatista", "item.minecraft.angler_pottery_shard": "Fragmento de caña de pescar", "item.minecraft.angler_pottery_sherd": "Fragmento de cerámica de caña de pescar", "item.minecraft.apple": "Man<PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Fragmento de arco", "item.minecraft.archer_pottery_sherd": "Fragmento de cerámica de arco", "item.minecraft.armadillo_scute": "Escama de cachicamo", "item.minecraft.armadillo_spawn_egg": "Generador de cachicamo", "item.minecraft.armor_stand": "Soporte para armadura", "item.minecraft.arms_up_pottery_shard": "Fragmento de manos arriba", "item.minecraft.arms_up_pottery_sherd": "Fragmento de cerámica de manos arriba", "item.minecraft.arrow": "Fle<PERSON>", "item.minecraft.axolotl_bucket": "<PERSON><PERSON> con A<PERSON>e", "item.minecraft.axolotl_spawn_egg": "<PERSON><PERSON>", "item.minecraft.baked_potato": "<PERSON>cida", "item.minecraft.bamboo_chest_raft": "Balsa de bambú con baúl", "item.minecraft.bamboo_raft": "Balsa de bambú", "item.minecraft.bat_spawn_egg": "<PERSON><PERSON>", "item.minecraft.bee_spawn_egg": "<PERSON><PERSON> abeja", "item.minecraft.beef": "Bistec crudo", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "Semillas de remolacha", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.birch_boat": "<PERSON><PERSON>", "item.minecraft.birch_chest_boat": "Bote de abedul con baúl", "item.minecraft.black_bundle": "Bolsa negra", "item.minecraft.black_dye": "Tinte negro", "item.minecraft.black_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.blade_pottery_shard": "Fragmento de espada", "item.minecraft.blade_pottery_sherd": "Fragmento de cerámica de espada", "item.minecraft.blaze_powder": "Polvo de blaze", "item.minecraft.blaze_rod": "Vara de blaze", "item.minecraft.blaze_spawn_egg": "Generar blaze", "item.minecraft.blue_bundle": "Bolsa azul", "item.minecraft.blue_dye": "<PERSON>te a<PERSON>l", "item.minecraft.blue_egg": "Huevo azul", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "Generar esqueleto <PERSON>", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.bolt_armor_trim_smithing_template.new": "Diseño de armadura de tornillo", "item.minecraft.bone": "Hueso", "item.minecraft.bone_meal": "Polvo de hueso", "item.minecraft.book": "Libro", "item.minecraft.bordure_indented_banner_pattern": "Estampado de bordura dentada", "item.minecraft.bow": "Arco", "item.minecraft.bowl": "Tazón", "item.minecraft.bread": "Pan", "item.minecraft.breeze_rod": "Vara de Brisa", "item.minecraft.breeze_spawn_egg": "Generar breeze", "item.minecraft.brewer_pottery_shard": "Fragmento de poción", "item.minecraft.brewer_pottery_sherd": "Fragmento de cerámica de poción", "item.minecraft.brewing_stand": "Soporte para pociones", "item.minecraft.brick": "Ladrillo", "item.minecraft.brown_bundle": "<PERSON><PERSON><PERSON> marr<PERSON>", "item.minecraft.brown_dye": "<PERSON><PERSON>", "item.minecraft.brown_egg": "<PERSON><PERSON> ma<PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.brush": "<PERSON><PERSON><PERSON>", "item.minecraft.bucket": "Balde", "item.minecraft.bundle": "Bolsa", "item.minecraft.bundle.empty": "Vacía", "item.minecraft.bundle.empty.description": "<PERSON><PERSON><PERSON> contener una pila mixta de objetos", "item.minecraft.bundle.full": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Fragmento de fuego", "item.minecraft.burn_pottery_sherd": "Fragmento de cerámica de fuego", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON> camello", "item.minecraft.carrot": "Zanahoria", "item.minecraft.carrot_on_a_stick": "Caña con zanahoria", "item.minecraft.cat_spawn_egg": "Generar gato", "item.minecraft.cauldron": "Calder<PERSON>", "item.minecraft.cave_spider_spawn_egg": "Generar a<PERSON> cue<PERSON>", "item.minecraft.chainmail_boots": "Botas de cota de malla", "item.minecraft.chainmail_chestplate": "Pechera de cota de malla", "item.minecraft.chainmail_helmet": "Casco de cota de malla", "item.minecraft.chainmail_leggings": "Grebas de cota de malla", "item.minecraft.charcoal": "Carbón vegetal", "item.minecraft.cherry_boat": "<PERSON><PERSON> c<PERSON>", "item.minecraft.cherry_chest_boat": "Bote de cerezo con baúl", "item.minecraft.chest_minecart": "Vagón con baúl", "item.minecraft.chicken": "<PERSON><PERSON> crudo", "item.minecraft.chicken_spawn_egg": "<PERSON><PERSON> gallina", "item.minecraft.chorus_fruit": "Fruta de chorus", "item.minecraft.clay_ball": "<PERSON><PERSON>", "item.minecraft.clock": "<PERSON><PERSON><PERSON>", "item.minecraft.coal": "Carbón", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.coast_armor_trim_smithing_template.new": "Diseño de armadura de costa", "item.minecraft.cocoa_beans": "Semillas de cacao", "item.minecraft.cod": "Bacalao crudo", "item.minecraft.cod_bucket": "Balde con bacalao", "item.minecraft.cod_spawn_egg": "<PERSON><PERSON> b<PERSON>", "item.minecraft.command_block_minecart": "Vagón con bloque de comandos", "item.minecraft.compass": "Brújula", "item.minecraft.cooked_beef": "Bistec", "item.minecraft.cooked_chicken": "<PERSON>o cocido", "item.minecraft.cooked_cod": "Bacalao cocinado", "item.minecraft.cooked_mutton": "Cordero cocido", "item.minecraft.cooked_porkchop": "<PERSON><PERSON><PERSON> cocida", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON> co<PERSON>o", "item.minecraft.cooked_salmon": "Salmón cocido", "item.minecraft.cookie": "Galleta", "item.minecraft.copper_ingot": "Lingote de cobre", "item.minecraft.cow_spawn_egg": "Generar vaca", "item.minecraft.creaking_spawn_egg": "Generador de tronador", "item.minecraft.creeper_banner_pattern": "Diseño de estandarte", "item.minecraft.creeper_banner_pattern.desc": "Estampado de creeper", "item.minecraft.creeper_banner_pattern.new": "Estampado de creeper", "item.minecraft.creeper_spawn_egg": "Generar creeper", "item.minecraft.crossbow": "Ballesta", "item.minecraft.crossbow.projectile": "Proyectil:", "item.minecraft.crossbow.projectile.multiple": "Proyectil: %s x %s", "item.minecraft.crossbow.projectile.single": "Proyectil: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON><PERSON> cian", "item.minecraft.cyan_dye": "Tinte turquesa", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Fragmento de peligro", "item.minecraft.danger_pottery_sherd": "Fragmento de cerámica de peligro", "item.minecraft.dark_oak_boat": "Bote de roble oscuro", "item.minecraft.dark_oak_chest_boat": "Bote de roble oscuro con baúl", "item.minecraft.debug_stick": "Bastón de desarrollo", "item.minecraft.debug_stick.empty": "%s no tiene propiedades", "item.minecraft.debug_stick.select": "seleccionado \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" cambió a %s", "item.minecraft.diamond": "Diamante", "item.minecraft.diamond_axe": "<PERSON><PERSON>", "item.minecraft.diamond_boots": "Botas de diamante", "item.minecraft.diamond_chestplate": "Pechera de diamante", "item.minecraft.diamond_helmet": "Casco de diamante", "item.minecraft.diamond_hoe": "Azadón de diamante", "item.minecraft.diamond_horse_armor": "Armadura de diamante para caballo", "item.minecraft.diamond_leggings": "G<PERSON><PERSON> de diamante", "item.minecraft.diamond_pickaxe": "Pico de di<PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON>", "item.minecraft.diamond_sword": "Espada de diamante", "item.minecraft.disc_fragment_5": "Fragmento de disco", "item.minecraft.disc_fragment_5.desc": "Disco - 5", "item.minecraft.dolphin_spawn_egg": "<PERSON><PERSON>", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON> burro", "item.minecraft.dragon_breath": "<PERSON><PERSON>", "item.minecraft.dried_kelp": "Algas secas", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON>", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.dune_armor_trim_smithing_template.new": "Diseño de armadura de dunas", "item.minecraft.echo_shard": "Fragmento de eco", "item.minecraft.egg": "Huevo", "item.minecraft.elder_guardian_spawn_egg": "<PERSON><PERSON> <PERSON>", "item.minecraft.elytra": "<PERSON><PERSON><PERSON>", "item.minecraft.emerald": "Esm<PERSON><PERSON>", "item.minecraft.enchanted_book": "Libro encantado", "item.minecraft.enchanted_golden_apple": "Manzana de oro encantada", "item.minecraft.end_crystal": "Cristal del End", "item.minecraft.ender_dragon_spawn_egg": "<PERSON><PERSON>", "item.minecraft.ender_eye": "<PERSON><PERSON> de ender", "item.minecraft.ender_pearl": "<PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON> enderman", "item.minecraft.endermite_spawn_egg": "Generar endermite", "item.minecraft.evoker_spawn_egg": "Generar invocador", "item.minecraft.experience_bottle": "Frasco de experiencia", "item.minecraft.explorer_pottery_shard": "Fragmento de explorador", "item.minecraft.explorer_pottery_sherd": "Fragmento de cerámica de explorador", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.eye_armor_trim_smithing_template.new": "Diseño de armadura de ojos", "item.minecraft.feather": "<PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "Ojo de araña fermentado", "item.minecraft.field_masoned_banner_pattern": "Estampado de campo de albañilería", "item.minecraft.filled_map": "Mapa", "item.minecraft.fire_charge": "Carga de fuego", "item.minecraft.firework_rocket": "Cohete de fuegos artificiales", "item.minecraft.firework_rocket.flight": "Duración del vuelo:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Pólvora de fuegos artificiales", "item.minecraft.firework_star.black": "negra", "item.minecraft.firework_star.blue": "azul", "item.minecraft.firework_star.brown": "marr<PERSON>", "item.minecraft.firework_star.custom_color": "Personalizado", "item.minecraft.firework_star.cyan": "turquesa", "item.minecraft.firework_star.fade_to": "Pasa a", "item.minecraft.firework_star.flicker": " con titileo.", "item.minecraft.firework_star.gray": "gris", "item.minecraft.firework_star.green": "verde", "item.minecraft.firework_star.light_blue": "azul cielo", "item.minecraft.firework_star.light_gray": "gris claro", "item.minecraft.firework_star.lime": "verde limón", "item.minecraft.firework_star.magenta": "magenta", "item.minecraft.firework_star.orange": "naran<PERSON>", "item.minecraft.firework_star.pink": "rosa", "item.minecraft.firework_star.purple": "morada", "item.minecraft.firework_star.red": "roja", "item.minecraft.firework_star.shape": "Forma desconocida", "item.minecraft.firework_star.shape.burst": "Estallido", "item.minecraft.firework_star.shape.creeper": "Con forma de creeper", "item.minecraft.firework_star.shape.large_ball": "Esfera grande", "item.minecraft.firework_star.shape.small_ball": "Esfera pequeña", "item.minecraft.firework_star.shape.star": "Con forma de estrella", "item.minecraft.firework_star.trail": " con rastro.", "item.minecraft.firework_star.white": "blanca", "item.minecraft.firework_star.yellow": "amarilla", "item.minecraft.fishing_rod": "Caña de pescar", "item.minecraft.flint": "Pedernal", "item.minecraft.flint_and_steel": "Yesquero", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.flow_armor_trim_smithing_template.new": "Diseño de armadura de espiral", "item.minecraft.flow_banner_pattern": "Diseño de estandarte", "item.minecraft.flow_banner_pattern.desc": "F<PERSON>jo", "item.minecraft.flow_banner_pattern.new": "Estampado de espiral", "item.minecraft.flow_pottery_sherd": "Fragmento de cerámica de flujo", "item.minecraft.flower_banner_pattern": "Diseño de estandarte", "item.minecraft.flower_banner_pattern.desc": "Estampado de flor", "item.minecraft.flower_banner_pattern.new": "Estampado de flor", "item.minecraft.flower_pot": "Mace<PERSON>", "item.minecraft.fox_spawn_egg": "<PERSON><PERSON>", "item.minecraft.friend_pottery_shard": "Fragmento de amistad", "item.minecraft.friend_pottery_sherd": "Fragmento de cerámica de amistad", "item.minecraft.frog_spawn_egg": "<PERSON><PERSON> rana", "item.minecraft.furnace_minecart": "Vagón con horno", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON> ghast", "item.minecraft.ghast_tear": "Lágrim<PERSON>", "item.minecraft.glass_bottle": "<PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "Rebanada de patilla brillante", "item.minecraft.globe_banner_pattern": "Diseño de estandarte", "item.minecraft.globe_banner_pattern.desc": "Estampado de planeta", "item.minecraft.globe_banner_pattern.new": "Estampado de planeta", "item.minecraft.glow_berries": "Bayas luminosas", "item.minecraft.glow_ink_sac": "Saco de tinta luminosa", "item.minecraft.glow_item_frame": "<PERSON> l<PERSON>", "item.minecraft.glow_squid_spawn_egg": "Generar calamar luminoso", "item.minecraft.glowstone_dust": "Polvo de piedra luminosa", "item.minecraft.goat_horn": "Cuerno de cabra", "item.minecraft.goat_spawn_egg": "Generar cabra", "item.minecraft.gold_ingot": "Lingote de oro", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON> de oro", "item.minecraft.golden_apple": "<PERSON>zan<PERSON> de oro", "item.minecraft.golden_axe": "Hacha de oro", "item.minecraft.golden_boots": "Botas de oro", "item.minecraft.golden_carrot": "Zanahoria de oro", "item.minecraft.golden_chestplate": "Pechera de oro", "item.minecraft.golden_helmet": "Casco de oro", "item.minecraft.golden_hoe": "Azadón de oro", "item.minecraft.golden_horse_armor": "Armadura de oro para caballo", "item.minecraft.golden_leggings": "Grebas de oro", "item.minecraft.golden_pickaxe": "Pico de oro", "item.minecraft.golden_shovel": "Pala de oro", "item.minecraft.golden_sword": "Espada de oro", "item.minecraft.gray_bundle": "Bolsa gris", "item.minecraft.gray_dye": "Tinte gris", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.green_bundle": "Bolsa verde", "item.minecraft.green_dye": "Tinte verde", "item.minecraft.green_harness": "A<PERSON><PERSON>", "item.minecraft.guardian_spawn_egg": "<PERSON><PERSON>", "item.minecraft.gunpowder": "Pólvora", "item.minecraft.guster_banner_pattern": "Diseño de estandarte", "item.minecraft.guster_banner_pattern.desc": "Ventisca", "item.minecraft.guster_banner_pattern.new": "Estampado de brisa", "item.minecraft.guster_pottery_sherd": "Fragmento de cerámica de Ventisca", "item.minecraft.happy_ghast_spawn_egg": "Huevo de aparicion de ghast alegre", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Corazón del mar", "item.minecraft.heart_pottery_shard": "Fragmento de corazón", "item.minecraft.heart_pottery_sherd": "Fragmento de cerámica de corazón", "item.minecraft.heartbreak_pottery_shard": "Fragmento de corazón roto", "item.minecraft.heartbreak_pottery_sherd": "Fragmento de cerámica de corazón roto", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON> ho<PERSON>n", "item.minecraft.honey_bottle": "<PERSON><PERSON> de <PERSON>", "item.minecraft.honeycomb": "Panal de abejas", "item.minecraft.hopper_minecart": "Vagón con tolva", "item.minecraft.horse_spawn_egg": "<PERSON><PERSON> caballo", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.host_armor_trim_smithing_template.new": "Diseño de armadura de anfitrión", "item.minecraft.howl_pottery_shard": "Fragmento de canino", "item.minecraft.howl_pottery_sherd": "Fragmento de cerámica de canino", "item.minecraft.husk_spawn_egg": "<PERSON><PERSON> (Husk)", "item.minecraft.ink_sac": "Saco de tinta", "item.minecraft.iron_axe": "<PERSON><PERSON> <PERSON>", "item.minecraft.iron_boots": "<PERSON><PERSON>ro", "item.minecraft.iron_chestplate": "Pechera de hierro", "item.minecraft.iron_golem_spawn_egg": "Generar gó<PERSON>", "item.minecraft.iron_helmet": "<PERSON><PERSON>", "item.minecraft.iron_hoe": "Azadón de hierro", "item.minecraft.iron_horse_armor": "Armadura de hierro para caballo", "item.minecraft.iron_ingot": "Ling<PERSON> de hierro", "item.minecraft.iron_leggings": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "<PERSON><PERSON>", "item.minecraft.iron_shovel": "<PERSON><PERSON>", "item.minecraft.iron_sword": "E<PERSON><PERSON> de <PERSON>", "item.minecraft.item_frame": "<PERSON>", "item.minecraft.jungle_boat": "<PERSON><PERSON>", "item.minecraft.jungle_chest_boat": "<PERSON><PERSON> de jungla con baúl", "item.minecraft.knowledge_book": "Enciclopedia", "item.minecraft.lapis_lazuli": "La<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Balde con lava", "item.minecraft.lead": "Rienda", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "Botas de cuero", "item.minecraft.leather_chestplate": "Túnica de cuero", "item.minecraft.leather_helmet": "Sombrero de cuero", "item.minecraft.leather_horse_armor": "Armadura de cuero para caballo", "item.minecraft.leather_leggings": "Pantalones de cuero", "item.minecraft.light_blue_bundle": "Bolsa azul claro", "item.minecraft.light_blue_dye": "Tinte azul cielo", "item.minecraft.light_blue_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_bundle": "Bolsa gris claro", "item.minecraft.light_gray_dye": "<PERSON>te gris claro", "item.minecraft.light_gray_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.lime_bundle": "Bolsa verde limón", "item.minecraft.lime_dye": "Tinte verde limón", "item.minecraft.lime_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion": "Poción persistente", "item.minecraft.lingering_potion.effect.awkward": "Poción rara persistente", "item.minecraft.lingering_potion.effect.empty": "Poción persistente no fabricable", "item.minecraft.lingering_potion.effect.fire_resistance": "Poción de resistencia al fuego persistente", "item.minecraft.lingering_potion.effect.harming": "Poción de daño persistente", "item.minecraft.lingering_potion.effect.healing": "Poción de curación persistente", "item.minecraft.lingering_potion.effect.infested": "Poción de infestación persistente", "item.minecraft.lingering_potion.effect.invisibility": "Poción de invisibilidad persistente", "item.minecraft.lingering_potion.effect.leaping": "Poción de salto persistente", "item.minecraft.lingering_potion.effect.levitation": "Poción de levitación persistente", "item.minecraft.lingering_potion.effect.luck": "Poción de suerte persistente", "item.minecraft.lingering_potion.effect.mundane": "Poción mundana persistente", "item.minecraft.lingering_potion.effect.night_vision": "Poción de visión nocturna persistente", "item.minecraft.lingering_potion.effect.oozing": "Poción de exudado persistente", "item.minecraft.lingering_potion.effect.poison": "Poción de veneno persistente", "item.minecraft.lingering_potion.effect.regeneration": "Poción de regeneración persistente", "item.minecraft.lingering_potion.effect.slow_falling": "Poción de caída lenta persistente", "item.minecraft.lingering_potion.effect.slowness": "Poción de lentitud persistente", "item.minecraft.lingering_potion.effect.strength": "Poción de fuerza persistente", "item.minecraft.lingering_potion.effect.swiftness": "Poción de velocidad persistente", "item.minecraft.lingering_potion.effect.thick": "Poción densa persistente", "item.minecraft.lingering_potion.effect.turtle_master": "Poción del maestro tortuga persistente", "item.minecraft.lingering_potion.effect.water": "Frasco de agua persistente", "item.minecraft.lingering_potion.effect.water_breathing": "Poción de respiración persistente", "item.minecraft.lingering_potion.effect.weakness": "Poción de debilidad persistente", "item.minecraft.lingering_potion.effect.weaving": "Poción de costura persistente", "item.minecraft.lingering_potion.effect.wind_charged": "Pocion de Viento Cargado persistente", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON> llama", "item.minecraft.lodestone_compass": "Brújula magnetizada", "item.minecraft.mace": "<PERSON><PERSON>", "item.minecraft.magenta_bundle": "Bolsa magenta", "item.minecraft.magenta_dye": "Tinte magenta", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Crema de magma", "item.minecraft.magma_cube_spawn_egg": "Generar cubo de magma", "item.minecraft.mangrove_boat": "<PERSON><PERSON> de mangle", "item.minecraft.mangrove_chest_boat": "Bote de mangle con baúl", "item.minecraft.map": "Mapa en blanco", "item.minecraft.melon_seeds": "Semillas de patilla", "item.minecraft.melon_slice": "Rebanada de patilla", "item.minecraft.milk_bucket": "Balde con leche", "item.minecraft.minecart": "Vagón", "item.minecraft.miner_pottery_shard": "Fragmento de minero", "item.minecraft.miner_pottery_sherd": "Fragmento de cerámica de minero", "item.minecraft.mojang_banner_pattern": "Diseño de estandarte", "item.minecraft.mojang_banner_pattern.desc": "Estampado de Mojang", "item.minecraft.mojang_banner_pattern.new": "Estampado de Mojang", "item.minecraft.mooshroom_spawn_egg": "<PERSON><PERSON> mooshroom", "item.minecraft.mourner_pottery_shard": "Fragmento de doliente", "item.minecraft.mourner_pottery_sherd": "Fragmento de cerámica de doliente", "item.minecraft.mule_spawn_egg": "<PERSON>rar mula", "item.minecraft.mushroom_stew": "<PERSON><PERSON><PERSON> de hongo<PERSON>", "item.minecraft.music_disc_11": "Disco", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Disco", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Disco", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Disco", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Disco", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Disco", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Disco de música", "item.minecraft.music_disc_creator.desc": "<PERSON>-<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Disco de Toca Discos", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "Disco", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Disco de música", "item.minecraft.music_disc_lava_chicken.desc": "Hiper Pociones - <PERSON><PERSON>", "item.minecraft.music_disc_mall": "Disco", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Disco", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Disco", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Disco", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Disco de Música", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Preci<PERSON><PERSON>", "item.minecraft.music_disc_relic": "Disco", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Disco", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Disco", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Disco de musiquitas", "item.minecraft.music_disc_tears.desc": "<PERSON><PERSON>", "item.minecraft.music_disc_wait": "Disco", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Disco", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "Cordero crudo", "item.minecraft.name_tag": "Etiqueta", "item.minecraft.nautilus_shell": "Caparazón de nautilo", "item.minecraft.nether_brick": "Ladrillo del Nether", "item.minecraft.nether_star": "Estrella del Nether", "item.minecraft.nether_wart": "Verruga del Nether", "item.minecraft.netherite_axe": "<PERSON><PERSON> de netherita", "item.minecraft.netherite_boots": "Botas de netherita", "item.minecraft.netherite_chestplate": "Pechera de netherita", "item.minecraft.netherite_helmet": "Casco de netherita", "item.minecraft.netherite_hoe": "Azadón de netherita", "item.minecraft.netherite_ingot": "Lingote de netherita", "item.minecraft.netherite_leggings": "Grebas de netherita", "item.minecraft.netherite_pickaxe": "Pico de netherita", "item.minecraft.netherite_scrap": "Fragmento de netherita", "item.minecraft.netherite_shovel": "<PERSON>la de netherita", "item.minecraft.netherite_sword": "Espada de netherita", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.netherite_upgrade_smithing_template.new": "Mejora de Inframundita", "item.minecraft.oak_boat": "<PERSON><PERSON>", "item.minecraft.oak_chest_boat": "<PERSON>te de roble con baúl", "item.minecraft.ocelot_spawn_egg": "<PERSON><PERSON>", "item.minecraft.ominous_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.ominous_trial_key": "Llave de Prueba Siniestra", "item.minecraft.orange_bundle": "<PERSON><PERSON><PERSON> naranja", "item.minecraft.orange_dye": "<PERSON><PERSON>", "item.minecraft.orange_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.painting": "Cuadro", "item.minecraft.pale_oak_boat": "<PERSON><PERSON> de roble p<PERSON>", "item.minecraft.pale_oak_chest_boat": "Bote de roble pálido con baúl", "item.minecraft.panda_spawn_egg": "<PERSON>rar panda", "item.minecraft.paper": "Papel", "item.minecraft.parrot_spawn_egg": "<PERSON><PERSON> loro", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON> de phantom", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON> phantom", "item.minecraft.pig_spawn_egg": "<PERSON><PERSON> cochino", "item.minecraft.piglin_banner_pattern": "Diseño de estandarte", "item.minecraft.piglin_banner_pattern.desc": "Hocico", "item.minecraft.piglin_banner_pattern.new": "Estampado de hocico", "item.minecraft.piglin_brute_spawn_egg": "<PERSON><PERSON> piglin bruto", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON> piglin", "item.minecraft.pillager_spawn_egg": "Generar saqueador", "item.minecraft.pink_bundle": "Bolsa rosada", "item.minecraft.pink_dye": "<PERSON>te rosa", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.pitcher_plant": "Nepenthes", "item.minecraft.pitcher_pod": "<PERSON><PERSON>hes", "item.minecraft.plenty_pottery_shard": "Fragmento de abundancia", "item.minecraft.plenty_pottery_sherd": "Fragmento de cerámica de abundancia", "item.minecraft.poisonous_potato": "<PERSON>", "item.minecraft.polar_bear_spawn_egg": "Generar oso polar", "item.minecraft.popped_chorus_fruit": "<PERSON>uta de chorus horneada", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON> cruda", "item.minecraft.potato": "<PERSON>", "item.minecraft.potion": "Poción", "item.minecraft.potion.effect.awkward": "Poción rara", "item.minecraft.potion.effect.empty": "Poción no fabricable", "item.minecraft.potion.effect.fire_resistance": "Poción de resistencia al fuego", "item.minecraft.potion.effect.harming": "Poción de daño", "item.minecraft.potion.effect.healing": "Poción de curación", "item.minecraft.potion.effect.infested": "Poción de Infestación", "item.minecraft.potion.effect.invisibility": "Poción de invisibilidad", "item.minecraft.potion.effect.leaping": "Poción de salto", "item.minecraft.potion.effect.levitation": "Poción de levitación", "item.minecraft.potion.effect.luck": "Poción de suerte", "item.minecraft.potion.effect.mundane": "Poción mundana", "item.minecraft.potion.effect.night_vision": "Poción de visión nocturna", "item.minecraft.potion.effect.oozing": "Poción de exudado", "item.minecraft.potion.effect.poison": "Poción de veneno", "item.minecraft.potion.effect.regeneration": "Poción de regeneración", "item.minecraft.potion.effect.slow_falling": "Poción de caída lenta", "item.minecraft.potion.effect.slowness": "Poción de lentitud", "item.minecraft.potion.effect.strength": "Poción de fuerza", "item.minecraft.potion.effect.swiftness": "Poción de velocidad", "item.minecraft.potion.effect.thick": "Poción densa", "item.minecraft.potion.effect.turtle_master": "Poción del maestro tortuga", "item.minecraft.potion.effect.water": "Frasco de agua", "item.minecraft.potion.effect.water_breathing": "Poción de respiración", "item.minecraft.potion.effect.weakness": "Poción de debilidad", "item.minecraft.potion.effect.weaving": "Poción de Costura", "item.minecraft.potion.effect.wind_charged": "Poción de Carga de Viento", "item.minecraft.pottery_shard_archer": "Fragmento de arco", "item.minecraft.pottery_shard_arms_up": "Fragmento de manos arriba", "item.minecraft.pottery_shard_prize": "Fragmento de tesoro", "item.minecraft.pottery_shard_skull": "Fragmento de calavera", "item.minecraft.powder_snow_bucket": "Balde con nieve en polvo", "item.minecraft.prismarine_crystals": "Cristales de prismarina", "item.minecraft.prismarine_shard": "Fragmento de prismarina", "item.minecraft.prize_pottery_shard": "Fragmento de tesoro", "item.minecraft.prize_pottery_sherd": "Fragmento de cerámica de tesoro", "item.minecraft.pufferfish": "Pez globo", "item.minecraft.pufferfish_bucket": "Balde con pez globo", "item.minecraft.pufferfish_spawn_egg": "Generar pez globo", "item.minecraft.pumpkin_pie": "<PERSON><PERSON>", "item.minecraft.pumpkin_seeds": "Semillas de auyama", "item.minecraft.purple_bundle": "Bolsa morada", "item.minecraft.purple_dye": "<PERSON>te morado", "item.minecraft.purple_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.quartz": "Cuarz<PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON> crudo", "item.minecraft.rabbit_foot": "Pata de conejo", "item.minecraft.rabbit_hide": "<PERSON><PERSON>", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON>", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.raiser_armor_trim_smithing_template.new": "Diseño de armadura de elevación", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON>", "item.minecraft.raw_copper": "Cobre en bruto", "item.minecraft.raw_gold": "Oro en bruto", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON> en bruto", "item.minecraft.recovery_compass": "Brújula de recuperación", "item.minecraft.red_bundle": "Bolsa roja", "item.minecraft.red_dye": "<PERSON><PERSON> rojo", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.redstone": "Polvo de redstone", "item.minecraft.resin_brick": "<PERSON><PERSON><PERSON> resin<PERSON>", "item.minecraft.resin_clump": "Abrazadera de resina", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.rib_armor_trim_smithing_template.new": "Diseño de armadura de costillas", "item.minecraft.rotten_flesh": "<PERSON><PERSON>", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON><PERSON> crudo", "item.minecraft.salmon_bucket": "Balde con salmón", "item.minecraft.salmon_spawn_egg": "<PERSON><PERSON>", "item.minecraft.scrape_pottery_sherd": "<PERSON><PERSON><PERSON> de Cerámica", "item.minecraft.scute": "Escama", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.sentry_armor_trim_smithing_template.new": "Diseño de armadura de centinela", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.shaper_armor_trim_smithing_template.new": "Diseño de armadura de modelador", "item.minecraft.sheaf_pottery_shard": "Fragmento de trigo", "item.minecraft.sheaf_pottery_sherd": "Fragmento de cerámica de trigo", "item.minecraft.shears": "Tijeras", "item.minecraft.sheep_spawn_egg": "Generar oveja", "item.minecraft.shelter_pottery_shard": "Fragmento de refugio", "item.minecraft.shelter_pottery_sherd": "Fragmento de cerámica de refugio", "item.minecraft.shield": "Escudo", "item.minecraft.shield.black": "Escudo negro", "item.minecraft.shield.blue": "Escudo a<PERSON>l", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.cyan": "Escudo turquesa", "item.minecraft.shield.gray": "Escudo gris", "item.minecraft.shield.green": "Escudo verde", "item.minecraft.shield.light_blue": "Escudo azul cielo", "item.minecraft.shield.light_gray": "Escudo gris claro", "item.minecraft.shield.lime": "Escudo verde limón", "item.minecraft.shield.magenta": "Escudo magenta", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON><PERSON> rosa", "item.minecraft.shield.purple": "Escudo morado", "item.minecraft.shield.red": "Escudo rojo", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON><PERSON> blanco", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_shell": "Caparazón de <PERSON>ker", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON>", "item.minecraft.sign": "Cartel", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.silence_armor_trim_smithing_template.new": "Diseño de armadura de silencio", "item.minecraft.silverfish_spawn_egg": "Generar pez de plata", "item.minecraft.skeleton_horse_spawn_egg": "Generar caballo esquelé<PERSON>o", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON> es<PERSON>", "item.minecraft.skull_banner_pattern": "Diseño de estandarte", "item.minecraft.skull_banner_pattern.desc": "Estampado de cráneo", "item.minecraft.skull_banner_pattern.new": "Estampado de calavera", "item.minecraft.skull_pottery_shard": "Fragmento de calavera", "item.minecraft.skull_pottery_sherd": "Fragmento de cerámica de calavera", "item.minecraft.slime_ball": "Bola de slime", "item.minecraft.slime_spawn_egg": "Generar slime", "item.minecraft.smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.smithing_template.applies_to": "Se aplica a:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Añade un lingote o cristal", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Añade una pieza de armadura", "item.minecraft.smithing_template.armor_trim.ingredients": "Lingotes y cristales", "item.minecraft.smithing_template.ingredients": "Ingredientes:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Añade un lingote de netherita", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Equipamiento de diamante", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Añade una armadura, arma o herramienta de diamante", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Lingote de netherita", "item.minecraft.smithing_template.upgrade": "Mejora: ", "item.minecraft.sniffer_spawn_egg": "<PERSON><PERSON> sniffer", "item.minecraft.snort_pottery_shard": "Fragmento de sniffer", "item.minecraft.snort_pottery_sherd": "Fragmento de cerámica de sniffer", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.snout_armor_trim_smithing_template.new": "Diseño de armadura de hocico", "item.minecraft.snow_golem_spawn_egg": "Generar gólem de nieve", "item.minecraft.snowball": "<PERSON><PERSON> de <PERSON>eve", "item.minecraft.spectral_arrow": "Flecha espectral", "item.minecraft.spider_eye": "<PERSON><PERSON> de a<PERSON>ña", "item.minecraft.spider_spawn_egg": "<PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.spire_armor_trim_smithing_template.new": "Diseño de armadura de aguja", "item.minecraft.splash_potion": "Poción arrojadiza", "item.minecraft.splash_potion.effect.awkward": "Poción rara arrojadiza", "item.minecraft.splash_potion.effect.empty": "Poción arrojadiza no fabricable", "item.minecraft.splash_potion.effect.fire_resistance": "Poción de resistencia al fuego arrojadiza", "item.minecraft.splash_potion.effect.harming": "Poción de daño arrojadiza", "item.minecraft.splash_potion.effect.healing": "Poción de curación arrojadiza", "item.minecraft.splash_potion.effect.infested": "Poción de Infestación Arrojadiza", "item.minecraft.splash_potion.effect.invisibility": "Poción de invisibilidad arrojadiza", "item.minecraft.splash_potion.effect.leaping": "Poción de salto arrojadiza", "item.minecraft.splash_potion.effect.levitation": "Poción de levitación arrojadiza", "item.minecraft.splash_potion.effect.luck": "Poción de suerte arrojadiza", "item.minecraft.splash_potion.effect.mundane": "Poción mundana arrojadiza", "item.minecraft.splash_potion.effect.night_vision": "Poción de visión nocturna arrojadiza", "item.minecraft.splash_potion.effect.oozing": "Poción de Exudado Arrojadiza", "item.minecraft.splash_potion.effect.poison": "Poción de veneno arrojadiza", "item.minecraft.splash_potion.effect.regeneration": "Poción de regeneración arrojadiza", "item.minecraft.splash_potion.effect.slow_falling": "Poción de caída lenta arrojadiza", "item.minecraft.splash_potion.effect.slowness": "Poción de lentitud arrojadiza", "item.minecraft.splash_potion.effect.strength": "Poción de fuerza arrojadiza", "item.minecraft.splash_potion.effect.swiftness": "Poción de velocidad arrojadiza", "item.minecraft.splash_potion.effect.thick": "Poción densa arrojadiza", "item.minecraft.splash_potion.effect.turtle_master": "Poción del maestro tortuga arrojadiza", "item.minecraft.splash_potion.effect.water": "Frasco de agua arrojadizo", "item.minecraft.splash_potion.effect.water_breathing": "Poción de respiración arrojadiza", "item.minecraft.splash_potion.effect.weakness": "Poción de debilidad arrojadiza", "item.minecraft.splash_potion.effect.weaving": "Poción de Costura Arrojadiza", "item.minecraft.splash_potion.effect.wind_charged": "Poción de Carga de Viento Arrojadiza", "item.minecraft.spruce_boat": "<PERSON><PERSON> de pino", "item.minecraft.spruce_chest_boat": "Bote de pino con baúl", "item.minecraft.spyglass": "Catalejo", "item.minecraft.squid_spawn_egg": "<PERSON><PERSON> calamar", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON><PERSON> de piedra", "item.minecraft.stone_hoe": "Azadón de piedra", "item.minecraft.stone_pickaxe": "Pico de piedra", "item.minecraft.stone_shovel": "Pala de piedra", "item.minecraft.stone_sword": "Espada de piedra", "item.minecraft.stray_spawn_egg": "Generar esqueleto glacial (Stray)", "item.minecraft.strider_spawn_egg": "<PERSON><PERSON> lavagante", "item.minecraft.string": "<PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON><PERSON><PERSON>", "item.minecraft.sweet_berries": "Bayas dulces", "item.minecraft.tadpole_bucket": "Balde con renacuajo", "item.minecraft.tadpole_spawn_egg": "<PERSON><PERSON>", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.tide_armor_trim_smithing_template.new": "Diseño de armadura de marea", "item.minecraft.tipped_arrow": "Flecha con efecto", "item.minecraft.tipped_arrow.effect.awkward": "Flecha con efecto", "item.minecraft.tipped_arrow.effect.empty": "Flecha con efecto no fabricable", "item.minecraft.tipped_arrow.effect.fire_resistance": "Flecha de resistencia al fuego", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.healing": "Flecha de curación", "item.minecraft.tipped_arrow.effect.infested": "Flecha de Infestación", "item.minecraft.tipped_arrow.effect.invisibility": "Flecha de invisibilidad", "item.minecraft.tipped_arrow.effect.leaping": "Flecha de salto", "item.minecraft.tipped_arrow.effect.levitation": "Flecha de levitación", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.mundane": "Flecha con efecto", "item.minecraft.tipped_arrow.effect.night_vision": "Flecha de visión nocturna", "item.minecraft.tipped_arrow.effect.oozing": "Flecha de Exudado", "item.minecraft.tipped_arrow.effect.poison": "Flecha de veneno", "item.minecraft.tipped_arrow.effect.regeneration": "Flecha de regeneración", "item.minecraft.tipped_arrow.effect.slow_falling": "Flecha de caída lenta", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.strength": "Flecha de fuerza", "item.minecraft.tipped_arrow.effect.swiftness": "Flecha de velocidad", "item.minecraft.tipped_arrow.effect.thick": "Flecha con efecto", "item.minecraft.tipped_arrow.effect.turtle_master": "Flecha del maestro tortuga", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.water_breathing": "Flecha de respiración", "item.minecraft.tipped_arrow.effect.weakness": "Flecha de debilidad", "item.minecraft.tipped_arrow.effect.weaving": "Flecha de Costura", "item.minecraft.tipped_arrow.effect.wind_charged": "Flecha de Carga de Viento", "item.minecraft.tnt_minecart": "Vagón con dinamita", "item.minecraft.torchflower_seeds": "Semillas de florantorcha", "item.minecraft.totem_of_undying": "Tótem de la inmortalidad", "item.minecraft.trader_llama_spawn_egg": "<PERSON><PERSON> llama de comerciante", "item.minecraft.trial_key": "Llave de Prueba", "item.minecraft.trident": "Tridente", "item.minecraft.tropical_fish": "Pez tropical", "item.minecraft.tropical_fish_bucket": "Balde con pez tropical", "item.minecraft.tropical_fish_spawn_egg": "Generar pez tropical", "item.minecraft.turtle_helmet": "Caparazón de tortuga", "item.minecraft.turtle_scute": "Escama de tortuga", "item.minecraft.turtle_spawn_egg": "<PERSON><PERSON> tortuga", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.vex_armor_trim_smithing_template.new": "Diseño de armadura de Vex", "item.minecraft.vex_spawn_egg": "Generar vex", "item.minecraft.villager_spawn_egg": "<PERSON><PERSON>", "item.minecraft.vindicator_spawn_egg": "Generar vindicador", "item.minecraft.wandering_trader_spawn_egg": "Generar comerciante nómada", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.ward_armor_trim_smithing_template.new": "Diseño de armadura de Warden", "item.minecraft.warden_spawn_egg": "Generar warden", "item.minecraft.warped_fungus_on_a_stick": "Caña con hongo distorsionado", "item.minecraft.water_bucket": "Balde con agua", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Diseño de armadura de buscacaminos", "item.minecraft.wheat": "Trigo", "item.minecraft.wheat_seeds": "Semillas de trigo", "item.minecraft.white_bundle": "Bolsa blanca", "item.minecraft.white_dye": "<PERSON>te blanco", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON> de herrería", "item.minecraft.wild_armor_trim_smithing_template.new": "Diseño de armadura salvaje", "item.minecraft.wind_charge": "Carga de brisa", "item.minecraft.witch_spawn_egg": "<PERSON><PERSON> bruja", "item.minecraft.wither_skeleton_spawn_egg": "Generar es<PERSON>", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON>", "item.minecraft.wolf_armor": "Armadura para lobo", "item.minecraft.wolf_spawn_egg": "Generar lobo", "item.minecraft.wooden_axe": "<PERSON><PERSON>", "item.minecraft.wooden_hoe": "Azadón de madera", "item.minecraft.wooden_pickaxe": "Pico <PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON>", "item.minecraft.wooden_sword": "Espada de madera", "item.minecraft.writable_book": "Libro y pluma", "item.minecraft.written_book": "Libro escrito", "item.minecraft.yellow_bundle": "<PERSON><PERSON><PERSON> amarilla", "item.minecraft.yellow_dye": "<PERSON><PERSON>", "item.minecraft.yellow_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON>lin", "item.minecraft.zombie_horse_spawn_egg": "<PERSON><PERSON> caballo zombi", "item.minecraft.zombie_spawn_egg": "<PERSON><PERSON> z<PERSON>", "item.minecraft.zombie_villager_spawn_egg": "<PERSON><PERSON> al<PERSON> z<PERSON>i", "item.minecraft.zombified_piglin_spawn_egg": "Generar piglin zombificado", "item.modifiers.any": "Cuando lo equipas:", "item.modifiers.armor": "<PERSON><PERSON><PERSON> <PERSON>:", "item.modifiers.body": "<PERSON><PERSON>do está en la mano principal:", "item.modifiers.chest": "En el cuerpo:", "item.modifiers.feet": "En los pies:", "item.modifiers.hand": "<PERSON><PERSON><PERSON> se Tiene:", "item.modifiers.head": "En la cabeza:", "item.modifiers.legs": "En las piernas:", "item.modifiers.mainhand": "En la mano principal:", "item.modifiers.offhand": "En la mano secundaria:", "item.modifiers.saddle": "Cuando ensillado:", "item.nbt_tags": "NBT: %s etiqueta(s)", "item.op_block_warning.line1": "Advertencia:", "item.op_block_warning.line2": "Colocar este bloque podría resultar en ejecución de comando", "item.op_block_warning.line3": "¡No lo use a menos que conozca el contenido exacto!", "item.unbreakable": "Irrompible", "itemGroup.buildingBlocks": "Construcción", "itemGroup.coloredBlocks": "Bloques de colores", "itemGroup.combat": "Combate", "itemGroup.consumables": "Consumibles", "itemGroup.crafting": "Fabricación", "itemGroup.foodAndDrink": "<PERSON><PERSON><PERSON> y bebida", "itemGroup.functional": "Bloques funcionales", "itemGroup.hotbar": "Barras de ítems guardadas", "itemGroup.ingredients": "Ingredientes", "itemGroup.inventory": "Inventario", "itemGroup.natural": "Bloques naturales", "itemGroup.op": "Herramientas de admin.", "itemGroup.redstone": "Bloques de redstone", "itemGroup.search": "Búsqueda", "itemGroup.spawnEggs": "Generadores", "itemGroup.tools": "Herramientas", "item_modifier.unknown": "Modificador de objeto desconocido: %s", "jigsaw_block.final_state": "Se convierte en:", "jigsaw_block.generate": "Generar", "jigsaw_block.joint.aligned": "<PERSON><PERSON>", "jigsaw_block.joint.rollable": "Rotable", "jigsaw_block.joint_label": "Tipo de unión:", "jigsaw_block.keep_jigsaws": "Mantener bloques", "jigsaw_block.levels": "Niveles: %s", "jigsaw_block.name": "Nombre:", "jigsaw_block.placement_priority": "Prioridad de colocación:", "jigsaw_block.placement_priority.tooltip": "Cuando el bloque rompecabezas se conecta a una pieza, este es el orden en el que la pieza se procesa para las conexiones en estructuras más anchas.\n\nLas piezas se procesan en orden descendente, teniendo en cuenta el orden de inserción.", "jigsaw_block.pool": "Grupo objetivo:", "jigsaw_block.selection_priority": "Prioridad de selección:", "jigsaw_block.selection_priority.tooltip": "Al procesar la pieza asociada a las conexiones, este es el orden en el que el bloque rompecabezas intenta conectar con su pieza de destino.\n\nLos bloques rompecabezas se procesan en orden descendente, con un orden de inserción aleatorio.", "jigsaw_block.target": "Nombre del objetivo:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - Bloques", "jukebox_song.minecraft.cat": "C418 - G<PERSON>", "jukebox_song.minecraft.chirp": "C418 - <PERSON><PERSON><PERSON><PERSON>", "jukebox_song.minecraft.creator": "<PERSON> <PERSON><PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (Caja de Música)", "jukebox_song.minecraft.far": "C418 - <PERSON><PERSON>", "jukebox_song.minecraft.lava_chicken": "Hiper Pociones - <PERSON><PERSON>", "jukebox_song.minecraft.mall": "C418 - Centro Comercial", "jukebox_song.minecraft.mellohi": "C418 - <PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON> otro lado", "jukebox_song.minecraft.pigstep": "<PERSON> - p<PERSON><PERSON> de cerdos", "jukebox_song.minecraft.precipice": "<PERSON> - Preci<PERSON><PERSON>", "jukebox_song.minecraft.relic": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "jukebox_song.minecraft.stal": "C418 - estuvo", "jukebox_song.minecraft.strad": "C418 - <PERSON><PERSON>", "jukebox_song.minecraft.tears": "<PERSON>", "jukebox_song.minecraft.wait": "C418 - <PERSON><PERSON><PERSON>", "jukebox_song.minecraft.ward": "C418 - <PERSON><PERSON><PERSON>", "key.advancements": "Progresos", "key.attack": "Atacar/Destruir", "key.back": "Caminar hacia atrás", "key.categories.creative": "Modo creativo", "key.categories.gameplay": "Acciones del juego", "key.categories.inventory": "Inventario", "key.categories.misc": "Varios", "key.categories.movement": "Acciones de movimiento", "key.categories.multiplayer": "Multijugador", "key.categories.ui": "Interfaz del juego", "key.chat": "<PERSON><PERSON><PERSON> chat", "key.command": "<PERSON><PERSON><PERSON> comando en el chat", "key.drop": "<PERSON><PERSON><PERSON> <PERSON><PERSON> se<PERSON>", "key.forward": "Caminar hacia delante", "key.fullscreen": "Pantalla completa", "key.hotbar.1": "Acceso 1 de barra de ítems", "key.hotbar.2": "Acceso 2 de barra de ítems", "key.hotbar.3": "Acceso 3 de barra de ítems", "key.hotbar.4": "Acceso 4 de barra de ítems", "key.hotbar.5": "Acceso 5 de barra de ítems", "key.hotbar.6": "Acceso 6 de barra de ítems", "key.hotbar.7": "Acceso 7 de barra de ítems", "key.hotbar.8": "Acceso 8 de barra de ítems", "key.hotbar.9": "Acceso 9 de barra de ítems", "key.inventory": "Abrir/Cerrar inventario", "key.jump": "Saltar", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Retroceso", "key.keyboard.caps.lock": "<PERSON><PERSON><PERSON>", "key.keyboard.comma": ",", "key.keyboard.delete": "Bo<PERSON>r", "key.keyboard.down": "<PERSON><PERSON>cha a<PERSON>", "key.keyboard.end": "Fin", "key.keyboard.enter": "Intro", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "<PERSON><PERSON>o", "key.keyboard.insert": "Insertar", "key.keyboard.keypad.0": "0 (TN)", "key.keyboard.keypad.1": "1 (TN)", "key.keyboard.keypad.2": "2 (TN)", "key.keyboard.keypad.3": "3 (TN)", "key.keyboard.keypad.4": "4 (TN)", "key.keyboard.keypad.5": "5 (TN)", "key.keyboard.keypad.6": "6 (TN)", "key.keyboard.keypad.7": "7 (TN)", "key.keyboard.keypad.8": "8 (TN)", "key.keyboard.keypad.9": "9 (TN)", "key.keyboard.keypad.add": "+ (TN)", "key.keyboard.keypad.decimal": ". (TN)", "key.keyboard.keypad.divide": "/ (TN)", "key.keyboard.keypad.enter": "Intro (TN)", "key.keyboard.keypad.equal": "= (TN)", "key.keyboard.keypad.multiply": "* (TN)", "key.keyboard.keypad.subtract": "- (TN)", "key.keyboard.left": "Flecha izda.", "key.keyboard.left.alt": "Alt izdo.", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Control izdo.", "key.keyboard.left.shift": "<PERSON><PERSON>.", "key.keyboard.left.win": "Win izdo.", "key.keyboard.menu": "Menú", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Blo<PERSON>", "key.keyboard.page.down": "Av P<PERSON>g", "key.keyboard.page.up": "<PERSON>", "key.keyboard.pause": "Pausa", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON><PERSON><PERSON>", "key.keyboard.right": "<PERSON><PERSON>cha dcha.", "key.keyboard.right.alt": "Alt dcho.", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Control dcho.", "key.keyboard.right.shift": "<PERSON><PERSON> dcho.", "key.keyboard.right.win": "Win dcho.", "key.keyboard.scroll.lock": "Bloq Despl", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Espacio", "key.keyboard.tab": "Tabulador", "key.keyboard.unknown": "<PERSON>", "key.keyboard.up": "Flecha arriba", "key.keyboard.world.1": "Macro 1", "key.keyboard.world.2": "Macro 2", "key.left": "Caminar hacia la izquierda", "key.loadToolbarActivator": "<PERSON>gar barra de í<PERSON>", "key.mouse": "Botón %1$s", "key.mouse.left": "Botón izdo.", "key.mouse.middle": "Botón central", "key.mouse.right": "Botón dcho.", "key.pickItem": "Agarrar bloque", "key.playerlist": "Lista de jugadores", "key.quickActions": "Acciones rápidas", "key.right": "Caminar hacia la derecha", "key.saveToolbarActivator": "Guardar barra de ítems", "key.screenshot": "Tomar captura de pantalla", "key.smoothCamera": "Cámara cinemática", "key.sneak": "Agacharse", "key.socialInteractions": "Pantalla de interacciones sociales", "key.spectatorOutlines": "Destacar j<PERSON> (espectadores)", "key.sprint": "<PERSON><PERSON>", "key.swapOffhand": "Cambiar ítem a la mano secundaria", "key.togglePerspective": "Cambiar perspectiva", "key.use": "Usar ítem/Colocar bloque", "known_server_link.announcements": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.community": "Comunidad", "known_server_link.community_guidelines": "Normas de la Comunidad", "known_server_link.feedback": "Opiniones", "known_server_link.forums": "For<PERSON>", "known_server_link.news": "Novedades", "known_server_link.report_bug": "Informar error del Servidor", "known_server_link.status": "Estado", "known_server_link.support": "Soporte", "known_server_link.website": "Sitio Web", "lanServer.otherPlayers": "Ajustes para otros jugadores", "lanServer.port": "Número de puerto", "lanServer.port.invalid": "Puerto no válido.\nDeja la caja de edición vacía o elige un número entre 1024 y 65535.", "lanServer.port.invalid.new": "Puerto no válido.\nDeja la caja de edición vacía o introduce un número entre el %s y el %s.", "lanServer.port.unavailable": "Puerto no disponible.\nDeja la caja de edición vacía o elige un número diferente entre 1024 y 65535.", "lanServer.port.unavailable.new": "Puerto no disponible.\nDeja la caja de edición vacía o introduce un número distinto entre el %s y el %s.", "lanServer.scanning": "Buscando partidas en LAN", "lanServer.start": "Abrir mundo al LAN", "lanServer.title": "Mundo en LAN", "language.code": "spa_VE", "language.name": "Español", "language.region": "Venezuela", "lectern.take_book": "<PERSON><PERSON>rar libro", "loading.progress": "%s%%", "mco.account.privacy.info": "Leer más sobre Mojang y leyes de privacidad", "mco.account.privacy.info.button": "Más información sobre el RGPD", "mco.account.privacy.information": "Mojang implementa ciertos procedimientos para ayudar a proteger a los menores y su privacidad, que consisten en cumplir con la Ley de Protección de Privacidad Infantil en Internet (COPPA) y el Reglamento General de Protección de Datos (RGPD).\n\nDebes tener consentimiento parental antes de poder tener acceso a tu cuenta de Realms.", "mco.account.privacyinfo": "Mojang implementa ciertos procedimientos para ayudar a proteger a niños y niñas, incluyendo su privacidad, en cumplimiento con la Ley de Protección de Privacidad Infantil en Internet (COPPA) y el Reglamento General de Protección de Datos (RGPD).\n\nNecesitas autorización parental para acceder a tu cuenta de Realms.\n\nSi tienes una cuenta de Minecraft antigua (para iniciar sesión usarías un nombre de usuario, en vez de un email), tendrás que migrar tu cuenta a una de Mojang para poder acceder a Realms.", "mco.account.update": "<PERSON><PERSON><PERSON> cuenta", "mco.activity.noactivity": "Sin actividad desde hace %s día(s)", "mco.activity.title": "Actividad de jugadores", "mco.backup.button.download": "Descargar la última", "mco.backup.button.reset": "Restablecer mundo", "mco.backup.button.restore": "Restaurar", "mco.backup.button.upload": "<PERSON><PERSON> mundo", "mco.backup.changes.tooltip": "Cambios", "mco.backup.entry": "Copia de seg. (%s)", "mco.backup.entry.description": "Descripción", "mco.backup.entry.enabledPack": "Se ha activado el Paquete", "mco.backup.entry.gameDifficulty": "Dificultad del juego", "mco.backup.entry.gameMode": "Modo del juego", "mco.backup.entry.gameServerVersion": "Versión del servidor del juego", "mco.backup.entry.name": "Nombre", "mco.backup.entry.seed": "<PERSON><PERSON>", "mco.backup.entry.templateName": "Nombre de plantilla", "mco.backup.entry.undefined": "Cambio sin definir", "mco.backup.entry.uploaded": "Subido", "mco.backup.entry.worldType": "Tipo de mundo", "mco.backup.generate.world": "Generar mundo", "mco.backup.info.title": "Cambios del último respaldo", "mco.backup.narration": "Copia de seguridad de %s", "mco.backup.nobackups": "Este realm no tiene copias de seguridad.", "mco.backup.restoring": "Restaurando realm", "mco.backup.unknown": "DESCONOCIDO", "mco.brokenworld.download": "<PERSON><PERSON><PERSON>", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "Por favor, regenera el mundo o selecciona otro.", "mco.brokenworld.message.line2": "También puedes descargar el mundo para el modo un jugador.", "mco.brokenworld.minigame.title": "Este minijuego ya no es compatible", "mco.brokenworld.nonowner.error": "Por favor, espera a que el propietario restablezca el mundo", "mco.brokenworld.nonowner.title": "El mundo está desactualizado", "mco.brokenworld.play": "<PERSON><PERSON>", "mco.brokenworld.reset": "Restablecer", "mco.brokenworld.title": "Tu mundo actual ya no es compatible", "mco.client.incompatible.msg.line1": "Tu cliente no es compatible con Minecraft Realms.", "mco.client.incompatible.msg.line2": "Por favor, usa la versión más reciente de Minecraft.", "mco.client.incompatible.msg.line3": "Realms no es compatible con las versiones snapshot.", "mco.client.incompatible.title": "¡Cliente incompatible!", "mco.client.outdated.stable.version": "La versión de tu cliente (%s) no es compatible con Realms.\n\nPor favor, actualiza a la versión más reciente de Minecraft.", "mco.client.unsupported.snapshot.version": "La versión de tu cliente (%s) no es compatible con Realms.\n\nRealms no está disponible en esta versión de prueba.", "mco.compatibility.downgrade": "Desactualizar", "mco.compatibility.downgrade.description": "Este mundo se jugó por última vez en la versión %s y la tuya es %s. Desactualizar un mundo a una versión anterior podría dañarlo. No podemos garantizarte que cargue o funcione.\n\nSe guardará una copia de seguridad de tu mundo en «saves». Restaura tu mundo si es necesario.", "mco.compatibility.incompatible.popup.title": "Versión Incompatible", "mco.compatibility.incompatible.releaseType.popup.message": "El mundo al que quieres entrar es incompatible con tu versión del juego.", "mco.compatibility.incompatible.series.popup.message": "Este mundo fue jugado por última vez en la versión %s; estás en la %s.\n\nEstas series no son compatibles. Necesitas un mundo nuevo para jugar en esta versión.", "mco.compatibility.unverifiable.message": "No se ha podido verificar la versión en la que este se jugó mundo por última vez. Si el mundo se actualiza o baja de versión, se creará automáticamente una copia de seguridad que se guardará en «saves».", "mco.compatibility.unverifiable.title": "No se puede verificar la compatibilidad", "mco.compatibility.upgrade": "Actualizar", "mco.compatibility.upgrade.description": "Este mundo fue jugado por última vez en la versión %s; estás en la versión %s.\n\nSe guardará una copia de seguridad de tu mundo en \"Copias del mundo\". Por favor restaura tu mundo si es necesario.", "mco.compatibility.upgrade.friend.description": "Este mundo se jugó por última vez en la versión %s; Estás en la versión %s.\n\nSe guardará una copia de seguridad del mundo en \"Copias de seguridad del mundo\".\n\nEl dueño del Realm puede restaurar el mundo si es necesario.", "mco.compatibility.upgrade.title": "¿De verdad quieres actualizar tu mundo?", "mco.configure.current.minigame": "Actual", "mco.configure.world.activityfeed.disabled": "El feed de jugadores está desactivado temporalmente", "mco.configure.world.backup": "Copias del mundo", "mco.configure.world.buttons.activity": "Actividad de jugadores", "mco.configure.world.buttons.close": "Cerrar realm", "mco.configure.world.buttons.delete": "Bo<PERSON>r", "mco.configure.world.buttons.done": "Aceptar", "mco.configure.world.buttons.edit": "<PERSON>ar mundo", "mco.configure.world.buttons.invite": "<PERSON>vi<PERSON> jugador", "mco.configure.world.buttons.moreoptions": "Más opciones", "mco.configure.world.buttons.newworld": "Mundo nuevo", "mco.configure.world.buttons.open": "Abrir realm", "mco.configure.world.buttons.options": "<PERSON>ar mundo", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Seleccione región...", "mco.configure.world.buttons.resetworld": "Reiniciar mundo", "mco.configure.world.buttons.save": "Guardado", "mco.configure.world.buttons.settings": "Opciones", "mco.configure.world.buttons.subscription": "Suscripción", "mco.configure.world.buttons.switchminigame": "Cambiar de minijuego", "mco.configure.world.close.question.line1": "Tu realm no estará disponible.", "mco.configure.world.close.question.line2": "¿Seguro que quieres continuar?", "mco.configure.world.close.question.title": "¿Necesitas hacer cambios sin distracciones?", "mco.configure.world.closing": "Cerrando el realm...", "mco.configure.world.commandBlocks": "Bloques de comandos", "mco.configure.world.delete.button": "Borrar realm", "mco.configure.world.delete.question.line1": "Tu realm se borrará permanentemente", "mco.configure.world.delete.question.line2": "¿Seguro que quieres continuar?", "mco.configure.world.description": "Descripción del realm", "mco.configure.world.edit.slot.name": "Nombre del mundo", "mco.configure.world.edit.subscreen.adventuremap": "Algunos ajustes están desactivados por tener un mapa de aventuras", "mco.configure.world.edit.subscreen.experience": "Algunos ajustes están desactivados por tener un mapa de experiencias", "mco.configure.world.edit.subscreen.inspiration": "Algunos ajustes no están disponibles porque el mundo actual es sólo una fuente de inspiración", "mco.configure.world.forceGameMode": "<PERSON><PERSON> modo de juego", "mco.configure.world.invite.narration": "Tienes %s nueva(s) invitacione(s)", "mco.configure.world.invite.profile.name": "Nombre", "mco.configure.world.invited": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "Invitado (%s)", "mco.configure.world.invites.normal.tooltip": "Usuario normal", "mco.configure.world.invites.ops.tooltip": "Administrador", "mco.configure.world.invites.remove.tooltip": "Bo<PERSON>r", "mco.configure.world.leave.question.line1": "Si abandonas este realm no podrás verlo hasta que seas invitado de nuevo", "mco.configure.world.leave.question.line2": "¿Seguro que quieres continuar?", "mco.configure.world.loading": "Cargando Realm", "mco.configure.world.location": "Ubicación", "mco.configure.world.minigame": "Actual: %s", "mco.configure.world.name": "Nombre del realm", "mco.configure.world.opening": "Abriendo el realm...", "mco.configure.world.players.error": "No existe ningún jugador con ese nombre", "mco.configure.world.players.inviting": "Invitando...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP (JcJ)", "mco.configure.world.region_preference": "Preferencia de región", "mco.configure.world.region_preference.title": "Preferencia de región", "mco.configure.world.reset.question.line1": "Tu mundo será regenerado y el actual se perderá", "mco.configure.world.reset.question.line2": "¿Seguro que quieres continuar?", "mco.configure.world.resourcepack.question": "Necesitas un paquete de recursos personalizado para jugar en este realm.\n\n¿Quieres descargarlo y jugar?", "mco.configure.world.resourcepack.question.line1": "Este realm requiere un paquete de recursos personalizado.", "mco.configure.world.resourcepack.question.line2": "¿Quieres descargarlo e instalarlo automáticamente para jugar?", "mco.configure.world.restore.download.question.line1": "El mundo será descargado y añadido a tus mundos de un jugador.", "mco.configure.world.restore.download.question.line2": "¿Quieres continuar?", "mco.configure.world.restore.question.line1": "Tu realm será restaurado a la fecha \"%s\" (%s)", "mco.configure.world.restore.question.line2": "¿Seguro que quieres continuar?", "mco.configure.world.settings.expired": "No puedes editar los ajustes de un Reinos caducado", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot": "Mundo %s", "mco.configure.world.slot.empty": "Vacío", "mco.configure.world.slot.switch.question.line1": "Tu realm cambiará a otro mundo", "mco.configure.world.slot.switch.question.line2": "¿Seguro que quieres continuar?", "mco.configure.world.slot.tooltip": "Cambiar al mundo", "mco.configure.world.slot.tooltip.active": "Entrar", "mco.configure.world.slot.tooltip.minigame": "Cambiar a minijuego", "mco.configure.world.spawnAnimals": "Generar animales", "mco.configure.world.spawnMonsters": "Aparición de monstruos", "mco.configure.world.spawnNPCs": "Generar <PERSON>", "mco.configure.world.spawnProtection": "Protección de área inicial", "mco.configure.world.spawn_toggle.message": "Si desactivas esta opción, ELIMINARÁS TODAS las entidades de ese tipo", "mco.configure.world.spawn_toggle.message.npc": "Si desactivas esta opción, ELIMINARÁS TODAS las entidades de ese tipo, por ejemplo, los aldeanos", "mco.configure.world.spawn_toggle.title": "¡Advertencia!", "mco.configure.world.status": "Estado", "mco.configure.world.subscription.day": "día", "mco.configure.world.subscription.days": "días", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "Extender suscrip<PERSON>", "mco.configure.world.subscription.less_than_a_day": "Menos de un día", "mco.configure.world.subscription.month": "mes", "mco.configure.world.subscription.months": "meses", "mco.configure.world.subscription.recurring.daysleft": "Renovado automáticamente en", "mco.configure.world.subscription.recurring.info": "Los cambios hechos a tu suscripción de Realms, como añadir tiempo a tu suscripción o cancelar facturaciones recurrentes, no se verán reflejados hasta tu próxima fecha de facturación.", "mco.configure.world.subscription.remaining.days": "%1$s día(s)", "mco.configure.world.subscription.remaining.months": "%1$s mes(es)", "mco.configure.world.subscription.remaining.months.days": "%1$s mes(es), %2$s día(s)", "mco.configure.world.subscription.start": "Fecha de inicio", "mco.configure.world.subscription.tab": "Suscripción", "mco.configure.world.subscription.timeleft": "Tiempo restante", "mco.configure.world.subscription.title": "Tu suscripción", "mco.configure.world.subscription.unknown": "Desconocida", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON> mundo", "mco.configure.world.switch.slot.subtitle": "El mundo está vacío. <PERSON><PERSON> favor, elige qué quieres hacer", "mco.configure.world.title": "Configurar realm:", "mco.configure.world.uninvite.player": "¿Seguro que quieres cancelar la invitación de %s?", "mco.configure.world.uninvite.question": "¿Seguro que quieres cancelar la invitación de", "mco.configure.worlds.title": "Mundos", "mco.connect.authorizing": "Iniciando se<PERSON>...", "mco.connect.connecting": "Conectando al realm...", "mco.connect.failed": "Error al conectar con el realm", "mco.connect.region": "Región del servidor: %s", "mco.connect.success": "Listo", "mco.create.world": "<PERSON><PERSON><PERSON>", "mco.create.world.error": "¡Necesitas escribir un nombre!", "mco.create.world.failed": "¡Error al crear mundo!", "mco.create.world.reset.title": "Creando mundo...", "mco.create.world.skip": "<PERSON><PERSON><PERSON>", "mco.create.world.subtitle": "Si lo deseas, elige el mundo que quieras poner en tu nuevo realm", "mco.create.world.wait": "Creando el realm...", "mco.download.cancelled": "Descarga cancelada", "mco.download.confirmation.line1": "El mundo que intentas descargar supera los %s", "mco.download.confirmation.line2": "No puedes volver a subir este mundo a tu realm", "mco.download.confirmation.oversized": "El mundo que vas a descargar es más grande que %s\n\nNo podrás volver a subir este mundo a tu realm.", "mco.download.done": "<PERSON><PERSON><PERSON> completada", "mco.download.downloading": "Descargando", "mco.download.extracting": "Extrayendo", "mco.download.failed": "<PERSON><PERSON><PERSON> fallida", "mco.download.percent": "%s %%", "mco.download.preparing": "Preparando la descarga", "mco.download.resourcePack.fail": "¡Error al descargar el paquete de recursos!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Descargando la última copia del mundo", "mco.error.invalid.session.message": "Reinicia Minecraft y vuelve a intentarlo", "mco.error.invalid.session.title": "La sesión no es válida", "mco.errorMessage.6001": "Cliente desactualizado", "mco.errorMessage.6002": "Términos de servicio no aceptados", "mco.errorMessage.6003": "Límite de descarga alcanzado", "mco.errorMessage.6004": "Límite de subida alcanzado", "mco.errorMessage.6005": "Mundo bloqueado", "mco.errorMessage.6006": "Mundo desactualizado", "mco.errorMessage.6007": "El usuario está en muchos Realms", "mco.errorMessage.6008": "Nombre del realm no válido", "mco.errorMessage.6009": "Descripción del realm no válida", "mco.errorMessage.connectionFailure": "Se ha producido un error. Inténtalo más tarde.", "mco.errorMessage.generic": "Ha ocurrido un error: ", "mco.errorMessage.initialize.failed": "Error al inicializar el Realm", "mco.errorMessage.noDetails": "No se han proporcionado los detalles del error", "mco.errorMessage.realmsService": "Ha ocurrido un error (%s):", "mco.errorMessage.realmsService.configurationError": "Ha ocurrido un error inesperado al intentar editar las opciones del mundo", "mco.errorMessage.realmsService.connectivity": "No se pudo conectar al Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "No se pudo comprobar la versión compatible, obteniendo respuesta: %s", "mco.errorMessage.retry": "Reintentar operación", "mco.errorMessage.serviceBusy": "Realms está ocupado en este momento.\nIntenta conectarte en unos minutos.", "mco.gui.button": "Botón", "mco.gui.ok": "Aceptar", "mco.info": "Información!", "mco.invited.player.narration": "Jugador invitado %s", "mco.invites.button.accept": "Aceptar", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "¡No hay invitaciones pendientes!", "mco.invites.pending": "Nueva(s) invitación(es)!", "mco.invites.title": "Invitaciones pendientes", "mco.minigame.world.changeButton": "Seleccionar o<PERSON> mini<PERSON>", "mco.minigame.world.info.line1": "¡Esto cambiará temporalmente tu mundo por un minijuego!", "mco.minigame.world.info.line2": "<PERSON><PERSON> puedes regresar a tu mundo original sin perder nada.", "mco.minigame.world.noSelection": "Selecciona un minijuego", "mco.minigame.world.restore": "Terminando minijuego...", "mco.minigame.world.restore.question.line1": "El minijuego terminará y tu realm se restaurará.", "mco.minigame.world.restore.question.line2": "¿Se<PERSON>ro que quieres hacer esto?", "mco.minigame.world.selected": "Minijuego seleccionado:", "mco.minigame.world.slot.screen.title": "Cambiando mundo...", "mco.minigame.world.startButton": "Cambiar", "mco.minigame.world.starting.screen.title": "Comenzando minijuego...", "mco.minigame.world.stopButton": "Terminar minijuego", "mco.minigame.world.switch.new": "¿Seleccionar otro minijuego?", "mco.minigame.world.switch.title": "Cambiar de minijuego", "mco.minigame.world.title": "Cambiar el realm a un minijuego", "mco.news": "Novedades de Minecraft Realms", "mco.notification.dismiss": "Descar<PERSON>", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON> ya", "mco.notification.transferSubscription.message": "Las suscripciones de Realms para Java se han movido a Microsoft Store. ¡No esperes a que tu suscripción termine!\nTransfiérela ya y obtén 30 días de Realms gratis.\nVe a tu perfil en minecraft.net para transferir tu suscripción.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON><PERSON> enlace", "mco.notification.visitUrl.message.default": "Por favor, visita el siguiente enlace", "mco.onlinePlayers": "Jugadores En Línea", "mco.play.button.realm.closed": "El reino está cerrado", "mco.question": "Pregunta", "mco.reset.world.adventure": "Aventuras", "mco.reset.world.experience": "Experiencias", "mco.reset.world.generate": "Mundo nuevo", "mco.reset.world.inspiration": "Inspiración", "mco.reset.world.resetting.screen.title": "Restableciendo mundo...", "mco.reset.world.seed": "<PERSON><PERSON> (opcional)", "mco.reset.world.template": "Plantillas de mundo", "mco.reset.world.title": "Restablecer mundo", "mco.reset.world.upload": "Subir mundo", "mco.reset.world.warning": "¡Esto borrará permanentemente el mundo actual!", "mco.selectServer.buy": "¡Compra un realm!", "mco.selectServer.close": "<PERSON><PERSON><PERSON>", "mco.selectServer.closed": "Realm cerrado", "mco.selectServer.closeserver": "Cerrar realm", "mco.selectServer.configure": "Configurar realm", "mco.selectServer.configureRealm": "Configurar realm", "mco.selectServer.create": "Crear un realm", "mco.selectServer.create.subtitle": "Elige qué mundo quieres poner en tu nuevo realm", "mco.selectServer.expired": "Realm caducado", "mco.selectServer.expiredList": "Tu realm ha caducado", "mco.selectServer.expiredRenew": "<PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Suscribirse", "mco.selectServer.expiredTrial": "Tu prueba terminó", "mco.selectServer.expires.day": "Caduca en 1 día", "mco.selectServer.expires.days": "Caduca en %s días", "mco.selectServer.expires.soon": "Caduca pronto", "mco.selectServer.leave": "Salir del realm", "mco.selectServer.loading": "Cargando lista de realms", "mco.selectServer.mapOnlySupportedForVersion": "Este mapa no es compatible con la %s", "mco.selectServer.minigame": "Minijuego:", "mco.selectServer.minigameName": "Minijuego: %s", "mco.selectServer.minigameNotSupportedInVersion": "El minijuego no es jugable en la %s", "mco.selectServer.noRealms": "Parece que no tienes ningún realm. Añade alguno para jugar con tus amigos.", "mco.selectServer.note": "Nota:", "mco.selectServer.open": "Abrir realm", "mco.selectServer.openserver": "Abrir realm", "mco.selectServer.play": "<PERSON><PERSON>", "mco.selectServer.popup": "Realms es una forma simple y segura de jugar Minecraft con hasta diez amigos a la vez. ¡Soporta minijuegos y mundos personalizados! Sólo paga el propietario del realm.", "mco.selectServer.purchase": "<PERSON><PERSON><PERSON>", "mco.selectServer.trial": "¡Pruébalo!", "mco.selectServer.uninitialized": "¡Haz clic para crear un realm!", "mco.snapshot.createSnapshotPopup.text": "Está a punto de crear un Realm Snapshot gratuito que se emparejará con su suscripción a Realms pagada. Este nuevo Realm Snapshot será accesible mientras la suscripción pagada siga activa. Tu Realm pagado no será afectado", "mco.snapshot.createSnapshotPopup.title": "¿<PERSON>rear Realm Snapshot?", "mco.snapshot.creating": "Creando <PERSON> de prueba...", "mco.snapshot.description": "Emparejado con %s", "mco.snapshot.friendsRealm.downgrade": "Debes estar en la versión %s para entrar a este Realm", "mco.snapshot.friendsRealm.upgrade": "%s necesita actualizar su <PERSON> antes de que puedas jugar desde esta version", "mco.snapshot.paired": "Este Realm Snapshot está emparejado con %s", "mco.snapshot.parent.tooltip": "Utiliza la última versión de Minecraft para jugar en este Realm", "mco.snapshot.start": "Iniciar <PERSON> Snapshot gratuito", "mco.snapshot.subscription.info": "Este es un Realm Snapshot que está emparejado a la suscripción de tu Realm '%s'. Permanecerá activo mientras tu Realm emparejado esté.", "mco.snapshot.tooltip": "Utiliza Realms Snapshot para conseguir un vistazo a las próximas versiones de Minecraft, que pueden incluir nuevas características y otros cambios.\n\nPuedes encontrar tus Realms normales en la versión completa del juego.", "mco.snapshotRealmsPopup.message": "Los Realms ahora están disponibles en versiones snapshot desde la Snapshot 23w41a. ¡Todas las suscripciones a Realms vienen con un Realm Snapshot gratuito que viene por separado de tu Realm normal!", "mco.snapshotRealmsPopup.title": "Realms ahora es compatible con las versiones snapshot", "mco.snapshotRealmsPopup.urlText": "<PERSON>ende más", "mco.template.button.publisher": "Autor(a)", "mco.template.button.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.button.trailer": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.default.name": "Plantilla de mundo", "mco.template.info.tooltip": "Página web del creador", "mco.template.name": "Plantilla", "mco.template.select.failure": "No pudimos recuperar la lista del contenido de esta categoría.\nPor favor, revisa tu conexión o vuelve a intentarlo más tarde.", "mco.template.select.narrate.authors": "Autores: %s", "mco.template.select.narrate.version": "versión %s", "mco.template.select.none": "¡Uuups! Parece que la categoría no tiene contenido actualmente.\nPor favor, vuelve a mirar en un rato, o, en caso de que seas creador(a),\n%s.", "mco.template.select.none.linkTitle": "quizás podrías enviarnos alguna de tus ideas", "mco.template.title": "Plantillas de mundo", "mco.template.title.minigame": "Minijuegos", "mco.template.trailer.tooltip": "<PERSON><PERSON><PERSON><PERSON>a", "mco.terms.buttons.agree": "Acepto", "mco.terms.buttons.disagree": "No acepto", "mco.terms.sentence.1": "Acepto los términos de servicio", "mco.terms.sentence.2": "de Minecraft Realms", "mco.terms.title": "Términos de servicio de Minecraft Realms", "mco.time.daysAgo": "Hace %1$s día(s)", "mco.time.hoursAgo": "Hace %1$s hora(s)", "mco.time.minutesAgo": "Hace %1$s minuto(s)", "mco.time.now": "ahora mismo", "mco.time.secondsAgo": "Hace %1$s segundo(s)", "mco.trial.message.line1": "¿Quieres tener tu propio realm?", "mco.trial.message.line2": "¡Haz clic para más información!", "mco.upload.button.name": "Subir", "mco.upload.cancelled": "Subida cancelada", "mco.upload.close.failure": "No se puede cerrar tu realm. Por favor, inténtalo de nuevo más tarde", "mco.upload.done": "Subida finalizada", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "¡Subida fallida! (%s)", "mco.upload.failed.too_big.description": "El mundo seleccionado es demasiado grande. El tamaño máximo permitido es %s.", "mco.upload.failed.too_big.title": "El mundo es demasiado grande", "mco.upload.hardcore": "¡No se pueden subir mundos extremos!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Preparando tu mundo", "mco.upload.select.world.none": "¡No se han encontrado mundos de un jugador!", "mco.upload.select.world.subtitle": "Por favor, selecciona un mundo de un jugador para subirlo", "mco.upload.select.world.title": "Subir mundo", "mco.upload.size.failure.line1": "¡\"%s\" es demasiado grande!", "mco.upload.size.failure.line2": "%s, de un máximo permitido de %s.", "mco.upload.uploading": "Subiendo \"%s\"", "mco.upload.verifying": "Verificando tu mundo", "mco.version": "Versión: %s", "mco.warning": "¡Advertencia!", "mco.worldSlot.minigame": "Minijuego", "menu.custom_options": "Opciones personalizadas...", "menu.custom_options.title": "Opciones personalizadas", "menu.custom_options.tooltip": "Nota: Las opciones personalizadas las proporcionan servidores de terceros y/o contenido.\n¡Ten cuidado!", "menu.custom_screen_info.button_narration": "Esto es una pantalla personalizada. Más información.", "menu.custom_screen_info.contents": "El contenido de esta pantalla está controlado por servidores y mapas de terceros que no son propiedad, están operados ni supervisados por Mojang Studios ni por Microsoft.\n\n¡Mano con cuidado! Tenga siempre cuidado al seguir los enlaces y nunca regale su información personal, incluidos los datos de inicio de sesión.\n\nSi esta pantalla le impide jugar, también puede desconectarse del servidor actual utilizando el botón de abajo.", "menu.custom_screen_info.disconnect": "Pantalla personalizada desconectada", "menu.custom_screen_info.title": "Acerca de las pantallas personalizadas", "menu.custom_screen_info.tooltip": "Esto es una pantalla personalizada. Haga clic aquí para más información.", "menu.disconnect": "Desconectarte", "menu.feedback": "Comentario...", "menu.feedback.title": "Comentario", "menu.game": "Menú", "menu.modded": " (Con mods)", "menu.multiplayer": "Multijugador", "menu.online": "Minecraft Realms", "menu.options": "Opciones...", "menu.paused": "Juego en pausa", "menu.playdemo": "<PERSON><PERSON> al mundo demo", "menu.playerReporting": "<PERSON>ar jugador", "menu.preparingSpawn": "Preparando área de aparición: %s%%", "menu.quick_actions": "Acciones rápidas...", "menu.quick_actions.title": "Acciones rápidas", "menu.quit": "Cerrar Minecraft", "menu.reportBugs": "Reportar error", "menu.resetdemo": "Reiniciar el mundo demo", "menu.returnToGame": "Volver al juego", "menu.returnToMenu": "Guardar y salir al menú principal", "menu.savingChunks": "Guardando chunks", "menu.savingLevel": "Guardando...", "menu.sendFeedback": "Enviar opinión", "menu.server_links": "Enlaces del servidor...", "menu.server_links.title": "Enlaces del servidor", "menu.shareToLan": "Abrir en LAN", "menu.singleplayer": "Un jugador", "menu.working": "Procesando...", "merchant.deprecated": "Los aldeanos se reabastecen dos veces al día.", "merchant.level.1": "Novato", "merchant.level.2": "Aprendiz", "merchant.level.3": "Cualificado", "merchant.level.4": "Experto", "merchant.level.5": "Maestro", "merchant.title": "%s - %s", "merchant.trades": "Intercambios", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "%1$s para bajarte", "multiplayer.applyingPack": "Aplicando paquete de recursos", "multiplayer.confirm_command.parse_errors": "Estás intentando ejecutar un comando desconocido o inválido.\n¿Seguro que quieres hacer eso?\nComando: %s", "multiplayer.confirm_command.permissions_required": "Estás intentando ejecutar un comando que necesita permisos elevados.\nEsto podría afectar negativamente a tu juego.\n¿Seguro que quieres hacer esto?\nComando: %s", "multiplayer.confirm_command.title": "Confirmar ejecución del comando", "multiplayer.disconnect.authservers_down": "Los servidores de autenticación no funcionan. Por favor, inténtalo más tarde y disculpa las molestias.", "multiplayer.disconnect.bad_chat_index": "Mensaje de chat perdido o reordenado desde el servidor", "multiplayer.disconnect.banned": "Te banearon en este servidor", "multiplayer.disconnect.banned.expiration": "\n<PERSON>cha de desbaneo: %s", "multiplayer.disconnect.banned.reason": "Estás baneado/a de este servidor.\nMotivo: %s", "multiplayer.disconnect.banned_ip.expiration": "\n<PERSON>cha de desbaneo: %s", "multiplayer.disconnect.banned_ip.reason": "Tu IP está baneada de este servidor.\nMotivo: %s", "multiplayer.disconnect.chat_validation_failed": "Error al validar el mensaje de chat", "multiplayer.disconnect.duplicate_login": "Te conectaste desde otra ubicación", "multiplayer.disconnect.expired_public_key": "La clave pública de perfil ha caducado. Comprueba que la hora del sistema esté sincronizada y reinicia tu juego.", "multiplayer.disconnect.flying": "No está permitido volar en este servidor", "multiplayer.disconnect.generic": "Desconectado/a", "multiplayer.disconnect.idling": "¡Ausente demasiado tiempo!", "multiplayer.disconnect.illegal_characters": "Caracteres no permitidos en el chat", "multiplayer.disconnect.incompatible": "¡Cliente incompatible! Por favor, usa %s", "multiplayer.disconnect.invalid_entity_attacked": "Se ha intentado atacar a una entidad no válida", "multiplayer.disconnect.invalid_packet": "El servidor envió un paquete invalido", "multiplayer.disconnect.invalid_player_data": "Datos del jugador inválidos", "multiplayer.disconnect.invalid_player_movement": "Se ha detectado un movimiento de jugador no válido", "multiplayer.disconnect.invalid_public_key_signature": "Firma de la clave pública del perfil no válida.\nPrueba reiniciando el juego.", "multiplayer.disconnect.invalid_public_key_signature.new": "Firma de la llave publica invalida.\nIntenta reiniciar el juego.", "multiplayer.disconnect.invalid_vehicle_movement": "Se ha detectado un movimiento de vehículo no válido", "multiplayer.disconnect.ip_banned": "Tu IP está baneada en este servidor", "multiplayer.disconnect.kicked": "Expulsado/a por un administrador", "multiplayer.disconnect.missing_tags": "El conjunto de etiquetas del servidor está incompleto.\nPor favor, contacta con algún admin del servidor.", "multiplayer.disconnect.name_taken": "Ya hay alguien conectado con tu nombre", "multiplayer.disconnect.not_whitelisted": "¡No estás en la lista blanca del servidor!", "multiplayer.disconnect.out_of_order_chat": "Se ha detectado un paquete de chat fuera de servicio. ¿Cambiaste la hora de tu sistema?", "multiplayer.disconnect.outdated_client": "¡Cliente incompatible! Por favor, usa la versión %s", "multiplayer.disconnect.outdated_server": "¡Cliente incompatible! Por favor, usa la versión %s", "multiplayer.disconnect.server_full": "¡El servidor está lleno!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON><PERSON> cer<PERSON>", "multiplayer.disconnect.slow_login": "La conexión ha tardado demasiado", "multiplayer.disconnect.too_many_pending_chats": "Demasiados mensajes de chat sin confirmar", "multiplayer.disconnect.transfers_disabled": "El servidor no acepta transferencias", "multiplayer.disconnect.unexpected_query_response": "Se enviaron datos desconocidos desde el cliente", "multiplayer.disconnect.unsigned_chat": "Se ha recibido un paquete de chat sin firma o con una firma no válida.", "multiplayer.disconnect.unverified_username": "¡No se pudo verificar tu nombre de usuario!", "multiplayer.downloadingStats": "Obteniendo estadísticas...", "multiplayer.downloadingTerrain": "Cargando el terreno...", "multiplayer.lan.server_found": "Nuevo servidor encontrado: %s", "multiplayer.message_not_delivered": "No se pudo enviar el mensaje, revisa los registros del servidor: %s", "multiplayer.player.joined": "%s se conectó", "multiplayer.player.joined.renamed": "%s (antes conocido como %s) se conectó", "multiplayer.player.left": "%s se desconectó", "multiplayer.player.list.hp": "%s PS", "multiplayer.player.list.narration": "Jugadores en línea: %s", "multiplayer.requiredTexturePrompt.disconnect": "Este servidor requiere un paquete de recursos personalizado", "multiplayer.requiredTexturePrompt.line1": "Este servidor recomienda el uso de un paquete de recursos personalizado.", "multiplayer.requiredTexturePrompt.line2": "Si rechazas el paquete de recursos personalizado serás desconectado del servidor.", "multiplayer.socialInteractions.not_available": "Las interacciones sociales solamente están disponibles en los mundos multijugador", "multiplayer.status.and_more": "... y %s más ...", "multiplayer.status.cancelled": "Operación cancelada", "multiplayer.status.cannot_connect": "Error al conectar", "multiplayer.status.cannot_resolve": "Error al resolver el nombre del host", "multiplayer.status.finished": "Conexión finalizada", "multiplayer.status.incompatible": "¡Versión incompatible!", "multiplayer.status.motd.narration": "Mensaje del día: %s", "multiplayer.status.no_connection": "(sin conexión)", "multiplayer.status.old": "Desactualizado", "multiplayer.status.online": "En línea", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Latencia de %s milisegundos", "multiplayer.status.pinging": "Conectando...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s de %s jugadores en línea", "multiplayer.status.quitting": "Desconectando", "multiplayer.status.request_handled": "Se recibió la solicitud de estado", "multiplayer.status.unknown": "¿¿??", "multiplayer.status.unrequested": "Recibido un estado no solicitado", "multiplayer.status.version.narration": "Versión del servidor: %s", "multiplayer.stopSleeping": "Levantarte de la cama", "multiplayer.texturePrompt.failure.line1": "El paquete de recursos no pudo ser aplicado", "multiplayer.texturePrompt.failure.line2": "Cualquier funcionalidad que requiera recursos personalizados puede no funcionar como se espera", "multiplayer.texturePrompt.line1": "El servidor recomienda usar su propio paquete de recursos.", "multiplayer.texturePrompt.line2": "¿Quieres descargarlo e instalarlo automágicamente?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMensaje del servidor:\n%s", "multiplayer.title": "Multijugador", "multiplayer.unsecureserver.toast": "Los mensajes enviados al servidor pueden ser modificados y no reflejar el mensaje original", "multiplayer.unsecureserver.toast.title": "Los mensajes del chat no pueden ser verificados", "multiplayerWarning.check": "No mostrar este mensaje de nuevo", "multiplayerWarning.header": "Atención: Juego en línea de terceros", "multiplayerWarning.message": "Atención: El modo multijugador está formado por servidores de terceros que no son propiedad, ni están administrados o supervisados por Mojang Studios o Microsoft. Mientras juegues en línea, puedes estar expuesto a mensajes de chat inapropiados u otro tipo de contenido creado por la comunidad, por lo que podría no ser adecuado para todos los usuarios.", "music.game.a_familiar_room": "<PERSON><PERSON> - Una habitación familiar", "music.game.an_ordinary_day": "<PERSON><PERSON> - Un día cualquiera", "music.game.ancestry": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "music.game.below_and_above": "<PERSON> y arriba", "music.game.broken_clocks": "<PERSON> - <PERSON><PERSON><PERSON><PERSON> rot<PERSON>", "music.game.bromeliad": "<PERSON>", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Recuerdos reconfortantes", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> en el viento", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - El fin", "music.game.endless": "<PERSON> - Endless (Sin fin)", "music.game.featherfall": "<PERSON> - Featherfall (Caída de pluma)", "music.game.fireflies": "<PERSON> - Fireflies (Luciérnagas)", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream (<PERSON><PERSON> flotante)", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Amatista infinita", "music.game.key": "C418 - <PERSON><PERSON><PERSON>", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - De <PERSON>quierda a derecha", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - Un día más", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON><PERSON>", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutación", "narration.button": "Botón: %s", "narration.button.usage.focused": "Presiona Enter para activar", "narration.button.usage.hovered": "Clic izquierdo para activar", "narration.checkbox": "Casilla: %s", "narration.checkbox.usage.focused": "Presiona Enter para alternar", "narration.checkbox.usage.hovered": "Clic izquierdo para alternar", "narration.component_list.usage": "Presiona el tabulador para navegar al siguiente elemento", "narration.cycle_button.usage.focused": "Presiona Enter para cambiar a %s", "narration.cycle_button.usage.hovered": "Clic izquierdo para cambiar a %s", "narration.edit_box": "Editar casilla: %s", "narration.item": "Artículo: %s", "narration.recipe": "Receta para %s", "narration.recipe.usage": "Clic izquierdo para seleccionar", "narration.recipe.usage.more": "Clic derecho para mostrar más recetas", "narration.selection.usage": "Presiona las flechas de arriba o abajo para moverte a otra entidad", "narration.slider.usage.focused": "Presiona las flechas de izquierda o derecha para cambiar el valor", "narration.slider.usage.hovered": "Arrastra el deslizador para cambiar el valor", "narration.suggestion": "Seleccionaste la sugerencia %s de %s: %s", "narration.suggestion.tooltip": "Seleccionaste la sugerencia %s de %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Pulsa Tabulador para pasar a la siguiente sugerencia", "narration.suggestion.usage.cycle.hidable": "Presiona Tabulador para pasar a la siguiente sugerencia, o Escapar para salir de las sugerencias", "narration.suggestion.usage.fill.fixed": "Pulsa Tabulador para usar la sugerencia", "narration.suggestion.usage.fill.hidable": "Pulsa Tabulador para usar la sugerencia, o Escapar para salir de las sugerencias", "narration.tab_navigation.usage": "Presiona Ctrl y Tab para cambiar entre pestañas", "narrator.button.accessibility": "Accesibilidad", "narrator.button.difficulty_lock": "Bloquear dificultad", "narrator.button.difficulty_lock.locked": "Dificultad bloqueada", "narrator.button.difficulty_lock.unlocked": "Dificultad no bloqueada", "narrator.button.language": "Idioma", "narrator.controls.bound": "La acción %s está asignada al %s", "narrator.controls.reset": "Reiniciar el botón de la acción %s", "narrator.controls.unbound": "La acción %s no tiene asignada ningún botón", "narrator.joining": "Entrando", "narrator.loading": "Cargando: %s", "narrator.loading.done": "Aceptar", "narrator.position.list": "Fila(s) de la lista seleccionada %s de %s", "narrator.position.object_list": "Elemento(s) de la fila seleccionada %s de %s", "narrator.position.screen": "Elemento %s de %s en la pantalla", "narrator.position.tab": "Pestaña numero %s de %s", "narrator.ready_to_play": "Listo para jugar! :)", "narrator.screen.title": "<PERSON><PERSON> principal", "narrator.screen.usage": "Usa el cursor del mouse o el tabulador para seleccionar elemento", "narrator.select": "Seleccionado: %s", "narrator.select.world": "Mundo \"%s\" seleccionado, jugado por última vez: %s, %s, %s, con la versión: %s", "narrator.select.world_info": " Seleccionado %s, última vez jugado: %s, %s", "narrator.toast.disabled": "Narración deshabilitada", "narrator.toast.enabled": "Narración habilitada", "optimizeWorld.confirm.description": "Esto tratará de optimizar tu mundo asegurándose de que todos los datos se guarden en el formato más reciente. Dependiendo del tamaño de tu mundo, podría tardar mucho tiempo. Una vez terminado, tu mundo debería funcionar más rápido, pero, a su vez, dejará de ser compatible con versiones antiguas. ¿Quieres continuar?", "optimizeWorld.confirm.proceed": "Crear copia de seguridad y optimizar", "optimizeWorld.confirm.title": "<PERSON>timi<PERSON> mundo", "optimizeWorld.info.converted": "Chunks actualizados: %s", "optimizeWorld.info.skipped": "Chunks omitidos: %s", "optimizeWorld.info.total": "Chunks en total: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Calculando chunks...", "optimizeWorld.stage.failed": "¡Error! :(", "optimizeWorld.stage.finished": "Finalizando...", "optimizeWorld.stage.finished.chunks": "Finalizando mejora de chunks...", "optimizeWorld.stage.finished.entities": "Finalizando mejora de entidades...", "optimizeWorld.stage.finished.poi": "Finalizando mejora de puntos de interés...", "optimizeWorld.stage.upgrading": "Mejorando todos los chunks...", "optimizeWorld.stage.upgrading.chunks": "Mejorando todos los chunks...", "optimizeWorld.stage.upgrading.entities": "Mejorando todas las entidades...", "optimizeWorld.stage.upgrading.poi": "Mejorando todos los puntos de interés...", "optimizeWorld.title": "Optimizando el mundo \"%s\"", "options.accessibility": "Opciones de accesibilidad...", "options.accessibility.high_contrast": "Contraste alto", "options.accessibility.high_contrast.error.tooltip": "El paquete de recursos de alto contraste no está disponible", "options.accessibility.high_contrast.tooltip": "Aumenta el contraste de los elementos de la interfaz", "options.accessibility.high_contrast_block_outline": "Alto contraste en contorno del bloque", "options.accessibility.high_contrast_block_outline.tooltip": "Mejora el contraste del borde del bloque apuntado.", "options.accessibility.link": "Guía de accesibilidad", "options.accessibility.menu_background_blurriness": "Desenfoque del fondo de los menús", "options.accessibility.menu_background_blurriness.tooltip": "Cambia el desenfoque de los fondos de los menús", "options.accessibility.narrator_hotkey": "Atajo al narrador", "options.accessibility.narrator_hotkey.mac.tooltip": "Permite activar o desactivar el narrador con Ctrl+B", "options.accessibility.narrator_hotkey.tooltip": "Permite activar o desactivar el narrador con 'Ctrl+B'", "options.accessibility.panorama_speed": "Rapidez del panorama", "options.accessibility.text_background": "Fondo de texto", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "En todo", "options.accessibility.text_background_opacity": "Opacidad de fondo", "options.accessibility.title": "Opciones de accesibilidad", "options.allowServerListing": "<PERSON><PERSON><PERSON> de servidor", "options.allowServerListing.tooltip": "Los servidores pueden incluir jugadores en línea como parte de su estado público.\nSi desactivas esta opción tu nombre no aparecerá en dichas listas.", "options.ao": "Iluminación suave", "options.ao.max": "Máxima", "options.ao.min": "<PERSON><PERSON><PERSON>", "options.ao.off": "NO", "options.attack.crosshair": "Mira", "options.attack.hotbar": "Barr<PERSON>", "options.attackIndicator": "Indicador de ataque", "options.audioDevice": "Dispositivo", "options.audioDevice.default": "Por defecto del sistema", "options.autoJump": "Salto automático", "options.autoSuggestCommands": "Sugerir comandos", "options.autosaveIndicator": "Indicador de autoguardado", "options.biomeBlendRadius": "Transición de biomas", "options.biomeBlendRadius.1": "NO (más rápido)", "options.biomeBlendRadius.11": "11x11 (extremo)", "options.biomeBlendRadius.13": "13x13 (exagerado)", "options.biomeBlendRadius.15": "15x15 (máximo)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (normal)", "options.biomeBlendRadius.7": "7x7 (de<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.9": "9x9 (muy de<PERSON><PERSON><PERSON>)", "options.chat": "A<PERSON><PERSON><PERSON> de chat...", "options.chat.color": "Colores", "options.chat.delay": "Retraso del chat: %s segundos", "options.chat.delay_none": "Ra<PERSON><PERSON><PERSON> chat: NO", "options.chat.height.focused": "Altura máx. (abierto)", "options.chat.height.unfocused": "Altura máx. (cerrado)", "options.chat.line_spacing": "Interlineado", "options.chat.links": "Enlaces web", "options.chat.links.prompt": "Avisar al abrir enlaces", "options.chat.opacity": "Opacidad de chat", "options.chat.scale": "Tamaño del texto", "options.chat.title": "Configuración del chat", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "Visible", "options.chat.visibility.hidden": "Oculto", "options.chat.visibility.system": "<PERSON><PERSON><PERSON> comandos", "options.chat.width": "<PERSON><PERSON>", "options.chunks": "%s chunks", "options.clouds.fancy": "Detalladas", "options.clouds.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.controls": "Controles...", "options.credits_and_attribution": "Créditos y atribuciones...", "options.damageTiltStrength": "Sacudida al recibir daño", "options.damageTiltStrength.tooltip": "La sacudida de la cámara cuando te golpean.", "options.darkMojangStudiosBackgroundColor": "Logotipo monocromático", "options.darkMojangStudiosBackgroundColor.tooltip": "Cambia el fondo de la pantalla de carga de Mojang Studios a negro.", "options.darknessEffectScale": "Pulsaciones oscuras", "options.darknessEffectScale.tooltip": "Controla la pulsación del efecto de oscuridad cuando un warden o chillador de sculk te lo provoca.", "options.difficulty": "Dificultad", "options.difficulty.easy": "F<PERSON><PERSON>l", "options.difficulty.easy.info": "Las criaturas agresivas aparecen, pero quitan menos daño. La barra de hambre estando vacía solo te quita hasta 5 corazones.", "options.difficulty.hard": "Dif<PERSON><PERSON>l", "options.difficulty.hard.info": "Aparecen criaturas hostiles más fuertes. La barra de hambre se agota, pudiendo reducir toda la salud.", "options.difficulty.hardcore": "Extremo", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Las criaturas agresivas aparecen, y tienen un daño estándar. <PERSON>uando la hambre baja por completo, empezarás a recibir daño hasta quedarte a medio corazón.", "options.difficulty.online": "Dificultad del servidor", "options.difficulty.peaceful": "Pacífico", "options.difficulty.peaceful.info": "No aparecen criaturas hostiles, y solo aparecen algunas criaturas neutrales. La barra de hambre no se agota y la salud se regenera con el tiempo.", "options.directionalAudio": "Audio direccional", "options.directionalAudio.off.tooltip": "Sonido estereo clásico", "options.directionalAudio.on.tooltip": "Usa audio direccional basado en HRTF para mejorar la simulación de sonido 3D. Se requiere hardware de audio compatible con HRTF, y se experimenta mejor con audífonos.", "options.discrete_mouse_scroll": "<PERSON><PERSON> discreto", "options.entityDistanceScaling": "Distancia de entidades", "options.entityShadows": "Sombras de entidades", "options.font": "Ajustes de fuente...", "options.font.title": "Ajustes de fuente", "options.forceUnicodeFont": "Forzar fuente unicode", "options.fov": "Campo de visión", "options.fov.max": "Quake Pro", "options.fov.min": "Normal", "options.fovEffectScale": "Efectos de FOV", "options.fovEffectScale.tooltip": "Controla la variación del campo de visión con los efectos del juego.", "options.framerate": "%s", "options.framerateLimit": "FPS máximos", "options.framerateLimit.max": "Sin límite", "options.fullscreen": "Pantalla completa", "options.fullscreen.current": "Actual", "options.fullscreen.entry": "%sx%s a %shz (%s bits)", "options.fullscreen.resolution": "Resolución de pantalla completa", "options.fullscreen.unavailable": "No disponible", "options.gamma": "<PERSON><PERSON><PERSON>", "options.gamma.default": "Por defecto", "options.gamma.max": "<PERSON><PERSON><PERSON>", "options.gamma.min": "Oscuro", "options.generic_value": "%s: %s", "options.glintSpeed": "Velocidad de destello", "options.glintSpeed.tooltip": "Controla que tan rápido es el brillo del destello visual va en los objetos encantados.", "options.glintStrength": "La Intensidad del destello", "options.glintStrength.tooltip": "Controla que tan transparente es el destello visual en los objetos encantados.", "options.graphics": "Grá<PERSON><PERSON>", "options.graphics.fabulous": "¡Fabulosos!", "options.graphics.fabulous.tooltip": "Los gráficos %s usan sombreadores de pantalla para renderizar las precipitaciones, las nubes y las partículas al mirarlas a través de bloques translúcidos o de agua.\nPuede afectar gravemente al rendimiento en dispositivos portátiles y al utilizar monitores 4K.", "options.graphics.fancy": "Detallados", "options.graphics.fancy.tooltip": "Los gráficos detallados equilibran el rendimiento y la calidad en la mayoría de dispositivos.\nLas precipitaciones, las nubes y las partículas puede que no se muestren al mirarlas a través de bloques translúcidos o de agua.", "options.graphics.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "Los gráficos rápidos reducen la cantidad de lluvia y nieve visible.\nLos efectos de transparencia se desactivan para bloques como las hojas de árboles.", "options.graphics.warning.accept": "Continuar sin soporte", "options.graphics.warning.cancel": "<PERSON><PERSON><PERSON>", "options.graphics.warning.message": "Detectamos que tu dispositivo gráfico no es compatible con la opción de gráficos %s.\n\n<PERSON><PERSON><PERSON> ignorar este mensaje y continuar, pero no ofreceremos soporte para tu dispositivo mientras uses los gráficos %s.", "options.graphics.warning.renderer": "Motor de renderizado detectado: [%s]", "options.graphics.warning.title": "Dispositivo gráfico no soportado", "options.graphics.warning.vendor": "Fabricante detectado: [%s]", "options.graphics.warning.version": "Versión de OpenGL detectada: [%s]", "options.guiScale": "Tamaño de interfaz", "options.guiScale.auto": "Auto.", "options.hidden": "Oculto", "options.hideLightningFlashes": "Esconder relámpago<PERSON>", "options.hideLightningFlashes.tooltip": "Evita que los rayos hagan que se ilumine el cielo. Aun así, se seguirán viendo los rayos.", "options.hideMatchedNames": "Ocultar nombres iguales", "options.hideMatchedNames.tooltip": "Los servidores de terceros pueden enviar mensajes de chat en formatos no estándar. Con esta opción activada, los jugadores ocultos serán emparejados en función de los nombres de los remitentes del chat.", "options.hideSplashTexts": "Ocultar los textos de bienvenida", "options.hideSplashTexts.tooltip": "Oculta los textos parpadeantes amarillos que aparecen en el menú principal.", "options.inactivityFpsLimit": "Reducir FPS", "options.inactivityFpsLimit.afk": "Al estar inactivo", "options.inactivityFpsLimit.afk.tooltip": "Limita los FPS tras pasar un corto tiempo sin actividad. El limite aumentará a 10 FPS después de pasar 10 minutos sin actividad.", "options.inactivityFpsLimit.minimized": "<PERSON><PERSON><PERSON><PERSON>", "options.inactivityFpsLimit.minimized.tooltip": "Limitar los cuadros por segundo cuando el juegos se encuentra minimizado.", "options.invertMouse": "Invertir mouse", "options.japaneseGlyphVariants": "Variantes de glifos japoneses", "options.japaneseGlyphVariants.tooltip": "Usa variantes japonesas de los caracteres CJK en la fuente predeterminada", "options.key.hold": "<PERSON><PERSON><PERSON>", "options.key.toggle": "Alternar", "options.language": "Idioma...", "options.language.title": "Idioma", "options.languageAccuracyWarning": "(Algunas traducciones pueden contener errores)", "options.languageWarning": "Algunas traducciones pueden contener errores", "options.mainHand": "<PERSON><PERSON> principal", "options.mainHand.left": "Iz<PERSON>erda", "options.mainHand.right": "Derecha", "options.mipmapLevels": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.cape": "Capa", "options.modelPart.hat": "Sombrero", "options.modelPart.jacket": "<PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "Pierna izquierda del pantalón", "options.modelPart.left_sleeve": "Manga izquierda", "options.modelPart.right_pants_leg": "<PERSON>na derecha del pantalón", "options.modelPart.right_sleeve": "Manga derecha", "options.mouseWheelSensitivity": "Sensibilidad de scroll", "options.mouse_settings": "Opciones de mouse...", "options.mouse_settings.title": "Opciones de mouse", "options.multiplayer.title": "Multijugador...", "options.multiplier": "%sx", "options.music_frequency": "Frecuencia de la música", "options.music_frequency.constant": "Continuo", "options.music_frequency.default": "Por defecto", "options.music_frequency.frequent": "Frecuente", "options.music_frequency.tooltip": "Cambia la frecuencia con la que se reproduce música durante una partida.", "options.narrator": "Narración", "options.narrator.all": "<PERSON><PERSON><PERSON> todo", "options.narrator.chat": "<PERSON><PERSON><PERSON> chat", "options.narrator.notavailable": "No disponible", "options.narrator.off": "NO", "options.narrator.system": "<PERSON><PERSON><PERSON> sistema", "options.notifications.display_time": "Duración de las notificaciones", "options.notifications.display_time.tooltip": "Afecta al tiempo que todas las notificaciones permanecen visibles en la pantalla.", "options.off": "NO", "options.off.composed": "%s: NO", "options.on": "SÍ", "options.on.composed": "%s: SÍ", "options.online": "En línea...", "options.online.title": "Opciones en línea", "options.onlyShowSecureChat": "Solo mensajes seguros", "options.onlyShowSecureChat.tooltip": "Solo mostrar mensajes de jugadores cuya pertenencia del mensaje haya sido verificada y no modificada.", "options.operatorItemsTab": "Pestaña de administrador", "options.particles": "Partículas", "options.particles.all": "<PERSON><PERSON>", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "Mínimas", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Generar chunks", "options.prioritizeChunkUpdates.byPlayer": "Por acción", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Los chunks cercanos siempre serán compilados inmediatamente. Esto podría afectar al rendimiento del juego al colocar o destruir bloques.", "options.prioritizeChunkUpdates.nearby": "Siempre", "options.prioritizeChunkUpdates.nearby.tooltip": "Los chunks cercanos siempre serán compilados inmediatamente. Esto podría afectar al rendimiento del juego al colocar o destruir bloques.", "options.prioritizeChunkUpdates.none": "En paralelo", "options.prioritizeChunkUpdates.none.tooltip": "Los chunks cercanos se compilan en hilos paralelos. Esto podría resultar en breves agujeros visuales al destruir bloques.", "options.rawMouseInput": "Entrada directa", "options.realmsNotifications": "Noticias e invitaciones de Realms", "options.realmsNotifications.tooltip": "Obtiene noticias e invitaciones de Realms en la pantalla de título y muestra su ícono respectivo en el botón Realms.", "options.reducedDebugInfo": "Reducir datos de F3", "options.renderClouds": "Nubes", "options.renderCloudsDistance": "Distancia de renderizado de las nubes", "options.renderDistance": "Renderizado", "options.resourcepack": "Paquetes de recursos...", "options.rotateWithMinecart": "G<PERSON><PERSON> con Vagón", "options.rotateWithMinecart.tooltip": "Controla la vista del jugador si la cámara debería rotar al montar en un Vagón. Solo disponible en mundos con el ajuste experimental «Mejoras de Vagones» activado.", "options.screenEffectScale": "Efectos de distorsión", "options.screenEffectScale.tooltip": "Modifica la intensidad de los efectos de distorsión que causan los portales del Nether y las náuseas.\nSi el valor es bajo, el efecto de náuseas será sustituido por un coloreado verde.", "options.sensitivity": "Sensibilidad", "options.sensitivity.max": "¡¡¡HIPERRÁPIDO!!!", "options.sensitivity.min": "*bostezo*", "options.showNowPlayingToast": "Aviso de Música", "options.showNowPlayingToast.tooltip": "Muestra un aviso cada vez que una canción empiezen a sonar. El aviso\nsiempre estará en el menú de pausa siempre y cuando este soñando una \ncanción.", "options.showSubtitles": "<PERSON>t<PERSON><PERSON><PERSON>", "options.simulationDistance": "Simulación", "options.skinCustomisation": "Mi aspecto...", "options.skinCustomisation.title": "Mi aspecto", "options.sounds": "Música y sonido...", "options.sounds.title": "Opciones de música y sonido", "options.telemetry": "Datos de telemetría...", "options.telemetry.button": "Recolección de datos", "options.telemetry.button.tooltip": "«%s» incluye solo la información necesaria.\n«%s» incluye también información opcional.", "options.telemetry.disabled": "Telemetría desactivada.", "options.telemetry.state.all": "Toda", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON>", "options.telemetry.state.none": "Ninguna", "options.title": "Opciones", "options.touchscreen": "<PERSON><PERSON><PERSON> t<PERSON>ctil", "options.video": "Gráficos...", "options.videoTitle": "Opciones de gráficos", "options.viewBobbing": "Movimiento de la visión", "options.visible": "Visible", "options.vsync": "VSync", "outOfMemory.message": "Minecraft se ha quedado sin memoria.\n\nEsto podría deberse a un error en el juego o a que la máquina virtual de Java no tiene suficiente memoria asignada.\n\nPara evitar la corrupción de niveles, la partida actual se ha cerrado. Hemos intentado liberar suficiente memoria para permitirte regresar al menú principal y volver a jugar, pero es posible que esto no haya funcionado.\n\nReinicia el juego si vuelves a ver este mensaje.", "outOfMemory.title": "¡Sin memoria!", "pack.available.title": "Disponibles", "pack.copyFailure": "Error al copiar los paquetes", "pack.dropConfirm": "¿Quieres agregar los siguientes paquetes a Minecraft?", "pack.dropInfo": "Arrastra archivos a esta ventana para agregar paquetes", "pack.dropRejected.message": "Las siguientes entradas no eran paquetes válidos y no se han copiado:\n %s", "pack.dropRejected.title": "Entradas que no son de paquetes", "pack.folderInfo": "(Coloca archivos de paquetes aquí)", "pack.incompatible": "Incompatible", "pack.incompatible.confirm.new": "Este paquete está diseñado para una versión de Minecraft más reciente y puede que no funcione correctamente.", "pack.incompatible.confirm.old": "Este paquete está diseñado para una versión de Minecraft anterior y puede que no funcione correctamente.", "pack.incompatible.confirm.title": "¿Se<PERSON>ro que quieres cargar este paquete?", "pack.incompatible.new": "(Diseñado para una versión de Minecraft posterior)", "pack.incompatible.old": "(Diseñado para una versión de Minecraft anterior)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON><PERSON> la <PERSON>a", "pack.selected.title": "Seleccionados", "pack.source.builtin": "integrado", "pack.source.feature": "característica", "pack.source.local": "local", "pack.source.server": "servidor", "pack.source.world": "mundo", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanés", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "<PERSON><PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Bodegón", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Objetivo Explotado Sastifactoriamente", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Calavera en llamas", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Busto", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Pájaro de las cavernas", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Cambiante", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "El reencuentro", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "<PERSON>", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Tierra", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Jefe final", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Hallazgo", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fuego", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Neblina", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditativo", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "Or<PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Búho con limones", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "La Porcena", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Apuntador", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Estanque", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "La piscina", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Paseo por la pradera", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Costa cálida", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Ataduras mortales", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "La calavera y las rosas", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "<PERSON><PERSON> preparado", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Girasoles", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "atardecer", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Desempacado", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "El Vacío", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "El caminante", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Páramo", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Agua", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "Descomposición", "painting.random": "Variante aleatoria", "parsing.bool.expected": "Se requiere un valor booleano", "parsing.bool.invalid": "<PERSON>or booleano no válido: \"%s\" no es \"true\" ni \"false\"", "parsing.double.expected": "Se requiere un valor doble", "parsing.double.invalid": "Valor doble no válido: %s", "parsing.expected": "Se requiere \"%s\"", "parsing.float.expected": "Se requiere un valor float", "parsing.float.invalid": "Valor float no válido: %s", "parsing.int.expected": "Se requiere un número entero", "parsing.int.invalid": "Número entero no válido: %s", "parsing.long.expected": "Se requiere un valor long", "parsing.long.invalid": "Valor long no válido: %s", "parsing.quote.escape": "La secuencia de escape \"\\%s\" no es válida en una cadena con comillas", "parsing.quote.expected.end": "Falta el cierre de comillas de la cadena", "parsing.quote.expected.start": "Faltan comillas al inicio de la cadena", "particle.invalidOptions": "No se pudo analizar las opciones de la Particula: %s", "particle.notFound": "Partícula desconocida: %s", "permissions.requires.entity": "Se requiere una entidad para ejecutar este comando aquí", "permissions.requires.player": "Se requiere un jugador para ejecutar este comando aquí", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Al aplicarse:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predicado desconocido: %s", "quickplay.error.invalid_identifier": "No se pudo encontrar un mundo identificador proporcionado", "quickplay.error.realm_connect": "No se pudo conectar al Realm", "quickplay.error.realm_permission": "Carece de permisos para conectarse al Realm", "quickplay.error.title": "Error al ingresar al modo de juego rápido", "realms.configuration.region.australia_east": "Nueva Gales del Sur, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, EE. UU.", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, EE. UU.", "realms.configuration.region.east_us_2": "Carolina del Norte, EE. UU.", "realms.configuration.region.france_central": "Francia", "realms.configuration.region.japan_east": "Este de Japón", "realms.configuration.region.japan_west": "Oeste de Japón", "realms.configuration.region.korea_central": "Corea del Sur", "realms.configuration.region.north_central_us": "Illinois, EE. UU.", "realms.configuration.region.north_europe": "Irlanda", "realms.configuration.region.south_central_us": "Texas, EE. UU.", "realms.configuration.region.southeast_asia": "Singapur", "realms.configuration.region.sweden_central": "<PERSON><PERSON>", "realms.configuration.region.uae_north": "Emiratos Árabes Unidos (EAU)", "realms.configuration.region.uk_south": "Sur de Inglaterra", "realms.configuration.region.west_central_us": "Utah, EE.UU.", "realms.configuration.region.west_europe": "País<PERSON>", "realms.configuration.region.west_us": "California, EE. UU.", "realms.configuration.region.west_us_2": "Washington, EE. UU.", "realms.configuration.region_preference.automatic_owner": "Automática (latencia del dueño del Realm)", "realms.configuration.region_preference.automatic_player": "Automática (latencia del primero en unirse)", "realms.missing.snapshot.error.text": "Minecraft Realms no es compatible con versiones snapshot.", "recipe.notFound": "Receta desconocida: %s", "recipe.toast.description": "Mira tu libro de recetas.", "recipe.toast.title": "¡Recetas conseguidas!", "record.nowPlaying": "Estás escuchando: %s", "recover_world.bug_tracker": "Reportar un error", "recover_world.button": "Intentar recuperar", "recover_world.done.failed": "Error al recuperar un estado anterior.", "recover_world.done.success": "¡Recuperación realizada con éxito!", "recover_world.done.title": "Recuperación completada", "recover_world.issue.missing_file": "Archivo faltante", "recover_world.issue.none": "<PERSON> problemas", "recover_world.message": "Se produjeron los siguientes problemas al intentar leer la carpeta «%s».\nTal vez sea posible restaurar el mundo de una versión anterior o puedes reportar el error en el registro de errores.", "recover_world.no_fallback": "No hay estados de recuperación disponibles", "recover_world.restore": "Intentar restaurar", "recover_world.restoring": "Intentando restaurar mundo...", "recover_world.state_entry": "Estado desde %s: ", "recover_world.state_entry.unknown": "desconocido", "recover_world.title": "Error al cargar el mundo", "recover_world.warning": "Error al cargar el resumen del mundo", "resourcePack.broken_assets": "RECURSOS DEFECTUOSOS DETECTADOS", "resourcePack.high_contrast.name": "Contraste alto", "resourcePack.load_fail": "Error al recargar el recurso", "resourcePack.programmer_art.name": "Arte de programador", "resourcePack.runtime_failure": "Se detectó un error en un paquete de recursos", "resourcePack.server.name": "Recursos específicos del mundo", "resourcePack.title": "Se<PERSON><PERSON><PERSON><PERSON> paque<PERSON> de recursos", "resourcePack.vanilla.description": "La apariencia por defecto de Minecraft", "resourcePack.vanilla.name": "Por defecto", "resourcepack.downloading": "Descargando paque<PERSON> de recursos", "resourcepack.progress": "Descargando archivo (%s MB)...", "resourcepack.requesting": "Haciendo una petición...", "screenshot.failure": "No se pudo guardar la captura de pantalla: %s", "screenshot.success": "Captura guardada como %s", "selectServer.add": "<PERSON><PERSON><PERSON>r<PERSON>", "selectServer.defaultName": "<PERSON><PERSON><PERSON>", "selectServer.delete": "Bo<PERSON>r", "selectServer.deleteButton": "Bo<PERSON>r", "selectServer.deleteQuestion": "¿Estás seguro de que quieres borrar este servidor?", "selectServer.deleteWarning": "«%s» desaparecerá... ¡para siempre! (¡mucho tiempo!)", "selectServer.direct": "Conexión directa", "selectServer.edit": "<PERSON><PERSON>", "selectServer.hiddenAddress": "(IP oculta)", "selectServer.refresh": "Actualizar", "selectServer.select": "Entrar al servidor", "selectWorld.access_failure": "Error al entrar al mundo", "selectWorld.allowCommands": "Per<PERSON><PERSON> comandos", "selectWorld.allowCommands.info": "/gamemode, /experience, etc.", "selectWorld.allowCommands.new": "Per<PERSON><PERSON> comandos", "selectWorld.backupEraseCache": "<PERSON><PERSON><PERSON> datos en caché", "selectWorld.backupJoinConfirmButton": "<PERSON><PERSON>r una copia y cargar", "selectWorld.backupJoinSkipButton": "¡Sé lo que estoy haciendo!", "selectWorld.backupQuestion.customized": "Los mundos personalizados ya no son compatibles", "selectWorld.backupQuestion.downgrade": "Degradar un mundo a una versión anterior no está soportado", "selectWorld.backupQuestion.experimental": "Los mundos con opciones de generación experimentales no tienen soporte", "selectWorld.backupQuestion.snapshot": "¿Seguro que quieres cargar este mundo?", "selectWorld.backupWarning.customized": "Minecraft eliminó la característica de mundos personalizados, por lo que este mundo ya no es compatible. <PERSON><PERSON>es cargarlo y jugar igual que antes, pero el nuevo terreno generado tendrá el aspecto de un mundo normal. ¡Sentimos las molestias!", "selectWorld.backupWarning.downgrade": "Este mundo fue jugado por última vez en la versión %s; estás en la versión %s. Degradar un mundo podría corromperlo - no podemos garantizar que vaya a cargar o funcionar. Si aún quieres continuar, ¡Por favor haz un respaldo!", "selectWorld.backupWarning.experimental": "Este mundo usa opciones experimentales que podrían dejar de funcionar en cualquier momento. No podemos garantizar que cargue o funcione correctamente. ¡Ten cuidado!", "selectWorld.backupWarning.snapshot": "Este mundo fue jugado por última vez en la versión %s; ahora mismo te encuentras en la versión %s. ¡Deberías crear una copia de seguridad para evitar corrupciones en el mundo!", "selectWorld.bonusItems": "<PERSON><PERSON>", "selectWorld.cheats": "comandos", "selectWorld.commands": "<PERSON><PERSON><PERSON>", "selectWorld.conversion": "¡Debe ser convertido!", "selectWorld.conversion.tooltip": "Este mundo debe ser abierto en una versión más antigua (como la 1.6.4) para ser convertido completamente sin problemas", "selectWorld.create": "<PERSON><PERSON>r un mundo nuevo", "selectWorld.customizeType": "Personalizar", "selectWorld.dataPacks": "Paquetes de <PERSON>", "selectWorld.data_read": "Leyendo datos del mundo...", "selectWorld.delete": "Bo<PERSON>r", "selectWorld.deleteButton": "Bo<PERSON>r", "selectWorld.deleteQuestion": "¿Estás seguro de que quieres borrar este mundo?", "selectWorld.deleteWarning": "\"%s\" desaparecerá... ¡para siempre! (¡mucho tiempo!)", "selectWorld.delete_failure": "Error al borrar el mundo", "selectWorld.edit": "<PERSON><PERSON>", "selectWorld.edit.backup": "Hacer copia de seguridad", "selectWorld.edit.backupCreated": "%s", "selectWorld.edit.backupFailed": "Error al realizar la copia de seguridad", "selectWorld.edit.backupFolder": "<PERSON><PERSON><PERSON>a de copias", "selectWorld.edit.backupSize": "Mundo copiado (%s MB)", "selectWorld.edit.export_worldgen_settings": "Exportar opciones de generación", "selectWorld.edit.export_worldgen_settings.failure": "Error al exportar", "selectWorld.edit.export_worldgen_settings.success": "Exportado con éxito", "selectWorld.edit.openFolder": "<PERSON><PERSON>r carpeta del mundo", "selectWorld.edit.optimize": "<PERSON>timi<PERSON> mundo", "selectWorld.edit.resetIcon": "Restaurar <PERSON> original", "selectWorld.edit.save": "Guardar", "selectWorld.edit.title": "<PERSON>ar mundo", "selectWorld.enterName": "Nombre del mundo", "selectWorld.enterSeed": "Semilla para el generador de mundos", "selectWorld.experimental": "Experimental", "selectWorld.experimental.details": "Detalles", "selectWorld.experimental.details.entry": "Características experimentales necesarias: %s", "selectWorld.experimental.details.title": "Requisitos de características experimentales.", "selectWorld.experimental.message": "¡Atención!\nEsta configuración requiere de caractertísticas que aún están siendo desarrolladas. Puede que tu mundo deje de funcionar, se rompa o no funcione con futuras actualizaciones.", "selectWorld.experimental.title": "Aviso de características experimentales", "selectWorld.experiments": "Experimentos", "selectWorld.experiments.info": "Tiene posible novedades que son experimentales. Ten cuidado porque las cosas pueden dañarse. La opción Experimental no se puede desactivar después de la creación del mundo.", "selectWorld.futureworld.error.text": "Se produjo un error al intentar cargar un mundo de una versión más reciente. Era una operación arriesgada;, sentimos las molestias.", "selectWorld.futureworld.error.title": "¡Se produjo un error!", "selectWorld.gameMode": "Modo", "selectWorld.gameMode.adventure": "Aventura", "selectWorld.gameMode.adventure.info": "Igual que el modo supervivencia, pero no puedes colocar o destruir bloques.", "selectWorld.gameMode.adventure.line1": "Igual que el modo supervivencia, pero no", "selectWorld.gameMode.adventure.line2": "puedes colocar ni destruir bloques.", "selectWorld.gameMode.creative": "Creativo", "selectWorld.gameMode.creative.info": "Explora, crea y da rienda suelta a tus ideas con la habilidad de vuelo y una cantidad infinita de materiales, sin recibir daño de los monstruos.", "selectWorld.gameMode.creative.line1": "Tienes recursos ilimitados, puedes volar", "selectWorld.gameMode.creative.line2": "y destruir bloques de forma instantánea.", "selectWorld.gameMode.hardcore": "Extremo", "selectWorld.gameMode.hardcore.info": "Modo supervivencia bloqueado en la dificultad \"Difícil\". No reapareces al morir.", "selectWorld.gameMode.hardcore.line1": "Igual que el modo supervivencia, pero", "selectWorld.gameMode.hardcore.line2": "más difícil y con solo una vida", "selectWorld.gameMode.spectator": "Espectador", "selectWorld.gameMode.spectator.info": "<PERSON><PERSON><PERSON> mi<PERSON>, pero no tocar.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON><PERSON> mi<PERSON>, pero no tocar", "selectWorld.gameMode.survival": "Supervivencia", "selectWorld.gameMode.survival.info": "Explora un mundo lleno de misterios, donde podr<PERSON> construir, recolectar, fabricar y enfrentarte a monstruos.", "selectWorld.gameMode.survival.line1": "Busca recursos, fabrica herramientas,", "selectWorld.gameMode.survival.line2": "sube niveles y come para no morir.", "selectWorld.gameRules": "Reglas del juego", "selectWorld.import_worldgen_settings": "<PERSON><PERSON><PERSON>r a<PERSON>", "selectWorld.import_worldgen_settings.failure": "Error al importar opciones", "selectWorld.import_worldgen_settings.select_file": "Selecciona un archivo de opciones (.json)", "selectWorld.incompatible.description": "Este mundo no se puede abrir en esta versión.\nLa última vez se jugó en la versión %s.", "selectWorld.incompatible.info": "Versión incompatible: %s", "selectWorld.incompatible.title": "Versión incompatible", "selectWorld.incompatible.tooltip": "Este mundo no se puede abrir porque se creó en una versión no compatible.", "selectWorld.incompatible_series": "Creado en una versión incompatible", "selectWorld.load_folder_access": "¡No se puede leer o acceder a la carpeta donde se guardan los mundos!", "selectWorld.loading_list": "Cargando lista de mundos", "selectWorld.locked": "Mundo abierto en otra sesión de Minecraft", "selectWorld.mapFeatures": "Generar estructuras", "selectWorld.mapFeatures.info": "Aldeas, naufragios, etc.", "selectWorld.mapType": "Tipo de mundo", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Más opciones del mundo...", "selectWorld.newWorld": "Mundo nuevo", "selectWorld.recreate": "<PERSON><PERSON><PERSON>", "selectWorld.recreate.customized.text": "Los mundos personalizados no son compatibles con esta versión de Minecraft. Podemos intentar rehacerlo con la misma semilla y las mismas propiedades, pero tendrá el aspecto de un mundo normal. ¡Sentimos las molestias!", "selectWorld.recreate.customized.title": "Los mundos personalizados ya no son compatibles", "selectWorld.recreate.error.text": "Algo falló al tratar de rehacer el mundo.", "selectWorld.recreate.error.title": "¡Ocurrió un error!", "selectWorld.resource_load": "Preparando recursos...", "selectWorld.resultFolder": "Se guardará en:", "selectWorld.search": "buscar mundos", "selectWorld.seedInfo": "Si no escribes una semilla se elegirá al azar", "selectWorld.select": "<PERSON><PERSON> al mundo seleccionado", "selectWorld.targetFolder": "Carpeta de guardado: %s", "selectWorld.title": "Seleccionar mundo", "selectWorld.tooltip.fromNewerVersion1": "¡El mundo pertenece a una versión más reciente,", "selectWorld.tooltip.fromNewerVersion2": "si lo cargas en la actual, podr<PERSON> corromperse!", "selectWorld.tooltip.snapshot1": "No te olvides de hacer una copia de seguridad de este mundo", "selectWorld.tooltip.snapshot2": "antes de cargarlo en esta versión snapshot.", "selectWorld.unable_to_load": "No se pueden cargar los mundos", "selectWorld.version": "versión:", "selectWorld.versionJoinButton": "<PERSON><PERSON>", "selectWorld.versionQuestion": "¿Quieres cargar este mundo?", "selectWorld.versionUnknown": "¿?", "selectWorld.versionWarning": "¡El mundo se guardó por última vez en la versión %s! \n¡Si se carga en esta versión podría dañarse!", "selectWorld.warning.deprecated.question": "Algunas características usadas son obsoletas y dejarán de funcionar en el futuro. ¿Quieres continuar?", "selectWorld.warning.deprecated.title": "¡Atención! Estas opciones utilizan características que ya no están en uso", "selectWorld.warning.experimental.question": "Estas opciones son experimentales y pueden dejar de funcionar en cualquier momento. ¿Quieres continuar?", "selectWorld.warning.experimental.title": "¡Advertencia! Estas opciones utilizan características experimentales", "selectWorld.warning.lowDiskSpace.description": "No queda mucho espacio en tu dispositivo.\nTu mundo podría corromperse si te quedas sin espacio en el disco mientras juegas.", "selectWorld.warning.lowDiskSpace.title": "¡Aviso! ¡Queda poco espacio en el disco!", "selectWorld.world": "Mundo", "sign.edit": "Editar mensaje del cartel", "sleep.not_possible": "No hay suficientes jugadores durmiendo para pasar la noche", "sleep.players_sleeping": "%s/%s jugadores durmiendo", "sleep.skipping_night": "Durmiendo esta noche", "slot.only_single_allowed": "Solo se permiten espacios individuales, se obtuvo ‘%s’", "slot.unknown": "Ranura desconocida: %s", "snbt.parser.empty_key": "La tecla no puede estar vacía", "snbt.parser.expected_binary_numeral": "Se espera un número binario", "snbt.parser.expected_decimal_numeral": "Se espera un número Decimal", "snbt.parser.expected_float_type": "Se espera un número de coma flotante", "snbt.parser.expected_hex_escape": "Se espera un carácter literal de longitud %s", "snbt.parser.expected_hex_numeral": "Se espera un número hexadecimal", "snbt.parser.expected_integer_type": "Se espera un número entero", "snbt.parser.expected_non_negative_number": "Se espera un número no negativo", "snbt.parser.expected_number_or_boolean": "Se espera un número o un boolean", "snbt.parser.expected_string_uuid": "Se espera un hilo que represente un UUID valido", "snbt.parser.expected_unquoted_string": "Se espera una cadena válida sin comillas", "snbt.parser.infinity_not_allowed": "No se admiten números no finitos", "snbt.parser.invalid_array_element_type": "Tipo de elemento de matriz inválido", "snbt.parser.invalid_character_name": "Nombre de carácter unicode inválido", "snbt.parser.invalid_codepoint": "Valor de carácter unicode no válido: %s", "snbt.parser.invalid_string_contents": "Contenido de cadena inválido", "snbt.parser.invalid_unquoted_start": "Las cadenas sin comillas no pueden empezar por los dígitos 0-9, + o -", "snbt.parser.leading_zero_not_allowed": "Los números decimales no pueden empezar por 0", "snbt.parser.no_such_operation": "No existe tal operación: %s", "snbt.parser.number_parse_failure": "Error al analizar el número: %s", "snbt.parser.undescore_not_allowed": "Los guiones bajos no están permitidos al principio ni al final de un número", "soundCategory.ambient": "Ambiente", "soundCategory.block": "Bloques", "soundCategory.hostile": "Criaturas hostiles", "soundCategory.master": "Volumen maestro", "soundCategory.music": "Música", "soundCategory.neutral": "Criaturas amigables", "soundCategory.player": "<PERSON><PERSON><PERSON>", "soundCategory.record": "Bloques musicales", "soundCategory.ui": "Interfaz", "soundCategory.voice": "Voces y diálogos", "soundCategory.weather": "Precipitaciones", "spectatorMenu.close": "<PERSON><PERSON><PERSON>", "spectatorMenu.next_page": "Siguient<PERSON>", "spectatorMenu.previous_page": "Anterior", "spectatorMenu.root.prompt": "Presiona una tecla para seleccionar un comando, y vuelve a presionarla para usarlo.", "spectatorMenu.team_teleport": "Teletransportarse a un miembro del equipo", "spectatorMenu.team_teleport.prompt": "Elige un equipo al que teletransportarte", "spectatorMenu.teleport": "Teletransportarse a un jugador", "spectatorMenu.teleport.prompt": "Elige un jugador al que teletransportarte", "stat.generalButton": "General", "stat.itemsButton": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "Animales domesticados", "stat.minecraft.aviate_one_cm": "Distancia volada en élitros", "stat.minecraft.bell_ring": "Campanas to<PERSON>das", "stat.minecraft.boat_one_cm": "Distancia en bote", "stat.minecraft.clean_armor": "Piezas de armadura limpiadas", "stat.minecraft.clean_banner": "Estandartes limpiados", "stat.minecraft.clean_shulker_box": "Cajas de shulker vaciadas", "stat.minecraft.climb_one_cm": "Distancia escalada", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON> a<PERSON>", "stat.minecraft.damage_absorbed": "Daño <PERSON>", "stat.minecraft.damage_blocked_by_shield": "Daño bloqueado con escudo", "stat.minecraft.damage_dealt": "<PERSON><PERSON> causado", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON> infligido (absorbido)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON> infligido (resistido)", "stat.minecraft.damage_resisted": "Daño resistido", "stat.minecraft.damage_taken": "<PERSON><PERSON> recibido", "stat.minecraft.deaths": "Númer<PERSON>uer<PERSON>", "stat.minecraft.drop": "<PERSON>b<PERSON><PERSON> so<PERSON>", "stat.minecraft.eat_cake_slice": "Trozos de torta comidos", "stat.minecraft.enchant_item": "Objetos encantados", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.fill_cauldron": "Calderos ll<PERSON>", "stat.minecraft.fish_caught": "Peces pescados", "stat.minecraft.fly_one_cm": "Distancia volada", "stat.minecraft.happy_ghast_one_cm": "Distan<PERSON> de <PERSON>", "stat.minecraft.horse_one_cm": "Distancia a caballo", "stat.minecraft.inspect_dispenser": "Dispensadores examinados", "stat.minecraft.inspect_dropper": "Soltadores examinados", "stat.minecraft.inspect_hopper": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_anvil": "Usos de yunques", "stat.minecraft.interact_with_beacon": "Usos de faros", "stat.minecraft.interact_with_blast_furnace": "Usos de altos hornos", "stat.minecraft.interact_with_brewingstand": "Usos de soportes para pociones", "stat.minecraft.interact_with_campfire": "Usos de fogatas", "stat.minecraft.interact_with_cartography_table": "Usos de mesas de cartografía", "stat.minecraft.interact_with_crafting_table": "Usos de mesas de crafteo", "stat.minecraft.interact_with_furnace": "Usos de hornos", "stat.minecraft.interact_with_grindstone": "Usos de afiladoras", "stat.minecraft.interact_with_lectern": "Usos de atriles", "stat.minecraft.interact_with_loom": "Usos de telares", "stat.minecraft.interact_with_smithing_table": "Usos de mesas de herrería", "stat.minecraft.interact_with_smoker": "Usos de ahumadores", "stat.minecraft.interact_with_stonecutter": "Usos de cortapiedras", "stat.minecraft.jump": "Saltos", "stat.minecraft.leave_game": "Veces que saliste de la partida", "stat.minecraft.minecart_one_cm": "Distancia en vagón", "stat.minecraft.mob_kills": "Criaturas matadas", "stat.minecraft.open_barrel": "<PERSON><PERSON> abiertos", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON> abie<PERSON>", "stat.minecraft.open_enderchest": "Baúles de ender abiertos", "stat.minecraft.open_shulker_box": "Cajas de shulker abiertas", "stat.minecraft.pig_one_cm": "Distancia en cochino", "stat.minecraft.play_noteblock": "Bloques musicales golpeados", "stat.minecraft.play_record": "Discos reproducidos", "stat.minecraft.play_time": "Tiempo jugado", "stat.minecraft.player_kills": "Ju<PERSON><PERSON> matados", "stat.minecraft.pot_flower": "Plantas sembradas en macetas", "stat.minecraft.raid_trigger": "Invasiones provocadas", "stat.minecraft.raid_win": "<PERSON>es ganadas", "stat.minecraft.sleep_in_bed": "Noches dormidas", "stat.minecraft.sneak_time": "Tiempo agachado", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON> corriendo", "stat.minecraft.strider_one_cm": "Distancia sobre lavagante", "stat.minecraft.swim_one_cm": "Distancia a nado", "stat.minecraft.talked_to_villager": "Charlas con aldeanos", "stat.minecraft.target_hit": "<PERSON><PERSON>", "stat.minecraft.time_since_death": "Tiempo desde últ. muerte", "stat.minecraft.time_since_rest": "Tiempo desde últ. descanso", "stat.minecraft.total_world_time": "Tiempo con el mundo abierto", "stat.minecraft.traded_with_villager": "Comercios con aldeanos", "stat.minecraft.trigger_trapped_chest": "Baúles trampa activados", "stat.minecraft.tune_noteblock": "Bloques musicales afinados", "stat.minecraft.use_cauldron": "Agua tomada de calderos", "stat.minecraft.walk_on_water_one_cm": "Distancia caminada en agua", "stat.minecraft.walk_one_cm": "Distancia caminada", "stat.minecraft.walk_under_water_one_cm": "Distancia caminada bajo agua", "stat.mobsButton": "Criaturas", "stat_type.minecraft.broken": "<PERSON><PERSON><PERSON> roto", "stat_type.minecraft.crafted": "Veces fabricado", "stat_type.minecraft.dropped": "<PERSON><PERSON><PERSON> solta<PERSON>", "stat_type.minecraft.killed": "Criaturas matadas: %s", "stat_type.minecraft.killed.none": "Criaturas matadas: 0", "stat_type.minecraft.killed_by": "Muertes causadas: %2$s", "stat_type.minecraft.killed_by.none": "Muertes causadas: 0", "stat_type.minecraft.mined": "<PERSON><PERSON><PERSON> minado", "stat_type.minecraft.picked_up": "<PERSON><PERSON><PERSON> obtenido", "stat_type.minecraft.used": "<PERSON>eces utilizado", "stats.none": "-", "structure_block.button.detect_size": "DETECTAR", "structure_block.button.load": "CARGAR", "structure_block.button.save": "GUARDAR", "structure_block.custom_data": "Nombre de dataTag personalizado", "structure_block.detect_size": "Detectar tamaño y posición de la estructura:", "structure_block.hover.corner": "Esquina: %s", "structure_block.hover.data": "Datos: %s", "structure_block.hover.load": "Cargar: %s", "structure_block.hover.save": "Guardar: %s", "structure_block.include_entities": "Incluir entidades:", "structure_block.integrity": "Integridad y semilla de la estructura", "structure_block.integrity.integrity": "Integridad de la estructura", "structure_block.integrity.seed": "Semilla de la estructura", "structure_block.invalid_structure_name": "\"%s\" no es un nombre de estructura válido", "structure_block.load_not_found": "La estructura \"%s\" no está disponible", "structure_block.load_prepare": "Posición de la estructura \"%s\" preparada", "structure_block.load_success": "Estructura \"%s\" cargada", "structure_block.mode.corner": "Esquina", "structure_block.mode.data": "Datos", "structure_block.mode.load": "<PERSON><PERSON>", "structure_block.mode.save": "Guardar", "structure_block.mode_info.corner": "Esquina: colócalo en esquinas para marcarlas", "structure_block.mode_info.data": "Datos: usa la lógica del juego", "structure_block.mode_info.load": "Cargar: carga tu estructura", "structure_block.mode_info.save": "Guardar: guarda tu estructura", "structure_block.position": "Posición relativa", "structure_block.position.x": "posición x relativa", "structure_block.position.y": "posición y relativa", "structure_block.position.z": "posición z relativa", "structure_block.save_failure": "No se pudo guardar la estructura \"%s\"", "structure_block.save_success": "Estructura guardada como \"%s\"", "structure_block.show_air": "Mostrar bloques invisibles:", "structure_block.show_boundingbox": "Mostrar límites:", "structure_block.size": "Tamaño de la estructura", "structure_block.size.x": "tamaño x de la estructura", "structure_block.size.y": "tamaño y de la estructura", "structure_block.size.z": "tamaño z de la estructura", "structure_block.size_failure": "No se pudo detectar el tamaño de la estructura. Agrega más esquinas con el mismo nombre de estructura", "structure_block.size_success": "Tamaño de \"%s\" detectado", "structure_block.strict": "Colocación estricta:", "structure_block.structure_name": "Nombre de la estructura", "subtitles.ambient.cave": "Ruido inquietante", "subtitles.ambient.sound": "Ruidos espeluznantes", "subtitles.block.amethyst_block.chime": "Amatista tintinea", "subtitles.block.amethyst_block.resonate": "Amatista resuena", "subtitles.block.anvil.destroy": "<PERSON><PERSON> des<PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON>", "subtitles.block.anvil.use": "<PERSON><PERSON> usado", "subtitles.block.barrel.close": "<PERSON>il cerrado", "subtitles.block.barrel.open": "<PERSON><PERSON> abierto", "subtitles.block.beacon.activate": "Faro activado", "subtitles.block.beacon.ambient": "<PERSON><PERSON><PERSON><PERSON>o", "subtitles.block.beacon.deactivate": "Faro desactivado", "subtitles.block.beacon.power_select": "Poder de faro seleccionado", "subtitles.block.beehive.drip": "<PERSON><PERSON>", "subtitles.block.beehive.enter": "<PERSON><PERSON> entrando en colmena", "subtitles.block.beehive.exit": "<PERSON><PERSON> saliendo de colmena", "subtitles.block.beehive.shear": "Tijeras raspando", "subtitles.block.beehive.work": "<PERSON><PERSON> trabajando", "subtitles.block.bell.resonate": "Campana resonando", "subtitles.block.bell.use": "Toque de <PERSON>", "subtitles.block.big_dripleaf.tilt_down": "Plantaforma se inclina hacia abajo", "subtitles.block.big_dripleaf.tilt_up": "Plantaforma se inclina hacia arriba", "subtitles.block.blastfurnace.fire_crackle": "Chispas de alto horno", "subtitles.block.brewing_stand.brew": "Burbujas de un soporte para pociones", "subtitles.block.bubble_column.bubble_pop": "Estallido de burbuja", "subtitles.block.bubble_column.upwards_ambient": "Corriente de burbujas", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON> ascendiente", "subtitles.block.bubble_column.whirlpool_ambient": "Remolino de burbujas", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON> descendiente", "subtitles.block.button.click": "Chasquido de botón", "subtitles.block.cake.add_candle": "<PERSON><PERSON> a<PERSON>lastán<PERSON>", "subtitles.block.campfire.crackle": "Chispas de fogata", "subtitles.block.candle.crackle": "Chisporroteo de vela", "subtitles.block.candle.extinguish": "Vela se apaga", "subtitles.block.chest.close": "<PERSON><PERSON> cerrado", "subtitles.block.chest.locked": "<PERSON><PERSON> blo<PERSON>", "subtitles.block.chest.open": "<PERSON><PERSON> abie<PERSON>o", "subtitles.block.chorus_flower.death": "Flor de chorus se marchita", "subtitles.block.chorus_flower.grow": "Flor de chorus crece", "subtitles.block.comparator.click": "Chasquido de comparador", "subtitles.block.composter.empty": "Compostador vaciado", "subtitles.block.composter.fill": "Compostador llena<PERSON>", "subtitles.block.composter.ready": "Compostador listo", "subtitles.block.conduit.activate": "Canalizador activado", "subtitles.block.conduit.ambient": "Latidos de canalizador", "subtitles.block.conduit.attack.target": "Canalizador ataca", "subtitles.block.conduit.deactivate": "Canalizador desactivado", "subtitles.block.copper_bulb.turn_off": "Lámpara de cobre se apaga", "subtitles.block.copper_bulb.turn_on": "Lámpara de cobre se enciende", "subtitles.block.copper_trapdoor.close": "Escotilla se cierra", "subtitles.block.copper_trapdoor.open": "Escotilla se abre", "subtitles.block.crafter.craft": "Fabricador fabrica", "subtitles.block.crafter.fail": "Fabricador falla", "subtitles.block.creaking_heart.hurt": "Gruñidos de corazón crujidor", "subtitles.block.creaking_heart.idle": "Ruidos espeluznantes", "subtitles.block.creaking_heart.spawn": "Despertó el corazón crujidor", "subtitles.block.deadbush.idle": "Ruidos secos", "subtitles.block.decorated_pot.insert": "Vasija decorada se llena", "subtitles.block.decorated_pot.insert_fail": "Vasija decorada se bambolea", "subtitles.block.decorated_pot.shatter": "V<PERSON><PERSON> se rompe", "subtitles.block.dispenser.dispense": "Objeto dispensado", "subtitles.block.dispenser.fail": "Error de un dispensador", "subtitles.block.door.toggle": "Crujido de puerta", "subtitles.block.dried_ghast.ambient": "<PERSON><PERSON>hast Seco", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON> seco rehidratado", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON><PERSON> seco en remojo", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON> seco se siente mejor", "subtitles.block.dry_grass.ambient": "Sonidos de viento", "subtitles.block.enchantment_table.use": "Mesa de encantamientos usada", "subtitles.block.end_portal.spawn": "Portal del End abierto", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON> de ender colocado", "subtitles.block.eyeblossom.close": "Florojo se cierra", "subtitles.block.eyeblossom.idle": "<PERSON><PERSON><PERSON><PERSON> de la flor de ojo", "subtitles.block.eyeblossom.open": "Florojo se abre", "subtitles.block.fence_gate.toggle": "Crujido de puerta de cerca", "subtitles.block.fire.ambient": "Chispas de fuego", "subtitles.block.fire.extinguish": "Fuego extinguido", "subtitles.block.firefly_bush.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON> zumban", "subtitles.block.frogspawn.hatch": "<PERSON><PERSON><PERSON> de rana eclosionan", "subtitles.block.furnace.fire_crackle": "Chispas de horno", "subtitles.block.generic.break": "Bloque roto", "subtitles.block.generic.fall": "Algo cae encima de un bloque", "subtitles.block.generic.footsteps": "Pisadas", "subtitles.block.generic.hit": "Rotura de bloque", "subtitles.block.generic.place": "Bloque colocado", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON><PERSON> usada", "subtitles.block.growing_plant.crop": "Planta podada", "subtitles.block.hanging_sign.waxed_interact_fail": "Cartel se bambolea", "subtitles.block.honey_block.slide": "Deslizamiento en bloque de miel", "subtitles.block.iron_trapdoor.close": "Escotilla cerrada", "subtitles.block.iron_trapdoor.open": "Escotilla abierta", "subtitles.block.lava.ambient": "Burbujeo de lava", "subtitles.block.lava.extinguish": "Lava solidificada", "subtitles.block.lever.click": "Chasquido de palanca", "subtitles.block.note_block.note": "Bloque musical", "subtitles.block.pale_hanging_moss.idle": "Ruidos espeluznantes", "subtitles.block.piston.move": "Movimiento de un pistón", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> gotea", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON>va gotea en un caldero", "subtitles.block.pointed_dripstone.drip_water": "Agua gotea", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Agua gotea en un caldero", "subtitles.block.pointed_dripstone.land": "Estalactita se derrumba", "subtitles.block.portal.ambient": "Zumbido de portal", "subtitles.block.portal.travel": "Ruido de portal se desvanece", "subtitles.block.portal.trigger": "Ruido de portal se intensifica", "subtitles.block.pressure_plate.click": "Chasquido de placa de presión", "subtitles.block.pumpkin.carve": "Tijer<PERSON> tallan", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.block.respawn_anchor.ambient": "Portal suena", "subtitles.block.respawn_anchor.charge": "El nexo de reaparición está cargado", "subtitles.block.respawn_anchor.deplete": "El nexo de reaparición requiere carga", "subtitles.block.respawn_anchor.set_spawn": "El nexo de reaparición se activó", "subtitles.block.sand.idle": "Son<PERSON>s <PERSON>", "subtitles.block.sand.wind": "Sonidos <PERSON>", "subtitles.block.sculk.charge": "Sculk burbujea", "subtitles.block.sculk.spread": "Sculk se propaga", "subtitles.block.sculk_catalyst.bloom": "Catalizador de sculk brota", "subtitles.block.sculk_sensor.clicking": "Sensor de sculk se activa", "subtitles.block.sculk_sensor.clicking_stop": "Sensor de sculk se desactiva", "subtitles.block.sculk_shrieker.shriek": "<PERSON><PERSON><PERSON> de sculk chilla", "subtitles.block.shulker_box.close": "Caja de shulker cerrada", "subtitles.block.shulker_box.open": "<PERSON>lk<PERSON> se abre", "subtitles.block.sign.waxed_interact_fail": "Cartel se tambalea", "subtitles.block.smithing_table.use": "Mesa de herrería usada", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON>do <PERSON>o", "subtitles.block.sniffer_egg.crack": "Huevo de sniffer con grietas ", "subtitles.block.sniffer_egg.hatch": "Huevo de Sniffer eclosionado ", "subtitles.block.sniffer_egg.plop": "Sniffer pone un huevo", "subtitles.block.sponge.absorb": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.sweet_berry_bush.pick_berries": "Bayas caen", "subtitles.block.trapdoor.close": "Escotilla se cierra", "subtitles.block.trapdoor.open": "Escotilla se abre", "subtitles.block.trapdoor.toggle": "Crujido de escotilla", "subtitles.block.trial_spawner.about_to_spawn_item": "Objeto Siniestro se Prepara", "subtitles.block.trial_spawner.ambient": "Generador de Prueba crepita", "subtitles.block.trial_spawner.ambient_charged": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.trial_spawner.ambient_ominous": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.trial_spawner.charge_activate": "Presagio envuelve un Generador de Prueba", "subtitles.block.trial_spawner.close_shutter": "Generador de Prueba se cierra", "subtitles.block.trial_spawner.detect_player": "Generador de Prueba se carga", "subtitles.block.trial_spawner.eject_item": "Generador de Prueba expulsa objetos", "subtitles.block.trial_spawner.ominous_activate": "Presagio envuelve un Generador de Prueba", "subtitles.block.trial_spawner.open_shutter": "Generador de Prueba se abre", "subtitles.block.trial_spawner.spawn_item": "Objetos que Suelta Siniestro", "subtitles.block.trial_spawner.spawn_item_begin": "Objeto Siniestro Aparece", "subtitles.block.trial_spawner.spawn_mob": "Generador de Prueba genera una criatura", "subtitles.block.tripwire.attach": "<PERSON>lo trampa se conecta", "subtitles.block.tripwire.click": "Chasquido de hilo trampa", "subtitles.block.tripwire.detach": "<PERSON>lo trampa se desconecta", "subtitles.block.vault.activate": "Boveda activada", "subtitles.block.vault.ambient": "Sonido de boveda", "subtitles.block.vault.close_shutter": "<PERSON><PERSON><PERSON> cer<PERSON>", "subtitles.block.vault.deactivate": "Boveda desactivada", "subtitles.block.vault.eject_item": "Boveda suelta un item", "subtitles.block.vault.insert_item": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.insert_item_fail": "Boveda falla al desbloquear", "subtitles.block.vault.open_shutter": "Boveda se abre", "subtitles.block.vault.reject_rewarded_player": "El arca rechazó al jugador", "subtitles.block.water.ambient": "Corriente de agua", "subtitles.block.wet_sponge.dries": "Esponja se seca", "subtitles.chiseled_bookshelf.insert": "Libro colocado", "subtitles.chiseled_bookshelf.insert_enchanted": "Libro encantado colocado", "subtitles.chiseled_bookshelf.take": "Libro tomado", "subtitles.chiseled_bookshelf.take_enchanted": "Libro encantado tomado", "subtitles.enchant.thorns.hit": "Pinchazo", "subtitles.entity.allay.ambient_with_item": "Allay busca", "subtitles.entity.allay.ambient_without_item": "<PERSON>ay anhela", "subtitles.entity.allay.death": "<PERSON>ay muere", "subtitles.entity.allay.hurt": "Allay herido", "subtitles.entity.allay.item_given": "Allay se ríe", "subtitles.entity.allay.item_taken": "Allay recoge", "subtitles.entity.allay.item_thrown": "Allay suelta", "subtitles.entity.armadillo.ambient": "Cachicamo g<PERSON>ñe", "subtitles.entity.armadillo.brush": "Cepillado de Cachicamo", "subtitles.entity.armadillo.death": "Cachicamo muere", "subtitles.entity.armadillo.eat": "Cachicamo come", "subtitles.entity.armadillo.hurt": "Cachicamo herido", "subtitles.entity.armadillo.hurt_reduced": "Cachicamo se resguarda", "subtitles.entity.armadillo.land": "Cachicamo aterriza", "subtitles.entity.armadillo.peek": "Cachicamo se asoma", "subtitles.entity.armadillo.roll": "Cachicamo se enrolla", "subtitles.entity.armadillo.scute_drop": "Cachicamo suelta una escama", "subtitles.entity.armadillo.unroll_finish": "Cachicamo se desenrolla", "subtitles.entity.armadillo.unroll_start": "Cachicamo se asoma", "subtitles.entity.armor_stand.fall": "Algo cayó", "subtitles.entity.arrow.hit": "Impacto de flecha", "subtitles.entity.arrow.hit_player": "Impacto a un jugador", "subtitles.entity.arrow.shoot": "Tiro de arco", "subtitles.entity.axolotl.attack": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON><PERSON> muere", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.axolotl.idle_air": "<PERSON><PERSON><PERSON><PERSON> chilla", "subtitles.entity.axolotl.idle_water": "<PERSON><PERSON><PERSON><PERSON> chilla", "subtitles.entity.axolotl.splash": "<PERSON><PERSON><PERSON><PERSON> sal<PERSON>a", "subtitles.entity.axolotl.swim": "<PERSON><PERSON><PERSON><PERSON> nada", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.bat.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> muere", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.takeoff": "Mu<PERSON><PERSON><PERSON><PERSON><PERSON> volando", "subtitles.entity.bee.ambient": "Zumbido de abeja", "subtitles.entity.bee.death": "<PERSON><PERSON> muere", "subtitles.entity.bee.hurt": "<PERSON><PERSON>", "subtitles.entity.bee.loop": "<PERSON><PERSON> zumba", "subtitles.entity.bee.loop_aggressive": "Zumbido de abeja furiosa", "subtitles.entity.bee.pollinate": "Zumbido de abeja feliz", "subtitles.entity.bee.sting": "<PERSON><PERSON> picando", "subtitles.entity.blaze.ambient": "Respiros de blaze", "subtitles.entity.blaze.burn": "Chispas de blaze", "subtitles.entity.blaze.death": "<PERSON> muere", "subtitles.entity.blaze.hurt": "<PERSON> herido", "subtitles.entity.blaze.shoot": "<PERSON> dispara", "subtitles.entity.boat.paddle_land": "Remando en tierra", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Traqueteo de esqueleto <PERSON>tanoso", "subtitles.entity.bogged.death": "Esqueleto pantanoso muere", "subtitles.entity.bogged.hurt": "Esqueleto pantanoso herido", "subtitles.entity.breeze.charge": "Breeze carga", "subtitles.entity.breeze.death": "<PERSON><PERSON> muere", "subtitles.entity.breeze.deflect": "<PERSON><PERSON>", "subtitles.entity.breeze.hurt": "Breeze herido", "subtitles.entity.breeze.idle_air": "<PERSON><PERSON> vuela", "subtitles.entity.breeze.idle_ground": "Breeze zumba", "subtitles.entity.breeze.inhale": "Breeze inhala", "subtitles.entity.breeze.jump": "Breeze salta", "subtitles.entity.breeze.land": "Breeze aterriza", "subtitles.entity.breeze.shoot": "<PERSON><PERSON> dispara", "subtitles.entity.breeze.slide": "Breeze se desplaza", "subtitles.entity.breeze.whirl": "<PERSON><PERSON> ulula", "subtitles.entity.breeze.wind_burst": "Carga de brisa estalla", "subtitles.entity.camel.ambient": "<PERSON><PERSON> gru<PERSON>e", "subtitles.entity.camel.dash": "Camello brinca", "subtitles.entity.camel.dash_ready": "Camello se recupera", "subtitles.entity.camel.death": "Camello muere", "subtitles.entity.camel.eat": "Camello come", "subtitles.entity.camel.hurt": "Camello herido", "subtitles.entity.camel.saddle": "Silla equipada", "subtitles.entity.camel.sit": "Camello se sienta", "subtitles.entity.camel.stand": "Camello se levanta", "subtitles.entity.camel.step": "Camello camina", "subtitles.entity.camel.step_sand": "Camello camina sobre arena", "subtitles.entity.cat.ambient": "Maullido de gato", "subtitles.entity.cat.beg_for_food": "Gato pide comida", "subtitles.entity.cat.death": "<PERSON><PERSON> muere", "subtitles.entity.cat.eat": "Gato come", "subtitles.entity.cat.hiss": "Bufido de gato", "subtitles.entity.cat.hurt": "Gato herido", "subtitles.entity.cat.purr": "Ronroneo de gato", "subtitles.entity.chicken.ambient": "Cacareo de gallina", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.chicken.egg": "Una gallina pone un huevo", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.cod.death": "Bacalao muere", "subtitles.entity.cod.flop": "Bacalao aleteando", "subtitles.entity.cod.hurt": "Bacalao herido", "subtitles.entity.cow.ambient": "Mugido de vaca", "subtitles.entity.cow.death": "Vaca muere", "subtitles.entity.cow.hurt": "Vaca herida", "subtitles.entity.cow.milk": "Vaca ordeñada", "subtitles.entity.creaking.activate": "Crepitante activa", "subtitles.entity.creaking.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.attack": "T<PERSON>dor ataca", "subtitles.entity.creaking.deactivate": "Crepitante se desactiva", "subtitles.entity.creaking.death": "Crepitante muere", "subtitles.entity.creaking.freeze": "Crepitante se detiene", "subtitles.entity.creaking.spawn": "Crepitante aparece", "subtitles.entity.creaking.sway": "Crepitante se protege", "subtitles.entity.creaking.twitch": "Crepitante se retuerce", "subtitles.entity.creaking.unfreeze": "Crepitante se mueve", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> de creeper", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON><PERSON>ín", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON> jugando", "subtitles.entity.dolphin.splash": "Delfín salpica", "subtitles.entity.dolphin.swim": "Delfín nada", "subtitles.entity.donkey.ambient": "Rebuzno de burro", "subtitles.entity.donkey.angry": "<PERSON><PERSON> relinchando", "subtitles.entity.donkey.chest": "Baúl equipado a un burro", "subtitles.entity.donkey.death": "<PERSON><PERSON> muere", "subtitles.entity.donkey.eat": "<PERSON><PERSON> come", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> her<PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON> salta", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.ambient_water": "<PERSON><PERSON><PERSON> gargar<PERSON>", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.drowned.shoot": "<PERSON><PERSON><PERSON> lanzando un tridente", "subtitles.entity.drowned.step": "Pasos de ahogado", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON> nada", "subtitles.entity.egg.throw": "<PERSON><PERSON> lanza<PERSON>", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON><PERSON><PERSON> de guardi<PERSON>", "subtitles.entity.elder_guardian.ambient_land": "Aleteo de guardián an<PERSON>o", "subtitles.entity.elder_guardian.curse": "Maldición de guardián anciano", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON> anciano muere", "subtitles.entity.elder_guardian.flop": "Aleteo de guardián an<PERSON>o", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON> anciano herido", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.ender_dragon.flap": "Aleteos de dragón", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON> dispara", "subtitles.entity.ender_eye.death": "<PERSON><PERSON> de ender cae", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> de ender lanzado", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> de ender la<PERSON>", "subtitles.entity.enderman.ambient": "Vwoopeo de end<PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON> muere", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> her<PERSON>", "subtitles.entity.enderman.scream": "Enderman grita", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON>", "subtitles.entity.enderman.teleport": "Enderman se teletransporta", "subtitles.entity.endermite.ambient": "Endermite se arrastra", "subtitles.entity.endermite.death": "Endermite muere", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.evoker.ambient": "Murmuro de invocador", "subtitles.entity.evoker.cast_spell": "Invocador lanza un hechizo", "subtitles.entity.evoker.celebrate": "Invocador festejando", "subtitles.entity.evoker.death": "Invocador muere", "subtitles.entity.evoker.hurt": "Invocador herido", "subtitles.entity.evoker.prepare_attack": "Invocador prepara un ataque", "subtitles.entity.evoker.prepare_summon": "Invocador prepara una invocación", "subtitles.entity.evoker.prepare_wololo": "Invocador prepara un encantamiento", "subtitles.entity.evoker_fangs.attack": "Rotura de colmillos", "subtitles.entity.experience_orb.pickup": "Experiencia obtenida", "subtitles.entity.firework_rocket.blast": "Explosión de fuegos artificiales", "subtitles.entity.firework_rocket.launch": "Lanzamiento de fuegos artificiales", "subtitles.entity.firework_rocket.twinkle": "Titileo de fuegos artificiales", "subtitles.entity.fish.swim": "Salpicadura<PERSON>", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON><PERSON> recuperado", "subtitles.entity.fishing_bobber.splash": "Salpicadura de caña de pescar", "subtitles.entity.fishing_bobber.throw": "Caña de pescar lanzada", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON> furioso", "subtitles.entity.fox.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.bite": "<PERSON><PERSON><PERSON> mordiendo", "subtitles.entity.fox.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.fox.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.fox.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON>", "subtitles.entity.fox.sleep": "<PERSON><PERSON><PERSON> roncando", "subtitles.entity.fox.sniff": "<PERSON><PERSON><PERSON> olfateando", "subtitles.entity.fox.spit": "<PERSON><PERSON><PERSON> escupiendo", "subtitles.entity.fox.teleport": "Zorro se teletransporta", "subtitles.entity.frog.ambient": "<PERSON> croa", "subtitles.entity.frog.death": "<PERSON> muere", "subtitles.entity.frog.eat": "<PERSON> come", "subtitles.entity.frog.hurt": "Rana <PERSON>", "subtitles.entity.frog.lay_spawn": "<PERSON>", "subtitles.entity.frog.long_jump": "<PERSON>a", "subtitles.entity.generic.big_fall": "Algo cae", "subtitles.entity.generic.burn": "Algo se quema", "subtitles.entity.generic.death": "Agonía", "subtitles.entity.generic.drink": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.eat": "Comiendo", "subtitles.entity.generic.explode": "Explosión", "subtitles.entity.generic.extinguish_fire": "Fuego extinguido", "subtitles.entity.generic.hurt": "Algo te hiere", "subtitles.entity.generic.small_fall": "Leve impacto contra el suelo", "subtitles.entity.generic.splash": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.swim": "Na<PERSON>do", "subtitles.entity.generic.wind_burst": "Carga de brisa estalla", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> dispara", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON> tararea", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> a<PERSON>e", "subtitles.entity.glow_item_frame.add_item": "Objeto colocado en un marco luminoso", "subtitles.entity.glow_item_frame.break": "<PERSON> lumi<PERSON>o se rompe", "subtitles.entity.glow_item_frame.place": "<PERSON> lumi<PERSON> colocado", "subtitles.entity.glow_item_frame.remove_item": "Objeto retirado de un marco luminoso", "subtitles.entity.glow_item_frame.rotate_item": "Chasquido de marco luminoso", "subtitles.entity.glow_squid.ambient": "Calamar luminoso nada", "subtitles.entity.glow_squid.death": "Calamar luminoso muere", "subtitles.entity.glow_squid.hurt": "Calamar luminoso herido", "subtitles.entity.glow_squid.squirt": "Calamar luminoso dispara tinta", "subtitles.entity.goat.ambient": "Balido de cabra", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.goat.horn_break": "Cuerno de cabra se desprende", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> herida", "subtitles.entity.goat.long_jump": "<PERSON><PERSON><PERSON> salta", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.prepare_ram": "Cabra se prepara", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON> embis<PERSON>", "subtitles.entity.goat.screaming.ambient": "Bramido <PERSON>", "subtitles.entity.goat.step": "Pasos de cabra", "subtitles.entity.guardian.ambient": "G<PERSON><PERSON><PERSON> guardián", "subtitles.entity.guardian.ambient_land": "Aleteo de guardián", "subtitles.entity.guardian.attack": "Guardián dispara", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.guardian.flop": "Aleteo de guardián", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON>uere", "subtitles.entity.happy_ghast.equip": "Equipar Arnés", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON> esta listo", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON><PERSON> se <PERSON>", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.unequip": "Desequipar Arnés", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.angry": "Gruñ<PERSON> de hoglin furioso", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> se zombifica", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON>n her<PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.step": "Pasos de hoglin", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON> relincha", "subtitles.entity.horse.armor": "Armadura de caballo equipada", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON> respira", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.horse.gallop": "Galopes de caballo", "subtitles.entity.horse.hurt": "Caballo herido", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON> salta", "subtitles.entity.horse.saddle": "Silla equipada", "subtitles.entity.husk.ambient": "Gruñ<PERSON> de zombi momificado", "subtitles.entity.husk.converted_to_zombie": "Zombi se desmomifica", "subtitles.entity.husk.death": "<PERSON><PERSON><PERSON> momificado muere", "subtitles.entity.husk.hurt": "<PERSON><PERSON><PERSON> momificado herido", "subtitles.entity.illusioner.ambient": "Murmuro de ilusionista", "subtitles.entity.illusioner.cast_spell": "Ilusionista lanza un hechizo", "subtitles.entity.illusioner.death": "Ilusionista muere", "subtitles.entity.illusioner.hurt": "Ilusionista herido", "subtitles.entity.illusioner.mirror_move": "Ilusionista se desplaza", "subtitles.entity.illusioner.prepare_blindness": "Ilusionista se prepara para cegar", "subtitles.entity.illusioner.prepare_mirror": "Ilusionista prepara una ilusión", "subtitles.entity.iron_golem.attack": "Gólem de hierro ataca", "subtitles.entity.iron_golem.damage": "Gólem de hierro rom<PERSON>", "subtitles.entity.iron_golem.death": "Gólem de hierro muere", "subtitles.entity.iron_golem.hurt": "Gólem de hierro herido", "subtitles.entity.iron_golem.repair": "Gólem de hierro reparándose", "subtitles.entity.item.break": "Objet<PERSON> destruido", "subtitles.entity.item.pickup": "Objeto recogido", "subtitles.entity.item_frame.add_item": "Objeto colocado en un marco", "subtitles.entity.item_frame.break": "<PERSON>", "subtitles.entity.item_frame.place": "<PERSON>", "subtitles.entity.item_frame.remove_item": "Objeto retirado de un marco", "subtitles.entity.item_frame.rotate_item": "Chasquido de ma<PERSON>o", "subtitles.entity.leash_knot.break": "Nudo de la rienda se rompe", "subtitles.entity.leash_knot.place": "Nudo de la rienda atada", "subtitles.entity.lightning_bolt.impact": "Rayos", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON>", "subtitles.entity.llama.ambient": "<PERSON><PERSON>", "subtitles.entity.llama.angry": "Balido de llama furiosa", "subtitles.entity.llama.chest": "Baúl equipado a una llama", "subtitles.entity.llama.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.llama.eat": "<PERSON><PERSON><PERSON> come", "subtitles.entity.llama.hurt": "<PERSON><PERSON><PERSON> herida", "subtitles.entity.llama.spit": "Llama escupe", "subtitles.entity.llama.step": "Pasos de llama", "subtitles.entity.llama.swag": "Llama decorada", "subtitles.entity.magma_cube.death": "Cubo de magma muere", "subtitles.entity.magma_cube.hurt": "Cubo de magma herido", "subtitles.entity.magma_cube.squish": "Cubo de magma saltando", "subtitles.entity.minecart.inside": "Vagoneta chirría", "subtitles.entity.minecart.inside_underwater": "Vagoneta chirría bajo el agua", "subtitles.entity.minecart.riding": "Vagón en movimiento", "subtitles.entity.mooshroom.convert": "Mooshroom se transforma", "subtitles.entity.mooshroom.eat": "Mooshroom come", "subtitles.entity.mooshroom.milk": "Mooshroom ordeñada", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom ordeñada con recelo", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON> de <PERSON>ula", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON> de <PERSON>ula", "subtitles.entity.mule.chest": "<PERSON>úl equipado a una mula", "subtitles.entity.mule.death": "<PERSON><PERSON> muere", "subtitles.entity.mule.eat": "<PERSON><PERSON> come", "subtitles.entity.mule.hurt": "<PERSON><PERSON> herida", "subtitles.entity.mule.jump": "<PERSON><PERSON> salta", "subtitles.entity.painting.break": "Cuadro destruido", "subtitles.entity.painting.place": "Cuadro colocado", "subtitles.entity.panda.aggressive_ambient": "Panda resoplando", "subtitles.entity.panda.ambient": "<PERSON><PERSON><PERSON>da", "subtitles.entity.panda.bite": "<PERSON>da mordiendo", "subtitles.entity.panda.cant_breed": "<PERSON><PERSON> balando", "subtitles.entity.panda.death": "<PERSON>da muere", "subtitles.entity.panda.eat": "Panda come", "subtitles.entity.panda.hurt": "Panda herido", "subtitles.entity.panda.pre_sneeze": "Cosquilleo en la nariz de un panda", "subtitles.entity.panda.sneeze": "Panda estorn<PERSON>ndo", "subtitles.entity.panda.step": "Pasos de panda", "subtitles.entity.panda.worried_ambient": "Gruñido de <PERSON>", "subtitles.entity.parrot.ambient": "<PERSON><PERSON> hablando", "subtitles.entity.parrot.death": "<PERSON><PERSON> muere", "subtitles.entity.parrot.eats": "<PERSON>ro come", "subtitles.entity.parrot.fly": "<PERSON><PERSON> vuela", "subtitles.entity.parrot.hurts": "<PERSON>ro herido", "subtitles.entity.parrot.imitate.blaze": "<PERSON>ro imita a un blaze", "subtitles.entity.parrot.imitate.bogged": "Loro imita a un esqueleto pantanoso", "subtitles.entity.parrot.imitate.breeze": "Loro imita a un breeze", "subtitles.entity.parrot.imitate.creaking": "Crujidos de loro", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON> imita a un creeper", "subtitles.entity.parrot.imitate.drowned": "<PERSON>ro imita a un ahogado", "subtitles.entity.parrot.imitate.elder_guardian": "Loro imita a un guardián", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON>ro imita al dragón", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON> imita a un endermite", "subtitles.entity.parrot.imitate.evoker": "<PERSON>ro imita a un invocador", "subtitles.entity.parrot.imitate.ghast": "<PERSON>ro imita a un ghast", "subtitles.entity.parrot.imitate.guardian": "Loro gime como un guardián", "subtitles.entity.parrot.imitate.hoglin": "Loro imita a un hoglin", "subtitles.entity.parrot.imitate.husk": "Loro imita a un zombi momificado", "subtitles.entity.parrot.imitate.illusioner": "Loro murmura como un ilusionista", "subtitles.entity.parrot.imitate.magma_cube": "Loro imita a un cubo de magma", "subtitles.entity.parrot.imitate.phantom": "<PERSON>ro imita a un phantom", "subtitles.entity.parrot.imitate.piglin": "<PERSON>ro imita a un piglin", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON>ro imita a un Piglin", "subtitles.entity.parrot.imitate.pillager": "Loro murmura como un saqueador", "subtitles.entity.parrot.imitate.ravager": "Loro imita a un devastador", "subtitles.entity.parrot.imitate.shulker": "<PERSON>ro imita a un shulker", "subtitles.entity.parrot.imitate.silverfish": "Loro sisea como un pececillo de plata", "subtitles.entity.parrot.imitate.skeleton": "Loro imita a un esqueleto", "subtitles.entity.parrot.imitate.slime": "Loro imita a un slime", "subtitles.entity.parrot.imitate.spider": "Loro imita a una araña", "subtitles.entity.parrot.imitate.stray": "Loro imita a un esqueleto glacial", "subtitles.entity.parrot.imitate.vex": "Loro imita a un vex", "subtitles.entity.parrot.imitate.vindicator": "Loro imita a un vindicador", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON> imita a un warden", "subtitles.entity.parrot.imitate.witch": "Loro imita a una bruja", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON> imita al <PERSON>", "subtitles.entity.parrot.imitate.wither_skeleton": "Loro imita a un esqueleto <PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON>ro imita a un zoglin", "subtitles.entity.parrot.imitate.zombie": "<PERSON>ro imita a un zombi", "subtitles.entity.parrot.imitate.zombie_villager": "Loro imita a un aldeano zombi", "subtitles.entity.phantom.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.phantom.bite": "Phantom mordiendo", "subtitles.entity.phantom.death": "Phantom muere", "subtitles.entity.phantom.flap": "Phantom aleteando", "subtitles.entity.phantom.hurt": "Phantom herido", "subtitles.entity.phantom.swoop": "Phantom se acerca", "subtitles.entity.pig.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.pig.hurt": "<PERSON><PERSON>o her<PERSON>", "subtitles.entity.pig.saddle": "Silla equipada", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> admirando un ítem", "subtitles.entity.piglin.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.piglin.angry": "<PERSON><PERSON><PERSON><PERSON> de piglin furioso", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> f<PERSON>", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> se zombifica", "subtitles.entity.piglin.death": "<PERSON><PERSON> muere", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> her<PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON><PERSON><PERSON> de piglin con envidia", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> re<PERSON>", "subtitles.entity.piglin.step": "Pasos de piglin", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON><PERSON><PERSON> de piglin brutal furioso", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON><PERSON> brutal se zombifica", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> brutal muere", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> brutal herido", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON> de <PERSON>lin brutal", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.pillager.celebrate": "Saqueador celebra", "subtitles.entity.pillager.death": "<PERSON>que<PERSON> muere", "subtitles.entity.pillager.hurt": "Saqueador herido", "subtitles.entity.player.attack.crit": "Ataque crítico", "subtitles.entity.player.attack.knockback": "Ataque con empuje", "subtitles.entity.player.attack.strong": "Ataque fuerte", "subtitles.entity.player.attack.sweep": "Ataque con barrido", "subtitles.entity.player.attack.weak": "Ataque débil", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON> repite", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.player.freeze_hurt": "Jugador se congela", "subtitles.entity.player.hurt": "Jugador(a) herido/a", "subtitles.entity.player.hurt_drown": "Jugado<PERSON>(a) se ahoga", "subtitles.entity.player.hurt_on_fire": "Jugador(a) se quema", "subtitles.entity.player.levelup": "Un jugador sube de nivel", "subtitles.entity.player.teleport": "Jugador se teletransporta", "subtitles.entity.polar_bear.ambient": "Gruñ<PERSON> de <PERSON>", "subtitles.entity.polar_bear.ambient_baby": "<PERSON>umbid<PERSON> de o<PERSON>", "subtitles.entity.polar_bear.death": "<PERSON>so polar muere", "subtitles.entity.polar_bear.hurt": "Oso polar herido", "subtitles.entity.polar_bear.warning": "<PERSON>so polar rugiendo", "subtitles.entity.potion.splash": "Frasco roto", "subtitles.entity.potion.throw": "<PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_out": "Pez globo se desinfla", "subtitles.entity.puffer_fish.blow_up": "Pez globo se infla", "subtitles.entity.puffer_fish.death": "Pez globo muere", "subtitles.entity.puffer_fish.flop": "Pez globo aleteando", "subtitles.entity.puffer_fish.hurt": "Pez globo herido", "subtitles.entity.puffer_fish.sting": "Pez globo picando", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON>o ataca", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON>o her<PERSON>", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON> salta", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.attack": "Devast<PERSON> mordiendo", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.death": "Devast<PERSON> muere", "subtitles.entity.ravager.hurt": "Devastador herido", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.step": "Pasos de devastador", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON>", "subtitles.entity.salmon.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.salmon.flop": "Salmón aleteando", "subtitles.entity.salmon.hurt": "Salmón herido", "subtitles.entity.sheep.ambient": "Balido de oveja", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.sheep.hurt": "Oveja herida", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> se cierra", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.shulker.open": "<PERSON>lk<PERSON> se abre", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> dispara", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> se teletransporta", "subtitles.entity.shulker_bullet.hit": "Proyectil de shulker explota", "subtitles.entity.shulker_bullet.hurt": "Proyectil de shulker se rompe", "subtitles.entity.silverfish.ambient": "Siseo de pez de plata", "subtitles.entity.silverfish.death": "Pez de plata muere", "subtitles.entity.silverfish.hurt": "Pez de plata herido", "subtitles.entity.skeleton.ambient": "Traqueteo de esqueleto", "subtitles.entity.skeleton.converted_to_stray": "Esqueleto se transforma en esqueleto glacial", "subtitles.entity.skeleton.death": "Esqueleto muere", "subtitles.entity.skeleton.hurt": "Esqueleto herido", "subtitles.entity.skeleton.shoot": "Esqueleto disparando", "subtitles.entity.skeleton_horse.ambient": "<PERSON><PERSON><PERSON> de caballo esquelético", "subtitles.entity.skeleton_horse.death": "<PERSON><PERSON><PERSON> esquelé<PERSON>o muere", "subtitles.entity.skeleton_horse.hurt": "C<PERSON>llo esquelético herido", "subtitles.entity.skeleton_horse.jump_water": "Salto de caballo esquelético", "subtitles.entity.skeleton_horse.swim": "Caballo esquelético nada", "subtitles.entity.slime.attack": "Slime ataca", "subtitles.entity.slime.death": "Slime muere", "subtitles.entity.slime.hurt": "Slime herido", "subtitles.entity.slime.squish": "Slime saltando", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.sniffer.digging": "Sniffer cava", "subtitles.entity.sniffer.digging_stop": "<PERSON>ni<PERSON> se levanta", "subtitles.entity.sniffer.drop_seed": "<PERSON>niffer suelta una semilla", "subtitles.entity.sniffer.eat": "Sniffer come", "subtitles.entity.sniffer.egg_crack": "<PERSON><PERSON> de sniffer se quiebra", "subtitles.entity.sniffer.egg_hatch": "Huevo de sniffer eclosiona", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON> se deleita", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON> herido", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON> huele", "subtitles.entity.sniffer.searching": "Sniffer busca", "subtitles.entity.sniffer.sniffing": "Sniffer olfatea", "subtitles.entity.sniffer.step": "Pasos de sniffer", "subtitles.entity.snow_golem.death": "Gólem de nieve muere", "subtitles.entity.snow_golem.hurt": "Gólem de nieve herido", "subtitles.entity.snowball.throw": "Bola de nieve lanzada", "subtitles.entity.spider.ambient": "Siseo de araña", "subtitles.entity.spider.death": "<PERSON><PERSON> muere", "subtitles.entity.spider.hurt": "<PERSON><PERSON> herida", "subtitles.entity.squid.ambient": "Calamar nada", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.squid.squirt": "Calamar dispara tinta", "subtitles.entity.stray.ambient": "Traqueteo de esqueleto glacial", "subtitles.entity.stray.death": "Esqueleto glacial muere", "subtitles.entity.stray.hurt": "Esqueleto glacial herido", "subtitles.entity.strider.death": "Lavagante muere", "subtitles.entity.strider.eat": "Lavagante come", "subtitles.entity.strider.happy": "Lavagante canta", "subtitles.entity.strider.hurt": "Lavagante herido", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.retreat": "Lavagante retrocede", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON> muere", "subtitles.entity.tadpole.flop": "Renacuajo nada", "subtitles.entity.tadpole.grow_up": "Renacuajo crece", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.tnt.primed": "<PERSON><PERSON> de dinami<PERSON>", "subtitles.entity.tropical_fish.death": "Pez tropical muere", "subtitles.entity.tropical_fish.flop": "Pez tropical chapotea", "subtitles.entity.tropical_fish.hurt": "Pez tropical herido", "subtitles.entity.turtle.ambient_land": "Gruñido de tortuga", "subtitles.entity.turtle.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.turtle.death_baby": "Cría de tortuga muere", "subtitles.entity.turtle.egg_break": "Huevo de tortuga se rompe", "subtitles.entity.turtle.egg_crack": "Huevo de tortuga se quiebra", "subtitles.entity.turtle.egg_hatch": "Huevo de tortuga eclosiona", "subtitles.entity.turtle.hurt": "<PERSON><PERSON><PERSON> herida", "subtitles.entity.turtle.hurt_baby": "Cría de tortuga herida", "subtitles.entity.turtle.lay_egg": "Tortuga poniendo un huevo", "subtitles.entity.turtle.shamble": "Tortuga reptando", "subtitles.entity.turtle.shamble_baby": "Cría de tortuga reptando", "subtitles.entity.turtle.swim": "Tortuga nada", "subtitles.entity.vex.ambient": "Ruido de vex", "subtitles.entity.vex.charge": "<PERSON><PERSON><PERSON> de vex", "subtitles.entity.vex.death": "Vex muere", "subtitles.entity.vex.hurt": "Vex herido", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.celebrate": "Aldeano festejando", "subtitles.entity.villager.death": "Aldeano muere", "subtitles.entity.villager.hurt": "Aldeano herido", "subtitles.entity.villager.no": "Aldeano no acepta", "subtitles.entity.villager.trade": "Aldeano intercambia", "subtitles.entity.villager.work_armorer": "Herrero de armaduras trabajando", "subtitles.entity.villager.work_butcher": "Carnicero trabajando", "subtitles.entity.villager.work_cartographer": "Cartógrafo t<PERSON>ajando", "subtitles.entity.villager.work_cleric": "Sacerdote trabajando", "subtitles.entity.villager.work_farmer": "Granjero trabajando", "subtitles.entity.villager.work_fisherman": "Pescador trabajando", "subtitles.entity.villager.work_fletcher": "Flechero trabajando", "subtitles.entity.villager.work_leatherworker": "Peletero trabajando", "subtitles.entity.villager.work_librarian": "Bibliotecario trabajando", "subtitles.entity.villager.work_mason": "<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_shepherd": "<PERSON>", "subtitles.entity.villager.work_toolsmith": "Herrero de herramientas trabajando", "subtitles.entity.villager.work_weaponsmith": "Herrero de armas trabajando", "subtitles.entity.villager.yes": "Aldeano acepta", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON> de vindicador", "subtitles.entity.vindicator.celebrate": "Vindicador celebrando", "subtitles.entity.vindicator.death": "Vindicador muere", "subtitles.entity.vindicator.hurt": "Vindicador herido", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON> de comerciante nómada", "subtitles.entity.wandering_trader.death": "Comerciante nómada muere", "subtitles.entity.wandering_trader.disappeared": "Comerciante nómada desaparece", "subtitles.entity.wandering_trader.drink_milk": "Comerciante nómada toma leche", "subtitles.entity.wandering_trader.drink_potion": "Comerciante nómada toma una poción", "subtitles.entity.wandering_trader.hurt": "Comerciante nómada herido", "subtitles.entity.wandering_trader.no": "Comerciante nómada rechazando", "subtitles.entity.wandering_trader.reappeared": "Comerciante nómada aparece", "subtitles.entity.wandering_trader.trade": "Comerciante nómada negociando", "subtitles.entity.wandering_trader.yes": "Comerciante nómada aceptando", "subtitles.entity.warden.agitated": "<PERSON> gru<PERSON>e furioso", "subtitles.entity.warden.ambient": "<PERSON>", "subtitles.entity.warden.angry": "Warden se enfurece", "subtitles.entity.warden.attack_impact": "Warden as<PERSON> un golpe", "subtitles.entity.warden.death": "<PERSON> muere", "subtitles.entity.warden.dig": "Warden cava", "subtitles.entity.warden.emerge": "Warden emerge", "subtitles.entity.warden.heartbeat": "Corazón de warden palpita", "subtitles.entity.warden.hurt": "Warden herido", "subtitles.entity.warden.listening": "<PERSON> se da cuenta", "subtitles.entity.warden.listening_angry": "<PERSON> se da cuenta furioso", "subtitles.entity.warden.nearby_close": "Warden se acerca", "subtitles.entity.warden.nearby_closer": "Warden a<PERSON>", "subtitles.entity.warden.nearby_closest": "Warden se aproxima", "subtitles.entity.warden.roar": "<PERSON><PERSON><PERSON> de warden", "subtitles.entity.warden.sniff": "<PERSON><PERSON><PERSON><PERSON> de warden", "subtitles.entity.warden.sonic_boom": "Warden resonando", "subtitles.entity.warden.sonic_charge": "Warden carga", "subtitles.entity.warden.step": "Pasos de warden", "subtitles.entity.warden.tendril_clicks": "Zarcillos de warden chasquean", "subtitles.entity.wind_charge.throw": "Carga de brisa lanzada", "subtitles.entity.wind_charge.wind_burst": "Carga de brisa estalla", "subtitles.entity.witch.ambient": "<PERSON><PERSON>", "subtitles.entity.witch.celebrate": "Bruja feste<PERSON>do", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.witch.drink": "B<PERSON>ja to<PERSON>o", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON> herida", "subtitles.entity.witch.throw": "Bruja tira pociones", "subtitles.entity.wither.ambient": "Wither furioso", "subtitles.entity.wither.death": "<PERSON><PERSON> muere", "subtitles.entity.wither.hurt": "Wither herido", "subtitles.entity.wither.shoot": "Wither ataca", "subtitles.entity.wither.spawn": "Wither invocado", "subtitles.entity.wither_skeleton.ambient": "Traqueteo de esqueleto <PERSON>er", "subtitles.entity.wither_skeleton.death": "Esqueleto del Wither muere", "subtitles.entity.wither_skeleton.hurt": "Esqueleto del Wither herido", "subtitles.entity.wolf.ambient": "<PERSON><PERSON>", "subtitles.entity.wolf.bark": "Lobo ladra", "subtitles.entity.wolf.death": "Lobo muere", "subtitles.entity.wolf.growl": "Gruñ<PERSON> de <PERSON>", "subtitles.entity.wolf.hurt": "Lobo herido", "subtitles.entity.wolf.pant": "Lobo jadea", "subtitles.entity.wolf.shake": "Lobo sacudiéndose", "subtitles.entity.wolf.whine": "Lobo gimotea", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> ataca", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> her<PERSON>", "subtitles.entity.zoglin.step": "Pasos de z<PERSON>lin", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON> go<PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON><PERSON> destruida", "subtitles.entity.zombie.converted_to_drowned": "<PERSON><PERSON><PERSON> se ahoga", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON> muere", "subtitles.entity.zombie.destroy_egg": "Huevo de tortuga pisado", "subtitles.entity.zombie.hurt": "<PERSON><PERSON>i herido", "subtitles.entity.zombie.infect": "Zombi infectando", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON><PERSON> de caballo zombi", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON>llo zombi muere", "subtitles.entity.zombie_horse.hurt": "Caballo zombi herido", "subtitles.entity.zombie_villager.ambient": "Gru<PERSON><PERSON> de aldeano z<PERSON>i", "subtitles.entity.zombie_villager.converted": "Aldeano zombi agoniza", "subtitles.entity.zombie_villager.cure": "<PERSON><PERSON> z<PERSON>i", "subtitles.entity.zombie_villager.death": "Aldeano zombi muere", "subtitles.entity.zombie_villager.hurt": "Aldeano zombi herido", "subtitles.entity.zombified_piglin.ambient": "Gruñ<PERSON> de piglin zombificado", "subtitles.entity.zombified_piglin.angry": "Gruñ<PERSON> de piglin zombificado furioso", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON> zombificado muere", "subtitles.entity.zombified_piglin.hurt": "<PERSON><PERSON> zombificado herido", "subtitles.event.mob_effect.bad_omen": "Un presagio se manifiesta", "subtitles.event.mob_effect.raid_omen": "Se acerca una Invasión", "subtitles.event.mob_effect.trial_omen": "Se Acerca un desafio Siniestro", "subtitles.event.raid.horn": "<PERSON><PERSON>", "subtitles.item.armor.equip": "Armadura equipada", "subtitles.item.armor.equip_chain": "Tintineo de armadura de cota de mallas", "subtitles.item.armor.equip_diamond": "Tintineo de armadura de diamante", "subtitles.item.armor.equip_elytra": "Ruido de élitros", "subtitles.item.armor.equip_gold": "Rechine de la armadura de oro", "subtitles.item.armor.equip_iron": "Rechinido de armadura de hierro", "subtitles.item.armor.equip_leather": "Frufrú de armadura de cuero", "subtitles.item.armor.equip_netherite": "Rechinido de armadura de netherita", "subtitles.item.armor.equip_turtle": "Caparazón de tortuga equipado", "subtitles.item.armor.equip_wolf": "Armadura para lobo equipada", "subtitles.item.armor.unequip_wolf": "Armadura para lobo desequipada", "subtitles.item.axe.scrape": "<PERSON><PERSON> raspa", "subtitles.item.axe.strip": "<PERSON><PERSON> remueve corteza", "subtitles.item.axe.wax_off": "<PERSON><PERSON> removida", "subtitles.item.bone_meal.use": "Polvo de hueso cruje", "subtitles.item.book.page_turn": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.book.put": "Libro colocado", "subtitles.item.bottle.empty": "Frasco vaciado", "subtitles.item.bottle.fill": "<PERSON><PERSON>", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "Cepillando gravilla", "subtitles.item.brush.brushing.gravel.complete": "Cepillado de gravilla completado", "subtitles.item.brush.brushing.sand": "Arena cepillada", "subtitles.item.brush.brushing.sand.complete": "Arena completa cepillada", "subtitles.item.bucket.empty": "Balde vaciado", "subtitles.item.bucket.fill": "<PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "Recogiendo Ajolote", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON> capturado", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON><PERSON> capturado", "subtitles.item.bundle.drop_contents": "Bolsa se vacía", "subtitles.item.bundle.insert": "<PERSON>b<PERSON>o guardado", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON>a llena", "subtitles.item.bundle.remove_one": "<PERSON>b<PERSON><PERSON> reti<PERSON>", "subtitles.item.chorus_fruit.teleport": "Jugador teletransportado", "subtitles.item.crop.plant": "<PERSON>lla cultivada", "subtitles.item.crossbow.charge": "Ballesta cargando", "subtitles.item.crossbow.hit": "Flecha golpea", "subtitles.item.crossbow.load": "Ballesta cargada", "subtitles.item.crossbow.shoot": "Tiro de ballesta", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON>", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "Zumbido de bola de fuego", "subtitles.item.flintandsteel.use": "Chasquido de yes<PERSON>o", "subtitles.item.glow_ink_sac.use": "Saco de tinta luminosa mancha", "subtitles.item.goat_horn.play": "Cuerno de cabra suena", "subtitles.item.hoe.till": "Azadón arando", "subtitles.item.honey_bottle.drink": "Tragando", "subtitles.item.honeycomb.wax_on": "Cera aplicada", "subtitles.item.horse_armor.unequip": "Armadura para caballo se suelta", "subtitles.item.ink_sac.use": "Saco de tinta mancha", "subtitles.item.lead.break": "Rienda se rompe", "subtitles.item.lead.tied": "Plomo atado", "subtitles.item.lead.untied": "<PERSON>lo<PERSON>", "subtitles.item.llama_carpet.unequip": "La alfombra se recorta", "subtitles.item.lodestone_compass.lock": "Brújula magnetizada con magnetita", "subtitles.item.mace.smash_air": "<PERSON><PERSON> a<PERSON>", "subtitles.item.mace.smash_ground": "<PERSON><PERSON> a<PERSON>", "subtitles.item.nether_wart.plant": "Verruga plantada", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON> se rompe", "subtitles.item.saddle.unequip": "Silla de montar recorta", "subtitles.item.shears.shear": "Tijeras cortando", "subtitles.item.shears.snip": "Corte de Tijera", "subtitles.item.shield.block": "Bloqueo con escudo", "subtitles.item.shovel.flatten": "Pala aplanando", "subtitles.item.spyglass.stop_using": "Catalejo se retrae", "subtitles.item.spyglass.use": "Catalejo se <PERSON>e", "subtitles.item.totem.use": "Tótem activado", "subtitles.item.trident.hit": "Tridente apuñalando", "subtitles.item.trident.hit_ground": "Tridente vibrando", "subtitles.item.trident.return": "Tridente regresando", "subtitles.item.trident.riptide": "Tridente propulsa", "subtitles.item.trident.throw": "Tridente resonando", "subtitles.item.trident.thunder": "Tridente invocando truenos", "subtitles.item.wolf_armor.break": "Armadura para lobo se rompe", "subtitles.item.wolf_armor.crack": "Armadura para lobo se agrieta", "subtitles.item.wolf_armor.damage": "Armadura para lobo recibe daño", "subtitles.item.wolf_armor.repair": "Armadura para lobo es reparada", "subtitles.particle.soul_escape": "Fuga de alma", "subtitles.ui.cartography_table.take_result": "Mapa dibujado", "subtitles.ui.hud.bubble_pop": "Nivel de oxígeno agotán<PERSON>", "subtitles.ui.loom.take_result": "Telar usado", "subtitles.ui.stonecutter.take_result": "Cortapiedras usado", "subtitles.weather.rain": "<PERSON><PERSON><PERSON>", "symlink_warning.message": "Cargando mundos desde carpetas con enlaces simbólicos puede ser inseguro si no sabes exactamente lo que estás haciendo. Visita %s para más información.", "symlink_warning.message.pack": "Cargando paquetes con enlaces simbólicos puede ser inseguro si no sabes exactamente lo que estás haciendo. Visita %s para más información.", "symlink_warning.message.world": "Cargando mundos desde carpetas con enlaces simbólicos puede ser inseguro si no sabes exactamente lo que estás haciendo. Visita %s para más información.", "symlink_warning.more_info": "Más información", "symlink_warning.title": "La carpeta del mundo contiene enlaces simbólicos", "symlink_warning.title.pack": "<PERSON><PERSON><PERSON>(s) añadido(s) contiene(n) enlaces simbólicos", "symlink_warning.title.world": "La carpeta del mundo contiene enlaces simbólicos", "team.collision.always": "Siempre", "team.collision.never": "Nunca", "team.collision.pushOtherTeams": "Colisión entre jugadores de distinto equipo", "team.collision.pushOwnTeam": "Colisión entre jugadores del mismo equipo", "team.notFound": "Equipo desconocido: %s", "team.visibility.always": "Siempre", "team.visibility.hideForOtherTeams": "Ocultar para los otros equipos", "team.visibility.hideForOwnTeam": "Ocultar para su equipo", "team.visibility.never": "Nunca", "telemetry.event.advancement_made.description": "Comprendiendo el contexto en el que se realiza un progreso nos ayudará a entender y mejorar la progresión del juego.", "telemetry.event.advancement_made.title": "Progreso realizado", "telemetry.event.game_load_times.description": "Este evento puede ayudarnos a determinar donde se necesitan las mejoras de rendimiento, midiendo los tiempos de ejecución de las diferentes fases de arranque.", "telemetry.event.game_load_times.title": "Tiempos de carga del juego", "telemetry.event.optional": "%s (opcional)", "telemetry.event.optional.disabled": "%s (opcional) - Desactivado", "telemetry.event.performance_metrics.description": "Conocer el perfil de rendimiento de Minecraft nos ayuda a mejorar y optimizar el juego para distintas computadoras y sistemas operativos. \nLa versión del juego se incluye para ayudarnos a comparar el rendimiento de nuevas versiones de Minecraft.", "telemetry.event.performance_metrics.title": "Medición de rendimiento", "telemetry.event.required": "%s (necesario)", "telemetry.event.world_load_times.description": "Es importante para nosotros saber el tiempo que se tarda en entrar a un mundo y cómo cambia eso con el tiempo. Por eje<PERSON><PERSON>, cuando añadimos nuevas características o realizamos cambios técnicos significativos, necesitamos ver cuánto impacto tienen en los tiempos de carga.", "telemetry.event.world_load_times.title": "Tiempos de carga de los mundos", "telemetry.event.world_loaded.description": "Saber cómo los jugadores juegan a Minecraft (qué modo de juego, cliente o servidor y versión del juego) nos permite centrarnos en mejorar las partes del juego que más importan.\nEl evento \"Mundo cargado\" está vinculado a el de \"Mundo descargado\" para calcular cuánto ha durado la partida.", "telemetry.event.world_loaded.title": "<PERSON><PERSON> cargado", "telemetry.event.world_unloaded.description": "Este evento está ligado al de «Mundo cargado» para calcular cuánto ha durado la sesión de juego.\nLa duración (en segundos y tics) se mide cuando una sesión de juego termina (saliendo al menú principal, desconectándose de un servidor).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON>", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON><PERSON> (Tics)", "telemetry.property.advancement_id.title": "ID del progreso", "telemetry.property.client_id.title": "ID de cliente", "telemetry.property.client_modded.title": "Cliente modificado", "telemetry.property.dedicated_memory_kb.title": "Memoria dedicada (kB)", "telemetry.property.event_timestamp_utc.title": "Hora del evento (UTC)", "telemetry.property.frame_rate_samples.title": "Muestras de fotogramas por segundo (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON>", "telemetry.property.game_version.title": "Versión del juego", "telemetry.property.launcher_name.title": "Nombre del launcher", "telemetry.property.load_time_bootstrap_ms.title": "Tiempo de inicio (Milisegundos)", "telemetry.property.load_time_loading_overlay_ms.title": "Tiempo en pantalla de carga (Milisegundos)", "telemetry.property.load_time_pre_window_ms.title": "Tiempo antes de que la ventana se abra (milisegundos)", "telemetry.property.load_time_total_time_ms.title": "Tiempo total de carga (Milisegundos)", "telemetry.property.minecraft_session_id.title": "ID de sesión de Minecraft", "telemetry.property.new_world.title": "Mundo nuevo", "telemetry.property.number_of_samples.title": "Cantidad de muestras", "telemetry.property.operating_system.title": "Sistema operativo", "telemetry.property.opt_in.title": "Incribirse", "telemetry.property.platform.title": "Plataforma", "telemetry.property.realms_map_content.title": "Contenido del mapa de Realms (Nombre del minijuego)", "telemetry.property.render_distance.title": "Distancia de renderizado", "telemetry.property.render_time_samples.title": "Muestras de tiempo de renderizado", "telemetry.property.seconds_since_load.title": "Tiempo desde la carga (segundos)", "telemetry.property.server_modded.title": "Servidor modificado", "telemetry.property.server_type.title": "<PERSON><PERSON><PERSON> de servidor", "telemetry.property.ticks_since_load.title": "Tiempo desde que se cargó (en tics)", "telemetry.property.used_memory_samples.title": "Memoria de acceso aleatorio usada", "telemetry.property.user_id.title": "ID de usuario", "telemetry.property.world_load_time_ms.title": "Tiempo de la carga del mundo (milisegundos)", "telemetry.property.world_session_id.title": "ID de sesión de mundo", "telemetry_info.button.give_feedback": "<PERSON>ger<PERSON>", "telemetry_info.button.privacy_statement": "Declaración de privacidad", "telemetry_info.button.show_data": "Abrir mi información", "telemetry_info.opt_in.description": "Permito enviar mis datos de telemetría opcionales", "telemetry_info.property_title": "Información incluida", "telemetry_info.screen.description": "La recolección de datos nos ayuda a mejorar Minecraft guiándonos hacia direcciones relevantes para nuestros jugadores.\nTambién puedes envíar sugerencias adicionales para ayudarnos a mejorar Minecraft.", "telemetry_info.screen.title": "Recolección de datos de telemetría", "test.error.block_property_mismatch": "Se requiere que la propiedad %s sea %s, era %s", "test.error.block_property_missing": "Falta una propiedad de bloque, se requiere que la propiedad %s sea %s", "test.error.entity_property": "La entidad %s falló la prueba: %s", "test.error.entity_property_details": "La entidad %s falló la prueba: %s, se requiere: %s, era: %s", "test.error.expected_block": "Se requiere el bloque %s, se obtuvo %s", "test.error.expected_block_tag": "Se requiere el bloque en #%s, se obtuvo %s", "test.error.expected_container_contents": "El contenedor debe contener: %s", "test.error.expected_container_contents_single": "El contenedor debe contener un único: %s", "test.error.expected_empty_container": "El contenedor debe estar vacío", "test.error.expected_entity": "Se requiere %s", "test.error.expected_entity_around": "Se requiere que %s exista alrededor de %s, %s, %s", "test.error.expected_entity_count": "Se requiere %s entidades del tipo %s, se encontró %s", "test.error.expected_entity_data": "Se requiere que los datos de la entidad sean: %s, eran: %s", "test.error.expected_entity_data_predicate": "Los datos de la entidad no coinciden para %s", "test.error.expected_entity_effect": "Se requiere que %s tenga efecto %s %s", "test.error.expected_entity_having": "El inventario de la entidad debe contener %s", "test.error.expected_entity_holding": "La entidad debe sostener %s", "test.error.expected_entity_in_test": "Se requiere que %s exista en la prueba", "test.error.expected_entity_not_touching": "No se requería que %s tocase %s, %s, %s (relativo: %s, %s, %s)", "test.error.expected_entity_touching": "Se requiere que %s toque %s, %s, %s (relativo: %s, %s, %s)", "test.error.expected_item": "Se requiere un objeto del tipo %s", "test.error.expected_items_count": "Se requieren %s objetos del tipo %s, se encontró %s", "test.error.fail": "Se cumplieron las condiciones de falla", "test.error.invalid_block_type": "Se encontró un tipo de bloque no esperado: %s", "test.error.missing_block_entity": "Falta una entidad de bloque", "test.error.position": "%s en %s, %s, %s (relativo: %s, %s, %s) en el tic %s", "test.error.sequence.condition_already_triggered": "Condición ya activada en %s", "test.error.sequence.condition_not_triggered": "Condición no activada", "test.error.sequence.invalid_tick": "Éxito en tic no válido: se requiere %s", "test.error.sequence.not_completed": "La prueba se terminó antes de que se completara la secuencia", "test.error.set_biome": "No se pudo establecer el bioma para la prueba", "test.error.spawn_failure": "No se pudo crear la entidad %s", "test.error.state_not_equal": "Estado incorrecto. Se requiere %s, era %s", "test.error.structure.failure": "Fallo al colocar la estructura de prueba %s", "test.error.tick": "%s en el tic %s", "test.error.ticking_without_structure": "Prueba de tics antes de colocar la estructura", "test.error.timeout.no_result": "No tuvo éxito o fracasó entre %s tics", "test.error.timeout.no_sequences_finished": "Ninguna secuencia ha acabado en %s ticks", "test.error.too_many_entities": "Se requiere que exista un único %s alrededor de %s, %s, %s, pero se encontró %s", "test.error.unexpected_block": "No se esperaba que el bloque fuese %s", "test.error.unexpected_entity": "No se esperaba que %s existiera", "test.error.unexpected_item": "No se esperaba un objeto del tipo %s", "test.error.unknown": "Error interno desconocido: %s", "test.error.value_not_equal": "Se requiere que %s sea %s, era %s", "test.error.wrong_block_entity": "Tipo incorrecto de entidad de bloque: %s", "test_block.error.missing": "Falta el bloque %s en la estructura de prueba", "test_block.error.too_many": "Demasiados bloques %s", "test_block.invalid_timeout": "Tiempo de espera no válido (%s): debe ser un número positivo de tics", "test_block.message": "Mensaje:", "test_block.mode.accept": "Aceptar", "test_block.mode.fail": "<PERSON>ar", "test_block.mode.log": "Registrar", "test_block.mode.start": "Iniciar", "test_block.mode_info.accept": "Modo aceptar: acepta superación de (parte de) una prueba", "test_block.mode_info.fail": "Modo fallar: falla la prueba", "test_block.mode_info.log": "Modo registrar: registra un mensaje", "test_block.mode_info.start": "Modo iniciar: el punto de inicio de una prueba", "test_instance.action.reset": "Reiniciar y cargar", "test_instance.action.run": "<PERSON><PERSON> y ejecutar", "test_instance.action.save": "Guardar estructura", "test_instance.description.batch": "Lote: %s", "test_instance.description.failed": "Falló: %s", "test_instance.description.function": "Función: %s", "test_instance.description.invalid_id": "Id. de prueba no válida", "test_instance.description.no_test": "No hay una prueba", "test_instance.description.structure": "Estructura: %s", "test_instance.description.type": "Tipo: %s", "test_instance.type.block_based": "Prueba basada en bloques", "test_instance.type.function": "Prueba de función integrada", "test_instance_block.entities": "Entidades:", "test_instance_block.error.no_test": "No se pudo realizar la instancia de prueba en %s, %s, %s porque ha definido una prueba", "test_instance_block.error.no_test_structure": "No se pudo realizar la instancia de prueba en %s, %s, %s porque no ha definido una estructura de prueba", "test_instance_block.error.unable_to_save": "No se pudo guardar la plantilla de estructura de prueba en %s, %s, %s", "test_instance_block.invalid": "[no válido]", "test_instance_block.reset_success": "Reinicio exitoso para la prueba: %s", "test_instance_block.rotation": "Rotación:", "test_instance_block.size": "Tamaño de la estructura de prueba", "test_instance_block.starting": "Se inició la prueba %s", "test_instance_block.test_id": "Identificador de instancia de prueba", "title.32bit.deprecation": "Se ha detectado un sistema de 32 bits. ¡Esto podría impedirte jugar en el futuro, ya que se necesitará un sistema de 64 bits!", "title.32bit.deprecation.realms": "Minecraft requerirá un sistema de 64 bits pronto, lo que podría impedirte jugar o usar Realms en este dispositivo. Necesitarás cancelar manualmente cualquier suscripción de Realms.", "title.32bit.deprecation.realms.check": "No mostrar este mensaje de nuevo", "title.32bit.deprecation.realms.header": "Sistema de 32 bits detectado", "title.credits": "© Copyright Mojang AB. ¡No distribuir!", "title.multiplayer.disabled": "El modo multijugador está desactivado. Por favor, comprueba los ajustes de tu cuenta de Microsoft.", "title.multiplayer.disabled.banned.name": "Debes cambiar tu nombre de usuario antes de jugar en modo multijugador", "title.multiplayer.disabled.banned.permanent": "Tu cuenta fue suspendida permanentemente y no podrás jugar en línea", "title.multiplayer.disabled.banned.temporary": "Tu cuenta fue suspendida temporalmente y no podrás jugar en línea", "title.multiplayer.lan": "Multijugador (LAN/LOCAL)", "title.multiplayer.other": "Multijugador (servidor de terceros)", "title.multiplayer.realms": "Multijugador (Realms)", "title.singleplayer": "Un jugador", "translation.test.args": "%s %s", "translation.test.complex": "Prefijo, %s%2$s otra vez %s y %1$s por ultimo %s y también %1$s otra vez!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "Hola %", "translation.test.invalid2": "Hola %s", "translation.test.none": "¡Hola, mundo!", "translation.test.world": "mundo", "trim_material.minecraft.amethyst": "Material de amatista", "trim_material.minecraft.copper": "Material de cobre", "trim_material.minecraft.diamond": "Material de diamante", "trim_material.minecraft.emerald": "Material de esmeralda", "trim_material.minecraft.gold": "Material de oro", "trim_material.minecraft.iron": "Material de hierro", "trim_material.minecraft.lapis": "Material de Lapislázuli", "trim_material.minecraft.netherite": "Material de Netherite", "trim_material.minecraft.quartz": "Material de cuarzo", "trim_material.minecraft.redstone": "Material de Redstone", "trim_material.minecraft.resin": "Material de resina", "trim_pattern.minecraft.bolt": "Diseño de armadura de remaches", "trim_pattern.minecraft.coast": "Decoración de armadura costera", "trim_pattern.minecraft.dune": "Decoración de armadura de dunas", "trim_pattern.minecraft.eye": "Decoración de armadura de ojos de ender", "trim_pattern.minecraft.flow": "Diseño de armadura de espiral", "trim_pattern.minecraft.host": "Diseño de armadura de anfitrión", "trim_pattern.minecraft.raiser": "Diseño de armadura de elevación", "trim_pattern.minecraft.rib": "Decoración de armadura de esqueleto de wither", "trim_pattern.minecraft.sentry": "Decoración de armadura centinela", "trim_pattern.minecraft.shaper": "Diseño de armadura de moldeador", "trim_pattern.minecraft.silence": "Diseño de armadura silenciosa", "trim_pattern.minecraft.snout": "Decoración de armadura de piglin", "trim_pattern.minecraft.spire": "Decoración de armadura de aguja", "trim_pattern.minecraft.tide": "Decoración de armadura de marea", "trim_pattern.minecraft.vex": "Decoración de armadura de vex", "trim_pattern.minecraft.ward": "Decoración de armadura de guardián", "trim_pattern.minecraft.wayfinder": "Diseño de armadura de buscacaminos", "trim_pattern.minecraft.wild": "Decoración de armadura salvaje", "tutorial.bundleInsert.description": "Clic derecho para añadir objetos", "tutorial.bundleInsert.title": "Usa una bolsa", "tutorial.craft_planks.description": "Mira el libro de recetas.", "tutorial.craft_planks.title": "¡Fabrica madera!", "tutorial.find_tree.description": "Golpea su tronco.", "tutorial.find_tree.title": "¡Encuentra un árbol!", "tutorial.look.description": "Para ello, usa el mouse.", "tutorial.look.title": "¡Mira a tu alrededor!", "tutorial.move.description": "Salta con %s.", "tutorial.move.title": "¡Camina con %s, %s, %s y %s!", "tutorial.open_inventory.description": "Presiona %s.", "tutorial.open_inventory.title": "¡Abre tu inventario!", "tutorial.punch_tree.description": "Mantén %s.", "tutorial.punch_tree.title": "¡Destruye el árbol!", "tutorial.socialInteractions.description": "Presiona %s para abrir", "tutorial.socialInteractions.title": "Interacciones sociales", "upgrade.minecraft.netherite_upgrade": "Me<PERSON>ra de netherita"}