{"accessibility.onboarding.accessibility.button": "Impostazions acès facilitât...", "accessibility.onboarding.screen.narrator": "Frache <PERSON> par abilitâ il naratôr", "accessibility.onboarding.screen.title": "Ti din il benvignût su Minecraft!\n\nVuelistu abilitâ il naratôr o modificâ lis impostazions di acès facilitât?", "addServer.add": "Fat", "addServer.enterIp": "<PERSON><PERSON><PERSON> dal ser<PERSON>", "addServer.enterName": "Non dal servidôr", "addServer.resourcePack": "Pachets risorsis dal servid<PERSON>r", "addServer.resourcePack.disabled": "Disabilitât", "addServer.resourcePack.enabled": "Abilitât", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON>", "addServer.title": "Modifiche lis informazions dal servidôr", "advMode.command": "<PERSON><PERSON><PERSON> de <PERSON>", "advMode.mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.mode.auto": "A ripetizion", "advMode.mode.autoexec.bat": "<PERSON><PERSON><PERSON> at<PERSON>f", "advMode.mode.conditional": "Condizionâl", "advMode.mode.redstone": "A impuls", "advMode.mode.redstoneTriggered": "E covente redstone", "advMode.mode.sequence": "Cjadene", "advMode.mode.unconditional": "Incondizionâl", "advMode.notAllowed": "<PERSON><PERSON>ugne jessi un operadôr in modalitât creative", "advMode.notEnabled": "I blocs di comant no son abilit<PERSON>ts in chest servidôr", "advMode.previousOutput": "Esit precedent", "advMode.setCommand": "Stabilìs il comant de console pal bloc", "advMode.setCommand.success": "Comant assegnât: %s", "advMode.trackOutput": "Mostre il risultât", "advMode.triggering": "Ativazion", "advMode.type": "<PERSON><PERSON><PERSON>", "advancement.advancementNotFound": "Progrès no cognossût: %s", "advancements.adventure.adventuring_time.description": "Discuvierç ducj i biomis", "advancements.adventure.adventuring_time.title": "Lin che o varìn furtune!", "advancements.adventure.arbalistic.description": "Cope cinc creaturis diviersis cuntun colp sôl di balestre", "advancements.adventure.arbalistic.title": "Il mistîr di trai cu la balestre", "advancements.adventure.avoid_vibration.description": "<PERSON><PERSON><PERSON><PERSON> dongje di un sensôr di Sculk o di une vuardie par no fâti sintî", "advancements.adventure.avoid_vibration.title": "Furtivitât 100", "advancements.adventure.blowback.description": "<PERSON> une brise cu la so cjarie di aiar", "advancements.adventure.blowback.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.brush_armadillo.description": "Oten un scus di un armadil cuntun sborfin", "advancements.adventure.brush_armadillo.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.bullseye.description": "<PERSON><PERSON><PERSON> il centri dal bersai di une distance di 30 metris", "advancements.adventure.bullseye.title": "Centri!", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Fâs un vâs decorât cun cuatri creps", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Restaur met<PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "<PERSON><PERSON><PERSON><PERSON> dongje di un fabricadôr cuant che al fabriche un fabricadôr", "advancements.adventure.crafters_crafting_crafters.title": "Fabricadôrs a fabrichin fabricadôrs", "advancements.adventure.fall_from_world_height.description": "Butiti jù de cime dal mont (limit di costruzion) fin tal pont plui bas dal mont cence murî", "advancements.adventure.fall_from_world_height.title": "Landris e scoieris", "advancements.adventure.heart_transplanter.description": "Met un cûr dal cric, cul inlineament just, jenfri doi blocs di tronc ​​di rôl palit", "advancements.adventure.heart_transplanter.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.hero_of_the_village.description": "Protêç un paîs di une incursion", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON> dal paîs", "advancements.adventure.honey_block_slide.description": "Salte e sbrisse suntun bloc di mîl par morestâ une colade", "advancements.adventure.honey_block_slide.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.kill_a_mob.description": "<PERSON> un mostri", "advancements.adventure.kill_a_mob.title": "Cjaçadôr di mostris", "advancements.adventure.kill_all_mobs.description": "Cope almancul une volte ducj i gjenars di mostris ostîi", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "<PERSON> une creature dongje di un catalizadôr di Sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Sanc catalizadôr", "advancements.adventure.lighten_up.description": "Russe une lampade di ram cuntune manarie par fale deventâ plui lusorose", "advancements.adventure.lighten_up.title": "<PERSON><PERSON><PERSON> lis ideis", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Pare un paisan di une scosse malvolude cence fâ cjapâ fûc", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Alte tension", "advancements.adventure.minecraft_trials_edition.description": "<PERSON>tre intune stanzie de prove", "advancements.adventure.minecraft_trials_edition.title": "Sfide acetade!", "advancements.adventure.ol_betsy.description": "Trai cuntune balestre", "advancements.adventure.ol_betsy.title": "La vecje Betsy", "advancements.adventure.overoverkill.description": "Fâs 50 cûrs di dam cuntun sôl colp cu la mace", "advancements.adventure.overoverkill.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.play_jukebox_in_meadows.description": "Dai vite a une pradarie cun la musiche di un zirediscs", "advancements.adventure.play_jukebox_in_meadows.title": "Suns di musiche", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Controle la potence dal segnâl di une librarie ceselade cuntun comparadôr", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "La potence dai libris", "advancements.adventure.revaulting.description": "<PERSON><PERSON><PERSON><PERSON> une cassefuarte dal malauguri cuntune clâf de prove dal malauguri", "advancements.adventure.revaulting.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.root.description": "Aventure, esplorazion e combatiment", "advancements.adventure.root.title": "Aventure", "advancements.adventure.salvage_sherd.description": "Scovete un bloc suspiet par otignî un crep", "advancements.adventure.salvage_sherd.title": "Rispietâ i reperts", "advancements.adventure.shoot_arrow.description": "Beche alc cuntune frece", "advancements.adventure.shoot_arrow.title": "Ce smire!", "advancements.adventure.sleep_in_bed.description": "Duar intun jet par cambiâ il to pont di rinassite", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON>e gnot", "advancements.adventure.sniper_duel.description": "Cope un scheletri di almancul 50 metris di distance", "advancements.adventure.sniper_duel.title": "Duel tra tiradôrs francs", "advancements.adventure.spyglass_at_dragon.description": "<PERSON><PERSON><PERSON> un drâc dal End cuntun canocjâl", "advancements.adventure.spyglass_at_dragon.title": "Isal un avion?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON><PERSON><PERSON> un ghast cuntun canoc<PERSON>âl", "advancements.adventure.spyglass_at_ghast.title": "Isal un balon?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON><PERSON><PERSON> un pampagal cuntun canocjâl", "advancements.adventure.spyglass_at_parrot.title": "Isal un uciel?", "advancements.adventure.summon_iron_golem.description": "Cree un golem di fier par difindi un paîs", "advancements.adventure.summon_iron_golem.title": "Robocop", "advancements.adventure.throw_trident.description": "Tire une fossigne cuintri alc.\nFâs câs: butâ vie la tô uniche arme no je une biele idee.", "advancements.adventure.throw_trident.title": "Trai cence munizions", "advancements.adventure.totem_of_undying.description": "Dopre un totem de imortalitât par scjampâ de muart", "advancements.adventure.totem_of_undying.title": "No je la mê ore!", "advancements.adventure.trade.description": "<PERSON><PERSON>ie cuntun paisan", "advancements.adventure.trade.title": "<PERSON>", "advancements.adventure.trade_at_world_height.description": "Comercie cuntun paisan al limit superiôr di costruzion dal mont", "advancements.adventure.trade_at_world_height.title": "Vendidôr astronomic", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Apliche almancul une volte chescj modei: spicis, music, cuestis, vuardie, cidin, vex, maree, esplorad<PERSON>r", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON> cun stîl", "advancements.adventure.trim_with_any_armor_pattern.description": "Cree une armadure cuntun ornament doprant un banc par fari", "advancements.adventure.trim_with_any_armor_pattern.title": "Fabricâ un gnûf stîl", "advancements.adventure.two_birds_one_arrow.description": "Cope doi fantasimis cuntune frece traforant", "advancements.adventure.two_birds_one_arrow.title": "Doi colomps cuntune frece", "advancements.adventure.under_lock_and_key.description": "Do<PERSON>re une clâf de prove su di une cassefuarte", "advancements.adventure.under_lock_and_key.title": "Sot clâf", "advancements.adventure.use_lodestone.description": "Dopre une bussule suntun bloc di magnetite", "advancements.adventure.use_lodestone.title": "Bussule magnetizade, direzion sigurade", "advancements.adventure.very_very_frightening.description": "Colpìs un paisan cuntun fulmin", "advancements.adventure.very_very_frightening.title": "Une vore paurôs", "advancements.adventure.voluntary_exile.description": "Cope il cjapitani di une patulie o di militârs avanzâts.\nPar un pôc al è il câs di stâ lontan dai paîs...", "advancements.adventure.voluntary_exile.title": "Esili volontari", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Cjamine su la nêf farinose... cence sprofondâ", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON><PERSON> come un cunin", "advancements.adventure.who_needs_rockets.description": "Dopre une cjarie di aiar par saltâ adalt di 8 blocs", "advancements.adventure.who_needs_rockets.title": "Cui aial dibisugne des fusetis?", "advancements.adventure.whos_the_pillager_now.description": "Tornii la farine a un sachizadôr", "advancements.adventure.whos_the_pillager_now.title": "<PERSON>ui isal cumò il sachizadôr?", "advancements.empty": "Al pâr che nol sedi nuie achì...", "advancements.end.dragon_breath.description": "Met intune butilie il flât di drâc", "advancements.end.dragon_breath.title": "Ti pucial il flât?", "advancements.end.dragon_egg.description": "<PERSON><PERSON><PERSON> l'<PERSON> dal drâc", "advancements.end.dragon_egg.title": "La gnove gjenerazion", "advancements.end.elytra.description": "<PERSON><PERSON><PERSON> lis elitris", "advancements.end.elytra.title": "Viers la cape dal cîl e là di là", "advancements.end.enter_end_gateway.description": "Scjampe de isule principâl", "advancements.end.enter_end_gateway.title": "Isal achì il passaç?", "advancements.end.find_end_city.description": "Jentre mo! Ce podaraial mai lâ strucj?", "advancements.end.find_end_city.title": "Citât al tiermin dal zûc", "advancements.end.kill_dragon.description": "Buine fortune", "advancements.end.kill_dragon.title": "End liberât", "advancements.end.levitate.description": "Alciti di 50 blocs par vie dai colps di un shulker", "advancements.end.levitate.title": "Ce biele viodude di ca sù!", "advancements.end.respawn_dragon.description": "Evoche di gnûf il drâc dal End", "advancements.end.respawn_dragon.title": "Cualchi volte a tornin", "advancements.end.root.description": "Ise pardabon la fin?", "advancements.end.root.title": "L'End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Fâs in mût che un slizeridôr al moli une torte su di un bloc sonôr", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Bon complean!", "advancements.husbandry.allay_deliver_item_to_player.description": "Fâti puartâ un ogjet di un slizeridôr", "advancements.husbandry.allay_deliver_item_to_player.title": "Tu âs un amì in me", "advancements.husbandry.axolotl_in_a_bucket.description": "Cjape un axolotl cuntun seglot", "advancements.husbandry.axolotl_in_a_bucket.title": "Il predatôr plui ninin", "advancements.husbandry.balanced_diet.description": "<PERSON>gje dut ce che si pues mangjâ, ancje ce che nol fâs ben", "advancements.husbandry.balanced_diet.title": "Une diete ecuilibrade", "advancements.husbandry.breed_all_animals.description": "Acompagne dôs bestiis par ogni specie!", "advancements.husbandry.breed_all_animals.title": "Doi par volte", "advancements.husbandry.breed_an_animal.description": "Incrose dôs bestiis de stesse specie", "advancements.husbandry.breed_an_animal.title": "Si respire amôr us gnot", "advancements.husbandry.complete_catalogue.description": "Mugne<PERSON><PERSON> ducj i gjenars di gjat!", "advancements.husbandry.complete_catalogue.title": "Il catalic dai gjats", "advancements.husbandry.feed_snifflet.description": "Dai di mangjâ a di un piçul di nasadôr", "advancements.husbandry.feed_snifflet.title": "Piçule nasade", "advancements.husbandry.fishy_business.description": "Pescje un pes", "advancements.husbandry.fishy_business.title": "Graciis par dut il pes", "advancements.husbandry.froglights.description": "Rive a vê dutis lis crot-lûs tal to inventari", "advancements.husbandry.froglights.title": "Il podê dal tricolôr!", "advancements.husbandry.kill_axolotl_target.description": "Fâs scuadre cuntun axolotl e vinç un combatiment", "advancements.husbandry.kill_axolotl_target.title": "La amicizie e cure ogni mâl!", "advancements.husbandry.leash_all_frog_variants.description": "<PERSON>ee ducj i gjenars di crot cuntun sguin<PERSON>âl", "advancements.husbandry.leash_all_frog_variants.title": "I trê mos<PERSON>", "advancements.husbandry.make_a_sign_glow.description": "Fâs lusî un test di cualsisei gjenar", "advancements.husbandry.make_a_sign_glow.title": "<PERSON><PERSON><PERSON> ce lusôr!", "advancements.husbandry.netherite_hoe.description": "Dopre un lingot di netherite par miorâ une sape, podopo rivalute lis tôs sieltis di vite", "advancements.husbandry.netherite_hoe.title": "Dedizion serie", "advancements.husbandry.obtain_sniffer_egg.description": "<PERSON><PERSON> un ûf di nasadôr", "advancements.husbandry.obtain_sniffer_egg.title": "Un odôr interessant", "advancements.husbandry.place_dried_ghast_in_water.description": "Met un bloc di <PERSON><PERSON><PERSON> secjât inte aghe", "advancements.husbandry.place_dried_ghast_in_water.title": "<PERSON>!", "advancements.husbandry.plant_any_sniffer_seed.description": "Plante une semence dal nasadôr", "advancements.husbandry.plant_any_sniffer_seed.title": "Plantâ il passât", "advancements.husbandry.plant_seed.description": "Plante la semence e cjalile cressi", "advancements.husbandry.plant_seed.title": "A cressin cussì di corse...", "advancements.husbandry.remove_wolf_armor.description": "Gjave la armadure di un lôf doprant lis fuarpiis", "advancements.husbandry.remove_wolf_armor.title": "A la mode", "advancements.husbandry.repair_wolf_armor.description": "Comede une armadure di lôf ruvinade doprant i scus di armadil", "advancements.husbandry.repair_wolf_armor.title": "Come gnûf", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Va intune barcje cuntune cjavre", "advancements.husbandry.ride_a_boat_with_a_goat.title": "La cjavre no je buine di nadâ!", "advancements.husbandry.root.description": "Il mont al è plen di amîs e mangjative", "advancements.husbandry.root.title": "Vite tai cjamps", "advancements.husbandry.safely_harvest_honey.description": "Dopre un fogaron par distrai lis âfs e intant cjape la mîl dal bôç cuntune butilie cence fâlis inrabiâ", "advancements.husbandry.safely_harvest_honey.title": "BenvignûZzzz!", "advancements.husbandry.silk_touch_nest.description": "<PERSON><PERSON>f un bôç cun dentri 3 âfs doprant “man di vilût”", "advancements.husbandry.silk_touch_nest.title": "Un bôç te sachete", "advancements.husbandry.tactical_fishing.description": "Pescje un pes cence une cane!", "advancements.husbandry.tactical_fishing.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.tadpole_in_a_bucket.description": "<PERSON><PERSON><PERSON> un cudul cuntun seglot", "advancements.husbandry.tadpole_in_a_bucket.title": "Come un cudul fûr da la aghe", "advancements.husbandry.tame_an_animal.description": "Mugnestre un animâl", "advancements.husbandry.tame_an_animal.title": "Amì par simpri!", "advancements.husbandry.wax_off.description": "Gjave la cere di un bloc di ram!", "advancements.husbandry.wax_off.title": "Gjave la cere", "advancements.husbandry.wax_on.description": "Do<PERSON>re un celet su di un bloc di ram!", "advancements.husbandry.wax_on.title": "Met la cere", "advancements.husbandry.whole_pack.description": "Mugnestre ducj i gjenars di lôf", "advancements.husbandry.whole_pack.title": "Dut il trop", "advancements.nether.all_effects.description": "Apliche su di te ducj i efiets pussibii intal stes moment", "advancements.nether.all_effects.title": "Robe di mats", "advancements.nether.all_potions.description": "Apliche su di te ducj i efiets des pozions intun colp sôl", "advancements.nether.all_potions.title": "Un cocktail esplosîf", "advancements.nether.brew_potion.description": "Prepare une pozion", "advancements.nether.brew_potion.title": "Distiladôr valent", "advancements.nether.charge_respawn_anchor.description": "<PERSON><PERSON>me fintal massim une ancure di rinassite", "advancements.nether.charge_respawn_anchor.title": "Cuasi 9 vitis come un gjat", "advancements.nether.create_beacon.description": "Fabriche e place un fâr", "advancements.nether.create_beacon.title": "Cuissà la bolete", "advancements.nether.create_full_beacon.description": "Alimente ae massime potence un fâr", "advancements.nether.create_full_beacon.title": "<PERSON><PERSON><PERSON>", "advancements.nether.distract_piglin.description": "Distrai i piglins cun aur", "advancements.nether.distract_piglin.title": "Oh ce biel", "advancements.nether.explore_nether.description": "Esplore ducj i biomis dal Nether", "advancements.nether.explore_nether.title": "Vacancis al cjalt", "advancements.nether.fast_travel.description": "Do<PERSON><PERSON> il Nether par spostâti di 7 km te superficie", "advancements.nether.fast_travel.title": "Chi si cor!", "advancements.nether.find_bastion.description": "Jentre intun bastion in ruvine", "advancements.nether.find_bastion.title": "Mi visi chê volte...", "advancements.nether.find_fortress.description": "<PERSON><PERSON> intune fuartece dal <PERSON>her", "advancements.nether.find_fortress.title": "Une fuartece maladete", "advancements.nether.get_wither_skull.description": "Oten il crani di un scheletri wither", "advancements.nether.get_wither_skull.title": "Ce cjâf <PERSON>!", "advancements.nether.loot_bastion.description": "Svalise un baûl dentri di un bastion in ruvine", "advancements.nether.loot_bastion.title": "Purcite che vuere!", "advancements.nether.netherite_armor.description": "Oten une armadure complete in netherite", "advancements.nether.netherite_armor.title": "Sfodraitmi di rudinaçs", "advancements.nether.obtain_ancient_debris.description": "<PERSON>ten un rudinaç antîc", "advancements.nether.obtain_ancient_debris.title": "Platât tes profonditâts", "advancements.nether.obtain_blaze_rod.description": "Puarte vie a une flamade une sô vuiscje", "advancements.nether.obtain_blaze_rod.title": "Atent a no scotâti", "advancements.nether.obtain_crying_obsidian.description": "<PERSON><PERSON> ossidiane vai<PERSON>", "advancements.nether.obtain_crying_obsidian.title": "<PERSON>ui taial civolis?", "advancements.nether.return_to_sender.description": "Cope un ghast cu la sô bale di fûc", "advancements.nether.return_to_sender.title": "<PERSON>ne mande al mitent", "advancements.nether.ride_strider.description": "Cavalche un strider doprant un baston cun fonc disnaturât", "advancements.nether.ride_strider.title": "Cheste barcje e à lis gjambis", "advancements.nether.ride_strider_in_overworld_lava.description": "Fâs un luuuunc zîr su di un strider, parsore di un lât di lave te superficie", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON><PERSON><PERSON> a c<PERSON>e", "advancements.nether.root.description": "Vistissiti lizêr là jù", "advancements.nether.root.title": "<PERSON>", "advancements.nether.summon_wither.description": "Evoche il Wither", "advancements.nether.summon_wither.title": "<PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "Recupere un ghast tal Nether e puartilu te superficie san e salf... podopo copilu", "advancements.nether.uneasy_alliance.title": "<PERSON>ì e no amîs", "advancements.nether.use_lodestone.description": "Dopre une bussule suntun bloc di magnetite", "advancements.nether.use_lodestone.title": "Bussule magnetizade, direzion sigurade", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Indebilìs e podopo cure un paisan zombi", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON><PERSON>", "advancements.story.deflect_arrow.description": "<PERSON><PERSON> un proietil cuntun scût", "advancements.story.deflect_arrow.title": "No tu mi âs becât!", "advancements.story.enchant_item.description": "Incjante un ogjet intun banc par incjantesims", "advancements.story.enchant_item.title": "Il strion aprendist", "advancements.story.enter_the_end.description": "Passe intun portâl pal End", "advancements.story.enter_the_end.title": "La Fin?", "advancements.story.enter_the_nether.description": "Cost<PERSON><PERSON><PERSON>, ative e jentre intun portâl pal Nether", "advancements.story.enter_the_nether.title": "L'infier al sbrove il curiôs...", "advancements.story.follow_ender_eye.description": "Va daûr di un voli di ender", "advancements.story.follow_ender_eye.title": "Bute un voli", "advancements.story.form_obsidian.description": "Oten un bloc di ossidiane", "advancements.story.form_obsidian.title": "Veri di dr<PERSON>", "advancements.story.iron_tools.description": "<PERSON><PERSON> an<PERSON> il to picon", "advancements.story.iron_tools.title": "Il gno picon di metal lusint", "advancements.story.lava_bucket.description": "Jemple un seglot cu la lave", "advancements.story.lava_bucket.title": "Al scote! Al scote!", "advancements.story.mine_diamond.description": "<PERSON><PERSON> di<PERSON>", "advancements.story.mine_diamond.title": "Diamants!", "advancements.story.mine_stone.description": "Distrûç un bloc di piere cul to gnûf picon", "advancements.story.mine_stone.title": "<PERSON>ûr come la piere", "advancements.story.obtain_armor.description": "Vistissiti cuntun toc di armadure di fier", "advancements.story.obtain_armor.title": "<PERSON>", "advancements.story.root.description": "Il cûr de storie dal zûc", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "La armadure di diamants e salve la vite", "advancements.story.shiny_gear.title": "Sfodraitmi di diamants", "advancements.story.smelt_iron.description": "Prodûs un lingot di fier", "advancements.story.smelt_iron.title": "La ete di fier", "advancements.story.upgrade_tools.description": "Costruìs un picon plui bon", "advancements.story.upgrade_tools.title": "Salt di cualitât", "advancements.toast.challenge": "Sfide completade!", "advancements.toast.goal": "Obiet<PERSON><PERSON> o<PERSON>!", "advancements.toast.task": "Progrès realizât!", "argument.anchor.invalid": "Posizion de ancure de entitât %s no valide", "argument.angle.incomplete": "Incomplet (si spietave 1 angul)", "argument.angle.invalid": "<PERSON><PERSON> no valit", "argument.block.id.invalid": "<PERSON><PERSON><PERSON> di bloc '%s' no cognossût", "argument.block.property.duplicate": "Al è pussibil stabilî dome une volte la proprietât '%s' pal bloc %s", "argument.block.property.invalid": "Il bloc %s nol acete '%s' pe proprietât %s", "argument.block.property.novalue": "Al jere spietât un valôr pe proprietât '%s' sul bloc %s", "argument.block.property.unclosed": "Si spietave une ] di sieradure pes proprietâts di stât dal bloc", "argument.block.property.unknown": "Il bloc %s nol à la proprietât '%s'", "argument.block.tag.disallowed": "Lis etichetis no son permetudis a<PERSON>, dome i blocs concrets", "argument.color.invalid": "Colôr '%s' no cognossût", "argument.component.invalid": "Component de chat no valit: %s", "argument.criteria.invalid": "Criteri '%s' no cognossût", "argument.dimension.invalid": "Dimension '%s' no cognossude", "argument.double.big": "Il dopli nol à di sei plui grant di %s, cjatât %s", "argument.double.low": "Il dopli nol à di sei plui piçul di %s, cjatât %s", "argument.entity.invalid": "Non o UUID no valit", "argument.entity.notfound.entity": "Nissune entitât cjatade", "argument.entity.notfound.player": "<PERSON><PERSON><PERSON>", "argument.entity.options.advancements.description": "Zuiadôrs cun specifics progrès", "argument.entity.options.distance.description": "Distance de creature", "argument.entity.options.distance.negative": "La distance no pues jessi negative", "argument.entity.options.dx.description": "Creaturis jenfri x e x + dx", "argument.entity.options.dy.description": "Creaturis jenfri y e y + dy", "argument.entity.options.dz.description": "Creaturis jenfri z e z + dz", "argument.entity.options.gamemode.description": "Zuiadôrs intune specifiche modalitât di zûc", "argument.entity.options.inapplicable": "Chi no si pues aplicâ la opzion '%s'", "argument.entity.options.level.description": "Nivel di esperience", "argument.entity.options.level.negative": "Il nivel nol à di jessi negatîf", "argument.entity.options.limit.description": "<PERSON><PERSON><PERSON> massim di creaturis di tornâ indaûr", "argument.entity.options.limit.toosmall": "Il limit al à di jessi di almancul 1", "argument.entity.options.mode.invalid": "Modalitât di zûc '%s' no valide o no cognossude", "argument.entity.options.name.description": "Non de creature", "argument.entity.options.nbt.description": "Creaturis cun specifics NBT", "argument.entity.options.predicate.description": "Predicât personalizât", "argument.entity.options.scores.description": "Creaturis cun specifics ponts", "argument.entity.options.sort.description": "Ordenament des creaturis", "argument.entity.options.sort.irreversible": "Gjenar di ordenament '%s' no valit o no cognossût", "argument.entity.options.tag.description": "Creaturis cuntune specifiche etichete", "argument.entity.options.team.description": "Creaturis intune specifiche scuadre", "argument.entity.options.type.description": "Creaturis di un specific gjenar", "argument.entity.options.type.invalid": "<PERSON><PERSON><PERSON> di entitât '%s' no valit o no cognossût", "argument.entity.options.unknown": "Opzion '%s' no cognossude", "argument.entity.options.unterminated": "Si spietave la fin des opzions", "argument.entity.options.valueless": "Al jere spietât un valôr pe opzion '%s'", "argument.entity.options.x.description": "Posizion x", "argument.entity.options.x_rotation.description": "Rotazion x des creaturis", "argument.entity.options.y.description": "Posizion y", "argument.entity.options.y_rotation.description": "Rotazion y des creaturis", "argument.entity.options.z.description": "Posizion z", "argument.entity.selector.allEntities": "<PERSON>tis lis creaturis", "argument.entity.selector.allPlayers": "Du<PERSON>j i zuiadôrs", "argument.entity.selector.missing": "Al mancje il gjenar di seletôr", "argument.entity.selector.nearestEntity": "Entitât plui vicine", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON>ad<PERSON><PERSON> plui <PERSON>", "argument.entity.selector.not_allowed": "Seletôr no permetût", "argument.entity.selector.randomPlayer": "Zuiadôr a câs", "argument.entity.selector.self": "Creature at<PERSON>l", "argument.entity.selector.unknown": "<PERSON><PERSON><PERSON> di seletôr '%s' no cognossût", "argument.entity.toomany": "E je permetude dome une entitât, ma il seletôr indicât al permet plui di une", "argument.enum.invalid": "Valôr \"%s\" no valit", "argument.float.big": "Il numar in virgule mobile nol à di sei plui grant di %s, cjatât %s", "argument.float.low": "Il numar in virgule mobile nol à di sei plui piçul di %s, cjatât %s", "argument.gamemode.invalid": "Modalitât di zûc no cognossude: %s", "argument.hexcolor.invalid": "Codiç di colôr esadecimâl '%s' no valit", "argument.id.invalid": "ID no valit", "argument.id.unknown": "ID no cognossût: %s", "argument.integer.big": "L'intîr nol à di sei plui grant di %s, cjatât %s", "argument.integer.low": "L'intîr nol à di sei plui piçul di %s, cjatât %s", "argument.item.id.invalid": "Element '%s' no cognossût", "argument.item.tag.disallowed": "Lis etichetis no son permetudis a<PERSON>, dome i ogjets concrets", "argument.literal.incorrect": "Si spietave un valôr leterâl %s", "argument.long.big": "L'intîr lunc nol à di sei plui grant di %s, cjatât %s", "argument.long.low": "L'intîr lunc nol à di sei plui piçul di %s, cjatât %s", "argument.message.too_long": "Messaç de chat masse lunc (%s > %s caratars massims)", "argument.nbt.array.invalid": "<PERSON><PERSON><PERSON> di <PERSON> '%s' no valit", "argument.nbt.array.mixed": "Impussibil inserî %s in %s", "argument.nbt.expected.compound": "Si spietave une etichete componude", "argument.nbt.expected.key": "<PERSON> spietave une clâf", "argument.nbt.expected.value": "<PERSON> spietave un valôr", "argument.nbt.list.mixed": "Impussibil inserî %s inte liste di %s", "argument.nbt.trailing": "<PERSON><PERSON>ts finâi no previodûts", "argument.player.entities": "Dome i zuiadôrs a puedin jessi influençâts di chest comant, ma il seletôr indicât al inclût entitâts", "argument.player.toomany": "Al è permetût dome un zuiadôr, ma il seletôr indicât al permet plui di un", "argument.player.unknown": "<PERSON><PERSON> zuia<PERSON>r nol esist", "argument.pos.missing.double": "<PERSON> spietave une coordenade", "argument.pos.missing.int": "Si spietave la posizion di un bloc", "argument.pos.mixed": "Impussibil miscliçâ coordenadis di mont e locâls (dutis a àn di doprâ ^ opûr nissune)", "argument.pos.outofbounds": "Chê posizion e je fûr dai limits ametûts.", "argument.pos.outofworld": "Chê posizion e je fûr di chest mont!", "argument.pos.unloaded": "Chê posizion no je cjariade", "argument.pos2d.incomplete": "Incomplet (si spie<PERSON>vin 2 coordenadis)", "argument.pos3d.incomplete": "Incomplet (si spietave 3 coordenadis)", "argument.range.empty": "Al jere previodût un valôr o un interval di valôrs", "argument.range.ints": "Si ametin dome numars intîrs, no decimâi", "argument.range.swapped": "Il minim nol pues jessi plui grant dal massim", "argument.resource.invalid_type": "L'element '%s' al à un gjenar sbaliât '%s' (si spietave '%s')", "argument.resource.not_found": "Impussibil cjatâ l'element '%s' di gjenar '%s'", "argument.resource_or_id.failed_to_parse": "Alc al è lât strucj tal analizâ la struture: %s", "argument.resource_or_id.invalid": "ID o etichete no valits", "argument.resource_or_id.no_such_element": "Impussibil cjatâ l'element '%s' tal regjistri '%s'", "argument.resource_selector.not_found": "Nissune corispondence pal seletôr '%s' di gjenar '%s'", "argument.resource_tag.invalid_type": "La etichete '%s' e à un gjenar sbali<PERSON>t '%s' (si spietave '%s')", "argument.resource_tag.not_found": "Impussibil cjatâ la etichete '%s' di gjenar '%s'", "argument.rotation.incomplete": "Incomplet (si spietave 2 coordenadis)", "argument.scoreHolder.empty": "Nol è stât cjatât nissun possessôr di ponts pertinents", "argument.scoreboardDisplaySlot.invalid": "Spazi di visualizazion '%s' no cognossût", "argument.style.invalid": "Stîl no valit: %s", "argument.time.invalid_tick_count": "La conte des ticadis (tick) no à di jessi negative", "argument.time.invalid_unit": "Unitât no valide", "argument.time.tick_count_too_low": "La conte des ticadis no à di sei inferiôr a %s, cjatât %s", "argument.uuid.invalid": "UUID no valit", "argument.waypoint.invalid": "La entitât selezionade no je un pont di riferiment", "arguments.block.tag.unknown": "Etichete di bloc '%s' no cognossude", "arguments.function.tag.unknown": "Etichete de funzion '%s' no cognossude", "arguments.function.unknown": "Funzion %s no cognossude", "arguments.item.component.expected": "Al jere previodût un component dal ogjet", "arguments.item.component.malformed": "Component '%s' malformât: %s'", "arguments.item.component.repeated": "Il component dal ogjet '%s' al e stât ripetût, ma si pues specificâ dome un valôr", "arguments.item.component.unknown": "Component dal ogjet no cognossût '%s'", "arguments.item.malformed": "Ogjet malformât: '%s'", "arguments.item.overstacked": "Al è pussibil intassâ %s fin a %s", "arguments.item.predicate.malformed": "Predicât '%s' malformât: '%s'", "arguments.item.predicate.unknown": "Predicât '%s' dal ogjet no cognossût", "arguments.item.tag.unknown": "Etichete di element '%s' no cognossude", "arguments.nbtpath.node.invalid": "Element dal percors NBT no valit", "arguments.nbtpath.nothing_found": "Nol è stât cjatât nissun element che al corispuint a %s", "arguments.nbtpath.too_deep": "NBT risultant annidade masse insot", "arguments.nbtpath.too_large": "NBT risultant masse grande", "arguments.objective.notFound": "L'obietîf dal segnepont<PERSON> '%s' nol è cognossût", "arguments.objective.readonly": "L'obiet<PERSON><PERSON> dal se<PERSON> '%s' al è di dome-leture", "arguments.operation.div0": "Impussibil dividi par zero", "arguments.operation.invalid": "Operazion no valide", "arguments.swizzle.invalid": "Cumbinazion di as no valide, si spietave une cumbinazion di 'x', 'y' e 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "Ponts di armadure", "attribute.name.armor_toughness": "Tignince de armadure", "attribute.name.attack_damage": "Dams di atac", "attribute.name.attack_knockback": "<PERSON><PERSON><PERSON><PERSON><PERSON> dal atac", "attribute.name.attack_speed": "Velocitât di atac", "attribute.name.block_break_speed": "Velocitât di distruzion di un bloc", "attribute.name.block_interaction_range": "Rai di interazion cui blocs", "attribute.name.burning_time": "Timp di combustion", "attribute.name.camera_distance": "Distance de incuadradure", "attribute.name.entity_interaction_range": "Rai di interazion cun lis entitâts", "attribute.name.explosion_knockback_resistance": "Resistence al cuintricolp des esplosions", "attribute.name.fall_damage_multiplier": "Moltiplicadôr dai dams pe colade", "attribute.name.flying_speed": "Veloci<PERSON><PERSON><PERSON> svolant", "attribute.name.follow_range": "Rai di rilevament des creaturis", "attribute.name.generic.armor": "Ponts di armadure", "attribute.name.generic.armor_toughness": "Tignince de armadure", "attribute.name.generic.attack_damage": "Dams di atac", "attribute.name.generic.attack_knockback": "<PERSON><PERSON><PERSON><PERSON><PERSON> dal atac", "attribute.name.generic.attack_speed": "Velocitât di atac", "attribute.name.generic.block_interaction_range": "Rai di interazion cui blocs", "attribute.name.generic.burning_time": "Timp di combustion", "attribute.name.generic.entity_interaction_range": "Rai di interazion cun lis entitâts", "attribute.name.generic.explosion_knockback_resistance": "Resistence al cuintricolp des esplosions", "attribute.name.generic.fall_damage_multiplier": "Moltiplicadôr dai dams pe colade", "attribute.name.generic.flying_speed": "Veloci<PERSON><PERSON><PERSON> svolant", "attribute.name.generic.follow_range": "Rai di rilevament des creaturis", "attribute.name.generic.gravity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.jump_strength": "Fuarce dal salt", "attribute.name.generic.knockback_resistance": "Resistence al cuintricolp", "attribute.name.generic.luck": "Fortune", "attribute.name.generic.max_absorption": "Assorbiment massim", "attribute.name.generic.max_health": "Salût massime", "attribute.name.generic.movement_efficiency": "Eficience dal moviment", "attribute.name.generic.movement_speed": "Velocitâ<PERSON>", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON> extra", "attribute.name.generic.safe_fall_distance": "Altece sigure pe colade", "attribute.name.generic.scale": "<PERSON><PERSON><PERSON>", "attribute.name.generic.step_height": "Altece dal pas", "attribute.name.generic.water_movement_efficiency": "Eficience dal moviment te aghe", "attribute.name.gravity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.horse.jump_strength": "Fuarce dal salt dal cjaval", "attribute.name.jump_strength": "Fuarce dal salt", "attribute.name.knockback_resistance": "Resistence al cuintricolp", "attribute.name.luck": "<PERSON><PERSON><PERSON>", "attribute.name.max_absorption": "Assorbiment massim", "attribute.name.max_health": "Salût massime", "attribute.name.mining_efficiency": "Eficience tal sgjavâ", "attribute.name.movement_efficiency": "Eficience dal moviment", "attribute.name.movement_speed": "Velocitâ<PERSON>", "attribute.name.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON> extra", "attribute.name.player.block_break_speed": "Velocitât di distruzion di un bloc", "attribute.name.player.block_interaction_range": "Rai di interazion cui blocs", "attribute.name.player.entity_interaction_range": "Rai di interazion cun lis entitâts", "attribute.name.player.mining_efficiency": "Eficience tal sgjavâ", "attribute.name.player.sneaking_speed": "Velocitâ<PERSON> s<PERSON>", "attribute.name.player.submerged_mining_speed": "Velocitât tal sgjavâ sot aghe", "attribute.name.player.sweeping_damage_ratio": "Rapuart di dam dal atac scoreât", "attribute.name.safe_fall_distance": "Altece sigure pe colade", "attribute.name.scale": "<PERSON><PERSON><PERSON>", "attribute.name.sneaking_speed": "Velocitâ<PERSON> s<PERSON>", "attribute.name.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>i", "attribute.name.step_height": "Altece dal pas", "attribute.name.submerged_mining_speed": "Velocitât tal sgjavâ sot aghe", "attribute.name.sweeping_damage_ratio": "Rapuart di dam dal atac scoreât", "attribute.name.tempt_range": "Rai di atrazion des creaturis", "attribute.name.water_movement_efficiency": "Eficience dal moviment te aghe", "attribute.name.waypoint_receive_range": "Puartade di ricezion dal pont di riferiment", "attribute.name.waypoint_transmit_range": "Puartade di trasmission dal pont di riferiment", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>i", "biome.minecraft.badlands": "Agadôrs", "biome.minecraft.bamboo_jungle": "Jungle di bambù", "biome.minecraft.basalt_deltas": "Deltas di basalt", "biome.minecraft.beach": "Splaze", "biome.minecraft.birch_forest": "Bosc di bedois", "biome.minecraft.cherry_grove": "Bosc di cjariesârs", "biome.minecraft.cold_ocean": "Ocean frêt", "biome.minecraft.crimson_forest": "Bosc cremisin", "biome.minecraft.dark_forest": "Bosc scûr", "biome.minecraft.deep_cold_ocean": "Ocean frêt e profont", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "biome.minecraft.deep_frozen_ocean": "Ocean glaçât e profont", "biome.minecraft.deep_lukewarm_ocean": "Ocean clip e profont", "biome.minecraft.deep_ocean": "Ocean profont", "biome.minecraft.desert": "Desert", "biome.minecraft.dripstone_caves": "<PERSON><PERSON><PERSON> calcaris", "biome.minecraft.end_barrens": "<PERSON><PERSON><PERSON> sterps dal End", "biome.minecraft.end_highlands": "Altiplans dal End", "biome.minecraft.end_midlands": "<PERSON>jon centrâl dal <PERSON>", "biome.minecraft.eroded_badlands": "Agadôrs erodûts", "biome.minecraft.flower_forest": "Bosc di rosis", "biome.minecraft.forest": "Bosc", "biome.minecraft.frozen_ocean": "Ocean glaçât", "biome.minecraft.frozen_peaks": "<PERSON><PERSON>", "biome.minecraft.frozen_river": "<PERSON><PERSON> gla<PERSON>", "biome.minecraft.grove": "Boscut", "biome.minecraft.ice_spikes": "Pontis di glace", "biome.minecraft.jagged_peaks": "<PERSON><PERSON>", "biome.minecraft.jungle": "Jungle", "biome.minecraft.lukewarm_ocean": "Ocean clip", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON> viva<PERSON>", "biome.minecraft.mangrove_swamp": "Palût di mangrovis", "biome.minecraft.meadow": "Pradarie", "biome.minecraft.mushroom_fields": "Cjamps di foncs", "biome.minecraft.nether_wastes": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.ocean": "Ocean", "biome.minecraft.old_growth_birch_forest": "Bosc di bedoi secolârs", "biome.minecraft.old_growth_pine_taiga": "Taiga di pins secolârs", "biome.minecraft.old_growth_spruce_taiga": "Taiga di peçs secolars", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON> p<PERSON>t", "biome.minecraft.plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.river": "<PERSON>lum", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Altiplan de savane", "biome.minecraft.small_end_islands": "<PERSON><PERSON><PERSON> da<PERSON>", "biome.minecraft.snowy_beach": "Splaze cuvierte di nêf", "biome.minecraft.snowy_plains": "<PERSON>uris cuvier<PERSON> di nêf", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON> cu<PERSON> di nêf", "biome.minecraft.snowy_taiga": "Taiga cuvierte di nêf", "biome.minecraft.soul_sand_valley": "Val des animis", "biome.minecraft.sparse_jungle": "Jungle rare", "biome.minecraft.stony_peaks": "Pontis di cret", "biome.minecraft.stony_shore": "Cuestis di cret", "biome.minecraft.sunflower_plains": "<PERSON>uris di g<PERSON>rasoi", "biome.minecraft.swamp": "Palût", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "L'End", "biome.minecraft.the_void": "Il Vueit", "biome.minecraft.warm_ocean": "Ocean cjalt", "biome.minecraft.warped_forest": "Bosc di<PERSON>urâ<PERSON>", "biome.minecraft.windswept_forest": "Bosc a<PERSON>ô<PERSON>", "biome.minecraft.windswept_gravelly_hills": "<PERSON>ulin<PERSON> gler<PERSON>", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON>", "biome.minecraft.windswept_savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.wooded_badlands": "Agadô<PERSON>", "block.minecraft.acacia_button": "Boton di agacie", "block.minecraft.acacia_door": "Puarte di agacie", "block.minecraft.acacia_fence": "Stangjaçade di agacie", "block.minecraft.acacia_fence_gate": "Portonut di agacie", "block.minecraft.acacia_hanging_sign": "Insegne di agacie", "block.minecraft.acacia_leaves": "Fueis di agacie", "block.minecraft.acacia_log": "Tronc di agacie", "block.minecraft.acacia_planks": "Breis di agacie", "block.minecraft.acacia_pressure_plate": "Plache di pression di agacie", "block.minecraft.acacia_sapling": "Arbossit di agacie", "block.minecraft.acacia_sign": "Cartel di agacie", "block.minecraft.acacia_slab": "Lastre di agacie", "block.minecraft.acacia_stairs": "Scjalins di agacie", "block.minecraft.acacia_trapdoor": "Trabuchel di agacie", "block.minecraft.acacia_wall_hanging_sign": "Insegne di parêt di agacie", "block.minecraft.acacia_wall_sign": "Targhe di parêt di agacie", "block.minecraft.acacia_wood": "Len di agacie", "block.minecraft.activator_rail": "Sinis di ativazion", "block.minecraft.air": "<PERSON><PERSON>", "block.minecraft.allium": "Allium", "block.minecraft.amethyst_block": "Bloc di ametiste", "block.minecraft.amethyst_cluster": "Agregât di ametiste", "block.minecraft.ancient_debris": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.andesite": "Andesite", "block.minecraft.andesite_slab": "Lastre di andesite", "block.minecraft.andesite_stairs": "Scjalins di andesite", "block.minecraft.andesite_wall": "<PERSON>ret di andesite", "block.minecraft.anvil": "<PERSON>uin", "block.minecraft.attached_melon_stem": "Stoc di angurie tacât", "block.minecraft.attached_pumpkin_stem": "Stoc de coce tacât", "block.minecraft.azalea": "<PERSON><PERSON><PERSON>", "block.minecraft.azalea_leaves": "Fueis di azalee", "block.minecraft.azure_bluet": "Houston<PERSON> caerulea", "block.minecraft.bamboo": "Bambù", "block.minecraft.bamboo_block": "Bloc di bambù", "block.minecraft.bamboo_button": "Boton di bam<PERSON>ù", "block.minecraft.bamboo_door": "Puarte di bambù", "block.minecraft.bamboo_fence": "Stangjaça<PERSON> di bambù", "block.minecraft.bamboo_fence_gate": "Portonut di bambù", "block.minecraft.bamboo_hanging_sign": "Insegne di bambù", "block.minecraft.bamboo_mosaic": "Bambù instreçât", "block.minecraft.bamboo_mosaic_slab": "Lastre di bambù instreçât", "block.minecraft.bamboo_mosaic_stairs": "Sc<PERSON><PERSON> di bambù instreçât", "block.minecraft.bamboo_planks": "Breis di bambù", "block.minecraft.bamboo_pressure_plate": "Plache di pression di bambù", "block.minecraft.bamboo_sapling": "Menade di bambù", "block.minecraft.bamboo_sign": "Cartel di bambù", "block.minecraft.bamboo_slab": "<PERSON><PERSON> di bambù", "block.minecraft.bamboo_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.bamboo_trapdoor": "Trab<PERSON><PERSON> di bambù", "block.minecraft.bamboo_wall_hanging_sign": "Insegne di parêt di bambù", "block.minecraft.bamboo_wall_sign": "<PERSON><PERSON><PERSON> di parêt di bambù", "block.minecraft.banner.base.black": "<PERSON><PERSON><PERSON> <PERSON>ri", "block.minecraft.banner.base.blue": "Cjamp blu", "block.minecraft.banner.base.brown": "Cjamp maron", "block.minecraft.banner.base.cyan": "Cjamp ciano", "block.minecraft.banner.base.gray": "<PERSON>jamp gr<PERSON><PERSON>", "block.minecraft.banner.base.green": "Cjamp vert", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON><PERSON> blu cl<PERSON>r", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON><PERSON> gr<PERSON><PERSON> c<PERSON>r", "block.minecraft.banner.base.lime": "Cjamp vert limon", "block.minecraft.banner.base.magenta": "Cjamp magenta", "block.minecraft.banner.base.orange": "Cjamp naranç", "block.minecraft.banner.base.pink": "<PERSON><PERSON><PERSON> rose", "block.minecraft.banner.base.purple": "Cjamp viole", "block.minecraft.banner.base.red": "Cjamp ros", "block.minecraft.banner.base.white": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.banner.base.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.black": "Ae bordure di neri", "block.minecraft.banner.border.blue": "Ae bordure di blu", "block.minecraft.banner.border.brown": "Ae bordure di maron", "block.minecraft.banner.border.cyan": "Ae bordure di ciano", "block.minecraft.banner.border.gray": "Ae bordure di grîs", "block.minecraft.banner.border.green": "Ae bordure di vert", "block.minecraft.banner.border.light_blue": "Ae bordure di blu clâr", "block.minecraft.banner.border.light_gray": "Ae bordure di grîs clâr", "block.minecraft.banner.border.lime": "Ae bordure di vert limon", "block.minecraft.banner.border.magenta": "Ae bordure di magenta", "block.minecraft.banner.border.orange": "Ae bordure di naranç", "block.minecraft.banner.border.pink": "Ae bordure di rose", "block.minecraft.banner.border.purple": "Ae bordure di viole", "block.minecraft.banner.border.red": "Ae bordure di ros", "block.minecraft.banner.border.white": "Ae bordure di blanc", "block.minecraft.banner.border.yellow": "Ae bordure di zâl", "block.minecraft.banner.bricks.black": "Semenât di modons di neri", "block.minecraft.banner.bricks.blue": "Semenât di modons di blu", "block.minecraft.banner.bricks.brown": "Semenât di modons di maron", "block.minecraft.banner.bricks.cyan": "Semenât di modons di ciano", "block.minecraft.banner.bricks.gray": "Semenât di modons di grîs", "block.minecraft.banner.bricks.green": "Semenât di modons di vert", "block.minecraft.banner.bricks.light_blue": "Semenât di modons di blu clâr", "block.minecraft.banner.bricks.light_gray": "Semenât di modons di grîs clâr", "block.minecraft.banner.bricks.lime": "Semenât di modons di vert limon", "block.minecraft.banner.bricks.magenta": "Semenât di modons di magenta", "block.minecraft.banner.bricks.orange": "Semenât di modons di naranç", "block.minecraft.banner.bricks.pink": "Semenât di modons di rose", "block.minecraft.banner.bricks.purple": "Semenât di modons di viole", "block.minecraft.banner.bricks.red": "Semenât di modons di ros", "block.minecraft.banner.bricks.white": "Semenât di modons di blanc", "block.minecraft.banner.bricks.yellow": "Semenât di modons di zâl", "block.minecraft.banner.circle.black": "Ae torte di neri", "block.minecraft.banner.circle.blue": "Ae torte di blu", "block.minecraft.banner.circle.brown": "Ae torte di maron", "block.minecraft.banner.circle.cyan": "Ae torte di ciano", "block.minecraft.banner.circle.gray": "Ae torte di grîs", "block.minecraft.banner.circle.green": "Ae torte di vert", "block.minecraft.banner.circle.light_blue": "Ae torte di blu clâr", "block.minecraft.banner.circle.light_gray": "Ae torte di grîs clâr", "block.minecraft.banner.circle.lime": "Ae torte di vert limon", "block.minecraft.banner.circle.magenta": "Ae torte di magenta", "block.minecraft.banner.circle.orange": "Ae torte di naranç", "block.minecraft.banner.circle.pink": "Ae torte di rose", "block.minecraft.banner.circle.purple": "Ae torte di viole", "block.minecraft.banner.circle.red": "Ae torte di ros", "block.minecraft.banner.circle.white": "Ae torte di blanc", "block.minecraft.banner.circle.yellow": "Ae torte di zâl", "block.minecraft.banner.creeper.black": "Ae muse di creeper di neri", "block.minecraft.banner.creeper.blue": "Ae muse di creeper di blu", "block.minecraft.banner.creeper.brown": "Ae muse di creeper di maron", "block.minecraft.banner.creeper.cyan": "Ae muse di creeper di ciano", "block.minecraft.banner.creeper.gray": "Ae muse di creeper di grîs", "block.minecraft.banner.creeper.green": "Ae muse di creeper di vert", "block.minecraft.banner.creeper.light_blue": "Ae muse di creeper di blu clâr", "block.minecraft.banner.creeper.light_gray": "Ae muse di creeper di grîs clâr", "block.minecraft.banner.creeper.lime": "Ae muse di creeper di vert limon", "block.minecraft.banner.creeper.magenta": "Ae muse di creeper di magenta", "block.minecraft.banner.creeper.orange": "Ae muse di creeper di naranç", "block.minecraft.banner.creeper.pink": "Ae muse di creeper di rose", "block.minecraft.banner.creeper.purple": "Ae muse di creeper di viole", "block.minecraft.banner.creeper.red": "Ae muse di creeper di ros", "block.minecraft.banner.creeper.white": "Ae muse di creeper di blanc", "block.minecraft.banner.creeper.yellow": "Ae muse di creeper di zâl", "block.minecraft.banner.cross.black": "Ae crôs di S. Andree di neri", "block.minecraft.banner.cross.blue": "Ae crôs di S. Andree di blu", "block.minecraft.banner.cross.brown": "Ae crôs di S. Andree di maron", "block.minecraft.banner.cross.cyan": "Ae crôs di S. Andree di ciano", "block.minecraft.banner.cross.gray": "Ae crôs di S. Andree di grîs", "block.minecraft.banner.cross.green": "Ae crôs di S. Andree di vert", "block.minecraft.banner.cross.light_blue": "Ae crôs di S. Andree di blu clâr", "block.minecraft.banner.cross.light_gray": "Ae crôs di S. Andree di grîs clâr", "block.minecraft.banner.cross.lime": "Ae crôs di S. Andree di vert limon", "block.minecraft.banner.cross.magenta": "Ae crôs di S. Andree di magenta", "block.minecraft.banner.cross.orange": "Ae crôs di S. Andree di naranç", "block.minecraft.banner.cross.pink": "Ae crôs di S. Andree di rose", "block.minecraft.banner.cross.purple": "Ae crôs di S. Andree di viole", "block.minecraft.banner.cross.red": "Ae crôs di S. Andree di ros", "block.minecraft.banner.cross.white": "Ae crôs di S. Andree di blanc", "block.minecraft.banner.cross.yellow": "Ae crôs di S. Andree di zâl", "block.minecraft.banner.curly_border.black": "Ae bordure dentelade di neri", "block.minecraft.banner.curly_border.blue": "Ae bordure dentelade di blu", "block.minecraft.banner.curly_border.brown": "Ae bordure dentelade di maron", "block.minecraft.banner.curly_border.cyan": "Ae bordure dentelade di ciano", "block.minecraft.banner.curly_border.gray": "Ae bordure dentelade di grîs", "block.minecraft.banner.curly_border.green": "Ae bordure dentelade di vert", "block.minecraft.banner.curly_border.light_blue": "Ae bordure dentelade di blu clâr", "block.minecraft.banner.curly_border.light_gray": "Ae bordure dentelade di grîs clâr", "block.minecraft.banner.curly_border.lime": "Ae bordure dentelade di vert limon", "block.minecraft.banner.curly_border.magenta": "Ae bordure dentelade di magenta", "block.minecraft.banner.curly_border.orange": "Ae bordure dentelade di naranç", "block.minecraft.banner.curly_border.pink": "Ae bordure dentelade di rose", "block.minecraft.banner.curly_border.purple": "Ae bordure dentelade di viole", "block.minecraft.banner.curly_border.red": "Ae bordure dentelade di ros", "block.minecraft.banner.curly_border.white": "Ae bordure dentelade di blanc", "block.minecraft.banner.curly_border.yellow": "Ae bordure dentelade di zâl", "block.minecraft.banner.diagonal_left.black": "Taiât: tal 1ⁿ di neri", "block.minecraft.banner.diagonal_left.blue": "Taiât: tal 1ⁿ di blu", "block.minecraft.banner.diagonal_left.brown": "Taiât: tal 1ⁿ di maron", "block.minecraft.banner.diagonal_left.cyan": "Taiât: tal 1ⁿ di ciano", "block.minecraft.banner.diagonal_left.gray": "Taiât: tal 1ⁿ di grîs", "block.minecraft.banner.diagonal_left.green": "Taiât: tal 1ⁿ di vert", "block.minecraft.banner.diagonal_left.light_blue": "Taiât: tal 1ⁿ di blu clâr", "block.minecraft.banner.diagonal_left.light_gray": "Taiât: tal 1ⁿ di grîs clâr", "block.minecraft.banner.diagonal_left.lime": "Taiât: tal 1ⁿ di vert limon", "block.minecraft.banner.diagonal_left.magenta": "Taiât: tal 1ⁿ di magenta", "block.minecraft.banner.diagonal_left.orange": "Taiât: tal 1ⁿ di naranç", "block.minecraft.banner.diagonal_left.pink": "Taiât: tal 1ⁿ di rose", "block.minecraft.banner.diagonal_left.purple": "Taiât: tal 1ⁿ di viole", "block.minecraft.banner.diagonal_left.red": "Taiât: tal 1ⁿ di ros", "block.minecraft.banner.diagonal_left.white": "Taiât: tal 1ⁿ di blanc", "block.minecraft.banner.diagonal_left.yellow": "Taiât: tal 1ⁿ di zâl", "block.minecraft.banner.diagonal_right.black": "Trançât: tal 1ⁿ di neri", "block.minecraft.banner.diagonal_right.blue": "Trançât: tal 1ⁿ di blu", "block.minecraft.banner.diagonal_right.brown": "Trançât: tal 1ⁿ di maron", "block.minecraft.banner.diagonal_right.cyan": "Trançât: tal 1ⁿ di ciano", "block.minecraft.banner.diagonal_right.gray": "Trançât: tal 1ⁿ di grîs", "block.minecraft.banner.diagonal_right.green": "Trançât: tal 1ⁿ di vert", "block.minecraft.banner.diagonal_right.light_blue": "Trançât: tal 1ⁿ di blu clâr", "block.minecraft.banner.diagonal_right.light_gray": "Trançât: tal 1ⁿ di grîs clâr", "block.minecraft.banner.diagonal_right.lime": "Trançât: tal 1ⁿ di vert limon", "block.minecraft.banner.diagonal_right.magenta": "Trançât: tal 1ⁿ di magenta", "block.minecraft.banner.diagonal_right.orange": "Trançât: tal 1ⁿ di naranç", "block.minecraft.banner.diagonal_right.pink": "Trançât: tal 1ⁿ di rose", "block.minecraft.banner.diagonal_right.purple": "Trançât: tal 1ⁿ di viole", "block.minecraft.banner.diagonal_right.red": "Trançât: tal 1ⁿ di ros", "block.minecraft.banner.diagonal_right.white": "Trançât: tal 1ⁿ di blanc", "block.minecraft.banner.diagonal_right.yellow": "Trançât: tal 1ⁿ di zâl", "block.minecraft.banner.diagonal_up_left.black": "Trançât: tal 2ᵗ di neri", "block.minecraft.banner.diagonal_up_left.blue": "Trançât: tal 2ᵗ di blu", "block.minecraft.banner.diagonal_up_left.brown": "Trançât: tal 2ᵗ di maron", "block.minecraft.banner.diagonal_up_left.cyan": "Trançât: tal 2ᵗ di ciano", "block.minecraft.banner.diagonal_up_left.gray": "Trançât: tal 2ᵗ di grîs", "block.minecraft.banner.diagonal_up_left.green": "Trançât: tal 2ᵗ di vert", "block.minecraft.banner.diagonal_up_left.light_blue": "Trançât: tal 2ᵗ di blu clâr", "block.minecraft.banner.diagonal_up_left.light_gray": "Trançât: tal 2ᵗ di grîs clâr", "block.minecraft.banner.diagonal_up_left.lime": "Trançât: tal 2ᵗ di vert limon", "block.minecraft.banner.diagonal_up_left.magenta": "Trançât: tal 2ᵗ di magenta", "block.minecraft.banner.diagonal_up_left.orange": "Trançât: tal 2ᵗ di naranç", "block.minecraft.banner.diagonal_up_left.pink": "Trançât: tal 2ᵗ di rose", "block.minecraft.banner.diagonal_up_left.purple": "Trançât: tal 2ᵗ di viole", "block.minecraft.banner.diagonal_up_left.red": "Trançât: tal 2ᵗ di ros", "block.minecraft.banner.diagonal_up_left.white": "Trançât: tal 2ᵗ di blanc", "block.minecraft.banner.diagonal_up_left.yellow": "Trançât: tal 2ᵗ di zâl", "block.minecraft.banner.diagonal_up_right.black": "Taiât: tal 2ᵗ di neri", "block.minecraft.banner.diagonal_up_right.blue": "Taiât: tal 2ᵗ di blu", "block.minecraft.banner.diagonal_up_right.brown": "Taiât: tal 2ᵗ di maron", "block.minecraft.banner.diagonal_up_right.cyan": "Taiât: tal 2ᵗ di ciano", "block.minecraft.banner.diagonal_up_right.gray": "Taiât: tal 2ᵗ di grîs", "block.minecraft.banner.diagonal_up_right.green": "Taiât: tal 2ᵗ di vert", "block.minecraft.banner.diagonal_up_right.light_blue": "Taiât: tal 2ᵗ di blu clâr", "block.minecraft.banner.diagonal_up_right.light_gray": "Taiât: tal 2ᵗ di grîs clâr", "block.minecraft.banner.diagonal_up_right.lime": "Taiât: tal 2ᵗ di vert limon", "block.minecraft.banner.diagonal_up_right.magenta": "Taiât: tal 2ᵗ di magenta", "block.minecraft.banner.diagonal_up_right.orange": "Taiât: tal 2ᵗ di naranç", "block.minecraft.banner.diagonal_up_right.pink": "Taiât: tal 2ᵗ di rose", "block.minecraft.banner.diagonal_up_right.purple": "Taiât: tal 2ᵗ di viole", "block.minecraft.banner.diagonal_up_right.red": "Taiât: tal 2ᵗ di ros", "block.minecraft.banner.diagonal_up_right.white": "Taiât: tal 2ᵗ di blanc", "block.minecraft.banner.diagonal_up_right.yellow": "Taiât: tal 2ᵗ di zâl", "block.minecraft.banner.flow.black": "<PERSON>lus neri", "block.minecraft.banner.flow.blue": "Flus blu", "block.minecraft.banner.flow.brown": "Flus maron", "block.minecraft.banner.flow.cyan": "<PERSON>lus ciano", "block.minecraft.banner.flow.gray": "Flus grîs", "block.minecraft.banner.flow.green": "Flus vert", "block.minecraft.banner.flow.light_blue": "Flus blu clâr", "block.minecraft.banner.flow.light_gray": "<PERSON>lus grîs clâr", "block.minecraft.banner.flow.lime": "Flus vert limon", "block.minecraft.banner.flow.magenta": "Flus magenta", "block.minecraft.banner.flow.orange": "Flus naranç", "block.minecraft.banner.flow.pink": "Flus rose", "block.minecraft.banner.flow.purple": "Flus viole", "block.minecraft.banner.flow.red": "Flus ros", "block.minecraft.banner.flow.white": "Flus blanc", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON>", "block.minecraft.banner.flower.black": "Ae rose di neri", "block.minecraft.banner.flower.blue": "Ae rose di blu", "block.minecraft.banner.flower.brown": "Ae rose di maron", "block.minecraft.banner.flower.cyan": "Ae rose di ciano", "block.minecraft.banner.flower.gray": "Ae rose di grîs", "block.minecraft.banner.flower.green": "Ae rose di vert", "block.minecraft.banner.flower.light_blue": "Ae rose di blu clâr", "block.minecraft.banner.flower.light_gray": "Ae rose di grîs clâr", "block.minecraft.banner.flower.lime": "Ae rose di vert limon", "block.minecraft.banner.flower.magenta": "Ae rose di magenta", "block.minecraft.banner.flower.orange": "Ae rose di naranç", "block.minecraft.banner.flower.pink": "Ae rose di rose", "block.minecraft.banner.flower.purple": "Ae rose di viole", "block.minecraft.banner.flower.red": "Ae rose di ros", "block.minecraft.banner.flower.white": "Ae rose di blanc", "block.minecraft.banner.flower.yellow": "<PERSON>e rose di z<PERSON>l", "block.minecraft.banner.globe.black": "Ae sfere di neri", "block.minecraft.banner.globe.blue": "Ae sfere di blu", "block.minecraft.banner.globe.brown": "Ae sfere di maron", "block.minecraft.banner.globe.cyan": "Ae sfere di ciano", "block.minecraft.banner.globe.gray": "Ae sfere di grîs", "block.minecraft.banner.globe.green": "Ae sfere di vert", "block.minecraft.banner.globe.light_blue": "Ae sfere di blu clâr", "block.minecraft.banner.globe.light_gray": "Ae sfere di grîs clâr", "block.minecraft.banner.globe.lime": "Ae sfere di vert limon", "block.minecraft.banner.globe.magenta": "Ae sfere di magenta", "block.minecraft.banner.globe.orange": "Ae sfere di naranç", "block.minecraft.banner.globe.pink": "Ae sfere di rose", "block.minecraft.banner.globe.purple": "Ae sfere di viole", "block.minecraft.banner.globe.red": "Ae sfere di ros", "block.minecraft.banner.globe.white": "Ae sfere di blanc", "block.minecraft.banner.globe.yellow": "Ae sfere di zâl", "block.minecraft.banner.gradient.black": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di neri", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di blu", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di maron", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di ciano", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di grîs", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di vert", "block.minecraft.banner.gradient.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di blu clâr", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di grîs clâr", "block.minecraft.banner.gradient.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di vert limon", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di magenta", "block.minecraft.banner.gradient.orange": "S<PERSON>mât in cjâf di naranç", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di rose", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di viole", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di ros", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di blanc", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON> in cjâf di zâl", "block.minecraft.banner.gradient_up.black": "Sfumât in ponte di neri", "block.minecraft.banner.gradient_up.blue": "Sfumât in ponte di blu", "block.minecraft.banner.gradient_up.brown": "S<PERSON>mât in ponte di maron", "block.minecraft.banner.gradient_up.cyan": "Sfumât in ponte di ciano", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> in ponte di grîs", "block.minecraft.banner.gradient_up.green": "S<PERSON>mât in ponte di vert", "block.minecraft.banner.gradient_up.light_blue": "Sfumât in ponte di blu clâr", "block.minecraft.banner.gradient_up.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON> in ponte di grîs clâr", "block.minecraft.banner.gradient_up.lime": "Sfumât in ponte di vert limon", "block.minecraft.banner.gradient_up.magenta": "Sfumât in ponte di magenta", "block.minecraft.banner.gradient_up.orange": "Sfumât in ponte di naranç", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON> in ponte di rose", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> in ponte di viole", "block.minecraft.banner.gradient_up.red": "Sfumât in ponte di ros", "block.minecraft.banner.gradient_up.white": "Sfumât in ponte di blanc", "block.minecraft.banner.gradient_up.yellow": "Sfumât in ponte di zâl", "block.minecraft.banner.guster.black": "<PERSON><PERSON><PERSON> nere", "block.minecraft.banner.guster.blue": "<PERSON><PERSON><PERSON> blu", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON> maron", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.gray": "Bugade grise", "block.minecraft.banner.guster.green": "<PERSON><PERSON>de verde", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON> blu cl<PERSON>r", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON> gr<PERSON><PERSON> c<PERSON>", "block.minecraft.banner.guster.lime": "Bugade vert limon", "block.minecraft.banner.guster.magenta": "Bugade magenta", "block.minecraft.banner.guster.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.pink": "<PERSON><PERSON><PERSON> rose", "block.minecraft.banner.guster.purple": "<PERSON><PERSON><PERSON> viole", "block.minecraft.banner.guster.red": "<PERSON><PERSON>de rosse", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON> zale", "block.minecraft.banner.half_horizontal.black": "Çoncjât: tal 1ⁿ di neri", "block.minecraft.banner.half_horizontal.blue": "Çoncjât: tal 1ⁿ di blu", "block.minecraft.banner.half_horizontal.brown": "Çoncjât: tal 1ⁿ di maron", "block.minecraft.banner.half_horizontal.cyan": "Çoncjât: tal 1ⁿ di ciano", "block.minecraft.banner.half_horizontal.gray": "Çoncjât: tal 1ⁿ di grîs", "block.minecraft.banner.half_horizontal.green": "Çoncjât: tal 1ⁿ di vert", "block.minecraft.banner.half_horizontal.light_blue": "Çoncjât: tal 1ⁿ di blu clâr", "block.minecraft.banner.half_horizontal.light_gray": "Çoncjât: tal 1ⁿ di grîs clâr", "block.minecraft.banner.half_horizontal.lime": "Çoncjât: tal 1ⁿ di vert limon", "block.minecraft.banner.half_horizontal.magenta": "Çoncjât: tal 1ⁿ di magenta", "block.minecraft.banner.half_horizontal.orange": "Çoncjât: tal 1ⁿ di naranç", "block.minecraft.banner.half_horizontal.pink": "Çoncjât: tal 1ⁿ di rose", "block.minecraft.banner.half_horizontal.purple": "Çoncjât: tal 1ⁿ di viole", "block.minecraft.banner.half_horizontal.red": "Çoncjât: tal 1ⁿ di ros", "block.minecraft.banner.half_horizontal.white": "Çoncjât: tal 1ⁿ di blanc", "block.minecraft.banner.half_horizontal.yellow": "Çoncjât: tal 1ⁿ di zâl", "block.minecraft.banner.half_horizontal_bottom.black": "Çoncjât: tal 2ᵗ di neri", "block.minecraft.banner.half_horizontal_bottom.blue": "Çoncjât: tal 2ᵗ di blu", "block.minecraft.banner.half_horizontal_bottom.brown": "Çoncjât: tal 2ᵗ di maron", "block.minecraft.banner.half_horizontal_bottom.cyan": "Çoncjât: tal 2ᵗ di ciano", "block.minecraft.banner.half_horizontal_bottom.gray": "Çoncjât: tal 2ᵗ di grîs", "block.minecraft.banner.half_horizontal_bottom.green": "Çoncjât: tal 2ᵗ di vert", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Çoncjât: tal 2ᵗ di blu clâr", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Çoncjât: tal 2ᵗ di grîs clâr", "block.minecraft.banner.half_horizontal_bottom.lime": "Çoncjât: tal 2ᵗ di vert limon", "block.minecraft.banner.half_horizontal_bottom.magenta": "Çoncjât: tal 2ᵗ di magenta", "block.minecraft.banner.half_horizontal_bottom.orange": "Çoncjât: tal 2ᵗ di naranç", "block.minecraft.banner.half_horizontal_bottom.pink": "Çoncjât: tal 2ᵗ di rose", "block.minecraft.banner.half_horizontal_bottom.purple": "Çoncjât: tal 2ᵗ di viole", "block.minecraft.banner.half_horizontal_bottom.red": "Çoncjât: tal 2ᵗ di ros", "block.minecraft.banner.half_horizontal_bottom.white": "Çoncjât: tal 2ᵗ di blanc", "block.minecraft.banner.half_horizontal_bottom.yellow": "Çoncjât: tal 2ᵗ di zâl", "block.minecraft.banner.half_vertical.black": "Partît: tal 1ⁿ di neri", "block.minecraft.banner.half_vertical.blue": "Partît: tal 1ⁿ di blu", "block.minecraft.banner.half_vertical.brown": "Partît: tal 1ⁿ di maron", "block.minecraft.banner.half_vertical.cyan": "Partît: tal 1ⁿ di ciano", "block.minecraft.banner.half_vertical.gray": "Partît: tal 1ⁿ di grîs", "block.minecraft.banner.half_vertical.green": "Partît: tal 1ⁿ di vert", "block.minecraft.banner.half_vertical.light_blue": "Partît: tal 1ⁿ di blu clâr", "block.minecraft.banner.half_vertical.light_gray": "Partît: tal 1ⁿ di grîs clâr", "block.minecraft.banner.half_vertical.lime": "Partît: tal 1ⁿ di vert limon", "block.minecraft.banner.half_vertical.magenta": "Partît: tal 1ⁿ di magenta", "block.minecraft.banner.half_vertical.orange": "Partît: tal 1ⁿ di naranç", "block.minecraft.banner.half_vertical.pink": "Partît: tal 1ⁿ di rose", "block.minecraft.banner.half_vertical.purple": "Partît: tal 1ⁿ di viole", "block.minecraft.banner.half_vertical.red": "Partît: tal 1ⁿ di ros", "block.minecraft.banner.half_vertical.white": "Partît: tal 1ⁿ di blanc", "block.minecraft.banner.half_vertical.yellow": "Partît: tal 1ⁿ di zâl", "block.minecraft.banner.half_vertical_right.black": "Partît: tal 2ᵗ di neri", "block.minecraft.banner.half_vertical_right.blue": "Partît: tal 2ᵗ di blu", "block.minecraft.banner.half_vertical_right.brown": "Partît: tal 2ᵗ di maron", "block.minecraft.banner.half_vertical_right.cyan": "Partît: tal 2ᵗ di ciano", "block.minecraft.banner.half_vertical_right.gray": "Partît: tal 2ᵗ di grîs", "block.minecraft.banner.half_vertical_right.green": "Partît: tal 2ᵗ di vert", "block.minecraft.banner.half_vertical_right.light_blue": "Partît: tal 2ᵗ di blu clâr", "block.minecraft.banner.half_vertical_right.light_gray": "Partît: tal 2ᵗ di grîs clâr", "block.minecraft.banner.half_vertical_right.lime": "Partît: tal 2ᵗ di vert limon", "block.minecraft.banner.half_vertical_right.magenta": "Partît: tal 2ᵗ di magenta", "block.minecraft.banner.half_vertical_right.orange": "Partît: tal 2ᵗ di naranç", "block.minecraft.banner.half_vertical_right.pink": "Partît: tal 2ᵗ di rose", "block.minecraft.banner.half_vertical_right.purple": "Partît: tal 2ᵗ di viole", "block.minecraft.banner.half_vertical_right.red": "Partît: tal 2ᵗ di ros", "block.minecraft.banner.half_vertical_right.white": "Partît: tal 2ᵗ di blanc", "block.minecraft.banner.half_vertical_right.yellow": "Partît: tal 2ᵗ di zâl", "block.minecraft.banner.mojang.black": "Al logo di neri", "block.minecraft.banner.mojang.blue": "Al logo di blu", "block.minecraft.banner.mojang.brown": "Al logo di maron", "block.minecraft.banner.mojang.cyan": "Al logo di ciano", "block.minecraft.banner.mojang.gray": "Al logo di grîs", "block.minecraft.banner.mojang.green": "Al logo di vert", "block.minecraft.banner.mojang.light_blue": "Al logo di blu clâr", "block.minecraft.banner.mojang.light_gray": "Al logo di grîs clâr", "block.minecraft.banner.mojang.lime": "Al logo di vert limon", "block.minecraft.banner.mojang.magenta": "Al logo di magenta", "block.minecraft.banner.mojang.orange": "Al logo di naranç", "block.minecraft.banner.mojang.pink": "Al logo di rose", "block.minecraft.banner.mojang.purple": "Al logo di viole", "block.minecraft.banner.mojang.red": "Al logo di ros", "block.minecraft.banner.mojang.white": "Al logo di blanc", "block.minecraft.banner.mojang.yellow": "Al logo di zâl", "block.minecraft.banner.piglin.black": "Al music di neri", "block.minecraft.banner.piglin.blue": "Al music di blu", "block.minecraft.banner.piglin.brown": "Al music di maron", "block.minecraft.banner.piglin.cyan": "Al music di ciano", "block.minecraft.banner.piglin.gray": "Al music di grîs", "block.minecraft.banner.piglin.green": "Al music di vert", "block.minecraft.banner.piglin.light_blue": "Al music di blu clâr", "block.minecraft.banner.piglin.light_gray": "Al music di grîs clâr", "block.minecraft.banner.piglin.lime": "Al music di vert limon", "block.minecraft.banner.piglin.magenta": "Al music di magenta", "block.minecraft.banner.piglin.orange": "Al music di naranç", "block.minecraft.banner.piglin.pink": "Al music di rose", "block.minecraft.banner.piglin.purple": "Al music di viole", "block.minecraft.banner.piglin.red": "Al music di ros", "block.minecraft.banner.piglin.white": "Al music di blanc", "block.minecraft.banner.piglin.yellow": "Al music di zâl", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON> losanghe di neri", "block.minecraft.banner.rhombus.blue": "A<PERSON> losanghe di blu", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON> losanghe di maron", "block.minecraft.banner.rhombus.cyan": "Ae losanghe di ciano", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON> losanghe di grîs", "block.minecraft.banner.rhombus.green": "Ae losanghe di vert", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON> losanghe di blu clâr", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON> losanghe di grîs cl<PERSON>r", "block.minecraft.banner.rhombus.lime": "Ae losanghe di vert limon", "block.minecraft.banner.rhombus.magenta": "Ae losanghe di magenta", "block.minecraft.banner.rhombus.orange": "Ae losanghe di naranç", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON> los<PERSON>he di rose", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON> losanghe di viole", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON> losanghe di ros", "block.minecraft.banner.rhombus.white": "<PERSON>e losanghe di blanc", "block.minecraft.banner.rhombus.yellow": "<PERSON>e losanghe di zâl", "block.minecraft.banner.skull.black": "Ae crepe di neri", "block.minecraft.banner.skull.blue": "Ae crepe di blu", "block.minecraft.banner.skull.brown": "Ae crepe di maron", "block.minecraft.banner.skull.cyan": "Ae crepe di ciano", "block.minecraft.banner.skull.gray": "Ae crepe di grîs", "block.minecraft.banner.skull.green": "Ae crepe di vert", "block.minecraft.banner.skull.light_blue": "Ae crepe di blu clâr", "block.minecraft.banner.skull.light_gray": "Ae crepe di grîs clâr", "block.minecraft.banner.skull.lime": "Ae crepe di vert limon", "block.minecraft.banner.skull.magenta": "Ae crepe di magenta", "block.minecraft.banner.skull.orange": "Ae crepe di naranç", "block.minecraft.banner.skull.pink": "Ae crepe di rose", "block.minecraft.banner.skull.purple": "Ae crepe di viole", "block.minecraft.banner.skull.red": "Ae crepe di ros", "block.minecraft.banner.skull.white": "Ae crepe di blanc", "block.minecraft.banner.skull.yellow": "Ae crepe di zâl", "block.minecraft.banner.small_stripes.black": "Ai pâi di neri", "block.minecraft.banner.small_stripes.blue": "Ai pâi di blu", "block.minecraft.banner.small_stripes.brown": "Ai pâi di maron", "block.minecraft.banner.small_stripes.cyan": "Ai pâi di ciano", "block.minecraft.banner.small_stripes.gray": "Ai pâi di grîs", "block.minecraft.banner.small_stripes.green": "Ai pâi di vert", "block.minecraft.banner.small_stripes.light_blue": "Ai pâi di blu clâr", "block.minecraft.banner.small_stripes.light_gray": "Ai pâi di grîs clâr", "block.minecraft.banner.small_stripes.lime": "Ai pâi di vert limon", "block.minecraft.banner.small_stripes.magenta": "Ai pâi di magenta", "block.minecraft.banner.small_stripes.orange": "Ai pâi di naranç", "block.minecraft.banner.small_stripes.pink": "Ai pâi di rose", "block.minecraft.banner.small_stripes.purple": "Ai pâi di viole", "block.minecraft.banner.small_stripes.red": "Ai pâi di ros", "block.minecraft.banner.small_stripes.white": "Ai pâi di blanc", "block.minecraft.banner.small_stripes.yellow": "Ai pâi di zâl", "block.minecraft.banner.square_bottom_left.black": "<PERSON>ton diestri de ponte di neri", "block.minecraft.banner.square_bottom_left.blue": "<PERSON> c<PERSON>ton diestri de ponte di blu", "block.minecraft.banner.square_bottom_left.brown": "<PERSON> c<PERSON>ton diestri de ponte di maron", "block.minecraft.banner.square_bottom_left.cyan": "<PERSON> c<PERSON>ton diestri de ponte di ciano", "block.minecraft.banner.square_bottom_left.gray": "<PERSON>ton diestri de ponte di grîs", "block.minecraft.banner.square_bottom_left.green": "<PERSON> c<PERSON>ton diestri de ponte di vert", "block.minecraft.banner.square_bottom_left.light_blue": "<PERSON> cjanton diestri de ponte di blu clâr", "block.minecraft.banner.square_bottom_left.light_gray": "<PERSON> cjanton diestri de ponte di grîs cl<PERSON>r", "block.minecraft.banner.square_bottom_left.lime": "<PERSON> cjanton diestri de ponte di vert limon", "block.minecraft.banner.square_bottom_left.magenta": "<PERSON> c<PERSON>ton diestri de ponte di magenta", "block.minecraft.banner.square_bottom_left.orange": "<PERSON> c<PERSON>ton diestri de ponte di naranç", "block.minecraft.banner.square_bottom_left.pink": "<PERSON> cjanton diestri de ponte di rose", "block.minecraft.banner.square_bottom_left.purple": "<PERSON> c<PERSON>ton diestri de ponte di viole", "block.minecraft.banner.square_bottom_left.red": "<PERSON> c<PERSON>ton diestri de ponte di ros", "block.minecraft.banner.square_bottom_left.white": "<PERSON>ton diestri de ponte di blanc", "block.minecraft.banner.square_bottom_left.yellow": "<PERSON>ton diestri de ponte di zâl", "block.minecraft.banner.square_bottom_right.black": "Al cjanton di çampe de ponte di neri", "block.minecraft.banner.square_bottom_right.blue": "Al cjanton di çampe de ponte di blu", "block.minecraft.banner.square_bottom_right.brown": "Al cjanton di çampe de ponte di maron", "block.minecraft.banner.square_bottom_right.cyan": "Al cjanton di çampe de ponte di ciano", "block.minecraft.banner.square_bottom_right.gray": "Al cjanton di çampe de ponte di grîs", "block.minecraft.banner.square_bottom_right.green": "Al cjanton di çampe de ponte di vert", "block.minecraft.banner.square_bottom_right.light_blue": "Al cjanton di çampe de ponte di blu clâr", "block.minecraft.banner.square_bottom_right.light_gray": "Al cjanton di çampe de ponte di grîs clâr", "block.minecraft.banner.square_bottom_right.lime": "Al cjanton di çampe de ponte di vert limon", "block.minecraft.banner.square_bottom_right.magenta": "Al cjanton di çampe de ponte di magenta", "block.minecraft.banner.square_bottom_right.orange": "Al cjanton di çampe de ponte di naranç", "block.minecraft.banner.square_bottom_right.pink": "Al cjanton di çampe de ponte di rose", "block.minecraft.banner.square_bottom_right.purple": "Al cjanton di çampe de ponte di viole", "block.minecraft.banner.square_bottom_right.red": "Al cjanton di çampe de ponte di ros", "block.minecraft.banner.square_bottom_right.white": "Al cjanton di çampe de ponte di blanc", "block.minecraft.banner.square_bottom_right.yellow": "Al cjanton di çampe de ponte di zâl", "block.minecraft.banner.square_top_left.black": "<PERSON> cjanton diestri dal cjâf di neri", "block.minecraft.banner.square_top_left.blue": "<PERSON> cjanton diestri dal cjâf di blu", "block.minecraft.banner.square_top_left.brown": "<PERSON> cjanton diestri dal cjâf di maron", "block.minecraft.banner.square_top_left.cyan": "<PERSON> c<PERSON>ton diestri dal cjâf di ciano", "block.minecraft.banner.square_top_left.gray": "<PERSON> c<PERSON>ton diestri dal cjâf di grîs", "block.minecraft.banner.square_top_left.green": "<PERSON> cjanton diestri dal cjâf di vert", "block.minecraft.banner.square_top_left.light_blue": "<PERSON> cjanton diestri dal cjâf di blu clâr", "block.minecraft.banner.square_top_left.light_gray": "<PERSON> cjanton diestri dal cjâf di grîs clâr", "block.minecraft.banner.square_top_left.lime": "<PERSON> cjanton diestri dal cjâf di vert limon", "block.minecraft.banner.square_top_left.magenta": "<PERSON> cjanton diestri dal cjâf di magenta", "block.minecraft.banner.square_top_left.orange": "<PERSON> c<PERSON>ton diestri dal cjâf di naranç", "block.minecraft.banner.square_top_left.pink": "<PERSON> cjanton diestri dal cjâf di rose", "block.minecraft.banner.square_top_left.purple": "<PERSON> cjanton diestri dal cjâf di viole", "block.minecraft.banner.square_top_left.red": "<PERSON> cjanton diestri dal cjâf di ros", "block.minecraft.banner.square_top_left.white": "<PERSON> c<PERSON>ton diestri dal cjâf di blanc", "block.minecraft.banner.square_top_left.yellow": "<PERSON> cjanton diestri dal cjâ<PERSON> di <PERSON>l", "block.minecraft.banner.square_top_right.black": "Al cjanton di çampe dal cjâf di neri", "block.minecraft.banner.square_top_right.blue": "Al cjanton di çampe dal cjâf di blu", "block.minecraft.banner.square_top_right.brown": "Al cjanton di çampe dal cjâf di maron", "block.minecraft.banner.square_top_right.cyan": "Al cjanton di çampe dal cjâf di ciano", "block.minecraft.banner.square_top_right.gray": "Al cjanton di çampe dal cjâf di grîs", "block.minecraft.banner.square_top_right.green": "Al cjanton di çampe dal cjâf di vert", "block.minecraft.banner.square_top_right.light_blue": "Al cjanton di çampe dal cjâf di blu clâr", "block.minecraft.banner.square_top_right.light_gray": "Al cjanton di çampe dal cjâf di grîs clâr", "block.minecraft.banner.square_top_right.lime": "Al cjanton di çampe dal cjâf di vert limon", "block.minecraft.banner.square_top_right.magenta": "Al cjanton di çampe dal cjâf di magenta", "block.minecraft.banner.square_top_right.orange": "Al cjanton di çampe dal cjâf di naranç", "block.minecraft.banner.square_top_right.pink": "Al cjanton di çampe dal cjâf di rose", "block.minecraft.banner.square_top_right.purple": "Al cjanton di çampe dal cjâf di viole", "block.minecraft.banner.square_top_right.red": "Al cjanton di çampe dal cjâf di ros", "block.minecraft.banner.square_top_right.white": "Al cjanton di çampe dal cjâf di blanc", "block.minecraft.banner.square_top_right.yellow": "Al cjanton di çampe dal cjâf di zâl", "block.minecraft.banner.straight_cross.black": "Ae crôs di neri", "block.minecraft.banner.straight_cross.blue": "Ae crôs di blu", "block.minecraft.banner.straight_cross.brown": "Ae crôs di maron", "block.minecraft.banner.straight_cross.cyan": "Ae crôs di ciano", "block.minecraft.banner.straight_cross.gray": "Ae crôs di grîs", "block.minecraft.banner.straight_cross.green": "Ae crôs di vert", "block.minecraft.banner.straight_cross.light_blue": "Ae crôs di blu clâr", "block.minecraft.banner.straight_cross.light_gray": "Ae crôs di grîs clâr", "block.minecraft.banner.straight_cross.lime": "Ae crôs di vert limon", "block.minecraft.banner.straight_cross.magenta": "Ae crôs di magenta", "block.minecraft.banner.straight_cross.orange": "Ae crôs di naranç", "block.minecraft.banner.straight_cross.pink": "Ae crôs di rose", "block.minecraft.banner.straight_cross.purple": "Ae crôs di viole", "block.minecraft.banner.straight_cross.red": "Ae crôs di ros", "block.minecraft.banner.straight_cross.white": "Ae crôs di blanc", "block.minecraft.banner.straight_cross.yellow": "Ae crôs di zâl", "block.minecraft.banner.stripe_bottom.black": "Ae fasse in ponte di neri", "block.minecraft.banner.stripe_bottom.blue": "Ae fasse in ponte di blu", "block.minecraft.banner.stripe_bottom.brown": "Ae fasse in ponte di maron", "block.minecraft.banner.stripe_bottom.cyan": "Ae fasse in ponte di ciano", "block.minecraft.banner.stripe_bottom.gray": "Ae fasse in ponte di grîs", "block.minecraft.banner.stripe_bottom.green": "Ae fasse in ponte di vert", "block.minecraft.banner.stripe_bottom.light_blue": "Ae fasse in ponte di blu clâr", "block.minecraft.banner.stripe_bottom.light_gray": "Ae fasse in ponte di grîs clâr", "block.minecraft.banner.stripe_bottom.lime": "Ae fasse in ponte di vert limon", "block.minecraft.banner.stripe_bottom.magenta": "Ae fasse in ponte di magenta", "block.minecraft.banner.stripe_bottom.orange": "Ae fasse in ponte di naranç", "block.minecraft.banner.stripe_bottom.pink": "Ae fasse in ponte di rose", "block.minecraft.banner.stripe_bottom.purple": "Ae fasse in ponte di viole", "block.minecraft.banner.stripe_bottom.red": "Ae fasse in ponte di ros", "block.minecraft.banner.stripe_bottom.white": "Ae fasse in ponte di blanc", "block.minecraft.banner.stripe_bottom.yellow": "Ae fasse in ponte di zâl", "block.minecraft.banner.stripe_center.black": "Al pâl di neri", "block.minecraft.banner.stripe_center.blue": "Al pâl di blu", "block.minecraft.banner.stripe_center.brown": "Al pâl di maron", "block.minecraft.banner.stripe_center.cyan": "Al pâl di ciano", "block.minecraft.banner.stripe_center.gray": "Al pâl di grîs", "block.minecraft.banner.stripe_center.green": "Al pâl di vert", "block.minecraft.banner.stripe_center.light_blue": "Al pâl di blu clâr", "block.minecraft.banner.stripe_center.light_gray": "Al pâl di grîs clâr", "block.minecraft.banner.stripe_center.lime": "Al pâl di vert limon", "block.minecraft.banner.stripe_center.magenta": "Al pâl di magenta", "block.minecraft.banner.stripe_center.orange": "Al pâl di naranç", "block.minecraft.banner.stripe_center.pink": "Al pâl di rose", "block.minecraft.banner.stripe_center.purple": "Al pâl di viole", "block.minecraft.banner.stripe_center.red": "Al pâl di ros", "block.minecraft.banner.stripe_center.white": "Al pâl di blanc", "block.minecraft.banner.stripe_center.yellow": "Al pâl di zâl", "block.minecraft.banner.stripe_downleft.black": "Ae sbare di neri", "block.minecraft.banner.stripe_downleft.blue": "Ae sbare di blu", "block.minecraft.banner.stripe_downleft.brown": "Ae sbare di maron", "block.minecraft.banner.stripe_downleft.cyan": "Ae sbare di ciano", "block.minecraft.banner.stripe_downleft.gray": "Ae sbare di grîs", "block.minecraft.banner.stripe_downleft.green": "Ae sbare di vert", "block.minecraft.banner.stripe_downleft.light_blue": "Ae sbare di blu clâr", "block.minecraft.banner.stripe_downleft.light_gray": "Ae sbare di grîs clâr", "block.minecraft.banner.stripe_downleft.lime": "Ae sbare di vert limon", "block.minecraft.banner.stripe_downleft.magenta": "Ae sbare di magenta", "block.minecraft.banner.stripe_downleft.orange": "Ae sbare di naranç", "block.minecraft.banner.stripe_downleft.pink": "Ae sbare di rose", "block.minecraft.banner.stripe_downleft.purple": "Ae sbare di viole", "block.minecraft.banner.stripe_downleft.red": "Ae sbare di ros", "block.minecraft.banner.stripe_downleft.white": "Ae sbare di blanc", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON> sbare di <PERSON>âl", "block.minecraft.banner.stripe_downright.black": "Ae bande di neri", "block.minecraft.banner.stripe_downright.blue": "Ae bande di blu", "block.minecraft.banner.stripe_downright.brown": "Ae bande di maron", "block.minecraft.banner.stripe_downright.cyan": "Ae bande di ciano", "block.minecraft.banner.stripe_downright.gray": "Ae bande di grîs", "block.minecraft.banner.stripe_downright.green": "Ae bande di vert", "block.minecraft.banner.stripe_downright.light_blue": "Ae bande di blu clâr", "block.minecraft.banner.stripe_downright.light_gray": "Ae bande di grîs clâr", "block.minecraft.banner.stripe_downright.lime": "Ae bande di vert limon", "block.minecraft.banner.stripe_downright.magenta": "Ae bande di magenta", "block.minecraft.banner.stripe_downright.orange": "Ae bande di naranç", "block.minecraft.banner.stripe_downright.pink": "Ae bande di rose", "block.minecraft.banner.stripe_downright.purple": "Ae bande di viole", "block.minecraft.banner.stripe_downright.red": "Ae bande di ros", "block.minecraft.banner.stripe_downright.white": "Ae bande di blanc", "block.minecraft.banner.stripe_downright.yellow": "Ae bande di zâl", "block.minecraft.banner.stripe_left.black": "<PERSON> pâl diestri di neri", "block.minecraft.banner.stripe_left.blue": "<PERSON> pâl diestri di blu", "block.minecraft.banner.stripe_left.brown": "<PERSON> pâl diestri di maron", "block.minecraft.banner.stripe_left.cyan": "Al pâl diestri di ciano", "block.minecraft.banner.stripe_left.gray": "<PERSON> pâl diestri di grîs", "block.minecraft.banner.stripe_left.green": "Al pâl diestri di vert", "block.minecraft.banner.stripe_left.light_blue": "Al pâl diestri di blu clâr", "block.minecraft.banner.stripe_left.light_gray": "Al pâl diestri di grîs clâr", "block.minecraft.banner.stripe_left.lime": "Al pâl diestri di vert limon", "block.minecraft.banner.stripe_left.magenta": "Al pâl diestri di magenta", "block.minecraft.banner.stripe_left.orange": "Al pâl diestri di naranç", "block.minecraft.banner.stripe_left.pink": "<PERSON> pâl diestri di rose", "block.minecraft.banner.stripe_left.purple": "Al pâl diestri di viole", "block.minecraft.banner.stripe_left.red": "<PERSON> pâl diestri di ros", "block.minecraft.banner.stripe_left.white": "Al pâl diestri di blanc", "block.minecraft.banner.stripe_left.yellow": "Al pâl diestri di <PERSON>âl", "block.minecraft.banner.stripe_middle.black": "Ae fasse di neri", "block.minecraft.banner.stripe_middle.blue": "Ae fasse di blu", "block.minecraft.banner.stripe_middle.brown": "Ae fasse di maron", "block.minecraft.banner.stripe_middle.cyan": "Ae fasse di ciano", "block.minecraft.banner.stripe_middle.gray": "Ae fasse di grîs", "block.minecraft.banner.stripe_middle.green": "Ae fasse di vert", "block.minecraft.banner.stripe_middle.light_blue": "Ae fasse di blu clâr", "block.minecraft.banner.stripe_middle.light_gray": "Ae fasse di grîs clâr", "block.minecraft.banner.stripe_middle.lime": "Ae fasse di vert limon", "block.minecraft.banner.stripe_middle.magenta": "Ae fasse di magenta", "block.minecraft.banner.stripe_middle.orange": "Ae fasse di naranç", "block.minecraft.banner.stripe_middle.pink": "Ae fasse di rose", "block.minecraft.banner.stripe_middle.purple": "Ae fasse di viole", "block.minecraft.banner.stripe_middle.red": "Ae fasse di ros", "block.minecraft.banner.stripe_middle.white": "Ae fasse di blanc", "block.minecraft.banner.stripe_middle.yellow": "Ae fasse di zâl", "block.minecraft.banner.stripe_right.black": "Al pâl di çampe di neri", "block.minecraft.banner.stripe_right.blue": "Al pâl di çampe di blu", "block.minecraft.banner.stripe_right.brown": "Al pâl di çampe di maron", "block.minecraft.banner.stripe_right.cyan": "Al pâl di çampe di ciano", "block.minecraft.banner.stripe_right.gray": "Al pâl di çampe di grîs", "block.minecraft.banner.stripe_right.green": "Al pâl di çampe di vert", "block.minecraft.banner.stripe_right.light_blue": "Al pâl di çampe di blu clâr", "block.minecraft.banner.stripe_right.light_gray": "Al pâl di çampe di grîs clâr", "block.minecraft.banner.stripe_right.lime": "Al pâl di çampe di vert limon", "block.minecraft.banner.stripe_right.magenta": "Al pâl di çampe di magenta", "block.minecraft.banner.stripe_right.orange": "Al pâl di çampe di naranç", "block.minecraft.banner.stripe_right.pink": "Al pâl di çampe di rose", "block.minecraft.banner.stripe_right.purple": "Al pâl di çampe di viole", "block.minecraft.banner.stripe_right.red": "Al pâl di çampe di ros", "block.minecraft.banner.stripe_right.white": "Al pâl di çampe di blanc", "block.minecraft.banner.stripe_right.yellow": "Al pâl di çampe di zâl", "block.minecraft.banner.stripe_top.black": "Ae fasse in cjâf di neri", "block.minecraft.banner.stripe_top.blue": "Ae fasse in cjâf di blu", "block.minecraft.banner.stripe_top.brown": "Ae fasse in cjâf di maron", "block.minecraft.banner.stripe_top.cyan": "Ae fasse in cjâf di ciano", "block.minecraft.banner.stripe_top.gray": "Ae fasse in cjâf di grîs", "block.minecraft.banner.stripe_top.green": "Ae fasse in cjâf di vert", "block.minecraft.banner.stripe_top.light_blue": "Ae fasse in cjâf di blu clâr", "block.minecraft.banner.stripe_top.light_gray": "Ae fasse in cjâf di grîs clâr", "block.minecraft.banner.stripe_top.lime": "Ae fasse in cjâf di vert limon", "block.minecraft.banner.stripe_top.magenta": "Ae fasse in cjâf di magenta", "block.minecraft.banner.stripe_top.orange": "Ae fasse in cjâf di naranç", "block.minecraft.banner.stripe_top.pink": "Ae fasse in cjâf di rose", "block.minecraft.banner.stripe_top.purple": "Ae fasse in cjâf di viole", "block.minecraft.banner.stripe_top.red": "Ae fasse in cjâf di ros", "block.minecraft.banner.stripe_top.white": "Ae fasse in cjâf di blanc", "block.minecraft.banner.stripe_top.yellow": "Ae fasse in cjâf di zâl", "block.minecraft.banner.triangle_bottom.black": "Ae ponte di neri", "block.minecraft.banner.triangle_bottom.blue": "Ae ponte di blu", "block.minecraft.banner.triangle_bottom.brown": "Ae ponte di maron", "block.minecraft.banner.triangle_bottom.cyan": "Ae ponte di ciano", "block.minecraft.banner.triangle_bottom.gray": "Ae ponte di grîs", "block.minecraft.banner.triangle_bottom.green": "Ae ponte di vert", "block.minecraft.banner.triangle_bottom.light_blue": "Ae ponte di blu clâr", "block.minecraft.banner.triangle_bottom.light_gray": "Ae ponte di grîs clâr", "block.minecraft.banner.triangle_bottom.lime": "Ae ponte di vert limon", "block.minecraft.banner.triangle_bottom.magenta": "Ae ponte di magenta", "block.minecraft.banner.triangle_bottom.orange": "Ae ponte di naranç", "block.minecraft.banner.triangle_bottom.pink": "Ae ponte di rose", "block.minecraft.banner.triangle_bottom.purple": "Ae ponte di viole", "block.minecraft.banner.triangle_bottom.red": "Ae ponte di ros", "block.minecraft.banner.triangle_bottom.white": "Ae ponte di blanc", "block.minecraft.banner.triangle_bottom.yellow": "Ae ponte di zâl", "block.minecraft.banner.triangle_top.black": "Ae ponte ribaltade di neri", "block.minecraft.banner.triangle_top.blue": "Ae ponte ribaltade di blu", "block.minecraft.banner.triangle_top.brown": "Ae ponte ribaltade di maron", "block.minecraft.banner.triangle_top.cyan": "Ae ponte ribaltade di ciano", "block.minecraft.banner.triangle_top.gray": "Ae ponte ribaltade di grîs", "block.minecraft.banner.triangle_top.green": "Ae ponte ribaltade di vert", "block.minecraft.banner.triangle_top.light_blue": "Ae ponte ribaltade di blu clâr", "block.minecraft.banner.triangle_top.light_gray": "Ae ponte ribaltade di grîs clâr", "block.minecraft.banner.triangle_top.lime": "Ae ponte ribaltade di vert limon", "block.minecraft.banner.triangle_top.magenta": "Ae ponte ribaltade di magenta", "block.minecraft.banner.triangle_top.orange": "Ae ponte ribaltade di naranç", "block.minecraft.banner.triangle_top.pink": "Ae ponte ribaltade di rose", "block.minecraft.banner.triangle_top.purple": "Ae ponte ribaltade di viole", "block.minecraft.banner.triangle_top.red": "Ae ponte ribaltade di ros", "block.minecraft.banner.triangle_top.white": "Ae ponte ribaltade di blanc", "block.minecraft.banner.triangle_top.yellow": "Ae ponte ribaltade di zâl", "block.minecraft.banner.triangles_bottom.black": "Ae ponte dentelade di neri", "block.minecraft.banner.triangles_bottom.blue": "Ae ponte dentelade di blu", "block.minecraft.banner.triangles_bottom.brown": "Ae ponte dentelade di maron", "block.minecraft.banner.triangles_bottom.cyan": "Ae ponte dentelade di ciano", "block.minecraft.banner.triangles_bottom.gray": "Ae ponte dentelade di grîs", "block.minecraft.banner.triangles_bottom.green": "Ae ponte dentelade di vert", "block.minecraft.banner.triangles_bottom.light_blue": "Ae ponte dentelade di blu clâr", "block.minecraft.banner.triangles_bottom.light_gray": "Ae ponte dentelade di grîs c<PERSON>âr", "block.minecraft.banner.triangles_bottom.lime": "Ae ponte dentelade di vert limon", "block.minecraft.banner.triangles_bottom.magenta": "Ae ponte dentelade di magenta", "block.minecraft.banner.triangles_bottom.orange": "Ae ponte dentelade di naranç", "block.minecraft.banner.triangles_bottom.pink": "Ae ponte dentelade di rose", "block.minecraft.banner.triangles_bottom.purple": "Ae ponte dentelade di viole", "block.minecraft.banner.triangles_bottom.red": "Ae ponte dentelade di ros", "block.minecraft.banner.triangles_bottom.white": "Ae ponte dentelade di blanc", "block.minecraft.banner.triangles_bottom.yellow": "Ae ponte dentelade di zâl", "block.minecraft.banner.triangles_top.black": "Al cjâf den<PERSON> di neri", "block.minecraft.banner.triangles_top.blue": "Al cjâf den<PERSON> di blu", "block.minecraft.banner.triangles_top.brown": "Al cj<PERSON><PERSON> den<PERSON> di maron", "block.minecraft.banner.triangles_top.cyan": "Al cjâf den<PERSON> di ciano", "block.minecraft.banner.triangles_top.gray": "Al c<PERSON><PERSON><PERSON> den<PERSON> di g<PERSON>î<PERSON>", "block.minecraft.banner.triangles_top.green": "Al cjâf den<PERSON> di vert", "block.minecraft.banner.triangles_top.light_blue": "Al cjâf den<PERSON> di blu clâr", "block.minecraft.banner.triangles_top.light_gray": "Al cjâ<PERSON> den<PERSON> di grîs clâr", "block.minecraft.banner.triangles_top.lime": "Al cjâf den<PERSON> di vert limon", "block.minecraft.banner.triangles_top.magenta": "Al cjâf den<PERSON> di magenta", "block.minecraft.banner.triangles_top.orange": "Al cjâf dentelât di naranç", "block.minecraft.banner.triangles_top.pink": "<PERSON> c<PERSON><PERSON><PERSON> den<PERSON> di rose", "block.minecraft.banner.triangles_top.purple": "Al cj<PERSON><PERSON> den<PERSON> di viole", "block.minecraft.banner.triangles_top.red": "Al cjâf den<PERSON> di ros", "block.minecraft.banner.triangles_top.white": "Al cjâf den<PERSON> di blanc", "block.minecraft.banner.triangles_top.yellow": "Al cjâf den<PERSON>âl", "block.minecraft.barrel": "Caratel", "block.minecraft.barrier": "<PERSON><PERSON>", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon.primary": "<PERSON><PERSON><PERSON> primari", "block.minecraft.beacon.secondary": "<PERSON><PERSON><PERSON> secondari", "block.minecraft.bed.no_sleep": "Tu puedis durmî dome di gnot o intant dai temporâi", "block.minecraft.bed.not_safe": "No tu puedis polsâ cum<PERSON>, cui mostris dongje", "block.minecraft.bed.obstructed": "Chest jet al è ostruît", "block.minecraft.bed.occupied": "Chest jet al è ocupât", "block.minecraft.bed.too_far_away": "No tu puedis polsâ cumò, il jet al è masse lontan", "block.minecraft.bedrock": "Cret di fonts", "block.minecraft.bee_nest": "<PERSON><PERSON><PERSON>", "block.minecraft.beehive": "Bôç artificiâl", "block.minecraft.beetroots": "Jerbe rave", "block.minecraft.bell": "Cjampane", "block.minecraft.big_dripleaf": "Planteforme grande", "block.minecraft.big_dripleaf_stem": "Stoc di planteforme grande", "block.minecraft.birch_button": "<PERSON>ton di bedoi", "block.minecraft.birch_door": "Puarte di bedoi", "block.minecraft.birch_fence": "Stangjaçade di bedoi", "block.minecraft.birch_fence_gate": "Portonut di bedoi", "block.minecraft.birch_hanging_sign": "Insegne di bedoi", "block.minecraft.birch_leaves": "<PERSON>eis di bedoi", "block.minecraft.birch_log": "Tronc di bedoi", "block.minecraft.birch_planks": "Breis di bedoi", "block.minecraft.birch_pressure_plate": "Plache di pression di bedoi", "block.minecraft.birch_sapling": "Arbossit di bedoi", "block.minecraft.birch_sign": "Cartel di bedoi", "block.minecraft.birch_slab": "<PERSON><PERSON> di bedoi", "block.minecraft.birch_stairs": "<PERSON><PERSON><PERSON> di bedoi", "block.minecraft.birch_trapdoor": "Trabuchel di bedoi", "block.minecraft.birch_wall_hanging_sign": "Insegne di parêt di bedoi", "block.minecraft.birch_wall_sign": "<PERSON><PERSON><PERSON> di parêt di bedoi", "block.minecraft.birch_wood": "<PERSON>oi", "block.minecraft.black_banner": "<PERSON><PERSON><PERSON> neri", "block.minecraft.black_bed": "Jet neri", "block.minecraft.black_candle": "<PERSON><PERSON><PERSON><PERSON> nere", "block.minecraft.black_candle_cake": "<PERSON>te cun cjandele nere", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON><PERSON>ri", "block.minecraft.black_concrete": "<PERSON><PERSON> neri", "block.minecraft.black_concrete_powder": "<PERSON><PERSON> neri in polvar", "block.minecraft.black_glazed_terracotta": "Crep dât di smalt neri", "block.minecraft.black_shulker_box": "Scjatule di shulker nere", "block.minecraft.black_stained_glass": "Veri colorât di neri", "block.minecraft.black_stained_glass_pane": "Panel di veri colôr neri", "block.minecraft.black_terracotta": "<PERSON><PERSON><PERSON> neri", "block.minecraft.black_wool": "<PERSON> nere", "block.minecraft.blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.blackstone_slab": "<PERSON><PERSON> di pier<PERSON>e", "block.minecraft.blackstone_stairs": "<PERSON><PERSON><PERSON>e", "block.minecraft.blackstone_wall": "<PERSON><PERSON> di pier<PERSON>e", "block.minecraft.blast_furnace": "For a fusion", "block.minecraft.blue_banner": "<PERSON><PERSON><PERSON> blu", "block.minecraft.blue_bed": "Jet blu", "block.minecraft.blue_candle": "<PERSON><PERSON><PERSON><PERSON> blu", "block.minecraft.blue_candle_cake": "<PERSON>te cun cjandele blu", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON><PERSON> blu", "block.minecraft.blue_concrete": "<PERSON><PERSON> blu", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON> blu in polvar", "block.minecraft.blue_glazed_terracotta": "Crep dât di smalt blu", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON> blu", "block.minecraft.blue_orchid": "Orchidee blu", "block.minecraft.blue_shulker_box": "Scja<PERSON><PERSON> di shulker blu", "block.minecraft.blue_stained_glass": "Veri colorât di blu", "block.minecraft.blue_stained_glass_pane": "Panel di veri colôr blu", "block.minecraft.blue_terracotta": "Crep blu", "block.minecraft.blue_wool": "Lane blu", "block.minecraft.bone_block": "Bloc di vues", "block.minecraft.bookshelf": "Librarie", "block.minecraft.brain_coral": "Coral dai cerviei", "block.minecraft.brain_coral_block": "Bloc di coral dai cerviei", "block.minecraft.brain_coral_fan": "<PERSON><PERSON><PERSON> dal coral dai cerviei", "block.minecraft.brain_coral_wall_fan": "<PERSON><PERSON><PERSON> su mûr dal coral dai cerviei", "block.minecraft.brewing_stand": "<PERSON><PERSON>", "block.minecraft.brick_slab": "Lastre di modons", "block.minecraft.brick_stairs": "Scjalins di modons", "block.minecraft.brick_wall": "<PERSON><PERSON> di modons", "block.minecraft.bricks": "Modons", "block.minecraft.brown_banner": "<PERSON><PERSON><PERSON> maron", "block.minecraft.brown_bed": "Jet maron", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "block.minecraft.brown_candle_cake": "<PERSON>te cun cjandele maron", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON><PERSON> maron", "block.minecraft.brown_concrete": "<PERSON><PERSON> maron", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON> maron in polvar", "block.minecraft.brown_glazed_terracotta": "Crep dât di smalt maron", "block.minecraft.brown_mushroom": "Fonc maron", "block.minecraft.brown_mushroom_block": "Bloc di fonc maron", "block.minecraft.brown_shulker_box": "Scjatule di shulker maron", "block.minecraft.brown_stained_glass": "Veri colorât di maron", "block.minecraft.brown_stained_glass_pane": "Panel di veri colôr maron", "block.minecraft.brown_terracotta": "<PERSON><PERSON><PERSON> maron", "block.minecraft.brown_wool": "<PERSON> maron", "block.minecraft.bubble_column": "Colone di bufulis", "block.minecraft.bubble_coral": "Coral des bufulis", "block.minecraft.bubble_coral_block": "Bloc di coral des bufulis", "block.minecraft.bubble_coral_fan": "<PERSON><PERSON><PERSON> dal <PERSON> des bufulis", "block.minecraft.bubble_coral_wall_fan": "<PERSON><PERSON><PERSON> su mûr dal coral des bufulis", "block.minecraft.budding_amethyst": "Ametiste ingjemant", "block.minecraft.bush": "Baraç", "block.minecraft.cactus": "Cactus", "block.minecraft.cactus_flower": "Rose di cactus", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "Calcite", "block.minecraft.calibrated_sculk_sensor": "Sensôr di Sculk calibrât", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>", "block.minecraft.candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "<PERSON><PERSON> cun cjandele", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Banc dal cartograf", "block.minecraft.carved_pumpkin": "Coce intaiade", "block.minecraft.cauldron": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cave_air": "<PERSON><PERSON>", "block.minecraft.cave_vines": "<PERSON><PERSON><PERSON><PERSON><PERSON> dai landris", "block.minecraft.cave_vines_plant": "Plante di rimpighins dai landris", "block.minecraft.chain": "Cjadene", "block.minecraft.chain_command_block": "Bloc comants concatenâts", "block.minecraft.cherry_button": "Boton di cjariesâr", "block.minecraft.cherry_door": "Puarte di cjariesâr", "block.minecraft.cherry_fence": "Stangjaçade di cjariesâr", "block.minecraft.cherry_fence_gate": "Portonut di cjariesâr", "block.minecraft.cherry_hanging_sign": "Insegne di cjariesâr", "block.minecraft.cherry_leaves": "Fueis di c<PERSON>iesâr", "block.minecraft.cherry_log": "Tronc di cjariesâr", "block.minecraft.cherry_planks": "Breis di cjariesâr", "block.minecraft.cherry_pressure_plate": "Plache di pression di cjariesâr", "block.minecraft.cherry_sapling": "Arbossit di cjariesâr", "block.minecraft.cherry_sign": "Cartel di cjariesâr", "block.minecraft.cherry_slab": "<PERSON><PERSON>", "block.minecraft.cherry_stairs": "<PERSON><PERSON><PERSON> c<PERSON>iesâr", "block.minecraft.cherry_trapdoor": "Trabuchel di cjariesâr", "block.minecraft.cherry_wall_hanging_sign": "Insegne di parêt di c<PERSON>iesâr", "block.minecraft.cherry_wall_sign": "Targhe di parêt di <PERSON>â<PERSON>", "block.minecraft.cherry_wood": "<PERSON>", "block.minecraft.chest": "Baûl", "block.minecraft.chipped_anvil": "<PERSON><PERSON>", "block.minecraft.chiseled_bookshelf": "Li<PERSON><PERSON> cese<PERSON>", "block.minecraft.chiseled_copper": "<PERSON>", "block.minecraft.chiseled_deepslate": "Ardesie profonde ceselade", "block.minecraft.chiseled_nether_bricks": "<PERSON><PERSON><PERSON> dal <PERSON> c<PERSON>", "block.minecraft.chiseled_polished_blackstone": "<PERSON><PERSON><PERSON> lustrade ceselade", "block.minecraft.chiseled_quartz_block": "Bloc di cuarç c<PERSON>ât", "block.minecraft.chiseled_red_sandstone": "Arenarie rosse ceselade", "block.minecraft.chiseled_resin_bricks": "Modons di resine cesel<PERSON>ts", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON> cese<PERSON>", "block.minecraft.chiseled_stone_bricks": "Modons di piere c<PERSON>", "block.minecraft.chiseled_tuff": "<PERSON><PERSON>", "block.minecraft.chiseled_tuff_bricks": "<PERSON><PERSON><PERSON> di tof c<PERSON>", "block.minecraft.chorus_flower": "<PERSON>", "block.minecraft.chorus_plant": "<PERSON><PERSON>", "block.minecraft.clay": "<PERSON><PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Rose-voli sierade", "block.minecraft.coal_block": "Bloc di cjarbon", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "Tiere grese", "block.minecraft.cobbled_deepslate": "Claps di ardesie profonde", "block.minecraft.cobbled_deepslate_slab": "<PERSON>re di claps di ardesie profonde", "block.minecraft.cobbled_deepslate_stairs": "<PERSON><PERSON><PERSON> di claps di ardesie profonde", "block.minecraft.cobbled_deepslate_wall": "<PERSON><PERSON> di claps di ardesie profonde", "block.minecraft.cobblestone": "Claps", "block.minecraft.cobblestone_slab": "<PERSON><PERSON> di claps", "block.minecraft.cobblestone_stairs": "<PERSON><PERSON><PERSON> di claps", "block.minecraft.cobblestone_wall": "<PERSON><PERSON> di claps", "block.minecraft.cobweb": "Tele di ragn", "block.minecraft.cocoa": "Cacau", "block.minecraft.command_block": "Bloc comants", "block.minecraft.comparator": "Comparadôr di redstone", "block.minecraft.composter": "Compostiere", "block.minecraft.conduit": "Condot", "block.minecraft.copper_block": "<PERSON> di ram", "block.minecraft.copper_bulb": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.copper_door": "Puarte di ram", "block.minecraft.copper_grate": "Grade di ram", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON> di ram", "block.minecraft.cornflower": "Barburice", "block.minecraft.cracked_deepslate_bricks": "Modons di ardesie profonde crepâts", "block.minecraft.cracked_deepslate_tiles": "Piastrelis di ardesie profonde crepadis", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON><PERSON> dal <PERSON> cre<PERSON>", "block.minecraft.cracked_polished_blackstone_bricks": "Mo<PERSON><PERSON> di pierenere lustrade crepade", "block.minecraft.cracked_stone_bricks": "Modons di piere cre<PERSON>", "block.minecraft.crafter": "Fabricadôr", "block.minecraft.crafting_table": "Banc di lavôr", "block.minecraft.creaking_heart": "<PERSON><PERSON><PERSON> dal cric", "block.minecraft.creeper_head": "<PERSON><PERSON><PERSON><PERSON> di <PERSON>er", "block.minecraft.creeper_wall_head": "<PERSON><PERSON><PERSON><PERSON> di creeper a mûr", "block.minecraft.crimson_button": "Boton cremisin", "block.minecraft.crimson_door": "Puarte cremisine", "block.minecraft.crimson_fence": "Stangjaçade cremisine", "block.minecraft.crimson_fence_gate": "Portonut cremisin", "block.minecraft.crimson_fungus": "Fonc cremisin", "block.minecraft.crimson_hanging_sign": "Insegne cremisine", "block.minecraft.crimson_hyphae": "<PERSON><PERSON> cremisinis", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON> crem<PERSON>", "block.minecraft.crimson_planks": "Breis cremisinis", "block.minecraft.crimson_pressure_plate": "Plache di pression cremisine", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.crimson_sign": "Cartel cremisin", "block.minecraft.crimson_slab": "<PERSON><PERSON> cremisine", "block.minecraft.crimson_stairs": "<PERSON><PERSON><PERSON> cremisi<PERSON>", "block.minecraft.crimson_stem": "Tronc cremisin", "block.minecraft.crimson_trapdoor": "Trabuchel cremisin", "block.minecraft.crimson_wall_hanging_sign": "Insegne di parêt cremisine", "block.minecraft.crimson_wall_sign": "Targ<PERSON> di parêt cremisine", "block.minecraft.crying_obsidian": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.cut_copper": "Ram <PERSON>tai<PERSON>t", "block.minecraft.cut_copper_slab": "<PERSON><PERSON> di ram in<PERSON>", "block.minecraft.cut_copper_stairs": "<PERSON><PERSON><PERSON> intai<PERSON>", "block.minecraft.cut_red_sandstone": "Arenarie rosse intaiade", "block.minecraft.cut_red_sandstone_slab": "Lastre di arenarie rosse intaiade", "block.minecraft.cut_sandstone": "Arenarie intaiade", "block.minecraft.cut_sandstone_slab": "Lastre di arenarie intaiade", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON> ciano", "block.minecraft.cyan_bed": "Jet ciano", "block.minecraft.cyan_candle": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.cyan_candle_cake": "<PERSON>te cun cjandele ciano", "block.minecraft.cyan_carpet": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON> ciano", "block.minecraft.cyan_concrete_powder": "Bet<PERSON> ciano in polvar", "block.minecraft.cyan_glazed_terracotta": "Crep dât di smalt ciano", "block.minecraft.cyan_shulker_box": "Scjatule di shulker ciano", "block.minecraft.cyan_stained_glass": "Veri colorât di ciano", "block.minecraft.cyan_stained_glass_pane": "Panel di veri colôr ciano", "block.minecraft.cyan_terracotta": "Crep ciano", "block.minecraft.cyan_wool": "<PERSON> ciano", "block.minecraft.damaged_anvil": "<PERSON><PERSON>", "block.minecraft.dandelion": "Tale", "block.minecraft.dark_oak_button": "Boton di rôl scûr", "block.minecraft.dark_oak_door": "Puarte di rôl scûr", "block.minecraft.dark_oak_fence": "Stangjaçade di rôl scûr", "block.minecraft.dark_oak_fence_gate": "Portonut di rôl scûr", "block.minecraft.dark_oak_hanging_sign": "Insegne di rôl scûr", "block.minecraft.dark_oak_leaves": "Fueis di rôl scûr", "block.minecraft.dark_oak_log": "Tronc di rôl scûr", "block.minecraft.dark_oak_planks": "Breis di rôl scûr", "block.minecraft.dark_oak_pressure_plate": "Plache di pression di rôl scûr", "block.minecraft.dark_oak_sapling": "Arbossit di rôl scûr", "block.minecraft.dark_oak_sign": "Cartel di rôl scûr", "block.minecraft.dark_oak_slab": "<PERSON><PERSON> di rôl scûr", "block.minecraft.dark_oak_stairs": "Sc<PERSON>lins di rôl scûr", "block.minecraft.dark_oak_trapdoor": "Trabuchel di rôl scûr", "block.minecraft.dark_oak_wall_hanging_sign": "Insegne di parêt di rôl scûr", "block.minecraft.dark_oak_wall_sign": "Targ<PERSON> di parêt di rôl scûr", "block.minecraft.dark_oak_wood": "Len <PERSON> rôl scûr", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON><PERSON> scure", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON> di prismarine scure", "block.minecraft.dark_prismarine_stairs": "Sc<PERSON><PERSON> di prismarine scure", "block.minecraft.daylight_detector": "Rilevadôr lûs diurne", "block.minecraft.dead_brain_coral": "Coral dai cerviei muart", "block.minecraft.dead_brain_coral_block": "Bloc di coral dai cerviei muart", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON><PERSON> dal coral dai cerviei muart", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON> su mûr dal coral dai cerviei muart", "block.minecraft.dead_bubble_coral": "Coral des bufulis muart", "block.minecraft.dead_bubble_coral_block": "Bloc di coral des bufulis muart", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON><PERSON> dal coral des bufulis muart", "block.minecraft.dead_bubble_coral_wall_fan": "Gorgonie su mûr dal coral des bufulis muart", "block.minecraft.dead_bush": "Sterp sec", "block.minecraft.dead_fire_coral": "Coral des flamis muart", "block.minecraft.dead_fire_coral_block": "Bloc di coral des flamis muart", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON><PERSON> dal coral des flamis muart", "block.minecraft.dead_fire_coral_wall_fan": "Go<PERSON>ie su mûr dal coral des flamis muart", "block.minecraft.dead_horn_coral": "Coral dai cuars muart", "block.minecraft.dead_horn_coral_block": "Bloc di coral dai cuars muart", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON><PERSON> dal coral dai cuars muart", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON><PERSON> su mûr dal coral dai cuars muart", "block.minecraft.dead_tube_coral": "Coral dai tubui muart", "block.minecraft.dead_tube_coral_block": "Bloc di coral dai tubui muart", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON><PERSON> dal coral dai tubui muart", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON><PERSON> su mûr dal coral dai tubui muart", "block.minecraft.decorated_pot": "<PERSON><PERSON><PERSON> de<PERSON>", "block.minecraft.deepslate": "<PERSON><PERSON>ie profonde", "block.minecraft.deepslate_brick_slab": "Lastre di modons di ardesie profonde", "block.minecraft.deepslate_brick_stairs": "Scjalins di modons di ardesie profonde", "block.minecraft.deepslate_brick_wall": "<PERSON>ret di modons di ardesie profonde", "block.minecraft.deepslate_bricks": "Modons di ardesie profonde", "block.minecraft.deepslate_coal_ore": "<PERSON><PERSON><PERSON><PERSON> <PERSON>bon in ardesie profonde", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON><PERSON><PERSON> <PERSON> in ardesie profonde", "block.minecraft.deepslate_diamond_ore": "<PERSON><PERSON><PERSON><PERSON> <PERSON> in ardesie profonde", "block.minecraft.deepslate_emerald_ore": "Mine<PERSON><PERSON><PERSON> <PERSON> smeralt in ardesie profonde", "block.minecraft.deepslate_gold_ore": "<PERSON><PERSON><PERSON><PERSON> di aur in ardesie profonde", "block.minecraft.deepslate_iron_ore": "<PERSON><PERSON><PERSON><PERSON> <PERSON> in ardesie profonde", "block.minecraft.deepslate_lapis_ore": "Mine<PERSON><PERSON><PERSON> <PERSON> lapislazuli in ardesie profonde", "block.minecraft.deepslate_redstone_ore": "<PERSON><PERSON><PERSON><PERSON> di redstone in ardesie profonde", "block.minecraft.deepslate_tile_slab": "Lastre di piastrelis di ardesie profonde", "block.minecraft.deepslate_tile_stairs": "Scjalins di piastrelis di ardesie profonde", "block.minecraft.deepslate_tile_wall": "Muret di piastrelis di ardesie profonde", "block.minecraft.deepslate_tiles": "Piastrelis di ardesie profonde", "block.minecraft.detector_rail": "Sinis di rilevazion", "block.minecraft.diamond_block": "Bloc di diamant", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.diorite": "Diorite", "block.minecraft.diorite_slab": "Lastre di diorite", "block.minecraft.diorite_stairs": "Scjalins di diorite", "block.minecraft.diorite_wall": "<PERSON><PERSON> di diorite", "block.minecraft.dirt": "Tiere", "block.minecraft.dirt_path": "Piste di teragn", "block.minecraft.dispenser": "Distributôr", "block.minecraft.dragon_egg": "Ûf di drâc", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "<PERSON><PERSON><PERSON><PERSON> di <PERSON>râc a mûr", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Bloc di alighis secjadis", "block.minecraft.dripstone_block": "Bloc di speleoteme", "block.minecraft.dropper": "Tiradôr", "block.minecraft.emerald_block": "Bloc di smeralt", "block.minecraft.emerald_ore": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t", "block.minecraft.enchanting_table": "Banc par incjantesims", "block.minecraft.end_gateway": "Passaç pal End", "block.minecraft.end_portal": "Portâl dal End", "block.minecraft.end_portal_frame": "<PERSON><PERSON><PERSON><PERSON> dal port<PERSON>l dal <PERSON>", "block.minecraft.end_rod": "<PERSON>ete dal End", "block.minecraft.end_stone": "<PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "Lastre di modons di piere dal <PERSON>", "block.minecraft.end_stone_brick_stairs": "<PERSON><PERSON><PERSON> di modons di piere dal <PERSON>", "block.minecraft.end_stone_brick_wall": "<PERSON><PERSON> di modons di piere dal <PERSON>", "block.minecraft.end_stone_bricks": "Modons di piere dal <PERSON>", "block.minecraft.ender_chest": "Baûl dal End", "block.minecraft.exposed_chiseled_copper": "<PERSON> c<PERSON> espon<PERSON>t", "block.minecraft.exposed_copper": "Ram esponût", "block.minecraft.exposed_copper_bulb": "<PERSON><PERSON><PERSON> di ram esponude", "block.minecraft.exposed_copper_door": "Puarte di ram esponude", "block.minecraft.exposed_copper_grate": "Grade di ram esponude", "block.minecraft.exposed_copper_trapdoor": "Trabuchel di ram esponût", "block.minecraft.exposed_cut_copper": "Ram intaiât esponût", "block.minecraft.exposed_cut_copper_slab": "<PERSON>re di ram intai<PERSON>t esponût", "block.minecraft.exposed_cut_copper_stairs": "<PERSON><PERSON><PERSON> di ram intaiât esponût", "block.minecraft.farmland": "<PERSON><PERSON> a<PERSON>", "block.minecraft.fern": "<PERSON><PERSON>", "block.minecraft.fire": "Fûc", "block.minecraft.fire_coral": "Coral des flamis", "block.minecraft.fire_coral_block": "Bloc di coral des flamis", "block.minecraft.fire_coral_fan": "<PERSON><PERSON><PERSON> dal <PERSON> des flamis", "block.minecraft.fire_coral_wall_fan": "<PERSON><PERSON><PERSON> su mûr dal coral des flamis", "block.minecraft.firefly_bush": "Baraç di lusignis", "block.minecraft.fletching_table": "Banc pes frecis", "block.minecraft.flower_pot": "<PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea": "Azalee floride", "block.minecraft.flowering_azalea_leaves": "Fueis di azalee floride", "block.minecraft.frogspawn": "Ûfs di crot", "block.minecraft.frosted_ice": "<PERSON><PERSON><PERSON>", "block.minecraft.furnace": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.glass": "<PERSON><PERSON>", "block.minecraft.glass_pane": "Panel di veri", "block.minecraft.glow_lichen": "Lichen luminessent", "block.minecraft.glowstone": "<PERSON><PERSON> luminose", "block.minecraft.gold_block": "Bloc di aur", "block.minecraft.gold_ore": "<PERSON><PERSON><PERSON><PERSON> <PERSON> aur", "block.minecraft.granite": "<PERSON><PERSON><PERSON>", "block.minecraft.granite_slab": "<PERSON><PERSON> di granî<PERSON>", "block.minecraft.granite_stairs": "<PERSON><PERSON><PERSON> granît", "block.minecraft.granite_wall": "<PERSON><PERSON> di <PERSON>", "block.minecraft.grass": "<PERSON><PERSON><PERSON>", "block.minecraft.grass_block": "Bloc di jerbe", "block.minecraft.gravel": "Glerie", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON> gr<PERSON><PERSON>", "block.minecraft.gray_bed": "Jet grîs", "block.minecraft.gray_candle": "Cjandele grise", "block.minecraft.gray_candle_cake": "<PERSON>te cun cjandele grise", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON> gr<PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON> grîs in polvar", "block.minecraft.gray_glazed_terracotta": "Crep dât di smalt grîs", "block.minecraft.gray_shulker_box": "Scjatule di shulker grise", "block.minecraft.gray_stained_glass": "Veri colorât di grîs", "block.minecraft.gray_stained_glass_pane": "Panel di veri colôr grîs", "block.minecraft.gray_terracotta": "<PERSON><PERSON><PERSON> gr<PERSON><PERSON>", "block.minecraft.gray_wool": "Lane grise", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON> vert", "block.minecraft.green_bed": "Jet vert", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON><PERSON> verde", "block.minecraft.green_candle_cake": "<PERSON>te cun cjandele verde", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON><PERSON> vert", "block.minecraft.green_concrete": "Beton vert", "block.minecraft.green_concrete_powder": "Beton vert in polvar", "block.minecraft.green_glazed_terracotta": "Crep dât di smalt vert", "block.minecraft.green_shulker_box": "Scjatule di shulker verde", "block.minecraft.green_stained_glass": "Veri colorât di vert", "block.minecraft.green_stained_glass_pane": "Panel di veri colôr vert", "block.minecraft.green_terracotta": "Crep vert", "block.minecraft.green_wool": "Lane verde", "block.minecraft.grindstone": "<PERSON><PERSON>", "block.minecraft.hanging_roots": "<PERSON><PERSON><PERSON><PERSON> che a pendulin", "block.minecraft.hay_block": "Bale di fen", "block.minecraft.heavy_core": "<PERSON><PERSON><PERSON> pesant", "block.minecraft.heavy_weighted_pressure_plate": "Plache di pression par pês elevâts", "block.minecraft.honey_block": "Bloc di mîl", "block.minecraft.honeycomb_block": "Bloc di celets", "block.minecraft.hopper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "Coral dai cuars", "block.minecraft.horn_coral_block": "Bloc di coral dai cuars", "block.minecraft.horn_coral_fan": "<PERSON><PERSON><PERSON> dal coral dai cuars", "block.minecraft.horn_coral_wall_fan": "<PERSON><PERSON><PERSON> su mûr dal coral dai cuars", "block.minecraft.ice": "<PERSON>lace", "block.minecraft.infested_chiseled_stone_bricks": "Modons di piere ceselâts e infestâts", "block.minecraft.infested_cobblestone": "Claps infestâts", "block.minecraft.infested_cracked_stone_bricks": "Modons di piere crepâts e infestâts", "block.minecraft.infested_deepslate": "Ardesie profonde infestade", "block.minecraft.infested_mossy_stone_bricks": "Modons di piere cun muscli infestâts", "block.minecraft.infested_stone": "Piere infestade", "block.minecraft.infested_stone_bricks": "Modons di piere infestâts", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON> di <PERSON>er", "block.minecraft.iron_block": "<PERSON> di fier", "block.minecraft.iron_door": "Puarte di fier", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON><PERSON>er", "block.minecraft.iron_trapdoor": "Trabuche<PERSON> di <PERSON>er", "block.minecraft.jack_o_lantern": "<PERSON><PERSON><PERSON><PERSON> di coce", "block.minecraft.jigsaw": "<PERSON> berdei", "block.minecraft.jukebox": "Zirediscs", "block.minecraft.jungle_button": "Boton di len di jungle", "block.minecraft.jungle_door": "Puarte di len di jungle", "block.minecraft.jungle_fence": "Stang<PERSON>çade in len di jungle", "block.minecraft.jungle_fence_gate": "Portonut in len di jungle", "block.minecraft.jungle_hanging_sign": "Insegne di len di jungle", "block.minecraft.jungle_leaves": "Fueis di jungle", "block.minecraft.jungle_log": "Tronc di jungle", "block.minecraft.jungle_planks": "Breis di len di jungle", "block.minecraft.jungle_pressure_plate": "Plache di pression di len di jungle", "block.minecraft.jungle_sapling": "Arbossit di jungle", "block.minecraft.jungle_sign": "Cartel di len di jungle", "block.minecraft.jungle_slab": "Lastre di len di jungle", "block.minecraft.jungle_stairs": "<PERSON><PERSON><PERSON> di len di jungle", "block.minecraft.jungle_trapdoor": "Trabuchel di len di jungle", "block.minecraft.jungle_wall_hanging_sign": "Insegne di parêt di len di jungle", "block.minecraft.jungle_wall_sign": "Targ<PERSON> di parê<PERSON> in len di jungle", "block.minecraft.jungle_wood": "Len di jungle", "block.minecraft.kelp": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp_plant": "<PERSON><PERSON><PERSON> di alighe", "block.minecraft.ladder": "<PERSON><PERSON><PERSON> a man", "block.minecraft.lantern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lapis_block": "Bloc di lapislazuli", "block.minecraft.lapis_ore": "<PERSON><PERSON><PERSON><PERSON> <PERSON> lapislazuli", "block.minecraft.large_amethyst_bud": "Gjeme di ametiste grande", "block.minecraft.large_fern": "Fe<PERSON> grant", "block.minecraft.lava": "Lave", "block.minecraft.lava_cauldron": "Cjalderon di lave", "block.minecraft.leaf_litter": "Grum di fueis", "block.minecraft.lectern": "<PERSON><PERSON>", "block.minecraft.lever": "<PERSON><PERSON>", "block.minecraft.light": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_banner": "<PERSON><PERSON><PERSON> blu cl<PERSON>r", "block.minecraft.light_blue_bed": "Jet blu clâr", "block.minecraft.light_blue_candle": "<PERSON><PERSON><PERSON><PERSON> blu cl<PERSON>r", "block.minecraft.light_blue_candle_cake": "<PERSON><PERSON> cun cjandele blu clâr", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON><PERSON> blu cl<PERSON>r", "block.minecraft.light_blue_concrete": "<PERSON><PERSON> blu clâr", "block.minecraft.light_blue_concrete_powder": "<PERSON><PERSON> blu clâr in polvar", "block.minecraft.light_blue_glazed_terracotta": "Crep dât di smalt blu clâr", "block.minecraft.light_blue_shulker_box": "Scja<PERSON><PERSON> di shulker blu clâr", "block.minecraft.light_blue_stained_glass": "Veri colorât di blu clâr", "block.minecraft.light_blue_stained_glass_pane": "Panel di veri colôr blu clâr", "block.minecraft.light_blue_terracotta": "<PERSON><PERSON><PERSON> blu clâr", "block.minecraft.light_blue_wool": "<PERSON> blu clâr", "block.minecraft.light_gray_banner": "<PERSON><PERSON><PERSON> gr<PERSON><PERSON> c<PERSON>r", "block.minecraft.light_gray_bed": "Jet grîs clâr", "block.minecraft.light_gray_candle": "<PERSON><PERSON><PERSON><PERSON> gr<PERSON><PERSON> c<PERSON>r", "block.minecraft.light_gray_candle_cake": "<PERSON>te cun cjandele gr<PERSON><PERSON> cl<PERSON>r", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON><PERSON> gr<PERSON><PERSON> clâr", "block.minecraft.light_gray_concrete": "<PERSON><PERSON> gr<PERSON><PERSON> cl<PERSON>r", "block.minecraft.light_gray_concrete_powder": "<PERSON><PERSON> grîs cl<PERSON>r in polvar", "block.minecraft.light_gray_glazed_terracotta": "Crep dât di smalt grîs clâr", "block.minecraft.light_gray_shulker_box": "Scjatule di shulker gr<PERSON><PERSON> clâr", "block.minecraft.light_gray_stained_glass": "Veri colorât di grîs clâr", "block.minecraft.light_gray_stained_glass_pane": "Panel di veri colôr gr<PERSON><PERSON> clâr", "block.minecraft.light_gray_terracotta": "<PERSON><PERSON><PERSON> gr<PERSON><PERSON> c<PERSON>r", "block.minecraft.light_gray_wool": "<PERSON> gr<PERSON><PERSON> cl<PERSON>r", "block.minecraft.light_weighted_pressure_plate": "Plache di pression par pês lizêrs", "block.minecraft.lightning_rod": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lilac": "<PERSON><PERSON>", "block.minecraft.lily_of_the_valley": "Sigj<PERSON>l di Salomon", "block.minecraft.lily_pad": "Lavaç di aghe", "block.minecraft.lime_banner": "Stendart vert limon", "block.minecraft.lime_bed": "Jet vert limon", "block.minecraft.lime_candle": "Cjandele vert limon", "block.minecraft.lime_candle_cake": "Torte cun cjandele vert limon", "block.minecraft.lime_carpet": "Tapêt vert limon", "block.minecraft.lime_concrete": "Beton vert limon", "block.minecraft.lime_concrete_powder": "Beton vert limon in polvar", "block.minecraft.lime_glazed_terracotta": "Crep dât di smalt vert limon", "block.minecraft.lime_shulker_box": "Scjatule di shulker vert limon", "block.minecraft.lime_stained_glass": "Veri colorât di vert limon", "block.minecraft.lime_stained_glass_pane": "Panel di veri colôr vert limon", "block.minecraft.lime_terracotta": "Crep vert limon", "block.minecraft.lime_wool": "Lane vert limon", "block.minecraft.lodestone": "Magnetite", "block.minecraft.loom": "Telâr", "block.minecraft.magenta_banner": "<PERSON>endart magenta", "block.minecraft.magenta_bed": "Jet magenta", "block.minecraft.magenta_candle": "<PERSON><PERSON>dele magenta", "block.minecraft.magenta_candle_cake": "Torte cun cjandele magenta", "block.minecraft.magenta_carpet": "Tapêt magenta", "block.minecraft.magenta_concrete": "Beton magenta", "block.minecraft.magenta_concrete_powder": "Beton magenta in polvar", "block.minecraft.magenta_glazed_terracotta": "Crep dât di smalt magenta", "block.minecraft.magenta_shulker_box": "Scjatule di shulker magenta", "block.minecraft.magenta_stained_glass": "Veri colorât di magenta", "block.minecraft.magenta_stained_glass_pane": "Panel di veri colôr magenta", "block.minecraft.magenta_terracotta": "Crep magenta", "block.minecraft.magenta_wool": "Lane magenta", "block.minecraft.magma_block": "Bloc di magme", "block.minecraft.mangrove_button": "Boton di mangrovie", "block.minecraft.mangrove_door": "Puarte di mangrovie", "block.minecraft.mangrove_fence": "Stangjaçade di mangrovie", "block.minecraft.mangrove_fence_gate": "Portonut di mangrovie", "block.minecraft.mangrove_hanging_sign": "Insegne di mangrovie", "block.minecraft.mangrove_leaves": "Fueis di mangrovie", "block.minecraft.mangrove_log": "Tronc di mangrovie", "block.minecraft.mangrove_planks": "Breis di mangrovie", "block.minecraft.mangrove_pressure_plate": "Plache di pression di mangrovie", "block.minecraft.mangrove_propagule": "Propagul di mangrovie", "block.minecraft.mangrove_roots": "Lidrîs di mangrovie", "block.minecraft.mangrove_sign": "Cartel di mangrovie", "block.minecraft.mangrove_slab": "<PERSON><PERSON> di mangrovie", "block.minecraft.mangrove_stairs": "<PERSON><PERSON><PERSON> di mangrovie", "block.minecraft.mangrove_trapdoor": "Trabuchel di mangrovie", "block.minecraft.mangrove_wall_hanging_sign": "Insegne di parêt di mangrovie", "block.minecraft.mangrove_wall_sign": "Targhe di parêt di mangrovie", "block.minecraft.mangrove_wood": "<PERSON> di mangrovie", "block.minecraft.medium_amethyst_bud": "<PERSON><PERSON><PERSON> di ametiste medie", "block.minecraft.melon": "<PERSON><PERSON><PERSON>", "block.minecraft.melon_stem": "Stoc di angurie", "block.minecraft.moss_block": "Bloc di muscli", "block.minecraft.moss_carpet": "<PERSON><PERSON><PERSON><PERSON> di muscli", "block.minecraft.mossy_cobblestone": "Claps cun muscli", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON> di claps cun muscli", "block.minecraft.mossy_cobblestone_stairs": "<PERSON><PERSON><PERSON> di claps cun muscli", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> di claps cul muscli", "block.minecraft.mossy_stone_brick_slab": "Lastre di modons di piere cun muscli", "block.minecraft.mossy_stone_brick_stairs": "Sc<PERSON>lins di modons di piere cun muscli", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON> di modons di piere cun muscli", "block.minecraft.mossy_stone_bricks": "Modons di piere cul muscli", "block.minecraft.moving_piston": "Piston in moviment", "block.minecraft.mud": "Pantan", "block.minecraft.mud_brick_slab": "Lastre di modons di pantan", "block.minecraft.mud_brick_stairs": "Scjalins di modons di pantan", "block.minecraft.mud_brick_wall": "<PERSON><PERSON> di modons di pantan", "block.minecraft.mud_bricks": "Modons di pantan", "block.minecraft.muddy_mangrove_roots": "Lidrîs di mangrovie impantanadis", "block.minecraft.mushroom_stem": "Gjambe di fonc", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Stangjaçade di modons dal Nether", "block.minecraft.nether_brick_slab": "<PERSON>re di modons dal Nether", "block.minecraft.nether_brick_stairs": "<PERSON><PERSON><PERSON> di modons dal Nether", "block.minecraft.nether_brick_wall": "<PERSON><PERSON> di modons dal Nether", "block.minecraft.nether_bricks": "<PERSON><PERSON><PERSON> dal <PERSON>", "block.minecraft.nether_gold_ore": "<PERSON><PERSON><PERSON><PERSON> <PERSON> aur dal <PERSON>", "block.minecraft.nether_portal": "<PERSON><PERSON><PERSON> pal <PERSON>", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON><PERSON><PERSON> <PERSON> cuarç dal <PERSON>her", "block.minecraft.nether_sprouts": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_wart": "<PERSON><PERSON><PERSON><PERSON><PERSON> dal <PERSON>", "block.minecraft.nether_wart_block": "Bloc di riçûi dal Nether", "block.minecraft.netherite_block": "Bloc di netherite", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Bloc sonôr", "block.minecraft.oak_button": "Boton di rôl", "block.minecraft.oak_door": "Puarte di rôl", "block.minecraft.oak_fence": "Stangjaçade di rôl", "block.minecraft.oak_fence_gate": "Portonut di rôl", "block.minecraft.oak_hanging_sign": "Insegne di rôl", "block.minecraft.oak_leaves": "Fueis di rôl", "block.minecraft.oak_log": "Tronc di rôl", "block.minecraft.oak_planks": "Breis di rôl", "block.minecraft.oak_pressure_plate": "Plache di pression di rôl", "block.minecraft.oak_sapling": "Arbossit di rôl", "block.minecraft.oak_sign": "Cartel di rôl", "block.minecraft.oak_slab": "<PERSON><PERSON> di rôl", "block.minecraft.oak_stairs": "Scjalins di rôl", "block.minecraft.oak_trapdoor": "Trabuchel di rôl", "block.minecraft.oak_wall_hanging_sign": "Insegne di parêt di rôl", "block.minecraft.oak_wall_sign": "<PERSON>rg<PERSON> di parê<PERSON> di rôl", "block.minecraft.oak_wood": "Len <PERSON> rôl", "block.minecraft.observer": "Osservadôr", "block.minecraft.obsidian": "Ossidiane", "block.minecraft.ochre_froglight": "<PERSON>rot<PERSON>l<PERSON>s colôr auriane", "block.minecraft.ominous_banner": "<PERSON><PERSON><PERSON> dal ma<PERSON>ri", "block.minecraft.open_eyeblossom": "Rose-voli vierte", "block.minecraft.orange_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_bed": "Jet naranç", "block.minecraft.orange_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_candle_cake": "<PERSON><PERSON> cun cjandele na<PERSON>", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.orange_concrete": "<PERSON><PERSON>", "block.minecraft.orange_concrete_powder": "Beton naranç in polvar", "block.minecraft.orange_glazed_terracotta": "Crep dât di smalt naranç", "block.minecraft.orange_shulker_box": "Scjatule di shulker naranç", "block.minecraft.orange_stained_glass": "Veri colorât di naranç", "block.minecraft.orange_stained_glass_pane": "Panel di veri colôr naranç", "block.minecraft.orange_terracotta": "Crep naranç", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_wool": "<PERSON>", "block.minecraft.oxeye_daisy": "Margarite", "block.minecraft.oxidized_chiseled_copper": "<PERSON>", "block.minecraft.oxidized_copper": "<PERSON>", "block.minecraft.oxidized_copper_bulb": "<PERSON><PERSON><PERSON> di ram ossidade", "block.minecraft.oxidized_copper_door": "Puarte di ram ossidade", "block.minecraft.oxidized_copper_grate": "Grade di ram ossidade", "block.minecraft.oxidized_copper_trapdoor": "Trabuchel di ram ossi<PERSON>t", "block.minecraft.oxidized_cut_copper": "<PERSON>", "block.minecraft.oxidized_cut_copper_slab": "<PERSON>re di ram in<PERSON><PERSON> o<PERSON>", "block.minecraft.oxidized_cut_copper_stairs": "<PERSON><PERSON><PERSON> di ram in<PERSON><PERSON> o<PERSON>", "block.minecraft.packed_ice": "Glaç pressât", "block.minecraft.packed_mud": "<PERSON><PERSON>", "block.minecraft.pale_hanging_moss": "Muscli palit che al pendule", "block.minecraft.pale_moss_block": "Bloc di muscli palit", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON><PERSON><PERSON> di muscli palit", "block.minecraft.pale_oak_button": "Boton di rôl palit", "block.minecraft.pale_oak_door": "Puarte di rôl palit", "block.minecraft.pale_oak_fence": "Stangjaçade di rôl palit", "block.minecraft.pale_oak_fence_gate": "Portonut di rôl palit", "block.minecraft.pale_oak_hanging_sign": "Insegne di rôl palit", "block.minecraft.pale_oak_leaves": "Fueis di rôl palit", "block.minecraft.pale_oak_log": "Tronc di rôl palit", "block.minecraft.pale_oak_planks": "Breis di rôl palit", "block.minecraft.pale_oak_pressure_plate": "Plache di pression di rôl palit", "block.minecraft.pale_oak_sapling": "Arbossit di rôl palit", "block.minecraft.pale_oak_sign": "Cartel di rôl palit", "block.minecraft.pale_oak_slab": "<PERSON><PERSON> di rôl palit", "block.minecraft.pale_oak_stairs": "<PERSON><PERSON><PERSON> rôl palit", "block.minecraft.pale_oak_trapdoor": "Trabuchel di rôl palit", "block.minecraft.pale_oak_wall_hanging_sign": "Insegne di parêt di rôl palit", "block.minecraft.pale_oak_wall_sign": "<PERSON>rg<PERSON> di parêt di rôl palit", "block.minecraft.pale_oak_wood": "<PERSON> rôl palit", "block.minecraft.pearlescent_froglight": "<PERSON>rot-l<PERSON>s col<PERSON>r perle", "block.minecraft.peony": "<PERSON><PERSON><PERSON>", "block.minecraft.petrified_oak_slab": "<PERSON><PERSON> di rôl impetride", "block.minecraft.piglin_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.piglin_wall_head": "<PERSON><PERSON><PERSON><PERSON> di <PERSON>lin a mûr", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON> rose", "block.minecraft.pink_bed": "Jet rose", "block.minecraft.pink_candle": "<PERSON><PERSON><PERSON><PERSON> rose", "block.minecraft.pink_candle_cake": "<PERSON>te cun cjandele rose", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON><PERSON> rose", "block.minecraft.pink_concrete": "<PERSON><PERSON> rose", "block.minecraft.pink_concrete_powder": "<PERSON><PERSON> rose in polvar", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON>p dât di smalt rose", "block.minecraft.pink_petals": "<PERSON><PERSON> rose", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON><PERSON> di shulker rose", "block.minecraft.pink_stained_glass": "Veri colorât di rose", "block.minecraft.pink_stained_glass_pane": "Panel di veri colôr rose", "block.minecraft.pink_terracotta": "<PERSON><PERSON><PERSON> rose", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON> rose", "block.minecraft.pink_wool": "<PERSON> rose", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pitcher_crop": "Colture di plante bocâl", "block.minecraft.pitcher_plant": "<PERSON><PERSON> b<PERSON>", "block.minecraft.player_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head.named": "Cjâf di %s", "block.minecraft.player_wall_head": "<PERSON><PERSON><PERSON><PERSON> di <PERSON> a mûr", "block.minecraft.podzol": "Tiere grise", "block.minecraft.pointed_dripstone": "Speleoteme spiçât", "block.minecraft.polished_andesite": "Andesite lustrade", "block.minecraft.polished_andesite_slab": "Lastre di andesite lustrade", "block.minecraft.polished_andesite_stairs": "<PERSON><PERSON><PERSON> di andesite lustrade", "block.minecraft.polished_basalt": "Ba<PERSON><PERSON> lustrât", "block.minecraft.polished_blackstone": "<PERSON><PERSON><PERSON> lustrade", "block.minecraft.polished_blackstone_brick_slab": "Lastre di modons di pierenere lustrade", "block.minecraft.polished_blackstone_brick_stairs": "<PERSON><PERSON><PERSON> di modons di pierenere lustrade", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON> di modons di pierenere lustrade", "block.minecraft.polished_blackstone_bricks": "Modons di pierenere lustrade", "block.minecraft.polished_blackstone_button": "<PERSON><PERSON> di pierenere lustrade", "block.minecraft.polished_blackstone_pressure_plate": "Plache di pression di pierenere lustrade", "block.minecraft.polished_blackstone_slab": "<PERSON><PERSON> di pierenere lustrade", "block.minecraft.polished_blackstone_stairs": "<PERSON><PERSON><PERSON> di pierenere lustrade", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON> di pierenere lustrade", "block.minecraft.polished_deepslate": "<PERSON><PERSON><PERSON> profonde lustrade", "block.minecraft.polished_deepslate_slab": "Lastre di ardesie profonde lustrade", "block.minecraft.polished_deepslate_stairs": "<PERSON><PERSON><PERSON> di ardesie profonde lustrade", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON> di ardesie profonde lustrade", "block.minecraft.polished_diorite": "Diorite lustrade", "block.minecraft.polished_diorite_slab": "Lastre di diorite lustrade", "block.minecraft.polished_diorite_stairs": "<PERSON><PERSON><PERSON> di diorite lustrade", "block.minecraft.polished_granite": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_granite_slab": "<PERSON>re di granît lustr<PERSON>t", "block.minecraft.polished_granite_stairs": "<PERSON><PERSON><PERSON> di granît lustr<PERSON>t", "block.minecraft.polished_tuff": "<PERSON><PERSON>", "block.minecraft.polished_tuff_slab": "<PERSON>re di tof lustrât", "block.minecraft.polished_tuff_stairs": "<PERSON><PERSON><PERSON> di tof lustr<PERSON>t", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON> di tof lustr<PERSON>t", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Arbossit di agacie in vâs", "block.minecraft.potted_allium": "Allium in vâs", "block.minecraft.potted_azalea_bush": "Azalee in vâs", "block.minecraft.potted_azure_bluet": "Houstonia caerulea in vâs", "block.minecraft.potted_bamboo": "Bambù in vâs", "block.minecraft.potted_birch_sapling": "Arbossit di bedoi in vâs", "block.minecraft.potted_blue_orchid": "Orchidee blu in vâs", "block.minecraft.potted_brown_mushroom": "Fonc maron in vâs", "block.minecraft.potted_cactus": "Cactus in vâs", "block.minecraft.potted_cherry_sapling": "Arbossit di cjariesâr in vâs", "block.minecraft.potted_closed_eyeblossom": "Rose-voli sierade in vâs", "block.minecraft.potted_cornflower": "Barburice in vâs", "block.minecraft.potted_crimson_fungus": "Fonc cremisin in vâs", "block.minecraft.potted_crimson_roots": "Lidr<PERSON>s cremisi<PERSON> in vâs", "block.minecraft.potted_dandelion": "Tale in vâs", "block.minecraft.potted_dark_oak_sapling": "Arbossit di rôl scûr in vâs", "block.minecraft.potted_dead_bush": "Sterp sec in vâs", "block.minecraft.potted_fern": "Felet in vâs", "block.minecraft.potted_flowering_azalea_bush": "Azalee floride in vâs", "block.minecraft.potted_jungle_sapling": "Arbossit di jungle in vâs", "block.minecraft.potted_lily_of_the_valley": "Sigj<PERSON><PERSON> di Salomon in vâs", "block.minecraft.potted_mangrove_propagule": "Propagul di mangrovie in vâs", "block.minecraft.potted_oak_sapling": "Arbossit di rôl in vâs", "block.minecraft.potted_open_eyeblossom": "Rose-voli vierte in vâs", "block.minecraft.potted_orange_tulip": "<PERSON><PERSON><PERSON> naranç in vâs", "block.minecraft.potted_oxeye_daisy": "Margherite in vâs", "block.minecraft.potted_pale_oak_sapling": "Arbossit di rôl palit in vâs", "block.minecraft.potted_pink_tulip": "<PERSON><PERSON><PERSON> rose in vâs", "block.minecraft.potted_poppy": "<PERSON><PERSON> in vâs", "block.minecraft.potted_red_mushroom": "Fonc ros in vâs", "block.minecraft.potted_red_tulip": "Tulipan ros in vâs", "block.minecraft.potted_spruce_sapling": "Arbossit di peç in vâs", "block.minecraft.potted_torchflower": "Rose-torce in vâs", "block.minecraft.potted_warped_fungus": "Fonc disnaturât in vâs", "block.minecraft.potted_warped_roots": "Lidr<PERSON><PERSON> disnat<PERSON> in vâs", "block.minecraft.potted_white_tulip": "Tulipan blanc in vâs", "block.minecraft.potted_wither_rose": "<PERSON> in vâs", "block.minecraft.powder_snow": "<PERSON><PERSON><PERSON>", "block.minecraft.powder_snow_cauldron": "Cjalderon di nêf farinose", "block.minecraft.powered_rail": "Sinis aliment<PERSON>", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Lastre di modons di prismarine", "block.minecraft.prismarine_brick_stairs": "Scjalins di modons di prismarine", "block.minecraft.prismarine_bricks": "Modons di prismarine", "block.minecraft.prismarine_slab": "<PERSON>re di prismarine", "block.minecraft.prismarine_stairs": "Scjalins di prismarine", "block.minecraft.prismarine_wall": "<PERSON><PERSON> di prismarine", "block.minecraft.pumpkin": "Coce", "block.minecraft.pumpkin_stem": "Stoc de coce", "block.minecraft.purple_banner": "<PERSON><PERSON><PERSON> viole", "block.minecraft.purple_bed": "Jet viole", "block.minecraft.purple_candle": "<PERSON><PERSON><PERSON><PERSON> viole", "block.minecraft.purple_candle_cake": "<PERSON>te cun cjandele viole", "block.minecraft.purple_carpet": "<PERSON><PERSON><PERSON><PERSON> viole", "block.minecraft.purple_concrete": "<PERSON><PERSON> viole", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON> viole in polvar", "block.minecraft.purple_glazed_terracotta": "Crep dât di smalt viole", "block.minecraft.purple_shulker_box": "Scjatule di shulker viole", "block.minecraft.purple_stained_glass": "Veri colorât di viole", "block.minecraft.purple_stained_glass_pane": "Panel di veri colôr viole", "block.minecraft.purple_terracotta": "Crep viole", "block.minecraft.purple_wool": "<PERSON> viole", "block.minecraft.purpur_block": "Bloc di Purpur", "block.minecraft.purpur_pillar": "Pilastri di Purpur", "block.minecraft.purpur_slab": "Lastre di Purpur", "block.minecraft.purpur_stairs": "Scjalins di Purpur", "block.minecraft.quartz_block": "Bloc di cuarç", "block.minecraft.quartz_bricks": "Modons di cuarç", "block.minecraft.quartz_pillar": "Pilastri di cuarç", "block.minecraft.quartz_slab": "<PERSON>re di cuarç", "block.minecraft.quartz_stairs": "Scjalins di cuarç", "block.minecraft.rail": "<PERSON><PERSON>", "block.minecraft.raw_copper_block": "Bloc di ram grês", "block.minecraft.raw_gold_block": "Bloc di aur grês", "block.minecraft.raw_iron_block": "Bloc di fier grês", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON> ros", "block.minecraft.red_bed": "Jet ros", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON><PERSON> rosse", "block.minecraft.red_candle_cake": "<PERSON>te cun cjandele rosse", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON><PERSON> ros", "block.minecraft.red_concrete": "<PERSON>on ros", "block.minecraft.red_concrete_powder": "Beton ros in polvar", "block.minecraft.red_glazed_terracotta": "Crep dât di smalt ros", "block.minecraft.red_mushroom": "Fonc ros", "block.minecraft.red_mushroom_block": "Bloc di fonc ros", "block.minecraft.red_nether_brick_slab": "Lastre di modons ros dal Nether", "block.minecraft.red_nether_brick_stairs": "<PERSON><PERSON><PERSON> di modons ros dal Nether", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON> di modons ros dal <PERSON>her", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON> ros dal <PERSON>her", "block.minecraft.red_sand": "Savalon ros", "block.minecraft.red_sandstone": "Arenarie rosse", "block.minecraft.red_sandstone_slab": "Lastre di arenarie rosse", "block.minecraft.red_sandstone_stairs": "Scjalins di arenarie rosse", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON> di arenarie rosse", "block.minecraft.red_shulker_box": "Scjatule di shulker rosse", "block.minecraft.red_stained_glass": "Veri colorât di ros", "block.minecraft.red_stained_glass_pane": "Panel di veri colôr ros", "block.minecraft.red_terracotta": "Crep ros", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> ros", "block.minecraft.red_wool": "Lane rosse", "block.minecraft.redstone_block": "Bloc di redstone", "block.minecraft.redstone_lamp": "Lampade di redstone", "block.minecraft.redstone_ore": "Minerâl di redstone", "block.minecraft.redstone_torch": "Torce di redstone", "block.minecraft.redstone_wall_torch": "Torce di parêt di redstone", "block.minecraft.redstone_wire": "Fîl di redstone", "block.minecraft.reinforced_deepslate": "Ardesie profonde rinfuarçade", "block.minecraft.repeater": "Ripetidôr di redstone", "block.minecraft.repeating_command_block": "Bloc comants ripetûts", "block.minecraft.resin_block": "Bloc di resine", "block.minecraft.resin_brick_slab": "Lastre di modons di resine", "block.minecraft.resin_brick_stairs": "Scjalins di modons di resine", "block.minecraft.resin_brick_wall": "<PERSON><PERSON> di modons di resine", "block.minecraft.resin_bricks": "Modons di resine", "block.minecraft.resin_clump": "Grop di resine", "block.minecraft.respawn_anchor": "Ancure di rinassite", "block.minecraft.rooted_dirt": "<PERSON>e cun lidr<PERSON>s", "block.minecraft.rose_bush": "Baraç di rosis", "block.minecraft.sand": "Savalon", "block.minecraft.sandstone": "Arenarie", "block.minecraft.sandstone_slab": "Lastre di arenarie", "block.minecraft.sandstone_stairs": "Scjalins di arenarie", "block.minecraft.sandstone_wall": "<PERSON><PERSON> di arenarie", "block.minecraft.scaffolding": "Impalcadure", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Catalizadôr di Sculk", "block.minecraft.sculk_sensor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk_shrieker": "Sculk zigon", "block.minecraft.sculk_vein": "<PERSON><PERSON>", "block.minecraft.sea_lantern": "<PERSON><PERSON><PERSON><PERSON> dai mârs", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON><PERSON>âr", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON> dal mâr", "block.minecraft.set_spawn": "Pont di rinassite stabilît", "block.minecraft.short_dry_grass": "<PERSON><PERSON><PERSON> secje basse", "block.minecraft.short_grass": "<PERSON><PERSON><PERSON> basse", "block.minecraft.shroomlight": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.shulker_box": "Scjatule di shulker", "block.minecraft.skeleton_skull": "Crani di scheletri", "block.minecraft.skeleton_wall_skull": "Crani di scheletri a mûr", "block.minecraft.slime_block": "Bloc di gjeladine", "block.minecraft.small_amethyst_bud": "Gjeme di ametiste piçule", "block.minecraft.small_dripleaf": "Planteforme piçule", "block.minecraft.smithing_table": "Banc dal fari", "block.minecraft.smoker": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Basalt sliss", "block.minecraft.smooth_quartz": "Bloc di cuarç slis", "block.minecraft.smooth_quartz_slab": "Lastre di cuarç slis", "block.minecraft.smooth_quartz_stairs": "Scjalins di cuarç slis", "block.minecraft.smooth_red_sandstone": "Arearie rosse slisse", "block.minecraft.smooth_red_sandstone_slab": "Lastre di arenarie rosse slisse", "block.minecraft.smooth_red_sandstone_stairs": "Scjalins di arenarie rosse slisse", "block.minecraft.smooth_sandstone": "Arenarie slisse", "block.minecraft.smooth_sandstone_slab": "Lastre di arenarie slisse", "block.minecraft.smooth_sandstone_stairs": "Scjalins di arenarie slisse", "block.minecraft.smooth_stone": "<PERSON><PERSON> s<PERSON>", "block.minecraft.smooth_stone_slab": "<PERSON>re di piere slisse", "block.minecraft.sniffer_egg": "Ûf di nasadôr", "block.minecraft.snow": "<PERSON><PERSON><PERSON>", "block.minecraft.snow_block": "Bloc di nêf", "block.minecraft.soul_campfire": "Fogaron des animis", "block.minecraft.soul_fire": "Fûc des animis", "block.minecraft.soul_lantern": "Fer<PERSON>l des animis", "block.minecraft.soul_sand": "Savalon des animis", "block.minecraft.soul_soil": "Tiere des animis", "block.minecraft.soul_torch": "Torce des animis", "block.minecraft.soul_wall_torch": "Torce des animis di parêt", "block.minecraft.spawn.not_valid": "No tu âs un jet to o une ancure di rinassite cjariade, opûr a son ost<PERSON><PERSON>ts", "block.minecraft.spawner": "Generadôr di <PERSON>ris", "block.minecraft.spawner.desc1": "Interagjiss cun l'uf di nassite:", "block.minecraft.spawner.desc2": "Imposte il gjenar di creature", "block.minecraft.sponge": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spore_blossom": "<PERSON> sopor<PERSON>", "block.minecraft.spruce_button": "Boton di peç", "block.minecraft.spruce_door": "Puarte di peç", "block.minecraft.spruce_fence": "Stangjaçade di peç", "block.minecraft.spruce_fence_gate": "Portonut di peç", "block.minecraft.spruce_hanging_sign": "Insegne di peç", "block.minecraft.spruce_leaves": "Fueis di peç", "block.minecraft.spruce_log": "Tronc di peç", "block.minecraft.spruce_planks": "Breis di peç", "block.minecraft.spruce_pressure_plate": "Plache di pression di peç", "block.minecraft.spruce_sapling": "Arbossit di peç", "block.minecraft.spruce_sign": "Cartel di peç", "block.minecraft.spruce_slab": "<PERSON>re di peç", "block.minecraft.spruce_stairs": "Scjalins di peç", "block.minecraft.spruce_trapdoor": "Trabuchel di peç", "block.minecraft.spruce_wall_hanging_sign": "Insegne di parêt di peç", "block.minecraft.spruce_wall_sign": "Targhe di parêt di peç", "block.minecraft.spruce_wood": "Len di peç", "block.minecraft.sticky_piston": "Piston tacadiç", "block.minecraft.stone": "<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Lastre di modons di piere", "block.minecraft.stone_brick_stairs": "<PERSON><PERSON><PERSON> di modons di piere", "block.minecraft.stone_brick_wall": "<PERSON><PERSON> di modons di piere", "block.minecraft.stone_bricks": "Modons di piere", "block.minecraft.stone_button": "Boton di piere", "block.minecraft.stone_pressure_plate": "Plache di pression di piere", "block.minecraft.stone_slab": "<PERSON><PERSON> di piere", "block.minecraft.stone_stairs": "<PERSON><PERSON><PERSON> di piere", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.stripped_acacia_log": "Tronc di agacie spelât", "block.minecraft.stripped_acacia_wood": "Len di agacie spelât", "block.minecraft.stripped_bamboo_block": "Bloc di bambù <PERSON>", "block.minecraft.stripped_birch_log": "Tronc di bedoi spelât", "block.minecraft.stripped_birch_wood": "Len di bedoi spelât", "block.minecraft.stripped_cherry_log": "Tronc di cjariesâr spelât", "block.minecraft.stripped_cherry_wood": "<PERSON> s<PERSON>", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON> cremisi<PERSON> s<PERSON>", "block.minecraft.stripped_crimson_stem": "Tronc cremisin spelât", "block.minecraft.stripped_dark_oak_log": "Tronc di rôl scûr spelât", "block.minecraft.stripped_dark_oak_wood": "Len di rôl scûr spelât", "block.minecraft.stripped_jungle_log": "Tronc di jungle spelât", "block.minecraft.stripped_jungle_wood": "Len di jungle spelât", "block.minecraft.stripped_mangrove_log": "Tronc di mangrovie spelât", "block.minecraft.stripped_mangrove_wood": "Len di mangrovie spelât", "block.minecraft.stripped_oak_log": "Tronc di rôl spelât", "block.minecraft.stripped_oak_wood": "Len di rôl spelât", "block.minecraft.stripped_pale_oak_log": "Tronc di rôl palit spelât", "block.minecraft.stripped_pale_oak_wood": "Len di rôl palit spelât", "block.minecraft.stripped_spruce_log": "Tronc di peç spelât", "block.minecraft.stripped_spruce_wood": "Len di peç spelât", "block.minecraft.stripped_warped_hyphae": "<PERSON>is disnaturadis s<PERSON>", "block.minecraft.stripped_warped_stem": "Tronc disnaturât s<PERSON>ât", "block.minecraft.structure_block": "Bloc di struture", "block.minecraft.structure_void": "Vueit di struture", "block.minecraft.sugar_cane": "<PERSON><PERSON><PERSON>", "block.minecraft.sunflower": "Gjirasol", "block.minecraft.suspicious_gravel": "Glerie suspiete", "block.minecraft.suspicious_sand": "Savalon suspiet", "block.minecraft.sweet_berry_bush": "Baraç des pomulis dolcis", "block.minecraft.tall_dry_grass": "<PERSON><PERSON><PERSON> secje alte", "block.minecraft.tall_grass": "<PERSON><PERSON>e alte", "block.minecraft.tall_seagrass": "<PERSON><PERSON><PERSON> dal mâr alte", "block.minecraft.target": "<PERSON><PERSON><PERSON>", "block.minecraft.terracotta": "Crep", "block.minecraft.test_block": "Bloc di prove", "block.minecraft.test_instance_block": "Bloc di istance di prove", "block.minecraft.tinted_glass": "<PERSON><PERSON>", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "Lis esplosions di TNT a son disativadis", "block.minecraft.torch": "<PERSON><PERSON>", "block.minecraft.torchflower": "<PERSON><PERSON>to<PERSON>e", "block.minecraft.torchflower_crop": "Plante di rose-torce", "block.minecraft.trapped_chest": "Baûl trapule", "block.minecraft.trial_spawner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "Ganç pal fîl di <PERSON>", "block.minecraft.tube_coral": "Coral dai tubui", "block.minecraft.tube_coral_block": "Bloc di coral dai tubui", "block.minecraft.tube_coral_fan": "<PERSON><PERSON><PERSON> dal coral dai tubui", "block.minecraft.tube_coral_wall_fan": "<PERSON><PERSON><PERSON> su mûr dal coral dai tubui", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Lastre di modons di tof", "block.minecraft.tuff_brick_stairs": "Sc<PERSON>lins di modons di tof", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON> di modons di tof", "block.minecraft.tuff_bricks": "<PERSON><PERSON><PERSON> di tof", "block.minecraft.tuff_slab": "<PERSON><PERSON> di tof", "block.minecraft.tuff_stairs": "<PERSON><PERSON><PERSON>f", "block.minecraft.tuff_wall": "<PERSON><PERSON> di tof", "block.minecraft.turtle_egg": "Ûf di copasse", "block.minecraft.twisting_vines": "Rampighins intorteâts", "block.minecraft.twisting_vines_plant": "Plante rampighine intorteade", "block.minecraft.vault": "Cassefuarte", "block.minecraft.verdant_froglight": "<PERSON>rot<PERSON>l<PERSON>s colôr sverdeant", "block.minecraft.vine": "<PERSON><PERSON>", "block.minecraft.void_air": "<PERSON>ar di v<PERSON>it", "block.minecraft.wall_torch": "<PERSON>ce di <PERSON>ê<PERSON>", "block.minecraft.warped_button": "<PERSON><PERSON> di<PERSON>", "block.minecraft.warped_door": "Puarte disnaturade", "block.minecraft.warped_fence": "Stangjaçade disnaturade", "block.minecraft.warped_fence_gate": "Portonut disnaturât", "block.minecraft.warped_fungus": "Fonc disnaturât", "block.minecraft.warped_hanging_sign": "Insegne disnaturade", "block.minecraft.warped_hyphae": "<PERSON><PERSON> disnat<PERSON>", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_planks": "Breis disnaturadis", "block.minecraft.warped_pressure_plate": "Plache di pression disnaturade", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>", "block.minecraft.warped_sign": "Cartel disnaturât", "block.minecraft.warped_slab": "<PERSON><PERSON> disnat<PERSON>", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.warped_stem": "Tronc disnaturât", "block.minecraft.warped_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wall_hanging_sign": "Insegne di parêt disnaturade", "block.minecraft.warped_wall_sign": "Targ<PERSON> di parêt disnaturade", "block.minecraft.warped_wart_block": "Bloc di riçûi disnaturât", "block.minecraft.water": "<PERSON><PERSON><PERSON>", "block.minecraft.water_cauldron": "Cjalderon di aghe", "block.minecraft.waxed_chiseled_copper": "<PERSON> in<PERSON>", "block.minecraft.waxed_copper_block": "Bloc incerât di ram", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON><PERSON> <PERSON> ram incerade", "block.minecraft.waxed_copper_door": "Puarte di ram incerade", "block.minecraft.waxed_copper_grate": "Grade di ram incerade", "block.minecraft.waxed_copper_trapdoor": "Trabuche<PERSON> di ram in<PERSON>ât", "block.minecraft.waxed_cut_copper": "<PERSON> in<PERSON>", "block.minecraft.waxed_cut_copper_slab": "<PERSON><PERSON> di ram intai<PERSON>t in<PERSON>t", "block.minecraft.waxed_cut_copper_stairs": "<PERSON><PERSON><PERSON> di ram intai<PERSON> incerât", "block.minecraft.waxed_exposed_chiseled_copper": "<PERSON> c<PERSON>t esponût incerât", "block.minecraft.waxed_exposed_copper": "Ram esponût incerât", "block.minecraft.waxed_exposed_copper_bulb": "<PERSON><PERSON><PERSON> di ram esponude incerade", "block.minecraft.waxed_exposed_copper_door": "Puarte di ram esponude incerade", "block.minecraft.waxed_exposed_copper_grate": "Grade di ram esponude incerade", "block.minecraft.waxed_exposed_copper_trapdoor": "Trabuchel di ram esponût incerât", "block.minecraft.waxed_exposed_cut_copper": "Ram intaiât esponût incerât", "block.minecraft.waxed_exposed_cut_copper_slab": "Lastre di ram intaiât esponût incerât", "block.minecraft.waxed_exposed_cut_copper_stairs": "<PERSON><PERSON><PERSON> di ram intaiât esponût incerât", "block.minecraft.waxed_oxidized_chiseled_copper": "<PERSON> c<PERSON> ossi<PERSON> in<PERSON>t", "block.minecraft.waxed_oxidized_copper": "<PERSON> o<PERSON> in<PERSON>", "block.minecraft.waxed_oxidized_copper_bulb": "Lam<PERSON><PERSON> di ram ossidade incerade", "block.minecraft.waxed_oxidized_copper_door": "Puarte di ram ossidade incerade", "block.minecraft.waxed_oxidized_copper_grate": "Grade di ram ossidade incerade", "block.minecraft.waxed_oxidized_copper_trapdoor": "Trabuchel di ram ossid<PERSON>t in<PERSON>ât", "block.minecraft.waxed_oxidized_cut_copper": "<PERSON> in<PERSON> ossi<PERSON><PERSON> in<PERSON>t", "block.minecraft.waxed_oxidized_cut_copper_slab": "<PERSON>re di ram intai<PERSON>t ossid<PERSON>t incerât", "block.minecraft.waxed_oxidized_cut_copper_stairs": "<PERSON><PERSON><PERSON> di ram intaiât ossidât incerât", "block.minecraft.waxed_weathered_chiseled_copper": "<PERSON> c<PERSON>t corot incerât", "block.minecraft.waxed_weathered_copper": "<PERSON> corot incerât", "block.minecraft.waxed_weathered_copper_bulb": "<PERSON><PERSON><PERSON> <PERSON> ram corote incerade", "block.minecraft.waxed_weathered_copper_door": "Puarte di ram corote incerade", "block.minecraft.waxed_weathered_copper_grate": "Grade di ram corote incerade", "block.minecraft.waxed_weathered_copper_trapdoor": "Trab<PERSON><PERSON> di ram corot incerât", "block.minecraft.waxed_weathered_cut_copper": "<PERSON> in<PERSON><PERSON><PERSON> corot incerât", "block.minecraft.waxed_weathered_cut_copper_slab": "<PERSON><PERSON> di ram in<PERSON><PERSON>t corot incerât", "block.minecraft.waxed_weathered_cut_copper_stairs": "<PERSON><PERSON><PERSON> di ram in<PERSON><PERSON>t corot incerât", "block.minecraft.weathered_chiseled_copper": "<PERSON> c<PERSON> corot", "block.minecraft.weathered_copper": "<PERSON> corot", "block.minecraft.weathered_copper_bulb": "<PERSON><PERSON><PERSON> <PERSON> corote", "block.minecraft.weathered_copper_door": "Puarte di ram corote", "block.minecraft.weathered_copper_grate": "Grade di ram corote", "block.minecraft.weathered_copper_trapdoor": "Trab<PERSON><PERSON> di ram corot", "block.minecraft.weathered_cut_copper": "<PERSON> in<PERSON><PERSON> corot", "block.minecraft.weathered_cut_copper_slab": "<PERSON><PERSON> di ram in<PERSON><PERSON><PERSON> corot", "block.minecraft.weathered_cut_copper_stairs": "<PERSON><PERSON><PERSON> di ram in<PERSON><PERSON><PERSON> corot", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON><PERSON> vaiulints", "block.minecraft.weeping_vines_plant": "<PERSON>e rampighine vaiulinte", "block.minecraft.wet_sponge": "Sponze bagnade", "block.minecraft.wheat": "Forment", "block.minecraft.white_banner": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.white_bed": "Jet blanc", "block.minecraft.white_candle": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.white_candle_cake": "<PERSON><PERSON> cun cjandele blancje", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON><PERSON> blanc", "block.minecraft.white_concrete": "<PERSON><PERSON> blanc", "block.minecraft.white_concrete_powder": "Beton blanc in polvar", "block.minecraft.white_glazed_terracotta": "Crep dât di smalt blanc", "block.minecraft.white_shulker_box": "Scjatule di shulker blancje", "block.minecraft.white_stained_glass": "Veri colorât di blanc", "block.minecraft.white_stained_glass_pane": "Panel di veri colôr blanc", "block.minecraft.white_terracotta": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> blanc", "block.minecraft.white_wool": "<PERSON> blan<PERSON>", "block.minecraft.wildflowers": "Rosis salvadis", "block.minecraft.wither_rose": "<PERSON>", "block.minecraft.wither_skeleton_skull": "Crani di scheletri wither", "block.minecraft.wither_skeleton_wall_skull": "<PERSON>rani di scheletri wither a mûr", "block.minecraft.yellow_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_bed": "<PERSON>", "block.minecraft.yellow_candle": "<PERSON><PERSON><PERSON><PERSON> zale", "block.minecraft.yellow_candle_cake": "<PERSON>te cun cjandele zale", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete": "<PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON> zâl in polvar", "block.minecraft.yellow_glazed_terracotta": "Crep dât di smalt zâl", "block.minecraft.yellow_shulker_box": "Scjatule di shulker zale", "block.minecraft.yellow_stained_glass": "Veri colorât di zâl", "block.minecraft.yellow_stained_glass_pane": "Panel di veri col<PERSON><PERSON>", "block.minecraft.yellow_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_wool": "Lane zale", "block.minecraft.zombie_head": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON><PERSON> di zombi a mûr", "book.byAuthor": "di %1$s", "book.edit.title": "Videade di modifiche dal libri", "book.editTitle": "Inserìs titul dal libri:", "book.finalizeButton": "Firme e siere", "book.finalizeWarning": "Atenzion! Se tu firmis il libri, nol sarà plui pussibil modificâlu.", "book.generation.0": "Origjin<PERSON><PERSON>", "book.generation.1": "<PERSON><PERSON> dal or<PERSON>l", "book.generation.2": "Copie di une copie", "book.generation.3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "book.invalid.tag": "* Etichete di libri no valide *", "book.pageIndicator": "Pagjine %1$s di %2$s", "book.page_button.next": "Pagjine sucessive", "book.page_button.previous": "<PERSON><PERSON><PERSON><PERSON>e", "book.sign.title": "Videade di firme dal libri", "book.sign.titlebox": "Titul", "book.signButton": "Firme", "book.view.title": "Videade dal libri", "build.tooHigh": "Il limit di altece pes construzions al è di %s", "chat.cannotSend": "Impussibil inviâ il messaç de chat", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Fâs clic par teletraspuartâti", "chat.copy": "Copie intes notis", "chat.copy.click": "Fâs clic par copiâ intes notis", "chat.deleted_marker": "Chest messaç de chat al è stât eliminât dal servidôr.", "chat.disabled.chain_broken": "Chat disativade par vie di une cjadene rote. Prove a tornâ a conetiti.", "chat.disabled.expiredProfileKey": "Chat disativade par vie de clâf dal profîl scjadude. Prove a tornâ a conetiti.", "chat.disabled.invalid_command_signature": "Lis firmis dal argoment dal comant a mancjin opûr no son validis.", "chat.disabled.invalid_signature": "Firme de chat no valide. Torne a provâ a conetiti.", "chat.disabled.launcher": "Chat disativade tes opzions dal inviadôr. Impussibil inviâ il messaç.", "chat.disabled.missingProfileKey": "Chat disativade par vie de clâf dal prof<PERSON>l man<PERSON>t. Prove a tornâ a conetiti.", "chat.disabled.options": "Chat disativade tes opzions dal client.", "chat.disabled.out_of_order_chat": "Pachets de chat fûr ordin. Ise stade modificade la ore dal to sisteme?", "chat.disabled.profile": "La chat no je abilitade da lis impostazions dal to account. Frache \"%s\" di gnûf par viodi plui informazions.", "chat.disabled.profile.moreInfo": "La chat no je abilitade da lis impostazions dal to account. Impussibil inviâ o viodi i messaçs.", "chat.editBox": "chat", "chat.filtered": "<PERSON><PERSON><PERSON><PERSON><PERSON> dal servidôr.", "chat.filtered_full": "Il servidôr al à platât il to messaç par cualchi zuiadôr.", "chat.link.confirm": "Vierzi pardabon chest sît web?", "chat.link.confirmTrusted": "Vierzi chest colegament o copiâlu intes notis?", "chat.link.open": "Vierç tal navigadôr", "chat.link.warning": "No sta vierzi mai i colegaments di personis che no tu ti fidis!", "chat.queue": "[+%s rie(is) in spiete]", "chat.square_brackets": "[%s]", "chat.tag.error": "Il servidôr al à inviât un messaç no valit.", "chat.tag.modified": "Messaç modificât dal servidôr. Origjinâl:", "chat.tag.not_secure": "Messaç no verificât. Impussibil segnalâ.", "chat.tag.system": "Messaç dal servidôr. Impussibil segnalâ.", "chat.tag.system_single_player": "<PERSON><PERSON><PERSON> dal servid<PERSON>.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s al/e à completât la sfide %s", "chat.type.advancement.goal": "%s al/e à otignût l'obietîf %s", "chat.type.advancement.task": "%s al/e à completât il progrès %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "<PERSON><PERSON><PERSON><PERSON> ae scuadre", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s al/e dîs %s", "chat.validation_error": "<PERSON><PERSON><PERSON><PERSON> di validazion de chat", "chat_screen.message": "Messaç di inviâ: %s", "chat_screen.title": "Videade de chat", "chat_screen.usage": "Scrîf un messaç e frache Invie par inviâlu", "chunk.toast.checkLog": "<PERSON><PERSON><PERSON> il regjistri par vê plui detais", "chunk.toast.loadFailure": "Nol è stât pussibil cjariâ il toc a %s", "chunk.toast.lowDiskSpace": "Pôc spazi sul disc!", "chunk.toast.lowDiskSpace.description": "Il salvament dal mont al podarès falî.", "chunk.toast.saveFailure": "Impussibil salvâ il toc a %s", "clear.failed.multiple": "Nissun element cjatât su %s zuiadôrs", "clear.failed.single": "Nissun element cjatât sul zuiadôr %s", "color.minecraft.black": "<PERSON><PERSON>", "color.minecraft.blue": "Blu", "color.minecraft.brown": "Mar<PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.green": "<PERSON>ert", "color.minecraft.light_blue": "<PERSON> clâr", "color.minecraft.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.lime": "Vert limon", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "<PERSON><PERSON><PERSON>", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "Viole", "color.minecraft.red": "<PERSON><PERSON>", "color.minecraft.white": "<PERSON>", "color.minecraft.yellow": "<PERSON><PERSON><PERSON>", "command.context.here": "<--[ACHÌ]", "command.context.parse_error": "%s te posizion %s: %s", "command.exception": "Impussibil analizâ il comant: %s", "command.expected.separator": "Al jere spietât un spazi blanc ae fin di un argoment, ma a son stâts cjatâts dâts finâi", "command.failed": "Alc al è lât strucj tal cirî di eseguî chest comant", "command.forkLimit": "Rivâts al numar massim di contescj (%s)", "command.unknown.argument": "Argo<PERSON> sbali<PERSON>t pal comant", "command.unknown.command": "Comant no cognossût o incomplet, viôt chi sot pal erôr", "commands.advancement.criterionNotFound": "Il progrès %1$s nol conten il criteri '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Impussibil assegnâ il criteri '%s' dal progrès %s a %s zuiadôrs parcè che lu àn za", "commands.advancement.grant.criterion.to.many.success": "Il criteri '%s' dal progrès %s al è stât assegnât a %s zuiadôrs", "commands.advancement.grant.criterion.to.one.failure": "Impussibil assegnâ il criteri '%s' dal progrès %s a %s par vie che lu à za", "commands.advancement.grant.criterion.to.one.success": "Il criteri '%s' dal progrès %s al è stât assegnât a %s", "commands.advancement.grant.many.to.many.failure": "Impussibil assegnâ %s progrès a %s zuiadôrs par vie che ju àn za", "commands.advancement.grant.many.to.many.success": "%s progr<PERSON> a son stâts assegnâts a %s zuiad<PERSON>rs", "commands.advancement.grant.many.to.one.failure": "Impussibil assegnâ %s progrès a %s, parcè che ju à za", "commands.advancement.grant.many.to.one.success": "%s progr<PERSON> a son stâts assegnâts a %s", "commands.advancement.grant.one.to.many.failure": "Impussibil assegnâ il progrès %s a %s zuiadôrs parcè che lu àn za", "commands.advancement.grant.one.to.many.success": "Il progrès %s al è stât assegnât a %s zuiadôrs", "commands.advancement.grant.one.to.one.failure": "Impussibil assegnâ il progrès %s a %s parcè che lu à za", "commands.advancement.grant.one.to.one.success": "Il progrès %s al è stât assegnât a %s", "commands.advancement.revoke.criterion.to.many.failure": "Impussibil gjavâ il criteri '%s' dal progrès %s a %s zuiadôrs par vie che no lu àn", "commands.advancement.revoke.criterion.to.many.success": "Il criteri '%s' dal progrès %s al è stât gjavât a %s zuiadôrs", "commands.advancement.revoke.criterion.to.one.failure": "Impussibil gjavâ il criteri '%s' dal progrès %s a %s par vie che no lu à", "commands.advancement.revoke.criterion.to.one.success": "Il criteri '%s' dal progrès %s al è stât gjavât a %s", "commands.advancement.revoke.many.to.many.failure": "Impussibil gjavâ %s progrès a %s zuiadôrs par vie che no ju àn", "commands.advancement.revoke.many.to.many.success": "%s progr<PERSON> a son stâts gjavâts a %s zuiad<PERSON>rs", "commands.advancement.revoke.many.to.one.failure": "Impussibil gjavâ %s progrès a %s par vie che no ju à", "commands.advancement.revoke.many.to.one.success": "%s progr<PERSON> a son stâts gjavâts a %s", "commands.advancement.revoke.one.to.many.failure": "Impussibil gjavâ il progrès %s a %s zuiadôrs parcè che no lu àn", "commands.advancement.revoke.one.to.many.success": "Il progrès %s al è stât gjavât a %s zuiadôrs", "commands.advancement.revoke.one.to.one.failure": "Impussibil gjavâ il progrès %s a %s parcè che no lu à", "commands.advancement.revoke.one.to.one.success": "Il progrès %s al è stât gjavât a %s", "commands.attribute.base_value.get.success": "Il valôr base dal atribût %s de entitât %s al è %s", "commands.attribute.base_value.reset.success": "Il valôr base dal atribût %s de entitât %s al è stât ripristinât al valôr predefinît %s", "commands.attribute.base_value.set.success": "Il valôr base dal atribût %s de entitât %s al è stât metût a %s", "commands.attribute.failed.entity": "%s no je une entitât valide par chest comant", "commands.attribute.failed.modifier_already_present": "Il modificadôr %s al è za aplicât al atribût %s de entitât %s", "commands.attribute.failed.no_attribute": "La entitât %s no à l'atribût %s", "commands.attribute.failed.no_modifier": "L'atribût %s de entitât %s no à il modificadôr %s", "commands.attribute.modifier.add.success": "Il modificadôr %s al è stât aplicât al atribût %s de entitât %s", "commands.attribute.modifier.remove.success": "Il modificadôr %s al è stât gjavât dal atribût %s de entitât %s", "commands.attribute.modifier.value.get.success": "Il valôr dal modificadôr %s dal atribût %s de entitât %s al è %s", "commands.attribute.value.get.success": "Il valôr dal atribût %s de entitât %s al è %s", "commands.ban.failed": "Nissune modifiche. Il zuiadôr al è za bandît", "commands.ban.success": "%s bandît/ide: %s", "commands.banip.failed": "Nissune modifiche. Chel IP al è za bandît", "commands.banip.info": "Chest bant al interesse %s zuiadôr(s): %s", "commands.banip.invalid": "Direzion IP no valide o zuiadôr no cognossût", "commands.banip.success": "La direzion IP %s e je stade bandide: %s", "commands.banlist.entry": "%2$s al/e à bandît %1$s: %3$s", "commands.banlist.entry.unknown": "(No cognossût)", "commands.banlist.list": "A son %s persone(is) bandide(is):", "commands.banlist.none": "No son personis bandidis", "commands.bossbar.create.failed": "E esist za une sbare dal boss cul ID '%s'", "commands.bossbar.create.success": "La sbare dal boss %s e je stade creade", "commands.bossbar.get.max": "La sbare dal boss personalizade %s e à il valôr massim di %s", "commands.bossbar.get.players.none": "La sbare dal boss personalizade %s no à nissun zuiadôr associât in linie", "commands.bossbar.get.players.some": "La sbare dal boss personalizade %s e à %s zuiadôr(s) associât(s) in linie: %s", "commands.bossbar.get.value": "La sbare dal boss personalizade %s e à il valôr di %s", "commands.bossbar.get.visible.hidden": "La sbare dal boss personalizade %s in chest moment e je platade", "commands.bossbar.get.visible.visible": "La sbare dal boss personalizade %s in chest moment e je in mostre", "commands.bossbar.list.bars.none": "No je ative nissune sbare dal boss personalizade", "commands.bossbar.list.bars.some": "A son %s sbare(is) dal boss personalizade(is) ativis: %s", "commands.bossbar.remove.success": "La sbare dal boss %s e je stade gjavade", "commands.bossbar.set.color.success": "La sbare dal boss personalizade %s e à cambiât colôr", "commands.bossbar.set.color.unchanged": "Nissune modifiche. Chel al è za il colôr di cheste sbare dal boss", "commands.bossbar.set.max.success": "La sbare dal boss personalizade %s e à cambiât il so valôr massim in %s", "commands.bossbar.set.max.unchanged": "Nissune modifiche. Chel al è za il massim di cheste sbare dal boss", "commands.bossbar.set.name.success": "La sbare dal boss personalizade %s e à cambiât non", "commands.bossbar.set.name.unchanged": "Nissune modifiche. Chel al è za il non di cheste sbare dal boss", "commands.bossbar.set.players.success.none": "La sbare dal boss personalizade %s no à plui zuiadôrs associâts", "commands.bossbar.set.players.success.some": "La sbare dal boss personalizade %s e je cumò leade a %s zuiadôr(s): %s", "commands.bossbar.set.players.unchanged": "Nissune modifiche. <PERSON><PERSON> zu<PERSON>rs a son za associâts ae sbare dal boss e nol va zontât o gjavât nissun altri", "commands.bossbar.set.style.success": "La sbare dal boss personalizade %s e à cambiât stîl", "commands.bossbar.set.style.unchanged": "Nissune modifiche. Chel al è za il stîl di cheste sbare dal boss", "commands.bossbar.set.value.success": "La sbare dal boss personalizade %s e à cambiât il valôr in %s", "commands.bossbar.set.value.unchanged": "Nissune modifiche. Chel al è za il valôr di cheste sbare dal boss", "commands.bossbar.set.visibility.unchanged.hidden": "Nissune modifiche. La sbare dal boss e je za platade", "commands.bossbar.set.visibility.unchanged.visible": "Nissune modifiche. La sbare dal boss e je za in mostre", "commands.bossbar.set.visible.success.hidden": "La sbare dal boss personalizade %s e je cumò platade", "commands.bossbar.set.visible.success.visible": "La sbare dal boss personalizade %s e je cumò in mostre", "commands.bossbar.unknown": "No esist nissune sbare dal boss cul ID '%s'", "commands.clear.success.multiple": "A son stâts gjavâts %s ogjet(s) di %s zuiadôrs", "commands.clear.success.single": "A son stâts gjav<PERSON>ts %s ogjet(s) dal zuiadôr %s", "commands.clear.test.multiple": "A son stâts cjatâts %s ogjet(s) corispondents intor di %s zuiadôrs", "commands.clear.test.single": "A son stâts cjatâts %s ogjet(s) corispondents intor di %s", "commands.clone.failed": "Nissun bloc al è stât clonât", "commands.clone.overlap": "Nol è pussibil soreponi lis areis di sorzint e di destinazion", "commands.clone.success": "%s bloc(s) clonât(s) cun sucès", "commands.clone.toobig": "Masse blocs inte aree specificade (massim %s, specificâts %s)", "commands.damage.invulnerable": "Il bersai al è invulnerabil a chest gjenar di dam", "commands.damage.success": "Aplicât %s dam a %s", "commands.data.block.get": "%s dal bloc in %s, %s, %s, dopo il fatôr di scjale di %s, al è %s", "commands.data.block.invalid": "Il bloc di destinazion nol è une entitât-bloc", "commands.data.block.modified": "I dâts di bloc in %s, %s, %s a son stâts modificâts", "commands.data.block.query": "Il bloc in %s,%s,%s, al à chescj dâts di bloc: %s", "commands.data.entity.get": "%s su %s, dopo il fatôr di scjale di %s, al è %s", "commands.data.entity.invalid": "Impussibil modificâ i dâts dal zuiadôr", "commands.data.entity.modified": "I dâts di entitât di %s a son stâts modificâts", "commands.data.entity.query": "%s al/e à chescj dâts di entitât: %s", "commands.data.get.invalid": "Impussibil otignî %s; a son ametudis dome etichetis numerichis", "commands.data.get.multiple": "Chest argoment al acete un sôl valôr NBT", "commands.data.get.unknown": "Impussibil otignî %s; la etichete no esist", "commands.data.merge.failed": "Nissune modifiche. Lis proprietâts specificadis e àn za chescj valôrs", "commands.data.modify.expected_list": "E jere previodude une liste, si à vût: %s", "commands.data.modify.expected_object": "Al jere previodût un ogjet, si à vût: %s", "commands.data.modify.expected_value": "Valôr previodût, vût: %s", "commands.data.modify.invalid_index": "Tabele de liste no valide: %s", "commands.data.modify.invalid_substring": "Indicadôrs di sotstringhe no valits: di %s a %s", "commands.data.storage.get": "%s tal contignidôr %s cun fatôr di scjale %s, al è %s", "commands.data.storage.modified": "I dâts tal contignidôr %s a son stâts modificâts", "commands.data.storage.query": "Il contignidôr %s al à chescj contignûts: %s", "commands.datapack.create.already_exists": "Il pachet cul non '%s' al esist za", "commands.datapack.create.invalid_full_name": "Gnûf non dal pachet '%s' no valit", "commands.datapack.create.invalid_name": "Caratars no valits tal gnûf non dal pachet '%s'", "commands.datapack.create.io_failure": "Impussibil creâ il pachet cul non '%s', controle i regjstris", "commands.datapack.create.metadata_encode_failure": "Impussibil codificâ i metadâts pal pachet cul non '%s': %s", "commands.datapack.create.success": "Al è stât creât un gnûf pachet vueit cul non '%s'", "commands.datapack.disable.failed": "Il pachet '%s' nol è abilitât!", "commands.datapack.disable.failed.feature": "Nol è pussibil disabilitâ il pachet '%s', parcè che al fâs part di une variabile abilitade!", "commands.datapack.enable.failed": "Il pachet '%s' al è za abilitât!", "commands.datapack.enable.failed.no_flags": "Nol è pussibil abilitâ il pachet \"%s\", parcè che lis variabilis necessaris no son abilitadis in chest mont: %s!", "commands.datapack.list.available.none": "No son plui disponibii pachets di dâts", "commands.datapack.list.available.success": "A son %s pachet(s) di dâts disponibil(i): %s", "commands.datapack.list.enabled.none": "No son pachets di dâts abilitâts", "commands.datapack.list.enabled.success": "A son %s pachet(s) di dâts abilitât(s): %s", "commands.datapack.modify.disable": "Daûr a disabilitâ il pachet di dâts %s", "commands.datapack.modify.enable": "Daûr a abilitâ il pachet di dâts %s", "commands.datapack.unknown": "Pachet di dâts '%s' no cognossût", "commands.debug.alreadyRunning": "La profiladure des ticadis e je za stade inviade", "commands.debug.function.noRecursion": "Nol è pussibil segnâ di dentri di une funzion", "commands.debug.function.noReturnRun": "Nol è pussibil segnâ cun return run", "commands.debug.function.success.multiple": "Segnât(s) %s comant(s) di %s funzion(s) tal file di jessude %s", "commands.debug.function.success.single": "Segnât(s) %s comant(s) de funzion '%s' tal file di jessude %s", "commands.debug.function.traceFailed": "Impussibil sta daûr de funzion", "commands.debug.notRunning": "La profiladure des ticadis no je stade inviade", "commands.debug.started": "La profiladure des ticadis e je scomençade", "commands.debug.stopped": "La profiladure des ticadis e je stade fermade dopo %s secont(s) e %s ticade(is) (%s ticade(is) par secont)", "commands.defaultgamemode.success": "La modalitât di zûc predefinide e je stade metude a %s", "commands.deop.failed": "Nissune modifiche. Il zuiadôr nol è un operadôr", "commands.deop.success": "%s nol/no è/je plui un operadôr di servidôr", "commands.dialog.clear.multiple": "Il dialic par %s zuiadôrs al è stât scancelât", "commands.dialog.clear.single": "Il dialic par %s al è stât scancelât", "commands.dialog.show.multiple": "Il dialic al è stât mostrât a %s zuiadôrs", "commands.dialog.show.single": "Il dialic al è stât mostrât a %s", "commands.difficulty.failure": "Dificoltât invariade; e je za stabilide su %s", "commands.difficulty.query": "Cumò la dificoltât e je su %s", "commands.difficulty.success": "La dificoltât e je stade metude a %s", "commands.drop.no_held_items": "La entitât no pues tignî in man ogjets", "commands.drop.no_loot_table": "La entitât %s no à une tabele dai ogjets", "commands.drop.no_loot_table.block": "Il bloc %s nol à une tabele dai ogjets", "commands.drop.success.multiple": "Ogjets molâts: %s", "commands.drop.success.multiple_with_table": "Ogjets molâts de tabele %2$s: %1$s", "commands.drop.success.single": "Ogjets molâts: %s di %s", "commands.drop.success.single_with_table": "Ogjets molâts de tabele %3$s: %1$s di %2$s", "commands.effect.clear.everything.failed": "Il destinatari nol à efiets di gjavâ", "commands.effect.clear.everything.success.multiple": "<PERSON><PERSON><PERSON> i efiets a son stâts gjavâts di %s destinataris", "commands.effect.clear.everything.success.single": "Du<PERSON><PERSON> i efiets a son stâts gjavâts di %s", "commands.effect.clear.specific.failed": "Il destinatari nol à l'efiet domandât", "commands.effect.clear.specific.success.multiple": "L'efiet %s al è stât gjavât di %s destinataris", "commands.effect.clear.specific.success.single": "L'efiet %s al è stât gjavât di %s", "commands.effect.give.failed": "Impussibil aplicâ chest efiet (o la destinazion no ven tocjade dai efiets opûr e à alc di plui fuart)", "commands.effect.give.success.multiple": "L'efiet %s al è stât aplicât a %s destinataris", "commands.effect.give.success.single": "L'efiet %s al è stât aplicât a %s", "commands.enchant.failed": "Nissune modifiche. O i destinataris no àn ogjets tes lôr mans o nol è pussibil aplicâ l'incjantesim", "commands.enchant.failed.entity": "%s no je une entitât valide par chest comant", "commands.enchant.failed.incompatible": "L'ogjet %s nol supuarte chel incjantament", "commands.enchant.failed.itemless": "Nissun element tignût te man di %s", "commands.enchant.failed.level": "%s al è plui alt dal massim nivel di %s supuartât di chel incjantament", "commands.enchant.success.multiple": "L'incjantesim %s al è stât aplicât a %s entitâts", "commands.enchant.success.single": "L'incjantesim %s al è stât aplicât al ogjet di %s", "commands.execute.blocks.toobig": "Masse blocs te aree specificade (massim %s, specificâts %s)", "commands.execute.conditional.fail": "<PERSON>ve falide", "commands.execute.conditional.fail_count": "Prove falide, conte: %s", "commands.execute.conditional.pass": "Prove passade", "commands.execute.conditional.pass_count": "Prove passade, conte: %s", "commands.execute.function.instantiationFailure": "Erôr te creazion de funzion %s: %s", "commands.experience.add.levels.success.multiple": "A son stâts assegnâts %s nivei di esperience a %s zuiadôrs", "commands.experience.add.levels.success.single": "A son stâts assegnâts %s nivei di esperience a %s", "commands.experience.add.points.success.multiple": "A son stâts assegnâts %s ponts di esperience a %s zuiadôrs", "commands.experience.add.points.success.single": "A son stâts assegnâts %s ponts di esperience a %s", "commands.experience.query.levels": "Il zuiadôr %s al à %s nivei di esperience", "commands.experience.query.points": "Il zuiadôr %s al à %s ponts di esperience", "commands.experience.set.levels.success.multiple": "I nivei di esperience di %2$s zuiadôrs a son stâts metûts a %1$s", "commands.experience.set.levels.success.single": "I nivei di esperience di %2$s a son stâts metûts a %s", "commands.experience.set.points.invalid": "I ponts di esperience no puedin jessi plui alts dal massim ametût dal nivel corint dal zuiadôr", "commands.experience.set.points.success.multiple": "I ponts di esperience di %2$s zuiadôrs a son stâts metûts a %1$s", "commands.experience.set.points.success.single": "I ponts di esperience di %2$s a son stâts metûts a %1$s", "commands.fill.failed": "Nol è stât plaçât nissun bloc", "commands.fill.success": "%s bloc(s) plaçât(s) cun sucès", "commands.fill.toobig": "Masse blocs te aree specificade (massim %s, specificâts %s)", "commands.fillbiome.success": "Biomis impostâts tra %s, %s, %s e %s, %s, %s", "commands.fillbiome.success.count": "%s biomis impostâts tra %s, %s, %s e %s, %s, %s", "commands.fillbiome.toobig": "Masse blocs tal volum specificât (massim %s, specificât %s)", "commands.forceload.added.failure": "Nol è stât segnât nissun toc pal cjariament sfuarçât", "commands.forceload.added.multiple": "%1$s tocs di %3$s a %4$s te dimension %2$s a son stâts segnâts pal cjariament sfuar<PERSON><PERSON>t", "commands.forceload.added.none": "Nol è stât cjatât nissun toc cun cjariament sfuarçât te dimension %s", "commands.forceload.added.single": "Il toc %s te dimension %s al è stât segnât pal cjariament sfuarçât", "commands.forceload.list.multiple": "A son stâts cjatâts %s tocs cun cjariament sfuarçât te dimension %s: %s", "commands.forceload.list.single": "Al è stât cjatât un toc cun cjariament sfuarçât te dimension %s: %s", "commands.forceload.query.failure": "Il toc %s te dimension %s nol è segnât pal cjariament sfuarçât", "commands.forceload.query.success": "Il toc %s te dimension %s al è stât segnât pal cjariament sfuarçât", "commands.forceload.removed.all": "No son plui segnâts tocs te dimension %s pal cjariament sfuar<PERSON><PERSON>t", "commands.forceload.removed.failure": "Il cjariament sfuarçât nol è stât gjavât di nissun toc", "commands.forceload.removed.multiple": "%1$s tocs di %3$s a %4$s te dimension %2$s no son plui segnâts pal cjariament sfuar<PERSON><PERSON>t", "commands.forceload.removed.single": "Il toc %s te dimension %s nol è plui segnât pal cjariament sfuarçât", "commands.forceload.toobig": "Masse tocs te aree (massim: %s, specificâts: %s)", "commands.function.error.argument_not_compound": "Gjenar di argoment no valit: %s, al jere previodût Compound", "commands.function.error.missing_argument": "Al mancje l'argoment %2$s te funzion %1$s", "commands.function.error.missing_arguments": "A mancjin argoments te funzion %s", "commands.function.error.parse": "Te creazion de macro %s: il comant \"%s\" al à causât un erôr: %s", "commands.function.instantiationFailure": "Erôr te creazion de funzion %s: %s", "commands.function.result": "La funzion %s e à tornât %s", "commands.function.scheduled.multiple": "Esecuzion di funzions %s", "commands.function.scheduled.no_functions": "Impussibil cjatâ funzions clamadis %s", "commands.function.scheduled.single": "Esecuzion de funzion %s", "commands.function.success.multiple": "A son stâts eseguîts %s comant(s) di %s funzions", "commands.function.success.multiple.result": "Eseguidis %s funzions", "commands.function.success.single": "A son stâts eseguîts %s comant(s) de funzion '%s'", "commands.function.success.single.result": "La funzion \"%2$s\" e à tornât %1$s", "commands.gamemode.success.other": "La modalitât di zûc di %s e je stade cambiade in %s", "commands.gamemode.success.self": "La tô modalitât di zûc e je stade cambiade in %s", "commands.gamerule.query": "La regule di zûc %s in chest moment e je metude a %s", "commands.gamerule.set": "La regule %s e je stade cumò metude a %s", "commands.give.failed.toomanyitems": "No si pues dâ plui di %s di %s", "commands.give.success.multiple": "Donâts %2$s × %1$s a %3$s zuiadôrs", "commands.give.success.single": "Donâts %2$s × %1$s a %3$s", "commands.help.failed": "Comant no cognossût o permès insuficients", "commands.item.block.set.success": "Un spazi in %s, %s, %s al è stât sostituît cun %s", "commands.item.entity.set.success.multiple": "Un spazi di %s entitâts al è stât sostituît cun %s", "commands.item.entity.set.success.single": "Un spazi di %s al è stât sostituît cun %s", "commands.item.source.no_such_slot": "Il destinatari nol à il spazi %s", "commands.item.source.not_a_container": "La posizion di origjin %s, %s, %s no je un contignidôr", "commands.item.target.no_changed.known_item": "Nissun destinatari al à acetât l'ogjet %s tal spazi %s", "commands.item.target.no_changes": "Nissun destinatari al à acetât l'ogjet tal spazi %s", "commands.item.target.no_such_slot": "Il destinatari nol à il spazi %s", "commands.item.target.not_a_container": "La posizion di destinazion %s, %s, %s no je un contignidôr", "commands.jfr.dump.failed": "Impussibil salvâ la regjistrazion JFR: %s", "commands.jfr.start.failed": "Impussibil inviâ la profiladure JFR", "commands.jfr.started": "Profiladure JFR inviade", "commands.jfr.stopped": "Profiladure JFR fermade e salvade in %s", "commands.kick.owner.failed": "Impussibil parâ fûr il proprietari dal servidôr intune session in LAN", "commands.kick.singleplayer.failed": "Impussibil parâ fûr intun mont fûr rêt in zuiadôr singul", "commands.kick.success": "%s parât/ade fûr: %s", "commands.kill.success.multiple": "A son stadis copadis %s entitâts", "commands.kill.success.single": "La entitât %s e je stade copade", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Cumò a son colegâts %s zuiadôrs (suntun massim di %s): %s", "commands.locate.biome.not_found": "Impussibil cjatâ il biome di gjenar \"%s\" dentri di une distance resonevule", "commands.locate.biome.success": "Il biome %s plui dongje al è a %s (%s blocs di distance)", "commands.locate.poi.not_found": "Impussibil cjatâ un pont di interès di gjenar \"%s\" a une distance resonevule", "commands.locate.poi.success": "Il biome %s plui dongje al è a %s (%s blocs di distance)", "commands.locate.structure.invalid": "No esist nissune struture di gjenar \"%s\"", "commands.locate.structure.not_found": "Impussibil cjatâ une struture di gjenar \"%s\" chi dongje", "commands.locate.structure.success": "La struture di gjenar %s plui dongje e je in %s (%s tocs di distance)", "commands.message.display.incoming": "%s ti à cisicât: %s", "commands.message.display.outgoing": "Tu âs cisicât a %s: %s", "commands.op.failed": "Nissune modifiche. Il zuiadôr al è za un operadôr", "commands.op.success": "Cualifiche di operadôr/ore di servidôr dade a %s", "commands.pardon.failed": "Nissune modifiche. Il zuiadôr nol è bandît", "commands.pardon.success": "Si à tornât a acetâ %s", "commands.pardonip.failed": "Nissune modifiche. Chel IP nol è bandît", "commands.pardonip.invalid": "Direzion IP no valide", "commands.pardonip.success": "La direzion IP %s e je stade ametude di gnûf", "commands.particle.failed": "Nissun al podeve viodi la particele", "commands.particle.success": "Visualizazion de particele %s", "commands.perf.alreadyRunning": "La profiladure di prestazion e je za stade inviade", "commands.perf.notRunning": "La profiladure di prestazion no je stade inviade", "commands.perf.reportFailed": "Impussibil creâ il rapuart dal debug", "commands.perf.reportSaved": "Il rapuart dal debug al è stât creât in %s", "commands.perf.started": "Scomençade une profiladure di prestazions di 10 seconts (dopre \"/perf stop\" par fermâle prime)", "commands.perf.stopped": "Fermade la profiladure di prestazion dopo %s secont(s) e %s ticade(is) (%s ticade(is) par secont)", "commands.place.feature.failed": "Alc al è lât strucj tal placâ la funzionalitât", "commands.place.feature.invalid": "No esist nissune funzion di gjenar \"%s\"", "commands.place.feature.success": "Plaçât \"%s\" a %s, %s, %s", "commands.place.jigsaw.failed": "Impussibil gjenerâ il berdei", "commands.place.jigsaw.invalid": "Nol esist nissun grup di modei di gjenar \"%s\"", "commands.place.jigsaw.success": "Puzzle gjenerât a %s, %s, %s", "commands.place.structure.failed": "Alc al è lât strucj tal placâ la struture", "commands.place.structure.invalid": "No esist nissune struture di gjenar \"%s\"", "commands.place.structure.success": "Struture \"%s\" gjenerade a %s, %s, %s", "commands.place.template.failed": "Alc al è lât strucj tal placâ il model", "commands.place.template.invalid": "Nol esist nissun model cun id \"%s\"", "commands.place.template.success": "Model \"%s\" cjam<PERSON>t a %s, %s, %s", "commands.playsound.failed": "Il sun al è masse lontan par jessi scoltât", "commands.playsound.success.multiple": "Il sun %s al è stât riprodot a %s zuiadôrs", "commands.playsound.success.single": "Il sun %s al è stât riprodot a %s", "commands.publish.alreadyPublished": "La partide multi-zuiadôr e je za ospitade te puarte %s", "commands.publish.failed": "Impussibil ospitâ la partide locâl", "commands.publish.started": "La partide locâl e je ospitade su la puarte %s", "commands.publish.success": "La partide multi-zuiadôr e je cumò ospitade su la puarte %s", "commands.random.error.range_too_large": "L'interval dal valôr casuâl nol à di jessi plui grant di 2147483646", "commands.random.error.range_too_small": "L'interval dal valôr casuâl nol à di jessi plui piçul di 2", "commands.random.reset.all.success": "Azerade(is) %s secuence(is) casuâl(s)", "commands.random.reset.success": "Azerade la secuence casuâl %s", "commands.random.roll": "%s al/e à tirât fûr %s (tra %s e %s)", "commands.random.sample.success": "Valôr casuâl: %s", "commands.recipe.give.failed": "No je stade imparade nissune ricete", "commands.recipe.give.success.multiple": "Sblocade(is) %s ricete(is) par %s zuiadôrs", "commands.recipe.give.success.single": "Sblocade(is) %s ricete(is) par %s", "commands.recipe.take.failed": "Nol è pussibil dismenteâ lis ricetis", "commands.recipe.take.success.multiple": "Gjavade(is) %s ricete(is) di %s zuiadôrs", "commands.recipe.take.success.single": "Gjavad(is) %s ricete(is) di %s", "commands.reload.failure": "Impussibil tornâ a cjariâ, a vignaran tignûts i dâts vecjos", "commands.reload.success": "Daûr a tornâ a cjariâ!", "commands.ride.already_riding": "%s al/e sta za parsore di %s", "commands.ride.dismount.success": "%s al/e à fermât di cjavalgjâ %s", "commands.ride.mount.failure.cant_ride_players": "Nol è pussibil montâ parsore dai zuiadôrs", "commands.ride.mount.failure.generic": "%s nol/no pues montâ parsore di %s", "commands.ride.mount.failure.loop": "Une entitât no pues montâ sore di se stesse o parsore dai siei passizîrs", "commands.ride.mount.failure.wrong_dimension": "Nol è pussibil montâ parsore di une entitât intune dimension diferente", "commands.ride.mount.success": "%s al/e à scomençât a cjavalgjâ %s", "commands.ride.not_riding": "%s nol/no sta su nissun veicul", "commands.rotate.success": "Zirât %s", "commands.save.alreadyOff": "Il salvament al è za disativât", "commands.save.alreadyOn": "Il salvament al è za ativât", "commands.save.disabled": "Il salvament automatic cumò al è disativât", "commands.save.enabled": "Il salvament automatic cumò al è ativât", "commands.save.failed": "Impussibil salvâ la partide (isal vonde spazi su disc?)", "commands.save.saving": "Salvament de partide... (Al podarès tirâle a dilunc!)", "commands.save.success": "La partide e je stade salvade", "commands.schedule.cleared.failure": "Nissun program cul id %s", "commands.schedule.cleared.success": "Gjavât(s) %s program(s) cun id %s", "commands.schedule.created.function": "La funzion '%s' e vignarà eseguide chi di %s ticade(is) (timp di zûc: %s)", "commands.schedule.created.tag": "La etichete '%s' e vignarà eseguide chi di %s ticade(is) (timp di zûc: %s)", "commands.schedule.macro": "Impussibil programâ une macro", "commands.schedule.same_tick": "Impussibil programâ la esecuzion te ticade atuâl", "commands.scoreboard.objectives.add.duplicate": "Al esist za un obietîf cun chel non", "commands.scoreboard.objectives.add.success": "Al è stât creât l'obietîf %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Nissune modifiche. Chel spazi di visualizazion al è za vueit", "commands.scoreboard.objectives.display.alreadySet": "Nissune modifiche. Chel spazi di visualizazion al sta za mostrant chel obietîf", "commands.scoreboard.objectives.display.cleared": "Ducj i obietîfs a son stât gjav<PERSON>ts de posizion %s", "commands.scoreboard.objectives.display.set": "La visualizazion dal obietîf %2$s e je stade metude te posizion %1$s", "commands.scoreboard.objectives.list.empty": "No si à obietîfs", "commands.scoreboard.objectives.list.success": "A son %s obietîf(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Inzornament visuâl automatic disabilitât pal obietîf %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Inzornament visuâl automatic abilitât pal obietîf %s", "commands.scoreboard.objectives.modify.displayname": "Il non visualizât dal obietîf %s al è stât cambiât in %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Scancelât il formât numeric predefinît pal obietîf %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Cambiât il formât numeric predefinît pal obietîf %s", "commands.scoreboard.objectives.modify.rendertype": "Il gjenar di rapresentazion su schermi dal obietîf %s al è stât cambiât", "commands.scoreboard.objectives.remove.success": "L'obietîf %s al è stât gjavât", "commands.scoreboard.players.add.success.multiple": "A son stâts zontâts %s ponts al obietîf %s par %s entitâts", "commands.scoreboard.players.add.success.single": "A son stâts zontâts %s ponts al obietîf %s par %s (cumò %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Scancelât il non visualizât par %s entitâts in %s", "commands.scoreboard.players.display.name.clear.success.single": "Scancelât il non visualizât par %s in %s", "commands.scoreboard.players.display.name.set.success.multiple": "Cambiât il non visualizât in %s par %s entitâts in %s", "commands.scoreboard.players.display.name.set.success.single": "Cambiât il non visualizât in %s par %s in %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Scancelât il formât numeric par %s entitâts in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Scancelât il formât numeric par %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Cambiât il formât numeric par %s entitâts in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Cambiât il formât numeric par %s in %s", "commands.scoreboard.players.enable.failed": "Nissune modifiche. Chel ativadôr al è za abilitât", "commands.scoreboard.players.enable.invalid": "“Enable” al funzione dome su obietîfs di gjenar “trigger”", "commands.scoreboard.players.enable.success.multiple": "L'ativadôr %s al è stât abilitât par %s entitâts", "commands.scoreboard.players.enable.success.single": "L'ativadôr %s al è stât abilitât par %s", "commands.scoreboard.players.get.null": "Impussibil otignî il valôr di %s par %s; nol è stât stabilît", "commands.scoreboard.players.get.success": "I ponts dal obietîf %3$s par %1$s a son %2$s", "commands.scoreboard.players.list.empty": "No si àn entitâts regjistradis", "commands.scoreboard.players.list.entity.empty": "%s nol/no à ponts di mostrâ", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s al/e à %s pont(s):", "commands.scoreboard.players.list.success": "A son %s entitât(s) regjistrade(is): %s", "commands.scoreboard.players.operation.success.multiple": "A son stâts inzorn<PERSON>ts i ponts dal obietîf %s par %s entitâts", "commands.scoreboard.players.operation.success.single": "I ponts dal obietîf %s par %s a son stâts metûts a %s", "commands.scoreboard.players.remove.success.multiple": "A son stâts gjavâts %s ponts dal obietîf %s par %s entitâts", "commands.scoreboard.players.remove.success.single": "A son stâts gjavâts %s ponts dal obietîf %s par %s (cumò %s)", "commands.scoreboard.players.reset.all.multiple": "A son stâts azerâts ducj i risultâts par %s entitâts", "commands.scoreboard.players.reset.all.single": "A son stâts azerâ<PERSON> ducj i risultâts par %s", "commands.scoreboard.players.reset.specific.multiple": "I ponts dal obietîf %s par %s entitâts a son stâts azerâts", "commands.scoreboard.players.reset.specific.single": "I ponts dal obietîf %s par %s a son stâts a<PERSON>ts", "commands.scoreboard.players.set.success.multiple": "I ponts dal obietîf %s par %s entitâts a son stâts metûts a %s", "commands.scoreboard.players.set.success.single": "I ponts dal obietîf %s par %s a son stâts metûts a %s", "commands.seed.success": "Semence: %s", "commands.setblock.failed": "Impussibil meti il bloc", "commands.setblock.success": "Il bloc in %s, %s, %s al è stât cambiât", "commands.setidletimeout.success": "Il timp di inativitât massim pai zuiadôrs al è stât metût a %s minût(s)", "commands.setidletimeout.success.disabled": "Il timp di inativitât massim pai zuiadôrs al è stât disabilitât", "commands.setworldspawn.failure.not_overworld": "Al è pussibil meti il pont di rinassite dome te superficie", "commands.setworldspawn.success": "Il pont di gjenerazion/rinassite globâl al è stât metût a %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Il pont di rinassite di %6$s zuiadôrs al è stât metût in %1$s, %2$s, %3$s [%4$s] te dimension %5$s", "commands.spawnpoint.success.single": "Il pont di rinassite di %6$s al è stât metût in %1$s, %2$s, %3$s [%4$s] te dimension %5$s", "commands.spectate.not_spectator": "%s nol è in modalitât spetatôr", "commands.spectate.self": "No tu puedis osservâ di te stes(se)", "commands.spectate.success.started": "Si sta osservant de entitât %s", "commands.spectate.success.stopped": "No si sta plui osservant di une entitât", "commands.spreadplayers.failed.entities": "Impussibil sparniçâ %s entitât(s) tor ator di %s, %s (masse entitâts pal spazi - prove a doprâ une difusion di massim %s)", "commands.spreadplayers.failed.invalid.height": "maxHeight %s no valit; al jere previodût un valôr plui grant de altece minime dal mont %s", "commands.spreadplayers.failed.teams": "Impussibil sparniçâ %s scuadre(is) tor ator di %s, %s (masse entitâts pal spazi - prove a doprâ une difusion di massim %s)", "commands.spreadplayers.success.entities": "Sparniçât %s zuiadôr(s) tor ator di %s, %s cuntune separazion medie di %s blocs", "commands.spreadplayers.success.teams": "Sparniçât %s scuadre(is) tor ator di %s, %s cuntune separazion medie di %s toc(s)", "commands.stop.stopping": "Si sta par fermâ il servidôr", "commands.stopsound.success.source.any": "<PERSON><PERSON><PERSON> i suns cun origjin '%s' a son stâts interots", "commands.stopsound.success.source.sound": "Il sun '%s' cun origjin '%s' al è stât interot", "commands.stopsound.success.sourceless.any": "<PERSON><PERSON><PERSON> i suns a son stâts interots", "commands.stopsound.success.sourceless.sound": "Il sun '%s' al è stât interot", "commands.summon.failed": "Impussibil evocâ la entitât", "commands.summon.failed.uuid": "Impussibil evocâ la entitât par vie di une duplicazion dai UUIDs", "commands.summon.invalidPosition": "Posizion no valide pe evocazion", "commands.summon.success": "La entitât %s e je stade evocade", "commands.tag.add.failed": "O la destinazion e à za la etichete opûr a'nd à masse", "commands.tag.add.success.multiple": "Zontade etichete '%s' a %s entitâts", "commands.tag.add.success.single": "Zontade etichete '%s' a %s", "commands.tag.list.multiple.empty": "Lis %s entitâts no àn etichetis", "commands.tag.list.multiple.success": "Lis %s entitâts a àn in dut %s etichetis: %s", "commands.tag.list.single.empty": "nissune etichetis par %s", "commands.tag.list.single.success": "%s al/e à %s etichetis: %s", "commands.tag.remove.failed": "Il destinatari nol à cheste etichete", "commands.tag.remove.success.multiple": "Gjavade etichete '%s' di %s entitâts", "commands.tag.remove.success.single": "Gjavade etichete '%s' di %s", "commands.team.add.duplicate": "E esist za une scuadre cun chel non", "commands.team.add.success": "La scuadre %s e je stade creade", "commands.team.empty.success": "Gjavât %s membri(s) de scuadre %s", "commands.team.empty.unchanged": "Nissune modifiche. Ch<PERSON> scuadre e je za vueide", "commands.team.join.success.multiple": "A son stâts zontâ<PERSON> %s membris ae scuadre %s", "commands.team.join.success.single": "La scuadre %2$s si è ingrandide cun %1$s", "commands.team.leave.success.multiple": "%s membris a son stâts gjavâts des lôr scuadris", "commands.team.leave.success.single": "La scuadre e fâs di mancul di %s", "commands.team.list.members.empty": "La scuadre %s no à membris", "commands.team.list.members.success": "La scuadre %s e à %s membri(s): %s", "commands.team.list.teams.empty": "No je nissune scuadre", "commands.team.list.teams.success": "A son %s scuadre(is): %s", "commands.team.option.collisionRule.success": "La regule di colision pe scuadre %s e je stade metude a \"%s\"", "commands.team.option.collisionRule.unchanged": "Nissune modifiche. La regule di colision e je za di chel valôr", "commands.team.option.color.success": "Il colôr de scuadre %s al è stât cambiât a %s", "commands.team.option.color.unchanged": "Nissune modifiche. Ch<PERSON> scuadre e à za chel colôr", "commands.team.option.deathMessageVisibility.success": "La visibilitât dai messaçs di muart pe scuadre %s e je stade metude a \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "Nissune modifiche. La visibilitât dai messaçs di muart e je za di chel valôr", "commands.team.option.friendlyfire.alreadyDisabled": "Nissune modifiche. Il “fûc amì” al è za disabilitât par chê scuadre", "commands.team.option.friendlyfire.alreadyEnabled": "Nissune modifiche. Il “fûc amì” al è za abilitât par chê scuadre", "commands.team.option.friendlyfire.disabled": "Al è stât disativât il fûc amì pe scuadre %s", "commands.team.option.friendlyfire.enabled": "Al è stât ativât il fûc amì pe scuadre %s", "commands.team.option.name.success": "Al è stât inzornât il non de scuadre %s", "commands.team.option.name.unchanged": "Nissune modifiche. Chê scuadre e à za chel non", "commands.team.option.nametagVisibility.success": "La visibilitât dai nons dai zuiadôrs pe scuadre %s e je stade metude a \"%s\"", "commands.team.option.nametagVisibility.unchanged": "Nissune modifiche. La visibilitât de etichete dal non e je za di chel valôr", "commands.team.option.prefix.success": "Il prefìs de scuadre al è stât cambiât in %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Nissune modifiche. Chê scuadre za no pues viodi i compagns invisibii", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Nissune modifiche. Chê scuadre za no pues viodi i compagns invisibii", "commands.team.option.seeFriendlyInvisibles.disabled": "I membris de scuadre %s no puedin plui viodi i compagns invisibii", "commands.team.option.seeFriendlyInvisibles.enabled": "I membris de scuadre %s a puedin viodi i compagns invisibii", "commands.team.option.suffix.success": "Il sufìs de scuadre al è stât cambiât in %s", "commands.team.remove.success": "La scuadre %s e je stade gjavade", "commands.teammsg.failed.noteam": "Tu âs di stâ intune scuadre par messazâ cu la scuadre", "commands.teleport.invalidPosition": "Posizion no valide pal teletraspuart", "commands.teleport.success.entity.multiple": "Teletraspuartadis %s entitâts lì di %s", "commands.teleport.success.entity.single": "%s teletraspuartât/ade lì di %s", "commands.teleport.success.location.multiple": "Teletraspuartadis %s entitâts in %s, %s, %s", "commands.teleport.success.location.single": "%s teletraspuartât/ade in %s, %s, %s", "commands.test.batch.starting": "Inviament dal ambient %s lot %s", "commands.test.clear.error.no_tests": "Impussibil cjatâ une prove di scancelâ", "commands.test.clear.success": "Struturis canceladis: %s", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Fâs clic par copiâ intes notis", "commands.test.create.success": "Creade configurazion de prove %s", "commands.test.error.no_test_containing_pos": "Impussibil cjatâ une istance di prove che e conten %s, %s, %s", "commands.test.error.no_test_instances": "No son stadis cjatadis istancis di prove", "commands.test.error.non_existant_test": "Impussibil cjatâ la prove %s", "commands.test.error.structure_not_found": "Impussibil cjatâ la struture de prove %s", "commands.test.error.test_instance_not_found": "Impussibil cjatâ la entitât dal bloc de istance di prove", "commands.test.error.test_instance_not_found.position": "Impussibil cjatâ la entitât dal bloc de istance di prove pe prove su %s, %s, %s", "commands.test.error.too_large": "La dimension de struture e scugne jessi plui piçule di %s blocs su ogni as", "commands.test.locate.done": "Localizazion terminade, struturis cjatadis: %s", "commands.test.locate.found": "Struture cjatade a: %s (distance: %s)", "commands.test.locate.started": "E je stade inviade la localizazion des struturis di prove, al podarès tirâle a dilunc...", "commands.test.no_tests": "Nissune prove di eseguî", "commands.test.relative_position": "Posizion relative a %s: %s", "commands.test.reset.error.no_tests": "Impussibil cjatâ provis di ripristinâ", "commands.test.reset.success": "Ripristine %s struture(-is)", "commands.test.run.no_tests": "Nissune prove cjatade", "commands.test.run.running": "Esecuzion di %s prove(is)...", "commands.test.summary": "Prove dal zûc completade! Provis eseguidis: %s", "commands.test.summary.all_required_passed": "Dutis lis provis necessariis a son stadis superadis :)", "commands.test.summary.failed": "Provis necessariis falidis: %s :(", "commands.test.summary.optional_failed": "Provis facoltativis falidis: %s", "commands.tick.query.percentiles": "Percentîi: P50: %sms P95: %sms P99: %sms, campion: %s", "commands.tick.query.rate.running": "Obietîf di frecuence des ticadis: %s par secont.\nTimp medi par ticade: %sms (obietîf: %sms)", "commands.tick.query.rate.sprinting": "Obietîf di frecuence des ticadis: %s par secont (i<PERSON><PERSON><PERSON><PERSON>, dome par riferiment).\nTimp medi par ticade: %sms", "commands.tick.rate.success": "L'obietîf di frecuence des ticadis al è stât stabilît a %s par secont", "commands.tick.sprint.report": "Acelerazion completade cun %s ticadis par secont o %s ms par ticade", "commands.tick.sprint.stop.fail": "Nissune acelerazion des ticadis", "commands.tick.sprint.stop.success": "Acelerazion des ticadis interote", "commands.tick.status.frozen": "Il zûc al è blocât", "commands.tick.status.lagging": "Il zûc al è in esecuzion, ma nol rive a stâ daûr ae frecuence des ticadis indicade", "commands.tick.status.running": "Il zûc al funzione come simpri", "commands.tick.status.sprinting": "Il zûc al è acelerât", "commands.tick.step.fail": "Impussibil eseguî l'avanzament dal zûc - prime il zûc al à di jessi blocât", "commands.tick.step.stop.fail": "<PERSON>ssun avanzament di ticade in cors", "commands.tick.step.stop.success": "Avanzament di ticade interot", "commands.tick.step.success": "Avanzament %s ticade(is)", "commands.time.query": "Timp: %s ticadis", "commands.time.set": "Il timp al è stât metût a %s ticadis", "commands.title.cleared.multiple": "I titui par %s zuiadôrs a son stâts scancelâts", "commands.title.cleared.single": "I titui par %s a son stâts scancelâts", "commands.title.reset.multiple": "Lis opzions dai titui par %s zuiadôrs a son stadis ripristinadis", "commands.title.reset.single": "Lis opzions dai titui par %s a son stadis ripristinadis", "commands.title.show.actionbar.multiple": "Visualizazion di un gnûf titul in sbare di azion par %s zuiadôrs", "commands.title.show.actionbar.single": "Visualizazion di un gnûf titul in sbare di azion par %s", "commands.title.show.subtitle.multiple": "Visualizazion di un gnûf sottitul par %s zuiadôrs", "commands.title.show.subtitle.single": "Visualizazion di un gnûf sottitul par %s", "commands.title.show.title.multiple": "Visualizazion di un gnûf titul par %s zuiadôrs", "commands.title.show.title.single": "Visualizazion di un gnûf titul par %s", "commands.title.times.multiple": "I timps di visualizazion dai titui par %s zuiadôrs a son cambiâts", "commands.title.times.single": "I timps di visualizazion dai titui par %s a son cambiâts", "commands.transfer.error.no_players": "Bisugne specificâ almancul un zuiadôr di trasferî", "commands.transfer.success.multiple": "Trasferiment di %s zuiadôrs a %s:%s", "commands.transfer.success.single": "Trasferiment di %s a %s:%s", "commands.trigger.add.success": "%s al è stât ativât (aumentât di %s)", "commands.trigger.failed.invalid": "Tu puedis ativâ dome i obietîfs che a son di gjenar 'trigger'", "commands.trigger.failed.unprimed": "No tu puedis ancjemò ativâ chest obietîf", "commands.trigger.set.success": "%s al è stât ativât (valôr metût a %s)", "commands.trigger.simple.success": "%s al è stât ativât", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = sì", "commands.waypoint.list.empty": "Nissun pont di riferiment in %s", "commands.waypoint.list.success": "%s pont(s) di riferiment in %s: %s", "commands.waypoint.modify.color": "Il colôr dal pont di riferiment cumò al è %s", "commands.waypoint.modify.color.reset": "Ripristine il colôr dal pont di riferiment", "commands.waypoint.modify.style": "Al è stât modificât il stîl dal pont di riferiment", "commands.weather.set.clear": "Il timp at<PERSON><PERSON><PERSON> al stât metût a seren", "commands.weather.set.rain": "Il timp at<PERSON><PERSON><PERSON> al stât metût a ploie", "commands.weather.set.thunder": "Il timp at<PERSON><PERSON><PERSON> al stât metût a ploie e tons", "commands.whitelist.add.failed": "Il zuiadôr al è za acetât su chest servidôr", "commands.whitelist.add.success": "La liste di personis acetadis si è slungjade zontant %s", "commands.whitelist.alreadyOff": "La liste dai zuiadôrs acetâts e je za disativade", "commands.whitelist.alreadyOn": "La liste dai zuiadôrs acetâts e je za ativade", "commands.whitelist.disabled": "La liste di personis acetadis e je cumò disativade", "commands.whitelist.enabled": "La liste di personis acetadis e je cumò ative", "commands.whitelist.list": "La liste di personis acetadis e à %s zuiadôr(s): %s", "commands.whitelist.none": "No je nissune persone acetade in cheste liste", "commands.whitelist.reloaded": "Si à tornât a cjariâ la liste di personis acetadis", "commands.whitelist.remove.failed": "Il zuiadôr nol è acetât su chest servidôr", "commands.whitelist.remove.success": "La liste di personis acetadis si è scurtade gjavant %s", "commands.worldborder.center.failed": "Nissune modifiche. I confins dal mont a son za centrâts lì", "commands.worldborder.center.success": "Il centri dal mont al è stât metût a %s, %s", "commands.worldborder.damage.amount.failed": "Nissune modifiche. Il dam dai confins dal mont al è za di chê cuantitât", "commands.worldborder.damage.amount.success": "La cuantitât di dams fûr dal confin dal mont e je stade stabilide a %s par bloc ogni secont", "commands.worldborder.damage.buffer.failed": "Nissune modifiche. La zone sigure fûr dal confin dal mont e je za a chê distance", "commands.worldborder.damage.buffer.success": "La zone sigure fûr dal confin dal mont e je stade metude a %s bloc(s)", "commands.worldborder.get": "In chest moment il confin dal mont al è larc %s bloc(s)", "commands.worldborder.set.failed.big": "Il confin dal mont nol pues jessi plui grant di %s blocs in largjece", "commands.worldborder.set.failed.far": "Il confin dal mont nol pues jessi plui lontan di %s blocs", "commands.worldborder.set.failed.nochange": "Nissune modifiche. I confins dal mont a son za di chê dimension", "commands.worldborder.set.failed.small": "I confins dal mont no puedin jessi plui piçui di 1 bloc in largjece", "commands.worldborder.set.grow": "Il confin dal mont si sta slargjant par deventâ larc %s blocs in %s seconts", "commands.worldborder.set.immediate": "Il confin dal mont al è stât metût a %s bloc(s) di largjece", "commands.worldborder.set.shrink": "Il confin dal mont si sta strenzint par deventâ larc %s bloc(s) in %s secont(s)", "commands.worldborder.warning.distance.failed": "Nissune modifiche. L'avertiment pai confins dal mont al è za metût a chê distance", "commands.worldborder.warning.distance.success": "La distance par visâ che si supere il confin dal mont e je stade metude a %s bloc(s)", "commands.worldborder.warning.time.failed": "Nissune modifiche. L'avertiment dai confins dal mont al è za metût a chê cuantitât di timp", "commands.worldborder.warning.time.success": "Il timp par visâ che si supere il confin dal mont al è stât metût a %s secont(s)", "compliance.playtime.greaterThan24Hours": "Tu stâs zuiant di plui di 24 oris", "compliance.playtime.hours": "Tu stâs zuiant di %s ore(is)", "compliance.playtime.message": "Z<PERSON><PERSON> par masse timp al pues interferî cu la vite di ogni dì", "connect.aborted": "Operazion interote", "connect.authorizing": "Daûr a jentrâ...", "connect.connecting": "Conession al servidôr...", "connect.encrypting": "Cifradure...", "connect.failed": "Nol è stât pussibil conetisi al servidôr", "connect.failed.transfer": "Conession falide tal trasferiment al servidôr", "connect.joining": "Partecipazion al mont...", "connect.negotiating": "Negoziazion...", "connect.reconfiging": "Riconfigurazion in cors...", "connect.reconfiguring": "Riconfigurazion in cors...", "connect.transferring": "Trasferiment a un gnûf servidôr...", "container.barrel": "Caratel", "container.beacon": "<PERSON><PERSON><PERSON>", "container.beehive.bees": "Âfs: %s / %s", "container.beehive.honey": "Mîl: %s / %s", "container.blast_furnace": "For a fusion", "container.brewing": "<PERSON><PERSON>", "container.cartography_table": "Banc dal cartograf", "container.chest": "Baûl", "container.chestDouble": "Ba<PERSON><PERSON> grant", "container.crafter": "Fabricadôr", "container.crafting": "Fabricazion", "container.creative": "Selezion elements", "container.dispenser": "Distributôr", "container.dropper": "Tiradôr", "container.enchant": "Incjante", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s lapislazuli", "container.enchant.lapis.one": "1 lapislazuli", "container.enchant.level.many": "%s nivei di incjantament", "container.enchant.level.one": "1 nivel di incjantament", "container.enchant.level.requirement": "<PERSON>vel domand<PERSON>t: %s", "container.enderchest": "Baûl dal End", "container.furnace": "<PERSON><PERSON><PERSON><PERSON>", "container.grindstone_title": "Comede e disincjante", "container.hopper": "<PERSON><PERSON><PERSON><PERSON>", "container.inventory": "Inventari", "container.isLocked": "%s al è blocât!", "container.lectern": "<PERSON><PERSON>", "container.loom": "Telâr", "container.repair": "Comede e da il non", "container.repair.cost": "Cost di incjantament: %1$s", "container.repair.expensive": "Masse cjâr!", "container.shulkerBox": "Scjatule di shulker", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "e %s in plui...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "Impussibil vierzi. Il botin nol è stât ancje<PERSON> g<PERSON>t.", "container.stonecutter": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "container.upgrade": "<PERSON><PERSON> un ogjet", "container.upgrade.error_tooltip": "Nol è pussibil miorâ l'ogjet in cheste maniere", "container.upgrade.missing_template_tooltip": "Zonte un model par fari", "controls.keybinds": "Associazion tascj...", "controls.keybinds.duplicateKeybinds": "Chest tast al è ancje doprât par:\n%s", "controls.keybinds.title": "Associazion tascj", "controls.reset": "Ripristine", "controls.resetAll": "Ripristine i tascj", "controls.title": "<PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Selezione un biome", "createWorld.customize.buffet.title": "Personalizazion dal biome singul", "createWorld.customize.flat.height": "Altece", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Limit inferiôr - %s", "createWorld.customize.flat.layer.top": "Limit superiôr - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON><PERSON> s<PERSON>", "createWorld.customize.flat.tile": "<PERSON><PERSON><PERSON><PERSON> dal strât", "createWorld.customize.flat.title": "Personalizazion dal mont-plac", "createWorld.customize.presets": "<PERSON><PERSON>", "createWorld.customize.presets.list": "In alternative, chi a 'nd è cualchidun fat di resint!", "createWorld.customize.presets.select": "Dopre il model", "createWorld.customize.presets.share": "<PERSON><PERSON><PERSON><PERSON> condividi il to model cun cualchidun? Dopre il ricuadri chi sot!", "createWorld.customize.presets.title": "Selezione un model", "createWorld.preparing": "Daûr a prontâ la creazion dal mont...", "createWorld.tab.game.title": "Zûc", "createWorld.tab.more.title": "P<PERSON><PERSON>", "createWorld.tab.world.title": "Mont", "credits_and_attribution.button.attribution": "Atribuzions", "credits_and_attribution.button.credits": "Ricognossiments", "credits_and_attribution.button.licenses": "Licencis", "credits_and_attribution.screen.title": "Ricognossiments e atribuzions", "dataPack.bundle.description": "Abilite l'ogjet sperimentâl \"sacut\"", "dataPack.bundle.name": "Sacuts", "dataPack.locator_bar.description": "Mostre la direzion dai altris zuiadôrs in multi-zuiadôr", "dataPack.locator_bar.name": "Sbare di localizazion", "dataPack.minecart_improvements.description": "Moviment dai carei mineraris mi<PERSON>t", "dataPack.minecart_improvements.name": "Mioraments dai carei mineraris", "dataPack.redstone_experiments.description": "Modifichis sperimentâls de redstone", "dataPack.redstone_experiments.name": "Esperiments de redstone", "dataPack.title": "Selezione i pachets di dâts", "dataPack.trade_rebalance.description": "Scambis cui paisans inzornâts", "dataPack.trade_rebalance.name": "Belançament dai scambis cui paisans", "dataPack.update_1_20.description": "Gnûfs contignûts e funzionalitâts par Minecraft 1.20", "dataPack.update_1_20.name": "Inzornament 1.20", "dataPack.update_1_21.description": "Gnûfs contignûts e funzionalitâts par Minecraft 1.21", "dataPack.update_1_21.name": "Inzornament 1.21", "dataPack.validation.back": "<PERSON><PERSON> in<PERSON>", "dataPack.validation.failed": "Validazion pachets dâts falide!", "dataPack.validation.reset": "Ristabilìs ai predefinîts", "dataPack.validation.working": "Validazion dai pachets di dâts selezionâts...", "dataPack.vanilla.description": "I dâts predefinîts par Minecraft", "dataPack.vanilla.name": "Predefinît", "dataPack.winter_drop.description": "Gnûfs contignûts e funzionalitâts pe espansion invernâl", "dataPack.winter_drop.name": "Espansion invernâl", "datapackFailure.safeMode": "Modalit<PERSON>t sigure", "datapackFailure.safeMode.failed.description": "Chest mont al conten dâts corots o no valits.", "datapackFailure.safeMode.failed.title": "Nol è stât pussibil cjariâ il mont in modalitât sigure.", "datapackFailure.title": "I erôrs tai pachets di dâts cumò selezionâts a àn impedît il cjariament dal mont.\nO tu ciris di cjariâlu dome cui pachets di dâts origjinâi (\"Modalitât sigure\") o tu tornis indaûr al menù principâl e tu ju comedis a man.", "death.attack.anvil": "Un incuin al à scliçât %1$s", "death.attack.anvil.player": "Un incuin al à scliçât %1$s dilunc il combatiment cun %2$s", "death.attack.arrow": "%2$s al/e à trat a %1$s", "death.attack.arrow.item": "%2$s al/e à trat a %1$s doprant %3$s", "death.attack.badRespawnPoint.link": "Progjetazion intenzionâl dal zûc", "death.attack.badRespawnPoint.message": "%2$s al/e à copât %1$s", "death.attack.cactus": "Une muart di spiçots e becadis par %1$s", "death.attack.cactus.player": "%1$s al/e à cjaminât intor di un cactus intant che al/e cirive di scjampâ di %2$s", "death.attack.cramming": "Masse pression par %1$s", "death.attack.cramming.player": "Masse pression par %1$s par colpe di %2$s", "death.attack.dragonBreath": "Il flât di drâc al à rustît %1$s", "death.attack.dragonBreath.player": "Il flât di drâc al à rustît %1$s par colpe di %2$s", "death.attack.drown": "%1$s inneât/ade", "death.attack.drown.player": "%1$s inneât/ade cirint di scjampâ di %2$s", "death.attack.dryout": "La disidratazion e à copât %1$s", "death.attack.dryout.player": "La disidratazion e à copât %1$s intant che al/e cirive di scjampâ di %2$s", "death.attack.even_more_magic": "Ancjemò altre magjie e à copât %1$s", "death.attack.explosion": "%1$s tonât/ade par aiar", "death.attack.explosion.player": "%2$s al/e à fat tonâ par aiar %1$s", "death.attack.explosion.player.item": "%2$s al/e à fat tonâ par aiar %1$s doprant %3$s", "death.attack.fall": "%1$s si è sfracaiât/ade par tiere", "death.attack.fall.player": "Cirint di scjampâ di %2$s, %1$s si è sfracaiât/ade par tiere", "death.attack.fallingBlock": "Un bloc al à scliçât %1$s", "death.attack.fallingBlock.player": "Un bloc al à scliçât %1$s dilunc il combatiment cun %2$s", "death.attack.fallingStalactite": "Une stalatite e à impirât %1$s", "death.attack.fallingStalactite.player": "Une stalatite e à impirât %1$s par colpe di %2$s", "death.attack.fireball": "%2$s al/e traè une bale di fûc a %1$s", "death.attack.fireball.item": "%2$s al/e à trat une bale di fûc a %1$s doprant %3$s", "death.attack.fireworks": "%1$s nus lasse cul bot", "death.attack.fireworks.item": "%1$s nus lasse cul bot par vie di une fusete tirade di %2$s cun %3$s", "death.attack.fireworks.player": "%1$s nus lasse cul bot dulinvie il combatiment cun %2$s", "death.attack.flyIntoWall": "%1$s al/e à provât la energjie cinetiche", "death.attack.flyIntoWall.player": "%1$s al/e à provât la energjie cinetiche, cirint di scjampâ di %2$s", "death.attack.freeze": "A %1$s i tocjà di murî inglaçât/ade", "death.attack.freeze.player": "A %1$s i tocjà di murî inglaçât/ade par colpe di %2$s", "death.attack.generic": "E vie %1$s... muart/e", "death.attack.generic.player": "%1$s muart/e par colpe di %2$s", "death.attack.genericKill": "A %1$s i tocjà di murî", "death.attack.genericKill.player": "A %1$s i tocjà di murî dilunc dal combatiment cun %2$s", "death.attack.hotFloor": "%1$s al/e à discuviert che il paviment al jere di lave", "death.attack.hotFloor.player": "%1$s al/e à cjaminât intune zone pericolose par vie di %2$s", "death.attack.inFire": "%1$s al/e à cjapât fûc", "death.attack.inFire.player": "%1$s al/e à cjaminât tal fûc intant dal combatiment cun %2$s", "death.attack.inWall": "%1$s nus lasse “cence flât” (intun mûr)", "death.attack.inWall.player": "%1$s si scjafoià intun mûr dilunc il combatiment cun %2$s", "death.attack.indirectMagic": "%2$s al/e à copât %1$s doprant la magjie", "death.attack.indirectMagic.item": "%2$s al/e à copât %1$s doprant %3$s", "death.attack.lava": "%1$s al/e à cirût di nadâ te lave", "death.attack.lava.player": "%1$s al/e à cirût di nadâ te lave par scjampâ di %2$s", "death.attack.lightningBolt": "Une saete e petà %1$s", "death.attack.lightningBolt.player": "Une saete e petà %1$s dilunc il combatiment cun %2$s", "death.attack.mace_smash": "%2$s al/e à fruçât %1$s", "death.attack.mace_smash.item": "%2$s al/e à fruçât %1$s cun %3$s", "death.attack.magic": "La magjie e à copât %1$s", "death.attack.magic.player": "La magjie e à copât %1$s intant che al/e cirive di scjampâ di %2$s", "death.attack.message_too_long": "Nus displâs, ma in realtât il messaç al jere masse lunc par podêlu spedî complet. Chi e je la version scurtade: %s", "death.attack.mob": "%2$s al/e à copât %1$s", "death.attack.mob.item": "%2$s al/e à copât %1$s doprant %3$s", "death.attack.onFire": "A %1$s i à tocjât di brusâ vîf/vive", "death.attack.onFire.item": "%1$s al/e à brusât fin a deventâ crostulin dilunc il combatiment cun %2$s impugnant %3$s", "death.attack.onFire.player": "%1$s al/e à brusât fin a deventâ crostulin dilunc il combatiment cun %2$s", "death.attack.outOfWorld": "%1$s colât/ade fûr dal mont", "death.attack.outOfWorld.player": "%1$s nol/no volè vivi tal stes mont di %2$s", "death.attack.outsideBorder": "%1$s al/e à passât i confins dal mont", "death.attack.outsideBorder.player": "%1$s al/e à passât i confins dal mont dilunc dal combatiment cun %2$s", "death.attack.player": "%2$s al/e à copât %1$s", "death.attack.player.item": "%2$s al/e à copât %1$s doprant %3$s", "death.attack.sonic_boom": "Une zigade cjarade in mût sonic e à nichilît %1$s", "death.attack.sonic_boom.item": "Une zigade cjariade in mût sonic e à nichilît %1$s intant che al/e scjampave di %2$s impugnant %3$s", "death.attack.sonic_boom.player": "Une zigade cjariade in mût sonic e à nichilît %1$s intant che al/e scjampave di %2$s", "death.attack.stalagmite": "Une stalagmite e à impalât %1$s", "death.attack.stalagmite.player": "Une stalagmite è à impalât %1$s dilunc il combatiment cun %2$s", "death.attack.starve": "%1$s muart/e di fam", "death.attack.starve.player": "%1$s muart/e di fam dilunc il combatiment cun %2$s", "death.attack.sting": "%1$s al/e à cjapât becadis fin ae sô muart", "death.attack.sting.item": "%2$s al/e à becât a muart %1$s doprant %3$s", "death.attack.sting.player": "%2$s al/e à becât a muart %1$s", "death.attack.sweetBerryBush": "Un baraç di pomulis dolcis al à becât %1$s fin ae sô muart", "death.attack.sweetBerryBush.player": "Un baraç di pomulis dolcis al à becât a muart %1$s che al/e cirive di scjampâ di %2$s", "death.attack.thorns": "%1$s copât/ade intant che al/e cirive di ferî %2$s", "death.attack.thorns.item": "%3$s al/e à copât %1$s intant che al/e cirive di ferî %2$s", "death.attack.thrown": "%2$s al/e à dât pachis a %1$s", "death.attack.thrown.item": "%2$s al/e à dât pachis a %1$s doprant %3$s", "death.attack.trident": "%2$s al/e à impalât %1$s", "death.attack.trident.item": "%2$s al/e à impalât %1$s cun %3$s", "death.attack.wither": "%1$s si è inflapît", "death.attack.wither.player": "%1$s si è inflapît intant dal combatiment cun %2$s", "death.attack.witherSkull": "Al è partît di %2$s un crani che al à colpît %1$s", "death.attack.witherSkull.item": "%2$s cun %3$s al à trat un crani che al à colpît %1$s", "death.fell.accident.generic": "%1$s colât/ade jù di adalt", "death.fell.accident.ladder": "A %1$s i à capitât di colâ di une scjale", "death.fell.accident.other_climbable": "%1$s colât/ade intant che si rimpinave", "death.fell.accident.scaffolding": "%1$s colât/ade di une impalcadure", "death.fell.accident.twisting_vines": "%1$s colât/colade des plantis rimpighinis intorteadis", "death.fell.accident.vines": "%1$s colât/ade des plantis rimpighinis", "death.fell.accident.weeping_vines": "%1$s colât/ade des plantis rimpighinis vaiulintis", "death.fell.assist": "%1$s colât/ade jù par colpe di %2$s", "death.fell.assist.item": "%1$s colât/ade jù par colpe di %2$s doprant %3$s", "death.fell.finish": "%2$s al/e à finît %1$s che al/e jere colât/ade masse lontan", "death.fell.finish.item": "%2$s, doprant %3$s, al/e à finît %1$s che al/e jere colât/ade masse lontan", "death.fell.killer": "Al jere destin par %1$s chel di colâ jù", "deathScreen.quit.confirm": "<PERSON><PERSON><PERSON> par<PERSON>?", "deathScreen.respawn": "Torne nas", "deathScreen.score": "Ponts", "deathScreen.score.value": "Ponts: %s", "deathScreen.spectate": "<PERSON><PERSON> spetatô<PERSON>", "deathScreen.title": "Tu sês muart/e!", "deathScreen.title.hardcore": "Partide finide!", "deathScreen.titleScreen": "<PERSON><PERSON>", "debug.advanced_tooltips.help": "F3 + H = sugjeriments avanzâts", "debug.advanced_tooltips.off": "Sugjeriments avanzâts: disativâts", "debug.advanced_tooltips.on": "Sugjeriments avanzâts: ativâts", "debug.chunk_boundaries.help": "F3 + G = mostre i ôrs dai tocs", "debug.chunk_boundaries.off": "Ôrs dai tocs: platâts", "debug.chunk_boundaries.on": "Ôrs dai tocs: visibii", "debug.clear_chat.help": "F3 + D = nete la chat", "debug.copy_location.help": "F3 + C = copie la posizion come comant /tp, tegniju fracâts par fâ colassâ il zûc", "debug.copy_location.message": "Posizion copiade intes notis", "debug.crash.message": "Tu stâs tignint fracât F3 + C, se no tu ju molis, il zuc al colassarà.", "debug.crash.warning": "Il zûc al colassarà chi di %s...", "debug.creative_spectator.error": "No tu âs il permès par cambiâ la modalitât di zûc", "debug.creative_spectator.help": "F3 + N = passe tra la modalitât di zûc precedente e la modalitât spetatôr", "debug.dump_dynamic_textures": "<PERSON><PERSON><PERSON> din<PERSON> salvadis in %s", "debug.dump_dynamic_textures.help": "F3 + S = espuarte tramis dinamichis", "debug.gamemodes.error": "No tu âs i permès par cambiâ la modalitât di zûc", "debug.gamemodes.help": "F3 + F4: vierç il menù par cambiâ la modalitât di zûc", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s <PERSON><PERSON><PERSON>f", "debug.help.help": "F3 + Q = mostre cheste liste", "debug.help.message": "Cumbinazions di tascj:", "debug.inspect.client.block": "Dâts di bloc (de bande dal client) copiâts intes notis", "debug.inspect.client.entity": "Dâts de entitât (de bande dal client) copiâts intes notis", "debug.inspect.help": "F3 + I = copie intes notis i dâts di bloc o di entitât", "debug.inspect.server.block": "Dâts di bloc (de bande dal servidôr) copiâts intes notis", "debug.inspect.server.entity": "Dâts de entitât (de bande dal servidôr) copiâts intes notis", "debug.pause.help": "F3 + Esc = sospint cence vierzi il menù di pause (dome se si pues sospindi)", "debug.pause_focus.help": "F3 + P = met in pause cuant che il barcon al piert il stât atîf", "debug.pause_focus.off": "<PERSON><PERSON> in pause se al mancje il stât atîf: disativât", "debug.pause_focus.on": "<PERSON><PERSON> in pause se al mancje il stât atîf: ativ<PERSON>t", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Invie o ferme la profiladure", "debug.profiling.start": "Profiladure di %s seconts inviade. Frache F3 + L par fermâle prime", "debug.profiling.stop": "Profiladure finide. Risultâts salvâts in %s", "debug.reload_chunks.help": "F3 + A = torne cjame i tocs", "debug.reload_chunks.message": "Daûr a tornâ a cjariâ ducj i tocs", "debug.reload_resourcepacks.help": "F3 + T = torne cjame i pachets des risorsis", "debug.reload_resourcepacks.message": "Pachets di risorsis tornâts a cjariâ", "debug.show_hitboxes.help": "F3 + B = mostre la incuadradure visive", "debug.show_hitboxes.off": "Incuadraduris visivis: platadis", "debug.show_hitboxes.on": "Incuadraduris visivis: ativis", "debug.version.header": "Informazions su la version dal client:", "debug.version.help": "F3 + V = Informazions su la version dal client", "demo.day.1": "Cheste dimostrazion e durarà cinc dîs. F<PERSON>s miôr che tu puedis!", "demo.day.2": "2ᵈᵉ zornade", "demo.day.3": "3<PERSON><PERSON> zornade", "demo.day.4": "4ᵗᵉ zornade", "demo.day.5": "Cheste e je la tô ultime zornade!", "demo.day.6": "Tu âs passât il cuint dì. Dopre %s par salvâ une videade de tô creazion.", "demo.day.warning": "Il timp a disposizion al è cuasi esaurît!", "demo.demoExpired": "La dimostrazion e je finide!", "demo.help.buy": "<PERSON><PERSON>re daurman!", "demo.help.fullWrapped": "Cheste dimostrazion e durarà 5 dîs (dîs dal zûc - plui o mancul 1 ore e 40 reâi). Controle i progrès pai sugjeriments! Gjolt!", "demo.help.inventory": "Dopre il tast %1$s par vierzi l'inventari", "demo.help.jump": "Salte fracant il tast %1$s", "demo.help.later": "Continue a zuiâ!", "demo.help.movement": "Dopre i tascj %1$s, %2$s, %3$s, %4$s e il mouse par spostâti ator", "demo.help.movementMouse": "<PERSON><PERSON><PERSON> ator doprant il mouse", "demo.help.movementShort": "Spostiti fracant %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Modalitât dimostrative di Minecraft", "demo.remainingTime": "Timp restant: %s", "demo.reminder": "La dimostrazion e je scjadude. Compre il zûc par continuâ o scomence un gnûf mont!", "difficulty.lock.question": "Blocâ pardabon la dificoltât di chest mont? Chest mont al sarà metût par simpri a %1$s e no tu lu podarâs cambiâ plui.", "difficulty.lock.title": "<PERSON><PERSON> la dificolt<PERSON>t dal mont", "disconnect.endOfStream": "Fin dal flus dâts", "disconnect.exceeded_packet_rate": "Parâts fûr par vê superât il limit pai pachets inviâts", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Domand<PERSON> di stât ignorade", "disconnect.loginFailedInfo": "Nol è stât pussibil jentrâ: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "La modalitât multi-zuiadôr e je disativade. Controle lis impostazions dal to account Microsoft.", "disconnect.loginFailedInfo.invalidSession": "Session no valide (prove a tornâ a inviâ il zûc e l'inviadôr)", "disconnect.loginFailedInfo.serversUnavailable": "Nol è pussibil rivâ a colegâsi ai servidôrs di autenticazion. Torne prove plui indenant.", "disconnect.loginFailedInfo.userBanned": "Tu sês bandît/ide dal zûc in linie", "disconnect.lost": "Conession pierdude", "disconnect.packetError": "<PERSON><PERSON><PERSON><PERSON> dal <PERSON> di rêt", "disconnect.spam": "Par<PERSON>ts fûr par spam", "disconnect.timeout": "Conession scjadude", "disconnect.transfer": "<PERSON><PERSON><PERSON><PERSON><PERSON> suntun al<PERSON> servid<PERSON>r", "disconnect.unknownHost": "Host no cognossût", "download.pack.failed": "Impussibil discjariâ %s di %s pachets", "download.pack.progress.bytes": "Progrès: %s (dimension totâl no cognossude)", "download.pack.progress.percent": "Progrès: %s%%", "download.pack.title": "Daûr a discjariâ il pachet des risorsis %s/%s", "editGamerule.default": "Predefinît: %s", "editGamerule.title": "Modifiche regulis dal zûc", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Assorbiment", "effect.minecraft.bad_omen": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Potence dal condot", "effect.minecraft.darkness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "<PERSON> dal dolfin", "effect.minecraft.fire_resistance": "Resistence al fûc", "effect.minecraft.glowing": "Luminessence", "effect.minecraft.haste": "<PERSON><PERSON><PERSON>", "effect.minecraft.health_boost": "Salût ampliade", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON> dal paîs", "effect.minecraft.hunger": "Fam", "effect.minecraft.infested": "Infestazion", "effect.minecraft.instant_damage": "Dam imediât", "effect.minecraft.instant_health": "Salût imediade", "effect.minecraft.invisibility": "Invisibilitât", "effect.minecraft.jump_boost": "Miorament salts", "effect.minecraft.levitation": "Levitazion", "effect.minecraft.luck": "Fortune", "effect.minecraft.mining_fatigue": "Fadie tal sgjav<PERSON>", "effect.minecraft.nausea": "<PERSON><PERSON><PERSON>", "effect.minecraft.night_vision": "Vision noturne", "effect.minecraft.oozing": "Secrezion", "effect.minecraft.poison": "<PERSON><PERSON><PERSON>", "effect.minecraft.raid_omen": "Malauguri de incursion", "effect.minecraft.regeneration": "Rigjenerazion", "effect.minecraft.resistance": "Resistence", "effect.minecraft.saturation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.slow_falling": "Colâ planc", "effect.minecraft.slowness": "Lentece", "effect.minecraft.speed": "Velocitâ<PERSON>", "effect.minecraft.strength": "<PERSON><PERSON><PERSON>", "effect.minecraft.trial_omen": "Malauguri de prove", "effect.minecraft.unluck": "Sfortune", "effect.minecraft.water_breathing": "<PERSON><PERSON><PERSON><PERSON> te aghe", "effect.minecraft.weakness": "Debilece", "effect.minecraft.weaving": "Tiessidure", "effect.minecraft.wind_charged": "<PERSON><PERSON><PERSON>", "effect.minecraft.wither": "Inflapiment", "effect.none": "<PERSON><PERSON><PERSON> efiet", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Afinitât ae aghe", "enchantment.minecraft.bane_of_arthropods": "Disfate dai artropots", "enchantment.minecraft.binding_curse": "Maledizion dal leam", "enchantment.minecraft.blast_protection": "Protezion des esplosions", "enchantment.minecraft.breach": "Infrazion", "enchantment.minecraft.channeling": "Canalizazion", "enchantment.minecraft.density": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "Pas dai abìs", "enchantment.minecraft.efficiency": "Eficience", "enchantment.minecraft.feather_falling": "Poiâsi sul fof", "enchantment.minecraft.fire_aspect": "Aspiet di fûc", "enchantment.minecraft.fire_protection": "Protezion dal fûc", "enchantment.minecraft.flame": "Flame", "enchantment.minecraft.fortune": "Fortune", "enchantment.minecraft.frost_walker": "<PERSON><PERSON>", "enchantment.minecraft.impaling": "Impalament", "enchantment.minecraft.infinity": "Infinitât", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "Sac", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "Fortune dal mâr", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "Riparazion", "enchantment.minecraft.multishot": "<PERSON><PERSON><PERSON> multipli", "enchantment.minecraft.piercing": "Traforazion", "enchantment.minecraft.power": "Potence", "enchantment.minecraft.projectile_protection": "Protezion dai proietii", "enchantment.minecraft.protection": "Protezion", "enchantment.minecraft.punch": "<PERSON><PERSON>", "enchantment.minecraft.quick_charge": "<PERSON><PERSON><PERSON> rapide", "enchantment.minecraft.respiration": "Respirazion", "enchantment.minecraft.riptide": "Rivoc", "enchantment.minecraft.sharpness": "U<PERSON>de", "enchantment.minecraft.silk_touch": "<PERSON> di vilût", "enchantment.minecraft.smite": "Anateme", "enchantment.minecraft.soul_speed": "Sveltece des animis", "enchantment.minecraft.sweeping": "<PERSON><PERSON> scoreant", "enchantment.minecraft.sweeping_edge": "<PERSON><PERSON> scoreant", "enchantment.minecraft.swift_sneak": "Furtivitât svelte", "enchantment.minecraft.thorns": "<PERSON><PERSON>", "enchantment.minecraft.unbreaking": "Indistrutibilitât", "enchantment.minecraft.vanishing_curse": "Maledizion de disparizion", "enchantment.minecraft.wind_burst": "Colp di aiar", "entity.minecraft.acacia_boat": "Barcje di agacie", "entity.minecraft.acacia_chest_boat": "Barcje di agacie cun baûl", "entity.minecraft.allay": "Slizeridôr", "entity.minecraft.area_effect_cloud": "Nûl di efiet persistent", "entity.minecraft.armadillo": "Armadil", "entity.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.arrow": "<PERSON><PERSON><PERSON>", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Çate di bambù cun baûl", "entity.minecraft.bamboo_raft": "Çate di bambù", "entity.minecraft.bat": "Gnotul", "entity.minecraft.bee": "<PERSON><PERSON>", "entity.minecraft.birch_boat": "<PERSON><PERSON><PERSON> di <PERSON>oi", "entity.minecraft.birch_chest_boat": "<PERSON><PERSON><PERSON> di bedoi cun baûl", "entity.minecraft.blaze": "Flamade", "entity.minecraft.block_display": "Visôr di blocs", "entity.minecraft.boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.bogged": "<PERSON><PERSON><PERSON>", "entity.minecraft.breeze": "Brise", "entity.minecraft.breeze_wind_charge": "<PERSON><PERSON><PERSON>", "entity.minecraft.camel": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON><PERSON>", "entity.minecraft.cave_spider": "Ragn dai landris", "entity.minecraft.cherry_boat": "<PERSON><PERSON><PERSON> di cjariesâr", "entity.minecraft.cherry_chest_boat": "<PERSON><PERSON><PERSON> di <PERSON>r cun baûl", "entity.minecraft.chest_boat": "<PERSON><PERSON><PERSON> cun ba<PERSON>l", "entity.minecraft.chest_minecart": "Carel minerari cun baûl", "entity.minecraft.chicken": "<PERSON><PERSON>", "entity.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Carel minerari cun bloc comants", "entity.minecraft.cow": "<PERSON><PERSON><PERSON>", "entity.minecraft.creaking": "<PERSON><PERSON>", "entity.minecraft.creaking_transient": "<PERSON><PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON> di rôl scûr", "entity.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON> di rôl scûr cun baûl", "entity.minecraft.dolphin": "<PERSON><PERSON><PERSON>", "entity.minecraft.donkey": "<PERSON>s", "entity.minecraft.dragon_fireball": "Bale di fûc di drâc", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.egg": "Ûf tirât", "entity.minecraft.elder_guardian": "Vuardian veteran", "entity.minecraft.end_crystal": "<PERSON><PERSON><PERSON> dal <PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON> dal <PERSON> tirade", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Evocadôr", "entity.minecraft.evoker_fangs": "<PERSON><PERSON><PERSON> dal evocad<PERSON>r", "entity.minecraft.experience_bottle": "Pozion di esperience tirade", "entity.minecraft.experience_orb": "Sfere di esperience", "entity.minecraft.eye_of_ender": "<PERSON><PERSON>", "entity.minecraft.falling_block": "Bloc che al cole", "entity.minecraft.falling_block_type": "Colâ di %s", "entity.minecraft.fireball": "Bale di fûc", "entity.minecraft.firework_rocket": "Fusete", "entity.minecraft.fishing_bobber": "Flotant di pescje", "entity.minecraft.fox": "<PERSON><PERSON><PERSON>", "entity.minecraft.frog": "<PERSON><PERSON>", "entity.minecraft.furnace_minecart": "Carel minerari cun fornâs", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Zigant", "entity.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON> lumi<PERSON>", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON><PERSON> luminessent", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Carel minerari cun tramuele", "entity.minecraft.horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.husk": "Zombi sec", "entity.minecraft.illusioner": "Ilusionist", "entity.minecraft.interaction": "Interazion", "entity.minecraft.iron_golem": "Golem di <PERSON>er", "entity.minecraft.item": "Ogjet", "entity.minecraft.item_display": "Visôr di og<PERSON>s", "entity.minecraft.item_frame": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.jungle_boat": "Bar<PERSON>je di len di jungle", "entity.minecraft.jungle_chest_boat": "Bar<PERSON><PERSON> di len di jungle cun baûl", "entity.minecraft.killer_bunny": "Il cunin sassin", "entity.minecraft.leash_knot": "<PERSON><PERSON> dal s<PERSON>l", "entity.minecraft.lightning_bolt": "Saete", "entity.minecraft.lingering_potion": "Pozion persistente", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "Spudacj di lama", "entity.minecraft.magma_cube": "Cubi di magme", "entity.minecraft.mangrove_boat": "Barcje di mangrovie", "entity.minecraft.mangrove_chest_boat": "Barcje di mangrovie cun baûl", "entity.minecraft.marker": "Segnepuest", "entity.minecraft.minecart": "<PERSON><PERSON> mine<PERSON>i", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "Mûl", "entity.minecraft.oak_boat": "<PERSON><PERSON>je di rôl", "entity.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON> di rôl cun baûl", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Gjeneradôr di ogjets dal ma<PERSON>guri", "entity.minecraft.painting": "Cuadri", "entity.minecraft.pale_oak_boat": "<PERSON><PERSON><PERSON> di rôl palit", "entity.minecraft.pale_oak_chest_boat": "<PERSON><PERSON><PERSON> di rôl palit cun baûl", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Pampagal", "entity.minecraft.phantom": "Fantasime", "entity.minecraft.pig": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON> brut<PERSON>l", "entity.minecraft.pillager": "Sachi<PERSON><PERSON><PERSON>", "entity.minecraft.player": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "<PERSON><PERSON> pol<PERSON>", "entity.minecraft.potion": "Pozion", "entity.minecraft.pufferfish": "Pes-bale", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "Salmon", "entity.minecraft.sheep": "<PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Palotule di shulker", "entity.minecraft.silverfish": "P<PERSON>ut di a<PERSON>t", "entity.minecraft.skeleton": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON> scheletric", "entity.minecraft.slime": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.small_fireball": "Piçule bale di fûc", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Golem di nêf", "entity.minecraft.snowball": "<PERSON><PERSON> di <PERSON>ê<PERSON>", "entity.minecraft.spawner_minecart": "Carel minerari cun gjeneradôr di mostris", "entity.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON>", "entity.minecraft.spider": "<PERSON><PERSON>", "entity.minecraft.splash_potion": "Pozion di tîr", "entity.minecraft.spruce_boat": "Barcje di peç", "entity.minecraft.spruce_chest_boat": "Bar<PERSON>je di peç cun baûl", "entity.minecraft.squid": "Calam<PERSON>r", "entity.minecraft.stray": "Scheletri vagabont", "entity.minecraft.strider": "Strider", "entity.minecraft.tadpole": "Cudul", "entity.minecraft.text_display": "Vis<PERSON>r di test", "entity.minecraft.tnt": "TNT lescjât", "entity.minecraft.tnt_minecart": "Carel minerari cun TNT", "entity.minecraft.trader_llama": "<PERSON> <PERSON>", "entity.minecraft.trident": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish": "Pes t<PERSON>", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "<PERSON><PERSON> chirurc dal rostri", "entity.minecraft.tropical_fish.predefined.10": "Idul moresc", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON> pavee or<PERSON>t", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON> agnul regjine", "entity.minecraft.tropical_fish.predefined.14": "Zebre rosse", "entity.minecraft.tropical_fish.predefined.15": "Bavose dai lavris ros", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON><PERSON> ros", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "Pes paiaç pomodoro", "entity.minecraft.tropical_fish.predefined.19": "<PERSON><PERSON> balestre", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON> chirurc blu", "entity.minecraft.tropical_fish.predefined.20": "Pes pampagal de code zale", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON> chir<PERSON>l", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON> pavee", "entity.minecraft.tropical_fish.predefined.4": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "Pes combatent", "entity.minecraft.tropical_fish.predefined.7": "Pseudochiromidae", "entity.minecraft.tropical_fish.predefined.8": "<PERSON><PERSON> imperi<PERSON> ros", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Pes bloc", "entity.minecraft.tropical_fish.type.brinely": "Pes di mâr alt", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON> a<PERSON>", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON> lusint", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.spotty": "Notolab<PERSON> celidotus", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Pes rai di soreli", "entity.minecraft.turtle": "Copasse", "entity.minecraft.vex": "Vex", "entity.minecraft.villager": "Paisan", "entity.minecraft.villager.armorer": "Armarûl di coracis", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "Cleric", "entity.minecraft.villager.farmer": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fisherman": "Pescjadôr", "entity.minecraft.villager.fletcher": "Freçâr", "entity.minecraft.villager.leatherworker": "Cuincepiels", "entity.minecraft.villager.librarian": "Bibliotecari", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON>", "entity.minecraft.villager.none": "Paisan", "entity.minecraft.villager.shepherd": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON> di imprescj", "entity.minecraft.villager.weaponsmith": "Armarûl", "entity.minecraft.vindicator": "Svindicad<PERSON><PERSON>", "entity.minecraft.wandering_trader": "Vendidôr ambulant", "entity.minecraft.warden": "<PERSON><PERSON><PERSON>", "entity.minecraft.wind_charge": "<PERSON><PERSON><PERSON>", "entity.minecraft.witch": "Strie", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Schelet<PERSON> di wither", "entity.minecraft.wither_skull": "<PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON><PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "Paisan zombi", "entity.minecraft.zombified_piglin": "<PERSON><PERSON>", "entity.not_summonable": "Impussibil evocâ la entitât di gjenar %s", "event.minecraft.raid": "Incursion", "event.minecraft.raid.defeat": "Sconfite", "event.minecraft.raid.defeat.full": "Incursion - Sconfite", "event.minecraft.raid.raiders_remaining": "Scorsadôrs restants: %s", "event.minecraft.raid.victory": "Vitorie", "event.minecraft.raid.victory.full": "Incursion - Vitorie", "filled_map.buried_treasure": "Mape de furtune soterade", "filled_map.explorer_jungle": "Mape dal esploradôr di junglis", "filled_map.explorer_swamp": "Mape dal esploradôr di palûts", "filled_map.id": "ID #%s", "filled_map.level": "(Nivel %s/%s)", "filled_map.locked": "Blocade", "filled_map.mansion": "Mape dal esploradôr di boscs", "filled_map.monument": "Mape dal esploradôr di mârs", "filled_map.scale": "Scjale 1:%s", "filled_map.trial_chambers": "Mape dal esploradôr des provis", "filled_map.unknown": "Mape no cognossude", "filled_map.village_desert": "Mape dal paisut dal desert", "filled_map.village_plains": "Mape dal paisut de planure", "filled_map.village_savanna": "Mape dal paisut de savane", "filled_map.village_snowy": "Mape dal paisut cuviert di nêf", "filled_map.village_taiga": "Mape dal paisut de taiga", "flat_world_preset.minecraft.bottomless_pit": "Fuesse cence fonts", "flat_world_preset.minecraft.classic_flat": "Plac come simpri", "flat_world_preset.minecraft.desert": "Desert", "flat_world_preset.minecraft.overworld": "Superficie", "flat_world_preset.minecraft.redstone_ready": "Mont pront pai argagns", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON> di <PERSON>", "flat_world_preset.minecraft.the_void": "Il vueit", "flat_world_preset.minecraft.tunnelers_dream": "Sium dai minadôrs", "flat_world_preset.minecraft.water_world": "Mont di aghe", "flat_world_preset.unknown": "???", "gameMode.adventure": "Modalitât aventure", "gameMode.changed": "La modalitât di zûc e je stade cambiade in %s", "gameMode.creative": "Modal<PERSON><PERSON>t creative", "gameMode.hardcore": "Modalitât impegnative", "gameMode.spectator": "Modalit<PERSON><PERSON> s<PERSON>", "gameMode.survival": "Modalit<PERSON><PERSON>", "gamerule.allowFireTicksAwayFromPlayer": "Permet propagazion dal fûc lontan dai zuiadôrs", "gamerule.allowFireTicksAwayFromPlayer.description": "Al controle se il fûc e la lave a puedin rivâ a propagâsi a plui di 8 tocs di distance dai zuiadôrs", "gamerule.announceAdvancements": "Comuniche i progrès", "gamerule.blockExplosionDropDecay": "Cuant che tu fasis tonâ alc, cualchi bloc nol molarà il so ogjet", "gamerule.blockExplosionDropDecay.description": "Se une interazion cui blocs e causione une esplosion che e distrûç blocs, cualchi ogjet molât al vignarà pierdût te esplosion.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Og<PERSON><PERSON>", "gamerule.category.misc": "<PERSON><PERSON><PERSON>", "gamerule.category.mobs": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.player": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Gjenerazion", "gamerule.category.updates": "Inzornaments dal mont", "gamerule.commandBlockOutput": "Comuniche il risultât dai blocs comants", "gamerule.commandModificationBlockLimit": "Limit dai blocs modificabii cuntun comant", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON> massim di blocs che si puedin modificâ cuntun sôl comant, come /fill o /clone.", "gamerule.disableElytraMovementCheck": "Disative il control dal moviment cu lis elitris", "gamerule.disablePlayerMovementCheck": "Disative il control dal moviment dal zuiadôr", "gamerule.disableRaids": "Disative lis incursions", "gamerule.doDaylightCycle": "<PERSON><PERSON>li dì-gnot", "gamerule.doEntityDrops": "<PERSON>âs col<PERSON> i ogjets des entitâts", "gamerule.doEntityDrops.description": "Al controle i ogjets molâts dai carei mineraris (includûts i inventaris), des curnîs, des barcjis e vie indenant.", "gamerule.doFireTick": "Inzorne il fûc", "gamerule.doImmediateRespawn": "<PERSON>ne a nassi daurman", "gamerule.doInsomnia": "<PERSON><PERSON><PERSON><PERSON> lis fantasimis", "gamerule.doLimitedCrafting": "Pretint lis ricetis pe fabricazion", "gamerule.doLimitedCrafting.description": "Se ativât, i zuiadôrs a puedin fabricâ i ogjets dome doprant lis ricetis sblocadis.", "gamerule.doMobLoot": "<PERSON>âs col<PERSON> i ogjets des creaturis", "gamerule.doMobLoot.description": "Al controle lis risorsis che a vegnin moladis des creaturis, includudis lis sferis di esperience.", "gamerule.doMobSpawning": "<PERSON><PERSON><PERSON><PERSON> lis creaturis", "gamerule.doMobSpawning.description": "Cualchi entitât e podarès vê regulis separadis.", "gamerule.doPatrolSpawning": "Gjenere lis patuliis di sachizadôrs", "gamerule.doTileDrops": "Fâs col<PERSON> i blocs", "gamerule.doTileDrops.description": "Al controle lis risorsis che a vegnin moladis dai blocs, includudis lis sferis di esperience.", "gamerule.doTraderSpawning": "Gjenere i vendidôrs ambulants", "gamerule.doVinesSpread": "Difusion des plantis rampighinis", "gamerule.doVinesSpread.description": "Controle se lis plantis rampighinis si puedin slargjâ in mût casuâl ai blocs vicins. Nol à nissun efiet su altris rampighins come i rampighins vaiulints, i rampighins intorteâs e vie indenant.", "gamerule.doWardenSpawning": "<PERSON><PERSON><PERSON><PERSON> lis vuardiis", "gamerule.doWeatherCycle": "Inzorne il timp meteorologjic", "gamerule.drowningDamage": "Causione i dams se inneât", "gamerule.enderPearlsVanishOnDeath": "Lis perlis dal End tiradis a sparissin dopo de muart", "gamerule.enderPearlsVanishOnDeath.description": "Controle se lis perlis dal End tiradis di un zuiadôr a sparissin cuant che chel zuiadôr al mûr.", "gamerule.entitiesWithPassengersCanUsePortals": "Lis entitâts cun passizîrs a puedin doprâ i portâi", "gamerule.entitiesWithPassengersCanUsePortals.description": "Permet aes entitâts cun passizîrs di teletraspuartâsi tai portâi dal <PERSON>her, tai portâi dal End e tai passaçs pal End.", "gamerule.fallDamage": "Causione i dams pe colade", "gamerule.fireDamage": "Causione i dams pe scotadure", "gamerule.forgiveDeadPlayers": "Perdone i zuiadôrs muarts", "gamerule.forgiveDeadPlayers.description": "Lis creaturis neutr<PERSON>, che a son stadis provocadis, a smetin di jessi inrabiadis cuant che il zuiadôr che lis à provocadis al mûr tai contors.", "gamerule.freezeDamage": "Causione i dams di conzelament", "gamerule.globalSoundEvents": "Events sonôrs glo<PERSON>âi", "gamerule.globalSoundEvents.description": "Cuant che a sucedin cierts events di zûc, come la gjenerazion di un boss, il sun si sint dapardut.", "gamerule.keepInventory": "No sta pierdi l'inventari dopo de muart", "gamerule.lavaSourceConversion": "Trasformazion di lave in sorzint", "gamerule.lavaSourceConversion.description": "Cuant che la lave corinte e je circondade di sorzints di lave su dôs bandis, si trasforme in sorzint.", "gamerule.locatorBar": "Abilite la sbare di localizazion dai zuiadôrs", "gamerule.locatorBar.description": "Se e ven abilitade, e ven mostrade une sbare sul schermi par indicâ la direzion dai zuiadôrs.", "gamerule.logAdminCommands": "Comuniche a ducj i comants dai aministradôrs", "gamerule.maxCommandChainLength": "Limit des cjadenis di comants", "gamerule.maxCommandChainLength.description": "Si apliche a blocs comants a cjadene e funzions.", "gamerule.maxCommandForkCount": "Limit dal contest dal comant", "gamerule.maxCommandForkCount.description": "<PERSON><PERSON><PERSON> massim di contescj che a puedin jessi doprâts dai comants come 'execute as'.", "gamerule.maxEntityCramming": "Limit di ingrumament des entitâts", "gamerule.minecartMaxSpeed": "Velocitât massime dal carel minerari", "gamerule.minecartMaxSpeed.description": "Velocitât massime predefinide di un carel minerari in moviment sul teren.", "gamerule.mobExplosionDropDecay": "Cuant che une creature e tone, cualchi bloc nol molarà il so contignût", "gamerule.mobExplosionDropDecay.description": "Cuant che une creature e fâs tonâ blocs, cualchi ogjet molât al larà pierdût te esplosion.", "gamerule.mobGriefing": "Permet lis azions distrutivis des creaturis", "gamerule.naturalRegeneration": "Rigjenere la salût", "gamerule.playersNetherPortalCreativeDelay": "Ritart dai portai pal Nether pai zuiadôrs in modalitât creative", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON><PERSON> (in ticadis) che un zuiadôr in modalitât creative al a di restâ intun portâl pal Nether par cambiâ dimension.", "gamerule.playersNetherPortalDefaultDelay": "Ritart dai portâi pal Nether pai zuiadôrs che no son in modalitât creative", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON><PERSON> (in ticadis) che un zuiadôr, che nol è in modalitât creative, al à di restâ intun portâl pal Nether par cambiâ dimension.", "gamerule.playersSleepingPercentage": "Per<PERSON><PERSON><PERSON><PERSON> di zuiadôrs indurmidîts", "gamerule.playersSleepingPercentage.description": "La percentuâl di zuiadôrs che a àn di sei indurmidîts par saltâ la gnot.", "gamerule.projectilesCanBreakBlocks": "I proietii a puedin spacâ i blocs", "gamerule.projectilesCanBreakBlocks.description": "Al controle se i proietii cun impat a puedin spacâ i blocs che si puedin rompi.", "gamerule.randomTickSpeed": "Frecuence des ticadis (tick) casuâls", "gamerule.reducedDebugInfo": "Diminuìs lis informazions di debug", "gamerule.reducedDebugInfo.description": "Limite il contignût de videade di debug.", "gamerule.sendCommandFeedback": "Invie lis rispuestis dai comants", "gamerule.showDeathMessages": "Mostre i messaçs di muart", "gamerule.snowAccumulationHeight": "Altece dal grum di nêf", "gamerule.snowAccumulationHeight.description": "Cuant che al nevee, la nêf si ingrume fin a chest numar di strâts.", "gamerule.spawnChunkRadius": "Rai dai tocs di gjenerazion", "gamerule.spawnChunkRadius.description": "<PERSON><PERSON><PERSON> di to<PERSON> che a restin cjariâts atôr dal pont di gjenerazion te superficie.", "gamerule.spawnRadius": "Rai dal pont di rinassite", "gamerule.spawnRadius.description": "Controle la dimension de aree atôr dal pont di gjenerazion dulà che il zuiadôr al pues gjenerâsi.", "gamerule.spectatorsGenerateChunks": "Permet ai spetatôrs di gjenerâ teren", "gamerule.tntExplodes": "Permet di lescjâ e di fâ tonâ i blocs di TNT", "gamerule.tntExplosionDropDecay": "Intes esplosions cul TNT, cualchi bloc nol molarà il so contignût", "gamerule.tntExplosionDropDecay.description": "Cuant che il TNT al tone e al distrûç blocs, cualchi ogjet che al varès di vignî molât al larà pierdût te esplosion.", "gamerule.universalAnger": "<PERSON><PERSON>", "gamerule.universalAnger.description": "Lis creaturis neutr<PERSON>ls, che a son stadis provocadis, a atachin ogni zuiadôr tai contors, no dome chel che lis à fatis inrabiâ. Al funzione miôr se la regule \"forgiveDeadPlayers\" e je disativade.", "gamerule.waterSourceConversion": "Trasformazion di aghe in sorzint", "gamerule.waterSourceConversion.description": "Cuant che la aghe corinte e je circondade di sorzints di aghe su dôs bandis, si trasforme in sorzint.", "generator.custom": "<PERSON><PERSON><PERSON><PERSON>", "generator.customized": "Personaliz<PERSON><PERSON> (vecjo)", "generator.minecraft.amplified": "AMPLIFICÂT", "generator.minecraft.amplified.info": "Fâs câs: juste par ridi! Al à bisugne di un computer torel.", "generator.minecraft.debug_all_block_states": "Modalitât Debug", "generator.minecraft.flat": "Plac", "generator.minecraft.large_biomes": "Biomis grancj", "generator.minecraft.normal": "Predefinît", "generator.minecraft.single_biome_surface": "<PERSON><PERSON><PERSON> singul", "generator.single_biome_caves": "<PERSON><PERSON>", "generator.single_biome_floating_islands": "<PERSON><PERSON><PERSON> pal aiar", "gui.abuseReport.attestation": "Inviant cheste segnalazion, tu confermis che lis informazions indicadis a son precisis e completis.", "gui.abuseReport.comments": "Coments", "gui.abuseReport.describe": "Condividi detais nus jude a cjapâ une decision ben informade.", "gui.abuseReport.discard.content": "Se tu vâs fûr, tu pierdarâs la segnalazion e i coments.\nDesideristu pardabon lâ fûr?", "gui.abuseReport.discard.discard": "Lasse e scarte segnalazion", "gui.abuseReport.discard.draft": "Salve come stampon", "gui.abuseReport.discard.return": "Continue modifiche", "gui.abuseReport.discard.title": "Scartâ segnalazion e coments?", "gui.abuseReport.draft.content": "Vuelistu continuâ a modificâ la segnalazion atuâl o scartâle e creâ une gnove?", "gui.abuseReport.draft.discard": "<PERSON><PERSON>", "gui.abuseReport.draft.edit": "Continue modifiche", "gui.abuseReport.draft.quittotitle.content": "Vuelistu continuâ a modificâle o scartâle?", "gui.abuseReport.draft.quittotitle.title": "Tu âs un stampon di segnalazion che al vignarà pierdût se tu jessis dal zûc", "gui.abuseReport.draft.title": "Modificâ il stampon de segnalazion de chat?", "gui.abuseReport.error.title": "Probleme tal inviâ la segnalazion", "gui.abuseReport.message": "Dulà âstu cjatât un compuartament scoret?\nChest nus judarà te analisi dal tô câs.", "gui.abuseReport.more_comments": "Par plasê descrîf ce che al è sucedût:", "gui.abuseReport.name.comment_box_label": "Par plasê descrîf parcè che tu vûs segnalâ chest non:", "gui.abuseReport.name.reporting": "Tu stâs segnalant \"%s\".", "gui.abuseReport.name.title": "Segnale non inadat dal zuiadôr", "gui.abuseReport.observed_what": "<PERSON><PERSON><PERSON> stâstu segnalant cheste robe chi?", "gui.abuseReport.read_info": "Impare di plui su lis segnalazions", "gui.abuseReport.reason.alcohol_tobacco_drugs": "<PERSON><PERSON><PERSON> o alcolics", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Cualchidun al sburte chei altris a partecipâ a cualchi ativitât ilegâl leade ae droghe o al alcolisim minorîl.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Sfrutament o abûs sessuâl su minôrs", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Cualchidun al fevele o al promôf compuartaments indecents cjapant dentri fruts.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Difamazion, identitât robade o informazion false", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Cualchidun al fâs dam ae reputazion di cualchidun altri, al fâs fente di jessi cualchidun altri o al condivît informazions falsis par abusâ o ingjanâ chei altris.", "gui.abuseReport.reason.description": "Descrizion:", "gui.abuseReport.reason.false_reporting": "False segnalazion", "gui.abuseReport.reason.generic": "O vuei segnalâ chest zuiadôr", "gui.abuseReport.reason.generic.description": "Chest zuiadôr mi da fastidi o al à fat alc che no mi plâs.", "gui.abuseReport.reason.harassment_or_bullying": "<PERSON><PERSON><PERSON><PERSON> o bulos", "gui.abuseReport.reason.harassment_or_bullying.description": "Cualchidun al umilie, al tache o al fâs il bulo cuintri di te o di cualchidun altri. Chest al inclût cuant che cualchidun al prove cun insistence a contatâ te o cualchidun altri cence permès, o al spartìs informazions privadis sul to cont o su chel di cualchidun altri cence permès (\"doxing\").", "gui.abuseReport.reason.hate_speech": "Incitament al odi", "gui.abuseReport.reason.hate_speech.description": "<PERSON>ual<PERSON>dun al tache te o un altri zuiadôr pes carateristichis de lôr identitât, come religjon, raze o sessualitât.", "gui.abuseReport.reason.imminent_harm": "Menace di fâ dal mâl a chei altris te vite reâl", "gui.abuseReport.reason.imminent_harm.description": "Cualchidun al menace di fâ dal mâl a te o a cualchidun altri te vite reâl.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Imagjins intimis cence consens", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> al fevele, al condivît o al promôf imagjinis intimis o privadis.", "gui.abuseReport.reason.self_harm_or_suicide": "Pericul iminent - autolesionisim o suicidi", "gui.abuseReport.reason.self_harm_or_suicide.description": "Cualchidun al menace o al fevele di fâsi dal mâl te vite reâl.", "gui.abuseReport.reason.sexually_inappropriate": "Contignûts sessuâi inadats", "gui.abuseReport.reason.sexually_inappropriate.description": "Aspiets di nature grafiche in relazion a ats sessuâi, orghins sessuâi o violence sessuâl.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terorisim o estremisim violent", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> al fevele, prom<PERSON><PERSON> o menace di fâ ats di terorisim o estremisim violent par resons politichis, relig<PERSON><PERSON>, ideologjichis o altris motîfs.", "gui.abuseReport.reason.title": "Selezione une categorie di segnalazion", "gui.abuseReport.report_sent_msg": "O vin ricevût la tô segnalazion cun sucès, gracie!\nLa nestre scuadre le visionarà il prime pussibil.", "gui.abuseReport.select_reason": "Sielç une categorie di segnalazion", "gui.abuseReport.send": "Invie segnalazion", "gui.abuseReport.send.comment_too_long": "Par plasê scurte il coment", "gui.abuseReport.send.error_message": "Al è vignût fûr un erôr tal inviâ la segnalazion: '%s'", "gui.abuseReport.send.generic_error": "Alc al è lât strucj tal inviâ la segnalazion.", "gui.abuseReport.send.http_error": "Al è vignût fûr un erôr HTTP no previodût tal inviâ la segnalazion.", "gui.abuseReport.send.json_error": "Cja<PERSON>ât un pachet malformât tal inviâ la segnalazion.", "gui.abuseReport.send.no_reason": "Par plasê sielç une categorie di segnalazion", "gui.abuseReport.send.not_attested": "Par plasê lei il test parsore e selezione la casele par podê inviâ la segnalazion", "gui.abuseReport.send.service_unavailable": "Impussibil contatâ il servizi di segnalazion dai abûs. Controle la tô conession a internet e torne prove.", "gui.abuseReport.sending.title": "Daûr a inviâ la segnalazion...", "gui.abuseReport.sent.title": "Segnalazion inviade", "gui.abuseReport.skin.title": "<PERSON><PERSON><PERSON> as<PERSON>t dal zu<PERSON>", "gui.abuseReport.title": "<PERSON><PERSON><PERSON>", "gui.abuseReport.type.chat": "Messaçs de chat", "gui.abuseReport.type.name": "Non dal zuiadôr", "gui.abuseReport.type.skin": "<PERSON><PERSON><PERSON> dal <PERSON>", "gui.acknowledge": "O soi cussient", "gui.advancements": "Progrès", "gui.all": "<PERSON><PERSON>", "gui.back": "Indaûr", "gui.banned.description": "%s\n\n%s\n\nImpare di plui a chest colegament: %s", "gui.banned.description.permanent": "Il to account al è bandît in mût permanent, chest al vûl dî che no tu puedis zuiâ in linie o jentrâ tai Realms.", "gui.banned.description.reason": "O vin ricevût une segnalazion par compuartament scoret dal to account. I nestris moderadôrs a àn revisionât il to câs e lu àn identificât come %s, chest al va cuintri dai standards de comunitât di Minecraft.", "gui.banned.description.reason_id": "Codiç: %s", "gui.banned.description.reason_id_message": "Codiç: %s - %s", "gui.banned.description.temporary": "%s Par intant, no tu podarâs zuiâ in linie o jentrâ tai Realms.", "gui.banned.description.temporary.duration": "Il to account al è sospindût in mût temporani e al vignarà riativât ca di %s.", "gui.banned.description.unknownreason": "O vin ricevût une segnalazion par compuartament scoret dal to account. I nestris moderadôrs a àn revisionât il to câs e a àn decidût che al va cuintri dai standards de comunitât di Minecraft.", "gui.banned.name.description": "Il to non atuâl - \"%s\" - al viole i nestris standards de comunitât. Tu puedis zuiâ in zuiadôr singul, ma tu âs di cambiâ il to non par zuiâ in linie.\n\nImpare di plui o domande une revision a chest colegament: %s", "gui.banned.name.title": "Non no permetût in multi-zuiadôr", "gui.banned.reason.defamation_impersonation_false_information": "Identitât robade o difusion di informazions par abusâ o ingjanâ chei altris", "gui.banned.reason.drugs": "Riferiment a droghis ilegâls", "gui.banned.reason.extreme_violence_or_gore": "Rafigurazion di senis di vite-reâl masse violentis o cruentis", "gui.banned.reason.false_reporting": "Masse segnalazions falsis o imprecisis", "gui.banned.reason.fraud": "Acuisizion o ûs fraudolent di contignûts", "gui.banned.reason.generic_violation": "Violazion dai standards de comunitât", "gui.banned.reason.harassment_or_bullying": "Lengaç abus<PERSON><PERSON> do<PERSON> in maniere direte e danose", "gui.banned.reason.hate_speech": "Incitament al odi o discriminazion", "gui.banned.reason.hate_terrorism_notorious_figure": "Riferiments a grups di odi, organizazions teroristichis o figuris malfamadis", "gui.banned.reason.imminent_harm_to_person_or_property": "Intenzion di fâ dal mâl a personis o proprietâts te vite reâl", "gui.banned.reason.nudity_or_pornography": "Condivision di materiâl ossen o pornografic", "gui.banned.reason.sexually_inappropriate": "Argoments o contignûts di nature sessuâl", "gui.banned.reason.spam_or_advertising": "Spam o publicitât", "gui.banned.skin.description": "Il to aspiet atuâl al viole i nestris standards de comunitât. Tu puedis zuiâ cuntun aspiet predefinît o sielzi un gnûf aspiet.\n\nScuvierç di plui o domande une revision a chest colegament: %s", "gui.banned.skin.title": "Aspiet no permetût", "gui.banned.title.permanent": "Account band<PERSON>t in mût permanent", "gui.banned.title.temporary": "Account sosp<PERSON><PERSON><PERSON> in mût temporani", "gui.cancel": "<PERSON><PERSON>", "gui.chatReport.comments": "Coments", "gui.chatReport.describe": "Condividi detais nus jude a cjapâ une decision ben informade.", "gui.chatReport.discard.content": "Se tu vâs fûr, tu pierdarâs la segnalazion e i coments.\nDesideristu pardabon lâ fûr?", "gui.chatReport.discard.discard": "Lasse e scarte segnalazion", "gui.chatReport.discard.draft": "Salve come stampon", "gui.chatReport.discard.return": "Continue modifiche", "gui.chatReport.discard.title": "Scartâ segnalazion e coments?", "gui.chatReport.draft.content": "Vuelistu continuâ a modificâ la segnalazion atuâl o scartâle e creâ une gnove?", "gui.chatReport.draft.discard": "<PERSON><PERSON>", "gui.chatReport.draft.edit": "Continue modifiche", "gui.chatReport.draft.quittotitle.content": "Vuelistu continuâ a modificâle o scartâle?", "gui.chatReport.draft.quittotitle.title": "Tu âs un stampon di segnalazion che al vignarà pierdût se tu jessis dal zûc", "gui.chatReport.draft.title": "Modificâ il stampon de segnalazion de chat?", "gui.chatReport.more_comments": "Par plasê descrîf ce che al è sucedût:", "gui.chatReport.observed_what": "<PERSON><PERSON><PERSON> stâstu segnalant chê robe ca?", "gui.chatReport.read_info": "Impare di plui su lis segnalazions", "gui.chatReport.report_sent_msg": "O vin ricevût la to segnalazion cun sucès, gracie!\nLa nestre scuadre la visionarà il prime pussibil.", "gui.chatReport.select_chat": "Sielç messaçs de chat di segnalâ", "gui.chatReport.select_reason": "Sielç une categorie di segnalazion", "gui.chatReport.selected_chat": "%s messaç(s) sielzût(s) di segnalâ", "gui.chatReport.send": "Invie segnalazion", "gui.chatReport.send.comments_too_long": "Par plasê scurte il coment", "gui.chatReport.send.no_reason": "Par plasê sielç une categorie di segnalazion", "gui.chatReport.send.no_reported_messages": "Par plasê sielç almancul un messaç de chat di segnalâ", "gui.chatReport.send.too_many_messages": "Cirînt di includi masse messaçs te segnalazion", "gui.chatReport.title": "<PERSON><PERSON><PERSON> messa<PERSON>s de chat dal zu<PERSON>r", "gui.chatSelection.context": "I messaçs vicins ae selezion a vignaran includûts par dâi un contest plui complet", "gui.chatSelection.fold": "%s messaç(s) platât(s)", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s si unìs ae chat", "gui.chatSelection.message.narrate": "%s al/e à dite: %s aes %s", "gui.chatSelection.selected": "%s/%s messaç(s) selezionâts", "gui.chatSelection.title": "Sielç messaçs de chat di segnalâ", "gui.continue": "Continue", "gui.copy_link_to_clipboard": "Copie il colegament intes notis", "gui.days": "%s zornade(is)", "gui.done": "Fat", "gui.down": "<PERSON><PERSON>", "gui.entity_tooltip.type": "Gjenar: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Rifiutâts %s files", "gui.fileDropFailure.title": "Impussibil zontâ i files", "gui.hours": "%s ore(is)", "gui.loadingMinecraft": "Da<PERSON>r a cjariâ Minecraft", "gui.minutes": "%s minût(s)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Boton %s", "gui.narrate.editBox": "Casele di modifiche %s: %s", "gui.narrate.slider": "Cursôr %s", "gui.narrate.tab": "Schede %s", "gui.no": "No", "gui.none": "Nissun", "gui.ok": "<PERSON><PERSON> ben", "gui.open_report_dir": "Vierç la cartele des segnalazions", "gui.proceed": "Va indenant", "gui.recipebook.moreRecipes": "<PERSON><PERSON> diestri par altris ricetis", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Cîr...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON> lis ricetis", "gui.recipebook.toggleRecipes.blastable": "Fusions realizabilis", "gui.recipebook.toggleRecipes.craftable": "Fabricazions realizabilis", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON> realizabilis", "gui.recipebook.toggleRecipes.smokable": "Fu<PERSON><PERSON><PERSON> realizabil<PERSON>", "gui.report_to_server": "<PERSON><PERSON><PERSON> al servidôr", "gui.socialInteractions.blocking_hint": "Gjestìs cul account di Microsoft", "gui.socialInteractions.empty_blocked": "<PERSON><PERSON><PERSON> zu<PERSON>r blocât te chat", "gui.socialInteractions.empty_hidden": "<PERSON><PERSON><PERSON> zuiadôr platât te chat", "gui.socialInteractions.hidden_in_chat": "I messaçs de chat di %s a vignaran platâts", "gui.socialInteractions.hide": "Plate te chat", "gui.socialInteractions.narration.hide": "Plate i messaçs di %s te chat", "gui.socialInteractions.narration.report": "Segnale zu<PERSON> %s", "gui.socialInteractions.narration.show": "Mostre i messaçs di %s te chat", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "Impussibil cjatâ un zuiadôr cun chel non", "gui.socialInteractions.search_hint": "Cîr...", "gui.socialInteractions.server_label.multiple": "%s - %s zuiadôrs", "gui.socialInteractions.server_label.single": "%s - %s zuiadôr", "gui.socialInteractions.show": "Mostre te chat", "gui.socialInteractions.shown_in_chat": "I messaçs de chat di %s a vignaran mostrâts", "gui.socialInteractions.status_blocked": "Blocât/ade", "gui.socialInteractions.status_blocked_offline": "Blocât/ade - <PERSON><PERSON>r rêt", "gui.socialInteractions.status_hidden": "Plat<PERSON>t/ade", "gui.socialInteractions.status_hidden_offline": "Platât/ade - <PERSON>ûr rêt", "gui.socialInteractions.status_offline": "<PERSON><PERSON><PERSON> rêt", "gui.socialInteractions.tab_all": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_blocked": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "Interazions sociâls", "gui.socialInteractions.tooltip.hide": "Scuindi i messaçs", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report.disabled": "Il servizi di segnalazion no le disponibil", "gui.socialInteractions.tooltip.report.no_messages": "Nissun messaç segnalabil dal zuiadôr %s", "gui.socialInteractions.tooltip.report.not_reportable": "Chest zuiadôr no po sei segnalat, parcè che i soi messaçs no podin sei verificâts in chest server", "gui.socialInteractions.tooltip.show": "Mostre i messaçs", "gui.stats": "Statistichis", "gui.toMenu": "Torne ae liste dai servidôrs", "gui.toRealms": "Torne ae liste dai Realms", "gui.toTitle": "Torne al menù principâl", "gui.toWorld": "Torne ae liste dai monts", "gui.togglable_slot": "Fâs clic par disabilitâ il spazi", "gui.up": "<PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "Indaûr (%ss)", "gui.waitingForResponse.title": "In spiete dal servidôr", "gui.yes": "Sì", "hanging_sign.edit": "Modifiche il messaç de insegne", "instrument.minecraft.admire_goat_horn": "Amirazion", "instrument.minecraft.call_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.dream_goat_horn": "Sium", "instrument.minecraft.feel_goat_horn": "Sintiment", "instrument.minecraft.ponder_goat_horn": "Riflession", "instrument.minecraft.seek_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON>", "inventory.binSlot": "Distr<PERSON><PERSON> ogjet", "inventory.hotbarInfo": "Salve la sbare rapide cun %1$s+%2$s", "inventory.hotbarSaved": "Sbare rapide salvade (ripristine cun %1$s+%2$s)", "item.canBreak": "Al pues spacâ:", "item.canPlace": "Si pues plaçâ su:", "item.canUse.unknown": "No cognossût", "item.color": "Colôr: %s", "item.components": "%s component(s)", "item.disabled": "Ogjet disabilitât", "item.durability": "Durabilitât: %s / %s", "item.dyed": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.acacia_boat": "Barcje di agacie", "item.minecraft.acacia_chest_boat": "Barcje di agacie cun baûl", "item.minecraft.allay_spawn_egg": "Ûf di nassite dal slizeridôr", "item.minecraft.amethyst_shard": "Sclese di ametiste", "item.minecraft.angler_pottery_shard": "Crep cun cjane di pescje", "item.minecraft.angler_pottery_sherd": "Crep cun cjane di pescje", "item.minecraft.apple": "<PERSON><PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Crep cun arc e frece", "item.minecraft.archer_pottery_sherd": "Crep cun arc e frece", "item.minecraft.armadillo_scute": "Scus di armadil", "item.minecraft.armadillo_spawn_egg": "Ûf di nassite dal armadil", "item.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.arms_up_pottery_shard": "Crep cun alçade di braçs", "item.minecraft.arms_up_pottery_sherd": "Crep cun alçade di braçs", "item.minecraft.arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.axolotl_bucket": "Seglot cun axolotl", "item.minecraft.axolotl_spawn_egg": "Ûf di nassite dal axolotl", "item.minecraft.baked_potato": "Patate rustide", "item.minecraft.bamboo_chest_raft": "Çate di bambù cun baûl", "item.minecraft.bamboo_raft": "Çate di bambù", "item.minecraft.bat_spawn_egg": "Ûf di nassite dal gnotul", "item.minecraft.bee_spawn_egg": "Ûf di nassite de <PERSON>f", "item.minecraft.beef": "Cjar di manç crude", "item.minecraft.beetroot": "Jerbe rave", "item.minecraft.beetroot_seeds": "Se<PERSON><PERSON> di jerbe rave", "item.minecraft.beetroot_soup": "Sope di jerbe rave", "item.minecraft.birch_boat": "<PERSON><PERSON><PERSON> di <PERSON>oi", "item.minecraft.birch_chest_boat": "<PERSON><PERSON><PERSON> di bedoi cun baûl", "item.minecraft.black_bundle": "<PERSON><PERSON> neri", "item.minecraft.black_dye": "<PERSON>ture nere", "item.minecraft.black_harness": "<PERSON>mb<PERSON><PERSON><PERSON> nere", "item.minecraft.blade_pottery_shard": "Crep cun lame", "item.minecraft.blade_pottery_sherd": "Crep cun lame", "item.minecraft.blaze_powder": "<PERSON><PERSON> di flamade", "item.minecraft.blaze_rod": "Vuiscje di flamade", "item.minecraft.blaze_spawn_egg": "Ûf di nassite de flamade", "item.minecraft.blue_bundle": "Sacut blu", "item.minecraft.blue_dye": "Tinture blu", "item.minecraft.blue_egg": "Ûf blu", "item.minecraft.blue_harness": "Imb<PERSON><PERSON>re blu", "item.minecraft.bogged_spawn_egg": "Ûf di nassite dal paludan", "item.minecraft.bolt_armor_trim_smithing_template": "Model par fari", "item.minecraft.bolt_armor_trim_smithing_template.new": "Ornament cun st<PERSON>l bulon", "item.minecraft.bone": "<PERSON><PERSON>", "item.minecraft.bone_meal": "Farine di vues", "item.minecraft.book": "Libri", "item.minecraft.bordure_indented_banner_pattern": "Model di stendart cun bordure dintade", "item.minecraft.bow": "Arc", "item.minecraft.bowl": "<PERSON><PERSON><PERSON>", "item.minecraft.bread": "Pan", "item.minecraft.breeze_rod": "Vuiscje di brise", "item.minecraft.breeze_spawn_egg": "Ûf di nassite de brise", "item.minecraft.brewer_pottery_shard": "Crep cun ampole", "item.minecraft.brewer_pottery_sherd": "Crep cun ampole", "item.minecraft.brewing_stand": "<PERSON><PERSON>", "item.minecraft.brick": "Modon", "item.minecraft.brown_bundle": "Sacut maron", "item.minecraft.brown_dye": "Tinture maron", "item.minecraft.brown_egg": "Ûf maron", "item.minecraft.brown_harness": "Imb<PERSON><PERSON><PERSON> maron", "item.minecraft.brush": "<PERSON><PERSON><PERSON>", "item.minecraft.bucket": "Seglot", "item.minecraft.bundle": "Sacut", "item.minecraft.bundle.empty": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Al pues tignî une pile messedade di ogjets", "item.minecraft.bundle.full": "Plen", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Crep cun flame", "item.minecraft.burn_pottery_sherd": "Crep cun flame", "item.minecraft.camel_spawn_egg": "Ûf di nassite dal dromedari", "item.minecraft.carrot": "<PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON>ston cu la carote", "item.minecraft.cat_spawn_egg": "Ûf di nassite dal gjat", "item.minecraft.cauldron": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Ûf di nassite dal ragn dai landris", "item.minecraft.chainmail_boots": "Stivâi in maie di fier", "item.minecraft.chainmail_chestplate": "Usberc in maie di fier", "item.minecraft.chainmail_helmet": "Elm in maie di fier", "item.minecraft.chainmail_leggings": "Braghesse in maie di fier", "item.minecraft.charcoal": "Carbonele", "item.minecraft.cherry_boat": "<PERSON><PERSON><PERSON> di cjariesâr", "item.minecraft.cherry_chest_boat": "<PERSON><PERSON><PERSON> di <PERSON>r cun baûl", "item.minecraft.chest_minecart": "Carel minerari cun baûl", "item.minecraft.chicken": "<PERSON>ç crût", "item.minecraft.chicken_spawn_egg": "Ûf di nassite dal poleç", "item.minecraft.chorus_fruit": "<PERSON><PERSON>", "item.minecraft.clay_ball": "Bale di arzile", "item.minecraft.clock": "<PERSON><PERSON><PERSON>", "item.minecraft.coal": "Cjarbon", "item.minecraft.coast_armor_trim_smithing_template": "Model par fari", "item.minecraft.coast_armor_trim_smithing_template.new": "Ornament cun st<PERSON>l cost<PERSON>r", "item.minecraft.cocoa_beans": "Favis di cacau", "item.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON> cr<PERSON>t", "item.minecraft.cod_bucket": "Seglot cun merluç", "item.minecraft.cod_spawn_egg": "Ûf di nassite dal merluç", "item.minecraft.command_block_minecart": "Carel minerari cun bloc comants", "item.minecraft.compass": "Bussule", "item.minecraft.cooked_beef": "Bisteche", "item.minecraft.cooked_chicken": "<PERSON><PERSON> cuet", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_mutton": "<PERSON><PERSON> di piore cuete", "item.minecraft.cooked_porkchop": "Brusadule di purcit cuete", "item.minecraft.cooked_rabbit": "<PERSON><PERSON> di cunin cuete", "item.minecraft.cooked_salmon": "Salmon cuet", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "<PERSON><PERSON> di ram", "item.minecraft.cow_spawn_egg": "Ûf di nassite de vacje", "item.minecraft.creaking_spawn_egg": "Ûf di nassite dal cric", "item.minecraft.creeper_banner_pattern": "<PERSON> di stendart", "item.minecraft.creeper_banner_pattern.desc": "<PERSON> di creeper", "item.minecraft.creeper_banner_pattern.new": "Model di stendart cun muse di creeper", "item.minecraft.creeper_spawn_egg": "Ûf di nassite dal creeper", "item.minecraft.crossbow": "<PERSON><PERSON><PERSON>", "item.minecraft.crossbow.projectile": "Proietil:", "item.minecraft.crossbow.projectile.multiple": "Proietil: %s x %s", "item.minecraft.crossbow.projectile.single": "Proietil: %s", "item.minecraft.cyan_bundle": "Sacut ciano", "item.minecraft.cyan_dye": "<PERSON>ture ciano", "item.minecraft.cyan_harness": "Imbragadure ciano", "item.minecraft.danger_pottery_shard": "Crep cun avertiment", "item.minecraft.danger_pottery_sherd": "Crep cun avertiment", "item.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON> di rôl scûr", "item.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON> di rôl scûr cun baûl", "item.minecraft.debug_stick": "Stec pal debug", "item.minecraft.debug_stick.empty": "%s nol à proprietâts", "item.minecraft.debug_stick.select": "selezionât \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" a %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "Man<PERSON><PERSON>", "item.minecraft.diamond_boots": "Stivâi di diamant", "item.minecraft.diamond_chestplate": "<PERSON><PERSON>", "item.minecraft.diamond_helmet": "<PERSON> di di<PERSON>t", "item.minecraft.diamond_hoe": "<PERSON><PERSON> di <PERSON>", "item.minecraft.diamond_horse_armor": "Furniment in diamant pal cjaval", "item.minecraft.diamond_leggings": "Gjambâi di diamant", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON> di diamant", "item.minecraft.diamond_shovel": "<PERSON><PERSON> <PERSON> di<PERSON>t", "item.minecraft.diamond_sword": "Spade di diamant", "item.minecraft.disc_fragment_5": "Toc di disc", "item.minecraft.disc_fragment_5.desc": "Disc musicâl - 5", "item.minecraft.dolphin_spawn_egg": "Ûf di nassite dal dolfin", "item.minecraft.donkey_spawn_egg": "Ûf di nassite dal mus", "item.minecraft.dragon_breath": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.dried_kelp": "<PERSON><PERSON><PERSON>", "item.minecraft.drowned_spawn_egg": "Ûf di nassite dal inneât", "item.minecraft.dune_armor_trim_smithing_template": "Model par fari", "item.minecraft.dune_armor_trim_smithing_template.new": "Ornament cun stîl dune", "item.minecraft.echo_shard": "Sclese di risonance", "item.minecraft.egg": "Ûf", "item.minecraft.elder_guardian_spawn_egg": "Ûf di nassite dal vuardian veteran", "item.minecraft.elytra": "<PERSON><PERSON><PERSON>", "item.minecraft.emerald": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "Libri <PERSON>", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON><PERSON> aur incjan<PERSON>", "item.minecraft.end_crystal": "<PERSON><PERSON><PERSON> dal <PERSON>", "item.minecraft.ender_dragon_spawn_egg": "Ûf di nassite dal drâc dal End", "item.minecraft.ender_eye": "<PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON> dal <PERSON>", "item.minecraft.enderman_spawn_egg": "Ûf di nassite dal enderman", "item.minecraft.endermite_spawn_egg": "Ûf di nassite dal endermite", "item.minecraft.evoker_spawn_egg": "Ûf di nassite dal evocadôr", "item.minecraft.experience_bottle": "Pozion di esperience", "item.minecraft.explorer_pottery_shard": "Crep cun mape", "item.minecraft.explorer_pottery_sherd": "Crep cun mape", "item.minecraft.eye_armor_trim_smithing_template": "Model par fari", "item.minecraft.eye_armor_trim_smithing_template.new": "Ornament cun stîl voli", "item.minecraft.feather": "Plume", "item.minecraft.fermented_spider_eye": "Voli di ragn fermentât", "item.minecraft.field_masoned_banner_pattern": "Model di stendart cun semenât di modons", "item.minecraft.filled_map": "Mape", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON> di <PERSON>c", "item.minecraft.firework_rocket": "Fusete", "item.minecraft.firework_rocket.flight": "<PERSON><PERSON> dal svol:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Stele de fusete", "item.minecraft.firework_star.black": "<PERSON><PERSON>", "item.minecraft.firework_star.blue": "Blu", "item.minecraft.firework_star.brown": "Mar<PERSON>", "item.minecraft.firework_star.custom_color": "Personalizade", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON><PERSON> su", "item.minecraft.firework_star.flicker": "Slusicament", "item.minecraft.firework_star.gray": "Grise", "item.minecraft.firework_star.green": "Verde", "item.minecraft.firework_star.light_blue": "<PERSON> clâr", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.lime": "Vert limon", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.pink": "<PERSON>", "item.minecraft.firework_star.purple": "Viole", "item.minecraft.firework_star.red": "Ross<PERSON>", "item.minecraft.firework_star.shape": "Forme no cognossude", "item.minecraft.firework_star.shape.burst": "A sclop", "item.minecraft.firework_star.shape.creeper": "A forme di creeper", "item.minecraft.firework_star.shape.large_ball": "Sfere grande", "item.minecraft.firework_star.shape.small_ball": "Sfere piçule", "item.minecraft.firework_star.shape.star": "A forme di stele", "item.minecraft.firework_star.trail": "Code", "item.minecraft.firework_star.white": "<PERSON><PERSON>", "item.minecraft.firework_star.yellow": "Z<PERSON>", "item.minecraft.fishing_rod": "<PERSON><PERSON><PERSON> di pescje", "item.minecraft.flint": "<PERSON><PERSON><PERSON>", "item.minecraft.flint_and_steel": "<PERSON><PERSON><PERSON>", "item.minecraft.flow_armor_trim_smithing_template": "Model par fari", "item.minecraft.flow_armor_trim_smithing_template.new": "Ornament cun stîl flus", "item.minecraft.flow_banner_pattern": "<PERSON> di stendart", "item.minecraft.flow_banner_pattern.desc": "Flus", "item.minecraft.flow_banner_pattern.new": "Model di stendart cun flus", "item.minecraft.flow_pottery_sherd": "Crep cun flus", "item.minecraft.flower_banner_pattern": "<PERSON> di stendart", "item.minecraft.flower_banner_pattern.desc": "<PERSON>", "item.minecraft.flower_banner_pattern.new": "Model di stendart cun rose", "item.minecraft.flower_pot": "Vâs par rosis", "item.minecraft.fox_spawn_egg": "Ûf di nassite de bolp", "item.minecraft.friend_pottery_shard": "Crep cun amì", "item.minecraft.friend_pottery_sherd": "Crep cun amì", "item.minecraft.frog_spawn_egg": "Ûf di nassite dal crot", "item.minecraft.furnace_minecart": "Carel minerari cun fornâs", "item.minecraft.ghast_spawn_egg": "Ûf di nassite dal ghast", "item.minecraft.ghast_tear": "Lagrime di ghast", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "Fete sflandorose di angurie", "item.minecraft.globe_banner_pattern": "<PERSON> di stendart", "item.minecraft.globe_banner_pattern.desc": "Globi", "item.minecraft.globe_banner_pattern.new": "Model di stendart cun globi", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON> luminessentis", "item.minecraft.glow_ink_sac": "Sache di ingjustri luminessent", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON> lumi<PERSON>", "item.minecraft.glow_squid_spawn_egg": "Ûf di nassite dal calamâr luminessent", "item.minecraft.glowstone_dust": "<PERSON><PERSON> di piere luminose", "item.minecraft.goat_horn": "<PERSON><PERSON>r di <PERSON>vre", "item.minecraft.goat_spawn_egg": "Ûf di nassite de cjavre", "item.minecraft.gold_ingot": "<PERSON><PERSON> di aur", "item.minecraft.gold_nugget": "Pepite di aur", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON>r", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON> di aur", "item.minecraft.golden_boots": "Stivâi in aur", "item.minecraft.golden_carrot": "<PERSON><PERSON>", "item.minecraft.golden_chestplate": "Corace in aur", "item.minecraft.golden_helmet": "Elm in aur", "item.minecraft.golden_hoe": "<PERSON><PERSON> di aur", "item.minecraft.golden_horse_armor": "Furniment in aur pal cjaval", "item.minecraft.golden_leggings": "Gjambâi in aur", "item.minecraft.golden_pickaxe": "<PERSON><PERSON> di aur", "item.minecraft.golden_shovel": "<PERSON><PERSON> di aur", "item.minecraft.golden_sword": "Spade di aur", "item.minecraft.gray_bundle": "Sacut grîs", "item.minecraft.gray_dye": "Tinture grise", "item.minecraft.gray_harness": "Imbragadure grise", "item.minecraft.green_bundle": "Sacut vert", "item.minecraft.green_dye": "Tinture verde", "item.minecraft.green_harness": "Imbragadure verde", "item.minecraft.guardian_spawn_egg": "Ûf di nassite dal vuardian", "item.minecraft.gunpowder": "<PERSON><PERSON> di s<PERSON>lope", "item.minecraft.guster_banner_pattern": "<PERSON> di stendart", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "Model di stendart cun bugade", "item.minecraft.guster_pottery_sherd": "Crep cun bugade", "item.minecraft.happy_ghast_spawn_egg": "Ûf di nassite dal <PERSON> legri", "item.minecraft.harness": "Imbragadure", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON><PERSON> dal mâr", "item.minecraft.heart_pottery_shard": "<PERSON>rep cun cûr", "item.minecraft.heart_pottery_sherd": "<PERSON>rep cun cûr", "item.minecraft.heartbreak_pottery_shard": "Crep cun cûr rot", "item.minecraft.heartbreak_pottery_sherd": "Crep cun cûr rot", "item.minecraft.hoglin_spawn_egg": "Ûf di nassite dal hoglin", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON> di <PERSON>", "item.minecraft.honeycomb": "<PERSON><PERSON>", "item.minecraft.hopper_minecart": "Carel minerari cun tramuele", "item.minecraft.horse_spawn_egg": "Ûf di nassite dal cjaval", "item.minecraft.host_armor_trim_smithing_template": "Model par fari", "item.minecraft.host_armor_trim_smithing_template.new": "Ornament cun stîl ospit", "item.minecraft.howl_pottery_shard": "Crep cun lôf", "item.minecraft.howl_pottery_sherd": "Crep cun lôf", "item.minecraft.husk_spawn_egg": "Ûf di nassite dal zombi sec", "item.minecraft.ink_sac": "Sache di ingjustri", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON> di <PERSON>er", "item.minecraft.iron_boots": "Stivâi di fier", "item.minecraft.iron_chestplate": "<PERSON><PERSON> di <PERSON>er", "item.minecraft.iron_golem_spawn_egg": "Ûf di nassite dal golem di fier", "item.minecraft.iron_helmet": "<PERSON> di fier", "item.minecraft.iron_hoe": "<PERSON><PERSON> di fier", "item.minecraft.iron_horse_armor": "Furniment in fier pal cjaval", "item.minecraft.iron_ingot": "<PERSON><PERSON> di fier", "item.minecraft.iron_leggings": "Gjambâi di fier", "item.minecraft.iron_nugget": "Pepite di fier", "item.minecraft.iron_pickaxe": "<PERSON><PERSON> di <PERSON>er", "item.minecraft.iron_shovel": "<PERSON><PERSON> di fier", "item.minecraft.iron_sword": "Spade di fier", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "Bar<PERSON>je di len di jungle", "item.minecraft.jungle_chest_boat": "Bar<PERSON><PERSON> di len di jungle cun baûl", "item.minecraft.knowledge_book": "<PERSON><PERSON> dal savê", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Seglot di lave", "item.minecraft.lead": "Sguinçâl", "item.minecraft.leather": "<PERSON><PERSON>", "item.minecraft.leather_boots": "Stivâi di corean", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_helmet": "Casc di corean", "item.minecraft.leather_horse_armor": "Furniment in corean pal cjaval", "item.minecraft.leather_leggings": "Bregons di corean", "item.minecraft.light_blue_bundle": "Sacut blu clâr", "item.minecraft.light_blue_dye": "<PERSON><PERSON> blu clâr", "item.minecraft.light_blue_harness": "Imb<PERSON><PERSON><PERSON> blu clâr", "item.minecraft.light_gray_bundle": "Sacut gr<PERSON>s cl<PERSON>r", "item.minecraft.light_gray_dye": "<PERSON><PERSON> grî<PERSON> cl<PERSON>r", "item.minecraft.light_gray_harness": "<PERSON>mb<PERSON><PERSON><PERSON> gr<PERSON><PERSON> c<PERSON>r", "item.minecraft.lime_bundle": "Sacut vert limon", "item.minecraft.lime_dye": "Tinture vert limon", "item.minecraft.lime_harness": "Imbragadure vert limon", "item.minecraft.lingering_potion": "Pozion persistente", "item.minecraft.lingering_potion.effect.awkward": "Pozion strane persistente", "item.minecraft.lingering_potion.effect.empty": "Pozion persistente no fabricabile", "item.minecraft.lingering_potion.effect.fire_resistance": "Pozion di resistence al fûc persistente", "item.minecraft.lingering_potion.effect.harming": "Pozion di dam persistente", "item.minecraft.lingering_potion.effect.healing": "Pozion di cure persistente", "item.minecraft.lingering_potion.effect.infested": "Pozion di infestazion persistente", "item.minecraft.lingering_potion.effect.invisibility": "Pozion di invisibilitât persistente", "item.minecraft.lingering_potion.effect.leaping": "Pozion di salt persistente", "item.minecraft.lingering_potion.effect.levitation": "Pozion di levitazion persistente", "item.minecraft.lingering_potion.effect.luck": "Pozion di fortune persistente", "item.minecraft.lingering_potion.effect.mundane": "Pozion ordenarie persistente", "item.minecraft.lingering_potion.effect.night_vision": "Pozion di vision noturne persistente", "item.minecraft.lingering_potion.effect.oozing": "Pozion di secrezion persistente", "item.minecraft.lingering_potion.effect.poison": "Pozion di invelenament persistente", "item.minecraft.lingering_potion.effect.regeneration": "Pozion di rigjenerazion persistente", "item.minecraft.lingering_potion.effect.slow_falling": "Pozion persistente pal colâ planc", "item.minecraft.lingering_potion.effect.slowness": "Pozion di lentece persistente", "item.minecraft.lingering_potion.effect.strength": "Pozion di fuarce persistente", "item.minecraft.lingering_potion.effect.swiftness": "Pozion di rapiditât persistente", "item.minecraft.lingering_potion.effect.thick": "Pozion penze persistente", "item.minecraft.lingering_potion.effect.turtle_master": "Pozion dal mestri copasse persistente", "item.minecraft.lingering_potion.effect.water": "<PERSON>ce di aghe persistente", "item.minecraft.lingering_potion.effect.water_breathing": "Pozion di respîr te aghe persistente", "item.minecraft.lingering_potion.effect.weakness": "Pozion di debilece persistente", "item.minecraft.lingering_potion.effect.weaving": "Pozion di tiessidure persistente", "item.minecraft.lingering_potion.effect.wind_charged": "Pozion di cjarie di aiar persistente", "item.minecraft.llama_spawn_egg": "Ûf di nassite dal lama", "item.minecraft.lodestone_compass": "Bussule magnetizade", "item.minecraft.mace": "Mace", "item.minecraft.magenta_bundle": "Sacut magenta", "item.minecraft.magenta_dye": "Tinture magenta", "item.minecraft.magenta_harness": "Imbragadure magenta", "item.minecraft.magma_cream": "Creme di magme", "item.minecraft.magma_cube_spawn_egg": "Ûf di nassite dal cubul di magme", "item.minecraft.mangrove_boat": "Barcje di mangrovie", "item.minecraft.mangrove_chest_boat": "Barcje di mangrovie cun baûl", "item.minecraft.map": "Mape vueide", "item.minecraft.melon_seeds": "<PERSON><PERSON><PERSON> di angurie", "item.minecraft.melon_slice": "Fete di angurie", "item.minecraft.milk_bucket": "Seglot di lat", "item.minecraft.minecart": "<PERSON><PERSON> mine<PERSON>i", "item.minecraft.miner_pottery_shard": "Crep cun picon", "item.minecraft.miner_pottery_sherd": "Crep cun picon", "item.minecraft.mojang_banner_pattern": "<PERSON> di stendart", "item.minecraft.mojang_banner_pattern.desc": "Logo", "item.minecraft.mojang_banner_pattern.new": "Model di stendart cun logo", "item.minecraft.mooshroom_spawn_egg": "Ûf di nassite di mooshroom", "item.minecraft.mourner_pottery_shard": "Crep cun corot", "item.minecraft.mourner_pottery_sherd": "Crep cun corot", "item.minecraft.mule_spawn_egg": "Ûf di nassite dal mûl", "item.minecraft.mushroom_stew": "Sope di foncs", "item.minecraft.music_disc_11": "Disc musicâl", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Disc musicâl", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Disc musicâl", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Disc musicâl", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Disc musicâl", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Disc musicâl", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Disc musicâl", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Disc musicâl", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> (carillon)", "item.minecraft.music_disc_far": "Disc musicâl", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Disc musicâl", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Disc musicâl", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Disc musicâl", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Disc musicâl", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Disc musicâl", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Disc musicâl", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Disc musicâl", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Disc musicâl", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Disc musicâl", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Disc musicâl", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Disc musicâl", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Disc musicâl", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON> di piore crude", "item.minecraft.name_tag": "Etichete pal non", "item.minecraft.nautilus_shell": "Cape di nautil", "item.minecraft.nether_brick": "<PERSON><PERSON> dal <PERSON>", "item.minecraft.nether_star": "<PERSON><PERSON> da<PERSON>", "item.minecraft.nether_wart": "<PERSON><PERSON><PERSON><PERSON><PERSON> dal <PERSON>", "item.minecraft.netherite_axe": "Manarie in netherite", "item.minecraft.netherite_boots": "Stivâi in netherite", "item.minecraft.netherite_chestplate": "Corace in netherite", "item.minecraft.netherite_helmet": "Elm in netherite", "item.minecraft.netherite_hoe": "Sape in netherite", "item.minecraft.netherite_ingot": "Lingot di netherite", "item.minecraft.netherite_leggings": "Gjambâi in netherite", "item.minecraft.netherite_pickaxe": "Picon in netherite", "item.minecraft.netherite_scrap": "Rotum di netherite", "item.minecraft.netherite_shovel": "Pale in netherite", "item.minecraft.netherite_sword": "Spade in netherite", "item.minecraft.netherite_upgrade_smithing_template": "Model par fari", "item.minecraft.netherite_upgrade_smithing_template.new": "Miorament di netherite", "item.minecraft.oak_boat": "<PERSON><PERSON>je di rôl", "item.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON> di rôl cun baûl", "item.minecraft.ocelot_spawn_egg": "Ûf di nassite dal gjat salvadi", "item.minecraft.ominous_bottle": "<PERSON><PERSON> dal ma<PERSON>", "item.minecraft.ominous_trial_key": "<PERSON><PERSON><PERSON><PERSON> de <PERSON> dal malauguri", "item.minecraft.orange_bundle": "<PERSON><PERSON>", "item.minecraft.orange_dye": "<PERSON><PERSON>", "item.minecraft.orange_harness": "Imbragadure <PERSON>", "item.minecraft.painting": "Cuadri", "item.minecraft.pale_oak_boat": "<PERSON><PERSON><PERSON> di rôl palit", "item.minecraft.pale_oak_chest_boat": "<PERSON><PERSON><PERSON> di rôl palit cun baûl", "item.minecraft.panda_spawn_egg": "Ûf di nassite dal panda", "item.minecraft.paper": "<PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Ûf di nassite dal pampagal", "item.minecraft.phantom_membrane": "Membrane di fantasime", "item.minecraft.phantom_spawn_egg": "Ûf di nassite de fantasime", "item.minecraft.pig_spawn_egg": "Ûf di nassite dal purcit", "item.minecraft.piglin_banner_pattern": "<PERSON> di stendart", "item.minecraft.piglin_banner_pattern.desc": "Music", "item.minecraft.piglin_banner_pattern.new": "Model di stendart cun music", "item.minecraft.piglin_brute_spawn_egg": "Ûf di nassite dal piglin brutâl", "item.minecraft.piglin_spawn_egg": "Ûf di nassite dal piglin", "item.minecraft.pillager_spawn_egg": "Ûf di nassite dal sachizadôr", "item.minecraft.pink_bundle": "Sacut rose", "item.minecraft.pink_dye": "Tinture rose", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON> rose", "item.minecraft.pitcher_plant": "<PERSON><PERSON> b<PERSON>", "item.minecraft.pitcher_pod": "Cosul di plante bocâl", "item.minecraft.plenty_pottery_shard": "<PERSON>rep cun ricjecis", "item.minecraft.plenty_pottery_sherd": "<PERSON>rep cun ricjecis", "item.minecraft.poisonous_potato": "<PERSON><PERSON> velenose", "item.minecraft.polar_bear_spawn_egg": "Ûf di nassite dal ors polâr", "item.minecraft.popped_chorus_fruit": "<PERSON><PERSON> sclopade", "item.minecraft.porkchop": "Brusadule di purcit crude", "item.minecraft.potato": "<PERSON><PERSON>", "item.minecraft.potion": "Pozion", "item.minecraft.potion.effect.awkward": "Pozion strane", "item.minecraft.potion.effect.empty": "Pozion no fabricabile", "item.minecraft.potion.effect.fire_resistance": "Pozion di resistence al fûc", "item.minecraft.potion.effect.harming": "Pozion di dam", "item.minecraft.potion.effect.healing": "Pozion di cure", "item.minecraft.potion.effect.infested": "Pozion di infestazion", "item.minecraft.potion.effect.invisibility": "Pozion di invisibilitât", "item.minecraft.potion.effect.leaping": "Pozion di salt", "item.minecraft.potion.effect.levitation": "Pozion di levitazion", "item.minecraft.potion.effect.luck": "Pozion di fortune", "item.minecraft.potion.effect.mundane": "Pozion ordenarie", "item.minecraft.potion.effect.night_vision": "Pozion de vision noturne", "item.minecraft.potion.effect.oozing": "Pozion di secrezion", "item.minecraft.potion.effect.poison": "Pozion di invelenament", "item.minecraft.potion.effect.regeneration": "Pozion di rigjenerazion", "item.minecraft.potion.effect.slow_falling": "Pozion dal colâ planc", "item.minecraft.potion.effect.slowness": "Pozion di lentece", "item.minecraft.potion.effect.strength": "Pozion di fuarce", "item.minecraft.potion.effect.swiftness": "Pozion di rapiditât", "item.minecraft.potion.effect.thick": "Pozion penze", "item.minecraft.potion.effect.turtle_master": "Pozion dal mestri copasse", "item.minecraft.potion.effect.water": "<PERSON>ce di aghe", "item.minecraft.potion.effect.water_breathing": "Pozion di respîr te aghe", "item.minecraft.potion.effect.weakness": "Pozion di debilece", "item.minecraft.potion.effect.weaving": "Pozion di tiessidure", "item.minecraft.potion.effect.wind_charged": "Pozion di cjarie di aiar", "item.minecraft.pottery_shard_archer": "Crep cun arc e frece", "item.minecraft.pottery_shard_arms_up": "Crep cun alçade di braçs", "item.minecraft.pottery_shard_prize": "Crep cun premi", "item.minecraft.pottery_shard_skull": "Crep cun crani", "item.minecraft.powder_snow_bucket": "Seglot di nêf farinose", "item.minecraft.prismarine_crystals": "Cristai di prismarine", "item.minecraft.prismarine_shard": "Toc di prismarine", "item.minecraft.prize_pottery_shard": "Crep cun premi", "item.minecraft.prize_pottery_sherd": "Crep cun premi", "item.minecraft.pufferfish": "Pes-bale", "item.minecraft.pufferfish_bucket": "Seglot cun pes-bale", "item.minecraft.pufferfish_spawn_egg": "Ûf di nassite dal pes-bale", "item.minecraft.pumpkin_pie": "Torte di coce", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON><PERSON> di coce", "item.minecraft.purple_bundle": "Sacut viole", "item.minecraft.purple_dye": "Tinture viole", "item.minecraft.purple_harness": "Imbraga<PERSON><PERSON> viole", "item.minecraft.quartz": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON>jar di cunin crude", "item.minecraft.rabbit_foot": "<PERSON><PERSON>pe di cunin", "item.minecraft.rabbit_hide": "Piel di cunin", "item.minecraft.rabbit_spawn_egg": "Ûf di nassite dal cunin", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON><PERSON> di cunin", "item.minecraft.raiser_armor_trim_smithing_template": "Model par fari", "item.minecraft.raiser_armor_trim_smithing_template.new": "Ornament cun st<PERSON><PERSON>", "item.minecraft.ravager_spawn_egg": "Ûf di nassite dal devastadôr", "item.minecraft.raw_copper": "<PERSON>", "item.minecraft.raw_gold": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "<PERSON><PERSON><PERSON> dal recupar", "item.minecraft.red_bundle": "Sacut ros", "item.minecraft.red_dye": "Tinture rosse", "item.minecraft.red_harness": "Imbragadure rosse", "item.minecraft.redstone": "Polvar di redstone", "item.minecraft.resin_brick": "Modon di resine", "item.minecraft.resin_clump": "Grop di resine", "item.minecraft.rib_armor_trim_smithing_template": "Model par fari", "item.minecraft.rib_armor_trim_smithing_template.new": "Ornament cun st<PERSON><PERSON> cuestis", "item.minecraft.rotten_flesh": "<PERSON><PERSON> fraide", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "Salmon crût", "item.minecraft.salmon_bucket": "Seglot cun salmon", "item.minecraft.salmon_spawn_egg": "Ûf di nassite di salmon", "item.minecraft.scrape_pottery_sherd": "Crep cun russade", "item.minecraft.scute": "<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Model par fari", "item.minecraft.sentry_armor_trim_smithing_template.new": "Ornament cun st<PERSON>l sentinele", "item.minecraft.shaper_armor_trim_smithing_template": "Model par fari", "item.minecraft.shaper_armor_trim_smithing_template.new": "Ornament cun st<PERSON>l <PERSON>r", "item.minecraft.sheaf_pottery_shard": "Crep cun mane", "item.minecraft.sheaf_pottery_sherd": "Crep cun mane", "item.minecraft.shears": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sheep_spawn_egg": "Ûf di nassite de piore", "item.minecraft.shelter_pottery_shard": "Crep cun sotet", "item.minecraft.shelter_pottery_sherd": "Crep cun sotet", "item.minecraft.shield": "Scût", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON> neri", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON> blu", "item.minecraft.shield.brown": "<PERSON><PERSON>t maron", "item.minecraft.shield.cyan": "Scût ciano", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON> grîs", "item.minecraft.shield.green": "Scût vert", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON> blu clâr", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON> gr<PERSON><PERSON> clâr", "item.minecraft.shield.lime": "Scût vert limon", "item.minecraft.shield.magenta": "Scût magenta", "item.minecraft.shield.orange": "Scût naranç", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON> rose", "item.minecraft.shield.purple": "<PERSON><PERSON><PERSON> viole", "item.minecraft.shield.red": "Scût ros", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON> blanc", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON>l", "item.minecraft.shulker_shell": "<PERSON><PERSON> dal shulker", "item.minecraft.shulker_spawn_egg": "Ûf di nassite dal shulker", "item.minecraft.sign": "Cartel", "item.minecraft.silence_armor_trim_smithing_template": "Model par fari", "item.minecraft.silence_armor_trim_smithing_template.new": "Ornament cun stîl cidin", "item.minecraft.silverfish_spawn_egg": "Ûf di nassite dal pessut di arint", "item.minecraft.skeleton_horse_spawn_egg": "Ûf di nassite dal cjaval scheletric", "item.minecraft.skeleton_spawn_egg": "Ûf di nassite dal scheletri", "item.minecraft.skull_banner_pattern": "<PERSON> di stendart", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "<PERSON> di stendart cun crani", "item.minecraft.skull_pottery_shard": "Crep cun crani", "item.minecraft.skull_pottery_sherd": "Crep cun crani", "item.minecraft.slime_ball": "<PERSON>le di gjeladine", "item.minecraft.slime_spawn_egg": "Ûf di nassite de gjeladine", "item.minecraft.smithing_template": "Model par fari", "item.minecraft.smithing_template.applies_to": "Si apliche a:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Zonte un lingot o un cristal", "item.minecraft.smithing_template.armor_trim.applies_to": "Armadure", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Zonte un toc di armadure", "item.minecraft.smithing_template.armor_trim.ingredients": "Lingots e cristai", "item.minecraft.smithing_template.ingredients": "Ingredients:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Zonte un lingot di netherite", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Furniment di diamant", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "<PERSON>onte une armadure, arme o imprest di diamant", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Lingot di netherite", "item.minecraft.smithing_template.upgrade": "Miorament: ", "item.minecraft.sniffer_spawn_egg": "Ûf di nassite dal nasadôr", "item.minecraft.snort_pottery_shard": "Crep cun nasadôr", "item.minecraft.snort_pottery_sherd": "Crep cun nasadôr", "item.minecraft.snout_armor_trim_smithing_template": "Model par fari", "item.minecraft.snout_armor_trim_smithing_template.new": "Ornament cun stîl music", "item.minecraft.snow_golem_spawn_egg": "Ûf di nassite dal golem di nêf", "item.minecraft.snowball": "<PERSON><PERSON> di <PERSON>ê<PERSON>", "item.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.spider_eye": "<PERSON>i di ragn", "item.minecraft.spider_spawn_egg": "Ûf di nassite dal ragn", "item.minecraft.spire_armor_trim_smithing_template": "Model par fari", "item.minecraft.spire_armor_trim_smithing_template.new": "Ornament cun stîl spicis", "item.minecraft.splash_potion": "Pozion di tîr", "item.minecraft.splash_potion.effect.awkward": "Pozion di tîr strane", "item.minecraft.splash_potion.effect.empty": "Pozion di tîr no fabricabile", "item.minecraft.splash_potion.effect.fire_resistance": "Pozion di tîr pe resistence al fûc", "item.minecraft.splash_potion.effect.harming": "Pozion di tîr di dam", "item.minecraft.splash_potion.effect.healing": "Pozion di tîr di cure", "item.minecraft.splash_potion.effect.infested": "Pozion di tîr di infestazion", "item.minecraft.splash_potion.effect.invisibility": "Pozion di tîr pe invisibilitât", "item.minecraft.splash_potion.effect.leaping": "Pozion di tîr par saltâ", "item.minecraft.splash_potion.effect.levitation": "Pozion di tîr pe levitazion", "item.minecraft.splash_potion.effect.luck": "Pozion di tîr pe fortune", "item.minecraft.splash_potion.effect.mundane": "Pozion di tîr ordenarie", "item.minecraft.splash_potion.effect.night_vision": "Pozion di tîr pe vision noturne", "item.minecraft.splash_potion.effect.oozing": "Pozion di tîr di secrezion", "item.minecraft.splash_potion.effect.poison": "Pozion di tîr invelenade", "item.minecraft.splash_potion.effect.regeneration": "Pozion di tîr pe rigjenerazion", "item.minecraft.splash_potion.effect.slow_falling": "Pozion di tîr par colâ planc", "item.minecraft.splash_potion.effect.slowness": "Pozion di tîr pe lentece", "item.minecraft.splash_potion.effect.strength": "Pozion di tîr pe fuarce", "item.minecraft.splash_potion.effect.swiftness": "Pozion di tîr pe rapiditât", "item.minecraft.splash_potion.effect.thick": "Pozion di tîr penze", "item.minecraft.splash_potion.effect.turtle_master": "Pozion di tîr dal mestri copasse", "item.minecraft.splash_potion.effect.water": "Boce di aghe di tirâ", "item.minecraft.splash_potion.effect.water_breathing": "Pozion di tîr par respitâ te aghe", "item.minecraft.splash_potion.effect.weakness": "Pozion di tîr pe debilece", "item.minecraft.splash_potion.effect.weaving": "Pozion di tîr di tiessidure", "item.minecraft.splash_potion.effect.wind_charged": "Pozion di tîr di cjarie di aiar", "item.minecraft.spruce_boat": "Barcje di peç", "item.minecraft.spruce_chest_boat": "Bar<PERSON>je di peç cun baûl", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "Ûf di nassite dal calamâr", "item.minecraft.stick": "Stec", "item.minecraft.stone_axe": "Manari<PERSON> di piere", "item.minecraft.stone_hoe": "<PERSON>pe di piere", "item.minecraft.stone_pickaxe": "<PERSON><PERSON> di piere", "item.minecraft.stone_shovel": "Pale di piere", "item.minecraft.stone_sword": "Spade di piere", "item.minecraft.stray_spawn_egg": "Ûf di nassite dal scheletri vagabont", "item.minecraft.strider_spawn_egg": "Ûf di nassite dal strider", "item.minecraft.string": "<PERSON><PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON>", "item.minecraft.suspicious_stew": "<PERSON><PERSON> suspiete", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON> dolcis", "item.minecraft.tadpole_bucket": "Seglot cun cudul", "item.minecraft.tadpole_spawn_egg": "Ûf di nassite dal cudul", "item.minecraft.tide_armor_trim_smithing_template": "Model par fari", "item.minecraft.tide_armor_trim_smithing_template.new": "Ornament cun st<PERSON>l maree", "item.minecraft.tipped_arrow": "<PERSON><PERSON><PERSON> im<PERSON>", "item.minecraft.tipped_arrow.effect.awkward": "<PERSON><PERSON><PERSON> im<PERSON>", "item.minecraft.tipped_arrow.effect.empty": "Frece imbombade no fabricabile", "item.minecraft.tipped_arrow.effect.fire_resistance": "Frece di resistence al fûc", "item.minecraft.tipped_arrow.effect.harming": "Frece di dam", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON><PERSON> di <PERSON>", "item.minecraft.tipped_arrow.effect.infested": "Frece di infestazion", "item.minecraft.tipped_arrow.effect.invisibility": "Frece di invisibilitât", "item.minecraft.tipped_arrow.effect.leaping": "Frece di salt", "item.minecraft.tipped_arrow.effect.levitation": "Frece di levitazion", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON>e di fortune", "item.minecraft.tipped_arrow.effect.mundane": "<PERSON><PERSON><PERSON> im<PERSON>", "item.minecraft.tipped_arrow.effect.night_vision": "Frece di vision noturne", "item.minecraft.tipped_arrow.effect.oozing": "Frece di secrezion", "item.minecraft.tipped_arrow.effect.poison": "Frece invel<PERSON>de", "item.minecraft.tipped_arrow.effect.regeneration": "Frece di rigjenerazion", "item.minecraft.tipped_arrow.effect.slow_falling": "<PERSON><PERSON><PERSON> dal col<PERSON> planc", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON> di lentece", "item.minecraft.tipped_arrow.effect.strength": "Frece di fuarce", "item.minecraft.tipped_arrow.effect.swiftness": "Frece di rapiditât", "item.minecraft.tipped_arrow.effect.thick": "<PERSON><PERSON><PERSON> im<PERSON>", "item.minecraft.tipped_arrow.effect.turtle_master": "<PERSON><PERSON><PERSON> dal mestri copasse", "item.minecraft.tipped_arrow.effect.water": "Frece di aghe", "item.minecraft.tipped_arrow.effect.water_breathing": "Frece di respîr te aghe", "item.minecraft.tipped_arrow.effect.weakness": "Frece di debilece", "item.minecraft.tipped_arrow.effect.weaving": "Frece di tiessidure", "item.minecraft.tipped_arrow.effect.wind_charged": "Frece di cjarie di aiar", "item.minecraft.tnt_minecart": "Carel minerari cun TNT", "item.minecraft.torchflower_seeds": "<PERSON><PERSON><PERSON> di rose-torce", "item.minecraft.totem_of_undying": "Totem de imortalitâ<PERSON>", "item.minecraft.trader_llama_spawn_egg": "Ûf di nassite dal lama di marcjadant", "item.minecraft.trial_key": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.trident": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tropical_fish": "Pes t<PERSON>", "item.minecraft.tropical_fish_bucket": "Seglot cun pes tropicâl", "item.minecraft.tropical_fish_spawn_egg": "Ûf di nassite dal pes tropicâl", "item.minecraft.turtle_helmet": "Casc di copasse", "item.minecraft.turtle_scute": "Scus di copasse", "item.minecraft.turtle_spawn_egg": "Ûf di nassite de copasse", "item.minecraft.vex_armor_trim_smithing_template": "Model par fari", "item.minecraft.vex_armor_trim_smithing_template.new": "Ornament cun stîl vex", "item.minecraft.vex_spawn_egg": "Ûf di nassite dal vex", "item.minecraft.villager_spawn_egg": "Ûf di nassite dal paisan", "item.minecraft.vindicator_spawn_egg": "Ûf di nassite dal svindicadôr", "item.minecraft.wandering_trader_spawn_egg": "Ûf di nassite dal vendidôr ambulant", "item.minecraft.ward_armor_trim_smithing_template": "Model par fari", "item.minecraft.ward_armor_trim_smithing_template.new": "Ornament cun st<PERSON>l vuardie", "item.minecraft.warden_spawn_egg": "Ûf di nassite de vuardie", "item.minecraft.warped_fungus_on_a_stick": "Baston cun fonc disnaturât", "item.minecraft.water_bucket": "Se<PERSON><PERSON> di aghe", "item.minecraft.wayfinder_armor_trim_smithing_template": "Model par fari", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Ornament cun stîl esploradôr", "item.minecraft.wheat": "Forment", "item.minecraft.wheat_seeds": "Se<PERSON><PERSON> di forment", "item.minecraft.white_bundle": "Sacut blanc", "item.minecraft.white_dye": "<PERSON><PERSON> blan<PERSON>", "item.minecraft.white_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON> blan<PERSON>", "item.minecraft.wild_armor_trim_smithing_template": "Model par fari", "item.minecraft.wild_armor_trim_smithing_template.new": "Ornament cun stîl salvadi", "item.minecraft.wind_charge": "<PERSON><PERSON><PERSON>", "item.minecraft.witch_spawn_egg": "Ûf di nassite de strie", "item.minecraft.wither_skeleton_spawn_egg": "Ûf di nassite dal scheletri wither", "item.minecraft.wither_spawn_egg": "Ûf di nassite dal <PERSON>er", "item.minecraft.wolf_armor": "Arm<PERSON><PERSON> pal lôf", "item.minecraft.wolf_spawn_egg": "Ûf di nassite dal lôf", "item.minecraft.wooden_axe": "Man<PERSON><PERSON> di len", "item.minecraft.wooden_hoe": "<PERSON>pe di len", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON> di len", "item.minecraft.wooden_shovel": "Pale di len", "item.minecraft.wooden_sword": "Spade di len", "item.minecraft.writable_book": "Libri e pene", "item.minecraft.written_book": "Libri scrit", "item.minecraft.yellow_bundle": "<PERSON><PERSON>", "item.minecraft.yellow_dye": "Tinture zale", "item.minecraft.yellow_harness": "Imbragadure zale", "item.minecraft.zoglin_spawn_egg": "Ûf di nassite dal zoglin", "item.minecraft.zombie_horse_spawn_egg": "Ûf di nassite dal cjaval zombi", "item.minecraft.zombie_spawn_egg": "Ûf di nassite dal zombi", "item.minecraft.zombie_villager_spawn_egg": "Ûf di nassite dal paisan zombi", "item.minecraft.zombified_piglin_spawn_egg": "Ûf di nassite dal piglin zombificât", "item.modifiers.any": "<PERSON><PERSON><PERSON> sù:", "item.modifiers.armor": "<PERSON><PERSON><PERSON> sù:", "item.modifiers.body": "<PERSON><PERSON><PERSON> sù:", "item.modifiers.chest": "Sul pet:", "item.modifiers.feet": "Sui pîts:", "item.modifiers.hand": "Te man:", "item.modifiers.head": "Sul cjâf:", "item.modifiers.legs": "Su lis gjambis:", "item.modifiers.mainhand": "Te man principâl:", "item.modifiers.offhand": "Te man secondarie:", "item.modifiers.saddle": "<PERSON> sielât:", "item.nbt_tags": "NBT: %s etichete(-is)", "item.op_block_warning.line1": "Atenzion:", "item.op_block_warning.line2": "Chest ogjet al podarès eseguî comants", "item.op_block_warning.line3": "No sta doprâlu se no tu sâs a ce che al covente!", "item.unbreakable": "Indistrutibil", "itemGroup.buildingBlocks": "Blocs di costruzion", "itemGroup.coloredBlocks": "Blocs colorâ<PERSON>", "itemGroup.combat": "Combatiment", "itemGroup.consumables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.crafting": "Fabricazion", "itemGroup.foodAndDrink": "<PERSON>g<PERSON><PERSON> e bevi", "itemGroup.functional": "Blocs funzionai", "itemGroup.hotbar": "<PERSON><PERSON><PERSON> rapidis salvadis", "itemGroup.ingredients": "Ingredients", "itemGroup.inventory": "Inventari di sorevivence", "itemGroup.natural": "Blocs naturai", "itemGroup.op": "Utilitât pai operadôrs", "itemGroup.redstone": "Redstone", "itemGroup.search": "<PERSON><PERSON><PERSON>", "itemGroup.spawnEggs": "Ûfs di nassite", "itemGroup.tools": "Imprescj e utilitâts", "item_modifier.unknown": "Modificadôr di ogjets no cognossût: %s", "jigsaw_block.final_state": "Al devente:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "Inlineât", "jigsaw_block.joint.rollable": "R<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint_label": "Gje<PERSON> di zonte:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON> i berdeis", "jigsaw_block.levels": "Nivei: %s", "jigsaw_block.name": "Non:", "jigsaw_block.placement_priority": "Prioritât dal plaçament:", "jigsaw_block.placement_priority.tooltip": "Cuant che chest bloc berdei si conet a un toc, chest al è l'ordin doprât par elaborâ il toc pes conessions ae struture plui grande.\n\nI tocs e vignaran elaborâts cun prioritât in calâ, risolvint i câs di paritât in base al ordin di inseriment.", "jigsaw_block.pool": "Bacin di destinazion:", "jigsaw_block.selection_priority": "Prioritât di selezion:", "jigsaw_block.selection_priority.tooltip": "Cuant che il toc gjenitôr al ven elaborât pes conessions, chest al è l'ordin doprât dal bloc berdei pai tentatîfs di conession al so toc di destinazion.\n\nI berdeis e vignaran elaborâts cun prioritât in calâ, cun ordin casuâl par risolvi i câs di paritât.", "jigsaw_block.target": "Non di destinazion:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (carillon)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Progrès", "key.attack": "Atacâ/Spacâ", "key.back": "<PERSON><PERSON><PERSON><PERSON> in<PERSON>r", "key.categories.creative": "Modal<PERSON><PERSON>t creative", "key.categories.gameplay": "Azions di zûc", "key.categories.inventory": "Inventari", "key.categories.misc": "<PERSON><PERSON><PERSON>", "key.categories.movement": "Moviment", "key.categories.multiplayer": "Multi-zuiadôr", "key.categories.ui": "Interface dal z<PERSON>c", "key.chat": "Vierzi la chat", "key.command": "<PERSON><PERSON><PERSON> un comant", "key.drop": "<PERSON><PERSON><PERSON> l'ogjet selezionât", "key.forward": "Cjaminâ indenant", "key.fullscreen": "Comutâ mût a plen visôr", "key.hotbar.1": "Spazi 1 de sbare rapide", "key.hotbar.2": "Spazi 2 de sbare rapide", "key.hotbar.3": "Spazi 3 de sbare rapide", "key.hotbar.4": "Spazi 4 de sbare rapide", "key.hotbar.5": "Spazi 5 de sbare rapide", "key.hotbar.6": "Spazi 6 de sbare rapide", "key.hotbar.7": "Spazi 7 de sbare rapide", "key.hotbar.8": "Spazi 8 de sbare rapide", "key.hotbar.9": "Spazi 9 de sbare rapide", "key.inventory": "Vierzi/sierâ l'inventari", "key.jump": "Saltâ", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Cessâ/Backspace", "key.keyboard.caps.lock": "<PERSON>", "key.keyboard.comma": ",", "key.keyboard.delete": "Canc", "key.keyboard.down": "<PERSON><PERSON><PERSON> j<PERSON>", "key.keyboard.end": "Fin", "key.keyboard.enter": "Invie", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "<PERSON><PERSON><PERSON>", "key.keyboard.insert": "Inser<PERSON><PERSON>", "key.keyboard.keypad.0": "Num 0", "key.keyboard.keypad.1": "Num 1", "key.keyboard.keypad.2": "Num 2", "key.keyboard.keypad.3": "Num 3", "key.keyboard.keypad.4": "Num 4", "key.keyboard.keypad.5": "Num 5", "key.keyboard.keypad.6": "Num 6", "key.keyboard.keypad.7": "Num 7", "key.keyboard.keypad.8": "Num 8", "key.keyboard.keypad.9": "Num 9", "key.keyboard.keypad.add": "Num +", "key.keyboard.keypad.decimal": "Num decimâl", "key.keyboard.keypad.divide": "Num /", "key.keyboard.keypad.enter": "Num invie", "key.keyboard.keypad.equal": "Num =", "key.keyboard.keypad.multiply": "Num *", "key.keyboard.keypad.subtract": "Num -", "key.keyboard.left": "Frece a çampe", "key.keyboard.left.alt": "Alt di çampe", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Ctrl di çampe", "key.keyboard.left.shift": "<PERSON><PERSON><PERSON>", "key.keyboard.left.win": "<PERSON> di çampe", "key.keyboard.menu": "<PERSON>ù", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Bloc num", "key.keyboard.page.down": "<PERSON><PERSON><PERSON> jù", "key.keyboard.page.up": "<PERSON><PERSON><PERSON> s<PERSON>", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Stamp", "key.keyboard.right": "<PERSON>ece a diestre", "key.keyboard.right.alt": "Alt di diestre", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Ctrl di diestre", "key.keyboard.right.shift": "<PERSON><PERSON><PERSON> diestre", "key.keyboard.right.win": "<PERSON> di diestre", "key.keyboard.scroll.lock": "Bloc scor", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "No associât", "key.keyboard.up": "<PERSON><PERSON><PERSON>", "key.keyboard.world.1": "Mont 1", "key.keyboard.world.2": "Mont 2", "key.left": "Laterâl a çampe", "key.loadToolbarActivator": "Cjariâ une sbare rapide", "key.mouse": "Boton %1$s", "key.mouse.left": "Boton di çampe", "key.mouse.middle": "Boton centrâl", "key.mouse.right": "<PERSON><PERSON> di diestre", "key.pickItem": "Cjapâ il bloc", "key.playerlist": "Listâ i zuiadôrs", "key.quickActions": "Azions sveltis", "key.right": "Later<PERSON>l a drete", "key.saveToolbarActivator": "Salvâ une sbare rapide", "key.screenshot": "Cat<PERSON>â la videade", "key.smoothCamera": "Comutâ vision cinematiche", "key.sneak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.socialInteractions": "Videade interazions sociâls", "key.spectatorOutlines": "Evidenziâ i zuiadôrs (spetadôrs)", "key.sprint": "<PERSON><PERSON>", "key.swapOffhand": "Cambiâ di man", "key.togglePerspective": "Comutâ la prospetive", "key.use": "Doprâ un ogjet/Plaçâ un bloc", "known_server_link.announcements": "<PERSON><PERSON><PERSON><PERSON>", "known_server_link.community": "Comunitât", "known_server_link.community_guidelines": "<PERSON><PERSON><PERSON> vuide de comunitât", "known_server_link.feedback": "Coments", "known_server_link.forums": "Forum", "known_server_link.news": "Notiziis", "known_server_link.report_bug": "<PERSON><PERSON><PERSON> erôr di servidôr", "known_server_link.status": "<PERSON><PERSON><PERSON>", "known_server_link.support": "Assistence", "known_server_link.website": "Sît web", "lanServer.otherPlayers": "Impostazions pai altris zuia<PERSON>ôrs", "lanServer.port": "<PERSON><PERSON><PERSON>", "lanServer.port.invalid": "Puarte no valide.\nLasse vueit il ricuadri di modifiche opûr inserìs un numar diferent tal interval 1024 - 65535.", "lanServer.port.invalid.new": "Puarte no valide.\nLasse vueit il ricuadri di modifiche opûr inserìs un numar diferent tal interval %s - %s.", "lanServer.port.unavailable": "Puarte no disponibile.\nLasse vueit il ricuadri di modifiche opûr inserìs un numar diferent tal interval 1024 - 65535.", "lanServer.port.unavailable.new": "Puarte no disponibile.\nLasse vueit il ricuadri di modifiche opûr inserìs un numar diferent tal interval %s - %s.", "lanServer.scanning": "<PERSON><PERSON><PERSON> di partidis te rêt locâl", "lanServer.start": "Invie il mont in LAN", "lanServer.title": "Mont in LAN", "language.code": "fur_IT", "language.name": "<PERSON><PERSON><PERSON>", "language.region": "Friûl", "lectern.take_book": "Cjape il libri", "loading.progress": "%s%%", "mco.account.privacy.info": "Lei di plui su Mojang e lis leçs su la riservatece", "mco.account.privacy.info.button": "Lei di plui sul RGPD", "mco.account.privacy.information": "Mojang al implemente cualchi misure par judâ a protezi i fruts e la lôr riservatece, in conformitât de Children's Online Privacy Protection Act (COPPA) e dal regolament gjenerâl su la protezion dai dâts (RGPD).\n\nAl podarès coventâ il consens dai gjenitôrs par acedi al to account su Realms.", "mco.account.privacyinfo": "Mojang al implemente cualchi misure par judâ a protezi i fruts e la lôr riservatece, in conformitât de Children's Online Privacy Protection Act (COPPA) e dal regolament gjenerâl su la protezion dai dâts (RGPD).\n\nAl podarès coventâ il consens dai gjenitôrs par acedi al to account su Realms.\n\nSe tu âs un vecjo account di Minecraft (jentre doprant il to non utent), tu varâs di eseguî la migrazion a un account Mojang par podê doprâ Realms.", "mco.account.update": "Inzorne l'account", "mco.activity.noactivity": "Nissune ativitât tai ultins %s dîs", "mco.activity.title": "Ativitât dai zuiadôrs", "mco.backup.button.download": "Discjame l'ultin", "mco.backup.button.reset": "Torne inizialize il mont", "mco.backup.button.restore": "Ripristine", "mco.backup.button.upload": "Cjame il mont", "mco.backup.changes.tooltip": "Modifichis", "mco.backup.entry": "Copie di sigurece (%s)", "mco.backup.entry.description": "Descrizion", "mco.backup.entry.enabledPack": "Pac<PERSON><PERSON>(s) atîf(s)", "mco.backup.entry.gameDifficulty": "Dific<PERSON><PERSON><PERSON> dal <PERSON>c", "mco.backup.entry.gameMode": "Modalitât di zûc", "mco.backup.entry.gameServerVersion": "Version dal servidôr", "mco.backup.entry.name": "Non", "mco.backup.entry.seed": "Se<PERSON><PERSON>", "mco.backup.entry.templateName": "Non dal model", "mco.backup.entry.undefined": "Modifiche no definide", "mco.backup.entry.uploaded": "Cjamât in rêt", "mco.backup.entry.worldType": "<PERSON><PERSON><PERSON>t", "mco.backup.generate.world": "Gjenere il mont", "mco.backup.info.title": "Modifichis rispiet ae ultime copie di sigurece", "mco.backup.narration": "Copie di sigurece dal %s", "mco.backup.nobackups": "In chest moment chest Realm nol à nissune copie di sigurece.", "mco.backup.restoring": "Da<PERSON>r a ripristinâ il to Realm", "mco.backup.unknown": "NO COGNOSSÛT", "mco.brokenworld.download": "Discjame", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "Torne inizialize o selezione un altri mont.", "mco.brokenworld.message.line2": "Tu puedis ancje sielzi di discjariâ il mont in zuiadôr singul.", "mco.brokenworld.minigame.title": "Chest minizûc nol è plui supuartât", "mco.brokenworld.nonowner.error": "Spiete che il proprietari dal Realm al torni a inizializâ il mont", "mco.brokenworld.nonowner.title": "Il mont al è sorpassât", "mco.brokenworld.play": "<PERSON><PERSON><PERSON>", "mco.brokenworld.reset": "Torne inizialize", "mco.brokenworld.title": "Il to mont atuâl nol è plui supuartât", "mco.client.incompatible.msg.line1": "Il to client nol è compatibil cui Realms.", "mco.client.incompatible.msg.line2": "Dopre la version plui resinte di Minecraft.", "mco.client.incompatible.msg.line3": "Realms nol è compatibil cu lis version sperimentâls.", "mco.client.incompatible.title": "Client incompatibil!", "mco.client.outdated.stable.version": "La version dal client (%s) no je compatibile cun Realms.\n\nDopre la version plui resinte di Minecraft.", "mco.client.unsupported.snapshot.version": "La version dal client (%s) no je compatibile cun Realms.\n\nRealms nol è disponibil par cheste version sperimentâl.", "mco.compatibility.downgrade": "Torne indaûr di version", "mco.compatibility.downgrade.description": "Chest mont al è stât zuiât la ultime volte te version %s; tu tu stâs doprant la version %s. Il tornâ indaûr di version di un mont al pues causâ coruzions - no podìn garantî che si cjami o che al funzioni.\n\nE vignarà salvade une copie di sigurece in \"Copiis di sigurece dal mont\". Se al covente, ripristine il mont.", "mco.compatibility.incompatible.popup.title": "Version incompatibile", "mco.compatibility.incompatible.releaseType.popup.message": "Chest mont al è incompatibil cu la to version.", "mco.compatibility.incompatible.series.popup.message": "La ultime volte chest mont al è stât zuiât te version %s; tu tu sês te version %s.\n\nLis dôs versions no son compatibii. Par zuiâ in cheste version, cree un gnûf mont.", "mco.compatibility.unverifiable.message": "Nol è stât pussibil verificâ la ultime version di chest mont. Se tu lu inzornis o al torne indaûr di version, une copie di sigurece e vignarà salvade in automatic in \"Copiis di sigurece dal mont\".", "mco.compatibility.unverifiable.title": "Compatibilitât no verificabile", "mco.compatibility.upgrade": "Inzorne", "mco.compatibility.upgrade.description": "La ultime volte che chest mont al è stât zuiât al jere te version %s; tu tu stâs doprant la version %s.\n\nUne copie di sigurece e vignarà salvade in \"Copiis di sigurece dal mont\". Se al covente, ripristine il mont.", "mco.compatibility.upgrade.friend.description": "La ultime volte chest mont al è stât zuiât te version %s; tu stâs doprant la version %s.\n\nUne copie di sigurece e vignarà salvade in \"Copiis di sigurece dal mont\".\n\nIl proprietari dal Realm al podarà ripristinâ il mont.", "mco.compatibility.upgrade.title": "Inzornâ pardabon chest mont?", "mco.configure.current.minigame": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.activityfeed.disabled": "Feed des ativitâts dai zuiadôrs disabilitât in mût temporani", "mco.configure.world.backup": "Copiis di sigurece dal mont", "mco.configure.world.buttons.activity": "Ativitâts dai zuiadôrs", "mco.configure.world.buttons.close": "<PERSON>ere il Realm in mût temporani", "mco.configure.world.buttons.delete": "Elimine", "mco.configure.world.buttons.done": "Fat", "mco.configure.world.buttons.edit": "Impostazions", "mco.configure.world.buttons.invite": "Invide", "mco.configure.world.buttons.moreoptions": "Altris opzions", "mco.configure.world.buttons.newworld": "Gnûf mont", "mco.configure.world.buttons.open": "Torne vierç il Realm", "mco.configure.world.buttons.options": "Opzions dal mont", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Selezione une regjon...", "mco.configure.world.buttons.resetworld": "Torne inizialize il mont", "mco.configure.world.buttons.save": "Salve", "mco.configure.world.buttons.settings": "Impostazions", "mco.configure.world.buttons.subscription": "Abonament", "mco.configure.world.buttons.switchminigame": "<PERSON>bie minizûc", "mco.configure.world.close.question.line1": "Tu puedis sierâ il to Realm in mût temporani, impedint l'acès al zûc intant che tu fâsis modifichis. Torne vierzilu cuant che tu sês pront. \n\nChest nol anule il to abonament a Realms.", "mco.configure.world.close.question.line2": "Continuâ pardabon?", "mco.configure.world.close.question.title": "Âstu dibisugne di modificâ cence interuzions?", "mco.configure.world.closing": "Daûr a sierâ il Realm par un pôc di timp...", "mco.configure.world.commandBlocks": "Blocs comants", "mco.configure.world.delete.button": "Elimine il Realm", "mco.configure.world.delete.question.line1": "Il to Realm al vignarà eliminât par simpri", "mco.configure.world.delete.question.line2": "Continuâ pardabon?", "mco.configure.world.description": "Descrizion dal Realm", "mco.configure.world.edit.slot.name": "Non dal mont", "mco.configure.world.edit.subscreen.adventuremap": "Cualchi impostazion e je disabilitade viodût che il to mont corint al è une aventure", "mco.configure.world.edit.subscreen.experience": "Cualchi impostazion e je disabilitade viodût che il to mont corint al è une esperience", "mco.configure.world.edit.subscreen.inspiration": "Cualchi impostazion e je disabilitade viodût che il to mont corint al è une ispirazion", "mco.configure.world.forceGameMode": "Modalitât di zûc sfuarçade", "mco.configure.world.invite.narration": "Tu âs %s gnûf(s) invît(s)", "mco.configure.world.invite.profile.name": "Non", "mco.configure.world.invited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "<PERSON><PERSON><PERSON><PERSON> (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON><PERSON> norm<PERSON>l", "mco.configure.world.invites.ops.tooltip": "Operadôr", "mco.configure.world.invites.remove.tooltip": "Gjave", "mco.configure.world.leave.question.line1": "Se tu bandonis chest Realm no tu lu tornarâs a viodi fintremai che no tu vignarâs invidât di gnûf", "mco.configure.world.leave.question.line2": "Continuâ pardabon?", "mco.configure.world.loading": "Da<PERSON>r a cjariâ il Realm", "mco.configure.world.location": "Posizion", "mco.configure.world.minigame": "Atuâl: %s", "mco.configure.world.name": "Non dal Realm", "mco.configure.world.opening": "Daûr a vierzi il Realm...", "mco.configure.world.players.error": "No esistin zuiadôrs cul non indicât", "mco.configure.world.players.inviting": "Invît dal zu<PERSON>...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PvP", "mco.configure.world.region_preference": "<PERSON>jon preferide", "mco.configure.world.region_preference.title": "Selezion de regjon preferide", "mco.configure.world.reset.question.line1": "Il to mont al sarà rigjenerât e chel atuâl al larà pierdût", "mco.configure.world.reset.question.line2": "Continuâ pardabon?", "mco.configure.world.resourcepack.question": "Ti covente un pachet di risorsis personalizât par zuiâ su chest Realm.\n\nDesideristu discjariâlu e zuiâ?", "mco.configure.world.resourcepack.question.line1": "Ti covente un pachet di risorsis personalizât par zuiâ su chest Realm", "mco.configure.world.resourcepack.question.line2": "Desideristu discjariâlu e zuiâ?", "mco.configure.world.restore.download.question.line1": "Il mont al vignarà discjariât e zontât ai tiei monts in zuiadôr singul.", "mco.configure.world.restore.download.question.line2": "Continuâ?", "mco.configure.world.restore.question.line1": "Il to mont al vignarà ripristinât ae date '%s' (%s)", "mco.configure.world.restore.question.line2": "Continuâ pardabon?", "mco.configure.world.settings.expired": "No tu puedis modificâ lis impostazions di un Realm scjadût", "mco.configure.world.settings.title": "Impostazions", "mco.configure.world.slot": "Mont %s", "mco.configure.world.slot.empty": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "Il to Realm al vignarà cambiât a un altri mont", "mco.configure.world.slot.switch.question.line2": "Continuâ pardabon?", "mco.configure.world.slot.tooltip": "Passe al mont", "mco.configure.world.slot.tooltip.active": "<PERSON><PERSON>", "mco.configure.world.slot.tooltip.minigame": "Passe al minizûc", "mco.configure.world.spawnAnimals": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.spawnMonsters": "<PERSON><PERSON>", "mco.configure.world.spawnNPCs": "Paisans", "mco.configure.world.spawnProtection": "Protezion dal pont di nassite", "mco.configure.world.spawn_toggle.message": "Disativant cheste opzion, dutis lis entitâts di chel gjenar a vignaran eliminadis", "mco.configure.world.spawn_toggle.message.npc": "Disativant cheste opzion, dutis lis entitâts di chel gjenar, come i paisans, a vignaran eliminadis", "mco.configure.world.spawn_toggle.title": "Atenzion!", "mco.configure.world.status": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.day": "dì", "mco.configure.world.subscription.days": "d<PERSON><PERSON>", "mco.configure.world.subscription.expired": "Scjadût", "mco.configure.world.subscription.extend": "Sprolungje l'abonament", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON><PERSON> di un dì", "mco.configure.world.subscription.month": "mês", "mco.configure.world.subscription.months": "mês", "mco.configure.world.subscription.recurring.daysleft": "<PERSON><PERSON><PERSON><PERSON><PERSON> in automatic chi di", "mco.configure.world.subscription.recurring.info": "Lis modifichis fatis sul to abonament a Realms, come zont<PERSON> timp o disativâ lis faturis ricorintis, no saran efetivis fin ae prossime date di fature.", "mco.configure.world.subscription.remaining.days": "%1$s dì(dîs)", "mco.configure.world.subscription.remaining.months": "%1$s mês", "mco.configure.world.subscription.remaining.months.days": "%1$s mês, %2$s dì(dîs)", "mco.configure.world.subscription.start": "Date di inizi", "mco.configure.world.subscription.tab": "Abonament", "mco.configure.world.subscription.timeleft": "<PERSON><PERSON> restant", "mco.configure.world.subscription.title": "Il to abonament", "mco.configure.world.subscription.unknown": "No cognossût", "mco.configure.world.switch.slot": "<PERSON>ree il mont", "mco.configure.world.switch.slot.subtitle": "Chest mont al è vueit, siel<PERSON> cemût creâ il to mont", "mco.configure.world.title": "Configure il Realm:", "mco.configure.world.uninvite.player": "<PERSON><PERSON><PERSON> pardabon l'invît di '%s'?", "mco.configure.world.uninvite.question": "<PERSON><PERSON><PERSON> pardabon l'invît di", "mco.configure.worlds.title": "Monts", "mco.connect.authorizing": "Daûr a jentrâ...", "mco.connect.connecting": "Conession al Realm...", "mco.connect.failed": "Nol è stât pussibil conetisi al Realm", "mco.connect.region": "Regjion dal servidôr: %s", "mco.connect.success": "Fat", "mco.create.world": "<PERSON><PERSON>", "mco.create.world.error": "Tu scugnis inserî un non!", "mco.create.world.failed": "Nol è stât pussibil creâ il mont!", "mco.create.world.reset.title": "Daûr a creâ il mont...", "mco.create.world.skip": "Salte", "mco.create.world.subtitle": "Se tu lu desideris, selezione cuâl mont meti tal to gnûf Realm", "mco.create.world.wait": "Daûr a creâ il Realm...", "mco.download.cancelled": "<PERSON><PERSON><PERSON><PERSON>", "mco.download.confirmation.line1": "Il mont che tu stâs par discjariâ al è plui grant di %s", "mco.download.confirmation.line2": "No tu podarâs plui cjamâ chest mont sul to Realm", "mco.download.confirmation.oversized": "Il mont che tu stâs par discjariâ al è plui grant di %s\n\nNo tu podarâs plui cjamâ in rêt chest mont sul to Realm", "mco.download.done": "<PERSON><PERSON><PERSON><PERSON> comple<PERSON>", "mco.download.downloading": "<PERSON><PERSON><PERSON> a <PERSON>", "mco.download.extracting": "Daûr a tirâ fûr", "mco.download.failed": "<PERSON><PERSON><PERSON><PERSON> fal<PERSON>", "mco.download.percent": "%s %%", "mco.download.preparing": "Da<PERSON>r a prontâ il discjariament", "mco.download.resourcePack.fail": "Alc al è lât strucj dal discjariâ il pachet di risorsis!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Da<PERSON>r a discjariâ l'ultin mont", "mco.error.invalid.session.message": "Prove a tornâ a inviâ Minecraft", "mco.error.invalid.session.title": "Session no valide", "mco.errorMessage.6001": "<PERSON><PERSON> so<PERSON><PERSON><PERSON>", "mco.errorMessage.6002": "Tiermins dal servizi no acetâts", "mco.errorMessage.6003": "Rivâts al limit di discjariament", "mco.errorMessage.6004": "Rivâts al limit par cjamâ in rêt", "mco.errorMessage.6005": "Mont blocât", "mco.errorMessage.6006": "Il mont nol è inzornât", "mco.errorMessage.6007": "Utent in masse Realms", "mco.errorMessage.6008": "Non dal Realm no valit", "mco.errorMessage.6009": "Descrizion dal Realm no valide", "mco.errorMessage.connectionFailure": "Al è vignût fûr un erôr, torne prove plui indenant.", "mco.errorMessage.generic": "Al è vignût fûr un erôr: ", "mco.errorMessage.initialize.failed": "Nol è stât pussibil inizializâ il realm", "mco.errorMessage.noDetails": "No son stâts furn<PERSON>ts detais dal erôr", "mco.errorMessage.realmsService": "Al è vignût fûr un erôr (%s):", "mco.errorMessage.realmsService.configurationError": "Al è vignût fûr un erôr no previodût tal modificâ lis impostazions dal mont", "mco.errorMessage.realmsService.connectivity": "Impussibil conetisi a Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Impussibil controlâ la version compatibile, rispueste ricevude: %s", "mco.errorMessage.retry": "Torne prove la operazion", "mco.errorMessage.serviceBusy": "Realms al è ocupât al moment. Prove di gnûf a conetiti al to Realm ca di un pâr di minûts.", "mco.gui.button": "<PERSON><PERSON>", "mco.gui.ok": "<PERSON><PERSON> ben", "mco.info": "Informazions!", "mco.invited.player.narration": "Invidât il zuiadôr %s", "mco.invites.button.accept": "Acete", "mco.invites.button.reject": "Refude", "mco.invites.nopending": "<PERSON>ssun invît in spiete!", "mco.invites.pending": "Gnûf(s) invît(s)!", "mco.invites.title": "Invîts in spiete", "mco.minigame.world.changeButton": "Selezione un altri minizûc", "mco.minigame.world.info.line1": "Chest al sostituirà in mût temporani il to mont cuntun mini-zûc!", "mco.minigame.world.info.line2": "<PERSON> podarâs tornâ al to mont origjinâl cence pierdi nuie.", "mco.minigame.world.noSelection": "Fâs une sielte", "mco.minigame.world.restore": "Daûr a terminâ il minizûc...", "mco.minigame.world.restore.question.line1": "Il minizûc al terminarà e il to Realm al vignarà ripristinât.", "mco.minigame.world.restore.question.line2": "Continuâ pardabon?", "mco.minigame.world.selected": "Minizûc selezionât:", "mco.minigame.world.slot.screen.title": "Daûr a cambiâ il mont...", "mco.minigame.world.startButton": "<PERSON><PERSON>", "mco.minigame.world.starting.screen.title": "Daûr a inviâ il minizûc...", "mco.minigame.world.stopButton": "Termine il minizûc", "mco.minigame.world.switch.new": "Selezionâ un altri minizûc?", "mco.minigame.world.switch.title": "<PERSON>bie minizûc", "mco.minigame.world.title": "Passe suntun minizûc tal realm", "mco.news": "Gnovis su Realms", "mco.notification.dismiss": "<PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.message": "I abonaments ai Realms Java si stan spostant tal Microsoft Store. No sta lassâ scjadê il to abonament!\nTrasferìs daurman par vê 30 dîs di Realms a gratis. Va tal to profîl su minecraft.net par trasferî il to abonament.", "mco.notification.visitUrl.buttonText.default": "Vierç colegament", "mco.notification.visitUrl.message.default": "Visite il colegament chi sot", "mco.onlinePlayers": "Zuiadôrs in linie", "mco.play.button.realm.closed": "Il Realm al è sierât", "mco.question": "<PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.experience": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.generate": "Gnûf mont", "mco.reset.world.inspiration": "Ispirazion", "mco.reset.world.resetting.screen.title": "Rigjenerazion dal mont...", "mco.reset.world.seed": "Semence (facoltatîf)", "mco.reset.world.template": "Modei di mont", "mco.reset.world.title": "Torne inizialize il mont", "mco.reset.world.upload": "Cjame un mont", "mco.reset.world.warning": "Chest al sostituirà il mont corint dal to Realm", "mco.selectServer.buy": "Compre un realm!", "mco.selectServer.close": "<PERSON><PERSON>", "mco.selectServer.closed": "Realm disativât", "mco.selectServer.closeserver": "Siere il Realm", "mco.selectServer.configure": "Configure", "mco.selectServer.configureRealm": "Configure il Realm", "mco.selectServer.create": "Cree un Realm", "mco.selectServer.create.subtitle": "Selezione cuâl mont meti tal to gnûf Realm", "mco.selectServer.expired": "Realm scjadût", "mco.selectServer.expiredList": "Il to abonament al è scjadût", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Aboniti", "mco.selectServer.expiredTrial": "La prove e je finide", "mco.selectServer.expires.day": "Al scjât chi di un dì", "mco.selectServer.expires.days": "Al scjât chi di %s dîs", "mco.selectServer.expires.soon": "Al scjât chi di pôc", "mco.selectServer.leave": "<PERSON><PERSON>", "mco.selectServer.loading": "<PERSON><PERSON><PERSON><PERSON> da liste dai Realms", "mco.selectServer.mapOnlySupportedForVersion": "Cheste mape no je supuartade in %s", "mco.selectServer.minigame": "Minizûc:", "mco.selectServer.minigameName": "Minizûc: %s", "mco.selectServer.minigameNotSupportedInVersion": "Impussibil zuiâ a chest minizûc in %s", "mco.selectServer.noRealms": "Al pâr che no tu vedis nissun Realm. Zonte un Realm par zuiâ cui tiei amîs.", "mco.selectServer.note": "Note:", "mco.selectServer.open": "<PERSON> viert", "mco.selectServer.openserver": "Vierç il Realm", "mco.selectServer.play": "<PERSON><PERSON><PERSON>", "mco.selectServer.popup": "Realms al è un mût sempliç e sigûr par gjoldi di un mont di Minecraft in rêt cun fin a dîs amîs par volte. Al supuarte tancj minizûcs e une vore di monts personalizâts! Dome il proprietari dal realm al à di paiâ.", "mco.selectServer.purchase": "Zonte Realm", "mco.selectServer.trial": "Fâs une prove!", "mco.selectServer.uninitialized": "Fâs clic par inviâ il to gnûf Realm!", "mco.snapshot.createSnapshotPopup.text": "Tu stâs par creâ un Realm gratuit intune version sperimentâl che al sarà associât al to abonament a Realms. Tu puedis zuiâ in chest gnûf Realm dome se il to abonament al è atîf. Il to Realm paiât nol sarà tocjât.", "mco.snapshot.createSnapshotPopup.title": "Creâ un Realm intune version sperimentâl?", "mco.snapshot.creating": "Creazion di un Realm intune version sperimentâl...", "mco.snapshot.description": "Associât cun \"%s\"", "mco.snapshot.friendsRealm.downgrade": "Ti covente la version %s par jentrâ in chest Realm", "mco.snapshot.friendsRealm.upgrade": "%s al/e à di inzornâ il so Realm prime che tu puedis zuiâ in cheste version", "mco.snapshot.paired": "Chest Realm sperimentâl al è associât cun \"%s\"", "mco.snapshot.parent.tooltip": "Dopre la ultime version di Minecraft par zuiâ in chest Realm", "mco.snapshot.start": "Invie un Realm sperimentâl gratuit", "mco.snapshot.subscription.info": "Chest al è un Realm intune version sperimentâl associât al to abonament pal Realm '%s'. Al reste atîf dome fintant che il Realm associât al è atîf.", "mco.snapshot.tooltip": "Dopre i Realms sperimentâi par viodi une anteprime des prossimis versions di Minecraft, che a podaressin includi gnovis funzionalitâts e cambiaments.\n\nTu cjatis i tiei Realms normâi te version uficiâl dal zûc.", "mco.snapshotRealmsPopup.message": "Cu la version 23w41a, i Realms a saran disponibii ancje tes versions sperimentâls. Ducj i abonaments ai Realms a includin un Realm pes versions sperimentâls, separât dal to Realm Java normâl!", "mco.snapshotRealmsPopup.title": "I Realms a son cumò disponibii tes versions sperimentâls", "mco.snapshotRealmsPopup.urlText": "Scuvierç di plui", "mco.template.button.publisher": "Autôr", "mco.template.button.select": "Selezione", "mco.template.button.trailer": "Trailer", "mco.template.default.name": "<PERSON> di mont", "mco.template.info.tooltip": "<PERSON><PERSON><PERSON> dal aut<PERSON>r", "mco.template.name": "Model", "mco.template.select.failure": "Impussibil recuperâ la liste dai contignûts di cheste categorie.\nControle la to conession a internet, opûr torne prove plui indenant.", "mco.template.select.narrate.authors": "Autôrs: %s", "mco.template.select.narrate.version": "version %s", "mco.template.select.none": "Orpo! al somee che in chest moment cheste categorie di contignûts e sedi vueide.\nTorne controle plui indenant par gnûfs contignûts o se tu sês un creatôr,\n%s.", "mco.template.select.none.linkTitle": "valute la idee di inviâ alc che tu âs fat tu", "mco.template.title": "Modei di mont", "mco.template.title.minigame": "Minizûcs", "mco.template.trailer.tooltip": "Panoramiche de mape", "mco.terms.buttons.agree": "O aceti", "mco.terms.buttons.disagree": "No aceti", "mco.terms.sentence.1": "O aceti i", "mco.terms.sentence.2": "tiermins dal servizi di Minecraft Realms", "mco.terms.title": "Condizions di ûs di Realms", "mco.time.daysAgo": "%1$s dì(dîs) indaûr", "mco.time.hoursAgo": "%1$s ore(is) indaûr", "mco.time.minutesAgo": "%1$s minût(s) indaûr", "mco.time.now": "<PERSON><PERSON>", "mco.time.secondsAgo": "%1$s secont(s) indaûr", "mco.trial.message.line1": "Desideristu otignî il to Realm?", "mco.trial.message.line2": "Fâs clic achì par vê plui informazions!", "mco.upload.button.name": "Cjame in rêt", "mco.upload.cancelled": "<PERSON><PERSON><PERSON><PERSON>", "mco.upload.close.failure": "Impussibil sierâ il <PERSON>, torne prove plui indenant", "mco.upload.done": "<PERSON><PERSON><PERSON><PERSON> comple<PERSON>", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "<PERSON><PERSON><PERSON>ent falît! (%s)", "mco.upload.failed.too_big.description": "Chest mont al è masse grant. La dimension massime ametude e je %s.", "mco.upload.failed.too_big.title": "Mont masse grant", "mco.upload.hardcore": "Nol è pussibil mandâ i monts in modalitât impegnative!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Daûr a prontâ il to mont", "mco.upload.select.world.none": "No son stâts cjatât monts in zuiadôr singul!", "mco.upload.select.world.subtitle": "Selezione un mont in zuiadôr singul di cjamâ in rêt", "mco.upload.select.world.title": "Cjame il mont", "mco.upload.size.failure.line1": "'%s' al è masse grant!", "mco.upload.size.failure.line2": "Al ocupe %s. La dimension massime ametude e je %s.", "mco.upload.uploading": "Daûr a mandâ '%s'", "mco.upload.verifying": "Daûr a verificâ il to mont", "mco.version": "Version: %s", "mco.warning": "Atenzion!", "mco.worldSlot.minigame": "Minizûc", "menu.custom_options": "Opzions personalizadis...", "menu.custom_options.title": "Opzions personalizadis", "menu.custom_options.tooltip": "Note: lis opzions personalizadis a son furnidis di servidôrs o contignûts di tierçs.\nFâs a ments!", "menu.custom_screen_info.button_narration": "Cheste e je une videade personalizade. Scuvierç di plui.", "menu.custom_screen_info.contents": "I contignûts di cheste videade a son controlâts di servidôrs o mapis di tierçs che no son supervisionâts, gjestîts o di proprietât di Mojang Studios o Microsoft.\n\nAtenzion! Sta simpri atent tal lâ daûr a colegaments e ten par te lis tos informazions privadis, includûts i dâts di acés.\n\nSe cheste videade no ti permèt di zuiâ, tu ti puedis disconeti dal servidôr doprant il boton chi sot.", "menu.custom_screen_info.disconnect": "Videade personalizade refudade", "menu.custom_screen_info.title": "Note su lis schermadis personalizadis", "menu.custom_screen_info.tooltip": "Cheste e je une videade personalizade. Fâs clic achì par scuvierzi di plui.", "menu.disconnect": "Disconetiti", "menu.feedback": "Coments...", "menu.feedback.title": "Coments", "menu.game": "<PERSON>ù <PERSON>û<PERSON>", "menu.modded": " (Modificât)", "menu.multiplayer": "<PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "Opzions...", "menu.paused": "<PERSON><PERSON><PERSON> in pause", "menu.playdemo": "<PERSON><PERSON>e tal mont di dimostrazion", "menu.playerReporting": "<PERSON><PERSON><PERSON>", "menu.preparingSpawn": "Preparazion de aree di gjenerazion: %s%%", "menu.quick_actions": "Azions sveltis...", "menu.quick_actions.title": "Azions sveltis", "menu.quit": "<PERSON><PERSON> dal <PERSON>c", "menu.reportBugs": "<PERSON><PERSON><PERSON>", "menu.resetdemo": "Ripristine il mont di dimostrazion", "menu.returnToGame": "Torne al zûc", "menu.returnToMenu": "Salve e jes al menù principâl", "menu.savingChunks": "Daûr a salvâ i tocs", "menu.savingLevel": "Daûr a salvâ il mont", "menu.sendFeedback": "Mande il to parê", "menu.server_links": "Colegaments dal servidôr...", "menu.server_links.title": "Colegaments dal servidôr", "menu.shareToLan": "Vierç ae LAN", "menu.singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.working": "In vore...", "merchant.deprecated": "I paisans si rifurnissin fin a dôs voltis par dì.", "merchant.level.1": "<PERSON><PERSON>", "merchant.level.2": "<PERSON><PERSON><PERSON>", "merchant.level.3": "<PERSON><PERSON>", "merchant.level.4": "<PERSON><PERSON><PERSON>", "merchant.level.5": "<PERSON><PERSON><PERSON>", "merchant.title": "%s - %s", "merchant.trades": "Scambis", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Frache %1$s par dismontâ", "multiplayer.applyingPack": "Aplicazion dal pachet di risorsis", "multiplayer.confirm_command.parse_errors": "Tu stâs provant a eseguî un comant invalid o no ricognossût.\nSêstu sigûr?\nComant: %s", "multiplayer.confirm_command.permissions_required": "Tu stâs provant a eseguî un comant che al domande permès elevâts.\nChest al podarès influençâ in mût negatîf il to zûc.\nContinuâ pardabon?\nComant: %s", "multiplayer.confirm_command.title": "Conferme la esecuzion dal comant", "multiplayer.disconnect.authservers_down": "I servidôrs di autenticazion no funzionin. Torne prove plui indenant!", "multiplayer.disconnect.bad_chat_index": "Ricevût un messaç de chat mancjant o reordenât dal servidôr", "multiplayer.disconnect.banned": "Tu sês stât bandît di chest servidôr", "multiplayer.disconnect.banned.expiration": "\nLa tô interdizion e scjât ai %s", "multiplayer.disconnect.banned.reason": "Tu sês stât bandît di chest servidôr.\nMotîf: %s", "multiplayer.disconnect.banned_ip.expiration": "\nIl to bant al scjât ai %s", "multiplayer.disconnect.banned_ip.reason": "La tô direzion IP e je bandide di chest servidôr.\nMotîf: %s", "multiplayer.disconnect.chat_validation_failed": "Erôr di validazion dal messaç de chat", "multiplayer.disconnect.duplicate_login": "Acès eseguît di une altre postazion", "multiplayer.disconnect.expired_public_key": "La clâf publiche dal to profîl e je scjadude. Controle che la ore dal to sisteme e sedi sincronizade e prove a tornâ a inviâ il zûc.", "multiplayer.disconnect.flying": "Su chest servidôr il svolâ nol è autorizât", "multiplayer.disconnect.generic": "Disconetût/ude", "multiplayer.disconnect.idling": "Inativitât par masse timp!", "multiplayer.disconnect.illegal_characters": "Caratars interdets te chat", "multiplayer.disconnect.incompatible": "Client incompatibil! Par plasê, dopre %s", "multiplayer.disconnect.invalid_entity_attacked": "Tentatîf di atacâ une entitât no valide", "multiplayer.disconnect.invalid_packet": "Il servidôr al à inviât un pachet no valit", "multiplayer.disconnect.invalid_player_data": "<PERSON><PERSON><PERSON> dal zuiadôr no valits", "multiplayer.disconnect.invalid_player_movement": "Ricev<PERSON>t pachet di spostament dal zuiadôr no valit", "multiplayer.disconnect.invalid_public_key_signature": "Firme de clâf publiche dal to profîl no valide.\nProve a tornâ a inviâ il zûc.", "multiplayer.disconnect.invalid_public_key_signature.new": "Firme de clâf publiche dal to profîl no valide.\nProve a tornâ a inviâ il zûc.", "multiplayer.disconnect.invalid_vehicle_movement": "<PERSON><PERSON><PERSON>t pachet di spostament dal veicul no valit", "multiplayer.disconnect.ip_banned": "Il to IP al è stât bandît di chest servidôr", "multiplayer.disconnect.kicked": "<PERSON><PERSON><PERSON><PERSON> fûr di un operadôr", "multiplayer.disconnect.missing_tags": "Ricevude dal servidôr une schirie di etichetis incompletis.\nPar plasê contate l'operadôr dal servidôr.", "multiplayer.disconnect.name_taken": "Chel non al è za stât cjolt di cualchidun", "multiplayer.disconnect.not_whitelisted": "Su chest servidôr no tu sês te liste blancje!", "multiplayer.disconnect.out_of_order_chat": "Ricevût un pachet de chat no funzionant. Ise stade modificade la ore dal to sisteme?", "multiplayer.disconnect.outdated_client": "Client incompatibil! Par plasê, dopre %s", "multiplayer.disconnect.outdated_server": "Client incompatibil! Par plasê, dopre %s", "multiplayer.disconnect.server_full": "Il servidôr al è plen!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.disconnect.slow_login": "Masse timp par jentrâ", "multiplayer.disconnect.too_many_pending_chats": "Masse messaçs de chat no ricognossûts", "multiplayer.disconnect.transfers_disabled": "Il servidôr nol acete trasferiments", "multiplayer.disconnect.unexpected_query_response": "Dâts personalizâts inspietâts dal client", "multiplayer.disconnect.unsigned_chat": "Ricevût un pachet de chat cence firme o cun firme no valide.", "multiplayer.disconnect.unverified_username": "Nol è stât pussibil verificâ il non-utent!", "multiplayer.downloadingStats": "Recupar des statistichis...", "multiplayer.downloadingTerrain": "<PERSON><PERSON><PERSON><PERSON> dal teren...", "multiplayer.lan.server_found": "Cja<PERSON>ât gnûf servidôr: %s", "multiplayer.message_not_delivered": "Impussibil consegnâ il messaç de chat, controle i regjistris dal servidôr: %s", "multiplayer.player.joined": "%s si unìs ae partide", "multiplayer.player.joined.renamed": "%s (in passât cul non %s) si unìs ae partide", "multiplayer.player.left": "Maman a %s (partide bandonade)", "multiplayer.player.list.hp": "%sPS", "multiplayer.player.list.narration": "Zuiadôrs in linie: %s", "multiplayer.requiredTexturePrompt.disconnect": "Il servidôr al domande un pachet di risorsis personalizât", "multiplayer.requiredTexturePrompt.line1": "Chest servidôr al domande che si dopri un pachet di risorsis personalizât.", "multiplayer.requiredTexturePrompt.line2": "Refudant chest pachet di risorsis personalizât tu ti disconetarâs di chest servidôr.", "multiplayer.socialInteractions.not_available": "Lis interazions sociâls a son disponibilis dome tai monts multi-zuiadôr", "multiplayer.status.and_more": "... e altris %s ...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.cannot_connect": "Impussibil conetisi al servidôr", "multiplayer.status.cannot_resolve": "Impussibil risolvi il non dal host", "multiplayer.status.finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Version incompatibile!", "multiplayer.status.motd.narration": "Messaç dal dì: %s", "multiplayer.status.no_connection": "(nissune conession)", "multiplayer.status.old": "No inzornât", "multiplayer.status.online": "In linie", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s milliseconts", "multiplayer.status.pinging": "Daûr a eseguî il ping...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s di %s zuiadôrs in linie", "multiplayer.status.quitting": "<PERSON><PERSON> in cors", "multiplayer.status.request_handled": "La richieste di stât e je stade tratade", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Ricevût stât no domandât", "multiplayer.status.version.narration": "Version di servidôr: %s", "multiplayer.stopSleeping": "<PERSON><PERSON>", "multiplayer.texturePrompt.failure.line1": "Impussibil aplicâ il pachet di risorsis dal servidôr", "multiplayer.texturePrompt.failure.line2": "Cualsisei funzionalitât che e domande risorsis personalizadis e podarès no funzionâ come che al jere previodût", "multiplayer.texturePrompt.line1": "Chest servidôr al consee di doprâ un pachet di risorsis personalizât.", "multiplayer.texturePrompt.line2": "Discjariâlu e instalâlu in maniere automagjiche?", "multiplayer.texturePrompt.serverPrompt": "%s\n\n<PERSON><PERSON>ç dal servidôr:\n%s", "multiplayer.title": "<PERSON><PERSON><PERSON> in multi-zuiadôr", "multiplayer.unsecureserver.toast": "I messaçs inviâts su chest servidôr a puedin sei modificâts e a podaressin no rifleti il messaç origjinâl", "multiplayer.unsecureserver.toast.title": "Impussibil verificâ i messaçs de chat", "multiplayerWarning.check": "No sta mostrâ plui cheste videade", "multiplayerWarning.header": "Cautele: partide in rêt di tiercis parts", "multiplayerWarning.message": "Cautele: lis partidis in rêt a son ufiertis di servidôrs di tierçs, che no son supervisionâts, gjest<PERSON>ts o di proprietât di Mojang Studios o Microsoft. Dilunc lis partidis in rêt tu podaressis jessi esponût a messaçs di chat che no son moderâts o altris gjenars di contignûts gjenerâts dai utents che no son adats a ducj.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> On <PERSON>", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygene", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Boton: %s", "narration.button.usage.focused": "<PERSON><PERSON> par ativâ", "narration.button.usage.hovered": "Clic di çampe par ativâ", "narration.checkbox": "Casele di selezion: %s", "narration.checkbox.usage.focused": "<PERSON><PERSON> par comutâ", "narration.checkbox.usage.hovered": "Clic di çampe par comutâ", "narration.component_list.usage": "<PERSON><PERSON> par lâ al element sucessîf", "narration.cycle_button.usage.focused": "<PERSON><PERSON> par passâ a %s", "narration.cycle_button.usage.hovered": "Clic di çampe par passâ a %s", "narration.edit_box": "Ricuadri di modifiche: %s", "narration.item": "Ogjet: %s", "narration.recipe": "Ricete pâr %s", "narration.recipe.usage": "Clic di çampe par selezionâ", "narration.recipe.usage.more": "Clic di diestre par mostrâ plui ricetis", "narration.selection.usage": "<PERSON><PERSON> i botons sù e jù par spostâti tra lis vôs", "narration.slider.usage.focused": "Frache te tastiere i botons frece di çampe e di diestre par cambiâ il valôr", "narration.slider.usage.hovered": "Strissine il cursôr par cambiâ valôr", "narration.suggestion": "Tu âs selezionât il sugjeriment %s di %s: %s", "narration.suggestion.tooltip": "Tu âs selezionât il sugjeriment %s di %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "<PERSON><PERSON> par passâ al prossim sugjeriment", "narration.suggestion.usage.cycle.hidable": "Frache Tab par passâ al prossim sugjeriment o Esc par bandonâ i sugjeriments", "narration.suggestion.usage.fill.fixed": "<PERSON><PERSON> Tab par doprâ il sugjeriment", "narration.suggestion.usage.fill.hidable": "Frache Tab par doprâ il sugjeriment o Esc par bandonâ i sugjeriments", "narration.tab_navigation.usage": "<PERSON><PERSON> e Tab par cambiâ schede", "narrator.button.accessibility": "<PERSON><PERSON><PERSON> facili<PERSON>", "narrator.button.difficulty_lock": "Bloc de dificoltât", "narrator.button.difficulty_lock.locked": "Blocade", "narrator.button.difficulty_lock.unlocked": "Sblocade", "narrator.button.language": "<PERSON><PERSON><PERSON>", "narrator.controls.bound": "La azion %s e je associade al %s", "narrator.controls.reset": "Ripristine il boton pe azion %s", "narrator.controls.unbound": "La azion %s no je associade", "narrator.joining": "Daûr a jentrâ", "narrator.loading": "Daûr a cjariâ: %s", "narrator.loading.done": "Fat", "narrator.position.list": "Selezionade la rie %s di %s de liste", "narrator.position.object_list": "Selezionât l'element %s di %s de rie", "narrator.position.screen": "Element dal schermi %s di %s", "narrator.position.tab": "Selezionade la schede %s di %s", "narrator.ready_to_play": "Pront par zuiâ", "narrator.screen.title": "<PERSON><PERSON>", "narrator.screen.usage": "Dopre il cursôr dal mouse o il tast Tab par selezionâ un element", "narrator.select": "Selezionât: %s", "narrator.select.world": "Selezionât %s, ultime volte doprât: %s, %s, %s, version: %s", "narrator.select.world_info": "Selezionât %s, ultime volte zuiât: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON><PERSON><PERSON>", "narrator.toast.enabled": "<PERSON><PERSON><PERSON><PERSON>", "optimizeWorld.confirm.description": "Chest al cirarà di otimizâ il to mont fasint in mût che ducj i dâts a sedin memorizâts tal plui resint formât di zûc. In base al mont, cheste operazion e podarès tirâle a dilunc. Une volte completade, tu podaressis vê un mont plui svelt ma nol sarà plui compatibil cu lis version vecjis dal zûc. Continuâ?", "optimizeWorld.confirm.proceed": "Cree une copie di sigurece e otimize", "optimizeWorld.confirm.title": "Otimize il mont", "optimizeWorld.info.converted": "Tocs miorâts: %s", "optimizeWorld.info.skipped": "Tocs ignorâts: %s", "optimizeWorld.info.total": "Tocs in totâl: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Conte dai tocs...", "optimizeWorld.stage.failed": "Lade strucje! :(", "optimizeWorld.stage.finished": "Daûr a completâ...", "optimizeWorld.stage.finished.chunks": "Daûr a completâ l'inzornament dai tocs...", "optimizeWorld.stage.finished.entities": "Daûr a completâ l'inzornament des entitâts...", "optimizeWorld.stage.finished.poi": "Daûr a completâ l'inzornament dai ponts di interès...", "optimizeWorld.stage.upgrading": "Inzornament di ducj i tocs...", "optimizeWorld.stage.upgrading.chunks": "Inzornament di ducj i tocs...", "optimizeWorld.stage.upgrading.entities": "Inzornament di dutis lis entitâts...", "optimizeWorld.stage.upgrading.poi": "Inzornament di ducj i ponts di interès...", "optimizeWorld.title": "Otimizazion dal mont '%s'", "options.accessibility": "Impostazions acès facilitât...", "options.accessibility.high_contrast": "Alt contrast", "options.accessibility.high_contrast.error.tooltip": "Il pachet di risorsis cun alt contrast nol è disponibil.", "options.accessibility.high_contrast.tooltip": "Al fâs cressi il contrast tra i elements de interface utent.", "options.accessibility.high_contrast_block_outline": "Contors evidents", "options.accessibility.high_contrast_block_outline.tooltip": "Al fâs cressi il contrast dal contor dal bloc pontât.", "options.accessibility.link": "<PERSON><PERSON>e pal acès facilit<PERSON>t", "options.accessibility.menu_background_blurriness": "Metude a fûc dal fonts dal menù", "options.accessibility.menu_background_blurriness.tooltip": "Modifiche la metude a fûc dal fonts dal menù.", "options.accessibility.narrator_hotkey": "Tast di sielte rapide dal naratôr", "options.accessibility.narrator_hotkey.mac.tooltip": "Permet di comutâ il naratôr fracant 'Cmd+B'.", "options.accessibility.narrator_hotkey.tooltip": "Permet di comutâ il naratôr fracant 'Ctrl+B'.", "options.accessibility.panorama_speed": "Velocitât di scoriment dal panorame", "options.accessibility.text_background": "Fonts dal test", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "<PERSON><PERSON><PERSON>", "options.accessibility.text_background_opacity": "Opacitât fonts dal test", "options.accessibility.title": "Impostazions acès facilitât", "options.allowServerListing": "Permet il listâ sui servidôrs", "options.allowServerListing.tooltip": "I servidôrs a puedin listâ i zuiadôrs in linie tant che part dal propri stât public. Disativant cheste opzion il to non nol vignarà fûr in chês listis.", "options.ao": "<PERSON><PERSON><PERSON> fofe", "options.ao.max": "Massime", "options.ao.min": "<PERSON><PERSON>", "options.ao.off": "No", "options.attack.crosshair": "<PERSON><PERSON><PERSON>", "options.attack.hotbar": "Sbare rapide", "options.attackIndicator": "Indicadôr di atac", "options.audioDevice": "Dispositîf", "options.audioDevice.default": "Predefinît di sisteme", "options.autoJump": "Salt automatic", "options.autoSuggestCommands": "Sugjeriments dai comants", "options.autosaveIndicator": "Indicadôr di salvament automatic", "options.biomeBlendRadius": "Misture di terens", "options.biomeBlendRadius.1": "No (la plui rapide)", "options.biomeBlendRadius.11": "11x11 (esagjerade)", "options.biomeBlendRadius.13": "13x13 (spandone)", "options.biomeBlendRadius.15": "15x15 (massime)", "options.biomeBlendRadius.3": "3x3 (rapide)", "options.biomeBlendRadius.5": "5x5 (<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.7": "7x7 (la<PERSON><PERSON>)", "options.biomeBlendRadius.9": "9x9 (une vore largje)", "options.chat": "Impostazions chat...", "options.chat.color": "Colôrs", "options.chat.delay": "Ritart chat: %s secont(s)", "options.chat.delay_none": "Ritart chat: <PERSON><PERSON><PERSON>", "options.chat.height.focused": "Altece test focalizât", "options.chat.height.unfocused": "Altece test no focalizât", "options.chat.line_spacing": "Spaziadure riis", "options.chat.links": "Colegaments Web", "options.chat.links.prompt": "Domande sui colegaments", "options.chat.opacity": "Opacitât test de chat", "options.chat.scale": "Dimension test de chat", "options.chat.title": "Impostazions chat", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "Visibile", "options.chat.visibility.hidden": "Platade", "options.chat.visibility.system": "Dome comants", "options.chat.width": "Largjece", "options.chunks": "%s tocs", "options.clouds.fancy": "<PERSON><PERSON><PERSON>", "options.clouds.fast": "<PERSON><PERSON><PERSON><PERSON>", "options.controls": "Controi...", "options.credits_and_attribution": "Ricognossiments e atribuzion...", "options.damageTiltStrength": "Pen<PERSON><PERSON>ent al dam", "options.damageTiltStrength.tooltip": "Trop che la videade si môf cuant che tu cjapis dam.", "options.darkMojangStudiosBackgroundColor": "Logo monocromatic", "options.darkMojangStudiosBackgroundColor.tooltip": "Al cambie a neri il colôr dal fonts de videade di cjariament di Mojang Studios.", "options.darknessEffectScale": "Pulsazion de scuretât", "options.darknessEffectScale.tooltip": "Al controle trop che al pulse l'efiet di scuretât cuant che une vuardie o un Sculk zigon tal cause.", "options.difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.easy": "Facile", "options.difficulty.easy.info": "I mostris si gjenerin ma a fasin mancul dam. La sbare de fam si esaurìs e e sbasse la salût fin a 5 cûrs.", "options.difficulty.hard": "Dificile", "options.difficulty.hard.info": "I mostris si gjenerin e a fasin plui dam. La sbare de fam si esaurìs e e scunìs la salût dal dut.", "options.difficulty.hardcore": "Impegnative", "options.difficulty.normal": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.normal.info": "I mostris si gjenerin e a fasin dams normâi. La sbare de fam si esaurìs e e sbasse la salût fin a mieç cûr.", "options.difficulty.online": "<PERSON><PERSON><PERSON><PERSON><PERSON> dal servid<PERSON>r", "options.difficulty.peaceful": "<PERSON><PERSON>", "options.difficulty.peaceful.info": "Nol gjenere mostris e cualchi creature neutrâl. La sbare de fam no si esaurìs e la salût si ripristine cul timp a lâ.", "options.directionalAudio": "Audio direzionâl", "options.directionalAudio.off.tooltip": "Sun stereo classic.", "options.directionalAudio.on.tooltip": "Al dopre l'audio direzionâl basât su HRTF par miorâ la simulazion dal sun 3D. Al covente un machinari audio compatibil cun HRTF e al funzione miôr cu lis scufis.", "options.discrete_mouse_scroll": "Scoriment discret", "options.entityDistanceScaling": "Distance des entitâts", "options.entityShadows": "Ombrenis des entitâts", "options.font": "Impostazions caratar...", "options.font.title": "Impostazions caratar", "options.forceUnicodeFont": "Sfuarce caratar unicode", "options.fov": "Amplece visive", "options.fov.max": "Quake Pro", "options.fov.min": "<PERSON><PERSON><PERSON><PERSON>", "options.fovEffectScale": "Efiets de amplece visive", "options.fovEffectScale.tooltip": "Al controle trop che la amplece visive e pues cambiâ cui efiets dal zûc.", "options.framerate": "%s fps", "options.framerateLimit": "FPS massims", "options.framerateLimit.max": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.fullscreen": "Plen visôr", "options.fullscreen.current": "<PERSON><PERSON><PERSON><PERSON>", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "Risoluzion a plen visôr", "options.fullscreen.unavailable": "Impostazion no disponibile", "options.gamma": "Luminositât", "options.gamma.default": "Predefinît", "options.gamma.max": "<PERSON><PERSON><PERSON>", "options.gamma.min": "<PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Velocitât dal lusarili", "options.glintSpeed.tooltip": "Al controle trop svelt che il lusarili al slusiche sui ogjets incjantâts.", "options.glintStrength": "Intensit<PERSON>t dal lusarili", "options.glintStrength.tooltip": "Al controle la trasparence dal lusarili sui ogjets incjantâts.", "options.graphics": "<PERSON><PERSON>", "options.graphics.fabulous": "Maraveose!", "options.graphics.fabulous.tooltip": "La grafiche di %s e dopre lis ombrenaduris (shaders) par dissegnâ il timp meteorologjic, i nûi, lis particelis daûr dai blocs translucits e la aghe.\nChest al pues impatâ no pôc lis prestazions sui dispositîfs portatii e i visôrs 4K.", "options.graphics.fancy": "Detaiade", "options.graphics.fancy.tooltip": "La grafiche detaiade e belance prestazions e cualitât pe plui part des machinis.\n<PERSON><PERSON> meteorologjic, nûi e particelis a podaressin no comparî daûr dai blocs traslucits o de aghe.", "options.graphics.fast": "Svelte", "options.graphics.fast.tooltip": "La grafiche svelte e diminuìs la cuantitât di ploie e nêf che si pues viodi.\nI efiets di trasparence pai varis blocs, come lis fueis, a vegnin disativâts.", "options.graphics.warning.accept": "Continue cence supuart", "options.graphics.warning.cancel": "<PERSON><PERSON> in<PERSON>", "options.graphics.warning.message": "Si à rilevât che la tô schede grafiche no supuarte la opzion %s.\n\nTu puedis ignorâ chest avîs e continuâ, ma se tu sielzis di doprâ la grafiche %s nol vignarà ufiert nissun supuart pal tô dispositîf.", "options.graphics.warning.renderer": "Visualizadôr rilevât: [%s]", "options.graphics.warning.title": "Dispositîf grafic no supuartât", "options.graphics.warning.vendor": "Vendidôr rilev<PERSON>t: [%s]", "options.graphics.warning.version": "Version OpenGL rilevade: [%s]", "options.guiScale": "Proporzions de GUI", "options.guiScale.auto": "Auto", "options.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.hideLightningFlashes": "Plate i lamps des saetis", "options.hideLightningFlashes.tooltip": "Al impedìs ai lamps dai fulmins di iluminâ il cîl. Al sarà simpri pussibil viodi i fulmins.", "options.hideMatchedNames": "Plate in base al non", "options.hideMatchedNames.tooltip": "I servidôrs di tiercis parts a podaressin inviâ messaçs te chat intun formât che nol è standard.\nAtivant cheste opzion, i zuiadôrs platâts a vignaran identificâts in base al non dal mitent dai messaçs.", "options.hideSplashTexts": "Plate tescj iniziâi", "options.hideSplashTexts.tooltip": "Plate i tescj iniziâi zâi tal menù principâl.", "options.inactivityFpsLimit": "Ridûs frecuence se", "options.inactivityFpsLimit.afk": "Inatîf", "options.inactivityFpsLimit.afk.tooltip": "Limite la frecuence dai fotograms a 30 cuant che il zuiadôr nol da comants par plui di un minût. Limite a 10 dopo altris 9 minûts.", "options.inactivityFpsLimit.minimized": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.inactivityFpsLimit.minimized.tooltip": "Limite la frecuence dai fotograms dome cuant che il barcon dal zûc al è minimizât.", "options.invertMouse": "In<PERSON><PERSON><PERSON> il mouse", "options.japaneseGlyphVariants": "Variantis dai caratars gjaponês", "options.japaneseGlyphVariants.tooltip": "Dopre lis variantis gjaponesis dai caratars CJK tai caratars predefinîts.", "options.key.hold": "Ten fracât", "options.key.toggle": "Comute", "options.language": "Lenghe...", "options.language.title": "<PERSON><PERSON><PERSON>", "options.languageAccuracyWarning": "(Nol è sigûr che lis traduzions in marilenghe a sedin precisis al 100%%)", "options.languageWarning": "Nol è sigûr che lis traduzions in marilenghe a sedin precisis al 100%%", "options.mainHand": "<PERSON> principâl", "options.mainHand.left": "Çampe", "options.mainHand.right": "Drete", "options.mipmapLevels": "Nivei di Mipmap", "options.modelPart.cape": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.hat": "Cjapiel", "options.modelPart.jacket": "Gjachete", "options.modelPart.left_pants_leg": "Gjambe di çampe", "options.modelPart.left_sleeve": "<PERSON><PERSON>", "options.modelPart.right_pants_leg": "<PERSON><PERSON><PERSON>tre", "options.modelPart.right_sleeve": "<PERSON><PERSON> di diestre", "options.mouseWheelSensitivity": "Sensibilitât de rudiele", "options.mouse_settings": "Impostazions mouse...", "options.mouse_settings.title": "Impostazions dal mouse", "options.multiplayer.title": "Impostazions multi-zuiadôr...", "options.multiplier": "%sx", "options.music_frequency": "Frecuence musiche", "options.music_frequency.constant": "<PERSON><PERSON>", "options.music_frequency.default": "Predefinît", "options.music_frequency.frequent": "Frecuent", "options.music_frequency.tooltip": "Al modifiche trop dispès che e ven riprodote la musiche intun mont dal zûc.", "options.narrator": "<PERSON><PERSON><PERSON><PERSON>", "options.narrator.all": "Al conte dut", "options.narrator.chat": "Al conte la chat", "options.narrator.notavailable": "No disponibil", "options.narrator.off": "No", "options.narrator.system": "Al conte il sisteme", "options.notifications.display_time": "<PERSON><PERSON> des notifichis", "options.notifications.display_time.tooltip": "Al regole par trop timp che dutis lis notifichis a restin visibilis su schermi.", "options.off": "No", "options.off.composed": "%s: No", "options.on": "Sì", "options.on.composed": "%s: <PERSON><PERSON>", "options.online": "In linie...", "options.online.title": "Opzions zûc in linie", "options.onlyShowSecureChat": "Mostre dome la chat sigure", "options.onlyShowSecureChat.tooltip": "Mostre dome i messaçs di altris zuiadôrs che a son verificâts pal mitent e che no son stâts modificâts.", "options.operatorItemsTab": "Ogjets par operadôrs", "options.particles": "<PERSON><PERSON><PERSON>", "options.particles.all": "<PERSON><PERSON>", "options.particles.decreased": "<PERSON><PERSON><PERSON>", "options.particles.minimal": "<PERSON><PERSON>", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Compilazion dai tocs", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Cualchi azion tal toc, come plaçâ o spacâ i blocs, lu tornarà a compilâ daurman.", "options.prioritizeChunkUpdates.nearby": "<PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.nearby.tooltip": "I tocs vicins a son simpri compilâts subit. Chest al pues impatâ lis prestazions cuant che al ven plaçât o spacât un bloc.", "options.prioritizeChunkUpdates.none": "In istancis", "options.prioritizeChunkUpdates.none.tooltip": "I tocs vicins a son compilâts in istancis paralelis. Chest al pues risultâ par pôc timp in busis di visuâl cuant che al ven spacât un bloc.", "options.rawMouseInput": "Jentrade direte", "options.realmsNotifications": "Gnovis e invîts di Realms", "options.realmsNotifications.tooltip": "Recupere notiziis e invîts di Realms tal menù principâl e mostre lis lôr iconis tal boton di Realms.", "options.reducedDebugInfo": "Dâts di F3 ridots", "options.renderClouds": "<PERSON><PERSON><PERSON>", "options.renderCloudsDistance": "Distance dai nûi", "options.renderDistance": "Distance di visualizâ", "options.resourcepack": "Pachets di risorsis...", "options.rotateWithMinecart": "Zire cul carel minerari", "options.rotateWithMinecart.tooltip": "Controle se la visuâl dal zuiadôr e zire cul zirâ dal carel minerari. Disponibil dome tai monts cun la impostazion sperimentâl \"Mioraments dai carei mineraris\" ative.", "options.screenEffectScale": "Efiets di distorsion", "options.screenEffectScale.tooltip": "Intensitât dai efiets nausie e distorsion di viodude dal portâl dal Nether.\nCun valôrs bas, l'efiet di nausie al ven sostituît di une patine verde.", "options.sensitivity": "Sensibilitât", "options.sensitivity.max": "IPERVELOCITÂT!!!", "options.sensitivity.min": "*sossedade*", "options.showNowPlayingToast": "Mostre lis notifichis de musiche", "options.showNowPlayingToast.tooltip": "Al mostre une notifiche cuant che une musiche e tache a sunâ. Cheste notifiche e ven ancje mostrade tal menù di pause dal zûc cuant che la musiche e sune.", "options.showSubtitles": "Mostre i sottitui", "options.simulationDistance": "Distance di simulazion", "options.skinCustomisation": "Personalizazion dal aspiet...", "options.skinCustomisation.title": "Personalizazion dal aspiet", "options.sounds": "Musiche e suns...", "options.sounds.title": "Opzions di musiche e suns", "options.telemetry": "Dâts di telemetrie...", "options.telemetry.button": "Racuelte dâts", "options.telemetry.button.tooltip": "\"%s\" al inclût dome i dâts che a coventin.\n\"%s\" al inclût i dâts facoltatîfs e ancje chei che a coventin.", "options.telemetry.disabled": "Telemetrie disabilitade.", "options.telemetry.state.all": "<PERSON><PERSON><PERSON>", "options.telemetry.state.minimal": "Minims", "options.telemetry.state.none": "Nissun", "options.title": "Opzions", "options.touchscreen": "<PERSON><PERSON><PERSON><PERSON><PERSON> schermi tatil", "options.video": "Impostazions video...", "options.videoTitle": "Impostazions video", "options.viewBobbing": "Pendolâ", "options.visible": "Visibil", "options.vsync": "VSync", "outOfMemory.message": "Minecraft al à esaurît la memorie.\n\nChest al podarès jessi causât di un erôr dal zûc o dal fat che la machine virtuâl Java no à vonde memorie.\n\nPar no corompi il mont, la partide in cors e je terminade. O vin provât a liberâ vonde memorie par fâti tornâ al menù principâl e tornâ a zuiâ, ma no sin sigûr che al vedi funzionât.\n\nTorne Invie il zûc se tu viodis chest messaç di gnûf.", "outOfMemory.title": "Memorie esauride!", "pack.available.title": "Disponibil", "pack.copyFailure": "Nol è stât pussibil copiâ i pachets", "pack.dropConfirm": "<PERSON><PERSON><PERSON> chescj pachets a Minecraft?", "pack.dropInfo": "<PERSON><PERSON><PERSON><PERSON> e mole i files in chest barcon par zontâ i pachets", "pack.dropRejected.message": "Chescj elements no son pachets valits e no son stâts copiâts:\n%s", "pack.dropRejected.title": "Pachets no valits", "pack.folderInfo": "(<PERSON> achì i pachets)", "pack.incompatible": "Incompatibil", "pack.incompatible.confirm.new": "Chest pachet al è stât fat par une version plui gnove di Minecraft e al è probabil che nol funzioni ben.", "pack.incompatible.confirm.old": "Chest pachet al è stât fat par une version plui vecje di Minecraft e al è probabil che nol funzioni plui ben.", "pack.incompatible.confirm.title": "<PERSON><PERSON><PERSON><PERSON> pardabon chest pachet?", "pack.incompatible.new": "(Fat par une version plui gnove di Minecraft)", "pack.incompatible.old": "(Fat par une version plui vecje di Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Vierç cartele dai pachets", "pack.selected.title": "Selezionât", "pack.source.builtin": "integ<PERSON><PERSON><PERSON>", "pack.source.feature": "funzionalitât", "pack.source.local": "<PERSON><PERSON><PERSON><PERSON>", "pack.source.server": "<PERSON>r<PERSON><PERSON><PERSON>", "pack.source.world": "mont", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "<PERSON><PERSON>", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Bersai bombardât cun sucès", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Crani in fûc", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Uciel dai landris", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Cambi", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "<PERSON><PERSON> si<PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Cueste creeper", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Tiere", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Lotadôrs", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Scuvierte", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fûc", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON><PERSON>", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab cun trei pevarons", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "<PERSON><PERSON><PERSON> basse", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Fulminant", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Limons e çus", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passaç", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Purcit su tele", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pontad<PERSON>r", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "La pissine", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Cjavalgjade te pradarie", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Litor<PERSON><PERSON>", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Spire mortâl", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Crani e garofui", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "La sene e je pronte", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "tramont_opac", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "<PERSON><PERSON>", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "Il vueit", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON>nda<PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Desert", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "<PERSON><PERSON>", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "<PERSON><PERSON><PERSON>", "parsing.bool.expected": "Al jere previodût un boolean", "parsing.bool.invalid": "Boolean no valit, %s nol è 'true' o 'false'", "parsing.double.expected": "Al jere previodût un dopli", "parsing.double.invalid": "Dopli '%s' no valit", "parsing.expected": "Al jere previodût '%s'", "parsing.float.expected": "Al jere previodût un numar in virgule mobile", "parsing.float.invalid": "<PERSON><PERSON><PERSON> in virgule mobile '%s' no valit", "parsing.int.expected": "Al jere previodût un intîr", "parsing.int.invalid": "Intîr '%s' no valit", "parsing.long.expected": "Al jere previodût un intîr lunc", "parsing.long.invalid": "Int<PERSON><PERSON> lunc '%s' no valit", "parsing.quote.escape": "Secuence di escape '\\%s' no valide inte stringhe citade", "parsing.quote.expected.end": "Stringhe de citazion no sierade", "parsing.quote.expected.start": "E jere spietade une citazion par scomençâ une stringhe", "particle.invalidOptions": "Impussibil esaminâ lis opzions de particele: %s", "particle.notFound": "Particele no cognossude: %s", "permissions.requires.entity": "E covente une entitât par eseguî chi chest comant", "permissions.requires.player": "Al covente un zuiadôr par eseguî chi chest comant", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "<PERSON><PERSON><PERSON> a<PERSON>:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Predicât no cognossût: %s", "quickplay.error.invalid_identifier": "Impussibil cjatâ il mont cul identificadôr furn<PERSON>t", "quickplay.error.realm_connect": "Impussibil conetisi al Realm", "quickplay.error.realm_permission": "Al mancje il permès par conetisi a chest Realm", "quickplay.error.title": "Alc al è lât strucj te partide svelte", "realms.configuration.region.australia_east": "<PERSON><PERSON><PERSON><PERSON>, Australie", "realms.configuration.region.australia_southeast": "Victoria, Australie", "realms.configuration.region.brazil_south": "<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.central_india": "Indie", "realms.configuration.region.central_us": "Iowa, Stâts Unîts di Americhe", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, Stâts Unîts di Americhe", "realms.configuration.region.east_us_2": "Carolina dal Nord, Stâts Unîts di Americhe", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "<PERSON><PERSON><PERSON><PERSON> dal <PERSON>", "realms.configuration.region.japan_west": "Gja<PERSON>n dal <PERSON>", "realms.configuration.region.korea_central": "Coree dal Sud", "realms.configuration.region.north_central_us": "Illinois, Stâts Unîts di Americhe", "realms.configuration.region.north_europe": "<PERSON><PERSON><PERSON>", "realms.configuration.region.south_central_us": "Texas, Stâts Unîts di Americhe", "realms.configuration.region.southeast_asia": "Singapôr", "realms.configuration.region.sweden_central": "<PERSON><PERSON><PERSON>", "realms.configuration.region.uae_north": "<PERSON><PERSON><PERSON><PERSON>î<PERSON>", "realms.configuration.region.uk_south": "Ingletiere dal Sud", "realms.configuration.region.west_central_us": "Utah, Stâts Unîts di Americhe", "realms.configuration.region.west_europe": "<PERSON><PERSON><PERSON>", "realms.configuration.region.west_us": "California, Stâts Unîts di Americhe", "realms.configuration.region.west_us_2": "Washington, Stâts Unîts di Americhe", "realms.configuration.region_preference.automatic_owner": "Automatic (ping dal proprietari dal <PERSON>)", "realms.configuration.region_preference.automatic_player": "Automatic (prin a jent<PERSON><PERSON> te session)", "realms.missing.snapshot.error.text": "Realms pal moment nol è supuartât tes versions sperimentâls", "recipe.notFound": "Ricete no cognossude: %s", "recipe.toast.description": "Controle il libri des ricetis", "recipe.toast.title": "Imparadis gnovis ricetis!", "record.nowPlaying": "In riproduzion: %s", "recover_world.bug_tracker": "<PERSON><PERSON><PERSON> un erôr", "recover_world.button": "Tente il recupar", "recover_world.done.failed": "Impussibil recuperâ di un stât prin.", "recover_world.done.success": "Il recupar al è lât a bon fin!", "recover_world.done.title": "<PERSON><PERSON><PERSON> comple<PERSON>", "recover_world.issue.missing_file": "<PERSON> mancjant", "recover_world.issue.none": "<PERSON><PERSON><PERSON> erôr", "recover_world.message": "<PERSON><PERSON><PERSON> la leture de cartele dal mont \"%s\" a son vignûts fûr chescj erôrs.\nAl podarès jessi pussibil ripristinâ il mont di un stât prin o tu puedis segnalâ chest erôr tal regjistri dai erôrs.", "recover_world.no_fallback": "Nissun stât disponibil di recuperâ", "recover_world.restore": "Tente di ripristinâ", "recover_world.restoring": "Daûr a tentâ il ripristinament dal mont...", "recover_world.state_entry": "Stât di %s: ", "recover_world.state_entry.unknown": "no cognossût", "recover_world.title": "Nol è stât pussibil cjariâ il mont", "recover_world.warning": "Nol è stât pussibil cjariâ il somari mont", "resourcePack.broken_assets": "RILEVADIS ROBIS DIFETOSIS", "resourcePack.high_contrast.name": "Alt contrast", "resourcePack.load_fail": "<PERSON><PERSON><PERSON><PERSON> tal <PERSON> a cjariâ risorsis", "resourcePack.programmer_art.name": "<PERSON><PERSON>", "resourcePack.runtime_failure": "<PERSON><PERSON><PERSON><PERSON> dal pachet di risorsis", "resourcePack.server.name": "Ris<PERSON><PERSON> specifichis dal mont", "resourcePack.title": "Selezione i pachets di risorsis", "resourcePack.vanilla.description": "L'aspiet predefinît di Minecraft", "resourcePack.vanilla.name": "Predefinît", "resourcepack.downloading": "Daûr a discjariâ il pachet des risorsis", "resourcepack.progress": "Discjariament dal file (%s MB)...", "resourcepack.requesting": "<PERSON><PERSON> in cors...", "screenshot.failure": "Impussibil salvâ la videade: %s\"", "screenshot.success": "Videade salvade come %s", "selectServer.add": "Zonte un servidôr", "selectServer.defaultName": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.delete": "Elimine", "selectServer.deleteButton": "Elimine", "selectServer.deleteQuestion": "Gjavâ pardabon chest servidôr?", "selectServer.deleteWarning": "'%s' al larà pierdût par simpri! (<PERSON><PERSON> ce tant timp!)", "selectServer.direct": "Conession direte", "selectServer.edit": "Modifiche", "selectServer.hiddenAddress": "(<PERSON><PERSON><PERSON><PERSON>)", "selectServer.refresh": "Inzorne", "selectServer.select": "<PERSON><PERSON> tal servid<PERSON>r", "selectWorld.access_failure": "Nol è stât pussibil acedi al mont", "selectWorld.allowCommands": "Permet trucs", "selectWorld.allowCommands.info": "Comants come /gamemode, /experience", "selectWorld.allowCommands.new": "Permet comants", "selectWorld.backupEraseCache": "Scancele dâts te cache", "selectWorld.backupJoinConfirmButton": "Cree une copie di sigurece e cjame", "selectWorld.backupJoinSkipButton": "O sai ce che o stoi fasint!", "selectWorld.backupQuestion.customized": "I monts personalizâts no son plui supuartâts", "selectWorld.backupQuestion.downgrade": "La regression a une version anteriôr di un mont no je supuartade", "selectWorld.backupQuestion.experimental": "I monts che a doprin impostazions sperimentâls no son supuartâts", "selectWorld.backupQuestion.snapshot": "<PERSON><PERSON><PERSON><PERSON> pardabon chest mont?", "selectWorld.backupWarning.customized": "<PERSON>r sfurtune, in cheste version di Minecraft no supuartìn i monts personalizâts. O podìn ancjemò cjariâ chest mont e tignî dut cemût che al jere, ma ogni gnûf teren gjenerât nol sarà plui personalizât. Nus displâs pal inconvenient!", "selectWorld.backupWarning.downgrade": "La ultime volte che chest mont al è stât zuiât, al jere te version %s; tu tu stâs doprant la version %s. Il tornâ indaûr di version di un mont al pues causâ coruzions - no podìn garantî che si cjami o che al funzioni. Se tu desideris distès continuâ, fâs prime une copie di sigurece.", "selectWorld.backupWarning.experimental": "Chest mont al dopre impostazions sperimentâls che a podaressin finî di funzionâ in cualsisei moment. No podìn garantî che si cjarii o che al funzioni. Chi si trate di teritoris inesplorâts!", "selectWorld.backupWarning.snapshot": "La ultime volte che chest mont al è stât zuiât, al jere te version %s; tu tu sês te version %s. Fâs une copie di sigurece tal câs che tu ledis incuintri a problemis di coruzion dal mont.", "selectWorld.bonusItems": "Casse di jutori", "selectWorld.cheats": "Trucs", "selectWorld.commands": "Comants", "selectWorld.conversion": "Al è necessari convertîlu!", "selectWorld.conversion.tooltip": "Par vê une conversion sigure, chest mont al à di jessi vierzût intune version plui vecje (come la 1.6.4)", "selectWorld.create": "Cree un gnûf mont", "selectWorld.customizeType": "Personalize", "selectWorld.dataPacks": "Pachets di dâts", "selectWorld.data_read": "Leture dai dâts dal mont...", "selectWorld.delete": "Elimine", "selectWorld.deleteButton": "Elimine", "selectWorld.deleteQuestion": "Eli<PERSON>â pardabon chest mont?", "selectWorld.deleteWarning": "'%s' al larà pierdût par simpri! (<PERSON><PERSON> ce tant timp!)", "selectWorld.delete_failure": "Nol è stât pussibil eliminâ il mont", "selectWorld.edit": "Modifiche", "selectWorld.edit.backup": "Fâs une copie di sigurece", "selectWorld.edit.backupCreated": "Copie di sigurece eseguide: %s", "selectWorld.edit.backupFailed": "Copie di sigurece falide", "selectWorld.edit.backupFolder": "Vierç la cartele des copiis di sigurece", "selectWorld.edit.backupSize": "dimension: %s MB", "selectWorld.edit.export_worldgen_settings": "Espuarte i parametris di gjenerazion dal mont", "selectWorld.edit.export_worldgen_settings.failure": "Esportazion falide", "selectWorld.edit.export_worldgen_settings.success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.openFolder": "Vierç la cartele dal mont", "selectWorld.edit.optimize": "Otimize mont", "selectWorld.edit.resetIcon": "Ripristine la icone", "selectWorld.edit.save": "Salve", "selectWorld.edit.title": "Modifiche il mont", "selectWorld.enterName": "Non dal mont", "selectWorld.enterSeed": "Se<PERSON>ce pal gjeneradôr di monts", "selectWorld.experimental": "Sperimentâl", "selectWorld.experimental.details": "Detais", "selectWorld.experimental.details.entry": "Funzionalitâts sperimentâls domandadis: %s", "selectWorld.experimental.details.title": "Recuisîts des funzionalitâts sperimentâls", "selectWorld.experimental.message": "Atenzion!\nCheste configurazion e domande funzionalitâts dâur a jessi svilupadis. Il to mont al podarès colassâ, corompisi o no funzionâ cun versions futuris.", "selectWorld.experimental.title": "Atenzion: funzionalitâts sperimentâls", "selectWorld.experiments": "Esperiments", "selectWorld.experiments.info": "I esperiments a son g<PERSON><PERSON> funzionalitâts che un doman a podaressin vignî zontâts al zûc. Atenzion: i esperiments a puedin causâ erôrs e nol è pussibil disativâju dopo de creazion dal mont.", "selectWorld.futureworld.error.text": "Alc al è lât strucj intant che si cirive di cjariâ il mont di une version future. Cheste e jere une operazion pericolose di scomençâ, nus displâs che no vedi funzionât.", "selectWorld.futureworld.error.title": "Al è vignût fûr un erôr!", "selectWorld.gameMode": "Modalitât di zûc", "selectWorld.gameMode.adventure": "Aventure", "selectWorld.gameMode.adventure.info": "Come la modalitât Sorevivence, ma no si puedin plaçâ o rompi i blocs.", "selectWorld.gameMode.adventure.line1": "Compagne di sorevivence ma no si pues", "selectWorld.gameMode.adventure.line2": "meti o gjavâ i blocs", "selectWorld.gameMode.creative": "Creative", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, costruìs e esplore cence limits. Tu puedis svolâ, vê materiâi infinîts e i mostris no ti fasin mâl.", "selectWorld.gameMode.creative.line1": "Ris<PERSON>is ilimita<PERSON>, svolâ in libertât e", "selectWorld.gameMode.creative.line2": "spacâ i blocs intun colp sôl", "selectWorld.gameMode.hardcore": "Impegnative", "selectWorld.gameMode.hardcore.info": "Modalitât Sorevivence blocade su la dificoltât 'Dificile'. Se tu mueris no tu puedis tornâ a nassi.", "selectWorld.gameMode.hardcore.line1": "Compagn di sorevivence, ma bloc<PERSON>t ae dificolt<PERSON>t", "selectWorld.gameMode.hardcore.line2": "plui dificile e une vite sole", "selectWorld.gameMode.spectator": "Spetatôr", "selectWorld.gameMode.spectator.info": "Tu puedis cjalâ ma no tocjâ.", "selectWorld.gameMode.spectator.line1": "Tu puedis cjalâ ma no tocjâ", "selectWorld.gameMode.survival": "Sorevivence", "selectWorld.gameMode.survival.info": "Esplore un mont misteriôs, dulà che tu puedis costruî, tirâ dongje risorsis, fabricâ ogjets e combati i mostris.", "selectWorld.gameMode.survival.line1": "<PERSON><PERSON><PERSON> r<PERSON>, fabricâ, vuadagnâ", "selectWorld.gameMode.survival.line2": "nivei, salût e fam", "selectWorld.gameRules": "<PERSON><PERSON><PERSON> da<PERSON>", "selectWorld.import_worldgen_settings": "Impuarte lis impostazions", "selectWorld.import_worldgen_settings.failure": "Er<PERSON>r tal impuartâ lis impostazions", "selectWorld.import_worldgen_settings.select_file": "Selezione il file des impostazions (.json)", "selectWorld.incompatible.description": "Nol è pussibil vierzi chest mont in cheste version.\nLa ultime volte al è stât zuiât te version %s.", "selectWorld.incompatible.info": "Version incompatibile: %s", "selectWorld.incompatible.title": "Version incompatibile", "selectWorld.incompatible.tooltip": "Nol è pussibil vierzi chest mont parcè che al è stât creât di une version che no je compatibile.", "selectWorld.incompatible_series": "<PERSON><PERSON><PERSON><PERSON> in une version incompatibil", "selectWorld.load_folder_access": "Impussibil lei o jentrâ te cartele dulà che a son salvâts i monts dal zûc!", "selectWorld.loading_list": "Daûr a cjariâ la liste dai monts", "selectWorld.locked": "Blocât di une altre istance in esecuzion di Minecraft", "selectWorld.mapFeatures": "<PERSON><PERSON><PERSON><PERSON> struturis", "selectWorld.mapFeatures.info": "Pa<PERSON>ut, relits e v.i.", "selectWorld.mapType": "<PERSON><PERSON><PERSON>t", "selectWorld.mapType.normal": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.moreWorldOptions": "Altris opzions dal mont...", "selectWorld.newWorld": "Gnûf mont", "selectWorld.recreate": "<PERSON>ne cree", "selectWorld.recreate.customized.text": "La personalizazion dai monts no je plui supuartade in cheste version di Minecraft. O podìn cirî di tornâlu a creâ cu la stesse semence e proprietâts, ma cualsisei personalizazion di teren e larà pierdude. Nus displâs pal inconvenient!", "selectWorld.recreate.customized.title": "I monts personalizâts no son plui supuartâts", "selectWorld.recreate.error.text": "Alc al è lât strucj tal cirî di tornâ a creâ il mont.", "selectWorld.recreate.error.title": "Al è vignût fûr un erôr!", "selectWorld.resource_load": "Daûr a prontâ lis risorsis...", "selectWorld.resultFolder": "Al vignarà salvât in:", "selectWorld.search": "cîr un mont", "selectWorld.seedInfo": "Lasse vueit par vê une semence casuâl", "selectWorld.select": "<PERSON><PERSON>e tal mont selezionât", "selectWorld.targetFolder": "Cartele di salvament: %s", "selectWorld.title": "Selezione un mont", "selectWorld.tooltip.fromNewerVersion1": "Mont salvât intune gnove version,", "selectWorld.tooltip.fromNewerVersion2": "il cjariament di chest mont al podarès causâ problemis!", "selectWorld.tooltip.snapshot1": "No sta dismenteâti di fâ une copie di sigurece di chest mont", "selectWorld.tooltip.snapshot2": "prime di cjar<PERSON> in cheste version.", "selectWorld.unable_to_load": "Impussibil c<PERSON> i <PERSON>ts", "selectWorld.version": "Version:", "selectWorld.versionJoinButton": "<PERSON><PERSON><PERSON>", "selectWorld.versionQuestion": "Cjamâ pardabon chest mont?", "selectWorld.versionUnknown": "no cognossude", "selectWorld.versionWarning": "La ultime volte chest mont al è stât zuiât te version %s, se tu lu vierzis in cheste version tu podaressis causâ coruzions!", "selectWorld.warning.deprecated.question": "Cualchi funzionalitât doprade e je deplorade e un doman no funzionarà plui. Continuâ?", "selectWorld.warning.deprecated.title": "Atenzion! Chestis impostazions a doprin funzionalitâts deploradis", "selectWorld.warning.experimental.question": "Chestis impostazions a son sperimentâls e un dì a podaressin no funzionâ. Continuâ?", "selectWorld.warning.experimental.title": "Atenzion! Chestis impostazions a doprin funzionalitâts sperimentâls", "selectWorld.warning.lowDiskSpace.description": "Al reste pôc spazi libar tal to dispositîf.\nFinî il spazi sul disc dilunc di une session di zûc al podarès comprometi il mont.", "selectWorld.warning.lowDiskSpace.title": "Atenzion! Pôc spazi sul disc!", "selectWorld.world": "Mont", "sign.edit": "Modifiche messaç dal cartel", "sleep.not_possible": "Nol è pussibil saltâ la gnot", "sleep.players_sleeping": "%s/%s zuiadôrs indurmidîts", "sleep.skipping_night": "Tu stâs passant la gnôt a polsâ", "slot.only_single_allowed": "Si ametin dome spazis singui, si à vût '%s'", "slot.unknown": "Spazi '%s' no cognossût", "snbt.parser.empty_key": "La clâf no pues jessi vueide", "snbt.parser.expected_binary_numeral": "Al jere previodût un numar binari", "snbt.parser.expected_decimal_numeral": "Al jere previodût un numar decimâl", "snbt.parser.expected_float_type": "Al jere previodût un numar in virgule mobile", "snbt.parser.expected_hex_escape": "Al jere previodût un caratar leterâl di lungjece %s", "snbt.parser.expected_hex_numeral": "Al jere previodût un numar esadecimâl", "snbt.parser.expected_integer_type": "Al jere previodût un numar intîr", "snbt.parser.expected_non_negative_number": "Al jere previodût un numar no negatîf", "snbt.parser.expected_number_or_boolean": "Al jere previodût un numar o un boolean", "snbt.parser.expected_string_uuid": "E jere previodude une stringhe cuntun UUID valit", "snbt.parser.expected_unquoted_string": "E jere previodude une stringhe valide cence virgulutis", "snbt.parser.infinity_not_allowed": "I numars no finîts no son permetûts", "snbt.parser.invalid_array_element_type": "<PERSON><PERSON><PERSON> di element dal array no valit", "snbt.parser.invalid_character_name": "Non dal caratar unicode no valit", "snbt.parser.invalid_codepoint": "Valôr dal caratar unicode no valit: %s", "snbt.parser.invalid_string_contents": "Contignûts de stringhe no valits", "snbt.parser.invalid_unquoted_start": "<PERSON>s stringhis cence virgulutis no puedin tacâ cun lis cifris 0-9, cun + o cun -", "snbt.parser.leading_zero_not_allowed": "I numars decimâls no puedin tacâ cun 0", "snbt.parser.no_such_operation": "Nissune operazion: %s", "snbt.parser.number_parse_failure": "Impussibil esaminâ il numar: %s", "snbt.parser.undescore_not_allowed": "I tratuts bas no son permetûts tal inizi o ae fin di un numar", "soundCategory.ambient": "Ambient", "soundCategory.block": "Blocs", "soundCategory.hostile": "<PERSON><PERSON><PERSON><PERSON> nemiis", "soundCategory.master": "Volum gje<PERSON>l", "soundCategory.music": "<PERSON><PERSON>", "soundCategory.neutral": "C<PERSON><PERSON><PERSON> amiis", "soundCategory.player": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soundCategory.record": "Blocs zirediscs/notis", "soundCategory.ui": "Interface utent", "soundCategory.voice": "Vôs e feveladis", "soundCategory.weather": "<PERSON><PERSON> met<PERSON><PERSON>", "spectatorMenu.close": "Siere il menù", "spectatorMenu.next_page": "Pagjine sucessive", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON><PERSON>e", "spectatorMenu.root.prompt": "Frache un tast par selezionâ un comant, e di gnûf par eseguîlu.", "spectatorMenu.team_teleport": "Teletraspuartâsi a un membri di une scuadre", "spectatorMenu.team_teleport.prompt": "Sielç une scuadre dulà teletraspuartâsi", "spectatorMenu.teleport": "Teletraspuartâsi al zuiadôr", "spectatorMenu.teleport.prompt": "Selezione un zuiadôr dulà teletraspuartâsi", "stat.generalButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.itemsButton": "Ogjets", "stat.minecraft.animals_bred": "Best<PERSON>s che a àn fiât", "stat.minecraft.aviate_one_cm": "Distance svolade cu lis elitris", "stat.minecraft.bell_ring": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.boat_one_cm": "Distance in barcje", "stat.minecraft.clean_armor": "Tocs di armadure netâts", "stat.minecraft.clean_banner": "Stendarts netâts", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON><PERSON> dal shulker netadis", "stat.minecraft.climb_one_cm": "Distance in rimpinade", "stat.minecraft.crouch_one_cm": "Distance scrufuiât", "stat.minecraft.damage_absorbed": "Dam assorbît", "stat.minecraft.damage_blocked_by_shield": "<PERSON> blocât cul scût", "stat.minecraft.damage_dealt": "Dam causât", "stat.minecraft.damage_dealt_absorbed": "<PERSON> causât (assorbît)", "stat.minecraft.damage_dealt_resisted": "Dam causât (incassât)", "stat.minecraft.damage_resisted": "Dam incassât", "stat.minecraft.damage_taken": "Dam ricevût", "stat.minecraft.deaths": "Voltis che tu sês muart/e", "stat.minecraft.drop": "Ogjet<PERSON> mol<PERSON>", "stat.minecraft.eat_cake_slice": "<PERSON>tis di torte mangjadis", "stat.minecraft.enchant_item": "Ogjet<PERSON>", "stat.minecraft.fall_one_cm": "Distance totâl colant", "stat.minecraft.fill_cauldron": "Cjalderons jemplâts", "stat.minecraft.fish_caught": "<PERSON><PERSON> p<PERSON>", "stat.minecraft.fly_one_cm": "Distance svolant", "stat.minecraft.happy_ghast_one_cm": "Distance dal Ghast legri", "stat.minecraft.horse_one_cm": "Distance a cjaval", "stat.minecraft.inspect_dispenser": "Distributôrs ispezionâts", "stat.minecraft.inspect_dropper": "Tiradôrs ispezionâts", "stat.minecraft.inspect_hopper": "T<PERSON><PERSON>is is<PERSON>", "stat.minecraft.interact_with_anvil": "Incuins ispezionâts", "stat.minecraft.interact_with_beacon": "Interazions cui fârs", "stat.minecraft.interact_with_blast_furnace": "Interazions cui fors a fusion", "stat.minecraft.interact_with_brewingstand": "Interazions cui lambics", "stat.minecraft.interact_with_campfire": "Interazions cui fogarons", "stat.minecraft.interact_with_cartography_table": "Interazions cui bancs dai cartografs", "stat.minecraft.interact_with_crafting_table": "Interazions cui bancs di lavôr", "stat.minecraft.interact_with_furnace": "Interazions cu lis fornâs", "stat.minecraft.interact_with_grindstone": "Interazions cu lis muelis", "stat.minecraft.interact_with_lectern": "Interazions cui letorins", "stat.minecraft.interact_with_loom": "Interazions cui telârs", "stat.minecraft.interact_with_smithing_table": "Interazions cui bancs par faris", "stat.minecraft.interact_with_smoker": "Interazions cui fumadôrs", "stat.minecraft.interact_with_stonecutter": "Interazions cui taiadôrs di piere", "stat.minecraft.jump": "Salts", "stat.minecraft.leave_game": "Jessudis des partidis", "stat.minecraft.minecart_one_cm": "Distance tal carel minerari", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_barrel": "<PERSON><PERSON><PERSON> v<PERSON>", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON> vierts", "stat.minecraft.open_enderchest": "<PERSON><PERSON>i dal <PERSON> vierts", "stat.minecraft.open_shulker_box": "Scjatulis di shulker viertis", "stat.minecraft.pig_one_cm": "Distance su purcits", "stat.minecraft.play_noteblock": "Blocs pes notis sunâts", "stat.minecraft.play_record": "Discs musicâi riprodots", "stat.minecraft.play_time": "<PERSON><PERSON> in zûc", "stat.minecraft.player_kills": "Zuiad<PERSON><PERSON> cop<PERSON>", "stat.minecraft.pot_flower": "<PERSON><PERSON> in<PERSON>adis", "stat.minecraft.raid_trigger": "Incursions inviadis", "stat.minecraft.raid_win": "Incursions vinçudis", "stat.minecraft.sleep_in_bed": "Polsadis intun jet", "stat.minecraft.sneak_time": "<PERSON><PERSON>", "stat.minecraft.sprint_one_cm": "Distance corint", "stat.minecraft.strider_one_cm": "Distance su strider", "stat.minecraft.swim_one_cm": "Distance nadant", "stat.minecraft.talked_to_villager": "Cjacaradis cui paisans", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON>", "stat.minecraft.time_since_death": "<PERSON><PERSON><PERSON> de ul<PERSON> muart", "stat.minecraft.time_since_rest": "<PERSON><PERSON> de <PERSON> polsade", "stat.minecraft.total_world_time": "<PERSON><PERSON> cul mont viert", "stat.minecraft.traded_with_villager": "Cumierçs cui paisans", "stat.minecraft.trigger_trapped_chest": "Baûi trapule ativâts", "stat.minecraft.tune_noteblock": "Blocs pes notis acordâts", "stat.minecraft.use_cauldron": "<PERSON>ghe cjolte dai cjalderons", "stat.minecraft.walk_on_water_one_cm": "Distance cjaminant sore de aghe", "stat.minecraft.walk_one_cm": "Distance cjaminant", "stat.minecraft.walk_under_water_one_cm": "Distance a pît sot de aghe", "stat.mobsButton": "<PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "Rots", "stat_type.minecraft.crafted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.dropped": "<PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.killed": "Tu âs copât %s voltis cheste creature", "stat_type.minecraft.killed.none": "La creature %s no je mai stade copade", "stat_type.minecraft.killed_by": "Par colpe di %s tu sês muart/e %s voltis", "stat_type.minecraft.killed_by.none": "La creature %s no ti à mai copât/ade", "stat_type.minecraft.mined": "Estrats", "stat_type.minecraft.picked_up": "<PERSON><PERSON><PERSON><PERSON><PERSON> sù", "stat_type.minecraft.used": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stats.none": "-", "structure_block.button.detect_size": "RILEVE", "structure_block.button.load": "CJAME", "structure_block.button.save": "SALVE", "structure_block.custom_data": "Non de etichete-dâts personalizât", "structure_block.detect_size": "Rileve dimension e posizion de struture:", "structure_block.hover.corner": "Angul: %s", "structure_block.hover.data": "Dâts: %s", "structure_block.hover.load": "Cjame: %s", "structure_block.hover.save": "Salve: %s", "structure_block.include_entities": "Inclût entitâts:", "structure_block.integrity": "Semence e integritât de struture", "structure_block.integrity.integrity": "Integritât de struture", "structure_block.integrity.seed": "Semence de struture", "structure_block.invalid_structure_name": "Non de struture '%s' no valit", "structure_block.load_not_found": "La struture '%s' no je disponibile", "structure_block.load_prepare": "Posizion de struture '%s' prontade", "structure_block.load_success": "<PERSON><PERSON>ture cjariade di '%s'", "structure_block.mode.corner": "<PERSON><PERSON>", "structure_block.mode.data": "<PERSON><PERSON><PERSON>", "structure_block.mode.load": "<PERSON><PERSON><PERSON>", "structure_block.mode.save": "Salve", "structure_block.mode_info.corner": "Modalitât angul - indicadôr di plaçament e dimension", "structure_block.mode_info.data": "Modalitât dâts - indicadôr logjiche di zûc", "structure_block.mode_info.load": "Modalitât c<PERSON> - cjariâ di file", "structure_block.mode_info.save": "Modalitât salvament - scrivi su file", "structure_block.position": "Posizion relative", "structure_block.position.x": "posizion relative x", "structure_block.position.y": "posizion relative y", "structure_block.position.z": "posizion relative z", "structure_block.save_failure": "Impussibil salvâ la struture '%s'", "structure_block.save_success": "Struture salvade come '%s'", "structure_block.show_air": "Mostre blocs invisibii:", "structure_block.show_boundingbox": "Mostre ricuardi di delimitazion:", "structure_block.size": "Dimension de struture", "structure_block.size.x": "dimension de struture x", "structure_block.size.y": "dimension de struture y", "structure_block.size.z": "dimension de struture z", "structure_block.size_failure": "Impussibil rilevâ la dimension de struture. Zonte i angui cul non de struture corispondente", "structure_block.size_success": "Dimension rilevade cun sucès par '%s'", "structure_block.strict": "Plaçament discret:", "structure_block.structure_name": "Non de struture", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON><PERSON> incuiet<PERSON>", "subtitles.ambient.sound": "<PERSON><PERSON><PERSON><PERSON> incuiet<PERSON>", "subtitles.block.amethyst_block.chime": "Ametiste e sglinghine", "subtitles.block.amethyst_block.resonate": "Ametiste e sune", "subtitles.block.anvil.destroy": "Incuin distrut", "subtitles.block.anvil.land": "Incuin butât a tiere", "subtitles.block.anvil.use": "<PERSON><PERSON>", "subtitles.block.barrel.close": "Caratel si siere", "subtitles.block.barrel.open": "Caratel si vierç", "subtitles.block.beacon.activate": "<PERSON><PERSON><PERSON>", "subtitles.block.beacon.ambient": "Businâ di fâr", "subtitles.block.beacon.deactivate": "<PERSON><PERSON><PERSON>", "subtitles.block.beacon.power_select": "<PERSON>d<PERSON> dal fâr se<PERSON>", "subtitles.block.beehive.drip": "<PERSON><PERSON><PERSON> e gote", "subtitles.block.beehive.enter": "Une âf e jentre tal bôç", "subtitles.block.beehive.exit": "Une âf e jes dal bôç", "subtitles.block.beehive.shear": "<PERSON><PERSON><PERSON><PERSON> a rassin", "subtitles.block.beehive.work": "Âfs a lavorin", "subtitles.block.bell.resonate": "Rivoc di cjampane", "subtitles.block.bell.use": "Cjampane e sune", "subtitles.block.big_dripleaf.tilt_down": "Planteforme si plee", "subtitles.block.big_dripleaf.tilt_up": "Planteforme si drece", "subtitles.block.blastfurnace.fire_crackle": "Sclopetament di for a fusion", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.bubble_pop": "Sclopetament di bufulis", "subtitles.block.bubble_column.upwards_ambient": "Vongolament di bufulis", "subtitles.block.bubble_column.upwards_inside": "<PERSON><PERSON><PERSON><PERSON> a s<PERSON>icin", "subtitles.block.bubble_column.whirlpool_ambient": "Sgurlament di bufulis", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON> di bufulis che a tirin jù", "subtitles.block.button.click": "Scjocade di pulsant", "subtitles.block.cake.add_candle": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "subtitles.block.campfire.crackle": "Sclopetament di fogaron", "subtitles.block.candle.crackle": "Sclopetament di cjandele", "subtitles.block.candle.extinguish": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>", "subtitles.block.chest.close": "<PERSON><PERSON>l si siere", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON> b<PERSON>â<PERSON>", "subtitles.block.chest.open": "Baûl al ven viert", "subtitles.block.chorus_flower.death": "<PERSON> cor<PERSON>l si inflapìs", "subtitles.block.chorus_flower.grow": "<PERSON> cor<PERSON>l e mene", "subtitles.block.comparator.click": "Comparadôr azionât", "subtitles.block.composter.empty": "Compostiere disvuedade", "subtitles.block.composter.fill": "Compostiere jem<PERSON>", "subtitles.block.composter.ready": "Compostiere pronte", "subtitles.block.conduit.activate": "Condot al ven ativât", "subtitles.block.conduit.ambient": "Condot al pulse", "subtitles.block.conduit.attack.target": "Condot al atache", "subtitles.block.conduit.deactivate": "Condot al ven disativât", "subtitles.block.copper_bulb.turn_off": "<PERSON><PERSON><PERSON> di ram si distude", "subtitles.block.copper_bulb.turn_on": "<PERSON><PERSON><PERSON> di ram si impie", "subtitles.block.copper_trapdoor.close": "<PERSON><PERSON><PERSON><PERSON> si siere", "subtitles.block.copper_trapdoor.open": "Trabuchel si vierç", "subtitles.block.crafter.craft": "Fabricadôr al fabriche", "subtitles.block.crafter.fail": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.creaking_heart.hurt": "<PERSON><PERSON>r dal cric al bruntule", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON><PERSON> incuiet<PERSON>", "subtitles.block.creaking_heart.spawn": "<PERSON><PERSON>r dal cric si svee", "subtitles.block.deadbush.idle": "Suns secs", "subtitles.block.decorated_pot.insert": "<PERSON><PERSON><PERSON> de<PERSON> jem<PERSON>", "subtitles.block.decorated_pot.insert_fail": "Vâs decorât al bale", "subtitles.block.decorated_pot.shatter": "Vâs decorât si creve", "subtitles.block.dispenser.dispense": "Ogjet distribuît", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON><PERSON> dal distributôr", "subtitles.block.door.toggle": "Ciulament di puarte", "subtitles.block.dried_ghast.ambient": "<PERSON><PERSON><PERSON> se<PERSON><PERSON><PERSON><PERSON> al sflade", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON> secjât si torne a idratâ", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON><PERSON> secj<PERSON>t si imbombe", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON> secjât si sint miôr", "subtitles.block.dry_grass.ambient": "Suns di aiar che al tire", "subtitles.block.enchantment_table.use": "Banc par incjantaments doprât", "subtitles.block.end_portal.spawn": "Portâl dal End viert", "subtitles.block.end_portal_frame.fill": "Voli di ender pla<PERSON><PERSON>", "subtitles.block.eyeblossom.close": "Rose-voli si siere", "subtitles.block.eyeblossom.idle": "Rose-voli e cisiche", "subtitles.block.eyeblossom.open": "Rose-voli si vierç", "subtitles.block.fence_gate.toggle": "Ciulament dal portonut", "subtitles.block.fire.ambient": "Sclopetament di fûc", "subtitles.block.fire.extinguish": "Fûc distudât", "subtitles.block.firefly_bush.idle": "Businâ di lusigne", "subtitles.block.frogspawn.hatch": "Ûfs di crot si vierzin", "subtitles.block.furnace.fire_crackle": "Sclopetament de fornâs", "subtitles.block.generic.break": "Bloc rot", "subtitles.block.generic.fall": "Alc al cole su di un bloc", "subtitles.block.generic.footsteps": "Pas", "subtitles.block.generic.hit": "Distruzion di un bloc", "subtitles.block.generic.place": "Bloc plaçât", "subtitles.block.grindstone.use": "<PERSON><PERSON>", "subtitles.block.growing_plant.crop": "Plante dispedade", "subtitles.block.hanging_sign.waxed_interact_fail": "Cartel al bale", "subtitles.block.honey_block.slide": "Sbrissament dal bloc di mîl", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON><PERSON><PERSON> di fier si siere", "subtitles.block.iron_trapdoor.open": "Trabuchel di fier si vierç", "subtitles.block.lava.ambient": "Bulidure di lave", "subtitles.block.lava.extinguish": "Cisament di lave", "subtitles.block.lever.click": "<PERSON>ve a<PERSON>", "subtitles.block.note_block.note": "Sun dal bloc pes notis", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON><PERSON> incuiet<PERSON>", "subtitles.block.piston.move": "Piston azionât", "subtitles.block.pointed_dripstone.drip_lava": "Lave e gote", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lave e gote intun cjalderon", "subtitles.block.pointed_dripstone.drip_water": "Aghe e gote", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Aghe e gote intun cjalderon", "subtitles.block.pointed_dripstone.land": "Stalatite e cole", "subtitles.block.portal.ambient": "Sunsûr di portâl", "subtitles.block.portal.travel": "Sunsûr di portâl che si sfante", "subtitles.block.portal.trigger": "Sunsûr di portâl che si intensifiche", "subtitles.block.pressure_plate.click": "Scjocade de plache di pression", "subtitles.block.pumpkin.carve": "Fuarpiis a tain", "subtitles.block.redstone_torch.burnout": "Torce di redstone brusade", "subtitles.block.respawn_anchor.ambient": "Sunsûr di portâl", "subtitles.block.respawn_anchor.charge": "Ancure de rinassite alimentade", "subtitles.block.respawn_anchor.deplete": "Ancure di rinassite si indebilìs", "subtitles.block.respawn_anchor.set_spawn": "Ancure di rinassite ativade", "subtitles.block.sand.idle": "Suns di savalon", "subtitles.block.sand.wind": "Suns di aiar che al tire", "subtitles.block.sculk.charge": "Sculk al bol", "subtitles.block.sculk.spread": "Sculk si slargje", "subtitles.block.sculk_catalyst.bloom": "Catalizadôr di Sculk al difont", "subtitles.block.sculk_sensor.clicking": "Sens<PERSON><PERSON> di Sculk si impie", "subtitles.block.sculk_sensor.clicking_stop": "<PERSON>s<PERSON><PERSON> di Sculk si distude", "subtitles.block.sculk_shrieker.shriek": "Sculk zigon al zighe", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON><PERSON> di Shulker si siere", "subtitles.block.shulker_box.open": "Scjatule di Shulker si vierç", "subtitles.block.sign.waxed_interact_fail": "Cartel al bale", "subtitles.block.smithing_table.use": "Banc par fari do<PERSON>t", "subtitles.block.smoker.smoke": "Fu<PERSON><PERSON><PERSON> al fume", "subtitles.block.sniffer_egg.crack": "Ûf di nasadôr si crepe", "subtitles.block.sniffer_egg.hatch": "Ûf di nasadôr si vierç", "subtitles.block.sniffer_egg.plop": "Nasad<PERSON>r al cole", "subtitles.block.sponge.absorb": "Sponze e supe", "subtitles.block.sweet_berry_bush.pick_berries": "Pomulis a cjapadis sù", "subtitles.block.trapdoor.close": "<PERSON><PERSON><PERSON><PERSON> si siere", "subtitles.block.trapdoor.open": "Trabuchel si vierç", "subtitles.block.trapdoor.toggle": "Trabuchel al ciule", "subtitles.block.trial_spawner.about_to_spawn_item": "Ogjet dal malauguri si prepare", "subtitles.block.trial_spawner.ambient": "Sclopetament dal gjeneradôr de prove", "subtitles.block.trial_spawner.ambient_charged": "Sclopetament dal gjeneradôr de prove dal maluguri", "subtitles.block.trial_spawner.ambient_ominous": "Sclopetament dal gjeneradôr de prove dal maluguri", "subtitles.block.trial_spawner.charge_activate": "Malauguri al inglobe il gjeneradôr de prove", "subtitles.block.trial_spawner.close_shutter": "Gjeneradôr de prove si siere", "subtitles.block.trial_spawner.detect_player": "Gje<PERSON>ad<PERSON>r de prove si cjarie", "subtitles.block.trial_spawner.eject_item": "Gjeneradôr de prove al bute fûr ogjets", "subtitles.block.trial_spawner.ominous_activate": "Malauguri al inglobe il gjeneradôr de prove", "subtitles.block.trial_spawner.open_shutter": "Gjeneradôr de prove si vierç", "subtitles.block.trial_spawner.spawn_item": "Ogjet dal malauguri al cole", "subtitles.block.trial_spawner.spawn_item_begin": "Ogjet dal malauguri al ven fûr", "subtitles.block.trial_spawner.spawn_mob": "Creature de prove e nas", "subtitles.block.tripwire.attach": "<PERSON><PERSON><PERSON>", "subtitles.block.tripwire.click": "Scjocade di fîl", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.activate": "Cassefuarte si impie", "subtitles.block.vault.ambient": "Sclopetament di cassefuarte", "subtitles.block.vault.close_shutter": "Cassefuarte si siere", "subtitles.block.vault.deactivate": "Cassefuarte si distude", "subtitles.block.vault.eject_item": "Cassefuarte e bute fûr un ogjet", "subtitles.block.vault.insert_item": "Cassefuarte si sbloche", "subtitles.block.vault.insert_item_fail": "Cassefuarte no si sbloche", "subtitles.block.vault.open_shutter": "Cassefuarte si vierç", "subtitles.block.vault.reject_rewarded_player": "Cassefuarte e refude un zuiadôr", "subtitles.block.water.ambient": "Aghe che e cor", "subtitles.block.wet_sponge.dries": "Sponze si secje", "subtitles.chiseled_bookshelf.insert": "Libri plaçât", "subtitles.chiseled_bookshelf.insert_enchanted": "Libri incantâ<PERSON> p<PERSON>", "subtitles.chiseled_bookshelf.take": "Li<PERSON> cjolt", "subtitles.chiseled_bookshelf.take_enchanted": "Libri incant<PERSON><PERSON> cjolt", "subtitles.enchant.thorns.hit": "Colp di spinis", "subtitles.entity.allay.ambient_with_item": "Slizeridôr al c<PERSON>ce", "subtitles.entity.allay.ambient_without_item": "Slizeridôr al brame", "subtitles.entity.allay.death": "Slizeridôr al mûr", "subtitles.entity.allay.hurt": "<PERSON><PERSON><PERSON>id<PERSON><PERSON>", "subtitles.entity.allay.item_given": "Slizeridôr al rî<PERSON>", "subtitles.entity.allay.item_taken": "Slizeridôr al cjape sù", "subtitles.entity.allay.item_thrown": "Slizeridôr al tire", "subtitles.entity.armadillo.ambient": "Armadil al rugne", "subtitles.entity.armadillo.brush": "<PERSON><PERSON>", "subtitles.entity.armadillo.death": "Arm<PERSON><PERSON> al mûr", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON> al mangje", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.hurt_reduced": "<PERSON><PERSON><PERSON> si pare", "subtitles.entity.armadillo.land": "Armadil al tocje tiere", "subtitles.entity.armadillo.peek": "Armadil al cuche", "subtitles.entity.armadillo.roll": "Armadil si inrodole", "subtitles.entity.armadillo.scute_drop": "Armadil al piert un scus", "subtitles.entity.armadillo.unroll_finish": "Armadil si disrodole", "subtitles.entity.armadillo.unroll_start": "Armadil al cuche", "subtitles.entity.armor_stand.fall": "<PERSON><PERSON><PERSON><PERSON> molât par tiere", "subtitles.entity.arrow.hit": "Foropade di frece", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.arrow.shoot": "<PERSON><PERSON><PERSON> tirade", "subtitles.entity.axolotl.attack": "Axolotl al atache", "subtitles.entity.axolotl.death": "Axolotl al mûr", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON><PERSON><PERSON> fer<PERSON>", "subtitles.entity.axolotl.idle_air": "Piulâ di axolotl", "subtitles.entity.axolotl.idle_water": "Piulâ di axolotl", "subtitles.entity.axolotl.splash": "Axolotl al svuacare", "subtitles.entity.axolotl.swim": "Axolotl al nade", "subtitles.entity.bat.ambient": "Vuicade di gnotul", "subtitles.entity.bat.death": "Gnotul al mûr", "subtitles.entity.bat.hurt": "Gnotul ferît", "subtitles.entity.bat.takeoff": "Gnotul al svole", "subtitles.entity.bee.ambient": "Bus<PERSON><PERSON>", "subtitles.entity.bee.death": "<PERSON><PERSON> e mûr", "subtitles.entity.bee.hurt": "<PERSON><PERSON> feride", "subtitles.entity.bee.loop": "Bus<PERSON><PERSON>", "subtitles.entity.bee.loop_aggressive": "<PERSON><PERSON><PERSON> rabi<PERSON> di <PERSON>", "subtitles.entity.bee.pollinate": "Âf e busine contente", "subtitles.entity.bee.sting": "<PERSON><PERSON> e beche", "subtitles.entity.blaze.ambient": "Re<PERSON><PERSON><PERSON> di flamade", "subtitles.entity.blaze.burn": "Sclopetament di flamade", "subtitles.entity.blaze.death": "Flamade e mûr", "subtitles.entity.blaze.hurt": "Flamade feride", "subtitles.entity.blaze.shoot": "Flamade e spare", "subtitles.entity.boat.paddle_land": "Vogade su tiere", "subtitles.entity.boat.paddle_water": "Vogade in aghe", "subtitles.entity.bogged.ambient": "Criçâ di paludan", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.bogged.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.charge": "Brise e cjarie", "subtitles.entity.breeze.death": "Brise e mûr", "subtitles.entity.breeze.deflect": "Brise e devie", "subtitles.entity.breeze.hurt": "Brise feride", "subtitles.entity.breeze.idle_air": "Brise e svole", "subtitles.entity.breeze.idle_ground": "Brise e sofle", "subtitles.entity.breeze.inhale": "Brise e inspire", "subtitles.entity.breeze.jump": "Brise e salte", "subtitles.entity.breeze.land": "Brise e tocje tiere", "subtitles.entity.breeze.shoot": "Brise e spare", "subtitles.entity.breeze.slide": "Brise e sbrisse", "subtitles.entity.breeze.whirl": "Brise e sgurle", "subtitles.entity.breeze.wind_burst": "Cjarie di aiar e sclope", "subtitles.entity.camel.ambient": "Dr<PERSON><PERSON><PERSON> al rugne", "subtitles.entity.camel.dash": "Dromedari al salte", "subtitles.entity.camel.dash_ready": "Dromedari al torne a dâ di ca", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON><PERSON> al mûr", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON><PERSON> al mangje", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON><PERSON> fer<PERSON>", "subtitles.entity.camel.saddle": "<PERSON><PERSON> metude sù", "subtitles.entity.camel.sit": "Dr<PERSON><PERSON>i si sente", "subtitles.entity.camel.stand": "Dromedari si alce", "subtitles.entity.camel.step": "<PERSON><PERSON><PERSON><PERSON> al cjamine", "subtitles.entity.camel.step_sand": "Dr<PERSON><PERSON><PERSON> al cjamine sul savalon", "subtitles.entity.cat.ambient": "Gnauleç di gjat", "subtitles.entity.cat.beg_for_food": "<PERSON><PERSON><PERSON> al domande mangja<PERSON>", "subtitles.entity.cat.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.cat.eat": "<PERSON><PERSON><PERSON> al mangje", "subtitles.entity.cat.hiss": "<PERSON><PERSON><PERSON> al tufe", "subtitles.entity.cat.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.cat.purr": "Gjat al fâs fûs", "subtitles.entity.chicken.ambient": "Gjaline e clocje", "subtitles.entity.chicken.death": "Gjaline e mûr", "subtitles.entity.chicken.egg": "Gjaline e ove", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON> feride", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON><PERSON> al mûr", "subtitles.entity.cod.flop": "<PERSON>rl<PERSON><PERSON> si smene", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cow.ambient": "Mungulâ di vacje", "subtitles.entity.cow.death": "Vacje e mûr", "subtitles.entity.cow.hurt": "<PERSON><PERSON><PERSON> feri<PERSON>", "subtitles.entity.cow.milk": "<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.activate": "<PERSON><PERSON>", "subtitles.entity.creaking.ambient": "<PERSON><PERSON> al criche", "subtitles.entity.creaking.attack": "<PERSON><PERSON> al atache", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON> si calme", "subtitles.entity.creaking.death": "<PERSON><PERSON> si fruçone", "subtitles.entity.creaking.freeze": "<PERSON><PERSON> si ferme", "subtitles.entity.creaking.spawn": "Cric al comparis", "subtitles.entity.creaking.sway": "<PERSON><PERSON>", "subtitles.entity.creaking.twitch": "<PERSON><PERSON> si plee", "subtitles.entity.creaking.unfreeze": "<PERSON><PERSON> si môf", "subtitles.entity.creeper.death": "Creeper al mûr", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>t", "subtitles.entity.creeper.primed": "Creeper al cise", "subtitles.entity.dolphin.ambient": "Suns e siviladis di dolfin", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON> di dolfin", "subtitles.entity.dolphin.attack": "<PERSON><PERSON><PERSON> al atache", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.dolphin.eat": "<PERSON><PERSON><PERSON> al man<PERSON>je", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.jump": "Salt di dolfin", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON> al zuie", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON> al svuacare", "subtitles.entity.dolphin.swim": "Dolfin al nade", "subtitles.entity.donkey.ambient": "<PERSON>s al rone", "subtitles.entity.donkey.angry": "<PERSON><PERSON>", "subtitles.entity.donkey.chest": "Baûl picjât sù sul mus", "subtitles.entity.donkey.death": "<PERSON><PERSON> al mûr", "subtitles.entity.donkey.eat": "<PERSON><PERSON> al mangje", "subtitles.entity.donkey.hurt": "<PERSON><PERSON>", "subtitles.entity.donkey.jump": "Mus al salte", "subtitles.entity.drowned.ambient": "Clucugnâ di inneât", "subtitles.entity.drowned.ambient_water": "Dromedari al clucugne", "subtitles.entity.drowned.death": "Inneât al mûr", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.drowned.shoot": "Inneât al tire la fossigne", "subtitles.entity.drowned.step": "Inneât al cjamine", "subtitles.entity.drowned.swim": "Inneât al nade", "subtitles.entity.egg.throw": "Ûf tirât", "subtitles.entity.elder_guardian.ambient": "Vuardian veteran si lamente", "subtitles.entity.elder_guardian.ambient_land": "<PERSON><PERSON><PERSON> <PERSON> vuardian veteran", "subtitles.entity.elder_guardian.curse": "Vuardian veteran al <PERSON>", "subtitles.entity.elder_guardian.death": "Vuardian veteran al mûr", "subtitles.entity.elder_guardian.flop": "Vuardian veteran si smene", "subtitles.entity.elder_guardian.hurt": "Vuardian veteran fer<PERSON><PERSON>", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.ender_dragon.flap": "<PERSON><PERSON><PERSON> al bat lis alis", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON> al atache", "subtitles.entity.ender_eye.death": "Voli di ender al cole", "subtitles.entity.ender_eye.launch": "Voli di ender tirât", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> di ender tirade", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> al fâs “vwoop”", "subtitles.entity.enderman.death": "<PERSON><PERSON> al mûr", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> fer<PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON> al berle", "subtitles.entity.enderman.stare": "<PERSON><PERSON> al berle", "subtitles.entity.enderman.teleport": "<PERSON><PERSON> si teletraspuarte", "subtitles.entity.endermite.ambient": "Endermite al strisse", "subtitles.entity.endermite.death": "Endermite al mûr", "subtitles.entity.endermite.hurt": "Endermite ferît", "subtitles.entity.evoker.ambient": "Evocadôr al murmuie", "subtitles.entity.evoker.cast_spell": "Evocadôr al tire magjiis", "subtitles.entity.evoker.celebrate": "Evocadôr al esulte", "subtitles.entity.evoker.death": "Evocadôr al mûr", "subtitles.entity.evoker.hurt": "Evocad<PERSON><PERSON>", "subtitles.entity.evoker.prepare_attack": "Evocadôr al prepare un atac", "subtitles.entity.evoker.prepare_summon": "Evocadôr al pronte une evocazion", "subtitles.entity.evoker.prepare_wololo": "Evocadôr al prepare un incjantesim", "subtitles.entity.evoker_fangs.attack": "Morseade cui dincj", "subtitles.entity.experience_orb.pickup": "Esperience cjapade sù", "subtitles.entity.firework_rocket.blast": "Une fusete pirotecniche e sclope", "subtitles.entity.firework_rocket.launch": "Fusete pirotecniche tirade", "subtitles.entity.firework_rocket.twinkle": "Une fusete pirotecniche e slusigne", "subtitles.entity.fish.swim": "Sclipignadis di aghe", "subtitles.entity.fishing_bobber.retrieve": "Flotant recuper<PERSON>t", "subtitles.entity.fishing_bobber.splash": "Flotant al sclipigne", "subtitles.entity.fishing_bobber.throw": "Flotant tirât", "subtitles.entity.fox.aggro": "<PERSON>lp si suste", "subtitles.entity.fox.ambient": "Bolp e joiole", "subtitles.entity.fox.bite": "Bolp e muart", "subtitles.entity.fox.death": "<PERSON>l<PERSON> e mûr", "subtitles.entity.fox.eat": "Bolp e mangje", "subtitles.entity.fox.hurt": "<PERSON><PERSON><PERSON> feride", "subtitles.entity.fox.screech": "Bolp e vuiche", "subtitles.entity.fox.sleep": "Bolp e roncee", "subtitles.entity.fox.sniff": "Bolp e nase", "subtitles.entity.fox.spit": "Bolp e spude", "subtitles.entity.fox.teleport": "Bolp si teletraspuarte", "subtitles.entity.frog.ambient": "Crot al graçole", "subtitles.entity.frog.death": "<PERSON><PERSON> al mûr", "subtitles.entity.frog.eat": "<PERSON><PERSON> al mangje", "subtitles.entity.frog.hurt": "<PERSON><PERSON> fer<PERSON>", "subtitles.entity.frog.lay_spawn": "Crot al ove", "subtitles.entity.frog.long_jump": "Crot al salte", "subtitles.entity.generic.big_fall": "Al è colât alc", "subtitles.entity.generic.burn": "Al bruse alc", "subtitles.entity.generic.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.drink": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.explode": "Esplosion", "subtitles.entity.generic.extinguish_fire": "Fûc distudât", "subtitles.entity.generic.hurt": "Alc si è ferît", "subtitles.entity.generic.small_fall": "Piçule colade", "subtitles.entity.generic.splash": "Sclipignade di aghe", "subtitles.entity.generic.swim": "Nadad<PERSON>", "subtitles.entity.generic.wind_burst": "Cjarie di aiar e sclope", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> si lagne", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> al atache", "subtitles.entity.ghastling.ambient": "G<PERSON><PERSON><PERSON> al grugjule", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON><PERSON> al mûr", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON><PERSON> fer<PERSON>", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON><PERSON> al comparìs", "subtitles.entity.glow_item_frame.add_item": "<PERSON><PERSON><PERSON>s luminess<PERSON> jemp<PERSON>", "subtitles.entity.glow_item_frame.break": "Curn<PERSON>s luminessente distrute", "subtitles.entity.glow_item_frame.place": "Curnîs luminessente plaçade", "subtitles.entity.glow_item_frame.remove_item": "Curnîs luminessente disvuedade", "subtitles.entity.glow_item_frame.rotate_item": "Ogjet insuazât al ven zirât", "subtitles.entity.glow_squid.ambient": "Calamâr luminessent al nade", "subtitles.entity.glow_squid.death": "Calamâr luminessent al mûr", "subtitles.entity.glow_squid.hurt": "Cal<PERSON><PERSON>r luminess<PERSON> fer<PERSON>t", "subtitles.entity.glow_squid.squirt": "Calamâr luminessent al sborfe l'ingjustri", "subtitles.entity.goat.ambient": "Cjavre e bebee", "subtitles.entity.goat.death": "Cjavre e mûr", "subtitles.entity.goat.eat": "Cjavre e mangje", "subtitles.entity.goat.horn_break": "<PERSON><PERSON><PERSON> e piert un cuar", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> feride", "subtitles.entity.goat.long_jump": "Cjavre e salte", "subtitles.entity.goat.milk": "Cjavre molzude", "subtitles.entity.goat.prepare_ram": "Cjavre e çoncone", "subtitles.entity.goat.ram_impact": "Cjavre e cjarie", "subtitles.entity.goat.screaming.ambient": "Cjavre e vose", "subtitles.entity.goat.step": "Cjavre e cjamine", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON> si lamente", "subtitles.entity.guardian.ambient_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON>ache", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.guardian.flop": "<PERSON><PERSON><PERSON> si smene", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.ambient": "G<PERSON><PERSON> legri al cjantuce", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON> legri al mûr", "subtitles.entity.happy_ghast.equip": "<PERSON>mb<PERSON><PERSON><PERSON> peade", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON> legri al è pront", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON>t legri si ferme", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON> legri ferît", "subtitles.entity.happy_ghast.unequip": "<PERSON>mb<PERSON><PERSON><PERSON> disleade", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> al <PERSON>ne", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> al rugne rabiôs", "subtitles.entity.hoglin.attack": "<PERSON><PERSON><PERSON> al atache", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON>glin si trasforme in zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> al <PERSON>sse", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> al cjamine", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON> al <PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON> al <PERSON>", "subtitles.entity.horse.armor": "Furniment dal cjaval metût sù", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON> al mangje", "subtitles.entity.horse.gallop": "Galop", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON> al salte", "subtitles.entity.horse.saddle": "<PERSON><PERSON>", "subtitles.entity.husk.ambient": "Zombi sec si lamente", "subtitles.entity.husk.converted_to_zombie": "Zombi sec si trasforme in zombi", "subtitles.entity.husk.death": "Zombi sec al mûr", "subtitles.entity.husk.hurt": "Zombi sec ferît", "subtitles.entity.illusioner.ambient": "Ilusionist al mur<PERSON><PERSON>", "subtitles.entity.illusioner.cast_spell": "Ilusionist al tire une magjie", "subtitles.entity.illusioner.death": "Ilusionist al mûr", "subtitles.entity.illusioner.hurt": "Ilusionist <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.mirror_move": "Ilusionist si teletras<PERSON>arte", "subtitles.entity.illusioner.prepare_blindness": "Ilusionist al prepare un svuarbament", "subtitles.entity.illusioner.prepare_mirror": "Ilusionist al prepare une ilusion", "subtitles.entity.iron_golem.attack": "Go<PERSON> di fier al atache", "subtitles.entity.iron_golem.damage": "Golem di fier si romp", "subtitles.entity.iron_golem.death": "Golem di fier al mûr", "subtitles.entity.iron_golem.hurt": "Golem di fier ferît", "subtitles.entity.iron_golem.repair": "Golem di fier comedât", "subtitles.entity.item.break": "Ogjet distrut", "subtitles.entity.item.pickup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON><PERSON> distrute", "subtitles.entity.item_frame.place": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.remove_item": "Curnîs disvuedade", "subtitles.entity.item_frame.rotate_item": "Ogjet insuazât al ven zirât", "subtitles.entity.leash_knot.break": "Sguinçâ<PERSON> g<PERSON>", "subtitles.entity.leash_knot.place": "S<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.lightning_bolt.impact": "Saete colade", "subtitles.entity.lightning_bolt.thunder": "Ton al rimbombe", "subtitles.entity.llama.ambient": "<PERSON> al bebee", "subtitles.entity.llama.angry": "<PERSON> al bebee rabiôs", "subtitles.entity.llama.chest": "Baûl picj<PERSON>t sù sul lama", "subtitles.entity.llama.death": "<PERSON>û<PERSON>", "subtitles.entity.llama.eat": "<PERSON> al <PERSON>", "subtitles.entity.llama.hurt": "<PERSON>", "subtitles.entity.llama.spit": "<PERSON> al spude", "subtitles.entity.llama.step": "<PERSON> al cjamine", "subtitles.entity.llama.swag": "<PERSON> de<PERSON>", "subtitles.entity.magma_cube.death": "Cubi di magme al mûr", "subtitles.entity.magma_cube.hurt": "Cubi di magme ferît", "subtitles.entity.magma_cube.squish": "Cubi di magme al strisse", "subtitles.entity.minecart.inside": "Carel minerari al scriule", "subtitles.entity.minecart.inside_underwater": "Carel minerari al scriule sot aghe", "subtitles.entity.minecart.riding": "Carel in moviment", "subtitles.entity.mooshroom.convert": "Mooshroom si trasforme", "subtitles.entity.mooshroom.eat": "Mooshroom e mangje", "subtitles.entity.mooshroom.milk": "Mooshroom molzude", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom molzude in maniere suspiete", "subtitles.entity.mule.ambient": "Mûl al rone", "subtitles.entity.mule.angry": "Mûl al sgagnìs", "subtitles.entity.mule.chest": "Baûl picjât sù sul mûl", "subtitles.entity.mule.death": "Mûl al mûr", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON> al mangje", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>", "subtitles.entity.mule.jump": "Mûl al salte", "subtitles.entity.painting.break": "Cuadri distrut", "subtitles.entity.painting.place": "Cuadri plaçâ<PERSON>", "subtitles.entity.panda.aggressive_ambient": "Panda al sbrufe", "subtitles.entity.panda.ambient": "Panda al sflade", "subtitles.entity.panda.bite": "Panda al muart", "subtitles.entity.panda.cant_breed": "Panda al zem", "subtitles.entity.panda.death": "Panda al mûr", "subtitles.entity.panda.eat": "Panda al mangje", "subtitles.entity.panda.hurt": "<PERSON><PERSON> fer<PERSON>", "subtitles.entity.panda.pre_sneeze": "<PERSON><PERSON><PERSON> dal panda al pice", "subtitles.entity.panda.sneeze": "Panda al starnudìs", "subtitles.entity.panda.step": "Panda al cjamine", "subtitles.entity.panda.worried_ambient": "Panda al mugule", "subtitles.entity.parrot.ambient": "Pampagal al cjacare", "subtitles.entity.parrot.death": "Pampagal al mûr", "subtitles.entity.parrot.eats": "Pampagal al mangje", "subtitles.entity.parrot.fly": "Pampagal al svolampe", "subtitles.entity.parrot.hurts": "Pam<PERSON><PERSON> ferît", "subtitles.entity.parrot.imitate.blaze": "Pampagal al respire come une flamade", "subtitles.entity.parrot.imitate.bogged": "Pampagal al imite il criçâ dal paludan", "subtitles.entity.parrot.imitate.breeze": "Pampa<PERSON> al sofle come une brise", "subtitles.entity.parrot.imitate.creaking": "Pampagal al criche", "subtitles.entity.parrot.imitate.creeper": "Pampagal al cise come un creeper", "subtitles.entity.parrot.imitate.drowned": "Pampagal al clucugne come un inneât", "subtitles.entity.parrot.imitate.elder_guardian": "Pampagal al imite un vuardian", "subtitles.entity.parrot.imitate.ender_dragon": "Pampa<PERSON> al rugne come un drâc", "subtitles.entity.parrot.imitate.endermite": "Pampagal al imite i endermites", "subtitles.entity.parrot.imitate.evoker": "Pampagal al murmuie", "subtitles.entity.parrot.imitate.ghast": "Pampagal al imite un ghast", "subtitles.entity.parrot.imitate.guardian": "Pampagal su lamente", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON> al rugne come un hoglin", "subtitles.entity.parrot.imitate.husk": "Pampagal al imite un zombi sec", "subtitles.entity.parrot.imitate.illusioner": "Pampagal al murmuie", "subtitles.entity.parrot.imitate.magma_cube": "Pampagal al imite un cubi di magma", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON> al strît come une fantasime", "subtitles.entity.parrot.imitate.piglin": "Pampagal al sbrufe come piglin", "subtitles.entity.parrot.imitate.piglin_brute": "Pampagal al sburfe", "subtitles.entity.parrot.imitate.pillager": "Pampagal al murmuie", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON> al rugne come un devastadôr", "subtitles.entity.parrot.imitate.shulker": "Pampagal al imite un shulker", "subtitles.entity.parrot.imitate.silverfish": "Pampagal al sivile", "subtitles.entity.parrot.imitate.skeleton": "Pampagal al imite il criçâ dal scheletri", "subtitles.entity.parrot.imitate.slime": "Pampagal al imite il strissâ de gjeladine", "subtitles.entity.parrot.imitate.spider": "Pampa<PERSON> al scriule come un ragn", "subtitles.entity.parrot.imitate.stray": "Pampagal al imite il criçâ di un scheletri vagabont", "subtitles.entity.parrot.imitate.vex": "Pampagal al imite un vex", "subtitles.entity.parrot.imitate.vindicator": "Pampagal al bruntule come un svindicadôr", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON><PERSON> si lamente come une vuardie", "subtitles.entity.parrot.imitate.witch": "<PERSON>pa<PERSON> al riduce come une strie", "subtitles.entity.parrot.imitate.wither": "Pampagal si infurie", "subtitles.entity.parrot.imitate.wither_skeleton": "Pampagal al imite il criçâ dal wither", "subtitles.entity.parrot.imitate.zoglin": "Pampagal al rugne", "subtitles.entity.parrot.imitate.zombie": "Pampagal al imite il lament di un zombi", "subtitles.entity.parrot.imitate.zombie_villager": "Pampagal al imite il lament di un paisan zombi", "subtitles.entity.phantom.ambient": "Fantasime e fâs strîts", "subtitles.entity.phantom.bite": "Fantasime e muart", "subtitles.entity.phantom.death": "Fantasime e mûr", "subtitles.entity.phantom.flap": "Fantasime e sbat lis alis", "subtitles.entity.phantom.hurt": "Fantasime feride", "subtitles.entity.phantom.swoop": "Fantasime e va jù in picade", "subtitles.entity.pig.ambient": "<PERSON><PERSON><PERSON><PERSON> al rugne", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON><PERSON> al mûr", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pig.saddle": "<PERSON><PERSON> metude sù", "subtitles.entity.piglin.admiring_item": "Piglin al amire un ogjet", "subtitles.entity.piglin.ambient": "Piglin al sbrufe", "subtitles.entity.piglin.angry": "Piglin al sbrufe rabiôs", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> al esulte", "subtitles.entity.piglin.converted_to_zombified": "Piglin si trasforme in piglin zombificât", "subtitles.entity.piglin.death": "<PERSON><PERSON> al mûr", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> fer<PERSON>", "subtitles.entity.piglin.jealous": "<PERSON>lin al sbrufe cun invidie", "subtitles.entity.piglin.retreat": "Piglin al ricesse", "subtitles.entity.piglin.step": "<PERSON><PERSON> al cjamine", "subtitles.entity.piglin_brute.ambient": "Piglin brutâl al sbrufe", "subtitles.entity.piglin_brute.angry": "Piglin brutâl al sbrufe rabiôs", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin brutâl si trasforme in piglin zombificât", "subtitles.entity.piglin_brute.death": "<PERSON>lin brutâl al mûr", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> brut<PERSON><PERSON> fer<PERSON>", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON> brutâl al cjamine", "subtitles.entity.pillager.ambient": "Murmuiâ di sachizadôr", "subtitles.entity.pillager.celebrate": "Sachizadôr al esulte", "subtitles.entity.pillager.death": "Sachizadôr al mûr", "subtitles.entity.pillager.hurt": "<PERSON>chi<PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.crit": "Colp critic", "subtitles.entity.player.attack.knockback": "Atac cun cuintricolp", "subtitles.entity.player.attack.strong": "Atac fuart", "subtitles.entity.player.attack.sweep": "Atac scoreât", "subtitles.entity.player.attack.weak": "Atac debil", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> al mûr", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> si inglace", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON><PERSON><PERSON> si innee", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON><PERSON><PERSON> al bruse", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>t s<PERSON> di nivel", "subtitles.entity.player.teleport": "Zuiad<PERSON>r si teletraspuarte", "subtitles.entity.polar_bear.ambient": "Mungulâ di ors polâr", "subtitles.entity.polar_bear.ambient_baby": "Piçul di ors polâr al mungule", "subtitles.entity.polar_bear.death": "<PERSON>s polâr al mûr", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON> pol<PERSON> fer<PERSON>t", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON> polâr al rugne", "subtitles.entity.potion.splash": "<PERSON>ce si fruçone", "subtitles.entity.potion.throw": "<PERSON><PERSON> tirade", "subtitles.entity.puffer_fish.blow_out": "Pes-bale si disglonfe", "subtitles.entity.puffer_fish.blow_up": "Pes-bale si sglonfe", "subtitles.entity.puffer_fish.death": "Pes-bale al mûr", "subtitles.entity.puffer_fish.flop": "Pes-bale si remene", "subtitles.entity.puffer_fish.hurt": "Pes-bale ferît", "subtitles.entity.puffer_fish.sting": "Pes-bale al beche", "subtitles.entity.rabbit.ambient": "Zig<PERSON> di cunin", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON> al atache", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.jump": "Cunin al salte", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.attack": "Devastad<PERSON>r al muart", "subtitles.entity.ravager.celebrate": "Devastadôr al esulte", "subtitles.entity.ravager.death": "Dev<PERSON><PERSON><PERSON><PERSON> al mûr", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.roar": "Devast<PERSON><PERSON><PERSON> al ruze", "subtitles.entity.ravager.step": "Dev<PERSON><PERSON><PERSON><PERSON> al <PERSON>", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.salmon.death": "Salmon al mûr", "subtitles.entity.salmon.flop": "<PERSON> si remene", "subtitles.entity.salmon.hurt": "<PERSON> ferît", "subtitles.entity.sheep.ambient": "Bebeâ di piore", "subtitles.entity.sheep.death": "<PERSON><PERSON> e mûr", "subtitles.entity.sheep.hurt": "<PERSON><PERSON> feride", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> al <PERSON>le", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> si siere", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> si vierç", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> al atache", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> si teletraspuarte", "subtitles.entity.shulker_bullet.hit": "Palotule di shulker e tone", "subtitles.entity.shulker_bullet.hurt": "Palotule di shulker si romp", "subtitles.entity.silverfish.ambient": "Pessut di arint al sivile", "subtitles.entity.silverfish.death": "Pessut di arint al mûr", "subtitles.entity.silverfish.hurt": "<PERSON><PERSON><PERSON> di arint ferît", "subtitles.entity.skeleton.ambient": "Criçâ di scheletri", "subtitles.entity.skeleton.converted_to_stray": "Scheletri si trasforme in scheletri vagabont", "subtitles.entity.skeleton.death": "<PERSON><PERSON><PERSON><PERSON> al mûr", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON><PERSON>er<PERSON>", "subtitles.entity.skeleton.shoot": "Atac di scheletri", "subtitles.entity.skeleton_horse.ambient": "Scheletri di cjaval al berle", "subtitles.entity.skeleton_horse.death": "Scheletri di cjaval al mûr", "subtitles.entity.skeleton_horse.hurt": "Scheletri di c<PERSON> fer<PERSON>", "subtitles.entity.skeleton_horse.jump_water": "Scheletri di cjaval al salte", "subtitles.entity.skeleton_horse.swim": "Scheletri di cjaval al nade", "subtitles.entity.slime.attack": "<PERSON><PERSON><PERSON><PERSON> e atache", "subtitles.entity.slime.death": "G<PERSON><PERSON><PERSON> e mûr", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON><PERSON> feride", "subtitles.entity.slime.squish": "Gjeladine e strisse", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON><PERSON> al mûr", "subtitles.entity.sniffer.digging": "Nasadôr al sgarfe", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON><PERSON> si jeve", "subtitles.entity.sniffer.drop_seed": "Na<PERSON><PERSON><PERSON> al mole une semence", "subtitles.entity.sniffer.eat": "<PERSON><PERSON><PERSON><PERSON>je", "subtitles.entity.sniffer.egg_crack": "Ûf di nasadôr si crepe", "subtitles.entity.sniffer.egg_hatch": "Ûf di nasadôr si vierç", "subtitles.entity.sniffer.happy": "Na<PERSON><PERSON><PERSON> si <PERSON>", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON><PERSON> al nase", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.sniffing": "<PERSON><PERSON><PERSON><PERSON> al usme", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON><PERSON> al <PERSON>", "subtitles.entity.snow_golem.death": "Golem di nêf al mûr", "subtitles.entity.snow_golem.hurt": "Golem di nê<PERSON> fer<PERSON>t", "subtitles.entity.snowball.throw": "<PERSON><PERSON> di nêf tirade", "subtitles.entity.spider.ambient": "Sivilâ di ragn", "subtitles.entity.spider.death": "<PERSON>gn al mûr", "subtitles.entity.spider.hurt": "<PERSON><PERSON> fer<PERSON>", "subtitles.entity.squid.ambient": "Nadâ di calamâr", "subtitles.entity.squid.death": "Calam<PERSON><PERSON> al mûr", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.squid.squirt": "Calamâr al sborfe l'ingjustri", "subtitles.entity.stray.ambient": "Criçâ di scheletri vagabont", "subtitles.entity.stray.death": "Scheletri vagabont al mûr", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON><PERSON> vagabont fer<PERSON>t", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.strider.eat": "<PERSON><PERSON><PERSON> al mangje", "subtitles.entity.strider.happy": "Gorghizade di Strider", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.idle": "Piulâ di Strider", "subtitles.entity.strider.retreat": "Strider al <PERSON>sse", "subtitles.entity.tadpole.death": "Cudul al mûr", "subtitles.entity.tadpole.flop": "Cudul si smene", "subtitles.entity.tadpole.grow_up": "Cudul al cres", "subtitles.entity.tadpole.hurt": "Cudul ferî<PERSON>", "subtitles.entity.tnt.primed": "TNT lescjât", "subtitles.entity.tropical_fish.death": "Pes tropicâl al mûr", "subtitles.entity.tropical_fish.flop": "Pes tropicâl si sbatacole", "subtitles.entity.tropical_fish.hurt": "<PERSON><PERSON> t<PERSON> fer<PERSON>", "subtitles.entity.turtle.ambient_land": "Piulâ di copasse", "subtitles.entity.turtle.death": "Copasse e mûr", "subtitles.entity.turtle.death_baby": "Piçul di copasse al mûr", "subtitles.entity.turtle.egg_break": "Ûf di copasse si romp", "subtitles.entity.turtle.egg_crack": "Ûf di copasse si crepe", "subtitles.entity.turtle.egg_hatch": "Ûf di copasse si vierç", "subtitles.entity.turtle.hurt": "Copasse feride", "subtitles.entity.turtle.hurt_baby": "Piçul di copasse ferît", "subtitles.entity.turtle.lay_egg": "Copasse e ove", "subtitles.entity.turtle.shamble": "Copasse e sgripie", "subtitles.entity.turtle.shamble_baby": "Piçul di copasse al sgripie", "subtitles.entity.turtle.swim": "Copasse e nade", "subtitles.entity.vex.ambient": "Vex si suste", "subtitles.entity.vex.charge": "Vex al vose", "subtitles.entity.vex.death": "Vex al mûr", "subtitles.entity.vex.hurt": "Vex ferît", "subtitles.entity.villager.ambient": "Murmuiâ di paisan", "subtitles.entity.villager.celebrate": "Paisan al esulte", "subtitles.entity.villager.death": "Paisan al mûr", "subtitles.entity.villager.hurt": "Paisan ferît", "subtitles.entity.villager.no": "Paisan al refude", "subtitles.entity.villager.trade": "Paisan al marcjadante", "subtitles.entity.villager.work_armorer": "Armarûl di coracis al lavore", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON><PERSON><PERSON><PERSON> al lavore", "subtitles.entity.villager.work_cartographer": "Cartograf al lavore", "subtitles.entity.villager.work_cleric": "Cleric al lavore", "subtitles.entity.villager.work_farmer": "Contadin al lavore", "subtitles.entity.villager.work_fisherman": "Pescjadôr al lavore", "subtitles.entity.villager.work_fletcher": "Freçâr al lavore", "subtitles.entity.villager.work_leatherworker": "Cuincepiels al lavore", "subtitles.entity.villager.work_librarian": "Bibliotecari al lavore", "subtitles.entity.villager.work_mason": "Mured<PERSON>r al lavore", "subtitles.entity.villager.work_shepherd": "Pastôr al lavore", "subtitles.entity.villager.work_toolsmith": "Fari di imprescj al lavore", "subtitles.entity.villager.work_weaponsmith": "Armarûl al lavore", "subtitles.entity.villager.yes": "Paisan al acete", "subtitles.entity.vindicator.ambient": "Svindicadôr al bruntule", "subtitles.entity.vindicator.celebrate": "Svindicadôr al esulte", "subtitles.entity.vindicator.death": "Svindicadô<PERSON> al mûr", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.ambient": "Murmuiâ di vendidôr ambulant", "subtitles.entity.wandering_trader.death": "Vendid<PERSON><PERSON> ambulant al mûr", "subtitles.entity.wandering_trader.disappeared": "Vendidôr ambulant al sparìs", "subtitles.entity.wandering_trader.drink_milk": "Vendid<PERSON><PERSON> ambulant al bêf lat", "subtitles.entity.wandering_trader.drink_potion": "Vendid<PERSON><PERSON> ambulant al bêf une pozion", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> ambulant fer<PERSON>t", "subtitles.entity.wandering_trader.no": "Vendidôr ambulant al refude", "subtitles.entity.wandering_trader.reappeared": "Vendidôr ambulant al comparìs", "subtitles.entity.wandering_trader.trade": "Vendid<PERSON><PERSON> ambulant al marcjadante", "subtitles.entity.wandering_trader.yes": "Vendidôr ambulant al acete", "subtitles.entity.warden.agitated": "<PERSON><PERSON><PERSON> e zem in mût gnervôs", "subtitles.entity.warden.ambient": "<PERSON><PERSON><PERSON> si lamente", "subtitles.entity.warden.angry": "<PERSON><PERSON>ie si inrabie", "subtitles.entity.warden.attack_impact": "<PERSON><PERSON><PERSON> e <PERSON>lpìs", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON> e mûr", "subtitles.entity.warden.dig": "Vuardie e s<PERSON>fe", "subtitles.entity.warden.emerge": "V<PERSON><PERSON> e salte fûr", "subtitles.entity.warden.heartbeat": "Cûr de vuardie al bat", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON> feride", "subtitles.entity.warden.listening": "<PERSON><PERSON><PERSON> e scolte", "subtitles.entity.warden.listening_angry": "<PERSON><PERSON><PERSON> e scolte in mût gnervôs", "subtitles.entity.warden.nearby_close": "<PERSON><PERSON><PERSON> si svicine", "subtitles.entity.warden.nearby_closer": "V<PERSON>ie e va indenant", "subtitles.entity.warden.nearby_closest": "<PERSON><PERSON><PERSON> si svicine une vore", "subtitles.entity.warden.roar": "<PERSON><PERSON><PERSON> e rugne", "subtitles.entity.warden.sniff": "<PERSON><PERSON><PERSON> e nase", "subtitles.entity.warden.sonic_boom": "V<PERSON><PERSON> e tire un ton sonic", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON> e cjarie", "subtitles.entity.warden.step": "<PERSON><PERSON><PERSON> e cjamine", "subtitles.entity.warden.tendril_clicks": "Antenis de vuardie a sclopetin", "subtitles.entity.wind_charge.throw": "Cjarie di aiar e svole", "subtitles.entity.wind_charge.wind_burst": "Cjarie di aiar e sclope", "subtitles.entity.witch.ambient": "Riduçâ di strie", "subtitles.entity.witch.celebrate": "Strie e esulte", "subtitles.entity.witch.death": "Strie e mûr", "subtitles.entity.witch.drink": "Strie e bêf", "subtitles.entity.witch.hurt": "Strie feride", "subtitles.entity.witch.throw": "Strie e tire une pozion", "subtitles.entity.wither.ambient": "Wither si infurie", "subtitles.entity.wither.death": "<PERSON><PERSON> al mûr", "subtitles.entity.wither.hurt": "<PERSON><PERSON> <PERSON>er<PERSON>", "subtitles.entity.wither.shoot": "Wither al atache", "subtitles.entity.wither.spawn": "Wither si libare", "subtitles.entity.wither_skeleton.ambient": "<PERSON><PERSON><PERSON><PERSON> dal scheletri wither", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON><PERSON><PERSON> wither al mûr", "subtitles.entity.wither_skeleton.hurt": "<PERSON><PERSON><PERSON><PERSON> wither fer<PERSON>t", "subtitles.entity.wolf.ambient": "Sfladâ di lôf", "subtitles.entity.wolf.bark": "<PERSON><PERSON><PERSON> al baie", "subtitles.entity.wolf.death": "<PERSON><PERSON><PERSON> al <PERSON>ûr", "subtitles.entity.wolf.growl": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.pant": "Sfladâ di lôf", "subtitles.entity.wolf.shake": "<PERSON><PERSON><PERSON> si scjasse", "subtitles.entity.wolf.whine": "<PERSON><PERSON><PERSON> si lamente", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> al rugne", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> al rugne rabiôs", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> al atache", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> al cjamine", "subtitles.entity.zombie.ambient": "<PERSON><PERSON>i", "subtitles.entity.zombie.attack_wooden_door": "Colps su puarte che e clope", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON>rte butade jù", "subtitles.entity.zombie.converted_to_drowned": "Zombi si trasforme in inneât", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.zombie.destroy_egg": "Ûf di copasse talpass<PERSON>t", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>", "subtitles.entity.zombie.infect": "Zombi al infete", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON><PERSON> z<PERSON> al berle", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON> al mûr", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON><PERSON> fer<PERSON>", "subtitles.entity.zombie_villager.ambient": "Laments di paisan zombi", "subtitles.entity.zombie_villager.converted": "Paisan zombi al vose", "subtitles.entity.zombie_villager.cure": "Paisan zombi si remene", "subtitles.entity.zombie_villager.death": "Paisan zombi al mûr", "subtitles.entity.zombie_villager.hurt": "Paisan zombi ferît", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON> zombific<PERSON> al rugne", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON> zombificât al rugne rabiôs", "subtitles.entity.zombified_piglin.death": "<PERSON><PERSON> zombificât al mûr", "subtitles.entity.zombified_piglin.hurt": "<PERSON><PERSON> z<PERSON><PERSON> fer<PERSON>", "subtitles.event.mob_effect.bad_omen": "Malauguri al cjape possès", "subtitles.event.mob_effect.raid_omen": "Incursion e menace chi dongje", "subtitles.event.mob_effect.trial_omen": "Prove dal malauguri e menace chi dongje", "subtitles.event.raid.horn": "Rivoc di cuar di vuere", "subtitles.item.armor.equip": "<PERSON><PERSON><PERSON> metude sù", "subtitles.item.armor.equip_chain": "Armadure di maie di fier e cingline", "subtitles.item.armor.equip_diamond": "Armadure di diamant e sdrondene", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON><PERSON> a bisiin", "subtitles.item.armor.equip_gold": "Armadure di aur e cingline", "subtitles.item.armor.equip_iron": "Armadure di fier e sdrondene", "subtitles.item.armor.equip_leather": "Armadure di corean e bisie", "subtitles.item.armor.equip_netherite": "Armadure di netherite e sdrondene", "subtitles.item.armor.equip_turtle": "Sun çondar di armadure di copasse", "subtitles.item.armor.equip_wolf": "Armadure di lôf leade", "subtitles.item.armor.unequip_wolf": "Armadure di lôf si dislee", "subtitles.item.axe.scrape": "Manarie e russe", "subtitles.item.axe.strip": "Manarie e scuarce", "subtitles.item.axe.wax_off": "<PERSON><PERSON>", "subtitles.item.bone_meal.use": "Farine di vues e bisie", "subtitles.item.book.page_turn": "Sfruiâ di pagjine zirade", "subtitles.item.book.put": "Ton di un libri plaçât", "subtitles.item.bottle.empty": "Butilie disvuedade", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.generic": "Scovetament", "subtitles.item.brush.brushing.gravel": "Scovetant glerie", "subtitles.item.brush.brushing.gravel.complete": "G<PERSON>ie scovetade", "subtitles.item.brush.brushing.sand": "Scovetant savalon", "subtitles.item.brush.brushing.sand.complete": "Savalon scovetât", "subtitles.item.bucket.empty": "Seglot disvued<PERSON><PERSON>", "subtitles.item.bucket.fill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "Axolotl c<PERSON>â<PERSON> su", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON> c<PERSON>t tal seglot", "subtitles.item.bucket.fill_tadpole": "Cudul cjapât su", "subtitles.item.bundle.drop_contents": "Sa<PERSON> dis<PERSON><PERSON><PERSON>", "subtitles.item.bundle.insert": "Ogjet met<PERSON><PERSON> den<PERSON>", "subtitles.item.bundle.insert_fail": "Sacut plen", "subtitles.item.bundle.remove_one": "Ogjet tirât fûr", "subtitles.item.chorus_fruit.teleport": "Zuiad<PERSON>r si teletraspuarte", "subtitles.item.crop.plant": "Plante semenade", "subtitles.item.crossbow.charge": "<PERSON><PERSON><PERSON><PERSON> bales<PERSON>", "subtitles.item.crossbow.hit": "Foropade di frece", "subtitles.item.crossbow.load": "Balestre <PERSON>", "subtitles.item.crossbow.shoot": "<PERSON><PERSON><PERSON> di balestre", "subtitles.item.dye.use": "Tinture e colore", "subtitles.item.elytra.flying": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "Bale di fûc tirade", "subtitles.item.flintandsteel.use": "<PERSON><PERSON><PERSON>", "subtitles.item.glow_ink_sac.use": "Sache di ingjustri luminessent strucade", "subtitles.item.goat_horn.play": "<PERSON><PERSON><PERSON> di <PERSON>", "subtitles.item.hoe.till": "Sap<PERSON> di <PERSON>pe", "subtitles.item.honey_bottle.drink": "Glotude di mîl", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON>", "subtitles.item.horse_armor.unequip": "Furniment dal cjaval si dislee", "subtitles.item.ink_sac.use": "Sache di ingjustri strucade", "subtitles.item.lead.break": "Sguinçâl si romp", "subtitles.item.lead.tied": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.lead.untied": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.llama_carpet.unequip": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.lodestone_compass.lock": "Bussule magnetizade su magnetite", "subtitles.item.mace.smash_air": "Mace e fruçone", "subtitles.item.mace.smash_ground": "Mace e fruçone", "subtitles.item.nether_wart.plant": "Plante semenade", "subtitles.item.ominous_bottle.dispose": "Boce si romp", "subtitles.item.saddle.unequip": "<PERSON><PERSON>", "subtitles.item.shears.shear": "<PERSON><PERSON><PERSON><PERSON> dopra<PERSON>", "subtitles.item.shears.snip": "Fuarpiis a taçin", "subtitles.item.shield.block": "<PERSON><PERSON>", "subtitles.item.shovel.flatten": "Tiere batude cu la pale", "subtitles.item.spyglass.stop_using": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.spyglass.use": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.totem.use": "Totem ativât", "subtitles.item.trident.hit": "Fossigne e impire", "subtitles.item.trident.hit_ground": "Fossigne e vibre", "subtitles.item.trident.return": "Fossigne e torne indaûr", "subtitles.item.trident.riptide": "Fossigne e passe in bande", "subtitles.item.trident.throw": "Fossigne e cinglinâ", "subtitles.item.trident.thunder": "Fossigne e fâs tonâ il cîl", "subtitles.item.wolf_armor.break": "Armadure di lôf si romp", "subtitles.item.wolf_armor.crack": "Armadure di lôf si creve", "subtitles.item.wolf_armor.damage": "Armadure di lôf ruvinade", "subtitles.item.wolf_armor.repair": "Armadure di lôf comedade", "subtitles.particle.soul_escape": "Anime e scjampe", "subtitles.ui.cartography_table.take_result": "Mape dissegnade", "subtitles.ui.hud.bubble_pop": "<PERSON>ssig<PERSON><PERSON> daûr a calâ", "subtitles.ui.loom.take_result": "<PERSON><PERSON><PERSON>", "subtitles.ui.stonecutter.take_result": "Tai<PERSON><PERSON><PERSON> <PERSON> piere do<PERSON><PERSON>", "subtitles.weather.rain": "Ploie", "symlink_warning.message": "Cjariâ monts di cartelis cun colegaments simbolics al è pericolôs se no tu sâs ce che tu stâs fasint. Visite %s par savênt di plui.", "symlink_warning.message.pack": "Cjariâ pachets cun colegaments simbolics al è pericolôs se no tu sâs ce che tu stâs fasint. Visite %s par savênt di plui.", "symlink_warning.message.world": "Cjariâ monts di cartelis cun colegaments simbolics al è pericolôs se no tu sâs ce che tu stâs fasint. Visite %s par savênt di plui.", "symlink_warning.more_info": "Altris informazions", "symlink_warning.title": "La cartele dal mont e conten colegaments simbolics", "symlink_warning.title.pack": "I pachets zontâts a contegnin colegaments simbolics", "symlink_warning.title.world": "La cartele dal mont e conten colegaments simbolics", "team.collision.always": "Simpri", "team.collision.never": "<PERSON>", "team.collision.pushOtherTeams": "Sburte i membris di altris scuadris", "team.collision.pushOwnTeam": "<PERSON><PERSON><PERSON> i membris de tô scuadre", "team.notFound": "Scuadre '%s' no cognossude", "team.visibility.always": "Simpri", "team.visibility.hideForOtherTeams": "Plate pes altris scuadris", "team.visibility.hideForOwnTeam": "Plate pe tô scuadre", "team.visibility.never": "<PERSON>", "telemetry.event.advancement_made.description": "Rivâ a capî il contest dâur dal completament di un progrès nus jude a miorâ la secuence di zûc.", "telemetry.event.advancement_made.title": "Progrès realizât", "telemetry.event.game_load_times.description": "Misurant i timps di esecuzion des fasis di inviament dal zûc, o podarìn capî dulà che a coventin mioraments.", "telemetry.event.game_load_times.title": "<PERSON><PERSON> di c<PERSON>iament dal z<PERSON>c", "telemetry.event.optional": "%s (facoltatîf)", "telemetry.event.optional.disabled": "%s (facoltatîf) - disabilitât", "telemetry.event.performance_metrics.description": "Cognossi il profîl des prestazions complessivis di Minecraft nus jude a regolâ e otimizâ il zûc par une ample schirie di dispositîfs e sistemis operatîfs.\nLa version dal zûc e je includude par judânus a fâ il confront cul profîl di prestazion pes gnovis versions di Minecraft.", "telemetry.event.performance_metrics.title": "Metrichis di prestazion", "telemetry.event.required": "%s (necessari)", "telemetry.event.world_load_times.description": "Par nô al è impuartant capî trop timp che al vûl par jentrâ intun mont e cemût che chest al cambie cul timp a lâ. Par esemp<PERSON>, cuant che o zontìn gnovis funzionalitâts opûr o fasìn modifichis tecnichis, o vin dibisugne di viodi cemût che chest al influìs sul timp di cjariament.", "telemetry.event.world_load_times.title": "<PERSON><PERSON> di c<PERSON> dai monts", "telemetry.event.world_loaded.description": "Savê cemût che i zuiadôrs a zuin a Minecraft (come modalitât di zûc, client o servidôr modificâts e version di zûc) nus permet di miorâ il zûc dulà che al è plui impuartant par i zuiadôrs.\nL'event di \"Mont cjariât\" al è associât cul event \"Mont discjariât\" par misurâ trop timp che e je durade la session di zûc.", "telemetry.event.world_loaded.title": "Mont cjar<PERSON>", "telemetry.event.world_unloaded.description": "Chest event al è associât al event \"Mont cjariât\" par misurâ trop timp che e je durade la session dal mont.\nLa durade (in seconts e ticadis) e ven misurade cuant che une session dal mont e je finide (cuant che tu jessis al menù principâl o tu ti disconetis dal servidôr).", "telemetry.event.world_unloaded.title": "<PERSON>", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON> (ticadis)", "telemetry.property.advancement_id.title": "ID progrès", "telemetry.property.client_id.title": "ID client", "telemetry.property.client_modded.title": "Client modificât", "telemetry.property.dedicated_memory_kb.title": "Memorie dedicade (kB)", "telemetry.property.event_timestamp_utc.title": "Orari dal event (UTC)", "telemetry.property.frame_rate_samples.title": "Campions di frecuence dai fotograms (FPS)", "telemetry.property.game_mode.title": "Modalitât di zûc", "telemetry.property.game_version.title": "Version dal zûc", "telemetry.property.launcher_name.title": "Non dal inviadôr", "telemetry.property.load_time_bootstrap_ms.title": "Timp di inviament (miliseconts)", "telemetry.property.load_time_loading_overlay_ms.title": "<PERSON>p te videade di cjariament (miliseconts)", "telemetry.property.load_time_pre_window_ms.title": "Timp prime che si vierzi la videade (miliseconts)", "telemetry.property.load_time_total_time_ms.title": "<PERSON><PERSON> (miliseconts)", "telemetry.property.minecraft_session_id.title": "ID session di <PERSON><PERSON>", "telemetry.property.new_world.title": "Gnûf mont", "telemetry.property.number_of_samples.title": "<PERSON><PERSON><PERSON> campions", "telemetry.property.operating_system.title": "Sisteme operatîf", "telemetry.property.opt_in.title": "Reg<PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.property.platform.title": "Plateforme", "telemetry.property.realms_map_content.title": "Contignût de mape di Realms (non dal minizûc)", "telemetry.property.render_distance.title": "Distance di visualizâ", "telemetry.property.render_time_samples.title": "Campions dal timp di visualizazion", "telemetry.property.seconds_since_load.title": "<PERSON><PERSON> pass<PERSON>t dal cjariament dal mont (seconts)", "telemetry.property.server_modded.title": "<PERSON><PERSON><PERSON><PERSON>od<PERSON>", "telemetry.property.server_type.title": "<PERSON><PERSON><PERSON>", "telemetry.property.ticks_since_load.title": "<PERSON><PERSON> pass<PERSON>t dal cjariament dal mont (ticadis)", "telemetry.property.used_memory_samples.title": "Memorie RAM doprade", "telemetry.property.user_id.title": "ID utent", "telemetry.property.world_load_time_ms.title": "<PERSON><PERSON> di c<PERSON> dal mont (milliseconts)", "telemetry.property.world_session_id.title": "ID session dal mont", "telemetry_info.button.give_feedback": "Mande il to parê", "telemetry_info.button.privacy_statement": "Informative su la riservatece", "telemetry_info.button.show_data": "<PERSON>re i miei d<PERSON>ts", "telemetry_info.opt_in.description": "O doi il permès par inviâ dâts di telemetrie facoltatîfs", "telemetry_info.property_title": "<PERSON><PERSON><PERSON> includûts", "telemetry_info.screen.description": "La racuelte di chescj dâts nus jude a miorâ Minecraft mostrant lis direzions plui impuartantis pai zuiadôrs.\nTu puedis ancje inviâ il to parê par judânus a miorâ Minecraft.", "telemetry_info.screen.title": "Racuelte di dâts di telemetrie", "test.error.block_property_mismatch": "Si spietave che la proprietât %s e fos %s, ma e jere %s", "test.error.block_property_missing": "E mancje la proprietât dal bloc, e jere previodude che la proprietât %s e fos %s", "test.error.entity_property": "La entitât %s no à passât la prove: %s", "test.error.entity_property_details": "Prove falide pe entitât %s: %s, previodût: %s, al jere: %s", "test.error.expected_block": "Bloc previodût %s, vût %s", "test.error.expected_block_tag": "Bloc previodût in #%s, vût %s", "test.error.expected_container_contents": "Il contignidôr al varès di contignî: %s", "test.error.expected_container_contents_single": "Il contignidôr al varès di contignî un ugnul: %s", "test.error.expected_empty_container": "Il contignidôr al varès di jessi vueit", "test.error.expected_entity": "Previodût %s", "test.error.expected_entity_around": "Al jere previodût che %s al esistès tor ator di %s, %s, %s", "test.error.expected_entity_count": "E jere previodude la entitât %s di gjenar %s, cjatade %s", "test.error.expected_entity_data": "A jerin previodût che i dâts de entitât a fossin: %s, a jerin: %s", "test.error.expected_entity_data_predicate": "<PERSON><PERSON><PERSON><PERSON> corispondence dai dâts de entitât par %s", "test.error.expected_entity_effect": "Al jere previodût che %s al ves l'efiet %s %s", "test.error.expected_entity_having": "L'inventari de entitât al varès di contignî %s", "test.error.expected_entity_holding": "La entitât e varès di tignî %s", "test.error.expected_entity_in_test": "Te prove al jere previodût che %s al esistès", "test.error.expected_entity_not_touching": "Nol jere previodût che %s al tocjàs %s, %s, %s (relatîf: %s, %s, %s)", "test.error.expected_entity_touching": "Al jere previodût che %s al tocjàs %s, %s, %s (relatîf: %s, %s, %s)", "test.error.expected_item": "Al jere previodût un element di gjenar %s", "test.error.expected_items_count": "A jerin previodûts %s elements di gjenar %s, cjatâts %s", "test.error.fail": "Condizions di faliment sodisfatis", "test.error.invalid_block_type": "Cja<PERSON><PERSON>t gjenar di bloc inspietât: %s", "test.error.missing_block_entity": "E mancje la entitât dal bloc", "test.error.position": "%s a %s, %s, %s (relatîf: %s, %s, %s) ae ticade %s", "test.error.sequence.condition_already_triggered": "Condizion za ativade a %s", "test.error.sequence.condition_not_triggered": "Condizion no ativade", "test.error.sequence.invalid_tick": "Superât te ticade no valide: al jere previodût %s", "test.error.sequence.not_completed": "La prove e je scjadude prime dal completament de secuence", "test.error.set_biome": "Impussibil configurâ il biome pe prove", "test.error.spawn_failure": "Impussibil creâ la entitât %s", "test.error.state_not_equal": "<PERSON><PERSON>t sbali<PERSON>t. Si spietave %s, ma al jere %s", "test.error.structure.failure": "Impussibil plaçâ la struture di prove par %s", "test.error.tick": "%s su la ticade %s", "test.error.ticking_without_structure": "Prove di ticade prime di plaçâ la struture", "test.error.timeout.no_result": "No je passade opûr e je falide dentri di %s ticadis", "test.error.timeout.no_sequences_finished": "Nissune secuence e à finît dentri di %s ticadis", "test.error.too_many_entities": "Si spietave dome un %s tor ator di %s, %s, %s, ma a'nd è %s", "test.error.unexpected_block": "Nol jere previodût che il bloc al fos %s", "test.error.unexpected_entity": "Nol jere previodût che %s al esistès", "test.error.unexpected_item": "Nol jere previodût un element di gjenar %s", "test.error.unknown": "Er<PERSON>r interni no cognossût: %s", "test.error.value_not_equal": "Si spietave che %s al fos %s, ma al jere %s", "test.error.wrong_block_entity": "G<PERSON><PERSON> di entitât dal bloc sbaliât: %s", "test_block.error.missing": "Ae struture di prove e mancje il bloc %s", "test_block.error.too_many": "Masse blocs %s", "test_block.invalid_timeout": "<PERSON><PERSON> di scjadince no valit (%s): al scugne jessi un numar positîf di ticadis", "test_block.message": "Messaç:", "test_block.mode.accept": "Acete", "test_block.mode.fail": "<PERSON><PERSON><PERSON><PERSON>", "test_block.mode.log": "Regjistre", "test_block.mode.start": "Invie", "test_block.mode_info.accept": "Modalitât acetazion - Acete la riesside di (part di) une prove", "test_block.mode_info.fail": "Modalitât faliment - Falìs <PERSON>", "test_block.mode_info.log": "Modalitât regjistri - Regjistre un messaç", "test_block.mode_info.start": "Modalitât inizi - Il pont iniziâl di une prove", "test_instance.action.reset": "Ripristine e cjame", "test_instance.action.run": "Cjame e eseguìs", "test_instance.action.save": "Salve struture", "test_instance.description.batch": "Lot: %s", "test_instance.description.failed": "Falît: %s", "test_instance.description.function": "Funzion: %s", "test_instance.description.invalid_id": "ID prove no valit", "test_instance.description.no_test": "Nissune prove cjatade", "test_instance.description.structure": "Struture: %s", "test_instance.description.type": "Gjenar: %s", "test_instance.type.block_based": "Prove basade su blocs", "test_instance.type.function": "Prove di funzion integrade", "test_instance_block.entities": "Entitâts:", "test_instance_block.error.no_test": "Impussibil eseguî la istance di prove su %s, %s, %s viodût che no je stade definide nissune prove", "test_instance_block.error.no_test_structure": "Impussibil eseguî la istance di prove su %s, %s, %s viodût che no esist une struture di prove", "test_instance_block.error.unable_to_save": "Impussibil salvâ il model di struture di prove pe istance di prove su %s, %s, %s", "test_instance_block.invalid": "[no valit]", "test_instance_block.reset_success": "Ripristinament lât a bon fin pe prove: %s", "test_instance_block.rotation": "Rotazion:", "test_instance_block.size": "Dimension de struture di prove", "test_instance_block.starting": "Inviament de prove %s", "test_instance_block.test_id": "ID de istance di prove", "title.32bit.deprecation": "<PERSON><PERSON><PERSON><PERSON>t sisteme a 32 bit: chest al podarès impedîti di zuiâ in futûr, parcè che al coventarà un sisteme a 64 bit!", "title.32bit.deprecation.realms": "Ca di pôc a Minecraft i coventarà un sisteme a 64-bit, chest ti impedirà di zuiâ a Minecraft o di doprâ Realms su chest dispositîf. Tu varâs di anulâ a man il to abonament a Realms.", "title.32bit.deprecation.realms.check": "No sta mostrâ plui cheste videade", "title.32bit.deprecation.realms.header": "<PERSON><PERSON><PERSON><PERSON><PERSON> sisteme a 32 bit", "title.credits": "Dirits di autôr di Mojang AB. Vietât distribuî!", "title.multiplayer.disabled": "La modalitât multi-zuiadôr e je disativade, controle lis impostazions dal to account Microsoft.", "title.multiplayer.disabled.banned.name": "Tu âs di cambiâ il to non prime di zuiâ in linie", "title.multiplayer.disabled.banned.permanent": "Il to account al è suspindût in mût permanent dal zûc in linie", "title.multiplayer.disabled.banned.temporary": "Il to account al è suspindût in mût temporani dal zûc in linie", "title.multiplayer.lan": "Multi-zuiadôr (LAN)", "title.multiplayer.other": "Multi-zuiadôr (di tierçs)", "title.multiplayer.realms": "Multi-<PERSON><PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "translation.test.args": "%s %s", "translation.test.complex": "Prefìs, %s%2$s ancjemò %s e %1$s tal ultin %s e ancje %1$s di gnûf!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "mandi %", "translation.test.invalid2": "mandi %s", "translation.test.none": "Mandi, mont!", "translation.test.world": "mont", "trim_material.minecraft.amethyst": "Di ametiste", "trim_material.minecraft.copper": "<PERSON>", "trim_material.minecraft.diamond": "<PERSON>", "trim_material.minecraft.emerald": "<PERSON>", "trim_material.minecraft.gold": "Di aur", "trim_material.minecraft.iron": "<PERSON> <PERSON>er", "trim_material.minecraft.lapis": "Di lapislazuli", "trim_material.minecraft.netherite": "Di netherite", "trim_material.minecraft.quartz": "Di <PERSON>ç", "trim_material.minecraft.redstone": "Di redstone", "trim_material.minecraft.resin": "Di resine", "trim_pattern.minecraft.bolt": "Ornament cun st<PERSON>l bulon", "trim_pattern.minecraft.coast": "Ornament cun st<PERSON>l cost<PERSON>r", "trim_pattern.minecraft.dune": "Ornament cun stîl dune", "trim_pattern.minecraft.eye": "Ornament cun stîl voli", "trim_pattern.minecraft.flow": "Ornament cun stîl flus", "trim_pattern.minecraft.host": "Ornament cun stîl ospit", "trim_pattern.minecraft.raiser": "Ornament cun st<PERSON><PERSON>", "trim_pattern.minecraft.rib": "Ornament cun st<PERSON><PERSON> cuestis", "trim_pattern.minecraft.sentry": "Ornament cun st<PERSON>l sentinele", "trim_pattern.minecraft.shaper": "Ornament cun st<PERSON>l <PERSON>r", "trim_pattern.minecraft.silence": "Ornament cun stîl cidin", "trim_pattern.minecraft.snout": "Ornament cun stîl music", "trim_pattern.minecraft.spire": "Ornament cun stîl spicis", "trim_pattern.minecraft.tide": "Ornament cun st<PERSON>l maree", "trim_pattern.minecraft.vex": "Ornament cun stîl vex", "trim_pattern.minecraft.ward": "Ornament cun st<PERSON>l vuardie", "trim_pattern.minecraft.wayfinder": "Ornament cun stîl esploradôr", "trim_pattern.minecraft.wild": "Ornament cun stîl salvadi", "tutorial.bundleInsert.description": "<PERSON><PERSON> diestri par zontâ ogjets", "tutorial.bundleInsert.title": "Dopre un sachet", "tutorial.craft_planks.description": "Il libri des ricetis al pues judâti", "tutorial.craft_planks.title": "<PERSON><PERSON><PERSON><PERSON> cualchi bree", "tutorial.find_tree.description": "Ti coventarà un pôc di len", "tutorial.find_tree.title": "Cjate un arbul", "tutorial.look.description": "<PERSON><PERSON>f il mouse par voltâti", "tutorial.look.title": "Bute un voli tor ator", "tutorial.move.description": "Salte cun %s", "tutorial.move.title": "Spostiti cun %s, %s, %s e %s", "tutorial.open_inventory.description": "Frache %s", "tutorial.open_inventory.title": "Vierç l'inventari", "tutorial.punch_tree.description": "Ten fracât %s", "tutorial.punch_tree.title": "Distrûç l'arbul", "tutorial.socialInteractions.description": "Frache %s par vierzi", "tutorial.socialInteractions.title": "Interazions sociâls", "upgrade.minecraft.netherite_upgrade": "Miorament di netherite"}